<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2014 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

/**
 * 为了兼容之前的类库，在这里统一载入之前的全局函数
 *
 * <AUTHOR>
 * @date   2016-05-19
 */
$prevFunctionsFile = __DIR__ . '/functions_prev.php';
if (file_exists($prevFunctionsFile)) {
    include_once $prevFunctionsFile;
}

/**
 * Think 系统函数库
 */
function utf8Length($str)
{
    $length = strlen(preg_replace('/[\x00-\x7F]/', '', $str));
    if ($length) {
        return strlen($str) - $length + intval($length / 3);
    }

    return strlen($str);
}

function throw_exception($error)
{
    if (!defined('IGNORE_EXCEPTION')) {
        showmessage($error, '', 'exception');
    } else {
        exit();
    }
}

function substr_replace_cn($string, $repalce = '*', $start = 0, $len = 0)
{
    $count = mb_strlen($string, 'UTF-8'); //此处传入编码，建议使用utf-8。此处编码要与下面mb_substr()所使用的一致
    if (!$count) {
        return $string;
    }
    if ($len == 0) {
        $end = $count; //传入0则替换到最后
    } else {
        $end = $start + $len; //传入指定长度则为开始长度+指定长度
    }
    $i            = 0;
    $returnString = '';
    while ($i < $count) {
        //循环该字符串
        $tmpString = mb_substr($string, $i, 1, 'UTF-8'); // 与mb_strlen编码一致
        if ($start <= $i && $i < $end) {
            $returnString .= $repalce;
        } else {
            $returnString .= $tmpString;
        }
        $i++;
    }

    return $returnString;
}

/**
 *
 * 获取订单二维码连接地址(短信内容中的地址)
 * <AUTHOR> Chen
 * @date   2016-09-27
 *
 * @param  string  $ordernum  票付通订单号
 *
 * @return string
 */
function get_order_url($ordernum)
{
    $hashids = new \Library\Hashids\SmsHashids();
    $url     = MOBILE_URL . 'M' . $hashids->encode($ordernum);
    unset($hashids);

    return $url;
}

/**
 * 向elk日志系统记录日志[elk.12301dev.com]
 *
 * <AUTHOR> Chen
 *
 * @param  string  $log_name  日志文件名
 * @param  mixed  $log_message  日志内容，可以为字符串或数组
 */
function write_to_logstash($log_name, $log_message)
{
    $log_dir = BASE_LOG_DIR . '/logstash/' . $log_name . '_' . date('ymd') . '.log';
    $word    = json_encode([
        'time'   => date("Y-m-d H:i:s"),
        'client' => $_SERVER['REMOTE_ADDR'],
        'domain' => $_SERVER['HTTP_HOST'],
        'status' => 200,
        'words'  => $log_message,
    ], JSON_UNESCAPED_UNICODE);
    file_put_contents($log_dir, $word . "\n", FILE_APPEND);
}

/**
 * 判断ip地址是否在网段内
 * @Author: zhujb
 * 2018/11/6
 *
 * @param $ip
 * @param $network
 *
 * @return bool
 */
function ip_in_network($ip, $network)
{
    $ip            = (double)(sprintf("%u", ip2long($ip)));
    $s             = explode('/', $network);
    $network_start = (double)(sprintf("%u", ip2long($s[0])));
    $network_len   = pow(2, 32 - $s[1]);
    $network_end   = $network_start + $network_len - 1;

    if ($ip >= $network_start && $ip <= $network_end) {
        return true;
    }

    return false;
}

/**
 * 取上一步来源地址
 *
 * @param
 *
 * @return string 字符串类型的返回结果
 */
function getReferer()
{
    return empty($_SERVER['HTTP_REFERER']) ? '' : $_SERVER['HTTP_REFERER'];
}

/**
 * 输出信息
 *
 * @param  string  $msg  输出信息
 * @param  string/array $url 跳转地址 当$url为数组时，结构为 array('msg'=>'跳转连接文字','url'=>'跳转连接');
 * @param  string  $show_type  输出格式 默认为html
 * @param  string  $msg_type  信息类型 succ 为成功，error为失败/错误
 * @param  string  $is_show  是否显示跳转链接，默认是为1，显示
 * @param  int  $time  跳转时间，默认为2秒
 *
 * @return string 字符串类型的返回结果
 */
function showMessage($msg, $url = '', $show_type = 'html', $msg_type = 'succ', $is_show = 1, $time = 2000)
{
    /**
     * 如果默认为空，则跳转至上一步链接
     */
    $url      = ($url != '' ? $url : getReferer());
    $msg_type = in_array($msg_type, ['succ', 'error']) ? $msg_type : 'error';
    /**
     * 输出类型
     */
    switch ($show_type) {
        case 'json':
            $return = '{';
            $return .= '"msg":"' . $msg . '",';
            $return .= '"url":"' . $url . '"';
            $return .= '}';
            echo $return;
            break;

        case 'exception':
            echo '<!DOCTYPE html>';
            echo '<html>';
            echo '<head>';
            echo '<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />';
            echo '<title></title>';
            echo '<style type="text/css">';
            echo 'body { font-family: "Verdana";padding: 0; margin: 0;}';
            echo 'h2 { font-size: 12px; line-height: 30px; border-bottom: 1px dashed #CCC; padding-bottom: 8px;width:800px; margin: 20px 0 0 150px;}';
            echo 'dl { float: left; display: inline; clear: both; padding: 0; margin: 10px 20px 20px 150px;}';
            echo 'dt { font-size: 14px; font-weight: bold; line-height: 40px; color: #333; padding: 0; margin: 0; border-width: 0px;}';
            echo 'dd { font-size: 12px; line-height: 40px; color: #333; padding: 0px; margin:0;}';
            echo '</style>';
            echo '</head>';
            echo '<body>';
            echo '<dl>';
            echo '<dd>' . $msg . '</dd>';
            echo '<dt><p /></dt>';
            echo '<dd><p /><p /><p /><p /></dd>';
            echo '<dd><p /><p /><p /><p />Copyright 2013-2016 www.12301.cc , All Rights Reserved </dd>';
            echo '</dl>';
            echo '</body>';
            echo '</html>';
            exit();
            break;

        case 'javascript':
            echo '<script>';
            echo 'alert(\'' . $msg . '\');';
            echo 'location.href=\'' . $url . '\'';
            echo '</script>';
            exit();
            break;
        default:
            break;
    }
    exit;
}

if (!function_exists('C')) {
    /**
     * 获取和设置配置参数 支持批量定义
     *
     * @param  string|array  $name  配置变量
     * @param  mixed  $value  配置值
     * @param  mixed  $default  默认值
     *
     * @return mixed
     */
    function C($name = null, $value = null, $default = null)
    {
        static $_config = [];
        // 无参数时获取所有
        if (empty($name)) {
            return $_config;
        }
        // 优先执行设置获取或赋值
        if (is_string($name)) {
            if (!strpos($name, '.')) {
                $name = strtoupper($name);
                if (is_null($value)) {
                    return isset($_config[$name]) ? $_config[$name] : $default;
                }

                $_config[$name] = $value;

                return null;
            }
            // 二维数组设置和获取支持
            $name    = explode('.', $name);
            $name[0] = strtoupper($name[0]);
            if (is_null($value)) {
                return isset($_config[$name[0]][$name[1]]) ? $_config[$name[0]][$name[1]] : $default;
            }

            $_config[$name[0]][$name[1]] = $value;

            return null;
        }
        // 批量设置
        if (is_array($name)) {
            $_config = array_merge($_config, array_change_key_case($name, CASE_UPPER));

            return null;
        }

        return null; // 避免非法参数
    }
}

if (!function_exists('load_config')) {
    /**
     * 动态加载业务配置数据
     *
     * @param  string  $key  配置的键
     * @param  string  $type  配置文件类型，默认business，业务配置
     *
     * @return mixed
     */
    function load_config($key, $type = 'business')
    {
        static $_load_config = [];

        $key  = strval($key);
        $type = strval($type);

        // 无参数时获取所有
        if (empty($key) || empty($type)) {
            return null;
        }

        //获取配置文件的所有配置
        if (isset($_load_config[$type])) {
            $configArr = $_load_config[$type];
        } else {
            $configFile = HTML_DIR . "/Service/Conf/{$type}.conf.php";
            if (file_exists($configFile)) {
                $configArr = include $configFile;
            } else {
                $configArr = [];
            }

            $_load_config[$type] = $configArr;
        }

        if (isset($configArr[$key])) {
            return $configArr[$key];
        } else {
            return null;
        }
    }
}

/**
 * 抛出异常处理
 *
 * @param  string  $msg  异常消息
 * @param  integer  $code  异常代码 默认为0
 *
 * @throws Library\Exception
 * @return void
 */
function E($msg, $code = 0)
{
    throw new Library\Exception($msg, $code);
}

/**
 * 获取输入参数 支持过滤和默认值
 * 使用方法:
 * <code>
 * I('id',0); 获取id参数 自动判断get或者post
 * I('post.name','','htmlspecialchars'); 获取$_POST['name']
 * I('get.'); 获取$_GET
 * </code>
 *
 * @param  string  $name  变量的名称 支持指定类型
 * @param  mixed  $default  不存在的时候默认值
 * @param  mixed  $filter  参数过滤方法
 * @param  mixed  $datas  要获取的额外数据源
 *
 * @return mixed
 */
function I($name, $default = '', $filter = null, $datas = null)
{
    static $_PUT = null;
    if (strpos($name, '/')) {
        // 指定修饰符
        [$name, $type] = explode('/', $name, 2);
    } elseif (C('VAR_AUTO_STRING')) {
        // 默认强制转换为字符串
        $type = 's';
    }
    if (strpos($name, '.')) {
        // 指定参数来源
        [$method, $name] = explode('.', $name, 2);
    } else {
        // 默认为自动判断
        $method = 'param';
    }
    switch (strtolower($method)) {
        case 'get':
            $input = &$_GET;
            break;
        case 'post':
            $input = &$_POST;
            break;
        case 'put':
            if (is_null($_PUT)) {
                parse_str(file_get_contents('php://input'), $_PUT);
            }
            $input = $_PUT;
            break;
        case 'param':
            switch ($_SERVER['REQUEST_METHOD']) {
                case 'POST':
                    $input = $_POST;
                    break;
                case 'PUT':
                    if (is_null($_PUT)) {
                        parse_str(file_get_contents('php://input'), $_PUT);
                    }
                    $input = $_PUT;
                    break;
                default:
                    $input = $_GET;
            }
            break;
        case 'path':
            $input = [];
            if (!empty($_SERVER['PATH_INFO'])) {
                $depr  = C('URL_PATHINFO_DEPR');
                $input = explode($depr, trim($_SERVER['PATH_INFO'], $depr));
            }
            break;
        case 'request':
            $input = &$_REQUEST;
            break;
        case 'session':
            $input = &$_SESSION;
            break;
        case 'cookie':
            $input = &$_COOKIE;
            break;
        case 'server':
            $input = &$_SERVER;
            break;
        case 'globals':
            $input = &$GLOBALS;
            break;
        case 'data':
            $input = &$datas;
            break;
        default:
            return null;
    }
    if ('' == $name) {
        // 获取全部变量
        $data    = $input;
        $filters = isset($filter) ? $filter : C('DEFAULT_FILTER');
        if ($filters) {
            if (is_string($filters)) {
                $filters = explode(',', $filters);
            }
            if (isset($filter)) {
                //自定义过滤器需要增加xss过滤
                array_unshift($filters, 'pft_xss_filter');
            }
            foreach ($filters as $filter) {
                if (strpos($filter, 'pft_') === 0) {
                    //pft过滤器接收两个参数，参数值和参数名
                    $data = array_map_recursive($filter, $data, $name); // 参数过滤
                } else {
                    $data = array_map_recursive($filter, $data); // 参数过滤
                }
            }
        }
    } elseif (isset($input[$name])) {
        // 取值操作
        $data    = $input[$name];
        $filters = isset($filter) ? $filter : C('DEFAULT_FILTER');
        if ($filters) {
            if (is_string($filters)) {
                if (0 === strpos($filters, '/')) {
                    if (1 !== preg_match($filters, (string)$data)) {
                        // 支持正则验证
                        return isset($default) ? $default : null;
                    }
                } else {
                    $filters = explode(',', $filters);
                }
            } elseif (is_int($filters)) {
                $filters = [$filters];
            }

            if (is_array($filters)) {
                if (isset($filter)) {
                    //自定义过滤器需要增加xss过滤
                    array_unshift($filters, 'pft_xss_filter');
                }
                foreach ($filters as $filter) {
                    $filter = trim($filter);
                    if (function_exists($filter)) {
                        if (strpos($filter, 'pft_') === 0) {
                            //pft过滤器接收两个参数，参数值和参数名
                            $data = is_array($data) ? array_map_recursive($filter, $data, $name) : $filter($data, $name); // 参数过滤
                        } else {
                            $data = is_array($data) ? array_map_recursive($filter, $data) : $filter($data); // 参数过滤
                        }
                    } else {
                        $data = filter_var($data, is_int($filter) ? $filter : filter_id($filter));
                        if (false === $data) {
                            return isset($default) ? $default : null;
                        }
                    }
                }
            }
        }
        if (!empty($type)) {
            switch (strtolower($type)) {
                case 'a': // 数组
                    $data = (array)$data;
                    break;
                case 'd': // 数字
                    $data = (int)$data;
                    break;
                case 'f': // 浮点
                    $data = (float)$data;
                    break;
                case 'b': // 布尔
                    $data = (boolean)$data;
                    break;
                case 's': // 字符串
                default:
                    $data = (string)$data;
            }
        }
    } else {
        // 变量默认值
        $data = isset($default) ? $default : null;
    }
    is_array($data) && array_walk_recursive($data, 'think_filter');

    return $data;
}

/**
 * xss过滤器
 * @param $data
 * @param $name
 * @return mixed|string|string[]
 */
function pft_xss_filter($data, $name)
{
    try {
        $qconf = \Library\Container::pull(\Library\Util\QConfUtil::class);
        if ($qconf->abTest('/php/platform/safety_input_filter')) {
            $safetyUtil = \Library\Container::pull(\Library\Util\SafetyUtil::class);
            $data = $safetyUtil->inputFilter($data, $name);
        }
    } catch (\Throwable $e) {
        $major = defined('PROJECT_NAME') ? PROJECT_NAME : 'unknown';
        pft_log('safety', json_encode([
            $major . '.safety_input_filter',
            $e->getMessage(),
            $e->getFile() . ':' . $e->getLine()
        ]));
    }
    return $data;
}

function pft_boolval($data, $name = '') {
    return filter_var($data, FILTER_VALIDATE_BOOLEAN);
}

function array_map_recursive($filter, $data, ...$args)
{
    $result = [];
    foreach ($data as $key => $val) {
        $result[$key] = is_array($val)
            ? array_map_recursive($filter, $val, ...$args)
            : call_user_func($filter, $val, ...$args);
    }

    return $result;
}

/**
 * 判断是否SSL协议
 * @return boolean
 */
function is_ssl()
{
    if (isset($_SERVER['HTTPS']) && ('1' == $_SERVER['HTTPS'] || 'on' == strtolower($_SERVER['HTTPS']))) {
        return true;
    } elseif (isset($_SERVER['SERVER_PORT']) && ('443' == $_SERVER['SERVER_PORT'])) {
        return true;
    }

    return false;
}

/**
 * 获取客户端IP地址
 *
 * @param  integer  $type  返回类型 0 返回IP地址 1 返回IPV4地址数字
 * @param  boolean  $adv  是否进行高级模式获取（有可能被伪装）
 *
 * @return mixed
 */
function get_client_ip($type = 0, $adv = true)
{
    $type = $type ? 1 : 0;
    static $ip = null;
    if (null !== $ip) {
        return $ip[$type];
    }

    if ($adv) {
        if (isset($_SERVER['HTTP_ALI_CDN_REAL_IP'])) {
            $ip = $_SERVER['HTTP_ALI_CDN_REAL_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            $pos = array_search('unknown', $arr);
            if (false !== $pos) {
                unset($arr[$pos]);
            }
            $ip = trim($arr[0]);
        } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
    } else if (isset($_SERVER['HTTP_PFT_X_FORWARDED_FOR']) && filter_var($_SERVER['HTTP_PFT_X_FORWARDED_FOR'],
            FILTER_VALIDATE_IP)) {
        //这个头部数据是和终端那边约定的头部
        $ip = $_SERVER['HTTP_PFT_X_FORWARDED_FOR'];
    } elseif (isset($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    // IP地址合法验证
    $long = sprintf("%u", ip2long($ip));
    $ip   = $long ? [$ip, $long] : ['0.0.0.0', 0];

    return $ip[$type];
}

/**
 * 发送HTTP状态
 *
 * @param  integer  $code  状态码
 *
 * @return void
 */
function send_http_status($code)
{
    static $_status = [
        // Informational 1xx
        100 => 'Continue',
        101 => 'Switching Protocols',
        // Success 2xx
        200 => 'OK',
        201 => 'Created',
        202 => 'Accepted',
        203 => 'Non-Authoritative Information',
        204 => 'No Content',
        205 => 'Reset Content',
        206 => 'Partial Content',
        // Redirection 3xx
        300 => 'Multiple Choices',
        301 => 'Moved Permanently',
        302 => 'Moved Temporarily ', // 1.1
        303 => 'See Other',
        304 => 'Not Modified',
        305 => 'Use Proxy',
        // 306 is deprecated but reserved
        307 => 'Temporary Redirect',
        // Client Error 4xx
        400 => 'Bad Request',
        401 => 'Unauthorized',
        402 => 'Payment Required',
        403 => 'Forbidden',
        404 => 'Not Found',
        405 => 'Method Not Allowed',
        406 => 'Not Acceptable',
        407 => 'Proxy Authentication Required',
        408 => 'Request Timeout',
        409 => 'Conflict',
        410 => 'Gone',
        411 => 'Length Required',
        412 => 'Precondition Failed',
        413 => 'Request Entity Too Large',
        414 => 'Request-URI Too Long',
        415 => 'Unsupported Media Type',
        416 => 'Requested Range Not Satisfiable',
        417 => 'Expectation Failed',
        // Server Error 5xx
        500 => 'Internal Server Error',
        501 => 'Not Implemented',
        502 => 'Bad Gateway',
        503 => 'Service Unavailable',
        504 => 'Gateway Timeout',
        505 => 'HTTP Version Not Supported',
        509 => 'Bandwidth Limit Exceeded',
    ];
    if (isset($_status[$code])) {
        header('HTTP/1.1 ' . $code . ' ' . $_status[$code]);
        // 确保FastCGI模式下正常
        header('Status:' . $code . ' ' . $_status[$code]);
    }
}

function think_filter(&$value)
{
    // TODO 其他安全过滤

    // 过滤查询特殊字符
    if (preg_match('/^(EXP|NEQ|GT|EGT|LT|ELT|OR|XOR|LIKE|NOTLIKE|NOT BETWEEN|NOTBETWEEN|BETWEEN|NOTIN|NOT IN|IN)$/i',
        $value)) {
        $value .= ' ';
    }
}

if (!function_exists('pft_log')) {

    /**
     * 统一写日志函数，日志统一写入BASE_LOG_DIR下面
     *
     * 扩展：1. 如果in_array($logMode, [1, 2, 3]的话，日志按照log.conf.php配置的目录写入
     *      然后这些日志会统一打入到日志系统，这样所有人都可以通过日志系统查询排查问题
     *      所以后面日志内容都写清楚，可以直观看出日志的业务意思
     *      2. 如果是使用新的模式的话，path只要传入log.conf.php里面配置的business键就可以了
     *
     * <AUTHOR>
     * @date   2016-03-07
     *
     * @param  string  $path  在BASE_LOG_DIR这个下面的目录 - product/reseller_storage
     * @param  string  $content  需要写入的内容
     * @param  mix  $logMode  日志格式 / 目录分隔模式：
     *                      日志格式：
     *                          1：业务成功日志 - success目录
     *                          2：业务失败日志 - fail目录
     *                          3：业务调试日志 - debug日志
     *                      目录分隔模式：
     *                          day：按日切分 - product/reseller_storage/2016/03/23.log
     *                          month：按月切分 - product/reseller_storage/2016/03.log
     *                          year：按年切分 - product/reseller_storage/2016.log
     *
     * @return
     */
    function pft_log($path, $content, $logMode = 'day')
    {
        $path    = strval($path);
        $path    = str_replace("\\", '/', trim($path, '/'));
        $content = strval($content);
        if (!$path || !$content) {
            return false;
        }

        $bizLogModeArr = [
            1 => 'success',
            2 => 'fail',
            3 => 'debug',
        ];

        if (array_key_exists($logMode, $bizLogModeArr)) {
            $basePath    = load_config('log_path', 'log');
            $businessArr = load_config('business', 'log');

            if (isset($businessArr[$path])) {
                $realPath = $basePath . '/' . $businessArr[$path];
                if (PHP_SAPI == 'cli') {
                    $realPath = 'cli/' . $realPath;
                }

                //按show/set/success/2018/0711.log这样存放
                $tmpPath  = BASE_LOG_DIR . '/' . $realPath . '/' . $bizLogModeArr[$logMode] . '/' . date('Y') . '/';
                $fileName = date('md') . '.log';
            } else {
                //如果没有定义的话，直接返回
                return false;
            }
        } else {
            //走旧模式
            $logMode = in_array($logMode, ['day', 'month', 'year']) ? $logMode : 'day';
            if (PHP_SAPI == 'cli') {
                $path = 'cli/' . $path;
            }

            $tmpPath  = BASE_LOG_DIR . '/' . $path . '/';
            $fileName = date('Y') . '.log';
            if ($logMode == 'day') {
                $tmpPath  .= date('Y') . '/' . date('m') . '/';
                $fileName = date('d') . '.log';
            } elseif ($logMode == 'month') {
                $tmpPath  .= date('Y') . '/';
                $fileName = date('m') . '.log';
            }
        }

        //如果文件不存在，就创建文件
        if (!file_exists($tmpPath)) {
            $res = @mkdir($tmpPath, 0777, true);
            if (!$res) {
                return false;
            }
        }

        //内容写入日志文件
        $file    = $tmpPath . $fileName;
        $content = date('Y-m-d H:i:s') . ' # server_ip:' . \Library\Tools\Helpers::getServerIp() . ' # ' . $content . "\r\n";
        $res     = @file_put_contents($file, $content, FILE_APPEND);
        if ($res) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 记录日志-不按年月日这样来建目录
     *
     * @param $path
     * @param $content
     *
     * @return bool
     */
    function pft_log_simple($path, $content)
    {
        $path    = strval($path);
        $path    = str_replace("\\", '/', trim($path, '/'));
        $content = strval($content);
        if (!$path || !$content) {
            return false;
        }
        if (PHP_SAPI == 'cli') {
            $path = 'cli/' . $path;
        }

        $tmpPath = BASE_LOG_DIR . '/' . $path;
        //如果文件不存在，就创建文件
        if (!file_exists($tmpPath)) {
            $res = mkdir($tmpPath, 0777, true);
            if (!$res) {
                return false;
            }
        }
        //内容写入日志文件
        $file    = $tmpPath . '.log';
        $content = date('Y-m-d H:i:s') . ' # ' . $content . "\r\n";
        $res     = @file_put_contents($file, $content, FILE_APPEND);
        if ($res) {
            return true;
        } else {
            return false;
        }
    }

    if (!function_exists('checkData')) {
        /**
         * 验证年月日
         */
        function checkData($mydate)
        {
            [$yy, $mm, $dd] = explode("-", $mydate);
            if (is_numeric($yy) && is_numeric($mm) && is_numeric($dd)) {
                return checkdate($mm, $dd, $yy);
            }

            return false;
        }
    }

    /**
     * session管理函数
     *
     * @param  string|array  $name  session名称 如果为数组则表示进行session设置
     * @param  mixed  $value  session值
     *
     * @return mixed
     */
    function session($name = '', $value = '')
    {
        $prefix = C('SESSION_PREFIX');
        if (is_array($name)) {
            // session初始化 在session_start 之前调用
            if (isset($name['prefix'])) {
                C('SESSION_PREFIX', $name['prefix']);
            }

            if (C('VAR_SESSION_ID') && isset($_REQUEST[C('VAR_SESSION_ID')])) {
                session_id($_REQUEST[C('VAR_SESSION_ID')]);
            } elseif (isset($name['id'])) {
                session_id($name['id']);
            }
            if ('common' == APP_MODE) {
                // 其它模式可能不支持
                ini_set('session.auto_start', 0);
            }
            if (isset($name['name'])) {
                session_name($name['name']);
            }

            if (isset($name['path'])) {
                session_save_path($name['path']);
            }

            if (isset($name['domain'])) {
                ini_set('session.cookie_domain', $name['domain']);
            }

            if (isset($name['expire'])) {
                ini_set('session.gc_maxlifetime', $name['expire']);
                ini_set('session.cookie_lifetime', $name['expire']);
            }
            if (isset($name['use_trans_sid'])) {
                ini_set('session.use_trans_sid', $name['use_trans_sid'] ? 1 : 0);
            }

            if (isset($name['use_cookies'])) {
                ini_set('session.use_cookies', $name['use_cookies'] ? 1 : 0);
            }

            if (isset($name['cache_limiter'])) {
                session_cache_limiter($name['cache_limiter']);
            }

            if (isset($name['cache_expire'])) {
                session_cache_expire($name['cache_expire']);
            }

            if (isset($name['type'])) {
                C('SESSION_TYPE', $name['type']);
            }

            if (C('SESSION_TYPE')) {
                // 读取session驱动
                $type   = C('SESSION_TYPE');
                $class  = strpos($type, '\\') ? $type : 'Think\\Session\\Driver\\' . ucwords(strtolower($type));
                $hander = new $class();
                session_set_save_handler(
                    [&$hander, "open"],
                    [&$hander, "close"],
                    [&$hander, "read"],
                    [&$hander, "write"],
                    [&$hander, "destroy"],
                    [&$hander, "gc"]);
            }
            // 启动session
            if (C('SESSION_AUTO_START')) {
                session_start();
            }

        } elseif ('' === $value) {
            if ('' === $name) {
                // 获取全部的session
                return $prefix ? $_SESSION[$prefix] : $_SESSION;
            } elseif (0 === strpos($name, '[')) {
                // session 操作
                if ('[pause]' == $name) {
                    // 暂停session
                    session_write_close();
                } elseif ('[start]' == $name) {
                    // 启动session
                    session_start();
                } elseif ('[destroy]' == $name) {
                    // 销毁session
                    $_SESSION = [];
                    session_unset();
                    session_destroy();
                } elseif ('[regenerate]' == $name) {
                    // 重新生成id
                    session_regenerate_id();
                }
            } elseif (0 === strpos($name, '?')) {
                // 检查session
                $name = substr($name, 1);
                if (strpos($name, '.')) {
                    // 支持数组
                    [$name1, $name2] = explode('.', $name);

                    return $prefix ? isset($_SESSION[$prefix][$name1][$name2]) : isset($_SESSION[$name1][$name2]);
                } else {
                    return $prefix ? isset($_SESSION[$prefix][$name]) : isset($_SESSION[$name]);
                }
            } elseif (is_null($name)) {
                // 清空session
                if ($prefix) {
                    unset($_SESSION[$prefix]);
                } else {
                    $_SESSION = [];
                }
            } elseif ($prefix) {
                // 获取session
                if (strpos($name, '.')) {
                    [$name1, $name2] = explode('.', $name);

                    return isset($_SESSION[$prefix][$name1][$name2]) ? $_SESSION[$prefix][$name1][$name2] : null;
                } else {
                    return isset($_SESSION[$prefix][$name]) ? $_SESSION[$prefix][$name] : null;
                }
            } else {
                if (strpos($name, '.')) {
                    [$name1, $name2] = explode('.', $name);

                    return isset($_SESSION[$name1][$name2]) ? $_SESSION[$name1][$name2] : null;
                } else {
                    return isset($_SESSION[$name]) ? $_SESSION[$name] : null;
                }
            }
        } elseif (is_null($value)) {
            // 删除session
            if (strpos($name, '.')) {
                [$name1, $name2] = explode('.', $name);
                if ($prefix) {
                    unset($_SESSION[$prefix][$name1][$name2]);
                } else {
                    unset($_SESSION[$name1][$name2]);
                }
            } else {
                if ($prefix) {
                    unset($_SESSION[$prefix][$name]);
                } else {
                    unset($_SESSION[$name]);
                }
            }
        } else {
            // 设置session
            if (strpos($name, '.')) {
                [$name1, $name2] = explode('.', $name);
                if ($prefix) {
                    $_SESSION[$prefix][$name1][$name2] = $value;
                } else {
                    $_SESSION[$name1][$name2] = $value;
                }
            } else {
                if ($prefix) {
                    $_SESSION[$prefix][$name] = $value;
                } else {
                    $_SESSION[$name] = $value;
                }
            }
        }

        return null;
    }
}
if (!function_exists('curl_post')) {
    /**
     * CURL 提交请求数据
     *
     * <AUTHOR>
     * @date   2016-04-11
     *
     * @param  string  $url  请求URL
     * @param  string  $postData  请求发送的数据
     * @param  int  $port  请求端口
     * @param  int  $timeout  超时时间
     * @param  string  $logPath  错误日志文件
     * @param  array  $http_headers  请求头信息
     * @param  bool  $ret_errcode  是否返回curl错误码
     * @param  array [user,pwd]  CURLOPT_USERPWD 的设置参数
     * @param  string  $method  请求方法 默认 post
     * @param  bool  $isAllHttpCode  是否返回所有的http状态结果  true - 是，返回所有响应    false - 否，只返回状态为200的响应
     *
     * @return bool|mixed
     */
    function curl_post($url, $postData, $port = 80, $timeout = 25, $logPath = '/api/curl_post', $http_headers = [], $ret_errcode = false, $userpwd = '', $method = 'post', $isAllHttpCode = false)
    {
        //可处理的method
        $methodList = [
            'post' => 1,
            'get'  => 1,
        ];

        $method = strtolower($method);
        if (!isset($methodList[$method])) {
            //如果请求方法不在列表中，默认设置为post
            $method = 'post';
        }

        //超时时间处理
        $timeout = intval($timeout);
        $timeout = $timeout <= 0 ? 25 : $timeout;

        $ch       = curl_init();
        $url_info = parse_url($url);
        if ($url_info['scheme'] == 'https') {
            $port = 443;
        } else {
            $port = isset($url_info['port']) ? $url_info['port'] : $port;
        }
        $logPath = str_replace(BASE_LOG_DIR, '', $logPath);
        if ($method == 'get') {
            //get方法
            $postStr  = is_array($postData) ? http_build_query($postData) : $postData;
            $url      = $url . '?' . $postStr;
            $postData = '';
        } else {
            //post方法
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        }

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_PORT, $port);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        if ((is_array($http_headers) || is_object($http_headers)) && count($http_headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $http_headers);
        }
        if ($userpwd != '' && is_array($userpwd)) {
            curl_setopt($ch, CURLOPT_USERPWD, "{$userpwd[0]}:{$userpwd[1]}");
        }

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);

        $res = curl_exec($ch);
        //错误处理
        $errCode = curl_errno($ch);
        if ($errCode > 0) {
            //记录日志
            $errMsg  = curl_error($ch);
            $logData = json_encode([
                'url'       => $url,
                'err_code'  => $errCode,
                'err_msg'   => $errMsg,
                'post_data' => $postData,
                'method'    => $method,
            ]);
            pft_log($logPath, $logData);
            curl_close($ch);
            if ($ret_errcode === true) {
                return ['status' => false, 'code' => $errCode, 'err_msg' => $errMsg];
            }

            //返回false
            return false;
        } else {
            //获取HTTP码
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            if ($isAllHttpCode == true) {
                //所有httpcode情况都有结果返回
                if ($httpCode != 200) {
                    //错误
                    $logData = json_encode([
                        'url'       => $url,
                        'err_code'  => $httpCode,
                        'err_msg'   => $res,
                        'post_data' => $postData,
                        'method'    => $method,
                    ]);
                    pft_log($logPath, $logData);
                }

                curl_close($ch);

                return $res;
            } else {
                if (!in_array($httpCode, [200, 201])) {
                    //接口错误
                    $logData = json_encode([
                        'url'       => $url,
                        'err_code'  => $httpCode,
                        'err_msg'   => $res,
                        'post_data' => $postData,
                        'method'    => $method,
                    ]);
                    pft_log($logPath, $logData);
                    curl_close($ch);
                    if ($ret_errcode === true) {
                        return ['status' => false, 'code' => $httpCode, 'err_msg' => $res];
                    }

                    return false;
                } else {
                    curl_close($ch);

                    return $res;
                }
            }
        }
    }
}

if (!function_exists('pft_file_get_contents')) {
    /**
     * 统一封装的file_get_contents
     * <AUTHOR>
     *
     * @param  string  $url  请求url
     * @param  integer  $timeout  超时时间
     * @param  array  $header  请求头部
     *
     * @return
     */
    function pft_file_get_contents($url, $timeout = 10, $header = [])
    {
        $url     = strval($url);
        $timeout = intval($timeout);
        $timeout = $timeout <= 0 ? 10 : $timeout;

        $contextOptions = [
            'http' => ['timeout' => $timeout],
        ];
        if ($header) {
            $contextOptions['http']['header'] = $header;
        }

        $context = stream_context_create($contextOptions);
        $res     = file_get_contents($url, false, $context);

        return $res;
    }
}

if (!function_exists('get_obj_instance')) {
    /**
     * 取得对象实例
     *
     * @param  string  $class  类名
     * @param  string  $method  方法名
     * @param  array  $args  参数
     *
     * @return mixed
     * @throws Exception
     */
    function get_obj_instance($class, $method = '', $args = [])
    {
        static $_cache = [];
        $key = $class . $method . (empty($args) ? null : md5(serialize($args)));
        if (isset($_cache[$key])) {
            return $_cache[$key];
        } else if (class_exists($class)) {
            $obj = new $class();
            if (method_exists($obj, $method)) {
                if (empty($args)) {
                    $_cache[$key] = $obj->$method();
                } else {
                    $_cache[$key] = call_user_func_array([
                        &$obj,
                        $method,
                    ], $args);
                }
            } else {
                $_cache[$key] = $obj;
            }

            return $_cache[$key];
        } else {
            throw new Exception('Class ' . $class . ' isn\'t exists!');
        }
    }
}
if (!function_exists('safe_str')) {
    /**
     * 纯文本输入
     *
     * @param $text
     *
     * @return mixed|string
     */
    function safe_str($text)
    {
        $text = trim($text);
        $text = strip_tags($text);
        $text = htmlspecialchars($text, ENT_QUOTES, "UTF-8");
        $text = str_replace("'", "", $text);

        //将全角字符转换为半角字符
        $text = convert_char($text);

        return $text;
    }
}
if (!function_exists('chk_date')) {
    /**
     * 日期校验
     * <AUTHOR>
     *
     * @param $mydate 日期 - 2018-10-11 / 2018/10/11
     * @param $delimiter 格式分隔符 - '-'或'/'
     *
     * @return mixed|string
     */
    function chk_date($mydate, $delimiter = '-')
    {
        $tmp = explode($delimiter, $mydate);
        if (count($tmp) !== 3) {
            return false;
        }

        [$yy, $mm, $dd] = $tmp;

        if (is_numeric($yy) && is_numeric($mm) && is_numeric($dd)) {
            return checkdate($mm, $dd, $yy);
        }

        return false;
    }
}

if (!function_exists('convert_char')) {
    /**
     * 全角字符转换为半角字符
     * <AUTHOR>
     * @date   2016-08-15
     *
     * @param  $str 输入字符
     *
     * @return 半角字符串
     */
    function convert_char($str)
    {
        //为空的话直接返回
        $str = strval($str);

        $arr = [
            '０' => '0',
            '１' => '1',
            '２' => '2',
            '３' => '3',
            '４' => '4',
            '５' => '5',
            '６' => '6',
            '７' => '7',
            '８' => '8',
            '９' => '9',
            'Ａ' => 'A',
            'Ｂ' => 'B',
            'Ｃ' => 'C',
            'Ｄ' => 'D',
            'Ｅ' => 'E',
            'Ｆ' => 'F',
            'Ｇ' => 'G',
            'Ｈ' => 'H',
            'Ｉ' => 'I',
            'Ｊ' => 'J',
            'Ｋ' => 'K',
            'Ｌ' => 'L',
            'Ｍ' => 'M',
            'Ｎ' => 'N',
            'Ｏ' => 'O',
            'Ｐ' => 'P',
            'Ｑ' => 'Q',
            'Ｒ' => 'R',
            'Ｓ' => 'S',
            'Ｔ' => 'T',
            'Ｕ' => 'U',
            'Ｖ' => 'V',
            'Ｗ' => 'W',
            'Ｘ' => 'X',
            'Ｙ' => 'Y',
            'Ｚ' => 'Z',
            'ａ' => 'a',
            'ｂ' => 'b',
            'ｃ' => 'c',
            'ｄ' => 'd',
            'ｅ' => 'e',
            'ｆ' => 'f',
            'ｇ' => 'g',
            'ｈ' => 'h',
            'ｉ' => 'i',
            'ｊ' => 'j',
            'ｋ' => 'k',
            'ｌ' => 'l',
            'ｍ' => 'm',
            'ｎ' => 'n',
            'ｏ' => 'o',
            'ｐ' => 'p',
            'ｑ' => 'q',
            'ｒ' => 'r',
            'ｓ' => 's',
            'ｔ' => 't',
            'ｕ' => 'u',
            'ｖ' => 'v',
            'ｗ' => 'w',
            'ｘ' => 'x',
            'ｙ' => 'y',
            'ｚ' => 'z',
            '（' => '(',
            '）' => ')',
            '〔' => '[',
            '〕' => ']',
            '【' => '[',
            '】' => ']',
            '〖' => '[',
            '〗' => ']',
            '“' => '[',
            '”' => ']',
            '‘' => '[',
            "'" => ']',
            '｛' => '{',
            '｝' => '}',
            '《' => '<',
            '》' => '>',
            '％' => '%',
            '＋' => '+',
            '—' => '-',
            '－' => '-',
            '～' => '-',
            '：' => ':',
            '。' => '.',
            '、' => ',',
            '，' => '.',
            '、' => '.',
            '；' => ',',
            '？' => '?',
            '！' => '!',
            '…' => '-',
            '‖' => '|',
            '”' => '"',
            "'" => '`',
            '‘' => '`',
            '｜' => '|',
            '〃' => '"',
            '　' => ' ',
        ];

        return strtr($str, $arr);
    }
}

if (!function_exists('load_excel')) {
    function load_excel()
    {
        static $PHPExcel;
        if (!empty($PHPExcel) && is_object($PHPExcel)) {
            return $PHPExcel;
        }
        $file = HTML_DIR . '/Service/Library/Business/PHPExcel.php';
        include_once $file;
        $PHPExcel = new PHPExcel();

        return $PHPExcel;
    }
}

if (!function_exists('xmlToArray')) {
    function xmlToArray($xml)
    {
        return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement',
            LIBXML_NOCDATA)), true);
    }
}

if (!function_exists('getUrl')) {
    function getUrl($url = false)
    {
        if (!$url) {
            $url = $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        }

        $position = strpos($url, '?');
        if ($position > 0) {
            $url = substr($url, 0, $position);
        }

        return $url;
    }
}

if (!function_exists('checkUrl')) {
    function checkUrl($url)
    {

        if (!preg_match('/(http|https):\/\/[\w.]+[\w\/]*[\w.]*\??[\w=&\+\%]*/is', $url)) {
            return false;
        }

        return true;
    }
}

/**
 * mac地址转int类型
 * <AUTHOR> Chen
 *
 * @param $mac
 *
 * @return string
 */
function mac2int($mac)
{
    return base_convert($mac, 16, 10);
}

/**
 * INT数字转mac地址
 *
 * @param $int
 *
 * @return string
 */
function int2mac($int)
{
    return base_convert($int, 10, 16);
}

/**
 * 好看的INT数字转mac地址
 *
 * @param $int
 *
 * @return string
 */
function int2macaddress($int)
{
    $hex = int2mac($int);
    while (strlen($hex) < 12) {
        $hex = '0' . $hex;
    }

    return strtoupper(implode(':', str_split($hex, 2)));
}

/**
 * mac地址校验
 *
 * @param $mac
 *
 * @return bool
 */
function checkMac($mac)
{
    $mac = str_replace('-', ':', $mac);
    if (preg_match("/^[A-F0-9]{2}:[A-F0-9]{2}:[A-F0-9]{2}:[A-F0-9]{2}:[A-F0-9]{2}:[A-F0-9]{2}$/i", $mac)) {
        return true;
    }

    return false;
}

if (!function_exists('genRandomNum')) {
    /**
     * 获取随机数
     * @date   2016-01-11
     * <AUTHOR>
     *
     * @param  int  $len  随机数长度
     *
     * @return
     */
    function genRandomNum($len)
    {
        $chars    = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0];
        $charsLen = count($chars) - 1;
        shuffle($chars); // 将数组打乱
        $output = "";
        for ($i = 0; $i < $len; $i++) {
            $output .= $chars[mt_rand(0, $charsLen)];
        }

        return $output;
    }
}
if (!function_exists('fsock_no_wait_post')) {
    /**
     * 直接提交数据不等待返回结果
     *
     * @param $url
     * @param $data
     */
    function fsock_no_wait_post($url, $data = null)
    {
        $pams = '';
        $lens = 0;
        if (!is_null($data)) {
            $pams = trim(http_build_query($data));
            $lens = strlen($pams);
        }

        $info = parse_url($url);
        $fp   = fsockopen($info["host"], 80, $errno, $errstr, 8);
        $qury = isset($info["query"]) ? $info["query"] : '';
        $head = "POST " . $info['path'] . "?" . $qury . " HTTP/1.0\r\n";
        $head .= "Host: " . $info['host'] . "\r\n";
        $head .= "Referer: http://" . $info['host'] . $info['path'] . "\r\n";
        $head .= "Content-type: application/x-www-form-urlencoded\r\n";
        $head .= "Content-Length: " . $lens . "\r\n";
        $head .= "\r\n";
        $head .= $pams;
        fputs($fp, $head);
        fclose($fp);
    }
}

if (!function_exists('tail_file')) {
    /**
     * 获取文件最后几行
     * <AUTHOR>
     * @date   2017-01-13
     *
     * @param  string  $filename  文件名称
     * @param  int  $line  获取几行
     *
     * @return
     */
    function tail_file($file, $num = 100)
    {
        if (!file_exists($file)) {
            return flase;
        }

        $fp  = fopen($file, "r");
        $pos = -2;
        $eof = "";

        //当总行数小于Num时，判断是否到第一行了
        $head  = false;
        $lines = [];

        while ($num > 0) {
            while ($eof != "\n") {
                if (fseek($fp, $pos, SEEK_END) == 0) {
                    //fseek成功返回0，失败返回-1
                    $eof = fgetc($fp);
                    $pos--;
                } else {
                    //当到达第一行，行首时，设置$pos失败
                    fseek($fp, 0, SEEK_SET);

                    //到达文件头部，开关打开
                    $head = true;
                    break;
                }

            }

            //数据写入返回数组
            array_unshift($lines, fgets($fp));

            //这一句，只能放上一句后，因为到文件头后，把第一行读取出来再跳出整个循环
            if ($head) {
                break;
            }

            $eof = "";
            $num--;
        }

        fclose($fp);

        return $lines;
    }
}

if (!function_exists('exec_zip')) {
    /**
     * 调用linux命令进行压缩操作 报表中心专用
     *
     * @param  int  $id  报表中心对应的任务ID
     */
    function exec_zip($id)
    {
        $downLoadDir = DOWNLOAD_DIR;
        $excelDir    = EXCEL_DIR;

        if (!is_dir($downLoadDir)) {
            mkdir($downLoadDir);
            $res = mkdir($downLoadDir, 0777, true);
            if (!$res) {
                return false;
            }
        }

        $commond = "zip -rj {$downLoadDir}{$id}.zip {$excelDir}{$id}";

        exec($commond);
    }
}

if (!function_exists('is_mobile')) {
    /**
     * 判断是不是手机登录
     * <AUTHOR>
     * @date   2017-03-28
     *
     * @return bool
     */
    function is_mobile()
    {
        // 如果有HTTP_X_WAP_PROFILE则一定是移动设备
        if (isset($_SERVER['HTTP_X_WAP_PROFILE'])) {
            return true;
        }
        // 如果via信息含有wap则一定是移动设备,部分服务商会屏蔽该信息
        if (isset($_SERVER['HTTP_VIA'])) {
            // 找不到为flase,否则为true
            return stristr($_SERVER['HTTP_VIA'], "wap") ? true : false;
        }
        // 脑残法，判断手机发送的客户端标志,兼容性有待提高
        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $clientkeywords = [
                'nokia',
                'sony',
                'ericsson',
                'mot',
                'samsung',
                'htc',
                'sgh',
                'lg',
                'sharp',
                'sie-',
                'philips',
                'panasonic',
                'alcatel',
                'lenovo',
                'iphone',
                'ipod',
                'blackberry',
                'meizu',
                'android',
                'netfront',
                'symbian',
                'ucweb',
                'windowsce',
                'palm',
                'operamini',
                'operamobi',
                'openwave',
                'nexusone',
                'cldc',
                'midp',
                'wap',
                'mobile',
            ];
            // 从HTTP_USER_AGENT中查找手机浏览器的关键字
            if (preg_match("/(" . implode('|', $clientkeywords) . ")/i", strtolower($_SERVER['HTTP_USER_AGENT']))) {
                return true;
            }
        }
        // 协议法，因为有可能不准确，放到最后判断
        if (isset($_SERVER['HTTP_ACCEPT'])) {
            // 如果只支持wml并且不支持html那一定是移动设备
            // 如果支持wml和html但是wml在html之前则是移动设备
            if ((strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') !== false) && (strpos($_SERVER['HTTP_ACCEPT'],
                        'text/html') === false || (strpos($_SERVER['HTTP_ACCEPT'],
                            'vnd.wap.wml') < strpos($_SERVER['HTTP_ACCEPT'], 'text/html')))) {
                return true;
            }
        }

        return false;
    }
}

if (!function_exists('get_base_url')) {

    /**
     * 获取除当前访问文件名外的url
     * @return string
     */
    function get_base_url()
    {

        $host = $_SERVER['HTTP_HOST'];

        $uriPath = dirname($_SERVER['REQUEST_URI']);

        if ($uriPath == '/') {
            $baseUrl = $host . $uriPath;
        } else {
            $baseUrl = $host . $uriPath . '/';
        }

        $baseUrl = 'http://' . $baseUrl;

        return $baseUrl;

    }

}

if (!function_exists('array_key')) {
    /**
     * 指定二维数组中的某个字段的值作为key
     *
     * <AUTHOR>
     * @date   2017-04-05
     *
     * @param  array  $array  数组
     * @param  string  $column  要作为key的字段
     *
     * @return array
     */
    function array_key($array, $column)
    {
        if (!is_array($array)) {
            return [];
        }
        $return = [];
        foreach ($array as $item) {
            $return[$item[$column]] = $item;
        }

        return $return;
    }

}

if (!function_exists('parse_env')) {
    /**
     * 获取当前的环境
     * <AUTHOR>
     * @date   2017-06-23
     */
    function parse_env()
    {
        if (ENV == 'DEVELOP') {
            if (stripos($_SERVER['HTTP_HOST'], '.local')) {
                return 'LOCAL';
            }
        }

        return ENV;
    }
}

if (!function_exists('image_opt')) {
    /**
     * 优化图片的大小(针对富文本)
     * <AUTHOR>
     * @date   2017-07-13
     *
     * @param  int  $content  文本内容
     * @param  int  $width  要限制的图片宽度
     * @param  int  $height  要限制的图片长度
     *
     * @return string
     */
    function image_opt($content, $width, $height = 0)
    {
        $pattern = "/<[img|IMG].*?src=[\'|\"](.*?(?:[\.gif|\.jpg|\.png]))[\'|\"].*?[\/]?>/";
        preg_match_all($pattern, $content, $match);

        if ($match[1]) {

            $tail = "imageView2/2/w/{$width}";

            if ($height) {
                $tail .= "/h/{$height}";
            }

            foreach ($match[1] as $item) {
                if (strpos($item, '?') !== false) {
                    $join = '&';
                } else {
                    $join = '?';
                }
                $tmpRepalce = $item . $join . $tail;
                $content    = str_replace($item, $tmpRepalce, $content);
            }
        }

        return $content;
    }

}

if (!function_exists('inWechatSmallApp')) {
    /**
     * 是否来自小程序的请求
     * <AUTHOR>
     * @date   2017-09-27
     *
     * @param  int  $type  0 支付宝+微信小程序，1微信小程序，2支付宝小程序
     *
     * @return bool
     */
    function inWechatSmallApp($type = 0)
    {
        $header = [];
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
        } else {
            foreach ($_SERVER as $key => $value) {
                if ('HTTP_' == substr($key, 0, 5)) {
                    $headers[str_replace('_', '-', substr($key, 5))] = $value;
                }
            }
        }
        if ($type == 0) {
            return isset($headers['SMALL-APP']) || isset($headers['ALIPAY-SMALL-APP']);
        } elseif ($type == 1) {
            return isset($headers['SMALL-APP']);
        } elseif ($type == 2) {
            return isset($headers['ALIPAY-SMALL-APP']);
        }

        return false;
    }
}

if (!function_exists('pft_trace')) {
    function pft_trace()
    {
        $errStr    = "Trace:\n";
        $traceInfo = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
        foreach ($traceInfo as $item) {
            $errStr .= "----------";
            $errStr .= "FILE:{$item['file']}    ";
            $errStr .= "LINE:{$item['line']}    ";
            $errStr .= "FUNCTION:{$item['function']}    ";
            $errStr .= "\n";
        }

        return $errStr;
    }
}

if (!function_exists('serialHashKey')) {
    /**
     * 获取流水号哈希key
     *
     * @date   2017-11-08
     * <AUTHOR> Lan
     *
     * @param  $params
     *
     * @return string
     */
    function serialHashKey($params)
    {
        $rand = explode('.', microtime(true));

        return $params . $rand[1] . mt_rand(0, 9);
    }
}

if (!function_exists('microtime_float')) {

    function microtime_float()
    {
        [$usec, $sec] = explode(" ", microtime());

        return ((float)$usec + (float)$sec);
    }
}

/**
 * 乱七八糟的字符过滤
 * wengbin
 */
if (!function_exists('content_filter')) {

    function content_filter($content)
    {
        return preg_replace_callback('/[\xf0-\xf7].{3}/', function ($r) {
            return '';
        }, $content);
    }

}

/**
 * 前端错误跳转
 * productOrder_show.html 页面还有使用到这个方法
 *
 * <AUTHOR>
 * @date   2018-03-21
 *
 * @param  url  $url  跳转的地址
 * @param  string  $msg  提示的消息
 *
 * @return null
 */
if (!function_exists('getOut')) {

    function getOut($url, $msg = '')
    {
        $js = "<script>";
        if ($msg) {
            $js .= "alert('$msg');";
        }

        $js .= "window.location='$url'</script>";
        exit($js);
    }

}

/**
 * 处理平台图片不兼容https
 *
 * <AUTHOR>
 * @date   2018-05-22
 *
 * @param  url  $imgUrl  图片地址
 *
 * @return string
 */
if (!function_exists('images_url_https')) {

    function images_url_https($imgUrl)
    {
        if (REQUEST_SCHEME == 'https') {
            $tmp = parse_url($imgUrl);
            if ($tmp && isset($tmp['host'])) {
                $hostList = ['ol3ooubvm.bkt.clouddn.com', 'images.pft12301.cc', 'images.12301.cc'];
                if (in_array($tmp['host'], $hostList)) {
                    if ($tmp['scheme'] && isset($tmp['scheme']) && $tmp['scheme'] == 'http') {
                        $imgUrl = str_replace('http://', 'https://', $imgUrl);
                    }
                }
            }
        }

        return $imgUrl;
    }
}

if (!function_exists('money_fmt')) {
    /**
     * 资金格式化
     * 说明：不能直接用number_format，不然默认会改变返回值为带,的字符串，导致无法进行后续计算
     *
     * @param  int  $num  传入数值 - 单位分
     * @param  int  $decimals  保留小数位数
     * @param  boolean  $compact  去除尾0
     *
     * @return float                        结果
     */
    function money_fmt($num, $decimals = 2, $compact = false)
    {
        $tmpNum = $num / 100;

        return $compact ? sprintf('%.' . $decimals . 'F', $tmpNum) + 0 : sprintf('%.' . $decimals . 'F', $tmpNum);
    }
}

if (!function_exists('float2int')) {
    /**
     * 将浮点数转换成整数
     * 比如：156638.8元 => 15663880分  float2int(156638.8, 2, 100)
     * 备注：如果直接 intval(156638.8 * 100) => 15663879
     *
     * @param  float  $floatnum  浮点数
     * @param  integer  $decimalPoint  精确到几位小数点
     * @param  integer  $multiple  倍数
     *
     * @return int
     */
    function float2int($floatnum, $decimalPoint = 2, $multiple = 100)
    {
        //按四舍五入处理成对应小数点位数的字符串
        $tmpNum = number_format(floatval($floatnum), $decimalPoint, '.', '');

        //乘以对应的倍数然后取整
        $intNum = intval(bcmul($tmpNum, $multiple));

        return $intNum;
    }
}

if (!function_exists('inWechatApp')) {

    /**
     * 是否在微信app内
     * @return [type] [description]
     */
    function inWechatApp()
    {
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        $pos        = strpos($user_agent, 'MicroMessenger');

        return $pos === false ? false : true;
    }

}

if (!function_exists('pft_authcode')) {
    /**
     * 对称加解密（从ucenter中提取）
     *
     * @param  string  $string  要加解密的字符串
     * @param  string  $operation  (ENCODE | DECODE)
     * @param  string  $key  自定义key
     * @param  int  $expiry  有效期
     *
     * @return string
     *
     * @example
     *
     *  $a = authcode('abc', 'ENCODE', 'key');
     *  $b = authcode($a, 'DECODE', 'key');
     *
     *  $a = authcode('abc', 'ENCODE', 'key', 3600);
     *  $b = authcode('abc', 'DECODE', 'key');
     */
    function pft_authcode($string, $operation = 'DECODE', $key = '', $expiry = 0)
    {

        $ckey_length = 4;

        $key  = md5($key ? $key : UC_KEY);
        $keya = md5(substr($key, 0, 16));
        $keyb = md5(substr($key, 16, 16));
        $keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length) : substr(md5(microtime()),
            -$ckey_length)) : '';

        $cryptkey   = $keya . md5($keya . $keyc);
        $key_length = strlen($cryptkey);

        $string        = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d',
                $expiry ? $expiry + time() : 0) . substr(md5($string . $keyb), 0, 16) . $string;
        $string_length = strlen($string);

        $result = '';
        $box    = range(0, 255);

        $rndkey = [];
        for ($i = 0; $i <= 255; $i++) {
            $rndkey[$i] = ord($cryptkey[$i % $key_length]);
        }

        for ($j = $i = 0; $i < 256; $i++) {
            $j       = ($j + $box[$i] + $rndkey[$i]) % 256;
            $tmp     = $box[$i];
            $box[$i] = $box[$j];
            $box[$j] = $tmp;
        }

        for ($a = $j = $i = 0; $i < $string_length; $i++) {
            $a       = ($a + 1) % 256;
            $j       = ($j + $box[$a]) % 256;
            $tmp     = $box[$a];
            $box[$a] = $box[$j];
            $box[$j] = $tmp;
            $result  .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
        }

        if ($operation == 'DECODE') {
            if ((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) && substr($result, 10,
                    16) == substr(md5(substr($result, 26) . $keyb), 0, 16)) {
                return substr($result, 26);
            } else {
                return '';
            }
        } else {
            return $keyc . str_replace('=', '', base64_encode($result));
        }
    }

    if (!function_exists('create_guid')) {
        function create_guid($namespace = '')
        {
            static $guid = '';
            $uid  = uniqid("", true);
            $data = $namespace;
            $data .= $_SERVER['REQUEST_TIME'] ?? '';
            $data .= $_SERVER['HTTP_USER_AGENT'] ?? '';
            $data .= $_SERVER['LOCAL_ADDR'] ?? '';
            $data .= $_SERVER['LOCAL_PORT'] ?? '';
            $data .= $_SERVER['REMOTE_ADDR'] ?? '';
            $data .= $_SERVER['REMOTE_PORT'] ?? '';
            $hash = strtoupper(hash('ripemd128', $uid . $guid . md5($data)));
            $guid =
                substr($hash, 0, 8) .
                substr($hash, 8, 4) .
                substr($hash, 12, 4) .
                substr($hash, 16, 4) .
                substr($hash, 20, 12);

            return $guid;
        }
    }
}
if (!function_exists('returnRpcData')) {
    function returnRpcData($code = '200', $data = [], $msg = "rpc远程调用成功")
    {
        $returnData         = [];
        $returnData['msg']  = $msg == '' ? 'rpc远程调用成功' : $msg;
        $returnData['code'] = $code;
        $returnData['data'] = $data;

        return json_encode($returnData, JSON_UNESCAPED_UNICODE);
    }
}

/**
 * 响应http状态码
 * copy from https://www.darklaunch.com/2010/09/01/http-status-codes-in-php-http-header-response-code-function
 * @author: guanpeng
 * @date: 2019/6/27
 *
 * @param $num
 */
function HTTPStatus($num)
{
    $http = [
        100 => 'HTTP/1.1 100 Continue',
        101 => 'HTTP/1.1 101 Switching Protocols',
        200 => 'HTTP/1.1 200 OK',
        201 => 'HTTP/1.1 201 Created',
        202 => 'HTTP/1.1 202 Accepted',
        203 => 'HTTP/1.1 203 Non-Authoritative Information',
        204 => 'HTTP/1.1 204 No Content',
        205 => 'HTTP/1.1 205 Reset Content',
        206 => 'HTTP/1.1 206 Partial Content',
        300 => 'HTTP/1.1 300 Multiple Choices',
        301 => 'HTTP/1.1 301 Moved Permanently',
        302 => 'HTTP/1.1 302 Found',
        303 => 'HTTP/1.1 303 See Other',
        304 => 'HTTP/1.1 304 Not Modified',
        305 => 'HTTP/1.1 305 Use Proxy',
        307 => 'HTTP/1.1 307 Temporary Redirect',
        400 => 'HTTP/1.1 400 Bad Request',
        401 => 'HTTP/1.1 401 Unauthorized',
        402 => 'HTTP/1.1 402 Payment Required',
        403 => 'HTTP/1.1 403 Forbidden',
        404 => 'HTTP/1.1 404 Not Found',
        405 => 'HTTP/1.1 405 Method Not Allowed',
        406 => 'HTTP/1.1 406 Not Acceptable',
        407 => 'HTTP/1.1 407 Proxy Authentication Required',
        408 => 'HTTP/1.1 408 Request Time-out',
        409 => 'HTTP/1.1 409 Conflict',
        410 => 'HTTP/1.1 410 Gone',
        411 => 'HTTP/1.1 411 Length Required',
        412 => 'HTTP/1.1 412 Precondition Failed',
        413 => 'HTTP/1.1 413 Request Entity Too Large',
        414 => 'HTTP/1.1 414 Request-URI Too Large',
        415 => 'HTTP/1.1 415 Unsupported Media Type',
        416 => 'HTTP/1.1 416 Requested Range Not Satisfiable',
        417 => 'HTTP/1.1 417 Expectation Failed',
        500 => 'HTTP/1.1 500 Internal Server Error',
        501 => 'HTTP/1.1 501 Not Implemented',
        502 => 'HTTP/1.1 502 Bad Gateway',
        503 => 'HTTP/1.1 503 Service Unavailable',
        504 => 'HTTP/1.1 504 Gateway Time-out',
        505 => 'HTTP/1.1 505 HTTP Version Not Supported',
    ];
    header($http[$num]);
}

if (!function_exists('filter_utf8')) {
    function filter_utf8($str)
    {
        /*utf8 编码表：
        * Unicode符号范围           | UTF-8编码方式
        * u0000 0000 - u0000 007F   | 0xxxxxxx
        * u0000 0080 - u0000 07FF   | 110xxxxx 10xxxxxx
        * u0000 0800 - u0000 FFFF   | 1110xxxx 10xxxxxx 10xxxxxx
        *
        */
        $re  = '';
        $str = str_split(bin2hex($str), 2);

        $mo  = 1 << 7;
        $mo2 = $mo | (1 << 6);
        $mo3 = $mo2 | (1 << 5);         //三个字节
        $mo4 = $mo3 | (1 << 4);          //四个字节
        $mo5 = $mo4 | (1 << 3);          //五个字节
        $mo6 = $mo5 | (1 << 2);          //六个字节

        for ($i = 0; $i < count($str); $i++) {
            if ((hexdec($str[$i]) & ($mo)) == 0) {
                $re .= chr(hexdec($str[$i]));
                continue;
            }

            //4字节 及其以上舍去
            if ((hexdec($str[$i]) & ($mo6)) == $mo6) {
                $i = $i + 5;
                continue;
            }

            if ((hexdec($str[$i]) & ($mo5)) == $mo5) {
                $i = $i + 4;
                continue;
            }

            if ((hexdec($str[$i]) & ($mo4)) == $mo4) {
                $i = $i + 3;
                continue;
            }

            if ((hexdec($str[$i]) & ($mo3)) == $mo3) {
                $i = $i + 2;
                if (((hexdec($str[$i]) & ($mo)) == $mo) && ((hexdec($str[$i - 1]) & ($mo)) == $mo)) {
                    $r  = chr(hexdec($str[$i - 2])) .
                          chr(hexdec($str[$i - 1])) .
                          chr(hexdec($str[$i]));
                    $re .= $r;
                }
                continue;
            }

            if ((hexdec($str[$i]) & ($mo2)) == $mo2) {
                $i = $i + 1;
                if ((hexdec($str[$i]) & ($mo)) == $mo) {
                    $re .= chr(hexdec($str[$i - 1])) . chr(hexdec($str[$i]));
                }
                continue;
            }
        }

        return $re;
    }

    //下划线转驼峰
    if (!function_exists('camelize')) {
        function camelize($uncamelized_words, $separator = '_')
        {
            $uncamelized_words = $separator . str_replace($separator, " ", strtolower($uncamelized_words));

            return ltrim(str_replace(" ", "", ucwords($uncamelized_words)), $separator);
        }
    }

    /**
     * 　　* 驼峰命名转下划线命名
     * 　　* 思路:
     * 　　* 小写和大写紧挨一起的地方,加上分隔符,然后全部转小写
     * 　　*/
    function uncamelize($camelCaps, $separator = '_')
    {
        return strtolower(preg_replace('/([a-z])([A-Z])/', "$1" . $separator . "$2", $camelCaps));
    }

    /**
     * 判断是否ajax请求
     * @return boolean [description]
     */
    function isAjax()
    {
        if ((isset($_SERVER['HTTP_X_REQUESTED_WITH'])
             && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest')
            || !empty($_POST[C('VAR_AJAX_SUBMIT')])
            || !empty($_GET[C('VAR_AJAX_SUBMIT')])) {

            return true;
        }

        return false;
    }

    /**
     * 只保留字符串前后字符，隐藏中间用*代替（两个字符时只显示第一个）
     *
     * @param  string  $str  字符串
     * @param  int  $len  保留字符串前后几个字符
     *
     * @return string 格式化后的姓名
     */
    if (!function_exists('substr_cut')) {
        function substr_cut($str, $len)
        {
            if (!$str || mb_strlen($str, 'utf-8') == 1) {
                return $str;
            }
            $strlen = mb_strlen($str, 'utf-8');

            $firstStr = mb_substr($str, 0, $len, 'utf-8');
            $lastStr  = mb_substr($str, $strlen - $len, $len, 'utf-8');

            if ($strlen == 2) {
                return $firstStr . '*';
            }
            if (mb_strlen($str, 'utf-8') <= $len * 2) {
                return $str;
            }

            return $strlen == 2 ? $firstStr . str_repeat('*',
                    mb_strlen($str, 'utf-8') - 1) : $firstStr . str_repeat("*",
                    $strlen - $len * 2) . $lastStr;
        }
    }
}

if (!function_exists('cache_random_expire_time')) {

    /**
     * 随机缓存时长
     *
     *
     * @param  int  $fixDay             缓存时间固定时长
     * @param  int  $randomDay          缓存时间随机时长
     * @param  int  $expireTimeType     类型 1=缓存到期时间  2=缓存时长
     * @return int
     */
    function cacheRandomExpireTime(int $fixedDay = 2, int $randomDay = 1, $expireTimeType = 1)
    {
        //固定缓存时长
        $fixedTime = 3600 * 24 * $fixedDay;
        //随机缓存时长
        $randTime  = rand(1, 3600 * 24 * $randomDay);

        if ($expireTimeType == 1) {
            //到期时间
            $expireTime = time() + $fixedTime + $randTime;
        } else {
            $expireTime = $fixedTime + $randTime;
        }

        return $expireTime;
    }

}

if (!function_exists('diff_between_two_days')) {

    /**
     * 计算出两个时间段之间的天数
     *
     * @param  int  $timestamp  开始日期
     * @param  int  $timestamp2  结束日期
     *
     * @return float|int
     */
    function diff_between_two_days(int $timestamp, int $timestamp2)
    {
        if ($timestamp < $timestamp2) {
            $tmp        = $timestamp2;
            $timestamp2 = $timestamp;
            $timestamp  = $tmp;
        }

        return ceil(($timestamp - $timestamp2) / 86400);
    }
}
//15位外国人永久居留证
if (!function_exists('isForeignIdCard15')) {
    function isForeignIdCard15($cardNo) {
        return preg_match('/^[a-zA-Z]{3}\\d{12}$/', $cardNo);
    }
}
//18位外国人永久居留证
if (!function_exists('isForeignIdCard18')) {
    function isForeignIdCard18($cardNo) {
        if (strlen($cardNo) != 18) {
            return false;
        }
        $checkCode = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
        //本体码 加权因子 乘积
        $code = $weights = $product = [];
        for ($i = 18; $i > 1; $i--) {
            $code[18 - $i] = intval($cardNo[18 - $i]);
            $weights[18 - $i] = pow(2, $i - 1) % 11;
            $product[18 - $i] = $code[18 - $i] * $weights[18 - $i];
        }
        //乘积之和取模
        $modulus = array_sum($product) % 11;
        $checkDigit = intval($cardNo[17]);
        return $checkCode[$modulus] == strval($checkDigit);
    }
}

/**
 * 异常抛出
 * @return \Library\Exception\ThrowApiException
 */
function apiReport():\Library\Exception\ThrowApiException
{
    return new \Library\Exception\ThrowApiException();
}

if (!function_exists('inWhitelist')) {
    function inWhitelist(string $key, int $merchantId) : bool {
        try {
            $whiteList = \qconf::getConf("/php/platform/whitelist/$key");
            $whiteList = json_decode($whiteList, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return false;
            }
            if (empty($whiteList)) {
                return false;
            }
            if (!in_array($merchantId, $whiteList)) {
                return false;
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}

// 格式化UTC时间
if (!function_exists('formatUTCDateTime')) {
    function formatUTCDateTime(string $dateTime, string $format = 'Y-m-d H:i:s') : string {
        $dateTime = new \DateTime($dateTime, new DateTimeZone('UTC'));
        $dateTime->setTimezone(new \DateTimeZone(date_default_timezone_get()));
        return $dateTime->format($format);
    }
}

// 驼峰命名转下划线命名
if (!function_exists('camelToSnake')) {
    function camelToSnake($input): string {
        return strtolower(preg_replace('/[A-Z]/', '_$0', lcfirst($input)));
    }
}

if (!function_exists('format')) {
    function format(string $type, $field)
    {
        switch ($type) {
            case 'datetime':
                return date('Y-m-d H:i:s', strtotime($field));
            case 'int':
                return intval($field);
            case'string':
                return strval($field);
            case 'array_int':
                return toArrayInt($field);
            case 'array_string':
                return toArrayString($field);
            default:
                return $field;
        }
    }
}

if (!function_exists('toArrayInt')) {
    function toArrayInt($arr): array
    {
        if (is_array($arr)) {
            $arr = array_values($arr);
            foreach ($arr as $k => $v) {
                $arr[$k] = intval($v);
            }
        } else {
            $arr = [intval($arr)];
        }
        return $arr;
    }
}

if (!function_exists('toArrayString')) {
    function toArrayString($arr): array
    {
        if (is_array($arr)) {
            $arr = array_values($arr);
            foreach ($arr as $k => $v) {
                $arr[$k] = strval($v);
            }
        } else {
            $arr = [strval($arr)];
        }
        return $arr;
    }
}
