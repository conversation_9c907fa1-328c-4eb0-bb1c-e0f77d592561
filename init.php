<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2/18-018
 * Time: 17:13
 */
define('PROJECT_NAME', 'Service');
include_once __DIR__ . '/autoload.php';
include_once __DIR__ . '/Conf/pft.conf.php';
include_once __DIR__ . '/Common/functions.php';
C(include __DIR__ . '/Conf/config_' . strtolower(ENV) . '.php');
spl_autoload_register("\\AutoLoading\\loading::autoload");
include_once __DIR__ . '/vendor/autoload.php';
date_default_timezone_set('Asia/Shanghai');
//define('__TIMESTAMP__', date('Y-m-d H:i:s'));
//添加全局的初始化常量标识
//define('IN_PFT', true);
define('PFT_INIT', true);
define('DIR_WWW_DOCUMENT_ROOT', realpath(dirname(__DIR__)) . '/');

//载入预定义的hook
Library\Hook::init(__DIR__ . '/Conf/hook.conf.php');
if (ENV != 'LOCAL') {
    ini_set('display_errors', 'Off');
}
//业务逻辑不要在框架初始化的时候去加
