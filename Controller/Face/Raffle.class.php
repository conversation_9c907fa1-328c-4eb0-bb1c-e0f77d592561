<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/3/4
 * Time: 14:18
 */

namespace Controller\Face;

use Library\Controller;
use Library\Tools\YarClient;

class Raffle extends Controller
{
    private $client = null;
    public function __construct()
    {
        $this->client = new YarClient('face');
    }

    /**
     * 获取最新签到的前五个用户及总数
     * <AUTHOR> Li
     * @date 2019-03-04
     *
     * @return array
     */
    public function getList()
    {
        header("Access-Control-Allow-Origin: *");    //解决跨域问题
        $checkRes = $this->client->call('/Face/Raffle/getFaceRaffleInfo',[]);
        if ($checkRes['res']['code'] != 200) {
            $this->apiReturn(204, [], $checkRes['res']['msg']);
        }

        $this->apiReturn(200, $checkRes['res']['data'], $checkRes['res']['msg']);
    }

    /**
     * 将抽奖用户的状态改为已删除
     * <AUTHOR> Li
     * @date 2019-03-06
     *
     * @return array
     */
    public function deleteFaceRaffle()
    {
        header("Access-Control-Allow-Origin: *");    //解决跨域问题
        $checkRes = $this->client->call('/Face/Raffle/deleteFaceRaffle',[]);
        if ($checkRes['res']['code'] != 200) {
            $this->apiReturn(204, [], $checkRes['res']['msg']);
        }

        $this->apiReturn(200, $checkRes['res']['data'], $checkRes['res']['msg']);
    }



}