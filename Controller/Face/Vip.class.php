<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: guanpeng
 * Date: 2018/12/28
 * Time: 16:15
 */

namespace Controller\Face;


use Business\Face\PersonTypeService;
use Business\JsonRpcApi\ScenicLocalService\Face;
use Business\Member\Member;
use Controller\Admin\Auth;
use Library\Business\WechatSmallApp;
use Library\Constants\BizCode;
use Library\Controller;
use Library\Helper\ErrorHelper;
use Library\Tools\Helpers;
use Library\Tools\YarClient;
use Model\AdminConfig\StaffManage;
use Process\Resource\Qiniu;
use Qiniu\Storage\UploadManager;
use Business\Face\FaceVip;

class Vip extends Controller
{
    private $client = null;
    private $sid    = 0;
    public function __construct()
    {
        parent::__construct();
        if (ENV=='PRODUCTION') {
            parent::isLogin();
        }
        if (ENV == 'LOCAL') {
            $this->sid = 6970;
        } else {
            $this->sid = $this->getLoginInfo()['sid'];
        }
        $this->client = new YarClient('face');
    }

    public function register()
    {
        $params = I('post.');
        if (!Helpers::isMobile($params['mobile'])) {
            parent::apiReturn(201, [], '手机号格式错误');
        }
        if (empty($params['name'])) {
            parent::apiReturn(201, [], '姓名不能为空');
        }
        $staffId = 0;
        if ($params['personType'] == 3 ) {
            if (!$params['staff_id']) {
                parent::apiReturn(201, [], '员工ID错误');
            }
            $staffId = $params['staff_id'] + 0;
        }

        $temId = date('YmdHis') . rand(1, 999);
        $imgResult = Helpers::uploadImage2AliOss('vipface', $params['photo_base64'], 'base64', $temId);
        if ($imgResult['code'] != 200 || empty($imgResult['data']['src'])) {
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '人脸图片上传失败');
        }
        $imgUrl = $imgResult['data']['src'];

        $saveData = [
            'member_id'     => $this->sid,
            'mobile'        => $params['mobile'],
            'vipName'       => $params['name'],
            'base64Img'     => $params['photo_base64'],
            'effectiveDate' => date('Ymd', strtotime($params['effectiveDate'])),
            'expireDate'    => date('Ymd', strtotime($params['expireDate'])),
            'deviceKeys'    => $params['deviceKeys'],
            'personType'    => $params['personType'],
            'idCard'        => $params['idCard'] ?: '',
            'needAuth'      => false,
            'staffId'       => $staffId,
            'codeId'        => 0,
            'imgUrl'        => $imgUrl,
            'type'          => $params['type'] ?: 1,
        ];
        $data = $this->client->call('/Face/Vip/register', $saveData);
        unset($saveData['base64Img'], $_POST['photo_base64']);
        pft_log('face/vip', 'register#' . json_encode($saveData, JSON_UNESCAPED_UNICODE)
            . ';req=' . json_encode($_POST, JSON_UNESCAPED_UNICODE)
            .';res='. json_encode($data, JSON_UNESCAPED_UNICODE) );
        if ($data['code'] == 200) {
            parent::apiReturn($data['res']['code'], $data['res']['data'], $data['res']['msg']);
        }
        parent::apiReturn(400,[], "RPC服务异常,错误代码:{$data['code']},错误描述:{$data['error_msg']}");
    }

    // /r/Face_Vip/getPassLog/getPersonTypeList
    public function getPersonTypeList()
    {
        try {
            $sid = $this->sid;
            $list = PersonTypeService::getInstance()->getPersonTypeList($sid, true);
            $this->apiReturn(BizCode::CODE_SUCCESS,$list,'success');
        } catch (\Throwable $e){
            $emsg = $e->getMessage();
            $code = $e->getCode();
            if(!$code) {
                $code = BizCode::CODE_PARAM_ERROR;
            }
            $this->apiReturn($code,null, $emsg);
        }
    }
    /**
     * 获取过闸日志
     *
     * @author: guanpeng
     * @date: 2019/1/8
     */
    public function getPassLog()
    {
        $faceId     = I('get.face_id');
        $name       = I('get.name', '');
        $bt         = I('get.begin_time', date('Y-m-d 00:00:00'));
        $et         = I('get.end_time',date('Y-m-d 23:59:59'));
        $deviceKey  = I('get.device_key');
        $limit      = I('get.limit', 20) +0;
        $page       = I('get.page', 1) +0;

        $type      = I('get.type');        //过闸类型 0VIP过闸 1订单过闸 4年卡过闸  员工卡过闸   人员接待-扩展出|人员类型。
        // type int 6=员工卡 0=人员接待 1订单过闸 4年卡过闸
        $identify  = I('get.identify');    //查询条件
        $status    = I('get.status', 2, 'intval');      //查询状态 0正常 1失败 2全部
        $personType    = I('person_type', 0, 'intval');      // int 1=vip  2=接待人员  3=员工 4=酒店住客

        $orderNum  = '';
        $mobile    = '';
        $idCardNo  = '';
        if ((strtotime($et) - strtotime($bt)) / 86400 > 90){
            parent::apiReturn(401,[], "查询时间间隔需小于90天");
        }
        if ($type != '0') { // 非 人员接待查询
            if (\ismobile($identify)) {
                //手机号
                $mobile   = $identify;
            } elseif (\Library\Tools::idcard_checksum18($identify)) {
                //身份证号
                $idCardNo = $identify;
            } else {
                //订单号
                $orderNum = $identify;
            }
        } else {
            $orderNum  = I('get.ordernum','','strval,trim');
            $code      = I('get.code', 0, 'intval');
            $chkCode   = I('get.chk_code','','strval,trim');
            $name      = I('get.name','','strval,trim');
            $mobile    = I('get.mobile','','strval,trim');
            $idCardNo  = I('get.id_card_no','','strval,trim');
        }

        $params = [
            'member_id'  => $this->sid, // 当前登录用户的sid
            'name'       => $name,
            'face_id'    => $faceId,
            'bt'         => $bt,
            'et'         => $et,
            'device_key' => $deviceKey,
            'limit'      => $limit,
            'page'       => $page,
            'ordernum'   => $orderNum,
            'mobile'     => $mobile,
            'idcard'     => $idCardNo,
            'type'       => $type,
            'status'     => $status,
            'code'       => $code,
            'chk_code'   => $chkCode,
            'person_type' => $personType,
        ];
//        var_export($params);
        $data       = (new Face())->getPassLog($params);//$this->client->call('/Face/Vip/getPassLog', $params);
        if ($data['code'] == 200) {
	        if ($data['data']['list']) {
	            $faceIdList = array_column($data['data']['list'],'faceid'); // 收集所有的人脸ID
                $memberList = [];
                $cardData = [];
	            if (in_array($type,[6,8])){ //type = 6 员工刷卡过闸机, type = 7 员工比对人脸过闸
                    $staffModel = new StaffManage();
                    $cardInfoList  = $staffModel->getCardByCardIdArr($faceIdList);
                    $cardMemberList = array_column($cardInfoList,'member_id');
                    $memberBiz = new Member();
                    $memberList = $memberBiz->getList($cardMemberList);
                    foreach ($cardInfoList as $k => $v){
                        $cardData[$v['card_id']] = [
                            'account' => isset($memberList[$v['member_id']]) ? $memberList[$v['member_id']]['account'] : '',
                            'visible_no' => $v['visible_no'],
                        ];
                    }
                }elseif ($type == 7){
                    $memberIdList = [];
                    foreach ($faceIdList as $key => $value){
                        $memberIdList[] = str_replace('STF','',$value);
                    }
                    $memberBiz = new Member();
                    $memberList = $memberBiz->getList($memberIdList);
                }

                $memberInfoList = [];
	            if ($type == 0) {
	                $operateIdList = array_column($data['data']['list'],'operate_id');
                    $operateIdList = array_values(array_filter(array_unique($operateIdList)));
                    $memberQueryRes = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                        [$operateIdList]);
                    $memberQueryRes['data'] = $memberQueryRes['data'] ?? [];
                    $memberInfoList = array_column($memberQueryRes['data'],null, 'id');
                }

		        $faceBiz = new \Business\Face\FaceBase();
		        //判断图片是否被做url压缩处理  没处理过的 加上后缀
		        foreach ($data['data']['list'] as &$item) {
		            //处理1970-01-01 08:00:00
                    if (strpos($item['end_time'], '1970') !==false) {
                        $item['end_time'] = '';
                    }
		        	$item['compare_face'] = $faceBiz->faceUrlConversion($item['compare_face']);
		        	$item['face_url']     = $faceBiz->faceUrlConversion($item['face_url']);
                    $item['compare_face'] = $faceBiz->faceUrlConversionToFace($item['compare_face']);
                    $item['face_url']     = $faceBiz->faceUrlConversionToFace($item['face_url']);
		        	if ($type == 7){
		        	    $item['account'] = isset($memberList[str_replace('STF','',$item['faceid'])]) ? $memberList[str_replace('STF','',$item['faceid'])]['account'] : '' ;
                    }elseif (in_array($type,[6,8])){
                        $item['account'] = isset($cardData[$item['faceid']]) ? $cardData[$item['faceid']]['account'] : '';
                        $item['visible_no'] = $cardData[$item['faceid']]['visible_no'];
		        	}elseif ($type == 0){
                        $item['create_name'] = $memberInfoList[$item['operate_id']]['dname'] ?? '';
                    }

		        }
	        }
            parent::apiReturn($data['code'], $data['data'], $data['msg']);
        }
        parent::apiReturn(401,[], "RPC服务异常,错误代码:{$data['code']},错误描述:{$data['error_msg']}");
    }

    public function deleteVip()
    {
        $vId        = I('post.vId') + 0;
        $onlyUnAuth = I('post.unAuth') + 0;
        if (!$vId) {
            parent::apiReturn(201, [], '参数错误');
        }
        $params = ['vId'=>$vId, 'onlyUnAuth'=>$onlyUnAuth, 'sid' => $this->sid];
        $data = $this->client->call('/Face/Vip/deleteVipById', $params);
        if ($data['code'] == 200) {
            parent::apiReturn($data['res']['code'], $data['res']['data'], $data['res']['msg']);
        }
        parent::apiReturn(400,[], "RPC服务异常,错误代码:{$data['code']},错误描述:{$data['error_msg']}");
    }

    /**
     * 手工授权
     *
     * @author: hanwenlin
     * @date: 2019/4/14
     */
    public function deviceAuth()
    {
        $vId = I('post.vId') + 0;
        if (!$vId) {
            parent::apiReturn(201, [], '参数错误');
        }
        $faceBiz = new FaceVip();
        $result = $faceBiz->deviceAuth($vId);
        parent::apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取当前登录用户不同类型的数量
     *
     * @author: guanpeng
     * @date: 2019/1/15
     */
    public function getPersonTypeSummary()
    {
        $params = [
            'member_id' => $this->sid,
        ];
        $data = $this->client->call('/Face/Vip/getPersonTypeSummary', $params);
        if ($data['code'] == 200) {
            parent::apiReturn($data['res']['code'], $data['res']['data'], $data['res']['msg']);
        }
        parent::apiReturn(400,[], "RPC服务异常,错误代码:{$data['code']},错误描述:{$data['error_msg']}");
    }

    public function getList()
    {
        $sid = $this->sid;
        $params = I('get.');
        $faceBiz = new FaceVip();
        $result =  $faceBiz->getFaceList($params, $sid);
        parent::apiReturn($result['code'],$result['data'], $result['msg']);
    }

    /**
     * 创建人脸小程序码
     * @author: guanpeng
     * @date: 2019/1/3
     */
    public function createMiniAppCode()
    {
        $params = I('post.');
        $sid = $this->sid;
        $faceBiz = new FaceVip();
        $result = $faceBiz->createMiniAppCode($params, $sid);
        parent::apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取小程序码列表
     * @author: guanpeng
     * @date: 2019/1/3
     */
    public function getMiniAppCodeList()
    {
        $page = I('get.page', 1, 'intval');
        $params = [
            'member_id' => $this->sid,
            'limit'     => '20',
            'page'      => $page,
        ];
        $data = $this->client->call('/Face/Vip/getMiniAppCodeList', $params);
        if ($data['code'] == 200) {
            if(!empty($data['res']['data'])) {
                $personTypeMap = PersonTypeService::getInstance()->getPersonTypeList($this->sid, false);
                foreach($data['res']['data'] as &$item){
                    $personType = (int)$item['person_type'];
                    $item['person_type_text'] = $personTypeMap[$personType] ?? '';
                }
            }
            parent::apiReturn($data['res']['code'], $data['res']['data'], $data['res']['msg']);
        }
        parent::apiReturn(400,[], "RPC服务异常,错误代码:{$data['code']},错误描述:{$data['error_msg']}");
    }

    /**
     * 删除人员接待小程序
     * @author: guanpeng
     * @date: 2019/1/3
     */
    public function deleteVipFromMiniCode()
    {
        $codeId = I('post.code_id', 0, 'intval');

        if (empty($codeId)) {
            parent::apiReturn(203,[], "缺少必要参数");
        }

        $params = [
            'codeId'   => $codeId,
            'memberId' => $this->sid,
        ];

        $data = $this->client->call('/Face/Vip/deleteMiniAppCode', $params);
        if ($data['code'] == 200) {
            parent::apiReturn($data['res']['code'], $data['res']['data'], $data['res']['msg']);
        }
        parent::apiReturn(400,[], "RPC服务异常,错误代码:{$data['code']},错误描述:{$data['error_msg']}");
    }


    /**
     * 修改有效期
     * @author: lanwanhui
     * @date: 2021/5/18
     */
    public function updateExpireDate()
    {
        $vId             = I('post.vId',0,'intval');                   //人脸vip接待管理表id
        $effectiveDate   = I('post.effective_date','','strval,trim');  //有效期开始时间
        $expireDate      = I('post.expire_date','','strval,trim');     //有效期结束时间

        if (empty($vId) || empty($effectiveDate) || empty($expireDate)) {
            $this->apiReturn(203,[], "缺少必要参数");
        }

        if (strtotime($effectiveDate) > strtotime($expireDate)) {
            $this->apiReturn(203,[], "有效期开始时间不能大于有效期结束时间");
        }

        $params = [
            'member_id'     => $this->sid,
            'vId'           => $vId,
            'effectiveDate' => date('Ymd', strtotime($effectiveDate)),
            'expireDate'    => date('Ymd', strtotime($expireDate)),
        ];

        $data = $this->client->call('/Face/Vip/updateExpireDate', $params);
        if ($data['code'] == 200) {
            parent::apiReturn($data['res']['code'], $data['res']['data'], $data['res']['msg']);
        }

        $this->apiReturn(400,[], "RPC服务异常,错误代码:{$data['code']},错误描述:{$data['error_msg']}");

    }
    
}