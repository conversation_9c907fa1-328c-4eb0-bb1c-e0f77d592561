<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: guanpeng
 * Date: 2019/1/14
 * Time: 19:02
 */

namespace Controller\Face;

use Library\Cache\Cache;
use Library\Tools\YarClient;
use Library\Tools\Helpers;
use Business\FaceIdentify\StaffFaceManage;
use Model\Product\Ticket;
use Model\Terminal\FaceCompare;
use Library\Business\TerminalCheck;

class Api
{
    private $client = null;

    public function __construct()
    {
        $this->client = new YarClient('face');
    }

    /**
     * 提供给Uface设备做云端人脸比对的接口
     *
     * @author: guanpeng
     * @date  : 2019/1/14
     */
    public function identify()
    {
        pft_log('orderface', 'request=' . json_encode(['request: ', $_REQUEST]));
        $requestId = I('post.requestId');//唯一序列号  硬件设备序号+特定ID
        $deviceKey = I('post.deviceKey');//硬件设备序号
        $imgFile   = $_FILES['file'];// 照片
        //$imgBase64   = base64_encode(file_get_contents($_POST['file']));// 照片
        $headPhoto = $imgFile['tmp_name'];
        if (!$requestId) {
            $this->returnData(0, [], '缺少唯一序列号');
        }
        if (!$headPhoto) {
            $this->returnData(0, [], '请上传图片');
        }
        if (!$deviceKey) {
            $this->returnData(0, [], '缺少设备号');
        }
//        $data = [
//            'identifyScore' => 100,
//            //人脸比对结果值
//            'type'          => 4,
//            //人脸类型,1年卡、2普通访客、3VIP 4订单'
//            'uid'           => 111,
//            //人脸ID 年卡中对应年卡的虚拟卡号
//            'requestId'     => $requestId,
//            //唯一序列号
//        ];
//        $res = [
//            'result' => 1,
//            'data'   => $data,
//            'msg'    => '识别成功',
//        ];
//        $this->returnData($res['result'], $res['data'], $res['msg']);
        $imgBase64 = base64_encode(file_get_contents($headPhoto));
        $time      = I('post.time');// 校验签名用的时间
        $sign      = I('post.sign'); // 安全校验签名
        if (md5($deviceKey . $time) != $sign) {
            $this->returnData(0, [], '签名校验失败');
        }

        $result = Helpers::uploadImage2AliOss('tmp_annual_card', $imgBase64);
        if ($result['code'] != 200) {
            $this->returnData(0, [], $result['msg']);
        }

        if (in_array($deviceKey, ['84E0F420724001F8', '84E0F420A58F01F8', '84E0F420A59501F8', '84E0F420A5A601F8'])) {
            //扬州何园的走独立市民卡流程
            $this->yangzhou($requestId, $deviceKey, $result['data']['src']);
        }

        // todo::调用Rpc服务,具体的业务在rpc那边实现，这边只负责结果返回
        $callResult = $this->client->call('/Face/Api/searchByDeviceKey',
            ['deviceKey' => $deviceKey, 'base64Img' => $imgBase64]);
        pft_log('orderface', 'threecallResult' . json_encode(['callResult', $callResult, $result['data']['src']],
                JSON_UNESCAPED_UNICODE));
        if ($callResult['code'] != 200) {
            $this->returnData(0, [], 'rpc服务器异常');
        }
        $uid        = $callResult['res']['data']['uid'];
        $groupId    = $callResult['res']['data']['group_id'];
        $faceid     = $callResult['res']['data']['faceid'];
        $deviceName = $callResult['res']['data']['device_name'];

        if (empty($uid) || $callResult['res']['code'] != 200) {
            $yarClient = new YarClient('face');
            $yarClient->call('/Face/Vip/addFacePassLog', [
                isset($faceid) ? $faceid : '',
                '',
                '',
                $result['data']['src'],
                $deviceKey,
                $deviceName,
                '',
                $callResult['res']['data']['aid'],
                '',
                '',
                0,
                0,
                0,
                0,
                3,
                '过闸失败',
                1,
                isset($callResult['res']['data']['score']) ? $callResult['res']['data']['score'] : 0,
                '',
            ]);
            $this->returnData(0, [], '识别失败');
        }
        $cache    = Cache::getInstance('redis');
        $lock_ret = $cache->lock($faceid, 1, 5);
        if (!$lock_ret) {
            pft_log('orderface', '5秒内重复请求req=' . $requestId);
            exit;
            //uface客户端有时候会瞬间多次请求，需要限制
            //$this->returnData(0, [], '两秒内不能重复请求');
        }

        if (strpos($groupId, 'annual_') !== false) {
            //年卡
            $this->annualCard(1, $callResult, $result['data']['src'], $uid, $deviceKey, $requestId);
        } elseif (strpos($faceid, 'STF') !== false) {
            //员工
            $this->staffFace(2, $callResult, $result['data']['src'], $faceid, $deviceKey, $requestId, $deviceName);
        } elseif (strpos($faceid, 'VIP') !== false) {
            //VIP接待
            $this->vipFace(3, $callResult, $result['data']['src'], $faceid, $deviceKey, $requestId, $deviceName);
        } else {
            //订单
            $this->orderFace(4, $callResult, $result['data']['src'], $faceid, $deviceKey, $requestId, $groupId,
                $deviceName);
        }

    }

    private function staffFace($type, $callResult, $imgUrl, $faceid, $deviceKey, $requestId, $deviceName)
    {
        if ($callResult['res']['code'] == 200) {
            $faceModel = new FaceCompare();
            $faceInfo  = $faceModel->getFaceInfoByFaceId(false, $faceid, 'face_url,idcard,mobile,nickname');
            if (empty($faceInfo)) {
                $this->returnData(0, [], '找不到员工');
            }
            $yarClient = new YarClient('face');
            $yarClient->call('/Face/Vip/addFacePassLog', [
                $faceid,
                $faceInfo['nickname'],
                '',
                $imgUrl,
                $deviceKey,
                $deviceName,
                '',
                $callResult['res']['data']['aid'],
                $faceInfo['idcard'],
                $faceInfo['mobile'],
                -1,
                0,
                0,
                0,
                0,
                '员工刷脸过闸',
                0,
                $callResult['res']['data']['score'],
                $faceInfo['face_url'],
            ]);

            $data = [
                'identifyScore' => (int)$callResult['res']['data']['score'],
                //人脸比对结果值
                'type'          => $type,
                //人脸类型,1年卡、2普通访客、3VIP 4订单'
                'uid'           => $faceid,
                //人脸ID 年卡中对应年卡的虚拟卡号
                'requestId'     => $requestId,
                //唯一序列号
            ];
            $this->returnData(1, $data, '请进');
        } else {
            $this->returnData(0, [], '人脸识别失败');
        }
    }

    private function vipFace($type, $callResult, $imgUrl, $faceid, $deviceKey, $requestId, $deviceName)
    {
        if ($callResult['res']['code'] == 200) {
            $faceModel = new FaceCompare();
            $faceInfo  = $faceModel->getVipFace($faceid, 'face_url,idcard,mobile,vip_name');
            if (empty($faceInfo)) {
                $this->returnData(0, [], '找不到VIP人脸');
            }
            $auth = explode('-', $callResult['res']['data']['faceinfo']);
            if (isset($auth[3]) && $auth[3] == 1) {
                $this->returnData(0, [], '未授权');
            }
            $yarClient = new YarClient('face');
            $yarClient->call('/Face/Vip/addFacePassLog', [
                $faceid,
                $faceInfo['vip_name'],
                '',
                $imgUrl,
                $deviceKey,
                $deviceName,
                '',
                $callResult['res']['data']['aid'],
                $faceInfo['idcard'],
                $faceInfo['mobile'],
                -1,
                0,
                0,
                0,
                0,
                'vip接待刷脸过闸',
                0,
                $callResult['res']['data']['score'],
                $faceInfo['face_url'],
            ]);

            $data = [
                'identifyScore' => (int)$callResult['res']['data']['score'],
                //人脸比对结果值
                'type'          => $type,
                //人脸类型,1年卡、2普通访客、3VIP 4订单'
                'uid'           => $faceid,
                //人脸ID 年卡中对应年卡的虚拟卡号
                'requestId'     => $requestId,
                //唯一序列号
            ];
            $this->returnData(1, $data, '请进');
        } else {
            $this->returnData(0, [], '人脸识别失败');
        }
    }

    private function orderFace($type, $callResult, $imgUrl, $faceid, $deviceKey, $requestId, $groupId, $deviceName)
    {
        if ($callResult['res']['code'] == 200) {
            $faceModel = new FaceCompare();
            $nowDate   = date('Ymd');
            $orderData = $faceModel->getFaceOrderByFaceId($faceid, $groupId, $nowDate,
                'lid,mobile,idcard,faceid,id,ordernum,created_time,idx,enter_num,is_delete,end_date');

            if (empty($orderData)) {
                $this->returnData(0, [], '无法获取订单');
            }

            $data = [
                'identifyScore' => (int)$callResult['res']['data']['score'],
                //人脸比对结果值
                'type'          => $type,
                //人脸类型,1年卡、2普通访客、3VIP 4订单'
                'uid'           => $faceid,
                //人脸ID 年卡中对应年卡的虚拟卡号
                'requestId'     => $requestId,
                //唯一序列号
            ];

            $this->faceReturn(1, $data, '请进');
            $orderModel = new \Model\Order\OrderTools('slave');
            $orderInfo  = $orderModel->getOrderInfo($orderData['ordernum'], 'salerid,aid,ordertel,lid');
            $count      = $faceModel->getNoUseNum($faceid, $groupId);
            $hash       = new \Library\Hashids\OrderHashids();
            $code       = 'OD#' . $hash->encode($orderData['ordernum']) . str_pad($orderData['idx'], 2, '0',
                    STR_PAD_LEFT);
            $landModel  = new \Model\Product\Land('slave');
            $landInfo   = $landModel->getLandInfo($orderData['lid'], false, 'terminal,apply_did');//根据lid取终端号
            $applyDid   = $landModel->getLandInfo($orderInfo['lid'], false, 'apply_did');//根据lid取终端号

            $terminalId = $landInfo['terminal'];
            if ($orderData['is_delete'] == 0) {
                $checkIp   = IP_TERMINAL_INSIDE;
                $port      = 2347;
                $checkLib  = TerminalCheck::connect($checkIp, $port);
                $checkTime = date('Y-m-d:H:i:s', time());
                $cfg       = ['vCheckDate' => $checkTime, 'ext_info' => [], 'vCmd' => 260, 'vMode' => 6];
                $chResult  = $checkLib->Terminal_Check_In_Voucher($terminalId, $orderInfo['salerid'], $code, $cfg);
                pft_log('orderface', 'threeFaceSearch:=' . json_encode([
                        'ordernum'        => $orderData['ordernum'],
                        'code'            => $code,
                        'codecheckResult' => $chResult,
                    ], JSON_UNESCAPED_UNICODE));
                if ($chResult['state'] == 'success') {
                    //解除人脸订单的绑定
                    $unbindResult = $faceModel->unbindFaceOrder($orderData['ordernum'], $orderData['idx']);
                }
            }

            if ($orderData['enter_num'] > 0) {
                $remainNum = $orderData['enter_num'] - 1;
                $faceModel->updateFaceEnterTime($orderData['ordernum'], $faceid, $remainNum);
            } elseif ($orderData['enter_num'] == -1) {
                $remainNum = -1;
            } else {
                $remainNum = 0;
            }

            if ($count == 1 && in_array($orderData['enter_num'], [-2, 0, 1])) {
                //没有其他未验证的订单时，删除百度库图片
                $delResult = $this->client->call('/Face/Order/baiduDelete', [$groupId, $faceid]);
            }
            pft_log('orderface', 'threeFaceSearch:delresult=' . json_encode([
                    'count'        => $count,
                    'unbindResult' => $unbindResult,
                    'delResult'    => $delResult,
                    'faceid'       => $faceid,
                    'group'        => $groupId,
                ], JSON_UNESCAPED_UNICODE));

            $endTime  = strtotime($orderData['end_date']) + 3600 * 24 - 1;
            $orderImg = $faceModel->getFaceInfoByFaceId(false, $faceid, 'face_url');

            pft_log('orderface', 'log=' . json_encode([
                    $orderData['faceid'],
                    '',
                    '',
                    $imgUrl,
                    $deviceKey,
                    $deviceName,
                    $orderData['ordernum'],
                    $applyDid['apply_did'],
                    $orderData['idcard'],
                    $orderData['mobile'],
                    $remainNum,
                    $endTime,
                    $terminalId,
                    $orderData['idx'],
                    1,
                    '订单刷脸过闸',
                    0,
                    $callResult['res']['data']['score'],
                    $orderImg['face_url'],
                ]));
            $this->client->call('/Face/Vip/addFacePassLog', [
                $orderData['faceid'],
                '',
                '',
                $imgUrl,
                $deviceKey,
                $deviceName,
                $orderData['ordernum'],
                $applyDid['apply_did'],
                $orderData['idcard'],
                $orderData['mobile'],
                $remainNum,
                $endTime,
                $terminalId,
                $orderData['idx'],
                1,
                '订单刷脸过闸',
                0,
                $callResult['res']['data']['score'],
                $orderImg['face_url'],
            ]);
            exit;
        } else {
            $this->returnData(0, [], '人脸识别失败');
        }
    }

    private function annualCard($type, $callResult, $imgUrl, $uid, $deviceKey, $requestId)
    {

        if ($callResult['res']['code'] != 200) {
            $optionParams = [
                'userName'    => '',
                'idCard'      => '',
                'mobile'      => '',
                'sid'         => 0,
                'remainNum'   => -1,
                'type'        => 'virtual_no',
                'virtualNo'   => '',
                'terminal'    => 0,
                'faceUrl'     => $imgUrl,
                'avalidEnd'   => 0,
                'deviceKey'   => $deviceKey,
                'memo'        => $callResult['res']['msg'],
                'score'       => $callResult['res']['data']['score'] ?: 0,
                'compareFace' => '',
            ];

            //增加比对人脸图片
            $annualCardModel = new \Model\Product\AnnualCard();
            $cardPhoto       = $annualCardModel->getGroupPhotos($callResult['res']['data']['uid']);
            if ($cardPhoto) {
                $optionParams['compareFace'] = json_decode($cardPhoto['photos'])[0];
            }

            $jobid = \Library\Resque\Queue::push('independent_system', 'Card_Job',
                [
                    'payTrackType' => 'pass_log',
                    'saveData'     => $optionParams,
                ]
            );
            pft_log('annual_card_request',
                json_encode(['人脸过闸日志记录', 'saveData: ' . json_encode($optionParams), ' jobid: ' . $jobid],
                    JSON_UNESCAPED_UNICODE), 3);

            $this->returnData(0, [], '识别失败');
        }
        $data = [
            'identifyScore' => (int)$callResult['res']['data']['score'],
            //人脸比对结果值
            'type'          => $type,
            //人脸类型,1年卡、2普通访客、3VIP 4订单'
            'uid'           => $uid,
            //人脸ID 年卡中对应年卡的虚拟卡号
            'requestId'     => $requestId,
            //唯一序列号
        ];
        $res  = [
            'result' => 1,
            'data'   => $data,
            'msg'    => '识别成功',
        ];
        //成功之后将图片地址等信息存入redis中
        $data['face_url'] = $imgUrl;  //人脸Url
        // X分钟内禁止重复过闸的唯一人脸标识，存储导Redis中
        $data['uniq_userinfo'] = md5(json_encode($callResult['res']['data']['user_infos']));
        $data['userinfo']      = json_encode($callResult['res']['data']['user_infos']);
        $data['device_key']    = $deviceKey;
        $cache                 = new StaffFaceManage();
        $redisKey              = "uface_identify:{$requestId}";
        $resss                 = $cache->identifyRedis($redisKey, $data);
        $cache                 = null;
        $this->returnData($res['result'], $res['data'], $res['msg']);
    }

    private function returnData($result = 0, $data = [], $msg = '')
    {
        $res = [
            'result' => $result,
            'data'   => $data,
            'msg'    => $msg,
        ];
        pft_log('orderface', 'threeDatareturn=' . json_encode($res, JSON_UNESCAPED_UNICODE));
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
        die();
    }

    /**
     * 接口数据返回,不退出
     * <AUTHOR>
     * @DateTime 2019-02-14
     *
     * @param  int  $code  返回码
     * @param  array  $data  接口返回数据
     * @param  string  $msg  错误说明，默认为空
     *
     * @return
     */
    public function faceReturn($code, $data = array(), $msg = '')
    {
        $data = array(
            'result' => $code,
            'data'   => $data,
            'msg'    => $msg,
        );

        $res = json_encode($data, JSON_UNESCAPED_UNICODE);
        pft_log('orderface', 'threefacereturn=' . $res);
        echo $res;
    }

    /**
     * 扬州市民卡人脸保存接口
     *
     * <AUTHOR>
     * @DateTime 2019-05-23
     *
     * @param  string  $requestId  请求id
     * @param  string  $deviceKey  设备号
     * @param  string  $url  图片地址
     *
     * @return
     */
    private function yangzhou($requestId, $deviceKey, $imgUrl)
    {
        $data               = [
            'identifyScore' => 99,
            'type'          => 5,//人脸类型,1年卡、2普通访客、3VIP 4订单 5扬州市民卡'
            'uid'           => '11',
            'requestId'     => $requestId,
        ];
        $res                = [
            'result' => 1,
            'data'   => $data,
            'msg'    => '识别成功',
        ];
        $data['face_url']   = $imgUrl;  //人脸Url
        $data['device_key'] = $deviceKey;
        $cache              = Cache::getInstance('redis');
        $redisKey           = "yangzhou:{$requestId}";
        $cache->set($redisKey, json_encode($data, JSON_UNESCAPED_UNICODE), '', 60);
        $this->returnData($res['result'], $res['data'], $res['msg']);
        exit;
    }
}