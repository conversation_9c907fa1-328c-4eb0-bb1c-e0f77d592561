<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: guanpeng
 * Date: 2019/1/2
 * Time: 15:33
 */

namespace Controller\Face;


use Library\Controller;
use Library\Tools\YarClient;

class Device extends Controller
{
    private $client = null;
    private $sid    = 0;
    public function __construct()
    {
        parent::__construct();
        if (ENV=='PRODUCTION') {
            parent::isLogin();
        }
        if (ENV == 'LOCAL') {
            $this->sid = 3385;
        } else {
            $this->sid = $this->getLoginInfo()['sid'];
        }
        $this->client = new YarClient('face');
    }

    public function getLists()
    {
        $data = $this->client->call('/Face/Device/getListByMemberId', [$this->sid]);
        if ($data['code'] == 200) {
            parent::apiReturn($data['res']['code'], $data['res']['data'], $data['res']['msg']);
        }
        parent::apiReturn(400,[], "RPC服务异常,错误代码:{$data['code']},错误描述:{$data['error_msg']}");
    }
}