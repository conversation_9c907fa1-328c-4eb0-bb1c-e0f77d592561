<?php
/**
 * User: hanwen<PERSON>
 * Date: 2018/12/28
 * Time: 16:15
 */

namespace Controller\Face;

use Library\Controller;
use Library\Tools\Helpers;
use Library\Tools\YarClient;
use Model\Terminal\FaceCompare;

class FaceSet extends Controller
{
    private $client = null;
    private $sid    = 0;
    public function __construct()
    {
        parent::__construct();
        $this->sid = $this->getLoginInfo()['sid'];
        if(empty($this->sid)) {
            parent::apiReturn(201, [], '请先登录');
        }
        $this->client = new YarClient('face');
    }

    /**
     * 设置小程序背景图
     *
     * @author: hanwenlin
     * @date  : 2019/5/7
     */
    public function setBackground()
    {
        $background = I('post.background');

        if (empty($background)) {
            parent::apiReturn(201, [], '背景图不能为空');
        }

        $imgResult = Helpers::uploadImage2AliOss('background', $background);
        if ($imgResult['code'] != 200 || empty($imgResult['data']['src'])) {
            parent::apiReturn(203, [], '背景图片上传失败');
        }

        $data = [
            'background' => $imgResult['data']['src'],
            'sid'        => $this->sid,
        ];
        $result = $this->yarCall('/Face/Order/setBackground', $data);
        if ($result['code'] != 200) {
            $result = [
                'code' => 203,
                'msg'  => 'RPC服务异常，' . $result['error_msg']
            ];
            return $result;
        }
        parent::apiReturn($result['res']['code'], [], $result['res']['msg']);
    }



    /**
     * 获取小程序背景图
     * @author: hanwenlin
     * @date: 2019/5/7
     */
    public function getBackground()
    {
        $faceModel = new FaceCompare();
        $result = $faceModel->getPlatformByAid($this->sid, 'background');
        $data['url'] = isset($result[0]) ? $result[0] : '';
        parent::apiReturn(200, $data);
    }

    private function yarCall($method, $data)
    {
        if(empty($this->_yarClient)) {
            $this->_yarClient = new YarClient('face');
        }
        return $this->_yarClient->call($method, $data);
    }

}