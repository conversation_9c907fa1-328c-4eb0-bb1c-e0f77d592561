<?php
/**
 * 产品相关权限控制
 * <AUTHOR>
 * @date 2019-12-31
 */

namespace Controller\Authority;
use Business\MemberLogin\MemberLoginHelper;
use Library\Controller;
use Business\Authority\AuthContext;
use Business\AppCenter\ModuleCommon;
use Business\ResourceCenter\Forward;
use Library\Cache\Cache;

class Product extends Controller
{

    // 登陆信息
    private $loginInfo;

    public function __construct()
    {
        parent::__construct();
        $this->loginInfo = $this->getLoginInfo();
    }


    /**
     * 用户可发布的产品类型
     * <AUTHOR>
     * @date   2019-12-21
     *
     * @return string
     */
    public function memberReleaseAuth()
    {
        $code = 200;
        $msg  = '';

        $memberId    = $this->loginInfo['memberID'];
        $returnData  = [];
        $typeDesc    = [];
        $proTypeAuth = [];

        $isSuper = $this->isSuper();

        $sid = $this->loginInfo['sid'];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        try {

            if (!$memberId) {
                throw new \Exception("参数错误");
            }

            //管理员有全部的权限
            if (!$isSuper) {
                //用户可以发布的产品类型
                $proTypeAuth = (new AuthContext())->memberProductTypeAuth($sid);
            }

            if ($proTypeAuth || $isSuper) {
                $typeDesc = (new \Business\Product\Product())->getProductType($proTypeAuth);
            }

            $returnData = ['product_type' => $typeDesc];

        } catch (\Exception $e) {

            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $returnData, $msg);
    }




    /**
     * 产品发布数量限制
     * <AUTHOR>
     * @date   2020-07-14
     *
     * @return string
     */
    public function releaseNumLimit()
    {
        $isUpdateProduct = I('is_update',0,'intval');
        $fid = $this->loginInfo['sid'];
        if (MemberLoginHelper::isSubMerchantLogin()){
            $fid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }
        if (!$fid) {
            $this->apiReturn(500, [], '获取用户信息失败, 请重新登录');
        }

        $res = (new \Business\Authority\AuthContext())->releaseProductNumLimit($fid,$isUpdateProduct);
        $res['res'] ? $this->apiReturn(200) : $this->apiReturn(201, [], $res['msg']);
    }



    /**
     * 是否显示被删除的产品数据
     * <AUTHOR>
     * @date   2020-07-14
     *
     * @return string
     */
    public function productShowAuth()
    {
        $fid = I('fid', 0, 'intval');

        if (!$fid) {
            $this->apiReturn(201, [], '参数错误');
        }

        if ($fid != $this->loginInfo['sid']) {
            $this->apiReturn(403, [], '无权限');
        }

        $returnData = ['show_delete_product' => false];
        $config = (new \Business\AppCenter\ExtConfig())->getIncreaseConfig($fid);
        if ($config['code'] != 200) {
            $this->apiReturn(403, [], '无权限');
        }

        $allowProductNum = $config['data']['product_num'];

        if ($allowProductNum == -1) {
            $returnData = ['show_delete_product' => true];
        }

        $this->apiReturn(200, $returnData, '');
    }

    /**
     * 资源中心中心权限
     * <AUTHOR>
     * @date 2021/11/9
     *
     */
    public function resourceCenterAuth()
    {
        $code = 200;
        $msg  = '';
        $data = [];

        try {
            $memberId = $this->loginInfo['memberID'];
            $sid      = $this->loginInfo['sid'];
            $dtype    = $this->loginInfo['dtype'];
            $qx       = $this->loginInfo['qx'];
            $tag      = 'resource_center_app';

            $statusMap = [
                'openAndSettled'    => '已开通已入住',
                'openAndNotSettled' => '已开通未入驻',
                'notOpen'           => '未开通',
            ];

            //判断当前账号是否开通了资源中心
            $moduleBiz = new ModuleCommon();
            $auth      = $moduleBiz->checkUserIsCanUseApp($sid, $tag);
            if (!$auth) {
                //未开通资源中心中心
                $status = 'notOpen';
            } else {
                if ($dtype == 6 && !in_array($tag, explode(',', $qx))) {
                    $status = 'notOpen';
                } else {
                    //缓存处理下
                    $cacheObj = Cache::getInstance('redis');
                    $cacheKey = "resource_center:settled_auth:$sid";
                    $cacheVal = $cacheObj->get($cacheKey);
                    if (!empty($cacheVal)) {
                        //获取不到默认未入驻
                        $supSettledState = json_decode($cacheVal, true)['supSettledState'] ?? false;
                    } else {
                        //判断资源中心是否入住 供货商入驻状态 Boolean supSettledState
                        $tag  = 'resource_queryResourceSupplierBaseInfo';
                        $biz  = new Forward();
                        $res  = $biz->forward($memberId, $sid, ['tag' => $tag], $dtype);
                        $data = $res['data'] ?? [];
                        //获取供应商入住状态，获取不到默认未入驻
                        $supSettledState = $data['supSettledState'] ?? false;
                        //缓存20s
                        $cacheObj->set($cacheKey, json_encode(['supSettledState' => $supSettledState]), '', 20);
                    }
                    if ($supSettledState) {
                        $status = 'openAndSettled';
                    } else {
                        $status = 'openAndNotSettled';
                    }
                }
            }

            $statusName = $statusMap[$status] ?? '未知';

            $data = [
                'status' => $status,
                'title'  => $statusName,
            ];
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }


    /**
     * 获取票属性权限
     * <AUTHOR>
     * @date 2022/4/6
     *
     */
    public function getAccessAttr()
    {
        $sid = $this->loginInfo['sid'];
        $account = $this->loginInfo['account'];
        $saccount = $this->loginInfo['saccount'];

        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $account = MemberLoginHelper::getLoginBusinessMember()->getSupplierAccount();
            $saccount = MemberLoginHelper::getLoginBusinessMember()->getSupplierAccount();
        }

        $authBiz = new AuthContext();
        $res = $authBiz->getProductAccessAttr($sid, $account, $saccount);

        $this->apiReturn(200, $res);
    }
}