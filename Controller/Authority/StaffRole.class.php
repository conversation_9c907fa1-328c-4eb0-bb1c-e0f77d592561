<?php
/**
 * 权限 - 员工相关入口
 * <AUTHOR>
 * @date   2023/3/28
 */

namespace Controller\Authority;

use Library\Controller;
use Business\Authority\AuthLogic as AuthLogicBiz;

class StaffRole extends Controller
{
    // 登陆信息
    private $_loginInfo;
    //商户id
    private $_sid = 0;
    //当前用户id
    private $_memberId = 0;
    //当前用户类型
    private $_dtype;

    public function __construct()
    {
        parent::__construct();
        $loginInfo = $this->getLoginInfo('ajax');

        $this->_loginInfo = $loginInfo;
        $this->_sid       = $loginInfo['sid'];
        $this->_memberId  = $loginInfo['memberID'];
        $this->_dtype     = $loginInfo['dtype'];
    }

    /**
     * 岗位管理
     * <AUTHOR>
     * @date   2023/3/28
     *
     */
    public function jobList()
    {
        $jobName = I('post.title', '', 'strval,trim');//岗位名称查询
        $jobType = I('post.type', '0', 'intval');//岗位类型：默认全部、1系统预设、2自定义

        $page = I('post.page', 1, 'intval');
        $size = I('post.size', 10, 'intval');

        $authLogicBiz = new AuthLogicBiz();
        $isStaff      = $authLogicBiz->checkIsStaff($this->_sid, $this->_memberId, $this->_dtype);
        $result       = $authLogicBiz->getJobs($this->_sid, $isStaff, $jobName, $jobType, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 添加岗位
     * <AUTHOR>
     * @date   2023/3/28
     *
     */
    public function jobCreate()
    {
        $jobName    = I('post.title', '', 'strval,trim');//岗位名称
        $jobRemarks = I('post.remarks', '', 'strval,trim');//岗位备注

        if ($jobName == '') {
            $this->apiReturn(203, [], '岗位名称不能为空');
        }

        $result = (new AuthLogicBiz())->createJob($this->_sid, $this->_memberId, $jobName, $jobRemarks);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 编辑岗位
     * <AUTHOR>
     * @date   2023/3/28
     *
     */
    public function jobEdit()
    {
        $jobId      = I('post.job_id', '0', 'intval');//岗位id
        $jobName    = I('post.title', '', 'strval,trim');//岗位名称
        $jobRemarks = I('post.remarks', '', 'strval,trim');//岗位备注

        if (!$jobId) {
            $this->apiReturn(203, [], '岗位信息错误');
        }

        if ($jobName == '') {
            $this->apiReturn(203, [], '岗位名称不能为空');
        }

        $result = (new AuthLogicBiz())->updateJob($this->_sid, $this->_memberId, $jobId, $jobName, $jobRemarks);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除岗位
     * <AUTHOR>
     * @date   2023/3/29
     *
     */
    public function jobDel()
    {
        $jobId    = I('post.job_id', '0', 'intval');//岗位id
        $changeId = I('post.change_id', '0', 'intval');//变更岗位id
        $isChange = I('post.is_change', '0', 'intval');//1是变更岗位， 其他就是只删除不变更

        if (!$jobId) {
            $this->apiReturn(203, [], '岗位信息错误');
        }

        if ($isChange == 1 && !$changeId) {
            $this->apiReturn(203, [], '请选择变更的岗位');
        }

        $isChange = $isChange == 1;

        $result = (new AuthLogicBiz())->delJob($this->_sid, $this->_memberId, $jobId, $isChange, $changeId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 同一个账号名称重复性验证
     * <AUTHOR>
     * @date   2023/3/28
     *
     */
    public function nameCheck()
    {
        $jobName = I('post.title', '', 'strval,trim');//岗位名称

        if ($jobName == '') {
            $this->apiReturn(203, [], '岗位名称不能为空');
        }

        $result = (new AuthLogicBiz())->jobNameCheck($this->_sid, $jobName);

        $this->apiReturn(200, $result, '');
    }

    /**
     * 创建角色包关联角色
     * <AUTHOR>
     * @date   2023/4/7
     *
     */
    public function createPackRole()
    {
        $jobId  = I('post.job_id', 0, 'intval');//角色包id
        $idsStr = I('post.ids_str', '', 'strval');//菜单id标识，英文逗号隔开
        $appId  = I('post.front_app_id', 0, 'intval');//系统应用id

        if (!$jobId) {
            $this->apiReturn(203, [], '岗位id错误');
        }
        if (!$appId) {
            $this->apiReturn(203, [], '系统应用id错误');
        }
        if (empty($idsStr)) {
            $this->apiReturn(203, [], '请选择功能权限');
        }

        $idsArr = explode(',', $idsStr);

        $res = (new AuthLogicBiz())->createPackRole($this->_sid, $this->_memberId, $jobId, $idsArr, $appId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 编辑角色包关联角色
     * <AUTHOR>
     * @date   2022/12/1
     *
     */
    public function editPackRole()
    {
        $jobId  = I('post.job_id', 0, 'intval');//角色包id
        $roleId = I('post.role_id', 0, 'intval');//角色id
        $idsStr = I('post.ids_str', '', 'strval');//菜单id标识，英文逗号隔开

        if (!$roleId) {
            $this->apiReturn(203, [], '角色id错误');
        }
        if (!$jobId) {
            $this->apiReturn(203, [], '岗位id错误');
        }

        $idsArr = explode(',', $idsStr);

        $res = (new AuthLogicBiz())->editPackRole($this->_sid, $this->_memberId, $jobId, $roleId, $idsArr);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 设置岗位菜单权限
     * <AUTHOR>
     * @date   2023/5/10
     *
     */
    public function saveJobMenuAuth()
    {
        $jobId      = I('post.job_id', 0, 'intval');//角色包id
        $idsStr     = I('post.ids_str', '', 'strval');//菜单id标识，英文逗号隔开
        $frontAppId = I('post.front_app_id', 0, 'intval');//菜单id标识，英文逗号隔开

        if (!$jobId) {
            $this->apiReturn(203, [], '岗位id错误');
        }

        if (!$frontAppId) {
            $this->apiReturn(203, [], '未选择系统');
        }

        $idsArr = explode(',', $idsStr);
        $idsArr = array_unique(array_filter(array_map("intval", $idsArr)));
        // if (empty($idsArr)) {
        //     $this->apiReturn(203, [], '请选择功能权限');
        // }

        $res = (new AuthLogicBiz())->saveRolePackageAndMenu($this->_sid, $this->_memberId, $jobId, $idsArr,
            $frontAppId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 查询用户角色包下指定系统应用权限
     * <AUTHOR>
     * @date   2023/4/7
     *
     */
    public function getMenuTree()
    {
        $jobId    = I('post.job_id', 0, 'intval');//角色包id
        $appId    = I('post.front_app_id', 0, 'intval');//系统应用id
        $isSelect = I('post.is_select', -1, 'intval');//是否只查询勾选0否1是
        if (!$jobId) {
            $this->apiReturn(203, [], '岗位id错误');
        }
        if (!$appId) {
            $this->apiReturn(203, [], '系统应用id错误');
        }

        $res = (new AuthLogicBiz())->getUserMenuTree($this->_sid, $jobId, $appId, $isSelect);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 查询商户全部岗位简易接口
     * <AUTHOR>
     * @date   2023/4/7
     *
     */
    public function getSimpleAllJobs()
    {
        $authLogicBiz = new AuthLogicBiz();
        $isStaff      = $authLogicBiz->checkIsStaff($this->_sid, $this->_memberId, $this->_dtype);
        $res          = $authLogicBiz->getSimpleAllJobs($this->_sid, $isStaff);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 赋予员工岗位权限
     * <AUTHOR>
     * @date   2023/4/10
     *
     */
    public function giveJobs()
    {
        // 授予用户
        $userId = I('post.user_id', '0', 'intval');
        if (!$userId) {
            $this->apiReturn(400, '', '非法参数');
        }

        $authLogicBiz = new AuthLogicBiz();

        // 是否有权限赋予角色
        if (!$authLogicBiz->checkGiveAuth($userId, $this->_memberId)) {
            $this->apiReturn(400, '', '无权限');
        }

        // 赋予岗位id
        $jobIds = I('post.job_ids', '', 'strval');

        //岗位id更是处理
        $jobIds = explode(',', $jobIds);
        $jobIds = array_map("trim", array_filter($jobIds));

        $authLogicBiz = new AuthLogicBiz();
        $isStaff      = $authLogicBiz->checkIsStaff($this->_sid, $this->_memberId, $this->_dtype);
        $res          = $authLogicBiz->giveJobs($this->_sid, $userId, $this->_memberId, $jobIds, $isStaff);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 设置数据岗位
     * @return void
     * @throws \Exception
     */
    public function giveDataJobs()
    {
        // 授予用户
        $userId = I('post.user_id', '0', 'intval');
        if (!$userId) {
            $this->apiReturn(400, '', '非法参数');
        }

        $authLogicBiz = new AuthLogicBiz();

        // 是否有权限赋予角色
        if (!$authLogicBiz->checkGiveAuth($userId, $this->_memberId)) {
            $this->apiReturn(400, '', '无权限');
        }

        // 赋予数据岗位id
        $jobIds = I('post.data_job_ids/a', []);

        //岗位id更是处理
        $jobIds = array_map('trim', array_filter($jobIds));

        $state = I('post.state', 0, 'intval');

        if ($state && !$jobIds) {
            $this->apiReturn(400, '', '数据岗位必选');
        }
        // 关闭的状态下清楚数据岗位
        if (!$state) {
            $jobIds = [];
        }
        $authLogicBiz = new AuthLogicBiz();
        $res          = $authLogicBiz->giveDataJobs($this->_sid, $userId, $this->_memberId, $jobIds);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取员工列表
     * <AUTHOR>
     * @date   2023/4/10
     *
     */
    public function getStaffList()
    {
        // 账号或手机号
        $accountMobile = I('post.account_mobile', '', 'strval,trim');
        // 当前页
        $page = I('post.page', 1, 'intval');
        // 获取页数
        $size = I('post.size', 15, 'intval');
        //员工卡类型
        $cardType = I('post.card_type', -1, 'intval'); // -1
        // -1全部 0正常1禁用
        $status = I('post.status', -1);
        //岗位id
        $jobId = I('job_id', 0, 'intval'); // 角色包id，支持下查询员工
        //数据岗位id
        $dataJobId = I('data_job_id', 0, 'intval'); // 数据岗位id

        $authLogicBiz = new AuthLogicBiz();
        $isStaff      = $authLogicBiz->checkIsStaff($this->_sid, $this->_memberId, $this->_dtype);
        $res          = $authLogicBiz->getMemberList($accountMobile, $this->_sid, $isStaff, $page, $size, $cardType,
            $status, $jobId, $dataJobId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取用户岗位信息（已有岗位信息）
     * <AUTHOR>
     * @date   2023/4/10
     *
     */
    public function getUserJobs()
    {
        $userId = I('post.user_id', '0', 'intval');

        $authLogicBiz = new AuthLogicBiz();
        $isStaff      = $authLogicBiz->checkIsStaff($this->_sid, $this->_memberId, $this->_dtype);
        $res          = $authLogicBiz->getUserJobs($this->_sid, $userId, $isStaff);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 设置岗位数据范围
     * <AUTHOR>
     * @date   2023/4/17
     *
     */
    public function setDataResource()
    {
        $jobId   = I('post.job_id', 0, 'intval');
        $config  = I('post.config/a');
        $limitId = I('post.limit_id', '', 'strval');
        if (!$jobId || empty($config) || !is_array($config)) {
            $this->apiReturn(400, '', '参数错误');
        }

        $res = (new AuthLogicBiz())->setDataResourceByJobId($this->_sid, $jobId, $config, $limitId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取产品列表
     * @return void
     */
    public function getProductList()
    {
        $pageNum = I('page_num', 1, 'intval');
        $pageSize = I('page_size', 15, 'intval');

        $keyword   = I('keyword', '', 'strval');
        //商品来源 1-转分销 2-自供应
        $supplyType = I('supply_type', 2, 'intval');

        $authLogicBiz = new AuthLogicBiz();
        $data = $authLogicBiz->getLidList($this->_sid, $keyword,
            $supplyType, $pageNum, $pageSize);
        $this->apiReturn(self::CODE_SUCCESS, $data);
    }
}