<?php
/**
 * 权限菜单
 * <AUTHOR>
 * @date 2019-05-29
 */
namespace Controller\Authority;

use Library\Controller;
use Business\Authority\AuthContext;

class Package extends Controller
{

    // 登陆信息
    private $loginInfo;

    public function __construct()
    {
        parent::__construct();
        $this->loginInfo = $this->getLoginInfo();
    }


    /**
     * 为用户分配套餐
     * <AUTHOR>
     * @date 2019-07-03
     */
    public function open()
    {
        if ($this->loginInfo['sdtype'] != 9) {
            $this->apiReturn(400, '', '无权限');
        }

        // 用户ID
        $memberId = I('post.member_id');
        // 渠道Id
        $channelId = I('post.channel_id');
        // 开通套餐，可能包含已开通套餐， [{id, begin_time, end_time},{}, {}]
        $package = I('post.package');

        if (empty($memberId) || empty($channelId)) {
            $this->apiReturn(400, '', '非法参数');
        }

        $operId = $this->loginInfo['memberID'];

        $business = new AuthContext();
        $res = $business->openPackage($memberId, $package, $channelId, $operId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取用户开通套餐列表
     * <AUTHOR>
     * @date 2019-07-16
     */
    public function getMemberList()
    {
        if ($this->loginInfo['sdtype'] != 9) {
            $this->apiReturn(400, '', '无权限');
        }

        // 账号
        $account = I('get.account', '');
        // 名称
        $dname   = I('get.dname', '');
        // 是否即将到期
        $recent = I('get.recent', 0);
        $page   = I('get.page', 1);
        $size   = I('get.size', 15);

        $business = new AuthContext();
        $res = $business->getMemberPackageList($account, $dname, $recent, $page, $size);

        $this->apiReturn(200, $res, '');
    }

    /**
     * 获取用户已开通套餐
     * <AUTHOR>
     * @date 2019-07-03
     */
    public function getMemberPackage()
    {
        if ($this->loginInfo['sdtype'] != 9) {
            $this->apiReturn(400, '', '无权限');
        }

        $memberId = I('get.member_id');
        if (empty($memberId)) {
            $this->apiReturn(400, '', '非法参数');
        }

        $channelId = I('get.channel_id');
        if (empty($channelId)) {
            $this->apiReturn(400, '', '渠道不能为空');
        }

        $business = new AuthContext();
        $data = $business->getMemberPackage($memberId, $channelId);

        $this->apiReturn(200, $data, '');
    }

    /**
     * 获取套餐操作记录
     * <AUTHOR>
     * @date 2019-07-19
     */
    public function getLogList()
    {
        if ($this->loginInfo['sdtype'] != 9) {
            $this->apiReturn(400, '', '无权限');
        }

        // 商家账号
        $account = I('get.account', '');
        // 操作员id
        $operId = I('get.oper_id', '');
        // 渠道
        $channelId = I('get.channel_id', '');
        // 操作开始时间
        $begin = I('get.begin', '');
        // 操作结束时间
        $end = I('get.end', '');

        $page = I('get.page', 1);
        $size = I('get.size', 15);

        $business = new AuthContext();
        $list = $business->getLogList($channelId, $account, $operId, $begin, $end, $page, $size);

        $this->apiReturn(200, $list, '');
    }

    /**
     * 获取套餐详情
     * <AUTHOR>
     * @date 2019-07-22
     */
    public function getDetailListByChannel()
    {
        if ($this->loginInfo['sdtype'] != 9) {
            $this->apiReturn(400, '', '无权限');
        }

        // 渠道
        $channelId = I('get.channel_id', '');

        $business = new AuthContext();
        $res = $business->getPackageDetailByChannelId($channelId);

        $this->apiReturn(200, $res, '');
    }

}