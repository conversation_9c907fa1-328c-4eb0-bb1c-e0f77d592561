<?php
/**
 * 权限页面元素
 * <AUTHOR>
 * @date 2019-06-09
 */

namespace Controller\Authority;
use Library\Controller;
use Business\Authority\AuthContext;
use Business\Authority\AuthLogic as AuthLogicBiz;

class Element extends Controller
{

    // 登陆信息
    private $_loginInfo;

    public function __construct()
    {
        parent::__construct();
        $this->_loginInfo = $this->getLoginInfo();
    }


    /**
     * 获取三级元素，  三级元素可能是按钮也可能是tab
     * <AUTHOR>
     * @date 2019-06-11
     */
    public function resource()
    {
        // 渠道ID
        $channel    = I('post.channel_id/d', 1);
        // menu tag
        $menuTag    = I('post.menu_tag/s');
        // 用户ID
        $memberId = $this->_loginInfo['memberID'];

        $menuType = I('post.menu_type/d',0);

        //如果是新版权限体验用户，可以直接使用新版权限中心鉴权
        $res = (new AuthLogicBiz())->resource($this->_loginInfo['sid'], $memberId, $menuTag, $this->_loginInfo['dtype'], $menuType);

        $this->apiReturn(200, $res, '');
    }


    /**
     * 获取四级元素， 也就是tab页对应的按钮
     * <AUTHOR>
     * @date 2019-06-11
     */
    public function elementResource()
    {
        // 渠道ID
        $channel    = I('post.channel_id/d', 1);
        // element tag
        $elementTag = I('post.element_tag/s');
        // 用户ID
        $memberId = $this->_loginInfo['memberID'];

        //如果是新版权限体验用户，可以直接使用新版权限中心鉴权
        $appId = intval($_SERVER['HTTP_FRONT_APP'] ?? AuthLogicBiz::FRONT_APP_DEFAULT_ID);
        $res = (new AuthLogicBiz())->elementResource($this->_loginInfo['sid'], $memberId, $elementTag, $this->_loginInfo['dtype'], $appId);

        $this->apiReturn(200, $res, '');
    }


    /**
     * OTA平台配置页面的tab页权限
     * <AUTHOR>
     * @date 2020-07-14
     *
     */
    public function getOtaConfig()
    {
        $fid = $this->_loginInfo['sid'];
        $channelType = I('channel', 'ota');
        $returnData  = [];

        if (!$fid) {
            $this->apiReturn(404, $returnData, '请重新登录');
        }

        $extConfigBiz = new \Business\AppCenter\ExtConfig();
        if ($channelType == $extConfigBiz::NEW_MEDIA_APP_SIGN) {
            $otaAuth = $extConfigBiz->checkOtaOpen($fid, $channelType);
            if (!$otaAuth) {
                $this->apiReturn(401, $returnData, '无权限');
            }
        }

        $extConfigRes = (new \Business\AppCenter\ExtConfig())->getIncreaseConfig($fid);
        if ($extConfigRes['code'] != 200) {
            $this->apiReturn(201, [], $extConfigRes['msg']);
        }

        $returnData['ota_platform'] = $extConfigRes['data']['ota_platform'];
        $returnData['all_category_platform'] = $extConfigRes['data']['all_category_platform'] ?? '';
        $this->apiReturn(200, $returnData);
    }


    /**
     * 新媒体平台配置页面的tab页权限
     * <AUTHOR>
     * @date 2021/8/13
     *
     */
    public function getNewMediaConfig()
    {
        $fid = $this->_loginInfo['sid'];
        $returnData = [];

        if (!$fid) {
            $this->apiReturn(404, $returnData, '请重新登录');
        }

        $extConfigBiz = new \Business\AppCenter\ExtConfig();
        $newMediaAuth = $extConfigBiz->checkOtaOpen($fid, $extConfigBiz::NEW_MEDIA_APP_SIGN);
        if (!$newMediaAuth) {
            $this->apiReturn(401, $returnData, '无权限');
        }
        $extConfigRes = (new \Business\AppCenter\ExtConfig())->getIncreaseConfig($fid);
        if ($extConfigRes['code'] != 200) {
            $this->apiReturn(201, [], $extConfigRes['msg']);
        }

        $returnData['new_media_platform'] =  $extConfigRes['data']['new_media_platform'] ?? '';
        $this->apiReturn(200, $returnData);
    }

}