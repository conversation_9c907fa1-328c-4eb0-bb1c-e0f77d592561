<?php

namespace Controller\Authority;

use Business\Authority\AuthLogic as AuthLogicBiz;
use Library\Controller;

class AuthRolePack extends Controller
{
    // 登陆信息
    private $loginInfo;

    public function __construct()
    {
        parent::__construct();
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 获取角色包全部权限
     *
     */
    public function getPackAuth()
    {
        $packId   = I('post.pack_id', 0, 'intval');//角色包id
        if (!$packId) {
            $this->apiReturn(203, [], '角色包id错误');
        }

        $res = (new AuthLogicBiz())->getPackAuth($packId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}