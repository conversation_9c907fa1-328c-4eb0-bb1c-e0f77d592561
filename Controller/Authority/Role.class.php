<?php
/**
 * 角色管理
 * <AUTHOR>
 * @date 2019-05-29
 */
namespace Controller\Authority;

use Library\Controller;
use Business\Authority\AuthContext;
use Business\Authority\AuthLogic as AuthLogicBiz;
use Model\Member\MemberRelationship;

class Role extends Controller
{
    // 登陆信息
    private $loginInfo;

    public function __construct()
    {
        parent::__construct();
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 获取角色列表
     * <AUTHOR>
     * @date 2019-05-29
     * @deprecated 废弃,后续删除
     */
    public function getList()
    {
        // 员工ID
        $business = new AuthContext();
        $memberId = $this->loginInfo['memberID'];
        $dtype    = $this->loginInfo['dtype'];
        $sid      = $this->loginInfo['sid'];

        $business->setMarkSourceFunc('Role/getList');
        if (!$business->hasAuth($memberId, $dtype)) {
            $this->apiReturn(400, '', '无权限');
        }

        $memberId = $business->getMemberId($sid, $memberId, $dtype);
        // 渠道
        $channeId = I('channel_id', '');
        // 当前页
        $page     = I('page', 1);
        // 获取数量
        $size     = I('size', 15);

        $business->setMarkSourceFunc('Role/getList');
        $list = $business->getRoleListFilterSystem($memberId, $channeId, 0, $page, $size);

        $this->apiReturn(200, $list, '');
    }

    public function getListForApprovalManage()
    {
        //旧接口需要赋予员工权限，基于权限中心需求考虑，提供新接口跳过员工权限校验，后续根据新版的权限中心考虑赋予权限
        // 员工ID
        $business = new AuthContext();
        $memberId = $this->loginInfo['memberID'];
        $dtype    = $this->loginInfo['dtype'];
        $sid      = $this->loginInfo['sid'];

        $memberId = $business->getMemberId($sid, $memberId, $dtype);
        // 渠道
        $channeId = I('channel_id', '');
        // 当前页
        $page     = I('page', 1);
        // 获取数量
        $size     = I('size', 15);

        $business->setMarkSourceFunc('Role/getListForApprovalManage');
        $list = $business->getRoleListFilterSystem($memberId, $channeId, 0, $page, $size);

        $this->apiReturn(200, $list, '');
    }

    public function getMemberListForRole()
    {
        // 员工ID
        $business = new AuthContext();
        $memberId = $this->loginInfo['memberID'];
        $dtype    = $this->loginInfo['dtype'];
        $sid      = $this->loginInfo['sid'];

        $business->setMarkSourceFunc('Role/getMemberListForRole');
        if (!$business->hasAuth($sid, $dtype)) {
            $this->apiReturn(400, '', '无权限');
        }
        // 渠道
        $roleId = I('role_id', '');
        // 当前

        $list = $business->getStaffListForRoleId($roleId);

        $this->apiReturn(200, $list, '');
    }


    /**
     * 新增角色, 同时会绑定权限
     * <AUTHOR>
     * @date 2019-05-29
     * @deprecated 废弃,后续删除
     */
    public function create()
    {
        pft_log('auth_discard_debug', json_encode(['authDataDoubleWrite_create', I('post.')]));
        // 员工ID
        $business = new AuthContext();
        $memberId = $this->loginInfo['memberID'];
        $dtype    = $this->loginInfo['dtype'];
        $sid      = $this->loginInfo['sid'];
        // 菜单权限以及元素权限tag, 因为前端没办法区分， 但是tag标识理论上不会出现重复
        $tag = I('tag', '');
        if (!$tag) {
            $this->apiReturn(400, '', '请选择权限');
        }

        $business->setMarkSourceFunc('Role/create');
        if (!$business->hasAuth($memberId, $dtype)) {
            $this->apiReturn(400, '', '无权限');
        }

        $tmpMemberId = $memberId;
        $memberId = $business->getMemberId($sid, $memberId, $dtype);

        // 渠道ID
        $channelId = I('channel_id', 1);
        // 角色名称
        $name = I('name', '');
        // 描述
        $desc = I('desc', '');
        // 生效时间
        $effectStart = I('effect_start', '');
        // 失效时间
        $effectEnd   = I('effect_end', '');
        // 是否系统默认分配，默认为0不是系统默认分配
        $isSystemInit = I('is_system_init', 0);

        $res = $business->createRole($memberId, $channelId, $name, $desc, $effectStart, $effectEnd,
            $isSystemInit, $tag);

        $params = array_merge(I('post.'), ['type' => 'create', 'role_id' => $res['data'] ?? 0]);
        $business->authDataDoubleWrite($res, $sid, $memberId, $params);

        pft_log('auth/add_role', "memberId:{$tmpMemberId},  roleName:{$name}, tag:{$tag}, res:{$res['code']}");

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }


    /**
     * 删除角色
     * <AUTHOR>
     * @date 2019-05-29
     * @deprecated 废弃,后续删除
     */
    public function del()
    {
        pft_log('auth_discard_debug', json_encode(['authDataDoubleWrite_del', I('post.')]));
        // 员工ID
        $business = new AuthContext();
        $memberId = $this->loginInfo['memberID'];
        $dtype    = $this->loginInfo['dtype'];
        $sid      = $this->loginInfo['sid'];

        $business->setMarkSourceFunc('Role/del');
        if (!$business->hasAuth($memberId, $dtype)) {
            $this->apiReturn(400, '', '无权限');
        }

        $id = I('id', '');

        if (empty($id)) {
            $this->apiReturn(400, '', '非法参数');
        }

        $tmpMemberId = $memberId;

        $memberId = $business->getMemberId($sid, $memberId, $dtype);
        $res = $business->delRole($memberId, $id);

        pft_log('auth/del_role', "memberId:{$tmpMemberId},  roleId:{$id},  res:{$res}");

        if (!$res) {
            $this->apiReturn(400, '', '删除失败');
        }

        $params = array_merge(I('post.'), ['type' => 'del']);
        $business->authDataDoubleWrite(['code'=>200], $sid, $memberId, $params);

        $this->apiReturn(200, '', '删除成功');
    }

    /**
     * 启用角色
     * <AUTHOR>
     * @date 2019-06-12
     * @deprecated 废弃,后续删除
     */
    public function enable()
    {
        // 员工ID
        $business = new AuthContext();
        $memberId = $this->loginInfo['memberID'];
        $dtype    = $this->loginInfo['dtype'];
        $sid      = $this->loginInfo['sid'];

        $business->setMarkSourceFunc('Role/enable');
        if (!$business->hasAuth($memberId, $dtype)) {
            $this->apiReturn(400, '', '无权限');
        }

        $id = I('id', '');

        if (empty($id)) {
            $this->apiReturn(400, '', '非法参数');
        }

        $tmpMemberId = $memberId;
        $memberId = $business->getMemberId($sid, $memberId, $dtype);
        $res = $business->enableRole($memberId, $id);

        pft_log('auth/enable_role', "memberId:{$tmpMemberId},  roleId:{$id},  res:{$res}");
        if (!$res) {
            $this->apiReturn(400, '', '启用失败');
        }

        $this->apiReturn(200, '', '启用成功');
    }

    /**
     * 编辑角色,同时也会更新权限
     * <AUTHOR>
     * @date 2019-05-29
     * @deprecated 废弃,后续删除
     */
    public function edit()
    {
        pft_log('auth_discard_debug', json_encode(['authDataDoubleWrite_edit', I('post.')]));
        // 员工ID
        $business = new AuthContext();
        $memberId = $this->loginInfo['memberID'];
        $dtype    = $this->loginInfo['dtype'];
        $sid      = $this->loginInfo['sid'];

        $business->setMarkSourceFunc('Role/edit');
        if (!$business->hasAuth($memberId, $dtype)) {
            $this->apiReturn(400, '', '无权限');
        }

        // 角色id
        $roleId  = I('id', '');
        // 角色名称
        $name    = I('name', '');
        // 描述
        $desc    = I('desc', '');
        //渠道
        $channel = I('channel', 1);
        // 失效时间
        $effectEnd   = I('effect_end', '');
        // 是否系统默认分配，默认为0不是系统默认分配
        $isSystemInit = I('is_system_init', 0);
        // 菜单权限以及元素权限tag, 因为前端没办法区分， 但是tag标识理论上不会出现重复
        $tag = I('tag', '');

        $tmpMemberId = $memberId;
        $memberId = $business->getMemberId($sid, $memberId, $dtype);
        $res = $business->editRole($roleId, $memberId, $name, $desc, $effectEnd, $channel, $isSystemInit,
            $tag);

        $params = array_merge(I('post.'), ['type' => 'edit']);
        $business->authDataDoubleWrite($res, $sid, $memberId, $params);

        pft_log('auth/edit_role', "memberId:{$tmpMemberId},  roleId:{$roleId}, tag:{$tag}, res:{$res['code']}");
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 判断同一账户 同一渠道下面, 角色名称是否重复
     * <AUTHOR>
     * @date 2019-06-05
     */
    public function nameCheck()
    {
        $channelId = I('channel_id', '');
        if (empty($channelId)) {
            $this->apiReturn(400, '', '渠道不能为空');
        }

        $name = I('name', '');
        if (empty($name)) {
            $this->apiReturn(400, '', '岗位名称不能为空');
        }

        $business = new AuthContext();
        $res = $business->nameCheck($channelId, $name, $this->loginInfo['memberID']);

        if (!$res) {
            $this->apiReturn(200, 1, '已存在相同岗位名称');
        }

        $this->apiReturn(200, 0, '不存在相同岗位名称');
    }

}