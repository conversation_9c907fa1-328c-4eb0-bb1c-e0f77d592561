<?php
/**
 * 员工相关接口
 */
namespace Controller\Authority;

use Business\Member\MemberRelation;
use Business\MemberLogin\MemberLoginHelper;
use Controller\Admin\Auth;
use Library\Controller;
use Business\Authority\AuthContext;
use Model\Member\MemberRelationship;
use Business\Authority\AuthLogic as AuthLogicBiz;

class Member extends Controller
{
    // 登陆信息
    private $loginInfo;

    public function __construct()
    {
        parent::__construct();
        $this->loginInfo = $this->getLoginInfo();
    }


    /**
     * 获取员工列表
     * <AUTHOR>
     * @date 2019-05-29
     */
    public function getList()
    {
        $memberId = $this->loginInfo['memberID'];
        $dtype    = $this->loginInfo['dtype'];
        $sid      = $this->loginInfo['sid'];

        $authLogicBiz = new AuthLogicBiz();
        $hasAuth = $authLogicBiz->hasAuth($sid, $memberId, AuthLogicBiz::STAFF_MANAGE_TAG, AuthLogicBiz::FRONT_APP_USER_CENTER_ID);
        if (!$hasAuth) {
            $this->apiReturn(400, '', '无权限');
        }

        // 账号或手机号
        $accountMobile = I('account_mobile');
        // 当前页
        $page = I('page', 1);
        // 获取页数
        $size = I('size', 15);
        //员工卡类型
        $cardType = I('card_type',-1); // -1
        $status   = I('status',-1); // -1全部 0正常1禁用

        $isStaff      = $authLogicBiz->checkIsStaff($sid, $memberId, $dtype);
        $res          = $authLogicBiz->getMemberList($accountMobile, $sid, $isStaff, $page, $size, $cardType, $status);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取员工列表
     * <AUTHOR>
     * @date 2019-05-29
     */
    public function getMemberListALL()
    {
        $memberId = $this->loginInfo['memberID'];
        $dtype    = $this->loginInfo['dtype'];
        $sid      = $this->loginInfo['sid'];

        $authLogicBiz = new AuthLogicBiz();
        // 账号或手机号
        $accountMobile = I('account_mobile');
        // 当前页
        $page = I('page', 1);
        // 获取页数
        $size = I('size', 15);
        //员工卡类型
        $cardType = I('card_type',-1); // -1
        $status   = I('status',-1); // -1全部 0正常1禁用
        $needSidInfo = I('need_sid_info',0);

        $isStaff = $authLogicBiz->checkIsStaff($sid, $memberId, $dtype);
        $res     = $authLogicBiz->getMemberList($accountMobile, $sid, $isStaff, $page, $size, $cardType, $status);
        $res['data']['list'] = $res['data']['list'] ?? [];
        if($needSidInfo && $page == 1){
            $member = new \Model\Member\Member();
            $memberData  = $member->getMemberInfo($sid);
            $linkData = [
                'account'=>$memberData['account'],
                'card_type'=>-1,
                'card_type_name'=>"",
                'id'=>$memberData['id'],
                'last_login_time'=>$memberData['lasttime'] ?? '',
                'mobile'=>$memberData['mobile'] ?? '',
                'name'=>$memberData['dname'] ?? '',
                'roles'=>'',
                'status'=>$memberData['status']
            ];
            array_unshift($res['data']['list'],$linkData);
        }
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取用户角色
     * <AUTHOR>
     * @date 2019-06-06
     * @deprecated 接口废弃,后续删除
     */
    public function getRoleList()
    {
        // 员工ID
        $memberId = I('member_id', '');
        $business = new AuthContext();
        $dtype    = $this->loginInfo['dtype'];

        $business->setMarkSourceFunc('Member/getRoleList');
        if (!$business->hasAuth($this->loginInfo['memberID'], $dtype)) {
            $this->apiReturn(400, '', '无权限');
        }

        // 是否有权限赋予角色
        if (!$this->_checkGiveAuth($memberId)) {
            $this->apiReturn(400, '', '无权限');
        }

        $res = $business->getMemberRoles($memberId, $this->loginInfo['sid']);
        $this->apiReturn(200, $res, '');
    }

    /**
     * 授予角色或回收
     * <AUTHOR>
     * @date 2019-05-29
     * @deprecated 废弃,后续删除
     */
    public function give()
    {
        pft_log('auth_discard_debug', json_encode(['authDataDoubleWrite_give', I('post.')]));
        // 员工ID
        $business = new AuthContext();
        $memberId = $this->loginInfo['memberID'];
        $dtype    = $this->loginInfo['dtype'];

        $business->setMarkSourceFunc('Member/give');
        if (!$business->hasAuth($memberId, $dtype)) {
            $this->apiReturn(400, '', '无权限');
        }

        // 授予用户
        $userId = I('user_id/d', '');
        if (empty($userId)) {
            $this->apiReturn(400, '', '非法参数');
        }

        // 是否有权限赋予角色
        if (!$this->_checkGiveAuth($userId)) {
            $this->apiReturn(400, '', '无权限');
        }

        // 赋予角色
        $roleIds = I('role_ids/s', '');

        $roleIdArr = explode(',', $roleIds);
        $roleIdArr = array_filter($roleIdArr);
        $roleIdArr = array_map(function($each) {
            return trim($each);
        }, $roleIdArr);

        $fid = $business->getMemberId($this->loginInfo['sid'], $memberId, $dtype);
        $res = $business->giveRole($fid, $this->loginInfo['sid'], $userId, $roleIdArr);

        $params = array_merge(I('post.'), ['type' => 'staff_give']);
        $business->authDataDoubleWrite($res, $this->loginInfo['sid'], $memberId, $params);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取用户菜单集
     * <AUTHOR>
     * @date 2019-07-22
     */
    public function getUserMenu()
    {
        // 渠道
        $channelId = I('get.channel_id');
        if (empty($channelId)) {
            $this->apiReturn(400, '', '参数缺失');
        }
        $authLogicBiz = new AuthLogicBiz();
        //获取菜单权限数据
        list($appId, $pageTag) = AuthLogicBiz::getHeaderAuthInfo();
        $res = $authLogicBiz->getUserMenu($this->loginInfo['sid'], $this->loginInfo['memberID'], $this->loginInfo['dtype'], $this->loginInfo['sdtype'], $appId, $pageTag, false);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 用户开放功能校验
     * <AUTHOR>
     * @date   2022/5/12
     *
     */
    public function checkAccess()
    {
        $sid = $this->loginInfo['sid'];
        $key = I('post.key', '', 'strval');

        if (empty($key)) {
            $this->apiReturn(400, [], '参数缺失');
        }

        $isSubMerchantLogin = false;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $isSubMerchantLogin = true;
        }

        $key  = str_replace('，', ',', $key);
        $keys = explode(',', $key);

        $business = new AuthContext();
        $res      = $business->checkAccessByKey($sid, $keys);

        //子商户销售报表权限处理
        if ($isSubMerchantLogin) {
            $filterField = ['report_pack_ticket_level_mould', 'report_hour_day_span', 'period_checked_report', 'not_checked_report'];
            foreach ($filterField as $tmp) {
                if (isset($res['data'][$tmp])) {
                    $res['data'][$tmp] = false;
                }
            }
        }

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 判断是否有赋予角色的权限
     * <AUTHOR>
     * @date 2019-06-04
     * @param int $memberId 要赋予的用户的ID
     * @return boolean
     */
    private function _checkGiveAuth($memberId)
    {
        // 当前用户ID
        $id = $this->loginInfo['memberID'];

        //$shipModel = new MemberRelationship();
        $memberRelationBiz = new MemberRelation();
        $parentInfo = $memberRelationBiz->getMemberStaffInfoBySonIdToJava($memberId,'id, parent_id',0);
        //$parentInfo = $shipModel->getDirectParentInfo($memberId, 'id, parent_id');

        // 是上级
        if ($parentInfo['parent_id'] == $id) {
            return true;
        }

        // 是否同一组织下
        $res = $memberRelationBiz->getMemberStaffInfoBySonIdToJava($id,'id, parent_id',0);
        //$res = $shipModel->getDirectParentInfo($id, 'id, parent_id');
        if ($res['parent_id'] == $parentInfo['parent_id']) {
            return true;
        }

        return false;
    }

}