<?php
/**
 * 权限菜单
 * <AUTHOR>
 * @date 2019-05-29
 */
namespace Controller\Authority;

use Library\Controller;
use Business\Authority\AuthContext;
use Business\Authority\AuthLogic as AuthLogicBiz;
use Library\Model;

class Menu extends Controller
{

    // 登陆信息
    private $loginInfo;

    public function __construct()
    {
        parent::__construct();
        $this->loginInfo = $this->getLoginInfo();
    }


    /**
     * 获取可用权限及菜单, 包含当前角色所拥有的权限
     * <AUTHOR>
     * @date 2019-05-29
     * @deprecated 接口废弃，后续删除
     */
    public function getListByChannel()
    {
        // 员工ID
        $business = new AuthContext();
        $memberId = $this->loginInfo['memberID'];
        $dtype    = $this->loginInfo['dtype'];

        $business->setMarkSourceFunc('Menu/getListByChannel');
        if (!$business->hasAuth($memberId, $dtype)) {
            $this->apiReturn(400, '', '无权限');
        }

        // 角色id, 当为新角色时 为0, 直接返回空
        $roleId    = I('role_id', '');
        $channelId = I('channel_id', 1);

        if ($roleId != 0 && empty($roleId)) {
            return $this->apiReturn(400, '', '参数缺失');
        }

        if (empty($channelId)) {
            return $this->apiReturn(400, '', '参数缺失');
        }

        $list  = $business->getListByChannelId($channelId, $roleId, $this->loginInfo['sid'], $this->loginInfo['dtype'], $this->loginInfo['sdtype']);

        $code = $list ? 200 : 500;
        return $this->apiReturn($code, $list, '');
    }


    /**
     *
     * 用户切换菜单 （废弃）
     *
     * <AUTHOR>
     *
     * @deprecated
     * @date   2019-06-4
     */
    public function userChangeMenu(){
        $this->apiReturn(500, [], '服务出错');
    }


    /**
     * 全部菜单列表 (废弃)
     *
     * <AUTHOR>
     * @date   2019-07-14
     *
     * @deprecated
     */
    public function getAllMenuList(){
        $this->apiReturn(500, [], '服务出错');
    }


    public function getMenuInfo(){
        $this->apiReturn(404, [], '');
    }


    public function test()
    {
        //$biz = new \Business\Statistics\RealTimeStatistics();
        //$res = $biz->createCustomizeData(62944797518654, 6, 1629448034, 80, 1);

        //$biz = new \Task\OrderReportExportV2();
        //$model = new \Model\Authority\Menu();
        //$task = $model->table('excel_task')->where(['id' => 5929])->find();
    }

}