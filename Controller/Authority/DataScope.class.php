<?php
/**
 * 数据权限
 * <AUTHOR>
 * @date 2019-06-09
 */

namespace Controller\Authority;
use Library\Controller;
use Business\Authority\AuthContext;
use Business\Authority\AuthLogic as AuthLogicBiz;

class DataScope extends Controller
{

    // 登陆信息
    private $_loginInfo;

    public function __construct()
    {
        parent::__construct();
        $this->_loginInfo = $this->getLoginInfo();
    }


    /**
     * 数据权限设置
     * <AUTHOR>
     * @date 2019-06-04
     * @deprecated 废弃,后续删除
     */
    public function set()
    {
        pft_log('auth_discard_debug', json_encode(['authDataDoubleWrite_set', I('post.')]));
        $role      = I('post.role_id/d');
        $scopeType = I('post.scope_type/d', '');
        $dataTag   = I('post.data_tag/s', '');
        $channel   = I('post.channel_id/d', 1);
        $limitId   = I('post.limit_id/s');

        if (!$role || !$scopeType) {
            $this->apiReturn(400, [], '参数错误');
        }

        // 员工ID
        $business = new AuthContext();
        $memberId = $this->_loginInfo['memberID'];
        $sid      = $this->_loginInfo['sid'];
        $dtype    = $this->_loginInfo['dtype'];

        $business->setMarkSourceFunc('DataScope/set');
        if (!$business->hasAuth($memberId, $dtype)) {
            $this->apiReturn(400, '', '无权限');
        }

        $result = $business->setDataScope($role, $dataTag, $scopeType, $limitId, $channel);

        if (!$result) {
            $this->apiReturn(500, [], '更新数据失败');
        }

        $params = array_merge(I('post.'), ['type' => 'data_resource']);
        $business->authDataDoubleWrite(['code'=>200], $this->_loginInfo['sid'], $memberId, $params);

        $this->apiReturn(200);
    }


    /**
     * 获取数据范围管理列表
     * <AUTHOR>
     * @date 2019-06-11
     * @deprecated 废弃,后续删除
     */
    public function getList()
    {
        // 员工ID
        $business = new AuthContext();
        $memberId = $this->_loginInfo['memberID'];
        $dtype    = $this->_loginInfo['dtype'];
        $sid      = $this->_loginInfo['sid'];

        $business->setMarkSourceFunc('DataScope/getList');
        if (!$business->hasAuth($memberId, $dtype)) {
            $this->apiReturn(400, '', '无权限');
        }

        // 渠道
        $channel = I('channel_id');
        // 当前页
        $page    = I('page', 1);
        // 获取数量
        $size    = I('size', 15);
        // 供应商Id
        $fid     = $business->getMemberId($sid, $memberId, $dtype);

        // 获取用户与数据资源列表
        $list = $business->getDataScopeList($channel, $fid, $page, $size);

        $this->apiReturn(200, $list, '');
    }


    /**
     * 查询用户数据权限
     *
     * <AUTHOR>
     * @date 2021-06-25
     *
     * 注：这个接口还不能废弃，一卡通相关的员工股数据权限还会走这个接口
     * https://my.12301.cc/new/cardTouristsList_report.html
     */
    public function getMemberSet()
    {
        $memberId = $this->_loginInfo['memberID'];
        $dtype    = $this->_loginInfo['dtype'];
        $dataTag  = I('get.data_scope_tag');

        if (empty($dataTag)) {
            $this->apiReturn(400, '', '参数错误');
        }

        $sid = $this->_loginInfo['sid'];
        $dataScope  = (new \Business\Authority\AuthLogic())->memberDataResource($sid, $memberId, $dtype, $dataTag);
        $returnData = [
            'data_scope_tag' => $dataTag,
            'data_scope'     => (int) $dataScope,
        ];

        $this->apiReturn(200, $returnData);
    }

}