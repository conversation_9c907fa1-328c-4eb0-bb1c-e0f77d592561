<?php
/**
 * 权限 - 系统应用相关
 * <AUTHOR>
 * @date   2023/3/29
 */

namespace Controller\Authority;

use Library\Controller;
use Business\Authority\AuthLogic as AuthLogicBiz;

class SysFront extends Controller
{
    // 登陆信息
    private $_loginInfo;
    //商户id
    private $_sid = 0;
    //当前用户id
    private $_memberId = 0;
    //当前用户类型
    private $_dtype;

    public function __construct()
    {
        parent::__construct();
        $loginInfo = $this->getLoginInfo('ajax');

        $this->_loginInfo = $loginInfo;
        $this->_sid       = $loginInfo['sid'];
        $this->_memberId  = $loginInfo['memberID'];
        $this->_dtype     = $loginInfo['dtype'];
    }

    /**
     * 获取商户全部系统应用列表
     * 传角色包id可以查询，对应的角色id
     * <AUTHOR>
     * @date   2023/3/29
     *
     */
    public function getUserFrontList()
    {
        $packId   = I('post.pack_id', 0, 'intval');//角色包id
        $showRole = I('post.show_role', 0, 'intval');//是否展示角色包id 1是 0否

        $res = (new AuthLogicBiz())->getUserFrontList($this->_sid, $packId, (bool)$showRole);

        //使用自定义排序函数对应用列表进行排序 针对系统应用id递增排序
        usort($res['data'], function($a, $b) {
            return $a['id'] - $b['id'];
        });

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取导航栏
     * <AUTHOR>
     * @date   2023/4/18
     *
     */
    public function getNavBar()
    {
        $isSupplier = $this->_dtype == 0;//标记下供应商
        $res        = (new AuthLogicBiz())->getNavigationByUserId($this->_sid, $this->_memberId, $isSupplier, $this->_loginInfo['sdtype']);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}