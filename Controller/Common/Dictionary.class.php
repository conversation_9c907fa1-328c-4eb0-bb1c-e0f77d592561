<?php
/**
 * 字典类
 * User: linchen
 * Date: 21/07/23
 * Time: 14:29
 */

namespace Controller\Common;



use Business\Common\DictionaryService;
use Library\Controller;

class Dictionary extends Controller
{
    public function __construct()
    {
        $isLogin = $this->isLogin('auto', false);
        if (!$isLogin) {
            $this->apiReturn(204, [], '请先登录');
        }
    }

    /**
     * 获取订单状态和游客状态
     * <AUTHOR>
     * @date   2021-07-26
     */
    public function getOrderStatusAndTouristStatus(){
        $dictionaryBiz = new DictionaryService();
        $resData = [
            'order'  => $dictionaryBiz->getOrderStatusDictionary(),
            'tourist' => $dictionaryBiz->getOrderTouristDictionary(),
            'tourist_print' => $dictionaryBiz->getOrderTouristPrintDictionary(),
        ];
        $this->apiReturn(200, $resData, 'success');
    }
}