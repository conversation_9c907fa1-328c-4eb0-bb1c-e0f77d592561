<?php

namespace Controller\Member;

use Library\Controller;
use Library\Tools\Helpers;

class ScenicCheckUser extends Controller
{
    /**
     * 获取多景点白名单
     * <AUTHOR>
     * @date   2020-04-20
     */
    public function getMemberWhithList()
    {
        $page = I('post.page', '');
        $size = I('post.size', '');

        $memberBiz = new \Business\Member\ScenicCheckUser();
        $result    = $memberBiz->getMemberWhith($page, $size);

        $this->apiReturn(200, $result ?: []);
    }

    /**
     * 添加多景点白名单
     * <AUTHOR>
     * @date   2020-04-20
     */
    public function addMemberWhith()
    {
        //判断是否为管理员
        if (!$this->isSuper()) {
            $this->apiReturn(204, [], '当前账号不是管理员');
        }
        $account    = I('post.account', '', 'strval');
        $MemberBiz  = new \Business\Member\ScenicCheckUser();
        $res        = $MemberBiz->addMemberWhith($account);
        $memberInfo = $this->getLoginInfo();
        $log        = '添加多景点票登录帐号供应商:' . $account . '为白名单,操作人：' . $memberInfo['memberID'];
        pft_log('scenic_check_user/white_list', $log);

        if ($res['code'] == 200) {
            $this->apiReturn(200, [], $res['msg']);
        } else {
            $this->apiReturn(500, [], $res['msg']);
        }
    }

    /**
     * 修改用户多景点白名单状态
     * <AUTHOR>
     * @date   2020-04-20
     */
    public function editMemberWhith()
    {
        //判断是否为管理员
        if (!$this->isSuper()) {
            $this->apiReturn(204, [], '当前账号不是管理员');
        }
        $account = I('post.account', '', 'strval');
        $status  = I('post.status', '');

        if (is_null($account) || !is_numeric($status)) {
            $this->apiReturn(201, [], '参数错误');
        }
        $memberWhiteMdl = new \Model\Member\ScenicCheckUser();

        $res        = $memberWhiteMdl->editMemberWhith($account, $status);
        $memberInfo = $this->getLoginInfo();
        $status     = $status == 1 ? '关闭' : '开启';
        $log        = '修改多景点票白名单:' . $account . '状态为' . $status . '为白名单,操作人：' . $memberInfo['memberID'];
        pft_log('debug/ticket_debug', $log);

        if ($res) {
            $this->apiReturn(200, [], '修改成功');
        } else {
            $this->apiReturn(500, [], '修改失敗');
        }
    }

    /**
     * 多景点配置白名单判断
     * <AUTHOR>
     * @date 2021/4/29
     *
     *
     * @return array
     */
    public function judgeChkWhite()
    {
        $memberInfo = $this->getLoginInfo();

        $parAccount = $memberInfo['saccount'];//父账号
        $account    = $memberInfo['account'];//账号

        $res        = 0;
        $memberMdl  = new \Model\Member\ScenicCheckUser();

        if ($memberMdl->isMemberWhith($account)) {
            $res = 1;
        }

        if ($memberMdl->isMemberWhith($parAccount)) {
            $res = 1;
        }

        return $this->apiReturn(200, ['is_chk_white' => $res], '');
    }
}