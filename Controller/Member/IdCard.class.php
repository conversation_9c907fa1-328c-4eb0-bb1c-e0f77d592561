<?php

namespace Controller\Member;

use Library\Controller;
use function Sodium\add;

/**
 * 身份证地址码管理
 * <AUTHOR>
 */
class IdCard extends Controller
{
    private $loginInfo;

    public function __construct()
    {
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 新建区域身份证识别码
     *
     * <AUTHOR>
     * @date   2020-07-28
     *
     * @return array
     */
    public function createIdCard()
    {
        $areaCode  = I('post.area_code', 0, 'intval');
        $idCard    = I('post.id_card', 0, 'intval');
        $addrType  = I('post.addr_type', 0, 'intval');
        $stateType = I('post.state_type', 0, 'intval');

        if (!$this->isSuper()) {
            $this->apiReturn(203, [], "没有权限");
        };

        if (!$areaCode || !$idCard || !$addrType || !$stateType) {
            $this->apiReturn(204, [], "参数错误");
        }

        $operatorId = $this->loginInfo['memberID'];
        $idCardApi  = new \Business\JavaApi\Member\IdCard();
        $result     = $idCardApi->create($areaCode, $idCard, $addrType, $stateType, '', $operatorId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常 ERROR:' . $result['msg']);
        }
    }

    /**
     * 修改区域身份证识别码
     *
     * <AUTHOR>
     * @date   2020-07-28
     *
     * @return array
     */
    public function updateIdCard()
    {
        $editArr = I('post.edit','', 'strval');

        if (!$this->isSuper()) {
            $this->apiReturn(203, [], "没有权限");
        };

        if (!$editArr) {
            $this->apiReturn(204, [], "参数错误");
        }
      
        $editArr = json_decode($editArr, true);
        if ($editArr && count($editArr) > 0) {
            $operatorId = $this->loginInfo['memberID'];
            $idCardApi  = new \Business\JavaApi\Member\IdCard();
            $error      = [];
            foreach ($editArr as $editData) {
                $result = $idCardApi->update($editData['id'], null, $editData['idCard'], null, null, null,
                    $operatorId);
                if (isset($result['code'])) {
                    if ($result['code'] != 200) {
                        $error[] = $editData['idCard'] . '编辑失败';
                    }
                } else {
                    $this->apiReturn(500, [], '接口异常');
                }
            }
            $code = $error ? 206 : 200;
            $this->apiReturn($code, $error, "成功");

        } else {
            $this->apiReturn(204, [], "参数错误");
        }
    }

    /**
     * 删除区域身份证识别码
     *
     * <AUTHOR>
     * @date   2020-07-28
     *
     * @return array
     */
    public function deleteIdCard()
    {
        $id = I('post.id', 0, 'intval');

        if (!$this->isSuper()) {
            $this->apiReturn(203, [], "没有权限");
        };

        if (!$id) {
            $this->apiReturn(204, [], "参数错误");
        }

        $operatorId = $this->loginInfo['memberID'];
        $idCardApi  = new \Business\JavaApi\Member\IdCard();
        $result     = $idCardApi->delete($id, $operatorId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取区域身份证码
     *查全部境内 + 省级/直辖市/港澳 : areaState = 86;parentCode = 0;查全部境外 : areaState = -86;默认不分页
     * <AUTHOR>
     * @date   2020-07-28
     *
     * @return array
     */
    public function getList()
    {
        $parentCode = I('post.parent_code', null, 'intval');
        $areaCode   = I('post.area_card', null, 'intval');
        $areaName   = I('post.area_name', null, 'string,trim');
        $areaState  = I('post.area_state', null, 'intval');
        $page       = I('post.page', null, 'intval');
        $size       = I('post.size', null, 'intval');

        $idCardApi = new \Business\JavaApi\Member\IdCard();
        $result    = $idCardApi->getList($parentCode, $areaCode, $areaName, $areaState, $page, $size);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据身份证编码主键查询
     *
     * <AUTHOR>
     * @date   2020-07-28
     *
     * @return array
     */
    public function getFind()
    {
        $id = I('post.id', 0, 'intval');

        $idCardApi = new \Business\JavaApi\Member\IdCard();
        $result    = $idCardApi->find($id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据地区编码获取身份证开头列表
     *
     * <AUTHOR>
     * @date   2020-07-28
     *
     * @return array
     */
    public function queryIdCardsByAreaCode()
    {
        $areaCode = I('post.area_code', null, 'intval');
        if (!$areaCode) {
            $this->apiReturn(204, [], "参数错误");
        }
        $idCardApi = new \Business\JavaApi\Area\Area();
        $result    = $idCardApi->queryIdCardsByAreaCode($areaCode);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

}