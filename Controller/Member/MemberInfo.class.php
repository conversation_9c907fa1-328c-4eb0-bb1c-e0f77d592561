<?php
/**
 * 用户信息处理
 * <AUTHOR>
 * @date   2016-11-04
 */

namespace Controller\Member;

use Business\Finance\AccountMoney;
use Business\JavaApi\InvoiceApi;
use Business\JavaApi\Member\MemberProfessionInfoService;
use Business\JavaApi\Member\MemberQuery;
use Business\JavaApi\Member\MemberRelationshipRemark;
use Business\Member\Customer;
use Business\Member\MemberBaseInfo;
use Library\Controller;
use Library\Tools;
use Library\Tools\Helpers;
use Model\Member\Member;
use Business\Member\Member as MemberBiz;
use Library\Tools\Vcode;
use Model\Member\MemberRelationship;
use Model\Member\PftStaffModel as Staff;
use Model\Product\Area;
use Model\Wechat\WxMember;
use pft\Member\MemberAccount;

class MemberInfo extends Controller
{
    private $sid = 0;
    private $loginInfo;


    private $_updateMobileCheckKey = "member:update_mobile:check_";

    public function __construct()
    {
        $this->loginInfo = $this->getLoginInfo();

        $this->sid = $this->loginInfo['memberID'];

    }

    /**
     * 获得Member模型实例
     * <AUTHOR>
     * @date   2016-11-10
     * @return Member
     */
    public function getMemberModel()
    {
        static $model;

        if (!$model) {
            $model = new \Model\Member\Member();
        }

        return $model;
    }

    /**
     * 发送手机验证码
     * <AUTHOR>
     * @date   2016-11-09
     *
     * @param  int    mobile 接收短信的手机号
     * @param  string tpl    短信模板
     *
     * @return ajax
     */
    public function sendVcode($mobile = null, $tpl = null)
    {
        $mobile = I('mobile', 0, 'intval');
        $version = I('version', 'v1', 'strval');
        if (!$mobile) {
            // 获得当前绑定的手机号码
            $info = $this->getMemberModel()->getMemberInfo($this->sid, 'id');
            if (!$info) {
                $this->apiReturn(406, [], '您尚未绑定手机！');
            } else {
                $mobile = $info['mobile'];
                $tpl    = 'check_old_mobile';
            }
        } else {
            // 验证新手机号是否可以绑定
            if ($version == 'v2') {
                $this->checkPlatformMobile($mobile, 'bool');
            } else {
                $this->checkMobile($mobile, 'bool');
            }
            $tpl = 'check_new_mobile';
        }

        // 是否黑名单号码
        $black_list    = load_config('black_list');
        $balack_mobile = $black_list['mobile'];
        if (in_array($mobile, $balack_mobile)) {
            $this->apiReturn(406, [], '该手机号已经被加入黑名单。');
        }

        $data = [
            '{1}'  => '手机号修改',
            'code' => '{2}',
        ];

        $res = Vcode::sendVcode($mobile, $tpl, $tpl, $data);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '发送验证码成功！');
        } else {
            $this->apiReturn(406, [], $res['msg']);
        }
    }

    public function sendVcodeByComName()
    {
        $mobile = I('mobile', 0, 'intval');
        if (!$mobile) {
            $info = $this->getMemberModel()->getMemberInfo($this->sid, 'id');
            if (!$info) {
                $this->apiReturn(204, [], '您尚未绑定手机！');
            } else {
                $this->apiReturn(204, [], '请填写手机号！');
            }
        }
        // 是否黑名单号码
        $black_list    = load_config('black_list');
        $balack_mobile = $black_list['mobile'];
        if (in_array($mobile, $balack_mobile)) {
            $this->apiReturn(406, [], '该手机号已经被加入黑名单。');
        }

        $data = [
            '{1}'  => '企业名称修改',
            'code' => '{2}',
        ];
        $tpl  = 'update_com_name';
        $res  = Vcode::sendVcode($mobile, $tpl, $tpl, $data);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '发送验证码成功！');
        } else {
            $this->apiReturn(406, [], $res['msg']);
        }
    }

    /**
     * 校验手机验证码
     * <AUTHOR>
     * @date   2016-11-10
     *
     * @param  int  $mobile  手机号
     * @param  string  $tpl  服务标识
     * @param  string  $code  验证码
     * @param  string  $successType  验证成功返回类型 'ajax'或者'bool'
     *
     * @return mixed    默认ajax|successType='bool'则验证成功返回true
     */
    public function checkVcode($successType = 'ajax')
    {
        $tpl    = I('post.tpl');
        $code   = I('post.code');
        $mobile = I('post.mobile', 0, 'intval');

        if (!$tpl || !$code || !$mobile) {
            $this->apiReturn(406, [], '参数错误');
        }


        $res = Vcode::verifyVcode($mobile, $code, $tpl);
        if ($res['code'] == 200) {
            if ($successType == 'bool') {
                return true;
            } else {
                $checkStr = uniqid();
                $cacheKey = $this->_updateMobileCheckKey . $checkStr;
                \Library\Cache\cache::getInstance('redis')->set($cacheKey, 1, 3600);
                $this->apiReturn(200, ['check_str' => $checkStr], '验证码正确');
            }

        } else {
            if ($successType == 'bool') {
                return false;
            } else {
                $this->apiReturn(406, [], '验证码输入错误,请重新输入');
            }
        }
    }

    /***
     * 修改手机号码的同时将密码重置为手机号后六位
     *
     * <AUTHOR>
     * @date   2017-12-20
     *
     * @param  string  $tel  手机号
     *
     * @return   string    $newPass    两次md5后的新密码
     */
    public function _resetPswForTel($tel)
    {
        $newPwd  = substr($tel, -6, 6);
        $newPass = md5(md5($newPwd));

        return $newPass;
    }

    /**
     * 校验手机验证码并绑定手机
     * <AUTHOR>
     * @date   2016-11-04
     *
     * @param  string mobile 要验证的手机号
     *
     * @return ajax返回
     */
    public function changeMobile()
    {
        $mobile   = I('post.mobile', 0, 'intval');
        $checkStr = I('post.check_str', '', 'strval');
        $version = I('post.version', 'v1', 'strval');

        $memberBus = new MemberBiz();
        $memberInfo = $memberBus->getInfo($this->loginInfo['memberID']);
        if (empty($memberInfo)) {
            $this->apiReturn(404, [], '用户信息错误');
        }

        $hasMobile = empty($memberInfo['mobile']) ? false : true;
        if ($hasMobile && !$checkStr) {
            $this->apiReturn(404, [], '非法请求,校验参数为空');
        }
        if ($memberInfo['dtype'] == 2) {
            $this->apiReturn(404, [], '资源账号不支持绑定手机号码');
        }

        if ($checkStr) {
            $cacheKey   = $this->_updateMobileCheckKey . $checkStr;
            $cacheValue = \Library\Cache\cache::getInstance('redis')->get($cacheKey);

            if (empty($cacheValue)) {
                $this->apiReturn(404, [], '非法请求,参数校验失败');
            }
        }

        if ($version == 'v2') {
            $this->checkPlatformMobile($mobile, 'bool');
        } else {
            $this->checkMobile($mobile, 'bool');
        }
        if (!$this->checkVcode('bool')) {
            $this->apiReturn(406, [], '验证码输入错误,请重新输入');
        }

        $data = ['mobile' => $mobile];
        //如果是会员卡入口修改手机号则将密码重置为手机号后六位
        $changeMemberCardMobile = I('post.membercardChange', 0);
        if ($changeMemberCardMobile == 1) {
            $newPass = $this->_resetPswForTel($data['mobile']);
            //用户二期 - 信息获取修改
            $memberBus->updateMemberPassword($this->sid, $newPass);
        }
        if ($this->getMemberModel()->setMemberInfoById($this->sid, $data)) {
            $this->apiReturn(200, [], '手机绑定成功');
        } else {
            $this->apiReturn(201, [], '手机绑定失败');
        }

    }
    

    /**
     * 校验企业名称并修改
     * <AUTHOR>
     * @date   2016-11-04
     *
     * @param  string mobile 要验证的手机号
     *
     * @return array
     */
    public function changeComName()
    {
        //TODO 之前改了一次，后面产品又说不能再让他修改了，为了防止产品后面又说能修改了，这边先直接返回不能修改，代码先留着吧
        $this->apiReturn(204, [], '暂不支持修改企业名，请联系客服');
        $comName = I('post.com_name', '');
        $mobile  = I('post.mobile', 0);
        if (!$comName) {
            $this->apiReturn(201, [], '企业名称不能为空');
        }
        if ($this->checkVcode('bool')) {
            $data      = [
                'com_name' => safetxt($comName),
            ];
            $memberBiz = new MemberBiz();
            $member    = $memberBiz->getInfo($this->sid);
            if ($member['mobile'] != $mobile) {
                $this->apiReturn(201, [], '和预留电话不一致');
            }
            $extRes = $memberBiz->updateMemberExtInfo($this->sid, $data);
            if (!$extRes) {
                $this->apiReturn(201, [], '更新企业名称失败');
            }
            // 更新电子发票信息
            $invoiceBiz = new InvoiceApi();
            $oldConfig  = $invoiceBiz->getSpecialConfig($this->sid);
            if (isset($oldConfig['id'])) {
                $oldConfig['compayName'] = $comName;
                $invoiceBiz->updateSpecialInvoice($oldConfig);
            }
            $this->apiReturn(200, [], '修改成功');
        } else {
            $this->apiReturn(201, [], '验证码验证失败');
        }
    }

    /**
     * 验证手机号是否可以绑定
     * <AUTHOR>
     * @date   2016-11-04
     *
     * @param  string mobile      要验证的手机号
     * @param  string successType ['bool'则号码可用返回true]
     *
     * @return mixed [successType=='bool'则号码可用时返回true,否则ajax返回]
     */
    public function checkMobile($mobile, $successType = 'ajax')
    {
        $mobile = $mobile ?: I('post.mobile');
        if (!$mobile) {
            $this->apiReturn(406, [], '参数错误');
        }

        //手机号码验证
        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(406, [], '手机号码格式不正确');
        }

        //todo::这种验证手机号占用已经不准确了
        $res = $this->getMemberModel()->getMemberInfo($mobile, 'mobile');
        if ($res) {
            $this->apiReturn(406, [], '手机号已被占用');
        } else {
            if ($successType == 'bool') {
                return true;
            } else {
                $this->apiReturn(200, [], '手机号可用');
            }
        }
    }

    /**
     * 验证平台用户手机号是否可以绑定
     *
     * @param  string $mobile      要验证的手机号
     * @param  string $successType [successType=='bool'则号码可用时返回true,否则ajax返回]
     */
    public function checkPlatformMobile($mobile, $successType = 'ajax')
    {
        $mobile = $mobile ?: I('post.mobile');
        if (!$mobile) {
            $this->apiReturn(406, [], '参数错误');
        }

        //手机号码验证
        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(406, [], '手机号码格式不正确');
        }

        $memberBiz  = new MemberBiz();
        $queryRes = $memberBiz->queryPlatformMemberByMobile($mobile);
        if ($queryRes['registered']) {
            $this->apiReturn(406, [], '手机号已被占用');
        } else {
            if ($successType == 'bool') {
                return true;
            } else {
                $this->apiReturn(200, [], '手机号可用');
            }
        }
    }

    /**
     * 更新支付宝账号
     * <AUTHOR>
     * @date   2016-11-04
     *
     * @return ajax返回
     */
    public function changeAlipay()
    {
        $alipay = I('post.alipay');
        if (!$alipay) {
            $this->apiReturn(406, [], '支付宝账号不能为空');
        }
        $data = ['alipay' => $alipay];
        if ($this->getMemberModel()->setMemberInfoById($this->sid, $data)) {
            $this->apiReturn(200, [], '支付宝帐号更新成功');
        } else {
            $this->apiReturn(201, [], '支付宝帐号更新失败');
        }
    }

    /**
     * 获取添加分销商的类型
     * <AUTHOR>
     * @date   2016-11-22
     *
     * @return
     */
    public function getAddSalerType()
    {
        $memberId  = $this->loginInfo['memberID'];
        $memberBiz = new \Business\Member\Member();
        $res       = $memberBiz->getMemberExtInfoGetFieldToJava($memberId, 'add_saler_type');

        if (!$res) {
            $this->apiReturn(500, [], '信息有误！');
        } else {
            $this->apiReturn(200, $res, '');
        }
    }

    /**
     * 取得登陆用户可以查看的签单人和客服列表
     *
     */
    public function getSalsesKefus()
    {

        // 返回值
        $res      = [];
        $isGetAll = 0;
        // 取得登陆用户信息
        $controller = new Controller();
        $isSuper    = $controller->isSuper();

        if (!$isSuper) {
            $this->apiReturn(500, [], '无权查看！');
        }

        $loginInfoArr  = $controller->getLoginInfo();
        $memberModel   = new Member();
        $pftStaffModel = new Staff();

        // 判断当前登陆人员是否是admin
        if ($loginInfoArr['memberID'] == 1) {
            $isGetAll = 1;
        }
        $memberBiz = new \Business\Member\Member();
        if ($isGetAll != 1) {
            // 判断当前人员所在部门(0:客服部，1:市场部，2:财务部，3:技术部，4:其他部门)
            $position = $memberBiz->getMemberExtInfoGetFieldToJava((int)$loginInfoArr['memberID'], 'position');
            if (is_numeric($position) && in_array($position, [0, 1])) {
                // 市场客服人员要 进行负责区域等 关联判断
                $staffInfo = $pftStaffModel->getInfoByMemberId((int)$loginInfoArr['memberID']);
                if (empty($staffInfo)) {
                    $this->apiReturn(500, [], '无权查看！');
                }
                // 判断当前员工的类型
                switch ($staffInfo['st_type']) {
                    case 0:  // 签单人员
                        $saleMan = [$staffInfo['id'] => $staffInfo['st_name']];
                        $kefuID  = $pftStaffModel->getKf();
                        break;
                    case 1:  // 客服
                        $isGetAll = 1;
                        break;
                    case 2:  // 区域经理
                        if ($staffInfo['st_group'] == 5) {
                            $isGetAll = 1;
                        } else {
                            if ($position == 1) { // 市场的区域经理进入该页面
                                $saleMan = $pftStaffModel->getQdryByArea($staffInfo['st_group']);
                                $kefuID  = $pftStaffModel->getKf();  // 客服取得全部
                            } else {  // 客服的区域经理进入该页面
                                $isGetAll = 1;
                            }
                        }
                        break;
                    case 3:  // 部门经理
                        $isGetAll = 1;
                        break;
                    case 4:   // 其他人员
                        $isGetAll = 1;
                        break;
                }
            } elseif (is_numeric($position) && in_array($position, [2, 3])) {
                $isGetAll = 1;
            } elseif ($position == 4) {
                $this->apiReturn(500, [], '无权查看！');
            }
        }

        // admin  特殊判断
        if ($isGetAll == 1) {
            $saleMan = $pftStaffModel->getQdry();
            $kefuID  = $pftStaffModel->getKf();
        }

        $saleMan = $saleMan ? $saleMan : '';
        $kefuID  = $kefuID ? $kefuID : '';

        $res = [
            'sales' => $saleMan,
            'kefus' => $kefuID,
        ];

        $this->apiReturn(200, $res, '');
    }

    /**
     * 根据手机号或账号查找员工信息
     * @author: guanpeng
     * @date: 2019/1/16
     */
    public function getStaffInfo()
    {
        $parentId = $this->getLoginInfo('ajax')['sid'];
        $account  = I('get.account', '');
        $mobile   = I('get.mobile', '');
        if (!Helpers::isMobile($mobile)) {
            $mobile = false;
        }
        if (!$mobile && !$account) {
            $this->apiReturn(203, [], '请输入员工手机号或员工账号进行查找');
        }
        $data = $this->getMemberModel()->getStaffInfo($parentId, $account, $mobile);
        if (empty($data)) {
            $this->apiReturn(204, [], '查找不到员工数据，请检查输入是否正确');
        }
        $this->apiReturn(200, $data, 'success');

    }

    /**
     * 获取供应商列表
     * @Author: zhujb
     * 2019/1/17
     */
    public function getMemberList()
    {
        $keyword     = I('keyword', '', 'strval');
        $page        = I('page', 1, 'intval');
        $size        = I('size', 10, 'intval');
        $onlyData    = I('only_data', 0, 'intval');
        $memberModel = new MemberQuery();

        $res = $memberModel->queryMemberPageByAccountOrDname($keyword, $page, $size);

        if (!isset($res['code']) || $res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        } else {
            if ($onlyData) {
                $this->apiReturn(200, $res['data']['list'], 'success');
            } else {
                $this->apiReturn(200, $res['data'], 'success');
            }
        }
    }

    /***
     * 查看用户信息
     * @author: Cai Yiqiang
     * @date: 2019/1/25
     */
    public function getUserInfo()
    {
        $dtype = I('post.dtype', 0, 'intval');
        $did   = I('post.id', 0, 'intval');

        if (!$did || !in_array($dtype, [0, 1, 4, 7])) {
            $this->apiReturn(203, [], '参数错误');
        }

        $isSuper = $this->sid == 1 ? true : false;
        //验证查看权限
        if (!$isSuper && ($this->sid != $did)) {
            $shipModel = new MemberRelationship();
            //$parent = $this->sid;
            $parent = $this->loginInfo['sid'];
            $son    = $did;

            $sonIdType = [];
            if ($dtype == 1) {
                //是否具有供应关系
                $shipType = 0;
                $parent   = $did;
                $son      = $this->sid;
            } elseif ($dtype == 7) {
                //是否是集团成员
                $shipType  = 1;
                $sonIdType = [0, 3];
            } else {
                //是否是分销商
                $shipType = 0;
            }

            $memberRelationBiz = new \Business\Member\MemberRelation();
            $ship              = $memberRelationBiz->getMemberRelationListToJava([$parent], [$son], [0], $sonIdType, [$shipType]);

            $hasAuth = $ship ? true : false;
            //没有上下级关系 就去查看审核表中记录
            if (!$ship && $dtype == 1) {
                $checkRes = $memberRelationBiz->findLastApply($parent, $son);
                if ($checkRes['code'] == 200) {//之前true 是没有数据
                    $hasAuth = true;
                }
            }

            if (!$hasAuth) {
                $this->apiReturn(204, [], '没有权限查看');
            }
        }

        $return = (new \Business\Member\Member())->formatMemberInfoById($did);
        $return['dtype'] = $dtype;

        $this->apiReturn(200, $return, 'success');
    }

    /**
     * 获取用户信息
     * Create by zhangyangzhen
     * Date: 2019/11/11
     * Time: 17:21
     */
    public function getMemberInfo()
    {
        $memberBiz   = new MemberBiz();
        $customerBiz = new Customer();

        $memberInfo   = $memberBiz->getInfo($this->sid, true);
        $customerInfo = $customerBiz->getCustomerInfoByMemberId($this->sid);

        $info = [
            'dname'              => $memberInfo['dname'],
            'cname'              => $memberInfo['cname'],
            'dtype'              => $memberInfo['dtype'],
            'com_name'           => $memberInfo['com_name'],
            'short_company_name' => $memberInfo['short_company_name'],
            'website'            => $memberInfo['website'],
            'address'            => $customerInfo['address'],
            'email'              => $customerInfo['email'],
            'qq'                 => $customerInfo['qq'],
            'mobile'             => $memberInfo['mobile'],
            'headphoto'          => $customerInfo['headphoto'],
            'business'           => $memberInfo['business'],
            'province'           => $memberInfo['province'],
            'city'               => $memberInfo['city'],
            'country'            => $memberInfo['country'],
            'com_type'           => $memberBiz::__CORP_KIND_ARR__[$memberInfo['corp_kind']],
        ];

        $this->apiReturn(self::CODE_SUCCESS, $info, 'success');
    }

    /**
     * 更新会员信息
     * Create by zhangyangzhen
     * Date: 2019/11/12
     * Time: 16:48
     */
    public function updateMemberInfo()
    {
        $dname   = I('post.dname', '', 'strval,trim');
        $cname   = I('post.cname', '', 'strval,trim');
        $comName = I('post.com_name', '', 'strval,trim');
        $qq      = I('post.qq', '', 'strval,trim');

        if (empty($dname) || !$dname) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '账号名称不能为空');
        }

        $memberBaseInfo = [
            'dname' => $dname,
            'cname' => $cname,
        ];

        $memberBiz = new MemberBiz();
        $baseRes   = $memberBiz->updateMemberBaseInfo($this->sid, $memberBaseInfo);

        if (!$baseRes) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '用户信息保存失败');
        } else {
            $loginInfo = $this->getLoginInfo();
            (new \Library\Tools\BusinessCache())->setBusinessCache(['dname' => $dname], $loginInfo['memberID']);
        }

        //往用户扩展表更新数据
        $memberExt = $memberBiz->getInfo($this->sid, true);
        if ($memberExt['com_name'] != $comName) {
            $extInfo = [
                'com_name' => $comName,
            ];

            $extRes = $memberBiz->updateMemberExtInfo($this->sid, $extInfo);

            if (!$extRes) {
                $this->apiReturn(self::CODE_INVALID_REQUEST, [], '企业名称保存失败');//目前这边只有企业名称一个字段，暂时这样提示
            }
        }

        //往客户表哦pft_customer更新数据
        $customerBiz  = new Customer();
        $customerInfo = $customerBiz->getCustomerInfoByMemberId($this->sid);

        if ($customerInfo['qq'] != $qq) {
            $customerInfo = [
                'qq' => $qq,
            ];

            $customerRes = $customerBiz->updateCustomerInfoByMemberId($this->sid, $customerInfo);

            if (!$customerRes) {
                $this->apiReturn(self::CODE_INVALID_REQUEST, [], 'qq号保存失败');//目前这边只有qq一个字段，暂时这样提示
            }
        }

        $this->apiReturn(self::CODE_SUCCESS, [], '保存成功');
    }

    /**
     * 修改用户头像
     * Create by zhangyangzhen
     * Date: 2019/11/21
     * Time: 11:07
     */
    public function updateMemberHeadphoto()
    {
        $head = I('post.headphoto', '', 'strval,trim');

        if (empty($head) || !$head) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '图片路径不能为空');
        }

        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($this->sid, 'id', 'id,customer_id');

        $cusModel = new Customer();
        $result   = $cusModel->updateCustomerHeadphoto($memberInfo['customer_id'], $head);

        if ($result) {
            $this->apiReturn(self::CODE_SUCCESS, [], '保存成功');
        } else {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '保存失败');
        }
    }

    /**
     * 获取用户基础信息
     * <AUTHOR>
     * @Date: 2020/04/01
     *
     */
    public function getUserBaseInfo()
    {
        $memberBaseInfoBiz = new MemberBaseInfo();
        $res               = $memberBaseInfoBiz->getUserBaseInfoService($this->loginInfo['sid'],
            $this->loginInfo['memberID']);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 获取用户微信绑定信息
     * <AUTHOR>
     * @Date: 2020/04/01
     *
     */
    public function getUserWxBindInfo()
    {
        $memberBaseInfoBiz = new MemberBaseInfo();
        $res               = $memberBaseInfoBiz->getUserWxBindInfoService($this->loginInfo['sid']);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 更新用户基础信息
     * <AUTHOR>
     * @Date: 2020/04/01
     *
     */
    public function updateMemberBaseInfo()
    {
        $params            = I('post.');
        $data              = $this->_userBaseInfoHandle($params);
        $memberBaseInfoBiz = new MemberBaseInfo();
        $res               = $memberBaseInfoBiz->updateMemberBaseInfoService($this->loginInfo['memberID'], $data);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 更新商家详情页面备注
     * Create by xwh
     * Date: 2020/05/06
     * Time: 11:07
     */
    public function updateRemark()
    {
        $member_id = I('post.member_id', 0);
        $remark    = I('post.remark', ' ', 'strval,trim');

        if (!$member_id) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        //长度不超100
        if (mb_strlen($remark) > 100) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '备注字数超出限制');
        }

        $memberbus = new MemberRelationshipRemark();
        $res       = $memberbus->addDistributorRemarkName($member_id, $this->loginInfo['sid'], $this->sid, $remark);
        if ($res['code'] != 200) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], $res['msg']);
        } else {
            $this->apiReturn(self::CODE_SUCCESS, [], $res['msg']);
        }
    }

    /**
     * 处理用户基本信息参数
     * <AUTHOR>
     * @Date: 2020/04/01
     *
     */
    private function _userBaseInfoHandle($params)
    {
        if (!isset($params['update_type']) || !in_array($params['update_type'], [0, 1])) {
            $this->apiReturn(204, [], '更新类型错误');
        }
        $data          = [
            'member'     => [],
            'member_ext' => [],
            'customer'   => [],
        ];
        $updateType    = $params['update_type'];
        $baseInfoField = [
            'dname'          => ['field' => 'dname', 'table' => 'member', 'must' => 1, 'msg' => ''],
            //对应数据库字段 对应表 是否必填  必填的备注
            'contact_name'   => ['field' => 'cname', 'table' => 'member', 'must' => 0],
            'sex'            => ['field' => 'sex', 'table' => 'customer', 'must' => 0],
            'contact_mobile' => ['field' => 'ctel', 'table' => 'member', 'must' => 0],
            'qq'             => ['field' => 'qq', 'table' => 'customer', 'must' => 0],
            'email'          => ['field' => 'email', 'table' => 'customer', 'must' => 0],
            'fax'            => ['field' => 'fax', 'table' => 'member_ext', 'must' => 0],
            'hotline'        => ['field' => 'hotline', 'table' => 'member_ext', 'must' => 0],
        ];
        $companyField  = [
            'short_name'    => ['field' => 'short_company_name', 'table' => 'member_ext', 'must' => 0],
            'com_type'      => ['field' => 'corp_kind', 'table' => 'member_ext', 'must' => 0],
            'com_level'     => ['field' => 'types', 'table' => 'member_ext', 'must' => 0],
            'business_type' => ['field' => 'format_type', 'table' => 'member_ext', 'must' => 0],
            'province'      => ['field' => 'province', 'table' => 'member_ext', 'must' => 0],
            'city'          => ['field' => 'city', 'table' => 'member_ext', 'must' => 0],
            'address'       => ['field' => 'address', 'table' => 'customer', 'must' => 0],
            'website'       => ['field' => 'website', 'table' => 'member_ext', 'must' => 0],
            'business'      => ['field' => 'business', 'table' => 'member_ext', 'must' => 0],
        ];
        if ($updateType == 0) {  //基本信息保存
            foreach ($params as $key => $value) {
                if (isset($baseInfoField[$key])) {
                    $field = $baseInfoField[$key];
                    if ($field['must'] == 1 && !$value) {
                        $this->apiReturn(204, [], $field['msg']);
                    }
                    if ($field['field'] == 'email' && $value) {
                        $emailRes = preg_match('/^[a-z0-9A-Z]+[-|a-z0-9A-Z._]+@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\.)+[a-z]{2,}$/',
                            $value);
                        if (!$emailRes) {
                            $this->apiReturn(204, [], '邮箱格式不对');
                        }
                    }
                    if (in_array($field['field'], ["contact_mobile"])) {
                        if (!Tools::ismobile($value) && $value) {
                            $this->apiReturn(204, [], '电话格式不对');
                        }
                    }
                    $data[$field['table']][$field['field']] = $value;
                }
            }
        } else if ($updateType == 1) {  //企业信息
            $customerBiz = new Customer();
            foreach ($params as $key => $value) {
                if (isset($companyField[$key])) {
                    $field = $companyField[$key];
                    if ($field['field'] == 'corp_kind') {
                        $data[$field['table']][$field['field']] = $customerBiz->getCorpKind($value);
                    } else {
                        $data[$field['table']][$field['field']] = $value;
                    }

                }
            }
        }

        return $data;
    }

    /**
     * 更新用户自己的头像
     * <AUTHOR>
     * @Date: 2020/04/01
     *
     */
    public function updateUserOwnHeadPhoto()
    {
        $head = I('post.head_photo', '', 'strval,trim');
        if (empty($head) || !$head) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '图片路径不能为空');
        }
        $data['member']    = [
            'headphoto' => $head,
        ];
        $memberBaseInfoBiz = new MemberBaseInfo();
        $res               = $memberBaseInfoBiz->updateMemberBaseInfoService($this->loginInfo['memberID'], $data);

        if (isset($res['code'])) {
            $this->apiReturn($res['code'], [], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 查询分销商备注名称
     * Create by xwh
     * Date: 2020/05/06
     * Time: 11:07
     */
    public function queryDistributorRemarkName()
    {
        $member_id = I('post.member_id', 0);

        if (!$member_id) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $memberbus = new MemberRelationshipRemark();
        $res       = $memberbus->queryDistributorRemarkName($member_id, $this->loginInfo['sid']);
        if ($res['code'] != 200) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], $res['msg']);
        } else {
            $this->apiReturn(self::CODE_SUCCESS, $res['data'], $res['msg']);
        }
    }

    /**
     * 设置微信备注
     * <AUTHOR>
     * @date 2022/2/22
     *
     * @return array
     */
    public function setAlias()
    {
        $id    = I('post.id', '', 'intval');
        $alias = I('post.alias', '', 'strval');

        if (empty($alias) || empty($id)) {
            $this->apiReturn(203, [], '参数错误');
        }
        if (mb_strlen($alias) > 20) {
            $this->apiReturn(203, [], '备注限制20字以内');
        }
        $wxMemberModel = new WxMember();
        $result = $wxMemberModel->setAlias($id, $alias);
        if (empty($result)) {
            $this->apiReturn(400, [], '设置失败');
        }

        $this->apiReturn(200, [], '设置成功');

    }
}