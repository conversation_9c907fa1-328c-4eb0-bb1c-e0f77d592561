<?php
/**
 * Created by PhpStorm.
 * User: linchen
 * Date: 2019/7/17
 * Time: 10:54
 */
namespace Controller\Member;

use function GuzzleHttp\Psr7\uri_for;
use Library\Controller;
use Business\Member\MemberSystem as MemberSystemBiz;
use Library\Tools;

class MemberSystem extends Controller{
    private $loginInfo;
    private $_memberType = [
        1 => '游客',
        2 => '分销商',
    //    3 => '营销员',//(暂不使用)
    //    4 => '卡用户',//(暂不使用)
    //    5 => '年卡',//(暂不使用)
    ];
    public function __construct()
    {
        $this->loginInfo = $this->getLoginInfo();
    }
    /**
     * 获取会员等级列表
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int   member_type  会员类型
     */
    public function getMemberGradeRule(){
        $sid = $this->loginInfo['sid'];
        $memberType      = I('get.member_type',0,'intval');
        $memberSystemBiz = new MemberSystemBiz();
        $result          = $memberSystemBiz->getMemberSystemRule($sid,$memberType);
        if (isset($result['code'])){
            $this->apiReturn($result['code'],$result['data'],$result['msg']);
        }else{
            $this->apiReturn(500,[],'系统错误');
        }
    }
    /**
     * 添加和修改会员等级
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int   member_type  会员类型
     * @param  int   grade_id  修改id
     * @param  string name  会员等级名称
     * @param  int member_grade  会员等级排名
     * @param  int obtain_type  会员获得类型:0=积分晋升,1=直接购买和积分晋升',
     * @param  int obtain_value  会员获得类型如果是直接购买，则为购买金额（分）
     */
    public function addOrEditMemberGradeManage(){
        $memberType = I('post.member_type',0,'intval');
        $gradeName  = I('post.name','');
        $memberGrade= I('post.member_grade_num',0,'intval');
        $obtainType = I('post.obtain_type',0,'intval');
        $obtainValue= I('post.obtain_value',0,'intval');
        $obtainMoney= I('post.obtain_money',0);
        $isEdit     = I('post.edit',0,'intval');
        $gradeId    = I('post.grade_id',0,'intval');
        $sid        = $this->loginInfo['sid'];
        $opId       = $this->loginInfo['memberID'];
        if ($isEdit){
            if (!$gradeId){
                $this->apiReturn(204,[],'缺失id参数');
            }
        }
        if (!in_array($memberType,array_keys($this->_memberType))){
            $this->apiReturn(204,[],'用户类型错误');
        }
        if (!$gradeName){
            $this->apiReturn(204,[],'等级名称有误');
        }
        if ($memberGrade < 0){
            $this->apiReturn(204,[],'会员等级有误');
        }
        if (!in_array($obtainType,[0,1,2])){
            $this->apiReturn(204,[],'会员类型有误');
        }
        if ($obtainValue < 0){
            $this->apiReturn(204,[],'积分不能小于0');
        }
        if ($obtainMoney < 0){
            $this->apiReturn(204,[],'金额不能小于0');
        }
        $memberSystemBiz = new MemberSystemBiz();
        $obtainMoney = float2int($obtainMoney);
        if ($isEdit){
            $result = $memberSystemBiz->editMemberGradeManage($gradeId,$sid,$opId,$memberType,$gradeName,$memberGrade,$obtainType,$obtainValue,$obtainMoney);
        }else {
            $result = $memberSystemBiz->addMemberGradeManage($sid, $opId, $memberType, $gradeName, $memberGrade, $obtainType, $obtainValue, $obtainMoney);
        }
        if (isset($result['code'])){
            $this->apiReturn($result['code'],$result['data'],$result['msg']);
        }else{
            $this->apiReturn(500,[],'系统错误');
        }
    }
    /**
     * 添加和修改会员积分配置规则
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int   member_type  会员类型
     * @param  int limit_num  消费笔数
     * @param  int limit_money  消费金额
     * @param  int point  每满消费获得积分数-个',
     * @param  int validity  积分是否有有效期
     * @param  string validity_start  积分有效期开始时间
     * @param  string validity_end  积分有效期结束时间
     */
    public function addOrEditMemberService(){
        $memberType      = I('post.member_type',0,'intval');
        $orderLimitNum   = I('post.limit_num',0,'intval');
        $orderLimitMoney = I('post.limit_money',0);
        $point           = I('post.point',0,'intval');
        $validity        = I('post.validity_type',0,'intval');
        $validStart      = I('post.validity_start','');
        $validEnd        = I('post.validity_end','');
        $integralRuleType= I('post.integral_rule_type',0,'intval');
        $isEdit          = I('post.edit',0,'intval');
        $serviceId       = I('post.id',0,'intval');
        $sid        = $this->loginInfo['sid'];
        $opId       = $this->loginInfo['memberID'];
        if ($isEdit){
            if (!$serviceId){
                $this->apiReturn(204,[],'缺失id参数');
            }
        }
        if ($orderLimitMoney < 0 && $point < 0 && $orderLimitNum < 0){
            $this->apiReturn(500,[],'参数有误');
        }
        if (!in_array($memberType,array_keys($this->_memberType))){
            $this->apiReturn(204,[],'用户类型错误');
        }
        if ($validity == 1){
            if (!strtotime($validStart) || !strtotime($validEnd)){
                $this->apiReturn(204,[],'时间格式错误');
            }
        }
        if (!in_array($integralRuleType,[0,1,2])){
            $this->apiReturn(204,[],'累计积分规则错误');
        }
        $orderLimitMoney = float2int($orderLimitMoney);
        $memberSystemBiz = new MemberSystemBiz();
        if ($isEdit){
            $result = $memberSystemBiz->editMemberServiceConfig($serviceId,$sid,$opId,$memberType,$integralRuleType,$orderLimitNum,
                $orderLimitMoney,$point,$validity,$validStart,$validEnd);
        }else{
            $result = $memberSystemBiz->addMemberServiceConfig($sid,$opId,$memberType,$integralRuleType,$orderLimitNum,
                $orderLimitMoney,$point,$validity,$validStart,$validEnd);
        }
        if (isset($result['code'])){
            $this->apiReturn($result['code'],$result['data'],$result['msg']);
        }else{
            $this->apiReturn(500,[],'系统错误');
        }
    }
    /**
     * 获取会员积分配置信息
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int   member_type  会员类型
     */
    public function getMemberServiceConfig(){
        $memberType      = I('get.member_type',0,'intval');
        $sid        = $this->loginInfo['sid'];
        if (!in_array($memberType,array_keys($this->_memberType))){
            $this->apiReturn(204,[],'用户类型错误');
        }
        $memberSystemBiz = new MemberSystemBiz();
        $result = $memberSystemBiz->getMemberServiceConfig($memberType,$sid);
        if (isset($result['code'])){
            $this->apiReturn($result['code'],$result['data'],$result['msg']);
        }else{
            $this->apiReturn(500,[],'系统错误');
        }
    }
    /**
     * 添加会员产品折扣信息
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int   member_type  会员类型
     * @param  int   tid  票id
     * @param  int   grade_id  会员等级ID
     */
    public function addMemberShipProductService(){
        $memberType      = I('post.member_type',0,'intval');
        $aid             = I('post.aid',0,'intval');
        $gradeId         = I('post.grade_id',0,'intval');
        $ticketIds       = I('post.ticket_ids','');
   //    $jsonData        = I('post.data','','strip_tags');
        $pid             = I('post.pid',0,'intval');
        $discount        = I('post.discount',0);
        $sharedDiscount  = I('post.shared_discount',0);
        $preferentialNum = I('post.preferential_num',0);
        $entourageDiscount = I('post.entourage_discount',0);
        $birthdayDiscount  = I('post.birthday_discount',0);
        $lid               = I('post.lid',0,'intval');
        $opId              = $this->loginInfo['memberID'];
        $sid        = $this->loginInfo['sid'];
        $discountData = [
            'discount'          => $discount,
            'entourage_discount'   => $entourageDiscount,
            'birthday_discount' => $birthdayDiscount
        ];

        if (!in_array($memberType,array_keys($this->_memberType))){
            $this->apiReturn(204,[],'用户类型错误');
        }
        if (!$pid  || ($gradeId <= 0)){
            $this->apiReturn(204,[],'参数错误');
        }
        if ($preferentialNum < 0){
            $this->apiReturn(204,[],'优惠次数要大于0');
        }
        $ticketArr = explode(',',$ticketIds);
        if ($ticketArr && is_array($ticketArr)){
            foreach ($ticketArr as $k => $v){
                $data[] = [
                    'memberId'   => $sid,
                    'superiorId' => $aid,
                    'membershipType' => $memberType,
                    'gradeId'        => $gradeId,
                    'ticketId'       => isset($v) ? $v : 0,
                    'pid'            => $pid,
                    'landId'         => $lid,
                    'operateId'      => $opId
                ];
            }
        }else{
            $this->apiReturn(204,[],'票参数错误');
        }
        foreach ($discountData as $value){
            if (is_numeric($value)){
                if (intval($value) >= 10 || intval($value) <= 0){
                    $this->apiReturn(204,[],'折扣有误');
                }
            }
        }
        if (!in_array($sharedDiscount,[0,1])){
            $this->apiReturn(204,[],'折扣参数错误');
        }
        $memberSystemBiz = new MemberSystemBiz();
        $result = $memberSystemBiz->addMemberShipProductService($data,$discountData,$sharedDiscount,$preferentialNum);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }
    /**
     * 会员等级晋升管理
     *
     * <AUTHOR>
     * @date 2019/7/24
     */
    public function addMemberPromotion()
    {
        $memberType     = I('get.member_type',0,'intval');     //会员类型:1游客,2分销商,3营销员(暂不使用),4卡用户(暂不使用),5年卡(暂不使用)",
        $grad           = I('get.grad', '', 'strval');          //等级信息json字符串
        $effectType     = I('get.effectType', 0, 'intval');    //会员生效设置:0立即生效,1次日生效",
        $activateType   = I('get.activateType', 0, 'intval');  //会员激活设置:0免激活,1需要激活",
        $gradeValidType = I('get.gradeValidType', 0, 'intval');//会员等级有效期0:不限,1按天,2\指定有效期",
        $day            = I('get.day', 0, 'intval');            //天数",
        $startDate      = I('get.startDate', '', 'strval');      //开始时间
        $endDate        = I('get.endDate', '', 'strval');        //结束时间
        $mobile         = I('get.mobile', 1, 'intval');         //手机 0启用,1未启用
        $birthday       = I('get.birthday', 1, 'intval');       //生日 0启用,1未启用
//        $grad = json_decode($grad, true);
//        if (json_last_error() > 0) {
//            $this->apiReturn(203, [], "等级信息错误");
//        }
        if (!in_array($memberType,array_keys($this->_memberType))) {
            $this->apiReturn(204, [], '用户类型错误');
        }
        $memberSystemBiz = new MemberSystemBiz();
        $sid       = $this->loginInfo['sid'];
        $operateId = $this->loginInfo['memberID'];
        $result = $memberSystemBiz->addMemberPromotion($sid, $memberType, $effectType, $activateType, $gradeValidType, $operateId, $day, $startDate, $endDate, $mobile, $birthday);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }

    /**
     *  获取会员等级晋升管理
     *
     * <AUTHOR>
     * @date 2019/7/26
     */
    public function getMemberPromotionById()
    {
        $memberType     = I('get.member_type',0,'intval');     //会员类型:1游客,2分销商,3营销员(暂不使用),4卡用户(暂不使用),5年卡(暂不使用)",
        if (!in_array($memberType,array_keys($this->_memberType))) {
            $this->apiReturn(204, [], '用户类型错误');
        }
        $sid       = $this->loginInfo['sid'];
        $memberSystemBiz = new MemberSystemBiz();
        $result = $memberSystemBiz->getMemberPromotionById($sid, $memberType);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }

    /**
     * 修改会员等级晋升管理
     *
     * <AUTHOR>
     * @date 2019/7/26
     */
    public function editMemberPromotion()
    {
        $id             = I('get.id', 0, 'intval');             //类型ID
        $memberType     = I('get.member_type',0,'intval');     //会员类型:1游客,2分销商,3营销员(暂不使用),4卡用户(暂不使用),5年卡(暂不使用)",
        $grad           = I('get.grad', '', 'strval');          //等级信息json字符串
        $effectType     = I('get.effectType', 0, 'intval');    //会员生效设置:0立即生效,1次日生效",
        $activateType   = I('get.activateType', 0, 'intval');  //会员激活设置:0免激活,1需要激活",
        $gradeValidType = I('get.gradeValidType', 0, 'intval');//会员等级有效期0:不限,1按天,2\指定有效期",
        $day            = I('get.day', 0, 'intval');            //天数",
        $startDate      = I('get.startDate', '', 'strval');      //开始时间
        $endDate        = I('get.endDate', '', 'strval');        //结束时间
        $mobile         = I('get.mobile', 1, 'intval');         //手机 0启用,1未启用
        $birthday       = I('get.birthday', 1, 'intval');       //生日 0启用,1未启用
//        $grad = json_decode($grad, true);
//        if (json_last_error() > 0) {
//            $this->apiReturn(203, [], "等级信息错误");
//        }
        if (!in_array($memberType,array_keys($this->_memberType))) {
            $this->apiReturn(204, [], '用户类型错误');
        }
        $memberSystemBiz = new MemberSystemBiz();
        $sid       = $this->loginInfo['sid'];
        $operateId = $this->loginInfo['memberID'];
        $result = $memberSystemBiz->editMemberPromotion($sid, $id, $memberType, $effectType, $activateType, $gradeValidType, $operateId, $day, $startDate, $endDate, $mobile, $birthday);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }
    
    /**
     * 获取折扣信息
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int   member_type  会员类型
     * @param  int   tid  票id
     * @param  int   grade_id  会员等级ID
     */
    public function getMemberShipProductList(){
        $aid            = I('get.aid',0,'intval');
        $memberType     = I('get.member_type',0,'intval');     //会员类型:1游客,2分销商,3营销员(暂不使用),4卡用户(暂不使用),5年卡(暂不使用)",
        $gradeId        = I('get.grade_id',0,'intval');
        $ticketId       = I('get.tid',0,'intval');
        $lid            = I('get.lid',0,'intval');
        $page           = I('get.page',1,'intval');
        $size           = I('get.size',10,'intval');
        $sid            = $this->loginInfo['sid'];
        $memberSystemBiz = new MemberSystemBiz();
        $result          = $memberSystemBiz->getMemberShipProductList($sid,$aid,$memberType,$gradeId,$ticketId,$lid,$page,$size);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }
    /**
     * 批量修改折扣信息
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int   member_type  会员类型
     * @param  int   tid  票id
     * @param  int   grade_id  会员等级ID
     */
    public function editBatchMemberProductInfo(){
        $discountIds       = I('post.discount_ids','');
        $discount          = I('post.discount','');
        $sharedDiscount    = I('post.shared_discount',0);
        $preferentialNum   = I('post.preferential_num',0);
        $entourageDiscount = I('post.entourage_discount',0);
        $birthdayDiscount  = I('post.birthday_discount',0);
        $memberSystemBiz = new MemberSystemBiz();
        $sid             = $this->loginInfo['sid'];
        $opId            = $this->loginInfo['memberID'];
        if ($discountIds){
            $arrDiscountIds = explode(',',$discountIds);
            foreach ($arrDiscountIds as $key => $value){
                if (!$value){
                    $this->apiReturn(204,[],'参数有误');
                }
            }
        }else{
            $this->apiReturn(204,[],'无修改');
        }
        $result = $memberSystemBiz->editBatchMemberProductInfo($sid,$opId,$arrDiscountIds,$discount,$sharedDiscount,$preferentialNum,$entourageDiscount,$birthdayDiscount);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }
    /**
     * 开启商户会员体系
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int   member_type  会员类型
     * @param  int   tid  票id
     * @param  int   grade_id  会员等级ID
     */
    public function openMemberSystemShip(){
        $sid  = $this->loginInfo['sid'];
        $opId = $this->loginInfo['memberID'];
        $memberSystemBiz = new MemberSystemBiz();
        $result = $memberSystemBiz->openMemberSystemShip($sid,$opId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }
    /**
     * 获取会员列表信息
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int   member_type  会员类型
     * @param  int   tid  票id
     * @param  int   grade_id  会员等级ID
     */
    public function getMemberSystemUserList(){
        $memberType     = I('get.member_type',0,'intval');     //会员类型:1游客,2分销商,3营销员(暂不使用),4卡用户(暂不使用),5年卡(暂不使用)",
        $gradeId        = I('get.grade_id',0,'intval');  //会员等级
        $search         = I('get.mobile','');
        $fid            = I('get.fid',0);
        $page           = I('get.page',1);
        $size           = I('get.size',10);
        if (!Tools::ismobile($search)){
            $name   = $search;
        }else{
            $mobile = $search;
        }
        $sid            = $this->loginInfo['sid'];
        $memberSystemBiz = new MemberSystemBiz();
        $result          = $memberSystemBiz->getMemberSystemUserList($sid,$fid,$memberType,$gradeId,$name,$mobile,$page,$size);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }
    /**
     * 用户自己开通会员体系
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int   member_type  会员类型
     * @param  int   tid  票id
     * @param  int   grade_id  会员等级ID
     */
    public function openMemberSystemBySupplier(){
        $sid            = $this->loginInfo['sid'];
        $memberId       = $this->loginInfo['memberID'];
        if ($sid != $memberId){
            $this->apiReturn(204, [], '无权限开通');
        }
        $memberSystemBiz = new MemberSystemBiz();
        $result          = $memberSystemBiz->openMemberSystemBySupplier($sid,$memberId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }
    /**
     * 获取会员等级下拉列表
     * 详细参数见文档
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int      member_type  会员类型
     */
    public function getMemberSystemGradeByType(){
        $memberType     = I('get.member_type',0,'intval');     //会员类型:1游客,2分销商,3营销员(暂不使用),4卡用户(暂不使用),5年卡(暂不使用)",
        if (!in_array($memberType,array_keys($this->_memberType))) {
            $this->apiReturn(204, [], '用户类型错误');
        }
        $sid = $this->loginInfo['sid'];
        $memberSystemBiz = new MemberSystemBiz();
        $result          = $memberSystemBiz->getMemberSystemGradeByType($sid,$memberType);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }
    /**
     * 批量删除会员体系产品规则
     * 详细参数见文档
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int      member_type  会员类型
     */
    public function memberSystemProductBatchDelete(){
        $ruleIds = I('post.rule_ids','');
        if ($ruleIds){
            $arrRuleIds = explode(',',$ruleIds);
            foreach ($arrRuleIds as $key => $value){
                if (!$value){
                    $this->apiReturn(204,[],'参数有误');
                }
            }
        }else{
            $this->apiReturn(204, [], '无修改');
        }
        $sid = $this->loginInfo['sid'];
        $opId = $this->loginInfo['memberID'];
        $memberSystemBiz = new MemberSystemBiz();
        $result = $memberSystemBiz->memberSystemProductBatchDelete($arrRuleIds,$sid,$opId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }
    /**
     * 修改会员等级
     * <AUTHOR>
     * @date   2019-07-17
     * @param  int      memberType 会员类型
     * @param  int      opId 操作员
     * @param  int      fid 用户
     * @param  int      gradeId 会员等级id
     */
    public function memberSystemUserGradeEdit(){
        $fid            = I('post.fid',0,'intval');
        $memberType     = I('post.member_type',0,'intval');     //会员类型:1游客,2分销商,3营销员(暂不使用),4卡用户(暂不使用),5年卡(暂不使用)",
        $gradeId        = I('post.grade_id',0,'intval');
        if (!in_array($memberType,array_keys($this->_memberType))) {
            $this->apiReturn(204, [], '用户类型错误');
        }
        if (!$fid || !$gradeId){
            $this->apiReturn(204, [], '等级或者用户参数缺失');
        }
        $sid             = $this->loginInfo['sid'];
        $opId            = $this->loginInfo['memberID'];
        $memberSystemBiz = new MemberSystemBiz();
        $result          = $memberSystemBiz->memberSystemUserGradeEdit($sid,$fid,$memberType,$gradeId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }
}