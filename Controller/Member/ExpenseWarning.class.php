<?php
/**
 * Created by Sublime.
 * User: chenyanbin
 * Date: 2017/1/9
 * Time: 10:34
 *
 * 账户余额提醒/注册套餐跳转
 */

namespace Controller\Member;

use Business\JavaApi\Member\MemberSignBill;
use Library\Controller;
use Library\Tools\BusinessCache;
use Model\AppCenter\ModuleList;
use Model\Member\PftStaffModel as Staff;
use Business\JavaApi\Account\AccountBook;
use Library\Constants\Account\BookSubject;
use Business\MemberLogin\MemberLoginHelper;
use Business\AppCenter\ModuleCommon as ModuleCommonBiz;
use Business\NewJavaApi\Member\SubMerchant as SubMerchantApi;

class ExpenseWarning extends Controller
{
    private $_model;
    private $sid;
    private $loginInfo;

    public function __construct()
    {
        $this->isLogin('ajax');

        //新规则  当前没有正在使用的套餐都跳转到选择套餐页面
        $this->loginInfo = $this->getLoginInfo('ajax', false, false);
        $this->sid       = $this->loginInfo['sid'];
        if (in_array($this->loginInfo['sdtype'], [0, 1])) {
            $listModel = new ModuleList();
            $useInfo   = $listModel->getTargetInfoByUidTime($this->sid);
            if (empty($useInfo)) {
                $this->limit('appCenter');
            }
        }

        //供应商的情况 需要判断当前用户合作模式是哪种  假日是订单模式且预存订单  需要判断预存账本中凭证费等于0时 跳转预存充值页
        if ($this->loginInfo['sdtype'] == 0) {
            $exclusiveBiz = new \Business\Finance\ExclusiveAccount();
            $accountInfo  = $exclusiveBiz->checkMemberInfo($this->sid, true);
            if ($accountInfo && isset($accountInfo[$this->sid]) && $accountInfo[$this->sid]['contract_model'] == 3
                && $accountInfo[$this->sid]['cooperate_type'] == 2 && $accountInfo[$this->sid]['voucher_remain'] == 0) {
                $this->limit('eleVoucher');
            }

            //当授权登录的时候 有操作的时候需要检测下授权信息
            if (isset($this->loginInfo['authorized_id']) && $this->loginInfo['authorized_id']) {
                $authorizedBiz = new \Business\Authorized\Authorized();
                $checkRes      = $authorizedBiz->checkAuthorizedInfoById($this->loginInfo['authorized_id']);
                if ($checkRes['code'] != 200) {
                    $this->limit('authorized_login', $checkRes['msg']);
                }
            }
        }

        //子商户是否过期
        $subMerchantExpired = $this->_checkSubMerchantExpired();
        if ($subMerchantExpired) {
            $this->limit('sub_merchant');
        }

        $this->_model = new \Model\Member\ExpenseWarning();
    }

    /**
     * 批量更新发送短信状态
     *
     */
    public function changeSmsInfo()
    {
        $this->_model->changeSmsInfo();
    }

    /**
     * 充值后 检查用户金额 改变session 值
     * <AUTHOR>
     *
     * @return
     */
    public function checkMoney()
    {
        if (extension_loaded('qconf')) {
            $whiteList = \qconf::getConf("/php/platform/arrears_fee_sid_ab_test");
            $whiteList = json_decode($whiteList, true);
        } else {
            $whiteList = [];
        }
        //ab_test 测试白名单
        if (in_array($this->sid, $whiteList) || in_array('*', $whiteList)) {
            $info       = $this->_model->checkLoginV2($this->sid);
        } else {
            $info       = $this->_model->checkLogin($this->sid);
        }
        $check      = $info['mInfo'];
        $checkMoney = $check['amoney'];
        if ($checkMoney >= 0) {
            (new BusinessCache())->delBusinessCache('ExpenseCode', $this->sid);
        }
    }

    /**
     * 登陆时判断改用户的账户余额(页面访问限制)
     *
     * ExpenseCode字段这边不再使用了, 这边判断注释了, 欠费强制跳转充值页不再由这边控制了
     * @deprecated
     */
    public function checkLogin()
    {
        // if (extension_loaded('qconf')) {
        //     $whiteList = \qconf::getConf("/php/platform/arrears_fee_sid_ab_test");
        //     $whiteList = json_decode($whiteList, true);
        // } else {
        //     $whiteList = [];
        // }
        // $business    = new BusinessCache();
        // $businessArr = $business->getBusinessCache($this->sid);
        //
        // if (isset($businessArr['ExpenseCode']) && $businessArr['ExpenseCode']) {
        //     $code = $businessArr['ExpenseCode'];
        // } else {
        //     //ab_test 测试白名单
        //     if (in_array($this->sid, $whiteList) || in_array('*', $whiteList)) {
        //         $code = $this->_ExpenseCodeJudgeV2();
        //     } else {
        //         $code = $this->_ExpenseCodeJudge();
        //     }
        //     $business->setBusinessCache(['ExpenseCode' => $code], $this->sid);
        // }
        //
        // if (!in_array($this->sid, $whiteList) && !in_array('*', $whiteList)) {
        //     //欠费白名单内的商户不限制访问
        //     if (in_array($code, [204, 205, 206])) {
        //         $whiteList = load_config('arrears_fee_white_list_sid', 'account');
        //         if (in_array($this->sid ?? 0, $whiteList)) {
        //             $code = 200;
        //         }
        //     }
        // }
        //
        // if ($code == 204 || $code == 205 || $code == 206) {
        //     $this->limit();         //限制访问
        // }
    }

    /**
     * 获取账户余额的状态码
     *
     * @return $code 200 正常、201 今日提醒 小于200元 大于0元、202 今日提醒 小于0元 大于-200、203 在欠费时长内继续使用、204 超出自定义设置时间 205超出7天时长 206账户余额小于-200   204,205,206访问页面限定
     */
    private function _ExpenseCodeJudge()
    {
        if (!$this->sid) {
            return 200;
        }
        $info  = $this->_model->checkLogin($this->sid);
        $memberSignBill = new MemberSignBill();
        $effectiveTimeResult = $memberSignBill->getMemberSignBill($this->sid);
        $effectivetime = $effectiveTimeResult['data']['arrearsTime'] ?? 0;
        $fid   = $this->sid;
        $code  = 200;
        $data  = $info['data'];
        $mInfo = $info['mInfo'];

        if ($data) {
            //$effectivetime = $data['effectivetime'];//自定义时间 时间戳  此字段迁至中台存储
            $arrearstime   = $data['arrearstime'];  //欠费-200到0元的开始时间  时间戳
            $isover        = $data['isover'];       //是否超过自定义时间 0 无 1 有
        } else {
            //$effectivetime = 0;
            $arrearstime   = 0;
            $isover        = 0;
        }
        $Info     = (new \Model\Member\Member)->getMemberInfo($fid, 'id', 'id,dtype,group_id');
        if (!isset($Info['group_id'])) {
            return 200;
        }
        if ($Info['group_id'] != 2) {
            //用户余额大于0元 去除七天欠费日期
            $money = $mInfo['amoney'] / 100;
            if ($money >= 0 && $data) {
                $InfoSms = $this->_model->getInfo($fid, 'issms');       //获取用户发送短信情况
                if ($InfoSms['issms'] == 1) {
                    $this->_model->updataSms([$fid], 1);             //改下用户短信发送情况
                }
                $this->_model->insertData($fid, 0, 0);              //去除欠费-200到0的用户的计入时间戳
                $time = $this->_model->checkSeven($fid);
                if ($time['isover'] == 1) {
                    $this->_model->insertData($fid, 3, 0);          //把超出自定义时间去除
                }
            }

            //用户余额小于0 判断是否配置了自定义欠费日期
            if ($money <= -200) {
                if ($effectivetime > 0) {
                    if ($this->_model->checkEffectiveTime($effectivetime)) {
                        $code = 204; //超出了自定义欠费时长
                        $this->_model->insertData($fid, 2, 0); //超出了自定义欠费时长 更新isover 为1
                    } else {
                        //$code = 203; //在欠费时长内继续使用
                        $code = 200;   //根据产品要求进行 提示修改
                    }
                } else {
                    $code = 206; //欠费大于-200
                    $this->_model->insertData($fid, 1, 0);
                }
            }
            //小于0元 大于-200
            if ($money >= -200 && $money < 0) {

                if ($effectivetime > 0) {
                    if ($this->_model->checkEffectiveTime($effectivetime)) {
                        $code = 204; //超出了自定义欠费时长
                        $this->_model->insertData($fid, 2, 0); //超出了自定义欠费时长 更新isover 为1
                    } else {
                        //$code = 202; //今日提醒 小于0元 大于-200
                        $code = 200;   //根据产品要求进行 提示修改
                    }
                } else {
                    if ($this->_model->sevenLimit($arrearstime, 0)) {
                        $code = 205; //没有自定义时间 并且超过了七天
                    } else {
                        if ($effectivetime == 0 && $arrearstime == 0) {
                            $this->_model->insertData($fid, 1, 0);
                        }
                        $code = 202; //今日提醒 小于0元 大于-200
                    }
                }
            }
            //如果有自定义时间 那么先判定自定义时间

            //小于200元 大于0元
            if ($money > 0 && $money <= 200) {
                //会员类型0供应商1分销商2直接供应方3合并终端后的资源方5普通用户6员工7集团帐号9平台帐号
                if ($Info['dtype'] == 0) {
                    $code = 201; //今日提醒 小于200元 大于0元
                }
            }

            return $code;
        } else {
            return 200;
        }
    }

    /**
     * 获取账户余额的状态码
     *
     * @return $code 200 正常、201 今日提醒 小于200元 大于0元、202 今日提醒 小于0元 大于-200、203 在欠费时长内继续使用、204 超出自定义设置时间 205超出7天时长 206账户余额小于0   204,205,206访问页面限定
     */
    private function _ExpenseCodeJudgeV2()
    {
        if (!$this->sid) {
            return 200;
        }
        $info  = $this->_model->checkLoginV2($this->sid);
        $memberSignBill = new MemberSignBill();
        $effectiveTimeResult = $memberSignBill->getMemberSignBill($this->sid);
        $effectivetime = $effectiveTimeResult['data']['arrearsTime'] ?? 0;
        $fid   = $this->sid;
        $code  = 200;
        $data  = $info['data'];
        $mInfo = $info['mInfo'];

        if ($data) {
            //$effectivetime = $data['effectivetime'];//自定义时间 时间戳  此字段迁至中台存储
            $arrearstime   = $data['arrearstime'];  //欠费-200到0元的开始时间  时间戳
            $isover        = $data['isover'];       //是否超过自定义时间 0 无 1 有
        } else {
            //$effectivetime = 0;
            $arrearstime   = 0;
            $isover        = 0;
        }
        $Info     = (new \Model\Member\Member)->getMemberInfo($fid, 'id', 'id,dtype,group_id');
        if (!isset($Info['group_id'])) {
            return 200;
        }
        if ($Info['group_id'] != 2) {
            //用户余额大于0元 去除七天欠费日期
            $money = $mInfo['amoney'] / 100;
            if ($money >= 0 && $data) {
                $InfoSms = $this->_model->getInfo($fid, 'issms');       //获取用户发送短信情况
                if ($InfoSms['issms'] == 1) {
                    $this->_model->updataSms([$fid], 1);             //改下用户短信发送情况
                }
                $this->_model->insertData($fid, 0, 0);              //去除欠费-200到0的用户的计入时间戳
                $time = $this->_model->checkSeven($fid);
                if ($time['isover'] == 1) {
                    $this->_model->insertData($fid, 3, 0);          //把超出自定义时间去除
                }
            }

            //用户余额小于0 判断是否配置了自定义欠费日期
            if ($money < 0) {
                if ($effectivetime > 0) {
                    if ($this->_model->checkEffectiveTime($effectivetime)) {
                        $code = 204; //超出了自定义欠费时长
                        $this->_model->insertData($fid, 2, 0); //超出了自定义欠费时长 更新isover 为1
                    } else {
                        //$code = 203; //在欠费时长内继续使用
                        $code = 200;   //根据产品要求进行 提示修改
                    }
                } else {
                    $code = 206; //欠费大于0
                    $this->_model->insertData($fid, 2, 0); //账户余额小于0 更新isover 为1
                }
            }
//            //产品何晟旻 要求不再支持欠200元内7天后强制要求充值的逻辑
//            //小于0元 大于-200
//            if ($money >= -200 && $money < 0) {
//
//                if ($effectivetime > 0) {
//                    if ($this->_model->checkEffectiveTime($effectivetime)) {
//                        $code = 204; //超出了自定义欠费时长
//                        $this->_model->insertData($fid, 2, 0); //超出了自定义欠费时长 更新isover 为1
//                    } else {
//                        //$code = 202; //今日提醒 小于0元 大于-200
//                        $code = 200;   //根据产品要求进行 提示修改
//                    }
//                } else {
//                    if ($this->_model->sevenLimit($arrearstime, 0)) {
//                        $code = 205; //没有自定义时间 并且超过了七天
//                    } else {
//                        if ($effectivetime == 0 && $arrearstime == 0) {
//                            $this->_model->insertData($fid, 1, 0);
//                        }
//                        $code = 202; //今日提醒 小于0元 大于-200
//                    }
//                }
//            }
            //如果有自定义时间 那么先判定自定义时间 

            //小于200元 大于0元
            if ($money > 0 && $money <= 200) {
                //会员类型0供应商1分销商2直接供应方3合并终端后的资源方5普通用户6员工7集团帐号9平台帐号
                if ($Info['dtype'] == 0) {
                    $code = 201; //今日提醒 小于200元 大于0元
                }
            }

            return $code;
        } else {
            return 200;
        }
    }

    /**
     * 会员管理写入自定义欠费时间
     *
     * @return array
     */
    public function editMemberExpense()
    {
        //自定义欠费时间戳
        $limitTime = I('limitTime', 0);
        //用户id
        $fid = I('fid', 0);

        if (!is_numeric($limitTime) || !is_numeric($fid)) {
            $this->apiReturn(201, array(), '参数有误！');
        }

        $memberBiz     = new \Business\Member\Member();
        $field         = 'position';
        $memberExtInfo = $memberBiz->getInfoInMemberExtFromJava($this->loginInfo['memberID'], $field);

        // 市场/客服 部门经理 可编辑，其它只读
        if (is_numeric($memberExtInfo['position']) && !in_array($memberExtInfo['position'], [0, 1])) {
            $this->apiReturn(201, array(), '没有权限(非市场、客服部门经理)');
        }
        $staffModel   = new Staff();
        $staffInfoArr = $staffModel->getInfoByMemberId((int)$this->loginInfo['memberID']);
        // 判断在pft_staff 中是不是 部门经理
        if ($staffInfoArr['st_type'] != 3) {
            $this->apiReturn(201, array(), '没有权限(非市场、客服部门经理)');
        }

        $v = $this->_model->insertData($fid, 4, $limitTime);
        $memberSignBill = new MemberSignBill();
        $effectiveTimeResult = $memberSignBill->setMemberSignBill($fid, $limitTime);
        //if (is_numeric($v)) {
        if ($effectiveTimeResult['code'] == self::CODE_SUCCESS) {
            $this->apiReturn(200, array(), '操作成功！');
        } else {
            $this->apiReturn(201, array(), '操作失败！');
        }
    }

    /**
     * 会员管理删除自定义欠费时间
     *
     * @return array
     */
    public function delMemberExpense()
    {
        //自定义时间 传入0 删除
        $limitTime = I('limitTime', 0);
        //会员id
        $fid = I('fid', 0);

        if (!is_numeric($limitTime) || !is_numeric($fid)) {
            $this->apiReturn(201, array(), '参数有误！');
        }

        //$v = $this->_model->insertData($fid, 5, $limitTime);
        $memberSignBill = new MemberSignBill();
        $effectiveTimeResult = $memberSignBill->setMemberSignBill($fid, $limitTime);
        //if (is_numeric($v)) {
        if ($effectiveTimeResult['code'] == self::CODE_SUCCESS) {
            $this->apiReturn(200, array(), '操作成功！');
        } else {
            $this->apiReturn(201, array(), '操作失败！');
        }
    }

    /**
     * 获取会员管理自定义欠费时间
     *
     * @return array
     */
    public function getMemberExpense()
    {
        //用户id
        $fid = I('fid', 0);
        if (!is_numeric($fid)) {
            $this->apiReturn(201, array(), '参数有误！');
        }
        $res = $this->_model->getInfo($fid);
        $this->apiReturn(200, $res, '操作成功！');
    }

    /**
     * 页面限定访问
     *
     */
    public function limit($page = 'recharge', $msg = '')
    {
        $host         = str_replace('www.', '', $_SERVER['HTTP_HOST']);
        $applyAccount = substr($host, 0, strpos($host, '12301') - 1);

        $white = $this->_whitePageArr();

        if (strstr($_SERVER['REQUEST_URI'], "?")) {
            $request_url = $_SERVER['REQUEST_URI'];
            $request_url = substr($_SERVER['REQUEST_URI'], 0, stripos($_SERVER['REQUEST_URI'], '?'));
        } else {
            $request_url = $_SERVER['REQUEST_URI'];
        }

        if ($applyAccount == 'my') {

            if ($page == 'recharge' && !in_array($request_url, $white['rechargeArr']) && !$this->checkUnAuth($white['rechargeArr'])) {
                // header("Location: /recharge.html");
                echo "<script>location.href='/new/recharge.html';</script>";
                exit;
            }
            if ($page == 'appCenter' && !in_array($request_url, $white['appCenterArr']) && !$this->checkUnAuth($white['appCenterArr'])) {
                // header("Location: /new/appcenter_apppackage.html");
                \Business\Member\Session::sessionSet('renewJudge', -1);
                (new BusinessCache())->setBusinessCache(['renewJudge' => -1], $this->sid);
                echo "<script>location.href='/new/servicecenter_packages.html#/';</script>";
                exit;
            }

            if ($page == 'eleVoucher' && !in_array($request_url, $white['eleVoucherArr']) && !$this->checkUnAuth($white['eleVoucherArr'])) {
                echo "<script>location.href='/new/prestorage.html';</script>";
                exit;
            }

            if ($page == 'authorized_login' && !in_array($request_url, $white['authorizedLoginArr'])) {
                echo "<script>location.href='/premisslogin.html?msg={$msg}';</script>";
                exit;
            }

        } else {
            if ($page == 'recharge' && !in_array($request_url, $white['rechargeArr'])) {
                // header("Location: /new/d/recharge.html");
                echo "<script>location.href='/new/recharge.html';</script>";
                exit;
            }
            if ($page == 'appCenter' && !in_array($request_url, $white['appCenterArr']) && I("a") != "apppackage") {
                // header("Location: /new/d/new/appcenter_apppackage.html");
                \Business\Member\Session::sessionSet('renewJudge', -1);
                (new BusinessCache())->setBusinessCache(['renewJudge' => -1], $this->sid);
                echo "<script>location.href='/new/servicecenter_packages.html#/';</script>";
                exit;
            }
            if ($page == 'eleVoucher' && !in_array($request_url, $white['eleVoucherArr'])) {
                echo "<script>location.href='/new/prestorage.html';</script>";
                exit;
            }
            if ($page == 'authorized_login' && !in_array($request_url, $white['authorizedLoginArr'])) {
                echo "<script>location.href='/premisslogin.html?msg={$msg}';</script>";
                exit;
            }
        }

        //子商户过期页面跳转
        if ($page == 'sub_merchant' && !in_array($request_url, $white['sub_merchant'])) {
            $bylogin = isset($_GET['bylogin']) ? '?bylogin=1' : '';
            echo "<script>location.href='/new/submerchant_expire.html{$bylogin}';</script>";
            exit;
        }

    }

    /**
     * 不跳转白名单页面
     *
     */

    private function _whitePageArr()
    {
        return [
            'rechargeArr'        => [
                '/recharge.html',
                '/new/recharge.html',
                '/new/servicecenter.html',
                '/new/servicecenter_packages.html',
                '/new/certification_authority.html',
                //'/unAuth.html',
            ],
            'appCenterArr'       => [
                '/new/appcenter_apppackage.html',
                '/new/appcenter_pay.html',
                '/new/recharge.html',
                '/new/servicecenter.html',
                '/new/servicecenter_packages.html',
                '/new/servicecenter_apps.html',
                '/new/servicecenter_terminals.html',
                '/new/servicecenter_resource.html',
                '/new/servicecenter_increment.html',
                '/terminal_chk.html',
                // '/unAuth.html',
            ],
            'eleVoucherArr'      => [
                '/new/prestorage.html',
                '/new/servicecenter.html',
                //'/unAuth.html',
            ],
            'authorizedLoginArr' => [
                '/premisslogin.html',
            ],
            //子商户页面白名单
            'sub_merchant'       => [
                //过期页面
                '/new/submerchant_expire.html',
            ],
        ];
    }

    /**
     * 判断子商户是否过期
     * <AUTHOR>
     * @date   2023/5/29
     *
     * @return bool
     */
    private function _checkSubMerchantExpired()
    {
        // 登录的是子商户
        if (!MemberLoginHelper::isSubMerchantLogin()) {
            return false;
        }

        //供应商套餐过期
        $supplierPackageExpiration = false;
        //供应商应用过期
        $supplierModuleExpiration = false;
        //子商户禁用
        $subMerchantsDisabled = false;
        // 子商户id
        $subMerchantId = MemberLoginHelper::getLoginBusinessMember()->getSId();
        // 子商户的供应商id
        $supplierId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();

        //判断子商户的供应商套餐是否过期
        $useInfo = (new ModuleList())->getTargetInfoByUidTime($supplierId);
        if (empty($useInfo)) {
            $supplierPackageExpiration = true;
        }

        //判断子商户的供应商商户管理应用是否过期
        if (!$supplierPackageExpiration) {
            $moduleOpen = (new ModuleCommonBiz())->checkUserIsCanUseApp($supplierId, 'Merchant_management');
            if (!$moduleOpen) {
                $supplierModuleExpiration = true;
            }
        }

        //判断子商户是否被禁用
        if (!$supplierPackageExpiration && !$supplierModuleExpiration) {
            $subMerchantInfoRes = (new SubMerchantApi())->getMemberInfo($subMerchantId);
            if ($subMerchantInfoRes['code'] != 200 || empty($subMerchantInfoRes['data'])) {
                //禁用查询失败，默认false
                return false;
            }

            //不存在默认禁用， 商户状态; 0：禁用,1:启用
            $state = $subMerchantInfoRes['data'][0]['status'] ?? 0;
            if (!$state) {
                return true;
            }
        }

        //返回
        if ($supplierPackageExpiration || $supplierModuleExpiration || $subMerchantsDisabled) {
            return true;
        }

        return false;
    }

    /**
     * 验证是否是无权页面访问
     * <AUTHOR>
     * @date   2024/04/10
     *
     * @param $whiteList
     *
     * @return bool
     */
    public function checkUnAuth($whiteList = [])
    {
        if (empty($whiteList) || !isset($_SERVER['HTTP_REFERER'])) {
            return false;
        }

        if (strstr($_SERVER['REQUEST_URI'], "?")) {
            $requestUrl = substr($_SERVER['REQUEST_URI'], 0, stripos($_SERVER['REQUEST_URI'], '?'));
        } else {
            $requestUrl = $_SERVER['REQUEST_URI'];
        }

        if ($requestUrl != '/unAuth.html') {
            return false;
        }

        $referer_url = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_PATH);

        //说明白名单url没有权限，继续跳转无权限
        if (in_array($referer_url, $whiteList)) {
            return true;
        }

        return false;
    }
}