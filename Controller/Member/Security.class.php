<?php
/**
 * 用户安全相关接口
 * <AUTHOR>
 * @date   2019-05-22
 */

namespace Controller\Member;

use Business\JavaApi\Member\MemberShortLink;
use Library\Controller;
use Business\Member\Security AS SecurityBiz;

class Security extends Controller {

    private $_unloginList = ['unblacklistForIpFromSms', 'confirmLogiIp'];

    public function __construct()
    {
        if (!in_array(I('a'), $this->_unloginList)) {
            $this->isLogin('ajax');
            $loginInfo = $this->getLoginInfo('ajax', false, false);

            $this->_mid = $loginInfo['memberID'];
            $this->_sid = $loginInfo['sid'];
        }
        $this->_securityBiz = new SecurityBiz();
    }

    /**
     * 根据过往的登录日志，匹配出常用登录地
     * <AUTHOR>
     * @date   2019-05-22
     */
    public function getLoginAddrList() {
        $result = $this->_securityBiz->getLoginAddrList($this->_mid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取常用登录地
     * <AUTHOR>
     * @date   2019-05-24
     */
    public function getCommonLoginAddr() {

        $result = $this->_securityBiz->getCommonLoginAddr($this->_mid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 设置常用登录地
     * <AUTHOR>
     * @date   2019-05-22
     */
    public function setCommonLoginAddr() {

        //开关
        $open = I('open', 0);
        //地址
        $addr = I('addr', '');
        //手机
        $mobile = I('mobile', '');
        //是否二次登陆
        $isTwoCheck = I('post.phone_login_check',0);
        $result = $this->_securityBiz->setCommonLoginAddr($this->_mid, $open, $addr, $mobile,$isTwoCheck);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 登录限制解锁
     * <AUTHOR>
     * @date   2019-05-23
     */
    public function unlockLogin() {

        $member_id = I('member_id', 0);

        $result = $this->_securityBiz->unlockLogin($this->_sid, $this->_mid, $member_id);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }

    }


    /**
     * 搜索ip禁用记录
     * <AUTHOR>
     * @date   2019-05-30
     */
    public function searchForIpLock() {

        $ip       = I('ip', '');
        $account  = I('account');
        $type     = I('type', 1); //1 ip搜索，2 账号搜索

        $result = $this->_securityBiz->searchForIpLock($this->_sid, $ip, $account, $type);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 将ip从黑名单移除
     * <AUTHOR>
     * @date   2019-05-23
     */
    public function unblacklistForIp() {

        $ip = I('ip', '');

        $result = $this->_securityBiz->unblacklistForIp($this->_sid, $ip);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    public function confirmLogiIp() {
        $auth = $_GET['auth'];
        $type = $_GET['type'] ?? 0;
        if (!$auth) {
            die('无效链接');
        }
        if ($type == 2){  //这边去找java啦
            $memberShortApi = new MemberShortLink();
            $res = $memberShortApi->checkShortLinkVerify($auth);
            if ($res['code'] != 200){
                die('无效链接');
            }
            die('确认成功');
        }
        $code = pft_authcode($auth, 'DECODE', '12301');
        if (!$code) {
            die('无效链接');
        }

        $codeArr = explode('|', $code);

        $ip = $codeArr[0];
        $customerId = $codeArr[1];

        $result = $this->_securityBiz->confirmLogiIp($customerId, long2ip($ip));
        die('确认成功');
    }

}
