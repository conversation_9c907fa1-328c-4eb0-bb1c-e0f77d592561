<?php
/**
 * Created by PhpStorm.
 * User: lanlc
 * Date: 8/13-013
 * Time: 16:34
 */

namespace Controller\Member;

use Business\JavaApi\Member\BalanceWarning;
use Library\Constants\Account\BookSubject;
use Model\Member\Member;
use Library\Controller;

class ThresHold extends Controller
{
    /** @var $model \Model\Member\BalanceWarning */
    private $model;

    private $loginInfo;

    private $specialAccount = [
        BookSubject::VOUCHER_ACCOUNT_CODE => '凭证费账本',
        BookSubject::SMS_ACCOUNT_CODE     => '短信费账本',
    ];

    public function __construct()
    {
        $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo('ajax', false, false);

        $this->model = new \Model\Member\BalanceWarning();
    }

    /*
     *  设置预警阈值
     *  @param  type    设置类型（0.分销商，1.供应商）
     *  @param  fid     分销商ID
     *  @param  aid     供应商ID
     *  @param  money   阈值
     *  @param  notice_type   预警方式 0：微信 1：短信
     *  @param  alarm_mobile   短信发送的手机号
     *  @param  warning_type  预警类型  1 授信 2 专项费用
     *  @param  warning_val  预警通知内容 {"2503":1,"2504":1}    2503 凭证费 : 0 状态 0关闭 1 开启   2504 短信费 : 0 状态 0关闭 1 开启
     *
     * */
    public function setThreshold()
    {
        $type           = I('post.type', '');
        $threshold      = I('post.money', '');  //专项费用情况 代表短信
        $threshold2     = I('post.money1', ''); //专项费用情况 代表凭证费
        $noticeType     = I('post.notice_type', '');
        $alarmMobile    = I('post.mobile', '');
        $state          = I('post.state', 0, 'intval');
        $warningType    = I('post.warning_type', 1, 'intval');
        $warningVal     = I('post.warning_val', '', 'strval');
        $noticePeople   = I('post.notice_people', -1, 'intval'); //通知对象 0通知自己 1通知对应 2通知双方
        $sidBalance     = I('post.sid_balance', 0, 'intval'); //交易对方预警金额
        $fidWarningType = I('post.fid_warning_type', 0, 'intval'); //自己预警类型: 0 剩余余额 1 可用额度
        $sidWarningType = I('post.sid_warning_type', 0, 'intval'); //交易对方预警类型: 0 剩余余额 1 可用额度

        //手机号效验
        $check = '/^(1(([35789][0-9])|(47)))\d{8}$/';
        if (!empty($alarmMobile) && !preg_match($check, $alarmMobile) && $noticeType == 1) {
            echo json_encode(array('flag' => -1, 'info' => '请输入正确的手机号'));
            exit;
        }

        //当前为供应商给分销商设置预警阈值
        if ($type == 1) {
            $aid = $this->loginInfo['sid'];
            $fid = I('fid', 0, 'intval');
        } else {
            $fid = $this->loginInfo['sid'];
            $aid = I('aid', 0, 'intval');
        }
        //当前登录id
        $loginId = $this->loginInfo['sid'];
        $opId    = $this->loginInfo['memberID'];

        if ($noticePeople == -1 && $threshold > 99999) {
            echo json_encode(array('flag' => -1, 'info' => '阈值超过上限'));
            exit;
        }
        if ($noticePeople == -1 && $threshold < 0) {
            echo json_encode(array('flag' => -1, 'info' => '阈值低于下限'));
            exit;
        }

        if ($threshold2 > 99999) {
            echo json_encode(array('flag' => -1, 'info' => '阈值超过上限'));
            exit;
        }
        if ($threshold2 < 0) {
            echo json_encode(array('flag' => -1, 'info' => '阈值低于下限'));
            exit;
        }

        if (!in_array($noticeType, [0, 1])) {
            echo json_encode(array('flag' => -1, 'info' => '参数错误!'));
            exit;
        }

        if ($warningVal && !json_decode($warningVal, true)) {
            echo json_encode(array('flag' => -1, 'info' => '预警配置有误，请稍后重试'));
            exit;
        }

        //授信预警的 阈值单位是分  专项费用预警的  阈值单位是条数  所以需要特殊处理下
        $threshold  = $warningType == 1 ? number_format($threshold, 2, '.', '') * 100 : $threshold;
        $threshold2 = $warningType == 2 ? $threshold2 : '';

        $this->writeLog($type, $fid, $aid, $threshold, $threshold2, $noticeType, $alarmMobile, $warningType, $noticePeople, $sidBalance, $fidWarningType, $sidWarningType);

        switch ($warningType) {
            //授信预警
            case 1:
                echo json_encode(array('flag' => -1, 'info' => '授信预警功能已迁移，请联系管理员'));
                exit;
                break;
            //专项费用预警
            case 2:
                $this->setSpecialNotice($loginId, $threshold, $threshold2, $noticeType, $warningVal, $opId, $alarmMobile);
                break;
        }
    }

    /*
     * 获取原配置预警阈值
     *  @param  type    设置类型（0.分销商，1.供应商）
     *  @param  fid     分销商ID
     *  @param  aid     供应商ID
     * */
    public function getBalance()
    {
        $type  = I('post.type', '');

        if ($type == 1) {
            $aid         = $this->loginInfo['sid'];
            $fid         = I('fid', 0, 'intval');
            $subjectCode = BookSubject::SUPPLY_CREDIT_SUBJECT_CODE;
        } else {
            $fid         = $this->loginInfo['sid'];
            $aid         = I('aid', 0, 'intval');
            $subjectCode = BookSubject::CREDIT_SUBJECT_CODE;
        }

        $where                = array(
            'fid'     => $fid,
            'is_boss' => $type,
            'boss_id' => $aid,
        );
        $loginId              = $this->loginInfo['sid'];
        $res                  = $this->model->getBalance($where);
        $balanceWarningApi    = new BalanceWarning();
        $balanceWarningNotice = $balanceWarningApi->queryBalanceWarningNotice($loginId, $subjectCode,
            $subjectCode == BookSubject::SUPPLY_CREDIT_SUBJECT_CODE ? $fid : $aid);
        if ($balanceWarningNotice['code'] != 200) {
            echo json_encode(array('flag' => 400, 'error' => '接口异常'));
            exit;
        }

        $model      = new Member();
        $memberInfo = $model->getMemberInfoByMulti([intval($loginId)], 'id', 'mobile');
        if ($memberInfo) {
            $mobile = $memberInfo[0]['mobile'];
        } else {
            $mobile = '';
        }

        if ($balanceWarningNotice['data']) {
            $alarmMobile      = '';//短信发送的手机号
            //短信预警返回字段处理
            foreach ($res as $value) {
                if (!empty($value['alarm_mobile'])) {
                    $alarmMobile = $value['alarm_mobile'];
                }
            }
            $noticeType     = isset($balanceWarningNotice['data']['noticeType']) ? $balanceWarningNotice['data']['noticeType'] : 0;
            $balance        = isset($balanceWarningNotice['data']['balance']) ? $balanceWarningNotice['data']['balance'] : 0;
            $state          = isset($balanceWarningNotice['data']['state']) ? $balanceWarningNotice['data']['state'] : 0;

            echo json_encode(array(
                    'flag'           => 1,
                    'balance'        => $balance / 100,
                    'state'          => $state,
                    'mobile'         => $mobile,
                    'notice_type'    => intval($noticeType),
                    'alarm_mobile'   => $alarmMobile,
                    'wxappid'        => PFT_WECHAT_APPID,
                    'balance_config' => true,
                ));
        } else {
            echo json_encode(array('flag' => -2, 'mobile' => $mobile, 'wxappid' => PFT_WECHAT_APPID,'balance_config' => false));
        }
    }

    /*
     * 设置预警阈值的状态
     *  @param  type    设置类型（0.分销商，1.供应商）
     *  @param  fid     分销商ID
     *  @param  aid     供应商ID
     *  @param  state   设置的状态
     * */
    public function setState()
    {
        $type  = I('post.type', '');
        $fid   = I('post.fid', '');
        $aid   = I('post.aid', '');
        $state = I('post.state', '');

        if ($type == 1) {
            if ($this->loginInfo['sid'] != $aid) {
                echo json_encode(array('flag' => -1, 'info' => '非法请求'));
                exit;
            }
        } else {
            if ($this->loginInfo['sid'] != $fid) {
                echo json_encode(array('flag' => -1, 'info' => '非法请求'));
                exit;
            }
        }

        $where         = array(
            'fid'     => intval($fid),
            'is_boss' => intval($type),
            'boss_id' => intval($aid),
        );
        $data['state'] = intval($state);
        $res           = $this->model->setState($where, $data);
        if ($res == -2) {
            echo json_encode(array('flag' => -2));
        } else if ($res == 0) {
            echo json_encode(array('flag' => -1));
        } else {
            echo json_encode(array('flag' => 1));
        }
    }

    private function writeLog($type, $fid, $aid, $threshold, $threshold2, $noticeType, $alarmMobile, $warningType, $noticePeople, $sidBalance, $fidWarningType, $sidWarningType)
    {
        $data = [
            'key'            => 'setThreshold',
            'type'           => $type,
            'fid'            => $fid,
            'aid'            => $aid,
            'threshold'      => $threshold,
            'threshold2'     => $threshold2,
            'noticeType'     => $noticeType,
            'alarmMobile'    => $alarmMobile,
            'warningType'    => $warningType,
            'noticePeople'   => $noticePeople,
            'sidBalance'     => $sidBalance,
            'fidWarningType' => $fidWarningType,
            'sidWarningType' => $sidWarningType,
        ];
        pft_log('debug/threshold', json_encode($data));
    }

    /**
     * 专项费用预警设置
     * <AUTHOR>  Li
     * @date  2020-10-22
     *
     * @param  int  $loginId  登录用户id
     * @param  int  $threshold  短信预警值
     * @param  int  $threshold2  凭证费预警值
     * @param  int  $noticeType  预警方式 0：微信 1：短信
     * @param  string  $warningVal  预警通知内容 {"2503":1,"2504":1}    2503 凭证费 : 0 状态 0关闭 1 开启   2504 短信费 : 0 状态 0关闭 1 开启
     */
    private function setSpecialNotice($loginId, $threshold, $threshold2, $noticeType, $warningVal, $openid = 0, $alarmMobile = '')
    {
        $warningInfo = json_decode($warningVal, true);
        if ($warningInfo) {
            $member = new \Model\Member\Member();
            $adata  = $member->getMemberInfo($loginId);

            $errorStr          = '';
            $balanceWarningApi = new BalanceWarning();
            $noticeModel       = new \Model\Member\WarningNotice();
            foreach ($warningInfo as $code => $state) {
                if (in_array($code, array_keys($this->specialAccount))) {

                    //配置用户账本ID
                    $subjectCode = $code;

                    if ($noticeType == 0) {
                        //原先旧的微信通知
                        $wxMemberModel = new \Model\Wechat\WxMember();
                        $openList      = $wxMemberModel->getOpenList($loginId, $page = 1, $size = 1);
                        if ($openList == null && $state == 1) {
                            $errorStr .= $this->specialAccount[$subjectCode] . '(请先绑定微信公众号再进行设置) ';;
                        }
                    }

                    $res = $noticeModel->insertData($loginId, $loginId, $subjectCode, $openid, $adata['mobile'],
                        $alarmMobile);

                    if ($res) {
                        $warningData = $balanceWarningApi->queryBalanceWarningNotice($loginId, $subjectCode);
                        $numbers     = $threshold;
                        if ($subjectCode == BookSubject::VOUCHER_ACCOUNT_CODE) {
                            $numbers = $threshold2;
                        }

                        if (empty($numbers)) {
                            continue;
                        }
                        $result = $warningData;
                        //专项预警处理
                        if ($warningData['code'] == 200 && empty($warningData['data'])) {
                            $result = $balanceWarningApi->createBalanceWarningNotice($loginId, $subjectCode, $state, $numbers,
                                $noticeType, $loginId, $openid, $alarmMobile);
                        } elseif ($warningData['code'] == 200 && $warningData['data']['id']) {
                            $result = $balanceWarningApi->updateBalanceWarningNotice($warningData['data']['id'],
                                $numbers, $state, $noticeType, $openid ,$alarmMobile);
                        }

                        if (!isset($result['code']) || $result['code'] != 200) {
                            $errorStr .= $this->specialAccount[$subjectCode] . '(配置异常) : ' . $result['msg'];
                        }
                    } else {
                        $errorStr .= $this->specialAccount[$subjectCode] . '(数据写入失败)';
                    }
                }
            }
            if ($errorStr) {
                echo json_encode(array('flag' => -1, 'info' => $errorStr));
                exit;
            }
            echo json_encode(array('flag' => -1, 'info' => '设置成功'));
            exit;
        } else {
            echo json_encode(array('flag' => -1, 'info' => '预警配置有误，请稍后重试'));
            exit;
        }
    }

    public function getSpecialNoticeInfo()
    {
        //当前登录id
        $loginId = $this->loginInfo['sid'];
        if (!$loginId) {
            echo json_encode(array('flag' => -1, 'info' => '请先登录'));
            exit;
        }
        $balanceWarningApi = new BalanceWarning();

        $memberBiz  = new \Business\Member\Member();
        $memberInfo = $memberBiz->getMemberInfoByMulti([intval($loginId)], 'id');
        if ($memberInfo) {
            $mobile = $memberInfo[0]['mobile'];
        } else {
            $mobile = '';
        }

        $specialNoticeInfo = [
            'flag'         => 1,
            'specialConf'  => [],
            'mobile'       => $mobile,
            'wxappid'      => PFT_WECHAT_APPID,
            'alarm_mobile' => '',
        ];
        $noticeModel       = new \Model\Member\WarningNotice();
        foreach (array_keys($this->specialAccount) as $subjectCode) {
            //获取出发送短信的手机号
            $warningNoticeList                 = $noticeModel->getAccountsConfig($loginId, $loginId, $subjectCode);
            $specialNoticeInfo['alarm_mobile'] = !empty($warningNoticeList['alarm_mobile']) ? $warningNoticeList['alarm_mobile'] : $warningNoticeList['mobile'];

            //获取预警配置
            $warningData                                    = $balanceWarningApi->queryBalanceWarningNotice((int)$loginId,
                (int)$subjectCode);
            $specialNoticeInfo['specialConf'][$subjectCode] = [
                'balance'     => '',
                'state'       => 0,
                'notice_type' => 1,
            ];

            //数据组装
            if ($warningData['code'] == 200 && !empty($warningData['data'])) {
                $specialNoticeInfo['specialConf'][$subjectCode] = [
                    'balance'     => $warningData['data']['balance'],
                    'state'       => (int)$warningData['data']['state'],
                    'notice_type' => (int)$warningData['data']['noticeType'],
                ];
            }
        }
        echo json_encode($specialNoticeInfo);
        exit;
    }

    /**
     * 设置预警通知阈值 授信预警/还款预警
     * <AUTHOR>  Li
     * @date  2022-03-11
     */
    public function setMemberBalanceNotice()
    {
        $type           = I('post.type', '');                    //0授信预警 1还款预警
        $memberId       = I('post.member_id', 0, 'intval');      //被设置的用户id  当授信预警时 传分销商id  当还款预警时 传供应商id
        $state          = I('post.state', 0, 'intval');          //预警状态 0关闭 1开启
        $noticePeople   = I('post.notice_people', -1, 'intval'); //通知对象 0通知自己 1通知对应 2通知双方
        $noticeConfig   = I('post.notice_config', []);

        //设置授信预警时 当前用户作为供应商
        $sid = $this->loginInfo['sid'];
        $fid = $memberId;

        //当前登录id
        $loginId = $this->loginInfo['sid'];
        $opId    = $this->loginInfo['memberID'];

        if (!in_array($noticePeople, [0, 1, 2])) {
            $this->apiReturn(203, [], '通知对象异常，请确认');
        }

        $noticeBiz = new \Business\Member\MemberBalanceNotice();
        //根据类型 对配置的值做判断
        if ($state) {
            switch ($noticePeople) {
                case 0:
                    $checkRes = $noticeBiz->noticeValueCheck($noticeConfig, $sid);
                    if ($checkRes['code'] != 200) {
                        $this->apiReturn(203, [], $checkRes['msg']);
                    }
                    break;
                case 1:
                    $checkRes = $noticeBiz->noticeValueCheck($noticeConfig, $fid);
                    if ($checkRes['code'] != 200) {
                        $this->apiReturn(203, [], $checkRes['msg']);
                    }
                    break;
                case 2:
                    $checkRes = $noticeBiz->noticeValueCheck($noticeConfig, $sid);
                    if ($checkRes['code'] != 200) {
                        $this->apiReturn(203, [], $checkRes['msg']);
                    }
                    $checkRes = $noticeBiz->noticeValueCheck($noticeConfig, $fid);
                    if ($checkRes['code'] != 200) {
                        $this->apiReturn(203, [], $checkRes['msg']);
                    }
                    break;
            }
        }

        $result = $noticeBiz->setBalanceNotice($type, $loginId, $state, $fid, $sid, $opId, $noticePeople, $noticeConfig);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(200, [], '配置成功');
    }

    /**
     * 获取原配置预警阈值
     * */
    public function getMemberBalance()
    {
        $type  = I('post.type', 0, 'intval');  //0授信预警 1还款预警

        if ($type == 0) {
            $sid         = $this->loginInfo['sid'];
            $fid         = I('fid', 0, 'intval');
            $subjectCode = BookSubject::SUPPLY_CREDIT_SUBJECT_CODE;
        } else {
            $fid         = $this->loginInfo['sid'];
            $sid         = I('sid', 0, 'intval');
            $subjectCode = BookSubject::CREDIT_SUBJECT_CODE;
        }

        $where = array(
            'fid'     => $fid,
            'is_boss' => !$type ? 1 : 0,
            'boss_id' => $sid,
        );
        $loginId              = $this->loginInfo['sid'];
        $res                  = $this->model->getBalance($where);

        $balanceWarningApi    = new BalanceWarning();
        $balanceWarningNotice = $balanceWarningApi->queryBalanceWarningNotice($loginId, $subjectCode,
            $subjectCode == BookSubject::SUPPLY_CREDIT_SUBJECT_CODE ? $fid : $sid);
        if ($balanceWarningNotice['code'] != 200) {
            $this->apiReturn(204, [], '接口异常');
        }

        $model      = new Member();
        $memberInfo = $model->getMemberInfoByMulti([intval($loginId)], 'id', 'mobile');
        if ($memberInfo) {
            $mobile = $memberInfo[0]['mobile'];
        } else {
            $mobile = '';
        }

        if ($balanceWarningNotice['data']) {
             $alarmMobile      = '';//短信发送的手机号
            $alarmMobileOther = '';//短信发送的手机号
            //短信预警返回字段处理
            foreach ($res as $value) {
                if (!empty($value['alarm_mobile'])) {
                    $alarmMobile = $value['alarm_mobile'];
                }
                if (!empty($value['alarm_mobile_other'])) {
                    $alarmMobileOther = $value['alarm_mobile_other'];
                }
            }

            $state          = isset($balanceWarningNotice['data']['state']) ? $balanceWarningNotice['data']['state'] : 0;
            $noticePeople   = isset($balanceWarningNotice['data']['noticePeople']) ? $balanceWarningNotice['data']['noticePeople'] : 0;

            $noticeConfig = [];
            if ($noticePeople == 0) {
                $noticeConfig['self'] = [
                    'accountId'    => $balanceWarningNotice['data']['accountId'],
                    'balance'      => $balanceWarningNotice['data']['balance'] / 100,
                    'warning_type' => $balanceWarningNotice['data']['fidWarningType'],
                    'notice_type'  => $balanceWarningNotice['data']['noticeType'],
                    'alarm_mobile' => $alarmMobile,
                ];
            } elseif ($noticePeople == 1) {
                $noticeConfig['other'] = [
                    'accountId'    => $balanceWarningNotice['data']['tradeAccountId'],
                    'balance'      => $balanceWarningNotice['data']['sidBalance'] / 100,
                    'warning_type' => $balanceWarningNotice['data']['sidWarningType'],
                    'notice_type'  => $balanceWarningNotice['data']['sidNoticeType'],
                    'alarm_mobile' => $alarmMobileOther,
                ];
            } else {
                $noticeConfig = [
                    'self' => [
                        'accountId'    => $balanceWarningNotice['data']['accountId'],
                        'balance'      => $balanceWarningNotice['data']['balance'] / 100,
                        'warning_type' => $balanceWarningNotice['data']['fidWarningType'],
                        'notice_type'  => $balanceWarningNotice['data']['noticeType'],
                        'alarm_mobile' => $alarmMobile,
                    ],
                    'other' => [
                        'accountId'    => $balanceWarningNotice['data']['tradeAccountId'],
                        'balance'      => $balanceWarningNotice['data']['sidBalance'] / 100,
                        'warning_type' => $balanceWarningNotice['data']['sidWarningType'],
                        'notice_type'  => $balanceWarningNotice['data']['sidNoticeType'],
                        'alarm_mobile' => $alarmMobileOther,
                    ],
                ];
            }

            $return = [
                'sid'            => $balanceWarningNotice['data']['sid'],
                'fid'            => $balanceWarningNotice['data']['fid'],
                'flag'           => 1,
                'state'          => $state,
                'noticePeople'   => $noticePeople,
                'wxappid'        => PFT_WECHAT_APPID,
                'balance_config' => $state ? true : false,
                'notice_config'  => $noticeConfig,
            ];

            $this->apiReturn(200, $return, 'success');
        }

        $return = [
            'mobile'         => $mobile,
            'wxappid'        => PFT_WECHAT_APPID,
            'balance_config' => false,
        ];
        $this->apiReturn(200, $return, '当前用户未配置');

    }

}