<?php
/**
 * 用户信息处理
 * <AUTHOR>
 * @date   2016-12-05
 */

namespace Controller\Member;

use Library\Controller;
use Library\Tools\Helpers;

class MemberCertifi extends Controller
{
    private $sid;
    private $did;


    public function __construct()
    {
        $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        $this->sid = $loginInfo['memberID'];
        $this->did = $loginInfo['sid'];
    }

    /**
     * 获得资质认证数据
     * <AUTHOR>
     * @date   2016-12-05
     *
     * @param return array 
     */
    public function getCertifiInfo()
    {
        //用户二期 - 信息获取修改 - 2 - modification-a
        $CustomerBus  = new \Business\Member\Customer();
        $MemberBus   = new \Business\Member\Member();
        $certificationInfo = $CustomerBus->getCertificationInfoByMemberId($this->did,true);
        $customerInfo      = $CustomerBus->getCustomerInfoByMemberId($this->did,true);
        $memberInfo        = $MemberBus->getInfo($this->did,true);
        $certificationInfo['com_type']   = $MemberBus::__CORP_KIND_ARR__[$memberInfo['corp_kind']];
        $certificationInfo['id_card_no'] = $customerInfo['id_card_no'];
        $data = $certificationInfo;

       $uploadApi = "/r/Member_MemberCertifi/upCertifiPhoto";  //图片上传地址
       $var = [
            'other_les'   => '//www.12301.cc/tpl/membercard/images/defaultThum.gif',// 其他证件
            'id_card_hand'=> '//www.12301.cc/tpl/membercard/images/defaultThum.gif',// 身份证正面
            'id_card_B'   => '//www.12301.cc/tpl/membercard/images/defaultThum.gif',// 身份证背面
            'bsn_les'     => '//www.12301.cc/tpl/membercard/images/defaultThum.gif',// 资质认证-营业执照
            'opt_les'     => '//www.12301.cc/tpl/membercard/images/defaultThum.gif',// 经营许可证
            'auth_les'    => '//www.12301.cc/tpl/membercard/images/defaultThum.gif',// 资源方授权书
            'org_les'     => '//www.12301.cc/tpl/membercard/images/defaultThum.gif',// 组织机构代码证
            'tax_les'     => '//www.12301.cc/tpl/membercard/images/defaultThum.gif',// 税务登记证
            'com_type'    => '',// 账户类型
            'did'         => $this->did,
            'certi_status'=> 0,
            'id_card_no'  => ''
        ];
        if(is_array($data)) $var = array_merge($var, array_filter($data));

        //图片地址替换http->https
        $var['other_les']       = images_url_https($var['other_les']);
        $var['id_card_hand']    = images_url_https($var['id_card_hand']);
        $var['id_card_B']       = images_url_https($var['id_card_B']);
        $var['bsn_les']         = images_url_https($var['bsn_les']);
        $var['opt_les']         = images_url_https($var['opt_les']);
        $var['auth_les']        = images_url_https($var['auth_les']);
        $var['org_les']         = images_url_https($var['org_les']);
        $var['tax_les']         = images_url_https($var['tax_les']);
        
        $dats = ['info' => $var,'upload_api' => $uploadApi];
        $this->apiReturn(200, $dats ?: [],'成功');
    }

    /**
     * 上传资质认证证件照
     * <AUTHOR>
     * @date   2016-12-05
     *
     * @param return array 
     */
    public function upCertifiPhoto()
    {
        $nArr = ["action","tt","callback",'limit','offset','id'];
        foreach($_REQUEST as $k=>$v){
            if((!is_array($v))&&(!is_object($v))){
                $v=safetxt($v);
                if(in_array($k,$nArr)){
                    $nInfo[$k]=$v;
                }
            }
        }

        $json = '{"status":"fail"}';
        echo '<script>window.parent.' . $nInfo['callback'] . '('.$json.');</script>';
    }

    /**
     * 上传资质认证数据
     * <AUTHOR>
     * @date   2016-12-05
     *
     * @param return array  code 200:成功 204:失败 msg
     */
    public function saveCertifiInfo()
    {
        $comType     = I('post.com_type');
        $did         = I('post.did');
        $idCard      = I('post.id_card');
        $idCardHand  = I('post.id_card_hand');
        $idCardB     = I('post.id_card_B');
        $otherLes    = I('post.other_les');
        $bsnLes      = I('post.bsn_les');
        $optLes      = I('post.opt_les');
        $taxLes      = I('post.tax_les');
        $orgLes      = I('post.org_les');
        $authLes     = I('post.auth_les');
        $did = $did + 0;
        if($did==0) {
            $this->apiReturn(204,[],'参数错误');
            exit;
        }
        $code = '204';
        $msg  = '保存失败';
        // 个人
        if($comType=='个人'){
            if(!$idCard){
                $this->apiReturn(205,[],'身份证不能为空');
                exit;
            }
            if(!$idCardHand){
                $this->apiReturn(205,[],'请上传手持身份证正面');
                exit;
            }
            if(!$idCardB) {
                $this->apiReturn(205,[],'请上传手持身份证背面');
                exit;
            }
            if($idCard){
                //用户二期 - 信息获取修改
                $CustomerModel = new \Model\Member\Customer();
                $check = $CustomerModel->getCustomerIdByIdCardNo($idCard);
                if($check) {
                    $this->apiReturn(204,[],'身份证已存在');
                }
            }
            $save['id_card_no']    = $idCard;
            $save['id_card_B']     = $idCardB;
            $save['other_les']     = $otherLes;
            $save['id_card_hand']  = $idCardHand;
            $save['certi_status']  = ($this->did==$did) ? 1:2;
            if($this->did==$did){
                $save['pass_time'] = date('Y-m-d H:i:s');
            }
            //用户二期 - 信息获取修改
            $CustomerBus = new \Business\Member\Customer();
            $res = $CustomerBus->updateCertification($this->did, $save, 1);
            if($res === true){
                $code = '200';
                $msg  = '保存成功';
            }  else{
                $code = '204';
                $msg  = $res;
            }
            $this->apiReturn($code, [], $msg);
            exit;
        }
        
        // 公司
        $save['bsn_les']  = $bsnLes;
        $save['opt_les']  = $optLes;
        $save['tax_les']  = $taxLes;
        $save['org_les']  = $orgLes;
        $save['auth_les'] = $authLes;
        if(!$bsnLes){
            $this->apiReturn(205,[],'请上传资质认证');
            exit;
        } 
        if(!$orgLes){
            $this->apiReturn(205,[],'请上传组织机构代码证');
            exit;
        }
        if(!$taxLes){
            $this->apiReturn(205,[],'请上传税务登记证');
            exit;
        }
        $save['certi_status']  = ($this->did==$did) ? 1:2;
        if($this->did==$did){
            $save['pass_time'] = date('Y-m-d H:i:s');
        }

        //用户二期 - 信息获取修改
        $CustomerBus = new \Business\Member\Customer();
        $res = $CustomerBus->updateCertification($this->did, $save, 2);
        if($res === true){
            $code = '200';
            $msg  = '保存成功';
        }  else{
            $code = '204';
            $msg  = $res;
        }
        $this->apiReturn($code, [], $msg);
    }
}