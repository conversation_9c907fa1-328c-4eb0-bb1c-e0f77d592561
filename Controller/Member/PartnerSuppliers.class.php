<?php

namespace Controller\Member;

use Library\Controller;
use Business\Member\PartnerSuppliers as PartnerSuppliersBiz;


/**
 * 合作供应商接口
 * <AUTHOR>
 */
class PartnerSuppliers extends Controller {


	private $_sid = 0;

	public function __construct() {
		parent::__construct();
		$this->_sid = $this->isLogin();
	}


	/**
	 * 获取我的供应商列表
	 */
	public function getSuppliersList() 
	{
		$keyword = I('keyword', '');
		$page    = I('page', 1);
		$size    = I('size', 10);

		$biz    = new PartnerSuppliersBiz();
		$result = $biz->getSuppliersList($this->_sid, $keyword, $page, $size);

		if (isset($result['code'])) {
			$this->apiReturn($result['code'], $result['data'], $result['msg']);
		} else {
			$this->apiReturn(500, [], '接口异常');
		}
	}


	/**
	 * 导出获取我的供应商列表
	 */
	public function exportSuppliersList() 
	{
		$keyword = I('keyword', '');

		$biz    = new PartnerSuppliersBiz();
		$result = $biz->exportSuppliersList($this->_sid, $keyword);

		if (isset($result['code']) && $result['code'] == 200) {
			$this->excelReturn('我的供应商', '我的供应商', $result['data']);
		} else {
			$this->apiReturn(500, [], '接口异常');
		}
	}

}