<?php
/**
 * 用户微信相关
 * <AUTHOR>
 * @date   2022/6/21
 */

namespace Controller\Member;

use Business\Member\MemberWx as MemberWxBiz;
use Business\Member\MemberBaseInfo as MemberBaseInfoBiz;
use Library\Controller;

class MemberWx extends Controller
{
    private $loginInfo;
    private $sid;
    private $memberId;

    public function __construct()
    {
        $this->loginInfo = $this->getLoginInfo();

        $this->sid      = $this->loginInfo['sid'];
        $this->memberId = $this->loginInfo['memberID'];
    }

    /**
     * 获取微信绑定列表
     * <AUTHOR>
     * @date   2022/6/21
     *
     */
    public function getWxUserBindList()
    {
        $memberBaseInfoBiz = new MemberBaseInfoBiz();
        $res               = $memberBaseInfoBiz->getUserWxBindList($this->memberId);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 设置用户微信自动登录
     * <AUTHOR>
     * @date   2022/6/21
     *
     */
    public function setWxUserAutoLogin()
    {
        $id    = I('post.bind_id', 0, 'intval');
        $state = I('post.state', 0, 'intval');  //1=开启 2=关闭

        if (!$id) {
            $this->apiReturn(204, [], '记录不存在');
        }

        if (empty($state) || !in_array($state, [1, 2])) {
            $this->apiReturn(204, [], '参数错误');
        }

        $memberId = $this->memberId;

        $res = [];
        switch ($state) {
            case 1:
                $res = (new MemberWxBiz())->allowAuthorizedLogin($id, $memberId);
                break;
            case 2:
                $res = (new MemberWxBiz())->closeAuthorizedLogin($id, $memberId);
                break;
        }

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 设置微信备注
     * <AUTHOR>
     * @date   2022/6/21
     *
     */
    public function setWxUserAlias()
    {
        $id    = I('post.bind_id', '', 'intval');
        $alias = I('post.alias', '', 'strval');

        if (empty($alias) || empty($id)) {
            $this->apiReturn(203, [], '参数错误');
        }
        if (mb_strlen($alias) > 20) {
            $this->apiReturn(203, [], '备注限制20字以内');
        }

        $memberId = $this->memberId;

        $res = (new MemberWxBiz())->updateRemark($id, $memberId, $alias);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 解除微信绑定
     * <AUTHOR>
     * @date   2022/6/21
     *
     */
    public function setWxUserUnbind()
    {
        $id = I('bind_id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $memberId = $this->memberId;

        $res = (new MemberWxBiz())->unbind($id, $memberId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 开关接收通知
     * <AUTHOR>
     * @date   2022/6/21
     *
     */
    public function setWxNoticeUser()
    {
        $id    = I('post.bind_id', 0);
        $state = I('post.state', 0, 'intval');  //1=开启 0=关闭
        if (!$id) {
            $this->apiReturn(200, [], '记录不存在');
        }
        if (!in_array($state, [0, 1])) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (in_array($this->loginInfo['dtype'], [2, 3, 6])) {
            $this->apiReturn(204, [], '没有权限操作');
        }

        $memberId = $this->memberId;

        $res = (new MemberWxBiz())->setWxNoticeUserById($id, $memberId, $state);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 设置微信通知
     * <AUTHOR>
     * @date   2022/6/21
     *
     */
    public function setWxNoticeInfo()
    {
        //用户微信绑定表主键id
        $id                   = I('post.bind_id', 0, 'intval');
        $orderNotice          = I('post.order_notice', -1, 'intval');
        $creditManage         = I('post.credit_manage', -1, 'intval');
        $refundRecharge       = I('post.refund_recharge', -1, 'intval');
        $reimburse            = I('post.reimburse', -1, 'intval');
        $smsAccountNotice     = I('post.sms_account_notice', -1, 'intval');
        $voucherAccountNotice = I('post.voucher_account_notice', -1, 'intval');
        $teamOrderNotice      = I('post.team_order_notice', 0, 'intval'); //团单通知
        $aliExpireNotice      = I('post.ali_expire_notice', 0, 'intval'); //阿里供销授权即将到期通知
        $codeFeeNumNotice     = I('post.code_fee_num_notice', 0, 'intval'); //码数预警

        if (!$id) {
            $this->apiReturn(204, [], '记录不存在');
        }
        if (!in_array($orderNotice, [0, 1]) || !in_array($creditManage, [0, 1]) || !in_array($refundRecharge, [0, 1])) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (in_array($this->loginInfo['dtype'], [2, 3, 6])) {
            $this->apiReturn(204, [], '没有权限操作');
        }

        $memberId = $this->memberId;

        $res = (new MemberWxBiz())->setWxNoticeInfoById($id, $memberId, $orderNotice, $creditManage, $refundRecharge,
            $reimburse, $smsAccountNotice, $voucherAccountNotice, $teamOrderNotice, $aliExpireNotice, $codeFeeNumNotice);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}