<?php
/**
 * 平台登录端口统一
 *
 * <AUTHOR>
 * @time   2017-07-10
 *
 * 200成功 207前端跳转登录页 其余弹框
 */

namespace Controller\Member;

use Business\JavaApi\Tools\verifyCode;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\Member\RegisterAction;
use Business\Member\Session as Session;
use Business\MultiDist\Member as MultiDistMember;
use Business\Wechat\Authorization as Authorization;
use Business\Member\Login as LoginBiz;
use Controller\Tpl\bargain;
use Library\Business\PFTcrypt;
use Library\Constants\MemberConst;
use Controller\Login as PcLogin;
use Library\Business\WechatSmallApp;
use Library\Cache\Cache;
use Library\Constants\RedisKey\LoginKeyConst;
use Library\Container;
use Library\Controller as Controller;
use Library\MessageNotify\PFTSMSInterface;
use Library\Tools\BusinessCache;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Tools\Helpers;
use Library\Util\EnvUtil;
use Model\Member\Member as Member;
use Model\Subdomain\SubdomainInfo as SubdomainInfo;
use Model\Wechat\WxMember as WxMember;
use Business\Member\SSOLogin as SSOLoginBiz;

class Login extends Controller
{
    const __PFT_MEMBERID__   = 22647;         // 票付通ID
    const __PFT_ACCOUNT__    = 124078;        // 票付通账号
    const __VCODE_IDENTIFY__ = 'login_sms';   // 短信服务标识

    const __DEFAULT_SY_ACCOUNT     = 100005;    // 三亚账号
    const __DEFAULT_SY_MEMBERID    = 55;        // 三亚ID

    private $_loginInfo       = [];            // 登录信息
    private $_supplyId        = 0;             // 供应商id
    private $_supplyAccount   = 0;             // 供应商账号
    private $_supplyAppId     = 0;             // 供应商AppId
    private $_formPlat        = 'b';           // 平台标识 [b=微平台|c=微商城]
    private $_brakUrl         = '';           // 返回地址

    public function __construct()
    {
        // $this->_sid = $this->isLogin();
        // $this->_login = new \Business\Member\Login();

        $this->_bus      = new \Business\Member\Session();
        $this->_formPlat = I('plat', 'b', 'trim');
        $this->_brakUrl  = I('back_url', '', 'trim');
        $this->_getSupplyInfo();
        $this->_getLoginInfo();
    }

    /**
     * 发送手机验证码
     * <AUTHOR>
     * @date   2017-07-10
     *
     * @param  int  $mobile  手机号
     */
    public function sendVcode()
    {
        if (ENV == 'LOCAL') {
            header("Access-Control-Allow-Origin: *"); // 允许任意域名发起的跨域请求
        }
        $mobile = I('mobile', 0, 'intval');
        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(204, [], "手机号错误");
        }
        $graph = I('graph', '');
        if (!$graph) {
            $this->apiReturn(204, [], "请输入图形验证码");
        }

        $graphRes = $this->checkImgCode($graph);

        if (!$graphRes) {
            $this->apiReturn(206, [], "图形验证码输入错误");
        }

        $redis = Cache::getInstance('redis');
        //按目前的量，24小时只允许发送1000个
        $limitKey = 'limit:mobile:cnt';
        $sendNum  = $redis->get($limitKey);

        if ($sendNum > 5000) {
            $this->apiReturn(206, [], "疑似遭到攻击,接口暂时关闭");
        }

        $mobileKey = "lock:{$mobile}:cnt";
        $times     = $redis->get($mobileKey);
        if ($times > 5) {
            $this->apiReturn(204, [], "发送失败:发送频率频繁");
        }

        if(I('sessionKey')){
            $result = $this->_bus->sendCheckCode($mobile);
        }else{
            $result = $this->_bus->sendLoginVcode($mobile);
        }
        if ($result['code'] == 200) {

            if (!$sendNum) {
                $redis->set($limitKey, 1, '', 3600 * 24);
            } else {
                $redis->incrBy($limitKey, 1);
            }

            if (!$times) {
                $redis->set($mobileKey, 1, '', 3600);
            } else {
                $redis->incrBy($mobileKey, 1);
            }
            $this->apiReturn(200, ["limitTime" => 60], '发送成功');
        } else {
            $this->apiReturn(204, [], "发送失败:{$result['msg']}");
        }
    }

    /**
     * 手机短信验证码登录
     * <AUTHOR>
     * @date   2017-07-10
     *
     * @param  int  $mobile  手机号
     * @param  string  $vcode  短信验证码
     */
    public function loginByVcode()
    {
        $mobile = I('mobile', 0, 'intval');
        $vcode  = I('vcode', '', 'trim');

        $plat = 'all_plat'; // 不区分平台 不存在则注册散客 存在则登录

        $result = $this->_bus->loginVcode($mobile, $vcode, $plat);
        if ($result['code'] == 200) {
            (new BusinessCache())->setBusinessCache(['from_supply' => $this->_supplyAccount], $result['data']['memberID']);
            $this->_bindWechat();
            $mType    = $this->_getMemberType();
            $smallApp = new WechatSmallApp();
            $scanCode = $smallApp->encodeShopCode($result['data']['memberID']);
            $this->apiReturn(200, ['type' => $mType, 'scanCode' => $scanCode], '登录成功');
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 手机短信验证码登录
     * <AUTHOR>
     * @date   2017-07-10
     *
     * @param  int  $mobile  手机号
     * @param  string  $vcode  短信验证码
     */
    public function loginByVcodeForAllDis()
    {
        $mobile = I('mobile', 0, 'intval');
        $vcode  = I('vcode', '', 'trim');

        $plat = 'all_plat'; // 不区分平台 不存在则注册散客 存在则登录

        $result = $this->_bus->loginVcode($mobile, $vcode, $plat);
        if ($result['code'] == 200) {
            (new BusinessCache())->setBusinessCache(['from_supply' => $this->_supplyAccount], $result['data']['memberID']);
            $this->_bindWechat();
            $mType    = $this->_getMemberType();
            $smallApp = new WechatSmallApp();
            $scanCode = $smallApp->encodeShopCode($result['data']['memberID']);

            //没有散客身份, 创建一个散客身份的角色
            $this->_createRoleForTouristbyVcode($mobile);

            $this->apiReturn(200, ['type' => $mType, 'scanCode' => $scanCode], '登录成功');
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 没有散客身份, 创建一个散客身份的角色
     *
     * @param $memberId
     * @param $mobile
     */
    private function _createRoleForTouristbyVcode($mobile)
    {
        $memberMode = new \Model\Member\Member();
        $loginBiz   = new LoginBiz();

        //客户id
        $customerId = $memberMode->getAuthByIdentifier($mobile, MemberConst::AUTH_MOBILE);  //获取用户id
        //获取dtype类型
        $selectTypes = $loginBiz->getAutoJudgeRole(MemberConst::PAGE_MICROPLAT);
        // 获取会员信息
        $roleList = $memberMode->getMultiRoleInfo($customerId, $selectTypes);    //获取对应角色的用户列表

        //没有散客身份, 创建一个散客身份的角色
        $this->_createRoleForTourist($customerId['customer_id'], $roleList);
    }

    /**
     * 登录
     * <AUTHOR>
     * @time   2017-07-10
     *
     * @param  string  $mobile  用户 [账号|手机号]
     * @param  string  $password  密码
     * @param  string  $authCode  验证码
     */
    public function login()
    {
        $mobile     = I('mobile');
        $password   = I('password');
        $authCode   = I('authCode','','trim');
        $roleType   = I('role_type', false);
        $barkUrl    = I('back_url','','trim');

        if (!$mobile) {
            $this->apiReturn(204, [], '账号不能为空');
        }

        if (!$password) {
            $this->apiReturn(204, [], '密码不能为空');
        }

        // 获取会员信息
        $mModel = $this->_getMemberModel();
        if (is_numeric($mobile) && mb_strlen($mobile) == 11) {
            if (!\ismobile($mobile)) {
                $this->apiReturn(204, [], '请输入正确的手机号');
            }
        }

        $loginBiz = new LoginBiz();
        $res      = $loginBiz->loginByPasswd($mobile, md5($password), MemberConst::PAGE_MICROPLAT, $roleType, $authCode);
        if ($res['code'] != 200) {
            if ($res['code'] == 301) {
                $res['data']['vcode'] = 1;
            }
            //密码错误情况提供给前端判断或者验证码
            if ($res['data']['imgCode']) {
                $res['data']['vcode'] = 1;
            }

            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }

        $tmp = [];
        if (isset($_SESSION['openid'])) {
            $tmp = $_SESSION;
        }

        if ($tmp) {
            $_SESSION['wechat_appid'] = $tmp['wechat_appid'];
            $_SESSION['openid']       = $tmp['openid'];
        }

        (new BusinessCache())->setBusinessCache(['from_supply' => $this->_supplyAccount], $res['data']['memberID']);
        $this->_bindWechat();
        //在session中标记当前session中所存信息用于何处：1微平台，平台，0微商城
        $_SESSION['ForWhere'] = 1;

        $mType = $this->_getMemberType();

        $this->apiReturn(200, ['type' => $mType, 'back_url'=> $barkUrl], '登录成功');
    }

    public function loginMicroPlatAfter()
    {
        $tmp = [];
        if (isset($_SESSION['openid'])) {
            $tmp = $_SESSION;
        }

        if ($tmp) {
            $_SESSION['wechat_appid'] = $tmp['wechat_appid'];
            $_SESSION['openid']       = $tmp['openid'];
        }

        $this->_getLoginInfo();

        (new BusinessCache())->setBusinessCache(['from_supply' => $this->_supplyAccount], $this->_loginInfo['memberID']);
        $this->_bindWechat();
        //在session中标记当前session中所存信息用于何处：1微平台，平台，0微商城
        $_SESSION['ForWhere'] = 1;

        return true;
    }

    /**
     * 登录
     * <AUTHOR>
     * @time   2017-07-10
     *
     * @param  string  $mobile  用户 [账号|手机号]
     * @param  string  $password  密码
     * @param  string  $authCode  验证码
     */
    public function loginForAllDis()
    {
        $mobile   = I('mobile');
        $password = I('password');
        $authCode = I('authCode', '', 'trim');
        $roleType = I('role_type', false);

        if (!$mobile) {
            $this->apiReturn(204, [], '账号不能为空');
        }

        if (!$password) {
            $this->apiReturn(204, [], '密码不能为空');
        }

        // 获取会员信息
        $mModel = $this->_getMemberModel();
        if (is_numeric($mobile) && mb_strlen($mobile) == 11) {
            if (!\ismobile($mobile)) {
                $this->apiReturn(204, [], '请输入正确的手机号');
            }
        }
        $loginBiz = new LoginBiz();
        if (Helpers::isMobile($mobile)) {
            $authInfo = (new \Model\Member\Member())->getAuthByIdentifier($mobile, MemberConst::AUTH_MOBILE);
            if (!$authInfo) {
                return $this->apiReturn(204, [], '未找到账号信息');
            }
            //客户id
            $customerId = $authInfo['customer_id'];

            //根据指定来源判断是否需要选择角色
            $selectTypes = $loginBiz->autoJudgeRole(MemberConst::PAGE_MALL);
            $roleAccount = $loginBiz->getTheRoleAccount($customerId, $selectTypes);
            if (!$roleAccount) {
                return $this->apiReturn(204, [], '相关角色账号获取失败');
            }
        } else {
            $roleAccount = $mobile;
        }

        $res = $loginBiz->loginByPasswd($roleAccount, $password, MemberConst::PAGE_MALL, $roleType, $authCode, false,
            $supplyId = 0);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }

        //获取公众id
        $aid          = $this->_supplyId;
        $memberInfo   = $this->getLoginInfo();
        $memberId     = $memberInfo['memberID'];
        $wxMemberMode = new WxMember();
        //查询当前登录账号是否绑定了微信号
        $bindWxInfo = $wxMemberMode->getDataByFiaAid($memberId, $aid);
        //更新session中的opeaid

        $_SESSION['openid'] = (empty($bindWxInfo) || !$bindWxInfo['fromusername']) ? I('weixinOpen') : $bindWxInfo['fromusername'];
        //如果账号未绑定则直接绑定session中的微信
        if (empty($bindWxInfo)) {
            //获取APPID
            $wxOpenModel = new \Model\Wechat\WxOpen();
            $appInfo     = $wxOpenModel->getAppId($aid);
            $appId       = $appInfo['appid'];

            $wxMemberMode->bindWechat($memberId, $aid, $_SESSION['openid'], $appId);
        }

        (new BusinessCache())->setBusinessCache(['from_supply' => $this->_supplyAccount], $memberId);
        $this->_bindWechat();
        $mType = $this->_getMemberType();
        //获取用户更高级的权限
        $memberMode = new Member();
        //判断登录账号是否为手机号
        if (Helpers::ismobile($mobile)) {
            $customerId = $memberMode->getAuthByIdentifier($mobile, MemberConst::AUTH_MOBILE);  //获取用户id
        } else {
            $customerId = $memberMode->getMemberInfo($mobile, 'account', 'customer_id');
        }
        $selectTypes = $loginBiz->getAutoJudgeRole(MemberConst::PAGE_MICROPLAT);           //获取需要选择的角色列表
        $roleList    = $memberMode->getMultiRoleInfo($customerId['customer_id'], $selectTypes);    //获取对应角色的用户列表

        //当用户角色只有一个，且角色类型为散客时，不做其他判断
        if (count($roleList) == 1 && $roleList[0]['dtype'] == 5) {
            $mType = 5;
        } else {
            foreach ($roleList as $key => $value) {
                if ($value['dtype'] == 0 && $value['id'] == $this->_supplyId) {
                    $mType = $value['dtype'];
                    break;
                } elseif ($value['dtype'] == 1) { //当有分销商身份时
                    //查看该分销商角色是否在该公众号下
                    $memberRelationBiz = new \Business\Member\MemberRelation();
                    $res               = $memberRelationBiz->getMemberDistributionInfoToJava($this->_supplyId,
                        $value['id'], 'id', 0);
                    if ($res) {
                        $mType = $value['dtype'];
                    }
                } else {
                    $mType = 5;
                }
            }
        }

        //没有散客身份, 创建一个散客身份的角色
        $roleList = $this->_createRoleForTourist($customerId['customer_id'], $roleList);

        //上一轮判断好，类型还为5的情况下，判断用户跟公众号是否有营销关系
        if ($mType == 5) {
            $allDis = new \Model\Mall\AllDis();
            foreach ($roleList as $key => $value) {
                $bz = new \Business\Mall\AllDis();
                if ($value['dtype'] == 5) {
                    //判断用户是否为营销员
                    $bz->checkMemberAndRecommend($value['id'], $value['dname'], $value['account'],
                        $_SESSION['supply_openid'], $this->_supplyId, $this->_supplyAppId);
                    $type = $allDis->getAllDisRecommendInfo($value['id'], $this->_supplyId);
                    if ($type) {
                        $mType = -2;
                    }

                }
            }
        }
        //平台没用到 是之前微商城那边用的
        $this->apiReturn(200, ['type' => $mType], '登录成功');
    }

    /**
     * 创建散客身份的角色（有其他角色，但没有散客身份的，创建一份散客身份）==== 微商城
     *
     * <AUTHOR>
     * @date    2019/05
     *
     * @param $touristType
     * @param $customerId
     * @param $roleList
     *
     * @return bool
     */
    private function _createRoleForTourist($customerId, $roleList)
    {
        //获取所有角色中的用户，是否有散客类型， 1 有散客类型， 0 没有散客类型
        $touristType = 0;
        foreach ($roleList as $item) {
            if ($item['dtype'] == 5) {
                $touristType = 1;
            }
        }

        //有游客类型，则无需再创建
        if (!$touristType) {
            //用户名称
            $dname = $roleList[0]['dname'];

            $openId = $_SESSION['openid'];
            //不是生产环境，就随机openid
            if (ENV != 'PRODUCTION') {
                $openId = substr(str_shuffle('bnxeyosvgjhdfprmizaltucqkw'), 0, 28);
            }

            $request = [
                'name'        => $dname ?: '游客',
                'passwd'      => 'pft@12301',
                'customer_id' => $customerId,
                'type'        => MemberConst::ROLE_TOURIST,
                'status'      => MemberConst::STATUS_NORMAL,
                'identifier'  => $openId,
                'info_source' => MemberConst::INFO_WECHAT,
                'page_source' => MemberConst::PAGE_MALL,
            ];

            //创建游客角色
            $registerAction = new RegisterAction();
            $res            = $registerAction->register((object)$request);

            $memberMode = new Member();
            //获取角色游客身份
            $tourist = $memberMode->getMultiRoleInfo($customerId, [5]);    //获取游客角色的用户列表

            if (empty($tourist) || $res['code'] != 200) {
                @pft_log('login/tourist',
                    "====前提：没有散客身份, 创建一个散客身份的角色。customerId: $customerId , 创建失败打印: " . json_encode($res,
                        JSON_UNESCAPED_UNICODE), 'month');
            }
            $roleList = array_merge($roleList, $tourist);
        }

        return $roleList;
    }

    /**
     * 编码openid和appid
     * <AUTHOR>
     * @time   2017-07-10
     *
     * @param  string  $openid
     * @param  string  $appid
     * @param  string  $expire  有效期 单位秒 [2位长度的字符串：01~99]
     */
    private function _encodeWxAuth($openid = '', $appid = '', $expire = '10')
    {
        $string = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $head   = substr(str_shuffle($string), 0, 5);

        return base64_encode($head . $openid . $appid . time() . $expire);
    }

    /**
     * 自动登录
     * <AUTHOR>
     * @time   2017-07-10
     *
     *  http://124078.12301.local/r/Member_Login/autoLogin?plat=c
     *  http://124078.12301.local/r/Member_Login/autoLogin?plat=b
     */
    public function autoLogin()
    {
        // 已登录，则去相应平台
        $this->toPlat();

        // 参数传递
        $param = [];

        $param['plat']     = $this->_formPlat;
        $param['bark_url'] = $this->_brakUrl ?: null;

        //回跳地址判断，判断是回跳等到微平台地址还是wx地址
        //wx地址主要是用来支付的
        $isWxPayPage =  I('is_wx_pay_page', 0);

        if ($this->_formPlat == 'b') {
            //B端业务
            //三亚的需要特殊处理下
            if ($this->_supplyAccount == SELF::__DEFAULT_SY_ACCOUNT) {
                $tmpJumpAccount = SELF::__DEFAULT_SY_ACCOUNT;
            } else {
                //除了三亚的  其他全走统一配置
                $tmpJumpAccount = 'm';
                //微平台登录切到单点登录
                $backUrl = SSO_DOMAIN . '/mobile/#/login?redirect=';
                $params = '';
                if ($param && is_array($param)) {
                    $params = '&' . http_build_query(['back_url'=> $param['bark_url'], 'plat' => $param['plat']]);
                }
                //登入之前地址
                $domain = str_replace('wx', $tmpJumpAccount, MOBILE_DOMAIN);
                $callBackUrl = \Business\Member\MemberWx::__LOGIN_MICRO_PLAT_SSO_CALLBACK_URL__;
                $backUrl = $backUrl . urlencode($domain . $callBackUrl . $params);

                // 单点登录页
                $this->_redirect($tmpJumpAccount, 'login', $backUrl);
            }
        } else {
            //C端业务
            $tmpJumpAccount = $this->_supplyAccount;
        }

        //登入之前地址
        if (!is_null($param['bark_url'])) {
            $param['bark_url'] = substr($param['bark_url'], 1);

            if ($isWxPayPage) {
                $domain = MOBILE_DOMAIN;
            } else {
                $domain = str_replace('wx', $tmpJumpAccount, MOBILE_DOMAIN);
            }

            $param['bark_url'] = $domain . $param['bark_url'];
        }

        if (I('code')) {
            $params = json_decode(base64_decode(I('state')), true);
            $appid  = $params['appid'];

            try {
                $authBiz = new Authorization($appid);
                $auth    = $authBiz->parseAuthInfo(I('code'));
                if ($auth['code'] != 200) {
                    $this->_redirect($tmpJumpAccount, 'login', null, ['bark_url' => $param['bark_url']]); // 登录页
                }
            } catch (\Exception $e) {
                @pft_log("login/error", "授权失败" . $e->getMessage());
                $this->_redirect($tmpJumpAccount, 'login', null, ['bark_url' => $param['bark_url']]); // 登录页
            }
            $openid = $auth['data']['openid'];

            $param['oatoken'] = $this->_encodeWxAuth($openid, $appid);

            $loginRes                 = $this->_bus->loginWx($appid, $openid); // 200 204 400
            $_SESSION['openid']       = $openid;
            $_SESSION['wechat_appid'] = $appid;

            if ($loginRes['code'] == 200) {
                $barkUrl = $param['bark_url'];
                unset($param['bark_url']);
                // 登录成功 判断类型 跳转相应平台
                $dtype = $this->_getMemberType();

                if ($this->_formPlat == 'c' || $dtype == 5) {
                    // 散客去微商城
                    $this->_redirect($params['supplyAccount'], 'c', null, $param);
                } else {
                    // 其他去微平台
                    $this->_redirect($tmpJumpAccount, 'b', $barkUrl, $param);
                }
            } else {
                //如果openid没有绑定的话，登录失败 -> 直接跳转到登录页面
                $this->_redirect($tmpJumpAccount, 'login', null, $param);
            }
        } else {
            // 微信中
            if ($this->_inWechatApp()) {
                $params['supplyAccount'] = $this->_supplyAccount;
                $params['supplyId']      = $this->_supplyId;
                $params['appid']         = $this->_supplyAppId;

                $callback = MOBILE_DOMAIN . 'api/index.php?' . $_SERVER['QUERY_STRING'];

                try {
                    $busWechat = new Authorization($params['appid']);
                    $busWechat->requestForAuth($callback, $params);
                } catch (\Exception $e) {
                    // 授权异常，兜底进去登录页面
                    $this->_redirect($tmpJumpAccount, 'login', null, $param);
                }
            } else {
                // 登录页
                $this->_redirect($tmpJumpAccount, 'login', null, $param);
            }
        }
    }

    /**
     * 获取图形验证码
     * <AUTHOR>
     * @time   2017-07-10
     */
    // http://124078.12301.local/r/Member_Login/getImgCode
    // http://wx.12301.local/api/index.php?c=MicroPlat_Member&a=getImgCode
    public function getImgCode()
    {
        (new PcLogin)->getCode();
    }

    /**
     * 校验图形验证码
     * <AUTHOR>
     * @time   2017-07-10
     *
     * @param  string  $authCode  图形验证码
     *
     * @return bool   [校验成功true|校验失败false]
     */
    public function checkImgCode($authCode = '')
    {
        $sessionKey = $_SERVER['HTTP_SESSION_KEY'] ? $_SERVER['HTTP_SESSION_KEY'] : I('sessionKey');
        if ($sessionKey) {
            $wechatSmallApp = new \Library\Business\WechatSmallApp();
            $sessionCode    = $wechatSmallApp->getSession($sessionKey, "auth_code");
            if ($sessionCode == strtolower($authCode)) {
                $wechatSmallApp->setSession($sessionKey, []);

                return true;
            }

            return false;
        }

        $clientCode = strtolower($authCode);
        $serverCode = I('session.auth_code', '', 'strtolower');
        if ($authCode && $clientCode == $serverCode) {
            return true;
        } else {
            if ($serverCode) {
                unset($_SESSION['auth_code']);
            }

            return false;
        }
    }

    /**
     * 跳转相应平台
     * <AUTHOR>
     * @time   2017-07-10
     */
    private function toPlat()
    {
        if ($this->_hasLogin()) {
            $this->_redirect($this->_supplyAccount, $this->_formPlat);
        }

        return true;
    }

    /**
     * 是否已登录
     * <AUTHOR>
     * @time   2017-07-10
     *
     * @return 已登录true|未登录false
     */
    private function _hasLogin()
    {
        $sid = $this->isLogin('auto', false);
        if (!$sid) {
            return false;
        } else {
            $this->_sid = $sid;

            return true;
        }
    }

    /**
     * 账号类型判断
     * <AUTHOR>
     * @time   2017-07-10
     *
     * @return -1无类型|账号类型
     */
    private function _getMemberType()
    {
        $this->_getLoginInfo();

        if (!isset($this->_loginInfo['dtype'])) {
            return -1;
        }

        // 散客 - 微商城
        if ($this->_isSanKeLogin()) {
            return 5;
        }

        // 其他类型 - 微平台
        if (isset($this->_loginInfo['dtype'])) {
            return $this->_loginInfo['dtype'];
        }
    }

    /**
     * 页面跳转
     * <AUTHOR>
     * @time   2017-01-23
     *
     * @param  int  $supplyAccount  供应商账号
     * @param  string  $redirect  预设地址
     * [
     *     c ：微商城 [默认]
     *     b : 微平台
     *     login ：通用登录页
     * ]
     * @param  string  $url  指定优先的urL [必须是完整的URL，如http://www..]
     * @param  array  $params  get参数数组   [例如:['ctx'=>0,'ctype'=>1,....]]
     *
     * @return 跳转页面
     */
    private function _redirect($supplyAccount = '124078', $redirect = 'c', $url = null, $params = [])
    {
        if (!is_null($url)) {
            header("Location:$url");
            exit();
        }

        $domain = str_replace('wx', $supplyAccount, MOBILE_DOMAIN);

        switch ($redirect) {
            case 'login':
                $uri = 'wx/login.html'; // 微平台登录页
                break;
            case 'b':
                $uri = 'wx/b/usercenter.html'; // 微平台个人中心
                break;
            case 'c': // no break
            default:
                $uri = 'wx/c/index.html'; // 微商城首页
                break;
        }

        $param = '';
        if ($params && is_array($params)) {
            $param = '?' . http_build_query($params);
        }

        header('Location:' . $domain . $uri . $param);
        exit();
    }

    /**
     * 是否散客账号登录
     * <AUTHOR>
     * @time   2017-07-10
     *
     * @return true=散客|false=非散客或未登录
     */
    private function _isSanKeLogin()
    {
        if ($this->_loginInfo['dtype'] == 5 ||
            ($this->_loginInfo['dtype'] == 1 &&
             $this->_loginInfo['mobile'] == $this->_loginInfo['account']
            )) {
            // 散客账号
            return true;
        }

        return false;
    }

    /**
     * 是否在微信app内
     * <AUTHOR>
     * @time   2017-07-10
     */
    private function _inWechatApp()
    {
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        $pos        = strpos($user_agent, 'MicroMessenger');
        if (in_array(ENV, ['PRODUCTION', 'TEST'])) {
            return $pos === false ? false : true;
        } else {
            return true;
        }
    }

    /**
     * 绑定微信
     * <AUTHOR>
     * @date   2017-07-10
     */
    private function _bindWechat()
    {
        $this->_getLoginInfo();
        if ($this->_inWechatApp() && $this->_loginInfo['openid']) {
            // 登录成功, 绑定微信账号
            return (new \Model\Wechat\WxMember())->bindWechat(
                $this->_loginInfo['memberID'],
                $this->_supplyId,
                $this->_loginInfo['openid'],
                $this->_loginInfo['wechat_appid']
            );
        }

        return true;
    }

    /**
     * 登录参数增加
     * <AUTHOR>
     * @time   2017-07-10
     *
     * @param  string  $field  参数名
     * @param  string  $value  参数值
     */
    private function _setLoginInfo($field = '', $value = '')
    {
        $_SESSION[$field]         = $value;
        $this->_loginInfo[$field] = $value;
        $this->_getLoginInfo();
    }

    /**
     * 登录信息
     * <AUTHOR>
     * @time   2017-07-10
     */
    private function _getLoginInfo()
    {
        $loginMember      = is_array($this->_loginInfo) ? $this->_loginInfo : [];
        $session          = is_array($_SESSION) ? $_SESSION : [];
        if (!empty($session['memberID'])) {
            $loginInfoCacheKey = sprintf(\Library\Controller::LOGIN_INFO_CACHE_KEY, $session['memberID']);
            $cache = \Library\Cache\Cache::getInstance('redis');
            $loginInfo = $cache->hGetAll($loginInfoCacheKey);

            $this->_loginInfo = array_merge($loginInfo,$loginMember,$session);
        }else{
            $this->_loginInfo = [];
        }

        return true;
    }

    /**
     * 解析当前链接所属供应商的信息[memberID, account, appid]
     * <AUTHOR>
     * @time   2017-07-10
     */
    private function _getSupplyInfo()
    {
        $this->_supplyAccount = $this->parseSupplyAccount();
        if (!$this->_supplyAccount) {
            exit('链接有误, 无法获取供应商!');
        }

        $this->_supplyId = $this->parseSupplyMemberId();

        if ($this->_inWechatApp()) {
            if (ENV == 'TEST') {
                $this->_supplyAppId = PFT_WECHAT_APPID;
            } else {
                $this->_supplyAppId = $this->parseSupplyAppId();
            }
        }

        return true;
    }

    /**
     * 解析当前链接所属供应商的账号
     * <AUTHOR>
     * @time   2017-07-10
     */
    private function parseSupplyAccount()
    {
        // 授权账号
        if (I('code')) {
            $params = json_decode(base64_decode(I('state')), true);

            return $params['supplyAccount'];
        }

        $host_info = explode('.', $_SERVER['HTTP_HOST']);

        if (in_array($host_info[0], ['wx', SELF::__PFT_ACCOUNT__, 'm'])) {
            return SELF::__PFT_ACCOUNT__;
        }

        return \safetxt($host_info[0]);
    }

    /**
     * 解析当前链接所属供应商的Appid
     * <AUTHOR>
     * @time   2017-07-10
     */
    private function parseSupplyAppId()
    {
        $account = $this->parseSupplyAccount();

        if ($account == SELF::__PFT_ACCOUNT__) {
            return PFT_WECHAT_APPID;
        }

        if (!$this->_loginInfo['wechat_appid']) {
            $wxOpenModel = new \Model\Wechat\WxOpen();
            $wxInfo      = $wxOpenModel->getWechatOffiAccInfo($account, 'account');
            $appid       = $wxInfo['appid'];
        } else {
            $appid = $this->_loginInfo['wechat_appid'];
        }

        return $appid ?: false;
    }

    /**
     * 解析当前链接所属供应商的ID
     * <AUTHOR>
     * @time   2017-07-10
     */
    private function parseSupplyMemberId()
    {
        $account = $this->parseSupplyAccount();

        if ($account == SELF::__PFT_ACCOUNT__) {
            return SELF::__PFT_MEMBERID__;
        }

        $member = $this->_getMemberModel()->getMemberInfo($account, 'account', 'id');

        return $member['id'] ?: 0;
    }

    /**
     * 获得Member模型实例
     * <AUTHOR>
     * @date   2016-11-10
     */
    private function _getMemberModel()
    {
        static $model;
        if (!$model) {
            $model = new \Model\Member\Member();
        }

        return $model;
    }

    // private function _sessionKeep()
    // {
    //     ini_set('session.use_trans_sid', 0); //设置垃圾回收最大生存时间
    //     ini_set('session.gc_maxlifetime', 13600); //使用 COOKIE 保存 SESSION ID 的方式
    //     ini_set('session.use_cookies', 1);
    //     ini_set('session.cookie_path', '/'); //多主机共享保存 SESSION ID 的 COOKIE
    //     ini_set("session.cookie_domain", '.12301.local');
    //     session_start();
    // }
    /**
     * 微平台获取账号角色
     * <AUTHOR>
     * @date   2020-10-19
     */
    public function getRoleByaccount()
    {
        $account = I('account');
        $buz     = new \Business\Member\Login();
        $result  = $buz->getRoleByaccount($account, MemberConst::PAGE_MICROPLAT);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 生成图形验证码 (登录输入图形验证码，点击图片刷新用)
     * <AUTHOR>
     * @date   2021-02-22
     */
    public function getImgVerifyCode()
    {
        $account = I('account', '', 'strval,trim');
        $type    = I('type', 1, 'intval');

        if (!$account) {
            $this->apiReturn(203, [], '请输入账号');
        }

        if (!in_array($type ,[1,2])) {
            $this->apiReturn(203, [], '生成图形验证码类型参数错误');
        }
        $result  = (new verifyCode())->getImgVerifyCode($account, $type);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
    /**
     * 发送登录二次验证码
     * <AUTHOR>
     * @date   2021-04-16
     */
    public function sendTwoCheckCode()
    {
        //手机号
        $mobile = I('mobile', '', "strval");
        if (empty($mobile)) {
            $this->apiReturn(203, [], "参数错误");
        }
        if (!ismobile($mobile)) {
            $this->apiReturn(203, [], "手机号格式错误！");
        }
        //短信发送成功缓存短信验证码
        /**
         * @var $redis \Library\Cache\CacheRedis
         */
        $key =   sprintf(LoginKeyConst::LOGIN_TWO_CHECK_CODE_KEY,$mobile);
        $redis    = Cache::getInstance('redis');
        $codeTime = $redis->get($key);
        if ($codeTime) {
            [$code, $time] = explode('|', $codeTime);
            if ($_SERVER['REQUEST_TIME'] - $time < 60) {
                $this->apiReturn(201, [], '短信发送间隔低于60秒');
            }
        }
        //限制每个手机号每天的的短息获取次数
        $smsLockKey = "twoLoginCheckCodeTimes:$mobile";
        $result     = $redis->get($smsLockKey);
        if ($result >= 10) {
            $this->apiReturn(401, [], '短信获取次数超过当天可获取的次数！');
        }
        // if (ENV != 'PRODUCTION') {
        //     $code = 123456;
        // }else{
        //     $code = genRandomNum(6);
        // }
        $code = genRandomNum(6);
        $messageServiceApi = Container::pull(MessageService::class);
        [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'VCODE', $mobile,
            ['登录二次验证', $code], 0, '', '验证码');
        if ($approval) {
            if (!EnvUtil::isRelease()) {
                pft_log('message_service', json_encode(['验证码', __METHOD__, [$mobile, ['登录二次验证', $code]], $res], JSON_UNESCAPED_UNICODE));
            }
        } else {
            /** @deprecated 放量结束后删除 */
            $smsMode = SmsFactory::getFactory($mobile);
            $res = $smsMode->veifyCode('登录二次验证', $code);
            if (!EnvUtil::isRelease()) {
                pft_log('message_service', json_encode(['验证码.old', __METHOD__, [$mobile, ['登录二次验证', $code]], $res], JSON_UNESCAPED_UNICODE));
            }
        }
        //发送短信验证码
        if ($res['code'] == 200) {
            $cache = $code . '|' . $_SERVER['REQUEST_TIME'];
            $redis->set($key, $cache, '', 300);
            pft_log('sms/data', "{$mobile}登录二次验证发送的短信验证码为{$code}");
            if (empty($result)) {
                $msg = "验证码发送成功，请注意查收！";
                //计算今天剩余的时间
                $time = 86400 - (time() + 8 * 3600) % 86400;
                $redis->set($smsLockKey, 1, '', $time);
            } else {
                $redis->incrBy($smsLockKey, 1);
                $msg = "验证码发送成功，上一条短信验证码已失效，请注意查收！";
            }
            $this->apiReturn(200, [], $msg);
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
    }

    /**
     * 微平台单点登录回跳处理
     * <AUTHOR>
     * @date   2022/6/23
     *
     */
    public function LoginMicroPlatBySSO()
    {
        $token   = I('get.token');
        $backUrl = I('get.back_url', '');
        if (!$token) {
            pft_log('sso/micro_plat/fail', 'token错误');
            $backUrl = SSO_DOMAIN . "/mobile/#/login?login_error=true";
            header("Location: " . $backUrl);
            exit;
        }

        //token解析获取用户基础信息
        $ssoClient = new SSOLoginBiz();
        $callRes   = $ssoClient->getMemberByToken($token);
        if ($callRes['code'] != 200 || empty($callRes['data'])) {
            pft_log('sso/micro_plat/fail', json_encode($callRes, JSON_UNESCAPED_UNICODE));
            $backUrl = SSO_DOMAIN . "/mobile/#/login?login_error=true";
            header("Location: " . $backUrl);
            exit;
        }

        $callData = $callRes['data'];

        //扩展信息组合， 微信相关信息
        $extraInfo = [];
        if (isset($callData['metadata']['wechat'])) {
            $wechat = $callData['metadata']['wechat'];
            if (!empty($wechat['openId'])) {
                $extraInfo['openid'] = $wechat['openId'];
                $_SESSION['openid']  = $wechat['openId'];
            }
            if (!empty($wechat['appId'])) {
                $extraInfo['wechat_appid'] = $wechat['appId'];
                $_SESSION['wechat_appid']  = $wechat['appId'];
            }
        }
        if ($callData['metadata']['linkIds']) {
            $extraInfo['sso_metadata_link_ids'] = json_encode($callData['metadata']['linkIds']);
        }

        //写session TODO::这边登录信息全部标记wx
        $sessionBiz = new Session();
        $sessionRes = $sessionBiz->loginByMobile($callData['id'], $extraInfo);
        if ($sessionRes['code'] != 200) {
            pft_log('sso/micro_plat/fail', json_encode($sessionRes, JSON_UNESCAPED_UNICODE));
            $backUrl = SSO_DOMAIN . "/mobile/#/login?login_error=true";
            header("Location: " . $backUrl);
            exit;
        }

        $loginData = $sessionRes['data'];

        (new BusinessCache())->setBusinessCache(['from_supply' => $this->_supplyAccount], $loginData['memberID']);
        //在session中标记当前session中所存信息用于何处：1微平台，平台，0微商城
        $_SESSION['ForWhere'] = 1;

        //处理backurl
        $backUrl = $this->_handleBackurl($loginData['memberID'], $backUrl);

        $backUrl = $backUrl ? urldecode($backUrl) : "/wx/b/usercenter.html";
        if ($backUrl) {
            $domain = str_replace('wx', 'm', MOBILE_DOMAIN);
            $backUrl = $domain . trim($backUrl, '/');
        }

        // if (strpos($backUrl, '?') === false) {
        //     $backUrl .= '?bylogin=1';
        // }else{
        //     $backUrl .= '&bylogin=1';
        // }
        header("Location: " . $backUrl);
        exit;
    }

    /**
     * 处理回跳的url，有些特殊场景可以指定到某个url
     * <AUTHOR>
     * @date   2022/7/7
     *
     * @param  int     $memberId    用户id
     * @param  string  $backUrl     返回url
     *
     * @return string
     */
    private function _handleBackurl(int $memberId = 0, string $backUrl = '')
    {
        if (!$memberId) {
            return $backUrl;
        }

        $chartUrl = "/wx/b/chart.html"; //图表分析url

        //盘山特殊处理 微平台领导看数据，需要直接跳转图表分析（前提是要开启图表分析这个应用）
        $microPlatReportAccount = load_config('micro_plat_report_account_test', 'account'); // 测试环境账号
        if (ENV == 'PRODUCTION') {
            $microPlatReportAccount = load_config('micro_plat_report_account', 'account');
        }
        //当前用户账号或者主账号只要有一个在，则返回true
        if (in_array($memberId, $microPlatReportAccount)) {
            $backUrl =  $chartUrl;
        }

        return $backUrl;
    }
}
