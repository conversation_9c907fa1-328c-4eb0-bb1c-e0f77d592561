<?php

namespace Controller\Member;

use Business\JavaApi\Tools\verifyCode;
use Business\Mall\SmallAppConfig;
use Business\Member\MemberBaseInfo;
use Library\Business\CaptchaCode;
use Library\Business\RedisGeo;
use Library\Business\WechatSmallApp;
use Library\Cache\Cache;
use Library\Controller;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Tools\BusinessCache;
use Library\Tools\Vcode;
use Library\Tools\Helpers;
use Model\domain\platform;
use Model\Member\Member as MemberModel;
use Model\Product\Land;
use Business\Member\Member as MemberBiz;
use Business\Member\RegisterAction;
use Library\Constants\MemberConst;

/**
 * @Author: CYQ19931115
 * @Date:   2018-02-01 10:45:59
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2018-04-03 14:37:47
 */
/**
 * 小程序用户数据
 */
class Member extends Controller
{

    private $business;

    public function __construct()
    {
        $this->business = new SmallAppConfig();
    }

    /**
     * 通过scanCode获取用户did
     * <AUTHOR>
     * @dateTime 2018-02-01T11:59:41+0800
     * @throws   \Exception                         可能抛出异常
     * @param    [type]                   $scanCode [code]
     * @return   [type]                             [用户id]
     */
    private function getMemberidByScanCode($scanCode)
    {
        $smallApp = new \Library\Business\WechatSmallApp();
        $memberId = $smallApp->decodeShopCode($scanCode);
        return $memberId;
    }

    /**
     * 获取用户数据
     * <AUTHOR>
     * @dateTime 2018-02-01T12:01:28+0800
     */
    public function getUserInfo()
    {
        $appid    = I('appid');
        $openid   = I('openid');
        $memberId = (new \Model\Wechat\WxMember())->GetWxInfoByOpenid($openid, $appid);
        $memberId = $memberId['aid'];
        if (!$memberId) {
            $this->apiReturn(500, [], "没有相应的用户数据");
        }
        $memModel = new MemberModel('slave');
        $member   = $memModel->getMemberInfo($memberId);

        $cache         = new \Library\Tools\BusinessCache();
        $businessCache = $cache->getBusinessCache($memberId);

        //用户名
        if (isset($businessCache['dname'])) {
            $member['dname'] = $businessCache['dname'];
        }

        //头像
        if (isset($businessCache['headImg'])) {
            $member['headphoto'] = $businessCache['headImg'];
        }

        $return = [
            'name'           => $member['dname'],
            'mobile'         => $member['mobile'],
            'headImg'        => $member['headphoto'],
            'service_mobile' => "1234123423",
        ];
        $this->apiReturn(200, $return);
    }

    /**
     * 解绑手机
     * <AUTHOR>
     * @dateTime 2018-02-12T11:07:17+0800
     * @throws   \Exception                             可能抛出异常
     * @return   [type]                   [description]
     */
    public function unbindPhoneNum()
    {

        $mobile = I('mobile', 0, 'intval');
        $vcode  = I('vcode', '', 'trim');

        $res  = Vcode::verifyVcode($mobile, $vcode, "login_sms");
        $code = $res['code'];
        $msg  = $res['msg'];

        //登录验证码不正确
        if ($code != 200) {
            return $this->apiReturn($code, [], $msg);
        }

        $appid    = I('appid');
        $openid   = I('openid');
        $memberId = (new \Model\Wechat\WxMember())->GetWxInfoByOpenid($openid, $appid);
        $memberId = $memberId['aid'];
        if (!$memberId) {
            $this->apiReturn(500, [], "没有相应的用户数据");
        }
        $memModel = new MemberModel();
        $result   = $memModel->setMemberInfoById($memberId, [
            "mobile" => "",
        ]);
        (new \Model\Wechat\WxMember())->unbindWechat(3385, $memberId, $openid, $appid);
        if ($result) {
            $this->apiReturn(200, [], "解绑成功");
        }
        $this->apiReturn(500, [], "解绑失败");
    }

    /**
     * 绑定电话号码
     * <AUTHOR>
     * @dateTime 2018-02-24T15:39:13+0800
     * @throws   \Exception                             可能抛出异常
     * @return   [type]                   [description]
     */
    public function bindPhoneNum()
    {
        $mobile = I('mobile', 0, 'intval');
        $vcode  = I('vcode', '', 'trim');

        $res  = Vcode::verifyVcode($mobile, $vcode, "login_sms");
        $code = $res['code'];
        $msg  = $res['msg'];

        //登录验证码不正确
        if ($code != 200) {
            return $this->apiReturn($code, [], $msg);
        }

        $openid   = I("openid");
        $appid    = I("appid");
        $memberid = $this->createUserByPhoneNo($mobile);
        if ($memberid) {
            $bindWechat = (new \Model\Wechat\WxMember())->bindWechat(3385, $memberid, $openid, $appid);
            \pft_log("lerko", $bindWechat ? "微信信息绑定成功!$openid $appid " : "微信绑定失败! $openid $appid");
            $this->apiReturn(200, [], "绑定手机号码成功!");
        }
        $this->apiReturn(500, [], "绑定失败!");
    }

    /**
     * 通过手机号码创建用户
     * <AUTHOR>
     * @dateTime 2018-01-24T17:35:50+0800
     * @throws   \Exception                        可能抛出异常
     * @param    [type]                   $phoneNo [description]
     * @return   [type]                            [description]
     */
    public function createUserByPhoneNo($phoneNo)
    {
        $memberBiz = new MemberBiz();
        //客户是否存在
        $customerId = $memberBiz->parseCustomerIdByMobile($phoneNo);
        if ($customerId) {
            //角色是否存在
            $memberModel = new MemberModel('slave');
            $role = $memberModel->getTheRoleInfo($customerId, MemberConst::ROLE_TOURIST, 'id');
            if ($role) {
                return $role['id'];
            }
        }

        $request = [
            'name'        => '游客',
            'passwd'      => substr($phoneNo, 0, 6),
            'type'        => MemberConst::ROLE_TOURIST,
            'identifier'  => $phoneNo,
            'customer_id' => $customerId,
            'info_source' => MemberConst::INFO_MOBILE,
            'page_source' => MemberConst::PAGE_WX_SMALLAPP,
        ];

        $registerAction = new RegisterAction();
        $result = $registerAction->register((object)$request);
        
        return $result['code'] == 200 ? $result['data']['member_id'] : false;
    }

    /**
     * 获取商家列表
     * <AUTHOR>
     * @dateTime 2018-02-01T16:22:20+0800
     */
    public function getLandList()
    {
        $memberId  = I("member_id", 3385);
        $page      = I("page", 1);
        $pageSize  = I("pageSize", 16);

        $landApi  = new \Business\CommodityCenter\Land();
        $landInfo = $landApi->queryLandMultiQueryByAdminAndPaging([], $page, $pageSize, '', 'id desc', false, [$memberId]);
        $result   = [];
        if (!empty($landInfo['list'])) {
            $ptypList = load_config("land_type", "orderSearch");
            foreach ($landInfo['list'] as $value) {
                $result[] = [
                    "id"      => $value['id'],
                    "title"   => $value['title'],
                    "salerid" => $value['salerid'],
                    "imgpath" => $value['imgpath'],
                    "p_type"  => $ptypList[$value['p_type']],
                ];
            }
        }
        $this->apiReturn(200, $result, "列表获取成功");
    }

    public function getLandUserList()
    {
        $data = json_decode(file_get_contents("php://input"), true);

        $longitude = $data['longitude'];
        $latitude  = $data['latitude'];

        $accounts  = ["201107", "201059", "201061", "201213", "201084", "589637", "589640", "201059"];
        $member    = new \Model\Member\Member();
        $userInfos = $member->getMemberInfoByMulti($accounts, "account");
        $fids      = array_column($userInfos, 'id');
        $subModel  = new platform();
        $imageList = $subModel->getListByFids($fids);
        $imageMap  = [];
        foreach ($imageList as $value) {
            $imageMap[$value['fid']] = $value;
        }

        $landModel = new Land();

        $smallApp = new \Library\Business\WechatSmallApp();
        $landApi  = new \Business\CommodityCenter\Land();

        $result   = [];
        foreach ($userInfos as $value) {
            $count = $landApi->queryLandMultiQueryByAdminAndPaging([], 1, 1, '', 'id desc', false, [$value['id']], [1], [], [], [], null, null, true);
            $result[] = [
                "id"       => $value['id'],
                "scanCode" => $smallApp->encodeShopCode($value['id']),
                "dname"    => $imageMap[$value['id']]['p_name'] ? $imageMap[$value['id']]['p_name'] : $value['dname'],
                "image"    => $imageMap[$value['id']]['p_logo'],
                "count"    => empty($count) ? 0 : $count,
                "dist"     => $this->getDist($value['id'], $longitude, $latitude),
            ];
        }
        $this->apiReturn(200, $result, "列表获取成功");
    }

    /**
     * 获取距离
     * <AUTHOR>
     * @dateTime 2018-02-02T16:43:41+0800
     * @throws   \Exception                          可能抛出异常
     * @param    [type]                   $memberId1 [description]
     * @param    [type]                   $memberId2 [description]
     * @return   [type]                              [description]
     */
    private function getDist($memberId1, $longitude, $latitude)
    {
        $geo         = new RedisGeo();
        $locationkey = md5($longitude . $latitude);
        $geo->geoAdd("shopGeoInfo", $longitude, $latitude, $locationkey);
        $dist = $geo->geoDist("shopGeoInfo", $locationkey, $memberId1);
        $geo->remGeoInfo("shopGeoInfo", $locationkey);
        return $dist;
    }

    /**
     * 生成随机长度
     * <AUTHOR>
     * @dateTime 2018-02-12T10:43:53+0800
     * @throws   \Exception                    可能抛出异常
     * @param    [type]                   $len [description]
     * @return   [type]                        [description]
     */
    public function random($len)
    {
        $srcstr = "ABCDEFGHIJKLMNPQRSTUVWXYZ123456789";
        mt_srand();
        $strs = "";
        for ($i = 0; $i < $len; $i++) {
            $strs .= $srcstr[mt_rand(0, 33)];
        }
        return ($strs);
    }

    //生成验证码方法
    public function getCode()
    {
        $sessionKey = I("sessionKey");
        $str        = $this->random(4); //随机生成的字符串
        $width      = 60; //验证码图片的宽度
        $height     = 25; //验证码图片的高度
        //Date in the past
        header("Expires:Mon,26 Jul 1997 05:00:00 GMT");
        //always modified
        header("Last-Modified:" . gmdate("D,d M Y H:i:s") . "GMT");
        //HTTP/1.1
        header("Cache-Control:no-store,no-cache,must-revalidate");
        header("Cache-Control:post-check=0,pre-check=0", false);
        //HTTP/1.0
        header("Pragma:no-cache");
        header("Content-Type:image/png");
        $im   = imagecreate($width, $height);
        $back = imagecolorallocate($im, 0xFF, 0xFF, 0xFF); //背景色
        //$pix=imagecolorallocate($im,187,190,247);        //模糊点颜色
        $font = imagecolorallocate($im, 41, 163, 238); //字体色
        //绘制1000个模糊作用的点
        //mt_srand();
        //for($i=0;$i<1000;$i++) {
        //    imagesetpixel($im,mt_rand(0,$width),mt_rand(0,$height),$pix);
        //}
        imagestring($im, 5, 7, 5, $str, $font); //绘制随机生成的字符串
        //imagerectangle($im,0,0,$width-1,$height-1,$font);//在验证码图像周围绘制1px的边框
        imagepng($im); //建立一张PNG格式图形
        imagedestroy($im); //将图片handle解构，释于内存空间
        $wechatSmallApp = new WechatSmallApp();
        $wechatSmallApp->setSession($sessionKey, ["auth_code" => strtolower($str)]);
    }

    /**
     * 校验电话号码
     * <AUTHOR>
     * @dateTime 2018-03-30T16:41:54+0800
     * @throws   \Exception                             可能抛出异常
     * @return   [type]                   [description]
     */
    public function vlidateVCode(){
        if(ENV=='LOCAL'){
            header("Access-Control-Allow-Origin: *"); // 允许任意域名发起的跨域请求  
        }
        $mobile = I('mobile', 0, 'intval');
        $vcode  = I('vcode', '', 'trim');

        //如果是内网测试环境，不进行验证码的校验
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL', 'TEST'])) {
            $this->apiReturn(200, [], "验证成功!");
        } else {
            $res  = Vcode::verifyVcode($mobile, $vcode, "login_sms");
            $code = $res['code'];
            $msg  = $res['msg'];

            //登录验证码不正确
            if ($code != 200) {
                $this->apiReturn($code, [], $msg);
            }
            $this->apiReturn(200, [], "验证成功!");
        }
    }

    /**
     * 园区卡设置充值优惠活动
     * <AUTHOR>
     * @date 2018-04
     * @param int    type           1 折扣 2 赠送 3 不优惠
     * @param string name           配置标题
     * @param string begin_time     开始时间 2018-04-30
     * @param string end_time       结束时间 2018-04-30
     * @param string recharge_money 充值金额 单位 ： 元
     * @param string deliver_money  赠送金额 单位 ： 元
     * @param string discount       折扣 9.8折 则9.8
     * @param int deposit       押金
     * @return string | json
     *
     */
    public function setRechargeConf()
    {
        // 获取参数
        $name          = I('name');
        $type          = I('type');
        $beginTime     = I('begin_time');
        $endTime       = I('end_time');
        $rechargeMoney = I('recharge_money');
        $deliverMoney  = I('deliver_money');
        $discount      = I('discount');
        $cardType      = I('card_type',0);
        $costMoney     = I('cost_money',0);
        $limitMoney    = I('limit_money',0);
        $depositMoney  = I('deposit',0);
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        $operid    = $loginInfo['memberID'];
        if (!$name || !$operid || !$sid || (!$rechargeMoney && in_array($type,[3,1,2]))) {
            $this->apiReturn(203, [], '参数有误');
        }
        if (!in_array($cardType,[0,1])){
            $this->apiReturn(203, [], '参数有误');
        }
        if ($limitMoney < 0){
            $this->apiReturn(203, [], '限制最少金额出错');
        }
        if ($type == 1) {
            $discount = $discount * 10;
            if ($discount > 99 || $discount < 1) {
                $this->apiReturn(203, [], '折扣方案设置有误');
            }

             // 金额和折扣计算
            if ($rechargeMoney * $discount < 1) {
                $this->apiReturn(203, [], '折扣比例方案设置有误');
            }
        }
        if ($cardType == 1){   //园区一卡通只有不小于占时这样，后面改掉
            $type = 4;
            if ($costMoney < 0 || $depositMoney < 0){
                $this->apiReturn(203, [], '押金或者工本费不能小于0');
            }
        }
        $configBiz = new \Business\CardSolution\Config();
        $setResArr = $configBiz->setRechargeConfig($name, $sid, $operid, $type, $beginTime, $endTime, $rechargeMoney, $deliverMoney, $discount, 0,$cardType,$costMoney, $limitMoney,$depositMoney);

        if ($setResArr['code'] == 200) {
            $this->apiReturn(200, [], '设置成功');
        }

        $this->apiReturn(204, [], '设置失败');
    }

    /**
     * 园区卡获取供应商设置的充值优惠活动
     * <AUTHOR>
     * @date 2018-04
     * @param int $sid             供应商id
     * @param string $beiginTime   起始时间
     * @param string $endTime      结束时间
     * @param string $page         页码
     * @param string $pageSize     每页显示条数
     * @param string $type         1 折扣 2 赠送 3 不优惠   1，2，3 英文逗号分隔
     * @param string $status       0 未生效 1：生效 2：失效
     *
     * @return string | json
     *
     */
    public function getRechargeConfig()
    {
        $sid        = I('sid');
        $beiginTime = I('begin_time', '');
        $endTime    = I('end_time', '');
        $page       = I('page', 1);
        $pageSize   = I('page_size', 10);
        $type       = I('type', '');
        $status     = I('status', '');
        $name       = I('name', '');
        $cardType   = I('get.card_type', -1);
        $loginInfo  = $this->getLoginInfo();
        $sid        = $loginInfo['sid'];
        if (empty($sid)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $configBiz     = new \Business\CardSolution\Config();
        $configListArr = $configBiz->getList($sid, $page, $pageSize, $type, $beiginTime, $endTime, $status, $name, $cardType);

        if ($configListArr['code'] != 200) {
            $this->apiReturn(204, [], '无数据');
        }

        // 计算数据总数
        $numArr =  $configBiz->countNum($sid,$type,$beiginTime,$endTime, $status, $name);

        $data = ['list' => $configListArr['data'], 'num' => $numArr['data']];

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 根据id获取有效的
     * <AUTHOR>
     * @date 2018-04
     * @param int $configId 配置表id
     * @return string | json
     *
     */
    public function getSolutionConfById()
    {
        $configId    = I('config_id');

        if (empty($configId)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $configModel = new \Model\CardSolution\Config();
        $configArr   = $configModel->getConfigById($configId, '*');

        if (empty($configArr)) {
            $this->apiReturn(204, [], '数据有误');
        }

        $configArr['begin_time'] = date('Y-m-d', $configArr['begin']);
        $configArr['end_time']   = date('Y-m-d', $configArr['end']);

        $this->apiReturn(200, $configArr, 'success');
    }

    /**
     * 根据虚拟园区一卡通的配置id更新记录
     * <AUTHOR>
     * @param int $configId  配置id
     * @param int $type      类型 1 折扣 2 赠送 3 不优惠
     * @param string $rechargeMoney  充值金额 单位：元
     * @param string $deliverMoney   赠送金额 单位：元
     * @param string $disCount       折扣  9.8折
     * @param string $beginTime      开始时间
     * @param string $endTime        结束时间
     * @param string $name           配置标题
     * @param string $status         0：待生效 1：已生效 2：已失效
     * @date 2018-04
     * @return string | json
     *
     */
    public function upSolutionConfById()
    {
        $configId      = I('config_id');
        $type          = I('type', '');
        $rechargeMoney = I('recharge_money', '');
        $deliverMoney  = I('deliver_money', '');
        $disCount      = I('discount', '');
        $beginTime     = I('begin_time', '');
        $endTime       = I('end_time', '');
        $name          = I('name', '');
        $status        = I('status', '');
        $cardType      = I('card_type');
        $costMoney     = I('cost_money','');
        $limitMoney    = I('limit_money','');
        $deposit       = I('deposit','');

        if (empty($configId)) {
            $this->apiReturn(203, [], '参数有误');
        }

        if (!in_array($cardType,[0,1])){
            $this->apiReturn(203, [], '类型错误');
        }
        if ($rechargeMoney != '' && $rechargeMoney < 0 && $type != 4 ) {
            $this->apiReturn(203, [], '充值金额设置有误');
        }

        if ($deliverMoney != '' && $deliverMoney < 0 && $type == 2) {
            $this->apiReturn(203, [], '赠送金额设置有误');
        }
        if ($disCount != '' && $type == 1) {
            $tmpDiscount = $disCount * 10;
            if ($tmpDiscount > 99 || $tmpDiscount < 1) {
                $this->apiReturn(203, [], '折扣方案设置有误');
            }
            // 金额和折扣计算
            if ($rechargeMoney * $tmpDiscount < 1) {
                $this->apiReturn(203, [], '折扣比例方案设置有误');
            }
        }
        if ($type == 4 && $cardType == 1){
            if ($costMoney < 0 || $limitMoney < 0){
                $this->apiReturn(203, [], '工本费或限制金额无效');
            }
        }
        $loginInfoArr = $this->getLoginInfo();

        $configModel  = new \Model\CardSolution\Config();
        $configArr    = $configModel->getConfigById($configId, '*');

        if ($loginInfoArr['sid'] != $configArr['sid']) {
            $this->apiReturn(203, [], '无权限修改');
        }

        if ($beginTime) {
            $beginTime = date('Y-m-d', strtotime($beginTime)) . ' 00:00:00';
            $beginTime = strtotime($beginTime);
        }

        if ($endTime) {
            $endTime = date('Y-m-d', strtotime($endTime)) . ' 23:59:59';
            $endTime = strtotime($endTime);
        }

        $data  = [
            'type'           => $type,
            'recharge_money' => $rechargeMoney ? $rechargeMoney * 100 : '',
            'deliver_money'  => $deliverMoney ? $deliverMoney * 100 : '',
            'discount'       => $disCount ? $disCount * 10 : '',
            'operate_id'     => $loginInfoArr['memberID'],
            'begin'          => $beginTime,
            'end'            => $endTime,
            'name'           => $name,
            'status'         => $status,
            'card_type'      => $cardType,
            'cost_money'     => is_numeric($costMoney) ? $costMoney * 100 : '',
            'limit_money'    => is_numeric($limitMoney) ? $limitMoney * 100 : '',
            'deposit'        => is_numeric($deposit) ? $deposit * 100 : '',
        ];
        if ($cardType === 0){
            $data['deposit'] = 0;
        }

        foreach ($data as $key => $value) {
            if ($value ===  '') {
                unset($data[$key]);
            }
        }

        // 设置状态得时候，如果是已失效  不让修改
        if (isset($data['status']) && $configArr['status'] == 2) {
            $this->apiReturn(203, [], '已失效记录不可修改状态');
        }

        if (isset($data['status']) && $configArr['status'] == 1 && $data['status'] == 0) {
            $this->apiReturn(203, [], '已经生效记录不可修改未生效');
        }

        // 不是设置配置状态得时候 。记录不是未生效 不让修改
        if (!isset($data['status']) && $configArr['status'] != 0) {
            $this->apiReturn(203, [], '非未生效记录不可修改配置');
        }

        $upRes = $configModel->updateConfigId($configId, $data);

        if ($upRes) {
            $this->apiReturn(200, [], '更新成功');
        }

        $this->apiReturn(204, [], '更新失败');
    }

    /**
     * 小程序码生成
     * <AUTHOR>
     * @date   2018-04-15
     */
    public function createSmallAppQrCode() {
        $this->apiReturn(400, [], "抱歉，该功能已下线");
        $loginInfo   = $this->getLoginInfo();
        $account     = I('post.account', 123933);
        $memberId    = $loginInfo['sid'];
        // 单独的客户小程序
        if ($memberId == 915059) {
            $account = $loginInfo['saccount'];
        };

        $path        = I("path", "pages/login/login");
        $configModel = new \Business\CardSolution\Config();
        $data        = $configModel->createSmallAppQrCode($memberId, $path, $account);
        $code        = $data['code'];
        $data        = $data['url'];
        $this->apiReturn($code, $data);
    }

    /**
     * 添加园区信息
     * <AUTHOR>
     * @date 2018-04
     * @param string $phoneNum  手机号
     * @return string | json
     *
     */
    public function addCardSolutionRoutine()
    {
        $phoneNum = I('phone_num');
        if (empty($phoneNum)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $loginInfoArr = $this->getLoginInfo();

        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $sid = $loginInfoArr['sid'];

        $cardConfigModel = new \Model\CardSolution\Config();
        $cardRoutineInfo = $cardConfigModel->getRoutineInfo($sid, 'id, phone_num');

        $data = [
            'sid' => $loginInfoArr['sid'],
            'phone_num' => $phoneNum
        ];

        $res = false;
        if ($cardRoutineInfo) {
            // 走更新操作
            $res = $cardConfigModel->upRoutineInfo($sid, $data);
        } else {
            $res = $cardConfigModel->insertRoutineData($data);
        }

        if ($res) {
            $this->apiReturn(200, [], '添加成功');
        }
        $this->apiReturn(204, [], '添加失败');
    }

    /**
     * 查询虚拟卡园区配置信息
     * <AUTHOR>
     * @date 2018-04
     * @return string | json
     * 
     */
    public function getCardRoutineInfo()
    {
        $loginInfoArr = $this->getLoginInfo();

        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $cardConfigModel = new \Model\CardSolution\Config();
        $cardRoutineInfo = $cardConfigModel->getRoutineInfo($loginInfoArr['sid'], 'id, phone_num');

        if ($cardRoutineInfo) {
            $this->apiReturn(200, $cardRoutineInfo, 'success');
        }

        $this->apiReturn(204, [], '无数据');
    }

    /**
     * 园区获取园区下园区卡用户
     * <AUTHOR>
     * @date   2018-04
     * @param string $beginTime  开始时间
     * @param string $endTime    结束时间
     * @param int    $page       页码
     * @param int    $pageSize   每页显示数量
     * @param string $phoneNum   手机号
     * @param string $cardNum    卡号
     * @return string | json
     *
     */
    public function getCardSolutionMember()
    {
        $beginTime = I('begin_time', '');
        $endTime   = I('end_time', '');
        $page      = I('page', 1);
        $pageSize  = I('page_size', 10);
        $phoneNum  = I('phone_num', '');
        $cardNum   = I('card_num', '');

        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }
        $memberBiz = new \Business\CardSolution\Member();
        $memberArr = $memberBiz->getCardMeberFidInfoBySid($loginInfoArr['sid'], $beginTime , $endTime, $page, $pageSize, $phoneNum, $cardNum);

        if ($memberArr['code'] == 200) {
            $this->apiReturn(200, $memberArr['data'], '获取数据成功');
        }
        $this->apiReturn(203, [], '无数据');
    }


    /**
     * 管理员注册mini云用户
     * <AUTHOR>
     * @date 2018-07-24
     */
    public function registerMiniMember()
    {
        // 只有管理员有权限操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限操作');
        }

        $mobile   = I('mobile', '');
        $password = I('password', '');

        if (empty($mobile) || empty($password)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数有误');
        }

        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入正确的手机号码');
        }

        $memberBiz = new MemberBiz();
        //客户是否存在
        $customerId = $memberBiz->parseCustomerIdByMobile($mobile);
        if ($customerId) {
            //角色是否存在
            $memberModel = new MemberModel('slave');
            $role = $memberModel->getTheRoleInfo($customerId, MemberConst::ROLE_APPLY, 'id');
            if ($role) {
                $this->apiReturn(self::CODE_NO_CONTENT, [], '该手机号已存在');
            }
        }

        //注册供应商角色
        $request = [
            'name'          => 'Mini' . $mobile,
            'passwd'        => $password,
            'type'          => MemberConst::ROLE_APPLY,
            'identifier'    => $mobile,
            'account'       => 'Mini' . $mobile,
            'customer_id'   => $customerId,
            'info_source'   => MemberConst::INFO_MOBILE,
            'page_source'   => MemberConst::PAGE_WX_SMALLAPP,
        ];

        $registerAction = new RegisterAction();
        $result = $registerAction->register((object)$request);

        if ($result['code'] == 200) {
            $this->apiReturn(self::CODE_SUCCESS, [], '注册账号成功');
        } else {
            $this->apiReturn(self::CODE_NO_CONTENT, [], $result['msg']);
        }
    }

    /**
     * 判断是否管理员
     * <AUTHOR>
     * @date 2019-11-25
     */
    public function checkIsSuper()
    {
        $data = $this->isSuper() ? 1 : 0;

        $this->apiReturn(200, $data, '');
    }
    /**
     * 获取会员服务费 迁移new\d\recharge.html （资源和集团不能用这个接口）
     * <AUTHOR>
     * @date 2019-11-25
     */
    public function getUserServiceFee()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (in_array($loginInfoArr['dtype'], [2, 7])) {
            $this->apiReturn(204, [], '暂不支持查询');
        }
        $memberBiz = new \Business\Member\Member();
        $res       = $memberBiz->getUserAccountBalance($loginInfoArr['sid']);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 平台手机号登录发送验证码时获取图形验证码
     * Create by yangjianhui
     * Date: 2019/4/7
     */
    public function getPcLoginImgCode()
    {
        //获取图形验证码的手机号
        $mobile = I('mobile', '', "strval");

        if (empty($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '手机号不能为空');
        }
        //随机生成的字符串
        $str     = CaptchaCode::generate($mobile);

        $width=60;    //验证码图片的宽度
        $height=25;    //验证码图片的高度
        header("Expires:Mon,26 Jul 1997 05:00:00 GMT");
        //always modified
        header("Last-Modified:".gmdate("D,d M Y H:i:s")."GMT");
        //HTTP/1.1
        header("Cache-Control:no-store,no-cache,must-revalidate");
        header("Cache-Control:post-check=0,pre-check=0",false);
        //HTTP/1.0
        header("Pragma:no-cache");
        header("Content-Type:image/png");
        $im=imagecreate($width,$height);
        $back=imagecolorallocate($im,0xFF,0xFF,0xFF);    //背景色
        $font=imagecolorallocate($im,41,163,238);        //字体色
        //绘制1000个模糊作用的点
        imagestring($im,5,7,5,$str,$font);//绘制随机生成的字符串
        imagepng($im);//建立一张PNG格式图形
        imagedestroy($im);//释于内存空间将图片handle解构，
    }

    /**
     * 检测手机号是否可以在平台登录
     * <AUTHOR>
     * @date 2020/4/7
     */
    public function checkMobileCanLoginPc()
    {
        $mobile = I('mobile', '', "strval");
        if (empty($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR,  [], '手机号不能为空');
        }
        $smsKey = 'codeErrorFrequency' . $mobile;
        /**
         * @var $redis \Library\Cache\CacheRedis
         */
        $redis  = Cache::getInstance('redis');
        $errorFrequency = $redis->get($smsKey);
        if ($errorFrequency >= 6) {
            $this->apiReturn(400,  [], "验证码输入频繁错误，等待1小时后在尝试！");
        }
        //发送短信前检测该手机号是否有授权信息
        $memberModel = new \Model\Member\Member();
        $authInfo    = $memberModel->getAuthByIdentifier($mobile, MemberConst::AUTH_MOBILE);
        if (!$authInfo) {
            $this->apiReturn(204, [], '未找到该手机号的授权信息');
        }
        $customerId = $authInfo['customer_id'];
        //检测账号是否被锁定
        $securityBz = new \Business\Member\Security();
        if ($securityBz->isLoginLock($customerId)) {
            $this->apiReturn(204, [], '账号密码输入错误次数过多，账号登录被锁定');
        }

        $roleArr    = [
            MemberConst::ROLE_APPLY,
            MemberConst::ROLE_DISTRIBUTOR,
            MemberConst::ROLE_RESOURCE,
            MemberConst::ROLE_STAFF,
            MemberConst::ROLE_GROUP,
            MemberConst::ROLE_MULTIDIST,
        ];
        //查看该角色下是否有可在平台登录的角色信息
        $roleList = $memberModel->getMultiRoleInfo($customerId, $roleArr);
        if (count($roleList) == 0) {
            $this->apiReturn(204, [], '未找到相关角色信息');
        }
        $this->apiReturn(200, [], '账号可登录');
    }
    /**
     * 平台手机号登录发送短信验证码
     * <AUTHOR>
     * @date 2020/4/7
     *
     */
    public function sendPcLoginSmsCode()
    {
        //手机号
        $mobile = I('mobile', '', "strval");
        $code   = I('code', '', "strval");
        if (empty($mobile) || empty($code)) {
            $this->apiReturn(203, [], "参数错误");
        }
        if (!ismobile($mobile)) {
            $this->apiReturn(203, [], "手机号格式错误！");
        }
        //检验验证码是否正确
        $checkCodeResult = CaptchaCode::compare($code, $mobile);
        if ($checkCodeResult === false) {
            $this->apiReturn(400, [], "图形验证码输入错误");
        }

        //$smsMode = SmsFactory::getFactory($mobile, 'fzzwx');
        ////短信发送成功缓存短信验证码
        ///**
        // * @var $redis \Library\Cache\CacheRedis
        // */
        //$redis    = Cache::getInstance('redis');
        //$codeTime = $redis->get("pcLoginCode:$mobile");
        //if ($codeTime) {
        //    list($code, $time) = explode('|', $codeTime);
        //    if ($_SERVER['REQUEST_TIME'] - $time < 60) {
        //        $this->apiReturn(201, [], '短信发送间隔低于60秒');
        //    }
        //}
        ////限制每个手机号每天的的短息获取次数
        //$smsLockKey = "getCodeFrequency:$mobile";
        //$result     = $redis->get($smsLockKey);
        //if ($result >= 10) {
        //    $this->apiReturn(401, [], '短信获取次数超过当天可获取的次数！');
        //}
        //$code = genRandomNum(6);
        //
        //$res = $smsMode->veifyCode('手机号验证码登录', $code);
        ////发送短信验证码
        //if ($res['code'] == 200) {
        //    $cache = $code . '|' . $_SERVER['REQUEST_TIME'];
        //    $key   = "pcLoginCode:$mobile";
        //    $redis->set($key, $cache, '', 300);
        //    pft_log('sms/data', "{$mobile}发送的短信验证码为{$code}");
        //    if (empty($result)) {
        //        $msg = "验证码发送成功，请注意查收！";
        //        //计算今天剩余的时间
        //        $time = 86400 - (time() + 8 * 3600) % 86400;
        //        $redis->set($smsLockKey, 1, '', $time);
        //    } else {
        //        $redis->incrBy($smsLockKey, 1);
        //        $msg = "验证码发送成功，上一条短信验证码已失效，请注意查收！";
        //    }
        $res = (new verifyCode())->sendSmsVerifyCode($mobile);
        if($res['code'] == 200 && $res['data']){
            $this->apiReturn(200, [], '验证码发送成功，请注意查收！');

        } else {
            pft_log('login/javaApi/sendSmsVerifyCode',json_encode($res));
            $this->apiReturn($res['code'], [], $res['msg']);
        }
    }

    /**
     * 手机验证码登录
     * <AUTHOR>
     * @date   2020/04/03
     *
     * @return array
     */
    public function loginBySmsCode()
    {
        $hostArr = [
            "my.12301.local",
            "my.12301.test",
            "my.12301dev.com",
            "my.gray.12301dev.com",
            "my.gray.12301.cc",
            "www.gray.12301.cc",
            "my.12301.cc",
            "www.12301.cc",
        ];
        if (!in_array($_SERVER['HTTP_HOST'], $hostArr)) {
            $this->apiReturn(400, [], '暂不支持在该域名下登录');
        }

        $mobile   = I('post.mobile', '', 'strval');
        $roleType = I('post.roleType', false);
        $smsCode  = I('post.smsCode', '', 'strval');
        if (!$mobile || empty($smsCode)) {
            $this->apiReturn(203, [], '手机号或者验证码不能为空');
        }

        if (!ismobile($mobile)) {
            $this->apiReturn(204, [], '手机号格式错误');
        }

        $loginBz = new \Business\Member\Login();
        $result  = $loginBz->loginBySmsCode($mobile, $smsCode, MemberConst::PAGE_PLATFORM, $roleType);
        if ($result['code'] == 200) {
            $loginInfo = $result['data'];

            //是否是订单模式(应用中心需要判断)
            $api    = new \Business\JavaApi\Member\MemberConfig();
            $cfgRes = $api->getConfigWithMemberId($loginInfo['sid']);
            $controller = new BusinessCache();
            $bizData  = [];
            if ($cfgRes['code'] == 200 && $cfgRes['data']) {
                if (($cfgRes['data']['fee_platform'] || $cfgRes['data']['fee_code']) && $loginInfo['sdtype'] == 0) {
                    $bizData['order_mode'] = 1;
                } else {
                    $bizData['order_mode'] = 0;
                }
            } else {
                $bizData['order_mode'] = 0;
            }

            //设置登录来源
            $bizData['source'] = 'platform';
            $controller->setBusinessCache($bizData, $loginInfo['memberID']);

            //删除在短信中的redis缓存
            /**
             * @var $redis \Library\Cache\CacheRedis
             */
            $redis = Cache::getInstance('redis');
            $redis->hdel("pcLoginCode:$mobile", '');
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 验证短信验证码是否正确
     *
     * @param  string     $mobile     手机号
     * @param  string     $smsCode    验证码
     *
     * <AUTHOR>
     * @date   2020/04/08
     *
     * @return array
     */
    private function _checkSmsCode($mobile, $smsCode)
    {
        if (empty($mobile || empty($smsCode))) {
            return [203, "手机号或者验证码不能为空"];
        }
        $key    = "pcLoginCode:$mobile";
        $smsKey = 'codeErrorFrequency' . $mobile;
        /**
         * @var $redis \Library\Cache\CacheRedis
         */
        $redis  = Cache::getInstance('redis');
        $errorFrequency = $redis->get($smsKey);
        if ($errorFrequency >= 6) {
            return [400,  "手机号或者验证码输入频繁错误，等待1小时候在尝试！"];
        }
        $result = $redis->get($key);
        $data   = explode('|', $result);
        if ($data[0] != $smsCode) {
            if (empty($errorFrequency)) {
                $redis->set($smsKey, 1, "", 3600);
            } else {
                $redis->incrBy($smsKey, 1);
            }
            $errorFrequency = $redis->get($smsKey);
            if ($errorFrequency >= 6) {
                return [400,  "手机号或者验证码输入频繁错误，等待1小时候在尝试！"];
            } else {
                //记录验证码错误次数
                return [400, "手机号或者验证码输入错误"];
            }
        }
        $redis->hdel($smsKey, '');
        return [200, "验证成功"];
    }

    /**
     * 获取资源方登录密码或修改密码
     * @date 2021-10-29
     * <AUTHOR>  Li
     *
     * @return array
     * @throws \Exception
     */
    public function resourceAccountPasswordOperate()
    {
        $salerid   = I('post.salerid', '', 'strval');
        //$applier   = I('post.applier', '', 'strval');
        $checkauth = I('post.checkauth', '', 'strval');
        $type      = I('post.type', 1, 'intval');
        $loginInfo = $this->getLoginInfo();
        $applier   = $loginInfo['account'];

        if (!$salerid || !$applier) {
            $this->apiReturn(203, [], '商家信息缺失');
        }

        if (!in_array($type, [1, 2])) {
            $this->apiReturn(203, [], '当前操作类型有误');
        }

        if (!$checkauth) {
            $this->apiReturn(204, [], '请输入当前用户登录密码');
        }

        $memberBiz = new MemberBiz();
        $result    = $memberBiz->resourceAccountPasswordOperate($salerid, $applier, $checkauth, $type);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], $result['msg']);
    }

    /**
     * 获取资源方账号景区类型
     * <AUTHOR> lingfeng
     * 2022/7/27 9:54
     */
    public function resourceAccountPType()
    {
        $loginInfo = $this->getLoginInfo();

        if ($loginInfo['dtype'] != 2){
            $this->apiReturn(self::CODE_NO_CONTENT, [], '非资源方账号');
        }

        $land = new \Business\CommodityCenter\Land();
        $landInfo = $land->queryLandMultiQueryBySalerid([$loginInfo['account']]);
        if (empty($landInfo)){
            $this->apiReturn(self::CODE_NO_CONTENT, [], '获取失败');
        }
        $landInfo = $landInfo[0];
        $returnData = [
            'p_type' => $landInfo['p_type'],
            'title' => $landInfo['title'],
            'salerid' => $landInfo['salerid'],
        ];
        $this->apiReturn(self::CODE_SUCCESS, $returnData, '获取成功');
    }

    /**
     * 校验登录用户是否允许给合作（供应商）充值还款（白名单）
     */
    public function checkLoginMemberRechargePartner()
    {
        $loginInfo = $this->getLoginInfo();

        $loginSid = $loginInfo['sid'];

        //默认不开启
        $isEnable = false;
        try {
            $sidList = \qconf::getConf("/php/platform/recharge_partner_whitelist");
            if ($sidList) {
                $sidList = json_decode($sidList, true);
            } else {
                $sidList = [];
            }

            //白名单账号，允许分销商给供应商充值
            if (is_array($sidList) && in_array($loginSid, $sidList)) {
                $isEnable = true;
            }
        } catch (\Throwable $e) {}
        $result = ['is_enable' => $isEnable];

        $this->apiReturn(200, $result, 'success');
    }

    /**
     * 校验主账号用户的密码
     * @date 2022-08-23
     * <AUTHOR>  Li
     *
     * @return array
     * @throws \Exception
     */
    public function checkMemberPassword()
    {
        $checkauth = I('post.checkauth', '', 'strval');
        $loginInfo = $this->getLoginInfo();

        if (!$checkauth) {
            $this->apiReturn(204, [], '请输入当前用户登录密码');
        }

        $memberBiz = new MemberBiz();
        $result    = $memberBiz->checkMemberPassword($loginInfo['sid'], $checkauth);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], $result['msg']);
    }
}
