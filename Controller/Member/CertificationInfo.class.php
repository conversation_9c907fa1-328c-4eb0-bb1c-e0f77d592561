<?php
/**
 * 资质认证相关接口
 */

namespace Controller\Member;

use Library\Controller;
use Business\Member\CertificationInfo as CertificationBiz;

class CertificationInfo extends Controller
{
    private $_certificationBiz;
    private $sid;
    private $memberId;
    private $_loginInfo;

    public function __construct()
    {
        $this->_certificationBiz = new CertificationBiz();
        $this->_loginInfo        = $this->getLoginInfo();
        $this->sid               = $this->_loginInfo['sid'];
        $this->memberId          = $this->_loginInfo['memberID'];
    }

    /**
     * 查询用户资质详细信息
     * <AUTHOR>  Li
     * @date  2022-08-15
     */
    public function getCertificationDetail()
    {
        $certificationRes = $this->_certificationBiz->getCertificationDetail($this->sid);

        $this->apiReturn(200, $certificationRes['data'], $certificationRes['msg']);
    }

    /**
     * 查询用户资质认证状态
     * <AUTHOR>  Li
     * @date  2022-08-15
     */
    public function getCertificationStatus()
    {
        $certificationRes = $this->_certificationBiz->getCertificationStatus($this->sid);

        $this->apiReturn(200, $certificationRes['data'], $certificationRes['msg']);
    }

    /**
     * 撤回用户资质认证
     * <AUTHOR>  Li
     * @date  2022-08-17
     */
    public function certificationCancel()
    {
        $id  = I('id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(203, [], '认证信息缺失');
        }

        $certificationRes = $this->_certificationBiz->certificationCancel($id, $this->sid);

        $this->apiReturn(200, $certificationRes['data'], $certificationRes['msg']);
    }

    /**
     * 校验资质宽限期等
     *
     * @auther  PeiJun  Li
     * @date 2022-08-17
     *
     * @return  false|string
     */
    public function checkCertification()
    {
        $isStaff          = $this->_loginInfo['dtype'] == 6 ? 1 : 0;
        $source           = I('source', 1, 'intval'); //来源 1平台 2微平台
        $type             = I('type', 1, 'intval');
        $certificationRes = $this->_certificationBiz->checkCertification($this->sid, $isStaff, $type, $source);

        $this->apiReturn($certificationRes['code'], $certificationRes['data'], $certificationRes['msg']);
    }

    /**
     * 解绑银行卡校验
     * <AUTHOR>  Li
     * @date  2022-11-15
     *
     * @return  false|string
     */
    public function unbindBankcardVerify()
    {
        $bankAccount  = I('bank_account', '', 'strval');
        if (!$bankAccount) {
            $this->apiReturn(203, [], '银行卡信息不能为空');
        }
        $certificationRes = $this->_certificationBiz->unbindBankcardVerify($this->sid, $bankAccount);

        $this->apiReturn($certificationRes['code'], $certificationRes['data'], $certificationRes['msg']);
    }

    /**
     * 获取会员服务费
     * <AUTHOR>  Li
     * @date  2022-11-22
     */
    public function getUserServiceFee()
    {
        if (in_array($this->_loginInfo['dtype'], [2, 7])) {
            $this->apiReturn(204, [], '暂不支持查询');
        }

        $memberBiz = new \Business\Member\Member();
        $res       = $memberBiz->getUserAccountBalance($this->_loginInfo['sid']);
        if (isset($res['code'])) {
            $res['data']['dtype']         = $this->_loginInfo['dtype'];
            $res['data']['login_account'] = $this->_loginInfo['account'];
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }
}