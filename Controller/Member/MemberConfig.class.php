<?php
/**
 * 会员管理
 *
 * <AUTHOR>
 * @date   2017-2-9
 */

namespace Controller\Member;

use Business\AppCenter\Module as ModuleBiz;
use Business\Ota\SystemConfig;
use Library\Business\WechatSmallApp;
use Model\Member\Member;

use Qiniu\Auth;
use Qiniu\Storage\UploadManager;
use \Library\Controller;

class MemberConfig extends Controller
{

    // 用户登陆信息
    private $_loginInfoArr;
    // 部门权限模型
    private $_departAuthModel;

    /**
     * 生成商家或者景点的小程序码
     */
    public function generateWechatSmallAppSceneCode()
    {
        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();

        $lib      = new WechatSmallApp();
        $account  = I('post.appidAccount');
        $memberid = I('post.memberId');
        $width    = I('post.width');
        $scenCode = $lib->encodeShopCode($memberid);
        $res      = $lib->getWxCodeUnlimit($account, $scenCode, $width);
        $prefix   = substr($res['data'], 0, 1);
        if ($prefix == '{') {
            $decodeRes = json_decode($res['data'], true);
            $this->apiReturn(401, $res, $decodeRes['errmsg']);
        }

        $upManager = new UploadManager();
        $config    = load_config('qiniu', 'Qiniu');
        $auth      = new Auth($config['accessKey'], $config['secretKey']);
        $token     = $auth->uploadToken($config['images']['bucket'], null, 3600);
        $fileName  = "wxAppCode_$memberid.png";
        [$ret, $error] = $upManager->put($token, $fileName, $res['data']);
        $ret['url']      = $config['images']['domain'] . $fileName;
        $ret['scenCode'] = $scenCode;
        parent::apiReturn(200, $ret);
    }

    /**
     * 生成跳转指定到知道页面的小程序二维码
     */
    public function generateWechatSmallAppCode()
    {
        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();

        $lib     = new WechatSmallApp();
        $account = I('post.appidAccount');
        $path    = I('post.path');
        $width   = I('post.width');
        $res     = $lib->getWxACode($account, $path, $width);
        if ($res['code'] != 200) {
            parent::apiReturn(401, $res['data'], $res['msg']);
        }

        $upManager = new UploadManager();
        $config    = load_config('qiniu', 'Qiniu');
        $auth      = new Auth($config['accessKey'], $config['secretKey']);
        $token     = $auth->uploadToken($config['images']['bucket'], null, 3600);
        $fileName  = "wxAppCode_" . md5($path) . ".png";
        [$ret, $error] = $upManager->put($token, $fileName, $res['data']);
        $ret['url'] = $config['images']['domain'] . $fileName;
        parent::apiReturn(200, $ret);
    }

    /**
     * 获取登陆用户的信息
     * <AUTHOR>
     *
     * @return array
     *
     */
    private function _loginInfoArr()
    {
        if (!$this->_loginInfoArr) {
            $this->_loginInfoArr = $this->getLoginInfo();
        }

        return $this->_loginInfoArr;
    }

    /**
     * 订单或产品回调地址验证
     * <AUTHOR>
     */
    public function checkSoapUrl()
    {
        // 产品或订单回调地址
        $url = I('url', '','strval,trim');
        $url = explode("?", $url);

        //验证操作权限
        $loginInfo = $this->getLoginInfo();

        if (count($url) > 1) {
            $this->apiReturn('402', [], '请输入不带参数的回调地址');
        } else if (!empty($url[0])) {
            $res = curl_post($url[0], [], '', 25, '/api/curl_post', [], false, '', 'get');
            $res = trim($res);

            if (strpos($res, "success") == FALSE && $res != "success" && $res != "\"success\"") {
                $res = curl_post($url[0], [], '', 25, '/api/curl_post', [], false, '', 'post');
                $res = trim($res);
                if (strpos($res, "success") == FALSE && $res != "success" && $res != "\"success\"") {
                    $this->apiReturn('402', [], '回调地址未返回正确的成功信息');
                }
            }
        }

        $this->apiReturn('200', [], 'ok');
    }

    /**
     * 设置用户订单回调通知
     * <AUTHOR>
     */
    public function setMemberDcodeURL()
    {
        // 订单回调url
        $url = I('url', '','strval,trim');
        // 接口编码， 如果是第一次配置则不为空
        $dcode = I('dcode', '');
        if (empty($url)) {
            $this->apiReturn(402, [], '订单回调地址不能为空');
        }

        //验证操作权限
        $loginInfo = $this->getLoginInfo();
        if ($loginInfo['sid'] != $loginInfo['memberID']) {
            $this->apiReturn(402, [], '主账号才可操作订单回调地址配置');
        }

        $memberModel        = new Member();
        $partnerMemberModel = new \Model\Member\PartnerMember('localhost');
        $memberBiz          = new \Business\Member\Member();
        $openMerchatAuthLib = new \Business\OtaApi\MerchatAuth();

        // 判断用户的dcode 是否正确
        $memberInfoArr = $memberModel->getMemberInfo($loginInfo['memberID'], 'id', 'dcode, dcodeURL');

        if ($dcode) {
            // 第一次配置 创建账号密码

            if ($memberInfoArr['dcode'] != $dcode) {
                $this->apiReturn(402, [], '接口编号错误');
            }

            //$Helpers = new Helpers();
            //$soap    = $Helpers->GetSoapInside();
            //$res     = $soap->Create_Conn_REC($loginInfo['saccount'], $loginInfo['dname']);

            $addRes = $openMerchatAuthLib->add($loginInfo['saccount'], $loginInfo['dname']);
            if ($addRes['code'] != 200) {
                $this->apiReturn(402, [], "账号生成出错 - " . $addRes['msg'] . "【{$addRes['code']}】");
            }

            $queryData = $addRes['data'];
            $password  = $queryData['pwd'];

            $memberBiz->updateDcodeUrl($loginInfo['memberID'], $url);
            //同步到java
            $this->_dataexSave($loginInfo['saccount']);
            \Library\Business\TerminalCache::DeleteMemberInfo($loginInfo['memberID']);
            $this->apiReturn(200, ['account' => $loginInfo['saccount'], 'pwd' => $password], "申请成功，请牢记页面上的账号密码");
        } else {
            // 判断路径是否有更改
            if ($memberInfoArr['dcodeURL'] == $url) {
                $this->apiReturn(202, [], '地址无变动');
            }

            //重新生成密码
            //$pwdUpdateRes = $partnerMemberModel->setDproductURLByAccount($loginInfo['saccount'], false);
            $updateRes = $openMerchatAuthLib->updateAuth($loginInfo['saccount']);

            if ($updateRes['code'] != 200) {
                $this->apiReturn(402, [], '更新商户秘钥失败');
            }

            $updateData = $updateRes['data'];
            $pwd        = $updateData['pwd'];

            $res = $memberBiz->updateDcodeUrl($loginInfo['memberID'], $url);

            if (!$res) {
                $this->apiReturn(402, [], '配置订单回调地址失败');
            }

            //同步到java
            $this->_dataexSave($loginInfo['saccount']);

            \Library\Business\TerminalCache::DeleteMemberInfo($loginInfo['memberID']);
            $this->apiReturn(200, ['account' => $loginInfo['saccount'], 'pwd' => $pwd], '配置订单回调地址成功');
        }
    }

    /**
     * 设置ip白名单
     * @Author: zhujb
     * 2018/11/6
     */
    public function setIpWhiteList()
    {
        $ipList = trim(I('post.ipList', '', 'strval'));

        if (!empty($ipList)) {
            $ipArr = explode(',', $ipList);
            foreach ($ipArr as $ip) {
                $ipInfo = explode('/', $ip);
                if (!filter_var($ipInfo[0], FILTER_VALIDATE_IP)) {
                    $this->apiReturn(400, [], 'IP地址格式错误');
                }
                if (!empty($ipInfo[1])) {
                    if ($ipInfo[1] < 0 || $ipInfo[1] > 36) {
                        $this->apiReturn(400, [], 'IP网段格式错误');
                    }
                }
            }
        }

        //验证操作权限
        $loginInfo          = $this->getLoginInfo();
        if ($loginInfo['sid'] != $loginInfo['memberID']) {
            $this->apiReturn(402, [], '主账号才可操作IP白名单配置');
        }
        $partnerMemberModel = new \Model\Member\PartnerMember('localhost');

        $res = $partnerMemberModel->setIpListByAccount($loginInfo['saccount'], $ipList);
        if ($res === true) {
            //同步到java
            $this->_dataexSave($loginInfo['saccount']);
            // 删除缓存
            \Library\Cache\Cache::getInstance('redis')->rm('dataex:' . $loginInfo['saccount']);
            $this->apiReturn(200, [], 'IP白名单配置成功');
        }
        $this->apiReturn(400, [], 'IP白名单配置失败');
    }

    /**
     * 设置用户产品回调通知
     * <AUTHOR>
     */
    public function setMemberDproductURL()
    {
        // 产品回调url
        $url = I('url', '','strval,trim');

        if (empty($url)) {
            $url = '';
        }

        //验证操作权限
        $loginInfo = $this->getLoginInfo();
        if ($loginInfo['sid'] != $loginInfo['memberID']) {
            $this->apiReturn(402, [], '主账号才可操作产品回调地址配置');
        }

        // 首先判断当前用户是否已经设置了订单回调地址
        $partnerMemberModel = new \Model\Member\PartnerMember('localhost');
        $dataexInfo         = $partnerMemberModel->getDataexByAccount($loginInfo['saccount']);

        if (empty($dataexInfo)) {
            // 当前用户未配置订单回调地址
            $this->apiReturn(402, [], '请先配置订单回调地址');
        }

        // 获取dproductURL 判断路径是否有更改
        if ($dataexInfo['dproductURL'] == $url) {
            $this->apiReturn(202, [], '地址无变动');
        }

        $res = $partnerMemberModel->setDproductURLByAccount($loginInfo['saccount'], $url, false);

        if (!$res) {
            // 配置失败
            $this->apiReturn(402, [], '配置产品回调地址失败');
        }
        //同步到java
        $this->_dataexSave($loginInfo['saccount']);

        \Library\Business\TerminalCache::DeleteMemberInfo($loginInfo['memberID']);
        // 配置成功
        $this->apiReturn(200, [], '配置产品回调地址成功');
    }

    /**
     * 设置用户出票回调通知
     * <AUTHOR>
     * @date   2018-5-18
     *
     */
    public function setMemberDTicketURL()
    {
        // 产品回调url
        $url = I('url', '','strval,trim');

        if (empty($url)) {
            $url = '';
        }

        //验证操作权限
        $loginInfo = $this->getLoginInfo();
        if ($loginInfo['sid'] != $loginInfo['memberID']) {
            $this->apiReturn(402, [], '主账号才可操作出票回调地址配置');
        }

        $memberModel = new Member();

        // 首先判断当前用户是否已经设置了订单回调地址
        $partnerMemberModel = new \Model\Member\PartnerMember('localhost');
        $dataexInfo         = $partnerMemberModel->getDataexByAccount($loginInfo['saccount']);

        if (empty($dataexInfo)) {
            // 当前用户未配置订单回调地址
            $this->apiReturn(402, [], '请先配置订单回调地址');
        }

        // 获取dproductURL 判断路径是否有更改
        if ($dataexInfo['dticketURL'] == $url) {
            $this->apiReturn(202, [], '地址无变动');
        }

        $res = $partnerMemberModel->setDticketURLByAccount($loginInfo['saccount'], $url);

        if (!$res) {
            // 配置失败
            $this->apiReturn(402, [], '配置出票回调地址失败');
        }

        //同步到java
        $this->_dataexSave($loginInfo['saccount']);

        \Library\Business\TerminalCache::DeleteMemberInfo($loginInfo['memberID']);
        // 配置成功
        $this->apiReturn(200, [], '配置出票回调地址成功');
    }

    /**
     * 处理所有显示的权限数组
     * <AUTHOR>
     *
     * @return array
     *
     */
    private function _handleAllAuth()
    {
        // 获取所有权限设置
        $allAuthArr = $this->_allAuth();
        // 获取登陆用户信息
        $loginInfoArr = $this->_loginInfoArr();

        $isSuper = false;
        if (isset($loginInfoArr['sid']) && $loginInfoArr['sid'] == 1) {
            $isSuper = true;
        }

        $authArr       = [];
        $px            = [];
        $noGroupAuth   = [];
        $newAllAuthArr = [];

        $newCount = 0;
        if ($allAuthArr['auth']) {
            foreach ($allAuthArr['auth'] as $authItemKey => $authItemVal) {
                // 如果不存在group 的权限提取
                if (!isset($authItemVal['group'])) {
                    $noGroupAuth[$authItemKey] = $authItemVal;
                    continue;
                }
                // 是否显示判断
                if ($isSuper && !in_array($loginInfoArr['sdtype'], explode(",", $authItemVal['limit']), true)) {
                    continue;
                }
                if (empty($authItemVal['app'])) {
                    if (!in_array($loginInfoArr['sdtype'], explode(",", $authItemVal['limit']), true)) {
                        continue;
                    }
                }
                $newAllAuthArr[$authItemVal['group']][$newCount]['label'] = $authItemVal['title'];
                $newAllAuthArr[$authItemVal['group']][$newCount]['value'] = $authItemKey;
                $newCount++;
            }
        }

        $count = 0;
        if ($allAuthArr['authGroup']) {
            foreach ($allAuthArr['authGroup'] as $authGroupKey => $authGroupValue) {
                if (isset($newAllAuthArr[$authGroupKey]) && !empty($newAllAuthArr[$authGroupKey])) {
                    $authArr[$count]['title']     = $authGroupValue['name'];
                    $authArr[$count]['group']     = $authGroupKey;
                    $px[$count]                   = $authGroupKey;
                    $authArr[$count]['checkList'] = array_values($newAllAuthArr[$authGroupKey]);
                    $count++;
                }
            }
        }

        // 对权限数组进行排序
        array_multisort($px, $authArr);

        // 在把权限中没有分组的插入数组首位
        $noGroupAuthArr = [];
        if ($noGroupAuth) {
            foreach ($noGroupAuth as $noGroupKey => $noGroupValue) {
                $noGroupAuthArr['title'] = ' ';
                $noGroupAuthArr['group'] = '';

                // 初始化下
                $noGroupAuthArr['checkList'] = [];

                $noGroupAuthArr['checkList'][] = [
                    'label' => $noGroupValue['title'],
                    'value' => $noGroupKey,
                ];
                array_unshift($authArr, $noGroupAuthArr);
            }
        }

        return $authArr;
    }

    /**
     * @param $sid
     * @param $departIdArr
     *
     * @return array|mixed
     */
    private function _departStaffList($sid, $departIdArr)
    {
        $shipModel   = new \Model\Member\MemberRelationship();
        $memberModel = new Member();
        // 获取账户下所有员工
        $sonArr = $shipModel->getStaffList($sid);
        if (empty($sonArr)) {
            return [];
        }
        $memberInfoArr = $memberModel->getInfoInMemberExtInfo('position,id,fid',
            ['fid' => ['IN', $sonArr], 'position' => ['IN', $departIdArr]]);
        if (empty($memberInfoArr)) {
            return [];
        }

        return $memberInfoArr;
    }

    /**
     * 取得所有要设置的权限列表
     * <AUTHOR>
     *
     * @return array
     *
     */
    private function _allAuth()
    {
        $auth = '';

        $loginInfo = $this->_loginInfoArr();
        $isSuper   = false;
        if (isset($loginInfo['sid']) && $loginInfo['sid'] == 1) {
            $isSuper = true;
        }

        //加载侧边栏
        $authBiz = new \Business\Member\Authority();
        $_auth   = $authBiz->getBaseMenu($loginInfo['memberID'], $loginInfo['sid'], $loginInfo['dtype'], $isSuper);

        $_auth_group = load_config('menu_main', 'appcenter');

        // 授信需要单独配置权限，此处添加防止供应商侧边显示
        $_auth['repayment'] = array(
            "group" => 4,
            "limit" => "0,6",
            "title" => "授信管理",
            "url"   => array(
                "repayment.html",
            ),
        );

        // 图表分析不在侧边栏显示
        $_auth['chart'] = array(
            'group' => 1,
            'limit' => '0,1,6',
            'title' => '图形分析(微信可见)',
            'plat'  => 'wx', // 表示仅限微信端使用
            'url'   => [
                'home.html',
            ],
        );

        $auth      = $_auth ?: '';
        $authGroup = $_auth_group ?: '';

        //特殊处理 原先的销售报表权限替换成 预订、验证、验证套票、预订套票四种类型
        unset($auth['orderReport']);

        return [
            'auth'      => $auth,
            'authGroup' => $authGroup,
        ];
    }

    /**
     * dataex变化同步至java
     *
     */
    private function _dataexSave($account)
    {
        if(empty($account)){
            return ['code' => 1000,'msg' => '参数缺失'];
        }

        $partnerMemberModel = new \Model\Member\PartnerMember('localhost');
        $dataexInfo         = $partnerMemberModel->getDataexByAccount($account);

        if (empty($dataexInfo)) {
            $data = [
                'key'     => 'dataex信息同步',
                'account' => $account,
            ];
            pft_log('member/memberconfig',json_encode($data));
            return ['code' => 1000, 'msg' => '找不到用户数据'];
        }
        
        $id          = $dataexInfo['id'];
        $account     = $dataexInfo['account'];
        $pwd         = $dataexInfo['pwd'];
        $notes       = $dataexInfo['notes'];
        $addtime     = $dataexInfo['addtime'];
        $pmode       = $dataexInfo['pmode'];
        $vtype       = $dataexInfo['vtype'];
        $lang        = $dataexInfo['lang'];
        $qx          = $dataexInfo['qx'];
        $dproducturl = $dataexInfo['dproductURL'];
        $dticketurl  = $dataexInfo['dticketURL'];
        $iplist      = $dataexInfo['iplist'];

        $Account = new \Business\JavaApi\Account\DataexService();
        $res     = $Account->dataexSave($id, $account, $pwd, $notes, $addtime, $pmode, $vtype, $lang, $qx, $dproducturl,
            $dticketurl, $iplist);
        return $res;
    }

    /**
     * 获取分销同业平台接口配置信息
     * <AUTHOR>
     * @date 2020/8/5
     *
     * @return array
     */
    public function getSameBusinessConfig()
    {
        $loginInfoArr    = $this->_loginInfoArr();
        $memberId        = $loginInfoArr['sid'];
        $systemConfigLib = $systemConfigLib = new SystemConfig();
        $result          = $systemConfigLib->getSameBusinessConfig($memberId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 检测是否开通同业平台分销应用
     * <AUTHOR>
     * @date 2020/8/20
     *
     * @return array
     */
    public function checkSameBusinessModule()
    {
        $loginInfoArr    = $this->_loginInfoArr();
        $memberId        = $loginInfoArr['sid'];
        $moduleLib       = new ModuleBiz();
        $result          = $moduleLib->checkSameBusinessModule($memberId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 检测同业免费版配置链接
     * <AUTHOR>
     * @date 2021/1/26
     *
     * @return array
     */
    public function checkFreeBusinessVaild()
    {
        $url = I('post.url', '', 'strval');//url
        if (empty($url)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $loginInfoArr    = $this->_loginInfoArr();
        $memberId        = $loginInfoArr['sid'];
        $systemConfigLib = $systemConfigLib = new SystemConfig();
        $result          = $systemConfigLib->checkFreeBusinessVaild($memberId, $url);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);

    }
}
