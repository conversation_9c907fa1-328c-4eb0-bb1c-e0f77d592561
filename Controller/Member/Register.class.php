<?php
/**
 * 新版注册控制逻辑
 *
 * <AUTHOR>
 * @date   2016-05-18
 */

namespace Controller\Member;

use Business\AppCenter\Module;
use Business\Captcha\CaptchaApi;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\RiskManagement\ShumeiRiskCheck;
use Library\Cache\CacheRedis;
use Library\Container;
use Library\Controller;
use Library\Kafka\KafkaProducer;
use Library\MessageNotify\MessageException;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Tools\Helpers;
use Library\Cache\Cache;
use Library\Util\EnvUtil;
use pft\member\MemberAccount;
use Business\Member\Member as MemberBiz;
use Business\Member\Register as businesReg;

class Register extends Controller
{

    // 用户模型
    private $_memModel;

    // 最大发送短信次数
    private $_maxSendMsgNum = 8;

    public function __construct()
    {
        //授权验证
        $isLegal = $this->_auth();

        if (!$isLegal) {
            // $this->apiReturn(403, [], '非法请求');
        }
    }

    /**
     * 实例化 用户模型
     *
     */
    private function _getMemModel()
    {
        if (!$this->_memModel) {
            $db = Helpers::getPrevDb();
            Helpers::loadPrevClass('MemberAccount');
            $this->_memModel = new MemberAccount($db);
        }

        return $this->_memModel;
    }

    /**
     * 检测手机号码
     * <AUTHOR>
     * @date   2016-05-18
     *
     * @param $mobile 手机号码
     *
     * @return
     */
    public function checkMobile()
    {
        $mobile = I('post.mobile');
        $version = I('post.version', 'v1', 'strval');

        if (!$mobile) {
            $this->apiReturn(406, [], '参数错误');
        }

        //手机号码验证
        if (!ismobile($mobile)) {
            $this->apiReturn(406, [], '手机号码不正确');
        }

        //客户账号是否存在
        $memberBiz  = new MemberBiz();
        if ($version == 'v2') {
            $queryRes = $memberBiz->queryPlatformMemberByMobile($mobile, [0, 1]);
            if ($queryRes['registered']) {
                //已经注册
                $this->apiReturn(200, ['is_register' => 1], '已经注册');
            } else {
                $this->apiReturn(200, ['is_register' => 0], '没有注册');
            }
        } else {
            $customerId = $memberBiz->parseCustomerIdByMobile($mobile);
            if ($customerId) {
                $memModel = new \Model\Member\Member('slave');
                //是否存在分销商/供应商角色
                $res = $memModel->getMultiRoleInfo($customerId, [0, 1], 'id');
            } else {
                $res = false;
            }
            if ($res) {
                //已经注册
                $this->apiReturn(200, array('is_register' => 1), '已经注册');
            } else {
                $this->apiReturn(200, array('is_register' => 0), '没有注册');
            }
        }
    }

    /**
     * 发送注冊短信验证码
     * <AUTHOR>
     * @date   2016-05-18
     *
     * @return
     */
    public function regVcode()
    {
        $mobile = I('post.mobile');
        $version = I('post.version', 'v1', 'strval');

        if (!$mobile) {
            $this->apiReturn(406, [], '参数错误');
        }

        //手机号码验证
        if (!Helpers::ismobile($mobile)) {
            $this->apiReturn(406, [], '手机号码不正确');
        }

        //图形验证码验证
        $isLegal = $this->_authImage();
        if (!$isLegal) {
            $this->apiReturn(403, [], '图形验证码错误');
        }

        $blackList   = load_config('black_list');
        $blackMobile = $blackList['mobile'];
        if (in_array($mobile, $blackMobile)) {
            $this->apiReturn(403, [], '该手机号已经被加入黑名单。');
        }

        $cacheRedis = Cache::getInstance('redis');
        $ip         = ip();
        $cacheKey   = "mobile:$ip:$mobile";
        $send_time  = $cacheRedis->get($cacheKey, '', true);
        if ($send_time > $this->_maxSendMsgNum) {
            $this->apiReturn(403, [], '该手机号发送次数超出系统限制。');
        }

        //客户账号是否存在
        $memberBiz  = new MemberBiz();
        if ($version == 'v2') {
            $queryRes = $memberBiz->queryPlatformMemberByMobile($mobile, [0, 1]);
            if ($queryRes['registered']) {
                $this->apiReturn(403, [], '该手机号用户已注册票付通会员，请尝试更换其它号码，若有疑问请联系我们！');
            }
        } else {
            $customerId = $memberBiz->parseCustomerIdByMobile($mobile);
            if ($customerId) {
                $memModel = new \Model\Member\Member('slave');
                //是否存在分销商/供应商角色
                $res = $memModel->getMultiRoleInfo($customerId, [0, 1], 'id');
                if ($res) {
                    $this->apiReturn(403, [], '该手机号用户已注册票付通会员，请尝试更换其它号码，若有疑问请联系我们！');
                }
            }
        }

        if (Helpers::getVerifyCode($mobile)) {
            $this->apiReturn(403, [], '发送间隔太短！请在120秒后再重试。');
        }
        //发送短信
        try {
            $code   = Helpers::setVerifyCode($mobile, 120);
            $messageServiceApi = Container::pull(MessageService::class);
            [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'register_code', $mobile, [$code, '2']);
            if ($approval) {
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['发送注册手机验证码', __METHOD__, [$mobile, [$code, '2']], $res], JSON_UNESCAPED_UNICODE));
                }
            } else {
                /** @deprecated 放量结束后删除 */
                $smsLib = SmsFactory::getFactory($mobile);
                $res = $smsLib->registerCode($code);
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['发送注册手机验证码.old', __METHOD__, [$mobile, [$code, '2']], $res], JSON_UNESCAPED_UNICODE));
                }
            }
            if ($res['code'] == 200) {
                $cacheRedis->incrBy($cacheKey);
                $this->apiReturn(200, [], '发送验证码成功');
            } else {
                $this->apiReturn($res['code'], [], $res['msg']);
            }
        } catch (MessageException $e) {
            $this->apiReturn(500, [], '对不起，短信服务器发生故障，造成的不便我们感到十分抱歉。请联系我们客服人员。');
        }
    }

    /**
     * 发送注册短信验证码
     * <AUTHOR>
     * @date   2016-05-18
     *
     * @return
     */
    public function resetVcode()
    {
        $passport = I('post.passport');
        $captchaCode = I('post.captchaCode');
        $version = I('post.version', 'v1', 'strval');

        if (!$passport) {
            $this->apiReturn(406, [], '参数错误');
        }
        $MemberBus  = new \Business\Member\Member();
        //判断是手机号还是账号
        if (strlen($passport) < 11) {
            //账号，获取手机号码
            //用户二期 - 原生sql统一
            $memberInfo = $MemberBus->getInfoByAccount($passport);
            $mobile     = $memberInfo["mobile"];
            if (!$memberInfo["mobile"]) {
                $this->apiReturn(406, [], '手机号码不正确');
            }

        } else {
            $mobile = $passport;

            //手机号码验证
            if (!ismobile($mobile)) {
                $this->apiReturn(406, [], '手机号码不正确');
            }
        }

        //图形验证码验证
        $isLegal = $this->_authImage();

        if (!$isLegal) {
            $this->apiReturn(403, [], '图形验证码错误');
        }

        //暂时注释
        $captchaCheck = new CaptchaApi();
        $captchaRes   = $captchaCheck->handleSecondCheck($captchaCode);
        if ($captchaRes !== true) {
            return $this->apiReturn(209, ['need_captcha' => true], '请验证滑块');
        }

        $blackList   = load_config('black_list');
        $blackMobile = $blackList['mobile'];
        if (in_array($mobile, $blackMobile)) {
            $this->apiReturn(403, [], '该手机号已经被加入黑名单。');
        }
        /**
         * @var CacheRedis $cacheRedis
         */
        $cacheRedis = Cache::getInstance('redis');
        $ip         = ip();
        $cacheKey   = "mobile:$ip:$mobile";
        if ($cacheRedis->exists($cacheKey) === false) {
            $send_time = 0;
            $cacheRedis->set($cacheKey, 0, '', 3600);
        } else {
            $send_time = $cacheRedis->get($cacheKey, '', true);
        }
        if ($send_time > $this->_maxSendMsgNum) {
            $this->apiReturn(403, [], '该手机号发送次数超出系统限制。');
        }
        //判断手机号码是不是已经注册了
        if ($version == 'v2') {
            $queryRes = $MemberBus->queryPlatformMemberByMobile($mobile);
            $res = $queryRes['registered'] ?? false;
        } else {
            $res       = $MemberBus->parseCustomerIdByMobile($mobile);
        }
        if (!$res) {
            // 3min 10times
            $requestCacheKey = "ipRequestLimit:$ip";
            if ($cacheRedis->exists($requestCacheKey) === false){
                $cacheRedis->set($requestCacheKey, 1, 180);
            }else{
                $requestTimes = $cacheRedis->get($requestCacheKey);
                if ($requestTimes >= 10){
                    $this->apiReturn(403, [], '对不起, 您请求过于频繁, 请稍后重试');
                }else{
                    $cacheRedis->incr($requestCacheKey);
                }
            }
            $this->apiReturn(403, [], '该手机号还没有注册');
        }
        //发送短信
        try {
            $code   = Helpers::setVerifyCode($mobile, 120);
            $messageServiceApi = Container::pull(MessageService::class);
            [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'register_code', $mobile, [$code, '2']);
            if ($approval) {
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['发送注册手机验证码', __METHOD__, [$mobile, [$code, '2']], $res], JSON_UNESCAPED_UNICODE));
                }
            } else {
                /** @deprecated 放量结束后删除 */
                $smsLib = SmsFactory::getFactory($mobile);
                $res = $smsLib->registerCode($code);
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['发送注册手机验证码.old', __METHOD__, [$mobile, [$code, '2']], $res], JSON_UNESCAPED_UNICODE));
                }
            }
            if ($res['code'] == 200) {
                $cacheRedis->incrBy($cacheKey);
                $_SESSION['reset_mobile'] = $mobile;
                $this->apiReturn(200, [], '发送验证码成功');
            }
        } catch (MessageException $e) {
            $this->apiReturn(500, [], '对不起，短信服务器发生故障，造成的不便我们感到十分抱歉。请联系我们客服人员。');
        }
    }

    /**
     * 检测验证码
     * <AUTHOR>
     * @date   2016-05-20
     *
     * @return
     */
    public function checkVcode()
    {
        $mobile = I('session.reset_mobile');
        $vcode    = I('post.vcode');

        if (!$vcode || !$mobile) {
            $this->apiReturn(406, [], '参数错误');
        }
        //手机号码验证
        if (!ismobile($mobile)) {
            $this->apiReturn(406, [], '手机号码不正确');
        }
        //手机验证码验证
        $isLegal = Helpers::ChkCode($mobile, $vcode);
        if ($isLegal !== true) {
            $this->apiReturn(406, [], '身份验证失败');
        }
        //将验证成功的数据写入
        $data = array('mobile' => $mobile, 'time' => time());

        $cache    = Cache::getInstance('redis');
        $cacheKey = 'reset_data:' . $mobile;
        $cache->set($cacheKey, json_encode($data));

        $this->apiReturn(200, [], '身份验证成功');
    }

    /**
     * 重置密码
     * <AUTHOR>
     * @date   2016-05-20
     *
     * @return
     */
    public function resetPwd()
    {
        $code = 406;
        $data = [];
        $msg  = '密码重置失败';

        $resetPwdDataArr['mobile'] = I('session.reset_mobile');
        $resetPwdDataArr['pwd']    = I('post.pass1');
        $resetPwdDataArr['repwd']  = I('post.pass2');

        $resetPwdCommon = new businesReg();
        $resetRes       = $resetPwdCommon->resetPwdCommon('pc', $resetPwdDataArr);

        if ($resetRes) {
            $code = $resetRes['code'];
            $data = $resetRes['data'];
            $msg  = $resetRes['msg'];
        }
        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 注册保存账号等信息
     * <AUTHOR>
     * @date   2016-05-18
     *
     * @return
     */
    public function account()
    {
        $this->apiReturn(406, [], '接口已关闭');
    }

    /**
     * 注册后选取套餐的版本(从pc端注册)
     * ---修改注册流程 后要选取套餐 确定账号的身份
     * ---现在账号的默认状态为:未审核
     *新需求注册后直接是审核通过状态 （lc留）
     */
    public function accountFromPc()
    {
        $code = 406;
        $data = [];
        $msg  = '注册失败';

        // 接收传参
        $registerDataArr['version']       = I('post.version', 'v1', 'strval');   // 是否是新流程（密码非必填）
        $registerDataArr['company']       = I('post.company', '', 'strval');  // 公司名字
        $registerDataArr['mobile']        = I('post.mobile', '', 'strval');   // 手机号
        $registerDataArr['pwd']           = I('post.pwd', '', 'strval');      //　密码
        $registerDataArr['pwd_needle']    = I('post.pwd_needle', 1, 'intval');      //　密码是否必填
        $registerDataArr['vcode']         = I('post.vcode', '', 'strval');    // 验证码
        $registerDataArr['nickname']      = safetxt(I('post.nickname', '', 'strval'));     // 昵称
        $registerDataArr['company_type']  = safetxt(I('post.company_type', '', 'strval')); // 公司类型
        $registerDataArr['province']      = I('post.province', 0, 'intval');           //　省
        $registerDataArr['city']          = I('post.city', 0, 'intval');               // 市
        $registerDataArr['address']       = safetxt(I('post.address', '', 'strval'));           // 地址
        $registerDataArr['business']      = safetxt(I('post.business', '', 'strval'));          //　经营范围
        $registerDataArr['status']        = 0;                                    // 新需求注册后直接是审核通过状态
        $registerDataArr['dtype']         = 1;                                    // 身份类型 先默认使用 分销商的
        $registerDataArr['business_type'] = I('post.business_type', 0, 'intval'); //业态
        $registerDataArr['com_level']     = I('post.com_level', 0, 'intval'); // 类型及级别

        $captchaCode = I('post.captchaCode', '', 'strval');

        $riskCheck = new ShumeiRiskCheck();
        $mobileCheckResult = $riskCheck->mobileCheck($registerDataArr['mobile'], 1);
        if ($mobileCheckResult['code'] !== 200){
            $this->apiReturn($mobileCheckResult['code'], $mobileCheckResult['data'], $mobileCheckResult['msg']);
        }

        $eventData = [
            'type'  => 'phoneMessage',
            'phone' => md5($registerDataArr['mobile']),
        ];
        $checkRes  = $riskCheck->shumeiCheckWithCaptcha('register', 1, md5($registerDataArr['mobile']), $eventData, $captchaCode);

        if ($checkRes['code'] != 200) {
            $this->apiReturn($checkRes['code'], $checkRes['data'], $checkRes['msg']);
        }

        $registerCommon = new businesReg();
        $regRes         = $registerCommon->registerCommon('pc', $registerDataArr);

        if ($regRes) {
            $code = $regRes['code'];
            $data = $regRes['data'];
            $msg  = $regRes['msg'];
            if ($registerDataArr['dtype'] == 1 && $code == 200) {
                $moduleBiz = new Module();
                //注册后默认给用户添加一个36的免费套餐
                $moduleBiz->addFreePackForRegisterByAccountId($data['account']);
            }
        }
        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 图形验证码的验证
     * <AUTHOR>
     * @date   2016-05-19
     *
     * @return
     */
    private function _authImage()
    {
        $clientImageCode = I('post.auth_code');
        $serverImageCode = I('session.auth_code');

        if (!$serverImageCode || !$clientImageCode) {
            return false;
        }

        if (strtolower($serverImageCode) == strtolower($clientImageCode)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 授权验证
     * <AUTHOR>
     * @date   2016-05-19
     *
     * @return
     */
    private function _auth()
    {
        $clientToken = I('post.token');
        $serverToken = I('session.token');

        if (!$serverToken || !$clientToken) {
            return false;
        }

        if ($clientToken == $serverToken) {
            return true;
        } else {
            return false;
        }
    }

}
