<?php
/**
 * 假期模式，控制平台一些功能的使用
 * <AUTHOR>
 */

namespace Controller\Member;

use Business\PftSystem\VacationModeBiz;
use Library\Controller;

class VacationMode extends Controller
{

    const DRIVER_DIR = 'Vacation';

    //全局规则信息
    private $_globalRule;
    //当前规则
    private $_currentRule;

    private $loginInfo;
    private $_account;

    public function __construct()
    {
        $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo('ajax', false, false);

        //当前登录用户的主账号
        $this->_account         = $this->loginInfo['saccount'];
        $this->_vacationmodeBiz = new VacationModeBiz();
    }

    /**
     * 获取规则
     *
     * @param  string  $identifier  页面标识
     *
     * @return array
     */
    private function _getRule($identifier)
    {
        return $this->_getRuleModel()->getRule($identifier);
    }

    /**
     * 获取假期规则模型
     * @return [type] [description]
     */
    private function _getRuleModel()
    {
        static $Model;
        if (!$Model) {
            $Model = new \Model\Member\VacationMode();
        }

        return $Model;
    }

    /***
     * 根据标识获取是否假日模式
     * <AUTHOR>
     * @date: 2019/1/13
     */
    public function accessLimit()
    {
        $identifier = I('identifier', '', 'strval');
        if (!$identifier) {
            $this->apiReturn(204, [], '参数错误');
        }

        $isAllow = 1;
        $msg     = '';

        $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage($identifier, $this->_account);

        if ($vacationMode === false) {
            $isAllow = 0;
            $msg     = '节假日模式，禁止使用该功能；禁用时间段：1月1号至1月3号，每日的8:00-16:00';
        }

        $return = [
            'is_allow' => $isAllow,
            'msg'      => $msg,
        ];

        $this->apiReturn(200, $return);
    }
}