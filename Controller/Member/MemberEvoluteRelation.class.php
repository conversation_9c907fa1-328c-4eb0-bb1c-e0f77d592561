<?php

/**
 * 新版合作分销商
 * <AUTHOR>  Li
 * @date  2020-10-30
 */

namespace Controller\Member;

use Business\NewJavaApi\Distribution\BatchOperateDistributor;
use Library\Controller;
use Business\Member\MemberEvoluteRelation as evoluteRelation;

class MemberEvoluteRelation extends Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    private $relationBiz = null;
    private $corpKindMap = [0, 1, 2, 3, 4, 5, 6, 7, 8];

    private function getRelationBiz()
    {
        if (!$this->relationBiz) {
            $this->relationBiz = new evoluteRelation();
        }

        return $this->relationBiz;
    }

    /**
     * 新版合作分销商 获取分组列表
     * <AUTHOR>  Li
     * @date  2020-10-30
     */
    public function queryCooperationEvoluteGroupList()
    {
        $sid        = $this->isLogin('ajax');
        $queryType  = I('post.query_type', -1, 'intval');       //查询类型 0：分销商名称，1：账号，2：联系人名称，3：手机号，4：分组名称，5：备注名， 6：联系人电话
        $queryValue = I('post.query_value', '', 'strval');      //查询的值
        $province   = I('post.province', -1, 'intval');         //省份id
        $city       = I('post.city', -1, 'intval');             //市id
        $corpKind   = I('post.corp_kind', '',
            'strval');        //公司类型 0:酒店 1:景区 2:旅行社 8:其他 4:集团 5:OTA 6:政府：其他 多个以逗号隔开
        $pageSize   = I('post.page_size', 10, 'intval');        //分页长度
        $pageNumber = I('post.page', 1, 'intval');              //页码
        $groupSize  = I('post.group_size', 1, 'intval');       //分销商分组页数

        $corpKindArr = [];
        if ($corpKind != '' && in_array($corpKind, $this->corpKindMap)) {
            $corpKindArr = explode(',', $corpKind);
        }

        $result = $this->getRelationBiz()->queryCooperationEvoluteGroupList($sid, $queryType, $queryValue, $province,
            $city, $corpKindArr, $pageSize, $pageNumber, $groupSize);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 新版合作分销商 分销商列表分页
     * <AUTHOR>  Li
     * @date  2020-10-30
     */
    public function queryEvoluteGroupMemberListByGroupId()
    {
        $sid        = $this->isLogin('ajax');
        $groupId    = I('post.group_id', 0, 'intval');      //分组id
        $uuid       = I('post.uuid', '', 'strval');          //页码
        $pageSize   = I('post.page_size', 10, 'intval');    //分页长度
        $pageNumber = I('post.page', 1, 'intval');          //页码

        if (empty($groupId) || empty($uuid)) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getRelationBiz()->queryEvoluteGroupMemberListByGroupId($sid, $groupId, $uuid, $pageSize,
            $pageNumber);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 合作分销商-变更分组-分组列表
     * <AUTHOR>  Li
     * @date  2020-10-30
     */
    public function queryEvoluteGroupSimpleList()
    {
        $sid            = $this->isLogin('ajax');
        $queryCondition = I('post.query_condition', '', 'strval');  //查询信息(名称、账号、手机号)
        $pageSize       = I('post.page_size', 10, 'intval');        //分页长度
        $pageNumber     = I('post.page', 1, 'intval');              //页码
        $groupSize      = I('post.group_size', 10, 'intval');       //分销商分组页数

        //判断是否有值 没有值  置空
        if (!$queryCondition) {
            $queryCondition = null;
        }

        $result = $this->getRelationBiz()->queryEvoluteGroupSimpleList($sid, $queryCondition, $pageSize, $pageNumber,
            $groupSize);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 合作分销商-变更分组-分组列表-分销商列表分页
     * <AUTHOR>  Li
     * @date  2020-10-30
     */
    public function queryEvoluteGroupMemberSimpleListByGroupId()
    {
        $sid        = $this->isLogin('ajax');
        $groupId    = I('post.group_id', 0, 'intval');      //分组id
        $pageSize   = I('post.page_size', 10, 'intval');    //分页长度
        $pageNumber = I('post.page', 1, 'intval');          //页码
        $uuid       = I('post.uuid', '', 'strval');          //页码

        if (empty($groupId) || empty($uuid)) {
            $this->apiReturn(203, [], '分组id缺失');
        }

        $result = $this->getRelationBiz()->queryEvoluteGroupMemberSimpleListByGroupId($sid, $groupId, $uuid, $pageSize,
        $pageNumber);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 分销商列表
     * <AUTHOR>  Li
     * @date  2020-10-30
     */
    public function memberDistributorQueryList()
    {
        $sid        = $this->isLogin('ajax');
        $pageSize   = I('post.page_size', 10, 'intval');        //分页长度
        $pageNumber = I('post.page', 1, 'intval');              //页码
        $fid        = I('post.fid', -1, 'intval');               //分销商id
        $conditions = I('post.conditions',
            '[{"queryType":"0","queryStart":"","queryEnd":""},{"queryType":"1","queryStart":0,"queryEnd":0},{"queryType":"2","queryStart":0,"queryEnd":0},{"queryType":"3","queryStart":0,"queryEnd":0},{"queryType":"4","queryStart":0,"queryEnd":0}]',
            'strval');        //组合查询 [{"queryType":1,"queryStart":100,"queryEnd":200}]
        $corpKind   = I('post.corp_kind', -1, 'intval');         //公司类型
        $groupId    = I('post.group_id', -1, 'intval');           //分组id
        $province   = I('post.province', -1, 'intval');          //省
        $city       = I('post.city', -1, 'intval');              //市
        $orderType  = I('post.order_type', 1, 'intval');        //0：正序，1：倒序
        $orderField = I('post.order_field', 1, 'intval');       //排序字段 0：时间 1：销售情况

        $conditionArr = null;
        if ($conditions) {
            $conditionArr = json_decode($conditions, true);
        }

        $result = $this->getRelationBiz()->memberDistributorQueryList($sid, $fid, $conditionArr, $corpKind,
            $groupId, $province, $city, $orderType, $orderField, $pageSize, $pageNumber, $groupTypeArr = [0,2,4]);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 根据分销商名称模糊查询
     * <AUTHOR>  Li
     * @date  2020-10-30
     */
    public function queryDistributorNameListBySidAndDistributorName()
    {
        $sid        = $this->isLogin('ajax');
        $pageSize   = I('post.page_size', 10, 'intval');    //分页长度
        $pageNumber = I('post.page', 1, 'intval');          //页码
        $fidName    = I('post.fname', '', 'strval');      //分销商名称

        $result = $this->getRelationBiz()->queryDistributorNameListBySidAndDistributorName($sid, $fidName, $pageSize,
            $pageNumber);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 根据分组名称模糊查询
     * <AUTHOR>  Li
     * @date  2020-10-30
     */
    public function queryGroupNameListBySidAndGroupName()
    {
        $sid        = $this->isLogin('ajax');
        $pageSize   = I('post.page_size', 10, 'intval');        //分页长度
        $pageNumber = I('post.page', 1, 'intval');              //页码
        $groupName  = I('post.group_name', '', 'strval');     //分组名称

        $result = $this->getRelationBiz()->queryGroupNameListBySidAndGroupName($sid, $groupName, $pageSize,
            $pageNumber);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 查询变更记录主任务
     * <AUTHOR>  Li
     * @date  2020-11-18
     */
    public function queryEvoluteMainTaskList()
    {
        $sid           = $this->isLogin('ajax');
        $operateStatus = I('post.operate_status', -1, 'intval');    //操作状态：0=未执行，1=进行中，2=部分成功，3=成功，4=失败 不过滤传null或者-1
        $operateType   = I('post.operate_type', -1, 'intval');      //操作类型：0=新增,1=删除,2=变更,3=导出 不过滤传null或者-1
        $operateName   = I('post.operate_name', '', 'strval');      //操作对象 不过滤传null或者""
        $operatorId    = I('post.operator_id', -1, 'intval');       //操作人 不过滤传null或者-1
        $startDate     = I('post.start_date', '', 'strval');        //开始时间  2020-01-01
        $endDate       = I('post.end_date', '', 'strval');          //结束时间  2020-01-01
        $pageNum       = I('post.page', 1, 'intval');               //当前页数
        $pageSize      = I('post.page_size', 10, 'intval');         //每页条数

        $result = $this->getRelationBiz()->queryEvoluteMainTaskList($sid, $operateStatus, $operateType, $operateName,
            $operatorId, $startDate, $endDate, $pageNum, $pageSize);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 查询变更记录子任务
     * <AUTHOR>  Li
     * @date  2020-11-18
     */
    public function queryEvoluteSubTaskList()
    {
        $mainTaskId    = I('post.main_task_id', 0, 'intval');       //主任务id
        $operateStatus = I('post.operate_status', -1, 'intval');    //operateStatus 操作状态：0=未执行，1=执行中，2=成功，3=失败
        $pageNum       = I('post.page', 1, 'intval');               //当前页数
        $pageSize      = I('post.page_size', 10, 'intval');         //每页条数

        if (!$mainTaskId) {
            $this->apiReturn(203, [], '主任务id缺失');
        }

        $result = $this->getRelationBiz()->queryEvoluteSubTaskList($mainTaskId, $operateStatus, $pageNum,
            $pageSize);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 增加导出任务日志
     * <AUTHOR>  Li
     * @date  2020-12-01
     */
    public function addExportTaskLog()
    {
        $loginInfo = $this->getLoginInfo();
        $result    = $this->getRelationBiz()->addExportTaskLog($loginInfo['sid'], $loginInfo['memberID']);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 重试任务
     * <AUTHOR>  Li
     * @date  2020-11-18
     */
    public function retryTask()
    {
        $loginInfo  = $this->getLoginInfo();
        $operatorId = $loginInfo['memberID'];
        $subTaskId  = I('post.sub_task_id', 0, 'intval');   //子任务id

        if (empty($subTaskId)) {
            $this->apiReturn(203, [], '请选择正确的任务进行重试');
        }

        $result = $this->getRelationBiz()->retryTask($subTaskId, $operatorId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 配置分组下的用户的结算方式/还款方式
     * <AUTHOR>  Li
     * @date  2020-11-19
     */
    public function configGroupClearing()
    {
        $loginInfo    = $this->getLoginInfo();
        $type         = I('post.type', 1, 'intval');            //操作类型 1结算方式 2还款方式
        $clearingMode = I('post.clearing_mode', null);   //结算方式
        $repayMode    = I('post.repay_mode', null);      //还款方式
        $groupIds     = I('post.group_ids');                    //整组配置的分组id 多个以逗号隔开
        $fids         = I('post.fids');                         //分销商id 多个以逗号隔开

        $groupIdArr = [];
        $fidArr     = [];

        if (!$groupIds && !$fids) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        if ($groupIds) {
            $groupIdArr = explode(',', $groupIds);
        }
        if ($fids) {
            $fidArr = explode(',', $fids);
        }

        $result = $this->getRelationBiz()->configGroupClearing($loginInfo['sid'], $type, $clearingMode, $repayMode, $groupIdArr, $fidArr, $loginInfo['memberID']);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 移动分组-混合
     * <AUTHOR>  Li
     * @date  2020-11-19
     */
    public function moveMixEvoluteGroup()
    {
        $loginInfo  = $this->getLoginInfo();
        $newGroupId = I('post.new_group_id', 0, 'intval');    //分组id
        $groupIds   = I('post.group_ids');                                 //整组配置的分组id 多个以逗号隔开
        $fids       = I('post.fids');                                      //分销商id 多个以逗号隔开

        $groupIdArr = [];
        $fidArr     = [];

        if (!$groupIds && !$fids) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        if ($groupIds) {
            $groupIdArr = explode(',', $groupIds);
        }
        if ($fids) {
            $fidArr = explode(',', $fids);
        }

        $evoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $result          = $evoluteGroupBiz->moveMixEvoluteGroup($loginInfo['sid'], $loginInfo['memberID'], $newGroupId,
            $groupIdArr, $fidArr);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 批量移动分组
     * <AUTHOR> lingfeng
     * 2022/5/24 14:04
     */
    public function moveMixGroupDistributor()
    {
        $loginInfo  = $this->getLoginInfo();
        $fidList = I('post.fidList', []);
        $newGroupId = I('post.newGroupId', 0, 'intval');

        $batchOperate = new BatchOperateDistributor();
        $result = $batchOperate->moveMixGroupDistributor($loginInfo['sid'], $fidList, $newGroupId, $loginInfo['memberID']);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 批量查询分组组内成员数
     * <AUTHOR>  Li
     * @date  2020-11-19
     */
    public function queryGroupFidCountByGroupIds()
    {
        $groupIds = I('post.group_ids');    //整租配置的分组id 多个以逗号隔开

        if (!$groupIds) {
            $this->apiReturn(203, [], '缺少分组id');
        }

        $groupIdArr = explode(',', $groupIds);

        $evoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $result          = $evoluteGroupBiz->queryGroupFidCountByGroupIds($groupIdArr);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 恢复通用分组
     * <AUTHOR>  Li
     * @date  2020-11-26
     */
    public function recoverCommonEvoluteGroup()
    {
        $loginInfo = $this->getLoginInfo();
        $fid       = I('post.fid', 0, 'intval');  //分销商id

        if (!$fid) {
            $this->apiReturn(203, [], '缺少分销商id');
        }

        $evoluteGroupBiz = new \Business\Product\Update\EvoluteGroup();
        $result          = $evoluteGroupBiz->recoverCommonEvoluteGroup($loginInfo['sid'], $fid, $loginInfo['memberID']);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 复制分组
     * <AUTHOR>  Li
     * @date  2020-11-30
     */
    public function cloneGroup()
    {
        $loginInfo = $this->getLoginInfo();
        $groupName = I('post.group_name', '', 'strval');         //分组名称
        $originGid = I('post.origin_group_id', 0, 'intval');    //复制原分组id

        if (empty($originGid)) {
            $this->apiReturn(203, [], '请选择源分组');
        }

        if (empty($groupName)) {
            $this->apiReturn(203, [], '请填写分组名称');
        }

        if (mb_strlen($groupName) > 20) {
            $this->apiReturn(203, [], '分组名称长度不能超过20字符');
        }

        $groupBiz = new \Business\Member\DistributorGroup();
        $result   = $groupBiz->cloneGroup($loginInfo['sid'], $groupName, $originGid, $loginInfo['sid']);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 统计移动分组的任务数量
     * <AUTHOR>  Li
     * @date  2021-12-08
     */
    public function countMoveEvoluteGroupTask()
    {
        $loginInfo  = $this->getLoginInfo();
        $newGroupId = I('post.new_group_id', 0, 'intval');    //分组id
        $groupIds   = I('post.group_ids');                                 //整组配置的分组id 多个以逗号隔开
        $fids       = I('post.fids');                                      //分销商id 多个以逗号隔开

        $groupIdArr = [];
        $fidArr     = [];

        if (!$groupIds && !$fids) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        if ($groupIds) {
            $groupIdArr = explode(',', $groupIds);
        }
        if ($fids) {
            $fidArr = explode(',', $fids);
        }

        $evoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $result          = $evoluteGroupBiz->countMoveEvoluteGroupTask($loginInfo['sid'], $loginInfo['memberID'], $newGroupId,
            $groupIdArr, $fidArr);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 配置分组排序
     * <AUTHOR>  Li
     * @date  2022-03-01
     */
    public function configEvoluteGroupOrderBy()
    {
        $loginInfo = $this->getLoginInfo();
        $groupId   = I('post.group_id');                 //整组配置的分组id 多个以逗号隔开
        $moveType  = I('post.move_type', 0, 'intval');   //操作类型 0置顶 1上移 2 下移
        $moveNum   = I('post.move_num', 0, 'intval');    //移动位数

        if (!$groupId || !in_array($moveType, [0, 1, 2]) || !$moveNum) {
            $this->apiReturn(500, [], '参数错误');
        }

        $result = $this->getRelationBiz()->configEvoluteGroupOrderBy($loginInfo['sid'], $groupId, $moveType, $moveNum, $loginInfo['memberID']);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }
}