<?php
/**
 * 集团账号相关接口
 * <AUTHOR>
 * @date   2025/05/31
 */

namespace Controller\Member;

use Library\Controller;
use Business\Member\GroupAccount as GroupAccountBiz;

class GroupAccount extends Controller
{
    private $loginInfo;

    public function __construct()
    {
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 获取集团账号下属成员列表
     * <AUTHOR>
     * @date   2025/05/31
     */
    public function getGroupMembersPaging()
    {
        if ($this->loginInfo['sdtype'] != 7) {
            $this->apiReturn(400, [],  '非集团账号，无权限访问');
        }
        $groupAccountId = $this->loginInfo['sid'];
        $pageNum        = I('post.page', 1, 'intval');
        $pageSize       = I('post.size', 10, 'intval');
        $account        = I('post.account', '', 'strval');
        $comName        = I('post.com_name', '', 'strval');
        $memberName     = I('post.member_name', '', 'strval');
        $biz            = new GroupAccountBiz();
        $res            = $biz->getGroupMembersPaging($groupAccountId, $pageNum, $pageSize, $account, $comName,
            $memberName);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}