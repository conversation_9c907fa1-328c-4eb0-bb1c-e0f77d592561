<?php

namespace Controller\Member;

use Library\Controller;

class SSOClient extends Controller {


    /**
     * sso系统登录后的回调,解析凭证进行当前系统的登录操作
     * <AUTHOR>
     * @date   2020-01-07
     */
    public function loginCallBackForPc()
    {
        $token     = I('token', '');
        $redirect  = I('redirect', '');
        //token解析
        $ssoClient = new \Business\Member\SSOClient();
        $callRes   = $ssoClient->callBackVerify($token, 'pc');
        if ($callRes['code'] != 200) {
            $this->apiReturn($callRes['code'], [], $callRes['msg']);
        }
        //写session
        $sessionBiz = new \Business\Member\Session();
        $sessionRes = $sessionBiz->loginMemberId($callRes['data']['member_id'], 'pc');
        if ($sessionRes['code'] != 200) {
            $this->apiReturn($sessionRes['code'], [], $sessionRes['msg']);
        }

        header("Location:{$redirect}");
        exit();
    }


    /**
     * sso系统登录后的回调,解析凭证进行当前系统的登录操作
     * <AUTHOR>
     * @date   2020-01-07
     */
    public function loginCallBackForZd()
    {
        $token     = I('token', '');
        $redirect  = I('redirect', '');
        //token解析
        $ssoClient = new \Business\Member\SSOClient();
        $callRes   = $ssoClient->callBackVerify($token, 'zd');
        if ($callRes['code'] != 200) {
            $this->apiReturn($callRes['code'], [], $callRes['msg']);
        }
        //写session
        $sessionBiz = new \Business\Member\Session();
        $sessionRes = $sessionBiz->loginMemberId($callRes['data']['member_id'], 'pc');
        if ($sessionRes['code'] != 200) {
            $this->apiReturn($sessionRes['code'], [], $sessionRes['msg']);
        }
        
        $businessSession['ticket'] = $callRes['data']['ticket'];

        //写入业务缓存
        (new \Library\Tools\BusinessCache())->setBusinessCache($businessSession, $callRes['data']['member_id']);

        header("Location:{$redirect}");
        exit();
    }

    /**
     * 接受sso的通知，对当前系统的会话进行注销
     * <AUTHOR>
     * @date   2020-01-07
     */
    public function logoutForPc()
    {
        $postData  = I('post.');
        $ssoClient = new \Business\Member\SSOClient();
        $result    = $ssoClient->logoutAccept($postData, 'pc');
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 接受sso的通知，对当前系统的会话进行注销
     * <AUTHOR>
     * @date   2020-01-07
     */
    public function logoutForZd()
    {
        $postData  = I('post.');
        $ssoClient = new \Business\Member\SSOClient();
        $result    = $ssoClient->logoutAccept($postData, 'zd');
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


}