<?php
/**
 * 客户信息
 * <AUTHOR>
 * @date   2019-01-07
 */

namespace Controller\Member;

use Library\Controller;
use Pimple\Container;
use Library\Tools;
use Library\Tools\Helpers;
use Library\Tools\Vcode;
use Business\Member\Customer as CustomerBus;
use Business\Member\Member as MemberBus;
use Model\Member\Customer as CustomerModel;
use Model\Member\Member as MemberModel;

class Customer extends Controller
{
    private $_loginInfo = [];//登录信息
    private $_isSuper   = false;
    private $_container = [];
    private $_tmpTpl    = 'check_new_mobile';//修改手机号需要的短信模板

    public function __construct() {
        parent::__construct();

        $this->_loginInfo = $this->getLoginInfo('ajax'); //需要用到的登录信息
        $this->_isSuper   = $this->isSuper(); //是否是管理员

        \Library\Tools\Helpers::composerAutoload(); //composer自动加载

        $this->_container = new Container();//模型容器

        $this->_container['customer_business'] = function () {
            return new CustomerBus(); //客户业务层
        };

        $this->_container['member_business'] = function () {
            return new MemberBus(); //用户业务层
        };

        $this->_container['customer_model'] = function () {
            return new CustomerModel(); //客户模型层
        };

        $this->_container['member_model'] = function () {
            return new MemberModel(); //用户模型层
        };

    }

    /**
     * 判断是不是客服
     * <AUTHOR>
     * @date   2019-01-08
     */
    private function _checkModifyPermissions(){
        if(!$this->_isSuper){
            return false;
        }

        $memberInfo = $this->_container['member_business']->getInfo($this->_loginInfo["memberID"] ,true);
        if($memberInfo["position"] === '0'){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 获取客户信息资料
     * <AUTHOR>
     * @date   2019-01-08
     */
    public function getBaseInfo(){
        //管理员可以查看别人
        if($this->_isSuper){
            $customerId = I("customer_id");
        }

        $showBtn = false;//显示保存的按钮
        if(empty($customerId)){
            $memberInfo = $this->_container['member_business']->getInfo($this->_loginInfo["memberID"]);
            $customerId = $memberInfo['customer_id'];
            $showBtn = true;
        }else{
            if($this->_checkModifyPermissions()){
                $showBtn = true;
            }
        }

        //客户资料
        $customerInfo = $this->_container['customer_business']->getCustomerInfo($customerId);
        if(!$customerInfo){
            $this->apiReturn(500, [], '获取客户信息失败');
        }
        if($customerInfo['birthday']){
            $customerInfo['birthday'] = str_pad($customerInfo['birthday'], 4, '0', STR_PAD_LEFT);
        }

        //收集需要返回的信息
        $returnData = [
            'dname'       => $customerInfo['dname'],         //客户名称
            'customer_id' => $customerInfo['customer_id'],   //客户ID
            'sex'         => $customerInfo['sex'] == 1 ? '男' : ($customerInfo['sex'] == 2 ? '女' : '未知'),           //性别：0=未知，1=男，2=女
            'mobile'      => $customerInfo['mobile'],        //联系用手机号
            'birthday'    => $customerInfo['birthday'],      //生日
            'id_card_no'  => $customerInfo['id_card_no'],    //身份证号
            'address'     => $customerInfo['address'],       //住址
            'email'       => $customerInfo['email'],         //账号邮箱
            'cname'       => $customerInfo['cname'],         //联系人姓名
            'ctel'        => $customerInfo['ctel'],          //其他联系电话
            'showBtn'     => $showBtn ? 1 : 0                //显示保存的按钮
        ];
        $this->apiReturn(200, $returnData, '');
    }

    /**
     * 获取客户认证资料
     * <AUTHOR>
     * @date   2019-01-08
     */
    public function getCertificationInfo(){

        if($this->_isSuper){
            $customerId = I("customer_id");
        }

        $showBtn = false;//显示保存的按钮
        if(empty($customerId)){
            $memberInfo = $this->_container['member_business']->getInfo($this->_loginInfo["memberID"]);
            $customerId = $memberInfo['customer_id'];
            $showBtn = true;
        }else{
            if($this->_checkModifyPermissions()){
                $showBtn = true;
            }
        }

        $certificationInfo = $this->_container['customer_business']->getCertificationInfo($customerId, true);
        if($certificationInfo['certi_status'] == 2){
            $showBtn = false;
        }
        //收集需要返回的信息
        $returnData = [
            'customer_id'  => $certificationInfo['customer_id'] ?: $customerId,   //客户id
            'bsn_les'      => $certificationInfo['bsn_les'] ?: '',                //营业执照
            'opt_les'      => $certificationInfo['opt_les'] ?: '',                //经营许可证
            'tax_les'      => $certificationInfo['tax_les'] ?: '',                //税务登记证
            'org_les'      => $certificationInfo['org_les'] ?: '',                //组织机构代码证
            'auth_les'     => $certificationInfo['auth_les'] ?: '',               //资源方授权书
            'certi_status' => $certificationInfo['certi_status'] ?: '0',          //会员认证状态 0=未认证,1=审核中,2=—已认证（审核通过）,3=拒绝'
            'showBtn'      => $showBtn ? 1 : 0                                    //显示保存的按钮
        ];
        $this->apiReturn(200, $returnData, '');
    }

    /**
     * 检查身份证重复
     * <AUTHOR>
     * @date   2019-01-08
     */
    public function checkIdCardNo(){
        $customerId = I("post.customer_id");  //客户id
        $id_card_no = I("post.id_card_no");   //身份证号
        if(!$id_card_no || !$customerId){
            $this->apiReturn(200, [], '检测参数不能为空');
        }

        if(!Tools::personID_format_err($id_card_no)){
            $this->apiReturn(401, [], '身份证校验错误');
        }

        $tmpCustomerId = $this->_container['customer_model']->getCustomerIdByIdCardNo($id_card_no);
        if($tmpCustomerId && $tmpCustomerId != $customerId){
            $this->apiReturn(500, [], '身份证已经被占用');
        }

        $this->apiReturn(200, [], '');
    }

    /**
     * 保存客户基本信息资料
     * <AUTHOR>
     * @date   2019-01-08
     */
    public function saveBaseInfo(){
        $customerId = I("post.customer_id");  //客户id
        $dname      = I("post.dname");        //客户名称
        $sex        = I("post.sex");          //性别 1 2
        $birthday   = I("post.birthday");     //生日
        $id_card_no = I("post.id_card_no");   //身份证号
        $address    = I("post.address");      //住址

        if(!$customerId){
            $this->apiReturn(400, [], '客户标识参数不能为空');
        }

        $memberInfo = $this->_container['member_business']->getInfo($this->_loginInfo["memberID"]);
        if(!$this->_checkModifyPermissions() && $memberInfo['customer_id'] != $customerId){
            $this->apiReturn(401, [], '没有修改的权限');
        };

        if(!$dname){
            $this->apiReturn(400, [], '客户名称不能为空或者性别参数错误');
        }

        if($id_card_no){
            if(!Tools::personID_format_err($id_card_no)){
                $this->apiReturn(400, [], '身份证校验错误');
            }
            $tmpCustomerId = $this->_container['customer_model']->getCustomerIdByIdCardNo($id_card_no);
            if($tmpCustomerId && $tmpCustomerId != $customerId){
                $this->apiReturn(400, [], '身份证已经被占用');
            }
        }

        if($birthday){
            if (!is_numeric($birthday)) {
                $this->apiReturn(400, [], '生日必须为数字');
            }
        }

        $data = [
            'dname'      => (string)$dname,
            'sex'        => intval($sex),
            'birthday'   => (string)$birthday,
            'id_card_no' => (string)$id_card_no,
            'address'    => (string)$address,
        ];

        $customerBiz = new  \Business\Member\Customer();
        $res = $customerBiz->updateCustomerBaseInfo($customerId,$data);
        if($res['code'] == 200){
            $this->apiReturn(200, [], '保存成功');
        }else{
            $this->apiReturn(200, [], '保存失败');
        }
    }

    /**
     * 保存客户联系信息
     * <AUTHOR>
     * @date   2019-01-08
     */
    public function saveContactInfo(){
        $customerId = I("post.customer_id");  //客户id
        $email      = I("post.email");        //账号邮箱
        $cname      = I("post.cname");        //联系人姓名
        $ctel       = I("post.ctel");         //其他联系电话

        if(!$customerId){
            $this->apiReturn(400, [], '客户标识参数不能为空');
        }

        $memberInfo = $this->_container['member_business']->getInfo($this->_loginInfo["memberID"]);
        if(!$this->_checkModifyPermissions() && $memberInfo['customer_id'] != $customerId){
            $this->apiReturn(401, [], '没有修改的权限');
        };

        if(!$email && !$cname && !$ctel){
            $this->apiReturn(400, [], '没有保存的信息');
        }

        if($email){
            if(!Helpers::isEmail($email)){
                $this->apiReturn(400, [], '邮箱格式错误');
            }
        }

        $data = [
            'email' => (string)$email,
            'cname' => (string)$cname,
            'ctel'  => (string)$ctel
        ];

        $customerBiz = new  \Business\Member\Customer();
        $res = $customerBiz->updateCustomerBaseInfo($customerId,$data);
        if($res['code'] == 200){
            $this->apiReturn(200, [], '保存成功');
        }else{
            $this->apiReturn(200, [], '保存失败');
        }
    }

    /**
     * 保存客户认证资料
     * <AUTHOR>
     * @date   2019-01-08
     */
    public function saveCertificationInfo(){
        $customerId = I("post.customer_id");  //客户id
        $bsn_les    = I("post.bsn_les",'');      //营业执照
        $opt_les    = I("post.opt_les",'');      //经营许可证
        $tax_les    = I("post.tax_les",'');      //税务登记证
        $org_les    = I("post.org_les",'');      //组织机构代码证
        $auth_les   = I("post.auth_les",'');     //资源方授权书

        if(!$customerId){
            $this->apiReturn(400, [], '客户标识参数不能为空');
        }

        if(empty($bsn_les)){
            $this->apiReturn(400, [], '三证合一营业执照必须上传');
        }
        if (!$auth_les){
            $this->apiReturn(400, [], '资源方授权书必须上传');
        }
//        if(empty($tax_les)){
//            $this->apiReturn(400, [], '税务登记证是必传的');
//        }
//
//        if(empty($org_les)){
//            $this->apiReturn(400, [], '组织机构代码证是必传的');
//        }

        $memberInfo = $this->_container['member_business']->getInfo($this->_loginInfo["memberID"]);
        if(!$this->_checkModifyPermissions() && $memberInfo['customer_id'] != $customerId){
            $this->apiReturn(401, [], '没有修改的权限');
        };

        $data = [
            'bsn_les'  => (string)$bsn_les,
            'opt_les'  => (string)$opt_les,
            'tax_les'  => (string)$tax_les,
            'org_les'  => (string)$org_les,
            'auth_les' => (string)$auth_les,
        ];
        $res = $this->_container['customer_business']->updateCertificationByCustomerId($customerId, $data, $this->_loginInfo["memberID"]);
        if($res){
            $this->apiReturn(200, [], '保存成功');
        }else{
            $this->apiReturn(500, [], '保存失败');
        }
    }

    /**
     * 发送短信验证码
     * <AUTHOR>
     * @date   2019-01-09
     */
    public function sendVcode()
    {
        $mobile = I('post.mobile');
        $verify = I('post.verify',1);
        if(!$mobile){
            $this->apiReturn(400, [], '手机号不能为空');
        }

        if(!Helpers::isMobile($mobile)) {
            $this->apiReturn(400, [], '手机号码格式不正确');
        }

        //检查黑名单号码
        $black_list    = load_config('black_list');
        $balack_mobile = $black_list['mobile'];
        if (in_array($mobile, $balack_mobile)) {
            $this->apiReturn(400, [], '该手机号已经被加入黑名单。');
        }

        if($verify == 1){
            //检查客户表是否被占用
            $check = $this->_container['member_model']->getCustomerInfo($mobile, 'mobile', $field = 'customer_id');
            if($check){
                $this->apiReturn(400, [], '该手机号已经被占用。');
            }
        }
        
        $data = [
            '{1}'   => '手机号修改',
            'code'  => '{2}'
        ];

        $res = Vcode::sendVcode($mobile, $this->_tmpTpl, $this->_tmpTpl, $data);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '发送验证码成功！');
        } else {
            $this->apiReturn(406, [], $res['msg']);
        }
    }

    /**
     * 旧手机短信验证码验证
     * <AUTHOR>
     * @date   2019-01-09
     */
    public function checkVcode()
    {
        $customerId = I("post.customer_id");
        $code       = I('post.code');
        $mobile     = I('post.mobile');
        if(!$code || !$mobile){
            $this->apiReturn(400, [], '验证码或者手机号不能为空');
        }

        if(!Helpers::isMobile($mobile)) {
            $this->apiReturn(400, [], '手机号码格式不正确');
        }

        //基础判断
        $memberInfo = $this->_container['member_business']->getInfo($this->_loginInfo["memberID"]);
        if(!$memberInfo){
            $this->apiReturn(500, [], '获取登录用户信息失败');
        }

        $checkAuth = $this->_checkModifyPermissions();//客服权限
        if($customerId){
            if(!$checkAuth && $memberInfo['customer_id'] != $customerId){
                $this->apiReturn(401, [], '没有修改的权限');
            };
        }

        //验证码判断
        if($checkAuth){ //客服不要验证码？
            $this->apiReturn(200, [], '验证成功(客服)');
        }else{
            //todo 测试的时候打开
            $res = Vcode::verifyVcode($mobile, $code, $this->_tmpTpl);
            if ($res['code'] != 200) {
                $this->apiReturn(500, [], '验证码输入错误或已经失效');
            }else{
                $this->apiReturn(200, [], '验证成功');
            }
        }
    }

    /**
     * 修改手机号
     * <AUTHOR>
     * @date   2019-01-09
     */
    public function changeMobile(){
        $customerId = I("post.customer_id");
        $code       = I('post.code');
        $mobile     = I('post.mobile');
        if(!$code || !$mobile){
            $this->apiReturn(400, [], '参数错误');
        }

        if(!Helpers::isMobile($mobile)) {
            $this->apiReturn(400, [], '手机号码格式不正确');
        }

        //基础判断
        $memberInfo = $this->_container['member_business']->getInfo($this->_loginInfo["memberID"]);
        if(!$memberInfo){
            $this->apiReturn(500, [], '获取登录用户信息失败');
        }

        $checkAuth = $this->_checkModifyPermissions();//客服权限
        if($customerId){
            if(!$checkAuth && $memberInfo['customer_id'] != $customerId){
                $this->apiReturn(401, [], '没有修改的权限');
            };
        }else{
            $customerId = $memberInfo['customer_id'];
        }

        //验证码判断 todo 后面打开
        if(!$checkAuth){ //客服不要验证码？
            $res = Vcode::verifyVcode($mobile, $code, $this->_tmpTpl);
            if ($res['code'] != 200) {
                $this->apiReturn(406, [], '验证码输入错误或已经失效');
            }
        }

        //最后检查客户表手机号是否被占用
        $check = $this->_container['member_model']->getCustomerInfo($mobile, 'mobile', $field = 'customer_id');
        if($check){
            $this->apiReturn(406, [], '该手机号已经被占用。');
        }

        //更新客户手机号
        $res = $this->_container['customer_business']->updateCustomerMobile($customerId, $mobile ,$this->_loginInfo["memberID"]);
        if ($res !== true){
            $this->apiReturn(500, [], $res);
        }else{
            $this->apiReturn(200, [], '绑定成功');
        }
    }



}