<?php
/**
 * 商户分组
 * <AUTHOR>
 * @date   2020-04-24
 */

namespace Controller\Member;

use Library\Controller;
use Library\Tools\Helpers;

use Business\JavaApi\Member\MemberGroupingFid as MemberGroupingFidBus;
use Business\JavaApi\Member\MemberGrouping as MemberGroupingBus;

class MemberGrouping extends Controller
{
    private $sid;
    private $did;

    public function __construct()
    {
        $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        $this->sid = $loginInfo['memberID'];
        $this->did = $loginInfo['sid'];
    }

    /**
     * 获取用户分组列表  --会员管理 设置分组用
     * Create by xwh
     * Date: 2020/04/28
     *
     */
    public function getMemberGrouping()
    {
        $isSuper = $this->isSuper();
        if (!$isSuper) {
            $this->apiReturn(206, '', '不是管理员');
        }
        $MemberGroupingBus = new MemberGroupingBus();
        $result            = $MemberGroupingBus->queryMemberGrouping($this->sid);
        if ($result) {
            $this->apiReturn(200, $result, '');
        }
    }

    /**
     * 创建/编辑用户分组列表  --会员管理 设置分组用
     * Create by xwh
     * Date: 2020/04/28
     *
     */
    public function createMemberGrouping()
    {
        $isSuper = $this->isSuper();
        if (!$isSuper) {
            $this->apiReturn(206, '', '不是管理员');
        }
        $member_id    = I('post.member_id', 0);
        $group_id     = I('post.group_id', -2);
        $group_name   = I('post.group_name', '', 'strval,trim');
        $old_group_id = I('post.old_group_id', -2, 'intval');
        if (empty($member_id) && $group_id == -2 && empty($group_name)) {
            $this->apiReturn(403, '', '参数错误');
        }
        if (strpos($group_name, '未分组') !== false) {
            $this->apiReturn(400, '', '分组名不能含有“未分组”');
        }
        $MemberGroupingBus    = new MemberGroupingBus();
        $MemberGroupingFidBus = new MemberGroupingFidBus();
        //创建
        if (!empty($group_name) && $group_id == -2) {
            //创建分组并添加分组成员 ---用户未分组
            if (!empty($member_id) && $old_group_id == 0) {
                $result = $MemberGroupingFidBus->addGroupingFid($member_id, $group_name, $this->sid);
                if ($result['code'] == 200 && !empty($result['data'])) {
                    $this->apiReturn(200, $result, $result['msg']);
                } else {
                    $this->apiReturn(400, '', $result['msg']);
                }
            }
            //用户在分组 -删除成员表-创建分组并添加分组成员
            if (!empty($member_id) && $old_group_id != 0) {
                $result = $MemberGroupingFidBus->deleteMemberGroupingByFid($member_id, $this->sid);
                if ($result['code'] == 200) {
                    $result = $MemberGroupingFidBus->addGroupingFid($member_id, $group_name, $this->sid);
                    if ($result['code'] == 200 && !empty($result['data'])) {
                        $this->apiReturn(200, $result, $result['msg']);
                    } else {
                        $this->apiReturn(400, '', $result['msg']);
                    }
                } else {
                    $this->apiReturn(400, '', $result['msg']);
                }
            }
            $result = $MemberGroupingBus->addMemberGrouping($this->sid, $group_name);

            if ($result['code'] == 200 && !empty($result['data'])) {
                $this->apiReturn(200, $result, '保存成功');
            } else {
                $this->apiReturn(400, '', $result['msg']);
            }
        }
        //编辑分组名称
        if (!empty($group_name) && $group_id != -2) {
            $result = $MemberGroupingBus->updateMemberGrouping($group_id, $group_name, $this->sid);
            if ($result['code'] == 200) {
                $this->apiReturn($result['code'], '', $result['msg']);
            } else {
                $this->apiReturn(400, '', $result['msg']);
            }
        }
        $this->apiReturn(403, '', '参数错误');
    }

    /**
     * 设置用户分组  --会员管理 设置分组用
     * Create by xwh
     * Date: 2020/04/28
     *
     */
    public function updateMemberGrouping()
    {
        $isSuper = $this->isSuper();
        if (!$isSuper) {
            $this->apiReturn(206, '', '不是管理员');
        }
        $member_id = I('post.member_id', 0);
        //group_id 0是未分组 前端传-1恢复为未分组
        $old_group_id = I('post.old_group_id', -2, 'intval');
        $new_group_id = I('post.new_group_id', -2, 'intval');
        if (empty($member_id) || $old_group_id == -2 || $new_group_id == -2) {
            $this->apiReturn(403, '', '参数错误');
        }
        $MemberGroupingFidBus = new MemberGroupingFidBus();

        //是否移动到未分组
        if ($old_group_id === -1) {
            $result = $MemberGroupingFidBus->deleteMemberGroupingByFid($member_id, $this->sid);
            if ($result['code'] == 200) {
                $this->apiReturn(200, [], '设置成功');
            } else {
                $this->apiReturn(400, [], '设置失败');

            }
        }
        //添加
        if ($old_group_id === 0) {
            $result = $MemberGroupingFidBus->addMemberGroupingFid($member_id, $new_group_id, $this->sid);
        } else {
            //移动
            $result = $MemberGroupingFidBus->moveMemberGroupingFid($member_id, $old_group_id, $new_group_id,
                $this->sid);
        }

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], $result['msg']);
        }
        $this->apiReturn(400, [], $result['msg']);

    }

    /**
     * 删除用户分组列表
     * Create by xwh
     * Date: 2020/04/28
     *
     */
    public function delMemberGrouping()
    {
        $isSuper = $this->isSuper();
        if (!$isSuper) {
            $this->apiReturn(206, '', '不是管理员');
        }
        $group_id = I('post.group_id', 0);

        if (empty($group_id)) {
            $this->apiReturn(403, '', '参数错误');
        }
        $MemberGroupingFidBus = new MemberGroupingFidBus();
        $MemberGroupingBus    = new MemberGroupingBus();
        //删除分组标签
        $result = $MemberGroupingBus->deleteMemberGrouping($group_id, $this->sid);

        if ($result['code'] == 200) {
            //删除分组所有成员
            $result = $MemberGroupingFidBus->deleteMemberGroupingByAllFid($group_id, $this->sid);
            if ($result['code'] == 200) {
                $this->apiReturn(200, [], $result['msg']);
            }

            $this->apiReturn(400, [], $result['msg']);
        } else {
            $this->apiReturn(400, [], $result['msg']);
        }

    }

    /**
     * 恢复分组标签
     * Create by xwh
     * Date: 2020/04/28
     *
     */
    public function recoverMemberGrouping()
    {
        $isSuper = $this->isSuper();
        if (!$isSuper) {
            $this->apiReturn(206, '', '不是管理员');
        }
        $group_id = I('post.group_id', 0);

        if (empty($group_id)) {
            $this->apiReturn(403, '', '参数错误');
        }
        $MemberGroupingBus = new MemberGroupingBus();
        //删除分组标签
        $result = $MemberGroupingBus->recoverMemberGrouping($group_id, $this->sid);

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], $result['msg']);
        } else {
            $this->apiReturn(400, [], $result['msg']);
        }

    }

}