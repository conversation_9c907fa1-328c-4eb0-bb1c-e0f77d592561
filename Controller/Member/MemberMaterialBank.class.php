<?php
/**
 * 用户素材库
 * <AUTHOR>
 * @date   2019-10-21
 */

namespace Controller\Member;

use Library\Controller;

use Library\Tools\Helpers;
use Library\Tools\ImgCompress;
use Library\Upload;
use \Model\Member\MemberMaterialBank as MemberMaterialBankMode;
use \Business\Member\MemberMaterialBank as MemberMaterialBankBz;

class MemberMaterialBank extends Controller
{

    /**
     * 创建图片分组
     * <AUTHOR>
     * @date   2019/10/21
     */
    public function createGroup()
    {
        $sid       = $this->isLogin('ajax');
        $groupName = I('post.groupName', '', 'strval');
        $remark    = I('post.remark', '', 'strval');
        if (empty($groupName)) {
            $this->apiReturn(203, [], '分组名称不能为空!');
        }
        $insertData             = [
            'sid'         => $sid,
            'group_name'  => $groupName,
            'remark'      => $remark,
            'create_time' => time(),
        ];
        $memberMaterialBankMode = new MemberMaterialBankMode();
        $res                    = $memberMaterialBankMode->insertOrUpdateGroup($insertData);
        if ($res === false) {
            $this->apiReturn(400, [], '创建分组失败');
        }
        $return = [
            'id' => $res,
        ];
        $this->apiReturn(200, $return, '创建分组成功');
    }

    /**
     * 获取供应商下的所有图片素材库分组信息
     * <AUTHOR>
     * @date   2019/10/21
     */
    public function getGroupBySid()
    {
        $sid                 = $this->isLogin('ajax');
        $memberMaterialBanBz = new MemberMaterialBankBz();
        $res                 = $memberMaterialBanBz->getGroupBySid($sid);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 删除分组
     * <AUTHOR>
     * @date   2019/10/21
     */
    public function deleteGroup()
    {
        $sid     = $this->isLogin('ajax');
        $groupId = I('post.groupId', 0, 'intval');
        if (empty($groupId)) {
            $this->apiReturn(203, [], '参数错误');
        }
        $memberMaterialBankMode = new MemberMaterialBankMode();
        $res                    = $memberMaterialBankMode->deleteGroup($groupId, $sid);
        if ($res === false) {
            $this->apiReturn(400, [], '删除分组失败');
        }
        //删除分组中的图片
        $memberMaterialBankMode->deleteImageByGroupId($groupId, $sid);
        $this->apiReturn(200, [], '删除分组成功');
    }

    /**
     * 编辑某个分组的信息
     *
     * <AUTHOR>
     * @date   2019/10/21
     */
    public function editGroupInfo()
    {
        $sid       = $this->isLogin('ajax');
        $groupId   = I('post.groupId', 0, 'intval');
        $groupName = I('post.groupName', '', 'strval');
        if (empty($groupId)) {
            $this->apiReturn(203, [], '参数错误');
        }
        $data                   = [
            'group_name' => $groupName,
        ];
        $memberMaterialBankMode = new MemberMaterialBankMode();
        $res                    = $memberMaterialBankMode->insertOrUpdateGroup($data, true, $groupId, 'id', $sid);
        if ($res === false) {
            $this->apiReturn(400, [], '修改失败');
        }

        $this->apiReturn(200, [], '修改成功');
    }

    /**
     * 保存图片前校验图片名称
     *
     * <AUTHOR>
     * @date   2019/10/22
     */
    public function checkImageName()
    {
        $sid          = $this->isLogin('ajax');
        $imageNameArr = I('post.imageName', '', 'strval');
        if (empty($imageNameArr)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $memberMaterialBankMode = new MemberMaterialBankMode();
        //校验图片名称是否存在重名
        $result = $memberMaterialBankMode->checkImageName($imageNameArr, $sid);

        if ($result) {
            $returnData = array_column($result, 'image_name');
            $this->apiReturn(400, $returnData, '该分组下已存在该名称');
        }

        $this->apiReturn(200, [], '名称可用');
    }

    /**
     * 保存图片到对应的分组
     *
     * <AUTHOR>
     * @date   2019/10/21
     */
    public function saveImage()
    {
        $sid       = $this->isLogin('ajax');
        $imageUrl  = I('post.imageUrl', '', 'strval');
        $groupId   = I('post.groupId', 0, 'intval');
        $imageName = I('post.imageName', '', 'strval');
        $imageSize = I('post.imageSize', 0);
        if (empty($imageName) || empty($imageUrl)) {
            $this->apiReturn(203, [], '参数错误');
        }
        $memberMaterialBankBz = new MemberMaterialBankBz();
        $result               = $memberMaterialBankBz->saveImage($sid, $imageUrl, $imageName, $groupId, $imageSize);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 获取某个分组的的信息详情
     * <AUTHOR>
     * @date   2019/10/21
     */
    public function getGroupInfoDetail()
    {
        $sid      = $this->isLogin('ajax');
        $groupId  = I('get.groupId', 0, 'intval');      //分组id
        $pageNum  = I('get.pageNum', 1, 'intval');
        $pageSize = I('get.pageSize', 10, 'intval');
        $keyWord  = I('get.keyWord', '', 'strval');
        $type     = I('get.type', 1, 'intval');  //分组类型 1：用户自己的分组  2：系统默认分组
        $memberMaterialBankBz = new MemberMaterialBankBz();
        $result               = $memberMaterialBankBz->getGroupInfoDetail($sid, $groupId, $type, $pageNum, $pageSize, $keyWord);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 对分组中的图片进行操作
     * <AUTHOR>
     * @date   2019/10/22
     */
    public function actionForImage()
    {
        $sid       = $this->isLogin('ajax');
        $action    = I('get.action', '', 'strval');         //操作类型
        $groupId   = I('get.groupId', 0, 'intval');         //分组id
        $imageIds  = I('get.imageIds', '', 'strval');       //图片id字符串
        $toGroupId = I('get.toGroupId', -1, 'intval');      //目标分组id
        $imageName = I('get.imageName', '', 'strval');      //图片名称
        if (empty($imageIds) || empty($action) || $groupId < 0) {
            $this->apiReturn(203, [], "参数错误");
        }
        $imageIdArr             = explode(',', $imageIds);
        $memberMaterialBankMode = new MemberMaterialBankMode();
        $count                  = count($imageIdArr);
        switch ($action) {
            case 'delete':
                $res = $memberMaterialBankMode->deleteImage($imageIdArr, $sid);
                if (!$res) {
                    $this->apiReturn(400, [], "删除失败");
                }
                //减少分组的图片数量
                $memberMaterialBankMode->setIncOrSetDecGroupImageNum($groupId, false, $count);
                $this->apiReturn(200, [], "删除成功");
                break;
            case 'move_group':
                //批量的操作图片
                if ($groupId == $toGroupId) {
                    $this->apiReturn(400, [], "图片已在该目录下无需移动");
                }
                $updateData = [
                    'group_id' => $toGroupId,
                ];
                $res        = $memberMaterialBankMode->updateImageInfo($imageIdArr, $sid, $groupId, $updateData);
                if (!$res) {
                    $this->apiReturn(400, [], "移动失败");
                }
                //减少原数组图片数量，增加目标分组的图片数量
                $memberMaterialBankMode->setIncOrSetDecGroupImageNum($groupId, false, $count);
                $memberMaterialBankMode->setIncOrSetDecGroupImageNum($toGroupId, true, $count);
                $this->apiReturn(200, [], "移动成功");
                break;
            default:
                //单张图片编辑
                $updateData = [
                    'image_name' => $imageName,
                ];
                $result     = $memberMaterialBankMode->checkImageName([$imageName], $sid);
                if ($result) {
                    $this->apiReturn(400, [], "图片名称已存在");
                }
                //检测图片是否重名

                if ($groupId != $toGroupId && $toGroupId != -1) {
                    $updateData['group_id'] = $toGroupId;
                }
                $res = $memberMaterialBankMode->updateImageInfo($imageIdArr, $sid, $groupId, $updateData);
                if ($res && $toGroupId != -1) {
                    //减少原数组图片数量，增加目标分组的图片数量
                    $memberMaterialBankMode->setIncOrSetDecGroupImageNum($groupId, false, $count);
                    $memberMaterialBankMode->setIncOrSetDecGroupImageNum($toGroupId, true, $count);
                }
                $this->apiReturn(200, [], "操作成功");
        }
    }

    /**
     * 单张上传图片到阿里云
     * <AUTHOR>
     * @date   2019/10/23
     */
    public function uploadImage()
    {
        $uploadImageType = I('post.upload_image_type', 'default', 'strval');
        //接收图片
        $upload          = new Upload();
        $upload->maxSize = 1048576;  //限制为1M
        if ($uploadImageType == 'material_center') {
            $upload->maxSize = 1048576 * 10;  //限制为10M
        }

        $upload->exts = ['jpg', 'gif', 'png', 'jpeg', ''];  //允许上传的文件后缀
        $image        = $upload->uploadOne($_FILES['image']);
        if (!$image) {
            $this->apiReturn(400, [], "图片上传失败{$upload->getError()}");
        }

        $src       = IMAGE_UPLOAD_DIR . $image['savename'];
        $deal      = I('post.deal', 'pass', 'strval');  //handle:需要阿里处理      pass：不需要不处理
        $newSrc    = IMAGE_UPLOAD_DIR . $image['name'];
        $allParams = I('param.'); //前端请求的参数，在新增特产产品属性中，上传图片需要使用到，将前端上传的参数原样返回即可 -- 陈华建

        [$width, $height, $type, $attr] = getimagesize($src);
        if (stripos(image_type_to_extension($type, false), 'webp') !== false) {
            $this->apiReturn(400, [], "暂不支持webp图片类文件上传");
        }

        if (empty($image['ext'])) {
            $image['ext'] = image_type_to_extension($type, false);
        }

        if (empty($image['ext']) || $upload->getError()) {
            pft_log('upload_image_error',
                json_encode($upload->getError()) . "参数为：" . json_encode($image) . "图片后缀名为：" . $image['ext']);
            $this->apiReturn(400, [], "不支持此类型上传文件(" . $upload->getError() . ")");
        }

        $zipFlag = false;
        //如果不需要处理则调用阿里的压缩，否则调用本地的压缩
        if ($image['ext'] != 'gif' && $deal == 'pass' && $uploadImageType != 'material_center') {
            //压缩图片
            $imgCompress = new ImgCompress($src, 1);
            $newImage    = $imgCompress->compressImg($newSrc);
        } else {
            $newSrc  = $src;
            $zipFlag = 'format_webp';
        }

        //上传图片到阿里云
        $prefix = 'material_bank';
        if ($uploadImageType == 'material_center') {
            $prefix = 'material_center';
        }

        $memberMaterialBankBz = new MemberMaterialBankBz();
        $uploadRes            = $memberMaterialBankBz->uploadToOssByLocalImage($newSrc, $prefix, $image['ext'],
            $zipFlag);

        if ($uploadRes['code'] != 200) {
            $this->apiReturn($uploadRes['code'], [], $uploadRes['msg']);
        }

        //返回的图片url
        $url = $uploadRes['data']['url'];

        $size = $memberMaterialBankBz->getFileSize($url);
        if (file_exists($src)) {
            unlink($src);
        }

        $returnData = [
            'size'   => $size,
            'url'    => $url,
            'params' => $allParams,
        ];

        $this->apiReturn(200, $returnData, "上传成功");
    }

    /**
     * 上传视频到阿里云oss
     * <AUTHOR>
     * @date 2021/1/20
     *
     * @return array
     */
    public function uploadVideo()
    {
        if (empty($_FILES['video'])) {
            $this->apiReturn(400, [], "请求上传视频！");
        }

        //接收视频
        $memberMaterialBankBz = new MemberMaterialBankBz();
        $upload               = new Upload();

        $upload->maxSize = 1048576 * 15;  //限制为15M
        $upload->exts    = ['mp4', 'AVI', 'mov', 'rmvb', 'rm', 'ogv', 'webm', 'wav', 'ogg'];  //允许上传的文件后缀
        $video           = $upload->uploadOne($_FILES['video']);
        if (!$video) {
            $this->apiReturn(400, [], "视频上传失败{$upload->getError()}");
        }

        $src = IMAGE_UPLOAD_DIR . $video['savename'];
        //$newSrc          = IMAGE_UPLOAD_DIR . $video['name'];

        //上传到阿里云
        $result = Helpers::uploadImage2AliOss('material_bank', $src, 'file', false, false, $video['ext']);

        if (file_exists($src)) {
            unlink($src);
        }

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $url = $result['data']['src'];

        if (ENV == 'TEST') {
            $url = str_replace('pft-scenic.oss-cn-hangzhou.aliyuncs.com', 'pft-scenic.12301.cc',
                $result['data']['src']);
        }
        if (ENV == 'PRODUCTION') {
            $url = str_replace('oss-cn-hangzhou.aliyuncs.com', '12301.cc', $result['data']['src']);
        }
        if (ENV == 'LOCAL' || ENV == 'DEVELOP') {
            $url = str_replace('pft-scenic.oss-cn-hangzhou.aliyuncs.com', 'pft-scenic.12301.cc',
                $result['data']['src']);
        }

        $size       = $memberMaterialBankBz->getFileSize($result['data']['src']);
        $returnData = [
            'size' => $size,
            'url'  => $url,
        ];

        $this->apiReturn($result['code'], $returnData, "上传成功");
    }
}
