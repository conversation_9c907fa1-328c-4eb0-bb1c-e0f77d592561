<?php
/**
 * 会员到期提醒，充值续费
 * <AUTHOR>
 */

namespace Controller\Member;

use Business\AppCenter\Renew as RenewBiz;
use Library\Cache\Cache;
use Library\Controller;
use Model\Member;

class Renew extends Controller
{

    const __WARNING_DAYS__ = 31; //提前多少天提醒

    /**
     * 设置session标识
     */
    public function setFlag()
    {
        $loginInfo = $this->getLoginInfo();

        $renewBiz = new RenewBiz();
        return $renewBiz->setProtocolFlag($loginInfo['sid'], $loginInfo['sdtype'], $loginInfo['sstatus'], $isForce = true);
    }

    /**
     * 获取套餐续费列表
     * @return void
     */
    public function getRenewMeal()
    {
        $memberId = $this->isLogin('ajax');
        //默认套餐
        $list = (new \Model\Member\Renew())->getRenewMeal();
        //特殊套餐
        $memberBiz = new \Business\Member\Member();
        $extInfo = $memberBiz->getMemberExtInfoGetFieldToJava($memberId, 'protocol_meal');
        //$extInfo = (new \Model\Member\Member())->getMemberExtInfo($memberId, 'protocol_meal');

        if ($extInfo) {
            $extInfo   = json_decode($extInfo, true);
            $extMeal[] = [
                'id'    => 'custom',
                'name'  => '会员协议套餐',
                'year'  => 1,
                'month' => 0,
                'money' => $extInfo[1],
                'desc'  => '有效期一年',
            ];
            $list = array_merge($extMeal, $list);
        }

        $this->apiReturn(200, $list);

    }

    /**
     * 是否完成续费
     * @return boolean [description]
     */
    public function isRenewComplete()
    {
        $outTradeNo = I('outTradeNo');

        if (!$outTradeNo) {
            $this->apiReturn(204, [], '参数错误');
        }

        $log = (new \Model\TradeRecord\OnlineTrade())->getLogByOrderId($outTradeNo);
        if (!$log) {
            $this->apiReturn(204, [], '续费记录不存在');
        }

        if ($log['status'] == 1) {
            $this->setFlag();
            $payStatus = 1;
        } else {
            $payStatus = 0;
        }

        $this->apiReturn(200, ['payStatus' => $payStatus]);
    }

    /**
     * 账户余额续费检测
     *
     * @return [type] [description]
     */
    public function renewByBalanceCheck()
    {
        //选择的套餐
        $meal     = I('meal');
        $memberId = $this->isLogin('ajax');

        //获取账户余额
        $balance = (new \Model\Member\Member())->getMoney($memberId, 0);
        //获取套餐金额
        $renewMny = (new \Model\Member\Renew())->gitMealMoney($meal, $memberId);

        $return = [
            'balance' => $balance,
        ];
        if ($balance < $renewMny) {
            $return['enough'] = 0;
        } else {
            $return['enough'] = 1;
        }

        $this->apiReturn(200, $return);

    }

    /**
     * 账户余额续费 - 这个接口已经废弃
     *
     * @return [type] [description]
     */
    public function renewByBalance()
    {
        //请求日志
        $logData = json_encode($_REQUEST, JSON_UNESCAPED_UNICODE);
        @pft_log('wepay/renew', $logData);

        $this->apiReturn(204, [], '续费失败');
    }

    /**
     * 访问白名单(欠费后仍可使用的功能)
     *
     * @return [type] [description]
     */
    private function _getAccessWhiteList()
    {
        return [
            '/home.html',
            '/new/renewwarning.html',
            '/new/renewwarning_index.html',
            '/r/Member_Renew/getRenewMeal/',
            '/r/pay_WxPay/renew/',
            '/r/pay_Alipay/renew/',
            '/r/Member_Renew/isRenewComplete',
            '/recharge.html',
            '/r/pay_Alipay/recharge',
            '/r/Member_Renew/renewByBalance',
            '/r/Member_Renew/renewByBalanceCheck',
            '/union/recharge.php',
        ];

    }

}
