<?php
/**
 * 和分销商审核有关系的
 * <AUTHOR>
 * @date   2018-08-23
 */

namespace Controller\Member;

use Library\Controller;
use Model\Member\Member;
use function PHPSTORM_META\type;

class DistributorAudit extends Controller
{
    private $_memberId; //会员id
    private $_sid;       //上级ID
    private $_type;      //登录账号类型
    private $_qx;        //有关权限的字符串
    private $_name;      //名字
    private $_account;   //账号

    public function __construct()
    {
        $loginInfo       = $this->getLoginInfo();   //登录信息
        $this->_memberId = $loginInfo['memberID'];  //会员id
        $this->_sid      = $loginInfo['sid'];       //上级ID
        $this->_type     = $loginInfo['dtype'];     //登录账号类型
        $this->_qx       = $loginInfo['qx'];        //有关权限的字符串
        $this->_name     = $loginInfo['dname'];
        $this->_account  = $loginInfo['saccount'];
    }

    /**
     * 获取被添加成为分销商审核状态
     * <AUTHOR>
     * @date   2018-08-20
     */
    public function getAuditStatus()
    {
        $dtype    = $this->_type;      //登录账号类型
        $memberID = $this->_memberId;  //会员id
        if (!in_array($dtype, [0, 1])) {          //只能供应商 和分销商
            $this->apiReturn(401, '', '没有权限');
        }

        if ($dtype == 6) {
            $check = 0;
        } else {
            $check = 1;
        }

        $memberBiz = new \Business\Member\Member();
        $status    = $memberBiz->getMemberExtInfoGetFieldToJava($memberID, 'distribution_check');
        $return    = [
            'show'   => $check, //给前端判断显示按钮(员工不显示)
            'status' => $status //被添加为分销商是否需要审核：0=不要，1=要
        ];

        $this->apiReturn(200, $return, '查询成功');
    }

    /**
     * 设置被添加成为分销商审核状态
     * <AUTHOR>
     * @date   2018-08-20
     *
     * @param  $status int 审核状态值：0=不要，1=要
     */
    public function setAuditStatus()
    {
        $dtype    = $this->_type;      //登录账号类型
        $memberID = $this->_memberId;  //会员id
        if (!in_array($dtype, [0, 1])) {          //只能供应商 和分销商
            $this->apiReturn(401, '', '没有权限');
        }

        $status = I('status'); //否需要审核：0=不要，1=要
        if ($status === "") {
            $this->apiReturn(400, '', '参数不能为空');
        }

        if (!in_array($status, [0, 1])) {
            $this->apiReturn(400, '', '参数不合法');
        }

        $Member = new Member();
        $data   = [
            'distribution_check' => $status,
        ];

        $memberBiz = new \Business\Member\Member();
        $res       = $memberBiz->updateMemberExtInfo($memberID, $data);
        if ($res) {
            $this->apiReturn(200, ['status' => $status], '保存成功');
        } else {
            $this->apiReturn(500, ['status' => $status], '保存失败');
        }
    }

    /**
     * 获取首页合作审核关系列表
     * <AUTHOR>
     * @date   2018-08-23
     *
     */
    public function getHomeAuditList()
    {
        $sid   = $this->_sid;  //上级ID
        $dtype = $this->_type; //登录账号类型
        $qx    = $this->_qx;   //权限判断的字符串

        if ($dtype == 6) {  //员工的情况
            $Audit = new \Business\Member\DistributorAudit();
            $check = $Audit->checkStaffDistributorAuth($qx);
        } else {
            $check = true;
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->findMemberRelationshipApply(0, $this->_sid, $this->_sid, '', '', '', 0,
            0, '', 1, 'id', 1, 5, true);

        if ($result['code'] != 200) {
            $this->apiReturn(500, '', '查询失败');
        }

        $result['data']['auth'] = $check ? 1 : 0;  //权限是否灰置的判断
        $this->apiReturn(200, $result['data'], '查询成功');
    }

    private function _handleFormatData($type, $data, $sid)
    {
        $list = [];

        foreach ($data as $key => $value) {
            $receiveMobile = $data['fid'] == 0 ? $value['push_phone'] : $value['receive_mobile'];
            if ($type == 0) {
                if ($value['fid'] == $sid) {  //我是分销商  显示的企业名称是供应商才对
                    $name     = $value['invite_company_name'] . '/' . $value['invite_account'];
                    $relation = $value['invite_name'] . '/' . $value['invite_mobile'];
                    $mode     = 1;
                } else {    //显示分销商的名字
                    $name     = $value['receive_company_name'] . '/' . $value['receive_account'];
                    $mode     = 2;
                    $relation = $value['receive_name'] . '/' . $receiveMobile;
                }
            } elseif ($type == 1) {
                $name     = $value['invite_company_name'] . '/' . $value['invite_account'];
                $relation = $value['invite_name'] . '/' . $value['invite_mobile'];
                $mode     = 1;
            } elseif ($type == 2) {
                $name     = $value['receive_company_name'] . '/' . $value['receive_account'];
                $mode     = 2;
                $relation = $value['receive_name'] . '/' . $receiveMobile;
            }

            $list[] = [
                'id'       => $value['id'],
                'name'     => $name ? $name : '',
                'add_time' => $value['add_time'],
                'mode'     => $mode ? $mode : 0,
                'relation' => $relation ? $relation : '',
                'status'   => $value['status'],
                'aid'      => $value['aid'],
                'fid'      => $value['fid'],
            ];
        }

        return $list;
    }

    /**
     * 再次发送邀请
     * <AUTHOR>
     * @date   2018-08-23
     */
    public function reSendInviteSms()
    {
        $id = I('post.id');
        if (!$id) {
            $this->apiReturn(204, '', '参数错误');
        }
        
        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->reSendInviteSms($id, $this->_sid, $this->_name, $this->_memberId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 获取合作关系审核列表
     * <AUTHOR> Li
     * @date  2020-08-04
     */
    public function findMemberRelationshipApply()
    {
        $type       = I('post.type', 0, 'intval'); //搜索类型  1 企业名称 2 企业联系人 3 手机号
        $identify   = I('post.identify', '', 'strval');
        $inviteType = I('post.invite_type', 0, 'intval'); //邀请类型;1 发送,2 接收
        $applyType  = I('post.apply_type', 0, 'intval'); //申请类型;1 查询添加,2 邀请添加,3 创建添加
        $client     = I('post.client', 0, 'intval'); //操作端: 1 微平台小程序,2 PC,3 微平台H5
        $stat       = I('post.status', 0, 'intval'); //审核状态: 1 待审核,2 已同意,3 已拒绝,-1 已失效
        $sid        = I('post.sid', 0, 'intval'); //搜索供应商id
        $fid        = I('post.fid', 0, 'intval'); //搜索分销商id
        $page       = I('post.page', 1, 'intval'); //当前页数
        $size       = I('post.size', 10, 'intval'); //每页条数

        $dtype = $this->_type; //登录账号类型
        $qx    = $this->_qx;   //权限判断的字符串

        if ($dtype == 6) {  //员工的情况
            $Audit = new \Business\Member\DistributorAudit();
            $check = $Audit->checkStaffDistributorAuth($qx);
            if ($check === false) {
                $this->apiReturn(401, '', '没有权限');
            }
        } else {
            $check = true;
        }

        $comName        = '';
        $cname          = '';
        $ctel           = '';
        $searchMemberId = null;

        if ($identify) {
            $memberBiz = new \Business\Member\Member();
            switch ($type) {
                case 1:
                    //通过企业名称查询用户id
                    $comName = $identify;
                    break;
                case 2:
                    //通过企业账号查询用户id
                    $memberInfo     = $memberBiz->getMemberInfoByMulti([$identify], 'account');
                    if (!$memberInfo) {
                        $this->apiReturn(204, [], '暂无数据');
                    }
                    $searchMemberId = $memberInfo[0]['id'];
                    break;
                    //手机号搜索存在未注册账号
                case 3:
//                    //通过手机号查询用户id
//                    $memberInfo = $memberBiz->getMemberInfoByMulti([$identify], 'mobile');
//                    if (!$memberInfo) {
//                        $this->apiReturn(204, [], '暂无数据');
//                    }
//                    $searchMemberId = $memberInfo[0]['id'];
                    $ctel = $identify;
                    break;
            }
        }

        if ($client) {
            switch ($client) {
                case 1:
                    $client = '微平台小程序';
                    break;
                case 2:
                    $client = 'PC';
                    break;
                case 3:
                    $client = '微平台H5';
                    break;
                case 4:
                    $client = 'APP';
                    break;
            }
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->findMemberRelationshipApply($sid, $fid, $this->_sid, $comName, $cname, $ctel,
            $inviteType,
            $applyType, $client, $stat, 'id', $page, $size, true, $searchMemberId);

        $result['data']['auth'] = $check ? 1 : 0;  //权限是否灰置的判断

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(204, [], $result['msg']);
    }

    /**
     * 批量同意或拒绝 合作关系
     * <AUTHOR> Li
     * @date  2020-08-04
     */
    public function checkCreateMemberRelationBatch()
    {
        $auditIds = I('post.audit_ids'); // 审核id 多个以逗号隔开
        $status   = I('post.status', 0, 'intval');   // 审核状态 1 拒绝 2通过
        $dtype    = $this->_type;            //登录账号类型
        $qx       = $this->_qx;              //权限判断的字符串

        if (empty($auditIds) || empty($status)) {
            $this->apiReturn(400, [], '缺少审核信息');
        }

        //员工权限
        $Audit = new \Business\Member\DistributorAudit();
        if ($dtype == 6) {   //员工的情况
            $check = $Audit->checkStaffDistributorAuth($qx);
            if ($check === false) {  //没有同意的权限
                $this->apiReturn(401, '', '没有权限');
            }
        }

        if ($status == 2) {
            $pass = true;
        } else {
            $pass = false;
        }

        $auditIdArr = explode(',', $auditIds);

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->checkCreateMemberRelationBatch($auditIdArr, $this->_sid, $pass);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], $result['msg']);
    }

    /**
     * 失效/激活建立组织的供销关系申请
     * <AUTHOR> Li
     * @date  2020-08-04
     */
    public function invalidOrActiveApply()
    {
        $auditId = I('post.audit_id'); // 审核id
        $state   = I('post.status', 0, 'intval');   // 审核状态 1 失效 2 待审核
        $dtype   = $this->_type;            //登录账号类型
        $qx      = $this->_qx;              //权限判断的字符串

        if (!$auditId || !$state) {
            $this->apiReturn(400, '', '参数不能为空');
        }

        if (in_array($state, [1, 2])) {
            if ($state == 1) {
                $status = -1;
            } else {
                $status = 1;
            }
        } else {
            $this->apiReturn(400, [], '状态码错误');
        }

        //员工权限
        $Audit = new \Business\Member\DistributorAudit();
        if ($dtype == 6) {   //员工的情况
            $check = $Audit->checkStaffDistributorAuth($qx);
            if ($check === false) {  //没有同意的权限
                $this->apiReturn(401, '', '没有权限');
            }
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->invalidOrActiveApply($auditId, $status, $this->_memberId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], $result['msg']);
    }

    /**
     * 查看审核记录申请用户详情
     * author queyourong
     * date 2022/6/28
     */
    public function queryAuditDetail()
    {
        $auditId = I('audit_id'); // 审核id

        $result = (new \Business\Member\DistributorAudit())->queryAuditDetail($auditId, $this->_memberId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], $result['msg']);
    }
}