<?php
/**
 * 用户授信
 * <AUTHOR>
 * @date   2017-03-01
 */

namespace Controller\Member;

use Business\Finance\AmountLedgerFreeze as AmountLedgerFreezeBiz;
use Business\JavaApi\Account\AccountBook;
use Business\JavaApi\Member\MemberRelationshipRemark;
use Library\Constants\Account\BookSubject;
use Library\Controller;
use Model\Finance\SettleBlance;
use Model\Member;
use Business\Finance\Credit;

class MemberCredit extends Controller
{
    private $_sid       = 0;
    private $_memberID  = 0;
    private $_loginInfo = null;
    private $_memberModel;

    public function __construct()
    {
        $this->_loginInfo   = $this->getLoginInfo();
        $this->_sid         = $this->_loginInfo['sid'];
        $this->_memberID    = $this->_loginInfo['memberID'];
        $this->_memberModel = new Member\Member();
    }

    /**
     * 获取后结模式超大授信额
     * @return
     */
    public function getUnlimitCredit()
    {
        static $unlimitCredit;
        if (!$unlimitCredit) {
            $unlimitCredit = load_config('credit_unlimit', 'trade_record');
        }

        return $unlimitCredit;
    }

    /**
     * 生成修改授信的流水ID
     *
     * @param  string  $timestamp  时间戳
     *
     * @return string
     */
    public static function genChangeCreditOrderId($timestamp = '')
    {
        if (!$timestamp) {
            $timestamp = time();
        }
        $tradeNo = 'bal_' . $timestamp;

        return $tradeNo;
    }

    /**
     * 处理修改授信模式的参数
     * I('post.did')  分销商id
     * I('post.mode') 授信模式 0-正常, 1-后结
     * I('post.credit')  设置授信额度（mode为0时才生效）
     * @return array
     */
    private function _handleCreditParams()
    {
        $fid = I('post.did', 0, 'intval');
        if (!$fid) {
            $this->apiReturn(400, [], '分销商ID不能为空');
        }

        $mode = I('post.mode', 0, 'intval');
        $memo = I('post.meno', '');

        //前端这个数据传过来的是元
        //前端逻辑整理的有问题，都是调整固定授信额度
        //正常走setFund_n.php ,后结调整回固定的时候又走这边。
        $credit = I('post.credit', 0);
        $credit = (int)($credit * 100); //*100转化为单位分

        //非后结模式信用额度不超过50万
        if ($mode == 0 && ($credit < 0 || $credit > 50000000)) {
            $this->apiReturn(400, [], '模式或授信额度错误');
        }

        if ($mode == 1) {
            //后结模式
            $text   = '开启'; //开启后结模式
            $credit = $this->getUnlimitCredit(); //后结模式的授信额度为一个超大值
        } else {
            //正常模式
            $text = '取消'; //取消后结模式
        }

        $params = [
            'fid'    => $fid,
            'mode'   => $mode,
            'text'   => $text,
            'credit' => $credit,
            'memo'   => $memo,
        ];

        return $params;
    }

    /**
     * 修改授信模式（参数同_handleCreditParams）
     * @update date 2018-2-24
     * @update author cyw
     *     更新对接java
     */
    public function setCreditMode()
    {
        $params = $this->_handleCreditParams();
        $mode   = $params['mode']; // 0-正常， 1-后结
        $fid    = $params['fid'];
        $credit = $params['credit'];
        $text   = $params['text'];
        $memo   = $params['memo'];

        $aid      = $this->_sid;
        $memberID = $this->_memberID;

        //查询当前授信额度信息
        $currentInfo = $this->_memberModel->getMoney($fid, 4, $aid, true);

        if ($currentInfo === false) {
            $this->apiReturn(201, [], "查询现有信用额度失败");
        }

        //记录备注
        if ($aid != $memberID) {
            $dname    = $this->_loginInfo['dname'];
            $account  = $this->_loginInfo['account'];
            $opRemark = "【员工：{$dname}({$account})】";
            if ($memo) {
                $memo = $memo . ',' . $opRemark;
            } else {
                $memo = $opRemark;
            }
        }

        $creditBiz = new Credit();
        $setResArr = $creditBiz->setLimit($aid, $fid, $credit, $memberID, $memo, $mode);

        if ($setResArr['code'] != 200) {
            $this->apiReturn(201, [], '授信模式修改失败');
        }

        if ($mode == 1) {
            //开启后结关闭分销商授信预警
            $BalWarnFilter = [
                'fid'     => intval($fid),
                'is_boss' => 1,
                'boss_id' => intval($aid),
            ];

            $BalWarnData['state'] = 0;

            $balanceWarningModel = new Member\BalanceWarning();
            $balanceWarningModel->setState($BalWarnFilter, $BalWarnData);
        }

        $this->apiReturn(200, [], "{$text}后结模式成功");
    }

    /**
     * 获取用户剩余服务费
     * Create by zhangyangzhen
     * Date: 2019/11/14
     * Time: 14:19
     * @throws \Exception
     */
    public function getMemberServiceMoney()
    {
        $model = new Member\Member();
        $money = $model->getMoney($this->_sid, 0);

        $loginInfo = [
            'account'  => $this->_loginInfo['account'],
            'sid'      => $this->_sid,
            'memberId' => $this->_memberID,
        ];

        $data = [
            'userInfo' => $loginInfo,
            'money'    => $money,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $data, 'success');
    }

    /**
     * 获取分销商授信，余额等信息
     * Create by zhangyangzhen
     * Date: 2019/11/23
     * Time: 15:34
     */
    public function getResellerCredit()
    {
        $did = I('post.did', 0, 'intval');
        $aid = I('post.aid', 0, 'intval');

        if (!$did && !$aid) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $memberId    = $did ?: $aid;
        $memberModel = new \Business\Member\Member();
        $memberInfo  = $memberModel->getInfo($memberId);
        if (!$memberInfo) {
            $this->apiReturn(204, [], '要查看的用户用户不存在');
        }

        //是否有分销关系
        $relationApi = new \Business\JavaApi\Member\MemberRelationQuery();
        if (!$relationApi->existsDisRelation($did ? $this->_sid : $aid, $did ? $did : $this->_sid)) {
            $this->apiReturn(204, [], '无权查看');
        }

        $accountBookApi = new \Business\JavaApi\Account\AccountBook();
        if ($did) {
            $creditRes = $accountBookApi->queryCreditBookMany2One($this->_sid, [$did]);
        } else {
            $creditRes = $accountBookApi->queryCreditBookMany2One($aid, [$this->_sid]);
        }
        if ($creditRes['code'] != 200) {
            $this->apiReturn(204, [], $creditRes['msg']);
        }

        $creditInfo = $creditRes['data'][0];

        //获取账户余额
        $balanceRes = $accountBookApi->queryOnePlatformBook($this->_sid);
        if ($balanceRes['code'] == 200) {
            $platformBalance = $balanceRes['data']['money'];
        } else {
            $platformBalance = 0;
        }

        //获取一次分销商备注信息
        $remark     = '';
        $groupInfo  = [];

        if ($did) {
            $memberbus  = new MemberRelationshipRemark();
            $remarkInfo = $memberbus->queryDistributorRemarkName($memberId, $this->_sid);
            if ($remarkInfo['code'] == 200 && !empty($remarkInfo['data']['remark'])) {
                $remark = $remarkInfo['data']['remark'];
            }

            // 分销商当前所属的分组信息
            $priceGroupBuz = new \Business\JavaApi\Product\PriceGroup();
            $evoGroupBuz   = new \Business\JavaApi\Product\EvoluteGroup();
            $newGroupRes = $priceGroupBuz->queryNewGroupIdByFidAndSid($memberId, $this->_sid);
            if ($newGroupRes['code'] == 200 && !empty($newGroupRes['data'])) {
                $groupInfoRes = $evoGroupBuz->queryByGroupId($newGroupRes['data']);
                if ($groupInfoRes['code'] == 200 && !empty($groupInfoRes['data'])) {
                    $groupInfo = [
                        'group_id'   => $groupInfoRes['data']['id'],
                        'group_name' => $groupInfoRes['data']['groupName'],
                    ];
                }
            }
        }

        $memberInfo = [
            'dname'     => $memberInfo['dname'],
            'cname'     => $memberInfo['cname'],
            'mobile'    => $memberInfo['mobile'],
            'headphoto' => $memberInfo['headphoto'],
            'id'        => $memberInfo['id'],
            'account'   => $memberInfo['account'],
            'balance'   => $platformBalance,
            'remark'    => $remark,
        ];

        //已用额度
        $usedLimit = $creditInfo['surplus_money'] < 0 ? -$creditInfo['surplus_money'] : 0;

        $returnData = [
            'loginInfo' => [
                'dname'   => $this->_loginInfo['dname'],
                'account' => $this->_loginInfo['account'],
            ],
            'member'    => $memberInfo,
            'credit'    => [
                'remain_limit'    => $creditInfo['credit_line'] - $usedLimit,
                'used_limit'      => $usedLimit,
                'total_limit'     => $creditInfo['credit_line'],
                'remain_money'    => $creditInfo['surplus_money'] + $creditInfo['credit_line'],
                'repayment_money' => $creditInfo['surplus_money'] > 0 ? $creditInfo['surplus_money'] : 0,
                'cmode'           => intval((int)$creditInfo['credit_line'] == ***********),
            ],
            'group' => $groupInfo,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $returnData, 'success');
    }

    /**
     * 获取我的供应商的授信信息和我的余额信息
     * <AUTHOR>
     * @date 2020/4/16
     *
     * @return string
     */
    public function getSupplierCredit()
    {
        $memberId = I('post.aid', 0, 'intval');

        if (!$memberId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $memberModel = new \Business\Member\Member();
        $memberInfo  = $memberModel->getInfo($memberId);
        if (!$memberInfo) {
            $this->apiReturn(204, [], '要查看的用户用户不存在');
        }

        //是否有分销关系
        $relationApi = new \Business\JavaApi\Member\MemberRelationQuery();
        if (!$relationApi->existsDisRelation($memberId, $this->_sid)) {
            $this->apiReturn(204, [], '无权查看');
        }

        $accountBookApi = new \Business\JavaApi\Account\AccountBook();
        $creditRes      = $accountBookApi->queryCreditBookMany2One($memberId, [$this->_sid]);

        if ($creditRes['code'] != 200) {
            $this->apiReturn(204, [], $creditRes['msg']);
        }

        $creditInfo = $creditRes['data'][0];

        $amountLedgerFreezeBiz = new AmountLedgerFreezeBiz();
        if ($amountLedgerFreezeBiz->isFrozenEnabled($this->_sid)) {
            $platformBalance = $amountLedgerFreezeBiz->getTotalBalance($this->_sid);
            $freMoney = $amountLedgerFreezeBiz->getFrozenBalance($this->_sid);
        } else {
            //获取账户余额
            $balanceRes = $accountBookApi->queryOnePlatformBook($this->_sid);
            if ($balanceRes['code'] != 200) {
                $this->apiReturn(204, [], '系统异常，请重试【err_code=2601】');
            }
            $platformBalance = $balanceRes['data']['money'];

            //获取冻结金额
            $accountMoneyBiz = new \Business\Finance\AccountMoney();
            $res             = $accountMoneyBiz->getFreezeMoney($this->_sid, true, 'supply_repayment');
            if ($res['code'] != 200) {
                $this->apiReturn(204, [], '系统异常，请重试【err_code=9601】');
            }
            $freMoney = $res['data']['money'];
        }

        //还款方式
        $repayInfo = (new \Business\JavaApi\Member\MemberClearingWay())->getMemeberClearingWay($memberId, $this->_sid);
        if ($repayInfo['code'] != 200) {
            $this->apiReturn(204, [], '系统异常，请重试【err_code=9602】');
        }
        $repayMode = $repayInfo['data']['repayMode'] ?? 0;

        $memberInfo = [
            'dname'          => $memberInfo['dname'],
            'id'             => $memberInfo['id'],
            'account'        => $memberInfo['account'],
            'balance'        => $platformBalance,
            'freeze_balance' => $freMoney,
            'repay_mode'     => $repayMode,
        ];

        //已用额度
        $usedLimit = $creditInfo['surplus_money'] < 0 ? -$creditInfo['surplus_money'] : 0;

        $returnData = [
            'loginInfo' => $this->_loginInfo,
            'member'    => $memberInfo,
            'credit'    => [
                'remain_limit'    => $creditInfo['credit_line'] - $usedLimit,
                'used_limit'      => $usedLimit,
                'total_limit'     => $creditInfo['credit_line'],
                'remain_money'    => $creditInfo['surplus_money'] + $creditInfo['credit_line'],
                'repayment_money' => $creditInfo['surplus_money'] > 0 ? $creditInfo['surplus_money'] : 0,
                'cmode'           => intval((int)$creditInfo['credit_line'] == ***********),
            ],
        ];

        $this->apiReturn(self::CODE_SUCCESS, $returnData, 'success');
    }

    /**
     * 我的分销商/我的供应商获取授信记录列表
     * <AUTHOR>
     * @date   2020-03-04
     */
    public function getCreditTradeList()
    {
        //我的分销商
        $fid = I('fid', 0, 'intval');
        //我的供应商
        $aid = I('aid', 0, 'intval');
        //开始时间
        $beginDate = I('begin_date', '');
        //结束时间
        $endDate = I('end_date', '');
        //授信记录类型
        $type = I('type', 'credit');
        //当前页码
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 15, 'intval');

        if ($aid) {
            $fid = $this->_sid;
        } else {
            $aid = $this->_sid;
        }

        $vacation = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('creditTrade',
            $this->_loginInfo['saccount']);
        if ($vacation === false) {
            if (strtotime($endDate) - strtotime($beginDate) >= 3600 * 24 * 14) {
                $this->apiReturn(500, [], '假日模式只限制查询14日数据');
            }
        }

        $tradeBiz = new \Business\Finance\TradeQuery();
        $res      = $tradeBiz->getCreditList($type, $fid, $aid, $beginDate, $endDate, $page, $size);

        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 我的分销商/我的供应商获取授信记录导出
     * <AUTHOR>
     * @date   2020-03-04
     */
    public function exportCreditTradeList()
    {
        //我的分销商
        $fid = I('fid', 0, 'intval');
        //我的供应商
        $aid = I('aid', 0, 'intval');
        //开始时间
        $beginDate = I('begin_date', '');
        //结束时间
        $endDate = I('end_date', '');
        //授信记录类型
        $type = I('type', 'credit');

        if ($aid) {
            $fid = $this->_sid;
        } else {
            $aid = $this->_sid;
        }

        $vacation = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('creditTrade',
            $this->_loginInfo['saccount']);
        if ($vacation === false) {
            if (strtotime($endDate) - strtotime($beginDate) >= 3600 * 24 * 14) {
                $this->apiReturn(500, [], '假日模式只限制查询14日数据');
            }
        }

        $tradeBiz = new \Business\Finance\TradeQuery();
        $result   = $tradeBiz->exportCreditList($type, $fid, $aid, $beginDate, $endDate);

        if (isset($result['code']) && $result['code'] == 200) {
            $this->excelReturn('授信交易记录导出', '授信交易记录导出', $result['data']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 授信记录管理
     * <AUTHOR>
     * @date 2022/3/23
     *
     */
    public function creditRecordManage()
    {
        //我的分销商
        $fid = I('post.fid', 0, 'intval');
        //开始时间
        $beginDate = I('post.begin_date', '', 'strval');
        //结束时间
        $endDate = I('post.end_date', '', 'strval');
        //授信记录类型
        $type = I('post.type', '', 'strval');
        //当前页码
        $page = I('post.page', 1, 'intval');
        //每页条数
        $size = I('post.size', 10, 'intval');
        //订单号查询
        $orderid = I('post.orderid', '', 'strval');

        $aid = $this->_sid;

        $vacation = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('creditTrade',
            $this->_loginInfo['saccount']);
        if ($vacation === false) {
            if (strtotime($endDate) - strtotime($beginDate) >= 3600 * 24 * 14) {
                $this->apiReturn(500, [], '假日模式只限制查询14日数据');
            }
        }

        $tradeBiz = new \Business\Finance\TradeQuery();
        $res      = $tradeBiz->getCreditRecordManage($type, $aid, $beginDate, $endDate, $fid, $orderid, $page, $size);

        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 授信记录汇总
     * <AUTHOR>
     * @date   2022/5/12
     *
     */
    public function recordSummary()
    {
        //我的分销商
        $fid = I('post.fid', 0, 'intval');
        //开始时间
        $beginDate = I('post.begin_date', '', 'strval');
        //结束时间
        $endDate = I('post.end_date', '', 'strval');
        //授信记录类型
        $type = I('post.type', '', 'strval');

        $aid = $this->_sid;

        $vacation = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('creditTrade',
            $this->_loginInfo['saccount']);
        if ($vacation === false) {
            if (strtotime($endDate) - strtotime($beginDate) >= 3600 * 24 * 14) {
                $this->apiReturn(500, [], '假日模式只限制查询14日数据');
            }
        }

        $tradeBiz = new \Business\Finance\TradeQuery();
        $res      = $tradeBiz->getRecordSummary($type, $aid, $beginDate, $endDate, $fid);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常, ' . $res['msg']);
        }
    }

    /**
     * 授信记录详情
     * <AUTHOR>
     * @date   2022/5/12
     *
     */
    public function creditDetails()
    {
        //授信记录类型
        $type = I('post.type', '', 'strval');
        //查询订单号
        $orderId = I('post.orderid', '', 'strval');
        //开始时间
        $beginDate = I('post.begin_date', '', 'strval');
        //结束时间
        $endDate = I('post.end_date', '', 'strval');

        $aid = $this->_sid;

        $tradeBiz = new \Business\Finance\TradeQuery();
        $res      = $tradeBiz->getCreditRecordManage($type, $aid, $beginDate, $endDate, 0, $orderId, 1, 1);

        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data']['list'][0] ?? [], $res['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

}