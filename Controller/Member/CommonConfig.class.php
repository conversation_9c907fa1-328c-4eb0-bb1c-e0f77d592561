<?php
/**
 * 配置相关信息获取
 */

namespace Controller\Member;

use Library\Controller;

class CommonConfig extends Controller
{

    /**
     * 获取企业类型
     * <AUTHOR>
     * @date 2021/1/11
     *
     */
    public function comTypes()
    {
        $companyType = (new \Business\JavaApi\Server\CommonDict())->getComDict();

        $this->apiReturn(200, empty($companyType) ? [] : array_merge($companyType));
    }
}