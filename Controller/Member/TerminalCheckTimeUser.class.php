<?php

namespace Controller\Member;

use Library\Controller;
use Library\Tools\Helpers;

class TerminalCheckTimeUser extends Controller
{
    /**
     * 获取分终端验证时限配置列表
     * <AUTHOR>
     * @date   2020-10-13
     */
    public function getTerminalCheckTimeUserList()
    {
        $page    = I('post.page', '');
        $size    = I('post.size', '');
        $account = I('post.account', '');

        if ($account === 0) {
            $this->apiReturn(200, []);
        }

        $memberBiz = new \Business\Member\TerminalCheckTimeUser();
        $result    = $memberBiz->getTerminalCheckTimeUserList($account, $page, $size);

        $this->apiReturn(200, $result ?: []);
    }

    /**
     * 添加获取分终端验证时限配置列表
     * <AUTHOR>
     * @date   2020-04-20
     */
    public function addTerminalCheckTimeUser()
    {
        //判断是否为管理员
        if (!$this->isSuper()) {
            $this->apiReturn(204, [], '当前账号不是管理员');
        }
        $account    = I('post.account', '', 'strval');
        $memberBiz  = new \Business\Member\TerminalCheckTimeUser();
        $memberInfo = $this->getLoginInfo();

        $result = $memberBiz->addTerminalCheckTimeUser($account, $memberInfo['memberID']);

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], $result['msg']);
        } else {
            $this->apiReturn(500, [], $result['msg']);
        }
    }

    /**
     * 修改用户多景点白名单状态
     * <AUTHOR>
     * @date   2020-04-20
     */
    public function editTerminalCheckTimeUser()
    {
        //判断是否为管理员
        if (!$this->isSuper()) {
            $this->apiReturn(204, [], '当前账号不是管理员');
        }
        $account = I('post.account', '', 'strval');
        $status  = I('post.terminal_status', -1, 'int');

        if (!$account || !in_array($status, [0, 1])) {
            $this->apiReturn(201, [], '参数错误');
        }
        $memberBiz  = new \Business\Member\TerminalCheckTimeUser();
        $memberInfo = $this->getLoginInfo();
        $result     = $memberBiz->editTerminalCheckTimeConfig($account, $status,
            $memberInfo['memberID']);

        $this->apiReturn($result['code'], [], $result['msg']);

    }
}