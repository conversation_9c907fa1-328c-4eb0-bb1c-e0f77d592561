<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: guanpeng
 * Date: 2018/10/18
 * Time: 16:08
 * 员工管理
 */

namespace Controller\Member;


use Business\Order\Face;
use Library\Business\Uface\OpenPlatform;
use Library\Controller;
use Library\Tools;
use Model\Product\Land;
use Model\Terminal\FaceCompare;
use Library\Resque\Queue;
use Library\Tools\YarClient;
use Library\Tools\Helpers;

class StaffManage extends Controller
{
    private $bossId;
    private $_loginInfo;
    public function __construct()
    {
        parent::__construct();
        //$this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo();
        $this->bossId = $this->_loginInfo['sid'];//3385;
    }

    /**
     * 员工照片注册
     *
     * <AUTHOR>
     * @date   2019-04-16
     *
     */
    public function faceRegister()
    {
        $staffId = I('post.staff_id');
        $base64Img = I('post.photo_base64');
        $expireTime = I('post.expireTime', 0, 'intval');//单位天
        $deviceArray = I('post.deviceArray', []);
        pft_log('orderface', 'staff_face=' . json_encode([
                'staffId' => $staffId,
                'expireTime' => $expireTime,
                'deviceArray' => $deviceArray,
                'bossid' => $this->bossId
            ]));


        if (empty($base64Img)) {
            $this->apiReturn(204, [], '请上传图片');
        }

        $model = new \Model\Member\Member('slave');
        $memberInfo = $model->getMemberInfo($staffId, 'id', 'dname,mobile');
        if (!$memberInfo) {
            $this->apiReturn(201, [], '员工信息不存在');
        }

        $bossInfo = $model->getStaffBySonId($staffId);
        if ($bossInfo['parent_id'] != $this->bossId) {
            $this->apiReturn(201, [], '不是您的员工');
        }

        $groupId = "STF" . $bossInfo['parent_id'];
        $personId = "STF" . $staffId;
        $temId    = "STF" . $staffId . rand(1,999);
        $imgResult = Helpers::uploadImage2AliOss('staffface', $base64Img, 'base64', $temId);
        if ($imgResult['code'] != 200 || empty($imgResult['data']['src'])) {
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '人脸图片上传失败');
        }
        $imgUrl = $imgResult['data']['src'];
        $data = [
            $personId,
            $groupId,
            $imgUrl,
            $deviceArray,
            $expireTime,
            $memberInfo,
            $this->bossId
        ];

        $yarClient = new YarClient('face');
        $result = $yarClient->call('/Face/Staff/faceAdd', $data);

        if ($result['code'] != 200) {
            $this->apiReturn(201, [], 'RPC服务异常，' . $result['error_msg']);
        }

        $this->apiReturn($result['res']['code'], [], $result['res']['msg']);
    }

    public function getPicture()
    {
        $staffId    = I('post.staff_id');
        $model      = new \Model\Member\Member('slave');
        $bossInfo   = $model->getStaffBySonId($staffId);
        if ($bossInfo['parent_id'] != $this->bossId) {
            $this->apiReturn(201, [], '不是您的员工');
        }

        $data['url'] = '';
        $faceModel    = new FaceCompare();
        $platform = $faceModel->getPlatformByAid($this->bossId, 'face_platform');
        $faceData     = $faceModel->getFaceInfoByFaceId(false, 'STF'.$staffId, 'face_url,expireday', 'STF'.$bossInfo['parent_id']);
        $data['platform']  = min($platform);
        if($faceData['face_url']) {
	        $faceBiz            = new \Business\Face\FaceBase();
            $data['url']        = $faceBiz->faceUrlConversion($faceData['face_url']);
            $data['expireday']  = $faceData['expireday'];
        }
        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 获取人脸设备
     * <AUTHOR>
     * @date   2018-10-26
     *
     * @return
     */
    public function getDevice() {
        $lid        = I('post.lid', 0, 'intval');

        if(empty($lid)){
            $this->apiReturn(201, [], '请输入景区id');
        }
        $landModel = new Land('slave');
        $landInfo  = $landModel->getLandInfo($lid, false, 'apply_did');
        if ($landInfo['apply_did'] != $this->bossId) {
            $this->apiReturn(201, [], '无权限查询设备');
        }
        $faceModel = new FaceCompare();
        $data      = $faceModel->getFaceDeviceByLid($lid);

        $this->apiReturn(200, $data['list'], 'success');

    }

    /**
     * 获取供应商景区
     * <AUTHOR>
     * @date   2018-10-29
     *
     * @return
     */
    public function getLands() {
        $sid       = $this->bossId;
        $keyword   = I('post.keyword', '');

        $javaApi  = new \Business\CommodityCenter\Land();
        $landInfo = $javaApi->queryLandMultiQueryByApplyDid([$sid], $keyword);

        $this->apiReturn(200, $landInfo, 'success');
    }

    public function getLandsPage()
    {
        $sid       = $this->bossId;
        $keyword   = I('post.keyword', '');

        $javaApi  = new \Business\CommodityCenter\Land();
        $landInfo = $javaApi->getLandInfoListBySid($sid, $keyword, 1, 20, '', null);

        $this->apiReturn(200, $landInfo, 'success');
    }

    /**
     * 添加员工
     * <AUTHOR>
     * @date   2020-03-04
     */
    public function addStaff()
    {
        $account       = I('post.account', '');
        $password      = I('post.password', '');
        $dName         = I('post.name', '');
        $mobile        = I('post.mobile', '');
        $qq            = I('post.qq', '');
        $physics_no    = I('post.physics_no', '');
        $visible_no    = I('post.visible_no', '');
        $staffGroup    = I('post.staff_group', 99);
        $staffType     = I('post.staff_type', 0);
        $cname         = I('post.cname', '');
        $staffCardType = I('post.staff_card_type', 0);
        $remark        = I('post.remark', '');
        $staffMemberId = I('post.staff_member_id', 0, 'intval');

        $accountLong = strlen($account);
        if ($accountLong > 12 || $accountLong < 4) {
            $this->apiReturn(204, [], '账号长度应为4~12!');
        }
        if (is_numeric($account)) {
            $this->apiReturn(204, [], '员工账号不能纯数字');
        }
        if (empty($staffMemberId)) {
            $this->apiReturn(204, [], '员工id不能为空');
        }
        for ($i = 0; $i < $accountLong; $i++) {
            $t = substr($account, $i, 1);
            $v = ord($t);
            if (($v < 48 || $v > 57) && ($v < 65 || $v > 90) && ($v < 97 || $v > 122)) {
                $this->apiReturn(204, [], '账号只可由数字与英文构成');
            }
        }
        if (!in_array($staffCardType,[0,1])){
            $this->apiReturn(204, [], '员工卡类型错误');
        }
        $password = safetxt($password);
        if (!$password || !$dName) {
            $this->apiReturn(204, [], '密码或者名字不能为空');
        }
        if (!preg_match('/^(?![0-9]+$)(?![a-zA-Z]+$)(?![-+_!@#$%^&*()]+$)[0-9A-Za-z@_!$&%]{6,16}$/', $password)){
            $this->apiReturn(204, [], '密码需要数字字母或者符号组成的6-16位');
        }
        if (!is_numeric($staffGroup) || !is_numeric($staffType)) {
            $this->apiReturn(204, [], '角色或区域数据有误');
        }
        if ($qq){
            if (!is_numeric($qq)){
                $this->apiReturn(204, [], 'qq号有误');
            }
        }
        $dType    = $this->_loginInfo['dtype'];
        $sid      = $this->_loginInfo['sid'];
        $memberId = $this->_loginInfo['memberID'];
        $qx       = $this->_loginInfo['qx'];
        $isAdmin  = false;
        if ($this->isSuper()) {
            $this->_checkAdminType($staffGroup,4,$staffType);
            $isAdmin = true;
        }
        $staffManageBiz = new \Business\Member\StaffManage();
        //判断权限
        $checkRes = $staffManageBiz->checkCommonStaffManageAuth($dType, $memberId, $sid, $qx, $isAdmin,true);
        if ($checkRes['code'] != 200) {
            $this->apiReturn($checkRes['code'], $checkRes['data'], $checkRes['msg']);
        }
        //添加用户
        $optionParam = [
            'mobile'      => $mobile,
            'qq'          => $qq,
            'physics_no'  => $physics_no,
            'visible_no'  => $visible_no,
            'staff_group' => $staffGroup,
            'staff_type'  => $staffType,
            'cname'       => $cname,
            'staff_card_type' => $staffCardType,
            'remark'      => $remark
        ];
        $res         = $staffManageBiz->addStaffService($sid, $memberId, $account, $dName, $password, $isAdmin,
            $optionParam, $staffMemberId);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '系统异常');
        }
    }
    /**
     * 获取员工管理配置信息
     * <AUTHOR>
     * @date   2020-03-04
     */
    public function getStaffManageInfo()
    {
        $isAdmin    = 0;  //普通用户
        $department = [];
        $role       = [];
        $group      = [];
        if ($this->isSuper()) {
            $isAdmin        = 1;  //管理员用户可以看
            $staffManageBiz = new \Business\Member\StaffManage();
            $res            = $staffManageBiz->getStaffManageModifyAuth($this->_loginInfo['memberID']);
            $role           = load_config('staff_role', 'staff_manage');
            $group          = load_config('staff_group', 'staff_manage');
            $department     = load_config('staff_department', 'staff_manage');
            if ($res || $this->_loginInfo['dtype'] == 9) {
                $isAdmin = 2; //管理员用户可以编辑
            }
        }
        $data = [
            'auth'  => $isAdmin,
            'dept'  => $department,
            'role'  => $role,
            'group' => $group,
        ];
        $this->apiReturn(200, $data, 'success');
    }
    /**
     * 获取员工用户信息
     * <AUTHOR>
     * @date   2020-03-04
     */
    public function getStaffUserInfo()
    {
        $userId = I('get.user_id', 0);
        if (!$userId) {
            $this->apiReturn(204, [], '用户错误');
        }
        $dType    = $this->_loginInfo['dtype'];
        $sid      = $this->_loginInfo['sid'];
        $memberId = $this->_loginInfo['memberID'];
        $qx       = $this->_loginInfo['qx'];
        $isAdmin  = false;
        if ($this->isSuper()) {
            $isAdmin = true;
        }
        //先判断操作人有没有权限看
        $staffManageBiz = new \Business\Member\StaffManage();
        $checkRes       = $staffManageBiz->checkCommonStaffManageAuth($dType, $memberId, $sid, $qx, $isAdmin);
        if ($checkRes['code'] != 200) {
            $this->apiReturn($checkRes['code'], $checkRes['data'], $checkRes['msg']);
        }
        $res = $staffManageBiz->getStaffUserInfo($userId, $sid);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '系统异常');
        }
    }
    /**
     * 修改员工信息
     * <AUTHOR>
     * @date   2020-03-04
     */
    public function editStaffUserInfo(){
        $userId = I('post.user_id',0);
        $dname  = I('post.name','');
        $cname  = I('post.cname','');  //联系人姓名
        $dept   = I('post.dept',4); //部门
        //$mobile = I('post.mobile','');//手机号
        $qq     = I('post.qq','');
        $email  = I('post.email','');
        $physics_no  = I('post.physics_no', '');
        $visible_no  = I('post.visible_no', '');
        $staffGroup  = I('post.staff_group', 99);
        $staffType   = I('post.staff_type', 0);
        $staffCardType = I('post.staff_card_type',0);  //0-员工卡 1-员工卡二次放行
        $remark        = I('post.remark','');
        if (!$userId){
            $this->apiReturn(204, [], '用户错误');
        }
        if (!$dname){
            $this->apiReturn(204, [], '名字不能为空');
        }
        /*if ($mobile && !Tools::ismobile($mobile)){
            $this->apiReturn(204, [], '电话有误');
        }*/
        if ($email){
            $emailRes = preg_match("/^[a-z0-9A-Z]+[-|a-z0-9A-Z._]+@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\.)+[a-z]{2,}$/", $email);
            if (!$emailRes){
                $this->apiReturn(204, [], '邮箱格式有误');
            }
        }
        if ($qq){
            if (!is_numeric($qq)){
                $this->apiReturn(204, [], 'qq号有误');
            }
        }
        $dType    = $this->_loginInfo['dtype'];
        $sid      = $this->_loginInfo['sid'];
        $memberId = $this->_loginInfo['memberID'];
        $qx       = $this->_loginInfo['qx'];
        $isAdmin  = false;
        if ($this->isSuper()) {
            $this->_checkAdminType($staffGroup,$dept,$staffType);
            $isAdmin = true;
        }
        $staffManageBiz = new \Business\Member\StaffManage();
        $optionParam = [
            'name' => $dname,
            'cname' => $cname,
            //'mobile' => $mobile,
            'qq'     => $qq,
            'email'  => $email,
            'position' => $dept,
            'physics_no' => $physics_no,
            'visible_no' => $visible_no,
            'staff_group' => $staffGroup,
            'staff_type'  => $staffType,
            'staff_card_type' => $staffCardType,
            'remark'         => $remark
        ];
        $opInfo = [
            'dtype' => $dType,
            'sid'   => $sid,
            'memberId' => $memberId,
            'qx'   => $qx,
        ];
        $res = $staffManageBiz->editStaffUserInfoService($userId,$opInfo,$optionParam,$isAdmin);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '系统异常');
        }
    }
    /**
     * 检测管理员角色
     * <AUTHOR>
     * @date   2020-03-04
     */
    private function _checkAdminType($group,$dept,$type){
        $staffDtpe = load_config('staff_department','staff_manage');
        $staffGroup = load_config('staff_group','staff_manage');
        $staffRole = load_config('staff_role','staff_manage');
        if (!in_array($group,array_column($staffGroup,'type'))){
            $this->apiReturn(204, [], '负责区域错误');
        }
        if (!in_array($type,array_column($staffRole,'type'))){
            $this->apiReturn(204, [], '角色有误');
        }
        if (!in_array($dept,array_keys($staffDtpe))){
            $this->apiReturn(204, [], '部门有误');
        }
    }
}