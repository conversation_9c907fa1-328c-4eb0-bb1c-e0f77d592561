<?php
/**
 * 发票相关C层
 * User: <PERSON><PERSON><PERSON><PERSON>n
 * Date: 2017/5/28
 * Time: 10:27
 */

namespace Controller\Member;

use Business\Finance\OweTicketAmount;
use Library\Controller;
use Business\JavaApi\InvoiceApi;
use Process\Member\Invoice as ProcessLib;
use Model\Member\Invoice as modelInvoice;
use Model\Member\Member;
use Library\Cache\Cache;

class Invoice extends Controller
{
    private $_processLib;
    private $_businessLib;
    private $_memberId;
    private $_memberInfo;
    private $_isSuper;

    //账号分组 - 测试组
    const __TEST_MEMBER_GROUP__ = 2;

    protected $commonInvoiceWhitelist = ENV == 'PRODUCTION' ? [13702234] : (ENV == 'TEST' ? [3262552,6970,3385] : [6970,3385]);

    public function __construct() {
        $this->_processLib  = new ProcessLib();
        $this->_businessLib = new InvoiceApi();
        $this->_memberInfo  = $this->getLoginInfo();
        $this->_memberId    = $this->_memberInfo['sid'];
        $this->_isSuper     = $this->isSuper();
//        if (!in_array($this->_memberId, [3385, 55]) && !$this->_isSuper) {
//            $this->apiReturn(201, [], '无权操作');
//        }
    }

    
    /**
     * 绑定发票开票点等信息
     *
     * @date   2017-06-01
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function bindInvoice() {
        $params = $this->_processLib->bindInvoice();

        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $paramTemp = $params[1];

        $paramTemp['businessId'] = $this->_memberId;

        $result = $this->_businessLib->bindInvoice($paramTemp);

        if ($result) {
            $this->apiReturn(200, [], '配置成功');
        } else {
            $this->apiReturn(400, [], '配置失败');
        }
    }

    /**
     * 商家电子发票配置
     *
     * @date   2017-05-28
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function setBusinessInvoice() {
        $params = $this->_processLib->setBusinessInvoice();

        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $paramTemp = $params[1];

        $paramTemp['businessId'] = $this->_memberId;

        $result = $this->_businessLib->setBusinessInvoice($paramTemp);

        if ($result) {
            $this->apiReturn(200, [], '配置成功');
        } else {
            $this->apiReturn(400, [], '配置失败');
        }
    }

    /**
     * 根据商家Id获取商家关联的电子发票信息接口
     *
     * @date   2017-06-02
     * <AUTHOR> Lan
     *
     * @return array
     */
    public function getInvoiceInfo() {
        $result = $this->_businessLib->getInvoiceInfo($this->_memberId);

        if ($result) {
            $this->apiReturn(200, $result, '获取成功');
        } else {
            $this->apiReturn(400, [], '没有相关数据');
        }
    }

    /**
     * 设置商家邮寄配置信息
     *
     * @date   2017-06-09
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function setAddress() {
        $params = $this->_processLib->setAddress();
        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $paramTemp = $params[1];

        $paramTemp['businessId'] = $this->_memberId;

        $result = $this->_businessLib->setAddress($paramTemp);

        if ($result) {
            $this->apiReturn(200, [], '设置成功');
        } else {
            $this->apiReturn(400, [], '设置失败');
        }
    }

    /**
     * 获取商家邮寄配置信息
     *
     * @date   2017-06-09
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function getAddress() {
        $fid           = I('post.fid', '', 'intval');
        //超管需要传fid，普通商家取自己的
        if ($fid == '') {
            $fid = $this->_memberId;
        }
        $result = $this->_businessLib->getAddress($fid);
        if ($result) {
            $this->apiReturn(200, $result, 'success');
        } else {
            $this->apiReturn(400, [], '请填写收件信息');
        }
    }

    /**
     * 专票申请-配置
     *
     * @date   2017-05-28
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function setSpecialInvoice() {
        $params = $this->_processLib->setSpecialInvoice();
        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $paramTemp = $params[1];

        $paramTemp['businessId'] = $this->_memberId;

        if (isset($paramTemp['id'])) {
            $result = $this->_businessLib->updateSpecialInvoice($paramTemp);
        } else {
            $result = $this->_businessLib->setSpecialInvoice($paramTemp);
        }

        if ($result) {
            $this->apiReturn(200, [], '配置成功');
        } else {
            $this->apiReturn(400, [], '配置失败');
        }
    }

    /**
     * 获取专票配置信息
     *
     * @date   2017-06-09
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function getSpecialConfig() {
        $result = $this->_businessLib->getSpecialConfig($this->_memberId);
        //用户二期 - 信息获取修改 - modification-a
        $MemberBus  = new \Business\Member\Member();
        $memberInfo = $MemberBus->getInfo($this->_memberId,true);
        //企业名称
        $company      = $memberInfo["com_name"];
        if ($company) {
            $result['company']    = $company;
            $result['compayName'] = $company;
        }
        if ($result) {
            $this->apiReturn(200, $result, 'success');
        } else {
            $this->apiReturn(400, [], '没有相关数据');
        }
    }

    /**
     * 发票管理与发票审核-获取数据
     *
     * @date   2017-05-28
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function invoiceManager() {
        $params = $this->_processLib->invoiceManager();
        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $paramTemp = $params[1];

        $paramTemp['fid'] = $this->_memberId;

        $modelInvoice = new modelInvoice();

        $count = $modelInvoice->getJournalData($paramTemp, 'count(*)');

        if (empty($count)) {
            $this->apiReturn(400, [], '没有相关数据');
        }

        $field = 'id, fid,trade_no, trade_type, money, status, invoice_type, courier_no,time, invoice_no, remark';
        $result = $modelInvoice->getJournalData($paramTemp, $field);

        //获取发票下载地址和发票编码
        $tradeNos = array_column($result,'trade_no');
        $url = $modelInvoice->getDataByTradeNoInvoiceVersionFileUrl($tradeNos);
        $url = array_key($url,'trade_no');
        foreach ($result as &$item){
            $item['invoiceVersionFilePath'] = empty($url[$item['trade_no']]['invoice_version_file_url']) ? '' : $url[$item['trade_no']]['invoice_version_file_url'];

            $item['invoice_no'] = empty($url[$item['trade_no']]['invoice_number']) ? '' : $url[$item['trade_no']]['invoice_number'];
            $item['base64_file_data'] = empty($url[$item['trade_no']]['file_data']) ? '' : $url[$item['trade_no']]['file_data'];
            $item['button'] = [
                // 普票且开票失败时，可重新开票
                'can_retry_invoice' => $item['invoice_type'] == 2 && $item['status'] == 7
            ];
        }

        $data = ['count' => $count, 'list' => $result];

        $this->apiReturn(200, [$data], '获取成功');
    }

    public function getSumInvoiceMoney()
    {
        $modelInvoice = new modelInvoice();
        $result = $modelInvoice->getSumInvoiceMoney($this->_memberId);
        $this->apiReturn(200, $result, '获取成功');
    }
    /**
     * 票付通后台审核数据获取
     *
     * @date   2017-07-19
     * <AUTHOR> Lan
     *
     * @return string
     */
    //public function invoiceCheck() {
    //    $this->checkAuthor();
    //    $params = $this->_processLib->invoiceCheck();
    //    if ($params[0] != 0) {
    //        $this->apiReturn($params[0], [], $params[1]);
    //    }
    //
    //    $paramTemp = $params[1];
    //
    //    $result = $this->_businessLib->getInvoiceCheck($paramTemp);
    //    if (!empty($result['resultList'])){
    //        $invoiceBiz = new \Business\Member\Invoice();
    //        $data       = $invoiceBiz->getInvoicePack($result['resultList']);
    //        $result['resultList'] = $data;
    //    }
    //    if ($result) {
    //        $this->apiReturn(200, $result, 'success');
    //    } else {
    //        $this->apiReturn(201, [], '暂无数据');
    //    }
    //}

    /**
     * 根据Id获取票付通开给商家开票记录详细信息
     *
     * @date   2017-06-02
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function getPftInvoiceById() {
        $params = $this->_processLib->getPftInvoiceById();
        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $tradeNo = $params[1]['id'];
        $result = $this->_businessLib->getPftInvoiceById($tradeNo);

        if ($result) {
            $modelInvoice = new modelInvoice();
            $field = 'id, fid, time, trade_type, money, courier_no,apply_time';
            $orderInfo = $modelInvoice->getDataByTradeNo($tradeNo,$field);
            $data = [
                'invoice' => $result,
                'order'   => $orderInfo,
            ];
            $this->apiReturn(200, $data, '获取成功');
        } else {
            $this->apiReturn(400, [], '没有相关数据');
        }
    }

    /**
     * 发票审核
     *
     * @date   2017-06-01
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function auditInvoice() {
        $this->checkAuthor();
        $params = $this->_processLib->auditInvoice();
        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $modelInvoice = new modelInvoice();

        $paramTemp = $params[1];

        //更新发票表
        $result = $modelInvoice->updateInvoiceByTradeNo($paramTemp['trade_no'], ['status' => $paramTemp['status']]);

        if ($result) {
            //更新管理表
            $result = $modelInvoice->updateDataByTradeNo($paramTemp['trade_no'], ['status' => $paramTemp['status']]);
        }

        if ($result) {
            $this->apiReturn(200, [], '操作成功');
        } else {
            $this->apiReturn(400, [], '操作失败');
        }
    }

    /**
     * 填写快递单号
     *
     * @date   2017-05-28
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function addCourierNumber() {
        $this->checkAuthor();
        $params = $this->_processLib->addCourierNumber();
        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $paramTemp = $params[1];

        $modelInvoice = new modelInvoice();

//        $res = $modelInvoice->checkCourierNo($paramTemp['number']);
//
//        if ($res['courier_no'] == $paramTemp['number']) {
//            $this->apiReturn(203, [], '快递单号已经存在');
//        }

        $result = $modelInvoice->updateDataByTradeNo($paramTemp['trade_no'], ['courier_no' => $paramTemp['number']]);

        if ($result) {
            $resJava = $this->_businessLib->addCourierNumber($paramTemp['trade_no'], ['expressNumber' => $paramTemp['number']]);
            if (!$resJava) {
                $this->apiReturn(400, [], '操作失败');
            }
            $this->apiReturn(200, [], '操作成功');
        } else {
            $this->apiReturn(400, [], '操作失败');
        }
    }

    /**
     * 专票申请
     *
     * @date   2017-06-01
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function addSpecialInvoice()
    {
        $memberModel = new \Business\Member\Member();
        $testAccount = $memberModel->getInfo($this->_memberId);

        if ($testAccount['group_id'] == self::__TEST_MEMBER_GROUP__) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '测试账号无法开票');
        }
        
        $this->checkMoney();
        $params = $this->_processLib->addSpecialInvoice();
        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $paramTemp = $params[1];

        $result = $this->_businessLib->getSpecialConfig($this->_memberId);
        if (empty($result['email'])) {
            $this->apiReturn(201, [], '请完善邮箱开票信息后再申请开票');
        }

        $cacheRedis = Cache::getInstance('redis');
        $keyLock    = "lock:specialInvoice:$this->_memberId";

        $ifLock = $cacheRedis->lock($keyLock, 1, 60);
        if (!$ifLock) {
            $this->apiReturn(206, [], '请勿频繁操作');
        }

        //申请商家ID
        $paramTemp['businessId'] = $this->_memberId;

        $journalIds = explode(',', $paramTemp['journalIds']);

        //获取欠票金额
        $sumOweTicketMoney = (new OweTicketAmount())->getOweTiketSum($this->_memberId);
        if ($sumOweTicketMoney['code'] != 200) {
            $cacheRedis->rm($keyLock);
            $this->apiReturn(400, [], $sumOweTicketMoney['msg']);
        }
        $sumOweTicketMoney = $sumOweTicketMoney['data'];

        $modelInvoice = new modelInvoice();
        $money = $modelInvoice->getDataByIds($journalIds, 'sum(money) as money');
        $money = $money[0]['money'];

        if ($sumOweTicketMoney >= $money) {
            $cacheRedis->rm($keyLock);
            $this->apiReturn(202, [], '选择的开票金额必须大于欠票金额');
        }

        if (!$money || $money != strval($paramTemp['price'])) {
            $cacheRedis->rm($keyLock);
            $this->apiReturn(202, [], '金额有误');
        }

        unset($paramTemp['journalIds']);

        $paramTemp['serialNumber'] = $modelInvoice->getSerial();

        $result = $this->_businessLib->addSpecialInvoice($paramTemp);

        if ($result) {
            $data = [
                'invoice_type' => 1,//专票
                'trade_no'     => $paramTemp['serialNumber'],//流水号
                'status'       => 3,//审核中
                'apply_time'  => time(),//申请时间
            ];
            $res = $modelInvoice->updateDataByIds($journalIds, $data);
            if (!$res) {
                $cacheRedis->rm($keyLock);
                pft_log('invoice/special', $modelInvoice->_sql());
                $this->apiReturn(400, [], '操作失败,请刷新重试');
            }
            $this->apiReturn(200, [], '操作成功');
        } else {
            $cacheRedis->rm($keyLock);
            $this->apiReturn(400, [], '操作失败');
        }
    }

    /**
     * 普通发票申请
     *
     * @date   2017-06-01
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function addCommonInvoice() {
        $timestamp = time();
        $week = date('w', $timestamp);
        $hour = date('H', $timestamp);
        if (!in_array($this->_memberId, $this->commonInvoiceWhitelist) && ($week == 0 || $week == 6 || $hour < 9 || $hour > 17)) {
            $this->apiReturn(201, [], '请于工作日 9:00-12:00  14:00-17:30 申请开票');
        }
        $this->checkMoney();
        $params = $this->_processLib->addCommonInvoice();
        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $paramTemp = $params[1];

        $result = $this->_businessLib->getSpecialConfig($this->_memberId);
        if (empty($result['email'])) {
            $this->apiReturn(201, [], '请完善邮箱开票信息后再申请开票');
        }

        $cacheRedis = Cache::getInstance('redis');
        $keyLock    = "lock:commonInvoice:$this->_memberId";
        $ifLock = $cacheRedis->lock($keyLock, 1, 60);
        if (!$ifLock) {
            $this->apiReturn(206, [], '请勿频繁操作');
        }

        //申请商家ID
        $paramTemp['businessId'] = 24296;

        if ($paramTemp['customerId'] != $this->_memberId) {
            $this->apiReturn(207, [], '非法操作');
        }

        $journalIds = explode(',', $paramTemp['journalIds']);

        $conf = load_config('item_category', 'trade_record');

        $modelInvoice = new modelInvoice();
        $info = $modelInvoice->getDataByIds($journalIds, 'money, trade_type, time, status');

        $body = [];
        $summoney = 0;
        if ($info) {
            foreach ($info as $key => $val) {
                // 未开票，未通过，开票失败才能开票
                if (!in_array($val['status'], [0,2,7])) {
                    $cacheRedis->rm($keyLock);
                    $this->apiReturn(201, [], '开票状态已更新，请刷新后再试');
                }
                if (in_array($val['trade_type'], [7, 8, 9, 97])) {//7电子凭证费,8短信息费,9提现交易手续费 交易类型增加显示时间
                    $tradeLogDate  = date('Y-m-d', $val['time']);
                    $tradeLogTime  = strtotime("$tradeLogDate - 1 month");
                    $commodityName = date('Y', $tradeLogTime).'年'.date('m', $tradeLogTime).'月'.$conf[$val['trade_type']][1];
                } else {
                    $commodityName = $conf[$val['trade_type']][1];
                }
                $body[$key]['commodityType']  = $paramTemp['commodityType'];
                $body[$key]['commodityName']  = $commodityName;
                $body[$key]['commodityCount'] = 1;
                $body[$key]['commodityPrice'] = $val['money'];
                $summoney                     += $val['money'];
            }
        } else {
            $cacheRedis->rm($keyLock);
            $this->apiReturn(202, [], '数据有误');
        }
        //欠票金额判断
        $sumOweTicketMoney = (new OweTicketAmount())->getOweTiketSum($this->_memberId);
        if ($sumOweTicketMoney['code'] != 200) {
            $this->apiReturn(400, [], $sumOweTicketMoney['msg']);
        }
        $sumOweTicketMoney = $sumOweTicketMoney['data'];
        if ($sumOweTicketMoney >= $summoney) {
            $this->apiReturn(202, [], '选择的开票金额必须大于欠票金额');
        }
        //欠票金额分摊
        if ($sumOweTicketMoney > 0) {
            $endMoney = $summoney - $sumOweTicketMoney;
            $itemMoney = round($endMoney / count($body),2);
            end($body);
            $key_last = key($body);
            foreach ($body as $key => &$item){
                if ($key === $key_last) {
                    $item['commodityPrice'] = $endMoney;
                }else{
                    $item['commodityPrice'] = $itemMoney;
                    $endMoney -= $itemMoney;
                }
            }
        }

        unset($paramTemp['journalIds']);

        $paramTemp['serialNumber'] = $modelInvoice->getSerial();

        //回调接口回传参数
        $paramTemp['sumOweTicketMoney'] = $sumOweTicketMoney;

        $result = $this->_businessLib->addCommonInvoice($paramTemp, $body);

        if ($result) {
            $data = [
                'invoice_type' => 2,//普票
                'trade_no'     => $paramTemp['serialNumber'],//流水号
                'status'       => 6,//状态：0：未开票，1：审核通过，2：未通过，3：审核中,4：已开票,5.线下开票，6：开票中,7:开票失败
                'apply_time'   => time(),//申请时间
            ];

            $res = $modelInvoice->updateDataByIds($journalIds, $data);

            if (!$res) {
                pft_log('invoice/common', $modelInvoice->_sql());
                $cacheRedis->rm($keyLock);
                $this->apiReturn(400, [], '操作失败,请刷新重试');
            }
            //挪到 \JsonRpc\ElectronicInvoices\InvoiceApi::invoiceCallBack中处理了
            //更新审核时间
            //$updateData = ['examine_time' => time()];//审核通过 需要加下审核时间
            //$etRes = $modelInvoice->updateInvoiceByTradeNo($paramTemp['serialNumber'], $updateData);
            //if (!$etRes) {
            //    pft_log('invoice/common', "更新审核时间错误" . $modelInvoice->_sql());
            //}
            ////扣减欠票
            //if ($sumOweTicketMoney > 0) {
            //    $ordernum = 'owe_' . str_replace('.', '', microtime(true));
            //    $result = (new OweTicketAmount())->eductionOweTicketAmount($this->_memberId, $sumOweTicketMoney,
            //        $paramTemp['serialNumber'], $ordernum, '发票申请');
            //    if (!$result['code']) {
            //        pft_log('invoice/oweTicketMoney', $result['msg']);
            //        $this->apiReturn(400, [], $result['msg']);
            //    }
            //}
            $cacheRedis->rm($keyLock);
            $this->apiReturn(200, [], '操作成功');
        } else {
            $cacheRedis->rm($keyLock);
            $this->apiReturn(400, [], '操作失败');
        }
    }

    /**
     * 获取开票内容
     *
     * @date   2017-06-01
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function getInvoiceContent() {
        $result = $this->_businessLib->getInvoiceContent($this->_memberId);
        if ($result) {
            $this->apiReturn(200, $result, '操作成功');
        } else {
            $this->apiReturn(400, [], '没有相关数据');
        }
    }

    /**
     * 获取交易类型
     *
     * @date   2017-06-05
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function getTradeConf() {
        $conf = load_config('item_category', 'trade_record');
        $this->apiReturn(200, $conf, '');
    }

    /**
     * 设置配置
     *
     * @date   2017-06-15
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function setRate() {
        $params = $this->_processLib->setRate();
        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }
        $result = $this->_businessLib->setRate($this->_memberId, $params[1]);

        if ($result) {
            $this->apiReturn(200, [], '设置成功');
        } else {
            $this->apiReturn(400, [], '设置失败');
        }
    }

    /**
     * 票付通后台审核数导出
     *
     * @date   2017-07-19
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function invoiceCheckExcel() {
        $this->checkAuthor();
        $params = $this->_processLib->invoiceCheckExcel();

        $result = $this->_businessLib->invoiceCheckExcel($params);

        if ($result) {
            self::_export($result);
        } else {
            $this->apiReturn(201, [], '暂无数据');
        }
    }

    /**
     * 票付通后台审核数导出处理
     *
     * @date   2017-07-19
     * <AUTHOR> Lan
     *
     * @param  array  $data  待导出的数据
     */
    private static function _export($data) {
        $body = '申请时间,申请流水号,公司名称,交易类型,金额,状态,发票类型,快递单号'."\n";

        foreach ($data as $val) {
            $body .= date('Y-m-d H:i:s', $val['billingTime'])."\t".','.$val['invoiceSerialNumber']."\t".','.
                    $val['invoiceHeader'].','.strtr($val['transactionType'], ',', '-').','.
                    $val['commodityPrice']/100 . ','.self::getInvoiceStatus($val['status'], $val['invoiceType']).','.
                    $val['expressNumber']."\n";
        }
        $fileName = '发票审核数据导出-'.date('Y-m-d H:i:s').'.csv';
        header("Content-type:text/csv");
        header("Content-Disposition:attachment;filename=".$fileName);
        header('Cache-Control:must-revalidate,post-check=0,pre-check=0');
        header('Expires:0');
        header('Pragma:public');
        echo chr(0xEF).chr(0xBB).chr(0xBF);
        echo $body;
        die();
    }

    /**
     * 根据数字返回类型
     *
     * @date   2017-07-19
     * <AUTHOR> Lan
     *
     * @param  int  $status  状态
     * @param  int  $type    类型
     *
     * @return string
     */
    private static function getInvoiceStatus($status, $type){
        if ($type == 1) {
            if ($status == 0) {
                $string = '开票中';
            } else {
                $string = '已开票';
            }
            $string .= ',普票';
        } else {
            if ($status == 0) {
                $string = '待审核';
            } elseif ($status == 1) {
                $string = '已审核';
            } elseif ($status == 2) {
                $string = '未通过';
            } else {
                $string = '已邮寄';
            }
            $string .= ',专票';
        }
        return $string;
    }

    /**
     * 判断用户账号余额
     *
     * @date   2017-07-19
     * <AUTHOR> Lan
     *
     * @return string
     */
    private function checkMoney() {
        $accountBookApi = new \Business\JavaApi\Account\AccountBook();
        $balanceRes = $accountBookApi->queryOnePlatformBook($this->_memberId);

        if ($balanceRes['code'] != 200) {
            $this->apiReturn(208, [], '余额账本信息获取失败');
        }

        $money = $balanceRes['data']['money'];
        if (intval($money) < 0) {
            $this->apiReturn(208, [], '您的账户余额为负，不能开发票，请充值为正后再开！');
        }
    }

    /**
     * 权限验证
     *
     * @date   2017-07-19
     * <AUTHOR> Lan
     *
     * @return string
     */
    private function checkAuthor() {
        if (!$this->_isSuper) {
            $this->apiReturn(202, [], '无权操作');
        }
    }

    /**
     * 财务获取发票数据
     *
     * @date   2018-01-02
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function getFinancialInvoice() {
        $this->checkAuthor();
        $params = $this->_processLib->getFinancialInvoice();

        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $params = $params[1];

        $memberModel = new Member();

        if ($params['account']) {
            $memberInfo = $memberModel->getMemberInfo($params['account'], 'account', 'id');
            $params['fid'] = $memberInfo['id'];
        }

        $modelInvoice = new modelInvoice();

        $count = $modelInvoice->getJournalData($params, 'count(*)');

        if (empty($count)) {
            $this->apiReturn(400, [], '没有相关数据');
        }

        $field = 'id, fid,trade_no, trade_type, money, status, invoice_type, courier_no,time, invoice_no';
        $result = $modelInvoice->getJournalData($params, $field);

        $memberIdArr = array_column($result, 'fid');

        $memberNames = $memberModel->getMemberInfoByMulti($memberIdArr, 'id', 'id, account', true);

        $data = ['count' => $count, 'list' => ['invoice' => $result, 'name' => $memberNames]];

        $this->apiReturn(200, $data, '获取成功');
    }

    /**
     * 将发票设置为已开票
     *
     * @date   2018-01-12
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function setInvoiceOut() {
        $this->checkAuthor();
        $id = I('id', '', 'intval');
        if ($id == '') {
            $this->apiReturn(201, [], 'id参数必传');
        }

        $modelInvoice = new modelInvoice();

        $checkData = $modelInvoice->getDataByIds($id, 'id');
        
        if (!$checkData) {
            $this->apiReturn(201, [], '发票记录不存在');
        }
        $res = $modelInvoice->updateDataByIds($id, ['status' => 5]);

        if (!$res) {
            $this->apiReturn(400, [], '设置失败');
        }

        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 已开票填写快递单号
     *
     * @date   2018-01-04
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function addCourierOfOut() {
        $this->checkAuthor();
        $id     = I('post.id', '', 'intval');
        $number = I('post.number', '', 'strval');

        if (!$id || !$number) {
            $this->apiReturn(201, [], '参数错误');
        }

        $modelInvoice = new modelInvoice();

        $checkData = $modelInvoice->getDataByIds($id, 'id');

        if (!$checkData) {
            $this->apiReturn(201, [], '发票记录不存在');
        }
        $res = $modelInvoice->updateDataByIds($id, ['courier_no' => $number]);

        if (!$res) {
            $this->apiReturn(400, [], '设置失败');
        }

        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 获取当前欠票金额
     *
     * @date   2021-03-31
     * <AUTHOR>
     *
     *
     * @return array
     */
    public function getOweTiketSum()
    {
        if (!$this->_memberId) {
            $this->apiReturn(400, [], '请登陆');
        }
        $sumOweTicketMoney = (new OweTicketAmount())->getOweTiketSum($this->_memberId);
        if ($sumOweTicketMoney['code'] != 200) {
            $this->apiReturn($sumOweTicketMoney['code'] ,[], $sumOweTicketMoney['msg']);
        }
        $this->apiReturn(200, $sumOweTicketMoney['data']);
    }

    /**
     * 同步开票状态
     * @return void
     */
    public function syncInvoiceStatus()
    {
        if (!$this->_memberId) {
            $this->apiReturn(400, [], '请登陆');
        }
        /** @var $redis \Library\Cache\CacheRedis; */
        $redis = \Library\Cache\CacheRedis::getInstance('redis');
        $lockKey = 'lock:syncInvoiceStatus:' . $this->_memberId;
        $timestamp = time();
        if (!$redis->lock($lockKey, $timestamp, 3 * 60)) {
            $startTime = $redis->lock_get($lockKey);
            if ($startTime) {
                $endTime = date('i分s秒', max(3 * 60 + $startTime - $timestamp, 1));
                $this->apiReturn(400, [], "请在{$endTime}后再次刷新开票状态");
            }
        }

        $paramTemp = [];
        $paramTemp['fid'] = $this->_memberId;
        $paramTemp['startDate'] = $paramTemp['endDate'] = $paramTemp['type'] = $paramTemp['tradeNo'] = '';
        $paramTemp['status'] = 6; // 开票中
        $paramTemp['currentPage'] = 1;
        $paramTemp['pagerRows'] = 999; //每次查询数量改成999

        $modelInvoice = new modelInvoice();
        $field = 'id, fid, trade_no, trade_type, money, status, invoice_type, courier_no,time, invoice_no, remark';
        $result = $modelInvoice->getJournalData($paramTemp, $field);
        if (!$result) {
            $this->apiReturn(self::CODE_SUCCESS, [], '同步成功');
        }
        $tradeNos = array_column($result, 'trade_no');
        $result = $this->_businessLib->syncInvoiceStatus($this->_memberId, $tradeNos);
        if (!$result) {
            $redis->lock_rm($lockKey);
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '同步失败');
        }
        $this->apiReturn(self::CODE_SUCCESS, [], '同步成功');
    }
}