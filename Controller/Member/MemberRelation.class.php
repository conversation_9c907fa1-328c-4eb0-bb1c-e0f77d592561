<?php

/**
 * 用户关系控制器
 *
 * <AUTHOR>
 */

namespace Controller\Member;

use Business\CommodityCenter\Product;
use Business\JavaApi\Account\AccountBook;
use Business\JavaApi\Member\MemberQuery;
use Business\Member\MemberPrivacy as MemberPrivacyBiz;
use Business\Member\MemberRelation as MemberRelationBiz;
use Library\Cache\Cache;
use Library\Constants\Account\BookSubject;
use Library\Controller;
use Business\Member\MemberRelation as RelationBiz;
use Library\MessageNotify\OrderNotify;
use Library\Tools\Helpers;
use Model\Member\MemberRelationship;
use Model\Member\PriorityReseller;
use Model\Product\PriceGroup as PriceGroupModel;
use Model\Product\PriceGroup as Group;
use Model\Product\Ticket;

class MemberRelation extends Controller
{
    /**
     * 初始化，添加某些方法的访问频率控制
     * <AUTHOR>
     * @date 2020-01-20
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 根据手机号搜索分销商(供应商)
     * <AUTHOR>
     * @date   2018-10-30
     */
    public function mobileSearchForDistributor()
    {
        $sid = $this->isLogin('ajax');

        $mobile = I('mobile');
        if (!$mobile || !ismobile($mobile)) {
            $this->apiReturn(204, [], '请输入正确的手机号');
        }

        $relationBiz = new RelationBiz();
        $result      = $relationBiz->mobileSearchForDistributor($sid, $mobile);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 添加分销商发送短信验证码
     * <AUTHOR>
     * @date   2018-10-30
     */
    public function sendVcodeForAddDis()
    {

        $this->isLogin('ajax');

        $mobile = I('mobile');
        $graph  = I('graph', '', 'strtolower');
        if (!$mobile || !ismobile($mobile)) {
            $this->apiReturn(204, [], '请输入正确的手机号');
        }
        if (!$graph) {
            $this->apiReturn(204, [], '请输入图形验证码');
        }

        $serverCode = I('session.auth_code', '', 'strtolower');
        if ($serverCode != $graph) {
            $this->apiReturn(204, [], '图形验证码错误');
        }
        unset($_SESSION['auth_code']);

        $relationBiz = new RelationBiz();
        $result      = $relationBiz->sendVcodeForAddDis($mobile);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /***
     * 供应商获取指定合作分销商的近期销售数据
     * @author: Cai Yiqiang
     * @date: 2019/1/24
     */
    public function getDisSaleInfoForSupplier()
    {
        $sid = $this->isLogin('ajax');
        $fid = I('post.fid', 0, 'intval');

        if (!$fid) {
            $this->apiReturn(201, [], '参数错误');
        }

        //暂时用缓存处理----以后如果数据要做更多用途 更换更合理的方法
        $expTime   = 12 * 60 * 60;
        $key       = "$sid _$fid _distributor_sale_data:getDisSaleInfoForSupplier";
        $cache     = \Library\Cache\Cache::getInstance('redis');
        $cacheData = $cache->get($key);

        if ($cacheData) {
            $data = json_decode($cacheData);
            $this->apiReturn(200, $data, 'success');
        } else {
            $memRelationBiz = new RelationBiz();

            $result = $memRelationBiz->getDisSaleInfoForSupplier((int)$sid, $fid);

            if ($result['code'] != 200) {
                $this->apiReturn($result['code'], [], $result['msg']);
            }
            $cache->set($key, json_encode($result['data']), '', $expTime);
            $this->apiReturn(200, $result['data'], 'success');
        }

    }

    /**
     * 处理分销商列表查询参数
     * @author: guanpeng
     * @date: 2019/5/13
     *
     * @param $sid
     *
     * @return array
     */
    private function handlerPartnersSearchParams($sid)
    {
        $searchMap = [
            'sid'      => $sid,
            'mobile'   => '',
            'dname'    => '',
            'account'  => '',
            'province' => '',
            'city'     => '',
            'ctype'    => [],
            'cname'    => '',
            'remark'   => '',
        ];
        $keyword   = I('get.keyword', '', 'safetxt');
        $ctype     = I('get.ctype', '', 'safetxt');
        $province  = I('get.province', '', 'intval');
        $city      = I('get.city', '', 'intval');

        // 关键字查询,分销商名称只支持dname查询
        if ($keyword != '') {
            //默认
            //$searchMap['remark'] = $keyword; //暂不支持备注查询
            if (is_numeric($keyword)) {
                if (Helpers::isMobile($keyword)) {
                    $searchMap['mobile'] = $keyword;
                } elseif (strlen($keyword) > 5) {
                    $searchMap['account'] = $keyword;
                } else {
                    return $searchMap;
                }
            } else {
                $searchMap['dname'] = $keyword;
                $searchMap['cname'] = $keyword;
            }
        }
        // 分销商类型
        if ($ctype != '') {
            $ctype              = explode(',', $ctype);
            $MemberBus          = new \Business\Member\Member();
            $ctype              = $MemberBus->getCorpKindArr($ctype);
            $searchMap['ctype'] = $ctype;
        }
        if ($province > 0) {
            $searchMap['province'] = $province + 0;
        }
        if ($city > 0) {
            $searchMap['city'] = $city + 0;
        }
        //$cnt =0;
        //foreach ($searchMap as $item) {
        //    if (empty($item)) {
        //        $cnt += 1;
        //    }
        //}
        //if ($cnt < 7) {
        //    return [];
        //}
        return $searchMap;
    }

    /**
     * 将分页统计的数据缓存到redis里面3分钟
     * @author: guanpeng
     * @date: 2019/5/23
     *
     * @param  int  $type  查下类型
     * @param  int  $sid  供应商ID
     * @param  MemberRelationship  $relationShipModel
     * @param  array  $didArr
     * @param  int  $groupid  分组ID
     *
     * @return array
     */
    private function getTotalPage($type, $sid, MemberRelationship $relationShipModel, array $didArr = [], $groupid = 0, $pageSize = 20)
    {
        $cacheInfo = MemberRelationBiz::getMyPartnerTotalCache($sid, $type, $groupid);
        if (ENV == 'LOCAL') {
            $cacheInfo = false;
        }
        if ($cacheInfo) {
            list($total, $totalPage) = explode(',', $cacheInfo);
        } else {
            //$pageSize    = 20;
            if ($type == 0) {
                $total = 0;
                $relationBiz = new \Business\Member\MemberEvoluteRelation();
                $result      = $relationBiz->queryGroupNameListBySidAndGroupName($sid, '', 1, 1);
                if ($result['code'] == 200 && !empty($result['data']['list'])) {
                    $total = $result['data']['total'];
                }
                //$total     = self::getGroupModel()->countGroupsBySid($sid);
                $totalPage = ceil($total / $pageSize);
            } elseif ($type == 1) {
                $total     = $relationShipModel->getUnGroupedFidCount($sid);
                $totalPage = ceil($total / $pageSize);
            } else { //if ($type ==2 || $type == 3)
                $total     = count($didArr);
                $totalPage = ceil($total / $pageSize);
            }
            MemberRelationBiz::setMyPartnerTotalCache($sid, $type, $groupid, $total, $totalPage);
        }

        return [$total + 0, $totalPage + 0];

    }

    /**
     * /r/Member_MemberRelation/getGroupAndDistributorList
     * 实现功能 = 分销商分组列表(分页) ,每个组显示固定数量的分销商详情列表
     *
     * @param  page       页码
     * @param  page_size  每页显示数量
     * @param  keyword    搜索关键字
     * @param  ctype      分销商类型
     * @param  province   省份id
     * @param  city       城市id
     *
     * @return string | json
     *
     */
    public function getGroupAndDistributorList()
    {
        // 前端参数获取
        $pageNum  = I('get.page', 1, 'intval');
        $pageSize = I('get.page_size', 20, 'intval');

        $loginInfoArr = $this->getLoginInfo();
        $sid          = $loginInfoArr['sid'];
        $memberId     = $loginInfoArr['memberID'];

        $searchParamArr = $this->handlerPartnersSearchParams($sid);
        if (empty($searchParamArr)) {
            parent::apiReturn(205, [], '请完善搜索信息后再操作');
        }

        $mobile          = $searchParamArr['mobile'];
        $province        = $searchParamArr['province'];
        $city            = $searchParamArr['city'];
        $distributorName = $searchParamArr['dname'];
        $contact         = $searchParamArr['cname'];
        $account         = $searchParamArr['account'];
        $corpKindArr     = $searchParamArr['ctype'];

        $memberRelationBiz = new \Business\Member\MemberRelation();
        $listArr           = $memberRelationBiz->getGroupAndDistributorList($sid, $distributorName, $account, $mobile,
            $contact, $province,
            $city, $corpKindArr, $pageNum, $pageSize);

        if ($listArr['code'] == 200) {
            parent::apiReturn(200, $listArr['data'], $listArr['msg']);
        } else {
            parent::apiReturn(205, [], $listArr['msg']);
        }
    }

    /**
     * /r/Member_MemberRelation/getInGroupDistributorList
     * 实现功能 = 分组内分销商的列表(分页)
     *
     * @param  page       页码
     * @param  page_size  每页显示数量
     * @param  group_id   分组id
     * @param  keyword    搜索关键字
     * @param  ctype      分销商类型
     * @param  province   省份id
     * @param  city       城市id
     *
     * @return string | json
     *
     */
    public function getInGroupDistributorList()
    {
        // 前端参数获取
        $pageNum  = I('get.page', 1, 'intval');
        $pageSize = I('get.page_size', 20, 'intval');
        $groupId  = I('get.group_id', '', 'intval');

        if (empty($groupId)) {
            parent::apiReturn(203, [], '缺失分组id');
        }

        $loginInfoArr = $this->getLoginInfo();
        $sid          = $loginInfoArr['sid'];
        $memberId     = $loginInfoArr['memberID'];

        $searchParamArr = $this->handlerPartnersSearchParams($sid);
        if (empty($searchParamArr)) {
            parent::apiReturn(205, [], '请完善搜索信息后再操作');
        }

        $mobile          = $searchParamArr['mobile'];
        $province        = $searchParamArr['province'];
        $city            = $searchParamArr['city'];
        $distributorName = $searchParamArr['dname'];
        $contact         = $searchParamArr['cname'];
        $account         = $searchParamArr['account'];
        $corpKind        = empty($searchParamArr['ctype'][0]) ? '' : $searchParamArr['ctype'][0];

        $memberRelationBiz = new \Business\Member\MemberRelation();
        $listArr           = $memberRelationBiz->getInGroupDistributorList($sid, $groupId, $distributorName, $account,
            $mobile, $contact, $province,
            $city, $corpKind, $pageNum, $pageSize);
        if ($listArr['code'] == 200) {
            parent::apiReturn(200, $listArr['data'], $listArr['msg']);
        } else {
            parent::apiReturn(205, [], $listArr['msg']);
        }
    }

    /**
     * /r/Member_MemberRelation/getDistributorGroupList
     * 获取供应商下未分组和普通列表
     *
     * @param  sid 供应商id
     *
     * @return array
     *
     */
    public function getDistributorGroupList()
    {
        $loginInfoArr = $this->getLoginInfo();
        $sid          = $loginInfoArr['sid'];
        $memberId     = $loginInfoArr['memberID'];

        $evoluteGroupBiz = new \Business\Product\Get\EvoluteGroup();
        $groupListArr    = $evoluteGroupBiz->getGroupListBySid($sid);

        if ($groupListArr['code'] == 200) {
            parent::apiReturn(200, $groupListArr['data'], 'succes');
        } else {
            parent::apiReturn(205, [], $groupListArr['msg']);
        }
    }

    /**
     * 合作分销商列表页面
     * /r/Member/MemberRelation_getMyPartners
     * @author: guanpeng
     * @date: 2019/5/13
     */
    public function getMyPartners()
    {
        $actionType = I('get.action');// 获取的数据类型，0获取分组列表，1获取未分组的分销商列表,2根据分组ID获取分销商列表
        $page       = I('get.page', 1, 'intval');
        $groupid    = I('get.price_groupid', 0, 'intval');
        $pageSize   = I('get.page_size', 20, 'intval');
        $sid        = $this->getLoginInfo('ajax')['sid'];

        $relationShipModel   = new MemberRelationship($sid);
        $didArr              = $groupList = $partners = [];
        if ($actionType == 0) {
            // 分组数据
            //$totalGroups =  self::getGroupModel()->countGroupsBySid($sid);
            $totalInfo = self::getTotalPage($actionType, $sid, $relationShipModel, [], 0, $pageSize);
            if ($page > $totalInfo[1]) {
                $page = $totalInfo[1];
            }
            $groupList['total_size'] = $totalInfo[0];
            $groupList['total_page'] = $totalInfo[1];

            $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
            $resellerGroup      = $getEvoluteGroupBiz->getFidCountBySid($sid);
            $groupList['list']  = [];
            if ($resellerGroup['code'] == 200 && !empty($resellerGroup['data'])) {
                $groupList['list'] = array_values($resellerGroup['data']);
            }

            //$groupList['list']       = self::getGroupModel()->getGroupsBySid($sid, 'id,name,dids,default,default_inc',
            //    false, $page, $pageSize);//取出分组分销商数据pft_price_group
            if (!$groupList['list']) {
                // 调用新版添加默认分组
                $addEvoluteGroupBiz = new \Business\Product\Add\EvoluteGroup();
                $addRes             = $addEvoluteGroupBiz->addGroup($sid, '旅行社', $sid);
                $addEvoluteGroupBiz->addGroup($sid, '酒店', $sid);
                if ($addRes['code'] == 200 && !empty($addRes['data'])) {
                    //调用新版接口
                    $upEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroup();
                    $upEvoluteGroupBiz->setDefaultGroup($sid, $sid, $addRes['new_group']);
                }

                parent::apiReturn(202, [], '您尚未添加过分组，系统以及为您默认创建了“旅行社”和“酒店”分组，请刷新页面后操作”');
            }
            foreach ($groupList['list'] as $key => $group) {
                $landIdArr = $productIdArr = [];

                $groupList['list'][$key]['landCount']   = count($landIdArr) > 0 ? count($landIdArr) : 0;
                $groupList['list'][$key]['ticketCount'] = count($productIdArr) > 0 ? count($productIdArr) : 0;

                $groupList['list'][$key]['total'] = count($group['fidArr']);
                unset($groupList['list'][$key]['fidArr']);
            }

        } elseif ($actionType == 1) {
            // 获取未分组数据
            $totalInfo = self::getTotalPage($actionType, $sid, $relationShipModel, [], 0, $pageSize);
            if ($page > $totalInfo[1]) {
                $page = $totalInfo[1];
            }
            $partners['total_size'] = $totalInfo[0];
            $partners['total_page'] = $totalInfo[1];
            $didArr                 = $relationShipModel->getUnGroupedFidList($sid, $page, $pageSize);
        } elseif ($actionType == 2) {
            $didArr          = [];
            $groupBiz        = new \Business\Product\Update\EvoluteGroup();
            $originGroupInfo = $groupBiz->queryByGroupId($groupid);
            if ($originGroupInfo['code'] == 200 && !empty($originGroupInfo['data'])) {
                $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
                $getResArr          = $getEvoluteGroupBiz->getFidArrByGroupId($sid, $groupid);
                if ($getResArr['code'] == 200 && !empty($getResArr['data'])) {
                    $didArr = $getResArr['data'];
                }
            }

            // 处理分页
            $page                   = $page - 1;
            $totalInfo              = self::getTotalPage($actionType, $sid, $relationShipModel, $didArr, $groupid,
                $pageSize);
            $partners['total_size'] = $totalInfo[0];
            $partners['total_page'] = $totalInfo[1];
            $totalInfo[1]           -= 1;
            if ($page > $totalInfo[1]) {
                $page = $totalInfo[1];
            }
            $dataPage = array_chunk($didArr, $pageSize);
            $didArr   = $dataPage[$page];
        } elseif ($actionType == 3) {
            $searchParams = $this->handlerPartnersSearchParams($sid);
            if (!empty($searchParams)) {
                $searchResult = MemberRelationBiz::getMyPartnerSearchCache($sid, $searchParams);
                if (!$searchResult) {
                    $searchResult = call_user_func_array([$relationShipModel, 'partnerRelationSearch'], $searchParams);
                    if ($searchResult) {
                        MemberRelationBiz::setMyPartnerSearchCache($sid, $searchParams, $searchResult);
                    }
                }
            }
            if (!empty($searchResult)) {
                $didArr = array_keys($searchResult);

                $partners['total_size'] = count($didArr);//$totalInfo[0];
                $partners['total_page'] = 1;//$totalInfo[1];
            }
        }

        if (is_array($didArr) && count($didArr) > 0) {
            $partners['list'] = $this->getPartnerListDetail($relationShipModel, $sid, $didArr, $groupid);
            if ($actionType == 3 && isset($searchResult)) {
                // 获取分组数据
                $groupIds                = array_column($searchResult, 'price_group_id');

                $groupBiz        = new \Business\Product\Update\EvoluteGroup();
                $originGroupInfo = $groupBiz->queryAllGroupsBySidAndGroupName($sid, '', $groupIds);

                $total             = 0;
                $groupList['list'] = [];
                if ($originGroupInfo['code'] == 200 && !empty($originGroupInfo['data']['list'])) {
                    $total = $originGroupInfo['data']['total'];
                    foreach ($originGroupInfo['data']['list'] as $group) {
                        $groupList['list'][] = [
                            'id'          => $group['id'],
                            'name'        => $group['groupName'],
                            'default'     => $group['groupDefault'],
                            'landCount'   => 0,
                            'ticketCount' => 0,
                        ];
                    }
                }

                $groupList['total_size'] = $total;
                $groupList['total_page'] = 1;

                foreach ($partners['list'] as $key => $partner) {
                    $partners['list'][$key]['price_group_id'] = $searchResult[$partner['id']]['price_group_id'];
                }
            }
            if (($actionType == 1 || $actionType == 2) && empty($searchResult)) {
                $searchResult = $relationShipModel->partnerRelationSearch($sid);
            }
            if (!empty($searchResult)) {
                foreach ($partners['list'] as $key => $partner) {
                    //加入分销商备注名称
                    $partners['list'][$key]['remark'] = $searchResult[$partner['id']]['remark'] ?? '';
                }
            } else {
                foreach ($partners['list'] as $key => $partner) {
                    //加入分销商备注名称
                    $partners['list'][$key]['remark'] = '';
                }
            }
        }

        parent::apiReturn(200, ['groups' => $groupList, 'partners' => $partners], 'success');
    }

    private static $GroupModel = null;

    /**
     * 获取分组模型
     *
     * @return PriceGroupModel
     */
    private static function getGroupModel()
    {
        if (!self::$GroupModel) {
            self::$GroupModel = new Group();
        }

        return self::$GroupModel;
    }

    /**
     * 获取分销商列表
     * @author: guanpeng
     * @date: 2019/5/13
     *
     * @param  MemberRelationship  $relationShipModel
     * @param $sid int
     * @param $didArr array
     * @param $groupId int
     *
     * @return array
     */
    private function getPartnerListDetail(MemberRelationship $relationShipModel, $sid, $didArr, $groupId = 0)
    {
        $memberBiz = new \Business\Member\Member();

        $distributors = [];
        $resData      = $memberBiz->getList($didArr, true);
        foreach ($resData as $k => $v) {
            $distributors[] = [
                'corp_kind' => $v['corp_kind'],
                'id'        => $v['id'],
                'dname'     => $v['dname'],
                'cname'     => $v['cname'],
                'account'   => $v['account'],
                'mobile'    => $v['mobile'],
                'dtype'     => $v['dtype'],
            ];
        }

        //获取用户的账户余额和授信额度
        $credits = $relationShipModel->getCreditForMulti($sid, $didArr);
        //结算方式
        $relationBiz       = new RelationBiz();
        $memberRelationBus = new \Business\Member\MemberRelation();
        $res               = $memberRelationBus->getDisClearingModels($sid, $didArr);
        $saleInfo          = $relationBiz->getBatchSaleInfoForSupplier($sid, $didArr);
        $wayInfos = (new \Business\Member\MemberRelation())->getAidClearingModelsAndRepayMode($didArr, $sid);

        //获取用户的账户余额和授信额度
        $accountBookApi = new AccountBook();
        $queryRes       = $accountBookApi->queryBatchBookForManyDistributors($didArr, $sid,
            [BookSubject::CREDIT_SUBJECT_CODE, BookSubject::PRE_CREDIT_SUBJECT_CODE]);
        if ($queryRes['code'] == 200) {
            $subjectMap = $queryRes['data'];
        } else {
            $subjectMap = [];
        }

        foreach ($distributors as $key => $dis) {
            $dis['price_group_id'] = $groupId;
            $dis['clearingWay']    = isset($res[$dis['id']]) ? $res[$dis['id']] : 0;
            $dis['repay_mode']     = isset($wayInfos[$dis['id']]['repayMode']) ? $wayInfos[$dis['id']]['repayMode'] : 0;
            $dis['sale_info']      = $saleInfo[$dis['id']];
            if (isset($credits[$dis['id']])) {
                $dis['remain'] = $credits[$dis['id']]['kmoney'];
                if ($credits[$dis['id']]['mode'] == 1) {
                    //后结模式,授信额度与可用额度都显示为不限
                    $dis['limit'] = '不限';
                    $dis['total'] = '不限';
                    //已用额度
                    $dis['used_limit'] = $dis['remain'] < 0 ? -$dis['remain'] : 0;
                    //剩余额度
                    $dis['remain_limit'] = '不限';
                    //可用金额
                    $dis['usable_money'] = '不限';
                } else {
                    //正常模式
                    $dis['limit'] = $credits[$dis['id']]['basecredit'];
                    $dis['total'] = $credits[$dis['id']]['credit'];
                    //已用额度
                    $dis['used_limit'] = $dis['remain'] < 0 ? -$dis['remain'] : 0;
                    //剩余额度
                    $dis['remain_limit'] = $dis['limit'] - $dis['used_limit'];
                    //可用金额
                    $dis['usable_money'] = $dis['total'];
                }

            } else {
                $dis['remain'] = 0;
                $dis['limit']  = 0;
                $dis['total']  = 0;
                //已用额度
                $dis['used_limit'] = 0;
                //剩余额度
                $dis['remain_limit'] = 0;
                //可用金额
                $dis['usable_money'] = 0;
            }
            if (isset($subjectMap[BookSubject::PRE_CREDIT_SUBJECT_CODE][$dis['id']])) {
                $dis['prestore'] = $subjectMap[BookSubject::PRE_CREDIT_SUBJECT_CODE][$dis['id']]['balanceMoney'];
            } else {
                $dis['prestore'] = 0;
            }
            $distributors[$key] = $dis;
        }

        return $distributors;
    }

    /**
     * 添加平台未注册用户的分销商
     * @author: linchen
     * @date: 2019/10/24
     *
     * @param $mobile string
     *
     * @return array
     */
    public function addUnRegisterDistributor()
    {
        $memberInfo = $this->getLoginInfo();
        $mobile     = I('post.mobile', '', 'string');
        $groupId    = I('post.group_id', 0, 'intval');
        if (!$mobile || !ismobile($mobile)) {
            $this->apiReturn(204, [], '电话号码错误');
        }
        if ($groupId < 0) {
            $this->apiReturn(204, [], '组id错误');
        }

        $memberRelationBiz = new MemberRelationBiz();
        $res               = $memberRelationBiz->addUnRegisterDistributor($mobile, $memberInfo['sid'], $groupId,
            $memberInfo['dname'], $memberInfo['memberID']);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 添加通过手机短信注册的分销商
     * @author: linchen
     * @date: 2019/10/24
     *
     * @param $mobile string 电话
     * @param  id int 日志id
     * @param  password string 密码
     *
     * @return array
     */
    public function addUnRegisterDistributorMemberRelationByPhone()
    {
        $mobile = I('post.mobile', '');
        $id     = I('post.id', '');
        $pwd    = I('post.password', '');
        if (!$mobile || !$id || !$pwd) {
            $this->apiReturn(204, [], '参数有误');
        }
        $pwd    = safetxt($pwd);
        $mobile = OrderNotify::url_sms_decode($mobile)[0];
        $id     = OrderNotify::url_sms_decode($id)[0];
        if (!ismobile($mobile) || !is_numeric($id) || !$pwd) {
            $this->apiReturn(204, [], '提交参数有误');
        }

        $memberRelationBiz = new MemberRelationBiz();
        $result            = $memberRelationBiz->addDistributorByPhone($id, $mobile, $pwd);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 通过distribution_check id获取信息
     * @author: linchen
     * @date: 2019/10/24
     *
     * @param  id int 日志id
     *
     * @return array
     */
    public function getRelationPhone()
    {
        $id     = I('get.id', '');
        $mobile = I('get.mobile', '');
        if (!$id) {
            $this->apiReturn(204, [], '参数有误');
        }
        $id     = OrderNotify::url_sms_decode($id)[0];
        $mobile = OrderNotify::url_sms_decode($mobile)[0];

        if (!is_numeric($id)) {
            $this->apiReturn(204, [], '提交参数有误');
        }
        $memberRelationBiz = new \Business\Member\DistributorAudit();
        $result            = $memberRelationBiz->getDistributionCheck($id, 0, $mobile);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 短信添加注册过的用户成分销商
     * @author: linchen
     * @date: 2019/10/24
     *
     * @param  id int 日志id
     * @param  mobile string 电话
     *
     * @return array
     */
    public function addRegisterDistributorMemberRelationByPhone()
    {
        $id     = I('post.id', '');
        $mobile = I('post.mobile', '');
        if (!$id) {
            $this->apiReturn(204, [], '参数有误');
        }
        $id                = OrderNotify::url_sms_decode($id)[0];
        $mobile            = OrderNotify::url_sms_decode($mobile)[0];
        $memberRelationBiz = new MemberRelationBiz();
        $result            = $memberRelationBiz->addDistributorBySms($id, $mobile);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 添加分销商
     * @author: linchen
     * @date: 2019/10/24
     *
     * @param  int son_id 分销商id
     *
     * @return array
     */
    public function addDistributorMemberRelation()
    {
        $memberInfo = $this->getLoginInfo();
        $sonId      = I('post.son_id', 0);
        $groupId    = I('post.group_id', 0);
        $notice     = I('post.notice', 1);   // 1短信 2微信
        $remark     = I('post.remark', '');   // 备注信息

        $memberId = $memberInfo['sid'];
        $limitKey = "platform:add_distributor:{$memberId}";

        $cacheObj = Cache::getInstance('redis');
        $lockTime = 1;
        $lockInfo = $cacheObj->lock($limitKey, 1, $lockTime);
        if (!$lockInfo) {
            $this->apiReturn(500, [], '请求过于频繁');
        }
        if (!is_numeric($sonId) || $sonId <= 0) {
            $this->apiReturn(204, [], '参数有误');
        }

        //隐私验证
        $vcode    = I('post.vcode', '', 'strval');
        $token    = I('post.token', '', 'strval');//验证码token

        $memberPrivacyBiz = new MemberPrivacyBiz();
        //格式化参数
        $verifData = $memberPrivacyBiz->verifCodeParamsFormat($memberInfo['sid'], $memberInfo['memberID'], $vcode, $token);
        //验证信息
        $verifResult = $memberPrivacyBiz->addDistributorPrivacyAuth($verifData);
        if ($verifResult['code'] != 200) {
            $this->apiReturn($verifResult['code'], $verifResult['data'], $verifResult['msg']);
        }

//        //限制用户3次后就要输入验证码了
//        $checkCount = $this->_checkAddMemberRelationCount($memberInfo['sid']);
//        if ($checkCount) {
//            $authImage = $this->_authImage();
//            if (!$authImage) {
//                $this->apiReturn(403, [], '图形验证码错误');
//            }
//        }

        //分组数量的限制
        $groupLimitRes = $this->_groupDistributorNumLimit($memberInfo['sid'], $groupId);
        if ($groupLimitRes[0] != 200) {
            $this->apiReturn($groupLimitRes[0], '', $groupLimitRes[1]);
        }

        $memberRelationBiz = new MemberRelationBiz();
        $result            = $memberRelationBiz->addDistributor($memberInfo['sid'], $sonId, $memberInfo['memberID'],
            $groupId, true, $notice, $remark);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 批量添加分销商
     * @author: linchen
     * @date: 2019/10/24
     *
     * @vacation batchAddDistributor
     *
     * @param  userInfo array 用户数组
     *
     * @return array
     */
    public function batchAddDistributor()
    {
        $memberInfo = $this->getLoginInfo();

        //隐私验证
        $vcode    = I('post.vcode', '', 'strval');
        $token    = I('post.token', '', 'strval');//验证码token

        $memberPrivacyBiz = new MemberPrivacyBiz();
        //格式化参数
        $verifData = $memberPrivacyBiz->verifCodeParamsFormat($memberInfo['sid'], $memberInfo['memberID'], $vcode, $token);
        //验证信息
        $verifResult = $memberPrivacyBiz->batchAddDistributorPrivacyAuth($verifData);
        if ($verifResult['code'] != 200) {
            $this->apiReturn($verifResult['code'], $verifResult['data'], $verifResult['msg']);
        }

        $groupId = I('post.group_id', 0);
        $notice  = I('post.notice', 1);   // 1短信 2微信
        $file    = $_FILES['file'];
        if (empty($file)) {
            $this->apiReturn(204, [], '数据为空');
        }
        $fileName = $file['tmp_name'];
        if ($fileName == '') {
            $this->apiReturn(204, [], '请选择要上传的文件');
        } elseif (strpos($file['name'], '.csv') === false) {
            $this->apiReturn(204, [], '文件格式有误');
        } elseif ($file['size'] > 2048) {
            $this->apiReturn(204, [], '最大2M');
        }

        $userInfo  = $this->handleAddDistributorParam($fileName);
        $countUser = count($userInfo);
        if ($countUser > 20) {
            $this->apiReturn(204, [], '批量操作目前只允许一次20人');
        }

        //添加数量限制
        $addNumLimit = (new \Business\Member\DistributorAudit())->addDistributorLimit($memberInfo['sid']);
        if ($addNumLimit['code'] != 200) {
            return $this->returnData($addNumLimit['code'], $addNumLimit['msg']);
        }

        //分组数量的限制
        $groupLimitRes = $this->_groupDistributorNumLimit($memberInfo['sid'], $groupId, $countUser);
        if ($groupLimitRes[0] != 200) {
            $this->apiReturn($groupLimitRes[0], '', $groupLimitRes[1]);
        }

        $memberRelationBiz = new MemberRelationBiz();
        $result            = $memberRelationBiz->batchAddDistributor($userInfo, $memberInfo['sid'],
            $memberInfo['memberID'], $groupId, $memberInfo['dname']);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 获取合作分销商变更记录
     *
     * <AUTHOR>
     * @date 2020/3/6
     *
     */
    public function getCooperationChangeLog()
    {
        $sid             = $this->isLogin('ajax');
        $operationState  = I('operation_state', '', 'strval');      //操作状态 0 失败 1成功  空 全部
        $operationType   = I('operation_type', '', 'strval');       //操作类型 0 删除 1 新增 2 修改 3导出 空 全部
        $operationObject = I('operation_object', '', 'strval');     //操作对象字符串
        $operator        = I('operator', '', 'strval');             //操作人搜索关键字
        $operatorType    = I('operatorType', '', 'strval');         //操作人搜索类型 1:账号搜索  2: 操作者名称
        $beginTime       = I('beginTime', '', 'strval');            //操作开始时间
        $endTime         = I('endTime', '', 'strval');              //操作结束时间
        $pageNum         = I('pageNum', 0, 'intval');
        $pageSize        = I('pageSize', 0, 'intval');
        //获取操作记录日志
        $memberRelationBz = new MemberRelationBiz();
        $result           = $memberRelationBz->getCooperationChangeLog($sid, $operationState, $operationType,
            $operationObject, $operator, $operatorType, $beginTime, $endTime, $pageNum, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取分销商
     * @author: linchen
     * @date: 2019/10/24
     *
     * @param  key_word string 关键字
     *
     * @return array
     */
    public function getDistributorInfoByPhoneOrAccount()
    {
        $memberInfo = $this->getLoginInfo();

        //暂时做下请求频率的限制，一个用户1秒只能请求一次
        //这个家伙通过机器找了一批的手机号来刷这个接口，然会把没有添加为供应商的手机号之类的给记录到csv里面去，然会通过批量导入功能把给这些人发要求短信
        $memberId = $memberInfo['sid'];
        $limitKey = "platform:get_distributor:{$memberId}";

        $cacheObj = Cache::getInstance('redis');
        $lockTime = 1;
        $lockInfo = $cacheObj->lock($limitKey, 1, $lockTime);
        if (!$lockInfo) {
            $this->apiReturn(500, [], '请求过于频繁');
        }

        $searchInfo = I('get.key_word', '');
        $searchType = I('get.search_type', -1, 'intval');   //搜索类型 默认-1 1账号 2手机号 3企业/个人实名
        $version  = I('get.version', 'v1', 'strval');//版本号
        if (empty($searchInfo)) {
            $this->apiReturn(204, [], '请输入关键字');
        }

        $vcode    = I('get.vcode', '', 'strval');
        $token    = I('get.token', '', 'strval');//验证码token
        $type     = I('get.type', 0, 'intval');//1是联系人；2手机号
        $value    = I('get.value', '', 'strval');//验证值
        $account  = I('get.account', '', 'strval');//多账户校验通过后 将对应的账号带入

        $verifData = (new MemberPrivacyBiz())->verifCodeParamsFormat($memberInfo['sid'], $memberInfo['memberID'], $vcode, $token, $type, $value);

        //这家伙频繁的添加被人作为分销商 这边记录下是通过什么添加的
        if ($memberId == 368579) {
            pft_log('debug/distributor', json_encode(
                [
                    'ac'         => 'getDistributorInfoByPhoneOrAccount',
                    'searchInfo' => $searchInfo,
                    'time'       => date('Y-m-d H:i:s'),
                ], JSON_UNESCAPED_UNICODE
            ));
        }

        $memberRelationBiz = new MemberRelationBiz();
        $result            = $memberRelationBiz->getDistributorInfoByPhoneOrAccount($memberInfo['sid'], $searchInfo, $verifData, $searchType, $account, $version);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 处理传上来的csv参数
     * @author: linchen
     * @date: 2019/10/24
     *
     * @param  userInfo array 用户数组
     *
     * @return array
     */
    private function handleAddDistributorParam($name)
    {
        $handle = fopen($name, 'r');
        if ($handle === false) {
            return [];
        }
        $val  = ['mobile', 'name', 'type', 'cname', 'ctel'];
        $list = [];
        $num  = 0;
        while (($data = fgetcsv($handle)) !== false) {
            $arr = [];
            foreach ($val as $key => $value) {
                $arr[$value] = trim(iconv('gbk', 'utf-8', $data[$key]));
            }
            $list[] = $arr;
            $num++;
            if ($num > 100) {
                break;
            }
        }
        unset($list[0]);
        $list = array_values($list);

        return $list;
    }

    /**
     * 拒绝手机上添加分销商
     * @author: linchen
     * @date: 2019/10/24
     *
     * @param $id int id
     * @param $mobile string 电话
     *
     * @return array
     */
    public function refuseVerifyByPhone()
    {
        $id     = I('post.id', '');
        $mobile = I('post.mobile', '');
        if (!$id) {
            $this->apiReturn(204, [], '参数有误');
        }
        $id                = OrderNotify::url_sms_decode($id)[0];
        $mobile            = OrderNotify::url_sms_decode($mobile)[0];
        $memberRelationBiz = new MemberRelationBiz();
        $result            = $memberRelationBiz->refuseAddRelationByPhone($id, $mobile);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    private function _checkAddMemberRelationCount($sid)
    {
        $redisCache = Cache::getInstance('redis');
        $redisKey   = 'add_member_relation_count:' . $sid;
        $addCount   = $redisCache->get($redisKey);
        if ($addCount >= 3) {
            return true;
        } else {
            $redisCache->incrBy($redisKey, 1, 1800);

            return false;
        }
    }

    /**
     * 图形验证码的验证
     * <AUTHOR>  copy dewei
     * @date   2020-01-19
     *
     * @return
     */
    private function _authImage()
    {
        $sid             = $this->isLogin('ajax');
        $clientImageCode = I('post.auth_code');
        $cache           = \Library\Cache\Cache::getInstance('redis');
        $cacheKey        = 'auth_code_relation' . $sid;
        $serverImageCode = $cache->get($cacheKey);

        if (!$serverImageCode || !$clientImageCode) {
            return false;
        }

        if (strtolower($serverImageCode) == strtolower($clientImageCode)) {
            $cache->rm($cacheKey);

            return true;
        } else {
            return false;
        }
    }

    /**
     * 生成验证码方法
     * <AUTHOR>  copy dewei
     * @date   2020-01-19
     *
     * @return
     */
    public function getCode()
    {
        $sid = $this->isLogin('ajax');
        //判断来源地址
        $referer = I('server.HTTP_REFERER');
        $host    = I('server.HTTP_HOST');
        if (strrpos($referer, $host) === false) {
            //exit('非法请求 d');
        }

        $this->_setHeader();

        $loginBiz  = new \Business\Member\Login();
        $graphInfo = $loginBiz->getGraphVcode();
        $im        = $graphInfo[0];
        $str       = $graphInfo[1];

        $cache    = \Library\Cache\Cache::getInstance('redis');
        $cacheKey = 'auth_code_relation' . $sid;
        $cache->set($cacheKey, $str);

        imagepng($im);//建立一张PNG格式图形
        imagedestroy($im);//将图片handle解构，释于内存空间
    }

    /**
     * 设置头部
     * <AUTHOR>  copy dewei
     * @date   2020-01-19
     *
     * @return
     */
    private function _setHeader()
    {
        header("Expires:Mon,26 Jul 1997 05:00:00 GMT");
        //always modified
        header("Last-Modified:" . gmdate("D,d M Y H:i:s") . "GMT");
        //HTTP/1.1
        header("Cache-Control:no-store,no-cache,must-revalidate");
        header("Cache-Control:post-check=0,pre-check=0", false);
        //HTTP/1.0
        header("Pragma:no-cache");
        header("Content-Type:image/png");
    }

    /**
     * 判断分销商是否有多还款金额/已用额度
     * <AUTHOR>
     * @date 2020/2/4
     *
     * @return array
     */
    public function judgeIsDelByRelation()
    {
        $id    = I('post.id', '');
        $dtype = I('post.dtype', -1);//0：删除分销商 1：删除供应商

        if (!$id) {
            $this->apiReturn(204, [], '参数有误');
        }

        if (!in_array($dtype, [0, 1])) {
            $this->apiReturn(204, [], '参数有误!');
        }

        $memberInfo = $this->getLoginInfo();

        if ($dtype == 0) {
            //删除分销商
            $fid = $id;
            $sid = $memberInfo['sid'];
        } else {
            //删除供应商
            $fid = $memberInfo['sid'];
            $sid = $id;
        }

        $memberRelationBus = new \Business\Member\MemberRelation();
        $result            = $memberRelationBus->getCreditBalanceMoney($fid, $sid);

        if ($result['code'] != 200) {
            $this->apiReturn(204, [], $result['msg']);
        }
        //以下注释的逻辑转移到了上面的Business内
        /*$isUnused = $isUsed = 0;

        $unusedMoney = $usedMoney = 0;

        //信用账户余额为大于0的时候是多还款金额
        if ($result['data']['balanceMoney'] > 0) {
            $isUnused    = 1;
            $unusedMoney = intval($result['data']['balanceMoney']) / 100;
        }

        //信用账户余额为小于0的时候是已用额度
        if ($result['data']['balanceMoney'] < 0) {
            $isUsed    = 1;
            $usedMoney = abs(intval($result['data']['balanceMoney'])) / 100;
        }

        $res = [
            'is_unused'    => $isUnused,//分销商是否有多还款金额 0:否 1:是
            'is_used'      => $isUsed,//分销商是否有已用额度 0:否 1:是
            'unused_money' => $unusedMoney,//多还款金额
            'used_money'   => $usedMoney,//已用额度
        ];*/

        $this->apiReturn(200, $result['data'], 'success');

    }

    /**
     * 获取推荐分销商列表
     * @author: xwh
     * @date: 2020/6/8
     *
     * @return array
     */
    public function getPriorityDistributor()
    {
        $key           = I('keyword', '', 'strval');
        $page          = I('page', 0, 'int');
        $pageSize      = I('pagesize', 0, 'int');


        $memberRelationBus = new \Business\Member\MemberRelation();
        $result            = $memberRelationBus->getRecommendDistributor($key, $page, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 添加分销商到推荐列表
     * @author: xwh
     * @date: 2020/6/8
     *
     * @return array
     */
    public function addPriorityDistributor()
    {
        $isSuper = $this->isSuper();
        if (!$isSuper) {
            $this->apiReturn(401, [], "非法访问");
        }
        $img_url    = I('post.img_url', '', 'strval,trim');
        $account_id = I('post.account_id', 0, 'int');
        if (empty($img_url) || empty($account_id)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $priorityModel = new PriorityReseller();
        $exitst        = $priorityModel->existPriorityDistributor($account_id);
        if ($exitst) {
            $this->apiReturn(400, [], '添加失败,已存在');
        }
        $memberInfo = $this->getLoginInfo()['memberID'];

        $res = $priorityModel->addPriorityDistributor($account_id, $memberInfo, $img_url);
        if (!$res) {
            $this->apiReturn(400, [], '添加失败');
        }
        $this->apiReturn(200, [], '添加成功');
    }

    /**
     * 上移下移分销商在推荐列表得排序
     * @author: xwh
     * @date: 2020/6/8
     *
     * @return array
     */
    public function editPriorityDistributor()
    {
        $isSuper = $this->isSuper();
        if (!$isSuper) {
            $this->apiReturn(401, [], "非法访问");
        }
        $mainUserId   = I('post.main_user_id', '', 'strval');
        $deputyUserId = I('post.deputy_user_id', '', 'strval');

        if (empty($mainUserId) || empty($deputyUserId)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $memberInfo            = $this->getLoginInfo()['memberID'];
        $PriorityResellerModel = new PriorityReseller();
        $re                    = $PriorityResellerModel->getPriorityDistributorByuserId($mainUserId, 'sort');
        $res                   = $PriorityResellerModel->getPriorityDistributorByuserId($deputyUserId, 'sort');

        if (empty($re) || empty($res)) {
            $this->apiReturn(204, [], '参数错误!');
        }
        $mainUserSort   = $re[0]['sort'];
        $deputyUserSort = $res[0]['sort'];
        $PriorityResellerModel->startTrans();
        try {
            //交换顺序
            $res = $PriorityResellerModel->updatePriorityDistributor($mainUserId, 'sort', $deputyUserSort, $memberInfo);
            if ($res == false) {
                throw new \Exception('移动失败!');
            }
            $res = $PriorityResellerModel->updatePriorityDistributor($deputyUserId, 'sort', $mainUserSort, $memberInfo);
            if ($res == false) {
                throw new \Exception('移动失败。');
            }

            $PriorityResellerModel->commit();

            $this->apiReturn(200, [], '设置成功');
        } catch (\Exception $e) {
            $PriorityResellerModel->rollback();
            $this->apiReturn(400, [], $e->getMessage());
        }
    }

    /**
     * 将分销商在推荐列表删除
     * @author: xwh
     * @date: 2020/6/8
     *
     * @return array
     */
    public function delPriorityDistributor()
    {
        $isSuper = $this->isSuper();
        if (!$isSuper) {
            $this->apiReturn(401, [], "非法访问");
        }
        $userId = I('post.user_id', '', 'strval');
        if (empty($userId)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $memberInfo            = $this->getLoginInfo()['memberID'];
        $PriorityResellerModel = new PriorityReseller();
        $res                   = $PriorityResellerModel->updatePriorityDistributor($userId, 'is_deleted', 1,
            $memberInfo);
        if (!$res) {
            $this->apiReturn(400, [], '删除失败!');
        }
        $this->apiReturn(200, [], '删除成功!');
    }

    /**
     * 根据账号或者名称模糊搜索分销商
     * @author: xwh
     * @date: 2020/6/8
     *
     * @return array
     */
    public function getRelationByaccountOrdname()
    {
        $keyWord = I('post.key_word', '', 'strval,trim');
        //1名称 2账号
        $type = I('post.type', 1, 'intval');
        if (empty($keyWord)) {
            $this->apiReturn(200, [], 'success');
        }
        $memberInfos = [];
        if ($type == 1) {
            if (mb_strlen($keyWord) == strlen($keyWord)) {
                //英文
                $searchType = 2;
            } else {
                //汉字
                $searchType = 1;
            }
            $memberModel = new \Model\Member\Member('slave');
            $memberInfos = $memberModel->getUserInfoByKeyWord($keyWord, $searchType,
                'id,account,dname,dtype,account_id');
        } else {
            $MemberBus     = new \Business\Member\Member();
            $res = $MemberBus->getInfoByAccount($keyWord);
            if ($res) {
                $memberInfos[] = $res;
            }
        }


        $this->apiReturn(200, $memberInfos, "success");
    }

    /**
     * 获取员工列表
     * <AUTHOR>
     * @date   2020-04-13
     */
    public function getStaffList()
    {
        $sid = $this->isLogin('ajax');

        $api = new \Business\JavaApi\Member\MemberRelationQuery();
        $res = $api->queryMemberRelationSonList($sid);

        $data = [];
        if ($res['code'] == 200) {
            $idArr     = array_column($res['data'], 'son_id');
            $idArr     = array_unique($idArr);
            $idArr[]   = $sid;
            $memberApi = new \Business\JavaApi\Member\MemberQuery();
            $memberRes = $memberApi->queryMemberByMemberQueryInfo(['idList' => $idArr]);
            if ($memberRes['code'] == 200) {
                $nameMap = array_key(array_column($memberRes['data'], 'memberInfo'), 'id');
            } else {
                $nameMap = [];
            }
            foreach ($res['data'] as $item) {
                $data[] = [
                    'id'   => $item['son_id'],
                    'name' => $nameMap[$item['son_id']]['dname'],
                ];
            }
            //加上自己
            $data[] = ['id' => $sid, 'name' => $nameMap[$sid]['dname']];
        }

        $this->apiReturn(200, $data);
    }

    /**
     * 添加分销商限制
     * <AUTHOR>
     * @date   2020-07-13
     */
    public function addDistributorLimit()
    {
        $loginInfo = $this->getLoginInfo();
        $fid       = $loginInfo['sid'];
        if (!$fid) {
            $this->apiReturn(500, [], '获取用户信息失败, 请重新登录');
        }

        $res = (new \Business\Member\DistributorAudit())->addDistributorLimit($fid);
        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 添加分销商分组数量限制
     * <AUTHOR>
     *
     * @param  integer  $sid  商家id
     * @param  integer  $addNum  添加数量
     *
     * @date   2020-09-10
     */
    private function _groupDistributorNumLimit($sid, $groupId = 0, $addNum = 1)
    {
        $limitNum            = \Business\Member\DistributorGroup::GROUP_LIMIT_MUM;
        $evoluteGroupBiz     = new \Business\Product\Get\EvoluteGroup();
        $evoluteGroupUserBiz = new \Business\Product\Update\EvoluteGroupUser();
        if ($groupId == 0) {
            //默认分组
            //获取默认分组
            $defaultResultArr = $evoluteGroupBiz->getDefaultGroupBySid($sid);
            $groupId          = $defaultResultArr['data']['id'];
        }

        $groupRes = $evoluteGroupUserBiz->queryGroupFidCountByGroupIds([$groupId]);
        if ($groupRes['code'] == 200 && isset($groupRes['data'][$groupId])) {
            $currentDidCount = $groupRes['data'][$groupId];
        } else {
            return [403, '无默认分组'];
        }

        //判断下默认分组的分销商数量是否超过限制， 超过则拒绝

        if ($currentDidCount >= $limitNum) {
            return [403, "分组中的分销商数到达上限{$limitNum}人，请更换分组后添加"];
        }

        if (($currentDidCount + $addNum) > $limitNum) {
            return [403, "分组中的分销商数为{$currentDidCount}人，分组人数上限{$limitNum}, 添加操作失败"];
        }

        return [200, ''];
    }

    /**
     * 获取ota对应是否添加成为分销商
     * <AUTHOR> Li
     * @date   2020-07-17
     */
    public function getOtaDisStatus()
    {
        $sid = $this->isLogin('ajax');

        $memberBiz = new \Business\Member\MemberRelation();
        $result    = $memberBiz->getOtaStatus($sid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], '接口异常！');
    }

    /**
     * 获取供应商对应的邀请码及是否审核信息
     * <AUTHOR>  Li
     * @date  2020-07-20
     */
    public function getInviteCode()
    {
        $sid = $this->isLogin('ajax');

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->getInviteCode($sid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], '接口异常！');
    }

    /**
     * 根据供应商id获取邀请码的状态,不做任何修改
     * <AUTHOR>  Li
     * @date  2020-07-20
     */
    public function getInviteCodeStateAndNotModify()
    {
        $sid = $this->isLogin('ajax');

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->getInviteCodeStateAndNotModify($sid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], '接口异常！');
    }

    /**
     * 根据供应商id设置邀请码审核状态
     * <AUTHOR>  Li
     * @date  2020-07-20
     */
    public function setWithCheck()
    {
        //是否需要审核  0不需要 1需要
        $withCheck = I('post.with_check', 1, 'intval');
        $withCheck = $withCheck === 1 ? true : false;

        $sid = $this->isLogin('ajax');

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->setWithCheck($sid, $withCheck);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], '接口异常！');
    }

    /**
     * 设置邀请码类型
     * author queyourong
     * date 2022/6/27
     */
    public function setInviteType()
    {
        //邀请码类型 1=简单, 2=详细
        $inviteType = I('invite_type', 1, 'intval');

        $sid = $this->isLogin('ajax');

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->setInviteType($sid, $inviteType);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], '接口异常！');
    }

    /**
     * 重新生成供应商账号对应的邀请码
     * <AUTHOR>  Li
     * @date  2020-07-20
     */
    public function regenerateInviteCode()
    {
        $sid = $this->isLogin('ajax');

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->regenerateInviteCode($sid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], '接口异常！');
    }

    /**
     * 创建账号添加分销商
     * <AUTHOR> Li
     * @date  2020-08-04
     */
    public function addAccountRegisterDistributor()
    {
        $dname      = I('post.dname', '', 'strval');
        $mobile     = I('post.mobile', '', 'strval');
        $groupId    = I('post.group_id', 0, 'intval');
        $verifyCode = I('post.verify_code', '', 'strval');  //短信验证码（注册时校验）
        $sid        = $this->isLogin('ajax');

        $verifyCode = trim($verifyCode);
        if (strlen($verifyCode) > 0 && strlen($verifyCode) < 5){
            $this->apiReturn(203, [], '验证码错误');
        }

        if (empty($dname)) {
            $this->apiReturn(203, [], '缺失必要参数');
        }

        $groupLimitRes = $this->_groupDistributorNumLimit($sid, $groupId);
        if ($groupLimitRes[0] != 200) {
            $this->apiReturn($groupLimitRes[0], [], $groupLimitRes[1]);
        }

        $limitKey = "platform:add_distributor:{$sid}";

        $cacheObj = Cache::getInstance('redis');
        $lockTime = 1;
        $lockInfo = $cacheObj->lock($limitKey, 1, $lockTime);
        if (!$lockInfo) {
            $this->apiReturn(500, [], '请求过于频繁');
        }

        //限制用户3次后就要输入验证码了
        $checkCount = $this->_checkAddMemberRelationCount($sid);
        if ($checkCount) {
            $clientImageCode = I('post.auth_code');
            if (!$clientImageCode) {
                $this->apiReturn(402, [], '请输入验证码');
            }
            $authImage = $this->_authImage();
            if (!$authImage) {
                $this->apiReturn(403, [], '验证码错误，请重新输入');
            }
        }

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->addAccountRegisterDistributor($sid, $dname, $mobile, $groupId, true, $verifyCode);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], '接口异常！');
    }

    /**
     * 创建账号添加分销商判断实名是否可以创建
     * <AUTHOR>
     * @date   2020-12-02
     *
     */
    public function judgeAllowComName()
    {
        $comName = I('post.com_name');
        if (!$comName) {
            $this->apiReturn(204, '', '参数错误');
        }

        $distributorBiz = new \Business\Member\Member();
        $result         = $distributorBiz->judgeAllowComName($comName);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常', false);
        }
    }

    /**
     * 获取邀请海报相关信息
     * <AUTHOR> Li
     * @date 2021-05-11
     *
     * @return array
     */
    public function posterInfo()
    {
        $type    = I('type', 1, 'intval');      //海报类型 1:供应商邀请添加海报
        $viascan = I('viascan', 0, 'intval');   //是否通过扫码获取的  0否 1是
        $sid     = I('sid', 0, 'intval');       //扫码进来传输的供应商id
        if (!$viascan) {
            $sid = $this->isLogin('ajax');
        }

        if (!$sid) {
            $this->apiReturn(203, [], '供应商信息缺失');
        }

        $posterBis = new \Business\Member\MemberPoster();
        $posterRes = $posterBis->getPosterInfo($sid, $type);

        if (!$posterRes || $posterRes['code'] != 200) {
            $this->apiReturn($posterRes['code'], $posterRes['data'], $posterRes['msg']);
        }

        $posterRes['data']['dname'] = (new \Business\Member\Member())->getInfo($sid)['dname'];

        $this->apiReturn(200, $posterRes['data'], '成功');
    }

    /**
     * 上传邀请海报图片
     * <AUTHOR> Li
     * @date 2021-05-11
     *
     * @return array
     */
    public function uploadPosterImage()
    {
        $type = I('type', 1, 'intval');
        if (!isset($_FILES['poster_image'])) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (empty($_FILES['poster_image']['tmp_name'])) {
            $this->apiReturn(203, [], '上传失败!');
        }

        ksort($_FILES);
        $qiniuProcess = new \Process\Resource\Qiniu();

        $posterBis = new \Business\Member\MemberPoster();
        $sizeMax   = $posterBis::IMAGE_SIZE;

        //海报图图片格式效验
        $posterImage = $_FILES['poster_image'];

        $posterImageType = explode('/', $posterImage['type']);
        if ($posterImageType[0] != 'image') {
            $this->apiReturn(203, [], '海报图上传格式有误');
        }
        if ($posterImage['size'] > $sizeMax) {
            $this->apiReturn(203, [], '海报图大小限制2M');
        }

        $sid = $this->isLogin('ajax');

        //更新海报版本号
        $inviteModel  = new \Model\Member\MemberPoster();
        $modInviteRes = $inviteModel->modPosterVerByUuid($sid);
        if (!$modInviteRes) {
            $this->apiReturn(204, [], '海报版本号更新失败');
        }

        //获取后缀
        $arr            = explode('.', $posterImage['name']);
        $ext            = array_pop($arr);
        $posterImgesRes = $posterBis->getPosterDir($sid, $type, $ext);
        $filename       = [
            $posterImgesRes['data']['posterDir'],
        ];
        $result         = $qiniuProcess->uploadFile($filename);

        if (!$result) {
            $this->apiReturn(204, [], $qiniuProcess->getError());
        }

        $posterImges = $posterBis->getPosterImage($sid, $type);
        $res         = [
            'poster_image' => $posterImges['data']['poster'],
        ];

        $this->apiReturn(200, $res, '上传成功');

    }

    /**
     * 保存邀请海报信息
     * <AUTHOR> Li
     * @date 2021-05-11
     *
     * @return array
     */
    public function setPosterData()
    {
        $xPosition  = I('post.x', '', 'strval');//二维码位置相关参数
        $yPosition  = I('post.y', '', 'strval');//二维码位置相关参数
        $wPosition  = I('post.w', '', 'strval');//二维码位置相关参数
        $hPosition  = I('post.h', '', 'strval');//二维码位置相关参数
        $textX      = I('post.text_x', '', 'strval');//文字位置相关参数
        $textY      = I('post.text_y', '', 'strval');//文字位置相关参数
        $fontSize   = I('post.font_size', '', 'strval');//文字大小
        $textWidth  = I('post.text_width', '', 'strval');//文字位置相关参数
        $textHeight = I('post.text_height', '', 'strval');//文字位置相关参数
        $fontColor  = I('post.font_color', '', 'strval');//文字颜色
        $text       = I('post.text', '', 'strval');//文字内容
        $bgImage    = I('post.bg_image', '', 'strval');//背景图片
        $isDefault  = I('post.is_default', 0, 'strval');//是否是默认海报 0:否  1:是
        $originDevicepixelratio = I('post.origin_devicepixelratio', '1', 'strval');//用户电脑缩放布局比例  正常为1=100%

        if (empty($xPosition) || empty($yPosition) || empty($wPosition) || empty($hPosition)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $sid         = $this->isLogin('ajax');
        $type        = I('type', 1, 'intval');
        $posterModel = new \Model\Member\MemberPoster();
        $res         = $posterModel->getMulitInviteConfigByUuid($sid, $type);
        if (!$res) {
            $this->apiReturn(204, [], '数据错误！');
        }

        $extraParam     = [
            'x'           => $xPosition,
            'y'           => $yPosition,
            'w'           => $wPosition,
            'h'           => $hPosition,
            'text_x'      => $textX,
            'text_y'      => $textY,
            'font_size'   => $fontSize,
            'text_width'  => $textWidth,
            'text_height' => $textHeight,
            'font_color'  => $fontColor,
            'text'        => $text,
            'origin_devicepixelratio' => $originDevicepixelratio,
        ];
        $extraParamJson = json_encode($extraParam, JSON_UNESCAPED_UNICODE);

        if ($extraParamJson == $res['extra'] && $bgImage == $res['poster_bg']) {
            $this->apiReturn(200, [], '成功');
        }

        $modParam     = ['extra' => $extraParamJson, 'poster_bg' => $bgImage, 'is_default' => $isDefault];
        $modInviteRes = $posterModel->modInviteConfig($sid, $type, $modParam);

        if (!$modInviteRes) {
            $this->apiReturn(204, [], '修改失败！');
        }

        $this->apiReturn(200, [], '成功');
    }

    /**
     * 恢复默认海报
     * <AUTHOR> Li
     * @date 2021-05-11
     *
     * @return array
     */
    public function setDefaultPosterBg()
    {
        $sid         = $this->isLogin('ajax');
        $type        = I('type', 1, 'intval');
        $posterModel = new \Model\Member\MemberPoster();
        $res         = $posterModel->getMulitInviteConfigByUuid($sid, $type);
        if (!$res) {
            $this->apiReturn(204, [], '数据错误！');
        }

        $bgImage = 'https://static.12301.cc/assets/build/images/wx_b/invite-poster-bg.png';

        $modParam     = ['poster_bg' => $bgImage, 'is_default' => 1];
        $modInviteRes = $posterModel->modInviteConfig($sid, $type, $modParam);

        if (!$modInviteRes) {
            $this->apiReturn(204, [], '修改失败！');
        }

        $this->apiReturn(200, [], '成功');
    }

    /**
     * 根据查询条件筛选分销商（搜索下拉框使用）
     * <AUTHOR>
     * @date 2021/9/26
     *
     * @return array
     */
    public function queryCooperationDistributorList()
    {
        $type       = I('type', 0, 'intval');//1=账号 2=名称
        $keyword    = I('keyword', '', 'strval');

        $sid        = $this->isLogin('ajax');

        if (empty($keyword) || empty($type)) {
            $this->apiReturn(201, [], '参数缺失');
        }

        switch ($type) {
            case 1:
                //$relationShipModel = new MemberRelationship($sid);
                //$searchResult      = $relationShipModel->partnerRelationSearch($sid, 0, '', $keyword);

                $memberQueryApi = new MemberQuery();
                $searchResult   = $memberQueryApi->queryMemberIdBySearch('', '', $keyword);

                if ($searchResult['code'] != 200 || empty($searchResult['data'])) {
                    $this->apiReturn(200, [], '');
                }

                break;
            case 2:
                //$relationShipModel = new MemberRelationship($sid);
                //$searchResult      = $relationShipModel->partnerRelationSearch($sid, 0, $keyword);

                $memberQueryApi = new MemberQuery();
                $searchResult   = $memberQueryApi->queryMemberIdBySearch('', $keyword);

                if ($searchResult['code'] != 200 || empty($searchResult['data'])) {
                    $this->apiReturn(200, [], '');
                }
                break;

            default:
                $this->apiReturn(201, [], '参数错误');
                break;
        }

        $userIdArr = array_column($searchResult['data'], 'id');

        $memberBuz = new \Business\Member\Member();
        $userInfo  = $memberBuz->getList($userIdArr);
        $result = [];
        foreach ($searchResult['data'] as $value) {
            $result[] = [
                'fid'     => $value['id'],
                'account' => $userInfo[$value['id']]['account'] ?? '',
                'dname'   => $userInfo[$value['id']]['dname'] ?? '',
                'mobile'  => $userInfo[$value['id']]['mobile'] ?? '',
            ];
        }

        $this->apiReturn(200, $result, '');

    }

    /**
     * 查询分销商列表
     * <AUTHOR>
     * @date   2024/04/26
     *
     * @return true|null
     */
    public function getDistributorList()
    {
        $searchType = I('post.type', 0, 'intval');   //搜索类型 1账号名称 2账号 3联系人 4手机号
        $keyWord    = I('post.keyword', '', 'strval');
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');
        $sid        = $this->isLogin('ajax');

        if (!in_array($searchType, [1, 2, 3, 4])) {
            return $this->apiReturn(203, [], '缺少必要参数');
        }

        if (!$keyWord) {
            return $this->apiReturn(200, [], '没有搜索');
        }

        $result = (new \Business\Member\Member())->memberQuery($sid, $keyWord, $searchType, $page, $size);

        //由于搜索为空，也返回非200，所以这里需要判断，非200直接返回空，未搜索到数据
        if ($result['code'] != 200) {
            return $this->apiReturn(200, [], '未搜索到数据');
        }

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


}