<?php
/**
 * 平台广告配置相关控制器
 * User: liuzuliang
 * Date: 2018-09-12
 */

namespace Controller\adCO;

use Library\Controller;

class PlatAdvert extends Controller
{
    /**
     * 添加编辑广告内容
     * @date   2018-07-10
     * <AUTHOR>
     */
    public function saveAdvert()
    {
        $this->isLogin('ajax');
        //目前只有管理员有权限
        if (!$this->isSuper()) {
            $this->apiReturn(401, [], '没有权限');
        }

        $PlatAdvert = new \Business\Advert\PlatAdvert();
        $res        = $PlatAdvert->saveAdvert();
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取广告内容配置信息
     * @date   2018-07-10
     * <AUTHOR>
     */
    public function getAdvertConfig()
    {
        $this->isLogin('ajax');
        //目前只有管理员有权限
        if (!$this->isSuper()) {
            $this->apiReturn(401, [], '没有权限');
        }

        $position = I('post.position');     //展示位置
        if ($position === '') {
            $this->apiReturn(400, [], '展示位置参数错误');
        }

        $AdvertModel = new \Model\Advert\PlatAdvert();
        $field       = 'id,status,begin,end,position,content';
        $res         = $AdvertModel->getAdvertInfoByPosition($position, 'position', $field);
        if ($res) {
            //处理一下时间 格式
            $res['begin'] = ($res['begin'] != 0) ? date("Y-m-d", $res['begin']) : 0;
            $res['end'] = ($res['end'] != 0) ? date("Y-m-d", $res['end']) : 0;
            $res['content'] = htmlspecialchars_decode($res['content']);
            $this->apiReturn(200, $res, '查询成功');
        } else {
            $this->apiReturn(200, [], '暂无配置信息');
        }
    }

    /**
     * 获取广告内容
     * @date   2018-07-10
     * <AUTHOR>
     */
    public function getAdvert()
    {
        $position = I('post.position');     //展示位置
        $domain   = I('server.HTTP_HOST'); //域名
        if ($position === '') {
            $this->apiReturn(400, [], '展示位置参数错误');
        }

        //获取平台右上角文字 非管理员的情况 需要判断当前用户合作模式是哪种  假日是订单模式且预存订单  需要判断预存账本中凭证费条数是否小于100  小于100 展示预存提醒广告位 （平台右上角展示）
        if ($position == 1) {
            $loginInfo = $this->getLoginInfo();
            if ($loginInfo['sdtype'] == 0 && $loginInfo['sid'] != 1) {
                $exclusiveBiz = new \Business\Finance\ExclusiveAccount();
                $accountInfo  = $exclusiveBiz->checkMemberInfo($loginInfo['sid']);
                if ($accountInfo && isset($accountInfo[$loginInfo['sid']]) && $accountInfo[$loginInfo['id']]['contract_model'] == 3 && $accountInfo[$loginInfo['id']]['cooperate_type'] == 2 && $accountInfo[$loginInfo['sid']]['voucher_remain'] < 100) {
                    $advetInfo = json_encode([
                        'text' => '您的专项电子凭证费已小于100条，为避免影响您的正常使用，请提前续费或是联系客服（电话：400-99-22301转1）  续费',
                        'href' => '/new/prestorage.html',
                    ]);

                    $advetInfo = htmlspecialchars_decode($advetInfo);
                    $this->apiReturn(200, ['content' => $advetInfo], '');
                }
            }
        }

        $AdvertBusiness = new \Business\Advert\PlatAdvert();
        $check          = $AdvertBusiness->filterShow($position, $domain);
        if ($check == false) {
            $this->apiReturn(200, '', '');
        }
        $dtype = -1;
        $sid   = 0;
        if (in_array($position, [0, 2, 7, 8])) {
            $loginInfo = $this->getLoginInfo();
            $dtype     = (int)$loginInfo['dtype'] ?? -1;
            $sid       = $loginInfo['sid'] ?? 0;
        }
        $res = $AdvertBusiness->getPositionAdvert($position, $sid, $dtype);

        if ($res) {
            $res['data']['content'] = htmlspecialchars_decode($res['data']['content'] ?? '');
            $this->apiReturn(200, ['content' => $res['data']['content']], '');
        } else {
            $this->apiReturn(200, '', '');
        }
    }

    /**
     * 获取运营简报设置
     * @date   2020-11-04
     * <AUTHOR>
     */
    public function getBulletinConfig()
    {
        $userId = I('post.user_id');     //用户id

        if (!$userId) {
            $this->apiReturn(400, [], '参数错误');
        }

        $AdvertModel = new \Model\Advert\BulletinConfig();

        $res = $AdvertModel->getBulletinConfig($userId);

        if ($res) {
            $res['content'] = json_decode(htmlspecialchars_decode($res['content']), true);
            $this->apiReturn(200, $res['content'], '');
        } else {
            $this->apiReturn(200, [], '');
        }
    }

    /**
     * 广告url跳转
     * <AUTHOR>
     * @date 2022/3/17
     *
     */
    public function toTarget()
    {
        $key = I('get.key');
        if (empty($key) || !is_string($key)) {
            $this->apiReturn(400, [], '异常请求');
        }

        $advertBusiness = new \Business\Advert\PlatAdvert();

        $res = $advertBusiness->handleTarget($key);
        $url = $res['data']['url'] ?? '';

        //验证是否是链接
        if (!checkUrl($url)) {
            $this->apiReturn(400, [], '跳转失败');
        }

        header("Location: $url");
    }

    /**
     * 广告关闭
     * <AUTHOR>
     * @date   2022/5/18
     *
     */
    public function closeShow()
    {
        $position = I('post.position');//广告位

        $loginInfo = $this->getLoginInfo();
        $dtype     = (int)$loginInfo['dtype'] ?? -1;
        $sid       = $loginInfo['sid'] ?? 0;

        $advertBusiness = new \Business\Advert\PlatAdvert();

        $res = $advertBusiness->closeShow($sid, $dtype, $position);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}