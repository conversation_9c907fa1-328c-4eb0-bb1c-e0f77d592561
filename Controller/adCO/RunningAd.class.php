<?php
/**
 * 运营广告
 * User: <PERSON><PERSON>
 * Date: 2018/8/20
 * Time: 16:17
 */

namespace Controller\adCO;


use Library\Controller;
use Business\Advert\RunningAd as AdBiz;

class runningAd extends Controller
{
    private $_adBiz;
    private $_mid;

    public function __construct()
    {
        parent::__construct();
        $this->_adBiz = new AdBiz();
    }

    /***
     * 保存广告配置
     */
    public function saveRunAd() {
        $picJson = I('post.picUrls');

        if (!$picJson || !is_string($picJson)) {
            $this->apiReturn(203, [], '缺少图片广告地址');
        }

        $res = $this->_adBiz->saveAd($picJson);
        if ($res === false) {
            $this->apiReturn(201, [], '保存失败');
        }

        $cacheRedis = \Library\Cache\Cache::getInstance('redis');
        $key        = "running_advert_{$this->_mid}";
        if ($cacheRedis->get($key)) {
            $cacheRedis->rm($key);
        }
        $cacheRedis->set($key, '', $picJson, 60 * 60 * 24 * 30);

        $this->apiReturn(200, [], 'success');
    }

    /***
     * 获取广告图片地址json
     */
    public function getAd() {
        $res = $this->_adBiz->getAd();

        $data = array();
        if ($res) {
            $data = [
                'picUrls' => $res
            ];
        }
        $this->apiReturn(200, $data, 'success');
    }
}