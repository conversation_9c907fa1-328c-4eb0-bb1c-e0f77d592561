<?php
/**
 * 广告管理系统相关控制器
 * Created by PhpStorm.
 * User: lanlc
 * Date: 16-10-19
 * Time: 上午11:20
 */

namespace Controller\adCO;

use Library\Controller;
use Model\Advert;
use Library\Tools\Helpers;
use Model\Product\Land;
use Model\Product\Ticket;
use OSS\Core\OssException;
use OSS\OssClient;

class AdCO extends Controller
{
    private $memberId;
    private $model;

    public function __construct()
    {
        $this->model = new Advert\AdCO();
    }

    /**
     * 添加广告消息
     *
     * @param  int $id 编辑时为广告ID
     * @param  int $pageType 页面类型ID
     * @param  int $pageId 页面名称ID
     * @param  string $content 广告文字内容
     * @param  int $position 广告位置
     * @param  string $adUrl 广告链接
     * @param  int $pId 产品ID
     * @param  string $picUrl 图片链接地址
     * @param  int $landId 景区id（设置微信消息的时候传）
     *
     * return array
     */
    public function saveAdCO()
    {
        $this->memberId = $this->isLogin();

        $id       = intval(I('post.id'));
        $pageType = intval(I('post.pageType'));
        $pageId   = intval(I('post.pageId'));
        $position = intval(I('post.position'));
        $pId      = intval(I('post.pId'));
        $landId   = intval(I('post.land_id'));
        $landName = I('post.land_name', '', 'strval');
        $status   = intval(I('post.status'));

        $adUrl   = I('post.adUrl');
        $content = I('post.content');
        $picUrl  = I('post.picUrl');

        $content = safe_str($content);
        $adUrl   = safe_str($adUrl);
        $picUrl  = safe_str($picUrl);

        if (!checkUrl($adUrl)) {
            $this->apiReturn('203', [], '请输入正确的广告地址');
        }

        if ($content == '' && $picUrl == '') {
            $this->apiReturn('203', [], '请填写广告或者图片');
        }
        if ($pageType == '' || $pageId == '' || ($position != 0 && $position == '')) {
            $this->apiReturn('203', [], '请填写必填参数');
        }
        //固定尾部
        if ($pId == -1) {
            $position = 1;
        }

        $res = $this->model->saveAdInfo($id, $pageType, $pageId, $position, $pId, $adUrl, $content, $picUrl,
            $this->memberId, $status, $landId, $landName);
        if ($res == 204) {
            $this->apiReturn(204, [], '该产品已经创建过广告链接，无法再次创建');
        } else if ($res == 400) {
            $this->apiReturn(202, [], '保存失败');
        } else if ($res == 205) {
            $this->apiReturn(205, [], '该位置的广告已存在');
        }
        $this->apiReturn(200, [], '保存成功');
    }

    /**
     * 获取广告列表
     */
    public function managerAd()
    {
        $this->memberId = $this->isLogin();
        $pageNum        = I('post.pageNum', 1, 'intval');
        $pageSize       = I('post.pageSize', 10, 'intval');
        $keyWord        = I('post.keyWord', '', 'strval');

        $res = $this->model->managerAd($this->memberId, $keyWord, $pageNum, $pageSize);

        if ($res) {
            $lidArr = [];
            foreach ($res['data'] as $val) {
                $lidArr[] = $val['product_id'];
            }
            //$modelLand = new \Model\Product\Land();
            //$land      = $modelLand->getLandInfoByMuli($lidArr, 'title,id');

            $javaAPi = new \Business\CommodityCenter\Land();
            $land    = $javaAPi->queryLandMultiQueryById($lidArr);

            foreach ($res['data'] as $key => $val) {
                $res['data'][$key]['title'] = '-';
                foreach ($land as $keyLand => $valLand) {
                    if ($val['product_id'] == $valLand['id']) {
                        $res['data'][$key]['title'] = $valLand['title'];
                    }
                }
                $res['data'][$key]['create_time'] = empty($res['data'][$key]['create_time']) ? '' : date('Y-m-d, h:i:s',
                    $res['data'][$key]['create_time']);
            }
            $data['list']     = $res['data'];
            $data['total']    = $res['count'];
            $data['page_arr'] = load_config('page_type');
            $this->apiReturn(200, $data, '获取成功');
        } else {
            $this->apiReturn(202, [], '没有相关数据');
        }
    }

    /**
     * 获取单个编辑广告数据
     *
     * @param  int $id 表ID
     * @param  int $this ->memberId 用户ID
     *
     * return array
     */
    public function getInfo()
    {
        $this->memberId = $this->isLogin();
        $id             = I('post.id');
        if ($id == '') {
            $this->apiReturn(202, [], '缺少参数');
        }

        $res = $this->model->getInfo($id, $this->memberId);
        if ($res) {
            $this->apiReturn(200, $res, '获取成功');
        } else {
            $this->apiReturn(203, [], '没有相关数据');
        }
    }

    /**
     * 获取供应商的正常景区数据
     * @editor zhujb
     * @date 2020-03-04
     *
     * return array
     */
    public function getLand()
    {
        $page  = I('post.page', 1, 'intval');
        $size  = I('post.size', 10, 'intval');
        $title = I('post.title', '', 'strval');

        $this->memberId = $this->isLogin();

        $productBiz = new \Business\Product\Product();

        $evoluteArr = $productBiz->getSaleProductByCondition($this->memberId, $this->memberId, 0, 0, $title, [], 0, [],
            0, $page, $size);

        if (empty($evoluteArr)) {
            $this->apiReturn(203, [], '没有相关数据');
        }
        $tidArr = array_column($evoluteArr['list'], 'ticket_id');

        $ticketModel = new Ticket('slave');
        $ticketArr   = $ticketModel->getTicketInfoByIdArr($tidArr, 'id,landid,pid,title');
        if (empty($ticketArr)) {
            $this->apiReturn(203, [], '没有相关数据');
        }

        $lidArr = array_column($ticketArr, 'landid');
        //$landModel = new Land('slave');
        //$landArr   = $landModel->getLandInfoByLids($lidArr, 'id,title');

        $javaAPi = new \Business\CommodityCenter\Land();
        $landArr = $javaAPi->queryLandMultiQueryById($lidArr);

        $landData = [];
        foreach ($landArr as $land) {
            $landData[$land['id']] = $land;
        }

        $list = [];
        foreach ($ticketArr as $item) {
            //$ttitle = $item['title'];
            $ltitle = isset($landData[$item['landid']]['title']) ? $landData[$item['landid']]['title'] : '';
            $list[] = [
                'tid'    => $item['id'],
                'title'  => $ltitle,
                'landid' => $item['landid'],
                'pid'    => $item['pid'],
            ];
        }

        $return = [
            'list'  => $list,
            'total' => $evoluteArr['total'],
        ];

        if ($list) {
            $this->apiReturn(200, $return, '获取成功');
        } else {
            $this->apiReturn(203, [], '没有相关数据');
        }
    }

    /**
     *
     * 获取页面类型
     *
     * return array
     */
    public function getPageType()
    {
        $this->memberId = $this->isLogin('ajax');
        $loginInfo      = $this->getLoginInfo('ajax', false, false);

        $res = load_config('page_type');
        // 订单短信链接只允许平台管理员配置
        if ($loginInfo['sdtype'] != 9) {
            unset($res[5]);
        }

        if ($res) {
            $this->apiReturn(200, $res, '获取成功');
        } else {
            $this->apiReturn(203, [], '没有相关数据');
        }
    }

    /**
     *
     * 获取页面类型对应的页面名称
     *
     * return array
     */
    public function getPageInfo()
    {
        $id = intval(I('post.typeId'));
        if ($id == '') {
            $this->apiReturn(201, [], '缺少参数');
        }
        $res = $this->model->getPageInfo($id);
        if ($res) {
            $this->apiReturn(200, $res, '获取成功');
        } else {
            $this->apiReturn(203, [], '没有相关数据');
        }
    }

    /**
     * 更新广告状态
     *
     * @param  int $status 状态
     *
     * return array
     */
    public function updateStatus()
    {
        $this->memberId = $this->isLogin();
        $id             = intval(I('post.id'));
        $status         = intval(I('post.status'));
        if ($id == '' || !in_array($status, [0, 1])) {
            $this->apiReturn(201, [], '错误参数');
        }
        $res = $this->model->updateStatus($id, $this->memberId, $status);
        if ($res) {
            $this->apiReturn(200, $res, '更新成功');
        } else {
            $this->apiReturn(203, [], '没有更改');
        }
    }

    /**
     * 删除广告
     *
     * @param  int $status 状态
     *
     * return array
     */
    public function delAd()
    {
        $this->memberId = $this->isLogin();
        $id             = intval(I('post.id'));
        if ($id == '') {
            $this->apiReturn(203, [], '错误参数');
        }
        $res = $this->model->delAd($id, $this->memberId);
        if ($res) {
            $this->apiReturn(200, $res, '更新成功');
        } else {
            $this->apiReturn(203, [], '没有更新');
        }
    }

    /**
     *  获取广告接口
     *
     * @param  string $
     *
     */
    public function getAd()
    {
        $pageIdentify = safe_str(I('post.pageIdentify'));
        $aid          = intval(I('post.aid'));

        $pageIdentify = getUrl($pageIdentify);
        if ($pageIdentify == '' || $aid == '') {
            $this->apiReturn(203, [], '错误参数');
        }
        $res = $this->model->getAd($pageIdentify, $aid);
        if (!$res) {
            $this->apiReturn(203, [], '没有相关数据');
        }
        foreach ($res as $key => $item) {
            $res[$key]['tempUrl']     = 'http://wx.12301.cc/api/index.php?c=StatisticsPvUv&a=tempUrl&';
            $query                    = [
                'url'          => $res[$key]['ad_url'],
                'pageIdentify' => $pageIdentify,
                'pageName'     => $res[$key]['page_name'],
                'type'         => $res[$key]['page_type'],
                'aid'          => $aid,
            ];
            $res[$key]['redirectUrl'] = $res[$key]['tempUrl'] . http_build_query($query);
        }
        $this->apiReturn(200, $res, '获取数据成功');
    }

    /**
     * 图片上传
     * @return [type] [description]
     */
    public function uploadImgV0()
    {
        $this->memberId = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        Helpers::loadPrevClass('Uploader', true);

        $callback_id = I('callback_id', 0, 'intval');

        $config   = array(
            "savePath"     => IMAGE_UPLOAD_DIR . "{$loginInfo['account']}/ad",
            "maxSize"      => 2048, //单位KB
            "allowFiles"   => array(".gif", ".png", ".jpg", ".jpeg", ".bmp"),
            'simpleFolder' => true,
        );
        $file     = key($_FILES);
        $Upload   = new \Uploader($file, $config);
        $img_info = $Upload->getFileInfo();
        if ($img_info['state'] == 'SUCCESS') {
            $img_url = IMAGE_URL . "ad/{$loginInfo['account']}/" . $img_info['name'];
            $r       = ['code' => 200, 'data' => ['src' => $img_url]];
        } else {
            $r = ['code' => 204, 'data' => [], 'msg' => '上传失败'];
        }

        $r = json_encode($r);

        $script = '<script type="text/javascript">
                var FileuploadCallbacks=window.parent.FileuploadCallbacks[' . $callback_id . '];
                for(var i in FileuploadCallbacks) FileuploadCallbacks[i](' . $r . ');
                </script>';
        echo $script;
    }

    public function uploadImg()
    {
        \Library\Tools\Helpers::composerAutoload();
        $this->memberId = $this->isLogin();
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        $callback_id    = I('callback_id', 0, 'intval');

        $baseConfig = C('oss');
        $config     = $baseConfig['aliyun']['senic'];
        $endpoint   = ENV == 'PRODUCTION' ? $config['endpoint']['prod'] : $config['endpoint']['dev'];

        $file = $_FILES[key($_FILES)];
        //上传本地图片
        $srcPath = "/tmp/{$file['name']}";
        move_uploaded_file($file['tmp_name'], $srcPath);

        $time    = time();
        $dstName = "{$time}_" . $file['name'];
        $dst     = "ad/{$loginInfo['account']}/{$dstName}";
        try {
            $ossClient = new OssClient($config['accessId'], $config['accessKeySecret'], $endpoint, false);
            $res       = $ossClient->uploadFile($config['bucket'], $dst, $srcPath);
            $url       = $res['info']['url'];
            //internal为了走内网更快，地址返回需要去除internal
            $string      = $url;
            $pattern     = '/oss-cn-hangzhou-internal/i';
            $replacement = 'oss-cn-hangzhou';
            $newUrl      = preg_replace($pattern, $replacement, $string);
            if (ENV == 'PRODUCTION') {
                $newUrl = str_replace('http', 'https', $newUrl);
            }
            $newUrl .= "/image_zip";
            if ($res['info']['http_code'] == 200) {
                $r = ['code' => 200, 'data' => ['src' => $newUrl]];
            } else {
                $r = ['code' => 400, 'data' => [], 'msg' => '上传失败'];
            }
        } catch (OssException $exception) {
            $r = ['code' => 204, 'data' => [], 'msg' => '上传失败'];
        }
        unlink($srcPath);
        $r      = json_encode($r);
        $script = '<script type="text/javascript">
                var FileuploadCallbacks=window.parent.FileuploadCallbacks[' . $callback_id . '];
                for(var i in FileuploadCallbacks) FileuploadCallbacks[i](' . $r . ');
                </script>';
        echo $script;
    }

    /**
     *
     * 获取统计PV数据
     *
     * @param  string $bDate 开始时间
     * @param  string $eDate 结束时间
     *
     * return array
     */
    /*
    public function getPV()
    {
        $bDate = I('post.bDate');
        $eDate = I('post.eDate');
        $page  = intval(I('post.page'));
        $content = safe_str(I('post.content'));

        if ($bDate == '') {
            $bDate = strtotime(date('Y-m-d'));
        }
        if ($eDate == '') {
            $eDate = strtotime(date('Y-m-d'));
        }

        if ($page == '') {
            $page = 1;
        }
        $pageNum = 15;
        $offSet = ($page-1)*$pageNum;
         
        $res = $this->model->getPV($this->memberId, $bDate, $eDate, $content, $offSet, $pageNum);
        if ($res) {
            $list = $res['data'];
            $data['list'] = $list;
            $data['page'] = $page;
            $data['totalPage'] = $res['count'];
            $data['page_arr'] =load_config('page_type');
            $this->apiReturn(200, $data, '获取成功');
        } else {
            $this->apiReturn(202, [], '没有相关数据');
        }
    }
    */
}