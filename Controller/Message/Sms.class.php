<?php
/**
 *
 * <AUTHOR>
 * @date 2021/11/30
 */

namespace Controller\Message;

use Business\OrderSms\DistributorOrderSms;
use Business\OrderSms\MerchantOrderSms;
use Business\Order\Modify;
use Model\Order\OrderTools;
use Model\Order\SmsJournal;
use Library\Controller;
use Model\Order\SmsJournalHistory;

class Sms extends Controller
{
    private $merchantOrderSms;

    public function __construct()
    {
        parent::__construct();

        $this->merchantOrderSms = new MerchantOrderSms();
        $this->distributorOrderSms = new DistributorOrderSms();
    }
    public function getSearchConfig()
    {
        // 短信归档时间策略与订单保持一致
        $timeType = load_config('time_type', 'orderSearch');

        $data = [
            'time_type' => $timeType,
        ];

        $this->apiReturn(200, $data, '');
    }


    public function getOrderSmsRecord()
    {
        $ordernum = I('post.ordernum', '', 'strval');
        $pageNum = I('post.page_num', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');

        $loginInfo = $this->getLoginInfo();
        $sid = $loginInfo['sid'];

        $code = 200;
        $msg = 'success';
        $data = [];
        try {
            $data = $this->merchantOrderSms->getOrderSmsRecord($sid, $ordernum, $pageNum, $pageSize);
        } catch (\Throwable $e) {
            $code = 500;
            $msg = $e->getMessage();
        }
        $this->apiReturn($code, $data, $msg);
    }

    public function getRelationOrderSmsRecord()
    {
        $ordernum = I('post.ordernum', '', 'strval');
        $pageNum = I('post.page_num', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');

        $loginInfo = $this->getLoginInfo();
        $sid = $loginInfo['sid'];

        $code = 200;
        $msg = 'success';
        $data = [];
        try {
            $data = $this->distributorOrderSms->getRelationOrderSmsRecord($sid, $ordernum, $pageNum, $pageSize);
        } catch (\Throwable $e) {
            $code = $e->getCode() ?: 500;
            $msg = $e->getMessage();
        }
        $this->apiReturn($code, $data, $msg);
    }

    public function submitBatchRelationOrdersSmsRecord()
    {
        $ordernums = I('post.ordernums');

        $loginInfo = $this->getLoginInfo();
        $memberId = $loginInfo['memberID'];
        $sid = $loginInfo['sid'];
//        $memberId = 3385;
//        $sid = 3385;

        try {
            $res = $this->distributorOrderSms->submitAsyncTaskOrderSmsRecord($memberId, $sid, $ordernums);
            $code = $res['code'];
            $data = $res['data'];
            $msg  = $res['msg'];
        } catch (\Throwable $e) {
            $code = $e->getCode() ?: 500;
            $data = [];
            $msg = $e->getMessage();
        }
        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 订单短信发送记录
     * <AUTHOR>
     * @date 2021/11/30
     *
     * @return array
     */
    public function orderSmsSendRecord()
    {
        $ordernum  = I('post.ordernum', '', 'strval');
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];

        $smsJournalModel = new SmsJournal();
        $result          = $smsJournalModel->getSmsRecordList(1, 30, null, $ordernum);
        $this->apiReturn(200, $result, '');
    }

    /**
     * 用户短信发送记录
     * <AUTHOR>
     * @date 2021/11/30
     *
     * @return array
     */
    public function smsSendRecord()
    {
        $ordernum  = I('post.ordernum', '', 'strval');
        $startTime = I('post.startTime', 0, 'intval');
        $endTime   = I('post.endTime', 0, 'intval');
        $mobile    = I('post.mobile', '', 'strval');
        $page      = I('post.page', 1, 'intval');
        $size      = I('post.size', 10, 'intval');
        $status      = I('post.status', '', 'strval');
        $resendStatus      = I('post.resend_status', '', 'strval');
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];

//        $smsJournalModel = new SmsJournal();
        $smsJournalModel = SmsJournalHistory::getSmsJournalModel(date('Y-m-d', $startTime), date('Y-m-d', $endTime));
        $result          = $smsJournalModel->getSmsRecordList($page, $size, $sid, $ordernum, $mobile, $startTime,
            $endTime,$status,$resendStatus);
        $arrOrderList = $result['list'];
        $orderArr = [];
        foreach ($arrOrderList as &$orderInfo){
            if(is_numeric($orderInfo['ordernum']) && (mb_strlen($orderInfo['ordernum'])==14)){
                array_push($orderArr,$orderInfo['ordernum']);
            }
            if(isset($orderInfo['is_resend'])&& $orderInfo['is_resend']){
                $orderInfo['api_code'] == 200? $orderInfo['resend_status'] =1:$orderInfo['resend_status'] =2;
                $orderInfo['status'] =false;
            }
            else{
                $orderInfo['api_code'] == 200? $orderInfo['status'] =1:$orderInfo['status'] =2;
                $orderInfo['resend_status'] =false;
            }
        }
        if (!empty($orderArr)) {
            $orderTools = new OrderTools();
            $data         = $orderTools->getOrderList($orderArr);
            $tidArr = array_column($data, 'tid','ordernum');
            //根据票ID集合获取票名
            $javaApi = new \Business\CommodityCenter\Ticket();
            $ticketArr = $javaApi->queryTicketInfoByIds(array_values($tidArr), 'id,title');

            $ticketInfoArr = [];
            foreach ($ticketArr as $ticketInfo){
                $info =[
                    'send_voucher' =>$ticketInfo['land_f']['sendVoucher']
                ];
                $ticketInfoArr[$ticketInfo['ticket']['id']] = $info;
            }
            foreach ($arrOrderList as &$info){
                if(array_key_exists($info['ordernum'],$tidArr)){
                    $info['send_voucher'] = $ticketInfoArr[$tidArr[$info['ordernum']]]['send_voucher'];
                }
                else{
                    $info['send_voucher'] = false;
                }
            }
        }
        $result['list'] = array_values($arrOrderList);
        $this->apiReturn(200, $result, '');
    }

    public function orderSmsResend(){
        $loginInfo = $this->getLoginInfo();
        $modifyBiz = new Modify();
        $id    = I('post.id', '', 'strval');
        $mobile    = I('post.mobile', '', 'strval');
        $arrIds = explode(',',$id);
        $result= [
            'success'=>[],
            'fail'=>[]
        ];
        if(empty($id)){
            $this->apiReturn(204, [], '短信Id不能为空');
        }
        else{
            $arrIds = array_values(array_unique($arrIds));
            $smsJournalModel = new SmsJournal();
            $recordList         = $smsJournalModel->getSmsRecordListByIds($arrIds);
            $arrOrderNumList = array_column($recordList,'ordernum','id');
            $tobList = array_column($recordList,'tob','id');
            $arrOrderNum = array_unique(array_values($arrOrderNumList));
            $orderTools = new OrderTools();
            $arrOrderList = $orderTools->getOrderList($arrOrderNum);
            $arrOrderList = array_column($arrOrderList,null,'ordernum');
            if(count($arrOrderNum)>1){
                foreach ($arrOrderNumList as $id => $orderNum){
                    if(self::checkResend($id,$orderNum,$tobList,$arrOrderList,$result) ){
                        if(array_key_exists($orderNum,$result['success'])===false){
                            $res = $modifyBiz->resendMessage($loginInfo, $orderNum, $mobile);
                            pft_log('sms/error',"短信重发失败：".json_encode($res,JSON_UNESCAPED_UNICODE));
                            $res['code'] ==200 ?  $result['success'][$orderNum] = '发送成功':$result['fail'][$orderNum] = $res['msg'];
                        }
                    }
                }
                if(isset($result['fail']) && !empty($result['fail'])){
                    $this->apiReturn(204, $result, '部分发送失败');
                }
                else{
                    $this->apiReturn(200, $result, '批量发送成功');
                }
            }
            else{
                $orderNum = reset($arrOrderNum);
                if(self::checkResend($id,$orderNum,$tobList,$arrOrderList,$result) ){
                    if(array_key_exists($orderNum,$result['success'])===false){
                        $res    = $modifyBiz->resendMessage($loginInfo, reset($arrOrderNum), $mobile);
                        $this->apiReturn($res['code'], $res['data'], $res['msg']);
                    }
                }
                if(isset($result['fail'])){
                    $this->apiReturn(204, [], $result['fail'][$orderNum]);
                }
            }
        }
    }

    private function checkResend($id,$orderNum,$tobList,$arrOrderList,&$result){
        if(!is_numeric($orderNum) || (mb_strlen($orderNum)!=14)){
            $result['fail'][$orderNum] = '仅支持订单号重发';
            return false;
        }
        elseif($arrOrderList[$orderNum]['status']!=0){
            $result['fail'][$orderNum] = '仅未使用的订单支持重发';
            return false;
        }
        elseif(array_key_exists($id,$tobList) && $tobList[$id]['tob']== 'Y'){
            $result['fail'][$orderNum] = '供应商短信不支持重发';
            return false;
        }
        else{
            return true;
        }
    }

}