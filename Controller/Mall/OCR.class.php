<?php
/**
 * 用户身份证文字识别
 * Created by PhpStorm.
 * User: zhangyangzhen
 * Date: 2018/12/6
 * Time: 11:36
 */

namespace Controller\Mall;

use Library\Controller;

class OCR extends Controller
{
    /**
     * 获取OCR身份证文字识别内容
     * Create by zhangyangzhen
     * Date: 2018/12/6
     * Time: 16:07
     */
    public function getOCR()
    {
        $image = I('post.image', ''); //base64加密后的图片信息

        if (empty($image)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请求参数为空');
        }

        $ocrModel = new \Library\OCR();

        $result   = $ocrModel->getOCR($image);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


}