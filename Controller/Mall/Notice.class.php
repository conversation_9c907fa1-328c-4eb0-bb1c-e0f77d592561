<?php

namespace Controller\Mall;

use Business\Mall\Notice as NoticeBiz;

class Notice extends Mall {

    /**
     * 获取供应商发布的公告列表
     * <AUTHOR>
     * @date   2018-08-29
     */
    public function getList() {

        $biz = new NoticeBiz();
        $result = $biz->getList($this->_supplyId, 15);

        if (isset($result['code'])) {
            $this->apireturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apireturn(500, [], '接口出现异常');
        }

    }


    /**
     * 获取公告详情
     * <AUTHOR>
     * @date   2018-08-29
     */
    public function getDetail() {

        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apireturn(204, [], '参数错误');
        }
        
        $biz = new NoticeBiz();
        $result = $biz->getDetail($id);

        if (isset($result['code'])) {
            $this->apireturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apireturn(500, [], '接口出现异常');
        }
    }

    /**
     * 获取自媒体运营广告内容
     * @date   2018-09-20
     * <AUTHOR>
     */
    public function getAdvert(){
        $position    = I('post.position');     //展示位置
        $domain      = I('server.HTTP_HOST'); //域名
        if($position === ''){
            $this->apiReturn(400, [], '展示位置参数错误');
        }

        $AdvertBusiness = new \Business\Advert\PlatAdvert();
        $check = $AdvertBusiness->filterShow($position,$domain);
        if($check == false){
            $this->apiReturn(200, '', '');
        }

        $AdvertModel = new \Model\Advert\PlatAdvert();
        $field    = 'begin,end,content';
        $where    = ['status' => 1];     //打开的状态
        $res      = $AdvertModel->getAdvertInfoByPosition($position,'position',$field,$where);
        if($res){
            //处理一下时间的判断
            $check = $AdvertBusiness->checkAdvertTime($res['begin'],$res['end']);
            if($check){
                $res['content'] = htmlspecialchars_decode($res['content']);
                $this->apiReturn(200, ['content' => $res['content']], '');
            }else{
                $this->apiReturn(200, '', '');
            }
        }else{
            $this->apiReturn(200, '', '');
        }
    }

}
