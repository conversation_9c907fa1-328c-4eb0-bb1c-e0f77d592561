<?php
/**
 * 微商城产品展示相关接口
 * <AUTHOR>
 */

namespace Controller\Mall;

use Business\JavaApi\Context\Product\TicketReserveStorageContext;
use Business\JavaApi\ProductApi;
use Business\JavaApi\TicketApi;
use Business\JavaApi\Ticket\Price;
use Business\Member\MemberMoney;
use Business\Order\OrderBook;
use Business\Product\Product as bizProduct;
use Business\Product\ProductList as bizProductList;
use Business\Product\Show as ShowBiz;
use Business\Product\Specialty;
use Business\Wechat\ActivityBase;
use Business\Product\Ticket as bizTicket;
use Library\Cache\Cache;
use Library\Constants\ThemeConst;
use Model\Mall\AllDis as AllDisModel;
use Model\Mall\MemberSmallAppConfig;
use Model\Market\Market;
use Model\Order\Coupon;
use Model\Product\AnnualCard;
use Model\Product\Area;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\Subdomain\SubdomainInfo;
use Process\Mall\AllDis as AllDisProcess;
use Process\Product\Settle\GroupByTopicOrPtypeSettle;
use Process\Resource\AreaFilter\AreaPinyinSettle;
use Process\Resource\AreaProcess;
use Business\JavaApi\StorageApi;
use Business\JavaApi\Ticket\TicketExtendAttr;

class Product extends Mall
{
    //首页返回数据条数
    protected $_indexShow = 10;

    //允许售卖的产品类型
    // protected $_allowType = ['A', 'B', 'C', 'F', 'H', 'G', 'I', 'J'];
    protected $_allowType = []; // 允许售卖全部先写个空数组

    //上次获取到的最后一个景区在数组中的索引，用于分页
    private $_lastPos = 0;

    /**
     * 微商城首页
     * @return [type] [description]
     */
    public function index()
    {
        //这边为了兼容云闪付首页，做了个判断
        if ($this->inUnionApp()) {
            $cityId = I('city', 381);
        }
        $hotList = $this->_getHotList(I('keyword', ''), $cityId);

        $return = [
            'list' => $hotList,
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 首页推荐产品
     *
     * @param  string $keyword 关键字/城市
     *
     * @return array
     */
    private function _getHotList($keyword = '', $cityId = 0)
    {
        // 设置需要的产品类型
        $allType = $this->_allowType;
        if ($keyword) {
            $areas = (new \Model\Product\Area())->getAreaList();
            if (array_search($keyword, $areas)) {
                $option['area'] = $keyword;
            } else {
                $option['title'] = $keyword;
            }
        }
        // TODO 这边区分下云闪付渠道的供应商,做法比较喽,大佬看到如果能提出优化方案我就改掉（lc留）
        if ($cityId && $this->inUnionApp()) {
            $option['city'] = $cityId;
        }

        $products = $this->_getProductSet($option, $allType, $pageNo = 1, $pageNum = 10);

        if (empty($products)) {
            return [];
        }
        //整合前端需要的数据
        $list = $this->_productDeal($products['lists']);
        if ($this->_allDisMan) {
            $list = AllDisProcess::getAllDisPrice($this->_supplyId, $list, 'index');
        }

        return $list;
    }

    protected function getSupplyIdsByAccounts(array $accounts)
    {
        foreach ($accounts as $key => $value) {
            if (empty($value)) {
                unset($accounts[$key]);
            }
        }
        $member      = new \Model\Member\Member();
        $where       = ["account" => ['in', $accounts]];
        $field       = "id";
        $memberInfos = $member->getMemberInfoByMulti($accounts, 'account', 'id');

        return array_column($memberInfos, "id");
    }

    /**
     * 微商城产品列表页
     */
    public function productList()
    {
        //产品类型
        $ptype = I('type', 'all');
        //上次查询的位置
        $lastPos = I('lastPos', 0, 'intval');
        //每页条数
        $pageSize = I('pageSize', 10, 'intval');
        //产品关键字
        $keyword = I('keyword', '', 'string');
        //主题
        $topic = I('topic', '', 'string');
        //城市代码
        $city = I('city', 0, 'intval');
        //小程序扫码
        $wxAppScenCode = I('post.scenCode', 0);
        // 优惠券id
        $couponId   = I('coupon_id', 0, 'intval');
        $activityId = I('activity_id', 0, 'intval');

        $ticketModel = new Ticket();

        // 限制产品类型和主题文件长度
        if (!empty($ptype) && mb_strlen($ptype, 'UTF8') > 3) {
            $ptype = 'all';
        }
        if (!empty($topic) && mb_strlen($topic, 'UTF8') > 10) {
            $topic = '';
        }
        if (!empty($keyword) && mb_strlen($keyword, 'UTF8') > 20) {
            $keyword = '';
        }

        $lids       = '';
        $limitPtype = '';
        // 根据优惠券过滤景区
        if (!empty($couponId)) {
            $couponModel   = new Coupon();
            $couponInfoArr = $couponModel->getCouponById($couponId, 'pid, limit_ptype');
            if (!empty($couponInfoArr['limit_ptype'])) {
                $limitPtype = $couponInfoArr['limit_ptype'];
            }

            if (!empty($couponInfoArr['pid'])) {
                $pidArr = explode(',', $couponInfoArr['pid']);
                $lidArr = $ticketModel->getTicketInfoByPidArr($pidArr, 'landid');
                $lidArr = array_unique(array_column($lidArr, 'landid'));
                $lids   = implode(',', $lidArr);
            }
        }
        // 根据是否根据活动id过滤景区
        if (!empty($activityId)) {
            $lids         = '';
            $marketModel  = new Market();
            $activityInfo = $marketModel->findMarketing($activityId, 'limit_product');
            if (!empty($activityInfo)) {
                if (preg_match('/[a-zA-Z]/', $activityInfo['limit_product'])) {
                    // 字母类型
                    $limitPtype = $activityInfo['limit_product'];
                } else {
                    $pidArr = [];
                    if (!empty($activityInfo['limit_product'])) {
                        $pidArr = explode(',', $activityInfo['limit_product']);
                    }
                    $lidArr = $ticketModel->getTicketInfoByPidArr($pidArr, 'landid');
                    $lidArr = array_unique(array_column($lidArr, 'landid'));
                    $lids   = implode(',', $lidArr);
                }
            }
        }

        // 搜索的类型和优惠券活动支持的类型不一致 返回数据空
        if (!empty($limitPtype) && $ptype != 'all' && $ptype != $limitPtype) {
            $return = [
                'list'    => [],
                'lastPos' => $this->_lastPos,
            ];
            $this->apiReturn(200, $return);
        }

        // 限制搜索的类型为优惠券活动限制的类型的产品
        if (!empty($limitPtype)) {
            $ptype = $limitPtype;
        }

        $supplyAccounts = I('post.supplyIds', '');
        $supplyIds      = $this->getSupplyIdsByAccounts(explode(',', $supplyAccounts));
        if ($this->inUnionApp()) {

        } else {
            $this->setSupplyIds($supplyIds);
        }
        if ($topic == -1) {
            $topic       = '';
            $productList = $this->_getProductList($ptype, $lastPos, $pageSize, $keyword, $topic, $city, $lids);
            GroupByTopicOrPtypeSettle::clearUp($productList);
        } else {
            $productList = $this->_getProductList($ptype, $lastPos, $pageSize, $keyword, $topic, $city, $lids);
        }

        $return = [
            'list'    => $productList,
            'lastPos' => $this->_lastPos,
        ];
        //第一页的请求,前端需要缓存一些信息
        if ($lastPos == 0) {
            $return['citys']  = $this->getAreaList(true);
            $return['themes'] = $this->getThemes();
            $return['type']   = $this->getTypeList(true);

            if ($wxAppScenCode) {
                $subModel           = new SubdomainInfo();
                $field              = 'M_name as name,M_banner as img,M_tel as tel,longitude,latitude';
                $info               = $subModel->getBindedSubdomainInfo($this->_supplyId, 'id', $field);
                $return['shopInfo'] = $info;
            }
        }
        // 加入轮播图(小程序用到)
        if ($this->inWechatSmallApp()) {
            $memberSmallAppConfig = new MemberSmallAppConfig();
            $configItem           = $memberSmallAppConfig->getConfigByMemberId($this->_supplyId);
            if ($configItem->id) {
                $smallAppConfig = $configItem->getImages('banner');
                if (is_array($smallAppConfig)) {
                    foreach ($smallAppConfig as $value) {
                        $banner[] = $value['path'];
                    }
                }

                $return['shopInfo']['imgUrls']   = $banner;
                $return['shopInfo']['name']      = $configItem->store_name;
                $return['shopInfo']['longitude'] = $configItem->longitude;
                $return['shopInfo']['latitude']  = $configItem->latitude;
                $return['shopInfo']['tel']       = $configItem->service_mobile;
            } else {
                $config = $this->getCustomConfig(true);

                $banner = [];
                foreach ($config['banner'] as $item) {
                    $banner[] = key($item);
                }

                $return['shopInfo']['imgUrls'] = $banner;
                $return['shopInfo']['name']    = $config['name'];
            }
        }
        $activid   = new ActivityBase();
        $activiPid = $activid->getPidInActive($this->_supplyId);
        if ($activiPid['code'] != 200) {
            $this->apiReturn(204, [], $activiPid['msg']);
        }
        if (empty($activiPid['data'])) {
            $this->apiReturn(200, $return);
        }
        // 与正在活动中的产品进行匹配
        foreach ($return['list'] as $key => $value) {
            foreach ($activiPid['data'] as $item) {
                if ($value['lid'] == $item['lid']) {
                    $return['list'][$key]['activityType'] = $item['type'];
                }
            }
        }

        $this->apiReturn(200, $return);
    }

    public function getBar()
    {
        $data = [
            "themes" => $this->getThemes(),
            "type"   => $this->getTypeList(true),
        ];
        if (!empty($_SESSION['memberID'])) {
            $data['systemMenu'] = $this->getSystemMenu();
        }
        $this->apiReturn(200, $data);
    }

    /***
     * 获取用户角色列表.
     * User: xujinyao
     * Date: 2019/4/9
     * @return array
     */
    protected function getSystemMenu()
    {
        $loginMemberInfo         = $this->getLoginInfo();
        $systemMenu              = [];
        $indexPage               = ($loginMemberInfo['dtype'] == 5) ? 'wx/c/index.html' : 'wx/login.html';//5散客， 微平台
        $memberBz                = new \Business\Member\Member();
        $roleList                = $memberBz->getMemberListByCustomerId($loginMemberInfo['customerId']);
        $systemMenu['indexPage'] = $indexPage;
        $systemMenu['roleList']  = $roleList['data'];

        return $systemMenu;
    }

    /**
     * 获取产品列表页的景区信息
     *
     * @param  string $ptype 产品类型
     * @param  int $lastPos 上一次搜索位置(改成页数)
     * @param  int $pageSize 每页条数
     * @param  string $keyword 关键字
     * @param  string $topic 主题
     * @param  string $city 城市code
     * @param  string $isTopicNN topic不为空
     * @param  int $couponId 优惠券id
     *
     * @return [type]
     */
    private function _getProductList($ptype, $lastPos, $pageSize, $keyword, $topic, $city, $lids = '')
    {

        $option = [];
        $type   = [];
        if ($ptype != 'all') {
            $type = [$ptype];
        }

        if ($keyword) {
            $option['title'] = $keyword;
        }

        //城市筛选
        if ($city) {
            if (in_array($city, [1, 2, 3, 4])) {
                $option['province'] = $city;
            } else {
                $option['city'] = $city;
            }
        }

        //主题筛选
        if ($topic) {
            $option['topic'] = $topic;
        }

        // 利用当前条数计算当前页面数量
        if ($lastPos == '') {
            $this->_lastPos = $pageNum = 1;
        } else {
            $this->_lastPos = $pageNum = $lastPos + 1;
        }

        $products = $this->_getProductSet($option, $type, $pageNum, $pageSize, $lids);

        if (empty($products['lists'])) {
            return [];
        }

        //整合前端需要的数据
        $list = $this->_productDeal($products['lists']);

        if ($this->_allDisMan && $list) {
            //全民分销价格优惠
            $list = AllDisProcess::getAllDisPrice($this->_supplyId, $list, 'list');
            if ($this->_promoter) {
                //计算推广佣金
                $list = AllDisProcess::calRecommendCommissionBetween($this->_supplyId, $list);

            }
        }

        return $list;
    }

    public function getAreaList($result = false)
    {
        $area     = new Area(2);
        $landInfo = AreaProcess::getAreaByMember($area, $this->_supplyId);
        AreaPinyinSettle::clearUp($landInfo);
        $landInfo = array_reverse($landInfo);
        $result   = [];
        foreach ($landInfo as $key => $value) {
            $result = array_merge($value, $result);
        }
        if ($result) {
            return $result;
        }
        $this->apiReturn(200, $result);
    }

    /**
     * 详情页-获取景区信息
     */
    public function getLandInfo()
    {
        header("Access-Control-Allow-Origin: *"); // 允许任意域名发起的跨域请求
        $lid = I('lid', '', 'intval');

        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        // 转换到java接口
        $productBiz = new bizProduct();
        if ($this->inUnionApp()) {
            $accountIds  = $this->getSupplyIds();
            $landInfoArr = $productBiz->getProductInfo($accountIds, $lid, 0);
        } else {
            $landInfoArr = $productBiz->getProductInfo($this->_supplyId, $lid, 0);
        }

        if ($landInfoArr['code'] != 200) {
            $this->apiReturn(204, [], $landInfoArr['msg']);
        }

        if ($landInfoArr['data']['imgpathGrp']) {
        } else {
            $landInfoArr['data']['imgpathGrp'] = [];
        }

        $landType = load_config('land_ptype', 'account');

        $landInfoArr['data']['ptype']    = $landType[$landInfoArr['data']['p_type']];
        $landInfoArr['data']['jqts']     = nl2br($landInfoArr['data']['jqts']);
        $landInfoArr['data']['jtzn']     = nl2br($landInfoArr['data']['jtzn']);
        $landInfoArr['data']['bhjq']     = htmlspecialchars_decode($landInfoArr['data']['bhjq']);
        $landInfoArr['data']['bhjq']     = \image_opt($landInfoArr['data']['bhjq'], 600);
        $landInfoArr['data']['venue_id'] = $landInfoArr['data']['venus_id'];

        if (strpos($landInfoArr['data']['lng_lat_pos'], ',') !== false) {
            [$longitude, $latitude] = explode(',', $landInfoArr['data']['lng_lat_pos']);
            $landInfoArr['data']['latitude']  = $latitude;
            $landInfoArr['data']['longitude'] = $longitude;
        } else {
            //没有的话，定位到北京天安门....
            $landInfoArr['data']['latitude']  = 39.915119;
            $landInfoArr['data']['longitude'] = 116.403963;
        }

        //如果是场次类产品，添加额外的场次数据
        if ($landInfoArr['data']['jtype'] == 'H') {
            $showBiz = new ShowBiz();
            $tmpInfo = $showBiz->getLastedRoundList($landInfoArr['data']['venue_id']);
            $code    = $tmpInfo['code'];

            if ($code == 1) {
                //有获取到数据
                $data = $tmpInfo['data'];

                $landInfoArr['data']['venue_img']       = $data['venue_img'];
                $landInfoArr['data']['show_start_date'] = $data['lasted_date'];
                $landInfoArr['data']['show_round_list'] = $data['round_list'];

            } else {
                //获取场次数据错误
                pft_log('order_show/error', json_encode(['getLandInfo', $landInfoArr['data']['venue_id'], $tmpInfo]));

                $landInfoArr['data']['venue_img']       = '';
                $landInfoArr['data']['show_start_date'] = '';
                $landInfoArr['data']['show_round_list'] = [];
            }
        }

        $this->apiReturn(200, $landInfoArr['data']);
    }

    /**
     * 详情页-获取景区下的门票列表
     */
    public function getTicketList()
    {
        header("Access-Control-Allow-Origin: *"); // 允许任意域名发起的跨域请求
        //景区id
        $lid = I('lid', 0, 'intval');
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        // 优惠券id
        $couponId   = I('coupon_id', 0, 'intval');
        $activityId = I('activity_id', 0, 'intval');

        //页面上面选定的日期
        $date = I('date', '', 'strval');
        $date = strtotime($date) ? date('Y-m-d', strtotime($date)) : date('Y-m-d');

        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $tids = '';
        if (!empty($couponId)) {
            $couponModel   = new Coupon();
            $ticketModel   = new Ticket();
            $couponInfoArr = $couponModel->getCouponById($couponId, 'pid');
            if (!empty($couponInfoArr)) {
                $pidArr = explode(',', $couponInfoArr['pid']);
                $tidArr = $ticketModel->getTicketInfoByPidArr($pidArr, 'id');
                $tidArr = array_unique(array_column($tidArr, 'id'));
                $tids   = implode(',', $tidArr);
            }
        }
        if (!empty($activityId)) {
            $tids         = '';
            $marketModel  = new Market();
            $activityInfo = $marketModel->findMarketing($activityId, 'limit_product');
            if (!empty($activityInfo['limit_product'])) {
                if (preg_match('/[a-zA-Z]/', $activityInfo['limit_product'])) {
                    // 字母类型
                    $ptype = $activityInfo['limit_product'];
                } else {
                    $tidArr = $ticketModel->getTicketInfoByPidArr($activityInfo['limit_product'], 'id');
                    $tidArr = array_unique(array_column($tidArr, 'id'));
                    $tids   = implode(',', $tidArr);
                }
            }
        }

        $inWechatSmApp = $this->inWechatSmallApp();
        $channelVal    = $this->checkChannel($inWechatSmApp);

        if ($this->inUnionApp()) {
            $ticketBiz   = new \Business\Product\Ticket();
            $productList = $ticketBiz->getRetailTicketListByAccountIds($this->getSupplyIds(), $lid, $channelVal, '', '',
                $tids);
        } else {
            $productList = $this->_getLandTickets($lid, $channelVal, '', $tids);
        }
        $tags = $this->_parseTicketsTags($productList);

        //是否根据上级id过滤
        $aidFilter = false;
        if ($aid) {
            $applySidArr = array_column($productList, 'superior_id');
            if (in_array($aid, $applySidArr)) {
                $aidFilter = true;
            }
        }

        //从java获取指定日期票类价格
        $ticketIds = array_column($productList, 'id');
        $ticketIds = implode(',', $ticketIds);
        $priceData = TicketApi::getSinglePrices($ticketIds, $date);//获取各个票的价格
        $startDate = TicketApi::getEarliestPriceDate($ticketIds);//获取各个票的最早可预订日期

        // 从java获取了门票列表
        $list = [];
        foreach ($productList as $key => $item) {
            if ($aidFilter && $item['superior_id'] != $aid) {
                continue;
            }

            $ptype  = $item['type'];
            $tPrice = $item['counter_price'] / 100;

            if (\inWechatSmallApp()) {
                $jsPrice = $item['window_price'] / 100;
            } else {
                $jsPrice = $item['retail_price'] / 100;
            }
            if ($ptype == 'H') {
                $tPrice = $priceData[$item['id']]['counter_price'];
                $tPrice = (isset($tPrice)) ? $tPrice / 100 : '';
                if (\inWechatSmallApp()) {
                    $jsPrice = $priceData[$item['id']]['window_price'];
                    $jsPrice = (isset($jsPrice)) ? $jsPrice / 100 : '';
                } else {
                    $jsPrice = $priceData[$item['id']]['retail_price'];
                    $jsPrice = (isset($jsPrice)) ? $jsPrice / 100 : '';
                }
            }

            $tmp = [
                'ticket'      => $item['name'],
                'pid'         => $item['product_id'],
                'tid'         => $item['id'],
                // 'px'      => $item['px'],
                'aid'         => $item['superior_id'],
                'sid'         => $item['supplier_id'],
                'jsprice'     => $jsPrice,
                'tprice'      => $tPrice,
                'tags'        => !empty(array_values($tags[$item['id']])) ? array_values($tags[$item['id']]) : '',
                'intro'       => explode('<br />', nl2br($item['introduction'])),
                'refund_rule' => OrderBook::handleRefundRule($item['refund_audit'], $item['refund_rule'],
                    $item['refund_early_minu'], $item['ticketDetail']['ext']['refund_after_time'] ?? 0),
                'riskWarning' => $item['riskWarning'],
                'usetime'     => OrderBook::getValidityDate($item),
                'predays'     => $item['preorder_early_days'],
                'pretimes'    => $item['preorder_expire_time'],
                'start_date'  => $startDate[$item['id']],
            ];

            //处理套票子票
            if (in_array($ptype, ['F', 'H'])) {
                $packBiz   = new \Business\Product\PackTicket();
                $childData = $packBiz->getChildTickets($item['id']);

                if (!empty($childData)) {
                    $tmp['childTickets'] = $childData;
                    $sonPidArr           = array_column($childData, 'pid');
                    $tmp['usetime']      = OrderBook::parsePackValidTag($sonPidArr);
                }
            }

            $list[] = $tmp;
        }
        if (!empty($list) && $this->inUnionApp() && $this->unionSmallAppType == 0) {
            //返回在云闪付里用的account给前端跳转
            $productBiz = new \Business\Mall\Product();
            $list       = $productBiz->getTicketAccountByUnion($list);
        }
        if ($list) {
            $list = $this->_fillExtraForTicketList($list, $ptype);
        }

        // 待确定 todo java
        if ($this->_allDisMan) {
            $list = AllDisProcess::getAllDisPrice($this->_supplyId, $list, 'list');
            $list = AllDisProcess::calRecommendCommission($this->_supplyId, $list);
        }
        //微信抢购活动
        $list = $this->_seckillDecorate($list);
        // 微信砍价活动
        $list = $this->_cutDecorate($list);
        //微信拼团活动
        $list = $this->_groupBookingDecorate($list);

        $this->apiReturn(200, ['type' => $ptype, 'list' => $list]);
    }

    /**
     * 获取门票包含的年卡特权
     * <AUTHOR>
     * @date   2017-11-20
     */
    public function getAnnualPrivilege()
    {

        $pid = I('pid', 0, 'intval');

        if (!$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $annualBiz  = new \Business\Product\AnnualCard();
        $privileges = $annualBiz->getPrivileges($pid);

        if ($privileges) {
            foreach ($privileges as &$item) {
                if ($item['use_limit'] == '-1') {
                    $item['use_limit'] = '-1,-1,-1';
                }
            }
        }

        $this->apiReturn(200, $privileges);
    }

    /**
     * 门票列表根据产品类型填充额外信息
     * <AUTHOR>
     * @date   2017-11-20
     *
     * @param  array $list 门票列表数据
     * @param  string $type 产品类型
     *
     * @return array
     */
    private function _fillExtraForTicketList($list, $type)
    {

        switch ($type) {

            case 'F':
                //套票
                $list = $this->_fillExtraForTicketListByF($list, $type);
                break;

            case 'H':
                $list = $this->_fillExtraForTicketListByH($list, $type);
                break;

            case 'I':
                $list = $this->_fillExtraForTicketListByI($list, $type);
                break;

            case 'default':
                break;
        }

        return $list;
    }

    /**
     * 门票列表填充套票信息
     * <AUTHOR>
     * @date   2017-11-20
     *
     * @param  array $list 门票列表数据
     * @param  string $type 产品类型
     *
     * @return array
     */
    private function _fillExtraForTicketListByF($list, $type)
    {

        //套票的话需要返回子票信息
        $list = $this->_parseSonTickets($list);

        return $list;

    }

    /**
     * 门票列表填充演出信息
     * <AUTHOR>
     * @date   2017-11-20
     *
     * @param  array $list 门票列表数据
     * @param  string $type 产品类型
     *
     * @return arrray
     */
    private function _fillExtraForTicketListByH($list, $type)
    {
        $tidArr      = array_column($list, 'tid');
        $zoneMapping = (new \Model\Product\Ticket())->getZoneName($tidArr);

        foreach ($list as &$item) {
            $tid = $item['tid'];

            if (isset($zoneMapping[$tid])) {
                $item['zone_name'] = $zoneMapping[$tid]['zone_name'];
                $item['zone_id']   = $zoneMapping[$tid]['id'];
            } else {
                $item['zone_name'] = '';
                $item['zone_id']   = 0;
            }
        }

        return $list;
    }

    /**
     * 门票列表填充年卡信息
     * <AUTHOR>
     * @date   2017-11-20
     *
     * @param  array $list 门票列表数据
     * @param  string $type 产品类型
     *
     * @return arrray
     */
    private function _fillExtraForTicketListByI($list, $type)
    {
        //年卡(虚拟卡库存)
        $pidArr = array_column($list, 'pid');

        $annualModel = new AnnualCard('slave');

        $sid    = $list[0]['sid'];
        $stgMap = $annualModel->getAnnualCardStorage($sid, $pidArr);

        foreach ($list as &$item) {
            if (isset($stgMap[$item['pid']])) {
                $item['storage'] = $stgMap[$item['pid']];
            } else {
                $item['storage'] = 0;
            }
        }

        return $list;
    }

    /**
     * 是否有抢购活动
     * <AUTHOR>
     * @date   2017-07-19
     *
     * @param  array $list 门票列表信息
     *
     * @return array
     */
    private function _seckillDecorate($list)
    {

        if (!$list) {
            return $list;
        }

        $pidArr = array_column($list, 'pid');

        $seckillBiz = new \Business\Wechat\Seckill();
        //获取哪些票类有抢购
        $result = $seckillBiz->getRecentSeckillForPid($pidArr);

        if (!$result['data']) {
            return $list;
        }

        $seckills = $result['data'];

        foreach ($list as &$item) {
            if (isset($seckills[$item['pid']])) {

                $seckill = $seckills[$item['pid']];

                if ($seckill['sid'] == $this->_supplyId) {

                    if ($seckill['aid'] != $item['aid']) {
                        continue;
                    }

                    $cycleRes = $seckillBiz->parseCycle($seckill['id']);

                    if ($cycleRes['code'] == 200) {
                        $seckill['begin'] = $cycleRes['data']['begin'];
                        $seckill['end']   = $cycleRes['data']['end'];
                        $item['seckill']  = $seckill;
                    }

                    $item['seckill'] = $seckill;
                }
            }
        }

        return $list;
    }

    /**
     * 是否有砍价活动
     * <AUTHOR>
     * @date   2018-12-10
     *
     * @param  array $list 门票列表信息
     *
     * @return array
     */
    private function _cutDecorate($list)
    {
        if (!$list) {
            return $list;
        }
        $pidArr = array_column($list, 'pid');

        $cutBiz = new \Business\Wechat\CutPrice();
        //获取哪些票类有砍价
        $result = $cutBiz->getRecentCutForPid($pidArr);

        if (!$result['data']) {
            return $list;
        }

        $cuts = $result['data'];

        foreach ($list as &$item) {
            if (isset($cuts[$item['pid']])) {

                $cut = $cuts[$item['pid']];

                if ($cut['sid'] == $this->_supplyId) {

                    if ($cut['aid'] != $item['aid']) {
                        continue;
                    }
                    $cycleRes = $cutBiz->parseCycle($cut['id']);
                    if ($cycleRes['code'] == 200) {
                        $cut['begin'] = $cycleRes['data']['begin'];
                        $cut['end']   = $cycleRes['data']['end'];

                        $item['cut'] = $cut;
                    }

                    $item['cut'] = $cut;
                }
            }
            unset($item);
        }

        return $list;
    }

    /**
     * 是否有拼团活动
     * <AUTHOR>
     * @date   2018-12-10
     *
     * @param  array $list 门票列表信息
     *
     * @return array
     */
    private function _groupBookingDecorate($list)
    {
        if (!$list) {
            return $list;
        }
        $pidArr = array_column($list, 'pid');

        $groupBookBiz = new \Business\Wechat\GroupBooking();
        //获取哪些票类有砍价
        $result = $groupBookBiz->getRecentGroupBookingForPid($pidArr);

        if (!$result['data']) {
            return $list;
        }

        $groupBooks = $result['data'];

        foreach ($list as &$item) {
            if (isset($groupBooks[$item['pid']])) {

                $groupBook = $groupBooks[$item['pid']];

                if ($groupBook['sid'] == $this->_supplyId) {

                    if ($groupBook['aid'] != $item['aid']) {
                        continue;
                    }
                    $item['group_book'] = $groupBook;
                }
            }
            unset($item);
        }

        return $list;
    }

    /**
     * 获取抢购活动的详情信息
     * <AUTHOR>
     * @date   2017-07-19
     */
    public function getSeckillDetail()
    {
        $seckillId = I('seckill_id', 0, 'intval');
        $aid       = I('aid', 0, 'intval');

        if (!$seckillId || !$aid) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $seckillBiz = new \Business\Wechat\Seckill();
        //获取抢购信息
        $field  = 'id,pid,begin,end,price';
        $secRes = $seckillBiz->getSeckill($seckillId, 0, $field);

        if ($secRes['code'] != 200 || !$secRes['data']) {
            $this->apiReturn(204, [], '抢购活动不存在');
        }

        $seckill = $secRes['data'];

        //周期性抢购
        $cycleRes = $seckillBiz->parseCycle($seckillId);

        if ($cycleRes['code'] != 200) {
            $this->apiReturn(204, [], '抢购周期解析错误');
        }

        $seckill['begin'] = $cycleRes['data']['begin'];
        $seckill['end']   = $cycleRes['data']['end'];

        //是否抢完
        //是否售罄
        $getRes = $seckillBiz->getRealtimeSeckillInfo($seckillId, $seckill['begin'], $seckill['end']);

        if ($getRes['code'] != 200) {
            $this->apiReturn(204, [], '抢购活动不存在');
        }

        $realtimeInfo = $getRes['data'];

        //获取周期key
        $cycleKey = $seckillBiz->generateCycleKey($seckill['begin'], $seckill['end']);

        if ($realtimeInfo[$cycleKey] == 0) {
            $seckill['sell_out'] = 1;
        } else {
            $seckill['sell_out'] = 0;
        }

        $pid = $seckill['pid'];

        //获取零售价(分)
        $retail = $seckill['price'];
        //获取门市价(元)
        // 获取票的信息
        $ticketBiz  = new \Business\CommodityCenter\Ticket();
        $ticketData = $ticketBiz->fieldConversion($ticketBiz->queryTicketInfoById($seckill['tid'], '', '', '', '',
            false, $aid, $this->_supplyId, $this->saleChannel));
        //$ticketApi  = new TicketApi();
        //$ticketData = $ticketApi->getTickets($seckill['tid'], $this->_supplyId, $aid, $this->saleChannel);

        //门票ID
        $tid = $ticketData['id'];

        //通过接口获取门市价
        $priceApi = new Price();
        $priceRes = $priceApi->getActualtimePrice($ticketData['account_id'], $ticketData['account_id'], $tid, false);

        if ($priceRes['code'] != 200) {
            $this->apiReturn(204, [], '获取价格失败');
        }
        //门市价 - 单位分
        $market = $priceRes['data']['counter_price'];

        //获取可使用时间
        $validTime = '';
        if (strtotime($ticketData['valid_period_start']) > 0) {
            //时间段内有效
            $tmpS = date('Y-m-d', strtotime($ticketData['valid_period_start']));
            $tmpE = date('Y-m-d', strtotime($ticketData['valid_period_end']));

            $validTime = $tmpS . '~' . $tmpE . '有效';
        } elseif ($ticketData['valid_period_days'] && !$ticketData['use_early_days']) {
            //多少天内有效
            $validTime = "下单后{$ticketData['valid_period_days']}天内有效";
        } elseif ($ticketData['valid_period_days'] && $ticketData['use_early_days']) {
            $validTime = "游玩日期前(含){$ticketData['use_early_days']}天有效,后{$ticketData['valid_period_days']}天有效";
        } else {
            //当天有效
            $validTime = '游玩日期当天有效';
        }

        // 获取景点的相关的信息
        // 获取个景点信息
        $productBiz  = new bizProduct();
        $landInfoArr = $productBiz->getProductInfo($this->_supplyId, $ticketData['item_id'], 0);

        $landInfo['id']      = $ticketData['item_id'];
        $landInfo['jqts']    = nl2br($landInfoArr['data']['jqts']);
        $landInfo['jtzn']    = nl2br($landInfoArr['data']['jtzn']);
        $landInfo['bhjq']    = htmlspecialchars_decode($landInfoArr['data']['bhjq']);
        $landInfo['bhjq']    = \image_opt($landInfoArr['data']['bhjq'], 600);
        $landInfo['p_type']  = $landInfoArr['data']['p_type'];
        $landInfo['imgpath'] = $landInfoArr['data']['imgpath'];

        //是否设置过微信提醒
        $warning = 0;
        if (I('session.openid')) {
            $openid  = I('session.openid');
            $warnRes = $seckillBiz->getOneWarning($seckillId, $openid, 'status');
            //设置过
            if ($warnRes['code'] == 200 && $warnRes['data']) {
                $warning = 1;
            }
        }

        //全民营销推广者
        $allDisModel     = new AllDisModel();
        $is_seckill_join = 0;
        $commission      = 0;

        if ($this->_promoter) {
            //获取供应商openid
            $memberInfo = $allDisModel->getAllDisMemberInfo(I('session.memberID'));
            if ($memberInfo && $memberInfo['supply_openid']) {
                $openid = $memberInfo['supply_openid'];
            }
            //佣金
            $param = [
                'pid'     => $pid,
                'tid'     => $tid,
                'aid'     => $aid,
                'disId'   => $this->disId,
                'jsprice' => $retail / 100,
            ];
            //计算推广佣金
            $commissionRes = AllDisProcess::calRecommendCommission($this->_supplyId, [$param]);
            $commission    = intval($commissionRes[0]['commission']);
            //根据后台配置是否有配置限时抢购参加全民营销
            $config = $allDisModel->getAllDisConfig($this->_supplyId);
            if ($config) {
                $config          = json_decode($config['config'], true);
                $is_seckill_join = $config['is_seckill_join'] ? 1 : 0;
            }
        }

        $return = [
            'set_warning'     => $warning,
            'land_info'       => $landInfo,
            'seckill_info'    => $seckill,
            'retail'          => $retail,
            'market'          => $market,
            'valid_time'      => $validTime,
            'title'           => $landInfoArr['data']['title'],
            'ttitle'          => $ticketData['name'],
            'commission'      => $commission,
            'is_seckill_join' => $is_seckill_join,
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 解析票类的一些标签属性
     *
     * @param  [type] $tickets 门票列表
     *
     * @return [type]          [description]
     */
    private function _parseTicketsTags($tickets)
    {
        $tags           = [];
        $orderBookModel = new \Business\Order\OrderBook();
        foreach ($tickets as $item) {
            if ($item['ticketDetail']['preorder_early_days'] > 0) {
                $tags[$item['id']]['useTimeStr'] = "最早可订{$item['ticketDetail']['preorder_early_days']}天票";
            } elseif ($item['ticketDetail']['preorder_early_days'] == 0) {
                $tags[$item['id']]['useTimeStr'] = "最早可订今日票";
            }
            $refundRuleStr                  = $orderBookModel::handleRefundRule($item['refund_audit'],
                $item['refund_rule'], $item['refund_early_minu'], $item['ticketDetail']['ext']['refund_after_time'] ?? 0);
            $tags[$item['id']]['refundStr'] = $refundRuleStr;

            $expireStr                      = $orderBookModel::getValidityDate($item['ticketDetail']);
            $tags[$item['id']]['expireStr'] = $expireStr;

            if ($item['pay_way'] == 0) {
                $tags[$item['id']]['cash'] = '现场支付';
            }

            if ($item['pay'] == 3) {
                $tags[$item['pid']]['card'] = '会员卡支付';
            }

            //退票规则：添加了这个值 -1=不可退且是可提现
            // if ($item['refund_rule'] == 2 || $item['refund_rule'] == -1) {
            //     $tags[$item['id']][] = '不可退';
            // }
        }

        return $tags;
    }

    /**
     * 解析子票信息
     * <AUTHOR>
     * @time   2017-01-15
     *
     * @param  array $tickets 门票列表
     *
     * @return array              [description]
     */
    private function _parseSonTickets($tickets = [])
    {
        if (!is_array($tickets) || !$tickets) {
            return [];
        }

        $ticketExtendAttr = new TicketExtendAttr();

        //$javaApi = new \Business\JavaApi\Product\PackageTicket();
        $packApi = new \Business\PackTicket\PackRelation();
        foreach ($tickets as $key => $item) {
            //$sonTickets = $javaApi->queryPageTicketInfoListByParentId($item['tid']);
            //$sonTickets = $ticketApi->getSonTicketList($item['tid']);
            $sonTickets = $packApi->queryPageTicketInfoListByParentId($item['tid']);
            // 兼容写法 ，有改版记得修改简单点
            if ($sonTickets['code'] == 200) {
                $childTicketIds      = array_column($sonTickets['data'], 'ticket_id');
                $ticketSpecialValRes = $ticketExtendAttr->queryTicketSpecialVal($childTicketIds,
                    ['more_credentials', 'auto_sale_rule', 'buy_limit_num']);
                $ticketSpecialMap    = [];
                if ($ticketSpecialValRes['code'] == 200) {
                    // key => item
                    foreach ($ticketSpecialValRes['data'] as $specialValItem) {
                        $ticketSpecialMap[$specialValItem['ticketId']][$specialValItem['key']] = $specialValItem['val'];
                    }
                }

                foreach ($sonTickets['data'] as $sonKey => $sonVal) {
                    $tickets[$key]['sonTickets'][$sonKey]['num']           = $sonVal['num'];
                    $tickets[$key]['sonTickets'][$sonKey]['ticket_id']     = $sonVal['ticket_id'];
                    $tickets[$key]['sonTickets'][$sonKey]['buy_limit_num'] = $sonVal['ticket_extend_info']['buy_limit_num'];
                    $tickets[$key]['sonTickets'][$sonKey]['title']         = $sonVal['item_name'] . $sonVal['ticket_name'];
                    $tickets[$key]['sonTickets'][$sonKey]['lid']           = $sonVal['item_id'];
                    $tickets[$key]['sonTickets'][$sonKey]['needID']        = $sonVal['ticket_extend_info']['tourist_info'];

                    // 填写更多信息
                    if ($sonVal['ticket_extend_info']['p_type'] == 'A') {
                        if (!isset($ticketSpecialMap[$sonVal['ticket_id']])) {
                            $tickets[$key]['sonTickets'][$sonKey]['more_credential_content'] = [];
                            $tickets[$key]['sonTickets'][$sonKey]['auto_sale_rule']          = [];
                            $tickets[$key]['sonTickets'][$sonKey]['auto_sale_rule_status']   = false;
                        } else {
                            $specialValTmp = $ticketSpecialMap[$sonVal['ticket_id']];
                            //填写更多信息游客取票人证件
                            $tickets[$key]['sonTickets'][$sonKey]['more_credential_content'] = $specialValTmp['more_credentials'] ? json_decode($specialValTmp['more_credentials'],
                                true) : [];
                            //自动起售停售
                            $tickets[$key]['sonTickets'][$sonKey]['auto_sale_rule'] = $specialValTmp['auto_sale_rule'] ? json_decode($specialValTmp['auto_sale_rule'],
                                true) : [];

                            $tickets[$key]['sonTickets'][$sonKey]['auto_sale_rule_status'] = true;
                            if ($tickets[$key]['sonTickets'][$sonKey]['auto_sale_rule']) {
                                $time = time();
                                if (strtotime($specialValTmp['auto_sale_rule']['start']) > $time || strtotime($specialValTmp['auto_sale_rule']['end']) < $time) {
                                    $tickets[$key]['sonTickets'][$sonKey]['auto_sale_rule_status'] = false;
                                } else {
                                    $tickets[$key]['sonTickets'][$sonKey]['auto_sale_rule_status'] = true;
                                }
                            }
                        }
                    }
                }
            }
        }

        return $tickets ?: [];
    }

    /**
     * 解析年卡是否需要填写身份证
     * <AUTHOR>
     * @date   2017-12-01
     *
     * @param  array $list 门票列表数据
     * @param  int $family_card_num 年卡成员人数 0为个人卡 大于0为家庭卡
     *
     * @return array
     */
    private function _parseCertLimit($list, $family_card_num = 0)
    {
        $javaApi = new \Business\CommodityCenter\Ticket();
        foreach ($list as &$item) {
            $tidAttr = $javaApi->queryTicketAttrsById($item['tid']);
            $needNo  = 1;
            foreach ($tidAttr as $ext) {
                if ($ext['key'] == 'annual_identity_info') {
                    $needNo = $ext['val'];
                }
            }
            $item['need_id']         = $needNo;
            $item['family_card_num'] = $family_card_num == 0 ? 1 : $family_card_num;
        }

        return $list;
    }

    /**
     * 详情页-相关套票
     */
    public function getRelatedPackage()
    {
        $lid = I('lid', '', 'intval');

        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $option['channel'] = 1;

        $recommendListArr = ProductApi::getRecommendProductList($this->_supplyId, $lid, $option);

        $return = [];
        if ($recommendListArr['code'] == 200) {
            foreach ($recommendListArr['data'] as $item) {
                $return[] = [
                    'ticket'  => $item['item_name'] . $item['name'],
                    'pid'     => $item['product_id'],
                    'aid'     => $item['superior_id'],
                    'jsprice' => $item['retail_price'] / 100,
                    'tprice'  => $item['counter_price'] / 100,
                    'tid'     => $item['id'],
                ];
            }
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 获取景区门票列表
     *
     * @param  int $lid 景区id
     *
     * @return [type]      [description]
     */
    private function _getLandTickets($lid, $channel = '', $operId = '', $tids = '')
    {
        if (!$lid) {
            return [];
        }
        // 获取景区下的门票列表
        $ticketBiz   = new bizTicket();
        $ticketsList = $ticketBiz->getRetailTicketList($this->_supplyId, $lid, $channel, $operId, true, $tids,
            0);

        if ($ticketsList['code'] != 200) {
            return [];
        }

        return $ticketsList['data'];
    }

    /**
     * 仅供微票房使用，确认订单页数据获取
     * Create by zhangyangzhen
     * Date: 2019/1/31
     * Time: 14:31
     *
     * @param  int $ticket_id 票ID
     * @param  string $type 产品类型
     * @param  int $land_id 景区ID
     */
    public function getWPFBookInfo()
    {
        header("Access-Control-Allow-Origin: *"); // 允许任意域名发起的跨域请求
        $ticketId = I('post.ticket_id', '', 'intval');
        $type     = I('post.type', '', 'strval');
        $landId   = I('post.land_id', '', 'intval');
        $memberId = $this->isLogin();

        if ($ticketId == '' || $landId == '') {
            $this->apiReturn(self::CODE_PARAM_ERROR, '', '参数错误');
        }

        $requestTicket = 0;

        //这个三种类型需要返回当前门票即可
        if (in_array($type, ['C', 'H', 'F'])) {
            $requestTicket = $ticketId;
        }

        //获取门票数据
        $data = TicketApi::getBookTickets($memberId, $this->_supplyId, $landId, $requestTicket, self::__APP_CHANNEL__);

        if ($data['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($data['code'], '', $data['sub_msg']);
        }

        $ticketData = $data['data'];

        if (empty($ticketData)) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '产品未通过审核或无权限购买');
        }

        $ticketData = OrderBook::handleWPTBookTicket($ticketData, $ticketId, $memberId, $this->_supplyId,
            self::__APP_CHANNEL__);

        if ($ticketData[0] != 0) {
            $this->apiReturn($ticketData[0], [], $ticketData[1]);
        }

        $ticketData = $ticketData[1];

        $memberMoney = MemberMoney::getAllMoney($memberId, $this->_supplyId, true);

        $responseData = [
            'status'      => 'success',
            'capital'     => $memberMoney,
            'account_pay' => 0,
            'land'        => [
                'lid'       => $ticketData['lid'],
                'pay'       => $ticketData['pay'],
                'ltitle'    => $ticketData['land_name'],
                'p_type'    => $ticketData['p_type'],
                'memberId'  => $memberId,
                'begintime' => $ticketData['begin_time'],
                'tickets'   => $ticketData['tickets'],
            ],
        ];

        $this->apiReturn(self::CODE_SUCCESS, $responseData, 'success');
    }

    /**
     * 预订页面-景区以及相关订单信息
     */
    public function getBookInfo()
    {
        header("Access-Control-Allow-Origin: *"); // 允许任意域名发起的跨域请求
        //获取预定须知的类型
        $type = I('type', 'common', 'strval');

        switch ($type) {
            case 'common':
                //共性产品
                $this->_getBookInfoForCommon();
                break;

            case 'seckill':
                //抢购
                $this->getBookInfoForSeckill();
                break;

            case 'special':
                //特产产品
                $this->_getBookInfoForSpecial();
                break;
            case 'cut_price':
                //砍价
                $this->_getBookInfoForCutPrice();
                break;
            case 'group':
                $this->_getBookInfoForGroup();
            default:
                $this->apiReturn(204, [], '预定类型错误');
                break;
        }
    }

    /**
     *  共性产品的预定信息(景区，线路，酒店等等)
     * <AUTHOR>
     * @date   2017-12-08
     */
    private function _getBookInfoForCommon()
    {
        // 根据平台规则重新写
        $tid    = I('tid', '', 'intval');
        $type   = I('type', '', 'strval');
        $landId = I('lid', '', 'intval');
        $aid    = I('aid', '', 'intval');
        $pid    = I('pid', '', 'intval');

        //页面上面选定的日期
        $date = I('date', '', 'strval');
        $date = strtotime($date) ? date('Y-m-d', strtotime($date)) : false;

        if ($pid < 1 || $aid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $bookInfo = $this->_getBookInfo($pid, $aid, $tid);
        // 没传tid 的特殊处理
        if (!$tid) {
            $tid = $bookInfo['tid'];
        }
        $tickets = $this->_getBookList($tid, $bookInfo['startDate'], $aid);

        if (!$tickets) {
            $this->apiReturn(204, [], '票类不存在');
        }

        if (!empty($tickets)) {
            $tickets = array_key($tickets, 'pid');
            foreach ($tickets as $key => $value) {
                if ($value['pid'] == $pid) {
                    $mainTicket = $value;
                    unset($tickets[$key]);
                    array_unshift($tickets, $mainTicket);
                }
            }
        }

        $bookInfo['tickets'] = array_values($tickets);

        //获取价格的日期
        $startDate = $date ? $date : date('Y-m-d', strtotime($bookInfo['startDate']));
        $ticketIds = array_column($bookInfo['tickets'], 'tid');
        $ticketIds = implode(',', $ticketIds);
        $priceData = TicketApi::getSinglePrices($ticketIds, $startDate);

        foreach ($bookInfo['tickets'] as $key => $val) {
            if (!isset($priceData[$val['tid']])) {
                continue;
            }
            $bookInfo['tickets'][$key]['tprice']       = $priceData[$val['tid']]['counter_price'] / 100;
            $bookInfo['tickets'][$key]['jsprice']      = $priceData[$val['tid']]['window_price'] / 100;
            $bookInfo['tickets'][$key]['retail_price'] = $priceData[$val['tid']]['retail_price'] / 100;
        }

        if (!$_SESSION['memberID'] || $_SESSION['identify'] == 'allDis') {
            $bookInfo['alldis'] = 1;
        } else {
            $bookInfo['alldis'] = 0;
        }

        $this->apiReturn(200, $bookInfo);
    }

    /**
     * 抢购预定页面接口
     * <AUTHOR>
     * @date   2017-10-31
     */
    public function getBookInfoForSeckill()
    {
        $pid = I('pid', '', 'intval');
        $aid = I('aid', '', 'intval');

        if ($pid < 1 || $aid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        //抢购业务接口
        $seckillBiz = new \Business\Wechat\Seckill();
        $cacheKey   = $seckillBiz->createCacheKey($pid, 'bookinfo');

        $redisObj  = Cache::getInstance('redis');
        $cacheData = $redisObj->get($cacheKey);
        $cacheData = [];
        if (!$cacheData) {
            //当前票类是否存在抢购活动
            $getRes = $seckillBiz->getRecentSeckillForPid([$pid]);

            if ($getRes['code'] != 200) {
                $this->apiReturn(204, [], '抢购活动获取失败');
            }
            if (!isset($getRes['data'][$pid])) {
                $this->apiReturn(202, [], '未获取到抢购活动信息');
            }
            $seckill = $getRes['data'][$pid];
            //获取最近周期的开始和结束时间
            $cycleRes = $seckillBiz->parseCycle($seckill['id']);

            if ($cycleRes['code'] != 200) {
                $this->apiReturn(204, [], '抢购周期解析失败');
            }

            $cycle = $cycleRes['data'];
            //预定信息
            $bookInfo = $this->_getBookInfo($pid, $aid);

            if (date('Y-m-d', $cycle['begin']) > $bookInfo['startDate']) {
                $bookInfo['startDate'] = date('Y-m-d', $cycle['begin']);
            }

            //票类列表
            $tickets = $this->_getBookList($bookInfo['tid'], $bookInfo['startDate'], $aid);
            if (!$tickets) {
                $this->apiReturn(204, [], '票类不存在');
            }
            //抢购不能下联票
            foreach ($tickets as $key => $item) {
                if ($item['pid'] != $pid) {
                    unset($tickets[$key]);
                }
            }

            $bookInfo['tickets'] = array_values($tickets);
            $seckillInfo         = [
                'id'        => $seckill['id'],
                'begin'     => $cycle['begin'],
                'end'       => $cycle['end'],
                'price'     => $seckill['price'],
                'buy_limit' => (int)$seckill['buy_limit'],
            ];

            $bookInfo['seckill_info'] = $seckillInfo;
            $redisObj->set($cacheKey, json_encode($bookInfo), '', 3600);
        } else {
            $bookInfo = json_decode($cacheData, true);
        }

        $seckill = $bookInfo['seckill_info'];
        //实时抢购信息
        $getRes = $seckillBiz->getRealtimeSeckillInfo($seckill['id'], $seckill['begin'], $seckill['end']);
        if ($getRes['code'] != 200) {
            $this->apiReturn(204, [], '抢购活动不存在');
        }

        $realtimeInfo = $getRes['data'];
        //获取周期key
        $cycleKey                            = $seckillBiz->generateCycleKey($seckill['begin'], $seckill['end']);
        $bookInfo['seckill_info']['storage'] = $realtimeInfo[$cycleKey];
        $this->apiReturn(200, $bookInfo);

    }

    /**
     * 特产产品获取预定信息
     * <AUTHOR>
     * @date   2017-12-08
     */
    private function _getBookInfoForSpecial()
    {
        $lid = I('lid', 0, 'intval');

        if (!$lid) {
            $this->apiReturn(204, [], '参数错误');
        }

        //获取产品信息
        $landModel = new Land('slave');

        $landInfo = $landModel->getLandInfo($lid, false, 'p_type,bhjq,title,area,imgpath,imgpathGrp');

        $landInfo['bhjq'] = htmlspecialchars_decode($landInfo['bhjq']);
        $landInfo['bhjq'] = \image_opt($landInfo['bhjq'], 600);

        if ($landInfo['p_type'] != 'J') {
            $this->apiReturn(204, [], '产品类型错误');
        }

        $return = [];
        //获取规格(门票)信息
        $tickets = $this->_getLandTickets($lid);

        if ($tickets) {
            $ticModel = new Ticket('slave');
            $pidArr   = array_column($tickets, 'product_id');
            //获取零售价
            $retailMap = $ticModel->getMuchRetailPrice($pidArr);
            $minPrice  = $maxPrice = 0;

            if ($retailMap) {
                //零售价格范围
                $minPrice = min($retailMap) * 100;
                $maxPrice = max($retailMap) * 100;
            }

            //获取门市价
            $tidArr = array_column($tickets, 'id');
            $aidArr = [];
            foreach ($tickets as $key => $value) {
                if (!empty($value['aid'])) {
                    $aidArr[] = $value['superior_id'];
                }
            }
            $ticketServiceApi = new \Business\JavaApi\Product\Ticket();
            $ticketParamList  = [];

            foreach ($tickets as $key => $value) {
                if (empty($value['superior_id'])) {
                    $this->apiReturn(203, [], "aid不能为空");
                }
                $ticketParamList[] = [
                    'ticketId' => $value['id'],
                    'sid'      => $value['superior_id'],
                    'fid'      => $this->_supplyId,
                    'channel'  => $this->saleChannel,
                ];
            }
            $tpriceArr = $ticketServiceApi->batchQueryTicketByIds($ticketParamList);
            if ($tpriceArr['code'] != 200) {
                $this->apiReturn($tpriceArr['code'], [], "获取门票信息失败");
            }
            $tpriceArr = $tpriceArr['data'];

            $orginMinPrice = $orginMaxPrice = 0;

            if ($tpriceArr) {
                //门市价范围
                $tpriceAll     = array_column($tpriceArr, 'tprice');
                $orginMinPrice = min($tpriceAll) * 100;
                $orginMaxPrice = max($tpriceAll) * 100;
            }

            //轮播图
            if ($landInfo['imgpathGrp']) {
                $tmpImgpathGrp = @json_decode($landInfo['imgpathGrp'], true);
                $imggroup      = is_array($tmpImgpathGrp) ? $tmpImgpathGrp : unserialize($landInfo['imgpathGrp']);
            } else {
                $imggroup = [$landInfo['imgpath']];
            }

            $return['title']         = $landInfo['title'];
            $return['imgpath']       = $landInfo['imgpath'];
            $return['imggroup']      = $imggroup;
            $return['bhjq']          = $landInfo['bhjq'];
            $return['area']          = $landInfo['area'];
            $return['max_price']     = $maxPrice;
            $return['min_price']     = $minPrice;
            $return['ori_max_price'] = $orginMaxPrice;
            $return['ori_min_price'] = $orginMinPrice;

            //获取库存
            $storageMap = $ticModel->getMuchStorage($pidArr);

            //获取特产产品规格信息
            $specialBiz = new Specialty();
            $sizeRes    = $specialBiz->getSpecification($tidArr, $this->_supplyId);

            if ($sizeRes['code'] != 200) {
                $this->apiReturn(204, [], $sizeRes['msg']);
            }

            if (!$sizeRes['data']) {
                $this->apiReturn(204, [], '未获取到产品规格信息');
            }

            $sizes = $sizeRes['data'];

            $tags = [];
            $imgs = [];
            foreach ($sizes as $item) {
                foreach ($item['spec_params'] as $val) {

                    if (isset($tags[$val['tag']])) {
                        if (!in_array($val['value'], $tags[$val['tag']])) {
                            $tags[$val['tag']][] = $val['value'];
                        }
                    } else {
                        $tags[$val['tag']][] = $val['value'];
                    }
                    //规格图片
                    if (isset($val['img'])) {
                        $imgs[$val['value']] = $val['img'];
                    }
                }
            }

            $return['tags'] = $tags;
            $return['imgs'] = $imgs;

            foreach ($tickets as $item) {

                $tmp = $sizes[$item['id']];

                $express = $tmp['express_pay_way'];
                $store   = $storageMap[$item['product_id']] >= -1 ? $storageMap[$item['product_id']] : 0;

                if (isset($retailMap[$item['product_id']])) {
                    $price = $retailMap[$item['product_id']] * 100;
                } else {
                    $price = 0;
                }

                $return['list'][] = [
                    'tid'             => $item['id'],
                    'pid'             => $item['product_id'],
                    'title'           => $item['name'],
                    'aid'             => $item['superior_id'],
                    'express'         => $express,
                    'store'           => $store,
                    'tags'            => $tmp['spec_params'],
                    'price'           => $price,
                    'ori_price'       => $tpriceArr[$item['id']]['tprice'] * 100,
                    'checked_express' => $tmp['checked_express'],
                    'special_dot'     => $tmp['pick_up_point'],
                ];
            }
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 砍价预定信息
     * <AUTHOR>
     * @date   2018-03-16
     */
    private function _getBookInfoForCutPrice()
    {

        $pid = I('pid', '', 'intval');
        $aid = I('aid', '', 'intval');
        //砍价发起记录id
        $joinId = I('join_id', 0, 'intval');

        if ($pid < 1 || $aid < 1 || $joinId < 1) {
            $this->apiReturn(204, [], '参数错误');
        }
        //获取砍价发起记录详情
        $cutBiz  = new \Business\Wechat\CutPrice();
        $joinRes = $cutBiz->getJoinInfo($joinId);
        if ($joinRes['code'] != 200) {
            $this->apiReturn(204, [], '未找到砍价发起记录');
        }
        $joinInfo = $joinRes['data'];
        $cutId    = $joinInfo['cut_id'];
        //是否在允许周期内
        if (time() < $joinInfo['begin'] || time() > $joinInfo['end']) {
            $this->apiReturn(204, [], '已超出砍价下单日期');
        }
        //获取砍价活动信息
        $getRes = $cutBiz->getActivity(0, $cutId);
        if ($getRes['code'] != 200) {
            $this->apiReturn(204, [], '砍价活动不存在');
        }
        $activity = $getRes['data'];
        //当前金额
        $nowPrice = $activity['market_price'] - $joinInfo['cut_money'];
        //预定信息
        $bookInfo = $this->_getBookInfo($pid, $aid);

        if (date('Y-m-d', $joinInfo['begin']) > $bookInfo['startDate']) {
            $bookInfo['startDate'] = date('Y-m-d', $joinInfo['begin']);
        }
        //票类列表
        $tickets = $this->_getBookList($bookInfo['tid'], $bookInfo['startDate'], $aid);
        if (!$tickets) {
            $this->apiReturn(204, [], '票类不存在');
        }

        $orderBook                   = new \Business\Order\OrderBook();
        $pftReserveStorageServiceApi = new \Business\JavaApi\Product\ReserveStorage();
        //砍价不能下联票
        foreach ($tickets as $key => $item) {

            $preTicketInfo               = [];
            $ticketReserveStorageContext = new TicketReserveStorageContext([
                'tid' => $item['tid'],
            ]);
            $preTicketInfoRes            = $pftReserveStorageServiceApi->query($ticketReserveStorageContext);
            if ($preTicketInfoRes['code'] == 200) {
                $preTicketInfo = $preTicketInfoRes['data'];
            }
            $tickets[$key]['reserve_storage'] = $preTicketInfo;

            if ($item['pid'] != $pid) {
                unset($tickets[$key]);
                continue;
            }
            $tickets[$key]['jsprice']                 = $nowPrice;
            $tickets[$key]['tprice']                  = $activity['market_price'];
            $tickets[$key]['buy_up']                  = 1;
            $tickets[$key]['more_credential_content'] = $item['more_credential_content'] ?? [];
            $tickets[$key]['auto_sale_rule']          = $item['auto_sale_rule'] ?? [];
            $tickets[$key]['auto_sale_rule_status']   = $item['auto_sale_rule_status'] ?? true;
            $tickets[$key]['auto_sale_rule_status']   = $item['auto_sale_rule_status'] ?? true;
            //套票的有效期文本单独处理
            $validTagResult = $orderBook->getValidityDate($item);
            $validTag       = $validTagResult;

            $bookInfo['validTag'] = $validTag;

            $tickets[$key]['tagArr'] = $this->getTagArr($item['tid']);
        }
        $bookInfo['ticketChangingRange']   = $bookInfo['ticket_changing_audit'];
        $bookInfo['ticketChangingAudit']   = $bookInfo['ticket_changing_range'];
        $bookInfo['ticketChangingWeekend'] = $bookInfo['ticket_changing_weekend'];
        $bookInfo['tickets']               = array_values($tickets);
        $bookInfo['activity_id']           = $activity['id'];

        $this->apiReturn(200, $bookInfo);
    }

    /**
     *  获取标签组
     * <AUTHOR>
     * @date 2020/8/25
     *
     * @param  int $tid 门票id
     *
     * @return array
     */
    public function getTagArr(int $tid)
    {

        $ticketBiz  = new \Business\JavaApi\CommodityCenter\Ticket();
        $ticketBz   = new \Business\Product\Ticket();
        $ticketInfo = $ticketBiz->queryTicketInfoByIds([$tid]);
        $tagArr     = [];
        if ($ticketInfo['code'] == 200) {
            $ticketInfo = $ticketInfo['data'][0];
            unset($ticketInfo['land_f']['p_type']);
            $tempTicketInfo = array_merge($ticketInfo['uuJqTicketDTO'],
                $ticketInfo['uuLandDTO'], $ticketInfo['confs'], $ticketInfo['uuLandFDTO']);
            $tagArr         = [
                'ticket_introduction' => $ticketBz->ticketExplainTag($tempTicketInfo),
                'advance_order'       => $ticketBz->advanceBookingTag($tempTicketInfo),
                'validity_rule'       => $ticketBz->expiryTime($tempTicketInfo),
                'verify_rule:'        => $ticketBz->checkRuleTag($tempTicketInfo),//
                'refund_rule'         => $ticketBz->refundRuleTag($tempTicketInfo),
                'change_rule'         => $ticketBz->ticketChangingTag($tempTicketInfo),//
                'refund_money'        => $ticketBz->refundFeeTag($tempTicketInfo),
            ];
        }

        return $tagArr;
    }

    /**
     * 拼团预订信息
     * <AUTHOR>
     * @date   2018-05-29
     */
    private function _getBookInfoForGroup()
    {
        $pid = I('pid', '', 'intval');
        $aid = I('aid', '', 'intval');
        //拼团活动id
        $groupId = I('group_id', 0, 'intval');
        //团id
        $openId = I('open_id', 0, 'intval');

        if ($pid < 1 || $aid < 1 || $groupId < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $groupBiz = new \Business\Wechat\GroupBooking();
        $getRes   = $groupBiz->getOneActivity($groupId);
        if ($getRes['code'] != 200) {
            $this->apiReturn(204, [], $getRes['msg']);
        }
        $activity = $getRes['data'];

        if (time() < $activity['begin'] || time() > $activity['end']) {
            $this->apiReturn(204, [], '不在拼团时间范围内');
        }

        //预定信息
        $bookInfo = $this->_getBookInfo($pid, $aid);
        if (date('Y-m-d', $activity['begin']) > $bookInfo['startDate']) {
            $bookInfo['startDate'] = date('Y-m-d', $activity['begin']);
        }

        //票类列表
        $tickets = $this->_getBookList($bookInfo['tid'], $bookInfo['startDate'], $aid);
        if (!$tickets) {
            $this->apiReturn(204, [], '票类不存在');
        }
        //拼团不能下联票
        foreach ($tickets as $key => $item) {
            if ($item['pid'] != $pid) {
                unset($tickets[$key]);
                continue;
            }
            $price = $activity['price'];
            if (!$openId) {
                $priceRes = $groupBiz->parseLeaderPrice($activity['price'], $activity['discounts_type'],
                    $activity['discounts_money']);
                if ($priceRes['code'] == 200) {
                    $price = $priceRes['data'];
                }
            }
            $tickets[$key]['jsprice'] = round($price);
            $tickets[$key]['buy_up']  = $activity['limit_buy'] == -1 ? 0 : (int)$activity['limit_buy'];
        }
        $bookInfo['tickets'] = array_values($tickets);
        $this->apiReturn(200, $bookInfo);
    }

    /**
     * 预定页面-门票列表
     * @param  int $tid 门票id
     * @param  string $date 可预订日期
     *
     * @return array
     */
    private function _getBookList($tid, $date, $aid)
    {
        // java 接口获取门票信息
        $product    = [];
        $ticketBiz  = new \Business\CommodityCenter\Ticket();
        $ticketData = $ticketBiz->fieldConversion($ticketBiz->queryTicketInfoById($tid, '', '', '', '', false, $aid,
            $this->_supplyId, $this->saleChannel));
        //$ticketApi         = new TicketApi();
        //$ticketData        = $ticketApi->getTickets($tid, $this->_supplyId, $this->saleChannel, $aid);
        $product['id']     = $ticketData['id'];
        $product['landid'] = $ticketData['item_id'];
        // 获取个景点信息
        $productBiz  = new bizProduct();
        $landInfoArr = $productBiz->getProductInfo($this->_supplyId, $ticketData['item_id'], 0);
        $ptype       = $landInfoArr['data']['p_type'];
        $tickets     = $this->_getLinkProduct($product['id'], $product['landid'], $ptype, $date);

        if (!$tickets) {
            return [];
        }

        if ($this->_allDisMan) {
            $tickets = AllDisProcess::getAllDisPrice($this->_supplyId, $tickets, 'book');
        }

        if ($ptype == 'F') {
            //获取子票列表
            $tickets = $this->_parseSonTickets($tickets);
        }

        if ($ptype == 'I') {
            //解析是否需要身份证
            $tickets = $this->_parseCertLimit($tickets, $ticketData['family_card_num']);
        }

        if ($ptype == 'H') {
            //获取演出区域id
            $tickets = $this->_getZoneId($tickets);

            $tidArr      = array_column($tickets, 'tid');
            $zoneMapping = (new \Model\Product\Ticket())->getZoneName($tidArr);

            foreach ($tickets as &$item) {
                $item['zone_name'] = $zoneMapping[$item['tid']]['zone_name'];
                $item['zone_id']   = $zoneMapping[$item['tid']]['id'];
            }
        }

        return $tickets;
    }

    /**
     * 景区以及相关订单信息
     *
     * @param  [type] $pid 产品id
     *
     * @return [type]      [description]
     */
    private function _getBookInfo($pid = '', $aid, $tid = '')
    {
        // 同时为空就有问题了
        if (!$pid && !$tid) {
            return [];
        }

        $Ticket    = new Ticket('slave');
        $ticketBiz = new \Business\Product\Ticket();
        //$ticketApi = new TicketApi();

        // 如果tid为空 pid 有值要进行一层转换
        if ($pid && $tid == '') {
            $tidPidArr = $Ticket->getTicketInfoByPid($pid, 'id,pid');
            $tid       = $tidPidArr['id'];
        }

        $ticketApi  = new \Business\CommodityCenter\Ticket();
        $ticketData = $ticketApi->fieldConversion($ticketApi->queryTicketInfoById($tid, '', '', '', '', false, $aid,
            $this->_supplyId, $this->saleChannel));
        //$ticketData = $ticketApi->getTickets($tid, $this->_supplyId, $this->saleChannel, $aid);
        $cancelCost = json_decode($ticketData['refund_stair'], true) ?: [];

        $bookInfo = [
            'refund_rule'             => $ticketData['refund_rule'],
            'refund_early_time'       => $ticketData['refund_early_minu'],
            'refund_after_time'       => $ticketData['refund_after_time'] ?? 0,
            'reb'                     => $ticketData['refund_value'],
            'reb_type'                => $ticketData['refund_type'],
            'cancel_cost'             => $cancelCost,
            'batch_check'             => $ticketData['verify_way'],
            'batch_day'               => $ticketData['verify_limit_amount'],
            'ticket_changing_audit'   => $ticketData['ticket_changing_audit'],
            'ticket_changing_range'   => $ticketData['ticket_changing_range'],
            'ticket_changing_weekend' => $ticketData['ticket_changing_weekend'],
            //最近可售日期
            'startDate'               => $ticketBiz->getHasRetailPriceDate($pid, 'min'),
            'tid'                     => $ticketData['id'],
            'lid'                     => $ticketData['item_id'],
        ];

        //提前预定天数
        if ($ticketData['preorder_early_days']) {
            $preDate = date('Y-m-d', strtotime("+{$ticketData['preorder_early_days']} days"));
            if ($preDate > $bookInfo['startDate']) {
                $bookInfo['startDate'] = $preDate;
            }
        }

        //有效时间
        $validTime = '';
        if (strtotime($ticketData['valid_period_start']) > 0) {
            //时间段内有效
            $tmpS = date('Y-m-d', strtotime($ticketData['valid_period_start']));
            $tmpE = date('Y-m-d', strtotime($ticketData['valid_period_end']));

            if ($tmpS > date('Y-m-d')) {
                $bookInfo['startDate'] = $tmpS;
            }
            if ($ticketData['valid_period_timecancheck']) {
                $tmpS = '随游玩日期变化';
            }
            $validTime = $tmpS . '~' . $tmpE;
        } elseif ($ticketData['valid_period_days']) {
            //多少天内有效
            $validTime = $ticketData['valid_period_days'];
        } else {
            //当天有效
            $validTime = 0;
        }
        $bookInfo['validTime'] = (string)$validTime;
        $bookInfo['validType'] = $ticketData['valid_period_type'];

        if ($ticketData['preorder_expire_time'] && in_array($bookInfo['startDate'], [date('Y-m-d')])) {
            //需要在几点前预定
            if (date('H:i:s') > $ticketData['preorder_expire_time']) {
                //预定日期+1
                $bookInfo['startDate'] = date('Y-m-d', strtotime($bookInfo['startDate']) + 3600 * 24);
            }
        }

        $bookInfo['needID'] = (int)$ticketData['identity_info'];
        if ($ticketData['p_type'] == 'I') {
            //年卡身份证信息  0 非必填 1激活必填 2下单必填
            $annualIdentityInfo = isset($ticketData['annual_identity_info']) ? (int)$ticketData['annual_identity_info'] : 0;
            $bookInfo['needID'] = $annualIdentityInfo;
        }

        //可验证时间
        $verifyTime = '';
        if ($ticketData['verify_time'] == 0) {

            $verifyMap = [1, 2, 3, 4, 5, 6, 7];
            //按星期验证
            if ($ticketData['verify_disable_week']) {
                $verifyTime = array_diff($verifyMap, explode(',', $ticketData['verify_disable_week']));
                foreach ($verifyTime as &$item) {
                    $item = $item == 0 ? 6 : $item - 1;
                }
                $verifyTime = array_values($verifyTime);
            } else {
                //不限制验证时间
                $verifyTime = -1;
            }

        } else {
            //时间段内有效
            $vTimeLimit = $ticketData['verify_time'];
            $verifyTime = str_replace('|', '~', $vTimeLimit);
        }
        $bookInfo['verifyTime'] = $verifyTime;

        // 获取个景点信息
        $productBiz         = new bizProduct();
        $landInfoArr        = $productBiz->getProductInfo($this->_supplyId, $ticketData['item_id'], 0);
        $bookInfo['title']  = $landInfoArr['data']['title'];
        $bookInfo['p_type'] = $landInfoArr['data']['p_type'];

        //线路获取集合地点
        if ($landInfoArr['data']['p_type'] == 'B') {
            $bookInfo['assStation'] = json_decode($ticketData['assembling_place'], true) ?: [];
            $bookInfo['assStation'] = array_values($bookInfo['assStation']);
        }

        // 额外填写更多信息
        $ticketExtendAttr    = new TicketExtendAttr();
        $ticketSpecialValRes = $ticketExtendAttr->queryTicketSpecialVal([$tid],
            ['more_credentials', 'auto_sale_rule']);
        $ticketSpecialMap    = [];
        if ($ticketSpecialValRes['code'] == 200) {
            // key => item
            foreach ($ticketSpecialValRes['data'] as $specialValItem) {
                $ticketSpecialMap[$specialValItem['ticketId']][$specialValItem['key']] = $specialValItem['val'];
            }
        }
        $ticketSpecialData = isset($ticketSpecialMap[$ticketData['id']]) ? $ticketSpecialMap[$ticketData['id']] : [];

        if ($ticketData['pType'] == 'A') {
            //填写更多信息游客取票人证件
            $bookInfo['more_credential_content'] = isset($ticketSpecialData['more_credentials']) ? json_decode($ticketSpecialData['more_credentials'],
                true) : [];
            //自动起售停售
            $bookInfo['auto_sale_rule'] = isset($ticketSpecialData['auto_sale_rule']) ? json_decode($ticketSpecialData['auto_sale_rule'],
                true) : [];

            $bookInfo['auto_sale_rule_status'] = true;
            if ($bookInfo['auto_sale_rule']) {
                $time = time();
                if (strtotime($bookInfo['auto_sale_rule']['start']) > $time || strtotime($bookInfo['auto_sale_rule']['end']) < $time) {
                    $bookInfo['auto_sale_rule_status'] = false;
                } else {
                    $bookInfo['auto_sale_rule_status'] = true;
                }
            }
        }

        return $bookInfo;
    }

    /**
     * 获取联票产品
     *
     * @param  int $tid 门票id
     * @param  int $landid 景区id
     * @param  string $type 景区类型
     * @param  string $date 可预订日期
     *
     * @return array
     */
    private function _getLinkProduct($tid, $landid, $type, $date)
    {
        $inWechatSmApp = $this->inWechatSmallApp();
        $channelVal    = $this->checkChannel($inWechatSmApp);
        $tickets       = $this->_getLandTickets($landid, $channelVal);

        $unLinkType = ['C', 'H', 'I', 'J']; //不能下联票

        $tidArr = [];
        foreach ($tickets as $ticket) {
            if (in_array($type, $unLinkType) && $ticket['id'] != $tid) {
                continue;
            }
            $tidArr[] = $ticket['id'];
        }

        $orderUnityBusiness = new \Business\Order\OrderUnity;
        //获取可以联票的门票id
        $idArrayInfo = $orderUnityBusiness->linkProductFilterCommon($tid, $tidArr, 'mobile');
        if ($idArrayInfo['code'] == 200) {
            $linkTidArr = $idArrayInfo['data'];
        } else {
            $linkTidArr = [];
        }

        $return    = [];
        $ticketBiz = new \Business\Product\Ticket();

        foreach ($tickets as $key => $item) {
            // 属性不一致无法联票
            if (!in_array($item['id'], $linkTidArr)) {
                continue;
            }

            $pos      = array_search($item['id'], $idArrayInfo['data']);
            $tmpPrice = $item['retail_price'];

            if (\inWechatSmallApp()) {
                $tmpPrice = $item['window_price'] ?: $item['counter_price'];
            }

            $tagArr = $ticketBiz->refundRuleTag($item);

            $return[$pos] = [
                'title'         => $item['name'],
                'jsprice'       => $tmpPrice / 100,
                'tprice'        => $item['counter_price'] / 100,
                'pid'           => $item['product_id'],
                'tid'           => $item['id'],
                'aid'           => $item['superior_id'],
                'buy_low'       => (int)$item['buy_min_amount'],
                'buy_up'        => (int)$item['buy_max_amount'],
                'retail_price'  => $item['retail_price'] / 100,
                'age_limit_max' => $item['age_limit_max'] ? (string)$item['age_limit_max'] : '',
                'age_limit_min' => $item['age_limit_min'] ? (string)$item['age_limit_min'] : '',
                'refundRuleStr' => $tagArr ?? '',
                'pre_sale'      => $item['ticketDetail']['pre_sale'],
                'need_name'     => (int)$item['ticketDetail']['ext']['need_name'] ?? 1,
                'need_mobile'   => (int)$item['ticketDetail']['ext']['need_mobile'] ?? 1,
            ];
        }
        ksort($return);

        return array_values($return);
    }

    /**
     * 预定页面-日历价格
     * @return [type] [description]
     */
    public function getCalendarPrice()
    {
        $pid  = I('pid', '', 'intval');
        $date = I('date', date('Y-m'));
        $aid  = I('aid', '', 'intval');
        $tid  = I('tid', '', 'intval');

        if (!$pid || !$date) {
            $this->apiReturn(204, [], '参数错误');
        }

        // 计算请求的其实时间和结束时间
        $startDate = $date . '-01';
        $endDate   = $date . date('-t', strtotime($date));

        $calendar = TicketApi::getrRetailCalendar($this->_supplyId, $tid, $startDate, $endDate);
        $priceset = [];
        foreach ($calendar as $key => $priceVal) {
            $priceset[$priceVal['time']] = $priceVal['lPrice'];
            if (\inWechatSmallApp()) {
                // 取的是窗口价 或 门市价
                $priceset[$priceVal['time']] = $priceVal['windowPrice'] ?: $priceVal['mPrice'];
            }
        }

        //单位转换成为元
        $priceset = array_map(function ($val) {
            return $val / 100;
        }, $priceset);

        if ($this->_allDisMan) {
            $tmp      = [
                'pid'  => $pid,
                'aid'  => $aid,
                'list' => $priceset,
            ];
            $priceset = AllDisProcess::getAllDisPrice($this->_supplyId, $tmp, 'calendar');
            $priceset = $priceset['list'];
        }

        $this->apiReturn(200, $priceset);
    }

    /**
     * 抢购日历
     * <AUTHOR>
     * @date   2018-02-06
     */
    public function seckillCalendar()
    {
        //抢购id
        $seckillId = I('seckill_id', 0, 'intval');
        //门票pid
        $pid = I('pid', 0, 'intval');
        //上级供应商id
        $date = I('date', date('Y-m-d'));

        //获取最近周期的开始和结束时间
        $seckillBiz = new \Business\Wechat\Seckill();
        $cacheKey   = $seckillBiz->createCacheKey("{$pid}_{$date}", 'calendar');

        //缓存是否存在
        $redisObj  = Cache::getInstance('redis');
        $cacheData = $redisObj->get($cacheKey);
        //获取最近周期的开始和结束时间

        if (!$cacheData) {
            //获取抢购活动信息
            $getRes = $seckillBiz->getSeckill($seckillId);
            if ($getRes['code'] != 200) {
                $this->apiReturn(204, [], '抢购活动不存在');
            }

            $seckill = $getRes['data'];
            //开始时间
            $begin = date('Y-m-d', $seckill['begin']);
            //结束时间
            $end       = date('Y-m-d', $seckill['end']);
            $beginDate = date('Y-m-d', strtotime($date));
            $tmp       = date('Y-m-01', strtotime($date));
            $endDate   = date('Y-m-d', strtotime("$tmp +1 month -1 day"));
            $tickModel = new Ticket('slave');
            $ticketBiz = new \Business\Product\Ticket();
            //有价格的日期
            $priceset = $ticketBiz->getRangeRetailPrice($beginDate, $endDate, $pid);
            //期票判断
            $ticket = $tickModel->getTicketInfoByPid($pid, 'order_start,order_end');
            if ($ticket['order_start'] && $ticket['order_end'] && $ticket['order_start'] != '0000-00-00 00:00:00') {
                $orderStart = date('Y-m-d', strtotime($ticket['order_start']));
                $orderEnd   = date('Y-m-d', strtotime($ticket['order_end']));
                $beginDate  = $orderStart;
                $endDate    = $orderEnd;
            }

            $beginDate = $beginDate >= date('Y-m-d') ? $beginDate : date('Y-m-d');
            $dateSet   = [];
            if ($priceset) {
                foreach ($priceset as $key => $item) {
                    if ($key >= $beginDate && $key <= $endDate) {
                        $dateSet[] = $key;
                    }
                }
            }

            $redisObj->set($cacheKey, json_encode($dateSet), '', 3600);
        } else {
            $dateSet = json_decode($cacheData, true);
        }

        $this->apiReturn(200, $dateSet);
    }

    /**
     * 砍价日历
     * <AUTHOR>
     * @date   2018-03-16
     */
    public function cutPriceCalendar()
    {

        $pid  = I('pid', '', 'intval');
        $date = I('date', date('Y-m-d'));
        $aid  = I('aid', '', 'intval');

        if (!$pid || !$date) {
            $this->apiReturn(204, [], '参数错误');
        }

        $this->_priceCalendar($pid, $aid, $date);
    }

    /**
     * 获取拼团日历
     * <AUTHOR>
     * @date   2018-05-29
     */
    public function groupBookingCalendar()
    {

        $pid  = I('pid', '', 'intval');
        $date = I('date', date('Y-m-d'));
        $aid  = I('aid', '', 'intval');

        if (!$pid || !$date) {
            $this->apiReturn(204, [], '参数错误');
        }

        $this->_priceCalendar($pid, $aid, $date);
    }

    private function _priceCalendar($pid, $aid, $date)
    {

        $beginDate = date('Y-m-d', strtotime($date));
        $tmp       = date('Y-m-01', strtotime($date));
        $endDate   = date('Y-m-d', strtotime("$tmp +1 month -1 day"));

        $tickModel = new \Business\Product\Ticket();
        $priceset  = $tickModel->getRangeRetailPrice($beginDate, $endDate, $pid);
        //单位转换成为元
        $priceset = array_map(function ($val) {
            return $val / 100;
        }, $priceset);

        $this->apiReturn(200, array_keys($priceset));
    }

    /**
     * 预定页面-获取价格和库存
     */
    public function getPriceAndStorage()
    {
        $aid     = I('aid', '', 'intval');
        $date    = I('date', date('Y-m-d'));
        $roundId = I('round_id', 0);
        $tids    = I('tids');
        $tidArr  = explode('-', $tids);

        if (!$tidArr || !$date || !$aid) {
            $this->apiReturn(201, [], '参数错误');
        }

        // 先利用tidArr获取pidArr
        $ticketModel = new Ticket();
        $tidPidArr   = $ticketModel->getMuchTicketInfo($tidArr, 'id, pid');

        $date = date('Y-m-d', strtotime($date));

        if ($date < date('Y-m-d')) {
            $this->apiReturn(202, '请选择正确的日期');
        }

        $ticketStr = implode(',', $tidArr);

        //演出类产品的库存
        if ($roundId && $roundId != 0) {
            $tidPidZoneArr = $this->getZoneId($tidPidArr);

            $showBiz    = new \Business\Product\Show();
            $tmpStorage = $showBiz->getZoneLeftStorageAll($roundId);

            $storageMap = [];
            if ($tmpStorage['code'] == 1) {
                $showStorage = $tmpStorage['data']['storage'];
                $zoneStorage = [];

                foreach ($showStorage as $key => $val) {
                    foreach ($val as $k => $v) {
                        $zoneStorage[$k] = $v;
                    }
                }

                foreach ($tidPidZoneArr as $tid => $value) {
                    $storageMap[$tid] = $zoneStorage[$value['zoneId']];
                }
            }
        } else {
            //获取库存
            $storageMap = StorageApi::getBatchStorageByPlayDate($ticketStr, $date);
        }

        if (empty($storageMap)) {
            $this->apiReturn(208, [], '获取库存信息出错，请稍后重试');
        }

        //获取价格
        $priceData = $this->_getPrice($aid, $date, $tidArr);

        if (!$priceData) {
            $this->apiReturn(205, [], '没有相关数据');
        }

        $return = [];
        foreach ($priceData as $tid => $item) {
            $store   = $storageMap[$tid];
            $price   = $item['retail_price'] / 100;
            $msprice = $item['counter_price'] / 100;
            if (\inWechatSmallApp()) {
                $price = $item['window_price'] / 100;
            }
            $return[$tidPidArr[$tid]] = [
                'price'   => $price,
                'msprice' => $msprice,
                'store'   => $store >= -1 ? $store : 0,
            ];
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 根据tid和pid获取zone_id
     * Create by zhangyangzhen
     * Date: 2019/2/19
     * Time: 14:50
     *
     * @param $tickets
     *
     * @return mixed
     */
    public function getZoneId($tickets)
    {
        $tidArr = array_key($tickets);

        $javaApi  = new \Business\CommodityCenter\LandF();
        $landfArr = $javaApi->queryLandFByLandIdAndTicketIdAndProductId($tidArr, [], [], 'tid, zone_id', true);

        $data = [];
        foreach ($tickets as $tid => $pid) {
            if (isset($landfArr[$tid])) {
                $zoneId = $landfArr[$tid];
            } else {
                $zoneId = 0;
            }

            $data[$tid] = [
                'tid'    => $tid,
                'pid'    => $pid,
                'zoneId' => $zoneId,
            ];
        }

        return $data;
    }

    /**
     * 预定页面-获取价格和库存（针对酒店）
     * @return [type] [description]
     */
    public function getHotelPriceAndStorage()
    {

        $beginDate = I('beginDate');
        $endDate   = I('endDate');
        $pid       = I('pid', 0, 'intval');
        $aid       = I('aid');

        if (!$pid || !$aid) {
            $this->apiReturn(204, [], '参数错误');
        }

        if ($beginDate < date('Y-m-d') || $endDate < $beginDate) {
            $this->apiReturn(204, [], '请选择正确的时间');
        }

        //根据产品id获取票类ID
        $ticketModel = new Ticket();
        $tmpTickets  = $ticketModel->getTicketInfoByPidArr([$pid], 'pid,id');
        $tidPidArr   = [];
        foreach ($tmpTickets as $item) {
            $tidPidArr[$item['pid']] = $item['id'];
        }
        //对接到java获取价格处理   PeiJun Li  2018-9-27
        $priceset = TicketApi::getCalendarPriceAndStorage($tidPidArr[$pid], $beginDate, $endDate);

        //新接口返回的数据key值不再是时间了，放在下面的循环处理
        //unset($priceset[$endDate]);

        //检测是否存在某天无价格,无库存
        $totalPirce = 0;
        $store      = [];
        foreach ($priceset as $k => $item) {
            $date = $item['time'];

            if ($date == $endDate) {
                unset($priceset[$k]);
                continue;
            }

            //如果是小程序获取窗口价，否则获取零售价
            if (\inWechatSmallApp()) {
                $price = $item['windowPrice'];
            } else {
                $price = $item['retailPrice'];
            }
            if ($price == -1) {
                $this->apiReturn(204, [], $date . '无价格');
            }
            //获取当日库存
            $tmp = $this->_getStorage([$pid], $aid, $date);

            $store[$date] = ((int)$tmp[$pid] >= -1) ? (int)$tmp[$pid] : 0;
            //全民分销价格
            if ($this->_allDisMan) {
                $param      = [
                    'jsprice' => $price / 100,
                    'pid'     => $pid,
                    'aid'     => $aid,
                ];
                $changeRes  = AllDisProcess::getAllDisPrice($this->_supplyId, [$param]);
                $totalPirce += $changeRes[0]['jsprice'];
            } else {
                $totalPirce += $price / 100;
            }
        }

        $return = [
            'jsprice' => $totalPirce,
            'store'   => $store,
        ];

        $this->apiReturn(200, $return);

    }

    /**
     * 预定页面-获取价格
     * @param  array $tidArr tid数组   [1026, 1027]
     * @param  int $aid 上级供应商id
     * @param  string $date 日期 2016-08-10
     *
     * @return  [1026 => 1, 1027 => 2]
     */
    private function _getPrice($aid, $date, $tidArr = '', $pidArr = '')
    {
        $priceset = [];
        if ($pidArr != '') {
            $tickModel = new Ticket('slave');
            $priceset  = $tickModel->getMuchRetailPrice($pidArr, $date);

            if ($this->_allDisMan) {
                foreach ($pidArr as $pid) {
                    $tmp[] = [
                        'pid'   => $pid,
                        'aid'   => $aid,
                        'price' => $priceset[$pid],
                    ];
                }
                $tmpPrice = AllDisProcess::getAllDisPrice($this->_supplyId, $tmp, 'interface');
                foreach ($tmpPrice as $item) {
                    $priceset[$item['pid']] = $item['price'];
                }
                // $priceset = array_values($priceset);
            }
        }

        // 返回从java获取到的价格数组
        if ($tidArr != '') {
            $ticketIds = implode(',', $tidArr);
            $priceset  = TicketApi::getSinglePrices($ticketIds, date('Y-m-d', strtotime($date)));
        }

        return $priceset;
    }

    /**
     * 预订页面-获取产品库存
     * @param  array $pidArr pid数组   [1026, 1027]
     * @param  int $aid 上级供应商id
     * @param  string $date 日期 2016-08-10
     *
     * @return  [1026 => 1, 1027 => 2]
     */
    private function _getStorage($pidArr, $aid, $date)
    {

        $tickModel = new Ticket('slave');
        $ptype     = $tickModel->getProductType($pidArr[0]);
        switch ($ptype) {
            case 'A': //景区

            case 'B': //线路

            case 'C': //酒店

            case 'G': //餐饮

            case 'I': //年卡

            case 'F': //套票
                $result = $this->_getStorageForCommon($pidArr, $date, $aid);
                break;

            case 'H': //演出
                $result = $this->_getStorageForShow($pidArr, $date, $aid);
                break;

            default:
                $this->apiReturn(204, '产品类型错误');
        }

        return $result;
    }

    /**
     * 通用景区类型获取库存接口
     *
     * @param  array $pidArr 产品id数组 [2016, 2017]
     * @param  string $date 日期 2016-08-01
     * @param  int $aid 上级供应商id
     *
     * @return array    [2016 => 1, 2017 => -1]
     */
    private function _getStorageForCommon($pidArr, $date, $aid)
    {
        $Model = new Ticket('slave');

        return $Model->getMuchStorage($pidArr, $date);
    }

    private function _getStorageForShow()
    {
        // to be continued
    }

    /**
     * 获取演出门票对应的区域id
     *
     * @param  array $tickets 门票列表信息
     *
     * @return array
     */
    private function _getZoneId($tickets)
    {
        $tidArr   = array_column($tickets, 'tid');
        $javaApi  = new \Business\CommodityCenter\LandF();
        $landfArr = $javaApi->queryLandFByLandIdAndTicketIdAndProductId($tidArr, [], [], 'tid, zone_id', true);

        foreach ($tickets as &$item) {
            $tid = $item['tid'];

            if (isset($landfArr[$tid])) {
                $item['zone_id'] = $landfArr[$tid];
            } else {
                $item['zone_id'] = 0;
            }
        }

        return $tickets;
    }

    /**
     * 获取产品集合
     *
     * @param  [type] $option 筛选参数
     *
     * @return [type]         [description]
     */
    private function _getProductSet($option, $allType = [], $pageNo = 1, $pageNum = 10, $lids = '')
    {
        if (empty($allType)) {
            $allType = $this->_allowType;
        }

        $productArr    = [];
        $inWechatSmApp = $this->inWechatSmallApp();
        $channelVal    = $this->checkChannel($inWechatSmApp);
        $productName   = isset($option['title']) ? $option['title'] : '';
        $province      = isset($option['province']) ? $option['province'] : '';
        $city          = isset($option['city']) ? $option['city'] : '';
        $topic         = isset($option['topic']) ? $option['topic'] : '';
        $level         = isset($option['level']) ? $option['level'] : '';
        $allowTypeStr  = $allType ? implode(',', $allType) : '';

        $productListBiz = new bizProductList();
        if ($inWechatSmApp) {
            // 自供应产品
            $productArr = $productListBiz->getWindowList($this->_supplyId, $pageNo, $pageNum, $channelVal, $productName,
                $allowTypeStr, $level, $topic, $province, $city, '', false, false, $lids);
        } else {
            $supplyIds = $this->_supplyId;
            if ($supplyIdsStr = $this->getSupplyIds()) {
                $supplyIds = $supplyIdsStr;
            }
            // 转分销+自供应
            $productArr = $productListBiz->getRetailList($supplyIds, $pageNo, $pageNum, $channelVal, $productName,
                $allowTypeStr, $level, $topic, $province, $city, '', true, false, $lids);
        }
        // 无数据提前返回
        if ($productArr['code'] != 200) {
            return [];
        }

        // 返回取得数据
        return $productArr['data'];
    }

    /**
     * 整合前端需要的数据
     *
     * @param  array $lands 景区信息
     * @param  array $pidArr 门票pid数组
     *
     * @return
     */
    private function _productDeal($LandTicketData)
    {
        // 获取产品数据中最低价格数据
        $data = [];
        if ($LandTicketData && is_array($LandTicketData)) {
            //微票房展示窗口价  微商城展示结算价
            if ($this->inWechatSmallApp()) {
                $landPtype = load_config('land_ptype', 'account');
                foreach ($LandTicketData as $landVal) {
                    $data[] = array(
                        'lid'          => $landVal['id'],
                        // 'tid'      => $landVal['ticket'][$minKey]['tid'],
                        'title'        => $landVal['name'],
                        'pid'          => $landVal['product_id'],
                        'province'     => $landVal['province'],
                        'city'         => $landVal['city'],
                        'address'      => $landVal['address'],
                        'aid'          => $landVal['supplier_id'],
                        'imgpath'      => $landVal['img_path'],
                        'topic'        => $landVal['topics'],
                        'tCount'       => $landVal['ticket_count'],
                        'lptype'       => $landVal['type'],
                        'ptype'        => $landPtype[$landVal['type']],
                        'tprice'       => sprintf('%.2f', ($landVal['counter_price'] / 100)),
                        'window_price' => sprintf('%.2f', ($landVal['window_price'] / 100)),
                    );
                }
            } else {
                foreach ($LandTicketData as $landVal) {
                    $data[] = array(
                        'lid'         => $landVal['id'],
                        // 'tid'      => $landVal['ticket'][$minKey]['tid'],
                        'title'       => $landVal['name'],
                        // 'area'    => $tickets[0]['area'],
                        'pid'         => $landVal['product_id'],
                        'max_pid'     => $landVal['max_product_id'],
                        'max_tid'     => $landVal['max_ticket_id'],
                        'province'    => $landVal['province'],
                        'city'        => $landVal['city'],
                        'address'     => $landVal['address'],
                        'aid'         => $landVal['supplier_id'],
                        'imgpath'     => $landVal['img_path'],
                        'topic'       => $landVal['topics'],
                        'lptype'      => $landVal['type'],
                        'jsprice'     => sprintf('%.2f', ($landVal['retail_price'] / 100)),
                        'max_jsprice' => sprintf('%.2f', ($landVal['max_retail_price'] / 100)),
                        'tprice'      => sprintf('%.2f', ($landVal['counter_price'] / 100)),
                    );
                }
            }
        }

        return $data;
    }

    /**
     * 获取微商城配置
     * @return [type] [description]
     */
    public function getCustomConfig($return = false)
    {

        $config        = $this->getMallConfig();
        $name          = $config['name'];
        $others        = json_decode($config['others'], true);
        $banner        = $others['banner'];
        $bannerInfoArr = $this->getMallBanner();
        if (!empty($bannerInfoArr)) {
            $banner = array_merge($banner, $bannerInfoArr);
        }
        if (!$banner) {
            $banner[0] = [
                'http://images.12301.cc/123624/14786829442552.jpg' => '',
            ];
        }

        $data = [
            'name'           => $name ?: '微商城',
            'banner'         => $banner,
            'tel'            => $config['telephone'],
            'theme'          => $others['template'] ?: 'default',
            'product_layout' => $others['product_layout'] ?: 0,
        ];

        if ($return) {
            return $data;
        }
        $this->apiReturn(200, $data);
    }

    /**
     * 获取产品类型列表
     *
     * @param  $return bool 是否当作函数返回值返回
     *
     * @return [type] [description]
     */
    public function getTypeList($back = false)
    {
        $config = $this->getMallConfig();
        $others = json_decode($config['others'], true);

        $return = [];
        if (isset($others['type'])) {
            foreach ($others['type'] as $identify => $name) {
                $return[] = [
                    'name'      => $name,
                    'identify'  => $identify,
                    "isChecked" => "true",
                ];
            }
        } else {
            $return = [
                // ['name' => '景区', 'identify' => 'A'],
                // ['name' => '酒店', 'identify' => 'C'],
                // ['name' => '周边游', 'identify' => 'B'],
                // ['name' => '套票', 'identify' => 'F'],
                // ['name' => '演出', 'identify' => 'H'],
            ];
        }
        //加入主题
        if (isset($others['themes'])) {
            foreach ($others['themes'] as $key => $name) {
                if (is_array($name)) {
                    $isChecked = $name['isChecked'];
                    $name      = $name['name'];
                }
                if ($isChecked == 'false') {
                    continue;
                }
                $return[] = [
                    'name'      => $name,
                    'show_name' => $key,
                    'identify'  => 'theme',
                    "isChecked" => $isChecked,
                ];
            }
        }

        //加入营销活动
        if (isset($others['marketing'])) {
            foreach ($others['marketing'] as $key => $name) {
                if (is_array($name)) {
                    $isChecked = $name['isChecked'];
                    $name      = $name['name'];
                }
                if ($isChecked == 'false') {
                    continue;
                }
                $return[] = [
                    'name'      => $name,
                    'show_name' => $key,
                    'identify'  => 'marketing',
                    "isChecked" => $isChecked,
                ];
            }
        }

        if ($back) {
            return $return;
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 获取主题列表
     * @return [type] [description]
     */
    public function getThemes()
    {

        $config = $this->getMallConfig();
        $others = json_decode($config['others'], true);

        if (isset($others['themes'])) {
            if (!is_numeric(key($others['themes']))) {
                $return = [];
                foreach ($others['themes'] as $value) {
                    $return[] = $value['name'];
                }
            } else {
                $return = $others['themes'];
            }
        } else {
            $return = ThemeConst::THEMES;
        }

        return $return;
    }

    /**
     * 祥源定制主题,需要的城市接口
     * <AUTHOR>
     * @date   2017-12-11
     */
    public function getCityForGroupTheme()
    {
        $getEvoluteBiz = new \Business\Product\Get\Evolute();
        $list          = $getEvoluteBiz->getEvoluteByFidStatus([$this->_supplyId], 0);

        $return = [];
        if ($list) {

            //过滤门票状态
            $tickModel = new Ticket('slave');
            $pidArr    = array_column($list, 'pid');
            $pidArr    = $tickModel->filterInvalidPid($pidArr);

            $lidArr = [];
            foreach ($list as $item) {
                $tmpChannel = explode(',', $item['channel']);
                //过滤渠道
                if (in_array(self::__MALL_CHANNEL__, $tmpChannel)) {
                    if (in_array($item['pid'], $pidArr)) {
                        $lidArr[]                = $item['lid'];
                        $pxMapping[$item['lid']] = $item['px'];
                    }
                }
            }

            if ($lidArr) {
                //获取景区code
                //$landModel   = new Land('slave');
                //$areaMapping = $landModel->getLandInfoByMuli($lidArr, 'id,area,title', true);

                $javaAPi     = new \Business\CommodityCenter\Land();
                $landInfo    = $javaAPi->queryLandMultiQueryById($lidArr);
                $areaMapping = array_column($landInfo, null, 'id');

                $areaModel = new Area();
                $areas     = $areaModel->getAreaList();

                foreach ($areaMapping as $lid => $item) {

                    if (!$item['area']) {
                        continue;
                    }

                    $codeArr  = explode('|', $item['area']);
                    $province = $codeArr[0];
                    $city     = $codeArr[1];

                    if (!$city || !$province) {
                        continue;
                    }

                    $return[$province]['name'] = $areas[$province];

                    if (isset($return[$province]['list'][$city])) {
                        $nowPx = $return[$province]['list'][$city]['land']['px'];
                        //推荐值大的代替小的
                        if ($pxMapping[$lid] > $nowPx) {
                            $return[$province]['list'][$city]['land'] = [
                                'lid'  => $lid,
                                'px'   => $pxMapping[$lid],
                                'name' => $item['title'],
                            ];
                        }

                        continue;
                    } else {
                        $return[$province]['list'][$city] = [
                            'name' => $areas[$city],
                            'code' => $city,
                            'land' => ['lid' => $lid, 'px' => $pxMapping[$lid], 'name' => $item['title']],
                        ];
                    }
                }
            }
        }

        $this->apiReturn(200, array_values($return));
    }

    /**
     * php获取中文字符拼音首字母
     *
     * @param  string $str 中文字符
     */
    private function _getFirstCharter($str)
    {
        if (empty($str)) {
            return '';
        }

        $fchar = ord($str{0});

        if ($fchar >= ord('A') && $fchar <= ord('z')) {
            return strtoupper($str{0});
        }

        $s1 = iconv('UTF-8', 'gb2312', $str);

        $s2 = iconv('gb2312', 'UTF-8', $s1);

        $s = $s2 == $str ? $s1 : $str;

        $asc = ord($s{0}) * 256 + ord($s{1}) - 65536;
        if ($asc >= -20319 && $asc <= -20284) {
            return 'A';
        }

        if ($asc >= -20283 && $asc <= -19776) {
            return 'B';
        }

        if ($asc >= -19775 && $asc <= -19219) {
            return 'C';
        }

        if ($asc >= -19218 && $asc <= -18711) {
            return 'D';
        }

        if ($asc >= -18710 && $asc <= -18527) {
            return 'E';
        }

        if ($asc >= -18526 && $asc <= -18240) {
            return 'F';
        }

        if ($asc >= -18239 && $asc <= -17923) {
            return 'G';
        }

        if ($asc >= -17922 && $asc <= -17418) {
            return 'H';
        }

        if ($asc >= -17417 && $asc <= -16475) {
            return 'J';
        }

        if ($asc >= -16474 && $asc <= -16213) {
            return 'K';
        }

        if ($asc >= -16212 && $asc <= -15641) {
            return 'L';
        }

        if ($asc >= -15640 && $asc <= -15166) {
            return 'M';
        }

        if ($asc >= -15165 && $asc <= -14923) {
            return 'N';
        }

        if ($asc >= -14922 && $asc <= -14915) {
            return 'O';
        }

        if ($asc >= -14914 && $asc <= -14631) {
            return 'P';
        }

        if ($asc >= -14630 && $asc <= -14150) {
            return 'Q';
        }

        if ($asc >= -14149 && $asc <= -14091) {
            return 'R';
        }

        if ($asc >= -14090 && $asc <= -13319) {
            return 'S';
        }

        if ($asc >= -13318 && $asc <= -12839) {
            return 'T';
        }

        if ($asc >= -12838 && $asc <= -12557) {
            return 'W';
        }

        if ($asc >= -12556 && $asc <= -11848) {
            return 'X';
        }

        if ($asc >= -11847 && $asc <= -11056) {
            return 'Y';
        }

        if ($asc >= -11055 && $asc <= -10247) {
            return 'Z';
        }

        return null;
    }

    /**
     * 得到指定产品当前可用的优惠券列表
     * <AUTHOR>
     * @date   2017-07-18
     *
     * @param  int $fid 获取优惠券的用户ID
     * @param  int $aid 优惠券的供应商ID
     * @param  int $pid 优惠券指定使用的产品ID
     * @param  date $useDate 优惠券使用日期
     *
     * @return ['valid'=>[], 'other'=>[]]
     */
    public function getCouponList()
    {
        // 全民分销
        if ($_SESSION['identify'] == 'allDis') {
            $this->apiReturn(202, [], '全民分销不可使用优惠券');
        }

        $aid = $this->_supplyId;
        $fid = $_SESSION['sid'];

        $pid     = I('pid', 0, 'intval');
        $today   = date('Y-m-d');
        $useDate = I('date', time(), 'strtotime');
        $useDate = date('Y-m-d', $useDate);
        if ($useDate < $today) {
            $useDate = $today;
        }

        $bus = new \Business\Order\Coupon();
        $res = $bus->getCouponList($fid, $aid, $pid, $useDate);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $return = $res['data'];

        // 其他优惠券 - 适用产品信息
        if (!empty($return['other'])) {

            $other = $bus->getCouponProductName($return['other']);
            if ($other['code'] != 200) {
                $this->apiReturn($other['code'], [], $other['msg']);
            }

            $return['other'] = $other['data'];
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 首页抢购轮播
     * <AUTHOR>
     * @date   2017-12-26
     */
    public function getSeckillCarousel()
    {

        $seckillBiz = new \Business\Wechat\Seckill();

        $result = $seckillBiz->getSeckillCarousel($this->_supplyId, 1, 5);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取正在进行中的抢购活动
     * <AUTHOR>
     * @date   2017-12-26
     */
    public function getUnderwaySeckills()
    {

        $page = I('page', 1, 'intval');

        $size = I('size', 10, 'intval');

        if ($size > 100) {
            $this->apiReturn(204, '获取条数超出限制');
        }

        $seckillBiz = new \Business\Wechat\Seckill();

        $result = $seckillBiz->getUnderwaySeckills($this->_supplyId, $page, $size);

        if (isset($result['code'])) {
            //全民营销推广
            if ($result['data'] && $this->_promoter) {
                $allDisModel     = new AllDisModel();
                $is_seckill_join = 0;
                $config          = $allDisModel->getAllDisConfig($this->_supplyId);
                if ($config) {
                    $config          = json_decode($config['config'], true);
                    $is_seckill_join = $config['is_seckill_join'] ? 1 : 0;
                }
                $list = $result['data'];
                foreach ($list as &$item) {
                    $item['jsprice']         = $item['price'] / 100;
                    $item['is_seckill_join'] = $is_seckill_join;

                }
                //计算推广佣金
                $result['data'] = AllDisProcess::calRecommendCommission($this->_supplyId, $list);
            }
            $this->apiReturn($result['code'], $result['data']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取即将开始的抢购活动
     * <AUTHOR>
     * @date   2017-12-26
     */
    public function getComingSeckills()
    {

        $page = I('page', 1, 'intval');
        $size = I('size', 10, 'intval');
        //微信openid
        $openid = I('session.openid', '');

        if ($size > 100) {
            $this->apiReturn(204, '获取条数超出限制');
        }
        $allDisModel = new AllDisModel();

        if (isset($_SESSION['identify'])) {
            //全民营销,获取供应商openid
            $memberInfo = $allDisModel->getAllDisMemberInfo(I('session.memberID'));
            if ($memberInfo && $memberInfo['supply_openid']) {
                $openid = $memberInfo['supply_openid'];
            }
        }

        $seckillBiz = new \Business\Wechat\Seckill();
        $result     = $seckillBiz->getComingSeckills($this->_supplyId, $page, $size, $openid);
        if (isset($result['code'])) {
            //全民营销推广
            if ($result['data'] && $this->_promoter) {
                $is_seckill_join = 0;
                $config          = $allDisModel->getAllDisConfig($this->_supplyId);
                if ($config) {
                    $config          = json_decode($config['config'], true);
                    $is_seckill_join = $config['is_seckill_join'] ? 1 : 0;
                }
                $list = $result['data'];
                foreach ($list as &$item) {
                    $item['jsprice']         = $item['price'] / 100;
                    $item['is_seckill_join'] = $is_seckill_join;
                }
                //计算推广佣金
                $result['data'] = AllDisProcess::calRecommendCommission($this->_supplyId, $list);

            }
            $this->apiReturn($result['code'], $result['data']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 设置抢购提醒
     * <AUTHOR>
     * @date   2017-12-26
     */
    public function setSeckillWarning()
    {

        $id = I('id', 0, 'intval');
        //微信openid
        $openid = I('session.openid', '');

        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }
        if (!$openid) {
            $this->apiReturn(204, [], '没有发送微信提醒的权限');
        }

        if (isset($_SESSION['identify'])) {
            //全民营销,获取供应商openid
            $allDisModel = new AllDisModel();
            $memberInfo  = $allDisModel->getAllDisMemberInfo(I('session.memberID'));
            if ($memberInfo && $memberInfo['supply_openid']) {
                $openid = $memberInfo['supply_openid'];
            }
        }

        $seckillBiz = new \Business\Wechat\Seckill();
        $result     = $seckillBiz->setSeckillWarning($id, $this->_supplyId, $openid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 获取营销的产品
     * <AUTHOR>
     * @date   2019-1-24
     */
    public function getMarketProduct()
    {
        $type    = I('type', '', 'strval');
        $product = new \Business\Mall\Product();
        $result  = $product->getMarketProduct($type, $this->_supplyId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 微商城年卡续费获取续费的产品和详情
     * author  linchen
     * Date: 2019-5-9
     */
    public function getAnnualRenewInfo()
    {
        $memberId = $this->isLogin('ajax');
        $lid      = I('post.lid', 0, 'intval');
        $cardId   = I('post.card_id', 0, 'intval');
        if (!$lid) {
            $this->apiReturn(204, [], '参数错误');
        }
        $annualBiz = new \Business\Product\AnnualCard();
        $result    = $annualBiz->getRenewalCardList($memberId, $this->_supplyId, $lid, $memberId, '', 1, $cardId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

}
