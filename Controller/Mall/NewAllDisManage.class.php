<?php
/**
 *  新全名营销后台设置
 *
 * User: xujy
 * Date: 2020/3/12
 */

namespace Controller\Mall;

use Library\Controller;

class NewAllDisManage extends Controller
{
    /**
     * @var $sid 供应商id
     */
    private $sid = null;

    private $loginInfo = null;

    public function __construct()
    {
        $this->loginInfo = $this->getLoginInfo();
        $this->_sid = $this->isLogin('ajax');
    }

    /**
     *  获取全名营销配置
     * <AUTHOR>
     * @date 2020/3/12
     * @return void
     */
    public function getAllDisConfig()
    {
        $allDisBiz = new \Business\Cooperator\AllDis\AllDis();
        $config    = $allDisBiz->getConfig($this->_sid);

        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }

    /**
     *  保存全民营销配置
     * <AUTHOR>
     * @date 2020/3/13
     * @return array
     */
    public function saveAllDisConfig()
    {
        $allDisBiz = new \Business\Cooperator\AllDis\AllDis();
        $config    = $allDisBiz->saveAllDisConfig($this->_sid, $_REQUEST);
        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }

    /**
     *  开通全民营销
     * <AUTHOR>
     * @date 2020/4/26
     * @return array
     */
    public function openAllDis()
    {
        $allDisBiz = new \Business\Cooperator\AllDis\AllDis();
        $config    = $allDisBiz->openAllDis($this->_sid);
        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }

    /**
     * 关闭全民营销
     * <AUTHOR>
     * @date 2020/4/26
     * @return array
     */
    public function closeAllDis()
    {
        $allDisBiz = new \Business\Cooperator\AllDis\AllDis();
        $isNew     = I('isNew', 0);
        pft_log("allDis/config", "关闭营销--" . json_encode($_REQUEST, JSON_UNESCAPED_UNICODE));
        if (!$isNew) {
            $this->apiReturn(205, [], "非法关闭");
        }
        $config = $allDisBiz->closeAllDis($this->_sid);
        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }
}
