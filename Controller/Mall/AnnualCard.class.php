<?php

namespace Controller\Mall;

use Business\Face\AnnualAliFaceBiz;
use Library\Controller;
use Library\Tools\Helpers;
use Library\Tools\Vcode;
use Business\PftSystem\FaceCompare;
use Business\Product\AnnualCard as AnnualBiz;

class AnnualCard extends Controller
{
    //验证短信业务的标识
    private $_verifySmsIdentify = 'verify_sms';

    //验证短信的发送频率 - 60秒
    private $_verifySmsInterval = 60;

    private $_verifySmsExpire = 1800;

    //短信模板
    private $_verifySmsTpl = 'annual_activate';

    /**
     * 获得二维码里数据
     * author  leafzl
     * Date: 2018-09-11
     */
    public function getQcodeData()
    {
        $virtualNo = I('virtual_no', '', 'strval');
        $sid       = I('sid', '', 'intval');

        if (!$virtualNo && !$sid) {
            $this->apiReturn(204, [], "参数错误");
        }
        $annual = new AnnualBiz();
        $result = $annual->getQcodeData($virtualNo, $sid);

        if ($result['code'] == 200) {
            $this->apiReturn(200, $result['data'], '获取成功');
        } else {
            $this->apiReturn($result['code'], [], $result['msg']);
        }
    }

    /**
     * 保存头像
     * author  leafzl
     * Date: 2018-09-11
     */
    public function saveFacePhotos()
    {
        $virtualNo = I('virtual_no', '', 'strval');
        $avatar    = I('avatar', '');

        if (!$virtualNo && !is_array($avatar)) {
            $this->apiReturn(204, [], "参数错误");
        }
        $cardModel = new \Model\Product\AnnualCard();
        $card = $cardModel->getAnnualCard($virtualNo, 'virtual_no');
        if (empty($card)) {
            $this->apiReturn(204, [], "未找到该卡信息");
        }
        $annual = new AnnualBiz();
        $result = $annual->saveGroupPhotos($virtualNo, $avatar);
        $callIsAliFacePlatform = AnnualAliFaceBiz::getInstance()->callIsAliFacePlatform($card['sid'], $card['pid']);
        if ($callIsAliFacePlatform) {
            $params = [
                'sid' => $card['sid'],
                'virtual_no' => $card['virtual_no'],
                'tourist_info' => [
                    [
                        'name' => $card['dname'],
                        'id_card_no' => $card['id_card_no'],
                    ]
                ],
            ];
            AnnualAliFaceBiz::getInstance()->annualFaceRegisterProcess($params);
        }
        if ($result['code'] == 200) {
            $this->apiReturn(200, [], '保存成功');
        }
        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 获得能上传几张照片
     * author  leafzl
     * Date:2018-09-11
     */
    public function getFamilyNum()
    {
        $cardNo  = I('card_no', '', 'strval');
        $account = I('post.account', '', 'strval');
        if (!$cardNo || !$account) {
            $this->apiReturn(500, [], '参数错误');
        }
        $member = new \Business\Member\Member();

        $info = $member->getInfoByAccount($account);
        if (!$info) {
            $this->apiReturn(402, [], '参数错误');
        }

        $annual = new AnnualBiz();
        $result = $annual->getFamilyNum($cardNo, $info['id']);

        if ($result['code'] == 200) {
            $this->apiReturn(200, $result['data']);
        } else {
            $this->apiReturn($result['code'], [], $result['msg']);
        }
    }

    /**
     * @todo 前端应该不再调用，暂时不处理人脸独立qps
     * 人脸检测
     * author  leafzl
     * Date: 2018-9-13
     */
    public function detectFace()
    {
        $image_url = I('image_url', '', 'strval');
        $sid = I('sid', 0, 'intval');
        if (!$image_url) {
            $this->apiReturn(204, [], "参数错误");
        }
        $img    = file_get_contents($image_url);
        $face   = new FaceCompare('baidu');
        $result = $face->detectFace(base64_encode($img), [
                'image_type'=>'BASE64',
                'liveness_control'=>'NORMAL',
            ],
            $sid
        );
        if ($result['code'] == 200) {
            $this->apiReturn(200, [], '检测通过');
        }
        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 获得年卡人脸的信息
     * author  leafzl
     * Date:2018-09-11
     */
    public function getFacePic()
    {
        $virtualNo = I('virtual_no', '', 'strval');

        if (!$virtualNo) {
            $this->apiReturn(204, [], "参数错误");
        }

        $annual = new AnnualBiz();
        $result = $annual->getFacePic($virtualNo);

        if ($result['code'] == 200) {
            $this->apiReturn(200, $result['data']);
        } else {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

    }

    /**
     * 发送手机验证码(微商城年卡激活用)
     * <AUTHOR>
     * @date   2018-9-10
     *
     * @param  int  $mobile  手机号
     */
    public function sendVcodeForActiveCard()
    {
        $mobile = I('mobile', '', 'intval');
        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(204, [], "手机号错误");
        }

        $data   = [
            '{1}'  => '年卡激活',
            'code' => '{2}',
        ];
        $result = Vcode::sendVcode($mobile, $this->_verifySmsTpl, $this->_verifySmsIdentify, $data, 6, false,
            $this->_verifySmsExpire, $this->_verifySmsInterval);

        if ($result['code'] == 200) {

            $this->apiReturn(200, [], '发送成功');
        } else {
            $this->apiReturn(204, [], "发送失败:{$result['msg']}");
        }
    }

    /**
     * 检查实体卡能不能用
     * author  leafzl
     * Date: 2018-9-10
     */
    public function checkCardNo()
    {
        $cardNo  = I('post.card_no', '', 'strval');
        $account = I('post.account', '', 'strval');

        if (!$cardNo || !$account) {
            $this->apiReturn(500, [], '参数错误');
        }
        $member = new \Business\Member\Member();

        $info = $member->getInfoByAccount($account);
        if (!$info) {
            $this->apiReturn(402, [], '参数错误');
        }
        $annual = new AnnualBiz();
        $result = $annual->checkCardNo($cardNo, $info['id']);

        if ($result['code'] == 200) {
            $this->apiReturn(200);
        } else {
            $this->apiReturn($result['code'], [], $result['msg']);
        }
    }

    /**
     * 检查手机是否存在通过验证码
     * author  leafzl
     * Date: 2018-9-10
     */
    public function checkMobileCode()
    {
        $mobile = I('mobile', '', 'strval');
        //实体卡
        $idCard = I('id_card', '', 'strval');
        $code   = I('code', '', 'intval');

        $annual = new AnnualBiz();
        $result = $annual->checkMobileCode($idCard, $mobile, $code);

        if ($result['code'] == 200) {
            $this->apiReturn(200);
        } else {
            $this->apiReturn($result['code'], [], $result['msg']);
        }
    }

    /**
     * 激活年卡
     * author  leafzl
     * Date: 2018-9-10
     */
    public function activeAnnualCard()
    {

        $mobile = I('mobile', '', 'strval');
        //实体卡
        $cardNo   = I('card_no', '', 'strval');
        $idCard   = I('id_card', '', 'strval');
        $userName = I('user_name', '', 'strval');
        $avatar   = I('avatar', '');
        $account  = I('post.account', '', 'strval');
        if (!$cardNo || !$account) {
            $this->apiReturn(500, [], '参数错误');
        }
        $member = new \Business\Member\Member();

        $info = $member->getInfoByAccount($account);
        if (!$info) {
            $this->apiReturn(402, [], '参数错误');
        }
        $annual = new AnnualBiz();
        $result = $annual->activeAnnualCardForshop($mobile, $cardNo, $idCard, $avatar, 11, $userName, $info['id']);

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], '激活成功');
        } else {
            $this->apiReturn($result['code'], [], $result['msg']);
        }
    }

}