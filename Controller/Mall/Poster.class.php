<?php

/**
 * 微商城产品海报接口(pc端/移动端)
 * <AUTHOR>
 */

namespace Controller\Mall;

use Business\CommodityCenter\TicketAttribute;
use Business\Cooperator\WeChat\WechatTrusteeship;
use Business\JavaApi\Member\MemberRelationQuery;
use Controller\Mall\Mall;
use Library\Container;
use Library\Tools\Helpers;
use Library\Cache\Cache;
use Process\Resource\Qiniu as QiniuProcess;
use Model\Product\Ticket;
use Model\Product\Land;
use Model\Member\Member;
use Business\Wechat\Seckill;
use Business\Mall\Poster as PosterBiz;
use Model\Member\MemberRelationship;
use Model\Subdomain\SubdomainInfo;
use Exception;
use Intervention\Image\ImageManager as Image;

use Endroid\QrCode\QrCode;

class Poster extends Mall
{
    //图片格式
    const EXT = 'png';
    //目录分隔符
    const DOT = DIRECTORY_SEPARATOR;
    //图片最大上传大小
    const MAX_IMAGE_SIZE = 1048576;
    //微商城首页地址
    const INDEX_URL = 'wx/c/index.html';
    //产品预订页地址
    const BOOKING_URL = 'wx/c/pdetail.html?lid={lid}&aid={aid}';
    //演出产品
    const H_BOOKING_URL = 'wx/c/pdetail_h.html?lid={lid}&aid={aid}';
    //特产产品
    const J_BOOKING_URL = '/wx/c/speciality.html?lid={lid}&aid={aid}';
    //抢购活动页地址
    const SECKILL_URL = 'c/seckill_detail.html?seckill_id={seckill_id}&aid={aid}&account={account}';
    //砍价活动页地址
    const CUT_PRICE_URL = 'wx/c/activity_detail.html?id={cut_id}&aid={aid}';
    //拼团活动页地址
    const GROUP_BOOKING_URL = 'wx/c/pintuan.html?id={group_id}&aid={aid}#/pdetail';
    //招聘页面地址
    const JOIN_US = 'html/joinus.html';

    //二维码宽度
    const QRCODE_WIDTH = 170;
    //二维码长度
    const QRCODE_HEIGHT = 170;
    //默认二维码x轴偏移量
    const QRCODE_X = 115;
    //默认二维码Y轴偏移量
    const QRCODE_Y = 220;
    //二维码变宽大小
    const QRCODE_P = 10;

    //当前登陆用户id
    private $_mid;
    //当前登陆用户账号名称
    private $_account;
    //微商城海报存放目录
    private $_mallPosterDir;
    //微商城海报背景图图存放目录
    private $_mallPosterBgDir;
    // 新版微商城海报存放目录
    private $_newMallPosterDir;
    //产品海报存放目录
    private $_proPosterDir;
    //产品海报背景图存放目录
    private $_proPosterBgDir;
    //临时海报图存放目录
    private $_tmpPosterDir;
    //图片域名
    private $_domain;
    //七牛云操作对象
    private $_qiniu;
    //七牛云配置
    private $_qiniuConf;
    //修补图片大小
    private $_fixSize = 0;
    //抢购活动id
    private $_seckillId = 0;
    //推广者id
    private $_parentId = 0;
    //海报类型
    private $_posterType = [0 => '微商城海报', 1 => '产品海报', 2 => '限时抢购海报', 3 => '砍价海报', 4 => '拼团海报'];
    //海报下载/显示/发送的选项
    private $_requestOptions = [];

    //用户登录信息
    private $loginInfo = [];

    public function __construct()
    {
        $this->_mallPosterDir    = 'poster/mallPoster/';
        $this->_mallPosterBgDir  = 'poster/mallPosterBg/';
        $this->_newMallPosterDir = 'poster/newMallPoster/';
        $this->_proPosterDir     = 'poster/proPoster/';
        $this->_proPosterBgDir   = 'poster/proPosterBg/';
        $this->_tmpPosterDir     = 'tmp/poster/proPosterBg/';

        $this->_qiniu     = new QiniuProcess();
        $this->_qiniuConf = $this->_qiniu->getConfig();

        $unNeed = $this->_unNeedLoginList();

        if (!in_array(I('a'), $unNeed)) {
            //进行登录判断
            $this->isLogin('ajax');
            $this->loginInfo = $this->getLoginInfo('ajax', false, false);

            $this->_mid     = $this->loginInfo['sid'];
            $this->_account = $this->loginInfo['saccount'];
        }
    }

    /**
     * 微商城海报二维码
     * @return file
     */
    public function mallQrCode($source = false)
    {

        $url      = MOBILE_DOMAIN . self::INDEX_URL;
        $indexUrl = str_replace('wx.', $this->_account . '.', $url);

        $qrCode = new QrCode();
        $qrCode->setText($indexUrl)
               ->setSize(self::QRCODE_WIDTH - self::QRCODE_P * 2)
               ->setPadding(self::QRCODE_P)
               ->setErrorCorrection('high')
               ->setForegroundColor(['r' => 0, 'g' => 0, 'b' => 0, 'a' => 0])
               ->setBackgroundColor(['r' => 255, 'g' => 255, 'b' => 255, 'a' => 0])
               ->setLabelFontSize(16)
               ->setImageType('png');

        //返回图片资源
        if ($source) {
            return $qrCode->getImage();
        }

        header('Content-Type: ' . $qrCode->getContentType());
        $qrCode->render();
    }

    /**
     * 输出微商城海报
     * <AUTHOR>
     * @date   2018-04-17
     */
    public function mallPoster()
    {
        $mallPoster = (new \Model\Mall\Poster())->getOutputMallPoster($this->_mid);

        if (!$mallPoster) {
            $this->apiReturn(204, [], '参数错误');
        }

        $manager = new Image(array('driver' => 'imagick'));
        echo $manager->make(file_get_contents($mallPoster['url'] . '?v=' . time()))->response('png');
    }

    /**
     * 获取微商城海报信息（用于编辑）
     * <AUTHOR>
     * @date   2018-04-03
     */
    public function getMallPosterForEdit()
    {

        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        $posterBiz = new PosterBiz();
        $result    = $posterBiz->getMallPosterForEdit($this->_mid, $id);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 保存微商城海报
     * @return array
     */
    public function saveMallPoster()
    {
        //简介
        $desc = I('desc');
        if (!$desc) {
            $this->apiReturn(200, [], '参数缺失');
        }
        // 新版海报标识
        $isNew = I('isnew', 0, 'intval');

        //文件名
        if ($isNew) {
            $filename = $this->_newMallPosterDir . $this->_uuid() . '.' . self::EXT;
        } else {
            $filename = $this->_mallPosterDir . $this->_uuid() . '.' . self::EXT;
        }

        $url = $this->_uploadToQiniu($filename);

        if ($url) {
            $return = [
                'url' => $url,
            ];

            $this->apiReturn(200, $return);
        } else {
            $this->apiReturn(204, [], '海报上传失败');
        }
    }

    /**
     * 保存微商城海报背景图
     *
     * @return array
     */
    public function saveMallPosterBg()
    {
        //简介
        $desc = I('desc');
        if (!$desc) {
            $this->apiReturn(200, [], '参数缺失');
        }

        //文件名
        $filename = $this->_mallPosterBgDir . $this->_uuid() . '.' . self::EXT;

        $url               = $this->_uploadToQiniu($filename);
        $returnData['url'] = $url;
        if ($url) {
            $this->apiReturn(200, $returnData, '海报背景图上传成功');
        } else {
            $this->apiReturn(204, [], '海报背景图上传失败');
        }
    }

    private function _uuid() {
        $charid = md5(uniqid(mt_rand(), true));
        $hyphen = chr(45);// "-"
        $uuid = substr($charid, 0, 8).$hyphen
                .substr($charid, 8, 4).$hyphen
                .substr($charid,12, 4).$hyphen
                .substr($charid,16, 4).$hyphen
                .substr($charid,20,12);
        return $uuid;
    }

    /**
     * 记录二维码到背景图的x,y轴的相对距离
     *
     * @param  int  $x  二维码到背景图的x轴的相对距离
     * @param  int  $y  二维码到背景图的y轴的相对距离
     * @param  int  $url  海报图url
     */
    public function saveXY()
    {
        //获取请求参数对象
        $request   = $this->_getRequestObject();
        $posterBiz = new PosterBiz();
        $result    = $posterBiz->savePosterInfo($this->_mid, $request, 0);

        if ($result['code'] == 200) {
            $this->apiReturn(200, $result['data'], '上传成功');
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 产品列表
     *
     * @param  string  $keyword    搜索关键词
     */
    public function getProducts() {
        $keyword  = I('keyword', '');
        $pageNum  = I('page', 1, 'intval');
        $pageSize = I('size', 10, 'intval');

        $return = [
            'list' => [],
            'totel' => 0
        ];

        $result = (new \Business\Product\ProductList())->getListV2($this->_mid, $pageNum, $pageSize, '', $keyword, 'A,B,C,H,F,L,G,J,I', '', '', '', '', '');
        if (empty($result['lands'])) {
            $this->apiReturn(200, $return);
        }

        $list = [];
        foreach ($result['lands'] as $item) {
            $list[] = [
                'id'        => $item['lid'],
                'title'     => $item['title'],
                'apply_sid' => $item['apply_did'],
                'p_type'    => $item['ptype'],
                'dname'     => $item['dname'],
            ];
        }

        $return = [
            'list'  => $list,
            'totel' => 0
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 获取景区名称
     *
     * @param  int  $lid  景区id
     *
     * @return array
     */
    public function getProductName()
    {
        $lid = I('lid', 0, 'intval');

        if (!$lid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $land = (new \Model\Product\Land())->getLandInfo($lid, false, 'title');

        if (!$land) {
            $this->apiReturn(204, [], '查不到产品');
        }

        $return = ['productName' => $land['title']];

        $this->apiReturn(200, $return);
    }

    /**
     * 获取商城海报列表
     * <AUTHOR>
     * @date   2018-04-03
     */
    public function mallPosterList()
    {
        //当前页
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');
        // 搜索词
        $keyword = I('keyword', '', 'strval');

        $posterBiz = new PosterBiz();
        $result    = $posterBiz->getMallPosterList($this->_mid, $page, $size, $keyword);

        //图片兼容https
        foreach ($result['data']['list'] as $key => &$value) {
            $value['url'] = images_url_https($value['url']);
        }

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取我的产品海报列表
     *
     * @param  int  $page  当前页
     * @param  int  $pageSize  每页显示条数
     * @param  string  $keyword  搜索关键词
     */
    public function myProPosters()
    {
        //当前页
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('pageSize', 10, 'intval');
        //产品关键字
        $keyword = I('keyword', '', 'strval');

        $httpInfo = explode('.', $_SERVER['HTTP_HOST']);

        if (is_numeric($httpInfo[0])) {
            $microplat = 1;
        } else {
            $microplat = 0;
        }

        $posterBiz = new PosterBiz();
        $result    = $posterBiz->getMyProPosterList($this->_mid, $page, $size, $keyword, $microplat);

        if ($result['code'] == 200) {
            $data         = $result['data'];
            $data['list'] = $data['list'] ?: [];
            //图片兼容https
            foreach ($data['list'] as $key => &$value) {
                $value['url'] = images_url_https($value['url']);
                if (!empty($item['new_url'])) {
                    $item['new_url'] = images_url_https($value['new_url']);
                }
            }
            foreach ($data['list'] as &$item) {
                $extra       = json_decode($item['extra'], true);
                $item['url'] = $this->_water($item['url'], $extra, 0, 1);
                if (!empty($item['new_url'])) {
                    $item['new_url'] = $this->_water($item['new_url'], $extra, 0, 1);
                }
            }
            $this->apiReturn(200, $data);
        } else {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
    }

    /**
     *  获取我的供应商产品海报列表
     * <AUTHOR>
     * @date 2019/10/17
     *
     * @return array
     */
    public function supplyProPosters()
    {
        //当前页
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('pageSize', 10, 'intval');
        //产品关键字
        $keyword = I('keyword', '', 'strval');

        $customerId = $this->loginInfo['customerId'] ?? 0;
        if (!empty($customerId)) {
            $memberBz   = new \Business\Mall\Member();
            $highInfo   = $memberBz->getMemberHighRoleInfo($customerId, $this->_mid);
            $this->_mid = $highInfo['data']['memberInfo']['id'];
        }
        $posterBiz = new PosterBiz();
        $result    = $posterBiz->getSupplyProPosterList($this->_mid, $page, $size, $keyword);

        if ($result['code'] == 200) {
            $data         = $result['data'];
            $data['list'] = $data['list'] ?: [];
            //图片兼容https
            foreach ($data['list'] as $key => &$value) {
                $value['url'] = images_url_https($value['url']);
            }
            //TODO:移动到business
            foreach ($data['list'] as &$item) {
                $extra       = json_decode($item['extra'], true);
                $item['url'] = $this->_water($item['url'], $extra, 0, 1);
            }

            $this->apiReturn(200, $data);
        } else {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
    }

    /**
     * 置顶
     * <AUTHOR>
     * @date   2018-03-28
     */
    public function stick()
    {
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        $posterBiz = new PosterBiz();
        $result    = $posterBiz->stick($this->_mid, $id);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 取消置顶
     * <AUTHOR>
     * @date   2018-03-28
     */
    public function unStick()
    {
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        $posterBiz = new PosterBiz();
        $result    = $posterBiz->unStick($this->_mid, $id);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 上架海报
     * <AUTHOR>
     * @date   2018-03-28
     */
    public function putaway()
    {
        $id   = I('id', 0, 'intval');
        $type = I('type');
        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        $posterBiz = new PosterBiz();
        $result    = $posterBiz->putaway($this->_mid, $id, $type);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 下架微商城海报
     * <AUTHOR>
     * @date   2018-03-28
     */
    public function soldout()
    {
        $id   = I('id', 0, 'intval');
        $type = I('type');
        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        $posterBiz = new PosterBiz();
        $result    = $posterBiz->soldout($this->_mid, $id, $type);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 微商城推广海报(链接指向供应商的微商城，但是显示昵称是会员本身)
     * <AUTHOR>
     * @date   2018-01-18
     */
    public function promotedMallPoster()
    {
        $host = $this->_parseBelongAccount(true);

        $mallPoster = (new \Model\Mall\Poster())->getMallPoster($host['memberId']);

        if ($mallPoster) {
            $this->_account  = $host['account'];
            $this->_parentId = $this->_mid;
            //获得七牛上的图链接
            $imgLink = $this->_getSupplyPosterUrl($mallPoster, $this->_mid);
            // 新版链接
            $newLink = $this->_getSupplyPosterUrl($mallPoster, $this->_mid, 0, 1);

            if (filter_var($imgLink, FILTER_VALIDATE_URL) AND filter_var($newLink, FILTER_VALIDATE_URL)) {
                $this->apiReturn(200, ['url' => $imgLink, 'new_url' => $newLink]);
            } else {
                $this->apiReturn(204, [], '海报获取失败');
            }
        }

        $this->apiReturn(204, [], '供应商没有制作微商城海报');
    }

    /**
     * 获取全民营销产品推广海报
     * <AUTHOR>
     * @date   2018-01-15
     */
    public function promotedProPosters()
    {
        $host = $this->_parseBelongAccount(true);
        //当前页
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');
        // 搜索词
        $keyword = I('keyword', '', 'strval');

        if ($size > 20) {
            $this->apiReturn(204, [], '超出每页条数');
        }

        $posterBiz = new PosterBiz();
        $result    = $posterBiz->getPromotedProPosterList($host['memberId'], $page, $size, $keyword);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取全民营销抢购推广海报
     * <AUTHOR>
     * @date   2018-01-15
     */
    public function promotedSeckillPosters()
    {
        $host = $this->_parseBelongAccount(true);
        //当前页
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');
        // 搜索词
        $keyword = I('keyword', '', 'strval');

        if ($size > 20) {
            $this->apiReturn(204, [], '超出每页条数');
        }

        $seckillBiz = new Seckill();
        $result     = $seckillBiz->promotedSeckillPosters($host['memberId'], $page, $size, $keyword);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取我的产品海报二维码(编辑用)
     *
     * @param  bool  $source  是否返回图片资源
     * @param  int  $lid  景区id
     * @param  int  $sid  当前操作用户id
     * @param  int  $posterId  海报id
     * @param  int  $aid  上级供应商id
     * @param  int  $pid  门票pid
     */
    public function proPosterQrCode()
    {
        $lid = I('lid', 0, 'intval');
        $sid = I('sid', 0, 'intval');

        if (!$lid || !$sid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $qrCode = $this->_createQrcodeForPro($lid, $sid, 0, 0);
        //直接输出图片给前端
        header('Content-Type: ' . $qrCode->getContentType());
        $qrCode->render();
    }

    /**
     * 获取抢购活动的二维码
     */
    public function seckillQrCode()
    {
        $this->mallQrCode();
    }

    /**
     * 获取海报信息(海报编辑接口)
     * <AUTHOR>
     * @date   2018-03-28
     */
    public function getProPosterForEdit()
    {

        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        $posterBiz = new PosterBiz();
        $result    = $posterBiz->getProPosterForEdit($this->_mid, $id);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 保存产品海报
     */
    public function saveProPoster()
    {
        //产品id
        $lid = I('get.lid', 0, 'intval');
        //上级供应商id
        $sid = I('get.sid', 0, 'intval');
        // 新版海报标识
        $isNew = I('get.isnew', 0, 'intval');

        if (!$lid || !$sid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $identifyArr = [$this->_mid, $lid, $sid];
        $posterBiz   = new PosterBiz();
        $result      = $posterBiz->uploadPoster($identifyArr, 'final', 'png', $isNew);

        if ($result['code'] == 200) {
            $this->apiReturn(200, ['url' => $result['data']]);
        } else {
            $this->apiReturn(204, [], '产品海报上传失败');
        }
    }

    /**
     * 保存产品海报背景图
     */
    public function saveProPosterBg()
    {
        //产品id
        $lid = I('get.lid', 0, 'intval');
        //供应商id
        $sid = I('get.sid', 0, 'intval');
        // 判断编辑
        $isEdit = I('get.isedit', 0, 'intval');

        if (!$lid || !$sid) {
            $this->apiReturn(204, [], '参数错误');
        }
        $postModel = new \Model\Mall\Poster();
        $result    = $postModel->getAllProPoster($this->_mid);
        $lidArr    = array_column($result, 'lid');
        if (in_array($lid, $lidArr) AND !$isEdit) {
            $this->apiReturn(204, [], '该产品已有海报了，请更换产品');
        }

        $identifyArr = [$this->_mid, $lid, $sid];
        $posterBiz   = new PosterBiz();
        $result      = $posterBiz->uploadPoster($identifyArr, 'bg');

        if ($result['code'] == 200) {
            $this->apiReturn(200, ['url' => $result['data']]);
        } else {
            $this->apiReturn(204, [], '产品海报背景图上传失败');
        }
    }

    /**
     * 保存抢购海报
     */
    public function saveSeckillPoster()
    {

        $pid = I('get.pid', 0, 'intval');
        if (!$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $identifyArr = [$this->_mid, $pid, $this->_mid];
        $posterBiz   = new PosterBiz();
        $result      = $posterBiz->uploadPoster($identifyArr);

        if ($result['code'] == 200) {
            $this->apiReturn(200, ['url' => $result['data']]);
        } else {
            $this->apiReturn(204, [], '抢购海报上传失败');
        }
    }

    /**
     * 保存砍价海报
     * <AUTHOR>
     * @date   2018-03-09
     */
    public function saveCutPricePoster()
    {
        $pid = I('get.pid', 0, 'intval');
        if (!$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $identifyArr = ['cut-price', $this->_mid, $pid, $this->_mid];
        $posterBiz   = new PosterBiz();
        $result      = $posterBiz->uploadPoster($identifyArr);

        if ($result['code'] == 200) {
            $this->apiReturn(200, ['url' => $result['data']]);
        } else {
            $this->apiReturn(204, [], '砍价海报上传失败');
        }
    }

    /**
     * 保存拼团海报
     * <AUTHOR>
     * @date   2018-05-21
     */
    public function saveGroupBookingPoster()
    {
        $pid = I('get.pid', 0, 'intval');
        if (!$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $identifyArr = ['group-booking', $this->_mid, $pid, $this->_mid];
        $posterBiz   = new PosterBiz();
        $result      = $posterBiz->uploadPoster($identifyArr);

        if ($result['code'] == 200) {
            $this->apiReturn(200, ['url' => $result['data']]);
        } else {
            $this->apiReturn(204, [], '拼团海报上传失败');
        }
    }

    /**
     * 保存抢购海报背景图
     */
    public function saveSeckillBg()
    {

        $pid = I('get.pid', 0, 'intval');

        if (!$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $identifyArr = [$this->_mid, $pid, $this->_mid];
        $posterBiz   = new PosterBiz();
        $result      = $posterBiz->uploadPoster($identifyArr, 'bg');

        if ($result['code'] == 200) {
            $this->apiReturn(200, ['url' => $result['data']]);
        } else {
            $this->apiReturn(204, [], '抢购海报背景图上传失败');
        }
    }

    /**
     * 保存砍价海报背景图
     */
    public function saveCutPriceBg()
    {

        $pid = I('get.pid', 0, 'intval');
        if (!$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $identifyArr = ['cut-price', $this->_mid, $pid, $this->_mid];
        $posterBiz   = new PosterBiz();
        $result      = $posterBiz->uploadPoster($identifyArr, 'bg');

        if ($result['code'] == 200) {
            $this->apiReturn(200, ['url' => $result['data']]);
        } else {
            $this->apiReturn(204, [], '砍价海报背景图上传失败');
        }
    }

    /**
     * 保存拼团海报背景图
     */
    public function saveGroupBookingBg()
    {

        $pid = I('get.pid', 0, 'intval');
        if (!$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $identifyArr = ['group-booking', $this->_mid, $pid, $this->_mid];
        $posterBiz   = new PosterBiz();
        $result      = $posterBiz->uploadPoster($identifyArr, 'bg');

        if ($result['code'] == 200) {
            $this->apiReturn(200, ['url' => $result['data']]);
        } else {
            $this->apiReturn(204, [], '拼团海报背景图上传失败');
        }
    }

    /**
     * 记录产品二维码到背景图的x,y轴的相对距离
     *
     * @param  int  $x  二维码到背景图的x轴的相对距离
     * @param  int  $y  二维码到背景图的y轴的相对距离
     * @param  int  $lid  景区id
     * @param  int  $sid  供应商id
     * @param  string  $proUrl  产品海报url
     * @param  string  $proBgUrl  产品海报背景url
     */
    public function saveProXY()
    {
        //获取请求参数对象
        $request   = $this->_getRequestObject();
        $posterBiz = new PosterBiz();
        $result    = $posterBiz->savePosterInfo($this->_mid, $request, 1);

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], '上传成功');
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 记录抢购二维码到背景图的x,y轴的相对距离
     * <AUTHOR>
     * @date   2017-07-15
     */
    public function saveSeckillXY()
    {
        //获取请求参数对象
        $request   = $this->_getRequestObject();
        $posterBiz = new PosterBiz();
        $result    = $posterBiz->savePosterInfo($this->_mid, $request, 2);

        if ($result['code'] == 200) {
            $data = ['poster_id' => $result['data']['id']];
            $this->apiReturn(200, $data, '上传成功');
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 记录砍价二维码到背景图的x,y轴的相对距离
     * <AUTHOR>
     * @date   2017-07-15
     */
    public function saveCutPriceXY()
    {
        //获取请求参数对象
        $request   = $this->_getRequestObject();
        $posterBiz = new PosterBiz();
        $result    = $posterBiz->savePosterInfo($this->_mid, $request, 3);

        if ($result['code'] == 200) {
            $data = ['poster_id' => $result['data']['id']];
            $this->apiReturn(200, $data, '上传成功');
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 记录拼团二维码到背景图的x,y轴的相对距离
     * <AUTHOR>
     * @date   2018-05-21
     */
    public function saveGroupBookingXY()
    {
        //获取请求参数对象
        $request   = $this->_getRequestObject();
        $posterBiz = new PosterBiz();
        $result    = $posterBiz->savePosterInfo($this->_mid, $request, 4);

        if ($result['code'] == 200) {
            $data = ['poster_id' => $result['data']['id']];
            $this->apiReturn(200, $data, '上传成功');
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 删除海报
     *
     * @param  int  $id  海报id
     */
    public function delete()
    {
        $id   = I('id', 0, 'intval');
        $type = I('type', 1, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($type == 0) {
            $res = (new \Model\Mall\Poster())->getAllMallPoster($this->_mid);
            if (count($res) <= 1) {
                $this->apiReturn(204, [], '微商城海报只剩1张，所以删除失败');
            }
        }

        $result = (new \Model\Mall\Poster())->deleteOnePoster($this->_mid, $id);

        if ($result) {
            $this->apiReturn(200, [], '删除成功');
        } else {
            $this->apiReturn(204, [], '删除失败,请重试');
        }
    }

    /**
     * 海报下载
     *
     * @param  int  $id  海报id
     * @param  int  $mid  会员id
     */
    public function downLoad()
    {
        //海报id
        $id = I('id', 0, 'intval');

        //上级供应商id
        $aid = I('aid', 0, 'intval');
        // 新版海报标识
        $isNew = I('isnew', 0, 'intval');
        $isWxQrCode = I('isWxQrCode', 0, 'intval'); //是否为微信二维码类型海报下载 0：否  1：是
        //下载类型为微信海报二维码时该参数生效
        $validityType = I('validityType', 1, 'intval'); //有效期类型 1：设置有效期为30天 2：不限期限
        //下载用户id,要取主账号id
        $memberInfo = $this->getLoginInfo();
        $mid        = $memberInfo['sid'];
        if (!$id || !$mid || empty($validityType)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $poster = (new \Model\Mall\Poster())->getOnePoster($id);
        if (!$poster) {
            $this->apiReturn(204, [], '海报不存在');
        }

        if ($this->_isIos()) {
            header('Content-type: image/' . self::EXT);
        } else {
            //下载头部
            Header("Content-type:  application/octet-stream ");
            Header("Accept-Ranges:  bytes ");
            Header("Content-Disposition:  attachment;  filename= 海报.png");
        }
        $this->_parseRequestOptions();
        $this->_downLoadSupplyPoster($poster, $mid, $aid, $isNew, $isWxQrCode, $validityType);
    }

    /**
     * 显示海报图
     * <AUTHOR>
     * @date   2017-06-27
     */
    public function showPoster()
    {
        //海报id
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }
        // 新版海报标识
        $isNew = I('isnew', 0, 'intval');

        $poster = (new \Model\Mall\Poster())->getOnePoster($id);
        if (!$poster) {
            $this->apiReturn(204, [], '海报不存在');
        }
        $this->_parseRequestOptions();

        header('Content-type: image/' . self::EXT);
        $this->_downLoadSupplyPoster($poster, $this->_mid, I('aid', 0), $isNew);
    }

    /**
     * 显示带推广功能的海报图
     * <AUTHOR>
     * @date   2018-01-17
     */
    public function showPosterForPromoted()
    {

        $host = $this->_parseBelongAccount();
        //海报id
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        $poster = (new \Model\Mall\Poster())->getOnePoster($id);
        if (!$poster) {
            $this->apiReturn(204, [], '海报不存在');
        }

        $this->_account  = $host['account'];
        $this->_parentId = $this->_mid;

        $this->_parseRequestOptions();
        //获得七牛上的图链接
        $imgLink = $this->_getSupplyPosterUrl($poster, $this->_mid, I('aid', 0));
        // 只有微商城海报和产品海报才获取新版海报
        if (intval($poster['type']) == 0 OR intval($poster['type']) == 1) {
            // 新版链接
            $newLink = $this->_getSupplyPosterUrl($poster, $this->_mid, I('aid', 0), 1);
        } else {
            $newLink = '';
        }

        if (filter_var($imgLink, FILTER_VALIDATE_URL)) {
            $this->apiReturn(200, ['url' => $imgLink, 'new_url' => $newLink]);
        } else {
            $this->apiReturn(204, [], '图片获取失败');
        }
    }

    /**
     * 发送微商城海报到公众号
     *
     * <AUTHOR>
     * @date   2017-04-25
     */
    public function sendMallPosterToWechat()
    {

        $openid = I('session.openid', '');
        $appid  = I('session.wechat_appid', '');

        if (!$openid || !$appid) {
            $this->apiReturn(204, [], '无发送权限');
        }

        //获取微商城海报
        $poster = (new \Model\Mall\Poster())->getMallPoster($this->_mid);

        if (!$poster) {
            $this->apiReturn(204, [], '海报资源不存在');
        }

        $this->_sendToWechatAction($poster['id'], $this->_mid, 0, $openid, $appid);
        $this->apiReturn(200, [], '发送成功');
    }

    /**
     * 发送海报到公众号(统一入口)
     * <AUTHOR>
     * @date   2018-03-24
     */
    public function sendPosterToWechat()
    {

        //海报id
        $posterId = I('posterId', 0, 'intval');
        //上级供应商id
        $aid    = I('aid', 0, 'intval');
        $openid = I('session.openid', '');
        $appid  = I('session.wechat_appid', '');
        $isNew  = I('isNew', 0, 'intval');
        //微平台发送海报兼容新版新版数据（解决二维码位置错误问题）-------------yangjianhui
        //1：抢购   2：砍价    3：拼团
        $activeType = I('activityType', 0, 'intval');
        if (!$openid || !$appid || !$posterId) {
            $this->apiReturn(204, [], '无发送权限');
        }

        if (function_exists('fastcgi_finish_request')) {
            fastcgi_finish_request();
        }

        $this->_parseRequestOptions();
        $this->_sendToWechatAction($posterId, $this->_mid, $aid, $openid, $appid, $isNew, $activeType);
    }

    /**
     * 发送推广海报
     * <AUTHOR>
     * @date   2018-01-17
     */
    public function sendPromotedPosterToWechat()
    {

        $memberId = $this->isLogin('ajax');
        $host     = $this->_parseBelongAccount(true);
        //海报id
        $posterId = I('poster_id', 0, 'intval');
        //上级供应商id
        $aid = I('aid', 0, 'intval');

        if (!$posterId || !$aid) {
            $this->apiReturn(204, [], '参数错误');
        }
        //获取appid
        //获取公众号appid
        $wxOpenModel = new \Model\Wechat\WxOpen();
        $wechat      = $wxOpenModel->getWechatOffiAccInfo($host['memberId'], 'fid');
        if (!$wechat) {
            $this->apiReturn(204, [], '公众号未托管');
        }

        //获取openid
        $allDisModel = new \Model\Mall\AllDis();
        $memberInfo  = $allDisModel->getAllDisMemberInfo($memberId);
        if (!$memberInfo || !$memberInfo['supply_openid']) {
            $this->apiReturn(204, [], '无发送权限');
        }

        $openid         = $memberInfo['supply_openid'];
        $appid          = $wechat['appid'];
        $this->_account = $host['account'];
        //抢购id
        $this->_parseRequestOptions();
        $this->_parentId = $memberId;

        $this->_sendToWechatAction($posterId, $this->_mid, $aid, $openid, $appid);
    }

    /**
     * 发送海报到公众号
     * <AUTHOR>
     * @date   2017-09-06
     *
     * @param  int  $posterId  海报id
     * @param  int  $mid  接收用户id
     * @param  int  $aid  上级供应商id
     * @param  string  $openid  微信openid
     * @param  string  $appid  微信appid
     * @param  int  $isNew  是否为新版微商城
     * @param  int  $activeType  微平台发送海报兼容新版新版数据（解决二维码位置错误问题）1：抢购   2：砍价    3：拼团
     */
    private function _sendToWechatAction($posterId, $mid, $aid, $openid, $appid, $isNew = 0, $activeType = 0)
    {

        //获取产品海报
        $poster = (new \Model\Mall\Poster())->getOnePoster($posterId);

        if (!$poster) {
            $this->apiReturn(204, [], '海报资源不存在');
        }

        $filepath = $this->_replaceImageSoruce($poster, $mid, $aid, $isNew, $activeType);
        if (!$filepath || !is_file($filepath)) {
            $this->apiReturn(204, [], '海报文件生成失败');
        }

        $text = '正在为您生成专属海报，点击"下载"，您可将海报分享到微信好友、群、朋友圈';
        \Library\wechat\core\CustomService::sendText($openid, $text, $appid);

        $result = $this->_sendPosterToWechat('', $openid, $appid, $filepath);

        if ($result['status'] == 1) {
            $this->apiReturn(200, [], '发送成功');
        } else {
            @pft_log("wechat/poster/fail", $result['msg']);
            $this->apiReturn(204, [], $result['msg']);
        }

    }

    /**
     * 设置当前操作用户id
     * <AUTHOR>
     * @date   2017-07-28
     *
     * @param  int  $mid  会员id
     */
    public function setMid($mid)
    {
        $this->_mid = $mid;
    }

    /**
     *
     * <AUTHOR>
     * @date   2017-04-25
     *
     * @param  string  $url  图片链接地址
     * @param  string  $openid  用户openid
     * @param  string  $appid  公众号id
     * @param  string  $tmpPath  是否已经生成临时文件
     *
     * @return array
     */
    private function _sendPosterToWechat($url, $openid, $appid, $tmpPath = '')
    {

        $errMsg = '';
        try {
            //appid是否托管
            $auth = (new \Model\Wechat\open())->getBindInfo($appid);
            if (!$auth) {
                throw new \Exception('未托管公众号');
            }

            if (!$tmpPath) {
                //保存临时文件，用户上传微信素材
                $tmpPath = '/tmp/' . $this->_mid . '-' . time() . '.png';
                @file_put_contents($tmpPath, file_get_contents($url));
            }
            if (!file_exists($tmpPath)) {
                throw new \Exception('文件保存失败');
            }

            //新增临时素材
            $result = \Library\wechat\core\Media::upload($tmpPath, 'image', $appid);
            if (!isset($result['media_id'])) {
                throw new \Exception('素材上传失败');
            }

            //调用客服接口发送图片
            $mediaId = $result['media_id'];
            $result  = \Library\wechat\core\CustomService::sendImage($openid, $mediaId, $appid);

            if (!$result || $result['errcode'] != 0) {
                @pft_log("wechat/poster/fail", json_encode($result));
                throw new \Exception('发送失败');
            }
        } catch (\Exception $e) {
            $errMsg = $e->getMessage();
        } finally {
            //删除临时文件
            file_exists($tmpPath) && @unlink($tmpPath);
            if ($errMsg) {
                return ['status' => 0, 'msg' => $errMsg];
            } else {
                return ['status' => 1];
            }
        }
    }

    /**
     * 文件是否存在于七牛云
     *
     * @param  string  $filename  文件名
     *
     * @return boolean
     */
    private function _isFileFromQiniu($filename)
    {

        return $this->_qiniu->hasFileExist($filename, 'images', false);

    }

    //获取七牛图片详情
    private function _getFileStat($filename)
    {

        return $this->_qiniu->getFileStat($filename, 'images', false);

    }

    /**
     * 添加水印
     *
     * <AUTHOR>
     * @date   2017-04-24
     *
     * @param  string  $originUrl  原图地址
     * @param  array  $extra  海报的一些扩展信息
     * @param  int  $mid  分销商id
     * @param  int  $thmub  是否缩略
     *
     * @return 图片链接
     */
    private function _water($originUrl, $extra, $mid = 0, $thumb = 0)
    {

        if (ENV == 'PRODUCTION') {
            $waterImg = 'http://images.pft12301.cc/shuiyin.png?v=' . time();
        } else {
            $waterImg = 'http://ol3ooubvm.bkt.clouddn.com/shuiyin.png?v=' . time();
        }

        // 是否返回缩略图
        if ($thumb) {
            $originUrl = \Qiniu\thumbnail($originUrl, 1, 225, 340);
        }

        return $originUrl;
    }

    /**
     * 上传图片到七牛
     *
     * @param  sintrg  $upName  在$_FILES中的名字
     * @param  string  $filename  自定义上传文件名
     *
     * @return mix
     */
    private function _uploadToQiniu($filename, $upName = 'image')
    {

        $result = $this->_qiniu->uploadFile($filename);

        if ($result) {
            return $this->_qiniuConf['images']['domain'] . $filename;
        }

        return false;

    }

    /**
     * 是否来自ios的请求
     *
     * @return boolean
     */
    private function _isIos()
    {

        if (strpos($_SERVER['HTTP_USER_AGENT'], 'iPhone') || strpos($_SERVER['HTTP_USER_AGENT'], 'iPad')) {
            return true;
        }

        return false;
    }

    /**
     * 无需登陆的接口列表(下载等)
     *
     * @return array
     */
    private function _unNeedLoginList()
    {
        return [
            'downLoad',
            'downMallPoster',
            'sendProPosterToWechat',
            'sendJobPoster',
        ];
    }

    /**
     * 下载供应商海报
     *
     * @param  array  $poster  海报信息
     * @param  int  $mid  下载会员id
     * @param  int  $aid  上级供应商id
     * @param  bool  $isNew  是否是新版微商城
     * @param  int  $isWxQrCode 是否为微信二维码类型海报下载 0：否  1：是
     * @param  int $validityType 有效期类型 1：设置有效期为30天 2：不限期限
     *
     * @return [type]         [description]
     */
    private function _downLoadSupplyPoster($poster, $mid, $aid = 0, $isNew = false, $isWxQrCode = 0, $validityType = 1)
    {
        //替换二维码
        $filepath = $this->_replaceImageSoruce($poster, $mid, $aid, $isNew, 0, $isWxQrCode, $validityType);
        $contents = '';
        $errMsg = '';
        try {
            if (!$filepath || !is_file($filepath)) {
                throw new \Exception("图片生成失败");
            }
            $file = fopen($filepath, "rb");
            if (!is_resource($file)) {
                throw new \Exception("图片读取失败");
            }
        } catch (\Exception $e) {
            $errMsg = $e->getMessage();
        } finally {
            if (is_file($filepath)) {
                @unlink($filepath);
            }

            if ($errMsg) {
                exit($errMsg);
            }

            while (!feof($file)) {
                $contents .= fread($file, 8192);
            }
            fclose($file);
            echo $contents;
        }
    }

    private function _getSupplyPosterUrl($poster, $mid, $aid = 0, $isNew = 0)
    {

        //用户是否已经合成过海报
        $imgName = $this->_tmpPosterDir . $this->_mid . '_' . $poster['id'] . '_' . $isNew . '.' . self::EXT;

        //获取文件状态
        $fileStat = $this->_getFileStat($imgName);
        $fresh    = false;
        if ($fileStat) {
            $putTime = substr($fileStat['putTime'], 0, 10);

            if ($putTime < time() - 3600) {
                $fresh = true;
            }
        }

        if (!isset($fileStat['url']) || $fresh) {
            //合成默认海报
            $filepath = $this->_replaceImageSoruce($poster, $mid, $aid, $isNew);

            $errMsg = '';
            try {
                if (empty($poster['new_url']) AND $isNew) {
                    throw new \Exception("海报已失效，请让商家重新创建海报");
                }
                if (!$filepath || !is_file($filepath)) {
                    throw new \Exception("图片生成失败");
                }
                $file = fopen($filepath, "rb");
                if (!is_resource($file)) {
                    throw new \Exception("图片生成失败");
                }
            } catch (\Exception $e) {
                $errMsg = $e->getMessage();
            } finally {
                if (is_file($filepath)) {
                    @unlink($filepath);
                }

                if ($errMsg) {
                    $this->apiReturn(204, [], $errMsg);
                }

                $contents = "";
                while (!feof($file)) {
                    $contents .= fread($file, 8192);
                }
                $imgLink = $this->_qiniu->binaryUpload($imgName, $contents);
            }
        } else {
            $imgLink = $fileStat['url'] . '?v=' . time();
        }

        return $imgLink;
    }

    /**
     * 替换供应商产品海报上的二维码
     *
     * <AUTHOR>
     * @date   2017-04-28
     *
     * @param  array  $poster  产品海报信息
     * @param  int  $mid  分销商id
     * @param  int  $aid  供应商id
     * @param  bool  $isNew  是否为新版微商城
     * @param  int  $activeType  微平台发送海报兼容新版新版数据（解决二维码位置错误问题）1：抢购   2：砍价    3：拼团
     * @param  int  $isWxQrCode 是否为微信二维码类型海报下载 0：否  1：是
     * @param int $validityType 有效期类型 1：设置有效期为30天 2：不限期限
     *
     * @return filepath | false
     */
    private function _replaceImageSoruce($poster, $mid, $aid = 0, $isNew = false, $activeType = 0, $isWxQrCode = 0, $validityType = 1)
    {
        $extra = json_decode($poster['extra'], true);
        if ($activeType == 1) {
            $extra['w']          = $extra['w'] * 2;
            $extra['h']          = $extra['h'] * 2;
            $extra['x']          = $extra['x'] * 2;
            $extra['y']          = $extra['y'] * 2;
            $extra['text_x']     = $extra['text_x'] * 2;
            $extra['text_y']     = $extra['text_y'] * 2;
            $extra['font_size']  = $extra['font_size'] * 2;
            $extra['text_width'] = $extra['text_width'] * 2;
        }
        if ($isNew) {
            $url = $this->_water($poster['new_url'], $extra, $mid);
        } else {
            if (empty($poster['url'])) {
                $poster['url'] = $poster['bg_url'];
            }
            $url = $this->_water($poster['url'], $extra, $mid);
        }

        $err = 0;
        try {
            $manager = new Image(array('driver' => 'imagick'));
            if (strpos($url, "https://") !== false) {
                $url = str_replace("https://", "http://", $url);
            }
            $image_1 = $manager->make(@file_get_contents($url . '?v=' . time()));
            if (!is_object($image_1)) {
                throw new \Exception("图片加载失败");
            }
            //如果是下载微信二维码类型海报
            if ($isWxQrCode) {
                $weChatTrusteeship = new WechatTrusteeship();
                $type = 1; //默认是产品海报
                switch ($poster['type']) {
                    case 0:
                        //微商城海报二维码
                        $type = 9;
                        break;
                    case 1:
                        //产品海报二维码
                        $type = 1;
                        break;
                    case 3:
                        //砍价活动二维码
                        $type = 4;
                        break;
                }
                $tmpAid = $aid == $mid ? 0 : $aid;
                $imageBase64 = $weChatTrusteeship->createWeChatImageToBase64($mid, 0, $poster['lid'],
                    $this->_requestOptions->cut_id, $tmpAid, $type, $validityType);
                //生成成功，修改图片大小
                if ($imageBase64['code'] == 200) {
                    preg_match('/^(data:\s*image\/(\w+);base64,)/', $imageBase64['data']['base64'], $imageInfo);
                    $tmpPath = IMAGE_UPLOAD_DIR;
                    if (!file_exists($tmpPath)) {
                        //检查是否有该文件夹，如果没有就创建，并给予最高权限
                        mkdir($tmpPath, 0700);
                    }
                    $tmpQrPath = $tmpPath . time() . "-wx" . ".jpg";
                    if (file_put_contents($tmpQrPath, base64_decode(str_replace($imageInfo[1], '', $imageBase64['data']['base64'])))) {
                        //调整图片大小
                        $qrPath = $tmpPath . $mid . '-qr1-' . time() . '.png';
                        $this->_resizeImage("jpg", $tmpQrPath, $extra['w'], $extra['h'], $qrPath);
                        $qrPath = is_file($qrPath) ? $qrPath : "";
                        @unlink($tmpQrPath);
                    }
                }
                if (!is_file($qrPath)) {
                    throw new \Exception("二维码临时文件生成失败");
                }
            } elseif (!$isNew || ($isNew && $activeType == 0)) {
                // 此处新版微商城海报不应该替换二维码，否则会替换成旧版的二维码
                //上级供应商id
                $aid = $aid ?: $poster['member_id'];

                //获取不同类型的二维码对象
                $qrCode = $this->_createQrcodeFromType($poster, $mid, $aid, $extra, $isNew);
                if (!is_object($qrCode)) {
                    throw new \Exception("二维码生成失败");
                }

                $qrPath = '/tmp/' . $mid . '-qr-' . time() . '.png';
                $qrCode->save($qrPath);
                if (!is_file($qrPath)) {
                    throw new \Exception("二维码临时文件生成失败");
                }
            }
            if (is_file($qrPath)) {
                //替换二维码
                $image_1->insert($qrPath, 'top-left', $extra['x'], $extra['y']);
                //加水印
                if (isset($extra['style']) && $extra['style'] == 2) {
                    $image_1 = $this->_waterForStyle2($image_1, $manager, $mid);
                }
            }

            //将替换后的图保存到指定文件
            $filepath = '/tmp/' . $mid . '-' . time() . '.png';
            $image_1->save($filepath);
            if (!is_file($filepath)) {
                throw new \Exception("替换图生成失败");
            }
        } catch (\Exception $e) {
            $err = 1;
        } finally {
            if (isset($qrPath) && is_file($qrPath)) {
                //@unlink($qrPath);
            }

            return $err ? false : $filepath;
        }
    }

    private function _waterForStyle2($sourceImg, $manager, $mid)
    {

        if ($mid > 0) {
            $memModel   = new Member('slave');
            $memberInfo = $memModel->getMemberInfo($mid, 'id', 'dname,headphoto');
            if ($memberInfo) {
                $name      = $memberInfo['dname'];
                $headphoto = $memberInfo['headphoto'];
            }
        } else {
            $name      = I('session.dname');
            $headphoto = I('session.headphoto');
        }

        if ($name && $headphoto) {
            if ($headphoto[0] == '/') {
                $headphoto = 'http:' . $headphoto;
            }
            //插入头像
            if (strpos($headphoto, "https://") !== false) {
                $headphoto = str_replace("https://", "http://", $headphoto);
            }
            $head = $manager->make($headphoto);
            $head->resize(72, 72);
            // $head->circle(36, 10, 10);
            $sourceImg->insert($head, 'top-left', 30, 1064);
            //商家名称
            $sourceImg->text($name, 120, 1116, function ($font) {
                $font->size(32);
                $font->file('/var/www/html/Service/Public/font/heiti.ttf');
                $font->color('#262d37');
            });
        }

        return $sourceImg;
    }

    /**
     * 生成不同类型的海报二维码
     * <AUTHOR>
     * @date   2018-03-08
     *
     * @param  array  $poster海报详情
     * @param  int  $mid  分销商id
     * @param  int  $aid  上级id
     * @param  array  $extra  额外信息
     *
     * @return mix
     */
    private function _createQrcodeFromType($poster, $mid, $aid, $extra, $isNew = false)
    {

        if (!in_array($poster['type'], array_keys($this->_posterType))) {
            return false;
        }

        switch ($poster['type']) {
            case 0:
                //微商城海报二维码
                $qrCode = $this->_createQrcodeForMall($mid, $extra);
                break;
            case 1:
                //产品海报二维码
                $qrCode = $this->_createQrcodeForPro($poster['lid'], $mid, $poster['id'], $aid, $extra, $isNew);
                break;
            case 2:
                //抢购海报二维码
                $qrCode = $this->_createQrcodeForSeckill($poster['pid'], $mid, $aid, $extra, $poster['id']);
                break;
            case 3:
                //砍价活动二维码
                $qrCode = $this->_createQrcodeForCutPrice($poster['pid'], $mid, $aid, $extra);
                break;
            case 4:
                //拼团活动二维吗
                $qrCode = $this->_createQrcodeForGroupBooking($poster['pid'], $mid, $aid, $extra);
                break;
            default:
                return false;
        }

        return $qrCode;
    }

    /**
     * 创建产品海报二维码
     * <AUTHOR>
     * @date   2017-09-05
     *
     * @param  int  $lid  景点id
     * @param  int  $mid  分销商id
     * @param  int  $posterId  海报id
     * @param  int  $aid  上级供应商id
     *
     * @return object
     */
    private function _createQrcodeForPro($lid, $mid, $posterId, $aid = 0, $extra = [], $isNew = false)
    {

        if (!$lid || !$mid) {
            return false;
        }
        //获取微商城账号
        if ($this->_account) {
            //已登录
            $account = $this->_account;
        } else {
            //未登录
            $account = $this->_parseAccountByMid($mid);
            if (!$account) {
                return false;
            }
        }
        //获取产品类型
        $landModel = new Land('slave');
        $landInfo  = $landModel->getLandInfo($lid, false, 'p_type');
        if ($landInfo && $landInfo['p_type'] == 'H') {
            $url = MOBILE_DOMAIN . self::H_BOOKING_URL;
        } elseif ($landInfo && $landInfo['p_type'] == 'J') {
            $url = MOBILE_DOMAIN . self::J_BOOKING_URL;
        } else {
            $url = MOBILE_DOMAIN . self::BOOKING_URL;
        }

        $replace    = [
            'wx.'   => $account . '.',
            '{lid}' => $lid,
            '{aid}' => $aid,
        ];
        $bookingUrl = str_replace(array_keys($replace), array_values($replace), $url);
        //植入poster_id
        if ($posterId) {
            $bookingUrl .= '&poster_id=' . $posterId;
        }
        if ($isNew) {
            if ($landInfo && $landInfo['p_type'] == 'J') {
                $bookingUrl  = "https://".$account.".12301.cc/h5/specialty/".$lid;
            } else {
                $bookingUrl  = "https://".$account.".12301.cc/h5/prod/".$lid;
            }
            //自己的海报没有必要带aid，这边做个过滤
            if ($aid && $aid != $mid) {
                $bookingUrl .= "?aid=" . $aid;
            }
        }

        return $this->_createQrcode($bookingUrl, $extra);
    }

    /**
     * 创建抢购活动海报二维码
     * <AUTHOR>
     * @date   2017-09-05
     *
     * @param  int  $pid  门票pid
     * @param  int  $mid  分销商id
     * @param  int  $aid  上级供应商id
     *
     * @return object
     */
    private function _createQrcodeForSeckill($pid, $mid, $aid, $extra, $posterId = 0)
    {

        /*
        if (!$pid || !$mid || !$this->_requestOptions->seckill_id) {
            return false;
        }
        //微商城账号
        if ($this->_account) {
            $account = $this->_account;
        } else {
            $account = $this->_parseAccountByMid($mid);
            if (!$account) {
                return false;
            }
        }

        $url = SECKILL_DOMAIN . self::SECKILL_URL;
        $replace = [
            'wx.'       => $account . '.',
            '{account}' => $account,
            '{aid}'     => $aid,
            '{seckill_id}' => $this->_requestOptions->seckill_id
        ];
        $seckillUrl = str_replace(array_keys($replace), array_values($replace), $url);
        //植入poster_id
        if ($posterId) {
            $seckillUrl .= '&poster_id='.$posterId;
        }
        */
        $memberBiz = new \Business\Mall\Poster();
        if (!empty($aid)) {
            $seckillUrl = $memberBiz->getNewMallUrl($mid, 'seckill', $this->_requestOptions->seckill_id, $aid);
        } else {
            $seckillUrl = $memberBiz->getNewMallUrl($mid, 'seckill', $this->_requestOptions->seckill_id);
        }

        return $this->_createQrcode($seckillUrl, $extra);
    }

    /**
     * 创建砍价活动海报二维码
     * <AUTHOR>
     * @date   2017-09-05
     *
     * @param  int  $pid  门票pid
     * @param  int  $mid  分销商id
     * @param  int  $aid  上级供应商id
     *
     * @return object
     */
    private function _createQrcodeForCutPrice($pid, $mid, $aid, $extra)
    {

        //if (!$pid || !$mid || !$this->_requestOptions->cut_id) {
        //    return false;
        //}
        ////微商城账号
        //if ($this->_account) {
        //    $account = $this->_account;
        //} else {
        //    $account = $this->_parseAccountByMid($mid);
        //    if (!$account) {
        //        return false;
        //    }
        //}
        //
        //$url = MOBILE_DOMAIN . self::CUT_PRICE_URL;
        //$replace = [
        //    'wx.'       => $account . '.',
        //    '{aid}'     => $aid,
        //    '{cut_id}'  => $this->_requestOptions->cut_id
        //];
        //$cutUrl = str_replace(array_keys($replace), array_values($replace), $url);

        $memberBiz = new \Business\Mall\Poster();
        $cutUrl = $memberBiz->getNewMallUrl($mid, 'cutprice', $this->_requestOptions->cut_id, $aid);

        return $this->_createQrcode($cutUrl, $extra);
    }

    /**
     * 创建拼团活动海报二维码
     * <AUTHOR>
     * @date   2018-05-21
     *
     * @param  int  $pid  门票pid
     * @param  int  $mid  分销商id
     * @param  int  $aid  上级供应商id
     *
     * @return object
     */
    private function _createQrcodeForGroupBooking($pid, $mid, $aid, $extra)
    {

        if (!$pid || !$mid || !$this->_requestOptions->group_id) {
            return false;
        }
        //微商城账号
        if ($this->_account) {
            $account = $this->_account;
        } else {
            $account = $this->_parseAccountByMid($mid);
            if (!$account) {
                return false;
            }
        }

        $url     = MOBILE_DOMAIN . self::GROUP_BOOKING_URL;
        $replace = [
            'wx.'        => $account . '.',
            '{aid}'      => $aid,
            '{group_id}' => $this->_requestOptions->group_id,
        ];
        $cutUrl  = str_replace(array_keys($replace), array_values($replace), $url);

        return $this->_createQrcode($cutUrl, $extra);
    }

    /**
     * 微商城海报二维码
     * <AUTHOR>
     */
    private function _createQrcodeForMall($mid, $extra)
    {

        if ($mid && !$this->_parentId) {
            //获取用户id
            $memModel   = new Member('slave');
            $memberInfo = $memModel->getMemberInfo($mid, 'id', 'account');
            $account    = $memberInfo['account'];
        } else {
            $host    = $this->_parseBelongAccount();
            $account = $host['account'];
        }

        $url     = MOBILE_DOMAIN . self::INDEX_URL;
        $replace = [
            'wx.' => $account . '.',
        ];
        $mallUrl = str_replace(array_keys($replace), array_values($replace), $url);

        return $this->_createQrcode($mallUrl, $extra);
    }

    private function _createQrcode($url, $extra = [])
    {
        //是否作为推广海报
        $url = $this->_decorateUrlForPromoted($url);

        if (isset($extra['w'])) {
            $padding = $extra['w'] / self::QRCODE_WIDTH * self::QRCODE_P;
            $qrSize  = $extra['w'] - $padding * 2;
        } else {
            $padding = self::QRCODE_P;
            $qrSize  = self::QRCODE_WIDTH - $padding * 2 - 20;
        }

        $qrCode = new QrCode();
        $qrCode->setText($url)
               ->setSize($qrSize)
               ->setPadding($padding)
               ->setErrorCorrection('high')
               ->setForegroundColor(['r' => 0, 'g' => 0, 'b' => 0, 'a' => 0])
               ->setBackgroundColor(['r' => 255, 'g' => 255, 'b' => 255, 'a' => 0])
               ->setLabelFontSize(16)
               ->setImageType('png');

        return $qrCode;
    }

    /**
     * 添加推广者id
     * <AUTHOR>
     * @date   2018-03-08
     *
     * @param  string  $url  url
     *
     * @return string
     */
    private function _decorateUrlForPromoted($url)
    {
        if ($this->_parentId) {
            if (stripos($url, '?') !== false) {
                $url .= '&parentId=' . $this->_parentId;
            } else {
                $url .= '?parentId=' . $this->_parentId;
            }
        }

        return $url;
    }

    /**
     * 根据会员id获取账号
     * <AUTHOR>
     * @date   2018-03-08
     *
     * @param  int  $mid  会员id
     *
     * @return int
     */
    private function _parseAccountByMid($mid)
    {

        $memModel   = new Member('slave');
        $memberInfo = $memModel->getMemberInfo($mid, 'id', 'id,account,dtype');
        if (!$memberInfo) {
            return false;
        }
        $account = $memberInfo['account'];
        if ($memberInfo['dtype'] == 6) {
            //员工，获取主账号id
            $shipModel = new MemberRelationQuery();
            $parent    = $shipModel->queryRelationshipEmployerMemberInfo($memberInfo['id']);
            if ($parent) {
                $account = $parent['account'];
            }
        }

        return $account;
    }

    /**
     * 根据http_host获取所属二级域名的账号
     * <AUTHOR>
     * @date   2018-01-17
     */
    private function _parseBelongAccount($getId = false)
    {

        $hostInfo = explode('.', $_SERVER['HTTP_HOST']);
        $account  = $hostInfo[0];

        if (!is_numeric($account) && I('account')) {
            $account = I('account');
        }
        if (!is_numeric($account)) {
            if ($account == 'my' && I('session.saccount')) {
                $account = I('session.saccount');
            } else {
                $this->apiReturn('204', [], '非法访问');
            }
        }

        if (!$getId) {
            return ['account' => $account];
        } else {
            //获取用户id
            $memModel   = new Member('slave');
            $memberInfo = $memModel->getMemberInfo($account, 'account', 'id');

            if (!$memberInfo) {
                $this->apiReturn('204', [], '非法访问');
            }

            return ['account' => $account, 'memberId' => $memberInfo['id']];
        }
    }

    private function _parseRequestOptions()
    {
        $this->_requestOptions = $this->_getRequestObject();
    }

    /**
     * 请求参数对象
     * <AUTHOR>
     * @date   2018-03-08
     */
    private function _getRequestObject()
    {
        $params = I('');

        return (object)$params;
    }

    /**
     * 根据用户账号删除海报
     * <AUTHOR>
     * @date   2018-11-08
     */
    public function deletePosterByMemberId()
    {
        if (!$this->isSuper()) {
            $this->apiReturn(103, '', '抱歉您无权限访问！');
        }
        $memberId = I('id', '', 'intval');
        if (empty($memberId)) {
            $this->apiReturn(205, '', '抱歉参数有误！');
        }
        $result = (new \Model\Mall\Poster())->deleteAllPoster($memberId);
        if ($result) {
            pft_log('admin/delposter', $this->loginInfo['memberID'] . '删除了:' . $memberId . '账号的所有海报');
            $this->apiReturn(200, [], '删除成功');
        } else {
            $this->apiReturn(204, [], '删除失败,请重试');
        }
    }

    /**
     * 修改图片大小或创建缩略图
     *
     * @param string $ext 文件后缀
     * @param string $tmpname 文件路径，如上传中的临时目录。
     * @param string $xmax 修改后宽度。
     * @param string $ymax 修改后高度。
     * @param string $newSrc 保存地址。
     *
     * @return array|string
     * <AUTHOR>
     * @date 2021/3/29
     *
     * */
    private function _resizeImage($ext, $tmpname, $xmax, $ymax, $newSrc)
    {
        pft_log('debug', "tmpname：" . $tmpname);
        switch ($ext) {
            case "jpg":
            case "jpeg":
                $im = imagecreatefromjpeg($tmpname);
                break;
            case "png":
                $im = imagecreatefrompng($tmpname);
                break;
            case "gif":
                $im = imagecreatefromgif($tmpname);
                break;
            default:
                break;
        }
        $x = imagesx($im);
        $y = imagesy($im);

        if($x >= $y) {
            $newx = $xmax;
            $newy = $newx * $y / $x;
        } else {
            $newy = $ymax;
            $newx = $x / $y * $newy;
        }

        $im2 = imagecreatetruecolor($newx, $newy);
        imagecopyresized($im2, $im, 0, 0, 0, 0, floor($newx), floor($newy), $x, $y);
        //保存结果到文件
        switch ($ext) {
            case "jpg":
            case "jpeg":
                imagejpeg($im2, $newSrc);

                break;
            case "png":
                pft_log('debug', "im2：" . $im2);
                break;
            case "gif":
                 imagegif($im2, $newSrc);
                break;
            default:
                break;
        }
    }

    /**
     * 微商城海报同步给下级分销商
     */
    public function mallPosterDistributorSync()
    {
        $id = I('id', 0, 'intval');
        $state = I('state', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }
        $posterBiz = Container::pull(PosterBiz::class);
        $result    = $posterBiz->turnOffMallPosterWithSyncDistributor($this->_mid, $id, $state ? 1 : 0);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }
}
