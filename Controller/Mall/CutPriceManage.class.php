<?php

/**
 * 砍价活动后台管理
 *
 * <AUTHOR>
 * @date 2018-03-06
 */

namespace Controller\Mall;

use Business\Order\TimeShareOrder;
use \Library\Controller;
use \Model\Mall\Poster;
use \Model\Product\Ticket;
use \Business\JavaApi\ProductApi;


class CutPriceManage extends Controller {

    public function __construct() {
        //主账号id
        $this->_sid = $this->isLogin('ajax');
    }

    /**
     * 获取可以做砍价的产品列表
     * <AUTHOR>
     * @date   2018-03-06
     */
    public function getProductList() {

        //关键字
        $keyword = I('keyword', '');
        $size    = I('size', 10, 'intval');
        if (!$keyword) {
            $this->apiReturn(204, [], '请输入关键字搜索');
        }
        if ($size > 10) {
            $this->apiReturn(204, [], '超出最大匹配条数');
        }

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->getProductList($this->_sid, $keyword, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }


    /**
     * 展示所选门票的相关属性
     * <AUTHOR>
     * @date   2018-03-06
     */
    public function getTicketAttr() {

        //门票pid
        $tid = I('tid', 0, 'intval');
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        //开始日期
        $date = I('date', date('Y-m-d'));
        //景区id
        $lid = I('lid', 0, 'intval');

        if (!$tid || !$date || !$lid) {
            $this->apiReturn(204, [], '参数缺失');
        }
        //获取分时预约数据
        $timeShareOrderBz = new TimeShareOrder();
        $res              = $timeShareOrderBz->ifOpenShareTime([$lid]);
        if ($res['code'] != 200) {
            $this->apiReturn(400, [], '分时预约数据获取失败');
        }
        if ($res['data'][$lid] == 1) {
            $this->apiReturn(400, [], '该景区开启了分时预约，不能被选为活动产品！');
        }
        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->getTicketAttr($this->_sid, $tid, $aid, $date);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }


    /**
     * 创建砍价活动
     * <AUTHOR>
     * @date   2018-03-08
     */
    public function create() {
        //请求参数对象
        $request = $this->_getRequestObject();

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->create($this->_sid, $request);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }

    /**
     * 获取活动详情
     * <AUTHOR>
     * @date   2018-03-08
     */
    public function getActivity() {
        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '活动id参数缺失');
        }

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->getActivity($this->_sid, $id, true);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }


    /**
     * 编辑砍价活动
     * <AUTHOR>
     * @date   2018-03-08
     */
    public function update() {
        //请求参数对象
        $request = $this->_getRequestObject();
        if (!isset($request->id) || $request->id < 1) {
            $this->apiReturn(204, [], '活动id缺失');
        }

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->update($this->_sid, $request->id, $request);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }


    /**
     * 我的砍价活动列表
     * <AUTHOR>
     * @date   2018-03-09
     */
    public function myActivityList() {
        //当前页数
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');
        if (!$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($size > 20) {
            $this->apiReturn(204, [], '获取条数超出限制');
        }

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->myActivityList($this->_sid, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }


    /**
     * 供应商砍价活动列表
     * <AUTHOR>
     * @date   2018-03-09
     */
    public function supplyActivityList() {
        //当前页数
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');
        if (!$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($size > 20) {
            $this->apiReturn(204, [], '获取条数超出限制');
        }

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->supplyActivityList($this->_sid, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }


    /**
     * 指定票类是否砍价活动
     * <AUTHOR>
     * @date   2018-03-09
     * @return boolean    [description]
     */
    public function hasActivityExists() {
        //门票pid
        $pid = I('pid', 0, 'intval');
        if (!$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->hasActivityExists($this->_sid, $pid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }


    /**
     * 删除一个砍价活动
     * <AUTHOR>
     * @date   2018-03-10
     */
    public function delete() {
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->delete($this->_sid, $id);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }


    /**
     * 获取一个砍价活动的参与情况
     * <AUTHOR>
     * @date   2018-03-14
     */
    public function getJoinList() {
        //砍价活动id
        $id     = I('id', 0, 'intval');
        //当前页数
        $page   = I('page', 1, 'intval');
        //每页条数
        $size   = I('size', 10, 'intval');
        if (!$page || !$size || !$id) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($size > 20) {
            $this->apiReturn(204, [], '获取条数超出限制');
        }

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->getJoinList($this->_sid, $id, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }


    private function _getRequestObject() {
        $params = I('');
        return (object)$params;
    }

}
