<?php

/**
 * 微商城用户相关接口
 * <AUTHOR>
 */

namespace Controller\Mall;

use Business\JavaApi\TicketApi;
use Business\Mall\WechatBindAnnual;
use Business\Member\DynamicCodePass;
use Business\Member\ContactPerson;
use Business\Order\Modify;
use Business\Order\OrderList;
use Business\Order\OrderTourist;
use Business\Order\OrderUnity;
use Business\JavaApi\Product\Ticket as NewTicketApi;
use Business\Order\ReservationOrder;
use Business\Product\ProductStorage;
use Business\Product\Show;
use Library\Business\CaptchaCode;
use Library\Business\WechatSmallApp;
use Library\Cache\Cache;
use Library\Constants\MemberConst;
use Library\Constants\Order\OrderStatus\CommonOrderStatus;
use Library\Constants\OrderConst;
use Library\MessageNotify\OrderNotify;
use Library\Tools\Vcode;
use Business\Wechat\Authorization;
use Business\Product\AnnualCard;
use Business\Member\AccountInfo;
use Business\Member\Session;
use Business\Product\Specialty;
use Business\Mall\AllDis;
use Library\TouTiao\Core;
use Library\wechat\core\OpenWeChatOAuth;
use Library\wechat\core\WeChatOAuth;
use Library\wechat\OpenplatformMiniApp;
use Model\Mall\ActivityOrders;
use Model\Member\Member as MemberModel;
use Model\Order\RefundAuditModel;
use Model\Order\SubOrderQuery;
use Model\Terminal\FaceSubTerminal;
use Model\Wechat\WxMember;
use Model\Product\Ticket;
use Model\Product\Land;
use Model\Order\OrderTools;
use Model\Order\OrderQuery;
use Model\Mall\GroupBooking;
use Model\Subdomain\SubdomainInfo;
use Business\Order\OrderOta;

use Business\Product\Ticket as TicketBiz;
use Business\ElectronicInvoices\InvoiceApi;

class Member extends Mall
{

    const __VCODE_ID__ = 'login_sms';

    const __DEFAULT_AVATAR__ = 'http://images.pft12301.cc/default/avatar.jpg';

    /**
     * 个人中心
     * @return [type] [description]
     */
    public function userCenter()
    {
        $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $memberId  = $loginInfo['memberID'];

        $memModel = new MemberModel('slave');
        $member   = $memModel->getMemberInfo($memberId);

        //账户余额
        $remainMoney = $memModel->getMoneyBatch([$memberId]);
        $remainMoney = $remainMoney[$memberId];

        //用户名
        if (isset($loginInfo['dname'])) {
            $member['dname'] = $loginInfo['dname'];
        }

        $businessCache = (new \Library\Tools\BusinessCache())->getBusinessCache($memberId);
        //头像
        if (isset($businessCache['headImg']) && $businessCache['headImg']) {
            $member['headphoto'] = $businessCache['headImg'];
        }

        $vipBiz  = new \Business\CreditsMall\Points($memberId, $this->_supplyId);
        $signRes = $vipBiz->ifTodaySign();

        if ($signRes['code'] == 200) {
            $hasSign = (int)$signRes['data'];
        } else {
            $hasSign = 0;
        }

        // 菜单项
        $menus = $this->_getMenus();

        // 判断商户是否开启积分商城,若开启，增加菜单项
        $pointManage       = new \Business\CreditsMall\Manage($this->_supplyId);
        $isEnablePointShop = $pointManage->isEnablePointShop($this->_supplyId);

        if ($isEnablePointShop['code'] == 200) {
            $menus['pointShop'] = ['name' => '积分兑换商城', 'data' => []];
        }
        $pointsRes = $vipBiz->getPoints();

        $allDisBiz    = new AllDis();
        $allDisConfig = $allDisBiz->getAllDisConfig($this->_supplyId);
        if ($allDisConfig && $allDisConfig['status'] == 1) {
            $openAllDis = 1;
        } else {
            $openAllDis = 0;
        }

        //获取获取的佣金金额
        $commission = 0;
        if ($this->_allDisMan) {
            $composeRes = $allDisBiz->commissionCompose($memberId, $this->_supplyId);
            if ($composeRes['code'] !== 200) {
                $this->apiReturn(204, [], '佣金组成解析失败');
            }
            $compose    = $composeRes['data'];
            $commission = array_sum($compose['recommend']) + array_sum($compose['develop']);
        }

        //判断用户所在供应商是否有开票权限   1.先通过发票中心的appid查询供应商是否有开票权限
        $options       = [
            'url'     => '/new/userInvoice_orders.html#/',
            'account' => $this->_supplyId,
        ];
        $memberModel   = new \Model\AdminConfig\AdminConfig();
        $invoiceConfig = $memberModel->checkConfig($options);
        if (empty($invoiceConfig['allow'])) {
            unset($menus['invoice']);
        }

        $return = [
            'name'        => $member['dname'],
            'mobile'      => $member['mobile'],
            'headImg'     => $member['headphoto'],
            'remainMoney' => $remainMoney,
            'menus'       => $menus,
            'has_sign'    => $hasSign,//今天是否签到
            'points'      => $pointsRes['data']['points'],
            'allDisMan'   => (int)$this->_allDisMan,
            'commission'  => $commission,
            'openAllDis'  => $openAllDis,
        ];

        if (\ismobile($member['mobile'])) {
            $return['wxAccount'] = 0;
        } else {
            $return['wxAccount'] = 1;
        }

        $this->apiReturn(200, $return);

    }

    /**
     * 获取微商城信息
     */
    public function getMallInfo()
    {
        $mallConfig = $this->getMallConfig();
        if ($mallConfig) {
            $return['tel'] = $mallConfig['telephone']; // 获取微商城客服电话
        } else {
            $return = [];
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 微信小程序登陆接口
     * 非账号密码登陆机制,主要用户获取openid
     */
    public function smallAppLogin()
    {
        $code    = I('code');//jsCode
        $appFlag = I('post.app_token', '', 'strval');// 小程序标识,对应wechat_sm_app.conf.php里面的配置的键名。
        if (!$code) {
            $this->apiReturn(204, [], '参数错误');
        }
        $smallLib = new \Library\Business\WechatSmallApp;
        //如果是独立小程序，则走独立小程序的授权登录接口
        if (isset($_SERVER['HTTP_WX_MINI_APPID']) && $_SERVER['HTTP_WX_MINI_APPID'] != "wx2f45381cd36a6400") {
            $sessionInfo = OpenplatformMiniApp::jsCode2session($_SERVER['HTTP_WX_MINI_APPID'], $code);
        } else {
            $account     = $appFlag ?: $this->_supplyAccount;
            $sessionInfo = $smallLib->getWxSession($code, $account);
        }
        //获取session成功
        if (isset($sessionInfo['openid'])) {
            //自定义的session_key
            $customKey = $smallLib->session_key();
            //保存appid和session_key
            $smallLib->setSession($customKey, $sessionInfo);

            $havaUnionid = 0;
            if (isset($sessionInfo['unionid']) && $sessionInfo['unionid']) {
                $havaUnionid = 1;
            }

            $memberModel = new \Model\Member\Member();

            $authInfo = $memberModel->getAuthByIdentifier($sessionInfo['openid'], MemberConst::INFO_WX_SMALL_APP + 1);
            if ($authInfo) {
                $member = $memberModel->getTheRoleInfo($authInfo['customer_id'], MemberConst::ROLE_TOURIST,
                    'id,dname,headphoto,mobile');
            }

            $return = [
                'member_id'   => $member ? $member['id'] : 0,
                'sessionKey'  => $customKey,
                'session_key' => $customKey,
                'expire'      => 1500,
                'havaUnionid' => $havaUnionid,
                'openid'      => $sessionInfo['openid'],
                'mobile'      => isset($member['mobile']) ? $member['mobile'] : '',
                'dname'       => isset($member['dname']) ? $member['dname'] : '',
                'headphoto'   => isset($member['headphoto']) ? $member['headphoto'] : '',
                'invoice'     => false,
                'account'     => $this->_supplyAccount,
            ];

            //判断用户所在供应商是否有开票权限   1.先通过发票中心的appid查询供应商是否有开票权限
            $options       = [
                'url'     => '/new/userInvoice_orders.html#/',
                'account' => $this->_supplyId,
            ];
            $memberModel   = new \Model\AdminConfig\AdminConfig();
            $invoiceConfig = $memberModel->checkConfig($options);
            if (!empty($invoiceConfig['allow'])) {
                $return['invoice'] = true;
            }
            //将登陆信息保存给Cooperator项目用
            $smallLib->setSession("mall-smallInfo:" . $customKey, $return);

            $this->apiReturn(200, $return);
        } else {
            if (strtolower(ENV) != 'production' && strtolower(ENV) != 'test') {
                $customKey = $smallLib->session_key();
                $openid    = substr(str_shuffle('abcdefghijklmnopqrstuvwxyz'), 0, 28);
                $return    = [
                    'member_id'   => 12,
                    'session_key' => $customKey,
                    'sessionKey'  => $customKey,
                    'expire'      => 1500,
                    'havaUnionid' => "wx5605b231e666f425",
                    'openid'      => $openid,
                    'mobile'      => '***********',
                    'dname'       => '测试账号',
                    'headphoto'   => 'http://wx.qlogo.cn/mmopen/g3MonUZtNHkdmzicIlibx6iaFqAc56vxLSUfpb6n5WKSYVY0ChQKkiaJSgQ1dZuTOgvLLrhJbERQQ4eMsv84eavHiaiceqxibJxCfHe/0',
                    'invoice'     => false,
                    'account'     => $this->_supplyAccount,
                ];
                //保存appid和session_key
                $smallLib->setSession($customKey, $return);
                $smallLib->setSession("mall-smallInfo:" . $customKey, $return);

                return $this->apiReturn(200, $return, "登陆成功");
            }
            $this->apiReturn(204, [], '登陆失败');
        }

    }

    /**
     * 头条登陆接口（复制上面的）
     * 非账号密码登陆机制,主要用户获取openid
     */
    public function touTiaoAppLogin()
    {
        $code = I('post.code');//jsCode
        if (!$code) {
            $this->apiReturn(204, [], '参数错误');
        }
        $smallLib   = new \Library\Business\WechatSmallApp;
        $touTiaoApi = new Core();
        try {
            $sessionInfo = $touTiaoApi->getTouTiaoOpenId($code);
        } catch (\Exception $e) {
            $errorMsg = $e->getMessage();
            $this->apiReturn(204, [], $errorMsg);
        }
        //获取session成功
        if (isset($sessionInfo['openid'])) {
            //自定义的session_key
            $customKey = $smallLib->session_key();
            //保存appid和session_key
            $smallLib->setSession($customKey, $sessionInfo);

            $havaUnionid = 0;
            if (isset($sessionInfo['unionid']) && $sessionInfo['unionid']) {
                $havaUnionid = 1;
            }

            $memberModel = new \Model\Member\Member();

            $authInfo = $memberModel->getAuthByIdentifier($sessionInfo['openid'], MemberConst::TOU_TIAO + 1);
            if ($authInfo) {
                $member = $memberModel->getTheRoleInfo($authInfo['customer_id'], MemberConst::ROLE_TOURIST,
                    'id,dname,headphoto,mobile');
            }

            $return = [
                'member_id'   => isset($member) ? $member['id'] : 0,
                'sessionKey'  => $customKey,
                'expire'      => 1500,
                'havaUnionid' => $havaUnionid,
                'openid'      => $sessionInfo['openid'],
                'mobile'      => isset($member['mobile']) ? $member['mobile'] : '',
                'dname'       => isset($member['dname']) ? $member['dname'] : '',
                'headphoto'   => isset($member['headphoto']) ? $member['headphoto'] : '',
                'invoice'     => false,
                'account'     => $this->_supplyAccount,
            ];
            pft_log('debug/tt_app', json_encode([
                'code'      => $code,
                'session'   => $sessionInfo,
                'cache_key' => $customKey,
                'return'    => $return,
            ]));

            //将登陆信息保存给Cooperator项目用
            $smallLib->setSession("mall-smallInfo:" . $customKey, $return);

            $this->apiReturn(200, $return);
        } else {
            $this->apiReturn(204, [], '登陆失败');
        }

    }

    /**
     * 微票房注册|存在则绑定手机
     * <AUTHOR>
     * @date   2019-05-23
     */
    public function regiterOrBindMobileForWeipiaofang()
    {

        $mobile = I('mobile', '');

        if (empty($mobile) || !ismobile($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '手机号参数错误');
        }

        $memberBiz = new \Business\Member\Member();
        $result    = $memberBiz->regiterOrBindMobileForWeipiaofang($mobile, $this->_smallOpenid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 微信用户授权登录  --  仅供微票房小程序使用
     * Create by zhangyangzhen
     * Date: 2019/1/30
     * Time: 15:01
     */
    public function login()
    {
        $code = I('post.code', '');//jsCode
        if (!$code) {
            $this->apiReturn(204, [], '参数错误');
        }

        $memberModel = new \Model\Member\Member('slave');
        $smallLib    = new WechatSmallApp();
        $sessionInfo = $smallLib->getWxSession($code, $this->_supplyAccount);

        if ($sessionInfo['openid']) {
            $openid    = $sessionInfo['openid'];
            $customKey = $smallLib->session_key();
            $smallLib->setSession($customKey, $sessionInfo);

            $authInfo   = $memberModel->getAuthByIdentifier($openid, MemberConst::INFO_WX_SMALL_APP + 1);
            $customerId = $authInfo ? $authInfo['customer_id'] : 0;

            if ($customerId) {
                $member = $memberModel->getTheRoleInfo($customerId, MemberConst::ROLE_TOURIST, 'id,dname,mobile');
                if ($member) {
                    $this->apiReturn(200, [], '登录成功');
                } else {
                    $this->apiReturn(202, [], '登录失败');
                }
            } else {
                $this->apiReturn(203, [], '未注册');
            }
        } else {
            $this->apiReturn(202, [], '登录失败');
        }
    }

    /**
     * 是否是合法的账号类型(是否是散客)
     *
     * @return boolean
     */
    public function isAvalidAccount()
    {
        $mobile = I('mobile');

        if (!\ismobile($mobile)) {
            $this->apiReturn(204, [], '请输入正确的手机号');
        }

        $memModel = new MemberModel('slave');
        //获取会员信息
        //todo::获取到的不一定是散客的角色，需要修改
        $memberInfo = $memModel->getMemberInfo($mobile, 'mobile');

        $avalid = 1;
        if ($memberInfo) {
            if (!$this->_isSanke($memberInfo)) {
                $avalid = 0;
            }
        }

        $this->apiReturn(200, ['avalid' => $avalid]);
    }

    /**
     * 退出登录
     * @return [type] [description]
     */
    public function logout($return = false)
    {
        //解绑
        if (isset($_SESSION['memberID'])) {
            (new \Model\Wechat\WxMember())->unBindWechat(
                $_SESSION['memberID'],
                self::__DEFAULT_MEMBERID
            );
            session_destroy();
            unset($_SESSION);
        }

        if ($return) {
            return true;
        }

        // 旧登录
        $this->apiReturn(200, [], '退出成功');
    }

    /**
     * 退出登录
     * @return [type] [description]
     */
    public function logoutForAllDis($return = false)
    {
        //解绑
        if (isset($_SESSION['memberID'])) {
            (new \Model\Wechat\WxMember())->unBindWechat(
                $_SESSION['memberID'],
                self::__DEFAULT_MEMBERID
            );
            session_destroy();
            unset($_SESSION);
        }

        if ($return) {
            return true;
        }

        // 旧登录
        $this->apiReturn(200, [], '退出成功');
    }

    /**
     * 发送验证码(取消订单)
     * @return [type] [description]
     */
    public function sendVcodeForCancleOrder()
    {
        $ordernum = I('ordernum', 0);

        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }
        //订单号解码
        $ordernum = $this->_ordernumDecode($ordernum);
        //获取联系人
        $orderModel = new OrderTools('slave');
        $orderInfo  = $orderModel->getOrderInfo($ordernum, 'ordertel');
        if (!$orderInfo) {
            $this->apiReturn(204, [], '订单不存在');
        }

        $data = [
            '{1}'  => '订单取消',
            'code' => '{2}',
        ];
        $res  = Vcode::sendVcode($orderInfo['ordertel'], 'order_cancel', 'order_cancel', $data);

        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '验证码发送成功');
        } else {
            $this->apiReturn(204, [], $res['msg']);
        }
    }

    /**
     * 自动登陆
     * @return [type] [description]
     */
    public function autoLogin()
    {

        $authBiz = new \Business\Wechat\Authorization();

        if (I('code')) {

            $params = json_decode(base64_decode(I('state')), true);
            $appid  = $params['appid'];

            $auth = $authBiz->parseAuthInfo(I('code'));
            if ($auth['code'] != 200) {
                $this->_redirectToIndex($params['supplyAccount']);
            }

            $sessionBiz = new \Business\Member\Session();
            $loginRes   = $sessionBiz->loginWx($appid, $auth['data']['openid'], $params['supplyId'], 5);

            $memberId = 0;
            if ($loginRes['code'] != 200) {
                //微信登陆失败
                $openid = $loginRes['data']['openid'];
            } else {
                $memberId                = $_SESSION['memberID'];
                $openid                  = $loginRes['data']['openid'];
                $_SESSION['from_supply'] = $params['supplyAccount'];
            }

            if ($memberId) {
                $memModel = new MemberModel('slave');
                $join     = $memModel->hasJoinAllDis($memberId, $params['supplyId']);
                if ($join) {
                    $businessData = [
                        'identify' => 'allDis',
                        'headImg'  => $join['head_img'],
                    ];
                    (new \Library\Tools\BusinessCache())->setBusinessCache($businessData, $memberId);
                    $domain   = str_replace('wx', $params['supplyAccount'], MOBILE_DOMAIN);
                    $redirect = $domain . 'r/Mall_Member/getSupplyOpenid?member_id=' . $memberId;
                    $this->_redirectToPage('', '', $redirect);
                    die;
                }
            }

            //存储appid和openid
            $this->_storeOpenInfo($openid, $appid);
            //登陆动作
            $this->_redirectToPage($params['supplyAccount'], 'index');

        } else {

            $params                  = I(null);
            $params['supplyAccount'] = $this->_supplyAccount;
            $params['supplyId']      = $this->_supplyId;

            $callback = MOBILE_DOMAIN . 'api/index.php?' . $_SERVER['QUERY_STRING'];

            $appid           = isset($params['appid']) ? $params['appid'] : self::__DEFAULT_APPID__;
            $params['appid'] = $appid;

            try {
                //发起授权
                $authBiz = new \Business\Wechat\Authorization();
                $authBiz->requestForAuth($callback, $params);
            } catch (\Exception $e) {
                //授权出错直接跳转到首页
                $this->_redirectToIndex($this->_supplyAccount);
            }
        }

    }

    /**
     * 微信开店
     * @return [type] [description]
     */
    public function joinAllDis()
    {

        if (I('code')) {
            try {
                $params = json_decode(base64_decode(I('state')), true);
                if (I('go_url')) {
                    $params['redirect'] = urldecode(I('go_url'));
                }
                $authBiz = new Authorization($params['appid']);
                $auth    = $authBiz->parseAuthInfo(I('code'));

                if ($auth['code'] != 200) {
                    throw new \Exception("授权失败");
                }

                $appid  = $params['appid'];
                $openid = $auth['data']['openid'];

                $Model     = new \Model\Member\Member();
                $extraInfo = [];
                if ($memberInfo = $Model->hasJoinAllDis($openid, $params['supplyId'])) {
                    //已开过店,自动登陆
                    $extraInfo = [
                        'identify' => 'allDis',
                        'openid'   => $memberInfo['openid'],
                        'headImg'  => $memberInfo['head_img'],
                        'dname'    => $memberInfo['nickname'],
                    ];
                    $memberId  = $memberInfo['member_id'];
                } else {
                    //注册全民营销用户
                    $regRes = $this->_registerAllDis(
                        $openid,
                        $params['supplyAccount'],
                        (int)$params['parentId']
                    );

                    if ($regRes === false) {
                        throw new \Exception("开店失败");
                    }

                    $memberId  = $regRes[0];
                    $extraInfo = [
                        'identify' => 'allDis',
                        'openid'   => $regRes[1]['openid'],
                        'headImg'  => $regRes[1]['headimgurl'],
                        'dname'    => $regRes[1]['nickname'],
                    ];
                }

                if ($memberId) {
                    $sessionBiz = new \Business\Member\Session();
                    //登录
                    $loginRes                = $sessionBiz->loginMemberId($memberId, 'wx');
                    $_SESSION                = array_merge($_SESSION, $extraInfo);
                    $_SESSION['from_supply'] = $params['supplyAccount'];

                    //旧版的全名营销没有supply_openid
                    //再次授权获取
                    // if (!isset($memberInfo['supply_openid']) || !$memberInfo['supply_openid']) {
                    $domain   = str_replace('wx', $params['supplyAccount'], MOBILE_DOMAIN);
                    $redirect = $domain . 'r/Mall_Member/getSupplyOpenid?member_id=' . $memberId . '&redirect=' . urlencode($params['redirect']);
                    $this->_redirectToPage('', '', $redirect);
                    die;
                    // }
                }
            } catch (\Exception $e) {

            } finally {
                if ($params['redirect']) {
                    $this->_redirectToPage('', '', str_replace('amp;', '', $params['redirect']));
                } else {
                    $this->_redirectToPage($params['supplyAccount'], 'index');
                }
            }
        } else {
            //回调参数,推广者id
            $params = [
                'parentId' => I('parentId', 0, 'intval'),
                'redirect' => I('redirect'),
            ];
            $this->wechatAuth('Mall_Member', 'joinAllDis', 1, '', 'index', $params, 'user');
        }
    }

    /**
     * 用供应商的公众号授权获取用户openid
     * <AUTHOR>
     * @date   2018-01-31
     * @return [type]     [description]
     */
    public function getSupplyOpenid()
    {
        if (I('code')) {
            try {
                $params = json_decode(base64_decode(I('state')), true);
                if (I('go_url')) {
                    $params['redirect'] = str_replace('amp;', '', I('go_url'));
                }
                $authBiz = new Authorization($params['appid']);
                $auth    = $authBiz->parseAuthInfo(I('code'));

                if ($auth['code'] != 200) {
                    throw new \Exception("授权失败");
                }

                $openid                    = $auth['data']['openid'];
                $_SESSION['supply_openid'] = $openid;

                //扫了营销员的码，但是最后不从营销员的推广链接进行注册，这边做个强制绑定
                $allDisModel   = new \Model\Mall\AllDis();
                $recommendInfo = $allDisModel->getAllDisRecommendInfo($params['member_id'], $params['supplyId']);
                $businessCache = (new \Library\Tools\BusinessCache())->getBusinessCache($params['member_id']);
                //更新openid
                $allDisBiz = new AllDis();
                if ($recommendInfo) {
                    if ((time() - $recommendInfo['update_time']) < 3600) {
                        if ($recommendInfo['fid'] == 0 || $recommendInfo['fid'] == $params['supplyId']) {
                            $redisObj = Cache::getInstance('redis');
                            $redisKey = "allDis:recommend:{$openid}";
                            $parentId = $redisObj->get($redisKey);
                            if ($parentId && $parentId != $params['member_id']) {
                                //父级的链
                                $PRecommendInfo = $allDisModel->getAllDisRecommendInfo($parentId, $params['supplyId']);
                                if ($PRecommendInfo) {
                                    $grandId = $PRecommendInfo['fid'];
                                } else {
                                    $grandId = 0;
                                }

                                $allDisModel->updateRcommonedParentId($recommendInfo['id'], $parentId, $grandId);
                                $redisObj->rm($redisKey);
                                $allDisBiz->newFansNotify($parentId, $businessCache['dname'], $params['supplyId']);
                            }
                        }
                    }
                }

                $res = $allDisBiz->updateSupplyOpenid($params['member_id'], $openid);
            } catch (\Exception $e) {
            } finally {
                if ($params['redirect']) {
                    $this->_redirectToPage('', '', $params['redirect']);
                } else {
                    $this->_redirectToPage($params['supplyAccount'], 'index');
                }
            }
        } else {
            $params['member_id'] = I('member_id');
            $params['redirect']  = I('redirect', '', 'urldecode');
            //回调参数,推广者id
            $this->wechatAuth('Mall_Member', 'getSupplyOpenid', 0, '', 'index', $params);
        }
    }

    /**
     * 微信开店
     * @return [type] [description]
     */
    public function newJoinAllDis()
    {
        if (I('code')) {
            try {
                $params = json_decode(base64_decode(I('state')), true);
                if (I('go_url')) {
                    $params['redirect'] = urldecode(I('go_url'));
                }
                // pft_log("allDis/login", "Code---".print_r($params, true));
                $authBiz = new Authorization($params['appid']);
                $auth    = $authBiz->parseAuthInfo(I('code'));

                if ($auth['code'] != 200) {
                    throw new \Exception("授权失败");
                }

                $appid  = $params['appid'];
                $openid = $auth['data']['openid'];

                $Model     = new \Model\Member\Member();
                $extraInfo = [];
                if ($memberInfo = $Model->hasJoinAllDis($openid, $params['supplyId'])) {
                    //已开过店,自动登陆
                    $extraInfo = [
                        'identify' => 'allDis',
                        'openid'   => $memberInfo['openid'],
                        'headImg'  => $memberInfo['head_img'],
                        'dname'    => $memberInfo['nickname'],
                    ];
                    $memberId  = $memberInfo['member_id'];
                } else {
                    //注册全民营销用户
                    $regRes = $this->_registerAllDis(
                        $openid,
                        $params['supplyAccount'],
                        (int)$params['parentId']
                    );

                    if ($regRes === false) {
                        throw new \Exception("开店失败");
                    }

                    $memberId  = $regRes[0];
                    $extraInfo = [
                        'identify' => 'allDis',
                        'openid'   => $regRes[1]['openid'],
                        'headImg'  => $regRes[1]['headimgurl'],
                        'dname'    => $regRes[1]['nickname'],
                    ];
                }
                $_SESSION['weixinOpen'] = $_SESSION['openid'] = $openid;    //对应票付通的openid
                // pft_log("allDis/login", "extraInfo---".print_r($extraInfo, true));

                if ($memberId) {
                    $sessionBiz = new \Business\Member\Session();
                    //登录
                    $loginRes                = $sessionBiz->loginMemberId($memberId, 'wx');
                    $_SESSION                = array_merge($_SESSION, $extraInfo);
                    $_SESSION['from_supply'] = $params['supplyAccount'];

                    //旧版的全名营销没有supply_openid
                    //再次授权获取
                    // if (!isset($memberInfo['supply_openid']) || !$memberInfo['supply_openid']) {
                    $domain   = str_replace('wx', $params['supplyAccount'], MOBILE_DOMAIN);
                    $redirect = $domain . 'r/Mall_Member/getSupplyOpenidForAllDis?member_id=' . $memberId . '&redirect=' . urlencode($params['from']);
                    // pft_log("allDis/login", "url---".print_r($redirect, true));
                    $this->_redirectToPage('', '', $redirect);
                    die;
                    // }
                }
            } catch (\Exception $e) {

            } finally {
                // pft_log("allDis/login", "params---".print_r($params, true));
                if ($params['from']) {
                    $params['from'] = str_replace('amp;', '', $params['from']);
                    if (!empty($extraInfo)) {
                        $queryPamar         = parse_url($params['from']);
                        $params['redirect'] = $queryPamar['scheme'] . "://" . $queryPamar['host'] . $queryPamar['path'] . "?" . base64_encode($queryPamar['query'] . "&weixinOpen=" . $extraInfo['openid'] . "&memberId=" . $memberId);
                    }
                    // pft_log("allDis/login", "url1-----".print_r($params['redirect']));
                    $this->_redirectToPage('', '', $params['redirect']);
                } else {

                    $this->_redirectToPage($params['supplyAccount'], 'index');
                }
            }
        } else {
            //回调参数,推广者id
            $params = [
                'parentId' => I('parentId', 0, 'intval'),
                'from'     => I('from'),
            ];
            // pft_log("allDis/login", "参数---".print_r($params, true));
            $this->wechatAuth('Mall_Member', 'newJoinAllDis', 1, '', 'index', $params, 'user');
        }
    }

    /**
     * 获取票付通的openid，
     *
     * 用于新版的微商城微信支付
     *
     * <AUTHOR>
     * @date    2019/06/03
     */
    public function getWechatFptOpenId()
    {
        $code = I('get.code', '');
        if ($code) {
            //解析参数
            $params = json_decode(base64_decode(I('state')), true);

            try {
                if (ENV == 'TEST') {
                    //获取票付通的openid
                    $auth = WeChatOAuth::getAccessTokenAndOpenId($code);
                } else {
                    //获取票付通的openid
                    $auth = OpenWeChatOAuth::getAccessTokenAndOpenId($params['appid'], $_REQUEST['code']);
                }

                $openid = $auth['openid'];
                //获取票付通公众号openid或者独立收款的公众号openid
                $_SESSION['pft_openid'] = $openid;

            } catch (\Exception $e) {
            }

            //跳转置首页
            $redirect = str_replace('amp;', '', I('go_url'));

            header("Location:{$redirect}");
            exit();

        } else {
            //最终要跳转的url
            $goUrl = I('from', '');

            if (ENV == "TEST") {
                $params['appid'] = 'wx042bc4a434a8d3c9';
            } else {
                $params['appid'] = PFT_WECHAT_APPID;
            }

            // 判断这个微商城的客户是否配置了独立收款,如果配置了那么获取openid使用用户自己的公众号来获取。
            $biz             = new \Business\Finance\PayMerchant();
            $merchantPayConf = $biz->getWepayInfoByAccount($this->_supplyAccount);
            if (isset($merchantPayConf['sub_appid']) && $merchantPayConf['sub_appid'] != '' && $merchantPayConf['use_env'] >= 1) {
                $params['appid'] = $merchantPayConf['sub_appid'];
            }
            pft_log("allDis/login", "getWechatFptOpenId-----" . json_encode($params, JSON_UNESCAPED_UNICODE));

            //调回的url
            $callback = MOBILE_DOMAIN . 'api/index.php?c=Mall_Member&a=getWechatFptOpenId';

            if ($goUrl) {
                $callback .= '&go_url=' . urlencode(html_entity_decode(I('from')));
            }

            try {
                if (!in_array(ENV, ['TEST', 'PRODUCTION'])) {
                    //本地跟内网测试直接跳转到给的url
                    $goUrl = html_entity_decode($goUrl);
                    header("Location: {$goUrl}");
                    exit();
                }

                //是否在微信app中
                if ($this->inWechatApp()) {
                    $state = base64_encode(json_encode($params));

                    if (ENV == 'TEST') {
                        $callback = urlencode($callback);
                        WeChatOAuth::getCode($callback, $state, "snsapi_base");
                    } else {
                        //发起授权
                        OpenWeChatOAuth::getCode($callback, $state, 'snsapi_base', $params['appid']);
                    }
                } else {
                    //授权出错直接跳转到给的url
                    $goUrl = html_entity_decode($goUrl);
                    header("Location: {$goUrl}");
                    exit();
                }
            } catch (\Exception $e) {
                $goUrl = html_entity_decode($goUrl);
                //授权出错直接跳转到给的url
                header("Location: {$goUrl}");
                exit();
            }
        }
    }

    /**
     * 用供应商的公众号授权获取用户openid
     * <AUTHOR>
     * @date   2018-01-31
     * @return [type]     [description]
     */
    public function getSupplyOpenidForAllDis()
    {
        if (I('code')) {
            try {
                $params = json_decode(base64_decode(I('state')), true);
                if (I('go_url')) {
                    $params['redirect'] = str_replace('amp;', '', I('go_url'));
                }
                $authBiz = new Authorization($params['appid']);
                $auth    = $authBiz->parseAuthInfo(I('code'));

                if ($auth['code'] != 200) {
                    throw new \Exception("授权失败");
                }

                $openid                    = $auth['data']['openid'];
                $_SESSION['supply_openid'] = $openid;       //针对供应商的openid
                //更新openid
                $allDisBiz = new AllDis();
                $res       = $allDisBiz->updateSupplyOpenid($params['member_id'], $openid);
                // pft_log("allDis/login", "授权---".print_r($res, true)."---参数----".print_r($params, true));
                //判断用户是否有绑定手机号
                $memberModel = new \Model\Member\Member();
                $memberInfo  = $memberModel->getMemberInfo($params['member_id'], 'id');
                $isNeedBind  = 0;
                //产品要求先不绑定
                // if(isset($memberInfo['mobile']) && !empty($memberInfo['mobile'])){
                //     $isNeedBind = 1;
                // }
            } catch (\Exception $e) {
            } finally {
                if ($params['redirect']) {
                    $queryPamar = parse_url($params['redirect']);
                    if (empty($queryPamar)) {
                        $params['redirect'] = $queryPamar['scheme'] . "://" . $queryPamar['host'] . $queryPamar['path'] . "?" . $queryPamar['query'] . "isNeedBind={$isNeedBind}&weixinOpen=" . $openid;
                    } else {
                        $params['redirect'] = $queryPamar['scheme'] . "://" . $queryPamar['host'] . $queryPamar['path'] . "?" . $queryPamar['query'] . "&isNeedBind={$isNeedBind}&weixinOpen=" . $openid;
                    }
                    // pft_log("allDis/login", "结束连接--".$params['redirect']);
                    $this->_redirectToPage('', 'h5', $params['redirect']);
                } else {
                    $this->_redirectToPage($params['supplyAccount'], 'index');
                }
            }
        } else {
            $params['member_id'] = I('member_id');
            $params['redirect']  = I('redirect', '', 'urldecode');
            //回调参数,推广者id
            $this->wechatAuth('Mall_Member', 'getSupplyOpenidForAllDis', 0, '', 'index', $params);
        }
    }

    /**
     * 获取未使用|历史订单信息
     * @return [type] [description]
     */
    public function getOrderList()
    {
        $memberId = $this->isLogin('ajax');

        $pageSize  = I('pageSize', 10, 'intval');
        $page      = I('page', 1, 'intval');
        $type      = I('type') ?: 'unuse';
        $beginDate = I('beginDate', '');
        $endDate   = I('endDate', '');
        $dateType  = I('dateType', 'play');

        if (!in_array($type, ['unuse', 'history', 'unpay', 'all'])) {
            $this->apiReturn(204, [], '查询类型错误');
        }

        $Order  = new OrderTools('slave');
        $Land   = new Land('slave');
        $Ticket = new Ticket('slave');
        $Group  = new GroupBooking();
        $tBiz   = new TicketBiz();

        //获取订单号的开票记录
        $invoiceApi = new InvoiceApi();

        $midArr = $this->_getALlMemberId();
        if (count($midArr) > 10) {
            $midArr = [$memberId];
        }
        //获取订单列表
        $orderRes = $this->_getOrderList($midArr, $page, $pageSize, $type, $beginDate, $endDate, $dateType);
        [$total, $orders] = array_values($orderRes);

        $list = [];
        if ($orders) {
            $lidArr       = array_column($orders, 'lid');
            $tidArr       = array_column($orders, 'tid');
            $orderArr     = array_column($orders, 'ordernum');
            $aidArr       = array_column($orders, 'aid');
            $faceModel    = new \Model\Terminal\FaceCompare();
            $platformData = $faceModel->getFacePlatforms(0, $lidArr);
            $platform     = [];
            foreach ($platformData as $val) {
                $platform[$val['lid']] = $val['face_platform'];
            }
            //景区信息
            $javaAPi      = new \Business\CommodityCenter\Land();
            $landRes      = $javaAPi->queryLandMultiQueryById($lidArr);
            $landsMapping = array_column($landRes, null, 'id');

            //门票信息
            $ticketsMapping = $Ticket->getMuchTicketInfo($tidArr, 'id,title');
            //拼团订单做特殊处理
            $groupOrder = $Group->parseTBCOrders($orderArr);

            //获取联票信息
            //$links = $Order->getOrderConcatId($orderArr);

            $queryParams = [$orderArr, 'orderid', 'concat_id'];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderDetail', 'queryOrderDetailsByOrdernums',
                $queryParams);
            $links       = [];
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $links = $queryRes['data'];
            }

            foreach ($orders as $k => $order) {

                //订单号对应的景区信息
                $landInfo = $landsMapping[$order['lid']];

                $link = [];
                if ($links[$order['ordernum']]) {
                    if ($links[$order['ordernum']] != $order['ordernum']) {
                        continue;
                    } else {
                        $field      = 'ss.tnum,ss.tid,ss.code';
                        $linkOrders = $Order->getLinkOrdersInfo($order['ordernum'], $field);
                        //显示联票的子票
                        foreach ($linkOrders as &$val) {
                            $val['title'] = $ticketsMapping[$val['tid']];
                        }

                        $link = $linkOrders;
                    }
                } else {
                    $link[] = [
                        'tnum'  => $order['tnum'],
                        'tid'   => $order['tid'],
                        'title' => $ticketsMapping[$order['tid']],
                    ];
                }

                $orderModeArr = load_config('source', 'orderSearch');

                $tmp = [
                    'ltitle'   => $landInfo['title'],
                    'imgpath'  => $landInfo['imgpath'],
                    'ttitle'   => $ticketsMapping[$order['tid']],
                    'ordernum' => $order['ordernum'],
                    'tickets'  => $link,
                    'ptype'    => $landInfo['p_type'],
                ];

                $orderQueryModel = new OrderQuery();
                $orderPrice      = $orderQueryModel->get_order_total_fee($order['ordernum']);

                $tmp['ordertime'] = $order['ordertime'];
//                if ($type == 'unuse') {
                $tmp['tid']        = $order['tid'];
                $tmp['lid']        = $order['lid'];
                $tmp['pid']        = $order['pid'];
                $tmp['status']     = $order['status'];
                $tmp['pid']        = $order['pid'];
                $tmp['begintime']  = $order['begintime'];
                $tmp['endtime']    = $order['endtime'];
                $tmp['ordername']  = $order['ordername'];
                $tmp['ordertel']   = $order['ordertel'];
                $tmp['code']       = $order['code'];
                $tmp['tnum']       = $order['tnum'];
                $tmp['paystatus']  = $order['pay_status'];
                $tmp['cancel']     = OrderList::isAllowCancel($order['ordernum'], $order['member']);
                $tmp['pay']        = intval($order['status'] == 0 && $order['pay_status'] == 2);
                $tmp['verifytime'] = $order['dtime'];
                $tmp['ordermode']  = $orderModeArr[$order['ordermode']];
                $tmp['orderprice'] = $orderPrice;
                if ($this->inUnionApp()) {
                    $tmp['aid'] = $order['aid'];
                }
                //已支付的获取订单状态(退票中，部分验证)
                if ($order['pay_status'] == 1) {
                    $tmp['status'] = $this->_parseOrderStatus($order['ordernum'], $tmp['status']);
                }
                if (in_array($order['ordernum'], $groupOrder)) {
                    $tmp['status'] = 102;//待确认状态
                }
                $tmp['is_face'] = isset($platform[$tmp['lid']]) ? 1 : 0;
//                } else {
//                    $tmp['verifytime'] = $order['dtime'];
//                    $tmp['status']     = $order['status'];
//                }

                if ($landInfo['p_type'] == 'J') {
                    //特产
                    $tmp = $this->_fillOrderInfoForSpecial($tmp);
                }

                $list[] = $tmp;
            }
        }
        if ($this->inUnionApp()) {
            $productBiz = new \Business\Mall\Product();
            $list       = $productBiz->getOrderAccount($list);
        }
        $return = [
            'total'     => $total,
            'list'      => $list,
            'page'      => $page,
            'totalPage' => ceil($total / $pageSize),
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 订单详情
     * @return [type] [description]
     */
    public function orderDetail()
    {
        $memberId = $this->isLogin('ajax');
        $ordernum = I('ordernum');

        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }

        $orderModel = new OrderTools('slave');
        //基本信息
        $mainInfo = $orderModel->getOrderInfo($ordernum);

        if (!$mainInfo) {
            $this->apiReturn(204, [], '订单不存在');
        }

        $midArr = $this->_getALlMemberId();

        if (!in_array($mainInfo['member'], $midArr)) {
            $this->apiReturn(204, [], '您无权查看订单');
        }

        $return = $this->_getOrderDetail($ordernum, $mainInfo, false, $this->_supplyId, $this->saleChannel);

        $this->apiReturn(200, $return);
    }

    private function _getALlMemberId()
    {

        $memberId = $this->isLogin('ajax');

        $orderBiz     = new \Business\Mall\Order();
        $cookieMidArr = $orderBiz->getCookieForMid();
        if ($cookieMidArr) {
            return array_merge([$memberId], $cookieMidArr);
        } else {
            return [$memberId];
        }
    }

    /**
     * @deprecated 该方法已不再维护
     * 订单详情展示(短信二维码链接)
     * @return [type] [description]
     */
    public function detailShow()
    {
        $ordernum = I('ordernum', '', 'strval');
        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }
        $hideQrcode = 0;
        //判断是否为分销海报通知,如果是分销海报通知,设置标识后面用
        if (strpos($ordernum, '-dis_poster')) {
            $hideQrcode = 1;
            $ordernum   = substr($ordernum, 0, strpos($ordernum, '-'));
        }
        $encryptionOrderNum = $ordernum; //加密的订单号
        //订单号解码
        $ordernum = $this->_ordernumDecode($ordernum);

        $ordernum   = strval($ordernum);
        $orderModel = new OrderTools('slave');
        //基本信息
        $mainInfo = $orderModel->getOrderInfo($ordernum);

        if (!$mainInfo) {
            $this->apiReturn(204, [], '订单不存在');
        }
        $return = $this->_getOrderDetail($ordernum, $mainInfo, true, $this->_supplyId, $this->saleChannel);

        //检测订单是否需要开票
        $invoiceApi           = new \Business\ElectronicInvoices\InvoiceApi();
        $orderInvoiceCheckRes = $invoiceApi->checkInvoiceAuth([$ordernum], $this->_supplyId, false, 'wx');
        if ($orderInvoiceCheckRes['code'] == 200 && isset($orderInvoiceCheckRes['data'][$ordernum])) {
            $orderInvoiceConf  = $orderInvoiceCheckRes['data'];
            $return['invoice'] = $orderInvoiceConf[$ordernum]['print_invoice'];
        }

        // 检测是否一票一码订单
        $ticketBiz      = new TicketApi();
        $ticketApi      = new \Business\JavaApi\TicketApi();
        $memberIdAndAid = $ticketApi->getOrderAidByMemberIdAndAid($ordernum, $mainInfo['member'],
            $mainInfo['aid'], $mainInfo['visitors']);
        $ticketBiz      = new \Business\CommodityCenter\Ticket();
        $ticketInfo     = $ticketBiz->fieldConversion($ticketBiz->queryTicketInfoById($mainInfo['tid'], '', '', '', '',
            false, $memberIdAndAid['aid'], $memberIdAndAid['memberId'], $this->saleChannel));
        //$ticketInfo            = $ticketBiz->getTickets($mainInfo['tid'], $memberIdAndAid['memberId'],
        //    $this->saleChannel, $memberIdAndAid['aid']);
        $return['hide_qrcode'] = $hideQrcode;
        $return['multi_code']  = [];

        $return['multi_code_status'] = [];
        // 配置了一人一票  +  短信显示门票码
        if ($ticketInfo['ext']['print_mode'] == 4 && (!isset($ticketInfo['ext']['code_show_type']) || $ticketInfo['ext']['code_show_type'] == '' || $ticketInfo['ext']['code_show_type'] == 1)) {
            $idx       = I('post.idx', 0, 'intval');
            $temidxArr = $orderModel->getTouristOrederIdxs($ordernum, 'idx, check_state, orderid, chk_code');
            $idxArr    = array_column($temidxArr, 'idx');
            if ($idxArr) {
                // if(count($idxArr) != $mainInfo['tnum'] ){
                //     $this->apiReturn(204, [], '订单数量不一致:'.$ordernum);
                // }
                // 获取一票一码的码
                //$return['multi_code'] = OrderUnity::getCodeByqOrdernumTnum($mainInfo['code'], $mainInfo['tnum']);
                //$return['multi_code'] = OrderUnity::getCodeByOrdernumTnum($ordernum,$mainInfo['tnum'],$idxArr,$idx);
                foreach ($temidxArr as $temVal) {
                    if (!empty($idx) && $temVal['idx'] != $idx) {
                        continue;
                    }
                    //$temMultiCode = OrderUnity::getCodeByOrdernumTnum($ordernum,$mainInfo['tnum'],$idxArr,$temVal['idx'])[0];
                    if (empty($temVal['chk_code'])) {
                        // 没有门票码用OD#
                        $temMultiCode                               = OrderUnity::getCodeByOrdernumTnum($ordernum,
                            $mainInfo['tnum'], $idxArr, $temVal['idx'])[0];
                        $return['multi_code'][]                     = $temMultiCode;
                        $return['multi_code_status'][$temMultiCode] = $temVal['check_state'];
                    } else {
                        $return['multi_code'][]                           = $temVal['chk_code'];
                        $return['multi_code_status'][$temVal['chk_code']] = $temVal['check_state'];
                    }
                }
            }
        }
        //取票信息
        $return['getaddr'] = isset($ticketInfo['get_ticket_info']) ? $ticketInfo['get_ticket_info'] : '';
        //门票属性 入园方式 0或者空 不限 1 取票入园
        $return['entry_method'] = isset($ticketInfo['ext']['entry_method']) ? $ticketInfo['ext']['entry_method'] : 0;
        //小票凭证信息展示方式 0=凭证码 1=凭证码+订单号 2=订单号
        $return['is_show_ordernum'] = isset($ticketInfo['ext']['is_show_ordernum']) ? $ticketInfo['ext']['is_show_ordernum'] : 0;

        //如果设置了外部码
        if (!empty($return['externalCode']['code'])) {
            //如果使用了外部码不考虑这2个参数，直接默认0
            $return['is_show_ordernum'] = 0;
            $return['entry_method']     = 0;
        }

        //飞猪（17）、ota（20）订单不提供退票功能,泰山门票禁止在短信链接中取消
        if ($return['apply_did'] == 286556 || (in_array($mainInfo['ordermode'], [17, 20]) && $mainInfo['remotenum'])) {
            $return['cancel'] = 0;
        }
        //小程序下单需要多获取些数据，提供给短信链接跳转小程序
        $return['jump_small_app'] = 0; //是否要跳转小程序 0：否 1：是
        if ($mainInfo['ordermode'] == 43) {
            $miniAppBz  = new \Business\Cooperator\WeChat\OpenPlatformMiniApp();
            $expireTime = strtotime('+ 30 days', time()); //scheme码的失效时间，默认30天，失效后需要重新生成
            $schemeInfo = $miniAppBz->generateUrlScheme($encryptionOrderNum, $mainInfo['aid'],
                1, true, $expireTime);
            //生成成功赋值
            if (!empty($schemeInfo['data'])) {
                $return['openlink']       = $schemeInfo['data']['openlink']; //小程序跳转链接
                $return['jump_small_app'] = 1;
            }
        }

        //如果是分销海报通知，不返回凭证码
        if ($return['hide_qrcode'] != 1) {
            $return['code'] = self::order_code_format($mainInfo['lid'], strval($return['code']));
        }

        //判断是否显示人脸录入按钮（只有一票一码并且配置了沃土人脸的景区才能显示人脸按钮）
        $return['is_face'] = 0;
        $landModel         = new Land('slave');
        if ($return['ptype'] == 'F') {
            $orderModel = new OrderTools('slave');
            $childOrder = $orderModel->getPackChildOrders($ordernum, 'orderid');
            if ($childOrder) {
                $orderInfo = $orderModel->getOrderInfo($childOrder[0]['orderid'], 'lid');
                $terminal  = $landModel->getTerminalByLid($orderInfo['lid']);
            }
        } else {
            $terminal = $return['terminal'];
        }

        $nowTerminal = $landModel->getNowTerminal($terminal);
        if ($nowTerminal) {
            $terminal = $nowTerminal;
        }
        $faceModel                      = new \Model\Terminal\FaceCompare();
        $platformData                   = $faceModel->getFacePlatform(false, $terminal);
        $return['face_sms_notice']      = 0;
        $return['face_sms_notice_info'] = '';
        if ($platformData['ext_info']) {
            //平台短信页面绑定人脸提示
            //{"sms_notice":"1","notice_info":"9639888866666"}
            $extInfo = json_decode($platformData['ext_info'], true);
            if (!empty($extInfo['sms_notice'])) {
                $return['face_sms_notice']      = 1;
                $return['face_sms_notice_info'] = $extInfo['notice_info'];
            }
        }

        $landExtDraft  = [];
        $ticketBiz     = new \Business\JavaApi\CommodityCenter\Ticket();
        $getTicketInfo = $ticketBiz->queryTicketAttrsById($ticketInfo['id']);
        if ($getTicketInfo['code'] == 200) {
            foreach ($getTicketInfo['data'] as $attr) {
                $landExtDraft[$attr['key']] = $attr['val'];
            }
        }

        //判断下 是否有开启人脸  或者录脸渠道配置了不可录脸
        if (!isset($landExtDraft['face_open'])) {
            $return['is_face'] = 0;
        } else {
            $return['is_face'] = (int)$landExtDraft['face_open'];
            if ($return['is_face'] == 1 && isset($landExtDraft['no_record_face_channel']) && in_array(1,
                    explode(',', $landExtDraft['no_record_face_channel']))) {
                $return['is_face'] = 0;
            }
        }

        //$ticketModel   = new \Model\Product\Ticket();
        //$ticketExtInfo = $ticketModel->getFaceConfig([$ticketInfo['id']], 'face_open');
        //if ($ticketExtInfo === false) {
        //    $return['is_face'] = 0;
        //} else {
        //    $return['is_face'] = (int)$ticketExtInfo[0];
        //}

        //如果是第三方订单且存在第三方订单凭证码， 则获取第三方订单凭证码
        $orderOtaBus                 = new OrderOta();
        $handleCodeRes               = $orderOtaBus->handleCode($ordernum, $return['code']);
        $noticeType                  = $orderOtaBus->getNoticeCode($mainInfo['lid']);
        $return['notice_type']       = $noticeType;
        $return['third_code']        = $handleCodeRes['multi_code'];
        $return['third_api_qr_code'] = $handleCodeRes['api_qr_code'];
        if (!empty($handleCodeRes['api_qr_code']) && is_array($handleCodeRes['api_qr_code'])) {
            $return['third_api_qr_code'] = array_filter($handleCodeRes['api_qr_code']);
        }

        //第三方订单情况处理
        if ($noticeType != -1) {
            $return['code'] = $handleCodeRes['code'];//赋值为第三方的凭证码
        }
        //第三方订单凭证码生成类型为默认类型0时候的处理
        if ($noticeType == 0) {
            //在类型为默认类型0的情况下判断第三方凭证码是否生成，生成则为同步发码，没生成则进一步判断是不发码还是异步发码
            if (!empty($handleCodeRes['code']) || !empty($handleCodeRes['multi_code'])) {
                $return['notice_type'] = 1;//类型为同步发码
            } else {
                $return['notice_type'] = 3;//类型为不发码
                if ($handleCodeRes['handle_status'] == 2) {
                    //超时情况下
                    $return['notice_type'] = 2;//类型为异步发码
                }
            }
            //all_api_order表没有记录情况为平台订单
            if ($handleCodeRes['handle_status'] == -1) {
                $return['notice_type'] = 0;//类型为非第三方订单
            }
        }
        //同步发码超时情况处理
        if (
            $noticeType == 1 &&
            empty($handleCodeRes['code']) &&
            empty($handleCodeRes['multi_code']) &&
            $handleCodeRes['handle_status'] == 2
        ) {
            $return['notice_type'] = 2;//类型为异步发码
        }
        //非第三方订单情况处理
        if ($noticeType == -1) {
            $return['notice_type'] = 0;//类型为非第三方订单
        }

        //文旅特殊处理
        $productExt = is_array($return['product_ext']) ? $return['product_ext'] : [];
        //带上取票人
        $productExt['orderName'] = isset($return['ordername']) ? $return['ordername'] : '';
        $orderBiz                = new \Business\Order\OrderList();
        $return                  = $orderBiz->_handleSpecialOrderExport($return['apply_did'], $return, $productExt,
            [$ordernum], false, false, true);

        //echo $return['ordername'];
        //$return['ordername'] = substr_replace_cn($return['ordername'],'*', 1, 1);
        //$return['ordertel']  = substr_replace($return['ordertel'],'*****', 3, 5);
        $this->apiReturn(200, $return);
    }

    /**
     * 根据订单号获取人脸图
     * <AUTHOR>
     * @date   2018-10-23
     */
    public function getOrderFace()
    {
        $ordernum = I('ordernum');
        $sign     = I('sign', '');
        if (!$ordernum) {
            $this->apiReturn(203, [], '订单号错误');
        }
        if (md5('pft12301' . $ordernum) != $sign) {
            //订单号解码
            $ordernum = $this->_ordernumDecode($ordernum);
            $ordernum = strval($ordernum);
        }

        $orderModel = new OrderTools('slave');
        $orderInfo  = $orderModel->getOrderInfo($ordernum, 'tnum,ifpack,ordertel,aid', false, true);
        if (empty($orderInfo)) {
            $this->apiReturn(203, [], '订单不存在');
        }
        $arrChildOrder = [];
        if ($orderInfo['ifpack'] == 1) {
            $childOrder = $orderModel->getPackChildOrders($ordernum, 'orderid');
            if ($childOrder) {
                $arrChildOrder = array_column($childOrder,'orderid');
                $ordernum  = $childOrder[0]['orderid'];
                $orderInfo = $orderModel->getOrderInfo($ordernum, 'tnum');
            }
        }
        $faceModel            = new \Model\Terminal\FaceCompare();
        $faceInfo             = $faceModel->getFaceByOrder([$ordernum]);
        $background           = $faceModel->getPlatformByAid($orderInfo['aid'], 'background');
        $result['background'] = isset($background[0]) ? $background[0] : '';
        $result['tnum']       = $orderInfo['tnum'];
        $result['img_list']   = [];//已上传的人脸
        $result['save_face']  = [];//保存的常用人脸

        //$saveFace = $faceModel->getFaceByMobile($orderInfo['ordertel'], false, 'id,face_url,created_at');
        $saveFace = $faceModel->getFaceByMobile($orderInfo['ordertel'], false, 'a.id,a.face_url,a.created_at,b.idx',
            true);
        $faceBiz  = new \Business\Face\FaceBase();

        if ($saveFace) {
            foreach ($saveFace as &$v) {
                $v['face_url']   = $faceBiz->faceUrlConversion($v['face_url']);
                $v['created_at'] = date('Y-m-d H:i', $v['created_at']);
            }
            $result['save_face'] = $saveFace;
        }

        if ($faceInfo) {
            $faceOrder = [];
            foreach ($faceInfo as $key => $value) {
                $faceOrder[$value['face_info_id']]['idx']       = $value['idx'];
                $faceOrder[$value['face_info_id']]['is_delete'] = $value['is_delete'];
            }
            $searchBranchFaceOrder = $arrChildOrder ? : [$ordernum];
            $arrIdx                = array_column($faceOrder,'idx');
            //通过pft_faceorder_bind和pft_face_branch_terminal_log关联确认下是否存在分终端使用人脸情况
            $faceSubTerminalMdl = new FaceSubTerminal();
            $branchFaceList = $faceSubTerminalMdl->getBranchTerminalRelationListGroupByIdx($searchBranchFaceOrder,$arrIdx);
            $branchArrIdx   = array_column($branchFaceList,'idx');
            $ids      = array_column($faceInfo, 'face_info_id');
            $faceData = $faceModel->getFaceInfoByFaceIds($ids, 'id,face_url,nickname,mobile,idcard');
            $data     = [];
            foreach ($faceData as $key => $value) {
                $data[$value['id']]              = $value;
                $data[$value['id']]['idx']       = $faceOrder[$value['id']]['idx'];
                $data[$value['id']]['is_delete'] = $faceOrder[$value['id']]['is_delete'];
                $data[$value['id']]['face_url']  = $faceBiz->faceUrlConversion($value['face_url']);
                if (in_array($faceOrder[$value['id']]['idx'],$branchArrIdx) && !$faceOrder[$value['id']]['is_delete']){
                    $data[$value['id']]['is_delete'] = 1;//改成已使用状态
                }
            }
            $data               = array_values($data);
            $result['img_list'] = $data;
            $result['tnum']     = $orderInfo['tnum'];
            $this->apiReturn(200, $result, 'success');
        } else {
            $this->apiReturn(200, $result, '还没上传人脸数据');
        }
    }

    /**
     * 凭证号格式化
     * <AUTHOR> Chen
     * @date   2018-01-30
     *
     * @param  int  $lid  景区ID
     * @param  int  $code  订单凭证号
     *
     * @return string
     */
    private function order_code_format($lid, $code)
    {
        if (isset($_COOKIE['debug'])) {

        }
        // 使用第三方系统的闸机验证的，需要在凭证号前面加前缀，这里先从配置读取，如果后期此类用户很多，考虑做一个配置的表。
        $codePrefixConf = load_config('lid_code_prefix', 'product');
        if (isset($codePrefixConf[$lid])) {

            $code = $codePrefixConf[$lid] . $code;
        }

        return $code;
    }

    /**
     * 取消订单(需要在登陆状态下)
     * @return [type] [description]
     */
    public function cancelOrder()
    {
        $memberId = $this->isLogin('ajax');

        $ordernum = I('ordernum');

        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }

        //基本信息
        $orderModel = new OrderTools();
        $mainInfo   = $orderModel->getOrderInfo($ordernum, 'member,status');

        if (!in_array($mainInfo['status'], [0, 7])) {
            $this->apiReturn(204, [], '订单状态错误');
        }

        $midArr = $this->_getALlMemberId();

        if (!in_array($mainInfo['member'], $midArr)) {
            $this->apiReturn(204, [], '您无权取消订单');
        }

        @pft_log('order/wechat/cancel', $memberId . ':' . $ordernum);

        $result = $this->_cancelOrder($ordernum, $memberId);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 取消订单(无需登陆，但需要短信验证)
     * @return [type] [description]
     */
    public function cancelOrderBySms()
    {
        $ordernum = I('ordernum');
        $vcode    = I('vcode', 0);

        if (!$ordernum || !$vcode) {
            $this->apiReturn(204, [], '参数错误');
        }
        //订单号解码
        $ordernum = $this->_ordernumDecode($ordernum);

        $result = $this->_cancelOrder($ordernum, 0, true, $vcode);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 订单号解码
     *
     * @param  [type] $ordernum 订单号
     *
     * @return [type]           [description]
     */
    private function _ordernumDecode($ordernum)
    {
        $ordernum = \Library\MessageNotify\OrderNotify::url_sms_decode($ordernum);

        return $ordernum[0];
    }

    /**
     * 调用订单取消接口
     *
     * @param  [type] $ordernum 订单号
     *
     * @return [type]           [description]
     */
    private function _cancelOrder($ordernum, $oper = 0, $isSms = false, $vcode = 0)
    {
        @pft_log('order/wechat/cancel', json_encode($_REQUEST, JSON_UNESCAPED_UNICODE), $pathMode = 'day');
        //获取联系人
        $orderInfo = (new \Model\Order\OrderTools())->getOrderInfo($ordernum,
            'tnum,tid,aid,lid,status,ordertel,member,pay_status,ordername,ordermode,remotenum');

        if (empty($orderInfo)) {
            $this->apiReturn(204, [], '订单信息有误');
        } elseif (!in_array($orderInfo['status'], [0, 2, 4, 7, CommonOrderStatus::WAIT_APPOINTMENT_CODE, 80, 81])) {
            $this->apiReturn(204, [], '订单状态错误');
        }

        $memberId = $orderInfo['member'];

        if ($isSms) {
            if ($orderInfo['ordermode'] == 20 && $orderInfo['remotenum']) {
                $this->apiReturn(204, [], 'ota订单无法通过短信取消');
            }
            $mobile    = $orderInfo['ordertel'];
            $verifyRes = Vcode::verifyVcode($mobile, $vcode, 'order_cancel');
            if ($verifyRes['code'] != 200) {
                $this->apiReturn(204, [], $verifyRes['msg']);
            }
        } else {
            $midArr = $this->_getALlMemberId();
            if (!in_array($orderInfo['member'], $midArr)) {
                $this->apiReturn(204, [], '您无权取消订单');
            }
        }
        $cancelChannel = OrderConst::WECHAT_SHOP_CANCEL;
        //添加取消备注
        $trackMsg = '';
        if (!$oper) {
            $oper          = $orderInfo['member'];
            $cancelChannel = OrderConst::MOBILE_CANCEL;
            $trackMsg      = '通过短信取消订单';
        }
        //这边微商城获取联票和套票的审核状态就根据主票去获取了
        $reqSerialNumber = '';
        if (!$reqSerialNumber) {
            $auditMdl  = new RefundAuditModel();
            $auditInfo = $auditMdl->getUnderAuditInfo($ordernum, 0, [2, 3]);
            if ($auditInfo) {
                $reqSerialNumber = $auditInfo['remote_sn'];
            }
        }

        pft_log('order/wechat/cancel', "操作人{$oper},订单号:{$ordernum}");

        $modifyBiz = new Modify();
        $cancelRes = $modifyBiz->baseCancel($ordernum, $memberId, $memberId, $cancelChannel, $trackMsg,
            $reqSerialNumber, true, true);

        if (in_array($cancelRes['code'], [200, 1095])) {
            $resData = [
                'ordernum'          => $ordernum,
                'req_serial_number' => $cancelRes['data']['req_serial_number'],
                'pft_serial_number' => $cancelRes['data']['pft_serial_number'],
            ];
            if ($cancelRes['code'] == 200) {
                $orderBuz = new \Business\Order\Query();
                $pType    = $orderBuz->getOrderPtype($ordernum, $orderInfo['lid']);
                //特产专用的取消短信通知
                if ($pType == 'J') {
                    $isCancelSms = $orderBuz->getCancelInfoForSms($ordernum, $orderInfo);//获取发送取消短信需要的数据
                }
                if (isset($isCancelSms) && !empty($isCancelSms)) {
                    $this->sendCancelSms($this->getSoap(), $isCancelSms);
                }

                $queryParams = [[$ordernum], true, 'pay_status'];
                $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder',
                    'queryOrderInfoByOrdernumList', $queryParams);
                $payMapping  = [];
                if ($queryRes['code'] == 200) {
                    $payMapping = $queryRes['data'];
                }
                if ($payMapping[$ordernum] == 2) {
                    $succMsg = '订单取消成功';
                } else {
                    $succMsg = '订单取消成功,已支付的金额将原路退回';
                }
            } else {
                $succMsg = '退票审核提交成功';
            }
            $this->apiReturn($cancelRes['code'], $resData, $succMsg);
        } else {
            $this->apiReturn($cancelRes['code'], [], $cancelRes['msg']);
        }
    }

    /**
     * 获取订单列表
     *
     * @param  [type] $memberId 会员id
     * @param  [type] $page     当前页
     * @param  [type] $pageSize 每页条数
     * @param  [type] $type     类型(已使用/历史)
     * @param  [type] $beginDate 开始日期
     * @param  [type] $endDate   结束日期
     * @param  [type] $dateType play/order
     * @param  [type] $returnOption 是否返回查询条件 true 返回 false 不返回
     *
     * @return [type]           [description]
     */
    private function _getOrderList($memberId, $page, $pageSize, $type, $beginDate, $endDate, $dateType, $returnOption = false)
    {
        $Order = new OrderTools('slave');

        if (is_array($memberId)) {
            $where['member'] = ['in', $memberId];
        } else {
            $where['member'] = $memberId;
        }
        if ($this->inUnionApp()) {
            $orderMode = [self::__UNION_ORDER_MODE__];
        } else {
            $orderMode = [self::__ORDER_MODE__, self::__APP_ORDER_MODE__, self::__UNION_ORDER_MODE__, 33, 34];
        }

        if ($type == 'unuse') {
            if ($this->inWechatSmallApp()) {
                $orderStatus = [0, 7];
                $payStatus   = [0, 1];
            } else {
                $orderStatus = [0, 7];
            }
        } elseif ($type == 'unpay') {
            $orderStatus         = [0, 7];
            $where['pay_status'] = [2];
        } elseif ($type == 'history') {
            //历史订单按日期查询
            if ($beginDate && $endDate) {
                //游玩时间
                if ($dateType == 'play') {
                    $where['playtime'] = ['between', [$beginDate, $endDate]];
                } else {
                    //下单时间
                    $where['ordertime'] = ['between', [$beginDate, $endDate]];
                }
            }

            $orderStatus = [1, 2, 3, 4, 5, 6];
        }

        if ($this->inWechatSmallApp()) {
            $field = 'member,ordernum,lid,tid,pid,status,totalmoney,ordermode,
                code,ordertime,dtime,begintime,endtime,tnum,code,ordername,ordertel,pay_status,member,aid, visitors';
        } elseif ($this->inUnionApp()) {
            $field = 'member,ordernum,lid,tid,pid,status,totalmoney,ordermode,
                code,ordertime,dtime,begintime,endtime,tnum,pay_status,member, aid, visitors';
        } else {
            $field = 'member,ordernum,lid,tid,pid,status,totalmoney,ordermode,
                code,ordertime,dtime,begintime,endtime,tnum,pay_status,member,aid, visitors';
        }

        $limit = ($page - 1) * $pageSize . ',' . $pageSize;

        $options = [
            'where' => $where,
            'field' => $field,
            'limit' => $limit,
            'order' => 'id desc',
        ];

        //$queryParams = [$memberId, $orderMode, $orderStatus, $payStatus, $beginDate, $endDate, $page, $pageSize];
        //$queryRes    = \Business\JavaApi\Order\Query\Container::query('orderPage', 'findOpenTicket', $queryParams);

        if ($returnOption) {
            return $options;
        }

        $orders = $Order->getOrders($options);
        $orders = $orders ?: [];

        $total = 0;
        if ($orders) {
            $options['limit'] = 1;
            $options['field'] = 'count(*) as total';
            $total            = $Order->getOrders($options);
            $total            = $total[0]['total'];
        }

        return ['total' => $total, 'list' => $orders];
    }

    /**
     * 解析订单状态(分批验证，退票中)
     *
     * @param  string  $ordernum  订单号
     * @param  int  $status  预定义的订单状态
     *
     * @return [type]           [description]
     */
    private function _parseOrderStatus($ordernum, $status)
    {
        if ($status == 7) {
            //部分验证
            return $status;
        }

        $where = [
            'stype'   => ['in', [2, 3]],
            'dstatus' => 0,
        ];

        $orderModel = new OrderTools('slave');
        $changes    = $orderModel->getOrderChangeRecord($ordernum, 'id', $where);

        if ($changes) {
            return 101; //退票中
        }

        return $status;
    }

    /**
     * 获取订单详情
     *
     * @param  int  $ordernum  订单号
     * @param  int  $mainInfo  订单表信息(ss_order)
     * @param  bool  $show  是否显示门市价
     * @param  int  $customerType  上级用户Id
     * @param  int  $channel  渠道
     *
     * @return array
     */
    private function _getOrderDetail($ordernum, $mainInfo, $show = false, $customerType = 0, $channel = 0)
    {
        $Order        = new OrderTools('slave');
        $Land         = new Land('slave');
        $Ticket       = new Ticket('slave');
        $newTicketApi = new NewTicketApi();
        $orderBiz     = new \Business\Order\OrderList();
        $ticketRes = $orderBiz->getTicketInfo([$mainInfo['tid']]);

        //额外详情
        //$detailInfo = $Order->getOrderDetailInfo($ordernum);
        $queryParams = [$ordernum];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderDetails', 'queryOrderDetailsByOrdernum',
            $queryParams);
        if ($queryRes['code'] != 200) {
            $log = [
                'parame'   => $ordernum,
                'apiData'  => $queryRes,
                'function' => 'queryOrderDetailsByOrdernum',
            ];
            pft_log('OrderDetail/Api/error', json_encode($log));
        }
        $detailInfo = $queryRes['data'];

        $packOrder    = $Order->getPackSubOrder([$ordernum]);
        $packOrderRes = [];
        foreach ($packOrder as $item) {
            //主票包含的子票订单号
            $packOrderRes[$item['pack_order']][] = $item['orderid'];
        }

        //获取联票订单
        $linkOrders = [];
        if ($detailInfo['concat_id'] != 0) {
            $field      = 'ss.totalmoney,ss.tnum,ss.tid,ss.pid,ss.playtime,ss.tprice';
            $linkOrders = $Order->getLinkOrdersInfo($ordernum, $field);
        }
        //景区信息
        $LandInfo = $Land->getLandInfo($mainInfo['lid'], false,
            'p_type,title,imgpath,apply_did,terminal_type,terminal');
        //支付状态
//        $payMapping = $Order->getOrderPayStatus([$ordernum]);
        $queryParams = [[$ordernum], true, 'pay_status'];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder', 'queryOrderInfoByOrdernumList',
            $queryParams);
        $payMapping  = [];
        if ($queryRes['code'] == 200) {
            $payMapping = $queryRes['data'];
        }

        $code = str_pad($mainInfo['code'], ($LandInfo['terminal_type'] == 2 ? 8 : 6), '0', STR_PAD_LEFT);

        //如果是第三方订单且存在第三方订单凭证码， 则获取第三方订单凭证码
        $orderOtaBus   = new OrderOta();
        $handleCodeRes = $orderOtaBus->handleCode($ordernum, $code);
        $noticeType    = $orderOtaBus->getNoticeCode($mainInfo['lid']);
        $resCode       = $handleCodeRes['code'];
        $resNoticeType = $noticeType;
        //第三方订单情况处理
        if ($noticeType != -1) {
            $resCode = $handleCodeRes['code'];//赋值为第三方的凭证码
        }
        //第三方订单凭证码生成类型为默认类型0时候的处理
        if ($noticeType == 0) {
            if (!empty($handleCodeRes['code'])) {
                $resNoticeType = 1;//类型为同步发码
            } else {
                $return['notice_type'] = 3;//类型为不发码
                if ($handleCodeRes['handle_status'] == 2) {
                    //超时情况下
                    $return['notice_type'] = 2;//类型为异步发码
                }
            }
            //all_api_order表没有记录情况为平台订单
            if ($handleCodeRes['handle_status'] == -1) {
                $return['notice_type'] = 0;//类型为非第三方订单
            }
        }
        //同步发码超时情况处理
        if ($noticeType == 1 && empty($handleCodeRes['code']) && $handleCodeRes['handle_status'] == 2) {
            $return['notice_type'] = 2;//类型为异步发码
        }
        //非第三方订单情况处理
        if ($noticeType == -1) {
            $resNoticeType = 0;//类型为非第三方订单
            $resCode       = $code; //重赋值为票付通的凭证码
        }

        $ticketBiz  = new \Business\CommodityCenter\Ticket();
        $ticketInfo = $ticketBiz->fieldConversion($ticketBiz->queryTicketInfoById($mainInfo['tid']));
        //$ticketApi  = new TicketApi();
        //$ticketInfo = $ticketApi->getTickets($mainInfo['tid']);
        //获取快照的属性
        if ($detailInfo['ext_content']) {
            $extContent = json_decode($detailInfo['ext_content'], true);
        } else {
            $extContent = [];
        }

        if ($detailInfo['product_ext']) {
            $extProduct = json_decode($detailInfo['product_ext'], true);
        } else {
            $extProduct = [];
        }

        $version = $extContent['ticketVersion'] ?? 0;

        // 走订单票属性快照
        $ticketBiz      = new \Business\Product\Ticket();
        $ticketSnapshot = $ticketBiz->getTicketSnapShotByCache($mainInfo['tid'], $version);
        if (!$ticketSnapshot) {
            return ['code' => 500, 'msg' => '获取票快照数据错误'];
        }
        $snapshotTicketInfo = isset($ticketSnapshot['uuJqTicketDTO']) ? array_merge($ticketSnapshot['confs'], $ticketSnapshot['uuJqTicketDTO']) : [];

        //是否有改签记录
        $isChangeLog       = false;
        $ticketReserve     = false;
        $isCanChangeTicket = 0;
        $canWeekend        = 0;
        //演出是否可选座
        $selectSeat        = 0;
        $reserveTime       = '';
        //演出预约有效期时间范围
        $reserveTimeArr = [];

        //套票不能改签
        if (!isset($packOrderRes[$ordernum]) || is_null($packOrderRes[$ordernum])) {
            //查询终端订单变动表是否存在改签记录
            $auditModel  = new RefundAuditModel();
            $changeInfos = $auditModel->getTicketChangingOrders([$ordernum], 'ordernum,dstatus');
            if ($changeInfos) {
                $changeInfo = array_shift($changeInfos);
                if (in_array($changeInfo['dstatus'], [0, 1, 2, 3])) {
                    //未审核，同意，等待第三方,拒绝
                    //已经改签过或者有待审核改签
                    $isChangeLog = true;
                }
            }
            //判断是否能改签到周末
            if (isset($snapshotTicketInfo['ticketChangingRange']) && isset($snapshotTicketInfo['preSale'])) {
                $isOnlineReserve = isset($snapshotTicketInfo['is_online_reserve']) ? $snapshotTicketInfo['is_online_reserve'] : 0;
                if ($snapshotTicketInfo['preSale'] == 0 || ($snapshotTicketInfo['preSale'] == 1 && $isOnlineReserve == 1)) {
                    $isCanChangeTicket = $snapshotTicketInfo['ticketChangingRange'];
                }
                $canWeekend = $snapshotTicketInfo['ticketChangingWeekend'];
            }
        }

        //改签按钮
        $changingButton = $isCanChangeTicket > 0 && !$isChangeLog && $mainInfo['status'] == CommonOrderStatus::UNUSED_CODE && $mainInfo['pay_status'] != 2 ? true : false;

        //演出是否可预约
        if ($LandInfo['p_type'] == 'H' && $snapshotTicketInfo['preSale'] == 1 && $mainInfo['status'] == CommonOrderStatus::WAIT_APPOINTMENT_CODE && $mainInfo['pay_status'] != 2) {
            $ticketReserve = true;

            $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
            $tmpTicketRes       = $commodityTicketBiz->queryTicketInfoById($mainInfo['tid']);
            if ($tmpTicketRes) {
                $ticketInfo  = array_merge($tmpTicketRes['land'], $tmpTicketRes['ext'], $tmpTicketRes['land_f'], $tmpTicketRes['ticket']);
                $reserveTime = (new \Business\Product\Ticket())->showReserveTime($ticketInfo);

                //获取一次演出预约的开始时间和结束时间
                if (isset($ticketInfo['reserve_storage'])) {
                    $reserveTimeArr = [
                        'start_time' => strtotime($ticketInfo['reserve_storage']['start_time']),
                        'end_time'   => strtotime($ticketInfo['reserve_storage']['end_time']),
                    ];
                }
            }
        }

        if ($LandInfo['p_type'] == 'H') {
            //演出的需要判断下供应商是否有选座的功能
            $configModel = new \Model\AdminConfig\AdminConfig();
            $result      = $configModel->havePermission($LandInfo['apply_did'], 44);
            $selectSeat  = $result ? 1 : 0;
        }

        //分时预约时间段
        $orderExt = $extContent;
        $timeStr  = $orderExt['sectionTimeStr'] ?? '';

        $showArr = [];
        if (isset($ticketRes[$mainInfo['tid']]) && $LandInfo['p_type'] == 'H') {
            $series      = $detailInfo['series'] ?? '';
            if ($series) {
                $showInfo    = $orderBiz->getShowInfo($detailInfo['series'] ?? '', [$ordernum => $mainInfo['tid']], $ordernum,
                    $ticketRes);
            } else {
                $showInfo = [
                    'venue_id' => $ticketRes[$mainInfo['tid']]['venus_id'],
                    'zone_id'  => $ticketRes[$mainInfo['tid']]['zone_id'],
                ];
            }

            $venueId     = $showInfo['venue_id'] ?? '';
            $zoneId      = $showInfo['zone_id'] ?? '';
            $partition   = $showInfo['partition'] ?? '';
            $seat        = $showInfo['seat'] ?? '';
            $performance = $showInfo['performance'] ?? '';
            $showArr     = [
                'aid'         => $mainInfo['aid'],
                'pid'         => $mainInfo['pid'],
                'venue_id'    => $venueId,
                'zone_id'     => $zoneId,
                'partition'   => $partition,
                'seat'        => $seat,
                'performance' => $performance,
            ];
        }

        //是否显示取消按钮
        $showCancel = OrderList::isAllowCancel($ordernum, $mainInfo['member'], $mainInfo['callback']);
        $return     = [
            'lid'                     => $mainInfo['lid'],
            'tid'                     => $mainInfo['tid'],
            'pid'                     => $mainInfo['pid'],
            'ltitle'                  => $LandInfo['title'],
            'ordernum'                => $ordernum,
            'code'                    => $resCode,
            'notice_type'             => $resNoticeType,//0：非第三方订单 1:同步发码 2:异步发码 3:不发码4.异步查码发码
            'ordertime'               => $mainInfo['ordertime'],
            'verifytime'              => $mainInfo['dtime'],
            'canceltime'              => $mainInfo['ctime'],
            'status'                  => $mainInfo['status'],
            'begintime'               => $mainInfo['begintime'],
            'endtime'                 => $mainInfo['endtime'],
            'ordername'               => $mainInfo['ordername'],
            'ordertel'                => $mainInfo['ordertel'],
            'tprice'                  => $mainInfo['tprice'],
            'ptype'                   => $LandInfo['p_type'],
            'imgpath'                 => $LandInfo['imgpath'],
            'apply_did'               => $LandInfo['apply_did'],
            'cancel'                  => $showCancel,
            'pay_status'              => $mainInfo['pay_status'],
            'pay'                     => intval($mainInfo['status'] == 0 && $payMapping[$ordernum] == 2),
            'terminal'                => $LandInfo['terminal'],
            'assembly'                => $detailInfo['assembly'],
            'expire_action_days'      => $ticketInfo['expire_action_days'],
            'getaddr'                 => isset($ticketInfo['get_ticket_info']) ? $ticketInfo['get_ticket_info'] : '',
            'entry_method'            => isset($ticketInfo['ext']['entry_method']) ? $ticketInfo['ext']['entry_method'] : 0,
            'is_show_ordernum'        => isset($ticketInfo['ext']['is_show_ordernum']) ? $ticketInfo['ext']['is_show_ordernum'] : 0,
            'time_str'                => $timeStr,
            'ticket_change'           => $changingButton,
            'reserve_time'            => $reserveTime,
            'reserve_time_map'        => $reserveTimeArr,
            'ticket_reserve'          => $ticketReserve,
            'select_seat'             => $selectSeat,
            'reservation_time'        => isset($extProduct['reservationOperateTime']) ? $mainInfo['playtime'] . ' ' . $timeStr : '',
            'product_ext'             => $extProduct,
            'ticket_changing_range'   => $isCanChangeTicket,
            'ticket_changing_weekend' => $canWeekend,
            'show_arr'                => $showArr,
            'is_show_used_detail'     => $LandInfo['p_type'] == 'F' ? false : ($ticketInfo['check_terminal_info'] != '' && $ticketInfo['check_terminal_info'] != '0' ? true : false),//是否使用分终端
            'aid'                     => $mainInfo['aid'],
        ];

        if ($mainInfo['status'] == 0) {
            $groupModel  = new GroupBooking();
            $groupOrders = $groupModel->parseTBCOrders([$ordernum]);
            if ($groupOrders) {
                $return['status'] = 102;//待确认状态
            }
        }
        //获取订单可能关联涉及到的营销活动
        $activityOrderModel = new ActivityOrders();
        $activityInfo       = $activityOrderModel->getActivityInfoByOrderNum($ordernum);
        if (!empty($activityInfo)) {
            $return['activity_id'] = $activityInfo['activity_id'];
            $activityTypeStr       = 'common';
            switch ($activityInfo['activity_type']) {
                //活动类型：1全民营销2砍价3海报订单4拼团订单5抢购订单
                case '1':
                    $activityTypeStr = 'allDis';
                    break;
                case '2':
                    $activityTypeStr = 'cut';
                    break;
                case '3':
                    $activityTypeStr = 'default';
                    break;
                case '4':
                    $activityTypeStr = 'group_book';
                    break;
                case 5:
                    $activityTypeStr = 'seckill';
                    break;
                default:
                    $activityTypeStr = 'common';
                    break;
            }
            $return['activity_type'] = $activityTypeStr;
            $return['aid']           = empty($activityInfo['aid']) ? $activityInfo['sid'] : $activityInfo['aid'];
        }

        //已支付的获取订单状态(退票中，部分验证)
        if ($payMapping[$ordernum] == 1) {
            $return['status'] = $this->_parseOrderStatus($ordernum, $return['status']);
            if ($return['status'] == 101) {
                $return['cancel'] = 0;
            }

            // 如果是部分验证的获取已经验证的数量
            // 获取已经验证的数量
            $subOrderModel         = new \Model\Order\SubOrderQuery();
            $res                   = $subOrderModel->getInfoInApplyInfoByOrder($ordernum,
                'orderid, refund_num, verified_num, origin_num');
            $return['verifiedNum'] = isset($res['verified_num']) ? $res['verified_num'] : 0;
        }

        //订单总金额
        $totalmoney = 0;
        $ticket     = [];
        //包含的门票票信息
        if ($linkOrders) {
            foreach ($linkOrders as $item) {
                //都显示零售价  ->  显示门市价，by zhangyangzhen 2019-01-08
                if ($show) {
                    //南宫的产品显示零售价，其他的显示门市价 -- by zhangyangzhen 2019-01-16
                    //线上南宫ID = 211777
                    if ($LandInfo['apply_did'] == '211777') {
                        $tmpPrice                 = $Ticket->getRetailPrice($item['pid'], $item['playtime']);
                        $item['totalmoney']       = round($tmpPrice * $item['tnum'] * 100, 2);
                        $return['isCounterPrice'] = 0;
                    } else {
                        $tmpPrice                 = $Ticket->getCounterPrice($item['pid'], $item['playtime']);
                        $item['totalmoney']       = round($tmpPrice * $item['tnum'] * 100, 2);
                        $return['isCounterPrice'] = 1;
                    }
                }

                $totalmoney += $item['totalmoney'];
                // 票信息通过java接口获取，新增渠道规则 -- jinmin
                $res    = $newTicketApi->queryTicketById($item['tid'], $customerType, $channel);
                $tInfo  = $res['data'];
                $ticket = $newTicketApi->mapAttribute($tInfo, 'ticket');
//                $ticket = $Ticket->getTicketInfoById($item['tid'], 'title,refund_rule');

                $sonTickets[] = [
                    'title'       => $ticket['title'],
                    'num'         => $item['tnum'],
                    'refund_rule' => $ticket['refund_rule'],
                    'apply_did'   => $ticket['apply_did'],
                    'salePrice'   => isset($tmpPrice) ? $tmpPrice : $item['tprice'],
                ];
            }
        } else {
            // 票信息通过java接口获取，新增渠道规则 -- jinmin
            $res    = $newTicketApi->queryTicketById($mainInfo['tid'], $customerType, $channel);
            $tInfo  = $res['data'];
            $ticket = $newTicketApi->mapAttribute($tInfo, 'ticket');
//            $ticket = $Ticket->getTicketInfoById($mainInfo['tid'], 'title,refund_rule');
            $tmpPrice = $mainInfo['tprice'];
            //都显示零售价  -> 显示门市价，by zhangyangzhen 2019-01-08

            //期票要用ordertime去查询
            $orderPlayTime = $mainInfo['playtime'];
            if ($ticket['preSale'] == 1) {
                $orderPlayTime = date('Y-m-d', strtotime($mainInfo['ordertime']));
            }
            if ($show) {
                //线上南宫ID = 211777
                if ($LandInfo['apply_did'] == '211777') {
                    $tmpPrice                 = $Ticket->getRetailPrice($mainInfo['pid'], $orderPlayTime);
                    $mainInfo['totalmoney']   = round($tmpPrice * $mainInfo['tnum'] * 100, 2);
                    $return['isCounterPrice'] = 0;
                } else {
                    $tmpPrice                 = $Ticket->getCounterPrice($mainInfo['pid'], $orderPlayTime);
                    $mainInfo['totalmoney']   = round($tmpPrice * $mainInfo['tnum'] * 100, 2);
                    $return['isCounterPrice'] = 1;
                }
            }

            $sonTickets[] = [
                'title'       => $ticket['title'],
                'num'         => $mainInfo['tnum'],
                'refund_rule' => $ticket['refund_rule'],
                'apply_did'   => $ticket['apply_did'],
                'salePrice'   => $tmpPrice,
            ];

            $totalmoney = $mainInfo['totalmoney'];
        }
        //如果是对接三方系统的,ota选择不发短信,返回三方系统的码——cgp@2017.09.13

        // 切换门票获取第三方系统信息
        $otaProductBiz    = new \Business\Ota\Product();
        $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTid($mainInfo['tid']);
        if (empty($thirdBindInfoArr)) {

        }
        $Mdetails = $thirdBindInfoArr['Mdetails'];
        $Mpath    = $thirdBindInfoArr['Mpath'];
        $sourceT  = $thirdBindInfoArr['sourceT'];

        if ($Mdetails == 1) {
            $addOnInfo = $Order->getOrderAddonInfo($ordernum, 'vcode');
            if (!empty($addOnInfo['vcode'])) {
                $return['code'] = $addOnInfo['vcode'];
            }
            // 九天系统，凭证号特殊处理---Pzp@2019/03/19
            if ($sourceT == 1 && $addOnInfo['vcode'] != '') {
                $return['code'] = str_pad($addOnInfo['vcode'], ($LandInfo['terminal_type'] == 2 ? 8 : 6), '0',
                    STR_PAD_LEFT);
            }
        }

        // 搜索订单使用的优惠券金额
        $orderCouponModel = new \Model\Order\Coupon('remote_1');
        $useCouponInfoArr = $orderCouponModel->getUseInfoByUseOrder($ordernum, 'id, all_use_coupon_money');
        if (empty($useCouponInfoArr)) {
            $return['coupon_money'] = 0;
        } else {
            $return['coupon_money'] = $useCouponInfoArr[0]['all_use_coupon_money'] / 100;
        }

        $return['totalmoney'] = $totalmoney / 100;
        $return['tickets']    = $sonTickets;

        switch ($LandInfo['p_type']) {
            case 'A':
                $return = $this->_showExternalCode($return);
                break;
            case 'B':
                $return = $this->_fillOrderInfoForRoute($return);
                break;

            case 'C':
                $return = $this->_fillOrderInfoForHotel($return);
                break;

            case 'H':
                $return = $this->_fillOrderInfoForShow($return);
                break;

            case 'I':
                $return = $this->_fillOrderInfoForAnnual($return);
                break;

            case 'J':
                $return = $this->_fillOrderInfoForSpecial($return);
                break;

            case 'F':
                $return = $this->_fillOrderInfoForPackage($return);
                break;
            default:
                # code...
                break;
        }

        return $return;
    }

    /**
     * 获取个人中心的显示菜单
     * @return [type] [description]
     */
    private function _getMenus()
    {
        $memberId = $this->isLogin('ajax');

        $menus = [

            'orderList' => [
                'name' => '我的订单',
                'data' => [],
            ],
            'card'      => [
                'name' => '会员卡',
                'data' => [],
            ],
            'coupon'    => [
                'name' => '优惠券',
                'data' => [],
            ],
            'invoice'   => [
                'name' => '开发票',
                'data' => [],
            ],
            // 'poster' => [
            //     'name'  => '分享店铺',
            //     'data' => []
            // ],
        ];

        if ($this->_allDisMan) {
            return $menus;
        } else {
            unset($menus['saleCenter']);
            unset($menus['poster']);

            return $menus;
        }
    }

    /**
     * 绑定微信账号
     *
     * @param  [type] $session 当前回话信息
     *
     * @return [type]          [description]
     */
    private function _bindWechat($session)
    {

        $data = $this->_getOpenInfo();

        if (!$data) {
            return true;
        }

        $res = (new \Model\Wechat\WxMember())->bindWechat(
            $session['memberID'],
            self::__DEFAULT_MEMBERID,
            $data['openid'],
            $data['appid']
        );

        return $res;

    }

    /**
     * 注册平台账号
     *
     * @param  int  $mobile  手机号
     * @param  string  $username  昵称
     *
     * @return mixed    false | memberId
     */
    private function _registerAccount($mobile, $username = '')
    {

        $password = md5(md5(substr($mobile, 5, 6)));

        $data = [
            'mobile'   => $mobile,
            'password' => $password,
            'dname'    => $username,
            'dtype'    => 5,
        ];

        //注册
        $memberId = (new \Model\Member\Member())->register($data);

        return $memberId ?: false;

    }

    /**
     * 注册全民营销
     *
     * @param  [type]  $openid        微信openid
     * @param  [type]  $supplyAccount 供应商账号
     * @param  integer  $parentid  邀请者id
     *
     * @return [type]                 [description]
     */
    private function _registerAllDis($openid, $supplyAccount, $parentId = 0)
    {

        $authBiz = new \Business\Wechat\Authorization();
        //获取微信账户信息
        $res = $authBiz->getOpenUserInfo($openid);

        if ($res['code'] != 200) {
            return false;
        }

        $userInfo = $res['data'];

        $AllDis = new \Model\Mall\AllDis();
        //现在未关注获取不到详细的用户信息..
        if ($userInfo['subscribe'] == 0 && !isset($userInfo['nickname'])) {

            $maxId = $AllDis->getMaxId();
            $maxId = $maxId + 1;

            $userInfo = [
                'openid'     => $userInfo['openid'],
                'unionid'    => $userInfo['unionid'],
                'nickname'   => 'wechat' . $maxId,
                'headimgurl' => self::__DEFAULT_AVATAR__,
                'city'       => '',
                'province'   => '',
                'sex'        => 1,
            ];

        }
        $memberId = $AllDis->joinAllDis($userInfo, $supplyAccount, $parentId);

        if ($memberId === false) {
            return false;
        } else {
            return [$memberId, $userInfo];
        }

    }

    /**
     * 直接跳转到微商城首页
     *
     * @param  [type] $supplyAccount 供应商账号
     *
     * @return [type]                [description]
     */
    private function _redirectToIndex($supplyAccount)
    {

        $this->_redirectToPage($supplyAccount, 'index');
    }

    /**
     * 跳到微商城指定页面
     * <AUTHOR>
     * @date   2017-12-25
     *
     * @param  int  $supplyAccount  供应商账号
     */
    private function _redirectToPage($supplyAccount, $page = 'index', $url = '')
    {

        if ($page !== 'h5') {
            if (!$url) {
                $url = $this->_getPageUrl($supplyAccount, $page);
            }
        }

        // pft_log("allDis/login", "跳转地址---".$url);

        header('Location:' . $url);
        exit();
    }

    /**
     * 根据页面标识获取页面url
     * <AUTHOR>
     * @date   2018-01-11
     *
     * @param  string  $supplyAccount  账号，123624
     * @param  string  $page  页面标识
     *
     * @return string
     */
    private function _getPageUrl($supplyAccount, $page = 'index')
    {

        $domain = str_replace('wx', $supplyAccount, MOBILE_DOMAIN);

        switch ($page) {
            //首页
            case 'index':
                $url = $domain . 'wx/c/index.html';
                break;
            case 'login':
                $url = $domain . 'wx/login.html';
                break;
            //个人中心
            case 'usercenter':
                $url = $domain . 'wx/c/usercenter.html';
                break;
            //订单列表
            case 'orderlist':
                $url = $domain . 'wx/c/userorder.html';
                break;
            //特产购买页(支付)
            case 'speciality':
                $url = $domain . 'wx/c/speciality.html';
                break;
            //年卡续费
            case 'annual_renew':
                $url = MOBILE_DOMAIN . 'html/member_card_renew.html';
                break;
            //绑定年卡页面
            case 'bindAnnual':
                $url = $domain . 'wx/c/card_load.html#/bindCardUser';
                break;
            case 'activity_annual':
                $url = $domain . 'wx/c/card_load.html#/';
                break;
            //我的年卡页面
            case 'myCard':
                $url = $domain . 'wx/c/member_card.html#/';
                break;
            default:
                $url = $domain . 'wx/c/index.html';
                break;
        }

        return $url;
    }

    /**
     * 填充线路信息
     *
     * @param  array  $info  订单详情
     *
     * @return [type]       [description]
     */
    private function _fillOrderInfoForRoute($info)
    {

        $ext     = (new \Model\Product\Ticket())->getTicketExtInfoByTid($info['tid'], 'ass_station');
        $station = json_decode($ext['ass_station'], true);

        $info['station'] = isset($station[$info['assembly']]) ? [$station[$info['assembly']]] : [];

        return $info;
    }

    /**
     * 填充酒店信息
     *
     * @param  array  $info  订单详情
     *
     * @return [type]       [description]
     */
    private function _fillOrderInfoForHotel($orderInfo)
    {

        $link = (new \Model\Order\OrderTools())->getLinkOrdersInfo($orderInfo['ordernum'], 'endtime');

        //$last = array_pop($link);
        //
        //if (!$last) {
        //    $last['playtime'] = $orderInfo['begintime'];
        //}

        $endTimeStr = max(array_column($link, 'endtime'));
        if (empty($endTimeStr)) {
            $endTimeStr = $orderInfo['endtime'];
        }

        $begintime = strtotime($orderInfo['begintime']);
        //$endtime   = strtotime($last['playtime']) + 3600 * 24;
        $endtime   = strtotime($endTimeStr);

        //住店时间
        $date = $orderInfo['begintime'] . '~' . date('Y-m-d', $endtime);

        //住店天数
        $days = ($endtime - $begintime) / 3600 / 24;

        $orderInfo['days']    = $days;
        $orderInfo['endtime'] = date('Y-m-d', $endtime);

        return $orderInfo;
    }

    /**
     * 填充演出座位信息
     *
     * @param  [type] $info 订单详情
     *
     * @return [type]       [description]
     */
    private function _fillOrderInfoForShow($info)
    {
        //$detail = (new \Model\Order\OrderTools())->getOrderDetailInfo($info['ordernum']);
        $queryParams = [$info['ordernum']];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderDetails', 'queryOrderDetailsByOrdernum',
            $queryParams);
        if ($queryRes['code'] != 200) {
            $log = [
                'parame'   => $info['ordernum'],
                'apiData'  => $queryRes,
                'function' => 'queryOrderDetailsByOrdernum',
            ];
            pft_log('fillOrderInfo/Api/error', json_encode($log));
        }
        $detail = $queryRes['data'];

        //分区名
        $zoneName = (new \Model\Product\Ticket())->getZoneName([$info['tid']]);

        $ticketExtInfo = [];
        $ticketBiz     = new \Business\JavaApi\CommodityCenter\Ticket();
        $getTicketInfo = $ticketBiz->queryTicketAttrsById($info['tid']);
        if ($getTicketInfo['code'] == 200) {
            foreach ($getTicketInfo['data'] as $attr) {
                if ($attr['key'] == 'is_show_seat') {
                    $ticketExtInfo[$attr['ticket_id']] = $attr;
                }
            }
        }
        //$ticketExtInfo = (new \Model\Product\Ticket())->getTicketExtConfig([$info['tid']], 'ticket_id,key,val',
        //    ['is_show_seat']);
        $isShowSeat = 1;
        if (!empty($ticketExtInfo[$info['tid']]['val']) && $ticketExtInfo[$info['tid']]['val'] == 2) {
            $isShowSeat = 2;
        }

        $seat = unserialize($detail['series']);

        //这里在原先的演出座位号后面加上"排座"单位
        $seatInfo = $seat[5];
        if (empty($seatInfo)) {
            $tmpSeatArr = [];
        } else {
            $tmpSeatArr = explode('_', $seatInfo);
        }

        $newSeatArr = [];

        foreach ($tmpSeatArr as $tmpSeat) {
            $tmpArr       = explode('-', $tmpSeat);
            $newSeatArr[] = $tmpArr[0] . '排' . '-' . $tmpArr[1] . '座' . '-' . $tmpArr[2];
        }

        $newSeatStr = trim(implode('_', $newSeatArr), '_');

        //演出座位修改
        $showBiz = new Show();
        $seatFive = $showBiz->handleSeatParamsFenQu($newSeatStr, "({$zoneName[$info['tid']]['zone_name']})");

        $info['seat'] = [
            $seat[11] ?? $seat[4],
            //$newSeatStr . "({$zoneName[$info['tid']]['zone_name']})",
            $seatFive,
            $seat[6],
        ];

        if ($isShowSeat == 2) {
            $info['seat'][1] = $zoneName[$info['tid']]['zone_name'];
        }

        return $info;
    }

    /**
     * 填充年卡虚拟卡号和物理卡号
     * <AUTHOR>
     * @date   2017-11-22
     *
     * @param  array  $info  订单详情
     *
     * @return array
     */
    private function _fillOrderInfoForAnnual($info)
    {

        $ordernum = $info['ordernum'];

        $annualModel = new \Model\Product\AnnualCard('slave');

        //获取虚拟卡号
        $virtualNo = $annualModel->getVirtualNoByOrdernum($ordernum);

        $info['virtual_no'] = $virtualNo ?: '';
        $info['physics_no'] = '';

        return $info;
    }

    /**
     * 获取特产订单信息
     * <AUTHOR>
     * @date   2017-12-13
     *
     * @param  array  $info  订单信息
     *
     * @return array
     */
    private function _fillOrderInfoForSpecial($info)
    {
        //$orderToolsModel = new OrderTools('slave');
        //$detailInfo      = $orderToolsModel->getOrderDetailInfo($info['ordernum'], 'product_ext,memo');
        $queryParams = [$info['ordernum']];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderDetails', 'queryOrderDetailsByOrdernum',
            $queryParams);
        if ($queryRes['code'] != 200) {
            $log = [
                'parame'   => $info['ordernum'],
                'apiData'  => $queryRes,
                'function' => 'queryOrderDetailsByOrdernum',
            ];
            pft_log('OrderInfoForSpecial/Api/error', json_encode($log));
        }
        $detailInfo  = $queryRes['data'];
        $extArr      = json_decode($detailInfo['product_ext'], true);
        $deliveryWay = $extArr['deliveryType'] ?? 1;

        $info['special']                 = [];
        $info['special']['delivery_way'] = $deliveryWay;

        if ($deliveryWay) {
            $pickPointApi = new \Business\JavaApi\Express\PickPoint();
            $pickData     = $pickPointApi->queryExpressPickPointByTid($info['tid']);

            $areaCodeArr = explode('|', $pickData['areaCode']);
            $areaCodeArr = array_filter($areaCodeArr);
            if ($areaCodeArr) {
                $areaModel = new \Model\Product\Area();
                $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
            } else {
                $codeMap = [];
            }

            //自提点信息
            $info['special']['self_pick_up'] = [
                'area'           => implode('', array_values($codeMap)),
                'address'        => $pickData['address'],
                'linkman'        => $pickData['linkman'] . '/' . $pickData['phone'],
                'reception_time' => $pickData['acceptTime'],
                'memo'           => $detailInfo['memo'],
            ];
        } else {
            //快递
            //收货信息
//            $orderUserModel = new \Model\Order\OrderUser();
//            $orderUserInfo  = $orderUserModel->getOneOrderUserByOrdernum($info['ordernum'],
//                'personid,voucher_type,province_code,city_code,town_code,address');
            $queryParams   = [[$info['ordernum']]];
            $queryRes      = \Business\JavaApi\Order\Query\Container::query('orderUserInfo',
                'getOrderUserInfoByOrderNum', $queryParams);
            $orderUserInfo = [];
            if ($queryRes['code'] == 200) {
                $orderUserInfo = $queryRes['data'][0];
            }

            $areaCodeArr = [$orderUserInfo['province_code'], $orderUserInfo['city_code'], $orderUserInfo['town_code']];
            $areaCodeArr = array_filter($areaCodeArr);
            if ($areaCodeArr) {
                $areaModel = new \Model\Product\Area();
                $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
            } else {
                $codeMap = [];
            }

            $info['special']['exp_info'] = [
                'province_code' => $codeMap[$orderUserInfo['province_code']] ?? '',
                'city_code'     => $codeMap[$orderUserInfo['city_code']] ?? '',
                'town_code'     => $codeMap[$orderUserInfo['town_code']] ?? '',
                'address'       => $orderUserInfo['address'],
                'exp_company'   => $extArr['expCompany'] ?? '',
                'exp_no'        => $extArr['expNo'] ?? '',
                'memo'          => $detailInfo['memo'],
            ];
        }

        return $info;
    }

    public function _fillOrderInfoForPackage($info)
    {
        //获取子票名称和分时预约信息
        $orderToolsModel = new OrderTools('slave');
        $sonOrderList    = $orderToolsModel->getPackOrdersInfo($info['ordernum']);
        $sonOrderMap     = array_key($sonOrderList, 'ordernum');
        $orderArr        = array_column($sonOrderList, 'ordernum');

        $tidArr       = $tidMap = [];
        $packSonOrder = [];
        //预约子票数组
        $packAlreadyReservation = [];
        //演出子票信息
        $packageSeriesInfo = [];
        //套票子票使用详情
        $packSonUsedDetails = [];
        //获取子票detail信息
        $details = $orderToolsModel->getOrderDetailInfo($orderArr, 'orderid,ext_content,product_ext');
        $applyInfoData = array_column($orderToolsModel->getOrderApplyInfo($orderArr),null,'orderid');
        foreach ($details as $detail) {
            $tmpTid          = $sonOrderMap[$detail['orderid']]['tid'];
            $tidArr[]        = $tmpTid;
            $ext             = json_decode($detail['ext_content'], true);
            $extProduct      = json_decode($detail['product_ext'], true);
            $timeStr         = $ext['sectionTimeStr'] ?? '';
            $reservationTime = isset($extProduct['reservationOperateTime']) ? $sonOrderMap[$detail['orderid']]['playtime'] . ' ' . $timeStr : '';
            //子票是否启用分时预约
            if ($timeStr) {
                $tidTimeMap[$tmpTid] = $timeStr;
            }
            if ($sonOrderMap[$detail['orderid']]['status'] == CommonOrderStatus::WAIT_APPOINTMENT_CODE) {
                $packSonOrder[$sonOrderMap[$detail['orderid']]['tid']] = [
                    'ordernum' => OrderNotify::url_sms($detail['orderid']),
                    'tid'      => $sonOrderMap[$detail['orderid']]['tid'],
                ];
            }
            if ($reservationTime) {
                $packAlreadyReservation[$sonOrderMap[$detail['orderid']]['tid']] = [
                    'reservation_time_str' => $reservationTime,
                ];
            }
            $packSonUsedDetails[$tmpTid] = [
                'tid'  => $tmpTid,
                'verify_num'  => $applyInfoData[$detail['orderid']]['verified_num'],
                'total_num'   => $sonOrderMap[$detail['orderid']]['tnum'],
                'is_show_used_detail' => false,
                'ordernum'    => $detail['orderid'],
            ];
            $packageSeriesInfo[$tmpTid] = $detail['series'];
        }

        $packageTimeStr        = [];
        $packageReservationStr = [];
        $packageShowInfoStr    = [];
        $tmpTicketMap          = [];
        if ($tidArr) {
            //获取子票的名称
            $javaApi   = new \Business\CommodityCenter\Ticket();
            $ticketMap = $javaApi->getTicketAndLandInfoByArrTidHandle($tidArr);

            foreach ($ticketMap as $ticket) {
                if ($packSonOrder[$ticket['id']]) {
                    $packSonOrder[$ticket['id']]['title'] = $ticket['title'] . $ticket['ttitle'];
                }
                if (isset($tidTimeMap[$ticket['id']])) {
                    $packageTimeStr[] = $ticket['title'] . $ticket['ttitle'] . '/' . $tidTimeMap[$ticket['id']];
                }
                if (isset($packAlreadyReservation[$ticket['id']])) {
                    $packageReservationStr[] = $ticket['title'] . $ticket['ttitle'] . '/' . $packAlreadyReservation[$ticket['id']]['reservation_time_str'];
                }

                //结构特殊处理下
                $tmpTicketMap[$ticket['id']] = [
                    'ltitle' => $ticket['title'],
                    'title'  => $ticket['ttitle'],
                ];
                if (isset($packSonUsedDetails[$ticket['id']])){
                    $packSonUsedDetails[$ticket['id']]['is_show_used_detail'] = ($ticket['chk_terminal_info'] != '' && $ticket['chk_terminal_info'] != 0) ? true : false;
                    $packSonUsedDetails[$ticket['id']]['title'] = $ticket['ttitle'];
                }
            }
        }

        //获取子票演出信息
        if ($packageSeriesInfo) {
            $orderListBiz       = new \Business\Order\OrderList();
            $packageShowInfoStr = $orderListBiz->getPackageShowInfo($packageSeriesInfo, $tmpTicketMap);
        }
        $info['package_son_used_detail']      = array_values($packSonUsedDetails);
        $info['package_son_reservation_info'] = array_values($packSonOrder);
        $info['package_time_str']             = $packageTimeStr;
        $info['pack_reservation_time']        = $packageReservationStr;
        $info['package_show_info_str']        = $packageShowInfoStr;

        return $info;
    }

    /**
     * 是否展示外部码信息
     * <AUTHOR>  Li
     * @date  2020-08-11
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return array|bool
     */
    private function _showExternalCode($orderInfo)
    {
        $productExt = $orderInfo['product_ext'];
        if (isset($productExt['externalSendCode']) && $productExt['externalSendCode'] == 1) {
            $externalBiz = new \Business\ExternalCode\CodeManage();

            $externalRes = $externalBiz->getExternalCodeInfoByOrdernum($orderInfo['tickets'][0]['apply_did'],
                $orderInfo['ordernum']);
            $tid         = $orderInfo['tid'];
            if (isset($externalRes['code']) && $externalRes['code'] == 200) {
                $orderInfo['externalCode']['code'] = $externalRes['data']['list'];

                //查询是否设置游玩日期
                $javaApi   = new \Business\CommodityCenter\Ticket();
                $ticketArr = $javaApi->batchQueryTicketByTicketIds([$tid], 'pre_sale');
                $preSale   = $ticketArr[0]['pre_sale']; //是否选择游玩日期 1否0是

                //有效期开始和结束时间的处理
                $expireType = $externalRes['data']['expire_type']; //发送给游客的有效期：1导码设置的有效期；2下单选择的游玩日期；3固定有效期
                $orderTime  = $orderInfo['ordertime']; //下单时间
                $playTime   = $orderInfo['begintime']; //游玩时间 对应字段为playtime， 前面传值的时候赋值给begintime
                $expireTime = $externalRes['data']['expire_time'];  //外部码过期时间， 0为长期有效
                $startDt    = $externalRes['data']['start_dt']; //固定日期的开始时间
                $endDt      = $externalRes['data']['end_dt']; //固定日期的结束时间
                //有效期处理
                $handleRes = $externalBiz->handleExternalCodeOrderTime($expireType, $preSale, $orderTime, $playTime,
                    $expireTime, $startDt, $endDt);
                if (isset($handleRes['code']) && $handleRes['code'] == 200) {
                    $orderInfo['externalCode']['begin_time'] = $handleRes['data']['begin_time'];
                    $orderInfo['externalCode']['end_time']   = $handleRes['data']['end_time'];
                }
            }
        }

        return $orderInfo;
    }

    /**
     * 分销商申请以OPENID散客账号登录
     * <AUTHOR>
     * @date   2016-12-8
     */
    public function resellerUseSankeAccountLogin()
    {
        if (I('session.sid')) {
            $this->logout(true);  //解绑
        }

        if ($this->inWechatApp()) {
            // 微信端
            if (isset($_SERVER['HTTP_REFERER'])) {
                $fromUrl = $_SERVER['HTTP_REFERER'];
                $query   = parse_url($fromUrl, PHP_URL_QUERY);
            }
            $query = $query ? '?' . $query : '';
            $url   = str_replace('wx', $this->_supplyAccount, MOBILE_DOMAIN) .
                     'r/Mall_Member/autoLoginByOpenId' . $query;
            // $url     = str_replace('https://', 'http://', $url);

            $this->apiReturn(200, ['url' => $url], '跳转微信授权');

        } else {
            // H5端
            $this->apiReturn(401, [], '请换号码登录');
        }
    }

    /**
     * 分销商以OPENID散客账号登录
     * <AUTHOR>
     * @date   2016-12-8
     */
    public function autoLoginByOpenId()
    {
        if (I('code')) {

            $params = json_decode(base64_decode(I('state')), true);

            $registerData = ['code' => I('code'), 'appid' => $params['appid']];
            //注册
            $registerBiz = new \Business\Member\Register(false);
            $registerRes = $registerBiz->registerCommon('wx', $registerData);
            if ($registerRes['code'] != 200) {
                exit($registerRes['msg']);
            }

            $memberId = $registerRes['data']['mid'];
            $openId   = $registerRes['data']['openid'];

            // 账号登录
            $busSession = new \Business\Member\Session();
            $loginRes   = $busSession->loginMemberId($memberId, 'wx');
            if ($loginRes['code'] != 200) {
                exit($loginRes['msg']);
            }

            //存储appid和openid
            $this->_storeOpenInfo($openId, $params['appid']);
            // 绑定微信账号
            $this->_bindWechat(['memberID' => $memberId]);

            // 登陆跳转
            if (isset($params['from'])) {
                // 订单填写页
                [$aid, $pid] = explode('z', $params['from']);
                if ($aid && $pid) {
                    $url = str_replace('wx', $params['supplyAccount'],
                            MOBILE_DOMAIN) . "wx/c/booking.html?aid={$aid}&pid={$pid}";
                    header("Location: {$url}");
                }
            } else {
                // 微商城首页
                $this->_redirectToPage($params['supplyAccount'], 'index');
            }
        } else {
            // 订单页参数传递
            if (isset($_SERVER['HTTP_REFERER'])) {
                $fromUrl = $_SERVER['HTTP_REFERER'];
                $res     = parse_url($fromUrl, PHP_URL_QUERY);

                //php7+版本 parse_str()未带第二个参数会报错
                parse_str($res, $tmpArr);
                $aid = isset($tmpArr['aid']) ? $tmpArr['aid'] : 0;
                $pid = isset($tmpArr['pid']) ? $tmpArr['pid'] : 0;

                if ($aid && $pid) {
                    $params['from'] = $aid . 'z' . $pid;
                }
            }

            // 调用微信授权
            $params['supplyAccount'] = $this->_supplyAccount;
            $params['appid']         = self::__DEFAULT_APPID__;

            $callback = MOBILE_DOMAIN . 'api/index.php?c=Mall_Member&a=autoLoginByOpenId';

            try {
                //发起授权
                $authBiz = new \Business\Wechat\Authorization();
                $authBiz->requestForAuth($callback, $params);
            } catch (\Exception $e) {
                //授权出错直接跳转到首页
                echo $e->getMessage();
            }
        }
    }

    /**
     * 通过海报进入产品详情页，触发发送海报的动作
     * <AUTHOR>
     * @date   2017-07-27
     */
    public function sendPoster()
    {
    }

    /**
     * 小程序单独的订单列表
     * 获取未使用|历史订单信息
     * @return [type] [description]
     */
    public function getSmallAppOrderList($ajaxReturn = true, $getAllOrder = false)
    {
        $memberId  = $this->isLogin('ajax');
        $pageSize  = I('pageSize', 10, 'intval');
        $page      = I('page', 1, 'intval');
        $type      = I('type') ?: 'unuse';
        $beginDate = I('beginDate', '');
        $endDate   = I('endDate', '');
        $dateType  = I('dateType', 'play');

        if (!in_array($type, ['unuse', 'history'])) {
            $this->apiReturn(204, [], '查询类型错误');
        }

        $Order  = new OrderTools('slave');
        $Land   = new Land('slave');
        $Ticket = new Ticket('slave');

        if ($getAllOrder) {
            $pageSize = 10000;
        }

        //获取订单列表
        $orderRes = $this->_getOrderList($memberId, $page, $pageSize, $type, $beginDate, $endDate, $dateType);
        [$total, $orders] = array_values($orderRes);

        $list = [];
        if ($orders) {
            $lidArr   = array_column($orders, 'lid');
            $tidArr   = array_column($orders, 'tid');
            $orderArr = array_column($orders, 'ordernum');

            //判断订单是否开过票
            $invoiceApi = new InvoiceApi();
            $invoiceRes = $invoiceApi->checkInvoiceRecordByMixed(0, $orderArr, [$memberId]);

            if ($invoiceRes['code'] != 200 || empty($invoiceRes['data'])) {
                $this->apiReturn($invoiceRes['code'], $invoiceRes['data'], $invoiceRes['msg']);
            }

            $invoiceRes['data'] = array_column($invoiceRes['data'], null, 'ordernum');

            foreach ($orders as $key => $value) {
                if (!empty($invoiceRes['data'][$value['ordernum']])) {
                    $orders[$key]['invoice_url'] = $invoiceRes['data'][$value['ordernum']]['image_url'];
                } else {
                    $orders[$key]['invoice_url'] = '';
                }
            }

            //景区信息
            $javaAPi      = new \Business\CommodityCenter\Land();
            $landRes      = $javaAPi->queryLandMultiQueryById($lidArr);
            $landsMapping = array_column($landRes, null, 'id');

            //门票信息
            $ticketsMapping = $Ticket->getMuchTicketInfo($tidArr, 'id,title');

            //支付状态
            if ($type == 'unuse') {
//                $payMapping = $Order->getOrderPayStatus($orderArr);
                $queryParams = [$orderArr, true, 'pay_status'];
                $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder',
                    'queryOrderInfoByOrdernumList', $queryParams);
                $payMapping  = [];
                if ($queryRes['code'] == 200) {
                    $payMapping = $queryRes['data'];
                }
            }
            //获取联票信息
            //$links = $Order->getOrderConcatId($orderArr);
            $queryParams = [$orderArr, 'orderid', 'concat_id'];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderDetail', 'queryOrderDetailsByOrdernums',
                $queryParams);
            $links       = [];
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $links = $queryRes['data'];
            }
            foreach ($orders as $k => $order) {
                $link = [];
                if ($links[$order['ordernum']]) {
                    if ($links[$order['ordernum']] != $order['ordernum']) {
                        continue;
                    } else {
                        $field      = 'ss.tnum,ss.tid,ss.code,ss.totalmoney';
                        $linkOrders = $Order->getLinkOrdersInfo($order['ordernum'], $field);
                        //显示联票的子票
                        foreach ($linkOrders as &$val) {
                            $val['title'] = $ticketsMapping[$val['tid']];
                        }

                        $link = $linkOrders;
                    }
                } else {
                    $link[] = [
                        'tnum'       => $order['tnum'],
                        'tid'        => $order['tid'],
                        'totalmoney' => $order['totalmoney'],
                        'title'      => $ticketsMapping[$order['tid']],
                    ];
                }

                $tmp = [
                    'ltitle'     => $landsMapping[$order['lid']]['title'],
                    'ttitle'     => $ticketsMapping[$order['tid']],
                    'imgpath'    => $landsMapping[$order['lid']]['imgpath'],
                    'pos'        => $landsMapping[$order['lid']]['lng_lat_pos'],
                    'ordernum'   => $order['ordernum'],
                    'totalmoney' => $order['totalmoney'],
                    'tickets'    => $link,
                ];

                if ($type == 'unuse') {
                    $tmp['lid']       = $order['lid'];
                    $tmp['status']    = $order['status'];
                    $tmp['begintime'] = $order['begintime'];
                    $tmp['endtime']   = $order['endtime'];
                    $tmp['ordertime'] = $order['ordertime'];
                    $tmp['ordertel']  = $order['ordertel'];
                    $tmp['ordername'] = $order['ordername'];
                    $tmp['code']      = $order['code'];
                    $tmp['tnum']      = $order['tnum'];
                    $tmp['paystatus'] = $payMapping[$order['ordernum']];
                    $tmp['cancel']    = OrderList::isAllowCancel($order['ordernum'], $memberId);
                    $tmp['pay']       = intval($order['status'] == 0 && $payMapping[$order['ordernum']] == 2);
                    //已支付的获取订单状态(退票中，部分验证)
                    if ($payMapping[$order['ordernum']] == 1) {
                        $tmp['status'] = $this->_parseOrderStatus($order['ordernum'], $tmp['status']);
                        // //分批验证支持退票
                        // if ($tmp['status'] == 7) {
                        //     $tmp['cancel'] = 1;
                        // }
                    }

                } else {
                    $tmp['verifytime'] = $order['dtime'];
                    $tmp['status']     = $order['status'];
                }

                $list[] = $tmp;
            }
        }

        $return = [
            'total'     => $total,
            'list'      => $list,
            'page'      => $page,
            'totalPage' => ceil($total / $pageSize),
        ];

        if (!$ajaxReturn) {
            return $return;
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 小程序 - 我的行程 所有未使用的订单按景区分类
     * <AUTHOR>
     * @date   2017-08-18
     */
    public function myTravel()
    {
        // 所有未使用的订单
        $res = $this->getSmallAppOrderList(false, true);
        if (empty($res['list'])) {
            $this->apiReturn(200, [], '暂无行程订单');
        }
        $orders = $res['list'];

        $memberId = $this->isLogin('ajax');

        // 按景区分组的订单
        $lidOrders = [];

//        $ticketModel     = new Ticket('slave');
        $ticketApi       = new TicketApi();
        $orderToolsModel = new OrderTools('slave');
        $orderQueryModel = new OrderQuery('slave');
        $bizOrderQuery   = new \Business\Order\Query();

        foreach ($orders as $order) {

            if (!in_array($order['paystatus'], [0, 1])) {
                continue;
            }

            // 主票订单号
            $orderNum = $order['ordernum'];

            // 包括主票的联票数组
            $orderNumArr = [];

            // 相关联票信息
            $linkOrders = $orderToolsModel->getLinkSubOrderInfo($orderNum, 'orderid');
            if ($linkOrders) {
                $orderNumArr = array_unique(array_column($linkOrders, 'orderid'));
            } else {
                $orderNumArr = [$orderNum];
            }

            // 查询订单内容
            $tidArr        = []; // 票tid
            $tidOrderMatch = []; // tid与order的对应关系

            $orderInfoRes = $orderToolsModel->getOrderInfo($orderNumArr, 'ordernum, tid');
            if ($orderInfoRes) {
                foreach ($orderInfoRes as $record) {

                    // 同一个票的订单号归纳一起
                    $tidOrderMatch[$record['tid']][] = $record['ordernum'];

                    // 单独提取tid
                    $tidArr[] = $record['tid'];
                }

                $tidArr = array_unique($tidArr);
            }

            // 票信息通过java接口获取，新增渠道规则
            $ticketApi      = new \Business\JavaApi\TicketApi();
            $memberIdAndAid = $ticketApi::getOrderAidByMemberIdAndAid($order['ordernum'], $order['member'],
                $order['aid'], $order['visitors']);

            $javaApi      = new \Business\CommodityCenter\Ticket();
            $queryInfoArr = [];
            foreach ($tidArr as $key => $value) {
                $queryInfoArr[$key]['ticketId'] = $value;
                //$queryInfoArr[$key]['sid']      = $memberIdAndAid['aid'];
                //$queryInfoArr[$key]['fid']      = $memberIdAndAid['member'];
                //$queryInfoArr[$key]['channel']  = 1;
            }
            $ticketApiRes = [];
            $ticketInfo   = $javaApi->queryTicketInfoByQueryInfos($queryInfoArr);
            if ($ticketInfo) {
                foreach ($ticketInfo as $item) {
                    $ticketApiRes[] = $javaApi->fieldConversion($item);
                }
            }

            //$ticketApiRes   = $ticketApi->batchQueryTicketDetail($tidArr, $memberIdAndAid['member'], 1,
            //    $memberIdAndAid['aid']);
            $ticketRes = [];
            foreach ($ticketApiRes as $item) {
                $ticketRes[] = [
                    'id'          => $item['id'],
                    'batch_check' => $item['verify_limit_amount'],
                ];
            }

            // 凭证码信息
            $codeInfos = [];
            if ($ticketRes) {
                foreach ($ticketRes as $ticketInfo) {
                    if ($ticketInfo['batch_check'] == 2 || $ticketInfo['batch_check'] == 3) {

                        //票类凭证信息-一票一码 或者 一票一证
                        $tmpOrderArr = $tidOrderMatch[$ticketInfo['id']];

                        //查询凭证信息
                        $codeRes = $bizOrderQuery->getOrderCodeInfo($tmpOrderArr, $order['code']);
                        if ($codeRes['code'] == 200) {
                            foreach ($codeRes['data'] as $item) {
                                if (!in_array($item, $codeInfos)) {
                                    $codeInfos[$item['orderid']][] = $item;
                                }
                            }

                        }
                    }
                }
            }

            // 加入 一票一码 或 一票一证 的信息
            $totalmoney = 0;
            foreach ($order['tickets'] as &$tick) {

                // 订单总价计算 分
                $totalmoney += $tick['totalmoney'];

                // 单种票总价 元
                $tick['totalmoney'] = sprintf('%.2f', $tick['totalmoney'] / 100);

                $tmp = [];
                $tid = $tick['tid'];
                if (isset($tidOrderMatch[$tid]) && $codeInfos) {
                    $codeInfo         = $codeInfos[$tidOrderMatch[$tid][0]];
                    $tick['ordernum'] = $codeInfo[0]['orderid'];
                    foreach ($codeInfo as $cinfo) {
                        $tmp[] = [
                            'idcard'  => $cinfo['idcard'],
                            'checked' => $cinfo['checked'],
                            'code'    => $cinfo['code'],
                        ];
                    }
                }
                $tick['codes'] = $tmp;
            }

            // 整理前端需要的数据
            $lid                            = $order['lid'];     // 景区ID
            $lidOrders[$lid]['lid']         = $lid;              // 景区ID
            $lidOrders[$lid]['pos']         = $order['pos'];     // 景区坐标经纬度
            $lidOrders[$lid]['ltitle']      = $order['ltitle'];  // 景区名称
            $lidOrders[$lid]['orderlist'][] = [
                'lid'        => $order['lid'],
                'ltitle'     => $order['ltitle'],
                // 'ttitle'     => $order['ttitle'],
                // 'imgpath'    => $order['imgpath'],
                // 'tnum'       => $order['tnum'],
                'totalmoney' => sprintf('%.2f', $totalmoney / 100), // 订单总价：元
                'ordernum'   => $order['ordernum'],
                'ordertel'   => $order['ordertel'],
                'ordername'  => $order['ordername'],
                'status'     => $order['status'],
                'begintime'  => $order['begintime'],
                'ordertime'  => $order['ordertime'],
                'endtime'    => $order['endtime'],
                'code'       => $order['code'],
                'paystatus'  => $order['paystatus'],
                'cancel'     => $order['cancel'],
                'pay'        => $order['pay'],
                'tickets'    => $order['tickets'],
            ];
        }

        $this->apiReturn(200, $lidOrders);
    }

    /**
     * 获得二维码里数据
     * author  leafzl
     * Date: 2018-09-11
     */
    public function getQcodeData()
    {
        $virtualNo = I('virtual_no', '', 'strval');
        $sid       = I('sid', '', 'intval');

        if (!$virtualNo && !$sid) {
            $this->apiReturn(204, [], "参数错误");
        }
        $annual = new AnnualCard();
        $result = $annual->getQcodeData($virtualNo, $sid);

        if ($result['code'] == 200) {
            $this->apiReturn(200, $result['data'], '获取成功');
        } else {
            $this->apiReturn($result['code'], [], $result['msg']);
        }
    }

    /**
     * 获取我的年卡列表
     * <AUTHOR>
     * @date   2017-11-22
     */
    public function getMyAnnualCardList()
    {
        //登陆会员id
        $mid = $this->isLogin('ajax');
        //供应商id
        $sid = $this->_supplyId;
        $page = I('page', 1, 'intval');
        $pageSize = I('page_size', 10, 'intval');
        $page = $page <= 0 ? 1 : $page;
        $pageSize = $pageSize <= 0 ? 10 : $pageSize;
        //记录开始处理请求的时间戳
        $startMark = str_pad(str_replace('.', '', microtime(true)), 14, '0');

        //记录请求日志
        $requestLog = json_encode([
            'request_id' => $startMark,
            'mid'        => $mid,
            'sid'        => $sid,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('annual_card_request', $requestLog, 3);
        $vipBiz = new \Business\Mall\MemberVip($mid, $sid);
        $res = $vipBiz->getMyAnnualCardList($page, $pageSize);
        //记录请求日志
        $responseLog = json_encode([
            'request_id' => $startMark,
            'respons'    => $res,
        ], JSON_UNESCAPED_UNICODE);

        if (isset($res['code'])) {
            pft_log('annual_card_response', $responseLog, 1);
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            pft_log('annual_card_response', $responseLog, 2);
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 获取我的年卡详情
     * <AUTHOR>
     * @date   2017-11-22
     */
    public function getMyAnnualDetail()
    {

        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        //登陆会员id
        $mid = $this->isLogin('ajax');
        //供应商id
        $sid = $this->_supplyId;

        $vipBiz = new \Business\Mall\MemberVip($mid, $sid);

        $res = $vipBiz->getMyAnnualDetail($id);

        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }

    }

    /**
     * 获取年卡使用记录
     * <AUTHOR>
     * @date   2017-12-05
     */
    public function getAnnualHistory()
    {

        //年卡id
        $id = I('id', 0, 'intval');
        //当前页数
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        //登陆会员id
        $mid = $this->isLogin('ajax');
        //供应商id
        $sid = $this->_supplyId;

        $vipBiz = new \Business\Mall\MemberVip($mid, $sid);

        $res = $vipBiz->getAnnualHistory($id, $page, $size);

        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }

    }

    /**
     * 年卡续费
     * <AUTHOR>
     * @date   2017-11-24
     * @return [type]     [description]
     */
    public function annualRenewBefore()
    {

        if (I('code')) {

            $authBiz = new \Business\Wechat\Authorization();
            $auth    = $authBiz->parseAuthInfo(I('code'));
            if ($auth['code'] != 200) {
                exit("openid获取出错，请返回重试");
            }

            $_SESSION['pft_openid'] = $auth['data']['openid'];
            $params                 = json_decode(base64_decode(I('state')), true);
            $redirect               = str_replace('amp;', '', I('go_url'));
            //跳转到续费页面
            header("Location:{$redirect}");
            exit();
        } else {

            $id       = I('id', 0, 'intval');
            $sid      = I('sid', 0, 'intval');
            $orderNum = I('ordernum', 0);
            if (!$id || !$sid || !$orderNum) {
                $this->apiReturn(204, [], '参数缺失');
            }
            //获取openid后的跳转地址
            $domain   = str_replace(REQUEST_SCHEME . '://wx', $this->_supplyAccount, MOBILE_DOMAIN);
            $domain   = substr($domain, 0, -1);
            $redirect = $this->_getPageUrl($this->_supplyAccount, 'annual_renew');
            $redirect = $redirect . "?id={$id}&sid={$sid}&account={$this->_supplyAccount}&ordernum={$orderNum}&h={$domain}";
            $this->wechatAuth('Mall_Member', 'annualRenewBefore', 1, $redirect);
        }
    }

    /**
     * 特产购买前授权
     * <AUTHOR>
     * @date   2017-12-27
     */
    public function specialtyBuyBefore()
    {

        if (I('code')) {
            $authBiz = new \Business\Wechat\Authorization();
            $auth    = $authBiz->parseAuthInfo(I('code'));
            if ($auth['code'] != 200) {
                exit("openid获取出错，请返回重试");
            }

            $_SESSION['pft_openid'] = $auth['data']['openid'];
            $params                 = json_decode(base64_decode(I('state')), true);
            $redirect               = str_replace('amp;', '', I('go_url'));
            //跳转到续费页面
            header("Location:{$redirect}");
            exit();
        } else {

            $lid = I('lid', 0, 'intval');

            if (!$lid) {
                $this->apiReturn(204, [], '参数缺失');
            }
            //获取openid后的跳转地址
            $redirect = $this->_getPageUrl($this->_supplyAccount, 'speciality');

            $redirect = $redirect . "?lid={$lid}&account={$this->_supplyAccount}";

            $this->wechatAuth('Mall_Member', 'specialtyBuyBefore', 1, $redirect);
        }
    }

    /**
     * 获取年卡续费信息
     * <AUTHOR>
     * @date   2017-11-24
     */
    public function annualRenewInfo()
    {

        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数缺失');
        }

        //登陆会员id
        $mid = $this->isLogin('ajax');
        //供应商id
        $sid = $this->_supplyId;

        $vipBiz = new \Business\Mall\MemberVip($mid, $sid);

        $res = $vipBiz->annualRenewInfo($id);

        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }

    }

    /**
     * 年卡余额续费
     * <AUTHOR>
     * @date   2017-11-24
     */
    public function annualRenewByAccount()
    {

        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数缺失');
        }

        //登陆会员id
        $mid = $this->isLogin('ajax');
        //供应商id
        $sid = $this->_supplyId;

        $vipBiz = new \Business\Mall\MemberVip($mid, $sid);

        $res = $vipBiz->annualRenewByAccount($id);

        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 签到
     * <AUTHOR>
     * @date   2017-11-23
     */
    public function signIn()
    {

        //登陆会员id
        $mid = $this->isLogin('ajax');
        //供应商id
        $sid = $this->_supplyId;

        $vipBiz = new \Business\CreditsMall\Points($mid, $sid);
        $res    = $vipBiz->signIn();

        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 获取当前登陆用户信息
     * <AUTHOR>
     * @date   2017-12-21
     */
    public function getUserInfo()
    {

        $mid = $this->isLogin('ajax');

        //用户二期 - 信息获取修改 - 2
        $MemberBus  = new \Business\Member\Member();
        $memberInfo = $MemberBus->getInfo($mid);
        if (!$memberInfo) {
            $this->apiReturn(200, [], '用户信息获取失败');
        }
        $CustomerBus              = new \Business\Member\Customer();
        $customerInfo             = $CustomerBus->getCustomerInfoByMemberId($mid);
        $memberInfo["id_card_no"] = $customerInfo["id_card_no"];

        $mobile = $memberInfo['mobile'];
        if (!\ismobile($mobile)) {
            $mobile = '';
        }

        $userInfo = [
            'name'    => $memberInfo['dname'],
            'avatar'  => $memberInfo['headphoto'],
            'mobile'  => $mobile,
            'id_card' => $memberInfo["id_card_no"],
        ];

        $this->apiReturn(200, $userInfo);
    }

    /**
     * 绑定手机号
     * <AUTHOR>
     * @date   2017-12-18
     */
    public function bindMobile()
    {

        $mid = $this->isLogin('ajax');

        $mobile = I('mobile', 0, 'intval');

        if (!$mobile) {
            $this->apiReturn(204, [], '参数错误');
        }

        $data = [
            'mobile' => $mobile,
        ];

        $accountBiz = new AccountInfo();

        $result = $accountBiz->editUserInfo($mid, $data, []);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 编辑个人信息
     * <AUTHOR>
     * @date   2017-12-18
     */
    public function editUserInfo()
    {
        $mid       = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        //名称
        $name = I('name', '');
        //身份证
        $idCard = I('id_card', '');
        //头像
        $avatar = I('avatar', '');

        if (!$name && !$idCard && !$avatar) {
            $this->apiReturn(204, [], '参数错误');
        }

        $customerId = $loginInfo['customerId'];
        if (!$customerId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $data    = [
            'dname'     => $name,
            'headphoto' => $avatar,
        ];
        $extData = [
            'id_card_no' => $idCard,
        ];

        $accountBiz = new AccountInfo();
        $result     = $accountBiz->editUserInfo($mid, $data);
        //用户二期 - 信息获取修改
        $customerBus = new \Business\Member\Customer();
        $cusRes      = $customerBus->updateCustomerBaseInfo($customerId, $extData);

        if ($result['code'] == 200 && $cusRes['code'] == 200) {
            $allDisBiz = new AllDis();
            $allDisBiz->updateMemberInfo($mid, $name, $avatar);
            $cache         = new \Library\Tools\BusinessCache();
            $businessCache = $cache->getBusinessCache($mid);
            $cacheData     = [
                'dname'   => $name ?: $businessCache['dname'],
                'headImg' => $name ?: $businessCache['headImg'],
            ];

            $cache->setBusinessCache($cacheData, $mid);

            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }

    }

    /*
     * 特产订单确认收货
     * <AUTHOR>
     * @date   2017-12-15
     */
    public function confirmReceipt()
    {
        $mid = $this->isLogin('ajax');

        $ordernum = I('ordernum', 0, 'intval');

        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }

        $specialBiz = new Specialty();

        $result = $specialBiz->confirmReceipt($mid, $ordernum);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 微信账号登陆，没有则注册一个
     * <AUTHOR>
     * @date   2017-12-25
     */
    public function loginByWechat($redirect = '', $account = '')
    {

        $code = I('code', '');
        if ($code) {
            try {
                $params = json_decode(base64_decode(I('state')), true);
                if (I('go_url')) {
                    $params['redirect'] = str_replace('amp;', '', I('go_url'));
                }

                $registerData = ['code' => $code, 'appid' => $params['appid']];
                //注册
                $registerBiz = new \Business\Member\Register(false);
                $registerRes = $registerBiz->registerCommon('wx', $registerData);
                if ($registerRes['code'] != 200) {
                    throw new \Exception($registerRes['msg']);
                }

                $memberId = $registerRes['data']['mid'];
                $openid   = $registerRes['data']['openid'];

                //登录,写session
                $sessionBiz = new Session();
                $loginRes   = $sessionBiz->loginMemberId($memberId, 'wx');
                if ($loginRes['code'] !== 200) {
                    throw new \Exception('会话信息写入失败');
                }
                $_SESSION['openid'] = $openid;
                // //写入微信绑定信息
                $wxModel = new WxMember;
                $wxModel->bindWechat($memberId, $params['supplyId'], $openid, $params['appid']);

                if ($params['redirect']) {
                    header("Location:{$params['redirect']}");
                    exit();
                } else {
                    $this->_redirectToPage($params['supplyAccount'], $params['from']);
                }

            } catch (\Exception $e) {
                //跳到登陆页
                $this->_redirectToPage($params['supplyAccount'], 'login');
            }
        } else {
            $from = I('from', 'index');

            if (!$redirect && $from) {
                $redirect = $this->_getPageUrl($this->_supplyAccount, $from);
            }
            $authAccount = $account ?: $this->_supplyAccount;
            $this->wechatAuth('Mall_Member', 'loginByWechat', 0, $redirect, 'login', [], 'userinfo', $authAccount);
        }
    }

    //获取openid并跳转
    public function getOpenidAndRedirect($redirect = '')
    {

        if (I('code')) {
            $params   = json_decode(base64_decode(I('state')), true);
            $authBiz  = new Authorization($params['appid']);
            $auth     = $authBiz->parseAuthInfo(I('code'));
            $redirect = str_replace('amp;', '', I('go_url'));

            if ($auth['code'] == 200) {
                $_SESSION['openid'] = $auth['data']['openid'];
            }
            header("Location:{$redirect}");
            exit();
        } else {
            if (!$redirect) {
                $this->apiReturn(204, [], '参数缺失');
            }
            $this->wechatAuth('Mall_Member', 'getOpenidAndRedirect', 0, $redirect);
        }
    }

    /**
     * 统一下微信授权
     * <AUTHOR>
     *
     * @date   2018-01-11
     *
     * @param  string  $callC  回调的控制器名
     * @param  string  $callA  回调的方法名
     * @param  integer  $toPft  是否以票付通的公众号发起授权
     * @param  string  $redirect  跳转地址
     * @param  string     exceptionPage 出现异常跳到那个页面
     */
    public function wechatAuth($callC, $callA, $toPft = 1, $redirect = '', $exceptionPage = '', $extra = [], $type = 'base', $bindAccount = '')
    {
        try {

            if ($toPft) {
                $appid = self::__DEFAULT_APPID__;
            } else {
                //获取托管的公众号信息
                $wxOpenModel = new \Model\Wechat\WxOpen();
                if (!$bindAccount) {
                    $bindAccount = $this->_supplyAccount;
                }
                $wxInfo = $wxOpenModel->getWechatOffiAccInfo($bindAccount, 'account');

                $appid = $wxInfo['appid'];
                if (!$appid) {
                    throw new \Exception("公众号未托管");
                }
            }
            //回调时候要用的参数
            $params = [
                'appid'         => $appid,
                'supplyId'      => $this->_supplyId,
                'supplyAccount' => $this->_supplyAccount,
            ];
            // pft_log("allDis/login","params--------".print_r($params, true));

            if ($extra) {
                $params = array_merge($params, $extra);
            }

            $goUrl = '';
            if (isset($params['redirect']) && $params['redirect']) {
                $goUrl = $params['redirect'];
            }
            unset($params['redirect']);

            if ($redirect) {
                $tmpParse = parse_url($redirect);
                if (isset($tmpParse['fragment']) && $tmpParse['fragment']) {
                    $redirect = str_replace('#' . $tmpParse['fragment'], '', $redirect);
                }
                //加上标识避免死循环
                if (stripos($redirect, '?') !== false) {
                    $goUrl = $redirect . '&go=1';
                } else {
                    $goUrl = $redirect . '?go=1';
                }
                if (isset($tmpParse['fragment']) && $tmpParse['fragment']) {
                    $goUrl = $goUrl . '#' . $tmpParse['fragment'];
                }
            }

            //回调地址
            $callback = MOBILE_DOMAIN . "api/index.php?c={$callC}&a={$callA}";
            if ($goUrl) {
                $callback .= '&go_url=' . urlencode($goUrl);
            }
            // pft_log("allDis/login", "回调地址---".$callback);

            //发起授权
            $authBiz = new Authorization($appid);
            $authBiz->requestForAuth($callback, $params, $type);

        } catch (\Exception $e) {
            if ($bindAccount == '123624') {
                print_r($e->getMessage());
                exit;
            }
            if ($exceptionPage) {
                //跳转到指定页面
                $this->_redirectToPage($this->_supplyAccount, $exceptionPage);
            }
        }
    }

    /**
     * 发送给卖家取消订单的通知短信
     * <AUTHOR>
     * @date   2018-01-25
     *
     * @param $soap
     * @param $ordernum
     */
    public function sendCancelSms($soap, $orderInfo)
    {
        if ($orderInfo['sms_buyer'] != 0) {
            $smsContent = "您的" . $orderInfo['ltitle'] . $orderInfo['ttitle'] . "的订单" . $orderInfo['ordernum'] . "已被取消。";
            //$soap->Send_SMS_System_Fee((string)$orderInfo['ordertel'], $smsContent, (string)$orderInfo['aid'], (string)$orderInfo['ordernum']);

            $sendRes = $this->_commonSendSms($orderInfo['ordertel'], $smsContent, $orderInfo['aid'],
                $orderInfo['ordernum']);
            pft_log('sms/special/smscancel', ' buyer sms' . $smsContent . ' tel:' . $orderInfo['ordertel']);
        }

        //现场支付发送走这
        if ($orderInfo['sms_supplier'] != 0 && $orderInfo['pay_status'] == 0) {
            $smsContent = '您好，' . $orderInfo['memdname'] . '预定的' . $orderInfo['ordernum'] . '订单已取消，联系人：' .
                          $orderInfo['ordername'] . '，电话：' . $orderInfo['ordertel'] . '，预订产品：' .
                          $orderInfo['ltitle'] . $orderInfo['ttitle'] . '(' . $orderInfo['tnum'] . ')张';

            //$soap->Send_SMS_System_Fee((string)$orderInfo['ordertel'], $smsContent, (string)$orderInfo['aid'],
            //    (string)$orderInfo['ordernum']);

            $sendRes = $this->_commonSendSms($orderInfo['ordertel'], $smsContent, $orderInfo['aid'],
                $orderInfo['ordernum']);
            pft_log('sms/special/smscancel', 'apply sms:' . $smsContent);
        }
    }

    /**
     * 发送短信
     * <AUTHOR>
     * @date 2021/7/17
     *
     * @param  string  $mobile
     * @param  string  $smsContent
     * @param  int  $aid  收费用户ID
     * @param  string  $ordernum  订单号
     *
     * @return array
     */
    private function _commonSendSms($mobile, $smsContent, $aid, $ordernum)
    {
        $param = [
            'tel'            => strval($mobile),
            'msg'            => strval($smsContent),
            '$smsChargeInfo' => [
                'member_id' => $aid,
                'ordernum'  => $ordernum,
            ],
        ];

        $lib      = new \Library\JsonRpc\PftRpcClient('common_service');
        $queryRes = $lib->call('Order/Sms/commonSend', $param, 'special_cancel');

        return $queryRes;
    }

    /**
     * 获取常用联系人
     * Create by zhangyangzhen
     * Date: 2019/2/14
     * Time: 11:39
     */
    public function getLinkPerson()
    {
        $memberId = $this->isLogin();

        $person = ContactPerson::getContacts($memberId);

        $this->apiReturn(self::CODE_SUCCESS, $person);
    }

    /**
     * 新增/更新/删除常用联系人
     * Create by zhangyangzhen
     * Date: 2018/12/26
     * Time: 10:55
     *
     * @param  string oldname    旧姓名     -- 更新联系人使用
     * @param  string oldmobile  旧手机号  --  更新联系人使用
     * @param  string oldidcard  旧身份证号  -- 更新联系人使用
     * @param  string name       姓名
     * @param  string mobile     手机号
     * @param  string idcard     身份证
     * @param  string type       类型  create=新增，update=更新，delete删除
     */
    public function changeLinkPerson($name = '', $mobile = '', $idcard = '', $type = '')
    {
        $memberId = $this->isLogin();

        $oldname   = I('post.oldname', '', 'trim');
        $oldmobile = I('post.oldmobile', '', 'trim');
        $oldidcard = I('post.oldidcard', '', 'trim');
        $name      = I('post.name', '', 'trim');
        $mobile    = I('post.mobile', '', 'trim');
        $idcard    = I('post.idcard', '', 'trim');
        $type      = I('post.type', '', 'trim');

        if (empty($name) || empty($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '姓名和手机号不能为空');
        }

        if (!in_array($type, ['create', 'update', 'delete'])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $memberBiz = new \Business\Member\Member();
        $cinfos    = $memberBiz->getMemberExtInfoGetFieldToJava($memberId, 'cinfos');
        $linkmans  = $this->_parseLinkman($cinfos);

        $msg = '';
        switch ($type) {
            //新增联系人
            case 'create' :
                $msg     = '新增';
                $linkman = [
                    'name'   => $name,
                    'tel'    => $mobile,
                    'idcard' => $idcard,
                ];

                //判断常用联系人是否存在
                if ($linkmans && in_array($linkman, $linkmans)) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '联系人已存在');
                }

                // 限制常用联系人数量
                if (count($linkmans) >= 12) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '常用联系人数量已达上限');
                }

                // 增加
                if (!$cinfos) {
                    $data['cinfos'] = implode(':', $linkman);
                } else {
                    $data['cinfos'] = $cinfos . '|' . implode(':', $linkman);
                }
                break;

            //更新联系人
            case 'update' :
                $msg = '更新';
                //更新联系人需要传旧的联系人信息
                if (empty($oldname) || empty($oldmobile)) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '请求参数错误');
                }

                $linkman = [
                    'name'   => $oldname,
                    'tel'    => $oldmobile,
                    'idcard' => $oldidcard,
                ];

                if (!$linkmans || !in_array($linkman, $linkmans)) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '联系人不存在');
                }

                foreach ($linkmans as $key => $val) {
                    if ($val == $linkman) {
                        $linkmans[$key]['name']   = $name;
                        $linkmans[$key]['tel']    = $mobile;
                        $linkmans[$key]['idcard'] = $idcard;
                    }

                    $linkmans[$key] = implode(':', $linkmans[$key]);
                }

                $data['cinfos'] = implode('|', $linkmans);
                break;

            //删除联系人
            case 'delete' :
                $msg     = '删除';
                $linkman = [
                    'name'   => $name,
                    'tel'    => $mobile,
                    'idcard' => $idcard,
                ];

                if (!$linkmans || !in_array($linkman, $linkmans)) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '联系人不存在');
                }

                foreach ($linkmans as $key => $val) {
                    if ($val == $linkman) {
                        unset($linkmans[$key]);
                    } else {
                        $linkmans[$key] = implode(':', $val);
                    }
                }

                $data['cinfos'] = implode('|', $linkmans);
                break;
        }

        $memberBiz = new \Business\Member\Member();
        $res       = $memberBiz->updateMemberExtInfo($memberId, $data);
        if (!$res) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '常用联系人' . $msg . '失败');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, [], '常用联系人' . $msg . '成功');
        }
    }

    /**
     * 解析常用联系人字段
     * Create by zhangyangzhen
     * Date: 2018/12/25
     * Time: 18:06
     *
     * @param  string  $linkmanField  数据库中联系人字段
     * 例如 ；'赵大:***********:|钱二:***********:35010519770402473X'
     *
     * @return array 解析后的数组
     * 例如 ；[['name'=>'赵大','tel'=>***********,'idcard'=>''],....]
     */
    private function _parseLinkman($linkmanField = '')
    {
        if (!$linkmanField || !is_string($linkmanField)) {
            return [];
        }

        $linkmanArr = explode('|', $linkmanField);
        foreach ($linkmanArr as $key => $linkman) {
            [$name, $tel, $idcard] = explode(':', $linkman);
            $info[$key]['name']   = $name;
            $info[$key]['tel']    = $tel;
            $info[$key]['idcard'] = $idcard;
        }

        return $info ?: [];
    }

    public function loginForBindAnnual($redirect = '')
    {
        $code = I('get.code', '');
        if ($code) {
            try {
                $params = json_decode(base64_decode(I('state')), true);
                if (I('go_url')) {
                    $params['redirect'] = str_replace('amp;', '', I('go_url'));
                }
                $redirectType = isset($params['type']) ? $params['type'] : 0;
                $authBiz      = new Authorization(I('get.appid'));
                $auth         = $authBiz->parseAuthInfo($code);
                if ($auth['code'] != 200) {
                    throw new \Exception("授权失败");
                }
                $_SESSION['openid'] = $auth['data']['openid'];
                $_SESSION['appid']  = $params['appid'];
                $wxMdl              = new  WxMember();
                $checkBindWx        = $wxMdl->GetWxInfoByOpenid($auth['data']['openid'], $params['appid']);
                $isBind             = false;
                if ($checkBindWx && in_array($redirectType, [0, 1, 2, 3])) {
                    switch ($redirectType) {
                        case 0:
                            $redirectType = 3;
                            break;
                    }
                    $isBind = true;
                }
                $fid = empty($checkBindWx) ? 0 : $checkBindWx['fid'];
                $this->loginForBindAnnualRedirect($redirectType, $params['supplyAccount'], $fid, $isBind);
            } catch (\Exception $e) {
                //跳到登陆页
                $this->_redirectToPage($params['supplyAccount'], 'login');
            }
        } else {
            $extra       = [
                'type' => I('type', 0),
            ];
            $authAccount = $this->_supplyAccount;
            $this->wechatAuth('Mall_Member', 'loginForBindAnnual', 0, $redirect = '', 'login', $extra, 'userinfo',
                $authAccount);
        }
    }

    private function loginForBindAnnualRedirect($type, $supplyId, $fid, $isBind = false)
    {
        $from = 'login';
        switch ($type) {
            case 0:     //绑定年卡页面
                $from = 'bindAnnual';
                break;
            case 1:     //购买年卡页面
                $from = 'index';
                break;
            case 2:     //激活年卡页面
                if ($isBind) {
                    $from = 'activity_annual';
                } else {
                    $from = 'bindAnnual';
                }
                break;
            case 3:    //我的年卡页面
                if ($isBind) {
                    $from = 'myCard';
                } else {
                    $from = 'bindAnnual';
                }
                break;
        }
        if ($isBind) {
            if (!$fid) {
                $from = 'login';
            } else {
                $sessionBiz = new Session();
                $loginRes   = $sessionBiz->loginMemberId($fid, 'wx');
                if ($loginRes['code'] !== 200) {
                    $from = 'login';
                }
            }
        }
        $this->_redirectToPage($supplyId, $from);
    }

    /**
     * 公众号年卡绑定根据手机号判断是否有买年卡
     * Create by zhangyangzhen
     * Date: 2018/12/25
     * Time: 18:06
     *
     * @param  string  $linkmanField  数据库中联系人字段
     * 例如 ；'赵大:***********:|钱二:***********:35010519770402473X'
     *
     * @return array 解析后的数组
     * 例如 ；[['name'=>'赵大','tel'=>***********,'idcard'=>''],....]
     */
    public function getBindAnnualByPhone()
    {
        $phone    = I('post.phone', 0);
        $isMobile = \Library\Tools::ismobile($phone);
        if (!$isMobile) {
            $this->apiReturn(204, '', '手机号错误');
        }
        $wechatBindAnnualBiz = new WechatBindAnnual();
        $result              = $wechatBindAnnualBiz->getUserInfoToAnnualByPhone($phone, $this->_supplyId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    public function wechatBindAnnual()
    {
        $phone  = I('post.phone', 0);
        $idCard = I('post.id_card', '');
        $code   = I('post.code', 0);
        $openId = I('session.openid', '');
        //  $openId = I('post.openid','');
        $appId = I('session.appid', '');
        //  $appId  = I('post.appid','');
        if (strlen($idCard) != 6) {
            $this->apiReturn(204, [], '身份证输入有误');
        }
        if (!$openId || !$appId) {
            $this->apiReturn(204, [], 'openid缺失');
        }
        $redis = Cache::getInstance('redis');
        $num   = $redis->get('bind' . $phone);
        if (!$num) {
            $num = 1;
        } else {
            if ($num > 3) {
                $checkCode = CaptchaCode::compare($code);
                if (!$checkCode) {
                    $this->apiReturn(204, ['is_code' => 1, 'error_type' => 'code'], '验证码错误');
                }
            }
        }
        $wechatBindAnnualBiz = new WechatBindAnnual();
        $result              = $wechatBindAnnualBiz->checkUserInfoByCard($phone, $this->_supplyId, $idCard, $openId,
            $appId, $num);
        if (isset($result['code'])) {
            if ($num >= 3) {
                $result['data']['is_code'] = 1;
            } else {
                $result['data']['is_code'] = 0;
            }
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 获取图形验证码
     *
     * @param  string  $account  用户账号唯一标识
     * Create by linchen
     * Date: 2019/1/15
     * Time: 18:30
     */
    public function getImgCodeByAnnualBind()
    {
        $str = CaptchaCode::generate();//随机生成的字符串

        $width  = 60;    //验证码图片的宽度
        $height = 25;    //验证码图片的高度

        //Date in the past
        header("Expires:Mon,26 Jul 1997 05:00:00 GMT");
        //always modified
        header("Last-Modified:" . gmdate("D,d M Y H:i:s") . "GMT");
        //HTTP/1.1
        header("Cache-Control:no-store,no-cache,must-revalidate");
        header("Cache-Control:post-check=0,pre-check=0", false);
        //HTTP/1.0
        header("Pragma:no-cache");
        header("Content-Type:image/png");
        $im   = imagecreate($width, $height);
        $back = imagecolorallocate($im, 0xFF, 0xFF, 0xFF);    //背景色
        //$pix=imagecolorallocate($im,187,190,247);        //模糊点颜色
        $font = imagecolorallocate($im, 41, 163, 238);        //字体色
        //绘制1000个模糊作用的点
        //mt_srand();
        //for($i=0;$i<1000;$i++) {
        //    imagesetpixel($im,mt_rand(0,$width),mt_rand(0,$height),$pix);
        //}
        imagestring($im, 5, 7, 5, $str, $font);//绘制随机生成的字符串
        //imagerectangle($im,0,0,$width-1,$height-1,$font);//在验证码图像周围绘制1px的边框
        imagepng($im);//建立一张PNG格式图形
        imagedestroy($im);//释于内存空间将图片handle解构，
    }

    /**
     * 获取一码通码
     * Create by linchen
     * Date: 2019/04/18
     * Time: 18:30
     */
    public function getDynamicCode()
    {
        $memberId = $this->isLogin();
        $biz      = new DynamicCodePass();
        $result   = $biz->encode($memberId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 一码通消息通知
     * Create by linchen
     * Date: 2019/04/18
     * Time: 18:30
     */
    public function validationMessage()
    {
        $memberId = $this->isLogin();
        $biz      = new DynamicCodePass();
        $result   = $biz->getCacheData($memberId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 模拟回调数据
     * Create by linchen
     * Date: 2019/04/18
     * Time: 18:30
     */
    public function simulationData()
    {
        $memberId = I('post.mid');
        $code     = I('post.code');
        $biz      = new DynamicCodePass();
        $result   = $biz->setCacheData($memberId, $code);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取开票订单列表数据   请求的页面地址 http://123624.12301.cc/wx/c/invoice/list.html
     *
     * @return [type] [description]
     */
    public function getInvoiceOrderList()
    {
        $memberId = $this->isLogin('ajax');

        $pageSize    = I('pageSize', 10, 'intval');
        $page        = I('page', 1, 'intval');
        $type        = I('type') ?: 'history';
        $beginDate   = I('beginDate', '');
        $endDate     = I('endDate', '');
        $dateType    = I('dateType', 'play');
        $invoiceType = I('invoice_type', 1, 'intval');  //开票状态  0 未开票 1 已开票

        if (!in_array($type, ['unuse', 'history', 'unpay', 'all'])) {
            $this->apiReturn(204, [], '查询类型错误');
        }

        $Order  = new OrderTools('slave');
        $Land   = new Land('slave');
        $Ticket = new Ticket('slave');
        $Group  = new GroupBooking();

        $midArr = $this->_getALlMemberId();
        if (count($midArr) > 10) {
            $midArr = [$memberId];
        }

        if ($invoiceType) {
            $type = 'all';
        }

        //获取订单列表
        //$option      = $this->_getOrderList($midArr, $page, $pageSize, $type, $beginDate, $endDate, $dateType, true);
        $option = $this->_getNewOption($beginDate, $endDate, $page, $pageSize);

        $invoiceBiz  = new \Business\ElectronicInvoices\Invoice();
        $orderResult = $invoiceBiz->getInvoiceOrderList($memberId, $option, $invoiceType);
        if ($orderResult['code'] != 200) {
            return $this->apiReturn($orderResult['code'], [], $orderResult['msg']);
        }
        $total  = $orderResult['total'];
        $orders = $orderResult['data'];

        $list = [];
        if ($orders) {
            $lidArr       = array_column($orders, 'lid');
            $tidArr       = array_column($orders, 'tid');
            $orderArr     = array_column($orders, 'ordernum');
            $aidArr       = array_column($orders, 'aid');
            $faceModel    = new \Model\Terminal\FaceCompare();
            $platformData = $faceModel->getFacePlatforms(0, $lidArr);
            $platform     = [];
            foreach ($platformData as $val) {
                $platform[$val['lid']] = $val['face_platform'];
            }
            //景区信息
            $javaAPi      = new \Business\CommodityCenter\Land();
            $landRes      = $javaAPi->queryLandMultiQueryById($lidArr);
            $landsMapping = array_column($landRes, null, 'id');
            //门票信息
            $ticketsMapping = $Ticket->getMuchTicketInfo($tidArr, 'id,title');
            //拼团订单做特殊处理
            $groupOrder = $Group->parseTBCOrders($orderArr);

            //检测订单是否需要开票
            $invoiceApi           = new \Business\ElectronicInvoices\InvoiceApi();
            $orderInvoiceCheckRes = $invoiceApi->checkInvoiceAuth($orderArr, $this->_supplyId);
            if ($orderInvoiceCheckRes['code'] != 200) {
                $this->apiReturn(204, [], $orderInvoiceCheckRes['data']['msg']);
            }
            $orderInvoiceConf = $orderInvoiceCheckRes['data'];

            //获取联票信息
            //$links = $Order->getOrderConcatId($orderArr);
            $queryParams = [$orderArr, 'orderid', 'concat_id'];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderDetail', 'queryOrderDetailsByOrdernums',
                $queryParams);
            $links       = [];
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $links = $queryRes['data'];
            }

            foreach ($orders as $k => $order) {

                //订单号对应的景区信息
                $landInfo = $landsMapping[$order['lid']];

                $link = [];
                if ($links[$order['ordernum']]) {
                    if ($links[$order['ordernum']] != $order['ordernum']) {
                        continue;
                    } else {
                        $field      = 'ss.tnum,ss.tid,ss.code';
                        $linkOrders = $Order->getLinkOrdersInfo($order['ordernum'], $field);
                        //显示联票的子票
                        foreach ($linkOrders as &$val) {
                            $val['title'] = $ticketsMapping[$val['tid']];
                        }

                        $link = $linkOrders;
                    }
                } else {
                    $link[] = [
                        'tnum'  => $order['tnum'],
                        'tid'   => $order['tid'],
                        'title' => $ticketsMapping[$order['tid']],
                    ];
                }

                $orderModeArr = load_config('source', 'orderSearch');

                $tmp = [
                    'ltitle'   => $landInfo['title'],
                    'imgpath'  => $landInfo['imgpath'],
                    'ttitle'   => $ticketsMapping[$order['tid']],
                    'ordernum' => $order['ordernum'],
                    'tickets'  => $link,
                    'ptype'    => $landInfo['p_type'],
                ];

                $orderQueryModel = new OrderQuery();
                $orderPrice      = $orderQueryModel->get_order_total_fee($order['ordernum']);

                //开票信息
                $tmp['invoice']     = $orderInvoiceConf[$order['ordernum']]['print_invoice'];
                $tmp['invoice_msg'] = $orderInvoiceConf[$order['ordernum']]['msg'];
                $tmp['sid']         = $orderInvoiceConf[$order['ordernum']]['sid'];
                if (isset($orderInvoiceConf[$order['ordernum']]['image_url'])) {
                    $tmp['invoice_url'] = $orderInvoiceConf[$order['ordernum']]['image_url'];
                } else {
                    $tmp['invoice_url'] = '';
                }

                $tmp['ordertime'] = $order['ordertime'];
//                if ($type == 'unuse') {
                $tmp['tid']        = $order['tid'];
                $tmp['lid']        = $order['lid'];
                $tmp['pid']        = $order['pid'];
                $tmp['status']     = $order['status'];
                $tmp['pid']        = $order['pid'];
                $tmp['begintime']  = $order['begintime'];
                $tmp['endtime']    = $order['endtime'];
                $tmp['ordername']  = $order['ordername'];
                $tmp['ordertel']   = $order['ordertel'];
                $tmp['code']       = $order['code'];
                $tmp['tnum']       = $order['tnum'];
                $tmp['paystatus']  = $order['pay_status'];
                $tmp['cancel']     = OrderList::isAllowCancel($order['ordernum'], $order['member']);
                $tmp['pay']        = intval($order['status'] == 0 && $order['pay_status'] == 2);
                $tmp['verifytime'] = $order['dtime'];
                $tmp['ordermode']  = $orderModeArr[$order['ordermode']];
                $tmp['orderprice'] = $orderPrice;
                if ($this->inUnionApp()) {
                    $tmp['aid'] = $order['aid'];
                }
                //已支付的获取订单状态(退票中，部分验证)
                if ($order['pay_status'] == 1) {
                    $tmp['status'] = $this->_parseOrderStatus($order['ordernum'], $tmp['status']);
                }
                if (in_array($order['ordernum'], $groupOrder)) {
                    $tmp['status'] = 102;//待确认状态
                }
                $tmp['is_face'] = isset($platform[$tmp['lid']]) ? 1 : 0;
//                } else {
//                    $tmp['verifytime'] = $order['dtime'];
//                    $tmp['status']     = $order['status'];
//                }

                if ($landInfo['p_type'] == 'J') {
                    //特产
                    $tmp = $this->_fillOrderInfoForSpecial($tmp);
                }

                $list[] = $tmp;
            }
        }
        if ($this->inUnionApp()) {
            $productBiz = new \Business\Mall\Product();
            $list       = $productBiz->getOrderAccount($list);
        }
        $return = [
            'total'     => $total,
            'list'      => $list,
            'page'      => $page,
            'totalPage' => ceil($total / $pageSize),
        ];

        $this->apiReturn(200, $return);
    }

    private function _getNewOption($beginDate, $endDate, $page, $pageSize)
    {
        $option = [];
        if ($this->inUnionApp()) {
            $option['ordermode'] = [self::__UNION_ORDER_MODE__];
        } else {
            $option['ordermode'] = [self::__ORDER_MODE__, self::__APP_ORDER_MODE__, self::__UNION_ORDER_MODE__, 33, 34];
        }

        $option['begin_time'] = $beginDate;
        $option['end_time']   = $endDate;
        $option['status']     = ['in', [1, 2, 3, 4, 5, 6]];
        $option['page']       = $page;
        $option['page_size']  = $pageSize;

        return $option;
    }

    /**
     * 短信页面无需登录获取最早可预约时间
     *
     */
    public function getEarlyCanReservationDateToMember()
    {
        $ordernum = I('get.ordernum', '', 'strval');
        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }
        //订单号解码
        $ordernum = $this->_ordernumDecode($ordernum);
        $ordernum = strval($ordernum);
        $result   = (new ReservationOrder())->getEarlyCanReservationDateService($ordernum);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 短信页面改签
     *
     */
    public function smsTicketChanging()
    {
        $ordernum        = I('post.order_num', '', 'strval');
        $sectionTimeId   = I('post.section_time_id', 0, 'intval');
        $sectionTimeStr  = I('post.section_time_str', '');
        $reservationDate = I('post.reservation_time', '');
        if (!$ordernum || !$reservationDate) {
            $this->apiReturn(204, [], '参数错误');
        }
        //订单号解码
        $ordernum  = $this->_ordernumDecode($ordernum);
        $modifyBiz = new Modify();
        $source    = 50;
        $result    = $modifyBiz->ticketChanging($ordernum, $reservationDate, 0, 0, $source, $sectionTimeId,
            $sectionTimeStr, true);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 短信页面预约
     *
     */
    public function smsOrderReservation()
    {
        $ordernum        = I('post.order_num', '', 'strval');
        $sectionTimeId   = I('post.section_time_id', 0, 'intval');
        $sectionTimeStr  = I('post.section_time_str', '');
        $reservationDate = I('post.reservation_time', '');
        if (!$ordernum || !$reservationDate) {
            $this->apiReturn(204, [], '参数错误');
        }
        //订单号解码
        $source              = 50;
        $ordernum            = $this->_ordernumDecode($ordernum);
        $orderReservationBiz = new ReservationOrder();
        $result              = $orderReservationBiz->orderReservationService($ordernum, 0, 0, $reservationDate, $source,
            $sectionTimeId, $sectionTimeStr, true);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 获取单天的预约库存或者普通库存
     *
     * <AUTHOR>
     * @date   2020-06-13
     *
     * @return
     */
    public function getOrderThisDayStorage()
    {
        $orderNum = I('get.order_num', '');
        $playDate = I('get.play_date', '');
        $supportCustomTime = I('get.support_custom_time', false, 'boolval');
        if (!$orderNum || !$playDate) {
            $this->apiReturn(204, [], '参数错误');
        }
        $ordernum          = $this->_ordernumDecode($orderNum);
        $productStorageBiz = new ProductStorage();
        $res               = $productStorageBiz->getOrderStorageByReservationOrNormalService($ordernum, $playDate,
            $playDate, ['unifiedStorage' => true, 'support_custom_time' => $supportCustomTime]);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 获取订单普通库存和预约库存列表（分时预约库存最大）
     *
     * <AUTHOR>
     * @date   2020-06-10
     *
     */
    public function getOrderStorageByReservationOrNormalToSms()
    {
        $orderNum = I('get.order_num', '');
        $month    = I('get.month', '');
        if (!$month || !$orderNum) {
            $this->apiReturn(204, [], '参数错误');
        }
        $startDate         = date('Y-m-d', strtotime($month . '-01'));
        $endDate           = date('Y-m-d', strtotime('+31 days', strtotime($month . '-01')));
        $ordernum          = $this->_ordernumDecode($orderNum);
        $productStorageBiz = new ProductStorage();
        $res               = $productStorageBiz->getOrderStorageByReservationOrNormalService($ordernum, $startDate,
            $endDate);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 检测邀请码是否过期
     * <AUTHOR> Li
     * @date   2020-08-12
     */
    public function checkInviteCode()
    {
        $sid        = I('post.sid', 0, 'intval');
        $inviteCode = I('post.invite_code');

        if (!$sid || !$inviteCode) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        //先获取出供应商最新的码
        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->getInviteCode($sid);

        if (!isset($result['code']) || $result['code'] != 200) {
            $this->apiReturn(500, $result['data'], $result['msg']);
        }

        $memberBiz  = new \Business\Member\Member();
        $memberInfo = $memberBiz->getInfo($sid, true);

        $codeStatus = 1;
        //判断码是否过期  没过期则返回供应商名称信息
        if ($inviteCode != $result['data']['inviteCode']) {
            $codeStatus = 0;
        }

        $this->apiReturn(200, ['code_status' => $codeStatus, 'dname' => $memberInfo['dname']]);
    }

    /**
     * 手机短信验证码登录
     * <AUTHOR> Li
     * @date   2020-08-11
     *
     * @param  int  $mobile  手机号
     * @param  string  $vcode  短信验证码
     */
    public function loginByVcode()
    {
        $mobile = I('post.mobile', '', 'strval');
        $vcode  = I('post.vcode', '', 'trim');
        $sid    = I('post.sid', 0, 'intval');

        if (!$sid) {
            $this->apiReturn(203, [], '缺少供应商信息');
        }

        if (!$vcode || !$mobile) {
            $this->apiReturn(203, [], '缺少验证信息');
        }

        //判断是否存在账号 存在返回对应分销商信息  不存在则注册用户
        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->loginVcode($sid, $mobile, $vcode);
        if ($result['code'] == 200) {
            $this->apiReturn(200, $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 邀请码审核
     * <AUTHOR> Li
     * @date   2020-08-11
     *
     * @param  int  $mobile  手机号
     * @param  string  $vcode  短信验证码
     */
    public function applyCreateMemberRelationByInviteCode()
    {
        $sid        = I('post.sid', 0, 'intval');        //供应商id
        $fid        = I('post.son_id', 0, 'intval');     //分销商id
        $inviteCode = I('post.invite_code', '', 'trim'); //邀请码
        $remark     = I('post.remark', '', 'trim');
        if (mb_strlen($remark) > 50) {
            $this->apiReturn(203, [], '备注字符过长，不得超过50字符');
        }
        //需要审核发起邀请码审核 否则直接建立分销关系
        $memberRelationBiz = new \Business\Member\MemberRelation();
        $memberRelationBiz->setOperator($sid, '');
        $addRes = $memberRelationBiz->addDistributorByInviteCode($sid, $fid, $fid, $inviteCode, 0, $remark, 'H5邀请');

        if ($addRes['code'] == 200) {
            $this->apiReturn(200, $addRes['data'], $addRes['msg']);
        } else {
            $this->apiReturn($addRes['code'], $addRes['data'], $addRes['msg']);
        }
    }

    /**
     * 通过或拒绝审核
     * <AUTHOR> Li
     * @date  2020-08-04
     */
    public function checkCreateMemberRelationBatch()
    {
        $auditIds = I('post.audit_ids'); // 审核id 多个以逗号隔开
        $status   = I('post.status', 0, 'intval');   // 审核状态 1 拒绝 2通过
        $memberId = I('post.fid');  //分销商id

        if (empty($auditIds) || empty($status)) {
            $this->apiReturn(400, [], '缺少审核信息');
        }

        if ($status == 2) {
            $pass = true;
        } else {
            $pass = false;
        }

        $auditIdArr = explode(',', $auditIds);

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->checkCreateMemberRelationBatch($auditIdArr, $memberId, $pass);

        if (isset($result['code']) && $result['code'] == 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], $result['msg']);
    }

    /**
     * 通过id mobile 获取审核详情
     * <AUTHOR> Li
     * @date   2020-08-14
     *
     * @return array
     */
    public function getDistributionCheck()
    {
        $id = I('post.id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数有误');
        }

        $memberRelationBiz = new \Business\Member\DistributorAudit();
        $result            = $memberRelationBiz->getDistributionCheck($id, $this->_sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 再次发送邀请
     * <AUTHOR> Li
     * @date   2020-08-14
     */
    public function reSendInviteSms()
    {
        $id = I('post.id');
        if (!$id) {
            $this->apiReturn(204, '', '参数错误');
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->reSendInviteSms($id, $this->_sid, $this->_loginInfo['sdname'],
            $this->_memberId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }
    /**
     * 获取订单的使用详情
     * <AUTHOR>
     * @date   2021-11-15
     */
    public function getOrderUsedDetail(){
        $orderNum = I('get.ordernum','');
        $page     = I('get.page',1);
        $size     = I('get.size',10);
        if (!$orderNum){
            $this->apiReturn(204, '', '订单号缺失');
        }
        $orderTouristBiz = new OrderTourist();
        $result = $orderTouristBiz->getOrderUsedDetailService($orderNum,$page,$size);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }
}
