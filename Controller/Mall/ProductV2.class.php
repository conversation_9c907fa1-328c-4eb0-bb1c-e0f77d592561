<?php
/**
 * 微商城产品展示相关接口
 * <AUTHOR>
 */

namespace Controller\Mall;

use Business\JavaApi\TicketApi;
use Business\Order\MergeOrder;
use Business\Order\OrderBook;
use Business\Product\Show as ShowBiz;
use Business\Product\Specialty;
use Library\Cache\Cache;
use Library\Constants\ThemeConst;
use Model\Mall\AllDis as AllDisModel;
use Model\Mall\MemberSmallAppConfig;
use Model\Product\AnnualCard;
use Model\Product\Area;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\Subdomain\SubdomainInfo;
use Process\Mall\AllDis as AllDisProcess;
use Process\Product\ProductList;
use Process\Product\Settle\GroupByTopicOrPtypeSettle;
use Process\Product\Settle\MallProductSettle;
use Process\Resource\AreaFilter\AreaPinyinSettle;
use Process\Resource\AreaProcess;

class ProductV2 extends Mall
{
    //首页返回数据条数
    protected $_indexShow = 10;

    //允许售卖的产品类型
    protected $_allowType = ['A', 'B', 'C', 'F', 'H', 'G', 'I', 'J'];

    //上次获取到的最后一个景区在数组中的索引，用于分页
    private $_lastPos = 0;

    /**
     * 微商城首页
     * @return [type] [description]
     */
    public function index()
    {

        $hotList = $this->_getHotList(I('keyword', ''));

        $return = [
            'list' => $hotList,
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 首页推荐产品
     *
     * @param  string  $keyword  关键字/城市
     *
     * @return array
     */
    private function _getHotList($keyword = '')
    {

        $option['where'] = [
            'l.p_type' => ['in', $this->_allowType],
        ];

        if ($keyword) {
            $areas = (new \Model\Product\Area())->getAreaList();
            if ($city = array_search($keyword, $areas)) {
                $option['where']['l.area'] = ['like', "%|{$city}|%"];
            } else {
                $option['where']['l.title'] = ['like', "%{$keyword}%"];
            }
        }

        $products = $this->_getProductSet($option);

        $pidArr = $lands = [];
        foreach ($products as $item) {
            $pidArr[]                 = $item['pid'];
            $lands[$item['landid']][] = $item;
        }

        //整合前端需要的数据
        $list = $this->_productDeal($lands, $pidArr, $this->_indexShow);

        if ($this->_allDisMan) {
            $list = AllDisProcess::getAllDisPrice($this->_supplyId, $list, 'index');
        }

        return $list;
    }

    /**
     * 微商城产品列表页
     */
    public function productList()
    {
        //产品类型
        $ptype = I('type', 'all');
        //上次查询的位置
        $lastPos = I('lastPos', 0, 'intval');
        //当前页数
        $page = I('page', 1, 'intval');
        //每页条数
        $pageSize = I('pageSize', 10, 'intval');
        //产品关键字
        $keyword = I('keyword', '');
        //主题
        $topic = I('topic', '');
        //城市代码
        $city = I('city', 0, 'intval');
        //小程序扫码
        $wxAppScenCode = I('post.scenCode', 0);

        $supplyAccounts = I('post.supplyIds', '');
        if ($supplyAccounts) {
            $supplyIds = $this->getSupplyIdsByAccounts(explode(',', $supplyAccounts));
            $this->setSupplyIds($supplyIds);
        }

        if ($topic == -1) {
            $topic       = '';
            $productList = $this->_getProductList($ptype, $lastPos, $pageSize, $keyword, $topic, $city);
            GroupByTopicOrPtypeSettle::clearUp($productList);
        } else {
            $productList = $this->_getProductList($ptype, $lastPos, $pageSize, $keyword, $topic, $city);
            if ($this->getSupplyIds()) {
                $idMapName = ProductList::getIdMapName($this->getArrSupplyIds());
                MallProductSettle::extra($productList, $idMapName);
            }
        }

        $return = [
            'list'    => $productList,
            'lastPos' => $this->_lastPos,
        ];
        //第一页的请求,前端需要缓存一些信息
        if ($lastPos == 0) {
            $return['citys']  = $this->getAreaList(true);
            $return['themes'] = $this->getThemes();
            $return['type']   = $this->getTypeList(true);

            if ($wxAppScenCode) {
                $subModel           = new SubdomainInfo();
                $field              = 'M_name as name,M_banner as img,M_tel as tel,longitude,latitude';
                $info               = $subModel->getBindedSubdomainInfo($this->_supplyId, 'id', $field);
                $return['shopInfo'] = $info;
            }
        }
        // 加入轮播图(小程序用到)
        if ($this->inWechatSmallApp()) {
            $memberSmallAppConfig = new MemberSmallAppConfig();
            $configItem           = $memberSmallAppConfig->getConfigByMemberId($this->_supplyId);
            if ($configItem->id) {
                $smallAppConfig = $configItem->getImages('banner');
                foreach ($smallAppConfig as $value) {
                    $banner[] = $value['path'];
                }
                $return['shopInfo']['imgUrls']   = $banner;
                $return['shopInfo']['name']      = $configItem->store_name;
                $return['shopInfo']['longitude'] = $configItem->longitude;
                $return['shopInfo']['latitude']  = $configItem->latitude;
                $return['shopInfo']['tel']       = $configItem->service_mobile;
            } else {
                $config = $this->getCustomConfig(true);

                $banner = [];
                foreach ($config['banner'] as $item) {
                    $banner[] = key($item);
                }

                $return['shopInfo']['imgUrls'] = $banner;
                $return['shopInfo']['name']    = $config['name'];
            }
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 获取产品列表页的景区信息
     *
     * @param  string  $ptype  产品类型
     * @param  int  $lastPos  上一次搜索位置
     * @param  int  $pageSize  每页条数
     * @param  string  $keyword  关键字
     * @param  string  $topic  主题
     * @param  string  $city  城市code
     * @param  string  $isTopicNN  topic不为空
     *
     * @return [type]
     */
    private function _getProductList($ptype, $lastPos, $pageSize, $keyword, $topic, $city)
    {

        $option = [];
        if ($ptype != 'all') {
            $option['where'] = ['l.p_type' => $ptype];
        }

        if ($keyword) {
            $option['where']['l.title'] = ['like', "%{$keyword}%"];
        }

        //城市筛选
        if ($city) {
            if (in_array($city, [1, 2, 3, 4])) {
                $option['where']['l.area'] = ['like', "{$city}|%"];
            } else {
                $option['where']['l.area'] = ['like', "%|{$city}|%"];
            }
        }

        //主题筛选
        if ($topic) {
            $option['where']['_string'] = "find_in_set('{$topic}', l.topic)";
        }

        $products = $this->_getProductSet($option);
        $pidArr   = $lands = [];
        foreach ($products as $item) {
            $pidArr[]                 = $item['pid'];
            $lands[$item['landid']][] = $item;
        }

        $this->_lastPos = $lastPos;

        //整合前端需要的数据
        $list = $this->_productDeal($lands, $pidArr, $pageSize);

        if ($this->_allDisMan && $list) {
            //全民分销价格优惠
            $list = AllDisProcess::getAllDisPrice($this->_supplyId, $list, 'list');
            if ($this->_promoter) {
                //计算推广佣金
                $list = AllDisProcess::calRecommendCommission($this->_supplyId, $list);
            }
        }

        return $list;
    }

    public function getAreaList($result = false)
    {
        $area     = new Area();
        $landInfo = AreaProcess::getAreaByMember($area, $this->_supplyId);
        AreaPinyinSettle::clearUp($landInfo);
        $landInfo = array_reverse($landInfo);
        $result   = [];
        foreach ($landInfo as $key => $value) {
            $result = array_merge($value, $result);
        }
        if ($result) {
            return $result;
        }
        $this->apiReturn(200, $result);
    }

    /**
     * 详情页-获取景区信息
     */
    public function getLandInfo()
    {
        $lid = I('lid', 0, 'intval');
        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $redis    = Cache::getInstance('redis');
        $cacheKey = "newLandInfo:$lid";
        $landInfo = $redis->hget($cacheKey);

        if (empty($landInfo)) {
            $field = 'id,p_type,title,area,address,jtzn,jqts,bhjq,imgpath,imgpathGrp,apply_did,lng_lat_pos,tel, venus_id as venue_id';

            $landModel = new Land('slave');
            $landInfo  = $landModel->getLandInfo($lid, false, $field);
            unset($landModel);

            if ($landInfo['imgpathGrp']) {
            } else {
                $landInfo['imgpathGrp'] = [];
            }
            $landInfo['venue_id'] = $landInfo['venus_id'];
            $landInfo['jqts']     = nl2br($landInfo['jqts']);
            $landInfo['jtzn']     = nl2br($landInfo['jtzn']);
            $landInfo['bhjq']     = htmlspecialchars_decode($landInfo['bhjq']);
            $landInfo['bhjq']     = \image_opt($landInfo['bhjq'], 600);

            if (strpos($landInfo['lng_lat_pos'], ',') !== false) {
                [$longitude, $latitude] = explode(',', $landInfo['lng_lat_pos']);
                $landInfo['latitude']  = $latitude;
                $landInfo['longitude'] = $longitude;
            } else {
                //没有的话，定位到北京天安门
                $landInfo['latitude']  = 39.915119;
                $landInfo['longitude'] = 116.403963;
            }

            $redis->hset($cacheKey, '', $landInfo, 1800);
        }

        if ($landInfo['imgpathGrp']) {
            $tmpImgpathGrp          = @json_decode($landInfo['imgpathGrp'], true);
            $imgpathGrp             = is_array($tmpImgpathGrp) ? $tmpImgpathGrp : unserialize($landInfo['imgpathGrp']);
            $landInfo['imgpathGrp'] = $imgpathGrp;
        }

        //如果是场次类产品，添加额外的场次数据
        if ($landInfo['p_type'] == 'H') {
            $showBiz = new ShowBiz();
            $tmpInfo = $showBiz->getLastedRoundList($landInfo['venue_id']);
            $code    = $tmpInfo['code'];

            if ($code == 1) {
                //有获取到数据
                $data = $tmpInfo['data'];

                $landInfo['venue_img']       = $data['venue_img'];
                $landInfo['show_start_date'] = $data['lasted_date'];
                $landInfo['show_round_list'] = $data['round_list'];

            } else {
                //获取场次数据错误
                pft_log('order_show/error', json_encode(['getLandInfo', $landInfo['venue_id'], $tmpInfo]));

                $landInfo['venue_img']       = '';
                $landInfo['show_start_date'] = '';
                $landInfo['show_round_list'] = [];
            }
        }

        $this->apiReturn(200, $landInfo);
    }

    /**
     * 详情页-获取景区下的门票列表
     */
    public function getTicketList()
    {
        header("Access-Control-Allow-Origin: *"); // 允许任意域名发起的跨域请求
        //景区id
        $lid = I('lid', 0, 'intval');
        //上级供应商id
        $aid = I('aid', 0, 'intval');

        //页面上面选定的日期
        $date = I('date', '', 'strval');
        $date = strtotime($date) ? date('Y-m-d', strtotime($date)) : date('Y-m-d');

        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $productList = $this->_getLandTickets($lid);
        $pidArr      = array_column($productList, 'pid');

        $ptype = (new \Model\Product\Ticket())->getProductType($pidArr[0]);

        //零售价获取
        $priceArr = $this->getMuchRetailPrice($pidArr);
        $tags     = $this->_parseTicketsTags($productList);

        //是否根据上级id过滤
        $aidFilter = false;
        if ($aid) {
            $applySidArr = array_column($productList, 'apply_sid');
            if (in_array($aid, $applySidArr)) {
                $aidFilter = true;
            }
        }

        $ticketIds = array_column($productList, 'tid');
        $ticketIds = implode(',', $ticketIds);
        $priceData = TicketApi::getSinglePrices($ticketIds, $date);

        $list = [];
        foreach ($productList as $item) {

            if (!isset($priceArr[$item['pid']])) {
                continue;
            }
            if ($aidFilter && $item['apply_sid'] != $aid) {
                continue;
            }

            if (!isset($priceData[$item['tid']])) {
                $tPrice = $item['tprice'];
            } else {
                $tPrice = $priceData[$item['tid']]['counter_price'] / 100;
            }

            if (\inWechatSmallApp()) {
                $jsPrice = $priceData[$item['tid']]['window_price'] / 100;
            } else {
                $jsPrice = $priceArr[$item['pid']];
            }

            $tmp = [
                'ticket'  => $item['ttitle'],
                'pid'     => $item['pid'],
                'tid'     => $item['tid'],
                'px'      => $item['px'],
                'aid'     => $item['apply_sid'],
                'jsprice' => $jsPrice,
                'tprice'  => $tPrice,
                'tags'    => $tags[$item['pid']] ?: '',
                'intro'   => explode('<br />', nl2br($item['notes'])),
            ];

            $list[] = $tmp;
        }

        if ($list) {
            $list = $this->_fillExtraForTicketList($list, $ptype);
        }

        if ($this->_allDisMan) {
            $list = AllDisProcess::getAllDisPrice($this->_supplyId, $list, 'list');
            $list = AllDisProcess::calRecommendCommission($this->_supplyId, $list);
        }
        //微信抢购活动
        $list = $this->_seckillDecorate($list);

        $this->apiReturn(200, ['type' => $ptype, 'list' => $list]);
    }

    /**
     * 获取门票包含的年卡特权
     * <AUTHOR>
     * @date   2017-11-20
     */
    public function getAnnualPrivilege()
    {

        $pid = I('pid', 0, 'intval');

        if (!$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $annualBiz  = new \Business\Product\AnnualCard();
        $privileges = $annualBiz->getPrivileges($pid);

        if ($privileges) {
            foreach ($privileges as &$item) {
                if ($item['use_limit'] == '-1') {
                    $item['use_limit'] = '-1,-1,-1';
                }
            }
        }

        $this->apiReturn(200, $privileges);
    }

    /**
     * 门票列表根据产品类型填充额外信息
     * <AUTHOR>
     * @date   2017-11-20
     *
     * @param  array  $list  门票列表数据
     * @param  string  $type  产品类型
     *
     * @return arrray
     */
    private function _fillExtraForTicketList($list, $type)
    {

        switch ($type) {

            case 'F':
                //套票
                $list = $this->_fillExtraForTicketListByF($list, $type);
                break;

            case 'H':
                $list = $this->_fillExtraForTicketListByH($list, $type);
                break;

            case 'I':
                $list = $this->_fillExtraForTicketListByI($list, $type);
                break;

            case 'default':
                break;
        }

        return $list;
    }

    /**
     * 门票列表填充套票信息
     * <AUTHOR>
     * @date   2017-11-20
     *
     * @param  array  $list  门票列表数据
     * @param  string  $type  产品类型
     *
     * @return arrray
     */
    private function _fillExtraForTicketListByF($list, $type)
    {

        //套票的话需要返回子票信息
        $list = $this->_parseSonTickets($list);

        return $list;

    }

    /**
     * 门票列表填充演出信息
     * <AUTHOR>
     * @date   2017-11-20
     *
     * @param  array  $list  门票列表数据
     * @param  string  $type  产品类型
     *
     * @return arrray
     */
    private function _fillExtraForTicketListByH($list, $type)
    {
        $tidArr      = array_column($list, 'tid');
        $zoneMapping = (new \Model\Product\Ticket())->getZoneName($tidArr);

        foreach ($list as &$item) {
            $tid = $item['tid'];

            if (isset($zoneMapping[$tid])) {
                $item['zone_name'] = $zoneMapping[$tid]['zone_name'];
                $item['zone_id']   = $zoneMapping[$tid]['id'];
            } else {
                $item['zone_name'] = '';
                $item['zone_id']   = 0;
            }
        }

        return $list;
    }

    /**
     * 门票列表填充年卡信息
     * <AUTHOR>
     * @date   2017-11-20
     *
     * @param  array  $list  门票列表数据
     * @param  string  $type  产品类型
     *
     * @return arrray
     */
    private function _fillExtraForTicketListByI($list, $type)
    {

        //年卡(虚拟卡库存)
        $pidArr = array_column($list, 'pid');

        $annualModel = new AnnualCard('slave');

        $stgMap = $annualModel->getAnnualCardStorage($this->_supplyId, $pidArr);

        foreach ($list as &$item) {
            if (isset($stgMap[$item['pid']])) {
                $item['storage'] = $stgMap[$item['pid']];
            } else {
                $item['storage'] = 0;
            }
        }

        return $list;
    }

    /**
     * 是否有抢购活动
     * <AUTHOR>
     * @date   2017-07-19
     *
     * @param  array  $list  门票列表信息
     *
     * @return array
     */
    private function _seckillDecorate($list)
    {

        if (!$list) {
            return $list;
        }

        $pidArr = array_column($list, 'pid');

        $seckillBiz = new \Business\Wechat\Seckill();
        //获取哪些票类有抢购
        $result = $seckillBiz->getRecentSeckillForPid($pidArr);

        if (!$result['data']) {
            return $list;
        }

        $seckills = $result['data'];

        foreach ($list as &$item) {
            if (isset($seckills[$item['pid']])) {

                $seckill = $seckills[$item['pid']];

                if ($seckill['sid'] == $this->_supplyId) {

                    if ($seckill['aid'] != $item['aid']) {
                        continue;
                    }

                    $cycleRes = $seckillBiz->parseCycle($seckill['id']);

                    if ($cycleRes['code'] == 200) {
                        $seckill['begin'] = $cycleRes['data']['begin'];
                        $seckill['end']   = $cycleRes['data']['end'];
                        $item['seckill']  = $seckill;
                    }

                    $item['seckill'] = $seckill;
                }
            }
        }

        return $list;
    }

    /**
     * 获取抢购活动的详情信息
     * <AUTHOR>
     * @date   2017-07-19
     */
    public function getSeckillDetail()
    {

        $seckillId = I('seckill_id', 0, 'intval');
        $aid       = I('aid', 0, 'intval');

        if (!$seckillId || !$aid) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $seckillBiz = new \Business\Wechat\Seckill();
        //获取抢购信息
        $field  = 'id,pid,begin,end,price';
        $secRes = $seckillBiz->getSeckill($seckillId, 0, $field);

        if ($secRes['code'] != 200 || !$secRes['data']) {
            $this->apiReturn(204, [], '抢购活动不存在');
        }

        $tickModel = new Ticket('slave');
        $landModel = new Land('slave');

        $seckill = $secRes['data'];

        //周期性抢购
        $cycleRes = $seckillBiz->parseCycle($seckillId);

        if ($cycleRes['code'] != 200) {
            $this->apiReturn(204, [], '抢购周期解析错误');
        }

        $seckill['begin'] = $cycleRes['data']['begin'];
        $seckill['end']   = $cycleRes['data']['end'];

        //是否抢完
        //是否售罄
        $getRes = $seckillBiz->getRealtimeSeckillInfo($seckillId, $seckill['begin'], $seckill['end']);

        if ($getRes['code'] != 200) {
            $this->apiReturn(204, [], '抢购活动不存在');
        }

        $realtimeInfo = $getRes['data'];

        //获取周期key
        $cycleKey = $seckillBiz->generateCycleKey($seckill['begin'], $seckill['end']);

        if ($realtimeInfo[$cycleKey] == 0) {
            $seckill['sell_out'] = 1;
        } else {
            $seckill['sell_out'] = 0;
        }

        $pid = $seckill['pid'];

        //获取零售价(分)
        $retail = $seckill['price'];
        //获取门市价(元)
        $field = 'landid,tprice,order_start,order_end,delaydays,delaydays,use_early_days';
        $tInfo = $tickModel->getTicketInfoByPid($pid, $field);

        $market = $tInfo['tprice'];
        //获取可使用时间
        $validTime = '';
        if (strtotime($tInfo['order_start']) > 0) {
            //时间段内有效
            $tmpS = date('Y-m-d', strtotime($tInfo['order_start']));
            $tmpE = date('Y-m-d', strtotime($tInfo['order_end']));

            $validTime = $tmpS . '~' . $tmpE . '有效';
        } elseif ($tInfo['delaydays'] && !$tInfo['use_early_days']) {
            //多少天内有效
            $validTime = "下单后{$tInfo['delaydays']}天内有效";
        } elseif ($tInfo['delaydays'] && $tInfo['use_early_days']) {
            $validTime = "游玩日期前(含){$tInfo['use_early_days']}天有效,后{$tInfo['delaydays']}天有效";
        } else {
            //当天有效
            $validTime = '游玩日期当天有效';
        }

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByProductIds([$pid], 'title,id,pid', 'id', 'title,imgpath,id');
        $nameMap   = [];
        foreach ($ticketArr as $ticketInfo) {
            $nameMap[$ticketInfo['product']['id']] = [
                'id'      => $ticketInfo['product']['id'],
                'ttitle'  => $ticketInfo['ticket']['title'],
                'tid'     => $ticketInfo['ticket']['id'],
                'pid'     => $ticketInfo['ticket']['pid'],
                'title'   => $ticketInfo['land']['title'],
                'imgpath' => $ticketInfo['land']['imgpath'],
                'landid'  => $ticketInfo['land']['id'],
            ];
        }

        //获取景区id
        $lid = $tInfo['landid'];
        //获取景点信息
        $field            = 'p_type,jtzn,jqts,bhjq,imgpath';
        $landInfo         = $landModel->getLandInfo($lid, false, $field);
        $landInfo['jqts'] = nl2br($landInfo['jqts']);
        $landInfo['jtzn'] = nl2br($landInfo['jtzn']);
        $landInfo['bhjq'] = htmlspecialchars_decode($landInfo['bhjq']);
        $landInfo['bhjq'] = \image_opt($landInfo['bhjq'], 600);

        //是否设置过微信提醒
        $warning = 0;
        if (I('session.openid')) {
            $openid = I('session.openid');
            if (isset($_SESSION['identify'])) {
                //全民营销,获取供应商openid
                $allDisModel = new AllDisModel();
                $memberInfo  = $allDisModel->getAllDisMemberInfo(I('session.memberID'));
                if ($memberInfo && $memberInfo['supply_openid']) {
                    $openid = $memberInfo['supply_openid'];
                }
            }
            $warnRes = $seckillBiz->getOneWarning($seckillId, $openid, 'status');
            //设置过
            if ($warnRes['code'] == 200 && $warnRes['data']) {
                $warning = 1;
            }
        }

        $return = [
            'set_warning'  => $warning,
            'land_info'    => $landInfo,
            'seckill_info' => $seckill,
            'retail'       => $retail,
            'market'       => $market * 100,
            'valid_time'   => $validTime,
            'title'        => $nameMap[$pid]['title'],
            'ttitle'       => $nameMap[$pid]['ttitle'],
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 解析票类的一些标签属性
     *
     * @param  [type] $tickets 门票列表
     *
     * @return [type]          [description]
     */
    private function _parseTicketsTags($tickets)
    {
        $tags = [];

        foreach ($tickets as $item) {
            if ($item['ddays'] > 0) {
                $tags[$item['pid']][] = "提前{$item['ddays']}天";
            }

            if ($item['pay'] == 0) {
                $tags[$item['pid']][] = '现场支付';
            }

            //退票规则：添加了这个值 -1=不可退且是可提现
            if ($item['refund_rule'] == 2 || $item['refund_rule'] == -1) {
                $tags[$item['pid']][] = '不可退';
            }
        }

        return $tags;
    }

    /**
     * 解析子票信息
     *
     * @param  array  $tickets  门票列表
     *
     * @return array
     */
    private function _parseSonTickets($tickets)
    {
        $packBiz = new \Business\Product\PackTicket();

        foreach ($tickets as $key => $item) {
            $sonTickets                  = $packBiz->getSonTickets($item['tid']);
            $tickets[$key]['sonTickets'] = $sonTickets;
        }

        return $tickets;
    }

    /**
     * 解析年卡是否需要填写身份证
     * <AUTHOR>
     * @date   2017-12-01
     *
     * @param  array  $list  门票列表数据
     *
     * @return array
     */
    private function _parseCertLimit($list)
    {
        $javaApi = new \Business\CommodityCenter\Ticket();
        foreach ($list as &$item) {
            $tidAttr = $javaApi->queryTicketAttrsById($item['tid']);
            $needNo  = 1;
            foreach ($tidAttr as $ext) {
                if ($ext['key'] == 'annual_identity_info') {
                    $needNo = $ext['val'];
                }
            }
            $item['need_id'] = $needNo;
        }

        return $list;
    }

    /**
     * 详情页-相关套票
     */
    public function getRelatedPackage()
    {
        $lid = I('lid', '', 'intval');

        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $tidArr = array_column($this->_getLandTickets($lid), 'tid');

        $packBiz   = new \Business\Product\PackTicket();
        $parentTid = $packBiz->getParentsByTid($tidArr);

        $option['where'] = [
            't.id' => ['in', implode(',', $parentTid)],
        ];

        $packages = $this->_getProductSet($option);
        $TmpArr   = array_column($packages, 'pid');

        //零售价获取
        $priceArr = $this->getMuchRetailPrice($TmpArr);

        $return = [];
        foreach ($packages as $item) {

            if (!isset($priceArr[$item['pid']])) {
                continue;
            }

            $return[] = [
                'ticket'  => $item['p_name'],
                'pid'     => $item['pid'],
                'aid'     => $item['apply_sid'],
                'jsprice' => $priceArr[$item['pid']],
                'tprice'  => $item['tprice'],
            ];

        }

        $this->apiReturn(200, $return);
    }

    /**
     * 获取景区门票列表
     *
     * @param  int  $lid  景区id
     *
     * @return [type]      [description]
     */
    private function _getLandTickets($lid)
    {

        $option['where'] = [
            'l.id' => $lid,
        ];

        $productList = $this->_getProductSet($option);

        return $productList ?: [];

    }

    /**
     * 预订页面-景区以及相关订单信息
     */
    public function getBookInfo()
    {

        //获取预定须知的类型
        $type = I('type', 'common', 'strval');

        switch ($type) {
            case 'common':
                //共性产品
                $this->_getBookInfoForCommon();
                break;

            case 'seckill':
                //抢购
                $this->getBookInfoForSeckill();
                break;

            case 'special':
                //特产产品
                $this->_getBookInfoForSpecial();
                break;

            default:
                $this->apiReturn(204, [], '预定类型错误');
                break;
        }
    }

    /**
     *  共性产品的预定信息(景区，线路，酒店等等)
     * <AUTHOR>
     * @date   2017-12-08
     */
    private function _getBookInfoForCommon()
    {
        $pid               = I('pid', '', 'intval');
        $aid               = I('aid', '', 'intval');
        $this->saleChannel = I('shop', 1, 'intval');

        //页面上面选定的日期
        $date = I('date', '', 'strval');
        $date = strtotime($date) ? date('Y-m-d', strtotime($date)) : false;

        if ($pid < 1 || $aid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $bookInfo = $this->_getBookInfo($pid, $aid);
        $tickets  = $this->_getBookList($pid, $aid, $bookInfo['startDate']);
        if (!$tickets) {
            $this->apiReturn(204, [], '票类不存在');
        }

        $swap = [];
        foreach ($tickets as $value) {
            if ($value['aid'] != $aid) {
                continue;
            }

            //套票的有效期文本单独处理
            if ($bookInfo['p_type'] == 'F' && !empty($value['sonTickets'])) {
                $pidArr            = array_column($value['sonTickets'], 'pid');
                $value['validTag'] = OrderBook::parsePackValidTag($pidArr);
            } else {
                $value['validTag'] = OrderBook::getValidityDate($value);
            }

            $value['refundTag']    = OrderBook::handleRefundRule($value['refund_audit'], $value['refund_rule'],
                $value['refund_early_time'], $value['refund_after_time'] ?? 0);
            $swap[$value['tid']][] = $value;
        }

        $ticketSelect = [];
        foreach ($swap as $key => $value) {
            $jpriceLarge = 0;
            foreach ($value as $ticketItem) {
                if ($ticketItem['jsprice'] < $jpriceLarge || $jpriceLarge == 0) {
                    $jpriceLarge        = $ticketItem['jsprice'];
                    $ticketSelect[$key] = $ticketItem;
                }
            }
        }

        $tickets = array_values($ticketSelect);
        if (!empty($tickets)) {
            foreach ($tickets as $key => $value) {
                if ($value['pid'] == $pid) {
                    $mainTicket    = $value;
                    $mainTicketKey = $key;
                    break;
                }
            }
            unset($tickets[$mainTicketKey]);
            array_unshift($tickets, $mainTicket);
        }

        $bookInfo['tickets'] = array_values($tickets);

        //获取价格的日期
        $startDate = $date ? $date : date('Y-m-d', strtotime($bookInfo['startDate']));

        $ticketIds = array_column($bookInfo['tickets'], 'tid');

        $javaApi = new \Business\CommodityCenter\LandF();
        $needIds = $javaApi->queryLandFByLandIdAndTicketIdAndProductId($ticketIds, [], [], 'tid,tourist_info', true);
        //$mergeOrder = new MergeOrder();
        //$needIds    = $mergeOrder->getLandFNeedIDByTids($ticketIds);

        $ticketIds = implode(',', $ticketIds);
        $priceData = TicketApi::getSinglePrices($ticketIds, $startDate);
        foreach ($bookInfo['tickets'] as $key => $val) {
            if (!isset($priceData[$val['tid']])) {
                $bookInfo['tickets'][$key]['needID'] = $needIds[$val['tid']];
                continue;
            }
            $bookInfo['tickets'][$key]['tprice']       = $priceData[$val['tid']]['counter_price'] / 100;
            $bookInfo['tickets'][$key]['payprice']     = $bookInfo['tickets'][$key]['jsprice'];
            $bookInfo['tickets'][$key]['jsprice']      = $priceData[$val['tid']]['window_price'] / 100;
            $bookInfo['tickets'][$key]['retail_price'] = $priceData[$val['tid']]['retail_price'] / 100;
            $bookInfo['tickets'][$key]['needID']       = $needIds[$val['tid']];
            $bookInfo['tickets'][$key]['paymode']      = $val['paymode'];
        }
        if ($bookInfo['refund_rule'] == 4 && !empty($bookInfo['tickets'])) {
            $bookInfo['validTime'] = array_column($bookInfo['tickets'], 'validTag');
        }

        if (!$_SESSION['memberID'] || $_SESSION['identify'] == 'allDis') {
            $bookInfo['alldis'] = 1;
        } else {
            $bookInfo['alldis'] = 0;
        }

        $this->apiReturn(200, $bookInfo);
    }

    /**
     * 抢购预定页面接口
     * <AUTHOR>
     * @date   2017-10-31
     */
    public function getBookInfoForSeckill()
    {

        $pid = I('pid', '', 'intval');
        $aid = I('aid', '', 'intval');

        if ($pid < 1 || $aid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $redisObj = Cache::getInstance('redis');
        $cacheKey = "seckill:{$pid}:bookinfo";

        //抢购业务接口
        $seckillBiz = new \Business\Wechat\Seckill();

        $cacheData = $redisObj->get($cacheKey);

        if (!$cacheData) {
            //当前票类是否存在抢购活动
            $getRes = $seckillBiz->getRecentSeckillForPid([$pid]);

            if ($getRes['code'] != 200) {
                $this->apiReturn(204, [], '抢购活动获取失败');
            }
            if (!isset($getRes['data'][$pid])) {
                $this->apiReturn(202, [], '未获取到抢购活动信息');
            }
            $seckill = $getRes['data'][$pid];
            //获取最近周期的开始和结束时间
            $cycleRes = $seckillBiz->parseCycle($seckill['id']);

            if ($cycleRes['code'] != 200) {
                $this->apiReturn(204, [], '抢购周期解析失败');
            }

            $cycle = $cycleRes['data'];
            //预定信息
            $bookInfo = $this->_getBookInfo($pid, $aid);

            if (date('Y-m-d', $cycle['begin']) > $bookInfo['startDate']) {
                $bookInfo['startDate'] = date('Y-m-d', $cycle['begin']);
            }
            //票类列表
            $tickets = $this->_getBookList($pid, $aid, $bookInfo['startDate']);
            if (!$tickets) {
                $this->apiReturn(204, [], '票类不存在');
            }
            //抢购不能下联票
            foreach ($tickets as $key => $item) {
                if ($item['pid'] != $pid) {
                    unset($tickets[$key]);
                }
            }

            $bookInfo['tickets'] = array_values($tickets);
            $seckillInfo         = [
                'id'        => $seckill['id'],
                'begin'     => $cycle['begin'],
                'end'       => $cycle['end'],
                'price'     => $seckill['price'],
                'buy_limit' => (int)$seckill['buy_limit'],
            ];

            $bookInfo['seckill_info'] = $seckillInfo;
            $redisObj->set($cacheKey, json_encode($bookInfo), '', 3600);
        } else {
            $bookInfo = json_decode($cacheData, true);
        }

        $seckill = $bookInfo['seckill_info'];
        //实时抢购信息
        $getRes = $seckillBiz->getRealtimeSeckillInfo($seckill['id'], $seckill['begin'], $seckill['end']);
        if ($getRes['code'] != 200) {
            $this->apiReturn(204, [], '抢购活动不存在');
        }

        $realtimeInfo = $getRes['data'];
        //获取周期key
        $cycleKey                            = $seckillBiz->generateCycleKey($seckill['begin'], $seckill['end']);
        $bookInfo['seckill_info']['storage'] = $realtimeInfo[$cycleKey];
        $this->apiReturn(200, $bookInfo);

    }

    /**
     * 特产产品获取预定信息
     * <AUTHOR>
     * @date   2017-12-08
     */
    private function _getBookInfoForSpecial()
    {

        $lid = I('lid', 0, 'intval');

        if (!$lid) {
            $this->apiReturn(204, [], '参数错误');
        }

        //获取产品信息
        $landModel = new Land('slave');

        $landInfo = $landModel->getLandInfo($lid, false, 'p_type,bhjq,title,area,imgpath,imgpathGrp');

        $landInfo['bhjq'] = htmlspecialchars_decode($landInfo['bhjq']);
        $landInfo['bhjq'] = \image_opt($landInfo['bhjq'], 600);

        if ($landInfo['p_type'] != 'J') {
            $this->apiReturn(204, [], '产品类型错误');
        }

        $return = [];
        //获取规格(门票)信息
        $tickets = $this->_getLandTickets($lid);
        if ($tickets) {
            $ticModel = new Ticket('slave');
            $pidArr   = array_column($tickets, 'pid');
            //获取零售价
            $retailMap = $ticModel->getMuchRetailPrice($pidArr);
            $minPrice  = $maxPrice = 0;

            if ($retailMap) {
                //零售价格范围
                $minPrice = min($retailMap) * 100;
                $maxPrice = max($retailMap) * 100;
            }

            //获取门市价
            $tidArr = array_column($tickets, 'tid');

            $ticketServiceApi = new \Business\JavaApi\Product\Ticket();
            $ticketParamList  = [];
            foreach ($tickets as $key => $value) {
                if (empty($value['superior_id'])) {
                    $this->apiReturn(203, [], "aid不能为空");
                }
                $ticketParamList[] = [
                    'ticketId' => $value['id'],
                    'sid'      => $value['superior_id'],
                    'fid'      => $this->_supplyId,
                    'channel'  => $this->saleChannel,
                ];
            }
            $tpriceArr = $ticketServiceApi->batchQueryTicketByIds($ticketParamList);
            if ($tpriceArr['code'] != 200) {
                $this->apiReturn($tpriceArr['code'], [], "获取门票信息失败");
            }
            $tpriceArr = $tpriceArr['data'];

            // $tpriceArr = $ticModel->getTicketInfoMulti($tidArr, 'id,tprice');

            $orginMinPrice = $orginMaxPrice = 0;

            if ($tpriceArr) {
                //门市价范围
                $tpriceAll     = array_column($tpriceArr, 'tprice');
                $orginMinPrice = min($tpriceAll) * 100;
                $orginMaxPrice = max($tpriceAll) * 100;
            }

            //轮播图
            if ($landInfo['imgpathGrp']) {
                $tmpImgpathGrp = @json_decode($landInfo['imgpathGrp'], true);
                $imggroup      = is_array($tmpImgpathGrp) ? $tmpImgpathGrp : unserialize($landInfo['imgpathGrp']);
            } else {
                $imggroup = [$landInfo['imgpath']];
            }

            $return['title']         = $landInfo['title'];
            $return['imgpath']       = $landInfo['imgpath'];
            $return['imggroup']      = $imggroup;
            $return['bhjq']          = $landInfo['bhjq'];
            $return['area']          = $landInfo['area'];
            $return['max_price']     = $maxPrice;
            $return['min_price']     = $minPrice;
            $return['ori_max_price'] = $orginMaxPrice;
            $return['ori_min_price'] = $orginMinPrice;

            //获取库存
            $storageMap = $ticModel->getMuchStorage($pidArr);

            //获取特产产品规格信息
            $specialBiz = new Specialty();
            $sizeRes    = $specialBiz->getSpecification($tidArr, $this->_supplyId);

            if ($sizeRes['code'] != 200) {
                $this->apiReturn(204, [], $sizeRes['msg']);
            }

            if (!$sizeRes['data']) {
                $this->apiReturn(204, [], '未获取到产品规格信息');
            }

            $sizes = $sizeRes['data'];

            $tags = [];
            $imgs = [];
            foreach ($sizes as $item) {
                foreach ($item['spec_params'] as $val) {

                    if (isset($tags[$val['tag']])) {
                        if (!in_array($val['value'], $tags[$val['tag']])) {
                            $tags[$val['tag']][] = $val['value'];
                        }
                    } else {
                        $tags[$val['tag']][] = $val['value'];
                    }
                    //规格图片
                    if (isset($val['img'])) {
                        $imgs[$val['value']] = $val['img'];
                    }
                }
            }

            $return['tags'] = $tags;
            $return['imgs'] = $imgs;

            foreach ($tickets as $item) {

                $tmp = $sizes[$item['tid']];

                $express = $tmp['express_pay_way'];
                $store   = $storageMap[$item['pid']] >= -1 ? $storageMap[$item['pid']] : 0;

                if (isset($retailMap[$item['pid']])) {
                    $price = $retailMap[$item['pid']] * 100;
                } else {
                    $price = 0;
                }

                $return['list'][] = [
                    'tid'             => $item['tid'],
                    'pid'             => $item['pid'],
                    'title'           => $item['ttitle'],
                    'express'         => $express,
                    'store'           => $store,
                    'tags'            => $tmp['spec_params'],
                    'price'           => $price,
                    'ori_price'       => $tpriceArr[$item['tid']]['tprice'] * 100,
                    'checked_express' => $tmp['checked_express'],
                    'special_dot'     => $tmp['pick_up_point'],
                ];
            }
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 预定页面-门票列表
     * @param  int  $pid  产品id
     * @param  int  $aid  上级供应商id
     * @param  string  $date  可预订日期
     *
     * @return array
     */
    private function _getBookList($pid, $aid, $date)
    {
        $Model = new Ticket();

        //门票类型
        $ptype   = $Model->getProductType($pid);
        $product = $Model->getTicketInfoByPid($pid, 'id,landid,pay');
        $tickets = $this->_getLinkProduct($product['id'], $product['landid'], $ptype, $date, $product['pay']);
        if (!$tickets) {
            return [];
        }

        if ($this->_allDisMan) {
            $tickets = AllDisProcess::getAllDisPrice($this->_supplyId, $tickets, 'book');
        }

        if ($ptype == 'F') {
            //获取子票列表
            $tickets = $this->_parseSonTickets($tickets);
        }

        if ($ptype == 'I') {
            //解析是否需要身份证
            $tickets = $this->_parseCertLimit($tickets);
        }

        if ($ptype == 'H') {
            //获取演出区域id
            $tickets = $this->_getZoneId($tickets);

            $tidArr      = array_column($tickets, 'tid');
            $zoneMapping = (new \Model\Product\Ticket())->getZoneName($tidArr);

            foreach ($tickets as &$item) {
                $item['zone_name'] = $zoneMapping[$item['tid']]['zone_name'];
                $item['zone_id']   = $zoneMapping[$item['tid']]['id'];
            }
        }

        return $tickets;
    }

    /**
     * 景区以及相关订单信息
     *
     * @param  [type] $pid 产品id
     *
     * @return [type]      [description]
     */
    private function _getBookInfo($pid, $aid)
    {
        $Land = new Land('slave');

        $Ticket        = new \Business\JavaApi\Product\Ticket();
        $ticketInfoRes = $Ticket->queryTicketInfoByPid($pid);
        if ($ticketInfoRes['code'] != 200) {
            $this->apiReturn(205, [], "获取门票信息出错");
        }

        $product = $ticketInfoRes['data']['uuJqTicketDTO'];
        $extInfo = $ticketInfoRes['data']['confs'];
        $product = array_merge($extInfo, $product);
        $product = $Ticket->mapAttribute($product);
        //阶梯退票手续费
        $cancelCost = json_decode($product['cancel_cost'], true) ?: [];

        $earlyDate = TicketApi::getEarliestTwoDayPriceDate($product['id'], $this->_supplyId, $this->saleChannel, $aid);
        if (!$earlyDate || empty($earlyDate)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '该产品暂不可售');
        }

        $firstDate  = $earlyDate[0]['date'];
        $secondDate = $earlyDate[1]['date'];

        $bookInfo = [
            'refund_rule'       => $product['refund_rule'],
            'refund_early_time' => $product['refund_early_time'],
            'reb'               => $product['reb'],
            'reb_type'          => $product['reb_type'],
            'cancel_cost'       => $cancelCost,
            'batch_check'       => $product['batch_check'],
            'batch_day'         => $product['batch_day_check'],
            //最近可售日期
            'startDate'         => $firstDate,
            'refund_after_time' => $product['refund_after_time'] ?? 0,
        ];

        //有效时间
        $validTime = '';
        if (strtotime($product['order_start']) > 0) {
            //时间段内有效
            $tmpS = date('Y-m-d', strtotime($product['order_start']));
            $tmpE = date('Y-m-d', strtotime($product['order_end']));

            if ($product['if_verify']) {
                $tmpS = '随游玩日期变化';
            }
            $validTime = $tmpS . '~' . $tmpE;
        } elseif ($product['delaydays']) {
            //多少天内有效
            $validTime = $product['delaydays'];
        } else {
            //当天有效
            $validTime = 0;
        }
        $bookInfo['validTime'] = $validTime;
        $bookInfo['validType'] = $product['delaytype'];

        //景区信息
        $land = $Land->getLandInfo(
            $product['landid'],
            false,
            'title,p_type,venus_id'
        );
        //门票额外信息
        $landExt = [
            'v_time_limit' => $ticketInfoRes['data']['uuLandFDTO']['vTimeLimit'],
            'ass_station'  => $ticketInfoRes['data']['uuLandFDTO']['assStation'],
            'tourist_info' => $ticketInfoRes['data']['uuLandFDTO']['touristInfo'],
            'dhour'        => $ticketInfoRes['data']['uuLandFDTO']['dhour'],
        ];

        if ($landExt['dhour'] && in_array($bookInfo['startDate'], [date('Y-m-d')])) {
            //需要在几点前预定
            if (date('H:i:s') > $landExt['dhour']) {
                //预定日期+1
                $bookInfo['startDate'] = date('Y-m-d', strtotime($bookInfo['startDate']) + 3600 * 24);
            }
        }

        if ($bookInfo['startDate'] != $firstDate && $secondDate) {
            $bookInfo['startDate'] = $secondDate;
        }

        $bookInfo['title']    = $land['title'];
        $bookInfo['p_type']   = $land['p_type'];
        $bookInfo['venus_id'] = $land['venus_id'];
        $bookInfo['needID']   = $landExt['tourist_info'];

        //可验证时间
        $verifyTime = '';
        if ($landExt['v_time_limit'] == 0) {

            $verifyMap = [0, 1, 2, 3, 4, 5, 6];
            //按星期验证
            if ($product['order_limit']) {
                $verifyTime = array_values(array_diff($verifyMap, explode(',', $product['order_limit'])));
            } else {
                //不限制验证时间
                $verifyTime = -1;
            }

        } else {
            //时间段内有效
            $vTimeLimit = $landExt['v_time_limit'];
            $verifyTime = str_replace('|', '~', $vTimeLimit);
        }
        $bookInfo['verifyTime'] = $verifyTime;

        //线路获取集合地点
        if ($land['p_type'] == 'B') {
            $bookInfo['assStation'] = $landExt['ass_station'] ? json_decode($landExt['ass_station'], true) : [];
            $bookInfo['assStation'] = array_values($bookInfo['assStation']);
        }

        return $bookInfo;
    }

    /**
     * 获取联票产品
     *
     * @param  int  $tid  门票id
     * @param  int  $landid  景区id
     * @param  string  $type  景区类型
     * @param  string  $date  可预订日期
     * @param  int  $payMode  主要票支付方式
     *
     * @return array
     */
    private function _getLinkProduct($tid, $landid, $type, $date, $payMode)
    {

        $Model = new Ticket('slave');

        $tickets    = $this->_getLandTickets($landid);
        $unLinkType = ['C', 'H', 'I', 'J']; //不能下联票

        $tidArr = array_column($tickets, "tid");

        $ticketParamList = [];
        foreach ($tickets as $key => $value) {
            $ticketParamList[] = [
                'ticketId' => $value['tid'],
                'sid'      => $value['sapply_sid'],
                'fid'      => $value['apply_sid'],
                'channel'  => $this->saleChannel,
            ];
        }
        $ticketApi   = new \Business\JavaApi\Product\Ticket();
        $tempTickets = $ticketApi->batchQueryTicketByIds($ticketParamList);
        if ($tempTickets['code'] != 200) {
            return $this->apiReturn($tempTickets['code'], [], $tempTickets['msg']);
        }
        $tempTickets    = $tempTickets['data'];
        $tempTicketInfo = [];
        foreach ($tempTickets as $key => $value) {
            $tempTicketInfo[$value['id']] = $value;
        }

        //获取一次门票扩展属性
        $ticketArr = (new \Business\CommodityCenter\Ticket())->queryTicketInfoByIds($tidArr);
        $ticketExt = [];
        foreach ($ticketArr as $ticket) {
            $ticketExt[$ticket['ticket']['id']] = $ticket['ext'];
        }

        $orderUnityBusiness  = new \Business\Order\OrderUnity;
        $idArrayInfo['data'] = $tidArr;
        $return              = [];
        foreach ($tickets as $key => $item) {
            $pos      = array_search($item['tid'], $idArrayInfo['data']);
            $tmpPrice = $Model->getSettlePrice($item['tid'], $this->_supplyId, $item['apply_sid'], $date);
            // if ($tmpPrice === false || $item['pay']!=$payMode) {
            if ($item['pay'] != $payMode) {
                continue;
            }
            // $return[$pos] = [
            $return[] = [
                'title'                     => $item['ttitle'],
                'intro'                     => isset($tempTicketInfo[$item['tid']]['notes']) ? $tempTicketInfo[$item['tid']]['notes'] : $item['notes'],
                'jsprice'                   => $tmpPrice,
                'tprice'                    => isset($tempTicketInfo[$item['tid']]['tprice']) ? $tempTicketInfo[$item['tid']]['tprice'] : $item['tprice'],
                'pid'                       => $item['pid'],
                'tid'                       => $item['tid'],
                'aid'                       => $item['apply_sid'],
                'buy_low'                   => isset($tempTicketInfo[$item['tid']]['buyLimitLow']) ? $tempTicketInfo[$item['tid']]['buyLimitLow'] : (int)$item['buy_limit_low'],
                'buy_up'                    => isset($tempTicketInfo[$item['tid']]['buyLimitUp']) ? $tempTicketInfo[$item['tid']]['buyLimitUp'] : (int)$item['buy_limit_up'],
                'paymode'                   => isset($tempTicketInfo[$item['tid']]['pay']) ? $tempTicketInfo[$item['tid']]['pay'] : $item['pay'],
                'age_limit_max'             => $item['age_limit_max'] ? (string)$item['age_limit_max'] : '',
                'age_limit_min'             => $item['age_limit_min'] ? (string)$item['age_limit_min'] : '',
                'use_early_days'            => isset($tempTicketInfo[$item['tid']]['useEarlyDays']) ? $tempTicketInfo[$item['tid']]['useEarlyDays'] : $item['use_early_days'],
                'valid_period_start'        => isset($tempTicketInfo[$item['tid']]['orderStart']) ? $tempTicketInfo[$item['tid']]['orderStart'] : $item['order_start'],
                'valid_period_end'          => isset($tempTicketInfo[$item['tid']]['orderEnd']) ? $tempTicketInfo[$item['tid']]['orderEnd'] : $item['order_end'],
                'valid_period_type'         => isset($tempTicketInfo[$item['tid']]['delaytype']) ? $tempTicketInfo[$item['tid']]['delaytype'] : $item['delaytype'],
                'valid_period_days'         => isset($tempTicketInfo[$item['tid']]['delaydays']) ? $tempTicketInfo[$item['tid']]['delaydays'] : $item['delaydays'],
                'valid_period_timecancheck' => isset($tempTicketInfo[$item['tid']]['ifVerify']) ? $tempTicketInfo[$item['tid']]['ifVerify'] : $item['if_verify'],
                'refund_rule'               => isset($tempTicketInfo[$item['tid']]['refundRule']) ? $tempTicketInfo[$item['tid']]['refundRule'] : $item['refund_rule'],
                'refund_audit'              => isset($tempTicketInfo[$item['tid']]['refundAudit']) ? $tempTicketInfo[$item['tid']]['refundAudit'] : $item['refund_audit'],
                'refund_early_time'         => isset($tempTicketInfo[$item['tid']]['refundEarlyTime']) ? $tempTicketInfo[$item['tid']]['refundEarlyTime'] : $item['refund_early_time'],
                'ddays'                     => isset($tempTicketInfo[$item['tid']]['ddays']) ? $tempTicketInfo[$item['tid']]['ddays'] : $item['ddays'],
                'refund_after_time'         => $ticketExt[$item['tid']]['refund_after_time'] ?? 0
            ];

            if ($item['tid'] == $tid && $type == 'B') {
                // $returnSwap=array_pop($return);
                // $return=[$returnSwap];
                // break;
            }
        }
        ksort($return);

        return array_values($return);
    }

    /**
     * 预定页面-日历价格
     * @return [type] [description]
     */
    public function getCalendarPrice()
    {

        $pid  = I('pid', '', 'intval');
        $date = I('date', date('Y-m-d'));
        $aid  = I('aid', '', 'intval');

        if (!$pid || !$date) {
            $this->apiReturn(204, [], '参数错误');
        }

        $beginDate = date('Y-m-d', strtotime($date));
        $tmp       = date('Y-m-01', strtotime($date));
        $endDate   = date('Y-m-d', strtotime("$tmp +1 month -1 day"));

        $tickModel = new \Business\Product\Ticket();
        $priceset  = $tickModel->getRangeRetailPrice($beginDate, $endDate, $pid);
        //单位转换成为元
        $priceset = array_map(function ($val) {
            return $val / 100;
        }, $priceset);

        if ($this->_allDisMan) {
            $tmp      = [
                'pid'  => $pid,
                'aid'  => $aid,
                'list' => $priceset,
            ];
            $priceset = AllDisProcess::getAllDisPrice($this->_supplyId, $tmp, 'calendar');
            $priceset = $priceset['list'];
        }

        $this->apiReturn(200, $priceset);
    }

    /**
     * 抢购日历
     * <AUTHOR>
     * @date   2018-02-06
     */
    public function seckillCalendar()
    {
        //抢购id
        $seckillId = I('seckill_id', 0, 'intval');
        //门票pid
        $pid = I('pid', 0, 'intval');
        //上级供应商id
        $date = I('date', date('Y-m-d'));

        $redisObj = Cache::getInstance('redis');
        $cacheKey = "seckill:{$pid}_{$date}:calendar";
        //缓存是否存在
        $cacheData = $redisObj->get($cacheKey);
        //获取最近周期的开始和结束时间
        $seckillBiz = new \Business\Wechat\Seckill();

        if (!$cacheData) {
            //获取抢购活动信息
            $getRes = $seckillBiz->getSeckill($seckillId);
            if ($getRes['code'] != 200) {
                $this->apiReturn(204, [], '抢购活动不存在');
            }
            $seckill = $getRes['data'];
            //开始时间
            $begin = date('Y-m-d', $seckill['begin']);
            //结束时间
            $end = date('Y-m-d', $seckill['end']);

            $beginDate = date('Y-m-d', strtotime($date));
            $tmp       = date('Y-m-01', strtotime($date));
            $endDate   = date('Y-m-d', strtotime("$tmp +1 month -1 day"));

            $tickModel = new Ticket('slave');
            $ticketBiz = new \Business\Product\Ticket();
            //有价格的日期
            $priceset = $ticketBiz->getRangeRetailPrice($beginDate, $endDate, $pid);
            //期票判断
            $ticket = $tickModel->getTicketInfoByPid($pid, 'order_start,order_end');
            if ($ticket['order_start'] && $ticket['order_end'] && $ticket['order_start'] != '0000-00-00 00:00:00') {
                $orderStart = date('Y-m-d', strtotime($ticket['order_start']));
                $orderEnd   = date('Y-m-d', strtotime($ticket['order_end']));
                $beginDate  = $orderStart;
                $endDate    = $orderEnd;
            }

            $beginDate = $beginDate >= date('Y-m-d') ? $beginDate : date('Y-m-d');
            $dateSet   = [];
            if ($priceset) {
                foreach ($priceset as $key => $item) {
                    if ($key >= $beginDate && $key <= $endDate) {
                        $dateSet[] = $key;
                    }
                }
            }

            $redisObj->set($cacheKey, json_encode($dateSet), '', 3600);
        } else {
            $dateSet = json_decode($cacheData, true);
        }

        $this->apiReturn(200, $dateSet);
    }

    /**
     * 该接口已经废弃，看之前日志主要还是调用Product/getPriceAndStorage
     * 预定页面-获取价格和库存
     */
    public function getPriceAndStorage()
    {
        $this->apiReturn(201, [], '接口废弃');
    }

    /**
     * 预定页面-获取价格和库存（针对酒店）
     * @return [type] [description]
     */
    public function getHotelPriceAndStorage()
    {

        $beginDate = I('beginDate');
        $endDate   = I('endDate');
        $pid       = I('pid');
        $aid       = I('aid');
        $pid       = explode('-', $pid);
        $result    = [];

        //根据产品id获取票类ID
        $tModel       = new \Model\Product\Ticket();
        $tmpTidPidArr = $tModel->getTicketInfoByPidArr($pid, 'pid,id');
        $tidPidArr    = [];
        foreach ($tmpTidPidArr as $item) {
            $tidPidArr[$item['pid']] = $item['id'];
        }
        foreach ($pid as $value) {
            //根据产品ID获取票ID
            $result[$value] = $this->getHotelPriceAndStorageOnce($value, $aid, $beginDate, $endDate,
                $tidPidArr[$value]);
        }
        $this->apiReturn(200, $result);
    }

    /**
     * 获取酒店的价格数据
     * <AUTHOR>
     * @dateTime 2018-03-13T11:51:42+0800
     * @throws   \Exception                          可能抛出异常
     *
     * @param    [type]                   $pid       产品ID
     * @param    [type]                   $aid       供应商ID
     * @param    [type]                   $beginDate 开始时间
     * @param    [type]                   $endDate   结束时间
     *
     * @return   [type]                   $tid       票ID   PeiJun Li  2018-9-27
     */
    private function getHotelPriceAndStorageOnce($pid, $aid, $beginDate, $endDate, $tid)
    {

        if (!$pid || !$aid) {
            return [
                'jsprice' => -1,
                'store'   => [],
            ];
        }

        if ($beginDate < date('Y-m-d') || $endDate < $beginDate) {
            return [
                'jsprice' => -1,
                'store'   => [],
            ];
        }

        //对接到java获取价格处理   PeiJun Li  2018-9-27
        $priceset = TicketApi::getCalendarPriceAndStorage($tid, $beginDate, $endDate);

        if (!$priceset) {
            return [
                'jsprice' => -1,
                'store'   => [],
            ];
        }

        //新接口返回的数据key值不再是时间了，放在下面的循环处理
        //unset($priceset[$endDate]);

        //检测是否存在某天无价格,无库存
        $totalPirce = 0;
        $store      = [];
        //对接到java获取价格处理   PeiJun Li  2018-9-27
        foreach ($priceset as $k => $item) {
            $date = $item['time'];

            if ($date == $endDate) {
                unset($priceset[$k]);
                continue;
            }

            $msprice = $item['counterPrice'] / 100; //门市价
            //如果是小程序获取窗口价，否则获取零售价
            if (\inWechatSmallApp()) {
                $price = $item['windowPrice'];
            } else {
                $price = $item['retailPrice'];
            }
            if ($price == -1) {
                return [
                    'jsprice' => -1,
                    'store'   => [],
                ];
            }

            //获取当日库存
            $tmp = $this->_getStorage([$pid], $aid, $date);

            $store[$date] = ((int)$tmp[$pid] >= -1) ? (int)$tmp[$pid] : 0;

            //全民分销价格
            if ($this->_allDisMan) {
                $param      = [
                    'jsprice' => $price / 100,
                    'pid'     => $pid,
                    'aid'     => $aid,
                ];
                $changeRes  = AllDisProcess::getAllDisPrice($this->_supplyId, [$param]);
                $totalPirce += $changeRes[0]['jsprice'];
            } else {
                $totalPirce += $price / 100;
            }
        }

        $return = [
            'msprice' => $msprice,
            'jsprice' => $totalPirce,
            'store'   => $store,
        ];

        return $return;
    }

    /**
     * 预定页面-获取价格
     * @param  array  $pidArr  pid数组   [1026, 1027]
     * @param  int  $aid  上级供应商id
     * @param  string  $date  日期 2016-08-10
     *
     * @return  [1026 => 1, 1027 => 2]
     */
    private function _getPrice($pidArr, $aid, $date)
    {
        $tickModel = new Ticket('slave');
        $priceset  = $tickModel->getMuchRetailPrice($pidArr, $date);

        if ($this->_allDisMan) {
            foreach ($pidArr as $pid) {
                $tmp[] = [
                    'pid'   => $pid,
                    'aid'   => $aid,
                    'price' => $priceset[$pid],
                ];
            }
            $tmpPrice = AllDisProcess::getAllDisPrice($this->_supplyId, $tmp, 'interface');
            foreach ($tmpPrice as $item) {
                $priceset[$item['pid']] = $item['price'];
            }
            // $priceset = array_values($priceset);
        }

        return $priceset;
    }

    /**
     * 预订页面-获取产品库存
     * @param  array  $pidArr  pid数组   [1026, 1027]
     * @param  int  $aid  上级供应商id
     * @param  string  $date  日期 2016-08-10
     *
     * @return  [1026 => 1, 1027 => 2]
     */
    private function _getStorage($pidArr, $aid, $date)
    {

        $tickModel = new Ticket('slave');
        $ptype     = $tickModel->getProductType($pidArr[0]);
        switch ($ptype) {
            case 'A': //景区

            case 'B': //线路

            case 'C': //酒店

            case 'G': //餐饮

            case 'I': //年卡

            case 'F': //套票
                $result = $this->_getStorageForCommon($pidArr, $date, $aid);
                break;

            case 'H': //演出
                $result = $this->_getStorageForShow($pidArr, $date, $aid);
                break;

            default:
                $this->apiReturn(204, '产品类型错误');
        }

        return $result;
    }

    /**
     * 通用景区类型获取库存接口
     *
     * @param  array  $pidArr  产品id数组 [2016, 2017]
     * @param  string  $date  日期 2016-08-01
     * @param  int  $aid  上级供应商id
     *
     * @return array    [2016 => 1, 2017 => -1]
     */
    private function _getStorageForCommon($pidArr, $date, $aid)
    {
        $Model = new Ticket('slave');

        return $Model->getMuchStorage($pidArr, $date);
    }

    private function _getStorageForShow()
    {
        // to be continued
    }

    /**
     * 获取演出门票对应的区域id
     *
     * @param  array  $tickets  门票列表信息
     *
     * @return array
     */
    private function _getZoneId($tickets)
    {
        $tidArr = array_column($tickets, 'tid');

        $javaApi  = new \Business\CommodityCenter\LandF();
        $landfArr = $javaApi->queryLandFByLandIdAndTicketIdAndProductId($tidArr, [], [], 'tid, zone_id', true);

        foreach ($tickets as &$item) {
            $tid = $item['tid'];

            if (isset($landfArr[$tid])) {
                $item['zone_id'] = $landfArr[$tid];
            } else {
                $item['zone_id'] = 0;
            }
        }

        return $tickets;
    }

    /**
     * 获取产品集合
     *
     * @param  array  $option  筛选参数
     *
     * @return array
     */
    private function _getProductSet($option)
    {
        $tickModel = new Ticket('slave');

        $inWechatSmApp = $this->inWechatSmallApp();
        //微信小程序暂时不做渠道过滤,也不获取转分销的产品
        if ($inWechatSmApp) {
            //自供应产品,如果是小程序里面请求，先不判断供应商ID
            $products = $tickModel->getSaleProducts($this->_supplyId, $option);
        } else {
            $supplyIds = $this->_supplyId;
            if ($supplyIdsStr = $this->getArrSupplyIds()) {
                $supplyIds       = $supplyIdsStr;
                $option['order'] = 'p.apply_did';
                if ($this->inUnionApp()) {
                    if ($this->unionSmallAppType == 1) {
                        $option['union_channel'] = self::__MALL_CHANNEL__;
                    } else {
                        $option['union_channel'] = self::__UNION_CHANNEL__;
                    }

                }
            }
            //转分销+自供应
            $products = $tickModel->getAllSaleProductsByFid($supplyIds, $option);
        }

        //渠道过滤(微信渠道)
        $products = $this->channelFilter($products, $inWechatSmApp);
        //产品排序
        $products = $this->productOrder($products);

        return $products;
    }

    protected function getSupplyIdsByAccounts(array $accounts)
    {
        foreach ($accounts as $key => $value) {
            if (empty($value)) {
                unset($accounts[$key]);
            }
        }
        $member      = new \Model\Member\Member();
        $where       = ["account" => ['in', $accounts]];
        $field       = "id";
        $memberInfos = $member->getMemberInfoByMulti($accounts, 'account', 'id');

        return array_column($memberInfos, "id");
    }

    /**
     * 整合前端需要的数据
     *
     * @param  array  $lands  景区信息
     * @param  array  $pidArr  门票pid数组
     *
     * @return
     */
    private function _productDeal($lands, $pidArr, $num)
    {
        if ($this->getSupplyIds()) {
            $landOrderSwap = [];
            $idMapName     = ProductList::getIdMapName($this->getArrSupplyIds());
            foreach ($lands as $value) {
                $account                                      = ProductList::searchAccount($value[0]['pid'],
                    $idMapName);
                $landOrderSwap[$account][$value[0]['landid']] = $value;
            }
            $landOrder = [];
            foreach ($landOrderSwap as $value) {
                foreach ($value as $key => $item) {
                    $landOrder[$key] = $item;
                }
            }
            $lands = $landOrder;
        }
        //零售价
        $priceArr = $this->getMuchRetailPrice($pidArr);

        $index = $enough = 0;
        $data  = [];
        foreach ($lands as $tickets) {
            $index++;

            if ($this->_lastPos && $index <= $this->_lastPos && $num != -1) {
                continue;
            }

            //最低零售价
            $minLPrice = $minGPrice = null;
            foreach ($tickets as $ticket) {
                if (!array_key_exists($ticket['pid'], $priceArr)) {
                    continue;
                }
                if ($minLPrice === null || $priceArr[$ticket['pid']] < $minLPrice) {
                    $minPid    = $ticket['pid'];
                    $minTid    = $ticket['tid'];
                    $minLPrice = $priceArr[$ticket['pid']];
                    $minGPrice = $ticket['tprice'];
                }
            }

            if ($minLPrice !== null) {
                //说明至少有一张可售票类
                $enough++;
                $data[]         = [
                    'lid'       => $tickets[0]['landid'],
                    'tid'       => $tickets[0]['tid'],
                    'title'     => $tickets[0]['title'],
                    'area'      => $tickets[0]['area'],
                    'address'   => $tickets[0]['address'],
                    'aid'       => $tickets[0]['apply_sid'],
                    'imgpath'   => $tickets[0]['imgpath'],
                    'topic'     => $tickets[0]['topic'],
                    'lptype'    => $tickets[0]['lptype'],
                    'jsprice'   => sprintf('%.2f', $minLPrice),
                    'tprice'    => sprintf('%.2f', $minGPrice),
                    'pid'       => $minPid,
                    'tid'       => $minTid,
                    'apply_did' => $tickets[0]['apply_did'],
                ];
                $this->_lastPos = $index;
                if ($enough == $num) {
                    // $this->_lastPos = $index;
                    if ($num == -1) {
                        continue;
                    }
                    break;
                }
            }
        }

        $ticketIds = array_column($data, 'tid');
        $ticketIds = implode(',', $ticketIds);
        $priceData = TicketApi::getSinglePrices($ticketIds);

        foreach ($data as $key => $value) {
            if (!isset($priceData[$value['tid']])) {
                continue;
            }
            $data[$key]['tprice'] = $priceData[$value['tid']]['counter_price'] / 100;
        }

        return $data;
    }

    /**
     * 获取微商城配置
     * @return [type] [description]
     */
    public function getCustomConfig($return = false)
    {

        $config = $this->getMallConfig();
        $name   = $config['name'];
        $others = json_decode($config['others'], true);
        $banner = $others['banner'];

        if (!$banner) {
            $banner[0] = [
                'http://images.12301.cc/123624/14786829442552.jpg' => '',
            ];
        }

        $data = [
            'name'   => $name ?: '微商城',
            'banner' => $banner,
            'tel'    => $config['telephone'],
            'theme'  => $others['template'] ?: 'default',
        ];

        if ($return) {
            return $data;
        }

        $this->apiReturn(200, $data);
    }

    /**
     * 获取产品类型列表
     *
     * @param  $return bool 是否当作函数返回值返回
     *
     * @return [type] [description]
     */
    public function getTypeList($back = false)
    {

        $config = $this->getMallConfig();
        $others = json_decode($config['others'], true);

        $return = [];
        if (isset($others['type'])) {
            foreach ($others['type'] as $identify => $name) {
                $return[] = [
                    'name'      => $name,
                    'identify'  => $identify,
                    "isChecked" => "true",
                ];
            }
        } else {
            $return = [
                // ['name' => '景区', 'identify' => 'A'],
                // ['name' => '酒店', 'identify' => 'C'],
                // ['name' => '周边游', 'identify' => 'B'],
                // ['name' => '套票', 'identify' => 'F'],
                // ['name' => '演出', 'identify' => 'H'],
            ];
        }
        //加入主题
        if (isset($others['themes'])) {
            foreach ($others['themes'] as $key => $name) {
                if (is_array($name)) {
                    $isChecked = $name['isChecked'];
                    $name      = $name['name'];
                }
                if ($isChecked == 'false') {
                    continue;
                }
                $return[] = [
                    'name'      => $name,
                    'show_name' => $key,
                    'identify'  => 'theme',
                    "isChecked" => $isChecked,
                ];
            }
        }

        if ($back) {
            return $return;
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 获取主题列表
     * @return [type] [description]
     */
    public function getThemes()
    {

        $config = $this->getMallConfig();
        $others = json_decode($config['others'], true);

        if (isset($others['themes'])) {
            if (!is_numeric(key($others['themes']))) {
                $return = [];
                foreach ($others['themes'] as $value) {
                    $return[] = $value['name'];
                }
            } else {
                $return = $others['themes'];
            }
        } else {
            $return = ThemeConst::THEMES;
        }

        return $return;
    }

    /**
     * 祥源定制主题,需要的城市接口
     * <AUTHOR>
     * @date   2017-12-11
     */
    public function getCityForGroupTheme()
    {
        $getEvoluteBiz = new \Business\Product\Get\Evolute();
        $list          = $getEvoluteBiz->getEvoluteByFidStatus([$this->_supplyId], 0);

        $return = [];
        if ($list) {

            //过滤门票状态
            $tickModel = new Ticket('slave');
            $pidArr    = array_column($list, 'pid');
            $pidArr    = $tickModel->filterInvalidPid($pidArr);

            $lidArr = [];
            foreach ($list as $item) {
                $tmpChannel = explode(',', $item['channel']);
                //过滤渠道
                if (in_array(self::__MALL_CHANNEL__, $tmpChannel)) {
                    if (in_array($item['pid'], $pidArr)) {
                        $lidArr[]                = $item['lid'];
                        $pxMapping[$item['lid']] = $item['px'];
                    }
                }
            }

            if ($lidArr) {
                //获取景区code
                //$landModel   = new Land('slave');
                //$areaMapping = $landModel->getLandInfoByMuli($lidArr, 'id,area,title', true);

                $javaAPi     = new \Business\CommodityCenter\Land();
                $landInfo    = $javaAPi->queryLandMultiQueryById($lidArr);
                $areaMapping = array_column($landInfo, null, 'id');

                $areaModel = new Area();
                $areas     = $areaModel->getAreaList();

                foreach ($areaMapping as $lid => $item) {

                    if (!$item['area']) {
                        continue;
                    }

                    $codeArr  = explode('|', $item['area']);
                    $province = $codeArr[0];
                    $city     = $codeArr[1];

                    if (!$city || !$province) {
                        continue;
                    }

                    $return[$province]['name'] = $areas[$province];

                    if (isset($return[$province]['list'][$city])) {
                        $nowPx = $return[$province]['list'][$city]['land']['px'];
                        //推荐值大的代替小的
                        if ($pxMapping[$lid] > $nowPx) {
                            $return[$province]['list'][$city]['land'] = [
                                'lid'  => $lid,
                                'px'   => $pxMapping[$lid],
                                'name' => $item['title'],
                            ];
                        }

                        continue;
                    } else {
                        $return[$province]['list'][$city] = [
                            'name' => $areas[$city],
                            'code' => $city,
                            'land' => ['lid' => $lid, 'px' => $pxMapping[$lid], 'name' => $item['title']],
                        ];
                    }
                }
            }
        }

        $this->apiReturn(200, array_values($return));
    }

    /**
     * php获取中文字符拼音首字母
     *
     * @param  string  $str  中文字符
     */
    private function _getFirstCharter($str)
    {
        if (empty($str)) {
            return '';
        }

        $fchar = ord($str{0});

        if ($fchar >= ord('A') && $fchar <= ord('z')) {
            return strtoupper($str{0});
        }

        $s1 = iconv('UTF-8', 'gb2312', $str);

        $s2 = iconv('gb2312', 'UTF-8', $s1);

        $s = $s2 == $str ? $s1 : $str;

        $asc = ord($s{0}) * 256 + ord($s{1}) - 65536;
        if ($asc >= -20319 && $asc <= -20284) {
            return 'A';
        }

        if ($asc >= -20283 && $asc <= -19776) {
            return 'B';
        }

        if ($asc >= -19775 && $asc <= -19219) {
            return 'C';
        }

        if ($asc >= -19218 && $asc <= -18711) {
            return 'D';
        }

        if ($asc >= -18710 && $asc <= -18527) {
            return 'E';
        }

        if ($asc >= -18526 && $asc <= -18240) {
            return 'F';
        }

        if ($asc >= -18239 && $asc <= -17923) {
            return 'G';
        }

        if ($asc >= -17922 && $asc <= -17418) {
            return 'H';
        }

        if ($asc >= -17417 && $asc <= -16475) {
            return 'J';
        }

        if ($asc >= -16474 && $asc <= -16213) {
            return 'K';
        }

        if ($asc >= -16212 && $asc <= -15641) {
            return 'L';
        }

        if ($asc >= -15640 && $asc <= -15166) {
            return 'M';
        }

        if ($asc >= -15165 && $asc <= -14923) {
            return 'N';
        }

        if ($asc >= -14922 && $asc <= -14915) {
            return 'O';
        }

        if ($asc >= -14914 && $asc <= -14631) {
            return 'P';
        }

        if ($asc >= -14630 && $asc <= -14150) {
            return 'Q';
        }

        if ($asc >= -14149 && $asc <= -14091) {
            return 'R';
        }

        if ($asc >= -14090 && $asc <= -13319) {
            return 'S';
        }

        if ($asc >= -13318 && $asc <= -12839) {
            return 'T';
        }

        if ($asc >= -12838 && $asc <= -12557) {
            return 'W';
        }

        if ($asc >= -12556 && $asc <= -11848) {
            return 'X';
        }

        if ($asc >= -11847 && $asc <= -11056) {
            return 'Y';
        }

        if ($asc >= -11055 && $asc <= -10247) {
            return 'Z';
        }

        return null;
    }

    /**
     * 得到指定产品当前可用的优惠券列表
     * <AUTHOR>
     * @date   2017-07-18
     *
     * @param  int  $fid  获取优惠券的用户ID
     * @param  int  $aid  优惠券的供应商ID
     * @param  int  $pid  优惠券指定使用的产品ID
     * @param  date  $useDate  优惠券使用日期
     *
     * @return ['valid'=>[], 'other'=>[]]
     */
    public function getCouponList()
    {
        // 全民分销
        if ($_SESSION['identify'] == 'allDis') {
            $this->apiReturn(202, [], '全民分销不可使用优惠券');
        }

        $aid = $this->_supplyId;
        $fid = $_SESSION['sid'];

        $pid     = I('pid', 0, 'intval');
        $today   = date('Y-m-d');
        $useDate = I('date', time(), 'strtotime');
        $useDate = date('Y-m-d', $useDate);
        if ($useDate < $today) {
            $useDate = $today;
        }

        $bus = new \Business\Order\Coupon();
        $res = $bus->getCouponList($fid, $aid, $pid, $useDate);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $return = $res['data'];

        // 其他优惠券 - 适用产品信息
        if (!empty($return['other'])) {

            $other = $bus->getCouponProductName($return['other']);
            if ($other['code'] != 200) {
                $this->apiReturn($other['code'], [], $other['msg']);
            }

            $return['other'] = $other['data'];
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 首页抢购轮播
     * <AUTHOR>
     * @date   2017-12-26
     */
    public function getSeckillCarousel()
    {

        $seckillBiz = new \Business\Wechat\Seckill();

        $result = $seckillBiz->getSeckillCarousel($this->_supplyId, 1, 5);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取正在进行中的抢购活动
     * <AUTHOR>
     * @date   2017-12-26
     */
    public function getUnderwaySeckills()
    {

        $page = I('page', 1, 'intval');

        $size = I('size', 10, 'intval');

        if ($size > 100) {
            $this->apiReturn(204, '获取条数超出限制');
        }

        $seckillBiz = new \Business\Wechat\Seckill();

        $result = $seckillBiz->getUnderwaySeckills($this->_supplyId, $page, $size);

        if (isset($result['code'])) {
            //全民营销推广
            if ($result['data'] && $this->_promoter) {
                $list = $result['data'];
                foreach ($list as &$item) {
                    $item['jsprice'] = $item['price'] / 100;
                }
                //计算推广佣金
                $result['data'] = AllDisProcess::calRecommendCommission($this->_supplyId, $list);
            }
            $this->apiReturn($result['code'], $result['data']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取即将开始的抢购活动
     * <AUTHOR>
     * @date   2017-12-26
     */
    public function getComingSeckills()
    {

        $page = I('page', 1, 'intval');
        $size = I('size', 10, 'intval');
        //微信openid
        $openid = I('session.openid', '');

        if ($size > 100) {
            $this->apiReturn(204, '获取条数超出限制');
        }

        if (isset($_SESSION['identify'])) {
            //全民营销,获取供应商openid
            $allDisModel = new AllDisModel();
            $memberInfo  = $allDisModel->getAllDisMemberInfo(I('session.memberID'));
            if ($memberInfo && $memberInfo['supply_openid']) {
                $openid = $memberInfo['supply_openid'];
            }
        }

        $seckillBiz = new \Business\Wechat\Seckill();
        $result     = $seckillBiz->getComingSeckills($this->_supplyId, $page, $size, $openid);

        if (isset($result['code'])) {
            //全民营销推广
            if ($result['data'] && $this->_promoter) {
                $list = $result['data'];
                foreach ($list as &$item) {
                    $item['jsprice'] = $item['price'] / 100;
                }
                //计算推广佣金
                $result['data'] = AllDisProcess::calRecommendCommission($this->_supplyId, $list);
            }
            $this->apiReturn($result['code'], $result['data']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 设置抢购提醒
     * <AUTHOR>
     * @date   2017-12-26
     */
    public function setSeckillWarning()
    {

        $id = I('id', 0, 'intval');
        //微信openid
        $openid = I('session.openid', '');

        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }
        if (!$openid) {
            $this->apiReturn(204, [], '没有发送微信提醒的权限');
        }

        if (isset($_SESSION['identify'])) {
            //全民营销,获取供应商openid
            $allDisModel = new AllDisModel();
            $memberInfo  = $allDisModel->getAllDisMemberInfo(I('session.memberID'));
            if ($memberInfo && $memberInfo['supply_openid']) {
                $openid = $memberInfo['supply_openid'];
            }
        }

        $seckillBiz = new \Business\Wechat\Seckill();
        $result     = $seckillBiz->setSeckillWarning($id, $this->_supplyId, $openid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

}
