<?php

/**
 * 拼团后台管理控制器
 * <AUTHOR>
 */
 
 namespace Controller\Mall;

 use Business\Wechat\GroupBooking AS GroupBiz;

class GroupBookingManage extends Mall {
    private $_sid;

    public function __construct() {
        //主账号id
        $this->_sid = $this->isLogin('ajax');
    }

    /**
     * 创建拼团活动
     * <AUTHOR>
     * @date   2018-05-21
     */
    public function create() {
        //请求参数数组
        $request = I('');

        $groupBiz = new GroupBiz();
        $result = $groupBiz->create($this->_sid, $request);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }

    /**
     * 获取拼团活动
     * <AUTHOR>
     * @date   2018-05-21
     */
    public function cliqueList()
    {   
        
        //当前页数
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');
        if (!$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($size > 20) {
            $this->apiReturn(204, [], '获取条数超出限制');
        }

        $groupBiz = new \Business\Wechat\GroupBooking();
        $result   = $groupBiz->cliqueList($this->_sid, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }


    /**
     * 获取的我的供应商拼团活动
     * <AUTHOR>
     * @date   2018-05-23
     */
    public function getSupplyActivityList() {

        //当前页数
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');
        if (!$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($size > 20) {
            $this->apiReturn(204, [], '获取条数超出限制');
        }

        $groupBiz = new \Business\Wechat\GroupBooking();
        $result   = $groupBiz->getSupplyActivityList($this->_sid, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }

    }
        
    /**
     * 更新拼团活动
     * <AUTHOR>
     * @date   2018-05-22
     */
    public function update()
    {
        //请求参数
        $request = I('');
        $id = $request['id'];

        if (!$id) {
            $this->apiReturn(204, [], '活动id缺失');
        }

        $groupBiz = new GroupBiz();
        $result = $groupBiz->update($this->_sid, $id, $request);


        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }


    /**
     * 删除活动
     * <AUTHOR>
     * @date   2018-05-28
     */
    public function delete() {
        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        $groupBiz = new GroupBiz();
        $result = $groupBiz->delete($this->_sid, $id);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '接口发生错误');
        }

    }
    /**
     * 获取可以做拼团的产品列表
     * <AUTHOR>
     * @date   2018-03-06
     */
    public function getProductList() {

        //关键字
        $keyword = I('keyword', '');
        $size    = I('size', 10, 'intval');
        if (!$keyword) {
            $this->apiReturn(204, [], '请输入关键字搜索');
        }
        if ($size > 10) {
            $this->apiReturn(204, [], '超出最大匹配条数');
        }

        $groupBiz = new GroupBiz();
        $result = $groupBiz->getProductList($this->_sid, $keyword, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }

    /**
     * 展示所选门票的相关属性
     * <AUTHOR>
     * @date   2018-05-22
     */
    public function getTicketAttr() {

        //门票id
        $tid = I('tid', 0, 'intval');
        //供应商id
        $aid = I('aid', 0, 'intval');
        //活动开始日期
        $date = I('date', date('Y-m-d'));

        if (!$tid || !$date) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $groupBiz = new GroupBiz();
        $result = $groupBiz->getTicketAttr($this->_sid, $tid, $aid, $date);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }

    /**
     * 获取拼团活动详情
     * <AUTHOR>
     * @date   2018-05-22
     */
    public function getActivityInfo() {
        $id = I('id');

        if (!$id) {
            $this->apiReturn(204, [], '活动id参数缺失');
        }

        $groupBiz = new GroupBiz();
        $result = $groupBiz->getActivityInfo($this->_sid,$id,true);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }

    /**
     * 获取团长的名称
     * author  leafzl
     * Date: 2018-05=23
     */
    public function getColonel()
    {
        $id     = I('id');
        $keyword = I('keyword');

        $groupBiz = new GroupBiz();
        $result = $groupBiz->getColonel($this->_sid, $id, $keyword);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }

    /**
     * 获取参与情况列表
     * author  leafzl
     * Date: 2018-05-23
     */

    public function getJoinConditionList()
    {
        $id = I('id');
        $begin = I('begin');
        $end = I('end');
        $page = I('page');
        $size = I('size');
        $mid = I('mid');

        $groupBiz = new GroupBiz();
        $result = $groupBiz->getJoinConditionList($this->_sid, $id, $begin, $end, $page, $size, $mid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }

    /**
     * 获取参与情况的统计
     * author  leafzl
     * Date: 2018-05-23
     */

    public function getJoinConditionStatis()
    {
        $id = I('id');
        $begin = I('begin');
        $end = I('end');
        $mid = I('mid');

        $groupBiz = new GroupBiz();
        $result = $groupBiz->getJoinConditionStatis($this->_sid, $id, $begin, $end, $mid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }

    /**
     * 获取凑团列表
     * author  leafzl
     * Date: 2018-05-23
     */

    public function getCollectConditionList()
    {
        $id = I('id');
        $open_id = I('open_id');
        $page = I('page',1);
        $size = I('size',10);

        $groupBiz = new GroupBiz();
        $result = $groupBiz->getCollectConditionList($this->_sid, $id, $open_id,$page,$size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }
}