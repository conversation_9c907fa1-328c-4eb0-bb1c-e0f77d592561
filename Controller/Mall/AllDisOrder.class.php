<?php
/**
 * 全民营销订单相关接口
 *
 * User: xujy
 * Date: 2020/3/24
 */

namespace Controller\Mall;

use Library\Controller;

class AllDisOrder extends Controller
{

    /**
     * @var $sid 供应商id
     */
    private $_sid = null;

    public function __construct()
    {
        $this->_sid = $this->isLogin('ajax');
    }

    public function getSummaryOrderList()
    {
        $orderId      = I('orderId', '', 'strval');
        $issueType    = I('issueType', '-1', 'strval');
        $beginTime    = I('beginTime', '', 'strval');
        $endTime      = I('endTime', '', 'strval');
        $parentRoleId = I('parentRoleId', 0, 'intval');
        $orderStatus = I('orderStatus', -1, 'intval');
        $isTaxWithholding = I('isTaxWithholding', -1, 'intval');
        $invoicingStatus = I('invoicingStatus', -1, 'intval');
        $allDisBiz    = new \Business\Cooperator\AllDis\AllDisOrder();
        $config       = $allDisBiz->getSummaryOrderList($this->_sid, $orderId, $issueType, $beginTime, $endTime,
            $parentRoleId, $orderStatus, $isTaxWithholding, $invoicingStatus);
        //$config    = $allDisBiz->getAllDisRoleList($this->_sid, '', '测试发展佣金');

        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }

    public function getSummaryOrderListForPage()
    {
        $nickName  = I('nickName', '', 'strval');
        $mobile    = I('mobile', '', 'strval');
        $pageSize  = I('size', '30', 'strval');
        $page      = I('page', '1', 'strval');
        $beginTime = I('beginTime', '', 'strval');
        $endTime   = I('endTime', '', 'strval');
        $allDisBiz = new \Business\Cooperator\AllDis\AllDisOrder();
        $config    = $allDisBiz->getAllDisOrderSummaryList($this->_sid, $nickName, $mobile, $beginTime,
            $endTime, $page, $pageSize);
        //$config    = $allDisBiz->getAllDisRoleList($this->_sid, '', '测试发展佣金');

        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }
}
