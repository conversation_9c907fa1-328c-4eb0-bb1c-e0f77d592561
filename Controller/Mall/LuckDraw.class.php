<?php
/**
 * 红包抽奖
 * Created by PhpStorm.
 * User: 杨建辉
 * Date: 2019/12/16
 * Time: 16:21
 */

namespace Controller\Mall;

use Library\Controller;

class LuckDraw extends Controller
{

    private   $_sid;
    protected $_luckDrawBiz = null;

    public function __construct()
    {
        $this->_sid = $this->isLogin('ajax');
    }

    /**
     *  红包业务模型
     * <AUTHOR>
     * @date 2020/6/20
     * @return \Business\Cooperator\LuckDraw\LuckDraw;
     */
    protected function _getLuckDrawBiz()
    {
        if (!$this->_luckDrawBiz) {
            $this->_luckDrawBiz = new \Business\Cooperator\LuckDraw\LuckDraw();
        }

        return $this->_luckDrawBiz;
    }

    /**
     *  获取活动列表
     * <AUTHOR>
     * @date 2020/6/20
     * @return array
     */
    public function getLuckDrawList()
    {
        $keyWord = I('post.keyWord', '', 'strval');
        //活动状态筛选      0：全部  1：进行中  2：未开始  3：已下架   4：已过期
        $type     = I('post.type', 0, 'intval');
        $pageNum  = I('post.pageNum', 1, 'intval');
        $pageSize = I('post.pageSize', 10, 'intval');
        $sid      = $this->_sid;

        if (!$sid || !$pageNum || !$pageSize) {
            $this->apiReturn(203, [], "参数缺失");
        }

        $luckDrawBz = $this->_getLuckDrawBiz();
        $res        = $luckDrawBz->getLuckDrawList($sid, $type, $keyWord, $pageNum, $pageSize);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /***
     *  获取活动详情
     * <AUTHOR>
     * @date 2020/6/20
     * @return array
     */
    public function getLuckDrawDetail()
    {
        $activeId = I('post.activeId', 0, 'intval'); //活动id
        $sid      = $this->_sid;

        if (!$activeId) {
            $this->apiReturn(203, [], "参数错误");
        }

        $luckDrawBz = $this->_getLuckDrawBiz();
        $res        = $luckDrawBz->getLuckDrawDetail($sid, $activeId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     *  更新/创建活动
     * <AUTHOR>
     * @date 2020/6/20
     * @return array
     */
    public function createOrUpdateLuckDrawActive()
    {
        $id               = I('post.id', 0, 'intval'); //活动id（编辑的时候存在则传）
        $activeName       = I('post.activeName', '', 'strval'); //活动名称
        $beginTime        = I('post.beginTime', '', 'strval'); //活动开始时间
        $endTime          = I('post.endTime', '', 'strval'); //活动结束时间
        $activeRemark     = I('post.activeRemark', '', 'strval'); //活动说明
        $applyUserType    = I('post.applyUserType', 1, 'intval'); //适合参与人员   1：所有用户  2：新用户 3：老用户
        $activeRule       = I('post.activeRule'); //详细规则
        $activeEntrance   = I('post.activeEntrance', '', 'strval'); //商城入口       1:首页弹窗 2:下单成功 3:首页浮窗
        $promptingContent = I('post.promptingContent', '', 'strval'); //未中奖提示语
        $prizeInfo        = I('post.prizeInfo'); //奖品信息数组
        $activityType     = I('post.activityType', 0, 'intval'); //0抽奖红包1派送红包
        $activityId       = I('activityId', 0, 'intval'); //冲突活动id
        $entranceType     = I('entranceType', 0, 'intval');//活动入口,1:首页弹窗 2:下单成功 3:首页浮窗
        $activityStatus   = I('activeStatus', 0, 'intval');//活动上架状态  0：未上架  1：已上架 2：已删除
        if (empty($activeName) || empty($beginTime) || empty($endTime) || empty($applyUserType) || empty($activeRule) || empty($prizeInfo)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $sid = $this->_sid;

        $winPercent = 0; //总中间概率
        $prizeNum   = 0; //总奖品数
        foreach ($prizeInfo as &$value) {
            if (empty($value['coupon_id'])) {
                $this->apiReturn(203, [], "请选择奖项");
            }
            $value['activity_type'] = $activityType;
            $winPercent             += $value['win_prize_percent'];
            $prizeNum               += $value['prize_num'];
        }
        if (!$activityType && $winPercent > 100) {
            $this->apiReturn(400, [], "中奖概率不得超过100%");
        }

        $activeData = [
            'sid'               => $sid,
            'active_name'       => $activeName,
            'begin_time'        => strtotime($beginTime),
            'end_time'          => strtotime($endTime),
            'prize_num'         => $prizeNum,
            'active_remark'     => $activeRemark,
            'apply_user_type'   => $applyUserType,
            'active_rule'       => json_encode($activeRule),
            'active_entrance'   => $activeEntrance,
            'win_prize_percent' => $winPercent,
            'prompting_content' => $promptingContent,
            'activity_type'     => $activityType,
            'activityId'        => $activityId,
            'entranceType'      => $entranceType,
            'active_status'     => $activityStatus,
        ];

        $luckDrawBz = $this->_getLuckDrawBiz();
        $res        = $luckDrawBz->createOrUpdateLuckDrawActive($id, $sid, $activeData, $prizeInfo);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     *  删除活动
     * <AUTHOR>
     * @date 2020/6/20
     * @return array
     */
    public function deleteLuckDrawActive()
    {
        //活动id
        $activeId = I('post.activeId', 0, 'intval');
        $sid      = $this->_sid;

        if (!$activeId || !$sid) {
            $this->apiReturn(203, [], "参数错误");
        }

        $luckDrawBz = $this->_getLuckDrawBiz();
        $res        = $luckDrawBz->deleteLuckDrawActive($sid, $activeId);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     *  删除单个奖项
     * <AUTHOR>
     * @date 2020/6/20
     * @return array
     */
    public function deleteLuckDrawPrize()
    {
        //奖项id
        $id       = I('post.id', 0, 'intval');
        $activeId = I('post.activeId', 0, 'intval');
        $sid      = $this->_sid;

        if (!$id || !$sid) {
            $this->apiReturn(203, [], "参数错误");
        }

        $luckDrawBz = $this->_getLuckDrawBiz();
        $res        = $luckDrawBz->deleteLuckDrawPrize($sid, $activeId, $id);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     *  活动上下架
     * <AUTHOR>
     * @date 2020/6/20
     * @return array
     */
    public function upOrDownLuckDraw()
    {
        //活动上下架  0：下架  1：上架
        $activeStatus = I('post.activeStatus', 0, 'intval');
        //活动id
        $activeId = I('post.activeId', 0, 'intval');
        $sid      = $this->_sid;

        if (!$activeId || !$sid) {
            $this->apiReturn(203, [], "参数错误");
        }

        $luckDrawBz = $this->_getLuckDrawBiz();
        $res        = $luckDrawBz->upOrDownLuckDraw($sid, $activeId, $activeStatus);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     *  获取抽奖记录
     * <AUTHOR>
     * @date 2020/6/20
     * @return array
     */
    public function getLuckDrawLogList()
    {
        $activeId = I('post.activeId', 0, 'intval');
        $pageNum  = I('post.pageNum', 1, 'intval');
        $pageSize = I('post.pageSize', 10, 'intval');
        //中奖状态 2:全部  1:中奖  0未中奖
        $status = I('post.status', 0, 'intval');
        $sid    = $this->_sid;

        if (!$activeId || !$pageNum || !$pageSize || !$sid) {
            $this->apiReturn(203, [], "参数错误");
        }

        $luckDrawBz = $this->_getLuckDrawBiz();
        $res        = $luckDrawBz->getLuckDrawLogList($sid, $activeId, $status, $pageNum, $pageSize);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     *  获取商家微信公众平台授权信息
     * <AUTHOR>
     * @date 2020/6/29
     * @return array
     */
    public function getSupplyInfo(){
        $sid    = $this->_sid;

        if (!$sid) {
            $this->apiReturn(203, [], "参数错误");
        }

        $luckDrawBz = new \Business\Cooperator\Account\WxOpen();
        $res        = $luckDrawBz->getSupplyInfo($sid);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}
