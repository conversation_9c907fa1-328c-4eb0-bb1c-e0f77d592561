<?php
/**
 * Created by PhpStorm.
 * User: zhangyangzhen
 * Date: 2018/5/29
 * Time: 15:08
 */

namespace Controller\Mall;

use Business\AppCenter\Payment;
use Business\Finance\AccountMoney;
use Business\Finance\Withdraw;
use Business\JavaApi\TicketApi;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\Order\OrderQueryJavaService;
use Business\Order\OrderQueryMove;
use Business\Order\Query;
use Library\Business\WechatSmallApp;
use Library\Cache\Cache;
use Library\Container;
use Library\Exception;
use Library\MessageNotify\MessageException;
use Library\Tools\Helpers;
use Library\Tools\PftException;
use Library\Util\EnvUtil;
use Model\Finance\Banks;
use Model\Member\MemberRelationship;
use Model\Order\OrderCommon;
use Model\Order\OrderQuery;
use Model\Order\SubOrderQuery;
use Model\Order\OrderRefer;
use Model\Order\OrderTools;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\Report\Statistics;
use Model\AppCenter\ModulePayment;
use Model\AppCenter\ModuleDetail;
use Business\Product\Product as ProductBiz;
use Business\Product\HandleTicket as TicketBiz;
use Process\Finance\TradeRecord as TradeProcess;
use Process\Resource\Qiniu;
use Qiniu\Auth;
use Qiniu\Storage\UploadManager;
use Business\Member\Member as MemberBiz;
use Business\Member\RegisterAction;
use Business\Member\Login as LoginBiz;
use Library\Constants\MemberConst;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Business\CaptchaCode;

class MiniCloud extends Mall
{
    private $pftMemberModel;       //Model\Member\Member
    private $tradeModel;           //Model\Finance\TradeRecord
    private $banksModel;           //Model\Finance\Banks
    private $_landModel;           //Model\Product\Land
    private $_ticketModel;
    private $_orderModel;
    private $_orderReferModel;
    private $_businessModel;

    private $memberId;  //账号id
    private $dtype;     //账号类型
    private $loginInfo; //登录账号信息

    private $prefix   = 'mobile:minicloud:';
    private $_isGroup = 0; //是否是集团账户

    private $_minMoney = 1000; // 提现最少金额(分)

    private $_orderModeConf;//渠道配置信息
    private $_orderModeKey;//渠道KEY

    //账户余额支付日志
    const PAY_ONLINE_PLA = '/appcenter/pay_online_pla';
    //锁日志
    const LOKEY_LOG = '/appcenter/lock_log';
    // 免费套餐配置
    private $freePackageConf = [
        'price_id'  => 1,   //资费ID
        'module_id' => 62,  //套餐ID，三年免费供应商套餐（MINI云和微票房）
        'pay_type'  => 2,   //1=模块支付，2=套餐支付
        'is_update' => 0,
    ];

    //最大文件大小 2m
    const __MAX_FILE_SIZE__ = 1048576;

    public function __construct()
    {
        $loginInfo       = $this->_getLoginInfo();
        $this->loginInfo = $loginInfo;
        $this->memberId  = isset($loginInfo['id']) ? $loginInfo['id'] : 0;
        $this->dtype     = isset($loginInfo['dtype']) ? $loginInfo['dtype'] : 0;

        //渠道配置信息
        $tmpOrderModeConf = load_config('order_mode_two', 'orderSearch');

        foreach ($tmpOrderModeConf as $key => $item) {
            if (is_array($item['key'])) {
                foreach ($item['key'] as $v) {
                    $this->_orderModeConf[$v] = $item['name'];
                }
            } else {
                $this->_orderModeConf[$item['key']] = $item['name'];
            }
            $this->_orderModeKey[$key] = $item['key'];
        }
    }

    /**
     * 判断是否为管理员账号
     * Create by zhangyangzhen
     * Date: 2018/6/1
     * Time: 10:57
     * @return bool
     */
    private function isAdmin()
    {
        if ($this->memberId && $this->memberId == 1) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 登录授权验证
     * Create by zhangyangzhen
     * Date: 2018/6/1
     * Time: 10:59
     * @return bool|void
     */
    private function _auth()
    {
        $sessionKey = $_SERVER['HTTP_SESSION_KEY'];
        $smallLib   = new \Library\Business\WechatSmallApp;
        $res        = $smallLib->getSession($sessionKey, null);
        if (!$res) {
            $this->apiReturn(202, [], '请先登录');
        } else {
            $smallLib->setSession($sessionKey, $res);
        }
    }

    /**
     * 获取登录信息
     * Create by zhangyangzhen
     * Date: 2018/6/1
     * Time: 10:54
     * @return array|bool|string
     */
    private function _getLoginInfo()
    {
        $sessionKey = $_SERVER['HTTP_SESSION_KEY'];
        if (empty($sessionKey)) {
            return false;
        }
        $smallLib = new \Library\Business\WechatSmallApp;
        $res      = $smallLib->getSession($sessionKey, null);

        return !empty($res) ? $res : [];
    }

    /**
     * 获取用户模型
     * Create by zhangyangzhen
     * Date: 2018/6/1
     * Time: 9:16
     * @return \Model\Member\Member
     */
    private function _getPftMemberModel()
    {
        if (empty($this->pftMemberModel)) {
            $this->pftMemberModel = new \Model\Member\Member();
        }

        return $this->pftMemberModel;
    }

    /**
     * 获取订单模型
     *
     * <AUTHOR>
     * @date   2018-06-05
     */
    private function _getOrderModel()
    {
        if (empty($this->_orderModel)) {
            $this->_orderModel = new OrderCommon();
        }

        return $this->_orderModel;
    }

    private function _getBusinessModel()
    {
        if (empty($this->_businessModel)) {
            $this->_businessModel = new \Business\Mall\SmallAppConfig();
        }

        return $this->_businessModel;
    }

    /**
     * 获取orderQuery模型
     *
     * <AUTHOR>
     * @date   2018-06-05
     */
    private function _getOrderReferModel()
    {
        if (empty($this->_orderReferModel)) {
            $this->_orderReferModel = new OrderRefer();
        }

        return $this->_orderReferModel;
    }

    /**
     * 获取票类模型
     *
     * <AUTHOR>
     * @date   2018-06-07
     */
    private function _getTicketModel()
    {
        if (empty($this->_ticketModel)) {
            $this->_ticketModel = new Ticket();
        }

        return $this->_ticketModel;
    }

    /**
     * 获取景区模型
     *
     * <AUTHOR>
     * @date    2018-06-07
     */
    private function _getLandModel()
    {
        if (empty($this->_landModel)) {
            $this->_landModel = new Land();
        }

        return $this->_landModel;
    }

    /**
     * 获取交易记录模型
     * Create by zhangyangzhen
     * Date: 2018/6/1
     * Time: 14:29
     *
     * @param  string  $defaultDb
     * @param  bool  $startTime
     * @param  bool  $endTime
     *
     * @return \Model\Finance\TradeRecord
     */
    private function _getTradeModel($defaultDb = 'slave', $startTime = false, $endTime = false)
    {
        if (null === $this->tradeModel) {
            $this->tradeModel = new \Model\Finance\TradeRecord($defaultDb, $startTime, $endTime);
            //设置集团账号标识
            if ($this->_isGroup) {
                $this->tradeModel->setGroupIdentify($this->memberId);
            }
        }

        return $this->tradeModel;
    }

    /**
     * 获取Model\Finance\Banks模型
     * Create by zhangyangzhen
     * Date: 2018/6/4
     * Time: 10:25
     * @return Banks
     */
    private function _getBanksModel()
    {
        if (empty($this->banksModel)) {
            $this->banksModel = new Banks();
        }

        return $this->banksModel;
    }

    /**
     * 发送注册手机验证码
     * Create by zhangyangzhen
     * Date: 2018/5/29
     * Time: 15:21
     */
    public function sendVCode()
    {
        $mobile = I('post.mobile', '');
        //手机号码验证
        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(406, [], '请输入正确的手机号码');
        }

        //判断手机号是否被列入黑名单
        $blackList   = load_config('black_list');
        $blackMobile = $blackList['mobile'];
        if (in_array($mobile, $blackMobile)) {
            $this->apiReturn(403, [], '该手机号已经被加入黑名单。');
        }

        $cacheRedis = Cache::getInstance('redis');
        $cacheKey   = $this->prefix . $mobile;
        $send_time  = $cacheRedis->get($cacheKey, '', true);
        if ($send_time > 10) {
            $this->apiReturn(403, [], '该手机号发送次数超出系统限制。');
        }

        if (Helpers::getVerifyCode($mobile)) {
            $this->apiReturn(403, [], '发送间隔太短！请在60秒后再重试。');
        }

        //发送短信
        try {
            $code   = Helpers::setVerifyCode($mobile, 60);
            $messageServiceApi = Container::pull(MessageService::class);
            [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'register_code', $mobile, [$code, '2']);
            if ($approval) {
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['发送注册手机验证码', __METHOD__, [$mobile, [$code, '2']], $res], JSON_UNESCAPED_UNICODE));
                }
            } else {
                /** @deprecated 放量结束后删除 */
                $smsLib = SmsFactory::getFactory($mobile);
                $res = $smsLib->registerCode($code);
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['发送注册手机验证码.old', __METHOD__, [$mobile, [$code, '2']], $res], JSON_UNESCAPED_UNICODE));
                }
            }
            if ($res['code'] == 200) {
                $cacheRedis->incrBy($cacheKey);
                $this->apiReturn(200, [], '发送验证码成功');
            } else {
                $this->apiReturn($res['code'], [], $res['msg']);
            }
        } catch (MessageException $e) {
            $this->apiReturn(500, [], '对不起，短信服务器发生故障，造成的不便我们感到十分抱歉。请联系我们客服人员。');
        }
    }

    /**
     * 注册验证码校验
     * Create by zhangyangzhen
     * Date: 2018/5/30
     * Time: 15:36
     */
    public function checkVCode()
    {
        $mobile = I('mobile', '');
        $code   = I('code', '');
        if (empty($code) || empty($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, '', '参数有误');
        }

        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入正确的手机号码');
        }

        //校验注册验证码
        $res = Helpers::ChkCode($mobile, $code);
        if ($res !== true) {
            $this->apiReturn(self::CODE_PARAM_ERROR, '', '验证码错误');
        }
        $this->apiReturn(self::CODE_SUCCESS, '', 'success');
    }

    /**
     * 注册
     * Create by zhangyangzhen
     * Date: 2018/5/31
     * Time: 9:47
     */
    public function register()
    {
        $mobile   = I('mobile', '');
        $password = I('password', '');
        $code     = I('code', '');
        if (empty($mobile) || empty($password) || empty($code)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数有误');
        }

        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入正确的手机号码');
        }

        //校验注册验证码
        $codeRes = Helpers::ChkCode($mobile, $code);
        if ($codeRes !== true) {
            $this->apiReturn(self::CODE_PARAM_ERROR, '', '验证码错误');
        }

        $memberBiz = new MemberBiz();
//        //客户是否存在
        $customerId = $memberBiz->parseCustomerIdByMobile($mobile);
        if ($customerId) {
            //角色是否存在
            $memberModel = new \Model\Member\Member();
            $role        = $memberModel->getTheRoleInfo($customerId, MemberConst::ROLE_APPLY, 'id');
            if ($role) {
                $this->apiReturn(self::CODE_NO_CONTENT, [], '该手机号已存在');
            }
        }

        //注册供应商角色
        $request = [
            'name'        => 'Mini' . $mobile,
            'passwd'      => $password,
            'type'        => MemberConst::ROLE_APPLY,
            'identifier'  => $mobile,
            // 'account'       => 'Mini' . $mobile,
            'customer_id' => $customerId,
            'info_source' => MemberConst::INFO_MOBILE,
            'page_source' => MemberConst::PAGE_MINI_SMALLAPP,
        ];

        $registerAction = new RegisterAction();
        $result         = $registerAction->register((object)$request);

        $data = $result['data'];

        if ($result['code'] == 200) {
            //为Mini云注册用户免费开通一个套餐
            $res = $this->buyFreePackage($data['account']);

            if ($res[0] == 200) {
                $this->apiReturn(self::CODE_SUCCESS, $data, '注册账号成功');
            } else {
                pft_log(self::PAY_ONLINE_PLA,
                    "开通结果：用户账号：{$data['account']}, 模块ID：{$this->freePackageConf['module_id']}开通失败, 失败原因:{$res[1]}");
                $this->apiReturn(201, [], '套餐开通失败，请联系客服人员！');
            }
        } else {
            $this->apiReturn(self::CODE_NO_CONTENT, [], '注册账号失败');
        }
    }

    /**
     * 为MINI云注册用户开通一个供应商套餐，只包含MINI云和微票房等最简供应商相关功能
     * Create by zhangyangzhen
     * Date: 2018/12/10
     * Time: 17:57
     *
     * @param $account
     *
     * @return array
     */
    private function buyFreePackage($account)
    {
        if (!$account) {
            return [201, '未找到该用户'];
        }

        $code = 200;
        $msg  = '';
        $data = [];

        $memberModel = new \Model\Member\Member();
        $memberInfo  = $memberModel->getInfoByAccount($account, 'id, dtype, account');

        $cache    = \Library\Cache\Cache::getInstance('redis');
        $dtype    = $memberInfo['dtype'];
        $memberId = $memberInfo['id'];
        if (!in_array($dtype, [0, 1])) {
            return [201, '账号类型错误'];
        }

        //加锁 防止恶意请求
        $locky    = "payInPlatform:" . $memberId . ':' . implode(':', $this->freePackageConf);
        $lock_ret = $cache->lock($locky, 1, 120);

        if (!$lock_ret) {
            pft_log(self::LOKEY_LOG, "[$locky]操作频繁");

            return [201, "请求正在处理中，请稍后"];
        };

        $packageBiz = new \Business\AppCenter\Package();

        $packageId = $this->freePackageConf['module_id'];
        $openRes   = $packageBiz->openFreeGivePackage($memberId, $packageId, $dtype);

        $orderNo   = $openRes['data']['order_no'] ?? '';

        $data['order_no'] = $orderNo;

        if (!empty($locky)) {
            $cache->rm($locky);
        }

        if ($openRes['code'] != 200) {
            return [201, $openRes['msg']];
        }

        //开通结果 日志
        if ($code == 200) {
            $result = '成功';
        } else {
            $result = '失败';
        }

        pft_log(self::PAY_ONLINE_PLA,
            "开通结果：用户ID：{$memberId}, 模块ID：{$this->freePackageConf['module_id']}, 资费ID：{$this->freePackageConf['price_id']}, 流水号：{$orderNo}, 费用：{$fee}|{$result}, 原因:{$msg}");

        return [$code, $msg, $data];
    }

    /**
     * 判断手机号是否在平台注册
     * Create by zhangyangzhen
     * Date: 2018/6/29
     * Time: 14:15
     */
    public function isExistMobile()
    {
        $mobile = I('mobile', '');

        if (empty($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '手机号不能为空');
        }

        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入正确的手机号码');
        }

        $model = $this->_getPftMemberModel();
        $res   = $model->getInfoByAccount($mobile);
        if ($res) {
            $this->apiReturn(self::CODE_NO_CONTENT, [], '已注册');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, [], '未注册');
        }
    }

    /**
     * 重置密码
     * Create by zhangyangzhen
     * Date: 2018/5/31
     * Time: 16:32
     */
    public function forgetPwd()
    {
        $mobile   = I('mobile', '');
        $password = I('password', '');
        $code     = I('code', '');

        if (empty($mobile) || empty($password) || empty($code)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '手机号/密码/验证码不能为空');
        }

        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入正确的手机号码');
        }

        //校验注册验证码
        $codeRes = Helpers::ChkCode($mobile, $code);
        if ($codeRes !== true) {
            $this->apiReturn(self::CODE_PARAM_ERROR, '', '验证码错误');
        }

        //判断该手机号是否注册
        $model   = $this->_getPftMemberModel();
        $isExist = $model->getInfoByAccount($mobile);
        if (!$isExist) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '该手机号还未注册');
        } else {
            $fid = $isExist['id'];
        }

        //重置密码
        //<AUTHOR> | @date 2018/10/22 | 会员表更新模块统一
        $Member = new \Business\Member\Member();
        $res    = $Member->updateMemberPassword($fid, $password);
        if ($res) {
            $this->apiReturn(self::CODE_SUCCESS, [], '重置密码成功');
        } else {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '重置密码失败');
        }
    }

    /**
     * 登录
     * Create by zhangyangzhen
     * Date: 2018/5/31
     * Time: 16:00
     */
    public function login()
    {
        $mobile   = I('post.mobile', '');
        $password = I('post.password', '');
        $roleType = I('post.role_type', false);
        $code     = I('post.auth_code', false);

        if (empty($mobile) || empty($password)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '手机号或密码不能为空');
        }

        //小程序这边的的图片验证码要特殊处理
        if ($code) {
            $code = $code . CaptchaCode::$_identifierMark . $mobile;
        }

        $loginBiz = new LoginBiz();
        //mini云的密码在前端已经做了一次md5加密
        $res = $loginBiz->loginByPasswd($mobile, md5($password), MemberConst::PAGE_MINI_SMALLAPP, $roleType, $code);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }

        $data = $res['data'];

        $pftMemberModel = $this->_getPftMemberModel();
        $memInfo        = $pftMemberModel->getMemberInfo($data['memberID'], 'id', 'headphoto,mobile,customer_id');
        //用户二期 - 信息获取修改 - 2
        $CustomerBus      = new \Business\Member\Customer();
        $customerInfo     = $CustomerBus->getCustomerInfo($memInfo['customer_id']);
        $memInfo["cname"] = $customerInfo["cname"];

        $info = [
            'id'        => $data['memberID'],
            'sid'       => $data['sid'],
            'account'   => $data['account'],
            'cname'     => $memInfo['cname'],
            'dname'     => $data['dname'],
            'dtype'     => $data['dtype'],
            'headphoto' => $memInfo['headphoto'],
            'mobile'    => $memInfo['mobile'],
        ];

        $smallLib = new \Library\Business\WechatSmallApp;
        //保存登录信息
        $sessionkey = $smallLib->session_key();
        $smallLib->setSession($sessionkey, $info);
        $scenCode = $smallLib->encodeShopCode($info['id']);//小程序码

        $returnData = [
            'scenCode'   => $scenCode,
            'sessionkey' => $sessionkey,
            'info'       => $info,
        ];

        $this->apiReturn($res['code'], $returnData, $res['msg']);
    }

    /**
     * 交易记录
     * Create by zhangyangzhen
     * Date: 2018/6/1
     * Time: 10:59
     */
    public function tradeRecord()
    {
        $this->_auth();

        try {
            $map        = [];
            $fid        = $this->memberId;
            $partner_id = 0;

            $startTime = I('begin_time', '');
            $endTime   = I('end_time', '');
            if (empty($startTime) || empty($endTime)) {
                //默认起止时间为本月
                $startTime = mktime(0, 0, 0, date('m'), 1, date('Y'));
                $startTime = date('Y-m-d H:i:s', $startTime);
                $endTime   = date('Y-m-d H:i:s', time());
            }
            $interval = [$startTime, $endTime];

            //判断起始时间是不是在可查询的段内
            $preTmp = TradeProcess::getDataPosition($startTime, $endTime);
            $code   = $preTmp['code'];
            if ($code == 0) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], $preTmp['msg']);
            }
            $map['rectime'] = array('between', $interval);

            //0购买产品 1修改/取消订单 6提现冻结 12取消提现 13拒绝提现 17撤销/撤改订单
            $subtype = [0, 1, 6, 9, 12, 13, 17];

            //0全部 1收入 2支出
            $dtype = I('dtype', 0);
            if ($dtype == 1) {
                $subtype        = [0, 9, 12, 13];
                $map['daction'] = 0;
            } elseif ($dtype == 2) {
                $subtype        = [1, 6, 9, 17];
                $map['daction'] = 1;
            }
            $map['dtype'] = ['in', $subtype];

            //订单号
            $orderid = \safe_str(I('orderid'));
            if ($orderid) {
                $map['orderid'] = $orderid;
                if (!$_REQUEST['btime'] && !$_REQUEST['etime']) {
                    unset($map['rectime']);
                    $interval = ['', ''];
                }

                //如果是通过订单号查询，如果查询得到订单信息，就使用订单的下单当天作为查询时间条件，否则使用今天作为查询条件
                $orderModel = new OrderTools('slave');
                $orderInfo  = $orderModel->getOrderInfo($orderid, 'ordertime');
                if ($orderInfo) {
                    $orderTimestamp = strtotime($orderInfo['ordertime']);
                    if ($orderTimestamp) {
                        $startTime = date('Y-m-d 00:00:00', $orderTimestamp);
                        $endTime   = date('Y-m-d 23:59:59', $orderTimestamp);
                    }
                }
            }

            //支付方式,ptypes = 100为全部
            $tmpMap = TradeProcess::parseAccountType($fid, $partner_id, 100);
            if ($tmpMap) {
                $map = array_merge($map, $tmpMap);
            }

            //交易金额为0的交易记录不显示
            $map['dmoney'] = ['gt', 0];

            //分页
            $page  = I('page', 1, 'intval');
            $limit = I('limit', 15, 'intval');

            $recordModel = $this->_getTradeModel('slave', $startTime, $endTime);
            $this->_output($recordModel, $map, $page, $limit, $interval, $fid, $partner_id);

        } catch (Exception $e) {
            \pft_log('trade_record/err', 'get_list|' . $e->getCode() . "|" . $e->getMessage(), 'month');
            $this->apiReturn($e->getCode(), [], $e->getMessage());
        }
    }

    /**
     * 根据传入的查询条件等输出结果
     * Create by zhangyangzhen
     * Date: 2018/6/1
     * Time: 14:47
     *
     * @param $recordModel      交易记录模型
     * @param $map              查询条件
     * @param $page             当前页数
     * @param $limit            每页行数
     * @param $interval         起止时间段   [开始时间, 结束时间]
     * @param $fid              会员id
     * @param $partner_id       对方商户id
     *
     * @throws Exception
     */
    private function _output($recordModel, $map, $page, $limit, $interval, $fid, $partner_id)
    {
        $data = $recordModel->getList($map, $page, $limit, $fid, $partner_id);

        $totalIncome  = 0;
        $totalExpense = 0;
        //列表数据处理
        if (!empty($data['list'])) {
            $list = $data['list'];
            foreach ($list as &$item) {
                if ($item['daction'] == 1) {
                    $totalExpense += $item['dmoney'];
                } else {
                    $totalIncome += $item['dmoney'];
                }
                TradeProcess::handleData($item);
                TradeProcess::getTradeNameAndAccount($item);
                $item['counter'] = substr($item['counter'], 0, strpos($item['counter'], '<br />'));
                $item['rectime'] = date('Y-m-d H:i', strtotime($item['rectime']));
            }
        } else {
            $list = [];
        }

        $res = [
            'total'        => $data['total'],
            'page'         => $page,
            'total_page'   => ceil($data['total'] / $limit),
            'limit'        => $limit,
            'list'         => $list,
            'btime'        => $interval[0],
            'etime'        => $interval[1],
            'totalIncome'  => $totalIncome,
            'totalExpense' => $totalExpense,
        ];

        $this->apiReturn(200, $res);
    }

    /**
     * 订单查询
     * Create by zhangyangzhen
     * Date: 2018/6/7
     * Time: 18:25
     */
    public function orderQuery()
    {
        $this->_auth();

        //订单状态
        $status = I('status', -1);
        //开始时间
        $beginDate = I('begin_date', '');
        //结束时间
        $endDate = I('end_date', '');
        //时间类型
        $dateType = I('date_type', 0, 'intval');
        //精确搜索关键字
        $exact = I('exact', '');
        //精确搜索类型
        $exactType = I('exact_type', '');
        //当前页数
        $page = I('page', 1, 'intval');
        //获取条数
        $size = I('size', 15, 'intval');
        //供应商/分销商搜索
        $memId = I('mem_id', 0, 'intval');
        //分销商|供应商
        $memType = I('mem_type', 0, 'intval');

        if ($size > 100) {
            $this->apiReturn(204, [], '获取条数超出范围');
        }

        //起止时间默认为本月
        if (empty($beginDate) || empty($endDate)) {
            $startTime = mktime(0, 0, 0, date('m'), 1, date('Y'));
            $beginDate = date('Y-m-d H:i:s', $startTime);
            $endDate   = date('Y-m-d H:i:s', time());
        }

        $result = $this->_searchOnlyByDate($this->memberId, $beginDate, $endDate, $dateType, $status, $memId, $memType, $page, $size);
        //处理渠道
        $list = $this->_dealData($result['data']);

        if ($result['code'] && $result['code'] == 200) {
            $data = array_values($list);
            $this->apiReturn(200, $data);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 获取订单详细
     * Create by zhangyangzhen
     * Date: 2018/6/15
     * Time: 10:30
     */
    public function orderDetail()
    {
        $this->_auth();

        //主订单号
        $ordernum = I('ordernum', '');

        if (!$ordernum) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单号缺失');
        }

        $queryBiz  = new Query();
        $ticketBiz = new \Business\CommodityCenter\Ticket();

        $result       = $queryBiz->getOrderDetail($this->memberId, $ordernum);
        $queryParams  = [[$ordernum]];
        $queryRes     = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo',
            'queryOrderTouristInfoByOrderId', $queryParams);
        $visitor_info = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $visitor_info = $queryRes['data'];
        }
        //$visitor_info = (new OrderQuery())->getTouristsInfoNoMobile($ordernum);
        //处理一票一证游客信息
        $visitor_info = $this->dealVisitorInfo($visitor_info);

        // if (count($visitor_info) > 1) {
        $result['data']['visitor_info'] = $visitor_info;
        // }

        //计算门票总数，和门票有效期以及退款规则
        $ticketApi = new TicketApi();

        foreach ($result['data']['ticket_list'] as $k => $v) {
            $ticketData = $ticketBiz->fieldConversion($ticketBiz->queryTicketInfoById($v['tid'], '', '', '', '', false,
                null, $this->memberId, 1));
            //$ticketData = $ticketApi->getTickets($v['tid'], $this->memberId, 1);
            $result['data']['ticket_list'][$k]['refund_rule'] = $ticketData['refund_rule'];

            //有效时间
            $validTime = '';
            if (strtotime($ticketData['valid_period_start']) > 0) {
                //时间段内有效
                $tmpS = date('Y-m-d', strtotime($ticketData['valid_period_start']));
                $tmpE = date('Y-m-d', strtotime($ticketData['valid_period_end']));

                if ($tmpS > date('Y-m-d')) {
                    $bookInfo['startDate'] = $tmpS;
                }
                if ($ticketData['valid_period_timecancheck']) {
                    $tmpS = '随游玩日期变化';
                }
                $validTime = $tmpS . '~' . $tmpE;
            } elseif ($ticketData['valid_period_days']) {
                //多少天内有效
                $validTime = $ticketData['valid_period_days'];
            } else {
                //当天有效
                $validTime = 0;
            }
            $result['data']['ticket_list'][$k]['validtime'] = $validTime;
        }

        if ($result && $result['code'] == 200) {
            $this->apiReturn(200, $result['data']);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }

    }

    /**
     * 处理游客信息
     * Create by zhangyangzhen
     * Date: 2018/6/15
     * Time: 10:33
     *
     * @param $list     array 游客信息
     *
     * @return mixed    int 是否显示
     */
    private function dealVisitorInfo($list)
    {
        foreach ($list as &$value) {
            if ($value['idx'] > 0 && $value['idcard']) {
                $value['is_display'] = 1;
            } else {
                $value['is_display'] = 0;

            }
            unset($value);
        }

        return $list;

    }

    /**
     * 根据时间范围查询订单
     *
     * <AUTHOR>
     * @date   2017-04-13
     *
     * @param  int  $memberId  会员id
     * @param  string  $beginDate  开始时间
     * @param  string  $endDate  截至时间
     * @param  string  $dateType  0 : 下单时间, 1 : 游玩时间 , 2 : 验证时间
     * @param  int  $status  订单状态
     * @param  int  $memId  分销商或者供应商id
     * @param  int  $memType  0:分销商|1供应商
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     *
     * @return array
     */
    private function _searchOnlyByDate($memberId, $beginDate, $endDate, $dateType, $status, $memId, $memType, $page, $size)
    {
        $this->_searchDateLimit($beginDate, $endDate);

        $queryBiz = new \Business\Order\Query();
        $return   = $queryBiz->searchOnlyByDate($memberId, $beginDate, $endDate, $dateType, $status, $memId, $memType, $page, $size);

        return $return;
    }

    /**
     * 搜索时间范围限制
     *
     * <AUTHOR>
     * @date   2017-06-19
     *
     * @param  string  $beginDate  开始日期
     * @param  string  $endDate  结束日期
     */
    private function _searchDateLimit($beginDate, $endDate)
    {
        $maxTimeDiff = 62 * 24 * 3600;
        if (strtotime($endDate) - strtotime($beginDate) > $maxTimeDiff) {
            $this->apiReturn(204, [], '最大只能查询62天的数据');
        }
    }

    /**
     * 渠道相关
     * Create by zhangyangzhen
     * Date: 2018/6/7
     * Time: 18:05
     *
     * @param $list     列表
     *
     * @return mixed
     */
    private function _dealData($list)
    {
        foreach ($list as $key => $value) {
            $list[$key]['ordermode'] = $this->_orderModeConf[$value['ordermode']];
            if ($value['buyerid'] == $value['sellerid']) {
                $list[$key]['ordermode'] = '自供自销';
            }
            if ($value['ordermode'] == 23) {
                $list[$key]['ordermode'] = $this->_orderModeConf[$value['ordermode']];
            }
        }

        return $list;
    }

    /**
     * 获取景区小程序配置
     * Create by zhangyangzhen
     * Date: 2018/6/21
     * Time: 15:29
     * @throws \Exception
     * @throws \Library\Tools\Interfaces\Exception
     */
    public function getLandConfig()
    {
        $this->_auth();

        try {
            /** @var SmallAppConfig 配置选项 */
            $model  = $this->_getBusinessModel();
            $result = $model->getData($this->memberId);
            $this->apiReturn(200, $result->toArray(), "获取成功!");
        } catch (PftException $e) {
            $this->apiReturn(500, [], $e->getMessage());
        } finally {
            $this->apiReturn(500, [], "出现错误");
        }
    }

    /**
     * 修改/保存景区小程序配置
     * Create by zhangyangzhen
     * Date: 2018/6/21
     * Time: 15:30
     * @throws \Exception
     * @throws \Library\Tools\Interfaces\Exception
     */
    public function saveLandConfig()
    {
        $this->_auth();

        try {
            $model = $this->_getBusinessModel();
            //取出该账号的配置信息
            $data = $model->getData($this->memberId);
            $data = $data->toArray();

            $image          = $data['images'];
            $image          = json_encode($image);
            $data['images'] = $image;

            $type = I('type', '');//修改内容：1=景区名称，2=景区电话，3=景区图片
            $land = I('land', '', 'htmlspecialchars_decode');

            if (empty($type) || empty($land)) {
                $this->apiReturn(self::CODE_PARAM_ERROR, '参数错误');
            }

            //微信小程序配置信息在平台上需要的参数，在mini云中新注册的账号是没有的，要设置默认值
            $data['aid']           = $this->memberId;
            $data['enable_status'] = 1;
            $data['longitude']     = 1;
            $data['latitude']      = 1;
            $data['address']       = '店铺地址';

            if (empty($data['store_name'])) {
                $data['store_name'] = '店铺名称';
            }
            if (empty($data['service_mobile'])) {
                $data['service_mobile'] = '13123456789';
            }

            if ($type == 1) {
                $data['store_name'] = $land;
            } elseif ($type == 2) {
                $data['service_mobile'] = $land;
            } elseif ($type == 3) {
                $data['images'] = $land;
            }

            /** @var SmallAppConfig 整理前端提交过来的参数 */
            $smallAppConfigData = $model->modelParameter($data);
            /** @var boolean 保存数据操作 */
            $result = $model->saveData($smallAppConfigData, $this->memberId);
            if ($result) {
                $this->apiReturn(200, [], "保存成功!");
            }
            $this->apiReturn(200, [], "保存失败!");
        } catch (PftException $e) {
            $this->apiReturn(500, [], $e->getMessage());
        } finally {
            $this->apiReturn(500, [], "出现错误");
        }
    }

    /**
     * 提现申请
     * Create by zhangyangzhen
     * Date: 2018/6/4
     * Time: 9:55
     *
     * @param $cash         申请提现金额（分）
     * @param $type_name    银行卡类型名 'alipay','bank1','bank2'
     */
    public function withdrawCash()
    {
        $this->_auth();
        $this->_checkWithdrawAuth();
        $wdMoney  = I('post.cash', 0, 'intval');
        $typeName = I('post.type_name', '');

        //获取用户禁止提现配置
        $limitApi      = new \Business\JavaApi\Member\MemberLimit();
        $withdrawLimit = $limitApi->queryFunctionLimitByFidAndLimitType($this->memberId, $limitType = 2);
        if ($withdrawLimit['code'] == 200 && !empty($withdrawLimit['data'])) {
            $this->apiReturn(204, [], '该账户处于提现禁用状态，不可操作');
        }

        // 提现操作
        $source = 3; //mini云
        $bus    = new Withdraw();
        $res    = $bus->apply($this->memberId, $wdMoney, $typeName, $cutFeeWay = 0, $this->memberId,
            $this->loginInfo['dname'], $source);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 账户可提现金额判断
     * Create by zhangyangzhen
     * Date: 2018/6/4
     * Time: 10:10
     */
    public function getValidWithdarwMoney()
    {
        $this->_auth();

        $orderStatus  = [0, 2, 7];
        $hasSaleMoney = true;

        $bus = new AccountMoney();
        $res = $bus->getValidWithdarwMoney($this->memberId, $orderStatus, $hasSaleMoney);
        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(self::CODE_SUCCESS, ['money' => $res['data']['withdraw_money']]);
    }

    /**
     * 账户余额
     * Create by zhangyangzhen
     * Date: 2018/7/31
     * Time: 11:31
     */
    public function getAccountMoney()
    {
        $this->_auth();

        $moneyBus = new AccountMoney();
        $res      = $moneyBus->getAccountMoney($this->memberId);
        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(self::CODE_SUCCESS, ['money' => $res['data']['money']]);
    }

    /**
     * 获取用户银行列表
     * Create by zhangyangzhen
     * Date: 2018/6/4
     * Time: 10:19
     */
    public function getBankList()
    {
        $this->_auth();

        $bus = new AccountMoney();
        $res = $bus->getBankList($this->memberId);
        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(self::CODE_SUCCESS, $res['data'], 'success');
    }

    /**
     * 获取已验证过的银行账户信息
     * Create by zhangyangzhen
     * Date: 2018/6/19
     * Time: 14:41
     */
    public function getBankAccount()
    {
        $bus = new AccountMoney();
        $res = $bus->getBankAccount($this->memberId);
        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(self::CODE_SUCCESS, $res['data'], 'success');
    }

    /**
     * 获取支行信息
     * Create by zhangyangzhen
     * Date: 2018/6/4
     * Time: 10:30
     */
    public function subbranchList()
    {
        $this->_auth();

        $page = I('post.page', 1);
        $size = I('post.size', 50);
        $name = I('post.name', '');

        $cityId     = I('post.city_id', 0);
        $bankId     = I('post.bank_id', 0);
        $provinceId = I('post.province_id', 0);

        if (!$cityId || !$bankId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $model = $this->_getBanksModel();
        $res   = $model->getSubbranch($cityId, $bankId, $provinceId, $name, $page, $size);

        if ($res === false) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, $res);
        }
    }

    /**
     * 获取银行列表
     * Create by zhangyangzhen
     * Date: 2018/6/4
     * Time: 10:29
     */
    public function bankList()
    {
        $this->_auth();

        $page = I('post.page', 1);
        $size = I('post.size', 700);

        $model = $this->_getBanksModel();

        //银行列表
        $list = $model->getBanks($page, $size);

        //省份列表
        $province = $model->getBankProvince();

        if ($list === false) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, ['list' => $list, 'province' => $province]);
        }
    }

    /**
     * 获取城市列表
     * Create by zhangyangzhen
     * Date: 2018/6/5
     * Time: 10:21
     */
    public function cityList()
    {
        $this->_auth();

        $province = I('post.province_id', false, 'intval');

        if (!$province) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $model = $this->_getBanksModel();

        //城市列表
        $list = $model->getCity($province);

        if ($list === false) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            $this->apiReturn(200, ['list' => $list]);
        }
    }

    /**
     * 添加银行卡
     * Create by zhangyangzhen
     * Date: 2018/6/4
     * Time: 11:31
     */
    public function addBankCard()
    {
        $this->_auth();

        $type = I('post.type', 0, 'intval');

        if (!in_array($type, array(1, 2))) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $bankName    = I('post.bankName'); //支行名称
        $bankCode    = I('post.bankCode'); //支行行号
        $bankAccount = I('post.bankAccount'); //账号
        $accountName = I('post.accountName'); //账户名
        $accType     = I('post.accType');    //银行卡类型，借记卡/存折/信用卡/公司账号

        if (empty($bankName)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '银行名称不能为空');
        }
        if (empty($bankCode)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '支行名称不能为空');
        }
        if (empty($bankAccount) || !is_numeric($bankAccount)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '银行账号格式错误');
        }

        $res = $this->addOrSaveBankCard($this->memberId, $type, $bankName, $bankCode, $bankAccount, $accountName,
            $accType);
        if ($res['code'] == self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_SUCCESS, [], '成功');
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
    }

    /**
     * 添加/更新用户银行卡信息
     * Create by zhangyangzhen
     * Date: 2018/7/6
     * Time: 9:29
     *
     * @param $sid          用户id
     * @param $type         行卡类别，1 or 2
     * @param $bankName     支行名称
     * @param $bankCode     支行代号
     * @param $bankAccount  银行账户
     * @param $accountName  银行账户名
     * @param $accType      银行卡类型，借记卡/存折/信用卡/公司账号
     *
     * @return array
     */
    private function addOrSaveBankCard($sid, $type, $bankName, $bankCode, $bankAccount, $accountName, $accType)
    {
        $bankInfo = $bankName . '|' . $bankCode . '|' . $bankAccount . '|' . $accountName . '|' . $accType . '|' . 1;

        $member    = new \Model\Member\Member();
        $memberBiz = new \Business\Member\Member();
        $field     = 'm.dtype,e.com_name';
        $data      = $memberBiz->getInfo($sid);
        //$data   = $member->getInfoInMemberAndExtInfo($field, $sid);
        if ($data['dtype'] == 6) {
            return ['code' => self::CODE_AUTH_ERROR, 'msg' => '员工账号无权操作,只有主账号才能绑定银行账户'];
        }

        $len  = strlen($bankInfo);
        $Bank = substr($bankInfo, 0, $len - 1);

        $filedbank = 'bank_account' . $type;
        $mInfo     = $member->getMemberInfo($sid);
        $temp      = substr($mInfo[$filedbank], 0, $len - 1);
        if ($temp == $Bank) {
            return ['code' => self::CODE_PARAM_ERROR, 'msg' => '银行卡信息未作修改'];
        }
        $bankData[$filedbank] = $bankInfo;
        $re                   = $member->updateMemberInfo($sid, $bankData);
        if ($re) {
            return ['code' => self::CODE_SUCCESS, 'msg' => '成功'];
        }
    }

    /**
     * 获取编辑银行卡信息
     * Create by zhangyangzhen
     * Date: 2018/6/4
     * Time: 11:32
     */
    public function getCardInfo()
    {
        $this->_auth();

        $type = I('type', 0, 'intval');

        if (!in_array($type, array(1, 2))) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $bus = new AccountMoney();
        $res = $bus->getCardInfo($this->memberId, $type);

        if ($res['code'] == 200) {
            $this->apiReturn(self::CODE_SUCCESS, $res['data'], '成功');
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
    }

    /**
     * 首页数据
     * <AUTHOR>
     * @date 2018-06-05
     */
    public function todayInfo()
    {
        // 登录判断
        $this->_auth();

        $fid  = $this->memberId;
        $data = [];

        //如果是缓存获取或是强制刷新
        $sdtype = $this->loginInfo['sdtype'];
        if ($sdtype == 7) {
            //集团账号获取所有的成员
            $memberList = $this->_getGroupMemberList($fid);
            $fidArr     = $memberList;
        } else {
            $fidArr = $fid;
        }

        $staticModel = new Statistics();
        $date        = date('Y-m-d');
        //预订数据
        $orderRes              = $staticModel->getDataByFidTime($fidArr, $date, 1);
        $data['order_tickets'] = empty($orderRes['ticket_num']) ? 0 : $orderRes['ticket_num'];
        $data['order_money']   = empty($orderRes['sale_money']) ? 0 : $orderRes['sale_money'];

        //验证数据
        $checkRes              = $staticModel->getDataByFidTime($fidArr, $date, 2);
        $data['check_tickets'] = empty($checkRes['ticket_num']) ? 0 : $checkRes['ticket_num'];
        $data['check_money']   = empty($checkRes['sale_money']) ? 0 : $checkRes['sale_money'];

        if (!$data) {
            $this->apiReturn(400, [], '数据获取失败');
        }

        //处理返回数据
        $resData                   = $data;
        $resData['calculate_time'] = date('Y-m-d H:i:s');

        $this->apiReturn(200, $resData);
    }

    /**
     * 下架产品
     * <AUTHOR>
     * @date 2018-06-07
     */
    public function soldOut()
    {
        // 登录判断
        $this->_auth();

        $proId = I('lid', 0, 'intval');

        if (empty($proId)) {
            $this->apiReturn(400, [], '参数缺失');
        }

        $productBiz = new ProductBiz();
        $result     = $productBiz->soldOut($this->loginInfo['sid'], $this->memberId, $proId);

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], $result['msg']);
        } else {
            $this->apiReturn(400, [], $result['msg']);
        }
    }

    /**
     * 上架产品
     * <AUTHOR>
     * @date 2018-06-12
     */
    public function putaway()
    {
        // 登录判断
        $this->_auth();

        $proId = I('lid', 0, 'intval');

        if (!$proId) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $productBiz = new ProductBiz();

        $result = $productBiz->putaway($this->loginInfo['sid'], $this->memberId, $proId);

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], $result['msg']);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 票类详情
     * <AUTHOR>
     * @date 2018-06-07
     */
    public function getTickets()
    {
        // 登录判断
        $this->_auth();

        $lid = I('post.lid', 0, 'intval');

        if (empty($lid)) {
            $this->apiReturn(204, [], '参数错误');
        }

        // 只显示未删除的票
        $ticketBiz = new TicketBiz($this->memberId);
        $result    = $ticketBiz->getMiniTickets($lid);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $data = $this->handleTicketListResult($result['data']);

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 处理票类详情列表返回数据格式
     * <AUTHOR>
     * @date 2018-06-19
     *
     * @param $data
     *
     * @return array
     */
    private function handleTicketListResult($data)
    {

        if (empty($data)) {
            return [];
        }

        $result = [];

        foreach ($data as $item) {

            $tmp                           = [];
            $tmpMoney                      = [];
            $tmp['ticket']['name']         = $item['name'];
            $tmp['ticket']['item_id']      = $item['item_id'];
            $tmp['ticket']['refund_rule']  = $item['refund_rule'];
            $tmp['ticket']['id']           = $item['ticket_id'];
            $tmp['ticket']['ticket_price'] = $item['window_price'];
            $tmp['ticket']['product_id']   = $item['product_id'];
            $tmpMoney['weekdays']          = $item['sale_weekdays'];
            $tmpMoney['js']                = $item['window_price'];
            $tmpMoney['ls']                = $item['window_price'];
            $tmpMoney['w_price']           = $item['window_price'];
            $tmpMoney['m_price']           = $item['window_price'];
            $tmpMoney['sdate']             = $item['sale_start_date'];
            $tmpMoney['edate']             = $item['sale_end_date'];
            $tmpMoney['storage']           = $item['storage_perday'];
            $tmpMoney['id']                = $item['price_id'];
            $tmp['price_section'][]        = $tmpMoney;
            $result[]                      = $tmp;
        }

        return $result;
    }

    /**
     * 获取产品详情
     * <AUTHOR>
     * @date 2018-06-12
     */
    public function getLandInfo()
    {
        // 登录判断
        $this->_auth();

        $lid = I('post.lid', 0, 'intval');

        if (empty($lid)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $productBiz = new ProductBiz();
        $result     = $productBiz->getLandInfo($lid);

        // 对result的数据进行进行处理
        $data = $this->getLandinfoResHandle($result['data']);

        if ($result['code'] == 200) {
            $this->apiReturn(200, $data, '');
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 编辑或添加票类
     * <AUTHOR>
     * @date 2018-06-08
     */
    public function setTicket()
    {
        // 登录判断
        $this->_auth();

        $submitData = I('post.');

        if (count($submitData) < 1) {
            $this->apiReturn(400, [], '未接收到门票信息');
        }

        $ticketBiz = new TicketBiz($this->memberId);

        $result = [];
        foreach ($submitData as $tid => $item) {
            $result[] = $ticketBiz->setMiniCommonTicket($item, $tid, $this->memberId);
        }

        $this->apiReturn(200, $result, '');
    }

    /**
     * 获取自供应产品列表
     * <AUTHOR>
     * @date 2018-06-11
     */
    public function getProductList()
    {
        // 登录判断
        $this->_auth();

        $type = I('post.type', 'sale', 'strval');
        $page = I('post.page', '1', 'intval');
        $size = I('post.size', '15', 'intval');

        $productBiz = new ProductBiz();
        $result     = $productBiz->getProductList($this->memberId, $type, $page, $size);

        if ($result['code'] == 200) {
            $this->apiReturn(200, $result['data'], $result['msg']);
        } else {
            $this->apiReturn(400, [], $result['msg']);
        }
    }

    /**
     * 新增产品或编辑产品
     * <AUTHOR>
     * @date 2018-06-11
     */
    public function setProduct()
    {
        // 登录判断
        $this->_auth();

        $attr = $this->parseProductNecessaryAttr();
        if ($attr['code'] != 200) {
            $this->apiReturn($attr['code'], [], $attr['msg']);
        }

        $optionAttr = $this->parseProductOptionalAttr();
        if ($optionAttr['code'] == 204) {
            $this->apiReturn(204, [], $attr['msg']);
        }

        $data = $attr['data'];

        $productBiz = new ProductBiz();

        if ($data['pro_id']) {
            // 更新
            $result = $productBiz->update(
                $this->loginInfo['sid'],
                $this->memberId,
                $data['pro_id'],
                $data['title'],
                $data['ptype'],
                $data['level'],
                $data['address'],
                $data['province'],
                $data['city'],
                $data['notice'],
                $data['details'],
                $data['thumb_img'],
                1,
                $optionAttr['data']
            );
        } else {
            // 新增
            $result = $productBiz->publish(
                $this->loginInfo['sid'],
                $this->memberId,
                $data['title'],
                $data['ptype'],
                $data['level'],
                $data['address'],
                $data['province'],
                $data['city'],
                $data['notice'],
                $data['details'],
                $data['thumb_img'],
                1,
                $optionAttr['data']
            );
        }

        $returnData = $data['pro_id'] ? [] : ['pro_id' => $result['data']];

        if ($result['code'] == 200) {
            $this->apiReturn(200, $returnData, '保存成功');
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 修改票类状态(删除等)
     * <AUTHOR>
     * @date 2018-06-12
     */
    public function setTicketStatus()
    {
        // 登录判断
        $this->_auth();

        $tid    = I('post.tid', 0, 'intval');
        $action = I('post.action', '', 'strval');

        if ($tid === 0 || !in_array($action, ['deleteTicket', 'recoveryTicket', 'listTicket', 'deListTicket'])) {
            $this->apiReturn(203, [], '参数错误');
        }

        $ticketBiz = new TicketBiz($this->memberId);

        $res = $ticketBiz->setTicketStatus($tid, $this->memberId, $action);

        if ($res) {
            $this->apiReturn(200, [], 'success');
        } else {
            $this->apiReturn(201, [], 'fail');
        }
    }

    /**
     * 删除产品
     * <AUTHOR>
     * @date 2018-06-12
     */
    public function delProduct()
    {
        // 登录判断
        $this->_auth();

        $proId = I('lid', 0, 'intval');

        if (!$proId) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $productBiz = new ProductBiz();

        $result = $productBiz->delete($this->loginInfo['sid'], $this->memberId, $proId);

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], $result['msg']);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 新增产品或编辑产品时 必要参数筛选
     * <AUTHOR>
     * @date 2018-06-11
     */
    private function parseProductNecessaryAttr()
    {
        //编辑时用到
        $proId = I('lastid', 0, 'intval');

        // 产品名称
        $title = I('mainTitle', '', 'strval');
        if (!$title) {
            return ['code' => 204, 'msg' => '请填写产品名称'];
        }

        //预定须知
        $notice = I('buyTips', '', 'strval');
        //特产产品没有预定须知
        if (!$notice) {
            return ['code' => 204, 'msg' => '请填写预定须知'];
        }

        //景点缩略图
        $thumbImg = I('thumb_img', '', 'strval');
        if (!$thumbImg) {
            return ['code' => 204, 'msg' => '请上传产品缩略图'];
        }

        // 产品类型， 默认A, 景区
        $ptype = I('ptype', 'A', 'strval');
        //省份代码
        $province = I('d_province', 12, 'intval');
        //城市代码
        $city = I('d_city', 381, 'intval');
        //景点详细地址
        $address = I('mainAddress', '无', 'strval');
        //景点详情
        $details = I('detailInfo', '<p>【景点简介】<br><br></p><p>【景点特色】<br><br></p><p>【景点包含】</p><p><br></p>', 'strval');
        //景点级别
        $level = I('jtype', 'AAAA', 'strval');

        $data = [
            'pro_id'    => $proId,
            'ptype'     => $ptype,     // 默认景区
            'province'  => $province,    // 默认福建
            'city'      => $city,   // 默认福州
            'address'   => $address,    // 默认无
            'title'     => $title,
            'level'     => $level,
            'notice'    => $notice,
            'details'   => $details,
            'thumb_img' => $thumbImg,
        ];

        return ['code' => 200, 'data' => $data, 'msg' => ''];
    }

    /**
     * 新增产品或编辑产品时 非必要参数筛选
     * <AUTHOR>
     * @date 2018-06-15
     */
    private function parseProductOptionalAttr()
    {
        $data = [];

        // 目前只有一个展示图参数
        $imgGroup = I('img_group', '');
        if (!empty($imgGroup)) {
            $data['img_path_group'] = json_encode($imgGroup);
        }

        return ['code' => 200, 'data' => $data, 'msg' => ''];
    }

    /**
     * 根据集团账号ID获取集团成员
     *
     * @param  int  $parentId
     *
     * @return array
     */
    private function _getGroupMemberList($parentId)
    {
        if (!$parentId) {
            return [];
        }

        $relationModel = new MemberRelationship();
        $tmpList       = $relationModel->getMemIdByGroupId($parentId);

        $resList = [];
        if ($tmpList) {
            $resList = array_column($tmpList, 'son_id');
            $resList = array_unique($resList);
        }

        return $resList;
    }

    /**
     * 获取产品详情结果处理
     * <AUTHOR>
     * @date 2018-06-29
     */
    private function getLandinfoResHandle($res)
    {

        if (empty($res)) {
            return $res;
        }

        if (isset($res['img_path_group'])) {
            $tmpImgpathGrp         = @json_decode($res['img_path_group'], true);
            $imgpathGrp            = is_array($tmpImgpathGrp) ? $tmpImgpathGrp : unserialize($res['img_path_group']);
            $res['img_path_group'] = $imgpathGrp;
        }

        return $res;
    }

    /**
     * 根据凭证码获取所有已经支付未使用的订单
     *
     * <AUTHOR>
     * @date   2018-06-05
     */
    public function getPayUnUsedOrder()
    {
        $this->_auth();

        $code = 200;
        $msg  = '';
        $data = [];
        try {
            //凭证码
            $orderCode = I('post.code', '', 'strval,trim');

            if (!$orderCode) {
                throw new \Exception('参数缺失', 204);
            }

            //根据供应商ID取出所有景区ID
            //$landModel = $this->_getLandModel();
            //$lidArr    = $landModel->getLidArrByApplyId($this->memberId);

            $javaApi = new \Business\CommodityCenter\Land();
            $lidArr  = $javaApi->queryLandMultiQueryByApplyDid([$this->memberId]);

            if (empty($lidArr)) {
                throw new \Exception('该供应商未发布产品', 204);
            }

            $lidArr = array_column($lidArr, 'id');
            //根据凭证号获取
            //$orderModel = $this->_getOrderModel();
            //$data       = $orderModel->getOrderInfoByLidAndCode($lidArr, $orderCode, [0, 2], 'ordernum, lid, tid, tnum, status');

            //$orderQueryJavaBiz = new OrderQueryJavaService();
            //$otherParams       = [
            //    'code'   => [[$orderCode]],
            //    'status' => [[0, 2]],
            //];
            //$selectParams      = [
            //    'field' => 'ordernum, lid, tid, tnum, status',
            //];
            //$data              = $orderQueryJavaBiz->getOrderInfoByLidAndOtherParam($lidArr, $otherParams,
            //    $selectParams);

            //订单查询迁移
            $orderQueryLib = new OrderQueryMove();
            $data          = $orderQueryLib->getOrderInfByLidCodeStatusPayStatus($lidArr, [0, 2], [$orderCode]);

            if (empty($data)) {
                throw new \Exception('无可验证的产品', 204);
            }

            //获取tid数据
            $tidArr    = array_column($data, 'tid');
            $orderNum  = array_column($data, 'ordernum');
            $linkOrder = $this->_getLinkOrder($orderNum);

            $linkMain   = [];
            $linkIds    = [];
            $linkOrders = [];
            if (!empty($linkOrder)) {
                $linkOrders = $linkOrder['linkOrderRes'];//主票订单为键名，子票订单数组为键值
                $linkMain   = $linkOrder['linkMainOrder'];//主票订单集合
                $linkIds    = $linkOrder['linkChildOrder'];//子票订单集合
                //$queryParams   = [
                //    'field'   => 'ordernum,tid',
                //    'orderBy' => ['dtime' => 'desc'],
                //    'limit'   => 10,
                //];
                //$linkOrderInfo = $orderQueryJavaBiz->getOrderInfoByOrderNumAndOtherParam($linkIds, [], $queryParams);

                //订单查询迁移
                $orderMove     = new OrderQueryMove();
                $linkOrderInfo = $orderMove->getOrderoOrderByDtimeDesc($linkIds);

                foreach ($linkOrderInfo as $value) {
                    $linkTid[$value['ordernum']]['tid'] = $value['tid'];
                    $linkTids[]                         = $value['tid'];
                }

                $tidArr = array_merge($tidArr, $linkTids);
            }

            $ticketModel = $this->_getTicketModel();
            $ticketInfo  = $ticketModel->getTicketList($tidArr, 'id, title');

            $javaAPi      = new \Business\CommodityCenter\Land();
            $landInfo     = $javaAPi->queryLandMultiQueryById($lidArr);

            //获取lid数据
            //$landInfo = $landModel->getLandInfoByMuli($lidArr, 'id, title, salerid');

            $landRes = [];
            if (!empty($landInfo)) {
                foreach ($landInfo as $value) {
                    $landRes[$value['id']]['title']   = $value['title'];
                    $landRes[$value['id']]['salerid'] = $value['salerid'];
                }
            }

            foreach ($data as $key => $item) {
                if (in_array($item['ordernum'], $linkIds)) {
                    //清除联票子票
                    unset($data[$key]);
                    continue;
                }
                $data[$key]['tid_title'][] = isset($ticketInfo[$item['tid']]) ? $ticketInfo[$item['tid']]['title'] : '';
                if (in_array($item['ordernum'], $linkMain)) {
                    //联票把子票并入主票
                    foreach ($linkOrders[$item['ordernum']] as $child) {
                        $data[$key]['tid_title'][] = $ticketInfo[$linkTid[$child]['tid']]['title'];
                    }
                }
                $data[$key]['lid_title'] = isset($landRes[$item['lid']]) ? $landRes[$item['lid']]['title'] : '';
                $data[$key]['saler_id']  = isset($landRes[$item['lid']]) ? $landRes[$item['lid']]['salerid'] : '';
            }
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 核销订单
     *
     * <AUTHOR>
     * @date   2018-06-05
     */
    public function orderCheck()
    {
        $this->_auth();

        $code = 200;
        $msg  = '';
        $data = [];
        try {
            //景区ID
            $salerId = I('post.saler_id', 0, 'intval');
            //订单号
            $orderNum = I('post.order_num', '', 'strval,trim');
            //状态
            $status = I('post.status', false);

            if (!$salerId || !$orderNum || $status === false) {
                throw new \Exception('参数缺失');
            }

            //$landModel = $this->_getLandModel();
            //$landInfo  = $landModel->getInfoBySalerId($salerId);

            $landApi  = new \Business\CommodityCenter\Land();
            $landInfoRes = $landApi->queryLandMultiQueryByAdminAndPaging([], 1, 1, '', 'id desc', false, [], [], [], [],
                [$salerId]);
            if (empty($landInfoRes['list'])) {
                throw new \Exception('未找到该产品');
            }
            $landInfo = $landInfoRes['list'][0];

            $terminal = $landInfo['terminal'];
            if ($status == 2) {
                $cfg['vCmd'] = 602;
            } else {
                $cfg['vCmd'] = 601;
            }
            $cfg['vMode']      = 9;
            $cfg['vCheckDate'] = date('Y-m-d H:i:s');
            $global_cfg        = ['ext_info' => ['op' => $this->memberId]];
            $tSock             = \Library\Business\TerminalCheck::connect(IP_TERMINAL);
            $chResult          = $tSock->Terminal_Check_In_Order($terminal, $salerId, $orderNum,
                array_merge($cfg, $global_cfg));
            $checkData         = json_encode(array($terminal, $salerId, $orderNum, $cfg));
            $logStr            = "[$orderNum]验证,操作人[$this->memberId],数据[$checkData],结果[" . json_encode($chResult) . "]";
            pft_log('mini_check', $logStr);
            if ($chResult['state'] != 'success') {
                throw new \Exception($chResult['msg']);
            }
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 订单取消
     *
     * <AUTHOR>
     * @date   2018-06-15
     */
    public function orderCancel()
    {
        // Order_Change_Pro 这个接口已经废弃，如果后面需要，切换到新版退票接口
        $this->apiReturn(400, [], '接口废弃');

        //// 登录判断
        //$this->_auth();
        //
        //$orderNum = I('post.ordernum', '', 'strval,trim');
        //
        //if (empty($orderNum)) {
        //    $this->apiReturn(400, [], '订单数据有误');
        //}
        //
        //$orderToolModel = new \Model\Order\OrderTools('slave');
        //$orderInfo      = $orderToolModel->getOrderInfo($orderNum, 'pay_status, aid');
        //
        //if (empty($orderInfo)) {
        //    $this->apiReturn(400, [], '订单不存在');
        //}
        //
        //if ($orderInfo['aid'] != $this->memberId) {
        //    $this->apiReturn(400, [], '无权取消');
        //}
        //
        ////默认需要退款
        //$needRefundMoney = false;
        //if ($orderInfo['pay_status'] == 2) {
        //    //如果订单未支付 不需要退款
        //    $needRefundMoney = true;
        //}
        //
        //$false = false;
        //try {
        //    $soap = self::getSoap();
        //    $res  = $soap->Order_Change_Pro($orderNum, 0, -1, $needRefundMoney,
        //        $this->memberId, null, null, null, 0, '', '', $this->dtype, '取消订单', $this->memberId, 0, '');
        //} catch (\Exception $e) {
        //    $false  = true;
        //    $errMsg = $e->getMessage();
        //}
        //
        //if ($false) {
        //    $this->apiReturn(403, [], '订单取消失败:' . $errMsg);
        //}
        //
        //if ($res != 100) {
        //    $this->apiReturn(403, [], '订单取消失败:错误代码' . $res);
        //}
        //
        //$this->apiReturn(200);
    }

    /**
     * 生成商家或者景点的小程序码
     * Create by zhangyangzhen
     * Date: 2018/6/15
     * Time: 18:31
     */
    public function getPageAppCode()
    {
        $this->_auth();

        $scenCode = I('post.scenCode', '', 'strval,trim');
        $account  = I('post.account');
        $page     = I('post.page', '', 'strval,trim');
        $codeType = I('post.codeType', 0, 'intval');
        if (!$account || !$scenCode) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请求出错');
        }

        $cacheRedis = Cache::getInstance('redis');
        $cacheKey   = "miniCloud:$account:$codeType:$page:$scenCode";
        $url        = $cacheRedis->get($cacheKey);
        if ($url) {
            $this->apiReturn(200, ['url' => $url]);
        }
        $lib = new WechatSmallApp();

        if ($codeType == 1) {
            $res = $lib->getWxACode($account, $scenCode);
        } else {
            $res = $lib->getWxCodeUnlimit($account, $scenCode, $page);
        }

        $prefix = substr($res['data'], 0, 1);
        if ($prefix == '{') {
            $decodeRes = json_decode($res['data'], true);
            $this->apiReturn(401, $res, $decodeRes['errmsg']);
        }

        $mdscenCode = md5($scenCode . $page);
        $fileName   = "wacv2_$mdscenCode.png";
        $config     = load_config('qiniu', 'Qiniu');
        $qiniu      = new Qiniu($config);
        $fileUrl    = $qiniu->hasFileExist($fileName);
        if ($fileUrl != false) {
            $ret['url'] = $fileUrl;
            $this->apiReturn(self::CODE_SUCCESS, $ret);
        }

        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();

        $upManager = new UploadManager();
        $auth      = new Auth($config['accessKey'], $config['secretKey']);
        $token     = $auth->uploadToken($config['images']['bucket'], null, 3600);
        list($ret, $error) = $upManager->put($token, $fileName, $res['data']);
        if (ENV == 'PRODUCTION') {
            $ret['url'] = 'https://images.pft12301.cc/' . $fileName;
        } else {
            $ret['url'] = 'https://images.12301dev.com/' . $fileName;
        }

        $cacheRedis->set($cacheKey, $ret['url']);
        $this->apiReturn(self::CODE_SUCCESS, $ret);
    }

    /**
     * 获取验证记录列表
     *
     * <AUTHOR>
     * @date   2018-06-15
     */
    public function recordTrack()
    {
        // 登录判断
        $this->_auth();

        $member = $this->memberId;
        $page   = I('page', 1, 'intval');
        //$orderModel    = new OrderTools('slave');
        $landModel         = new Land();
        $ticketModel       = new Ticket();
        $orderQueryJavaBiz = new OrderQueryJavaService();
        //$otherParams       = [
        //    'aid'   => [[$member]],
        //    'dtime' => [[0], 'gt'],
        //];
        //$selectParams      = [
        //    'field'   => 'ordernum,dtime,lid,tid,tnum',
        //    'orderBy' => ['dtime' => 'desc'],
        //    'page'    => $page,
        //    'size'    => 10,
        //];
        //$orderInfo         = $orderQueryJavaBiz->getOrderListByOtherParam($otherParams, $selectParams);

        //订单查询迁移ywx
        $orderMove = new OrderQueryMove();
        $orderInfo = $orderMove->queryGetOrderListByAidDtimeDesc(intval($member), $page, 10);

        if (empty($orderInfo['list'])) {
            $this->apiReturn(403, [], '没有验证记录');
        }

        $orderIds   = array_column($orderInfo['list'], 'ordernum');
        $linkOrder  = $this->_getLinkOrder($orderIds);
        $linkMain   = [];
        $linkIds    = [];
        $linkOrders = [];

        if (!empty($linkOrder)) {
            //联票处理
            $linkOrders = $linkOrder['linkOrderRes'];//主票订单为键名，子票订单数组为键值
            $linkMain   = $linkOrder['linkMainOrder'];//主票订单集合
            $linkIds    = $linkOrder['linkChildOrder'];//子票订单集合
            //$queryParams   = [
            //    'field'   => 'ordernum,tid,tnum',
            //    'limit'   => 10,
            //    'orderBy' => ['dtime' => 'desc'],
            //];
            //$linkOrderInfo = $orderQueryJavaBiz->getOrderInfoByOrderNumAndOtherParam($linkIds, [], $queryParams);

            //订单查询迁移
            $orderMove     = new OrderQueryMove();
            $linkOrderInfo = $orderMove->getOrderoOrderByDtimeDesc($linkIds);

            foreach ($linkOrderInfo as $value) {
                $linkTid[$value['ordernum']]['tid']  = $value['tid'];
                $linkTid[$value['ordernum']]['tnum'] = $value['tnum'];
                $linkTids[]                          = $value['tid'];//联票子票id集合
            }
        }

        //获取景区和门票
        $landIds   = array_column($orderInfo['list'], 'lid');
        $ticketIds = array_column($orderInfo['list'], 'tid');
        if (!empty($linkOrder)) {
            $ticketIds = array_merge($ticketIds, $linkTids);//把子票id加入原票id集合
        }
        //$landInfo   = $landModel->getLandInfoByMuli($landIds, 'id, title', 1);

        $javaAPi      = new \Business\CommodityCenter\Land();
        $landInfo     = $javaAPi->queryLandMultiQueryById($landIds);
        $landInfo     = array_column($landInfo, 'title', 'id');
        $ticketInfo = $ticketModel->getTicketList($ticketIds, 'id, title');

        //验证数
        $subOrderModel = new SubOrderQuery();
        $checkRes      = $subOrderModel->getInfoInApplyInfoByOrder($orderIds, 'verified_num,orderid');
        foreach ($checkRes as $item) {
            $checkNum[$item['orderid']] = $item['verified_num'];
        }

        foreach ($orderInfo['list'] as $k => $v) {
            if (in_array($v['ordernum'], $linkIds)) {
                //清除联票子票
                unset($orderInfo['list'][$k]);
                continue;
            }
            $orderInfo['list'][$k]['lid'] = $landInfo[$v['lid']];
            unset($orderInfo['list'][$k]['tid']);
            $orderInfo['list'][$k]['tid'][] = $ticketInfo[$v['tid']]['title'] . '*' . $v['tnum'];
            if (in_array($v['ordernum'], $linkMain)) {
                //联票把子票并入主票
                foreach ($linkOrders[$v['ordernum']] as $child) {
                    $orderInfo['list'][$k]['tid'][] = $ticketInfo[$linkTid[$child]['tid']]['title'] . '*' . $linkTid[$child]['tnum'];
                }
            }
            $orderInfo['list'][$k]['checknum'] = $checkNum[$v['ordernum']];
        }
        $orderInfo['list'] = array_values($orderInfo['list']);
        $this->apiReturn(200, $orderInfo, '查询成功');

    }

    /**
     * 判断是否有开通应用如果没有开通，则第一次免费开通
     * can_use 是否可用， left_time 剩余时间, is_free 是否免费  begin_time套餐开始时间, end_time 套餐结束时间
     * <AUTHOR>
     * @date 2018-07-24
     */
    public function checkPackage()
    {
        pft_log('page_use/debug', '/r/Mall_MiniCloud/checkPackage is use');
        // 登录判断
        $this->_auth();

        $moduleDetail = new ModuleDetail();
        $useInfo      = $moduleDetail->getModuleUsedByUid($this->memberId, 47);

        if ($useInfo) {
            // 用户已经开通模块， 判断模块是否可用

            $isFree = $useInfo['use_free'];

            if (time() < $useInfo['begin_time']) {
                // 未到套餐使用时间
                $canUse   = 0;
                $leftTime = 0;
            } else if (time() > $useInfo['expire_time']) {
                // 套餐过期
                $canUse   = 0;
                $leftTime = 0;
            } else {
                // 未过期
                $canUse   = 1;
                $leftTime = round(($useInfo['expire_time'] - time()) / 60 / 60 / 24, 1);
            }

            $data = [
                'can_use'    => $canUse,
                'left_time'  => $leftTime,
                'is_free'    => $isFree,
                'begin_time' => date('Y-m-d H:i:s', $useInfo['begin_time']),
                'end_time'   => date('Y-m-d H:i:s', $useInfo['expire_time']),
            ];

            $this->apiReturn(200, $data, '获取数据成功');
        }

        // 免费开通模块
        $model = new ModulePayment();
        $res   = $model->addMiNiUserInfoByFree($this->memberId, 47);

        if ($res['code'] != 200) {
            $this->apiReturn(204, '', $res['msg']);
        }

        // 说明成功开通模块, 47模块 免费7天, 免费开通的时候一定可用
        $data = [
            'can_use'    => 1,
            'left_time'  => 7,
            'is_free'    => 1,
            'begin_time' => '',
            'end_time'   => '',
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 获取联票子票
     */
    private function _getLinkOrder($orderIdArr)
    {
        $model          = new OrderTools('slave');
        $linkOrder      = $model->getLinkSubOrderInfo($orderIdArr, 'orderid, concat_id');
        $linkOrderRes   = [];
        $linkChildOrder = [];
        $linkMainOrder  = [];
        foreach ($linkOrder as $item) {
            if ($item['concat_id'] == $item['orderid']) {
                continue;
            }
            //联票包含的票
            $linkOrderRes[$item['concat_id']][] = $item['orderid'];
            $linkChildOrder[]                   = $item['orderid'];
            $linkMainOrder[]                    = $item['concat_id'];
        }
        unset($linkOrder);
        $linkMainOrder = array_unique($linkMainOrder);

        if (empty($linkOrderRes) || empty($linkChildOrder) || empty($linkChildOrder)) {
            return [];
        }

        return [
            'linkOrderRes'   => $linkOrderRes,
            'linkChildOrder' => $linkChildOrder,
            'linkMainOrder'  => $linkMainOrder,
        ];
    }

    /**
     * 员工提现权限判断
     * Create by zhangyangzhen
     * Date: 2018/10/29
     * Time: 16:15
     * @return bool
     */
    private function _checkWithdrawAuth()
    {
        if (in_array($this->dtype, [0, 1])) {
            return true;
        } elseif ($this->dtype == 6) {
            $mModel = new \Model\Member\Member();

            // 员工账号有效性检测
            $res = $mModel->getStaffBySonId($this->memberId);
            if (!$res || $res['parent_id'] != $this->loginInfo['sid']) {
                $this->apiReturn(201, [], '您没有提现权限');
            }

            // 员工账号权限判断
            $son = $mModel->getMemberInfo($this->memberId);
            if (!$son) {
                $this->apiReturn(201, [], '员工账号不存在');
            }

            if (!in_array('accourtmanage', explode(',', $son['member_auth']))) {
                $this->apiReturn(201, [], '您没有提现权限');
            }
        } else {
            $this->apiReturn(201, [], '您没有提现权限');
        }
    }

}