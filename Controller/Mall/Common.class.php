<?php

namespace Controller\Mall;

use Library\Controller;
use Library\wechat\core\Popularize;
use Model\Subdomain\SubdomainInfo;
use Model\Member\Member;
use Library\Cache\Cache;
use Exception;

class Common extends Controller {

    private $_imgPath;  //图片保存地址
    private $_dirPath;  //图片保存文件夹

    private $_expires = 2592000;

    public function __construct() {
        $this->_dirPath = 'wechat/poster/';
        $this->_imgPath = IMAGE_UPLOAD_DIR . $this->_dirPath;
    }

    /**
     * 获取微商城配置
     * @return [type] [description]
     */
    public function getMallConfig()
    {
        $MemberModel = new Member('slave');

        $account = explode('.', $_SERVER['HTTP_HOST'])[0];
        $owner   = $MemberModel->getMemberInfo((int)$account, 'account');

        if (!$owner) {
            $this->apiReturn(400, [], '我要报警了');
        }

        $shopConfigModel = new \Model\Subdomain\ShopConfig();
        $config          = $shopConfigModel->getMallConfig($owner['id']);

        if (!$config) {
            $this->apiReturn(200, [], '');
        }

        $return['name']   = $config['name'];
        $return['banner'] = json_decode($config['others'], true)['banner'];

        $this->apiReturn(200, $return, '');
    }

    /**
     * 获取微信公众号信息
     * @return [type] [description]
     */
    public function getWechatOffiAccInfo($output = true)
    {
        $wxOpenModel = new \Model\Wechat\WxOpen();
        $account     = explode('.', $_SERVER['HTTP_HOST'])[0];
        $info        = $wxOpenModel->getWechatOffiAccInfo((int)$account, 'account');

        if ($this->isAjax() && $output) {
            $this->apiReturn(200, $info, '');
        }

        return $info;
    }

}
