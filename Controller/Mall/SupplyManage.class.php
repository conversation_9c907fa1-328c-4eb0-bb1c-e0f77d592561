<?php

/**
 * 微商城供应商管理接口
 * <AUTHOR>
 */

namespace Controller\Mall;

use Library\Controller;

class SupplyManage extends Controller {

	//供应商id
	private $_sid;

	public function __construct() {

		$this->_sid = $this->isLogin('ajax');

	}



	/**
	 * 微商城会员列表
	 * <AUTHOR>
	 * @date   2017-11-27
	 */
	public function memberList() {

		$page = I('page', 1, 'intval');
		$size = I('size', 10, 'intval');

		$keyword = I('keyword', '');

        $vipBiz  = new \Business\Mall\MemberVip($this->_sid, $this->_sid);

        $res = $vipBiz->memberList($page, $size, $keyword);

        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }

	}

}