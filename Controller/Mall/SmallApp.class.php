<?php
/**
 * Created by PhpStorm.
 * User: zhangyangzhen
 * Date: 2018/10/19
 * Time: 10:27
 */

namespace Controller\Mall;

use Business\AppCenter\Payment;
use Business\JavaApi\Member\MemberRelationQuery;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\Member\Register;
use Business\Member\RegisterAction;
use Library\Business\CaptchaCode;
use Library\Cache\Cache;
use Library\Container;
use Library\MessageNotify\MessageException;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Tools\Helpers;
use Library\Util\EnvUtil;
use Model\Member\Member;
use Business\Member\Member as MemberBiz;
use Business\Member\Login;
use Library\Business\WechatSmallApp;
use Model\Member\MemberRelationship;
use Library\Constants\MemberConst;

class SmallApp extends Mall
{
    const CODE_UN_LOGIN = 202;  //未登录

    //账户余额支付日志
    const PAY_ONLINE_PLA    = '/appcenter/pay_online_pla';
    //锁日志
    const LOKEY_LOG         = '/appcenter/lock_log';

    private $_smallAppModel;
    private $_memberModel;
    private $_memberRelationshipModel;

    private $_prefix = 'wptVCode:';

    // 免费套餐配置
    private  $freePackageConf = [
        'price_id'  => 1,//资费ID
        'module_id' => 36,//套餐ID，一年免费分销商套餐
        'pay_type'  => 2,//1=模块支付，2=套餐支付
        'is_update' => 0,
    ];

    // 生产人员账号配置
    protected $production = [
        'pftchenlu',
        '571452',
        '220530'
    ];
    // 实施人员账号配置
    protected $implementer = [
        'pftljh',
        'xmblzj',
        'pftxh1',
        'pftcy',
        'pftfzw',
        'pftlyc',
        'pftzwr',
        'pftchenhf',
        'pftzcn',
        '356161'
    ];

    public function __construct()
    {

    }

    /**
     * 登录
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 10:26
     *
     * @param string account 账号
     * @param string password 密码
     * @param int dtype 角色类型 0=供应商，1=分销商等
     */
    public function login()
    {
        $account  = I('post.account', '', 'strval');
        $password = I('post.password', '', 'strval');
        $dtype    = I('post.dtype', false);
        $code     = I('post.auth_code', false);

        if (empty($account) || empty($password)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '账号和密码不能为空');
        }

        //小程序这边的的图片验证码要特殊处理
        if($code) {
            $code = $code . CaptchaCode::$_identifierMark . $account;
        }

        $loginBiz = new Login();
        $res = $loginBiz->loginByPasswd($account, md5(md5($password)), MemberConst::PAGE_WPT_SMALLAPP, $dtype, $code);

        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }

        $res['data']['member_auth'] = is_null($res['data']['member_auth']) ? '' : $res['data']['member_auth']; //member_auth默认值为null，低版本redis会报错

        $data = $res['data'];

        // 如果用户是员工账号，则去获取上级信息
        if ($data['dtype'] == 6) {
            $shipModel          = new MemberRelationQuery();
            $parentInfo         = $shipModel->queryRelationshipEmployerMemberInfo($data['memberID']);
            $data['sid']        = $parentInfo['id'];
            $data['sdtype']     = $parentInfo['dtype'];
            $data['saccount']   = $parentInfo['account'];
            $data['sdname']     = $parentInfo['dname'];
        } else {
            $data['sid']        = $data['memberID'];
            $data['sdtype']     = $data['dtype'];
            $data['saccount']   = $data['account'];
            $data['sdname']     = $data['dname'];
        }

        $data['id']         = $data['memberID'];
        $data['headphoto']  = $data['headImg'];

        $smallLib   = $this->_getSmallAppModel();
        //保存登录信息
        $sessionkey = $smallLib->session_key();
        $smallLib->setSession($sessionkey, $data);

        // 是否为生产人员
        $data['production'] = false;
        if (ENV != 'PRODUCTION') {
            $this->production = [123933];
        }
        if (in_array($data['account'], $this->production)) {
            $data['production'] = true;
        }
        // 是否为实施人员
        $data['implementer'] = false;
        if (ENV != 'PRODUCTION') {
            $this->implementer = [123917];
        }
        if (in_array($data['account'], $this->implementer)) {
            $data['implementer'] = true;
        }

        $returnData = [
            'sessionkey' => $sessionkey,
            'data'       => $data,
            'expire'     => 1800
        ];

        $this->apiReturn($res['code'], $returnData, $res['msg']);
    }

    /**
     * 注册
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 13:51
     */
    public function register()
    {
        $code = 406;
        $data = [];
        $msg  = '注册失败';

        $mobile       = I('post.mobile', '', 'strval,trim');
        $company      = I('post.company', 'strval,trim');
        $companyType  = I('post.company_type', '', 'strval,trim');
        $nickname     = I('post.nickname', '', 'strval,trim');
        $vcode        = I('post.vcode', '', 'strval,trim');
        $pwd          = I('post.pwd', '', 'strval,trim');

        //手机验证码验证
        $isLegal = Helpers::ChkCode($mobile, $vcode);
        if ($isLegal !== true) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '验证码错误');
        }

        $memberBiz  = new MemberBiz();
        $customerId = $memberBiz->parseCustomerIdByMobile($mobile);
        if ($customerId) {
            //角色是否存在
            $memberModel = $this->_getMemberModel();
            $role = $memberModel->getTheRoleInfo($customerId, MemberConst::ROLE_DISTRIBUTOR, 'id');
            if ($role) {
                $this->apiReturn(self::CODE_NO_CONTENT, [], '该手机号已存在');
            }
        }

        $request = [
            'name'          => $nickname,
            'passwd'        => $pwd,
            'type'          => MemberConst::ROLE_DISTRIBUTOR,
            'identifier'    => $mobile,
            'customer_id'   => $customerId,
            'info_source'   => MemberConst::INFO_MOBILE,
            'page_source'   => MemberConst::PAGE_WPT_SMALLAPP,
            'status'        => 0,    //新需求原本是3需要审核，现在不需要审核了默认给36的套餐
            'com_name'      => $company,
            'com_type'      => $companyType
        ];

        $registerAction = new RegisterAction();
        $regRes         = $registerAction->register((object)$request);

//        $regModel = $this->_getRegisterModel();
//        $regRes   = $regModel->registerCommon('pc', $regData);

        if ($regRes) {
            $code = $regRes['code'];
            $data = $regRes['data'];
            $msg  = $regRes['msg'];
        }

        if ($code == 200) {
            $res = $this->buyFreePackage($data['account']);

            if ($res[0] == 200) {
                $this->apiReturn(200, $data, 'success');
            } else {
                pft_log(self::PAY_ONLINE_PLA, "开通结果：用户账号：{$data['account']}, 模块ID：{$this->freePackageConf['module_id']}开通失败, 失败原因:{$res[1]}");
                $this->apiReturn(201, [], '套餐开通失败，请联系客服人员！');
            }
        } else {
            $this->apiReturn($code, $data, $msg);
        }
    }

    /**
     * 给新用户开通一年免费的分销商套餐
     * <AUTHOR>
     * @date   2018-11-07 23:20:13
     * @param $account
     * @return array
     */
    private function buyFreePackage($account)
    {
        if (!$account) {
            return [201, '未找到该用户'];
        }

        $code = 200;
        $msg  = '';
        $data = [];

        $memberModel = new Member();
        $memberInfo  = $memberModel->getInfoByAccount($account, 'id,dtype,account');

        $cache       = \Library\Cache\Cache::getInstance('redis');
        $dtype       = $memberInfo['dtype'];
        $memberId    = $memberInfo['id'];
        if (!in_array($dtype, [0, 1])) {
            return [201, '账号类型错误'];
        }

        //pft_log(self::PAY_ONLINE_PLA, "申请开通：用户ID：{$memberId}, 套餐ID：{$this->freePackageConf['module_id']}, 是否升级：{$this->freePackageConf['is_update']}");
        //加锁 防止恶意请求
        $locky    = "payInPlatform:" . implode(':', $this->freePackageConf);
        $lock_ret = $cache->lock($locky, 1, 120);

        if (!$lock_ret) {
            pft_log(self::LOKEY_LOG, "[$locky]操作频繁");
            return [201, "请求正在处理中，请稍后"];
        };

        $packageBiz = new \Business\AppCenter\Package();

        $packageId = $this->freePackageConf['module_id'];
        $openRes   = $packageBiz->openFreeGivePackage($memberId, $packageId, $dtype);

        $orderNo = $openRes['data']['order_no'] ?? '';

        $data['order_no'] = $orderNo;

        if (!empty($locky)) {
            $cache->rm($locky);
        }

        if ($openRes['code'] != 200) {
            return [201, $openRes['msg']];
        }

        //开通结果 日志
        if ($code == 200) {
            $result = '成功';
        } else {
            $result = '失败';
        }

        pft_log(self::PAY_ONLINE_PLA, "开通结果：用户ID：{$memberId}, 模块ID：{$this->freePackageConf['module_id']}, 资费ID：{$this->freePackageConf['price_id']}, 流水号：{$orderNo}, 费用：{$fee}|{$result}, 原因:{$msg}");

        return [$code, $msg, $data];
    }

    /**
     * 登录授权验证
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 11:10
     * @return array|bool|string
     */
    protected function _auth()
    {
        $token = $_SERVER['HTTP_SESSION_KEY'];
        $data  = $this->_getLoginInfo($token);

        if (!$data || empty($data)) {
            $this->apiReturn(self::CODE_UN_LOGIN, [], '请重新登录');
        }

        $redis  = Cache::getInstance('redis');

        //判断过期时间，小于10分钟，更新过期时间
        $expire = $redis->ttl($token);
        if ($expire < 600) {
            $redis->expire($token, 1800);
        }

        return $data;
    }

    /**
     * 获取图形验证码
     * @param string $account 用户账号唯一标识
     * Create by zhangyangzhen
     * Date: 2019/1/15
     * Time: 18:30
     */
    public function getImgCode()
    {
        $account = I('get.account', '');

        if (empty($account)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, '用户账号不能为空');
        }

        $str     = CaptchaCode::generate($account);//随机生成的字符串

        $width=60;    //验证码图片的宽度
        $height=25;    //验证码图片的高度

        //Date in the past
        header("Expires:Mon,26 Jul 1997 05:00:00 GMT");
        //always modified
        header("Last-Modified:".gmdate("D,d M Y H:i:s")."GMT");
        //HTTP/1.1
        header("Cache-Control:no-store,no-cache,must-revalidate");
        header("Cache-Control:post-check=0,pre-check=0",false);
        //HTTP/1.0
        header("Pragma:no-cache");
        header("Content-Type:image/png");
        $im=imagecreate($width,$height);
        $back=imagecolorallocate($im,0xFF,0xFF,0xFF);    //背景色
        //$pix=imagecolorallocate($im,187,190,247);        //模糊点颜色
        $font=imagecolorallocate($im,41,163,238);        //字体色
        //绘制1000个模糊作用的点
        //mt_srand();
        //for($i=0;$i<1000;$i++) {
        //    imagesetpixel($im,mt_rand(0,$width),mt_rand(0,$height),$pix);
        //}
        imagestring($im,5,7,5,$str,$font);//绘制随机生成的字符串
        //imagerectangle($im,0,0,$width-1,$height-1,$font);//在验证码图像周围绘制1px的边框
        imagepng($im);//建立一张PNG格式图形
        imagedestroy($im);//释于内存空间将图片handle解构，
    }

    /**
     * 忘记密码
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 11:11
     */
    public function resetPwd()
    {
        $mobile   = I('post.mobile', '');
        $password = I('post.password', '');
        $code     = I('post.code', '');

        if (empty($mobile) || empty($password) || empty($code)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '手机号/密码/验证码不能为空');
        }

        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入正确的手机号码');
        }

        //校验注册验证码
        $codeRes = Helpers::ChkCode($mobile, $code);
        if ($codeRes !== true) {
            $this->apiReturn(self::CODE_PARAM_ERROR, '', '验证码错误');
        }

        //判断该手机号是否注册
        $model   = $this->_getMemberModel();
        $isExist = $model->getInfoByAccount($mobile);
        if (!$isExist) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '该手机号还未注册');
        } else {
            $fid = $isExist['id'];
        }

        //重置密码

        $bizMember = new \Business\Member\Member();
        $res       = $bizMember->updateMemberPassword($fid, $password);

        if ($res) {
            $this->apiReturn(self::CODE_SUCCESS, [], '重置密码成功');
        } else {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '重置密码失败');
        }
    }

    /**
     * 判断手机号是否注册
     * Create by zhangyangzhen
     * Date: 2018/11/1
     * Time: 11:24
     */
    public function isExistMobile()
    {
        $mobile = I('post.mobile', '','trim');

        if (empty($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '手机号不能为空');
        }

        $model = $this->_getMemberModel();
        $res   = $model->getInfoByAccount($mobile);

        if (!empty($res)) {
            $this->apiReturn(self::CODE_SUCCESS, ['isRegister' => 1], '');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, ['isRegister' => 0], '');
        }
    }

    /**
     * 发送手机验证码
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 11:24
     */
    public function sendVCode()
    {
        $mobile = I('post.mobile', '');
        //手机号码验证
        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(406, [], '请输入正确的手机号码');
        }

        //判断手机号是否被列入黑名单
        $blackList   = load_config('black_list');
        $blackMobile = $blackList['mobile'];
        if (in_array($mobile, $blackMobile)) {
            $this->apiReturn(403, [], '该手机号已经被加入黑名单。');
        }

        $cacheRedis = Cache::getInstance('redis');
        $cacheKey   = $this->_prefix . $mobile;
        $send_time  = $cacheRedis->get($cacheKey, '', true);
        if ($send_time > 10) {
            $this->apiReturn(403, [], '该手机号发送次数超出系统限制。');
        }

        if (Helpers::getVerifyCode($mobile)) {
            $this->apiReturn(403, [], '发送间隔太短！请在60秒后再重试。');
        }

        //发送短信
        try {
            $code   = Helpers::setVerifyCode($mobile, 60);
            $messageServiceApi = Container::pull(MessageService::class);
            [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'register_code', $mobile, [$code, '2']);
            if ($approval) {
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['发送注册手机验证码', __METHOD__, [$mobile, [$code, '2']], $res], JSON_UNESCAPED_UNICODE));
                }
            } else {
                /** @deprecated 放量结束后删除 */
                $smsLib = SmsFactory::getFactory($mobile);
                $res = $smsLib->registerCode($code);
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['发送注册手机验证码.old', __METHOD__, [$mobile, [$code]], $res], JSON_UNESCAPED_UNICODE));
                }
            }
            if ($res['code'] == self::CODE_SUCCESS) {
                $cacheRedis->incrBy($cacheKey);
                $this->apiReturn(self::CODE_SUCCESS, [], '发送验证码成功');
            } else {
                $this->apiReturn($res['code'], [], $res['msg']);
            }
        } catch (MessageException $e) {
            $this->apiReturn(500, [], '对不起，短信服务器发生故障，造成的不便我们感到十分抱歉。请联系我们客服人员。');
        }
    }

    /**
     * 通过sessionkey获取用户信息
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 11:27
     */
    public function getUserInfoBySessionKey()
    {
        $sessionKey = $_SERVER['HTTP_SESSION_KEY'];

        $data = $this->_getLoginInfo($sessionKey);

        //用户二期 - 信息获取修改 - 2 - modification-a
        $MemberBus   = new \Business\Member\Member();
        $memberInfo  = $MemberBus->getInfo($data['id'],true);
        $memberExt = [
            'com_type' => $MemberBus::__CORP_KIND_ARR__[$memberInfo['corp_kind']],
            'province' => $memberInfo['province'],
            'city'     => $memberInfo['city'],
            'business' => $memberInfo['business'],
            'com_name' => $memberInfo['com_name']
        ];

        $data = array_merge($data, $memberExt);

        if (!$data || empty($data)) {
            $this->apiReturn(self::CODE_SUCCESS, [], 'success');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, $data, 'success');
        }
    }

    /**
     * 获取登录信息
     * Create by zhangyangzhen
     * Date: 2018/9/3
     * Time: 16:07
     * @return array|bool|string
     */
    private function _getLoginInfo($sessionKey)
    {
        if (empty($sessionKey)) {
            $this->apiReturn(self::CODE_UN_LOGIN, [], 'SESSION-KEY不能为空');
        }

        $smallLib = $this->_getSmallAppModel();
        $res      = $smallLib->getSession($sessionKey, null);

        return $res;
    }

    /**
     * 获取两次md5加密密码
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 9:58
     * @param $password
     * @return string
     */
    protected function _getMd5Password($password)
    {
        return md5(md5($password));
    }

    /**
     * 获取Model\Member\Member模型
     * Create by zhangyangzhen
     * Date: 2018/9/7
     * Time: 9:42
     * @return Member
     */
    private function _getMemberModel()
    {
        if (empty($this->_memberModel)) {
            $this->_memberModel = new Member();
        }

        return $this->_memberModel;
    }

    /**
     * 获取Model\Member\MemberRelationship模型
     * Create by zhangyangzhen
     * Date: 2018/9/7
     * Time: 10:25
     * @return MemberRelationship
     */
    private function _getMemberRelationshipModel()
    {
        if (empty($this->_memberRelationshipModel)) {
            $this->_memberRelationshipModel = new MemberRelationship();
        }

        return $this->_memberRelationshipModel;
    }

    /**
     * 获取微信小程序模型
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 10:04
     * @return WechatSmallApp
     */
    private function _getSmallAppModel()
    {
        if (empty($this->_smallAppModel)) {
            $this->_smallAppModel = new WechatSmallApp();
        }

        return $this->_smallAppModel;
    }

    /**
     * 统一的数据返回
     * <AUTHOR>
     * @date   2017-02-14
     *
     * @param  string  $code  业务码
     * @param  string  $msg  相关信息
     * @param  array  $data  数组数据
     *
     * @return
     */
    public function returnData($code, $msg = '', $data = [])
    {
        return ['code' => $code, 'msg' => $msg, 'data' => $data];
    }
}