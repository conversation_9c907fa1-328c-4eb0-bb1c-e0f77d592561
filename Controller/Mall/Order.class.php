<?php

/**
 * 微商城订单相关接口
 * <AUTHOR>
 */

namespace Controller\Mall;

use Business\Finance\PayMerchant;
use Business\JavaApi\Ticket\Price;
use Business\JavaApi\Product\Ticket as NewTicketApi;
use Business\Member\MemberSystem;
use Business\Order\OrderUnity;
use Controller\Mall\Mall;
use Library\Business\WechatSmallApp;
use Model\Order\OrderLog;
use Model\Subdomain\SubdomainInfo;
use Model\Member\Member;
use Model\Product\Ticket;
use Model\Product\Land;
use Business\Wechat\Seckill;
use Business\Member\Member as MemberBiz;
use Business\Product\Specialty;
use Business\Product\AnnualCard as AnnualBiz;
use Business\Mall\MemberVip;
use Business\Order\FzCityCard as FzCityCardBiz;
use Business\Product\FzJasmine as JasminBiz;
use Business\Member\RegisterAction;
use Library\Constants\MemberConst;

use Process\Order\OrderParams;
use Business\Order\PlatformSubmit;

use Business\Order\OrderOta;

class Order extends Mall
{

    private $_orderExtra = [];

    /**
     * 抢购提交订单
     * <AUTHOR>
     * @date   2017-08-07
     */
    public function seckillOrder()
    {

        //获取参数
        $paramsRes = OrderParams::getSeckillParams($this->_supplyId);
        if ($paramsRes[0] != 200) {
            $this->apiReturn(204, [], $paramsRes[1]);
        }

        $request = $paramsRes[2];
        if ($_SESSION['memberID'] && $_SESSION['dtype'] == 5) {
            $request['order_member'] = $_SESSION['memberID'];
        }

        $seckillBiz = new Seckill();
        $result     = $seckillBiz->preOrderCheck($request);
        if ($result['code'] != 200) {
            $this->apiReturn(204, [], $result['msg']);
        } else {
            $this->apiReturn(200, ['identify' => $result['data']['job_id']]);
        }
    }

    /**
     * 抢购是否完成(成功或者失败)
     * <AUTHOR>
     * @date   2017-08-07
     * @return boolean    [description]
     */
    public function isSeckillComplete()
    {
        //任务id
        $jobId = I('identify');
        //活动id
        $seckillId = I('seckill_id', 0, 'intval');

        if (!$jobId || !$seckillId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $seckillBiz = new Seckill();
        $result     = $seckillBiz->isSeckillComplete($seckillId, $jobId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器异常');
        }

    }

    /**
     * 购买年卡下单接口
     * <AUTHOR>
     * @date   2018-06-27
     */
    public function orderForCard()
    {
        //下单参数对象
        $paramsObject = (object)I('');

        $paramsObject->aid    = $this->_supplyId;
        $paramsObject->up_aid = I('aid', 0, 'intval');
        $paramsObject->sale_channel = 1;

        $memberRes = $this->_createOrParseMemberId(I(''));
        if ($memberRes[0] != 200) {
            $this->apiReturn(204, [], $memberRes[1]);
        } else {
            $memberId = $memberRes[2];
        }
        $paramsObject->operid = $memberId;

        $annualBiz = new AnnualBiz();
        $result    = $annualBiz->orderForCard($memberId, $paramsObject);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    private function _createOrParseMemberId($orderParams)
    {
        //是否已经登录
        $memberId = I('session.memberID', 0);
        $dtype    = I('session.dtype', 0);
        if ($memberId) {
            //已经登录
            if ($dtype != MemberConst::ROLE_TOURIST) {
                return [204, '请先退出，并使用散客账号下单'];
            }
        } else {
            $openid   = $this->_smallOpenid ?: '';
            $memberId = $this->_registerMember($orderParams['ordername'], $orderParams['ordertel'], $openid);
            if (!is_numeric($memberId)) {
                return [204, '新用户账号生成失败'];
            }
        }

        return [200, '', $memberId];
    }

    protected function changeMemberTypeForShop($customerId, $sid)
    {
        $memberBz   = new \Business\Member\Member();
        $customList = $memberBz->getMemberListByCustomerId($customerId, $sid);
        foreach ($customList as $key => $value) {
            if ($value['dtype'] == 5) {
                return $value['id'];
            }
        }

        return false;
    }

    /**
     * 提交订单(砍价，拼团，茉莉积分，特产,其他的都改到orderv2下了)
     */
    public function order()
    {

        @pft_log('order/wechat/mall', json_encode($_REQUEST, JSON_UNESCAPED_UNICODE), $pathMode = 'day');
        //登录才能下单
        $memberId = $this->isLogin('ajax');
        //获取参数
        $paramsRes = OrderParams::getMarketingParams($this->_supplyId);
        if ($paramsRes[0] != 200) {
            $this->apiReturn(204, [], $paramsRes[1]);
        }

        $orderParam              = $paramsRes[2];
        $orderParam['member_id'] = $memberId;
        $orderParam['op_id']     = $memberId;
        $orderParam['is_sale']   = true;

        //下单渠道
        if (I('pay_mode') == 33) {
            //e福州
            $orderParam['channel'] = 33;
        } else {
            $orderParam['channel'] = self::__ORDER_MODE__;
        }
        //营销活动参数检测
        $checkRes = $this->_marketingCheck($memberId, $orderParam);
        if ($checkRes[0] != 200) {
            $this->apiReturn(204, [], $checkRes[1]);
        }

        $orderParam = $checkRes[1];

        $platformOrderBiz = new PlatformSubmit();
        $orderRes         = $platformOrderBiz->submit($orderParam);
        if ($orderRes[0] != 200) {
            $this->apiReturn(204, [], $orderRes[1]);
        }

        //订单号
        $ordernum = $orderRes[2]['orderNum'];
        //保存订单请求参数
        $this->_saveOrderAndParamsMap([$ordernum], I(''), $orderParam['channel']);
        //监听微商城订单
        $hookParams             = $orderParam;
        $hookParams['ordernum'] = $ordernum;
        $hookParams             = array_merge($this->_orderExtra, $hookParams, $orderRes[2]['originRes']['data']);
        \Library\Hook::Listen('mall_order_success', $hookParams);

        $return = [
            'ordernum' => $ordernum,
            'paymode'  => $orderParam['paymode'],
        ];

        @pft_log('order/wechat/mall', json_encode($return, JSON_UNESCAPED_UNICODE), $pathMode = 'day');
        $this->apiReturn(200, $return);
    }

    private function _marketingCheck($memberId, $orderParams)
    {
        $checkRes = [200, $orderParams];
        //砍价
        if ($orderParams['join_id']) {
            $checkRes = $this->_checkForCutActivity($memberId, $orderParams);
        }
        //拼团
        if ($orderParams['group_id']) {
            $checkRes = $this->_checkForGroupActivity($memberId, $orderParams);
        }
        //茉莉积分
        if ($orderParams['discount_id']) {
            $checkRes = $this->_checkJasmineDiscount($orderParams['discount_id'], $orderParams);
        }

        return $checkRes;
    }

    private function _checkForCutActivity($memberId, $orderParams)
    {

        $checkRes = (new \Business\Wechat\CutPrice())->preOrderCheck($memberId, $orderParams['join_id']);
        if ($checkRes['code'] != 200) {
            return [204, $checkRes['msg']];
        }

        //砍价最终下单价格
        $orderParams['cut_price'] = $checkRes['data']['price'];
        $orderParam['tnum']       = 1;
        $this->_orderExtra        = $checkRes['data'];

        return [200, $orderParams];
    }

    private function _checkForGroupActivity($memberId, $orderParams)
    {

        $checkRes = (new \Business\Wechat\GroupBooking())->orderCheck($memberId, $orderParams['group_id'],
            $orderParams['open_id'], $orderParams['tnum']);
        if ($checkRes['code'] != 200) {
            return [204, $checkRes['msg']];
        }

        //拼团最终下单价格
        $orderParams['group_price'] = $checkRes['data']['price'];
        //拼团有单独的渠道
        $orderParams['channel'] = 34;
        //微信openid
        $orderParams['openid'] = I('session.openid', '');
        $this->_orderExtra     = $checkRes['data'];

        return [200, $orderParams];
    }

    /**
     * 保存订单号和对应的请求参数信息
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @param  array  $orderArr  订单号数组
     * @param  array  $params  前端提交的参数
     *
     * @return bool
     */
    private function _saveOrderAndParamsMap($orderArr, $params, $channel = '')
    {
        if (empty($channel)) {
            $channel = self::__ORDER_MODE__;
        }
        (new \Model\Order\OrderLog())->saveOrderAndParamsMap($orderArr, $params, $channel);
    }

    /**
     * 支付页面接口
     */
    public function pay()
    {
        $ordernum      = I('post.ordernum', '');
        $host          = I('host', '', 'strval'); //xuwenbin.12301.cc 或者 123624.12301.cc
        $wxAppScenCode = I('post.shop_code', 0);
        if ($wxAppScenCode) {
            $wxLib      = new WechatSmallApp();
            $merchantId = $wxLib->decodeShopCode($wxAppScenCode);
        }

        $tmpHost = explode('.', $host);
        if (count($tmpHost) == 3) {
            $host = strval(trim($tmpHost[0]));
        } else {
            //兼容之前直接intval的处理逻辑
            $host = intval($host);
        }

        if (!$ordernum || !$host) {
            $this->apiReturn(204, [$ordernum, $host], '参数错误');
        }

        $Member = new \Model\Member\Member();
        // $Ticket = new Ticket('slave');

        $Order      = new \Model\Order\OrderTools();
        $OrderQuery = new \Model\Order\OrderQuery();

        $supply = $Member->getMemberInfo((string)$host, 'account', 'id');
        if (!$supply) {
            $this->apiReturn(204, [], '非法请求');
        }
        $orderInfo               = $Order->getOrderInfo($ordernum);
        $orderInfo['totalPrice'] = $OrderQuery->get_order_total_fee($ordernum);
        $orderExtra              = $Order->getOrderDetailInfo($ordernum);
        //11：待出票状态
        if (!in_array($orderInfo['status'], [0, 4, 11])) {
            $this->apiReturn(204, [], '订单状态错误');
        }
        if ($orderExtra['pay_status'] != 2) {
            $this->apiReturn(206, [], '订单已支付');
        }
        //获取票类信息
        // $ticket = $Ticket->getTicketInfoById($orderInfo['tid'], 'cancel_auto_onMin,title');
        $Ticket         = new \Business\JavaApi\Product\Ticket();
        $ticketApi      = new \Business\JavaApi\TicketApi();
        $memberIdAndAid = $ticketApi::getOrderAidByMemberIdAndAid($ordernum, $orderInfo['member'], $orderInfo['aid'], $orderInfo['visitors']);
        $ticket         = $Ticket->queryTicketById($orderInfo['tid'], $memberIdAndAid['memberId'],
            $memberIdAndAid['aid'], $this->saleChannel);
        if ($ticket['code'] != 200) {
            $this->apiReturn(205, [], "获取票属性异常");
        }
        $ticket = $ticket['data'];
        $ticket = $Ticket->mapAttribute($ticket);
        if (time() - strtotime($orderInfo['ordertime']) > $ticket['cancel_auto_onMin'] * 60) {
            $this->apiReturn(204, [], '已超过支付时间');
        }

        //是否是抢购订单
        $seckillModel = new \Model\Mall\Seckill;
        $secOrder     = $seckillModel->getOrderInfo($ordernum);

        //获取订单游客实名信息
        $tourists = $this->_getTouristsReal($ordernum);

        if ($secOrder) {
            $secInfo = $seckillModel->getSeckill($secOrder['seckill_id']);
            if ($secInfo && $secInfo['cancel_time'] != -1) {
                $ticket['cancel_auto_onMin'] = $secInfo['cancel_time'];
            }
        }

        //获取支付参数
        if ($this->inWechatSmallApp()) {
            $payParams = $this->_getPayParamsForSmallApp(
                $ordernum,
                $supply,
                $host,
                $ticket['title'],
                $ticket['cancel_auto_onMin']
            );
        } else {
            //订单号的供应商和商城所有者是否一致
            //if ($orderInfo['aid'] != $supply['id']) {
            //    $this->apiReturn(204, [], '非法请求');
            //}
            $payParams = $this->_getPayParams(
                $ordernum,
                $supply,
                $host,
                $ticket['title'],
                $ticket['cancel_auto_onMin']
            );
        }

        $payParams['ptype'] = $payParams['detail']['ptype'];
        // 小程序需要
        $payParams['payParams']['shop_owner_id'] = isset($merchantId) ? $merchantId : '';
        // $payParams['payParams']['expireTime'] = $ticket['cancel_auto_onMin'];
        $payParams['tourists'] = $tourists;

        $this->apiReturn(200, $payParams);

    }

    /**
     * 支付是否完成
     * @return boolean [description]
     */
    public function isPayComplete($ordernum = '')
    {
        $ordernum = I('ordernum', '', 'intval');

        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }

//        $payMapping = (new \Model\Order\OrderTools())->getOrderPayStatus([$ordernum]);
        $queryParams = [[$ordernum],true,'pay_status'];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder','queryOrderInfoByOrdernumList', $queryParams);
        $payMapping = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $payMapping = $queryRes['data'];
        }
        if ($payMapping[$ordernum] == 1) {
            $return['payStatus'] = 1;
        } else {
            $return['payStatus'] = 0;
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 支付成功页面
     */
    public function paySuccess()
    {
        $ordernum  = I('ordernum', '', 'strval');
        $passCheck = I("passcheck");

        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }

        $return = $this->_getOrderDetail($ordernum);

        $return['is_face'] = 0;
        if ($return['ptype'] == 'F') {
            $orderModel = new \Model\Order\OrderTools('slave');
            $childOrder = $orderModel->getPackChildOrders($ordernum, 'orderid');
            if ($childOrder) {
                $orderInfo = $orderModel->getOrderInfo($childOrder[0]['orderid'], 'lid');
                $lid       = $orderInfo['lid'];
            }
        } else {
            $lid = $return['lid'];
        }

        // $faceModel    =  new \Model\Terminal\FaceCompare();
        // $platformData = $faceModel -> getFacePlatform($lid);
        // if($platformData['face_platform']) {
        //     $return['is_face']  = 1;
        // }

        $landExtDraft  = [];
        $ticketBiz     = new \Business\JavaApi\CommodityCenter\Ticket();
        $getTicketInfo = $ticketBiz->queryTicketAttrsById($return['tid']);
        if ($getTicketInfo['code'] == 200) {
            foreach ($getTicketInfo['data'] as $attr) {
                $landExtDraft[$attr['key']] = $attr['val'];
            }
        }
        if (!isset($landExtDraft['face_open'])) {
            $return['is_face'] = 0;
        } else {
            $return['is_face'] = (int)$landExtDraft['face_open'];
        }

        //$ticketModel   = new \Model\Product\Ticket();
        //$ticketExtInfo = $ticketModel->getFaceConfig([$return['tid']], 'face_open');
        //if ($ticketExtInfo === false) {
        //    $return['is_face'] = 0;
        //} else {
        //    $return['is_face'] = (int)$ticketExtInfo[0];
        //}

        //新微商城下不限制
        if (!$this->inAlipaySmallApp() && $return['aid'] != $this->_supplyId && !$passCheck) {
            $this->apiReturn(204, [], '非法访问');
        }

        $toolModel = new \Model\Order\OrderTools('slave');
        $extra     = $toolModel->getOrderDetailInfo($ordernum);

        if (!$extra['aids']) {
            $aid = $this->_supplyId;
        } else {
            $tmp = explode(',', $extra['aids']);
            $aid = array_pop($tmp);
        }

        $return['aid'] = $aid;

        // 关注公众号
        $wxOpenModel    = new \Model\Wechat\WxOpen();
        $account        = explode('.', $_SERVER['HTTP_HOST'])[0];
        $info           = $wxOpenModel->getWechatOffiAccInfo((int)$account, 'account');
        if ($info) {
            $return['alert']      = 1;
            $return['qrcode_url'] = $info['qrcode_url'];
            $return['nick_name']  = $info['nick_name'];
        } else {
            $return['alert'] = 0;
        }

        //二维码类型 门票码判断
        if ($return['tickets'][0]['print_mode'] == 4 && ($return['tickets'][0]['code_show_type'] == '' || $return['tickets'][0]['code_show_type'] == 1)) {
            $temidxArr = $toolModel->getTouristOrederIdxs($ordernum, 'idx, check_state, orderid, chk_code');
            $idxArr    = array_column($temidxArr, 'idx');
            if ($idxArr) {
                foreach ($temidxArr as $temVal) {
                    if ($temVal['check_state'] != 2) {
                        if (empty($temVal['chk_code'])) {
                            // 没有门票码用OD#
                            $temMultiCode                               = OrderUnity::getCodeByOrdernumTnum($ordernum,
                                $return['tickets'][0]['num'], $idxArr, $temVal['idx'])[0];
                            $return['multi_code'][]                     = $temMultiCode;
                            $return['multi_code_status'][$temMultiCode] = $temVal['check_state'];
                        } else {
                            $return['multi_code'][]                           = $temVal['chk_code'];
                            $return['multi_code_status'][$temVal['chk_code']] = $temVal['check_state'];
                        }
                    }
                }
            }
        }

        //如果是第三方订单且存在第三方订单凭证码， 则获取第三方订单凭证码
        $orderOtaBus           = new OrderOta();
        $handleCodeRes         = $orderOtaBus->handleCode($ordernum, $return['qrcode']);
        $noticeType            = $orderOtaBus->getNoticeCode($return['lid']);
        $return['notice_type'] = $noticeType;
        //第三方订单情况处理
        if ($noticeType != -1) {
            $return['qrcode'] = $handleCodeRes['code'];//赋值为第三方的凭证码
        }
        //第三方订单凭证码生成类型为默认类型0时候的处理
        if ($noticeType == 0) {
            if (!empty($handleCodeRes['code'])) {
                $return['notice_type'] = 1;//类型为同步发码
            } else {
                $return['notice_type'] = 3;//类型为不发码
                if ($handleCodeRes['handle_status'] == 2) {
                    //超时情况下
                    $return['notice_type'] = 2;//类型为异步发码
                }
            }
            //all_api_order表没有记录情况为平台订单
            if ($handleCodeRes['handle_status'] == -1) {
                $return['notice_type'] = 0;//类型为非第三方订单
            }
        }
        //同步发码超时情况处理
        if ($noticeType == 1 && empty($handleCodeRes['code']) && $handleCodeRes['handle_status'] == 2) {
            $return['notice_type'] = 2;//类型为异步发码
        }
        //非第三方订单情况处理
        if ($noticeType == -1) {
            $return['notice_type'] = 0;//类型为非第三方订单
        }

//        $sid = $this->_supplyId;
//        $memberInfo = $this->getLoginInfo();
//        $memberId   = $memberInfo['memberID'];
//        //获取订单费用项
//        $memberSystemModeBs = new MemberSystem();
//        $orderExpenseitem   = $memberSystemModeBs->getMulOrderExpenseitem($sid, $memberId, [$ordernum], 'item_money');
        $return['money'] = 0;
        $this->apiReturn(200, $return);
    }

    /**
     * 支付成功激活年卡
     * <AUTHOR>
     */
    public function activeAnnual()
    {
        //激活接口转移了
        $this->apiReturn(200, [], '成功');
    }

    /**
     * 订单是否完成支付
     * @return boolean [description]
     */
    public function isOrderComplete()
    {
        $ordernum = I('ordernum', 0, 'intval');
        $host     = I('host', '', 'intval');

        if ($host == 'm') {
            $host = self::__DEFAULT_ACCOUNT;
        }

        if (!$ordernum || !$host) {
            $this->apiReturn('204', [], '参数错误');
        }

        $mModel = new Member('slave');
        $supply = $mModel->getMemberInfo($host, 'account');

        if (!$supply) {
            $this->apiReturn(204, [], '非法请求');
        }

        $Order = new \Model\Order\OrderTools();

        $orderInfo = $Order->getOrderInfo($ordernum);
        //订单号的供应商和商城所有者是否一致
        if ($orderInfo['aid'] != $supply['id'] || $orderInfo['ordermode'] != self::__ORDER_MODE__) {
            $this->apiReturn(204, [], '非法请求');
        }

//        $payMapping = $Order->getOrderPayStatus([$ordernum]);
        $queryParams = [[$ordernum],true,'pay_status'];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder','queryOrderInfoByOrdernumList', $queryParams);
        $payMapping = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $payMapping = $queryRes['data'];
        }
        $payStatus = 0;
        if ($payMapping[$ordernum] == 1) {
            $payStatus = 1;
        }

        $this->apiReturn(200, ['payStatus' => $payStatus]);
    }

    /***
     *  查询用户茉莉分,获取折扣方案
     */
    public function getUserDiscount()
    {
        $name     = I('name', '', 'strval');
        $idCardNo = I('idCardNo', '', 'strval');
        $tid      = I('tid', 0, 'intval');

        if (!$name || !$idCardNo || !$tid) {
            $this->apiReturn(203, [], '缺少用户信息或票信息');
        }

        $loginInfo = $this->getLoginInfo();
        $memberId  = $loginInfo['memberID'];
        $sid       = $loginInfo['sid'];

        $fzBiz      = new FzCityCardBiz();
        $jasmineBiz = new JasminBiz($memberId, $sid);
        $creditInfo = $fzBiz->getUserCreditScore($name, $idCardNo);

        if (!$creditInfo || $creditInfo['type'] != 1) {
            $this->apiReturn(204, [], '未查询到用户的茉莉分信息');
        }

        //获取分数对应折扣方案
        $useDiscount = $jasmineBiz->getScoreUseDiscount($creditInfo['score'], $tid);
        $return      = [
            'discount'      => $useDiscount,
            'jasmine_score' => $creditInfo['score'],
        ];

        $this->apiReturn(200, $return, 'success');
    }

    /**
     * 仅供微票房小程序登录使用（实际是注册）
     * Create by zhangyangzhen
     * Date: 2019/1/30
     * Time: 11:15
     *
     * @param  string  $mobile  手机号
     */
    public function register()
    {
        //先转一层
        $ctrl = new \Controller\Mall\Member();
        $ctrl->regiterOrBindMobileForWeipiaofang();
    }

    /**
     * 注册新账号
     *
     * @param  [type] $data 账号信息
     *
     * @return [type]       [description]
     */
    private function _registerMember($name, $mobile, $account, $sfz = '', $avatar = '')
    {

        $memberBiz   = new MemberBiz();
        $memberModel = new Member('slave');

        $infoSource = MemberConst::INFO_MOBILE;
        $pageSource = MemberConst::PAGE_MALL;
        $identifier = $mobile;
        //这边先做个判断，太乱了
        if ($this->inWechatSmallApp()) {
            $infoSource = MemberConst::INFO_WX_SMALL_APP;
            $pageSource = MemberConst::PAGE_WX_SMALLAPP;
            //openid
            $identifier = $account;
        }
        if ($this->inAlipaySmallApp()) {
            $infoSource = MemberConst::INFO_ALI_SAMLL_APP;
            $pageSource = MemberConst::PAGE_ALI_SMALLAPP;
            //openid
            $identifier = $account;
        }

        //客户是否存在
        if ($this->inWechatSmallApp() || $this->inAlipaySmallApp()) {
            $authInfo   = $memberModel->getAuthByIdentifier($account, $infoSource + 1);
            $customerId = $authInfo ? $authInfo['customer_id'] : 0;
        } else {
            $customerId = $memberBiz->parseCustomerIdByMobile($mobile);
        }

        if ($customerId) {
            //角色是否存在
            $role = $memberModel->getTheRoleInfo($customerId, MemberConst::ROLE_TOURIST, 'id');
            if ($role) {
                return $role['id'];
            }
        }

        if (ismobile($mobile)) {
            $passwd = substr($mobile, 5);
        } else {
            $passwd = '';
        }

        $request = [
            'name'        => $name,
            'passwd'      => $passwd,
            'type'        => MemberConst::ROLE_TOURIST,
            'avatar'      => $avatar,
            'id_card'     => $sfz,
            'identifier'  => $identifier,
            'customer_id' => $customerId,
            'info_source' => $infoSource,
            'page_source' => $pageSource,
        ];

        $registerAction = new RegisterAction();
        $result         = $registerAction->register((object)$request);

        if ($result['code'] == 200) {
            //创建会员关系
            $vipBiz = new MemberVip($result['data']['member_id'], $this->_supplyId);
            $vipBiz->createRelation(0);

            return $result['data']['member_id'];
        } else {
            $this->errmsg = $result['msg'];

            return false;
        }
    }

    /***
     * 茉莉分积分折扣下单检测
     * <AUTHOR> Yiqiang
     * @date   2018/08/08
     */
    private function _checkJasmineDiscount($discountId, $orderParams)
    {
        $jasmineBiz   = new \Business\Product\FzJasmine();
        $discountInfo = $jasmineBiz->getOneDiscountById($discountId);
        if ($discountInfo) {
            $orderParams['jasmine_discount'] = floatval($discountInfo['discount']);
        }

        return [200, $orderParams];
    }

    /**
     * 获取支付参数
     *
     * @param  string  $ordernum  订单号
     * @param  array  $supply  供应商账号信息
     * @param  string  $host  供应商账号
     * @param  string  $ttitle  门票名称
     * @param  int  $autoCancel  自动取消时间
     *
     * @return array
     */
    private function _getPayParams($ordernum, $supply, $host, $ttitle, $autoCancel)
    {
        //微商城配置信息
        $mallConfig = $this->getMallConfig($supply['id']);
        $others     = json_decode($mallConfig['others'], true);

        $hasSetPay = isset($others['pay_ali']);

        $cardSolutionPay = 0;
        if (!empty($others['pay_card'])) {
            $ConfigSolutionModel = new \Business\CardSolution\Config();
            $checkRes            = $ConfigSolutionModel->isCanUseCardSolutionPay($ordernum);

            if ($checkRes === true) {
                $cardSolutionPay = 1;
            }
        }

        $return = [
            'payWay' => [
                'ali'  => $hasSetPay ? $others['pay_ali'] : 0,
                'wx'   => $hasSetPay ? $others['pay_wx'] : 1,
                'uni'  => $hasSetPay ? $others['pay_uni'] : 1,
                'card' => $cardSolutionPay,
            ],
        ];
        //不在微信app内
        if (!$this->inWechatApp()) {
            $return['payWay']['wx'] = 0;
        }

        $detail = $this->_getOrderDetail($ordernum);

        $payParams = [
            'subject'    => $detail['landTitle'] . $ttitle,
            'expireTime' => $autoCancel,
            'outTradeNo' => $ordernum,
            'buyId'      => $supply['id'],
            'domain'     => str_replace('wx', $host, MOBILE_DOMAIN),
        ];

        unset($detail['qrcode']);
        $return['detail'] = $detail;
        // 特约商户独立收款
        $biz             = new PayMerchant();
        $merchantPayConf = $biz->getMerchantConfig(PayMerchant::WEPAY, $supply['id'], '', '', false);
        if (isset($merchantPayConf['sub_appid'])
            && $merchantPayConf['sub_appid']
            && $merchantPayConf['use_env'] >= 1
            && $merchantPayConf['use_env'] != 3) {
            $payParams['appid'] = $merchantPayConf['sub_appid'];
            //需要从网页授权中获取
            $payParams['openid'] = $_SESSION['pft_openid'];
        } elseif ($return['payWay']['wx']) {
            //这个参数统一在配置文件里面已经配置了 - dwer.cn
            $payParams['appid'] = PFT_WECHAT_APPID;

            //需要从网页授权中获取
            $payParams['openid'] = $_SESSION['pft_openid'];
        }

        $return['payParams'] = $payParams;

        return $return;
    }

    /**
     * 获取支付参数
     *
     * @param  string  $ordernum  订单号
     * @param  array  $supply  供应商账号信息
     * @param  string  $host  供应商账号
     * @param  string  $ttitle  门票名称
     * @param  int  $autoCancel  自动取消时间
     *
     * @return array
     */
    private function _getPayParamsForSmallApp($ordernum, $supply, $host, $ttitle, $autoCancel)
    {

        $return = [
            'payWay' => [
                'wx' => 1, //微信支付
            ],
        ];
        //订单详情
        $detail = $this->_getOrderDetail($ordernum);

        $payParams = [
            'subject'    => $detail['landTitle'] . $ttitle,
            'expireTime' => $autoCancel,
            'outTradeNo' => $ordernum,
            'buyId'      => $supply['id'],
            'domain'     => str_replace('wx', $host, MOBILE_DOMAIN),
        ];

        //unset($detail['qrcode']);
        $return['detail'] = $detail;
        $conf             = load_config($this->_supplyAccount, 'wechat_sm_app');
        $tempAppid = 'wx5605b231e666f425';
        if (ENV == 'PRODUCTION') {
            $tempAppid = 'wx2f45381cd36a6400';
        }
        $appid            = !is_null($conf) ? $conf['appid'] : $tempAppid;
        if ($return['payWay']['wx']) {
            $payParams['appid']  = $appid;
            $payParams['openid'] = $this->_smallOpenid;
        }

        $return['payParams'] = $payParams;

        return $return;
    }

    /**
     * 获取订单详细信息
     *
     * @param  int  $ordernum  订单号
     *
     * @return array
     */
    private function _getOrderDetail($ordernum)
    {
        $Order      = new \Model\Order\OrderTools();
        $OrderQuery = new \Model\Order\OrderQuery();

        // $Land = new \Model\Product\Land();

        $orderExtra = $Order->getOrderDetailInfo($ordernum);

        $orderInfo = $Order->getOrderInfo($ordernum);

        //景区类型
        $ticketApi = new \Business\JavaApi\Product\Ticket();
        $ticket = $ticketApi->queryTicketInfoById($orderInfo['tid']);
        if($ticket['code'] != 200){
            $this->apiReturn(205, [], "获取门票信息失败");
        }

        $ticket = $ticket['data'];
        $land = $ticket['uuLandDTO'];
        $land = $ticketApi->mapAttribute($land, 'land');
        $pType  = $land['p_type'];

        //获取包含的门票信息
        $tickets = $this->_getOrderTickets(
            $ordernum,
            $orderInfo['tid'],
            $orderInfo['tnum'],
            //是否是联票
            $orderExtra['concat_id'] ? true : false,
            $orderInfo['playtime']
        );

        //根据景区类型不同，获取一些额外的展示信息
        $extra = $this->_getExtraInfo(
            $pType,
            $orderInfo,
            $orderExtra
        );

        $totalmoney = $OrderQuery->get_order_total_fee($ordernum);

        // 获取优惠券金额
        $couponMoney      = 0;
        $orderCouponModel = new \Model\Order\Coupon();
        $useCouponArr     = $orderCouponModel->getUseCouponInfoByTradeId($ordernum, 'id,all_use_coupon_money');
        if (!empty($useCouponArr)) {
            $couponMoney = ($useCouponArr[0]['all_use_coupon_money'] / 100);
        }
        if ($land['terminal_type'] == 2) {
            $orderInfo['code'] = str_pad($orderInfo['code'], 8, '0', STR_PAD_LEFT);
        }

        return [
            'lid'         => $orderInfo['lid'],
            'pid'         => $orderInfo['pid'],
            'aid'         => $orderInfo['aid'],
            'tid'         => $orderInfo['tid'],
            'ptype'       => $land['p_type'],
            'landTitle'   => $land['title'],
            'totalmoney'  => $totalmoney / 100,
            'couponMoney' => $couponMoney,
            'ordername'   => $orderInfo['ordername'],
            'ordertel'    => $orderInfo['ordertel'],
            'qrcode'      => $orderInfo['code'],
            'tickets'     => $tickets,
            'extra'       => $extra,
            'pay_status'  => $orderInfo['pay_status'],
            'terminal'    => $land['terminal'],
        ];
    }

    /**
     * 获取订单的门票信息
     *
     * @param  int  $ordernum  主票订单号
     * @param  int  $tid  主票tid
     * @param  int  $tnum  主票票数
     * @param  boolean  $link  是否是联票
     * @param  string  $playtime  游玩时间
     *
     * @return [type]            [description]
     */
    private function _getOrderTickets($ordernum, $tid, $tnum, $link = false, $playtime = '')
    {

        if ($link) {
            $field = 'ss.tid,ss.tnum';

            $tickets = (new \Model\Order\OrderTools())->getLinkOrdersInfo($ordernum, $field);
        } else {
            $tickets[] = [
                'tid'  => $tid,
                'tnum' => $tnum,
            ];
        }

        $Model      = new Ticket();
        $priceApi   = new Price();
        $ticketApi  = new \Business\JavaApi\TicketApi();
        $tidArray   = array_unique(array_column($tickets, 'tid'));

        $javaApi  = new \Business\CommodityCenter\Ticket();
        $queryInfoArr = [];
        foreach ($tidArray as $key => $value) {
            $queryInfoArr[$key]['ticketId'] = $value;
        }
        $tidData = [];
        $ticketInfoArr = $javaApi->queryTicketInfoByQueryInfos($queryInfoArr);
        if ($ticketInfoArr) {
            foreach ($ticketInfoArr as $item) {
                $tidData[] = $javaApi->fieldConversion($item);
            }
        }
        //$tidStr     = implode(',', array_unique($tidArray));
        //$tidData    = $ticketApi->getTicketArr($tidStr);
        $ticketData = [];
        foreach ($tidData as $ticket) {
            $ticketData[$ticket['id']] = [
                'apply_did'        => $ticket['account_id'],
                'title'            => $ticket['name'],
                'getaddr'          => $ticket['get_ticket_info'],
                'pid'              => $ticket['product_id'],
                'entry_method'     => isset($ticket['ext']['entry_method']) ? $ticket['ext']['entry_method'] : 0,
                'is_show_ordernum' => isset($ticket['ext']['is_show_ordernum']) ? $ticket['ext']['is_show_ordernum'] : 0,
                'print_mode'       => isset($ticket['ext']['print_mode']) ? $ticket['ext']['print_mode'] : 0,
                'code_show_type'   => isset($ticket['ext']['code_show_type']) ? $ticket['ext']['code_show_type'] : '',
            ];
        }
        $return = [];
        foreach ($tickets as $item) {
            $priceRes = $priceApi->getActualtimePrice($ticketData[$item['tid']]['apply_did'],
                $ticketData[$item['tid']]['apply_did'], $tid, $playtime);
            if ($priceRes['code'] != 200) {
                $price = 0;//正常都会获取到
            } else {
                $price = $this->inWechatSmallApp() || $this->inAlipaySmallApp() ? $priceRes['data']['window_price'] / 100 : $priceRes['data']['retail_price'] / 100;
            }

            $return[] = [
                'title'            => $ticketData[$item['tid']]['title'],
                'num'              => $item['tnum'],
                'price'            => $price,
                'getaddr'          => $ticketData[$item['tid']]['getaddr'],
                'entry_method'     => $ticketData[$item['tid']]['entry_method'],
                'is_show_ordernum' => $ticketData[$item['tid']]['is_show_ordernum'],
                'print_mode'       => isset($ticketData[$item['tid']]['print_mode']) ? $ticketData[$item['tid']]['print_mode'] : 0,
                'code_show_type'   => isset($ticketData[$item['tid']]['code_show_type']) ? $ticketData[$item['tid']]['code_show_type'] : '',
            ];
        }

        return $return;
    }

    /***
     * 根据订单获取游客实名信息
     * <AUTHOR> Yiqiang
     * @date   2018-09-13
     *
     * @param  string  $ordernum  订单号
     */
    private function _getTouristsReal($ordernum)
    {
        $Order       = new \Model\Order\OrderTools();
        $touristInfo = $Order->getOrderTouristInfo(strval($ordernum));
        $tourists    = array();
        if ($touristInfo) {
            foreach ($touristInfo as $tourist) {
                $tourists[] = [
                    'name'   => $tourist['tourist'],
                    'idcard' => $tourist['idcard'],
                ];
            };
        }

        return $tourists;
    }

    /**
     * 根据票种获取不同的订单信息
     *
     * @param  string  $type  类型
     * @param  array  $orderInfo  订单信息
     * @param  array  $orderExtra  订单额信息
     *
     * @return [type]             [description]
     */
    private function _getExtraInfo($type, $orderInfo, $orderExtra)
    {

        switch ($type) {
            case 'A':
                return $this->_getExtraInfoForLand($orderInfo);
                break;

            case 'F':
                return $this->_getExtraInfoForLand($orderInfo);
                break;

            case 'B':
                return $this->_getExtraInfoForLoad($orderInfo, $orderExtra);
                break;

            case 'C':
                return $this->_getExtraInfoForHotel($orderInfo);
                break;

            case 'H':
                return $this->_getExtraInfoForShow($orderExtra, $orderInfo);
                break;

            case 'I':
                return $this->_getExtraInfoForAnnual($orderInfo);
                break;

            case 'J':
                return $this->_getExtraInfoForSpecial($orderInfo, $orderExtra);
                break;

            default:
                # code...
                break;
        }
    }

    /**
     * 获取景区订单的信息
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return [type]            [description]
     */
    private function _getExtraInfoForLand($orderInfo)
    {

        //有效时间
        $date = $orderInfo['begintime'] . '~' . $orderInfo['endtime'];

        return [
            'date' => $date,
        ];
    }

    /**
     * 获取线路订单的信息
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return [type]            [description]
     */
    private function _getExtraInfoForLoad($orderInfo, $orderExt = [])
    {

        //集合时间
        $date = $orderInfo['begintime'];

        $Ticket = new \Model\Product\Ticket();

        $ticket = $Ticket->getTicketExtInfoByTid($orderInfo['tid'], 'ass_station');

        //集合地点
        $station = json_decode($ticket['ass_station'], true);

        return [
            'date'    => $date,
            'station' => $station[$orderExt['assembly']],
        ];
    }

    /**
     * 获取酒店订单的信息
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return [type]            [description]
     */
    private function _getExtraInfoForHotel($orderInfo)
    {

        $link = (new \Model\Order\OrderTools())->getLinkOrdersInfo($orderInfo['ordernum'], 'playtime');

        $last = array_pop($link);

        if (!$last) {
            $last['playtime'] = $orderInfo['playtime'];
        }

        $begintime = strtotime($orderInfo['playtime']);
        $endtime   = strtotime($last['playtime']) + 3600 * 24;

        //住店时间
        $date = $orderInfo['playtime'] . '~' . date('Y-m-d', $endtime);

        //住店天数
        $days = ($endtime - $begintime) / 3600 / 24;

        return [
            'date' => $date,
            'days' => $days,
        ];

    }

    /**
     * 获取演出订单的信息
     *
     * @param  [type] $orderExtra 订单额外信息
     *
     * @return [type]             [description]
     */
    private function _getExtraInfoForShow($orderExtra, $orderInfo = '')
    {
        $series = unserialize($orderExtra['series']);

        //演出日期
        $date = $series[11] ?? $series[4];

        //座位
        $seat = explode(',', $series[6])[2];
        if (!empty($orderInfo)) {
            $tid           = $orderInfo['tid'];
            $ticketExtInfo = [];
            $ticketBiz     = new \Business\JavaApi\CommodityCenter\Ticket();
            $getTicketInfo = $ticketBiz->queryTicketAttrsById($tid);
            if ($getTicketInfo['code'] == 200) {
                foreach ($getTicketInfo['data'] as $attr) {
                    if ($attr['key'] == 'is_show_seat') {
                        $ticketExtInfo[$attr['ticket_id']] = $attr;
                    }
                }
            }
            //$ticketModel   = new \Model\Product\Ticket();
            //$ticketExtInfo = $ticketModel->getTicketExtConfig([$orderInfo['tid']], 'ticket_id,key,val',
            //    ['is_show_seat']);
            if (!empty($ticketExtInfo[$tid]['val']) && $ticketExtInfo[$tid]['val'] == 2) {
                $seat = '';
            }
        }

        return [
            'date' => $date,
            'seat' => $seat,
        ];
    }

    /**
     * 获取年卡订单信息
     * <AUTHOR>
     * @date   2017-11-22
     *
     * @param  array  $orderInfo  订单详情
     *
     * @return array
     */
    private function _getExtraInfoForAnnual($orderInfo)
    {

        $ordernum  = (string)$orderInfo['ordernum'];
        $orderLog  = new OrderLog('slave');
        $params    = $orderLog->getOrderParams($ordernum);
        $params    = json_decode($params, true);
        $virtualNo = isset($params['virtual_no']) ? $params['virtual_no'] : '';

        return [
            'virtual_no' => $virtualNo,
            'physics_no' => '',
        ];
    }

    /**
     * 获取特产订单信息
     * <AUTHOR>
     * @date   2017-12-13
     *
     * @param  array  $orderInfo  订单详情
     * @param  array  $orderExtra  订单额外信息
     *
     * @return array
     */
    private function _getExtraInfoForSpecial($orderInfo, $orderExtra)
    {

        //是否有选择收货人(快递)
        $series = $orderExtra['series'];

        $tid = $orderInfo['tid'];

        $specialBiz = new Specialty();

        $shipId = $tempShipId = 0;
        if ($series) {
            list($shipId, $expressWay) = explode('|', $series);
            if($expressWay ==0){
                $tempShipId = 0;
            }else{
                $tempShipId = $shipId;
            }
        }
        //解析收货/取货信息
        $speRes = $specialBiz->parsePackInfo($tid, $tempShipId);

        $return = [];
        if ($speRes['code'] == 200) {

            $return['take_delivery'] = $speRes['data']['take_delivery'];
            $return['pick_delivery'] = $speRes['data']['pick_delivery'];

            if ($series) {
                //显示收货信息
                $return['delivery_way'] = 0;
            } else {
                //显示取货信息
                $return['delivery_way'] = 1;
            }

            if (!$series) {
                //下单几天内取货有效
                $ticModel = new Ticket('slave');
                $ticket   = $ticModel->getTicketInfoById($tid, 'expire_action_days');

                $days      = (int)$ticket['expire_action_days'];
                $orderTime = strtotime($orderInfo['ordertime']);

                $validDate = date('Y-m-d', strtotime("+{$days} day", $orderTime));

                $return['take_delivery']['code']       = $orderInfo['code'];
                $return['take_delivery']['valid_date'] = $validDate;
            }
        }

        return $return;
    }

    /**
     * 年卡是否需要替换
     * <AUTHOR>
     * @date   2017-12-01
     */
    public function isAnnualToReplace()
    {
        $this->apiReturn(200, [], '无需替换');
    }

    /**
     * 新增收货人信息
     * <AUTHOR>
     * @date   2017-12-12
     */
    public function addShippingAddr()
    {
        //收货人姓名
        $name = I('name', '');
        //手机号
        $mobile = I('mobile', 0, 'intval');
        //省份
        $province = I('province', '', 'strval');
        //城市
        $city = I('city', '', 'strval');
        //县区
        $town = I('town', '', 'strval');
        //详细地址
        $detailAddr = I('detail_addr', '');
        //邮编
        $postcode = I('postcode', 0, 'intval');

        if (!($name && $mobile && $province && $city && $detailAddr)) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (!\ismobile($mobile)) {
            $this->apiReturn(204, [], '请填写正确的手机号');
        }

        if (isset($_SESSION['memberID'])) {
            $mid = $_SESSION['memberID'];
        } else {
            $regRes = $this->_registerMember($name, $mobile, $mobile);
            if (!is_numeric($regRes)) {
                $this->apiReturn(204, [], '注册失败，请重试');
            }
            $mid = $regRes;
        }

        $paramsObj = new \stdClass;
        //参数对象
        $paramsObj->name        = $name;
        $paramsObj->mobile      = $mobile;
        $paramsObj->province    = $province;
        $paramsObj->city        = $city;
        $paramsObj->town        = $town;
        $paramsObj->postcode    = $postcode;
        $paramsObj->detail_addr = $detailAddr;
        $paramsObj->add_type    = 0;

        $specialBiz = new Specialty();

        $result = $specialBiz->createShippingAddr($mid, $paramsObj);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '添加失败');
        }
    }

    /**
     * 获取收货人列表
     * <AUTHOR>
     * @date   2017-12-12
     */
    public function getShippingAddrs()
    {

        $mid = $this->isLogin('ajax');

        $field = 'id,name,mobile,detail_addr';

        $specialBiz = new Specialty();

        $result = $specialBiz->getShippingAddrs($mid, $field);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '获取出错');
        }
    }

    /**
     * 特产产品预定确认信息
     * <AUTHOR>
     * @date   2017-12-12
     */
    public function specialtyGoodsInfo()
    {
        //门票(规格)pid
        $tid = I('tid', 0, 'intval');

        if (!$tid) {
            $this->apiReturn(204, [], '参数缺失');
        }

        // 票信息通过java接口获取，新增渠道规则 -- jinmin
        $newTicketApi = new NewTicketApi();
        $ticModel = new Ticket('slave');

        $ticketRes = $newTicketApi->queryTicketById($tid);
        if ($ticketRes['code']!=200) {
            $this->apiReturn(204, [], '商品信息不存在');
        }
        $ticket = $newTicketApi->mapAttribute($ticketRes['data'], 'ticket');
//
//        $ticket = $ticModel->getTicketInfoById($tid, 'landid,pay,title,pid,refund_rule,expire_action_days');
//        if (!$ticket) {
//            $this->apiReturn(204, [], '商品信息不存在');
//        }

        $price = $ticModel->getRetailPrice($ticket['pid']);

        if ($price === false) {
            $this->apiReturn(204, [], '获取价格失败');
        }

        $price *= 100;
        //获取产品名称和图片
        $landModel = new Land('slave');
        $land      = $landModel->getLandInfo($ticket['landid'], false, 'imgpath,title');

        //获取特产配置信息
        $specialBiz = new Specialty();

        $speRes = $specialBiz->getOneSpecification($tid);

        if ($speRes['code'] != 200) {
            $this->apiReturn(204, [], '商品规格信息获取失败');
        }

        $specification = $speRes['data'];

        $return = [
            'tid'             => $tid,
            'pid'             => $ticket['pid'],
            'pay'             => $ticket['pay'],
            'ltitle'          => $land['title'],
            'ttitle'          => $ticket['title'],
            'price'           => $price,
            'imgpath'         => $land['imgpath'],
            'refund_rule'     => $ticket['refund_rule'],
            'valid_day'       => !empty($ticket['delaydays']) ? $ticket['delaydays'] : $ticket['expire_action_days'],
            //是否开启定点取货
            'special_dot'     => $specification['pick_up_point'],
            'weight'          => $specification['single_weight'],
            'express_way'     => $specification['express_pay_way'],
            'express_pay_way'     => $specification['express_pay_way'],
            'link_man'        => $specification['pick_up_contacts'],
            'address'         => $specification['pick_up_address'],
            //是否开启快递配送
            'checked_express' => $specification['checked_express'],
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 年卡续费接口
     * <AUTHOR>
     * @date   2019-05-09
     */
    public function orderForCardRenew()
    {
        $mid = $this->isLogin('ajax');
        //下单参数对象
        $paramsObject = (object)I('');
        $annualBiz    = new AnnualBiz();
        //年卡校验
        if (!$paramsObject->card_id || !is_numeric($paramsObject->card_id)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $checkRes = $annualBiz->annualRenewHCheck($paramsObject->card_id, 'id', false, $mid);
        if ($checkRes['code'] !== 200) {
            $this->apiReturn(204, [], $checkRes['msg']);
        }
        unset($paramsObject->card_id);

        $paramsObject->sale_channel = 1;
        $paramsObject->annual_renew = 1;
        $result       = $annualBiz->orderForCard($mid, $paramsObject);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }

    }
}
