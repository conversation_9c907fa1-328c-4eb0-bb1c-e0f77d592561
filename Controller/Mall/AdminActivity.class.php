<?php
/**
 * 后台活动列表
 *
 * User: xujy
 * Date: 2020/6/18
 */

namespace Controller\Mall;

use Library\Controller;

class AdminActivity extends Controller
{

    /**
     *  分页获取营销活动
     * <AUTHOR>
     * @date 2020/6/18
     * @return array
     */
    public function getActivityList()
    {
        //1：抢购    2：砍价  3：拼团
        $type     = I('type', 0, 'intval');
        $pageNum  = I('pageNum', 1, 'intval');
        $pageSize = I('pageSize', 10, 'intval');
        //搜索活动
        $keyWord = I('keyWord', '', 'strval');

        if (!$type) {
            $this->apiReturn(203, [], "活动类型必传");
        }

        $loginInfoArr = $this->getLoginInfo('ajax');

        $productBiz = new \Business\Cooperator\Activity\Activity();
        $res        = $productBiz->getActivityList($loginInfoArr['sid'], 1, $type, $pageNum, $pageSize, $keyWord);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     *  通过活动id串获取活动列表
     * <AUTHOR>
     * @date 2020/6/23
     * @return array
     */
    public function getActivityListByIds()
    {

        //1：抢购    2：砍价  3：拼团
        $type        = I('type', 1, 'intval');
        $activityIds = I('activityIds', '', 'strval');

        $loginInfoArr = $this->getLoginInfo('ajax');

        $productBiz = new \Business\Cooperator\Activity\Activity();
        $res        = $productBiz->getActivityListByIds($loginInfoArr['sid'], 1, $type, $activityIds);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}
