<?php
/**
 * 优惠券相关模块 -- 微商城所有操作
 * <AUTHOR>
 * @date 2018-11
 *
 */
namespace Controller\Mall;

use Business\Market\ValidateCoupon;
use Library\Cache\Cache;

class Coupon extends Mall {

    /**
     * 用户可以领取的活动列表
     * <AUTHOR>
     * @date 2018-11
     *
     * @param  int page  当前页面
     * @param  int page_size 每页显示数量
     * @param  int activity_id 固定活动的id
     * @param  int give_mode   送券方式：0-手动领取(票券中心)，1-系统发放(弹窗)
     * @param  int show_coupon_num   每个活动显示的优惠券数量
     * @param  int show_type   1:弹窗显示 -- 会进行自动同步发券操作  2:票券中心
     * @return string | json
     *
     */
    public function userCanGetActivityList()
    {
        $page = I('request.page', 1);
        $pageSize = I('request.page_size', 5);
        $activityId = I('request.activity_id', '');
        $giveMode = I('request.give_mode', 0);
        $showCouponNum = I('request.show_coupon_num', 5);
        $showType = I('request.show_type', '');

        // 获取登录用户id
        $memberId = $this->isLogin('ajax');
        $sid = $this->_supplyId;
        if (empty($memberId) || empty($sid) || empty($page) || empty($pageSize)) {
            $this->apiReturn(203, [], '参数错误');
        }

        // 实例化业务方法
        $couponListBiz = new \Business\Market\CouponList();
        $sendCouponBiz = new \Business\Market\SendCoupon();

        // 判断用户是否新用户
        $isNewUser = $this->_isNewUser($memberId);
        if ($isNewUser) {
            $onlyMember = [2,3];   // 新用户和所有用户
        } else {
            $onlyMember = [2,4];   // 老用户和所有用户
        }

        $activityType = [4,5];

        // 弹窗下请求的业务逻辑
        if ($showType == 1) {
            // 弹窗业务逻辑请求 -- 提前给用户发送优惠券
            $autoResArr = $sendCouponBiz->autoSendActivityCoupon($sid, $memberId, $isNewUser, true);
            if ($autoResArr['code'] != 200) {
                $this->apiReturn(200, [], 'success');
            }
            // 设置请求的活动类型
            $activityType = [4];
            $giveMode = 1;
        }
        if ($showType == 2 || empty($showType)) {
            $giveMode = '';
        }

        $listArr = $couponListBiz->userCanGetActivityList($sid, $memberId, $onlyMember, $activityType, $activityId, $giveMode, $showCouponNum, $page, $pageSize);

        $this->apiReturn(200, $listArr['data'], 'success');
    }

    /**
     * 用户可以领取的优惠券列表 -- 通过活动id
     * <AUTHOR>
     * @date 2018-11
     *
     * @param int page           页码
     * @param int page_size      显示条数
     * @param int activity_id    活动id
     * @return string | json
     *
     */
    public function userCanGetCouponList()
    {
        $page = I('request.page', 1);
        $pageSize = I('request.page_size', 2);
        $activityId = I('request.activity_id', '');
        if (empty($activityId) || empty($page) || empty($pageSize)) {
            $this->apiReturn(203, [], '参数有误');
        }

        // 获取登录用户id
        $memberId = $this->isLogin('ajax');
        $sid = $this->_supplyId;

        // 获取活动信息
        $marketModel = new \Model\Market\Market('remote_1');
        $couponNum = $marketModel->countMarketingCouponById($activityId, 1);
        if ($couponNum < 1) {
            $this->apiReturn(200, [], '无优惠券数据');
        }

        $marketCouponInfo = $marketModel->getMarketingCouponByIds([$activityId], 1, $page, $pageSize, 'id, activity_id, coupon_id, coupon_num');
        if (empty($marketCouponInfo)) {
            $this->apiReturn(200, [], '无优惠券数据');
        }

        $marketCouponInfoArr = [];
        foreach ($marketCouponInfo as $item) {
            $marketCouponInfoArr[$item['coupon_id']] = $item;
        }

        // 获取优惠券数据
        $couponIds = array_column($marketCouponInfo, 'coupon_id');

        // 获取优惠券的详情
        $couponModel = new \Model\Product\Coupon('remote_1');
        $couponInfo = $couponModel->getCouponByIds($couponIds, 'coupon_name, id, coupon_value, use_startdate, use_enddate, condition, collect_days, effective_type, limit_ptype, status, pid, remarks');
        if (empty($couponInfo)) {
            $this->apiReturn(200, [], '无优惠券数据');
        }

        // 获取用户已经领取的优惠券
        $memberCouponInfoArr = [];
        $getCouponInfoArr = [];
        $memberCouponInfo = $couponModel->getMemberCouponByFidAndCouponId($memberId, $sid, $couponIds, '', 'id, coupon_id, coupon_code, active_id');
        if (!empty($memberCouponInfo)) {
            foreach ($memberCouponInfo as $memberCouponItem) {
                $memberCouponInfoArr[$memberCouponItem['coupon_id']] = $memberCouponItem;
                if (!isset($getCouponInfoArr[$memberCouponItem['coupon_id']]['get_nums'])) {
                    $getCouponInfoArr[$memberCouponItem['coupon_id']]['get_nums'] = 0;
                }
                $getCouponInfoArr[$memberCouponItem['coupon_id']]['get_nums'] += 1;
            }
        }

        // 整理优惠券详情数据
        $couponInfoArr = [];
        foreach ($couponInfo as $couponItme) {
            $couponInfoArr[$couponItme['id']] = $couponItme;
            if ($couponItme['limit_ptype']) { // 限制产品类型
                $couponInfoArr[$couponItme['id']]['can_use_type'] = 2;
            } elseif ($couponItme['pid']) { // 限制产品id
                $couponInfoArr[$couponItme['id']]['can_use_type'] = 3;
            } else {
                $couponInfoArr[$couponItme['id']]['can_use_type'] = 1;
            }
            $couponInfoArr[$couponItme['id']]['is_get'] = false;
            $couponInfoArr[$couponItme['id']]['coupon_code'] = '';
            if (isset($memberCouponInfo[$couponItme['id']]) && $getCouponInfoArr[$couponItme['id']]['get_nums'] >= $marketCouponInfoArr[$couponItme['id']]['coupon_num']) {
                $couponInfoArr[$couponItme['id']]['is_get'] = true;
                $couponInfoArr[$couponItme['id']]['coupon_code'] = $memberCouponInfo[$couponItme['id']]['coupn_code'];
            }
        }

        $this->apiReturn(200, ['total' => $couponNum, 'list' => $couponInfoArr], 'success');
    }

    /**
     * @date 2018-11-02
     * <AUTHOR>
     *
     * 获取优惠券基本信息
     * @param int    activity_id  活动id
     * @param string coupon_id 优惠券id
     * @return string | json
     *
     */
    public function activityAndCouponInfo()
    {
        $activityId = I('request.activity_id', '');
        $couponId = I('request.coupon_id', '');
        if (empty($activityId) && empty($couponId)) {
            $this->apiReturn(203, [], '参数有误');
        }

        // 获取登录用户id
        $memberId = $this->isLogin('ajax');
        $sid = $this->_supplyId;

        $couponBiz = new \Business\Market\CouponList();
        $couponInfoArr = $couponBiz->couponInfo($couponId, $sid);
        if ($couponInfoArr['code'] != 200) {
            $this->apiReturn($couponInfoArr['code'], [], $couponInfoArr['msg']);
        }
        $couponInfo = $couponInfoArr['data'];

        $activityInfo = [];
        if ($activityId) {
            $marketModel = new \Model\Market\Market('remote_1');
            $activityInfo = $marketModel->findMarketing($activityId, 'id, activity_name, give_mode, activity_type');

            // 获取活动绑定固定券的数据
            $marketCouponInfo = $marketModel->getMarketByIdAndCouponId($activityId, $couponId, 'id, price');
            $activityInfo['price'] = isset($marketCouponInfo['price']) ? $marketCouponInfo['price'] : '';
        }

        $data = [
            'coupon_info' => $couponInfo,
            'activity_info' => $activityInfo,
        ];
        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 订单支付成功后展示用户是否获得或可领取优惠券
     *
     * <AUTHOR>
     * @date   2018-11
     *
     * @param coupon_status 优惠券状态
     * @param page          页码
     * @param page_size     每页显示条数
     * @param order_num     支付完哪笔订单的订单号
     *
     */
    public function paySuccessCanGetCouponList()
    {
        $page = I('request.page', 1, 'intval');
        $pageSize = I('request.page_size', 5, 'intval');
        $orderNum = I('request.order_num', '', 'strval');
        $memberId = $this->isLogin('ajax');
        $sid = $this->_supplyId;

        // 根据订单获取用户参与的活动id 的类型
        if (empty($memberId) || empty($sid) || empty($page) || empty($pageSize) || empty($orderNum)) {
            $this->apiReturn(203, [], '参数有误');
        }

        // 判断订单是否使用的优惠券
        $couponModel = new \Model\Order\Coupon('slave');
        $isUseCoupon = $couponModel->CheckOrderUseCoupon($orderNum);
        if ($isUseCoupon) {
           $this->apiReturn(200, ['total' => 0, 'list' => []], '已使用优惠券下单');
        }

        $orderCouponModel = new \Model\Order\Coupon('remote_1');
        $useLogArr = $orderCouponModel->getUseCouponInfoByTradeId($orderNum, 'fid, aid, use_order_num, all_use_coupon_money,coupon_code');
        if (!empty($useLogArr)) {
            $this->apiReturn(200, ['total' => 0, 'list' => []], '已使用优惠券下单');
        }

        $couponListBiz = new \Business\Market\CouponList();
        $sendCouponBiz = new \Business\Market\SendCoupon();
        $isCanGetCouponOrder = $sendCouponBiz->checkOrderCanGetCoupon($orderNum);
        if (!$isCanGetCouponOrder) {
            $this->apiReturn(200, ['total' => 0, 'list' => []], '已经参与其他活动');
        }

        // 参与下单送券活动
        $isNewUser = $this->_isNewUser($memberId);
        if ($isNewUser) {
            $onlyMember = [2,3];   // 新用户和所有用户
        } else {
            $onlyMember = [2,4];   // 老用户和所有用户
        }

        // 获取商家现在所有进行中的优惠活动  --  默认先发十个进行中的优惠券活动
        $marketModel = new \Model\Market\Market();
        $marketInfoArr = $marketModel->getMarketByOnlyMemberAndStatus($sid, $onlyMember, [5], '', 3, '', '', '', 1, 15, 'id, give_mode,is_only_uper_limit,activity_type');
        $canGetCouponInfoArr = [];
        $activityInfoArr = [];
        if (!empty($marketInfoArr)) {
            foreach ($marketInfoArr as $marketItem) {
                $canGetCouponArr = $couponListBiz->orderCanGetCouponIdByActivity($marketItem['id'], $orderNum);
                if (!empty($canGetCouponArr)) {
                    $canGetCouponInfoArr = array_merge($canGetCouponArr, $canGetCouponInfoArr);
                    $activityInfoArr[$marketItem['id']]['give_mode'] = $marketItem['give_mode'];
                }
            }
        }

        if (empty($canGetCouponInfoArr)) {
            $this->apiReturn(200, ['total' => 0, 'list' => []], '无可获取的优惠券');
        }

        $couponModel = new \Model\Product\Coupon();
        $totalNum = count($canGetCouponInfoArr);
        $canGetCouponArr = array_column($canGetCouponInfoArr, 'coupon_id');
        $canGetCouponList = array_chunk($canGetCouponArr, $pageSize);
        $canGetCouponInfoList = array_chunk($canGetCouponInfoArr, $pageSize);

        $pageKey = $page - 1;
        // 获取当前页需要的优惠券的信息
        // 获取优惠券信息
        $couponInfo = $couponModel->getCouponByIds($canGetCouponList[$pageKey], 'coupon_name, id, coupon_value, use_startdate, use_enddate, condition, collect_days, effective_type, limit_ptype, status, pid, remarks, status');
        if (empty($couponInfo)) {
            $this->apiReturn(200, [], '无优惠券数据');
        }

        // 整理优惠券详情数据
        $couponInfoArr = [];
        foreach ($couponInfo as $couponItme) {
            // 过滤不正常的状态
//            if ($couponItme['status'] != 0) {
//                continue;
//            }

            $couponInfoArr[$couponItme['id']] = $couponItme;
            if ($couponItme['limit_ptype']) { // 限制产品类型
                $couponInfoArr[$couponItme['id']]['can_use_type'] = 2;
            } elseif ($couponItme['pid']) { // 限制产品id
                $couponInfoArr[$couponItme['id']]['can_use_type'] = 3;
            } else {
                $couponInfoArr[$couponItme['id']]['can_use_type'] = 1;
            }

            if ($couponItme['effective_type'] == 0) {
                $couponInfoArr[$couponItme['id']]['use_startdate'] = $couponInfoArr[$couponItme['id']]['use_enddate'] = date('Y-m-d');
            }

            //得到优惠券后多少天有效
            if ($couponItme['effective_type'] == 4) {
                $couponInfoArr[$couponItme['id']]['use_startdate'] = date('Y-m-d', time());
                $couponInfoArr[$couponItme['id']]['use_enddate']   = date('Y-m-d', time() + $couponItme['collect_days']*86400);
            }
        }

        $userCouponListArr['code'] = 200;
        $userCouponListArr['msg'] = 'success';
        $userCouponListArr['data']['total'] = $totalNum;
        $userCouponListArr['data']['list'] = [];

        foreach ($canGetCouponInfoList[$pageKey] as $couponIdItem) {
            if (!isset($couponInfoArr[$couponIdItem['coupon_id']])) {
                continue;
            }

            $isGet = $activityInfoArr[$couponIdItem['activity_id']]['give_mode'];
            $startDate = $couponInfoArr[$couponIdItem['coupon_id']]['use_startdate'];
            $endDate = $couponInfoArr[$couponIdItem['coupon_id']]['use_enddate'];

            // 判断该订单参与活动的优惠券是否已经领取了  -- 手动领取的再次进入的时候需要判断
            $memberCouponInfoArr = $orderCouponModel->getUserCouponByOrdernumAndActiveIdCouponId($orderNum, $couponIdItem['activity_id'], $couponIdItem['coupon_id'], 'id,coupon_id,start_date,end_date');
            if (!empty($memberCouponInfoArr)) {
//                $isGet = 1;
                // 更新用户使用时间
                $startDate = $memberCouponInfoArr[0]['start_date'];
                $endDate = $memberCouponInfoArr[0]['end_date'];
            }
            //判断是否已达上限
            $allCount = $sendCouponBiz->getUserCanGetCouponNum($couponIdItem['activity_id'],$couponIdItem['coupon_id'],$memberId);

            $couponInfoArr[$couponIdItem['coupon_id']]['user_can_get_num'] = $allCount['data']['user_can_get_num'];
            $couponInfoArr[$couponIdItem['coupon_id']]['user_got_num'] = $allCount['data']['user_got_num'];
            $couponInfoArr[$couponIdItem['coupon_id']]['explainStr'] = empty($allCount['explainStr']) ? "" : $allCount['explainStr'];

            $userCouponListArr['data']['list'][] = [
                'coupon_id' => $couponIdItem['coupon_id'],
                'start_date' => $startDate,
                'end_date' => $endDate,
                'active_id' => $couponIdItem['activity_id'],
                'coupon_info' => $couponInfoArr[$couponIdItem['coupon_id']],
                'give_mode' => $isGet,
                'user_can_get_num' => $allCount['data']['user_can_get_num'],
                'user_got_num' => $allCount['data']['user_got_num'],
                'explainStr' => $allCount['explainStr'],
            ];
        }

        if ($userCouponListArr['code'] == 200) {
            $this->apiReturn(200, $userCouponListArr['data'], $userCouponListArr['msg']);
        }
        $this->apiReturn(203, [], $userCouponListArr['msg']);
    }

    /**
     * 用户优惠券列表
     * <AUTHOR>
     * @date 2018-11
     *
     * @param int coupon_status  dstatus : 0 正常 1已过期 2,已使用 3.人工销毁 4.退票回收
     * @param int page 页码
     * @param int page_size 每页显示数量
     * @param string order_num 云票务那边是合并付款订单号
     *
     * @return string | json
     *
     */
    public function userCouponList()
    {
        $couponStatus = I('request.coupon_status', 1);
        $page = I('request.page', 1, 'intval');
        $pageSize = I('request.page_size', 10, 'intval');
        $orderNum = I('request.order_num', '', 'strval');
        $memberId = $this->isLogin('ajax');
        $sid = $this->_supplyId;

        if (empty($memberId) || empty($sid) || empty($page) || empty($pageSize)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $couponListBiz = new \Business\Market\CouponList();
        $userCouponListArr = $couponListBiz->userCouponList($memberId, $sid, $page, $pageSize, $couponStatus, $orderNum);
        if ($userCouponListArr['code'] == 200) {
            $this->apiReturn(200, $userCouponListArr['data'], $userCouponListArr['msg']);
        }
        $this->apiReturn(203, [], $userCouponListArr['msg']);
    }

    /**
     * 用户领取优惠券
     * <AUTHOR>
     * @date 2018-11
     *
     * @param int coupon_id 优惠券id
     * @param int activity_id 发券活动id
     * @return string | json
     *
     */
    public function userGetCoupon()
    {
        $memberId = $this->isLogin('ajax');
        $sid = $this->_supplyId;
        $couponId = I('request.coupon_id', '');
        $activityId = I('request.activity_id', 0);
        $orderNum = I('request.order_num', '');
        if (empty($couponId) || empty($activityId)) {
            $this->apiReturn(203, [], '请求参数有误');
        }

        $sendCouponBiz = new \Business\Market\SendCoupon();

        $validateSendCoupon = new ValidateCoupon();
        $validataRes = $validateSendCoupon->isSendCoupon($activityId, $couponId, $memberId, $sid, $orderNum, 0);
        if($validataRes['code'] != 200){
            $this->apiReturn($validataRes['code'], array(), $validataRes['msg']);
        }

        $validataArr = $validataRes['data'];
        pft_log('coupon/info', "发券校验返回数据------".print_r($validataArr, true).PHP_EOL);
        $userGetRes = $sendCouponBiz->userGetCoupon($activityId, $couponId, $memberId, $sid, $validataArr['getNum'], $orderNum, true, $validataArr['remainNum'], $validataArr['userGotCouponNum'], $validataArr['couponInfo']);
        if ($userGetRes['code'] != 200) {
            $data = isset($userGetRes['data']) ? $userGetRes['data'] : [];
            //检测是否修改活动状态
            $validateSendCoupon->checkMarketingStatusForCouponByActivityIds(array($activityId));
            $this->apiReturn($userGetRes['code'], $data, $userGetRes['msg']);
        }

        // 返回用户领券成功
        $this->apiReturn(200, $userGetRes['data'], '领取成功');
    }

    /**
     * <AUTHOR>
     * @date 2018-11
     *
     * 下单可以使用的优惠券列表  -- 合并一般都是下单同类型的产品,所以限制类型的只要限制总金额
     * @param array info : [tid, pid , count, price, p_type]  数组中 传 tid  pid  数量  单价 产品类型
     * @return string | json
     *
     */
    public function canUseCouponList()
    {
        $info = I('request.info', '');
        $memberId = $this->isLogin('ajax');
        $sid = $this->_supplyId;

        if (empty($memberId) || empty($sid) || empty($info) || !is_array($info)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $totalPrice = 0;
        $priceArr = [];
        // 获取需要下单的参数
        foreach ($info as $item) {
            $totalPrice = $totalPrice + ($item['price'] * $item['count'] * 100);
            // 该产品购买的金额
            $priceArr[$item['pid']]['total_price'] = ($item['price'] * $item['count'] * 100);
        }

        // 下单的tidarr
        $tidArr = array_column($info, 'tid');
        $pidArr = array_column($info, 'pid');
        $ptypeArr = array_column($info, 'p_type');

        $couponModel = new \Model\Product\Coupon();
        $userCouponList = $couponModel->userCouponList($memberId, $sid, 0, 'id desc', '', '', 'coupon_id, start_date, end_date, create_time, use_time, coupon_code, active_id');

        $couponIdArr = array_column($userCouponList, 'coupon_id');
        $couponIdArr = array_unique($couponIdArr);

        // 获取优惠券信息
        $couponInfo = $couponModel->getCouponByIds($couponIdArr, 'coupon_name, id, coupon_value, use_startdate, use_enddate, condition, collect_days, effective_type, limit_ptype, status, pid, remarks, limit_use, condition');
        if (empty($couponInfo)) {
            $this->apiReturn(200, [], '无可用优惠券数据');
        }

        // 获取用户现在占用中的券
        $couponListBiz = new \Business\Market\CouponList();
        $inUserCouponCodeArr = $couponListBiz->getInUseCouponCode($sid, $memberId);

        // 整理优惠券详情数据
        $couponInfoArr = [];
        $maxCouponValArr = [];
        $willExpireCouponArr = [];
        foreach ($couponInfo as $couponItme) {
            // 券金额大于订单总金额的剔除
            if ($couponItme['coupon_value'] > $totalPrice) {
                continue;
            }
            if ($totalPrice < $couponItme['condition']) {
                continue;
            }
            // 剔除下单的产品没有在优惠券限制类型中
            if ($couponItme['limit_ptype'] && !in_array($couponItme['limit_ptype'], $ptypeArr)) {
                continue;
            }
            // 剔除限制产品
            if ($couponItme['pid']) {
                $limitPidArr = explode(',', $couponItme['pid']);
                $sectResArr = array_intersect($pidArr, $limitPidArr);
                if (empty($sectResArr)) {
                    continue;
                }

                // 限制产品的券  剔除大于所有可以使用该券的价格总和
                $pidTotalPrice = 0;
                foreach ($sectResArr as $pid) {
                    $pidTotalPrice += $priceArr[$pid]['total_price'];
                }
                if ($couponItme['coupon_value'] > $pidTotalPrice) {
                    continue;
                }
                if ($couponItme['condition'] > $pidTotalPrice) {
                    continue;
                }
            }

            $couponItme['can_use_type'] = 1;
            if ($couponItme['limit_ptype']) {
                $couponItme['can_use_type'] = 2;
            }
            if ($couponItme['pid']) {
                $couponItme['can_use_type'] = 3;
            }

            $couponInfoArr[$couponItme['id']] = $couponItme;

            if (empty($maxCouponValArr)) {
                $maxCouponValArr = $couponItme;
            }
            if ($couponItme['coupon_value'] > $maxCouponValArr['coupon_value']) {
                $maxCouponValArr = $couponItme;
            }
        }

        if (empty($couponInfoArr)) {
            $this->apiReturn(200, [], '无可用优惠券数据');
        }

        // 用户券列表信息处理

        // 最快过期优惠券获取
        $userCouponArr = [];
        foreach ($userCouponList as $key => $item) {
            if (!isset($couponInfoArr[$item['coupon_id']]) || empty($couponInfoArr[$item['coupon_id']])) {
                continue;
            }

            // 剔除被占用中的优惠券
            if (in_array($item['coupon_code'], $inUserCouponCodeArr)) {
                continue;
            }

            $item['use_startdate'] = $item['start_date'] . " 00:00:00";
            $item['use_enddate'] = $item['end_date'] . " 23:59:59";
            if (strtotime($item['use_startdate']) > time() || time() > strtotime($item['use_enddate'])) {
                continue;
            }

            $item['will_expire'] = false;
            if ($item['end_date']) {
                if ((strtotime($item['end_date']) - 86400 * 3) < strtotime(date('Y-m-d'))) {
                    $item['will_expire'] = true;
                }
            }

            if (empty($willExpireCouponArr)) {
                $willExpireCouponArr = $couponInfoArr[$item['coupon_id']];
                $willExpireCouponArr = array_merge($willExpireCouponArr, $item);
            }
            if (strtotime($item['end_date']) < strtotime($willExpireCouponArr['end_date'])) {
                $willExpireCouponArr = $couponInfoArr[$item['coupon_id']];
                $willExpireCouponArr = array_merge($willExpireCouponArr, $item);
            }

            $userCouponArr[$item['coupon_code']] = $couponInfoArr[$item['coupon_id']];
            $userCouponArr[$item['coupon_code']] = array_merge($userCouponArr[$item['coupon_code']], $item);

            if ($item['coupon_id'] == $maxCouponValArr['id'] && !isset($maxCouponValArr['coupon_code'])) {
                $maxCouponValArr = array_merge($maxCouponValArr, $item);
            }
        }

        if (empty($userCouponArr)) {
            $this->apiReturn(200, [], '无可用优惠券数据');
        }

        // 删除两个特殊的券
        unset($userCouponArr[$maxCouponValArr['coupon_code']]);
        unset($userCouponArr[$willExpireCouponArr['coupon_code']]);

        // 组合券的数组返回
        $data = [];
        if (isset($maxCouponValArr['coupon_code'])) {
            $data[0] = $maxCouponValArr;

            // 如果最大金额最快过期的不是同一个则需要相应增加上去
            if ($maxCouponValArr['coupon_code'] != $willExpireCouponArr['coupon_code']) {
                $data[1] = $willExpireCouponArr;
            }
        } else {
            $data[0] = $willExpireCouponArr;
        }

        $data = array_merge($data, $userCouponArr);
        $data = array_values($data);
        $this->apiReturn(200, $data, 'success');
    }

    /**
     * <AUTHOR>
     * @date 2018-11
     *
     * 根据用户注册时间是否超过7天判断是否是新用户
     * @param $id
     * @return bool
     *
     */
    private function _isNewUser($id)
    {
        if (empty($id)) {
            return false;
        }
        $memberModel = new \Model\Member\Member();
        $memberInfo = $memberModel->getMemberInfo($id, 'id', 'id, creattime');
        $creatTimeStamp = strtotime($memberInfo['creattime']);

        $time = 60*60*24*7;

        if (($creatTimeStamp + $time) > time()) {
            return true;
        }
        return false;
    }

}