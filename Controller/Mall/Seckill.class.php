<?php

/**
 * 抢购活动后台配置控制器
 * 
 * <AUTHOR>
 * @date 2017-07-15
 */

namespace Controller\Mall;

use Business\Wechat\ActivityBase;
use \Library\Controller;
use \Model\Mall\Seckill as SeckillModel;
use \Model\Mall\Poster;
use \Model\Product\Ticket;
use \Business\JavaApi\ProductApi;


class Seckill extends Controller {

    public function __construct() {

        //主账号id
        $this->_sid = $this->isLogin('ajax');

    }


    /**
     * 获取我的抢购活动列表
     * <AUTHOR>
     * @date   2017-07-15
     */
    public function getMySeckillList() {

        $page    = I('page', 1, 'intval');
        $size    = I('size', 10, 'intval');
        $keyword = I('keyword','','strval');

        if ($size > 100) {
            $this->apiReturn(204, [], '非法请求');
        }

        $secKillBiz = new \Business\Wechat\Seckill();
        $result = $secKillBiz->getMySeckillList($this->_sid, $page, $size, $keyword);

        //https图片兼容处理
        if ($result['data']['list']) {
            foreach ($result['data']['list'] as $key => &$value) {
                $value['poster_url'] = images_url_https($value['poster_url']);
            }
        }

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }
    }

    /**
     * 获取产品列表
     * author  leafzl
     * Date: 2018-12-10
     */
    public function getProduct(){
        $keyword = I('keyword','','strval');
        $type    = I('type','A,B,C,D,E,G','strval');
        $activeModel = new ActivityBase();

        $result   = $activeModel->getProTicket($this->_sid,$keyword,$type);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }

    }


    /**
     * 获取供应商的抢购活动列表
     * <AUTHOR>
     * @date   2017-07-15
     */
    public function getSupplySeckillList() {
        $page = I('page', 1, 'intval');
        $size = I('size', 10, 'intval');

        if ($size > 100) {
            $this->apiReturn(204, [], '非法请求');
        }

        $secKillBiz = new \Business\Wechat\Seckill();
        $result = $secKillBiz->getSupplySeckillList($this->_sid, $page, $size);

        //https图片兼容处理
        if ($result['data']['list']) {
            foreach ($result['data']['list'] as $key => &$value) {
                $value['poster_url'] = images_url_https($value['poster_url']);
            }
        }
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }
    }

    /**
     * 获取一场抢购活动的详细信息
     * <AUTHOR>
     * @date   2017-07-15
     */
    public function getSeckill() {
        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $secKillBiz = new \Business\Wechat\Seckill();

        $info = $secKillBiz->getSeckill($id, $this->_sid, '*', true);

        if (!$info || $info['code'] != 200) {
            $this->apiReturn(204, [], '查无此抢购活动');
        }

        $info['cycle_detail'] = json_decode($info['cycle_detail'], true);

        $this->apiReturn(200, $info['data']);
    }

    /**
     * 创建抢购活动
     * <AUTHOR>
     * @date   2017-07-15
     */
    public function create() {

        //参数对象
        $paramsObj = $this->_parseParams();

        $secKillBiz = new \Business\Wechat\Seckill();

        $result = $secKillBiz->create($this->_sid, $paramsObj);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }

    }

    /**
     * 编辑抢购活动
     * <AUTHOR>
     * @date   2017-07-15
     */
    public function update() {

        //抢购活动id
        $secKillId  = I('id', 0, 'intval');

        if (!$secKillId) {
            $this->apiReturn(204, [], '活动id缺失');
        }

        $paramsObj = $this->_parseParams();

        $paramsObj->id = $secKillId;

        $secKillBiz = new \Business\Wechat\Seckill();

        $result = $secKillBiz->update($this->_sid, $paramsObj);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }

    }



    /**
     * 删除抢购活动
     * <AUTHOR>
     * @date   2017-07-18
     * @return [type]     [description]
     */
    public function delete() {
        //抢购活动id
        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        } 

        $secKillBiz = new \Business\Wechat\Seckill();

        $result = $secKillBiz->delete($this->_sid, $id);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }
    }


    ///**
    // * 获取产品列表
    // * <AUTHOR>
    // * @date   2017-07-15
    // */
    //public function getProductList() {
    //    //搜索关键字
    //    $keyword = I('keyword', '', 'strval');
    //
    //    if (!$keyword) {
    //        $this->apiReturn(204, [], '请输入关键字搜索');
    //    }
    //
    //    $secKillBiz = new \Business\Wechat\Seckill();
    //
    //    $result = $secKillBiz->getProductList($this->_sid, $keyword);
    //
    //    if (isset($result['code'])) {
    //        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    //    } else {
    //        $this->apiReturn(204, [], '发生未知错误');
    //    }
    //
    //}


    /**
     * 获取票类的一些属性和价格库存
     * <AUTHOR>
     * @date   2017-10-27
     */
    public function getTicketAttr() {
        //门票pid
        $pid = I('pid', 0, 'intval');
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        //开始日期
        $date = I('date', date('Y-m-d'));

        if (!$pid || !$date) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $secKillBiz = new \Business\Wechat\Seckill();

        $result = $secKillBiz->getTicketAttr($pid, $this->_sid, $aid, $date);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '发生未知错误');
        }

    }


    /**
     * 指定是否已经存在前巩固活动
     * <AUTHOR>
     * @date   2017-11-07
     */
    public function hasInActivity() {
        //门票pid
        $pid = I('pid', 0, 'intval');

        if (!$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $model = new \Model\Mall\Seckill;

        $list = $model->getListByPid($this->_sid, $pid);

        foreach ($list as $item) {

            if ($item['end'] > time()) {
                $this->apiReturn(204, [], '该票类已存在抢购活动,请前往编辑');
            }
        }

        $this->apiReturn(200, [], '不存在');
    }


    /**
     * 解析保存活动的参数
     * <AUTHOR>
     * @date   2017-10-30
     * @return object
     */
    private function _parseParams() {

        //门票pid
        $pid      = I('pid', 0, 'intval');
        //上级供应商id
        $aid      = I('aid', 0, 'intval');
        //开始时间
        $begin    = I('begin', '', 'strtotime');
        //结束时间
        $end      = I('end', '', 'strtotime');
        //是否周期重复
        $isCycle     = I('is_cycle', 0, 'intval');
        //周期类型
        $cycleType   = I('cycle_type', 1, 'intval');
        //周期详情
        $cycleDetail = I('cycle_detail');
        //抢购价格
        $price       = I('price', 0, 'intval');
        //抢购库存
        $storage     = I('storage', 0, 'intval');
        //库存类型
        $storageType = I('storage_type', 1, 'intval');
        //抢购限制
        $buyLimit    = I('buy_limit', 1, 'intval');
        //超时未支付取消时间(分钟)
        $cancelTime  = I('cancel_time', 0, 'intval');
        //海报id
        $posterId    = I('poster_id', 0, 'intval');

        if (!($pid && $aid && $begin && $end && $posterId && $price && $storage && $buyLimit && $cancelTime)) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $object = new \stdClass();

        $object->pid    = $pid;
        $object->aid    = $aid;
        $object->begin  = $begin;
        $object->end    = $end;
        $object->price  = $price;
        $object->storage    = $storage;
        $object->storageType= $storageType;
        $object->buyLimit   = $buyLimit;
        $object->cancelTime = $cancelTime;
        $object->posterId   = $posterId;
        $object->cycleType  = $cycleType;
        $object->isCycle    = $isCycle;
        $object->cycleDetail = json_encode($cycleDetail);

        return $object;
    }

}