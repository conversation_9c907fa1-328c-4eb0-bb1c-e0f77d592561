<?php
/**
 * Created by PhpStorm.
 * User: zhangyangzhen
 * Date: 2018/4/26
 * Time: 9:42
 */

namespace Controller\Mall;

use Business\JavaApi\Tools\verifyCode;
use Business\Member\Login;
use Business\Statistics\HomeOrder;
use function GuzzleHttp\Psr7\str;
use Library\Business\CaptchaCode;
use Library\Business\WechatSmallApp;
use Library\Cache;
use Library\Constants\MemberConst;
use Model\AppCenter\ModuleList;
use Model\BigDataPanel\PftBigAge;
use Model\BigDataPanel\PftBigDistriChannel;
use Model\BigDataPanel\PftBigHourData;
use Model\BigDataPanel\PftBigOrderBook;
use Model\BigDataPanel\PftBigStatistics;
use Model\BigDataPanel\PftBigTouristPlace;
use Model\BigDataPanel\PftBigTripNumbers;
use Model\Member\MemberRelationship;
use Model\Product\Land;
use Model\Report\Statistics;
use Business\Report\BigDataPanel as BigDataPanelBiz;

class BigDataPanel extends Mall
{
    const CODE_UN_LOGIN       = 202;
    const BIG_DATA_PANEL_AUTH = 'big_panel_admin'; //大数据面板权限
    const BIG_DATA_MODULE_ID  = 41; //大数据模块

    private $_redisPreKey = 'bigdata:';
    private $_redis;
    private $_sid; //用户id
    private $todayTotalOrder = 0; //今日预定总数
    private $todayTotalCheck = 0; //今日检票总数

    //线下预订方式 10 云票务  12 自助机  14 闸机  15 智能终端  18 年卡  其他都算成在线预定
    private $_offLineOrderMode = [10, 12, 14, 15, 18];

    public function __construct()
    {
        $this->_redis = Cache\Cache::getInstance('redis');

        $loginInfo  = $this->_getLoginInfo();
        $this->_sid = $loginInfo['sid'];
    }

    /**
     * 小程序账号密码登录
     * Create by zhangyangzhen
     * Date: 2018/5/4
     * Time: 17:59
     */
    public function userPwdLogin()
    {
        $username   = I('post.username', '');
        $password   = I('post.password', '');
        $dtype      = I('post.dtype', false);
        $verifyCode = I('post.auth_code', false);

        $smallLib = new \Library\Business\WechatSmallApp;

        //判断用户名和密码是否为空
        if (empty($username) || empty($password)) {
            $this->apiReturn(self::CODE_NO_CONTENT, [], '用户名或密码不能为空');
        }

        // //小程序这边的的图片验证码要特殊处理
        // if ($verifyCode) {
        //     $verifyCode = $verifyCode . CaptchaCode::$_identifierMark . $username;
        // }

        $loginBiz = new Login();
        $res      = $loginBiz->loginByPasswd($username, md5(md5($password)), MemberConst::PAGE_BIGDATA_SMALLAPP, $dtype, $verifyCode);

        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }

        $data = $res['data'];

        //判断员工账号是否有大数据权限
        if ($data['dtype'] == 6) {
            //判断父级账户是否为管理员
            if ($data['sid'] == 1) {
                $memberAuth = explode(',', $data['member_auth']);
                if (!in_array(self::BIG_DATA_PANEL_AUTH, $memberAuth)) {
                    $this->apiReturn(self::CODE_AUTH_ERROR, [], '该员工账号没有大数据面板权限！');
                }
            } else {
                $moduleListModel = new ModuleList();
                $mres            = $moduleListModel->checkMemberModuleAuth($data['sid'], self::BIG_DATA_MODULE_ID);
                if (!$mres) {
                    $this->apiReturn(self::CODE_AUTH_ERROR, [], '该员工账号没有大数据面板权限！');
                }
            }
        }

        //保存appid和session_key
        $customKey = $smallLib->session_key();
        $smallLib->setSession($customKey, $data);

        $returnData = [
            'sessionKey' => $customKey,
            'expire'     => 1800,
            'data'       => $data,
        ];
        $this->apiReturn(self::CODE_SUCCESS, $returnData, '');
    }

    /**
     * 通过session_key获取缓存信息
     * Create by zhangyangzhen
     * Date: 2018/5/7
     * Time: 10:33
     * @return string
     */
    public function getUserInfoBySessionKey()
    {
        $info = $this->_getLoginInfo();
        if ($info === false) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], 'SESSION-KEY不能为空');
        }
        $this->apiReturn(self::CODE_SUCCESS, $info, '');
    }

    // /**
    //  * 生成验证码方法
    //  * Create by zhangyangzhen
    //  * Date: 2018/5/5
    //  * Time: 15:57
    //  * @throws \Exception
    //  */
    // public function getCode()
    // {
    //     $sessionKey = $_SERVER['HTTP_SESSION_KEY'];
    //     if (empty($sessionKey)) {
    //         $this->apiReturn(self::CODE_NO_CONTENT, [], 'sessionKey不能为空');
    //     }
    //
    //     $str            = $this->random(4); //随机生成的字符串
    //     $wechatSmallApp = new WechatSmallApp();
    //     $wechatSmallApp->setSession($sessionKey, ["auth_code" => strtolower($str)]);
    //     $this->apiReturn(self::CODE_SUCCESS, $str, '');
    // }

    /**
     * 首页数据
     * Create by zhangyangzhen
     * Date: 2018/5/3
     * Time: 17:18
     */
    public function index()
    {
        //登录授权验证
        $this->_auth();

        //昨日预定和检票数
        $yesterdayOrderCheck = $this->getYesterdayOrderAndCheck();
        //获取景区数量/产品数量/供应商数量/分销商数量/旅行社数量
        $statistics = $this->getStatistics();
        //获取年度销售金额 从验证报表中获取
        $saleMoneyYear = $this->getSaleMoneyYear();
        //销售趋势
        $saleTrend = $this->getSaleTrend();
        //今日每小时预定统计量
        $hourOrder = $this->hourOrder();
        //今日每小时检票统计量
        $hourCheck = $this->hourCheck();

        $data = [
            'yesterdayOrderCheck' => $yesterdayOrderCheck,
            'statistics'          => $statistics,
            'saleMoneyYear'       => $saleMoneyYear,
            'saleTrend'           => $saleTrend,
            'hourOrder'           => $hourOrder,
            'hourCheck'           => $hourCheck,
        ];
        $this->apiReturn(self::CODE_SUCCESS, $data, '');
    }

    /**
     * 产品排行
     * Create by zhangyangzhen
     * Date: 2018/5/4
     * Time: 9:06
     * @return array|mixed|string
     */
    public function productTop()
    {
        //登录授权验证
        $this->_auth();

        $data = $this->getProductTop();
        $this->apiReturn(self::CODE_SUCCESS, $data, '');
    }

    /**
     * 客源地分析
     * Create by zhangyangzhen
     * Date: 2018/5/4
     * Time: 9:08
     * @return array|string
     */
    public function touristPlace()
    {
        //登录授权验证
        $this->_auth();

        $data = $this->getTouristPlace();
        $this->apiReturn(self::CODE_SUCCESS, $data, '');
    }

    /**
     * 分销渠道
     * Create by zhangyangzhen
     * Date: 2018/5/4
     * Time: 9:08
     */
    public function otaReseller()
    {
        //登录授权验证
        $this->_auth();

        $data = $this->getOtaReseller();
        $this->apiReturn(self::CODE_SUCCESS, $data, '');
    }

    /**
     * 游客画像（获取出行人数，游客年龄段统计）
     * Create by zhangyangzhen
     * Date: 2018/5/3
     * Time: 15:23
     */
    public function touristData()
    {
        //登录授权验证
        $this->_auth();

        $numbers = $this->getTripNumbers();
        $age     = $this->getAgeData();

        $data = [
            'numbers' => $numbers,
            'age'     => $age,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $data, '');
    }

    /**
     * 预定占比
     * Create by zhangyangzhen
     * Date: 2018/5/4
     * Time: 9:13
     */
    public function bookScale()
    {
        //登录授权验证
        $this->_auth();

        $onlineBook  = $this->getOnlineBook();
        $bookChannel = $this->getBookChannelRank();

        $data = [
            'onlineBook'  => $onlineBook,
            'bookChannel' => $bookChannel,
        ];
        $this->apiReturn(self::CODE_SUCCESS, $data, '');
    }

    /**
     * 获取某个时间段内产品排行
     * Create by zhangyangzhen
     * Date: 2018/4/26
     * Time: 10:02
     * @return array|mixed
     */
    private function getProductTop()
    {
        $begin = I('post.begin');
        $end   = I('post.end');

        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        if ($this->isAdmin()) {
            $result = $this->_getAdminProductTop($begin, $end);
        } else {
            $result = $this->_getUserProductTop($begin, $end);
        }

        return $result;
    }

    /**
     * 管理员产品排行
     * Create by zhangyangzhen
     * Date: 2019/6/20
     * Time: 15:03
     * @return array|mixed
     */
    private function _getAdminProductTop($begin, $end)
    {
        //需要做缓存的一些时间
        $defaultBegin = $time = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', time());
        $sevenAgo     = date('Ymd', strtotime("-7 day"));
        $thisYear     = date('Y', time()) . '0101';

        $key          = $this->_redisPreKey . 'producttop:' . $defaultBegin;
        $lastBeginKey = $this->_redisPreKey . 'producttop:lastbegin:';
        $lastEndKey   = $this->_redisPreKey . 'producttop:lastend';
        $expire       = 3600 * 2;

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间是前7天，最近7天，最近30天，今年的话，走缓存
            if ($begin == $defaultBegin && $end == $defaultEnd) {
                $key = $this->_redisPreKey . 'producttop:' . $defaultBegin;
            } elseif ($begin == $sevenAgo && $end == $defaultEnd) {
                $key = $this->_redisPreKey . 'producttop:' . $sevenAgo;
            } elseif ($begin == $thisYear && $end == $defaultEnd) {
                $key = $this->_redisPreKey . 'producttop:' . $thisYear;
            } else {
                //记录上一次查询时间的数据
                $lastBegin = $this->_redis->get($lastBeginKey);
                $lastEnd   = $this->_redis->get($lastEndKey);

                if ($begin == $lastBegin && $end == $lastEnd) {
                    $key = $this->_redisPreKey . 'producttop:' . $lastBegin . $lastEnd;
                } else {
                    $readCache = false;
                }
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        $this->_redis->set($lastBeginKey, $defaultBegin, '', $expire);
        $this->_redis->set($lastEndKey, $defaultEnd, '', $expire);

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new Statistics();
            //获取总数
            $total = $model->getProductTopByDateTotal($defaultBegin, $defaultEnd);
            $num   = 100000;
            $landData  = [];

            $landModel = new Land();
            //取出的数据 分页取 $i页数 $num 条数
            for ($i = 1; $i <= ceil($total / $num); $i++) {
                $tmpData  = $model->getProductTopByDate($defaultBegin, $defaultEnd, '', $i, $num);
                //取出景区id 资源id映射数据
                $lidArr  = array_column($tmpData, 'lid');
                $javaAPi = new \Business\CommodityCenter\Land();
                $landRes = $javaAPi->queryLandMultiQueryById($lidArr);
                $resourceList = array_column($landRes, 'resourceID', 'id');

                //景区订单数据添加resourceID 参数
                foreach ($tmpData as $k => $value){
                    if(!empty($resourceList[$value["lid"]])){
                        $tmpData[$k]['resourceID'] = $resourceList[$value["lid"]];
                        unset($tmpData[$k]["lid"]); //删掉lid避免数组太大
                    }else{
                        unset($tmpData[$k]);//删掉没有资源的景区
                    }
                }
                $landData = array_merge($landData, $tmpData); //景区订单数据
            }

            $resourceData = [];//初始化数组
            //计算去重
            foreach ($landData as $k => $v){
                $orderNum  = (int)$v['order_num'];
                $ticketNum = (int)$v['ticket_num'];
                if($resourceData[$v["resourceID"]]){
                    $resourceData[$v["resourceID"]]['order_num'] += $orderNum;
                    $resourceData[$v["resourceID"]]['ticket_num'] += $ticketNum;
                }else{
                    $resourceData[$v["resourceID"]] = $v;
                }
            }

            //order_num 排序
            array_multisort(array_column($resourceData,'order_num'), SORT_DESC, $resourceData);

            if(!empty($resourceData)){
                $data = array_slice($resourceData, 0, 50);
                $resourceidArr   = array_column($data, 'resourceID');//资源id

                //获取资源名称
                $ResourceModel = new \Model\Product\LandResource();
                $resourceRes = $ResourceModel->getResourceListByIdArr($resourceidArr, 'id,title');

                //数组重组成旧版本参数
                foreach ($data as $k => $value){
                    if(isset($resourceRes[$value["resourceID"]])){
                        $data[$k]['name'] = (string)$resourceRes[$value["resourceID"]];
                    }else{
                        $data[$k]['name'] = '未知';
                    }
                    $data[$k]['order'] = $value['order_num'];
                    $data[$k]['ticket'] = $value['ticket_num'];
                    unset($data[$k]['resourceID']);
                    unset($data[$k]['order_num']);
                    unset($data[$k]['ticket_num']);
                }

                $data = json_encode($data);

                if ($readCache) {
                    $this->_redis->set($key, $data, '', $expire);
                }
            }
        }

        $result = json_decode($data, true);
        return $result;
    }

    /**
     * 用户产品排行
     * Create by zhangyangzhen
     * Date: 2019/6/20
     * Time: 15:09
     * @param $begin
     * @param $end
     * @return array|mixed
     */
    private function _getUserProductTop($begin, $end)
    {
        //需要做缓存的一些时间
        $defaultBegin = $time = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', time());
        $sevenAgo     = date('Ymd', strtotime("-7 day"));
        $thisYear     = date('Y', time()) . '0101';

        $key          = $this->_redisPreKey . $this->_sid . 'producttop:' . $defaultBegin;
        $lastBeginKey = $this->_redisPreKey . $this->_sid . 'producttop:lastbegin:';
        $lastEndKey   = $this->_redisPreKey . $this->_sid . 'producttop:lastend';
        $expire       = 3600 * 2;

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间是前7天，最近7天，最近30天，今年的话，走缓存
            if ($begin == $defaultBegin && $end == $defaultEnd) {
                $key = $this->_redisPreKey . $this->_sid . 'producttop:' . $defaultBegin;
            } elseif ($begin == $sevenAgo && $end == $defaultEnd) {
                $key = $this->_redisPreKey . $this->_sid . 'producttop:' . $sevenAgo;
            } elseif ($begin == $thisYear && $end == $defaultEnd) {
                $key = $this->_redisPreKey . $this->_sid . 'producttop:' . $thisYear;
            } else {
                //记录上一次查询时间的数据
                $lastBegin = $this->_redis->get($lastBeginKey);
                $lastEnd   = $this->_redis->get($lastEndKey);

                if ($begin == $lastBegin && $end == $lastEnd) {
                    $key = $this->_redisPreKey . $this->_sid . 'producttop:' . $lastBegin . $lastEnd;
                } else {
                    $readCache = false;
                }
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        $this->_redis->set($lastBeginKey, $defaultBegin, '', $expire);
        $this->_redis->set($lastEndKey, $defaultEnd, '', $expire);

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new Statistics();
            //获取总数
            $total = $model->getProductTopByDateTotal($defaultBegin, $defaultEnd, $this->_sid);
            $num   = 100000;
            $landData  = [];

            $landModel = new Land();
            //取出的数据 分页取 $i页数 $num 条数
            for ($i = 1; $i <= ceil($total / $num); $i++) {
                $tmpData  = $model->getProductTopByDate($defaultBegin, $defaultEnd, $this->_sid, $i, $num);
                //取出景区id 资源id映射数据
                $lidArr   = array_column($tmpData, 'lid');
                $javaAPi = new \Business\CommodityCenter\Land();
                $landRes = $javaAPi->queryLandMultiQueryById($lidArr);
                $resourceList = array_column($landRes, 'resourceID', 'id');

                //景区订单数据添加resourceID 参数
                foreach ($tmpData as $k => $value){
                    if(!empty($resourceList[$value["lid"]])){
                        $tmpData[$k]['resourceID'] = $resourceList[$value["lid"]];
                        unset($tmpData[$k]["lid"]); //删掉lid避免数组太大
                    }else{
                        unset($tmpData[$k]);//删掉没有资源的景区
                    }
                }
                $landData = array_merge($landData, $tmpData); //景区订单数据
            }

            $resourceData = [];//初始化数组
            //计算去重
            foreach ($landData as $k => $v){
                $orderNum  = (int)$v['order_num'];
                $ticketNum = (int)$v['ticket_num'];
                if($resourceData[$v["resourceID"]]){
                    $resourceData[$v["resourceID"]]['order_num'] += $orderNum;
                    $resourceData[$v["resourceID"]]['ticket_num'] += $ticketNum;
                }else{
                    $resourceData[$v["resourceID"]] = $v;
                }
            }

            //order_num 排序
            array_multisort(array_column($resourceData,'order_num'), SORT_DESC, $resourceData);

            if(!empty($resourceData)){
                $data = array_slice($resourceData, 0, 50);
                $resourceidArr   = array_column($data, 'resourceID');//资源id

                //获取资源名称
                $ResourceModel = new \Model\Product\LandResource();
                $resourceRes = $ResourceModel->getResourceListByIdArr($resourceidArr, 'id,title');

                //数组重组成旧版本参数
                foreach ($data as $k => $value){
                    if(isset($resourceRes[$value["resourceID"]])){
                        $data[$k]['name'] = (string)$resourceRes[$value["resourceID"]];
                    }else{
                        $data[$k]['name'] = '未知';
                    }
                    $data[$k]['order'] = $value['order_num'];
                    $data[$k]['ticket'] = $value['ticket_num'];
                    unset($data[$k]['resourceID']);
                    unset($data[$k]['order_num']);
                    unset($data[$k]['ticket_num']);
                }

                $data = json_encode($data);

                if ($readCache) {
                    $this->_redis->set($key, $data, '', $expire);
                }
            }
        }

        $result = json_decode($data, true);
        return $result;
    }

    /**
     * 游客来源地分析
     * Create by zhangyangzhen
     * Date: 2018/4/26
     * Time: 13:43
     * @return array
     */
    private function getTouristPlace()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));
        $time         = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        $provinceNameReturn = [];
        $cityNameReturn     = [];

        $model = new PftBigTouristPlace();
        //判断是否为管理员账号
        if ($this->isAdmin()) {
            $key = $this->_redisPreKey . 'place:' . $time;
            if ($readCache) {
                $data = $this->_redis->get($key);
            }

            if (empty($data)) {
                $res = $model->getInfoByDate($defaultBegin, $defaultEnd);

                if (empty($res)) {
                    $res = [];
                }
                $data = json_encode($res);
                if ($readCache) {
                    $this->_redis->set($key, $data, '', 3600 * 24);
                }
            }
        } else {
            $key = $this->_redisPreKey . $this->_sid . 'place:' . $time;
            if ($readCache) {
                $data = $this->_redis->get($key);
            }

            if (empty($data)) {
                $res = $model->getInfoByDate($defaultBegin, $defaultEnd, $this->_sid);

                if (empty($res)) {
                    $res = [];
                }
                $data = json_encode($res);
                if ($readCache) {
                    $this->_redis->set($key, $data, '', 3600 * 24);
                }
            }
        }
        $result = json_decode($data, true);

        $cityName       = load_config('tel_city', 'account');
        $provinceName   = load_config('tel_province', 'account');
        $idCardProvince = load_config('id_card_province', 'account');
        $idCardCity     = load_config('id_card_city', 'account');

        if (!empty($result['province'])) {
            foreach ($result['province'] as $item) {
                if (strlen($item['code']) == 4 && isset($idCardProvince[substr($item['code'], 0, 2)])) {
                    // 因为身份证编码是4位,所以直接判断key
                    $provinceNameReturn[] = [
                        'name'   => $idCardProvince[substr($item['code'], 0, 2)],
                        'num'    => intval($item['num']),
                        'ticket' => intval($item['ticket']),
//                        'per'    => intval($item['ticket']) / $provinceTotalRes
                    ];
                } elseif (isset($provinceName[$item['code']])) {
                    $provinceNameReturn[] = [
                        'name'   => $provinceName[$item['code']],
                        'num'    => intval($item['num']),
                        'ticket' => intval($item['ticket']),
//                        'per'    => intval($item['ticket']) / $provinceTotalRes
                    ];
                }
            }
        }

        if (!empty($result['city'])) {
            foreach ($result['city'] as $item) {
                if (strlen($item['code']) == 4 && isset($idCardCity[$item['code']])) {
                    $cityNameReturn[] = [
                        'name'   => $idCardCity[$item['code']],
                        'num'    => intval($item['num']),
                        'ticket' => intval($item['ticket']),
//                        'per'    => intval($item['ticket']) / $cityTotalRes
                    ];
                } elseif (isset($cityName[$item['code']])) {
                    $cityNameReturn[] = [
                        'name'   => $cityName[$item['code']],
                        'num'    => intval($item['num']),
                        'ticket' => intval($item['ticket']),
//                        'per'    => intval($item['ticket']) / $cityTotalRes
                    ];
                }
            }
        }

        // 因为身份证跟手机号保存的code不同，所以会有2份数据，进行处理整合成一份数据, 因为整理后的数据顺序可能会改变，所以重新进行排序
        $res     = [];
        $sortkey = [];
        foreach ($provinceNameReturn as $province) {
            if (!isset($res[$province['name']])) {
                $res[$province['name']] = $province;
                $sortkey[]              = intval($province['ticket']);
            } else {
                $res[$province['name']]['num'] += intval($province['num']);
                $res[$province['name']]['ticket'] += intval($province['ticket']);
                $sortkey[] = $res[$province['name']]['ticket'] + intval($province['ticket']);
            }
        }
        $provinceNameReturn = array_values($res);
        $provinceNameReturn = $this->twoArraySort($provinceNameReturn, 'ticket');

        $res     = [];
        $sortkey = [];
        foreach ($cityNameReturn as $city) {
            if (!isset($res[$city['name']])) {
                $res[$city['name']] = $city;
                $sortkey[]          = intval($city['ticket']);
            } else {
                $res[$city['name']]['num'] += intval($city['num']);
                $res[$city['name']]['ticket'] += intval($city['ticket']);
            }
        }
        $cityNameReturn = array_values($res);
        $cityNameReturn = $this->twoArraySort($cityNameReturn, 'ticket');

        $res = ['province' => $provinceNameReturn, 'city' => $cityNameReturn];
        return $res;
    }

    /**
     * 获取分销商排行
     * Create by zhangyangzhen
     * Date: 2018/4/26
     * Time: 14:39
     * @return array|string
     */
    private function getOtaReseller()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($this->isAdmin()) {
            $key = $this->_redisPreKey . 'reseller:' . $time;
            if ($readCache) {
                $data = $this->_redis->get($key);
            }

            if (empty($data)) {
                $model = new PftBigDistriChannel();
                $data  = $model->getResellerRankCountTicket($defaultBegin, $defaultEnd);
                $data  = json_encode($data);
                if ($readCache) {
                    $this->_redis->set($key, $data, '', 3600 * 24);
                }
            }

            $statisticsModel = new Statistics();
            $totalRes        = $statisticsModel->getTotalResellerTicket($defaultBegin, $defaultEnd);
        } else {
            $key = $this->_redisPreKey . $this->_sid . 'reseller:' . $time;
            if ($readCache) {
                $data = $this->_redis->get($key);
            }

            if (empty($data)) {
                $model = new PftBigDistriChannel();
                $data  = $model->getResellerRankCountTicket($defaultBegin, $defaultEnd, $this->_sid);
                $data  = json_encode($data);
                if ($readCache) {
                    $this->_redis->set($key, $data, '', 3600 * 24);
                }
            }

            $statisticsModel = new Statistics();
            $totalRes        = $statisticsModel->getTotalResellerTicket($defaultBegin, $defaultEnd, $this->_sid);
        }

        $data = json_decode($data, true);
        foreach ($data as $key => $val) {
            $data[$key]['per'] = sprintf('%.2f', $val['ticket_num'] / $totalRes * 100);
        }

        $res = ['list' => $data, 'total' => $totalRes];
        return $res;
    }

    /**
     * 获取游客同行人数
     * Create by zhangyangzhen
     * Date: 2018/4/26
     * Time: 16:13
     * @return array|string
     */
    private function getTripNumbers()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        $model = new PftBigTripNumbers();
        if ($this->isAdmin()) {
            $key = $this->_redisPreKey . 'trip:' . $time;
            if ($readCache) {
                $data = $this->_redis->get($key);
            }

            if (empty($data)) {
                // 初始化统计变量
                $levelOne = $levelTwo = $levelThree = $levelFour = 0;

                // 将日期字符串转换为时间戳，按天循环遍历
                for ($t = strtotime($defaultBegin); $t <= strtotime($defaultEnd); $t += 86400) {
                    $curDate = date('Ymd', $t);
                    // 查询单日数据并累加（管理员账号不需要传会员ID）
                    $tmpRes = $model->getDataSummaryByTime($curDate, $curDate);

                    $levelOne   += (int)$tmpRes['level_one'];
                    $levelTwo   += (int)$tmpRes['level_two'];
                    $levelThree += (int)$tmpRes['level_three'];
                    $levelFour  += (int)$tmpRes['level_four'];
                }
                
                $total = $levelOne + $levelTwo + $levelThree + $levelFour;
                $count = [
                    'levelOne'   => $levelOne / $total,
                    'levelTwo'   => $levelTwo / $total,
                    'levelThree' => $levelThree / $total,
                    'levelFour'  => $levelFour / $total,
                ];

                $data = json_encode($count);
                if ($readCache) {
                    $this->_redis->set($key, $data, '', 3600 * 24);
                }
            }
        } else {
            $key = $this->_redisPreKey . $this->_sid . 'trip:' . $time;
            if ($readCache) {
                $data = $this->_redis->get($key);
            }

            if (empty($data)) {
                $res = $model->getDataSummaryByTime($defaultBegin, $defaultEnd, $this->_sid);

                $levelOne   = $res['level_one'];
                $levelTwo   = $res['level_two'];
                $levelThree = $res['level_three'];
                $levelFour  = $res['level_four'];

                $total = $levelOne + $levelTwo + $levelThree + $levelFour;
                if ($total) {
                    $count = [
                        'levelOne'   => $levelOne / $total,
                        'levelTwo'   => $levelTwo / $total,
                        'levelThree' => $levelThree / $total,
                        'levelFour'  => $levelFour / $total,
                    ];
                }else{
                    $count = [
                        'levelOne'   => 0,
                        'levelTwo'   => 0,
                        'levelThree' => 0,
                        'levelFour'  => 0,
                    ];
                }

                $data = json_encode($count);
                if ($readCache) {
                    $this->_redis->set($key, $data, '', 3600 * 24);
                }
            }
        }
        $result = json_decode($data, true);
        return $result;
    }

    /**
     * 获取游客年龄
     * Create by zhangyangzhen
     * Date: 2018/4/26
     * Time: 16:17
     * @return array
     */
    private function getAgeData()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $data = [];
        $time = date('Ymd', strtotime("-1 day"));

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        $model = new PftBigAge();
        //判断是否为管理员账号
        if ($this->isAdmin()) {
            $key = $this->_redisPreKey . 'age:' . $time;
            if ($readCache) {
                $data = $this->_redis->get($key);
            }

            if (empty($data)) {
                $data = $model->getDataByTime($defaultBegin, $defaultEnd);
                $data = is_array($data) ? $data : [];
                $data = json_encode($data);
                if ($readCache) {
                    $this->_redis->set($key, $data, '', 3600 * 24);
                }
            }
        } else {
            $key = $this->_redisPreKey . $this->_sid . 'age:' . $time;
            if ($readCache) {
                $data = $this->_redis->get($key);
            }

            if (empty($data)) {
                $data = $model->getDataByTime($defaultBegin, $defaultEnd, $this->_sid);
                $data = is_array($data) ? $data : [];
                $data = json_encode($data);
                if ($readCache) {
                    $this->_redis->set($key, $data, '', 3600 * 24);
                }
            }
        }

        if (empty($data)) {
            $total = 0;
        } else {
            $data  = json_decode($data, true);
            $total = $data['level_one_ticket'] + $data['level_two_ticket'] + $data['level_three_ticket'] + $data['level_four_ticket'] + $data['level_five_ticket'];
        }

        if (empty($total)) {
            $result = [
                //0-6岁
                'level_one'   => 0,
                //7-17岁
                'level_two'   => 0,
                //18-40
                'level_three' => 0,
                //41-65
                'level_four'  => 0,
                //66以后
                'level_five'  => 0,
            ];
        } else {
            $result = [
                //0-6岁
                'level_one'   => $data['level_one_ticket'] / $total,
                //7-17岁
                'level_two'   => $data['level_two_ticket'] / $total,
                //18-40
                'level_three' => $data['level_three_ticket'] / $total,
                //41-65
                'level_four'  => $data['level_four_ticket'] / $total,
                //66以后
                'level_five'  => $data['level_five_ticket'] / $total,
            ];
        }

        return $result;
    }

    /**
     * 获取在线预订占比数（默认统计近7天）
     * Create by zhangyangzhen
     * Date: 2018/5/2
     * Time: 15:25
     * @return array|mixed|string
     */
    private function getOnlineBook()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        $model = new PftBigOrderBook();
        if ($this->isAdmin()) {
            $keyOne = $this->_redisPreKey . 'order:' . $time;
            $keyTwo = $this->_redisPreKey . 'onlinescale:' . $time;
            //提前预定数分析  -- BEGIN
            if ($readCache) {
                $data = $this->_redis->get($keyOne);
            }

            $one         = 0;
            $two         = 0;
            $three       = 0;
            $four        = 0;
            $five        = 0;
            $oneTicket   = 0;
            $twoTicket   = 0;
            $threeTicket = 0;
            $fourTicket  = 0;
            $fiveTicket  = 0;
            $total       = 0;

            if (empty($data)) {
                //从数据库获取
                $data = $model->getDataByTime($defaultBegin, $defaultEnd);

                if (!empty($data)) {
                    //总订单数
                    $total = $data['the_same_day'] + $data['before_one'] + $data['before_three'] + $data['before_seven'] + $data['before_other'];

                    if ($total) {
                        //当天预订 比例（下同）
                        $one = $data['the_same_day'] / $total;
                        //提前一天
                        $two = $data['before_one'] / $total;
                        //提前三天
                        $three = $data['before_three'] / $total;
                        //提前七天
                        $four = $data['before_seven'] / $total;
                        //其他
                        $five = $data['before_other'] / $total;

                        //当天预订 人数 (下同)
                        $oneTicket = $data['the_same_day_ticket'];
                        //提前一天
                        $twoTicket = $data['before_one_ticket'];
                        //提前三天
                        $threeTicket = $data['before_three_ticket'];
                        //提前七天
                        $fourTicket = $data['before_seven_ticket'];
                        //其他
                        $fiveTicket = $data['before_other_ticket'];
                    }
                }

                $count = [
                    'the_same_day'        => $one,
                    'before_one'          => $two,
                    'before_three'        => $three,
                    'before_seven'        => $four,
                    'before_other'        => $five,
                    'the_same_day_ticket' => $oneTicket,
                    'before_one_ticket'   => $twoTicket,
                    'before_three_ticket' => $threeTicket,
                    'before_seven_ticket' => $fourTicket,
                    'before_other_ticket' => $fiveTicket,
                ];

                $data = json_encode($count);
                if ($readCache) {
                    $this->_redis->set($keyOne, $data, '', 3600 * 24);
                }
            }
            $data = json_decode($data, true);
            //提前预定数分析  -- END

            //在线预订比例数分析 -- BEGIN
            if ($readCache) {
                $onLineRes = $this->_redis->get($keyTwo);
            }

            if (empty($onLineRes)) {
                $res = $model->getDataByMode($defaultBegin, $defaultEnd, $this->_offLineOrderMode);

                //线上预定票数
                $num   = $res['ticket'];
                $total = $data['the_same_day_ticket'] + $data['before_one_ticket'] + $data['before_three_ticket'] + $data['before_seven_ticket'] + $data['before_other_ticket'];

                $onLineScale  = 0;
                $onLineOrder  = 0;
                $offLineOrder = 0;

                if ($total) {
                    //在线支付比例
                    $onLineScale = $num / $total;
                    //在线支付票数
                    $onLineOrder = $num;
                    //线下支付票数
                    $offLineOrder = $total - $onLineOrder;
                }

                $onLineRes = [
                    'scale'      => $onLineScale,
                    'on_ticket'  => $onLineOrder,
                    'off_ticket' => $offLineOrder,
                ];

                $onLineRes = json_encode($onLineRes);
                if ($readCache) {
                    $this->_redis->set($keyTwo, $onLineRes, '', 3600 * 24);
                }
            }
        } else {
            $keyOne = $this->_redisPreKey . $this->_sid . 'order:' . $time;
            $keyTwo = $this->_redisPreKey . $this->_sid . 'onlinescale:' . $time;
            //提前预定数分析  -- BEGIN
            if ($readCache) {
                $data = $this->_redis->get($keyOne);
            }

            $one         = 0;
            $two         = 0;
            $three       = 0;
            $four        = 0;
            $five        = 0;
            $oneTicket   = 0;
            $twoTicket   = 0;
            $threeTicket = 0;
            $fourTicket  = 0;
            $fiveTicket  = 0;
            $total       = 0;

            if (empty($data)) {
                //从数据库获取
                $data = $model->getDataByTime($defaultBegin, $defaultEnd, $this->_sid);

                if (!empty($data)) {
                    //总订单数
                    $total = $data['the_same_day'] + $data['before_one'] + $data['before_three'] + $data['before_seven'] + $data['before_other'];

                    if ($total) {
                        //当天预订 比例（下同）
                        $one = $data['the_same_day'] / $total;
                        //提前一天
                        $two = $data['before_one'] / $total;
                        //提前三天
                        $three = $data['before_three'] / $total;
                        //提前七天
                        $four = $data['before_seven'] / $total;
                        //其他
                        $five = $data['before_other'] / $total;

                        //当天预订 人数 (下同)
                        $oneTicket = $data['the_same_day_ticket'];
                        //提前一天
                        $twoTicket = $data['before_one_ticket'];
                        //提前三天
                        $threeTicket = $data['before_three_ticket'];
                        //提前七天
                        $fourTicket = $data['before_seven_ticket'];
                        //其他
                        $fiveTicket = $data['before_other_ticket'];
                    }
                }

                $count = [
                    'the_same_day'        => $one,
                    'before_one'          => $two,
                    'before_three'        => $three,
                    'before_seven'        => $four,
                    'before_other'        => $five,
                    'the_same_day_ticket' => $oneTicket,
                    'before_one_ticket'   => $twoTicket,
                    'before_three_ticket' => $threeTicket,
                    'before_seven_ticket' => $fourTicket,
                    'before_other_ticket' => $fiveTicket,
                ];

                $data = json_encode($count);
                if ($readCache) {
                    $this->_redis->set($keyOne, $data, '', 3600 * 24);
                }
            }

            $data = json_decode($data, true);
            //提前预定数分析  -- END

            //在线预订比例数分析 -- BEGIN
            if ($readCache) {
                $onLineRes = $this->_redis->get($keyTwo);
            }

            if (empty($onLineRes)) {
                $res = $model->getDataByMode($defaultBegin, $defaultEnd, $this->_offLineOrderMode, $this->_sid);

                //线上预定票数
                $num   = $res['ticket'];
                $total = $data['the_same_day_ticket'] + $data['before_one_ticket'] + $data['before_three_ticket'] + $data['before_seven_ticket'] + $data['before_other_ticket'];

                $onLineScale  = 0;
                $onLineOrder  = 0;
                $offLineOrder = 0;

                if ($total) {
                    //在线支付比例
                    $onLineScale = $num / $total;
                    //在线支付票数
                    $onLineOrder = $num;
                    //线下支付票数
                    $offLineOrder = $total - $onLineOrder;
                }

                $onLineRes = [
                    'scale'      => $onLineScale,
                    'on_ticket'  => $onLineOrder,
                    'off_ticket' => $offLineOrder,
                ];

                $onLineRes = json_encode($onLineRes);
                if ($readCache) {
                    $this->_redis->set($keyTwo, $onLineRes, '', 3600 * 24);
                }
            }
        }

        $onLineRes = json_decode($onLineRes, true);
        //在线预定比例
        $data['online_scale'] = $onLineRes['scale'];
        //在线预订人数
        $data['online_ticket'] = $onLineRes['on_ticket'];
        //线下预订人数
        $data['offline_ticket'] = $onLineRes['off_ticket'];
        //总预定人数
        $data['total_ticket'] = $onLineRes['on_ticket'] + $onLineRes['off_ticket'];
        //在线预订比例数分析 -- END

        return $data;
    }

    /**
     * 获取预定渠道排行
     * Create by zhangyangzhen
     * Date: 2018/5/2
     * Time: 16:02
     * @return array|mixed|string
     */
    private function getBookChannelRank()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($this->isAdmin()) {
            $key = $this->_redisPreKey . 'bookchannel:' . $time;
            if ($readCache) {
                $data = $this->_redis->get($key);
            }

            if (empty($data)) {
                $statisticsModel = new Statistics();
                $data            = $statisticsModel->getBookChannelRank($defaultBegin, $defaultEnd);
                $data            = json_encode($data);
                if ($readCache) {
                    $this->_redis->set($key, $data, '', 3600 * 24);
                }
            }
        } else {
            $key = $this->_redisPreKey . $this->_sid . 'bookchannel:' . $time;
            if ($readCache) {
                $data = $this->_redis->get($key);
            }

            if (empty($data)) {
                $statisticsModel = new Statistics();
                $data            = $statisticsModel->getBookChannelRank($defaultBegin, $defaultEnd, $this->_sid);
                $data            = json_encode($data);
                if ($readCache) {
                    $this->_redis->set($key, $data, '', 3600 * 24);
                }
            }
        }
        $data = json_decode($data, true);
        return $data;
    }

    /**
     * 获取销售趋势
     * Create by zhangyangzhen
     * Date: 2018/5/2
     * Time: 16:15
     * @return array
     */
    private function getSaleTrend()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (!strtotime($begin) || !strtotime($end)) {
            $bTimeNew = date('Y-m-d', time() - 3600 * 24 * 6);
            $eTimeNew = date('Y-m-d', time());
        } else {
            $bTimeNew = date('Y-m-d', strtotime($begin));
            $eTimeNew = date('Y-m-d', strtotime($end));
        }

        $homeOrderBusiness = new HomeOrder();
        $bTimeOld          = date('Y-m-d', strtotime('-1 year', strtotime($bTimeNew)));
        $eTimeOld          = date('Y-m-d', strtotime('-1 year', strtotime($eTimeNew)));

        //判断是否为管理员账号
        if ($this->isAdmin()) {
            $num   = $homeOrderBusiness->saleTrends($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, 1);
            $money = $homeOrderBusiness->saleTrends($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, 2);
        } else {
            $num   = $homeOrderBusiness->saleTrends($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, 1, $this->_sid);
            $money = $homeOrderBusiness->saleTrends($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, 2, $this->_sid);
        }

        return ['num' => $num, 'money' => $money];
    }

    /**
     * 获取今天的每小时的预订统计量
     * Create by zhangyangzhen
     * Date: 2018/5/2
     * Time: 16:56
     * @return array
     */
    private function hourOrder()
    {
        $date = I('post.date');
        if (!strtotime($date)) {
            $date = '';
        } else {
            $date = date('Ymd', strtotime($date));
        }

        $result = [];
        $model  = new PftBigHourData();

        if ($this->isAdmin()) {
            $data = $model->getTodayOrderData($date);
            foreach ($data as $item) {
                $result[$item['hour']]['order']  = $item['order'];
                $result[$item['hour']]['ticket'] = $item['ticket'];
            }
        } else {
            //当前日期
            $date = date('Ymd');
            //当前小时数
            $hour = date('H');
            //当前月份
            $month = date('m');
            //当前天
            $day = date('d');

            if ($hour < 6) {
                return [];
            }

            $statisticsModel = new Statistics();
            $key             = $this->_redisPreKey . 'order:' . $this->_sid . ':';
            $cacheTime       = $this->_getTodayRemainTime();

            for ($i = 0; $i <= $hour; $i++) {
                if ($i != $hour) {
                    $tmpKey  = $key . $i;
                    $tmpData = $this->_redis->get($tmpKey);
                } else {
                    $tmpData = "";
                }

                $begin   = mktime($i, 0, 0, $month, $day);
                $end     = mktime($i, 59, 59, $month, $day);

                if (empty($tmpData)) {
                    //从数据库获取
                    $tmpData = $statisticsModel->getInfoByDateAndInsertTimeV2($date, $begin, $end, $this->_sid, 1);
                    $tmpData = json_encode($tmpData);

                    if ($i != $hour) {
                        $this->_redis->set($tmpKey, $tmpData, '', $cacheTime);
                    }
                }

                $tmpData              = json_decode($tmpData, true);
                $result[$i]['order']  = empty($tmpData['order_num']) ? 0 : $tmpData['order_num'];
                $result[$i]['ticket'] = empty($tmpData['ticket_num']) ? 0 : $tmpData['ticket_num'];
            }
        }

        return $result;
    }

    /**
     * 获取今天的每小时的检票统计量
     * Create by zhangyangzhen
     * Date: 2018/5/2
     * Time: 16:57
     * @return array
     */
    private function hourCheck()
    {
        $result = [];
        if ($this->isAdmin()) {
            $date = I('post.date');
            if (!strtotime($date)) {
                $date = '';
            } else {
                $date = date('Ymd', strtotime($date));
            }

            $model = new PftBigHourData();
            $data  = $model->getTodayCheckData($date);
            foreach ($data as $item) {
                $result[$item['hour']]['order']  = $item['order'];
                $result[$item['hour']]['ticket'] = $item['ticket'];
            }
        } else {
            //当前日期
            $date = date('Ymd');
            //当前小时数
            $hour = date('H');
            //当前月份
            $month = date('m');
            //当前天
            $day = date('d');
            if ($hour < 6) {
                return [];
            }

            $statisticsModel = new Statistics();
            $key             = $this->_redisPreKey . 'check:' . $this->_sid . ':';
            $cacheTime       = $this->_getTodayRemainTime();

            for ($i = 0; $i <= $hour; $i++) {
                if ($i != $hour) {
                    $tmpKey  = $key . $i;
                    $tmpData = $this->_redis->get($tmpKey);
                } else {
                    $tmpData = "";
                }

                if (empty($tmpData)) {
                    $begin = mktime($i, 0, 0, $month, $day);
                    $end   = mktime($i, 59, 59, $month, $day);

                    //从数据库获取
                    $tmpData = $statisticsModel->getInfoByDateAndInsertTimeV2($date, $begin, $end, $this->_sid, 2);
                    $tmpData = json_encode($tmpData);

                    if ($i != $hour) {
                        $this->_redis->set($tmpKey, $tmpData, '', $cacheTime);
                    }
                }

                $tmpData              = json_decode($tmpData, true);
                $result[$i]['order']  = empty($tmpData['order_num']) ? 0 : $tmpData['order_num'];
                $result[$i]['ticket'] = empty($tmpData['ticket_num']) ? 0 : $tmpData['ticket_num'];
            }
        }
        return $result;
    }

    /**
     * 获取今天剩余的时间（秒）
     * Create by zhangyangzhen
     * Date: 2019/8/7
     * Time: 14:25
     *
     * @return false|int
     */
    private function _getTodayRemainTime()
    {
        $tomorrow  = date('Y-m-d', strtotime('+1 day'));

        $timestamp = strtotime($tomorrow);

        $time      = $timestamp - time();

        return $time;
    }

    /**
     * 获取昨日预定和检票数
     * Create by zhangyangzhen
     * Date: 2018/5/2
     * Time: 18:07
     * @return array
     */
    private function getYesterdayOrderAndCheck()
    {
        $time = date('Ymd', strtotime('-1 day'));

        $model = new Statistics();

        //判断是否为管理员账号
        if ($this->isAdmin()) {
            $order = $model->getOrderByDate($time);
            $check = $model->getCheckByDate($time);
        } else {
            $order = $model->getOrderByDate($time, $this->_sid);
            $check = $model->getCheckByDate($time, $this->_sid);
        }

        $returnData = [
            'ordernum' => $order,
            'checknum' => $check,
        ];

        return $returnData;
    }

    /**
     * 获取景区数量/产品数量/供应商数量/分销商数量/旅行社数量 (当年累计)
     * Create by zhangyangzhen
     * Date: 2018/5/3
     * Time: 9:34
     * @return mixed
     */
    private function getStatistics()
    {
        $time = date('Ymd', strtotime("-1 day"));

        //判断是否为管理员账号
        if ($this->isAdmin()) {
            $key  = $this->_redisPreKey . 'statistics:' . $time;
            $data = $this->_redis->get($key);

            if (empty($data)) {
                $model = new PftBigStatistics();
                $res   = $model->getData();
                if (!empty($res)) {
                    $orderNumTotal = $res['on_line'] + $res['off_line'];
                    $scale         = $orderNumTotal ? $res['on_line'] / $orderNumTotal : 0;
                    $data          = [
                        //景区数量
                        'land'         => isset($res['land']) ? $res['land'] : 0,
                        //产品数量
                        'product'      => isset($res['product']) ? $res['product'] : 0,
                        //供应商数量
                        'supplier'     => isset($res['supplier']) ? $res['supplier'] : 0,
                        //分销商数量
                        'distributor'  => isset($res['distributor']) ? $res['distributor'] : 0,
                        //旅行社数量
                        'travel'       => isset($res['travel']) ? $res['travel'] : 0,
                        //在线预定比例
                        'online_scale' => $scale,
                    ];

                    $data = json_encode($data);
                    $this->_redis->set($key, $data, '', 3600 * 24);
                }
            }
        } else {
            $key  = $this->_redisPreKey . $this->_sid . 'statistics:' . $time;
            $data = $this->_redis->get($key);

            if (empty($data)) {
                $relationModel = new MemberRelationship();
                $num           = $relationModel->getCountDispatch($this->_sid);

                $data = [
                    'distributor' => $num,
                ];
                $data = json_encode($data);
                $this->_redis->set($key, $data, '', 3600 * 24);
            }
        }

        $result = json_decode($data, true);
        return $result;
    }

    /**
     * 获取年度销售金额 从验证报表中获取
     * Create by zhangyangzhen
     * Date: 2018/5/3
     * Time: 9:41
     * @return int
     */
    private function getSaleMoneyYear()
    {
        $bigDataBiz = new BigDataPanelBiz();

        //判断是否为管理员账号
        if ($this->isAdmin()) {
            $data = $bigDataBiz->getYearSaleMoney($isSuper = true, $memberId = 0);
        } else {
            $data = $bigDataBiz->getYearSaleMoney($isSuper = false, $this->_sid);

        }

        return $data;
    }

    /**
     * 登录授权验证
     * Create by zhangyangzhen
     * Date: 2018/5/17
     * Time: 16:11
     * @return bool|void
     */
    private function _auth()
    {
        $sessionKey = $_SERVER['HTTP_SESSION_KEY'];
        $smallLib   = new \Library\Business\WechatSmallApp;
        $res        = $smallLib->getSession($sessionKey, null);
        if (!$res) {
            $this->apiReturn(self::CODE_UN_LOGIN, [], '请先登录');
        } else {
            $smallLib->setSession($sessionKey, $res);
        }
    }

    /**
     * 生成随机长度
     * <AUTHOR>
     * @dateTime 2018-02-12T10:43:53+0800
     * @throws   \Exception                    可能抛出异常
     * @param    [type]                   $len [description]
     * @return   [type]                        [description]
     */
    private function random($len)
    {
        $srcstr = "ABCDEFGHIJKLMNPQRSTUVWXYZ123456789";
        mt_srand();
        $strs = "";
        for ($i = 0; $i < $len; $i++) {
            $strs .= $srcstr[mt_rand(0, 33)];
        }
        return ($strs);
    }

    /**
     * 两次md5加密密码
     * Create by zhangyangzhen
     * Date: 2018/5/16
     * Time: 10:45
     * @param $password
     * @return string
     */
    private function getMD5Password($password)
    {
        return md5(md5($password));
    }

    /**
     * 获取登录信息
     * Create by zhangyangzhen
     * Date: 2018/5/30
     * Time: 10:13
     * @return array|bool|string
     */
    private function _getLoginInfo()
    {
        $sessionKey = $_SERVER['HTTP_SESSION_KEY'];
        if (empty($sessionKey)) {
            return false;
        }
        $smallLib = new \Library\Business\WechatSmallApp;
        $res      = $smallLib->getSession($sessionKey, null);
        return !empty($res) ? $res : [];
    }

    /**
     * 判断是否为管理员账号
     * Create by zhangyangzhen
     * Date: 2018/5/29
     * Time: 18:06
     * @return bool
     */
    private function isAdmin()
    {
        if ($this->_sid != 1) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 二维数组排序
     * Create by zhangyangzhen
     * Date: 2018/6/4
     * Time: 16:04
     * @param $arrays
     * @param $sort_key
     * @param int $sort_order
     * @param int $sort_type
     * @return bool
     */
    private function twoArraySort($arrays, $sort_key, $sort_order = SORT_DESC, $sort_type = SORT_NUMERIC)
    {
        if (is_array($arrays)) {
            foreach ($arrays as $array) {
                if (is_array($array)) {
                    $key_arrays[] = $array[$sort_key];
                } else {
                    return false;
                }
            }
        } else {
            return false;
        }
        array_multisort($key_arrays, $sort_order, $sort_type, $arrays);
        return $arrays;
    }

    /**
     * 生成图形验证码 (登录输入图形验证码，点击图片刷新用)
     * <AUTHOR>
     * @date   2021-02-22
     */
    public function getImgVerifyCode()
    {
        $account = I('account', '', 'strval,trim');
        $type    = I('type', 1, 'intval');

        if (!$account) {
            $this->apiReturn(203, [], '请输入账号');
        }

        if (!in_array($type ,[1,2])) {
            $this->apiReturn(203, [], '生成图形验证码类型参数错误');
        }
        $result  = (new verifyCode())->getImgVerifyCode($account, $type);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}
