<?php

/**
 * @Author: CYQ19931115
 * @Date:   2018-02-26 11:54:47
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2018-02-27 18:29:41
 */

namespace Controller\Mall;

use Business\Mall as MallBusiness;
use Library\Controller;
use Library\Tools\PftException;

/**
 * 小程序配置页面控制器
 */
class SmallAppConfig extends Controller
{

    private $business;
    private $_moduleId = 59; //小程序模块

    public function __construct()
    {
        $this->business = new MallBusiness\SmallAppConfig();
    }

    /**
     * 保存小程序配置数据
     * <AUTHOR>
     * @dateTime 2018-02-26T13:52:43+0800
     */
    public function saveConfig()
    {
        try {
            $data           = I("post.");
            $data["images"] = I("post.images", '', 'htmlspecialchars_decode');
            $data['aid']    = $this->isLogin("ajax");
            $moduleListMdl = new \Model\AppCenter\ModuleList();
            $checkModule   = $moduleListMdl->checkMemberUseModuleAuth($data['aid'], [$this->_moduleId]);
            if ($checkModule['code'] != 200){
                $this->apiReturn(204, [], "您还没有开通该模块,请联系管里员!");
            }
            /** @var SmallAppConfig 整理前端提交过来的参数 */
            $smallAppConfigData = $this->business->modelParameter($data);
            /** @var boolean 保存数据操作 */
            $result = $this->business->saveData($smallAppConfigData, $data['aid']);
            if ($result) {
                $this->apiReturn(200, [], "保存成功!");
            }
            $this->apiReturn(200, [], "保存失败!");
        } catch (PftException $e) {
            $this->apiReturn(500, [], $e->getMessage());
        } finally {
            $this->apiReturn(500, [], "出现错误");
        }
    }

    /**
     * 获取小程序配置数据
     * <AUTHOR>
     * @dateTime 2018-02-26T13:53:23+0800
     */
    public function getConfig()
    {
        try {
            $aid = $this->isLogin("ajax");
            /** @var SmallAppConfig 配置选项 */
            $result = $this->business->getData($aid);
            $memberModel = new \Model\Member\Member();
            $account = $memberModel->getMemberInfo($aid, 'id', 'id ,account');
            $return = $result->toArray();
            $return['account'] = $account['account'];
            if (!isset($return['images'])) {
                $return['images'] = [];
            }
            $this->apiReturn(200, $return, "获取成功!");
        } catch (PftException $e) {
            $this->apiReturn(500, [], $e->getMessage());
        } finally {
            $this->apiReturn(500, [], "出现错误");
        }
    }
}
