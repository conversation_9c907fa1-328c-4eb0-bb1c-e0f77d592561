<?php
/**
 * 短信，开票中转站
 *
 * User: <PERSON><PERSON><PERSON><PERSON> Li
 * date: 2019-10-12
 */

namespace Controller\Mall;

//class Invoice extends Mall
use Library\Controller;
use Business\ElectronicInvoices\Invoice as invoiceBiz;

class Invoice extends Controller
{

    /**
     * 检测扫码开票信息对应订单是否能开票
     *
     * <AUTHOR> Li
     * @date   019-10-12
     */
    public function checkInvoiceAuth()
    {
        $orderInfo = I('q');
        $source    = I('source', 'platform');
        $sid       = I('sid', 0, 'intval');

        if (empty($orderInfo)) {
            $this->apiReturn(203, [], '开票信息缺失');
        }

        $orderNum = $this->_ordernumDecode($orderInfo);
        if (empty($orderNum)) {
            $this->apiReturn(203, [], '数据解析错误');
        }

        $invoiceApi  = new \Business\ElectronicInvoices\InvoiceApi();
        $cheRes     = $invoiceApi->checkInvoiceAuth([$orderNum], $sid, false, $source);
        $return     = [
            'ordernum'       => $orderNum,  //订单号
            'print_invoices' => false,          //是否能开票  false 不能 true 能
        ];

        if ($cheRes['code'] == 200 && $cheRes['data'][$orderNum]['code'] == 200) {
            $return['sid']            = $cheRes['data'][$orderNum]['sid'];
            $return['tid']            = $cheRes['data'][$orderNum]['tid'];
            $return['print_invoices'] = true;
            $returnMsg                = '可以开票';
            $returnCode               = 200;
        } else {
            $returnMsg  = $cheRes['data'][$orderNum]['msg'];
            $returnCode = 204;
            if ($cheRes['data'][$orderNum]['image_url']) {
                $return['image_url'] = $cheRes['data'][$orderNum]['image_url'];
                $returnCode = 205;
            }
        }

        $this->apiReturn($returnCode, $return, $returnMsg);
    }

    /**
     * 订单号解码
     *
     * @param  [type] $ordernum 订单号
     *
     * @return [type]           [description]
     */
    private function _ordernumDecode($ordernum)
    {
        $ordernum = \Library\MessageNotify\OrderNotify::url_sms_decode($ordernum);

        return $ordernum[0];
    }
}