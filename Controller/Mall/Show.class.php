<?php
/**
 * 微商城演出产品展示相关接口
 * <AUTHOR>
 * @date   2018-01-17
 */

namespace Controller\Mall;

use Business\Product\Show as ShowBiz;
use Controller\Mall\Mall;
use Controller\MicroPlat\Common as MicroCommon;
use Model\Member\Reseller;
use Model\Product\Show as ShowModel;

class Show extends Mall
{
    /**
     * 初始化，判断登录等信息
     * <AUTHOR>
     * @date   2018-01-31
     *
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取场馆分区位置
     * <AUTHOR>
     * @date   2018-01-17
     *
     * @return json
     */
    public function getZoneSeats()
    {
        $venueId = I('post.venue_id', 0, 'intval');
        $zoneId  = I('post.zone_id', 0, 'intval');
        $roundId = I('post.round_id', 0, 'intval');

        if (!$venueId || !$zoneId || !$roundId) {
            $this->apiReturn(203, [], '参数错误');
        }

        //获取具体的数据
        $showBiz = new ShowBiz();

        $res  = $showBiz->getZoneBaseSeats($venueId, $zoneId, $roundId);
        $code = $res['code'];
        $msg  = $res['msg'];

        if ($code != 1) {
            $this->apiReturn(204, [], $msg);
        }

        //返回
        $this->apiReturn(200, $res['data'], '获取成功');
    }

    /**
     *  获取场次座位的状态 - 5秒钟同步一下状态
     * <AUTHOR>
     * @date   2018-01-17
     *
     * @return
     */
    public function getSeatStatus()
    {
        $venueId = I('post.venue_id', 0, 'intval');
        $zoneId  = I('post.zone_id', 0, 'intval');
        $roundId = I('post.round_id', 0, 'intval');
        $seatIds = I('post.seat_ids', '', 'strval');

        $showModel = new ShowModel();
        $roundInfo = $showModel->getRoundInfo($roundId, 'venus_id');
        if (!$roundInfo) {
            return $this->apiReturn(204, '场次信息不存在');
        }

        //判断参数对不对
        if ($venueId != $roundInfo['venus_id']) {
            return $this->apiReturn(204, '场次不属于该场馆');
        }

        $seatIdList = [];
        if ($seatIds) {
            $seatIds    = trim($seatIds, ',');
            $seatIdList = explode(',', $seatIds);
            $seatIdList = array_unique($seatIdList);
        }

        //获取具体座位
        $showBiz = new ShowBiz();
        $res     = $showBiz->getSeatStatus($roundId, $zoneId, $seatIdList);
        $code    = $res['code'];
        $msg     = $res['msg'];

        if ($code != 1) {
            $this->apiReturn(205, [], $msg);
        }

        //TODO:对应C端的话，后期这边的状态需要做下限制
        //状态：0=空闲 1=预留 2=锁定 3=已售出 4=已退 5=不可售

        //返回
        $this->apiReturn(200, $res['data'], '获取成功');
    }

    /**
     * 切换日期，获取相应日期的场次列表
     * <AUTHOR>
     * @date   2018-01-17
     *
     * @return
     */
    public function getRoundList()
    {
        $venueId = I('post.venue_id', 0, 'intval');
        $date    = I('post.date', 0, 'strval');

        if (!$venueId || !$date || !strtotime($date)) {
            $this->apiReturn(203, [], '参数错误');
        }

        //获取指定日期的场次
        $date    = date('Y-m-d', strtotime($date));
        $nowtime = time();

        $showModel = new ShowModel();
        $showBiz   = new \Business\PftShow\Show();
        $roundList = [];

        $tmpList = $showModel->getRoundList($venueId, $field = 'id, round_name, bt, et, round_mode', $status = 0, $date, $date, 'use_date, bt, et');
        foreach ($tmpList as $item) {
            //将过期的场次屏蔽
            if (strtotime($date . ' ' . $item['et']) < $nowtime) {
                continue;
            }

            $roundTimeRes = $showBiz->getRoundTime($item['round_mode'], $item['bt'], $item['et']);
            $roundTime    = '';
            if ($roundTimeRes['code'] == 200) {
                $roundTime = $roundTimeRes['data'];
            }

            $roundList[] = [
                'round_id'   => intval($item['id']),
                'round_name' => $item['round_name'],
                'begin_time' => $item['bt'],
                'end_time'   => $item['et'],
                'round_time' => $roundTime,
            ];
        }

        //返回
        $this->apiReturn(200, $roundList, '获取成功');
    }

    /**
     * 通过门票和场次获取库存
     * <AUTHOR>
     * @date   2018-01-17
     *
     * @return
     */
    public function getStorage()
    {
        $venueId = I('post.venue_id', 0, 'intval');
        $roundId = I('post.round_id', 0, 'intval');

        //类型，1=微商城，2=微平台
        $type = I('post.type', 1, 'intval');

        $aids  = trim(I('post.aids', '', 'strval'), ',');
        $zones = trim(I('post.zones', '', 'strval'), ',');
        $pids  = trim(I('post.pids', '', 'strval'), ',');

        if (!$venueId || !$roundId || !$aids || !$zones || !$pids) {
            $this->apiReturn(203, [], '参数错误');
        }

        $aidList  = explode(',', $aids);
        $zoneList = explode(',', $zones);
        $pidList  = explode(',', $pids);

        $aidNum  = count($aidList);
        $zoneNum = count($zoneList);
        $pidNum  = count($pidList);

        if ($aidNum != $zoneNum || $zoneNum != $pidNum) {
            $this->apiReturn(203, [], '产品和分区不匹配');
        }

        //获取购买用户
        if ($type == 1) {
            //微商城
            $memberId = $this->parseSupplyMemberId();
        } else {
            //微平台
            $microplatLib = new MicroCommon();
            $memberId     = $microplatLib->getMemberId();
        }

        //实例化模型
        $sellerModel = new Reseller();
        $showBiz     = new ShowBiz();

        //返回数据
        $resData = [];

        for ($i = 0; $i < $aidNum; $i++) {
            $aid    = $aidList[$i];
            $zoneId = $zoneList[$i];
            $pid    = $pidList[$i];

            if ($aid && $zoneId && $pid) {
                if ($memberId == $aid) {
                    $topSellerId = $memberId;
                } else {
                    $topSellerId = $sellerModel->getTopResellerId($memberId, $aid, $pid);
                    $topSellerId = $topSellerId ? $topSellerId : $memberId;
                }

                $tmp  = $showBiz->getZoneLeftStorage($roundId, $zoneId, $topSellerId);
                $code = $tmp['code'];

                if ($code == 0) {
                    //获取库存失败
                    pft_log('order_show/error', json_encode(['getStorage', $roundId, $zoneId, $topSellerId, $tmp]));
                    $storage = 0;
                } else {
                    $storage = intval($tmp['data']['storage']);
                }

                $resData[] = [
                    'aid'     => $aid,
                    'pid'     => $pid,
                    'storage' => intval($storage),
                ];
            }
        }

        //返回
        $this->apiReturn(200, $resData, '获取成功');
    }

    /**
     * 获取分区座位和库存等信息
     * <AUTHOR>
     * @date   2018-01-17
     *
     * @return
     */
    public function getRoundZoneInfo()
    {
        $venueId = I('post.venue_id', 0, 'intval');
        $zoneId  = I('post.zone_id', 0, 'intval');
        $roundId = I('post.round_id', 0, 'intval');
        $seats   = I('post.seat_ids', '', 'strval');

        //获取分销库存的参数
        $aid = I('post.aid', 0, 'intval');
        $pid = I('post.pid', 0, 'intval');

        //类型，1=微商城，2=微平台
        $type = I('post.type', 1, 'intval');

        //是否获取所有票类库存信息
        $allTicket = I('post.all_ticket', '');
        if (!$venueId || !$roundId) {
            $this->apiReturn(203, [], '参数错误');
        }

        //获取场次信息
        $showModel = new ShowModel();
        $showBiz   = new ShowBiz();

        $roundInfo = $showModel->getRoundInfo($roundId);
        if (!$roundInfo) {
            $this->apiReturn(204, [], '场次不存在');
        }

        if ($zoneId) {
            //获取分区数据
            $zoneInfo = $showBiz->getZoneBaseInfo($zoneId);

            if (!$zoneInfo) {
                $this->apiReturn(204, [], '分区不存在');
            }

            //获取分区库存
            $topSellerId = 0;

            //获取购买用户
            if ($type == 1) {
                //微商城
                $memberId = $this->parseSupplyMemberId();
            } else {
                //微平台
                $microplatLib = new MicroCommon();
                $memberId     = $microplatLib->getMemberId();
            }

            if ($aid && $pid && $memberId) {
                if ($memberId == $aid) {
                    $topSellerId = $memberId;
                } else {
                    $sellerModel = new Reseller();
                    $topSellerId = $sellerModel->getTopResellerId($memberId, $aid, $pid);
                    $topSellerId = $topSellerId ? $topSellerId : $memberId;
                }
            }
            
            $tmpStorage = $showBiz->getZoneLeftStorage($roundId, $zoneId, $topSellerId);
            if ($allTicket) {
                $tmpStorage = $showBiz->getZoneLeftStorageAll($roundId, $topSellerId);
            }

            $code       = $tmpStorage['code'];

            if ($code == 0) {
                //获取库存失败
                pft_log('order_show/error', json_encode(['getRoundZoneInfo', $roundId, $zoneId, $topSellerId, $tmpStorage]));
                $storage = 0;
            } else {
                $storage = $tmpStorage['data']['storage'];
            }

            //如果有传座位的话，返回座位列表
            $seatList = [];
            if ($seats) {
                $seatIdList = explode(',', $seats);

                $tmpSeatList = $showModel->getSeatsList($venueId, $zoneId, $seatIdList);
                foreach ($tmpSeatList as $item) {
                    $seatList[] = [
                        'seat_id'    => $item['id'],
                        'custom_num' => $item['custom_num'],
                        'entrance'   => $item['custom_pos'],
                    ];
                }
            }
        } else {
            $zoneInfo = ['zone_name' => ''];
            $storage  = [];
            if ($allTicket) {
                $tmpStorage = $showBiz->getZoneLeftStorageAll($roundId);

                if($tmpStorage['code'] == 1) {
                    $storage = $tmpStorage['data']['storage'];
                }
            }
            $seatList = [];
        }

        $showBiz      = new \Business\PftShow\Show;
        $roundTimeRes = $showBiz->getRoundTime($roundInfo['round_mode'], $roundInfo['bt'], $roundInfo['et']);
        $roundTime    = '';
        if ($roundTimeRes['code'] == 200) {
            $roundTime = $roundTimeRes['data'];
        }

        //返回的数据
        $resData = [
            'round_name' => $roundInfo['round_name'],
            'zone_name'  => $zoneInfo['zone_name'],
            'use_date'   => $roundInfo['use_date'],
            'start_time' => $roundInfo['bt'],
            'end_time'   => $roundInfo['et'],
            'round_mode' => $roundInfo['round_mode'],
            'storage'    => $storage,
            'seat_list'  => $seatList,
            'round_time' => $roundTime,
        ];

        $this->apiReturn(200, $resData, '获取成功');
    }

    /**
     * 获取演出场馆库存显示配置
     * <AUTHOR>
     * @date   2019-01-17
     *
     * @return
     */
    public function getShowStockConfig()
    {
        $venueId = I('post.venue_id', 0, 'intval');

        if (!$venueId) {
            $this->apiReturn(203, [], '参数错误');
        }

        $showModel  = new ShowModel();
        $showConfig = $showModel->getShowStockConfig($venueId);

        $this->apiReturn(200, $showConfig, '获取成功');
    }

    /**
     * 查看产品供应商是否开通了微商城选座
     * <AUTHOR>
     * @date   2019-03-29
     */
    public function getOptionalShow(){
        $lid = I('post.lid');
        if(!$lid){
            $this->apiReturn(203, [], '参数错误');
        }

        //判断供应商是否有开启微商城选座
        $check = ShowBiz::checkMallAuth($lid);
        $data = [
            'auth' => $check ? 1 : 0
        ];
        $this->apiReturn(200, $data, '');
    }

    /**
     * 获取场馆全景座位图
     */
    public function getPanoramicSeatsInfo()
    {
        $applyDid = I('post.apply_did', 0, 'intval');
        $venueId  = I('post.venue_id', 0, 'intval');
        $roundId  = I('post.round_id', 0, 'intval');

        if (!$venueId || !$applyDid || !$roundId) {
            $this->apiReturn(203, [], '参数错误');
        }

        $panoramicRes = (new \Business\PftShow\Show())->getPanoramicSeatsInfo($applyDid, $venueId, $roundId);

        $this->apiReturn($panoramicRes['code'], $panoramicRes['data'], $panoramicRes['msg']);
    }
}
