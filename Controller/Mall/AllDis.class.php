<?php
/**
 * 微商城全民分销
 * <AUTHOR>
 */

namespace Controller\Mall;

use Business\JavaApi\Fund\Commission as CommissionFund;
use Business\Mall\AllDis as AllDisBiz;
use Controller\Mall\Mall;
use Endroid\QrCode\QrCode;
use Library\Cache\Cache;
use Library\Constants\DingTalkRobots;
use Library\Tools\Helpers;
use Model\Mall\AllDis as AllDisModel;
use Model\Member\Member;
use Model\Product\Land;
use Model\Subdomain\SubdomainInfo;

class AllDis extends Mall
{
    private $_errMsg = '';

    const CASH_GLOBAL_KEY = 'alldiscash:global:switch';

    /**
     * 获取红包提现列表
     *
     * @param  string beginTime(开始时间),endTime(结束时间),page(当前页),pageSize(每页条数)
     *
     * @return
     */
    public function redpackList()
    {
        $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        if ($loginInfo['dtype'] != 5) {
            $customerId = $loginInfo['customerId'];
            $memberBz   = new \Business\Mall\Member();
            $sankeInfo  = $memberBz->getSankeInfoByMemberId($customerId, 5);
            if ($sankeInfo['code'] != 200) {
                $this->apiReturn($sankeInfo['code'], $sankeInfo['msg']);
            }
            $memberId = $sankeInfo['data']['id'];
        }

        $model = new AllDisModel('remote_1');

        if ($this->memberId != 1) {
            $this->apiReturn(204, [], '您没有查看权限');
        }

        $beginTime = I('beginTime', '', 'strtotime');
        $endTime   = I('endTime', '', 'strtotime');

        if (!$beginTime || !$endTime) {
            $this->apiReturn(204, [], '请选择正确的时间范围');
        }

        $options = [
            'beginTime' => $beginTime,
            'endTime'   => $endTime,
            'page'      => intval(I('page')) ?: 1,
            'pageSize'  => intval(I('pageSize')) ?: 20,
        ];

        $result = $model->getRedPackList($options, true);

        $total_money = $model->getTotalMoney($options);

        $return = [
            'list'        => $result['list'],
            'total'       => (int)$result['total'],
            'total_page'  => ceil($result['total'] / $options['pageSize']),
            'cur_page'    => $options['page'],
            'total_money' => $total_money,
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 推广二维码
     * <AUTHOR>
     * @date   2018-01-17
     */
    public function promotedQrcode()
    {
        $memberId = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        $allDisBiz = new AllDisBiz();
        $name      = $loginInfo['dname'];
        $result    = $allDisBiz->promotedInfo($memberId, $name, $this->_supplyId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口错误');
        }
    }

    /**
     * 输出推广二维码
     * <AUTHOR>
     * @date   2018-02-01
     */
    public function echoQrcode()
    {
        $memberId = $this->isLogin('ajax');

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->promotedQrcode($memberId, $this->_supplyId);

        if (isset($result['code']) && $result['code'] == 200) {
            header('Content-type: image/png');
            $data = file_get_contents($result['data']['url']);
            echo $data;
        } else {
            $this->apiReturn(500, [], '接口错误');
        }
    }

    /**
     * 发送推广二维码到公众号
     * <AUTHOR>
     * @date   2018-01-17
     */
    public function sendPromotedQrcode()
    {
        $memberId = $this->isLogin('ajax');
        $data     = I('image');

        if (!$data || base64_decode(base64_encode($data)) != $data) {
            $this->apiReturn(204, [], '图片数据缺失');
        }
        //获取公众号appid
        $wxOpenModel = new \Model\Wechat\WxOpen();
        $wechat      = $wxOpenModel->getWechatOffiAccInfo($this->_supplyId, 'fid');
        if (!$wechat) {
            $this->apiReturn(204, [], '公众号无权限');
        }
        $appid = $wechat['appid'];

        //获取openid
        $allDisModel = new AllDisModel();
        $memberInfo  = $allDisModel->getAllDisMemberInfo($memberId);
        if (!$memberInfo['supply_openid']) {
            $this->apiReturn(204, [], 'openid参数缺失');
        }

        $err = '';
        try {
            $data    = base64_decode($data);
            $tmpPath = '/tmp/' . time() . "_{$memberId}.png";
            file_put_contents($tmpPath, $data);
            if (!file_exists($tmpPath)) {
                throw new \Exception("文件保存失败");
            }
            //新增临时素材
            $result = \Library\wechat\core\Media::upload($tmpPath, 'image', $appid);
            if (!isset($result['media_id'])) {
                throw new \Exception("图片上传微信服务器失败");
            }
            //调用客服接口发送图片
            $mediaId = $result['media_id'];
            $result  = \Library\wechat\core\CustomService::sendImage($memberInfo['supply_openid'], $mediaId, $appid);

            if (!$result || $result['errcode'] != 0) {
                pft_log('debug/wengbin', json_encode($result), 'day');
                throw new \Exception("发送失败");
            }
        } catch (\Exception $e) {
            $err = $e->getMessage();
        } finally {
            if (isset($tmpPath) && is_file($tmpPath)) {
                @unlink($tmpPath);
            }
            if ($err) {
                $this->apiReturn(204, [], $err);
            } else {
                $this->apiReturn(200, [], '发送成功');
            }
        }
    }

    /**
     * 获取全民分销状态(是否显示开店或者分享按钮)
     *
     * @return void
     */
    public function getAllDisStatus()
    {
        $sid = $this->parseSupplyMemberId();

        $config = (new \Model\Mall\AllDis())->getAllDisConfig($sid);

        $openAllDis = $isAllDisMan = 0;
        //是否开通全民分销
        if ($config && $config['status'] == 1 && $this->inWechatApp()) {
            $openAllDis = 1;
        }
        if (ENV != 'PRODUCTION' && $this->_supplyId == 3385) {
            $openAllDis = 1;
        }

        //是否登录
        $isLogin = $this->isLogin('ajax', false);

        if ($isLogin) {
            $loginInfo = $this->getLoginInfo('ajax', false, false);

            if (!$this->_isSanke($loginInfo['memberID'])) {
                //分销商和供应商登陆不提供开店功能
                $openAllDis = 0;
            } else {
                //已开过店,可分享店铺
                $isAllDisMan = 1;
            }
        } else {
            //还没开店
            $isAllDisMan = 0;
        }

        $data = [
            'openAllDis'  => $openAllDis,
            'isAllDisMan' => $isAllDisMan,
        ];

        $this->apiReturn(200, $data);
    }

    /**
     * 获取店铺分享的二维码
     *
     * @return void
     */
    public function getShareQrcode()
    {
        $memberId = $this->isLogin('ajax');

        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();

        $url      = MOBILE_DOMAIN . 'wx/c/index.html?parentId=' . $memberId;
        $indexUrl = str_replace('wx.', $this->_supplyAccount . '.', $url);

        $qrCode = new QrCode();
        $qrCode->setText($indexUrl)
               ->setSize(160)
               ->setPadding(10)
               ->setErrorCorrection('high')
               ->setForegroundColor(['r' => 0, 'g' => 0, 'b' => 0, 'a' => 0])
               ->setBackgroundColor(['r' => 255, 'g' => 255, 'b' => 255, 'a' => 0])
               ->setLabelFontSize(16)
               ->setImageType('png');

        header('Content-Type: ' . $qrCode->getContentType());
        $qrCode->render();
    }

    /**
     * 批量发放佣金
     * <AUTHOR>
     * @date   2017-10-16
     */
    public function multiIssue()
    {
        $memberId = $this->isLogin('ajax');

        //逗号隔开的订单号列表
        $orders = I('orders');
        $isPromoted = I('isPromoted', 0, 'intval');

        if (!$orders) {
            $this->apiReturn(204, '订单号缺失');
        }

        $orderArr  = explode(',', $orders);
        $allDisBiz = new AllDisBiz();

        $failList = [];
        $response = [
            'bonus_money' => 0,
            'develop_money' => 0,
            'recommend_money' => 0,
            'return_money' => 0,
            'individual_income_tax' => 0,
            'invoicing_service_fee' => 0,
            'pft_invoicing_service_fee' => 0,
        ];
        foreach ($orderArr as $ordernum) {
            $res = $allDisBiz->issueCommission((string)$ordernum, $memberId, 1, $isPromoted);
            if ($res['code'] != 200) {
                $failList[] = sprintf('%s：%s',(string)$ordernum,$res['msg']);
            } else if ($isPromoted) {
                $response['bonus_money'] += $res['data']['bonus_money'];
                $response['develop_money'] += $res['data']['develop_money'];
                $response['recommend_money'] += $res['data']['recommend_money'];
                $response['return_money'] += $res['data']['return_money'];
                $response['individual_income_tax'] += $res['data']['individual_income_tax'];
                $response['invoicing_service_fee'] += $res['data']['invoicing_service_fee'];
                $response['pft_invoicing_service_fee'] += $res['data']['pft_invoicing_service_fee'];
            }
        }

        if ($failList) {
            $orderString = implode('<br/>', $failList);
            $this->apiReturn(204, $failList, '以下订单发放失败：<br/>' . $orderString);
        } else {
            $this->apiReturn(200, $isPromoted ? $response : [], '');
        }
    }

    /**
     * 佣金提现
     *
     * @return array
     */
    public function cash()
    {
        $memberId  = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        $realName = I('realName', '', "strval");
        if (empty($realName)) {
            $this->apiReturn(203, [], "真实姓名缺失");
        }
        //避免重复提现
        $Redis = Cache::getInstance('redis');
        $key   = md5('wechatCash_' . $memberId);
        $lock  = $Redis->lock($key, 1, 5);
        if (!$lock) {
            $this->apiReturn(204, [], '操作频繁,请稍后再试');
        }

        if ($loginInfo['dtype'] != 5) {
            $this->apiReturn(204, [], '当前角色无法提现');
        }

        //全局开关
        $switch = $Redis->get(self::CASH_GLOBAL_KEY);
        if ($switch) {
            $this->apiReturn(204, [], '系统维护');
        }

        $money  = I('money', 0, 'intval');
        $money  = $money * 100;
        $Member = new \Model\Member\Member();

        if ($memberId <= 1) {
            $this->apiReturn(204, [], '非法操作');
        }

        //提现金额控制
        if ($money < 100 || $money > 20000) {
            $this->apiReturn(204, [], '每次提现金额只能在1-200之间');
        }

        $Hour = date('H');
        if ($Hour > 21 || $Hour < 8) {
            $this->apiReturn(204, [], '请在早上八点到晚上十点间进行提现');
        }
        //判断用户状态
        $memberInfo = $Member->getMemberInfo($memberId);
        if ($memberInfo['status'] != 0) {
            $this->apiReturn(204, [], '账户状态异常');
        }
        //判断是否获得过足够的佣金
        $commission = (new \Model\Mall\AllDis())->countCommission($memberId);

        if ($money > $commission) {
            $this->apiReturn(204, [], '你没有获取过足够的佣金');
        }
        //余额判断
        $leftMoney = $Member->getMoney($memberId, 0);
        if ($leftMoney < $money) {
            $this->apiReturn(204, [], '余额不足');
        }
        //供应商id
        $sid = $this->parseSupplyMemberId();
        //如果是内网测试环境，直接把支付状态设置为已经支付
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            //模拟支付
            $cashRes = ['payment_no' => 'online_' . time()];
        } else {
            //真实支付
            if (!isset($_SESSION['openid'])) {
                $this->apiReturn(204, [], 'openid缺失，请退出重试');
            }

            //拉黑
            $blacklist = [
                'oNbmEuIPWVvMuuvAQ9vbNCpWPa0I',
                'oNbmEuIa0RV-hNBRREKGFFW_uMDk',
                'oNbmEuIVztuXsOq9N5zN1Wwc1BqU',
                'oNbmEuE3OYHD_NwdChL7EXwoE0Uk',
                'oNbmEuH-236B1VU_Jemu266UAjC0',
                'oNbmEuBdlBpBziDyeHD5Akb7lvo8',
            ];
            if (in_array($_SESSION['openid'], $blacklist)) {
                $this->apiReturn(204, [], '账号异常');
            }

            //发红包
            $cashRes = $this->_issueRedPack($_SESSION['openid'], $money, $realName);
        }

        if ($cashRes) {
            //提现记录
            (new \Model\Mall\AllDis())->addCashRecord($memberId, $sid, $money, $cashRes['partner_trade_no'],
                $cashRes['payment_no']);
            //扣除余额
            $decRes = $this->_decCashMoney($memberId, $money, $cashRes['payment_no']);
        } else {
            $decRes = false;
        }

        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            pft_log('AllDis', json_encode([$loginInfo['dname'], $money, $cashRes, $decRes]));
        } else {
            //实时通知程序员
            $this->_notifyCoder($loginInfo['dname'], $money, $cashRes, $decRes);
        }

        if ($cashRes) {
            $this->apiReturn(200, [], '提现成功');
        } else {
            $this->apiReturn(204, [], '：' . $this->_showCustomer);
        }
    }

    /**
     * 佣金提现(修复数据用)
     *
     * @return array
     */
    public function cashMove()
    {
        die('无需操作');
    }

    /**
     * 发放微信红包
     *
     * @param  string  $openid  微信openid
     * @param  int  $money  提现金额(分)
     * @param  string  $realName  提现真实姓名
     *
     * @return array
     */
    private function _issueRedPack($openid, $money, $realName)
    {
        $allDisBiz = new AllDisBiz();
        $return    = $allDisBiz->issueRedPack($openid, $money, $realName);
        if ($return['code'] == 200) {
            $result = $return['data'];
            if ($result['return_code'] == 'SUCCESS' && $result['result_code'] == 'SUCCESS') {
                return $result;
            } else {
                $this->_showCustomer = $this->_errMsg = $result['return_msg'] . ':' . $result['err_code_des'];
            }
        }

        return false;
    }

    /**
     * 扣除提现余额
     *
     * @param  int  $memberId  会员id
     * @param  int  $money  提现金额(分)
     *
     * @return bool
     */
    private function _decCashMoney($memberId, $money, $tradeNo)
    {
        //订单号
        //这个订单长度要注意，流水表的长度是只有20
        $orderId = 'cash' . $memberId . time();
        $remark  = '佣金提现';

        $commissionBiz = new CommissionFund();
        $decRes        = $commissionBiz->withdraw($orderId, $tradeNo, $memberId, $money, $remark, 0);

        //记录扣款日志
        pft_log('all_dis/cash', json_encode(['cash', $decRes, $orderId, $tradeNo, $memberId, $money, $remark]));

        return $decRes['code'] == 200 ? true : false;
    }

    /**
     * 提现实时通知程序员
     *
     * @param  string  $dname  用户名
     * @param  int  $money  提现金额(分)
     * @param  bool  $cashRes  红包发放结果
     * @param  bool  $decRes  账户余额扣除结果
     *
     * @return void
     */
    private function _notifyCoder($dname, $money, $cashRes, $decRes)
    {
        $cashRes = $cashRes ? '成功' : '失败';
        $decRes  = $decRes ? '成功' : '失败';
        $robot   = DingTalkRobots::TAKE_CASH_SUCCESS;
        $text    = "全民分销提现申请\n";
        $money   /= 100;
        $text    .= "{$dname}申请提现{$money}元,提现结果:{$cashRes},余额扣除:{$decRes}\n";
        if (!empty($this->_errMsg)) {
            $text  .= $this->_errMsg;
            $robot = DingTalkRobots::TAKE_CASH_FAIL;
        }
        $text .= "\n时间：" . date('m月d日 H点i分s秒') . "\n";
        Helpers::sendDingTalkGroupRobotMessageRaw($text, $robot);
    }

    /**
     * 获取全民分销中心的数据
     *
     * <AUTHOR>
     * @date   2017-01-03
     *
     * @return [type] [description]
     */
    public function getAllDisCenterData()
    {
        $memberId  = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        if ($loginInfo['dtype'] != 5) {
            $customerId = $loginInfo['customerId'];
            $memberBz   = new \Business\Mall\Member();
            $sankeInfo  = $memberBz->getSankeInfoByMemberId($customerId, 5);
            if ($sankeInfo['code'] != 200) {
                $this->apiReturn($sankeInfo['code'], $sankeInfo['msg']);
            }
            $memberId = $sankeInfo['data']['id'];
        }

        $AllDis = new \Model\Mall\AllDis();
        // 当前登录用户是否授权全民分销
        $disMember = $AllDis->getAllDisMemberInfo($memberId, $this->_supplyId);
        if (!$disMember) {
            $this->apiReturn(204, [], '您的账户尚未授权全民分销');
        }

        $allDisBiz  = new AllDisBiz();
        $composeRes = $allDisBiz->commissionCompose($memberId, $this->_supplyId);

        if ($composeRes['code'] !== 200) {
            $this->apiReturn(204, [], '佣金组成解析失败');
        }

        $compose = $composeRes['data'];

        $disInfo = [
            'nickname' => $disMember['nickname'],
            'head_img' => $disMember['head_img'],
            'sumComm'  => array_sum($compose['recommend']) + array_sum($compose['develop']),
            'un_issue' => $compose['recommend']['un_issue'] + $compose['develop']['un_issue'],
        ];

        // 下级分销商列表
        $primaryDis = $AllDis->getPrimaryDisList($memberId, $this->_supplyId);
        $primaryDis = $primaryDis ?: [];

        $dis_list = [];
        foreach ($primaryDis as $key => $dis) {

            $dis_list[$key]['member_id'] = $dis['member_id'];
            $dis_list[$key]['nickname']  = $dis['nickname'];
            $dis_list[$key]['head_img']  = $dis['head_img'];

            // 下级为我创造佣金的订单数量
            $dis_list[$key]['order_count'] = $AllDis->getDisOrderCount(
                $dis['member_id'], $this->_supplyId
            );
            // 下级为我创造介绍佣金
            $tmpRecommend = $AllDis->countCommission(
                $dis['member_id'], $this->_supplyId, 'recommend_money', 'member_id', -1
            );
            //下级为我创造的发展佣金
            $tmpDevelop = $AllDis->countCommission(
                $dis['member_id'], $this->_supplyId, 'develop_money', 'parent_id', -1
            );

            $dis_list[$key]['commission'] = $tmpRecommend + $tmpDevelop;

        }
        //按订单数排序
        usort($dis_list, function ($val1, $val2) {
            if ($val1['order_count'] > $val2['order_count']) {
                return -1;
            } else {
                return 1;
            }
        });
        //自己下单自己获得介绍佣金的订单数量
        $myOrderCount = $AllDis->getMyCommissionOrderCount($memberId, $this->_supplyId);
        //自己下单自己获得介绍佣金的佣金金额
        $myProduceRe = $AllDis->getMyProduceReCommission($memberId, $this->_supplyId);
        $myData      = [
            'member_id'   => $memberId,
            'nickname'    => $disInfo['nickname'],
            'head_img'    => $disInfo['head_img'],
            'order_count' => $myOrderCount,
            'commission'  => $myProduceRe,
        ];
        array_unshift($dis_list, $myData);
        // $disInfo['dis_list'] = $dis_list;
        //普通游客(扫码直接下单，未成为推广员)的佣金数量
        $notFansCommission = $AllDis->getNotFansCommission($memberId, $this->_supplyId);
        if ($notFansCommission) {
            //普通游客(扫码直接下单，未成为推广员)的订单数量
            $notFansOrder = $AllDis->getNotFansOrder($memberId, $this->_supplyId);
            $notFansData  = [
                'member_id'   => -1,
                'nickname'    => '未知游客',
                'head_img'    => 'https://static.12301.cc/images/avatar/touxiang.png',
                'order_count' => $notFansOrder,
                'commission'  => $notFansCommission,
            ];
            array_push($dis_list, $notFansData);
        }

        $disInfo['dis_list'] = $dis_list;
        $this->apiReturn(200, $disInfo, 'ok');
    }

    /**
     * 获取我的一个粉丝的订单列表
     * <AUTHOR>
     * @date   2018-01-16
     */
    public function myOneFansDetail()
    {
        //我的id
        $myId      = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        if ($loginInfo['dtype'] != 5) {
            $customerId = $loginInfo['customerId'];
            $memberBz   = new \Business\Mall\Member();
            $sankeInfo  = $memberBz->getSankeInfoByMemberId($customerId, 5);
            if ($sankeInfo['code'] != 200) {
                $this->apiReturn($sankeInfo['code'], $sankeInfo['msg']);
            }
            $myId = $sankeInfo['data']['id'];
        }

        //当前页数
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');
        //类型 1.总榜 2.月榜
        $type = I('type', 1, 'intval');
        //粉丝id
        $memberId = I('member_id', 0, 'intval');

        if ($memberId == -1) {
            $this->apiReturn(204, [], '未知游客的订单详细有待开放');
        }

        if ($size > 10) {
            $this->apiReturn(204, [], '超出每页条数的限制');
        }

        if (!$memberId) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $biz = new AllDisBiz();
        if ($myId == $memberId) {
            //查看自己下单自己获得介绍佣金的订单
            $result = $biz->myReOrderDetail($this->_supplyId, $myId, $page, $size);
        } else {
            $result = $biz->myOneFansDetail($this->_supplyId, $memberId, $page, $size);
        }

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 推广海报是否存在
     * <AUTHOR>
     * @date   2018-01-17
     */
    public function isPosterExist()
    {
        $type     = I('type', 1, 'intval');
        $identify = I('identify');

        if (!$type && !$identify) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (!$this->_promoter) {
            $this->apiReturn(204, [], '登录后查看海报');
        }

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->isPosterExist($this->_supplyId, $type, $identify);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 获取真实的订单信息(比如套票的主订单号)
     *
     * @param  [type] $ordernum 订单号
     *
     * @return [type]           [description]
     */
    private function _getRealOrderInfo($ordernum)
    {
        $Order = $this->_getStaticModel('ordertools');

        $addon = $Order->getOrderAddonInfo($ordernum);

        if ($addon && $addon['ifpack'] == 2) {
            return $Order->getOrderInfo($addon['pack_order']);
        }

        return false;
    }

    /**
     * 佣金提现记录
     * <AUTHOR>
     * @date   2018-11-20
     */
    public function getCashRecords()
    {
        $memberId = $this->isLogin('ajax');

        $page = I('page', 1, 'intval');
        $size = I('size', 10, 'intval');

        if (!$page || !$size || $size > 100) {
            $this->apiReturn(204, [], '参数错误');
        }

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->getCashRecords($memberId, $this->_supplyId, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取分享记录
     * <AUTHOR>
     * @date   2018-11-22
     */
    public function getShareRecords()
    {
        $memberId  = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        if ($loginInfo['dtype'] != 5) {
            $customerId = $loginInfo['customerId'];
            $memberBz   = new \Business\Mall\Member();
            $sankeInfo  = $memberBz->getSankeInfoByMemberId($customerId, 5);
            if ($sankeInfo['code'] != 200) {
                $this->apiReturn($sankeInfo['code'], $sankeInfo['msg']);
            }
            $memberId = $sankeInfo['data']['id'];
        }

        $page = I('page', 1, 'intval');
        $size = I('size', 10, 'intval');

        if (!$page || !$size || $size > 100) {
            $this->apiReturn(204, [], '参数错误');
        }

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->getShareRecords($memberId, $this->_supplyId, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取订单记录
     * author  leafzl
     * Date: 2018-11-23
     */
    public function getOrderRecords()
    {
        $memberId  = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        if ($loginInfo['dtype'] != 5) {
            $customerId = $loginInfo['customerId'];
            $memberBz   = new \Business\Mall\Member();
            $sankeInfo  = $memberBz->getSankeInfoByMemberId($customerId, 5);
            if ($sankeInfo['code'] != 200) {
                $this->apiReturn($sankeInfo['code'], $sankeInfo['msg']);
            }
            $memberId = $sankeInfo['data']['id'];
        }

        $page        = I('page', 1, 'intval');
        $size        = I('size', 10, 'intval');
        $time        = I('search_date', date('Y-m'));
        $search_type = I('search_type', date('Y-m'));
        $type        = I('type', 1, 'intval');
        $keyword     = I('keyword');

        if (!$page || !$size || $size > 100) {
            $this->apiReturn(204, [], '参数错误');
        }

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->getOrderRecords($memberId, $this->_supplyId, $time, $type, $search_type, $keyword,
            $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 获取返佣记录
     * author  leafzl
     * Date: 2018-11-23
     */
    public function getReBateRecords()
    {
        $memberId  = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        if ($loginInfo['dtype'] != 5) {
            $customerId = $loginInfo['customerId'];
            $memberBz   = new \Business\Mall\Member();
            $sankeInfo  = $memberBz->getSankeInfoByMemberId($customerId, 5);
            if ($sankeInfo['code'] != 200) {
                $this->apiReturn($sankeInfo['code'], $sankeInfo['msg']);
            }
            $memberId = $sankeInfo['data']['id'];
        }

        $page   = I('page', 1, 'intval');
        $size   = I('size', 10, 'intval');
        $time   = I('search_date', date('Y-m'));
        $type   = I('type', 1, 'intval');
        $status = I('status', 1, 'intval');

        if (!$page || !$size || $size > 100) {
            $this->apiReturn(204, [], '参数错误');
        }

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->getReBateRecords($memberId, $this->_supplyId, $time, $type, $status, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 获取返佣的钱
     * author  leafzl
     * Date: 2018-11-23
     */
    public function getReBateMoney()
    {
        $memberId  = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        if ($loginInfo['dtype'] != 5) {
            $customerId = $loginInfo['customerId'];
            $memberBz   = new \Business\Mall\Member();
            $sankeInfo  = $memberBz->getSankeInfoByMemberId($customerId, 5);
            if ($sankeInfo['code'] != 200) {
                $this->apiReturn($sankeInfo['code'], $sankeInfo['msg']);
            }
            $memberId = $sankeInfo['data']['id'];
        }

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->getReBateMoney($memberId, $this->_supplyId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 获取我的粉丝列表
     * author  leafzl
     * Date: 2018-11-23
     */
    public function getMyFansList()
    {
        $memberId  = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        if ($loginInfo['dtype'] != 5) {
            $customerId = $loginInfo['customerId'];
            $memberBz   = new \Business\Mall\Member();
            $sankeInfo  = $memberBz->getSankeInfoByMemberId($customerId, 5);
            if ($sankeInfo['code'] != 200) {
                $this->apiReturn($sankeInfo['code'], $sankeInfo['msg']);
            }
            $memberId = $sankeInfo['data']['id'];
        }

        $page    = I('page', 1, 'intval');
        $size    = I('size', 10, 'intval');
        $type    = I('type', 1, 'intval');
        $keyword = I('keyword', '', 'strval');
        if (!$page || !$size || $size > 100) {
            $this->apiReturn(204, [], '参数错误');
        }

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->getMyFansList($memberId, $this->_supplyId, $type, $keyword, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 获取我的数据
     * author  leafzl
     * Date: 2018-11-23
     */
    public function getMySelf()
    {
        $memberId  = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        if ($loginInfo['dtype'] != 5) {
            $customerId = $loginInfo['customerId'];
            $memberBz   = new \Business\Mall\Member();
            $sankeInfo  = $memberBz->getSankeInfoByMemberId($customerId, 5);
            if ($sankeInfo['code'] != 200) {
                $this->apiReturn($sankeInfo['code'], $sankeInfo['msg']);
            }
            $memberId = $sankeInfo['data']['id'];
        }

        $type = I('type', 1, 'intval');

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->getMySelf($memberId, $this->_supplyId, $type);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 获取我的营销中心
     * author  leafzl
     * Date: 2018-11-26
     */
    public function myCentre()
    {
        $memberId  = $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        if ($loginInfo['dtype'] != 5) {
            $customerId = $loginInfo['customerId'];
            $memberBz   = new \Business\Mall\Member();
            $sankeInfo  = $memberBz->getSankeInfoByMemberId($customerId, 5);
            if ($sankeInfo['code'] != 200) {
                $this->apiReturn($sankeInfo['code'], $sankeInfo['msg']);
            }
            $memberId = $sankeInfo['data']['id'];
        }

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->myCentre($memberId, $this->_supplyId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 更新分享记录的look值
     * author  leafzl
     * Date: 2018-11-26
     */
    public function lookIncrease()
    {
        $lid      = I('lid');
        $parentId = I('parentId');
        $type     = I('type');
        $url      = I('url');
        $content  = I('content');

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->lookIncrease($parentId, $this->_supplyId, $lid, $type, $url, $content);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 模糊查找产品列表
     * author  leafzl
     * Date: 2018-11-29
     */
    public function productList()
    {
        $this->isLogin('ajax');
        $keyWord  = I('keyword');
        //$landModel = new Land();

        $lidArr     = [];
        $title      = '';
        $lettersArr = [];
        if (mb_strlen($keyWord) == strlen($keyWord)) {
            $lettersArr = [$keyWord];
            //英文
            //$searchType = 2;
        } else {
            $title = $keyWord;
            //汉字
            //$searchType = 1;
        }

        $landApi   = new \Business\CommodityCenter\Land();
        $evoluteInfoRes = $landApi->queryLandMultiQueryByAdminAndPaging($lidArr, 1, 200, $title, 'id desc',
            false, [], [], $lettersArr);

        $evoluteInfo = [];
        if ($evoluteInfoRes['list']) {
            $evoluteInfo = $evoluteInfoRes['list'];
        }
        //$evoluteInfo = $landModel->getProductInfoByKeyWord($keyWord, $searchType, 'id, title');
//        foreach ($evoluteInfo as $item) {
//            $data[$item['id']] = $item['title'];
//        }
        $this->apiReturn(200, $evoluteInfo);

    }
    /**
     * 获取全民营销说明
     * <AUTHOR>
     * @date   2018-11-27
     */
    public function getDescription()
    {
        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->getDescription($this->_supplyId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 获取模型方法
     * <AUTHOR>
     * @date   2017-10-16
     *
     * @param  string  $name  模型名称
     *
     * @return object
     */
    private function _getStaticModel($name)
    {
        static $modelMap = [];

        $name = strtolower($name);

        if (!isset($modelMap[$name])) {
            switch ($name) {

                case 'ordertools':
                    $model = new \Model\Order\OrderTools('slave');
                    break;

                case 'alldis':
                    $model = new \Model\Mall\AllDis;
                    break;

                case 'orderquery':
                    $model = new \Model\Order\OrderQuery('slave');
                    break;

                default:
                    exit('error');
            }

            $modelMap[$name] = $model;
        }

        return $modelMap[$name];
    }
}
