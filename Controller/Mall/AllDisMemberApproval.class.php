<?php
/**
 * 审批记录列表
 *
 * User: xujy
 * Date: 2020/4/3
 */

namespace Controller\Mall;

use Library\Controller;

class AllDisMemberApproval extends Controller
{
    /**
     * @var $sid 供应商id
     */
    private $_sid = null;

    public function __construct()
    {
        $this->_sid = $this->isLogin('ajax');
    }

    /**
     *  获取营申请记录列表
     * <AUTHOR>
     * @date 2020/4/3
     * @return array
     */
    public function getAllDisMemberApprovalList()
    {
        $nickName    = I('nickName', '', 'strval');
        $mobile      = I('mobile', '', 'strval');
        $opStatus    = I('opStatus', -1, 'intval');
        $beginTime   = I('beginTime', '', 'strval');
        $endTime     = I('endTime', '', 'strval');
        $page        = I('page', 1, 'intval');
        $pageSize    = I('pageSize', 30, 'intval');
        $approvalBiz = new \Business\Cooperator\AllDis\AllDisMemberApproval();
        $list        = $approvalBiz->getAllDisMemberApprovalList($this->_sid, $nickName, $mobile, $opStatus,
            $beginTime, $endTime, $page, $pageSize);
        $this->apiReturn($list['code'], $list['data'], $list['msg']);
    }

    /**
     *  获取营申请记录信息
     * <AUTHOR>
     * @date 2020/4/3
     * @return array
     */
    public function getAllDisMemberApprovalInfo()
    {
        $id          = I('id', 0, 'intval');
        $approvalBiz = new \Business\Cooperator\AllDis\AllDisMemberApproval();
        $list        = $approvalBiz->getAllDisMemberApprovalInfo($this->_sid, $id);
        $this->apiReturn($list['code'], $list['data'], $list['msg']);
    }

    /**
     *  审批
     * <AUTHOR>
     * @date 2020/4/3
     * @return array
     */
    public function batchCheckApprovalForSupply()
    {

        $ids          = I('ids', []);
        $opStatus     = I('opStatus', 1, 'intval');
        $parentType   = I('parentType', -1, 'intval');
        $parentRoleId = I('parentRoleId', 0, 'intval');
        $roleLevel    = I('roleLevel', 3, 'intval');
        $approvalBiz  = new \Business\Cooperator\AllDis\AllDisMemberApproval();
        $list         = $approvalBiz->batchCheckApprovalForSupply($this->_sid, $ids, $opStatus, $parentType,
            $parentRoleId, $roleLevel);
        $this->apiReturn($list['code'], $list['data'], $list['msg']);
    }
}
