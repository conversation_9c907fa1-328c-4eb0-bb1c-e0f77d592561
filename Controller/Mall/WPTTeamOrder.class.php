<?php
/**
 * 微平台报团订单
 * Created by PhpStorm.
 * User: zhangyangzhen
 * Date: 2019/3/6
 * Time: 18:04
 */

namespace Controller\Mall;

use Business\Member\MemberMoney;
use Business\Member\MemberRelation;
use Business\Order\Modify;
use Business\Product\HandleTicket;
use Business\TeamOrder\Application;
use Business\TeamOrder\Booking as BizBook;
use Business\Product\Show as BizShow;
use Business\TeamOrder\GuideManager as GuideManagerBusiness;
use Business\TeamOrder\TeamConfig;
use Business\TeamOrder\TeamOrderSearch as BizSearch;
use Business\TeamOrder\TeamOrderVerify;
use Library\Cache\CacheRedis;
use Library\Constants\OrderConst;
use Library\Tools\Helpers;
use Model\Member\Guide;
use Model\Member\Member;
use Model\Member\MemberRelationship;
use Model\Member\Reseller;
use Model\Order\OrderRefer;
use Model\Product\Evolute;
use Model\Product\Land;
use Model\Product\PriceGroup;
use Model\Product\Ticket;
use Process\Order\TeamOrder;

class WPTTeamOrder extends SmallApp
{
    const __ORDER_URL__ = MY_DOMAIN . '/api/terminal/terminal_order_v.php';

    private $_loginInfo;    //登录用户信息
    private $_memberId;     //用户id
    private $_sid;          //上级id
    private $_dtype;        //用户类型

    //模型变量
    private $_bizBookModel  = null;
    private $_landModel     = null;
    private $_ticketModel   = null;
    private $_resellerModel = null;
    private $_bizShowModel  = null;
    private $_guideModel    = null;
    private $_bizTeamOrder  = null;
    private $_memberModel   = null;
    private $_evoluteModel  = null;

    //导游信息 redis key
    private $_commonUseKey = 'guide:common:';

    public function __construct()
    {
        $this->_loginInfo = $this->_auth();
        $this->_sid       = $this->_loginInfo['sid'];
        $this->_memberId  = $this->_loginInfo['id'];
        $this->_dtype     = $this->_loginInfo['dtype'];
    }

    /**
     * 获取报团预定产品列表
     * Create by zhangyangzhen
     * Date: 2019/3/7
     * Time: 11:53
     *
     * @param  string  $date  日期
     * @param  int  $aid  上级供应商ID
     * @param  string  $keyword  产品名称关键字
     * @param  int  $page  页码
     * @param  int  $size  每页显示数量
     *
     * @return array
     */
    public function getProductList()
    {
        $date    = I('post.date', '', 'strval,trim');
        $aid     = I('post.aid', 0, 'intval');
        $keyword = I('post.keyword', '', 'strval,trim');
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 10, 'intval');

        if (!$date || !$aid) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '日期或供应商不能为空');
        }

        $bizBookModel = $this->_getBizBookModel();
        $result       = $bizBookModel->productSearch($this->_sid, $aid, $date, $keyword, $page, $size, 2);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取供应商列表
     * Create by zhangyangzhen
     * Date: 2019/3/13
     * Time: 11:00
     *
     * @param  int  $page  页码
     * @param  int  $size  每页显示数量
     * @param  string  $keyword  关键字搜索
     *
     * @return array
     */
    public function getSupplierList()
    {
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 10, 'intval');
        $keyword = I('post.keyword', '', 'strval,trim');
        $relationBiz = new MemberRelation();
        $result      = $relationBiz->getSupplierList($this->_sid, $page, $size, $keyword);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取演出场次信息
     * Create by zhangyangzhen
     * Date: 2019/3/11
     * Time: 11:54
     *
     * @param  int  $pid  产品ID
     * @param  int  $tid  票ID
     * @param  int  $aid  供应商ID
     * @param  string  $date  日期
     *
     * @return array
     */
    public function getShowInfo()
    {
        $lid    = I('post.lid', '', 'intval');
        $tid    = I('post.tid', '', 'intval');
        $aid    = I('post.aid', '', 'intval');
        $date   = I('post.date', '');

        if (!$lid || !$tid || !$aid || !$date) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        $result = $this->_getBizBookModel()->getShowInfo($aid, $this->_sid, $lid, $tid, $date);


        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取常用导游
     * Create by zhangyangzhen
     * Date: 2019/3/11
     * Time: 14:26
     *
     * @return array
     */
    public function getCommonUseGuide()
    {
        $keyword       = I('post.keyword', '', 'strval,trim');
        $sid           = I('post.sid', '', 'intval');
        $guideBusiness = new GuideManagerBusiness();
        $guideArr      = $guideBusiness->getGuideInfo($sid, $this->_sid, $keyword);
        if ($guideArr['code'] != 200) {
            $this->apiReturn($guideArr['code'], [], '无数据');
        }

        $return = [];
        foreach ($guideArr['data'] as $guide) {
            $return[$guide['id']] = [
                'id'         => $guide['id'],
                'mobile'     => $guide['guide_mobile'],
                'name'       => $guide['guide_number'],
                'guide_name' => $guide['guide_name'],
            ];
        }

        $this->apiReturn(self::CODE_SUCCESS, $return);
    }

    /**
     * 提交订单
     * Create by zhangyangzhen
     * Date: 2019/3/11
     * Time: 16:18
     */
    public function submit()
    {
        try {
            $orderData = TeamOrder::submit($this->_memberId);

            $orderData['memberId'] = $this->_sid;
            $verifyBis = new TeamOrderVerify();
            //检测下单是否需要审核
            $chechRes = $verifyBis->checkOrderReview($orderData['sourceId'], $orderData['aid'],
                $orderData['memberId'], $orderData);

            if ($chechRes['code'] == 201) {
                $this->apiReturn(201, [],
                    '已生成报团申请单，报团申请单号：' . $chechRes['data']['audit_id'] . '；可在分销商报团申请管理查询，供应商审核后生效。');
            }

            $app     = new Application();
            $order   = $app->order;
            $prepare = $app->prepare;
            $subData = $prepare->getOrderData($orderData);
            $res     = $order->submit($subData);

            $this->apiReturn(200,
                [
                    'team_order' => $res[0],
                    'qrUrl'      => \Business\TeamOrder\Booking::getTeamQrCode(
                        $subData['team_data']['ordernum'],
                        $subData['team_data']['tnum'],
                        $subData['team_data']['playtime']),
                    'trade_id'   => $res[2]['tradeId'],
                ],
                $res[1]);
        } catch (\Exception $e) {
            if ($e->getCode() == 1001) {
                $return = json_decode($e->getMessage(), true);
                $this->apiReturn($e->getCode(), $return['data'], $return['msg']);
            }

            $this->apiReturn($e->getCode(), [], $e->getMessage());
        }
    }

    /**
     * 分销商报团预订查询
     * Create by zhangyangzhen
     * Date: 2019/3/13
     * Time: 9:48
     *
     * @param  string  $playDate  游玩日期
     * @param  int  $status  订单状态 0=未确认，1=已确认，2=已验证，3=已取消
     * @param  int  $lid  景区ID
     * @param  int  $tid  票类ID
     * @param  int  $page  页码
     * @param  int  $size  每页数量
     * @param  int  $search_type  搜索类型，1=团单，2=子订单
     * @param  string  $ordernum  关键字
     */
    public function getBuyList()
    {
        $playDate   = I('post.play_date', '', 'strval');
        $status     = I('post.status', false);
        $lid        = I('post.lid', false);
        $tid        = I('post.tid', false);
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');
        $payStatus  = I('post.pay_status', false);
        $searchType = I('post.search_type', 2, 'intval');
        $ordernum   = I('post.ordernum', '', 'strval,trim');

        if (empty($playDate) || !strtotime($playDate)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '游玩日期错误');
        }

        $business = $this->_getBizTeamOrder();
        $data     = $business->getTeamOrderList($playDate, $this->_sid, false, $status, $lid, $tid, $payStatus, $page,
            $size, false, $searchType, $ordernum, 0,'',
            '', '', 0, '','','', 2);

        if ($data['data']['list']) {
            $aidArr = array_column($data['data']['list'], 'aid');
            $memberRelationBus = new MemberRelation();
            $res    = $memberRelationBus->getAidClearingModels($aidArr, $this->_sid);

            foreach ($data['data']['list'] as &$value) {
                $aid                  = isset($value['aid']) ? $value['aid'] : 0;
                $value['clearingway'] = isset($res[$aid]) ? $res[$aid] : 0;
                $value['modify_auth'] = $value['buyid'] == $this->_sid ? 1 : 0;
            }
            unset($value);

            $data['data']['list'] = $this->handleTeamOrderList($data['data']['list']);
        }

        $this->apiReturn($data['code'], $data['data'], $data['msg']);
    }

    /**
     * 供应商报团订单查询
     * Create by zhangyangzhen
     * Date: 2019/3/13
     * Time: 10:49
     *
     * @param  string  $playDate  游玩日期
     * @param  int  $status  订单状态 0=未确认，1=已确认，2=已验证，3=已取消
     * @param  int  $lid  景区ID
     * @param  int  $tid  票类ID
     * @param  int  $page  页码
     * @param  int  $size  每页数量
     * @param  int  $fid  分销商ID
     * @param  int  $payStatus  支付状态
     * @param  int  $search_type  搜索类型，1=团单，2=子订单
     * @param  string  $ordernum  关键字
     */
    public function getSaleList()
    {
        $playDate   = I('post.play_date');
        $status     = I('post.status', false);
        $lid        = I('post.lid', false);
        $tid        = I('post.tid', false);
        $page       = I('post.page', 1, 'int');
        $size       = I('post.size', 15, 'int');
        $fid        = I('post.fid', false);
        $payStatus  = I('post.pay_status', false);
        $searchType = I('post.search_type', false);
        $ordernum   = I('post.ordernum', '');

        if (empty($playDate) || !strtotime($playDate)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '游玩日期错误');
        }

        $business = $this->_getBizTeamOrder();
        $data     = $business->getTeamOrderList($playDate, $fid, $this->_sid, $status, $lid, $tid, $payStatus, $page,
            $size, false, $searchType, $ordernum);

        if (!empty($data['data']['list'])) {
            $data['data']['list'] = $this->handleTeamOrderList($data['data']['list']);
            foreach ($data['data']['list'] as &$value) {
                //modify_auth修改权限 0：无  1：有
                $value['modify_auth'] = 0;
                //判断下当前商家有没有权限修改订单信息,登录者是这笔订单原始供应商，有权限又改
                if (isset($value['son']) && $value['son'][0]['apply_did'] == $this->_sid) {
                    $value['modify_auth'] = 1;
                }
            }
            unset($value);
        }

        $this->apiReturn($data['code'], $data['data'], $data['msg']);
    }

    /**
     * 团单列表处理
     * Create by zhangyangzhen
     * Date: 2019/3/18
     * Time: 18:24
     *
     * @param $data
     *
     * @return mixed
     */
    private function handleTeamOrderList($data)
    {
        if (!empty($data)) {
            foreach ($data as $key => $val) {
                if ($val['tnum'] < 0) {
                    $data[$key]['tnum'] = 0;
                }

                $totalMoney = 0;
                if (!empty($val['son'])) {
                    foreach ($val['son'] as $k => $v) {
                        $totalMoney += $v['total_money'];
                    }
                }
                $data[$key]['totalMoney'] = $totalMoney;
            }
        }

        return $data;
    }

    /**
     * 获取查询条件的可变信息
     * Create by zhangyangzhen
     * Date: 2019/3/13
     * Time: 11:53
     *
     * @param  int  $type  搜索类型，1=产品，2=票类，3=分销商
     * @param  string  $keyWord  关键字搜索
     * @param  int  $land_id  景区ID
     */
    public function getSearchConfigOther()
    {
        $type    = I('post.type', 0, 'intval');
        $keyWord = I('post.key_word', '', 'strval,trim');

        if (empty($keyWord)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入关键字进行搜索');
        }

        if ($type <= 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $data = [];
        if ($this->_sid == 1) {
            //管理员
            switch ($type) {
                case 1:
                    //$landModel = $this->_getLandModel();
                    //if (mb_strlen($keyWord) == strlen($keyWord)) {
                    //    //英文
                    //    $searchType = 2;
                    //} else {
                    //    //汉字
                    //    $searchType = 1;
                    //}
                    ////产品id
                    //if (is_numeric($keyWord)) {
                    //    $searchType = 3;
                    //}
                    //$evoluteInfo = $landModel->getProductInfoByKeyWord($keyWord, $searchType, 'id, title');

                    $lidArr     = [];
                    $title      = '';
                    $lettersArr = [];
                    if (mb_strlen($keyWord) == strlen($keyWord)) {
                        $lettersArr = [$keyWord];
                        //英文
                        //$searchType = 2;
                    } else {
                        $title = $keyWord;
                        //汉字
                        //$searchType = 1;
                    }
                    //产品id
                    if (is_numeric($keyWord)) {
                        $lidArr     = [$keyWord];
                        //$searchType = 3;
                    }

                    $landApi   = new \Business\CommodityCenter\Land();
                    $evoluteInfo = $landApi->queryLandMultiQueryByAdminAndPaging($lidArr, 1, 200, $title, 'id desc',
                        false, [], [], $lettersArr);

                    if ($evoluteInfo['list']) {
                        foreach ($evoluteInfo['list'] as $item) {
                            $data[$item['id']] = $item['title'];
                        }
                    }

                    break;

                case 2:
                    //景区ID
                    $lid         = I('post.land_id');
                    $javaApi = new \Business\CommodityCenter\Ticket();
                    $ticketArr = $javaApi->queryTicketInfoByApplyDidAndLandIdAndPTypeAndApplyLimitAndPStatusAndStatus(null, [$lid], 'id,title', '', '', '', null, null, null, null, null, $keyWord,1, 200);
                    $evoluteInfo = array_column($ticketArr['data']['list'], 'ticket');

                    foreach ($evoluteInfo as $item) {
                        $data[$item['id']] = $item['title'];
                    }
                    break;
                case 3:
                    if (mb_strlen($keyWord) == strlen($keyWord)) {
                        //英文
                        $searchType = 2;
                    } else {
                        //汉字
                        $searchType = 1;
                    }
                    $memberModel = $this->_getMemberModel();
                    $memberInfo  = $memberModel->getUserInfoByKeyWord($keyWord, $searchType, 'id,account, dname');

                    $data = $memberInfo;
                    break;
            }
        } else {
            switch ($type) {
                case 1:
                    $evoluteModel = $this->_getEvoluteModel();
                    if (mb_strlen($keyWord) == strlen($keyWord)) {
                        //英文
                        $searchType = 2;
                    } else {
                        //汉字
                        $searchType = 1;
                    }
                    //产品id
                    if (is_numeric($keyWord)) {
                        $searchType = 3;
                    }
                    $data = $evoluteModel->getInfoByFidAndLandKeyTitle($this->_sid, $keyWord, $searchType);

                    break;

                case 2:
                    //景区ID
                    $lid          = I('post.land_id');
                    $evoluteModel = $this->_getEvoluteModel();

                    $evoluteInfo = $evoluteModel->getInfoByFidAndLandIdAndKeyTitle($this->_sid, $lid, $keyWord);
                    foreach ($evoluteInfo as $item) {
                        $data[$item['id']] = $item['title'];
                    }
                    break;
                case 3:
                    if (mb_strlen($keyWord) == strlen($keyWord)) {
                        //英文
                        $searchType = 2;
                    } else {
                        //汉字
                        $searchType = 1;
                    }
                    $memberRelationShipModel = new MemberRelationship();
                    $memberRelationShipInfo  = $memberRelationShipModel->getResellerInfoByFidAndTitle($this->_sid,
                        $keyWord, false, $searchType);

                    $data = $memberRelationShipInfo;
                    break;
            }
        }

        $this->apiReturn(200, $data);
    }

    /**
     * 报团订单修改订单
     * Create by zhangyangzhen
     * Date: 2019/3/14
     * Time: 14:44
     *
     * @param  int  $ordernum  订单号
     * @param  array  $num_mapping  修改后的门票数量映射
     * @param  string  $teamorder  报团订单号
     *
     * @return array
     */
    public function numModify()
    {
        $ordernum   = I('post.ordernum');
        $numMapping = I('post.num_mapping');
        $teamOrder  = I('post.team_order', '', 'strval');

        //团单身份证信息  新增 [['tourist' => '姓名', 'idcard' => '身份证号']]  修改传['tourist' => '','idcard' => 12356456123123312]
        $touristList = I('post.tourist_list', []);

        if (!$ordernum || !$numMapping) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数缺失');
        }

        $business = new Modify();

        //增加已确认判断
        $app       = new Application();
        $order     = $app->order;
        $orderData = $app->order_class->getTeamModel()->getMainOrderInfoByOrderNum($teamOrder);

        if (!in_array($orderData['status'], [1, 4]) && $orderData['aid'] == $this->_sid) {
            $this->apiReturn(205, [], '团队订单未确认，无法修改');
        }

        $isChangeTeam  = false;
        $channelCancel = OrderConst::WECHAT_PLATFORM_CANCEL;
        foreach ($numMapping as $key => $value) {
            $result = $business->addOrCancelTicket($key, [$key => $value], $this->_sid, $this->_memberId,
                $channelCancel, $touristList);

            if ($result['code'] == 200) {
                $isChangeTeam = true;
            }
        }

        if ($isChangeTeam && $teamOrder) {
            $res = $order->modifyTeamTicketNum($teamOrder);

            if (!$res) {
                $this->apiReturn(400, [], '更新团队订单人数失败');
            }
        }

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 核销订单
     * Create by zhangyangzhen
     * Date: 2019/3/14
     * Time: 15:21
     *
     * @param  int  $landId  景区ID
     * @param  string  $orderNum  订单号
     * @param  int  $status  订单状态
     */
    public function orderCheck()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $landId   = I('post.land_id', 0, 'intval');
            $orderNum = I('post.ordernum', '', 'strval,trim');
            $status   = I('post.status', 0, 'intval');
            $tnum     = I('post.checkNum', 0, 'intval');

            if (!$landId || !$orderNum || !$tnum) {
                throw new \Exception('参数错误');
            }

            $landModel = $this->_getLandModel();
            $landInfo  = $landModel->getLandInfo($landId);

            if (empty($landInfo)) {
                throw new \Exception('未找到该产品');
            }

            //还是使用之前的验证逻辑
            // 获取订单信息
            $opts = [
                'ssl' => ['verify_peer' => false, 'verify_peer_name' => false],
            ];

            $ctx       = stream_context_create($opts);
            $raw_resut = @file_get_contents(self::__ORDER_URL__ . '?ordernum=' . $orderNum, false, $ctx);
            if (!$raw_resut) {
                throw new \Exception('订单未找到');
            }

            $orderInfo = json_decode($raw_resut, true);

            //判断验证票数和订单票数是否一致
            $hasChange  = 0;
            $batchCheck = 0;
            foreach ($orderInfo[$orderNum]['tickets'] as $val) {
                if ($val['batch_check'] == 1) {
                    $batchCheck = 1;
                }

                if ($val['tnum'] != $tnum) {
                    $hasChange = 1;
                    break;
                }
            }

            $terminal   = $landInfo['terminal'];
            $global_cfg = ['ext_info' => ['op' => $this->_memberId]];
            $now        = date('Y-m-d H:i:s');

            $tSock = \Library\Business\TerminalCheck::connect(IP_TERMINAL);
            if ($hasChange) {
                $vCmd      = $batchCheck ? 290 : 289;
                $cfg       = ['vMode' => 5, 'vCmd' => $vCmd, 'vCheckDate' => $now];
                $chResult  = $tSock->Terminal_Check_Distribute($terminal, $landInfo['salerid'],
                    $orderInfo[$orderNum]['code'], 1, [$orderNum, $tnum], array_merge($cfg, $global_cfg));
                $checkData = json_encode(array(
                    $terminal,
                    $landInfo['salerid'],
                    $orderInfo[$orderNum]['code'],
                    1,
                    [$orderNum, $tnum],
                    array('vCmd' => 289),
                ));
            } else {
                $cfg['vMode'] = 5;
//                $cfg['vMode'] = 9;
                if ($status == 2) {
                    $cfg['vCmd'] = 602;
                } else {
                    $cfg['vCmd'] = 601;
                }
                $cfg['vCheckDate'] = $now;
                $chResult          = $tSock->Terminal_Check_In_Order($terminal, $landInfo['salerid'], $orderNum,
                    array_merge($cfg, $global_cfg));
                $checkData         = json_encode([$terminal, $landInfo['salerid'], $orderNum, $cfg]);
            }

            $logStr = "[$orderNum]验证,操作人[$this->_memberId],数据[$checkData],结果[" . json_encode($chResult) . "]";
            pft_log('wpt_smapp_check', $logStr);
            if ($chResult['state'] != 'success') {
                throw new \Exception($chResult['msg']);
            }
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 获取账户和授信信息
     * <AUTHOR>
     * @date   2018-05-09
     *
     * @param  int aid 上级供应商ID
     */
    public function getAccountBalance()
    {
        $aid = I('post.aid', 0, 'intval');

        if (!$aid) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $memberRelationBus      = new MemberRelation();
        $res                    = $memberRelationBus->getDisClearingModels($aid, [$this->_sid]);
        $clearingway            = isset($res[$this->_sid]) ? $res[$this->_sid] : 0;
        $balance                = MemberMoney::getAllMoney($this->_sid, $aid, true);
        $balance['clearingway'] = $clearingway;

        $this->apiReturn(self::CODE_SUCCESS, $balance);
    }

    /**
     * 报团订单确认和支付
     * Create by zhangyangzhen
     * Date: 2019/3/14
     * Time: 16:05
     *
     * @param  int pay_type //支付方式:1=现金，2=授信，3=余额，4=到付
     * @param  string order_ids 未支付的订单号
     * @param  string team_order 报团订单号
     */
    public function comfirmAndPayOrder()
    {
        $payType   = I('post.pay_type', 0, 'intval');
        $orderStr  = I('post.order_ids', '', 'strval');
        $teamOrder = I('post.team_order', '', 'strval');

        if (!($payType && $teamOrder && $orderStr)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数缺失');
        }

        $app = new Application();

        $payFail = ['son_ordernum' => ['IN', $orderStr]];

        $sonOrder = $app->order_class->getTeamModel()->getDataByMainOrder($teamOrder);

        $orderArr = array_column($sonOrder, 'son_ordernum');

        if (!$orderArr) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '数据异常');
        }

        foreach ($orderArr as $orderId) {
            $res = $app->order->setOrderPayed($orderId, $payType, $teamOrder, 16);

            if ($res[0] != self::CODE_SUCCESS) {
                $payFail[] = $orderId . '支付失败，原因:' . $res[1];
            }

            if ($payType == 4) {
                $this->apiReturn($res[0], ['fail' => $payFail], $res[1]);
            }
        }

        $this->apiReturn($res[0], ['fail' => $payFail], $res[1]);
    }

    /**
     * 获取演出场次列表
     * Create by zhangyangzhen
     * Date: 2019/3/11
     * Time: 13:42
     *
     * @param  int  $venueId  场馆ID
     * @param  string  $date  日期
     * @param  int  $topSellerId  一级分销商，用来获取分销库存
     *
     * @return array
     */
    private function _getRoundsInfo($venueId, $date, $topSellerId)
    {
        if (!$venueId || !$date || !$topSellerId) {
            return [];
        }

        $showBiz = $this->_getBizShowModel();
        $date    = date('Y-m-d', strtotime($date));
        $tmpList = $showBiz->getRoundList($venueId, $date, $topSellerId, $zoneId = false, $isRemoveExpire = true);
        $code    = $tmpList['code'];

        //数据出错了
        if ($code == 0) {
            return [];
        }

        //数据格式处理
        $roundList = [];
        $tmpData   = $tmpList['data'];
        foreach ($tmpData as $item) {
            $roundInfo = [
                'id'           => intval($item['id']),
                'round_name'   => $item['round_name'],
                'bt'           => $item['bt'],
                'et'           => $item['et'],
                'venus_id'     => $item['venus_id'],
                'use_date'     => $item['use_date'],
                'storage'      => $item['storage'],
                'area_storage' => $item['area_storage'],
            ];

            $roundList[] = $roundInfo;
        }

        return $roundList;
    }

    /**
     * 处理票类信息
     * Create by zhangyangzhen
     * Date: 2019/3/11
     * Time: 13:47
     *
     * @param  array  $tickets  门票列表
     *
     * @return array
     */
    private function _handleTicketsInfo($tickets)
    {
        $return = [];
        foreach ($tickets as $ticket) {

            if ($ticket['pay'] == 0) {
                continue;
            }

            $return[] = [
                'pid'          => $ticket['pid'],
                'tid'          => $ticket['tid'],
                'title'        => $ticket['title'],
                'price'        => $ticket['tprice'],
                'zone_id'      => $ticket['zone_id'],
                'tourist_info' => $ticket['tourist_info'],
            ];
        }

        return $return;
    }

    /**
     * 团单预定模型
     * Create by zhangyangzhen
     * Date: 2019/3/7
     * Time: 11:59
     * @return BizBook|null
     */
    private function _getBizBookModel()
    {
        if (empty($this->_bizBookModel)) {
            $this->_bizBookModel = new BizBook();
        }

        return $this->_bizBookModel;
    }

    /**
     * 景区模型
     * Create by zhangyangzhen
     * Date: 2019/3/11
     * Time: 12:00
     * @return Land|null
     */
    private function _getLandModel()
    {
        if (empty($this->_landModel)) {
            $this->_landModel = new Land();
        }

        return $this->_landModel;
    }

    /**
     * 票类模型
     * Create by zhangyangzhen
     * Date: 2019/3/11
     * Time: 12:01
     * @return Ticket|null
     */
    private function _getTicketModel()
    {
        if (empty($this->_ticketModel)) {
            $this->_ticketModel = new Ticket();
        }

        return $this->_ticketModel;
    }

    /**
     * 分销商模型
     * Create by zhangyangzhen
     * Date: 2019/3/11
     * Time: 13:37
     * @return Reseller|null
     */
    private function _getResellerModel()
    {
        if (empty($this->_resellerModel)) {
            $this->_resellerModel = new Reseller();
        }

        return $this->_resellerModel;
    }

    /**
     * 演出模型
     * Create by zhangyangzhen
     * Date: 2019/3/11
     * Time: 13:45
     */
    private function _getBizShowModel()
    {
        if (empty($this->_bizShowModel)) {
            $this->_bizShowModel = new BizShow();
        }

        return $this->_bizShowModel;
    }

    /**
     * 导游模型
     * Create by zhangyangzhen
     * Date: 2019/3/11
     * Time: 14:24
     * @return Guide|null
     */
    private function _getGuideModel()
    {
        if (empty($this->_guideModel)) {
            $this->_guideModel = new Guide();
        }

        return $this->_guideModel;
    }

    /**
     * 团单搜索模型
     * Create by zhangyangzhen
     * Date: 2019/3/13
     * Time: 10:10
     * @return BizSearch|null
     */
    private function _getBizTeamOrder()
    {
        if (empty($this->_bizTeamOrder)) {
            $this->_bizTeamOrder = new BizSearch();
        }

        return $this->_bizTeamOrder;
    }

    /**
     * 用户模型
     * Create by zhangyangzhen
     * Date: 2019/3/13
     * Time: 13:40
     * @return Member|null
     */
    private function _getMemberModel()
    {
        if (empty($this->_memberModel)) {
            $this->_memberModel = new Member('slave');
        }

        return $this->_memberModel;
    }

    /**
     * 分销模型
     * Create by zhangyangzhen
     * Date: 2019/3/13
     * Time: 13:54
     * @return Evolute|null
     */
    private function _getEvoluteModel()
    {
        if (empty($this->_evoluteModel)) {
            $this->_evoluteModel = new Evolute();
        }

        return $this->_evoluteModel;
    }

    /**
     * 获取订单游客身份证信息
     *
     * @param  string   ordernum   订单号
     * @param  array   tids   门票id数组   [123, 35435]
     */
    public function getOrderTouristInfo()
    {
        //订单号
        $ordernum = I('post.ordernum');
        if (!$ordernum) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '参数缺失');
        }
        $businessModify = new Modify();
        //游客身份证信息
        $idCardArray = $businessModify->getOrderTouristInfoByOrderNum($ordernum);
        if (!$idCardArray) {
            $idCardArray = [];
        }
        $data = [
            'idCardArray' => $idCardArray,
        ];

        $this->apiReturn(parent::CODE_SUCCESS, $data, '获取成功');
    }

    /**
     * 报团预定的预定按钮那边
     *
     * order_data数据格式
     * order_data[景区id][票类id]: 购买数量
     * order_data[192670][520636]: 2
     * order_data[192670][520703]: 2
     *
     */
    public function teamOrderReserveCheck()
    {
        $orderData = I('post.order_data', '', 'strval');
        $aid       = I('post.aid', 0, 'intval'); //上级供应商id
        $fid       = I('post.fid', 0, 'intval'); //报团计调下单的时候传这个id
        if (!$orderData || (!$aid && !$fid)) {
            $this->apiReturn(204, '', '参数错误');
        }
        $fid = empty($fid) ? $this->_sid : $fid; //报团计调的时候才有这个参数，预定的时候还是原来的逻辑
        $TeamLimitBiz = new \Business\TeamOrder\TeamLimit();
        $result       = $TeamLimitBiz->teamOrderReserveCheck($aid, $fid, $orderData);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

       /**
     * 获取剩余的全部门票
     * <AUTHOR>
     * @date   2018-05-08
     */
    public function showMoreTickets()
    {
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        //产品id
        $productId = I('product_id', 0, 'intval');
        //日期
        $date = I('date', '');
        //分销商id
        $fid = I('fid', 0, 'intval');

        if ((!$aid && !$fid) || !$productId || !$date) {
            $this->apiReturn(204, [], '参数错误');
        }

        //报团计调获取数据使用
        if (!empty($fid)) {
            $result = $this->_getBizBookModel()->showMoreTickets($fid, $productId, $this->_sid, $date);
        } else {
            $result = $this->_getBizBookModel()->showMoreTickets($this->_sid, $productId, $aid, $date);
        }

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 团单修改审核判断
     * <AUTHOR> Li
     * @date   2019-03-23
     *
     * @return array
     */
    public function modifyOrderReview()
    {
        $aid        = I('post.aid');
        $fid        = I('post.fid');
        $ticketInfo = I('post.ticket_info');
        $teamOrder  = I('post.team_order');
        $type       = I('post.type', 0, 'intval'); // 0修改 1取消订单

        if (!$aid || !$fid || !$ticketInfo || !$teamOrder) {
            $this->apiReturn(204, [], '参数错误');
        }

        $verifyBis = new TeamOrderVerify();
        $checkRes  = $verifyBis->modifyOrderReview($aid, $fid, $ticketInfo, $teamOrder, $type, $this->_sid);

        if ($checkRes['code'] == 201) {
            $this->apiReturn(201, [], '已生成报团申请单，报团申请单号：' . $checkRes['data']['audit_id'] . '；可在分销商报团申请管理查询，供应商审核后生效。');
        }

        if ($checkRes['code'] != 200) {
            $this->apiReturn($checkRes['code'], $checkRes['data'], $checkRes['msg']);
        }

        $this->apiReturn(200, [], '无需审核');
    }

    /**
     * 获取供应商配置信息
     *
     * @return array
     * <AUTHOR>
     * @date 2021/10/18
     *
     *
     */
    public function getSupplyConfig()
    {
        $sourceId = I('post.sourceId', 0, 'intval');
        if (empty($sourceId)) {
            $this->apiReturn(203, [], "参数错误");
        }

        $configBz = new TeamConfig();
        $result   = $configBz->getConfig($sourceId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
    /**
     * 单张上传图片到阿里云
     * <AUTHOR>
     * @date   2019/10/23
     */
    public function uploadImage()
    {
        $tempFile = $_FILES['image']['tmp_name'] ?? null;
        if (empty($tempFile)) {
            $this->apiReturn(203, [], "文件不能为空");
        }
        if(!is_uploaded_file($tempFile)){
            $this->apiReturn(203, [], "非法操作，非上传文件");
        }
        $errCode = $_FILES['image']['error'] ?? 0;
        if($errCode!= 0) {
            $this->apiReturn(203, [], '图片上传失败，错误码：'.$errCode);
        }
        $imageBase64Data = base64_encode(file_get_contents($tempFile));
        //上传图片到阿里云
        $prefix = 'identity_card';
        $imgResult = Helpers::uploadImage2AliOss($prefix, $imageBase64Data);
        if ($imgResult['code'] != 200) {
            $this->apiReturn(204, [], "上传阿里云失败");
        }
        //返回的图片url
        $url = $imgResult['data']['src'];
        $returnData = [
            'url'    => $url,
        ];

        $this->apiReturn(200, $returnData, "操作成功");
    }

    /***
     * 获取已加票数量
     * <AUTHOR>
     * @date 2021/11/5
     *
     * @return array
     */
    public function getAddedTicket()
    {
        $orderNum = I('post.order_num');
        if (!$orderNum) {
            $this->apiReturn(203, [], '参数缺失');
        }
        $queryParams = [[$orderNum], [16]];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderTrack', 'findByParams', $queryParams);

        $addNum = 0;
        if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
            $this->apiReturn(200, ['add_num' => $addNum], '获取成功');
        }

        $addNum = 0;
        foreach ($queryRes['data'] as $value) {
            $addNum = $addNum + $value['tnum'];
        }

        $this->apiReturn(200, ['add_num' => $addNum], '获取成功');
    }

    /**
     * 获取订单各票原始票数
     */
    public function getOriginTicketNum()
    {
        //订单号
        $ordernum = I('post.order_num');
        if (!$ordernum) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '参数缺失');
        }
        $businessModify       = new Modify();
        $originTicketNumArray = $businessModify->getOriginTnumArrayByMainOrderNum($ordernum);
        if (!$originTicketNumArray) {
            $this->apiReturn(parent::CODE_CREATED, [], '获取失败');
        }
        $this->apiReturn(parent::CODE_SUCCESS, $originTicketNumArray, '获取成功');
    }

    /**
     * 获取报团审核申请列表
     * <AUTHOR> Li
     * @date   2019-03-19
     *
     * @return array
     */
    public function getTeamorderStashList()
    {
        $dtype      = I('post.dtype');   //用来判断是供应商的审核列表还是分销商的申请列表  1供应商 2分销商
        $playTime   = I('post.play_time', '', 'strval');
        $identify   = I('post.identify', '', 'strval');
        $sid        = I('post.sid', 0, 'intval');
        $fid        = I('post.fid', 0, 'intval');
        $type       = I('post.type', 0, 'intval');
        $status     = I('post.status', 4, 'intval');
        $applyTime  = I('post.apply_time', '', 'strval');
        $verifyTime = I('post.verify_time', '', 'strval');
        $page       = I('post.page', 1, 'intval');
        $pageSize   = I('post.size', 15, 'intval');
        $teamOrderVerifyBz = new TeamOrderVerify();
        $result = $teamOrderVerifyBz->getTeamorderStashList($this->_sid, $dtype, $playTime, $identify, $sid,
            $fid, $applyTime, $verifyTime, $page, $pageSize, $type, $status);


        $this->apiReturn($result['code'], $result['data']);
    }

    /**
     * 获取报团审核申请详情
     * <AUTHOR> Li
     * @date   2019-03-19
     *
     * @return array
     */
    public function getTeamorderStashById()
    {
        $id   = I('get.id');
        $type = I('get.type'); //查看类型 1：供应商产看 2：分销商查看
        if (!$id) {
            $this->apiReturn('203', [], '缺少必要参数');
        }

        $teamOrderVerifyBz = new TeamOrderVerify();
        $result = $teamOrderVerifyBz->getTeamorderStashById($this->_sid, $id, $type);


        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 审核申请
     * <AUTHOR> Li
     * @date   2019-03-19
     *
     * @return array
     */
    public function teamOrderAudit()
    {
        $id     = I('post.id');
        $type   = I('post.type', 2, 'intval');   //审核状态 2拒绝 1通过 3取消
        $remark = I('post.remark', '', 'strval');   //审核备注

        if (!$id) {
            $this->apiReturn(204, [], '缺少必要参数');
        }
        $teamOrderVerifyBz = new TeamOrderVerify();
        $result = $teamOrderVerifyBz->teamOrderAudit($this->_sid, $id, $type, $remark);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取证件类型
     *
     * @date 2024/01/04
     * @auther yangjianhui
     * @return array
     */
    public function identity()
    {
        $queryBiz = new \Business\Order\Query();
        $res      = $queryBiz->getIdentity();
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}