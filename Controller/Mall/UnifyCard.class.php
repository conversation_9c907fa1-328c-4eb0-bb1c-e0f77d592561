<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/3/16
 * Time: 17:09
 */
namespace Controller\Mall;

use Business\Product\UnifyCard as BusinessLib;
use Model\Product\ParkCard;

class UnifyCard extends Common{
    public function __construct() {
        parent::__construct();
    }

    /**
     * 消费查询
     *
     * @date   2018-03-16
     * <AUTHOR>
     *
     * @param
     *
     * @return string
     */
    public function order() {
        $beginTime = I('post.b_time', date('Y-m-d'), 'strval');
        $endTime   = I('post.e_time', date('Y-m-d'), 'strval');

        $mobile    = I('post.mobile', '', 'strval');
        $cardNo    = I('post.card_no', '', 'strval');
        $page      = I('post.page', 1, 'intval');
        $pageSize  = I('post.page_size', 10, 'intval');
        $type      = I('post.type', 2, 'intval');

        if (!$mobile && !$cardNo) {
            $this->apiReturn(201, [], '请输入手机号或者卡号');
        }

        $endTime .= ' 23:59:59';

        $data = (new BusinessLib)->getOrder($beginTime, $endTime, $mobile, $cardNo, $type, $page, $pageSize);

        if ($data[0] != 0) {
            $this->apiReturn($data[0], [], $data[1]);
        }

        $this->apiReturn(200, $data[1], 'success');
    }

    /**
     * 获取用户信息
     *
     * @date   2018-03-23
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    public function getUserByCard() {
        $cardNo    = I('post.card_no', '', 'strval');
        $mobile    = I('post.mobile', '', 'strval');

        if (!$cardNo) {
            $this->apiReturn(201, [], '请输入卡号');
        }

        if (!$mobile) {
            $this->apiReturn(201, [], '请输入手机号');
        }

        $parkCard = new ParkCard();

        $cardData = $parkCard->getCardByCardNo($cardNo);

        if (!$cardData) {
            $this->apiReturn(205, [], '卡号信息不存在');
        }

        $memberBiz  =  new \Business\Member\Member();
        $memberInfo = $memberBiz->getInfo($cardData['member_id']);
        if (!$memberInfo || $memberInfo['mobile'] != $mobile) {
            $this->apiReturn(205, [], '手机号对应的卡号信息不存在');
        }

        $this->apiReturn(200, ['mid' => $cardData['member_id'], 'aid' => $cardData['sid']], 'success');

    }
}