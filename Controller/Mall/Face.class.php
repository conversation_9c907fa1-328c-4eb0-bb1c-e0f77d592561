<?php
/**
 * 短信，小程序订单人脸绑定
 *
 * User: hanwenlin
 * date: 2018-10-19
 */

namespace Controller\Mall;

use Business\CommodityCenter\Ticket as ticketBiz;
use Controller\Mall\Mall;
use Business\Order\Face as bizFace;
use Library\Resque\Queue;
use Library\Tools;
use Library\Tools\Helpers;
use Library\Tools\YarClient;
use Model\Order\OrderTools;
use Model\Order\TeamOrderSearch;
use Model\Product\Land;
//use Model\Product\Ticket;
use Model\Terminal\FaceCompare;
use Model\Terminal\TeamOrderFace;

class Face extends Mall
{

    //订单短信绑定人脸
    public function faceOrderBind() {
        $mobile      = I('post.mobile', '');
        $idcard      = I('post.idcard', '');
        $facename    = I('post.facename', '');
        $ordernum    = I('post.ordernum');
        $sex         = I('post.sex', 'F');
        $idx         = I('post.idx');
        $base64Img   = I('post.face');
        $isSave      = I('post.is_save', 0);
        $logId       = I('post.logid', 0);
        if(filter_var($base64Img, FILTER_VALIDATE_URL) !== false) {
            $base64Img = "data:image/png;base64,".base64_encode(file_get_contents($base64Img));
        }

        pft_log('orderface', 'sms_face:request='.json_encode(['ordernum'=> $ordernum, 'idx'=>$idx, 'idcard'=>$idcard, 'mobile'=>$mobile, 'is_save'=>$isSave], JSON_UNESCAPED_UNICODE));
        if(empty($ordernum)){
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '请传入订单号');
        }

        if(empty($base64Img)){
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '图片上传失败');
        }

        if(empty($idx)){
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '请输入图片序号');
        }

        if(empty($mobile) && $isSave == 1){
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '请输入手机号');
        }

        if(empty($facename)){
            $facename = '匿名游客';
        }

        $bizFace = new bizFace();

        $result  = $bizFace->handleFace($ordernum, $base64Img, $idx, $mobile, $idcard, $facename, $sex, 2, $isSave);
        if($logId && $result['code'] == 200) {
             $this->getTeamOrderFaceModel()->setTeamOrderFaceState($logId);
        }
        pft_log('orderface', 'sms_face:return='.json_encode(['ordernum'=>$ordernum, 'result'=>$result], JSON_UNESCAPED_UNICODE));

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 短信小程序订单人脸更新
     *
     * <AUTHOR>
     * @date   2019/4/29
     */
    public function faceUpdate()
    {
        $ordernum  = I('post.ordernum');
        $idx       = I('post.idx');
        $base64Img = I('post.face');
        $facename    = I('post.facename', '');
        $isSave    = I('post.is_save', 0);
        $mobile    = I('post.mobile');
        pft_log('orderface', 'sms_face_update:request'
            . json_encode(['ordernum' => $ordernum, 'idx' => $idx]));

        if (empty($ordernum)) {
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '请传入订单号');
        }

        if (empty($base64Img)) {
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '图片上传失败');
        }

        if (empty($idx)) {
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '请输入图片序号');
        }

        if(empty($mobile) && $isSave == 1){
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '请输入手机号');
        }

        if(filter_var($base64Img, FILTER_VALIDATE_URL) !== false) {
            $base64Img = "data:image/png;base64,".base64_encode(file_get_contents($base64Img));
        }

        $bizFace = new bizFace();
        $result  = $bizFace->faceUpdate($ordernum, $idx, $base64Img, $isSave, $facename,false,2);
        pft_log('orderface', 'sms_face_update:return='
            . json_encode(['ordernum' => $ordernum, 'return' => $result],
                JSON_UNESCAPED_UNICODE));
        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 订单小程序绑定人脸,通过手机号
     * Author: hanwenlin
     * date :2018/11/28
     */
    public function samllAppOrderBind() {
        $mobile      = I('post.mobile', '');
        $scenCode    = I('post.scencode', '');
        $checkSign   = md5("pft12301" . $mobile);
        $sign        = I('post.sign');

        if(empty($scenCode)){
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '请传入scenCode');
        }

        if(empty($mobile)){
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '请传入手机号');
        }

        if ($sign != $checkSign) {
            parent::apiReturn(parent::CODE_AUTH_ERROR, [], '身份校验失败');
        }
        $smallAppModel = new \Library\Business\WechatSmallApp();
        $aid           = $smallAppModel->decodeShopCode($scenCode);
        if(empty($aid)) {
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], 'scenCode解析错误');
        }
        $faceModel  = new \Model\Terminal\FaceCompare();
        $platform   = $faceModel->getPlatformByAid($aid, 'id,lid,background');
        $lid        = array_column($platform, 'lid');
        $background = array_column($platform, 'background');
        $background = isset($background[0]) ? $background[0] : '';

        if(empty($lid)) {
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '该商户未配置人脸');
        }

        $orderModel    = new \Model\Order\OrderTools('slave');
        //$data          = $orderModel->getOrderByMobile($mobile, $lid, 'ordernum,tnum', [0,7], 1);
        $queryParams = [$mobile, $lid, date('Y-m-d'), 1, [0,7]];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderQuery','findByOrdertelAndParams', $queryParams);

        if ($queryRes['code'] != 200) {
            $this->apiReturn(500, [], '查询订单错误');
        }

        $data = $queryRes['data'];
        if(empty($data)) {
            parent::apiReturn(parent::CODE_PARAM_ERROR, ['background'=>$background], '找不到订单');
        } else {
            $data['background'] = $background;
            $data['code'] =  \Library\MessageNotify\OrderNotify::url_sms($data['ordernum']);
            $this->apiReturn(200, $data, 'success');
        }
    }

    /**
     * 通过小程序扫码进行vip接待人员注册
     *
     * @author: guanpeng
     * @date: 2019/1/8
     */
    public function registerVipFromMiniCode()
    {
        $params = I('post.');
        $codeId = $params['code_id'] + 0;
        if (!$codeId) {
            parent::apiReturn(201, [], '小程序码ID错误');
        }
        if (!Helpers::isMobile($params['mobile'])) {
            parent::apiReturn(201, [], '手机号格式错误');
        }
        if (empty($params['name'])) {
            parent::apiReturn(201, [], '姓名不能为空');
        }

        $faceBiz = new \Business\Face\FaceVip();
        $result = $faceBiz->registerVipFromMiniCode($params, $codeId);
        parent::apiReturn($result['code'], $result['data'], $result['msg']);
    }

    public function getVipRegisterInfo()
    {
        $codeId = I('get.code_id');
        $mobile = I('get.mobile');
        $client = new YarClient('face');
        $data = $client->call('/Face/Vip/getVipRegisterInfo', ['code'=>$codeId, 'mobile'=>$mobile]);
        if ($data['code'] == 200) {
	        $faceBiz                         = new \Business\Face\FaceBase();
	        $data['res']['data']['face_url'] = $faceBiz->faceUrlConversion($data['res']['data']['face_url']);
            parent::apiReturn($data['res']['code'], $data['res']['data'], $data['res']['msg']);
        }
        parent::apiReturn($data['code'], $data['res'], $data['msg']);
    }

    public function updateVipFace()
    {
        $codeId    = I('post.code_id');
        $id        = I('post.id');
        $mobile    = I('post.mobile');
        $base64Img = I('post.photo_base64');
        if(empty($id) || empty($base64Img) || empty($codeId)) {
            parent::apiReturn(203, [], '参数错误');
        }

        $imgResult     = Helpers::uploadImage2AliOss('vipface', $base64Img);
        if ($imgResult['code'] != 200 ) {
            parent::apiReturn(203, [], '人脸上传失败');
        }
        $faceUrl = $imgResult['data']['src'];
        $client  = new YarClient('face');
        $data    = $client->call('/Face/Vip/updateVipFace', ['id'=>$id, 'code'=>$codeId, 'mobile'=>$mobile, 'base64Img'=>$base64Img, 'faceUrl'=>$faceUrl]);
        if ($data['code'] == 200) {
            parent::apiReturn($data['res']['code'], $data['res']['data'], $data['res']['msg']);
        }
        parent::apiReturn($data['code'], $data['res'], $data['msg']);
    }

    /**
     * 删除常用人脸
     * Author: hanwenlin
     * date :2019/01/21
     */
    public function delSaveFace()
    {
        $mobile = I('post.mobile', '');
        $id     = I('post.save_id', '');
        $faceModel = new \Model\Terminal\FaceCompare();
        $result = $faceModel->updateFaceSave($id, $mobile);
        if($result) {
            $this->apiReturn(200, [], '删除成功');
        } else {
            $this->apiReturn(203, [], '删除失败');
        }
    }

    private $teamFaceModel = null;
    private function getTeamOrderFaceModel()
    {
        if (is_null($this->teamFaceModel)) {
            $this->teamFaceModel = new TeamOrderFace();
        }
        return $this->teamFaceModel;
    }

    /**
     * 扫码进入团单人脸注册数据获取
     *
     * @author: guanpeng
     * @date: 2019/2/24
     * api:/r/Mall_Face/getTeamOrderFace
     * get.openid:          微信openid
     * get.name:            微信昵称或实际姓名
     * get.mobile:          手机号
     * get.team_ordernum:   团队单号
     */
    public function getTeamOrderInitData()
    {
        $wxCode     = I('get.wx_code','','safe_str');
        //$openId     = I('get.openid','','safe_str');
        $name       = I('get.name','','safe_str');
        $mobile     = I('get.mobile','','safe_str');
        $teamOrder  = I('get.team_ordernum','','safe_str');
        if (empty($teamOrder)) {
            $this->apiReturn(203, [], '团单号错误');
        }
        if (empty($mobile) || !Helpers::isMobile($mobile)) {
            $this->apiReturn(203, [], '手机号格式错误');
        }
        if (ENV=='PRODUCTION' || ENV =='TEST') {
            $smallLib = new \Library\Business\WechatSmallApp;
            $sessionInfo = $smallLib->getWxSession($wxCode);
            if ($sessionInfo===false) {
                $this->apiReturn(203, [], '获取微信授权失败，请关闭页面重新打开');
            }
        } else {
            $sessionInfo = [
                'openid' => 'TESt_Openid_'.time(),
            ];
        }

        // 检测这个团单是不是第一次打开注册人脸的页面，如果是的话默认第一个为管理员
        $count      = $this->getTeamOrderFaceModel()->countTeamOrder($teamOrder);
        $code       = 200;
        $faceList   = $codes = $faceInfo = $orderList = [];

        if ($count == 0) {
            $msg = "当前团单[{$teamOrder}]还未添加过人脸，第一位注册的用户将设置为该团单的管理员";
            $lastId = $this->getTeamOrderFaceModel()->setTeamOrderLeader($teamOrder, $sessionInfo['openid'], $name, $mobile);
            if ($lastId > 0) {
                $msg = "管理员设置成功!请将该小程序页面分享给您的团队成员吧~";
            }
        }
        else {
            $msg = 'success';
            // 是否已经注册人脸，已注册返回保存好的数据
            $faceInfo = $this->getTeamOrderFaceModel()->getTeamOrderByMobile($teamOrder, $mobile);
            if (count($faceInfo)>0) {
                //$faceInfo = $this->dataReplace($faceInfo);
                // 校验是不是团单管理员
                if ($faceInfo['is_teamleader'] == 1) {
                    $faceList = $this->getTeamOrderFaceModel()->getTeamOrderList($teamOrder);
                    //$faceList = $this->dataReplace($faceList);
                    $orderList= $this->getOrderCodes($teamOrder, $mobile, true);
                } else {
                    // 获取子订单信息，先检查手机号在uu_order_touristinfo表里面是否已经分配idx数据了，如果没有，设置一个新的mobile字段
                    if($faceInfo['is_checked'] == 1) {
                        $codes = $this->getOrderCodes($teamOrder, $mobile);
                    }
                }
            }
            else {
                $faceInfo   = [
                    "face_url"=>"","name"=>"","mobile"=>"","idcard"=>"",
                ];
            }
        }
        $isTeamLeader = 0;
        if ($count ==0 || (isset($faceInfo['is_teamleader']) && $faceInfo['is_teamleader']==1)) {
            $isTeamLeader = 1;
        }
        $formatData = [
            'is_team_leader' => $isTeamLeader,
            'is_first_time'  => $count==0 ? 1 : 0,
            'idcard_flag'    => isset($codes['idcard']) ? $codes['idcard'] : false,
            'wechat_info'    => $sessionInfo,
            'register_face'  => $faceInfo,
            'face_list'      => $faceList,
            'order_list'     => $orderList,
            'order_codes'    => isset($codes['orders']) ? array_values($codes['orders']):[],
        ];
        $this->apiReturn($code, $formatData, $msg);
    }

    /**
     * 是否需要填写身份证标识处理
     *
     * @author: guanpeng
     * @date: 2019/2/26
     * @param $tidArray
     * @return bool
     */
    private function needIdCardCheck($tidArray)
    {
        $javaApi    = new \Business\CommodityCenter\LandF();
        $landExtInfo = $javaApi->queryLandFByLandIdAndTicketIdAndProductId($tidArray, [], [], 'lid,tid,tourist_info');

        if (!empty($landExtInfo) && is_array($landExtInfo)) {
            $touristRes = [];
            foreach ($landExtInfo as $item) {
                $touristRes[] = $item['tourist_info'];
            }
            return max($touristRes) == 2;
        }
        return false;
    }

    private function getBindedOrderList($teamOrderList)
    {
        $orderNums  = array_column($teamOrderList, 'son_ordernum');
        $orderTM    = new OrderTools('slave');
        //$ticketM    = new Ticket('slave');
        // 获取子票信息
        $childOrder = $orderTM->getPackChildOrders($orderNums, 'orderid,pack_order');
        if (count($childOrder)) {
            $childData  = [];
            foreach ($childOrder as $key=>$val) {
                $childData[$val['pack_order']][] = $val['orderid'];
            }
            foreach ($orderNums as $k=>$v) {
                if(isset($childData[$v])) {
                    $orderNums[$k] = $childData[$v][0];
                }
            }
        }

        // 处理订单信息
        //$lid       = array_column($orders, 'lid');
        $tidArr       = array_column($teamOrderList, 'tid');
        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,title', '', 'id,apply_did,title');
        //$tickets   = $ticketM->getPackageInfoByTid($tid);
        $orders    = $orderTM->getOrderListNew($orderNums,'ordernum,tnum,lid,tid,playtime,begintime,ordername,status');
        $faceOrdernum = array_column($orders, 'ordernum');
        // 获取已注册的人脸信息
        $faceModel  = new FaceCompare();
        $faceOrderRes   = $faceModel->getFaceByOrder($faceOrdernum);
        $faceInfos      = $sortedTicket = [];
        if (is_array($faceOrderRes) && !empty($faceOrderRes)) {
            $faceInfoIds = array_column($faceOrderRes, 'face_info_id');
            $faceRes     = $faceModel->getFaceInfoByFaceIds($faceInfoIds, 'id,face_url,id,idcard,nickname,mobile');
            $faceUrl     = [];
            foreach ($faceRes as $item) {
                $faceUrl[$item['id']]['face_url'] = $item['face_url'];
                $faceUrl[$item['id']]['name']      = $item['nickname'];
                $faceUrl[$item['id']]['mobile']    = $item['mobile'];
            }
            foreach ($faceOrderRes as $key=>$item) {
                $faceInfos[$item['ordernum']][] = [
                    'idx'       => $item['idx'],
                    'idcard'    => $item['idcard'],
                    'face_url'  => $faceUrl[$item['face_info_id']]['face_url'],
                    'name'      => $faceUrl[$item['face_info_id']]['name'],
                    'mobile'    => $faceUrl[$item['face_info_id']]['mobile'],
                ];
            }
        }
        if (!empty($ticketArr)) {
            foreach ($ticketArr as $ticketInfos) {
                $sortedTicket[$ticketInfos['ticket']['id']] = $ticketInfos['land']['title'] .'-'. $ticketInfos['ticket']['title'];;
            }
        }
        //foreach ($tickets as $ticket) {
        //    $sortedTicket[$ticket['tid']] = $ticket['ltitle'] .'-'. $ticket['ttitle'];
        //}
        // 格式化输出
        foreach ($orders as $key=>$item) {
            $orders[$key]['face_info']    = isset($faceInfos[$item['ordernum']]) ? $faceInfos[$item['ordernum']]: [];
            $orders[$key]['ticket_title'] = $sortedTicket[$item['tid']];
        }
        return $orders;
    }

    private function getOrderCodes($teamOrder, $mobile, $isTeamLeader = false)
    {
        $teamOM    = new TeamOrderSearch();
        $subOrders = $teamOM->getSonOrderInfoByMainOrderNum($teamOrder);
        if (empty($subOrders)) {
            return [];
        }
        // 管理员获取列表
        if ($isTeamLeader === true) {
           return $this->getBindedOrderList($subOrders);
        }
        // 普通散客

        $orderNums  = array_column($subOrders, 'son_ordernum');
        $faceModel  = new FaceCompare();
        $faceOrder  = $faceModel->getFaceByOrder($orderNums, 0, $mobile);
        $orderNums  = array_column($faceOrder, 'ordernum');

        if(empty($orderNums)) {
            return [];
        }

        $tidArray   = array_column($subOrders, 'tid');
        $idCardFlag = $this->needIdCardCheck($tidArray);

        foreach ($subOrders as $key=>$item) {
            //管理员未确认的订单去除掉
            if (!in_array($item['son_ordernum'], $orderNums)) {
                 unset($subOrders[$key]);
            }
        }

        $subOrders  = array_values($subOrders);

        $idxOrderMap = [];
        foreach ($faceOrder as $key=>$val) {
            $idxOrderMap[$val['ordernum']] = $val['idx'];
        }

        $tidArr       = array_column($subOrders, 'tid');
        //$ticketM   = new Ticket('slave');
        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,title', '', 'id,apply_did,title');
        if (empty($ticketArr)) {
            return [];
        }
        //$tickets   = $ticketM->getPackageInfoByTid($tid);
        $orderHash = new \Library\Hashids\OrderHashids();
        $sortedTicket = [];
        foreach ($ticketArr as $ticketInfos) {
            $sortedTicket[$ticketInfos['ticket']['id']] = $ticketInfos['land']['title'] .'-'. $ticketInfos['ticket']['title'];;
        }
        //foreach ($tickets as $ticket) {
        //    $sortedTicket[$ticket['tid']] = $ticket['ltitle'] .'-'. $ticket['ttitle'];
        //}
        foreach ($subOrders as $key=>$item) {
            if (isset($idxOrderMap[$item['son_ordernum']])) {
                $encodeNum = $orderHash->encode($item['son_ordernum']) . str_pad($idxOrderMap[$item['son_ordernum']], 2, '0', STR_PAD_LEFT);
                $subOrders[$key]['ticket_title'] = $sortedTicket[$item['tid']];
                $subOrders[$key]['code']         = 'OD#' . $encodeNum;
                $subOrders[$key]['qrUrl']        = "http://qr.topscan.com/api.php?text=OD%23{$encodeNum}";
            }
        }
        return ['idcard'=>$idCardFlag, 'orders'=>$subOrders];
    }

    /**
     * 对敏感数据做下脱敏处理
     *
     * @author: guanpeng
     * @date: 2019/2/24
     * @param array $data 需要处理的数据
     * @return array
     */
    private function dataReplace(array $data)
    {
        if (count($data)==count($data, 1)) {
            // 一维数组
            foreach ($data as $key => $val) {
                if ($key == 'mobile') {
                    $data['show_mobile'] = substr_replace($val, "****", 3, 4);
                } elseif ($key == 'idcard' && !empty($val)) {
                    $data[$key] = substr_replace($val, '********', 6, 8);
                }
            }
        } else {
            // 多维数组
            foreach ($data as $key => $arr) {
                $data[$key] = $this->dataReplace($arr);
            }
        }
        return $data;
    }

    /**
     * 修改团队订单人脸管理员
     * @author: guanpeng
     * @date: 2019/2/24
     *
     * api:/r/Mall_Face/changeTeamOrderLeader
     * post.openid:                旧的管理员微信openid
     * post.openid_new:            新的微信openid
     * post.team_ordernum:         团队单号
     */
    public function changeTeamOrderLeader()
    {
        $openId     = I('post.openid','','safe_str');
        $openIdNew  = I('post.openid_new','','safe_str');
        $teamOrder  = I('post.team_ordernum','','safe_str');
        if ($openId == $openIdNew || $openIdNew == '') {
            $this->apiReturn(203, [], '管理员上传的游客无法更改身份证为管理员');
        }
        $faceInfo   = $this->getTeamOrderFaceModel()->getTeamOrderByOpenId($teamOrder, $openId);
        if ($faceInfo['is_teamleader']!=1) {
            $this->apiReturn(203, [], '当前openid不属于该团单管理员');
        }
        $res = $this->getTeamOrderFaceModel()->changeTeamOrderLeader($teamOrder, $openId, $openIdNew);
        if ($res === true) {
            $this->apiReturn(200, [], '恭喜，变更成功！');
        }
        $this->apiReturn(203, [], '抱歉，变更失败。');

    }

    /**
     * 保存团队订单人脸列表,游客用
     * @author: guanpeng
     * @date: 2019/2/20
     *
     * api:    /r/Mall_Face/collectTeamOrderFace
     * method: post
     * param:  team_ordernum    团队单号
     * param:  face_url         人脸URL
     * param:  openid           小程序OPENID
     * param:  imageBase64      base64的图片
     */
    public function collectTeamOrderFace()
    {
        $teamOrder    = I('post.team_ordernum','','safe_str');
        $faceBase64   = I('post.image_base64','');
        $faceUrl      = I('post.face_url','','safe_str');
        $openId       = I('post.openid','','safe_str');
        $name         = I('post.name','','safe_str');
        $mobile       = I('post.mobile','','safe_str');
        $idCard       = I('post.idcard','','safe_str');
        // 如果是管理员，允许不传人脸
        $isTeamLeader = I('post.is_first_time','0', 'intval');

        if (empty($teamOrder)) {
            $this->apiReturn(203, ['team_ordernum'], '团单号错误');
        }
        //if (empty($openId)) {
        //    $this->apiReturn(203, ['openid'], '微信openid错误');
        //}
        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(203, ['mobile'], '手机号格式错误');
        }
        if (!empty($idCard) && Tools::personID_format_err($idCard)==false) {
            $this->apiReturn(203, ['idcard'], '身份证格式错误');
        }
        if (empty($faceBase64) && empty($faceUrl) && !$isTeamLeader) {
            $this->apiReturn(203, ['imageBase64'], '人脸照片错误');
        }

        $tourFace = $this->getTeamOrderFaceModel()->getTeamOrderByMobile($teamOrder, $mobile);
        if($tourFace) {
            $this->apiReturn(203, [], '该手机号已上传人脸');
        }

        $openPlat  = new \Library\Business\Uface\OpenPlatform();
        $detectRes = $openPlat->faceValidDetect($faceUrl, substr(strstr($faceBase64,','),1));
        if ($detectRes['result'] != 1) {
            $this->apiReturn(203, [], '图片质量不符' . $detectRes['msg']);
        }

        if (!$faceUrl) {
            $result     = Helpers::uploadImage2AliOss('teamorder', $faceBase64);
            if ($result['code'] != 200 ) {
                $this->apiReturn(203, [], '人脸上传失败');
            }
            $faceUrl = $result['data']['src'];
        }

        $lastId  = $this->getTeamOrderFaceModel()->saveTeamOrderFaceInfo($teamOrder, $openId, $name, $mobile,
            $faceUrl,$isTeamLeader, $idCard);
        if (!$lastId) {
            $this->apiReturn(203, [], '抱歉，数据保存失败');
        }
        $this->apiReturn(
            200,
            [
                'face_url'  => $faceUrl,
                'team_order'=> $teamOrder,
                'lastid'    => $lastId
            ],
            '恭喜，人脸注册成功'
        );
    }

    /**
     * 删除团单人脸-管理员
     *
     * @author: guanpeng
     * @date: 2019/2/25
     * api:    /r/Mall_Face/deleteTeamOrderFace
     * method: post
     * param:  team_ordernum    团队单号
     * param:  openid           小程序OPENID
     * param:  logid            记录ID
     * param:  state            状态，默认2
     */
    public function deleteTeamOrderFace()
    {
        $logId        = I('post.logid','0','intval');
        $teamOrder    = I('post.team_ordernum','','safe_str');
        $openId       = I('post.openid','','safe_str');
        $state        = I('post.state','2', 'intval');
        $faceInfo     = $this->getTeamOrderFaceModel()->getTeamOrderByOpenId($teamOrder, $openId);
        if ($faceInfo['id'] !=$logId && $faceInfo['is_teamleader']!=1) {
            $this->apiReturn(203, [], '无权限操作');
        }
        $faceInfo     = $this->getTeamOrderFaceModel()->getTeamOrderById($logId);
        if ($faceInfo['is_checked'] == 1) {
            $this->apiReturn(203, [], '该人脸已审核通过无法删除');
        }
        $result = $this->getTeamOrderFaceModel()->setTeamOrderFaceState($logId, $state);
        if ($result !== false) {
            $this->apiReturn(200, [], '操作成功');
        }
        $this->apiReturn(203, [], '操作失败');
    }

    /**
     * 获取团队订单人脸列表（未分配数据）
     * @author: guanpeng
     * @date: 2019/2/20
     *
     * api:    /r/Mall_Face/getTeamOrderFaceList
     * method: get
     * param:  team_ordernum
     */
    public function getTeamOrderFaceList()
    {
        $teamOrder = I('get.team_ordernum');
        $teamOrder = safe_str($teamOrder);
        if (!$teamOrder) {
            $this->apiReturn(203, [], '团单号错误');
        }
    }

    /**
     * 输出团单已经分配好的订单、人脸数据,管理员用
     *
     * @author: guanpeng
     * @date: 2019/2/20
     * api:    /r/Mall_Face/getTeamOrderInfoByMainOrder
     * method: get
     * param:  team_ordernum
     * param:  openid
     */
    public function getTeamOrderInfoByMainOrder()
    {
        $teamOrder  = I('get.team_ordernum','', 'safe_str');
        $openId     = I('get.openid','','safe_str');
        $teamOrder = safe_str($teamOrder);
        if (!$teamOrder) {
            $this->apiReturn(203, [], '团单号错误');
        }
        if (!$openId) {
            $this->apiReturn(203, [], 'OPENID错误');
        }
        $teamOrderInfo = $this->getTeamOrderFaceModel()->getTeamOrderByOpenId($teamOrder, $openId);
        if ($teamOrderInfo['is_teamleader']!=1) {
            $this->apiReturn(203, [], "您不是该团单({$teamOrder})的管理员，无权限查下");
        }
        $teamOM    = new TeamOrderSearch();
        $orders    = $teamOM->getSonOrderInfoByMainOrderNum($teamOrder);
        if (empty($orders)) {
            $this->apiReturn(203, [], '不存在订单');
        }
        $orderNums  = array_column($orders, 'son_ordernum');
        $orderTM    = new OrderTools('slave');
        //$ticketM    = new Ticket('slave');
        $childOrder = $orderTM->getPackChildOrders($orderNums, 'orderid,pack_order');
        $childData  = [];
        foreach ($childOrder as $key=>$val) {
            $childData[$val['pack_order']][] = $val['orderid'];
        }
        foreach ($orderNums as $k=>$v) {
            if($childData[$v]) {
                $orderNums[$k] = $childData[$v][0];
            }
        }
        // 处理订单信息
        //$lid       = array_column($orders, 'lid');
        $tidArr       = array_column($orders, 'tid');

        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,title', '', 'id,apply_did,title');
        if (empty($ticketArr)) {
            $this->apiReturn(203, [], '门票信息获取失败');
        }
        //$tickets   = $ticketM->getPackageInfoByTid($tid);
        $orders    = $orderTM->getOrderListNew($orderNums,'ordernum,tnum,lid,tid,playtime,begintime,ordername,status');
        $faceOrdernum = array_column($orders, 'ordernum');
        // 获取已注册的人脸信息
        $faceModel  = new FaceCompare();
        $faceOrderRes   = $faceModel->getFaceByOrder($faceOrdernum);
        $faceInfos      = $sortedTicket = [];
        if (is_array($faceOrderRes) && !empty($faceOrderRes)) {
	        $faceBiz     = new \Business\Face\FaceBase();
            $faceInfoIds = array_column($faceOrderRes, 'face_info_id');
            $faceRes     = $faceModel->getFaceInfoByFaceIds($faceInfoIds, 'id,face_url,id,idcard,nickname');
            $faceUrl     = [];
            foreach ($faceRes as $item) {
                $faceUrl[$item['id']]['face_url'] = $faceBiz->faceUrlConversion($item['face_url']);
                $faceUrl[$item['id']]['name']     = $item['nickname'];
            }
            foreach ($faceOrderRes as $key=>$item) {
                $faceInfos[$item['ordernum']][] = [
                    'idx'       => $item['idx'],
                    'idcard'    => $item['idcard'],
                    'face_url'  => $faceUrl[$item['face_info_id']]['face_url'],
                    'name'      => $faceUrl[$item['face_info_id']]['name'],
                ];
            }
        }
        foreach ($ticketArr as $ticketInfos) {
            $sortedTicket[$ticketInfos['ticket']['id']] = $ticketInfos['land']['title'] .'-'. $ticketInfos['ticket']['title'];;
        }
        //foreach ($tickets as $ticket) {
        //    $sortedTicket[$ticket['tid']] = $ticket['ltitle'] .'-'. $ticket['ttitle'];
        //}
        // 格式化输出
        foreach ($orders as $key=>$item) {
            $orders[$key]['face_info']    = isset($faceInfos[$item['ordernum']]) ? $faceInfos[$item['ordernum']]: [];
            $orders[$key]['ticket_title'] = $sortedTicket[$item['tid']];
        }
        $this->apiReturn(200, $orders, "success");
    }
}