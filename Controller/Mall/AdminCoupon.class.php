<?php
/**
 * 后台优惠券管理界面接口
 *
 * User: xujy
 * Date: 2020/6/15
 */

namespace Controller\Mall;

use Library\Controller;
use Library\SimpleExcel;
use Model\Member\Member;
use Model\Order\Coupon;

class AdminCoupon extends Controller
{

    private $_sid;

    public function __construct()
    {
        $this->_sid = $this->isLogin('ajax');
    }

    /**
     *  获取优惠券列表
     * <AUTHOR>
     * @date 2020/6/15
     * @return array
     */
    public function getCouponList()
    {
        $page            = I('page', 1, 'intval'); //分页数
        $pageSize        = I('pageSize', 10, 'intval');  //单页数
        $couponName      = I('couponName', '', 'strval');    //优惠券名称
        $status          = I('status', -1, 'intval');    //优惠券状态： -1全部0正常1下架
        $couponUsageType = I('coupon_usage_type', 1, 'intval');   // 优惠券类型：1满减券 2兑换券
        $sid             = $this->_sid;
        if (!$page || !$pageSize || !$sid) {
            $this->apiReturn(203, [], "参数缺失");
        }

        $couponBiz = new \Business\Cooperator\Coupon\AdminCoupon();
        $list      = $couponBiz->getCouponList($sid, $couponName, $status, $page, $pageSize,$couponUsageType);
        $this->apiReturn($list['code'], $list['data'], $list['msg']);
    }

    /**
     *  将优惠券上架、下架
     * <AUTHOR>
     * @date 2020/6/16
     * @return array
     */
    public function createCoupon()
    {

        // 基础信息
        $couponId    = I('request.couponId', 0, 'intval');
        $couponName  = I('request.couponName', '', 'strval');
        $couponValue = I('request.couponValue', 0, 'intval');
        $couponNum   = I('request.nums', 0, 'intval');
        $remark      = I('request.remarks', '');

        // 使用规则
        $effectiveType     = I('request.effectiveType', 0, 'intval');
        $collectDays       = I('request.collectDays', 0, 'intval');
        $validStime        = I('request.useStartdate', '');
        $validEtime        = I('request.useEnddate', '');
        $canUseType        = I('request.canUseType', 1);
        $canUseProductType = I('request.canUseProductType', '');
        $canUseProductIds  = I('request.canUseProductIds', '');
        $canUseCondition   = I('request.condition', 0, 'intval');
        //$useLimitType       = I('request.limitUse', 1,'intval');
        $useLimitType       = 1;//固定限制每单使用一张
        $canuseLimitChannel = I('request.channel', '');
        $couponStatus       = I('status', 0);
        //$couponNumDay       = I('request.limit_coupon_num_day', 1,'intval');
        $couponNumDay = 1;//限制使用一单一张
        $couponUsageType       = I('request.coupon_usage_type', 1); // 优惠券类型：1满减券 2兑换券

        if (empty($couponName)) {
            $this->apiReturn(203, [], '优惠券名称不能为空');
        }
        if ($couponNum < 1) {
            $this->apiReturn(203, [], '优惠券数量设置有误');
        }
        if ($couponValue < 1) {
            $this->apiReturn(203, [], '优惠券值设置有误');
        }
        $sid        = $this->_sid;
        $couponType = 0;    // 判断是否管理员发券  区分是否商家券还是平台券  --- 目前数据库中的0 可以默认是三亚的优惠券
        if ($this->isSuper()) {
            $couponType = 2;    //平台券
        } else {
            $couponType = 1;    //商家券
        }
        //优惠券叠加类型:0不可叠加1可叠加2指定类型3指定活动
        $overlayType = I('overlayType', 0, 'intval');
        //可叠加的活动类型：1拼团2抢购3砍价
        $activityType = I('activityType', '1', 'strval');
        //可叠加的活动id逗号串：拼团|抢购|砍价
        $activityIds = I('activityIds', '', 'strval');
        $getType     = I('getType', 0, 'intval');

        /**
         * @var $adminCouponBiz  \Business\Cooperator\Coupon\AdminCoupon;
         */
        $adminCouponBiz = new \Business\Cooperator\Coupon\AdminCoupon();
        $res            = $adminCouponBiz->createCoupon($sid,
            $couponId,
            $couponName,
            $couponValue,
            $couponNum,
            $remark,
            $effectiveType,
            $collectDays,
            $validStime,
            $validEtime,
            $canUseType,
            $canUseProductType,
            $canUseProductIds,
            $canUseCondition,
            $useLimitType,
            $canuseLimitChannel,
            $couponStatus,
            $couponNumDay,
            $couponType,
            $overlayType,
            $activityType,
            $activityIds,
            $getType,
            $couponUsageType
        );
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    public function couponCanUseProduct()
    {

    }

    /**
     *  获取优惠券详情
     * <AUTHOR>
     * @date 2020/6/18
     * @return array
     */
    public function couponInfo()
    {
        $couponId = I('couponId', 0, 'intval');
        $sid      = $this->_sid;

        if (!$couponId || !$sid) {
            $this->apiReturn(203, [], "参数缺失");
        }

        $couponBiz = new \Business\Cooperator\Coupon\AdminCoupon();
        $list      = $couponBiz->couponInfo($couponId, $sid);

        $this->apiReturn($list['code'], $list['data'], $list['msg']);
    }

    /**
     *  优惠券上下架
     * <AUTHOR>
     * @date 2020/6/17
     * @return array
     */
    public function changeCouponStatus()
    {
        $couponId = I('couponId', 0, 'intval');
        //优惠券状态:0= 正常, 1=下架, 2=过期,3=删除
        $status = I('status', 0, 'intval');
        $sid    = $this->_sid;
        if (!$couponId || !$sid) {
            $this->apiReturn(203, [], "参数缺失");
        }

        $couponBiz = new \Business\Cooperator\Coupon\AdminCoupon();
        $list      = $couponBiz->changeCouponStatus($sid, $couponId, $status);
        $this->apiReturn($list['code'], $list['data'], $list['msg']);
    }

    /**
     *  删除优惠券
     * <AUTHOR>
     * @date 2020/6/17
     * @return array
     */
    public function delCoupon()
    {
        $couponId = I('couponId', 0, 'intval');
        $sid      = $this->_sid;
        if (!$couponId || !$sid) {
            $this->apiReturn(203, [], "参数缺失");
        }

        $couponBiz = new \Business\Cooperator\Coupon\AdminCoupon();
        $list      = $couponBiz->delCoupon($couponId, $sid);
        $this->apiReturn($list['code'], $list['data'], $list['msg']);
    }

    /**
     *  获取指定优惠券中的可使用的产品
     * <AUTHOR>
     * @date 2020/6/18
     * @return array
     */
    public function getProductForCouponCanUse()
    {

        $pids = I('pids', '', 'strval');
        $sid  = $this->_sid;
        if (!$pids || !$sid) {
            $this->apiReturn(203, [], "参数缺失");
        }

        $couponBiz = new \Business\Cooperator\Coupon\AdminCoupon();
        $list      = $couponBiz->getProductForCouponCanUse($pids);
        $this->apiReturn($list['code'], $list['data'], $list['msg']);
    }

    /**
     *  优惠券撤回销毁
     * <AUTHOR>
     * @date 2019/10/18
     *
     * @return array
     */
    public function rollDestruction()
    {

        $memberCouponId = I('id', 0, 'intval');
        $sid            = $this->_sid;
        if (!$memberCouponId || !$sid) {
            $this->apiReturn(203, [], "参数缺失");
        }

        $couponBiz = new \Business\Cooperator\Coupon\AdminCoupon();
        $list      = $couponBiz->rollDestruction($sid, $memberCouponId);
        $this->apiReturn($list['code'], $list['data'], $list['msg']);
    }

    /**
     * 获取活动数据列表
     * @Author: zhujb
     *
     * @param  int pageSize 每页条数
     * @param  int page     页码
     * @param  string fid   领劵人
     * @param  string activity_name 活动名称
     * @param  string coupon_name 优惠券名称
     * @param  string create_time 领用时间
     * @param  string use_time 使用时间
     * @param  int status -状态 -1:全部,0:正常,1:已过期,2:已使用,3:人工销毁,4:退票回收
     * 2018/11/7
     */
    public function getActivitiesList()
    {
        $pageSize     = I('pageSize', 15, 'intval');
        $page         = I('page', 1, 'intval');
        $aid          = $this->_sid;
        $activityName = I('activity_name', '', 'strval');
        $couponName   = I('coupon_name', '', 'strval');
        $createTime   = I('create_time', '', 'strval');
        $useTime      = I('use_time', '', 'strval');
        $status       = I('status', -1, 'intval');
        $fid          = I('fid', 0, 'intval');
        $isExport     = I('is_export', 0, 'intval');
        $userName     = I('userName', '', 'strval');    //用户名称或用户账号
        $userType     = I('userType', 0, 'intval');    //用户搜素类型：0用户名称1用户账号
        //$userName = "测试一下";
        //$userType = 0;
        //$userName = "********";
        //$userType = 1;

        $memberIds = [];
        if ($userName) {
            $memberApi = new \Business\JavaApi\Member\MemberQuery();

            if ($userType) {

                $memberList = $memberApi->queryMemberByMemberQueryInfo(['accountList' => [$userName]]);
                if ($memberList['code'] != 200 || empty($memberList['data'])) {

                    $res['list']     = [];
                    $res['total']    = 0;
                    $res['pageSize'] = $pageSize;
                    $this->apiReturn(200, $res, '获取成功');
                }
                $memberList = $memberList['data'];

                foreach ($memberList as $key => $value) {
                    $memberIds[] = $value['memberInfo']['id'];
                }

            } else {

                $memberList = $memberApi->queryMemberInfoPagingByName($userName);
                if ($memberList['code'] != 200 || empty($memberList['data'])) {

                    $res['list']     = [];
                    $res['total']    = 0;
                    $res['pageSize'] = $pageSize;
                    $this->apiReturn(200, $res, '获取成功');
                }

                $memberList = $memberList['data']['list'];
                if(empty($memberList)){

                    $res['list']     = [];
                    $res['total']    = 0;
                    $res['pageSize'] = $pageSize;
                    $this->apiReturn(200, $res, '获取成功');
                }

                foreach ($memberList as $key => $value) {
                    $memberIds[] = $value['id'];
                }
            }
            if (empty($memberIds)) {
                $res['list']     = [];
                $res['total']    = 0;
                $res['pageSize'] = $pageSize;
                $this->apiReturn(200, $res, '获取成功');
            }

            $fid = $memberIds;
        }

        $memberCouponModel = new Coupon('remote_1');
        $marketModel       = new \Model\Market\Market();
        $memberModel       = new Member();

        $activityIdArr = [];
        if (!empty($activityName)) {
            // 模糊搜索活动名字
            $marketArr     = $marketModel->selectMarketing($this->_sid, $activityName, 0, 1, 100, 'id');
            $activityIdArr = array_column($marketArr, 'id');
            if (empty($activityIdArr)) {
                $res['list']     = [];
                $res['total']    = 0;
                $res['pageSize'] = $pageSize;
                $this->apiReturn(200, $res, '获取成功');
            }
        }

        $couponIdArr = [];
        if (!empty($couponName)) {
            $couponArr   = $memberCouponModel->getCouponInfoByNames($couponName, 'id', $aid);
            $couponIdArr = array_column($couponArr, 'id');
            if (empty($couponIdArr)) {
                $res['list']     = [];
                $res['total']    = 0;
                $res['pageSize'] = $pageSize;
                $this->apiReturn(200, $res, '获取成功');
            }
        }

        $field           = 'fid,coupon_id,dstatus,create_time,use_time,start_date,end_date,ordernum,id,use_order,coupon_code';
        $memberCouponArr = $memberCouponModel->getMemberCoupons($aid, $fid, $couponIdArr, $activityIdArr, $createTime,
            $useTime, $status, $field, $page, $pageSize);
        $total           = $memberCouponModel->getMemberCouponCount($aid, $fid, $couponIdArr, $activityIdArr,
            $createTime, $useTime, $status);

        // 获取优惠券名称信息
        $couponList        = [];
        $memberCouponIdArr = array_column($memberCouponArr, 'coupon_id');
        $couponData        = $memberCouponModel->getCoupons('id', $memberCouponIdArr, 'id,coupon_name');
        foreach ((array)$couponData as $coupon) {
            $couponList[$coupon['id']] = $coupon;
        }

        // 获取领劵用户信息
        $memberIdArr = array_column($memberCouponArr, 'fid');
        $memberList  = $memberModel->getMemberInfoByMulti($memberIdArr, 'id', 'id,dname,account', true);

        $list = [];

        $hadUseNum = 0;
        foreach ((array)$memberCouponArr as $value) {
            $value['coupon_name']    = $couponList[$value['coupon_id']]['coupon_name'];
            $value['member_account'] = $memberList[$value['fid']]['account'];
            $value['member_dname']   = $memberList[$value['fid']]['dname'];
            $list[]                  = $value;

            if ($value['dstatus'] == 2) {
                $hadUseNum += 1;
            }
        }

        $res['list']      = $list;
        $res['total']     = $total;
        $res['pageSize']  = $pageSize;
        $res['num_count'] = array('issue_num' => '', 'member_coupon_num' => $total, 'had_use_num' => $hadUseNum);

        if (!empty($isExport)) {
            $this->_exportMemberCoupon($list);
            exit;
        } else {
            $this->apiReturn(200, $res, '获取成功');
        }

    }


    /**
     * 导出活动数据报表
     * @Author: zhujb
     * 2018/11/19
     *
     * @param $data
     */
    private function _exportMemberCoupon($data)
    {
        $exportData    = array();
        $exportData[0] = array('优惠券名称', '优惠券ID', '领劵用户', '用户账号', '领取时间', '有效期开始时间', '有效期结束时间', '使用时间', '使用订单号');
        foreach ($data as $value) {
            $exportData[] = array(
                $value['coupon_name'],
                $value['coupon_id'],
                $value['member_dname'],
                $value['member_account'],
                $value['create_time'],
                $value['start_date'],
                $value['end_date'],
                $value['use_time'],
                ($value['use_order'] ? ($value['use_order'] . "\t") : ''),
            );
        }

        $xls = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($exportData);
        $xls->generateXML(date('Ymd') . '活动数据报表');
        die;
    }

    /**
     *  判断用户是否有开通红包翻翻乐模块
     * <AUTHOR>
     * @date 2020/7/3
     * @return array
     */
    public function checkMemberUseModuleAuth()
    {

        $aid = $this->_sid;
        //红包翻翻乐
        $moduleId        = 56;
        $moduleListModel = new \Model\AppCenter\ModuleList();
        $res             = $moduleListModel->checkMemberUseModuleAuth($aid, [$moduleId]);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}
