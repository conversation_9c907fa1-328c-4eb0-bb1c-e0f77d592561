<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: Administrator
 * Date: 2020/2/25
 * Time: 15:30
 */

namespace Controller\Mall;

use Business\FreeTicket4DoctorNurse\PassCode;
use Business\FreeTicket4DoctorNurse\Register;
use Business\FreeTicket4DoctorNurse\Order;
use Library\Cache\Cache;
use Library\Controller;
use Library\Tools;
use Library\Tools\Helpers;
use Model\DoctorNurse\DNModel;
use Model\Product\Land;

ini_set('display_errors', 'On');

class Doctor extends Controller
{

    public function __construct()
    {
        if (ENV == 'PRODUCTION' && !Helpers::inWechatSmallApp()) {
            parent::apiReturn(400, [], "请在微信小程序内打开应用");
        } else {
            // echo json_encode($_POST, JSON_UNESCAPED_UNICODE);
            $postData = file_get_contents('php://input');
            if ($postData) {
                $_POST = json_decode($postData, true);
            }
        }
    }

    public function getQrCode()
    {
        $infoId = I("post.id", '', 'intval');
        if (!$infoId || !is_numeric($infoId)) {
            parent::apiReturn(400, [], "参数错误");
        }
        $model = new DNModel();

        $detail = $model->getDnInfoById($infoId, 'is_confirm');
        if ($detail['is_confirm'] != 2) {
            parent::apiReturn(400, [], "未审核通过");
        }
        $pcBiz = new PassCode();
        $code  = $pcBiz->generateCode($infoId);
        parent::apiReturn(200, ['qr_code' => $code], 'success');

    }

    public function getBindInfo()
    {
        $openid = I("post.openid");
        if (!$openid) {
            parent::apiReturn(204, [], "未查到数据");
        }
        $model = new DNModel();
        $data  = $model->getDnInfoByOpenId($openid);
        if (!empty($data)) {
            $pcBiz                 = new PassCode();
            $data['qr_code']       = $pcBiz->generateCode($data['id']);
            $rawIdCard             = Register::idcardDec($data['idcard'], $data['idcard_seckey']);
            $data['idcard']        = substr_replace($rawIdCard, '********', 6, 8);
            $data['first_name']    = mb_substr($data['name'], 0, 1);
            $data['identity_name'] = $data['identity'] == 0 ? '医生' : '护士';
            unset($data['idcard_seckey']);
            parent::apiReturn(200, $data);
        }
        parent::apiReturn(204, [], "未查到数据");
    }

    public function bindInfo()
    {
        $saveToken = "lock:" . md5($_SERVER['HTTP_USER_AGENT'] . $_SERVER['REMOTE_ADDR']);
        $redis     = Cache::getInstance('redis');
        if ($redis->lock_exist($saveToken)) {
            parent::apiReturn(400, [], "请求频繁，请120秒后重试");
        }
        $openId   = I('post.openid', '', 'safe_str');
        $mobile   = I('post.mobile', '', 'safe_str');
        $idcard   = I('post.idcard', '', 'safe_str');
        $name     = I('post.name', '', 'safe_str');
        $identiy  = I('post.identiy', 0, 'intval');
        $hospital = I('post.hospital', '', 'safe_str');
        $license = I('post.license', '', 'safe_str');
        if (!in_array($identiy, [0, 1])) {
            parent::apiReturn(400, [], "身份只有医生和护士");
        }
        $nameLen     = mb_strlen($name);
        $hospitalLen = mb_strlen($hospital);
        if (!is_chinese($name) || $nameLen < 2 || $nameLen > 8) {
            parent::apiReturn(400, [], "姓名须是2到20个汉字");
        }
        if ($hospitalLen < 4 || $hospitalLen > 20) {
            parent::apiReturn(400, [], "所在医疗机构须是4到20个汉字");
        }
        if (!Helpers::isMobile($mobile)) {
            parent::apiReturn(400, [], "手机号格式错误");
        }
        if (!Tools::personID_format_err($idcard)) {
            parent::apiReturn(400, [], "身份证格式错误");
        }
        $base64Img = I('post.face');
        $prov      = I('post.province', '', 'intval');
        $bizSdk    = new Register();
        $result    = $bizSdk->saveBaseInfo($name, $mobile, $openId, $idcard, $identiy, $prov, $hospital,
            $base64Img, $license);
        $redis->lock($saveToken, 1, 120);
        parent::apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取已合作景区列表
     * @author: Guangpeng Chen
     * @date: 2020/2/26
     */
    public function getCoopsLands()
    {
        $page     = I("get.page", 1, 'intval');
        $rows     = I("get.rows", 50, 'intval');
        $title    = I("get.title", '', 'safe_str');
        $openid   = I('get.openid', '', 'strval');
        $province = I('get.province', 0, 'intval');

        $imgMap = [];
        $m      = new DNModel();
        $area   = '';
        if (!empty($province)) {
            $area = $province . '|';
        }
        $data       = $m->getCooperators($rows, $page, $title, $area);
        $landIdList = array_column($data, 'landid');
        // 获取缩略图
        //$landModel = new Land('slave');
        //$landInfos = $landModel->getLandInfoByLids($landIdList, 'id,imgpath,title');

        $javaAPi = new \Business\CommodityCenter\Land();
        $landInfos = $javaAPi->queryLandMultiQueryById($landIdList);

        if (empty($landInfos)) {
            parent::apiReturn(parent::CODE_NO_CONTENT, [], '暂无数据');
        }
        foreach ($landInfos as $item) {
            $imgMap[$item['id']]['imgpath'] = $item['imgpath'];
            $imgMap[$item['id']]['title']   = $item['title'];
        }
        foreach ($data as $key => $item) {
            $data[$key]['imgpath']   = $imgMap[$item['landid']]['imgpath'];
            $data[$key]['landtitle'] = $imgMap[$item['landid']]['title'];

            // 人脸标示先隐藏
            $passWay = explode(',', $item['pass_way']);
            if (in_array(2, $passWay)) {
                $passWay = array_diff($passWay, [2]);
            }

            $data[$key]['pass_way'] = implode(',', $passWay);
        }
        if ($data) {
            $biz  = new Order();
            $data = $biz->handleCoopsLandsAddAppointment($data, $openid);
            parent::apiReturn(200, $data, 'success');
        }
        parent::apiReturn(parent::CODE_NO_CONTENT, [], '暂无数据');
    }

    /**
     * 订单预约
     * <AUTHOR>
     */
    public function orderAppointment()
    {
        $openid = I('post.openid', '', 'strval');
        $lid    = I('post.lid', 0, 'intval');

        $biz = new Order();
        $res = $biz->orderAppointment($openid, $lid);

        parent::apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取汇总数据
     * <AUTHOR>
     */
    public function getStatisticsByApplyId()
    {
        $applyId        = I('get.apply_id', 0, 'intval');
        $checkStartTime = I('get.check_start_time', '', 'strval'); // 验证开始时间
        $checkEndTime   = I('get.check_end_time', '', 'strval');   // 验证结束时间
        $page           = I('get.page', 1, 'intval');
        $pageSize       = I('get.page_size', 10, 'intval');

        $biz = new Order();
        $res = $biz->orderStatistics($applyId, $checkStartTime, $checkEndTime, $page, $pageSize);

        parent::apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 医护购即验, 微商城端
     * <AUTHOR>
     */
    public function QuickOrderForDoctorNurse()
    {
        $authCode = I('post.auth_code', '', 'strval');
        $applyDid = I('post.apply_did', 0, 'intval');
        $lid      = I('post.lid', 0, 'intval');

        $biz = new \Business\FreeTicket4DoctorNurse\Order();
        $res = $biz->orderByCodeForWx($authCode, $applyDid, $lid);

        $this->apiReturn($res['code'], $res['data'], $res['msg'], true);
    }
}