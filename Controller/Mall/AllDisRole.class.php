<?php
/**
 * 营销员、客户操作接口
 *
 * User: xujy
 * Date: 2020/3/13
 */

namespace Controller\Mall;

use Library\Controller;

class AllDisRole extends Controller
{

    /**
     * @var $sid 供应商id
     */
    private $_sid = null;

    public function __construct()
    {
        $this->_sid = $this->isLogin('ajax');
    }

    /**
     *  获取客户关系列表
     * <AUTHOR>
     * @date 2020/3/12
     * @return array
     */
    public function getRoleList()
    {
        $mobile    = I('mobile', '', 'strval');
        $nickName  = I('nickName', '', 'strval');
        $page      = I('page', 1, 'intval');
        $pageSize  = I('size', 20, 'intval');
        $allDisBiz = new \Business\Cooperator\AllDis\AllDisRole();
        $config    = $allDisBiz->getAllDisRoleList($this->_sid, $mobile, $nickName, $page, $pageSize);
        //$config    = $allDisBiz->getAllDisRoleList($this->_sid, '', '测试发展佣金');

        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }

    /**
     *  查询营销员列表
     * <AUTHOR>
     * @date 2020/3/13
     * @return array
     */
    public function getAllDisRoleListForYX()
    {
        $mobile       = I('mobile', '', 'strval');
        $nickName     = I('nickName', '', 'strval');
        $level        = I('level', 0, 'intval');
        $id           = I('id', 0, 'intval');
        $parentRoleId = I('parentRoleId', 0, 'intval');
        $beginTime    = I('beginTime', '', 'strval');
        $endTime      = I('endTime', '', 'strval');
        $page         = I('page', 1, 'intval');
        $pageSize     = I('size', 20, 'intval');
        $certificationStatus = I('certificationStatus', -1, 'intval');

        $allDisBiz    = new \Business\Cooperator\AllDis\AllDisRole();
        $config       = $allDisBiz->getAllDisRoleListForYX($this->_sid, $mobile, $nickName, $level, 0, $id,
            $parentRoleId, $beginTime, $endTime, $page, $pageSize, 0, $certificationStatus);
        //$config    = $allDisBiz->getAllDisRoleList($this->_sid, '', '测试发展佣金');

        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }

    /**
     * 获取上级营销员列表
     * <AUTHOR>
     * @date 2020/3/16
     * @return array
     */
    public function getParentAllDisRoleList()
    {
        $mobile    = I('mobile', '', 'strval');
        $nickName  = I('nickName', '', 'strval');
        $expRoleId = I('expRoleId', '1', 'intval');//排除在外的营销员id
        $allDisBiz = new \Business\Cooperator\AllDis\AllDisRole();
        $config    = $allDisBiz->getAllDisRoleListForYX($this->_sid, $mobile, $nickName, 0, 1, 0, 0, '',
            '', 1, 20, $expRoleId);
        //$config    = $allDisBiz->getAllDisRoleList($this->_sid, '', '测试发展佣金');

        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }

    /**
     *  获取手机号用户列表
     * <AUTHOR>
     * @date 2020/3/16
     * @return array
     */
    public function getMobileList()
    {
        $mobile     = I('mobile', '', 'strval');
        $allDisBIz  = new \Business\Cooperator\AllDis\AllDisRole();
        $mobileList = $allDisBIz->getMobileList($this->_sid, $mobile);
        $this->apiReturn($mobileList['code'], $mobileList['data'], $mobileList['msg']);
    }

    /**
     *  导入营销员
     * <AUTHOR>
     * @date 2020/3/13
     * @return array
     */
    public function addAllDisRole()
    {

        $memberId = I('memberId', 0, 'intval');
        $mobile   = I('mobile', '', 'strval');
        $nickName = I('nickName', '', 'strval');
        //设置上级类型0指定上级1无指定上级
        $parentType   = I('parentType', 0, 'intval');
        $parentRoleId = I('parentRoleId', 0, 'intval');
        $roleLevel    = I('roleLevel', 0, 'intval');
        //短信通知：无需短信通知0发送短信给导入的手机号1
        $noticeType = I('noticeType', 0, 'intval');
        $nameType   = I('name_type', 1, 'intval');
        $remarks   = I('remarks', '', 'strval');
        $allDisBiz  = new \Business\Cooperator\AllDis\AllDisRole();
        $config     = $allDisBiz->addAllDisRole($this->_sid, $mobile, $memberId, $nickName, $parentType, $parentRoleId,
            $roleLevel, $noticeType, $nameType, $remarks);
        //$config    = $allDisBiz->getAllDisRoleList($this->_sid, '', '测试发展佣金');

        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }

    /**
     *  编辑营销员
     * <AUTHOR>
     * @date 2020/3/13
     * @return array
     */
    public function editAllDisRole()
    {

        $roleId   = I('roleId', 0, 'intval');
        $nickName = I('nickName', '', 'strval');
        //设置上级类型0指定上级1无指定上级
        $parentType   = I('parentType', 0, 'intval');
        $parentRoleId = I('parentRoleId', 0, 'intval');
        $roleLevel    = I('roleLevel', 0, 'intval');
        $remarks    = I('remarks', 0, 'strval');
        $allDisBiz    = new \Business\Cooperator\AllDis\AllDisRole();
        $config       = $allDisBiz->editAllDisRole($this->_sid, $roleId, $nickName, $parentType, $parentRoleId,
            $roleLevel, $remarks);

        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }

    /**
     *  清退下级
     * <AUTHOR>
     * @date 2020/3/18
     * @return array
     */
    public function removeAllDisRole()
    {
        $roleId       = I('roleId');
        $parentRoleId = I('parentRoleId', 0, 'intval');
        $allDisBiz    = new \Business\Cooperator\AllDis\AllDisRole();
        $config       = $allDisBiz->removeAllDisRole($this->_sid, $roleId, $parentRoleId);

        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }

    /**
     *获取营销员详情信息
     * <AUTHOR>
     * @date 2020/3/24
     * @return array
     */
    public function getAllDisOrderSummaryInfo()
    {

        $roleId    = I('roleId');
        $allDisBiz = new \Business\Cooperator\AllDis\AllDisOrderSummary();
        $res       = $allDisBiz->getAllDisOrderSummaryInfo($this->_sid, $roleId);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}
