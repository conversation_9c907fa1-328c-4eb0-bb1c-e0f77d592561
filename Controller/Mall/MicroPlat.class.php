<?php
/**
 * Created by PhpStorm.
 * User: zhangyangzhen
 * Date: 2018/9/6
 * Time: 10:58
 */

namespace Controller\Mall;

use Business\Finance\AccountMoney;
use Business\Finance\Credit;
use Business\Finance\Withdraw;
use Business\JavaApi\InvoiceApi;
use Business\JavaApi\Member\MemberLimit;
use Business\JavaApi\Member\MemberQuery;
use Business\JavaApi\Member\MemberRelationshipRemark;
use Business\JavaApi\Order\Query\Container;
use Business\JavaApi\Ticket\Listing;
use Business\JavaApi\Ticket\TicketSnapshot;
use Business\JavaApi\TicketApi;
use Business\Member\AccountInfo;
use Business\Member\ContactPerson;
use Business\Member\Customer;
use Business\Member\MemberMoney;
use Business\Member\MemberRelation;
use Business\Member\MemberRelation as MemberRelationBiz;
use Business\Order\MergeOrder;
use Business\Order\Modify;
use Business\Order\OrderAidsSplitQuery;
use Business\Order\OrderBook;
use Business\Order\OrderList;
use Business\Order\OrderSearch;
use Business\Order\Query;
use Business\Order\ReservationOrder;
use Business\Order\TimeShareOrder;
use Business\Product\Add\EvoluteGroup;
use Business\Product\HandleTicket;
use Business\Product\ProductList;
use Business\Product\ProductStorage;
use Business\Product\Update\EvoluteGroupUser;
use Library\Business\WechatSmallApp;
use Library\Cache\Cache;
use Library\Constants\OrderConst;
use Library\Tools;
use Library\Tools\Helpers;
use Library\Tools\Vcode;
use Library\Tools\YarClient;
use Model\AdminConfig\AdminConfig;
use Model\DataCollection\DataCollection;
use Model\Finance\Banks;
use Model\Member\Member;
use Model\Member\MemberRelationship;
use Model\Notice\Announce;
use Model\Order\OrderTools;
use Model\Order\RefundAuditModel;
use Model\Order\SubOrderQuery\SubOrderSplit;
use Model\Product\Area;
use Model\Product\Land;
use Model\Product\PriceGroup;
use Model\Product\Ticket;
use Model\Report\Statistics;
use Process\Resource\AreaFilter\AreaLikeSettle;
use Process\Resource\AreaFilter\AreaPinyinSettle;
use Process\Resource\AreaProcess;

use Process\Order\OrderParams;
use Business\Order\PlatformSubmit;
use Business\Order\BaseCheck;

class MicroPlat extends SmallApp
{
    //首页菜单ID
    const __TEAM_ORDER__   = 21;//报团预定入口
    const __DISTRI_QUERY__ = 22;//分销商查询入口
    const __APPLY_QUERY__  = 23;//供应商查询入口

    //模型
    private $_announceModel;
    private $_memberModel;
    private $_statisticsModel;
    private $_memberRelationshipModel;
    private $_withdrawModel;
    private $_accountMoneyModel;
    private $_banksModel;
    private $_businessOrderQueryModel;
    private $_busMemberRelation;
    private $_orderBusiness;
    private $_orderReferModel;

    private $_options     = [];//下单参数
    private $_tempOptions = [];//临时下单参数
    private $_selectType  = false; //订单查询库选择
    private $_soap;

    private $_channel    = 10; //可售渠道，默认10=微平台
    private $_maxLinkNum = 12; //常用联系人最大储存数量

    private $_loginInfo;    //登录用户信息
    private $_memberId;     //用户id
    private $_sid;          //上级id
    private $_dtype;        //用户类型

    private $_prefix = 'microplatsmallapp:';

    public function __construct()
    {
        $this->_loginInfo  = $this->_auth();
        $this->_sid        = $this->_loginInfo['sid'];
        $this->_memberId   = $this->_loginInfo['id'];
        $this->_dtype      = $this->_loginInfo['dtype'];
        $this->_selectType = I('post.time_type');

        $this->_orderBusiness = new \Business\Order\OrderList($this->_selectType);
        $this->_soap          = $this->getSoap();
    }

    /**
     * 微平台小程序首页
     * Create by zhangyangzhen
     * Date: 2018/9/7
     * Time: 14:11
     */
    public function index()
    {
        $mModel = $this->_getMemberModel();

        //获取今日统计数据
        $todayInfo = $this->_getTodayInfo();

        // 员工和资源方账号 不显示账户余额
        if (!in_array($this->_dtype, [2, 3, 6])) {
            $userMoney = $mModel->getMoney($this->_memberId, 0);
        }

        //未读公告以及数量
        $unRead = $this->_getUnreadNotice();

        //首页入口的相关权限
        $adminConfig = new AdminConfig();
        $authConfig  = $adminConfig->getAccess($this->_sid);

        $auth = [];
        if (in_array(self::__TEAM_ORDER__, $authConfig)) {
            $auth['teamorder_book'] = 1;
        }

        if (in_array(self::__DISTRI_QUERY__, $authConfig)) {
            $auth['distri_query'] = 1;
        }

        if (in_array(self::__APPLY_QUERY__, $authConfig)) {
            $auth['apply_query'] = 1;
        }

        $terminalManageAuth = $adminConfig->getAppCenterByKey('terminal_manage');
        $terminalManageId   = $terminalManageAuth['id'] ?? 0;
        if (in_array($terminalManageId, $authConfig)) {
            $auth['terminal_manage'] = 1;
        }

        if ($this->_dtype == 0) {
            //供应商如果在newzd配置了人脸，返回人脸vip接待按钮
            $faceModel = new \Model\Terminal\FaceCompare();
            $isFace    = $faceModel->getPlatformByAid($this->_sid);
            if (count($isFace)) {
                $auth['faceapp']    = 1;
                $auth['facemanage'] = 1;
            }
        }

        $memberBiz  = new \Business\Member\Member();
        $member     = $memberBiz->getInfo($this->_memberId, true);
        $returnData = [
            'todayInfo' => $todayInfo,
            'userMoney' => isset($userMoney) ? $userMoney : 0,
            'unRead'    => $unRead,
            'entryAuth' => $auth,
            'name'      => $member['dname'],
            'headphoto' => $member['headphoto'] ?: "images/touxiang.png",
            'com_name'  => $member['com_name'],
        ];

        $this->apiReturn(self::CODE_SUCCESS, $returnData, 'success');
    }

    /**
     * 微平台合并付款支付列表
     * Create by zhangyangzhen
     * Date: 2019/1/4
     * Time: 17:23
     */
    public function pay()
    {
        $tradeId = I("tradeid", 0);
        if (!$tradeId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单号不能为空');
        }

        // 订单查询
        $Member    = $this->_getMemberModel();
        $Ticket    = new Ticket();
        $Order     = new OrderTools();
        $landModel = new \Model\Product\Land('slave');

        // 账号合法性
        $supply = $Member->getMemberInfo($this->_sid, 'id', 'id,account');
        if (!$supply) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '非法请求');
        }

        $payment = new \Business\Order\MergeOrder();
        if ($payment->isCombineOrder($tradeId)) {
            $mergeOrder = new MergeOrder();
            $ordernums  = $mergeOrder->getMainOrderNumByTradeId($tradeId);
            if (!is_array($ordernums) || empty($ordernums)) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '合并付款子订单不存在');
            }
            $orderInfos = $Order->getOrderInfo($ordernums,
                'status,ordernum,pay_status,tid,ordertime,paymode,tnum,lid,aid,ordername,ordertel,code,pid,totalmoney,begintime,endtime');
            if (!is_array($orderInfos) || empty($orderInfos)) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单信息错误');
            }
            $orderData = [];
            foreach ($orderInfos as $item) {
                $orderData[$item['ordernum']] = $item;
            }

            $tids      = array_column($orderInfos, 'tid');
            $javaApi   = new \Business\CommodityCenter\Ticket();
            $ticketArr = $javaApi->queryTicketInfoByIds($tids, 'id,cancel_auto_onMin,title');

            if (empty($ticketArr)) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '门票信息获取失败');
            }
            $ticketData = [];
            foreach ($ticketArr as $ticketInfos) {
                $ticketData[$ticketInfos['ticket']['id']] = [
                    'id'                => $ticketInfos['ticket']['id'],
                    'cancel_auto_onMin' => $ticketInfos['ticket']['cancel_auto_onMin'],
                    'title'             => $ticketInfos['ticket']['title'],
                ];
            }

            $land = $landModel->getLandInfo($orderInfos[0]['lid'], false, 'p_type,title');

            foreach ($ordernums as $value) {
                $orderInfo = $orderData[$value];
                if (!$orderInfo) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单不存在');
                }

                if (!in_array($orderInfo['status'], [0, 4])) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单状态错误');
                }

                // 订单已支付，跳转支付成功页面
                if ($orderInfo['pay_status'] != 2 && $orderInfo['totalmoney'] != 0) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单已支付');
                }

                // 获取票类信息
                $ticket = $ticketData[$orderInfo['tid']];

                if (time() - strtotime($orderInfo['ordertime']) > $ticket['cancel_auto_onMin'] * 60) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '已超过支付时间');
                }
                // 获取支付参数
                $payParams = $this->_getPayParams(
                    $value,
                    $this->_sid,
                    $supply['account'],
                    $ticket['title'],
                    $ticket['cancel_auto_onMin'],
                    $orderInfo,
                    $land
                );

                // 更多参数
                $payParams['ptype']             = $payParams['detail']['ptype'];
                $payParams['detail']['paymode'] = $orderInfo['paymode'];

                $combineData[] = $payParams;
            }
            $money      = $mergeOrder->getTradeIdTotleMoney($tradeId);
            $resultData = [
                'list'       => $combineData,
                'totalMoney' => $money / 100,
            ];
            $this->apiReturn(self::CODE_SUCCESS, $resultData);
        } else {
            // 订单合法性
            $ordernum  = $tradeId;
            $orderInfo = $Order->getOrderInfo($ordernum,
                'status,ordernum,pay_status,tid,ordertime,paymode,tnum,lid,aid,ordername,ordertel,code,pid,totalmoney,begintime,endtime');
            if (!$orderInfo) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单不存在');
            }

            $orderInfo['totalPrice'] = $orderInfo['totalmoney'];

            if (!in_array($orderInfo['status'], [0, 4])) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单状态错误');
            }

            // 订单已支付，跳转支付成功页面
            if ($orderInfo['pay_status'] != 2) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单已支付');
            }

            // 获取票类信息
            $ticket = $Ticket->getTicketInfoById($orderInfo['tid'], 'cancel_auto_onMin,title');

            if (time() - strtotime($orderInfo['ordertime']) > $ticket['cancel_auto_onMin'] * 60) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '已超过支付时间');
            }

            $land = $landModel->getLandInfo($orderInfo['lid'], false, 'p_type,title');

            // 获取支付参数
            $payParams = $this->_getPayParams(
                $ordernum,
                $this->_sid,
                $supply['account'],
                $ticket['title'],
                $ticket['cancel_auto_onMin'],
                $orderInfo,
                $land
            );

            // 更多参数
            $payParams['ptype']             = $payParams['detail']['ptype'];
            $payParams['detail']['paymode'] = $orderInfo['paymode'];

            $combineData[] = $payParams;
            $resultData    = [
                'list'       => $combineData,
                'totalMoney' => $orderInfo['totalPrice'] / 100,
            ];
            $this->apiReturn(self::CODE_SUCCESS, $resultData);
        }

    }

    /**
     * 获取支付参数
     * Create by zhangyangzhen
     * Date: 2019/1/4
     * Time: 16:54
     *
     * @param  string $ordernum 订单号
     * @param  int $aid 供应商ID
     * @param  string $account 供应商账号
     * @param  string $ttitle 门票名称
     * @param  int $autoCancel 自动取消时间
     *
     * @return array
     */
    private function _getPayParams($ordernum, $aid, $account, $ttitle, $autoCancel, $orderData = false, $land = false)
    {
        $return = [
            'payWay' => [
                'ali' => 1,
                'wx'  => 1,
            ],
        ];

        if (!$this->inWechatApp()) {
            // 不在微信app内 禁止使用
            $return['payWay']['wx'] = 0;
        }

        $detail = $this->_getOrderDetail($ordernum, $orderData, $land, $ttitle);

        unset($detail['qrcode']);
        $return['detail'] = $detail;

        $payParams = [
            'subject'    => $detail['landTitle'] . $ttitle,
            'expireTime' => $autoCancel,
            'outTradeNo' => $ordernum,
            'buyId'      => $aid,
            'domain'     => str_replace('wx', $account, MOBILE_DOMAIN),
        ];

        $return['payParams'] = $payParams;

        return $return;
    }

    /**
     * 获取订单详细信息
     *
     * @param  int $ordernum 订单号
     *
     * @return array
     */
    private function _getOrderDetail($ordernum = false, $orderData = false, $land = false, $ttitle = false)
    {
        $orderInfo = $orderData;

        //景区类型
        $pType = $land['p_type'];

        //获取包含的门票信息
        $tickets[] = [
            'title' => $ttitle,
            'num'   => $orderInfo['tnum'],
        ];

        //根据景区类型不同，获取一些额外的展示信息
        if (in_array($pType, ['A', 'F'])) {
            $date  = $orderInfo['begintime'] . '~' . $orderInfo['endtime'];
            $extra = ['date' => $date];
        }

        return [
            'lid'       => $orderInfo['lid'],
            'tid'       => $orderInfo['tid'],
            'aid'       => $orderInfo['aid'],
            'ordernum'  => $ordernum,
            'paymode'   => $orderInfo['paymode'],
            'pid'       => $orderInfo['pid'],
            'ptype'     => $land['p_type'],
            'landTitle' => $land['title'],
            'ordername' => $orderInfo['ordername'],
            'ordertel'  => $orderInfo['ordertel'],
            'qrcode'    => $orderInfo['code'],
            'tickets'   => $tickets,
            'extra'     => $extra,
        ];
    }

    /**
     * 下单 -- 提交订单
     * Create by zhangyangzhen
     * Date: 2019/1/4
     * Time: 9:51
     *
     * @param  array proid 产品列表
     * @param  int coupon_code 优惠券
     * @param  string begintime 游玩时间
     * @param  int paymode 支付方式
     * @param  string ordername 联系人姓名
     * @param  string contacttel 联系人手机号
     * @param  string memo 备注
     * @param  array idCardInfo 身份证列表
     *
     * @throws \ErrorException
     * @throws \Exception
     */
    public function order()
    {
        @pft_log('order/wechat/microplat', json_encode($_REQUEST, JSON_UNESCAPED_UNICODE), $pathMode = 'day');

        //并发，重复提交判断
        $this->_concurrencyHCheck();

        //是否是下单权限
        if ($this->_dtype == 6) {
            $baseCheck = new BaseCheck();
            if (!$baseCheck->isHaveOrderAuth($this->_memberId)) {
                $this->apiReturn(204, [], '该员工账号无权限下单');
            }
        }

        //获取参数
        $paramsRes = OrderParams::getMicroPlatParams();
        if ($paramsRes[0] != 200) {
            $this->apiReturn(204, [], $paramsRes[1]);
        }

        $orderParam = $paramsRes[2];
        //加入
        $orderParam['member_id'] = $this->_sid;
        $orderParam['op_id']     = $this->_memberId;
        $orderParam['is_sale']   = false;
        $orderParam['channel']   = 19;

        $platformOrderBiz = new PlatformSubmit();
        $orderRes         = $platformOrderBiz->combineSubmit($orderParam);

        if ($orderRes[0] == 200) {
            //下单成功,延长两秒的锁时间,避免微信10秒重试机制导致重复下单
            $this->_continueLockLife(2);

            $orderData = $orderRes[2];
            //补充一个字段is_third用来判断是不是第三方产品，前端根据这个字段做提示处理
            $return = [
                'tradeId'  => $orderData['tradeId'],
                'is_third' => $orderData['is_third'],
                'paymode'  => $orderData['list'][0]['paymode'],
            ];
            foreach ($orderData['list'] as $item) {
                $return['list'][] = [
                    'ordernum' => $item['ordernum'],
                    'paymode'  => $item['paymode'],
                ];
            }
            $this->apiReturn(200, $return);
        } else {
            //下单出错移除锁
            $this->_removeLock();
            $this->apiReturn(204, [], $orderRes[1]);
        }
    }

    /**
     * 下单 -- 可以使用的优惠券列表  -- 合并一般都是下单同类型的产品,所以限制类型的只要限制总金额
     * <AUTHOR>
     * @date 2019-01-04
     *
     * @param  array info : [tid, pid , count, price, p_type]  数组中 传 tid  pid  数量  单价 产品类型
     *
     * @return string | json
     *
     */
    public function getCoupon()
    {
        $info = I('post.info', '');
        $aid  = I('post.aid', 0);

        if (empty($this->_sid) || empty($aid) || empty($info) || !is_array($info)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数有误');
        }

        $totalPrice = 0;
        $priceArr   = [];
        // 获取需要下单的参数
        foreach ($info as $item) {
            $totalPrice = $totalPrice + ($item['price'] * $item['count'] * 100);
            // 该产品购买的金额
            $priceArr[$item['pid']]['total_price'] = ($item['price'] * $item['count'] * 100);
        }

        // 下单的tidarr
        $tidArr   = array_column($info, 'tid');
        $pidArr   = array_column($info, 'pid');
        $ptypeArr = array_column($info, 'p_type');

        $couponModel    = new \Model\Product\Coupon();
        $userCouponList = $couponModel->userCouponList($this->_sid, $aid, 0, 'id desc', '', '',
            'coupon_id, start_date, end_date, create_time, use_time, coupon_code, active_id');

        $couponIdArr = array_column($userCouponList, 'coupon_id');
        $couponIdArr = array_unique($couponIdArr);

        // 获取优惠券信息
        $couponInfo = $couponModel->getCouponByIds($couponIdArr,
            'coupon_name, id, coupon_value, use_startdate, use_enddate, condition, collect_days, effective_type, limit_ptype, status, pid, remarks, limit_use, condition');
        if (empty($couponInfo)) {
            $this->apiReturn(self::CODE_SUCCESS, [], '无可用优惠券数据');
        }

        // 获取用户现在占用中的券
        $couponListBiz       = new \Business\Market\CouponList();
        $inUserCouponCodeArr = $couponListBiz->getInUseCouponCode($aid, $this->_sid);

        // 整理优惠券详情数据
        $couponInfoArr       = [];
        $maxCouponValArr     = [];
        $willExpireCouponArr = [];
        foreach ($couponInfo as $couponItme) {
            // 券金额大于订单总金额的剔除
            if ($couponItme['coupon_value'] > $totalPrice) {
                continue;
            }
            if ($totalPrice < $couponItme['condition']) {
                continue;
            }
            // 剔除下单的产品没有在优惠券限制类型中
            if ($couponItme['limit_ptype'] && !in_array($couponItme['limit_ptype'], $ptypeArr)) {
                continue;
            }
            // 剔除限制产品
            if ($couponItme['pid']) {
                $limitPidArr = explode(',', $couponItme['pid']);
                $sectResArr  = array_intersect($pidArr, $limitPidArr);
                if (empty($sectResArr)) {
                    continue;
                }

                // 限制产品的券  剔除大于所有可以使用该券的价格总和
                $pidTotalPrice = 0;
                foreach ($sectResArr as $pid) {
                    $pidTotalPrice += $priceArr[$pid]['total_price'];
                }
                if ($couponItme['coupon_value'] > $pidTotalPrice) {
                    continue;
                }
                if ($couponItme['condition'] > $pidTotalPrice) {
                    continue;
                }
            }

            $couponItme['can_use_type'] = 1;
            if ($couponItme['limit_ptype']) {
                $couponItme['can_use_type'] = 2;
            }
            if ($couponItme['pid']) {
                $couponItme['can_use_type'] = 3;
            }

            $couponInfoArr[$couponItme['id']] = $couponItme;

            if (empty($maxCouponValArr)) {
                $maxCouponValArr = $couponItme;
            }
            if ($couponItme['coupon_value'] > $maxCouponValArr['coupon_value']) {
                $maxCouponValArr = $couponItme;
            }
        }

        if (empty($couponInfoArr)) {
            $this->apiReturn(self::CODE_SUCCESS, [], '无可用优惠券数据');
        }

        // 用户券列表信息处理

        // 最快过期优惠券获取
        $userCouponArr = [];
        foreach ($userCouponList as $key => $item) {
            if (!isset($couponInfoArr[$item['coupon_id']]) || empty($couponInfoArr[$item['coupon_id']])) {
                continue;
            }

            // 剔除被占用中的优惠券
            if (in_array($item['coupon_code'], $inUserCouponCodeArr)) {
                continue;
            }

            $item['use_startdate'] = $item['start_date'] . " 00:00:00";
            $item['use_enddate']   = $item['end_date'] . " 23:59:59";
            if (strtotime($item['use_startdate']) > time() || time() > strtotime($item['use_enddate'])) {
                continue;
            }

            $item['will_expire'] = false;
            if ($item['end_date']) {
                if ((strtotime($item['end_date']) - 86400 * 3) < strtotime(date('Y-m-d'))) {
                    $item['will_expire'] = true;
                }
            }

            if (empty($willExpireCouponArr)) {
                $willExpireCouponArr = $couponInfoArr[$item['coupon_id']];
                $willExpireCouponArr = array_merge($willExpireCouponArr, $item);
            }
            if (strtotime($item['end_date']) < strtotime($willExpireCouponArr['end_date'])) {
                $willExpireCouponArr = $couponInfoArr[$item['coupon_id']];
                $willExpireCouponArr = array_merge($willExpireCouponArr, $item);
            }

            $userCouponArr[$item['coupon_code']] = $couponInfoArr[$item['coupon_id']];
            $userCouponArr[$item['coupon_code']] = array_merge($userCouponArr[$item['coupon_code']], $item);

            if ($item['coupon_id'] == $maxCouponValArr['id'] && !isset($maxCouponValArr['coupon_code'])) {
                $maxCouponValArr = array_merge($maxCouponValArr, $item);
            }
        }

        if (empty($userCouponArr)) {
            $this->apiReturn(self::CODE_SUCCESS, [], '无可用优惠券数据');
        }

        // 删除两个特殊的券
        unset($userCouponArr[$maxCouponValArr['coupon_code']]);
        unset($userCouponArr[$willExpireCouponArr['coupon_code']]);

        // 组合券的数组返回
        $data = [];
        if (isset($maxCouponValArr['coupon_code'])) {
            $data[0] = $maxCouponValArr;

            // 如果最大金额最快过期的不是同一个则需要相应增加上去
            if ($maxCouponValArr['coupon_code'] != $willExpireCouponArr['coupon_code']) {
                $data[1] = $willExpireCouponArr;
            }
        } else {
            $data[0] = $willExpireCouponArr;
        }

        $data = array_merge($data, $userCouponArr);
        $data = array_values($data);
        $this->apiReturn(self::CODE_SUCCESS, $data, 'success');
    }

    /**
     * 下单 -- 下单成功后锁继续存在两秒钟
     * Create by zhangyangzhen
     * Date: 2019/1/3
     * Time: 17:50
     *
     * @param  int $second
     */
    private function _continueLockLife($second = 2)
    {
        $redis = Cache::getInstance('redis');
        $key   = md5(json_encode(I('post.')));
        $redis->set($key, 1, '', $second);
    }

    /**
     * 下单 -- 移除锁
     * Create by zhangyangzhen
     * Date: 2019/1/3
     * Time: 17:39
     */
    private function _removeLock()
    {
        $redis = Cache::getInstance('redis');
        $key   = md5(json_encode(I('post.')));
        $redis->rm($key);
    }

    /**
     * 下单 -- 重复下单（并发判断）
     * Create by zhangyangzhen
     * Date: 2019/1/3
     * Time: 15:50
     */
    private function _concurrencyHCheck()
    {
        //避免重复下单
        $redis = Cache::getInstance('redis');
        $key   = md5(json_encode(I('post.')));
        $lock  = $redis->lock($key, 1, 60);

        if (!$lock) {
            @pft_log('order/wechat', '重复提交', $pathMode = 'day');
            $this->apiReturn(204, [], '您有一笔订单正在处理中,请稍后再试');
        }
    }

    /**
     * 产品预订 -- 产品列表页
     * Create by zhangyangzhen
     * Date: 2018/12/25
     * Time: 16:26
     *
     * @param  string product_type 产品类型
     * @param  string product_name 产品名称
     * @param  int is_supply 是否自供应产品，-1=全部，1=自供应，0=分销
     * @param  int city 城市code
     * @param  int page 页码
     * @param  int size 每页数量
     */
    public function productList()
    {
        $ptype    = I('post.product_type', '', 'strval');
        $pname    = I('post.product_name', '', 'trim');
        $isSupply = I('post.is_supply', -1, 'intval');
        $city     = I('post.city', 0, 'intval');
        $page     = I('post.page', 1, 'intval');
        $size     = I('post.size', 10, 'intval');
        if (empty($ptype)) {
            //先限制特产的不允许
            $ptype = 'A,B,C,F,G,H,K';
        } elseif ($ptype == 'I') {
            $ptype = 'Z';
        }
        $result = ProductList::getWPTSmallAppList($this->_sid, $pname, $ptype, $city, $this->_channel, $isSupply, $page,
            $size);
        $list   = $result['lists'];

        if (!empty($list)) {
            $landPtpye = load_config('land_ptype', 'account');
            //处理产品类型为中文
            foreach ($list as $key => $val) {
                $list[$key]['productType'] = $landPtpye[$val['ptype']];
            }
        }

        $this->apiReturn(self::CODE_SUCCESS, $list, 'success');
    }

    /**
     * 预定页面 -- 日历价格
     * Create by zhangyangzhen
     * Date: 2019/6/21
     * Time: 10:12
     *
     * @param  int ticket_id 票ID
     * @param  string month 月份，格式 2019-06
     * @param  int   aid 上级供应商ID
     */
    public function getCalendarPriceV2()
    {
        $ticketId = I('post.ticket_id', 0, 'intval');
        $month    = I('post.month', '', 'strval');
        $aid      = I('post.aid', '', 'intval');

        if (!$ticketId || !$month || !$aid) {
            $this->apiReturn(201, [], '参数有误');
        }

        $startDate = $month . '-01';
        $endDate   = $month . date('-t', strtotime($month));
        $endDate   = date('Y-m-d', strtotime("$endDate +2 day"));

        //日历模式的请求就是自己的
        if ($aid == -1) {
            $aid = $this->_sid;
        }

        $tPriceBiz = new \Business\Product\Price();
        $calendar  = $tPriceBiz->buyGetCalendar($this->_sid, $aid, $startDate, $endDate, $ticketId);
        //echo '<pre>';print_r($calendar);die;
        //$calendar = TicketApi::getOrderCalendarV2($this->_sid, $aid, $ticketId, $startDate, $endDate);

        if ($calendar['code'] == self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_SUCCESS, $calendar['data'], 'success');
        } else {
            $this->apiReturn(500, [], '暂无日历信息');
        }
    }

    /**
     * 产品预订 -- 产品详情页 -- 获取景区信息
     * Create by zhangyangzhen
     * Date: 2018/12/24
     * Time: 14:40
     *
     * @param  int lid 景区ID
     * @param  int aid 供应商ID
     */
    public function getLandInfo()
    {
        $lid = I('post.lid', 0, 'intval');
        $aid = I('post.aid', 0, 'intval');

        if ($lid < 1 || $aid < 1) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        // 走JAVA接口
        $productBiz = new \Business\Product\Product();
        $landInfo   = $productBiz->getWPTProductInfo($this->_sid, $aid, $lid, $this->_channel);
        if ($landInfo['code'] != self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请求出错');
        }

        $result = $this->_handleLandInfo($landInfo['data']);

        $this->apiReturn(self::CODE_SUCCESS, $result);
    }

    /**
     * 处理从java获取的景区信息
     * Create by zhangyangzhen
     * Date: 2018/12/29
     * Time: 14:39
     *
     * @param $data
     *
     * @return array
     */
    private function _handleLandInfo($data)
    {
        $ptype = $data['ptype'];//产品类似

        //获取景区类型配置
        $landPtype = load_config('land_ptype', 'account');

        $landInfo = [
            'lid'        => $data['lid'],
            'title'      => $data['landName'],
            'prodType'   => $landPtype[$data['ptype']],
            'ptype'      => $data['ptype'],
            'traffic'    => nl2br($data['jtzn']),
            'booking'    => nl2br($data['jqts']),
            'intro'      => htmlspecialchars_decode($data['bhjq']),
            'supplyType' => $data['supplyType'],
            'supplyId'   => $data['supplierId'],
            'supplyName' => $data['supplierName'],
            'province'   => $data['provinceName'],
            'city'       => $data['cityName'],
        ];

        //处理轮播图
        if ($data['imgpathGrp']) {
            $tmpImgpathGrp          = @json_decode($data['imgpathGrp'], true);
            $imgpathGrp             = is_array($tmpImgpathGrp) ? $tmpImgpathGrp : unserialize($data['imgpathGrp']);
            $landInfo['imgpathGrp'] = $imgpathGrp;
        } else {
            $landInfo['imgpathGrp'] = [];
        }

        //处理退款规则
        $tickets = $data['ticketList'];
        if (!empty($tickets)) {
            $tidArr = array_column($tickets, 'ticketId');
            //获取一次门票扩展属性
            $ticketArr = (new \Business\CommodityCenter\Ticket())->queryTicketInfoByIds($tidArr);
            $ticketExt = [];
            foreach ($ticketArr as $ticket) {
                $ticketExt[$ticket['ticket']['id']] = $ticket['ext'];
            }

            //获取票类标签
            $ticketBz = new \Business\Product\Ticket();
            $tagArr   = $ticketBz->getTicketTagByIds($tidArr);
            foreach ($tickets as $key => $val) {
                //处理退款规则描述
                $message                     = OrderBook::handleRefundRule($val['refundAudit'], $val['refundRule'],
                    $val['refundEarlyTime'], $ticketExt[$val['ticketId']]['refund_after_time'] ?? 0);
                $tickets[$key]['refundRule'] = $message;
                $tickets[$key]['pre_sale']   = $val['preSale'];
                $tickets[$key]['tag_list']   = $tagArr[$landInfo['ptype']][$val['ticketId']] ?? [];

                if ($ptype == 'F') {
                    $packBiz   = new \Business\Product\PackTicket();
                    $childData = $packBiz->getChildTickets($val['ticketId']);

                    if (!empty($childData)) {
                        $tickets[$key]['childTickets'] = $childData;
                        $sonPidArr                     = array_column($childData, 'pid');
                        $tickets[$key]['usetime']      = OrderBook::parsePackValidTag($sonPidArr);
                    }
                } else {
                    $validData                = [
                        'type'                      => $ptype,
                        'valid_period_start'        => $val['validStartDateStr'],
                        'valid_period_end'          => $val['validEndDateStr'],
                        'valid_period_type'         => $val['validType'],
                        'use_early_days'            => $val['useEarlyDays'],
                        'valid_period_days'         => $val['validDays'],
                        'valid_period_timecancheck' => $val['ifVerify'],
                        'delaytype'                 => $val['delayType'],
                    ];
                    $tickets[$key]['usetime'] = OrderBook::getValidityDate($validData);
                }
            }

        }

        $landInfo['tickets'] = $tickets;

        return $landInfo;
    }

    /**
     * 获取产品列表城市
     * Create by zhangyangzhen
     * Date: 2019/1/19
     * Time: 14:47
     *
     * @param  int $supply_id 供应商ID 默认0，将展示所有城市，否则展示该供应商产品所在城市
     * @param  int $oversea 海内外，0=国内，1=海外，2=全部
     * @param  string $keyword 关键字
     *
     * @return array
     */
    public function getAreaList()
    {
        $oversea = I('post.oversea', 0);
        $keyword = I('post.keyword', '');

        $hasOverSea = 0;
        switch ($oversea) {
            case 0:
                $overseaList = $this->_getAreaList(1);
                if (!empty($overseaList)) {
                    $hasOverSea = 1;//表示存在海外城市，给前端做展示用
                }
                $areaList = $this->_getAreaList($oversea);
                break;
            case 1:
                $areaList   = $this->_getAreaList($oversea);
                $hasOverSea = 1;
                break;
            case 2:
                $areaList    = $this->_getAreaList($oversea);
                $overseaList = $this->_getAreaList(1);
                if (!empty($overseaList)) {
                    $hasOverSea = 1;//表示存在海外城市，给前端做展示用
                }
                break;
            default:
                $this->apiReturn(self::CODE_PARAM_ERROR, '参数错误');
                break;
        }

        //拼音过滤
        AreaPinyinSettle::filtration($areaList, $keyword);
        //地区名称like过滤
        AreaLikeSettle::filtration($areaList, $keyword);
        //按照拼音分组
        AreaPinyinSettle::clearUp($areaList);

        $retureData = [
            'areaList'   => $areaList,
            'hasOverSea' => $hasOverSea,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $retureData);
    }

    private function _getAreaList($oversea)
    {
        $area     = new Area($oversea);
        $areaList = AreaProcess::getAreaByMember($area, $this->_sid);

        return $areaList;
    }

    /**
     * 产品预订 -- 预定页面 -- 预订数据的获取
     * Create by zhangyangzhen
     * Date: 2019/1/2
     * Time: 10:39
     */
    public function getBookData()
    {
        $ticketId = I('post.ticket_id', 0, 'intval');
        $type     = I('post.type', '', 'strval');
        $landId   = I('post.land_id', 0, 'intval');
        $applyId  = I('post.apply_id', 0, 'intval');

        if (!$ticketId || !$applyId || !$landId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, '', '参数错误');
        }

        $requestTicket = 0;
        //这个三种类型需要返回当前门票即可
        if (in_array($type, ['C', 'H', 'F'])) {
            $requestTicket = $ticketId;
        }

        //获取门票数据
        $data = TicketApi::getBookTickets($this->_sid, $applyId, $landId, $requestTicket, $this->_channel);

        if ($data['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($data['code'], '', $data['sub_msg']);
        }

        $ticketData = $data['data'];
        if (empty($ticketData)) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '产品未通过审核或无权限购买');
        }

        $earlyDate = TicketApi::getEarliestTwoDayPriceDate($ticketId);

        if (!$earlyDate || empty($earlyDate)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '该产品暂不可售');
        }

        $firstDate  = $earlyDate[0]['date'];
        $secondDate = $earlyDate[1]['date'];

        $firstMonth = date('m', strtotime($firstDate));
        $currentMon = date('m', time());
        //判断当前月份和最早可售日期的月份是否一致，不一致的话要取最早可售日期的价格
        //因为日历接口只取当月的价格，隔月的价格没有取
        if (intval($firstMonth) !== intval($currentMon)) {
            $firstPrice  = TicketApi::getPriceAndStorageByPlayDate($ticketId, $this->_sid, $applyId, $firstDate);
            $secondPrice = TicketApi::getPriceAndStorageByPlayDate($ticketId, $this->_sid, $applyId, $secondDate);

            $firstPrice  = $firstPrice[$ticketId]['settlement'];
            $secondPrice = $secondPrice[$ticketId]['settlement'];
        }

        $ticketData = OrderBook::handleWPTBookTicket($ticketData, $ticketId, $this->_sid, $applyId, $this->_channel);

        if ($ticketData[0] != 0) {
            $this->apiReturn($ticketData[0], [], $ticketData[1]);
        }

        $ticketData = $ticketData[1];

        $memberMoney = MemberMoney::getAllMoney($this->_sid, $applyId, true);
        //获取会员可选择的支付方式
        $memberRelationBus = new MemberRelation();
        $payList           = $memberRelationBus->isExsitRecord($applyId, $this->_sid);
        //为票附上期票属性
        foreach ($ticketData['tickets'] as &$value) {
            $earlyDateTmp         = TicketApi::getEarliestTwoDayPriceDate($value['tid'], $this->_sid, 10, $applyId);
            $value['first_date']  = $earlyDateTmp[0]['date'];
            $value['second_date'] = $earlyDateTmp[1]['date'];
        }
        $responseData = [
            'status'      => 'success',
            'capital'     => $memberMoney,
            'clearingWay' => empty($payList) ? 0 : $payList['clearing_mode'],
            'account_pay' => 0,
            'land'        => [
                'lid'         => $ticketData['lid'],
                'pay'         => $ticketData['pay'],
                'ltitle'      => $ticketData['land_name'],
                'p_type'      => $ticketData['p_type'],
                'memberSID'   => $this->_sid,
                'begintime'   => $ticketData['begin_time'],
                'tickets'     => $ticketData['tickets'],
                'firstDate'   => $firstDate,
                'secondDate'  => $secondDate,
                'firstPrice'  => $firstPrice ?: false,
                'secondPrice' => $secondPrice ?: false,
            ],
            'sys_time'    => date('Y-m-d H:i:s', time()),
        ];

        $this->apiReturn(self::CODE_SUCCESS, $responseData, 'success');
    }

    /**
     * 预定页面 -- 预定页面 -- 获取价格和库存
     * <AUTHOR>
     * @time   2019-01-02
     *
     * @param  string   pids   产品ID集合 用-分隔 例如 11138-11139
     * @param  int      aid    上级分销商ID
     * @param  date     date   日期 '2017-01-20'
     *
     * @return  ['11138'=>['price'=>1.58,'store'=>100],'11139'=>[..]]
     */
    public function getPriceAndStorage()
    {
        $aid    = I('aid', '', 'intval');
        $date   = I('date', date('Y-m-d'));
        $tids   = I('tids');
        $tidArr = explode('-', $tids);
        if (empty($tidArr[0])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '未指定产品');
        }

        if (!$tidArr || !$date) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $date = date('Y-m-d', strtotime($date));
        if ($date < date('Y-m-d')) {
            $this->apiReturn(self::CODE_PARAM_ERROR, '请选择正确的日期');
        }

        $ticketStr = implode(',', $tidArr);

        //$priceAndStorage = TicketApi::getPriceAndStorageByPlayDate($ticketStr, $this->_sid, $aid, $date);
        //
        //if (empty($priceAndStorage)) {
        //    $this->apiReturn(self::CODE_AUTH_ERROR, [], '获取价格出错，请稍后重试');
        //}

        $tPriceBiz          = new \Business\Product\Price();
        $priceAndStorageRes = $tPriceBiz->buyBatchGet($this->_sid, $aid, $date, $ticketStr);

        if ($priceAndStorageRes['code'] == 200 && !empty($priceAndStorageRes['data'])) {
            $priceAndStorage = $priceAndStorageRes['data'];
        }

        if (empty($priceAndStorage)) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '获取价格出错，请稍后重试');
        }

        // 先利用tidArr获取pidArr
        $ticketModel = new Ticket();
        $tidPidArr   = $ticketModel->getMuchTicketInfo($tidArr, 'id, pid');
        //获取分时预约数据
        $timeShareOrderBz = new TimeShareOrder();
        $ticketShareRes   = $timeShareOrderBz->getTimeSlicesWithTidArr($tidArr, $date, 10);     //微平台销售渠道为10
        if ($ticketShareRes['code'] != 200) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分时数据获取错误！');
        }
        //获取票属性
        $ticketBz   = new \Business\CommodityCenter\Ticket();
        $ticketInfo = $ticketBz->queryTicketInfoByIds($tidArr, 'id, pre_sale');
        if (empty($ticketInfo)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '票属性获取错误！');
        }
        $ticketInfo   = array_column($ticketInfo, 'ticket');
        $preTicketArr = [];
        foreach ($ticketInfo as $value) {
            if ($value['pre_sale'] == 1) {
                array_push($preTicketArr, $value['id']);
            }
        }
        $ticketShareInfo = $ticketShareRes['data'];
        $return          = [];
        foreach ($priceAndStorage as $key => $val) {
            $return[$tidPidArr[$key]]                    = [
                'jsprice' => $val['settlement'] != -1 ? $val['settlement'] / 100 : 0,  //结算价，-1表示无价格
                'lsprice' => $val['retail'] != -1 ? $val['retail'] / 100 : 0,  //零售价，-1表示无价格
                'msprice' => $val['counter'] != -1 ? $val['counter'] / 100 : 0,  //门市价，-1表示无价格
                'store'   => $val['availableStorage'] >= -1 ? $val['availableStorage'] : 0,
            ];
            $return[$tidPidArr[$key]]['time_share_info'] = $ticketShareInfo[$key] ?? [];
            if (!empty($ticketShareInfo[$key]) && in_array($key, $preTicketArr)) {
                $return[$tidPidArr[$key]]['time_share_info']['use_time_share'] = 0;
            }
        }

        $this->apiReturn(self::CODE_SUCCESS, $return);
    }

    /**
     * 获取常用联系人
     * Create by zhangyangzhen
     * Date: 2018/12/25
     * Time: 17:55
     */
    public function getLinkPerson()
    {
        $person = ContactPerson::getContacts($this->_sid);

        $this->apiReturn(self::CODE_SUCCESS, $person);
    }

    /**
     * 新增/更新/删除常用联系人
     * Create by zhangyangzhen
     * Date: 2018/12/26
     * Time: 10:55
     *
     * @param  string oldname    旧姓名     -- 更新联系人使用
     * @param  string oldmobile  旧手机号  --  更新联系人使用
     * @param  string oldidcard  旧身份证号  -- 更新联系人使用
     * @param  string name       姓名
     * @param  string mobile     手机号
     * @param  string idcard     身份证
     * @param  string type       类型  create=新增，update=更新
     */
    public function changeLinkPerson($name = '', $mobile = '', $idcard = '', $type = '')
    {
        $oldname   = I('post.oldname', '', 'trim');
        $oldmobile = I('post.oldmobile', '', 'trim');
        $oldidcard = I('post.oldidcard', '', 'trim');
        $name      = I('post.name', '', 'trim');
        $mobile    = I('post.mobile', '', 'trim');
        $idcard    = I('post.idcard', '', 'trim');
        $type      = I('post.type', '', 'trim');

        if (empty($name) || empty($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '姓名和手机号不能为空');
        }

        if (!in_array($type, ['create', 'update', 'delete'])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $memberBiz = new \Business\Member\Member();
        $cinfos    = $memberBiz->getMemberExtInfoGetFieldToJava($this->_sid, 'cinfos');
        $linkmans  = $this->_parseLinkman($cinfos);

        $msg = '';
        switch ($type) {
            //新增联系人
            case 'create' :
                $msg     = '新增';
                $linkman = [
                    'name'   => $name,
                    'tel'    => $mobile,
                    'idcard' => $idcard,
                ];

                //判断常用联系人是否存在
                if ($linkmans && in_array($linkman, $linkmans)) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '联系人已存在');
                }

                // 限制常用联系人数量
                if (count($linkmans) >= $this->_maxLinkNum) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '常用联系人数量已达上限');
                }

                // 增加
                if (!$cinfos) {
                    $data['cinfos'] = implode(':', $linkman);
                } else {
                    $data['cinfos'] = $cinfos . '|' . implode(':', $linkman);
                }
                break;

            //更新联系人
            case 'update' :
                $msg = '更新';
                //更新联系人需要传旧的联系人信息
                if (empty($oldname) || empty($oldmobile)) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '请求参数错误');
                }

                $linkman = [
                    'name'   => $oldname,
                    'tel'    => $oldmobile,
                    'idcard' => $oldidcard,
                ];

                if (!$linkmans || !in_array($linkman, $linkmans)) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '联系人不存在');
                }

                foreach ($linkmans as $key => $val) {
                    if ($val == $linkman) {
                        $linkmans[$key]['name']   = $name;
                        $linkmans[$key]['tel']    = $mobile;
                        $linkmans[$key]['idcard'] = $idcard;
                    }

                    $linkmans[$key] = implode(':', $linkmans[$key]);
                }

                $data['cinfos'] = implode('|', $linkmans);
                break;

            //删除联系人
            case 'delete' :
                $msg     = '删除';
                $linkman = [
                    'name'   => $name,
                    'tel'    => $mobile,
                    'idcard' => $idcard,
                ];

                if (!$linkmans || !in_array($linkman, $linkmans)) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '联系人不存在');
                }

                foreach ($linkmans as $key => $val) {
                    if ($val == $linkman) {
                        unset($linkmans[$key]);
                    } else {
                        $linkmans[$key] = implode(':', $val);
                    }
                }

                $data['cinfos'] = implode('|', $linkmans);
                break;
        }
        $memberBiz = new \Business\Member\Member();
        $res       = $memberBiz->updateMemberExtInfo($this->_sid, $data);
        if (!$res) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '常用联系人' . $msg . '失败');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, [], '常用联系人' . $msg . '成功');
        }
    }

    /**
     * 解析常用联系人字段
     * Create by zhangyangzhen
     * Date: 2018/12/25
     * Time: 18:06
     *
     * @param  string $linkmanField 数据库中联系人字段
     * 例如 ；'赵大:***********:|钱二:***********:35010519770402473X'
     *
     * @return array 解析后的数组
     * 例如 ；[['name'=>'赵大','tel'=>***********,'idcard'=>''],....]
     */
    private function _parseLinkman($linkmanField = '')
    {
        if (!$linkmanField || !is_string($linkmanField)) {
            return [];
        }

        $linkmanArr = explode('|', $linkmanField);
        foreach ($linkmanArr as $key => $linkman) {
            [$name, $tel, $idcard] = explode(':', $linkman);
            $info[$key]['name']   = $name;
            $info[$key]['tel']    = $tel;
            $info[$key]['idcard'] = $idcard;
        }

        return $info ?: [];
    }

    /**
     * 订单查询
     * Create by zhangyangzhen
     * Date: 2018/11/20
     * Time: 17:00
     */
    public function getList()
    {
        $code = 200;
        $msg  = '';

        try {
            $params = I('param.');

            if ($this->_dtype == 2) {
                //景区账号数据查询
                $orderRes = $this->_getScenicList($params);
            } else {
                //供应商/分销商/集团账号查询
                $orderRes = $this->_getBusinessList($params);
            }

            $total = $orderRes['total'];
            $list  = $orderRes['list'];

            // 处理是否需要人脸提示
            $list = $this->_getOrderBusiness()->handleOrderHasFace($list);

            $data = [
                'list'  => $list,
                'total' => $total,
            ];
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 查询景区账号下面的数据
     * <AUTHOR>
     * @date 2020/12/18
     *
     * @param  array $paramArr 查询参数
     *
     * @return array
     */
    private function _getScenicList(array $paramArr)
    {
        //登录信息
        $loginInfo  = $this->_loginInfo;
        $sid        = $loginInfo['sid'];
        $memberType = $loginInfo['dtype'];
        $account    = $loginInfo['account'];
        $memberId   = $loginInfo['memberID'];

        //景区数据查询
        $paramArr['salerid'] = $account;

        //参数处理
        $res = $this->_getOrderBusiness()->handleOrderParam($paramArr, $sid);
        if (empty($res['code'])) {
            throw new \Exception($res['msg']);
        }

        //查询时间段处理
        $timeTypeConf = load_config('time_type', 'orderSearch');
        $selectType   = $paramArr['time_type'];
        if (isset($timeTypeConf[$selectType])) {
            $dbStart = $timeTypeConf[$selectType][0];
            $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
        } else {
            $dbStart = false;
            $dbEnd   = false;
        }

        //预留数组，后期会扩展成数组查询
        $pidArr = $paramArr['pid'] ? [$paramArr['pid']] : false;
        $tidArr = $paramArr['tid'] ? [$paramArr['tid']] : false;

        $orderNumArr = false;
        if (isset($paramArr['ordernum']) && $paramArr['ordernum']) {
            $orderNumArr = is_array($paramArr['ordernum']) ? $paramArr['ordernum'] : [$paramArr['ordernum']];
        }

        if ($paramArr['order_mode'] !== false) {
            $orderModeIn = is_array($paramArr['order_mode']) ? $paramArr['order_mode'] : [$paramArr['order_mode']];
        } else {
            $orderModeIn = false;
        }

        if ($paramArr['pay_mode'] !== false) {
            $pmodeIn = is_array($paramArr['pay_mode']) ? $paramArr['pay_mode'] : [$paramArr['pay_mode']];
        } else {
            $pmodeIn = false;
        }

        //新版本数据查询
        $queryRes = $this->_getOrderBusiness()->getScenicListByOrderService($memberId, $sid, $memberType,
            $paramArr['salerid'], $dbStart,
            $dbEnd, $paramArr['page'], $paramArr['size'], $orderNumArr, $paramArr['ordername'],
            $paramArr['person_id'], $paramArr['userMobileSubject'], $paramArr['order_time_start'], $paramArr['order_time_end'],
            $paramArr['play_time_start'], $paramArr['play_time_end'], $paramArr['dtime_start'], $paramArr['dtime_end'],
            $paramArr['begin_time_start'], $paramArr['begin_time_end'], $paramArr['status'], $paramArr['pay_status'],
            $pmodeIn, $orderModeIn, $paramArr['p_type'], $paramArr['operate_id'],
            $paramArr['check_resource'], $pidArr, $tidArr,
            $paramArr['order_source'], $paramArr['if_print'], $paramArr['mark']);

        if ($queryRes['code'] != 200) {
            throw new \Exception($queryRes['msg']);
        }
        if ($queryRes['data']['total'] <= 0) {
            throw new \Exception("没有符合条件的订单");
        }

        $list  = $queryRes['data']['list'];
        $total = $queryRes['data']['total'];

        return ['list' => $list, 'total' => $total];
    }

    /**
     * 供应商/分销商/集团账号查询
     * <AUTHOR>
     * @date 2020/12/18
     *
     * @param  array $paramArr 查询参数
     *
     * @return array
     */
    private function _getBusinessList(array $paramArr)
    {
        //登录信息
        $loginInfo  = $this->_loginInfo;
        $sid        = $loginInfo['sid'];
        $memberType = $loginInfo['dtype'];
        $memberId   = $loginInfo['memberID'];

        //参数处理
        $res = $this->_getOrderBusiness()->handleOrderParam($paramArr, $sid);
        if (empty($res['code'])) {
            throw new \Exception($res['msg']);
        }

        //查询时间段处理
        $timeTypeConf = load_config('time_type', 'orderSearch');
        $selectType   = $paramArr['time_type'];
        if (isset($timeTypeConf[$selectType])) {
            $dbStart = $timeTypeConf[$selectType][0];
            $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
        } else {
            $dbStart = false;
            $dbEnd   = false;
        }

        $lidArr = [];
        if ($paramArr['lid']) {
            //直接按选定的景区查询
            $lidArr = [$paramArr['lid']];
        } else {
            //如果有按资源库查询的话
            if ($paramArr['source_id']) {
                $landApi      = new \Business\CommodityCenter\Land();
                $resourceList = $landApi->queryLandMultiQueryByResourceId([$paramArr['source_id']]);
                $lidArr       = array_column($resourceList, 'id');
            }
        }

        if ($memberType == 7) {
            //集团账号
            $orderSearchBusiness = new OrderSearch();
            $memberArr           = $orderSearchBusiness->getRelationMember($sid);
        } else {
            $memberArr = [$sid];
        }

        //预留数组，后期会扩展成数组查询
        $pidArr = $paramArr['pid'] ? [$paramArr['pid']] : false;
        $tidArr = $paramArr['tid'] ? [$paramArr['tid']] : false;

        //供应商、分销商数组，预留数组，后期会扩展成数组查询
        $sellerIdArr = $paramArr['aid'] ? [$paramArr['aid']] : false;

        if ($paramArr['reseller_id']) {
            //数组转换
            $buyerIdArr = is_array($paramArr['reseller_id']) ? $paramArr['reseller_id'] : [$paramArr['reseller_id']];
        } else {
            $buyerIdArr = false;
        }

        $ordernumArr = false;
        if (isset($paramArr['ordernum']) && $paramArr['ordernum']) {
            $ordernumArr = is_array($paramArr['ordernum']) ? $paramArr['ordernum'] : [$paramArr['ordernum']];
        }

        if ($paramArr['order_mode'] !== false) {
            $ordermodeIn = is_array($paramArr['order_mode']) ? $paramArr['order_mode'] : [$paramArr['order_mode']];
        } else {
            $ordermodeIn = false;
        }

        if ($paramArr['pay_mode'] !== false) {
            $pmodeIn = is_array($paramArr['pay_mode']) ? $paramArr['pay_mode'] : [$paramArr['pay_mode']];
        } else {
            $pmodeIn = false;
        }

        //新版本数据查询
        $queryRes = $this->_getOrderBusiness()->getBusinessListByOrderService($memberId, $sid, $memberType,
            $memberArr, $dbStart, $dbEnd, $paramArr['page'], $paramArr['size'], $ordernumArr, $paramArr['ordername'],
            $paramArr['person_id'], $paramArr['userMobileSubject'], $paramArr['order_time_start'], $paramArr['order_time_end'],
            $paramArr['play_time_start'], $paramArr['play_time_end'], $paramArr['dtime_start'], $paramArr['dtime_end'],
            $paramArr['begin_time_start'], $paramArr['begin_time_end'], $paramArr['status'], $paramArr['pay_status'],
            $pmodeIn, $ordermodeIn, $paramArr['p_type'], $paramArr['operate_id'],
            $paramArr['check_resource'], $lidArr, $pidArr, $sellerIdArr, $buyerIdArr, $tidArr,
            $paramArr['order_source'], $paramArr['if_print'], $paramArr['mark'], true);

        if ($queryRes['code'] != 200) {
            throw new \Exception($queryRes['msg']);
        }

        if ($queryRes['data']['total'] <= 0) {
            throw new \Exception("没有符合条件的订单");
        }

        $list  = $queryRes['data']['list'];
        $total = $queryRes['data']['total'];

        return ['list' => $list, 'total' => $total];
    }

    /**
     * 取消订单
     * Create by zhangyangzhen
     * Date: 2018/11/5
     * Time: 13:45
     */
    public function orderCancel()
    {
        //主订单号
        $ordernum = I('post.ordernum', '');

        if (!$ordernum) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单号缺失');
        }

        //取消权限检测
        $queryBiz = $this->_getBusinessOrderQueryModel();
        $checkRes = $queryBiz->cancelCheck($ordernum, $this->_memberId);

        if (!$checkRes['status']) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], $checkRes['msg']);
        }

        $modifyBiz       = new Modify();
        $cancelChannel   = OrderConst::WECHAT_BOOK_CANCEL;
        $reqSerialNumber = $modifyBiz->getAuditInfoByOrder($ordernum);
        $result          = $modifyBiz->cancelBaseFormat($ordernum, $this->_memberId, $this->_sid, $cancelChannel, '',
            $reqSerialNumber);
        //$result    = $modifyBiz->cancel($ordernum, $this->_sid, $this->_memberId, $this->_loginInfo['sdtype']);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], [], $result['msg']);
        } else {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '未知错误');
        }
    }

    /**
     * 重发短信
     * Create by zhangyangzhen
     * Date: 2018/11/5
     * Time: 14:00
     */
    public function resendMsg()
    {
        $ordernum = I('post.ordernum', '');
        $mobile   = I('post.mobile', '');

        if (!$ordernum) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单号缺失');
        }

        if ($mobile && !Helpers::isMobile($mobile)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '手机号无效');
        }

        $orderTools = new OrderTools();
        $orderInfo  = $orderTools->getOrderInfo($ordernum, 'ordertel');

        if ($mobile == $orderInfo['ordertel']) {
            $mobile = 0;
        }

        $modifyBiz = new Modify();
        $result    = $modifyBiz->resendMsg($ordernum, $this->_sid, $mobile);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], [], $result['msg']);
        } else {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '未知错误');
        }
    }

    /**
     * 通过门票码获取订单详情
     * <AUTHOR>
     * @date 2019-10-31
     */
    public function getDetailByChkCode()
    {
        $code = 200;
        $data = [];
        $msg  = '';

        try {
            $chkCode = I('post.chk_code', '', 'strval');
            if (empty($chkCode)) {
                throw new \Exception("门票码不能为空");
            }

            $queryParams = [$chkCode];
            $queryRes    = Container::query('orderTouristInfo', 'queryOrderTouristInfoByChkCode', $queryParams);
            if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                throw new \Exception("不存在对应订单号");
            }

            $touristInfoRes = array_unshift($queryRes['data']);

            $orderId = $touristInfoRes['orderId'];

            //获取
            $sid        = $this->_sid;
            $memberType = $this->_dtype;
            $account    = $this->_loginInfo['account'];
            $currentFid = $this->_sid;

            //判断身份 取当前符合要求的会员id集合
            if ($this->isAdmin()) {
                //管理员
                $currentFid = 1;
            } elseif ($memberType == 7) {
                //集团账号
                $orderSearchBusiness = new OrderSearch();
                $currentFid          = $orderSearchBusiness->getRelationMember($sid);
            } elseif (in_array($memberType, [2, 3])) {
                //景区账号
                $salerId    = $account;
                $orderTools = new OrderTools();
                $orderInfo  = $orderTools->getOrderInfo($orderId, 'salerid');

                if ($salerId != $orderInfo['salerid']) {
                    throw new \Exception('无权查看此订单');
                }
            } else {
                //订单查询迁移三期
                $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
                $orderChain             = $orderAidsSplitQueryLib->getListByOrderSingleNew(strval($orderId));

                if (empty($orderChain)) {
                    throw new \Exception('订单分销链数据不存在');
                }

                $buyerid  = array_column($orderChain, 'buyerid');
                $sellerid = array_column($orderChain, 'sellerid');
                $members  = array_unique(array_merge($buyerid, $sellerid));

                if ($currentFid != 1 && !in_array($currentFid, $members)) {
                    throw new \Exception('无权查看此订单');
                }
            }

            $data = $this->_getOrderBusiness()->handleOrderDetail($orderId, $currentFid, $memberType, $sid);

            $data = $this->_handleMallOrderDetail($data);

        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 获取订单明细
     * Create by zhangyangzhen
     * Date: 2018/12/2
     * Time: 14:54
     */
    public function getDetail()
    {
        $code = 200;
        $data = [];
        $msg  = '';

        try {
            $orderId = I('post.ordernum', '', 'strval');

            if (empty($orderId)) {
                throw new \Exception("订单号不能为空");
            }

            //获取
            $sid        = $this->_sid;
            $memberType = $this->_dtype;
            $account    = $this->_loginInfo['account'];
            $currentFid = $this->_sid;

            //判断身份 取当前符合要求的会员id集合
            if ($this->isAdmin()) {
                //管理员
                $currentFid = 1;
            } elseif ($memberType == 7) {
                //集团账号
                $orderSearchBusiness = new OrderSearch();
                $currentFid          = $orderSearchBusiness->getRelationMember($sid);
            } elseif (in_array($memberType, [2, 3])) {
                //景区账号
                $salerId    = $account;
                $orderTools = new OrderTools();
                $orderInfo  = $orderTools->getOrderInfo($orderId, 'salerid');

                if ($salerId != $orderInfo['salerid']) {
                    throw new \Exception('无权查看此订单');
                }
            } else {
                //订单查询迁移三期
                $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
                $orderChain             = $orderAidsSplitQueryLib->getListByOrderSingleNew(strval($orderId));

                if (empty($orderChain)) {
                    throw new \Exception('订单分销链数据不存在');
                }

                $buyerid  = array_column($orderChain, 'buyerid');
                $sellerid = array_column($orderChain, 'sellerid');
                $members  = array_unique(array_merge($buyerid, $sellerid));

                if ($currentFid != 1 && !in_array($currentFid, $members)) {
                    throw new \Exception('无权查看此订单');
                }
            }

            $data = $this->_getOrderBusiness()->handleOrderDetail($orderId, $currentFid, $memberType, $sid);

            $data = $this->_handleMallOrderDetail($data);
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 针对微平台小程序订单详细数据要求处理
     * Create by zhangyangzhen
     * Date: 2018/12/3
     * Time: 13:43
     *
     * @param $data
     *
     * @return mixed
     */
    private function _handleMallOrderDetail($data)
    {
        //分销链，拿出顶级供应商和最末级分销商
        $split = $data['split_list'];
        if (strpos($split, '/')) {
            $data['seller'] = substr($split, 0, strpos($split, '/'));
            $data['buyer']  = substr($split, strrpos($split, '/') + 1);
        } else {
            $data['buyer'] = $data['seller'] = $split;
        }

        //获取票名称，景区名称
        $ticketModel = new Ticket();
        $landModel   = new Land();
        $pidArr      = array_unique(array_column($data['land_module'], 'product_id'));
        $javaApi     = new \Business\CommodityCenter\Ticket();
        $ticketArr   = $javaApi->queryTicketInfoByProductIds($pidArr, 'title,id,pid', 'id', 'title,imgpath,id');
        $proMap      = [];
        foreach ($ticketArr as $ticketInfo) {
            $proMap[$ticketInfo['product']['id']] = [
                'id'      => $ticketInfo['product']['id'],
                'ttitle'  => $ticketInfo['ticket']['title'],
                'tid'     => $ticketInfo['ticket']['id'],
                'pid'     => $ticketInfo['ticket']['pid'],
                'title'   => $ticketInfo['land']['title'],
                'imgpath' => $ticketInfo['land']['imgpath'],
                'landid'  => $ticketInfo['land']['id'],
            ];
        }

        foreach ($data['land_module'] as $key => $val) {
            $data['land_module'][$key]['landid'] = $proMap[$val['product_id']]['landid'];
            $data['land_module'][$key]['ttitle'] = $proMap[$val['product_id']]['ttitle'];
            $data['land_module'][$key]['tid']    = $proMap[$val['product_id']]['tid'];

            $landInfo = $landModel->getLandInfo($proMap[$val['product_id']]['landid'], false, 'id, p_type');

            if ($landInfo['p_type'] == 'F') {
                $sonTicket                              = HandleTicket::getSonTicketId($proMap[$val['product_id']]['tid']);
                $data['land_module'][$key]['sonTicket'] = $sonTicket;
            }
        }

        $data['ltitle'] = $proMap[$data['land_module'][0]['product_id']]['title'];//主订单景区名称

        $query  = new Query();
        $cancel = $query->cancelCheck($data['ordernum'], $this->_memberId);

        $data['cancelBtn'] = $cancel['status'];
        //期票预约订单按钮显示判断
        $data['appointment'] = false;  //是否展示预约按钮  true：是 false：否
        $data['change']      = false;  //是否展示改签按钮  true：是 false：否
        $productExt          = json_decode($data['product_ext'], true);
        if (isset($productExt['reservationOrder']) && $productExt['reservationOrder'] == 1) {
            $data['appointment'] = true;
        }
        //查询终端订单变动表是否存在改签记录
        $auditModel  = new RefundAuditModel();
        $changeInfos = $auditModel->getTicketChangingOrders([$data['ordernum']], 'id');
        //获取票类快照属性判断是否允许改签
        $versionInfo = json_decode($data['ext_content'], true);
        $versionInfo = $versionInfo['ticketVersion'] ?? 0;
        $ticketApi   = new TicketSnapshot();
        $ticketInfo  = $ticketApi->queryTicketSnapshot($data['order_user_info']['tid'], $versionInfo);
        if ($ticketInfo['data']['uuJqTicketDTO']['ticketChangingRange'] != 0 && empty($changeInfos) && $productExt['reservationOrder'] == 1) {
            $data['change'] = true;
        }

        return $data;
    }

    /**
     * 获取订单操作记录
     * Create by zhangyangzhen
     * Date: 2018/11/5
     * Time: 14:28
     */
    public function operatingRecord()
    {
        //主订单号
        $ordernum = I('post.ordernum', '');

        if (!$ordernum) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单号缺失');
        }

        $queryBiz = $this->_getBusinessOrderQueryModel();

        $result = $queryBiz->operatingRecord($this->_sid, $ordernum);

        if ($result && $result['code'] == self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_SUCCESS, $result['data']);
        } else {
            $this->apiReturn(self::CODE_NO_CONTENT, [], $result['msg']);
        }
    }

    /**
     * 订单查询-获取可分销产品名称
     * Create by zhangyangzhen
     * Date: 2018/11/17
     * Time: 14:35
     */
    public function getProductNameList()
    {
        $num = I('post.num', 10, 'intval');
        $key = I('post.key', '', 'trim');
        if (!$key) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入关键字');
        }

        if (!$num || $num <= 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '非法数量');
        }

        $listModel = new Listing();
        $product   = $listModel->getGroundingListing($this->_sid, $key, '', $num);

        if ($product['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($product['code'], [], $product['msg']);
        } else {
            $this->apiReturn(self::CODE_SUCCESS, $product['data']);
        }
    }

    /**
     * 订单查询 - 获取供应商
     * Create by zhangyangzhen
     * Date: 2018/11/8
     * Time: 13:53
     */
    public function getAllSuppier()
    {
        $keywords = I('post.keywords', '', 'strval,trim');

        $bus = $this->_getBusMemberRelation();
        $res = $bus->getOrderAllSuppier($this->_sid, false, $keywords);

        if ($res['code'] != 200) {
            $this->apiReturn(self::CODE_SUCCESS);
        }

        $this->apiReturn(self::CODE_SUCCESS, $res['data']);
    }

    /**
     * 订单查询 - 获取分销商
     * Create by zhangyangzhen
     * Date: 2018/11/19
     * Time: 16:28
     */
    public function getAllReseller()
    {
        $getEvoluteGroupBiz = new EvoluteGroupUser();
        $resellerGroup      = $getEvoluteGroupBiz->getFidCountBySid($this->_sid);
        $groupList          = [];
        if ($resellerGroup['code'] == 200 && !empty($resellerGroup['data'])) {
            $groupList   = array_values($resellerGroup['data']);
        }

        if (!$groupList) {
            $sid = $this->_sid;
            // 调用新版添加默认分组
            $addEvoluteGroupBiz = new EvoluteGroup();
            $addRes             = $addEvoluteGroupBiz->addGroup($sid, '旅行社', $sid);
            $addEvoluteGroupBiz->addGroup($sid, '酒店', $sid);
            if ($addRes['code'] == 200 && !empty($addRes['data'])) {
                //调用新版接口
                $upEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroup();
                $upEvoluteGroupBiz->setDefaultGroup($sid, $sid, $addRes['new_group']);
            }
        }

        $arrMemberId = []; // 所有已分组用户ID集合
        foreach ($groupList as $group) {
            if (isset($allGroupFid[$group['id']]) && !empty($allGroupFid[$group['id']]['fidArr'])) {
                $fidArr = array_unique($allGroupFid[$group['id']]['fidArr']);
                if ($fidArr) {
                    $arrMemberId = array_merge($arrMemberId, $fidArr);
                }
            }
        }

        $shipModel    = $this->_getMemberRelationshipModel($this->_sid);
        $distributors = $shipModel->getValidResellers($this->_sid);

        if (count($distributors['list']) <= 0) {
            $this->apiReturn(self::CODE_SUCCESS, [], '暂无分销商');
        }

        $didArr          = [];
        $newDistributors = [];
        foreach ($distributors['list'] as $distributor) {
            unset($distributor['py']);
            $didArr[]                            = $distributor['id'];
            $newDistributors[$distributor['id']] = $distributor;
        }

        $groups = [];
        if ($groupList) {
            foreach ($groupList as $group) {
                $groupDidArr  = $group['fidArr'];
                $groupMembers = [];
                foreach ($groupDidArr as $did) {
                    if (in_array($did, $didArr)) {
                        $groupMembers[] = $newDistributors[$did];
                    }
                }

                $groups[] = [
                    'group_id'      => $group['id'],
                    'group_name'    => $group['name'],
                    'group_default' => $group['default'] ?: 0,
                    'group_members' => $groupMembers,
                ];
                $didArr   = array_diff($didArr, $groupDidArr);
            }
        }

        //未经分组的分销商，默认为“未分组”
        $unJoinMembes = [];
        foreach ($didArr as $did) {
            $unJoinMembes[] = $newDistributors[$did];
        }

        $groups[] = [
            'group_id'      => 0,
            'group_name'    => '未分组',
            'group_members' => $unJoinMembes,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $groups);
    }

    /**
     * 获取分销商分组和供应商用户
     * Create by zhangyangzhen
     * Date: 2018/12/11
     * Time: 18:45
     */
    public function getResellerAndSuppier()
    {
        // 权限判断
        $auth = $this->_checkMemberAuth(['supplyPartner', 'fxPartner']);

        if (!$auth) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '您没有查看的权限');
        }

        // 设置权限标识
        if (count($auth) == 2) {
            $type = 2;
        } elseif (isset($auth['fxPartner'])) {
            $type = 1;
        } elseif (isset($auth['supplyPartner'])) {
            $type = 0;
        }

        $bus = $this->_getBusMemberRelation();
        $res = $bus->getPartnerCount($this->_sid, $type);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 通过分销商分组ID获取分销商
     * Create by zhangyangzhen
     * Date: 2018/12/12
     * Time: 10:00
     *
     * @param  int    gid 分销商分组ID
     * @param  string keyword 关键字
     * @param  int    page 分页
     * @param  int    size 每页数量
     */
    public function getResellerByGroupId()
    {
        // 权限判断
        $auth = $this->_checkMemberAuth(['fxPartner']);

        if (!$auth) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无查看分销商的权限');
        }

        $groupId = I('post.gid', -1, 'intval');
        $keyword = I('post.keyword', '', 'trim');
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 5, 'intval');

        if ($groupId < 0 || !is_numeric($page) || $page < 0 || !is_numeric($size) || $size < 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $bus = $this->_getBusMemberRelation();
        $res = $bus->getGroupResellerList($this->_sid, $groupId, $page, $size, false, $keyword);

        if (!in_array($res['code'], [200, 202])) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(self::CODE_SUCCESS, $res['data'], $res['msg']);
    }

    /**
     * 模糊查询分销商，供应商
     * Create by zhangyangzhen
     * Date: 2018/12/12
     * Time: 10:24
     *
     * @param  string keyword 关键字
     * @param  string type    筛选类型  'supplier'=供应商|'reseller'=分销商
     * @param  int page 页码
     * @param  int size 每页数量
     */
    public function searchPartner()
    {
        // 权限判断
        $auth = $this->_checkMemberAuth(['supplyPartner', 'fxPartner']);

        if (!$auth) {
            $this->apiReturn(201, [], '您没有查看的权限');
        }

        $keyword = I('post.keyword', '', 'trim');
        $type    = I('post.type', 'supplier', 'strval');
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 5, 'intval');

        if (!in_array($type, ['supplier', 'reseller'])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '用户类型参数错误');
        }

        $bus = $this->_getBusMemberRelation();
        $res = $bus->searchPartner($this->_sid, $keyword, $type, $page, $size);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $data = $res['data'];

        // 供应商信息不可见
        if (!isset($auth['supplyPartner']) || $type == 'reseller') {
            $data['supplier'] = [];
        }

        // 分销商信息不可见
        if (!isset($auth['fxPartner']) || $type == 'supplier') {
            $data['reseller'] = [];
        }

        $returnData = array_merge($data['supplier'], $data['reseller']);

        $this->apiReturn(self::CODE_SUCCESS, $returnData);
    }

    /**
     * 获取供应商授信信息
     * Create by zhangyangzhen
     * Date: 2018/12/12
     * Time: 13:44
     *
     * @param  int aid 供应商ID
     */
    public function getSupplierInfo()
    {
        $aid = I('post.aid', 0, 'intval');

        if (!$aid || !is_numeric($aid)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        //是否 是合作供应商
        $bus = $this->_getBusMemberRelation();
        $res = $bus->getSupplierCount($this->_sid);

        if ($res['code'] != 200) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], $res['msg']);
        }

        $supplierList = $res['data']['supplier_list'];
        if ($supplierList) {
            $supplierList = array_column($supplierList, 'id');
        }

        if (!in_array($aid, $supplierList)) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '不是合作供应商');
        }

        //当前登录用户的账户余额
        $memberModel = $this->_getMemberModel();
        $remain      = $memberModel->getMoney($this->_sid, 0);

        if ($remain === false) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '系统异常，请重试【err_code=2601】');
        }

        //供应商信息
        $supplierInfo = $memberModel->getMemberInfo($aid, 'id', 'dname,cname,mobile,account');

        if (!$supplierInfo || empty($supplierInfo)) {
            $this->apiReturn(self::CODE_NO_CONTENT, [], '供应商账号不存在');
        }

        // 供应商授信
        $credit     = $kmoney = 0;
        $creditInfo = $this->getCreditInfo($this->_sid, $aid);
        if ($creditInfo) {
            if ($creditInfo['mode'] == 1) {
                $credit = -1; // 不限
            } elseif ($creditInfo['mode'] == 0 && isset($creditInfo['credit'])) {
                $credit = $creditInfo['basecredit'];
            }
            $kmoney = $creditInfo['kmoney'];
        }

        $returnData = [
            'credit'  => $credit, // 信用额度 -分，-1为不限
            'kmoney'  => $kmoney, // 信用余额 -分
            'remain'  => $remain, // 当前用户账户余额 -分
            'dname'   => $supplierInfo['dname'],
            'cname'   => $supplierInfo['cname'],
            'mobile'  => $supplierInfo['mobile'],
            'account' => $supplierInfo['account'],
            'aid'     => $aid,
            'did'     => $this->_sid,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $returnData);
    }

    /**
     * 获取分销商授信账户信息
     * Create by zhangyangzhen
     * Date: 2018/12/12
     * Time: 15:33
     *
     * @param  int did 分销商ID
     */
    public function getResellerInfo()
    {
        $did = I('post.did', -1, 'intval');   //分销商ID

        if (!$did || $did < 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $memberModel = $this->_getMemberModel();

        //分销商当前所属分组信息
        $bus = $this->_getBusMemberRelation();
        $res = $bus->getResellerGroupInfo($this->_sid, $did);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $groupInfo = [
            'group_id'   => $res['data']['group_id'],
            'group_name' => $res['data']['group_name'],
        ];

        // 分销商信息
        $memberInfo = $memberModel->getMemberInfo($did, 'id', 'dname,cname,mobile,account');

        // 供应商授信
        $credit     = $kmoney = 0;
        $creditInfo = $this->getCreditInfo($did, $this->_sid);

        if ($creditInfo) {
            if ($creditInfo['mode'] == 1) {
                $credit = -1; // 不限
                $kmoney = $creditInfo['kmoney'];
            } elseif ($creditInfo['mode'] == 0 && isset($creditInfo['credit'])) {
                $credit = $creditInfo['basecredit'];
                $kmoney = $creditInfo['kmoney'];
            }
        }

        $remark = '';
        if (isset($memberInfo['id'])) {
            $memberbus  = new MemberRelationshipRemark();
            $remarkInfo = $memberbus->queryDistributorRemarkName($did, $this->_sid);
            if ($remarkInfo['code'] == 200 && !empty($remarkInfo['data']['remark'])) {
                $remark = $remarkInfo['data']['remark'];
            }
        }

        $return = [
            'credit'  => $credit, // 信用额度 -分 -1为不限
            'kmoney'  => $kmoney, // 信用余额 -分
            'dname'   => $memberInfo['dname'],
            'cname'   => $memberInfo['cname'],
            'mobile'  => $memberInfo['mobile'],
            'account' => $memberInfo['account'],
            'remark'  => $remark,
            'group'   => $groupInfo,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $return);
    }

    /**
     * 根据分组名称模糊查询
     * <AUTHOR>  Li
     * @date  2020-10-30
     */
    public function queryGroupNameListBySidAndGroupName()
    {
        $sid        = $this->isLogin();
        $pageSize   = I('post.page_size', 10, 'intval');        //分页长度
        $pageNumber = I('post.page', 1, 'intval');              //页码
        $groupName  = I('post.group_name', '', 'strval');     //分组名称

        $relationBiz = new \Business\Member\MemberEvoluteRelation();
        $result      = $relationBiz->queryGroupNameListBySidAndGroupName($sid, $groupName, $pageSize,
            $pageNumber);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $list    = [];
        $javaApi = new \Business\JavaApi\Product\EvoluteProduct();

        foreach ($result['data']['list'] as $item) {
            $hasPrice = 0;
            $checkRes   = $javaApi->queryProductCount($item['group_id'], $sid, $sid);
            if ($checkRes['code'] == 200 && !empty($checkRes['data'])) {
                $hasPrice = 1;
            }
            $list[] = [
                'id'       => $item['group_id'],
                'name'     => $item['group_name'],
                'hasPrice' => $hasPrice,
            ];
        }

        $this->apiReturn(200, $list, '分组获取成功');
    }

    /**
     * 获取所有分组
     * Create by zhangyangzhen
     * Date: 2018/12/12
     * Time: 16:06
     *
     * @param  string keyword 关键字查询
     */
    public function getResellerGroups()
    {
        $keyword  = I('post.keyword', '', 'trim');
        $page     = I('post.page', 1, 'intval');
        $pageSize = I('post.size', 300, 'intval');

        $relationBiz = new \Business\Member\MemberEvoluteRelation();
        $result   = $relationBiz->queryGroupNameListBySidAndGroupName($this->_sid, $keyword, $pageSize, $page);

        if ($result['code'] != 200 || empty($result['data']['list'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $javaApi   = new \Business\JavaApi\Product\EvoluteProduct();
        $groupInfo = [];
        foreach ($result['data']['list'] as $group) {
            $hasPrice = 0;
            $checkRes = $javaApi->queryProductCount($group['group_id'], $this->_sid, $this->_sid);
            if ($checkRes['code'] == 200 && !empty($checkRes['data'])) {
                $hasPrice = 1;
            }
            $groupInfo[] = [
                'id'       => $group['group_id'],
                'name'     => $group['group_name'],
                'hasPrice' => $hasPrice,
            ];
        }

        $this->apiReturn(self::CODE_SUCCESS, $groupInfo);
    }

    /**
     * 修改指定的分销商的所属分组
     * Create by zhangyangzhen
     * Date: 2018/12/12
     * Time: 16:52
     *
     * @param  int did 分销商id
     * @param  int gid 分组id
     */
    public function changeResellerGroup()
    {
        $did = I('post.did', -1, 'intval');
        $gid = I('post.gid', -1, 'intval');

        if (!$did || $did < 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分销商不存在');
        }

        if (!$gid || $gid < 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请选择新的分组');
        }

        //禁止频繁操作
        $redis    = Cache::getInstance('redis');
        $lockey   = "{$this->_sid}:{$did}:move_group";
        $lockInfo = $redis->lock($lockey, 1, $lockTime = 120);

        if (!$lockInfo) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '分销商正在分组.... 请稍后再试');
        }

        // 操作日志
        $log = 'move_group:memberID=' . $this->_memberId . ':resellerId=' . $did . ':newGroupId=' . $gid . ':' . json_encode(I(''));
        pft_log('price/request', $log, 'day');

        // 分组切换
        $bus = $this->_getBusMemberRelation();
        $bus->setOperator($this->_sid, $this->_loginInfo['saccount']);
        $res = $bus->changeResellerGroup($this->_sid, $did, $gid);

        $redis->rm($lockey);

        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(self::CODE_SUCCESS, [], '分组成功');
    }

    /**
     * 获取分销商和供应商的授信信息
     * Create by zhangyangzhen
     * Date: 2018/12/12
     * Time: 14:05
     *
     * @param  int $sid 分销商ID
     * @param  int $aid 供应商ID
     *
     * @return array|bool
     */
    private function getCreditInfo($sid = 0, $aid = 0)
    {
        if (!$sid || !$aid) {
            return false;
        }

        $return = [];

        $mModel = $this->_getMemberModel();
        $cmoney = $mModel->getMoney($sid, 4, $aid);
        if ($cmoney && is_array($cmoney)) {
            $cmoney = array_pop($cmoney);
            if ($cmoney['mode'] == 1) {
                $return = [
                    'mode'   => 1,                      // 模式: 不限额度
                    'kmoney' => $cmoney['kmoney']       // 信用余额 -分
                ];
            } else {
                $credit = $cmoney['kmoney'] + $cmoney['basecredit'];
                $return = [
                    'mode'       => 0,                      // 模式: 固定额度
                    'kmoney'     => $cmoney['kmoney'],      // 信用余额 -分
                    'basecredit' => $cmoney['basecredit'],  // 信用额度 -分
                    'credit'     => $credit                 // 可用额度 -分
                ];
            }
        }

        return $return;
    }

    /**
     * 获取分销商分组和供应商权限判断
     * Create by zhangyangzhen
     * Date: 2018/12/11
     * Time: 18:14
     *
     * @param $authArr
     *
     * @return array
     */
    private function _checkMemberAuth($authArr)
    {
        $return = [];

        if (!$authArr || !is_array($authArr)) {
            return $return;
        }

        if ($this->_dtype == 6) {
            $allowAuth = $this->_getMemberModel()->getMemberInfo($this->_memberId, 'id', 'member_auth');
            if ($allowAuth) {
                $allowAuth = explode(',', $allowAuth['member_auth']);
                foreach ($authArr as $auth) {
                    if (in_array($auth, $allowAuth)) {
                        $return[$auth] = 1; // 1=有权限
                    }
                }
            }
        } else {
            foreach ($authArr as $auth) {
                $return[$auth] = 1;
            }
        }

        return $return;
    }

    /**
     * 供应商给下级分销商修改授信额度
     * Create by zhangyangzhen
     * Date: 2018/12/12
     * Time: 18:10
     *
     * @param  int    mode 授信模式,0=固定额度,1=不限额度
     * @param  float  money 授信额度 单位：元（mode=0时生效）
     * @param  string meno 备注
     * @param  int    did 分销商ID
     */
    public function changeCredit()
    {
        $mode  = I('post.mode', 0, 'intval');
        $money = I('post.money', 0, 'floatval');
        $memo  = I('post.memo', '', 'strval');
        $did   = I('post.did', -1, 'intval');

        // 参数校验
        if (!$did || $did <= 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请选择分销商');
        }

        // 禁止频繁操作
        if ($mode == 1) {
            // 不限额度
            $lockey = "lock:set_fund:set_unlimit:{$this->_memberId}_{$did}";
            $memo   .= '微平台小程序设置不限额度';
        } else {
            // 固定额度
            $lockey = "lock:set_fund:set_limit:{$this->_memberId}_{$did}_{$money}";
            $memo   .= '微平台小程序设置固定额度';
        }

        $cache    = Cache::getInstance('redis');
        $lockInfo = $cache->lock($lockey, 1, 60);

        if (!$lockInfo) {
            $this->apiReturn(201, [], '正在为您修改授信额度，请稍后');
        }

        // 将操作员写入备注中
        if ($this->_memberId != $this->_sid) {
            $memo .= "【员工：{$this->_loginInfo['dname']}({$this->_loginInfo['account']})】";
        }

        //将金额转换给分
        $money = $money * 100;

        // 模式判断
        $creditBiz = new Credit();
        $res       = $creditBiz->setLimit($this->_sid, $did, $money, $this->_memberId, $memo, $mode);

        //清除锁
        $cache->rm($lockey);

        if ($res['code'] != 200) {
            $this->apiReturn(201, [], $res['msg']);
        }

        $this->apiReturn(200, [], '授信成功');
    }

    /**
     * 供应商给下级分销商设置的线下还款
     * Create by zhangyangzhen
     * Date: 2018/12/13
     * Time: 10:12
     *
     * @param  int    did 分销商ID
     * @param  float  money 线下还款金额，单位：元
     * @param  string memo 备注
     */
    public function offlinePay()
    {
        $did   = I('post.did', -1, 'intval');
        $money = I('post.money', 0, 'floatval');
        $memo  = I('post.memo', '', 'trim');

        // 参数校验
        if (!$did || $did <= 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请选择分销商');
        }

        if (!$money || $money <= 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '还款金额必须大于0元');
        }

        // 将操作员写入备注中
        if ($this->_memberId != $this->_sid) {
            $memo .= "【员工：{$this->_loginInfo['dname']}({$this->_loginInfo['account']})】";
        }

        // 禁止频繁操作
        $cache    = Cache::getInstance('redis');
        $lockey   = "lock:set_fund:offline:{$this->_memberId}_{$did}_{$money}";
        $lockInfo = $cache->lock($lockey, 1, $lockTime = 120);
        if (!$lockInfo) {
            $this->apiReturn(self::CODE_NO_CONTENT, [], '业务正在处理中，请稍后再试');
        }

        $bus = new Credit();
        $res = $bus->offlinePay($this->_sid, $did, $money, $this->_memberId, $memo);

        //清除锁
        $cache->rm($lockey);

        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], [], $res['msg']);
        } else {
            $this->apiReturn(self::CODE_SUCCESS, [], '还款成功');
        }
    }

    /**
     * 系统公告列表
     * Create by zhangyangzhen
     * Date: 2018/11/21
     * Time: 18:19
     *
     * @param  int $type 通知类型
     * @param  int $page 页码
     * @param  int $size 条数
     * @param  int $status 资讯类型（0系统公告，1功能需求）
     * @param  string $keyword 关键字
     */
    public function getSysAnnounce()
    {
        $aModel = $this->_getAnnounceModel();

        $type    = I('post.type', 0, 'intval');
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 10, 'intval');
        $status  = I('post.status', 0, 'intval');
        $keyword = I('post.keyword', '', 'trim');

        $field = 'id, title, details, create_time, update_time, img_url';
        $order = 'update_time desc';

        if ($keyword) {
            // 公告标题模糊搜索
            $map = [
                'title' => ['like', "%{$keyword}%"],
            ];

        } else {
            $month = date('Y-m-d H:i:s', strtotime("-1 month"));
            $map   = [
                'update_time' => ['egt', $month],
            ];
        }

        // 公告列表
        $list = $aModel->getSysNotice($type, $page, $size, $status, $field, $order, $map);

        if ($list) {
            // 获取已读公告
            $aidArr      = array_column($list, 'id');
            $hasReadAids = $aModel->hasReadAids($this->_memberId, $aidArr);

            // 公告内容优化
            $maxLength = 50;
            foreach ($list as $key => $announce) {
                $detail = strip_tags(htmlspecialchars_decode($announce['details']));
                $length = mb_strlen($detail);

                if ($length > $maxLength) {
                    $list[$key]['details'] = mb_substr($detail, 0, $maxLength) . '...';
                } else {
                    $list[$key]['details'] = $detail;
                }

                // 已读标识 1已读 0未读
                if (in_array($announce['id'], $hasReadAids)) {
                    $list[$key]['read'] = 1;
                } else {
                    $list[$key]['read'] = 0;
                }
            }
        }

        $this->apiReturn(self::CODE_SUCCESS, $list);
    }

    /**
     * 获取系统公告详细内容
     * Create by zhangyangzhen
     * Date: 2018/11/4
     * Time: 15:53
     *
     * @param  int id 公告ID
     */
    public function getAnnounceContent()
    {
        $aModel = $this->_getAnnounceModel();

        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请选择公告');
        }

        $res = $aModel->getSysNoticeContent($id);

        $res['details'] = htmlspecialchars_decode($res['details']);

        // 设置公告为已读
        if ($res && !$aModel->is_read($this->_memberId, $res['id'])) {
            $aModel->add_read($this->_memberId, $res['id']);
        }

        $this->apiReturn(self::CODE_SUCCESS, $res);
    }

    /**
     * 判断登陆用户是不是管理员
     * Create by zhangyangzhen
     * Date: 2018/11/2
     * Time: 14:13
     * @return bool
     */
    public function isAdmin()
    {
        if ($this->_sid && $this->_sid == 1) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 订单通知
     * Create by zhangyangzhen
     * Date: 2018/10/31
     * Time: 13:38
     */
    public function posterNotify()
    {
        $model = new AccountInfo();
        $type  = I('post.type');

        if (!in_array($type, [0, 1])) {
            $this->apiReturn('204', [], '参数错误');
        }

        $res = $model->posterNotify($type, $this->_memberId);

        if ($res['code'] == self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_SUCCESS, [], '成功');
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);

        }
    }

    /**
     * 个人中心修改密码
     * Create by zhangyangzhen
     * Date: 2018/10/18
     * Time: 16:53
     */
    public function updatePassword()
    {
        $old  = I('post.old', '');
        $new1 = I('post.new1', '');
        $new2 = I('post.new2', '');

        if (empty($old) || empty($new1) || empty($new2)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数不能为空');
        }

        if ($new1 != $new2) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '两次输入的密码不一致');
        }

        //判断旧密码是否正确
        $old = $this->_getMd5Password($old);

        //用户二期 - 信息获取修改
        $CustomerBus = new \Business\Member\Customer();
        $res         = $CustomerBus->checkOldPassword($this->_memberId, $old);
        if (!$res) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '原密码错误');
        }

        $Member = new \Business\Member\Member();
        $rst    = $Member->updateMemberPassword($this->_memberId, $new1);
        if ($rst) {
            $this->apiReturn(self::CODE_SUCCESS, [], '修改密码成功');
        } else {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '修改密码失败');
        }
    }

    /**
     * 获取用户信息
     * Create by zhangyangzhen
     * Date: 2018/10/31
     * Time: 11:50
     */
    public function getMemberInfo()
    {
        $MemberModel = $this->_getMemberModel();

        $field1 = 'id,account,member_auth,dname,mobile,headphoto,status,dtype,customer_id';
        $member = $MemberModel->getMemberInfo($this->_memberId, 'id', $field1);
        $MemberBus   = new \Business\Member\Member();
        $CustomerBus = new \Business\Member\Customer();
        $memberInfo  = $MemberBus->getInfo($this->_memberId, true);
        if ($memberInfo) {
            $memberInfo['id']          = $this->_memberId;
            $memberInfo['com_type']    = $MemberBus::__CORP_KIND_ARR__[$memberInfo['corp_kind']];
            $member_auth               = $MemberModel->getMemberInfo($this->_memberId, 'id', 'member_auth');
            $memberInfo['member_auth'] = $member_auth['member_auth'];
            $customerInfo              = $CustomerBus->getCustomerInfoByMemberId($this->_memberId);
            $memberExt                 = [
                'cname'   => $customerInfo["cname"],
                'address' => $customerInfo["address"],
            ];
        }

        $data = array_merge($memberInfo, $memberExt);

        $auth = explode(',', $member['member_auth']);

        //判断分销通知是否开启
        if (in_array('poster_notify', $auth)) {
            $data['type'] = 1;
        } else {
            $data['type'] = 0;
        }

        if (empty($data)) {
            $this->apiReturn(self::CODE_SUCCESS, [], 'success');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, $data, 'success');
        }
    }

    /**
     * 修改个人资料
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 14:53
     */
    public function updateMember()
    {
        $dname    = I('post.dname');
        $comtype  = I('post.com_type');
        $address  = I('post.address', '', 'trim');
        $province = I('post.province');
        $city     = I('post.city');
        $business = I('post.business');
        $headimg  = I('post.headimg');

        $data = [
            'dname'     => $dname,
            'com_type'  => $comtype,
            'province'  => $province,
            'city'      => $city,
            'address'   => $address,
            'business'  => $business,
            'headphoto' => $headimg,
        ];

        $busModel = new \Business\Member\AccountInfo();
        $res      = $busModel->microModifyUserInfo($this->_memberId, $data);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 提现申请
     * @deprecated 已废弃，该方法已迁移至WxService中
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 15:37
     */
    public function withdrawCash()
    {
        $result = $this->_checkWithdrawAuth();
        if ($result != true) {
            $this->apiReturn(201, [], '您没有提现权限');
        }

        //获取用户禁止提现配置
        $limitApi      = new MemberLimit();
        $withdrawLimit = $limitApi->queryFunctionLimitByFidAndLimitType($this->_memberId, 2);
        if ($withdrawLimit['code'] == 200 && !empty($withdrawLimit['data'])) {
            $this->apiReturn(204, [], '该账户处于提现禁用状态，不可操作');
        }

        $wdMoney  = I('post.cash', 0, 'intval');
        $typeName = I('post.type_name', '');

        if (!$wdMoney || !$typeName) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数缺失');
        }

        // 提现操作
        $bus    = $this->_getWithdrawModel();
        $source = 2;//微平台
        $res    = $bus->apply($this->_memberId, $wdMoney, $typeName, 0, $this->_memberId, $this->_loginInfo['dname'],
            $source);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 员工提现权限判断
     * Create by zhangyangzhen
     * Date: 2018/10/29
     * Time: 16:15
     * @return bool
     */
    private function _checkWithdrawAuth()
    {
        if (in_array($this->_dtype, [0, 1])) {
            return true;
        } elseif ($this->_dtype == 6) {
            $mModel = $this->_getMemberModel();

            // 员工账号有效性检测
            $res = $mModel->getStaffBySonId($this->_memberId);
            if (!$res || $res['parent_id'] != $this->_sid) {
                $this->apiReturn(201, [], '您没有提现权限');
            }

            // 员工账号权限判断
            $son = $mModel->getMemberInfo($this->_memberId);
            if (!$son) {
                $this->apiReturn(201, [], '员工账号不存在');
            }

            if (!in_array('accourtmanage', explode(',', $son['member_auth']))) {
                $this->apiReturn(201, [], '您没有提现权限');
            }
        } else {
            $this->apiReturn(201, [], '您没有提现权限');
        }
    }

    /**
     * 账户可提现金额判断
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 15:58
     */
    public function getValidWithdrawMoney()
    {
        $orderStatus  = [0, 2, 7];

        $bus = $this->_getAccountMoneyModel();
        $res = $bus->getValidWithdarwMoney($this->_memberId, $orderStatus, true);
        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(self::CODE_SUCCESS, ['money' => $res['data']['withdraw_money']]);
    }

    /**
     * 账户余额
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 16:15
     */
    public function getAccountMoney()
    {
        $moneyBus = $this->_getAccountMoneyModel();
        $res      = $moneyBus->getAccountMoney($this->_memberId);
        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(self::CODE_SUCCESS, ['money' => $res['data']['money']]);
    }

    /**
     * 获取用户银行列表
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 16:20
     */
    public function getBankList()
    {
        $bus = $this->_getAccountMoneyModel();
        $res = $bus->getBankList($this->_memberId);
        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(self::CODE_SUCCESS, $res['data'], 'success');
    }

    /**
     * 获取已验证过的银行账户信息
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 16:49
     */
    public function getBankAccount()
    {
        $bus = $this->_getAccountMoneyModel();
        $res = $bus->getBankAccount($this->_memberId);
        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(self::CODE_SUCCESS, $res['data'], 'success');
    }

    /**
     * 获取支行信息
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 16:49
     */
    public function subbranchList()
    {
        $page = I('post.page', 1);
        $size = I('post.size', 50);
        $name = I('post.name');

        $cityId     = I('post.city_id', 0);
        $bankId     = I('post.bank_id', 0);
        $provinceId = I('post.province_id', 0);

        if (!$cityId || !$bankId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $model = $this->_getBanksModel();
        $res   = $model->getSubbranch($cityId, $bankId, $provinceId, $name, $page, $size);

        if ($res === false) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, $res);
        }
    }

    /**
     * 获取银行列表
     * Create by zhangyangzhen
     * Date: 2018/6/4
     * Time: 10:29
     */
    public function bankList()
    {
        $page = I('post.page', 1);
        $size = I('post.size', 700);

        $model = $this->_getBanksModel();

        //银行列表
        $list = $model->getBanks($page, $size);

        if ($list === false) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, $list);
        }
    }

    /**
     * 获取省份列表
     * Create by zhangyangzhen
     * Date: 2018/10/22
     * Time: 16:00
     */
    public function provinceList()
    {
        $model = $this->_getBanksModel();

        $province = $model->getBankProvince();

        $province = !empty($province) ? $province : [];

        $this->apiReturn(self::CODE_SUCCESS, $province, 'success');
    }

    /**
     * 获取城市列表
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 16:55
     */
    public function cityList()
    {
        $province = I('post.province_id', false);
        if (!$province) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $model = $this->_getBanksModel();

        //城市列表
        $list = $model->getCity($province);

        if ($list === false) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, ['list' => $list]);
        }
    }

    /**
     * 添加/编辑银行卡
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 16:57
     */
    public function addBankCard()
    {
        $type        = I('post.type', '');   //银行卡序号，bank1=第一张银行卡，bank2=第二张银行卡
        $bankName    = I('post.bankName', ''); //支行名称
        $bankCode    = I('post.bankCode', ''); //支行行号
        $bankAccount = I('post.bankAccount', ''); //账号
        $accountName = I('post.accountName', ''); //账户名
        $accType     = I('post.accType', '');    //银行卡类型，0=借记卡/1=存折/2=信用卡/3=公司账号

        $model = new \Business\Member\Member();
        $res   = $model->addBankAccount($this->_sid, $type, $bankName, $bankCode, $bankAccount, $accountName, $accType);

        if ($res['code'] == self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_SUCCESS, [], '保存成功');
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
    }

    /**
     * 删除银行卡
     * Create by zhangyangzhen
     * Date: 2018/10/30
     * Time: 10:15
     */
    public function deleteBankCard()
    {
        $type = I('post.type', 0);//行卡类别，1 or 2

        if (!$type || empty($type) || !in_array($type, [1, 2])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '银行卡类别不能为空');
        }

        $model = new AccountMoney();
        $res   = $model->deleteBankCard($this->_memberId, $type);

        if ($res != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], [], '银行卡删除失败');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, [], '银行卡删除成功');
        }
    }

    /**
     * 获取编辑银行卡信息
     * Create by zhangyangzhen
     * Date: 2018/6/4
     * Time: 11:32
     */
    public function getCardInfo()
    {
        $type = I('post.type', 0, 'intval');
        $bus  = $this->_getAccountMoneyModel();

        if (!in_array($type, [1, 2])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        $res = $bus->getCardInfo($this->_memberId, $type);

        if ($res['code'] == 200) {
            $data               = $res['data'];
            $banksModel         = $this->_getBanksModel();
            $data['bankName']   = $banksModel->getBankByCode($data['bank_id']);
            $data['branchName'] = $banksModel->getSubbranchByCode($data['branch_id']);

            $this->apiReturn(self::CODE_SUCCESS, $data, '成功');
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
    }

    /**
     * 获取今日统计数据
     * Create by zhangyangzhen
     * Date: 2018/9/7
     * Time: 10:39
     *
     * @param  int $isFlush 是否强制刷新
     *
     * @return array|mixed
     */
    private function _getTodayInfo($isFlush = 0)
    {
        $homeOrderBiz          = new \Business\Statistics\HomeOrder();
        $statisticsConfigLimit = $homeOrderBiz->getHomeOverviewConfig($this->_sid);
        //全部字段配置key
        $statisticsSignKey = $statisticsConfigLimit->allKeySign();
        //获取全部配置
        $extFilter = $statisticsConfigLimit->getConfig();

        $todayKey  = $this->_prefix . "today_summary:" . $this->_memberId . ":$statisticsSignKey";
        $cacheTime = 30 * 60;

        $redis = Cache::getInstance('redis');
        $data  = $redis->get($todayKey);

        if (!$data || $isFlush) {
            $data = [];
            //如果是缓存获取或是强制刷新
            $sdtype = $this->_loginInfo['sdtype'];
            if ($sdtype == 7) {
                //集团账号获取所有的成员
                $memberList = $this->_getGroupMemberList($this->_memberId);
                $fidArr     = $memberList;
            } else {
                $fidArr = $this->_memberId;
            }

            $staticModel = $this->_getStatisticsModel();
            $date        = date('Y-m-d');
            //预订数据
            $orderRes              = $staticModel->getDataByFidTime($fidArr, $date, 1, $extFilter);
            $data['order_tickets'] = empty($orderRes['ticket_num']) ? 0 : $orderRes['ticket_num'];
            $data['order_money']   = empty($orderRes['sale_money']) ? 0 : $orderRes['sale_money'];

            //验证数据
            $checkRes              = $staticModel->getDataByFidTime($fidArr, $date, 2, $extFilter);
            $data['check_tickets'] = empty($checkRes['ticket_num']) ? 0 : $checkRes['ticket_num'];
            $data['check_money']   = empty($checkRes['sale_money']) ? 0 : $checkRes['sale_money'];

            //缓存数据
            $cacheData = json_encode($data);
            $redis->set($todayKey, $cacheData, '', $cacheTime);
        } else {
            $data = json_decode($data, true);
        }

        if (!$data) {
            $this->apiReturn(400, [], '数据获取失败');
        }

        //数据统计时间
        $data['calculate_time'] = date('Y-m-d H:i:s');

        return $data;
    }

    /**
     * 获取最新4条公告和未读公告数量
     * Create by zhangyangzhen
     * Date: 2018/9/6
     * Time: 15:01
     */
    private function _getUnreadNotice()
    {
        $aModel = $this->_getAnnounceModel();

        //未读公告id数组
        $unReadAids = $aModel->hasUnreadAids($this->_memberId);
        $count      = count($unReadAids);

        $month = date('Y-m-d H:i:s', strtotime("-1 month"));
        $map   = [
            'update_time' => ['egt', $month],
        ];

        //获取最新的4条公告
        $newAnnounce = $aModel->getSysNotice(0, 1, 4, 0, 'id, title', 'update_time desc', $map);

        $data = [
            'newNotice'   => $newAnnounce ?: [],
            'unReadCount' => $count ?: 0,
        ];

        return $data;
    }

    /**
     * 根据集团账号ID获取集团成员
     * Create by zhangyangzhen
     * Date: 2018/9/7
     * Time: 10:18
     *
     * @param $parentId
     *
     * @return array
     */
    private function _getGroupMemberList($parentId)
    {
        if (!$parentId) {
            return [];
        }

        $relationModel = $this->_getMemberRelationshipModel();
        $tmpList       = $relationModel->getMemIdByGroupId($parentId);

        $resList = [];
        if ($tmpList) {
            $resList = array_column($tmpList, 'son_id');
            $resList = array_unique($resList);
        }

        return $resList;
    }

    /**
     * 获取 openid 和 access_token
     * Create by zhangyangzhen
     * Date: 2018/10/30
     * Time: 17:47
     * @throws \Exception
     */
    public function getOpenidAndAccessToken()
    {
        $code = I('post.code', '');

        if (!$code || empty($code)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], 'code不能为空');
        }

        $wechat = new WechatSmallApp();
        $res    = $wechat->getWxSession($code, 123626);

        if ($res != false) {
            $this->apiReturn(self::CODE_SUCCESS, $res, 'success');
        } else {
            $this->apiReturn(self::CODE_NO_CONTENT, [], '获取openid失败');
        }
    }

    /**
     * 获取Model\Notice\Announce模型
     * Create by zhangyangzhen
     * Date: 2018/9/6
     * Time: 11:11
     * @return Announce
     */
    private function _getAnnounceModel()
    {
        if (empty($this->_announceModel)) {
            $this->_announceModel = new Announce();
        }

        return $this->_announceModel;
    }

    /**
     * 获取Model\Member\Member模型
     * Create by zhangyangzhen
     * Date: 2018/9/7
     * Time: 9:42
     * @return Member
     */
    private function _getMemberModel()
    {
        if (empty($this->_memberModel)) {
            $this->_memberModel = new Member();
        }

        return $this->_memberModel;
    }

    /**
     * 获取Model\Report\Statistics模型
     * Create by zhangyangzhen
     * Date: 2018/9/7
     * Time: 10:22
     * @return Statistics
     */
    private function _getStatisticsModel()
    {
        if (empty($this->_statisticsModel)) {
            $this->_statisticsModel = new Statistics();
        }

        return $this->_statisticsModel;
    }

    /**
     * 获取 Business/Finance/Withdraw 提现模型
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 15:40
     * @return Withdraw
     */
    private function _getWithdrawModel()
    {
        if (empty($this->_withdrawModel)) {
            $this->_withdrawModel = new Withdraw();
        }

        return $this->_withdrawModel;
    }

    /**
     * 获取 Business\Finance\AccountMoney 账户资金模型
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 16:00
     * @return AccountMoney
     */
    private function _getAccountMoneyModel()
    {
        if (empty($this->_accountMoneyModel)) {
            $this->_accountMoneyModel = new AccountMoney();
        }

        return $this->_accountMoneyModel;
    }

    /**
     * 获取 Model/Finance/Banks 银行相关模型
     * Create by zhangyangzhen
     * Date: 2018/10/19
     * Time: 16:51
     * @return Banks
     */
    private function _getBanksModel()
    {
        if (empty($this->_banksModel)) {
            $this->_banksModel = new Banks();
        }

        return $this->_banksModel;
    }

    /**
     * 获取Model\Member\MemberRelationship模型
     * Create by zhangyangzhen
     * Date: 2018/9/7
     * Time: 10:25
     * @return MemberRelationship
     */
    private function _getMemberRelationshipModel($sid = 0)
    {
        if (empty($this->_memberRelationshipModel)) {
            $this->_memberRelationshipModel = new MemberRelationship($sid);
        }

        return $this->_memberRelationshipModel;
    }

    /**
     * 获取 Business/Order/Query 订单查询模型
     * Create by zhangyangzhen
     * Date: 2018/11/3
     * Time: 16:06
     * @return Query
     */
    private function _getBusinessOrderQueryModel()
    {
        if (empty($this->_businessOrderQueryModel)) {
            $this->_businessOrderQueryModel = new Query();
        }

        return $this->_businessOrderQueryModel;
    }

    /**
     * 获取 Business/Order/OrderList 订单业务逻辑模型
     * Create by zhangyangzhen
     * Date: 2018/11/20
     * Time: 16:32
     * @return OrderList
     */
    private function _getOrderBusiness()
    {
        if (!isset($this->_orderBusiness)) {
            $this->_orderBusiness = new \Business\Order\OrderList($this->_selectType);
        }

        return $this->_orderBusiness;
    }

    /**
     * 获取 /Model/Order/OrderRefer 订单模型
     * Create by zhangyangzhen
     * Date: 2018/11/20
     * Time: 16:32
     * @return \Model\Order\OrderRefer
     */
    private function _getOrderModel()
    {
        if (!isset($this->_orderReferModel)) {
            $this->_orderReferModel = new \Model\Order\OrderRefer($this->_selectType);
        }

        return $this->_orderReferModel;
    }

    /**
     * 获取 Business/Member/MemberRelation 模型
     * Create by zhangyangzhen
     * Date: 2018/12/12
     * Time: 13:52
     * @return MemberRelation
     */
    private function _getBusMemberRelation()
    {
        if (empty($this->_busMemberRelation)) {
            $this->_busMemberRelation = new MemberRelation();
        }

        return $this->_busMemberRelation;
    }

    /**
     * 创建人脸小程序码
     * @author: hanwenlin
     * @date: 2019/4/14
     */
    public function createMiniAppCode()
    {
        $params  = I('post.');
        $faceBiz = new \Business\Face\FaceVip();
        $result  = $faceBiz->createMiniAppCode($params, $this->_sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 手工授权
     *
     * @author: hanwenlin
     * @date: 2019/4/14
     */
    public function deviceAuth()
    {
        $vId = I('post.vId') + 0;
        if (!$vId) {
            parent::apiReturn(201, [], '参数错误');
        }
        $faceBiz = new \Business\Face\FaceVip();
        $result  = $faceBiz->deviceAuth($vId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取人员列表
     *
     * @author: hanwenlin
     * @date: 2019/4/14
     */
    public function getFaceList()
    {
        $params  = I('post.');
        $faceBiz = new \Business\Face\FaceVip();
        $result  = $faceBiz->getFaceList($params, $this->_sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 通过小程序扫码进行vip接待人员注册
     *
     * @author: hanwenlin
     * @date: 2019/4/15
     */
    public function registerVipFromMiniCode()
    {
        $params = I('post.');
        $codeId = $params['code_id'] + 0;
        if (!$codeId) {
            parent::apiReturn(201, [], '小程序码ID错误');
        }
        if (!Helpers::isMobile($params['mobile'])) {
            parent::apiReturn(201, [], '手机号格式错误');
        }
        if (empty($params['name'])) {
            parent::apiReturn(201, [], '姓名不能为空');
        }
        if (empty($params['photo_base64'])) {
            parent::apiReturn(201, [], '请上传照片');
        }

        $faceBiz = new \Business\Face\FaceVip();
        $result  = $faceBiz->registerVipFromMiniCode($params, $codeId, $this->_memberId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取照片信息
     *
     * @author: hanwenlin
     * @date: 2019/4/15
     */
    public function getVipRegisterInfo()
    {
        $codeId = I('post.code_id');
        $mobile = I('post.mobile');
        if (empty($codeId) || empty($mobile)) {
            parent::apiReturn(203, [], '参数错误');
        }
        $faceBiz = new \Business\Face\FaceVip();
        $result  = $faceBiz->getVipRegisterInfo($codeId, $mobile);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * vip接待人员照片更新
     *
     * @author: hanwenlin
     * @date: 2019/4/15
     */
    public function updateVipFace()
    {
        $codeId    = I('post.code_id');
        $id        = I('post.id');
        $mobile    = I('post.mobile');
        $base64Img = I('post.photo_base64');

        if (empty($id) || empty($base64Img) || empty($codeId)) {
            parent::apiReturn(203, [], '参数错误');
        }

        $faceBiz = new \Business\Face\FaceVip();
        $result  = $faceBiz->updateVipFace($codeId, $id, $mobile, $base64Img, $this->_memberId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 发送短信修改企业名称或者绑定手机号
     *
     * @author: linchen
     * @date: 2019/11/07
     */
    public function sendVcodeByComNameOrMobile()
    {
        $mobile    = I('mobile', 0, 'intval');
        $type      = I('type', 0);  //0修改或者绑定手机号 1 修改企业名称校验
        $memberMdl = new Member();
        $info      = $memberMdl->getMemberInfo($this->_memberId, 'id');
        if ($mobile) {
            if ($type == 1) {
                $tpl = 'update_com_name';
            } else {
                $tpl = 'update_new_mobile';
            }
        } else {
            if ($type == 1) {
                $this->apiReturn(406, [], '请填写手机号！');
            }
            $mobile = $info['mobile'];
            $tpl    = 'check_old_mobile';
        }
        // 是否黑名单号码
        $black_list    = load_config('black_list');
        $balack_mobile = $black_list['mobile'];
        if (in_array($mobile, $balack_mobile)) {
            $this->apiReturn(406, [], '该手机号已经被加入黑名单。');
        }
        if ($type == 1) {
            $data = [
                '{1}'  => '企业名称修改',
                'code' => '{2}',
            ];
        } else {
            $data = [
                '{1}'  => '手机号修改',
                'code' => '{2}',
            ];
        }
        $res = Vcode::sendVcode($mobile, $tpl, $tpl, $data);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '发送验证码成功！');
        } else {
            $this->apiReturn(406, [], $res['msg']);
        }
    }

    /**
     * 校验手机验证码
     * <AUTHOR>
     * @date   2019-11-10
     *
     * @return mixed    默认ajax|successType='bool'则验证成功返回true
     */
    public function checkVcode($successType = 'ajax')
    {

        $tpl    = I('post.tpl');
        $code   = I('post.code');
        $mobile = I('post.mobile', 0, 'intval');

        if (!$tpl || !$code || !$mobile) {
            $this->apiReturn(406, [], '参数错误');
        }

        $res = Vcode::verifyVcode($mobile, $code, $tpl);
        if ($res['code'] == 200) {
            if ($successType == 'bool') {
                return true;
            } else {
                $this->apiReturn(200, [], '验证码正确');
            }
        } else {
            if ($successType == 'bool') {
                return false;
            } else {
                $this->apiReturn(406, [], '验证码输入错误,请重新输入');
            }
        }
    }

    /**
     * 校验企业名称并修改
     * <AUTHOR>
     * @date   2019-11-04
     *
     * @param  string mobile 要验证的手机号
     *
     * @return array
     */
    public function changeComName()
    {
        $comName = I('post.com_name', '');
        $mobile  = I('post.mobile', 0);
        if (!$comName) {
            $this->apiReturn(406, [], '企业名称不能为空');
        }
        if ($this->checkVcode('bool')) {
            $data       = [
                'com_name' => safetxt($comName),
            ];
            $memberBiz  = new \Business\Member\Member();
            $memberInfo = $memberBiz->getInfo($this->_memberId);
            if ($memberInfo['mobile'] != $mobile) {
                $this->apiReturn(201, [], '和预留手机号不一致');
            }
            $checkEditComName = $memberBiz->checkUserChangeComName($this->_memberId, safetxt($comName));
            if ($checkEditComName['code'] != 200) {
                $this->apiReturn(201, [], $checkEditComName['msg']);
            }
            $extRes = $memberBiz->updateMemberExtInfo($this->_memberId, $data);
            if (!$extRes) {
                $this->apiReturn(201, [], '更新企业名称失败');
            }
            // 更新电子发票信息
            $invoiceBiz = new InvoiceApi();
            $oldConfig  = $invoiceBiz->getSpecialConfig($this->_memberId);
            if (isset($oldConfig['id'])) {
                $oldConfig['compayName'] = $comName;
                $invoiceBiz->updateSpecialInvoice($oldConfig);
            }
            $this->apiReturn(200, [], '修改成功');
        } else {
            $this->apiReturn(406, [], '验证码校验失败');
        }
    }

    /**
     * 修改手机号
     * <AUTHOR>
     * @date   2019-11-04
     *
     * @param  string mobile 要验证的手机号
     *
     * @return array
     */
    public function changeMobile()
    {
        $mobile = I('post.mobile', 0, 'intval');
        if (!Tools::ismobile($mobile)) {
            $this->apiReturn(406, [], '电话号码有误');
        }
        if ($this->checkVcode('bool')) {
            $customerBiz     = new Customer();
            $customerInfo    = $customerBiz->getCustomerInfoByMemberId($this->_memberId);
            $memberBiz       = new \Business\Member\Member();
            $checkMemberInfo = $memberBiz->parseCustomerIdByMobile($mobile);
            if ($checkMemberInfo && $customerInfo['customer_id'] != $checkMemberInfo) {
                $this->apiReturn(406, [], '电话被占用');
            }
            $data      = ['mobile' => $mobile];
            $memberMdl = new Member();
            $res       = $memberMdl->setMemberInfoById($this->_memberId, $data);
            if (!$res) {
                $this->apiReturn(406, [], '更新失败');
            }
            $this->apiReturn(200, [], '更新成功');
        } else {
            $this->apiReturn(406, [], '验证码校验失败');
        }
    }

    /***************生产人员终端设备功能模块*****************/

    /**
     * 绑定特征码跟序列码
     * @Author: zhujb
     * 2019/10/15
     */
    public function bindDeviceKeyBySequence()
    {
        $deviceKey    = I('post.device_key', '', 'strval,trim');
        $sequenceCode = I('post.sequence_code', '', 'strval,trim');
        $typeId       = I('post.type_id', 0, 'intval');
        $productId    = I('post.product_id', 0, 'intval');
        $opId         = I('post.op_id', 0, 'intval');
        $bindType     = I('post.bind_type', 0, 'intval');

        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '请填入特征码');
        }

        if (empty($sequenceCode)) {
            $this->apiReturn(204, [], '请填入序列码');
        }

        if (empty($typeId)) {
            $this->apiReturn(204, [], '请选择类型');
        }

        if (empty($productId)) {
            $this->apiReturn(204, [], '请选择产品');
        }

        $paramArr = [
            'device_key'    => $deviceKey,
            'sequence_code' => $sequenceCode,
            'type_id'       => $typeId,
            'product_id'    => $productId,
            'op_id'         => $opId,
            'bind_type'     => $bindType,
        ];

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/bindDeviceKeyBySequence', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 获取所有类型
     * @Author: zhujb
     * 2019/10/17
     */
    public function getAllDeviceType()
    {
        $paramArr  = [];
        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/getAllDeviceType', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 根据类型获取产品
     * @Author: zhujb
     * 2019/10/17
     */
    public function getProductsByType()
    {
        $typeId   = I('post.type_id', 0, 'intval');
        $paramArr = [
            'type_id' => $typeId,
        ];

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/getProductsByType', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 获取序列号详情
     * @Author: zhujb
     * 2019/10/17
     */
    public function getSequenceInfo()
    {
        $sequenceCode = I('post.sequence_code', '', 'strval,trim');
        if (empty($sequenceCode)) {
            $this->apiReturn(204, [], '请填入序列码');
        }

        $paramArr = [
            'id'            => 0,
            'sequence_code' => $sequenceCode,
        ];

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/getOneDeviceSequence', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 查询设备列表
     * @Author: zhujb
     * 2019/10/17
     */
    public function getDeviceList()
    {
        $page             = I('post.page', 1, 'intval');
        $pageSize         = I('post.page_size', 10, 'intval');
        $typeId           = I('post.type_id', 0, 'intval');
        $sortType         = I('post.sort_type', 0, 'intval');
        $keyword          = I('post.keyword', '', 'strval,trim');
        $sid              = I('post.sid', 0, 'intval');
        $implementationId = I('post.implementation_id', 0, 'intval');

        if (empty($sortType)) {
            $order = 'status asc,create_time desc';
        } else if ($sortType == 1) {
            $order = 'status asc,create_time asc';
        } else {
            $order = 'status asc,id desc';
        }

        $paramArr = [
            'page'              => $page,
            'page_size'         => $pageSize,
            'order'             => $order,
            'sid'               => $sid,
            'device_key'        => '',
            'type_id'           => $typeId,
            'systemVersionNo'   => '',
            'authStatus'        => '',
            'deviceStatus'      => 0,
            'deviceName'        => '',
            'remark'            => '',
            'status'            => 0,
            'originType'        => 0,
            'packageId'         => 0,
            'createBeginTime'   => '',
            'createEndTime'     => '',
            'authBeginTime'     => '',
            'authEndTime'       => '',
            'versionNo'         => '',
            'adcode'            => 0,
            'sequenceCode'      => '',
            'keyword'           => $keyword,
            'implementation_id' => $implementationId,
        ];

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/getDeviceManageList', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }

        $newList = [];
        foreach ($res['res']['data']['list'] as $item) {
            $versionNo  = $item['deviceInfo']['version_no'];
            $deviceType = $item['deviceInfo']['device_type'];

            $newList[] = [
                'id'                => $item['deviceInfo']['id'],
                'device_key'        => $item['deviceInfo']['device_key'],
                'device_name'       => $item['deviceInfo']['device_name'],
                'system_version_no' => $item['deviceInfo']['system_version_no'],
                'version_no'        => $versionNo,
                'status'            => $item['deviceInfo']['status'],
                'device_type'       => $deviceType,
                'device_type_text'  => $item['deviceInfo']['device_type_text'],
                'status_text'       => $item['deviceInfo']['status_text'],
                'hasNew'            => $item['deviceInfo']['has_new'],
            ];
        }
        $res['res']['data']['list'] = $newList;
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 获取设备详细
     * @Author: zhujb
     * 2019/10/17
     */
    public function getDeviceDetail()
    {
        $id        = I('post.id', 0, 'intval');
        $deviceKey = I('post.device_key', '', 'strval,trim');
        if (empty($id) && empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'device_key'  => $deviceKey,
            'id'          => $id,
            'origin_type' => 'web',
        ];

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/getGatheringDeviceInfo', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }

        $data    = $res['res']['data'];
        $newData = [
            'id'                => $data['device_info']['id'],
            'device_key'        => $data['device_info']['device_key'],
            'device_name'       => $data['device_info']['device_name'],
            'sequence_code'     => $data['device_info']['sequence_code'],
            'device_type'       => $data['device_info']['device_type'],
            'product_id'        => $data['device_info']['product_id'],
            'system_version_no' => $data['device_info']['system_version_no'],
            'create_time'       => $data['device_info']['create_time'],
            'version_no'        => $data['device_info']['version_no'],
            'status'            => $data['device_info']['status'],
            'address'           => $data['device_address_info']['address'],
            'device_type_text'  => $data['device_info']['device_type_text'],
            'status_text'       => $data['device_info']['status_text'],

            'product_name' => $data['device_product_info']['product_name'],
            'pro_model_no' => $data['device_product_info']['pro_model_no'],

            'update_time' => $data['sequence_info']['update_time'],

            'in_num'    => $data['device_business_info']['in_num'],
            'check_num' => $data['device_business_info']['check_num'],
            'buy_num'   => $data['device_business_info']['buy_num'],
            'get_num'   => $data['device_business_info']['get_num'],

            'setting' => $data['device_setting_info'],

            'origin_type_text' => $data['device_user_info']['origin_type_text'],
            // 是否有新版本
            'hasNew'           => $data['device_info']['has_new'],
        ];

        $this->apiReturn($res['res']['code'], $newData, $res['res']['msg'], true);
    }

    /**
     * 解绑序列号，特征码
     * @Author: zhujb
     * 2019/11/14
     */
    public function unBindSequenceOnDevice()
    {
        $deviceKey = I('post.device_key', '', 'strval,trim');
        $opId      = I('post.op_id', 0, 'intval');

        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '特征码必填');
        }

        $paramArr = [
            'device_key' => $deviceKey,
            'op_id'      => $opId,
        ];

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/unBindSequenceOnDevice', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /*************************终端管理模块***********************************/

    /**
     * 添加终端
     * @Author: zhujb
     * 2019/11/6
     */
    public function addDeviceByMember()
    {
        $scanString = I('post.scan_string', '', 'strval,trim');
        $sid        = I('post.sid', 0, 'intval');
        $deviceName = I('post.device_name', '', 'strval,trim');

        if (empty($sid)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'scan_string' => $scanString,
            'sid'         => $sid,
            'device_name' => $deviceName,
        ];

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/addDeviceByMember', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 获取设备最近版本
     * @Author: zhujb
     * 2019/11/7
     */
    public function getDeviceNewVersion()
    {
        $page     = 1;
        $pageSize = 4;
        $typeId   = I('post.type_id', 0, 'intval');

        if (empty($typeId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'page'      => $page,
            'page_size' => $pageSize,
            'type_id'   => $typeId,
        );

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/getRecentVersionList', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 推送升级
     * @Author: zhujb
     * 2019/11/7
     */
    public function updateVersion()
    {
        $updateDescription = I('update_description', '', 'strval');
        $appUrl            = I('app_url', '', 'strval');
        $versionCode       = I('version_code', '', 'strval');
        $versionManagerNo  = I('version_manager_no', '', 'strval');
        $deviceKey         = I('device_key', '', 'strval');
        $opId              = I('op_id', 0, 'intval');

        if (empty($appUrl)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'app_url'            => $appUrl,
            'device_key'         => $deviceKey,
            'op_id'              => $opId,
            'upgrade_type'       => 1,
            'version_code'       => $versionCode,
            'version_manager_no' => $versionManagerNo,
            'update_description' => $updateDescription,
        );

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/upgradeDevice', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 获取设备升级进度
     * @Author: zhujb
     * 2019/11/11
     */
    public function getDeviceUpgradeProcess()
    {
        $deviceKey = I('post.device_key', '', 'strval,trim');

        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'device_key' => $deviceKey,
        );

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/getNewUpgradeByDevice', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错');
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg']);
    }

    /**
     * 修改设备名称
     * @Author: zhujb
     * 2019/11/11
     */
    public function changeDeviceName()
    {
        $deviceKey  = I('post.device_key', '', 'strval,trim');
        $deviceName = I('post.device_name', '', 'strval,trim');

        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'device_key'  => $deviceKey,
            'device_name' => $deviceName,
        );

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/saveDeviceName', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 保存设备配置
     * @Author: zhujb
     * 2019/11/11
     */
    public function saveDeviceSetting()
    {
        $deviceKey = I('post.device_key', '', 'strval,trim');
        $setting   = I('post.setting', '', 'strval,trim');
        $opId      = I('post.op_id', 0, 'intval');

        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $ip       = get_client_ip();
        $paramArr = [
            'device_key' => $deviceKey,
            'op_id'      => $opId,
            'setting'    => $setting,
            'origin'     => 'MicroPlat',
            'ip'         => ip2long($ip),
        ];

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/saveGateDeviceSetting', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 客工服务台获取闸机出入园人数
     * @Author: zhujb
     * 2020/2/20
     */
    public function getGateDeviceAccessNum()
    {
        $salerId  = I('post.saler_id', '', 'strval,trim');
        $begin    = I('post.begin_time', date('Y-m-d 00:00:00'), 'strval');
        $end      = I('post.end_time', date('Y-m-d 23:59:59'), 'strval');
        $gateType = I('post.gate_type', 0, 'intval');

        if (!$salerId || empty($salerId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数salerid不能为空');
        }

        if ((strtotime($end) - strtotime($begin)) > 31 * 24 * 3600) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '查询时间跨度不能超过31天');
        }

        if (time() - strtotime($begin) > 3600 * 24 * 180) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '目前仅支持查询半年的出入园数据');
        }

        //$landModel = new Land('slave');
        //$landInfo  = $landModel->getLandInfoBySalerId($salerId, false, 'id,apply_did');

        $landApi  = new \Business\CommodityCenter\Land();
        $landInfo = $landApi->queryLandMultiQueryBySalerid([$salerId])[0];

        $landId    = $landInfo['id'];
        $dataModel = new DataCollection();
        //获取入园数据
        $inSummary = $dataModel->getEnterData($begin, $end, $landId, 'sum(leave_num) as cnt', $gateType);
        if (!empty($inSummary) && is_array($inSummary) && !empty($inSummary[0]['cnt'])) {
            $inCount = $inSummary[0]['cnt'];
        } else {
            $inCount = 0;
        }

        //获取出园数据
        $outCount = 0;
        if ($inCount > 0) {
            $outSummary = $dataModel->getLeaveData($begin, $end, $landId, 'sum(leave_num) as cnt', $gateType);
            $outCount   = isset($outSummary[0]['cnt']) ? $outSummary[0]['cnt'] : 0;
        }

        $data = [
            'dayIn'  => $inCount,
            'dayOut' => $outCount,
            'left'   => $inCount - $outCount,
        ];

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 添加闸机二维码
     * @Author: zhujb
     * 2019/3/28
     *
     * @param  string    qrcode_name   二维码名称
     * @param  int       qrcode_type   1：签到码，2：购票码，3：扩展板波特率设置，4：机芯设置，5：闸机界面信息购票码
     * @param  string    qrcode_data   二维码数据
     * @param  string    remark        备注
     */
    public function addGateCode()
    {
        $qrcodeName = I('post.qrcode_name', '', 'strval');
        $qrcodeType = I('post.qrcode_type', 0, 'intval');
        $qrcodeData = I('post.qrcode_data', '', 'strval');
        $remark     = I('post.remark', '', 'strval');

        if (empty($qrcodeName) || empty($qrcodeType) || empty($qrcodeData)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'qrcode_name' => $qrcodeName,
            'qrcode_type' => $qrcodeType,
            'qrcode_data' => $qrcodeData,
            'remark'      => $remark,
        ];

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/addGateCode', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }

        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 编辑闸机二维码
     * @Author: zhujb
     * 2019/3/28
     *
     * @param  int     code_id        二维码id
     * @param  string  qrcode_name    二维码名称
     * @param  string  qrcode_data    二维码数据
     * @param  string  remark         备注
     */
    public function editGateCode()
    {
        $codeId     = I('post.code_id', 0, 'intval');
        $qrcodeName = I('post.qrcode_name', '', 'strval');
        $qrcodeData = I('post.qrcode_data', '', 'strval');
        $remark     = I('post.remark', '', 'strval');

        if (empty($qrcodeName) || empty($codeId) || empty($qrcodeData)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'code_id'     => $codeId,
            'qrcode_name' => $qrcodeName,
            'qrcode_data' => $qrcodeData,
            'remark'      => $remark,
        ];

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/editGateCode', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 获取闸机二维码列表
     * @Author: zhujb
     * 2019/3/28
     *
     * @param  string     qrcode_name    二维码名称
     * @param  string     remark         备注
     * @param  int        qrcode_type    二维码类型 1：签到码，2：购票码，3：扩展板波特率设置，4：机芯设置，5：闸机界面信息购票码
     * @param  string     begin_time     开始时间
     * @param  string     end_time       结束时间
     * @param  int        page           页码
     * @param  int        page_size      每页条数
     */
    public function getGateCodeList()
    {
        $qrcodeName = I('post.qrcode_name', '', 'strval');
        $page       = I('post.page', 1, 'intval');
        $qrcodeType = I('post.qrcode_type', 0, 'intval');
        $pageSize   = I('post.page_size', 10, 'intval');

        $paramArr = [
            'qrcode_name' => $qrcodeName,
            'remark'      => '',
            'qrcode_type' => $qrcodeType,
            'begin_time'  => '',
            'end_time'    => '',
            'page'        => $page,
            'page_size'   => $pageSize,
        ];

        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/getGateCodeList', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }

        $list = [];
        if (!empty($res['res']['data']['list'])) {
            $list = $this->_handleList($res['res']['data']['list']);
        }

        $res['res']['data']['list'] = $list;

        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 特殊处理列表字段
     * @Author: zhujb
     * 2020/3/11
     *
     * @param $list
     *
     * @return array
     */
    public function _handleList($list)
    {
        $ticketModel = new Ticket('slave');
        $memberModel = new Member('slave');

        $res = [];
        foreach ($list as $item) {
            $lid     = 0;
            $account = '';
            if ($item['qrcode_type'] == 2) {
                // 解析购票码
                $temp       = explode(',', $item['qrcode_data']);
                $tid        = $temp[1];
                $ticketInfo = $ticketModel->getTicketInfoById($tid, 'landid');
                $lid        = $ticketInfo['landid'];
            } elseif ($item['qrcode_type'] == 1) {
                // 解析签到码
                $temp       = explode(',', $item['qrcode_data']);
                $terminal   = $temp[2];

                $terminalApi = new \Business\CommodityCenter\TerminalLand();
                $landInfoRes = $terminalApi->queryLandMultiQueryByTerminal([$terminal]);
                $landInfo    = [];
                if ($landInfoRes) {
                    $landInfo = $landInfoRes[0];
                }

                $aid        = $landInfo['apply_did'];
                $memberInfo = $memberModel->getMemberByMixed('id', $aid, 'account');
                $account    = $memberInfo[0] ?: '';
            }

            $item['lid']     = $lid;
            $item['account'] = $account;
            $res[]           = $item;
        }

        return $res;
    }

    /**
     * 模糊搜索景区
     * @Author: zhujb
     * 2020/3/10
     */
    public function getLandListByKeyword()
    {
        $keyword = I('post.keyword', '', 'strval,trim');
        $account = I('post.account', '', 'strval,trim');
        if (empty($keyword) || empty($account)) {
            $this->apiReturn(200, [], '');
        }

        $sid = 0;
        if ($account) {
            $memberBiz  = new \Business\Member\Member();
            $memberInfo = $memberBiz->getInfoByAccount($account);

            $sid = $memberInfo ? $memberInfo['id'] : 0;
        }

        if (empty($sid)) {
            $this->apiReturn(204, [], '供应商信息获取错误');
        }

        $productBiz = new \Business\Product\Product();
        $landList   = $productBiz->getSaleProductByCondition($sid, 0, 0, 0, $keyword);
        $lidArr     = array_column($landList, 'lid');
        //$landModel  = new Land('slave');
        //$res        = $landModel->getLandInfoByMuli($lidArr, 'id,terminal,title');

        $javaAPi = new \Business\CommodityCenter\Land();
        $res     = $javaAPi->queryLandMultiQueryById($lidArr);

        $this->apiReturn(200, $res, '');
    }

    /**
     * 根据lid 获取门票信息
     * @Author: zhujb
     * 2020/3/10
     */
    public function getTicketListByLid()
    {
        $lid = I('post.lid', 0, 'intval');
        if (empty($lid)) {
            $this->apiReturn(204, [], '没有门票信息');
        }

        //获取门票数据
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable('id,title,pid',
            '', '', '', false,
            null, [$lid]);
        $res       = array_column($ticketArr, 'ticket');

        $this->apiReturn(200, $res, '');
    }

    /**
     * 获取供应商列表
     * @Author: zhujb
     * 2019/1/17
     */
    public function getMemberList()
    {
        $keyword     = I('keyword', '', 'strval');
        $page        = I('page', 1, 'intval');
        $size        = I('size', 10, 'intval');
        $memberModel = new MemberQuery();

        $res = $memberModel->queryMemberPageByAccountOrDname($keyword, $page, $size);

        if (!isset($res['code']) || $res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        } else {
            $this->apiReturn(200, $res['data'], 'success');
        }
    }

    /**
     * 获取期票可最早预约日期
     *
     * @return array
     * @throws \Exception
     * <AUTHOR>
     * @date 2020/6/17
     *
     */
    public function getRecentOrderDate()
    {
        $orderNum = I('order_num', '', 'strval');
        if (empty($orderNum)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        $mallOrderBz = new ReservationOrder();
        $res         = $mallOrderBz->getEarlyCanReservationDateService($orderNum);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取期票预约库存数据
     *
     * @return array
     * <AUTHOR>
     * @date 2020/6/11
     *
     *
     */
    public function getOrderReservationInventory()
    {
        $orderNum = I('order_num', '', 'strval');
        $date     = I('start_date', '', 'strval');
        $end      = I('end_date', '', 'strval');
        if (empty($orderNum)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        if (empty($date)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '预定日期未选择');
        }
        $mallOrderBz = new ProductStorage();
        $res         = $mallOrderBz->getOrderStorageByReservationOrNormalService($orderNum, $date, $end);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 微平台期票订单预约
     * <AUTHOR>
     * @date   2020/06/18
     */
    public function orderReservation()
    {
        $orderNum       = I('order_num', '', 'strval');         //订单号
        $sectionTimeStr = I('section_time_str', '', 'strval');  //分时预约时间段
        $playTime       = I('play_time', '', 'strval');         //预约游玩时间
        $sectionTimeId  = I('section_time_id', 0, 'intval');   //分时预约时间段Id
        if (!$orderNum) {
            $this->apiReturn(204, [], '订单号不能为空');
        }
        if (!strtotime($playTime)) {
            $this->apiReturn(204, [], '预约时间错误');
        }
        $modifyBiz = new ReservationOrder();
        $result    = $modifyBiz->orderReservationService($orderNum, $this->_sid, $this->_sid, $playTime, 22,
            $sectionTimeId, $sectionTimeStr);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }

    /**
     * 期票订单改签
     *
     * @return array
     * <AUTHOR>
     * @date 2020/6/22
     *
     */
    public function OrderBookChange()
    {
        $orderNum       = I('order_num', '', 'strval');         //订单号
        $sectionTimeStr = I('section_time_str', '', 'strval');  //分时预约时间段
        $playTime       = I('play_time', '', 'strval');         //预约游玩时间
        $sectionTimeId  = I('section_time_id', 0, 'intval');   //分时预约时间段Id
        if (!$orderNum) {
            $this->apiReturn(204, [], '订单号不能为空');
        }
        if (!strtotime($playTime)) {
            $this->apiReturn(204, [], '预约时间错误');
        }
        $modifyBz = new Modify();
        $result   = $modifyBz->ticketChanging($orderNum, $playTime, $this->_sid, $this->_sid, 22, $sectionTimeId,
            $sectionTimeStr);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 平台小程序更新分销商接口
     * <AUTHOR>
     * @date 2020/8/11
     *
     */
    public function updateRemark()
    {
        $sid       = $this->_sid; //上级id
        $memberSid = $this->_memberId; //当前用户id
        $memberId  = I('post.member_id', 0); //需要更改备注的分销商id
        $remark    = I('post.remark', ' ', 'strval,trim'); //备注

        //长度不超100
        if (mb_strlen($remark) > 100) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '备注字数超出限制');
        }

        $memberbus = new MemberRelationshipRemark();
        $res       = $memberbus->addDistributorRemarkName($memberId, $sid, $memberSid, $remark);
        if ($res['code'] != 200) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], $res['msg']);
        } else {
            $this->apiReturn(self::CODE_SUCCESS, [], $res['msg']);
        }
    }

    //===================新版添加分销商================

    /**
     * 获取分销商
     * <AUTHOR>  Li
     * @date  2020-08-07
     *
     * @return array
     */
    public function getDistributorInfoByPhoneOrAccount()
    {
        $searchInfo = I('post.key_word', '');

        if (empty($searchInfo)) {
            $this->apiReturn(204, [], '请输入关键字');
        }
        $memberRelationBiz = new MemberRelationBiz();
        $result            = $memberRelationBiz->getDistributorInfoByPhoneOrAccount($this->_sid, $searchInfo);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 添加分销商
     * <AUTHOR>  Li
     * @date  2020-08-07
     *
     * @return array
     */
    public function addDistributorMemberRelation()
    {
        $sonId   = I('post.son_id', 0);
        $groupId = I('post.group_id', 0);
        $notice  = I('post.notice', 1);   // 1短信 2微信
        $remark  = I('post.remark', '');   // 备注信息

        if (!is_numeric($sonId) || $sonId <= 0) {
            $this->apiReturn(204, [], '参数有误');
        }

        $memberRelationBiz = new MemberRelationBiz();
        $result            = $memberRelationBiz->addDistributor($this->_sid, $sonId, $this->_memberId,
            $groupId, true, $notice, $remark, '微平台小程序');
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 根据供应商id获取邀请码的状态,不做任何修改
     * <AUTHOR>  Li
     * @date  2020-07-20
     */
    public function getInviteCodeStateAndNotModify()
    {

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->getInviteCodeStateAndNotModify($this->_sid);

        if (!isset($result['code']) || $result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(200, $result['data'], '获取成功');
    }

    /**
     * 根据供应商id设置邀请码审核状态
     * <AUTHOR>  Li
     * @date  2020-07-20
     */
    public function setWithCheck()
    {
        //是否需要审核  0不需要 1需要
        $withCheck = I('post.with_check', 1, 'intval');
        $withCheck = $withCheck === 1 ? true : false;

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->setWithCheck($this->_sid, $withCheck);

        if (!isset($result['code']) || $result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 重新生成供应商账号对应的邀请码
     * <AUTHOR>  Li
     * @date  2020-07-20
     */
    public function regenerateInviteCode()
    {
        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->regenerateInviteCode($this->_sid);

        if (!isset($result['code']) || $result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(200, $result['data'], '获取成功');
    }

    /**
     * 创建账号添加分销商
     * <AUTHOR> Li
     * @date  2020-08-04
     */
    public function addAccountRegisterDistributor()
    {
        $dname   = I('post.dname', '', 'strval');
        $mobile  = I('post.mobile', '', 'strval');
        $groupId = I('post.group_id', 0, 'intval');

        if (empty($dname)) {
            $this->apiReturn(203, [], '请填写实名');
        }

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->addAccountRegisterDistributor($this->_sid, $dname, $mobile, $groupId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 邀请码添加分销商
     * <AUTHOR> Li
     * @date   2020-08-11
     *
     * @param  int $mobile 手机号
     * @param  string $vcode 短信验证码
     */
    public function applyCreateMemberRelationByInviteCode()
    {
        $sid        = I('post.sid', 0, 'intval');        //供应商id
        $fid        = I('post.son_id', 0, 'intval');     //分销商id
        $inviteCode = I('post.invite_code', '', 'trim'); //邀请码
        $remark     = I('post.remark', '', 'trim');
        if (mb_strlen($remark) > 50) {
            $this->apiReturn(203, [], '备注字符过长，不得超过50字符');
        }

        //需要审核发起邀请码审核 否则直接建立分销关系
        $memberRelationBiz = new \Business\Member\MemberRelation();
        $addRes            = $memberRelationBiz->addDistributorByInviteCode($sid, $fid, $fid, $inviteCode, 0, $remark);

        if ($addRes['code'] == 200) {
            $this->apiReturn(200, $addRes['data'], $addRes['msg']);
        } else {
            $this->apiReturn($addRes['code'], $addRes['data'], $addRes['msg']);
        }
    }

    /**
     * 成为我的供应商
     * <AUTHOR> Li
     * @date  2020-08-12
     *
     * @return array
     */
    public function becomeMySupplier()
    {
        $sid      = I('post.sid', 0, 'intval');
        $groupId  = I('post.group_id', 0, 'intval');
        $memberId = $this->_sid;

        if (!$sid || !$memberId) {
            $this->apiReturn(200, [], '缺少实名信息');
        }

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->becomeMySupplier($sid, $memberId, $groupId);

        $this->apiReturn(200, $result['data'], '添加成功');
    }

    /**
     * 添加平台未注册用户的分销商
     * <AUTHOR> Li
     * @date  2020-08-12
     *
     * @return array
     */
    public function addUnRegisterDistributor()
    {
        $mobile  = I('post.mobile', '', 'string');
        $groupId = I('post.group_id', 0, 'intval');
        if (!$mobile || !ismobile($mobile)) {
            $this->apiReturn(204, [], '电话号码错误');
        }
        if ($groupId < 0) {
            $this->apiReturn(204, [], '组id错误');
        }
        $memberRelationBiz = new MemberRelationBiz();
        $res               = $memberRelationBiz->addUnRegisterDistributor($mobile, $this->_loginInfo['sid'], $groupId,
            $this->_loginInfo['sdname'], $this->_loginInfo['id']);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 创建添加分销商小程序码
     * <AUTHOR> Li
     * @date  2020-08-14
     *
     * @return array
     */
    public function createMiniAppCodeForDistro()
    {
        $page = I('post.page');

        $lib      = new \Library\Business\WechatSmallApp();
        $scenCode = $lib->encodeShopCode($this->_sid);

        $time = time();
        // 生成小程序吗
        $scene  = $scenCode . '&' . $time;
        $res    = $lib->getWxCodeUnlimit(0, $scene, $page);
        $prefix = substr($res['data'], 0, 1);
        if ($prefix == '{') {
            $decodeRes = json_decode($res['data'], true);
            $this->apiReturn(401, $res, $decodeRes['errmsg']);
        }

        $fileName = "distro_" . $this->_sid . '_' . $time . ".png";
        $config   = load_config('qiniu', 'Qiniu');
        $qiniu    = new \Process\Resource\Qiniu($config);
        $fileUrl  = $qiniu->hasFileExist($fileName);
        if ($fileUrl != false) {
            $ret = ['url' => $fileUrl, 'param' => $scene];
            $this->apiReturn(200, $ret);
        }

        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();
        $upManager = new \Qiniu\Storage\UploadManager();
        $auth      = new \Qiniu\Auth($config['accessKey'], $config['secretKey']);
        $token     = $auth->uploadToken($config['images']['bucket'], null, 3600);
        list($ret, $error) = $upManager->put($token, $fileName, $res['data']);
        $ret['url'] = $config['images']['domain'] . $fileName;

        $this->apiReturn(200, $ret, '生成成功');
    }

    /**
     * 解析小程序码内容
     * <AUTHOR> Li
     * @date  2020-08-19
     */
    public function getMiniAppCodeInfo()
    {
        $scenCode      = I('post.scencode', '');
        $smallAppModel = new \Library\Business\WechatSmallApp();
        $aid           = $smallAppModel->decodeShopCode($scenCode);
        if (empty($aid)) {
            $this->apiReturn(204, [], 'scenCode解析错误');
        }

        $memberBiz  = new \Business\Member\Member();
        $memberInfo = $memberBiz->getInfo($aid);
        if (empty($memberInfo)) {
            $this->apiReturn(204, [], '客户信息异常');
        }

        $this->apiReturn(200, $memberInfo, '获取成功');
    }

    /**
     * 获取合作关系审核列表
     * <AUTHOR> Li
     * @date  2020-08-14
     */
    public function findMemberRelationshipApply()
    {
        $type       = I('post.type', 0, 'intval'); //搜索类型  1 企业名称 2 账号 3 手机号
        $identify   = I('post.identify', '', 'strval');
        $inviteType = I('post.invite_type', 0, 'intval'); //邀请类型;1 发送,2 接收
        $applyType  = I('post.apply_type', 0, 'intval'); //申请类型;1 查询添加,2 邀请添加,3 创建添加
        $client     = I('post.client', 0, 'intval'); //操作端: 1 微平台小程序,2 PC,3 微平台H5
        $stat       = I('post.status', 0, 'intval'); //审核状态: 1 待审核,2 已同意,3 已拒绝,-1 已失效
        $sid        = I('post.sid', 0, 'intval'); //搜索供应商id
        $fid        = I('post.fid', 0, 'intval'); //搜索分销商id
        $page       = I('post.page', 1, 'intval'); //当前页数
        $size       = I('post.size', 10, 'intval'); //每页条数

        $comName        = '';
        $cname          = '';
        $ctel           = '';
        $searchMemberId = 0;

        if ($identify) {
            $memberBiz = new \Business\Member\Member();
            switch ($type) {
                case 1:
                    //通过企业名称查询用户id
                    $comName = $identify;
                    break;
                case 2:
                    //通过企业账号查询用户id
                    $memberInfo = $memberBiz->getMemberInfoByMulti([$identify], 'account');
                    if (!$memberInfo) {
                        $this->apiReturn(204, [], '暂无数据');
                    }
                    $searchMemberId = $memberInfo[0]['id'];
                    break;
                case 3:
                    //通过手机号查询用户id
                    $memberInfo = $memberBiz->getMemberInfoByMulti([$identify], 'mobile');
                    if (!$memberInfo) {
                        $this->apiReturn(204, [], '暂无数据');
                    }
                    $searchMemberId = $memberInfo[0]['id'];
                    break;
            }
        }

        if ($client) {
            switch ($client) {
                case 1:
                    $client = '微平台小程序';
                    break;
                case 2:
                    $client = 'PC';
                    break;
                case 3:
                    $client = '微平台H5';
                    break;
            }
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->findMemberRelationshipApply($sid, $fid, $this->_sid, $comName, $cname, $ctel,
            $inviteType,
            $applyType, $client, $stat, 'id', $page, $size, true, $searchMemberId);

        if (isset($result['code']) && $result['code'] == 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(204, [], $result['msg']);
    }

    /**
     * 批量同意或拒绝 合作关系
     * <AUTHOR> Li
     * @date  2020-08-04
     */
    public function checkCreateMemberRelation()
    {
        $auditId = I('post.audit_id'); // 审核i
        $status  = I('post.status', 0, 'intval');   // 审核状态 1 拒绝 2通过

        if (empty($auditId) || empty($status)) {
            $this->apiReturn(400, [], '缺少审核信息');
        }

        if ($status == 2) {
            $pass = true;
        } else {
            $pass = false;
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->checkCreateMemberRelationBatch([$auditId], $this->_sid, $pass);

        if (isset($result['code']) && $result['code'] == 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], $result['msg']);
    }

    /**
     * 再次发送邀请
     * <AUTHOR> Li
     * @date   2020-08-14
     */
    public function reSendInviteSms()
    {
        $id = I('post.id');
        if (!$id) {
            $this->apiReturn(204, '', '参数错误');
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->reSendInviteSms($id, $this->_sid, $this->_loginInfo['sdname'],
            $this->_memberId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 通过id mobile 获取审核详情
     * <AUTHOR> Li
     * @date   2020-08-14
     *
     * @return array
     */
    public function getDistributionCheck()
    {
        $id = I('post.id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数有误');
        }

        $memberRelationBiz = new \Business\Member\DistributorAudit();
        $result            = $memberRelationBiz->getDistributionCheck($id, $this->_sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 失效/激活建立组织的供销关系申请
     * <AUTHOR> Li
     * @date  2020-08-04
     */
    public function invalidOrActiveApply()
    {
        $auditId = I('post.audit_id'); // 审核id
        $state   = I('post.status', 0, 'intval');   // 审核状态 1 失效 2 待审核

        if (!$auditId || !$state) {
            $this->apiReturn(400, '', '参数不能为空');
        }

        if (in_array($state, [1, 2])) {
            if ($state == 1) {
                $status = -1;
            } else {
                $status = 1;
            }
        } else {
            $this->apiReturn(400, [], '状态码错误');
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->invalidOrActiveApply($auditId, $status, $this->_memberId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], $result['msg']);
    }

    /**
     * 搜索列表
     *
     * @param  int $type 搜索类型 0 账号名称 1账号 2手机号 3商户ID 4 公司名称
     * @param  string $keyword 关键词
     *
     * @return  array
     */
    public function getSearch()
    {
        $type      = \safe_str(I('type', ''));
        $keyword   = \safe_str(I('keyword', ''));
        $checkType = array("0", "1", "2", "3", "4");
        if (!in_array($type, $checkType)) {
            $this->apiReturn(202, [], '参数有误请重新输入！');
        }
        $comName = '';
        $dname   = '';
        $fid     = 0;
        $mobile  = '';
        $account = '';
        if ($type == 0) {
            $dname = $keyword;
        } else if ($type == 1) {
            $account = $keyword;
        } else if ($type == 2) {
            $mobile = $keyword;
        } else if ($type == 3) {
            $fid = $keyword;
        } else if ($type == 4) {
            $comName = $keyword;
        }

        $memberQueryApi = new MemberQuery();
        $res            = $memberQueryApi->queryMemberIdBySearch($comName, $dname, $account, $fid, $mobile);
        if ($res['code'] != 200) {
            $this->apiReturn(204, [], '服务异常');
        }
        if ($res['data']) {
            //产品价格日志 角色：供应商 获取下级用户
            if (I('post.permissions', 0, 'intval')) {
                $info              = $this->getLoginInfo();
                $memberRelationBus = new MemberRelation();
                //获取keyword的供应商id
                $arr = $memberRelationBus->getMemberStaffInfoBySonIdToJava(array_column($res['data'], 'id')[0],
                    'parent_id');
                if ($arr['parent_id'] != $info['sid'] && $info['memberID'] != array_column($res['data'], 'id')[0]) {
                    $this->apiReturn(200, [], '无结果！');
                }
            }
            $this->apiReturn(200, [$res['data']], '成功！');
        } else {
            $this->apiReturn(200, [], '无结果！');
        }
    }
}
