<?php
/**
 * 营销员角色操作记录
 *
 * User: xujy
 * Date: 2020/3/16
 */

namespace Controller\Mall;

use Library\Controller;

class AllDisMembersLog extends Controller
{

    /**
     * @var $sid 供应商id
     */
    private $_sid = null;

    public function __construct()
    {
        $this->_sid = $this->isLogin('ajax');
    }

    /**
     *  获取营销员的相关操作记录
     * <AUTHOR>
     * @date 2020/3/13
     * @return array
     */
    public function getAllDisMembersLogList()
    {
        $page      = I('page', 1, 'intval');
        $pageSize  = I('pageSize', 20, 'intval');
        $roleId    = I('roleId', 0, 'intval');
        $allDisBiz = new \Business\Cooperator\AllDis\AllDisMembersLog();
        $config    = $allDisBiz->getAllDisMembersLogList($this->_sid, $roleId, $page, $pageSize);
        //$config    = $allDisBiz->getAllDisMembersList($this->_sid, '', '测试发展佣金');

        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }

    /**
     *  导入记录
     * <AUTHOR>
     * @date 2020/3/16
     * @return array
     */
    public function getAllDisMembersAddLogList()
    {
        $page      = I('page', 1, 'intval');
        $pageSize  = I('pageSize', 20, 'intval');
        $opStatus  = I('opStatus', '', 'strval');
        $allDisBiz = new \Business\Cooperator\AllDis\AllDisMembersLog();
        $config    = $allDisBiz->getAllDisMembersAddLogList($this->_sid, $opStatus, $page, $pageSize);
        //$config    = $allDisBiz->getAllDisMembersList($this->_sid, '', '测试发展佣金');

        $this->apiReturn($config['code'], $config['data'], $config['msg']);
    }

}
