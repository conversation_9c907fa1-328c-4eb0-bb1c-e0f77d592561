<?php
/**
 * 微商城后台产品相关接口
 *
 * User: xujy
 * Date: 2020/6/18
 */
namespace Controller\Mall;

use Library\Controller;

class AdminProduct extends Controller{

    /**
     *  添加优惠券选择产品的时候使用
     * <AUTHOR>
     * @date 2020/6/18
     * @return array
     */
    public function getProduct()
    {
        $keyword = I('keyword');
        $seaType = I('type', true, 'boolval');
        $size    = I('size', 10, 'intval');

        $loginInfoArr = $this->getLoginInfo('ajax');

        $productBiz = new \Business\Product\Product();
        $res        = $productBiz->getProductListForMallActivity($loginInfoArr['sid'], $keyword, $size, $seaType);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}
