<?php

namespace Controller\Mall;

class CutPrice extends Mall {


    /**
     * 获取正在进行中的砍价活动
     * <AUTHOR>
     * @date   2018-03-10
     */
    public function getCutProductList() {

        $page = I('page', 1, 'intval');
        $size = I('size', 10, 'intval');

        if (!$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($size > 20) {
            $this->apiReturn(204, [], '每页条数超出限制');
        }

        $openid = I('session.openid', '');

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->getCutProductList($this->_supplyId, $page, $size, $openid);

        if (isset($result)) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }


    /**
     * 设置砍价活动提醒
     * <AUTHOR>
     * @date   2018-03-10
     */
    public function setWarning() {
        //砍价活动id
        $id = I('id', 0, 'intval');
        //微信openid
        $openid = I('session.openid', '');
        if (I('session.identify') == 'allDis' && I('session.supply_openid')) {
            $openid = I('session.supply_openid');
        }
        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }
        if (!$openid) {
            $this->apiReturn(204, [], '没有发送微信提醒的权限');
        }

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->setWarning($this->_supplyId, $id, $openid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }


    /**
     * 获取砍价活动详情
     * <AUTHOR>
     * @date   2018-03-12
     */
    public function getActivityDetail() {
        //砍价活动id
        $id = I('id', 0, 'intval');
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        if (!$id || !$aid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $openid = I('session.openid', '');

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->getActivityDetail($this->_supplyId, $id, $aid, $openid, 1);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 是否关注了公众号
     * <AUTHOR>
     * @date   2018-04-10
     */
    public function hasSubscribe() {

        //微信openid
        $openid = I('session.openid', '');
        if (I('session.identify') == 'allDis' && I('session.supply_openid')) {
            $openid = I('session.supply_openid');
        }
        if (!$openid) {
            $this->apiReturn(200, [], '已关注');
        }

        $query = $_SERVER['HTTP_REFERER'];
        $urlInfo = parse_url($query);
        parse_str($urlInfo['query'], $query);
        $id = $query['id'];

        if ($id < 1) {
            $this->apiReturn(204, [], '来源错误');
        } 

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->hasSubscribe($id, $openid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }


    /**
     * 砍价详情页
     * <AUTHOR>
     * @date   2018-03-13
     */
    public function getCutPriceDetail() {
        $mid = $this->isLogin('ajax');
        //砍价活动id
        $id = I('id', 0, 'intval');
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        //发起砍价记录id
        $joinId = I('join_id', 0, 'intval');
        if (!$id || !$aid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $openid = I('session.openid', '');

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->getCutPriceDetail($this->_supplyId, $id, $aid, $mid, $joinId, $openid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }


    /**
     * 砍价
     * <AUTHOR>
     * @date   2018-03-13
     */
    public function cutPrice() {
        //用户id
        $mid = $this->isLogin('ajax');
        //帮砍的参与记录id
        $joinId = I('join_id', 0, 'intval');

        if (!$joinId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->cutPrice($mid, $joinId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }


    /**
     * 砍价帮列表
     * <AUTHOR>
     * @date   2018-03-14
     */
    public function getHelpList() {
        $this->isLogin('ajax');
        //帮砍的参与记录id
        $joinId = I('join_id', 0, 'intval');
        $page   = I('page', 1, 'intval');
        $size   = I('size', 10, 'intval');

        if (!$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($size > 20) {
            $this->apiReturn(204, [], '每页条数超出限制');
        }

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->getHelpList($joinId, $page, $size);

        if (isset($result)) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }


    /**
     * 我的砍价产品列表
     * <AUTHOR>
     * @date   2018-03-15
     */
    public function getMyCutPriceList() {

        $mid    = $this->isLogin('ajax');
        $page   = I('page', 1, 'intval');
        $size   = I('size', 10, 'intval');

        if (!$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($size > 20) {
            $this->apiReturn(204, [], '每页条数超出限制');
        }

        $cutBiz = new \Business\Wechat\CutPrice();
        $result = $cutBiz->getMyCutPriceList($this->_supplyId, $mid, $page, $size);

        if (isset($result)) {
            $this->apiReturn($result['code'], array_values($result['data']), $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }
    

}