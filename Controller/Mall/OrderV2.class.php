<?php

/**
 * 微商城订单相关接口
 * <AUTHOR>
 */

namespace Controller\Mall;

use Business\JavaApi\Express\PickPoint;
use Business\JavaApi\LogisticsCenter\Carriage;
use Business\JavaApi\Ticket\SpecialtyTicket;
use Business\JavaApi\TicketApi;
use Business\JavaApi\Ticket\Price;
use Business\Member\Member as MemberBiz;
use Business\Member\MemberSystem;
use Business\Member\RegisterAction;
use Business\Order\MergeOrder;
use Business\Order\OrderAidsSplitQuery;
use Business\Order\OrderList;
use Business\Order\OrderOta;
use Business\Order\OrderUnity;
use Business\Order\PlatformSubmit;
use Business\Product\Specialty;
use Business\UnionCloud\UnionCloudMember;
use Library\Business\WechatSmallApp;
use Library\Constants\MemberConst;
use Model\Member\Member;
use Model\Order\OrderTools;
use Model\Order\SubOrderQuery\SubOrderSplit;
use Model\Product\AnnualCard;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\Subdomain\SubdomainInfo;
use Process\Order\OrderParams;

class OrderV2 extends Mall
{

    private $_options = []; //下单参数

    private $_soap;

    private $_isNewCoupon = false;

    private $_selectType = false;

    private $ticModel = null;

    public function __construct()
    {

        parent::__construct();
        $this->_selectType = I('post.time_type');
    }

    private function getTicketModel()
    {
        if (is_null($this->ticModel)) {
            $this->ticModel = new Ticket('slave');
        }

        return $this->ticModel;
    }

    /**
     * 获取优惠券列表
     * @return [type] [description]
     */
    public function getCouponList()
    {
        $this->apiReturn(200, []);
    }

    /**
     * 提交订单
     * @return [type] [description]
     */
    public function order()
    {
        @pft_log('order/wechat/mall', json_encode($_REQUEST, JSON_UNESCAPED_UNICODE), $pathMode = 'day');
        //参数检验等
        $orderParam = $this->_beforeOrder();
        //下单
        $platformOrderBiz = new PlatformSubmit();
        $orderRes         = $platformOrderBiz->combineSubmit($orderParam);
        //下单后
        $this->_afterOrder($orderRes, $orderParam);
    }

    private function _beforeOrder()
    {
        //获取参数
        $paramsRes = OrderParams::getMallPlatParams($this->_supplyId);
        if ($paramsRes[0] != 200) {
            $this->apiReturn(204, [], $paramsRes[1]);
        }

        $orderParam = $paramsRes[2];
        //生成或者匹配下单人id
        $getMember = $this->_createOrParseMemberId($orderParam);
        if ($getMember[0] != 200) {
            $this->apiReturn(204, [], $getMember[1]);
        }

        $orderParam['member_id']    = $getMember[2];
        $orderParam['op_id']        = $getMember[2];
        $orderParam['is_sale']      = true;
        $orderParam['channel']      = $this->inWechatSmallApp() ? self::__APP_ORDER_MODE__ : self::__ORDER_MODE__;
        $orderParam['sale_channel'] = 1;
        if ($this->inUnionApp()) {
            $orderParam['channel'] = self::__UNION_ORDER_MODE__;
        }
        //优惠券使用检测
        $couponRes = $this->_couponUseCheck($orderParam);
        if ($couponRes[0] != 200) {
            $this->apiReturn(204, [], $couponRes[1]);
        }
        if ($couponRes[2]) {
            foreach ($orderParam['combine_pids'] as &$item) {
                if (isset($couponRes[2][$item['pid']])) {
                    $item['coupon'] = $couponRes[2][$item['pid']];
                }
            }
        }

        return $orderParam;
    }

    private function _couponUseCheck($orderParam)
    {
        if ($orderParam['coupon_code'] != -1) {
            //优惠券使用检查
            $useCouponBiz = new \Business\Market\UseCoupon();
            $checkRes     = $couponResult = $useCouponBiz->couponCheckRule($orderParam['coupon_code'], $orderParam,
                $orderParam['member_id'], $this->_supplyId);
            if ($checkRes['code'] == 200) {
                return [200, '', $checkRes['data']];
            } else {
                return [204, $checkRes['msg']];
            }
        }

        return [200, '', []];
    }

    private function _afterOrder($orderRes, $orderParam)
    {

        if ($orderRes[0] != 200) {
            $this->apiReturn(204, [], $orderRes[1]);
        }

        $orderData = $orderRes[2];
        $tradeId   = $orderData['tradeId'];
        $list      = $orderData['list'];

        $requestRaw = I('');
        //记录请求参数
        $this->_saveOrderAndParamsMap(array_column($list, 'ordernum'), $requestRaw);

        $couponUseMap = [];
        foreach ($orderParam['combine_pids'] as $value) {
            if (isset($value['coupon'])) {
                $couponUseMap[$value['pid']] = [
                    'value' => $value['coupon']['use_coupon_value'],
                    'code'  => $orderParam['coupon_code'],
                ];
            }
        }

        //兼容旧版返回
        $return = ['tradeId' => $tradeId];
        foreach ($list as $item) {
            $return['orderList'][] = [
                'ordernum' => $item['ordernum'],
                'paymode'  => $item['paymode'],
                'code'     => 200,
            ];

            \Library\Hook::Listen('mall_order_success',
                $this->_createHookParams($orderParam['member_id'], $item, $requestRaw));
            //优惠券占用记录
            if (isset($couponUseMap[$item['pid']])) {
                $this->_addCouponValUseLog($orderParam['member_id'], $item, $tradeId, $couponUseMap);
            }
        }
        $this->apiReturn(200, $return);
    }

    private function _createHookParams($mid, $orderInfo, $requestRaw)
    {

        $params                 = $orderInfo;
        $params['order_member'] = $mid;
        $params['aid']          = $this->_supplyId;
        $params['request']      = $requestRaw;

        //是否是全民营销订单
        if ($this->_allDisMan || isset($requestRaw['parentId'])) {
            $params['allDis_order'] = 1;
        }
        //是否是海报推广的订单
        if (isset($_COOKIE["poster:{$orderInfo['lid']}"])) {
            $params['poster_order'] = 1;
        }
        pft_log('allDis/issue', "下单订单v2---" . print_r($params, true) . PHP_EOL);

        return $params;
    }

    private function _createOrParseMemberId($orderParams)
    {
        //是否已经登录
        $memberId = I('session.memberID', 0);
        $dtype    = I('session.dtype', 0);
        if ($memberId) {
            //已经登录
            if ($dtype != MemberConst::ROLE_TOURIST) {
                return [204, '请先退出，并使用散客账号下单'];
            }
        } elseif ($this->inUnionApp()) {
            $unionPhone  = I('session.union_phone', 0);
            $unionOpenId = I('session.union_openid', 0);
            $unionBiz    = new UnionCloudMember();
            $unionRes    = $unionBiz->unionMemberCreate($unionPhone, $unionOpenId);
            if ($unionRes['code'] != 200) {
                return [204, $unionRes['msg']];
            }
            $memberId = $unionRes['data'];
        } else {
            $openid   = $this->_smallOpenid ?: '';
            $memberId = $this->_registerMember($orderParams['ordername'], $orderParams['ordertel'], $openid);
            if (!is_numeric($memberId)) {
                return [204, '新用户账号生成失败'];
            }
        }

        return [200, '', $memberId];
    }

    protected function changeMemberTypeForShop($customerId, $sid)
    {
        $memberBz   = new \Business\Member\Member();
        $customList = $memberBz->getMemberListByCustomerId($customerId, $sid);

        if ($customList['code'] != 200) {
            return false;
        }
        $customList = $customList['data'];

        foreach ($customList as $key => $value) {
            if ($value['dtype'] == 5) {
                return $value['id'];
            }
        }

        return false;
    }

    /**
     * 保存订单号和对应的请求参数信息
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @param  array  $orderArr  订单号数组
     * @param  array  $params  前端提交的参数
     *
     * @return bool
     */
    private function _saveOrderAndParamsMap($orderArr, $params)
    {
        (new \Model\Order\OrderLog())->saveOrderAndParamsMap($orderArr, $params, self::__ORDER_MODE__);
    }

    /**
     * 合并付款
     * <AUTHOR>
     * @dateTime 2018-03-14T09:52:36+0800
     * @throws   \Exception                             可能抛出异常
     * @return   [type]                   [description]
     */
    public function payCombine()
    {
        $tradeId  = I("tradeid");
        $host     = I('host', '', 'intval');
        $shopCode = I('post.shop_code', 0);

        if ($host == 'm') {
            $host = self::__DEFAULT_ACCOUNT;
        }

        $mergeOrder = new MergeOrder();

        if ($mergeOrder->isCombineOrder($tradeId)) {

            $ordernums = $mergeOrder->getMainOrderNumByTradeId($tradeId);
            $results   = [];
            foreach ($ordernums as $value) {
                $orderData           = $this->CombinePayItem($value, $host, $shopCode);
                $results['orders'][] = $orderData;
            }
            $money = $mergeOrder->getTradeIdTotleMoneyAndOnsaleMoney($tradeId);

            $results['totleMoney']  = $money['totalMoney'] / 100;
            $results['couponMoney'] = $money['totalOnSaleMoney'] / 100;

            $this->apiReturn(200, $results);
        } else {
            $results                = [];
            $combinePayItem         = $this->CombinePayItem($tradeId, $host, $shopCode);
            $results['orders'][]    = $combinePayItem;
            $results['totleMoney']  = $combinePayItem['detail']['totalmoney'];
            $results['couponMoney'] = '';

            $this->apiReturn(200, $results);
        }
    }

    public function CombinePayItem($ordernum, $host, $shopCode = 0)
    {
        if (!$ordernum || !$host) {
            return ["code" => 204, 'msg' => "参数错误"];
        }

        if ($shopCode) {
            $wxLib      = new WechatSmallApp();
            $merchantId = $wxLib->decodeShopCode($shopCode);
        }

        $Member     = new \Model\Member\Member();
        $Order      = new \Model\Order\OrderTools();
        $OrderQuery = new \Model\Order\OrderQuery('localhost');

        if ($this->inWechatSmallApp()) {
            //微票房直接取供应商ID
            $supply['id'] = $this->_supplyId;
        } else {
            $supply = $Member->getMemberInfo((string)$host, 'account', 'id');
        }

        // if (!$supply) {
        //     return ["code"=>204,'msg'=>"非法请求"];
        // }
        $orderInfo               = $Order->getOrderInfo($ordernum);
        $orderInfo['totalPrice'] = $OrderQuery->get_order_total_fee($ordernum);
//        $orderExtra = $Order->getOrderDetailInfo($ordernum);
        //获取票类信息
        $ticket = $this->getTicketModel()->getTicketInfoById($orderInfo['tid'], 'cancel_auto_onMin,title');
        // if (time() - strtotime($orderInfo['ordertime']) > $ticket['cancel_auto_onMin'] * 60) {
        //     return ["code"=>204,'msg'=>"已超过支付时间"];
        // }
        //获取游客实名信息
        $tourists = $this->_getTouristsReal($ordernum);

        //是否是抢购订单
        $seckillModel = new \Model\Mall\Seckill;
        $secOrder     = $seckillModel->getOrderInfo($ordernum);

        //判断下壶口瀑布微票房的  如果是壶口瀑布的  直接将merchantId替换成壶口瀑布的用户id（壶口瀑布独立收款问题特殊处理） @琪瑛 @光鹏
        if ($this->inWechatSmallApp() && $orderInfo['aid'] == 13769393) {
            $merchantId = $orderInfo['aid'];
        }

        if ($secOrder) {
            $secInfo = $seckillModel->getSeckill($secOrder['seckill_id']);
            if ($secInfo && $secInfo['cancel_time'] != -1) {
                $ticket['cancel_auto_onMin'] = $secInfo['cancel_time'];
            }
            //上面的getSeckill应该是没用的了, 但是为了不影响原有逻辑就先不删了, 在获取玩支付参数后, 调用Coo的rpc接口获取抢购配置信息, 更新每张票的抢购金额
        }

        //获取支付参数
        if ($this->inWechatSmallApp()) {
            $payParams = $this->_getPayParamsForSmallApp(
                $ordernum,
                $supply,
                $host,
                $ticket['title'],
                $ticket['cancel_auto_onMin']
            );
        } else {
            //订单号的供应商和商城所有者是否一致
            // if ($orderInfo['aid'] != $supply['id']) {
            //     $this->apiReturn(204, [], '非法请求');
            // }
            $payParams = $this->_getPayParams(
                $ordernum,
                $supply,
                $host,
                $ticket['title'],
                $ticket['cancel_auto_onMin']
            );
        }

        if ($secOrder) {
            //针对抢购订单, 手动更新一下票单价
            $activityBiz = new \Business\Cooperator\Activity\Activity();
            $res = $activityBiz->getSeckillActivityConf($secOrder['activity_id'], $secOrder['tid']);
            $activityPrice = $res['activity_conf']['price'] ?? 0;
            $activityPrice = bcdiv($activityPrice, 100, 2);
            $payParams['detail']['tickets'][0]['price'] = $activityPrice;
        }

        $payParams['ptype'] = $payParams['detail']['ptype'];
        // $payParams['payParams']['expireTime'] = $ticket['cancel_auto_onMin'];
        // 小程序需要
        $payParams['payParams']['shop_owner_id'] = isset($merchantId) ? $merchantId : '';
        $payParams['tourists']                   = $tourists;

        //FIXME 临时修复小乐园星球由于票有效期小于预订有效期, 导致票价格为-0.01的情况
        if($payParams['detail']['lid'] == 301119) {
            if(count($payParams['detail']['tickets']) == 1 && $payParams['detail']['tickets'][0]['price'] == '-0.01') {
                $price = $payParams['detail']['tprice'];
                $price = $price / 100;
                $payParams['detail']['tickets'][0]['price'] = $price;
                $payParams['detail']['tickets'][0]['window_price'] = $price;
                $payParams['detail']['tickets'][0]['retail_price'] = $price;
            }
        }

        return $payParams;
    }

    /**
     * 支付页面接口
     * 合并支付需要更改 获取的金额 以及 购买的票
     */
    public function pay()
    {

        $ordernum = I('ordernum', '', 'intval');
        $host     = I('host', '', 'intval');

        if ($host == 'm') {
            $host = self::__DEFAULT_ACCOUNT;
        }

        if (!$ordernum || !$host) {
            $this->apiReturn(204, [], '参数错误');
        }

        $Member     = new \Model\Member\Member();
        $Order      = new \Model\Order\OrderTools();
        $OrderQuery = $OrderQuery = new \Model\Order\OrderQuery('localhost');

        $supply = $Member->getMemberInfo((string)$host, 'account', 'id');
        if (!$supply) {
            $this->apiReturn(204, [], '非法请求');
        }
        $orderInfo               = $Order->getOrderInfo($ordernum);
        $orderInfo['totalPrice'] = $OrderQuery->get_order_total_fee($ordernum);
        $orderExtra              = $Order->getOrderDetailInfo($ordernum);
        if (!in_array($orderInfo['status'], [0, 4])) {
            $this->apiReturn(204, [], '订单状态错误');
        }
        if ($orderExtra['pay_status'] != 2) {
            $this->apiReturn(206, [], '订单已支付');
        }
        //获取票类信息
        $ticket = $this->getTicketModel()->getTicketInfoById($orderInfo['tid'], 'cancel_auto_onMin,title');
        if (time() - strtotime($orderInfo['ordertime']) > $ticket['cancel_auto_onMin'] * 60) {
            $this->apiReturn(204, [], '已超过支付时间');
        }

        //是否是抢购订单
        $seckillModel = new \Model\Mall\Seckill;
        $secOrder     = $seckillModel->getOrderInfo($ordernum);

        if ($secOrder) {
            $secInfo = $seckillModel->getSeckill($secOrder['seckill_id']);
            if ($secInfo && $secInfo['cancel_time'] != -1) {
                $ticket['cancel_auto_onMin'] = $secInfo['cancel_time'];
            }
        }

        //获取支付参数
        if ($this->inWechatSmallApp()) {
            $payParams = $this->_getPayParamsForSmallApp(
                $ordernum,
                $supply,
                $host,
                $ticket['title'],
                $ticket['cancel_auto_onMin']
            );
        } else {
            //订单号的供应商和商城所有者是否一致
            if ($orderInfo['aid'] != $supply['id']) {
                $this->apiReturn(204, [], '非法请求');
            }
            $payParams = $this->_getPayParams(
                $ordernum,
                $supply,
                $host,
                $ticket['title'],
                $ticket['cancel_auto_onMin']
            );
        }

        $payParams['ptype'] = $payParams['detail']['ptype'];
        // $payParams['payParams']['expireTime'] = $ticket['cancel_auto_onMin'];

        $this->apiReturn(200, $payParams);

    }

    /**
     * 支付是否完成
     * @return boolean [description]
     */
    public function isPayComplete($ordernum = '')
    {
        $ordernum = I('ordernum', '', 'intval');

        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }

//        $payMapping = (new \Model\Order\OrderTools())->getOrderPayStatus([$ordernum]);
        $queryParams = [[$ordernum],true,'pay_status'];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder','queryOrderInfoByOrdernumList', $queryParams);
        $payMapping = [];
        if ($queryRes['code'] == 200) {
            $payMapping = $queryRes['data'];
        }
        if ($payMapping[$ordernum] == 1) {
            $return['payStatus'] = 1;
        } else {
            $return['payStatus'] = 0;
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 支付成功页面
     */
    public function paySuccess()
    {
        $tradeId    = I('tradeId');
        $mergeOrder = new MergeOrder();
        $orderNums  = $mergeOrder->getMainOrderNumByTradeId($tradeId);
        $result     = [];

        foreach ($orderNums as $value) {
            $result[] = $this->_getOrderPaySuccessDetail($value);
        }
//        $memberInfo = $this->getLoginInfo();
//        $memberId   = $memberInfo['memberID'];
//        $sid        = $this->_supplyId;
//        //获取订单费用项
//        $memberSystemModeBs = new MemberSystem();
//        $orderExpenseitem   = $memberSystemModeBs->getMulOrderExpenseitem($sid, $memberId, $orderNums, 'ordernum, item_money');
//        $orderExpenseitem   = array_key($orderExpenseitem['data'], 'ordernum');
        foreach ($result as &$value) {
            $value['money'] = 0;
        }
        $this->apiReturn(200, $result);
    }

    /**
     * 获取订单支付结果
     * <AUTHOR>
     * @dateTime 2018-03-16T14:21:16+0800
     * @throws   \Exception                         可能抛出异常
     *
     * @param    [type]                   $ordernum [description]
     *
     * @return   [type]                             [description]
     */
    private function _getOrderPaySuccessDetail($ordernum)
    {
        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }

        $return = $this->_getOrderDetail($ordernum);

        // if (!$this->inWechatApp() && $return['aid'] != $this->_supplyId) {
        //     // 支付宝小程序，先不判断这个
        //     if (!$this->inAlipaySmallApp()) {
        //         $this->apiReturn(204, [], '非法访问');
        //     }
        // }

        $landExtDraft  = [];
        $ticketBiz     = new \Business\JavaApi\CommodityCenter\Ticket();
        $getTicketInfo = $ticketBiz->queryTicketAttrsById($return['tid']);
        if ($getTicketInfo['code'] == 200) {
            foreach ($getTicketInfo['data'] as $attr) {
                $landExtDraft[$attr['key']] = $attr['val'];
            }
        }
        if (!isset($landExtDraft['face_open'])) {
            $return['is_face'] = 0;
        } else {
            $return['is_face'] = (int)$landExtDraft['face_open'];
        }

        //$ticketModel   = new \Model\Product\Ticket();
        //$ticketExtInfo = $ticketModel->getFaceConfig([$return['tid']], 'face_open');
        //if ($ticketExtInfo === false) {
        //    $return['is_face'] = 0;
        //} else {
        //    $return['is_face'] = (int)$ticketExtInfo[0];
        //}

        $toolModel = new \Model\Order\OrderTools('slave');
        $extra     = $toolModel->getOrderDetailInfo($ordernum);

        if (!$extra['aids']) {
            $aid = $this->_supplyId;
        } else {
            $tmp = explode(',', $extra['aids']);
            $aid = array_pop($tmp);
        }

        $return['aid'] = $aid;

        // 关注公众号
        $wxOpenModel = new \Model\Wechat\WxOpen();
        $account     = explode('.', $_SERVER['HTTP_HOST'])[0];
        $info        = $wxOpenModel->getWechatOffiAccInfo((int)$account, 'account');
        if ($info) {
            $return['alert']      = 1;
            $return['qrcode_url'] = $info['qrcode_url'];
            $return['nick_name']  = $info['nick_name'];

        } else {
            $return['alert'] = 0;
        }

        //二维码类型 门票码判断
        if ($return['tickets'][0]['print_mode'] == 4 && ($return['tickets'][0]['code_show_type'] == '' || $return['tickets'][0]['code_show_type'] == 1)) {
            $temidxArr = $toolModel->getTouristOrederIdxs($ordernum, 'idx, check_state, orderid, chk_code');
            $idxArr    = array_column($temidxArr, 'idx');
            if ($idxArr) {
                foreach ($temidxArr as $temVal) {
                    if ($temVal['check_state'] != 2) {
                        if (empty($temVal['chk_code'])) {
                            // 没有门票码用OD#
                            $temMultiCode                               = OrderUnity::getCodeByOrdernumTnum($ordernum,
                                $return['tickets'][0]['num'], $idxArr, $temVal['idx'])[0];
                            $return['multi_code'][]                     = $temMultiCode;
                            $return['multi_code_status'][$temMultiCode] = $temVal['check_state'];
                        } else {
                            $return['multi_code'][]                           = $temVal['chk_code'];
                            $return['multi_code_status'][$temVal['chk_code']] = $temVal['check_state'];
                        }
                    }
                }
            }
        }

        //如果是第三方订单且存在第三方订单凭证码， 则获取第三方订单凭证码
        $orderOtaBus   = new OrderOta();
        $handleCodeRes = $orderOtaBus->handleCode($ordernum, $return['qrcode']);
        $noticeType    = $orderOtaBus->getNoticeCode($return['lid']);

        $return['notice_type'] = $noticeType;
        //第三方订单情况处理
        if ($noticeType != -1) {
            $return['qrcode'] = $handleCodeRes['code'];//赋值为第三方的凭证码
        }
        //第三方订单凭证码生成类型为默认类型0时候的处理
        if ($noticeType == 0) {
            if (!empty($handleCodeRes['code'])) {
                $return['notice_type'] = 1;//类型为同步发码
            } else {
                $return['notice_type'] = 3;//类型为不发码
                if ($handleCodeRes['handle_status'] == 2) {
                    //超时情况下
                    $return['notice_type'] = 2;//类型为异步发码
                }
            }
            //all_api_order表没有记录情况为平台订单
            if ($handleCodeRes['handle_status'] == -1) {
                $return['notice_type'] = 0;//类型为非第三方订单
            }
        }
        //同步发码超时情况处理
        if ($noticeType == 1 && empty($handleCodeRes['code']) && $handleCodeRes['handle_status'] == 2) {
            $return['notice_type'] = 2;//类型为异步发码
        }
        //非第三方订单情况处理
        if ($noticeType == -1) {
            $return['notice_type'] = 0;//类型为非第三方订单
        }

        return $return;
    }

    /**
     * 订单是否完成支付
     * @return boolean [description]
     */
    public function isOrderComplete()
    {
        $ordernum = I('ordernum', 0, 'intval');
        $host     = I('host', '', 'intval');

        if ($host == 'm') {
            $host = self::__DEFAULT_ACCOUNT;
        }

        if (!$ordernum || !$host) {
            $this->apiReturn('204', [], '参数错误');
        }

        $mModel = new Member('slave');
        $supply = $mModel->getMemberInfo($host, 'account');

        if (!$supply) {
            $this->apiReturn(204, [], '非法请求');
        }

        $Order = new \Model\Order\OrderTools();

        $orderInfo = $Order->getOrderInfo($ordernum);
        //订单号的供应商和商城所有者是否一致
        if ($orderInfo['aid'] != $supply['id'] || $orderInfo['ordermode'] != self::__ORDER_MODE__) {
            $this->apiReturn(204, [], '非法请求');
        }

//        $payMapping = $Order->getOrderPayStatus([$ordernum]);
        $queryParams = [[$ordernum],true,'pay_status'];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder','queryOrderInfoByOrdernumList', $queryParams);
        $payMapping = [];
        if ($queryRes['code'] == 200) {
            $payMapping = $queryRes['data'];
        }
        $payStatus = 0;
        if ($payMapping[$ordernum] == 1) {
            $payStatus = 1;
        }

        $this->apiReturn(200, ['payStatus' => $payStatus]);
    }

    /**
     * 注册新账号
     *
     * @param  [type] $data 账号信息
     *
     * @return [type]       [description]
     */
    private function _registerMember($name, $mobile, $openid, $sfz = '', $avatar = '')
    {

        $memberBiz   = new MemberBiz();
        $memberModel = new Member('slave');

        $infoSource = MemberConst::INFO_MOBILE;
        $pageSource = MemberConst::PAGE_MALL;
        $identifier = $mobile;
        //这边先做个判断，太乱了
        if ($this->inWechatSmallApp()) {
            $infoSource = MemberConst::INFO_WX_SMALL_APP;
            $pageSource = MemberConst::PAGE_WX_SMALLAPP;
            $identifier = $openid;
        }
        if ($this->inAlipaySmallApp()) {
            $infoSource = MemberConst::INFO_ALI_SAMLL_APP;
            $pageSource = MemberConst::PAGE_ALI_SMALLAPP;
            $identifier = $openid;
        }

        //客户是否存在
        if ($this->inWechatSmallApp() || $this->inAlipaySmallApp()) {
            //兼容旧数据结构，获取customer_id
            $oldExist = $memberModel->getMemberInfo($openid, 'account', 'id');
            if ($oldExist) {
                return $oldExist['id'];
            }
            $authInfo   = $memberModel->getAuthByIdentifier($openid, $infoSource + 1);
            $customerId = $authInfo ? $authInfo['customer_id'] : 0;
        } else {
            $customerId = $memberBiz->parseCustomerIdByMobile($mobile);
        }

        if ($customerId) {
            //角色是否存在
            $role = $memberModel->getTheRoleInfo($customerId, MemberConst::ROLE_TOURIST, 'id');
            if ($role) {
                return $role['id'];
            }
        }

        if (ismobile($mobile)) {
            $passwd = substr($mobile, 5);
        } else {
            $passwd = '';
        }

        $request = [
            'name'        => $name,
            'passwd'      => $passwd,
            'type'        => MemberConst::ROLE_TOURIST,
            'avatar'      => $avatar,
            'id_card'     => $sfz,
            'identifier'  => $identifier,
            'customer_id' => $customerId,
            'info_source' => $infoSource,
            'page_source' => $pageSource,
        ];

        $registerAction = new RegisterAction();
        $result         = $registerAction->register((object)$request);

        if ($result['code'] == 200) {
            return $result['data']['member_id'];
        } else {
            return false;
        }
    }

    /**
     * 添加优惠券占用使用记录表
     *
     * @param $orderInfo   子订单信息
     * @param $tradeid     合并付款的订单号
     *
     * @return bool
     *
     */
    private function _addCouponValUseLog($memberId, $orderInfo, $tradeid, $couponUseMap)
    {
        // 录入优惠券使用记录
        $couponOrderModel = new \Model\Order\Coupon('remote_1');
        $useLogDataArr    = [
            'fid'                  => $memberId,
            'aid'                  => $this->_supplyId,
            'use_order_num'        => $orderInfo['ordernum'],
            'tradeid'              => $tradeid,
            'all_use_coupon_money' => $couponUseMap[$orderInfo['pid']]['value'],
            'use_time'             => time(),
            'status'               => 1,
            'async_status'         => 1,
            'coupon_code'          => trim($couponUseMap[$orderInfo['pid']]['code']),
        ];
        $addres           = $couponOrderModel->addCoponUseLog($useLogDataArr);
        if (!$addres) {
            // 添加日志
        }
    }

    /**
     * 获取支付参数
     *
     * @param  string  $ordernum  订单号
     * @param  array  $supply  供应商账号信息
     * @param  string  $host  供应商账号
     * @param  string  $ttitle  门票名称
     * @param  int  $autoCancel  自动取消时间
     *
     * @return array
     */
    private function _getPayParams($ordernum, $supply, $host, $ttitle, $autoCancel)
    {
        //微商城配置信息
        $mallConfig = $this->getMallConfig($supply['id']);
        $others     = json_decode($mallConfig['others'], true);

        $hasSetPay = isset($others['pay_ali']);

        $cardSolutionPay = 0;
        if (!empty($others['pay_card'])) {
            $ConfigSolutionModel = new \Business\CardSolution\Config();
            $checkRes            = $ConfigSolutionModel->isCanUseCardSolutionPay($ordernum);

            if ($checkRes === true) {
                $cardSolutionPay = 1;
            }
        }

        $return = [
            'payWay' => [
                'ali'  => $hasSetPay ? $others['pay_ali'] : 1,
                'wx'   => $hasSetPay ? $others['pay_wx'] : 1,
                'uni'  => $hasSetPay ? $others['pay_uni'] : 1,
                'card' => $cardSolutionPay,
            ],
        ];
        //不在微信app内
        if (!$this->inWechatApp()) {
            $return['payWay']['wx'] = 0;
        }

        $detail = $this->_getOrderDetail($ordernum);

        $payParams = [
            'subject'    => $detail['landTitle'] . $ttitle,
            'expireTime' => $autoCancel,
            'outTradeNo' => $ordernum,
            'buyId'      => $supply['id'],
            'domain'     => str_replace('wx', $host, MOBILE_DOMAIN),
        ];

        unset($detail['qrcode']);
        $return['detail'] = $detail;

        if ($return['payWay']['wx']) {
            $payParams['appid'] = self::__DEFAULT_APPID__;
            //需要从网页授权中获取
            $payParams['openid'] = $_SESSION['pft_openid'];
        }

        $return['payParams'] = $payParams;

        return $return;
    }

    /**
     * 获取支付参数
     *
     * @param  string  $ordernum  订单号
     * @param  array  $supply  供应商账号信息
     * @param  string  $host  供应商账号
     * @param  string  $ttitle  门票名称
     * @param  int  $autoCancel  自动取消时间
     *
     * @return array
     */
    private function _getPayParamsForSmallApp($ordernum, $supply, $host, $ttitle, $autoCancel)
    {

        $return = [
            'payWay' => [
                'wx' => 1, //微信支付
            ],
        ];
        //订单详情
        $detail = $this->_getOrderDetail($ordernum);

        $payParams = [
            'ordernum'   => $ordernum,
            'subject'    => $detail['landTitle'] . $ttitle,
            'expireTime' => $autoCancel,
            'outTradeNo' => $ordernum,
            'buyId'      => $supply['id'],
            'domain'     => str_replace('wx', $host, MOBILE_DOMAIN),
        ];
        $tempAppid = 'wx5605b231e666f425';
        if (ENV == 'PRODUCTION') {
            $tempAppid = 'wx2f45381cd36a6400';
        }
        //unset($detail['qrcode']);
        $return['detail'] = $detail;
        $conf             = load_config($this->_supplyAccount, 'wechat_sm_app');
        $appid            = !is_null($conf) ? $conf['appid'] : $tempAppid;
        //独立小程序，appid逻辑另外处理
        if (!empty($_SERVER['HTTP_WX_MINI_APPID']) && strcmp($_SERVER['HTTP_WX_MINI_APPID'], $tempAppid) != 0) {
            $appid = $_SERVER['HTTP_WX_MINI_APPID'];
        }
        if ($return['payWay']['wx']) {
            $payParams['appid']  = $appid;
            $payParams['openid'] = $this->_smallOpenid;
        }

        $return['payParams'] = $payParams;

        return $return;
    }

    /**
     * 获取订单详细信息
     *
     * @param  int  $ordernum  订单号
     *
     * @return array
     */
    private function _getOrderDetail($ordernum)
    {
        $Order      = new \Model\Order\OrderTools();
        //小程序还在用此接口，为了获取准确的支付订单价，该查询需要查主库
        $OrderQuery = new \Model\Order\OrderQuery('localhost');
        $Land       = new \Model\Product\Land();

        $orderExtra = $Order->getOrderDetailInfo($ordernum);
        $orderInfo  = $Order->getOrderInfo($ordernum);

        //景区类型
        $land  = $Land->getLandInfo($orderInfo['lid'], false, 'p_type,title,imgpath');
        $pType = $land['p_type'];

        //获取包含的门票信息
        $tickets = $this->_getOrderTickets(
            $ordernum,
            $orderInfo['tid'],
            $orderInfo['tnum'],
            //是否是联票
            $orderExtra['concat_id'] ? true : false,
            $orderInfo['playtime']
        );
        //根据景区类型不同，获取一些额外的展示信息
        $extra = $this->_getExtraInfo(
            $pType,
            $orderInfo,
            $orderExtra
        );

        $totalmoney = $OrderQuery->get_order_total_fee($ordernum);

        //获取订单的优惠金额
        //$orderCouponModel = new \Model\Order\Coupon();
        $orderCouponModel = new \Model\Order\Coupon('localhost');
        $couponInfo       = $orderCouponModel->getOrderCouponInfo($ordernum, 'emoney');
        //分时预约数据
        $extContent           = json_decode($orderExtra['ext_content'], true);
        $packageTimeShareInfo = $extContent['packageTimeShareInfo'];
        //存在套票分时预约数据时，根据主票id获取子票信息
        if ($packageTimeShareInfo && $pType == 'F') {
            //$javaApi = new \Business\JavaApi\Product\PackageTicket();
            //$result  = $javaApi->queryPageTicketInfoListByParentId($orderInfo['tid']);
            //$ticketApi = new TicketApi();
            //$result    = $ticketApi->getSonTicketList($orderInfo['tid']);
            $packApi = new \Business\PackTicket\PackRelation();
            $result  = $packApi->queryPageTicketInfoListByParentId($orderInfo['tid']);
            if (!empty($result['data'])) {
                $childList = array_key($result['data'], 'ticket_id');
                foreach ($packageTimeShareInfo as $key => $value) {
                    if (isset($childList[$key])) {
                        $packageTimeShareInfo[$key]['ticket_name'] = $childList[$key]['ticket_name'];
                        $packageTimeShareInfo[$key]['item_name']   = $childList[$key]['item_name'];
                    }
                }
            }
        }

        return [
            'lid'                     => $orderInfo['lid'],
            'pid'                     => $orderInfo['pid'],
            'aid'                     => $orderInfo['aid'],
            'tid'                     => $orderInfo['tid'],
            'tnum'                    => $orderInfo['tnum'],
            'tprice'                  => $orderInfo['tprice'],
            'ptype'                   => $land['p_type'],
            'landTitle'               => $land['title'],
            'landImage'               => !empty($land['imgpath']) ? $land['imgpath'] : '',
            'memo'                    => !empty($orderExtra['memo']) ? $orderExtra['memo'] : '',
            'totalmoney'              => $totalmoney / 100,
            'couponMoney'             => !empty($couponInfo) ? ($couponInfo / 100) : 0,
            'ordername'               => $orderInfo['ordername'],
            'ordertel'                => $orderInfo['ordertel'],
            'qrcode'                  => $orderInfo['code'],
            'tickets'                 => $tickets,
            'paymode'                 => $orderInfo['paymode'],
            'ordernum'                => $ordernum,
            'extra'                   => $extra,
            'pay_status'              => $orderInfo['pay_status'],
            'time_share_order'        => isset($extContent['sectionTimeStr']) ? $extContent['sectionTimeStr'] : '',
            'package_time_share_info' => $packageTimeShareInfo ?: "",
        ];
    }

    /**
     * 获取订单的门票信息
     *
     * @param  int  $ordernum  主票订单号
     * @param  int  $tid  主票tid
     * @param  int  $tnum  主票票数
     * @param  boolean  $link  是否是联票
     * @param  string  $date  下单游玩日期
     *
     * @return [type]            [description]
     */
    private function _getOrderTickets($ordernum, $tid, $tnum, $link = false, $date = '')
    {

        if ($link) {
            $field = 'ss.tid,ss.tnum';

            $tickets = (new \Model\Order\OrderTools())->getLinkOrdersInfo($ordernum, $field);
        } else {
            $tickets[] = [
                'tid'  => $tid,
                'tnum' => $tnum,
            ];
        }

        $Model      = $this->getTicketModel();
        $priceApi   = new Price();
        //$ticketApi  = new \Business\JavaApi\TicketApi();
        $tidArray   = array_column($tickets, 'tid');
        $javaApi  = new \Business\CommodityCenter\Ticket();
        $queryInfoArr = [];
        foreach ($tidArray as $key => $value) {
            $queryInfoArr[$key]['ticketId'] = $value;
        }
        $tidData = [];
        $ticketInfoArr = $javaApi->queryTicketInfoByQueryInfos($queryInfoArr);
        if ($ticketInfoArr) {
            foreach ($ticketInfoArr as $item) {
                $tidData[] = $javaApi->fieldConversion($item);
            }
        }
        //$tidStr     = implode(',', array_unique($tidArray));
        //$tidData    = $ticketApi->getTicketArr($tidStr);
        $ticketData = [];
        foreach ($tidData as $ticket) {
            $ticketData[$ticket['id']] = [
                'apply_did'        => $ticket['account_id'],
                'title'            => $ticket['name'],
                'getaddr'          => $ticket['get_ticket_info'],
                'pid'              => $ticket['product_id'],
                'entry_method'     => isset($ticket['ext']['entry_method']) ? $ticket['ext']['entry_method'] : 0,
                'is_show_ordernum' => isset($ticket['ext']['is_show_ordernum']) ? $ticket['ext']['is_show_ordernum'] : 0,
                'print_mode'       => isset($ticket['ext']['print_mode']) ? $ticket['ext']['print_mode'] : 0,
                'code_show_type'   => isset($ticket['ext']['code_show_type']) ? $ticket['ext']['code_show_type'] : '',
                'refund_rule'      => isset($ticket['refund_rule']) ? $ticket['refund_rule'] : '0',
            ];
        }

        $return = [];
        foreach ($tickets as $item) {
            $priceRes     = $priceApi->getActualtimePrice($ticketData[$item['tid']]['apply_did'],
                $ticketData[$item['tid']]['apply_did'], $tid, $date);
            $retail_price = 0;
            $window_price = 0;
            if ($priceRes['code'] != 200) {
                $price = 0; //正常都会获取到
            } else {
                $price        = $this->inWechatSmallApp() || $this->inAlipaySmallApp() ? $priceRes['data']['window_price'] / 100 : $priceRes['data']['retail_price'] / 100;
                $retail_price = $priceRes['data']['retail_price'] / 100;
                $window_price = $priceRes['data']['window_price'] / 100;
            }

            $return[] = [
                'title'            => $ticketData[$item['tid']]['title'],
                'num'              => $item['tnum'],
                'price'            => $price,
                'retail_price'     => $retail_price,
                'window_price'     => $window_price,
                'getaddr'          => $ticketData[$item['tid']]['getaddr'],
                'entry_method'     => $ticketData[$item['tid']]['entry_method'],
                'is_show_ordernum' => $ticketData[$item['tid']]['is_show_ordernum'],
                'print_mode'       => isset($ticketData[$item['tid']]['print_mode']) ? $ticketData[$item['tid']]['print_mode'] : 0,
                'code_show_type'   => isset($ticketData[$item['tid']]['code_show_type']) ? $ticketData[$item['tid']]['code_show_type'] : '',
                'refund_rule'      => isset($ticketData[$item['tid']]['refund_rule']) ? $ticketData[$item['tid']]['refund_rule'] : '',
            ];
        }

        return $return;
    }

    /***
     * 根据订单获取游客实名信息
     * <AUTHOR> Yiqiang
     * @date   2018-09-13
     *
     * @param  string  $ordernum  订单号
     */
    private function _getTouristsReal($ordernum)
    {
        $Order       = new \Model\Order\OrderTools();
        $touristInfo = $Order->getOrderTouristInfo(strval($ordernum));
        $tourists    = array();
        if ($touristInfo) {
            foreach ($touristInfo as $tourist) {
                $tourists[] = [
                    'name'   => $tourist['tourist'],
                    'idcard' => $tourist['idcard'],
                ];
            };
        }

        return $tourists;
    }

    /**
     * 根据票种获取不同的订单信息
     *
     * @param  string  $type  类型
     * @param  array  $orderInfo  订单信息
     * @param  array  $orderExtra  订单额信息
     *
     * @return [type]             [description]
     */
    private function _getExtraInfo($type, $orderInfo, $orderExtra)
    {

        switch ($type) {
            case 'A':
                return $this->_getExtraInfoForLand($orderInfo);
                break;

            case 'F':
                return $this->_getExtraInfoForLand($orderInfo);
                break;

            case 'B':
                return $this->_getExtraInfoForLoad($orderInfo, $orderExtra);
                break;

            case 'C':
                return $this->_getExtraInfoForHotel($orderInfo);
                break;
            case 'G':
                return $this->_getExtraInfoForLand($orderInfo);
                break;
            case 'H':
                return $this->_getExtraInfoForShow($orderExtra, $orderInfo);
                break;

            case 'I':
                return $this->_getExtraInfoForAnnual($orderInfo);
                break;

            case 'J':
                return $this->_getExtraInfoForSpecial($orderInfo, $orderExtra);
                break;

            default:
                # code...
                break;
        }
    }

    /**
     * 获取景区订单的信息
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return [type]            [description]
     */
    private function _getExtraInfoForLand($orderInfo)
    {

        //有效时间
        $date = $orderInfo['begintime'] . '~' . $orderInfo['endtime'];

        return [
            'date' => $date,
        ];
    }

    /**
     * 获取线路订单的信息
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return [type]            [description]
     */
    private function _getExtraInfoForLoad($orderInfo, $orderExt = [])
    {

        //集合时间
        $date   = $orderInfo['begintime'];
        $ticket = $this->getTicketModel()->getTicketExtInfoByTid($orderInfo['tid'], 'ass_station');

        //集合地点
        $station = $ticket['ass_station'] ? json_decode($ticket['ass_station'], true) : [];

        return [
            'date'    => $date,
            'station' => $station[$orderExt['assembly']],
        ];
    }

    /**
     * 获取酒店订单的信息
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return [type]            [description]
     */
    private function _getExtraInfoForHotel($orderInfo)
    {

        $link = (new \Model\Order\OrderTools())->getLinkOrdersInfo($orderInfo['ordernum'], 'playtime');

        $last = array_pop($link);

        if (!$last) {
            $last['playtime'] = $orderInfo['playtime'];
        }

        $begintime = strtotime($orderInfo['playtime']);
        $endtime   = strtotime($last['playtime']) + 3600 * 24;

        //住店时间
        $date = $orderInfo['playtime'] . '~' . date('Y-m-d', $endtime);

        //住店天数
        $days = ($endtime - $begintime) / 3600 / 24;

        return [
            'date' => $date,
            'days' => $days,
        ];

    }

    /**
     * 获取演出订单的信息
     *
     * @param  [type] $orderExtra 订单额外信息
     *
     * @return [type]             [description]
     */
    private function _getExtraInfoForShow($orderExtra, $orderInfo = '')
    {
        $series = unserialize($orderExtra['series']);

        //演出日期
        $date = $series[11] ?? $series[4];

        //座位
        $seat = explode(',', $series[6])[2];
        if (!empty($orderInfo)) {
            $tid           = $orderInfo['tid'];
            $ticketExtInfo = [];
            $ticketBiz     = new \Business\JavaApi\CommodityCenter\Ticket();
            $getTicketInfo = $ticketBiz->queryTicketAttrsById($tid);
            if ($getTicketInfo['code'] == 200) {
                foreach ($getTicketInfo['data'] as $attr) {
                    if ($attr['key'] == 'is_show_seat') {
                        $ticketExtInfo[$attr['ticket_id']] = $attr;
                    }
                }
            }
            //$ticketModel   = new \Model\Product\Ticket();
            //$ticketExtInfo = $ticketModel->getTicketExtConfig([$orderInfo['tid']], 'ticket_id,key,val',
            //    ['is_show_seat']);
            if (!empty($ticketExtInfo[$tid]['val']) && $ticketExtInfo[$tid]['val'] == 2) {
                $seat = '';
            }
        }
        //处理区-座位
        $seatZone = '';
        $seatInfo = explode(',', $series[6]);
        if (!empty($seatInfo)) {
            $seatStr  = '';
            $zoneName = explode(':', $seatInfo[1])[1];
            $seatArr  = explode('_', $seatInfo[2]);
            foreach ($seatArr as $item) {
                if (empty($item)) {
                    continue;
                }
                $tmp     = explode('-', $item);
                $tmp[0]  = str_replace("座位号:", '', $tmp[0]);
                $seatStr .= $tmp[2]. $tmp[0] . '排' . $tmp[1] . '座，';
            }
            $seatStr = rtrim($seatStr, "，");
            if (!empty($seatStr)) {
                $seatZone = "({$zoneName}) - " . $seatStr;
            }
        }

        return [
            'date'     => $date,
            'seat'     => $seat,
            'seatZone' => $seatZone,
        ];
    }

    /**
     * 获取年卡订单信息
     * <AUTHOR>
     * @date   2017-11-22
     *
     * @param  array  $orderInfo  订单详情
     *
     * @return array
     */
    private function _getExtraInfoForAnnual($orderInfo)
    {

        $ordernum = (string)$orderInfo['ordernum'];

        //获取虚拟卡号
        $annualModel = new AnnualCard('slave');

        $virtualNo = $annualModel->getVirtualNoByOrdernum($ordernum);

        return [
            'virtual_no' => $virtualNo ?: '',
            'physics_no' => '',
        ];
    }

    /**
     * 获取特产订单信息
     * <AUTHOR>
     * @date   2017-12-13
     *
     * @param  array  $orderInfo  订单详情
     * @param  array  $orderExtra  订单额外信息
     *
     * @return array
     */
    private function _getExtraInfoForSpecial($orderInfo, $orderExtra)
    {
        $return     = [];
        $extArr     = json_decode($orderExtra['product_ext'], true);
        //获取特产配置信息
        $specialGoodServiceApi = new SpecialtyTicket();
        $goodsInfo             = $specialGoodServiceApi->querySpecialPriceByGoodsIdAndTicketIdsAndEvolute($orderInfo['aid'],
            0, 0, $orderInfo['lid'], [$orderInfo['tid']]);
        //获取特产景区信息
        $return['ttitle']  = "";
        if (!empty($goodsInfo['data'][0]['specialValue'])) {
            foreach ($goodsInfo['data'][0]['specialValue'] as $value) {
                $return['ttitle'] .= $value['itemName'] . ":" . $value['valueName'];
            }
        }
        //计算订单有效期
        $landInfo      = $specialGoodServiceApi->getSpecialGoodsByGidOrLid('', $orderInfo['lid']);
        $orderValueDay = date('Y-m-d H:i:s', strtotime("+ {$landInfo[0]['goodsDeliveryAttribute']['no_pick_auto_cancel']} day", strtotime($orderInfo['ordertime'])));
        $return['refund_rule'] = $landInfo[0]['goodsRefundAttribute']['refund_rule'];
        //deliveryType  0:快递  1:自取
        $return['delivery_way'] = $extArr['deliveryType'];
        if ($extArr['deliveryType'] == 1) {
            //请求自提点信息
            $pickPointApi = new PickPoint();
            $pickData     = $pickPointApi->queryExpressPickPointByTid($orderInfo['tid']);
            $areaCodeArr = explode('|', $pickData['areaCode']);
            $areaCodeArr = array_filter($areaCodeArr);
            if ($areaCodeArr) {
                $areaModel = new \Model\Product\Area();
                $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
            } else {
                $codeMap   = [];
            }
            //自提点信息
            $return['take_delivery'] = [
                'area'               => implode('', array_values($codeMap)),
                'address'            => $pickData['address'],
                'linkman'            => $pickData['linkman'] . '/' . $pickData['phone'],
                'reception_time'     => $pickData['acceptTime'],
                'cancel_auto_on_min' => $orderValueDay,
                'province_code'      => $areaCodeArr[0] ?? 0,
            ];
            $return['delivery_price'] = 0;
        } else {
            //快递
            //收货信息
//            $orderUserModel = new \Model\Order\OrderUser();
//            $orderUserInfo  = $orderUserModel->getOneOrderUserByOrdernum($orderInfo['ordernum'],
//                'personid,voucher_type,province_code,city_code,town_code,address');
            $queryParams = [[$orderInfo['ordernum']]];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderUserInfo','getOrderUserInfoByOrderNum', $queryParams);
            $orderUserInfo = [];
            if ($queryRes['code'] == 200) {
                $orderUserInfo = $queryRes['data'][0];
            }

            $areaCodeArr = [$orderUserInfo['province_code'], $orderUserInfo['city_code'], $orderUserInfo['town_code']];
            $areaCodeArr = array_filter($areaCodeArr);
            if ($areaCodeArr) {
                $areaModel = new \Model\Product\Area();
                $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
            } else {
                $codeMap = [];
            }

            $return['take_delivery'] = [
                'province'      => $codeMap[$orderUserInfo['province_code']] ?? '',
                'city'          => $codeMap[$orderUserInfo['city_code']] ?? '',
                'town'          => $codeMap[$orderUserInfo['town_code']] ?? '',
                'address'       => $orderUserInfo['address'],
                'exp_company'   => $extArr['expCompany'] ?? '',
                'exp_no'        => $extArr['expNo'] ?? '',
                'province_code' => $areaCodeArr[0] ?? 0,
            ];
            $return['delivery_price'] = $extArr['carriage'] ?? 0;
        }
        $return['take_delivery']['code']       = $orderInfo['code'];
        $return['take_delivery']['valid_date'] = $orderValueDay;

        return $return;
    }

    /**
     * 微票房订单详细
     * Create by zhangyangzhen
     * Date: 2019/2/1
     * Time: 15:30
     */
    public function getWPFOrderDetail()
    {
        $memberId = $this->isLogin('ajax');

        $ordernum = I('post.ordernum', '', 'strval');

        if (!$ordernum) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        //判断当前用户是否有权限查看该订单
        //$chainModel = new SubOrderSplit();
        //$orderChain = $chainModel->getListByOrderSingle($ordernum, 'id,buyerid,sellerid');

        //订单查询迁移三期
        $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
        $orderChain = $orderAidsSplitQueryLib->getListByOrderSingleNew(strval($ordernum));

        $buyerid  = array_column($orderChain, 'buyerid');
        $sellerid = array_column($orderChain, 'sellerid');
        $members  = array_unique(array_merge($buyerid, $sellerid));

        if (!in_array($memberId, $members)) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无权查看此订单');
        }

        $orderTools = new OrderTools();

        $orderInfo               = $orderTools->getOrderInfo($ordernum);
        $orderInfo['totalPrice'] = $orderInfo['totalmoney'];

        //套票处理
        $packBiz   = new \Business\Product\PackTicket();
        $childData = $packBiz->getChildTickets($orderInfo['tid']);
        if (!empty($childData)) {
            $orderInfo['childTickets'] = $childData;
        }

        //游客信息
        $touristInfo = $orderTools->getOrderTouristInfo($ordernum);
        if ($touristInfo) {
            foreach ($touristInfo as $tourist) {
                if ($tourist['idcard']) {
                    $orderInfo['tourists'][] = [
                        'name'   => $tourist['tourist'],
                        'idcard' => $tourist['idcard'],
                    ];
                }
            };
        }

        $data = $this->_handleWPFOrderDetail($orderInfo);

        $this->apiReturn(self::CODE_SUCCESS, $data);
    }

    /**
     * 微票房订单确认页 -- 获取日历价格
     * Create by zhangyangzhen
     * Date: 2019/2/2
     * Time: 16:28
     */
    public function getWPFCalendarPrice()
    {
        $pid   = I('post.pid', '', 'strval');
        $start = I('post.start', date('Y-m-d'));
        $end   = I('post.end', '');
        $start = date('Y-m-d', strtotime($start));

        $pid = explode('-', $pid);

        if (!$pid || !$start) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $tModel    = new Ticket();
        $ticketBiz = new \Business\Product\Ticket();

        // 最近的有价格日期
        $minDate = $ticketBiz->getHasRetailPriceDate($pid);
        if ($start < $minDate) {
            $start = $minDate;
        }

        // 设置时间段
        $beginDate = date('Y-m-d', strtotime($start));
        $tmp       = date('Y-m-01', strtotime($start));

        if (empty($end)) {
            $endDate = date('Y-m-d', strtotime("$tmp +1 month -1 day"));
        } else {
            $endDate = date('Y-m-d', strtotime($end));
        }

        $tidPidArr = $tModel->getTicketByPidArr($pid, 'id, pid');
        $tidArr    = implode(',', array_column($tidPidArr, 'id'));

        $priceSet     = [];
        $priceListArr = TicketApi::getLowerWindowPriceCalendar($tidArr, $beginDate, $endDate);

        if ($priceListArr[0] != 200) {
            $this->apiReturn(self::CODE_NO_CONTENT, [], $priceListArr[1]);
        }

        if (!empty($priceListArr[1])) {
            foreach ($priceListArr[1] as $key => $priceStorgeVal) {
                $priceSet[$priceStorgeVal['time']] = $priceStorgeVal['windowPrice'] / 100; // 窗口价，单位：元
            }
        }

        $this->apiReturn(self::CODE_SUCCESS, $priceSet);
    }

    /**
     * 微票房订单详情处理
     * Create by zhangyangzhen
     * Date: 2019/2/13
     * Time: 18:56
     *
     * @param $data
     *
     * @return mixed
     */
    private function _handleWPFOrderDetail($data)
    {
        $ordernum = $data['ordernum'];

        //下单方式配置
        $orderModeConf = load_config('order_mode', 'orderSearch');
        $payModeConf   = load_config('pay_mode', 'orderSearch');

        $data['orderMode'] = $orderModeConf[$data['ordermode']];

        $ticketModel = new Ticket('slave');
        $landModel   = new Land('slave');

        //获取产品和票类信息
        $ticket  = $ticketModel->getTicketInfoById($data['tid'], 'cancel_auto_onMin,title');
        $product = $landModel->getLandInfo($data['lid'], false, 'p_type,title,imgpath,apply_did');

        if ($product['p_type'] == 'B') {
            $orderTools = new OrderTools();
            $orderExtra = $orderTools->getOrderDetailInfo($ordernum);
            $javaApi    = new \Business\CommodityCenter\LandF();
            $ticketArr  = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([], [], [$data['pid']], 'ass_station');
            $extInfo    = $ticketArr[0];

            $station          = $extInfo['ass_station'] ? json_decode($extInfo['ass_station'], true) : [];
            $data['assembly'] = $station[$orderExtra['assembly']];
        }

        $data['payBtn']    = intval($data['status'] == 0 && $data['pay_status'] == 2);
        $data['cancelBtn'] = OrderList::isAllowCancel($ordernum, $data['member']);
        $data['payMode']   = ($data['pay_status'] == 2) ? '' : $payModeConf[$data['paymode']];
        $data['ttitle']    = $ticket['title'];
        $data['ltitle']    = $product['title'];
        $data['ptype']     = $product['p_type'];

        return $data;
    }
}
