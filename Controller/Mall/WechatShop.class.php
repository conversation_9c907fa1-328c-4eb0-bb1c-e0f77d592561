<?php

namespace Controller\Mall;

use Business\Member\SmallApp;
use Library\Controller;
use Model\Member\MemberRelationship;

/**
 * 微信门店controller
 *
 * @date 2017-05-09
 * <AUTHOR>
 */
class WechatShop extends Controller
{
    public function __construct()
    {

    }

    /**
     * 保存门店数据
     *
     * <AUTHOR>
     * @date   2017-05-09
     */
    public function save()
    {

        $sid = $this->isLogin();

        //门店数据列表
        $shopList = I('shop_list');

        if (!$shopList || !is_array($shopList)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $result = (new \Business\Mall\WechatShop())->save($sid, $shopList);

        if (isset($result['code'])) {

            $this->apiReturn($result['code'], [], $result['msg'] ?: '');

        } else {

            $this->apiReturn(204, [], '保存失败');

        }
    }

    /**
     * 获取门店列表
     *
     * <AUTHOR>
     * @date   2017-05-10
     *
     */
    public function getShopList()
    {
        $host = explode('.', $_SERVER['HTTP_HOST']);

        $requestSid = I('sid', 0, 'intval');
        if ($requestSid) {
            $sid = $requestSid;
        } else {
            if (is_numeric($host[0])) {
                $supply = (new \Model\Member\Member())->getMemberInfo($host[0], 'account', 'id');
                if (!$supply) {
                    $this->apiReturn(204, [], '参数错误');
                }
                $sid = $supply['id'];
            }
        }

        $result = (new \Business\Mall\WechatShop())->getShopList($sid);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '列表获取失败');
        }
    }

    /**
     * 删除一个微信门店
     *
     * <AUTHOR>
     * @date   2017-05-11
     */
    public function delete()
    {
        $sid = $this->isLogin();

        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        $result = (new \Business\Mall\WechatShop())->delete($sid, $id);

        if (isset($result['code'])) {

            $this->apiReturn($result['code'], $result['data'], $result['msg']);

        } else {

            $this->apiReturn(204, [], '删除失败');

        }
    }

    /**
     * 获取分销商列表
     *
     * <AUTHOR>
     * @date   2017-05-11
     */
    public function getDisList()
    {
        $sid = $this->isLogin();

        //关键字
        $keyword = I('keyword', '');
        //当前页
        $page = I('page', 1, 'intval');
        //每页条数
        $pageSize = I('page_size', 10, 'intval');


        $dname   = '';
        $letters = '';
        $mobile  = '';
        $account = '';
        if (is_numeric($keyword) && strlen($keyword) < 11) {
            $keyword            = str_replace("\_", "", $keyword);
            $account = $keyword;
        } else {
            $dname = $keyword;
        }

        $queryParams = [$sid, $dname, $letters, $mobile, $account, -1, $page, $pageSize];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberRelationQuery',
            'queryDistributorRelationSonIdsByParentId', $queryParams);

        $list = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $queryParams = [$queryRes['data']['list']];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                $queryParams);
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $list = $queryRes['data'];
            }
        }

        //$memModel = new MemberRelationship('slave');
        //$list     = $memModel->getResellers($sid, 1, false, true, $page, $pageSize, $keyword);
        //
        //$list = $list ?: [];

        $this->apiReturn(200, $list);
    }

    /**
     * 获取用户的小程序二维码
     * <AUTHOR>
     * @dateTime 2017-10-30T16:26:21+0800
     * @throws   Exception                              可能抛出异常
     * @return   [type]                   [description]
     */
    public function getPageAppCodeConfig() {
        $account  = I("account", 123624);
        $face     = I("face", 0);
        if($face) {
            $path=I("path","pages/typeface/typeface");
        } else {
            $path=I("path","pages/index/index");
        }
        $sceneCodeAndLandId = I('sceneCode', '', 'strval');  //小程序传个lid就跳转到lid的页面
        $memberId = $this->isLogin();
        $smallApp = new SmallApp();
        $data     = $smallApp->getPageAppCodeConfig($memberId, $path, $account,$sceneCodeAndLandId);
        $code     = $data['code'];
        unset($data['code']);
        $this->apiReturn($code, $data);
    }

}
