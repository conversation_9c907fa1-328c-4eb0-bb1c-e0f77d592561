<?php
/**
 * 全民营销后台管理
 * <AUTHOR>
 */

namespace Controller\Mall;

use Library\Controller;
use Business\Mall\AllDis as AllDisBiz;
use Business\Cooperator\AllDis\AllDis as AllDisBizCoo;

class AllDisManage extends Controller
{

    private $_sid;

    public function __construct()
    {
        $this->_sid = $this->isLogin('ajax');
    }

    /**
     * 获取订单列表
     * author  leafzl
     * Date: 2018-04-24
     */

    public function getDisOrder()
    {
        //当前页数
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');
        //时间
        $beginTime = I('beginTime', '', 'strtotime');
        $endTime   = I('endTime', '', 'strtotime') + 24 * 60 * 60 - 1;
        //订单号
        $orderId = I('orderId');
        //-1全部0未发放1已发放
        $type         = I('type', -1, 'intval');
        $parentRoleId = I('parentRoleId', 0, 'intval');

        $dis = new AllDisBiz();
        //获取订单列表
        $res = $dis->getDisOrder($this->_sid, $orderId, $beginTime, $endTime, $page, $size, $type, $parentRoleId);

        if ($res && $res['code'] == 200) {
            $this->apiReturn(200, $res['data']);
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);

        }

    }

    /**
     * 获取订单列表
     * author  leafzl
     * Date: 2018-04-24
     */

    public function getOldDisOrder()
    {
        //当前页数
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');
        //时间
        $beginTime = I('beginTime', '', 'strtotime');
        $endTime   = I('endTime', '', 'strtotime') + 24 * 60 * 60 - 1;
        //订单号
        $orderId = I('orderId');
        //-1全部0未发放1已发放
        $type         = I('type', -1, 'intval');
        $parentRoleId = I('parentRoleId', 0, 'intval');

        /*$dis = new AllDisBiz();
        //获取订单列表
        $res = $dis->getOldDisOrder($this->_sid, $orderId, $beginTime, $endTime, $page, $size, $type, $parentRoleId);*/

        $dis = new \Business\Cooperator\AllDis\AllDisOrder();
        $res = $dis->getOldDisOrder($this->_sid, $orderId, $beginTime, $endTime, $page, $size, $type, $parentRoleId);

        if ($res && $res['code'] == 200) {
            $this->apiReturn(200, $res['data']);
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

    }

    /**
     * 管理员根据订单号获取单个订单
     * author  weijie
     * Date: 2018-06-25
     */

    public function getDisOrderByNum()
    {

        //订单号
        $orderId = I('orderId');

        $super = $this->isSuper();
        if ($super) {
            if ($this->_sid != 1) {
                $this->apiReturn(500, [], '登录用户不合法');
            }
        }

        /*$dis = new AllDisBiz();
        //获取订单
        $result = $dis->getDisOrderByNum($orderId);*/

        $dis    = new \Business\Cooperator\AllDis\AllDisOrder();
        $result = $dis->getDisOrderByNum($orderId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 获取订单统计数据
     * author  leafzl
     * Date: 2018-4-25
     */
    public function getDisOrderStatic()
    {

        //时间
        $beginTime = I('beginTime', '', 'strtotime');
        $endTime   = I('endTime', '', 'strtotime') + 24 * 60 * 60 - 1;
        //订单号
        $orderId = I('orderId');

        $dis = new AllDisBiz();
        //获取订单列表
        $res = $dis->getDisOrderStatic($this->_sid, $orderId, $beginTime, $endTime);

        if ($res && $res['code'] == 200) {
            $this->apiReturn(200, $res['data']);
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);

        }

    }

    /**
     * 获取产品列表
     * <AUTHOR>
     * @date   2018-04-24
     */
    public function getProductList()
    {

        //搜索类型
        $type = I('type', 1, 'intval');
        //关键字
        $keyword = I('keyword', '');
        $page    = I('page', 1, 'intval');
        $size    = I('size', 10, 'intval');
        $pType   = I('ptype', '', 'strval');

        if (!$type || !$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->getProductList($this->_sid, $type, $page, $size, $keyword, $pType);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 门票展开
     * <AUTHOR>
     * @date   2018-04-26
     */
    public function showMoreTickets()
    {
        //产品id
        $lid = I('lid', 0, 'intval');
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        //查询类型
        $type = I('type', 1, 'intval');
        //以获取到的票的id
        $pids = I('pids', '', 'strval');

        $page = I('page', 1, 'intval');
        $size = I('size', 10, 'intval');

        if (!$lid || !$aid || !$type) {
            $this->apiReturn(204, [], '参数错误');
        }

        /*$allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->showMoreTickets($this->_sid, $aid, $lid, $type, $pids, $page, $size);*/
        $allDisBiz = new AllDisBizCoo();
        $result = $allDisBiz->showMoreTickets($this->_sid, $aid, $lid, $type, $pids, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 打开门票参与全民营销
     * <AUTHOR>
     * @date   2018-04-25
     */
    public function openTicket()
    {
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        //门票pid
        $pid = I('pid', 0, 'intval');

        if (!$aid || !$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $allDisBiz = new AllDisBizCoo();
        $result    = $allDisBiz->openTicket($this->_sid, $aid, $pid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 关闭门票参与全民营销
     * <AUTHOR>
     * @date   2018-04-25
     */
    public function closeTicket()
    {
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        //门票pid
        $pid = I('pid', 0, 'intval');

        if (!$aid || !$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $allDisBiz = new AllDisBizCoo();

        $result    = $allDisBiz->closeTicket($this->_sid, $aid, $pid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    ///**
    // * 更新tnum
    // * author  leafzl
    // * Date: 2018-4-25
    // * @return bool
    // */
    //public function updateTnumToDis()
    //{
    //    return false;
    //    set_time_limit(0);
    //    $allDisBiz = new AllDisBiz();
    //    $res       = $allDisBiz->updateTnumToDis();
    //}

    ///**
    // * 更新tnum
    // * author  leafzl
    // * Date: 2018-4-25
    // * @return bool
    // */
    //public function updatePidToDis()
    //{
    //    return false;
    //    set_time_limit(0);
    //    $allDisBiz = new AllDisBiz();
    //    $res       = $allDisBiz->updatePidToDis();
    //}

    ///**
    // * 更新aid
    // * author  leafzl
    // * Date: 2018-4-25
    // * @return bool
    // */
    //public function updateAidToDis()
    //{
    //    return false;
    //    set_time_limit(0);
    //    //$allDisBiz = new AllDisBiz();
    //    //$res       = $allDisBiz->updateAidToDis();
    //}

    /**
     * 单独设置产品佣金比例
     * <AUTHOR>
     * @date   2018-04-25
     */
    public function setTicketCommissionForLid()
    {

        $setMap = I('set_map', []);

        if (!is_array($setMap) || count($setMap['list']) < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->setTicketCommissionForLid($this->_sid, $setMap);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 单独设置门票佣金比例
     * <AUTHOR>
     * @date   2018-04-25
     */
    public function setTicketCommissionForPid()
    {

        $setMap = I('set_map', []);

        if (!is_array($setMap) || count($setMap['list']) < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        /*$allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->setTicketCommissionForPid($this->_sid, $setMap);*/

        $allDisBiz = new AllDisBizCoo();
        $result    = $allDisBiz->setTicketCommissionForPid($this->_sid, $setMap);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 红包状态检测，如果被退回，则重新发放
     * <AUTHOR>
     * @date   2018-09-03
     */
    public function redStatusCheck()
    {

        if (!$this->isSuper()) {
            $this->apiReturn(204, [], '无权操作');
        }
        //pft_all_dis_cash表的id
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], 'id缺失');
        }

        $allDisBiz = new AllDisBiz();
        $result    = $allDisBiz->redStatusCheck($id);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 转旧数据到pft_all_dis_summary表里（仅限一次）
     * author  leafzl
     * Date:  2018-11-22
     */
    public function summaryOldDate()
    {
        $alldisBiz = new  AllDisBiz();
        $alldisBiz->summaryOldDate();
    }

    /**
     * 保存营销说明
     * <AUTHOR>
     * @date   2018-11-27
     */
    public function saveDescription()
    {
        //0默认1自定义
        $type = I('type', 0, 'intval');
        //描述
        $description = I('description', [], '');

        if ($type && !$description) {
            $this->apiReturn(204, [], '请输入营销说明');
        }

        $alldisBiz = new  AllDisBiz();
        $result    = $alldisBiz->saveDescription($this->_sid, $type, $description);

        if (isset($result['code'])) {
            //清除缓存
            $alldisBiz->delAllDisConfigCache($this->_sid);
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取营销说明
     * <AUTHOR>
     * @date   2018-11-27
     */
    public function getDescription()
    {
        $alldisBiz = new  AllDisBiz();
        $result    = $alldisBiz->getDescription($this->_sid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 是否可以关闭全民营销
     * <AUTHOR>
     * @date   2018-11-27
     */
    public function closeWarning()
    {
        //接口迁移 逻辑都迁往cooperator
        /*$alldisBiz = new AllDisBiz();
        $result    = $alldisBiz->closeWarning($this->_sid);*/
        $allDisBiz = new \Business\Cooperator\AllDis\AllDis();
        $result = $allDisBiz->closeWarning($this->_sid);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取全名营销配置
     *
     * <AUTHOR>
     * @date   2019/11/15
     */
    public function getAllDisConfig()
    {
        $sid        = $this->_sid;
        $allDisMode = new \Business\Mall\AllDis();
        $res        = $allDisMode->getAllDisConfig($sid, 'config, auto_issue');
        if ($res) {
            $res['config']                  = json_decode($res['config'], true);
            $res['config']['scale_setting'] = empty($res['config']['scale_setting']) ? [] : json_decode($res['config']['scale_setting'],
                true);
        }
        $this->apiReturn(200, $res, "获取成功");
    }

    /**
     * 更新/保存全民营销配置
     *
     * <AUTHOR>
     * @date   2019/11/15
     */
    public function saveAllDisConfig()
    {
        $sid            = $this->_sid;
        $developPer     = I('post.develop_per', 0, 'strval');
        $recommendPer   = I('post.recommend_per', 0, 'strval');
        $returnPer      = I('post.return_per', 0, 'strval');
        $isSeckillJoin  = I('post.is_seckill_join', 0, 'intval');
        $productSetting = I('post.product_setting', 0, 'intval');
        $autoIssue      = I('post.auto_issue', 0, 'intval');
        $scaleSetting   = I('post.scale_setting', '', 'strval');
        $type           = I('post.type', 0, 'intval');
        if (($developPer + $recommendPer + $returnPer) > 100) {
            $this->apiReturn(203, [], "佣金百分比设置大于100%");
        }
        $config     = [
            'develop_per'     => $developPer,
            'recommend_per'   => $recommendPer,
            'return_per'      => $returnPer,
            'platform_per'    => 0,        //平台佣金暂定为0
            'is_seckill_join' => $isSeckillJoin,
            'type'            => $type,
            'scale_setting'   => $scaleSetting,
            'product_setting' => $productSetting,
        ];
        $data       = [
            'config'     => json_encode($config),
            'auto_issue' => $autoIssue,
        ];
        $allDisMode = new \Model\Mall\AllDis();
        $res        = $allDisMode->saveAllDisConfig($sid, $data);
        if ($res === false) {
            $this->apiReturn(400, [], "更新失败");
        }

        //清除缓存
        $allDisBiz = new \Business\Mall\AllDis();
        $allDisBiz->delAllDisConfigCache($sid);
        $this->apiReturn(200, [], "更新成功");
    }
}
