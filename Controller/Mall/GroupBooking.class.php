<?php

/**
 * 拼团c端控制器
 * <AUTHOR>
 */

namespace Controller\Mall;

class GroupBooking extends Mall
{

    /**
     * 获取进行中或者即将开始的拼团活动
     * <AUTHOR>
     * @date   2018-05-22
     */
    public function getMallActivityList()
    {

        $page = I('page', 1, 'intval');
        $size = I('size', 10, 'intval');

        if (!$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($size > 20) {
            $this->apiReturn(204, [], '每页条数超出限制');
        }

        $openid = I('session.openid', '');

        $groupBiz = new \Business\Wechat\GroupBooking();
        $result   = $groupBiz->getMallActivityList($this->_supplyId, $page, $size, $openid);

        if (isset($result)) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取拼团商品详情
     * <AUTHOR>
     * @date   2018-05-23
     */
    public function getGoodsDetail()
    {
        //拼团活动id
        $id = I('id', 0, 'intval');
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        if (!$id || !$aid) {
            $this->apiReturn(204, [], '参数错误');
        }
        $mid = $this->isLogin('ajax');

        $openid = I('session.openid', '');

        $groupBiz = new \Business\Wechat\GroupBooking();
        $result = $groupBiz->getGoodsDetail($mid, $this->_supplyId, $id, $aid, $openid, 1);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取拼团活动详情（开团和凑团）
     * <AUTHOR>
     * @date   2018-05-24
     */
    public function getGroupDetail()
    {

        $mid = $this->isLogin('ajax');
        //活动id
        $id = I('id', 0, 'intval');
        //开团id
        $joinId = I('join_id', 0, 'intval');
        //上级供应商id
        $aid = I('aid', 0, 'intval');

        if (!$id || !$aid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $openid = I('session.openid', '');

        $groupBiz = new \Business\Wechat\GroupBooking();
        $result = $groupBiz->getGroupDetail($this->_supplyId, $mid, $id, $aid, $joinId, $openid, 1);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 可能喜欢的拼团活动
     * <AUTHOR>
     * @date   2018-05-30
     */
    public function probablyLikeIt()
    {
        //拼团活动id
        $id = I('id', 0, 'intval');
        //获取条数
        $size = I('size', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数错误');
        }

        $groupBiz = new \Business\Wechat\GroupBooking();
        $result   = $groupBiz->probablyLikeIt($id, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取我的拼团列表
     * author  leafzl
     * Date: 2018-05-28
     */

    public function getMyGroupList()
    {

        $mid  = $this->isLogin('ajax');
        $page = I('page', 1);
        $size = I('size', 5);

        $groupBiz = new \Business\Wechat\GroupBooking();
        $result   = $groupBiz->getMyGroupList($mid, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }
    /**
     * 是否关注了公众号
     * <AUTHOR>
     * @date   2018-05-30
     */
    public function hasAttention()
    {

        //微信openid
        $openid = I('session.openid', '');

        if (I('session.identify') == 'allDis' && I('session.supply_openid')) {
            $openid = I('session.supply_openid');
        }
        if (!$openid) {
            $this->apiReturn(200, [], '已关注');
        }

        $query   = $_SERVER['HTTP_REFERER'];
        $urlInfo = parse_url($query);
        parse_str($urlInfo['query'], $query);
        $id = $query['id'];

        if ($id < 1) {
            $this->apiReturn(204, [], '来源错误');
        }

        $groupBiz = new \Business\Wechat\GroupBooking();
        $result   = $groupBiz->hasAttention($id, $openid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 设置拼团活动提醒
     * <AUTHOR>
     * @date   2018-05-30
     */
    public function setWarning()
    {
        //拼团活动id
        $id = I('id', 0, 'intval');
        //微信openid
        $openid = I('session.openid', '');
        if (I('session.identify') == 'allDis' && I('session.supply_openid')) {
            $openid = I('session.supply_openid');
        }

        $groupBiz = new \Business\Wechat\GroupBooking();
        $result   = $groupBiz->setWarning($id, $openid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取参团记录
     * <AUTHOR>
     * @date   2018-05-30
     */
    public function getCollectRecords()
    {
        $mid = $this->isLogin('ajax');

        $page   = I('page', 1, 'intval');
        $size   = I('size', 5, 'intval');
        $openId = I('open_id', 0, 'intval');

        $groupBiz = new \Business\Wechat\GroupBooking();
        $result   = $groupBiz->getCollectRecords($openId, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

}
