<?php

/**
 * 改签相关
 * <AUTHOR>  Li
 * @date  2021-08-26
 */

namespace Controller\Mall;


use Business\Order\Modify;
use Business\Order\ShowModify;

class TicketChanging extends Mall
{
    /**
     * 订单改签
     * <AUTHOR>  Li
     * @date  2021-08-26
     */
    public function ticketChanging()
    {
        $orderNum       = I('post.ordernum', '', 'strval');         //订单号
        $sectionTimeStr = I('post.section_time_str', '', 'strval');  //分时预约时间段
        $playTime       = I('post.play_date', '', 'strval');         //预约游玩时间
        $sectionTimeId  = I('post.section_time_id', 0, 'intval');    //分时预约时间段Id
        if (!$orderNum) {
            $this->apiReturn(204, [], '订单号不能为空');
        }
        if (!strtotime($playTime)) {
            $this->apiReturn(204, [], '预约时间错误');
        }
        $modifyBz = new Modify();
        $result   = $modifyBz->ticketChanging($orderNum, $playTime, $this->_supplyId, $this->_supplyId, 22, $sectionTimeId,
            $sectionTimeStr, true);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 演出订单改签
     * <AUTHOR>  Li
     * @date   2021-08-18
     */
    public function showTicketChanging()
    {
        $ordernum   = I('post.ordernum', '', 'strval');     //订单号
        $changeTime = I('post.play_date', '', 'strval');    //改签日期
        $venueId    = I('post.venue_id', 0, 'intval');      //演出场馆id
        $aid        = I('post.aid', 0, 'intval');           //演出门票上级供应商id
        $roundId    = I('post.round_id', 0, 'intval');       //演出场次id
        $seats      = I('post.seat_ids', '', 'strval');       //演出座位id 多个以逗号隔开

        if (!$ordernum || !$changeTime || !$aid || !$venueId || !$roundId) {
            $this->apiReturn(204, [], '缺少参数');
        }
        $seatIdList = explode(',', $seats);

        $source   = 16;
        $orderBiz = new ShowModify();
        $res      = $orderBiz->showTicketChanging($ordernum, $changeTime, $aid, $aid, $venueId, $roundId, $this->_supplyId,
            $source, $seatIdList, true);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 获取演出改签库存信息
     * <AUTHOR>  Li
     * @date   2021-08-26
     */
    public function getShowInfoList()
    {
        $aid       = I('post.aid', 0, 'intval');           //演出门票上级供应商id
        $venues    = I('post.venues', '', 'strval');
        $date      = I('post.date', '', 'strval');
        $isReserve = I('post.is_reserve', 0, 'strval');    //是否是预约获取库存

        if (! $aid || !$venues || !$date) {
            $this->apiReturn(203, [], '参数错误');
        }

        $orderBiz = new ShowModify();
        $result  = $orderBiz->getShowInfoList($aid, $venues, $date, $isReserve);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 预定页面 - 获取价格和库存
     * <AUTHOR>
     * @time   2017-01-10
     *
     * @param  string   pids   产品ID集合 用-分隔 例如 11138-11139
     * @param  int      aid    上级分销商ID
     * @param  date     date   日期 '2017-01-20'
     *
     * @return  ['11138'=>['price'=>1.58,'store'=>100],'11139'=>[..]]
     */
    public function getPriceAndStorage()
    {
        $aid  = I('aid', '', 'intval');
        $date = I('date', date('Y-m-d'));
        $tids = I('tids');

        $tidArr = explode('-', $tids);
        if (empty($tidArr[0])) {
            $this->apiReturn(201, [], '未指定产品');
        }

        if (!$tidArr || !$date) {
            $this->apiReturn(201, [], '参数错误');
        }

        $date = date('Y-m-d', strtotime($date));
        if ($date < date('Y-m-d')) {
            $this->apiReturn(202, '请选择正确的日期');
        }

        $ticketStr = implode(',', $tidArr);

        $priceKey = 'settlement';

        $tPriceBiz          = new \Business\Product\Price();
        $priceAndStorageRes = $tPriceBiz->buyBatchGet($aid, $aid, $date, $ticketStr, true);

        if ($priceAndStorageRes['code'] != self::CODE_SUCCESS || empty($priceAndStorageRes['data'])) {
            $this->apiReturn(208, [], '获取价格出错，请稍后重试');
        }
        $priceAndStorage = $priceAndStorageRes['data'];

        // 先利用tidArr获取pidArr
        $ticketModel = new \Model\Product\Ticket();
        $tidPidArr   = $ticketModel->getMuchTicketInfo($tidArr, 'id, pid, apply_did, is_agreement_ticket');
        //获取分时预约
        $timeShareOrderBz = new \Business\Order\TimeShareOrder();
        $ticketShareRes   = $timeShareOrderBz->getTimeSlicesWithTidArr($tidArr, $date, 10);   //微平台销售渠道为10
        if ($ticketShareRes['code'] != 200) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分时数据获取错误！');
        }
        $ticketShareInfo = $ticketShareRes['data'];
        $return          = [];
        foreach ($priceAndStorage as $key => $val) {

            $pid = $tidPidArr[$key]['pid'];

            $return[$pid]                    = [
                'price'  => $val[$priceKey] != -1 ? $val[$priceKey] / 100 : 0,  //-1表示无价格
                'store'  => $val['availableStorage'] >= -1 ? $val['availableStorage'] : 0,
                'retail' => $val['retail'] != -1 ? $val['retail'] / 100 : 0,  //-1表示无价格
                'ticketId'    => $key
            ];
            $return[$pid]['time_share_info'] = $ticketShareInfo[$key] ?? [];
        }

        //协议票库存处理
        $storageLib = new \Business\AgreementTicket\Storage();
        foreach ($return as &$value) {
            $fid = $aid;
            $isAgreement = $tidPidArr[$value['ticketId']]['is_agreement_ticket'] ?? 0;
            $applyDid = $tidPidArr[$value['ticketId']]['apply_did'] ?? 0;

            if (!$isAgreement || $fid == $applyDid) {
                continue;
            }

            $sid = $aid;
            $pid = $tidPidArr[$value['ticketId']]['pid'] ?? 0;
            $time = strtotime($date);
            $storageRes = $storageLib->queryStorageByOrderPage($fid, $sid, $pid, $time);
            if ($storageRes['code'] != 200 || empty($storageRes['data'])) {
                continue;
            }

            if ($storageRes['data']['storage'] == -1) {
                continue;
            }

            $value['store'] = $storageRes['data']['storage'];
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 获取日历价格接口
     * <AUTHOR>  Li
     * @date  2021-09-02
     */
    public function getCalendarData()
    {
        $ticketId = I('post.ticket_id', '', 'intval');
        $month    = I('post.month', '', 'strval');
        $aid      = I('post.aid', '', 'intval');
        $fsid     = I('post.fsid', '', 'intval');

        if (!$ticketId || !$month || !$aid) {
            $this->apiReturn(201, [], '参数有误');
        }

        $startDate = $month . '-01';
        $endDate   = $month . date('-t', strtotime($month));

        $memberId = $aid;

        //日历模式的请求就是自己的
        if ($aid == -1) {
            $aid = $this->_supplyId;
        }

        if ($fsid) {
            $memberId = $fsid;
        }


        $tPriceBiz = new \Business\Product\Price();
        $calendar  = $tPriceBiz->buyGetCalendar($memberId, $aid, $startDate, $endDate, $ticketId, true);

        if ($calendar['code'] == self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_SUCCESS, $calendar['data'], 'success');
        } else {
            $this->apiReturn(self::CODE_NO_CONTENT, [], '暂无日历信息');
        }
    }
}
