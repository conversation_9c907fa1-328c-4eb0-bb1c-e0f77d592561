<?php
/**
 * 郑州魔方园一卡通
 */

namespace Controller\Mall;

use Business\JavaApi\Fund\MemberShipCard;
use Business\JsonRpcApi\MessageService\MessageService;
use Library\Business\Xcrypt;
use Library\Container;
use Library\MessageNotify\MessageException;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Tools\Helpers;
use Library\Cache\Cache;
use Library\TouTiao\Core;
use Library\Util\EnvUtil;
use Model\CardSolution\Journal;
use Model\CardSolution\RechargeScheme;
use Model\CardSolution\Refund;
use Model\Member\Member;
use Model\Order\OrderHandler;
use Model\Order\OrderTrack;
use Model\Order\OrderTools;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\TradeRecord\OnlineTrade;

class CardSolution extends Mall
{

    private $_memberModel;
    private $_cardMemberModel;
    private $_refundModel;
    private $_refundJournalModel;
    private $_cardConfigModel;
    private $_orderQueryModel;
    private $_orderHandleModel;
    private $_trackModel;

    //对称加密的key 为8/16/24 的size的
    private $_key = 'ykt12345';

    private function _getMemberModel()
    {
        if (empty($this->_memberModel)) {
            $this->_memberModel = new Member();
        }

        return $this->_memberModel;
    }

    private function _getCardMemberModel()
    {
        if (empty($this->_cardMemberModel)) {
            $this->_cardMemberModel = new \Model\CardSolution\Member();
        }

        return $this->_cardMemberModel;
    }

    private function _getRefundModel()
    {
        if (empty($this->_refundModel)) {
            $this->_refundModel = new Refund();
        }

        return $this->_refundModel;
    }

    private function _getRefundJournal()
    {
        if (empty($this->_refundJournalModel)) {
            $this->_refundJournalModel = new Journal();
        }

        return $this->_refundJournalModel;
    }

    private function _getCardConfigModel()
    {
        if (empty($this->_cardConfigModel)) {
            $this->_cardConfigModel = new \Model\CardSolution\Config();
        }

        return $this->_cardConfigModel;
    }

    private function _getOrderQueryModel()
    {
        if (empty($this->_orderQueryModel)) {
            $this->_orderQueryModel = new \Model\Order\OrderQuery();
        }

        return $this->_orderQueryModel;
    }

    private function _getOrderHandleModel()
    {
        if (empty($this->_orderHandleModel)) {
            $this->_orderHandleModel = new OrderHandler();
        }

        return $this->_orderHandleModel;
    }

    private function _getTrackModel()
    {
        if (empty($this->_trackModel)) {
            $this->_trackModel = new OrderTrack();
        }

        return $this->_trackModel;
    }

    /**
     * 会员中心
     *
     * <AUTHOR>
     * @date   2018-04-13
     */
    public function userCenter()
    {
        //登陆用户
        $memberId = $this->isLoginCardSolution();
        //供应商ID
        $applyId = $this->_supplyId;

        if (empty($applyId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $memberModel = $this->_getMemberModel();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname, headphoto');
        $userInfo    = $memberModel->getMemberInfo($memberId, 'id', 'dname, headphoto');
        //供应商名字
        $applyName = $memberInfo['dname'];
        $headPhoto = $userInfo['headphoto'];

        //会员卡号
        $cardMemberModel = $this->_getCardMemberModel();
        $cardMemberInfo  = $cardMemberModel->getMemberInfoBySidAndFid($memberId, $applyId, 'card_no, mobile');
        $cardNo          = isset($cardMemberInfo['card_no']) ? $cardMemberInfo['card_no'] : '';
        $mobile          = isset($cardMemberInfo['mobile']) ? $cardMemberInfo['mobile'] : '';

        //余额
        $memberMoney = $memberModel->getMoney($memberId, 3, $applyId, true);
        $kMoney      = isset($memberMoney['kmoney']) ? $memberMoney['kmoney'] : 0;
        $baseCredit  = isset($memberMoney['basecredit']) ? $memberMoney['basecredit'] : 0;
        $balance     = $kMoney + $baseCredit;

        $cardConfigModel = $this->_getCardConfigModel();
        $cardRoutineInfo = $cardConfigModel->getRoutineInfo($applyId, 'id, phone_num');
        $applyPhone      = isset($cardRoutineInfo['phone_num']) ? $cardRoutineInfo['phone_num'] : '';

        //生成付款码
        $paymentCode = $this->_getPaymentCode($memberId, $applyId);

        $data = [
            'apply_name'  => $applyName,
            'card_no'     => $cardNo,
            'balance'     => $balance,
            'pay_code'    => $paymentCode,
            'head_photo'  => $headPhoto,
            'mobile'      => $mobile,
            'apply_phone' => $applyPhone,
        ];

        $this->apiReturn(200, $data);
    }

    /**
     * 提现
     * <AUTHOR>
     * @date   2018-04-13
     */
    public function withdraw()
    {
        //登陆用户
        $memberId = $this->isLoginCardSolution();
        //供应商ID
        $applyId = $this->_supplyId;
        //先判断能不能提现
        $refundModel = $this->_getRefundModel();
        //提现金额
        $money = I('post.money', 0);
        $money = $money * 100;

        $canRefund = $refundModel->getRefundConfigByFidAndSid($memberId, $applyId);

        if (!$canRefund) {
            $this->apiReturn(403, [], '该账号已经使用优惠充值, 不能提现');
        }

        $memberModel = new Member();
        $balance     = $memberModel->balanceAndCredit($memberId, $applyId, true);
        if ($money > $balance) {
            $this->apiReturn(403, [], '超过提现额度');
        }

        $orderNum = 'ykt_' . str_replace('.', '', microtime(true));
        $res      = $refundModel->insertRefundJournal($memberId, $applyId, $orderNum, $money);
        if (empty($res)) {
            $this->apiReturn(403, [], '申请提现错误，请重试，错误代码：202');
        }

        $subjectCode   = \Library\Constants\Account\BookSubject::CREDIT_SUBJECT_CODE;
        $MemberShipBiz = new MemberShipCard();
        $decRes        = $MemberShipBiz->creditWithdraw($memberId, $memberId, $applyId, $money, $orderNum, $subjectCode,
            '');
        if (empty($decRes) || $decRes['code'] != 200) {
            $this->apiReturn(403, [], '退还授信失败:' . $decRes['msg']);
        }

        //加入购物清单
        $journalModel = $this->_getRefundJournal();
        $journalData  = [
            'type'        => 3,
            'fid'         => $memberId,
            'sid'         => $applyId,
            'operid'      => $memberId,
            'create_time' => time(),
            'ordernum'    => $orderNum,
            'money'       => $money,
        ];
        $res          = $journalModel->insertData($journalData);
        if (empty($res)) {
            $this->apiReturn(403, [], '申请提现失败，请重试，错误代码：201');
        }

        $this->apiReturn(200);
    }

    /**
     * 微商城会员卡购票 回调
     *
     * <AUTHOR>
     * @date   2018-04-24
     */
    public function wxCardSolutionBuy()
    {
        $outTradeNo = I('post.out_trade_no');
        $biz        = new \Business\Order\MergeOrder();
        //是否合并付款
        $isCombine = false;

        if ($biz->isCombineOrder($outTradeNo)) {
            // 获取关联的订单号
            $model     = new \Model\Order\Payment();
            $orderList = $model->getCombileLogByTradeId($outTradeNo);
            if (empty($orderList)) {
                $this->apiReturn(403, '未找到合并付款订单');
            }

            if ($orderList['pay_status'] == 1) {
                $this->apiReturn(403, '订单已经付款');
            }

            //获取合并付款的所有订单
            $orderNumArr = array_column($orderList, 'ordernum');
            $isCombine   = true;
        } else {
            $orderNumArr = [$outTradeNo];
        }

        $result = $this->_handleCombineOrders($orderNumArr, $isCombine, $outTradeNo);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 会员卡支付合并付款
     *
     * @return
     */
    private function _handleCombineOrders($orderNumArr, $isCombine, $combineTradeId)
    {
        $memberId       = $this->isLogin('ajax');
        $supplyId       = $this->_supplyId;
        $model          = new \Model\Order\Payment();
        $orderToolModel = new \Model\Order\OrderTools();

        //获取订单的联票订单
        $linkOrderInfo    = $orderToolModel->getLinkSubOrderInfo($orderNumArr, 'orderid, concat_id');
        $orderLinkNumArr  = array_column($linkOrderInfo, 'orderid');
        $orderNumArr      = array_unique(array_merge($orderNumArr, $orderLinkNumArr));
        $linkMainOrderNum = [];
        $linkMainRelation = [];
        $linkSunOrderNum  = [];
        foreach ($linkOrderInfo as $value) {
            //联票主票
            $linkMainOrderNum[] = $value['concat_id'];
            //联票主票包含的子票 包括自己
            $linkMainRelation[$value['concat_id']][] = $value['orderid'];
            //联票子票
            if ($value['concat_id'] != $value['orderid']) {
                $linkSunOrderNum[] = $value['orderid'];
            }
        }

        $orderTools = new OrderTools();
        $orderInfo  = $orderTools->getOrderListNew($orderNumArr,
            'member, aid, ordernum, tid, lid, tnum, tprice, pay_status');

        if (empty($orderInfo)) {
            return ['code' => 403, 'msg' => '未找到订单信息'];
        }

        $lidArr = array_column($orderInfo, 'lid');
        //$landModel = new Land();
        //$landInfo  = $landModel->getLandInfoByMuli($lidArr, 'id, p_type');

        $javaAPi  = new \Business\CommodityCenter\Land();
        $landInfo = $javaAPi->queryLandMultiQueryById($lidArr);
        foreach ($landInfo as $value) {
            if ($value['p_type'] != 'A') {
                return ['code' => 403, 'msg' => '不是景区类型'];
            }
        }

        $orderRes = [];
        foreach ($orderInfo as $value) {
            if ($value['pay_status'] == 1) {
                return ['code' => 403, 'msg' => '订单已经支付'];
            }
            if ($value['member'] != $memberId || $value['aid'] != $supplyId) {
                return ['code' => 403, 'msg' => '订单信息错误, 不属于该用户'];
            }
            $orderRes[$value['ordernum']] = $value;
        }

        //判断是否开通了会员卡
        $cardMemberModel = new \Model\CardSolution\Member();
        $cardInfo        = $cardMemberModel->getMemberInfoBySidAndFid($memberId, $supplyId);

        if (empty($cardInfo)) {
            return ['code' => 403, 'msg' => '未在该景区开通会员卡'];
        }

        //获取票类折扣信息
        $tidArr      = array_column($orderInfo, 'tid');
        $ticketModel = new Ticket();
        $ticketInfo  = $ticketModel->getTicketList($tidArr, 'id, discount_scale, apply_did');
        foreach ($ticketInfo as $value) {
            if ($value['apply_did'] != $this->_supplyId) {
                //非直接供应商不能使用会员卡
                return ['code' => 403, 'msg' => '非供应商直接供应产品不能使用会员卡'];
            }
            $ticketRes[$value['id']] = $value['discount_scale'];
        }

        //求出所有账单
        $money = 0;
        foreach ($orderRes as $value) {
            if (isset($ticketRes[$value['tid']])) {
                $discount = $ticketRes[$value['tid']];
                if (!empty($discount)) {
                    $money += ceil($value['tprice'] * $value['tnum'] * $discount / 100);
                } else {
                    $money += ceil($value['tprice'] * $value['tnum']);
                }
            }
        }

        //余额
        $memberModel = $this->_getMemberModel();
        $balance     = $memberModel->balanceAndCredit($memberId, $this->_supplyId, true);
        if ($money > $balance) {
            return ['code' => 403, 'msg' => '会员卡余额不足'];
        }

        //求出所有订单的
        $successOrders = $failOrders = [];
        foreach ($orderRes as $value) {
            //修改有打折商品的价格 每个有打折的订单都要改 包含联票子票
            if (isset($ticketRes[$value['tid']])) {
                $discount = $ticketRes[$value['tid']];
                if (!empty($discount)) {
                    $tmpPrice      = ceil($value['tprice'] * $discount / 100);
                    $tmpTotalMoney = ceil($value['tnum'] * $value['tprice'] * $discount / 100);
                    $res           = $this->_getOrderHandleModel()->UpdateOrderPrice($value['ordernum'], $tmpPrice,
                        $tmpTotalMoney, $memberId);

                    if (empty($res)) {
                        pft_log('card_solution/error', '订单号：' . $value['ordernum'] . '修改价格失败');
                    }
                }
            }

            if (in_array($value['ordernum'], $linkSunOrderNum)) {
                //如果是联票子票 不进行下面的操作
                continue;
            }

            $tmpTotalMoney = 0;
            if (in_array($value['ordernum'], $linkMainOrderNum)) {
                //联票主票 需要计算所有子票的价格
                if (isset($linkMainRelation[$value['ordernum']])) {
                    foreach ($linkMainRelation[$value['ordernum']] as $item) {
                        $tmpTid = $orderRes[$item]['tid'];
                        if (isset($ticketRes[$tmpTid])) {
                            $discount = $ticketRes[$tmpTid]['discount_scale'];
                            if (!empty($discount)) {
                                $tmpTotalMoney += ceil($value['tprice'] * $value['tnum'] * $discount / 100);
                            } else {
                                $tmpTotalMoney += ceil($value['tprice'] * $value['tnum']);
                            }
                        }
                    }
                }
            } else {
                //普通票
                $tmpTid   = $value['tid'];
                $discount = isset($ticketRes[$tmpTid]) ? $ticketRes[$tmpTid] : 0;
                if (!empty($discount)) {
                    $tmpTotalMoney = ceil($value['tprice'] * $value['tnum'] * $discount / 100);
                } else {
                    $tmpTotalMoney = ceil($value['tprice'] * $value['tnum']);
                }
            }

            $return = $this->_orderAfterConSolution($value['ordernum'], $tmpTotalMoney, $memberId, $supplyId);
            if ($return['code'] == 200) {
                $successOrders[] = $value['ordernum'];
                // 写用户清单记录
                $cardJourData   = [
                    'type'        => 1,
                    'fid'         => $memberId,
                    'sid'         => $supplyId,
                    'create_time' => time(),
                    'ordernum'    => $value['ordernum'],
                    'money'       => $tmpTotalMoney,
                ];
                $cardJournalRes = $this->_getRefundJournal()->insertData($cardJourData);
                if (empty($cardJournalRes)) {
                    pft_log('card_solution/error', '订单号：' . $value['ordernum'] . '写交易清单失败');
                }
            } else {
                $failOrders[] = $value['ordernum'];
            }
        }

        if ($isCombine) {
            if (count($successOrders)) {
                $model->changeCombinePayStatus($combineTradeId, $successOrders, 1);
            }
            if (count($failOrders)) {
                $model->changeCombinePayStatus($combineTradeId, $failOrders, 2);
            }
        }

        return ['code' => 200, 'data' => $successOrders];
    }

    /**
     * 会员卡支付 后的 处理
     *
     * <AUTHOR>
     * @date   2018-04-24
     */
    private function _orderAfterConSolution($orderNum, $totalMoney, $memberId, $supplyId)
    {
        //$orderModel = $this->_getOrderQueryModel();
        $orderBiz  = new \Business\Order\Query();
        $orderData = $orderBiz->getSimpleOrderInfo($orderNum);

        $memberModel = $this->_getMemberModel();
        if (!$orderData['mainOrder']['ordernum']) {
            return ['code' => 403, 'msg' => '订单号：' . $orderNum . '订单号不存在'];
        }

        if ($orderData['mainOrder']['paystatus'] != 2) {
            return ['code' => 403, 'msg' => '订单号：' . $orderNum . '订单已支付'];
        }

        //授信扣款 交易记录
        $tradeBiz = new \Business\JavaApi\Fund\Trade();
        $tradeRes = $tradeBiz->balancePay($orderNum, 0, $memberId, $supplyId, $totalMoney, $payType = 2,
            $remark = '会员卡购票', [], 'merbercard_' . $orderNum);

        if (empty($tradeRes) || $tradeRes['code'] != 200) {
            return ['code' => 403, 'msg' => '订单号：' . $orderNum . '扣款失败'];
        }

        //修改订单状态
        $res = $this->_getOrderHandleModel()->UpdatePayStatus($orderNum, 1, 17, $memberId);
        if (empty($res)) {
            pft_log('card_solution/error', '订单号：' . $orderNum . '修改状态失败');
        }

        //主票加追踪记录
        $trackModel = $this->_getTrackModel();
        $res        = $trackModel->addTrack($orderNum, \Model\Order\OrderTrack::ORDER_PAY,
            $orderData['mainOrder']['tid'],
            $orderData['mainOrder']['tnum'], $orderData['mainOrder']['tnum'],
            35, 0, 0, '', $orderData['mainOrder']['member']);
        if (empty($res)) {
            pft_log('card_solution/error', '订单号：' . $orderNum . '添加追踪表失败');
        }

        //子票加追踪记录
        if (count($orderData['childOrder'])) {
            foreach ($orderData['childOrder'] as $childOrder) {
                $res = $trackModel->addTrack($childOrder['ordernum'], \Model\Order\OrderTrack::ORDER_PAY,
                    $childOrder['tid'],
                    $childOrder['tnum'], $childOrder['tnum'], 35);
                if (empty($res)) {
                    pft_log('card_solution/error', '订单号：' . $orderNum . '添加追踪表失败');
                }
            }
        }

        return ['code' => 200];
    }

    /**
     * 生成二维码
     * <AUTHOR>
     * @date   2018-04-15
     */
    private function _getPaymentCode($memberId, $applyId)
    {
        $jsonArr = [
            'member_id' => $memberId,
            'apply_did' => $applyId,
            'time'      => time(),
        ];

        $jsonStr = json_encode($jsonArr);
        $xCrypt  = new Xcrypt($this->_key);
        $jsonStr = $xCrypt->encrypt($jsonStr);

        return $jsonStr;
    }

    /**
     * 生成用户
     *
     * @param  int phoneNum   手机号
     * @param  string vCode   手机验证码
     * @param  int sid        园区
     *
     */
    public function bindCardMember()
    {
        $phoneNum  = I('phoneNum');
        $vCode     = I('vCode');
        $avatarUrl = I('avatarUrl', '');
        $scenCode  = I('scenCode', '');
        $sid       = $this->_supplyId;

        if (empty($sid) || empty($phoneNum) || empty($scenCode)) {
            $this->apiReturn(203, [], '参数有误');
        }

        //手机验证码验证
        // $isLegal = Helpers::ChkCode($phoneNum, $vCode);
        // if($isLegal !== true && ENV == 'PRODUCTION') {
        //     $this->apiReturn(204, [], '验证码错误');
        // }

        // 线上的先限制用户
        if (ENV == 'PRODUCTION' && !in_array($sid, [3385, 6864, 915059, 705315, 3371536])) {
            $this->apiReturn(204, [], '暂未对该园区开放');
        }

        $isSmallApp = \inWechatSmallApp();
        if ($isSmallApp) {
            $openId  = $this->_smallOpenid;
            $unionid = $this->_unionid;
        } else {
            $this->apiReturn(204, [], '请在小程序中使用该功能');
        }

        $cardMemberBiz = new \Business\CardSolution\Member();
        $bindRes       = $cardMemberBiz->bindCardSolution($phoneNum, $openId, $sid, $unionid, $avatarUrl);

        if ($bindRes['code'] == 200) {
            $msg = isset($bindRes['msg']) ? $bindRes['msg'] : '绑定园区成功';
            $this->apiReturn(200, [], $msg);
        }

        $this->apiReturn(204, [], $bindRes['msg']);
    }

    /**
     * 判断是否郑州园区一卡通用户
     *
     * @param  int unionid  同一开放平台唯一id
     * @param  int sid      园区id
     *
     * @return string | json
     *
     */
    public function isCardSolutionUser()
    {
        $isScanCode = I('scenCode', 0);

        // 如果不是选择特定得园区则返回用户所有得
        if ($isScanCode) {
            $sid = $this->_supplyId;
        } else {
            $sid = '';
        }

        $openId = '';
        if ($this->inWechatSmallApp()) {
            $openId = $this->_smallOpenid;
        }

        if ($openId == '') {
            $this->apiReturn(203, [], '参数有误');
        }

        $cardMemberModel = $this->_getCardMemberModel();
        $cardSolutionArr = $cardMemberModel->getInfoByOpenId($openId, $sid, 'sid,fid,mobile');

        if (empty($cardSolutionArr)) {
            $this->apiReturn(200, [], '无园区信息');
        }

        $sidArr          = array_column($cardSolutionArr, 'sid');
        $memModel        = $this->_getMemberModel();
        $memberIdInfoArr = $memModel->getMemberInfoByMulti($sidArr, $column = 'id', $field = 'id, account, dname',
            true);

        $data = [];
        foreach ($cardSolutionArr as $key => $memVal) {
            $data['list'][$key]             = $memVal;
            $data['list'][$key]['scenCode'] = $this->_createScenCode($memVal['sid']);
            $data['list'][$key]['name']     = $memberIdInfoArr[$memVal['sid']]['dname'];
            $data['list'][$key]['account']  = $memberIdInfoArr[$memVal['sid']]['account'];
        }

        $this->apiReturn(200, $data, '已有园区一卡通账户');
    }

    /**
     * 发送注册短信验证码
     * http://my.12301.local/r/Member_Register/regVcode/
     *
     * @param  int $mobile 手机号
     *
     * @return string | json
     *
     */
    public function sendVcode()
    {
        $mobile = I('post.mobile');
        //手机号码验证
        if (!ismobile($mobile)) {
            $this->apiReturn(406, [], '手机号码不正确');
        }

        $blackList   = load_config('black_list');
        $blackMobile = $blackList['mobile'];
        if (in_array($mobile, $blackMobile)) {
            $this->apiReturn(403, [], '该手机号已经被加入黑名单。');
        }

        $cacheRedis = Cache::getInstance('redis');
        $cacheKey   = "mobile:smallapp:$mobile";
        $send_time  = $cacheRedis->get($cacheKey, '', true);
        if ($send_time > 10) {
            $this->apiReturn(403, [], '该手机号发送次数超出系统限制。');
        }

        if (Helpers::getVerifyCode($mobile)) {
            $this->apiReturn(403, [], '发送间隔太短！请在60秒后再重试。');
        }

        //发送短信
        try {
            $code   = Helpers::setVerifyCode($mobile, 60);
            $messageServiceApi = Container::pull(MessageService::class);
            [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'register_code', $mobile, [$code, '2']);
            if ($approval) {
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['发送注册手机验证码', __METHOD__, [$mobile, [$code, '2']], $res], JSON_UNESCAPED_UNICODE));
                }
            } else {
                /** @deprecated 放量结束后删除 */
                $smsLib = SmsFactory::getFactory($mobile);
                $res = $smsLib->registerCode($code);
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['发送注册手机验证码.old', __METHOD__, [$mobile, [$code]], $res], JSON_UNESCAPED_UNICODE));
                }
            }
            if ($res['code'] == 200) {
                $cacheRedis->incrBy($cacheKey);
                $this->apiReturn(200, [], '发送验证码成功');
            } else {
                $this->apiReturn($res['code'], [], $res['msg']);
            }
        } catch (MessageException $e) {
            $this->apiReturn(500, [], '对不起，短信服务器发生故障，造成的不便我们感到十分抱歉。请联系我们客服人员。');
        }
    }

    /**
     * 获取可用得充值优惠活动
     *
     * @param  int $sid 供应商id
     * @param  string $page 页码
     * @param  string $pageSize 每页显示条数
     *
     * @return string | json
     *
     */
    public function getValidRechargeConfig()
    {
        $page     = I('page', 1);
        $pageSize = I('page_size', 10);
        $sid      = $this->_supplyId;
//        $sid      = 6970;
        if (empty($sid)) {
            $this->apiReturn(203, [], '参数有误');
        }
        // 验证登陆用户
        $this->isLoginCardSolution();
        //新版的充值配置获取新的表
        $rechargeMode = new RechargeScheme();
        //虚拟卡类型固定为7
        $configListArr = $rechargeMode->getAllRechargeSchemeList($sid, 'id, scheme_title, discount_rule', time(), 7);
        foreach ($configListArr as &$item) {
            $item['discount_rule'] = json_decode($item['discount_rule'], true);
        }
        $this->apiReturn(200, $configListArr, 'success');
    }

    /**
     * 获取用户清单
     *
     * @param  int $page 页码
     * @param  int $pageSize 显示数量
     *
     * @return string | json
     */
    public function getUserJournal()
    {
        $page     = I('page', 1);
        $pageSize = I('pageSize', 10);
        $applyId  = $this->_supplyId;

        if ($this->inWechatSmallApp()) {
            $openId = $this->_smallOpenid;
        }

        if ($openId == '') {
            $this->apiReturn(203, [], '参数有误');
        }

        $memberId = $this->isLoginCardSolution();  // 充值账号id

        if (!$memberId) {
            $this->apiReturn(203, [], '参数有误');
        }

        $cardMemberBiz   = new \Business\CardSolution\Member();
        $cardInfoListArr = $cardMemberBiz->getJournalList($memberId, $applyId, $page, $pageSize);

        if ($cardInfoListArr['code'] != 200) {
            $this->apiReturn(204, [], '无数据');
        }

        $this->apiReturn(200, ['list' => $cardInfoListArr['data']], 'success');
    }

    /**
     * 用户的园区卡充值
     * <AUTHOR>
     * @date   2018-04-20
     *
     * @param  int $configId 充值得配置id
     *
     */
    public function pay()
    {
        // 0 根据配置充值 1 用户自己输入金额
        $type = I('post.type', 0);
        // 优惠设置id
        $configId = I('config_id', -1);
        $money    = I('post.money', 0, 'string');
        $appId    = I('post.appid');

        //登陆用户
        $memberId = $this->isLoginCardSolution();

        //供应商ID
        $sid = $this->_supplyId;

        if (!($sid && $memberId)) {
            $this->apiReturn(203, [], '参数有误');
        }

        // lock it
        if (!$this->_lock()) {
            $this->clientReturn(204, [], '操作频繁,请稍后再试');
        }

        //判断是否开通会员卡
        $memberModel = new \Model\CardSolution\Member();
        $memberInfo  = $memberModel->getMemberInfoBySidAndFid($memberId, $sid, 'id');
        if (empty($memberInfo)) {
            $this->apiReturn(203, [], '未在该景区开通会员卡');
        }

        if ($type == 0) {
            //查询配置是否存在且是生效的
            if (empty($configId) || $configId == -1) {
                $this->apiReturn(203, [], '套餐错误');
            }
            $rechargeSchemeMode = new RechargeScheme();
            $configInfo         = $rechargeSchemeMode->findRechargeSchemeById($configId, 2);
            if (empty($configInfo)) {
                $this->apiReturn(203, [], '套餐错误');
            }
            $rechargeMode = new \Business\CardSolution\Recharge();
            // 用户实际支付金额
            $realPayMoney = $money * 100;
            //获取新版虚拟卡充值方案赠送的钱
            $useRecharge = $rechargeMode->useRechargeScheme('', $sid, $realPayMoney, true);
            if ($useRecharge['code'] != 200) {
                $this->clientReturn($useRecharge['code'], [], $useRecharge['msg']);
            }
            $awardPrice = $useRecharge['data'];
            // 用户实际到账金额
            $rechargeMoney = $realPayMoney + $awardPrice;
        } else {
            //用户自己输入金额
            // 用户实际支付金额
            $realPayMoney = $money * 100;
            // 用户实际到账金额
            $rechargeMoney = $money * 100;
            // 重置configid   用户自己输入的时候就不是取配置了,前端切换的时候传值有误
            $configId = 0;
        }

        $orderNum  = 'ykt_' . str_replace('.', '', microtime(true));
        $subject   = '会员卡充值' . $rechargeMoney / 100 . '元';
        $payParams = [
            'subject'    => $subject,
            'outTradeNo' => $orderNum,
        ];

        $payParams['openid']      = $this->_smallOpenid;
        $payParams['merchant_id'] = $sid;

        $describe = ['fid' => $memberId, 'sid' => $sid];
        $describe = json_encode($describe);
        //生成充值记录
        $onLineTrade = new OnlineTrade();
        $sellerEmail = ['appid' => $appId];
        $sellerEmail = json_encode($sellerEmail);
        $create      = $onLineTrade->addRecord(
            $orderNum, $subject, $realPayMoney, $sellerEmail,
            '', 1, $describe, 0, '', $sid
        );
        if (empty($create)) {
            $this->apiReturn(203, [], '创建支付订单失败');
        }

        $refundJournalModel = $this->_getRefundJournal();
        $res                = $refundJournalModel->addRechargeLog($memberId, $sid, $realPayMoney, $rechargeMoney,
            $orderNum, $create, 2, $configId);
        if (empty($res)) {
            $this->apiReturn(203, [], '添加充值记录失败');
        }

        $this->apiReturn(200, $payParams);
    }

    /**
     * 对请求加锁，避免重复操作
     * <AUTHOR>
     * @date   2017-03-13
     *
     */
    private function _lock()
    {
        $Redis = \Library\Cache\Cache::getInstance('redis');
        $key   = md5(json_encode(I(null)));

        return $Redis->lock($key, 1, 10);
    }

    /**
     * 解密前端返回的解密数据
     *
     * @param  string encrypted_data   加密数据
     * @param  string iv               向量
     *
     * @return array
     *
     */
    public function decryptData()
    {
        $encryptedData = I('encrypted_data');
        $iv            = I('iv');
        $account       = I('account', '999999');
        if (!$this->inWechatSmallApp()) {
            $this->apiReturn(203, [], '服务错误');
        }

        if (empty($encryptedData) || empty($iv)) {
            $this->apiReturn(203, [], '参数有误');
        }

        if (empty($this->_sessionKey)) {
            $this->apiReturn(204, [], '参数有误');
        }
        //define('ENV', 'PRODUCTION');//生产环境
        //define('ENV', 'TEST');//预生产环境
        //define('ENV', 'DEVELOP');//内网测试环境，本地开发环境也可以使用这个
        //define('ENV', 'LOCAL');//本地开发环境，静态文件之类的都会使用本地文件
        if (ENV == "PRODUCTION" || ENV == 'TEST') {
            $solutionMemberBiz = new \Business\CardSolution\Member();
            $decryptDataArr    = $solutionMemberBiz->decryptData($this->_sessionKey, $encryptedData, $iv, $account,
                $this->_miniAppId);
        } else {
            $smallLib               = new \Library\Business\WechatSmallApp;
            $res                    = $smallLib->getSession("mall-smallInfo:" . $this->_sessionKey, '*');
            $decryptDataArr['data'] = [
                'openId'    => $res['openid'],
                'nickName'  => $res['dname'],
                'gender'    => 1,
                'language'  => 'zh_CN',
                'city'      => 'FuZhou',
                'province'  => 'Fujian',
                'country'   => 'China',
                'avatarUrl' => $res['headphoto'],
                'unionId'   => $res['havaUnionid'],
            ];
        }

        $this->apiReturn(200, $decryptDataArr['data'], 'success');
    }

    /**
     * 解密前端返回的解密数据
     *
     * @param  string encrypted_data   加密数据
     * @param  string iv               向量
     *
     * @return array
     *
     */
    public function touTiaoDecryptData()
    {
        $encryptedData = I('post.encrypted_data');
        $iv            = I('post.iv');
        if (!$this->inTouTiaoSmallApp()) {
            $this->apiReturn(203, [], '服务错误');
        }
        if (empty($encryptedData) || empty($iv)) {
            $this->apiReturn(203, [], '参数有误');
        }

        if (empty($this->_sessionKey)) {
            $this->apiReturn(204, [], '参数有误');
        }
        $touTiaoApi     = new Core();
        $decryptDataArr = $touTiaoApi->decryptDataByTouTiao($this->_sessionKey, $encryptedData, $iv);
        if ($decryptDataArr['code'] != 200) {
            $this->apiReturn(204, [], '解密失败');
        }

        $this->apiReturn(200, json_decode($decryptDataArr['data'], true), 'success');
    }

    /**
     * 生成sid加密串
     * <AUTHOR>
     * @date   2018-04
     *
     * @param  int $sid 园区id
     *
     * @return string | bool
     *
     */
    private function _createScenCode($sid)
    {
        if (!$sid) {
            return false;
        }

        $lib      = new \Library\Business\WechatSmallApp();
        $sCenCode = $lib->encodeShopCode($sid);

        if (!$sCenCode) {
            return false;
        }

        return $sCenCode;
    }
}
