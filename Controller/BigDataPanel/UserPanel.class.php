<?php
/**
 * 用户大数据面板
 */

namespace Controller\BigDataPanel;

use Business\Report\BigDataPanel as BigDataPanelBiz;
use Business\Statistics\HomeOrder;
use Library\Cache\Cache;
use Library\Tools\Helpers;
use Library\Tools\Letter;
use Model\BigDataPanel\PftBigAge;
use Model\BigDataPanel\PftBigDataPanelConfig;
use Model\BigDataPanel\PftBigDistriChannel;
use Model\BigDataPanel\PftBigOrderBook;
use Model\BigDataPanel\PftBigSex;
use Model\BigDataPanel\PftBigTouristPlace;
use Model\BigDataPanel\PftBigTripNumbers;
use Model\Member\Member;
use Model\Member\MemberRelationship;
use Model\Product\Area;
use Model\Product\Land;
use Model\Report\Statistics;
use Business\BigDataPanel\UserPanel as UserPanelBz;

class UserPanel extends \Library\Controller
{
    const __DEFAULT_CITY__ = "北京";

    private $_redisPreKey = 'bduser:';
    private $_redis;

    //线下预订方式 10 云票务  12 自助机  14 闸机  15 智能终端  18 年卡  其他都算成在线预定
    private $_offLineOrderMode = [10, 12, 14, 15, 18];

    //用户主账号ID
    private $_sid;
    /**
     * @var array 不需要登录校验的接口白名单
     */
    private $whiteMethodList = [
        'getWeather',
    ];

    /**
     * @var array 不需要登录校验的接口白名单
     */
    private $whiteDataMethodList = [
        'getWeatherByAccountOrCity',
    ];

    private $spcAccountList = [
        '1a18aafa48ded7c6c364115183109a3b', //589253 扬州市何园管理处  591906
        '7d08885331177bf9d8dc151d987b5881', //589250 扬州市个园管理处  591889
        'c39ffd5af04f6f7565decb03f592bf01', //589277 扬州市茱萸湾风景区管理处（扬州动物园）592024
        'f7704bcbda248fdc2b42df317cb72ae7', //123624  3385
    ];

    /**
     * 获取初始数据
     * @vacation  bigDataPanel
     */
    public function __construct()
    {
        if (!in_array($_GET['a'], $this->whiteMethodList)) {

            if (in_array($_GET['a'], $this->whiteDataMethodList) && !empty($_GET['data']) && in_array($_GET['data'],
                    $this->spcAccountList)) {
                switch ($_GET['data']) {
                    case '1a18aafa48ded7c6c364115183109a3b':
                        $this->_sid = 591906;
                        break;
                    case '7d08885331177bf9d8dc151d987b5881':
                        $this->_sid = 591889;
                        break;
                    case 'c39ffd5af04f6f7565decb03f592bf01':
                        $this->_sid = 592024;
                        break;
                    case 'f7704bcbda248fdc2b42df317cb72ae7':
                        $this->_sid = 3385;
                        break;
                }
            } else {
                $loginInfo  = $this->getLoginInfo();
                $memberType = $loginInfo['dtype'];

                $this->_sid         = $loginInfo['sid'];
                $this->_redisPreKey = $this->_redisPreKey . $this->_sid . ':';
                if ($memberType == 7) {
                    //集团账号
                    $queryParams = [$this->_sid];
                    $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberRelationQuery',
                        'queryPageGroupSonIds', $queryParams);
                    $memberIdArr = [$this->_sid];
                    if ($queryRes['code'] == 200 && !empty($queryRes['data']['list'])) {
                        $memberIdArr = array_merge($memberIdArr, $queryRes['data']['list']);
                    }

                    //$shipModel   = new MemberRelationship($this->_sid);
                    //$memberIdArr = $shipModel->getResellers($this->_sid, $memberType, true, false);
                    $currentFid = I('post.current_fid');
                    if ($currentFid && in_array($currentFid, $memberIdArr)) {
                        //如果选择了某一个集团成员查看
                        $this->_sid         = $currentFid;
                        $this->_redisPreKey = $this->_redisPreKey . $this->_sid . ':';
                    } else {
                        $this->_sid = $memberIdArr;
                    }
                }
            }
        }
        $this->_redis = Cache::getInstance('redis');
    }

    /**
     * 获取集团账号的成员
     */
    public function getGroupMemberId()
    {
        $loginInfo       = $this->getLoginInfo();
        $sid             = $loginInfo['sid'];
        $memberShipModel = new MemberRelationship();
        $data            = $memberShipModel->getMemberByParentId($sid, 'id, son_id');
        if (empty($data)) {
            $this->apiReturn(200, []);
        }

        $sonIdArr    = array_column($data, 'son_id');
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfoByMulti($sonIdArr, 'id', 'id, dname', true);
        $this->apiReturn(200, $memberInfo);
    }

    /**
     * 首页
     */
    public function DayCollect()
    {
        //分销渠道分析
        $channel = $this->_getChannel();
        //产品排行
        $productTop = $this->_getProductTop();
        //轨迹分析
        $trailData = $this->_getTrailData();
        //出游人数
        $tripNumbers = $this->_getTripNumbers();
        //在线预订
        $onLineBook = $this->_getOnlineBook();
        //游客地分析
        $touristPlace = $this->_getTouristPlace();
        //销售趋势
        $saleTrend = $this->_getSaleTrend();
        //获取OTA分销用户排行版
        $otaReseller = $this->_getOtaReseller();
        //分销商数量
        $statistics = $this->_getStatistics();
        //年度销售总额
        $saleMoneyYear = $this->_getSaleMoneyYear();

        $data = [
            'online_book'     => $onLineBook,
            'trip_number'     => $tripNumbers,
            'sale_money_year' => $saleMoneyYear,
            'product_top'     => $productTop,
            'tourist_place'   => $touristPlace,
            'channel'         => $channel,
            'trail'           => $trailData,
            'sale_trend'      => $saleTrend,
            'ota_reseller'    => $otaReseller,
            'statistics'      => $statistics,
        ];

        $this->apiReturn(200, $data);
    }

    /**
     * 获取首页每小时更新的数据
     * <AUTHOR>
     * @date   2017-6-21
     */
    public function HourCollect()
    {
        //天气
        $weatherInfo = $this->_getWeather();
        //今年预订 包含今日
        $yearOrder = $this->_yearOrder();
        //今年检票 包含今日
        $yearCheck = $this->_yearCheck();
        //今日预订小时汇总
        $hourOrder = $this->_hourOrder();
        //今日检票小时汇总
        $hourCheck = $this->_hourCheck();
        //销售趋势
        $saleTrend = $this->_getSaleTrend();

        $data = [
            'weather'    => $weatherInfo,
            'year_order' => $yearOrder,
            'year_check' => $yearCheck,
            'hour_order' => $hourOrder,
            'hour_check' => $hourCheck,
            'sale_trend' => $saleTrend,
        ];

        $this->apiReturn(200, $data);
    }

    /**
     * 子页 游客画像
     */
    public function touristPortrait()
    {
        //轨迹分析
        $trailData = $this->_getTrailData();
        //出游人数
        $tripNumbers = $this->_getTripNumbers();
        //性别占比
        $sexData = $this->_getSexData();
        //年龄占比
        $ageData = $this->_getAgeData();
        //游客地分析
        $touristPlace = $this->_getTouristPlace();

        $data = [
            'trail'         => $trailData,
            'trip_number'   => $tripNumbers,
            'sex'           => $sexData,
            'age'           => $ageData,
            'tourist_place' => $touristPlace,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页 游客数据子页
     */
    public function touristData()
    {
        //轨迹分析
        $trailData = $this->_getTrailData();
        //今日预订小时汇总
        $hourOrder = $this->_hourOrder();
        //今日检票小时汇总
        $hourCheck = $this->_hourCheck();
        //销售趋势
        $saleTrend = $this->_getSaleTrend();

        $data = [
            'sale_trend' => $saleTrend,
            'trail'      => $trailData,
            'hour_order' => $hourOrder,
            'hour_check' => $hourCheck,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页 在线预订占比
     */
    public function onLineBook()
    {
        //在线预订
        $onLineBook = $this->_getOnlineBook();
        //预订渠道排行
        $bookChannel = $this->_getBookChannelRank();

        $data = [
            'online_book'  => $onLineBook,
            'book_channel' => $bookChannel,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页 游客地分析
     */
    public function touristPlaceAnalyse()
    {
        //轨迹分析
        $trailData = $this->_getTrailData();
        //游客地分析
        $touristPlace = $this->_getTouristPlace();

        $data = [
            'tourist_place' => $touristPlace,
            'trail'         => $trailData,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页 7天产品排行
     */
    public function sevenProductRank()
    {
        //产品排行
        $productTop = $this->_getProductTop();

        $data = [
            'product_top' => $productTop,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页 分销渠道
     */
    public function disChannel()
    {
        //分销渠道 自供自销/OTA分销
        $channel = $this->_getChannel();
        //获取OTA分销用户排行版
        $otaReseller = $this->_getOtaReseller();

        $data = [
            'channel'      => $channel,
            'ota_reseller' => $otaReseller,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页 天气接口 （无需登录）
     */
    public function getWeather()
    {
        // 如果没用登录，校验token;token先写死。
        if (!$this->isLogin('ajax', false) && I('get.token') != 'c1cbb8d10a75c3be82b7985d8c74c7c9') {
            $this->apiReturn(203, [], '未授权');
        }
        $data = $this->_getWeather();

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页获取天气 （需要登录，默认取登录用户信息里的城市天气）
     * @author: zhangyz
     * @date: 2020/7/2
     */
    public function getWeatherPage()
    {
        $data = $this->_getWeather();

        $this->apiReturn(200, $data, '');
    }

    /**
     * 游客画像：游客出行人数
     *
     * <AUTHOR>
     * @date   2017-9-4
     */
    private function _getTripNumbers()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));
        $key  = $this->_redisPreKey . ':' . 'trip:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new PftBigTripNumbers();
            $res   = $model->getDataByTime($defaultBegin, $defaultEnd, $this->_sid);

            $levelOne = $levelTwo = $levelThree = $levelFour = 0;

            if (!empty($res)) {
                foreach ($res as $item) {
                    $levelOne   += $item['level_one'];
                    $levelTwo   += $item['level_two'];
                    $levelThree += $item['level_three'];
                    $levelFour  += $item['level_four'];
                }
            }

            $total = $levelOne + $levelTwo + $levelThree + $levelFour;

            //Division by zero
            if ($total == 0) {
                $count = [
                    'levelOne'   => 0,
                    'levelTwo'   => 0,
                    'levelThree' => 0,
                    'levelFour'  => 0,
                ];
            } else {
                $count = [
                    'levelOne'   => $levelOne / $total,
                    'levelTwo'   => $levelTwo / $total,
                    'levelThree' => $levelThree / $total,
                    'levelFour'  => $levelFour / $total,
                ];
            }

            $data = json_encode($count);

            if ($readCache) {
                $this->_redis->set($key, $data, '', 3600 * 24);
            }
        }

        $result = json_decode($data, true);

        return $result;
    }

    /**
     * 获取近一年的销售金额 从验证报表中获取
     * <AUTHOR>
     * @date   2017-4-18
     */
    private function _getSaleMoneyYear()
    {
        $bigDataBiz = new BigDataPanelBiz();
        $data       = $bigDataBiz->getYearSaleMoney($isSuper = false, $this->_sid);

        return $data;
    }

    /**
     * 获取分销商数
     */
    private function _getStatistics()
    {
        $relationModel = new MemberRelationship();
        $num           = $relationModel->getCountDispatch($this->_sid);

        return [
            'distributor' => $num,
        ];
    }

    /**
     * 游客画像：性别占比
     *
     * <AUTHOR>
     * @date   2017-9-4
     */
    private function _getSexData()
    {
        $begin = I('post.begin');
        $end   = I('post.end');

        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $data  = [];
        $model = new PftBigSex();
        $time  = date('Ymd', strtotime("-1 day"));
        $key   = $this->_redisPreKey . 'sex:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $data = $model->getDataByTime($defaultBegin, $defaultEnd, $this->_sid);
            $data = is_array($data) ? $data : [];

            //固化
            if ($readCache) {
                $value = json_encode($data);
                $this->_redis->set($key, $value, '', 3600 * 24);
            }
        }

        return $data;
    }

    /**
     * 游客画像：年龄占比
     *
     * <AUTHOR>
     * @date   2017-9-4
     */
    private function _getAgeData()
    {
        $begin = I('post.age_begin');
        $end   = I('post.age_end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $data  = [];
        $model = new PftBigAge();
        $time  = date('Ymd', strtotime("-1 day"));
        $key   = $this->_redisPreKey . 'age:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $data = $model->getDataByTime($defaultBegin, $defaultEnd, $this->_sid);
            $data = is_array($data) ? $data : [];
            $data = json_encode($data);
            //固化
            if ($readCache) {
                $this->_redis->set($key, $data, '', 3600 * 24);
            }
        }

        if (empty($data)) {
            $total = 0;
        } else {
            $data = json_decode($data, true);

            $total = $data['level_one_ticket'] + $data['level_two_ticket'] + $data['level_three_ticket'] +
                     $data['level_four_ticket'] + $data['level_five_ticket'];
        }

        if (empty($total)) {
            $result = [
                //0-6岁
                'level_one'   => 0,
                //7-17岁
                'level_two'   => 0,
                //18-40
                'level_three' => 0,
                //41-65
                'level_four'  => 0,
                //66以后
                'level_five'  => 0,
            ];
        } else {
            $result = [
                //0-6岁
                'level_one'   => $data['level_one_ticket'] / $total,
                //7-17岁
                'level_two'   => $data['level_two_ticket'] / $total,
                //18-40
                'level_three' => $data['level_three_ticket'] / $total,
                //41-65
                'level_four'  => $data['level_four_ticket'] / $total,
                //66以后
                'level_five'  => $data['level_five_ticket'] / $total,
            ];
        }

        return $result;
    }

    /**
     * 游客画像 游客画像：轨迹分析
     */
    private function _getTrailData()
    {
        $time   = date('Ymd', strtotime("-1 day"));
        $key    = $this->_redisPreKey . 'trail:' . $time;
        $return = $this->_redis->get($key);

        if (empty($return)) {
            $date       = date('Ymd', time() - 3600 * 24);
            $trailModel = new PftBigTouristPlace();
            $data       = $trailModel->getInfoByAid($date, $this->_sid, 2);
            $lidArr     = array_column($data, 'lid');
            $lidArr     = array_filter($lidArr);
            $lidArr     = array_unique($lidArr);

            //$landModel = new Land();
            //$landRes   = $landModel->getLandInfoByMuli($lidArr, 'id, area, title');

            $javaAPi = new \Business\CommodityCenter\Land();
            $landRes = $javaAPi->queryLandMultiQueryById($lidArr);
            $landRes = $landRes ?: [];
            foreach ($landRes as $item) {
                $landInfo[$item['id']] = $item;
            }
            $cityConfig = load_config('cities', 'account');
            $telConfig  = load_config('tel_city', 'account');
            $idCardCity = load_config('id_card_city', 'account');

            foreach ($data as $item) {
                //根据获取到的数据分析出发地和终止地
                $landItem = isset($landInfo[$item['lid']]) ? $landInfo[$item['lid']] : '';
                if (empty($landItem)) {
                    continue;
                }
                $areaCode = explode('|', $landItem['area']);
                $province = $areaCode[0];
                $city     = $areaCode[1];

                if (!isset($cityConfig[$province])) {
                    continue;
                }

                //起点
                $begin = '未知';
                foreach ($cityConfig[$province] as $value) {
                    if ($value['area_id'] == $city) {
                        $begin = $value['area_name'];
                        break;
                    }
                }

                //终点
                $end = isset($telConfig[$item['code']]) ? $telConfig[$item['code']] : '';
                if (empty($end)) {
                    $end = isset($idCardCity[$item['code']]) ? $idCardCity[$item['code']] : '未知';
                }
                // 前端华建反馈出发地和目的地反了，调整一下
                $return[] = ['end' => $begin, 'begin' => $end];
            }
        }

        return $return;
    }

    /**
     * 游客数据：获取每小时的预订统计量
     */
    private function _hourOrder()
    {
        //当前日期
        $date = date('Ymd');
        //当前小时数
        $hour = date('H');
        //当前月份
        $month = date('m');
        //当前天
        $day = date('d');

        if ($hour < 6) {
            return [];
        }

        $statisticsModel = new Statistics();
        $key             = $this->_redisPreKey . 'order:' . $this->_sid . ':';
        $cacheTime       = $this->_getTodayRemainTime();

        $data = [];
        for ($i = 0; $i <= $hour; $i++) {
            if ($i != $hour) {
                $tmpKey  = $key . $i;
                $tmpData = $this->_redis->get($tmpKey);
            } else {
                $tmpData = "";
            }

            $begin = mktime($i, 0, 0, $month, $day);
            $end   = mktime($i, 59, 59, $month, $day);

            if (empty($tmpData)) {
                //从数据库获取
                $tmpData = $statisticsModel->getInfoByDateAndInsertTimeV2($date, $begin, $end, $this->_sid, 1);
                $tmpData = json_encode($tmpData);
                if ($i != $hour) { //当前小时为实时数据，不记录缓存
                    $this->_redis->set($tmpKey, $tmpData, '', $cacheTime);
                }
            }

            $tmpData            = json_decode($tmpData, true);
            $data[$i]['order']  = empty($tmpData['order_num']) ? 0 : $tmpData['order_num'];
            $data[$i]['ticket'] = empty($tmpData['ticket_num']) ? 0 : $tmpData['ticket_num'];
        }

        return $data;
    }

    /**
     * 获取今天的每小时的检票统计量
     */
    private function _hourCheck()
    {
        //当前日期
        $date = date('Ymd');
        //当前小时数
        $hour = date('H');
        //当前月份
        $month = date('m');
        //当前天
        $day = date('d');
        if ($hour < 6) {
            return [];
        }

        $statisticsModel = new Statistics();
        $key             = $this->_redisPreKey . 'check:' . $this->_sid . ':';
        $cacheTime       = $this->_getTodayRemainTime();

        $data = [];
        for ($i = 0; $i <= $hour; $i++) {
            if ($i != $hour) {
                $tmpKey  = $key . $i;
                $tmpData = $this->_redis->get($tmpKey);
            } else {
                $tmpData = "";
            }

            $begin = mktime($i, 0, 0, $month, $day);
            $end   = mktime($i, 59, 59, $month, $day);

            if (empty($tmpData)) {
                //从数据库获取
                $tmpData = $statisticsModel->getInfoByDateAndInsertTimeV2($date, $begin, $end, $this->_sid, 2);
                $tmpData = json_encode($tmpData);
                if ($i != $hour) {  //当前小时为实时数据，不记录缓存
                    $this->_redis->set($tmpKey, $tmpData, '', $cacheTime);
                }

            }

            $tmpData            = json_decode($tmpData, true);
            $data[$i]['order']  = empty($tmpData['order_num']) ? 0 : $tmpData['order_num'];
            $data[$i]['ticket'] = empty($tmpData['ticket_num']) ? 0 : $tmpData['ticket_num'];
        }

        return $data;
    }

    /**
     * 获取今天剩余的时间（秒）
     * Create by zhangyangzhen
     * Date: 2019/8/7
     * Time: 14:25
     *
     * @return false|int
     */
    private function _getTodayRemainTime()
    {
        $tomorrow = date('Y-m-d', strtotime('+1 day'));

        $timestamp = strtotime($tomorrow);

        $time = $timestamp - time();

        return $time;
    }

    /**
     * 游客数据：销售趋势
     */
    private function _getSaleTrend()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (!strtotime($begin) || !strtotime($end)) {
            $bTimeNew = date('Y-m-d', time() - 3600 * 24 * 6);
            $eTimeNew = date('Y-m-d', time());
        } else {
            $bTimeNew = date('Y-m-d', strtotime($begin));
            $eTimeNew = date('Y-m-d', strtotime($end));
        }

        $homeOrderBusiness = new HomeOrder();
        $bTimeOld          = date('Y-m-d', strtotime('-1 year', strtotime($bTimeNew)));
        $eTimeOld          = date('Y-m-d', strtotime('-1 year', strtotime($eTimeNew)));
        $num               = $homeOrderBusiness->saleTrends($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, 1, $this->_sid);
        $money             = $homeOrderBusiness->saleTrends($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, 2, $this->_sid);

        return ['num' => $num, 'money' => $money];
    }

    /**
     * 在线预订占比:获取在线预订占比数（默认统计近30天）
     * 首页默认取近30天 子页可以通过开始结束时间选择
     *
     * <AUTHOR>
     * @date   2017-4-5
     */
    private function _getOnlineBook()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time   = date('Ymd', strtotime("-1 day"));
        $keyOne = $this->_redisPreKey . 'order:' . $time;
        $keyTwo = $this->_redisPreKey . 'onlinescale:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        //提前预定数分析  -- BEGIN
        if ($readCache) {
            //先从redis获取
            $data = $this->_redis->get($keyOne);
        }

        $one         = 0;
        $two         = 0;
        $three       = 0;
        $four        = 0;
        $five        = 0;
        $oneTicket   = 0;
        $twoTicket   = 0;
        $threeTicket = 0;
        $fourTicket  = 0;
        $fiveTicket  = 0;
        $total       = 0;

        $model = new PftBigOrderBook();

        if (empty($data)) {
            //从数据库获取
            $data = $model->getDataByTime($defaultBegin, $defaultEnd, $this->_sid);

            if (!empty($data)) {
                //总订单数
                $total = $data['the_same_day'] + $data['before_one'] + $data['before_three'] +
                         $data['before_seven'] + $data['before_other'];

                $totalOrder = $total;

                if ($total) {
                    //当天预订 比例（下同）
                    $one = $data['the_same_day'] / $total;
                    //提前一天
                    $two = $data['before_one'] / $total;
                    //提前三天
                    $three = $data['before_three'] / $total;
                    //提前七天
                    $four = $data['before_seven'] / $total;
                    //其他
                    $five = $data['before_other'] / $total;

                    //当天预订 人数 (下同)
                    $oneTicket = $data['the_same_day_ticket'];
                    //提前一天
                    $twoTicket = $data['before_one_ticket'];
                    //提前三天
                    $threeTicket = $data['before_three_ticket'];
                    //提前七天
                    $fourTicket = $data['before_seven_ticket'];
                    //其他
                    $fiveTicket = $data['before_other_ticket'];
                }
            }

            $count = [
                'the_same_day'        => $one,
                'before_one'          => $two,
                'before_three'        => $three,
                'before_seven'        => $four,
                'before_other'        => $five,
                'the_same_day_ticket' => $oneTicket,
                'before_one_ticket'   => $twoTicket,
                'before_three_ticket' => $threeTicket,
                'before_seven_ticket' => $fourTicket,
                'before_other_ticket' => $fiveTicket,
            ];

            $data = json_encode($count);

            if ($readCache) {
                //如果是从缓存读取 没读到 则存进缓存
                $this->_redis->set($keyOne, $data, '', 3600 * 24);
            }
        }

        $data = json_decode($data, true);
        //提前预定数分析  -- END

        //在线预订比例数分析 -- BEGIN
        if ($readCache) {
            $onLineRes = $this->_redis->get($keyTwo);
        }

        if (empty($onLineRes)) {
            $res = $model->getDataByMode($defaultBegin, $defaultEnd, $this->_offLineOrderMode, $this->_sid);

            //线上预定的票数
            $num = $res['ticket'];

            $onLineScale  = 0;
            $onLineOrder  = 0;
            $offLineOrder = 0;

            $total = $data['the_same_day_ticket'] + $data['before_one_ticket'] + $data['before_three_ticket'] +
                     $data['before_seven_ticket'] + $data['before_other_ticket'];

            if ($total) {
                //在线支付比例
                $onLineScale = $num / $total;
                //在线支付票数
                $onLineOrder = $num;
                //线下支付票数
                $offLineOrder = $total - $onLineOrder;
            }

            $onLineRes = [
                'scale'      => $onLineScale,
                'on_ticket'  => $onLineOrder,
                'off_ticket' => $offLineOrder,
            ];

            $onLineRes = json_encode($onLineRes);

            if ($readCache) {
                $this->_redis->set($keyTwo, $onLineRes, '', 3600 * 24);
            }
        }

        $onLineRes = json_decode($onLineRes, true);
        //在线预定比例
        $data['online_scale'] = $onLineRes['scale'];
        //在线预订人数
        $data['online_ticket'] = $onLineRes['on_ticket'];
        //线下预订人数
        $data['offline_ticket'] = $onLineRes['off_ticket'];

        //在线预订比例数分析 -- END

        return $data;
    }

    /**
     * 游客地分析:获取游客分析地（近30天）
     * <AUTHOR>
     * @date   2017-4-14
     */
    private function _getTouristPlace()
    {
        // 平台不再提供该接口数据
        return ['province' => [], 'city' => []];

        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));
        $time         = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        $provinceNameReturn = [];
        $cityNameReturn     = [];

        if ($readCache) {
            $key  = $this->_redisPreKey . 'place:' . $time;
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new PftBigTouristPlace();
            $res   = $model->getInfoByDate($defaultBegin, $defaultEnd, $this->_sid);

            if (empty($res)) {
                $res = [];
            }
            $data = json_encode($res);

            //固化
            if ($readCache) {
                $key = $this->_redisPreKey . 'place:' . $time;
                $this->_redis->set($key, $data, '', 3600 * 24);
            }
        }

        $result = json_decode($data, true);

        $cityName       = load_config('tel_city', 'account');
        $provinceName   = load_config('tel_province', 'account');
        $idCardProvince = load_config('id_card_province', 'account');
        $idCardCity     = load_config('id_card_city', 'account');

        if (!empty($result['province'])) {
            foreach ($result['province'] as $item) {

                if (strlen($item['code']) == 4 && isset($idCardProvince[substr($item['code'], 0, 2)])) {
                    // 因为身份证编码是4位,所以直接判断key
                    $provinceNameReturn[] = [
                        'name'   => $idCardProvince[substr($item['code'], 0, 2)],
                        'num'    => intval($item['num']),
                        'ticket' => intval($item['ticket']),
                    ];
                } elseif (isset($provinceName[$item['code']])) {
                    $provinceNameReturn[] = [
                        'name'   => $provinceName[$item['code']],
                        'num'    => intval($item['num']),
                        'ticket' => intval($item['ticket']),
                    ];
                }

            }
        }

        if (!empty($result['city'])) {
            foreach ($result['city'] as $item) {

                if (strlen($item['code']) == 4 && isset($idCardCity[$item['code']])) {
                    $cityNameReturn[] = [
                        'name'   => $idCardCity[$item['code']],
                        'num'    => intval($item['num']),
                        'ticket' => intval($item['ticket']),
                    ];
                } elseif (isset($cityName[$item['code']])) {
                    $cityNameReturn[] = [
                        'name'   => $cityName[$item['code']],
                        'num'    => intval($item['num']),
                        'ticket' => intval($item['ticket']),
                    ];
                }

            }
        }

        // 因为身份证跟手机号保存的code不同，所以会有2份数据，进行处理整合成一份数据, 因为整理后的数据顺序可能会改变，所以重新进行排序
        $res = [];
        foreach ($provinceNameReturn as $province) {
            if (!isset($res[$province['name']])) {
                $res[$province['name']] = $province;
            } else {
                $res[$province['name']]['num']    += intval($province['num']);
                $res[$province['name']]['ticket'] += intval($province['ticket']);
            }
        }
        $provinceNameReturn = array_values($res);
        usort($provinceNameReturn, function ($a, $b) {
            return ($a['ticket'] > $b['ticket']) ? -1 : 1;
        });
        $res = [];
        foreach ($cityNameReturn as $city) {
            if (!isset($res[$city['name']])) {
                $res[$city['name']] = $city;
            } else {
                $res[$city['name']]['num']    += intval($city['num']);
                $res[$city['name']]['ticket'] += intval($city['ticket']);
            }
        }
        $cityNameReturn = array_values($res);
        usort($cityNameReturn, function ($a, $b) {
            return ($a['ticket'] > $b['ticket']) ? -1 : 1;
        });

        return ['province' => $provinceNameReturn, 'city' => $cityNameReturn];
    }

    /**
     * 产品排行
     */
    private function _getProductTop()
    {
        $begin = I('post.begin');
        $end   = I('post.end');

        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        //需要做缓存的一些时间
        $defaultBegin = $time = date('Ymd', strtotime("-7 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));
        $today        = date('Ymd', time());
        $monthAgo     = date('Ymd', strtotime("-30 day"));
        $thisYear     = date('Y', time()) . '0101';

        $key          = $this->_redisPreKey . 'producttop:' . $defaultBegin;
        $lastBeginKey = $this->_redisPreKey . 'producttop:lastbegin:';
        $lastEndKey   = $this->_redisPreKey . 'producttop:lastend';
        $expire       = 3600 * 2;

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //记录上一次查询时间的数据
            $lastBegin = $this->_redis->get($lastBeginKey);
            $lastEnd   = $this->_redis->get($lastEndKey);

            //如果选择的时间是前7天，最近7天，最近30天，今年的话，走缓存
            if ($begin == $defaultBegin && $end == $defaultEnd) {
                $key    = $this->_redisPreKey . 'producttop:' . $defaultBegin;
                $expire = 3600 * 24;
            } elseif ($begin == $defaultBegin && $end == $today) {
                $key = $this->_redisPreKey . 'producttop:' . $today;
            } elseif ($begin == $monthAgo && $end == $today) {
                $key = $this->_redisPreKey . 'producttop:' . $monthAgo;
            } elseif ($begin == $thisYear && $end == $today) {
                $key = $this->_redisPreKey . 'producttop:' . $thisYear;
            } elseif ($begin == $lastBegin && $end == $lastEnd) {
                $key = $this->_redisPreKey . 'producttop:' . $lastBegin . $lastEnd;
            } else {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        } else {
            $expire = 3600 * 24;
        }

        $this->_redis->set($lastBeginKey, $defaultBegin, '', $expire);
        $this->_redis->set($lastEndKey, $defaultEnd, '', $expire);

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new Statistics();
            //获取总数
            $total    = $model->getProductTopByDateTotal($defaultBegin, $defaultEnd, $this->_sid);
            $num      = 100000;
            $landData = [];

            $landModel = new Land();
            //取出的数据 分页取 $i页数 $num 条数
            for ($i = 1; $i <= ceil($total / $num); $i++) {
                $tmpData = $model->getProductTopByDate($defaultBegin, $defaultEnd, $this->_sid, $i, $num);
                //取出景区id 资源id映射数据
                $lidArr       = array_column($tmpData, 'lid');
                $javaAPi      = new \Business\CommodityCenter\Land();
                $landRes      = $javaAPi->queryLandMultiQueryById($lidArr);
                //$resourceList = array_column($landRes, 'resource_id', 'id');
                $productName = array_column($landRes, 'title', 'id');
                //景区订单数据添加resourceID 参数
                foreach ($tmpData as $k => $value) {
                    $tmpData[$k]['title'] = $productName[$value["lid"]] ?? '';
                }
                $landData = array_merge($landData, $tmpData); //景区订单数据
            }

            $countedData = [];//初始化数组
            //计算去重
            foreach ($landData as $k => $v) {
                $orderNum  = (int)$v['order_num'];
                $ticketNum = (int)$v['ticket_num'];
                if ($countedData[$v["lid"]]) {
                    $countedData[$v["lid"]]['order_num']  += $orderNum;
                    $countedData[$v["lid"]]['ticket_num'] += $ticketNum;
                } else {
                    $countedData[$v["lid"]] = $v;
                }
            }

            //order_num 排序
            array_multisort(array_column($countedData, 'order_num'), SORT_DESC, $countedData);

            if (!empty($countedData)) {
                $data          = array_slice($countedData, 0, 50);
                /*$resourceidArr = array_column($data, 'resourceID');//资源id
                //获取资源名称
                $ResourceModel = new \Model\Product\LandResource();
                $resourceRes   = $ResourceModel->getResourceListByIdArr($resourceidArr, 'id,title');*/

                //数组重组成旧版本参数
                foreach ($data as $k => $value) {
                    /*if (isset($resourceRes[$value["resourceID"]])) {
                        $data[$k]['name'] = (string)$resourceRes[$value["resourceID"]];
                    } else {
                        $data[$k]['name'] = '未知';
                    }*/
                    $data[$k]['name']   = $value['title'];
                    $data[$k]['order']  = $value['order_num'];
                    $data[$k]['ticket'] = $value['ticket_num'];
                    unset($data[$k]['order_num']);
                    unset($data[$k]['ticket_num']);
                    unset($data[$k]['title']);
                }

                $data = json_encode($data);

                if ($readCache) {
                    $this->_redis->set($key, $data, '', $expire);
                }
            }
        }

        $result = json_decode($data, true);

        return $result ?: [];
    }

    /**
     * 分销渠道 默认三十天
     */
    private function _getChannel()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));
        $key  = $this->_redisPreKey . 'channel:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new PftBigDistriChannel();
            $data  = $model->getInfoByDate($defaultBegin, $defaultEnd, $this->_sid);
            $data  = json_encode($data, true);
            if ($readCache) {
                $this->_redis->set($key, $data, '', 3600 * 24);
            }
        }

        $data = json_decode($data, true);

        return $data;
    }

    /**
     * 获取OTA分销排行
     */
    private function _getOtaReseller()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));
        $key  = $this->_redisPreKey . 'reseller:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new PftBigDistriChannel();
            $data  = $model->getResellerRankCountTicket($defaultBegin, $defaultEnd, $this->_sid);
            $data  = json_encode($data);
            $key   = $this->_redisPreKey . 'reseller:' . $time;
            $this->_redis->set($key, $data, '', 3600 * 24);
        }

        $data = json_decode($data, true);

        return $data;
    }

    private function _getBookChannelRank()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));
        $key  = $this->_redisPreKey . 'bookchannel:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $statisticsModel = new Statistics();
            $data            = $statisticsModel->getBookChannelRank($defaultBegin, $defaultEnd, $this->_sid);
            $data            = json_encode($data);
            $key             = $this->_redisPreKey . 'bookchannel:' . $time;
            $this->_redis->set($key, $data, '', 3600 * 24);
        }

        $data = json_decode($data, true);

        return $data;
    }

    /**
     * 今年检测
     */
    private function _yearCheck()
    {
        //获取截止到昨天的数据  和  累积的接待游客数一致
        $bigDataBiz = new BigDataPanelBiz();
        $data       = $bigDataBiz->getYearTicektnum($dataType = 1, $isSuper = false, $this->_sid);

        //获取今天的数据
        $statisticsModel = new Statistics();
        $num             = $statisticsModel->getCheckToday($this->_sid);

        //两者相加
        $total = $num + $data;

        return $total;
    }

    /**
     * 今年预订
     */
    private function _yearOrder()
    {
        //获取截止到昨天的数据
        $bigDataBiz = new BigDataPanelBiz();
        $data       = $bigDataBiz->getYearTicektnum($dataType = 2, $isSuper = false, $this->_sid);

        //获取今天的数据
        $statisticsModel = new Statistics();
        $num             = $statisticsModel->getOrderToday($this->_sid);

        //两者相加
        $total = $num + $data;

        return $total;
    }

    /**
     * 获取天气
     * API接口文档地址 - https://dev.heweather.com/docs/api/weather
     * Create by zhangyangzhen
     * Date: 2018/9/26
     * Time: 18:11
     * @return bool|mixed
     */
    private function _getWeather()
    {
        $city = I('post.weather_city', '', 'strval');
        // 用户最后一次查询地址redis key
        //        $userQueryKey   = $this->_redisPreKey . 'lastqueryplace';
        // 判断redis中是否存在用户的最后一次查询地址
        //        $lastQueryPlace = $this->_redis->get($userQueryKey);

        if (empty($city)) {
            if ($this->_sid == 1) {
                //如果是管理员
                $city = self::__DEFAULT_CITY__;
            } else {
                //这里做个特殊处理 如果是集团账号，取集团账号的第一个
                if (is_array($this->_sid)) {
                    $sid = $this->_sid[0];
                } else {
                    $sid = $this->_sid;
                }
                //用户二期 - 信息获取修改 - modification-a
                $MemberBus  = new \Business\Member\Member();
                $memberInfo = $MemberBus->getInfo($sid, true);
                if (empty($memberInfo)) {
                    $city = self::__DEFAULT_CITY__;
                } else {
                    $cityConf = load_config('cities', 'account');
                    $province = $memberInfo['province'];
                    $cityCode = $memberInfo['city'];

                    // 兼容三方系统不支持部分区域重新划分导致的（unknown location），比如崇文区
                    $cityCode = ($cityCode == 37) ? 35 : $cityCode;

                    if (!isset($cityConf[$province])) {
                        $city = self::__DEFAULT_CITY__;
                    } else {
                        $cities  = $cityConf[$province];
                        $areaArr = array_column($cities, 'area_name', 'area_id');
                        $city    = $areaArr[$cityCode] ?: self::__DEFAULT_CITY__;
                    }
                }
            }
        }

        return $this->_getWeatherByCity($city);
    }

    /**
     * 通过城市查询天气
     * @author: zhangyz
     * @date: 2020/4/28
     *
     * @param  string $city 城市名称
     *
     * @return bool|mixed|string
     */
    private function _getWeatherByCity($city)
    {
        $spellClass = new Letter();
        $spell      = $spellClass->convertInitalPinyin($city);
        $time       = date('Ymd', strtotime("-1 day"));
        $redisKey   = $this->_redisPreKey . "weather:{$spell}:" . $time;
        $res        = $this->_redis->get($redisKey);
        if (!empty($res)) {
            return json_decode($res, true);
        }

        $res = Helpers::getWeather($city);
        if (!empty($res)) {
            //保存城市信息
            $this->_redis->set($redisKey, json_encode($res), '', 3600 * 24);
        }

        return $res;
    }

    /**
     * 专供给扬州的出入园数据页面，用到的天气。现在还有一个山西仙堂山也要共用，直接通过ID写死城市
     * 这边新写一个接口专门给这个页面使用，和大数据面板的获取天气接口区分开
     * 页面地址：https://my.12301.cc/new/userPannel_yangParkData.html
     * @author: zhangyz
     * @date: 2020/4/28
     */
    public function getWeatherByAccountOrCity()
    {
        $paramsCity   = I('post.weather_city', '', 'strval');  //市
        $paramsCity   = urldecode($paramsCity);
        //优先取传参的城市，再取配置的城市，再去用户信息中的所在城市（用户信息中只能到市，不能到县，所以客户有需求的话手动配置一下）
        $cityArr = [
            606974  => '扬州',   //江苏省扬州市何园风景区（散客班票）-终端合并 account=********
            4884790 => '襄垣县', //山西仙堂山旅游开发有限公司 account=5902703
            691796  => '泽州县', //大阳古镇旅游景区 account = 206133
        ];

        // 地区自定义名称配置，有客户想要展示景区的名称，而不是城市名称
        $customerCityName = [
            6970   => '横山岛',
            864375 => '横山岛',
        ];

        //这里做个特殊处理 如果是集团账号，取集团账号的第一个
        if (is_array($this->_sid)) {
            $sid = $this->_sid[0];
        } else {
            $sid = $this->_sid;
        }
        if ($paramsCity) {
            $city = $paramsCity;
        } elseif (isset($cityArr[(int)$this->_sid])) {
            $city = $cityArr[(int)$this->_sid];
        } else {
            $panelConfigModel = new PftBigDataPanelConfig();
            $field            = "address,title,custom_config,optional_config,version,history_data";
            $configInfo       = $panelConfigModel->getConfigInfoBySid($sid, $field);
            if ($configInfo) {
                list($provinceId, $cityId, $countyId) = explode('|', $configInfo['address']);
                $areaCode                      = explode('|', $configInfo['address']);
                $areaModel                     = new Area();
                $areaName                      = $areaModel->getAreaNameByCodeArr($areaCode);
                $weatherCity                   = empty($areaName[$countyId]) ? $areaName[$cityId] : $areaName[$countyId];
                if (!empty($areaName[$countyId]) && !empty($areaName[$cityId]) && strstr($areaName[$cityId], "市")) {
                    $weatherCity = $areaName[$countyId]. ",". strstr($areaName[$cityId], "市", true);
                }
                //直辖市直接取区作为定位地址北京，天津，上海，重庆
                if (in_array($provinceId, [1, 2, 3, 4])) {
                    $weatherCity = $areaName[$cityId];
                }
                $weatherCity                   = empty($weatherCity) ? self::__DEFAULT_CITY__ : $weatherCity;
                $configInfo['custom_config']   = empty($configInfo['custom_config']) ? [] : json_decode($configInfo['custom_config'], true);
                $configInfo['optional_config'] = empty($configInfo['custom_config']) ? [] : json_decode($configInfo['optional_config'], true);

            } else {
                $MemberBus  = new \Business\Member\Member();
                $memberInfo = $MemberBus->getInfo($sid, true);
                if (empty($memberInfo)) {
                    $city = self::__DEFAULT_CITY__;
                } else {
                    $cityConf = load_config('cities', 'account');
                    $province = $memberInfo['province'];
                    $cityCode = $memberInfo['city'];

                    if (!isset($cityConf[$province])) {
                        $city = self::__DEFAULT_CITY__;
                    } elseif (in_array($province, [1, 2, 3, 4])) {
                        $provinceConf = load_config('province', 'account');
                        $city         = $provinceConf[$province]['area_name'];
                    } else {
                        $cities  = $cityConf[$province];
                        $areaArr = array_column($cities, 'area_name', 'area_id');
                        $city    = $areaArr[$cityCode] ?: self::__DEFAULT_CITY__;
                    }
                }
            }
        }
        $weatherCity = $weatherCity ?? "$city";

        $res = $this->_getWeatherByCity($weatherCity);
        if (isset($customerCityName[$sid]) && empty($configInfo)) {
            $res['HeWeather6'][0]['basic']['location'] = $customerCityName[$sid];
        }

        $res['config'] = $configInfo ?? [];

        $this->apiReturn(self::CODE_SUCCESS, $res, 'success');
    }

    /**
     * 新增修改出入园大数据面板配置
     *
     * @return array
     * <AUTHOR>
     * @date 2021/10/14
     *
     *
     */
    public function addOrUpdatePanelConfig()
    {
        $id             = I('post.id', '', 'intval');
        $title          = I('post.title', '', 'strval');
        $address        = I('post.address', '', 'strval');
        $customConfig   = I('post.custom_config', '', 'strval');
        $optionalConfig = I('post.optional_config', '', 'strval');
        $version        = I('post.version', 0, 'intval');
        $historyData    = I('post.history_data', 0, 'intval');
        if (empty($title) || empty($address) || empty($optionalConfig)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $userPanel = new UserPanelBz();
        $result    = $userPanel->addOrUpdatePanelConfig($this->_sid, $id, $title, $address, $customConfig, $optionalConfig, $version,
            $historyData);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取出入园大数据面板配置
     *
     * @return array
     * <AUTHOR>
     * @date 2021/10/14
     *
     *
     */
    public function getPanelConfigInfo()
    {
        $userPanel = new UserPanelBz();
        $result    = $userPanel->getPanelConfigInfo($this->_sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    public function getPanelConfigDetail()
    {
        $id = I('post.id', 0, 'intval');
        $userPanel = new UserPanelBz();
        $result    = $userPanel->getPanelConfigDetail($id, $this->_sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
    public function getPanelConfigList()
    {
        $userPanel = new UserPanelBz();
        $page = I('post.page', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');
        $page = $page < 1 ? 1 : $page;
        $pageSize = $page < 1 ? 10 : $pageSize;
        $params = [
            'sid' => $this->_sid,
            'page' => $page,
            'pageSize' => $pageSize,
        ];
        $result    = $userPanel->getPanelConfigList($params);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    public function deletePanelConfig()
    {
        $id = I('post.id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(203, [], "参数错误");
        }
        $userPanel = new UserPanelBz();
        $result    = $userPanel->deletePanelConfig($id, $this->_sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}
