<?php
/**
 * User: linchen
 * Date: 2021/03/18
 * Time: 16:26
 */

namespace Controller\BigDataPanel;

use Library\Controller;
class WuYuanParkSummary extends Controller
{
    public function getWuYuanParkSummaryNew(){
        $beginTime = I('get.begin_time','');
        $endTime   = I('get.end_time','');

        if (!$beginTime || !$endTime){
            $this->apiReturn(204,[],'时间有误');
        }
        $parkSummaryBiz = new \Business\BigDataPanel\ParkSummary();
        $result = $parkSummaryBiz->getParkSummaryByDeviceList($beginTime,$endTime);
        if (isset($result['code'])){
            $this->apiReturn($result['code'],$result['data'],$result['msg']);
        }else{
            $this->apiReturn(500,[],'服务异常');
        }
    }
}