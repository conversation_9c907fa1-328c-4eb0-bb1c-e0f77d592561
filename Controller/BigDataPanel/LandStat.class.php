<?php

/**
 * 园区统计
 * Created by PhpStorm.
 * User: Jason
 * Date: 2017/5/16
 * Time: 14:35
 */

namespace Controller\BigDataPanel;

use Library\Controller;

class LandStat extends Controller
{
    private $_landStatBusiness;

    private $_memberId;
    private $_dtype;
    private $_account;

    public function __construct()
    {
        $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        $this->_memberId = $loginInfo['memberID'];
        $this->_dtype    = $loginInfo['dtype'];
        $this->_account  = $loginInfo['account'];
    }

    /**
     * 获取园区今日数据
     */
    public function getLandTodayData()
    {
        parent::apiReturn(parent::CODE_CREATED, [], '获取数据失败');
        $this->_landStatBusiness = new \Business\Statistics\LandStat();
        $result                  = $this->_landStatBusiness->getLandData($this->_account, $this->_dtype);
        if (!$result) {
            parent::apiReturn(parent::CODE_CREATED, [], '获取数据失败');
        }
        parent::apiReturn(parent::CODE_SUCCESS, $result, '获取获取成功');
    }

    /**
     * 修改园区接待能力相关信息
     *
     * @param  post.reception_ability 接待能力的人数
     * @param  post.warn_percent  接待能力报警百分数
     */
    public function dealLandReceptionInfo()
    {
        $receptionAbility = I('post.reception_ability', 0, 'intval');
        $warnPercent      = I('post.warn_percent', 0, 'intval');
        if ($receptionAbility <= 0 || $warnPercent <= 0 || $warnPercent > 100) {
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '参数错误');
        }
        $this->_landStatBusiness = new \Business\Statistics\LandStat();
        $result                  = $this->_landStatBusiness->modifyLandReceptionInfo($this->_account, $this->_dtype,
            $receptionAbility, $warnPercent);
        if (!$result) {
            parent::apiReturn(parent::CODE_CREATED, [], '修改失败');
        }
        parent::apiReturn(parent::CODE_SUCCESS, $result, '修改成功');
    }

}