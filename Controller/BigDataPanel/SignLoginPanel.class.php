<?php
/**
 * 通用的免登录签名效验大数据面板接口
 * <AUTHOR>
 * @date 2020/11/30
 */

namespace Controller\BigDataPanel;

use Business\BigDataPanel\SystemAuth;
use Library\Controller;

class SignLoginPanel extends Controller
{

    private $_apply_did;

    public function __construct()
    {
        $params = I('param.');
        if (empty($params['_source']) || empty($params['device_id']) || empty($params['timestamp']) || empty($params['sign'])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数有误');
        }

        $source    = strval($params['_source']);
        $deviceId  = strval($params['device_id']);
        $timestamp = intval($params['timestamp']);
        $signature = strval($params['sign']);

        $systemAuthLib = new SystemAuth();
        $checkRes      = $systemAuthLib->checkSignature($source, $deviceId, $timestamp, $signature);

        if ($checkRes['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($checkRes['code'], $checkRes['data'], $checkRes['msg']);
        }

        $applyDid = $systemAuthLib->queryApplyDidByDeviceIdCache($deviceId);
        if (empty($applyDid)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '匹配失败!');
        }

        $this->_apply_did = $applyDid;
    }

    /**
     * 获取园区人流数据
     * <AUTHOR>
     * @date 2020/11/30
     *
     * @return array
     */
    public function getParkSummary()
    {
        $parkSummaryLib = new \Business\BigDataPanel\ParkSummary();
        $result = $parkSummaryLib->getEnterLeaveData($this->_apply_did, date('Ymd'));
        if ($result['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $dayCnt   = $result['data']['enter_data'];
        $leaveCnt = $result['data']['leave_data'];
        $left     = (int)$dayCnt - (int)$leaveCnt;
        $data     = [
            'dayIn'  => (int)$dayCnt,
            'dayOut' => (int)$leaveCnt,
            'left'   => $left < 0 ? 0 : $left,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $data, 'success');
    }

}