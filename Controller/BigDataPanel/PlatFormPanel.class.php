<?php
/**
 * 大数据面板 模块
 * -> 平台大数据（后期可能有供应商大数据  控制器以Panel为尾缀）
 * 每个小块数据一个function  每个(子)页面需要的数据由多个function组成
 *
 * <AUTHOR>
 * @date   2017-4-5
 */
namespace Controller\BigDataPanel;

use Business\Report\BigDataPanel as BigDataPanelBiz;
use Business\Statistics\HomeOrder;
use Library\Cache;
use Library\Tools\Helpers;
use Library\Tools\Letter;
use Library\Tools\PinYin;
use Model\BigDataPanel\PftBigAge;
use Model\BigDataPanel\PftBigDistriChannel;
use Model\BigDataPanel\PftBigHourData;
use Model\BigDataPanel\PftBigOrderBook;
use Model\BigDataPanel\PftBigSex;
use Model\BigDataPanel\PftBigStatistics;
use Model\BigDataPanel\PftBigTouristPlace;
use Model\BigDataPanel\PftBigTrail;
use Model\BigDataPanel\PftBigTripNumbers;
use Model\Member\Member;
use Model\Product\Land;
use Model\Report\Statistics;

class PlatFormPanel extends \Library\Controller
{
    const __DEFAULT_CITY__ = "北京";

    private $_redisPreKey = 'bigdata:';
    private $_redis;
    private $_sid;

    private $_getIpUrl  = 'http://int.dpool.sina.com.cn/iplookup/iplookup.php?format=json';
    private $_getWeaUrl = 'http://jisutqybmf.market.alicloudapi.com';
    private $_appCode   = 'fccb365e404a47a29e0f9fbfd015c2f2';

    //线下预订方式 10 云票务  12 自助机  14 闸机  15 智能终端  18 年卡  其他都算成在线预定
    private $_offLineOrderMode = [10, 12, 14, 15, 18];


    public function __construct()
    {
        $this->_redis = Cache\Cache::getInstance('redis');
        $loginInfo    = $this->getLoginInfo();
        $this->_sid   = $loginInfo['sid'];
    }
    /**
     * 获取按日汇总的数据
     * <AUTHOR>
     * @date   2017-4-5
     */
    public function DayCollect()
    {
        //在线预订
        $onLineBook = $this->_getOnlineBook();
        //出游人数
        $tripNumbers = $this->_getTripNumbers();
        //产品排行
        $productTop = $this->_getProductTop();
        //游客地分析
        $touristPlace = $this->_getTouristPlace();
        //景区数量,产品数量,供应商数量,分销商数量,旅行社数量
        $statistics = $this->_getStatistics();
        //年度销售总额
        $saleMoneyYear = $this->_getSaleMoneyYear();
        //接待游客数量
        $touristNum = $this->_getTicketNum();
        //分销渠道 自供自销/OTA分销
        $channel = $this->_getChannel();
        //获取OTA分销用户排行版
        $otaReseller = $this->_getOtaReseller();
        //销售趋势
        $saleTrend = $this->_getSaleTrend();

        $data = [
            'online_book'     => $onLineBook,
            'trip_number'     => $tripNumbers,
            'product_top'     => $productTop,
            'tourist_place'   => $touristPlace,
            'statistics'      => $statistics,
            'sale_money_year' => $saleMoneyYear,
            'tourist_num'     => $touristNum,
            'channel'         => $channel,
            'sale_trend'      => $saleTrend,
            'ota_reseller'    => $otaReseller,
        ];

        $this->apiReturn(200, $data);
    }

    /**
     * 获取首页每小时更新的数据
     * <AUTHOR>
     * @date   2017-6-21
     */
    public function HourCollect()
    {
        //天气
        $weatherInfo = $this->_getWeather();
        //今年预订 包含今日
        $yearOrder = $this->_yearOrder();
        //今年检票 包含今日
        $yearCheck = $this->_yearCheck();
        //轨迹分析
        $trailData = $this->_getTrailData();
        //今日预订小时汇总
        $hourOrder = $this->_hourOrder();
        //今日检票小时汇总
        $hourCheck = $this->_hourCheck();
        //销售趋势
        $saleTrend = $this->_getSaleTrend();

        $data = [
            'weather'    => $weatherInfo,
            'year_order' => $yearOrder,
            'year_check' => $yearCheck,
            'trail'      => $trailData,
            'hour_order' => $hourOrder,
            'hour_check' => $hourCheck,
            'sale_trend' => $saleTrend,
        ];

        $this->apiReturn(200, $data);
    }

    /**
     * 获取首页地图和游客地来源
     * <AUTHOR>
     * @date   2018-04-04
     */
    public function SecondCollect()
    {
        //轨迹分析
        $trailData = $this->_getTrailData();
        //游客地分析
        $touristPlace = $this->_getTouristPlace();

        $data = [
            'trail'         => $trailData,
            'tourist_place' => $touristPlace,
        ];

        $this->apiReturn(200, $data);
    }

    /**
     * 子页 在线预定占比 默认三十天
     */
    public function onLineBook()
    {
        //在线预订
        $onLineBook = $this->_getOnlineBook();
        //预定渠道
        $bookChannel = $this->_getBookChannelRank();

        $data = [
            'online_book'  => $onLineBook,
            'book_channel' => $bookChannel,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页 游客数据子页
     */
    public function touristData()
    {
        //今日预订小时汇总
        $hourOrder = $this->_hourOrder();
        //今日检票小时汇总
        $hourCheck = $this->_hourCheck();
        //销售趋势
        $saleTrend = $this->_getSaleTrend();

        $data = [
            'sale_trend' => $saleTrend,
            'hour_order' => $hourOrder,
            'hour_check' => $hourCheck,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页 游客地分析
     */
    public function touristPlaceAnalyse()
    {
        //轨迹分析
        $trailData = $this->_getTrailData();
        //游客地分析
        $touristPlace = $this->_getTouristPlace();

        $data = [
            'tourist_place' => $touristPlace,
            'trail'         => $trailData,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页 分销渠道
     */
    public function disChannel()
    {
        //分销渠道 自供自销/OTA分销
        $channel = $this->_getChannel();
        //获取OTA分销用户排行版
        $otaReseller = $this->_getOtaReseller();

        $data = [
            'channel'      => $channel,
            'ota_reseller' => $otaReseller,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页 实时天气
     */
    public function getWeather()
    {
        //天气
        $weatherInfo = $this->_getWeather();

        $data = [
            'weather' => $weatherInfo,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页 游客画像
     */
    public function touristPortrait()
    {
        //轨迹分析
        $trailData = $this->_getTrailData();
        //出游人数
        $tripNumbers = $this->_getTripNumbers();
        //性别占比
        $sexData = $this->_getSexData();
        //年龄占比
        $ageData = $this->_getAgeData();
        //游客地分析
        $touristPlace = $this->_getTouristPlace();

        $data = [
            'trail'         => $trailData,
            'trip_number'   => $tripNumbers,
            'sex'           => $sexData,
            'age'           => $ageData,
            'tourist_place' => $touristPlace,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 子页 7天产品排行
     */
    public function sevenProductRank()
    {
        //产品排行
        $productTop = $this->_getProductTop();
        //在线预订
        $onLineBook = $this->_getOnlineBook();

        $data = [
            'product_top' => $productTop,
            'online_book' => $onLineBook,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 获取在线预订占比数（默认统计近30天）
     * 首页默认取近30天 子页可以通过开始结束时间选择
     *
     * <AUTHOR>
     * @date   2017-4-5
     */
    private function _getOnlineBook()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time   = date('Ymd', strtotime("-1 day"));
        $keyOne = $this->_redisPreKey . 'order:' . $time;
        $keyTwo = $this->_redisPreKey . 'onlinescale:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        //提前预定数分析  -- BEGIN
        if ($readCache) {
            //先从redis获取
            $data = $this->_redis->get($keyOne);
        }

        $one         = 0;
        $two         = 0;
        $three       = 0;
        $four        = 0;
        $five        = 0;
        $oneTicket   = 0;
        $twoTicket   = 0;
        $threeTicket = 0;
        $fourTicket  = 0;
        $fiveTicket  = 0;
        $total       = 0;

        $model = new PftBigOrderBook();

        if (empty($data)) {
            //从数据库获取
            $data = $model->getDataByTime($defaultBegin, $defaultEnd);

            if (!empty($data)) {
                //总订单数
                $total = $data['the_same_day'] + $data['before_one'] + $data['before_three'] +
                    $data['before_seven'] + $data['before_other'];

                if ($total) {
                    //当天预订 比例（下同）
                    $one = $data['the_same_day'] / $total;
                    //提前一天
                    $two = $data['before_one'] / $total;
                    //提前三天
                    $three = $data['before_three'] / $total;
                    //提前七天
                    $four = $data['before_seven'] / $total;
                    //其他
                    $five = $data['before_other'] / $total;

                    //当天预订 人数 (下同)
                    $oneTicket = $data['the_same_day_ticket'];
                    //提前一天
                    $twoTicket = $data['before_one_ticket'];
                    //提前三天
                    $threeTicket = $data['before_three_ticket'];
                    //提前七天
                    $fourTicket = $data['before_seven_ticket'];
                    //其他
                    $fiveTicket = $data['before_other_ticket'];
                }
            }

            $count = [
                'the_same_day'        => $one,
                'before_one'          => $two,
                'before_three'        => $three,
                'before_seven'        => $four,
                'before_other'        => $five,
                'the_same_day_ticket' => $oneTicket,
                'before_one_ticket'   => $twoTicket,
                'before_three_ticket' => $threeTicket,
                'before_seven_ticket' => $fourTicket,
                'before_other_ticket' => $fiveTicket,
            ];

            $data = json_encode($count);

            if ($readCache) {
                //如果是从缓存读取 没读到 则存进缓存
                $this->_redis->set($keyOne, $data, '', 3600 * 24);
            }
        }

        $data = json_decode($data, true);
        //提前预定数分析  -- END

        //在线预订比例数分析 -- BEGIN
        if ($readCache) {
            $onLineRes = $this->_redis->get($keyTwo);
        }

        if (empty($onLineRes)) {
            $res = $model->getDataByMode($defaultBegin, $defaultEnd, $this->_offLineOrderMode);

            //线上预定票数
            $num = $res['ticket'];

            $total = $data['the_same_day_ticket'] + $data['before_one_ticket'] + $data['before_three_ticket'] +
                $data['before_seven_ticket'] + $data['before_other_ticket'];

            $onLineScale  = 0;
            $onLineOrder  = 0;
            $offLineOrder = 0;

            if ($total) {
                //在线支付比例
                $onLineScale = $num / $total;
                //在线支付票数
                $onLineOrder = $num;
                //线下支付票数
                $offLineOrder = $total - $onLineOrder;
            }

            $onLineRes = [
                'scale'      => $onLineScale,
                'on_ticket'  => $onLineOrder,
                'off_ticket' => $offLineOrder,
            ];

            $onLineRes = json_encode($onLineRes);

            if ($readCache) {
                $this->_redis->set($keyTwo, $onLineRes, '', 3600 * 24);
            }
        }

        $onLineRes = json_decode($onLineRes, true);
        //在线预定比例
        $data['online_scale'] = $onLineRes['scale'];
        //在线预订人数
        $data['online_ticket'] = $onLineRes['on_ticket'];
        //线下预订人数
        $data['offline_ticket'] = $onLineRes['off_ticket'];
        //在线预订比例数分析 -- END

        return $data;
    }

    /**
     * 获取出行人数（近三十天统计）
     * <AUTHOR>
     * @date   2017-4-5
     */
    private function _getTripNumbers()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));
        $key  = $this->_redisPreKey . 'trip:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new PftBigTripNumbers();
            $res   = $model->getDataByTime($defaultBegin, $defaultEnd);

            $levelOne = $levelTwo = $levelThree = $levelFour = 0;

            if (!empty($res)) {
                foreach ($res as $item) {
                    $levelOne += $item['level_one'];
                    $levelTwo += $item['level_two'];
                    $levelThree += $item['level_three'];
                    $levelFour += $item['level_four'];
                }
            }

            $total = $levelOne + $levelTwo + $levelThree + $levelFour;
            $count = [
                'levelOne'   => $levelOne / $total,
                'levelTwo'   => $levelTwo / $total,
                'levelThree' => $levelThree / $total,
                'levelFour'  => $levelFour / $total,
            ];

            $data = json_encode($count);

            if ($readCache) {
                $this->_redis->set($key, $data, '', 3600 * 24);
            }
        }

        $result = json_decode($data, true);

        return $result;
    }

    /**
     * 获取近7天景区排行top10(按供应商排行)
     * <AUTHOR>
     * @date   2018-03-22
     */
    private function _getSupplierTop()
    {
        $begin = I('post.begin');
        $end   = I('post.end');

        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        //默认的起止时间
        $defaultBegin = date('Ymd', strtotime("-7 day"));
        $defaultEnd   = $time   = date('Ymd', strtotime("-1 day"));
        $key          = $this->_redisPreKey . 'suppliertop:' . $time;

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new Statistics();
            $res   = $model->getSupplierTopByDate($defaultBegin, $defaultEnd);
            $data  = [];

            if (!empty($res)) {
                $fidArr      = array_column($res, 'fid');
                $memberModel = new Member();
                $memberInfo  = $memberModel->getMemberInfoByMulti($fidArr, 'id', 'id, dname');
                foreach ($memberInfo as $item) {
                    $memberRes[$item['id']] = $item['dname'];
                }
                foreach ($res as $item) {
                    $data[] = [
                        'name'   => isset($memberRes[$item['fid']]) ? $memberRes[$item['fid']] : '未知',
                        'order'  => $item['order_num'],
                        'ticket' => $item['ticket_num'],
                    ];
                }

                $data = json_encode($data, true);

                if ($readCache) {
                    $key = $this->_redisPreKey . 'suppliertop:' . $time;
                    $this->_redis->set($key, $data, '', 3600 * 24);
                }
            } else {
                $data = json_encode($data, true);
            }
        }

        $result = json_decode($data, true);

        return $result;
    }

    /**
     * 产品排行
     */
    private function _getProductTop()
    {
        $begin = I('post.begin');
        $end   = I('post.end');

        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        //需要做缓存的一些时间
        $defaultBegin = $time = date('Ymd', strtotime("-7 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));
        $today        = date('Ymd', time());
        $monthAgo     = date('Ymd', strtotime("-30 day"));
        $thisYear     = date('Y', time()) . '0101';

        $key          = $this->_redisPreKey . 'producttop:' . $defaultBegin;
        $lastBeginKey = $this->_redisPreKey . 'producttop:lastbegin:';
        $lastEndKey   = $this->_redisPreKey . 'producttop:lastend';
        $expire       = 3600 * 2;

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //记录上一次查询时间的数据
            $lastBegin = $this->_redis->get($lastBeginKey);
            $lastEnd   = $this->_redis->get($lastEndKey);

            //如果选择的时间是前7天，最近7天，最近30天，今年的话，走缓存
            if ($begin == $defaultBegin && $end == $defaultEnd) {
                $key    = $this->_redisPreKey . 'producttop:' . $defaultBegin;
                $expire = 3600 * 24;
            } elseif ($begin == $defaultBegin && $end == $today) {
                $key = $this->_redisPreKey . 'producttop:' . $today;
            } elseif ($begin == $monthAgo && $end == $today) {
                $key = $this->_redisPreKey . 'producttop:' . $monthAgo;
            } elseif ($begin == $thisYear && $end == $today) {
                $key = $this->_redisPreKey . 'producttop:' . $thisYear;
            } elseif ($begin == $lastBegin && $end == $lastEnd) {
                $key = $this->_redisPreKey . 'producttop:' . $lastBegin . $lastEnd;
            } else {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        } else {
            $expire = 3600 * 24;
        }

        $this->_redis->set($lastBeginKey, $defaultBegin, '', $expire);
        $this->_redis->set($lastEndKey, $defaultEnd, '', $expire);

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new Statistics();
            //获取总数
            $total = $model->getProductTopByDateTotal($defaultBegin, $defaultEnd);
            $num   = 100000;
            $landData  = [];

            $landModel = new Land();
            //取出的数据 分页取 $i页数 $num 条数
            for ($i = 1; $i <= ceil($total / $num); $i++) {
                $tmpData  = $model->getProductTopByDate($defaultBegin, $defaultEnd, '', $i, $num);
                //取出景区id 资源id映射数据
                $lidArr       = array_column($tmpData, 'lid');
                $javaAPi      = new \Business\CommodityCenter\Land();
                $landRes      = $javaAPi->queryLandMultiQueryById($lidArr);
                $resourceList = array_column($landRes, 'resourceID', 'id');

                //景区订单数据添加resourceID 参数
                foreach ($tmpData as $k => $value){
                    if(!empty($resourceList[$value["lid"]])){
                        $tmpData[$k]['resourceID'] = $resourceList[$value["lid"]];
                        unset($tmpData[$k]["lid"]); //删掉lid避免数组太大
                    }else{
                        unset($tmpData[$k]);//删掉没有资源的景区
                    }
                }
                $landData = array_merge($landData, $tmpData); //景区订单数据
            }

            $resourceData = [];//初始化数组
            //计算去重
            foreach ($landData as $k => $v){
                $orderNum  = (int)$v['order_num'];
                $ticketNum = (int)$v['ticket_num'];
                if($resourceData[$v["resourceID"]]){
                    $resourceData[$v["resourceID"]]['order_num'] += $orderNum;
                    $resourceData[$v["resourceID"]]['ticket_num'] += $ticketNum;
                }else{
                    $resourceData[$v["resourceID"]] = $v;
                }
            }

            //order_num 排序
            array_multisort(array_column($resourceData,'order_num'), SORT_DESC, $resourceData);

            if(!empty($resourceData)){
                $data = array_slice($resourceData, 0, 50);
                $resourceidArr   = array_column($data, 'resourceID');//资源id

                //获取资源名称
                $ResourceModel = new \Model\Product\LandResource();
                $resourceRes = $ResourceModel->getResourceListByIdArr($resourceidArr, 'id,title');

                //数组重组成旧版本参数
                foreach ($data as $k => $value){
                    if(isset($resourceRes[$value["resourceID"]])){
                        $data[$k]['name'] = (string)$resourceRes[$value["resourceID"]];
                    }else{
                        $data[$k]['name'] = '未知';
                    }
                    $data[$k]['order'] = $value['order_num'];
                    $data[$k]['ticket'] = $value['ticket_num'];
                    unset($data[$k]['resourceID']);
                    unset($data[$k]['order_num']);
                    unset($data[$k]['ticket_num']);
                }

                $data = json_encode($data);

                if ($readCache) {
                    $this->_redis->set($key, $data, '', $expire);
                }
            }
        }

        $result = json_decode($data, true);
        return $result;
    }

    /**
     * 获取游客分析地（近30天）
     * <AUTHOR>
     * @date   2017-4-14
     */
    private function _getTouristPlace()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));
        $time         = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        $provinceNameReturn = [];
        $cityNameReturn     = [];

        if ($readCache) {
            $key  = $this->_redisPreKey . 'place:' . $time;
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new PftBigTouristPlace();

            // 初始化统计容器
            $agg = [
                'province' => [],
                'city'     => [],
            ];

            // 按天循环查询并累加
            for ($ts = strtotime($defaultBegin); $ts <= strtotime($defaultEnd); $ts += 86400) {
                $curDate = date('Ymd', $ts);
                $tmp     = $model->getInfoByDate($curDate, $curDate);

                // 累加省份数据
                if (!empty($tmp['province'])) {
                    foreach ($tmp['province'] as $item) {
                        $code = $item['code'];
                        if (!isset($agg['province'][$code])) {
                            $agg['province'][$code] = [
                                'code'   => $code,
                                'num'    => 0,
                                'ticket' => 0,
                            ];
                        }
                        $agg['province'][$code]['num']    += (int)$item['num'];
                        $agg['province'][$code]['ticket'] += (int)$item['ticket'];
                    }
                }

                // 累加城市数据
                if (!empty($tmp['city'])) {
                    foreach ($tmp['city'] as $item) {
                        $code = $item['code'];
                        if (!isset($agg['city'][$code])) {
                            $agg['city'][$code] = [
                                'code'   => $code,
                                'num'    => 0,
                                'ticket' => 0,
                            ];
                        }
                        $agg['city'][$code]['num']    += (int)$item['num'];
                        $agg['city'][$code]['ticket'] += (int)$item['ticket'];
                    }
                }
            }

            // 将累加结果转换为索引数组
            $res = [
                'province' => array_values($agg['province']),
                'city'     => array_values($agg['city']),
            ];

            $data = json_encode($res);

            //固化
            if ($readCache) {
                $key = $this->_redisPreKey . 'place:' . $time;
                $this->_redis->set($key, $data, '', 3600 * 24);
            }
        }

        $result = json_decode($data, true);

        $cityName       = load_config('tel_city', 'account');
        $provinceName   = load_config('tel_province', 'account');
        $idCardProvince = load_config('id_card_province', 'account');
        $idCardCity     = load_config('id_card_city', 'account');

        if (!empty($result['province'])) {
            foreach ($result['province'] as $item) {
                if (strlen($item['code']) == 4 && isset($idCardProvince[substr($item['code'], 0, 2)])) {
                    // 因为身份证编码是4位,所以直接判断key
                    $provinceNameReturn[] = [
                        'name'   => $idCardProvince[substr($item['code'], 0, 2)],
                        'num'    => intval($item['num']),
                        'ticket' => intval($item['ticket']),
                    ];
                } elseif (isset($provinceName[$item['code']])) {
                    $provinceNameReturn[] = [
                        'name'   => $provinceName[$item['code']],
                        'num'    => intval($item['num']),
                        'ticket' => intval($item['ticket']),
                    ];
                }
            }
        }

        if (!empty($result['city'])) {
            foreach ($result['city'] as $item) {
                if (strlen($item['code']) == 4 && isset($idCardCity[$item['code']])) {
                    $cityNameReturn[] = [
                        'name'   => $idCardCity[$item['code']],
                        'num'    => intval($item['num']),
                        'ticket' => intval($item['ticket']),
                    ];
                } elseif (isset($cityName[$item['code']])) {
                    $cityNameReturn[] = [
                        'name'   => $cityName[$item['code']],
                        'num'    => intval($item['num']),
                        'ticket' => intval($item['ticket']),
                    ];
                }
            }
        }

        // 因为身份证跟手机号保存的code不同，所以会有2份数据，进行处理整合成一份数据, 因为整理后的数据顺序可能会改变，所以重新进行排序
        $res     = [];
        $sortkey = [];
        foreach ($provinceNameReturn as $province) {
            if (!isset($res[$province['name']])) {
                $res[$province['name']] = $province;
                $sortkey[]              = intval($province['ticket']);
            } else {
                $res[$province['name']]['num'] += intval($province['num']);
                $res[$province['name']]['ticket'] += intval($province['ticket']);
            }
        }
        $provinceNameReturn = array_values($res);
        $provinceNameReturn = $this->twoArraySort($provinceNameReturn, 'ticket');
//        array_multisort($sortkey, SORT_DESC, $provinceNameReturn);
        $res     = [];
        $sortkey = [];
        foreach ($cityNameReturn as $city) {
            if (!isset($res[$city['name']])) {
                $res[$city['name']] = $city;
                $sortkey[]          = intval($city['ticket']);
            } else {
                $res[$city['name']]['num'] += intval($city['num']);
                $res[$city['name']]['ticket'] += intval($city['ticket']);
            }
        }
        $cityNameReturn = array_values($res);
        $cityNameReturn = $this->twoArraySort($cityNameReturn, 'ticket');
//        array_multisort($sortkey, SORT_DESC, $cityNameReturn);
        return ['province' => $provinceNameReturn, 'city' => $cityNameReturn];
    }

    /**
     * 二维数组排序
     * Create by zhangyangzhen
     * Date: 2018/6/4
     * Time: 16:04
     * @param $arrays
     * @param $sort_key
     * @param int $sort_order
     * @param int $sort_type
     * @return bool
     */
    private function twoArraySort($arrays, $sort_key, $sort_order = SORT_DESC, $sort_type = SORT_NUMERIC)
    {
        if (is_array($arrays)) {
            foreach ($arrays as $array) {
                if (is_array($array)) {
                    $key_arrays[] = $array[$sort_key];
                } else {
                    return false;
                }
            }
        } else {
            return false;
        }
        array_multisort($key_arrays, $sort_order, $sort_type, $arrays);
        return $arrays;
    }

    /**
     * 获取景区数量/产品数量/供应商数量/分销商数量/旅行社数量 (当年累计)
     * <AUTHOR>
     * @date   2017-4-18
     */
    private function _getStatistics()
    {
        $time = date('Ymd', strtotime("-1 day"));
        $key  = $this->_redisPreKey . 'statistics:' . $time;
        $data = $this->_redis->get($key);

        if (empty($data)) {
            $model = new PftBigStatistics();
            $res   = $model->getData();
            if (!empty($res)) {
                $orderNumTotal = $res['on_line'] + $res['off_line'];
                $scale         = $orderNumTotal ? $res['on_line'] / $orderNumTotal : 0;
                $data          = [
                    //景区数量
                    'land'         => isset($res['land']) ? $res['land'] : 0,
                    //产品数量
                    'product'      => isset($res['product']) ? $res['product'] : 0,
                    //供应商数量
                    'supplier'     => isset($res['supplier']) ? $res['supplier'] : 0,
                    //分销商数量
                    'distributor'  => isset($res['distributor']) ? $res['distributor'] : 0,
                    //旅行社数量
                    'travel'       => isset($res['travel']) ? $res['travel'] : 0,
                    //在线预定比例
                    'online_scale' => $scale,
                ];

                $data = json_encode($data);
                $key  = $this->_redisPreKey . 'statistics:' . $time;

                $this->_redis->set($key, $data, '', 3600 * 24);
            }
        }

        $result = json_decode($data, true);

        return $result;
    }

    /**
     * 获取年度销售金额 从验证报表中获取
     * <AUTHOR>
     * @date   2017-4-18
     */
    private function _getSaleMoneyYear()
    {
        $bigDataBiz = new BigDataPanelBiz();
        $data       = $bigDataBiz->getYearSaleMoney($isSuper = true);
        return $data;
    }

    /**
     * 获取全销售时间段的游客数量 从验证报表取
     * @return int
     */
    private function _getTicketNum()
    {
        $bigDataBiz = new BigDataPanelBiz();
        $data       = $bigDataBiz->getYearTicektnum($dataType = 1, $isSuper = true);
        return $data;
    }

    /**
     * 获取天气
     * API接口文档地址 - https://www.heweather.com/douments/api/s6/weather-forecast
     * Create by zhangyangzhen
     * Date: 2018/9/26
     * Time: 18:11
     * @return bool|mixed
     */
    private function _getWeather()
    {
        $city = I('post.weather_city', '', 'strval');

        if (empty($city)) {
            $city = self::__DEFAULT_CITY__;
        }

        if (empty($city)) {
            //获取城市
            $opts = [
                'http' => [
                    'method'  => "GET",
                    'timeout' => 15,
                ],
            ];

            $context = stream_context_create($opts);
            $return  = file_get_contents($this->_getIpUrl, false, $context);
            $return  = json_decode($return, true);
            $city    = is_array($return) && isset($return['city']) ? $return['city'] : '';

            if (empty($city) || !$city) {
                return false;
            }
        }

        $query = false;

        $pinYinClass = new Letter();
        $letters     = $pinYinClass->convertInitalPinyin($city);

        $time     = date('Ymd', strtotime("-1 day"));
        $redisKey = $this->_redisPreKey . "weather:{$letters}:" . $time;
        $res      = $this->_redis->get($redisKey);

        if (empty($res)) {
            $query = true;
            $res   = Helpers::getWeather($city);
        }

        $res = json_decode($res, true);

        if ($res['HeWeather6'][0]['status'] == 'ok') {
            //保存 用户查询地址
            if ($query) {
                $this->_redis->set($redisKey, json_encode($res), '', 3600 * 24);
            }
        }

        return $res;
    }

    /**
     * 今年预订
     */
    private function _yearOrder()
    {
        //获取截止到昨天的数据
        $bigDataBiz = new BigDataPanelBiz();
        $data       = $bigDataBiz->getYearTicektnum($dataType = 2, $isSuper = true);

        //获取今天的数据
        $statisticsModel = new Statistics();
        $num             = $statisticsModel->getOrderToday();

        //两者相加
        $total = $num + $data;
        return $total;
    }

    /**
     * 今年检测
     */
    private function _yearCheck()
    {
        //获取截止到昨天的数据  和  累积的接待游客数一致
        $bigDataBiz = new BigDataPanelBiz();
        $data       = $bigDataBiz->getYearTicektnum($dataType = 1, $isSuper = true);

        //获取今天的数据
        $statisticsModel = new Statistics();
        $num             = $statisticsModel->getCheckToday();

        //两者相加
        $total = $num + $data;
        return $total;
    }

    /**
     * 分销渠道 默认三十天
     */
    private function _getChannel()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));
        $key  = $this->_redisPreKey . 'channel:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new PftBigDistriChannel();
            $data  = $model->getInfoByDate($defaultBegin, $defaultEnd, $memberId = '');
            $data  = json_encode($data, true);
            if ($readCache) {
                $this->_redis->set($key, $data, '', 3600 * 24);
            }
        }

        $data = json_decode($data, true);

        return $data;
    }

    /**
     * 分销渠道 默认三十天 , 统计票数
     */
    private function _getChannelCountTicket()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));
        $key  = $this->_redisPreKey . 'channel:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new PftBigDistriChannel();
            $data  = $model->getInfoByDateCountTicket($defaultBegin, $defaultEnd, $memberId = '');
            $data  = json_encode($data, true);
            if ($readCache) {
                $this->_redis->set($key, $data, '', 3600 * 24);
            }
        }

        $data = json_decode($data, true);

        return $data;
    }

    /**
     * 获取OTA分销排行
     */
    private function _getOtaReseller()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));
        $key  = $this->_redisPreKey . 'reseller:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $model = new PftBigDistriChannel();
            $data  = $model->getResellerRankCountTicket($defaultBegin, $defaultEnd);
            $data  = json_encode($data);
            $key   = $this->_redisPreKey . 'reseller:' . $time;
            $this->_redis->set($key, $data, '', 3600 * 24);
        }

        $data   = json_decode($data, true);

        return $data;
    }

    /**
     * 销售趋势
     */
    private function _getSaleTrend()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (!strtotime($begin) || !strtotime($end)) {
            $bTimeNew = date('Y-m-d', time() - 3600 * 24 * 6);
            $eTimeNew = date('Y-m-d', time());
        } else {
            $bTimeNew = date('Y-m-d', strtotime($begin));
            $eTimeNew = date('Y-m-d', strtotime($end));
        }
        $homeOrderBusiness = new HomeOrder();

        $bTimeOld = date('Y-m-d', strtotime('-1 year', strtotime($bTimeNew)));
        $eTimeOld = date('Y-m-d', strtotime('-1 year', strtotime($eTimeNew)));
        $num      = $homeOrderBusiness->saleTrends($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, 1);
        $money    = $homeOrderBusiness->saleTrends($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, 2);

        return ['num' => $num, 'money' => $money];
    }

    /**
     * 轨迹分析
     */
    private function _getTrailData()
    {
        $trailModel = new PftBigTrail();
        $data       = $trailModel->getData();

        return $data;
    }

    /**
     * 获取今天的每小时的预订统计量
     */
    private function _hourOrder()
    {
        $date = I('post.date');
        if (!strtotime($date)) {
            $date = '';
        } else {
            $date = date('Ymd', strtotime($date));
        }

        $result = [];
        $model  = new PftBigHourData();
        $data   = $model->getTodayOrderData($date);
        foreach ($data as $item) {
            $result[$item['hour']]['order']  = $item['order'];
            $result[$item['hour']]['ticket'] = $item['ticket'];
        }
        return $result;
    }

    /**
     * 获取今天的每小时的检票统计量
     */
    private function _hourCheck()
    {
        $date = I('post.date');
        if (!strtotime($date)) {
            $date = '';
        } else {
            $date = date('Ymd', strtotime($date));
        }

        $result = [];
        $model  = new PftBigHourData();
        $data   = $model->getTodayCheckData($date);
        foreach ($data as $item) {
            $result[$item['hour']]['order']  = $item['order'];
            $result[$item['hour']]['ticket'] = $item['ticket'];
        }
        return $result;
    }

    /**
     * 获取性别分析数据
     */
    private function _getSexData()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $data  = [];
        $model = new PftBigSex();
        $time  = date('Ymd', strtotime("-1 day"));
        $key   = $this->_redisPreKey . 'sex:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            // 初始化累加器
            $data = [
                '1' => 0, // 男
                '2' => 0, // 女
            ];

            // 按天循环查询并累加
            for ($ts = strtotime($defaultBegin); $ts <= strtotime($defaultEnd); $ts += 86400) {
                $curDate = date('Ymd', $ts);
                $tmp     = $model->getDataByTime($curDate, $curDate);

                if (!empty($tmp)) {
                    foreach ($tmp as $sex => $ticket) {
                        if (!isset($data[$sex])) {
                            $data[$sex] = 0;
                        }
                        $data[$sex] += (int)$ticket;
                    }
                }
            }

            //固化
            if ($readCache) {
                $value = json_encode($data);
                $this->_redis->set($key, $value, '', 3600 * 24);
            }
        }

        return $data;
    }

    /**
     * 获取年龄分析数据
     */
    private function _getAgeData()
    {
        $begin = I('post.age_begin');
        $end   = I('post.age_end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $data  = [];
        $model = new PftBigAge();
        $time  = date('Ymd', strtotime("-1 day"));
        $key   = $this->_redisPreKey . 'age:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            // 初始化累加数组
            $sumData = [
                'level_one'          => 0,
                'level_two'          => 0,
                'level_three'        => 0,
                'level_four'         => 0,
                'level_five'         => 0,
                'level_one_ticket'   => 0,
                'level_two_ticket'   => 0,
                'level_three_ticket' => 0,
                'level_four_ticket'  => 0,
                'level_five_ticket'  => 0,
            ];

            // 按天循环查询并累加
            for ($ts = strtotime($defaultBegin); $ts <= strtotime($defaultEnd); $ts += 86400) {
                $curDate = date('Ymd', $ts);
                $tmp     = $model->getDataByTime($curDate, $curDate);

                if (!empty($tmp)) {
                    foreach ($sumData as $field => $val) {
                        if (isset($tmp[$field])) {
                            $sumData[$field] += (int)$tmp[$field];
                        }
                    }
                }
            }

            $data = json_encode($sumData);

            //固化
            if ($readCache) {
                $this->_redis->set($key, $data, '', 3600 * 24);
            }
        }

        if (empty($data)) {
            $total = 0;
        } else {
            $data = json_decode($data, true);

            $total = $data['level_one_ticket'] + $data['level_two_ticket'] + $data['level_three_ticket'] +
                $data['level_four_ticket'] + $data['level_five_ticket'];
        }

        if (empty($total)) {
            $result = [
                //0-6岁
                'level_one'   => 0,
                //7-17岁
                'level_two'   => 0,
                //18-40
                'level_three' => 0,
                //41-65
                'level_four'  => 0,
                //66以后
                'level_five'  => 0,
            ];
        } else {
            $result = [
                //0-6岁
                'level_one'   => $data['level_one_ticket'] / $total,
                //7-17岁
                'level_two'   => $data['level_two_ticket'] / $total,
                //18-40
                'level_three' => $data['level_three_ticket'] / $total,
                //41-65
                'level_four'  => $data['level_four_ticket'] / $total,
                //66以后
                'level_five'  => $data['level_five_ticket'] / $total,
            ];
        }

        return $result;
    }

    private function _getBookChannelRank()
    {
        $begin = I('post.begin');
        $end   = I('post.end');
        if (($begin && !strtotime($begin)) || ($end && !strtotime($end))) {
            return [];
        }

        $time = date('Ymd', strtotime("-1 day"));
        $key  = $this->_redisPreKey . 'bookchannel:' . $time;

        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));

        $readCache = true;
        if ($begin && $end) {
            //选择的开始和结束时间
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));

            //如果选择的时间和默认时间一样 优先读取缓存
            if ($begin != $defaultBegin || $end != $defaultEnd) {
                $readCache = false;
            }

            $defaultBegin = $begin;
            $defaultEnd   = $end;
        }

        if ($readCache) {
            $data = $this->_redis->get($key);
        }

        if (empty($data)) {
            $statisticsModel = new Statistics();
            $data            = $statisticsModel->getBookChannelRank($defaultBegin, $defaultEnd);
            $data            = json_encode($data);
            $key             = $this->_redisPreKey . 'bookchannel:' . $time;
            $this->_redis->set($key, $data, '', 3600 * 24);
        }

        $data = json_decode($data, true);
        return $data;
    }
}
