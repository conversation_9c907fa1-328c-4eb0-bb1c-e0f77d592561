<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON> Lan
 * Date: 2017/7/13
 * Time: 16:26
 */

namespace Controller\BigDataPanel;

use Business\Order\OrderList;
use Library\Controller;
use Model\Report\Statistics;
use Model\Product\Land;
use Model\DataCollection\DataCollection;
use \Library\Cache\Cache;

class ParkSummary extends Controller
{

    private $_loginInfo;
    private $_memberId;
    private $_sid;
    private $_memberType;
    private $_account;
    private $_landId;
    private $_landModel;
    private $_landIdKey;
    private $_landIdArr;
    private $_specialToNew = [6378989];

    /**
     * @var array 不需要登录校验的接口白名单
     */
    private $whiteDataMethodList = [
        'getData',
    ];

    private $spcAccountList = [
        '1a18aafa48ded7c6c364115183109a3b', //589253 扬州市何园管理处  591906
        '7d08885331177bf9d8dc151d987b5881', //589250 扬州市个园管理处  591889
        'c39ffd5af04f6f7565decb03f592bf01', //589277 扬州市茱萸湾风景区管理处（扬州动物园）592024
        'f7704bcbda248fdc2b42df317cb72ae7', //123624  3385
    ];

    public function __construct()
    {
        // 判断如果有data的参数就不用登陆判断
        // 1、扬州市何园管理处：589253
        // 2、扬州市个园管理处：589250
        // 3、扬州市茱萸湾风景区管理处（扬州动物园）：589277
        if (in_array($_GET['a'], $this->whiteDataMethodList) && !empty($_GET['data']) && in_array($_GET['data'], $this->spcAccountList)) {
            $this->_loginInfo['sdtype'] = 0;

            switch ($_GET['data']) {
                case '1a18aafa48ded7c6c364115183109a3b':
                    $this->_loginInfo['sid'] = 591906;
                break;
                case '7d08885331177bf9d8dc151d987b5881':
                    $this->_loginInfo['sid'] = 591889;
                break;
                case 'c39ffd5af04f6f7565decb03f592bf01':
                    $this->_loginInfo['sid'] = 592024;
                break;
                case 'f7704bcbda248fdc2b42df317cb72ae7':
                    $this->_loginInfo['sid'] = 3385;
                break;
            }
        } else {
            $loginInfo         = $this->getLoginInfo();
            $this->_loginInfo  = $loginInfo;
            $this->_account    = $loginInfo['account'];
            $this->_memberId   = $loginInfo['memberID'];
            $this->_sid        = $loginInfo['sid'];
            $this->_memberType = $loginInfo['dtype'];
            $this->_landModel  = new Land();
    
            $landInfo         = $this->_landModel->getListByResource($this->_account, $this->_memberType);
            $this->_landIdKey = md5(json_encode(array_column($landInfo, 'id')));
            $this->_landIdArr = array_column($landInfo, 'id');
            $this->_landId    = $landInfo[0]['id'] ?? 0;
        }
    }

    /**
     * 获取园区数据-日数据
     *
     * @date   2018-06-12
     *
     * @return void
     */
    public function getData()
    {
        $start = I('post.start_time', '', 'strval');
        $end   = I('post.end_time', '', 'strval');
        //0旧接口，1新接口
        $isToNewSummary = I('post.is_new', 0, 'intval');
		$chooseAppointNum = I('post.choose_appoint_num', 1, 'intval');
        //地址栏带过来的配置id
        $configId = I('config_id', 0, 'intval');
        if (!empty($start) && !empty($end) && strtotime($start) && strtotime($end)) {
            $dayBeginTime = date('Y-m-d H:i:s', strtotime($start));
            $dayEndTime   = date('Y-m-d H:i:s', strtotime($end));
        } else {
            $dayBeginTime = date('Y-m-d 04:00:00');
            $dayEndTime   = date('Y-m-d 23:59:59');
        }
        $params = [
            'isToNewSummary' => $isToNewSummary,
            'chooseAppointNum' => $chooseAppointNum,
            'configId' => $configId,
            'dayBeginTime' => $dayBeginTime,
            'dayEndTime' => $dayEndTime,
            'loginInfo' => $this->_loginInfo,
            'landIdArr' => $this->_landIdArr,
        ];
        $biz = new \Business\BigDataPanel\ParkSummary();
        $data = $biz->getEnterAndOutDataForPanel($params);
        $this->apiReturn(self::CODE_SUCCESS, $data, 'success');
    }

    /**
     * 获取园区数据-画线
     *
     * @date   2017-07-14
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function getLine()
    {
        $dateType = I('post.type', 2, 'intval');//1=日，2=周，3=月

        switch ($dateType) {
            case 2:
                $beginTime = date('Y-m-d 04:00:00', strtotime('Sunday -6 day'));
                break;

            case 3:
                $beginTime = date('Y-m-01 04:00:00');
                break;

            default:
                $beginTime = date('Y-m-d 04:00:00');
                break;
        }

        $endTime = date('Y-m-d 23:59:59');

        // $this->_landId = 14610;
        //$this->_landId = 14688;

        $collectionModel = new DataCollection();

        $lineIn = self::getCache($this->_landIdKey . ':' . $dateType, 'lineIn');
        $lineIn = unserialize($lineIn);

        $lineOut = self::getCache($this->_landIdKey . ':' . $dateType, 'lineOut');
        $lineOut = unserialize($lineOut);

        if ($dateType == 1) {
            if (!$lineIn) {
                $lineIn = $collectionModel->getEnterData($beginTime, $endTime, $this->_landIdArr,
                    'time, leave_num as num');
                if ($lineIn) {
                    $lineIn = $this->handleData($lineIn);
                    self::setCache($this->_landIdKey . ':' . $dateType, 'lineIn', serialize($lineIn));
                }
            }

            if (!$lineOut) {
                $lineOut = $collectionModel->getLeaveData($beginTime, $endTime, $this->_landIdArr,
                    'time, leave_num as num');
                if ($lineOut) {
                    $lineOut = $this->handleData($lineOut);
                    self::setCache($this->_landIdKey . ':' . $dateType, 'lineOut', serialize($lineOut));
                }
            }

        } else {
            if (!$lineIn) {
                //非日就减去5个小时，因为脚本是3点多汇总统计的
                $lineIn = $collectionModel->getEnterData($beginTime, $endTime, $this->_landIdArr,
                    'time, leave_num as num');

                if ($lineIn) {
                    $lineIn = $this->handleData($lineIn, true);
                    self::setCache($this->_landIdKey . $dateType, 'lineIn', serialize($lineIn));
                }
            }

            if (!$lineOut) {
                $lineOut = $collectionModel->getLeaveData($beginTime, $endTime, $this->_landIdArr,
                    'time, leave_num as num');
                if ($lineOut) {
                    $lineOut = $this->handleData($lineOut, true);
                    self::setCache($this->_landIdKey . $dateType, 'lineOut', serialize($lineOut));
                }
            }
        }

        $data = [
            'lineIn'  => $lineIn ?: 0,
            'lineOut' => $lineOut ?: 0,
        ];
        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 设置园区数据
     *
     * @date   2017-07-14
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function setLand()
    {
        $maxPeople = I('post.max_people', 0, 'intval');
        $warning   = I('post.warning', 0, 'intval');
        if ($maxPeople <= 0 || $warning <= 0 || $warning > 100) {
            $this->apiReturn(parent::CODE_PARAM_ERROR, [], '参数错误');
        }

        //先查询出更新接口所需要的参数
        $landDetail = $this->_landModel->getLandInfo($this->_landId);
        if (!$landDetail) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '景区信息错误');
        }

        $area = explode('|', $landDetail['area']);
        //保存的参数整理下
        $saveData = [
            'id'               => $this->_landId,
            //'operaterId'       => $this->_memberId,
            'operaterId'       => $landDetail['apply_did'],
            'title'            => $landDetail['title'],
            'applyDid'         => $landDetail['apply_did'],
            'status'           => $landDetail['status'],
            'jqts'             => $landDetail['jqts'],
            'bhjq'             => $landDetail['bhjq'],
            'imgpath'          => $landDetail['imgpath'],
            'pType'            => $landDetail['p_type'],
            'receptionAbility' => $maxPeople,
            'warnPercent'      => $warning,
            'provinceId'       => $area[0],
            'cityId'           => $area[1],
        ];

        //调用Java统一更新接口
        $landJavaBis = new \Business\JavaApi\CommodityCenter\Land();
        $result      = $landJavaBis->updateLand($saveData);
        if ($result['code'] != 200) {
            $this->apiReturn(201, [], 'fail');
        }
        $this->apiReturn(200, [], 'success');
        //$result = $this->_landModel->updateLandByLandId($this->_landId, $maxPeople, $warning);

        //if ($result) {
        //    $this->apiReturn(200, [], 'success');
        //} else {
        //    $this->apiReturn(201, [], 'fail');
        //}
    }

    /**
     * 获取景区配置数据
     *
     * @date   2017-07-14
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function getLandInfo()
    {
        //$landModel = new Land();
        //$result    = $landModel->getInfoInLand('reception_ability as max_people, warn_percent as warning',
        //    ['id' => $this->_landId]);

        $landApi = new \Business\CommodityCenter\Land();
        $result  = $landApi->queryLandMultiQueryById([$this->_landId]);
        if ($result) {
            $return = [];
            foreach ($result as $item) {
                $return[] = [
                    'max_people' => $item['reception_ability'],
                    'warning'    => $item['warn_percent'],
                ];
            }
            $this->apiReturn(200, $return, 'success');
        } else {
            $this->apiReturn(201, [], 'fail');
        }
    }

    /**
     * 获取该园区的缓存数据
     *
     * @date   2017-07-13
     * <AUTHOR> Lan
     *
     * @param  int  $landId  景区ID
     * @param  string  $key  获取数据的类型
     *
     * @return string
     */
    private static function getCache($landId, $key)
    {
        $redis = Cache::getInstance('redis');
        $key   = 'park:' . $key . $landId;
        $redis->rm($key);

        return $redis->get($key);
    }

    /**
     * 设置该园区的缓存数据
     *
     * @date   2017-07-13
     * <AUTHOR> Lan
     *
     * @param  int  $landId  景区ID
     * @param  string  $key  获取数据的类型
     * @param  int  $data  缓存数据
     *
     * @return array|bool
     */
    private static function setCache($landId, $key, $data)
    {
        $redis = Cache::getInstance('redis');
        $key   = 'park:' . $key . $landId;

        return $redis->set($key, $data, '', 1800);
    }

    /**
     * 参数处理
     *
     * @date   2017-07-14
     * <AUTHOR> Lan
     *
     * @param  array  $data  待处理的参
     * @param  bool  $ifDay  是否按天统计
     *
     * @return array
     */
    private static function handleData($data, $ifDay = false)
    {
        $tempArr = [];
        if ($ifDay) {
            foreach ($data as $val) {
                $time           = strtotime(date('Y-m-d 12:00:00', $val['time']));
                $tempArr[$time] += intval($val['num']);
            }
        } else {
            foreach ($data as $val) {
                $tempArr[$val['time']] += intval($val['num']);
            }
        }

        return $tempArr;
    }

    /**
     * 获取婺源数据
     *
     * @param  int  sid  供应商id
     * User: Liucm
     * Date: 2021/2/22
     * Time: 18:55
     */
    public function getWuYuanData()
    {
        $sId = $this->_loginInfo['sid'];
        if (empty($sId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误,请去12301后台配置出入园数据和页面样式');
        }
        $model      = new \Model\BigDataPanel\PftPanelConfig();
        $ConfigData = $model->getPanelConfigData($sId);

        if (empty($ConfigData)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '获取配置错误,请去12301后台配置出入园数据和页面样式');
        }
        if (isset($ConfigData['state']) && $ConfigData['state'] != 1) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '配置已失效,请去12301后台配置出入园数据和页面样式');
        }

        $customText = json_decode($ConfigData['custom_text'], true);

        $hour = $customText['enter_num']['number'] ?? 0;

        if (($hour < 1) && $hour != 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '获取时间配置错误,请去12301后台配置出入园数据和页面样式');
        }

        $dayEndTime   = time();
        $dayBeginTime = $dayEndTime - $hour * 3600;
        if ($hour == 0 ){
            $dayBeginTime = strtotime(date('Y-m-d 00:00:01'));
        }
        $today        = strtotime(date("Y-m-d"), time());
        if ($dayBeginTime < $today) {
            $dayBeginTime = $today;
        }
        $collectionModel = new DataCollection();
        $deviceKeyArr    = explode(',', $ConfigData['key_id']);
        //$macAddress      = explode(',', $ConfigData['mac_address']);
        //取自定义闸机数据
        if ($ConfigData['enter_type'] == 2) {
            $deviceEnterNum = $collectionModel->getDeviceEnterDataByTimeAndKey($dayBeginTime, $dayEndTime,
                $deviceKeyArr, 0);
            $deviceEnterNum = $deviceEnterNum[0]['num'] ?? 0;
            //$CameraEnterNum = $collectionModel->getCameraDataCountByTimeAndMac($dayBeginTime, $dayEndTime, $macAddress,
            //    0);
            //$CameraEnterNum = $CameraEnterNum[0]['num'] ?? 0;
        } else {
            //获取供应商下全部入园数据
            $deviceEnterNum = $collectionModel->getEnterDataBySid($dayBeginTime, $dayEndTime, $sId, 0,
                'sum(num) as num');
            $deviceEnterNum = $deviceEnterNum[0]['num'] ?? 0;
        }
        if ($sId == 7920916){
            //获取摄像头数据
            $macAddress     = $this->defeaultMacAddress();
            $CameraEnterNum = $collectionModel->getCameraDataCountByTimeAndMac($dayBeginTime, $dayEndTime, $macAddress,
                0);
            $CameraEnterNum = $CameraEnterNum[0]['num'] ?? 0;
        }else{
            $CameraEnterNum = 0;
        }

        if ($deviceEnterNum < 1) {
            $deviceEnterNum = 0;
        }
        if ($CameraEnterNum < 1) {
            $CameraEnterNum = 0;
        }
        $customTextAttr = json_decode($ConfigData['custom_text_attr'], true);
        $size           = $customTextAttr['size'] ?? 32;
        $space          = $customTextAttr['space'] ?? 10;
        $titlecolor     = empty($customTextAttr['title_color']) ? '#fff' : $customTextAttr['title_color'];
        $numcolor       = empty($customTextAttr['num_color']) ? '#f90' : $customTextAttr['num_color'];
        $enterName      = $customText['enter_num']['value'];
        $enterNum       = $deviceEnterNum + $CameraEnterNum;
        $ensort         = $customText['enter_num']['sort'];
        $enstatus       = $customText['enter_num']['status'];
        $momentName     = $customText['moment_num']['value'];
        $momentNum      = $enterNum + (int)$customText['moment_num']['number'];
        $mosort         = $customText['moment_num']['sort'];
        $mostatus       = $customText['moment_num']['status'];
        $dayName        = $customText['day_num']['value'];
        $dayNum         = $customText['day_num']['number'];
        $daysort        = $customText['day_num']['sort'];
        $daystatus      = $customText['day_num']['status'];
        $data = [
            'config' => [
                'size'  => $size,
                'space' => $space,
                'title_color' => $titlecolor,
                'num_color'   => $numcolor,
            ],
            'data'   => [
                'enter_num'      => [
                    'name'   => $enterName,
                    'value'  => $enterNum,
                    'sort'   => $ensort,
                    'status' => $enstatus,
                ],
                'moment_num'     => [
                    'name'   => $momentName,
                    'value'  => $momentNum,
                    'sort'   => $mosort,
                    'status' => $mostatus,
                ],
                'day_num'        => [
                    'name'   => $dayName,
                    'value'  => $dayNum,
                    'sort'   => $daysort,
                    'status' => $daystatus,
                ],
                'custom'         => empty($customText['custom'])? [] : $customText['custom'],
                'deviceEnterNum' => $deviceEnterNum,
                'CameraEnterNum' => $CameraEnterNum,
            ],
        ];

        $this->apiReturn(self::CODE_SUCCESS, $data, 'success');
    }

    /**
     * 默认的mac地址
     * User: Liucm
     * Date: 2021/2/22
     * Time: 20:29
     * @return array
     */
    private function defeaultMacAddress()
    {
        return [
            '2c:a5:9c:68:ac:6a',
            '2c:a5:9c:68:ac:8d',
            '2c:a5:9c:68:ac:57',
        ];
    }
}
