<?php


/**
 * 出入园统计
 */

namespace Controller\BigDataPanel;

use Business\Report\InAndOutSummary as ReportInAndOutSummary;
use Library\Controller;

class InAndOutSummary extends Controller
{

    private $_sid = 0;

    public function __construct()
    {
        $this->_sid = $this->isLogin('ajax');
    }

    /**
     * 日汇总
     *
     * <AUTHOR>
     */
    public function daySummary()
    {
        $beginDate = I('begin_date', '');
        $endDate   = I('end_date', '');

        $summaryBiz = new ReportInAndOutSummary();
        $result = $summaryBiz->daySummary($this->_sid, $beginDate, $endDate);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
        
    }


    /**
     * 月汇总
     *
     * <AUTHOR>
     */
    public function monthSummary()
    {
        $beginMonth = I('begin_month', '');
        $endMonth   = I('end_month', '');

        $summaryBiz = new ReportInAndOutSummary();
        $result = $summaryBiz->monthSummary($this->_sid, $beginMonth, $endMonth);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 日报表
     *
     * <AUTHOR>
     */
    public function dayReport()
    {
        $beginDate = I('begin_date', '');
        $endDate   = I('end_date', '');

        $summaryBiz = new ReportInAndOutSummary();
        $result = $summaryBiz->hourSummary($this->_sid, $beginDate, $endDate);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

}