<?php

/**
 * 面板自定义配置
 * Class PanelConfig
 * liucm 2021/2/22 10:12
 * @package Controller\BigDataPanel
 */

namespace Controller\BigDataPanel;

use Library\Controller;

class PanelConfig extends Controller
{
    private $_loginInfo;

    public function __construct()
    {
        $loginInfo        = $this->getLoginInfo();
        $this->_loginInfo = $loginInfo;
    }

    /**
     * 获取面板配置
     *
     * @param  int sid 供应商id
     * User: Liucm
     * Date: 2021/2/22
     * Time: 16:51
     */
    public function getPanelConfig()
    {
        $sId = $this->_loginInfo['sid'];
        if (empty($sId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '供应商id不能为空');
        }
        $model      = new \Model\BigDataPanel\PftPanelConfig();
        $ConfigData = $model->getPanelConfigData($sId);
        if (!empty($ConfigData)) {
            $id             = $ConfigData['id'];
            $type           = $ConfigData['enter_type'];
            $keyId          = $ConfigData['key_id'];
            $macAddress     = $ConfigData['mac_address'];
            $state          = $ConfigData['state'];
            $customTextAttr = json_decode($ConfigData['custom_text_attr'], true);
            $size           = $customTextAttr['size'] ?? 32;
            $space          = $customTextAttr['space'] ?? 10;
            $titlecolor     = empty($customTextAttr['title_color']) ? '#fff' : $customTextAttr['title_color'];
            $numcolor       = empty($customTextAttr['num_color']) ? '#f90' : $customTextAttr['num_color'] ;
            $customText     = json_decode($ConfigData['custom_text'], true);
            $enterNum       = $customText['enter_num'] ?? [];
            $momentNum      = $customText['moment_num'] ?? [];
            $dayNum         = $customText['day_num'] ?? [];
            $custom         = empty($customText['custom']) ? [] : $customText['custom'];
            $returnData     = [
                'id'               => $id,
                'enter_type'       => $type,
                'key_id'           => $keyId,
                'mac_address'      => $macAddress,
                'size'             => $size,
                'space'            => $space,
                'title_color'      => $titlecolor,
                'num_color'        => $numcolor,
                'background_color' => $customTextAttr['background_color'] ?? "#0F0F0F",
                'enter_num'        => $enterNum,
                'moment_num'       => $momentNum,
                'person_density'   => $customText['person_density'] ?? [],
                'in_park_num'      => $customText['in_park_num'] ?? [],
                'day_num'          => $dayNum,
                'state'            => $state,
                'custom'           => $custom,
            ];
        } else {
            $returnData = $this->defaultConfig();
        }

        $this->apiReturn(self::CODE_SUCCESS, $returnData);
    }

    /**
     * 增加或修改面板设置
     * User: Liucm
     * Date: 2021/2/22
     * Time: 16:52
     */
    public function insertOrEditPanelConfig()
    {
        $sId             = $this->_loginInfo['sid'];
        $id              = I('post.id', 0, 'intval');
        $type            = I('post.enter_type', 1, 'intval');
        $keyId           = I('post.key_id', '', 'strval');
        $macAddress      = I('post.mac_address', '', 'strval');
        $size            = I('post.size', 32, 'intval');
        $space           = I('post.space', 10, 'intval');
        $enterNum        = I('post.enter_num');
        $momentNum       = I('post.moment_num');
        $dayNum          = I('post.day_num');
        $inParkNum       = I('post.in_park_num', '', 'strval');    //在园人数
        $personDensity   = I('post.person_density'); //人流密度
        $state           = I('post.state', 1, 'intval');
        $custom          = I('post.custom');
        $titleColor      = I('post.title_color');
        $numColor        = I('post.num_color');
        $backgroundColor = I('post.background_color');

        if (empty($sId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '供应商id不能为空');
        }
        if ($type == 2 && empty($keyId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '自定义时闸机不能为空');
        }
        if ($enterNum['number'] < 0 || $enterNum['number'] > 24) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '选择小时段超出');
        }
        if ($momentNum['number'] < 0 || $dayNum['number'] < 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '默认人数错误');
        }
        $inParkNum = json_decode($inParkNum, true);
        if ($inParkNum['number'] < 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '在园虚拟人数设置有误');
        }
        if (empty($inParkNum['name'])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '在园人数展示名称有误');
        }
        $personDensity = json_decode(html_entity_decode($personDensity), true);
        if (empty($personDensity['name'])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '人流密度名称有误');
        }
        $keyId      = implode(',', array_unique(array_map('trim', explode(',', $keyId))));
        $customText = [
            'enter_num'      => $enterNum,
            'moment_num'     => $momentNum,
            'day_num'        => $dayNum,
            'custom'         => empty($custom) ? [] : $custom,
            'in_park_num'    => empty($inParkNum) ? [] : $inParkNum,
            'person_density' => empty($personDensity) ? [] : $personDensity,
        ];

        $customText     = json_encode($customText, JSON_UNESCAPED_UNICODE);

        $customTextAttr = [
            'size'             => $size,
            'space'            => $space,
            'title_color'      => empty($titleColor) ? '#fff' : $titleColor,
            'num_color'        => empty($numColor) ? '#f90' : $numColor,
            'background_color' => empty($backgroundColor) ? '#0F0F0F' : $backgroundColor,
        ];
        $customTextAttr = json_encode($customTextAttr);
        $model          = new \Model\BigDataPanel\PftPanelConfig();
        $ConfigData     = $model->inOrEditPanelConfigData($sId, $type, $keyId, $macAddress, $customText,
            $customTextAttr, $state);

        if ($ConfigData) {
            $this->apiReturn(self::CODE_SUCCESS, [], '操作成功');
        }
        $this->apiReturn(self::CODE_CREATED, [], '操作失败，请重试');
    }

    /**
     * 获取默认面板值
     * User: Liucm
     * Date: 2021/2/22
     * Time: 18:43
     * @return array
     */
    private function defaultConfig()
    {
        $list = [
            'id'          => 0,
            'enter_type'  => 1,
            'key_id'      => '',
            'mac_address' => '',
            'size'        => '32',
            'space'       => '10',
            'title_color' => '#fff',
            'num_color'   => '#f90' ,
            'enter_num'   => [
                'name'        => '景点入园数',
                'value'       => '当前客流量(人):',
                'number'      => '2',
                'value_mark'  => '注:不超过20个字',
                'number_mark' => '注：选择小时段，默认景区2小时内 （某景区在验票时间2小时内的数据，比如现在10：30  那就是显示8：30后到10:30之间的验票人数），选择0的话，就是当前的时间的数据',
            ],
            'moment_num'  => [
                'name'        => '瞬时承载量(人)',
                'value'       => '瞬时承载量(人):',
                'number'      => '3200',
                'value_mark'  => '注:不超过20个字',
                'number_mark' => '注:瞬时承载量(人)= 景点入园数 + 编辑框的值(正整数)',
            ],
            'day_num'     => [
                'name'        => '景区日承载量(人)',
                'value'       => '景区日承载量(人):',
                'number'      => '25000',
                'value_mark'  => '注:不超过20个字',
                'number_mark' => '注:输入的值即页面展示',
            ],
            'custom'     => [],
            'state'       => 1,
        ];

        return $list;
    }

    /**
     * 获取婺源免登陆数据获取地址
     * 
     */
    public function getWuYuanNoAuthUrl()
    {
        $sId = $this->_loginInfo['sid']; 
        if (empty($sId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '供应商id不能为空');
        }

        try {
            $lib  = new \Library\JsonRpc\PftRpcClient('ota_supplier_api');
            $res  = $lib->call('Page/WuYuan/getNoAuthPageUrl', [$sId], 'pft_platform');
            $data = $res['data'];
            $code = $res['code'];
            $msg  = $res['msg'];
        } catch (\Exception $e) {
            $msg  = $e->getMessage();
            $code = $e->getCode();
            $data = [];
        }

        if ($code == 200) {
            $this->apiReturn(200, $data, 'success');
        } else {
            $this->apiReturn(205, [], $msg);
        }
    }
}