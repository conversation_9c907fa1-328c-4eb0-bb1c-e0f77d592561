<?php
/**
 * Created by PhpStorm.
 * User: zhangyangzhen
 * Date: 2020/1/2
 * Time: 10:24
 */

namespace Controller\BigDataPanel;

use Library\Controller;
use Library\Tools\Helpers;

class BI extends Controller
{
    public function __construct()
    {

    }

    /**
     * BI部因为袋鼠云平台接口限制，新提供一个接受json格式的获取天气接口给BI，不做登录限制
     * 任务链接：https://www.tapd.cn/44385193/prong/stories/view/1144385193001004549
     * Create by zhangyangzhen
     * Date: 2019/12/12
     * Time: 17:52
     */
    public function BIGetWeather()
    {
        //接收前端传过来的json格式
        $data = file_get_contents("php://input");
        $data = json_decode($data, true);
        $city = isset($data['city']) ? $data['city'] : '福州';//华建说默认福州

        $res = Helpers::getWeather($city);

        if (!$res) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '请求超时');
        }

        $res    = json_decode($res, true);
        $status = $res['HeWeather6'][0]['status'] ?: '获取天气失败';
        if ($status == 'ok') {
            $this->apiReturn(self::CODE_SUCCESS, $res, 'success');
        } else {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], $status);
        }
    }
}