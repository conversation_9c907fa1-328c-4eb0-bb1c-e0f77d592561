<?php
/**
 * 返利相关的
 * <AUTHOR>
 * @date  2021-07-09
 */

namespace Controller\Rebate;

use Library\Controller;

class RebateRelated extends Controller
{
    private $loginInfo   = [];
    private $_rebateBiz;
    private $_channelMap = [0, 16, 17, 20, 24, 9, 10, 49, 43, 44, 40, 19, 22, 50, 11, 19, 45, 55];

    public function __construct()
    {
        $this->isLogin('ajax');
        $this->loginInfo  = $this->getLoginInfo();
        $this->_rebateBiz = new \Business\Rebate\Rebate();
    }

    /**
     * 获取组合好的规则类目组合
     * <AUTHOR>  Li
     * @date  2021-07-09
     *
     * @return array
     */
    public function getRebateRuleList()
    {
        $page = I('post.page', 1, 'intval');     //当前页数
        $size = I('post.size', 10, 'intval');    //每页条数

        $result = $this->_rebateBiz->getRebateRuleList($page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取订单来源配置
     * <AUTHOR>  Li
     * @date  2021-07-09
     *
     * @return array
     */
    public function getOrderChannel()
    {
        $tmpOrderModeConf = load_config('order_mode_two', 'orderSearch');
        $orderModeConf    = [];
        foreach ($tmpOrderModeConf as $key => $item) {
            if (is_array($item['key'])) {
                foreach ($item['key'] as $v) {
                    if (in_array($v, $this->_channelMap)) {
                        if ($v == 17) {
                            $item['name'] = '淘宝码商';
                        }
                        if ($v == 20) {
                            $item['name'] = 'OTA';
                        }
                        $orderModeConf[$v] = $item['name'];
                    }
                }
            } else {
                if (in_array($item['key'], $this->_channelMap)) {
                    if ($item['key'] == 17) {
                        $item['name'] = '淘宝码商';
                    }
                    if ($item['key'] == 20) {
                        $item['name'] = 'OTA';
                    }
                    $orderModeConf[$item['key']] = $item['name'];
                }
            }
        }

        $this->apiReturn(200, $orderModeConf, '获取成功');
    }

    /**
     * 创建用户规则
     * <AUTHOR>  Li
     * @date  2021-07-09
     *
     * @return array
     */
    public function addUserRebateRule()
    {
        $ruleName           = I('post.rule_name', '', 'strval');    //规则名称
        $ruleType           = I('post.rule_type', 1, 'intval');     //条件设置 1全部满足 2任意满足
        $conditionDetailArr = I('post.condition_detail_arr',
            []);   //规则详情  [['rule_mark' => 'order_num', 'opt' => 'EGT', 'num' => 200,], ['rule_mark' => 'verify_num', 'opt' => 'BETWEEN', 'num' => 150, 'num2' => 180,], ['rule_mark' => 'order_time_to_verity_time', 'opt' => 'GT', 'num' => 24, 'unit' => 1,]];
        $state              = I('post.state', 0, 'intval');         //类目状态 0正常 1删除
        $ruleName           = trim($ruleName);
        if (empty($conditionDetailArr)) {
            $this->apiReturn(203, [], '规则信息不能为空');
        }

        $result = $this->_rebateBiz->addUserRebateRule($this->loginInfo['sid'], $ruleName, $ruleType,
            $conditionDetailArr, $this->loginInfo['memberID'], $state);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 编辑用户规则
     * <AUTHOR>  Li
     * @date  2021-07-09
     *
     * @return array
     */
    public function editUserRebateRule()
    {
        $ruleId             = I('post.rule_id', 0, 'intval');       //规则id
        $ruleName           = I('post.rule_name', '', 'strval');    //规则名称
        $ruleType           = I('post.rule_type', 1, 'intval');     //条件设置 1全部满足 2任意满足
        $conditionDetailArr = I('post.condition_detail_arr',
            []);   //规则详情  [['rule_mark' => 'order_num', 'opt' => 'EGT', 'num' => 200,], ['rule_mark' => 'verify_num', 'opt' => 'BETWEEN', 'num' => 150, 'num2' => 180,], ['rule_mark' => 'order_time_to_verity_time', 'opt' => 'GT', 'num' => 24, 'unit' => 1,]];
        $state              = I('post.state', 0, 'intval');         //类目状态 0正常 1删除
        $ruleName           = trim($ruleName);
        if (empty($conditionDetailArr)) {
            $this->apiReturn(203, [], '规则信息不能为空');
        }

        $result = $this->_rebateBiz->editUserRebateRule($ruleId, $this->loginInfo['sid'], $ruleName, $ruleType,
            $conditionDetailArr,
            $this->loginInfo['memberID'], $state);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除用户规则
     * <AUTHOR>  Li
     * @date  2021-07-09
     *
     * @return array
     */
    public function deleteUserRebateRule()
    {
        $ruleId = I('post.rule_id', 0, 'intval');       //规则id

        if (!$ruleId) {
            $this->apiReturn(203, [], '规则id缺失');
        }

        $result = $this->_rebateBiz->deleteUserRebateRule($this->loginInfo['sid'], $ruleId,
            $this->loginInfo['memberID']);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 通过规则id获取明细
     * <AUTHOR>  Li
     * @date  2021-07-10
     *
     * @return array
     */
    public function getUserRuleInfoById()
    {
        $ruleId = I('post.rule_id', 0, 'intval');       //规则id

        if (!$ruleId) {
            $this->apiReturn(203, [], '规则id缺失');
        }

        $result = $this->_rebateBiz->getUserRuleInfoById($this->loginInfo['sid'], $ruleId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取用户规则列表
     * <AUTHOR>  Li
     * @date  2021-07-10
     *
     * @return array
     */
    public function getUserRuleList()
    {
        $page         = I('post.page', 1, 'intval');        //当前页数
        $size         = I('post.size', 10, 'intval');       //每页条数
        $ruleName     = I('post.rule_name', '', 'strval');  //规则名称
        $conditionArr = I('post.condition_detail_arr',
            []);    //规则条件数组 ['order_num', 'verify_num", "order_time_to_verity_time']
        if (empty($conditionArr)) {
            $conditionArr = [];
        }

        $result = $this->_rebateBiz->getUserRuleList($this->loginInfo['sid'], $page, $size, $ruleName, $conditionArr);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 通过规则id获取关联的活动信息
     * <AUTHOR>  Li
     * @date  2021-07-17
     *
     * @return array
     */
    public function getRuleRelationListByRuleId()
    {
        $page   = I('post.page', 1, 'intval');      //当前页数
        $size   = I('post.size', 10, 'intval');     //每页条数
        $ruleId = I('post.rule_id', 0, 'intval');   //规则id
        if (!$ruleId) {
            $this->apiReturn(203, [], '规则id缺失');
        }

        $result = $this->_rebateBiz->getRuleRelationListByRuleId($this->loginInfo['sid'], $ruleId, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 创建返利活动
     * <AUTHOR>  Li
     * @date  2021-07-10
     *
     * @return array
     */
    public function addRebateActivity()
    {
        $activityName  = I('post.activity_name', '', 'strval');     //活动名称
        $fidStr        = I('post.fid_str', '', 'strval');           //分销商id 多个以逗号隔开
        $groupIdStr    = I('post.group_id_str', '', 'strval');      //分销商分组id 多个以逗号隔开
        $tidArr        = I('post.tid_arr', []);                     //门票id+权重{"123123":100,"24232":50}
        $ruleLadderArr = I('post.rule_ladder_arr', []);             //返利阶梯 {{"type":1,"money":1000,"num":1,"unit":1}}
        $ruleIdArr     = I('post.rule_id_arr', []);                 //规则id配置  {"1":100,"2":50}
        $startData     = I('post.start_date', '', 'strval');        //开始日期 2021-07-10
        $endDate       = I('post.end_date', '', 'strval');          //结束时间 2021-07-10
        $cycle         = I('post.cycle', 1, 'intval');              //活动结算周期  1每日 2每周 3每月 4每季度 5每半年 6每年
        $rebateType    = I('post.rebate_type', 0, 'intval');        //返利阶梯类型 0常规返利  1阶梯返利
        $activityName  = trim($activityName);

        if (!$activityName || (!$fidStr && !$groupIdStr) || !$tidArr || !$ruleLadderArr || !is_array($ruleLadderArr) || !strtotime($startData) || !strtotime($endDate) || !$cycle) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $fidArr = [];
        if ($fidStr) {
            $fidArr = explode(',', $fidStr);
        }
        $groupIdArr = [];
        if ($groupIdStr) {
            $groupIdArr = explode(',', $groupIdStr);
        }

        $startTime = 0;
        $endTime   = 0;
        if (strtotime($startData) && strtotime($endDate)) {
            $startTime = strtotime($startData);
            $endTime   = strtotime($endDate . ' 23:59:59');
        }

        if (date('Y-m-d', $startTime) < date('Y-m-d')) {
            $this->apiReturn(203, [], '开始时间不能早于当前日期');
        }
        if (date('Y-m-d', $endTime) < date('Y-m-d')) {
            $this->apiReturn(203, [], '结束时间不能早于当前日期');
        }

        $result = $this->_rebateBiz->addRebateActivity($this->loginInfo['sid'], $activityName, $fidArr, $groupIdArr,
            $tidArr, $ruleLadderArr, $ruleIdArr, $startTime, $endTime, $cycle, $this->loginInfo['memberID'], $rebateType);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 编辑返利活动
     * <AUTHOR>  Li
     * @date  2021-07-12
     *
     * @return array
     */
    public function editRebateActivity()
    {
        $activityId    = I('post.activity_id', '', 'strval');       //活动id
        $activityName  = I('post.activity_name', '', 'strval');     //活动名称
        $fidStr        = I('post.fid_str', '', 'strval');           //分销商id 多个以逗号隔开
        $groupIdStr    = I('post.group_id_str', '', 'strval');      //分销商分组id 多个以逗号隔开
        $tidArr        = I('post.tid_arr', []);                     //门票id 多个以逗号隔开
        $ruleLadderArr = I('post.rule_ladder_arr', '', 'strval');   //返利阶梯 {{"type":1,"money":1000,"num":1,"unit":1}}
        $ruleIdArr     = I('post.rule_id_arr', []);                 //规则id配置  {"1":100,"2":50}
        $startData     = I('post.start_date', '', 'strval');        //开始日期 2021-07-10
        $endDate       = I('post.end_date', '', 'strval');          //结束时间 2021-07-10
        $cycle         = I('post.cycle', '', 'strval');             //活动结算周期  1每日 2每周 3每月 4每季度 5每半年 6每年
        $state         = I('post.state', 0, 'intval');              //活动状态 0启用 1删除 2停用
        $rebateType    = I('post.rebate_type', 0, 'intval');        //返利阶梯类型 0常规返利  1阶梯返利
        $activityName  = trim($activityName);

        if (!$activityId || !$activityName || (!$fidStr && !$groupIdStr) || !$tidArr || !$ruleLadderArr || !is_array($ruleLadderArr) || !strtotime($startData) || !strtotime($endDate) || !$cycle) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $fidArr = [];
        if ($fidStr) {
            $fidArr = explode(',', $fidStr);
        }
        $groupIdArr = [];
        if ($groupIdStr) {
            $groupIdArr = explode(',', $groupIdStr);
        }

        $startTime = 0;
        $endTime   = 0;
        if (strtotime($startData) && strtotime($endDate)) {
            $startTime = strtotime($startData);
            $endTime   = strtotime($endDate . ' 23:59:59');
        }

        $result = $this->_rebateBiz->editRebateActivity($activityId, $this->loginInfo['sid'], $activityName, $fidArr,
            $groupIdArr, $tidArr, $ruleLadderArr, $ruleIdArr, $startTime, $endTime, $cycle,
            $this->loginInfo['memberID'], $rebateType, $state);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 更新返利活动状态
     * <AUTHOR>  Li
     * @date  2021-08-03
     *
     * @return array
     */
    public function editRebateActivityStatus()
    {
        $activityId = I('post.activity_id', '', 'strval');       //活动id
        $state      = I('post.state', 1, 'intval');              //活动状态 0启用 1删除 2停用

        if (!$activityId || !in_array($state, [0, 1, 2])) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $result = $this->_rebateBiz->editRebateActivityStatus($activityId, $this->loginInfo['sid'],
            $this->loginInfo['memberID'], $state);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取一条活动的明细
     * <AUTHOR>  Li
     * @date  2021-07-15
     *
     * @return array
     */
    public function getUserActivityInfoById()
    {
        $activityId = I('activity_id', 0, 'intval');       //活动id

        if (!$activityId) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $result = $this->_rebateBiz->getUserActivityInfoById($this->loginInfo['sid'], $activityId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 活动及关系id关联处理
     * <AUTHOR>  Li
     * @date  2021-07-12
     *
     * @return array
     */
    public function handleRuleRelation()
    {
        $activityId = I('post.activity_id', '', 'strval');       //活动id
        $ruleIdStr  = I('post.rule_id_str', '', 'strval');       //规则id配置  {"1":100,"2":50}
        $state      = I('post.state', 0, 'intval');              //类目状态 0启用 1删除 2停用

        if (!$activityId || !$ruleIdStr) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $ruleIdArr = [];
        if ($ruleIdStr) {
            $ruleIdArr = explode(',', $ruleIdStr);
        }

        $result = $this->_rebateBiz->handleRuleRelation($this->loginInfo['sid'], $activityId, $ruleIdArr, $state);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 分页获取用户活动列表
     * <AUTHOR>  Li
     * @date  2021-07-12
     *
     * @return array
     */
    public function getUserActivityList()
    {
        $page  = I('post.page', 1, 'intval');     //当前页数
        $size  = I('post.size', 10, 'intval');    //每页条数
        $state = I('post.state', -1, 'intval');    //活动状态

        $result = $this->_rebateBiz->getUserActivityList($this->loginInfo['sid'], $page, $size, $state);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    //===============返利操作==================

    /**
     * 单个返利
     * <AUTHOR>  Li
     * @date  2021-07-17
     */
    public function doRebateForOne()
    {
        $cycle       = I('post.cycle', 1, 'intval');         //结算周期
        $date        = I('post.date');                       //返利日期
        $fid         = I('post.fid');                        //分销商id
        $activityId  = I('post.activity_id');                //活动id
        $money       = I('post.money', 0, 'intval');         //返利金额
        $accountType = I('post.account_type', 1, 'intval');  //返利到余额|授信|现金

        if (!$cycle || !$activityId || !$fid || !$date || !$money) {
            $this->apiReturn(204, [], '参数错误');
        }

        //需要判断下供应商分销商关系是否存在  如果不存在  不能选择授信返还
        if ($accountType == 2) {
            $relationBiz = new \Business\Member\MemberRelation();
            $res         = $relationBiz->existsSupplyShip($this->loginInfo['sid'], $fid);
            if ($res['code'] != 200 || empty($res['data'])) {
                $this->apiReturn(204, [], '返利失败，未开启授信');
            }
        }

        //返利操作加上锁
        $lockKey      = 'rebate:operate:lock:' . $date . $cycle . $fid;
        $cacheService = \Library\Cache\Cache::getInstance('redis');
        $lockRet      = $cacheService->lock($lockKey, 1, 10);
        if (!$lockRet) {
            $this->apiReturn(204, [], '当前用户返利操作中，请勿重复操作');
        }

        $rebateData = date('Ymd', strtotime($date));

        $rebateBiz = new \Business\Rebate\Rebate();
        $result    = $rebateBiz->doRebateForOne($this->loginInfo['sid'], $activityId, $fid, $cycle, $money, $accountType,
            $rebateData, $this->loginInfo['memberID']);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }
    }

    /**
     * 批量返利
     * <AUTHOR>  Li
     * @date  2021-07-17
     */
    public function doRebateForMulti()
    {
        $black       = I('post.black', '');              //不参与返利的分销商名单 多个以逗号隔开
        $cycle       = I('post.cycle', 0, 'intval');    //结算周期
        $date        = I('post.date');                  //返利日期
        $accountType = I('account_type', 1, 'intval');  //返利到余额|授信|现金

        if (!$date) {
            $this->apiReturn(204, [], '参数错误');
        }

        //返利操作加上锁
        $lockKey      = 'rebate:operate:lock:' . $date . $cycle;
        $cacheService = \Library\Cache\Cache::getInstance('redis');
        $lockRet      = $cacheService->lock($lockKey, 1, 10);
        if (!$lockRet) {
            $this->apiReturn(204, [], '当前用户返利操作中，请勿重复操作');
        }

        $black      = explode(',', $black);
        $rebateData = date('Ymd', strtotime($date));

        $rebateBiz = new \Business\Rebate\Rebate();
        $result    = $rebateBiz->doRebateForMulti($this->loginInfo['sid'], $accountType, $rebateData, $black, $cycle,
            $this->loginInfo['memberID']);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }
    }

    /**
     * 获取报表返利对象
     * <AUTHOR>  Li
     * @date  2021-08-05
     *
     * @return array
     */
    public function getRebateFidList()
    {
        $date  = I('date', 0, 'intval');     //返利日期 ********
        $cycle = I('cycle', 0, 'intval');    //结算周期
        $fid   = I('fid', 0, 'intval');    //每页条数

        if (!$date) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $result = $this->_rebateBiz->getRebateFidList($this->loginInfo['sid'], $date, $cycle, $fid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取自供应产品
     * <AUTHOR>  Li
     * @date  2021-08-11
     *
     * @return array
     */
    public function getProductList()
    {
        $keyword = I('keyword', '', 'strval');
        $page    = I('page', 1, 'intval');     //当前页数
        $size    = I('size', 10, 'intval');    //每页条数

        $result = $this->_rebateBiz->getProductList($this->loginInfo['sid'], $keyword, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取当前用户参与了返利得对象
     * <AUTHOR>
     * @date  2021-08-11
     *
     * @return array
     */
    public function getRebateIntoFids()
    {
        $page = I('page', 0, 'intval');     //当前页数
        $size = I('size', 0, 'intval');    //每页条数

        $result = $this->_rebateBiz->getRebateIntoFids($this->loginInfo['sid'], $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

}

?>