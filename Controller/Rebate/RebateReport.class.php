<?php
/**
 * 返利报表
 * <AUTHOR>
 * @date  2021-07-09
 */

namespace Controller\Rebate;

use Library\Controller;

class RebateReport extends Controller
{
    private $loginInfo = [];
    private $_rebateBiz;
    private $_rebateReportBiz;

    public function __construct()
    {
        $this->isLogin('ajax');
        $this->loginInfo        = $this->getLoginInfo();
        $this->_rebateBiz       = new \Business\Rebate\Rebate();
        $this->_rebateReportBiz = new \Business\Rebate\RebateReport();
    }

    /** 分页获取供应商id对应的返利活动报表列表
     * 获取一条活动的明细
     * <AUTHOR>
     * @date  2021-07-15
     *
     * @return array
     */
    public function getRebateRerportList()
    {
        $startTime = I('post.start_time', '', 'strval');       //开始时间
        $endTime   = I('post.end_time', '', 'strval');       //结束时间
        $fid       = I('post.fid', 0, 'intval');
        $page      = I('post.page', 1, 'intval');
        $size      = I('post.size', 10, 'intval');

        if (!$startTime || !$endTime) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $result = $this->_rebateBiz->getRebateRerportList($this->loginInfo['sid'], $startTime, $endTime, $fid, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /** 获取返利活动报表对应的对象列表
     * 获取一条活动的明细
     * <AUTHOR>
     * @date  2021-07-15
     *
     * @return array
     */
    public function getRebateRerportUserListByReportDateAndCycle()
    {
        $date  = I('post.date', 0, 'intval');       //结算时间  20210713
        $cycle = I('post.cycle', 0, 'intval');       //结算周期 1每日 2每周 3每月 4每季度 5每半年 6每年
        $page  = I('post.page', 1, 'intval');
        $fid   = I('post.fid', 0, 'intval');
        $size  = I('post.size', 10, 'intval');

        if (!$date || !$cycle) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $result = $this->_rebateBiz->getRebateRerportUserListByReportDateAndCycle($this->loginInfo['sid'], $date,
            $cycle, $fid, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取返利确认表记录 获取一条活动的明细
     * <AUTHOR>
     * @date  2021-07-15
     *
     * @return array
     */
    public function getRebateCheckoutDetailList()
    {
        $date  = I('post.date', '', 'intval');       //结算时间  20210713
        $cycle = I('post.cycle', 0, 'intval');
        $page  = I('post.page', 1, 'intval');
        $size  = I('post.size', 10, 'intval');
        $fid   = I('post.fid', 0, 'intval');

        if (!$date || !$cycle) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $result = $this->_rebateBiz->getRebateCheckoutDetailList($this->loginInfo['sid'], $date, $cycle, $page, $size, $fid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取返利确认表记录返利总计金额
     * <AUTHOR>
     * @date  2021-07-15
     *
     */
    public function sumRebateCheckoutTableMoney()
    {
        $rerportDate = I('post.rerport_date', 0, 'intval'); // 结算开始时间
        $cycle       = I('post.cycle', 0, 'intval');
        $fid         = I('post.fid', 0, 'intval');

        if (!$rerportDate || !$cycle) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $result = $this->_rebateBiz->sumRebateCheckoutTableMoney($this->loginInfo['sid'], $rerportDate, $cycle, $fid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取返利对象参与返利的产品明细
     * <AUTHOR>
     * @date  2021-07-15
     *
     * @return array
     */
    public function getTicketSumDetailList()
    {
        $fid        = I('post.fid', 0, 'intval');
        $cycleStart = I('post.cycle_start_time', 0, 'strval,trim'); // 结算开始时间
        $cycleEnd   = I('post.cycle_end_time', 0, 'strval,trim'); // 结算结束时间
        $cycle      = I('post.cycle', 0, 'intval');//周期
        $getSum     = I('post.get_sum', 0, 'intval');
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');

        $result = $this->_rebateBiz->getTicketSumDetailList($this->loginInfo['sid'], $fid, $cycle, $cycleStart,
            $cycleEnd, $getSum, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取返利确认明细
     * <AUTHOR>
     * @date  2021-07-15
     *
     * @return array
     */
    public function getRebateCheckDetail()
    {
        $fid        = I('post.fid', 0, 'intval');
        $cycleStart = I('post.cycle_start_time', 0, 'strval,trim'); // 结算开始时间
        $cycleEnd   = I('post.cycle_end_time', 0, 'strval,trim'); // 结算结束时间
        $cycle      = I('post.cycle', 0, 'intval');//周期
        $reportDate = I('post.report_date', 0, 'intval');
        $activityid = I('post.activityid', 0, 'intval');

        $result = $this->_rebateBiz->getRebateCheckDetail($fid, $cycle, $reportDate, $cycleStart, $cycleEnd,
            $activityid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);

    }

    /**
     * 获取票列表（返利确认明细(满足阶梯-》规则->票列表)）
     * <AUTHOR>
     * @date  2021-07-14
     * */
    public function getRabateCheckoutTicketListByTimeAndActivityidSetKey()
    {
        $fid               = I('post.fid', 0, 'intval');
        $cycleStart        = I('post.cycle_start_time', '', 'strval'); // 结算开始时间
        $cycleEnd          = I('post.cycle_end_time', '', 'strval'); // 结算结束时间
        $cycle             = I('post.cycle', 0, 'intval');
        $activityid        = I('post.activityid', 0, 'intval');
        $setKey            = I('post.set_key', 0, 'intval');
        $ruleCombinationId = I('post.rule_combination_id', 0, 'intval');
        $page              = I('post.page', 1, 'intval');
        $size              = I('post.size', 10, 'intval');

        $result = $this->_rebateBiz->getRabateCheckoutTicketListByTimeAndActivityidSetKey($fid, $ruleCombinationId,
            $cycleStart, $cycleEnd,
            $activityid, $setKey, $cycle, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取结算时间结算周期内参与活动返利的返利金额总计
     * <AUTHOR>
     * @date  2021-07-14
     * */
    public function sumRebateCheckMoney()
    {
        $fid         = I('post.fid', 0, 'intval');
        $rerportDate = I('post.rerport_date', 0, 'intval'); // 结算时间
        $cycle       = I('post.cycle', 0, 'intval');
        $activityid  = I('post.activityid', 0, 'intval');

        $result = $this->_rebateBiz->sumRebateCheckMoney($fid, $this->loginInfo['sid'], $rerportDate,$cycle, $activityid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    /**
     * 获取结算时间结算周期内参与活动返利的返利金额总计
     * <AUTHOR>
     * @date  2021-07-14
     * */
    public function getCycleRebateSumMoney()
    {
        $rerportDate = I('post.date', 0, 'strval'); // 结算时间
        $cycle       = I('post.cycle', 0, 'intval');
        $fid         = I('post.fid', 0, 'intval');
        $filterFids  = I('post.filter_fids', []);

        if (!$rerportDate) {
            $this->apiReturn(203, [], '日期格式有误');
        }
        $rerportDate = date('Ymd', strtotime($rerportDate));

        $result = $this->_rebateBiz->getCycleRebateSumMoney($this->loginInfo['sid'], $rerportDate, $cycle, $fid, $filterFids);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取返利报表汇总数据
     * <AUTHOR>  Li
     * @date  2021-10-19
     * */
    public function getReportlList()
    {
        $cycleStartData = I('post.cycle_start_time', '', 'strval'); // 结算开始时间
        $cycleEndData   = I('post.cycle_end_time', '', 'strval'); // 结算结算时间
        $fid            = I('post.fid', 0, 'intval');
        $page           = I('post.page', 1, 'intval');
        $size           = I('post.size', 10, 'intval');

        if (!$cycleStartData || !$cycleEndData) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $cycleStart = date('Ymd', strtotime($cycleStartData));
        $cycleEnd   = date('Ymd', strtotime($cycleEndData));

        $result = $this->_rebateReportBiz->getReportlList($this->loginInfo['sid'], $cycleStart, $cycleEnd, $page, $size, $fid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取返利报表汇总数据
     * <AUTHOR>  Li
     * @date  2021-10-19
     * */
    public function getReportDetailList()
    {
        $cycleData    = I('post.cycle_date', '', 'strval'); // 结算开始时间
        $fid          = I('post.fid', 0, 'intval');
        $state        = I('post.state', -1, 'intval');
        $activityName = I('post.activity_name', '', 'strval');
        $page         = I('post.page', 1, 'intval');
        $size         = I('post.size', 10, 'intval');

        if (!$cycleData) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $cycleStart = strtotime(date('Y-m-d 00:00:00', strtotime($cycleData)));
        $cycleEnd   = strtotime(date('Y-m-d 23:59:59', strtotime($cycleData)));

        $result = $this->_rebateReportBiz->getReportDetailList($this->loginInfo['sid'], $cycleStart, $cycleEnd, $page, $size, $state, $fid, $activityName);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取返利对象对应活动的所有订单明细
     * <AUTHOR>  Li
     * @date  2021-10-19
     * */
    public function getOrderDetailList()
    {
        $fid        = I('post.fid', 0, 'intval');
        $cycle      = I('post.cycle', 0, 'intval');
        $activityId = I('post.activity_id', 0, 'intval');
        $cycleStart = I('post.cycle_start_time', '', 'strval'); // 结算开始时间
        $cycleEnd   = I('post.cycle_end_time', '', 'strval'); // 结算结束时间
        $lid        = I('post.lid', 0, 'intval');
        $tid        = I('post.tid', 0, 'intval');
        $orderNum   = I('post.order_num', '', 'strval');
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');

        if (!$fid || !$cycle || !$activityId || !$cycleStart || !$cycleEnd) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $cycleStart = strtotime(date('Y-m-d 00:00:00', strtotime($cycleStart)));
        $cycleEnd   = strtotime(date('Y-m-d 23:59:59', strtotime($cycleEnd)));

        $result = $this->_rebateReportBiz->getOrderDetailList($this->loginInfo['sid'], $fid, $cycle, $activityId, $cycleStart, $cycleEnd, $lid, $tid, $orderNum, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取返利对象对应活动的结算金额
     * <AUTHOR>  Li
     * @date  2021-10-19
     * */
    public function getOrderSummaryDetailList()
    {
        $fid        = I('post.fid', 0, 'intval');
        $cycle      = I('post.cycle', 0, 'intval');
        $activityId = I('post.activity_id', 0, 'intval');
        $cycleStart = I('post.cycle_start_time', '', 'strval'); // 结算开始时间
        $cycleEnd   = I('post.cycle_end_time', '', 'strval'); // 结算结束时间

        if (!$fid || !$cycle || !$activityId || !$cycleStart || !$cycleEnd) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $cycleStart = strtotime(date('Y-m-d 00:00:00', strtotime($cycleStart)));
        $cycleEnd   = strtotime(date('Y-m-d 23:59:59', strtotime($cycleEnd)));

        $result = $this->_rebateReportBiz->getOrderSummaryDetailList($this->loginInfo['sid'], $fid, $cycle, $activityId, $cycleStart, $cycleEnd);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

}