<?php
/**
 * 授权登录
 * <AUTHOR>  Li
 * @date 2021-03-05
 */

namespace Controller\Authorized;

use Library\Cache\Cache;
use Library\Cache\CacheRedis;
use Library\Controller;

class AuthorizedLogin extends Controller
{
    /**
     * 授权登录处理
     * <AUTHOR>  Li
     * @date  2021-03-05
     */
    public function authorizedLogin()
    {
        $authorizedCode = I('post.authorized_code', '', 'strval'); //授权码加密串
        $adminId        = I('post.adminId', '', 'strval'); //登录的管理员id
        if (empty($authorizedCode) || empty($adminId)) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        //这边需要校验一次解析后 授权信息  状态有效期等
        $authorizedBiz = new \Business\Authorized\Authorized();
        $checkRes      = $authorizedBiz->decodeAuthorizedCode($authorizedCode);
        if ($checkRes['code'] != 200) {
            $this->apiReturn($checkRes['code'], [], $checkRes['msg']);
        }

        //用授权用户id登录
        $authorizedInfo = $checkRes['data'];
        $memberBiz      = new \Business\Member\Session();
        $loginRes       = $memberBiz->loginMemberId($authorizedInfo['member_id'], 'pc', false,
            $authorizedInfo['id'], $adminId);

        $this->apiReturn($loginRes['code'], $loginRes['data'], $loginRes['msg']);
    }

    /**
     * 根据sessionid获取登录信息，**临时**提供给api网关鉴权的方案
     * 地址: https://my.12301.cc/r/Authorized_AuthorizedLogin/sessionIdAuth?session_id=xxxxx
     * @author: Guangpeng Chen
     * @date: 2021/8/7
     * @return bool
     */
    public function sessionIdAuth()
    {
        $sessionId = I('get.session_id');
        $sessKey = "PHPREDIS_SESSION:{$sessionId}";
        $redis = Cache::getInstance('redis');
        $redis->select(12);
        $response = [
            'expire'=> 5,
            'res'   => 0,
        ];
        if ($redis->exists($sessKey)) {
            $sessionData = $redis->get($sessKey);
            if (session_decode($sessionData) && isset($_SESSION['memberID'])) {
                $response['res'] = 1;
                $response['auth_result']['member_id']   = $_SESSION['memberID'];
                $response['auth_result']['sid']         = $_SESSION['sid'];
                $response['auth_result']['dtype']       = $_SESSION['dtype'];
                $response['auth_result']['sdtype']      = $_SESSION['sdtype'];
                $response['auth_result']['customer_id'] = $_SESSION['customerId'];
                $response['auth_result']['account']     = $_SESSION['account'];
            }
            $expire = $redis->ttl($sessKey);
            // 给session续期
            if ($expire < 300) {
                $expire += 300;
                $redis->expire($sessKey, $expire);
            }
            $response['expire']  = $expire;
        }
        return $this->apiReturn(200, $response, 'success');

    }
}