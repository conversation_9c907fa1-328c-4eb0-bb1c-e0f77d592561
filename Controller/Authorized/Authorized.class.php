<?php
/**
 * 授权相关
 * <AUTHOR>  Li
 * @date 2021-03-04
 */

namespace Controller\Authorized;

use Library\Controller;

class Authorized extends Controller
{
    // 登陆信息
    private $loginInfo;

    public function __construct()
    {
        parent::__construct();
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 创建授权信息
     * <AUTHOR>  Li
     * @date  2021-03-05
     */
    public function createAuthorizedInfo()
    {
        $validPeriod = I('valid_period', 0, 'intval'); //授权时间 天数
        $expiraDate  = I('expira_date', '', 'strval'); //指定的过期时间
        $memo        = I('memo', '', 'strval'); //备注信息

        if (!$validPeriod || ($validPeriod && !is_numeric($validPeriod))) {
            $this->apiReturn(203, [], '有效期设置有误');
        }

        if ($validPeriod && !$expiraDate) {
            $expiraTime = strtotime("+{$validPeriod} day");
        } else {
            $expiraTime = strtotime($expiraDate);
        }

        $authorizedBiz = new \Business\Authorized\Authorized();
        $result        = $authorizedBiz->createAuthorizedInfo($this->loginInfo['account'], $this->loginInfo['memberID'],
            $expiraTime, $memo);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 设置授权状态
     * <AUTHOR>  Li
     * @date  2021-03-05
     */
    public function setAuthorizedStatus()
    {
        $authorizeId = I('authorized_id', 0, 'intval'); //授权主键id
        $state       = I('state', 0, 'intval'); //状态 0 作废 1 正常

        if (!$authorizeId || !in_array($state, [0, 1])) {
            $this->apiReturn(203, [], '有效期设置有误');
        }

        $authorizedBiz = new \Business\Authorized\Authorized();
        $result        = $authorizedBiz->setAuthorizedStatus($this->loginInfo['memberID'], $authorizeId, $state);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取授权记录列表
     * <AUTHOR>  Li
     * @date  2021-03-04
     */
    public function getAuthorizedInfoList()
    {
        $page      = I('page', 1, 'intval');        //当前页数
        $size      = I('size', 10, 'intval');       //每页条数
        $startDate = I('start_date', '', 'strval'); //查询开始时间    2021-03-04
        $endDate   = I('end_date', '', 'strval');   //查询结束时间    2021-03-04
        $state     = I('state', -1, 'intval');      //查询状态  -1 全部 0 失效 1 有效 2过期

        $startTime = 0;
        $endTime   = 0;
        if (strtotime($startDate) && strtotime($endDate)) {
            $startTime = strtotime($startDate);
            $endTime   = strtotime($endDate . ' 23:59:59');
        }
        
        $authorizedBiz = new \Business\Authorized\Authorized();
        $result        = $authorizedBiz->getAuthorizedInfoList($this->loginInfo['memberID'], $page, $size, $startTime,
            $endTime, $state);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取授权登录记录列表
     * <AUTHOR>  Li
     * @date  2021-03-04
     */
    public function getAuthorizedLoginList()
    {
        $page        = I('page', 1, 'intval');          //当前页数
        $size        = I('size', 10, 'intval');         //每页条数
        $authorizeId = I('authorize_id', 0, 'intval');  //授权id

        if (!$authorizeId) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $authorizedBiz = new \Business\Authorized\Authorized();
        $result        = $authorizedBiz->getAuthorizedLoginList($authorizeId, $page, $size);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

}