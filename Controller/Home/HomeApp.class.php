<?php

/**
 * 会员应用相关接口
 * 
 * <AUTHOR>
 */
 
 namespace Controller\Home;

 use Library\Controller;

 class HomeApp extends Controller {

    private $_sid;  //主账号id

    private $_mid;  //登陆用户id


    public function __construct() {
        //主账号id
        $this->_sid = $this->isLogin('ajax');
        //登陆用户id
        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $this->_mid = $loginInfo['memberID'];
    }


    /**
     * 获取营销应用接口
     * 
     * @return void
     */
    public function  marketingAppList() {

        $return = [
            [
                'name'  => '全民营销',
                'url'   => 'www.12301.cc'
            ],
            [
                'name'  => '海报推广',
                'url'   => 'www.12301.cc'
            ],
            [
                'name'  => '微商城',
                'url'   => 'www.12301.cc'
            ],
        ];

        $this->apiReturn(200, $return);

    }


    /**
     * 获取微商城统计数据
     * 
     * @return voild
     */
    public function getMicroMallStatistics() {

        $return = [
            'pv'    => 1,
            'uv'    => 2,
            'bind'  => 3,
            'error' => 4
        ];

        $this->apiReturn(200, $return);

    }

}