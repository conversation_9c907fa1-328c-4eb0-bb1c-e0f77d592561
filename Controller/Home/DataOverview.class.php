<?php
/**
 * 数据概览（含旅游券）
 */

namespace Controller\Home;

use Library\Cache;
use Library\Controller;
use Model\Member;
use Model\Member\MemberRelationship;
use Model\Order;
use Model\Product;
use Model\Report;

class DataOverview extends Controller
{
    private $product_limit = 7;
    /**
     * 获取运营概况 产品排行 渠道排行
     * <AUTHOR>  Li
     * @date  2022-03-15
     *
     * @params int    $search_type      搜索方式  =1  票数   =2  总金额  默认1
     * @params date   $begin_time       开始时间  Y-m-d
     * @params date   $end_time         结束时间  Y-m-d
     * @params int    $compare_type     对比方式  =1 同比  =2 环比       默认1
     * @params int    $isGetThree       1 获取三个 0 获取销售趋势
     */
    public function getOperaOverview()
    {
        $isGetThree   = I('post.is_get_three', 0, 'intval');
        $isGetCompare = I('post.is_get_compare', 0, 'intval');
        //销售趋势
        $loginInfo = $this->getLoginInfo();
        $sdtype    = $loginInfo['sdtype'];
        $sid       = $loginInfo['sid'];

        $return    = [
            'sale_trend'  => [],
            'product_use' => [],
            'sale_rank'   => [],
        ];

        //集团账号数据判断
        $fidArr = $sid;
        if ($sdtype == 7) {
            //集团账号获取所有的成员
            $memberList = $this->_getGroupMemberList($sid);
            $fidArr     = $memberList;
        }

        //旅游券运营概况数据
        //运营趋势图
        $operationTrend = [];
        //$operationTrend = ['current' => ['20220310' => 1, '20220311' => 1, '20220312' => 1, '20220313' => 1, '20220314' => 1, '20220315' => 1, '20220316' => 1,], 'mom'     => ['20220303' => 1, '20220304' => 1, '20220305' => 1, '20220306' => 1, '20220307' => 1, '20220308' => 1, '20220309' => 1,], 'yoy'     => ['20210310' => 1, '20210311' => 1, '20210312' => 1, '20210313' => 1, '20210314' => 1, '20210315' => 1, '20210316' => 1,],];
        //产品列表
        $productList = [];
        //$productList = [['voucher_id' => 1, 'tid' => 111, 'voucher_name' => '测试旅游券门票', 'verify_price' => 50000, 'reserve_person_count' => 10],];
        //分销渠道列表
        $distributorList = [];
        //$distributorList = [['sid'=>4638,'sname'=>'s囊额啊', 'verify_price' => 55000, 'reserve_person_count' => 10]];

        try {
            $params    = $this->_handleSaleTrendsParams($isGetCompare);
            //获取旅游券相关运营概况数据
            $dimension            = $params['searchType'] == 2 ? 'verify_price' : 'reserve_person_count';
            $operationOverviewRes = (new \Business\JsonRpcApi\TravelVoucherService\DataOverview())->getOperaOverview($sid, $params['bTimeNew'], $params['eTimeNew'], $dimension);
            if ($operationOverviewRes['code'] == 200 && !empty($operationOverviewRes['data'])) {
                $operationTrend  = $operationOverviewRes['data']['operation_trend'] ?? [];
                $productList     = $operationOverviewRes['data']['product_list'] ?? [];
                $distributorList = $operationOverviewRes['data']['distributor_list'] ?? [];
            }

            $saleTrend = [];
            if (!empty($saleTrend) && $params['isCache']) {
                $saleTrend = unserialize($saleTrend);
            } else {
                $business  = new \Business\Statistics\DataOverview();
                $saleTrend = $business->getOperaOverview($params['bTimeNew'], $params['eTimeNew'], $params['bTimeOld'],
                    $params['eTimeOld'], $params['searchType'], $fidArr, $operationTrend);
                if ($isGetCompare) {
                    $saleTrendNew       = $business->getOperaOverview($params['bTimeNew'], $params['eTimeNew'],
                        $params['bChainTimeOld'], $params['eChainTimeOld'], $params['searchType'], $fidArr, $operationTrend, $isGetCompare);
                    $saleTrend['chain'] = $saleTrendNew['old'];
                }
            }
        } catch (\Exception $e) {
            $saleTrend = [];
            $msg       = $e->getMessage();
        }

        $return['sale_trend'] = $saleTrend;

        if (!empty($isGetThree)) {
            $beginTime = I('post.begin_time');
            $endTime   = I('post.end_time');
            $isCache   = true;
            $start     = strtotime($beginTime);
            $end       = strtotime($endTime);
            $day       = 0;
            if ($start && $end) {
                $day = ($end - $start) / (60 * 60 * 24);
                if ($day != 6) {
                    $isCache = false;
                }
            }
            //获取三个
            //产品使用排行
            try {
                $key     = 'tv_product_use_rank_v2:' . date('Ymd') . ':' . $sid;
                $redis   = Cache\Cache::getInstance('redis');
                $useRank = $redis->get($key);
                if (ENV != 'PRODUCTION') {
                    $useRank = [];
                }

                if (!empty($useRank) && $isCache) {
                    $useRank = unserialize($useRank);
                } else {
                    $statisticsApiBuz = new \Business\AppCenter\Statistics();
                    $rank             = $statisticsApiBuz->getTicketSaleRankInWeek($fidArr, $day);
                    $rank             = $rank['data'];

                    $totalMoney = $statisticsApiBuz->getSaleTotalMoneyInWeekByOrder($fidArr, $day);
                    $totalMoney = $totalMoney['data'];
                    if ($productList) {
                        //总金额加上旅游券的
                        $totalMoney['money'] += array_sum(array_column($productList, 'verify_price'));
                    }

                    if ((empty($rank) || !is_array($rank)) && empty($productList)) {
                        throw new \Exception("无数据");
                    }

                    if ($rank) {
                        $lidArr = array_column($rank, 'lid');
                        $tidArr = array_column($rank, 'tid');

                        $ticketModel = new Product\Ticket();
                        $javaAPi     = new \Business\CommodityCenter\Land();
                        $nameRes     = $javaAPi->queryLandMultiQueryById($lidArr);
                        $ticketRes   = $ticketModel->getTicketInfoByIdArr($tidArr, 'id,title');

                        $nameInfo = [];
                        if (!empty($nameRes)) {
                            foreach ($nameRes as $item) {
                                $nameInfo[$item['id']] = $item['title'];
                            }
                        }

                        $ticketInfos = [];
                        if (!empty($ticketRes)) {
                            foreach ($ticketRes as $item) {
                                $ticketInfos[$item['id']] = $item['title'];
                            }
                        }

                        foreach ($rank as &$value) {
                            $value['type']  = 'common';
                            $value['name']  = $nameInfo[$value['lid']] ?: '未知产品';
                            $value['tname'] = $ticketInfos[$value['tid']] ?: '未知票类';

                            if (!empty($totalMoney['money'])) {
                                $value['scale'] = round(($value['total_money'] / $totalMoney['money']) * 100, 2);
                            } else {
                                $value['scale'] = 0;
                            }

                            $value['total_money'] = round($value['total_money'] / 100, 2);

                            unset($value['lid']);
                        }
                    }

                    //如果旅游券的有数据的情况
                    if ($productList) {
                        foreach ($productList as $tvProduct) {
                            $scale = 0;
                            if (!empty($totalMoney['money'])) {
                                $scale = round(($tvProduct['verify_price'] / $totalMoney['money']) * 100, 2);
                            }
                            $rank[] = [
                                'type'        => 'travel_voucher',
                                'tid'         => $tvProduct['tid'],
                                'name'        => $tvProduct['voucher_name'],
                                'tname'       => $tvProduct['voucher_name'],
                                'people_num'  => $tvProduct['reserve_person_count'],
                                'total_money' => round($tvProduct['verify_price']/ 100, 2),
                                'scale'       => $scale,
                            ];
                        }
                    }

                    $sumScale   = array_sum(array_column($rank, 'scale'));
                    $otherScale = 100 - $sumScale;

                    //再根据总金额做个排序处理
                    array_multisort(array_column($rank,'total_money'),SORT_DESC, $rank);

                    //再将超过展示部分的数据做删除
                    foreach ($rank as $idx => $item) {
                        if ($idx >= $this->product_limit) {
                            unset($rank[$idx]);
                        }
                    }

                    $useRank = [
                        'rank'  => $rank,
                        'other' => $otherScale,
                    ];

                    if ($isCache) {
                        $str = serialize($useRank);
                        $redis->set($key, $str, '', 60 * 60 * 24);
                    }
                }
            } catch (\Exception $e) {
                $useRank = [];
                $msg     = $e->getMessage();
            }

            $return['product_use'] = $useRank;

            //渠道排行
            try {
                $key      = 'tv_sale_rank_v2:' . date('Ymd') . ':' . $sid;
                $redis    = Cache\Cache::getInstance('redis');
                $saleRank = $redis->get($key);
                if (ENV != 'PRODUCTION') {
                    $saleRank = [];
                }
                if (!empty($saleRank) && $isCache) {
                    $saleRank = unserialize($saleRank);
                } else {
                    $statisticsApiBuz = new \Business\AppCenter\Statistics();
                    $rank             = $statisticsApiBuz->getSaleRankInWeekByOrder($fidArr, $day);
                    $rank             = $rank['data'];

                    if ((empty($rank) || !is_array($rank)) && empty($distributorList)) {
                        throw new \Exception("无数据");
                    }

                    if ($rank) {
                        $idArr = array_column($rank, 'reseller_id');

                        $memberModel = new Member\Member();
                        $nameRes     = $memberModel->getMemberInfoByMulti($idArr, 'id', 'id, dname');

                        if (!empty($nameRes) && is_array($nameRes)) {
                            foreach ($nameRes as $item) {
                                $nameInfo[$item['id']] = $item['dname'];
                            }
                        }

                        $nameIdArr = [];
                        if (!empty($nameInfo)) {
                            $nameIdArr = array_keys($nameInfo);
                        }

                        foreach ($rank as &$value) {
                            $value['type'] = 'common';
                            if (in_array($value['reseller_id'], $nameIdArr)) {
                                $value['name'] = empty($nameInfo[$value['reseller_id']]) ? '未知' : $nameInfo[$value['reseller_id']];
                            } else {
                                $value['name'] = '未知';
                            }

                            $value['total_money'] = round($value['total_money'] / 100, 2);
                            unset($value['reseller_id']);
                        }
                    }

                    //如果旅游券的有数据的情况
                    if ($distributorList) {
                        foreach ($distributorList as $tvDistributor) {
                            $rank[] = [
                                'type'        => 'travel_voucher',
                                'sid'         => $tvDistributor['sid'],
                                'name'        => $tvDistributor['sname'],
                                'people_num'  => $tvDistributor['reserve_person_count'],
                                'total_money' => round($tvDistributor['verify_price'] / 100, 2),
                            ];
                        }
                    }

                    //再根据总金额做个排序处理
                    array_multisort(array_column($rank,'total_money'),SORT_DESC, $rank);

                    //再将超过展示部分的数据做删除
                    foreach ($rank as $idx => $item) {
                        if ($idx >= $this->product_limit) {
                            unset($rank[$idx]);
                        }
                    }

                    $saleRank = $rank;
                    if ($isCache) {
                        $str = serialize($saleRank);
                        $redis->set($key, $str, '', 60 * 60 * 24);
                    }
                }
            } catch (\Exception $e) {
                $saleRank = [];
                $msg      = $e->getMessage();
            }
            $return['sale_rank'] = $saleRank;
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 销售趋势 参数处理
     * <AUTHOR>
     * @date   2017-01-13
     *
     * @params int    $search_type      搜索数据  =1  票数   =2  总金额  默认1
     * @params date   $begin_time       开始时间  Y-m-d
     * @params date   $end_time         结束时间  Y-m-d
     * @params int    $compare_type     对比方式  =1 同比  =2 环比       默认1
     */
    private function _handleSaleTrendsParams($isGetCompare = 0)
    {
        $searchType  = I('post.search_type', 1, 'intval');
        $compareType = I('post.compare_type', 1, 'intval');
        $beginTime   = I('post.begin_time');
        $endTime     = I('post.end_time');

        if (!strtotime($beginTime) || !strtotime($endTime)) {
            throw new \Exception("时间格式错误");
        }

        if (strtotime($endTime) - strtotime($beginTime) > 60 * 60 * 24 * 100) {
            throw new \Exception("时间间隔不能多于100天");
        }
        if ($isGetCompare) {
            $bTimeNew = date('Ymd', strtotime($beginTime));
            $eTimeNew = date('Ymd', strtotime($endTime));
            //同比
            $bTimeOld = date('Ymd', strtotime('-1 year', strtotime($beginTime)));
            $eTimeOld = date('Ymd', strtotime('-1 year', strtotime($endTime)));

            //环比
            $space = strtotime($endTime) - strtotime($beginTime);

            $bTimeOldStap = strtotime($beginTime) - $space - (60 * 60 * 24);
            $eTimeOldStap = strtotime($beginTime) - (60 * 60 * 24);

            $bChainTimeOld = date('Ymd', $bTimeOldStap);
            $eChainTimeOld = date('Ymd', $eTimeOldStap);
        } else {
            switch ($compareType) {
                default:
                case 1:
                    //同比  去年同一时期
                    $bTimeNew = date('Ymd', strtotime($beginTime));
                    $eTimeNew = date('Ymd', strtotime($endTime));

                    $bTimeOld = date('Ymd', strtotime('-1 year', strtotime($beginTime)));
                    $eTimeOld = date('Ymd', strtotime('-1 year', strtotime($endTime)));

                    break;
                case 2:
                    //环比  上一周期
                    $bTimeNew = date('Ymd', strtotime($beginTime));
                    $eTimeNew = date('Ymd', strtotime($endTime));

                    $space = strtotime($endTime) - strtotime($beginTime);

                    $bTimeOldStap = strtotime($beginTime) - $space - (60 * 60 * 24);
                    $eTimeOldStap = strtotime($beginTime) - (60 * 60 * 24);

                    $bTimeOld = date('Ymd', $bTimeOldStap);
                    $eTimeOld = date('Ymd', $eTimeOldStap);

                    break;
            }
        }

        $res = $this->_judgeTime($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, $bChainTimeOld, $eChainTimeOld);
        if ($res === false) {
            throw new \Exception('不支持该时间段查询');
        } else {
            list($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, $bChainTimeOld, $eChainTimeOld) = $res;
        }

        $isCache = false;
        $tmpTime = date('Ymd', strtotime($endTime) - 60 * 60 * 24 * 6);

        if ($compareType == 1 && $searchType == 1 && $eTimeNew == date('Ymd') && $bTimeNew == $tmpTime) {
            //如果是默认的搜索条件 缓存结果
            $isCache = true;
        }
        if ($isGetCompare) {
            $data = [
                'searchType'    => $searchType,
                'bTimeNew'      => $bTimeNew,
                'eTimeNew'      => $eTimeNew,
                'bTimeOld'      => $bTimeOld,
                'eTimeOld'      => $eTimeOld,
                'bChainTimeOld' => $bChainTimeOld,
                'eChainTimeOld' => $eChainTimeOld,
                'isCache'       => $isCache,
            ];
        } else {
            $data = [
                'searchType' => $searchType,
                'bTimeNew'   => $bTimeNew,
                'eTimeNew'   => $eTimeNew,
                'bTimeOld'   => $bTimeOld,
                'eTimeOld'   => $eTimeOld,
                'isCache'    => $isCache,
            ];
        }

        return $data;
    }

    /**
     * 判断查询年份是否存在数据，当前只有16年及以后才有
     *
     * @param $bTimeNew
     * @param $eTimeNew
     * @param $bTimeOld
     * @param $eTimeOld
     * @param $chainBegin
     * @param $chainEnd
     *
     * @return array|bool
     */
    private function _judgeTime($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, $chainBegin, $chainEnd)
    {
        $newYear = date('Y', strtotime($bTimeNew));
        $oldYear = date('Y', strtotime($bTimeOld));

        if ($newYear < 2016) {
            return false;
        }

        //判断同比时间，再判断环比时间（环比时间比同比时间迟）
        if ($newYear >= 2016 && $oldYear < 2016) {
            $bTimeOld = $bTimeNew;
            $eTimeOld = $eTimeNew;

            if ($chainBegin) {
                $chainYear = date('Y', strtotime($chainBegin));
                if ($chainYear < 2016) {
                    $chainBegin = $bTimeNew;
                    $chainEnd   = $eTimeNew;
                }
            }
        }

        return [$bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, $chainBegin, $chainEnd];
    }

    /**
     * 根据集团账号ID获取集团成员
     *
     * @param  int $parentId
     *
     * @return array
     */
    private function _getGroupMemberList($parentId)
    {
        if (!$parentId) {
            return [];
        }

        $relationModel = new MemberRelationship();
        $tmpList       = $relationModel->getMemIdByGroupId($parentId);

        $resList = [];
        if ($tmpList) {
            $resList = array_column($tmpList, 'son_id');
            $resList = array_unique($resList);
        }

        return $resList;
    }

    /**
     * 首页数据概况 （预订数量 / 预订金额 取消数量 / 取消金额 验证数量 / 验证金额）
     * <AUTHOR>  Li
     * @date  2022-03-15
     */
    public function homeOverviewData()
    {
        $isFlush  = I('post.is_flush', 0, 'intval');
        $timeType = I('post.time_type', 1, 'intval');  //时间范围 1今天 2昨天 3近七天 4近30天

        $loginInfo = $this->getLoginInfo();
        $sdtype    = $loginInfo['sdtype'];
        $sid       = $loginInfo['sid'];

        if (!in_array($timeType, [1, 2, 3, 4])) {
            $this->apiReturn(203, [], '时间参数错误');
        }
        if (in_array($timeType, [1, 2])) {
            // 今天 昨天
            $result = (new \Business\Statistics\DataOverview())->getRealDataByFidTime($isFlush, $sid, $sdtype, $timeType);
        } elseif (in_array($timeType, [3, 4])) {
            //7天 30天
            $result = (new \Business\Statistics\DataOverview())->oldDataOverview($isFlush, $sid, $sdtype, $timeType);
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}