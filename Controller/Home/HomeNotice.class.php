<?php

/**
 * 首页右边栏通知相关接口
 *
 * <AUTHOR>
 */

namespace Controller\Home;

use Business\CommodityCenter\Ticket;
use Business\JavaApi\EvoluteApi;
use Business\Product\Product;
use Library\Cache\Cache;
use Library\Controller;
use Model\Member\BalanceWarning;
use Model\Notice\Announce;
use Rpc\Member\Member;

class HomeNotice extends Controller
{

    private $_sid; //主账号id

    private $_cache; //缓存对象

    private $_cacheKey; //缓存key

    const CACHE_TIME = 3600; //缓存时间
    const DISTRIBUTION_SOURCE_SELF = 1;//自供应
    const DISTRIBUTION_SOURCE_TRANSFER = 2;//转分销
    const DISTRIBUTION_SOURCE_CENTER = 3;//资源中心采购

    /**分销来源数组
     * @var array
     */
    public static $distributionSourceArr= [
        self::DISTRIBUTION_SOURCE_SELF => '自供应',
        self::DISTRIBUTION_SOURCE_TRANSFER => '转分销',
        self::DISTRIBUTION_SOURCE_CENTER => '资源中心采购',
    ];

    public function __construct()
    {

        //主账号id
        $this->_sid = $this->isLogin('ajax');

        $this->_cache = Cache::getInstance('redis');

        parse_str($_SERVER['QUERY_STRING'], $query);

        $this->_cacheKey = 'notice:' . $query['a'] . ':' . $this->_sid;

        //对查询条数做统一判断
        if (I('size') && I('size') > 20) {
            $this->apiReturn(204, [], '查询条数超出限制');
        }

        //查询时间段统一限制
        if (I('recentDay') && I('recentDay') > 30) {
            $this->apiReturn(204, [], '查询天数超出限制');
        }

    }

    /**
     * 合作分销商变动通知
     *
     * @param  int $recentDay 统计最近多少天的数据
     * @return void
     */
    public function partnerChange()
    {

        //最近几天
        $recentDay = I('recentDay', 7, 'intval');

        if (!$recentDay) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (!$cacheData = $this->_cache->get($this->_cacheKey)) {

            //结束时间
            $end = time();
            //开始时间
            $begin = $end - $recentDay * 3600 * 24;

            $RelationM = new \Model\Member\MemberRelationship();

            //作为供应商添加/删除分销商
            $asSupply = $RelationM->getPartnerChange($this->_sid, $begin, $end);

            //作为分销商被添加/或者删除
            $asDis = $RelationM->getPartnerChange($this->_sid, $begin, $end, false);

            $return = [
                'toAdd' => $asSupply[0],
                'toDel' => $asSupply[1],
                'beAdd' => $asDis[0],
                'beDel' => $asDis[1],
            ];
            //缓存数据
            $this->_setCache($return);
        } else {
            $return = json_decode($cacheData);
        }

        $this->apiReturn(200, $return);
    }

    /***
     * 产品价格与状态变动通知(原产品价格变动和产品变动接口合并)
     * <AUTHOR> Yiqiang
     * @date   2018-08-22
     */
    public function priceProductChange()
    {
        //最近几天
        $recentDay = I('recentDay', 7, 'intval');
        $size      = I('size', 5, 'intval');

        if (!$recentDay) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (!$cacheData = $this->_cache->get($this->_cacheKey)) {
            //结束时间
            $end = time();
            //开始时间
            $begin        = $end - $recentDay * 3600 * 24;
            $beginProduct = date('Y-m-d', $begin);
            //$evoluteApi   = new EvoluteApi();
            //$priceRe      = $evoluteApi->getPriceProductChangeList($this->_sid, 0, 0, 0, $beginProduct, $size);

            $productDistributionPriceLib = new \Business\JavaApi\Product\ProductDistributionPrice();
            $result                      = $productDistributionPriceLib->distributionPriceChangeQuery($this->_sid, 0, 0,
                0, $beginProduct, 0, 0, $size);
            $priceNoticeList             = $result['code'] == 200 ? $result['data']['list'] : [];

            //获取价格变动
            //$evoluteApi = new EvoluteApi();
            //$productRe  = $evoluteApi->getStatusProductChangeList($this->_sid, 0, 0, $beginProduct, $size);
            $productRe = (new \Business\JavaApi\Product\ProductDistribution)->queryUpperOrLowerList($this->_sid, 0, $beginProduct, 0, $size);
            //合并处理后筛选出时间最近的几条
            $tmpList = array_merge($priceNoticeList, $productRe['data']['list']);
            $tmpList = $tmpList ? $this->_parseExtraInfo($tmpList) : [];
            $list    = array_slice($tmpList, 0, 5);

            //缓存数据
            $this->_setCache($list);
        } else {
            $list = json_decode($cacheData);
        }

        $this->apiReturn(200, $list, 'success');
    }

    /**
     * 系统通知/平台公告
     *
     * @return [type] [description]
     */
    public function getSysNotice()
    {
//        //系统通知类型
//        $type = I('type', 0, 'intval');
        //条数
        $size = I('size', 10, 'intval');

        if (!$cacheData = $this->_cache->get($this->_cacheKey)) {
            $page   = 1;
            $status = 0;
            $loginInfo = $this->getLoginInfo();
            $field = 'a.id,a.title,a.create_time';
            //获取指定用户
            $list = (new \Model\Notice\Announce())->getSysNotice(
                1,
                $page,
                $size,
                $status,
                $field,
                'a.update_time desc',
                [],
                $loginInfo['account']
            );
            //获取普通重要公告
            $alllist = (new \Model\Notice\Announce())->getSysNotice(
                0,
                $page,
                $size,
                $status,
                $field,
                'a.update_time desc'
            );
            $list = array_merge($list,$alllist);
            usort($list, function ($val1, $val2) {
                if (strtotime($val1['create_time']) > strtotime($val2['create_time'])) {
                    return -1;
                } elseif (strtotime($val1['create_time']) == strtotime($val2['create_time'])) {
                    return 0;
                } else {
                    return 1;
                }
            });
            $this->_setCache($list);
        } else {
            $list = json_decode($cacheData);
        }

        $this->apiReturn(200, $list);
    }

    /**
     * 产品上下架通知列表页
     *
     * <AUTHOR>
     * @date   2017-03-27
     */
    public function productChangeList()
    {
        //最近几天
        $recentDay = I('recent_day', 10, 'intval');
        //上次自供应产品查找的位置
        $selfPos = I('self_pos', 0, 'intval');
        //上次转分销产品查找的位置
        $disPos = I('dis_pos', 0, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');

        if (!$recentDay) {
            $this->apiReturn(204, [], '参数错误');
        }

        //结束时间
        $end = time();
        //开始时间
        $begin  = $end - $recentDay * 3600 * 24;
        $begin  = date('Y-m-d', $begin);
        // $tModel = new EvoluteApi();
        // $result = $tModel->getStatusProductChangeList($this->_sid, $selfPos, $disPos, $begin, $size);
        $result = (new \Business\JavaApi\Product\ProductDistribution)->queryUpperOrLowerList($this->_sid, $selfPos, $begin, $disPos, $size);

        if ($result['code'] != 200) {
            $return = [
                'list'     => [],
                'self_pos' => 0,
                'dis_pos'  => 0,
            ];

            $this->apiReturn(200, $return);
        }
        $result = $result['data'];
        if ($result['selfpos'] == $selfPos && $result['disPos'] == $disPos) {
            //没有新数据了
            $list = [];
        } else {
            $list = $result['list'];
        }

        $list = $list ? $this->_parseExtraInfo($list) : [];

        $return = [
            'list'     => $list,
            'self_pos' => $result['selfpos'],
            'dis_pos'  => $result['disPos'],
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 产品价格变动通知列表页
     *
     * <AUTHOR>
     * @date   2017-03-27
     *
     */
    public function priceChangeList()
    {
        //最近几天
        $recentDay = I('recent_day', 10, 'intval');
        //上次自供应产品查找的位置
        $selfPos = I('self_pos', 0, 'intval');
        //上次转分销产品查找的位置
        $disPos = I('dis_pos', 0, 'intval');
        //上级供货价变化查找的位置
        $supplyPos = I('supply_pos', 0, 'intval');
        //每页条数
        $size = I('size', 100, 'intval');
        //资源中心变动最后查询记录位置
        $rurchase = I('rurchase', 0, 'int');
        //页面来源（资源中心）
        $source = I('source',0,'int');
        if (!$recentDay) {
            $this->apiReturn(204, [], '参数错误');
        }

        //结束时间
        $end = time();
        //开始时间
        $begin      = $end - $recentDay * 3600 * 24;
        $begin      = date('Y-m-d', $begin);
        // $evoluteApi = new EvoluteApi();
        // $result     = $evoluteApi->getPriceProductChangeList($this->_sid, $selfPos, $disPos, $supplyPos, $begin, $size,$rurchase,$source);
        $result = (new \Business\JavaApi\Product\ProductDistributionPrice())->distributionPriceChangeQuery($this->_sid, $selfPos, $disPos, $supplyPos, $begin, $rurchase, $source, $size);

        if ($result['code'] != 200) {
            $return = [
                'list'     => [],
                'self_pos' => 0,
                'dis_pos'  => 0,
            ];

            $this->apiReturn(200, $return);
        }
        $result = $result['data'];
        if ($result['selfpos'] == $selfPos && $result['disPos'] == $disPos
            && $result['supply'] == $supplyPos && $result['rurchase'] == $rurchase) {
            //没有新数据了
            $list = [];
        } else {
            $list = $result['list'];
        }

        $list = $list ? $this->_parseExtraInfo($list) : [];

        $return = [
            'list'       => $list,
            'self_pos'   => $result['selfpos'],
            'dis_pos'    => $result['disPos'],
            'supply_pos' => $result['supply'],
            'rurchase'  => $result['rurchase'],
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 合作伙伴变化列表页
     *
     * <AUTHOR>
     * @date   2017-04-06
     */
    public function partnerChangeList()
    {

        $type = I('type', 0, 'intval');
        //最近几天
        $recentDay = I('recent_day', 10, 'intval');
        //页数
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');

        if (!$recentDay) {
            $this->apiReturn(204, [], '参数错误');
        }

        //结束时间
        $end = time();
        //开始时间
        $begin = $end - $recentDay * 3600 * 24;

        //加载用户模型
        $RelationM = new \Model\Member\MemberRelationship();
        $MemberM   = new \Model\Member\Member();

        if ($type == 0 || $type == 1) {
            $asSupply = true;
        } else {
            $asSupply = false;
        }

        if ($type == 0 || $type == 2) {
            $add = true;
        } else {
            $add = false;
        }

        $result = $RelationM->getPartnerChangeList($this->_sid, $begin, $end, $page, $size, $asSupply, $add);

        //获取分销商名称
        if ($asSupply) {
            $midArr = array_column($result['list'], 'fid');
        } else {
            $midArr = array_column($result['list'], 'sid');
        }
        $data = [];
        if ($midArr) {
            $nameMap = $MemberM->getMemberInfoByMulti($midArr, 'id', 'id,dname', true);
            foreach ($result['list'] as &$item) {
                $data[] = [
                    'name' => $nameMap[$asSupply ? $item['fid'] : $item['sid']]['dname'],
                    'time' => $item['create_time'],
                ];
            }
        }

        $return = [
            'total'      => $result['total'],
            'page'       => $page,
            'total_page' => ceil($result['total'] / $size),
            'list'       => $data,
        ];

        $this->apiReturn(200, $return);

    }

    /**
     * 填充门票名称和所在地区信息
     *
     * <AUTHOR>
     * @date   2017-03-27
     *
     * @param  array     $list 待处理的列表数据
     *
     * @return array
     */
    private function _parseExtraInfo($list)
    {
        //加载ticket模型
        $commodityTicketBiz = new Ticket();
        $pidArr  = array_column($list, 'pid');
        $nameMap = $commodityTicketBiz->getTicketAndLandInfoByArrPidHandle($pidArr);
        $productBiz = new Product();
        $areaMap = $productBiz->parseCityProvinceByPid($pidArr);

        //获取产品名称
        foreach ($list as &$item) {
            $item['diff'] = (int) $item['diff'];
            //票类名称
            $item['proName'] = $nameMap[$item['pid']]['ttitle'];
            //景区名称
            $item['landName'] = $nameMap[$item['pid']]['title'];
            //城市名称
            $item['city'] = $areaMap[$item['pid']]['city'];
            //省份名称
            $item['province'] = $areaMap[$item['pid']]['province'];
            //分销来源
            if (!empty($item['source'])){
                $item['source'] = self::$distributionSourceArr[$item['source']]??$item['source'];
            }
        }

        //时间排序
        //usort($list, function ($item1, $item2) {
        //    if ($item1['time'] > $item2['time']) {
        //        return -1;
        //    } else {
        //        return 1;
        //    }
        //});

        return $list;
    }

    /**
     * 设置数据缓存
     *
     * <AUTHOR>
     * @date   2017-03-22
     *
     * @param  array     $data 要缓存的数据
     */
    private function _setCache($data)
    {

        $this->_cache->set($this->_cacheKey, json_encode($data), '', self::CACHE_TIME);

    }

    /**
     * 更新重要公告是否同意
     *
     * <AUTHOR>
     * @date   2020-8-26
     *
     * @param  array
     */
    public function agreeClause()
    {
        $status     = I('status', 1 , 'intval');//0未同意1同意2不同意
        $loginInfo     = $this->getLoginInfo();
        $acconut       = $loginInfo['account'];
        $dname         = $loginInfo['dname'];
        $data          = [
            'dname' => $dname,
            'c_status' => $status,
            'update_time' => time()
        ];
        $announceModel = new Announce();
        $result        = $announceModel->updateAnnounceClause($acconut, $data);

        if ($result) {
            $this->apiReturn(200, [], '成功');
        }else{
            $this->apiReturn(400, [], '失败');
        }
    }
    

}
