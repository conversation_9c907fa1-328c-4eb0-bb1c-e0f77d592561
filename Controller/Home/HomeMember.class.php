<?php

/**
 * 会员信息相关接口
 *
 * <AUTHOR>
 */

namespace Controller\Home;

use Business\AppCenter\Module;
use Business\Mall\WechatShop;
use Business\Member\MemberRelation;
use Business\Member\SmallApp;
use Business\MultiDist\Member;
use Business\Tools\Tools;
use Endroid\QrCode\QrCode;
use Library\Cache\Cache;
use Library\Cache\CacheRedis;
use Library\Controller;
use Model\Order\RefundAuditModel;
use \Business\Finance\AmountLedgerFreeze as AmountLedgerFreezeBiz;

class HomeMember extends Controller
{
    private $_sid; //主账号id

    private $_mid; //登陆用户id

    private $sdtype; //主账号类型

    private $_qx; //有关权限的字符串

    private $_redisPrefix = 'tuding:'; //菜单栏图钉缓存

    private $_memType; //账号类型

    public function __construct()
    {
        //主账号id
        $this->_sid = $this->isLogin('ajax');

        //主账号
        $loginInfo       = $this->getLoginInfo('ajax', false, false);
        $this->_saccount = $loginInfo['saccount'];
        //主账号类型
        $this->sdtype = $loginInfo['sdtype'];

        //登陆用户id
        $this->_mid = $loginInfo['memberID'];
        //账号类型
        $this->_memType = $loginInfo['dtype'];
        $this->_qx      = $loginInfo['qx'];        //有关权限的字符串
    }

    /**
     * 获取用户信息
     *
     * @return void
     */
    public function getMemberInfo()
    {
        $memModel = new \Model\Member\Member();

        $mainInfo = $memModel->getMemberInfo($this->_mid);

        if (!$mainInfo) {
            $this->apiReturn(204, [], '账号不存在');
        }

        //首页的异常待处理订单及淘宝补单数量
        $abnormal       = 0;
        $taobao         = 0;
        //退票审核
        $isShowAbonrmal = 0;
        //淘宝补单
        $isShowTaobao   = 0;
        //上游异常订单通知
        $isShowUpnum    = 0;
        //下游失败订单通知
        $isShowDownnum  = 0;
        $isShowSmall    = 0;
        $homeUrl        = $this->_getMallLink();
        $employeeSettlementAuth    = 1;

        if ($this->_memType != 6) {
            //账户余额
            $amountLedgerFreezeBiz = new AmountLedgerFreezeBiz();
            if ($amountLedgerFreezeBiz->isFrozenEnabled($this->_sid)) {
                $remain        = $amountLedgerFreezeBiz->getAccountBalance($this->_sid);
                $frozenEnabled = true;
            } else {
                $remain = $memModel->getPlatformBalanceByMemberId($this->_mid);
            }

            //$remain = $memModel->getMoney($this->_mid, 0);

            //其他账号显示
            $isShowAbonrmal = 1;
            $isShowTaobao   = 1;
            $isShowUpnum = 1;
            $isShowDownnum = 1;

            //退票审核
            $abnormal = $this->_countAbnormalOrder($this->_sid, 7);
            //淘宝补单，由中台提供接口
//            $taobao = $this->_countTaobaoAbnormal($this->_sid, 7);
        } else {
            //员工账号不显余额
            $remain                 = 0;
            $employeeSettlementAuth = 0;

            //获取出员工权限  当登录用户为员工时  判断是否有异常待处理订单及淘宝补单的权限
            if ($mainInfo['member_auth']) {
                $mainInfoAuth = explode(',', $mainInfo['member_auth']);
                //退票审核
                if (in_array("terchange", $mainInfoAuth)) {
                    $isShowAbonrmal = 1;
                    $abnormal       = $this->_countAbnormalOrder($this->_sid, 7);
                }

                //淘宝补单
                if (in_array("api_order_taobao", $mainInfoAuth)) {
                    $isShowTaobao = 1;
                    //淘宝补单，由中台提供接口
//                    $taobao       = $this->_countTaobaoAbnormal($this->_sid, 7);
                }

                //上游异常订单通知
                if (in_array('api_order_upstream', $mainInfoAuth)) {
                    $isShowUpnum = 1;
                }

                //下游失败订单通知
                if (in_array('api_order_remote', $mainInfoAuth)) {
                    $isShowDownnum = 1;
                }

                //票款结算
                if (in_array("accourtmanage", $mainInfoAuth)) {
                    $employeeSettlementAuth = 1;
                    $amountLedgerFreezeBiz = new AmountLedgerFreezeBiz();
                    if ($amountLedgerFreezeBiz->isFrozenEnabled($this->_sid)) {
                        $remain        = $amountLedgerFreezeBiz->getAccountBalance($this->_sid);
                        $frozenEnabled = true;
                    } else {
                        $remain = $memModel->getMoney($this->_sid, 0);
                    }
                }
            }
        }
        $wechatShopBiz = new WechatShop();
        $res           = $wechatShopBiz->getWxShopConfig($this->_sid);
        if ($res['code'] == 200 && $res['data']) {
            $wechatModule = load_config('wx_module','authority');
            $isShowSmall  = $res['data']['home_qrcode_type'];
            $isDefault    = false;
            $moduleRes    = $this->_getUserOpenWechatModule($this->_sid,array_keys($wechatModule));
            foreach ($wechatModule as $k => $v){
                if (!isset($moduleRes[$k]) && $v['is_show'] == 1){
                    $moduleRes = array_merge($moduleRes,[$k => ['module_id' => $k]]);   //所有人都有
                    break;
                }
            }
            foreach ($wechatModule as $key => $value){
                if (isset($moduleRes[$key]) && $isShowSmall == $value['type']){
                    $isDefault    = false;
                    $homeUrl      = $this->_getMallLink($isShowSmall);
                    break;
                }else{
                    $isDefault = true;
                }
            }

            if ($isDefault){
                $isShowSmall  = 0;
                $homeUrl      = $this->_getMallLink($isShowSmall);
            }
        }
        $partnerChange = (new MemberRelation())->partnerChange($this->_sid, $this->_memType, $this->_qx);
     
        if ($partnerChange['code'] == 200) {
            $partnerChange = $partnerChange['data']['total'];
        }else{
            $partnerChange = 0;
        }
        $loginInfo       = $this->getLoginInfo('ajax', false, false);
        $unreadmsg       = empty($loginInfo['unreadmsg']) ? 0 : $loginInfo['unreadmsg'];
        $return = [
            'unread'                   => $unreadmsg,
            'remainMoney'              => $remain,
            'is_show_abonrmal'         => $isShowAbonrmal,
            'is_show_taobao'           => $isShowTaobao,
            'is_show_upnum'            => $isShowUpnum,
            'is_show_downnum'          => $isShowDownnum,
            'abnormalOrder'            => $abnormal,
            'taobao'                   => $taobao,
            'isShowSmall'              => $isShowSmall,
            'home_url'                 => $homeUrl,
            'partner_change'           => $partnerChange,
            'employee_settlement_auth' => $employeeSettlementAuth,
            'frozenEnabled'            => $frozenEnabled ?? false,
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 设置提醒
     * <AUTHOR>
     * @date   2017-03-14
     *
     * @param  $val  int 提醒阈值
     */
    public function setRemainWaring()
    {
        $val = I('val', 0, 'intval');

        //是否绑定手机号
        $mModel = new \Model\Member\Member();

        $info = $mModel->getMemberInfo($this->_sid, 'id', 'mobile');

        if (!$info['mobile']) {
            $this->apiReturn(204, [], '请先绑定手机号');
        }

        $data = [
            'early_warning' => $val
        ];

        $memberBiz = new \Business\Member\Member();
        $result = $memberBiz->updateMemberExtInfo($this->_sid, $data);

        if ($result) {
            $this->apiReturn(200, [], '设置成功');
        } else {
            $this->apiReturn(204, [], '设置失败');
        }
    }

    /**
     * 新版微商城二维码链接
     *
     * <AUTHOR>
     * @date   2017-03-14
     *
     */
    public function mallQrcode()
    {
        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();
        $wechatShopBiz = new WechatShop();
        $result        = $wechatShopBiz->getWxShopConfig($this->_sid);
        $type           = 0;
        if (!empty($result['data'])){
            $type = $result['home_qrcode_type'];
        }
        $url = $this->_getMallLink($type);

        $qrCode = new QrCode();
        $qrCode->setText($url)
            ->setSize(86)
            ->setPadding(0)
            ->setErrorCorrection('high')
            ->setForegroundColor(['r' => 0, 'g' => 0, 'b' => 0, 'a' => 0])
            ->setBackgroundColor(['r' => 255, 'g' => 255, 'b' => 255, 'a' => 0])
            ->setLabelFontSize(16)
            ->setImageType('png');

        header('Content-Type: ' . $qrCode->getContentType());
        $qrCode->render();
    }

    /**
     * 统计异常订单数量
     *
     * @param  int $memberId 会员id
     * @return int
     */
    private function _countAbnormalOrder($memberId, $dayRange = 0)
    {
        /** @var CacheRedis $redis */
        $redis = Cache::getInstance('redis');
        $cacheKey = 'platform:countAbnormalOrder:' . $memberId;
        $abnormal = $redis->get($cacheKey);
        if ($abnormal !== false) {
            return $abnormal;
        }
        $timestamp = time();
        $now  = date('H', $timestamp) + 0;
        $date = date('Ymd', $timestamp);
        if ($date > 20181001 && $date < 20181008) {
            if ($now > 8 && $now < 15) {
                return mt_rand(0, 100);
            }
        }
        $timeType = $beginTime = $endTime = null;
        if ($dayRange > 0) {
            $beginTime = --$dayRange ? strtotime("-{$dayRange} days", $timestamp) : $timestamp;
            $beginTime = strtotime(date('Y-m-d', $beginTime));
            $endTime = $timestamp;
            //查询添加时间
            $timeType = 1;
        }
        $auditModel = new RefundAuditModel('slave');
        $abnormal   = $auditModel->getAuditListNew(
            $memberId,
            null,
            null,
            $timeType,
            $beginTime,
            $endTime,
            0,
            null,
            true
        );
        $abnormal   = array_values($abnormal)[0];
        $redis->setex($cacheKey, 60, $abnormal);
        return $abnormal;
    }

    /**
     * 统计淘宝需要补单的订单数
     *
     * <AUTHOR>
     * @date   2017-03-14
     *
     * @param  int  $memberId  会员id
     *
     * @return int
     */
    public function _countTaobaoAbnormal($memberId, $dayRange = 0)
    {
        $redisKey    = 'abnormalTaoNum:' . $memberId;
        $redis       = Cache::getInstance('redis');
        $abnormalNum = $redis->get($redisKey);
        if ($abnormalNum !== false) {
            return $abnormalNum;
        }
        $timestamp = time();
        $addTime = [];
        if ($dayRange > 0) {
            $beginTime = --$dayRange ? strtotime("-{$dayRange} days", $timestamp) : $timestamp;
            $beginTime = date('Y-m-d 00:00:00', $beginTime);
            $endTime = date('Y-m-d H:i:s', $timestamp);
            $addTime = [$beginTime, $endTime];
        }
        //統一都走新接口
        $taobaoModel = new \Model\Taobao\Taobao();
        $count       = $taobaoModel->countExceptionTaobaoLogByUser($memberId, $addTime);

        // $sidArr = load_config('sid_array', 'enable_new_taobao');
        // if (in_array($memberId, $sidArr)) {
        //     //新接口
        //     $taobaoModel = new \Model\Taobao\Taobao();
        //     $count       = $taobaoModel->countExceptionTaobaoLogByUser($memberId);
        // } else {
        //     //旧接口
        //     $taobaoModel = new \Model\Taobao\Taobao();
        //     $count       = $taobaoModel->countTaobaoLogByUserAndStatus($memberId, 0);
        // }

        $redis->set($redisKey, $count, '', 60 * 2);
        return $count;
    }

    /**
     * 设置菜单栏图钉
     * <AUTHOR>
     * @date   2017-3-20
     *
     * @param  string  $action   add  添加图钉  delete 去除图钉
     * @param  string  $tag      图钉的菜单标识
     *
     * @return array   code = 200 成功   code = 400 失败
     */
    public function tuDingConfig()
    {
        $this->apiReturn(200, '', '成功');
    }

    /**
     * 获取当前用户配置的图钉
     * <AUTHOR>
     * @date   2017-3-21
     *
     * @return array
     */
    public function getTuDing()
    {
        return [];
    }

    /**
     * 保存首页的二维码配置
     * <AUTHOR>
     * @date   2020-02-24
     *
     */
    public function saveHomeQrCodeConfig()
    {
        if ($this->_memType == 10) {
            $this->apiReturn(400, [], '分销专员，且不可设置');
        }
        $type          = I('post.type', 0, 'intval');
        $weChatShopBiz = new WechatShop();
        $res           = $weChatShopBiz->saveHomeQrCodeConfig($this->_sid, $type);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], [], $res['msg']);
        } else {
            $this->apiReturn(500, '', '系统异常');
        }
    }

    /**
     * 获取首页显示的二维码类型
     * <AUTHOR>
     * @date   2020-02-24
     *
     */
    public function getHomeQrCodeType()
    {
        $resList   = load_config('wx_module','authority');
        $res       = $this->_getUserOpenWechatModule($this->_sid,array_keys($resList));
        //$res       = $moduleBiz->getUserOpenModuleByModuleId($this->_sid, array_keys($resList));
        if (empty($res)) {
            //代表啥模块都没开通返回给前端一个默认新版
            $resList[58]['is_show']    = 1;
            $resList[58]['url']        = $this->_getMallLink();
            $resList[58]['is_default'] = 1;
        } else {
            $wechatShopBiz  = new WechatShop();
            $defaultCodeRes = $wechatShopBiz->getWxShopConfig($this->_sid);
            $defaultType    = $defaultCodeRes['code'] == 200 ? $defaultCodeRes['data']['home_qrcode_type'] : 0;
            foreach ($resList as $key => $value) {
                if (isset($res[$key])) {
                    $resList[$key]['is_show'] = 1;
                    $resList[$key]['is_default'] = 0;
                    //$resList[$key]['url']     = $this->_getMallLink($value['type']);
                    if ($value['type'] == $defaultType) {
                        $resList[$key]['is_default'] = 1;
                    }
                }else{
                    $resList[$key]['is_default'] = 0;
                }
            }
            foreach ($resList as $k => $v){
                if ($v['is_show'] == 1){
                    $resList[$k]['url']     = $this->_getMallLink($v['type']);
                }
            }
        }
        $this->apiReturn(200, array_values($resList));
    }

    /**
     * 获取新版微商城的地址
     *
     * <AUTHOR>
     * @date   2017-03-14
     *
     * @param  int  $type  类型 0新版微商城 1旧版 2小程序
     *
     * @return url
     */
    private function _getMallLink($type = 0)
    {
        if ($type == 1) {
            //旧版微商城地址
            $url = MOBILE_DOMAIN . 'wx/c/index.html';
            $url = str_replace('wx.', $this->_saccount . '.', $url);
        } elseif ($type == 2) {
            $smallBiz = new SmallApp();
            $smallUrl = $smallBiz->getPageAppCodeConfig($this->_sid, 'pages/index/index', '123624');    //目前用在小程序不在这边获取
            if ($smallUrl['code'] == 200) {
                $url = $smallUrl['url'];
            } else {
                $url = '';
            }
        } else {
            //新版微商城地址
            $url = MOBILE_DOMAIN . 'h5';
            $url = str_replace('wx.', $this->_saccount . '.', $url);
        }

        return $url;
    }
    private function _getUserOpenWechatModule($sid,$arrModule){
        $cacheRedis = Cache::getInstance('redis');
        $redisKey   = 'wechat:module'.$sid;
        $expireTime = 3600;
        if (ENV != 'PRODUCTION'){
            $expireTime = 10;
        }
        $res        = $cacheRedis->get($redisKey,'',true);
        if ($res === false){
            $moduleBiz = new Module();
            $res       = $moduleBiz->getUserOpenModuleByModuleId($sid, $arrModule);
            $cacheRedis->set($redisKey,$res,'',$expireTime,true);
        }
        return $res;
    }

    /**
     * 获取常用功能
     * <AUTHOR>
     * @date   2021-01-13
     *
     */
    public function getCommonFunction()
    {
        if (!$this->isLogin('ajax')) {
            $this->apiReturn(400, [], '请登陆');
        }

        $userToolsBiz = new Tools();
        $res          = $userToolsBiz->getCommonFunction($this->_mid, $this->_memType, $this->sdtype, $this->_sid);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, '', '系统异常');
        }
    }

    public function getFunctionForTag()
    {
        if (!$this->isLogin('ajax')) {
            $this->apiReturn(400, [], '请登陆');
        }

        $userToolsBiz = new Tools();
        $res          = $userToolsBiz->getFunctionForTag($this->_mid, $this->_memType, $this->sdtype, $this->_sid);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, '', '系统异常');
        }
    }
    /**
     * 提供标签展示接口
     */
    public function getApplyTagList()
    {
        if (!$this->isLogin('ajax')) {
            $this->apiReturn(400, [], '请登陆');
        }
        $tagName          = I('get.name', '', 'string');
        $userToolsBiz = new Tools();
        $res          = $userToolsBiz->getApplyTagList($tagName);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, '', '系统异常');
        }
    }

    /**
     * 设置常用功能
     * <AUTHOR>
     * @date   2021-01-13
     *
     */
    public function setCommonFunction()
    {
        if (!$this->isLogin('ajax')) {
            $this->apiReturn(400, [], '请登陆');
        }

        $commonTagName = I('post.common_tag_name',[]);

        $userToolsBiz = new Tools();
        $res          = $userToolsBiz->setCommonFunction($this->_mid, $this->_memType, $this->sdtype, $this->_sid, $commonTagName);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, '', '系统异常');
        }
    }

    /**
     * 设置应用菜单得排序
     * <AUTHOR>
     * @date   2021-11-03
     *
     */
    public function setApplyMenuSort()
    {
        if (!$this->isLogin('ajax')) {
            $this->apiReturn(400, [], '请登陆');
        }

        $commonTagName = I('post.sort_data', []);
        $pageInfo      = $this->getLoginPageInfo();
        $userToolsBiz  = new Tools();
        if (!is_array($commonTagName)) {
            $this->apiReturn(203, [], '参数错误');
        }
        $res = $userToolsBiz->setApplyMenuSort($this->_mid, $commonTagName, $pageInfo['leftBar']);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, '', '系统异常');
        }
    }



}
