<?php
/**
 * 新版首页相关数据接口
 * Created by Sublime.
 * User: chenyanbin
 * Date: 17-1-4
 * Time: 上午11:20
 */

namespace Controller\Home;

use Business\Authority\DataAuthLogic;
use Library\Cache;
use Library\Controller;
use Model\Member;
use Model\Member\MemberRelationship;
use Model\Order;
use Model\Product;
use Model\Report;
use Business\Common\CommonConfig as CommonConfigBiz;

class HomeOrder extends Controller
{
    private $memberId;
    private $model;

    //用户登录信息
    private $_loginInfo = null;

    //首页数据缓存统一前缀
    private $_prefix = 'newindex:';

    public function __construct()
    {
        $this->memberId = $this->isLogin();
        $this->model    = new Order\HomeOrder();

        //登录信息
        $this->_loginInfo = $this->getLoginInfo();
    }

    /**
     * 员工权限过滤
     * <AUTHOR>
     * @date   2018-07-19
     *
     * @param  string $auth 权限信息
     */
    private function _authFilter($auth)
    {
        $auths = $auth;
        //判断登录用户权限
        if ($this->_loginInfo['dtype'] == 6 && !$this->judgeAccountAutho($this->_loginInfo['memberID'], $auths)) {
            $this->apiReturn(306, [], '没有相应权限，无法获取数据');
        }
    }

    public function YesterdayInfo()
    {
        //员工权限过滤
        // $this->_authFilter('orderquery');
        $isFlush  = I('is_flush', 0, 'intval');
        $only7    = I('seven', false, 'bool'); // true=返回7日数据 false=返回7日和30日数据
        $oldToday = I('old', 0, 'intval');     //true 返回12日数据和60日数据
        $isNew    = I('is_new', 0, 'intval');  //1的话用pft_report_order_two和check_two
        $fid      = $this->memberId;

        $flushKey      = 'today_flush_time' . $fid;
        $flushInterval = 5 * 60;

        $redis         = Cache\Cache::getInstance('redis');
        //刷新频率控制
        if ($isFlush) {
            //判断下是否有锁
            $lockRet = $redis->lock($flushKey, 1, $flushInterval);
            if (!$lockRet) {
                $this->apiReturn(400, [], '5分钟后再刷新');
            }
        }

        if ($oldToday) {
            $weekDay     = date("Y-m-d", strtotime("-7 day"));
            $monthDay    = date("Y-m-d", strtotime("-30 day"));
            $oldWeekDay  = date("Y-m-d", strtotime("-13 day"));
            $oldMonthDay = date("Y-m-d", strtotime("-59 day"));
            $timeKey     = date("Ymd") . 'old';
        } else {
            $weekDay     = date("Y-m-d");
            $monthDay    = date("Y-m-d");
            $oldWeekDay  = date("Y-m-d", strtotime("-6 day"));
            $oldMonthDay = date("Y-m-d", strtotime("-29 day"));
            $timeKey     = date("Ymd");
        }

        $cacheTime = 60 * 60 * 24;
        $todayKey  = $this->_prefix . "data:{$fid}:{$timeKey}";
        $redis     = Cache\Cache::getInstance('redis');
        $data      = $redis->get($todayKey);

        if (!empty($data) && !$isFlush) {
            $data = json_decode($data, true);
        } else {
            $sdtype      = $this->_loginInfo['sdtype'];
            $reportModel = new Report\Statistics();

            //返回数据
            $chTicketNumSeven = 0;
            $chSaleMoneySeven = 0;
            $chTicketNumMonth = 0;
            $chSaleMoneyMonth = 0;
            if ($sdtype == 0) {
                //供应商 取检票数据
                //近七日检票数
                $checking         = $reportModel->getCheckedList($oldWeekDay, $weekDay, 'product', '', '',
                    $this->memberId);
                $checkSum         = $checking['sum'];
                $chTicketNumSeven = $checkSum['ticket_num'] + $checkSum['finish_ticket'] - $checkSum['revoke_ticket']; //近7日检票数
                $chSaleMoneySeven = $checkSum['sale_money'] + $checkSum['finish_sale_money'] - $checkSum['revoke_sale_money']; //近7日检票金额

                if (!$only7) {
                    $checking         = $reportModel->getCheckedList($oldMonthDay, $monthDay, 'product', '', '',
                        $this->memberId);
                    $checkSum         = $checking['sum'];
                    $chTicketNumMonth = $checkSum['ticket_num'] + $checkSum['finish_ticket'] - $checkSum['revoke_ticket']; //近30日检票数
                    $chSaleMoneyMonth = $checkSum['sale_money'] + $checkSum['finish_sale_money'] - $checkSum['revoke_sale_money']; //近30日检票金额
                }
            } elseif ($sdtype == 1) {
                //分销商 取预订票数
                $booking          = $reportModel->getOrderList($oldWeekDay, $weekDay, 'product', '', '',
                    $this->memberId);
                $bookSum          = $booking['sum'];
                $chTicketNumSeven = $bookSum['ticket_num'] - $bookSum['cancel_ticket'] - $bookSum['revoke_ticket'];
                $chSaleMoneySeven = $bookSum['sale_money'] - $bookSum['cancel_sale_money'] - $bookSum['revoke_sale_money'];

                if (!$only7) {
                    $booking          = $reportModel->getOrderList($oldMonthDay, $monthDay, 'product', '', '',
                        $this->memberId);
                    $bookSum          = $booking['sum'];
                    $chTicketNumMonth = $bookSum['ticket_num'] - $bookSum['cancel_ticket'] - $bookSum['revoke_ticket'];
                    $chSaleMoneyMonth = $bookSum['sale_money'] - $bookSum['cancel_sale_money'] - $bookSum['revoke_sale_money'];
                }
            } else if ($sdtype == 7) {
                //集团账号 取检票数据
                //获取集团下面的成员
                $memberList = $this->_getGroupMemberList($this->memberId);

                if ($memberList) {
                    $checking         = $reportModel->getCheckedList($oldWeekDay, $weekDay, 'product', '', '',
                        $memberList);
                    $checkSum         = $checking['sum'];
                    $chTicketNumSeven = $checkSum['ticket_num'] + $checkSum['finish_ticket'] - $checkSum['revoke_ticket']; //近7日检票数
                    $chSaleMoneySeven = $checkSum['sale_money'] + $checkSum['finish_sale_money'] - $checkSum['revoke_sale_money']; //近7日检票金额

                    if (!$only7) {
                        $checking         = $reportModel->getCheckedList($oldMonthDay, $monthDay, 'product', '', '',
                            $memberList);
                        $checkSum         = $checking['sum'];
                        $chTicketNumMonth = $checkSum['ticket_num'] + $checkSum['finish_ticket'] - $checkSum['revoke_ticket']; //近30日检票数
                        $chSaleMoneyMonth = $checkSum['sale_money'] + $checkSum['finish_sale_money'] - $checkSum['revoke_sale_money']; //近30日检票金额
                    }
                }
            }

            //$refund        = $reportModel->getCancelList($yesterday, $today, 'product', '', '', $this->memberId);
            //$refundSum     = $refund['sum'];
            //$reTicketNum   = $refundSum['ticket_num']; //昨日退票数
            //$reCancelMoney = $refundSum['cancel_money']; //昨日退票金额

            $data      = [
                //'bookOrderNum'  => $bookOrderNum,
                //'bookSaleMoney' => $bookSaleMoney,
                //'reTicketNum'   => $reTicketNum,
                //'reCancelMoney' => $reCancelMoney,
                'chTicketNumSeven' => $chTicketNumSeven,
                'chSaleMoneySeven' => $chSaleMoneySeven,
                'chTicketNumMonth' => $chTicketNumMonth,
                'chSaleMoneyMonth' => $chSaleMoneyMonth,
            ];
            $cacheData = json_encode($data);
            $redis->set($todayKey, $cacheData, '', $cacheTime);

            //记录上传刷新时间
            if ($isFlush) {
                $redis->lock($flushKey, 1, $flushInterval);
            }
        }

        $this->apiReturn(200, ['data' => $data]);
    }

    /**
     * 获取新版首页今天汇总数据
     * <AUTHOR>
     * @date   2017-8-7
     *
     * @return array
     *  {
     *      'code' : 200,
     *      'data' : {
     *          'order_tickets' : 2, //今日预定票数
     *          'order_money' : 2, //今日预定金额 - 分
     *          'check_tickets' : 2, //今日验证票数
     *          'check_money' : 2, //今日验证金额 - 分
     *          'calculate_time' : '2017-01-23 17:51', //数据统计时间
     *      }
     *  }
     */
    public function todayInfo()
    {
        //员工权限过滤
        // $this->_authFilter('orderquery');

        $fid           = $this->memberId;
        $isFlush       = I('post.is_flush', 0, 'intval');
        $isNew         = I('post.is_new', 0, 'intval');
        $flushKey      = 'today_flush_time' . $fid;
        $flushInterval = 5 * 60;

        $data = [
            'order_tickets' => 0,
            'order_money' => 0,
            'old_order_tickets' => 0,
            'old_order_money' => 0,
            'check_tickets' => 0,
            'check_money' => 0,
            'old_check_tickets' => 0,
            'old_check_money' => 0
        ];
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition();
        if ($condition !== false) {
            //刷新频率控制
            $redis         = Cache\Cache::getInstance('redis');
            //刷新频率控制
            if ($isFlush) {
                //判断下是否有锁
                $lockRet = $redis->lock($flushKey, 1, $flushInterval);
                if (!$lockRet) {
                    $this->apiReturn(400, [], '5分钟后再刷新');
                }
            }

            $statisticsConfigLimit = (new \Business\Statistics\HomeOrder())->getHomeOverviewConfig($fid);
            //全部字段配置key
            $statisticsSignKey = $statisticsConfigLimit->allKeySign();
            //获取全部配置
            $extFilter = $statisticsConfigLimit->getConfig();

            $todayKey  = $this->_prefix . "today_summary:{$fid}:{$statisticsSignKey}";
            $cacheTime = 30 * 60;

            $redis = Cache\Cache::getInstance('redis');
            $data  = $redis->get($todayKey);

            if (!$data || $isFlush) {
                $data = [];
                //如果是缓存获取或是强制刷新
                $sdtype = $this->_loginInfo['sdtype'];
                if ($sdtype == 7) {
                    //集团账号获取所有的成员
                    $memberList = $this->_getGroupMemberList($fid);
                    $fidArr     = $memberList;
                } else {
                    $fidArr = $fid;
                }

                $staticModel = new Report\Statistics();
                $date        = date('Y-m-d');
                $yesterday   = date('Y-m-d', strtotime('-1 day'));
                //预订数据
                $orderRes              = $staticModel->getRealDataByFidTime($fidArr, $date, 1, false, $extFilter, $condition);
                $data['order_tickets'] = $orderRes ? $orderRes['ticket_num'] - $orderRes['cancel_ticket'] - $orderRes['revoke_ticket'] : 0;
                $data['order_money']   = $orderRes ? $orderRes['sale_money'] - $orderRes['cancel_sale_money'] - $orderRes['revoke_sale_money'] : 0;

                //昨天的预订数据
                $yesterdayOrderRes         = $staticModel->getRealDataByFidTime($fidArr, $yesterday, 1, false, $extFilter, $condition);
                $data['old_order_tickets'] = $yesterdayOrderRes ? $yesterdayOrderRes['ticket_num'] - $yesterdayOrderRes['cancel_ticket'] - $yesterdayOrderRes['revoke_ticket'] : 0;
                $data['old_order_money']   = $yesterdayOrderRes ? $yesterdayOrderRes['sale_money'] - $yesterdayOrderRes['cancel_sale_money'] - $yesterdayOrderRes['revoke_sale_money'] : 0;
                //验证数据
                $checkRes              = $staticModel->getRealDataByFidTime($fidArr, $date, 2, false, $extFilter, $condition);
                $data['check_tickets'] = $checkRes ? $checkRes['ticket_num'] - $checkRes['revoke_ticket'] + $checkRes['finish_ticket'] : 0;
                $data['check_money']   = $checkRes ? $checkRes['sale_money'] - $checkRes['revoke_sale_money'] + $checkRes['finish_sale_money'] : 0;
                //昨天的验证数据
                $yesterdayCheckRes         = $staticModel->getRealDataByFidTime($fidArr, $yesterday, 2, false, $extFilter, $condition);
                $data['old_check_tickets'] = $yesterdayCheckRes ? $yesterdayCheckRes['ticket_num'] - $yesterdayCheckRes['revoke_ticket'] + $checkRes['finish_ticket'] : 0;
                $data['old_check_money']   = $yesterdayCheckRes ? $yesterdayCheckRes['sale_money'] - $yesterdayCheckRes['revoke_sale_money'] + $checkRes['finish_sale_money'] : 0;
                //缓存数据
                $cacheData = json_encode($data);
                $redis->set($todayKey, $cacheData, '', $cacheTime);

                //记录上传刷新时间
                if ($isFlush) {
                    $redis->lock($flushKey, 1, $flushInterval);
                }
            } else {
                $data = json_decode($data, true);
            }
        }

        if (!$data) {
            $this->apiReturn(400, [], '数据获取失败');
        }

        //处理返回数据
        $resData                   = $data;
        $resData['calculate_time'] = date('Y-m-d H:i:s');

        $this->apiReturn(200, $resData);
    }

    /**
     * 方法合并 销售趋势 产品排行 渠道排行
     * <AUTHOR>
     * @date   2017-01-13
     *
     * @params int    $search_type      搜索方式  =1  票数   =2  总金额  默认1
     * @params date   $begin_time       开始时间  Y-m-d
     * @params date   $end_time         结束时间  Y-m-d
     * @params int    $compare_type     对比方式  =1 同比  =2 环比       默认1
     * @params int    $isGetThree       1 获取三个 0 获取销售趋势
     */
    public function saleTrends()
    {
        //员工权限过滤
        // $this->_authFilter('orderReport');

        $isGetThree   = I('post.isGetThree');
        $isGetCompare = I('post.isGetCompare', 0);
        $isNew        = I('post.is_new', 0, 'intval');
        //销售趋势
        $fid       = $this->memberId;
        $saleTrend = [];
        $return    = [
            'sale_trend'  => [],
            'product_use' => [],
            'sale_rank'   => [],
        ];

        //集团账号数据判断
        $sdtype = $this->_loginInfo['sdtype'];
        $fidArr = $fid;
        if ($sdtype == 7) {
            //集团账号获取所有的成员
            $memberList = $this->_getGroupMemberList($fid);
            $fidArr     = $memberList;
        }

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();

        $statisticsConfigLimit = (new \Business\Statistics\HomeOrder())->getHomeOverviewConfig($fid);
        //全部字段配置key
        $statisticsSignKey = $statisticsConfigLimit->allKeySign();
        //获取全部配置
        $extFilter = $statisticsConfigLimit->getConfig();

        try {
            $params = $this->_handleSaleTrendsParams($isGetCompare);
            if ($isGetCompare) {
                $key = 'sale_trends:' . date('Ymd') . ':' . $fid . ':merge';
            } else {
                $key = 'sale_trends:' . date('Ymd') . ':' . $fid;
            }
            // $redis     = Cache\Cache::getInstance('redis');
            // $saleTrend = $redis->get($key);
            //如果有缓存值 且 搜索的条件符合缓存要求
            $saleTrend = [];//暂时不用缓存
            if (!empty($saleTrend) && $params['isCache']) {
                $saleTrend = unserialize($saleTrend);
            } else {
                $business  = new \Business\Statistics\HomeOrder();
                $saleTrend = $business->saleTrends($params['bTimeNew'], $params['eTimeNew'], $params['bTimeOld'],
                    $params['eTimeOld'], $params['searchType'], $fidArr, $extFilter, $dataAuthLimit);
                if ($isGetCompare) {
                    $saleTrendNew       = $business->saleTrends($params['bTimeNew'], $params['eTimeNew'],
                        $params['bChainTimeOld'], $params['eChainTimeOld'], $params['searchType'], $fidArr, $extFilter, $dataAuthLimit);
                    $saleTrend['chain'] = $saleTrendNew['old'];
                }
                // if ($params['isCache']) {
                //     $str = serialize($saleTrend);
                //     $redis->set($key, $str, '', 60*60*24);
                // }
            }
        } catch (\Exception $e) {
            $saleTrend = [];
            $msg       = $e->getMessage();
        }
        $return['sale_trend'] = $saleTrend;

        if (!empty($isGetThree)) {
            $beginTime = I('post.begin_time');
            $endTime   = I('post.end_time');
            $isCache   = true;
            $start     = strtotime($beginTime);
            $end       = strtotime($endTime);
            $day       = 0;
            if ($start && $end) {
                $day = ($end - $start) / (60 * 60 * 24);
                if ($day != 6) {
                    $isCache = false;
                }
            }

            //获取三个
            //产品使用排行
            try {
                $key     = 'product_use_rank_v2:' . date('Ymd') . ':' . $fid . ":{$statisticsSignKey}";
                $redis   = Cache\Cache::getInstance('redis');
                $useRank = $redis->get($key);

                if (!empty($useRank) && $isCache) {
                    $useRank = unserialize($useRank);
                } else {
                    //$lidCount   = $model->getSaleLidCountInWeek($fidArr,$day);
                    $statisticsApiBuz = new \Business\AppCenter\Statistics();
                    $rank             = $statisticsApiBuz->getTicketSaleRankInWeek($fidArr, $day, $extFilter);
                    $rank             = $rank['data'];

                    $totalMoney = $statisticsApiBuz->getSaleTotalMoneyInWeekByOrder($fidArr, $day, $extFilter);
                    $totalMoney = $totalMoney['data'];

                    if (empty($rank) || !is_array($rank)) {
                        throw new \Exception("无数据");
                    }

                    $lidArr = array_column($rank, 'lid');
                    $tidArr = array_column($rank, 'tid');

                    //$landModel   = new Product\Land();
                    $ticketModel = new Product\Ticket();
                    //$nameRes     = $landModel->getLandInfoByMuli($lidArr, 'id, title');

                    $javaAPi   = new \Business\CommodityCenter\Land();
                    $nameRes   = $javaAPi->queryLandMultiQueryById($lidArr);
                    $ticketRes = $ticketModel->getTicketInfoByIdArr($tidArr, 'id,title');

                    $nameInfo = [];
                    if (!empty($nameRes)) {
                        foreach ($nameRes as $item) {
                            $nameInfo[$item['id']] = $item['title'];
                        }
                    }

                    $ticketInfos = [];
                    if (!empty($ticketRes)) {
                        foreach ($ticketRes as $item) {
                            $ticketInfos[$item['id']] = $item['title'];
                        }
                    }

                    foreach ($rank as &$value) {
                        $value['name']  = $nameInfo[$value['lid']] ?: '未知产品';
                        $value['tname'] = $ticketInfos[$value['tid']] ?: '未知票类';

                        if (!empty($totalMoney['money'])) {
                            $value['scale'] = round(($value['total_money'] / $totalMoney['money']) * 100, 2);
                        } else {
                            $value['scale'] = 0;
                        }

                        $value['total_money'] = round($value['total_money'] / 100, 2);

                        unset($value['lid']);
                    }

                    $sumScale   = array_sum(array_column($rank, 'scale'));
                    $otherScale = 100 - $sumScale;

                    $useRank = [
                        'rank'  => $rank,
                        'other' => $otherScale,
                    ];
                    if ($isCache) {
                        $str = serialize($useRank);
                        $redis->set($key, $str, '', 60 * 60 * 24);
                    }
                }
            } catch (\Exception $e) {
                $useRank = [];
                $msg     = $e->getMessage();
            }

            $return['product_use'] = $useRank;

            //渠道排行
            try {
                $fid      = $this->memberId;
                $key      = 'sale_rank_v2:' . date('Ymd') . ':' . $fid . ":{$statisticsSignKey}";
                $redis    = Cache\Cache::getInstance('redis');
                $saleRank = $redis->get($key);
                if (!empty($saleRank) && $isCache) {
                    $saleRank = unserialize($saleRank);
                } else {
                    $statisticsApiBuz = new \Business\AppCenter\Statistics();
                    $rank             = $statisticsApiBuz->getSaleRankInWeekByOrder($fidArr, $day, $extFilter);
                    $rank             = $rank['data'];
                    //$rank = $model->getSaleRankInWeek($fidArr,$day);
                    if (empty($rank) || !is_array($rank)) {
                        throw new \Exception("无数据");
                    }

                    $idArr = array_column($rank, 'reseller_id');

                    $memberModel = new Member\Member();
                    $nameRes     = $memberModel->getMemberInfoByMulti($idArr, 'id', 'id, dname');

                    if (!empty($nameRes) && is_array($nameRes)) {
                        foreach ($nameRes as $item) {
                            $nameInfo[$item['id']] = $item['dname'];
                        }
                    }

                    $nameIdArr = [];
                    if (!empty($nameInfo)) {
                        $nameIdArr = array_keys($nameInfo);
                    }

                    foreach ($rank as &$value) {
                        if (in_array($value['reseller_id'], $nameIdArr)) {
                            $value['name'] = empty($nameInfo[$value['reseller_id']]) ? '未知' : $nameInfo[$value['reseller_id']];
                        } else {
                            $value['name'] = '未知';
                        }

                        $value['total_money'] = round($value['total_money'] / 100, 2);
                        unset($value['reseller_id']);
                    }

                    $saleRank = $rank;
                    if ($isCache) {
                        $str = serialize($saleRank);
                        $redis->set($key, $str, '', 60 * 60 * 24);
                    }
                }
            } catch (\Exception $e) {
                $saleRank = [];
                $msg      = $e->getMessage();
            }
            $return['sale_rank'] = $saleRank;
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 七天产品使用排行
     * <AUTHOR>
     * @date   2017-01-16
     *
     * @return array(
     *              'rank' => [
     *                            "total_money": "135",    //价格
     *                            "name": "测试云票务手牌",   //名称
     *                            "scale": 67.5            //比例   67.5%
     *                        ],
     *              "other": 0,                //其它  比例   0%
     *              "lidCount": "19"           //产品总数
     *         )
     */
    public function productUseRank()
    {
        $code = 200;
        $data = '';
        $msg  = '';
        try {
            $fid   = $this->memberId;
            $key   = 'product_use_rank:' . date('Ymd') . ':' . $fid;
            $redis = Cache\Cache::getInstance('redis');
            $data  = $redis->get($key);
            if (!empty($data)) {
                $data = unserialize($data);
            } else {
                //集团账号数据判断
                $sdtype = $this->_loginInfo['sdtype'];
                $fidArr = $fid;
                if ($sdtype == 7) {
                    //集团账号获取所有的成员
                    $memberList = $this->_getGroupMemberList($fid);
                    $fidArr     = $memberList;
                }

                $model = new Report\ApplyerReport();

                $rank       = $model->getProductUseRankInWeek($fidArr);
                $totalMoney = $model->getSaleTotalMoneyInWeek($fidArr);
                $lidCount   = $model->getSaleLidCountInWeek($fidArr);

                if (empty($rank) || !is_array($rank)) {
                    throw new \Exception("无数据");
                }

                $idArr = array_column($rank, 'lid');
                //
                //$landModel = new Product\Land();
                //$nameRes   = $landModel->getLandInfoByMuli($idArr, 'id, title');

                $javaAPi   = new \Business\CommodityCenter\Land();
                $nameRes   = $javaAPi->queryLandMultiQueryById($idArr);

                $nameInfo = [];
                if (!empty($nameRes)) {
                    foreach ($nameRes as $item) {
                        $nameInfo[$item['id']] = $item['title'];
                    }
                }

                $nameIdArr = [];
                if (!empty($nameInfo)) {
                    $nameIdArr = array_keys($nameInfo);
                }

                foreach ($rank as &$value) {
                    if (in_array($value['lid'], $nameIdArr)) {
                        $value['name'] = $nameInfo[$value['lid']];
                    } else {
                        $value['name'] = '未知';
                    }

                    if (!empty($totalMoney['money'])) {
                        $value['scale'] = round(($value['total_money'] / $totalMoney['money']) * 100, 2);
                    } else {
                        $value['scale'] = 0;
                    }

                    $value['total_money'] = round($value['total_money'] / 100, 2);

                    unset($value['lid']);
                }

                $sumScale   = array_sum(array_column($rank, 'scale'));
                $otherScale = 100 - $sumScale;

                $data = [
                    'rank'     => $rank,
                    'other'    => $otherScale,
                    'lidCount' => $lidCount,
                ];

                $str = serialize($data);
                $redis->set($key, $str, '', 60 * 60 * 24);
            }
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 七天渠道使用排行(分销排行)
     * <AUTHOR>
     * @date   2017-01-16
     */
    public function saleRank()
    {
        $code = 200;
        $data = '';
        $msg  = '';
        try {
            $fid   = $this->memberId;
            $key   = 'sale_rank:' . date('Ymd') . ':' . $fid;
            $redis = Cache\Cache::getInstance('redis');
            $data  = $redis->get($key);

            if (!empty($data)) {
                $data = unserialize($data);
            } else {
                $model = new Report\ApplyerReport();

                //集团账号数据判断
                $sdtype = $this->_loginInfo['sdtype'];
                $fidArr = $fid;
                if ($sdtype == 7) {
                    //集团账号获取所有的成员
                    $memberList = $this->_getGroupMemberList($fid);
                    $fidArr     = $memberList;
                }

                $rank = $model->getSaleRankInWeek($fidArr);

                if (empty($rank) || !is_array($rank)) {
                    throw new \Exception("无数据");
                }

                $idArr = array_column($rank, 'reseller_id');

                $memberModel = new Member\Member();
                $nameRes     = $memberModel->getMemberInfoByMulti($idArr, 'id', 'id, dname');

                $nameInfo = [];
                if (!empty($nameRes)) {
                    foreach ($nameRes as $item) {
                        $nameInfo[$item['id']] = $item['dname'];
                    }
                }

                $nameIdArr = array_keys($nameInfo);

                foreach ($rank as &$value) {
                    if (in_array($value['reseller_id'], $nameIdArr)) {
                        $value['name'] = $nameInfo[$value['reseller_id']];
                    } else {
                        $value['name'] = '未知';
                    }

                    $value['total_money'] = round($value['total_money'] / 100, 2);
                    unset($value['reseller_id']);
                }

                $data = $rank;
                $str  = serialize($data);
                $redis->set($key, $str, '', 60 * 60 * 24);
            }
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 销售趋势 参数处理
     * <AUTHOR>
     * @date   2017-01-13
     *
     * @params int    $search_type      搜索数据  =1  票数   =2  总金额  默认1
     * @params date   $begin_time       开始时间  Y-m-d
     * @params date   $end_time         结束时间  Y-m-d
     * @params int    $compare_type     对比方式  =1 同比  =2 环比       默认1
     */
    private function _handleSaleTrendsParams($isGetCompare = 0)
    {
        $searchType  = I('post.search_type', 1, 'intval');
        $compareType = I('post.compare_type', 1, 'intval');
        $beginTime   = I('post.begin_time');
        $endTime     = I('post.end_time');

        if (!strtotime($beginTime) || !strtotime($endTime)) {
            throw new \Exception("时间格式错误");
        }

        if (strtotime($endTime) - strtotime($beginTime) > 60 * 60 * 24 * 100) {
            throw new \Exception("时间间隔不能多于100天");
        }
        if ($isGetCompare) {
            $bTimeNew = date('Ymd', strtotime($beginTime));
            $eTimeNew = date('Ymd', strtotime($endTime));
            //同比
            $bTimeOld = date('Ymd', strtotime('-1 year', strtotime($beginTime)));
            $eTimeOld = date('Ymd', strtotime('-1 year', strtotime($endTime)));

            //环比
            $space = strtotime($endTime) - strtotime($beginTime);

            $bTimeOldStap = strtotime($beginTime) - $space - (60 * 60 * 24);
            $eTimeOldStap = strtotime($beginTime) - (60 * 60 * 24);

            $bChainTimeOld = date('Ymd', $bTimeOldStap);
            $eChainTimeOld = date('Ymd', $eTimeOldStap);
        } else {
            switch ($compareType) {
                default:
                case 1:
                    //同比  去年同一时期
                    $bTimeNew = date('Ymd', strtotime($beginTime));
                    $eTimeNew = date('Ymd', strtotime($endTime));

                    $bTimeOld = date('Ymd', strtotime('-1 year', strtotime($beginTime)));
                    $eTimeOld = date('Ymd', strtotime('-1 year', strtotime($endTime)));

                    break;
                case 2:
                    //环比  上一周期
                    $bTimeNew = date('Ymd', strtotime($beginTime));
                    $eTimeNew = date('Ymd', strtotime($endTime));

                    $space = strtotime($endTime) - strtotime($beginTime);

                    $bTimeOldStap = strtotime($beginTime) - $space - (60 * 60 * 24);
                    $eTimeOldStap = strtotime($beginTime) - (60 * 60 * 24);

                    $bTimeOld = date('Ymd', $bTimeOldStap);
                    $eTimeOld = date('Ymd', $eTimeOldStap);

                    break;
            }
        }

        $res = $this->_judgeTime($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, $bChainTimeOld, $eChainTimeOld);
        if ($res === false) {
            throw new \Exception('不支持该时间段查询');
        } else {
            list($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, $bChainTimeOld, $eChainTimeOld) = $res;
        }

        $isCache = false;
        $tmpTime = date('Ymd', strtotime($endTime) - 60 * 60 * 24 * 6);

        if ($compareType == 1 && $searchType == 1 && $eTimeNew == date('Ymd') && $bTimeNew == $tmpTime) {
            //如果是默认的搜索条件 缓存结果
            $isCache = true;
        }
        if ($isGetCompare) {
            $data = [
                'searchType'    => $searchType,
                'bTimeNew'      => $bTimeNew,
                'eTimeNew'      => $eTimeNew,
                'bTimeOld'      => $bTimeOld,
                'eTimeOld'      => $eTimeOld,
                'bChainTimeOld' => $bChainTimeOld,
                'eChainTimeOld' => $eChainTimeOld,
                'isCache'       => $isCache,
            ];
        } else {
            $data = [
                'searchType' => $searchType,
                'bTimeNew'   => $bTimeNew,
                'eTimeNew'   => $eTimeNew,
                'bTimeOld'   => $bTimeOld,
                'eTimeOld'   => $eTimeOld,
                'isCache'    => $isCache,
            ];
        }

        return $data;
    }

    /**
     * 判断查询年份是否存在数据，当前只有16年及以后才有
     * @author: zhangyz
     * @date: 2020/4/18
     *
     * @param $date
     *
     * @return bool|array
     */
    private function _judgeTime($bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, $chainBegin, $chainEnd)
    {
        $newYear = date('Y', strtotime($bTimeNew));
        $oldYear = date('Y', strtotime($bTimeOld));

        if ($newYear < 2016) {
            return false;
        }

        //判断同比时间，再判断环比时间（环比时间比同比时间迟）
        if ($newYear >= 2016 && $oldYear < 2016) {
            $bTimeOld = $bTimeNew;
            $eTimeOld = $eTimeNew;

            if ($chainBegin) {
                $chainYear = date('Y', strtotime($chainBegin));
                if ($chainYear < 2016) {
                    $chainBegin = $bTimeNew;
                    $chainEnd   = $eTimeNew;
                }
            }
        }

        return [$bTimeNew, $eTimeNew, $bTimeOld, $eTimeOld, $chainBegin, $chainEnd];
    }

    /**
     * 根据集团账号ID获取集团成员
     *
     * @param  int $parentId
     *
     * @return array
     */
    private function _getGroupMemberList($parentId)
    {
        if (!$parentId) {
            return [];
        }

        $relationModel = new MemberRelationship();
        $tmpList       = $relationModel->getMemIdByGroupId($parentId);

        $resList = [];
        if ($tmpList) {
            $resList = array_column($tmpList, 'son_id');
            $resList = array_unique($resList);
        }

        return $resList;
    }

    /**
     * 首页数据概况 （预订数量 / 预订金额 取消数量 / 取消金额 验证数量 / 验证金额）
     *
     * @author: xwh
     * @date: 2021-01-13
     * @return array
     */
    public function homeOverviewData()
    {
        $isFlush  = I('post.is_flush', 0, 'intval');
        $sdtype   = $this->_loginInfo['sdtype'];
        $timeType = I('post.time_type', 'today');
        $fid      = $this->memberId;

        if (!in_array($timeType, [1, 2, 'today', 'yesterday'])) {
            $this->apiReturn(203, [], '时间参数错误');
        }

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();

        $homeOrderBiz = new \Business\Statistics\HomeOrder();

        if (in_array($timeType, ['today', 'yesterday'])) {
            // 今天 昨天
            $result = $homeOrderBiz->getRealDataByFidTime($isFlush, $fid, $sdtype, $timeType, $this->_loginInfo['memberID'], $dataAuthLimit);
        } elseif (in_array($timeType, [1, 2])) {
            //7天 30天
            $result = $homeOrderBiz->oldDataOverview($isFlush, $fid, $sdtype, $timeType, $this->_loginInfo['memberID'], $dataAuthLimit);
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取报表概览配置开关
     * <AUTHOR>
     * @date   2023/3/31
     *
     */
    public function getHomeOverviewConfig()
    {
        $sid = $this->_loginInfo['sid'];

        $homeOrderBiz = new \Business\Statistics\HomeOrder();
        $result       = $homeOrderBiz->getHomeOverviewConfig($sid);

        $this->apiReturn(200, $result->getConfig());
    }

    /**
     * 设置报表概览配置开关
     * <AUTHOR>
     * @date   2023/3/31
     *
     */
    public function setHomeOverviewConfig()
    {
        $showSelfSonTicket = I('post.show_self_son_ticket', 0, 'intval');
        $sid               = $this->_loginInfo['sid'];

        $homeOrderBiz = new \Business\Statistics\HomeOrder();
        $result       = $homeOrderBiz->setHomeOverviewConfig($sid, $showSelfSonTicket);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}