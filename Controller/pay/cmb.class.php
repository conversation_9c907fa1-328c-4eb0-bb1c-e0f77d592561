<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: chenguangpeng
 * Date: 1/24-024
 * Time: 15:21
 */

namespace Controller\pay;


use Business\Order\Query;
use Library\Business\cmb\sdk;
use Library\Cache\Cache;
use Library\Constants\DingTalkRobots;
use Library\Controller;
use Library\Model;
use Library\Tools\Helpers;
use Model\Member\Member;
use Model\Member\Recharge;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\TradeRecord\OnlineRefund;
use Model\TradeRecord\OnlineTrade;
//ini_set('display_errors', 'On');
define('IN_PFT', true);
class cmb  extends Controller  implements PayInterface
{
    const SOURCE_T = 12;

    const RECHARGE_NOTIFY_URL = PAY_DOMAIN . 'r/pay_cmb/rechargeNotify';
    const ORDER_NOTIFY_URL    = PAY_DOMAIN . 'r/pay_cmb/orderNotify';
    const RENEW_NOTIFY_URL    = PAY_DOMAIN . 'r/pay_cmb/renewNotify/'; //平台会员充值通知地址
    const PARK_NOTIFY_URL     = PAY_DOMAIN . 'r/pay_cmb/parkPayNotify/'; //停车场支付通知地址
    const ANNUAL_RENEW_NOTIFY_URL = PAY_DOMAIN . 'r/pay_cmb/annualRenewNotify/';//年卡续费通知地址

    private $orderModel = null;
    private $sdk        = null;
    public function __construct()
    {
        $this->sdk = new sdk();
    }

    public function cardSolutionRecharge()
    {
        // TODO: Implement cardSolutionRecharge() method.
    }

    public function renew()
    {
        pft_log('debug/cmb', json_encode($_POST));
        echo '{"code":200}';
    }
    /**
     * 年卡微信续费
     * <AUTHOR>
     */
    public function annualRenew()
    {
        $this->apiReturn(204, [], '接口已关闭');
    }

    public function recharge()
    {
        $money     = I('post.money');
        $money     = floatval(number_format($money, 2, '.', ''));
        $total_fee = $money * 100;
        $openid    = I('post.openid');
        $aid       = I('post.aid');
        $did       = I('post.did', 0);
        $is_qr     = I('post.qr_pay', 0);
        $pay_type  = I('post.pay_type', 0, 'intval');
        $memo      = I('post.memo', '', 'trim');

        $loginSessionInfo = (new \Business\Member\Session())->getSessionLoginInfo();
        if(!$did) {
            $did = $loginSessionInfo['sid'] ?? 0;
        }

        if (!$did) {
            exit('{"status":"fail","msg":"用户身份获取错误"}');
        }
        if ($is_qr == 0 && empty($openid)) {
            exit('{"status":"fail","msg":"OPENID为空"}');
        }
        if (!is_numeric($money) || $money < 0) {
            exit('{"status":"fail","msg":"请输入大于0的金额，金额必须是数字"}');
        }
        $modelMember = new Member();
        if ($did == 1) {
            $body = '补打款';
        } else {

            $queryParams = [[$did, $aid]];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                $queryParams);

            if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                exit('{"status":"fail","msg":"用户信息获取失败，请稍后重试"}');
            }

            $memberInfo = array_column($queryRes['data'], null, 'id');

            //$seller_nama = $modelMember->getMemberCacheById($did, 'dname');
            $seller_nama = $memberInfo[$did]['dname'];
            if ($aid > 0) {
                //$boss_name = $modelMember->getMemberCacheById($aid, 'dname');
                $boss_name = $memberInfo[$aid]['dname'];
                $body      = "[$seller_nama]给{$boss_name}充值{$money}元|{$did}|$aid";
            } else {
                $body = "[{$seller_nama}]账户充值{$money}元|{$did}";
            }
        }
        if ($memo) {
            $body .= '|' . $memo;
        }
        $out_trade_no             = time() . $did . mt_rand(1000, 9999);
        $log_data                 = $_POST;
        $log_data['out_trade_no'] = $out_trade_no;
        $log_data['body']         = $body;
        unset($log_data);
        $pay_type = $pay_type == 1 ? sdk::PAY_WAY_ALIPAY_QR : sdk::PAY_WAY_WXPAY_QR;

        //收单系统出现错误:body参数长度有误，所以对body参数做处理
        $payBody = $body;
        if (mb_strlen($body) > 20) {
            $payBody = mb_substr($body, 0, 20) . '...';
        }

        if ($is_qr) {
            $parameters = $this->sdk->qrPay($total_fee, $body, $out_trade_no, self::RECHARGE_NOTIFY_URL, '微信充值', $pay_type);
            if ($parameters['code'] != 200) {
                //添加日志
                pft_log('recharge_error', json_encode([$out_trade_no, $total_fee, $payBody, $pay_type, $parameters]));
                $msg = "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['return_msg']}";
                if (!$is_qr) {
                    $msg .= ",您可以尝试使用【微信二维码支付】功能来完成充值";
                }

                parent::apiReturn(401, [], $msg);
            }
            $data = ['outTradeNo' => $out_trade_no, 'qrUrl' => $parameters['data']['code_url']];
        } else {
            $parameters = $this->sdk->jsApiPay($total_fee, $body, $out_trade_no, $openid, self::RECHARGE_NOTIFY_URL);
            if ($parameters['code']!=200) {
                parent::apiReturn(401, [], "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['errcode']}:{$parameters['msg']}");
            }
            $data = [
                'appId'      => $parameters['data']['appId'],
                'timeStamp'  => $parameters['data']['timeStamp'] . '',
                'nonceStr'   => $parameters['data']['nonceStr'],
                'signType'   => $parameters['data']['signType'],
                'package'    => $parameters['data']['package'],
                'paySign'    => $parameters['data']['paySign'],
            ];
            $data = ['parameter' => $data, 'outTradeNo' => $out_trade_no];
            //$parameters = $this->sdk->pageJumpPay($total_fee, $body, $out_trade_no, 'http://www.12301.cc', self::RECHARGE_NOTIFY_URL);
            //$data = ['jump_url' => $parameters['data']['jsapi_pay_url'], 'outTradeNo' => $out_trade_no];
            pft_log('cmb/data', "response#id=recharge,data=". json_encode($data, JSON_UNESCAPED_UNICODE));

        }
        $model = new OnlineTrade();
        $ret   = $model->addLog($out_trade_no, $money, $body, $body, self::SOURCE_T, OnlineTrade::PAY_METHOD_RECHARGE,
            $parameters['data']['pmt_tag'].'_'.$pay_type, 0, $parameters['data']['ord_no']);
        if (!$ret) {
            parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
        }

        parent::apiReturn(200, $data);
    }
    public function order()
    {
        //平台订单号
        $outTradeNo = I('post.out_trade_no', 0, 'string');
        $qrPay      = I('post.is_qr', 0, 'intval'); //是否二维码支付
        //微信用户openid
        $openid   = I('post.openid', null);
        $remark   = I('post.remark', '微信订单支付');
        $pay_type = I('post.pay_type', 0);
        //订单主体说明
        $subject = mb_substr(trim(I('post.subject')), 0, 20, 'utf-8');
        //只允许 汉字 + 数字 + .
        $subject = preg_replace("/[^\x{4e00}-\x{9fa5}|1-9|\.]/iu", '', $subject);
        $subject .= "(订单号:{$outTradeNo})";
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }

        if (!$qrPay && !$openid) {
            parent::apiReturn(401, [], '请用微信APP打开进行支付');
        }

        //获取订单总金额
        $OrderQeury = new OrderQuery('localhost');
        $totalFee   = $OrderQeury->get_order_total_fee((string) $outTradeNo);
        if ($qrPay) {
            //生成微信支付二维码
            $pay_type = $pay_type == 1 ? sdk::PAY_WAY_ALIPAY_QR : sdk::PAY_WAY_WXPAY_QR;
            //当支付宝与微信同时请求的时候会有错误，所以订单号加上一个支付类型的前缀做标识，在异步通知的时候过滤
            $prefix_outTradeNo = $pay_type . $outTradeNo;
            $parameters  = $this->sdk->qrPay($totalFee, $subject, $prefix_outTradeNo, self::ORDER_NOTIFY_URL, $remark, $pay_type);
            if ($parameters['code']!=200) {
                $msg = "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['msg']}";
                parent::apiReturn(401, [], $msg);
            }
            $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => $parameters['data']['code_url']];
        } else {
            $pay_type    = sdk::PAY_WAY_WXPAY_JS;
            $parameters  = $this->sdk->jsApiPay($totalFee, $subject, $outTradeNo, $openid, self::ORDER_NOTIFY_URL);
            if ($parameters['code']!=200) {
                parent::apiReturn(401, [], "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['errcode']}:{$parameters['msg']}");
            }
            $data = [
                'appId'      => $parameters['data']['appId'],
                'timeStamp'  => $parameters['data']['timeStamp'] . '',
                'nonceStr'   => $parameters['data']['nonceStr'],
                'signType'   => $parameters['data']['signType'],
                'package'    => $parameters['data']['package'],
                'paySign'    => $parameters['data']['paySign'],
            ];
        }
        $tradeModel = new OnlineTrade();
        $ret        = $tradeModel->addLog($outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_ORDER, $parameters['data']['pmt_tag'].'_'.$pay_type, 0, $parameters['data']['ord_no'] );
        if (!$ret) {
            parent::apiReturn(401, [], '支付记录生成失败');
        }
        pft_log('cmb/data', "response#id=order,data=". json_encode($data, JSON_UNESCAPED_UNICODE));
        parent::apiReturn(200, $data);
    }
    public function cardPay()
    {
        $outTradeNo  = I('post.ordernum');
        $auth_code   = I('post.auth_code');
        $total_fee   = I('post.money') * 100;
        $subject     = I('post.subject', '订单支付');
        $pay_scen    = I('post.pay_scen', 1);
        $pay_type    = I('post.pay_type');
        if (!$outTradeNo || !$auth_code) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }
        pft_log('/micropay/cmb', json_encode($_POST));
        //switch ($pay_scen) {
        //    case 1:
        //        //订单支付
        //        if (is_numeric($outTradeNo)) {
        //            $orderQuery = new OrderQuery();
        //            $total_fee  = $orderQuery->get_order_total_fee($outTradeNo);
        //        }
        //        break;
        //    case 2:
        //        break;
        //}
        if ($pay_type==1) $pay_type = sdk::PAY_WAY_ALIPAY_MICOPAY;
        elseif ($pay_type == 2 || empty($pay_type)) $pay_type = sdk::PAY_WAY_WXPAY_MICROPAY;
        $OnlineTrade = new OnlineTrade();
        $pay_result = $this->sdk->micropay($total_fee, $subject, $outTradeNo, self::ORDER_NOTIFY_URL, $auth_code, $pay_type);
        if ($pay_result == false) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "支付失败,订单号:{$outTradeNo},请求微信支付失败", true);
        }
        $transaction_id = $pay_result['trade_no'];
        $json_buy       = '';
        $json_sell      = '';
        $res            = $OnlineTrade->updateLog($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_WEPAY, $json_sell, $json_buy);
        $output = ['ordernum' => $outTradeNo, 'trade_no' => $transaction_id];
        pft_log('micropay/wepay_result', $outTradeNo. ':支付结果响应:'.json_encode($pay_result) . ';修改支付状态返回:'. $res);
        parent::apiReturn(200, $output, "微信支付成功", true);
    }
    public function micropay()
    {
        $outTradeNo  = I('post.ordernum');
        $auth_code   = I('post.auth_code');
        $total_fee   = I('post.money') * 100;
        $is_member   = I('post.is_member', 0);
        $subject     = I('post.subject', '订单支付');
        $pay_scen    = I('post.pay_scen', 1);
        $payTerminal = I('post.terminal', 0, 'intval'); //支付的终端
        $pay_type    = I('post.pay_type', 1);
        $checkSource     = I('post.check_source', -1, 'intval');//验证来源

        if (!$outTradeNo || !$auth_code) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }
        pft_log('/micropay/cmb', json_encode($_POST));
        switch ($pay_scen) {
            case 1:
                //订单支付
                if (is_numeric($outTradeNo)) {
                    $orderQuery = new OrderQuery();
                    $total_fee  = $orderQuery->get_order_total_fee($outTradeNo);
                }
                break;
            case 2:
                break;
        }
        $OnlineTrade = new OnlineTrade();
        if ($pay_type==1) $pay_type = sdk::PAY_WAY_ALIPAY_MICOPAY;
        elseif ($pay_type == 2 || empty($pay_type)) $pay_type = sdk::PAY_WAY_WXPAY_MICROPAY;
        $pay_result = $this->sdk->micropay($total_fee, $subject, $outTradeNo, self::ORDER_NOTIFY_URL, $auth_code, $pay_type);
        if ($pay_result['code'] != 200) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "支付失败,订单号:{$outTradeNo},请求微信支付失败", true);
        }
        $result      = $OnlineTrade->addLog($outTradeNo, $total_fee / 100, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_ORDER, $pay_result['data']['pmt_tag'].'_'.$pay_type, 0, $pay_result['data']['ord_no']);
        if (!$result) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }
        $transaction_id = $pay_result['data']['ord_no'];
        $json_buy       = '';
        $json_sell      = '';
        $pay_to_pft     = true;
        //是否支付后立即验证标识
        $needVerify = I('post.verify', 0);
        //支付时所使用的终端号
        $terminal = I('post.terminal', 0, 'strval');
        $sourceT = self::SOURCE_T;
        if ($pay_result['data']['pmt_tag'] == sdk::WEPAY) {
            $sourceT = 12;
            //微信
        } elseif ($pay_result['data']['pmt_tag'] == sdk::ALIPAY) {
            //支付宝
            $sourceT = 13;
        }
        $paySource = $this->sdk->getTrackSourceMap($pay_type);
        try {
            $res      =  self::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, $sourceT,
                $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $paySource, '', $payTerminal);
        } catch (\SoapFault $e) {
            $retry = true;
            pft_log('order_pay/error', "(招商聚合)订单支付失败:{$outTradeNo}，soap抛出异常:{$e->getCode()}->{$e->getMessage()}");
            Helpers::sendDingTalkGroupRobotMessage("(招商聚合)[{$outTradeNo}]订单支付失败;soap抛出异常:{$e->getCode()}->{$e->getMessage()}", "支付失败",  Helpers::getServerIp(), DingTalkRobots::MYSQL_ERROR);
        }
        if (isset($retry) && $retry === true) {
            $res      =  self::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, $sourceT,
                $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $paySource, '', $payTerminal);
        }
        $output = [];
        if ($res === 100) {
            $orderInfo = $this->_getOrder($outTradeNo);
            $args = [
                'ordernum' => $outTradeNo,
                'buyerId'  => $orderInfo['member'], //购买人的ID
                'mobile'   => $orderInfo['ordertel'],
                'aid'      => $orderInfo['aid'],
            ];
            $jobId = \Library\Resque\Queue::push('notify', 'OrderNotify_Job', $args);
            if ($needVerify) {
                if (empty($this->orderInfo)) {
                }
                $query   = new Query();
                $paymode = 5;
                $output  = $query->getOrderInfoForPrintByRpc($outTradeNo, $this->orderInfo, $total_fee, $paymode, $terminal,$checkSource,0);
            }
            $msg    = '支付成功';
            $code   = parent::CODE_SUCCESS;
        } else {
            $msg    = "微信支付成功但订单状态更新失败,订单号:{$outTradeNo}";
            $code   = parent::CODE_SUCCESS;
        }
        pft_log('micropay/cmb_result', $code . ';'.$outTradeNo. ':支付结果响应:'.json_encode($pay_result) . ';output:' . json_encode($output). ';修改支付状态返回:'. $res);
        parent::apiReturn($code, $output, $msg, true);
    }
    public function Query()
    {
        $ordernum     = I('post.ordernum');
        $tradeNo = I('post.tradeno', '');
        $res          = $this->sdk->query($ordernum, $tradeNo);
        parent::apiReturn(parent::CODE_SUCCESS, $res);
    }

    /**
     * 接口废弃
     * <AUTHOR>
     * @date 2021/2/7
     *
     * @return array
     */
    public function Refund()
    {
        parent::apiReturn(parent::CODE_AUTH_ERROR);
    }

    private function _notifyData()
    {
        unset($_GET['a']);
        unset($_GET['c']);
        if ($_GET['status'] == 4) {
            pft_log('cmb/notify_cancel', json_encode($_GET));
            exit('notify_success');
        }
        pft_log('cmb/notify_success', json_encode($_GET));
        //$json = '{"ord_no":"9151687350670676226354857","out_no":"16384731","rand_str":"rkmUTiObT9EGNUh8dLgAD28VUswySfMkA8etr3Elci10dj8q4o0Hr9DmB9VupHOZQ3th77CkqEkDXt31S4JjdnGPwBjWj8Wacprkx4EXJZAGtEIlIsFWPmLmY5jhdfrq","status":"1","timestamp":"1516873515","sign":"1162cdd4a9d0a806a46b2c61f1124d57"}';
        //$_GET = json_decode($json, true);
        if ($this->sdk->check_sign($_GET) && $_GET['status']==1) {
            $model = new OnlineTrade('localhost');
            $orderNo = self::filter_ordernum($_GET['out_no']);
            $data = $model->getLog($orderNo, self::SOURCE_T, $_GET['ord_no']);
            $data['total_fee'] *= 100;//将元转换为分
            $data['trade_no']  = $_GET['ord_no'];//使用银行的订单号作为交易号
            return $data;
        }
        pft_log('cmb/fail',  "签名失败;" . json_encode($_GET));
        exit("签名失败");
    }

    public function rechargeNotify()
    {
        $data = $this->_notifyData();
        $pay_total_fee = $data['total_fee'];
        $trade_no      = $data['trade_no']; //微信/支付宝订单号
        $res           = false;
        if ($data['status']==0) {

            //旧逻辑，已经弃用
            // $modelRecharge = new Recharge();
            // $res = $modelRecharge->OnlineRecharge($data['out_trade_no'], self::SOURCE_T, $trade_no, $pay_total_fee, '','',PFT_WECHAT_APPID);

            $res = true;
            if ($res == true) {
                echo "notify_success";
            }
        }
        return $res;
    }
    public function orderNotify()
    {
        $data = $this->_notifyData();
        $res      = $this->orderHandler($data);
        if ($res == 100) {
            $this->orderModel = new OrderTools('localhost');
            $order_info       = $this->orderModel->getOrderInfo($data['out_trade_no'], 'member,ordertel,aid');
            if ($order_info['ordertel'] != '' && $order_info['ordertel'] != '00000000000') {
                //重试三次
                $resendTimes = 3;
                while ($resendTimes--) {
                    $jobId = $this->_orderNotidyJob($data['out_trade_no'], $order_info);
                    if ($jobId) {
                        break;
                    }
                }
            }
            pft_log('cmb/notify', "订单民生通知处理:ordernum={$data['out_trade_no']};job_id=$jobId");
            echo 'notify_success';
        } else {
            echo 'FAIL';
        }
    }
    public function payResultCheck()
    {
        $ordernum = I('post.ordernum');
        //支付场景：1订单支付,2充值
        $pay_scen = I('post.pay_scen', 2);
        $pay_type = 0;
        if (isset($_POST['pay_type'])) {
            $pay_type = intval($_POST['pay_type']);
        }
        //todo::检测支付状态
        $model     = new OnlineTrade();
        $_ordernum = self::filter_ordernum($ordernum);
        $pay_log   = $model->getLog($_ordernum, self::SOURCE_T, '', 1);
        $pay_log['total_fee'] *= 100;
        if (!$pay_log) {
            parent::apiReturn(parent::CODE_CREATED, [], '支付记录不存在', true);
        } else {
            parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
        }

        pft_log('wepay_cmbc/notify_delay', "民生通知延迟处理:pay_scen=$pay_scen&ordernum=$ordernum&status={$pay_log['status']}");
        //若系统支付状态还未更新，请求银行数据
        if ($pay_scen == 1) {
            $res = $this->orderResultCheck($ordernum, $pay_log);
            if ($res == 1) {
                parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
            }

            //if ($res == 1) parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败', true);
        } elseif ($pay_scen == 2) {
            $this->rechargeResultCheck($ordernum, $pay_log);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], 'Unknow Request', true);
    }

    /**
     * 年卡续费通知
     * <AUTHOR>
     */
    public function annualRenewNotify()
    {
        $data = $this->_notifyData();
        //这边加个锁
        $Redis = Cache::getInstance('redis');
        $key   = 'renew' . $data['out_trade_no'];
        $lock  = $Redis->lock($key, 1, 30);
        if (!$lock) {
            exit('FAIL');
        }
        $outTradeNo = $data['out_trade_no'];
        $money      = $data['total_fee'];
        $tradeNo    = $data['trade_no'];
        $vipBiz = new \Business\Mall\MemberVip(1, 1);
        $result = $vipBiz->wechatRenewNotify($outTradeNo, $money, $tradeNo);
        @pft_log('cmb/annualrenew', json_encode($result), 'day');
        echo 'notify_success';
    }
    /**
     * 充值完成检测
     *
     * @param $ordernum
     * @param array $pay_log
     */
    private function rechargeResultCheck($ordernum, array $pay_log)
    {
        $openid      = I('post.openid');
        $fid         = I('post.did');
        $aid         = I('post.aid');
        $modelMember = new Member('localhost');
        if ($pay_log['status'] !== 1) {
            //$res = $this->_queryBankPayInfo($ordernum);
            $res = $this->sdk->query($ordernum, '');

            if ($res['data']['status'] != 1) {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, ['payStatus' => 0], '银行您还未接收到您的支付请求，请确认您是否已经支付成功');
            }
            $ret = $this->rechargeHandler($pay_log);
            if ($ret === true) {
                $moneyArr    = $modelMember->getMoney($fid, 3, $aid, true); //查询会员账户可用授信额度
                $creditMoney = ($moneyArr['basecredit'] + $moneyArr['kmoney']) / 100;
                pft_log('wepay_cmbc/notify_delay', "民生通知延迟处理:fid=$fid&ordernum=$ordernum");
                parent::apiReturn(parent::CODE_SUCCESS, ['money' => $creditMoney, 'payStatus' => 1], '支付成功', true);
            }
            parent::apiReturn(parent::CODE_INVALID_REQUEST, ['payStatus' => 0], '系统发生异常', true);
        }
        $moneyArr    = $modelMember->getMoney($fid, 3, $aid, true); //查询会员账户可用授信额度
        $creditMoney = ($moneyArr['basecredit'] + $moneyArr['kmoney']) / 100;
        parent::apiReturn(parent::CODE_SUCCESS, ['money' => $creditMoney, 'payStatus' => 1], '支付成功', true);
    }

    private function rechargeHandler($data)
    {
        return false;
    }
    /**
     * 订单支付完成检测
     *
     * @param string $ordernum
     * @param array $pay_log
     * @param bool $is_micropay
     * @param bool $terminal 购票终端号
     * @return int
     */
    private function orderResultCheck($ordernum, array $pay_log, $is_micropay = false, $terminal = 0)
    {
        if ($pay_log['status'] !== 1) {
            [$payChannel, $payWay] = explode('_', $pay_log['royalty_parameters']);
            $queryOrdernum = $payWay . $ordernum;
            $res           = $this->sdk->query($queryOrdernum, '');
            if ($res['data']['status'] == 1) {
                $ret = $this->orderHandler($pay_log, $terminal);
            }
            if ($ret === 100) {
                $orderInfo = $this->_getOrder($ordernum);
                $this->_orderNotidyJob($ordernum, $orderInfo);
                $job_id = \Library\Resque\Queue::push('notify', 'OrderNotify_Job',
                    [
                        'ordernum' => $ordernum,
                        'buyerId'  => $orderInfo['member'], //购买人的ID
                        'mobile'   => $orderInfo['ordertel'],
                        'aid'      => $orderInfo['aid'],
                    ]
                );
                if ($orderInfo['aids']) {
                    // 转分销获取第二级分销商
                    $tmp                 = explode(',', $orderInfo['aids']);
                    $orderInfo['member'] = $tmp[1];
                }
                //pft_log('debug', "orderinfo:" . json_encode($this->orderInfo ,JSON_UNESCAPED_UNICODE));
                pft_log('wepay_cmbc/notify_delay', "订单民生通知延迟处理:ordernum=$ordernum;job_id=$job_id");
                return 1;
            }
            return 2;
        }
        return 1;
    }
    /**
     * 插入订单通知队列
     * <AUTHOR>
     * @date   2017-07-31
     * @param  string     $ordernum   订单号
     * @param  array     $orderInfo 订单信息
     * @return string
     */
    private function _orderNotidyJob($ordernum, $orderInfo)
    {

        $args = [
            'ordernum' => $ordernum,
            'buyerId'  => $orderInfo['member'], //购买人的ID
            'mobile'   => $orderInfo['ordertel'],
            'aid'      => $orderInfo['aid'],
        ];

        $jobId = \Library\Resque\Queue::push('notify', 'OrderNotify_Job', $args);

        if (!$jobId) {
            pft_log('cmb/notify_fail', $ordernum);
        }

        return $jobId;
    }
    /**
     * 订单支付成功处理
     *
     * @param array $result
     * @param int $terminal 购票终端号
     * @return mixed
     */
    private function orderHandler(array $result, $terminal = 0)
    {
        if ($result['status'] == 1) {
            return 201;
        }
        $pay_to_pft    = true;
        $pay_total_fee = $result['total_fee'];
        $trade_no      = $result['trade_no']; //微信/支付宝订单号
        $daction       = null;
        $ordernum      = $result['out_trade_no'];
        [$payChannel, $payWay] = explode('_', $result['royalty_parameters']);
        $sourceT = self::SOURCE_T;
        if ($payChannel == sdk::WEPAY) {
            $sourceT = 12;
            //微信
        } elseif ($payChannel == sdk::ALIPAY) {
            //支付宝
            $sourceT = 13;
        }
        $paySource = $this->sdk->getTrackSourceMap($payWay);
        $res = self::getServerInside()->Change_Order_Pay($ordernum, $trade_no, $sourceT, $pay_total_fee, $daction,
            '', '', 1, $pay_to_pft, $paySource, '', $terminal);
        pft_log('cmb/notify', "$ordernum, $res");
        return $res;
    }
    private function _getOrder($ordernum)
    {
        $this->orderModel = new OrderTools('localhost');
        $orderInfo        = $this->orderModel->getOrderInfo($ordernum, 'member,ordername,ordertel,aid,code,lid,tid,tnum,tprice,paymode,salerid', 'de.aids,de.series');
        if ($orderInfo['aids']) {
            // 转分销获取第二级分销商
            $tmp                 = explode(',', $orderInfo['aids']);
            $orderInfo['member'] = $tmp[1];
        }
        $this->orderInfo = $orderInfo;
        return $orderInfo;
    }
    private function filter_ordernum($ordernum)
    {
        $search = ['qr_', sdk::PAY_WAY_ALIPAY_QR, sdk::PAY_WAY_WXPAY_QR, sdk::PAY_WAY_WXPAY_MICROPAY, sdk::PAY_WAY_ALIPAY_MICOPAY];
        return str_replace($search, '', $ordernum);
    }
}