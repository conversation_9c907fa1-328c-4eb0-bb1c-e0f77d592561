<?php
/**
 * 支付宝支付
 * User: chenguangpeng
 * Date: 5/16-016
 * Time: 18:41
 */

namespace Controller\pay;

use Business\Finance\AccountMoney;
use Business\JsonRpcApi\PayService\PayConfig;
use Business\Order\Query;
use Business\Pay\PayBase;
use Business\Pay\PayCache;
use Business\Pay\PayCenter;
use Business\Product\UnifyCard;
use Business\RiskManagement\ShumeiRiskCheck;
use Library\Business\Alipay\AlipayException;
use Library\Business\Alipay\F2FPay;
use Library\Business\Alipay\H5Pay;
use Library\Business\Alipay\V0\AlipaySubmit;
use Library\Cache\Cache;
use Library\Constants\DingTalkRobots;
use Library\Controller;
use Library\Exception;
use Library\RateLimit\RateLimit;
use Library\Resque\Queue;
use Library\Tools\Helpers;
use Model\Finance\PayMerchant;
use Model\Member\Member;
use Model\Member\Recharge;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\TradeRecord\OnlineTrade;
use Process\Order\Validate\MergeOrderPreCheck\OrderPreCheckException;

include "/var/www/html/alipay/lib/alipay_submit_mobile.class.php";

class Alipay extends Controller implements PayInterface
{
    /**
     * 定义支付渠道
     */
    const SOURCE_T = 0;
    //const RECHARGE_NOTIFY_URL = PAY_DOMAIN . 'recharge/alipay.php';
    const RECHARGE_RETURN_URL = PAY_DOMAIN . 'recharge/alipay_return.php';
    const ORDER_NOTIFY_URL    = PAY_DOMAIN . '/r/pay_Alipay/orderNotify/';
    const RECHARGE_NOTIFY_URL = PAY_DOMAIN . '/r/pay_Alipay/rechargeNotify/';
    const XGS_NOTIFY_URL      = PAY_DOMAIN . '/r/pay_Alipay/xgsNotify/';
    const COMMON_NOTIFY_URL   = PAY_DOMAIN . '/r/pay_Alipay/commonNotify/';//通用支付回调处理，转发数据到具体的业务逻辑上
    private $orderModel = null;
    /**
     * @var null|string 第三方授权码
     */
    private $appAuthToken = null;
    private $merchantId   = 0;
    private $parternerId  = null;
    private $isvAppId     = null;
    private $config       = null;
    /**
     * Alipay constructor.
     *
     * @param  array  $config  第三方服务商授权配置
     * @param  int  $merchantId  第三方服务端的平台供应商ID
     */
    public function __construct($config = [], $merchantId = 0)
    {
        if (!empty($config)) {
            $this->config = $config;
            $this->appAuthToken = $config['app_auth_token'];
            if (empty($config['master_pid'])) {
                $this->parternerId = PFT_ALIPAY_PARTNER_ID;
            } else {
                $this->parternerId = $config['master_pid'];
            }
            $this->merchantId = $config['memberid'];
            $this->isvAppId   = $config['appid']??PFT_ALIPAY_F2FPAY_ID;
        } elseif (isset($_POST['appid'])) {
            $this->isvAppId   = I('post.appid');
        }
    }

    /**
     * PC端订单支付
     * <AUTHOR>
     * @date   2018-06-05
     *
     * @return
     */
    public function pc_order_api()
    {
        define('BUYER', $_POST['buyid']);
        include "/var/www/html/alipay/alipay.config.world.php";
        /**************************请求参数**************************/
        //设置未付款交易的超时时间，一旦超时，该笔交易就会自动被关闭。取值范围： 1m～15d。
        $it_b_pay = isset($_POST['it_b_pay']) ? $_POST['it_b_pay'] : '15d';
        //支付类型
        $payment_type = "1";
        //必填，不能修改
        //服务器异步通知页面路径
        $notify_url = $_POST['notify_url'] ? $_POST['notify_url'] : PAY_DOMAIN . "order/alipay_notify.php";
        //需http://格式的完整路径，不能加?id=123这类自定义参数

        //页面跳转同步通知页面路径
        $return_url = $_POST['return_url'] ? $_POST['return_url'] : "http://www.12301.cc/pay/return_url.php";
        //需http://格式的完整路径，不能加?id=123这类自定义参数，不能写成http://localhost/

        //卖家支付宝帐户
        $seller_email = $alipay_config['seller_email']; //"<EMAIL>";//$_POST['WIDseller_email'];
        //必填

        //商户订单号
        $out_trade_no = $_POST['WIDout_trade_no'];
        //商户网站订单系统中唯一订单号，必填

        //订单名称
        $subject = $_POST['WIDsubject'];
        //必填

        //付款金额
        $total_fee = $_POST['WIDtotal_fee'];
        //必填

        //订单描述

        $body = $_POST['WIDbody'];
        //商品展示地址
        $show_url = $_POST['WIDshow_url'];
        //需以http://开头的完整路径，例如：http://www.xxx.com/myorder.html

        //防钓鱼时间戳
        $anti_phishing_key = "";
        //若要使用请调用类文件submit中的query_timestamp函数

        //客户端的IP地址
        $exter_invoke_ip = "";
        //非局域网的外网IP地址，如：*********

        //分利润
        $royalty_type       = 10;
        $account1           = $_POST['WIDaccount1'];
        $royalty_parameters = "";
        if ($account1) {
            $pay1               = $_POST['WIDpay1'];
            $pay1txt            = $_POST['WIDpay1txt'];
            $pay1               = number_format((int)$pay1 / 100, 2, '.', '');
            $royalty_parameters .= $account1 . "^" . $pay1 . "^" . $pay1txt;
        }
        if ($account2 = $_POST['WIDaccount2']) {
            $pay2               = $_POST['WIDpay2'];
            $pay2               = number_format((int)$pay2 / 100, 2, '.', '');
            $pay2txt            = $_POST['WIDpay2txt'];
            $royalty_parameters .= "|" . $account2 . "^" . $pay2 . "^" . $pay2txt;
        }
        if (!$royalty_parameters) {
            $royalty_type = 0;
        }

        $buyer_email   = $_POST['WIDbuyer'];
        $default_login = "Y";
        $trade         = new \Model\TradeRecord\OnlineTrade();
        $res           = $trade->addLog($out_trade_no, $total_fee, $body, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_ORDER);
        if (!$res) {
            exit('支付数据有误，请核对支付金额');
        }

        //如果是内网测试环境，直接把支付状态设置为已经支付
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            $trade_no     = 'online_' . time();
            $daction      = null;
            $seller_email = '<EMAIL>';
            $buyer_email  = '<EMAIL>';
            $pay_to_pft   = $this->merchantId ? false : true;
            $pay_channel  = 7; //支付渠道
            $sourceT      = 0; //定义支付来源为支付宝啊

            $options = [
                'buyer_info'  => $buyer_email,
                'sell_info'   => $seller_email,
                'pay_channel' => $pay_channel,
            ];

            $res = \Library\Tools\Helpers::payComplete($out_trade_no, $trade_no, $sourceT, $total_fee * 100,
                (int)$pay_to_pft, $options);
            if ($res['code'] != 200) {
                exit('订单模拟支付失败:' . $res['msg']);
            }

            //跳转到支付成功的页面
            $jumpUrl = $return_url . "?out_trade_no={$out_trade_no}";
            header("Location:{$jumpUrl}");
        } else {
            //构造要请求的参数数组，无需改动
            $parameter = [
                "service"            => "create_direct_pay_by_user",
                "partner"            => trim($alipay_config['partner']),
                "payment_type"       => $payment_type,
                "notify_url"         => $notify_url,
                "return_url"         => $return_url,
                "seller_email"       => $seller_email,
                "out_trade_no"       => $out_trade_no,
                "subject"            => $subject,
                "total_fee"          => $total_fee,
                "body"               => $body,
                "show_url"           => $show_url,
                "anti_phishing_key"  => $anti_phishing_key,
                "exter_invoke_ip"    => $exter_invoke_ip,
                "_input_charset"     => trim(strtolower($alipay_config['input_charset'])),
                "royalty_parameters" => $royalty_parameters,
                "royalty_type"       => $royalty_type,
                "buyer_email"        => $buyer_email,
                "it_b_pay"           => $it_b_pay,
                "default_login"      => $default_login,
            ];
            //print_r($alipay_config);exit;
            //建立请求
            $alipaySubmit = new AlipaySubmit($alipay_config);
            $html_text    = $alipaySubmit->buildRequestForm($parameter, "get", "确认");
            echo $html_text;
        }
    }

    /**
     * 手机端支付宝支付
     * <AUTHOR>
     * @date   2018-05-30
     *
     * @return
     */
    public function mobile_api()
    {
        exit("支付通道升级中");
        define('BUYER', $_REQUEST['buy_id']);
        include "/var/www/html/alipay/alipay.config.world.php";
        $host = $_SERVER['HTTP_HOST'];
        pft_log("debug/alipay", json_encode($_REQUEST, JSON_UNESCAPED_UNICODE));
        if (isset($_REQUEST['domain'])) {
            $domain = "{$_REQUEST['domain']}/wx/html/";
        } else { // if ($host == 'wx.12301.cc')
            $domain = 'http://wx.12301.cc/';
            exit("系统检测到当前交易存在违规行为，为避免给您带来损失，已停止交易");
        }

        $safeCheck = new SafetyCheck();
        $safeResult= $safeCheck->safeCheck(__FUNCTION__, $_REQUEST);
        if ($safeResult['code']!=200) {
            exit($safeResult['msg']);
        }
        /**************************调用授权接口alipay.wap.trade.create.direct获取授权码token**************************/
        //返回格式
        $format = "xml";
        //必填，不需要修改
        //返回格式
        $v = "2.0";
        //必填，不需要修改
        //请求号//必填，须保证每次请求都是唯一
        $req_id = date('Ymdhis');
        //服务器异步通知页面路径
        //$notify_url = "http://wx.12301.cc/pay/alipay_v3.3/notify_url.php";
        //2016年4月21日17:59:51，切换
        $notify_url = PAY_DOMAIN . "order/mobile_alipay_notify.php";
        //需http://格式的完整路径，不允许加?id=123这类自定义参数
        //页面跳转同步通知页面路径
        $call_back_url = "{$domain}html/success.html";
        if ($_REQUEST['success_back_notify'] == 'mall') {
            $call_back_url = "{$_REQUEST['domain']}/wx/mall/ordersuccess.html";
        }

        //新版微商城
        if ($_REQUEST['from'] == 'new') {
            // $call_back_url = "{$_REQUEST['domain']}/wx/c/ordersuccess.html";
            $call_back_url = "{$_REQUEST['domain']}/wx/c/spa.html#/ordersuccess";
        }
        // 微平台
        if ($_REQUEST['success_back_notify'] == 'microPlat') {
            $call_back_url = "{$_REQUEST['domain']}/wx/b/order_pay_success.html";
        }

        //新版全民营销微商城 2019.05.22
        if ($_REQUEST['from'] == 'h5') {
            $call_back_url = "{$_REQUEST['domain']}h5/ordersuccess/c";
        }

        // 如果前端有传回调页面，就跳转到对应回调页面
        if (!empty($_REQUEST['success_callback_url'])) {
            $call_back_url = $_REQUEST['success_callback_url'];
        }

        //需http://格式的完整路径，不允许加?id=123这类自定义参数
        //操作中断返回地址
        $merchant_url = "{$domain}pay/alipay_v3.3/cancel.php";
        if ($_REQUEST['success_back_notify'] == 'mall') {
            $merchant_url = "{$_REQUEST['domain']}/wx/pay/alipay_v3.3/cancel.php";
        }

        // 微平台
        if ($_REQUEST['success_back_notify'] == 'microPlat') {
            $merchant_url = "{$_REQUEST['domain']}/wx/pay/alipay_v3.3/cancel.php";
        }
        //用户付款中途退出返回商户的地址。需http://格式的完整路径，不允许加?id=123这类自定义参数
        //卖家支付宝帐户
        //$seller_email = $_POST['WIDseller_email'];
        $seller_email = $alipay_config['seller_email'];
        //商户订单号
        $out_trade_no = $_REQUEST['out_trade_no'];
        //商户网站订单系统中唯一订单号，必填

        //订单名称
        $subject = $_REQUEST['subject'];
        $memo    = $_REQUEST['subject'];
        //必填

        //付款金额
        $mergeOrder = new \Business\Order\MergeOrder();
        if ($mergeOrder->isCombineOrder($out_trade_no)) {
            try {
                $mergeOrder->handlerCombinePayLog($out_trade_no, '', self::SOURCE_T, $this->merchantId);
            } catch (OrderPreCheckException $e) {
                exit($e->getMessage());
            }
            $memo      = "合并订单总记录:" . $out_trade_no;
            $total_fee = $mergeOrder->getTradeIdTotleMoney($out_trade_no);
        } else {
            //获取订单总金额
            $modelOrderQeury = new \Model\Order\OrderQuery('localhost');
            $total_fee       = $modelOrderQeury->get_order_total_fee($out_trade_no);
        }

        $total_fee = $total_fee / 100;
        //必填
        $expire_time = $_REQUEST['expire_time'] > 0 ? intval($_REQUEST['expire_time']) : 21600; //交易自动关闭时间（可空，默认值21600（即15天））
        //请求业务参数详细
        $req_data = '<direct_trade_create_req><notify_url>'
                    . $notify_url . '</notify_url><call_back_url>'
                    . $call_back_url . '</call_back_url><seller_account_name>'
                    . $seller_email . '</seller_account_name><out_trade_no>'
                    . $out_trade_no . '</out_trade_no><subject>'
                    . $subject . '</subject><total_fee>'
                    . $total_fee . '</total_fee>'
                    . '<pay_expire>' . $expire_time . '</pay_expire>'
                    . '<merchant_url>' . $merchant_url . '</merchant_url></direct_trade_create_req>';
        //必填
        /************************************************************/

        //构造要请求的参数数组，无需改动
        $para_token = [
            "service"        => "alipay.wap.trade.create.direct",
            "partner"        => trim($alipay_config['partner']),
            "sec_id"         => trim($alipay_config['sign_type']),
            "format"         => $format,
            "v"              => $v,
            "req_id"         => $req_id,
            "req_data"       => $req_data,
            "_input_charset" => trim(strtolower($alipay_config['input_charset'])),
        ];
        //记录数据
        $trade = new \Model\TradeRecord\OnlineTrade();
        $res   = $trade->addLog($out_trade_no, $total_fee, $subject, $memo, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_ORDER);
        if (!$res) {
            exit('支付数据有误，请核对支付金额');
        }

        //如果是内网测试环境，直接把支付状态设置为已经支付
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL', 'TEST'])) {
            //模拟支付
            $trade_no     = 'online_' . time();
            $daction      = null;
            $seller_email = '<EMAIL>';
            $buyer_email  = '<EMAIL>';
            $pay_to_pft   = $this->merchantId ? false : true;
            $pay_channel  = 7; //支付渠道
            $sourceT      = OnlineTrade::CHANNEL_ALIPAY; //定义支付来源为支付宝啊

            $options = [
                'buyer_info'  => $buyer_email,
                'sell_info'   => $seller_email,
                'pay_channel' => $pay_channel,
            ];

            $res = \Library\Tools\Helpers::payComplete($out_trade_no, $trade_no, OnlineTrade::CHANNEL_ALIPAY,
                $total_fee * 100, (int)$pay_to_pft, $options);
            if ($res['code'] != 200) {
                exit($res['msg']);
            }
            //跳转到支付成功的页面
            $split   = strpos($call_back_url, '?') !== false ? '&' : '?';
            $jumpUrl = $call_back_url . $split . "out_trade_no={$out_trade_no}";
            header("Location:{$jumpUrl}");

        } else {
            //建立请求
            $alipaySubmit = new \AlipaySubmitMobile($alipay_config);
            $html_text    = $alipaySubmit->buildRequestHttp($para_token);

            //URLDECODE返回的信息
            $html_text = urldecode($html_text);
            //解析远程模拟提交后返回的信息
            $para_html_text = $alipaySubmit->parseResponse($html_text);
            //获取request_token
            $request_token = $para_html_text['request_token'];

            /**************************根据授权码token调用交易接口alipay.wap.auth.authAndExecute**************************/

            //业务详细
            $req_data = '<auth_and_execute_req><request_token>' . $request_token . '</request_token></auth_and_execute_req>';
            //必填

            //构造要请求的参数数组，无需改动
            $parameter = [
                "service"        => "alipay.wap.auth.authAndExecute",
                "partner"        => trim($alipay_config['partner']),
                "sec_id"         => trim($alipay_config['sign_type']),
                "format"         => $format,
                "v"              => $v,
                "req_id"         => $req_id,
                "req_data"       => $req_data,
                "_input_charset" => trim(strtolower($alipay_config['input_charset'])),
            ];
            //建立请求
            //$alipaySubmit = new \AlipaySubmit($alipay_config);
            $html_text = $alipaySubmit->buildRequestForm($parameter, 'get', '确认');
            echo $html_text;
        }
    }

    /**
     * 支付宝充值
     * <AUTHOR> Chen
     * @date 2016-10-03
     *
     * @description 支付宝充值-post
     * @aid：供应商ID，可以为空；大于0表示授信预存
     * @did：当前充值的用户
     * @money：充值的金额，单位“元”
     */
    public function recharge()
    {
        $alipay_config = [];
        include "/var/www/html/alipay/alipay.config.world.php";

        $money = I('post.money');

        //兼容php7+版本
        $aid       = I('post.aid', '', 'intval');
        $did       = I('post.did', 0, 'intval');
        $is_qr     = I('post.qr_pay', 0);   // 0-跳转模式 1-扫码模式 2-app模式
        $shopId    = I('post.shop_id', '', 'intval');
        $returnUrl = I('post.return_url', '', 'strval');
        $remark    = I('post.remark', '', 'strval');
        $account   = I('post.account', '');
        $captchaCode   = I('post.captchaCode', '');
        $requestFrom   = I('post.requestFrom', '');
        $ip            = I('post.ip', '');

        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $sid       = $loginInfo['sid'] ?? 0;

        $did = $did ? $did : $sid;
        if (!$did) {
            exit('{"status":"fail","msg":"用户身份获取错误"}');
        }

        if (!is_numeric($money) || $money < 0) {
            exit('{"status":"fail","msg":"请输入大于0的金额，金额必须是数字"}');
        }

        $queryParams = [[$did, $shopId, $aid]];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
            $queryParams);

        if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
            exit('{"status":"fail","msg":"用户信息获取失败，请稍后重试"}');
        }

        $memberInfo = array_column($queryRes['data'], null, 'id');

        //$modelMember = new Member();
        //$seller_nama = str_replace('|', '', $modelMember->getMemberCacheById($did, 'dname'));

        $seller_nama = str_replace('|', '', $memberInfo[$did]['dname']);
        if ($aid > 0) {
            //$boss_name = str_replace('|', '', $modelMember->getMemberCacheById($aid, 'dname'));
            $boss_name   = str_replace('|', '', $memberInfo[$aid]['dname']);

            if ($shopId) {
                //$through_name = str_replace('|', '', $modelMember->getMemberCacheById($shopId, 'dname'));
                $through_name   = str_replace('|', '', $memberInfo[$shopId]['dname']);

                $body         = "[$seller_nama]通过{$through_name}给{$boss_name}充值{$money}元|{$did}|$aid|$shopId|$remark";
            } else {
                $body = "[$seller_nama]给{$boss_name}充值{$money}元|{$did}|$aid||$remark";
            }
        } else {
            $body = "[{$seller_nama}]账户充值{$money}元|{$did}|||$remark";
        }

        //支付订单号
        $out_trade_no = time() . $did . mt_rand(1000, 9999);

        if (!empty($_SERVER['HTTP_DEVICEID']) && $requestFrom == 'APP'){
            $eventData = [
                'product'   => '账本充值',
                'orderId'   => $out_trade_no,
                'price'     => floatval($money),
            ];
            $riskCheck = new ShumeiRiskCheck();
            $checkResult = $riskCheck->shumeiCheckWithCaptcha('virtualOrder', 2, $account, $eventData, $captchaCode, $ip);
            if ($checkResult['code'] != 200){
                parent::apiReturn($checkResult['code'], $checkResult['data'], $checkResult['msg']);
            }
        }

        //如果是内网测试环境，直接把支付状态设置为已经支付
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            //模拟支付，内网模拟支付只支持扫码的场景

            //写入充值记录
            $model = new OnlineTrade();
            $ret   = $model->addLog($out_trade_no, $money, $body, $body, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_RECHARGE);
            if (!$ret) {
                exit('{"status":"fail","msg":"记录发生错误，请联系客服人员"}');
            }

            $trade_no        = 'online_' . time();
            $buyerEmail      = '<EMAIL>';
            $accountMoneyBiz = new AccountMoney();
            $realMoney       = $money * 100; //支付宝这边使用的是元
            $res             = $accountMoneyBiz->recharge($out_trade_no, self::SOURCE_T, $trade_no, $realMoney,
                $buyerEmail, '<EMAIL>', '12301.cc');

            if ($is_qr == 1) {
                //现在平台充值使用的是扫码支付
                if ($res == true) {
                    //返回数据
                    $data = ['outTradeNo' => $out_trade_no, 'qrUrl' => ''];
                    parent::apiReturn(200, $data);
                } else {
                    exit('{"status":"fail","msg":"模拟支付失败"}');
                }
            } elseif ($is_qr == 2){  //app支付
                exit('模拟支付失败');
            } else {
                //授信预存的使用传统跳转支付
                if ($res == true) {
                    //跳转到支付成功的页面
                    $jumpParam = [
                        'trade_no'     => $trade_no . "【模拟支付】",
                        'out_trade_no' => $out_trade_no,
                        'total_fee'    => $money,
                        'subject'      => $body,
                        'body'         => $body,
                        'buyer_email'  => $buyerEmail,
                        'trade_status' => 'TRADE_SUCCESS',
                    ];

                    $queryParam = http_build_query($jumpParam);
                    $jumpUrl    = $returnUrl . '?' . $queryParam;
                    $jumpScript = "<script >window.location.href=\"{$jumpUrl}\";</script>";

                    exit($jumpScript);
                } else {
                    exit('模拟支付失败');
                }
            }
        } else {

            if ($shopId) {
                $notifyUrl = self::XGS_NOTIFY_URL;
            } else {
                $notifyUrl = self::RECHARGE_NOTIFY_URL;
            }

            //真实支付
            if ($is_qr == 1) {
                //扫码支付
                $appid = I('post.appid');
                if (!$appid) {
                    $appid = PFT_ALIPAY_F2FPAY_ID;
                }
                try {
                    $f2fpay = new F2FPay($appid);
                } catch (AlipayException $exception) {
                    exit('{"status":"fail","msg":"' . $exception->getMessage() . '"}');
                }

                $response = $f2fpay->qrpay($out_trade_no, $money, $body, self::RECHARGE_NOTIFY_URL, $body, '', null,
                    true);
                if ($response->alipay_trade_precreate_response->code == 10000 && $response->alipay_trade_precreate_response->msg == 'Success') {
                    //写入充值记录
                    $model = new OnlineTrade();
                    $ret   = $model->addLog($out_trade_no, $money, $body, $body, self::SOURCE_T,
                        OnlineTrade::PAY_METHOD_RECHARGE);
                    if (!$ret) {
                        exit('{"status":"fail","msg":"记录发生错误，请联系客服人员"}');
                    }

                    //返回数据
                    $data = [
                        'outTradeNo' => $out_trade_no,
                        'qrUrl'      => $response->alipay_trade_precreate_response->qr_code,
                    ];
                    parent::apiReturn(200, $data);
                } else {
                    exit('{"status":"fail","msg":"充值接口异常，请联系客服人员"}');
                }
            } elseif ($is_qr == 2){  //app 支付
                $model = new OnlineTrade();
                $ret   = $model->addLog($out_trade_no, $money, $body, $body, self::SOURCE_T,
                    OnlineTrade::PAY_METHOD_RECHARGE);
                if (!$ret) {
                    exit('{"status":"fail","msg":"记录发生错误,请联系客服人员"}');
                }
                $appid = I('post.appid');
                if (!$appid) {
                    $appid = PFT_ALIPAY_APP_PAY_ID;
                }
                try {
                    $f2fpay = new F2FPay($appid);
                } catch (AlipayException $exception) {
                    exit('{"status":"fail","msg":"' . $exception->getMessage() . '"}');
                }
                $f2fpay->setSdkExecute(true);
                $biz = ['disable_pay_channels' => 'credit_group'];
                $response = $f2fpay->appPay($out_trade_no, $money, $body, self::RECHARGE_NOTIFY_URL, $body,
                    '',null,null,$biz);
                parent::apiReturn($response['code'], ['alipay_url' => $response['alipay_url']]);
            } else {
                //传统的跳转支付
                $model = new OnlineTrade();
                $ret   = $model->addLog($out_trade_no, $money, $body, $body, self::SOURCE_T,
                    OnlineTrade::PAY_METHOD_RECHARGE);
                if (!$ret) {
                    exit('{"status":"fail","msg":"记录发生错误,请联系客服人员"}');
                }

                //构造要请求的参数数组，无需改动
                $parameter = [
                    "service"              => "create_direct_pay_by_user",
                    "partner"              => trim($alipay_config['partner']),
                    "payment_type"         => 1,
                    "notify_url"           => self::RECHARGE_NOTIFY_URL,
                    "return_url"           => $returnUrl ? $returnUrl : self::RECHARGE_RETURN_URL,
                    "seller_email"         => $alipay_config['seller_email'],
                    "out_trade_no"         => $out_trade_no,
                    "subject"              => $body,
                    "total_fee"            => $money,
                    "body"                 => $body,
                    "show_url"             => '',
                    "anti_phishing_key"    => "",
                    "exter_invoke_ip"      => '',
                    "_input_charset"       => trim(strtolower($alipay_config['input_charset'])),
                    "royalty_parameters"   => '',
                    "royalty_type"         => 0,
                    "buyer_email"          => '',
                    "it_b_pay"             => '15d',
                    "default_login"        => 'Y',
                    'disable_pay_channels' => 'credit_group',
                ];
                //建立请求
                $alipaySubmit = new AlipaySubmit($alipay_config);
                $html_text    = $alipaySubmit->buildRequestForm($parameter, "get", "确认");
                echo $html_text;
            }
        }

        //构造要请求的参数数组，无需改动
        $parameter = [
            "service"              => "create_direct_pay_by_user",
            "partner"              => trim($alipay_config['partner']),
            "payment_type"         => 1,
            "notify_url"           => $notifyUrl,
            "return_url"           => self::RECHARGE_RETURN_URL,
            "seller_email"         => $alipay_config['seller_email'],
            "out_trade_no"         => $out_trade_no,
            "subject"              => $body,
            "total_fee"            => $money,
            "body"                 => $body,
            "show_url"             => '',
            "anti_phishing_key"    => "",
            "exter_invoke_ip"      => '',
            "_input_charset"       => trim(strtolower($alipay_config['input_charset'])),
            "royalty_parameters"   => '',
            "royalty_type"         => 0,
            "buyer_email"          => '',
            "it_b_pay"             => '15d',
            "default_login"        => 'Y',
            'disable_pay_channels' => 'credit_group',
        ];
        //建立请求
        $alipaySubmit = new AlipaySubmit($alipay_config);
        $html_text    = $alipaySubmit->buildRequestForm($parameter, "get", "确认");
        echo $html_text;
    }

    /**
     * 支付宝异步通知  /r/pay_Alipay/rechargeNotify/
     * <AUTHOR> Chen
     * @date   2017-02-13
     */
    public function rechargeNotify()
    {
        if (I('get.dev')) {
            $_POST = json_decode(file_get_contents('php://input'), true);
        }

        //记录日志
        $logData = json_encode([
            'key'    => '支付宝充值',
            'notify' => $_POST,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('alipay_recharge', $logData, 3);

        if (isset($_POST['app_id'])) {
            $appid         = I('post.app_id');
            $f2fpay        = new F2FPay($appid);
            $verify_result = $f2fpay->verify($_POST);
        } else {
            $alipay_config = [];
            //加载配置文件
            include "/var/www/html/alipay/alipay.config.world.php";
            if (!isset($verify_result)) {
                $alipayNotify  = new AlipaySubmit($alipay_config);
                $verify_result = $alipayNotify->verifyNotify();
            }
        }
        //计算得出通知验证结果
        $out_trade_no = $_POST['out_trade_no']; // 商户订单号
        $trade_no     = $_POST['trade_no']; // 支付宝交易号
        $trade_status = $_POST['trade_status']; // 交易状态

        if ($verify_result) {
            //验证成功
            //记录 POST 的数据
            if ($_POST['trade_status'] == 'TRADE_FINISHED') {
                //记录日志
                $logData = json_encode([
                    'key'      => '支付宝充值成功',
                    'ordernum' => $out_trade_no,
                ], JSON_UNESCAPED_UNICODE);
                pft_log('alipay_recharge', $logData, 1);

            } else if ($_POST['trade_status'] == 'TRADE_SUCCESS') {
                //判断该笔订单是否在商户网站中已经做过处理

                $pay_total_fee = intval(strval((float)$_POST['total_fee'] * 100));
                if (isset($_POST['total_amount'])) {
                    $pay_total_fee = intval(strval((float)$_POST['total_amount'] * 100));
                }
                $accountMoneyBiz = new AccountMoney();
                $res             = $accountMoneyBiz->recharge($out_trade_no, self::SOURCE_T, $trade_no, $pay_total_fee,
                    $_POST['buyer_email'], $alipay_config['seller_email'], $_POST['seller_id']);

                if ($res === true) {
                    pft_log('alipay_pc/ok', "$out_trade_no|更新订单状态|返回代码[$res]");

                    //记录日志
                    $logData = json_encode([
                        'key'      => '支付宝充值 - 更新订单状态',
                        'ordernum' => $out_trade_no,
                        'res'      => $res,
                    ], JSON_UNESCAPED_UNICODE);
                    pft_log('alipay_recharge', $logData, 1);

                    exit('success');
                } else {
                    //记录日志
                    $logData = json_encode([
                        'key'      => '支付宝充值 - 更新订单失败',
                        'ordernum' => $out_trade_no,
                        'res'      => $res,
                    ], JSON_UNESCAPED_UNICODE);
                    pft_log('alipay_recharge', $logData, 2);

                    exit('fail');
                }
            }
            echo "success"; //请不要修改或删除
        } else {
            //记录日志
            $logData = json_encode([
                'key'          => '支付宝充值 - 验证失败',
                'ordernum'     => $out_trade_no,
                'trade_no'     => $trade_no,
                'trade_status' => $trade_status,
            ], JSON_UNESCAPED_UNICODE);
            pft_log('alipay_recharge', $logData, 2);

            //验证失败
            echo "fail";
        }
    }

    public function cardPay()
    {
        $appid = I('post.appid');
        if (!$appid) {
            $appid = PFT_ALIPAY_F2FPAY_ID;
        }

        $outTradeNo = I('post.ordernum');
        $auth_code  = I('post.auth_code');
        $total_fee  = I('post.money') * 100;
        $subject    = I('post.subject', '订单支付');
        $pay_scen   = I('post.pay_scen', 1);
        pft_log('/micropay/alipay', json_encode($_POST));

        $f2f      = new F2FPay($this->isvAppId);
        $response = $f2f->micropay($auth_code, $subject, $total_fee / 100, $outTradeNo, [], $this->appAuthToken,
            $this->parternerId);
        //是否支付后立即验证标识
        $onlineTradeModel = new OnlineTrade();
        $output           = [];
        //支付时所使用的终端号
        if ((int)$response->code == 10000) {
            //支付成功
            $transaction_id = (string)$response->trade_no;
            $payToPft       = $this->_checkPayToPft();
            //修改为支付宝账号
            $json_buy = (string)$response->buyer_logon_id;
            $res      = $onlineTradeModel->updateLog($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_ALIPAY,
                $this->isvAppId, $json_buy, 1, '', $payToPft);
            $output   = ['ordernum' => $outTradeNo, 'trade_no' => $transaction_id,'pay_to_pft' => $payToPft];
            $code     = 200;
            $msg      = "支付宝支付成功";
        } elseif ((int)$response->code == 10003) {
            //支付超时
            $msg  = "支付失败,订单号:{$outTradeNo};支付超时";
            $code = parent::CODE_INVALID_REQUEST;
        } else {
            $msg  = "支付失败,订单号:{$outTradeNo};请求支付宝支付失败,失败信息:{$response->sub_msg}";
            $code = parent::CODE_INVALID_REQUEST;
            Helpers::sendDingTalkGroupRobotMessage("(支付宝)[{$outTradeNo}]订单支付失败;支付接口响应信息:" . json_encode($response,
                    JSON_UNESCAPED_UNICODE), "支付失败", Helpers::getServerIp(), DingTalkRobots::MYSQL_ERROR);
        }
        pft_log('micropay/alipay_result', $outTradeNo . ':支付结果响应:' . json_encode($response) . ';修改支付状态返回:' . $res);
        parent::apiReturn($code, $output, $msg, true);
    }

    public $jsonRpcClient = null;
    /**
     * 支付宝付款码支付-post
     */
    public function micropay()
    {
        $outTradeNo = I('post.ordernum');
        $auth_code  = I('post.auth_code');
        $total_fee  = I('post.money', 0);
        $total_fee  = float2int($total_fee) * 100;
        $isMember   = I('post.is_member', 0);
        $subject    = I('post.subject', '订单支付');
        // 1订单支付,2团单支付
        $pay_scen    = I('post.pay_scen', 1);
        $payTerminal = I('post.terminal', 0, 'intval'); //支付的终端
        //支付的操作人 可以不传 用在记录追踪表李
        $payTrackOpId = I('post.pay_track_op_id', 0);
        //扫脸支付部分也是调用当面付alipay.trade.pay接口，
        //与扫码支付不同有两个参数需要调整：收银软件扫脸成功后获取ftoken透传给支付isv服务网关，调用alipay.trade.pay时auth_code取值为ftoken,且scene=security_code。
        $facePayFlag = I('post.facepay', 0, 'intval'); // 支付宝刷脸支付标识
        //支付来源 - 具体可以参照business.conf.php -> track_source
        // 0=黑色终端机 2=自助机 4=云票务 5=云闸机 20=安卓智能终端机
        $paySource = I('post.pay_source', 20, 'intval');
        //业务类型
        $payBiz = I('post.pay_biz',0,'intval'); //0-无 1-计时卡业务
        $checkSource     = I('post.check_source', -1, 'intval');//验证来源
        $needVerify   = I('post.verify', 0); //是否支付后立即验证标识
        $deviceNum    = I('post.deviceNum', 'PFT_12301'); //商户机具终端编号
        $mergePayFlag = false;

        //年卡下单类型  默认值为空字符串, 0年卡下单 1年卡续费
        $annualCardOrderType = I('post.annualCardOrderType', false);

        //记录日志
        $logData = json_encode([
            'key'    => '支付宝扫码支付',
            'notify' => $_POST,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('alipay_micropay', $logData, 3);
        PayBase::setPayDailyRecord(1,'扫码请求',I('post.'),[]);
        $paymentObj = new \Business\Order\MergeOrder();
        if ($paymentObj->isCombineOrder($outTradeNo)) {
            $mergePayFlag = true;
            $paymentObj->handlerCombinePayLog($outTradeNo, $subject, OnlineTrade::CHANNEL_ALIPAY, $this->merchantId,$payBiz);
        }

        //支付授权码，25~30开头的长度为16~24位的数字，实际字符串长度以开发者获取的付款码长度为准
        $codeLength = strlen($auth_code);
        if (!$facePayFlag && ($codeLength < 16 || $codeLength > 24)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '非法的支付条码，请重新刷新支付宝支付码', true);
        }
        switch ($pay_scen) {
            case 1:
                //订单支付
                if (is_numeric($outTradeNo) || $mergePayFlag === true) {
                    $orderQuery = new OrderQuery('localhost');
                    $total_fee  = $orderQuery->get_order_total_fee($outTradeNo);
                }
                break;
            case 2:
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '暂无此支付模式', true);
                break;
        }
        $timeOrderDeposit = 0;
        $payBaseBiz = new PayBase();
        if ($payBiz){
            $payOrderOtherRes = $payBaseBiz->orderPayOtherBizHandle($outTradeNo,$payBiz);
            if ($payOrderOtherRes['code'] == 200){
                $timeOrderDeposit = $payOrderOtherRes['data']['timeOrderDeposit'];
                if ($timeOrderDeposit > 0){
                    $subject .= '/订单押金';
                }
            }
            $total_fee += $timeOrderDeposit;
        }
        $OnlineTrade = new OnlineTrade();
        $result      = $OnlineTrade->addLog($outTradeNo, $total_fee / 100, $subject, $subject,
            self::SOURCE_T, OnlineTrade::PAY_METHOD_ORDER, '', $this->merchantId);
        if (!$result) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }

        //支付宝刷脸支付的商户数据
        //直接客户端上传
        $facepayMerchantId = I('post.merchant_id', 0);

        $f2f      = new F2FPay($this->isvAppId); //self::ORDER_NOTIFY_URL
        $response = $f2f->micropay($auth_code, $subject, $total_fee / 100, $outTradeNo, [], $this->appAuthToken,
            $this->parternerId, $facePayFlag, $deviceNum, $facepayMerchantId);
        //支付时所使用的终端号
        if ($response->code != 10000) {
            if ($response->code == 10003) {
                //支付超时
                $msg  = "支付失败,订单号:{$outTradeNo};支付超时";
                $code = parent::CODE_INVALID_REQUEST;
            } else {
                $msg  = "支付失败,订单号:{$outTradeNo};请求支付宝支付失败,失败信息:{$response->sub_msg}";
                $code = parent::CODE_INVALID_REQUEST;
                Helpers::sendDingTalkGroupRobotMessage("(支付宝)[{$outTradeNo}]订单支付失败;支付接口响应信息:" . json_encode($response,
                        JSON_UNESCAPED_UNICODE), "支付失败", Helpers::getServerIp(), DingTalkRobots::BANK_JOURNAL);
            }
            parent::apiReturn($code, [], $msg, true);
        } else {
            $payToPft   = is_null($this->appAuthToken) ? true : false;
            $payChannel = 8;
            //支付成功
            $transactionId = (string)$response->trade_no;
            //修改为支付宝账号
            $jsonBuy     = (string)$response->buyer_logon_id;
            $payTotalFee = float2int($response->total_amount);
            if ($timeOrderDeposit > 0){
                $payTotalFee -= $timeOrderDeposit;
            }
            $options = [
                'buyer_info'   => $jsonBuy,
                'sell_info'    => json_encode([
                    'app_id'    =>$this->isvAppId,
                    'auth_app_id'=>$this->config['sub_appid']??'']
                ),
                'pay_channel'  => $payChannel,
                'pay_termianl' => $payTerminal,
                'oper'         => $payTrackOpId,
                'pay_source'   => $checkSource,
            ];

            $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_ALIPAY,
                $payTotalFee, (int)$payToPft, $options);

            if (!is_null($this->jsonRpcClient)) {
                $this->jsonRpcClient->call('Order/PaymentLock/unlock', [$outTradeNo], 'scenic');
            }

            if ($result['code'] != 200) {
                $msg  = '付款失败:' . $result['msg'];
                $code = parent::CODE_INVALID_REQUEST;
            } else {
                $code          = parent::CODE_SUCCESS;
                $msg           = '付款成功';
                $output        = [];
                $successOrders = $result['data']['success_orders'];
                foreach ($successOrders as $item) {
                    $output[] = [
                        'code'     => 200,
                        'msg'      => '支付成功',
                        'order_id' => $item,
                    ];
                }
                if ($needVerify) {
                    if ($mergePayFlag === true) {
                        $verifyRes = $paymentObj->handleCombineOrderBuyVerifyByRpc($successOrders, $payTerminal, $payTrackOpId, $checkSource);
                        if ($verifyRes){
                            $verifyData = $verifyRes[0];  //默认就取第0个，因为手持机那边只能读取一个订单
                            $output  = $verifyData;
                        }
                    } else {
                        if (empty($this->orderInfo)) {
                            $this->orderInfo = $this->_getOrder($outTradeNo);
                        }
                        $query   = new Query();
                        $paymode = 1;

                        $output  = $query->getOrderInfoForPrintByRpc($outTradeNo, $this->orderInfo, $total_fee, $paymode, $payTerminal,$checkSource,$payTrackOpId);

                    }
                }
                $payBaseBiz->afterOrderPayAction($payBiz,OnlineTrade::CHANNEL_ALIPAY,$transactionId, (int)$payToPft,$successOrders);
            }

//            pft_log('micropay/alipay_result',
//                $outTradeNo . ':支付结果响应:' . json_encode($response) . ';修改支付状态返回:' . $result);


            PayBase::setPayDailyRecord(1,'扫码成功',[$outTradeNo],$result);
            parent::apiReturn($code, $output, $msg, true);
        }
    }

    private function _getOrder($ordernum)
    {
        $this->orderModel = new OrderTools('localhost');
        $orderInfo        = $this->orderModel->getOrderInfo($ordernum,
            'member,ordername,ordertel,aid,code,lid,tid,tnum,tprice,paymode,salerid,ordermode,ss.pay_status,ss.status',
            'de.aids,de.series');
        if ($orderInfo['aids']) {
            // 转分销获取第二级分销商
            $tmp                 = explode(',', $orderInfo['aids']);
            $orderInfo['member'] = $tmp[1];
        }
        $this->orderInfo = $orderInfo;

        return $orderInfo;
    }

    public function Query()
    {
        $out_trade_no = I('post.out_trade_no');
        $trade_no     = I('post.trade_no', '');
        $merchantId   = I('post.merchant_id', 0, 'intval');
        $authToken    = null;
        if ($merchantId > 0) {
            $merBiz     = new \Business\Finance\PayMerchant();
            $alipayConf = $merBiz->getMerchantConfig(\Business\Finance\PayMerchant::ALIPAY, $merchantId);
            $authToken  = $alipayConf['app_auth_token'];
        }
        $f2f = new F2FPay($this->isvAppId);
        $res = $f2f->query($out_trade_no, $trade_no, $authToken);
        echo json_encode($res->alipay_trade_query_response);
    }

    /**
     * 支付宝退款——post
     *
     * <AUTHOR> Chen
     * @date 2016-10-05
     * @appid string 支付宝收款账号
     * @out_trade_no string 票付通平台订单号
     * @total_fee int 支付金额，单位：元
     */
    public function Refund()
    {
        $out_trade_no = I('post.out_trade_no');
        $appid        = I('post.appid', PFT_ALIPAY_F2FPAY_ID);
        $merchantId   = I('post.merchant_id', 0, 'intval');
        $sign         = I('post.sign');
        if (!$sign || sha1($out_trade_no)!=$sign) {
            parent::apiReturn(200, [], '签名错误', true);
        }
        $authToken    = null;
        if ($merchantId > 0) {
            $merBiz     = new \Business\Finance\PayMerchant();
            $alipayConf = $merBiz->getMerchantConfig(\Business\Finance\PayMerchant::ALIPAY, $merchantId);
            $authToken  = $alipayConf['app_auth_token'];
        }
        if (I('post.refund_raw', 0, 'intval') == 1) {
            $refund_fee = I('post.refund_money');
            $trade_no   = I('post.trade_no');
        } else {
            $modelTrade = new \Model\TradeRecord\OnlineRefund();
            $trade_info = $modelTrade->GetTradeLog($out_trade_no);
            $refund_fee = $trade_info['refund_money'] / 100;
            $trade_no   = $trade_info['trade_no'];
        }
        $out_refund_no = I('post.ordernum') . '_' . time(); //商户退款单号，商户自定义，此处仅作举例
        $f2fpay        = new F2FPay($appid);
        $result        = $f2fpay->refund($trade_no, $refund_fee, $out_refund_no, $authToken);

        $independence = I('post.independence', 0, 'intval');
        if ($independence == 1) {
            //对于云票务独立收款需要返回通知
            parent::apiReturn(200, [], '支付宝退款成功', true);
        }
        parent::apiReturn(200, $result, '支付宝退款成功', true);
    }

    public function RefundQuery()
    {
        $out_trade_no = I('post.out_trade_no');
        $trade_no     = I('post.trade_no');
        $out_refund_no= I('post.out_refund_no');
        $appid        = I('post.appid', PFT_ALIPAY_F2FPAY_ID);
        $merchantId   = I('post.merchant_id', 0, 'intval');
        $authToken    = null;
        if ($merchantId > 0) {
            $merBiz     = new \Business\Finance\PayMerchant();
            $alipayConf = $merBiz->getMerchantConfig(\Business\Finance\PayMerchant::ALIPAY, $merchantId);
            $authToken  = $alipayConf['app_auth_token'];
        }
        $f2fpay        = new F2FPay($appid);
        $result        = $f2fpay->refundQuery($trade_no, $out_trade_no, $out_refund_no, [], $authToken);
        print_r($result);
    }
    /**
     * 平台会员续费 - 这个接口已经废弃
     * @return json
     */
    public function renew()
    {
        //请求日志
        $logData = json_encode($_REQUEST, JSON_UNESCAPED_UNICODE);
        @pft_log('alipay/renew', $logData);

        $this->apiReturn(204, [], '充值记录生成失败');
    }

    /**
     * 续费通知接口 - 这个接口已经废弃
     * @return json
     */
    public function renewNotify()
    {
        //请求日志
        $logData = json_encode($_REQUEST, JSON_UNESCAPED_UNICODE);
        @pft_log('alipay/renew', $logData);

        exit('fail');
    }

    public function order()
    {
        //平台订单号
        $outTradeNo = I('post.out_trade_no', 0, 'string');
        $qrPay      = I('post.is_qr', 0, 'intval'); //是否二维码支付 3=app支付
        $miniAppid  = I('post.mini_appid', ''); // 支付宝小程序appid
        if (isset($_POST['applet_sub_appid'])) {
            $miniAppid = I('post.applet_sub_appid', '');
        }
        //订单主体说明
        $subject = mb_substr(trim(I('post.subject')), 0, 20, 'utf-8');
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }
        $postOutTradeNo = $outTradeNo;
        if (in_array($qrPay,[1,2])) {
            $postOutTradeNo = 'qr_' . $outTradeNo;
        }
        PayBase::setPayDailyRecord(1,'订单支付',I('post.'));
        $mergeOrder = new \Business\Order\MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $mergeOrder->handlerCombinePayLog($outTradeNo, $subject, self::SOURCE_T, $this->merchantId);
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string)$outTradeNo);
        }

        //如果是内网测试环境，直接把支付状态设置为已经支付
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            //模拟支付
            $data = [];
            if ($qrPay) {
                $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => ''];
            }

            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog($outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_ORDER, '', $this->merchantId);
            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }

            $daction     = 0;
            $trade_no    = 'online_' . time();
            $json_sell   = 'seller.12301.cc';
            $json_buy    = 'buyer.12301.cc';
            $pay_to_pft  = $this->merchantId ? false : true;
            $pay_channel = 7;
            $payTerminal  = I('post.terminal', 0, 'intval'); //支付的终端

            $options = [
                'buyer_info'  => $json_buy,
                'sell_info'   => $json_sell,
                'pay_channel' => $pay_channel,
                'pay_terminal'=> $payTerminal,
            ];

            $res = \Library\Tools\Helpers::payComplete($outTradeNo, $trade_no, self::SOURCE_T, $totalFee,
                (int)$pay_to_pft, $options);

            parent::apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            //真实支付
            $money = $totalFee / 100;
            if (!empty($miniAppid)) {
                $buyerId = I("post.buyer_id");
                // /r/pay_MobilePay/order 接口是聚合支付通道，兼容微信和支付宝的字段
                if (empty($buyerId) && isset($_POST['openid'])) {
                    $buyerId = I('post.openid');
                }
                if (empty($buyerId)) {
                    $this->apiReturn(204, [], '支付失败，购买者ID为空');
                }
                if ($miniAppid == '2018071060507776') {
                    $money          = 0.01;
                    $postOutTradeNo = 'test_' . time();
                }
                // 送蚂蚁森林能量 ---https://alipay.open.taobao.com/docs/doc.htm?docType=1&treeId=682&articleId=118428
                 $bizParams = [
                     'order_type' => 'resort_ticket',
                 ];
                $alipayProviderId = I('post.alipay_provider_id', '', 'safetxt');
                $f2fpay  = new F2FPay($this->isvAppId);
                $response  = $f2fpay->tradeCreate($postOutTradeNo, $money, $subject, self::ORDER_NOTIFY_URL, $subject,
                    $this->appAuthToken, $alipayProviderId, $buyerId, $bizParams);
                //var_dump($response);
            }
            elseif ($qrPay == 3) {
                $appid = I('post.appid');
                if (!$appid) {
                    $appid = PFT_ALIPAY_APP_PAY_ID;
                }
                try {
                    $f2fpay = new F2FPay($appid);
                } catch (AlipayException $exception) {
                    parent::apiReturn(401, [], $exception->getMessage());
                }
                $f2fpay->setSdkExecute(true);
                $resData = $f2fpay->appPay($postOutTradeNo, $money, $subject, self::ORDER_NOTIFY_URL, $subject);
                $response = new \stdClass();
                $response->code = 10000;
                $response->msg  = 'Success';
            }
            else {
                if ((new SafetyCheck())->refererCheck(true) !== true ) {
                    $this->apiReturn(204, [], '二维码支付请求异常，禁止支付:'.self::SOURCE_T);
                }
                $f2fpay   = new F2FPay($this->isvAppId);
                $result   = $f2fpay->qrpay($postOutTradeNo, $money, $subject, self::ORDER_NOTIFY_URL, $subject,
                    $this->appAuthToken);
                $response = $result->alipay_trade_precreate_response;
            }

            if ($response->code != 10000 || $response->msg != 'Success') {
                $this->apiReturn(204, [], '接口出错,请重试,错误描述:' . $response->sub_msg . "({$response->code})");
            }
            if (in_array($qrPay,[1,2])) {
                $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => $response->qr_code];
            }elseif ($qrPay == 3){
                $data = ['outTradeNo' => $outTradeNo, 'alipay_url' => $resData['alipay_url']];
            } elseif ($miniAppid) {
                $data = ['outTradeNo' => $response->out_trade_no, 'tradeNo' => $response->trade_no];
            }
            if (isset($_POST['pay_from']) && $_POST['pay_from'] == 'wanju') {
                $redis = Cache::getInstance('redis');
                $redis->set("wepay_wanju:{$outTradeNo}", 1, '', 600);
            }
            //生成支付记录
            $payMethod  = OnlineTrade::PAY_METHOD_ORDER;
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog($outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
                $payMethod, '', $this->merchantId);
            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }

            parent::apiReturn(200, $data);
        }
    }

    /**
     * 会员卡购票 不能删 会报错
     */
    public function cardSolutionRecharge()
    {
        // TODO: Implement cardSolutionRecharge() method.
    }

    public function orderNotify()
    {
        $payChannel  = 7;
        $outTradeNo  = str_replace('qr_', '', $_POST['out_trade_no']); // 商户订单号
        $tradeNo     = $_POST['trade_no']; // 支付宝交易号
        $tradeStatus = $_POST['trade_status']; // 交易状态
        $f2fpay      = new F2FPay($_POST['app_id']);

        //记录日志
        PayBase::setPayDailyRecord(1,'回调通知',[],$_POST);
        $verify_result = $f2fpay->verify($_POST);
        if (!$verify_result) {
            //记录日志
            PayBase::setPayDailyRecord(1,'回调通知验证失败',[$outTradeNo]);
            exit("fail");
        }

        if ($_POST['trade_status'] == 'TRADE_FINISHED') {
            pft_log('alipay_orderNotify/ok', "1:$tradeNo:$outTradeNo:$tradeStatus\n");
        } else if ($_POST['trade_status'] == 'TRADE_SUCCESS') {
            //判断该笔订单是否在商户网站中已经做过处理
            if (isset($_POST['total_fee'])) {
                $payTotalFee = intval(strval((float)$_POST['total_fee'] * 100));
            } elseif (isset($_POST['total_amount'])) {
                $payTotalFee = intval(strval((float)$_POST['total_amount'] * 100));
            }

            //daction=1,取消订单不原路退回，$daction=0原路退回。TICKET_MACHINE为取票机标识——cgp@2015年8月25日16:13:11
            $daction    = strpos($_POST['body'], 'TICKET_MACHINE') === false ? 1 : 0;
            $OrderModel = new OrderTools('slave');
            $order_info = $OrderModel->getOrderInfo($outTradeNo);
            if ($order_info['ordermode'] == 13) {
                $daction = 0;
            }
            $payToPft = true;
            if ($_POST['seller_id'] != PFT_ALIPAY_PARTNER_ID) {
                $payToPft = false;
            }
            $payTerminal = PayCache::getPayTerminalId($outTradeNo);
            $options = [
                'buyer_info'  => json_encode([
                    'buyer_id'=> safetxt($_POST['buyer_id'])
                ]),
                'sell_info'   => json_encode([
                    'seller_email'  => safetxt($_POST['seller_email']),
                    'app_id'        => safetxt($_POST['app_id']),
                    'auth_app_id'   => safetxt($_POST['auth_app_id'])
                ]),
                'pay_channel'  => $payChannel,
                'pay_termianl' => $payTerminal,
            ];


            $res = \Library\Tools\Helpers::payComplete($outTradeNo, $tradeNo, OnlineTrade::CHANNEL_ALIPAY, $payTotalFee,
                (int)$payToPft, $options);

            $biz = new \Business\Order\MergeOrder();
            if ($res['code'] = 200) {
                $needVerify = PayCache::getOrderVerfify($outTradeNo);
                if ($needVerify) {
                    $successOrders = $res['data']['success_orders'];
                    $biz->handleCombineOrderBuyVerify($successOrders, 0);
                }
            }

            PayBase::setPayDailyRecord(1,'回调通知结果',[$outTradeNo],$res);

            if ($res['code'] == 200 || $res['code'] == 101 || $res['code'] == 102) {
                $redis     = Cache::getInstance('redis');
                $wanjuFlag = $redis->get("wepay_wanju:{$outTradeNo}");

                if ( $wanjuFlag ) {
                    $post2wanju = [
                        'code'=>200,
                        'data'=>[
                            'out_trade_no'  => $outTradeNo,
                            'success_orders'=> $res['data']['success_orders'],
                            'total_fee'     => $payTotalFee,
                            'trade_no'      => $tradeNo,
                            'trade_time'    => date('Y-m-d H:i:s'),
                        ],
                        'msg'=>'success'
                    ];
                    $wanJuUrl = ENV == "PRODUCTION" ? "https://wanju.12301.cc/api/callback/pay-notify":"https://wanju.12301dev.com/api/callback/pay-notify";
                    $content = \Library\Tools\Helpers::curl_post_json($wanJuUrl, json_encode($post2wanju, JSON_UNESCAPED_UNICODE));
                    pft_log('wepay2wanju', json_encode([$post2wanju, $wanJuUrl, $content], JSON_UNESCAPED_UNICODE));
                }
                exit('success');
            } else {
                exit('fail');
            }
        }
    }

    public function payResultCheck()
    {
        $ordernum = I('post.ordernum');
        //支付场景：1订单支付,2充值
        $pay_scen = I('post.pay_scen', 2);
        //todo::检测支付状态
        $model   = new OnlineTrade();
        $pay_log = $model->getLog($ordernum, self::SOURCE_T);
        if (!$pay_log) {
            parent::apiReturn(parent::CODE_CREATED, [], '支付记录不存在', true);
        }

        if ($pay_log['status'] == 1) {
            parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败', true);
    }

    /**
     * 授权回调地址
     * 2022年3月3日起废弃！！！因为支付宝不允许一个账号既是服务商又是收款商户，所以服务商身份证被关了。后续的授权使用玩聚的支付宝服务商账号
     * url:https://openauth.alipay.com/oauth2/appToAppAuth.htm?app_id=2015102800574442&redirect_uri=https%3A%2F%2Fpay.12301.cc%2Fr%2Fpay_Alipay%2FauthCallback&member_id=票付通平台会员ID
     */
    public function authCallback()
    {
        $code      = I('get.app_auth_code');
        $member_id = I('get.member_id');
        if (!is_numeric($member_id)) {
            $html = <<<HTML
<h1>授权失败！用户ID参数错误无法绑定。 </h1>
HTML;
            exit($html);
        }
        $f2fpay      = new F2FPay(PFT_ALIPAY_ISV_APPID);
        $res         = $f2fpay->OpenAuth($code);
        $queryParams = [[$member_id]];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
            $queryParams);
        $dname       = array_column($queryRes['data'], null, 'id')[$member_id]['dname'] ?? '';
        //[app_auth_token] => 201710BB85d05e22487f4748a602baee27eb5A09 [app_refresh_token] => 201710BBf01460e6f8b145568b498a6d615cfX09 [auth_app_id] => 2017101809369321 [expires_in] => 31536000 [re_expires_in] => 32140800 [user_id] => 2088821494460091
        if ($res->alipay_open_auth_token_app_response->code == 10000) {
            $app_auth_token    = $res->alipay_open_auth_token_app_response->app_auth_token;
            $app_refresh_token = $res->alipay_open_auth_token_app_response->app_refresh_token;
            $sub_app_id       = $res->alipay_open_auth_token_app_response->auth_app_id;
            //TODO:: 过期时间，在过期前要根据app_refresh_token重新授权
            $expires_in    = $res->alipay_open_auth_token_app_response->expires_in;
            $re_expires_in = $res->alipay_open_auth_token_app_response->re_expires_in;
            $user_id       = $res->alipay_open_auth_token_app_response->user_id;
            //todo::存入数据库
            //$model  = new PayMerchant();
            //$lastId = $model->saveAlipayMerchantConfig($member_id, PFT_ALIPAY_F2FPAY_ID, $app_auth_token, $app_refresh_token,
            //    $expires_in, $re_expires_in, $user_id, $sub_app_id);
            $payConfigModel = new PayConfig();
            $result         = $payConfigModel->aliPayConfigHandle($member_id, $dname, PFT_ALIPAY_ISV_APPID, $app_auth_token, $app_refresh_token,
                $expires_in, $re_expires_in, $user_id, $sub_app_id);

            if ($result['code'] == 200) {
                $html = <<<HTML
<h1>恭喜，授权成功! </h1>
HTML;
            }
            pft_log('alipay/auth',
                'get=' . json_encode($_GET) . ';response=' . json_encode($res, JSON_UNESCAPED_UNICODE));
        } else {
            $html = <<<HTML
<h1>授权失败，</h1>
HTML;
        }
        echo $html;
        echo json_encode($_GET, JSON_UNESCAPED_UNICODE);
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 仙盖山支付宝回调业务处理
     *
     * @date   2018-04-14
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    public function xgsNotify()
    {
        if (I('get.dev')) {
            $_POST = json_decode(file_get_contents('php://input'), true);
            //$verify_result = 1;
        }
        pft_log('alipay_pc/xgs_recharge', json_encode($_POST));
        if (isset($_POST['app_id'])) {
            $appid         = I('post.app_id');
            $f2fpay        = new F2FPay($appid);
            $verify_result = $f2fpay->verify($_POST);
        } else {
            $alipay_config = [];
            //加载配置文件
            include "/var/www/html/alipay/alipay.config.world.php";
            if (!isset($verify_result)) {
                $alipayNotify  = new AlipaySubmit($alipay_config);
                $verify_result = $alipayNotify->verifyNotify();
            }
        }
        //计算得出通知验证结果
        $out_trade_no = $_POST['out_trade_no']; // 商户订单号
        $trade_no     = $_POST['trade_no']; // 支付宝交易号
        $trade_status = $_POST['trade_status']; // 交易状态
        if ($verify_result) {
            //验证成功
            //记录 POST 的数据
            if ($_POST['trade_status'] == 'TRADE_FINISHED') {
                pft_log('alipay_pc/ok', "1:$trade_no:$out_trade_no:$trade_status\n");
            } else if ($_POST['trade_status'] == 'TRADE_SUCCESS') {
                //判断该笔订单是否在商户网站中已经做过处理
                $pay_total_fee = $_POST['total_fee'] * 100;
                if (isset($_POST['total_amount'])) {
                    $pay_total_fee = $_POST['total_amount'] * 100;
                }

                $businessLib = new UnifyCard();
                $res         = $businessLib->rechargeNotify($out_trade_no, self::SOURCE_T, $trade_no, $pay_total_fee,
                    $_POST['buyer_email'], $alipay_config['seller_email'], $_POST['seller_id']);

                if ($res === true) {
                    pft_log('alipay_pc/ok', "$out_trade_no|更新订单状态|返回代码[$res]");
                    exit('success');
                } else {
                    pft_log('alipay_pc/error', "$out_trade_no|更新订单失败[rechargeNotify]|返回代码[$res]");
                    exit('fail');
                }
            }
            echo "success"; //请不要修改或删除
            pft_log('alipay_pc/ok', "2:$trade_no:$out_trade_no:$trade_status");
        } else {
            //验证失败
            echo "fail";
            pft_log('alipay_pc/error', "3:$trade_no:$out_trade_no:$trade_status");
        }
    }

    /**
     * 内网模拟发送短信
     * <AUTHOR>
     * @date   2018-06-08
     *
     * @param  string  $ordernum
     *
     * @return null
     */
    private function _simuSendSms($ordernum)
    {
        $orderModel = new OrderTools('slave');
        $orderInfo  = $orderModel->getOrderInfo($ordernum);

        $jobId = Queue::push('notify', 'OrderNotify_Job',
            [
                'ordernum' => $ordernum,
                'buyerId'  => $orderInfo['member'], //购买人的ID
                'mobile'   => $orderInfo['ordertel'],
                'aid'      => $orderInfo['aid'],
            ]
        );
        pft_log('alipay_pc/ok', "$ordernum|job_id=$jobId");
    }

    /**
     * 人脸初始化
     *
     * See doc:https://docs.alipay.com/pre-open/20171214172813219030/gbxlpt
     * <AUTHOR> Chen
     * @date 2018-06-12
     *
     */
    public function smilePayInit()
    {
        $merBiz      = new \Business\Finance\PayMerchant();
        $merchantId  = I('post.merchant_id');
        $alipayConf  = $merBiz->getMerchantConfig(\Business\Finance\PayMerchant::ALIPAY, $merchantId);
        $zimmetainfo = $_POST['zimmetainfo'];
        $f2fpay      = new F2FPay(PFT_ALIPAY_F2FPAY_ID);
        $res         = $f2fpay->smilePayInit($zimmetainfo, $alipayConf['app_auth_token']);
        //print_r($res);
        $data = [];
        if ($res->zoloz_authentication_customer_smilepay_initialize_response->code == 10000) {
            $data = json_decode($res->zoloz_authentication_customer_smilepay_initialize_response->result);
            parent::apiReturn(parent::CODE_SUCCESS, $data, '请求成功', true);
        } else {
            parent::apiReturn(
                parent::CODE_INVALID_REQUEST,
                $data,
                '请求失败，错误代码:' . $res->zoloz_authentication_customer_smilepay_initialize_response->code,
                true);
        }
    }

    /**
     * https://pay.12301.cc/r/pay_Alipay/h5UserAuth
     * <AUTHOR>
     * @date 2020-09-05
     */
    public function h5UserAuth()
    {
        $h5Pay = new H5Pay(PFT_ALIPAY_F2FPAY_ID);
        $data = $h5Pay->getBuyerId(PAY_DOMAIN . 'r/pay_Alipay/h5UserAuth');
        parent::apiReturn($data['code'], $data['data'], $data['msg']);
    }

    /**
     * 小程序授权获取用户信息
     * https://pay.12301.cc/r/pay_Alipay/miniAppUserAuth
     * @return bool
     * @throws \Library\Exception
     * 成功响应：{"code":200,"data":{"user_id":"2088802396522265","alipay_user_id":"20881019814879197164249542613626"},"msg":""}
     */
    public function miniAppUserAuth()
    {
        $authCode = I("post.auth_code"); //客户端获取的auth_code
        $appId    = I("post.mini_appid");
        try {
            $f2fPay = new F2FPay($appId);
        } catch (AlipayException $e) {
            parent::apiReturn(400, [], $e->getMessage());

            return false;
        }
        $authToken = $f2fPay->appAuthToken($authCode);
        $data      = [];
        if (isset($authToken->user_id)) {
            $accessToken            = $authToken->access_token;
            $data['user_id']        = $authToken->user_id;
            $data['alipay_user_id'] = $authToken->alipay_user_id;
            // TODO::检验是否已经存在过记录，不存在的话保存数据
            //$authInfo    = $f2fPay->getAuthInfo($accessToken);
            parent::apiReturn(200, $data);
        }
        parent::apiReturn(400, [], "获取用户ID失败:错误代码:" . $authToken->code . ";错误描述:" . $authToken->sub_msg);
    }

    /**
     * 小程序发送模板消息
     * https://pay.12301.cc/r/pay_Alipay/sendMsgAfterPay
     *
     * @return bool
     */
    public function sendMsgAfterPay()
    {
        $appid    = I('post.mini_appid'); //小程序APPID
        $buyerId  = I('post.buyer_id'); //买家ID
        $formId   = I('post.trade_no'); //支付接口返回的交易号
        $code     = I('post.code'); //订单凭证号
        $title    = I('post.title'); //景点门票名称
        $orderNum = I('post.orderNum'); //订单号
        pft_log('alipay/smallapp', 'req=' . json_encode($_POST, JSON_UNESCAPED_UNICODE));
        if (empty($formId)) {
            parent::apiReturn(400, [], 'invalid params');
        }
        $conf = load_config($appid, 'alipay');
        $data = [
            'keyword1' => ['value' => $orderNum],
            'keyword2' => ['value' => $title],
            'keyword3' => ['value' => $code],
        ];
        try {
            $f2fPay = new F2FPay($appid);
        } catch (AlipayException $e) {
            return false;
        }
        $page   = "pages/code/code?code={$code}&ordernum={$orderNum}";
        $result = $f2fPay->sendTemplateMsg($buyerId, $formId, $conf['tmpMsgIdList']['AT0001'], $page, $data,
            $appAuthToken = '');
        pft_log('alipay/smallapp', 'res=' . json_encode($result, JSON_UNESCAPED_UNICODE));

        if ($result->code = 10000) {
            parent::apiReturn(200);
        }
        parent::apiReturn($result->code, [], $result->sub_msg);
    }

    /**
     * 检查是否支付到票付通的微信支付账号
     * */
    private function _checkPayToPft()
    {
        $payToPft = is_null($this->appAuthToken) ? true : false;

        return $payToPft;
    }
    /**
     * app通用支付
     * @param array $paramsData 支付传入参数
     * @return array
     */
    public function appApiPay(array $paramsData){
        $outTradeNo = $paramsData['order_id'];
        $money      = $paramsData['money'];
        $subject    = $paramsData['subject'];
        $notifyUrl  = $paramsData['notify_url'];
        $appid      = $paramsData['appid']?:PFT_ALIPAY_APP_PAY_ID;
        $merchantId = $paramsData['merchant_id'];
        $money = number_format($money / 100, 2, '.', '');
        try {
            $f2fpay = new F2FPay($appid);
        } catch (AlipayException $exception) {
            parent::apiReturn(204,[],$exception->getMessage());
        }
        $f2fpay->setSdkExecute(true);
        $response = $f2fpay->appPay($outTradeNo, $money, $subject, self::COMMON_NOTIFY_URL, $subject);
        $model = new OnlineTrade();
        $ret   = $model->addLog($outTradeNo, $money, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_SIMPLE, '', $merchantId, '', '', $notifyUrl);
        if (!$ret){
            parent::apiReturn(204,[],'交易记录生成失败');
        }
        parent::apiReturn($response['code'], ['alipay_url' => $response['alipay_url']]);
    }
    /**
     * 通用支付回调地址
     * @param array $paramsData 支付传入参数
     * @return array
     */
    public function commonNotify()
    {
        $successData = $_POST;
        $f2fpay      = new F2FPay($successData['app_id']);

        $verify_result = $f2fpay->verify($successData);
        if (!$verify_result) {
            //记录日志
            PayBase::setPayDailyRecord(1,'回调APP通知验证失败',[$successData['out_trade_no']]);
            exit("fail");
        }
        if ($successData['trade_status'] == 'TRADE_FINISHED') {
            pft_log('alipay_orderNotify/ok', "1:{$successData['trade_no']}:{$successData['out_trade_no']}:{$successData['trade_status']}\n");
        } else if ($successData['trade_status'] == 'TRADE_SUCCESS') {
            //判断该笔订单是否在商户网站中已经做过处理
            if (isset($_POST['total_fee'])) {
                $payTotalFee = intval(strval((float)$successData['total_fee'] * 100));
            } elseif (isset($_POST['total_amount'])) {
                $payTotalFee = intval(strval((float)$successData['total_amount'] * 100));
            }else{
                PayBase::setPayDailyRecord(1,'回调APP金额错误',[$successData['out_trade_no']]);
                exit("fail");
            }
            $payToPft = true;
            if ($successData['seller_id'] != PFT_ALIPAY_PARTNER_ID) {
                $payToPft = false;
            }

            $options = [
                'buyer_info'  => $successData['buyer_email'],
                'sell_info'   => $successData['seller_email'],
                'pay_to_pft'  => $payToPft,
            ];
            //todo::Java跟PHP的处理方式不同
            // 发送数据需要做加密，接收端做解密，可以单独写到一个类里面处理这个支付的逻辑
            $payCenterBiz = new PayCenter();
            PayBase::setPayDailyRecord(1, '通用APP支付回调参数', $successData);
            try {
                $payCenterBiz->setOptionData($options);
                $payCenterBiz->setNotifyData($successData['out_trade_no'], $successData['trade_no'],
                    $payTotalFee,
                    $payCenterBiz::STATUS_SUCCESS, self::SOURCE_T);
            } catch (\Exception $e) {
                PayBase::setPayDailyRecord(1, '通用APP支付回调参数错误', $successData, [$e->getMessage()]);
                exit("fail");
            }
            $res = $payCenterBiz->notify();
            PayBase::setPayDailyRecord(1, '通用APP支付回调参数结果', $successData, $res);
            if (in_array($res['code'], [200])) {
                exit('success');
            } else {
                exit('fail');
            }
        }
    }
    /**
     * 通用扫码的回调触发接口
     * <AUTHOR>
     * @date   2020-11-18
     * @return array
     */
    public function qrCommonNotifyTouch()
    {
        if (ENV == 'PRODUCTION') {
            parent::apiReturn(401, [], '禁止推送');
        }
        $orderNum = I('post.order_id', '');
        if (!$orderNum) {
            parent::apiReturn(401, [], '订单号缺失');
        }
        $code = 200;
        $msg  = '推送成功';
        try {
            $tradeLogModel = new OnlineTrade();
            $payCenterBiz  = new PayCenter();
            // 获取支付记录
            $payLogArr = $tradeLogModel->getLog($orderNum, self::SOURCE_T);
            if (empty($payLogArr)) {
                throw new \Exception('未找到支付记录');
            }
            $payCenterBiz->setNotifyData($orderNum, rand(1, 9999999), $payLogArr['total_fee'] * 100,
                $payCenterBiz::STATUS_SUCCESS, self::SOURCE_T);
            $res = $payCenterBiz->notify();
            if ($res['code'] != 200) {
                throw new \Exception($res['msg']);
            }
        } catch (\Exception $e) {
            $code = 401;
            $msg  = $e->getMessage();
        }
        parent::apiReturn($code, [], $msg);
    }
}
