<?php
/**
 * 农行聚合收款
 * User: chenguangpeng
 * Date:2019/04/24
 * Time: 15:43
 */

namespace Controller\pay;

use Business\Finance\AccountMoney;
use Business\Order\MergeOrder;
use Business\Order\Payment;
use Business\Pay\PayBase;
use Business\Pay\PayCache;
use Library\Business\WechatSmallApp;
use Library\Cache\Cache;
use Library\Constants\DingTalkRobots;
use Library\Controller;
use Library\Resque\Queue;
use Library\Tools\Helpers;
use Model\Member\Member;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\Order\TeamOrderSearch;
use Model\TradeRecord\OnlineTrade;
use OSS\Core\OssException;
use OSS\OssClient;
use Process\Order\Validate\MergeOrderPreCheck\OrderPreCheckException;

class AbchinaPay extends Controller implements PayInterface
{
    /**
     * 定义支付渠道
     */
    const SOURCE_T            = 22;
    const TRACK_SOURCE        = 47;
    const RECHARGE_NOTIFY_URL = PAY_DOMAIN . 'r/pay_AbchinaPay/rechargeNotify';
    const XGS_NOTIFY_URL      = PAY_DOMAIN . 'r/pay_AbchinaPay/xgsNotify';
    const ORDER_NOTIFY_URL    = PAY_DOMAIN . 'r/pay_AbchinaPay/orderNotify';
    const REFUND_NOTIFY       = PAY_DOMAIN . 'r/pay_AbchinaPay/refundNotify/'; //退款回调地址

    private $orderModel = null;
    private $merchantId = 0;
    private $appid;

    public function __construct($merchantId = 0)
    {
        $config      = load_config('abchina', 'pay');
        $this->appid = $config[$merchantId]['appid'];

        //微信小程序
        if (isset($_SERVER['HTTP_SMALL_APP'])) {
            $input       = file_get_contents('php://input');
            $_POST       = json_decode($input, true);
            $this->appid = $config[$merchantId]['mini_appid'];
        }
        //$appid       = empty(I('post.appid')) ? PFT_WECHAT_APPID : I('post.appid');
        $this->merchantId = $merchantId;
    }

    /**
     * 订单支付
     */
    public function order()
    {
        $logData = [
            'post'  => $_POST,
            'refer' => $_SERVER['HTTP_REFERER'],
            'agent' => $_SERVER['HTTP_USER_AGENT'],
            'reqid' => $_SERVER['REQUEST_ID'],
        ];
        pft_log('abchina/order_req', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

        //平台订单号
        $outTradeNo = I('out_trade_no', 0, 'string');
        //是否二维码支付
        $qrPay = I('is_qr', 0, 'intval');
        //微信用户openid
        $openid = I('openid', null);
        // 公众号对应的实际openid——独立收款商户
        $subAppid   = I('post.sub_appid', null);
        $subOpendid = I('post.sub_openid', null);
        if (!is_null($subAppid) && $subOpendid) {
            $openid = $subOpendid;
        }
        //订单主体说明
        $subject = mb_substr(trim(I('subject')), 0, 20, 'utf-8') . "[{$outTradeNo}]";
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }

        $mergeOrder = new MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
            try {
                $mergeOrder->handlerCombinePayLog($outTradeNo, '', self::SOURCE_T, $this->merchantId);
            } catch (OrderPreCheckException $e) {
                parent::apiReturn(401, [], $e->getMessage());
            }
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string)$outTradeNo);
        }

        //如果是内网测试环境，直接把支付状态设置为已经支付
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            //模拟支付
            if ($qrPay) {
                $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => ''];
            } else {
                $data = json_encode([]);
            }

            //生成支付记录
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog(
                $outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_ORDER, '', $this->merchantId
            );

            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }

            //模拟支付成功
            $transactionId = 'online_' . time();
            $is_member     = null;
            $json_sell     = json_encode(['appid' => 'seller.12301.cc']);
            $json_buy      = json_encode(['openid' => 'buyer.12301.cc']);
            $pay_to_pft    = false;
            $payTerminal   = '';
            $options       = [
                'buyer_info'   => $json_buy,
                'sell_info'    => $json_sell,
                'pay_channel'  => self::TRACK_SOURCE,
                'pay_termianl' => $payTerminal,
            ];

            $res = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_ABCCHINA,
                $totalFee, (int)$pay_to_pft, $options);

            //兼容旧的农行小程序支付，增加status字段
            echo json_encode(['code' => 200, 'data' => $data, 'status' => 'ok', 'msg' => 'success']);
            exit;
        } else {
            //真实支付
            if (!$qrPay && !$openid) {
                parent::apiReturn(401, [], '请用微信APP打开进行支付');
            }
            if ($qrPay) {
                $type = 'qr';
            } else {
                $type = 'js';
            }
            $money      = number_format($totalFee / 100, 2, '.', '');
            $frontUrl   = I('post.success_url', $_SERVER['HTTP_REFERER']);
            $goodsName  = "支付订单[{$outTradeNo}]";
            $data       = $this->payRpcRequest($type,
                $outTradeNo, $totalFee, self::ORDER_NOTIFY_URL . "?merchantId={$this->merchantId}", $this->appid,
                $openid,
                $goodsName, $frontUrl
            );
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog(
                $outTradeNo, $money, $subject, $subject, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_ORDER, '', $this->merchantId
            );

            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }
            echo json_encode(['code' => 200, 'data' => $data['info'], 'status' => 'ok', 'msg' => 'success']);
            exit;
        }
    }

    /**
     * 会员卡支付
     */
    public function cardSolutionRecharge()
    {
        echo 'cardSolutionRecharge,now time is ' . date('Y-m-d H:i:s'), "\n";

        return ['code' => 500];
    }

    /**
     * 郑州会员卡回调地址
     *
     * <AUTHOR>
     * @date   2018-04-22
     */
    public function cardSolutionNotify()
    {

    }

    /**
     * 微信端支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $ordernum = I('post.ordernum');
        //支付场景：1订单支付,2充值
        $pay_scen  = I('post.pay_scen', 2);
        $isMiniApp = I('post.mini_app');
        $data      = ['scan_channel' => 0];
        pft_log('abchina/pay_result_check', json_encode($_POST, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        //todo::检测支付状态
        $model = new OnlineTrade();
        if ($isMiniApp) {
            $scanLog = $model->checkQrCodeScan($ordernum);
            if ($scanLog) {
                $data['scan_channel'] = $scanLog['scan_channel'];
                $payLog               = $model->getLog($ordernum, self::SOURCE_T);
                if ($payLog['status'] == 1) {
                    parent::apiReturn(parent::CODE_SUCCESS, $data, '支付成功', true);
                } else {
                    parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '未支付', true);
                }
            } else {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '请扫描二维码', true);
            }
        } else {
            sleep(1);
            $payLog = $model->getLog($ordernum, self::SOURCE_T);
            if (!$payLog) {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
            }

            if ($payLog['status'] == 1) {
                parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
            }
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看', true);
        }
    }

    /**
     * 纯粹的刷卡支付接口
     */
    public function cardPay()
    {
    }

    public function micropay()
    {
        $outTradeNo   = I('post.ordernum');
        $authCode     = I('post.auth_code');
        $totalFee     = I('post.money', 0, 'float2int');
        $isMember     = I('post.is_member', 0);
        $subject      = I('post.subject', '订单支付');
        $payScen      = I('post.pay_scen', 1);
        $payTerminal  = I('post.terminal', 0, 'intval'); //支付的终端
        $needVerify   = I('post.verify', 0);//是否支付后立即验证标识
        $terminal     = I('post.terminal', 0, 'strval');//支付时所使用的终端号
        $pay_type     = I('post.pay_type', 2, 'intval');
        $checkSource     = I('post.check_source', -1, 'intval');//验证来源
        $mergePayFlag = false;// 合并付款标识
        //支付来源 - 具体可以参照business.conf.php -> track_source
        // 0=黑色终端机 2=自助机 4=云票务 5=云闸机 20=安卓智能终端机
        $paySource = I('post.pay_source', 20, 'intval');
        //支付的操作人 可以不传 用在记录追踪表李
        $payTrackOpId = I('post.pay_track_op_id', 0);
        //年卡下单类型  默认值为空字符串, 0年卡下单 1年卡续费
        $annualCardOrderType = I('post.annualCardOrderType', false);
        //记录日志
        $logData = json_encode([
            'key'    => '农行支付',
            'notify' => $_POST,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('abchina_micropay', $logData, 3);
        if (!$outTradeNo || !$authCode) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }
        //扫码支付授权码，设备读取用户微信中的条码或者二维码信息
        //（注：用户刷卡条形码规则：18位纯数字，以10、11、12、13、14、15开头）
        $codeLength = strlen($authCode); //$codeLength != 18 ||
        if (!is_numeric($authCode)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '[abcbank]非法的支付条码，请重新刷新支付码:'.$authCode, true);
        }
        $paymentObj = new \Business\Order\MergeOrder();
        if ($paymentObj->isCombineOrder($outTradeNo)) {
            $mergePayFlag = true;
            $paymentObj->handlerCombinePayLog($outTradeNo, $subject, self::SOURCE_T, $this->merchantId);
        }
        switch ($payScen) {
            case 1:
                //订单支付
                if (is_numeric($outTradeNo) || $mergePayFlag === true) {
                    $orderQuery = new OrderQuery('localhost');
                    $totalFee   = $orderQuery->get_order_total_fee($outTradeNo);
                }
                break;
            case 2:
                break;
        }
        $OnlineTrade = new OnlineTrade();
        $result      = $OnlineTrade->addLog($outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_ORDER, '', $this->merchantId);
        if (!$result) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }
        $client    = new \Library\Tools\YarClient('pay');
        $payResult = $client->call('Pay/AbchinaPay/microPay',
            [$this->merchantId, $authCode, $subject . "[{$outTradeNo}]", $outTradeNo, $totalFee]);
        pft_log('abchina_micropay', json_encode($payResult), 3);
        $num          = 0;
        $getMoneyFlag = false;
        do {
            $payResult = $client->call('Pay/AbchinaPay/checkPayStatus', [$this->merchantId, $outTradeNo]);
            if ($payResult['code'] == 200) {
                if ($payResult['res']['code'] == 200) {
                    $getMoneyFlag = true;
                    break;
                }
                if ($payResult['res']['code'] == 500) {
                    $num += 2;
                    //break;
                }
            }
            $num++;
            sleep(1);
        } while ($num < 300);

        if (!$getMoneyFlag) {
            // 如果60秒后还没收款成功冲正
            pft_log('abchina_micropay', json_encode([
                'ordernum' => $outTradeNo,
                'msg'      => '支持超时,需要冲正',
                'data'     => [$outTradeNo, $totalFee],
            ], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE), 3
            );
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '请求农行接口失败:' . $payResult['res']['trade_state'], true);
        }

        //记录日志
        $logData = json_encode([
            'key'      => '农行支付结果',
            'ordernum' => $outTradeNo,
            'res'      => $payResult,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('abchina_micropay', $logData, 3);

        if ($payResult['res']['code'] != 200) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "支付失败,订单号:{$outTradeNo},请求农行失败", true);
        }
        $transactionId = 'ABCBank_'.$outTradeNo;
        $jsonBuy       = '';
        $jsonSell      = '';
        $payToPft      = false;
        $payChannel    = self::TRACK_SOURCE;

        $options = [
            'buyer_info'   => $jsonBuy,
            'sell_info'    => $jsonSell,
            'pay_channel'  => $payChannel,
            'pay_termianl' => $payTerminal,
            'oper'         => $payTrackOpId,
            'pay_source'   => $checkSource,
        ];

        $res = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, self::SOURCE_T, $totalFee,
            (int)$payToPft, $options);
        if ($res['code'] == 200) {
            $data          = [];
            $successOrders = $res['data']['success_orders'];
            foreach ($successOrders as $item) {
                $data[] = [
                    'code'     => 200,
                    'msg'      => '支付成功',
                    'order_id' => $item,
                ];
            }
            //立即验证
            if ($needVerify) {
                if ($mergePayFlag) {
                    $verifyRes = $paymentObj->handleCombineOrderBuyVerifyByRpc($successOrders, $payTerminal, $payTrackOpId, $checkSource);
                    if ($verifyRes){
                        $verifyData = $verifyRes[0];  //默认就取第0个，因为手持机那边只能读取一个订单
                        $data  = $verifyData;
                    }
                } else {
                    $orderInfo = $this->_getOrder($outTradeNo);
                    $query     = new \Business\Order\Query();
                    $data      = $query->getOrderInfoForPrintByRpc($outTradeNo, $orderInfo, $totalFee, 5, $terminal,$checkSource,$payTrackOpId);

                }
            }
        }

        //记录日志
        $logData = json_encode([
            'key'      => '农行支付平台响应',
            'ordernum' => $outTradeNo,
            'res'      => $res,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('abchina_micropay', $logData, 3);

        parent::apiReturn($res['code'], $data, $res['msg'], true);
    }

    /**
     * 平台充值
     *
     * <AUTHOR> Chen
     * @date   2016-10-03
     *
     * @description              充值-post
     * @money:充值的金额，单位“元”
     * @openid:微信openid
     * @aid:供应商ID，可以为空；大于0表示授信预存
     * @did:充值的人的id
     * @appid:微信收款公众号
     * @is_qr:是否扫码支付
     * @memo:备注
     */
    public function recharge()
    {
        parent::apiReturn(401, [], '暂未开通');
    }

    /**
     * 微信退款——post
     *
     * <AUTHOR> Chen
     * @date         2016-10-05
     * @appid        string 微信公众号appid
     * @out_trade_no string 票付通平台订单号
     * @total_fee    int 支付金额，单位：元
     */
    public function Refund()
    {
    }

    public function manaulHandler()
    {
        if (isset($_SESSION['openid'])) {
            $authOpenid = load_config('mobile_data_monitor');
            if (!isset($authOpenid[$_SESSION['openid']])) {
                exit('{"code":401,"msg":"Auth Error"}');
            }
        } elseif (isset($_POST['sanya_recharge'])) {
            // 三亚充值
        } else {
            exit('{"code":401,"msg":"Auth Error"}');
        }
        $ordernum    = I('post.ordernum');
        $refundMoney = I('post.refund_money');
        $action      = I('post.action');
        switch ($action) {
            case 'query':
                $res = $this->query($ordernum, 0);
                break;
            case 'query_micropay':
                $res = $this->query($ordernum, 1);
                break;
            case 'refund':
                if ($_SESSION['openid'] != 'oNbmEuDdAEWDS_a02HYFlzNYFUTg') {
                    $res = ['msg' => '这是微信操作，请将订单号以及退款金额发给光鹏'];
                } else {
                    $method         = 'Pay/YeePayCloudBusiness/refund';
                    $refundOrderNum = "{$ordernum}_" . date('YmdHis');
                    $res            = $this->getYarClient()->call($method, [
                        $this->merchantId,
                        'ordernum'        => $ordernum,
                        'refund_ordernum' => $refundOrderNum,
                        'refund_money'    => $refundMoney,
                    ]);
                }
                break;
            case 'recharge':
                $res = $this->tradeHandlerBySystem($ordernum, 'rechargeNotify');
                break;
            case 'orderpay':
                $res = $this->tradeHandlerBySystem($ordernum, 'orderNotify');
                break;
            default:
                $res = ['msg' => 'invalid action'];
                break;
        }
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 查询微信支付的订单-POST
     *
     * <AUTHOR> Chen
     * @date         2016-10-05
     * @out_trade_no string 平台订单号
     * @trade_no     string 微信支付交易号
     */
    public function query()
    {
        $args = func_get_args();
        if (count($args)) {
            [$orderNum, $action] = $args;
        } else {
            $orderNum = I('post.out_trade_no');
            $action   = I('post.action');
        }
        $method       = 'Pay/AbchinaPay/orderQuery';
        $responseData = $this->getYarClient()->call($method, [$this->merchantId, $orderNum]);

        return $responseData;
    }

    private function decodeNotifyData()
    {
        pft_log('abchina/notify',
            "input=" . file_get_contents('php://input') . ';get=' . json_encode($_GET) . ';post=' . json_encode($_POST));
        $response = $_POST['MSG'];
        if (empty($response)) {
            exit('fail');
        }
        $client       = new \Library\Tools\YarClient('pay');
        $params       = [I('get.merchantId'), 'data' => $response];
        $responseData = $client->call('Pay/AbchinaPay/notifyDecode', $params);
        //记录日志
        $logData = json_encode(
            [
                'reqid' => $_SERVER['REQUEST_ID'],
                'key'   => '农行回调',
                'data'  => $responseData,
            ], JSON_UNESCAPED_UNICODE
        );
        pft_log('abchina/notify', $logData, 'day');
        $res       = $responseData['res'];
        $payResult = $responseData['res']['data'];

        if ($res['code'] != 200 || $payResult['ReturnCode'] != '0000') {
            exit("通知数据异常");
        }
        $sellerInfo = [
            'MerchantID' => $payResult['MerchantID'],
        ];

        $payResult['buy_info']  = '';
        $payResult['sell_info'] = json_encode($sellerInfo, JSON_UNESCAPED_UNICODE);

        return $payResult;
    }

    /**
     * 退款回调通知
     *
     * @author: guanpeng
     * @date:   2019/3/22
     */
    public function refundNotify()
    {
        $logData = json_encode(
            [
                'reqid' => $_SERVER['REQUEST_ID'],
                'key'   => '退款回调',
                'post'  => $_POST,
                'get'   => $_GET,
            ], JSON_UNESCAPED_UNICODE
        );
        pft_log('abchina/refund_notify', $logData, 'day');
    }

    /**
     * @author: guanpeng
     * @date: 2019/4/4
     *
     * @param  bool  $onlyReturn  是否使用只return不echo
     * @param  int  $code
     * @param  string  $msg
     *
     * @return array
     */
    private function response($onlyReturn, $code, $msg)
    {
        if ($onlyReturn == false) {
            echo $msg;
        }

        return ['code' => $code];
    }

    /**
     * 微信充值异步通知
     *
     * <AUTHOR> chen
     * @date   2019-03-24
     */
    public function rechargeNotify($payResult = [])
    {
        if (empty($payResult)) {
            $useReturn = false;
            $payResult = $this->decodeNotifyData();
        } else {
            $useReturn = true;
        }
        $ordern        = $payResult['customerRequestNo']; //订单号
        $trade_no      = $payResult['trade_no']; //交易号
        $pay_total_fee = $payResult['amount'] * 100; //金额用分为单位

        $accountMoneyBiz = new AccountMoney();
        $rechargeRes     = $accountMoneyBiz->recharge(
            $ordern, self::SOURCE_T, $trade_no,
            $pay_total_fee, $payResult['buy_info'],
            $payResult['sell_info'], PFT_WECHAT_APPID, PFT_WECHAT_APPID
        );

        $logData = json_encode([
            'reqid'  => $_SERVER['REQUEST_ID'],
            'key'    => '易宝充值',
            'notify' => $rechargeRes,
            'args'   => [
                $ordern,
                self::SOURCE_T,
                $trade_no,
                $pay_total_fee,
                $payResult['buy_info'],
                $payResult['sell_info'],
                PFT_WECHAT_APPID,
                PFT_WECHAT_APPID,
            ],
        ], JSON_UNESCAPED_UNICODE);
        pft_log('abchina_recharge', $logData, 3);
        if ($rechargeRes !== true) {
            return $this->response($useReturn, 500, 'fail');
        } else {
            return $this->response($useReturn, 200, 'success');
        }
    }

    /**
     * 退款查詢
     */
    public function refundQuery()
    {
        $out_trade_no  = I('post.out_trade_no');
        $out_refund_no = I('post.out_refund_no');
        $responseData  = $this->getYarClient()->call(
            'Pay/YeePayCloudBusiness/queryRefund',
            [$this->merchantId, $out_trade_no, $out_refund_no]
        );

        return $responseData;
    }

    private $client = null;

    private function getYarClient()
    {
        if (is_null($this->client)) {
            $this->client = new \Library\Tools\YarClient('pay');
        }

        return $this->client;
    }

    /**
     * 平台会员续费 - 这个接口已经废弃
     *
     * @return json
     */
    public function renew()
    {
    }

    /**
     * 续费通知接口 - 这个接口已经废弃
     */
    public function renewNotify()
    {
        //请求日志
        $logData = json_encode($_REQUEST, JSON_UNESCAPED_UNICODE);
        @pft_log('wepay/renew', $logData);
        exit('fail');
    }

    /**
     * 年卡微信续费
     *
     * <AUTHOR>
     */
    public function annualRenew()
    {
    }

    /**
     * 年卡续费通知
     *
     * <AUTHOR>
     */
    public function annualRenewNotify()
    {

    }

    /**
     * 延时队列检测支付结果
     *
     * @param  string  $orderNum  支付订单号
     * @param  string  $successMethod  支付成功但回调方法
     *
     * @return array 处理结果，成功['code'=>200]
     */
    public function tradeHandlerBySystem($orderNum, $successMethod)
    {
        // 如果提前已经被支付系统notify通知了就不要往下执行了，多数情况下是这种
        if (PayBase::getOnlinePayState($orderNum, self::SOURCE_T) == 1) {
            PayBase::clearOnlinePayState($orderNum, self::SOURCE_T);

            return ['code' => 200];
        }
        $tradeResult = $this->query($orderNum, 0);
        pft_log('queue/delay_job',
            json_encode(['action' => __METHOD__, 'args' => func_get_args(), 'query_result' => $tradeResult],
                JSON_UNESCAPED_UNICODE));
        if ($tradeResult['code'] == 200
            && $tradeResult['res']['code'] == 200
            && ($tradeResult['res']['data']['status'] == 'PAY_SUCCESS'
                || $tradeResult['res']['data']['status'] == 'SUCCESS')
        ) {
            $tradeResult['res']['data']['trade_no'] = "YeePay{$orderNum}_" . date('Ymd');
            // call_user_func_array 第二个参数传的必须是数组
            $result = call_user_func_array([$this, $successMethod], [$tradeResult['res']['data']]);
            if ($result['code'] == 200) {
                PayBase::clearOnlinePayState($orderNum, self::SOURCE_T);
            }

            return $result;
        }

        return ['code' => 500];
    }

    /**
     * 订单支付成功回调
     *
     * @param  array  $payResult  支付结果
     *
     * @return array 成功or失败 'code'=>200成功
     */
    public function orderNotify($payResult = [])
    {
        if (empty($payResult)) {
            $payResult = $this->decodeNotifyData();
        }
        $outTradeNo     = $payResult['OrderNo'];
        $transaction_id = $payResult['ThirdOrderNo']; //交易号
        $totalFee       = float2int($payResult['Amount']); //金额用分为单位
        $json_buy       = $payResult['buy_info'];
        $json_sell      = $payResult['sell_info'];
        $payTerminal    = 0;
        $pay_to_pft     = false;

        $options = [
            'buyer_info'   => $json_buy,
            'sell_info'    => $json_sell,
            'pay_channel'  => self::TRACK_SOURCE,
            'pay_termianl' => $payTerminal,
        ];

        $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_ABCCHINA,
            $totalFee, (int)$pay_to_pft, $options);

        if ($result['code'] == 200) {
            $needVerify = PayCache::getOrderVerfify($outTradeNo);
            if ($needVerify) {
                $successOrders = $result['data']['success_orders'];
                $paymentObj    = new \Business\Order\MergeOrder();
                $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
            }
        }

        if ($result['code'] == 200 || $result['code'] == 102 || $result['code'] == 101) {
            echo 'success';
        } else {
            echo 'fail';
        }

        if ($result['code'] == 200) {
            return ['code' => 200];
        } else {
            return ['code' => 500];
        }
    }

    /**
     * 仙盖山微信充值
     *
     * @date   2018-03-22
     * <AUTHOR> Lan
     *
     * @param [
     *          'money'  => 充值金额，单位元
     *          'openid' => openid
     *          'aid'    => 供应商Id
     *          'shop_id'=> 商铺Id
     *          'did'    => 会员Id
     *          'qr_pay' => 支付方式：1=扫码，0=jsAPI
     *          'memo'   => 备注
     *        ]
     *
     * @return string
     */
    public function xgsRecharge()
    {
        $money     = I('post.money');
        $money     = floatval(number_format($money, 2, '.', ''));
        $total_fee = $money * 100;
        $openid    = I('post.openid');
        $aid       = I('post.aid');
        $shopId    = I('post.shop_id');
        $did       = I('post.did', 0);
        $is_qr     = I('post.qr_pay', 0);
        $memo      = I('post.memo', '', 'trim');

        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $sid       = $loginInfo['sid'] ?? 0;

        $did = $did ? $did : $sid;
        if (!$did) {
            parent::apiReturn(401, [], '用户身份获取错误');
        }

        if ($is_qr == 0 && empty($openid)) {
            parent::apiReturn(401, [], 'OPENID为空');
        }

        if (!is_numeric($money) || $money < 0) {
            parent::apiReturn(401, [], '请输入大于0的金额，金额必须是数字');
        }

        $queryParams = [[$did, $shopId, $aid]];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
            $queryParams);

        if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
            parent::apiReturn(401, [], '用户信息获取失败，请稍后重试');
        }

        $memberInfo = array_column($queryRes['data'], null, 'id');
        $sellerName = $memberInfo[$did]['dname'] ?? '';
        $shopName   = $memberInfo[$shopId]['dname'] ?? '';
        $bossName   = $memberInfo[$aid]['dname'] ?? '';
        $body       = "[$sellerName]通过{$shopName}给{$bossName}充值{$money}元|{$did}|$shopId|$aid";

        if ($memo) {
            $body .= '|' . $memo;
        }
        $outTradeNo = time() . $did . mt_rand(1000, 9999);
    }

    /**
     * 仙盖山微信回调业务处理
     *
     * @date   2018-04-14
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    public function xgsNotify()
    {

    }

    /**
     * 获取订单信息
     *
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @param  string  $ordernum
     *
     * @return array
     */
    private function _getOrder($ordernum)
    {
        $this->orderModel = new OrderTools('localhost');
        $orderInfo        = $this->orderModel->getOrderInfo(
            $ordernum,
            'member,ordername,ordertel,aid,code,lid,tid,tnum,tprice,ordermode,paymode,salerid,ss.pay_status,ss.status,ss.callback',
            'de.aids,de.series'
        );
        if ($orderInfo['aids']) {
            // 转分销获取第二级分销商
            $tmp                 = explode(',', $orderInfo['aids']);
            $orderInfo['member'] = $tmp[1];
        }
        $this->orderInfo = $orderInfo;

        return $orderInfo;
    }

    /**
     * 生成支付订单号
     *
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @param  string  $ordernum
     *
     * @return string
     */
    private function filterOrderNum($ordernum)
    {
        return str_replace(['qr_'], '', $ordernum);
    }

    /**
     * 内网模拟发送短信
     *
     * <AUTHOR>
     * @date   2018-06-08
     *
     * @param  string  $ordernum
     *
     * @return null
     */
    private function _simuSendSms($ordernum)
    {
        $orderModel = new OrderTools('slave');
        $orderInfo  = $orderModel->getOrderInfo($ordernum);

        $jobId = Queue::push(
            'notify', 'OrderNotify_Job',
            [
                'ordernum' => $ordernum,
                'buyerId'  => $orderInfo['member'], //购买人的ID
                'mobile'   => $orderInfo['ordertel'],
                'aid'      => $orderInfo['aid'],
            ]
        );
        pft_log('abcchina/ok', "$ordernum|job_id=$jobId");
    }

    /**
     * 生成小程序支付码,应用于自助机场景--阿里云OSS存储
     *
     * <AUTHOR> Chen
     * @date   2018-08-09
     */
    public function getPayCodeByOrdernum()
    {
        $defaultAc  = ENV == 'PRODUCTION' ? '123624' : '100014';
        $ac         = I('post.ac', $defaultAc);
        $orderNum   = I('post.out_trade_no');
        $merchantId = I('post.merchant_id');
        $channel    = I('post.channel', 2); // 提交渠道，与追踪记录表的source字段一致。
        $scene      = $orderNum . ':' . $merchantId . ':' . $channel; // 最长32位
        $page       = I('post.page', 'pages/pay/pay');
        $width      = 450;
        $lib        = new WechatSmallApp();
        $res        = $lib->getWxCodeUnlimit($ac, $scene, $page, $width);
        $prefix     = substr($res['data'], 0, 1);
        if ($prefix == '{') {
            $decodeRes = json_decode($res['data'], true);
            $this->apiReturn(401, $res, $decodeRes['errmsg']);
        }
        $fileName = "wechat_miniapp_code/{$orderNum}.png";
        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();
        $baseConfig = C('oss');
        $config     = $baseConfig['aliyun']['senic'];
        $endpoint   = ENV == 'PRODUCTION' ? $config['endpoint']['prod'] : $config['endpoint']['dev'];
        $ret        = ['outTradeNo' => $orderNum];
        try {
            $ossClient = new OssClient($config['accessId'], $config['accessKeySecret'], $endpoint, false);
            $res       = $ossClient->putObject($config['bucket'], $fileName, $res['data']);
            if ($res['info']['http_code'] == 200) {
                $code       = 200;
                $ret['url'] = $config['domain'] . '/' . $fileName . '?x-oss-process=style/image_shuiyin';
            } else {
                $code = 400;
            }
        } catch (OssException $exception) {
            $code = 400;
        }
        parent::apiReturn($code, $ret);
    }

    private function payRpcRequest($type, $orderNum, $money, $orderNotify, $appId, $openId, $subject, $fontUrl)
    {
        $client = new \Library\Tools\YarClient('pay');
        if ($type == 'qr') {

        } else {
            $res = $client->call('Pay/AbchinaPay/jsPay',
                [$this->merchantId, $subject, $orderNum, $money, $appId, $openId, $orderNotify]);
        }
        if ($res['code'] == 200 && $res['res']['code'] == 200) {
            $data = $this->_formatResData($type, $res['res']['data']);

            return $data;
        } else {
            parent::apiReturn(401, [], '接口请求发生错误,请联系客服人员');
        }
    }

    private function _formatResData($type, $res)
    {
        if ($type == 'qr') {

        } else {
            // 替换sub_appId为appId
            $appid = $res['PrePayID']['sub_appId'];
            unset($res['PrePayID']['sub_appId']);
            $res['PrePayID']['appId'] = $appid;
            $data                     = [
                'orderNum' => $res['orderNum'],
                'info'     => $res['PrePayID'],
            ];
        }

        return $data;
    }

}
