<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: Administrator
 * Date: 2019/12/11
 * Time: 9:40
 * 建设银行聚合支付通道
 */

namespace Controller\pay;

use Business\Order\MergeOrder;
use Business\Order\Payment;
use Business\Pay\PayCache;
use Model\Order\OrderQuery;
use Model\Order\TeamOrderSearch;
use Model\TradeRecord\OnlineTrade;
use Process\Order\Validate\MergeOrderPreCheck\OrderPreCheckException;

class CCBBank extends OnlinePayBase
{
    /**
     * 定义支付渠道
     */
    const SOURCE_T = 23;
    const TRACK_SOURCE = 48;
    private $merchantId = 0;
    private $appid;

    private $client = null;

    public function __construct($merchantId = 0)
    {
        $config      = load_config('ccb', 'pay');
        $this->appid = $config[$merchantId]['appid'];
        $merchantId  = isset($_POST['merchant_id']) ? $_POST['merchant_id'] + 0 : $merchantId;
        //微信小程序
        if (isset($_SERVER['HTTP_SMALL_APP'])) {
            $input       = file_get_contents('php://input');
            $_POST       = json_decode($input, true);
            $this->appid = $config[$merchantId]['mini_appid'];
        }
        $this->merchantId = $merchantId;
    }

    public function micropay()
    {
        $outTradeNo   = I('post.ordernum');
        $authCode     = I('post.auth_code');
        $totalFee     = I('post.money', 0);
        $totalFee     = float2int($totalFee);
        $isMember     = I('post.is_member', 0);
        $subject      = I('post.subject', '订单支付');
        $payScen      = I('post.pay_scen', 1);
        $payTerminal  = I('post.terminal', 0, 'intval'); //支付的终端
        $needVerify   = I('post.verify', 0);//是否支付后立即验证标识
        $terminal     = I('post.terminal', 0, 'strval');//支付时所使用的终端号
        $pay_type     = I('post.pay_type', 2, 'intval');
        $mergePayFlag = false;// 合并付款标识
        //支付来源 - 具体可以参照business.conf.php -> track_source
        // 0=黑色终端机 2=自助机 4=云票务 5=云闸机 20=安卓智能终端机
        $paySource = I('post.pay_source', 20, 'intval');
        //支付的操作人 可以不传 用在记录追踪表李
        $payTrackOpId = I('post.pay_track_op_id', 0);
        //年卡下单类型  默认值为空字符串, 0年卡下单 1年卡续费
        $annualCardOrderType = I('post.annualCardOrderType', false);
        $checkSource     = I('post.check_source', -1, 'intval');//验证来源
        //记录日志
        $logData = json_encode([
            'key'    => '建行支付',
            'notify' => $_POST,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('ccb/micropay', $logData);
        if (!$outTradeNo || !$authCode) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }
        //扫码支付授权码，设备读取用户微信中的条码或者二维码信息
        //（注：用户刷卡条形码规则：18位纯数字，以10、11、12、13、14、15开头）
        $codeLength = strlen($authCode);
        if (!is_numeric($authCode)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '非法的支付条码，请重新刷新支付码', true);
        }
        $paymentObj = new \Business\Order\MergeOrder();
        if ($paymentObj->isCombineOrder($outTradeNo)) {
            $mergePayFlag = true;
            $paymentObj->handlerCombinePayLog($outTradeNo, $subject, self::SOURCE_T, $this->merchantId);
        }
        switch ($payScen) {
            case 1:
                //订单支付
                if (is_numeric($outTradeNo) || $mergePayFlag === true) {
                    $orderQuery = new OrderQuery('localhost');
                    $totalFee   = $orderQuery->get_order_total_fee($outTradeNo);
                }
                break;
            case 2:
                break;
        }
        $OnlineTrade = new OnlineTrade();
        $result      = $OnlineTrade->addLog($outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_ORDER, '', $this->merchantId);
        if (!$result) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }
        $payResult = $this->callMicropay($authCode, $subject, $outTradeNo, $totalFee / 100);
        //记录日志
        $logData = json_encode([
            'key'      => '建行支付结果',
            'ordernum' => $outTradeNo,
            'res'      => $payResult,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('ccb/micropay', $logData);
        $transactionId = $payResult['tradeno'];
        $jsonBuy       = '';
        $jsonSell      = '';
        $payToPft      = false;
        $payChannel    = 48;
        // 合并付款流程处理
        $options = [
            'buyer_info'   => $jsonBuy,
            'sell_info'    => $jsonSell,
            'pay_channel'  => $payChannel,
            'pay_termianl' => $payTerminal,
            'oper'         => $payTrackOpId,
            'pay_source'   => $checkSource,
        ];
        $res = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, self::SOURCE_T, $totalFee,
            (int)$payToPft, $options);

        if ($res['code'] == 200) {
            $data          = [];
            $successOrders = $res['data']['success_orders'];
            foreach ($successOrders as $item) {
                $data[] = [
                    'code'     => 200,
                    'msg'      => '支付成功',
                    'order_id' => $item,
                ];
            }
            //立即验证
            if ($needVerify) {
                if ($mergePayFlag) {
                    $verifyRes = $paymentObj->handleCombineOrderBuyVerifyByRpc($successOrders, $payTerminal, $payTrackOpId, $checkSource);
                    if ($verifyRes){
                        $verifyData = $verifyRes[0];  //默认就取第0个，因为手持机那边只能读取一个订单
                        $data  = $verifyData;
                    }
                } else {
                    $orderInfo = $this->_getOrder($outTradeNo);
                    $query     = new \Business\Order\Query();

                    $data      = $query->getOrderInfoForPrintByRpc($outTradeNo, $orderInfo, $totalFee, 5, $terminal,$checkSource,$payTrackOpId);

                }
            }
            $result = ['code' => parent::CODE_SUCCESS, 'data' => $data, 'msg' => '支付成功'];
        } else {
            $result = ['code' => parent::CODE_INVALID_REQUEST, 'data' => [], 'msg' => '付款失败'];
        }

        //记录日志
        $logData = json_encode([
            'key'      => '建行支付平台响应',
            'ordernum' => $outTradeNo,
            'res'      => $result,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('ccb_micropay', $logData, 3);
        parent::apiReturn($result['code'], $result['data'], $result['msg'], true);
    }

    /**
     * 单纯的条码支付收款并记录日志
     * @author: Guangpeng Chen
     * @date: 2019/12/17
     */
    public function cardPay()
    {
        $outTradeNo = I('post.ordernum');
        $auth_code  = I('post.auth_code');
        $totalFee   = I('post.money', 0, 'floatval');
        $subject    = I('post.subject', '订单支付');
        $posId      = I('post.posid', null);
        if (!$outTradeNo || !$auth_code) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }
        pft_log('/ccb/cardPay', json_encode(['action' => 'req', 'data' => $_POST], JSON_UNESCAPED_UNICODE));
        $OnlineTrade = new OnlineTrade();
        // ($mid, $authCode, $proinfo, $orderid, $amount)
        $payResult = $this->callMicropay($auth_code, $subject, $outTradeNo, $totalFee, $posId);
        $tradeNo   = $payResult['tradeno'];
        // ($out_trade_no, $total_fee, $body, $description, $sourceT, $pay_method = 0, $payOrderNum = '', $merchantId = 0, $tradeNo='', $status = 0)
        $res    = $OnlineTrade->addLog($outTradeNo, $totalFee, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_SIMPLE, '', $this->merchantId, $tradeNo, 1);
        $output = ['ordernum' => $outTradeNo, 'trade_no' => $tradeNo];
        pft_log('/ccb/cardPay', json_encode(['action' => 'res', 'data' => $output], JSON_UNESCAPED_UNICODE));
        parent::apiReturn(200, $output, "支付成功", true);
    }

    public function refund()
    {

    }

    /**
     * 获取订单号前缀
     *      同一个订单号对应微信和支付宝通道，需要有不同的订单号，不然会报错
     * @author: Guangpeng Chen
     * @date: 2020/1/19
     * @param string $authCode 支付码
     * @param string $orderNo  订单号
     *
     * @return string
     */
    private function getOrderNumPrefix($authCode, $orderNo)
    {
        $prefixConf = load_config('micropay_pay_auth_code', 'pay');
        $subStr = substr($authCode,0, 2);
        if (in_array($subStr, $prefixConf['alipay'])) {
            // return 'alipay_'.$orderNo;
        } elseif (in_array($subStr, $prefixConf['wepay'])) {
            // return 'wepay_'.$orderNo;
        }
        return $orderNo;
    }
    /**
     * @author: Guangpeng Chen
     * @date: 2019/12/17
     *
     * @param  string  $authCode  支付码
     * @param  string  $subject  描述
     * @param  string  $outTradeNo  订单号
     * @param  double  $totalFee  单位是元
     * @param  string  $posId  柜台号
     *
     * @return array
     */
    private function callMicropay($authCode, $subject, $outTradeNo, $totalFee, $posId = null)
    {
        // $payOrderNum = $this->getOrderNumPrefix($authCode, $outTradeNo);
        $payResult    = $this->getYarClient()->call('Pay/CCB/microPay',
            [$this->merchantId, $authCode, $subject, $outTradeNo, $totalFee, $posId]);
        $getMoneyFlag = false;
        if ($payResult['code'] != 200) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '系统内部请求异常');
        }
        if ($payResult['res']['code'] != 200) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '请求建行异常:' . $payResult['res']['msg']);
        }
        $ret = [
            'tradeno' => $payResult['res']['data']['tradeno'],
            'amount'  => $payResult['res']['data']['amount'],
        ];

        if ($payResult['res']['data']['state'] == 'Y') {
            $getMoneyFlag = true;
        } elseif ($payResult['res']['data']['state'] == 'Q') {
            $num = 0;
            do {
                $queryResult = $this->getYarClient()->call('Pay/CCB/microPayOrderQuery',
                    [$this->merchantId, $outTradeNo, $num, $payResult['res']['data']['qrcode_type']]);
                if ($queryResult['code'] == 200
                    && $queryResult['res']['code'] == 200
                    && $queryResult['res']['data']['state'] == 'Y') {
                    $getMoneyFlag = true;
                    break;
                }
                $num++;
                sleep(1);
            } while ($num < 300);
        } else {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '请求建行异常');
        }
        if ($getMoneyFlag === false) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '用户支付超时');
        }

        return $ret;
    }

    public function order()
    {
        $logData = [
            'post' =>$_POST,
            'refer'=>$_SERVER['HTTP_REFERER'],
            'agent'=>$_SERVER['HTTP_USER_AGENT'],
            'reqid'=>$_SERVER['REQUEST_ID'],
        ];
        pft_log('ccb/order_req', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

        //平台订单号
        $outTradeNo = I('out_trade_no', 0, 'string');
        //是否二维码支付
        $qrPay      = I('is_qr', 0, 'intval');
        //微信用户openid
        $openid     = I('openid', null);
        // 公众号对应的实际openid——独立收款商户
        $subAppid   = I('post.sub_appid', null);

        // merchant_id: 4123750
        $ccbConfig   = load_config('ccb','pay');
        $subAppid   = $ccbConfig[$this->merchantId]['appid'];
        $subOpendid = I('post.sub_openid', null);
        if (!is_null($subAppid) && $subOpendid) {
            $openid = $subOpendid;
        }
        //订单主体说明
        $subject = mb_substr(trim(I('subject')), 0, 20, 'utf-8') . "[{$outTradeNo}]";
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }

        $mergeOrder = new MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
            try {
                $mergeOrder->handlerCombinePayLog($outTradeNo, '', self::SOURCE_T, $this->merchantId);
            } catch (OrderPreCheckException $e) {
                parent::apiReturn(401, [], $e->getMessage());
            }
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string) $outTradeNo);
        }

        //如果是内网测试环境，直接把支付状态设置为已经支付
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            //模拟支付
            if ($qrPay) {
                $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => ''];
            } else {
                $data = json_encode([]);
            }

            //生成支付记录
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog(
                $outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_ORDER, '', $this->merchantId
            );

            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }

            //模拟支付成功
            $transactionId = 'online_' . time();
            $is_member     = null;
            $json_sell     = json_encode(['appid' => 'seller.12301.cc']);
            $json_buy      = json_encode(['openid' => 'buyer.12301.cc']);
            $pay_to_pft    = false;
            $payTerminal   = '';
            $options = [
                'buyer_info' => $json_buy,
                'sell_info'  => $json_sell,
                'pay_channel' => self::TRACK_SOURCE,
                'pay_termianl' => $payTerminal
            ];


            $res = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_ABCCHINA, $totalFee, (int)$pay_to_pft, $options);

            //兼容旧的农行小程序支付，增加status字段
            echo json_encode(['code' => 200, 'data' => $data, 'status' => 'ok', 'msg' => 'success']);
            exit;
        }
        else {
            //真实支付
            if (!$qrPay && !$openid) {
                parent::apiReturn(401, [], '请用微信APP打开进行支付');
            }
            $money    = number_format($totalFee / 100, 2, '.', '');
            $expireAt = date('YmdHis', strtotime("+30 mins"));
            if ($qrPay) {
                //$client = new \Business\JsonRpcApi\PayService\UnifiedPay();
                //$data   = $client->qrRpcService($outTradeNo, $subject, $money, $this->merchantId, $payType, $clientId,$callBackUrl,
                //    $_SERVER['REMOTE_ADDR']);
                $data       = $this->getYarClient()->call('Pay/CCB/qrPay',
                    [$this->merchantId, $outTradeNo, $money, $expireAt, uniqid('ccb_')]);
            } else {
                $data       = $this->getYarClient()->call('Pay/CCB/jsPay',
                    [$this->merchantId, $subAppid, $openid, $outTradeNo, $money,  uniqid('ccb_'), $expireAt]);
            }
            $output     = $this->formatPayData($outTradeNo, $data, $qrPay);
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog($outTradeNo, $money, $subject, $subject, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_ORDER, '', $this->merchantId );
            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }
            echo json_encode(['code' => 200, 'data' => $output, 'status' => 'ok', 'msg' => 'success']);
            exit;
        }
    }

    private function formatPayData($orderNum, Array $RpcData, $payType=0)
    {
        if ($RpcData['code']!=200) {
            parent::apiReturn(401, [], '内部调用发生错误');
        }
        if ($RpcData['res']['code']!=200) {
            parent::apiReturn(401, [], '请求建行接口失败:' . $RpcData['res']['msg']);
        }
        if ($payType == 1) { // 支付宝+微信二维码支付
            $data = [
                'qrUrl'      => $RpcData['res']['data']['url'],
                'outTradeNo' => $orderNum
            ];
        }
        elseif ($payType == 2) {// 微信h5支付
            $data = [
                'qrUrl'     =>$RpcData['res']['data']['mweb_url'],
                'outTradeNo'=>$orderNum
            ];
        }
        else {
            unset($RpcData['res']['data']['orderNum'],
                    $RpcData['res']['data']['partnerid'],
                    $RpcData['res']['data']['mweb_url']);
            if (!isset($_SERVER['HTTP_SMALL_APP'])) {
                unset($RpcData['res']['data']['prepayid']);
            }
            $data = $RpcData['res']['data'];
        }
        return $data;
    }

    /**
     * 支付成功后的回调通知，此地址是配置在建行的什么商户后台，需要提供这个地址给客户自己去配置，我们是没有权限处理的。
     * url: https://pay.12301.cc/r/pay_CCBBank/orderNotify
     * @author: Guangpeng Chen
     * @date: 2019/12/11
     */
    public function orderNotify()
    {
        $logData = [
            'params'   => $_GET,
        ];
        pft_log('ccbbank/notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

        if (!empty($_GET['ORDERID']) && substr($_GET['ORDERID'],0,2) == 'pf') {
            unset($_GET['c']);
            unset($_GET['a']);
            $realUrl = defined("IS_PFT_GRAY")?'http://pay-center.gray.12301.cc/r/pay_CCBank/notify/':'http://pay-center.12301.cc/r/pay_CCBank/notify/';
            $res = curl_post($realUrl, $_GET, 80, 25, '/api/curl_post', [], false, '', 'get');
            echo $res;
            exit();
        }
        
        if ($this->checkSign() === false) {
            exit('checkSign fail');
        }
        $payResult        = $_GET;
        // "POSID":"*********","BRANCHID":"*********","ORDERID":"********","PAYMENT":"0.02","CURCODE":"01","REMARK1":"pay","REMARK2":"pay","ACC_TYPE":"WX","SUCCESS":"Y","TYPE":"1","REFERER":"","CLIENTIP":"************","SIGN":"38497cb649f42507c668dec6c22c9c325a51c05a4b23a31bc68d961e952cbc304306598e38cfeaacd31fa85e367012679df6f6c80885705e231ff8ef58453056dcdc1b5ee86d0c8e6873db5ad1b3561dbe41132ca1510a5e1a4d424a6cbd5fc9b372d96cd4a17f01b3e58250681648ff70aa8d87e5890630ecca0181a6f95f77"
        // "POSID":"*********","BRANCHID":"*********","ORDERID":"********","PAYMENT":"0.02","CURCODE":"01","REMARK1":"pay","REMARK2":"pay","ACC_TYPE":"WX","SUCCESS":"Y","TYPE":"1","REFERER":"","CLIENTIP":"************","SIGN":"7cfacb0a76b14754e6e78c0f628925faefde76f03c79f1a31c146c9af854d92fcf15d66f0e89125cdc0aba50b66494aa1589c82a31b3457dcbefa30995f89b3adb809952d7581cb3d3100fce0bec75058d1b89f110a57d2435bb4b8260e30aba555e2bf8dd20c9193e344ea8e8a7a7b0171fb6c5909519c1729a5fae1c3a916b"
        if ($payResult['SUCCESS'] != 'Y') {
            exit('支付不成功');
        }
        $outTradeNo       = $payResult['ORDERID'];

        if (strpos($payResult['REMARK1'], 'ccb_')!==false) {
            $transaction_id = $payResult['REMARK1'];
        } else {
            $transaction_id = 'ccb_'.$payResult['ORDERID'];
        }
//        $transaction_id   = strpos($payResult['REMARK1'], 'ccb_');//'ccb_'.$payResult['ORDERID']; //交易号
        $totalFee         = float2int($payResult['PAYMENT'] * 100); //金额用分为单位
        $json_buy         = '';
        $json_sell        = json_encode(['posid'=>$payResult['POSID'], 'branchid'=>$payResult['BRANCHID'],'acc_type'=>$payResult['ACC_TYPE']]);
        $payTerminal      = 0;
        $pay_to_pft       = false;

        $options = [
            'buyer_info' => $json_buy,
            'sell_info'  => $json_sell,
            'pay_channel' => self::TRACK_SOURCE,
            'pay_termianl' => $payTerminal
        ];

        $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transaction_id, self::SOURCE_T, $totalFee, (int)$pay_to_pft, $options);

        if ($result['code'] == 200) {
            $needVerify = PayCache::getOrderVerfify($outTradeNo);
            if ($needVerify) {
                $successOrders = $result['data']['success_orders'];
                $paymentObj = new \Business\Order\MergeOrder();
                $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
            }
        }
        pft_log('ccbbank/notify', json_encode(['key'=>$outTradeNo, 'res'=>$result], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        if ($result['code'] == 200 || $result['code'] == 102 || $result['code'] == 101) {
            echo 'success';
        } else {
            echo 'fail';
        }
        if ($result['code'] == 200) {
            return  ['code' => 200];
        } else {
            return ['code' => 500];
        }
    }
    /**
     * 微信端支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $ordernum = I('post.ordernum');
        //支付场景：1订单支付,2充值
        $pay_scen  = I('post.pay_scen', 2);
        $isMiniApp = I('post.mini_app');
        $data      = ['scan_channel' => 0];
        pft_log('ccb/pay_result_check', json_encode($_POST, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));
        //todo::检测支付状态
        $model = new OnlineTrade();
        if ($isMiniApp) {
            $scanLog = $model->checkQrCodeScan($ordernum);
            if ($scanLog) {
                $data['scan_channel'] = $scanLog['scan_channel'];
                $payLog               = $model->getLog($ordernum, self::SOURCE_T);
                if ($payLog['status'] == 1) {
                    parent::apiReturn(parent::CODE_SUCCESS, $data, '支付成功', true);
                } else {
                    parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '未支付', true);
                }
            } else {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '请扫描二维码', true);
            }
        } else {
            //sleep(1);
            $payLog = $model->getLog($ordernum, self::SOURCE_T);
            if (!$payLog) {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
            }

            if ($payLog['status'] == 1) {
                parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
            }
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看', true);
        }
    }
    /**
     * 校验异步通知回来的回调通知
     * @author: Guangpeng Chen
     * @date: 2019/12/17
     * @return bool
     */
    private function checkSign()
    {
        unset($_GET['a'], $_GET['c']);
        $params = [http_build_query($_GET)];
        $payRes = $this->getYarClient()->call('Pay/CCB/checkSign', $params);
        if ($payRes['code'] != 200 || $payRes['res']['code'] != 200) {
            parent::apiReturn(401, [], '接口请求发生错误,请联系客服人员');
        }
        if ($payRes['res']['code'] == 200) {
            return true;
        }

        return false;
    }

    private function getYarClient()
    {
        if (is_null($this->client)) {
            $this->client = new \Library\Tools\YarClient('pay');
        }
        return $this->client;
    }
}
