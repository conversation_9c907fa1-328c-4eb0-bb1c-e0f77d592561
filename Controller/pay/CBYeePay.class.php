<?php
/**
 * 易宝云企支付收款
 * User: chenguangpeng
 * Date:2019/01/26
 * Time: 15:43
 */

namespace Controller\pay;

use Bean\Request\Pay\QrPayRequestBean;
use Business\Captcha\CaptchaApi;
use Business\Finance\AccountMoney;

use Business\JavaApi\Finance\WithdrawQuery;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\JsonRpcApi\PayService\CommonHandle;
use Business\JsonRpcApi\PayService\UnifiedPay;
use Business\Order\MergeOrder;
use Business\Pay\PayBase;
use Business\Pay\PayCache;
use Business\Pay\PayCenter;
use Business\Pay\SpecialOrderPay;
use Business\Pay\UnifiedPayment;
use Business\Pay\WxPayGoldPlan;
use Business\RiskManagement\ShumeiRiskCheck;
use Business\Wechat\WeChatCard;
use Library\Business\WechatSmallApp;
use Library\Cache\Cache;
use Library\Constants\DingTalkRobots;
use Library\Container;
use Library\Kafka\KafkaProducer;
use Library\MessageNotify\PFTSMSInterface;
use Library\Resque\Queue;
use Library\Tools\Helpers;
use Library\Util\EnvUtil;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\Order\Payment;
use Model\TradeRecord\OnlineRefund;
use Model\TradeRecord\OnlineTrade;
use OSS\Core\OssException;
use OSS\OssClient;
use Process\Order\Validate\MergeOrderPreCheck\OrderPreCheckException;

class CBYeePay extends OnlinePayBase
{
    /**
     * 定义支付渠道
     */
    const SOURCE_T                 = 20;
    const TRACK_SOURCE             = 42;
    const RECHARGE_NOTIFY_URL      = PAY_DOMAIN . 'r/pay_CBYeePay/rechargeNotify';
    const XGS_NOTIFY_URL           = PAY_DOMAIN . 'r/pay_CBYeePay/xgsNotify';
    const ORDER_NOTIFY_URL         = PAY_DOMAIN . 'r/pay_CBYeePay/orderNotify/';
    const RENEW_NOTIFY_URL         = PAY_DOMAIN . 'r/pay_CBYeePay/renewNotify/'; //平台会员充值通知地址
    const PARK_NOTIFY_URL          = PAY_DOMAIN . 'r/pay_CBYeePay/parkPayNotify/'; //停车场支付通知地址
    const ANNUAL_RENEW_NOTIFY_URL  = PAY_DOMAIN . 'r/pay_CBYeePay/annualRenewNotify/'; //年卡续费通知地址
    const CARD_SOLUTION_NOTIFY_URL = PAY_DOMAIN . 'r/pay_CBYeePay/cardSolutionNotify/'; //郑州园区会员卡充值通知地址
    const REFUND_NOTIFY            = PAY_DOMAIN . 'r/pay_CBYeePay/refundNotify/'; //退款回调地址
    const COMMON_NOTIFY_URL        = PAY_DOMAIN . 'r/pay_CBYeePay/commonNotify/'; //通用支付回调处理，转发数据到具体的业务逻辑上

    protected $orderModel = null;
    protected $merchantId = 0;
    protected $appid;

    public function __construct($merchantId = 0)
    {
        //微信小程序
        if (isset($_SERVER['HTTP_SMALL_APP'])) {
            $input = file_get_contents('php://input');
            $_POST = json_decode($input, true);
        }

        $appid            = empty(I('post.appid')) ? PFT_WECHAT_APPID : I('post.appid');
        $this->appid      = $appid;
        $this->merchantId = $merchantId;
    }

    /**
     * 订单支付
     */
    public function order()
    {
        $logData = [
            'post'  => $_POST,
            'refer' => $_SERVER['HTTP_REFERER'],
            'agent' => $_SERVER['HTTP_USER_AGENT'],
            'reqid' => $_SERVER['REQUEST_ID'],
        ];
        pft_log('yeepay/order_req', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        $payType = I('post.pay_type', 2, 'intval');
        //平台订单号
        $outTradeNo = I('post.out_trade_no', 0, 'string');
        //是否二维码支付，0 公众号里面支付，1 二维码支付，2 H5支付（手机浏览器跳小程序）
        $qrPay = I('post.is_qr', 0, 'intval');
        //微信用户openid
        $openid = I('post.openid', null);
        // 公众号对应的实际openid——独立收款商户
        $subAppid   = I('post.sub_appid', null);
        $subOpendid = I('post.sub_openid', null);
        $captchaCode = I('post.captchaCode', '', 'strval'); //滑块校验码
        if (!is_null($subAppid) && $subOpendid) {
            $openid = $subOpendid;
        }
        if ($qrPay == 2) {
            $merchantId    = I('post.merchant_id', '0', 'intval');
            $payServiceApi = new CommonHandle();
            $checkRes      = $payServiceApi->h5PayLimit($merchantId);
            if ($checkRes['code']!=200) {
                parent::apiReturn(401, [], '当前商家暂不支持使用此支付方式');
            }
        }
        if (ENV == 'TEST') {
            // dev环境，appid必须使用票付通这个公众号的APPID，openid需要自己获取后填在这里。
            $openid      = 'oNbmEuDdAEWDS_a02HYFlzNYFUTg';
            $this->appid = 'wxd72be21f7455640d';
        }
        //订单主体说明
        $subject = mb_substr(trim(I('subject')), 0, 20, 'utf-8') . "[{$outTradeNo}]";
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }
        PayBase::setPayDailyRecord(3, '订单请求', I('post.'), []);
        $mergeOrder = new MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
            try {
                $mergeOrder->handlerCombinePayLog($outTradeNo, '', self::SOURCE_T, $this->merchantId);
            } catch (OrderPreCheckException $e) {
                parent::apiReturn(401, [], $e->getMessage());
            }
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string)$outTradeNo);
        }

        //如果是内网测试环境，直接把支付状态设置为已经支付
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            //模拟支付
            if ($qrPay) {
                $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => ''];
            } else {
                $data = json_encode([]);
            }
            $payMethod = OnlineTrade::PAY_METHOD_ORDER;
            //生成支付记录
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog(
                $outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
                $payMethod, '', $this->merchantId
            );

            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }

            //模拟支付成功
            $transactionId = 'online_' . time();
            $is_member     = null;
            $json_sell     = json_encode(['appid' => $this->appid]);
            $json_buy      = json_encode(['openid' => 'buyer.12301.cc']);
            $pay_to_pft    = $this->merchantId ? false : true;
            $payTerminal   = I('post.terminal', 0, 'intval'); //支付的终端

            $options = [
                'buyer_info'   => $json_buy,
                'sell_info'    => $json_sell,
                'pay_channel'  => self::TRACK_SOURCE,
                'pay_termianl' => $payTerminal,
            ];

            $res = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_YEEPAY,
                $totalFee, (int)$pay_to_pft, $options);

            echo json_encode(['code' => $res['code'], 'data' => $res['data'], 'status' => 'ok', 'msg' => $res['msg']]);
            exit;
        } else {
            //真实支付
            if (!$qrPay && !$openid) {
                parent::apiReturn(401, [], '请用微信APP打开进行支付');
            }
            // 二维码或h5方式支付
            if ($qrPay>0 && (new SafetyCheck())->refererCheck(true) !== true ) {
                $this->apiReturn(204, [], '二维码支付请求异常，禁止支付:'.self::SOURCE_T);
            }
            $money      = number_format($totalFee / 100, 2, '.', '');
            //数美风控校验
            $this->_riskShumeiPayOrder($openid, $money, $outTradeNo, $captchaCode);

            $frontUrl   = I('post.success_url', $_SERVER['HTTP_REFERER']);
            $goodsName  = "支付订单[{$outTradeNo}]";

            $payMethod  = OnlineTrade::PAY_METHOD_ORDER;
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog(
                $outTradeNo, $money, $subject, $subject, self::SOURCE_T,
                $payMethod, '', $this->merchantId
            );
            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }
            $callMethod = 'wxGzh';
            if ($_POST['appid'] == 'wxe0d4cb8dbc61dbb4') {
                $callMethod = 'wxMiniPay'; // 票付通微平台小程序内发起支付
            }
            if ($qrPay == 2 && $payType == 2) {
                $callMethod = 'wxMiniPay'; // H5支付，通过浏览器发起小程序支付
            }
            elseif ($qrPay == 1 && $payType == 1) {
                $callMethod = 'qrPayAlipay';
            }

            if ($qrPay==1 && $payType == 2) {
                $data = $this->getQrPayParams($outTradeNo);
            } else {
                $data = $this->payRpcRequest(
                    $outTradeNo, $money, self::ORDER_NOTIFY_URL, $this->appid, $openid,
                    $goodsName, $frontUrl, $callMethod
                );
            }

            echo json_encode([
                'code'   => 200,
                'data'   => $data['parameter'],
                'status' => 'ok',
                'msg'    => 'success',
            ]);
            exit;
        }
    }

    private function _riskShumeiPayOrder($account, $money, $outTradeNo, $captchaCode)
    {
        if (empty($_SERVER['HTTP_DEVICEID'])){
            return;
        }
        //滑块验证
        if (empty($captchaCode)){
            $riskCheck = new ShumeiRiskCheck();
            $virtualOrderEventData = [
                'product'   => '购买商品',
                'orderId'   => $outTradeNo,
                'price'     => floatval($money),
            ];
            $orderCheckRes = $riskCheck->shumeiCheck('virtualOrder', 3, $account, $virtualOrderEventData);

            $paymentEventData = [
                'method'    => 'widget',
                'channel'   => 'widget',
                'amount'    => floatval($money),
                'orderId'   => $outTradeNo,
            ];
            $payCheckRes      = $riskCheck->shumeiCheck('payment', 3, $account, $paymentEventData);
            if (($payCheckRes['code'] == 200 && $payCheckRes['data']['riskLevel'] == 'REJECT') || ($orderCheckRes['code'] == 200 && $orderCheckRes['data']['riskLevel'] == 'REJECT')) {
                $this->apiReturn(209, ['need_captcha' => true], '请验证滑块');
            }
        }else{
            $captchaCheck = new CaptchaApi();
            $captchaRes   = $captchaCheck->handleSecondCheck($captchaCode);
            if ($captchaRes !== true) {
                $this->apiReturn(209, ['need_captcha' => true], '请验证滑块');
            }
        }
    }

    /**
     * 会员卡支付
     */
    public function cardSolutionRecharge()
    {
        echo 'cardSolutionRecharge,now time is ' . date('Y-m-d H:i:s'), "\n";

        return ['code' => 500];
    }


    /**
     * 微信端支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $ordernum = I('post.ordernum');
        //支付场景：1订单支付,2充值
        $pay_scen  = I('post.pay_scen', 2);
        $isMiniApp = I('post.mini_app');
        $data      = ['scan_channel' => 0];
        pft_log('yeepay/pay_result_check', json_encode($_POST, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        //todo::检测支付状态
        $model = new OnlineTrade();
        if ($isMiniApp) {
            $scanLog = $model->checkQrCodeScan($ordernum);
            if ($scanLog) {
                $data['scan_channel'] = $scanLog['scan_channel'];
                $payLog               = $model->getLog($ordernum, self::SOURCE_T);
                if ($payLog['status'] == 1) {
                    parent::apiReturn(parent::CODE_SUCCESS, $data, '支付成功', true);
                } else {
                    parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '未支付', true);
                }
            } else {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '请扫描二维码', true);
            }
        } else {
            sleep(1);
            $payLog = $model->getLog($ordernum, self::SOURCE_T);
            if (!$payLog) {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
            }

            if ($payLog['status'] == 1) {
                parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
            }
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看', true);
        }
    }

    /**
     * 纯粹的刷卡支付接口
     */
    public function cardPay()
    {
        $outTradeNo = I('post.ordernum');
        $authCode   = I('post.auth_code');
        $totalFee   = I('post.money', 0) * 100;
        $subject    = I('post.subject', '订单支付');
        if (!$outTradeNo || !$authCode) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }
        $transactionId = self::_microapy($authCode, $outTradeNo, $totalFee, $subject);
        $output        = ['ordernum' => $outTradeNo, 'trade_no' => $transactionId];
        $OnlineTrade   = new OnlineTrade();
        $payToPft      = false;
        $res           = $OnlineTrade->updateLog($outTradeNo, $transactionId, self::SOURCE_T, '', '', 1, '', $payToPft);
        parent::apiReturn(200, $output, "支付成功", true);
    }

    /**
     * 通用的二维码支付接口 仅生成简单的支付二维码，不参与具体的业务逻辑处理
     *
     * @author: Guangpeng Chen
     * @date: 2020/11/11
     */
    public function qrPay($paramsData = [])
    {
        $outTradeNo = $paramsData['order_id'];
        $money      = $paramsData['money'];
        $subject    = $paramsData['subject'];
        $notifyUrl  = $paramsData['notify_url'];
        $payType    = $paramsData['pay_type'];
        $frontUrl   = $paramsData['front_url'];
        $openid     = $paramsData['open_id'];
        $detail     = !empty($paramsData['detail'])?$paramsData['detail']:$subject;

        $callMethod = $payType == 2 ? 'qrPayWechat' : 'qrPayAlipay';

        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            $payCenterBiz = new PayCenter();
            $model        = new OnlineTrade();
            $ret          = $model->addLog($outTradeNo, $money / 100, $subject, $detail, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_SIMPLE, '', 0, '', '', $notifyUrl);
            if (!$ret) {
                parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
            }
            $returnData = [
                'outTradeNo' => $outTradeNo,
                'qrUrl'      => PAY_DOMAIN . 'r/pay_MobilePay/qrCommonNotifyTouch',
            ];
            parent::apiReturn(200, $returnData, '通知成功');
        } else {
            $money = number_format($money / 100, 2, '.', '');
            if ($callMethod == 'qrPayWechat') {
                $data = [
                    'parameter' => [
                        'outTradeNo' => $outTradeNo,
                        'qrUrl'      => 'https://wx.12301.cc/html/platform_recharge.html?p=' . self::encodePayId($outTradeNo),
                    ],
                ];
            } elseif ($callMethod == 'qrPayAlipay') {
                $data = $this->payRpcRequest(
                    $outTradeNo, $money, self::COMMON_NOTIFY_URL, $this->appid, $openid,
                    $subject, $frontUrl, $callMethod
                );
            }
            if (!isset($data['parameter'])) {
                parent::apiReturn(401, [], '生成支付地址失败，请联系客服人员');
            }
            $model = new OnlineTrade();
            $ret   = $model->addLog($outTradeNo, $money, $subject, $subject, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_SIMPLE, '', 0, '', '', $notifyUrl);
            $model = null;
            if (!$ret) {
                parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
            }
            parent::apiReturn(200, $data['parameter']);
        }
    }

    public function jsApiPay($paramsData = [])
    {
        $outTradeNo = $paramsData['order_id'];
        $money      = $paramsData['money'];
        $subject    = $paramsData['subject'];
        $notifyUrl  = $paramsData['notify_url'];
        $payType    = $paramsData['pay_type'];
        $frontUrl   = $paramsData['front_url'];
        $openid     = $paramsData['open_id'];
        $appid      = $paramsData['appid']?:'wxd72be21f7455640d';
        $detail     = !empty($paramsData['detail'])?$paramsData['detail']:$subject;

        $callMethod = 'wxGzh';

        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            $model        = new OnlineTrade();
            $ret          = $model->addLog($outTradeNo, $money / 100, $subject, $subject, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_SIMPLE, '', 0, '', '', $notifyUrl);
            if (!$ret) {
                parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
            }
            $returnData = [
                'outTradeNo' => $outTradeNo,
                'qrUrl'      => PAY_DOMAIN . 'r/pay_MobilePay/qrCommonNotifyTouch',
            ];
            parent::apiReturn(200, $returnData, '通知成功');
        }
        else {
            if (!$openid){
                parent::apiReturn(401, [], 'openid缺失');
            }
            $money = number_format($money / 100, 2, '.', '');
            $data = $this->payRpcRequest(
                $outTradeNo, $money, self::COMMON_NOTIFY_URL, $appid, $openid, $subject, $frontUrl, $callMethod
            );
            if (!isset($data['parameter'])) {
                parent::apiReturn(401, [], '生成支付参数失败，请联系客服人员');
            }
            if (!empty($frontUrl)) {
                (new CommonHandle())->setRedirectUrlRpc($outTradeNo, $frontUrl);
                //(new WxPayGoldPlan())->setRedirectUrl($outTradeNo, $frontUrl);
            }
            $model = new OnlineTrade();
            $ret   = $model->addLog($outTradeNo, $money, $subject, $detail, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_SIMPLE, '', 0, '', '', $notifyUrl);
            $model = null;
            if (!$ret) {
                parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
            }
            parent::apiReturn(200, $data['parameter']);
        }
    }
    /**
     * 通用支付接口回调
     * @author: Guangpeng Chen
     * @date: 2020/11/11
     */
    public function commonNotify()
    {
        $payResult = $this->decodeNotifyData();
        //todo::转发请求到RPC服务，Java跟PHP的处理方式不同
        // 发送数据需要做加密，接收端做解密，可以单独写到一个类里面处理这个支付的逻辑
        $useReturn    = false;
        $payCenterBiz = new PayCenter();
        PayBase::setPayDailyRecord(3, '通用支付回调参数', $payResult);
        try {
            $payCenterBiz->setNotifyData($payResult['customerRequestNo'], $payResult['trade_no'],
                float2int($payResult['amount']),
                $payCenterBiz::STATUS_SUCCESS, self::SOURCE_T);
        } catch (\Exception $e) {
            PayBase::setPayDailyRecord(3, '通用支付回调参数错误', $payResult, [$e->getMessage()]);

            return $this->response($useReturn, 500, 'fail');
        }
        if (isset($payResult['bankOrderNo'])) {
            $redirectUrl = '';
            $payServiceApi = new CommonHandle();
            $redirectRes = $payServiceApi->getRedirectUrlRpc($payResult['customerRequestNo']);
            if ($redirectRes['code'] == 200){
                $redirectUrl = $redirectRes['data']['redirect_url'];
            }
//            $wxPGP       = new WxPayGoldPlan();
//            $redirectUrl = $wxPGP->getRedirectUrl($payResult['customerRequestNo']);
            if ($redirectUrl) {
                $payServiceApi->setRedirectUrlRpc($payResult['bankOrderNo'],$redirectUrl);
                //$wxPGP->setRedirectUrl($payResult['bankOrderNo'], $redirectUrl);
            }
        }
        $res = $payCenterBiz->notify();
        PayBase::setPayDailyRecord(3, '通用支付回调参数结果', $payResult, $res);
        if (in_array($res['code'], [102, 200])) {
            return $this->response($useReturn, 200, 'success');
        } else {
            return $this->response($useReturn, 500, 'fail');
        }
    }

    public $jsonRpcClient = null;
    public function micropay()
    {
        $outTradeNo = I('post.ordernum');
        $authCode   = I('post.auth_code');
        $totalFee   = I('post.money', 0, 'float2int');
        $isMember   = I('post.is_member', 0);
        $subject    = I('post.subject', '订单支付');
        // 1订单支付,2团单支付
        $payScen      = I('post.pay_scen', 1);
        $payTerminal  = I('post.terminal', 0, 'intval'); //支付的终端
        $needVerify   = I('post.verify', 0);//是否支付后立即验证标识
        $terminal     = I('post.terminal', 0, 'strval');//支付时所使用的终端号
        $checkSource     = I('post.check_source', -1, 'intval');//验证来源
        $mergePayFlag = false;// 合并付款标识
        //业务类型
        $payBiz = I('post.pay_biz',0,'intval'); //0-无 1-计时卡业务
        $prefixCodeConf = load_config('micropay_pay_auth_code', 'pay');
        $code_chk       = substr($authCode, 0, 2);
        //支付来源 - 具体可以参照business.conf.php -> track_source
        if (in_array($code_chk, $prefixCodeConf['wepay'])) {
            $this->paySource = 11;
        } elseif (in_array($code_chk, $prefixCodeConf['alipay'])) {
            $this->paySource = 8;
        } elseif (in_array($code_chk, $prefixCodeConf['union'])) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '暂不支持该支付通道');
        }

        //支付的操作人 可以不传 用在记录追踪表李
        $payTrackOpId = I('post.pay_track_op_id', 0);
        $subSid       = I('post.sub_sid', 0, 'intval'); //子商户支付的时候，这个是子商户的供应商id
        //年卡下单类型  默认值为空字符串, 0年卡下单 1年卡续费
        $annualCardOrderType = I('post.annualCardOrderType', false);

        if (!$outTradeNo || !$authCode) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }
        //扫码支付授权码，设备读取用户微信中的条码或者二维码信息
        //（注：用户刷卡条形码规则：18位纯数字，以10、11、12、13、14、15开头）
        $codeLength = strlen($authCode);
        if ($codeLength != 18 || !is_numeric($authCode)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "非法的支付条码[{$authCode}]，请刷新支付码", true);
        }
        PayBase::setPayDailyRecord(3, '扫码请求', I('post.'));
        $paymentObj = new \Business\Order\MergeOrder();
        if ($paymentObj->isCombineOrder($outTradeNo)) {
            $mergePayFlag = true;
            $paymentObj->handlerCombinePayLog($outTradeNo, $subject, self::SOURCE_T, $this->merchantId,$payBiz);
        }

        switch ($payScen) {
            case 1:
                //订单支付
                if (is_numeric($outTradeNo) || $mergePayFlag === true) {
                    $orderQuery = new OrderQuery('localhost');
                    $totalFee   = $orderQuery->get_order_total_fee($outTradeNo);
                }
                break;
            case 2:
                $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '暂无次支付方式');
                break;
        }
        $timeOrderDeposit = 0;
        $leaseOrderDeposit = 0;
        $payBaseBiz = new PayBase();
        if ($payBiz){
            $payOrderOtherRes = $payBaseBiz->orderPayOtherBizHandle($outTradeNo,$payBiz);
            if ($payOrderOtherRes['code'] == 200){
                $timeOrderDeposit = $payOrderOtherRes['data']['timeOrderDeposit'];
                $leaseOrderDeposit = $payOrderOtherRes['data']['leaseOrderDeposit'];
                if ($timeOrderDeposit > 0){
                    $subject .= '/订单押金';
                }
                if ($leaseOrderDeposit > 0){
                    $subject .= '[租赁业务]';
                }
            }
            $totalFee += ($timeOrderDeposit + $leaseOrderDeposit);
        }



        $OnlineTrade = new OnlineTrade();
        $result      = $OnlineTrade->addLog(
            $outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_ORDER, '', $this->merchantId
        );
        if (!$result) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }
        //调用条码支付接口
        $transactionId = self::_microapy($authCode, $outTradeNo, $totalFee, $subject);

        $payToPft = true;
        $jsonSell = $jsonBuy = '';
        if ($timeOrderDeposit > 0){
            $totalFee -= $timeOrderDeposit;
        }
        $options = [
            'buyer_info'   => $jsonBuy,
            'sell_info'    => $jsonSell,
            'pay_channel'  => $this->paySource,
            'pay_termianl' => $payTerminal,
            'oper'         => empty($subSid) ? $payTrackOpId : $subSid,
            'pay_source'   => $checkSource,
            'subOpId'      => $subSid,
            'leaseOrderDeposit' => $leaseOrderDeposit,
        ];

        $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_YEEPAY,
            $totalFee, (int)$payToPft, $options);
        if (!is_null($this->jsonRpcClient)) {
            $this->jsonRpcClient->call('Order/PaymentLock/unlock', [$outTradeNo], 'scenic');
        }
        $data = [];
        if ($result['code'] == 200) {
            $successOrders = $result['data']['success_orders'];
            foreach ($successOrders as $item) {
                $data[] = [
                    'code'     => 200,
                    'msg'      => '支付成功',
                    'order_id' => $item,
                ];
            }
            if ($needVerify) {
                if ($mergePayFlag === true) {
                    $verifyRes = $paymentObj->handleCombineOrderBuyVerifyByRpc($successOrders, $payTerminal, $payTrackOpId, $checkSource, true, $subSid);
                    if ($verifyRes){
                        $verifyData = $verifyRes[0];  //默认就取第0个，因为手持机那边只能读取一个订单
                        $data  = $verifyData;
                    }
                } else {
                    $orderInfo = $this->_getOrder($outTradeNo);
                    $query     = new \Business\Order\Query();
                    $paymode   = 5;

                    $data      = $query->getOrderInfoForPrintByRpc($outTradeNo, $orderInfo, $totalFee, $paymode, $terminal,$checkSource, $payTrackOpId, $subSid);

                }
            }
            $payBaseBiz->afterOrderPayAction($payBiz,OnlineTrade::CHANNEL_YEEPAY,$transactionId, (int)$payToPft,$successOrders);
        }
        PayBase::setPayDailyRecord(3, '扫码支付成功', [$outTradeNo], $result);

        parent::apiReturn($result['code'], $data, $result['msg'], true);
    }

    private function _microapy($authCode, $outTradeNo, $totalFee, $subject)
    {
        $client = new \Library\Tools\YarClient('pay');
        $params = [
            'authCode'    => $authCode,
            'orderId'     => $outTradeNo,
            'totalAmount' => $totalFee / 100,
            'notifyUrl'   => self::ORDER_NOTIFY_URL,
            'goodsName'   => $subject . "[$outTradeNo]",
            'payId'       => get_client_ip(),
            'timeout'     => 15,// 设置超时时间为15分钟
        ];
        $payRes = $client->call('Pay/YeePayCloudBusiness/microPay', $params);
        PayBase::setPayDailyRecord(3, '扫码三方返回', $params, $payRes);
        if ($payRes['code'] != 200 || $payRes['res']['code'] != 200) {
            parent::apiReturn(401, [], '接口请求发生错误,请联系客服人员');
        }
        if ($payRes['res']['data']['retCode'] == '0000'
            && $payRes['res']['data']['status'] == 'SUCCESS') {
            $transactionId = $payRes['res']['data']['bankOrderNo'];
        } elseif ($payRes['res']['data']['retCode'] == 1028
                  && $payRes['res']['data']['status'] == 'PAY_PROCESSING') {
            // 等待用户输入密码的过程
            $times = 0;
            $flag  = false;
            do {
                $res = $client->call('Pay/YeePayCloudBusiness/queryOrder', [$outTradeNo]);
                if ($res['res']['data']['orderStatus'] == 'SUCCESS'
                    || $res['res']['data']['orderStatus'] == 'PAY_SUCCESS') {
                    $flag = true;
                    break;
                }
                $times += 1;
                sleep(1);
            } while ($times < 300);
            if ($flag === false) {
                parent::apiReturn(401, [], '支付超时');
            }
            $transactionId = $res['res']['data']['bankOrderNo'];
        } else {
            parent::apiReturn(401, [], "未知状态[{$payRes['res']['data']['status']}]");
        }

        return $transactionId;
    }

    /**
     * 平台充值
     *
     * <AUTHOR> Chen
     * @date   2016-10-03
     *
     * @description              充值-post
     * @money:充值的金额，单位“元”
     * @openid:微信openid
     * @aid:供应商ID，可以为空；大于0表示授信预存
     * @did:充值的人的id
     * @appid:微信收款公众号
     * @is_qr:是否扫码支付
     * @memo:备注
     */
    public function recharge()
    {
        $money    = I('post.money');
        $money    = floatval(number_format($money, 2, '.', ''));
        $openid   = I('post.openid');
        $aid      = I('post.aid');
        $did      = I('post.did', 0);
        $qrPay    = I('post.qr_pay', 0);
        if (isset($_POST['is_qr'])) {
            $qrPay    = I('post.is_qr', 0);
        }
        $memo     = I('post.memo', '', 'trim');
        $shopId   = I('post.shop_id', '', 'intval');
        $frontUrl = I('post.success_url', $_SERVER['HTTP_REFERER']);
        $payType  = I('post.pay_type', 2, 'intval');
        $captchaCode = I('post.captchaCode', '', 'strval'); //滑块校验码

        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $sid       = $loginInfo['sid'] ?? 0;
        
        if ($loginInfo['dtype'] == 2) {
            exit('{"status":"fail","msg":"景区账号不能充值，请退出重登"}');
        }

        if ($loginInfo['dtype'] == 5) {
            exit('{"status":"fail","msg":"散客账号不能充值，请退出重登"}');
        }

        $did = $did ? $did : $sid;
        if (!$did) {
            exit('{"status":"fail","msg":"用户身份获取错误"}');
        }
        if ($qrPay == 0 && empty($openid)) {
            exit('{"status":"fail","msg":"OPENID为空"}');
        }
        if (!is_numeric($money) || $money < 0) {
            exit('{"status":"fail","msg":"请输入大于0的金额，金额必须是数字"}');
        }

        $queryParams = [[$did, $shopId, $aid]];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
            $queryParams);

        if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
            exit('{"status":"fail","msg":"用户信息获取失败，请稍后重试"}');
        }

        $memberInfo = array_column($queryRes['data'], null, 'id');

        //$modelMember = new Member();
        //$sellName    = str_replace('|', '', $modelMember->getMemberCacheById($did, 'dname'));
        $sellName = str_replace('|', '', $memberInfo[$did]['dname']);

        if ($aid > 0) {
            $boss_name = str_replace('|', '', $memberInfo[$aid]['dname']);
            if ($shopId) {
                $through_name = str_replace('|', '', $memberInfo[$shopId]['dname']);
                $body         = "[$sellName]通过{$through_name}给{$boss_name}充值{$money}元|{$did}|$aid|$shopId";
            } else {
                $body = "[$sellName]给{$boss_name}充值{$money}元|{$did}|$aid";
            }
        } else {
            $body = "[{$sellName}]账户充值{$money}元|{$did}";
        }

        if ($memo) {
            $body .= '|' . $memo;
        }
        //支付订单号,如果前端有传的话用前端传过来的。
        $outTradeNo = I('post.rechage_ordernum', time() . $did . mt_rand(1000, 9999));
        //如果是内网测试环境，直接把支付状态设置为已经支付
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            //写入支付记录
            $model = new OnlineTrade();
            $ret   = $model->addLog($outTradeNo, $money, $body, $body, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_RECHARGE);
            if (!$ret) {
                parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
            }
            $trade_no = 'online_' . time();
            //模拟支付成功
            $accountMoneyBiz = new AccountMoney();
            $res             = $accountMoneyBiz->recharge(
                $outTradeNo, self::SOURCE_T, $trade_no, $money, 'buyer.12301.cc',
                'seller.12301.cc', '12301.cc', '12301.cc'
            );
            if ($res !== true) {
                parent::apiReturn(401, [], '模拟支付失败');
            }
            //返回信息
            if ($qrPay) {
                $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => ''];
            } else {
                $data = ['parameter' => "{}", 'outTradeNo' => $outTradeNo];
            }
            parent::apiReturn(200, $data);
        }
        else {
            $account = $loginInfo['account'] ?: $memberInfo[$did]['account'];
            // 风控，添加数美记录
            $this->_riskShumeiRecharge($account, $money, $outTradeNo, $captchaCode);
            if ($openid != '') {
                $payServiceApi  = new CommonHandle();
                $redirectRes    = $payServiceApi->rechargeCheck($did, 2, $openid, $outTradeNo);
                if ($redirectRes['code'] != 200) {
                    parent::apiReturn(401, [], '检测到您的账号存在风险，请确认是否本人操作，有问题请联系客服');
                }
            }

            $callMethod = 'wxGzh';
            if (isset($_POST['use_env']) && $_POST['use_env'] == 1) {
                $callMethod = 'wxMiniPay';// 小程序支付
            }
            if ( !$qrPay) {
                $sourceT    = self::SOURCE_T;
                $data = $this->payRpcRequest(
                    $outTradeNo, $money, self::RECHARGE_NOTIFY_URL, $this->appid, $openid,
                    "充值[{$outTradeNo}]", $frontUrl, $callMethod
                );
            } else {
                $sourceT    = 36;
            }
            $model = new OnlineTrade();
            $ret   = $model->addLog($outTradeNo, $money, $body, $body, $sourceT,
                OnlineTrade::PAY_METHOD_RECHARGE);
            $model = null;
            if (!$ret) {
                parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
            }

            parent::apiReturn(200, $data);
            //PayBase::addDelayTask(__CLASS__, 'tradeHandlerBySystem', $outTradeNo, 'rechargeNotify', self::SOURCE_T);
        }
    }

    private function _riskShumeiRecharge($account, $money, $outTradeNo, $captchaCode)
    {
        if (empty($_SERVER['HTTP_DEVICEID'])){
            return;
        }
        //滑块验证
        if (empty($captchaCode)){
            $riskCheck = new ShumeiRiskCheck();
            $virtualOrderEventData = [
                'product'   => '账本充值',
                'orderId'   => $outTradeNo,
                'price'     => floatval($money),
            ];
            $orderCheckRes = $riskCheck->shumeiCheck('virtualOrder', 2, $account, $virtualOrderEventData);

            $paymentEventData = [
                'method'    => 'widget',
                'channel'   => 'widget',
                'amount'    => floatval($money),
                'orderId'   => $outTradeNo,
            ];
            $payCheckRes      = $riskCheck->shumeiCheck('payment', 2, $account, $paymentEventData);
            if (($payCheckRes['code'] == 200 && $payCheckRes['data']['riskLevel'] == 'REJECT') || ($orderCheckRes['code'] == 200 && $orderCheckRes['data']['riskLevel'] == 'REJECT')) {
                $this->apiReturn(209, ['need_captcha' => true], '请验证滑块');
            }
        }else{
            $captchaCheck = new CaptchaApi();
            $captchaRes   = $captchaCheck->handleSecondCheck($captchaCode);
            if ($captchaRes !== true) {
                $this->apiReturn(209, ['need_captcha' => true], '请验证滑块');
            }
        }
    }

    public function encodePayId($outTradeNo)
    {
        $data = base64_encode(json_encode(['s' => self::SOURCE_T, 'o' => $outTradeNo]));

        return $data;
    }

    /**
     * 处理原始的支付逻辑
     * @author: guanpeng
     * @date: 2019/4/17
     */
    public function rawPay()
    {
        // 基础安全校验
        $ordernum                    = I("post.ordernum");
        $openId                      = I('post.openid');//'oNbmEuDdAEWDS_a02HYFlzNYFUTg';
        $params                      = [];
        $model                       = new OnlineTrade('slave');
        $tradeInfo                   = $model->getLog($ordernum, self::SOURCE_T);
        $params["customerRequestNo"] = $ordernum;
        $params["amount"]            = $tradeInfo['total_fee'];//需支付额,单位元
        if (empty($tradeInfo)) {
            parent::apiReturn(401, [], '交易不存在');
        }
        if ($tradeInfo['status'] == 1) {
            parent::apiReturn(401, [], '交易已支付成功');
        }
        switch ($tradeInfo['pay_method']) {
            case OnlineTrade::PAY_METHOD_RECHARGE:
                $params["notifyUrl"] = self::RECHARGE_NOTIFY_URL;
                break;
            case OnlineTrade::PAY_METHOD_ORDER:
                $params["notifyUrl"] = self::ORDER_NOTIFY_URL;
                break;
            case OnlineTrade::PAY_METHOD_SIMPLE:
                $params["notifyUrl"] = self::COMMON_NOTIFY_URL;
                break;
            default:
                break;
        }
        if ($tradeInfo['pay_method'] == OnlineTrade::PAY_METHOD_RECHARGE) {
            [$desc, $did, $aid, , $remark] = explode('|', $tradeInfo['description']);

            $payServiceApi = new CommonHandle();
            $redirectRes = $payServiceApi->rechargeCheck($did, 2, $openId);
            if ($redirectRes['code'] != 200) {
                parent::apiReturn(401, [], '检测到您的账号存在风险，请确认是否本人操作，有问题请联系客服');
            }
        }

        $params["frontUrl"]  = '';//
        $params["goodsName"] = $tradeInfo['subject'];
        //$params["payType"]           = "1";//
        $params["appId"]    = PFT_WECHAT_APPID;//$this->appid;
        $params["userid"]   = $openId;
        $params["bankCode"] = "OFFLINE";
        $params["date"]     = date('Y-m-d H:i:s');
        $params["ip"]       = $_SERVER['REMOTE_ADDR'];
        $client             = new \Library\Tools\YarClient('pay');
        $res                = $client->call('Pay/YeePayCloudBusiness/wxGzh', $params);
        //print_r($res);
        if ($res['code'] != 200 || $res['res']['code'] != 200 || $res['res']['data']['retCode'] != '0000') {
            $logData = [
                'reqid' => $_SERVER['REQUEST_ID'],
                'res'   => $res,
            ];
            pft_log('yeepay/order_error', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            parent::apiReturn(401, [], '接口请求发生错误,请联系客服人员');
        }
        $data = [
            'parameter'  => json_decode($res['res']['data']['resultData']),
            'outTradeNo' => $params["customerRequestNo"],
        ];
        parent::apiReturn(200, $data, 'success');
    }
    public function rawPayTest()
    {
        // 基础安全校验
        $openId = I('post.openid', 'o4o3x0GDgx_P2KkQ3YwWfg-flpSw');
        $params                      = [];
        $params["customerRequestNo"] = 'test_cgp_'.time();
        $params["amount"]            = 0.01;//需支付额,单位元

        $params["notifyUrl"] = self::COMMON_NOTIFY_URL;
        $params["frontUrl"]  = '';//
        $params["goodsName"] = 'test';
        //$params["payType"]           = "1";//
        $params["appId"]    = 'wx2f45381cd36a6400';//$this->appid;
        $params["userid"]   = $openId;
        $params["bankCode"] = "OFFLINE";
        $params["date"]     = date('Y-m-d H:i:s');
        $params["ip"]       = $_SERVER['REMOTE_ADDR'];

        $data = $this->payRpcRequest('test_cgp_'.time(), 0.01, self::COMMON_NOTIFY_URL, 'wx2f45381cd36a6400',
            $openId, $goodsName = '充值', $frontUrl = '', $callMethod = 'wxMiniPay');
        echo json_encode([
            'code'   => 200,
            'data'   => $data['parameter'],
            'status' => 'ok',
            'msg'    => 'success',
        ]);
    }
    private function cacheYeePay($outTradeNo, $action, $data = [])
    {
        $cacheKey = "yeepay:$outTradeNo";
        /**
         * @var $redis \Redis
         */
        $redis = Cache::getInstance('redis');
        if (!$redis->exists($cacheKey)) {
            $retData = false;
        }
        if ($action == 'del') {
            $retData = $redis->del($cacheKey);
        } elseif ($action == 'get') {
            $retData = $redis->hGetAll($cacheKey);
            if (isset($retData['parameter'])) {
                $retData['parameter'] = json_decode($retData['parameter'], true);
            }
        } elseif ($action == 'set') {
            if (isset($data['parameter'])) {
                $data['parameter'] = json_encode($data['parameter']);
            }
            $retData = $redis->hMset($cacheKey, $data);
            $redis->expire($cacheKey, 1800);
        }
        unset($redis);

        return $retData;
    }

    private function payRpcRequest($outTradeNo, $money, $notifyUrl, $appId = 'wxd72be21f7455640d',
        $openId = '', $goodsName = '充值', $frontUrl = '', $callMethod = 'wxGzh'
    )
    {
        $cacheData = $this->cacheYeePay($outTradeNo, 'get');
        if ($cacheData) {
            return $cacheData;
        }
        //if (!empty($openId)) {
        //    WeChatCard::cacheOpenId($outTradeNo, 'set', $openId);
        //}
        $client                      = new \Library\Tools\YarClient('pay');
        $params                      = [];
        $params["customerRequestNo"] = $outTradeNo;
        $params["amount"]            = $money;//需支付额
        $params["notifyUrl"]         = $notifyUrl;//
        $params["frontUrl"]          = $frontUrl;//
        $params["goodsName"]         = $goodsName;
        $params["payType"]           = "1";//
        $params["appId"]             = $appId;//$this->appid;
        $params["userid"]            = $openId;
        $params["bankCode"]          = "OFFLINE";
        $jsApiMethods = ['wxGzh', 'wxMiniPay',  'qrPayWechat','qrPayAlipay'];
        if ( in_array($callMethod, $jsApiMethods)) {
            unset($params['payType']);
            // 原生API支付
            $params["date"] = date('Y-m-d H:i:s');
            $params["ip"]   = $_SERVER['REMOTE_ADDR'];
            $res            = $client->call('Pay/YeePayCloudBusiness/' . $callMethod, $params);
        } else {
            $res = $client->call('Pay/YeePayCloudBusiness/pay', $params);
        }
        PayBase::setPayDailyRecord(3, '订单三方返回', $params, $res);
        if ($res['code'] != 200 || $res['res']['code'] != 200 || $res['res']['data']['retCode'] != '0000') {
            $logData = [
                'customerRequestNo'=>$outTradeNo,
                'callMethod'=>$callMethod,
                'params'=>$params,
                'reqid' => $_SERVER['REQUEST_ID'],
                'res'   => $res,
            ];
            pft_log('yeepay/order_error', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            parent::apiReturn(401, [], '接口请求发生错误,请联系客服人员[错误代码:cbyp222]');
        }
        $data = $this->returnFormat($callMethod, $outTradeNo, $res);
        $this->cacheYeePay($outTradeNo, 'set', $data);

        return $data;
    }

    private function returnFormat($callMethod, $outTradeNo, Array $res)
    {
        if ($callMethod == 'wxGzh' || $callMethod == 'wxMiniPay') {
            $data = ['parameter' => json_decode($res['res']['data']['resultData']), 'outTradeNo' => $outTradeNo];
        } elseif ($callMethod == 'qrPayWechat' || $callMethod == 'qrPayAlipay') {
            $data = [
                'parameter' => [
                    'outTradeNo' => $outTradeNo,
                    'qrUrl'      => $res['res']['data']['resultData'],
                ],
            ];
        } else {
            $data = ['action' => 'redirect', 'outTradeNo' => $outTradeNo, 'qrUrl' => $res['res']['data']['payUrl']];
        }

        return $data;
    }

    private function getQrPayParams($outTradeNo)
    {
        $url  = MOBILE_DOMAIN . 'html/platform_recharge.html?p=' . self::encodePayId($outTradeNo);
        $data = [
            'parameter' => [
                'outTradeNo' => $outTradeNo,
                'qrUrl'      => $url,
            ],
        ];
        PayBase::setPayDailyRecord(3, '订单主扫返回', [$outTradeNo], $data);

        return $data;
    }

    /**
     * 微信退款——post
     *
     * <AUTHOR> Chen
     * @date         2016-10-05
     * @appid        string 微信公众号appid
     * @out_trade_no string 票付通平台订单号
     * @total_fee    int 支付金额，单位：元
     */
    public function Refund()
    {
        //退款权限控制
        $flag = 0;
        if (isset($_SESSION['openid'])) {
            $authOpenid = load_config('mobile_data_monitor');
            if (!isset($authOpenid[$_SESSION['openid']])) {
                exit('{"code":401,"msg":"Auth Error"}');
            }
            $flag = 1;
        }

        $independence = I('post.independence', 0, 'intval');
        if ($independence == 0) {
            //商户端默认都是没有权限
            exit('{"code":401,"msg":"Auth Error"}');
        }

        $merchantId = I('post.merchant_id', 0, 'intval');
        $method     = 'Pay/YeePayCloudBusiness/refund';
        $params     = ['ordernum' => I('post.ordernum'), 'refund_money' => I('post.refund_money')];
        $this->getYarClient()->call($method, $params);
    }

    public function manaulHandler()
    {
        if (isset($_SESSION['openid'])) {
            $authOpenid = load_config('mobile_data_monitor');
            if (!isset($authOpenid[$_SESSION['openid']])) {
                exit('{"code":401,"msg":"Auth Error"}');
            }
        } elseif (isset($_POST['sanya_recharge'])) {
            // 三亚充值
        } else {
            exit('{"code":401,"msg":"Auth Error"}');
        }
        $ordernum    = I('post.ordernum');
        $refundMoney = I('post.refund_money');
        $action      = I('post.action');
        switch ($action) {
            case 'query':
                $res = $this->query($ordernum, 0);
                break;
            case 'query_micropay':
                $res = $this->query($ordernum, 1);
                break;
            case 'refund':
                if ($_SESSION['openid'] != 'oNbmEuDdAEWDS_a02HYFlzNYFUTg') {
                    $res = ['msg' => '这是危险操作，请将订单号以及退款金额发给光鹏'];
                } else {
                    $method         = 'Pay/YeePayCloudBusiness/refund';
                    $refundOrderNum = mt_rand(1000, 9999) . time();
                    $res            = $this->getYarClient()->call($method, [
                        'ordernum'        => $ordernum,
                        'refund_ordernum' => $refundOrderNum,
                        'refund_money'    => $refundMoney,
                    ]);
                }
                break;
            case 'refundQuery':
                // 获取退款流水号
                $paymentObj = new \Business\Order\MergeOrder();
                if ($paymentObj->isCombineOrder($ordernum)) {
                    $combineModel = new Payment();
                    $orderList    = $combineModel->getCombileLogByTradeId($ordernum);
                    $pftOrder     = array_column($orderList, 'ordernum');
                } else {
                    $pftOrder = $ordernum;
                }
                $tradeModel = new OnlineRefund('slave');
                $data       = $tradeModel->getRefundJournalByOrderNum($pftOrder);
                $res        = [
                    'pftOrder' => $pftOrder,
                    'data'     => [],
                ];
                foreach ($data as $item) {
                    $res['data'][] = $this->getYarClient()->call('Pay/YeePayCloudBusiness/queryRefund',
                        [$ordernum, $item['batch_no']]);
                }
                break;
            case 'recharge':
                $res = $this->tradeHandlerBySystem($ordernum, 'rechargeNotify');
                break;
            case 'orderpay':
                $res = $this->tradeHandlerBySystem($ordernum, 'orderNotify');
                break;
            default:
                $res = ['msg' => 'invalid action'];
                break;
        }
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 查询微信支付的订单-POST
     *
     * <AUTHOR> Chen
     * @date         2016-10-05
     * @out_trade_no string 平台订单号
     * @trade_no     string 微信支付交易号
     */
    public function query()
    {
        $args = func_get_args();
        if (count($args)) {
            [$orderNum, $action] = $args;
        } else {
            $orderNum = I('post.out_trade_no');
            $action   = I('post.action');
        }
        $method = 'Pay/YeePayCloudBusiness/queryOrder';
        //if ($action == 1) {
        //    $method = 'Pay/YeePayCloudBusiness/micropayQuery';
        //}
        $responseData = $this->getYarClient()->call($method, [$orderNum]);

        return $responseData;
    }

    private function decodeNotifyData()
    {
        $response = $_POST['response'];
        if (empty($response)) {
            exit('fail');
        }
        $client       = new \Library\Tools\YarClient('pay');
        $params       = ['data' => $response];
        $responseData = $client->call('Pay/YeePayCloudBusiness/notifyDecode', $params);
        //记录日志
        PayBase::setPayDailyRecord(3, '回调', [], $responseData);
        $res       = $responseData['res'];
        $payResult = json_decode($responseData['res']['data'], true);

        /**
         * { "parentCustomerName": "", "orderType": "SALES", "amount": 0.01, "upCustomerNo": "***********",
         * "retCode": "0000", "retMsg": "成功", "customerName": "",
         * "customerRequestNo": "1548401787_72395", "completeDate": "2019-01-25 15:39:09",
         * "orderSoure": "CASHIER", "currency": "CNY", "parentCustomerNo": "***********",
         * "splitInfo": "", "orderDate": "2019-01-25 15:36:29",
         * "customerNo": "***********", "status": "PAY_SUCCESS" }
         */
        if ($res['code'] != 200
            || $payResult['retCode'] != '0000'
            || ($payResult['status'] != 'PAY_SUCCESS' && $payResult['status'] != 'SUCCESS')) {
            PayBase::setPayDailyRecord(3, '回调通知失败', [], $payResult);
            PHP_SAPI == 'cli' ?: exit("通知数据异常");
        }
        $payResult['trade_no'] = $payResult['bankOrderNo'];

        $this->cacheYeePay($payResult['customerRequestNo'], 'del');

        $buyerInfo  = [
            'orderType'    => $payResult['orderType'],
            'orderSoure'   => $payResult['orderSoure'],
            'customerName' => $payResult['customerName'],
        ];
        $sellerInfo = [
            'customerNo'         => $payResult['customerNo'],
            'upCustomerNo'       => $payResult['upCustomerNo'],
            'parentCustomerNo'   => $payResult['parentCustomerNo'],
            'parentCustomerName' => $payResult['parentCustomerName'],
        ];
        // 调用查询接口，获取银行流水号
        /* $queryRes = $client->call('Pay/YeePayCloudBusiness/queryOrder', [$payResult['customerRequestNo']]);
         pft_log('wepay_bridge', "queryOrder:{$queryRes['res']['data']['bankOrderNo']}," . json_encode($queryRes, JSON_UNESCAPED_UNICODE));
         if (isset($queryRes['res']['data']['bankOrderNo'])) {
             $payResult['bank_orderno'] = $queryRes['res']['data']['bankOrderNo'];
         }*/
        $payResult['buy_info']  = json_encode($buyerInfo, JSON_UNESCAPED_UNICODE);
        $payResult['sell_info'] = json_encode($sellerInfo, JSON_UNESCAPED_UNICODE);

        return $payResult;
    }

    /**
     * 退款回调通知
     *
     * @author: guanpeng
     * @date:   2019/3/22
     */
    public function refundNotify()
    {
        $logData = json_encode(
            [
                'reqid' => $_SERVER['REQUEST_ID'],
                'key'   => '退款回调',
                'post'  => $_POST,
                'get'   => $_GET,
            ], JSON_UNESCAPED_UNICODE
        );
        pft_log('yeepay/refund_notify', $logData, 'day');
    }

    /**
     * @author: guanpeng
     * @date: 2019/4/4
     *
     * @param  bool  $onlyReturn  是否使用只return不echo
     * @param  int  $code
     * @param  string  $msg
     *
     * @return array
     */
    private function response($onlyReturn, $code, $msg)
    {
        if ($onlyReturn == false) {
            echo $msg;
        }

        return ['code' => $code];
    }

    /**
     * 微信充值异步通知
     *
     * <AUTHOR> chen
     * @date   2019-03-24
     */
    public function rechargeNotify($payResult = [])
    {
        if (empty($payResult)) {
            $useReturn = false;
            $payResult = $this->decodeNotifyData();
        } else {
            $useReturn = true;
        }
        $ordern        = $payResult['customerRequestNo']; //订单号
        $trade_no      = $payResult['trade_no']; //交易号
        $pay_total_fee = float2int($payResult['amount']); //金额用分为单位

        $accountMoneyBiz = new AccountMoney();
        $rechargeRes     = $accountMoneyBiz->recharge(
            $ordern, self::SOURCE_T, $trade_no,
            $pay_total_fee, $payResult['buy_info'],
            $payResult['sell_info'], PFT_WECHAT_APPID, PFT_WECHAT_APPID
        );

        $logData = json_encode([
            'reqid'  => $_SERVER['REQUEST_ID'],
            'key'    => '易宝充值',
            'notify' => $rechargeRes,
            'args'   => [
                $ordern,
                self::SOURCE_T,
                $trade_no,
                $pay_total_fee,
                $payResult['buy_info'],
                $payResult['sell_info'],
                PFT_WECHAT_APPID,
                PFT_WECHAT_APPID,
            ],
        ], JSON_UNESCAPED_UNICODE);
        pft_log('yeepay_recharge', $logData, 3);
        if ($rechargeRes !== true) {
            return $this->response($useReturn, 500, 'fail');
        } else {
            return $this->response($useReturn, 200, 'success');
        }
    }

    /**
     * 退款查詢
     */
    public function refundQuery()
    {
        $out_trade_no  = I('post.out_trade_no');
        $out_refund_no = I('post.out_refund_no');
        $responseData  = $this->getYarClient()->call(
            'Pay/YeePayCloudBusiness/queryRefund',
            [$out_trade_no, $out_refund_no]
        );

        return $responseData;
    }

    private $client = null;

    private function getYarClient()
    {
        if (is_null($this->client)) {
            $this->client = new \Library\Tools\YarClient('pay');
        }

        return $this->client;
    }

    /**
     * 平台会员续费 - 这个接口已经废弃
     *
     * @return json
     */
    public function renew()
    {
    }

    /**
     * 续费通知接口 - 这个接口已经废弃
     */
    public function renewNotify()
    {
        //请求日志
        $logData = json_encode($_REQUEST, JSON_UNESCAPED_UNICODE);
        @pft_log('wepay/renew', $logData);
        exit('fail');
    }

    /**
     * 年卡微信续费
     *
     * <AUTHOR>
     */
    public function annualRenew()
    {
    }

    /**
     * 年卡续费通知
     *
     * <AUTHOR>
     */
    public function annualRenewNotify()
    {

    }

    /**
     * 延时队列检测支付结果
     *
     * @param  string  $orderNum  支付订单号
     * @param  string  $successMethod  支付成功但回调方法
     *
     * @return array 处理结果，成功['code'=>200]
     */
    public function tradeHandlerBySystem($orderNum, $successMethod)
    {
        // 如果提前已经被支付系统notify通知了就不要往下执行了，多数情况下是这种
        if (PayBase::getOnlinePayState($orderNum, self::SOURCE_T) == 1) {
            PayBase::clearOnlinePayState($orderNum, self::SOURCE_T);

            return ['code' => 200];
        }
        $tradeResult = $this->query($orderNum, 0);
        pft_log('queue/delay_job',
            json_encode(['action' => __METHOD__, 'args' => func_get_args(), 'query_result' => $tradeResult],
                JSON_UNESCAPED_UNICODE));
        if ($tradeResult['code'] == 200
            && $tradeResult['res']['code'] == 200
            && ($tradeResult['res']['data']['status'] == 'PAY_SUCCESS'
                || $tradeResult['res']['data']['status'] == 'SUCCESS')
        ) {
            $tradeResult['res']['data']['trade_no'] = "YeePay{$orderNum}_" . date('Ymd');
            // call_user_func_array 第二个参数传的必须是数组
            $result = call_user_func_array([$this, $successMethod], [$tradeResult['res']['data']]);
            if ($result['code'] == 200) {
                PayBase::clearOnlinePayState($orderNum, self::SOURCE_T);
            }

            return $result;
        }

        return ['code' => 500];
    }

    /**
     * 订单支付成功回调
     *
     * @param  array  $payResult  支付结果
     *
     * @return array 成功or失败 'code'=>200成功
     */
    public function orderNotify($payResult = [])
    {
        if (empty($payResult)) {
            $payResult = $this->decodeNotifyData();
        }
        $outTradeNo = $payResult['customerRequestNo'];
        // 查询易宝支付的银行号

        $transaction_id = $payResult['trade_no']; //交易号
        $totalFee       = intval(strval($payResult['amount'] * 100)); //金额用分为单位
        $json_buy       = $payResult['buy_info'];
        $json_sell      = $payResult['sell_info'];
        $biz            = new \Business\Order\MergeOrder();
        // 更新缓存状态为已支付
        PayBase::setOnlinePayState($outTradeNo, self::SOURCE_T, 1);
        $is_member   = 0;
        $payTerminal = PayCache::getPayTerminalId($outTradeNo);
        $pay_to_pft  = true;
        // 为了支持微信支付成功后的页面跳转，需要获取到易宝支付的银行流水号，与pay.12301.cc/wepay/wepay_bridge.php对应
        if (isset($payResult['bankOrderNo'])) {
            $payServiceApi = new CommonHandle();
            $redirectRes   = $payServiceApi->getRedirectUrlRpc($outTradeNo);
            if ($redirectRes['code'] == 200){
                $payServiceApi->setRedirectUrlRpc($payResult['bankOrderNo'], $redirectRes['data']['redirect_url']);
            }
            $wxPGP       = new WxPayGoldPlan();
//            $redirectUrl = $wxPGP->getRedirectUrl($outTradeNo);
            //$wxPGP->setRedirectUrl($payResult['bankOrderNo'], $redirectUrl);
            if (method_exists($wxPGP, 'getTradeOpenid')) {
                $openId = $wxPGP->getTradeOpenid($outTradeNo);
                if ($openId) {
                    $wxPGP->setTradeOpenid($payResult['bankOrderNo'], $openId);
                }
            }
        }

        $options = [
            'buyer_info'   => $json_buy,
            'sell_info'    => $json_sell,
            'pay_channel'  => self::TRACK_SOURCE,
            'pay_termianl' => $payTerminal,
        ];

        $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_YEEPAY,
            $totalFee, (int)$pay_to_pft, $options);

        if ($result['code'] == 200) {
            // 购即验逻辑
            $needVerify = PayCache::getOrderVerfify($outTradeNo);
            if ($needVerify) {
                $successOrders = $result['data']['success_orders'];
                $paymentObj    = new \Business\Order\MergeOrder();
                $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
            }
            // if ($openId = WeChatCard::cacheOpenId($outTradeNo, 'get')) {
            //     WeChatCard::sendCard($openId, $result['data']['success_orders']);
            // }
        }

        if ($result['code'] == 200 || $result['code'] == 102 || $result['code'] == 101) {
            echo 'success';
        } else {
            echo 'fail';
        }

        if ($result['code'] == 200) {
            return ['code' => 200];
        } else {
            return ['code' => 500];
        }
    }

    /**
     * 仙盖山微信充值
     *
     * @date   2018-03-22
     * <AUTHOR> Lan
     *
     * @param [
     *          'money'  => 充值金额，单位元
     *          'openid' => openid
     *          'aid'    => 供应商Id
     *          'shop_id'=> 商铺Id
     *          'did'    => 会员Id
     *          'qr_pay' => 支付方式：1=扫码，0=jsAPI
     *          'memo'   => 备注
     *        ]
     *
     * @return string
     */
    public function xgsRecharge()
    {
        $money     = I('post.money');
        $money     = floatval(number_format($money, 2, '.', ''));
        $total_fee = $money * 100;
        $openid    = I('post.openid');
        $aid       = I('post.aid');
        $shopId    = I('post.shop_id');
        $did       = I('post.did', 0);
        $qrPay     = I('post.qr_pay', 0);
        $memo      = I('post.memo', '', 'trim');

        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $sid       = $loginInfo['sid'] ?? 0;

        $did = $did ? $did : $sid;
        if (!$did) {
            parent::apiReturn(401, [], '用户身份获取错误');
        }

        if ($qrPay == 0 && empty($openid)) {
            parent::apiReturn(401, [], 'OPENID为空');
        }

        if (!is_numeric($money) || $money < 0) {
            parent::apiReturn(401, [], '请输入大于0的金额，金额必须是数字');
        }

        $queryParams = [[$did, $shopId, $aid]];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
            $queryParams);

        if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
            parent::apiReturn(401, [], '用户信息获取失败，请稍后重试');
        }

        $memberInfo = array_column($queryRes['data'], null, 'id');

        //$modelMember = new Member();
        //$sellerName  = $modelMember->getMemberCacheById($did, 'dname');
        //$shopName    = $modelMember->getMemberCacheById($shopId, 'dname');
        //$bossName    = $modelMember->getMemberCacheById($aid, 'dname');

        $sellerName = $memberInfo[$did]['dname'] ?? '';
        $shopName   = $memberInfo[$shopId]['dname'] ?? '';
        $bossName   = $memberInfo[$aid]['dname'] ?? '';
        $body       = "[$sellerName]通过{$shopName}给{$bossName}充值{$money}元|{$did}|$shopId|$aid";

        if ($memo) {
            $body .= '|' . $memo;
        }
        $outTradeNo = time() . $did . mt_rand(1000, 9999);
    }

    /**
     * 仙盖山微信回调业务处理
     *
     * @date   2018-04-14
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    public function xgsNotify()
    {

    }

    /**
     * 生成支付订单号
     *
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @param  string  $ordernum
     *
     * @return string
     */
    private function filterOrderNum($ordernum)
    {
        return str_replace(['qr_'], '', $ordernum);
    }

    /**
     * 内网模拟发送短信
     *
     * <AUTHOR>
     * @date   2018-06-08
     *
     * @param  string  $ordernum
     *
     * @return null
     */
    private function _simuSendSms($ordernum)
    {
        $orderModel = new OrderTools('slave');
        $orderInfo  = $orderModel->getOrderInfo($ordernum);

        $jobId = Queue::push(
            'notify', 'OrderNotify_Job',
            [
                'ordernum' => $ordernum,
                'buyerId'  => $orderInfo['member'], //购买人的ID
                'mobile'   => $orderInfo['ordertel'],
                'aid'      => $orderInfo['aid'],
            ]
        );
        pft_log('alipay_pc/ok', "$ordernum|job_id=$jobId");
    }

    /**
     * 生成小程序支付码,应用于自助机场景--阿里云OSS存储
     *
     * <AUTHOR> Chen
     * @date   2018-08-09
     */
    public function getPayCodeByOrdernum()
    {
        $defaultAc  = ENV == 'PRODUCTION' ? '123624' : '100014';
        $ac         = I('post.ac', $defaultAc);
        $orderNum   = I('post.out_trade_no');
        $merchantId = I('post.merchant_id');
        $channel    = I('post.channel', 2); // 提交渠道，与追踪记录表的source字段一致。
        $scene      = $orderNum . ':' . $merchantId . ':' . $channel; // 最长32位
        $page       = I('post.page', 'pages/pay/pay');
        $width      = 450;
        $lib        = new WechatSmallApp();
        $res        = $lib->getWxCodeUnlimit($ac, $scene, $page, $width);
        $prefix     = substr($res['data'], 0, 1);
        if ($prefix == '{') {
            $decodeRes = json_decode($res['data'], true);
            $this->apiReturn(401, $res, $decodeRes['errmsg']);
        }
        $fileName = "wechat_miniapp_code/{$orderNum}.png";
        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();
        $baseConfig = C('oss');
        $config     = $baseConfig['aliyun']['senic'];
        $endpoint   = ENV == 'PRODUCTION' ? $config['endpoint']['prod'] : $config['endpoint']['dev'];
        $ret        = ['outTradeNo' => $orderNum];
        try {
            $ossClient = new OssClient($config['accessId'], $config['accessKeySecret'], $endpoint, false);
            $res       = $ossClient->putObject($config['bucket'], $fileName, $res['data']);
            if ($res['info']['http_code'] == 200) {
                $code       = 200;
                $ret['url'] = $config['domain'] . '/' . $fileName . '?x-oss-process=style/image_shuiyin';
            } else {
                $code = 400;
            }
        } catch (OssException $exception) {
            $code = 400;
        }
        parent::apiReturn($code, $ret);
    }

    /**
     * 易宝代付回调通知
     * https://pay.12301.cc/r/pay_CBYeePay/transferNotify
     * @author: guanpeng
     * @date: 2019/6/4
     */
    public function transferNotify()
    {
        $client       = new \Library\Tools\YarClient('pay');
        $response     = $_POST['response'];
        $params       = ['data' => $response, 'dfFlag' => true];
        if ($_GET['customer_no']) {
            $params['customer_no'] = I('get.customer_no');
        }
        $responseData = $client->call('Pay/YeePayCloudBusiness/notifyDecode', $params);
        $logData      = json_encode(
            [
                'get'   => $_GET,
                'reqid' => $_SERVER['REQUEST_ID'],
                'params' => $params,
                'key'   => '易宝回调',
                'raw'   => $_POST['response'],
                'data'  => $responseData,
            ], JSON_UNESCAPED_UNICODE
        );
        pft_log('yeepay/transfer_notify', $logData, 'day');
        if ($responseData['code'] !== 200) {
            exit('fail');
        }

        //batchNo   批次号 String
        //orderId   订单号 String
        //transferStatusCode    打款状态码   String  详见：返回码说明-打款状态码
        //bankTrxStatusCode 银行状态码   String  详见：返回码说明-银行处理状态
        //successAmount 成功金额    String  拆分订单时使用
        //finishDate    完成时间    String  拆分订单时使用
        //failAmount    失败金额    String  拆分订单时使用
        //refundAmount  退款金额    String  拆分订单时使用

        $payResult = json_decode($responseData['res']['data'], true);
        if (strpos($payResult['orderId'], '_') !== false) {
            [$batchNo, $pftOrderId] = explode('_', $payResult['orderId']);
        } else {
            $pftOrderId = $payResult['orderId'];
        }
        if ($payResult['bankTrxStatusCode'] == 'S') {
            //$model = new Withdraws();
            //$model->feedbackSuccess($pftOrderId, $payResult['batchNo']);
            $pftOrderId = intval($pftOrderId);
            $batchNo    = strval($payResult['batchNo']);

            $withdrawLib = new \Business\Finance\Withdraw();
            $res         = $withdrawLib->feedbackSuccess($pftOrderId, $batchNo, 0);

        } elseif ($payResult['bankTrxStatusCode'] == 'F') {
            //$model = new Withdraws();
            //$model->feedbackFail($pftOrderId, "提现失败:" . $payResult['bankMsg']);
            $pftOrderId = intval($pftOrderId);
            $bankMsg    = "提现失败:" . $payResult['bankMsg'];

            $withdrawLib = new \Business\Finance\Withdraw();
            $res         = $withdrawLib->feedbackFail($pftOrderId, $bankMsg, 0);

            // 发送短信通知
            //$info   = $model->getWithdrawInfo($pftOrderId);
            //$fid    = $info['fid'];

            $withdrawQuery = new WithdrawQuery();
            $queryRes      = $withdrawQuery->queryWdCashInfoById($pftOrderId);
            if ($queryRes['code'] != 200 || !$queryRes['data']) {
                $logInfo = json_encode([
                    'key'         => '提现失败通知 - 提现记录获取失败',
                    'withdraw_id' => $pftOrderId,
                ]);
                pft_log('yeepy/transfer_notify', $logInfo);
            } else {
                //提现信息
                $withdrawInfo = $queryRes['data'];
                $fid          = $withdrawInfo['fid'];

                $queryParams = [[$fid]];
                $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                    $queryParams);

                $mobile = '';
                if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                    $mobile = array_column($queryRes['data'], null, 'id')[$fid]['mobile'];
                }

                $msg = "您有一笔提现资金失败了，流水号:{$pftOrderId},";
                if (!empty($payResult['bankMsg'])) {
                    $msg .= "失败原因:{$payResult['bankMsg']}";
                } else {
                    $msg .= "银行未返回失败原因";
                }
                $msg .= ",如有疑问请联系客服人员";
                if (Helpers::isMobile($mobile)) {
                    $cache  = Cache::getInstance('redis');
                    $lockey = "yptransfer:{$pftOrderId}";
                    $res    = $cache->lock($lockey, 1, 86400);
                    if (!$res) {
                        echo 'SUCCESS';

                        return false;
                    }
                    $jobId   = \Library\Resque\Queue::push('notify', 'SmsNotify_Job', [
                        'action' => 'customMsg',
                        'mobile' => $mobile,
                        'params' => [
                            'memberid'   => $fid,
                            'smsSign'    => '票付通',
                            'msg'        => $msg,
                            'mobile'     => $mobile,
                            'callback'   => '',
                            'extend'     => '',
                            'isCharging' => true,
                            'remark'     => '提现失败',
                        ],
                    ]);
                    $dingMsg = "批次号:{$payResult['batchNo']}\n" .
                               "订单号:{$payResult['orderId']}\n" .
                               "打款状态码:{$payResult['transferStatusCode']}\n" .
                               "银行状态码:{$payResult['bankTrxStatusCode']}\n" .
                               "成功金额:{$payResult['successAmount']}\n" .
                               "完成时间:{$payResult['finishDate']}\n" .
                               "退款金额:{$payResult['refundAmount']}\n" .
                               "错误描述:{$payResult['bankMsg']}\n";
                    file_get_contents('http://tool.pft12301.com/webhook/1013/send?title='
                                      .urlencode('🏦易宝代付结果异步通知🏦').'&desp='.urlencode($dingMsg));
                    // Helpers::sendDingTalkGroupRobotMessageRaw($dingMsg, DingTalkRobots::YEEPAY_ROBOT);
                }

                $logInfo = json_encode([
                    'key'    => '提现失败通知',
                    'fid'    => $fid,
                    'msg'    => $msg,
                    'mobile' => $mobile,
                    'jobId'  => $jobId,
                ]);
                pft_log('yeepy/transfer_notify', $logInfo);
            }
        }

        echo 'SUCCESS';
    }

    /**
     * 非平台订单支付
     * <AUTHOR>
     * @date   2018-07-28
     */
    public function specialOrderPay()
    {
        $logData = [
            'post'  => $_POST,
            'refer' => $_SERVER['HTTP_REFERER'],
            'agent' => $_SERVER['HTTP_USER_AGENT'],
            'reqid' => $_SERVER['REQUEST_ID'],
        ];
        pft_log('yeepay/special_req', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

        //平台订单号
        $outTradeNo       = I('out_trade_no', 0, 'string');
        $specialOrderMode = I('special_mode', 0);
        //是否二维码支付
        $qrPay = I('is_qr', 0, 'intval');
        //微信用户openid
        $openid = I('openid', null);
        // 公众号对应的实际openid——独立收款商户
        $subAppid   = I('post.sub_appid', null);
        $subOpendid = I('post.sub_openid', null);
        if (!is_null($subAppid) && $subOpendid) {
            $openid = $subOpendid;
        }
        if (ENV == 'TEST') {
            // dev环境，appid必须使用票付通这个公众号的APPID，openid需要自己获取后填在这里。
            $openid      = 'oNbmEuDdAEWDS_a02HYFlzNYFUTg';
            $this->appid = 'wxd72be21f7455640d';
        }
        //订单主体说明
        $subject = mb_substr(trim(I('subject')), 0, 20, 'utf-8') . "[{$outTradeNo}]";
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }

        if (!$specialOrderMode) {
            parent::apiReturn(401, [], '业务类型错误');
        }

        $specialOrderBiz = new SpecialOrderPay();
        $moneyRes        = $specialOrderBiz->specialOrderPayCheck($outTradeNo, $specialOrderMode);
        if ($moneyRes['code'] != 200) {
            parent::apiReturn($moneyRes['code'], $moneyRes['data'], $moneyRes['msg']);
        }
        $orderNotifyUrl = $specialOrderBiz->chooseOrderNotify($specialOrderMode, 'cbyee');
        if (!$orderNotifyUrl) {
            parent::apiReturn(401, [], '回调地址有误');
        }
        $money      = $moneyRes['data'];
        $money      = number_format($money / 100, 2, '.', '');
        $frontUrl   = I('post.success_url', $_SERVER['HTTP_REFERER']);
        $goodsName  = "支付订单[{$outTradeNo}]";
        $callMethod = 'wxGzh';

        //模拟支付
        if (ENV == 'DEVELOP' || ENV == 'LOCAL') {
            $transaction_id = 'special' . $outTradeNo;
            $pay_to_pft     = true;
            $payMethod      = OnlineTrade::PAY_METHOD_ORDER;
            //生成支付记录
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog(
                $outTradeNo, $money, $subject, $subject, self::SOURCE_T,
                $payMethod, '', $this->merchantId
            );
            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }
            $specialOrderBiz = new SpecialOrderPay();
            $result          = $specialOrderBiz->specialOrderHasPayAfter($outTradeNo, $transaction_id,
                self::TRACK_SOURCE, $money * 100, [], [], $pay_to_pft);
            echo json_encode([
                'code'   => 200,
                'data'   => json_encode($result),
                'status' => 'ok',
                'msg'    => 'success',
            ]);
        } else {
            if ($qrPay) {
                $callMethod = 'qrPayWechat';
            }
            $data       = $this->payRpcRequest(
                $outTradeNo, $money, $orderNotifyUrl, $this->appid, $openid,
                $goodsName, $frontUrl, $callMethod
            );
            $payMethod  = OnlineTrade::PAY_METHOD_ORDER;
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog(
                $outTradeNo, $money, $subject, $subject, self::SOURCE_T,
                $payMethod, '', $this->merchantId
            );

            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }
            $specialOrderBiz->afterPayHandle($specialOrderMode);
            echo json_encode([
                'code'   => 200,
                'data'   => $data['parameter'],
                'status' => 'ok',
                'msg'    => 'success',
            ]);
            exit;
        }
    }

    /**
     * 非平台订单支付回调
     * <AUTHOR>
     * @date   2018-07-28
     * @return array
     */
    public function specialNotify()
    {
        if (empty($payResult)) {
            $payResult = $this->decodeNotifyData();
        }
        $outTradeNo     = $payResult['customerRequestNo'];
        $transaction_id = $payResult['trade_no']; //交易号
        $totalFee       = $payResult['amount'] * 100; //金额用分为单位
        $json_buy       = $payResult['buy_info'];
        $json_sell      = $payResult['sell_info'];

        if (!PayBase::setSpecialLock($outTradeNo)) {
            echo 'fail';

            return ['code' => 204];
        }
        // 更新缓存状态为已支付
        PayBase::setOnlinePayState($outTradeNo, self::TRACK_SOURCE, 1);
        $pay_to_pft      = true;
        $specialOrderBiz = new SpecialOrderPay();
        $result          = $specialOrderBiz->specialOrderHasPayAfter($outTradeNo, $transaction_id, self::TRACK_SOURCE,
            $totalFee, $json_buy, $json_sell, $pay_to_pft);
        pft_log('member/system/info', '返回详情：' . $outTradeNo . json_encode($result));
        if ($result['code'] == 10) {   //预留退款的
            pft_log('member/system/refund', '订单退款：' . $outTradeNo);
            $specialOrderBiz->specialOnlineRefund($outTradeNo, $transaction_id, self::SOURCE_T, $json_sell, $json_buy,
                $pay_to_pft, $totalFee, '会员加入失败');
        }
        echo 'success';

        return ['code' => 200];
    }

    /**
     * 通用扫码的回调触发接口
     * <AUTHOR>
     * @date   2020-11-18
     * @return array
     */
    public function qrCommonNotifyTouch()
    {
        if (ENV == 'PRODUCTION') {
            parent::apiReturn(401, [], '禁止推送');
        }
        $orderNum = I('post.order_id', '');
        if (!$orderNum) {
            parent::apiReturn(401, [], '订单号缺失');
        }
        $code = 200;
        $msg  = '推送成功';
        try {
            $tradeLogModel = new OnlineTrade();
            $payCenterBiz  = new PayCenter();
            // 获取支付记录
            $payLogArr = $tradeLogModel->getLog($orderNum, self::SOURCE_T);
            if (empty($payLogArr)) {
                throw new \Exception('未找到支付记录');
            }
            $payCenterBiz->setNotifyData($orderNum, rand(1, 9999999), $payLogArr['total_fee'] * 100,
                $payCenterBiz::STATUS_SUCCESS, self::SOURCE_T);
            $res = $payCenterBiz->notify();
            if ($res['code'] != 200) {
                throw new \Exception($res['msg']);
            }
        } catch (\Exception $e) {
            $code = 401;
            $msg  = $e->getMessage();
        }
        parent::apiReturn($code, [], $msg);
    }


    /**
     * 易宝商户入网回调
     * User: lanwanhui
     * Date: 2021/8/10
     *
     * @return
     */
    public function merchatNotify()
    {
        pft_log('yeep_register_notify',$_POST['response']);

        $source = trim($_POST['response']);
        if (empty($source)) {
            return;
        }

        $client   = new \Library\Tools\YarClient('pay');
        $res      = $client->call('Merchant/YeePayMerchant/merchatNotify', [$source]);

        if ($res['code'] == 200) {
            echo $res['res'];
        }

    }

    
    /**
     * 易宝商户修改产品费率回调
     * User: lanwanhui
     * Date: 2021/5/10
     *
     * @return
     */
    public function productNotify()
    {
        pft_log('yeep_product_notify',$_POST['response']);

        $source = trim($_POST['response']);
        if (empty($source)) {
            return;
        }

        $client   = new \Library\Tools\YarClient('pay');
        $res      = $client->call('Merchant/YeePayMerchant/productNotify', [$source]);

        if ($res['code'] == 200) {
            echo $res['res'];
        }

    }
}
