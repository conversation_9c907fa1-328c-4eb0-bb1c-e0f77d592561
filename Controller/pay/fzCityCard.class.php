<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: chenguangpeng
 * Date: 1/16-016
 * Time: 9:26
 */

namespace Controller\pay;

use Business\Member\Session;
use Business\Order\FzCityCard as FzCityCardBiz;
use Library\Business\CityCard\FzSdk;
use Library\Controller;
use Library\Tools\PftException;
use Library\Tools\Vcode;
use Model\Order\OrderQuery;
use Model\TradeRecord\OnlineTrade;

define('IN_PFT', true);

class fzCityCard extends Controller
{
    private $business;

    public function __construct()
    {
        $this->business = new FzCityCardBiz();
    }

    /**
     * 授权回调通知地址
     * 判断是否有再系统中注册了
     */
    public function authCallBack()
    {
        $code = I('code');
        /** @var string 这里使用code的md5作为session */
        $result = $this->getUserInfo($code);
        pft_log("fzcitycard/oauth", "获取userInfo:" . json_encode($result));
        $fzSdk      = new FzSdk();
        $session_id = $result['refreshToken'];
        if ($result) {
            $queryData = [
                "name"       => $result['data']['certName'],
                "certNo"     => $result['data']['certNo'],
                "mobile"     => $result['data']['mobile'],
                "session_id" => $session_id,
                "appid"      => $fzSdk->getConfig()['appId'],
                "toonNo"     => $result['data']['toonNo'],
                "appname"    => '测试',
                "memberId"   => $result['memberId'],
            ];
            if (ENV == 'PRODUCTION') {
                $url = "http://my.12301.cc/r/pay_fzCityCard/setSession?" . http_build_query($queryData);
            } else {
                $url = "http://my.12301dev.com/r/pay_fzCityCard/setSession?" . http_build_query($queryData);
            }
            header("Location:$url");
            die;
        } else {
            $this->apiReturn(500, [], "用户信息获取失败!");
            die;
        }
    }

    private function autoLogin($memberId)
    {
        $session            = new Session();
        $loginStatus        = $session->loginMemberId($memberId);
        $_SESSION['source'] = 'platform';
    }

    public function setSession()
    {
        $data = I("get.");
        $this->autoLogin($data['memberId']);
        if (ENV == 'PRODUCTION') {
            $url = "http://124472.12301.cc/wx/c/efuzhou_index.html#/efuzhou_index?" . http_build_query($data);
        } else {
            $url = "http://123624.12301dev.com/wx/c/efuzhou_index.html#/efuzhou_index?" . http_build_query($data);
        }
        header("Location:$url");
        die;
    }

    public function payResultCheck()
    {
        //$api = new Api\V1\cityCard();
        //$api->payResultCheck(true);
    }

    /**
     * 如果受理端接口调不通，会尝试8次
     * 这里通过支付接口 返回的 returnPara 带回来数据计入到 pft_citycard_orders
     */
    public function notifyTrade()
    {
        pft_log('fzcitycard/notify/data', "sign={$_POST['sign']}&data={$_POST['data']}&raw=" . json_encode($_POST));
        $sign     = $_POST['sign'];
        $jsonData = $_POST['data'];
        $jsonArr  = json_decode($jsonData, true);
        $sdk      = new FzSdk();
        $mySign   = $sdk->verify($_POST['data'], $sign);
        if (!$mySign) {
            pft_log('fzcitycard/notify/error', "{$jsonArr['payOrderNo']} 签名错误");
            exit("success");
        }
        $tranState = $jsonArr['tranStatus'];
        if ($tranState != 2) {
            exit("success");
        }
        $outTradeNo = $jsonArr['payOrderNo'];
        if (strstr($outTradeNo, '-')) {
            $outTradeNo = explode('-', $outTradeNo)[0];
        }
        $tradeNo    = $jsonArr['platOrderNo'];
        $payOrgId   = $jsonArr['payOrgId'];
        $json_buy   = "";
        $payChannel = 33;
        $this->business->setAlreadyPay($outTradeNo); //设置订单为支付状态
        $OrderModel = new \Model\Order\OrderTools('slave');
        $order_info = $OrderModel->getOrderInfo($outTradeNo);
        try {
            //统一调用open服务器服务
            $res = parent::getSoap()->Change_Order_Pay($outTradeNo, $tradeNo, OnlineTrade::CHANNEL_CITYCARD,
                $jsonArr['tranAmt'], 0, $payOrgId, $json_buy, 1, false, $payChannel);
        } catch (\SoapFault $e) {
            $retry = true;
            pft_log('fzcitycard/notify/error',
                "(福州市民卡)订单支付失败:{$outTradeNo}，soap抛出异常:{$e->getCode()}->{$e->getMessage()}");
        }
        pft_log('fzcitycard/notify/result', "$outTradeNo:" . var_export($res, true));
        if ((isset($retry) && $retry === true) || !$res) {
            $res = parent::getSoap()->Change_Order_Pay($outTradeNo, $tradeNo, OnlineTrade::CHANNEL_CITYCARD,
                $jsonArr['tranAmt'], 0, $payOrgId, $json_buy, 1, false, $payChannel);
        }
        if ($res === 100) {
            $response         = ['respCode' => '01', 'respMsg' => '处理成功', 'data' => ''];
            $sign             = $sdk->sign($response);
            $response['sign'] = $sign;
            $job_id = \Library\Resque\Queue::push('notify', 'OrderNotify_Job',
                [
                    'ordernum' => $outTradeNo,
                    'buyerId'  => $order_info['member'],//购买人的ID
                    'mobile'   => $order_info['ordertel'],
                    'aid'      => $order_info['aid'],
                ]
            );
            pft_log('fzcitycard/notify/result', "订单号[$outTradeNo],job_id=$job_id");
            exit('success');
            // echo json_encode($response);
        } else {
            exit("success");
        }
    }

    public function notifyRefund()
    {
        $data = file_get_contents('php://input');
    }

    /**
     *
     *  平安银行借记卡：6216261000000000018
     * 手机号：13552535506
     * 证件类型：01
     * 证件号：341126197709218366
     * 密码：123456
     * 姓名：全渠道
     * 短信验证码：123456（手机）/111111（PC）（先点获取验证码之后再输入）
     * 绑定授权信息 表单数据
     * <AUTHOR>
     * @dateTime 2018-01-19T17:57:03+0800
     */
    public function getAuthorization()
    {
        $phoneNo = I("phoneNo");
        $gcode   = I('gcode'); //图形验证码
        $vcode   = I('code');
        if ($gcode != $_SESSION["auth_code"]) {
            $this->apiReturn(500, [], "验证码错误!");
        }
        $vcode  = new Vcode();
        $result = $vcode->verifyVcode($phoneNo, $vcode, "fzcitycard");
        if ($result['code'] != 200) {
            $this->apiReturn(500, [], "手机验证码错误,请重新发送!");
        }
        $registStatus = $this->business->createUserByPhoneNo($phoneNo);
        $fzSdk        = new FzSdk();
        $ret          = $fzSdk->auth($phoneNo, $registStatus);
        echo $this->buildRequestForm($ret, 'post', '提交');
        die;
    }

    public function sendVcode()
    {
        $phoneNo = I("phoneNo");
        $vcode   = new Vcode();
        $vcode->sendVcode($phoneNo, 'register', "fzcitycard");
        $this->apiReturn(500, [], "验证码发送成功!");
    }

    /**
     * h5页面支付
     * <AUTHOR>
     * @dateTime 2018-01-23T10:03:52+0800
     * @throws   \Exception                             可能抛出异常
     */
    public function h5pay()
    {
        $memberId = $this->isLogin('auto', false);
        if (!$memberId) {
            echo "<h1>授权失效</h1>";
            die;
        }
        /** @var string sessionid 也是 refreshToken */
        $sessionId = I('session_id');
        /** @var string 用户参数 */
        $toonNo = I("toonNo");
        /** @var string 订单号 */
        $orderNum = I('order_num');
        /** @var int 票的id */
        $ticketId = I("ticket_id");
        /** @var int 产品id */
        $pid = I("pid");

        $supplyIds = I("supplyIds", '123917,123918.123624');

        $account = $this->business->getAccountByPid($pid, $supplyIds);
        pft_log("lerko", json_encode($account));

        /** @var string 用户授权码 */
        $userOAuthCode = $this->business->getUserAuthCode($sessionId, $toonNo);
        if (ENV == 'TEST' && !$userOAuthCode) {
            $userOAuthCode = '****************************************************************************************************************************************************';
        }
        pft_log('fzcitycard/oauth', "userOAuthCode:" . $userOAuthCode);
        /** 保存订单关系 */
        $efzUserInfo  = $this->business->getOAuthInfoByMemberId($memberId);
        $userUniqueId = $efzUserInfo['cert_no'];
        $ticketIds    = explode(',', $ticketId);
        $this->business->saveFzCityCardOrderBatch($ticketIds, $userUniqueId, $orderNum);
        $fzConfig   = load_config(ENV, "fz_citycard");
        $notifyUrl  = $fzConfig['payNotifyUrl'];
        $OrderQeury = new OrderQuery('localhost');
        $price      = $OrderQeury->get_order_total_fee((string)$orderNum);
        $orderTime  = $this->business->getOrderTime($orderNum);
        if ($price == 0) {
            header("Location:http://$account.12301dev.com/wx/c/efuzhou_ordersuccess.html?order_num=********###");
            die;
        }
        try {
            $fzSdk = new FzSdk();
            pft_log("fzcitycard/pay", "购买的账户:" . $account);
            $ret = $fzSdk->h5pay($orderNum, $orderTime, $price, $notifyUrl, $userOAuthCode, $account);
        } catch (PftException $e) {
            echo "<h1>{$e->getMessage()}</h1>";
            die;
        }
        pft_log("fzcitycard/pay", "pay参数:" . json_encode($ret));
        $this->business->addLog($orderNum, $price / 100, "", "$userUniqueId  购买了:$ticketId");
        echo $this->buildRequestForm($ret, 'post', '提交');
        die;
    }

    /**
     * 建立请求，以表单HTML形式构造（默认）
     *
     * @param  array  $para_temp  请求参数数组
     * @param  string  $method  提交方式。两个值可选：post、get
     * @param  string  $button_name  确认按钮显示文字
     *
     * @return string 提交表单HTML文本
     */
    public function buildRequestForm($para_temp, $method, $button_name)
    {
        if (ENV == "TEST") {
            $url = 'http://testbs.ggjfw.com/openapi/gateway';
        } else {
            $url = 'https://jf.fuzhou.gov.cn:9600/openapi/gateway';
        }
        if (ENV == 'PRODUCTION') {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HEADER, 0);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($para_temp));
            curl_exec($ch);
        } else {
            $sHtml = "<form id='alipaysubmit' name='alipaysubmit' action='$url' charset='UTF8' method='" . $method . "'>";
            foreach ($para_temp as $key => $val) {
                $sHtml .= "<input type='textarea' name='" . $key . "' value='" . $val . "'/></br>";
            }
            $sHtml = $sHtml . "页面正在跳转中……如果长时间没有跳转，请点击<input type='submit' value='" . $button_name . "'></form>";

            // $sHtml = $sHtml."<script>document.forms['alipaysubmit'].submit();</script>";
            return $sHtml;
        }
    }

    /**
     * e福州用户授权
     * <AUTHOR>
     * @dateTime 2018-01-25T13:50:18+0800
     */
    private function getUserInfo($code)
    {
        $fzSdk = new FzSdk();
        $data  = $fzSdk->getOAuthAccessToken($code, 1);
        if ($data !== false) {
            $userInfo = $fzSdk->getUserInfo($data['accessToken']);
            \pft_log("fzcitycard/oauth", "userInfo:" . json_encode($userInfo, true));
            if ($userInfo === false) {
                return false;
            }
            /** @var int 创建或者更新用户信息并且获取用户信息返回用户id */
            $memberId = $this->business->createUserByPhoneNo($userInfo['data']['mobile']);
            \pft_log("fzcitycard/oauth", "memberId:" . $memberId);
            /** 保存用户信息到数据库 */
            $this->business->saveUserInfo($memberId, $userInfo['data']);
            $userInfo['refreshToken'] = $data['refreshToken'];
            $userInfo['memberId']     = $memberId;

            return $userInfo;
        } else {
            return false;
        }
    }

    /**
     * 授权接口 跳转到授权界面
     * <AUTHOR>
     * @dateTime 2018-01-25T16:31:32+0800
     */
    public function authorization()
    {
        $fzSdk = new FzSdk();
        $fzSdk->getOAuthCode(PAY_DOMAIN . 'r/pay_fzCityCard/authCallBack');
    }

    /**
     * 获取用户数据通过SessionId
     * <AUTHOR>
     * @dateTime 2018-01-25T16:20:48+0800
     *
     * @param    [type]                   $sessionId 放置给前端的sessionId
     *
     * @return   [type]                              [description]
     */
    public function getUserInfoBySessionId()
    {
        $sessionId = I("session_id");
        if ($sessionId) {
            $this->apiReturn(500, [], "参数错误!");
        }
        $fzSdk = new FzSdk();
        $ret   = $fzSdk->getCacheInfoBySessionId($sessionId);
        if ($ret === false) {
            $info = $fzSdk->getOAuthAccessToken($sessionId, 2);
            if ($info !== false) {
                $ret = $info;
            } else {
                $this->apiReturn(500, $userInfo, "刷新accessToken失败!");
            }
        }
        $userInfo = $fzSdk->getUserInfo($ret['accessToken']);
        /** @var int 创建或者更新用户信息并且获取用户信息返回用户id */
        $memberId = $this->business->createUserByPhoneNo($userInfo['data']['mobile']);
        /** 保存用户信息到数据库 */
        $this->business->saveUserInfo($memberId, $userInfo['data']);
        $this->autoLogin($memberId);
        $this->apiReturn(200, $userInfo, "用户信息获取成功!");
    }

}
