<?php
/*
 * User: lanwan<PERSON>
 * Date: 2021/7/12
 */
namespace Controller\pay;

use Library\Controller;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\TradeRecord\OnlineTrade;

class UnionPayApplet extends Controller
{
    const TRACK_SOURCE = 56;
    const SOURCE_T = 26;
    private $reqId;     //请求唯一标识

    public function __construct()
    {
        $this->reqId      = $_SERVER['REQUEST_ID'] ?: uniqid();
    }

    /**
     * 查询会员卡信息
     * User: lanwanhui
     * Date: 2021/7/17
     */
    public function queryCard()
    {
        $mobile           = I('post.mobile');                                    //手机号
        $merchantId       = I('post.merchant_id', 0, 'intval');    //供应商id

        if (!$mobile || !$merchantId) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或供应商id必须传');
        }

        //获取配置
        $payConfig      = load_config('unionpayapplet_pay','pay');
        if (!isset($payConfig[$merchantId]) || empty($payConfig[$merchantId])) {
            $this->apiReturn(204, [], "配置不存在");
        }

        //商户标识
        $mchntIdentify = $payConfig[$merchantId];

        //查询卡信息
        $queryCardRs = $this->_queryCardInfo($mobile, $mchntIdentify);

        if ($queryCardRs[0] != 200) {
            $this->apiReturn(204, [], $queryCardRs[2]);
        }

        //门店标识
        $shopIdentify = $queryCardRs[1]['shopIdentify'];
        if (empty($shopIdentify)) {
            $this->apiReturn(204, [], "获取门店信息失败");
        }

        $memCardNo = $queryCardRs[1]['memCardNo'];
        if (empty($memCardNo)) {
            $this->apiReturn(204, [], "获取卡号失败");
        }

        $this->apiReturn(200,  $queryCardRs[1], "获取卡信息成功");

    }


    /**
     * 支付
     * User: lanwanhui
     * Date: 2021/7/17
     */
    public function pay()
    {
        $logData = [
            'post'  => $_POST,
            'refer' => $_SERVER['HTTP_REFERER'],
            'agent' => $_SERVER['HTTP_USER_AGENT'],
            'reqid' => $this->reqId,
        ];

        pft_log('unionpayapplet/pay', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

        $outTradeNo       = I('post.ordernum'); //订单号
        $mobile           = I('post.mobile');//手机号
        $merchantId       = I('post.merchant_id', 0, 'intval');//供应商id
        $subject          = I('post.subject', '订单支付');  //订单备注
        $payTerminal      = I('post.terminal', 0, 'intval'); //支付的终端
        $payTrackOpId     = I('post.pay_track_op_id', 0, 'intval');//支付的操作人

        if (!$outTradeNo || !$mobile || !$merchantId) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或手机号或供应商id必传');
        }

        $paymentObj = new \Business\Order\MergeOrder();
        if ($paymentObj->isCombineOrder($outTradeNo)) {
            $paymentObj->handlerCombinePayLog($outTradeNo, $subject, self::SOURCE_T, $merchantId);
        }

        //订单支付
        $orderQuery = new OrderQuery('localhost');
        $totalFee   = $orderQuery->get_order_total_fee($outTradeNo);
        if ($totalFee <= 0) {
            $this->apiReturn(204, [], '订单金额有误');
        }

        $OnlineTrade = new OnlineTrade();
        $result      = $OnlineTrade->addLog(
            $outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_ORDER, '', $merchantId);

        if (!$result) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }

        //调用条码支付接口
        $transactionId = $this->_pay($merchantId, $mobile, $outTradeNo, $totalFee);

        $options = [
            'buyer_info'   => '',
            'sell_info'    => '',
            'pay_channel'  => self::TRACK_SOURCE,
            'pay_termianl' => $payTerminal,
            'oper'         => $payTrackOpId,
        ];

        $result = \Library\Tools\Helpers::payComplete($outTradeNo, 'unionapplet'.$outTradeNo, OnlineTrade::CHANNEL_UNIONPAYAPPLET,
            $totalFee, 0, $options);

        $data = [];
        if ($result['code'] == 200) {
            $successOrders = $result['data']['success_orders'];
            foreach ($successOrders as $item) {
                $data[] = [
                    'code'     => 200,
                    'msg'      => '支付成功',
                    'order_id' => $item,
                ];
            }
        }

        $this->apiReturn($result['code'], $data, $result['msg']);
    }


    /**
     * 消费
     * User: lanwanhui
     * Date: 2021/7/13
     *
     * @param int    $merchantId  供应商号
     * @param string $mobile      手机号
     * @param string $outTradeNo  订单号
     * @param int    $totalFee    订单金额
     *
     * @return string
     */
    private function _pay($merchantId, $mobile, $outTradeNo, $totalFee)
    {
        //获取配置
        $payConfig      = load_config('unionpayapplet_pay','pay');
        if (!isset($payConfig[$merchantId]) || empty($payConfig[$merchantId])) {
            parent::apiReturn(204, [], "配置不存在");
        }

        //商户标识
        $mchntIdentify = $payConfig[$merchantId];

        //查询卡信息
        $queryCardRs = $this->_queryCardInfo($mobile, $mchntIdentify);
        if ($queryCardRs[0] != 200) {
            parent::apiReturn(204, [], $queryCardRs[2]);
        }

        //门店标识
        $shopIdentify = $queryCardRs[1]['shopIdentify'];
        if (empty($shopIdentify)) {
            parent::apiReturn(204, [], "获取门店信息失败");
        }

        $memCardNo = $queryCardRs[1]['memCardNo'];
        if (empty($memCardNo)) {
            parent::apiReturn(204, [], "获取门卡号信息失败");
        }

        //消费
        $consumeRs = $this->_consume($memCardNo, $shopIdentify, $outTradeNo, $totalFee);

        //成功
        if ($consumeRs[0] == 200) {
            return '';
        }

        //失败
        if ($consumeRs[0] == 204) {
            parent::apiReturn(204, [], $consumeRs[2]);
        }

        //其他返回码，不知道是否成功，查询一次
        $tradeRs = $this->_queryTrade($outTradeNo);
        if ($tradeRs[0] == 200) {
            return '';
        } else {
            parent::apiReturn(204, [], '消费失败');
        }

    }


    /**
     * 查询卡号信息
     * User: lanwanhui
     * Date: 2021/7/13
     *
     * @param string $mobile        手机号
     * @param string $mchntIdentify 商户标识
     *
     * @return array
     */
    private function _queryCardInfo($mobile, $mchntIdentify)
    {
        try {

            $requestData = [
                //卡号
                'memCardNo'     => '',
                // 手机号
                'mobileNo'      => $mobile,
                // 商户标识
                'mchntIdentify' => $mchntIdentify,
            ];

            $lib = new \Library\JsonRpc\PftRpcClient('ota_supplier_api');

            $queryRes = $lib->call('OtherSystemApi/UnionPayMini/getMember', [$requestData], 'plat');

            pft_log('unionpayapplet/querycard', json_encode($queryRes, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

            if (!isset($queryRes['code']) || $queryRes['code'] != 200) {
                $msg = empty($queryRes['msg']) ? '获取卡信息失败' : $queryRes['msg'];
                return [204, [], $msg];
            }

            $returnData = [
                'memCardNo'      => empty($queryRes['data']['memCardNo']) ? '' : $queryRes['data']['memCardNo'],
                'shopIdentify'   => empty($queryRes['data']['shopIdentify']) ? '' : $queryRes['data']['shopIdentify'],
                'memCardBalance' => empty($queryRes['data']['memCardBalance']) ?  0 : $queryRes['data']['memCardBalance'],
            ];

            return [200, $returnData, ''];

        } catch (\Exception $e) {

            $logData = [
                'e_msg'  => $e->getMessage(),
                'e_line' => $e->getLine(),
                'e_file' => $e->getFile(),
                'mobile' => $mobile,
            ];

            pft_log('unionpayapplet/querycard', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

            return [204, [], '获取卡信息失败'];

        }

    }


    /**
     * 消费接口
     * User: lanwanhui
     * Date: 2021/7/13
     *
     * @param string $memCardNo    卡号
     * @param string $shopIdentify 门店标识
     * @param string $outTradeNo   订单号
     * @param int    $totalFee     交易金额
     *
     * @return array
     */
    private function _consume($memCardNo, $shopIdentify, $outTradeNo, $totalFee){

        try {

            $requestData = [
                // 门店标识  查询接口获取
                'shopIdentify'       => $shopIdentify,
                // 交易时间 yyyyMMddHHmmss
                'tranTime'           => date('YmdHis'),
                // 第三方支付订单号
                'thirdPartyOrderNo'  => $outTradeNo,
                // 原始金额 (单位: 分)
                'originalAmount'     => $totalFee,
                //优惠金额
                'discountAmount'     => '0',
                // 实付金额 (单位: 分)
                'realAmount'         => $totalFee,
                //实收明细
                'reamtArray'         => [
                    [
                        // 实付金额 (单位: 分)
                        'realAmount'      => $totalFee,
                        // 支付方式 (06: 会员卡)
                        'payType'         => '06',
                    ],
                ],
                'discountArray'      => [
                    [
                        // 优惠类型 (01: 会员卡)
                        'discountType'     => '01',
                        // 优惠金额
                        'discountAmount'   =>  0,
                    ],
                ],
                // 会员卡号 (查询会员卡信息接口获取)
                'memCardNo'         => $memCardNo,
            ];

            $lib = new \Library\JsonRpc\PftRpcClient('ota_supplier_api');

            $consumeRes = $lib->call('OtherSystemApi/UnionPayMini/consume', [$requestData], 'plat');

            pft_log('unionpayapplet/consume', json_encode($consumeRes, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

            //RPC那边请求上游可能超时了，调用查询接口查一下
            if (isset($consumeRes['code']) && $consumeRes['code'] == 500) {
                return [500, [], ''];
            }

            //RPC那边请求上游失败了 直接返回失败
            if (!isset($consumeRes['code']) || $consumeRes['code'] != 200) {
                $msg = empty($consumeRes['msg']) ? '请求消费接口失败' : $consumeRes['msg'];
                return [204, [], $msg];
            }

            //接口请求成功
            if (isset($consumeRes['data']['respCode']) && $consumeRes['data']['respCode'] === '0000') {
                return [200, [], '成功'];
            } else {
                $msg = empty($consumeRes['data']['respDesc']) ? '请求消费接口失败了' : $consumeRes['data']['respDesc'];
                return [204, [], $msg];
            }

        } catch (\Exception $e) {

            $logData = [
                'e_msg'=>$e->getMessage(),
                'e_line'=>$e->getLine(),
                'e_file'=>$e->getFile(),
                'outTradeNo'=>$outTradeNo,
            ];

            pft_log('unionpayapplet/consume', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

            return [500, [], '请求消费接口失败'];

        }

    }


    /**
     * 交易查询
     * User: lanwanhui
     * Date: 2021/01/03
     *
     * @param string $outTradeNo 订单号
     *
     * @return array
     */
    private function _queryTrade($outTradeNo){

        try {

            $requestData = [
                // 第三方订单号
                'thirdPartyOrderNo' => $outTradeNo,
            ];

            $lib = new \Library\JsonRpc\PftRpcClient('ota_supplier_api');

            $queryRes = $lib->call('OtherSystemApi/UnionPayMini/tradeQuery', [$requestData], 'plat');

            pft_log('unionpayapplet/querytrade', json_encode($queryRes, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

            //RPC那边请求上游可能超时了，调用查询接口查一下
            if (isset($queryRes['code']) && $queryRes['code'] == 500) {
                return [500, [], ''];
            }

            //RPC那边请求上游失败了 直接返回失败
            if (!isset($queryRes['code']) || $queryRes['code'] != 200) {
                $msg = empty($queryRes['msg']) ? '查询交易接口失败' : $queryRes['msg'];
                return [204, [], $msg];
            }

            if (isset($queryRes['data']['respCode']) && $queryRes['data']['respCode'] === '0000'&& $queryRes['data']['payStatus'] === '1') {
                return [200, [], '成功'];
            } else {
                $msg = empty($consumeRes['data']['respDesc']) ? '查询交易接口失败了' : $consumeRes['data']['respDesc'];
                return [204, [], $msg];
            }

        } catch (\Exception $e) {

            $logData = [
                'e_msg'      => $e->getMessage(),
                'e_line'     => $e->getLine(),
                'e_file'     => $e->getFile(),
                'outTradeNo' => $outTradeNo,
            ];

            pft_log('unionpayapplet/querytrade', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

            return [500, [], '查询交易失败'];

        }

    }


    /**
     * 获取订单信息
     * User: lanwanhui
     * Date: 2021/7/13
     *
     * @param string $ordernum 订单号
     *
     * @return mixed
     */
    protected function _getOrder($ordernum)
    {
        $orderModel = new OrderTools('localhost');
        $orderInfo = $orderModel->getOrderInfo(
            $ordernum,
            'member,ordername,ordertel,aid,code,lid,tid,tnum,tprice,ordermode,paymode,salerid,ss.pay_status,ss.status',
            'de.aids,de.series'
        );
        if ($orderInfo['aids']) {
            // 转分销获取第二级分销商
            $tmp = explode(',', $orderInfo['aids']);
            $orderInfo['member'] = $tmp[1];
        }
        return  $orderInfo;

    }

}