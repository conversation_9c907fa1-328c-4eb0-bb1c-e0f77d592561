<?php

namespace Controller\pay;

use Business\Captcha\CaptchaApi;
use Business\Finance\AccountMoney;
use Business\JsonRpcApi\PayService\CommonHandle;
use Business\RiskManagement\ShumeiRiskCheck;
use Controller\Finance\Recharge;
use Library\Controller;
use Model\TradeRecord\OnlineTrade;

abstract class PayBase extends Controller implements PayInterface
{
    public function renew()
    {
        // TODO: Implement renew() method.
    }

    public function recharge()
    {
        $money    = I('post.money');
        $money    = floatval(number_format($money, 2, '.', ''));
        $tradeMoneyFen = bcmul($money, 100, 0);
        $openid   = I('post.openid');
        $aid      = I('post.aid');
        $did      = I('post.did', 0);
        $qrPay    = I('post.qr_pay', 0);
        if (isset($_POST['is_qr'])) {
            $qrPay    = I('post.is_qr', 0);
        }
        $memo     = I('post.memo', '', 'trim');
        $shopId   = I('post.shop_id', '', 'intval');
        $captchaCode = I('post.captchaCode', '', 'strval'); //滑块校验码
        //支付订单号,如果前端有传的话用前端传过来的。
        $outTradeNo = I('post.recharge_ordernum', time() . $did . mt_rand(1000, 9999));

        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $sid       = $loginInfo['sid'] ?? 0;

        if ($loginInfo['dtype'] == 2) {
            $this->apiReturn(401, [], '景区账号不能充值，请退出重登');
        }

        if ($loginInfo['dtype'] == 5) {
            $this->apiReturn(401, [], '散客账号不能充值，请退出重登');
        }

        $did = $did ?: $sid;
        if (!$did) {
            $this->apiReturn(400, [], '用户身份获取错误');
        }
        if ($qrPay == 0 && empty($openid)) {
            $this->apiReturn(400, [], 'OPENID为空');
        }
        if (!is_numeric($money) || $money < 0) {
            $this->apiReturn(400, [], '请输入大于0的金额，金额必须是数字');
        }

        $queryParams = [[$did, $shopId, $aid]];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
            $queryParams);

        if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
            $this->apiReturn(500, [], '用户信息获取失败，请稍后重试');
        }

        $memberInfo = array_column($queryRes['data'], null, 'id');

        $sellName = str_replace('|', '', $memberInfo[$did]['dname']);

        if ($aid > 0) {
            $boss_name = str_replace('|', '', $memberInfo[$aid]['dname']);
            if ($shopId) {
                $through_name = str_replace('|', '', $memberInfo[$shopId]['dname']);
                $body         = "[$sellName]通过{$through_name}给{$boss_name}充值{$money}元|{$did}|$aid|$shopId";
            } else {
                $body = "[$sellName]给{$boss_name}充值{$money}元|{$did}|$aid";
            }
        } else {
            $body = "[{$sellName}]账户充值{$money}元|{$did}";
        }

        if ($memo) {
            $body .= '|' . $memo;
        }

        //测试环境直接模拟支付成功
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            $model = new OnlineTrade();
            $ret   = $model->addLog($outTradeNo, $money, $body, $body, $this->getSourceT(),
                OnlineTrade::PAY_METHOD_RECHARGE);
            if (!$ret) {
                $this->apiReturn(401, [], '记录发生错误,请联系客服人员');
            }
            $trade_no = 'online_' . time();
            $res = $this->rechargeNotifySuccess($outTradeNo, $trade_no, $tradeMoneyFen, 'buyer.12301.cc',
                'seller.12301.cc', '12301.cc', '12301.cc');
            if ($res !== true) {
                $this->apiReturn(500, [], '模拟支付失败');
            }
            //返回信息
            if ($qrPay) {
                $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => ''];
            } else {
                $data = ['parameter' => "{}", 'outTradeNo' => $outTradeNo];
            }
        } else {
            $account = $loginInfo['account'] ?: $memberInfo[$did]['account'];
            // 风控，添加数美记录
            $this->riskShumeiRecharge($account, $money, $outTradeNo, $captchaCode);
            if ($openid != '') {
                $payServiceApi  = new CommonHandle();
                $redirectRes    = $payServiceApi->rechargeCheck($did, 2, $openid, $outTradeNo);
                if ($redirectRes['code'] != 200) {
                    $this->apiReturn(401, [], '检测到您的账号存在风险，请确认是否本人操作，有问题请联系客服');
                }
            }
            $rechargeHandlerRes = $this->rechargeHandler($outTradeNo, $body, $tradeMoneyFen, $did);
            if ($rechargeHandlerRes['code'] != 200) {
                $this->apiReturn($rechargeHandlerRes['code'], $rechargeHandlerRes['data'], $rechargeHandlerRes['msg']);
            }
            $data = $rechargeHandlerRes['data'];
            $model = new OnlineTrade();
            $ret   = $model->addLog($outTradeNo, $money, $body, $body, $this->getSourceT(),
                OnlineTrade::PAY_METHOD_RECHARGE);
            if (!$ret) {
                $this->apiReturn(401, [], '记录发生错误,请联系客服人员');
            }
        }
        $this->apiReturn(200, $data);
    }

    public function order()
    {
        // TODO: Implement order() method.
    }

    public function cardSolutionRecharge()
    {
        // TODO: Implement cardSolutionRecharge() method.
    }

    public function micropay()
    {
        // TODO: Implement micropay() method.
    }

    public function Query()
    {
        // TODO: Implement Query() method.
    }

    public function Refund()
    {
        // TODO: Implement Refund() method.
    }

    public function orderNotify()
    {
        // TODO: Implement orderNotify() method.
    }

    public function rechargeNotify()
    {
        $res = $this->rechargeNotifyHandler();
        if ($res['code'] != 200) {
            exit($res['msg']);
        }
        $content = $res['data'];
        $rechargeRes = $this->rechargeNotifySuccess($content['out_trade_no'], $content['trade_no'],
            $content['amount']);
        \Business\Pay\PayBase::setPayDailyRecord(100,'支付中心充值回调通知',$content, $rechargeRes, ['sourceT'=> $this->getSourceT()]);
        if ($rechargeRes !== true) {
            exit('FAIL');
        } else {
            exit('SUCCESS');
        }
    }

    protected function rechargeNotifySuccess($outTradeNo, $tradeNo, $totalFeeFen, $buyerInfo = '', $sellerInfo = '',
        $wxAppId = PFT_WECHAT_APPID, $wxSubAppId = PFT_WECHAT_APPID) {
        $accountMoneyBiz = new AccountMoney();
        $sourceT         = $this->getSourceT();

        return $accountMoneyBiz->recharge($outTradeNo, $sourceT, $tradeNo,
            $totalFeeFen, $buyerInfo,$sellerInfo, $wxAppId, $wxSubAppId);
    }

    public function payResultCheck()
    {
        $this->isLogin('ajax');
        $ordernum = I('post.ordernum');
        pft_log('yeepay/pay_result_check', json_encode([$this->getClassname(), $_POST], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        $model = new OnlineTrade();

        $payLog = $model->getLog($ordernum, $this->getSourceT());
        if (!$payLog) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
        }

        if ($payLog['status'] == 1) {
            parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看', true);
    }

    protected function riskShumeiRecharge($account, $money, $outTradeNo, $captchaCode)
    {
        if (empty($_SERVER['HTTP_DEVICEID'])){
            return;
        }
        //滑块验证
        if (empty($captchaCode)){
            $riskCheck = new ShumeiRiskCheck();
            $virtualOrderEventData = [
                'product'   => '账本充值',
                'orderId'   => $outTradeNo,
                'price'     => floatval($money),
            ];
            $orderCheckRes = $riskCheck->shumeiCheck('virtualOrder', 2, $account, $virtualOrderEventData);

            $paymentEventData = [
                'method'    => 'widget',
                'channel'   => 'widget',
                'amount'    => floatval($money),
                'orderId'   => $outTradeNo,
            ];
            $payCheckRes      = $riskCheck->shumeiCheck('payment', 2, $account, $paymentEventData);
            if (($payCheckRes['code'] == 200 && $payCheckRes['data']['riskLevel'] == 'REJECT') || ($orderCheckRes['code'] == 200 && $orderCheckRes['data']['riskLevel'] == 'REJECT')) {
                $this->apiReturn(209, ['need_captcha' => true], '请验证滑块');
            }
        }else{
            $captchaCheck = new CaptchaApi();
            $captchaRes   = $captchaCheck->handleSecondCheck($captchaCode);
            if ($captchaRes !== true) {
                $this->apiReturn(209, ['need_captcha' => true], '请验证滑块');
            }
        }
    }

    protected function returnData($code, $msg = '', $data = [])
    {
        return ['code' => $code, 'msg' => $msg, 'data' => $data];
    }

    /**
     * 接口数据返回
     *
     * @param  int  $code  返回码
     * @param  array  $data  接口返回数据
     * @param  string  $msg  错误说明，默认为空
     * @param  boolean  $object  当data为空时，是否返回空对象
     *
     */
    public function apiReturn($code, $data = [], $msg = '', $object = false)
    {
        if ($data == [] && $object) {
            $data = new \stdClass;
        }

        // 返回嵌入多语言逻辑
        if (isset($_SERVER['HTTP_LANGUAGE'])) {
            $data = $this->handleDataLanguage($data);
            $msg  = $this->handleLanguage($msg);
        }

        $data = [
            'code' => $code,
            'data' => $data,
            'msg'  => $msg,
            'status' => $code == 200 ? 'success' : 'fail',
        ];

        //如果入口的地方有记录请求时间戳
        if (isset($_POST) && isset($_POST['_pft_base_start_mark'])) {
            $startMark = $_POST['_pft_base_start_mark'];
            $endMark   = str_pad(str_replace('.', '', microtime(true)), 14, '0');

            //毫秒
            $costTime = (intval($endMark) - intval($startMark)) / 10;

            $logData = json_encode([
                'request'       => $_SERVER['REQUEST_ID'],
                'start_mark'    => $_POST['_pft_base_start_mark'],
                'cost_time'     => $costTime . 'ms',
                'api'           => $_POST['_pft_base_controller'] . '::' . $_POST['_pft_base_action'],
                'response_data' => $data,
            ]);
            pft_log('pft_api_response', $logData, 3);
        }
        //暂时屏蔽Cannot modify header information - headers already sent 错误
        @header('Content-type:text/json');

        $res = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        echo $res;
        if (PHP_SAPI == 'cli') {
            return true;
        }
        exit();
    }

    protected function getClassname() {
        $arr = explode('\\', static::class);
        return array_pop($arr);
    }

    protected function getRechargeCallbackUrl() {
        $classname = $this->getClassname();
        if (defined('IS_PFT_GRAY')) {
            return "http://pay.gray.12301.cc/r/pay_{$classname}/rechargeNotify";
        } else {
            return PAY_DOMAIN . "r/pay_{$classname}/rechargeNotify";
        }
    }

    abstract protected function getSourceT();

    abstract protected function rechargeHandler($outTradeNo, $subject, $totalFeeFen, $did);

    abstract protected function rechargeNotifyHandler();
}