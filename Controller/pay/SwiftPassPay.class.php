<?php
/**
 * Created by PhpStorm.
 * User: linchen
 * Date: 2019/1/17 0017
 * Time: 17:12
 */
namespace Controller\pay;
use Business\JsonRpcApi\PayService\CommonHandle;
use Business\Order\MergeOrder;
use Business\Pay\PayBase;
use Business\Pay\PayCache;
use Business\Pay\WxPayGoldPlan;
use Library\Business\WePay\WxPayNotifyResponse;
use Model\Order\OrderQuery;
use Library\Business\WePay\WxPayApi;
use Library\Controller;
use Library\Tools\Helpers;
use Model\Order\OrderTools;
use Model\TradeRecord\OnlineTrade;
use Model\Order\TeamOrderSearch;
use Business\Order\Query;
use Library\Constants\DingTalkRobots;
define('IN_PFT', true);
class SwiftPassPay extends Controller implements PayInterface {
    private $appid        = '';
    private $appkey       = '';
    private $orderModel = null;
    private $pay_type   = 1;
    private $memberId;
    private $wx_appid   = '';
    private $zfb_appid  = '';
    
    public function __construct($config = null,$memberId = 0,$pay_type = 1)
    {
        $this->appid          = $config['appid'] ? $config['appid'] :'';
        $this->appkey         = $config['appkey'] ? $config['appkey'] : '';
        $this->wx_appid       = $config['wxAppId'] ? $config['wxAppId'] : '';
        $this->zfb_appid      = $config['zfbAppId'] ? $config['zfbAppId'] : '';
        $this->pay_type       = $pay_type;
        $this->memberId       = $memberId;
    }

    /**
     * 支付后的回调
     * 如果受理端接口调不通
     * 这里通过支付接口 返回的 jsonRequestData
     */
    public function orderNotify(){
        $log_path = 'swift/order';
        $notify   = new WxPayNotifyResponse();
        $notify->saveData($log_path);
        //记录日志
        $logData = json_encode([
            'key'    => '微信或者支付宝订单二维码支付',
            'notify' => $notify->data,
        ], JSON_UNESCAPED_UNICODE);
        $sign   = $notify->data['sign'];
        $config = load_config('swiftpassnotify','pay');
        pft_log('swift_qr_order', $logData, 3);
        unset($notify->data['sign']);
        if ($notify->data['mch_id'] == '113550048193'){  //这边先这样兼容下，这个商户号目前用md5
            $checkSign = $this->isValidSign($sign,$notify->data,$config[$notify->data['mch_id']]);
        }else{
            $client = new \Library\Tools\YarClient('pay');
            $notify->data['sign'] = $sign;
            $checkSign = $client->call('Pay/SwiftPassPay/verifyRSASign', [$notify->data]);
        }
        if (!$checkSign || !isset($notify->data['pay_result']) || $notify->data['pay_result'] != 0){
            echo 'FAILED';
            exit;
        }
        $prefix = ['qr_','alipay_','wepay_','cloud_'];
        //截取订单号的来源编号
        $outTradeNo     = str_replace($prefix, '', $notify->data['out_trade_no']); //订单号
        foreach ($prefix as $k => $v){
            if (strpos($notify->data['out_trade_no'],$v !== false)){
                pft_log('swift/scanorder',$notify->data['out_trade_no']);
                break;
            }
        }



        $transaction_id = $notify->data['transaction_id']; //交易号
        $total_fee      = (int) $notify->data['total_fee'] + 0; //金额用分为单位
        $pay_channel    = 41;
        $payTerminal    = '';
        $is_member      = 0;
        $buyerInfo      = array('buyerId' => $notify->data['openid']);
        $sellerInfo     = array('buyerId' => $notify->data['mch_id']);
        $pay_to_pft     = 0;
        //理论上是不支付到票付通的吧
        $json_buy  = json_encode($buyerInfo);
        $json_sell = json_encode($sellerInfo);
        $options = [
            'buyer_info'   => $json_buy,
            'sell_info'    => $json_sell,
            'pay_channel'  => $pay_channel,
            'pay_termianl' => $payTerminal,
        ];
        // 点金计划处理回调问题
        $payServiceApi = new CommonHandle();
        $redirectRes = $payServiceApi->getRedirectUrlRpc($outTradeNo);
        if ($redirectRes['code'] == 200){
            $payServiceApi->setRedirectUrlRpc($transaction_id, $redirectRes['data']['redirect_url']);
        }
//        $wxPGP       = new WxPayGoldPlan();
//        $redirectUrl = $wxPGP->getRedirectUrl($outTradeNo);
//        $wxPGP->setRedirectUrl($transaction_id, $redirectUrl);

        $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_SWIFTPASS,
            $total_fee, (int)$pay_to_pft, $options);
        PayBase::setPayDailyRecord(5,'回调结果',[],$result);
        if ($result['code'] == 200) {
            $needVerify = PayCache::getOrderVerfify($outTradeNo);
            if ($needVerify) {
                $successOrders = $result['data']['success_orders'];
                $paymentObj = new \Business\Order\MergeOrder();
                $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
            }
        }
        if ($result['code'] == 200 || $result['code'] == 102 || $result['code'] == 101) {
            echo 'SUCCESS';
        } else {
            echo 'FAILED';
        }
        exit;

//        $biz       = new \Business\Order\MergeOrder();
//        if ($biz->isCombineOrder($outTradeNo)) {
//            $result = $biz->handlerCombinePayOrders($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_SWIFTPASS, $is_member,
//                $json_sell, $json_buy, $pay_to_pft, $pay_channel, $payTerminal);
//
//            $needVerify = \Library\Cache\Cache::getInstance('redis')->get('ordernum_needVerify:'.$outTradeNo);
//            if ($needVerify) {
//                $successOrders = $result['data']['success_orders'];
//                $paymentObj = new \Business\Order\MergeOrder();
//                $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
//            }
//        } else {
//            try {
//                $res = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_SWIFTPASS,
//                    $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $pay_channel, '', $payTerminal);
//            } catch (\SoapFault $e) {
//                $retry = true;
//                pft_log('order_pay/error', "(微信银联)订单支付失败:{$outTradeNo}，soap抛出异常:{$e->getCode()}->{$e->getMessage()}");
//                //记录日志
//                $logData = json_encode([
//                    'key' => '微信银联订单支付',
//                    'ordernum' => $outTradeNo,
//                    'error' => "soap抛出异常:{$e->getCode()}->{$e->getMessage()}"
//                ], JSON_UNESCAPED_UNICODE);
//                pft_log('swift_qr_order', $logData, 3);
//
//                Helpers::sendDingTalkGroupRobotMessage("[{$outTradeNo}]订单支付失败;soap抛出异常:{$e->getCode()}->{$e->getMessage()}", "支付失败", Helpers::getServerIp(), DingTalkRobots::MYSQL_ERROR);
//            }
//            if (isset($retry) && $retry === true) {
//                $res = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_SWIFTPASS,
//                    $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $pay_channel, '', $payTerminal);
//            }
//            if ($res === 100) {
//                $orderInfo = $this->_getOrder($outTradeNo);
//                if (Helpers::isMobile($orderInfo['ordertel'])) {
//                    $args = [
//                        'ordernum' => $outTradeNo,
//                        'buyerId'  => $orderInfo['member'], //购买人的ID
//                        'mobile'   => $orderInfo['ordertel'],
//                        'aid'      => $orderInfo['aid'],
//                    ];
//                    $jobId = \Library\Resque\Queue::push('notify', 'OrderNotify_Job', $args);
//                }
//            } else {
//                echo 'FAILED';
//                exit;
//            }
//        }
//        echo 'SUCCESS';
//        exit;
    }
    public function renew(){

    }
    public function recharge(){

    }
    public function order(){
        //平台订单号
        $outTradeNo = I('post.out_trade_no', 0, 'string');
        //是否二维码支付
        $qrPay = I('post.is_qr', 0, 'intval');
        //微信用户openid
        $openid = I('post.openid', null);
        // 公众号对应的实际openid——独立收款商户
        $subAppid   = I('post.sub_appid', null);
        $subOpendid = I('post.sub_openid', null);
        if (!is_null($subAppid) && $subAppid == WxPayApi::$sub_appid && $subOpendid) {
            $openid = $subOpendid;
        }
        $appid = $this->wx_appid;
        // 支付宝支付
        if ($this->pay_type == 1) {
            $appid = $this->zfb_appid;
            $openid= I('post.buyer_id');
        }
        //订单主体说明
        $subject = mb_substr(trim(I('post.subject')), 0, 20, 'utf-8');
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }
        PayBase::setPayDailyRecord(5,'订单请求',I('post.'));
        $mergeOrder = new MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
            try {
                $mergeOrder->handlerCombinePayLog($outTradeNo, '', OnlineTrade::CHANNEL_SWIFTPASS, $this->memberId);
            } catch (OrderPreCheckException $e) {
                parent::apiReturn(401, [], $e->getMessage());
            }
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string) $outTradeNo);
        }

        //真实支付
        if (!$qrPay && !$openid) {
            parent::apiReturn(401, [], '请用微信APP打开进行支付');
        }
        $client = new \Library\Tools\YarClient('pay');
        if ($qrPay) {
            if ($this->pay_type == 2){
                $tmpOutTradeNo = 'wepay_'.$outTradeNo;
                $requestParams = [$this->appid,$this->appkey,$subject,$tmpOutTradeNo , $totalFee];
                $result        = $client->call('Pay/SwiftPassPay/wxScan', $requestParams);
            }elseif ($this->pay_type == 5){
                $tmpOutTradeNo = 'cloud_'.$outTradeNo;
                $requestParams = [$this->appid,$this->appkey,$subject,$tmpOutTradeNo , $totalFee];
                $result        = $client->call('Pay/SwiftPassPay/unifiedScanPay', $requestParams);
            }else{
                $tmpOutTradeNo = 'qr_'.$outTradeNo;
                $requestParams = [$this->appid,$this->appkey,$subject,$tmpOutTradeNo , $totalFee];
                $result        = $client->call('Pay/SwiftPassPay/alipayScan', $requestParams);
            }
            //记录日志
            PayBase::setPayDailyRecord(5,'主扫三方支付',$requestParams,$result);
            //生成微信支付二维码
            if ($result['code'] == 200 && $result['res']['result_code'] == '0'){
                //$data          = ['outTradeNo' => $outTradeNo, 'qrUrl' => $result['res']['code_img_url']];
                $data          = ['outTradeNo' => $outTradeNo, 'qrUrl' => $result['res']['code_url']];
            } else {
                parent::apiReturn(401, [], '支付二维码生成失败');
            }
        } else {
            $jsRequsetParms = [$this->appid,$this->appkey, $openid, $appid,$subject, $outTradeNo, $totalFee];
            //1.$mid int 商户号 2 $tid int 终端号 3 $msgSrcId int 来源编号4 $sign string MD5密钥 5 $msgSrc string 消息来源 6 $money int 金额7 $orderId string 订单号 8 $returnUrl string 返回地址
            $res = $client->call('Pay/SwiftPassPay/jsPay', $jsRequsetParms);
            //记录日志
            PayBase::setPayDailyRecord(5,'主扫三方支付',$jsRequsetParms,$res);
            if ($res['code'] == 200 && $res['res']['result_code'] == '0'){
                $data = json_decode($res['res']['pay_info'],true);
            }else{
                parent::apiReturn(401, [], $res['res']['err_msg']);
            }
        }
        $payMethod = OnlineTrade::PAY_METHOD_ORDER;
        $tradeModel = new OnlineTrade();
        $ret        = $tradeModel->addLog($outTradeNo, $totalFee / 100, $subject, $subject, OnlineTrade::CHANNEL_SWIFTPASS, $payMethod, '', $this->memberId);
        if (!$ret) {
            parent::apiReturn(401, [], '支付记录生成失败');
        }
        echo json_encode(['code' => 200, 'data' => $data, 'status' => 'ok', 'msg' => 'success']);
        exit;
    }
    public function cardSolutionRecharge(){

    }
    public function micropay(){
        $outTradeNo  = I('post.ordernum');
        $authCode    = I('post.auth_code');
        $totalFee    = I('post.money') * 100;
        $isMember    = I('post.is_member', 0);
        $subject     = I('post.subject', '订单支付');
        $payScen     = I('post.pay_scen', 1);
        $payTerminal = I('post.terminal', 0, 'intval'); //支付的终端
        $needVerify  = I('post.verify', 0);//是否支付后立即验证标识
        $terminal    = I('post.terminal', 0, 'strval');//支付时所使用的终端号
        $pay_type   = I('post.pay_type', 2, 'intval');
        $checkSource     = I('post.check_source', -1, 'intval');//验证来源
        $mergePayFlag = false;// 合并付款标识
        //支付来源 - 具体可以参照business.conf.php -> track_source
        // 0=黑色终端机 2=自助机 4=云票务 5=云闸机 20=安卓智能终端机
        $paySource = I('post.pay_source', 20, 'intval');
        //支付的操作人 可以不传 用在记录追踪表李
        $payTrackOpId  = I('post.pay_track_op_id', 0);
        //年卡下单类型  默认值为空字符串, 0年卡下单 1年卡续费
        $annualCardOrderType = I('post.annualCardOrderType', false);
        if (!$outTradeNo || !$authCode) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }
        //扫码支付授权码，设备读取用户微信中的条码或者二维码信息
        //（注：用户刷卡条形码规则：18位纯数字，以10、11、12、13、14、15开头）
        $codeLength = strlen($authCode);
        if (!in_array($codeLength,[18,19]) || !is_numeric($authCode)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '非法的支付条码，请重新刷新支付码', true);
        }
        $paymentObj      = new \Business\Order\MergeOrder();
        if($paymentObj->isCombineOrder($outTradeNo)){
            $mergePayFlag = true;
            $paymentObj->handlerCombinePayLog($outTradeNo, $subject, OnlineTrade::CHANNEL_SWIFTPASS, $this->memberId);
        }
        if ($pay_type == 2){
            //记录日志
            PayBase::setPayDailyRecord(5,'微信扫码请求',I('post.'));
        }elseif ($pay_type == 5){
            //记录日志
            PayBase::setPayDailyRecord(5,'云闪付扫码请求',I('post.'));
        } else{
            //记录日志
            PayBase::setPayDailyRecord(5,'支付宝扫码请求',I('post.'));
        }
        switch ($payScen) {
            case 1:
                //订单支付
                if (is_numeric($outTradeNo) || $mergePayFlag === true) {
                    $orderQuery = new OrderQuery('localhost');
                    $totalFee  = $orderQuery->get_order_total_fee($outTradeNo);
                }
                break;
            case 2:
                break;
        }
        $OnlineTrade = new OnlineTrade();
        $result      = $OnlineTrade->addLog($outTradeNo, $totalFee / 100, $subject, $subject, OnlineTrade::CHANNEL_SWIFTPASS, OnlineTrade::PAY_METHOD_ORDER, '', $this->memberId);
        if (!$result) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }
        $client = new \Library\Tools\YarClient('pay');
        //1.$mid int 商户号 2 $tid int 终端号 3 $msgSrcId int 来源编号4 $sign string MD5密钥 5 $msgSrc string 消息来源 6 $money int 金额7 $orderId string 订单号 8 $returnUrl string 返回地址
        $requestParams = [$this->appid,$this->appkey,$authCode,$subject,$outTradeNo,$totalFee];
        $payResult  = $client->call('Pay/SwiftPassPay/microPay', $requestParams);
        PayBase::setPayDailyRecord(5,'扫码请求三方',$requestParams,$payResult);
        if (isset($payResult['code']) && $payResult['code'] == 200 && isset($payResult['res']['need_query']) && $payResult['res']['need_query'] == 'N'){
            $errorInfo = $payResult['res']['err_msg'] ? : '返回消息异常联系管理员';
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [],$errorInfo , true);
        }
        $num = 0;
        $getMoneyFlag = false;
        do {
            $payResult  = $client->call('Pay/SwiftPassPay/getTradeDetail', [$this->appid,$this->appkey,$outTradeNo]);
            if ($payResult['code'] == 200 && $payResult['res']['trade_state'] == 'SUCCESS'){
                $getMoneyFlag = true;
                break;
            }
            $num++;
            sleep(1);
        } while($num < 60);

        if (!$getMoneyFlag) {
            // 如果60秒后还没收款成功冲正
            //$reverseResult  = $client->call('Pay/ChinaUmsPay/chinaUmsPosReverse', [$this->memberId,$this->appid,$this->appkey,$this->merchantId,$this->terminalCode,$outTradeNo,$totalFee]);
            pft_log('swift_micropay',json_encode([
                'ordernum'=>$outTradeNo,
                'msg'=>'支持超时,需要冲正',
                'data'=>[$this->memberId,$this->appid,$this->appkey,$this->merchantId,$this->terminalCode,$outTradeNo,$totalFee]], JSON_UNESCAPED_SLASHES|JSON_UNESCAPED_UNICODE),3
            );
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '请求威富通接口失败:' . $payResult['res']['trade_state'], true);
        }

        //记录日志
        $logData = json_encode([
            'key'      => '威富通扫码支付结果',
            'ordernum' => $outTradeNo,
            'res'      => $payResult,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('swift_micropay', $logData, 3);

        if ($payResult['res']['trade_state'] != 'SUCCESS') {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "支付失败,订单号:{$outTradeNo},请求威富通失败", true);
        }
        $buyerInfo = isset($payResult['res']['thirdPartyName']) ? $payResult['res']['thirdPartyName']: '';
        $sellerInfo = '';
        $transactionId = $payResult['res']['transaction_id'];
        $jsonBuy       = $buyerInfo;
        $jsonSell      = $sellerInfo;
        $payToPft      = false;
        $payChannel    = 41;
        $options = [
            'buyer_info'   => $jsonBuy,
            'sell_info'    => $jsonSell,
            'pay_channel'  => $payChannel,
            'pay_termianl' => $payTerminal,
            'oper'         => $payTrackOpId,
            'pay_source'   => $checkSource,
        ];
        $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_SWIFTPASS,
            $totalFee, (int)$payToPft, $options);
        $data = [];
        if ($result['code'] == 200) {
            $successOrders = $result['data']['success_orders'];
            foreach ($successOrders as $item) {
                $data[] = [
                    'code'     => 200,
                    'msg'      => '支付成功',
                    'order_id' => $item,
                ];
            }
            if ($needVerify) {
                if ($mergePayFlag === true) {
                    $verifyRes = $paymentObj->handleCombineOrderBuyVerifyByRpc($successOrders, $payTerminal, $payTrackOpId, $checkSource);
                    if ($verifyRes){
                        $verifyData = $verifyRes[0];  //默认就取第0个，因为手持机那边只能读取一个订单
                        $data  = $verifyData;
                    }
                } else {
                    $orderInfo = $this->_getOrder($outTradeNo);
                    $query     = new \Business\Order\Query();
                    $paymode   = 28;

                    $data      = $query->getOrderInfoForPrintByRpc($outTradeNo, $orderInfo, $totalFee, $paymode, $terminal,$checkSource,$payTrackOpId);

                }
            }
        }
        // 合并付款流程处理
//        if ($mergePayFlag===true) {
//            $result = $paymentObj->handlerCombinePayOrders($outTradeNo, $transactionId, OnlineTrade::CHANNEL_SWIFTPASS, $isMember, $jsonSell, $jsonBuy, $payToPft, $payChannel, $payTerminal, $payTrackOpId, $annualCardOrderType);
//            if ($result['code']!=200) {
//                $result = ['code'=>parent::CODE_INVALID_REQUEST, 'data'=>[],'msg'=>'合并付款失败'];
//            } else {
//                $msg    = '合并付款支付成功';
//                if ($needVerify) {
//                    // 合并付款模式不支持买即验
//                    $msg .= ",当前不支持买即验，请手工验证";
//                }
//                //更新团队订单的支付状态
//                $teamOrderModel = new TeamOrderSearch();
//                $teamOrderInfo  = $teamOrderModel->getMainOrderInfoByCombinePayId(strval($outTradeNo));
//                if (!empty($teamOrderInfo)) {
//                    $teamOrderModel->setTeamOrderByOrder($teamOrderInfo['ordernum'], ['pay_status' => 1]);
//                }
//                $result = ['code'=>parent::CODE_SUCCESS, 'data'=>[],'msg'=>$msg];
//            }
//        }
//        else {
//            $result = $this->_changeOrderPay($outTradeNo, $transactionId,$totalFee, $isMember, $jsonSell, $jsonBuy,$payToPft, $payChannel,$payTerminal,$needVerify,$terminal, $payTrackOpId, $annualCardOrderType);
//        }
        //记录日志
        PayBase::setPayDailyRecord(5,'扫码成功',[$outTradeNo],$result);
        parent::apiReturn($result['code'], $data, $result['msg'], true);
    }
    public function Query(){

    }
    public function Refund(){

    }
    public function rechargeNotify(){

    }
    public function payResultCheck(){

    }
    /**
     * 获取订单信息
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @param  string $ordernum
     * @return array
     */
    private function _getOrder($ordernum)
    {
        $this->orderModel = new OrderTools('localhost');
        $orderInfo        = $this->orderModel->getOrderInfo($ordernum, 'member,ordername,ordertel,aid,code,lid,tid,tnum,tprice,ordermode,paymode,salerid,ss.pay_status,ss.status', 'de.aids,de.series');
        if ($orderInfo['aids']) {
            // 转分销获取第二级分销商
            $tmp                 = explode(',', $orderInfo['aids']);
            $orderInfo['member'] = $tmp[1];
        }
        $this->orderInfo = $orderInfo;
        return $orderInfo;
    }
    private function _changeOrderPay($outTradeNo, $transaction_id,$total_fee, $is_member, $json_sell, $json_buy, $pay_to_pft, $pay_channel,$payTerminal,$needVerify, $terminal, $payTrackOpId = 0, $annualCardOrderType = '') {
        try {
            $res      = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_SWIFTPASS,
                $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $pay_channel, '', $payTerminal, $payTrackOpId, $annualCardOrderType);
        } catch (\SoapFault $e) {
            $retry = true;
            pft_log('order_pay/error', "(威富通)订单支付失败:{$outTradeNo}，soap抛出异常:{$e->getCode()}->{$e->getMessage()}");
            Helpers::sendDingTalkGroupRobotMessage("(威富通)[{$outTradeNo}]订单支付失败;soap抛出异常:{$e->getCode()}->{$e->getMessage()}", "支付失败",  Helpers::getServerIp(), DingTalkRobots::MYSQL_ERROR);
        }
        if (isset($retry) && $retry === true) {
            $res      = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_SWIFTPASS,
                $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $pay_channel, '', $payTerminal, $payTrackOpId, $annualCardOrderType);
        }
        $output = [];
        if ($res === 100) {
            $orderInfo = $this->_getOrder($outTradeNo);
            $args = [
                'ordernum' => $outTradeNo,
                'buyerId'  => $orderInfo['member'], //购买人的ID
                'mobile'   => $orderInfo['ordertel'],
                'aid'      => $orderInfo['aid'],
            ];
            $jobId = \Library\Resque\Queue::push('notify', 'OrderNotify_Job', $args);
            if ($needVerify) {
                if (empty($this->orderInfo)) {
                }
                $query   = new Query();
                $paymode = 5;
                $output  = $query->getOrderInfoForPrint($outTradeNo, $this->orderInfo, $total_fee, $paymode, $terminal, 0);
            }

            if ($orderInfo['ordermode'] == 24) {
                $teamOrderModel = new TeamOrderSearch();
                $mainOrderInfo  = $teamOrderModel->getMainOrderInfoBySonOrder(strval($outTradeNo));
                if (empty($mainOrderInfo)) {
                    parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功, 未找到团单号');
                }
                $teamOrder = $mainOrderInfo['main_ordernum'];
                $sonOrder  = $teamOrderModel->getSonOrderInfoByMainOrderNum($teamOrder);
                $orderArr  = array_column($sonOrder, 'son_ordernum');
                $orderTool = new OrderTools();
                $orderData = $orderTool->getOrderInfo($orderArr, 'status,pay_status');

                $needUpdatePay = true;
                if ($orderData) {
                    foreach ($orderData as $value) {
                        if ($value['pay_status'] != 1 && $value['status'] != 3) {
                            $needUpdatePay = false;
                        }
                    }
                }

                if ($needUpdatePay) {
                    $payStatus = 1;
                } else {
                    $payStatus = 2;
                }

                if ($mainOrderInfo['status'] != 1 || $mainOrderInfo['pay_status'] != $payStatus) {
                    $res = $teamOrderModel->setTeamOrderByOrder($teamOrder, ['updatetime' => time(), 'status' => 1, 'pay_status' => $payStatus]);
                }
            }

            $msg    = '支付成功';
            $code   = parent::CODE_SUCCESS;
        } else {
            $msg    = "威富通支付成功但订单状态更新失败,订单号:{$outTradeNo}";
            $code   = parent::CODE_INVALID_REQUEST;
        }
        return ['code'=>$code,'data'=>$output,'msg'=>$msg,'pay_res'=>$res];
    }
    /**
     * 验证签名
     * @param $sign
     * @param $data
     * @return bool
     * <AUTHOR>
     * @date   2019-1-22
     */
    private function isValidSign($sign, $data,$mch_key)
    {
        return $sign === $this->_getMD5Sign($data,$mch_key);
    }
    /**
     * md5的签名
     * @param $data
     * @return string
     * <AUTHOR>
     * @date   2019-1-22
     */
    private function _getMD5Sign($data, $mch_key)
    {
        $str = $this->_getSignStr($data) . "key={$mch_key}";
        return strtoupper(md5($str));
    }
    /**
     * 未加密的签名
     * @param $data
     * @return string
     * <AUTHOR>
     * @date   2019-1-22
     */
    private function _getSignStr($data)
    {
        if (is_array($data)) {
            ksort($data);
        }
        // sign不参与签名
        unset($data['sign']);
        $str = '';
        foreach ($data as $key => $value) {
            $str .= "{$key}={$value}&";
        }
        return $str;
    }
    /**
     * 非平台订单支付
     * <AUTHOR>
     * @date   2019-07-28
     */
    public function specialOrderPay(){
        parent::apiReturn(204, [], '该收款渠道暂不支持此收款方式，请联系管里员');
        exit;
    }
}