<?php
/**
 * 订单查询
 * User: lanwanhui
 * Date: 2021/10/14
 */

namespace Controller\pay;

use Library\Controller;
use Model\TradeRecord\OnlineTrade;

class CommonPayQuery extends Controller
{

    /**
     * 支付订单查询
     * User: lanwanhui
     * Date: 2022/2/24
     */
    public function payResultCheck()
    {
        $ordernum = I('ordernum','','trim,strval');
        if(empty($ordernum)) {
            parent::apiReturn(parent::CODE_PARAM_ERROR, [], '订单号不能为空', true);
        }

        $model  = new OnlineTrade();
        $payLog = $model->getTradeDetailByOutTradeNo($ordernum);
        if (!$payLog) {
            parent::apiReturn(parent::CODE_NO_CONTENT, [], '支付记录不存在', true);
        }

        parent::apiReturn(parent::CODE_SUCCESS, [
            'out_trade_no'  => $payLog['out_trade_no'],
            'trade_no'      => $payLog['trade_no'] ?: '',
            'total_fee'     => $payLog['total_fee'] * 100,
            'trade_time'    => $payLog['dtime'] ?: '',
            'status'        => $payLog['status'],
        ]);

    }

}