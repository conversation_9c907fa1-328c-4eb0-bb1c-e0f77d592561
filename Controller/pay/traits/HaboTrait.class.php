<?php

namespace Controller\pay\traits;

use Controller\AppCenter\HaboDock;
use Library\Business\cmbc_wx_pay\WxSdk;
use Model\TradeRecord\OnlineTrade;
use Business\Ota\HaboPayBusiness;

/**
 * 哈勃支付trait
 * 支付代码结构比较混  目前只是简单的提取出来放在这里
 * 使用的类只能是CmbcWxPay这个类  以及WxPay
 * @Author: Lerko
 * @Date:   2017-09-04 14:32:38
 * @Email: <EMAIL>
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2017-09-27 15:02:25
 */
trait HaboTrait{

    
    /**
     * 哈泊支付
     * <AUTHOR>
     * @date 2017-07-12
     *
     * @description 充值-post
     * @money:充值的金额，单位“元”
     * @carno:车牌号
     * @openid:微信openid
     * @aid:供应商ID
     * @is_qr:是否扫码支付 0 不扫码  1 扫码
     */
    public function parkPay()
    {
        $money  = I('post.money');//支付金额
        $carno  = I('post.carno');//车牌号码
        $openid = I('post.openid');
        $aid    = I('post.aid');
        $is_qr  = I('post.qr_pay', 0);

        //todo::以下是【测试数据】   carno 车牌号  openid 微信openid  aid 被充值人id
        // $carno  = '闽D3P585';
        // $openid = 'oNbmEuHM2miiuxsInnD3B-WnYATM';
        // $aid    = '3385';
        //todo::以上是【测试数据】

        if ($is_qr==0 && empty($openid)){
            parent::apiReturn(400, [], "OPENID为空");
        }
        if (!is_numeric($money) || $money<0) {
            parent::apiReturn(400, [], "请输入大于0的金额，金额必须是数字");
        }
        if (!$carno) {
            parent::apiReturn(400, [], "车牌为空");
        }
        //todo::根据车牌号码获取需要支付金额，不要相信客户端提交的金额
        $HaboDock = new HaboDock();
        $HaboInfo = $HaboDock->getOrderPost($carno);

        if ($HaboInfo == '500') {
            parent::apiReturn(400, [], "抱歉获取停车信息失败!");
        }

        //根据车牌获取支付金额
        $total_fee    = $HaboInfo['NoPayPrice']*100;

        //todo::这里是支付金额【测试数据】  单位为分
        $total_fee    = 1;
        //todo::这里是支付金额【测试数据】  单位为分


        $body         = $HaboInfo['Message'];
        $out_trade_no = time().$aid.mt_rand(1000,9999);
        $pay_type     = WxSdk::API_WXQRCODE;
        //todo::发起支付
        $payType = 10;  //10：微信支付 、20：支付宝、 30：其他 目前只有10

        //todo::储存信息在支付表里 支付成功后 回调给哈泊时使用
        $info = [
            'inTime'  => $HaboInfo['InTime'],
            'outTime' => $HaboInfo['OutTime'],
            'parkId'  => $HaboInfo['ParkId'],
            'carNo'   => $HaboInfo['CarNo'],
            'fee'     => $total_fee,
            'payType' => $payType,
            'aid'     => $aid,
        ];

        $bodys = json_encode($info);

        if ($is_qr) {
            $parameters = $this->wxPayLib->qrPay($total_fee, $body, $out_trade_no, self::PARK_NOTIFY_URL, '微信支付', $pay_type);
            if ($parameters['return_code']!='SUCCESS') {
                $msg = "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['return_msg']}";
                if (!$is_qr) $msg .= ",您可以尝试使用【微信二维码支付】功能来完成充值";
                parent::apiReturn(400, [], $msg);
            }
            $data = ['outTradeNo'=>$out_trade_no,'qrUrl'=>$parameters['code_url']];
        } else {
            $parameters = $this->wxPayLib->jsApiPay($total_fee, $body, $out_trade_no,$openid, self::PARK_NOTIFY_URL);
            if (is_array($parameters) && isset($parameters['return_code']) && $parameters['return_code']!='SUCCESS') {
                parent::apiReturn(400, [], "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['return_msg']}");
            }
            $data = ['parameter'=>json_decode($parameters), 'outTradeNo'=>$out_trade_no];
        }

        $OnlineTrade= new \Model\TradeRecord\OnlineTrade();
        //生成充值记录
        $create = $OnlineTrade->addRecord(
            $out_trade_no,
            $bodys,
            $total_fee,
            '',
            '',
            8,
            $body,
            0
        );
        if (!$create)  $this->apiReturn(204, [], '订单记录生成失败');

        $this->apiReturn(200, $data);
    }

    /**
     * 停车场支付回调
     */
    public function parkPayNotify()
    {
        $raw  = file_get_contents('php://input');
        $result = $this->wxPayLib->PayNotify($raw);

        pft_log('wepay_habo/order', "callback:" . json_encode($result, JSON_UNESCAPED_UNICODE));

        if ($result['return_code']!=='SUCCESS') {
            pft_log('wepay_habo/order', "fail:" . json_encode($result, JSON_UNESCAPED_UNICODE));
            exit('FAIL');
        }

        if ($result['data']['tradeStatus']!=='S') {
            exit('FAIL');
        }
        //哈泊支付回调处理
        $res = $this->parkPayHandler($result);

        if ($res!==true) {
            echo 'FAIL';
        }
        else {
            echo 'SUCCESS';
        }

        $info = $result['data'];
        //平台订单号
        $outTradeNo = $info['orderNo'];
        //微信支付订单
        $tradeNo    = $info['centerSeqId'];
        //金额（单位：分）
        $money      = $info['amount'];

        $modelTradeLog = new OnlineTrade();

        $pay_log_data  = $modelTradeLog->getLog($outTradeNo, 8);
        $subject       = $pay_log_data['subject'];

        $infoArray     = json_decode($subject, true);

        $checkMoney    = $infoArray['fee'];

        //TODO::支付成功后，校验支付金额与实际的费用是否一致
        //TODO::支付无误，调用哈泊接口
        if ($checkMoney == $money) {
            $HaboDock = new HaboDock();
            $HaboInfo = $HaboDock->setPaySign($infoArray, $tradeNo);

            //回调不成功 处理
            if (!$HaboInfo) {
                //如果回掉失败 隔10秒 再次调用
                sleep(10);

                $HaboInfo = $HaboDock->setPaySign($infoArray, $tradeNo);
                if (!$HaboInfo) {
                    pft_log('wepay_habo/callback', "支付金额不一致:平台订单号：" .$outTradeNo.'微信支付流水号:'.$tradeNo.'微信支付金额：'
                        .$money.'应支付金额:'.$checkMoney.'车牌号：'.$infoArray['carNo']);
                }
            }
        }else {
            //支付金额 不符 进入日志 不回调哈泊系统
            pft_log('wepay_habo/error', "支付金额不一致:平台订单号：" .$outTradeNo.'微信支付流水号:'.$tradeNo.'微信支付金额：'
                .$money.'应支付金额:'.$checkMoney.'车牌号：'.$infoArray['carNo']);

        }
    }

     /**
     * 哈泊订单支付成功处理
     *
     * @param array $result
     * @return mixed
     */
    protected function parkPayHandler(Array $result)
    {
        pft_log('wepay_habo/order', "订单数据:" . json_encode($result, JSON_UNESCAPED_UNICODE));
        //$modelHaboPark = new \Model\Member\HaboParkPay();
        $bunissHaboPark = new HaboPayBusiness();

        $pay_total_fee = $result['data']['amount'];
        $trade_no      = $result['data']['centerSeqId'];//微信订单号

        parse_str(str_replace('|', '&', $result['data']['centerInfo']), $certerInfoList);

        $json_sell     = json_encode(['merchantNo'=>$result['data']['merchantNo'], 'platformId'=>$result['data']['platformId']]);
        pft_log('wepay_habo/order', "failrd:$json_sell");
        $sourceT       = self::SOURCE_T;
        //微信
        $json_buy      = json_encode(['openid'=>$certerInfoList['openid'],'bankTradeNo'=>$result['data']['bankTradeNo'], 'bankOrderNo'=>$result['data']['bankOrderNo']]);

        $res = $bunissHaboPark->parkPayComplete($result['data']['orderNo'], $sourceT, $trade_no,$pay_total_fee, $json_buy,$json_sell);

        return $res;
    }
}