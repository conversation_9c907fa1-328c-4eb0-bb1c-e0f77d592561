<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 2/21-021
 * Time: 14:29
 *
 * 移动支付封装
 */
namespace Controller\pay;

use Bean\Request\Pay\JsApiPayRequestBean;
use Bean\Request\Pay\MiniJsApiPayRequestBean;
use Bean\Request\Pay\QrPayRequestBean;
use Bean\Request\Pay\AppPayRequestBean;
use Business\Captcha\RechargeCaptcha;
use Business\JsonRpcApi\PayService\CommonHandle;
use Business\Captcha\CaptchaApi;
use Business\Order\MergeOrder;
use Business\RiskManagement\ShumeiRiskCheck;
use Model\Order\OrderQuery;
use Business\Pay\PayBase;
use Business\Pay\PayCache;
use Business\Pay\UnifiedPayment;
use Library\ApplicationContext;
use Model\Finance\PayMerchant;
use Business\Wechat\Applet as AppletBiz;
use Process\Pay\PayHandle;

class MobilePay
{
    /**
     * @var $payObject PayInterface
     * pay_type   1:支付宝 2:微信 3:平安银行 4:威富通的银联wap（预留） 5:银联云闪付 6 头条抖音支付 7百度小程序
     */
    private $payObject = null;
    /**
     * 用来标记支付宝、微信刷卡支付是否票付通收款
     * @var bool
     */
    private $cardPayToPft  = false;
    private $unionMicropay = false;
    private $channelId     = 0;   //统一支付新业务id  0-不走统一收银台业务  1-招行独立收款业务,对于pay_service的PayChannelItem.class.php
    private $payType       = 2;

    /**
     * MobilePay constructor.
     */
    public function __construct()
    {
        if (\inWechatSmallApp(1)) {
            $input = file_get_contents('php://input');
            $_POST = json_decode($input, true);
        }
        pft_log('debug/pay', json_encode($_POST, JSON_UNESCAPED_UNICODE));
        $merchantId = I('post.merchant_id', 0, 'intval');
        $pay_type   = I('post.pay_type', 2, 'intval');

        //read merchant config
        $merchantConf     = load_config('other_mch', 'pay');
        $chinaUmsConfig   = load_config('chinaums','pay');
        $swiftPassConfig  = load_config('swiftpass','pay');
        $unionCloudConfig = load_config('union_cloud','pay');
        //$abchinaConfig    = load_config('abchina','pay');// 农行聚合
        $ccbConfig        = [];//load_config('ccb','pay');//建行
        $abchinaBmpConfig = load_config('abchina_bmp','pay');//农行bmp
        $greenRoad        = load_config('greenroad','pay');//成都绿聚支付
        $cmbConfig        = load_config('cmb_pay','pay'); //招行
        $baiDuConfig      = load_config('bai_du_pay','pay');
        $bocbankConfig    = load_config('bocbank_pay','pay');//中国银行
        $yopConfig        = load_config('yop_pay','pay');//易宝独立收款
        $icbcbankConfig   = load_config('icbc_bank_pay','pay');//建行独立收款

        // 为提高支付成功率，此处处理可能由于人为原因导致的支付方式选择错误而支付失败的问题
        if ($_GET['a'] == 'micropay' || $_GET['a']=='cardPay') {
            $prefixCodeConf = load_config('micropay_pay_auth_code', 'pay');
            $code_chk       = substr($_POST['auth_code'], 0, 2);
            if (in_array($code_chk, $prefixCodeConf['wepay'])) {
                $pay_type = 2;
                // 做好微信现有账户被停掉的准备，增加创新科技的收款账号
                if (!isset($merchantConf[$merchantId]) && isset($merchantConf[1])) {
                    $merchantId = 1;
                }
            } elseif (in_array($code_chk, $prefixCodeConf['alipay'])) {
                $pay_type = 1;
            } elseif (in_array($code_chk, $prefixCodeConf['union'])) {
                $pay_type = 5;
                $this->unionMicropay = true;
            }
        }
        $this->payType = $pay_type;
        // 是否mini云小程序支付
        $isApplet = I('post.is_applet');
        //支付宝小程序appid
        $miniAppid  = I('post.mini_appid', '');
        $appletBiz = new AppletBiz();

        // 判断缓存独立收款是否存在
        $appletSubAppId = I('post.applet_sub_appid');
        $merBiz = new \Business\Finance\PayMerchant();
        $code   = I('post.auth_code');
        $cloudPayWhite = load_config('cloud_pay_white','pay');//获取白名单用户，这边的用户可以使用第三方的云闪付接口
        //这边判断下云闪付的扫码付款
        if ($pay_type == 5  && isset($unionCloudConfig[$merchantId])) {
            if ($this->unionMicropay && !$unionCloudConfig[$merchantId]['underline']){
                //如果没开启扫码就继续往下走吧
            } else{
                $unionData       = $unionCloudConfig[$merchantId];
                $this->payObject = new UnionPay($unionData,$merchantId,$pay_type);
                return true;
            }
        }
        /*elseif ($pay_type == 5 && !in_array($merchantId,$cloudPayWhite)
            && !isset($ccbConfig[$merchantId]) && !isset($abchinaBmpConfig[$merchantId])
        ) // 农行、建行支持银行APP里面的付款码
        {
            $res = json_encode(['code' => 400,'data'=> [],'msg'=> '商户暂不支持云闪付码支付'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            echo $res;
            exit;
        }*/
        if ($pay_type == 6){
            $this->payObject = new ToutiaoPay();
            return true;
        }

        //这边判断银联商务的渠道
        if (isset($chinaUmsConfig[$merchantId])) {
            if (isset($code) && !empty($code)) {
                $chinaUmsConfigDb   = $merBiz->getChinaUmsMerchantConfig($merchantId,'',1);
            } else {
                $chinaUmsConfigDb   = $merBiz->getChinaUmsMerchantConfig($merchantId,'',0);
            }
            if (!empty($chinaUmsConfigDb)) { //银联商务的优先级最大,鹏总说的
                //切换到支付中心  20220519开发
                $this->channelId = 3;
                if ($chinaUmsConfigDb['type'] == 1){  //被扫的
                    $this->micropay();
                    exit;
                }else{  //走 order的
                    $this->order();
                    exit;
                }
//                $this->payObject = new ChinaUmsPay($chinaUmsConfigDb, $merchantId,$pay_type);
            }
        }//这边是威富通渠道的
        elseif (isset($swiftPassConfig[$merchantId])){
            if ($_GET['a'] == 'order'){
                $this->channelId = 100;
                $this->order();
                exit;
            }else if ($_GET['a'] == 'micropay'){
                $this->channelId = 100;
                $this->micropay();
                exit;
            }
        }
        elseif (isset($cmbConfig[$merchantId]) && in_array($_GET['a'],['micropay','order'])){
            if ($_GET['a'] == 'order' && $cmbConfig[$merchantId]['online'] == true && empty($isApplet)){
                $this->channelId =12;
                $this->order();
                exit;
            }else if ($_GET['a'] == 'micropay'){
                $this->channelId =12;
                $this->micropay();
                exit;
            }
        }
        elseif (isset($baiDuConfig[$merchantId]) && $pay_type == 7){  //百度小程序
            $this->channelId = 13;
            $this->order();
            exit;
        }
        elseif (isset($bocbankConfig[$merchantId]) && in_array($_GET['a'],['order','micropay']) && empty($isApplet)) {
            if ($_GET['a'] == 'order'){
                $this->channelId =14;
                $this->order();
                exit;
            }else if ($_GET['a'] == 'micropay'){
                $this->channelId =14;
                $this->micropay();
                exit;
            }
        }
        elseif (isset($yopConfig[$merchantId]) && in_array($_GET['a'],['micropay','order']) && empty($isApplet) && empty($miniAppid)){
            $isQr     = I('post.is_qr',0,'intval');
            if ($_GET['a'] == 'order' && $isQr != 3){
                $this->channelId =5;
                $this->order();
                exit;
            }else if ($_GET['a'] == 'micropay'){
                $this->channelId =5;
                $this->micropay();
                exit;
            }
        }
        elseif (isset($icbcbankConfig[$merchantId]) && in_array($_GET['a'],['micropay','order']) && empty($isApplet)){
            if ($_GET['a'] == 'micropay'){
                $this->channelId =15;
                $this->micropay();
                exit;
            }
            //
            if ($_GET['a'] == 'order'){
                $this->channelId =15;
                $this->order();
                exit;
            }
        }
        elseif (isset($abchinaBmpConfig[$merchantId]) && in_array($_GET['a'],['micropay'])){
            if ($_GET['a'] == 'micropay'){
                $this->channelId =16;
                $this->micropay();
                exit;
            }
        }
        elseif (isset($greenRoad[$merchantId]) && in_array($_GET['a'],['order'])){
            if ($_GET['a'] == 'order'){
                $this->channelId =19;
                $this->order();
                exit;
            }
        }
        //存在用农行，并且是扫码和微信公众号支付，并且不是主动扫码的模式，并且如果是微信公众号和小程序的话要有appid
        // && in_array($_GET['a'],['micropay','order'])
        //elseif (isset($abchinaConfig[$merchantId]) && !I('post.is_qr') && empty($isApplet))
        //{
        //    if ($_GET['a'] == 'micropay' || ($_GET['a'] == 'order' && $abchinaConfig[$merchantId]['onLine']==true)) {
        //        $this->payObject = new AbchinaPay($merchantId);
        //    }
            // if ($_GET['a']=='order' && $abchinaConfig[$merchantId]['onLine'] != true) {
            //
            // } else {
            //     $this->payObject = new AbchinaPay($merchantId);
            // }
        //}
        elseif (isset($ccbConfig[$merchantId]) && in_array($_GET['a'],['micropay','order']) ) {
            //
            // if ($_GET['a'] == 'micropay' || ($_GET['a'] == 'order' && $ccbConfig[$merchantId]['onLine']==true)) {
            //     $this->payObject = new CCBBank($merchantId);
            // }
            if ($ccbConfig[$merchantId]['onLine'] != true && $_GET['a'] == 'order') {

            } else {
                $this->payObject = new CCBBank($merchantId);
            }
        }

        elseif ($_GET['a'] == 'order' && !empty($_POST['force_paycenter'])) {
            $this->channelId =100;
            $this->order();
            exit;
        }

        if (is_null($this->payObject)) {
            //切换到支付中心的用户
            if ($merchantId>0 && in_array($_GET['a'],['micropay','order'])) {
                $isQr     = I('post.is_qr',0,'intval');
                if ($_GET['a'] == 'order'){
                    $this->channelId =100;
                    $this->order();
                    exit;
                }else if ($_GET['a'] == 'micropay'){
                    $this->channelId =100;
                    $this->micropay();
                    exit;
                }
            }

            //票付通微票房小程序
            if ($merchantId > 0 && $_GET['a'] == 'order' && $isApplet && $appletSubAppId == 'wx2f45381cd36a6400') {
                $this->channelId =100;
                $this->order();
                exit;
            }

            $this->cardPayToPft = true;
            if ($pay_type == 1) {
                if ($this->isPlatformOrderReferer()) {
                    $this->payObject = new CBYeePay();
                } else {
                    $this->payObject = new Alipay();
                }
            } elseif ($pay_type == 2) {
                //$this->payOjbect = new WxPay();
                //
                // //微商城、微平台微信支付收款使用易宝渠道
                if ( ENV=='PRODUCTION'
                    && ($_POST['appid'] == 'wxd72be21f7455640d' || $_POST['appid']=='wxf21fd808fe27259e')
                    && (isset($_POST['shop_host']) || isset($_POST['success_url'])))
                    //&& (strpos($_POST['shop_host'], '123624.12301')!==false) || strpos($_POST['success_url'],'123624.12301')!==false)
                {
                    $this->payObject = new CBYeePay();
                } elseif ($this->isPlatformOrderReferer()) {
                    $this->payObject = new CBYeePay();
                } else {
                    $this->payObject = new WxPay();
                }
            }
        }

        if (is_null($this->payObject)) {
            $res = json_encode(['code' => 400,'data'=> [],'msg'=> '商户暂不支持此支付方式'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            echo $res;
            exit;
        }
    }

    private function isPlatformOrderReferer()
    {
        // 票付通微平台小程序----临时处理。小程序客户端应用需要升级
        if ($_POST['appid']=='wxe0d4cb8dbc61dbb4'  ) {
            return true;
        }
        // 支付宝二维码充值
        if (strpos($_SERVER['HTTP_REFERER'], 'recharge.html')!==false && $_POST['pay_type'] == 1) {
            return $_POST['qr_pay']==1 || $_POST['is_qr']==1 || $_POST['pay_scen']==2;
        }
        // 微平台微信充值
        if ($_POST['pay_type']==2 && $_SERVER['HTTP_REFERER'] == 'https://wx.12301.cc/html/recharge.html') {
            return true;
        }
        // || strpos($_SERVER['HTTP_REFERER'], 'recharge.html')
        return strpos($_SERVER['HTTP_REFERER'], 'new/orderpay.html') || strpos($_SERVER['HTTP_REFERER'], 'recharge.html');
    }

    public function renew()
    {
        $this->payObject->renew();
    }

    public function order()
    {
        if ($this->channelId) {
            //todo 目前只实现jsApi的统一支付
            $payParamsHandleProcess = new PayHandle();
            $resultParams           = $payParamsHandleProcess->orderPayHandle();
            if ($resultParams[0]!= 200){
                $res = json_encode(['code' => 400,'data'=> [],'msg'=> $resultParams[1]],
                    JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                echo $res;
                exit;
            }
            $handleParams = $resultParams[2];
            $handleParams['channelId'] = $this->channelId;
            if ($handleParams['isApplet'] == 1) {
                $miniJsApiPayRequestBean = new MiniJsApiPayRequestBean($handleParams);
                $unifiedPaymentBiz   = new UnifiedPayment();
                $result              = $unifiedPaymentBiz->unifiedMiniJsApiPay($miniJsApiPayRequestBean);
                PayBase::setPayDailyRecord(100,'小程序支付',I('post.'),$result);
                echo json_encode($result,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                exit;
            }  elseif ($handleParams['isQr'] == 1 || $handleParams['isQr'] == 2) {  //扫码支付暂没实现
                $qrPayRequestBean   = new QrPayRequestBean($handleParams);
                $unifiedPaymentBiz  = new UnifiedPayment();
                $result = $unifiedPaymentBiz->unifiedQrPay($qrPayRequestBean);
                PayBase::setPayDailyRecord(100,'扫码支付',I('post.'),$result);
                echo json_encode($result,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                exit;
            } elseif ($handleParams['isQr'] == 3) {  //app支付
                $appPayRequestBean   = new AppPayRequestBean($handleParams);
                $unifiedPaymentBiz  = new UnifiedPayment();
                $result = $unifiedPaymentBiz->unifiedAppPay($appPayRequestBean);
                PayBase::setPayDailyRecord(100,'扫码支付',I('post.'),$result);
                echo json_encode($result,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                exit;
            } else {
                $shopHost = I('post.shop_host', '', 'strval');
                $successUrl = I('post.success_url', '', 'strval');
                if (($shopHost || $successUrl) && !empty($_SERVER['HTTP_DEVICEID'])){
                    $this->shumeiRiskCheck();
                }
                //公众号支付
                $jsApiPayRequestBean = new JsApiPayRequestBean($handleParams);
                $payServiceApi = new CommonHandle();
                $redirectRes = $payServiceApi->getRedirectUrlRpc($jsApiPayRequestBean->getOutTradeNo());
                if ($redirectRes['code'] == 200){
                    $jsApiPayRequestBean->setFontUrl($redirectRes['data']['redirect_url']);
                }
                $unifiedPaymentBiz   = new UnifiedPayment();
                $result              = $unifiedPaymentBiz->unifiedJsApiPay($jsApiPayRequestBean);
                PayBase::setPayDailyRecord(100,'jsapi支付',I('post.'),$result);
                echo json_encode($result,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                exit;
            }
        } else {
            $outTradeNo = I('post.out_trade_no', '', 'strval,trim');
            $verify     = I('post.verify', 0, 'intval');
            $isQr       = I('post.is_qr',0,'intval');
            $terminalId = I('post.terminal',0,'intval');
            if ($terminalId > 0) {
                PayCache::setPayTerminalId($outTradeNo, $terminalId);
            }
            // 判断是否有购即验参数
            if ($verify) {
                PayCache::setOrderVerfify($outTradeNo, $verify);
            }
            if ($isQr == 3 && !in_array(get_class($this->payObject),['Controller\pay\WxPay','Controller\pay\Alipay'])){
                $res = json_encode(['code' => 400,'data'=> [],'msg'=> '该支付渠道还未开通APP支付服务，详情请联系客服'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                echo $res;
                exit;
            }
            $this->payObject->order();
        }
    }

    private function shumeiRiskCheck()
    {
        $captchaCode = I('post.captchaCode', '', 'strval'); //滑块校验码
        $outTradeNo  = I('post.out_trade_no', '', 'strval,trim');
        $openid      = I('post.openid', null);//微商城微信支付会有传微信用户openid
        $account     = I('post.account', '');//微平台产品预订微信支付会有传account参数
        $shopHost    = I('post.shop_host', '', 'strval');
        $account     = $account ?: $openid;
        if ($shopHost && $openid){
            //判断是微商城来源直接返回
            return [];
        }
        $mergeOrder  = new MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string)$outTradeNo);
        }
        $money = number_format($totalFee / 100, 2, '.', '');
        if (empty($captchaCode)) {
            $riskCheck             = new ShumeiRiskCheck();
            $virtualOrderEventData = [
                'product' => '购买商品',
                'orderId' => $outTradeNo,
                'price'   => floatval($money),
            ];
            $orderCheckRes         = $riskCheck->shumeiCheck('virtualOrder', 3, $account, $virtualOrderEventData);

            $paymentEventData = [
                'method'  => 'widget',
                'channel' => 'widget',
                'amount'  => floatval($money),
                'orderId' => $outTradeNo,
            ];
            $payCheckRes      = $riskCheck->shumeiCheck('payment', 3, $account, $paymentEventData);
            if (($payCheckRes['code'] == 200 && $payCheckRes['data']['riskLevel'] == 'REJECT') || ($orderCheckRes['code'] == 200 && $orderCheckRes['data']['riskLevel'] == 'REJECT')) {
                $this->apiReturn(209, ['need_captcha' => true], '请验证滑块');
            }
        } else {
            $captchaCheck = new CaptchaApi();
            $captchaRes   = $captchaCheck->handleSecondCheck($captchaCode);
            if ($captchaRes !== true) {
                $this->apiReturn(209, ['need_captcha' => true], '请验证滑块');
            }
        }
    }

    public function cardSolutionRecharge()
    {
        $this->payObject->cardSolutionRecharge();
    }

    public function apiReturn($code, $data = [], $msg = '', $object = false)
    {
        if ($data == [] && $object) {
            $data = new \stdClass;
        }
        $data = [
            'code' => $code,
            'data' => $data,
            'msg'  => $msg,
        ];
        $res = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        echo $res;
        if (PHP_SAPI == 'cli') {
            return true;
        }
        exit();
    }

    /**
     * 校验是否启用验证码，用于处理前端时候显示滑动验证码组件
     */
    public function checkCaptchaEnable()
    {
        $isEnable = RechargeCaptcha::checkRechargeSubmitCaptchaEnable();
        $result = ['is_enable' => $isEnable];
        $this->apiReturn(200, $result, 'success');
    }
    /**
     * 下单校验验证码是否处理
     */
    protected function handleCaptcha()
    {
        if (!$_POST['req_from']) {
            return;
        }

        $captchaCode = $_POST['captcha_code'];
        $res = RechargeCaptcha::handleRechargeSubmitCaptcha($captchaCode);
        if (!is_array($res)) {
            return;
        }
        list($code, $msg, $data) = $res;
        if (200 != $code) {
            // 验证码错误
            $this->apiReturn($code, $data, $msg);
        }
    }

    public function recharge()
    {
        // 处理滑动验证码
        //$this->handleCaptcha();
        if ( $_POST['sanya_flag'] == 'platform_recharge' || (isset($_POST['use_env']) && in_array($_POST['use_env'], [2]))) {
            $this->payObject = new CBYeePay(0);
        } else if (isset($_POST['use_env']) && in_array($_POST['use_env'], [0, 1])) {
            $this->payObject = new CBYeePay(0);
            //支付方式是微信并且是微平台小程序或者来源是微平台公众号的支付方式，则走收银台
            if ($_POST['pay_type'] == PaymentCenter::PAY_TYPE_WECHAT) {
                $this->payObject = new PaymentCenter();
            }
        }
        // 此处针对测试账号开放
        if ($_POST['aid'] == 55 && $_POST['sanya_flag'] == 'cgp_test') {
            $merchantId = 55;
            //$merBiz = new \Business\Finance\PayMerchant();
            //$config = $merBiz->getMerchantConfig(\Business\Finance\PayMerchant::WEPAY, $merchantId);
            $this->payObject = new CBYeePay($merchantId);
        }
        //分销商给供应商还款
        if ($_POST['sanya_flag'] == 'wx_repayment') {
            //分销商id
            $memberId = $_POST['member_id'];
            //供应商id
            $sid      = $_POST['sid'];
            if (!$memberId || !$sid) {
                $res = json_encode(['code' => 400,'data'=> [],'msg'=> '分销商给供应商还款,参数错误，详情请联系客服'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                echo $res;
                exit;
            }
            $repayInfo = (new \Business\JavaApi\Member\MemberClearingWay())->getMemeberClearingWay($sid, $memberId);
            if ($repayInfo['code'] != 200) {
                $res = json_encode(['code' => 400,'data'=> [],'msg'=> '获取当前分销商还款模式失败'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                echo $res;
                exit;
            }
            $repayMode = $repayInfo['data']['repayMode'] ?? 0;
            if ($repayMode) {
                $res = json_encode(['code' => 400,'data'=> [],'msg'=> '该供应商设置仅限线下还款模式'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                echo $res;
                exit;
            }
        }

        if (!method_exists($this->payObject,'recharge')){
            $res = json_encode(['code' => 400,'data'=> [],'msg'=> '该支付渠道还未开通充值服务，详情请联系客服'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            echo $res;
            exit;
        }
        $this->payObject->recharge();
    }

    public function micropay()
    {
        //入口保存信息CLIENTID：手持机闸机类设备码    WEB_CLIENT_ID云票务设备码
        $clientId = $_SERVER['HTTP_WEB_CLIENT_ID'] ?? $_SERVER['HTTP_CLIENTID'];
        $clientId = empty($clientId) ? "" : $clientId;
        ApplicationContext::set("device_key", $clientId);

        //---- 线下被扫支付过程,上锁禁止支付未完成而订单被取消----START
        $jsonRpcParams = [I('post.ordernum'), $this->channelId??0];
        $rpcClient = new \Library\JsonRpc\PftRpcClient('pft_scenic_local_service');
        $data = $rpcClient->call('Order/PaymentLock/lock', $jsonRpcParams, 'scenic');
        $this->payObject->jsonRpcClient = $rpcClient;
        if($data['code'] != 200) {
            pft_log('debug/micropay_lock', json_encode([$jsonRpcParams, $data], JSON_UNESCAPED_SLASHES));
        }
        //---- 线下被扫支付过程,上锁禁止支付未完成而订单被取消----END

        if ($this->channelId){  //新版支付统一入口，走收银台
            $payParamsHandleProcess = new PayHandle();
            $resultParams = $payParamsHandleProcess->microPayHandle();
            if ($resultParams[0] != 200){
                $res = json_encode(['code' => 400,'data'=> [],'msg'=> $resultParams[1]], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                echo $res;
                exit;
            }
            if ($this->payType == 5) {
                $this->payType = 3;
            }
            $resultParams[2]['channelId'] = $this->channelId;
            $resultParams[2]['payType']   = $this->payType;
            $microPayBean = new \Bean\Request\Pay\MicroPayRequestBean($resultParams[2]);
            $unifiedPaymentBiz = new UnifiedPayment();
            $unifiedPaymentBiz->jsonRpcClient = $rpcClient;
            $result = $unifiedPaymentBiz->unifiedMicroPay($microPayBean);
            #/alidata/log/site/log_system/orderpaylog/pay/debug/
            PayBase::setPayDailyRecord(100,'付款码请求',I('post.'),$result);
            echo json_encode($result,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            exit;
        }else{
            // 微信条码支付改用易宝支付
            $facePay = I('post.facepay',0);
            //277352,彭山景区id，彭山景区用微信或支付宝的原生通道测试一周看下成功率
            // $useOriginChannelMembers = [
            //     277352,
            // ];
            if (($_POST['appid'] == 'wxd72be21f7455640d' ||  $this->cardPayToPft === true) && $facePay != 1) {
                $this->payObject = new CBYeePay(0);
                // $mid = I('post.merchant_id', 0, 'intval');
                // if (!in_array($mid, $useOriginChannelMembers)) {
                //     $this->payObject = new CBYeePay(0);
                // }
            }
            $this->payObject->micropay();
        }
    }

    public function parkPay()
    {
        $this->payObject->parkPay();
    }

    public function payResultCheck()
    {
        //判断是否使用聚合支付通道 - 通道已经关闭
        if ($_POST['aid'] == 55 && $_POST['sanya_flag'] == 'cgp_test') {
            $merchantId = 55;
            //$merBiz = new \Business\Finance\PayMerchant();
            //$config = $merBiz->getMerchantConfig(\Business\Finance\PayMerchant::WEPAY, $merchantId);
            $this->payObject = new CBYeePay($merchantId);
        } elseif ($this->isPlatformOrderReferer()) {
            $this->payObject = new CBYeePay();
        }elseif ($_POST['pay_type']==6){
            $this->payObject = new ToutiaoPay();
        }
        $this->payObject->payResultCheck();
    }

    //年卡续费
    public function annualRenew()
    {
        $this->payObject->annualRenew();
    }

    /**
     * 扫码支付
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @return array
     */
    public function cardPay()
    {
        if (($_POST['appid'] == 'wxd72be21f7455640d' ||  $this->cardPayToPft === true)) {
            $this->payObject = new CBYeePay(0);
        }
        $this->payObject->cardPay();
    }
    /*
     * 仙盖山充值
     */
    public function xgsRecharge()
    {
        $this->payObject->xgsRecharge();
    }

    //退卡退款
    public function refund () {
        $this->payObject->Refund();
    }
    //退卡退款
    public function smilePayInit () {
        $this->payObject->smilePayInit();
    }
    //非订单类微信内支付和扫码支付
    public function specialOrderPay()
    {
        $this->payObject->specialOrderPay();
    }

    public function qrPay(){
        $this->payObject = new CBYeePay(0);
        if (!method_exists($this->payObject,'qrPay')){
            $res = json_encode(['code' => 400,'data'=> [],'msg'=> '该支付渠道还未开通支付服务，详情请联系客服'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            echo $res;
            exit;
        }
        $payParamsHandleProcess = new PayHandle();
        $handleData = $payParamsHandleProcess->payLogHandle();
        if ($handleData[0] != 200){
            $res = json_encode(['code' => 400,'data'=> [],'msg'=> $handleData[1]], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            echo $res;
            exit;
        }
        $this->payObject->qrPay($handleData[2]);
    }

    public function jsApiPay(){
        // 票付通收款，默认走易宝支付的通道。
        if (I('post.appid') == PFT_WECHAT_APPID) {
            $this->payObject = new CBYeePay(0);
        }
        if (!method_exists($this->payObject,'jsApiPay')){
            $res = json_encode(['code' => 400,'data'=> [],'msg'=> '该支付渠道还未开通支付服务，详情请联系客服'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            echo $res;
            exit;
        }
        $payParamsHandleProcess = new PayHandle();
        $handleData = $payParamsHandleProcess->payLogHandle();
        if ($handleData[0] != 200){
            $res = json_encode(['code' => 400,'data'=> [],'msg'=> $handleData[1]], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            echo $res;
            exit;
        }
        $this->payObject->jsApiPay($handleData[2]);
    }


    public function qrCommonNotifyTouch(){
        if (!method_exists($this->payObject,'qrCommonNotifyTouch')){
            $res = json_encode(['code' => 400,'data'=> [],'msg'=> '该支付渠道还未开通充值服务，详情请联系客服'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            echo $res;
            exit;
        }
        $this->payObject->qrCommonNotifyTouch();
    }
    /**
     * app支付
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @return array
     */
    public function appApiPay(){
        if (!method_exists($this->payObject,'appApiPay')){
            $res = json_encode(['code' => 400,'data'=> [],'msg'=> '该支付渠道还未开通支付服务，详情请联系客服'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            echo $res;
            exit;
        }
        $payParamsHandleProcess = new PayHandle();
        $handleData = $payParamsHandleProcess->payLogHandle();
        if ($handleData[0] != 200){
            $res = json_encode(['code' => 400,'data'=> [],'msg'=> $handleData[1]], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            echo $res;
            exit;
        }
        $this->payObject->appApiPay($handleData[2]);
    }

    /**
     * 临时提供给玩聚小程序校验独立收款配置是否开启的功能，过度方案。
     * @author: Guangpeng Chen
     * @date: 2022/4/12
     */
    public function isConfigOpen()
    {
        $merchantId    = I('get.merchant_id',0, 'intval');
        $merchantConf  = load_config('other_mch', 'pay');
        if (!isset($merchantConf[$merchantId])) {
            echo 400;
        } else {
            echo 200;
        }
    }
}