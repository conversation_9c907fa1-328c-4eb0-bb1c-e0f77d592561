<?php
namespace Controller\pay;

use Bean\Request\Pay\PayNotifyRequestBean;
use Business\AppCenter\Payment as PaymentBiz;
use Business\CardSolution\Order;
use Business\Finance\AccountMoney;
use Business\Pay\PayBase;
use Business\Pay\UnifiedPayment;
use Bean\Request\Pay\SettleNotifyContentRequestBean;
use Controller\Finance\Recharge;
use Business\ExclusiveAccount\SpecialedAccount;

class CommonPayNotify{
    /**
     * 统一收银台支付回调地址
     * <AUTHOR>
     * @date   2022-01-13
     *
     *
     * @return mixed
     */
    public function payNotify(){
        try {
            $request = file_get_contents("php://input");
            if (!$request){
                exit('FAIL');
            }
            PayBase::setPayDailyRecord(100,'回调',$request,[]);
            $requestData = json_decode($request,true);
            $payNotifyRequestBean = new PayNotifyRequestBean($requestData);
            $unifiedPaymentBiz = new UnifiedPayment();
            $result = $unifiedPaymentBiz->unifiedNotify($payNotifyRequestBean);
            PayBase::setPayDailyRecord(100,'回调结果',$request,$result);
            if ($result['code'] != 200){
                exit('FAIL');
            }
            exit('SUCCESS');
        } catch (\Throwable $e) {
            $logData = [
                'msg'=>$e->getMessage(),
                'line'=>$e->getLine(),
                'file'=>$e->getFile(),
            ];
            pft_log('common_notify_error',json_encode($logData));
            exit('FAIL');
        }
    }

    /**
     * 平台账户充值回调通知
     */
    public function platformRechargeNotify()
    {
        $request              = file_get_contents("php://input");
        if (empty($request)) {
            exit("INVALID REQUEST");
        }
        $requestData          = json_decode($request, true);
        $payNotifyRequestBean = new PayNotifyRequestBean($requestData);
        $unifiedPaymentBiz    = new UnifiedPayment();
        $decryptRes           = $unifiedPaymentBiz->decryptNotifyData($payNotifyRequestBean->getContent()->toArrayWithMapping(null,
            1, true));
        PayBase::setPayDailyRecord(100,'支付中心充值回调通知 1',$request,$decryptRes);

        if (!$decryptRes) {
            exit('FAIL');
        }
        $content         = $requestData['content']['biz_content'];
        $accountMoneyBiz = new AccountMoney();
        $sourceT         = Recharge::RECHARGE_SOURCET;
//        $payMode = $payNotifyRequestBean->getContent()->getBizContent()->getPayMode();
//        $sourceToPaymode = load_config('sourceT_pay_mode_map');
//        $sourceT       =  array_search($payMode,$sourceToPaymode);

        $rechargeRes     = $accountMoneyBiz->recharge($content['out_trade_no'], $sourceT, $content['trade_no'],
            $content['amount'], '','', PFT_WECHAT_APPID, PFT_WECHAT_APPID);

        PayBase::setPayDailyRecord(100,'支付中心充值回调通知',$content,$rechargeRes, ['sourceT'=>$sourceT]);
        if ($rechargeRes !== true) {
            echo 'FAIL';
        } else {
            echo 'SUCCESS';
        }
    }

    /**
     * 统一收银台退款回调地址
     * <AUTHOR>
     * @date   2022-01-13
     *
     *
     * @return mixed
     */
    public function refundNotify(){

    }
    /**
     * 统一收银台业务回调地址
     * <AUTHOR>
     * @date   2022-01-13
     *
     *
     * @return mixed
     */
    public function bizNotify(){

    }

    /**
     * 清算通知
     * <AUTHOR>
     * @date   2022-6-8
     *
     * @return mixed
     */
    public function sellteNotify()
    {
        try {

            $request     = file_get_contents("php://input");
            $requestData = json_decode($request,true);
            $content     = $requestData['content'] ?? [];

            $settleNotifyRequestBean = new SettleNotifyContentRequestBean($content);
            $unifiedPaymentBiz       = new UnifiedPayment();
            $result = $unifiedPaymentBiz->settleNotify($settleNotifyRequestBean);

            $logData = [
                'requst' => $request,
                'result' => $result,
            ];
            pft_log('order_pay_settle_notify',json_encode($logData));

            if ($result['code'] != 200){
                echo 'FAIL';
            } else {
                echo 'SUCCESS';
            }

        } catch (\Throwable $e) {

            $logData = [
                'requst' => $request ?? '',
                'result' => $result ?? '',
                'msg'=>$e->getMessage(),
                'line'=>$e->getLine(),
                'file'=>$e->getFile(),
            ];
            pft_log('order_pay_settle_notify',json_encode($logData));

            echo 'FAIL';

        }
    }


    /**
     * 支付中心支付完成后
     *
     * @date 2022/08/12
     * @auther yangjianhui
     * @return bool
     */
    public function afterOneCardPay()
    {
        $request              = file_get_contents("php://input");
        $requestData          = json_decode($request, true);
        $payNotifyRequestBean = new PayNotifyRequestBean($requestData);
        $unifiedPaymentBiz    = new UnifiedPayment();
        $decryptRes           = $unifiedPaymentBiz->decryptNotifyData($payNotifyRequestBean->getContent()->toArrayWithMapping(null,
            1, true));
        if (!$decryptRes) {
            exit('FALSE');
        }
        $orderBiz = new Order();
        $result   = $orderBiz->afterOneCardPay($requestData['content']['biz_content']);
        if ($result['code'] != 200) {
            pft_log('pre_card_pay/error', json_encode($result, JSON_UNESCAPED_UNICODE) . "支付参数" .
                                          json_encode($requestData, JSON_UNESCAPED_UNICODE));

            exit('FALSE');
        }

        exit('SUCCESS');
    }

    /**
     * 平台专项预存服务费回调通知
     */
    public function platformSpecialDepositsNotify()
    {
        $request = file_get_contents("php://input");
        if (empty($request)) {
            exit("INVALID REQUEST");
        }
        $requestData          = json_decode($request, true);
        $payNotifyRequestBean = new PayNotifyRequestBean($requestData);
        $unifiedPaymentBiz    = new UnifiedPayment();
        $decryptRes           = $unifiedPaymentBiz->decryptNotifyData($payNotifyRequestBean->getContent()->toArrayWithMapping(null,
            1, true));
        PayBase::setPayDailyRecord(100,'支付中心充值回调通知 1', $request, $decryptRes);

        if (!$decryptRes) {
            exit('FAIL');
        }
        $content         = $requestData['content']['biz_content'];
        $payMode         = $payNotifyRequestBean->getContent()->getBizContent()->getPayMode();
        $sourceToPaymode = load_config('sourceT_pay_mode_map');
        $sourceT         = array_search($payMode, $sourceToPaymode);

        $specialedAccountBiz = new SpecialedAccount();
        $rechargeRes         = $specialedAccountBiz->platformSpecialDepositsAfter($content['out_trade_no'], $sourceT, $content['trade_no'], $content['amount']);

        PayBase::setPayDailyRecord(100,'支付中心充值回调通知',$content, $rechargeRes, ['sourceT' => $sourceT]);
        if ($rechargeRes !== true) {
            echo 'FAIL';
        } else {
            echo 'SUCCESS';
        }
    }

    /**
     * 套餐、应用等服务费回调通知
     */
    public function platformPackageNotify()
    {
        $request = file_get_contents("php://input");
        if (empty($request)) {
            exit("INVALID REQUEST");
        }
        $requestData          = json_decode($request, true);
        $payNotifyRequestBean = new PayNotifyRequestBean($requestData);
        $unifiedPaymentBiz    = new UnifiedPayment();
        $decryptRes           = $unifiedPaymentBiz->decryptNotifyData($payNotifyRequestBean->getContent()->toArrayWithMapping(null,
            1, true));
        PayBase::setPayDailyRecord(100,'支付中心充值回调通知 1', $request, $decryptRes);

        if (!$decryptRes) {
            exit('FAIL');
        }
        $content         = $requestData['content']['biz_content'];
        $payMode         = $payNotifyRequestBean->getContent()->getBizContent()->getPayMode();
        $sourceToPaymode = load_config('sourceT_pay_mode_map');
        $sourceT         = array_search($payMode, $sourceToPaymode);

        $paymentBiz  = new PaymentBiz();
        $rechargeRes = $paymentBiz->platformBuyPackageAfter($content['out_trade_no'], $sourceT, $content['trade_no'], $content['amount']);

        PayBase::setPayDailyRecord(100,'支付中心充值回调通知',$content, $rechargeRes, ['sourceT' => $sourceT]);
        if ($rechargeRes !== true) {
            echo 'FAIL';
        } else {
            echo 'SUCCESS';
        }
    }
}