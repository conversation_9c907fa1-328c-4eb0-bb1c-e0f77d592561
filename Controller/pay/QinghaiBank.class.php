<?php
/**
 * 青海银行独立收款
 * User: lan<PERSON><PERSON>
 * Date: 2021/10/14
 * Time: 9:40
 */

namespace Controller\pay;

use Business\Order\MergeOrder;
use Business\Order\Payment;
use Business\Pay\PayCache;
use Library\Cache\Cache;
use Model\Order\OrderQuery;
use Model\TradeRecord\OnlineTrade;
use Process\Order\Validate\MergeOrderPreCheck\OrderPreCheckException;

class QinghaiBank extends OnlinePayBase
{

    private $reqId;
    const   SOURCE_T = 29;
    const   TRACK_SOURCE = 60;
    const   CACHE_PREFIX = 'pay:qinghaiyh:';

    public function __construct()
    {
        $this->reqId      = $_SERVER['REQUEST_ID']?:uniqid();
    }


    /**
     *
     * 获取支付二维码
     * User: lanwanhui
     * Date: 2021/10/15
     *
     * @throws \Exception
     */
    public function order()
    {
        $logData = [
            'post' =>$_POST,
            'refer'=>$_SERVER['HTTP_REFERER'],
            'agent'=>$_SERVER['HTTP_USER_AGENT'],
            'reqid'=>$_SERVER['REQUEST_ID'],
        ];
        pft_log('qinghaipay/order_req', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

        //平台订单号
        $outTradeNo = I('out_trade_no', '', 'strval,trim');
        //供应商id
        $merchantId = I('merchant_id', 0, 'intval');
        //设备特征码
        $clientId   = I('clientId', '', 'strval,trim');
        //订单主体说明
        $subject    = I('subject', '', 'strval,trim');
        $subject    = mb_substr($subject, 0, 20, 'utf-8') . "[{$outTradeNo}]";

        if (!$outTradeNo || !$merchantId || !$clientId) {
            parent::apiReturn(401, [], '参数缺失');
        }

        $qinghaiConfig   = load_config('qinghai_pay','pay');
        if (empty($qinghaiConfig[$merchantId])) {
            parent::apiReturn(401, [], '无该渠道付款权限');
        }

        if (empty($qinghaiConfig[$merchantId][$clientId])) {
            parent::apiReturn(401, [], '该设备未配置支付渠道');
        }

        //机具标识
        $machNo        =  $qinghaiConfig[$merchantId][$clientId]['machNo'];
        //独立收款商户号
        $qrCode        = $qinghaiConfig[$merchantId][$clientId]['qrCode'];
        //签名秘钥
        $signSecret    = $qinghaiConfig[$merchantId][$clientId]['signSecret'];
        //加密秘钥
        $encryptSecret = $qinghaiConfig[$merchantId][$clientId]['encryptSecret'];
        //回调签名秘钥
        $notifySecret = $qinghaiConfig[$merchantId]['notifySecret'];

        $mergeOrder = new MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
            try {
                $mergeOrder->handlerCombinePayLog($outTradeNo, '', self::SOURCE_T, $merchantId);
            } catch (OrderPreCheckException $e) {
                parent::apiReturn(401, [], $e->getMessage());
            }
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string) $outTradeNo);
        }

        if (empty($totalFee)) {
            parent::apiReturn(401, [], '获取支付金额失败');
        }

        $money    = number_format($totalFee / 100, 2, '.', '');
        //生成url
        $url = $this->createUrl($outTradeNo,$money,$subject,$machNo,$qrCode,$signSecret,$encryptSecret);

        $output = ['outTradeNo' => $outTradeNo, 'qrUrl' => $url];

        $tradeModel = new OnlineTrade();
        $ret        = $tradeModel->addLog($outTradeNo, $money, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_ORDER, '', $merchantId);
        if (!$ret) {
            parent::apiReturn(401, [], '支付记录生成失败');
        }
        $this->setCacheInfo($outTradeNo, $notifySecret, $totalFee);
        echo json_encode(['code' => 200, 'data' => $output, 'status' => 'ok', 'msg' => 'success']);

    }

    /**
     * 支付成功后的回调通知,回调地址配置在三方
     * User: lanwanhui
     * Date: 2021/10/15
     */
    public function orderNotify()
    {
        $jsonData = file_get_contents("php://input");

        //$jsonData='{"trade_state":"SUCCESS","total_fee":"0.01","sign":"3EEA0169AA669684EFB3BDAF87530927831F85E35261CF05A48ABE1F7EAD55F2","trade_no":"63429394019489","pay_type":"Ali","state_code":"00"}';
        $logData = [
            'req_id'   => $this->reqId,
            'jsondata' => $jsonData,
        ];
        pft_log('qinghaipay/notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

        if (empty($jsonData))  {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '参数为空',
            ];
            pft_log('qinghaipay/notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit();
        }

        $data = json_decode($jsonData, true);
        if (empty($data) || empty($data['trade_no']) || empty($data['sign']) || empty($data['trade_state']) || $data['trade_state'] != 'SUCCESS') {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '参数错误',
            ];
            pft_log('qinghaipay/notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit();
        }

        $outTradeNo = $data['trade_no'];
        $caceInfo   = $this->getCacheInfo($outTradeNo);

        if (empty($caceInfo) || empty($caceInfo['signSecret']) || empty($caceInfo['money'])) {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '缓存失效',
            ];
            pft_log('qinghaipay/notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit('{"code":"SUCCESS","message":"成功"}');
        }

        $signSecret = $caceInfo['signSecret'];
        $totalFee   = $caceInfo['money'];

        //验证签名
        if (!$this->checkSign($data, $signSecret)) {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '签名错误',
            ];
            pft_log('qinghaipay/notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit();
        }

        $this->delCacheInfo($outTradeNo);

        $onlineTrade = new OnlineTrade();
        $tradeLog    = $onlineTrade->getLog($outTradeNo,self::SOURCE_T);
        if (empty($tradeLog) || $tradeLog['status'] != 0) {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '交易记录不存在或订单已经支付',
            ];
            pft_log('qinghaipay/notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit('{"code":"SUCCESS","message":"成功"}');
        }

        $options = [
            'buyer_info' => '',
            'sell_info'  => '',
            'pay_channel' => self::TRACK_SOURCE,
            'pay_termianl' => 0
        ];
        $result = \Library\Tools\Helpers::payComplete($outTradeNo, $outTradeNo.'_'.rand(1000,9999), self::SOURCE_T, $totalFee, 0, $options);

        if ($result['code'] == 200) {
            $needVerify = PayCache::getOrderVerfify($outTradeNo);
            if ($needVerify) {
                $successOrders = $result['data']['success_orders'];
                $paymentObj = new \Business\Order\MergeOrder();
                $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
            }
        }

        $logData = [
            'req_id'     => $this->reqId,
            'outTradeNo' => $outTradeNo,
            'rs'         => $result,
        ];
        pft_log('qinghaipay/notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

        if ($result['code'] == 200 || $result['code'] == 102 || $result['code'] == 101) {
            exit('{"code":"SUCCESS","message":"成功"}');
        }

    }


    /**
     * 退款
     * User: lanwanhui
     * Date: 2021/10/15
     *
     * @param int    $merchantId 供应商id
     * @param string $payNo      支付订单号
     * @param string $payAmount  金额
     *
     * @return array
     */
    public function refund($merchantId, $payNo, $payAmount)
    {
        try {

            $qinghaiConfig   = load_config('qinghai_pay','pay');
            if (empty($qinghaiConfig[$merchantId])) {
                return ['code'=>'201','msg'=>'未配置商户','data'=>[]];
            }

            //签名秘钥
            $signSecret    = $qinghaiConfig[$merchantId]['signSecret'];
            //加密秘钥
            $encryptSecret = $qinghaiConfig[$merchantId]['encryptSecret'];

            $data = [
                //订单号
                'payNo'     => $payNo,
                //订单金额（元）
                'payAmount' => strval($payAmount),
                //时间戳
                'time'      => time().'',
            ];

            $sm3          = new \Library\Tools\SM3();
            $data['sign'] = $this->createRequestStr($data, $signSecret);
            $data['sign'] = $sm3->sign($data['sign']);
            $data['sign'] = strtoupper($data['sign']);

            $sm4      = new \Library\Tools\SM4();
            $postData = $sm4->encrypt($encryptSecret, json_encode($data));

            $url = 'https://upay.bankqh.com:1443/cyanpay/yckjMobileEnd/aniPayRefund';

            $rs  = $this->httpPost($url,['data'=>$postData]);

            $logData = [
                'req_id'   => $this->reqId,
                'payNo'    => $payNo,
                'request'  => $data,
                'response' => $rs,
            ];
            pft_log('qinghaipay/refund', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

            if (empty($rs)) {
                return ['code'=>'200','msg'=>'','data'=>[]];
            }

            $rs = json_decode($rs, true);

            if (isset($rs['code']) && $rs['code'] == 'ERROR') {
                return ['code'=>'201','msg'=>$rs['message'] ?? '','data'=>[]];
            } else {
                return ['code'=>'200','msg'=>'','data'=>[]];
            }

        }catch (\Exception $e) {
            $logData = [
                'req_id'   => $this->reqId,
                'payNo'    => $payNo,
                'msg'      => $e->getMessage(),
                'line'     => $e->getLine(),
                'file'     =>$e->getFile(),
            ];
            pft_log('qinghaipay/refund', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
      
            return ['code'=>'200','msg'=>'','data'=>[]];
        }

    }

    /**
     * 订单查询
     * User: lanwanhui
     * Date: 2021/7/13
     */
    public function payResultCheck()
    {
        $ordernum = I('post.ordernum');
        //支付场景：1订单支付,2充值
        $pay_scen  = I('post.pay_scen', 2);
        $isMiniApp = I('post.mini_app');
        $data      = ['scan_channel' => 0];
        pft_log('qinghaipay/pay_result_check', json_encode($_POST, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));
        //todo::检测支付状态
        $model = new OnlineTrade();
        if ($isMiniApp) {
            $scanLog = $model->checkQrCodeScan($ordernum);
            if ($scanLog) {
                $data['scan_channel'] = $scanLog['scan_channel'];
                $payLog               = $model->getLog($ordernum, self::SOURCE_T);
                if ($payLog['status'] == 1) {
                    parent::apiReturn(parent::CODE_SUCCESS, $data, '支付成功', true);
                } else {
                    parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '未支付', true);
                }
            } else {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '请扫描二维码', true);
            }
        } else {
            sleep(1);
            $payLog = $model->getLog($ordernum, self::SOURCE_T);
            if (!$payLog) {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
            }

            if ($payLog['status'] == 1) {
                parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
            }
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看', true);
        }
    }

    /**
     * 验证签名
     * User: lanwanhui
     * Date: 2021/10/15
     *
     * @param array  $data       待签名数据
     * @param string $signSecret 秘钥
     *
     * @return bool
     */
    private function checkSign($data, $signSecret)
    {

        if (empty($data['sign'])) {
            return false;
        }
        $postSign = $data['sign'];
        unset($data['sign']);

        $sm3  = new \Library\Tools\SM3();
        $sign = $this->createRequestStr($data, $signSecret);
        $sign = $sm3->sign($sign);
        $sign = strtoupper($sign);

        if ($postSign != $sign) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 设置缓存
     * User: lanwanhui
     * Date: 2021/10/14
     *
     * @param string $payOrderNum 订单刚
     * @param string $signSecret  签名秘钥
     * @param int    $money       金额
     *
     */
    private function setCacheInfo($payOrderNum, $signSecret, $money)
    {
        $cacheKey = self::CACHE_PREFIX . $payOrderNum;
        $redis    = Cache::getInstance('redis');
        $redis->hMSet($cacheKey, ['signSecret'=>$signSecret, 'money'=>$money]);
        $redis->expire($cacheKey, 3600);
    }

    /**
     * 获取缓存
     * User: lanwanhui
     * Date: 2021/10/14
     *
     * @param string $payOrderNum 订单号
     *
     * @return mixed
     */
    private function getCacheInfo($payOrderNum)
    {
        $cacheKey  = self::CACHE_PREFIX . $payOrderNum;
        $redis     = Cache::getInstance('redis');
        $cacheInfo = $redis->hGetAll($cacheKey);
        return $cacheInfo;
    }


    /**
     * 删除回调缓存的数据
     * User: lanwanhui
     * Date: 2021/10/14
     *
     * @param string $payOrderNum 订单号
     *
     */
    private function delCacheInfo($payOrderNum)
    {
        $cacheKey  = self::CACHE_PREFIX . $payOrderNum;

        $redis = Cache::getInstance('redis');

        $redis->hdel($cacheKey, '');
    }


    /**
     * 生成支付地址
     * User: lanwanhui
     * Date: 2021/10/15
     *
     * @param string $payNo         订单号
     * @param string $payAmount     订单金额 （元）
     * @param string $payName       订单名称
     * @param string $machNo        机具标志
     * @param string $qrCode        商户号
     * @param string $signSecret    签名秘钥
     * @param string $encryptSecret 加密秘钥
     *
     * @return string
     */
    private function createUrl($payNo,$payAmount,$payName,$machNo,$qrCode,$signSecret,$encryptSecret)
    {
        $data = [
            //商户编号
            'qrCode'    => $qrCode,
            //订单号
            'payNo'     => $payNo,
            //订单名称
            'payName'   => $payName,
            //订单金额（元）
            'payAmount' => $payAmount,
            //机具标志
            'machNo'    => $machNo,
            //时间戳
            'time'      => time().'',
        ];

        //生成签名
        $sm3= new \Library\Tools\SM3();
        $data['sign'] = $this->createRequestStr($data, $signSecret);
        $data['sign'] = $sm3->sign($data['sign']);
        $data['sign'] = strtoupper($data['sign']);

        //生成加密数据
        $sm4 = new \Library\Tools\SM4();
        $requestStr = $this->createRequestStr($data);
        $encryptRequestStr = $sm4->encrypt($encryptSecret,$requestStr);
        $encryptRequestStr = urlencode($encryptRequestStr);

        $url = 'https://upay.bankqh.com:1443/cyanpay/yckjMobileEnd/aniPayPage?payload='.$encryptRequestStr;

        return $url;
    }


    /**
     * 生成请求字符串
     * User: lanwanhui
     * Date: 2021/10/15
     *
     * @param array $data        请求参数数组
     * @param string $signSecret 签名秘钥
     *
     * @return false|string
     */
    protected function createRequestStr(array $data, $signSecret = ''){

        $str = "";

        ksort($data);

        foreach($data as $k=>$v){

            if (empty($v)) continue;

            $str .= $k."=".$v.'&';

        }

        $str = substr($str,0,strlen($str)-1);

        if (!empty($signSecret)) {
            $str .= '&key='.$signSecret;
        }

        return $str;

    }


    /**
     * http请求
     * User: lanwanhui
     * Date: 2021/10/14
     *
     * @param string $url  请求地址
     * @param string $data 请求数据
     *
     * @return bool|string
     */
    private function httpPost($url , $data){

        try{

            $ch = curl_init ();

            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, 20);
            curl_setopt ($ch, CURLOPT_URL, $url);
            curl_setopt ($ch, CURLOPT_POST, 1 );
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt ($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt ($ch, CURLOPT_RETURNTRANSFER, true );
            $resp      = curl_exec ( $ch );
            $errCode   = curl_errno($ch);
            if ($errCode > 0) {
                $errMsg = curl_error($ch);

                $logData = [
                    'req_id'   => $this->reqId,
                    'data'     => $data,
                    'errMsg'   => $errMsg,
                    'errCode'  => $errCode,
                ];
                pft_log('qinghaipay/refund', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

            }
            curl_close($ch);
            return $resp;

        }catch (\Exception $e){

            return false;

        }

    }
}
