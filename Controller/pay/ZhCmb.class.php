<?php
/**
 * Created by PhpStorm.
 * User: linchen
 * Date: 2018/11/26 0026
 * Time: 18:40
 */
namespace Controller\pay;
use Library\Business\CMBPay\CmbPayApi;
use Library\Cache\Cache;
use Library\Controller;
use Model\Cmb\CmbFriend;
use Model\Member\Member;
use Model\TradeRecord\OnlineTrade;
define('IN_PFT', true);
class ZhCmb extends Controller {
    /**
     *
     * 支付接口
     * <AUTHOR>
     * @date   2018-11-26
     * @param string order_num 订单号
     */
    public function Cmbpay(){
        $memberId = $this->isLogin('auto', false);
        if (!$memberId) {
            echo "<h1>授权失效</h1>";die;
        }
        $orderNum = I('order_num');
        $sign     = I('sign','');
        if (!isset($orderNum)){
            $this->apiReturn(204,'','未找到订单');
        }
        $redis = Cache::getInstance('redis');
        $key   = 'cmbpay'.$orderNum;
        $Order  = new \Model\Order\OrderTools();
        $orderInfo = $Order->getOrderInfo($orderNum);
        if (empty($orderInfo)){
            $this->apiReturn(204,'','订单不存在');
        }
        if ($orderInfo['member'] != $memberId){
            $this->apiReturn(204,'','订单错误');
        }
        $OrderQeury  = new \Model\Order\OrderQuery();
        $price      = $OrderQeury->get_order_total_fee((string) $orderNum);
        if($price==0){
            $this->apiReturn(204,'','价格错误');
        }
        $lock  = $redis->lock($key,1,5);
        if (!$lock){
            $this->apiReturn(204,'','支付处理中');
        }
        $price = $price / 100;
        $ticketId = $orderInfo['tid'];
        $onlinetrade        = new OnlineTrade();
        $cmbConfig          = load_config('product','cmb_pay');
        if ($orderInfo['pid'] == $cmbConfig['one_product_card'] || $orderInfo['pid'] == $cmbConfig['normal_card']){
            $cmbSDK = new CmbPayApi();
        }else{
            $cmbSDK = new CmbPayApi(1);
        }
        $result = $cmbSDK->cmbPay($orderNum,$memberId,$price,$orderInfo['ordertel']);
        if (!$result){
            $this->apiReturn(200,$result,'提交订单失败');
        }
        $onlinetrade->addLog($orderNum,$price,'',"$memberId  购买了:$ticketId",16,2);
        $this->apiReturn(200,$result,'提交订单成功');
    }
    /**
     * 支付后的回调
     * 如果受理端接口调不通，会尝试9次
     * 这里通过支付接口 返回的 jsonRequestData
     */
    public function notifyTrade(){
        $json  = I('post.jsonRequestData', '', 'urlencode');
        $json  = urldecode($json);
        if (empty($json)) {
            exit('fail');
        }
        $data   = json_decode( $json, true);
        pft_log('cmb/notify','data=' . $json );
        $CmbApi = new CmbPayApi();
        $res    = $CmbApi->verify($data);
        pft_log('cmb/verify','data=' . $json . ';verify='.var_export($res, true));
        if (!$res) {
            echo 'fail';
        } else {
            $json_buy   = "";
            $payChannel = 38;
            $OrderModel = new \Model\Order\OrderTools('slave');
            $order_info = $OrderModel->getOrderInfo($data['noticeData']['orderNo']);
            try {
                //统一调用open服务器服务
                $res = parent::getSoap()->Change_Order_Pay($data['noticeData']['orderNo'], $data['noticeData']['bankSerialNo'], OnlineTrade::CHANNEL_CMB_NETPAYMENT,
                    $data['noticeData']['amount'] *100, 0, $data['noticeData']['noticeSerialNo'], $json_buy, 1, false, $payChannel);
            } catch (\SoapFault $e) {
                $retry = true;
                pft_log('cmb/notify/error', "(招行一网通)订单支付失败:{$data['noticeData']['orderNo']}，soap抛出异常:{$e->getCode()}->{$e->getMessage()}");
            }
            $outTradeNo = $data['noticeData']['orderNo'];
            pft_log('cmb/notify/result', "$outTradeNo" . json_encode($res));
            if (isset($retry) && $retry === true) {
                $res = parent::getSoap()->Change_Order_Pay($outTradeNo, $data['noticeData']['bankSerialNo'], OnlineTrade::CHANNEL_CMB_NETPAYMENT,
                    $data['noticeData']['amount'] *100, 0, $data['noticeData']['noticeSerialNo'], $json_buy, 1, false, $payChannel);
            }

            if ($res === 100) {
                $cmbConfig = load_config('one_product_card','cmb_pay');
                if (in_array($order_info['pid'],$cmbConfig)){
                    $cmbModel = new CmbFriend();
                    $result = $cmbModel->addOneAnnualProductRecord($order_info['pid'],$order_info['ordernum'],$order_info['member'],$data['noticeData']['amount'] *100);
                    if (!$result){
                        pft_log('cmb/record',"订单号[$outTradeNo]下的".$order_info['member'].'插入失败');
                    }
                }
                $productConfig = load_config('product','cmb_pay');
                if ($order_info['pid'] == $productConfig['normal_card'] || $order_info['pid'] == $productConfig['one_product_card']){
                        $cmbMemberBiz  = new \Business\Cmb\CmbMember();
                        $res = $cmbMemberBiz->updateIdCard($order_info['member']);
                        if ($res){
                            $extMemberBiz = new Member();
                            $memberBiz    = new \Business\Member\Member();
                            $memberBiz->updateMemberBaseInfo($order_info['member'],['dname' => $order_info['ordername']],true);
                            //用户二期 - 信息获取修改
                            $CustomerBus = new \Business\Member\Customer();
                            $CustomerBus->updateCustomerInfoByMemberId($order_info['member'], ['id_card_no' => $order_info['personid']]);
//                            $extMemberBiz->updateMemberExtInfo($order_info['member'],['id_card_no' => $order_info['personid']]);
                        }
                }
                $job_id = \Library\Resque\Queue::push('notify', 'OrderNotify_Job',
                    [
                        'ordernum'=> $outTradeNo,
                        'buyerId' => $order_info['member'],//购买人的ID
                        'mobile'  => $order_info['ordertel'],
                        'aid'     => $order_info['aid'],
                    ]
                );
                pft_log('fzcitycard/notify/result',"订单号[$outTradeNo],job_id=$job_id");
            }
            echo 'HTTP Status Code 200';
        }
        exit;
    }
    /**
     * 如果受理端接口调不通，会尝试9次
     * 签约的回调
     */
    public function signNotice(){
        $json  = I('post.jsonRequestData', '', 'urldecode');
        $data   = json_decode( $json, true);
        $CmbApi = new CmbPayApi();
        $res    = $CmbApi->verify($data);
        pft_log('cmb/signNotice','data=' . $json . ';verify='.var_export($res, true));
        echo  'HTTP Status Code 200';
        exit;
    }

}