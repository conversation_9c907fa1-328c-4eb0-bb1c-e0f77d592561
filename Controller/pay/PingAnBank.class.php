<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 2/10-010
 * Time: 16:09
 */

namespace Controller\pay;

use Business\Finance\AccountMoney;
use Library\Business\pingan_bank\PingAnSdk;
use Library\Controller;
use Library\Resque\Queue;
use Model\Member\Member;
use Model\Member\Recharge;
use Model\Member\Renew;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\TradeRecord\BankOnlineTrade;
use Model\TradeRecord\OnlineTrade;

class PingAnBank extends Controller
{
    const SOURCE_T            = 11;
    const PAY_CHANNEL         = 27; //定义支付渠道——对应Model=>OrderTrack.class.php
    const ORDER_NOTIFY_URL    = PAY_DOMAIN . 'r/pay_PingAnBank/orderNotify/';
    const RECHARGE_NOTIFY_URL = PAY_DOMAIN . 'r/pay_PingAnBank/rechargeNotify/';
    const REFUND_NOTIFY_URL   = PAY_DOMAIN . 'r/pay_PingAnBank/refundNotify/';
    const RENEW_NOTIFY_URL    = PAY_DOMAIN . 'r/pay_PingAnBank/renewNotify/';

    private $lib;
    public function __construct()
    {
        $this->lib = new PingAnSdk();
    }

    /**
     * 订单支付
     */
    public function payOrder()
    {
        $remark    = I('remark', '');
        $orderId   = I('ordernum');
        $orderInfo = $orderId . '订单支付';
        if (empty($orderId)) {
            exit('<script>alert("订单号为空");history.go(-1)</script>');
        }
        $OrderQeury = new OrderQuery('localhost');
        $amount     = $OrderQeury->get_order_total_fee($orderId);
        if ($amount) {
            $amount = $amount / 100;
        }
        if ($amount < 3000) {
            exit('<script>alert("抱歉只允许支付金额大于3000元的订单进行支付！");history.go(-1)</script>');
        }
        $sysTrade = new OnlineTrade();
        $getId    = $sysTrade->getLog($orderId, self::SOURCE_T);
        if ($getId['status'] == 1) {
            exit('<script>alert("抱歉改订单已支付成功，请勿重复支付！");history.go(-1)</script>');
        }
        $lastId = $sysTrade->addLog($orderId, $amount, $orderInfo, $orderInfo, self::SOURCE_T);
        if ($lastId) {
            $bankTrade = new BankOnlineTrade();
            $lastId    = $bankTrade->addLog($orderId, $amount, $orderInfo, $orderInfo, self::SOURCE_T);
            if ($lastId) {
                $this->lib->Pay($amount * 100, $orderInfo, $lastId, self::ORDER_NOTIFY_URL, $remark);
            } else {
                exit('<script>alert("服务异常！");history.go(-1)</script>');
            }
        } else {
            exit('<script>alert("服务异常！");history.go(-1)</script>');
        }
    }

    /**
     * <AUTHOR> Chen
     * @date 2016-10-03
     *
     * @description 充值-post
     * @money:充值的金额，单位“元”
     * @aid:供应商ID，可以为空；大于0表示授信预存
     * @did:充值的人的id
     */
    public function recharge()
    {
        $this->isLogin('ajax');
        $money = I('money');
        if (!$money) {
            exit('<script>alert("抱歉充值金额有误！");history.go(-1)</script>');
        }
        $money = floatval(number_format($money, 2, '.', ''));
        $aid   = I('aid');

        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $did       = $loginInfo['memberID'] ?? 0;

        $remark = I('remark', '充值');
        if (!$did) {
            exit('<script>alert("用户身份获取错误！");history.go(-1)</script>');
        }
        if (!is_numeric($money) || $money < 3000) {
            exit('<script>alert("请输入大于3000的金额，金额必须是数字！");history.go(-1)</script>');
        }
        $modelMember = new Member();
        if ($did == 1) {
            $orderInfo = '补打款';
        } else {

            $queryParams = [[$did, $aid]];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                $queryParams);

            if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                exit('{"status":"fail","msg":"用户信息获取失败，请稍后重试"}');
            }

            $memberInfo = array_column($queryRes['data'], null, 'id');

            //$seller_nama = $modelMember->getMemberCacheById($did, 'dname');
            $seller_nama = $memberInfo[$did]['dname'];
            if ($aid > 0) {
                //$boss_name = $modelMember->getMemberCacheById($aid, 'dname');
                $boss_name = $memberInfo[$aid]['dname'];
                $orderInfo = "[$seller_nama]给{$boss_name}充值{$money}元|{$did}|$aid";
            } else {
                $orderInfo = "[{$seller_nama}]账户充值{$money}元|{$did}";
            }
        }
        $orderId  = time() . $did . mt_rand(1000, 9999);
        $sysTrade = new OnlineTrade();
        $lastId   = $sysTrade->addLog($orderId, $money, $orderInfo, $orderInfo, self::SOURCE_T, OnlineTrade::PAY_METHOD_RECHARGE);
        if ($lastId) {

            //如果是内网测试环境，直接把支付状态设置为已经支付
            if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
                //模拟成功支付

                //由于平安银行订单号特殊，故增加一个独立的表用来记录流水，实际上没有什么用。
                $bankTrade = new BankOnlineTrade();
                $lastId    = $bankTrade->addLog($orderId, $money, $orderInfo, $orderInfo, self::SOURCE_T);

                if (!$lastId) {
                    exit('<script>alert("服务异常！");history.go(-1)</script>');
                }

                //设置为支付成功
                $accountBiz  = new AccountMoney();
                $trade_no    = 'online_' . time();
                $realMoney   = $money * 100;
                $rechargeRes = $accountBiz->recharge($orderId, self::SOURCE_T, $trade_no, $realMoney, '<EMAIL>', '<EMAIL>');

                if($rechargeRes) {
                    //成功回跳到支付成功页面
                    header('Location: ' . MY_DOMAIN . 'alipay/ali_order/return_url_pingan.html');
                } else {
                    exit('<script>alert("模拟支付失败");history.go(-1)</script>');
                }

            } else {
                //由于平安银行订单号特殊，故增加一个独立的表用来记录流水，实际上没有什么用。
                $bankTrade = new BankOnlineTrade();
                $lastId    = $bankTrade->addLog($orderId, $money, $orderInfo, $orderInfo, self::SOURCE_T);
                if ($lastId) {
                    $this->lib->Pay($money * 100, $orderInfo, $lastId, self::RECHARGE_NOTIFY_URL, $remark);
                } else {
                    exit('<script>alert("服务异常！");history.go(-1)</script>');
                }
            }
        } else {
            exit('<script>alert("服务异常！");history.go(-1)</script>');
        }
    }

    /**
     * 平台续费
     * <AUTHOR>
     *
     * @param  int  $meal 充值套餐 1,2,3
     * @return
     */
    public function renew()
    {
        //请求日志
        exit('<script>alert("服务异常！");history.go(-1)</script>');
    }

    /**
     * 平台续费回调 - 这个接口已经废弃
     * <AUTHOR>
     * @return
     */
    public function renewNotify()
    {
        $postData = I('post.');
        pft_log('pingan/renew', json_encode($postData, JSON_UNESCAPED_UNICODE));

        return false;
    }

    /**
     * 获取票付通订单号
     *
     * @param $tradeId
     * @return string
     */
    private function getOrderNum($tradeId)
    {
        $bankTrade = new BankOnlineTrade();
        $data      = $bankTrade->getLog($tradeId);
        return $data['out_trade_no'];
    }
    /**
     * 处理异步通知数据
     *
     * @return array|bool
     */
    private function notifyResult()
    {
        $orig = I('post.orig');
        $sign = I('post.sign');
        $res  = $this->lib->notifyResult($orig, $sign);
        pft_log('pingan/notify', "orig:$orig, sign: $sign, decode:" . json_encode($res, JSON_UNESCAPED_UNICODE));

        if (is_array($res) && $res['status'] == '01') {
            //TODO::获取平台订单号
            $tradeId             = $this->lib->clearOrderPrefix($res['orderId']);
            $res['out_trade_no'] = $this->getOrderNum($tradeId);
            return $res;
        } else {
            return false;
        }
    }

    /**
     * 平安银行订单支付异步通知接口 pay.12301.cc/r/pay_PingAnBank/orderNotify
     *
     * @return bool
     */
    public function orderNotify()
    {
        $data = $this->notifyResult();
        if ($data === false) {
            return false;
        }

        /**
         * @var $soap \ServerInside
         */
        $soap = parent::getSoap();
        $res  = $soap->Change_Order_Pay($data['out_trade_no'], $data['orderId'], self::SOURCE_T,
            $data['amount'] * 100, null, '{"masterId":"' . $data['masterId'] . '"}', '', 1,
            true, self::PAY_CHANNEL);
        if ($res === 100) {
            $OrderModel = new OrderTools('slave');
            $order_info = $OrderModel->getOrderInfo($data['out_trade_no']);
            if ($data['out_trade_no'] != '') {
                $job_id = Queue::push('notify', 'OrderNotify_Job',
                    [
                        'ordernum' => $data['out_trade_no'],
                        'buyerId'  => $order_info['member'], //购买人的ID
                        'mobile'   => $order_info['ordertel'],
                        'aid'      => $order_info['aid'],
                    ]
                );
            }
            pft_log('pingan/order_ok', "{$data['out_trade_no']}|更新订单状态|返回代码[$res],job_id=$job_id");
            exit('success');
        } elseif ($res == 101 || $res == 102) {
            pft_log('pingan/order_ok', "{$data['out_trade_no']}|更新订单状态[Change_Order_Pay]|已更新不要重复更新|返回代码[$res]");
            exit('success');
        }
        pft_log('pingan/order_error', "{$data['out_trade_no']}|更新订单失败[Change_Order_Pay]|返回代码[$res]");
        exit('fail');
    }

    /**
     * 平安银行充值异步通知接口 pay.12301.cc/r/pay_PingAnBank/rechargeNotify
     *
     * @return bool
     */
    public function rechargeNotify()
    {
        $data = $this->notifyResult();
        if ($data === false) {
            return false;
        }

        $pay_total_fee = $data['amount'] * 100;
        $trade_no      = $data['orderId'];
        $json_sell     = '{"masterId":"' . $data['masterId'] . '"}';
        $json_buy      = '';

        // $modelRecharge = new Recharge();
        // $res = $modelRecharge->OnlineRecharge($data['out_trade_no'], self::SOURCE_T, $trade_no, $pay_total_fee,
        //     $json_buy, $json_sell);

        $accountBiz  = new AccountMoney();
        $rechargeRes = $accountBiz->recharge($data['out_trade_no'], self::SOURCE_T, $trade_no, $pay_total_fee,
            $json_buy, $json_sell);

        //记录日志
        $logData = json_encode([
            'key' => '平安充值',
            'notify' => $data,
            'res' => $rechargeRes
        ], JSON_UNESCAPED_UNICODE);
        pft_log('pingan_recharge', $logData, 3);

        $res = $rechargeRes ? true : false;
        return $res;
    }

    /**
     * 支付成功，返回页面 pay.12301.cc/r/pay_PingAnBank/returnPage
     */
    public function returnPage()
    {
        $orig                = I('post.orig');
        $sign                = I('post.sign');
        $res                 = $this->lib->returnResult($orig, $sign);
        $tradeId             = $this->lib->clearOrderPrefix($res['orderId']);
        $res['out_trade_no'] = $this->getOrderNum($tradeId);
        if ($res['status'] == '01') {
            header('Location: ' . MY_DOMAIN . 'alipay/ali_order/return_url_pingan.html');
        } else {
            header('Location: ' . MY_DOMAIN . 'alipay/ali_order/return_url_pingan_error.html');
        }
        exit;
    }

    public function Query()
    {
        $orderId = I('post.orderid');
        $res     = $this->lib->Query($orderId);
        parent::apiReturn(200, $res);
    }

    public function PayOrderList()
    {
        $start_date = I('post.start_date');
        $end_date   = I('post.end_date');
        $xml        = $this->lib->PayOrderList($start_date, $end_date);
        echo $xml;
    }

    public function FileDownload()
    {
        $date = I("post.date");
        if (strpos($date, '-') !== false) {
            $date = str_replace('-', '', $date);
        }

        $xml = $this->lib->FileDownload($date);
        if ($xml) {
            $xml = mb_convert_encoding($xml, 'UTF-8');
            echo $xml;
            $arr = simplexml_load_string($xml);
            print_r($arr);
        }

    }

    public function refund()
    {
        $orderNum   = I('post.ordernum');
        $objectName = I('post.refund_reson', '退款');
        $remark     = I('post.remark', '退款');
        $sysTrade   = new OnlineTrade();
        $payInfo    = $sysTrade->getLog($orderNum, self::SOURCE_T);
        print_r($payInfo);
        if ($payInfo) {
            $bankTrade = new BankOnlineTrade();
            $lastId    = $bankTrade->addLog($orderNum, $payInfo['total_fee'], '退款', '退款', self::SOURCE_T);
            $result    = $this->lib->Refund($lastId, $payInfo['trade_no'], $payInfo['total_fee'], $objectName, $remark,
                self::REFUND_NOTIFY_URL);
            parent::apiReturn(200, $result);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '数据不存在');
    }

    public function refundQuery()
    {
        $start_date = I('post.start_date');
        $end_date   = I('post.end_date');
        $result     = $this->lib->refundQuery($start_date, $end_date);
        parent::apiReturn(200, $result, '');
    }

    /**
     * 退款成功，返回页面 pay.12301.cc/r/pay_PingAnBank/refundNotify
     */
    public function refundNotify()
    {
        $data = $this->notifyResult();
    }
}
