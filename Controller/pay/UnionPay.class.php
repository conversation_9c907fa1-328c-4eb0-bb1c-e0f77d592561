<?php
/**
 * Created by PhpStorm.
 * User: linchen
 * Date: 2019/1/17 0017
 * Time: 17:12
 */
namespace Controller\pay;
use Business\Order\MergeOrder;
use Business\Pay\PayBase;
use Business\Pay\PayCache;
use Library\Business\WePay\WxPayNotifyResponse;
use Model\Order\OrderQuery;
use Library\Business\WePay\WxPayApi;
use Library\Controller;
use Library\Tools\Helpers;
use Model\Order\OrderTools;
use Model\TradeRecord\OnlineTrade;
use Model\Order\TeamOrderSearch;
use Business\Order\Query;
use Library\Constants\DingTalkRobots;
define('IN_PFT', true);
class UnionPay extends Controller implements PayInterface {
    private $mchId        = '';
    private $orderModel = null;
    private $pay_type   = 1;
    private $memberId;
    
    public function __construct($config = null,$memberId = 0,$pay_type = 1)
    {
        $this->mchId          = $config['mch_id'] ? $config['mch_id'] :'';
        $this->pay_type       = $pay_type;
        $this->memberId       = $memberId;
    }

    /**
     * 支付后的回调
     * 如果受理端接口调不通
     * 这里通过支付接口 返回的 jsonRequestData
     */
    public function orderNotify(){
        $jsonData = I('post.');
        PayBase::setPayDailyRecord(4,'回调',[],$jsonData);
        $client = new \Library\Tools\YarClient('pay');
        $checkSign = $client->call('Pay/UnionPay/checkSign', [$jsonData['merId'],json_encode($jsonData)]);
        if (!$checkSign || $jsonData['respCode'] != '00'){
            echo 'FAILED';
            exit;
        }
        //订单号处理
        $ordernum       = $jsonData['orderId'];
        $outTradeNo     = $this->_orderChangeNotify($ordernum);
        $transaction_id = $jsonData['queryId']; //交易号

        $total_fee      = (int) $jsonData['txnAmt'] + 0; //金额用分为单位
        $pay_channel    = 43;
        $payTerminal    = '';
        $is_member      = 0;
        $buyerInfo = isset($jsonData['txnTime']) ? $jsonData['txnTime']: '';
        $sellerInfo = isset($jsonData['merId']) ? $jsonData['merId']: '';
        $pay_to_pft     = 0;

        $options = [
            'buyer_info'   => $buyerInfo,
            'sell_info'    => $sellerInfo,
            'pay_channel'  => $pay_channel,
            'pay_termianl' => $payTerminal,
        ];
        $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_UNIONPAY_NEW,
            $total_fee, (int)$pay_to_pft, $options);
        PayBase::setPayDailyRecord(4,'回调结果',[],$result);
        if ($result['code'] == 200) {
            $needVerify = PayCache::getOrderVerfify($outTradeNo);
            if ($needVerify) {
                $successOrders = $result['data']['success_orders'];
                $paymentObj = new \Business\Order\MergeOrder();
                $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
            }
        }
        if ($result['code'] == 200 || $result['code'] == 102 || $result['code'] == 101) {
            echo 'SUCCESS';
        } else {
            echo 'FAILED';
        }
        exit;
//        $biz       = new \Business\Order\MergeOrder();
//        if ($biz->isCombineOrder($outTradeNo)) {
//            $result = $biz->handlerCombinePayOrders($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_UNIONPAY_NEW, $is_member,
//                $sellerInfo, $buyerInfo, $pay_to_pft, $pay_channel, $payTerminal);
//
//            $needVerify = \Library\Cache\Cache::getInstance('redis')->get('ordernum_needVerify:'.$outTradeNo);
//            if ($needVerify) {
//                $successOrders = $result['data']['success_orders'];
//                $paymentObj = new \Business\Order\MergeOrder();
//                $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
//            }
//        } else {
//            try {
//                $res = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_UNIONPAY_NEW,
//                    $total_fee, $is_member, $sellerInfo, $buyerInfo, 1, $pay_to_pft, $pay_channel, '', $payTerminal);
//            } catch (\SoapFault $e) {
//                $retry = true;
//                pft_log('order_pay/error', "(微信银联)订单支付失败:{$outTradeNo}，soap抛出异常:{$e->getCode()}->{$e->getMessage()}");
//                //记录日志
//                $logData = json_encode([
//                    'key' => '云闪付',
//                    'ordernum' => $outTradeNo,
//                    'error' => "soap抛出异常:{$e->getCode()}->{$e->getMessage()}"
//                ], JSON_UNESCAPED_UNICODE);
//                pft_log('swift_qr_order', $logData, 3);
//
//                Helpers::sendDingTalkGroupRobotMessage("[{$outTradeNo}]订单支付失败;soap抛出异常:{$e->getCode()}->{$e->getMessage()}", "支付失败", Helpers::getServerIp(), DingTalkRobots::MYSQL_ERROR);
//            }
//            if (isset($retry) && $retry === true) {
//                $res = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_UNIONPAY_NEW,
//                    $total_fee, $is_member, $sellerInfo, $buyerInfo, 1, $pay_to_pft, $pay_channel, '', $payTerminal);
//            }
//            if ($res === 100) {
//                $orderInfo = $this->_getOrder($outTradeNo);
//                if (Helpers::isMobile($orderInfo['ordertel'])) {
//                    $args = [
//                        'ordernum' => $outTradeNo,
//                        'buyerId'  => $orderInfo['member'], //购买人的ID
//                        'mobile'   => $orderInfo['ordertel'],
//                        'aid'      => $orderInfo['aid'],
//                    ];
//                    $jobId = \Library\Resque\Queue::push('notify', 'OrderNotify_Job', $args);
//                }
//            } else {
//                echo 'FAILED';
//                exit;
//            }
//        }
//        echo 'SUCCESS';
//        exit;
    }
    public function renew(){

    }
    public function recharge(){

    }
    public function order(){
        //平台订单号
        $outTradeNo = I('post.out_trade_no', 0, 'string');
        //是否app里支付
        $appPay = I('post.is_app',0,'intval');
        //是否二维码支付
        $qrPay  = I('post.is_qr', 0, 'intval');
        //订单主体说明
        $subject = mb_substr(trim(I('post.subject')), 0, 20, 'utf-8');
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }
        PayBase::setPayDailyRecord(4,'订单请求',I('post.'));
        $mergeOrder = new MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
            try {
                $mergeOrder->handlerCombinePayLog($outTradeNo, '', OnlineTrade::CHANNEL_UNIONPAY_NEW, $this->memberId);
            } catch (OrderPreCheckException $e) {
                parent::apiReturn(401, [], $e->getMessage());
            }
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string) $outTradeNo);
        }

        //真实支付
        if (!$appPay && !$qrPay) {
            parent::apiReturn(401, [], '支付类型错误');
        }
        //如果是内网测试环境，直接把支付状态设置为已经支付（测试环境）
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            //模拟支付
            if ($qrPay) {
                $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => ''];
            } else {
                $data = json_encode([]);
            }
            $payMethod = OnlineTrade::PAY_METHOD_ORDER;
            //生成支付记录
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog(
                $outTradeNo, $totalFee / 100, $subject, $subject, OnlineTrade::CHANNEL_UNIONPAY_NEW,
                $payMethod, '', $this->mchId
            );
            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }

            //模拟支付成功
            $transactionId = 'online_' . time();
            $is_member     = null;
            $json_sell     = json_encode(['appid' => 'seller.12301.cc']);
            $json_buy      = json_encode(['openid' => 'buyer.12301.cc']);
            $pay_to_pft    =  false;
            $payTerminal   = '';

            $options = [
                'buyer_info' => $json_buy,
                'sell_info'  => $json_sell,
                'pay_channel' => 43,
                'pay_termianl' => $payTerminal
            ];

            $res = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_UNIONPAY_NEW, $totalFee, (int)$pay_to_pft, $options);

            echo json_encode(['code' => $res['code'], 'data' => $res['data'], 'status' => 'ok', 'msg' => $res['msg']]);
            exit;
        }

        $frontUrl = I('post.success_url', $_SERVER['HTTP_REFERER']);
        $frontUrl = htmlspecialchars_decode($frontUrl);
        $client = new \Library\Tools\YarClient('pay');
            //1.$mid int 商户号 2 $tid int 终端号 3 $msgSrcId int 来源编号4 $sign string MD5密钥 5 $msgSrc string 消息来源 6 $money int 金额7 $orderId string 订单号 8 $returnUrl string 返回地址
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $unionOrderId = $this->_orderChange($outTradeNo,true);
            if ($qrPay){
                $requestParams = [$this->mchId,$unionOrderId,$totalFee];
                $res = $client->call('Pay/UnionPay/createUnionQrPay', $requestParams);
            }else{
                $requestParams = [$this->mchId,$unionOrderId,$totalFee,$subject,$frontUrl];
                $res = $client->call('Pay/UnionPay/createAppPay', $requestParams);
            }
        }else{
            $unionOrderId = $this->_orderChange($outTradeNo);
            if ($qrPay){
                $requestParams = [$this->mchId,$unionOrderId,$totalFee];
                $res = $client->call('Pay/UnionPay/createUnionQrPay', $requestParams);
            }else{
                $requestParams = [$this->mchId,$unionOrderId,$totalFee,$subject,$frontUrl];
                $res = $client->call('Pay/UnionPay/createAppPay', $requestParams);
            }
        }
        PayBase::setPayDailyRecord(4,'订单支付请求',$requestParams,$res);
        if ($res['code'] == 200 && $res['res']['data']['respCode'] == '00'){
            if ($qrPay){
                $data = [
                    'outTradeNo' => $outTradeNo,
                    'qrUrl'      => $res['res']['data']['qrCode']
                ];
            }else{
                $data['tn'] = $res['res']['data']['tn'];
            }
        }else{
            parent::apiReturn(401, [], $res['res']['msg']);
        }
        $payMethod = OnlineTrade::PAY_METHOD_ORDER;
        $tradeModel = new OnlineTrade();
        $ret        = $tradeModel->addLog($outTradeNo, $totalFee / 100, $subject, $subject, OnlineTrade::CHANNEL_UNIONPAY_NEW, $payMethod, '', $this->memberId);
        PayBase::setPayDailyRecord(4,'订单成功结果',[$outTradeNo],$data);

        if (!$ret) {
            parent::apiReturn(401, [], '支付记录生成失败');
        }
        echo json_encode(['code' => 200, 'data' => $data, 'status' => 'ok', 'msg' => 'success']);
        exit;
    }
    public function cardSolutionRecharge(){

    }
    public function micropay(){
        $outTradeNo  = I('post.ordernum');
        $authCode    = I('post.auth_code');
        $totalFee    = float2int(I('post.money'));
        $isMember    = I('post.is_member', 0);
        $subject     = I('post.subject', '订单支付');
        $payScen     = I('post.pay_scen', 1);
        $payTerminal = I('post.terminal', 0, 'intval'); //支付的终端
        $needVerify  = I('post.verify', 0);//是否支付后立即验证标识
        $terminal    = I('post.terminal', 0, 'strval');//支付时所使用的终端号
        $pay_type   = I('post.pay_type', 2, 'intval');
        $mergePayFlag = false;// 合并付款标识
        $checkSource     = I('post.check_source', -1, 'intval');//验证来源
        //支付来源 - 具体可以参照business.conf.php -> track_source
        // 0=黑色终端机 2=自助机 4=云票务 5=云闸机 20=安卓智能终端机
        $paySource = I('post.pay_source', 20, 'intval');
        //支付的操作人 可以不传 用在记录追踪表李
        $payTrackOpId  = I('post.pay_track_op_id', 0);
        //年卡下单类型  默认值为空字符串, 0年卡下单 1年卡续费
        $annualCardOrderType = I('post.annualCardOrderType', false);
        if (!$outTradeNo || !$authCode) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }
        //扫码支付授权码，设备读取用户云闪付中的条码或者二维码信息
        //（注：用户刷卡条形码规则：18位纯数字，以62开头）
        $codeLength = strlen($authCode);
        if ($codeLength != 19 || !is_numeric($authCode)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '非法的支付条码，请重新刷新云闪付支付码', true);
        }
        PayBase::setPayDailyRecord(4,'扫码请求',I('post.'));
        $paymentObj      = new \Business\Order\MergeOrder();
        if($paymentObj->isCombineOrder($outTradeNo)){
            $mergePayFlag = true;
            $paymentObj->handlerCombinePayLog($outTradeNo, $subject, OnlineTrade::CHANNEL_UNIONPAY_NEW, $this->memberId);
        }
        //记录日志
        switch ($payScen) {
            case 1:
                //订单支付
                if (is_numeric($outTradeNo) || $mergePayFlag === true) {
                    $orderQuery = new OrderQuery('localhost');
                    $totalFee  = $orderQuery->get_order_total_fee($outTradeNo);
                }
                break;
            case 2:
                break;
        }
        $OnlineTrade = new OnlineTrade();
        $result      = $OnlineTrade->addLog($outTradeNo, $totalFee / 100, $subject, $subject, OnlineTrade::CHANNEL_UNIONPAY_NEW, OnlineTrade::PAY_METHOD_ORDER, '', $this->memberId);
        if (!$result) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }
        $client = new \Library\Tools\YarClient('pay');
        if ($paymentObj->isCombineOrder($outTradeNo)){
            //银联云闪付不支持-和_的订单要把这个转掉
            $unionOrderId = str_replace('CMB-','CLOUD',$outTradeNo);
            $requestParam = [$this->mchId,$unionOrderId,$totalFee,$authCode];
            $payResult  = $client->call('Pay/UnionPay/createUnionPosPay', $requestParam);
        }else{
            $requestParam = [$this->mchId,$outTradeNo,$totalFee,$authCode];
            $payResult  = $client->call('Pay/UnionPay/createUnionPosPay', $requestParam);
        }
        PayBase::setPayDailyRecord(4,'扫码三方支付',$requestParam,$payResult);
        if (isset($payResult['code']) && $payResult['code'] == 200 && isset($payResult['res']['code']) && $payResult['res']['code'] == 200){
            if ($payResult['res']['data']['respCode'] == '00'){
              //接下去查询
            }else{
                //先输出一轮
                $this->apiReturn(parent::CODE_INVALID_REQUEST, [], $payResult['res']['data']['respMsg']);
            }
        }
        //没进上面if判断有可能超时等问题
        $num = 0;
        $getMoneyFlag = false;
        do {
            if ($paymentObj->isCombineOrder($outTradeNo)){
                $payResult  = $client->call('Pay/UnionPay/queryUnionPosPay', [$this->mchId,$unionOrderId]);
            }else{
                $payResult  = $client->call('Pay/UnionPay/queryUnionPosPay', [$this->mchId,$outTradeNo]);
            }
            if (isset($payResult['code']) && $payResult['code'] == 200 && isset($payResult['res']['code']) && $payResult['res']['code'] == 200 &&
                $payResult['res']['data']['respCode'] == '00' && ($payResult['res']['data']['origRespCode'] == '00' || $payResult['res']['data']['origRespCode'] == 'A6')){
                $getMoneyFlag = true;
                break;
            }
            $num++;
            sleep(1);
        } while($num < 60);

        if (!$getMoneyFlag) {
            // 如果60秒后还没收款成功冲正
            //$reverseResult  = $client->call('Pay/ChinaUmsPay/chinaUmsPosReverse', [$this->memberId,$this->appid,$this->appkey,$this->merchantId,$this->terminalCode,$outTradeNo,$totalFee]);
            pft_log('union_micropay',json_encode([
                'ordernum'=>$outTradeNo,
                'msg'=>'支持超时,需要冲正',
                'data'=>[$this->memberId,$this->mchId,$outTradeNo,$totalFee]], JSON_UNESCAPED_SLASHES|JSON_UNESCAPED_UNICODE),3
            );
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '请求云闪付接口失败:' . $payResult['res']['data']['origRespMsg'], true);
        }

        //记录日志
        $logData = json_encode([
            'key'      => '云闪付扫码支付结果',
            'ordernum' => $outTradeNo,
            'res'      => $payResult,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('union_micropay', $logData, 3);

        if ($payResult['res']['data']['origRespCode'] != '00') {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "支付失败,订单号:{$outTradeNo},请求云闪付失败", true);
        }
        //这边我故意记录了付款的商户id和付款的时间，这样退款可以用queryId或者txnTime+订单号退款2选一
        $buyerInfo = isset($payResult['res']['data']['txnTime']) ? $payResult['res']['data']['txnTime']: '';
        $sellerInfo = isset($payResult['res']['data']['merId']) ? $payResult['res']['data']['merId']: '';
        $transactionId = $payResult['res']['data']['queryId'];
        $jsonBuy       = $buyerInfo;
        $jsonSell      = $sellerInfo;
        $payToPft      = false;
        $payChannel    = 43;


        $options = [
            'buyer_info'   => $jsonBuy,
            'sell_info'    => $jsonSell,
            'pay_channel'  => $payChannel,
            'pay_termianl' => $payTerminal,
            'oper'         => $payTrackOpId,
            'pay_source'   => $checkSource,
        ];

        $res = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_UNIONPAY_NEW, $totalFee, (int)$payToPft, $options);

        if ($res['code'] == 200) {
            $data = [];
            $successOrders = $res['data']['success_orders'];
            foreach ($successOrders as $item) {
                $data[] = [
                    'code' => 200,
                    'msg'  => '支付成功',
                    'order_id' => $item
                ];
            }
            //立即验证
            if ($needVerify) {
                if ($mergePayFlag) {
                    $verifyRes = $paymentObj->handleCombineOrderBuyVerifyByRpc($successOrders, $payTerminal, $payTrackOpId, $checkSource);
                    if ($verifyRes){
                        $verifyData = $verifyRes[0];  //默认就取第0个，因为手持机那边只能读取一个订单
                        $data  = $verifyData;
                    }
                } else {
                    $orderInfo = $this->_getOrder($outTradeNo);
                    $query = new \Business\Order\Query();
                    $data      = $query->getOrderInfoForPrintByRpc($outTradeNo, $orderInfo, $totalFee, 30, $terminal,$checkSource,$payTrackOpId);
                }
            }
            $result = ['code' => parent::CODE_SUCCESS, 'data' => $data, 'msg' => '支付成功'];
        } else {
            $result = ['code' => parent::CODE_INVALID_REQUEST, 'data' => [], 'msg' => '付款失败'];
        }

        //记录日志
        PayBase::setPayDailyRecord(4,'扫码成功',[$outTradeNo],$result);
        parent::apiReturn($result['code'], $result['data'], $result['msg'], true);
    }
    public function Query(){

    }
    public function Refund(){

    }
    public function rechargeNotify(){

    }
    /**
     * 获取订单信息
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @param  string $ordernum
     * @return array
     */
    private function _getOrder($ordernum)
    {
        $this->orderModel = new OrderTools('localhost');
        $orderInfo        = $this->orderModel->getOrderInfo($ordernum, 'member,ordername,ordertel,aid,code,lid,tid,tnum,tprice,ordermode,paymode,salerid,ss.pay_status,ss.status', 'de.aids,de.series');
        if ($orderInfo['aids']) {
            // 转分销获取第二级分销商
            $tmp                 = explode(',', $orderInfo['aids']);
            $orderInfo['member'] = $tmp[1];
        }
        $this->orderInfo = $orderInfo;
        return $orderInfo;
    }
    private function _changeOrderPay($outTradeNo, $transaction_id,$total_fee, $is_member, $json_sell, $json_buy, $pay_to_pft, $pay_channel,$payTerminal,$needVerify, $terminal, $payTrackOpId = 0, $annualCardOrderType = '') {
        try {
            $res      = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_UNIONPAY_NEW,
                $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $pay_channel, '', $payTerminal, $payTrackOpId, $annualCardOrderType);
        } catch (\SoapFault $e) {
            $retry = true;
            pft_log('order_pay/error', "(云闪付)订单支付失败:{$outTradeNo}，soap抛出异常:{$e->getCode()}->{$e->getMessage()}");
            Helpers::sendDingTalkGroupRobotMessage("(云闪付)[{$outTradeNo}]订单支付失败;soap抛出异常:{$e->getCode()}->{$e->getMessage()}", "支付失败",  Helpers::getServerIp(), DingTalkRobots::MYSQL_ERROR);
        }
        if (isset($retry) && $retry === true) {
            $res      = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_UNIONPAY_NEW,
                $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $pay_channel, '', $payTerminal, $payTrackOpId, $annualCardOrderType);
        }
        $output = [];
        if ($res === 100) {
            $orderInfo = $this->_getOrder($outTradeNo);
            $args = [
                'ordernum' => $outTradeNo,
                'buyerId'  => $orderInfo['member'], //购买人的ID
                'mobile'   => $orderInfo['ordertel'],
                'aid'      => $orderInfo['aid'],
            ];
            $jobId = \Library\Resque\Queue::push('notify', 'OrderNotify_Job', $args);
            if ($needVerify) {
                if (empty($this->orderInfo)) {
                }
                $query   = new Query();
                $paymode = 5;
                $output  = $query->getOrderInfoForPrint($outTradeNo, $this->orderInfo, $total_fee, $paymode, $terminal, 0);
            }

            if ($orderInfo['ordermode'] == 24) {
                $teamOrderModel = new TeamOrderSearch();
                $mainOrderInfo  = $teamOrderModel->getMainOrderInfoBySonOrder(strval($outTradeNo));
                if (empty($mainOrderInfo)) {
                    parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功, 未找到团单号');
                }
                $teamOrder = $mainOrderInfo['main_ordernum'];
                $sonOrder  = $teamOrderModel->getSonOrderInfoByMainOrderNum($teamOrder);
                $orderArr  = array_column($sonOrder, 'son_ordernum');
                $orderTool = new OrderTools();
                $orderData = $orderTool->getOrderInfo($orderArr, 'status,pay_status');

                $needUpdatePay = true;
                if ($orderData) {
                    foreach ($orderData as $value) {
                        if ($value['pay_status'] != 1 && $value['status'] != 3) {
                            $needUpdatePay = false;
                        }
                    }
                }

                if ($needUpdatePay) {
                    $payStatus = 1;
                } else {
                    $payStatus = 2;
                }

                if ($mainOrderInfo['status'] != 1 || $mainOrderInfo['pay_status'] != $payStatus) {
                    $res = $teamOrderModel->setTeamOrderByOrder($teamOrder, ['updatetime' => time(), 'status' => 1, 'pay_status' => $payStatus]);
                }
            }

            $msg    = '支付成功';
            $code   = parent::CODE_SUCCESS;
        } else {
            $msg    = "云闪付支付成功但订单状态更新失败,订单号:{$outTradeNo}";
            $code   = parent::CODE_INVALID_REQUEST;
        }
        return ['code'=>$code,'data'=>$output,'msg'=>$msg,'pay_res'=>$res];
    }
    public function micropayNotify(){
        $jsonData = I('post.');
        pft_log('union/order', 'orderNotify='.json_encode($jsonData));
        $client = new \Library\Tools\YarClient('pay');
        $checkSign = $client->call('Pay/UnionPay/checkSign', [$jsonData['merId'],json_encode($jsonData)]);
        if ($checkSign['code'] == 200){
            pft_log('union/callback', '成功'.json_encode($jsonData));
        }
    }
    /**
     * 微信端支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $ordernum = I('post.ordernum');
        //支付场景：1订单支付,2充值
        $pay_scen  = I('post.pay_scen', 2);
        $isMiniApp = I('post.mini_app');
        $data      = ['scan_channel' => 0];
        $model = new OnlineTrade();
        if ($isMiniApp) {
            $scanLog = $model->checkQrCodeScan($ordernum);
            if ($scanLog) {
                $data['scan_channel'] = $scanLog['scan_channel'];
                $payLog               = $model->getLog($ordernum, OnlineTrade::CHANNEL_UNIONPAY_NEW);
                if ($payLog['status'] == 1) {
                    parent::apiReturn(parent::CODE_SUCCESS, $data, '支付成功', true);
                } else {
                    parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '未支付', true);
                }
            } else {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '请扫描二维码', true);
            }
        } else {
            $payLog = $model->getLog($ordernum, OnlineTrade::CHANNEL_UNIONPAY_NEW);
            if (!$payLog) {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
            }

            if ($payLog['status'] == 1) {
                parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
            }
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败', true);
        }
    }
    /**
     * 转换订单号
     */
    private function _orderChange($orderNum,$isCombine = false){
        if ($isCombine){
            $unionOrderId = str_replace('CMB-','QRCLOUD',$orderNum);
        }else{
            $unionOrderId = 'QR'.$orderNum;
        }
        return $unionOrderId;
    }
    /**
     * 回调转换订单号
     */
    private function _orderChangeNotify($ordernum){
        if (strpos($ordernum,'QRCLOUD') !== false){
            $outTradeNo = str_replace('QRCLOUD','CMB-',$ordernum);
        }else {
            $outTradeNo = str_replace('QR','',$ordernum);
        }
        return $outTradeNo;
    }
}
