<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 1/20-020
 * Time: 10:49
 *
 * 民生民生乐收银支付
 */

namespace Controller\pay;

use Business\Order\Query;
use Controller\pay\traits\HaboTrait;
use Library\Business\cmbc_wx_pay\WxSdk;
use Library\Controller;
use Model\Member\Member;
use Model\Member\Recharge;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\TradeRecord\OnlineTrade;

define('IN_PFT', true);
class CmbcWxPay extends Controller implements PayInterface
{
    use HaboTrait;

    const SOURCE_T    = 8;
    const PAY_CHANNEL = 29; //对应OrderTrack.source

    const RECHARGE_NOTIFY_URL     = PAY_DOMAIN . 'r/pay_CmbcWxPay/rechargeNotify/';
    const ORDER_NOTIFY_URL        = PAY_DOMAIN . 'r/pay_CmbcWxPay/orderNotify/';
    const RENEW_NOTIFY_URL        = PAY_DOMAIN . 'r/pay_CmbcWxPay/renewNotify/'; //平台会员充值通知地址
    const PARK_NOTIFY_URL         = PAY_DOMAIN . 'r/pay_CmbcWxPay/parkPayNotify/'; //停车场支付通知地址
    const ANNUAL_RENEW_NOTIFY_URL = PAY_DOMAIN . 'r/pay_CmbcWxPay/annualRenewNotify/'; //年卡续费通知地址

    private $sourceT = 8;

    private $wxPayLib = null;
    /**
     * @var $orderModel OrderTools
     */
    private $orderModel = null;
    private $orderInfo  = [];
    public function __construct()
    {
        $this->wxPayLib = new WxSdk();
    }

    /**
     * 续费
     */
    public function renew()
    {
        $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $memberId  = $loginInfo['sid'];

        $meal        = I('post.meal');
        $Renew       = new \Model\Member\Renew();
        $OnlineTrade = new \Model\TradeRecord\OnlineTrade();
        //获取套餐金额
        $money = $Renew->gitMealMoney($meal, $memberId);
        if ($money == false) {
            $this->apiReturn(204, [], '非法提交');
        }
        //请求日志
        $logData = json_encode($_REQUEST, JSON_UNESCAPED_UNICODE);
        pft_log('wepay_cmbc/renew', $memberId . ':' . $logData);
        //描述主体
        $body = '票付通平台续费充值|' . $meal;
        //生成平台流水号
        $outTradeNo = time() . $memberId . mt_rand(1000, 9999);
        //获取支付二维码
        $result = $this->wxPayLib->qrPay($money, $body, $outTradeNo, self::RENEW_NOTIFY_URL, $body, 'renew');
        if ($result['return_code'] != 'SUCCESS') {
            $this->apiReturn(204, [], $result['return_msg']);
        }
        //生成充值记录
        $create = $OnlineTrade->addRecord(
            $outTradeNo,
            $body,
            $money,
            '',
            json_encode(['memberId' => $memberId]),
            1
        );
        if (!$create) {
            $this->apiReturn(204, [], '充值记录生成失败');
        }

        $this->apiReturn(200, ['qrUrl' => $result['code_url'], 'outTradeNo' => $outTradeNo]);
    }
    /**
     * <AUTHOR> Chen
     * @date 2016-10-03
     *
     * @description 充值-post
     * @money:充值的金额，单位“元”
     * @openid:微信openid
     * @aid:供应商ID，可以为空；大于0表示授信预存
     * @did:充值的人的id
     * @appid:微信收款公众号
     * @is_qr:是否扫码支付
     * @memo:备注
     */
    public function recharge()
    {
        $money     = I('post.money');
        $money     = floatval(number_format($money, 2, '.', ''));
        $total_fee = $money * 100;
        $openid    = I('post.openid');
        $aid       = I('post.aid');
        $did       = I('post.did', 0);
        $is_qr     = I('post.qr_pay', 0);
        $pay_type  = I('post.pay_type', 0, 'intval');
        $memo      = I('post.memo', '', 'trim');

        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $sid       = $loginInfo['sid'] ?? 0;

        $did = $did ? $did : $sid;
        if (!$did) {
            exit('{"status":"fail","msg":"用户身份获取错误"}');
        }

        if ($is_qr == 0 && empty($openid)) {
            exit('{"status":"fail","msg":"OPENID为空"}');
        }
        if (!is_numeric($money) || $money < 0) {
            exit('{"status":"fail","msg":"请输入大于0的金额，金额必须是数字"}');
        }
        $modelMember = new Member();
        if ($did == 1) {
            $body = '补打款';
        } else {

            $queryParams = [[$did, $aid]];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                $queryParams);

            if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                exit('{"status":"fail","msg":"用户信息获取失败，请稍后重试"}');
            }

            $memberInfo = array_column($queryRes['data'], null, 'id');

            //$seller_nama = $modelMember->getMemberCacheById($did, 'dname');
            $seller_nama = $memberInfo[$did]['dname'];
            if ($aid > 0) {
                //$boss_name = $modelMember->getMemberCacheById($aid, 'dname');
                $boss_name = $memberInfo[$aid]['dname'];;
                $body      = "[$seller_nama]给{$boss_name}充值{$money}元|{$did}|$aid";
            } else {
                $body = "[{$seller_nama}]账户充值{$money}元|{$did}";
            }
        }
        if ($memo) {
            $body .= '|' . $memo;
        }
        $out_trade_no             = time() . $did . mt_rand(1000, 9999);
        $log_data                 = $_POST;
        $log_data['out_trade_no'] = $out_trade_no;
        $log_data['body']         = $body;
        unset($log_data);
        $pay_type = $pay_type == 1 ? WxSdk::API_ZFBQRCODE : WxSdk::API_WXQRCODE;

        //收单系统出现错误:body参数长度有误，所以对body参数做处理
        $payBody = $body;
        if (mb_strlen($body) > 20) {
            $payBody = mb_substr($body, 0, 20) . '...';
        }

        if ($is_qr) {
            $parameters = $this->wxPayLib->qrPay($total_fee, $body, $out_trade_no, self::RECHARGE_NOTIFY_URL, '微信充值', $pay_type);
            if ($parameters['return_code'] != 'SUCCESS') {

                //添加日志
                pft_log('recharge_error', json_decode($out_trade_no, $total_fee, $payBody, $pay_type, $parameters));

                $msg = "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['return_msg']}";
                if (!$is_qr) {
                    $msg .= ",您可以尝试使用【微信二维码支付】功能来完成充值";
                }

                parent::apiReturn(401, [], $msg);
            }
            $data = ['outTradeNo' => $out_trade_no, 'qrUrl' => $parameters['code_url']];
        } else {
            $parameters = $this->wxPayLib->jsApiPay($total_fee, $body, $out_trade_no, $openid, self::RECHARGE_NOTIFY_URL);
            if (is_array($parameters) && isset($parameters['return_code']) && $parameters['return_code'] != 'SUCCESS') {

                //添加日志
                pft_log('recharge_error', json_encode([$out_trade_no, $total_fee, $payBody, $pay_type, $parameters], JSON_UNESCAPED_UNICODE));

                parent::apiReturn(401, [], "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['return_msg']}");
            }
            $data = ['parameter' => json_decode($parameters), 'outTradeNo' => $out_trade_no];
        }
        $model = new OnlineTrade();
        $ret   = $model->addLog($out_trade_no, $money, $body, $body, self::SOURCE_T, OnlineTrade::PAY_METHOD_RECHARGE);
        if (!$ret) {
            parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
        }

        parent::apiReturn(200, $data);
    }

    /**
     * 民生在线支付
     * <AUTHOR>
     * @date   2017-06-28
     *
     * @param  int $pay_type 支付方式
     * @param  init $channel 支付渠道
     * @param  int $ordernum 订单号
     * @param  int $from 来源
     * @param  int $is_member 是不是分销商
     * @param  string $auth_code 授权码
     * @param  int $money 金额 - 元
     * @param  string $subject 备注
     * @param  int $pay_scen 支付场景
     * @param  string $verify 是否需要验证
     * @param  string $terminal 终端号
     *
     * @return [type]
     */
    public function micropay()
    {
        pft_log('/micropay/cmbc', json_encode($_POST));
        $pay_type    = I('post.pay_type', 1);
        $pay_channel = I('post.channel', 1);
        $outTradeNo  = I('post.ordernum');
        $from        = I('post.from');
        $is_member   = I('post.is_member');

        $authCode = I('post.auth_code');
        $totalFee = I('post.money') * 100;
        $subject  = I('post.subject', "支付订单({$outTradeNo})");

        //支付场景
        $payScen = I('post.pay_scen', 1);

        //是否支付后立即验证标识
        $needVerify = I('post.verify', 0);

        //支付时所使用的终端号
        $terminal = I('post.terminal', 0, 'intval');

        $checkSource     = I('post.check_source', -1, 'intval');//验证来源

        $notify_url = self::ORDER_NOTIFY_URL;
        switch ($payScen) {
            case 1:
                //订单支付
                if (is_numeric($outTradeNo)) {
                    $orderQuery = new OrderQuery();
                    $totalFee   = $orderQuery->get_order_total_fee($outTradeNo);
                    $notify_url = self::ORDER_NOTIFY_URL;
                }
                break;
            case 2:
                break;
        }
        if (empty($outTradeNo)) {
            $outTradeNo = 'micrpay_' . time();
        }

        $OnlineTrade = new OnlineTrade();
        $result      = $OnlineTrade->addLog($outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T, OnlineTrade::PAY_METHOD_ORDER);
        if (!$result) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }

        $pay_type = $pay_type == 1 ? WxSdk::API_ZFBSCAN : WxSdk::API_WXSCAN;
        if (ENV == 'DEVELOP' || ENV == 'LOCAL') {
            //if (ENV!=='PRODUCTION' ) {
            $res = ['return_code' => 'SUCCESS'];
        } else {
            $res = $this->wxPayLib->cardPay($totalFee, $subject, $outTradeNo, $notify_url, $authCode, $pay_type);
        }
        $code = parent::CODE_INVALID_REQUEST;
        if ($res['return_code'] === 'SUCCESS') {
            //参考微信刷卡支付的程序，这里做一个循环
            $queryTimes = 60;
            while ($queryTimes > 0) {
                $queryTimes--;
                $succResult = $this->orderResultCheck($outTradeNo, ['status' => 0], true, $terminal);
                //如果需要等待1s后继续
                if ($succResult == 2) {
                    usleep(500000); //sleep(0.5)
                    continue;
                } else if ($succResult == 1) {
                    //查询成功
                    $code = parent::CODE_SUCCESS;
                    $msg  = '支付成功';
                    break;
                } else {
                    //订单交易失败
                    $code = parent::CODE_INVALID_REQUEST;
                    $msg  = "支付失败,订单号:{$outTradeNo}";
                    break;
                }
            }
            $output = [];
            pft_log('/wepay_cmbc/req', json_encode($_POST) . ":" . json_encode($this->orderInfo));
            if ($needVerify && $code == parent::CODE_SUCCESS) {
                if (empty($this->orderInfo)) {
                    $this->orderInfo = $this->_getOrder($outTradeNo);
                }
                $query = new Query();
                if ($this->sourceT == 8) {
                    $paymode = 5;
                } elseif ($this->sourceT == 9) {
                    $paymode = 1;
                }
                //pft_log('debug', 'result:' . $needVerify .";orderinfo:" . json_encode($this->orderInfo, JSON_UNESCAPED_UNICODE) . ';output:'. json_encode($output, JSON_UNESCAPED_UNICODE));
                $output = $query->getOrderInfoForPrintByRpc($outTradeNo, $this->orderInfo, $totalFee, $paymode, $terminal,$checkSource,0);

            }
            parent::apiReturn($code, $output, $msg, true);
        }
        parent::apiReturn($code, [], "支付失败,订单号:{$outTradeNo}", true);
    }

    /**
     * 订单支付
     */
    public function order()
    {
        //平台订单号
        $outTradeNo = I('post.out_trade_no', 0, 'string');
        $qrPay      = I('post.is_qr', 0, 'intval'); //是否二维码支付
        //微信用户openid
        $openid   = I('post.openid', null);
        $remark   = I('post.remark', '微信订单支付');
        $pay_type = I('post.pay_type', 0);
        //订单主体说明
        $subject = mb_substr(trim(I('post.subject')), 0, 20, 'utf-8');
        //只允许 汉字 + 数字 + .
        $subject = preg_replace("/[^\x{4e00}-\x{9fa5}|1-9|\.]/iu", '', $subject);
        $subject .= "(订单号:{$outTradeNo})";
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }

        if (!$qrPay && !$openid) {
            parent::apiReturn(401, [], '请用微信APP打开进行支付');
        }

        //获取订单总金额
        $OrderQeury = new OrderQuery('localhost');
        $totalFee   = $OrderQeury->get_order_total_fee((string) $outTradeNo);
        if ($qrPay) {
            //生成微信支付二维码
            $pay_type          = $pay_type == 1 ? WxSdk::API_ZFBQRCODE : WxSdk::API_WXQRCODE;
            $prefix_outTradeNo = "qr_" . $outTradeNo;
            //当支付宝与微信同时请求的时候会有错误，所以订单号加上一个支付类型的前缀做标识，在异步通知的时候过滤
            if (I('post.both')) {
                $prefix_outTradeNo = $pay_type . $outTradeNo;
            }
            $payOrderNum = $prefix_outTradeNo;
            $parameters  = $this->wxPayLib->qrPay($totalFee, $subject, $prefix_outTradeNo, self::ORDER_NOTIFY_URL, $remark, $pay_type);
            if ($parameters['return_code'] != 'SUCCESS') {
                $msg = "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['return_msg']}";
                parent::apiReturn(401, [], $msg);
            }
            $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => $parameters['code_url']];
        } else {
            $payOrderNum = '';
            $parameters  = $this->wxPayLib->jsApiPay($totalFee, $subject, $outTradeNo, $openid, self::ORDER_NOTIFY_URL);
            if (is_array($parameters) && isset($parameters['return_code']) && $parameters['return_code'] != 'SUCCESS') {
                parent::apiReturn(401, [], "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['return_msg']}");
            }
            $data = json_decode($parameters);
        }
        $tradeModel = new OnlineTrade();
        $ret        = $tradeModel->addLog($outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T, OnlineTrade::PAY_METHOD_ORDER, $payOrderNum);
        if (!$ret) {
            parent::apiReturn(401, [], '支付记录生成失败');
        }

        parent::apiReturn(200, $data);
    }

    public function Query()
    {
        $ordernum     = I('post.ordernum');
        $trade_type   = I('post.trade_type', 1);
        $orgvoucherNo = I('orgvoucherNo', '');
        $res          = $this->wxPayLib->Query($ordernum, $trade_type, $orgvoucherNo);
        parent::apiReturn(parent::CODE_SUCCESS, $res);
    }

    /**
     * 微信端支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $ordernum = I('post.ordernum');
        //支付场景：1订单支付,2充值
        $pay_scen = I('post.pay_scen', 2);
        $pay_type = 0;
        if (isset($_POST['pay_type'])) {
            $pay_type = intval($_POST['pay_type']);
        }
        //todo::检测支付状态
        $model     = new OnlineTrade();
        $_ordernum = self::filter_ordernum($ordernum);
        $pay_log   = $model->getLog($_ordernum, self::SOURCE_T);
        if (!$pay_log) {
            parent::apiReturn(parent::CODE_CREATED, [], '支付记录不存在', true);
        }

        pft_log('wepay_cmbc/notify_delay', "接口已经弃用 - 民生通知延迟处理:pay_scen=$pay_scen&ordernum=$ordernum&status={$pay_log['status']}");

        //这边接口已经弃用
        parent::apiReturn(parent::CODE_CREATED, [], '接口已经弃用', true);

        //若系统支付状态还未更新，请求银行数据
        if ($pay_scen == 1) {
            $res = $this->orderResultCheck($ordernum, $pay_log);
            if ($pay_type == 1 && strpos($pay_log['buyer_email'], 'buyer_id') === false) {
                // 支付宝
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败', true);
            } elseif ($pay_type == 2 && strpos($pay_log['buyer_email'], 'openid') === false) {
                // 微信支付
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败', true);
            }
            if ($res == 1) {
                parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
            }

            //if ($res == 1) parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败', true);
        } elseif ($pay_scen == 2) {
            $this->rechargeResultCheck($ordernum, $pay_log);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], 'Unknow Request', true);
    }
    /**
     * 手工处理
     */
    public function ManualPay()
    {
        $ordernum = I('post.ordernum');
        $res      = $this->wxPayLib->Query($ordernum, 1, '');
        if ($res['tradeStatus'] !== 'S') {
            exit('FAIL');
        }
        //由于民生银行狗日的接口，异步通知返回的字段与查询接口返回的字段不统一
        if (!isset($res['orderNo']) && isset($res['merchantSeq'])) {
            $res['orderNo'] = $res['merchantSeq'];
        }
        $ret = $this->rechargeHandler(['data' => $res]);
        if ($ret === true) {
            parent::apiReturn(parent::CODE_SUCCESS, [], '处理成功');
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '处理失败');
    }

    public function Refund()
    {
        parent::apiReturn(parent::CODE_AUTH_ERROR);
    }
    /**
     * 充值异步通知处理
     */
    public function rechargeNotify()
    {
        $raw    = file_get_contents('php://input');
        $result = $this->wxPayLib->PayNotify($raw);
        if ($result['return_code'] !== 'SUCCESS') {
            pft_log('wepay_cmbc/recharge', "fail:" . json_encode($result, JSON_UNESCAPED_UNICODE));
            exit('FAIL');
        }
        if ($result['data']['tradeStatus'] !== 'S') {
            exit('FAIL');
        }

        $res = $this->rechargeHandler($result);
        //TODO::包含全局文件
        if ($res !== true) {
            echo 'FAIL';
        } else {
            echo 'SUCCESS';
        }
    }

    /**
     * 订单支付异步通知 /r/pay_CmbcWxPay/orderNotify/
     */
    public function orderNotify()
    {
        $raw    = file_get_contents('php://input');
        $result = $this->wxPayLib->PayNotify($raw);
        if ($result['return_code'] !== 'SUCCESS') {
            pft_log('wepay_cmbc/order', "fail:" . json_encode($result, JSON_UNESCAPED_UNICODE));
            exit('FAIL');
        }
        if ($result['data']['tradeStatus'] !== 'S') {
            exit('FAIL');
        }
        $ordernum = self::filter_ordernum($result['data']['orderNo']);
        $res      = $this->orderHandler($result);
        if ($res == 100) {
            $this->orderModel = new OrderTools('localhost');
            $order_info       = $this->orderModel->getOrderInfo($ordernum, 'member,ordertel,aid');
            if ($order_info['ordertel'] != '' && $order_info['ordertel'] != '00000000000') {
                //重试三次
                $resendTimes = 3;
                while ($resendTimes--) {
                    $jobId = $this->_orderNotidyJob($ordernum, $order_info);
                    if ($jobId) {
                        break;
                    }
                }
            }
            pft_log('wepay_cmbc/notify', "订单民生通知处理:ordernum=$ordernum;job_id=$jobId");
            echo 'SUCCESS';
        } else {
            echo 'FAIL';
        }
    }

    /**
     * 插入订单通知队列
     * <AUTHOR>
     * @date   2017-07-31
     * @param  string     $ordernum   订单号
     * @param  array     $orderInfo 订单信息
     * @return string
     */
    private function _orderNotidyJob($ordernum, $orderInfo)
    {

        $args = [
            'ordernum' => $ordernum,
            'buyerId'  => $orderInfo['member'], //购买人的ID
            'mobile'   => $orderInfo['ordertel'],
            'aid'      => $orderInfo['aid'],
        ];

        $jobId = \Library\Resque\Queue::push('notify', 'OrderNotify_Job', $args);

        if (!$jobId) {
            pft_log('wepay_cmbc/notify_fail', $ordernum);
        }

        return $jobId;
    }

    public function orderNotifyManual()
    {
        $raw    = file_get_contents('php://input');
        $result = json_decode($raw, true);
        //print_r($result);
        //echo $result['data']['orderNo'];exit;
        $ordernum = self::filter_ordernum($result['data']['orderNo']);
        $res      = $this->orderHandler($result);
        if ($res == 100) {
            $this->orderModel = new OrderTools('slave');
            $order_info       = $this->orderModel->getOrderInfo($ordernum, 'member,ordertel,aid');
            $job_id = \Library\Resque\Queue::push('notify', 'OrderNotify_Job',
                [
                    'ordernum' => $ordernum,
                    'buyerId'  => $order_info['member'], //购买人的ID
                    'mobile'   => $order_info['ordertel'],
                    'aid'      => $order_info['aid'],
                ]
            );
            pft_log('wepay_cmbc/notify', "订单民生通知处理:ordernum=$ordernum;job_id=$job_id");
            echo 'SUCCESS';
        } else {
            echo 'FAIL';
        }
    }
    /**
     * 统一请求银行系统的支付信息
     *
     * @param string $ordernum 订单号
     * @return array
     */
    private function _queryBankPayInfo($ordernum)
    {
        if (ENV == 'DEVELOP' || ENV == 'LOCAL') {
            $tradNo = '400' . str_replace('.', '', microtime());
            $res    = [
                'orderNo'     => $ordernum,
                'tradeStatus' => 'S',
                'amount'      => ********,
                'centerSeqId' => $tradNo,
                'centerInfo'  => "openid=test12301|is_subscribe=N|bank_type=CIB_CREDIT|time_end=**************|payeeSeq=4006072001201705262683730522|transaction_id={$tradNo}|",
                'merchantNo'  => 'M15002017020000352749',
            ];
            return $res;
        }
        $res = $this->wxPayLib->Query($ordernum, 1, '');
        //pft_log('cmbc_wepay/debug', 'queryBankInfo:'.json_encode($res));
        //由于民生银行狗日的接口，异步通知返回的字段与查询接口返回的字段不统一
        if (!isset($res['orderNo']) && isset($res['merchantSeq'])) {
            $res['orderNo'] = $res['merchantSeq'];
        }
        return $res;
    }

    /**
     * 充值完成检测
     *
     * @param $ordernum
     * @param array $pay_log
     */
    private function rechargeResultCheck($ordernum, array $pay_log)
    {
        $openid      = I('post.openid');
        $fid         = I('post.did');
        $aid         = I('post.aid');
        $modelMember = new Member('localhost');
        if ($pay_log['status'] !== 1) {
            $res = $this->_queryBankPayInfo($ordernum);
            if ($res['tradeStatus'] !== 'S') {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, ['payStatus' => 0], '银行您还未接收到您的支付请求，请确认您是否已经支付成功');
            }
            $ret = $this->rechargeHandler(['data' => $res]);

            if ($ret === true) {
                $moneyArr    = $modelMember->getMoney($fid, 3, $aid, true); //查询会员账户可用授信额度
                $creditMoney = ($moneyArr['basecredit'] + $moneyArr['kmoney']) / 100;
                pft_log('wepay_cmbc/notify_delay', "民生通知延迟处理:fid=$fid&ordernum=$ordernum");
                parent::apiReturn(parent::CODE_SUCCESS, ['money' => $creditMoney, 'payStatus' => 1], '支付成功', true);
            }
            parent::apiReturn(parent::CODE_INVALID_REQUEST, ['payStatus' => 0], '系统发生异常', true);
        }
        $moneyArr    = $modelMember->getMoney($fid, 3, $aid, true); //查询会员账户可用授信额度
        $creditMoney = ($moneyArr['basecredit'] + $moneyArr['kmoney']) / 100;
        parent::apiReturn(parent::CODE_SUCCESS, ['money' => $creditMoney, 'payStatus' => 1], '支付成功', true);
    }

    private function _getOrder($ordernum)
    {
        $this->orderModel = new OrderTools('localhost');
        $orderInfo        = $this->orderModel->getOrderInfo($ordernum, 'member,ordername,ordertel,aid,code,lid,tid,tnum,tprice,paymode,salerid', 'de.aids,de.series');
        if ($orderInfo['aids']) {
            // 转分销获取第二级分销商
            $tmp                 = explode(',', $orderInfo['aids']);
            $orderInfo['member'] = $tmp[1];
        }
        $this->orderInfo = $orderInfo;
        return $orderInfo;
    }
    /**
     * 订单支付完成检测
     *
     * @param string $ordernum
     * @param array $pay_log
     * @param bool $is_micropay
     * @param bool $terminal 购票终端号
     * @return int
     */
    private function orderResultCheck($ordernum, array $pay_log, $is_micropay = false, $terminal = 0)
    {
        if ($pay_log['status'] !== 1) {
            $queryOrdernum = !empty($pay_log['royalty_parameters']) ? $pay_log['royalty_parameters'] : $ordernum;
            $res           = $this->_queryBankPayInfo($queryOrdernum);
            $cmp           = strcmp($res['tradeStatus'], 'S');
            if ($cmp != 0) {
                $res = $this->_queryBankPayInfo(WxSdk::API_WXQRCODE . $ordernum);
                $cmp = strcmp($res['tradeStatus'], 'S');
                if ($cmp != 0) {
                    return 2;
                }
            }

            $ret = $this->orderHandler(['data' => $res], $terminal);
            //pft_log('cmbc_wepay/debug', 'orderResultCheck:'.$ordernum . ':'. $res['tradeStatus'] .':res=' .$cmp. ";ret={$ret}");

            if ($ret === 101 || $ret == 5005) {
                if ($is_micropay === true) {
                    $this->_getOrder($ordernum);
                }
                return 1;
            }
            if ($ret === 100) {
                $orderInfo = $this->_getOrder($ordernum);
                $job_id = \Library\Resque\Queue::push('notify', 'OrderNotify_Job',
                    [
                        'ordernum' => $ordernum,
                        'buyerId'  => $orderInfo['member'], //购买人的ID
                        'mobile'   => $orderInfo['ordertel'],
                        'aid'      => $orderInfo['aid'],
                    ]
                );
                if ($orderInfo['aids']) {
                    // 转分销获取第二级分销商
                    $tmp                 = explode(',', $orderInfo['aids']);
                    $orderInfo['member'] = $tmp[1];
                }
                //pft_log('debug', "orderinfo:" . json_encode($this->orderInfo ,JSON_UNESCAPED_UNICODE));
                pft_log('wepay_cmbc/notify_delay', "订单民生通知延迟处理:ordernum=$ordernum;job_id=$job_id");
                return 1;
            }
            return 2;
        }
        return 1;
    }

    /**
     * 做充值处理
     *
     * @param array $result 银行返回的数据信息
     * @return bool
     */
    private function rechargeHandler(array $result)
    {
        //打印调试信息
        if (I('post.debug', 0, 'intval') == 1) {
            print_r($result);
        }

        $pay_total_fee = $result['data']['amount'];
        $trade_no      = $result['data']['centerSeqId']; //微信/支付宝订单号
        parse_str(str_replace('|', '&', $result['data']['centerInfo']), $certerInfoList);
        $wx_app_id = $wx_sub_app_id = PFT_WECHAT_APPID;
        $json_sell = json_encode(['merchantNo' => $result['data']['merchantNo'], 'platformId' => $result['data']['platformId']]);
        $sourceT   = self::SOURCE_T;
        if (isset($certerInfoList['transaction_id'])) {
            $this->sourceT = 8;
            //微信
            $json_buy = json_encode(['openid' => $certerInfoList['openid'], 'bankTradeNo' => $result['data']['bankTradeNo'], 'bankOrderNo' => $result['data']['bankOrderNo']]);
        } elseif (isset($certerInfoList['tradeNo']) || isset($certerInfoList['buyer_user_id'])) {
            $this->sourceT = 9;
            //支付宝
            $json_buy = json_encode(['buyer_id' => $certerInfoList['buyer_id'], 'bankTradeNo' => $result['data']['bankTradeNo'], 'bankOrderNo' => $result['data']['bankOrderNo']]);
        }

        //旧逻辑，已经弃用
        // $modelRecharge = new Recharge();
        // $res = $modelRecharge->OnlineRecharge($result['data']['orderNo'], $this->sourceT, $trade_no, $pay_total_fee, $json_buy,
        //     $json_sell, $wx_app_id, $wx_sub_app_id);

        $accountMoneyBiz = new \Business\Finance\AccountMoney\AccountMoney();
        $res             = $accountMoneyBiz->recharge($result['data']['orderNo'], $this->sourceT, $trade_no, $pay_total_fee, $json_buy,
            $json_sell, $wx_app_id, $wx_sub_app_id);

        return $res;
    }

    /**
     * 订单支付成功处理
     *
     * @param array $result
     * @param int $terminal 购票终端号
     * @return mixed
     */
    private function orderHandler(array $result, $terminal = 0)
    {
        $pay_to_pft    = true;
        $pay_total_fee = $result['data']['amount'];
        $trade_no      = $result['data']['centerSeqId']; //微信/支付宝订单号
        parse_str(str_replace('|', '&', $result['data']['centerInfo']), $certerInfoList);
        $daction       = $result['data']['remark'] == 'TICKET_MACHINE_12301' ? 0 : null;
        $json_sell     = json_encode(['merchantNo' => $result['data']['merchantNo'], 'platformId' => $result['data']['platformId']]);
        $json_buy      = '';
        $this->sourceT = self::SOURCE_T;
        $ordernum      = self::filter_ordernum($result['data']['orderNo']);
        if (isset($certerInfoList['openid'])) {
            $this->sourceT = 8;
            //微信
            $json_buy = json_encode(['openid' => $certerInfoList['openid'], 'bankTradeNo' => $result['data']['bankTradeNo'], 'bankOrderNo' => $result['data']['bankOrderNo']]);
        } elseif (isset($certerInfoList['buyer_id']) || isset($certerInfoList['buyer_user_id']) || isset($certerInfoList['buyer_logon_id'])) {
            $this->sourceT = 9;
            //支付宝
            $json_buy = json_encode(['buyer_id' => $certerInfoList['buyer_id'], 'bankTradeNo' => $result['data']['bankTradeNo'], 'bankOrderNo' => $result['data']['bankOrderNo']]);
        }
        //$res = parent::getSoap()->Change_Order_Pay($ordernum, $trade_no, $this->sourceT, $pay_total_fee, $daction,
        //    $json_sell, $json_buy, 1, $pay_to_pft, self::PAY_CHANNEL);
        $res = self::getServerInside()->Change_Order_Pay($ordernum, $trade_no, $this->sourceT, $pay_total_fee, $daction,
            $json_sell, $json_buy, 1, $pay_to_pft, self::PAY_CHANNEL, $result['data']['orderNo'], $terminal);
        pft_log('wepay_cmbc/notify', "$ordernum, $res");
        return $res;
    }

    private function filter_ordernum($ordernum)
    {
        $search = ['qr_', WxSdk::API_WXSCAN, WxSdk::API_WXQRCODE, WxSdk::API_ZFBQRCODE, WxSdk::API_ZFBSCAN];
        return str_replace($search, '', $ordernum);
    }

    /**
     * 年卡微信续费
     * <AUTHOR>
     * @date   2017-11-27
     */
    public function annualRenew()
    {
        $this->apiReturn(204, [], '接口已关闭');
    }

    /**
     * 年卡续费通知
     * <AUTHOR>
     * @date   2017-11-28
     */
    public function annualRenewNotify()
    {

        $raw = file_get_contents('php://input');

        $result = $this->wxPayLib->PayNotify($raw);

        if ($result['return_code'] !== 'SUCCESS') {
            $error = "fail:" . json_encode($result, JSON_UNESCAPED_UNICODE);
            pft_log('wepay/annualrenew', $msg, 'day');
            exit('FAIL');
        }
        if ($result['data']['tradeStatus'] !== 'S') {
            exit('FAIL');
        }

        $vipBiz = new \Business\Mall\MemberVip(1, 1);

        //总金额
        $totalFee = $result['data']['amount'];
        //续费订单号
        $outTradeNo = $result['data']['orderNo'];
        //微信订单号
        $tradeNo = $result['data']['centerSeqId'];

        $result = $vipBiz->wechatRenewNotify($outTradeNo, $totalFee, $tradeNo);

        @pft_log('wepay/annualrenew', json_encode($result), 'day');

        if ($result['code'] == 200) {
            echo 'SUCCESS';
        } else {
            echo 'FAIL';
        }
    }

    /**
     * 会员卡支付
     */
    public function cardSolutionRecharge()
    {
        parent::apiReturn(401, [], '接口废弃');
    }

}
