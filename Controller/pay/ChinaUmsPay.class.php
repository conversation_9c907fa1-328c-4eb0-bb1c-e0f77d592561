<?php
/**
 * Created by PhpStorm.
 * User: linchen
 * Date: 2019/1/17 0017
 * Time: 17:12
 */
namespace Controller\pay;
use Business\Order\MergeOrder;
use Business\Pay\PayBase;
use Business\Pay\PayCache;
use Model\Order\OrderQuery;
use Library\Business\WePay\WxPayApi;
use Library\Controller;
use Library\Tools\Helpers;
use Model\Order\OrderTools;
use Model\TradeRecord\OnlineTrade;
use Model\Order\TeamOrderSearch;
use Business\Order\Query;
use Library\Constants\DingTalkRobots;
define('IN_PFT', true);
class ChinaUmsPay extends Controller implements PayInterface {
    private $merchantId   = '';
    private $terminalCode = '';
    private $instMid      = '';
    private $msgSrc       = '';
    private $msgSrcId     = '';
    private $sign         = '';
    private $appid        = '';
    private $appkey       = '';
    private $orderModel = null;
    private $encryptionMode = 0; //0-公众号md5 1-sha256
    private $pay_type   = 1;
    private $memberId;
    private $errCode      = [
        '03'  => '无效商户',
        '13'  => '无效金额',
        '22'  => '原交易不存在',
        '25'  => '找不到原始交易',
        '30'  => '报文格式错误',
        '57'  => '不允许此交易',
        '61'  => '超出金额限制',
        '64'  => '原始金额错误',
        '92'  => '发卡方线路异常',
        '94'  => '重复交易,请重新下单',
        '96'  => '交换中心异常',
        '97'  => '终端号未登记',
        'A7'  => '安全处理失败',
        'ER'  => '参见具体返回信息',
    ];
    public function __construct($config = null,$memberId = 0,$pay_type = 1)
    {
        $this->merchantId     = $config['merchant_id'] ? $config['merchant_id'] : '';
        $this->terminalCode   = $config['terminal_code'] ? $config['terminal_code']: '';
        $this->instMid        = $config['inst_mid']? $config['inst_mid'] : '';
        $this->msgSrc         = $config['msg_src']? $config['msg_src'] : '';
        $this->msgSrcId       = $config['msg_src_id'] ? $config['msg_src_id'] :'';
        $this->sign           = $config['sign'] ? $config['sign'] : '';
        $this->appid          = $config['appid'] ? $config['appid'] :'';
        $this->appkey         = $config['appkey'] ? $config['appkey'] : '';
        $this->encryptionMode = $config['encryption_mode'] ? $config['encryption_mode'] : 0;
        $this->pay_type       = $pay_type;
        $this->memberId       = $memberId;
    }
    /**
     * 支付后的回调
     * 如果受理端接口调不通
     * 这里通过支付接口 返回的 jsonRequestData
     */
    public function orderNotify(){
        // $jsonData = I('post.');
        $jsonData = I('post.','','stripslashes');
        pft_log('chinaums/order', 'orderNotify='.json_encode($jsonData));
        $merBiz = new \Business\Finance\PayMerchant();
        //记录日志
        PayBase::setPayDailyRecord(7, '回调', [], $jsonData);
        $chinaUmsConfig  = $merBiz->getChinaUmsMerchantConfig('',$jsonData['mid'],0);
        if (empty($chinaUmsConfig)){
            echo 'FAILED';
            exit;
        }else{
            $this->sign = $chinaUmsConfig['sign'];
        }
        if ($jsonData['status'] == 'TRADE_REFUND'){
            echo 'SUCCESS';
            exit;
        }
        if (!$this->makeOrderRequest($jsonData,$this->sign) || $jsonData['status'] != 'TRADE_SUCCESS'){
            echo 'FAILED';
            exit;
        }
        //截取订单号的来源编号
        $count = strlen($chinaUmsConfig['msg_src_id']);
        $outTradeNo     = substr($jsonData['merOrderId'],$count); //订单号
        $transaction_id = $jsonData['targetOrderId']; //交易号
        $total_fee      = (int) $jsonData['totalAmount'] + 0; //金额用分为单位
        $pay_channel    = 39;
        $payTerminal    = '';
        $buyerInfo      = '';
        $sellerInfo     = array('buyerId' => $jsonData['buyerId']);
        $pay_to_pft     = 0;
        //理论上是不支付到票付通的吧
        $json_buy  = $buyerInfo;
        $json_sell = json_encode($sellerInfo);
        //$biz       = new \Business\Order\MergeOrder();

        $options = [
            'buyer_info'   => $json_buy,
            'sell_info'    => $json_sell,
            'pay_channel'  => $pay_channel,
            'pay_termianl' => $payTerminal,
        ];

        $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_CHINAUMS,
            $total_fee, (int)$pay_to_pft, $options);
        if ($result['code'] == 200) {
            $needVerify = PayCache::getOrderVerfify($outTradeNo);
            if ($needVerify) {
                $successOrders = $result['data']['success_orders'];
                $paymentObj = new \Business\Order\MergeOrder();
                $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
            }
        }
        if ($result['code'] == 200 || $result['code'] == 102 || $result['code'] == 101) {
            echo 'SUCCESS';
        } else {
            echo 'FAILED';
        }
        exit;

//        if ($biz->isCombineOrder($outTradeNo)) {
//            $result = $biz->handlerCombinePayOrders($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_CHINAUMS, $is_member,
//                $json_sell, $json_buy, $pay_to_pft, $pay_channel, $payTerminal);
//
//            $needVerify = \Library\Cache\Cache::getInstance('redis')->get('ordernum_needVerify:'.$outTradeNo);
//            if ($needVerify) {
//                $successOrders = $result['data']['success_orders'];
//                $paymentObj = new \Business\Order\MergeOrder();
//                $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
//            }
//        } else {
//            try {
//                $res = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_CHINAUMS,
//                    $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $pay_channel, '', $payTerminal);
//            } catch (\SoapFault $e) {
//                $retry = true;
//                pft_log('order_pay/error', "(微信银联)订单支付失败:{$outTradeNo}，soap抛出异常:{$e->getCode()}->{$e->getMessage()}");
//                //记录日志
//                $logData = json_encode([
//                    'key' => '微信银联订单支付',
//                    'ordernum' => $outTradeNo,
//                    'error' => "soap抛出异常:{$e->getCode()}->{$e->getMessage()}"
//                ], JSON_UNESCAPED_UNICODE);
//                pft_log('chinaums_qrorder', $logData, 3);
//
//                Helpers::sendDingTalkGroupRobotMessage("[{$outTradeNo}]订单支付失败;soap抛出异常:{$e->getCode()}->{$e->getMessage()}", "支付失败", Helpers::getServerIp(), DingTalkRobots::MYSQL_ERROR);
//            }
//            if (isset($retry) && $retry === true) {
//                $res = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_WEPAY,
//                    $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $pay_channel, '', $payTerminal);
//            }
//            if ($res === 100) {
//                $orderInfo = $this->_getOrder($outTradeNo);
//                if (Helpers::isMobile($orderInfo['ordertel'])) {
//                    $args = [
//                        'ordernum' => $outTradeNo,
//                        'buyerId'  => $orderInfo['member'], //购买人的ID
//                        'mobile'   => $orderInfo['ordertel'],
//                        'aid'      => $orderInfo['aid'],
//                    ];
//                    $jobId = \Library\Resque\Queue::push('notify', 'OrderNotify_Job', $args);
//                }
//            } else {
//                echo 'FAILED';
//                exit;
//            }
//        }
//        echo 'SUCCESS';
//        exit;
    }

    public function makeOrderRequest($payObj, $md5Key)
    {
        $params = $payObj;
        $client = new \Library\Tools\YarClient('pay');
        if (isset($params['signType'])  && $params['signType'] == 'SHA256') {  //用sha256的
            $signData = $client->call('Pay/ChinaUmsPay/makeSha256Sign',[$md5Key,$params]);
            if ($signData['code'] != 200){
                pft_log('chinaums/error', '签名验证失败' . $params['merOrderId']);
                return false;
            }
            if ($params['sign'] == $signData['res']) {
                return true;
            }
            pft_log('chinaums/error', '签名验证失败' . $params['merOrderId']);
            return false;
        } else {
            $signData = $client->call('Pay/ChinaUmsPay/makeSign',[$md5Key,$params]);
            if ($signData['code'] != 200){
                pft_log('chinaums/error', '签名验证失败' . $params['merOrderId']);
                return false;
            }
            if ($params['sign'] == $signData['res']) {
                return true;
            }
            pft_log('chinaums/error', '签名验证失败' . $params['merOrderId']);

            return false;
        }
    }
    // 加密算法
    private function makeSign($md5Key, $params) {
        $str = $this->buildSignStr($params) . $md5Key;
        return strtoupper(md5($str));
    }
    // 获取加密的参数字符串
    private function buildSignStr($params) {
        $keys = [];
        foreach($params as $key => $value) {
            if ($key == 'sign')
            {
                continue;
            }
            array_push($keys, $key);
        }
        $str = '';
        sort($keys);
        $len = count($keys);
        for($i = 0; $i < $len; $i++) {
            $v = $params[$keys[$i]];
            if (is_array($v)) {
                $v = json_encode($v);
            }
            $str .= $keys[$i] . '=' . $v . (($i === $len -1) ? '' : "&");
        }
        return $str;
    }
    public function renew(){

    }
    public function recharge(){

    }
    public function order(){
        //平台订单号
        $outTradeNo = I('out_trade_no', 0, 'string');
        //是否二维码支付
        $qrPay = I('is_qr', 0, 'intval');
        //微信用户openid
        $openid = I('openid', null);
        // 公众号对应的实际openid——独立收款商户
        $subAppid   = I('post.sub_appid', null);
        $subOpendid = I('post.sub_openid', null);
        $shopHost   = I('post.shop_host',0);
        if (!is_null($subAppid) && $subAppid == WxPayApi::$sub_appid && $subOpendid) {
            $openid = $subOpendid;
        }
        //订单主体说明
        $subject = mb_substr(trim(I('subject')), 0, 20, 'utf-8');
        if (!$outTradeNo || !$subject || !$shopHost) {
            parent::apiReturn(401, [], '参数缺失');
        }

        $mergeOrder = new MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
            try {
                $mergeOrder->handlerCombinePayLog($outTradeNo, '', OnlineTrade::CHANNEL_CHINAUMS, $this->memberId);
            } catch (\Exception $e) {
                parent::apiReturn(401, [], $e->getMessage());
            }
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string) $outTradeNo);
        }
        if (!$openid) {
            parent::apiReturn(401, [], '请用微信APP打开进行支付');
        }
        //真实支付
        $client = new \Library\Tools\YarClient('pay');
        //1.$mid int 商户号 2 $tid int 终端号 3 $msgSrcId int 来源编号4 $sign string MD5密钥 5 $msgSrc string 消息来源 6 $money int 金额7 $orderId string 订单号 8 $returnUrl string 返回地址
        $res  = $client->call('Pay/ChinaUmsPay/chinaUmsPay', [$this->merchantId,$this->terminalCode,$this->msgSrcId,$this->sign,
            $this->msgSrc,$totalFee,$outTradeNo,$shopHost."h5/ordersuccess/c?ordernum={$outTradeNo}",$subject,$openid,$this->appid,$this->appkey,$this->encryptionMode]);
        PayBase::setPayDailyRecord(7, '公众号', [$outTradeNo], $res);
        if ($res['code'] != 200){
            parent::apiReturn(401, [], '支付地址生成失败');
        }else{
            $payMethod = OnlineTrade::PAY_METHOD_ORDER;
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog($outTradeNo, $totalFee / 100, $subject, $subject, OnlineTrade::CHANNEL_CHINAUMS, $payMethod, '', $this->memberId);
            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }
        }
        echo json_encode(['code' => 200, 'data' => $res['res'], 'status' => 'ok', 'msg' => 'success','chinaums' => 1]);
        exit;
    }
    public function cardSolutionRecharge(){

    }
    public function micropay(){
        $outTradeNo  = I('post.ordernum');
        $authCode    = I('post.auth_code','', 'trim');
        $totalFee    = I('post.money',0, 'float2int');
        $isMember    = I('post.is_member', 0);
        $subject     = I('post.subject', '订单支付');
        $payScen     = I('post.pay_scen', 1);
        $payTerminal = I('post.terminal', 0, 'intval'); //支付的终端
        $needVerify  = I('post.verify', 0);//是否支付后立即验证标识
        $terminal    = I('post.terminal', 0, 'strval');//支付时所使用的终端号
        $pay_type   = I('post.pay_type', 2, 'intval');
        $mergePayFlag = false;// 合并付款标识
        //支付来源 - 具体可以参照business.conf.php -> track_source
        // 0=黑色终端机 2=自助机 4=云票务 5=云闸机 20=安卓智能终端机
        $paySource = I('post.pay_source', 20, 'intval');
        //支付的操作人 可以不传 用在记录追踪表李
        $payTrackOpId  = I('post.pay_track_op_id', 0);
        //年卡下单类型  默认值为空字符串, 0年卡下单 1年卡续费
        $annualCardOrderType = I('post.annualCardOrderType', false);
        $checkSource     = I('post.check_source', -1, 'intval');//验证来源
        if (!$outTradeNo || !$authCode) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }
        //扫码支付授权码，设备读取用户微信中的条码或者二维码信息
        //（注：用户刷卡条形码规则：18位纯数字，以10、11、12、13、14、15开头）
        $codeLength = strlen($authCode);
        if ($codeLength < 16 || $codeLength > 24 || !is_numeric($authCode)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "非法的支付条码[{$authCode}]，请重新刷新支付码", true);
        }
        $paymentObj      = new \Business\Order\MergeOrder();
        if($paymentObj->isCombineOrder($outTradeNo)){
            $mergePayFlag = true;
            $paymentObj->handlerCombinePayLog($outTradeNo, $subject, OnlineTrade::CHANNEL_CHINAUMS_POS, $this->memberId);
        }

        PayBase::setPayDailyRecord(6,'扫码请求',I('post.'));
        switch ($payScen) {
            case 1:
                //订单支付
                if (is_numeric($outTradeNo) || $mergePayFlag === true) {
                    $orderQuery = new OrderQuery('localhost');
                    $totalFee  = $orderQuery->get_order_total_fee($outTradeNo);
                }
                break;
            case 2:
                break;
        }
        $OnlineTrade = new OnlineTrade();
        $result      = $OnlineTrade->addLog($outTradeNo, $totalFee / 100, $subject, $subject, OnlineTrade::CHANNEL_CHINAUMS_POS, OnlineTrade::PAY_METHOD_ORDER, '', $this->memberId);
        if (!$result) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }
        $client = new \Library\Tools\YarClient('pay');
        //1.$mid int 商户号 2 $tid int 终端号 3 $msgSrcId int 来源编号4 $sign string MD5密钥 5 $msgSrc string 消息来源 6 $money int 金额7 $orderId string 订单号 8 $returnUrl string 返回地址
        $params     = [$this->memberId,$this->appid,$this->appkey,$this->merchantId,$this->terminalCode,$authCode,$totalFee,$outTradeNo,$subject];
        $payResult  = $client->call('Pay/ChinaUmsPay/chinaUmsPosPay', $params);
        PayBase::setPayDailyRecord(6,'扫码三方返回',$params,$payResult);
        if ($payResult['code'] == 200 && isset($payResult['res']['errCode']) && $payResult['res']['errCode'] != '00'){
            if ($payResult['res']['errCode'] == 'ER') {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [],$payResult['res']['errInfo'] , true);
            } else {
                $errorInfo = isset($this->errCode[$payResult['res']['errCode']]) ? $this->errCode[$payResult['res']['errCode']] : '异常错误';
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [],$errorInfo, true);
            }
        }
        $num = 0;
        $getMoneyFlag = false;
        do {
            $payResult  = $client->call('Pay/ChinaUmsPay/chinaUmsPosQuery', [$this->memberId,$this->appid,$this->appkey,$this->merchantId,$this->terminalCode,$outTradeNo]);
            if ($payResult['code'] == 200 && $payResult['res']['queryResCode'] == '00'){
                $getMoneyFlag = true;
                break;
            }
            $num++;
            sleep(1);
        } while($num < 60);

        if (!$getMoneyFlag) {
            // 如果60秒后还没收款成功冲正
            //$reverseResult  = $client->call('Pay/ChinaUmsPay/chinaUmsPosReverse', [$this->memberId,$this->appid,$this->appkey,$this->merchantId,$this->terminalCode,$outTradeNo,$totalFee]);
            pft_log('chinaums_reverse',json_encode([
                'ordernum'=>$outTradeNo,
                'msg'=>'支持超时,需要冲正',
                'data'=>[$this->memberId,$this->appid,$this->appkey,$this->merchantId,$this->terminalCode,$outTradeNo,$totalFee]], JSON_UNESCAPED_SLASHES|JSON_UNESCAPED_UNICODE),3
            );
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '请求银联商务接口失败:' . $payResult['res']['queryResCode'], true);
        }
        PayBase::setPayDailyRecord(6,'扫码三方返回+查询结果',$params,$payResult);
        if ($payResult['res']['queryResCode'] != '00') {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "支付失败,订单号:{$outTradeNo},请求银联商务失败", true);
        }
        $buyerInfo = isset($payResult['res']['thirdPartyName']) ? $payResult['res']['thirdPartyName']: '';
        $sellerInfo = '';
        $transactionId = $payResult['res']['orderId'];
        $jsonBuy       = $buyerInfo;
        $jsonSell      = $sellerInfo;
        $payToPft      = false;
        $payChannel    = 40;


        $options = [
            'buyer_info'   => $jsonBuy,
            'sell_info'    => $jsonSell,
            'pay_channel'  => $payChannel,
            'pay_termianl' => $payTerminal,
            'oper'         => $payTrackOpId,
            'pay_source'   => $checkSource,
        ];

        $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_CHINAUMS_POS, $totalFee, (int)$payToPft, $options);

        $data = [];
        if ($result['code'] == 200) {
            $successOrders = $result['data']['success_orders'];
            foreach ($successOrders as $item) {
                $data[] = [
                    'code' => 200,
                    'msg'  => '支付成功',
                    'order_id' => $item
                ];
            }
            if ($needVerify) {
                if ($mergePayFlag === true) {
                    $verifyRes = $paymentObj->handleCombineOrderBuyVerifyByRpc($successOrders, $payTerminal, $payTrackOpId, $checkSource);
                    if ($verifyRes){
                        $verifyData = $verifyRes[0];  //默认就取第0个，因为手持机那边只能读取一个订单
                        $data  = $verifyData;
                    }
                } else {
                    $orderInfo = $this->_getOrder($outTradeNo);
                    $query = new \Business\Order\Query();
                    $paymode = 27;
                    $data   = $query->getOrderInfoForPrintByRpc($outTradeNo, $orderInfo, $totalFee, $paymode, $terminal,$checkSource,$payTrackOpId);
                }
            }
        }

        //记录日志
        PayBase::setPayDailyRecord(6,'扫码支付成功',[$outTradeNo],$result);

        parent::apiReturn($result['code'], $data, $result['msg'], true);




//        // 合并付款流程处理
//        if ($mergePayFlag===true) {
//            $result = $paymentObj->handlerCombinePayOrders($outTradeNo, $transactionId, OnlineTrade::CHANNEL_CHINAUMS_POS, $isMember, $jsonSell, $jsonBuy, $payToPft, $payChannel, $payTerminal, $payTrackOpId, $annualCardOrderType);
//            if ($result['code']!=200) {
//                $result = ['code'=>parent::CODE_INVALID_REQUEST, 'data'=>[],'msg'=>'合并付款失败'];
//            } else {
//                $msg    = '合并付款支付成功';
//                if ($needVerify) {
//                    // 合并付款模式不支持买即验
//                    $msg .= ",当前不支持买即验，请手工验证";
//                }
//                //更新团队订单的支付状态
//                $teamOrderModel = new TeamOrderSearch();
//                $teamOrderInfo  = $teamOrderModel->getMainOrderInfoByCombinePayId(strval($outTradeNo));
//                if (!empty($teamOrderInfo)) {
//                    $teamOrderModel->setTeamOrderByOrder($teamOrderInfo['ordernum'], ['pay_status' => 1]);
//                }
//                $result = ['code'=>parent::CODE_SUCCESS, 'data'=>[],'msg'=>$msg];
//            }
//        }
//        else {
//            $result = $this->_changeOrderPay($outTradeNo, $transactionId,$totalFee, $isMember, $jsonSell, $jsonBuy,$payToPft, $payChannel,$payTerminal,$needVerify,$terminal, $payTrackOpId, $annualCardOrderType);
//        }
//        //记录日志
//        $logData = json_encode([
//            'key' => '银联商务支付平台响应',
//            'ordernum' => $outTradeNo,
//            'res' => $result,
//        ], JSON_UNESCAPED_UNICODE);
//        pft_log('chinaums_micropay', $logData, 3);
//
//        parent::apiReturn($result['code'], $result['data'], $result['msg'], true);
    }
    public function Query(){

    }
    public function Refund(){

    }
    public function rechargeNotify(){

    }
    public function payResultCheck(){

    }
    /**
     * 获取订单信息
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @param  string $ordernum
     * @return array
     */
    private function _getOrder($ordernum)
    {
        $this->orderModel = new OrderTools('localhost');
        $orderInfo        = $this->orderModel->getOrderInfo($ordernum, 'member,ordername,ordertel,aid,code,lid,tid,tnum,tprice,ordermode,paymode,salerid,ss.pay_status,ss.status', 'de.aids,de.series');
        if ($orderInfo['aids']) {
            // 转分销获取第二级分销商
            $tmp                 = explode(',', $orderInfo['aids']);
            $orderInfo['member'] = $tmp[1];
        }
        $this->orderInfo = $orderInfo;
        return $orderInfo;
    }
    private function _changeOrderPay($outTradeNo, $transaction_id,$total_fee, $is_member, $json_sell, $json_buy, $pay_to_pft, $pay_channel,$payTerminal,$needVerify, $terminal, $payTrackOpId = 0, $annualCardOrderType = '') {
        try {
            $res      = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_CHINAUMS_POS,
                $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $pay_channel, '', $payTerminal, $payTrackOpId, $annualCardOrderType);
        } catch (\SoapFault $e) {
            $retry = true;
            pft_log('order_pay/error', "(银联商务)订单支付失败:{$outTradeNo}，soap抛出异常:{$e->getCode()}->{$e->getMessage()}");
            Helpers::sendDingTalkGroupRobotMessage("(银联商务)[{$outTradeNo}]订单支付失败;soap抛出异常:{$e->getCode()}->{$e->getMessage()}", "支付失败",  Helpers::getServerIp(), DingTalkRobots::MYSQL_ERROR);
        }
        if (isset($retry) && $retry === true) {
            $res      = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_CHINAUMS_POS,
                $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $pay_channel, '', $payTerminal, $payTrackOpId, $annualCardOrderType);
        }
        $output = [];
        if ($res === 100) {
            $orderInfo = $this->_getOrder($outTradeNo);
            $args = [
                'ordernum' => $outTradeNo,
                'buyerId'  => $orderInfo['member'], //购买人的ID
                'mobile'   => $orderInfo['ordertel'],
                'aid'      => $orderInfo['aid'],
            ];
            $jobId = \Library\Resque\Queue::push('notify', 'OrderNotify_Job', $args);
            if ($needVerify) {
                if (empty($this->orderInfo)) {
                }
                $query   = new Query();
                $paymode = 5;
                $output  = $query->getOrderInfoForPrint($outTradeNo, $this->orderInfo, $total_fee, $paymode, $terminal, 0);
            }

            if ($orderInfo['ordermode'] == 24) {
                $teamOrderModel = new TeamOrderSearch();
                $mainOrderInfo  = $teamOrderModel->getMainOrderInfoBySonOrder(strval($outTradeNo));
                if (empty($mainOrderInfo)) {
                    parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功, 未找到团单号');
                }
                $teamOrder = $mainOrderInfo['main_ordernum'];
                $sonOrder  = $teamOrderModel->getSonOrderInfoByMainOrderNum($teamOrder);
                $orderArr  = array_column($sonOrder, 'son_ordernum');
                $orderTool = new OrderTools();
                $orderData = $orderTool->getOrderInfo($orderArr, 'status,pay_status');

                $needUpdatePay = true;
                if ($orderData) {
                    foreach ($orderData as $value) {
                        if ($value['pay_status'] != 1 && $value['status'] != 3) {
                            $needUpdatePay = false;
                        }
                    }
                }

                if ($needUpdatePay) {
                    $payStatus = 1;
                } else {
                    $payStatus = 2;
                }

                if ($mainOrderInfo['status'] != 1 || $mainOrderInfo['pay_status'] != $payStatus) {
                    $res = $teamOrderModel->setTeamOrderByOrder($teamOrder, ['updatetime' => time(), 'status' => 1, 'pay_status' => $payStatus]);
                }
            }

            $msg    = '支付成功';
            $code   = parent::CODE_SUCCESS;
        } else {
            $msg    = "银联商务支付成功但订单状态更新失败,订单号:{$outTradeNo}";
            $code   = parent::CODE_INVALID_REQUEST;
        }
        return ['code'=>$code,'data'=>$output,'msg'=>$msg,'pay_res'=>$res];
    }
    /**
     * 非平台订单支付
     * <AUTHOR>
     * @date   2019-07-28
     */
    public function specialOrderPay(){
        parent::apiReturn(204, [], '该收款渠道暂不支持此收款方式，请联系管里员');
        exit;
    }
}
