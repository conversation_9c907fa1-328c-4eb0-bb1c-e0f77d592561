<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: Administrator
 * Date: 2021/5/29
 * Time: 10:40
 */

namespace Controller\pay;

use Business\Pay\PayCache;
use Library\Cache\Cache;
use Library\Controller;
use Business\Order\MergeOrder;
use Model\Member\Member;
use Process\Order\Validate\MergeOrderPreCheck\OrderPreCheckException;
use Model\Order\OrderQuery;
use Model\TradeRecord\OnlineTrade;
use Model\Order\OrderTools;

class JXNSH extends Controller
{
    //todo:定义支付渠道
    const TRACK_SOURCE = 55;
    const SOURCE_T     = 25;
    const ORDER_NOTIFY_URL        = PAY_DOMAIN . 'r/pay_JXNSH/orderNotify';
    const MICRO_NOTIFY_URL        = PAY_DOMAIN . 'r/pay_JXNSH/microNotify';


    private $reqId;
    private $merchantId;//供应商id
    private $appid;     //请求支付接口使用供应商id

    private $payType;

    public function __construct($merchantId = 0, $appid = 0, $pay_type = 0)
    {
        $this->reqId      = $_SERVER['REQUEST_ID']?:uniqid();
        $this->merchantId = $merchantId;
        $this->appid      = $appid;
        $this->payType    = $pay_type;
    }


    /**
     * 扫码支付
     * User: lanwanhui
     * Date: 2021/6/3
     */
    public function order()
    {

        $logData = [
            'post'  => $_POST,
            'input'  => file_get_contents("php://input"),
            'refer' => $_SERVER['HTTP_REFERER'],
            'agent' => $_SERVER['HTTP_USER_AGENT'],
            'reqid' => $this->reqId,
        ];
        pft_log('jxnsh/order_req', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

        $payType = I('post.pay_type', 2, 'intval');
        //平台订单号
        $outTradeNo = I('out_trade_no', 0, 'string');
        //是否二维码支付
        $qrPay = I('is_qr', 0, 'intval');
        //微信用户openid
        $openid = I('openid', null);

        // 公众号对应的实际openid——独立收款商户
        $subAppid   = I('post.sub_appid', null);
        $subOpendid = I('post.sub_openid', null);
        if (!is_null($subAppid) && $subOpendid) {
            $openid = $subOpendid;
        }

        //订单主体说明
        $subject = mb_substr(trim(I('subject')), 0, 20, 'utf-8') . "[{$outTradeNo}]";
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }

        $mergeOrder = new MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
            try {
                $mergeOrder->handlerCombinePayLog($outTradeNo, '', self::SOURCE_T, $this->merchantId);
            } catch (OrderPreCheckException $e) {
                parent::apiReturn(401, [], $e->getMessage());
            }
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string)$outTradeNo);
        }

        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {

            //模拟支付
            if ($qrPay) {
                $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => ''];
            } else {
                $data = json_encode([]);
            }

            //生成支付记录
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog(
                $outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_ORDER, '', $this->merchantId
            );

            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }

            //模拟支付成功
            $transactionId = 'online_' . time();
            $is_member     = null;
            $json_sell     = json_encode(['appid' => 'seller.12301.cc']);
            $json_buy      = json_encode(['openid' => 'buyer.12301.cc']);
            $pay_to_pft    = false;
            $payTerminal   = '';

            $options = [
                'buyer_info'   => $json_buy,
                'sell_info'    => $json_sell,
                'pay_channel'  => self::TRACK_SOURCE,
                'pay_termianl' => $payTerminal,
            ];

            $res = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_JXNSH,
                $totalFee, (int)$pay_to_pft, $options);

            echo json_encode(['code' => $res['code'], 'data' => $res['data'], 'status' => 'ok', 'msg' => $res['msg']]);
            exit;

        } else {

            //真实支付
            if (!$qrPay && !$openid) {
                parent::apiReturn(401, [], '请用微信APP打开进行支付');
            }

            $money      = number_format($totalFee / 100, 2, '.', '');
            $goodsName  = "支付订单[{$outTradeNo}]";

            $payMethod  = OnlineTrade::PAY_METHOD_ORDER;
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog(
                $outTradeNo, $money, $subject, $subject, self::SOURCE_T,
                $payMethod, '', $this->merchantId
            );
            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }

            $requestPayType = $payType == 1 ? 0 : 1;

            $params = [
                'mid'        => $this->appid,
                'orderId'    => $outTradeNo,
                'amount'     => $totalFee,
                'notifyUrl'  => self::ORDER_NOTIFY_URL,
                'payType'    => $requestPayType,
                'orderName'  => $goodsName,
            ];

            //扫码支付
            if ($qrPay) {

                $RpcRes = $this->getYarClient()->call('Pay/JxnshPay/qrPay',$params);

                $logData = [
                    'RpcRes' => $RpcRes,
                    'reqid' => $this->reqId,
                ];
                pft_log('jxnsh/order_req', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

                if ($RpcRes['code'] != 200) {
                    parent::apiReturn(401, [], '内部调用发生错误');
                }

                if ($RpcRes['res']['code'] != 200) {
                    parent::apiReturn(401, [], '请求接口失败:' . $RpcRes['res']['msg']);
                }

                $data = [
                    'qrUrl'      => $RpcRes['res']['data']['trade_qrcode'],
                    'outTradeNo' => $outTradeNo
                ];

                $this->setCacheInfo($outTradeNo,$this->appid,$totalFee);

                echo json_encode(['code' => 200, 'data' => $data, 'status' => 'ok', 'msg' => 'success']);
                exit;

            //jsapi支付,代码先写着，还没调试好
            } else {

                $params['subAppId']=$subAppid;
                $params['subOpenId'] =$openid;

                $RpcRes = $this->getYarClient()->call('Pay/JxnshPay/jsApiPay',$params);
                $logData = [
                    'RpcRes' => $RpcRes,
                    'reqid' => $this->reqId,
                ];
                pft_log('jxnsh/order_req', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

                if ($RpcRes['code'] != 200) {
                    parent::apiReturn(401, [], '内部调用发生错误');
                }

                if ($RpcRes['res']['code'] != 200) {
                    parent::apiReturn(401, [], '请求建行接口失败:' . $RpcRes['res']['msg']);
                }

                $this->setCacheInfo($outTradeNo,$this->appid,$totalFee);

                echo json_encode(['code' => 200, 'data' => $RpcRes['res']['data'], 'status' => 'ok', 'msg' => 'success']);
                exit;
            }

        }
    }


    /**
     * 商家主扫
     * User: lanwanhui
     * Date: 2021/6/3
     */
    public function micropay()
    {
        $logData = [
            'post'  => $_POST,
            'refer' => $_SERVER['HTTP_REFERER'],
            'agent' => $_SERVER['HTTP_USER_AGENT'],
            'reqid' => $this->reqId,
        ];
        pft_log('jxnsh/micropay_req', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

        $outTradeNo   = I('post.ordernum');
        $authCode     = I('post.auth_code');
        $totalFee     = I('post.money', 0, 'float2int');
        $subject      = I('post.subject', '订单支付');
        $payScen      = I('post.pay_scen', 1);
        $payTerminal  = I('post.terminal', 0, 'intval'); //支付的终端
        $needVerify   = I('post.verify', 0);//是否支付后立即验证标识
        $terminal     = I('post.terminal', 0, 'strval');//支付时所使用的终端号
        $mergePayFlag = false;// 合并付款标识
        $payTrackOpId = I('post.pay_track_op_id', 0, 'intval');//支付的操作人
        $payType      = empty($this->payType) ? I('post.pay_type', 2, 'intval') : $this->payType;
        $checkSource     = I('post.check_source', -1, 'intval');//验证来源

        if (!$outTradeNo || !$authCode) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }

        $codeLength = strlen($authCode);
        if (!in_array($codeLength,[18,19]) || !is_numeric($authCode)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '非法的支付条码，请重新刷新支付码', true);
        }

        $paymentObj = new \Business\Order\MergeOrder();
        if ($paymentObj->isCombineOrder($outTradeNo)) {
            $mergePayFlag = true;
            $paymentObj->handlerCombinePayLog($outTradeNo, $subject, self::SOURCE_T, $this->merchantId);
        }

        switch ($payScen) {
            case 1:
                //订单支付
                if (is_numeric($outTradeNo) || $mergePayFlag === true) {
                    $orderQuery = new OrderQuery('localhost');
                    $totalFee   = $orderQuery->get_order_total_fee($outTradeNo);
                }
                break;
            case 2:
                $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '暂无次支付方式');
                break;
        }

        $OnlineTrade = new OnlineTrade();
        $result      = $OnlineTrade->addLog(
            $outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_ORDER, '', $this->merchantId
        );

        if (!$result) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }
        $opName = $payTrackOpId;
        if ($payTrackOpId > 0) {
            $memberModel = new Member('slave');
            try {
                $memberInfo  = $memberModel->getMemberInfo($payTrackOpId, 'id', 'dname');
                $opName      = $memberInfo['dname'];
            } catch (\Exception $e) {

            }
            unset($memberModel, $memberInfo);
        }

        $notifyCacheInfo = [
            'totalFee'     => $totalFee,
            'payTerminal'  => $payTerminal,
            'payTrackOpId' => $payTrackOpId,
            'checkSource'  => $checkSource,
            'needVerify'   => $needVerify,
            'mid'          => $this->appid
        ];

        //调用条码支付接口
        $transactionId = $this->_microapy($authCode, $outTradeNo, $totalFee, $payType, $subject, $opName, $notifyCacheInfo);

        $payToPft = false;
        $jsonSell = $jsonBuy = '';

        $options = [
            'buyer_info'   => $jsonBuy,
            'sell_info'    => $jsonSell,
            'pay_channel'  => self::TRACK_SOURCE,
            'pay_termianl' => $payTerminal,
            'oper'         => $payTrackOpId,
            'pay_source'   => $checkSource,
        ];

        $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_JXNSH,
            $totalFee, (int)$payToPft, $options);

        $data = [];
        if ($result['code'] == 200) {
            $successOrders = $result['data']['success_orders'];
            foreach ($successOrders as $item) {
                $data[] = [
                    'code'     => 200,
                    'msg'      => '支付成功',
                    'order_id' => $item,
                ];
            }
            if ($needVerify) {
                if ($mergePayFlag === true) {
                    $verifyRes = $paymentObj->handleCombineOrderBuyVerifyByRpc($successOrders, $payTerminal, $payTrackOpId, $checkSource);
                    if ($verifyRes){
                        $verifyData = $verifyRes[0];  //默认就取第0个，因为手持机那边只能读取一个订单
                        $data  = $verifyData;
                    }
                } else {
                    $orderInfo = $this->_getOrder($outTradeNo);
                    $query     = new \Business\Order\Query();
                    $paymode   = 39;
                    $data      = $query->getOrderInfoForPrintByRpc($outTradeNo, $orderInfo, $totalFee, $paymode, $terminal,$checkSource,$payTrackOpId);

                }
            }
        }

        parent::apiReturn($result['code'], $data, $result['msg'], true);

    }


    /***
     * 商家主扫
     * User: lanwanhui
     * Date: 2021/6/4
     *
     * @param string $authCode   付款码
     * @param string $outTradeNo 订单号
     * @param int    $totalFee   订单金额
     * @param string $payType    支付方式
     * @param string $subject    订单说明
     * @param string $remark    订单备注
     *
     * @return mixed|string
     */
    private function _microapy($authCode, $outTradeNo, $totalFee, $payType, $subject, $remark='被扫支付', $notifyCacheInfo)
    {
        $client = new \Library\Tools\YarClient('pay');

        $requestPayType = $payType == 1 ? 0 : 1;

        $params = [
            'mid'         => $this->appid,
            'orderId'     => $outTradeNo,
            'amount'      => $totalFee,
            'notifyUrl'   => self::MICRO_NOTIFY_URL,
            'payType'     => $requestPayType,
            'orderName'   => $subject,
            'authCode'    => $authCode,
            'remark'      => $remark,
        ];

        //支付
        $payRes = $client->call('Pay/JxnshPay/micropay', $params);

        $logData = [
            'payRes' => $payRes,
            'reqid'  => $this->reqId,
        ];
        pft_log('jxnsh/micropay_req', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));


        if ($payRes['code'] != 200 || $payRes['res']['code'] != 200) {
            $msg = empty($payRes['res']['msg']) ? '接口错误,请联系客服人员' : $payRes['res']['msg'];
            $this->setMicroNotifyCache($outTradeNo, $notifyCacheInfo);
            parent::apiReturn(401, [], $payRes['res']['msg']);
        }

        //成功
        if ($payRes['res']['data']['status'] === '1') {

            $transactionId = $payRes['res']['data']['trade_no'];

        //等待用户输入密码的过程
        } elseif ($payRes['res']['data']['status'] === '9') {

            $times = 0;
            $flag  = false;

            do {

                //查询
                $res = $client->call('Pay/JxnshPay/query', [$this->appid, $outTradeNo]);

                $logData = [
                    'queryRes' => $payRes,
                    'reqid'    => $this->reqId,
                ];
                pft_log('jxnsh/micropay_req', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

                if ($res['res']['data']['status'] === '1') {
                    $flag = true;
                    break;
                }

                $times += 1;
                sleep(1);

            } while ($times < 300);

            if ($flag === false) {
                $this->setMicroNotifyCache($outTradeNo, $notifyCacheInfo);
                parent::apiReturn(401, [], '支付超时');
            }

            $transactionId = $res['res']['data']['trade_no'];

        }  elseif ($payRes['res']['data']['status'] === '2') {

            parent::apiReturn(401, [], "待支付");

        }  elseif ($payRes['res']['data']['status'] === '4') {

            parent::apiReturn(401, [], "已取消");

        } else {
            $this->setMicroNotifyCache($outTradeNo, $notifyCacheInfo);
            parent::apiReturn(401, [], "未知状态[{$payRes['res']['data']['status']}]");

        }

        return $transactionId;

    }


    /**
     * 获取订单信息
     * User: lanwanhui
     * Date: 2021/6/3
     *
     * @param $ordernum
     *
     * @return mixed
     */
    protected function _getOrder($ordernum)
    {
        $orderModel = new OrderTools('localhost');
        $orderInfo = $orderModel->getOrderInfo(
            $ordernum,
            'member,ordername,ordertel,aid,code,lid,tid,tnum,tprice,ordermode,paymode,salerid,ss.pay_status,ss.status',
            'de.aids,de.series'
        );
        if ($orderInfo['aids']) {
            // 转分销获取第二级分销商
            $tmp = explode(',', $orderInfo['aids']);
            $orderInfo['member'] = $tmp[1];
        }
        return  $orderInfo;

    }

    const CACHE_MICRO_PREFIX = 'pay:jxnsh:micnotify:';

    /**
     * 设置回调缓存的数据
     * User: lanwanhui
     * Date: 2021/9/23
     *
     * @param string $payOrderNum 订单号
     * @param array  $cacheInfo   缓存数据
     *
     * @return bool
     */
    private function setMicroNotifyCache($payOrderNum, $cacheInfo)
    {
        if (empty($cacheInfo)) {
            return false;
        }

        $cacheKey = self::CACHE_MICRO_PREFIX . $payOrderNum;

        $redis = Cache::getInstance('redis');

        $redis->hMSet($cacheKey, $cacheInfo);

        $redis->expire($cacheKey, 1800);

    }

    /**
     * 获取回调缓存的数据
     * User: lanwanhui
     * Date: 2021/9/23
     *
     * @param string $payOrderNum 订单号
     *
     */
    private function getMicroNotifyCache($payOrderNum)
    {
        $cacheKey = self::CACHE_MICRO_PREFIX . $payOrderNum;

        $redis = Cache::getInstance('redis');

        $cacheInfo = $redis->hGetAll($cacheKey);

        return $cacheInfo;

    }

    /**
     * 删除回调缓存的数据
     * User: lanwanhui
     * Date: 2021/9/23
     *
     * @param string $payOrderNum 订单号
     *
     */
    private function delMicroNotifyCache($payOrderNum)
    {
        $cacheKey = self::CACHE_MICRO_PREFIX . $payOrderNum;

        $redis = Cache::getInstance('redis');

        $redis->hdel($cacheKey, '');

    }


    /**
     * 商家扫码异步回调
     * User: lanwanhui
     * Date: 2021/6/4
     */
    public function microNotify()
    {
        $logData = [
            'req_id'   => $this->reqId,
            'params'   => $_GET,
        ];
        pft_log('jxnsh/micro_notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

        if (empty($_GET['sign']) || empty($_GET['out_no']) || empty($_GET['status']) || $_GET['status'] != 1) {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '参数错误',
            ];
            pft_log('jxnsh/micro_notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit('params error');
        }

        $sign   = $_GET['sign'];
        unset($_GET['a'], $_GET['c'], $_GET['sign']);
        $payResult = $_GET;

        $cacheInfo = $this->getMicroNotifyCache($payResult['out_no']);
        if (empty($cacheInfo)) {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '订单已支付完成或缓存失效',
            ];
            pft_log('jxnsh/micro_notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit('notify_success');
        }

        if (!$this->checkSign($cacheInfo['mid'], $sign, $payResult)) {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '签名失败',
            ];
            pft_log('jxnsh/micro_notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit('checkSign fail');
        }

        $this->delMicroNotifyCache($payResult['out_no']);

        //查询一下订单，顺便获取下上游订单号
        $client = new \Library\Tools\YarClient('pay');
        $queryParams = [
            'mid'    => $cacheInfo['mid'],
            'ord_no' => $payResult['out_no'],
        ];
        $queryRes = $client->call('Pay/JxnshPay/query',$queryParams);
        $logData = [
            'req_id'   => $this->reqId,
            'queryrs'  => $queryRes,
        ];
        pft_log('jxnsh/micro_notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        if ($queryRes['code'] != 200 || $queryRes['res']['code'] != 200 || $queryRes['res']['data']['status'] != 1) {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '查询订单未成功',
            ];
            pft_log('jxnsh/micro_notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit('notify_success');
        }
        $transactionId = !empty($queryRes['res']['data']['trade_no']) ? $queryRes['res']['data']['trade_no'] : $payResult['out_no'].'refund'.rand(1,999);

        $onlineTrade = new OnlineTrade();

        $tradeLog = $onlineTrade->getLog($payResult['out_no'],self::SOURCE_T);
        if (empty($tradeLog) || $tradeLog['status'] != 0) {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '交易记录不存在或订单已经支付',
            ];
            pft_log('jxnsh/micro_notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit('notify_success');
        }

        $options = [
            'buyer_info'   => '',
            'sell_info'    => '',
            'pay_channel'  => self::TRACK_SOURCE,
            'pay_termianl' => $cacheInfo['payTerminal'],
            'oper'         => $cacheInfo['payTrackOpId'],
            'pay_source'   => $cacheInfo['checkSource'],
        ];

        $result = \Library\Tools\Helpers::payComplete($payResult['out_no'], $transactionId, OnlineTrade::CHANNEL_JXNSH,
            $cacheInfo['totalFee'], 0, $options);

        $logData = [
            'req_id'   => $this->reqId,
            'result'   => $result,
        ];
        pft_log('jxnsh/micro_notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

        if ($result['code'] == 200) {
            $successOrders = $result['data']['success_orders'];
            if ($cacheInfo['needVerify']) {
                $paymentObj = new \Business\Order\MergeOrder();
                if ($paymentObj->isCombineOrder($payResult['out_no'])) {
                    $paymentObj->handleCombineOrderBuyVerifyByRpc($successOrders, $cacheInfo['payTerminal'], $cacheInfo['payTrackOpId'], $cacheInfo['checkSource']);
                } else {
                    $orderInfo = $this->_getOrder($payResult['out_no']);
                    $query     = new \Business\Order\Query();
                    $query->getOrderInfoForPrintByRpc($payResult['out_no'], $orderInfo, $cacheInfo['totalFee'], 39, $cacheInfo['payTerminal'], $cacheInfo['checkSource'], $cacheInfo['payTrackOpId']);

                }
            }
        }

        echo 'notify_success';

    }

    /**
     * 回调通知
     * http://pay.12301dev.com/r/pay_JXNSH/orderNotify
     * @author: Guangpeng Chen
     * @date: 2021/5/29
     */
    public function orderNotify()
    {
        $logData = [
            'req_id'   => $this->reqId,
            'params'   => $_GET,
        ];
        pft_log('jxnsh/notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

        if (empty($_GET['sign']) || empty($_GET['out_no']) || empty($_GET['status']) || $_GET['status'] != 1) {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '参数错误',
            ];
            pft_log('jxnsh/notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit('params error');
        }

        $sign = $_GET['sign'];

        unset($_GET['a'], $_GET['c'], $_GET['sign']);

        $payResult = $_GET;

        $cacheInfo = self::getCacheInfo($payResult['out_no']);
        if (empty($cacheInfo)) {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '获取缓存信息错误',
            ];
            pft_log('jxnsh/notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit;
        }

        if ($this->checkSign($cacheInfo['mid'], $sign, $payResult) === false) {
            $logData = [
                'req_id'   => $this->reqId,
                'err_msg'  => '签名失败',
            ];
            pft_log('jxnsh/notify', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            exit('checkSign fail');
        }

        if ($_GET['status'] == 1) {
            //TODO:支付成功逻辑
            $outTradeNo       = $payResult['out_no'];
            $transactionId   = 'jxnsh'.$payResult['out_no'].'_'.time(); //交易号
            $totalFee         = $cacheInfo['money']; //金额用分为单位
            $jsonBuy         = '';
            $jsonSell        = '';
            $payTerminal      = 0;
            $payToPft       = false;
            $options = [
                'buyer_info' => $jsonBuy,
                'sell_info'  => $jsonSell,
                'pay_channel' => self::TRACK_SOURCE,
                'pay_termianl' => $payTerminal
            ];
            $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, self::SOURCE_T, $totalFee, (int)$payToPft, $options);
            if ($result['code'] == 200) {
                $needVerify = PayCache::getOrderVerfify($outTradeNo);
                if ($needVerify) {
                    $successOrders = $result['data']['success_orders'];
                    $paymentObj = new \Business\Order\MergeOrder();
                    $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
                }
            }
            pft_log('jxnsh/notify', json_encode(['key'=>$outTradeNo, 'res'=>$result], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        }

        echo 'notify_success';

    }

    const CACHE_PREFIX = 'pay:jxnsh:';
    private function setCacheInfo($payOrderNum, $mid, $money)
    {
        $cacheKey = self::CACHE_PREFIX . $payOrderNum;
        /**
         * @var $redis \Redis
         */
        $redis = Cache::getInstance('redis');
        $redis->hMSet($cacheKey, ['mid'=>$mid, 'money'=>$money]);
        $redis->expire($cacheKey, 3600);
    }
    private function getCacheInfo($payOrderNum)
    {
        $cacheKey = self::CACHE_PREFIX . $payOrderNum;
        /**
         * @var $redis \Redis
         */
        $redis = Cache::getInstance('redis');
        $cacheInfo = $redis->hGetAll($cacheKey);
        return $cacheInfo;
    }

    private $client = null;
    private function getYarClient()
    {
        if (is_null($this->client)) {
            $this->client = new \Library\Tools\YarClient('pay');
        }
        return $this->client;
    }
    private function checkSign($mid, $sign, array $data)
    {
        $params = ['mid'=>$mid, 'sign'=>$sign, 'data'=> $data];
        $payRes = $this->getYarClient()->call('Pay/JxnshPay/checkSign', $params);
        if ($payRes['code'] == 200 && $payRes['res']['code'] == 200) {
            return true;
        } else {
            return false;
        }

    }


    /**
     * 订单查询
     * User: lanwanhui
     * Date: 2021/7/13
     */
    public function payResultCheck()
    {
        $ordernum = I('post.ordernum');
        //支付场景：1订单支付,2充值
        $pay_scen  = I('post.pay_scen', 2);
        $isMiniApp = I('post.mini_app');
        $data      = ['scan_channel' => 0];
        pft_log('jxnsh/pay_result_check', json_encode($_POST, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        //todo::检测支付状态
        $model = new OnlineTrade();
        if ($isMiniApp) {
            $scanLog = $model->checkQrCodeScan($ordernum);
            if ($scanLog) {
                $data['scan_channel'] = $scanLog['scan_channel'];
                $payLog               = $model->getLog($ordernum, self::SOURCE_T);
                if ($payLog['status'] == 1) {
                    parent::apiReturn(parent::CODE_SUCCESS, $data, '支付成功', true);
                } else {
                    parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '未支付', true);
                }
            } else {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '请扫描二维码', true);
            }
        } else {
            sleep(1);
            $payLog = $model->getLog($ordernum, self::SOURCE_T);
            if (!$payLog) {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
            }

            if ($payLog['status'] == 1) {
                parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
            }
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看', true);
        }
    }
}
