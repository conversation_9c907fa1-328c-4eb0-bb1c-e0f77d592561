<?php
/**
 * 微信支付
 * User: chenguangpeng
 * Date: 5/16-016
 * Time: 15:43
 *
 * 测试地址：http://www.12301.cc/p/test_alipay_recharege.php
 */

namespace Controller\pay;

use Business\Finance\AccountMoney;
use Business\Finance\PayMerchant;
use Business\JsonRpcApi\PayService\CommonHandle;
use Business\Order\MergeOrder;
use Business\Order\Query;
use Business\Pay\PayBase;
use Business\Pay\PayCache;
use Business\Pay\PayCenter;
use Business\Pay\SpecialOrderPay;
use Business\Product\UnifyCard;
use Business\RiskManagement\ShumeiRiskCheck;
use Business\Wechat\WeChatCard;
use Controller\pay\traits\HaboTrait;
use Library\Business\WechatSmallApp;
use Library\Business\WePay\WxPayApi;
use Library\Business\WePay\WxPayDataBase;
use Library\Business\WePay\WxPayException;
use Library\Business\WePay\WxPayLib;
use Library\Business\WePay\WxPayNotifyResponse;
use Library\Cache\Cache;
use Library\Constants\DingTalkRobots;
use Library\Controller;
use Library\Resque\Queue;
use Library\Tools\Helpers;
use Model\Member\Member;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\Order\SubOrderQuery\SubOrderTrack;
use Model\Order\TeamOrderSearch;
use Model\TradeRecord\OnlineTrade;
use OSS\Core\OssException;
use OSS\OssClient;
use Process\Resource\Qiniu;
use Qiniu\Auth;
use Qiniu\Storage\UploadManager;

class WxPay extends Controller implements PayInterface
{
    use HaboTrait;

    /**
     * 定义支付渠道
     */
    const SOURCE_T = 1;

    const RECHARGE_NOTIFY_URL = PAY_DOMAIN . 'r/pay_WxPay/rechargeNotify';
    const XGS_NOTIFY_URL = PAY_DOMAIN . 'r/pay_WxPay/xgsNotify';
//    const ORDER_NOTIFY_URL = PAY_DOMAIN . 'r/pay_WxPay/orderNotify';
    const ORDER_NOTIFY_URL = PAY_DOMAIN . 'order/mobile_wepay_notify.php';
    const RENEW_NOTIFY_URL = PAY_DOMAIN . 'r/pay_WxPay/renewNotify/'; //平台会员充值通知地址
    const PARK_NOTIFY_URL = PAY_DOMAIN . 'r/pay_WxPay/parkPayNotify/'; //停车场支付通知地址
    const ANNUAL_RENEW_NOTIFY_URL = PAY_DOMAIN . 'r/pay_WxPay/annualRenewNotify/'; //年卡续费通知地址
    const CARD_SOLUTION_NOTIFY_URL = PAY_DOMAIN . 'r/pay_WxPay/cardSolutionNotify/'; //郑州园区会员卡充值通知地址
    const COMMON_NOTIFY_URL   = PAY_DOMAIN . '/r/pay_WxPay/commonNotify/';//通用支付回调处理，转发数据到具体的业务逻辑上
    private $WePayConf = [];
    private $orderModel = null;
    private $merchantId = 0;

    /**
     * @var $wxPayLib WxPayLib
     */
    private $wxPayLib;

    public function __construct($config = null, $merchantId = 0)
    {

        if (empty($config) && $_GET['a'] == 'jsApiPay') {
            $merchantId     = I('post.merchant_id', 0, 'intval');
            $isApplet       = I('post.is_applet');
            $appletSubAppId = I('post.applet_sub_appid');
            if ($merchantId) {
                $merBiz = new \Business\Finance\PayMerchant();
                if (!empty($isApplet)) {
                    $config = $merBiz->getMerchantConfig(\Business\Finance\PayMerchant::WEPAY, $merchantId, $appletSubAppId, 3);
                } else {
                    $config = $merBiz->getMerchantConfig(\Business\Finance\PayMerchant::WEPAY, $merchantId, '', '1|2', false);
                }
            }
        }

        //微信小程序
        if (isset($_SERVER['HTTP_SMALL_APP'])) {
            $input = file_get_contents('php://input');
            $_POST = json_decode($input, true);
        }

        $appid = empty(I('post.appid')) ? PFT_WECHAT_APPID : I('post.appid');

        //if (ENV == 'TEST') {
        //    $appid = empty(I('post.appid')) ? PFT_WECHAT_APPID : I('post.appid');
        //} else {
        //    $appid = !empty($appid) ? $appid : PFT_WECHAT_APPID;
        //}
        $this->wxPayLib   = new WxPayLib($appid, $config);
        $this->merchantId = $merchantId;
    }

    /**
     * 订单支付
     */
    public function order()
    {
        //平台订单号
        $outTradeNo = I('post.out_trade_no', 0, 'string');
        //是否二维码支付
        $qrPay = I('post.is_qr', 0, 'intval');  //0-jsPay  1-主扫 2-h5 3-app
        //微信用户openid
        $openid = I('post.openid', null);
        // 公众号对应的实际openid——独立收款商户
        $subAppid   = I('post.sub_appid', null);
        $subOpendid = I('post.sub_openid', null);
        if (!is_null($subAppid) && $subAppid == WxPayApi::$sub_appid && $subOpendid) {
            $openid = $subOpendid;
        }
        //订单主体说明
        $subject = mb_substr(trim(I('subject')), 0, 20, 'utf-8');
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }
        PayBase::setPayDailyRecord(2,'订单支付请求',I('post.'));
        $mergeOrder = new MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
            try {
                $mergeOrder->handlerCombinePayLog($outTradeNo, '', self::SOURCE_T, $this->merchantId);
            } catch (\Exception $e) {
                parent::apiReturn(401, [], $e->getMessage());
            }
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string)$outTradeNo);
        }

        //如果是内网测试环境，直接把支付状态设置为已经支付
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            //模拟支付
            if ($qrPay) {
                $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => ''];
            } else {
                $data = json_encode([]);
            }
            $payMethod = OnlineTrade::PAY_METHOD_ORDER;
            //生成支付记录
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog($outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
                $payMethod, '', $this->merchantId);

            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }

            //模拟支付成功
            $transactionId = 'online_' . time();
            $is_member     = null;
            $json_sell     = json_encode(['appid' => 'seller.12301.cc']);
            $json_buy      = json_encode(['openid' => 'buyer.12301.cc']);
            $pay_to_pft    = $this->merchantId ? false : true;
            $pay_channel   = 29;
            $payTerminal  = I('post.terminal', 0, 'intval'); //支付的终端
            $options = [
                'buyer_info'  => $json_buy,
                'sell_info'   => $json_sell,
                'pay_channel' => 1,
                'pay_terminal'=> $payTerminal,

            ];

            $res = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, self::SOURCE_T, $totalFee, 1,
                $options);
            if ($res['code'] != 200) {
                parent::apiReturn(401, [], '模拟支付失败:' . $res['msg']);
            }

            //兼容旧的微信小程序支付，增加status字段
            echo json_encode(['code' => 204, 'data' => $data, 'status' => 'ok', 'msg' => 'success']);
            exit;
        }
        else {
            //真实支付
            if (!$qrPay && !$openid) {
                parent::apiReturn(401, [], '请用微信APP打开进行支付');
            }

            if (in_array($qrPay,[1,2])) {
                if ((new SafetyCheck())->refererCheck(true) !== true ) {
                    $this->apiReturn(204, [], '二维码支付请求异常，禁止支付:'.self::SOURCE_T);
                }
                //生成微信支付二维码
                $tmpOutTradeNo = 'qr_' . $outTradeNo;
                $result        = $this->_orderPayByQrcodeOrH5($qrPay,$totalFee, $tmpOutTradeNo, $subject);
                $data          = ['outTradeNo' => $outTradeNo, 'qrUrl' => $result['data']];
            }elseif ($qrPay == 3){
                $result        = $this->_orderPayByAppApi($totalFee,$outTradeNo,$subject);
                $data          = ['parameter' => $result['data'],'outTradeNo' => $outTradeNo];
            } else {
                //调用微信jsapi支付
                $result = $this->_orderPayByJsapi($totalFee, $outTradeNo, $subject, $openid);
                $data   = $result['data'];
            }
            if (isset($_POST['pay_from']) && $_POST['pay_from'] == 'wanju') {
                $redis = Cache::getInstance('redis');
                $redis->set("wepay_wanju:{$outTradeNo}", 1, '', 600);
            }
            if ($result['status'] == 0) {
                parent::apiReturn(401, [], $result['msg']);
            } else {
                //生成支付记录
                $payMethod  = OnlineTrade::PAY_METHOD_ORDER;
                $tradeModel = new OnlineTrade();
                $ret        = $tradeModel->addLog($outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
                    $payMethod, '', $this->merchantId);

                if (!$ret) {
                    parent::apiReturn(401, [], '支付记录生成失败');
                }
            }
            //兼容旧的微信小程序支付，增加status字段
            echo json_encode(['code' => 200, 'data' => $data, 'status' => 'ok', 'msg' => 'success']);
            exit;
        }
    }

    /**
     * 会员卡支付
     */
    public function cardSolutionRecharge()
    {
        //平台订单号
        $outTradeNo = I('out_trade_no', 0, 'string');
        //微信用户openid
        $openid = I('openid', null);

        //订单主体说明
        $subject = mb_substr(trim(I('subject')), 0, 20, 'utf-8');
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }
        if (!$openid) {
            parent::apiReturn(401, [], '请用微信APP打开进行支付');
        }

        $onlineModel = new OnlineTrade();
        $data        = $onlineModel->getLogByOrderId($outTradeNo);
        if (empty($data)) {
            parent::apiReturn(401, [], '订单号缺失');
        }

        $totalFee = $data['total_fee'];
        $totalFee = $totalFee * 100;
        //调用微信jsapi支付
        $result = $this->_orderPayByJsapi($totalFee, $outTradeNo, $subject, $openid, self::CARD_SOLUTION_NOTIFY_URL);
        $data   = $result['data'];

        if ($result['status'] == 0) {
            parent::apiReturn(401, [], $result['msg']);
        }

        //兼容旧的微信小程序支付，增加status字段
        echo json_encode(['code' => 200, 'data' => $data]);
    }

    /**
     * 郑州会员卡回调地址
     *
     * <AUTHOR>
     * @date   2018-04-22
     */
    public function cardSolutionNotify()
    {
        //请求日志
        $notify = new WxPayNotifyResponse();
        $notify->saveData('wepay/card_solution');
        $appid             = isset($notify->data['sub_appid']) ? $notify->data['sub_appid'] : $notify->data['appid'];
        $payMerchantConfig = [];

        // 法利兰支付配置在 wepay.conf.php(这边使用法利兰自己的服务,非服务商模式)  圣瑞配置在表pft_merchant_wepay(这边用的是票付通服务商模式)
        // todo 统一成服务商模式 修改法利兰配置信息, 小程序请求支付的时候需要增加传的标识     appid 和 sub_appid共同判断是否需要读取配置

        // 此处判断是否服务商模式还不完整  todo 法利兰获取微信支付配置改到 pft_merchant_wepay
        $payToPft = true;
        if (isset($notify->data['sub_appid']) && !empty($notify->data['sub_appid'])) {
            // 如果存在sub_appid, 并且存在独立配置, 从数据库中获取key
            $payMerchantBiz    = new PayMerchant();
            $payMerchantConfig = $payMerchantBiz->getMerchantConfigBySubInfo($notify->data['sub_appid'],
                $notify->data['sub_mch_id']);
            if ($notify->data['sub_appid'] != 'wxd72be21f7455640d') {
                $payToPft = false;
            }
        }
        $this->wxPayLib = new WxPayLib($appid, $payMerchantConfig);

        //支付检测
        $checkRes = $this->_notifyBaseCheck($notify);
        if (!$checkRes) {
            //支付出错直接返回
            exit($notify->ToXml());
        }

        //这边加个锁
        $Redis = Cache::getInstance('redis');
        $key   = 'card_solution:' . $notify->data['out_trade_no'];
        $lock  = $Redis->lock($key, 1, 30);

        if (!$lock) {
            exit($notify->ToXml());
        }

        //平台订单号
        $outTradeNo = $notify->data['out_trade_no'];
        $money      = $notify->data['total_fee'];
        //支付宝订单号
        $tradeNo = $notify->data['transaction_id'];

        $recharge = new \Business\CardSolution\Recharge();
        $result   = $recharge->onlineRecharge($outTradeNo, $tradeNo, $money, $payToPft);

        if ($result['code'] != 200) {
            $notify->SetReturn_code("FAIL");
            $notify->SetReturn_msg($result['msg']);
        }

        $logStr = 'orderNo:' . $outTradeNo . ', tradeNo:' . $tradeNo . json_encode($result, JSON_UNESCAPED_UNICODE);
        @pft_log('wepay/card_solution', $logStr, 'day');

        exit($notify->ToXml());
    }

    /**
     * 微信端支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $ordernum = I('post.ordernum');
        //支付场景：1订单支付,2充值
        $pay_scen  = I('post.pay_scen', 2);
        $isMiniApp = I('post.mini_app');
        $data      = ['scan_channel' => 0];
        //todo::检测支付状态
        $model = new OnlineTrade();
        if ($isMiniApp) {
            $scanLog = $model->checkQrCodeScan($ordernum);
            if ($scanLog) {
                $data['scan_channel'] = $scanLog['scan_channel'];
                $payLog               = $model->getLog($ordernum, self::SOURCE_T);
                if ($payLog['status'] == 1) {
                    parent::apiReturn(parent::CODE_SUCCESS, $data, '支付成功', true);
                } else {
                    parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '未支付', true);
                }
            } else {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, $data, '请扫描二维码', true);
            }
        } else {
            $payLog = $model->getLog($ordernum, self::SOURCE_T);
            if (!$payLog) {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [], 'WxPay支付记录不存在', true);
            }

            if ($payLog['status'] == 1) {
                parent::apiReturn(parent::CODE_SUCCESS, [
                    'trade_no'  =>$payLog['trade_no'],
                    'total_fee' =>$payLog['total_fee'] * 100,
                    'trade_time'=>$payLog['dtime'],
                ], '支付成功', true);
            }
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败', true);
        }
    }

    /**
     * 微信二维码支付
     *
     * @param  int $qrPay   二维码orH5
     * @param  string $totalFee   总金额
     * @param  string $outTradeNo 平台订单号
     * @param  string $subject    订单描述
     * @param string $notifyUrl
     * @return array
     * @throws WxPayException
     */
    public function _orderPayByQrcodeOrH5($qrPay, $totalFee, $outTradeNo, $subject, $notifyUrl = self::ORDER_NOTIFY_URL)
    {
        $tradeTypeMap = [
            1=>['type'=>'qr', 'key'=>'code_url'],
            2=>['type'=>'h5', 'key'=>'mweb_url'],
        ];
        $tradeType = $tradeTypeMap[$qrPay]['type']??'qr';
        $urlType   = $tradeTypeMap[$qrPay]['key'];
        if ($tradeType == 'qr') {
            //微信二维码支付
            $parameters = $this->wxPayLib->qrPay(
                $totalFee,
                $subject,
                $outTradeNo,
                $notifyUrl ?: self::ORDER_NOTIFY_URL,
                '微信二维码支付',
                'orderpay'
            );
        } elseif ($tradeType == 'h5') {
            $parameters = $this->wxPayLib->h5Pay(
                $totalFee,
                $subject,
                $outTradeNo,
                $notifyUrl ?: self::ORDER_NOTIFY_URL,
                '微信H5支付',
                'orderpay'
            );
            $parameters = [
                'return_code'=> 0,
                'return_msg' => "[{$outTradeNo}]支付失败，当前不支持H5收款",
            ];
        }
        PayBase::setPayDailyRecord(2,'主扫支付',func_get_args(),$parameters,['type' => $tradeType,'referer'=>$_SERVER['HTTP_REFERER'],
                                                                         'ip'=>get_client_ip(),'agent'=>$_SERVER['HTTP_USER_AGENT'],'refer'=>$_SERVER['HTTP_REFERER']]);
        if ($parameters['return_code'] == 'SUCCESS') {
            $return = [
                'status' => 1,
                'data'   => $parameters[$urlType],
            ];
        } else {
            $return = [
                'status' => 0,
                'msg'    => $parameters['return_msg'],
            ];
        }
        return $return;
    }

    /**
     * 微信jsapi支付
     *
     * @param  [type] $totalFee   总金额
     * @param  [type] $outTradeNo 平台订单号
     * @param  [type] $subject    订单描述
     * @param  [type] $openid     微信penid
     * @param  string $notifyUrl   回调通知地址
     * @param  string $attach     扩展信息
     * @param  int $orderExpire   订单过期时间,秒
     *
     * @return [type]             [description]
     */
    public function _orderPayByJsapi($totalFee, $outTradeNo, $subject, $openid, $notifyUrl = '', $attach='', $orderExpire=7200)
    {
        //微信jsapi支付
        $parameters = $this->wxPayLib->jsApiPay(
            $totalFee,
            $subject,
            $outTradeNo,
            $openid,
            $notifyUrl ?: self::ORDER_NOTIFY_URL,
            $attach,
            $orderExpire
        );
        PayBase::setPayDailyRecord(2,'jspay支付',func_get_args(),$parameters);
        if (is_array($parameters) && isset($parameters['return_code']) && $parameters['return_code'] != 'SUCCESS') {
            $return = [
                'status' => 0,
                'msg'    => $parameters['return_msg'],
            ];
        } else {
            $return = [
                'status' => 1,
                'data'   => is_array($parameters) ?: json_decode($parameters, true),
            ];
        }

        return $return;
    }


    /**
     * 微信app支付
     *
     * @param  [type] $totalFee   总金额
     * @param  [type] $outTradeNo 平台订单号
     * @param  [type] $subject    订单描述
     *
     * @return [type]             [description]
     */
    public function _orderPayByAppApi($totalFee, $outTradeNo, $subject, $notifyUrl = '')
    {
        //微信jsapi支付
        $parameters = $this->wxPayLib->appApiPay($totalFee, $subject, $outTradeNo,
            $notifyUrl ?: self::ORDER_NOTIFY_URL, '', '', 1);
        PayBase::setPayDailyRecord(2,'app支付',func_get_args(),$parameters);
        if ($parameters['code'] != 200) {
            $return = [
                'status' => 0,
                'msg'    => $parameters['msg'],
                'data'   => [],
            ];
        } else {
            $return = [
                'status' => 1,
                'data'   => $parameters['data'],
                'msg'    => 'success'
            ];
        }

        return $return;
    }


    /**
     * 纯粹的刷卡支付接口
     */
    public function cardPay()
    {
        $outTradeNo = I('post.ordernum');
        $auth_code  = I('post.auth_code');
        $total_fee  = I('post.money') * 100;
        $subject    = I('post.subject', '订单支付');
        $pay_scen   = I('post.pay_scen', 1);

        if (!$outTradeNo || !$auth_code) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }

        pft_log('/micropay/wepay', json_encode($_POST));

        if ($total_fee == 0) {
            $output         = ['ordernum' => $outTradeNo, 'trade_no' => ''];
            parent::apiReturn(200, $output, "0元不需要支付", true);
        }

        $OnlineTrade = new OnlineTrade();
        try {
            $payResult  = $this->wxPayLib->micropay($auth_code, $subject, $total_fee, $outTradeNo);
        } catch (WxPayException $e) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "订单号[{$outTradeNo}],商户号:[".WxPayApi::$sub_mchid.'],错误信息:'. $e->getMessage(), true);
        }

        if ($payResult == false) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "支付失败,订单号:{$outTradeNo},请求微信支付失败，请检查账户余额是否充足", true);
        }

        $buyerInfo      = array(
            'openid'     => $payResult['openid'],
            'sub_openid' => $payResult['sub_openid'],
        );
        $sellerInfo     = array(
            'appid'     => $payResult['appid'],
            'sub_appid' => $payResult['sub_appid'],
        );
        $transaction_id = $payResult['transaction_id'];
        $json_buy       = json_encode($buyerInfo);
        $json_sell      = json_encode($sellerInfo);
        $payToPft     = $this->checkPayToPft($payResult['mch_id'],$payResult['sub_mch_id'], $payResult['sub_appid'], $payResult['appid']);//$pay_result['sub_appid'] == PFT_WECHAT_APPID ? true : false;
        $pay_channel    = 11;
        $res            = $OnlineTrade->updateLog($outTradeNo, $transaction_id, OnlineTrade::CHANNEL_WEPAY, $json_sell,
            $json_buy, 1, '', $payToPft);
        $output         = ['ordernum' => $outTradeNo, 'trade_no' => $transaction_id,'pay_to_pft' => $payToPft];
        pft_log('micropay/wepay_result', $outTradeNo . ':支付结果响应:' . json_encode($payResult) . ';修改支付状态返回:' . $res);
        parent::apiReturn(200, $output, "微信支付成功", true);
    }

    /**
     * 检查是否支付到票付通的微信支付账号
     * @author: Guangpeng Chen
     * @date: 2019/12/25
     *
     * @param string $mchId    主商户id
     * @param string $subMchId 子商户ID
     * @param string $subAppid 子商户APPID
     * @param string $appId    主商户APPID
     *
     * @return bool
     */
    private function checkPayToPft($mchId, $subMchId, $subAppid, $appId)
    {
        $payToPft = true;
        if (in_array($mchId,PFT_WEPAY_MCHID) || in_array($subMchId,PFT_WEPAY_MCHID)) {
            $payToPft     = true;
        } elseif ($subAppid && $subAppid != PFT_WECHAT_APPID) {
            $payToPft = false;
        } elseif ($appId != PFT_WECHAT_APPID) { // 普通模式
            $payToPft     = false;
        }
        return $payToPft;
    }

    public $jsonRpcClient=null;

    public function micropay()
    {
        $outTradeNo = I('post.ordernum');
        $authCode   = I('post.auth_code');
        $totalFee   = I('post.money', 0);
        $totalFee   = float2int($totalFee);
        $isMember   = I('post.is_member', 0);
        $subject    = I('post.subject', '订单支付');
        // 1订单支付,2团单支付
        $payScen      = I('post.pay_scen', 1);
        $payTerminal  = I('post.terminal', 0, 'intval'); //支付的终端
        $needVerify   = I('post.verify', 0);//是否支付后立即验证标识
        $terminal     = I('post.terminal', 0, 'strval');//支付时所使用的终端号
        $checkSource  = I('post.check_source', -1, 'intval');//验证来源
        $mergePayFlag = false;// 合并付款标识

        //支付来源 - 具体可以参照business.conf.php -> track_source
        // 0=黑色终端机 2=自助机 4=云票务 5=云闸机 20=安卓智能终端机
        $paySource = I('post.pay_source', 20, 'intval');
        //支付的操作人 可以不传 用在记录追踪表里
        $payTrackOpId = I('post.pay_track_op_id', 0);
        $subSid       = I('post.sub_sid', 0, 'intval'); //子商户支付的时候，这个是子商户的供应商id
        //年卡下单类型  默认值为空字符串, 0年卡下单 1年卡续费
        $annualCardOrderType = I('post.annualCardOrderType', false);
        //业务类型
        $payBiz = I('post.pay_biz',0,'intval'); //0-无 1-计时卡业务
        if (!$outTradeNo || !$authCode) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }
        //扫码支付授权码，设备读取用户微信中的条码或者二维码信息
        //（注：用户刷卡条形码规则：18位纯数字，以10、11、12、13、14、15开头）
        $codeLength = strlen($authCode);
        if ($codeLength != 18 || !is_numeric($authCode)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '非法的支付条码，请重新刷新微信支付码', true);
        }
        //记录日志
        PayBase::setPayDailyRecord(2,'扫码请求',I('post.'));
        $paymentObj = new \Business\Order\MergeOrder();
        if ($paymentObj->isCombineOrder($outTradeNo)) {
            $mergePayFlag = true;
            $paymentObj->handlerCombinePayLog($outTradeNo, $subject, self::SOURCE_T, $this->merchantId,$payBiz);
        }



        switch ($payScen) {
            case 1:
                //订单支付
                if (is_numeric($outTradeNo) || $mergePayFlag === true) {
                    $orderQuery = new OrderQuery('localhost');
                    $totalFee   = $orderQuery->get_order_total_fee($outTradeNo);
                }
                break;
            case 2:
                $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '暂无此支付模式');
                break;
        }

        $timeOrderDeposit = 0;
        $payBaseBiz = new PayBase();
        if ($payBiz){
            $payOrderOtherRes = $payBaseBiz->orderPayOtherBizHandle($outTradeNo,$payBiz);
            if ($payOrderOtherRes['code'] == 200){
                $timeOrderDeposit = $payOrderOtherRes['data']['timeOrderDeposit'];
                if ($timeOrderDeposit > 0){
                    $subject .= '/订单押金';
                }
            }
            $totalFee += $timeOrderDeposit;
        }


        if ($totalFee <= 0 ) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "订单号[{$outTradeNo}]待支付金额为0元，无需支付" , true);
        }
        $OnlineTrade = new OnlineTrade();
        $result      = $OnlineTrade->addLog($outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_ORDER, '', $this->merchantId);
        if (!$result) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }

        //如果是内网测试环境，直接把支付状态设置为已经支付
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            //模拟支付成功
            $payResult = [
                'transaction_id' => 'online_' . time(),
                'openid'         => 'buyer.12301.cc',
                'sub_openid'     => 'buyer.12301.cc',
                'appid'          => 'seller.12301.cc',
                'sub_appid'      => 'seller.12301.cc',
            ];
        } else {
            //真实支付
            try {
                $payResult = $this->wxPayLib->micropay($authCode, $subject, $totalFee, $outTradeNo);
            } catch (WxPayException $e) {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [],
                    "订单号[{$outTradeNo}]请求微信支付接口失败:" . $e->getMessage(), true);
            }
        }

        //记录日志
        $logData = json_encode([
            'key'      => '微信扫码支付结果',
            'ordernum' => $outTradeNo,
            'res'      => $payResult,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('wepay_micropay', $logData, 3);
        if ($payResult == false) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "支付失败,订单号:{$outTradeNo},请求微信支付失败", true);
        }
        $buyerInfo     = [
            'openid'     => $payResult['openid'],
            'sub_openid' => $payResult['sub_openid'],
        ];
        $sellerInfo    = [
            'appid'     => $payResult['appid'],
            'sub_appid' => $payResult['sub_appid'],
        ];
        $transactionId = $payResult['transaction_id'];
        $jsonBuy       = json_encode($buyerInfo);
        $jsonSell      = json_encode($sellerInfo);
        $payToPft      = true;
        if (isset($payResult['sub_mch_id'])) {
            $payToPft = in_array($payResult['sub_mch_id'], PFT_WEPAY_MCHID) ? true : false;
        } else {
            $payToPft = in_array($payResult['mch_id'], PFT_WEPAY_MCHID) ? true : false;
        }
        if ($timeOrderDeposit > 0){
            $totalFee -= $timeOrderDeposit;
        }
        $payChannel = 11;

        $options = [
            'buyer_info'   => $jsonBuy,
            'sell_info'    => $jsonSell,
            'pay_channel'  => $payChannel,
            'pay_termianl' => $payTerminal,
            'oper'         => empty($subSid) ? $payTrackOpId : $subSid,
            'pay_source'   => $checkSource,
            'subOpId'      => $subSid,
        ];

        $res = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_WEPAY, $totalFee,
            (int)$payToPft, $options);
        if (!is_null($this->jsonRpcClient)) {
            $this->jsonRpcClient->call('Order/PaymentLock/unlock', [$outTradeNo], 'scenic');
        }

        if ($res['code'] == 200) {
            $data          = [];
            $successOrders = $res['data']['success_orders'];
            foreach ($successOrders as $item) {
                $data[] = [
                    'code'     => 200,
                    'msg'      => '支付成功',
                    'order_id' => $item,
                ];
            }
            // 发送微信卡券
            // if ($payToPft) {
            //     WeChatCard::sendCard($payResult['sub_openid'], $successOrders);
            // }
            //立即验证
            if ($needVerify) {
                if ($mergePayFlag) {
                    $verifyRes = $paymentObj->handleCombineOrderBuyVerifyByRpc($successOrders, $payTerminal, $payTrackOpId, $checkSource, true, $subSid);
                    if ($verifyRes){
                        $verifyData = $verifyRes[0];  //默认就取第0个，因为手持机那边只能读取一个订单
                        $data  = $verifyData;
                    }
                } else {
                    $orderInfo = $this->_getOrder($outTradeNo);
                    $query     = new \Business\Order\Query();
                    $data      = $query->getOrderInfoForPrintByRpc($outTradeNo, $orderInfo, $totalFee, 5, $terminal,$checkSource, $payTrackOpId, $subSid);
                }
            }
            $payBaseBiz->afterOrderPayAction($payBiz,OnlineTrade::CHANNEL_WEPAY,$transactionId, (int)$payToPft,$successOrders);
            $result = ['code' => parent::CODE_SUCCESS, 'data' => $data, 'msg' => '支付成功'];
        } else {
            $result = ['code' => parent::CODE_INVALID_REQUEST, 'data' => [], 'msg' => '付款失败'];
        }

        //记录日志
        PayBase::setPayDailyRecord(2,'扫码成功结果',[$outTradeNo],$result);

        parent::apiReturn($result['code'], $result['data'], $result['msg'], true);
    }

    /**
     * 平台充值
     * <AUTHOR> Chen
     * @date 2016-10-03
     *
     * @description 充值-post
     * @money:充值的金额，单位“元”
     * @openid:微信openid
     * @aid:供应商ID，可以为空；大于0表示授信预存
     * @did:充值的人的id
     * @appid:微信收款公众号
     * @is_qr:是否扫码支付
     * @memo:备注
     */
    public function recharge()
    {
        $money     = I('post.money');
        $money     = floatval(number_format($money, 2, '.', ''));
        $total_fee = $money * 100;
        $openid    = I('post.openid');
        $aid       = I('post.aid');
        $did       = I('post.did', 0);
        $is_qr     = I('post.qr_pay', 0); // 0-微信公众号 1- 扫码支付 2-app支付
        if (isset($_POST['is_qr'])) {
            $is_qr    = I('post.is_qr', 0);
        }
        $memo      = I('post.memo', '', 'trim');
        $shopId    = I('post.shop_id', '', 'intval');
        $account   = I('post.account', '');
        $captchaCode   = I('post.captchaCode', '');
        $requestFrom   = I('post.requestFrom', '');
        $ip            = I('post.ip', '');

        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $sid       = $loginInfo['sid'] ?? 0;

        $did = $did ? $did : $sid;
        if (!$did) {
            exit('{"status":"fail","msg":"用户身份获取错误"}');
        }
        if ($is_qr == 0 && empty($openid)) {
            exit('{"status":"fail","msg":"OPENID为空"}');
        }
        if (!is_numeric($money) || $money < 0) {
            exit('{"status":"fail","msg":"请输入大于0的金额，金额必须是数字"}');
        }
        if ($openid != '') {
            $payServiceApi = new CommonHandle();
            $redirectRes = $payServiceApi->rechargeCheck($did, 2, $openid);
            if ($redirectRes['code'] != 200) {
                parent::apiReturn(401, [], '检测到您的账号存在风险，请确认是否本人操作，有问题请联系客服');
            }
        }

        $modelMember    = new Member();
        $limitCreditPay = true;
        if ($did == 1) {
            $body = '补打款';
        } else {

            $queryParams = [[$did, $shopId, $aid]];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                $queryParams);

            if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                exit('{"status":"fail","msg":"用户信息获取失败，请稍后重试"}');
            }

            $memberInfo = array_column($queryRes['data'], null, 'id');

            //$seller_nama = str_replace('|', '', $modelMember->getMemberCacheById($did, 'dname'));
            $seller_nama = str_replace('|', '', $memberInfo[$did]['dname']);
            if ($aid > 0) {
                // 三亚先行允许使用信用卡支付
                if ($aid == 55) {
                    $limitCreditPay = false;
                }

                //$boss_name = str_replace('|', '', $modelMember->getMemberCacheById($aid, 'dname'));
                $boss_name = str_replace('|', '', $memberInfo[$aid]['dname']);
                if ($shopId) {
                    //$through_name = str_replace('|', '', $modelMember->getMemberCacheById($shopId, 'dname'));
                    $through_name = str_replace('|', '', $memberInfo[$shopId]['dname']);
                    $body         = "[$seller_nama]通过{$through_name}给{$boss_name}充值{$money}元|{$did}|$aid|$shopId";
                } else {
                    $body = "[$seller_nama]给{$boss_name}充值{$money}元|{$did}|$aid";
                }
            } else {
                $body = "[{$seller_nama}]账户充值{$money}元|{$did}";
            }
        }

        if ($memo) {
            $body .= '|' . $memo;
        }

        //支付订单号
        $out_trade_no = time() . $did . mt_rand(1000, 9999);
        if (!empty($_SERVER['HTTP_DEVICEID']) && $requestFrom == 'APP'){
            $eventData = [
                'product'   => '账本充值',
                'orderId'   => $out_trade_no,
                'price'     => floatval($money),
            ];
            $riskCheck = new ShumeiRiskCheck();
            $account = $account ?: $memberInfo[$did]['account'];
            $checkResult = $riskCheck->shumeiCheckWithCaptcha('virtualOrder', 2, $account, $eventData, $captchaCode, $ip);
            if ($checkResult['code'] != 200){
                parent::apiReturn($checkResult['code'], $checkResult['data'], $checkResult['msg']);
            }
        }

        //如果是内网测试环境，直接把支付状态设置为已经支付
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
            //写入支付记录
            $model = new OnlineTrade();
            $ret   = $model->addLog($out_trade_no, $money, $body, $body, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_RECHARGE);
            if (!$ret) {
                parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
            }

            $trade_no = 'online_' . time();

            //模拟支付成功
            $accountMoneyBiz = new AccountMoney();
            $res             = $accountMoneyBiz->recharge($out_trade_no, self::SOURCE_T, $trade_no, $total_fee,
                'buyer.12301.cc',
                'seller.12301.cc', '12301.cc', '12301.cc');

            //TODO::包含全局文件
            if ($res !== true) {
                parent::apiReturn(401, [], '模拟支付失败');
            }

            //返回信息
            if ($is_qr == 1) {
                $data = ['outTradeNo' => $out_trade_no, 'qrUrl' => ''];
            } else {
                $data = ['parameter' => "{}", 'outTradeNo' => $out_trade_no];
            }
            parent::apiReturn(200, $data);
        } else {

            if ($shopId) {
                if (!$aid) {
                    exit('{"status":"fail","msg":"供应商Id有误"}');
                }
                $notifyUrl = self::XGS_NOTIFY_URL;
            } else {
                $notifyUrl = self::RECHARGE_NOTIFY_URL;
            }

            if ($is_qr == 1) {
                //$parameters = $this->wxPayLib->qrPay($total_fee, $body, $out_trade_no, self::RECHARGE_NOTIFY_URL,'微信充值','recharge');
                $parameters = $this->wxPayLib->qrPay($total_fee, $body, $out_trade_no, self::RECHARGE_NOTIFY_URL,
                    '微信充值', 'recharge', 1, $limitCreditPay);
                if ($parameters['return_code'] != 'SUCCESS') {
                    $msg = "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['return_msg']}";
                    if (!$is_qr) {
                        $msg .= ",您可以尝试使用【微信二维码支付】功能来完成充值";
                    }

                    parent::apiReturn(401, [], $msg);
                }
                $data = ['outTradeNo' => $out_trade_no, 'qrUrl' => $parameters['code_url']];
            }elseif ($is_qr == 2){   //app支付
                $parameters = $this->wxPayLib->appApiPay($total_fee, $body, $out_trade_no,
                    self::RECHARGE_NOTIFY_URL, '', '', 1, $limitCreditPay);
                if ($parameters['code'] != 200){
                    parent::apiReturn(401, [], $parameters['msg']);
                }
                $data = ['parameter' => $parameters['data'], 'outTradeNo' => $out_trade_no];
            } else {
                $parameters = $this->wxPayLib->jsApiPay($total_fee, $body, $out_trade_no, $openid,
                    self::RECHARGE_NOTIFY_URL, '', '', 1, $limitCreditPay);
                if (is_array($parameters) && isset($parameters['return_code']) && $parameters['return_code'] != 'SUCCESS') {
                    parent::apiReturn(401, [], "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['return_msg']}");
                }
                $data = ['parameter' => json_decode($parameters), 'outTradeNo' => $out_trade_no];
            }
            $model = new OnlineTrade();
            $ret   = $model->addLog($out_trade_no, $money, $body, $body, self::SOURCE_T,
                OnlineTrade::PAY_METHOD_RECHARGE);
            if (!$ret) {
                parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
            }

            parent::apiReturn(200, $data);
        }
    }

    /**
     * 微信退款——post
     *
     * <AUTHOR> Chen
     * @date 2016-10-05
     * @appid string 微信公众号appid
     * @out_trade_no string 票付通平台订单号
     * @total_fee int 支付金额，单位：元
     */
    public function Refund()
    {
        //退款权限控制
        $flag = 0;
        if (isset($_SESSION['openid'])) {
            $authOpenid = load_config('mobile_data_monitor');
            if (!isset($authOpenid[$_SESSION['openid']])) {
                exit('{"code":401,"msg":"Auth Error"}');
            }
            $flag = 1;
        }
        $independence = I('post.independence', 0, 'intval'); //对于云票务独立收款，session中是没有memberID的 by Cai Yiqiang 2018-06-21
        if ($independence == 0) {
            if ($flag == 0) {
                $loginInfo = $this->getLoginInfo('ajax', false, false);

                if (!$loginInfo) {
                    exit('{"code":401,"msg":"Auth Error"}');
                }
            }
        }

        $merchantId = I('post.merchant_id', 0, 'intval');
        if ($merchantId > 0) {
            $merBiz         = new \Business\Finance\PayMerchant();
            $config         = $merBiz->getMerchantConfig(\Business\Finance\PayMerchant::WEPAY, $merchantId);
            $this->wxPayLib = null;
            $this->wxPayLib = new \Library\Business\WePay\WxPayLib($config['appid'], $config);
        }
        $out_trade_no = I('post.out_trade_no');
        if (I('post.refund_raw', 0, 'intval') == 1) {
            $refund_fee = I('post.refund_money') * 100;
            $trade_no   = I('post.trade_no');
        } else {
            $modelTrade = new \Model\TradeRecord\OnlineRefund();
            $trade_info = $modelTrade->GetTradeLog($out_trade_no);

            $refund_fee = $trade_info['refund_money'];
            $trade_no   = $trade_info['trade_no'];
        }
        $total_fee     = I('post.total_fee', 0) * 100;
        $out_refund_no = I('post.ordernum') . '_' . time(); //商户退款单号，商户自定义，此处仅作举例
        $result        = $this->wxPayLib->refund($out_refund_no, $trade_no, $out_refund_no, $total_fee, $refund_fee);
        pft_log('wepay/refund', json_encode($result), 'month');
        if ($result['result_code'] == 'SUCCESS' && $result['return_code'] == 'SUCCESS') {
            //云票务独立收款退款成功需要通知 by Cai Yiqiang 2018-06-21
            parent::apiReturn(200, [], "微信退款成功", true);
        } else {
            parent::apiReturn(201, [], "退款失败,错误描述:{$result['return_msg']}", true);
        }
    }

    /**
     * 查询微信支付的订单-POST
     *
     * <AUTHOR> Chen
     * @date 2016-10-05
     * @appid 微信公众号appid
     * @out_trade_no string 平台订单号
     * @trade_no string 微信支付交易号
     */
    public function query()
    {
        $out_trade_no = I('post.out_trade_no');
        $trade_no     = I('post.trade_no');
        $ret_type     = I('post.ret_type', '');
        $res          = $this->wxPayLib->query($out_trade_no, $trade_no, $ret_type);
        if ($ret_type == 'xml') {
            echo $res;
        } else {
            echo json_encode($res);
        }
    }

    /**
     * 微信充值异步通知
     * <AUTHOR> chen
     * @date 2017-01-24
     */
    public function rechargeNotify()
    {
        $log_path = 'wepay_recharge';
        $notify   = new WxPayNotifyResponse();
        $notify->saveData($log_path);

        //记录日志
        $logData = json_encode([
            'key'    => '微信充值',
            'notify' => $notify->data,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('wepay_recharge', $logData, 3);
        $appid = $notify->data['appid'];

        WxPayApi::SetCommonInfo($appid);
        if ($appid == PFT_WECHAT_APPID && ENV != 'TEST') {
            WxPayApi::$key = 'a8884215d651c6b1ea0ea0ded4a04179';
        }

        //使用通用通知接口
        if ($notify->checkSign() == false) {
            $notify->SetReturn_code("FAIL"); //返回状态码
            $notify->SetReturn_msg("签名失败"); //返回信息
        } else {
            if ($notify->data["return_code"] == "FAIL") {
                $notify->SetReturn_code("FAIL");
                $notify->SetReturn_msg("通信出错");

                $logData = json_encode([
                    'key'   => '微信充值通信错误',
                    'error' => $notify->xml,
                ], JSON_UNESCAPED_UNICODE);
                pft_log('wepay_recharge', $logData, 2);

            } elseif ($notify->data["result_code"] == "FAIL") {
                $notify->SetReturn_code("FAIL");
                $notify->SetReturn_msg("业务出错");

                $logData = json_encode([
                    'key'   => '微信充值业务错误',
                    'error' => $notify->xml,
                ], JSON_UNESCAPED_UNICODE);
                pft_log('wepay_recharge', $logData, 2);

            } else {
                $notify->SetReturn_code("SUCCESS"); //设置返回码
                $ordern        = $notify->data['out_trade_no']; //订单号
                $trade_no      = $notify->data['transaction_id']; //交易号
                $pay_total_fee = (int)$notify->data['total_fee'] + 0; //金额用分为单位
                $sourceT       = 1;
                $wx_app_id     = $notify->data['appid'];
                // 如果有子商户appid，以子商户的appid为准
                if (isset($notify->data['sub_appid'])) {
                    $wx_app_id     = $notify->data['appid'];
                    $wx_sub_app_id = $notify->data['sub_appid'];
                }
                $buyerInfo  = array('openid' => $notify->data['openid']);
                $sellerInfo = array('appid' => $notify->data['appid']);
                if (isset($notify->data['sub_appid'])) {
                    $buyerInfo['sub_openid'] = $notify->data['sub_openid'];
                    $sellerInfo['sub_appid'] = $notify->data['sub_appid'];
                }
                $json_buy  = json_encode($buyerInfo);
                $json_sell = json_encode($sellerInfo);

                //旧逻辑，已经弃用
                // $modelRecharge = new Recharge();
                // $res           = $modelRecharge->OnlineRecharge($ordern, $sourceT, $trade_no, $pay_total_fee, $json_buy,
                //     $json_sell, $wx_app_id, $wx_sub_app_id);

                $accountMoneyBiz = new AccountMoney();
                $res             = $accountMoneyBiz->recharge($ordern, $sourceT, $trade_no, $pay_total_fee, $json_buy,
                    $json_sell, $wx_app_id, $wx_sub_app_id);

                //TODO::包含全局文件
                if ($res !== true) {
                    $notify->SetReturn_code("FAIL");
                    $notify->SetReturn_msg("发生错误");
                } else {
                    $notify->SetReturn_code("SUCCESS"); //设置返回码
                }
            }
        }
        $returnXml = $notify->ToXml();
        echo $returnXml;
    }

    /**
     * 退款查詢
     */
    public function refundQuery()
    {
        $out_trade_no  = I('post.out_trade_no');
        $trade_no      = I('post.trade_no');
        $out_refund_no = I('post.out_refund_no');
        $refund_id     = I('post.refund_id');
        $input         = new WxPayDataBase();
        $input->SetTransaction_id($trade_no);
        $input->SetOut_trade_no($out_trade_no);
        $res = $this->wxPayLib->refundQuery($out_trade_no, $trade_no, $out_refund_no, $refund_id);
        echo json_encode($res);
    }

    /**
     * 平台会员续费 - 这个接口已经废弃
     * @return json
     */
    public function renew()
    {
        //请求日志
        $logData = json_encode($_REQUEST, JSON_UNESCAPED_UNICODE);
        @pft_log('wepay/renew',  $logData);

        $this->apiReturn(204, [], '充值记录生成失败');
    }

    /**
     * 续费通知接口 - 这个接口已经废弃
     *
     */
    public function renewNotify()
    {
        //请求日志
        $logData = json_encode($_REQUEST, JSON_UNESCAPED_UNICODE);
        @pft_log('wepay/renew', $logData);

        exit('fail');
    }

    /**
     * 年卡微信续费
     * <AUTHOR>
     */
    public function annualRenew()
    {
        $this->apiReturn(204, [], '续费服务暂停');
        $memberId = $this->isLogin('ajax');
        //openid
        $openid = I('openid');
        //年卡id
        $cardId = I('id', 0, 'intval');
        //上级供应商id
        $sid = I('sid', 0, 'intval');

        if (!$openid || !$cardId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $vipBiz = new \Business\Mall\MemberVip($memberId, $sid);

        //续费合法性检测
        $annualBiz = new \Business\Product\AnnualCard();
        $checkRes  = $annualBiz->annualRenewHCheck($cardId, 'id', false, $memberId);

        if ($checkRes['code'] !== 200) {
            $this->apiReturn(204, [], $checkRes['msg']);
        }

        $cardBase = $checkRes['data'];

        //获取续费金额
        $ticModel = new \Model\Product\Ticket('slave');
        $retail   = $ticModel->getRetailPrice($cardBase['pid']);
        if ($retail === false) {
            $this->apiReturn(204, [], '续费价格获取失败,此年卡产品可能已过期');
        }
        $retail *= 100;

        //生成平台订单号
        $subject    = '年卡续费';
        $outTradeNo = 'annual' . time() . rand(1000, 9999);
        //提交支付请求
        $result = $this->_orderPayByJsapi($retail, $outTradeNo, $subject, $openid, self::ANNUAL_RENEW_NOTIFY_URL);

        if ($result['status'] == 0) {
            $this->apiReturn(204, [], $result['msg']);
        } else {
            //购买者信息
            $buyerInfo = [
                'member_id' => $memberId,
                'card_id'   => $cardId,
                'sid'       => $sid,
            ];
            $buyerInfo = json_encode($buyerInfo);
            //在线续费记录
            $tradeModel = new OnlineTrade();
            $tradeRes   = $tradeModel->addRecord($outTradeNo, $subject, $retail, '', $buyerInfo, 1);
            if (!$tradeRes) {
                $this->apiReturn(204, [], '支付记录生成失败');
            }
            $this->apiReturn(200, $result['data']);
        }
    }

    /**
     * 年卡续费通知
     * <AUTHOR>
     */
    public function annualRenewNotify()
    {
        //请求日志
        $logData = json_encode($_REQUEST, JSON_UNESCAPED_UNICODE);
        @pft_log('wepay/annualrenew', $logData);

        $notify = new WxPayNotifyResponse();
        $notify->saveData('wepay/renew');
        $appid = $notify->data['appid'];

        //支付检测
        $checkRes = $this->_notifyBaseCheck($notify);
        if (!$checkRes) {
            //支付出错直接返回
            exit($notify->ToXml());
        }

        //这边加个锁
        $Redis = Cache::getInstance('redis');
        $key   = 'renew' . $notify->data['out_trade_no'];
        $lock  = $Redis->lock($key, 1, 30);

        if (!$lock) {
            exit($notify->ToXml());
        }

        $outTradeNo = $notify->data['out_trade_no'];
        $money      = $notify->data['total_fee'];
        $tradeNo    = $notify->data['transaction_id'];

        $vipBiz = new \Business\Mall\MemberVip(1, 1);
        $result = $vipBiz->wechatRenewNotify($outTradeNo, $money, $tradeNo);
        @pft_log('wepay/annualrenew', json_encode($result), 'day');

        exit($notify->ToXml());
    }

    /**
     * 微信支付回调
     */
    public function orderNotify()
    {
        $notify = new WxPayNotifyResponse();
        $notify->saveData();
        //记录日志
        $logData = json_encode([
            'key'    => '微信订单二维码支付',
            'notify' => $notify->data,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('wepay_qr_order', $logData, 3);
        // $pay2Pft = $this->payToPft($notify);
        $pay2Pft = $this->checkPayToPft($notify->data['mch_id'], $notify->data['sub_mch_id'], $notify->data['sub_appid'], $notify->data['appid']);
        $appid   = isset($notify->data['sub_appid']) ? $notify->data['sub_appid'] : $notify->data['appid'];
        $payMerchantConfig = [];
        // 独立收款的用户,获取对应的配置
        if ($pay2Pft === false) {
            $payMerchantBiz    = new PayMerchant();
            $payMerchantConfig = $payMerchantBiz->getMerchantConfigBySubInfo($notify->data['sub_appid'],
                $notify->data['sub_mch_id']);
            if (!empty($payMerchantConfig['key'])) {
                $WePayConf[$appid]['key'] = $payMerchantConfig['key'];
            }
        }
        $this->wxPayLib   = new WxPayLib($appid, $payMerchantConfig);
        //支付检测
        $checkRes = $this->_notifyBaseCheck($notify);
        if (!$checkRes) {
            //支付出错直接返回
            exit($notify->ToXml());
        }
        //订单号
        $buyerInfo  = ['openid' => $notify->data['openid']];
        $sellerInfo = ['appid' => $notify->data['appid']];
        $outTradeNo = str_replace(['qr_', 'card_'], '', $notify->data['out_trade_no']);
        $attach     = $notify->data['attach'];
        //交易号
        $tradeNo     = $notify->data['transaction_id'];
        $payTotalFee = $notify->data['total_fee']; //金额用分为单位
        $sourceT     = 1;
        $payChannel  = 10;
        if (isset($notify->data['sub_appid'])) {
            $buyerInfo['sub_openid'] = $notify->data['sub_openid'];
            $sellerInfo['sub_appid'] = $notify->data['sub_appid'];
        }
        //调用统一的内部soap接口
        $server = \Library\Tools\Helpers::GetSoapInside();

        $OrderModel = new \Model\Order\OrderTools('slave');
        $orderInfo  = $OrderModel->getOrderInfo($outTradeNo);

        $jsonBuy  = json_encode($buyerInfo);
        $jsonSell = json_encode($sellerInfo);
        $daction  = $attach == 'TICKET_MACHINE_12301' ? 0 : null;

        $biz = new \Business\Order\MergeOrder();
        if ($biz->isCombineOrder($outTradeNo)) {
            $result = $biz->handlerCombinePayOrders($outTradeNo, $tradeNo, OnlineTrade::CHANNEL_WEPAY, $daction,
                $jsonSell, $jsonBuy, $pay2Pft, $payChannel, '');

            $needVerify = PayCache::getOrderVerfify($outTradeNo);
            if ($needVerify) {
                $successOrders = $result['data']['success_orders'];
                $paymentObj    = new \Business\Order\MergeOrder();
                $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
            }
            $notify->SetReturn_msg('OK');
            $notify->SetReturn_code('SUCCESS');
            exit($notify->ToXml());
        }

        $res = $server->Change_Order_Pay($outTradeNo, $tradeNo, $sourceT, $payTotalFee, $daction,
            $jsonSell, $jsonBuy, 1, $pay2Pft, $payChannel);

        $logData = json_encode([
            'key'      => '微信二维码支付结果',
            'ordernum' => $outTradeNo,
            'trade_no' => $tradeNo,
            'res'      => $res,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('wepay_qr_order', $logData, 3);

        //支付逻辑在内部扣款失败的时候会返回0
        if ($res == false) {
            $notify->SetReturn_msg('支付失败');
            $notify->SetReturn_code('FAIL');
            exit($notify->ToXml());
        } elseif ($res == 101 || $res == 102) {
            $notify->SetReturn_msg('OK');
            $notify->SetReturn_code('SUCCESS');
            exit($notify->ToXml());
        }

        //最后重发短信
        $job_id = \Library\Resque\Queue::push('notify', 'OrderNotify_Job',
            [
                'ordernum' => $outTradeNo,
                'buyerId'  => $orderInfo['member'], //购买人的ID
                'mobile'   => $orderInfo['ordertel'],
                'aid'      => $orderInfo['aid'],
            ]
        );
        pft_log('wepay/ok', "订单号[$outTradeNo],job_id=$job_id");
        $notify->SetReturn_msg('OK');
        $notify->SetReturn_code('SUCCESS');
        exit($notify->ToXml());
    }

    /**
     * 仙盖山微信充值
     *
     * @date   2018-03-22
     * <AUTHOR> Lan
     *
     * @param [
     *          'money'  => 充值金额，单位元
     *          'openid' => openid
     *          'aid'    => 供应商Id
     *          'shop_id'=> 商铺Id
     *          'did'    => 会员Id
     *          'qr_pay' => 支付方式：1=扫码，0=jsAPI
     *          'memo'   => 备注
     *        ]
     *
     * @return string
     */
    public function xgsRecharge()
    {
        $money     = I('post.money');
        $money     = floatval(number_format($money, 2, '.', ''));
        $total_fee = $money * 100;
        $openid    = I('post.openid');
        $aid       = I('post.aid');
        $shopId    = I('post.shop_id');
        $did       = I('post.did', 0);
        $is_qr     = I('post.qr_pay', 0);
        $memo      = I('post.memo', '', 'trim');

        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $sid       = $loginInfo['sid'] ?? 0;

        $did = $did ? $did : $sid;
        if (!$did) {
            parent::apiReturn(401, [], '用户身份获取错误');
        }

        if ($is_qr == 0 && empty($openid)) {
            parent::apiReturn(401, [], 'OPENID为空');
        }

        if (!is_numeric($money) || $money < 0) {
            parent::apiReturn(401, [], '请输入大于0的金额，金额必须是数字');
        }

        $modelMember = new Member();

        if ($did == 1) {
            $body = '补打款';
        } else {

            $queryParams = [[$did, $shopId, $aid]];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                $queryParams);

            if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                parent::apiReturn(401, [], '用户信息获取失败，请稍后重试');
            }

            $memberInfo = array_column($queryRes['data'], null, 'id');

            //$sellerName = $modelMember->getMemberCacheById($did, 'dname');
            //$shopName   = $modelMember->getMemberCacheById($shopId, 'dname');
            //$bossName   = $modelMember->getMemberCacheById($aid, 'dname');

            $sellerName = $memberInfo[$did]['dname'] ?? '';
            $shopName   = $memberInfo[$shopId]['dname'] ?? '';
            $bossName   = $memberInfo[$aid]['dname'] ?? '';
            $body       = "[$sellerName]通过{$shopName}给{$bossName}充值{$money}元|{$did}|$shopId|$aid";
        }

        if ($memo) {
            $body .= '|' . $memo;
        }

        $outTradeNo = time() . $did . mt_rand(1000, 9999);

        $limitCreditPay = false;
        if ($is_qr) {
            $parameters = $this->wxPayLib->qrPay($total_fee, $body, $outTradeNo, self::XGS_NOTIFY_URL, '微信充值',
                'recharge', 1, $limitCreditPay);

            if ($parameters['return_code'] != 'SUCCESS') {
                $msg = "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['return_msg']}";
                if (!$is_qr) {
                    $msg .= ",您可以尝试使用【微信二维码支付】功能来完成充值";
                }

                parent::apiReturn(401, [], $msg);
            }

            $data = ['outTradeNo' => $outTradeNo, 'qrUrl' => $parameters['code_url']];
        } else {
            $parameters = $this->wxPayLib->jsApiPay($total_fee, $body, $outTradeNo, $openid, self::XGS_NOTIFY_URL, '',
                '', 1, $limitCreditPay);
            if (is_array($parameters) && isset($parameters['return_code']) && $parameters['return_code'] != 'SUCCESS') {
                parent::apiReturn(401, [], "支付失敗，向微信提交支付订单失败，错误信息：{$parameters['return_msg']}");
            }

            $data = ['parameter' => json_decode($parameters), 'outTradeNo' => $outTradeNo];
        }

        $model = new OnlineTrade();

        $ret = $model->addLog($outTradeNo, $money, $body, $body, self::SOURCE_T, OnlineTrade::PAY_METHOD_RECHARGE);

        if (!$ret) {
            parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
        }

        parent::apiReturn(200, $data);
    }

    /**
     * 仙盖山微信回调业务处理
     *
     * @date   2018-04-14
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    public function xgsNotify()
    {
        $log_path = 'wepay/xgsRecharge';
        $notify   = new WxPayNotifyResponse();

        $notify->saveData($log_path);

        $appid = $notify->data['appid'];

        if ($appid == PFT_WECHAT_APPID) {
            WxPayApi::$key = 'a8884215d651c6b1ea0ea0ded4a04179';
        }

        //支付检测
        $checkRes = $this->_notifyBaseCheck($notify);
        if (!$checkRes) {
            //支付出错直接返回
            exit($notify->ToXml());
        }

        $ordern        = $notify->data['out_trade_no']; //订单号
        $trade_no      = $notify->data['transaction_id']; //交易号
        $pay_total_fee = (int)$notify->data['total_fee'] + 0; //金额用分为单位
        $sourceT       = 1;
        $wx_app_id     = $notify->data['appid'];

        // 如果有子商户appid，以子商户的appid为准
        if (isset($notify->data['sub_appid'])) {
            $wx_app_id     = $notify->data['sub_appid'];
            $wx_sub_app_id = $notify->data['sub_appid'];
        }

        $buyerInfo  = array('openid' => $notify->data['openid']);
        $sellerInfo = array('appid' => $notify->data['appid']);

        if (isset($notify->data['sub_appid'])) {
            $buyerInfo['sub_openid'] = $notify->data['sub_openid'];
            $sellerInfo['sub_appid'] = $notify->data['sub_appid'];
        }

        $json_buy  = json_encode($buyerInfo);
        $json_sell = json_encode($sellerInfo);

        $businessLib = new UnifyCard();

        $res = $businessLib->rechargeNotify($ordern, $sourceT, $trade_no, $pay_total_fee, $json_buy,
            $json_sell, $wx_app_id, $wx_sub_app_id);

        if ($res !== true) {
            $notify->SetReturn_code("FAIL");
            $notify->SetReturn_msg("发生错误");
        } else {
            $notify->SetReturn_code("SUCCESS"); //设置返回码
        }

        $returnXml = $notify->ToXml();
        echo $returnXml;
    }

    /**
     * 获取订单信息
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @param  string  $ordernum
     *
     * @return array
     */
    private function _getOrder($ordernum)
    {
        $this->orderModel = new OrderTools('localhost');
        $orderInfo        = $this->orderModel->getOrderInfo($ordernum,
            'member,ordername,ordertel,aid,code,lid,tid,tnum,tprice,ordermode,paymode,salerid,ss.pay_status,ss.status',
            'de.aids,de.series');
        if ($orderInfo['aids']) {
            // 转分销获取第二级分销商
            $tmp                 = explode(',', $orderInfo['aids']);
            $orderInfo['member'] = $tmp[1];
        }
        $this->orderInfo = $orderInfo;

        return $orderInfo;
    }

    /**
     * 支付基本检测,包括签名以及支付结果等
     *
     * @param  WxPayNotifyResponse  $notify
     * @return bool
     */
    private function _notifyBaseCheck(&$notify)
    {
        //签名验证
        if ($notify->checkSign() == false) {
            //设置返回状态码,下同
            $notify->SetReturn_code("FAIL");
            //设置返回信息,下同
            $notify->SetReturn_msg("签名失败");
            @pft_log('wepay/error', '签名验证失败');

            return false;
        }
        //通信检测
        if ($notify->data["return_code"] == "FAIL") {
            $notify->SetReturn_code("FAIL");
            $notify->SetReturn_msg("通信出错");

            @pft_log('wepay/error', "通信出错:\n" . $notify->xml);

            return false;
        }
        //业务检测
        if ($notify->data["result_code"] == "FAIL") {
            $notify->SetReturn_code("FAIL");
            $notify->SetReturn_msg("业务出错");

            @pft_log('wepay/error', "业务出错:\n" . $notify->xml);

            return false;
        }
        $notify->SetReturn_code("SUCCESS");

        return true;
    }

    /**
     * 生成支付订单号
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @param  string  $ordernum
     *
     * @return string
     */
    private function filterOrderNum($ordernum)
    {
        return str_replace(['qr_'], '', $ordernum);
    }

    /**
     * 内网模拟发送短信
     * <AUTHOR>
     * @date   2018-06-08
     *
     * @param  string  $ordernum
     *
     * @return null
     */
    private function _simuSendSms($ordernum)
    {
        $orderModel = new OrderTools('slave');
        $orderInfo  = $orderModel->getOrderInfo($ordernum);

        $jobId = Queue::push('notify', 'OrderNotify_Job',
            [
                'ordernum' => $ordernum,
                'buyerId'  => $orderInfo['member'], //购买人的ID
                'mobile'   => $orderInfo['ordertel'],
                'aid'      => $orderInfo['aid'],
            ]
        );
        pft_log('alipay_pc/ok', "$ordernum|job_id=$jobId");
    }

    /**
     * 生成小程序支付码,应用于自助机场景
     *
     * <AUTHOR> Chen
     * @date 2018-08-06
     */
    public function getPayCodeByOrdernumOld()
    {
        $ac         = I('post.ac', '123624');
        $orderNum   = I('post.out_trade_no');
        $merchantId = I('post.merchant_id');
        $channel    = I('post.channel', 2); // 提交渠道，与追踪记录表的source字段一致。
        $scene      = $orderNum . ':' . $merchantId . ':' . $channel; // 最长32位
        $page       = I('post.page', 'pages/pay/pay');
        $width      = 450;
        $lib        = new WechatSmallApp();
        $res        = $lib->getWxCodeUnlimit($ac, $scene, $page, $width);
        $prefix     = substr($res['data'], 0, 1);
        if ($prefix == '{') {
            $decodeRes = json_decode($res['data'], true);
            $this->apiReturn(401, $res, $decodeRes['errmsg']);
        }
        $fileName = "autodelete/{$orderNum}.png";
        $config   = load_config('qiniu', 'Qiniu');
        $qiniu    = new Qiniu($config);
        $fileUrl  = $qiniu->hasFileExist($fileName);
        if ($fileUrl != false) {
            $ret['url']        = $fileUrl;
            $ret['outTradeNo'] = $orderNum;
            parent::apiReturn(200, $ret);
        }

        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();

        $upManager = new UploadManager();
        $auth      = new Auth($config['accessKey'], $config['secretKey']);
        $token     = $auth->uploadToken($config['images']['bucket'], null, 3600);
        list($ret, $error) = $upManager->put($token, $fileName, $res['data']);
        // 如果小程序码失败，走原来的二维码支付接口
        if ($error) {
            $code = 400;
        } else {
            $code              = 200;
            $ret['url']        = $config['images']['domain'] . $fileName;
            $ret['outTradeNo'] = $orderNum;
        }
        parent::apiReturn($code, $ret);
    }

    /**
     * 生成小程序支付码,应用于自助机场景--阿里云OSS存储
     *
     * <AUTHOR> Chen
     * @date 2018-08-09
     */
    public function getPayCodeByOrdernum()
    {
        $defaultAc  = ENV == 'PRODUCTION' ? '123624' : '100014';
        $ac         = I('post.ac', $defaultAc);
        $orderNum   = I('post.out_trade_no');
        $merchantId = I('post.merchant_id');
        $channel    = I('post.channel', 2); // 提交渠道，与追踪记录表的source字段一致。
        $scene      = $orderNum . ':' . $merchantId . ':' . $channel; // 最长32位
        $page       = I('post.page', 'pages/pay/pay');
        $width      = 450;
        $lib        = new WechatSmallApp();
        $res        = $lib->getWxCodeUnlimit($ac, $scene, $page, $width);
        $prefix     = substr($res['data'], 0, 1);
        if ($prefix == '{') {
            $decodeRes = json_decode($res['data'], true);
            $this->apiReturn(401, $res, $decodeRes['errmsg']);
        }
        $fileName = "wechat_miniapp_code/{$orderNum}.png";
        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();
        $baseConfig = C('oss');
        $config     = $baseConfig['aliyun']['senic'];
        $endpoint   = ENV == 'PRODUCTION' ? $config['endpoint']['prod'] : $config['endpoint']['dev'];
        $ret        = ['outTradeNo' => $orderNum];
        try {
            $ossClient = new OssClient($config['accessId'], $config['accessKeySecret'], $endpoint, false);
            $res       = $ossClient->putObject($config['bucket'], $fileName, $res['data']);
            if ($res['info']['http_code'] == 200) {
                $code       = 200;
                $ret['url'] = $config['domain'] . '/' . $fileName . '?x-oss-process=style/image_shuiyin';
            } else {
                $code = 400;
            }
        } catch (OssException $exception) {
            $code = 400;
        }
        parent::apiReturn($code, $ret);
    }

    public function specialOrderPay(){
        //平台订单号
        $outTradeNo       = I('out_trade_no', 0, 'string');
        $specialOrderMode = I('special_mode',0);
        //是否二维码支付
        $qrPay            = I('is_qr', 0, 'intval');
        //微信用户openid
        $openid           = I('openid', null);
        // 公众号对应的实际openid——独立收款商户
        $subAppid         = I('post.sub_appid', null);
        $subOpendid       = I('post.sub_openid', null);
        if (!is_null($subAppid) && $subAppid == WxPayApi::$sub_appid && $subOpendid) {
            $openid       = $subOpendid;
        }
        //订单主体说明
        $subject          = mb_substr(trim(I('subject')), 0, 20, 'utf-8');
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }

        if (!$specialOrderMode){
            parent::apiReturn(401, [], '业务类型错误');
        }

        $specialOrderBiz  = new SpecialOrderPay();
        $moneyRes         = $specialOrderBiz->specialOrderPayCheck($outTradeNo,$specialOrderMode);
        if ($moneyRes['code'] != 200){
            parent::apiReturn($moneyRes['code'], $moneyRes['data'], $moneyRes['msg']);
        }
        $orderNotifyUrl   = $specialOrderBiz->chooseOrderNotify($specialOrderMode,'wx');
        if (!$orderNotifyUrl){
            parent::apiReturn(401, [], '回调地址有误');
        }
        $orderMoney       = $moneyRes['data'];
        if ($qrPay) {
            //生成微信支付二维码
            $tmpOutTradeNo = $outTradeNo;
            $result        = $this->_orderPayByQrcodeOrH5($qrPay, $orderMoney, $tmpOutTradeNo, $subject,$orderNotifyUrl);
            $data          = ['outTradeNo' => $outTradeNo, 'qrUrl' => $result['data']];
        } else {
            //调用微信jsapi支付
            $result        = $this->_orderPayByJsapi($orderMoney, $outTradeNo, $subject, $openid,$orderNotifyUrl);
            $data          = $result['data'];
        }
        if ($result['status'] == 0) {
            parent::apiReturn(401, [], $result['msg']);
        } else {
            //生成支付记录
            $payMethod     = OnlineTrade::PAY_METHOD_ORDER;
            $tradeModel    = new OnlineTrade();
            $ret           = $tradeModel->addLog($outTradeNo, $orderMoney / 100, $subject, $subject, self::SOURCE_T, $payMethod, '', $this->merchantId);
            if (!$ret) {
                parent::apiReturn(401, [], '支付记录生成失败');
            }
        }
        $specialOrderBiz->afterPayHandle($specialOrderMode);
        //兼容旧的微信小程序支付，增加status字段
        echo json_encode(['code' => 200, 'data' => $data, 'status' => 'ok', 'msg' => 'success']);
        exit;
    }

    public function specialNotify(){
        $log_path     = 'wepay/special_order';
        $notify       = new WxPayNotifyResponse();
        $notify->saveData($log_path);

        //记录日志
        $logData      = json_encode([
            'key'     => '微信订单二维码支付',
            'notify'  => $notify->data,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('wepay_sp_order', $logData, 3);

        $appid        = $notify->data['appid'];
        //支付检测
        $checkRes     = $this->_notifyBaseCheck($notify);
        if (!$checkRes) {
            //支付出错直接返回
            exit($notify->ToXml());
        }

        if (!PayBase::setSpecialLock($notify->data['out_trade_no'])){
            exit($notify->ToXml());
        }
        $buyerInfo    = array('openid' => $notify->data['openid']);
        $sellerInfo   = array('appid' => $notify->data['appid']);
        $pay_to_pft   = true;
        if (isset($notify->data['sub_appid'])) {
            $buyerInfo['sub_openid'] = $notify->data['sub_openid'];
            $sellerInfo['sub_appid'] = $notify->data['sub_appid'];
        }
        if ($notify->data['mch_id'] == '1287192601' || $notify->data['sub_mch_id'] == '1287192601'|| $notify->data['sub_mch_id'] == '1510469351') {
            $pay_to_pft     = true;
        } elseif (isset($notify->data['sub_appid'])) { // 子商户模式
            if ($notify->data['sub_appid'] != 'wxd72be21f7455640d') {
                $pay_to_pft = false;
            }
        } elseif ($notify->data['appid'] != 'wxd72be21f7455640d') { // 普通模式
            $pay_to_pft     = false;
        }
        $outTradeNo      = $notify->data['out_trade_no']; //订单号
        $transaction_id  = $notify->data['transaction_id']; //交易号
        $total_fee       = (int) $notify->data['total_fee'] + 0; //金额用分为单位
        $pay_channel     = 4;     //支付渠道
        $is_member       = null;
        $buyerInfo       = json_encode($buyerInfo);
        $sellerInfo      = json_encode($sellerInfo);
        $specialOrderBiz = new SpecialOrderPay();
        $result          = $specialOrderBiz->specialOrderHasPayAfter($outTradeNo,$transaction_id,$pay_channel,$total_fee,$buyerInfo,$sellerInfo,$pay_to_pft);
        pft_log('member/system/info','返回详情：'.$outTradeNo.json_encode($result));
        if ($result['code'] == 10){   //预留退款的
            pft_log('member/system/refund','订单退款：'.$outTradeNo);
            $specialOrderBiz->specialOnlineRefund($outTradeNo,$transaction_id,self::SOURCE_T,$sellerInfo,$buyerInfo,$pay_to_pft,$total_fee,'会员加入失败');
        }
        echo '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';
        exit();

    }
    /**
     * app通用支付
     * @param array $paramsData 支付传入参数
     * @return array
     */
    public function appApiPay(array $paramsData){
        $outTradeNo = $paramsData['order_id'];
        $money      = $paramsData['money'];
        $subject    = $paramsData['subject'];
        $notifyUrl  = $paramsData['notify_url'];
        $merchantId = $paramsData['merchant_id'];
        $parameters = $this->wxPayLib->appApiPay($money, $subject, $outTradeNo,
            self::COMMON_NOTIFY_URL, '', '', 1);
        if ($parameters['code'] != 200){
            parent::apiReturn(401, [], $parameters['msg']);
        }
        $logMoney = number_format($money / 100, 2, '.', '');
        $model = new OnlineTrade();
        //todo 目前只有自己收款的调用这个接口merchant = 0
        $ret   = $model->addLog($outTradeNo, $logMoney, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_SIMPLE, '', $merchantId, '', '', $notifyUrl);
        if (!$ret){
            parent::apiReturn(204,[],'交易记录生成失败');
        }
        $data = ['parameter' => $parameters['data'], 'outTradeNo' => $outTradeNo];
        parent::apiReturn(200, $data);
    }
    /**
     * app通用支付回调
     *
     * @return array
     */
    public function commonNotify(){
        $log_path = 'wepay_commonNotify';
        $notify   = new WxPayNotifyResponse();
        $notify->saveData($log_path);
        $appid     = isset($notify->data['sub_appid']) ? $notify->data['sub_appid'] : $notify->data['appid'];
        $WePayConf = include '/var/www/html/Service/Conf/WePay.conf.php';
        if (isset($notify->data['sub_appid']) && !empty($notify->data['sub_appid'])) {
            // 如果存在sub_appid, 并且存在独立配置, 从数据库中获取key
            $payMerchantBiz    = new PayMerchant();
            $payMerchantConfig = $payMerchantBiz->getMerchantConfigBySubInfo($notify->data['sub_appid'], $notify->data['sub_mch_id']);
            if (!empty($payMerchantConfig['key'])) {
                $WePayConf[$appid]['key'] = $payMerchantConfig['key'];
            }
        }
        //校验签名
        if (ENV === 'PRODUCTION' && $notify->checkSign($WePayConf[$appid]['key']) == false) {
            PayBase::setPayDailyRecord(2, '通用APP支付回调签名错误', $notify->data);
            $notify->SetReturn_code("FAIL"); //返回状态码
            $notify->SetReturn_msg("签名失败"); //返回信息
            $returnXml = $notify->ToXml();
            echo $returnXml;
            exit;
        }
        if ($notify->data["return_code"] == "FAIL") {
            PayBase::setPayDailyRecord(2, '通用APP支付回调通信出错', $notify->data);
            $notify->SetReturn_code("FAIL");
            $notify->SetReturn_msg("通信出错");
            $returnXml = $notify->ToXml();
            echo $returnXml;
            exit;
        }
        if ($notify->data["result_code"] == "FAIL") {
            PayBase::setPayDailyRecord(2, '通用APP支付回调业务出错', $notify->data);
            $notify->SetReturn_code("FAIL");
            $notify->SetReturn_msg("业务出错");
            $returnXml = $notify->ToXml();
            echo $returnXml;
            exit;
        }
        PayBase::setPayDailyRecord(2, '通用APP支付回调参数', $notify->data);
        $pay_to_pft = true; //票付通微信商户收钱
        $buyerInfo    = array('openid' => $notify->data['openid']);
        $sellerInfo   = array('appid' => $notify->data['appid']);
        if (isset($notify->data['sub_appid'])) {
            $buyerInfo['sub_openid'] = $notify->data['sub_openid'];
            $sellerInfo['sub_appid'] = $notify->data['sub_appid'];
        }
        if ($notify->data['mch_id'] == '1287192601' || $notify->data['sub_mch_id'] == '1287192601' || $notify->data['sub_mch_id'] == '1510469351') {
            $pay_to_pft = true;
        } elseif (isset($notify->data['sub_appid'])) { // 子商户模式
            if ($notify->data['sub_appid'] != 'wxd72be21f7455640d') {
                $pay_to_pft = false;
            }
        } elseif ($notify->data['appid'] != 'wxd72be21f7455640d') { // 普通模式
            $pay_to_pft = false;
        }
        $payCenterBiz = new PayCenter();
        $json_buy  = json_encode($buyerInfo);
        $json_sell = json_encode($sellerInfo);
        $optionData   = [
            'pay_to_pft' => $pay_to_pft,
            'sell_info'  => $json_sell,
            'buyer_info' => $json_buy,
        ];

        try {
            $payCenterBiz->setOptionData($optionData);
            $payCenterBiz->setNotifyData($notify->data['out_trade_no'], $notify->data['transaction_id'],
                $notify->data['total_fee'], $payCenterBiz::STATUS_SUCCESS, self::SOURCE_T, $notify->data['attach'] ?? '');
        } catch (\Exception $e) {
            PayBase::setPayDailyRecord(2, '通用APP支付回调参数错误', $notify->data, [$e->getMessage()]);
            $notify->SetReturn_code("FAIL");
            $notify->SetReturn_msg("业务出错");
            $returnXml = $notify->ToXml();
            echo $returnXml;
            exit;
        }
        $res = $payCenterBiz->notify();
        PayBase::setPayDailyRecord(2, '通用APP支付回调参数结果', $notify->data, $res);
        if (in_array($res['code'], [200])) {
            $notify->SetReturn_code("SUCCESS"); //设置返回码
        } else {
            $notify->SetReturn_code("FAIL");
            $notify->SetReturn_msg("发生错误");
        }

        $returnXml = $notify->ToXml();
        echo $returnXml;
    }

    /**
     * 通用扫码的回调触发接口
     * <AUTHOR>
     * @date   2020-11-18
     * @return array
     */
    public function qrCommonNotifyTouch()
    {
        if (ENV == 'PRODUCTION') {
            parent::apiReturn(401, [], '禁止推送');
        }
        $orderNum = I('post.order_id', '');
        if (!$orderNum) {
            parent::apiReturn(401, [], '订单号缺失');
        }
        $code = 200;
        $msg  = '推送成功';
        try {
            $tradeLogModel = new OnlineTrade();
            $payCenterBiz  = new PayCenter();
            // 获取支付记录
            $payLogArr = $tradeLogModel->getLog($orderNum, self::SOURCE_T);
            if (empty($payLogArr)) {
                throw new \Exception('未找到支付记录');
            }
            $payCenterBiz->setNotifyData($orderNum, rand(1, 9999999), $payLogArr['total_fee'] * 100,
                $payCenterBiz::STATUS_SUCCESS, self::SOURCE_T);
            $res = $payCenterBiz->notify();
            if ($res['code'] != 200) {
                throw new \Exception($res['msg']);
            }
        } catch (\Exception $e) {
            $code = 401;
            $msg  = $e->getMessage();
        }
        parent::apiReturn($code, [], $msg);
    }

    /**
     * 通用JSAPI支付接口
     * @author: Guangpeng Chen
     * @date: 2021/8/4
     * @param  array  $paramsData
     */
    public function jsApiPay(array $paramsData)
    {
        $outTradeNo = $paramsData['order_id'];
        $merchantId = $paramsData['merchant_id'];
        $money      = $paramsData['money'];
        $subject    = $paramsData['subject'];
        $notifyUrl  = $paramsData['notify_url'];
        $payType    = $paramsData['pay_type'];
        $frontUrl   = $paramsData['front_url'];
        $openid     = $paramsData['open_id'];
        $appid      = $paramsData['appid']?:'wxd72be21f7455640d';
        $attach     = $paramsData['attach']?:'';
        $detail     = !empty($paramsData['detail'])?$paramsData['detail']:$subject;
        // 订单超时的秒数
        $orderExpire= $paramsData['order_expire']?$paramsData['order_expire']:3600;
        $result        = $this->_orderPayByJsapi($money, $outTradeNo, $subject, $openid, self::COMMON_NOTIFY_URL, $attach, $orderExpire);

        if (!$openid){
            parent::apiReturn(401, [], 'openid缺失');
        }
        $money = number_format($money / 100, 2, '.', '');

        if ($result['status'] == 0) {
            parent::apiReturn(401, [], $result['msg']);
        }
        if (!empty($frontUrl)) {
            (new CommonHandle())->setRedirectUrlRpc($outTradeNo, $frontUrl);
            //(new WxPayGoldPlan())->setRedirectUrl($outTradeNo, $frontUrl);
        }
        $model = new OnlineTrade();
        $ret   = $model->addLog($outTradeNo, $money, $subject, $detail, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_SIMPLE, '', $merchantId, '', '', $notifyUrl);
        $model = null;
        if (!$ret) {
            parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
        }
        parent::apiReturn(200, $result['data']);
    }
}
