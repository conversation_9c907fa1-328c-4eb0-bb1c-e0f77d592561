<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 2/21-021
 * Time: 14:35
 */

namespace Controller\pay;


interface PayInterface
{
    /**
     * {"code": 200, "data": { "qrUrl" : "weixin://wxpay/bizpayurl?pr=3xELohV",//要用qrcode生成二维码 "outTradeNo" : "415a4s1d5asd4" //充值流水号 }, "msg": ""}
     * @return mixed
     */
    public function renew();
    public function recharge();
    public function order();
    public function cardSolutionRecharge();
    public function micropay();
    public function Query();
    public function Refund();
    public function rechargeNotify();
    public function orderNotify();
    public function payResultCheck();
}