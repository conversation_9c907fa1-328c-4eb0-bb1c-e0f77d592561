<?php
/**
 * Created by PhpStorm.
 * User: cgp
 * Date: 2018/8/11
 * Time: 16:31
 */

namespace Controller\pay;

use Library\Business\Alipay\F2FPay;
use Library\Business\WePay\WxPayLib;
use Library\Controller;

class Verify extends Controller
{
    const ORDER_NOTIFY_URL         = PAY_DOMAIN . 'r/pay_Verify/notify';

    public function __construct()
    {
        if ($_GET['a']!='notify' && PHP_SAPI != 'cli') {
            parent::isLogin();
            if (!parent::isSuper()) {
                parent::apiReturn(parent::CODE_AUTH_ERROR, [], '无权限操作');
            }
        }
    }
    private $ordernum = '';
    private $merchantId = 0;
    /**
     * @var WxPayLib
     */
    private $wxPayLib = null;
    /**
     * @var F2FPay
     */
    private $aliPayLib = null;
    private $appAuthToken = null;
    public function pay()
    {
        $method     = I('post.method');
        $merchantId = I('post.merchant_id');
        if(empty($method) || empty($merchantId)){
            parent::apiReturn(400, "", "参数不能为空");
        }
        $this->ordernum = 'test_' . time();
        if (!method_exists($this, $method)) {
            parent::apiReturn(400, "", "参数有误");
        }
        $merBiz = new \Business\Finance\PayMerchant();
        if (strpos($method,'wepay') !== false) {
            $id  = I('post.id');//id查用于识别是否为小程序
            if(empty($id)){
                parent::apiReturn(400, "", "参数不能为空");
            }

            $PayMerchant  = new \Model\Finance\PayMerchant();
            $wepayRes     = $PayMerchant->getWePayMerChantConfigByMemberId($id,"use_env");
            $useEnv       = $wepayRes["use_env"];
            $isApplet     = $useEnv == 3 ? true : false;
            $wepayConf    = $merBiz->getMerchantConfig(\Business\Finance\PayMerchant::WEPAY, $merchantId, $subAppid='', $useEnv, $isApplet);

            $this->wxPayLib   = new WxPayLib('', $wepayConf);
        } else {
            $alipayConf      = $merBiz->getMerchantConfig(\Business\Finance\PayMerchant::ALIPAY, $merchantId);
            $this->aliPayLib   = new F2FPay(PFT_ALIPAY_F2FPAY_ID, self::ORDER_NOTIFY_URL);
            $this->appAuthToken = $alipayConf['app_auth_token'];
        }
        $data = $this->$method();
        parent::apiReturn($data['code'], $data['data'], $data['msg']);
    }
    public function notify()
    {
        $logData = file_get_contents('php://input');
        $get     = json_encode($_GET,JSON_UNESCAPED_UNICODE);
        $post    = json_encode($_POST,JSON_UNESCAPED_UNICODE);
        pft_log('pay/verify', 'input='.$logData . ';post='.$post . ';get='.$get);
        echo 'SUCCESS';
    }
    private function wepayQr()
    {
        //微信二维码支付
        $parameters = $this->wxPayLib->qrPay(1,'测试支付',
            $this->ordernum,self::ORDER_NOTIFY_URL);
        if ($parameters['return_code'] == 'SUCCESS') {
            $data = [
                'code' => 200,
                'data'   => ['url'=>$parameters['code_url']],
            ];
        } else {
            $data = [
                'code' => 0,
                'data' => [],
                'msg'    => $parameters['return_msg'],
            ];
        }
        return $data;
    }
    private function alipayQr()
    {
        $result   = $this->aliPayLib->qrpay($this->ordernum, 0.01, '测试支付', self::ORDER_NOTIFY_URL, '测试支付', $this->appAuthToken);
        $response = $result->alipay_trade_precreate_response;
        if ($response->code != 10000 || $response->msg != 'Success') {
            $this->apiReturn(204, [], '接口出错,请重试');
            $data = [
                'code' => 0,
                'data'   => [],
                'msg' => $response->sub_msg,
            ];
        } else {
            $data = [
                'code' => 200,
                'data'   => ['url'=>$response->qr_code],
            ];
        }
        return $data;
    }
    private function wepayMicropay()
    {
        $authCode = I('post.auth_code');
        $payResult  = $this->wxPayLib->micropay($authCode, '测试条码支付', 1, $this->ordernum);
        if ($payResult == false) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], "支付失败,订单号:{$payResult}");
        }
        parent::apiReturn(parent::CODE_SUCCESS, $payResult);
    }
    private function alipayMicropay()
    {
        $authCode = I('post.auth_code');
        $payResult  = $this->aliPayLib->micropay($authCode, '测试条码支付', 0.01, $this->ordernum, [], $this->appAuthToken,
            PFT_ALIPAY_PARTNER_ID);
        if ($payResult ->code != 10000) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, $payResult, "支付失败,订单号:{$this->ordernum}" );
        }
        parent::apiReturn(parent::CODE_SUCCESS, (array)$payResult);
    }
    private function wepayRefund()
    {
        $tradeNo = I('post.trade_no');
        $ordernum = I('post.ordernum');
        if (empty($ordernum) || strpos($ordernum, 'test') === false) {
            parent::apiReturn(401, [], '订单号需要test开头');
        }
        $data     = $this->wxPayLib->refund($ordernum, $tradeNo, 'refund_' . time(), 1, 1);
        if ($data["return_code"] == "FAIL") {
            parent::apiReturn(400, $data);
        }
        parent::apiReturn(200, $data);
    }
    private function alipayRefund()
    {
        $tradeNo = I('post.trade_no');
        $ordernum = I('post.ordernum');
        if (empty($ordernum) || strpos($ordernum, 'test') === false) {
            parent::apiReturn(401, [], '订单号需要test开头');
        }
        $data     = $this->aliPayLib->refund($tradeNo, 0.01, 'refund_'.time(), $this->appAuthToken);
        if ($data->alipay_trade_refund_response->code == 10000) {
            parent::apiReturn(200, (array)$data);
        }
        parent::apiReturn(400, (array)$data);
    }
}