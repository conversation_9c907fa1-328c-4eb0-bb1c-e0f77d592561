<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: Administrator
 * Date: 2020/5/1
 * Time: 15:29
 * 今日头条小程序支付api
 * https://microapp.bytedance.com/dev/cn/mini-app/develop/open-capacity/payment/mini-app-pay-plugin-reference/plug-in
 */

namespace Controller\pay;

use Business\Order\MergeOrder;
use Library\Business\Alipay\F2FPay;
use Library\Business\WePay\WxPayApi;
use Library\Business\WePay\WxPayLib;
use Library\Controller;
use Model\Order\OrderQuery;
use Model\TradeRecord\OnlineTrade;

class ToutiaoPay extends Controller
{
    private $merchantId = 0;
    public function order()
    {
        //平台订单号
        $outTradeNo = I('out_trade_no', 0, 'string');
        $memberId   = I('post.memberid');
        //订单主体说明
        $subject = mb_substr(trim(I('subject')), 0, 20, 'utf-8');
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }

        $mergeOrder = new MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
            try {
                $mergeOrder->handlerCombinePayLog($outTradeNo, '', OnlineTrade::CHANNEL_ALIPAY, $this->merchantId);
                $mergeOrder->handlerCombinePayLog($outTradeNo, '', OnlineTrade::CHANNEL_WEPAY,  $this->merchantId);
            } catch (\Exception $e) {
                parent::apiReturn(401, [], $e->getMessage());
            }
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string)$outTradeNo);
        }
        $ttOrderInfo = [];


        $h5Res = $this->wxH5Pay($totalFee, $subject, $outTradeNo);
        $alipayRes = $this->alipayAppPay($totalFee, $subject, $outTradeNo);
        $ts = time();
        $payConf = load_config('pft', 'pay');
        $ttConf  = $payConf['toutiao'];
        $ttOrderInfo['merchant_id'] = $ttConf['mchid'];
        $ttOrderInfo['app_id'] = $ttConf['appid'];
        $ttOrderInfo['sign_type'] = 'MD5';
        $ttOrderInfo['timestamp'] = $ts;
        $ttOrderInfo['version'] ='2.0';
        $ttOrderInfo['trade_type'] ='H5';
        $ttOrderInfo['product_code'] ='pay';
        $ttOrderInfo['payment_type'] ='direct';
        $ttOrderInfo['out_order_no'] = $outTradeNo . '_'.mt_rand(100, 999);
        $ttOrderInfo['uid'] = $memberId;
        $ttOrderInfo['total_amount'] = $totalFee;
        $ttOrderInfo['currency'] = 'CNY';
        $ttOrderInfo['subject'] = $subject;
        $ttOrderInfo['body'] = $subject;
        $ttOrderInfo['trade_time'] = $ts;
        $ttOrderInfo['valid_time'] = $ts+1800;// 30分钟有效
        $ttOrderInfo['notify_url'] = 'https://pay.12301.cc';//
        $ttOrderInfo['alipay_url'] = $alipayRes;//
        $ttOrderInfo['wx_url'] = $h5Res['mweb_url'];
        $ttOrderInfo['wx_type'] = 'MWEB';
        $ttOrderInfo['sign'] = md5($this->getSignContent($ttOrderInfo, 'UTF-8', $ttConf['appsecrect']));
        $ttOrderInfo['risk_info'] = json_encode(['ip'=>get_client_ip()], JSON_UNESCAPED_UNICODE);

        parent::apiReturn(200, $ttOrderInfo, 'success');
    }

    /**
     * @author: Guangpeng Chen
     * @date: 2020/5/1
     * @param $totalFee
     * @param $subject
     * @param $outTradeNo
     *
     * @return mixed
     * @throws \Library\Business\Alipay\AlipayException
     * @throws \Library\Exception
     */
    private function alipayAppPay($totalFee, $subject, $outTradeNo)
    {
        $alipay = new F2FPay(PFT_ALIPAY_F2FPAY_ID);
        // 只返回请求数据不实际请求的标识
        // $alipay->setSdkExecute(true);
        $res    = $alipay->appPay($outTradeNo, $totalFee / 100, $subject, Alipay::ORDER_NOTIFY_URL, $subject);
        if ($res['code'] == 200 && $res['alipay_url']) {
            $payMethod  = OnlineTrade::PAY_METHOD_ORDER;
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog($outTradeNo, $totalFee / 100, $subject, $subject, Alipay::SOURCE_T,
                $payMethod, '', $this->merchantId);
        }
        return $res['alipay_url'] ? $res['alipay_url'] : '';
    }

    private function wxH5Pay($totalFee, $subject, $outTradeNo)
    {
        $wxPayLib = new WxPayLib(PFT_WECHAT_H5PAY_APPID);
        $parameters = $wxPayLib->h5Pay(
            $totalFee,
            $subject,
            $outTradeNo,
            WxPay::ORDER_NOTIFY_URL,
            '微信H5支付',
            'orderpay'
        );
        if ($parameters['result_code'] != 'SUCCESS') {
            // 微信支付失败
        } else {
            //生成支付记录
            $payMethod  = OnlineTrade::PAY_METHOD_ORDER;
            $tradeModel = new OnlineTrade();
            $ret        = $tradeModel->addLog($outTradeNo, $totalFee / 100, $subject, $subject, WxPay::SOURCE_T,
                $payMethod, '', $this->merchantId);
        }
        return $parameters;
    }
    /**
     * 签名处理
     * @param array $params
     * @param string $charset
     * @param string $appSecret
     * @return string
     */
    public function getSignContent($params, $charset, $appSecret) {
        ksort($params);
        $stringToBeSigned = "";
        $i = 0;
        foreach ($params as $k => $v) {
            if (false === $this->checkEmpty($v) && "@" != substr($v, 0, 1)) {
                // 转换成目标字符集
                $v = $this->characet($v, $charset);
                if ($i == 0) {
                    $stringToBeSigned .= "$k" . "=" . "$v";
                } else {
                    $stringToBeSigned .= "&" . "$k" . "=" . "$v";
                }
                $i++;
            }
        }
        $stringToBeSigned = $stringToBeSigned.$appSecret;
        unset ($k, $v);
        return $stringToBeSigned;
    }

    /**
     * 校验$value是否非空
     * @param $value
     * @return  boolean;
     *  if not set ,return true;
     *  if is null , return true;
     **/
    public function checkEmpty($value) {
        if (!isset($value))
            return true;
        if ($value === null)
            return true;
        if (trim($value) === "")
            return true;
        return false;
    }

    /**
     * 转换字符集编码
     * @param $data
     * @param $targetCharset
     * @return string
     */
    public function characet($data, $targetCharset) {
        if (!empty($data)) {
            $fileType = "UTF-8";
            if (strcasecmp($fileType, $targetCharset) != 0) {
                $data = mb_convert_encoding($data, $targetCharset, $fileType);
            }
        }
        return $data;
    }

    /**
     * 微信端支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $ordernum = I('post.ordernum');
        //支付场景：1订单支付,2充值
        $model  = new OnlineTrade();
        $payLogWx = $model->getLog($ordernum, OnlineTrade::CHANNEL_ALIPAY);
        $payLogAli = $model->getLog($ordernum, OnlineTrade::CHANNEL_WEPAY);
        if (!$payLogWx && !$payLogAli) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
        }

        if ($payLogWx['status'] == 1 || $payLogAli['status'] == 1) {
            parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败', true);
    }
}