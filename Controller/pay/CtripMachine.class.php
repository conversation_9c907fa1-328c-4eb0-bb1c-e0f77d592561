<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: chenguangpeng
 * Date: 5/29-029
 * Time: 12:09
 *
 * 携程自助机支付
 */

namespace Controller\pay;


use Business\Ota\CtripTicketMachine;
use Library\Controller;

class CtripMachine extends Controller
{
    public function __construct()
    {
    }

    public function payResultCheck()
    {
        $pftOrderNo      = I('post.ordernum');
        $cancelAuthToken = I("post.token");
        //$cancelAuthTokenCheck = md5($pftOrderNo.date('YmdH'));
        //if ($cancelAuthToken != $cancelAuthTokenCheck) {
        //    parent::apiReturn(parent::CODE_INVALID_REQUEST,[], '参数校验失败', true);
        //}
        if (empty($pftOrderNo)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '请求参数错误');
        }
        $biz = new CtripTicketMachine();
        $res = $biz->changeOrderPay($pftOrderNo);
        parent::apiReturn($res['code'],[], $res['msg']);
    }

    /**
     * 前端取消携程订单
     * <AUTHOR> Chen
     * @date 2018-08-02
     *      post.ordernum：票付通订单号
     *      post.cancel_type：取消类型
     *      post.token：校验密码
     */
    public function cancelOrder()
    {
        $pftOrder   = I("post.ordernum");
        $cancelType = I("post.cancel_type");//1：未支付或支付超时取消，2：出票超时取消
        $cancelAuthToken = I("post.token");
        $cancelAuthTokenCheck = md5($pftOrder.date('Ymd'));
        pft_log('api/CtripTicketMachine', "客户端超时取消订单#" . json_encode($_POST));
        if ($cancelAuthToken != $cancelAuthTokenCheck) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST,[], '参数校验失败', true);
        }
        if (empty($pftOrder) ||!is_numeric($pftOrder)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST,[], '参数个数错误', true);
        }
        $biz = new \Library\Business\CtripTicketMachine();
        // 先查下一遍订单状态是否已经支付
        $ctripOrder = $biz->getCtripOrderInfo($pftOrder);
        if ($ctripOrder['code'] == 200 && in_array($ctripOrder['data']['orderstatus'], [1,2,4,5,7])) {
            // 如果取消失败，再调用一次支付结果检测，因为有可能因为在临界点的时候支付完成了。
            $this->payResultCheck();
        }
        $res = $biz->cancelCtripOrder($pftOrder, $cancelType);
        $msg = $res['msg'];
        if ($res['code']==200) {
            $model = new \Model\Ota\CtripTicketMachine();
            $model->cancelCtripOrder([$pftOrder]);
            if ($cancelType == 2) {
                $msg   = "订单出票失败，已经被系统自动取消";
            } else {
                $msg   = "订单超时未支付，已经被系统自动取消";
            }
        }
        parent::apiReturn($res['code'], $res['data'], $msg, true);
    }
}