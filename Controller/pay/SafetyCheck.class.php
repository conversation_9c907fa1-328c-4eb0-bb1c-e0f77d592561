<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: Administrator
 * Date: 2021/12/22
 * Time: 14:05
 */

namespace Controller\pay;

class SafetyCheck
{
    public $rateNum = 10;
    public $seconds = 60;
    public $domainList = [
        '12301.cc',
        'pft12301.cc',
        '12301dev.com',
        '12301.test',
        'pft12301.com',
        'servicewechat.com',// 微信小程序内发起的支付
        'gray.12301.cc',
    ];
    public function rateCheck($funcName, $params)
    {
        $ipAddr   = get_client_ip(1);
        $limitObj = new \Library\RateLimit\TokenBuket("pay:{$funcName}:{$ipAddr}", $this->rateNum, $this->seconds);
        $isLimit  = $limitObj->isLimit();
        if ($isLimit) {
            $logArr = [
                'action'    => "rateCheckFail",
                'params'    => ['api'=>$funcName, 'ip'=>long2ip($ipAddr), 'post'=>$_POST,'get'=>$_GET],
                'msg'       => "请求频率异常，设置阈值:{$this->rateNum}/{$this->seconds}",
            ];
            pft_log('online_pay_limit', json_encode($logArr));
            // todo:发送钉钉通知
            return true;
        }
    }
    private function isSubDomain($domain)
    {
        return substr_count($domain, '.')>1;
    }
    public function refererCheck($mustRf=false)
    {
        $rf = $_SERVER['HTTP_REFERER'];
        $logArr = [
            'action'    => "refererCheckFail",
            'params'    => ['post'=>$_POST,'get'=>$_GET,'user_agent'=> $_SERVER['HTTP_USER_AGENT']],
            'msg'       => '',
        ];
        if ($mustRf == true && empty($mustRf)) {
            $logArr['msg'] = 'REFERER必填';
            pft_log('online_pay_limit', json_encode($logArr,  JSON_UNESCAPED_UNICODE));
            return false;
        }
        if (!empty($rf)) {
            $parse = parse_url($rf);
            $host = $parse['host'];
            if (!$host) {
                $logArr['msg'] = 'parse_url FAIL,EMPTY HOST';
                pft_log('online_pay_limit', json_encode($logArr,  JSON_UNESCAPED_UNICODE));
                return false;
            }
            if ($this->isSubDomain($host)) {
                $host = substr($host, strpos($host, '.')+1);
            }
            if (!in_array($host, $this->domainList)) {
                $logArr['msg'] = "域名校验失败:{$host},{$rf}";
                pft_log('online_pay_limit', json_encode($logArr,  JSON_UNESCAPED_UNICODE));
                return false;
            }
        }
        return true;
    }

    /**
     * 必要的参数校验
     * @author: Guangpeng Chen
     * @date: 2021/12/22
     * @param $params
     *
     * @return bool
     */
    private function paramsCheck($params)
    {
        $mustCheck = [
            'domain',
            'from',
        ];
        $logArr = [
            'action'    => "paramsCheckFail",
            'params'    => ['post'=>$_POST,'get'=>$_GET],
            'msg'       => '',
        ];
        foreach ($mustCheck as $item) {
            if (!isset($params[$item])) {
                $logArr['msg'] = "参数[{$item}]不存在";
                pft_log('online_pay_limit', json_encode($logArr,  JSON_UNESCAPED_UNICODE));
                return false;
            }
        }
        $parse = parse_url($params['domain']);
        $domain = $parse['host'];
        if ($this->isSubDomain($domain)) {
            $domain = substr($domain, strpos($domain, '.')+1);
        }
        if (!in_array($domain, $this->domainList)) {
            $logArr['msg'] = "参数[domain],{$params['domain']},{$domain}不存在";
            pft_log('online_pay_limit', json_encode($logArr,  JSON_UNESCAPED_UNICODE));
            return false;
        }
        return true;
    }

    /**
     * 安全校验
     *
     * @author: Guangpeng Chen
     * @date: 2021/12/22
     * @param $funcName
     * @param $params
     * @return array
     */
    public function safeCheck($funcName, $params)
    {
        $this->rateCheck($funcName, $params);
        $res = $this->refererCheck();
        if (!$res) {
            return ['code'=>400, 'msg'=>'请求异常001'];
        }
        $res2 = $this->paramsCheck($params);
        if (!$res2) {
            return ['code'=>400, 'msg'=>'请求异常002'];
        }
        return ['code'=>200];
    }
}
// $host = parse_url($t)['host'];
// var_dump(substr($host, strpos($host, '.')+1));

