<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: guanpeng
 * Date: 2019/5/2
 * Time: 8:51
 */

namespace Controller\pay;


use Business\Order\Query as OrderQueryBiz;
use Library\Constants\DingTalkRobots;
use Library\Controller;
use Library\Tools\Helpers;
use Model\Order\OrderTools;
use Model\Order\SubOrderQuery\SubOrderTrack;

class OnlinePayBase extends Controller
{
    protected $paySource = 0;
    protected $orderModel;
    protected $orderInfo;
    protected function changeOrderPay($outTradeNo, $transaction_id, $total_fee, $is_member, $json_sell, $json_buy,
        $pay_to_pft, $payChannel, $payTerminal, $needVerify, $terminal,
        $payTrackOpId = 0, $annualCardOrderType = '')
    {
        try {
            $res = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, $payChannel,
                $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_p<PERSON>, $this->paySource, '', $payTerminal, $payTrackOpId, $annualCardOrderType);
        } catch (\SoapFault $e) {
            $retry = true;
            pft_log('order_pay/error', "订单支付失败:{$outTradeNo}，soap抛出异常:{$e->getCode()}->{$e->getMessage()}");
            Helpers::sendDingTalkGroupRobotMessage(
                "(微信)[{$outTradeNo}]订单支付失败;soap抛出异常:{$e->getCode()}->{$e->getMessage()}",
                "支付失败", Helpers::getServerIp(),
                DingTalkRobots::MYSQL_ERROR
            );
        }
        if (isset($retry) && $retry === true) {
            $res = parent::getSoap()->Change_Order_Pay($outTradeNo, $transaction_id, $payChannel,
                $total_fee, $is_member, $json_sell, $json_buy, 1, $pay_to_pft, $this->paySource, '', $payTerminal, $payTrackOpId, $annualCardOrderType);
        }
        $output = [];
        if ($res === 100) {
            $orderInfo = $this->_getOrder($outTradeNo);
            $args = [
                'ordernum' => $outTradeNo,
                'buyerId' => $orderInfo['member'], //购买人的ID
                'mobile' => $orderInfo['ordertel'],
                'aid' => $orderInfo['aid'],
            ];
            $jobId = \Library\Resque\Queue::push('notify', 'OrderNotify_Job', $args);
            if ($needVerify) {
                if (empty($this->orderInfo)) {
                }

                $query    = new OrderQueryBiz();
                $paymode  = $this->paySource == 11 ? 5 : 1;

                //验证操作人员ID取支付人员ID，没有的话取下单人ID
                $operMember = $payTrackOpId;
                if (!$operMember) {
                    $subTrack   = new SubOrderTrack();
                    $trackInfo  = $subTrack->getTrackListByOrderAndAction($outTradeNo, 0, 'oper_member');
                    $operMember = $trackInfo['oper_member'];
                }

                $output = $query->getOrderInfoForPrint($outTradeNo, $this->orderInfo, $total_fee, $paymode, $terminal, 0, $operMember);
            }

            if ($orderInfo['ordermode'] == 24) {
                $teamOrderModel = new \Model\Order\TeamOrderSearch();
                $mainOrderInfo = $teamOrderModel->getMainOrderInfoBySonOrder(strval($outTradeNo));
                if (empty($mainOrderInfo)) {
                    parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功, 未找到团单号');
                }
                $teamOrder = $mainOrderInfo['main_ordernum'];
                $sonOrder = $teamOrderModel->getSonOrderInfoByMainOrderNum($teamOrder);
                $orderArr = array_column($sonOrder, 'son_ordernum');
                $orderTool = new OrderTools();
                $orderData = $orderTool->getOrderInfo($orderArr, 'status,pay_status');

                $needUpdatePay = true;
                if ($orderData) {
                    foreach ($orderData as $value) {
                        if ($value['pay_status'] != 1 && $value['status'] != 3) {
                            $needUpdatePay = false;
                        }
                    }
                }

                if ($needUpdatePay) {
                    $payStatus = 1;
                } else {
                    $payStatus = 2;
                }

                if ($mainOrderInfo['status'] != 1 || $mainOrderInfo['pay_status'] != $payStatus) {
                    $res = $teamOrderModel->setTeamOrderByOrder($teamOrder, ['updatetime' => time(), 'status' => 1, 'pay_status' => $payStatus]);
                }
            }

            $msg = '支付成功';
            $code = parent::CODE_SUCCESS;
        } else {
            $msg = "收款成功但订单状态更新失败,订单号:{$outTradeNo}";
            $code = parent::CODE_INVALID_REQUEST;
        }
        return ['code' => $code, 'data' => $output, 'msg' => $msg, 'pay_res' => $res];
    }

    /**
     * 获取订单信息
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @param  string $ordernum
     * @return array
     */
    protected function _getOrder($ordernum)
    {
        $this->orderModel = new OrderTools('localhost');
        $orderInfo = $this->orderModel->getOrderInfo(
            $ordernum,
            'member,ordername,ordertel,aid,code,lid,tid,tnum,tprice,ordermode,paymode,salerid,ss.pay_status,ss.status',
            'de.aids,de.series'
        );
        if ($orderInfo['aids']) {
            // 转分销获取第二级分销商
            $tmp = explode(',', $orderInfo['aids']);
            $orderInfo['member'] = $tmp[1];
        }
        $this->orderInfo = $orderInfo;
        return $orderInfo;
    }
}