<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: Administrator
 * Date: 2021/5/29
 * Time: 10:40
 */

namespace Controller\pay;

use Business\Pay\PayBase;
use Library\Controller;
use Library\Exception;
use Model\Order\OrderQuery;
use Model\TradeRecord\OnlineTrade;
use Model\Order\OrderTools;

class ShengShiTongPay extends Controller
{
    //todo:定义支付渠道
    const TRACK_SOURCE = 58;
    const SOURCE_T     = 27;


    /**
     * 商家主扫
     * User: lanwanhui
     * Date: 2021/6/3
     */
    public function CodePay()
    {
        PayBase::setPayDailyRecord(8,'扫码请求',I('post.'));

        $outTradeNo   = I('post.ordernum');
        $authCode     = I('post.auth_code');
        $subject      = I('post.subject', '订单支付');
        $payTerminal  = I('post.terminal', 0, 'intval'); //支付的终端
        $payTrackOpId = I('post.pay_track_op_id', 0, 'intval');//支付的操作人
        $checkSource  = I('post.check_source', -1, 'intval');//验证来源
        $deviceKey    = I('post.device_id','');

        if (!$outTradeNo || !$authCode) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误，订单号或支付码必须传');
        }

        $codeLength = strlen($authCode);
        if (!in_array($codeLength,[28]) || !is_numeric($authCode)) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '非法的支付条码，请重新刷新支付码', true);
        }
        $payConfig = load_config('shengshitong_pay','pay');
        //获取下订单号
        $orderToolMdl  = new OrderTools();
        $orderInfo  = $orderToolMdl->getOrderInfo($outTradeNo,'ss.apply_did,ss.pay_status,ss.lid,ss.aid');
        if (empty($orderInfo)){
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '订单号有误');
        }elseif ($orderInfo['pay_status'] != 2){
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '订单已支付');
        }elseif (!isset($payConfig[$orderInfo['aid']])){
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '改供应商没有开通此服务');
        }
        $orderQuery = new OrderQuery('localhost');
        $totalFee   = $orderQuery->get_order_total_fee($outTradeNo);

        $OnlineTrade = new OnlineTrade();
        $result      = $OnlineTrade->addLog(
            $outTradeNo, $totalFee / 100, $subject, $subject, self::SOURCE_T,
            OnlineTrade::PAY_METHOD_ORDER, '', $orderInfo['aid']
        );

        if (!$result) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '支付失败,生成交易记录失败', true);
        }
        $systemName = '';
        if (ENV != 'LOCAL'){
            //调用条码支付接口
            try{
                $lib = new \Library\JsonRpc\PftRpcClient('ota_supplier_api');
                $requestConfigData = [
                    'aid'    => $orderInfo['aid'],
                ];
                $landConfigRes = $lib->call('OtherSystemApi/ShengShiTong/getSysIdentityByAid', [$requestConfigData], 'pay');
                if ($landConfigRes['code'] != 200){
                    $this->apiReturn(parent::CODE_INVALID_REQUEST, [], $landConfigRes['code'], true);
                }
                $systemName  = $landConfigRes['data']['sys_identity'];
                $requestData = [
                    'code'         => $authCode, // 28位二维码内容
                    'device'       => $deviceKey, // 设备号
                    'sys_identity' => $systemName, // 系统标识 ：景区名称首字母大写 + “-” + 景区系统名称 示例： SYGG-QMX
                    'orderno'      => $outTradeNo, // 订单编号
                    'goods_name'   => $subject, // 商品名称
                    'pay_fee'      => $totalFee, // 商品支付价格
                ];
                $res = $lib->call('OtherSystemApi/ShengShiTong/payByCredit', [$requestData], 'pay');
                PayBase::setPayDailyRecord(8,'扫码返回',$requestData,$res);
                if ($res['code'] != 200) {
                    $this->apiReturn(parent::CODE_INVALID_REQUEST, [], $res['msg'], true);
                }
                $resData = $res['data'];
                if ($resData['errcode'] != 0) {
                    $this->apiReturn(parent::CODE_INVALID_REQUEST, [], $resData['errmsg'], true);
                }
            }catch (Exception $e){
                $this->apiReturn(parent::CODE_INVALID_REQUEST, [], $e->getMessage(), true);
            }
        }else{
            $systemName = 'SYGG-QMX';
        }


        $payToPft = false;
        $jsonBuy = '';
        $jsonSell  = json_encode(['system' => $systemName]);
        $transactionId = 'SST_'.$outTradeNo;
        $options = [
            'buyer_info'   => $jsonBuy,
            'sell_info'    => $jsonSell,
            'pay_channel'  => self::TRACK_SOURCE,
            'pay_termianl' => $payTerminal,
            'oper'         => $payTrackOpId,
            'pay_source'   => $checkSource,
        ];

        $result = \Library\Tools\Helpers::payComplete($outTradeNo, $transactionId, OnlineTrade::CHANNEL_SHENGSHITONG,
            $totalFee, (int)$payToPft, $options);
        $data = [];
        if ($result['code'] == 200) {
            $successOrders = $result['data']['success_orders'];
            foreach ($successOrders as $item) {
                $data[] = [
                    'code'     => 200,
                    'msg'      => '支付成功',
                    'order_id' => $item,
                ];
            }
        }

        parent::apiReturn($result['code'], $data, $result['msg'], true);

    }
}
