<?php

namespace Controller\pay;

use Bean\Request\Pay\PayNotifyRequestBean;
use Business\JsonRpcApi\PayService\UnifiedPay;
use Business\Pay\UnifiedPayment;

class PaymentCenter extends PayBase
{
    /**
     * 定义支付渠道 易宝新版接口，票付通收款
     */
    const SOURCE_T = 41;

    const CLIENT_ID_PLATFORM = "platform_system";
    const IS_QR_JSAPI = 0; //公众号/小程序支付
    const IS_QR_CODE = 1; //二维码支付

    const PAY_TYPE_ALI = 1; //支付宝
    const PAY_TYPE_WECHAT = 2; //微信

    protected function rechargeHandler($outTradeNo, $subject, $totalFeeFen, $did) {
        $qrPay    = I('post.qr_pay', 0);
        if (isset($_POST['is_qr'])) {
            $qrPay    = I('post.is_qr', 0); // 0-微信公众号 1- 扫码支付 2-app支付
        }
        $useEnv = I('post.use_env', 0, 'intval'); // 0=公众号 1=小程序
        $payType  = I('post.pay_type', self::PAY_TYPE_WECHAT, 'intval');
        $frontUrl = I('post.success_url', '', 'strval'); // 支付成功后跳转地址

        //充值限制使用信用卡
        $limitCreditPay = true;

        $unifiedPayRpc = new UnifiedPay();
        switch ($qrPay) {
            case self::IS_QR_CODE:
                $result = $unifiedPayRpc->unifyQrPayRpcService(
                    $outTradeNo,
                    $subject,
                    $totalFeeFen,
                    1,
                    $payType,
                    self::CLIENT_ID_PLATFORM,
                    $this->getRechargeCallbackUrl(),
                    get_client_ip(),
                    $frontUrl,
                    ['pft_member_id' => $did, 'source' => 'platform_recharge'],
                    $limitCreditPay
                );
                if ($result['code'] != 200) {
                    return $this->returnData($result['code'], $result['msg'], $result['data']);
                }
                return $this->returnData(200, '', [
                    'qrUrl' => $result['data']['url'],
                    'outTradeNo' => $result['data']['out_trade_no'],
                ]);
            default:
                $appid = I('post.appid', PFT_WECHAT_APPID, 'strval');
                $openid   = I('post.openid', '', 'strval');
                if (empty($openid)) {
                    return $this->returnData(400, 'OPENID不能为空');
                }
                if ($useEnv == 1) {
                    $result = $unifiedPayRpc->miniJsApiRpcService(
                        $outTradeNo,
                        $subject,
                        $totalFeeFen,
                        1,
                        $payType,
                        self::CLIENT_ID_PLATFORM,
                        $this->getRechargeCallbackUrl(),
                        get_client_ip(),
                        $appid,
                        $openid,
                        '',
                        '',
                        '',
                        1800,
                        $limitCreditPay
                    );
                } else {
                    $result = $unifiedPayRpc->jsApiRpcService(
                        $outTradeNo,
                        $subject,
                        $totalFeeFen,
                        1,
                        $payType,
                        self::CLIENT_ID_PLATFORM,
                        $this->getRechargeCallbackUrl(),
                        get_client_ip(),
                        $appid,
                        $openid,
                        '',
                        $frontUrl,
                        '',
                        $limitCreditPay
                    );
                }
                if ($result['code'] != 200) {
                    return $this->returnData($result['code'], $result['msg'], $result['data']);
                }
                if ($payType == self::PAY_TYPE_ALI) {
                    return $this->returnData(200, '', [
                        'tradeNo' => $result['data']['tradeNo'],
                        'outTradeNo' => $result['data']['out_trade_no'],
                    ]);
                } else if ($payType == self::PAY_TYPE_WECHAT) {
                    return $this->returnData(200, '', [
                        'parameter' => $result['data']['payData'],
                        'outTradeNo' => $result['data']['outTradeNo'],
                    ]);
                } else {
                    return $this->returnData(400, '支付类型暂不支持', []);
                }
        }
    }

    /**
     * 平台账户充值回调通知
     */
    protected function rechargeNotifyHandler()
    {
        $request              = file_get_contents("php://input");
        if (empty($request)) {
            return $this->returnData(400, 'INVALID REQUEST');
        }
        $requestData          = json_decode($request, true);
        $payNotifyRequestBean = new PayNotifyRequestBean($requestData);
        $unifiedPaymentBiz    = new UnifiedPayment();
        $decryptRes           = $unifiedPaymentBiz->decryptNotifyData($payNotifyRequestBean->getContent()->toArrayWithMapping(null,
            1, true));
        \Business\Pay\PayBase::setPayDailyRecord(100,'支付中心充值回调通知 1',$request,$decryptRes);

        if (!$decryptRes) {
            return $this->returnData(400, 'FAIL');
        }
        return $this->returnData(200, '', $requestData['content']['biz_content']);
    }

    protected function getSourceT()
    {
        return self::SOURCE_T;
    }
}