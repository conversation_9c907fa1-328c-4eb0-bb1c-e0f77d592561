<?php
/**
 * 开户行管理
 * User: xwh
 * Date: 2020/07/22
 * Time: 10:27
 */

namespace Controller\Bank;

use Library\Controller;

class Bank extends Controller
{

    public function __construct()
    {
        $isLogin = $this->isLogin();
        if (!$isLogin) {
            $this->apiReturn(202, [], "请登录");
        }
    }

    /**
     * 创建银行
     * <AUTHOR>
     * @date 2020/7/22
     *
     * @return array
     */
    public function createBank()
    {
        $super = $this->isSuper();
        if (!$super) {
            $this->apiReturn(202, [], "非管理员");
        }
        $bankCode = I('code', 0, 'intval');
        $bankName = I('name', '', 'strval,trim');

        if (empty($bankCode) || empty($bankName) || !is_chinese($bankName)) {
            $this->apiReturn(203, [], "参数错误");
        }

        $AreaApi = new \Business\JavaApi\Bank\Bank();
        $result  = $AreaApi->createBankCode($bankCode, $bankName);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 获取银行+全国各省
     * <AUTHOR>
     * @date 2020/7/22
     *
     * @return array
     */
    public function getBankList()
    {
        $bankApi  = new \Business\JavaApi\Bank\Bank();
        $areaApi  = new \Business\JavaApi\Area\Area();
        $result   = $bankApi->bankCodes();
        $province = $areaApi->getList(null, null, 1);

        if ($result['code'] != 200 || $province['code'] != 200) {
            $this->apiReturn(500, [], '系统错误');
        }

        $this->apiReturn($result['code'], ['list' => $result['data'], 'province' => $province['data']], $result['msg']);
    }

    /**
     * 获取银行
     * <AUTHOR>
     * @date 2020/7/22
     *
     * @return array
     */
    public function getBanksPage()
    {
        $page = I('page', 1, 'intval');
        $size = I('size', 10, 'intval');
        $name = I('name', null, 'strval,trim');

        $bankApi = new \Business\JavaApi\Bank\Bank();
        $result  = $bankApi->banksPage($page, $size, $name);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取支行
     * <AUTHOR>
     * @date 2020/7/22
     *
     * @return array
     */
    public function getBranchList()
    {
        $code             = I('code', null, 'strval,trim');//开户银行的现代化支付行号
        $name             = I('name', null, 'strval,trim');//开户银行名称
        $key              = I('key', null, 'strval,trim');//开户银行的现代化支付行号,或开户行名称
        $bankId           = I('bank_id', null, 'intval');//所属银行代码
        $phone            = I('phone', null, 'string,trim');
        $cityAreaCode     = I('city_area_code', null, 'intval');
        $provinceAreaCode = I('province_area_code', null, 'intval');
        $page             = I('page', 1, 'intval');
        $size             = I('size', 10, 'intval');

        $AreaApi = new \Business\JavaApi\Bank\Bank();
        $result  = $AreaApi->getList($code, $name, $bankId, $phone, $cityAreaCode, $provinceAreaCode,$key, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 创建支行
     * <AUTHOR>
     * @date 2020/7/22
     *
     * @return array
     */
    public function createBranchBank()
    {
        $super = $this->isSuper();
        if (!$super) {
            $this->apiReturn(202, [], "非管理员");
        }
        $bankCode         = I('post.code', '', 'intval');
        $bankName         = I('post.name', '', 'strval,trim');
        $parentId         = I('post.parent_id', 0, 'intval');
        $phone            = I('post.phone', null, 'strval,trim');
        $cityAreaCode     = I('post.city_code', 0, 'intval');
        $provinceAreaCode = I('post.province_code', 0, 'intval');

        if (empty($bankCode) || empty($bankName) || !is_chinese($bankName) || !$parentId || !$cityAreaCode || !$provinceAreaCode) {
            $this->apiReturn(203, [], "参数错误");
        }
        $getInfo = $this->getLoginInfo();

        $AreaApi = new \Business\JavaApi\Bank\Bank();
        $result  = $AreaApi->create($bankCode, $bankName, $parentId, $phone, $cityAreaCode, $provinceAreaCode,
            $getInfo['memberID']);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 编辑支行
     * <AUTHOR>
     * @date 2020/7/22
     *
     * @return array
     */
    public function updateBranchBank()
    {
        $super = $this->isSuper();
        if (!$super) {
            $this->apiReturn(202, [], "非管理员");
        }
        $id               = I('id', '', 'intval');
        $bankCode         = I('code', '', 'strval,trim');
        $bankName         = I('name', '', 'strval,trim');
        $parentId         = I('parent_id', '', 'intval');
        $phone            = I('phone', null, 'strval,trim');
        $cityAreaCode     = I('city_code', '', 'intval');
        $provinceAreaCode = I('province_code', '', 'intval');

        if (!$id || !$bankCode || !$bankName || !is_chinese($bankName) || !$parentId || !$cityAreaCode || !$provinceAreaCode) {
            $this->apiReturn(203, [], "参数错误");
        }
        $getInfo = $this->getLoginInfo();

        $AreaApi = new \Business\JavaApi\Bank\Bank();
        $result  = $AreaApi->update($id, $bankCode, $bankName, $parentId, $phone, $cityAreaCode, $provinceAreaCode,
            $getInfo['memberID']);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 删除支行
     * <AUTHOR>
     * @date 2020/7/22
     *
     * @return array
     */
    public function delBranchBank()
    {
        $super = $this->isSuper();
        if (!$super) {
            $this->apiReturn(202, [], "非管理员");
        }
        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(203, [], "参数错误");
        }

        $AreaApi = new \Business\JavaApi\Bank\Bank();
        $result  = $AreaApi->deleteById($id);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 根据大行编码查询地区树,附带地区下面的支行数量
     * <AUTHOR>
     * @date 2020/7/22
     *
     * @return array
     */
    public function findAreaTreeWithBankCount()
    {
        $code = I('code', 0, 'intval');
        $page = I('page', 1, 'intval');
        $size = I('size', 10, 'intval');

        if (!$code) {
            $this->apiReturn(203, [], "参数错误");
        }

        $AreaApi = new \Business\JavaApi\Bank\Bank();
        $result  = $AreaApi->findAreaTreeWithBankCount($code, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据大行id获取大行
     * <AUTHOR>
     * @date 2020/7/22
     *
     * @return array
     */
    public function findBank()
    {
        $id = I('bank_id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(203, [], "参数错误");
        }

        $AreaApi = new \Business\JavaApi\Bank\Bank();
        $result  = $AreaApi->findBank($id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }



}