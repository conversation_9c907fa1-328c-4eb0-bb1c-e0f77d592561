<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 3/22-022
 * Time: 17:47
 * http://open.12301.test/pft_api.php?c=OnlineRefund&a=wx
 */
namespace Controller;

define('BASE_PAY_DIR', '/var/www/html/alipay');

use Business\CommodityCenter\Ticket;
use Business\Finance\PayMerchant;
use Business\JavaApi\Order\DouyinRefund;
use Business\JsonRpcApi\PayService\UnifiedRefund;
use Business\Lease\AutoBoxBiz;
use Business\Order\FzCityCard;
use Business\Order\Refund;
use Business\Order\RefundCenter;
use Library\Business\Alipay\F2FPay;
use Library\Business\CityCard\FzSdk;
use Library\Business\CMBPay\CmbPayApi;
use Library\Business\pingan_bank\PingAnSdk;
use Library\Business\WePay\WxPayApi;
use Library\Business\WePay\WxPayException;
use Library\Business\WePay\WxPayLib;
use Library\Constants\DingTalkRobots;
use Library\Controller;
use Library\Exception;
use Library\Resque\Queue;
use Library\Tools\Helpers;
use Model\CardSolution\TimingOrderDeposit;
use Model\Order\Payment;

class OnlineRefund extends Controller
{
    const REFUND_NOTIFY_URL = PAY_DOMAIN . 'r/pay_PingAnBank/refundNotify/';
    const SOURCE_T = null;

    const XHS_CHANNEL = 'XHS_ESCROW_PAY';
    const DY_CHANNEL = 'BYTE_GUARANTEE_PAY';
    private $data;
    private $miniProgramChannelMap = [
        44 => self::DY_CHANNEL,
        87 => self::XHS_CHANNEL,
    ];
    /**
     * @var $model \Model\TradeRecord\OnlineRefund
     */
    private $model;
    /**
     * @var $combineModel Payment
     */
    private $combineModel;

    private $online;
    private $log_id;
    private $uuOrderRefundPk;
    private $combineLog = null;
    public function index()
    {
        pft_log('refund_request', json_encode($_POST));
        $auth      = I('post.auth');
        $ordern    = I('post.ordernum');
        $noJournal = I('post.no_journal', 0, 'intval');
        //退款标记，默认是1，原路退款设置值为2
        $flagAuto    = I('post.auto_status', 1, 'intval');
        $isTradeFail = I('post.is_trade_fail', 0, 'intval');
        $payErrorCode = I('post.pay_error_code', 0, 'intval');
        $originPayNum = I('post.origin_pay_num', '', 'strval');
        $uuOrderRefundPk = I('post.uu_order_refund_pk', 0, 'intval');
        $this->uuOrderRefundPk = $uuOrderRefundPk;
        //$comp = md5(md5(.md5(strrev(I('post.ordernum')))));
        $comp = md5(md5($ordern) . md5(strrev($ordern)));
        //echo $auth;exit;
        if (empty($auth) || $auth != $comp) {
            pft_log('refund_request', '身份验证失败' . $auth . ':' . $comp);
            exit("身份验证失败" . $auth . ':' . $comp);
        }
        $this->log_id = I("post.log_id");
        if (!$this->log_id) {
            exit("退款记录ID不能为空");
        }

        $this->model  = new \Model\TradeRecord\OnlineRefund();
        $this->online = new \Model\TradeRecord\OnlineTrade();
        $data         = $this->model->GetRefundLog($this->log_id);
        if (!$data) {
            exit("退款记录不存在");
        }

        $combineLog = [];
        if (!empty($originPayNum)) {
            $mergeOrder = new \Business\Order\MergeOrder();
            if($mergeOrder->isCombineOrder($originPayNum)) {
                $this->combineModel = new Payment();
                $combineLog  = $this->combineModel->getCombileLogByOrdernum($data['ordernum']);
            }
        } else {
            $combineLog = $this->getCombineLog($data['ordernum'], $isTradeFail);
        }

        if ($combineLog) {
            $this->combineLog = $combineLog;
            if ($this->checkRefundMoneyEnough($data['ordernum'], $data['refund_money']) !== true) {
                pft_log('refund_request', "{$data['ordernum']},{$combineLog['tradeid']},订单退款金额不足，无法退款");
                exit("订单退款金额不足，无法退款");
            }
        }
        $this->data = (object) $data;
        $pay_mode   = I("post.pay_mode");
        $isToPayCenter = $this->checkRefundToPayCenter();
        $res        = $this->_selectPayMode($pay_mode,$isToPayCenter, $payErrorCode);
        if ($res['code'] == 200) {
            $this->model->UpdateRefundLogOk(
                $this->log_id,
                $noJournal,
                $flagAuto,
                isset($res['data']['batch_no']) ? $res['data']['batch_no'] : ''
            );
            if ($this->combineLog) {
                $this->_combikePayRefund();
            }
            parent::apiReturn(200);
        } else {
            $this->model->UpdateRefundMsg(
                $this->log_id,
                $res['msg'],
                $res['data']['batch_no'] ?? ''
            );
            $this->model->saveRefundFailRequest(json_encode($_POST), __FUNCTION__);
            parent::apiReturn(201);
        }
    }
    /**
     * 非平台订单退钱
     * <AUTHOR>
     * @date   2019-11-21
     * @param  $auth int 加密串
     * @param  $ordern string 订单号
     * @param  $no_journal int 是否加一条退款流水
     *
     * @return  array
     *
     */
    public function otherOrderRefund()
    {
        pft_log('refund_request_v1', json_encode($_POST));
        $auth      = I('post.auth');
        $ordern    = I('post.ordernum');
        $noJournal = I('post.no_journal', 0, 'intval'); //是否需要添加最后的退款流水
        $pay_mode  = I("post.pay_mode");
        $flagAuto  = I('post.auto_status', 1, 'intval');
        $itemCode  = I('post.item_code', 0);
        //此参数是为了记录的流水和之前的流水订单号一样
        $tradeOrder= I('post.trade_order','');
        $comp      = md5(md5($ordern) . md5(strrev($ordern)));
        //echo $auth;exit;
        if (empty($auth) || $auth != $comp) {
            pft_log('refund_request', '身份验证失败' . $auth . ':' . $comp);
            parent::apiReturn(204, [], "身份验证失败" . $auth . ':' . $comp);
        }
        $logId = I("post.log_id");
        if (!$logId) {
            parent::apiReturn(204, [], "退款记录ID不能为空");
        }
        $this->model  = new \Model\TradeRecord\OnlineRefund();
        $this->online = new \Model\TradeRecord\OnlineTrade();
        $orderRefund  = new Refund();
        $data         = $this->model->GetRefundLog($logId);
        if (!$data) {
            parent::apiReturn(204, [], "退款记录不存在");
        }
        $this->data = (object)$data;
        $isToPayCenter = $this->checkRefundToPayCenter();
        $res        = $this->_selectPayMode($pay_mode,$isToPayCenter);
        if ($res['code'] == 200) {
            $orderRefund->updateRefundResult(
                $logId,
                $data,
                $noJournal,
                $flagAuto,
                isset($res['data']['batch_no']) ? $res['data']['batch_no'] : '',
                $itemCode,
                $tradeOrder
            );
            parent::apiReturn(200);
        } else {
            $this->model->UpdateRefundMsg(
                $logId,
                $res['msg'],
                isset($res['data']['batch_no']) ? $res['data']['batch_no'] : ''
            );
            $this->model->saveRefundFailRequest(json_encode($_POST), __FUNCTION__);
            Helpers::sendDingTalkGroupRobotMessage($data['subject'] . '失败了，快去看', "在线退款失败", "订单号:{$ordern}",
                DingTalkRobots::BANK_JOURNAL);
            parent::apiReturn(201);
        }
        parent::apiReturn(200);
    }
    /**
     * 选择退款渠道
     * <AUTHOR>
     * @date   2019-11-21
     * @param  $pay_mode int 支付模式
     * @param  $pointPayCenter bool 指向支付中心
     *
     * @return  array
     *
     */
    private function _selectPayMode($pay_mode,$pointPayCenter = false, $payErrorCode = 0){
        $res        = [];
        if ($pointPayCenter){
            $res = $this->unifiedRefund($payErrorCode);
        }else {
            //如果是内网测试环境，直接把支付状态设置为已经支付
            if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL'])) {
                $res = ['code' => 200];
            } else {
                //真实退款
                switch ($pay_mode) {
                    case 1:
                        $res = $this->alipay();
                        break;
                    case 5:
                        $res = $this->wx();
                        break;
                    //由于民生“乐收银”兼容了支付宝和微信，所以这里统一用13和14这个两个值。
                    //由于招商银行聚合支付兼容了支付宝和微信，所以这里统一用13和14这个两个值。——2018-01-25 12:12:00
                    case 14:
                    case 16:
                        //$res = $this->cmbcWxPay();
                        // $res = $this->cmbPay();
                        break;
                    case 15:
                        //平安银行暂不开放原路退回
                        //$res = $this->pingan();
                        break;
                    case 25:
                        $res = $this->cmbNetPay();
                        break;
                    case 26:
                        $res = $this->chinaUmsPay();
                        break;
                    case 27:
                        $res = $this->chinaUmsPosPay();
                        break;
                    case 28:
                        $res = $this->swiftPassPay();
                        break;
                    case 29:
                        $res = $this->CBYeePayRefund();
                        break;
                    case 30:
                        $res = $this->UnionPayRefund();
                        break;
                    case 31:
                        $res = $this->AbchinaPayRefund();
                        break;
                    case 32:
                        $res = $this->CCBBankRefund();
                        break;
                    case 33:
                        $res = $this->fzcitycard();
                        break;
                    case 39:
                        $res = $this->JXNSHRefund();
                        break;
                    case 40:
                        $res = $this->unionPayAppletfund();
                        break;
                    case 41:
                        $res = $this->shengShiTongRefund();
                        break;
                    case 43:
                        $res = $this->qingHaiRefund();
                        break;
                    case 44:
                    case 87:
                        // 这里虽然是抖音担保支付退款，其实在退款流程中是请求的小程序，然后调用的抖音担保支付的退款，这期小红书担保支付，也是一样的模式，请求小程序然后再由小程序请求小红书，所以可以复用之前抖音担保支付的逻辑
                        $res = $this->douYinDanBaoRefund($pay_mode);
                        break;
                    case 45:
                    case 46:
                    case 48:
                    case 49:
                    case 50:
                    case 51:
                    case 52:
                    case 55:
                    case 56:
                    case 58:
                    case 59:
                    case 60:
                    case 61:
                    case 62:
                    case 63:
                    case 64:
                    case 66:
                    case 68:
                    case 69:
                    case 70:
                    case 71:
                    case 72:
                    case 75:
                    case 86:
                    case 88://西安银行
                    case 89://富滇银行
                    case 90://浙江农信
                        //收银台服务的支付统一退款方法,需要的paymode指向这个方法就好了
                        $res = $this->unifiedRefund($payErrorCode);
                        break;
                    case 73: //请求天时同程退款
                        $tradeId = !empty($this->combineLog['tradeid']) ? $this->combineLog['tradeid'] : $this->data->ordernum;
                        $res = AutoBoxBiz::getInstance()->onlineRefundToTs($tradeId, $this->data->refund_num);
                        break;
                    default:
                        exit('Unkonw paymode');
                        break;
                }
            }
        }
        return $res;
    }

    public function wx()
    {
        $appid = $this->data->appid ? $this->data->appid : PFT_WECHAT_APPID;
        if (!$this->data->ordernum) {
            pft_log('wepay/refund_error', '订单号为空');
            return ['code' => 400, 'msg' => "订单号为空"];
        }
        if (!$this->data->refund_money || !is_numeric($this->data->refund_money)) {
            pft_log('wepay/refund_error', $this->data->ordernum . '退款金额不能为空且必须为数字');
            return ['code' => 400, 'msg' => "退款金额不能为空且必须为数字"];
        }
        $out_trade_no  = $this->data->ordernum;
        $refund_fee    = $this->data->refund_money;
        $out_refund_no = uniqid('pftwepay');//商户退款单号，商户自定义，此处仅作举例
        //总金额需与订单号out_trade_no对应，demo中的所有订单的总金额为1分
        //获取总金额
        $trade_info = $this->model->GetTradeLog($out_trade_no);
        $total_fee  = $trade_info['total_fee'] * 100;
        // 如果是合并付款的订单，那么根据合并付款的记录汇总总的支付金额
        if ($this->combineLog) {
            $total_fee = 0;
            if($this->combineLog['tradeid']){
                $combineData = (new \Model\TradeRecord\OnlineTrade())->getLog($this->combineLog['tradeid'],1);
                if ($combineData){
                    $total_fee = float2int($combineData['total_fee']);
                }

            }

          //  $total_fee = $this->getCombineTotalFee();
        }
        $trade_no = $this->data->trade_no;

        $payConfig = [];
        if ($trade_info['merchant_id'] > 0) {
            $merBiz       = new PayMerchant();
            $sellerInfo   = $trade_info['seller_email'] ? @json_decode($trade_info['seller_email'],true) : [];
            $subAppid     = $sellerInfo['sub_appid'] ?: '';
            $payConfig = $merBiz->getMerchantConfig(PayMerchant::WEPAY, $trade_info['merchant_id'],$subAppid);
        }
        $wxPayLib = new WxPayLib($appid, $payConfig);
        try {
            $refundResult = $wxPayLib->refund($out_refund_no, $trade_no, $out_refund_no, $total_fee, $refund_fee);

            //商户根据实际情况设置相应的处理流程,此处仅作举例
            if ($refundResult["return_code"] == "FAIL") {
                if ($refundResult['return_msg'] == '订单已全额退款') {
                    return ['code' => 200,
                        'data'         => ['batch_no' => $out_refund_no],
                        'msg'          => '已全额退款'];
                }
                Helpers::sendDingTalkGroupRobotMessage("微信支付退款失败,总金额:{$total_fee},退款金额:{$refund_fee}分,交易号:$trade_no;错误描述:{$refundResult['return_msg']}", '在线支付退款失败', "订单号:$out_refund_no", DingTalkRobots::BANK_JOURNAL);
                pft_log('wepay/refund_error', "通信出错：return_msg:{$refundResult['return_msg']},err_code:{$refundResult['err_code']},err_code_des{$refundResult['err_code_des']}");
                return ['code' => 400, 'msg' => "通信出错：return_msg:{$refundResult['return_msg']},err_code:{$refundResult['err_code']},err_code_des{$refundResult['err_code_des']}"];
            } elseif ($refundResult["return_code"] == 'SUCCESS' && $refundResult['result_code'] == 'SUCCESS') {
                pft_log('wepay/refund_ok', "退款成功:退款记录ID[{$this->log_id}],订单号[{$out_trade_no}],总金额[{$total_fee}],退款金额[{$refund_fee}]");
                return [
                    'code' => 200,
                    'data' => ['batch_no' => $out_refund_no],
                    'msg'  => '退款成功',
                ];
            } elseif ($refundResult['err_code'] == 'SYSTEMERROR') {
                //系统超时等,请使用相同参数再次调用API。
                sleep(3);
                $refundResult2 = $wxPayLib->refund($out_refund_no, $trade_no, $out_refund_no, $total_fee, $refund_fee);
                pft_log('wepay/refund_error', $out_trade_no . "第一次结果:" . json_encode($refundResult) . ';间隔3秒后重试结果:' . json_encode($refundResult2));
                if ($refundResult2["return_code"] == 'SUCCESS' && $refundResult2['result_code'] == 'SUCCESS'){
                    return [
                        'code' => 200,
                        'data' => ['batch_no' => $out_refund_no],
                        'msg'  => '退款成功',
                    ];
                }
            } else {
                if ($refundResult['return_msg'] == '订单已全额退款') {
                    return [
                        'code' => 200,
                        'data' => ['batch_no' => $out_refund_no],
                        'msg'  => '已全额退款'];
                }
                pft_log('wepay/refund_error', $out_trade_no . ':' . json_encode($refundResult));
                $msg = "微信支付交易号:{$trade_no}，平台订单号:{$out_trade_no},总金额:{$total_fee},金额:{$refund_fee}分;\n错误描述:{$refundResult['err_code_des']}";
                if ($refundResult['err_code'] == 'NOTENOUGH') {
                    $msg .= "\n(需要财务人员登录到微信支付后台手工处理退款)";
                }
                $msg .= "\n商户号:" . WxPayApi::$mchid . ',特约商户号:' . WxPayApi::$sub_mchid;
                Helpers::sendDingTalkGroupRobotMessage($msg, '在线支付退款失败', "订单号:$out_refund_no", DingTalkRobots::BANK_JOURNAL);
                return ['code' => 400,
                    'data'         => ['batch_no' => $out_refund_no],
                    'msg'          => "退款失败," . $refundResult['err_code_des']];
            }
        } catch (WxPayException $exception) {
            $msg = "微信支付交易号:{$trade_no}，平台订单号:{$out_trade_no},总金额:{$total_fee},金额:{$refund_fee}分;\n错误描述:{$exception->getMessage()}";
            pft_log('wepay/refund_error', $msg);
            Helpers::sendDingTalkGroupRobotMessage($msg, '在线支付退款失败', "订单号:$out_refund_no", DingTalkRobots::BANK_JOURNAL);
        }

    }

    /**
     * 平安银行退款
     * <AUTHOR>
     *
     * @param  int
     * @param  string
     * @return
     */
    public function pingan()
    {
        if (!$this->data->ordernum) {
            pft_log('pingan/refund_error', '订单号为空');
            return ['code' => 400, 'msg' => '订单号为空'];
        }
        $PingAnSdk  = new PingAnSdk();
        $orderNum   = $this->data->ordernum;
        $objectName = I('post.refund_reson', '退款');
        $remark     = I('post.remark', '退款');
        $payInfo    = $this->online->getLog($orderNum, self::SOURCE_T);
        if ($payInfo) {
            $lastId = $this->model->addLog($orderNum, $payInfo['total_fee'], '退款', '退款', self::SOURCE_T);
            $result = $PingAnSdk->Refund($lastId, $payInfo['trade_no'], $payInfo['total_fee'], $objectName, $remark,
                self::REFUND_NOTIFY_URL);
            if ($result['status'] == '01') {
                pft_log('pingan/refund_ok', "退款成功，商户号：{$result['masterId']},平安订单号：{$result['orderId']},
                退款金额:{$result['refundAmt']}");
                return ['code' => 200, 'msg' => '退款成功'];
            } else {
                pft_log('pingan/refund_error', "退款失败:商户号：{$result['masterId']},平安订单号：{$result['orderId']},
                退款金额:{$result['refundAmt']},错误原因：{$result['errorMsg']}，错误代码：{$result['errorCode']}");
                return ['code' => 400, 'msg' => "退款失败," . $result['errorMsg']];
            }
        }
        return ['code' => 400, 'msg' => "数据不存在"];
    }

    /**
     * 银联商务支付退款
     * <AUTHOR>
     *
     * @return array
     */
    public function chinaUmsPay()
    {
        // TODO::对接银联商务
        $ordernum         = $this->data->ordernum;
        $refund_fee       = $this->data->refund_money;
        $trade_info       = $this->model->GetTradeLog($this->data->ordernum);
        $merBiz           = new \Business\Finance\PayMerchant();
        $chinaUmsConfigDb = $merBiz->getChinaUmsMerchantConfig($trade_info['merchant_id'], '', 0);
        $total_fee        = $trade_info['total_fee'] * 100;
        $out_request_no   = $this->data->ordernum . '_' . $this->getMillisecond(); // 标识一次退款请求，同一笔交易多次退款需要保证唯一，
        if ($this->combineLog) {
            $ordernum  = $this->combineLog['tradeid'];
            $total_fee = $this->getCombineTotalFee();
        }
        $client = new \Library\Tools\YarClient('pay');
        // $magId,$msgType,$orderId,$mid,$tid,$money,$sign
        $payResult = $client->call('Pay/ChinaUmsPay/chinaUmsRefund', [$chinaUmsConfigDb['msg_src_id'], $chinaUmsConfigDb['msg_src'],
            $ordernum, $chinaUmsConfigDb['merchant_id'], $chinaUmsConfigDb['terminal_code'], $refund_fee, $chinaUmsConfigDb['sign'], $out_request_no,$chinaUmsConfigDb['encryption_mode'] ?? 0]);

        if ($payResult['code'] == 200 && $payResult['res']['errCode'] == 'SUCCESS') {
            pft_log('chinaums/refund_ok', "银联商务退款成功:退款记录ID[{$this->log_id}],订单号[{$this->data->ordernum}],总金额[{$total_fee}],退款金额[{$refund_fee}],流水号[{$out_request_no}]");
            return ['code' => 200,
                'data'         => ['batch_no' => $out_request_no],
                'msg'          => '退款成功,' . $payResult['res']['errMsg']];
        }
        $msg = "银联商务退款失败#退款记录ID:{$this->log_id};订单号:{$this->data->ordernum};退款订单号:{$ordernum};金额:{$refund_fee}分{$out_request_no};失败信息:" . json_encode($payResult, JSON_UNESCAPED_UNICODE);
        pft_log('chinaums/refund_fail', $msg);
        $develop_mail = load_config('warning_mail_list');
        $kefu_mail    = load_config('kefu_mail_list');
        $mail_list    = implode('|', array_merge($develop_mail, $kefu_mail));
        Queue::push('notify', 'Mail_Job', [
            'body'    => str_replace(['#', ';'], '<br/>', $msg),
            'email'   => $mail_list,
            'subject' => '警告:退款失败',
        ]);

        return ['code' => 400,
            'data'         => ['batch_no' => $out_request_no],
            'msg'          => '退款失败:' . $payResult['res']['errMsg']];
    }
    /**
     * 银联商务POS支付退款
     * <AUTHOR>
     *
     * @return array
     */
    public function chinaUmsPosPay()
    {
        // TODO::对接银联商务
        $ordernum         = $this->data->ordernum;
        $refund_fee       = $this->data->refund_money;
        $trade_info       = $this->model->GetTradeLog($this->data->ordernum);
        $merBiz           = new \Business\Finance\PayMerchant();
        $chinaUmsConfigDb = $merBiz->getChinaUmsMerchantConfig($trade_info['merchant_id'], '', 1);
        $total_fee        = $trade_info['total_fee'] * 100;
        $out_request_no   = $this->data->ordernum . '_' . $this->getMillisecond(); // 标识一次退款请求，同一笔交易多次退款需要保证唯一，
        if ($this->combineLog) {
            $ordernum  = $this->combineLog['tradeid'];
            $total_fee = $this->getCombineTotalFee();
        }
        $client = new \Library\Tools\YarClient('pay');
        // $magId,$msgType,$orderId,$mid,$tid,$money,$sign
        $payResult = $client->call('Pay/ChinaUmsPay/chinaUmsPosRefund', [$trade_info['merchant_id'], $chinaUmsConfigDb['appid'],
            $chinaUmsConfigDb['appkey'], $chinaUmsConfigDb['merchant_id'], $chinaUmsConfigDb['terminal_code'], $ordernum, $trade_info['trade_no'], $out_request_no, $refund_fee]);
        if ($payResult['code'] == 200 && $payResult['res']['errCode'] == '00') {
            pft_log('chinaums/refund_ok', "银联商务Pos退款成功:退款记录ID[{$this->log_id}],订单号[{$this->data->ordernum}],总金额[{$total_fee}],退款金额[{$refund_fee}],流水号[{$out_request_no}]");
            return [
                'code' => 200,
                'data' => ['batch_no' => $out_request_no],
                'msg'  => '退款成功,' . $payResult['res']['errInfo'],
            ];
        }
        $msg = "银联商务Pos退款失败#退款记录ID:{$this->log_id};订单号:{$this->data->ordernum};退款订单号:{$ordernum};金额:{$refund_fee}分{$out_request_no};失败信息:" . json_encode($payResult, JSON_UNESCAPED_UNICODE);
        pft_log('chinaums/refund_fail', $msg);
        $develop_mail = load_config('warning_mail_list');
        $kefu_mail    = load_config('kefu_mail_list');
        $mail_list    = implode('|', array_merge($develop_mail, $kefu_mail));
        Queue::push('notify', 'Mail_Job', [
            'body'    => str_replace(['#', ';'], '<br/>', $msg),
            'email'   => $mail_list,
            'subject' => '警告:退款失败',
        ]);

        return ['code' => 400,
            'data'         => ['batch_no' => $out_request_no],
            'msg'          => '退款失败:' . $payResult['res']['errInfo']];
    }
    /**
     * 威富通支付退款
     * <AUTHOR>
     *
     * @return array
     */
    public function swiftPassPay()
    {
        // TODO::对接威富通
        $ordernum       = $this->data->ordernum;
        $refund_fee     = $this->data->refund_money;
        $trade_info     = $this->model->GetTradeLog($this->data->ordernum);
        $config         = load_config('swiftpass', 'pay');
        $total_fee      = $trade_info['total_fee'] * 100;
        $out_request_no = $this->data->ordernum . '_' . $this->getMillisecond(); // 标识一次退款请求，同一笔交易多次退款需要保证唯一，
        if ($this->combineLog) {
            $ordernum  = $this->combineLog['tradeid'];
            $total_fee = $this->getCombineTotalFee();
        }
        $client = new \Library\Tools\YarClient('pay');
        // $magId,$msgType,$orderId,$mid,$tid,$money,$sign
        $payResult = $client->call('Pay/SwiftPassPay/refund', [$config[$trade_info['merchant_id']]['appid'], $config[$trade_info['merchant_id']]['appkey'],
            $ordernum, $out_request_no, $total_fee, $refund_fee,$trade_info['trade_no']]);
        if ($payResult['code'] == 200 && isset($payResult['res']['result_code']) && $payResult['res']['result_code'] == '0') {
            pft_log('swiftpass/refund_ok', "银联商务退款成功:退款记录ID[{$this->log_id}],订单号[{$this->data->ordernum}],总金额[{$total_fee}],退款金额[{$refund_fee}],流水号[{$out_request_no}]");
            return [
                'code' => 200,
                'data' => ['batch_no' => $out_request_no],
                'msg'  => '退款成功,' . $payResult['res']['errMsg'],
            ];
        }
        $msg = "银联商务退款失败#退款记录ID:{$this->log_id};订单号:{$this->data->ordernum};退款订单号:{$ordernum};金额:{$refund_fee}分{$out_request_no};失败信息:" . json_encode($payResult, JSON_UNESCAPED_UNICODE);
        pft_log('swiftpass/refund_fail', $msg);
        $develop_mail = load_config('warning_mail_list');
        $kefu_mail    = load_config('kefu_mail_list');
        $mail_list    = implode('|', array_merge($develop_mail, $kefu_mail));
        Queue::push('notify', 'Mail_Job', [
            'body'    => str_replace(['#', ';'], '<br/>', $msg),
            'email'   => $mail_list,
            'subject' => '警告:退款失败',
        ]);

        return ['code' => 400,
            'data'         => ['batch_no' => $out_request_no],
            'msg'          => '退款失败:' . $payResult['res']['errMsg']];
    }
    /**
     * 招商银行一网通支付退款
     * @see http://openhome.cmbchina.com/pay/H5Pay/APIList/RefundAPI.aspx
     *
     * @return array
     */
    public function cmbNetPay()
    {
        //对接招行退款接口
        $skd            = new CmbPayApi();
        $ordernum       = $this->data->ordernum;
        $refund_fee     = $this->data->refund_money;
        $trade_info     = $this->model->GetTradeLog($this->data->ordernum);
        $total_fee      = $trade_info['total_fee'] * 100;
        $out_request_no = date('Ymd') . $this->data->ordernum; // 标识一次退款请求，同一笔交易多次退款需要保证唯一，
        $res            = $skd->refund($ordernum, $out_request_no, $refund_fee / 100);

        if ($res['rspData']['rspCode'] == 'SUC0000') {
            pft_log('cmb/refund_ok', "一网通退款成功:退款记录ID[{$this->log_id}],订单号[{$this->data->ordernum}],总金额[{$total_fee}],退款金额[{$refund_fee}],流水号[{$out_request_no}]");
            return ['code' => 200, 'msg' => '退款成功,' . $res['return_msg']];
        }
        $msg = "招商银行一网通退款失败#退款记录ID:{$this->log_id};订单号:{$this->data->ordernum};退款订单号:{$ordernum};金额:{$refund_fee}分{$out_request_no};失败信息:" . json_encode($res, JSON_UNESCAPED_UNICODE);
        pft_log('cmb/refund_fail', $msg);
        $develop_mail = load_config('warning_mail_list');
        $kefu_mail    = load_config('kefu_mail_list');
        $mail_list    = implode('|', array_merge($develop_mail, $kefu_mail));
        Queue::push('notify', 'Mail_Job', [
            'body'    => str_replace(['#', ';'], '<br/>', $msg),
            'email'   => $mail_list,
            'subject' => '警告:退款失败',
        ]);

        return ['code' => 400, 'msg' => '退款失败:' . $res['return_msg']];
    }

    /**
     * 易宝支付退款--paymode=29
     *
     * @author: guanpeng
     * @date: 2019/3/19
     */
    public function CBYeePayRefund()
    {
        $client   = new \Library\Tools\YarClient('pay');
        $ordernum = $this->data->ordernum;
        if ($this->combineLog) {
            $ordernum = $this->combineLog['tradeid'];
        }
        $refund_fee      = $this->data->refund_money;
        $amount          = number_format($refund_fee / 100, 2, '.', '');
        $refundRequestNo = md5($this->getMillisecond() . $this->data->ordernum); // 标识一次退款请求，同一笔交易多次退款需要保证唯一，
        $res             = $client->call('Pay/YeePayCloudBusiness/refund', [$ordernum, $refundRequestNo, $amount, PAY_DOMAIN . 'r/pay_CBYeePay/refundNotify', '']);
        //print_r($res);
        if ($res['code'] == 200 && $res['res']['data']['retCode'] == '0000') {
            return [
                'code' => 200,
                'data' => ['batch_no' => $refundRequestNo],
                'msg'  => '退款成功',
            ];
        }
        return [
            'code' => 400,
            'data' => ['batch_no' => $refundRequestNo],
            'msg'  => "退款失败,原因:{$res['res']['data']['result']['retMsg']}",
        ];
    }

    /**
     * 农行支付退款
     * <AUTHOR> Chen
     * @date 2019-04-24 21:32:37
     * @return array
     */
    public function AbchinaPayRefund()
    {
        $client   = new \Library\Tools\YarClient('pay');
        $ordernum = $this->data->ordernum;
        if ($this->combineLog) {
            $ordernum = $this->combineLog['tradeid'];
        }

        $tradeInfo       = $this->model->GetTradeLog($ordernum);
        $refundFee       = $this->data->refund_money;
        $refundRequestNo = $this->getMillisecond() . $this->data->ordernum; // 标识一次退款请求，同一笔交易多次退款需要保证唯一，
        $res             = $client->call('Pay/AbchinaPay/refund', [$tradeInfo['merchant_id'], $ordernum, $refundRequestNo, $refundFee]);

        if ($res['code'] == 200 && $res['res']['data']['ReturnCode'] == '0000') {
            return [
                'code' => 200,
                'data' => ['batch_no' => $refundRequestNo],
                'msg'  => '退款成功',
            ];
        }
        return [
            'code' => 400,
            'data' => ['batch_no' => $refundRequestNo],
            'msg'  => "退款失败,原因:{$res['res']['data']['msg']}",
        ];
    }

    /**
     * 农行支付退款
     * <AUTHOR> Chen
     * @date 2019-04-24 21:32:37
     * @return array
     */
    public function CCBBankRefund()
    {
        $client   = new \Library\Tools\YarClient('pay');
        $ordernum = $this->data->ordernum;
        if ($this->combineLog) {
            $ordernum = $this->combineLog['tradeid'];
        }

        $tradeInfo       = $this->model->GetTradeLog($ordernum);
        $refundFee       = $this->data->refund_money / 100;
        $res             = $client->call('Pay/CCB/refund', [$tradeInfo['merchant_id'], $ordernum, $refundFee]);
        if ($res['code'] == 200 && $res['res']['code'] == 200) {
            return [
                'code' => 200,
                'data' => [],
                'msg'  => '退款成功',
            ];
        }
        return [
            'code' => 400,
            'data' => [],
            'msg'  => "退款失败,原因:{$res['res']['data']['msg']}",
        ];
    }

    public function UnionPayRefund()
    {
        $ordernum       = $this->data->ordernum;
        $refund_fee     = $this->data->refund_money;
        $trade_info     = $this->model->GetTradeLog($this->data->ordernum);
        $total_fee      = $trade_info['total_fee'] * 100;
        $out_request_no = $this->data->ordernum . 'CLOUD' . $this->getMillisecond(); // 标识一次退款请求，同一笔交易多次退款需要保证唯一，
        if ($this->combineLog) {
            $ordernum  = $this->combineLog['tradeid'];
            $ordernum  = str_replace('CMB-', 'CLOUD', $ordernum);
            $total_fee = $this->getCombineTotalFee();
        }
        $client = new \Library\Tools\YarClient('pay');
        // $magId,$msgType,$orderId,$mid,$tid,$money,$sign
        $payResult = $client->call('Pay/UnionPay/refundUnionPay', [$out_request_no, $trade_info['seller_email'], $trade_info['trade_no'], $refund_fee]);
        if ($payResult['res']['data']['respCode'] == '00') {
            pft_log('union/refund_ok', "云闪付退款成功:退款记录ID[{$this->log_id}],订单号[{$this->data->ordernum}],总金额[{$total_fee}],退款金额[{$refund_fee}],流水号[{$out_request_no}]");
            return [
                'code' => 200,
                'data' => ['batch_no' => $out_request_no],
                'msg'  => '退款成功,' . $payResult['res']['data']['errMsg'],
            ];
        }
        $msg = "银联商务退款失败#退款记录ID:{$this->log_id};订单号:{$this->data->ordernum};退款订单号:{$ordernum};金额:{$refund_fee}分{$out_request_no};失败信息:" . json_encode($payResult, JSON_UNESCAPED_UNICODE);
        pft_log('union/refund_fail', $msg);
        $develop_mail = load_config('warning_mail_list');
        $kefu_mail    = load_config('kefu_mail_list');
        $mail_list    = implode('|', array_merge($develop_mail, $kefu_mail));
        Queue::push('notify', 'Mail_Job', [
            'body'    => str_replace(['#', ';'], '<br/>', $msg),
            'email'   => $mail_list,
            'subject' => '警告:退款失败',
        ]);

        return ['code' => 400,
            'data'         => ['batch_no' => $out_request_no],
            'msg'          => '退款失败:' . $payResult['res']['data']['respMsg']];
    }

    /**
     * 支付宝刷卡支付退款
     */
    public function alipay()
    {
        $trade_info   = $this->model->GetTradeLog($this->data->ordernum);
        $appAuthToken = null;
        $isvAppId     = PFT_ALIPAY_F2FPAY_ID;
        $subAppId     = '';
        if ($trade_info['merchant_id'] > 0) {
            if (strpos($trade_info['seller_email'], 'auth_app_id')!==false) {
                $_merchantInfo = json_decode($trade_info['seller_email'], true);
                $subAppId = $_merchantInfo['auth_app_id'];
            }
            $merBiz       = new PayMerchant();
            $payConfig    = $merBiz->getMerchantConfig(PayMerchant::ALIPAY, $trade_info['merchant_id'], $subAppId);
            $appAuthToken = $payConfig['app_auth_token'];
            $isvAppId     = $payConfig['appid']??$isvAppId;
        }

        $f2f            = new F2FPay($isvAppId);
        $refund_fee     = number_format($this->data->refund_money / 100, 2, '.', ''); //元为单位
        $out_request_no = date('YmdHis') . $this->data->ordernum . mt_rand(1000, 9999); // 标识一次退款请求，同一笔交易多次退款需要保证唯一，如需部分退款，则此参数必传。
        pft_log("alipay/refund_info", "before:appid={$this->data->appid},trade_no={$this->data->trade_no}, refund_fee=$refund_fee, ordernum={$this->data->ordernum},out_request_no={$out_request_no}");
        //多次退款需要提交原支付订单的商户订单号和设置不同的退款单号
        for ($i=0; $i<5; $i++) {
            $refundResult = $f2f->refund($this->data->trade_no, $refund_fee, $out_request_no, $appAuthToken);
            if ($refundResult->alipay_trade_refund_response->code == 10000) {
                pft_log("alipay/refund_ok", "退款成功:退款记录ID[{$this->log_id}],订单号[{$this->data->ordernum}],总金额[{$this->data->total_fee}],退款金额[{$this->data->refund_fee}]");
                return [
                    'code' => 200,
                    'data' => ['batch_no' => $out_request_no],
                    'msg'  => '退款成功',
                ];
            }
            //"code": "20000","msg": "Service Currently Unavailable","sub_code": "aop.ACQ.SYSTEM_ERROR","sub_msg": "系统异常",
            if ($refundResult->alipay_trade_refund_response->code != 20000) {
                break;
            }
            sleep(1);
        }

        $msg = "支付宝退款失败:退款记录ID[{$this->log_id}],订单号[{$this->data->ordernum}],金额:{$refund_fee}元;失败信息" . json_encode($refundResult, JSON_UNESCAPED_UNICODE);
        Helpers::sendDingTalkGroupRobotMessage($msg, '在线支付退款失败', "订单号:{$this->data->ordernum}", DingTalkRobots::BANK_JOURNAL);
        pft_log("alipay/refund_error", $msg);
        return [
            'code' => 400,
            'data' => ['batch_no' => $out_request_no],
            'msg'  => "退款失败,原因:{$refundResult->alipay_trade_refund_response->sub_msg}",
        ];
    }


    /**
     * 江西农商行退款
     *
     * @author: lanwanhui
     * @date: 2021/6/4
     */
    public function JXNSHRefund()
    {
        $client   = new \Library\Tools\YarClient('pay');
        $ordernum = $this->data->ordernum;
        if ($this->combineLog) {
            $ordernum = $this->combineLog['tradeid'];
        }
        $refund_fee      = $this->data->refund_money;
        $refundRequestNo = uniqid('pftjxnsh'); // 标识一次退款请求，同一笔交易多次退款需要保证唯一，

        $jxnshConfig      = load_config('jxnsh_pay','pay');//江西农商行
        if (!isset($jxnshConfig[$this->data->aid]) || empty($jxnshConfig[$this->data->aid])) {
            pft_log('jxnsh/micropay_refund', $ordernum.'退款失败,原因:未找到供应商配置');
            return [
                'code' => 400,
                'data' => ['batch_no' => $refundRequestNo],
                'msg'  => "退款失败,原因:未找到供应商配置",
            ];
        }

        $params = [
            'mid'           => $jxnshConfig[$this->data->aid],
            'ord_no'        => $ordernum,
            'out_no'        => '',
            'refund_out_no' => $refundRequestNo,
            'refund_amount' => $refund_fee,
        ];
        $res = $client->call('Pay/JxnshPay/refund',$params);

        $logData = [
            'params'     => $params,
            'refundRes'  => $res,
        ];
        pft_log('jxnsh/micropay_refund', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

        if ($res['code'] == 200 && $res['res']['data']['status'] == '1') {
            return [
                'code' => 200,
                'data' => ['batch_no' => $refundRequestNo],
                'msg'  => '退款成功',
            ];
        }
        return [
            'code' => 400,
            'data' => ['batch_no' => $refundRequestNo],
            'msg'  => "退款失败,原因:{$res['res']['msg']}",
        ];
    }

    /**
     * 银联会员卡退款
     *
     * @author: lanwanhui
     * @date: 2021/7/13
     */
    public function unionPayAppletfund()
    {
        try {

            $ordernum = $this->data->ordernum;
            if ($this->combineLog) {
                $ordernum = $this->combineLog['tradeid'];
            }

            $refund_fee      = $this->data->refund_money;

            //获取配置
            $payConfig      = load_config('unionpayapplet_pay','pay');
            if (!isset($payConfig[$this->data->aid]) || empty($payConfig[$this->data->aid])) {
                return [
                    'code' => 400,
                    'data' => [],
                    'msg'  => "退款失败,原因:未找到供应商配置",
                ];
            }

            $client = new \Library\JsonRpc\PftRpcClient('ota_supplier_api');

            $params = [
                // 第三方订单号
                'thirdPartyOrderNo' => $ordernum,
            ];

            $res = $client->call('OtherSystemApi/UnionPayMini/cancel', [$params], 'plat');

            $logData = [
                'params'     => $params,
                'refundRes'  => $res,
            ];
            pft_log('unionpayapplet/cancel', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

            if (!isset($res['code']) || $res['code'] != 200) {
                $msg = empty($res['msg']) ? '退款失败' : $res['msg'];
                return [
                    'code' => 400,
                    'data' => [],
                    'msg'  => "退款失败,原因:{$msg}",
                ];
            }

            if (isset($res['data']['respCode']) && $res['data']['respCode'] === '0000') {
                return [
                    'code' => 200,
                    'data' => [],
                    'msg'  => '退款成功',
                ];
            } else {
                $msg = empty($res['data']['respDesc']) ? '退款失败了' : $res['data']['respDesc'];
                return [
                    'code' => 400,
                    'data' => [],
                    'msg'  => "退款失败,原因:{$msg}",
                ];
            }

        }catch (\Exception $e) {

            $logData = [
                'e_msg'      => $e->getMessage(),
                'e_line'     => $e->getLine(),
                'e_file'     => $e->getFile(),
                'ordernum'   => $ordernum,
            ];

            pft_log('unionpayapplet/cancel', json_encode($logData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

            return [
                'code' => 400,
                'data' => [],
                'msg'  => "退款失败,原因:{$e->getMessage()}",
            ];

        }

    }

    /**
     * 青海银行退款
     *
     * @author: lanwanhui
     * @date: 2021/10/15
     */
    public function qingHaiRefund()
    {
        $ordernum = $this->data->ordernum;
        if ($this->combineLog) {
            $ordernum = $this->combineLog['tradeid'];
        }

        $refund_fee  = $this->data->refund_money;
        $merchantId  = $this->data->aid;
        $money       = number_format($refund_fee / 100, 2, '.', '');

        $pay = new \Controller\pay\QinghaiBank();

        $rs = $pay->refund($merchantId, $ordernum, $money);

        if ($rs['code'] == 200) {
            return [
                'code' => 200,
                'data' => [],
                'msg'  => '退款成功',
            ];
        } else {
            $msg = empty($res['msg']) ? '退款失败' : $res['msg'];
            return [
                'code' => 400,
                'data' => [],
                'msg'  => "退款失败,原因:{$msg}",
            ];
        }

    }


    /**
     * 盛世通支付退款--paymode=41
     *
     * @author: linchen
     * @date: 2021/9/07
     */
    private function shengShiTongRefund()
    {
        $ordernum = $this->data->ordernum;
        if ($this->combineLog) {
            $ordernum = $this->combineLog['tradeid'];
        }
        $trade_info   = $this->model->GetTradeLog($this->data->ordernum);
        $refundRequestNo = md5($this->getMillisecond() . $this->data->ordernum); // 标识一次退款请求，同一笔交易多次退款需要保证唯一，
        $sellInfo        = $trade_info['seller_email'] ? json_decode($trade_info['seller_email'],true) : '';
        $systemName      = $sellInfo['system'] ?? '';
        try {
            $requestData = [
                'orderno'       => $ordernum,
                'refund_amount' => $this->data->refund_money,
                'sys_identity'  => $systemName,
            ];
            $lib         = new \Library\JsonRpc\PftRpcClient('ota_supplier_api');
            $res         = $lib->call('OtherSystemApi/ShengShiTong/creditPayRefund', [$requestData], 'refund');
        } catch (\Exception $e) {
            return [
                'code' => 400,
                'data' => ['batch_no' => $refundRequestNo],
                'msg'  => "退款失败,原因:{$e->getMessage()}",
            ];
        }
        //print_r($res);
        if ($res['code'] == 200 && $res['data']['errcode'] == 0) {
            return [
                'code' => 200,
                'data' => ['batch_no' => $refundRequestNo],
                'msg'  => '退款成功',
            ];
        }

        return [
            'code' => 400,
            'data' => ['batch_no' => $refundRequestNo],
            'msg'  => "退款失败,原因:{$res['res']['data']['result']['retMsg']}",
        ];
    }
    private function getCombineLog($ordernum, $isTradeFail = false)
    {
        $this->combineModel = new Payment();
        $map                = $isTradeFail ? 0 : ['in', [1, 2]];
        $combineLog         = $this->combineModel->getCombileLogByOrdernum($ordernum, $map);
        pft_log('refund_request', "getCombineLog=" . $this->combineModel->_sql());
        return $combineLog;
    }

    private function getCombineTotalFee()
    {
        $data = $this->combineModel->getSumMoneyByTradeId($this->combineLog['tradeid']);
        pft_log('refund_request', "getCombineTotalFee=" . $this->combineModel->_sql());
        return $data;
    }

    private function _combikePayRefund()
    {
        $res = $this->combineModel->changeCombinePayStatus($this->combineLog['tradeid'], [$this->combineLog['ordernum']], Payment::REFUND_STATUS);
        pft_log('refund_request', "_combikePayRefund=" . $this->combineModel->_sql());
        return $res;
    }
    /**
     * 福州市民卡退款
     * <AUTHOR>
     * @dateTime 2018-02-01T15:00:13+0800
     * @return   [type]                   [返回是否成功]
     */
    public function fzcitycard()
    {
        $fzSdk      = new FzSdk();
        $fzBiz      = new FzCityCard();
        $ordertime  = $fzBiz->getOrderTime($this->data->ordernum);
        $account    = $fzBiz->getProductAccount($this->data->aid);
        $trade_info = $this->model->GetTradeLog($this->data->ordernum);
        $total_fee  = $trade_info['total_fee'] * 100;
        $ret        = $fzSdk->refund($this->log_id, $this->data->ordernum, $total_fee, $ordertime, $account);
        if ($ret['code'] == 200) {
            return ['code' => 200, 'msg' => '退款成功'];
        } else {
            pft_log("fzcitycard/refund_error", $ret['respMsg']);
            return ['code' => 400, 'msg' => "退款失败,原因:{$ret['respMsg']}"];
        }
    }

    /**
     * 检测订单金额是否足够退款
     *
     * @param string ordernum     订单号
     * @param int    $refundMoney 退款金额，单位分
     * @return bool
     */
    public function checkRefundMoneyEnough($ordernum, $refundMoney)
    {
        //获取订单已退款的金额，然后和订单总金额比较
        $toolModel = new \Model\Order\OrderTools('slave');
        $orderInfo = $toolModel->getOrderInfo(strval($ordernum),'ss.ordernum,ss.tid','concat_id',false,'current_num,origin_num,sale_price');
        //$orderInfo = $toolModel->getOrderApplyInfo(strval($ordernum), 'origin_num,sale_price');
        if (!$orderInfo) {
            return false;
        }
        $commondityTicketBiz = new Ticket();
        $ticketInfo = $commondityTicketBiz->queryTicketInfoById($orderInfo['tid']);
        $timeOrderDeposit = 0;
        if ($ticketInfo['land']['p_type'] == 'K'){
            //获取下有没有押金
            $timeOrderDepositMdl = new TimingOrderDeposit();
            $timeOrderDepositInfo = $timeOrderDepositMdl->getTimeOrderDepositInfo($ordernum,'handle_status,money,gather_way');
            if ($timeOrderDepositInfo && in_array($timeOrderDepositInfo['handle_status'],[1,2])){
                if ($timeOrderDepositInfo['gather_way'] == 1){
                    $timeOrderDeposit += $timeOrderDepositInfo['money'];
                }
            }
        }
        if ($orderInfo['concat_id'] && $orderInfo['ordernum'] == $orderInfo['concat_id']){  //这种才代表联票主票
            //查出下面所有子联票
            $linkOrders = $toolModel->getLinkSubOrder($orderInfo['concat_id']);
            if (!$linkOrders){
                return false;
            }
            $linkApplyInfo = $toolModel->getOrderApplyInfo($linkOrders,'origin_num,sale_price');
            $totalMoney = 0;
            foreach ($linkApplyInfo as $key => $value){
                $totalMoney += $value['origin_num'] * $value['sale_price'];
            }
        }else{
            // /transaction/orderModifyService/orderNumModify这个接口会更新 current_num,如果是减票是不会修改的
            $totalMoney = $orderInfo['current_num'] * $orderInfo['sale_price'] + $timeOrderDeposit;
        }


        //已退款金额
        $refundModel = new \Model\TradeRecord\OnlineRefund();
        $refundList  = $refundModel->getRefundPays(strval($ordernum), 1);
        $hasFefundMoney = 0;
        if ($refundList) {
            $hasFefundMoney = array_sum(array_column($refundList, 'refund_money'));
        }

        if (($hasFefundMoney + $refundMoney) > $totalMoney) {
            return false;
        } 

        return true;
    }
    /**
     * 获取毫秒时间戳
     *
     * @return bool
     */
    private function getMillisecond()
    {
        list($msec, $sec) = explode(' ', microtime());
        $msectime = (float) sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
        return $msectime;
    }

    /**
     * 抖音担保交易支付退款--paymode=44
     *
     * @author: xwh
     * @date: 2021/10/25
     */
    private function douYinDanBaoRefund($payMode)
    {
        $douyinApi       = new DouyinRefund();
        $ordernum = $this->data->ordernum;
        if ($this->combineLog) {
            $ordernum = $this->combineLog['tradeid'];
        }
        $refundRequestNo = md5($this->getMillisecond() . $this->data->ordernum); // 标识一次退款请求，同一笔交易多次退款需要保证唯一
        try {
            $channel = array_key_exists($payMode, $this->miniProgramChannelMap) ? $this->miniProgramChannelMap[$payMode] : self::XHS_CHANNEL;
            $res = $douyinApi->douyinRefund($ordernum, intval($this->data->refund_money), $refundRequestNo, $channel);
        } catch (\Exception $e) {
            return [
                'code' => 400,
                'data' => ['batch_no' => $refundRequestNo],
                'msg'  => "退款失败,原因:{$e->getMessage()}",
            ];
        }

        if ($res['code'] == 200) {
            return [
                'code' => 200,
                'data' => ['batch_no' => $refundRequestNo],
                'msg'  => '退款成功',
            ];
        }

        return [
            'code' => 400,
            'data' => ['batch_no' => $refundRequestNo],
            'msg'  => "退款失败,原因:{$res['msg']}",
        ];
    }
    /**
     * 退款（走收银台的）
     *
     * @author: linchen
     * @date: 2022/01/17
     */
    private function unifiedRefund($payErrorCode = 0){
        $ordernum = $this->data->ordernum;


        /*
        if(empty($this->combineLog)) {
            $trade_info   = $this->model->GetTradeLog($ordernum);
        } else {
            $trade_info   = $this->model->GetTradeLog($this->combineLog['tradeid']);
            //合并付款没有发起支付,用子订单号支付了,所以合并付款订单号找不到
            if (empty($trade_info)) {
                $trade_info       = $this->model->GetTradeLog($ordernum);
            } else {
                $child_trade_info       = $this->model->GetTradeLog($ordernum);
                //合并付款调支付接口了但是没支付成功,又用子订单去支付
                if ($trade_info['pay_id'] != $child_trade_info['pay_id']) {
                    $trade_info = $child_trade_info;
                } else {
                    $ordernum = $this->combineLog['tradeid'];
                }
            }
        }
        */

        if ($this->combineLog) {
            $ordernum = $this->combineLog['tradeid'];
        }
        $payStatus = null;
        // 异常退款的情况下，支付状态不等于1，需要取消过滤
        if ($payErrorCode == 102) {
            $payStatus = null;
        }
        $trade_info = (new \Model\TradeRecord\OnlineTrade())->getLogByOrdernumAndSourceT($ordernum, $this->data->sourceT, $payStatus);
        //$trade_info   = $this->model->GetTradeLog($ordernum);
        pft_log('refund_test_lan',json_encode($trade_info));
        $refundRequestNo = RefundCenter::GenerateRefundNo();//md5($this->getMillisecond() . $this->data->ordernum); // 标识一次退款请求，同一笔交易多次退款需要保证唯一，
        $refundReason = $this->data->subject ? $this->data->subject : '订单退款';
        $payServiceRpc = new UnifiedRefund();
        $refundRes = $payServiceRpc->refundRpcService($ordernum,$trade_info['merchant_id'],$refundRequestNo,$trade_info['trade_no'],$this->data->refund_money,$trade_info['payid'], $this->data->ordernum, 'platform_system',$refundReason, $payErrorCode);
        if ($refundRes['code'] != 200){
            return [
                'code' => 400,
                'data' => ['batch_no' => $refundRequestNo],
                'msg'  => "退款失败,原因:{$refundRes['msg']}",
            ];
        }
        return [
            'code' => 200,
            'data' => ['batch_no' => $refundRequestNo],
            'msg'  => '退款成功',
        ];
    }

    /**
     * 校验下退款是否要去支付中心退款
     * @return bool
     */
    public function checkRefundToPayCenter(){

        $ordernum = $this->data->ordernum;
        if ($this->combineLog) {
            $ordernum = $this->combineLog['tradeid'];
        }
        $trade_info = (new \Model\TradeRecord\OnlineTrade())->getLogByOrdernumAndSourceT($ordernum, $this->data->sourceT);
        if (!empty($trade_info['payid'])) {
            return true;
        }
        return false;
    }
}
