<?php

namespace Controller\AnnualCard;

use Library\Controller;

class Product extends Controller
{

    private $_sid        = null;
    private $_memberId   = null;
    private $_productBiz = null;
    private $_ticketBiz  = null;

    public function __construct()
    {
        $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
        $this->_ticketBiz  = new \Business\AnnualCard\TicketService();
        $this->_productBiz = new \Business\AnnualCard\ProductBiz();
    }

    /**
     * 查询年卡特权产品列表
     * <AUTHOR>  Li
     * @date  2022-06-21
     */
    public function getAnnualLandList()
    {
        $page        = I('post.page', 1, 'intval');
        $size        = I('post.size', 10, 'intval');
        $landTitle   = I('post.land_title', '', 'strval');
        $ticketTitle = I('post.ticket_title', '', 'strval');

        $result = $this->_ticketBiz->queryDistributionLandTitlePage($this->_sid, $page, $size, $landTitle, $ticketTitle);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 查询年卡特权产品列表
     * <AUTHOR>  Li
     * @date  2022-06-21
     */
    public function getAnnualTicketList()
    {
        $lid         = I('post.lid', 0, 'intval');
        $page        = I('post.page', 1, 'intval');
        $size        = I('post.size', 10, 'intval');
        $landTitle   = I('post.land_title', '', 'strval');
        $ticketTitle = I('post.ticket_title', '', 'strval');

        if (!$lid) {
            $this->apiReturn(203, [], '景区id不能为空');
        }

        $result = $this->_ticketBiz->queryDistributionTicketTitlePage($this->_sid, $lid, $page, $size, $landTitle, $ticketTitle);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 查询指定景区下的年卡门票列表
     * <AUTHOR>  Li
     * @date  2022-10-08
     */
    public function getAnnualCardLandTicketList()
    {
        $lid   = I('post.lid', 0, 'intval');
        $tid   = I('post.tid', 0, 'intval');
        $page  = I('post.page', 1, 'intval');
        $size  = I('post.size', 10, 'intval');
        $title = I('post.title', '', 'strval');
        $type  = I('post.type', 1, 'intval');   //获取类型  1续费 2升级

        if (!$lid) {
            $this->apiReturn(203, [], '景区id不能为空');
        }

        if (!$tid) {
            $this->apiReturn(203, [], '当前门票id异常');
        }

        if (!in_array($type, [1, 2])) {
            $this->apiReturn(203, [], '传入类型异常，请检查');
        }

        $result = $this->_productBiz->getAnnualCardLandTicketList($lid, $this->_sid, $tid, $type, $title, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取指定套餐关联操作产品列表
     * <AUTHOR>  Li
     * @date  2022-10-13
     */
    public function getPackageRelationSaleList()
    {
        $cardId = I('post.card_id', 0, 'intval');
        $aid    = I('post.aid', 0, 'intval');
        $type   = I('post.type', 1, 'intval');   //获取类型  1续费 2升级

        if (!$cardId || !$aid) {
            $this->apiReturn(203, [], '年卡数据异常');
        }

        if (!in_array($type, [1, 2])) {
            $this->apiReturn(203, [], '传入类型异常，请检查');
        }

        $result = $this->_productBiz->getPackageRelationSaleList($cardId, $aid, $this->_sid, $type);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 用于年卡批量删除
     * 查询票属性的特权包含删除的票属性
     * <AUTHOR>
     * @Date 2023/9/11 18:56
     */

    public function getCardPrivilegesByTidAndStateArrPage()
    {
        $ticketId = I('post.ticket_id', 0, 'intval');
        if (!$ticketId) {
            $this->apiReturn(203, [], '门票id必传');
        }
        $result = $this->_productBiz->getCardPrivilegesByTidAndStateArr($ticketId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }
}
