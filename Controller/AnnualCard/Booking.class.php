<?php

namespace Controller\AnnualCard;

use Library\Controller;
use Business\AnnualCard\Booking as annualCardBooking;

class Booking extends Controller
{

    private $_bookingBiz = null;
    private $_sid        = null;
    private $_memberId   = null;

    public function __construct()
    {
        $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        $this->_bookingBiz = new annualCardBooking();
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 获取供应商对应的预约规则列表
     * <AUTHOR>  Li
     * @date  2020-11-05
     *
     * @return array
     */
    public function getPrivilegeByCardTid()
    {
        $cardTid = I('post.card_tid', 0, 'intval');

        if (!$cardTid) {
            $this->apiReturn(203, [], '参数出错');
        }

        $result = $this->_bookingBiz->getPrivilegeByCardTid($this->_sid, $cardTid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取供应商对应的预约规则列表
     * <AUTHOR>  Li
     * @date  2020-11-05
     *
     * @return array
     */
    public function getBookingRuleList()
    {
        $page = I('post.page', 1, 'intval');
        $size = I('post.size', 10, 'intval');

        $result = $this->_bookingBiz->getBookingRuleList($this->_sid, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取供应商对应的预约规则列表
     * <AUTHOR>  Li
     * @date  2020-11-05
     *
     * @return array
     */
    public function getBookingRuleById()
    {
        $ruleId = I('post.rule_id', 0, 'intval');

        if (!$ruleId) {
            $this->apiReturn(203, [], '参数出错');
        }

        $result = $this->_bookingBiz->getBookingRuleById($this->_sid, $ruleId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 修改规则状态
     * <AUTHOR>  Li
     * @date  2020-11-05
     *
     * @return array
     */
    public function setBookingRuleStatus()
    {
        $ruleId     = I('post.rule_id', 0, 'intval');
        $state      = I('post.state', 1, 'intval');
        $operatorId = I('post.operator_id', 0, 'intval');

        if (!$ruleId) {
            $this->apiReturn(203, [], '参数出错');
        }

        $result = $this->_bookingBiz->setBookingRuleStatus($this->_sid, $ruleId, $state, $operatorId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 创建特权预约规则
     * <AUTHOR>  Li
     * @date  2020-11-05
     *
     * @return array
     */
    public function addBookingRule()
    {
        $title      = I('post.title', '', 'strval');      //规则名称
        $cardlid    = I('post.card_pid', 0, 'intval');    //年卡景区ID
        $cardTid    = I('post.card_tid', 0, 'intval');    //年卡门票ID
        $priMap     = I('post.pri_map/a');    //年卡特权开关配置数组 [{"1012898":1,"1012899":0}]
        $operatorId = I('post.operator_id', 0, 'intval');//操作员id

        if (!$title || !$cardlid || !$cardTid || !$priMap) {
            $this->apiReturn(203, [], '参数出错');
        }

        //因为传过来的是景区id  所以需要通过景区id获取产品id
        $javaApi = new \Business\CommodityCenter\Ticket();
        $pidArr  = $javaApi->queryProductIdsByTicketIds([$cardTid]);
        $cardPid  = $pidArr[0];

        $result = $this->_bookingBiz->addBookingRule($this->_sid, $title, $cardPid, $cardTid, $priMap, $operatorId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 编辑特权预约规则
     * <AUTHOR>  Li
     * @date  2020-11-05
     *
     * @return array
     */
    public function editBookingRule()
    {
        $ruleId     = I('post.rule_id', 1, 'intval');
        $title      = I('post.title', '', 'strval');
        $cardPid    = I('post.card_pid', 0, 'intval');
        $cardTid    = I('post.card_tid', 0, 'intval');
        $priMap     = I('post.pri_map/a');
        $state      = I('post.state', 1, 'intval');
        $operatorId = I('post.operator_id', 0, 'intval');

        if (!$ruleId || !$title || !$cardPid || !$cardTid || !$priMap) {
            $this->apiReturn(203, [], '参数出错');
        }

        if (is_array($priMap)) {
            $priMap = json_encode($priMap);
        }

        $result = $this->_bookingBiz->editBookingRule($ruleId, $this->_sid, $title, $cardPid, $cardTid, $priMap, $state,
            $operatorId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除特权预约规则
     * <AUTHOR>  Li
     * @date  2020-11-05
     *
     * @return  array
     */
    public function delBookingRule()
    {
        $ruleId     = I('post.rule_id', 0, 'intval');
        $operatorId = I('post.operator_id', 0, 'intval');

        if (!$ruleId) {
            $this->apiReturn(203, [], '参数出错');
        }

        $result = $this->_bookingBiz->delBookingRule($ruleId, $this->_sid, $operatorId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取供应商配置的日历库存
     * <AUTHOR>  Li
     * @date  2020-11-06
     *
     * @return  array
     */
    public function getPrivilegeStorageList()
    {
        $page   = I('post.page', 1, 'intval');
        $size   = I('post.size', 15, 'intval');
        $priLid = I('post.pri_lid', 0, 'intval');  //年卡特权景区id
        $priTid = I('post.pri_tid', 0, 'intval');  //年卡特权门票id

        $result = $this->_bookingBiz->getPrivilegeStorageList($this->_sid, $page, $size, $priLid, $priTid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取供应商配置的日历库存
     * <AUTHOR>  Li
     * @date  2020-11-06
     *
     * @return  array
     */
    public function getBookingStorageList()
    {
        $pid       = I('post.pid', 0, 'intval');
        $tid       = I('post.tid', 0, 'intval');
        $startData = I('post.start_date', '', 'strval');  //日期格式
        $endData   = I('post.end_date', '', 'strval');    //日期格式

        if (!$pid || !$tid || !$startData || !$endData) {
            $this->apiReturn(203, [], '参数出错');
        }
        $startTime = strtotime($startData);
        $endTime   = strtotime($endData);

        $result = $this->_bookingBiz->getBookingStorageList($this->_sid, $pid, $tid, $startTime, $endTime);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 创建日历库存
     * <AUTHOR>  Li
     * @date  2020-11-05
     *
     * @return  array
     */
    public function addBookingStorage()
    {
        $pid        = I('post.pid', 0, 'intval');
        $ticketId   = I('post.tid', 0, 'intval');
        $ptype      = I('post.ptype', 0, 'intval');
        $operatorId = I('post.operator_id', 0, 'intval');
        $storageArr = I('post.storage_map/a', '',
            'strval');    //日历价格 [{"play_date": 20201106,"storage": 100,"state": 1}]

        if (!$storageArr || !$pid || !$ticketId) {
            $this->apiReturn(203, [], '参数出错');
        }

        $result = $this->_bookingBiz->addBookingStorage($this->_sid, $pid, $ticketId, $ptype, $storageArr, $operatorId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 编辑日历库存
     * <AUTHOR>  Li
     * @date  2020-11-05
     *
     * @return  array
     */
    public function editBookingStorage()
    {
        $pid        = I('post.pid', 0, 'intval');
        $ticketId   = I('post.tid', 0, 'intval');
        $ptype      = I('post.ptype', 0, 'intval');
        $operatorId = I('post.operator_id', 0, 'intval');
        $storageArr = I('post.storage_map/a', '',
            'strval');    //日历价格 [{"play_date": 20201106,"storage": 100,"state": 1}]

        if (!$storageArr || !$pid || !$ticketId) {
            $this->apiReturn(203, [], '参数出错');
        }

        $result = $this->_bookingBiz->editBookingStorage($this->_sid, $pid, $ticketId, $ptype, $storageArr,
            $operatorId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}
