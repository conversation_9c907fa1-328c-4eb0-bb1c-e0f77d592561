<?php
/**
 * 年卡物理卡相关操作
 */

namespace Controller\AnnualCard;

use Business\AnnualCard\PhysicsOperateService;
use Library\Controller;
use Throwable;

class PhysicsOperate extends Controller
{
    private $sid;
    private $memberId;
    private $biz;

    public function __construct()
    {
        if (ENV == 'LOCAL') {
            $loginInfo = [
                'sid' => 6970,
                'memberID' => 6970,
                'sdtype' => 0,
                'dtype' => 0,
                'customerId' => 6074,
            ];
        } else {
            $loginInfo = $this->getLoginInfo();
        }
        $this->sid = $loginInfo['sid'];
        $this->memberId = $loginInfo['memberID'];
        $this->biz = PhysicsOperateService::getInstance();
    }
    public function bulkEntityCardAssociations()
    {
        try {
            $pid = I('post.pid', 0, 'intval');  //年卡pid
            if (empty($pid)) {
                throw new \Exception('请选择产品');
            }
            if (!isset($_FILES['excel'])) {
                throw new \Exception('请上传excel');
            }
            //取得扩展名
            $filetype = strtolower(strrchr($_FILES['excel']['name'], "."));
            if (!in_array($filetype, ['.xlsx', '.xls'])) {
                throw new \Exception('文件格式不正确,请下载模板文件进行填写');
            }
            $params = [
                'sid' => $this->sid,
                'member_id' => $this->memberId,
                'pid' => $pid,
                'file' => $_FILES['excel'],
            ];
            $res = $this->biz->importForBulkEntityCardAssociations($params);
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } catch (Throwable $e) {
            $this->apiReturn(400, [], $e->getMessage());
        }
    }

    //Excel 模板下载接口
    public function downloadExcel()
    {
        $this->biz->downloadExcel();
    }
}