<?php

namespace Controller\AnnualCard;

use Model\Product\AnnualCard;

trait AnnualArchiveTrait
{
    public function getArchiveAnnualCardTable($avalidEnd): string
    {
        $avalidEnd = empty($avalidEnd) ? 0 : $avalidEnd;
        $avalidEnd = is_numeric($avalidEnd) ? intval($avalidEnd) : strtotime($avalidEnd);
        $timeType = load_config('time_type', 'annualSearch');
        $table = AnnualCard::ANNUAL_CARD_TABLE;
        if ($avalidEnd > 0) {
            $table = $timeType[1][0] >= date('Y-m-d', $avalidEnd) ? AnnualCard::ANNUAL_CARD_TABLE_HISTORY : $table;
        }
        return $table;
    }
}