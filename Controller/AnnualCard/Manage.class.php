<?php
/**
 * 年卡管理
 * <AUTHOR>
 * @date   2022/10/8
 */

namespace Controller\AnnualCard;

use Business\AnnualCard\AnnualCardManage;
use Library\Controller;
use Business\AnnualCard\CardManage as CardManageBiz;
use Business\AnnualCard\Package as PackageBiz;
use Library\SimpleExcel;
use Model\Product\AnnualCard;

class Manage extends Controller
{
    use AnnualArchiveTrait;
    private $_sid        = null;
    private $_memberId   = null;
    private $_manageBiz  = null;
    private $_annualManageBiz  = null;

    public function __construct()
    {
        $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
        $this->_manageBiz  = new CardManageBiz();
        $this->_annualManageBiz  = new AnnualCardManage();
    }

    /**
     * 获取年卡会员列表
     * <AUTHOR>
     * @date   2022/10/8
     *
     */
    public function getCardList()
    {
        $from            = I('post.if_fid', 0, 'intval');//
        $identify        = I('post.identify', '');//搜索内容
        $searchType      = I('post.search_type', -1, 'intval');   //搜索类型 0物理卡 1 实体卡 2虚拟卡 3手机号 4身份证 5姓名
        $pageSize        = I('post.page_size', 10, 'intval');//页数
        $page            = I('post.page', 1, 'intval');//页码
        $status          = I('post.status', 5, 'intval');//年卡状态 0未激活 1正常 2禁用 3仓库中 4挂失 5除了仓库中的所有 6年卡延期中的全部 （未激活、已激活）
        $saleStart       = I('post.sale_start', '', 'strval');   //售出开始时间
        $saleEnd         = I('post.sale_end', '', 'strval');     //售出结束时间
        $activeStart     = I('post.active_start', '', 'strval');//激活开始时间
        $activeEnd       = I('post.active_end', '', 'strval');   //激活结束时间
        $distributorId   = I('post.distributor_id', 0, 'intval');//分销商id
        $lid             = I('post.lid', 0, 'intval');           //景区id
        $tid             = I('post.tid', 0, 'intval');          //年卡产品门票id
        $validStartStart = I('post.valid_start_start', '', 'strval');//有效期开始的开始时间
        $validStartEnd   = I('post.valid_start_end', '', 'strval');//有效期开始的结束时间
        $validEndStart   = I('post.valid_end_start', '', 'strval');   //有效期结束的开始时间
        $validEndEnd     = I('post.valid_end_end', '', 'strval');   //有效期结束的结束时间
        $isUseCnt        = I('post.is_use_cnt', 0, 'intval');   //默认不展示
        $packState       = I('post.pack_status', 0, 'intval');   //套餐状态：1未生效、2生效中、3已过期
        //归档数据查询 归档维度是pft_annual_card的有效期截止时间avalid_end
        $timeType = I('post.time_type', 0, 'intval');
        if ($from && !$identify) {
            $this->apiReturn(203, [], '请输入搜索参数');
        }

        $identify = trim($identify, ' ');

        if ((empty($validStartStart) && !empty($validStartEnd)) || (!empty($validStartStart) && empty($validStartEnd))) {
            $this->apiReturn(203, [], '时间查询字段缺失');
        }

        //有效期开始时间范围
        $validStart = [
            'start' => $validStartStart,
            'end'   => $validStartEnd,
        ];

        if ((empty($validEndStart) && !empty($validEndEnd)) || (!empty($validEndStart) && empty($validEndEnd))) {
            $this->apiReturn(203, [], '时间查询字段缺失');
        }
        //归档数据相关参数
        $table = $timeType == 0 ? AnnualCard::ANNUAL_CARD_TABLE : AnnualCard::ANNUAL_CARD_TABLE_HISTORY;
        //有效期结束时间范围
        $validEnd = [
            'start' => $validEndStart,
            'end'   => $validEndEnd,
        ];

        $pid = [];
        if ($lid) {
            //通过传入景区id 获取到pid
            $javaApi = new \Business\CommodityCenter\LandF();
            $pid  = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([], [$lid], [], 'pid', true);
        }
        if ($tid) {
            //通过传入门票id 获取到pid
            $ticketModel = new \Model\Product\Ticket();
            $pid         = $ticketModel->getTicketInfoById($tid, 'pid');
            $pid         = [$pid['pid']];
        }

        $sid = $this->_sid;

        $isSelf = !$from;

        $result = $this->_manageBiz->queryCardList($sid, $isSelf, $page, $pageSize, $identify, $searchType,
            $status, $lid, $tid, $pid, $saleStart, $saleEnd, $activeStart, $activeEnd, $validStart, $validEnd,
            $distributorId, $packState, (boolean)$isUseCnt, $table);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 年卡归档日期下拉列表
     * <AUTHOR>
     * @Date 2023/8/29 9:37
     * @return void
     */
    public function getCardArchiveConfig()
    {
        $result = $this->_manageBiz->getCardArchiveConfig();
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取卡详情
     * <AUTHOR>
     * @date   2022/10/10
     *
     */
    public function getCardDetail()
    {
        $cardId    = I('post.card_id', 0, 'intval');//卡id
        $virtualNo = I('post.virtual_no', '');
        $memberId  = I('post.member_id', 0, 'intval');
        $avalidEnd  = I('post.avalid_end', '', 'strval');
        if (!$virtualNo || !$cardId) {
            $this->apiReturn(204, [], '参数错误');
        }
        $sid = $this->_sid;
        //确定数据是否归档
        $table = $this->getArchiveAnnualCardTable($avalidEnd);
        $result = $this->_manageBiz->getCardDetail($sid, $memberId, $virtualNo, $cardId, $table);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取年卡归档后的套餐特权
     * <AUTHOR>
     * @Date 2023/8/28 18:03
     * @return void
     */
    public function getArchivePackageAndPrivilege()
    {
        $cardId = I('post.card_id', 0, 'intval');//卡id
        if (!$cardId) {
            $this->apiReturn(204, [], '参数错误');
        }
        $result = $this->_manageBiz->getArchivePackageAndPrivilege($cardId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取卡套餐对应的特权信息
     */
    public function getCardPrivilegeInfo()
    {
        $cardId = I('post.card_id', 0, 'intval');

        if (!$cardId) {
            $this->apiReturn(203, [], '年卡id缺失');
            return;
        }

        $cardInfo = (new \Model\Product\AnnualCard())->getCardInfoById($cardId, 'id,sid,pid');
        if (empty($cardInfo)) {
            $this->apiReturn(203, [], '年卡数据异常');
            return;
        }

        $packagePrivilegeRes = (new \Business\AnnualCard\Package())->getPeriodPackagePrivilegeInfo($cardInfo['id'], $cardInfo['sid']);
        if ($packagePrivilegeRes['code'] != 200) {
            $this->apiReturn(203, [], $packagePrivilegeRes['msg']);
            return;
        }

        $this->apiReturn(200, $packagePrivilegeRes['data'], '获取成功');
    }

    /**
     * 通过年卡门票产品分页获取年卡特权
     * <AUTHOR>
     * @date   2022/10/13
     *
     */
    public function getHistoryOrder()
    {
        $virtualNo = I('post.virtual_no', '', 'strval');
        $cardId    = I('post.card_id', 0, 'intval');//卡id
        $memberid  = 0;
        if (!$virtualNo || !$cardId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $page   = I('post.page', 1, 'intval');
        $size   = I('post.size', 10, 'intval');
        $start  = I('post.order_start', '', 'strval');
        $end    = I('post.order_end', '', 'strval');

        $checkStart = date("Y-m-d", strtotime($start . " 00:00:00")) == $start;
        $start      = $checkStart ? strtotime($start . " 00:00:00") : 0;

        $checkEnd = date("Y-m-d", strtotime($end . " 23:59:59")) == $end;
        $end      = $checkEnd ? strtotime($end . " 23:59:59") : 0;

        $orderTime = [
            'start' => $start,
            'end'   => $end,
        ];

        $sid = $this->_sid;

        $result = $this->_manageBiz->getHistoryOrderList($sid, $memberid, $virtualNo, $cardId, $orderTime, $page, $size);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取自供应续费卡列表
     */
    public function getRenewalCardListForSupply()
    {
        $identify = I('identify', '');//搜索内容
        $pageSize = I('page_size', 10, 'intval');//页数
        $page     = I('page', 1, 'intval');//页码

        if (!$identify) {
            $this->apiReturn(201, [], '请输入搜索参数');
            return;
        }

        $identify = trim($identify, ' ');

        $carRes = $this->_manageBiz->queryCardList($this->_sid, true, $page, $pageSize, $identify);
        if ($carRes['code'] != 200 || empty($carRes['data']['list'])) {
            $this->apiReturn($carRes['code'], $carRes['data'], $carRes['msg']);
            return;
        }

        $this->apiReturn(200, $carRes['data'], '获取成功');

    }

    /**
     * 获取一级转分销续费卡列表
     */
    public function getRenewalCardListForDis()
    {
        $identify = I('identify', '');//搜索内容
        $pageSize = I('page_size', 10, 'intval');//页数
        $page     = I('page', 1, 'intval');//页码

        if (!$identify) {
            $this->apiReturn(201, [], '请输入搜索参数');
        }

        //通过中台接口获取出所有1级转分销的产品
        $disProductRes = (new \Business\AnnualCard\TicketService())->queryAnnualDistributionTicketTitlePage(0, $this->_sid, [], 1, 200, ['I'], 1);
        if ($disProductRes['code'] != 200 || empty($disProductRes['data']['list'])) {
            $this->apiReturn(204, [], '数据获取异常');
        }

        //将所有产品id作为查询条件查出卡信息
        $pidArr   = array_column($disProductRes['data']['list'], 'pid');
        if (!$pidArr) {
            $this->apiReturn(204, [], '产品获取失败');
        }

        $identify = trim($identify, ' ');

        $carRes = $this->_manageBiz->queryCardList($this->_sid, false, $page, $pageSize, $identify, -1, 5, 0, 0, $pidArr);
        if ($carRes['code'] != 200 || empty($carRes['data']['list'])) {
            $this->apiReturn($carRes['code'], $carRes['data'], $carRes['msg']);
        }
        //年卡状态为撤销的数据不渲染
        $return = $this->_manageBiz->cleanRevokeCard($carRes['data']);
        $msg = empty($return['list']) ? '无数据' : '获取成功';
        $this->apiReturn(200, $return, $msg);
    }

    /**
     * 查询年卡记录
     * <AUTHOR>
     * @date   2022/10/17
     *
     */
    public function getPackageRecordList()
    {
        $cardId    = I('post.card_id', 0, 'intval');//卡id
        $size      = I('post.size', 10, 'intval');//页数
        $page      = I('post.page', 1, 'intval');//页码
        $type      = I('post.type', 0, 'intval');//操作类型
        $state     = I('post.state', 0, 'intval');//套餐状态
        $ordernum  = I('post.ordernum', '', 'strval');//订单号
        $operStart = I('post.oper_start', '', 'strval');//操作开始时间
        $operEnd   = I('post.oper_end', '', 'strval');//操作结束时间
        $virtualNo = I('post.virtual_no', '', 'strval');//虚拟卡号
        $sid       = $this->_sid;

        if (!empty($ordernum)) {
            $ordernum = trim($ordernum, ' ');
        }

        if (!$cardId || !$virtualNo) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //搜索时间处理
        $checkStart = date("Y-m-d", strtotime($operStart . " 00:00:00")) == $operStart;
        $operStart  = $checkStart ? strtotime($operStart . " 00:00:00") : 0;
        $checkEnd   = date("Y-m-d", strtotime($operEnd . " 23:59:59")) == $operEnd;
        $operEnd    = $checkEnd ? strtotime($operEnd . " 23:59:59") : 0;

        $result = (new PackageBiz())->getPackageRecordList($sid, $cardId, $virtualNo, $page, $size, $ordernum, $type, $operStart, $operEnd, $state);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取年卡审核列表
     *
     * @date 2023/04/11
     * @auther yangjianhui
     * @return array
     */
    public function getAnnualActiveAuditList()
    {
        $identify   = I('identify', '');//搜索内容
        $searchType = I('search_type', -1, 'intval');   //搜索类型 1 手机号 2姓名 3身份证
        $applyStart = I('apply_start', '', 'strval');   //审核提交开始时间
        $applyEnd   = I('apply_end', '', 'strval');     //审核提交结束时间
        $auditStart = I('audit_start', '', 'strval');//审核开始时间
        $auditEnd   = I('audit_end', '', 'strval');   //审核结束时间
        $lid        = I('lid', 0, 'intval');           //景区id
        $tid        = I('tid', 0, 'intval');          //年卡产品门票id
        $status     = I('status', -1, 'intval');//审核状态 -1 全部 0未审核  1：审核通过 2：审核不通过
        $size       = I('size', 10, 'intval');//页数
        $page       = I('page', 1, 'intval');//页码
        $excel      = I('excel', 0, 'intval');//是否导出excel 0：否 1：是

        $identify = trim($identify, ' ');

        if ((empty($applyStart) && !empty($applyEnd)) || (!empty($applyStart) && empty($applyEnd))) {
            $this->apiReturn(203, [], '时间查询字段缺失');
        }


        if ((empty($auditStart) && !empty($auditEnd)) || (!empty($auditStart) && empty($auditEnd))) {
            $this->apiReturn(203, [], '时间查询字段缺失');
        }

        $searchTid = [];
        if ($lid) {
            //通过传入景区id 获取到pid
            $javaApi   = new \Business\CommodityCenter\LandF();
            $searchTid = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([], [$lid], [], 'tid', true);
        }
        if ($tid) {
            $searchTid = $tid;
        }

        $result = $this->_manageBiz->getAnnualActiveAuditList($this->_sid, $identify, $searchType, $applyStart, $applyEnd, $auditStart,
            $auditEnd, $searchTid, $status, $excel, $page, $size);

        if ($excel) {
            $this->_exportAnnualActiveApplyList($result['data']['list']);
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 审核年卡激活申请
     *
     * @date 2023/04/12
     * @auther yangjianhui
     * @return array
     */
    public function auditActiveApply()
    {
        $id          = I('post.id', '', 'strval');
        $status      = I('post.status', 0, 'intval');
        $auditRemark = I('post.audit_remark', '', 'strval');
        if (empty($id)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $result = $this->_manageBiz->auditActiveApply($this->_sid, $this->_memberId, $id, $status, $auditRemark);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取申请详情
     *
     * @date 2023/04/13
     * @auther yangjianhui
     * @return array
     */
    public function getApplyDetailInfo()
    {
        $id     = I('post.id', 0, 'intval');
        $result = $this->_manageBiz->getApplyDetailInfo($this->_sid, $id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 重发审核短信
     *
     * @date 2023/04/13
     * @auther yangjianhui
     * @return array
     */
    public function resendAuditMessage()
    {
        $id     = I('post.id', 0, 'intval');
        $result = $this->_manageBiz->resendAuditMessage($this->_sid, $id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取年卡管理操作记录 默认倒序
     * <AUTHOR>
     * @Date 2023/7/21 23:50
     * @return void
     */
    public function getCardManageRecordList()
    {
        $batchId = I('post.batch_id', 0, 'intval');
        $startTime = I('post.start_time', '', 'strval');
        $endTime = I('post.end_time', '', 'strval');
        $optMember = I('post.opt_member', 0, 'intval');
        $channel = I('post.channel', 0, 'intval');
        $optType = I('post.opt_type', 0, 'intval');
        $virtualNo = I('post.virtual_no', '', 'strval');
        $page = I('post.page', 1, 'intval');
        $pageSize = I('post.page_size', 15, 'intval');
        $startTime = empty($startTime) ? date('Y-m-d') : $startTime;
        $endTime = empty($endTime) ? date('Y-m-d') : $endTime;
        //dtype=6，为员工，需要通过sid+opt_member 查询；如果不是员工，直接用sid查询
        $sid = $this->_sid;
        if ($optMember) {
            $memberModel = new \Model\Member\Member();
            $memberInfo  = $memberModel->getMemberInfo($optMember, 'id', 'dtype, account');
            //如果不是员工账号 忽略optMember
            if ($memberInfo['dtype'] != 6) {
                $optMember = 0;
            }
        }
        $page = $page < 1 ? 1 : $page;
        $pageSize = $pageSize < 1 ? 15 : $pageSize;
        $params = compact('batchId', 'startTime', 'endTime', 'sid', 'optMember', 'channel', 'optType', 'virtualNo', 'page', 'pageSize');
        $result = $this->_annualManageBiz->getCardManageRecordList($params, true);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 年卡操作管理记录搜索表单枚举值
     * <AUTHOR>
     * @Date 2023/7/24 10:02
     * @return void
     */
    public function getCardManageOptTypeAndChannel()
    {
        $result = $this->_annualManageBiz->getCardManageOptTypeAndChannel();
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    private function _exportAnnualActiveApplyList($data)
    {
        $filename = date('Ymd') . '年卡激活申请记录';

        $excelData[] = [
            '申请单号',
            '年卡产品',
            '年卡套餐',
            '虚拟卡号',
            '实体卡号',
            '物理卡号',
            '会员名称',
            '手机号',
            '身份证号',
            '申请时间',
            '申请备注',
            '审核状态',
            '审核时间',
            '审核备注',
        ];

        foreach ($data as $value) {
            $stateName = '';
            switch ($value['state']) {
                case 0:
                    $stateName = "未审核";
                    break;
                case 1:
                    $stateName = "已通过";
                    break;
                case 2:
                    $stateName = "已拒绝";
                    break;
                default:
                    break;
            }
            $excelData[] = [
                $value['id'],
                $value['lname'],
                $value['package_title'],
                $value['active_params']['virtualNo'],
                $value['active_params']['cardNo'],
                $value['active_params']['cardNo'],
                $value['name'],
                $value['mobile'],
                $value['id_card'] . "\t",
                $value['create_time'],
                $value['apply_remark'],
                $stateName,
                $value['update_time'],
                $value['audit_remark'],
            ];
        }

        $xls     = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excelData);
        $xls->generateXML($filename);
        exit;
    }
}