<?php

namespace Controller\AnnualCard;

use Library\Controller;
use Model\Member\Member;
use Model\Product\AnnualCard;

class Task extends Controller
{

    private $_sid           = null;
    private $_memberId      = null;
    private $_annualTaskBiz = null;

    public function __construct()
    {
        $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        $this->_sid           = $loginInfo['sid'];
        $this->_memberId      = $loginInfo['memberID'];
        $this->_annualTaskBiz = new \Business\AnnualCard\Task();
    }

    /**
     * 年卡导入Task处理
     * <AUTHOR>  Li
     * @date  2021-08-31
     */
    public function annualCardImport()
    {
        $ticketId = I('post.tid', 0, 'intval');  //年卡门票id
        $type = I('post.type', '');
        if ($type == 'giftcard') {
            $this->annualGiftCardImport();
            return;
        }
        if (!$ticketId) {
            $this->apiReturn(204, [], '请先选择产品');
        }

        if (!isset($_FILES['excel'])) {
            $this->apiReturn(204, [], '请上传excel');
        }

        //取得扩展名
        $filetype = strtolower(strrchr($_FILES['excel']['name'], "."));
        if (!in_array($filetype, ['.xlsx', '.xls'])) {
            $this->apiReturn(204, [], '文件格式不正确,请下载模板文件进行填写');
        }

        $excelProcess = new \Process\Order\OrderFromExcel();
        $excelData    = $excelProcess->parseForBatchOrder($_FILES['excel']['tmp_name']);

        if (empty($excelData)) {
            $this->apiReturn(204, [], '内容不能为空');
        }

        $result  = $this->_annualTaskBiz->annualCardImport($ticketId, $this->_sid, $excelData, $this->_memberId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 年卡批量人脸短信
     * <AUTHOR>  Li
     * @date  2022-06-21
     */
    public function annualFaceSms()
    {
        $annualId    = I('post.annual_ids', '', 'strval');  //年卡主键id  多个以逗号隔开
        $annualIdArr = [];
        if ($annualId) {
            $annualIdArr = explode(',', $annualId);
        }
        if (empty($annualIdArr)) {
            $this->apiReturn(204, [], '年卡信息异常');
        }

        $excelData = $this->_annualTaskBiz->getAnnualInfoByIdArr($annualIdArr, $this->_sid);
        if (empty($excelData)) {
            $this->apiReturn(204, [], '年卡数据获取异常');
        }

        $result  = $this->_annualTaskBiz->annualFaceSms($this->_sid, $excelData, $this->_memberId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 年卡批量恢复
     * <AUTHOR>  Li
     * @date  2022-06-21
     */
    public function annualRecover()
    {
        $annualId = I('post.annual_ids', '', 'strval');  //年卡主键id  多个以逗号隔开
        $timeType = I('post.time_type', 0, 'intval');
        $annualIdArr = [];
        if ($annualId) {
            $annualIdArr = explode(',', $annualId);
        }
        if (empty($annualIdArr)) {
            $this->apiReturn(204, [], '年卡信息异常');
        }
        $table = $timeType == 0 ? AnnualCard::ANNUAL_CARD_TABLE : AnnualCard::ANNUAL_CARD_TABLE_HISTORY;
        $excelData = $this->_annualTaskBiz->getAnnualInfoByIdArr($annualIdArr, $this->_sid, $table);
        if (empty($excelData)) {
            $this->apiReturn(204, [], '年卡数据获取异常');
        }

        $result  = $this->_annualTaskBiz->annualRecover($this->_sid, $excelData, $this->_memberId, $table);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 年卡批量禁用
     * <AUTHOR>  Li
     * @date  2022-06-21
     */
    public function annualDisabled()
    {
        $annualId    = I('post.annual_ids', '', 'strval');  //年卡主键id  多个以逗号隔开
        $timeType = I('post.time_type', 0, 'intval');
        $annualIdArr = [];
        if ($annualId) {
            $annualIdArr = explode(',', $annualId);
        }
        if (empty($annualIdArr)) {
            $this->apiReturn(204, [], '年卡信息异常');
        }
        $table = $timeType == 0 ? AnnualCard::ANNUAL_CARD_TABLE : AnnualCard::ANNUAL_CARD_TABLE_HISTORY;
        $excelData = $this->_annualTaskBiz->getAnnualInfoByIdArr($annualIdArr, $this->_sid, $table);
        if (empty($excelData)) {
            $this->apiReturn(204, [], '年卡数据获取异常');
        }

        $result  = $this->_annualTaskBiz->annualDisabled($this->_sid, $excelData, $this->_memberId, $table);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 年卡批量延期
     * <AUTHOR>  Li
     * @date  2022-06-21
     */
    public function annualPostpone()
    {
        $annualId  = I('post.annual_ids', '', 'strval');  //年卡主键id  多个以逗号隔开
        $type      = I('post.type', 1, 'intval');  //延期方式 1调整至指定日期 2顺眼X天
        $delayData = I('post.delay_data', '', 'strval');

        $annualIdArr = [];
        if ($annualId) {
            $annualIdArr = explode(',', $annualId);
        }
        if (empty($annualIdArr)) {
            $this->apiReturn(204, [], '年卡信息异常');
        }

        if (!in_array($type, [1, 2]) || empty($delayData)) {
            $this->apiReturn(204, [], '操作类型异常，请联系管理员');
        }

        $excelData = $this->_annualTaskBiz->getAnnualInfoByIdArr($annualIdArr, $this->_sid);
        if (empty($excelData)) {
            $this->apiReturn(204, [], '年卡数据获取异常');
        }

        foreach ($excelData as &$item) {
            $item['type']       = $type;
            $item['delay_data'] = $delayData;
        }

        $result  = $this->_annualTaskBiz->annualPostpone($this->_sid, $excelData, $this->_memberId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 批量延期
     * <AUTHOR>
     * @date   2022/10/18
     *
     */
    public function annualBatchPostpone()
    {
        //年卡主键id  多个以逗号隔开
        $annualId = I('post.annual_ids', '', 'strval');
        //导入虚拟卡文件
        $importFile = $_FILES['file'] ?? [];
        //列表请求参数
        $params = I('post.params', '', 'strval');
        //延期方式 1调整至指定日期 2顺延X天
        $delayType = I('post.delay_type', 1, 'intval');
        //延期内容
        $delayData = I('post.delay_data', '', 'strval');
        //批量延期操作：1.勾选延期；2.列表延期；3.导入虚拟卡号文件延期
        $type = I('post.type', 0, 'intval');
        //门票id
        $tid = I('post.tid', 0, 'intval');

        if ($type == 1 && empty($annualId)) {
            $this->apiReturn(203, [], '延期参数缺失');
        }
        if ($type == 2 && empty($params)) {
            $this->apiReturn(203, [], '延期参数缺失');
        }
        if ($type == 3 && empty($importFile)) {
            $this->apiReturn(203, [], '延期参数缺失');
        }
        if (!in_array($type, [1, 2, 3])) {
            $this->apiReturn(203, [], '延期操作错误');
        }
        if (!in_array($delayType, [1, 2])) {
            $this->apiReturn(203, [], '延期方式错误');
        }
        if (empty($delayData)) {
            $this->apiReturn(203, [], '延期参数错误');
        }

        //需要校验下日志
        if ($delayType == 1) {
            if (date("Y-m-d", strtotime($delayData . ' 00:00:00')) != $delayData) {
                $this->apiReturn(203, [], '延期的指定日期格式错误');
            }
            if (strtotime($delayData . ' 00:00:00') < strtotime(date("Y-m-d 00:00:00"))) {
                $this->apiReturn(203, [], '延期的指定日期值错误');
            }
        }

        $result  = $this->_annualTaskBiz->annualBatchPostpone($this->_sid, $this->_memberId, $type, $delayType, $delayData, $tid, $annualId, $params, $importFile);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 年卡批量新增特权（往原有套餐上加特权）
     * <AUTHOR>
     * @Date 2023/6/26 16:46
     *
     * 添加特权的 group_priv:
     * [{
     * "superior_id":3385,"use_limit":"-1","group_name":"ypc-tq001","sort":2,
     * "tid_arr":[1019394,1019526,110034171],
     * "tid_aid_map":{"1019394":3385,"1019526":3385,"110034171":3385}
     * }]
     * 删除特权的 group_priv:
     * group_id => group_name
     * {"2290":"木兰草原景区--在测，不要改-测试票","2291":"木兰草原景区--在测，不要改-测试票"}
     * @return void
     */
    public function annualBatchOptPrivilege()
    {
        //年卡主键id  多个以逗号隔开
        $annualId = I('post.annual_ids', '', 'strval');
        //特权参数 里面包含特权门票id
        $group_priv = I('post.group_priv', '', 'strval');
        //导入虚拟卡文件
        $importFile = $_FILES['file'] ?? [];
        //列表请求参数
        $params = I('post.params', '', 'strval');
        //批量操作：1.勾选；2.列表；3.导入虚拟卡号文件
        $type = I('post.type', 0, 'intval');
        //操作类型 1-批量添加特权 2-批量删除特权
        $optType = I('post.opt_type', 0, 'intval');
        //年卡门票id
        $tid = I('post.tid', 0, 'intval');
        if (empty($group_priv)) {
            $this->apiReturn(203, [], '特权参数缺失');
        }
        if ($type == 1 && empty($annualId)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if ($type == 2 && empty($params)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if ($type == 3 && empty($importFile)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if (!in_array($type, [1, 2, 3])) {
            $this->apiReturn(203, [], '操作方式不支持');
        }
        if (!in_array($optType, [1, 2])) {
            $this->apiReturn(203, [], '操作类型不支持');
        }
        $group_priv_arr = json_decode($group_priv, true);
        if (json_last_error() != JSON_ERROR_NONE) {
            $this->apiReturn(203, [], '特权参数有误');
        }
        $result  = $this->_annualTaskBiz->annualBatchPrivilege($this->_sid, $this->_memberId, $type, $optType, $tid, $annualId, $group_priv_arr, $params, $importFile);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], '接口异常');
    }

    //年卡批量导入礼品卡
    //采用南阳年卡物理卡导入下单不激活脚本逻辑，每次导入不超过3000条，
    //下单参数中的手机号和姓名使用供应商的手机号和供应商名称
    public function annualGiftCardImport()
    {
        try {
            $ticketId = I('post.tid', 0, 'intval');  //年卡门票id
            if (empty($ticketId)) {
                throw new \Exception('请选择产品');
            }
            if (!isset($_FILES['excel'])) {
                throw new \Exception('请上传excel');
            }
            //取得扩展名
            $filetype = strtolower(strrchr($_FILES['excel']['name'], "."));
            if (!in_array($filetype, ['.xlsx', '.xls'])) {
                throw new \Exception('文件格式不正确,请下载模板文件进行填写');
            }
            $excelProcess = new \Process\Order\OrderFromExcel();
            $excelData = $excelProcess->parseForBatchOrder($_FILES['excel']['tmp_name']);
            if (empty($excelData)) {
                throw new \Exception('excel数据为空');
            }
            //获取供应商信息
            $supplierInfo = (new Member('slave'))->getMemberInfo($this->_sid);
            if (empty($supplierInfo)) {
                throw new \Exception('供应商不存在');
            }
            if (empty($supplierInfo['mobile'])) {
                throw new \Exception('供应商手机号不存在，由于下单需要手机号，请补充');
            }
            if (empty($supplierInfo['dname'])) {
                throw new \Exception('供应商名称不存在，由于下单需要购票人名称，请补充');
            }
            $params = [
                'sid' => $this->_sid,
                'tid' => $ticketId,
                'order_mobile' => $supplierInfo['mobile'],
                'order_name' => $supplierInfo['dname'],
                'member_id' => $this->_memberId,
                'excel_data' => $excelData,
            ];
            $result = $this->_annualTaskBiz->annualGiftCardImport($params);
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } catch (\Throwable $e) {
            $this->apiReturn(500, [], $e->getMessage());
        }
    }
    /**
     *  文件导入校验
     * <AUTHOR>
     * @date   2022/10/18
     *
     */
    public function annualBatchFileCheck()
    {
        $type = I('post.type', 'postpone', 'strval');

        //定义默认返回格式
        $data = [
            'total' => 0
        ];

        if (!isset($_FILES['file'])) {
            $this->apiReturn(203, $data, '请上传excel');
        }

        //取得扩展名
        $filetype = strtolower(strrchr($_FILES['file']['name'], "."));
        if (!in_array($filetype, ['.xlsx', '.xls'])) {
            $this->apiReturn(204, $data, '文件格式不正确,请下载模板文件进行填写');
        }

        //文件校验
        $result =  $this->_annualTaskBiz->annualBatchPostponeFileCheck($_FILES['file'], $type);

        if (isset($result['code'])) {
            $data['total'] = count($result['data']);
            $this->apiReturn($result['code'], $data, $result['msg']);
        } else {
            $this->apiReturn(500, $data, '接口异常');
        }
    }

}
