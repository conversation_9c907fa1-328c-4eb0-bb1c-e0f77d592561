<?php
/**
 * Created by PhpStorm.
 * User: linchen
 * Date: 2018/11/29 0029
 * Time: 18:04
 */
namespace Controller\Cmb;

class CmbSign extends Cmb {
    private $_mid;
    public function __construct()
    {
        parent::__construct();
        $memberId = $this->isLogin('auto', false);
        if (!$memberId){
            $this->apiReturn('204','','请先登录');
        }
        $this->_mid = $memberId;
    }
    /***
     * 立即打卡
     * <AUTHOR>
     * @date   2018-11-26
     * @param int address_id 签到地点id
     * @param int tid   票id
     */
    public function signCoupon(){
        $signId = I('post.address_id',0);
        if ($signId < 1) {
            $this->apiReturn('204','','获取地点状态失败');
        }
        $CmbSignBiz = new \Business\Cmb\CmbSign();
        $result     = $CmbSignBiz->signUp($this->_mid,$signId);
        $this->apiReturn($result['code'],$result['data'],$result['msg']);
    }
    /***
     * 获取我的签到
     * <AUTHOR>
     * @date   2018-11-26
     */
    public function getMySign(){
        $CmbSignBiz = new \Business\Cmb\CmbSign();
        $result      = $CmbSignBiz->getMySign($this->_mid);
        $this->apiReturn($result['code'],$result['data'],$result['msg']);
    }
    /***
     * 签到分享
     * <AUTHOR>
     * @date   2018-11-26
     */
    public function signShare(){
        $CmbFriendModel = new \Model\Cmb\CmbFriend();
        $checkSign      = $CmbFriendModel->checkSign($this->_mid);
        if (!empty($checkSign)){
            $arrIds = explode(',',$checkSign['signid']);
            $count  = count($arrIds);
            if ($count < 2){
                $this->apiReturn(200,'','还没集齐福卡呢');
            }
        }else{
            $this->apiReturn(200,'','还没集齐福卡呢');
        }
        $result   = $CmbFriendModel->updateShare($this->_mid);
        if ($result){
            $this->apiReturn(200,'','分享成功');
        }else{
            $this->apiReturn(200,'','分享失败');
        }
    }
}