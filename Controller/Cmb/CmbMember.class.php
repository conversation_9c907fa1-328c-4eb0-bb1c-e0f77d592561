<?php
/**
 * Created by PhpStorm.
 * User: linchen
 * Date: 2018/11/23 0023
 * Time: 14:04
 */

namespace Controller\Cmb;
use Library\Constants\MemberConst;
use Library\Tools;
use Model\Member\Member;

class cmbMember extends Cmb
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 招行注册和登录
     * <AUTHOR>
     * @param  string sign 验签
     * @date   2018-11-27
     */
    public function CmbRegister(){
        $sign = I('post.sign','');
        if (empty($sign)){
            $this->apiReturn(204,'','参数错误');
        }
        $userId = $this->rsa($sign);
        if (!$userId) {
            $this->apiReturn(204,'','验签失败');
        }
        $userData = explode(';;',$userId);

        if (!isset($userData[1]) || !$userData[1]){
            $this->apiReturn(204,'','手机号为空');
        }

        $isMobile = \Library\Tools::ismobile($userData[1]);
        if(!$isMobile) {
            $this->apiReturn(204,'','手机号错误');
        }

        //if (!(\idcard_checksum18($userData[4]))){
        //    $this->apiReturn(204,'','身份证号错误');
        //}

        $time     = substr($userData[3],0,strlen($userData[3])-3);
        if (time() - $time > 1800){
            $this->apiReturn(204,'','验签过期');
        }

        $cmbBiz  = new \Business\Cmb\CmbMember();
        $isLogin = $this->isLogin('ajax', false);

        if (!$isLogin) {
            $memModel   = new Member();
            $authInfo   = $memModel->getAuthByIdentifier($userData[0], MemberConst::INFO_CMB + 1);
            if ($authInfo) {
                $role   = $memModel->getTheRoleInfo($authInfo['customer_id'], MemberConst::ROLE_TOURIST, 'id,dname');
                $result = $cmbBiz->CmbSessionLogin($role['id']);
                if ($result['code'] != 200) {
                    $this->apiReturn(204,'',$result['msg']);
                } else {
                    $cache      = \Library\Cache\Cache::getInstance('redis');
                    $cache->set(SELF::CMB_CACHE_KEY. $this->_sid, $userData[2]);
                    $this->apiReturn(200,$result['data']['memberID'],$result['msg']);
                }
            } else {
                $registerRes    = $cmbBiz->CmbRegister($userData[0], $userData[1],'','',$userData[2]);
                if($registerRes['code'] == 200){
                    $role       = $memModel->getTheRoleInfo($registerRes['data']['customer_id'], MemberConst::ROLE_TOURIST, 'id');
                    $result     = $cmbBiz->CmbSessionLogin($role['id']);
                    if ($result['code'] != 200){
                        $this->apiReturn(204,'','登录失败');
                    }else{
                        $cache      = \Library\Cache\Cache::getInstance('redis');
                        $cache->set(SELF::CMB_CACHE_KEY. $this->_sid, $userData[2]);
                        $this->apiReturn(200,$result['data']['memberID'],$result['msg']);
                    }
                }else{
                    $this->apiReturn(204,'',$registerRes['msg']);
                }
            }
        } else {
            $loginInfo = $this->getLoginInfo('ajax', false, false);
            $memberId  = $loginInfo['memberID'];

            $this->apiReturn(200, $memberId, '登录成功');
        }
    }

    /**
     * 获取用户名字
     * <AUTHOR>
     * @param  string $hid 用户id
     * @date   2018-11-27
     */
    public function getMemberInfo(){
        $mid = I('post.hid',0);
        if ($mid < 1){
            $this->apiReturn(200,'','参数错误');
        }
        $cmbFriendMdl = new \Model\Cmb\CmbFriend();
        $list         = $cmbFriendMdl->getAssistance($mid);
        foreach ($list as $value){
            if ($value['mid'] == $mid && $value['uid'] == $mid){
                $name = $value['helpname'];
                break;
            }
        }
        if (empty($list)){
            $total        = count($list);
        }else{
            $arrUid       = array_column($list,'uid');
            $total        = count($arrUid);
            if (in_array($mid,$arrUid)){
                $total = $total-1;
            }
        }
        if (empty($name)){
            $name = '招行';
        }else{
            $name = mb_substr($name,0,1,'UTF-8');
        }
        $data = [
            'name'  => $name,
            'total' => $total
        ];
        $this->apiReturn(200,$data,'获取成功');
    }
}