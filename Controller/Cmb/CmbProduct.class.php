<?php
/**
 * Created by PhpStorm.
 * User: linchen
 * Date: 2018/11/24 0024
 * Time: 10:12
 */

namespace Controller\Cmb;
use Business\JavaApi\TicketApi;
use Business\Order\OrderQueryJavaService;
use Business\Order\OrderQueryMove;
use Business\Product\Product as bizProduct;
use Model\Member\Member;
use Business\Product\Show as ShowBiz;
use Business\Product\Ticket as bizTicket;
use Model\Order\OrderQuery;
use Model\Product\AnnualCard;
use Model\Product\Land;
use Model\Product\Ticket;

class CmbProduct extends Cmb
{

    public function __construct()
    {
        parent::__construct();
    }
    /**
     * 详情页-获取景区信息
     * <AUTHOR>
     * @param  int pid 产品id
     * @date   2018-11-27
     */
    public function getLandInfo(){
        header("Access-Control-Allow-Origin: *"); // 允许任意域名发起的跨域请求

        $pid = I('pid', '', 'intval');
        if ($pid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }
        // 转换到java接口
        $ticketModel = new Ticket();
        $tInfo    = $ticketModel->getTicketInfoByPid($pid, 'id,title,pid,apply_did,landid');
        $productBiz  = new bizProduct();
        $landInfoArr = $productBiz->getProductInfo($this->_sid, $tInfo['landid'], 0);

        if ($landInfoArr['code'] != 200) {
            $this->apiReturn(204, [], $landInfoArr['msg']);
        }

        if ($landInfoArr['data']['jtype'] != 'I'){
            $this->apiReturn(204, [], '该产品不是年卡');
        }

        if ($landInfoArr['data']['imgpathGrp']) {
        } else {
            $landInfoArr['data']['imgpathGrp'] = [];
        }
        $landInfoArr['data']['jqts'] = nl2br($landInfoArr['data']['jqts']);
        $landInfoArr['data']['jtzn'] = nl2br($landInfoArr['data']['jtzn']);
        $landInfoArr['data']['bhjq'] = htmlspecialchars_decode($landInfoArr['data']['bhjq']);
        $landInfoArr['data']['bhjq'] = \image_opt($landInfoArr['data']['bhjq'], 600);
        $landInfoArr['data']['venue_id'] = $landInfoArr['data']['venus_id'];

        $productList   = $this->_getLandTickets( $tInfo['landid'],38);//微商城渠道
        if (empty($productList)){
            $this->apiReturn(204, [], '票类获取失败');
        }
        $tags          = $this->_parseTicketsTags($productList);
        //从java获取指定日期票类价格
        $ticketIds = array_column($productList, 'id');
        $ticketIds = implode(',', $ticketIds);
        $date = date('Y-m-d');
        $priceData = TicketApi::getSinglePrices($ticketIds, $date);
        $aidFilter = false;
        $list = [];
        foreach ($productList as $item) {
            $ptype  = $item['type'];
            $tPrice = $item['counter_price'] / 100;

            if (\inWechatSmallApp()) {
                $jsPrice = $item['window_price'] / 100;
            } else {
                $jsPrice = $item['retail_price'] / 100;
            }
            if ($ptype == 'H') {
                $tPrice = $priceData[$item['id']]['counter_price'];
                $tPrice = (isset($tPrice)) ? $tPrice / 100 : '';
                $jsPrice = $priceData[$item['id']]['retail_price'];
                $jsPrice = (isset($jsPrice)) ? $jsPrice / 100 : '';
            }

            $tmp = [
                'ticket'        => $item['name'],
                'pid'           => $item['product_id'],
                'tid'           => $item['id'],
                // 'px'      => $item['px'],
                'aid'           => $item['superior_id'],
                'sid'           => $item['supplier_id'],
                'jsprice'       => $jsPrice,
                'tprice'        => $tPrice,
                'tags'          => $tags[$item['id']] ?: '',
                'intro'         => explode('<br />', nl2br($item['introduction'])),
                'refund_rule'   => $item['refund_rule'],
                'riskWarning'=> $item['riskWarning'],
            ];
            if ($tmp['pid'] == $pid){
                $list[] = $tmp;
            }
        }
        if ($list) {
            $list = $this->_fillExtraForTicketListByI($list, 'I');
        }
        $data = ['land' => $landInfoArr['data'],'type' => 'I' ,'list' => $list];
        $this->apiReturn(200,$data);
    }
    /**
     * 获取景区门票列表
     * @param  int $lid 景区id
     * @return [type]      [description]
     */
    private function _getLandTickets($lid, $channel = '', $operId = '')
    {
        if (!$lid) {
            return [];
        }
        // 获取景区下的门票列表
        $ticketBiz = new bizTicket();
        $ticketsList = $ticketBiz->getRetailTicketList($this->_sid, $lid, $channel, $operId);

        if ($ticketsList['code'] != 200) {
            return [];
        }

        return $ticketsList['data'];
    }
    /**
     * 解析票类的一些标签属性
     * @param  [type] $tickets 门票列表
     * @return [type]          [description]
     */
    private function _parseTicketsTags($tickets)
    {
        $tags = [];

        foreach ($tickets as $item) {
            if ($item['preorder_early_days'] > 0) {
                $tags[$item['id']][] = "提前{$item['preorder_early_days']}天";
            }

            if ($item['pay_way'] == 0) {
                $tags[$item['id']][] = '现场支付';
            }

            if ($item['pay'] == 3) {
                $tags[$item['pid']][] = '会员卡支付';
            }

            //退票规则：添加了这个值 -1=不可退且是可提现
            if ($item['refund_rule'] == 2 || $item['refund_rule'] == -1) {
                $tags[$item['id']][] = '不可退';
            }
        }

        return $tags;
    }
    /**
     * 门票列表填充年卡信息
     * <AUTHOR>
     * @date   2018-11-29
     * @param  array     $list  门票列表数据
     * @param  string    $type 产品类型
     * @return array
     */
    private function _fillExtraForTicketListByI($list, $type)
    {
        //年卡(虚拟卡库存)
        $pidArr = array_column($list, 'pid');

        $annualModel = new AnnualCard('slave');

        $sid    = $list[0]['sid'];
        $stgMap = $annualModel->getAnnualCardStorage($sid, $pidArr);

        foreach ($list as &$item) {
            if (isset($stgMap[$item['pid']])) {
                $item['storage'] = $stgMap[$item['pid']];
            } else {
                $item['storage'] = 0;
            }
        }

        return $list;
    }
    /**
     * 获取我的票卷
     * <AUTHOR>
     * @date   2018-11-29
     * @param  string    type 产品类型
     * @return array
     */
    public function getTicketRoll(){
        $type = I('post.type','0');
        $memberId   = $this->isLogin('ajax');
        //$orderQueryJavaBiz = new OrderQueryJavaService();
        $option     = [
            'pay_status' => [[0,1]],
            'ordermode'  => [[38]],  //招行
        ];
        $status = [];
        switch ($type){
            case 0:
                $status     = [0,1,2];
                break;
            case 1:
                $status     = [1];
                break;
            case 2:
                $status     = [2];
                break;
            default:
                $this->apiReturn(204,'','状态错误');
                break;
        }
        if ($status){
            $option['status']  = [$status];
        }
        //$selectParams = [
        //    'field' => '*',
        //    'limit' => 100,
        //];
        //$orderList = $orderQueryJavaBiz->getOrderInfoByMemberIdAndOtherParam($memberId,$option,$selectParams);

        //订单查询迁移ywx
        $statusNew = null;
        if (!empty($status)) {
            $statusNew = $status;
        }
        $orderMove = new OrderQueryMove();
        $orderList = $orderMove->queryFindByMemberAndParamsZs($memberId, $statusNew, [0,1], 1, 100);

        $list = $this->_handleOrder($orderList);
        $this->apiReturn(200,$list);
    }
    /**
     * 处理订单信息
     * <AUTHOR>
     * @date   2018-11-29
     * @param  array     $orderList  订单列表
     * @return array
     */
    private function _handleOrder($orderList){
        $list = [];
        if ($orderList){
            $lidArr     = array_column($orderList, 'lid');
            $tidArr     = array_column($orderList, 'tid');
            $Ticket     = new Ticket('slave');
            //景区信息
            $javaAPi      = new \Business\CommodityCenter\Land();
            $landRes      = $javaAPi->queryLandMultiQueryById($lidArr);
            $landsMapping = array_column($landRes, null, 'id');

            //门票信息
            $ticketsMapping = $Ticket->getMuchTicketInfo($tidArr, 'id,title,tprice,notes,order_start,order_end,delaytype,use_early_days,delaydays');
            foreach ($orderList as $key => $value){
                $tmp['title']        = $landsMapping[$value['lid']]['title'];
                $tmp['p_type']       = $landsMapping[$value['lid']]['p_type'];
                $tmp['notes']        = $ticketsMapping[$value['tid']]['notes'];
                if (!is_null($ticketsMapping[$value['tid']]['order_start']) && !is_null($ticketsMapping[$value['tid']]['order_end'])){
                    $tmp['order_start']  = $ticketsMapping[$value['tid']]['order_start'];
                    $tmp['order_end']    = $ticketsMapping[$value['tid']]['order_end'];
                }elseif ($ticketsMapping[$value['tid']]['delaytype'] == 0){
                    $tmp['order_start']  = $value['playtime'];
                    $tmp['order_end']    = $value['playtime'];
                }elseif ($ticketsMapping[$value['tid']]['delaydays'] != 0){
                    $tmp['order_start']  = date('Y-m-d',strtotime($value['playtime']) - $ticketsMapping[$value['tid']]['use_early_days'] * 86400);
                    $tmp['order_end']    = date('Y-m-d',strtotime($value['playtime']) + $ticketsMapping[$value['tid']]['delaydays'] * 86400);
                }
                if ($landsMapping[$value['lid']]['p_type'] == 'I'){
                    continue;
                }
                $productConfig = load_config('product','cmb_pay');
                if ($value['pid'] == $productConfig['one_product'][0]){
                    $tmp['price'] = '导览仪';
                }else{
                    $tmp['price'] = 15;
                }
                $tmp['name']   = $ticketsMapping[$value['tid']]['title'];
                if ($value['status'] == 0){
                    $tmp['status'] = '未使用';
                }elseif ($value['status'] == 1){
                    $tmp['status'] = '已使用';
                }else{
                    $tmp['status'] = '已过期';
                }
                $tmp['salerid']    = $value['salerid'];
                $tmp['code']       = $value['code'];
                $tmp['ordernum']   = $value['ordernum'];
                array_push($list,$tmp);
            }
        }
        return $list;
    }
    /**
     * 获取已使用的年卡
     * <AUTHOR>
     * @date   2018-11-29
     * @return array
     */
    public function getMyAnnual(){
        $mid     = $this->isLogin('ajax');
        $vipBiz  = new \Business\Mall\MemberVip($mid, $this->_sid);

        $res = $vipBiz->getMyAnnualCardList(1, 50);
        if ($res['code'] == 200){
            foreach ($res['data']['list'] as $key => $value){
                $result = $vipBiz->getAnnualHistory($value['id'], 1, 1);
                if ($result['code'] == 200 && count($result['data']['list']) > 0){
                    continue;
                }else{
                    unset($res['data'][$key]);
                }
            }
        }
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }
    /**
     * 获取已禁用的年卡
     * <AUTHOR>
     * @date   2018-11-29
     * @return array
     */
    public function getMyForbidAnnual(){
        $mid     = $this->isLogin('ajax');
        $vipBiz  = new \Business\Mall\MemberVip($mid, $this->_sid);

        $res = $vipBiz->getMyAnnualCardList(1, 50);
        if ($res['code'] == 200){
            foreach ($res['data']['list'] as $key => $value){
                if ($value['status'] != 2){
                    unset($res['data'][$key]);
                }
            }
        }
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }
}