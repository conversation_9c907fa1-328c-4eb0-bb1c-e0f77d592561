<?php
/**
 * Created by PhpStorm.
 * User: linchen
 * Date: 2018/11/26 0026
 * Time: 11:28
 */

namespace Controller\Cmb;
use Business\Member\Member;
use Business\Product\AnnualCard as AnnualBiz;
use Library\Cache\Cache;
use Library\Constants\MemberConst;
use Library\Tools\Helpers;
use Model\Product\Ticket;

class CmbOrder extends Cmb
{
    private $_soapInstance = null;
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 招行下单接口
     * <AUTHOR>
     * @date   2018-11-27
     */
    public function order(){
        $params    = I('','');
        $annualBiz = new AnnualBiz();
        $orderBiz  = new \Business\Cmb\CmbOrder();
        $landInfo  = $orderBiz->getLandInfo($params['pid']);

        if (empty($landInfo)){
            $this->apiReturn(500, [], '接口异常');
        }

        if (!isset($params['ordertel']) || empty($params['ordertel'])){
            $params['ordertel'] = '';
        }

        $this->_checkSignForCmb($params['sign']);
        $this->_checkForCmb($params['ordertel'],$params['id_card'],$params['avatar'],$params['sign']);

        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $mid       = $loginInfo['memberID'] ?? 0;

        if ($landInfo['p_type'] != 'I' ){
            $isDownOrder = $orderBiz->isDownOrder($mid,$params['pid']);
            if (!$isDownOrder) {
                $this->apiReturn(204, [], '你已经购买过一分钱产品了不能重复购买');
            }
            $result = $orderBiz->orderForOther($mid,$params,$this->_sid);
            $this->apiReturn($result['code'],$result['data'],$result['msg']);
        }

        $ticModel   = new Ticket('slave');
        $ticketInfo = $ticModel->getTicketInfoByPid($params['pid'], 'apply_did');

        if (!$ticketInfo) {
            $this->apiReturn(204, [], '产品信息未找到');
        }

        $bindRes    = $annualBiz->getBindAnnualCard($ticketInfo['apply_did'], $mid);
        if ($bindRes['code'] == 200){
            $this->apiReturn(204, [], '你已经买过年卡了');
        }

        $cmbModel           = new \Model\Cmb\CmbFriend();
        $checkOneProductNow = $cmbModel->getOneProductByMid($mid);
        if ($checkOneProductNow > 0){
            $this->apiReturn(204, [], '你已经购买过一次一分钱年卡了');
        }

        //检查一分钱年卡是否有购买权限
        $checkOneProduct = $orderBiz->checkOneMoneyProduct($params['pid'],$mid);
        if (!$checkOneProduct) {
            $this->apiReturn(204, [], '请先集齐好友呦');
        }

        $paramsObject = [
            'pid'           => $params['pid'],
            'aid'           => $this->_sid,
            'paymode'       => 1,
            'ordername'     => $params['ordername'],
            'ordertel'      => $params['ordertel'],
            'order_type'    => $params['order_type'],
            'virtual_no'    => $params['virtual_no'],
            'avatar'        => isset($params['avatar']) ? $params['avatar'] : '',
            'id_card'       =>isset($params['id_card'])? $params['id_card'] :'' ,
            'ordermode'     => 38 //招行
        ];


        $result  = $annualBiz->orderForCard($mid, (object)$paramsObject,100);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 检查签名是否存在
     * <AUTHOR>
     * @param  string $sign 验签
     * @date   2018-11-27
     */
    public function _checkSignForCmb($sign){
        if (empty($sign)){
            $this->apiReturn(204, [], '签名获取失败');
        }
    }

    /**
     * 下单自动注册
     * <AUTHOR>
     * @param  string $mobile 电话
     * @param  string $sfz   身份证
     * @param  string $avatar   头像
     * @param  string $sign   签名
     * @date   2018-11-27
     */
    public function _checkForCmb($mobile,$sfz = '',$avatar = '',$sign){
        $cmbBiz         = new \Business\Cmb\CmbMember();
        $userId         = $cmbBiz->rsa($sign);
        if (!$userId) {
            $this->apiReturn(204,'','验签失败');
        }
        $userData   = explode(';;',$userId);

        if (!isset($userData[1]) || !$userData[1]){
            $this->apiReturn(204,'','手机号为空');
        }

        $isMobile = \Library\Tools::ismobile($userData[1]);
        if(!$isMobile) {
            $this->apiReturn(204,'','手机号错误');
        }

        if (empty($userData[1]) || !isset($userData[1])){
            $this->apiReturn(204,'','手机号为空');
        }

        $time       = substr($userData[3],0,strlen($userData[3])-3);
        if (time() - $time > 1800){
            $this->apiReturn(204,'','验签过期');
        }

        if (!empty($mobile) && $userData[1] != $mobile){
            $this->apiReturn(204,'','和预留手机号不一致');
        }

        $isLogin = $this->isLogin('ajax', false);

        if (!$isLogin){
            $memModel       = new \Model\Member\Member();
            $authInfo       = $memModel->getAuthByIdentifier($userData[0], MemberConst::INFO_CMB + 1);
            if ($authInfo){
                $role = $memModel->getTheRoleInfo($authInfo['customer_id'], MemberConst::ROLE_TOURIST, 'id');
                $result     = $cmbBiz->CmbSessionLogin($role['id']);
                $cache      = \Library\Cache\Cache::getInstance('redis');
                $cache->set(SELF::CMB_CACHE_KEY. $this->_sid, $userData[2]);

                if ($result['code'] != 200){
                    $this->apiReturn(204,'',$result['msg']);
                }
            }else{
                $registerRes = $cmbBiz->CmbRegister($userData[0], $userData[1], $sfz, $avatar,$userData[2]);
                if($registerRes['code'] == 200){
                    $role    = $memModel->getTheRoleInfo($registerRes['data']['customer_id'], MemberConst::ROLE_TOURIST, 'id');
                    $result  = $cmbBiz->CmbSessionLogin($role['id']);

                    $cache      = \Library\Cache\Cache::getInstance('redis');
                    $cache->set(SELF::CMB_CACHE_KEY. $this->_sid, $userData[2]);
                    if ($result['code'] != 200){
                        $this->apiReturn(204,'','登录失败');
                    }
                    return $role['id'];
                }else{
                    $this->apiReturn(204,'',$registerRes['msg']);
                }
            }
        }
    }

    /**
     * 招行支付页面
     * <AUTHOR>
     * @param int ordernum 订到号
     * @date   2018-11-27
     */
    public function Pay(){
        $ordernum = I('post.ordernum', '' );

        if (!$ordernum ) {
            $this->apiReturn(204, [$ordernum], '参数错误');
        }
        $Order     = new \Model\Order\OrderTools();
        $orderInfo = $Order->getOrderInfo($ordernum);
        if (!in_array($orderInfo['status'], [0,4])) {
            $this->apiReturn(204, [], '订单状态错误');
        }
        if ($orderInfo['pay_status'] != 2){
            $this->apiReturn(204, [], '订单已支付');
        }
        $orderBiz = new \Business\Cmb\CmbOrder();
        $result = $orderBiz->Pay($ordernum,$orderInfo);
        if ($result['code'] != 200){
            $this->apiReturn(204, [], $result['msg']);
        }
        $this->apiReturn(200, $result['data'], $result['msg']);
    }

}