<?php
/**
 * Created by PhpStorm.
 * User: linchen
 * Date: 2018/11/28 0028
 * Time: 19:40
 */

namespace Controller\Cmb;

use Model\Member\Member;
use Model\Product\AnnualCard as CardModel;

class CmbFriend extends Cmb
{
    private $_mid;

    public function __construct()
    {
        parent::__construct();
        $memberId = $this->isLogin('auto', false);
        if (!$memberId) {
            $this->apiReturn('204', '', '请先登录');
        }
        $this->_mid = $memberId;
    }

    /**
     * 我的砍价页面
     * <AUTHOR>
     * @date   2018-11-28
     * @return array
     */
    public function myAssistance()
    {
        $cmbFriendBiz = new \Business\Cmb\CmbFriendHelp();
        $isAssistance = $cmbFriendBiz->getAssistance($this->_mid, $this->_mid); //本人
        if (empty($isAssistance)) {
            //插入一条
            $cache  = \Library\Cache\Cache::getInstance('redis');
            $name   = $cache->get(SELF::CMB_CACHE_KEY . $this->_sid);
            $result = $cmbFriendBiz->setAssistanceInfo($this->_mid, $this->_mid, $name);
            if ($result['code'] != 200) {
                $this->apiReturn('204', '', '插入失败');
            }
        }
        //获取是否有禁用的三坊七巷发布的年卡
        $stopAnnualCard = $cmbFriendBiz->getUserStopAnnual($this->_mid, $this->_sid);
        $type           = 0;
        $activeNum      = 0;
        $annualId       = 0;
        if ($stopAnnualCard && $stopAnnualCard['avalid_end'] > time()) {
            $type          = 1;
            $oneProductMdl = new \Model\Cmb\CmbFriend();
            $activeInfo    = $oneProductMdl->getOneAnnualProductByMid($this->_mid);
            //$activeNum     = ($activeInfo['active_num'] + 2) * 5;
            $annualId  = $stopAnnualCard['id'];
            $activeNum = $cmbFriendBiz->activeNumCheck($activeInfo['paytime'], $activeInfo['active_num']);
        }
        $list   = $cmbFriendBiz->getAssistance($this->_mid);
        $people = count($list) - 1;
        $price  = 0;
        foreach ($list as $key => $value) {
            $price += $value['price'];
        }
        $data = [
            'people'     => $people,
            'price'      => $price,
            'user_id'    => $this->_mid,
            'type'       => $type,
            'active_num' => $activeNum,
            'annual_id'  => $annualId,
        ];
        $this->apiReturn(200, $data, '获取成功');

    }

    /**
     * 获取年卡虚拟卡库存和总数
     * <AUTHOR>
     *
     * @param int pid 产品id
     *
     * @date   2018-11-28
     * @return array
     */
    public function getVirtualStorage()
    {
        if (($pid = I('pid')) < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $cardModel  = new CardModel();
        $virStorage = $cardModel->getAnnualCardStorage($this->_sid, $pid, 'virtual');
        $virTotal   = $cardModel->getAnnualCardTotal($this->_sid, $pid, 'virtual');

        $return = ['storage' => $virStorage, 'total' => $virTotal];

        $this->apiReturn(200, $return);
    }

    /**
     * 获取助力列表
     * <AUTHOR>
     *
     * @param int pid 产品id
     *
     * @date   2018-11-28
     * @return array
     */
    public function getAssistanceList()
    {
        $cmbFriendBiz = new \Business\Cmb\CmbFriendHelp();
        $list         = $cmbFriendBiz->getAssistance($this->_mid); //本人
        foreach ($list as $key => $value) {
            if ($value['mid'] == $value['uid']) {
                unset($list[$key]);
                continue;
            }
            $list[$key]['dname'] = empty($value['helpname']) ? '招行' : mb_substr($value['helpname'], 0, 1, 'UTF-8');
        }
        $list = array_values($list);
        $this->apiReturn(200, $list, '助力列表获取成功');
    }

    /**
     * 好友助力
     * <AUTHOR>
     *
     * @param int hid 需要助力的人
     *
     * @date   2018-11-28
     * @return array
     */
    public function friendAssistance()
    {
        $uid = I('post.hid', '');        //要帮忙助力的人
        if (empty($uid)) {
            $this->apiReturn(204, '', '助力人不能为空');
        } elseif ($uid == $this->_mid) {
            $this->apiReturn(204, '', '自己不能给自己助力呦');
        }
        $cmbFriendBiz = new \Business\Cmb\CmbFriendHelp();
        $cache        = \Library\Cache\Cache::getInstance('redis');
        $name         = $cache->get(SELF::CMB_CACHE_KEY . $this->_sid);
        $result       = $cmbFriendBiz->setAssistanceInfo($uid, $this->_mid, $name, $this->_sid);
        if ($result['code'] == 200) {
            $this->apiReturn(200, '', '助力成功');
        } else {
            $this->apiReturn(204, '', $result['msg']);
        }
    }

    /**
     * 年卡解除禁用
     * <AUTHOR>
     *
     * @param int id 年卡id
     *
     * @date   2018-11-28
     * @return array
     */
    public function cmbAnnualRemoveForbidden()
    {
        $uid = I('post.id', 0, 'intval');    //年卡id
        if (!$uid) {
            $this->apiReturn(204, '', 'id不能为空');
        }
        //解禁年卡
        $cmbFriendBiz = new \Business\Cmb\CmbFriendHelp();
        $result       = $cmbFriendBiz->cmbAnnualRemoveForbidden($this->_mid, $this->_sid, $uid);
        if ($result && $result['code'] == 200) {
            $this->apiReturn(200, '', '启用成功');
        } else {
            $this->apiReturn($result['code'], '', $result['msg']);
        }
    }
}