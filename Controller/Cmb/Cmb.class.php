<?php
/**
 * 招行三坊七巷基础类
 * User: linchen
 * Date: 18/11/23
 * Time: 14:29
 */

namespace Controller\Cmb;

use Library\Business\CMBPay\CmbPayApi;
use Library\Cache\Cache;
use Library\Controller;
use Model\Member\Member;

class Cmb extends Controller
{
    private $redisExpire = 900;
    protected $_sid;
    protected $_supplyId;
    const CMB_CACHE_KEY = 'CMB_CACHE_INFO:';

    public function __construct()
    {
        if (!$this->inCmbApp()) {
            exit('非法访问');
        }
        $this->_sid = I('account', '124472');
        $memModel = new Member('slave');
        $memberData = $memModel->getMemberInfo((string)$this->_sid, 'account');
        $this->_sid = $memberData['id'];
    }

    /**
     * 解密rsa秘钥
     * <AUTHOR>
     * @date   2018-11-28
     */
    public function rsa($secretKey)
    {
        //   $secretKey = 'MBjqdx9poEDhnZlRKA88vl0O3IL/qTxtMKVVJnm+r40VCR73lawo50+5vtbISCYIlAKJATuCkUQTIx61MpO7ap52TcdmCSTs/ROuRtb45/fF/iPKpfADkByHinVMNkYdNj7u+5YyoTKLQ2bzdmHB0NnRJBb4sOPsfEWG05wZprs=';
        //获取招行公钥
        $rsa = file_get_contents('/var/www/html/alipay/certs/cmbpay/sfqx_cacert.pem');
        $keyInfo = openssl_pkey_get_public($rsa);
        if (!$keyInfo) {
            return false;
        }
        $decryptData = '';
        $secretKey = base64_decode($secretKey);
        $result = openssl_public_decrypt($secretKey, $decryptData, $rsa);
        if ($result) {
            return $decryptData;
        } else {
            return false;
        }
    }
    /**
     * 验证token
     * <AUTHOR>
     * @date   2018-11-28
     * */
    public function checkToken()
    {
        $token = I('post.token', '');
        if (empty($token)) {
            return false;
        }
        $redis = Cache::getInstance('redis');
        $data = $redis->get($token);
        if ($data) {
            $result = unserialize($data);
            $redis->expire($token, $this->redisExpire);
            return $result;
        } else {
            return false;
        }
    }

    /**
     * 是否是招行的app过来的判断是否在招行app
     * @return bool
     */
    public function inCmbApp()
    {
        $userAgent = $_SERVER['HTTP_USER_AGENT'];
        if (stripos($userAgent, 'MPBank') === false) {
            return false;
        } else {
            return true;
        }
    }
    /**
     * 测试专用生成用户id
     * <AUTHOR>
     * @date   2018-11-28
     */
    public function jiami(){
        $name = I('post.name','linchen');
        $data = explode(',',$name);
//        $rsa = file_get_contents('/var/www/html/Service/Public/sfqx_private_key_pkcs8.pem');
        $rsa = file_get_contents('/var/www/html/alipay/certs/cmbpay/sfqx_private_key_pkcs8.pem');
        $encrypted = '';
//        $data = $data[0].';;'.$data[1].';;招林'.rand(1000,9999).';;'.time().'123'.';;'.$data[2];
        $data = $data[0].';;'.$data[1].';;招林'.rand(1000,9999).';;'.time().'123';
        openssl_private_encrypt($data, $encrypted, $rsa);
        $this->apiReturn(200,base64_encode($encrypted));
    }
    /**
     * 获取产品和供应商配置
     * <AUTHOR>
     * @date   2018-11-28
     */
    public function getProductConfig(){
        $product = load_config('product','cmb_pay');
        $this->apiReturn('200',$product);
    }
}