<?php
/**
 * 营销短信控制基类
 * <AUTHOR>
 * @date 2020/3/27 0027
 */

namespace Controller\MarketingSms;

use Library\Constants\MemberConst;
use Library\Controller;

class MsBase extends Controller
{
    protected $_params;
    protected $_sid;
    protected $_dtype;
    protected $_sdtype;
    protected $_memberId;

    //唯一标识
    const _TAGNAME_ = 'marketing_sms';

    public function __construct()
    {
        parent::__construct();
        $this->_params = I('param.');
        $this->_params = (object)$this->_params;

        $loginInfo          = $this->getLoginInfo();
        $this->_sid         = $loginInfo['sid'];//上级用户ID
        $this->_dtype       = $loginInfo['dtype'];//当前账号类型
        $this->_sdtype      = $loginInfo['sdtype'];//上级账号类型
        $this->_memberId    = $loginInfo['memberID'];//用户ID

        $isOpen = $this->checkUseModule($this->_sid);
        if (!$isOpen && !in_array($this->_sdtype, [MemberConst::ROLE_APPLY])) {
            $this->apiReturn(500, [], '未开通应用');
        }
    }

    /**
     * 客户端请求参数验证
     * <AUTHOR>
     * @date 2020/10/27
     *
     * @param array $params  请求参数对象
     * @param array $keyArr  验证的字段名数组
     *
     * @return array|bool
     */
    public function _verifyParams($params, $keyArr)
    {
        if (empty($params) || empty($keyArr)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数缺失');
        }
        $msg = '';
        foreach ($keyArr as $key) {
            if (!isset($params->$key)) {
                $msg = "$key 参数不存在";
                break;
            }
        }
        if (!empty($msg)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], $msg);
        }

        return true;
    }

    /**
     * 是否开通应用
     * <AUTHOR>
     * @date 2021/05/26
     *
     * @param  int  $sid  商家id  上级账号
     *
     * @return bool
     */
    public function checkUseModule(int $sid)
    {
        $moduleBiz = new \Business\AppCenter\Module();
        $result    = $moduleBiz->checkUserIsCanUseApp($sid, self::_TAGNAME_, true);
        if (!$result) {
            return false;
        }

        return true;
    }
}