<?php
/**
 * 营销短信相关接口
 * <AUTHOR>
 * @date 2020/12/29
 */

namespace Controller\MarketingSms;

use Business\MarketingSms\Statis;
use Business\SmsDiy\Manage as ManageBiz;
use Business\SmsDiy\Sign as SignBiz;
use Library\Tools;
use Model\SmsDiy\SmsSignApply as SignModel;
use Library\Tools\Helpers;

class SendSms extends MsBase
{

    private $signNameGover = '票付通';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 文件上传
     * <AUTHOR>
     * @date 2021/5/27
     *
     * @return array
     */
    public function uploadFile()
    {
        $file = $_FILES['file'];

        //如果临时文件不存在
        if (empty($file['tmp_name'])) {
            $this->apiReturn(400, [], "文件不存在");
        }

        //文件有效判断
        if ($file['size'] <= 0 && $file['error'] == 1) {
            $this->apiReturn(203, [], '文件无法识别，请重试');
        } elseif ($file['size'] <= 0) {
            $this->apiReturn(203, [], '文件内容不能为空');
        }

        //允许上传的文件类型
        $allowType = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
        ];

        //验证文件类型
        if (!in_array($file['type'], $allowType)) {
            $this->apiReturn(203, [], '上传的文件类型不对');
        }

        $time = time();

        //获取文件名
        $fileName = substr($file['name'], 0, strrpos($file['name'], '.'));
        $fileName = "{$fileName}_{$time}";
        $filePath = "marketing_sms/{$this->_sid}/execl/{$fileName}";

        //截取文件的后缀名
        $suffix = strrchr($file['name'], '.');
        //只允许上传文件
        $zip = ['.xlsx', '.xls', '.csv'];
        if (!in_array($suffix, $zip)) {
            $this->apiReturn(400, [], "只允许上传execl文件");
        }

        //上传文件
        $result = Helpers::uploadFileAliOss($suffix, $filePath, $file['tmp_name']);
        if ($result['code'] != 200) {
            $this->apiReturn(400, [], $result['msg']);
        }

        $sendSmsLib = new \Business\MarketingSms\SendSms();
        $addFileRes = $sendSmsLib->addFile($result['data']['src'], $this->_memberId);

        if ($addFileRes['code'] != 200 || empty($addFileRes['data']['id'])) {
            pft_log('MarketingSms/SendSms/uploadFile/error', "uid:{$this->_sid}||src:{$result['data']['src']}");
            $this->apiReturn(400, [], "失败");
        }

        $result['data']['id'] = (int)$addFileRes['data']['id'];

        $sendSmsLib->pushFileUpload($result['data']['id']);

        $this->apiReturn(200, $result['data'], "文件上传成功");
    }

    /**
     * 获取余额/预存
     * <AUTHOR>
     * @date 2021/5/27
     *
     * @return array
     */
    public function queryMoney()
    {
        $uid    = $this->_sid; //供应商ID
        $params = $this->_params;
        self::_verifyParams($params, ['id']);
        $id = intval($params->id);

        $sendSmsLib = new \Business\MarketingSms\SendSms();
        $result     = $sendSmsLib->queryMoney($uid, $id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);

    }

    /**
     * 获取剩余的发送条数
     * <AUTHOR>
     * @date 2021/5/26
     *
     * @return json
     */
    public function querySendNum()
    {
        $applyDid   = $this->_sid; //供应商ID
        $sendSmsLib = new \Business\MarketingSms\SendSms();
        $listRes    = $sendSmsLib->querySendNum($applyDid);

        //获取抬头
        $listRes['data']['sign_id']   = 0;
        $listRes['data']['sign_name'] = $this->signNameGover;

        if ($listRes['data']['raise'] == 1) {
            $this->apiReturn($listRes['code'], $listRes['data'], $listRes['msg']);
        }

        $manageBiz = new SignBiz();
        if ($manageBiz->checkUseModule($this->_sid)) {
            //自定义抬头
            $signModel = new SignModel();
            $applyInfo = $signModel->getInfo($this->_sid, 0, $signModel::_auditStatusSuccess);
            if (!empty($applyInfo)) {
                $listRes['data']['sign_id']   = $applyInfo['sign_id'];
                $listRes['data']['sign_name'] = $applyInfo['sign_name'];
            }
        }

        $this->apiReturn($listRes['code'], $listRes['data'], $listRes['msg']);
    }

    /**
     * 添加任务
     * <AUTHOR>
     * @date 2021/5/27
     *
     * @return array
     */
    public function addTask()
    {
        $uid        = $this->_sid; //供应商ID
        $operaterId = $this->_memberId; //操作用户ID

        $params = $this->_params;
        self::_verifyParams($params, ['pType', 'isTiming', 'content', 'signName']);
        $pType    = intval($params->pType); //推送人员类型 1=用户画像配置 2=导入手机号 3=输入手机号 4=选择用户类型
        $isTiming = intval($params->isTiming); //是否定时发送 1=定时发送 2=非定时发送
        $content  = $params->content; //短信内容
        $signId   = !empty($params->signId) ? intval($params->signId) : 0; //抬头
        $signName = $params->signName; //抬头名称

        $pushTypeId = !empty($params->pushTypeId) ? intval($params->pushTypeId) : 0; //不同推送类型的业务ID
        $sendTime   = !empty($params->sendTime) ? strtotime($params->sendTime) : 0; //发送时间

        $mobiles = !empty($params->mobiles) ? $params->mobiles : ''; //输入手机号

        $oldUrl = !empty($params->oldUrl) ? $params->oldUrl : ''; //旧链接
        $newUrl = !empty($params->newUrl) ? $params->newUrl : ''; //新链接

        //判断抬头权限
        if ($signId != 0) {
            $manageBiz = new SignBiz();
            if ($manageBiz->checkUseModule($this->_sid)) {
                //自定义抬头
                $signModel = new SignModel();
                $applyInfo = $signModel->getInfo($this->_sid, 0, $signModel::_auditStatusSuccess);
                if (empty($applyInfo)) {
                    $this->apiReturn(203, [], '未配置自定义抬头');
                }
                if ($applyInfo['sign_id'] != $signId) {
                    $this->apiReturn(203, [], '抬头ID参数错误');
                }
                if ($applyInfo['sign_name'] != $signName) {
                    $this->apiReturn(203, [], '抬头名称参数错误');
                }
            } else {
                $this->apiReturn(203, [], '无自定义抬头权限');
            }
        } else {
            if ($signName != $this->signNameGover) {
                $this->apiReturn(203, [], '抬头参数有误');
            }
        }


        $sendSmsLib = new \Business\MarketingSms\SendSms();
        $result     = $sendSmsLib->addTask($uid, $pType, $pushTypeId, $operaterId, $isTiming, $sendTime, $signId,
            $signName,
            $content, $mobiles, $oldUrl, $newUrl);

        if ($result['code'] == 200 && ($pType == 1 || $pType == 2 || $pType == 4)) {
            $sendSmsLib->pushTaskAfter($result['data']['id']);
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取短链接
     * <AUTHOR>
     * @date 2020/6/23
     *
     * @return array
     */
    public function shortUrl()
    {
        $url = I('post.url', '', 'strval');//链接

        if (empty($url)) {
            $this->apiReturn(203, [], '参数缺失!');
        }

        $shortUrl = Tools::transformShortUrl($url);
        if ($shortUrl === false) {
            $this->apiReturn(400, [], '短链接生成失败!');
        }

        $shortUrl = str_replace('http://', '', $shortUrl);
        $shortUrl = str_replace('https://', '', $shortUrl);

        $this->apiReturn(200, ['url' => $shortUrl], '');
    }

    /**
     * 分页获取发送任务列表
     * <AUTHOR>
     * @date 2021/6/8
     *
     * @return array
     */
    public function queryTaskPage()
    {
        $params = $this->_params;
        self::_verifyParams($this->_params, ['page', 'size']);
        $uid           = $this->_sid; //供应商ID
        $page          = intval($this->_params->page);
        $size          = intval($this->_params->size);
        $state         = !empty($params->state) ? intval($params->state) : null; //发送状态 1=待发送 2=发送中 3=发送完成 4=发送失败
        $userAccount   = !empty($params->userAccount) ? $params->userAccount : null; //账号
        $userName      = !empty($params->userName) ? $params->userName : null; //名称
        $startSendTime = !empty($params->startSendTime) ? intval($params->startSendTime) : null; //发起开始时间
        $endSendTime   = !empty($params->endSendTime) ? intval($params->endSendTime) : null; //发起结束时间

        $recordBiz = new \Business\MarketingSms\SendSms();
        $result    = $recordBiz->queryTaskPage($uid, $page, $size, $state, $userAccount, $userName, $startSendTime,
            $endSendTime);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取发送任务详情
     * <AUTHOR>
     * @date 2021/6/8
     *
     * @return array
     */
    public function queryTaskDetailById()
    {
        self::_verifyParams($this->_params, ['id']);
        $uid = $this->_sid; //供应商ID
        $id  = intval($this->_params->id);

        $recordBiz = new \Business\MarketingSms\SendSms();
        $result    = $recordBiz->queryTaskDetailById($id, $uid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 分页获取发送任务列表
     * <AUTHOR>
     * @date 2021/6/8
     *
     * @return array
     */
    public function queryRecordDetailPage()
    {
        $params = $this->_params;
        self::_verifyParams($this->_params, ['page', 'size', 'taskId']);
        $taskId = intval($this->_params->taskId);
        $page   = intval($this->_params->page);
        $size   = intval($this->_params->size);
        $state  = !empty($params->state) ? intval($params->state) : null; //发送状态 1=待发送 2=发送中 3=发送完成 4=发送失败
        $mobile = !empty($params->mobile) ? $params->mobile : null; //手机号

        $recordBiz = new \Business\MarketingSms\SendSms();
        $result    = $recordBiz->queryRecordDetailPage($taskId, $page, $size, $state, $mobile);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 汇总统计
     * <AUTHOR>
     * @date 2021/6/8
     *
     * @return array
     */
    public function statisTotal()
    {
        $uid = $this->_sid; //供应商ID

        $recordBiz = new Statis();
        $result    = $recordBiz->statisTotal($uid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 月统计
     * <AUTHOR>
     * @date 2021/6/8
     *
     * @return array
     */
    public function statisMonth()
    {

        $params = $this->_params;
        self::_verifyParams($params, ['page', 'size']);

        $uid  = $this->_sid; //供应商ID
        $page = intval($params->page); //页码
        $size = intval($params->size); //大小

        $recordBiz = new Statis();
        $result    = $recordBiz->statisMonth($uid, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取配置信息
     * <AUTHOR>
     * @date 2021/5/26
     *
     * @return json
     */
    public function queryConfigInfo()
    {
        $applyDid   = $this->_sid; //供应商ID
        $sendSmsLib = new \Business\MarketingSms\SendSms();
        $listRes    = $sendSmsLib->querySendNum($applyDid);

        $listRes['data']['hasTai']     = 2;//是否有自定义抬头权限 1=有 2=无
        $listRes['data']['prestorage'] = 2;//是否有专项预存权限 1=有 2=无

        $manageBiz = new SignBiz();
        if ($manageBiz->checkUseModule($applyDid)) {
            $signBiz = new SignBiz();
            $signDiy = $signBiz->getSignBySid($applyDid);
            if ($signDiy['data']['is_diy'] == 1) {
                $listRes['data']['hasTai'] = 1;
            }
        }

        $adminConfModel = new \Model\AdminConfig\AdminConfig();

        $prestorageRes  = $adminConfModel->getAppCenterByKey('prestorage');
        $prestorageId   = isset($prestorageRes['id']) ? $prestorageRes['id'] : 0;
        $checkPowerRes  = $adminConfModel->havePermission($applyDid, $prestorageId);

        if ($checkPowerRes) {
            $listRes['data']['prestorage'] = 1;
        }

        $this->apiReturn($listRes['code'], $listRes['data'], $listRes['msg']);
    }

    /**
     * 设置
     * <AUTHOR>
     * @date 2021/6/8
     *
     * @return array
     */
    public function setConfig()
    {
        $params = $this->_params;
        self::_verifyParams($params, ['recType', 'raise']);
        $uid        = $this->_sid; //供应商ID
        $operaterId = $this->_memberId; //操作人ID
        $recType    = intval($params->recType); //优先扣费方式 1=余额 2=短信专项预存
        $raise      = intval($params->raise); //抬头 1=标准 2=自定义

        $sendSmsLib = new \Business\MarketingSms\SendSms();
        $result     = $sendSmsLib->setConfig($uid, $recType, $raise, $operaterId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 内容检测内容检测
     * <AUTHOR>
     * @date 2021/6/8
     *
     * @return array
     */
    public function validateContent()
    {
        $params = $this->_params;
        self::_verifyParams($params, ['text']);
        $content = $params->text; //要检测的内容

        $sendSmsLib = new \Business\MarketingSms\SendSms();
        $result     = $sendSmsLib->validateContent($content);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 月统计
     * <AUTHOR>
     * @date 2021/6/8
     *
     * @return array
     */
    public function tetstset()
    {
        $uid = $this->_sid; //供应商ID
        // 供应商员工是没有权限的 所以用登录账号的id
        $adminConfModel = new \Model\AdminConfig\AdminConfig();
        $checkPowerRes  = $adminConfModel->havePermission($uid, 60);
        //if (!$checkPowerRes) {
        //    $this->apiReturn(400, [], '只有管理员或开通权限的账号主账号可以强制取消');
        //}
        var_dump($checkPowerRes);
        exit;

        $params = $this->_params;
        self::_verifyParams($params, ['content']);
        $content = $params->content; //短信内容
        var_dump($content);
        exit;
    }

}
