<?php
/**
 * 用户画像
 * <AUTHOR>
 * @date 2021/5/27
 */

namespace Controller\MarketingSms;

class Draw extends MsBase
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 用户画像列表
     * <AUTHOR>
     * @date 2021/5/27
     *
     * @return array
     */
    public function queryDrawPage()
    {
        $uid = $this->_sid; //供应商ID

        $params = $this->_params;
        self::_verifyParams($this->_params, ['page', 'size']);
        $page            = intval($this->_params->page); //当前页
        $size            = intval($this->_params->size); //每页大小
        $name            = !empty($params->name) ? $params->name : null; //名称
        $startCreateTime = !empty($params->startCreateTime) ? intval($params->startCreateTime) : null; //创建时间筛选开始时间
        $endCreateTime   = !empty($params->endCreateTime) ? intval($params->endCreateTime) : null; //创建时间筛选结束时间
        $startModTime    = !empty($params->startModTime) ? intval($params->startModTime) : null; //修改时间筛选开始时间
        $endModTime      = !empty($params->endModTime) ? intval($params->endModTime) : null; //修改时间筛选结束时间
        $status          = !empty($params->status) ? $params->status : null; //状态

        $drawLib = new \Business\MarketingSms\Draw();
        $result  = $drawLib->queryDrawPage($uid, $page, $size, $name, $startCreateTime, $endCreateTime, $startModTime,
            $endModTime, $status);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 画像的用户列表
     * <AUTHOR>
     * @date 2021/5/27
     *
     * @return array
     */
    public function queryDrawUserPage()
    {
        $uid = $this->_sid; //供应商ID

        self::_verifyParams($this->_params, ['page', 'size', 'drawId']);
        $page   = intval($this->_params->page); //当前页
        $size   = intval($this->_params->size); //每页大小
        $drawId = intval($this->_params->drawId); //画像ID

        $drawLib = new \Business\MarketingSms\Draw();
        $result  = $drawLib->queryDrawUserPage($uid, $drawId, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除画像
     * <AUTHOR>
     * @date 2021/6/12
     *
     * @return json
     */
    public function delDraw()
    {
        self::_verifyParams($this->_params, ['id']);
        $id = intval($this->_params->id); //画像ID

        $uid        = $this->_sid; //供应商ID
        $operaterId = $this->_memberId; //操作用户ID

        $drawLib = new \Business\MarketingSms\Draw();
        $result    = $drawLib->delDraw($id, $uid, $operaterId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 编辑画像名称
     * <AUTHOR>
     * @date 2021/6/12
     *
     * @return json
     */
    public function modDrawName()
    {
        self::_verifyParams($this->_params, ['id', 'name']);
        $id = intval($this->_params->id); //画像ID
        $name = $this->_params->name; //画像ID

        $uid        = $this->_sid; //供应商ID
        $operaterId = $this->_memberId; //操作用户ID

        $drawLib = new \Business\MarketingSms\Draw();
        $result    = $drawLib->modDrawName($id, $name, $uid, $operaterId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取用户画像规则
     * <AUTHOR>
     * @date 2021/6/12
     *
     * @return json
     */
    public function queryDrawRule()
    {
        self::_verifyParams($this->_params, ['drawId']);
        $drawId = intval($this->_params->drawId); //画像ID

        $drawLib = new \Business\MarketingSms\Draw();
        $result    = $drawLib->queryDrawRule($drawId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 创建画像
     * <AUTHOR>
     * @date 2021/6/12
     *
     * @return json
     */
    public function createDraw()
    {
        self::_verifyParams($this->_params, ['name', 'rule']);
        $name = $this->_params->name; //画像ID
        $rule = $this->_params->rule; //画像ID

        $uid        = $this->_sid; //供应商ID
        $operaterId = $this->_memberId; //操作用户ID

        $drawLib = new \Business\MarketingSms\Draw();
        $result    = $drawLib->createDraw($name, $rule, $uid, $operaterId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取应用信息
     * <AUTHOR>
     * @date 2021/6/12
     *
     * @return json
     */
    public function moduleInfo()
    {
        $uid        = $this->_sid; //供应商ID
        $sdtype     = $this->_sdtype;
        $drawLib    = new \Business\MarketingSms\Draw();
        $result     = $drawLib->moduleInfo($uid, $sdtype);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取自供应产品
     * <AUTHOR>
     * @date 2021/8/13
     *
     * @return json
     */
    public function queryTicketInfoByApplyDid()
    {
        self::_verifyParams($this->_params, ['page', 'size']);
        $page = $this->_params->page; //画像ID
        $size = $this->_params->size; //画像ID

        $type = !empty($this->_params->type) ? $this->_params->type : null;
        $name = !empty($this->_params->name) ? $this->_params->name : null;

        $uid        = $this->_sid; //供应商ID
        $drawLib    = new \Business\MarketingSms\Draw();
        $result     = $drawLib->queryTicketInfoByApplyDid($uid, $page, $size, $type, $name);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取用户画像信息
     * <AUTHOR>
     * @date 2021/8/20
     *
     * @return array
     */
    public function queryDrawInfo()
    {
        self::_verifyParams($this->_params, ['drawId']);
        $drawId = intval($this->_params->drawId); //每页大小

        $drawLib    = new \Business\MarketingSms\Draw();

        $result = $drawLib->queryDrawInfo($drawId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取下单渠道
     * <AUTHOR>
     * @date 2021/8/20
     *
     * @return array
     */
    public function queryOrderChannel()
    {
        $drawLib = new \Business\MarketingSms\Draw();
        $result  = $drawLib->queryOrderChannel();

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 判断是否能创建画像
     * <AUTHOR>
     * @date 2021/8/20
     *
     * @return array
     */
    public function isCreateDraw()
    {
        $uid = $this->_sid; //供应商ID

        $drawLib = new \Business\MarketingSms\Draw();
        $result  = $drawLib->isCreateDraw($uid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

}