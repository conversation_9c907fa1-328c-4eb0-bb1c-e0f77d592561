<?php
/**
 * 托管发票控制层
 */

namespace Controller\ElectronicInvoices;

use Business\ElectronicInvoices\AgentInvoiceApi;
use Business\ElectronicInvoices\ECBase;
use Library\Controller;

class AgentInvoice extends Controller
{
    private $loginInfo = [];
    private $sid       = 0;
    private $memberId  = 0;

    public function __construct()
    {
        $loginInfo = $this->getLoginInfo();
        if (empty($loginInfo['memberID'])) {
            $this->apiReturn(201, [], '请先登陆');
        }

        $this->loginInfo = $loginInfo;
        $this->sid       = $loginInfo['sid'];
        $this->memberId  = $loginInfo['memberID'];
    }

    /**
     * 获取托管开票规则
     */
    public function getRuleConfig()
    {
        $ruleInfo = (new AgentInvoiceApi())->getAgentRuleConfig($this->sid);
        if ($ruleInfo['code'] != 200 || empty($ruleInfo['data'])) {
            $this->apiReturn(200, [], '获取失败');
        }

        $this->apiReturn(200, $ruleInfo['data'], '获取成功');
    }

    /**
     * 保存托管开票规则配置
     */
    public function saveRuleConfig()
    {
        $id     = I('post.id', 0, 'intval');        //配置id
        $status = I('post.status', 0, 'intval');   //状态 1=开启 2=关闭
        $rules  = I('post.rules', []);                          //开票额度规则

        if (!$status || ($status == 1 && empty($rules))) {
            $this->apiReturn(203, [], '参数错误');
        }

        $result = (new AgentInvoiceApi())->saveAgentRuleConfig($this->sid, $id, $this->memberId, $status, $rules);
        if ($result['code'] != 200) {
            $this->apiReturn(204, [], $result['msg'] ?: '保存失败');
        }

        $this->apiReturn(200, [], '保存成功');
    }

    /**
     * 获取可开科目
     */
    public function getSubjectList()
    {
        $spbm     = I('spbm', '', 'strval');     //税收分类编码
        $spName   = I('sp_name', '', 'strval');  //商品名称
        $pageNum  = I('page_num', 1, 'intval');
        $pageSize = I('page_size', 15, 'intval');
        $res = (new AgentInvoiceApi())->getSubjectList($this->sid, $pageNum, $pageSize, $spName, $spbm);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }
        $this->apiReturn($res['code'], ['list' => $res['data']['list'] ?? [], 'total' => $res['data']['total'] ?? 0, 'current_page' => $res['data']['current_page'] ?? $pageNum]);
    }

    /**
     * 获取可开科目详情
     */
    public function getSubjectInfo()
    {
        $id  = I('id', 0, 'intval');
        $res = (new AgentInvoiceApi())->getSubjectInfo($this->sid, $id);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }
        $this->apiReturn($res['code'], $res['data']);
    }

    /**
     * 保存可开科目
     */
    public function saveSubject()
    {
        $id      = I('post.id', 0, 'intval');           //配置ID
        $name    = I('post.name', '', 'strval');        //商品名称
        $spbm    = I('post.spbm', '', 'strval');        //税收分类编码
        $taxRate = I('post.tax_rate', '', 'strval');    //税率

        //获取模型
        $result = (new AgentInvoiceApi())->saveSubject($this->sid, $id, $name, $spbm, $taxRate);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除可开科目
     */
    public function delSubject()
    {
        $id      = I('post.id', 0, 'intval');           //配置ID

        //获取模型
        $result = (new AgentInvoiceApi())->delSubject($this->sid, $id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 查询开票额度
     */
    public function getInvoiceLimit()
    {
        $fid  = I('fid', 0, 'intval');  //分销商
        $res = (new AgentInvoiceApi())->getInvoiceLimit($this->sid, $fid);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 测试扣减额度
     */
    public function testDec()
    {
        $param = [
            'sid' => $this->sid,
            'fid' => 10221256,
            'serial_no' => '111111111111',
            'trade_no' => '111111111111',
            'amount' => 1000,
        ];
        $res = (new ECBase())->ExternalCodeCall('index/AgentInvoice/reduceInvoiceLimit', $param);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

}
