<?php
/**
 * 诺诺发票回调地址
 * <AUTHOR> Li
 * @date 2018-11-22
 */

namespace Controller\ElectronicInvoices;

use Business\ElectronicInvoices\Invoice as invoiceBiz;
use Library\Controller;
use Model\Order\OrderQuery;
use Library\Business\NuoInvoice as invoiceLib;
use Library\Cache\Cache as Cache;
use Business\ElectronicInvoices\InvoiceApi;

class Callback extends Controller
{
    private $_configure = [];

    public function __construct()
    {
        $this->_configure = load_config('nuonuo', 'electronic_invoice');
        if (empty($this->_configure)) {
            $this->outApiReturn(401, [], '配置文件出错');
        }
    }

    /**
     * 首次申请授权的回调处理
     *
     * <AUTHOR> Li
     * @date 2018-11-22
     */
    public function callback()
    {
        $code   = I('get.code');
        $taxnum = I('get.taxnum');
        $sid    = I('get.state');

        //获取到code 和 taxnum  通过这两个参数获取access_token 及 refresh_token
        if ((!isset($code) && empty($code)) || (!isset($taxnum) && empty($taxnum)) || empty($sid)) {
            header("Location: " . MY_DOMAIN . "new/userInvoice_orders.html#/authorize?state=" . $sid . '&code=500');
            exit;
        }

        $invoiceLib = new invoiceLib();
        $result     = $invoiceLib->getTokenByCode($code, $taxnum, $sid);

        pft_log('Invoice/debug', json_encode([
            'ac'     => 'authorization_code_result',
            'code'   => $code,
            'taxnum' => $taxnum,
            'result' => $result,
        ]));

        if ($result === false || empty($result)) {
            header("Location: " . MY_DOMAIN . "new/userInvoice_orders.html#/authorize?state=" . $sid . '&code=500');
            exit;
        }

        $result = json_decode($result, true);
        //失败后直接跳转到指定页面
        if (isset($result['error']) && !empty($result['error'])) {
            header("Location: " . MY_DOMAIN . "new/userInvoice_orders.html#/authorize?state=" . $sid . '&code=500');
            exit;
        }

        $oauthUser = json_decode($result['oauthUser'], true);

        pft_log('Invoice/debug', json_encode([
            'ac'        => 'authorization_code',
            'code'      => $code,
            'taxnum'    => $taxnum,
            'result'    => $result,
            'oauthUser' => $oauthUser,
        ]));

        //数据异常直接返回500
        if (empty($result) || empty($oauthUser)) {
            header("Location: " . MY_DOMAIN . "new/userInvoice_orders.html#/authorize?state=" . $sid . '&code=500');
            exit;
        }

        //航信授权回调之后少了系统id会导致后续自动刷新refresh_token功能会有问题 所以这边如果是基础服务授权回调的 需要默认开票系统为航信
        $sysId = 0;
        //将票付通的access_token 存到缓存中
        if ($oauthUser['userName'] == $this->_configure['userTax'] && $sid == 1) {
            $redis  = Cache::getInstance('redis');
            $access = "access_token";
            $redis->set($access, $result['access_token']);

            pft_log('Invoice/debug', json_encode([
                'ac'            => '刷新缓存',
                'access_token'  => $result['access_token'],
                'refresh_token' => $result['refresh_token'],
                'userName'      => $oauthUser['userName'],
                'time'          => date('Y-m-d H:i:m', time()),
            ], JSON_UNESCAPED_UNICODE));
            $sysId = 1;
        }

        //设置过期时间
        $expireTime = time() + ($result['expires_in'] ?? 86400);
        //如果是管理端 刷新的是极速开票应用的token  需要制定商家更新
        //获取模型
        $invoiceApi = new InvoiceApi();
        //通过得到用户税号 对应修改 access_token和refresh_token之后保存到数据库中
        $res = $invoiceApi->editEnterpriseInfoByTariffNumber($oauthUser['userName'], $result['access_token'],
            $result['refresh_token'], $result['userId'], $sid, $expireTime, $sysId);

        //失败后直接跳转到指定页面
        if ($res['code'] != 200) {
            header("Location: " . MY_DOMAIN . "new/userInvoice_orders.html#/authorize?state=" . $sid . '&code=500');
            exit;
        }
        //成功后直接跳转到指定页面
        header("Location: " . MY_DOMAIN . "new/userInvoice_orders.html#/authorize?state=" . $sid . '&code=200');

    }

    /**
     * 开票的回调处理
     * @deprecated 该回调已不再维护
     * <AUTHOR> Li
     * @date 2018-11-29
     */
    public function invoicesCallback()
    {
        $this->outApiReturn('0000', [], '通知成功');
//        $params = I('post.param');
//
//        $invoiceBiz = new invoiceBiz();
//        //获取解码参数
//        $param = $invoiceBiz->getParams($params, 1, $this->_configure['EappSecret']);
//        $param = json_decode($param, true);
//
//        $operation = $param['operation']; //操作类型    queryOrderInfo【根据订单号查询订单信息】   deliverInvoice【回传发票信息】
//        $orderNo   = $param['orderNo']; //订单号
//        $content   = $param['content']; //开票请求参数
//        //合并开票
//        if (strpos($orderNo, '_m_') !== false) {
//            pft_log('InvoiceMerge/debug',
//                json_encode(['合并回调数据', 'param: ' . json_encode($param), '请求时间： ' . date('Y-m-d H:i:m', time())],
//                    JSON_UNESCAPED_UNICODE));
//            $this->_merge($orderNo, $operation, $content);
//            exit();
//        }
//
//        $orderNo = explode('NNFP_', $orderNo); //处理订单号
//        $orderNo = $orderNo[1];
//
//        pft_log('Invoice/debug',
//            json_encode(['回调数据', 'param: ' . json_encode($param), '请求时间： ' . date('Y-m-d H:i:m', time())],
//                JSON_UNESCAPED_UNICODE));
//
//        if ((!isset($operation) || empty($operation)) || (!isset($orderNo) || empty($orderNo))) {
//            $this->outApiReturn('0101', [], '信息未填写完整');
//        }
//
//        //获取模型
//        $invoiceApi = new InvoiceApi();
//
//        //通过订单号先到旅游券检测下
//        $tvInvoiceApi = new \Business\JsonRpcApi\TravelVoucherService\OrderQuery();
//        $tvOrderInfo  = $tvInvoiceApi->getOrderInfoDetail([$orderNo], true);
//        $isTVOrder    = false;
//        if ($tvOrderInfo) {
//            $isTVOrder = true;
//            $javaApi   = new \Business\CommodityCenter\Ticket();
//            $ticketArr = $javaApi->queryTicketInfoByIds(array_column($tvOrderInfo, 'tid'), 'id,pid,title,tprice,batch_check');
//            $tickets   = [];
//            if (!empty($ticketArr)) {
//                foreach ($ticketArr as $ticket) {
//                    $tickets[$ticket['ticket']['id']] = $ticket['ticket'];
//                }
//            }
//            $orderInfo['ticket']      = $tickets;
//            $orderInfo['order']       = $tvOrderInfo;
//            $orderInfo['member_list'] = $tvOrderInfo['aids'];
//
//        } else {
//            //根据订单号查询订单
//            $result = $this->getOrderInfoByOrderNo($orderNo);
//            if ($result === false) {
//                $this->outApiReturn('0101', [], '未查到该订单信息');
//            }
//            $orderInfo = $result;
//        }
//
//        if (!$orderInfo) {
//            $this->outApiReturn('0101', [], '未查到该订单信息');
//        }
//
//        if (!$isTVOrder) {
//            //假如是子票  获取主票订单号来做处理
//            $orderNo = $invoiceBiz->getParentOrderNum($orderNo);
//        }
//
//        if ($operation == 'queryOrderInfo') {
//            //查找平台开票记录 该订单是否以开具过发票
//            $checkRes = $this->checkInvoiceRecordByOrderNo($orderNo);
//            if ($checkRes === false) {
//                $this->outApiReturn('0000', [], '此发票已开具纸票');
//            }
//
//            //查询出开票配置
//            $invoiceConf = $invoiceApi->getInvoiceConfigByTid($orderInfo['order']['tid'], $orderInfo['order']['apply_did'], true);
//            if ($invoiceConf['code'] != 200 || empty($invoiceConf['data'])) {
//                $this->outApiReturn('0101', [], '企业开票信息获取失败');
//            }
//            $invoiceConf = $invoiceConf['data'];
//            $invoiceInfo = $invoiceApi->getEnterpriseOpenRecordsList($orderInfo['order']['apply_did'], $invoiceConf['billing_party']);
//            if ($invoiceInfo['code'] != 200 || empty($invoiceInfo['data'])) {
//                $this->outApiReturn('0101', [], '企业开票信息获取失败');
//            }
//
//            $invoiceInfo = $invoiceInfo['data'][0];
//
//            //获取开票人相关配置
//            $billerInfo = $invoiceApi->getBillerConfig($orderInfo['order']['apply_did']);
//            if ($billerInfo['code'] != 200 || empty($billerInfo['data'])) {
//                $this->outApiReturn($billerInfo['code'], $billerInfo['data'], $billerInfo['msg']);
//            }
//            $invoiceApi     = new InvoiceApi();
//            $zdyName = [];
//            $zdyNameRes = $invoiceApi->productDefinitionNameList(0, 0, $orderInfo['order']['tid'], '', '', 0, 0);
//            if ($zdyNameRes['code'] == 200 && $zdyNameRes['data']['list']) {
//                $zdyName = $zdyNameRes['data']['list'][0];
//            }
//
//            $billerInfo = $billerInfo['data'];
//            $ext        = $billerInfo['expand'] ? json_decode($billerInfo['expand'],true) : [];
//            $deptid     = isset($ext['deptid']) && !empty($ext['deptid']) ? strval($ext['deptid']) : '';
//            $dutyFree   = isset($ext['duty_free']) && !empty($ext['duty_free']) ? intval($ext['duty_free']) : 0;
//            //默认是免税的
//            $zzstsgl = '免税';
//            $lslbs   = 1;
//            $yhzcbs  = 1;
//            $taxRate = 0;
//
//            //税率标识 增值税特殊管理配置处理
//            if (!$dutyFree && $invoiceConf['tax_rate'] > 0) {
//                $yhzcbs  = 0;
//                $zzstsgl = '';
//                $lslbs   = 0;
//                $taxRate = round($invoiceConf['tax_rate'], 2);
//            }
//            $ticketTitle = $orderInfo['ticket'][$orderInfo['order']['tid']]['title'];
//            if ($zdyName) {
//                $ticketTitle     = $zdyName['name'];
//            }else{
//                if (mb_strlen($ticketTitle) > 9) {
//                    $ticketTitle = mb_substr($ticketTitle, 0, 9) . '...';
//                }
//            }
//
//            $returnData = [
//                'buyerName'           => $orderInfo['order']['ordername'],
//                'buyerPhone'          => $orderInfo['order']['contacttel'],
//                'saletaxnum'          => $invoiceInfo['tariff_number'],
//                'buyerAccount'        => '',
//                'buyerAddress'        => '',
//                'buyerEmail'          => '',
//                'multiOrderInfo'      => [
//                    [
//                        'bhtaxtotal'  => '',
//                        'checker'     => isset($billerInfo['reviewer']) ? $billerInfo['reviewer'] : '',
//                        'clerk'       => isset($billerInfo['drawer']) ? $billerInfo['drawer'] : '管理员',
//                        'clerkid'     => '',
//                        'deptid'      => $deptid,
//                        'detail'      => [
//                            [
//                                'fphxz'      => 0,
//                                'goodsname'  => $ticketTitle, //票名称
//                                'hsbz'       => 1, //单价含税标sdf志   0 不含税 1 含税
//                                'lslbs'      => $lslbs, //单价含税标sdf志   0 不含税 1 含税
//                                'num'        => $orderInfo['order']['tnum'], //数量
//                                'price'      => $orderInfo['order']['tprice'] / 100, //单价
//                                'spbm'       => $invoiceConf['spbm'],
//                                'spec'       => '',
//                                'tax'        => '',
//                                'taxamt'     => $orderInfo['order']['tprice'] / 100,
//                                'taxfreeamt' => '',
//                                'taxrate'    => $taxRate,
//                                'unit'       => '',
//                                'yhzcbs'     => $yhzcbs,
//                                'zsbm'       => '',
//                                'zzstsgl'    => $zzstsgl,
//                            ],
//                        ],
//                        'invoicedate' => $orderInfo['order']['ordertime'],
//                        'kptype'      => 1,
//                        'message'     => '',
//                        'orderno'     => 'NNFP_' . $orderInfo['order']['ordernum'], //订单号
//                        'ordertotal'  => $orderInfo['order']['totalmoney'] / 100, //订单总价
//                        'payee'       => isset($billerInfo['payer']) ? $billerInfo['payer'] : '',
//                        'qdbz'        => 0,
//                        'qdxmmc'      => '',
//                        'saleaccount' => '',
//                        'salephone'   => '',
//                        'saletaxnum'  => $invoiceInfo['tariff_number'],
//                        'self_flag'   => '1',
//                        'taxtotal'    => $orderInfo['order']['totalmoney'],
//                        'tsfs'        => '-1',
//                    ],
//                ],
//                'phone'               => '',
//                'scanMultiInvoiceVos' => [],
//                'self_flag'           => '1',
//                'taxnum'              => '',
//                'telephone'           => '',
//            ];
//            pft_log('invoice/hanxinCallback', json_encode(['ordernum'=>$orderNo, 'returnData'=>$returnData]));
//            $this->outApiReturn('0000', $returnData, '请求成功');
//
//        } else if ($operation == 'deliverInvoice') {
//            //增加异常捕获
//            if (isset($content['errorMessage']) && !isset($content['invoiceImageUrl'])) {
//                $this->outApiReturn('0101', [], $content['errorMessage']);
//            }
//
//            //查找平台开票记录 该订单是否以开具过发票
//            $checkRes = $this->checkInvoiceRecordByOrderNo($orderNo);
//            if ($checkRes === false) {
//                $this->outApiReturn('0000', [], '此发票已开具纸票');
//            }
//
//            //这边获取下是否开启了 分销订单开票 开启后支持分销链上的订单也允许开票，开票流程同直销订单，只支持末端开票 0关闭 1开启
//            //查询出开票配置
//            $invoiceConf = $invoiceApi->getInvoiceConfigByTid($orderInfo['order']['tid'], $orderInfo['order']['apply_did'], true);
//            if ($invoiceConf['code'] != 200 || empty($invoiceConf['data'])) {
//                $this->outApiReturn('0101', [], '企业开票信息获取失败');
//            }
//            $invoiceConf = $invoiceConf['data'];
//            //只支持末端开票
//            if (!empty($invoiceConf['distribution'])) {
//                $memberId = end($orderInfo['member_list']);
//            }else{
//                $memberId = $orderInfo['member_list'][1];
//            }
//
//            $enterPriseInfo = $invoiceApi->getEnterpriseOpenRecordsByMixed($orderInfo['order']['apply_did'],
//                $content['salerTaxNum']);
//            if ($enterPriseInfo['code'] != 200) {
//                $this->outApiReturn('0101', [], '开票企业查询失败');
//            }
//
//            $timestmp = time();
//
//            $data = [
//                'out_trade_no'        => $this->getTradeNo(),
//                'serial_num'          => $content['invoiceSerialNum'],
//                'ordernum'            => $orderNo,
//                'add_time'            => $timestmp,
//                'image_url'           => $content['invoiceImageUrl'],
//                'sid'                 => $orderInfo['order']['apply_did'],
//                'member_id'           => $memberId,
//                'ordertel'            => $orderInfo['order']['ordertel'],
//                'personid'            => $orderInfo['order']['personid'],
//                'operator_id'         => $orderInfo['order']['apply_did'],
//                'enterprise_name'     => $enterPriseInfo['data']['enterprise_name'],
//                'enterprise_code'     => $enterPriseInfo['data']['enterprise_code'],
//                'tariff_number'       => $enterPriseInfo['data']['tariff_number'],
//                'invoice_code'        => $content['invoiceCode'],
//                'invoice_num'         => $content['invoiceNum'],
//                'original_order_info' => [
//                    'tnum'       => $orderInfo['order']['tnum'],
//                    'tprice'     => $orderInfo['order']['tprice'],
//                    'totalmoney' => $orderInfo['order']['totalmoney'],
//                ],
//            ];
//
//            //添加开票记录
//            $result = $this->addEnterpriseInvoiceRecords($data);
//
//            if ($result !== false) {
//                $this->outApiReturn('0000', [], '通知成功');
//            } else {
//                $this->outApiReturn('0101', [], '参数或接口错误');
//            }
//        } else {
//            $this->outApiReturn('0101', [], '请求类型错误');
//        }

    }

    /**
     * 提供给诺诺我们订单详情
     *
     * <AUTHOR> Li
     * @date 2018-11-30
     */
    private function getOrderInfoByOrderNo($orderNo)
    {
        //根据订单号查询订单
        if (!$orderNo) {
            return false;
        }

        $orderQueryModel = new OrderQuery();
        $result          = $orderQueryModel->OrderDetail($orderNo, 0);

        if (empty($result['order'])) {
            return false;
        }

        if ($result['order']['pay_status'] == 2) {
            return false;
        }

        return $result;
    }

    /**
     * 检测订单号开票记录
     * @deprecated 该函数已废弃
     * <AUTHOR> Li
     * @date 2018-11-30
     */
//    private function checkInvoiceRecordByOrderNo(int $sid, $orderNo)
//    {
//        if (!$orderNo || !$sid) {
//            return false;
//        }
//
//        $invoiceApi = new InvoiceApi();
//        $res        = $invoiceApi->checkInvoiceRecordByMixed($sid, [$orderNo]);
//        if ($res['code'] == 200 && !empty($res['data'])) {
//            return false;
//        } else {
//            return true;
//        }
//    }

    /**
     * 批量检测订单号开票记录
     *
     * <AUTHOR>
     * @date 2021-11-16
     */
//    private function checkInvoiceRecordByOrderNos($orderNo,  bool $isCheckPending = false)
//    {
//        if (!$orderNo) {
//            return false;
//        }
//
//        $invoiceApi = new InvoiceApi();
//        $res        = $invoiceApi->checkInvoiceRecordByMixed($orderNo, [],  $isCheckPending);
//
//        return $res;
//    }

    /**
     * 保存供应商开票记录
     *
     * <AUTHOR> Li
     * @date 2018-11-30
     */
    private function addEnterpriseInvoiceRecords($params)
    {
        pft_log('Invoice/debug',
            json_encode(['开票参数', 'param: ' . json_encode($params), '请求时间： ' . date('Y-m-d H:i:m', time())],
                JSON_UNESCAPED_UNICODE));

        //添加开票记录
        $invoiceApi = new InvoiceApi();
        $result     = $invoiceApi->addEnterpriseInvoiceRecords($params['sid'], $params['member_id'],
            $params['ordernum'],
            $params['out_trade_no'], $params['serial_num'], $params['ordertel'],
            $params['personid'], $params['enterprise_name'], $params['enterprise_code'], $params['tariff_number'],
            $params['image_url'], $params['add_time'],
            $params['operator_id'], $params['invoice_code'], $params['invoice_num'], $params['original_order_info']);

        pft_log('Invoice/debug',
            json_encode(['开票结果', $result, '请求时间： ' . date('Y-m-d H:i:m', time())],
                JSON_UNESCAPED_UNICODE));

        if ($result['code'] != 200) {
            return false;
        } else {
            return true;
        }

    }

    /**
     * 诺诺发票回调接口数据返回
     * <AUTHOR> Li
     * @DateTime 2018-11-29
     *
     * @param  string  $code  返回码
     * @param  array  $data  接口返回数据
     * @param  string  $msg  错误说明，默认为空
     *
     * @return
     */
    public function outApiReturn($code, $data = array(), $msg = '')
    {
        $data = array(
            'code'     => $code,
            'result'   => $data,
            'describe' => $msg,
        );

        $invoiceBiz = new invoiceBiz();
        //设置加密参数
        //dev测试
        //$data = $invoiceBiz->getParams(json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), 0, $this->_configure['testSecret']);
        //正式测试
        //$data = $invoiceBiz->getParams(json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), 0, $this->_configure['testSecret1']);
        //正式地址
        $data = $invoiceBiz->getParams(json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), 0,
            $this->_configure['EappSecret']);

        //暂时屏蔽Cannot modify header information - headers already sent 错误
        //@header('Content-type:text/json');
        //$res = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        echo $data;
        if (PHP_SAPI == 'cli') {
            return true;
        }
        exit();
    }

    /**
     * 获取企业扫码开票链接地址
     * @deprecated 代码在WxService上面
     * <AUTHOR> Li
     * @date 2018-12-04
     */
    public function getOpenInvoiceUrl()
    {
        $sid          = I('sid', 0, 'intval'); //开票供应商ID
        $tid          = I('tid', 0, 'intval'); //开票门票ID
        $orderNum     = I('ordernum', '', 'strval'); //开票订单号
        $source       = I('source', 'platform', 'strval'); //来源
        $isNeedCerifi = I('is_need_cerifi', 0, 'intval'); //是否需要校验开票操作人资质认证
        $orderType    = I('order_type', -1, 'intval'); //订单类型 1平台订单 2旅游券订单 3共享租赁订单

        if (empty($sid) || ($orderType != 3 && empty($tid)) || empty($orderNum)) {
            $this->apiReturn(201, [], '缺少必要参数');
        }

        $fid = $this->isLogin('ajax', false) ?: 0;
        if ($isNeedCerifi && !$fid) {
            $this->apiReturn(201, [], '当前操作需要开票人信息，请联系管理员');
        }

        $qConf = \Library\Container::pull(\Library\Util\QConfUtil::class);
        if ($source != 'hardware' || !$qConf->abTest('/php/platform/whitelist/invoice_merge_trade_order')) {
            $openInvoiceUrl = (new InvoiceApi())->getOpenInvoiceUrl($sid, $tid, $orderNum, false, 0 , $source, $fid, $isNeedCerifi, $orderType);
            if ($openInvoiceUrl['code'] != 200) {
                $this->apiReturn($openInvoiceUrl['code'], [], $openInvoiceUrl['msg']);
            }
            $this->apiReturn(200, ['openInvoiceUrl' => $openInvoiceUrl['data']], '获取成功');
        } else {
            // 交易单多选订单页，合并开票（云票务渠道使用）
            try {
                $url = (new InvoiceApi())->getMultipleInvoiceUrl($orderNum, $sid, $source, $tid);
            } catch (\Throwable $e) {
                $this->apiReturn(400, [], $e->getMessage());
                return;
            }
            $this->apiReturn(200, ['openInvoiceUrl' => $url]);
        }
    }

    /**
     * 获取交易订单列表
     */
    public function getTradeOrderList()
    {
        $cmbId = I('cmb_id', '', 'strval'); // 交易单号（加密）
        $sid = I('sid', '', 'strval'); // 开票供应商ID（加密）
        $source = I('source', '', 'strval'); // 来源（加密）
        $ordernum = I('ordernum', '', 'strval'); // 扫码的订单号（加密）
        if (empty($cmbId) || empty($sid) || empty($source) || empty($ordernum)) {
            $this->apiReturn(400, [], '缺少必要参数');
        }

        $cmbId = base64_decode($cmbId);
        $sid = \Library\MessageNotify\OrderNotify::url_sms_decode($sid)[0];
        $source = base64_decode($source);
        $ordernum = \Library\MessageNotify\OrderNotify::url_sms_decode($ordernum)[0];
        if (empty($cmbId) || empty($sid) || empty($source) || empty($ordernum)) {
            $this->apiReturn(400, [], '参数有误，解密异常');
        }

        try {
            //失败和已红冲跳过校验，直接跳转开票页面，避免页面循环跳转
            $exitsInvoiceUrl = (new InvoiceApi())->getExitsInvoiceUrlByOrderNum($sid, $ordernum, 0, [4, 5]);
            if ($exitsInvoiceUrl) {
                $this->apiReturn(302, ['openInvoiceUrl' => $exitsInvoiceUrl]);
                return;
            }
            $orderList = (new InvoiceApi())->getCanInvoiceOrderListByCmbId($cmbId, $sid, $source, $ordernum);
        } catch (\Exception $e) {
            $this->apiReturn(400, [], $e->getMessage());
            return;
        }
        $this->apiReturn(200, $orderList);
    }

    /**
     * 获取交易订单开票地址
     */
    public function getTradeOpenInvoiceUrl()
    {
        $cmbId = I('cmb_id', '', 'strval'); // 交易单号（加密）
        $sid = I('sid', '', 'strval'); // 开票供应商ID（加密）
        $source = I('source', '', 'strval'); // 来源（加密）
        $orderNumArr    = I('ordernum_arr/a', []); // 开票订单号集合（明文）
        $ordernum = I('ordernum', '', 'strval'); // 扫码的订单号（加密）

        if (empty($cmbId) || empty($sid) || empty($source) || empty($orderNumArr) || empty($ordernum)) {
            $this->apiReturn(400, [], '缺少必要参数');
        }

        $cmbId = base64_decode($cmbId);
        $sid = \Library\MessageNotify\OrderNotify::url_sms_decode($sid)[0];
        $source = base64_decode($source);
        $ordernum = \Library\MessageNotify\OrderNotify::url_sms_decode($ordernum)[0];
        if (empty($cmbId) || empty($sid) || empty($source) || empty($ordernum)) {
            $this->apiReturn(400, [], '参数有误，解密异常');
        }

        try {
            $openInvoiceUrl = (new InvoiceApi())->getTradeOpenInvoiceUrl($cmbId, $sid, $source, $orderNumArr, $ordernum);
        } catch (\Exception $e) {
            $this->apiReturn(400, [], $e->getMessage());
            return;
        }
        $this->apiReturn(200, $openInvoiceUrl);
    }

    /**
     * 百旺开票中转接口
     * <AUTHOR>  Li
     * @date 2021-09-03
     *
     */
    public function baiwangTransit()
    {
        $orderNum    = I('ordernum');
        $sid         = I('sid');
        $invoiceType = I('invoice_type', 0); //0 增值税普通电子发票 1 增值税电子专用发票 3 增值税专用发票, 4 增值税普通发票
        $mergeId     = I('merge_id', '');

        $invoiceApi = new invoiceBiz();
        $result     = $invoiceApi->baiwangTransit($sid, $orderNum, $this->isLogin('ajax', false) ?: 0, $invoiceType, $mergeId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


//    private function _merge($mergeId, $operation, $content)
//    {
//        //获取模型
//        $invoiceApi = new InvoiceApi();
//        $getMergeList = $invoiceApi->getMergeSonList($mergeId, 1);
//
//        if($getMergeList['code']!=200 || !$getMergeList['data']){
//            pft_log('invoice/hanxinCallbackMerge', json_encode($getMergeList));
//            $this->outApiReturn('0101', [], '未查到该订单信息');
//        }
//        $getMergeList = $getMergeList['data'];
//        $sonOrderArr  = array_column($getMergeList, 'ordernum');
//
//        //需要通过订单号 获取到对应订单的类型
//        $commonOrderArr        = [];
//        $travelVoucherOrderArr = [];
//        foreach ($getMergeList as $orderInfo) {
//            //平台订单
//            if ($orderInfo['order_type'] == 1) {
//                $commonOrderArr[] = $orderInfo['ordernum'];
//            }
//            //旅游券订单
//            if ($orderInfo['order_type'] == 2) {
//                $travelVoucherOrderArr[] = $orderInfo['ordernum'];
//            }
//        }
//
//        $tmpOrderInfo = [];
//        if ($commonOrderArr) {
//            //根据订单号查询订单
//            $subOrderModel   = new \Model\Order\SubOrderQuery();
//            $commonOrderInfo = $subOrderModel->getOrderDetail($commonOrderArr);
//            if (!$commonOrderInfo) {
//                pft_log('invoice/hanxinCallbackMerge/error',
//                    '合并发票，未查到订单信息' . json_encode(['ordernum' => $mergeId, 'sonOrderArr' => $commonOrderArr, 'tmpOrderInfo'=>$commonOrderInfo]));
//                $this->outApiReturn('0101', [], '未查到订单信息');
//            }
//            $tmpOrderInfo = array_merge($tmpOrderInfo, $commonOrderInfo);
//        }
//
//        if ($travelVoucherOrderArr) {
//            //通过订单号先到旅游券检测下
//            $tvInvoiceApi = new \Business\JsonRpcApi\TravelVoucherService\OrderQuery();
//            $tvOrderInfo  = $tvInvoiceApi->getOrderInfoDetail($travelVoucherOrderArr, true);
//            if (!$tvOrderInfo) {
//                pft_log('invoice/hanxinCallbackMerge/error',
//                    '合并发票，未查到订单信息' . json_encode(['ordernum' => $mergeId, 'sonOrderArr' => $travelVoucherOrderArr, 'tmpOrderInfo'=>$tvOrderInfo]));
//                $this->outApiReturn('0101', [], '未查到订单信息');
//            }
//            $tmpOrderInfo = array_merge($tmpOrderInfo, $tvOrderInfo);
//        }
//
//        if ($operation == 'queryOrderInfo') {
//            $time = date('Y-m-d H:i:s');
//            // //假如是子票  获取主票订单号来做处理
//            // $orderNo = $invoiceBiz->getParentOrderNum($orderNo);
//            //查找平台开票记录 该订单是否以开具过发票(合并订单会提前写开票记录所以传true)
//            $checkRes = $this->checkInvoiceRecordByOrderNos($sonOrderArr, true);
//            if ($checkRes === false || !$checkRes['data']) {
//                pft_log('invoice/hanxinCallbackMerge/error',
//                    '合并发票，此发票已开具纸票' . json_encode(['ordernum' => $mergeId, 'sonOrderArr' => $sonOrderArr, 'checkRes' => $checkRes]));
//                $this->outApiReturn('0000', [], '此发票已开具纸票');
//            }
//
//            //查询出开票配置
//            //合并开票前验证过开票企业是否一致
//            $tid       = $tmpOrderInfo[0]['tid'];
//            $tidArr    = array_unique(array_column($tmpOrderInfo, 'tid'));
//            $apply_did = $tmpOrderInfo[0]['apply_did'];
//            $ordername = $tmpOrderInfo[0]['ordername'];
//            $ordertel  = $tmpOrderInfo[0]['ordertel'];
//            //获取票信息
//            $ticketInfo = (new \Business\JavaApi\CommodityCenter\Ticket())->batchQueryTicketByTicketIds($tidArr);
//            if ($ticketInfo['code'] != 200) {
//                pft_log('invoice/hanxinCallbackMerge/error',
//                    '合并发票，未查到订单票信息' . json_encode(['ordernum' => $mergeId, 'sonOrderArr' => $sonOrderArr, 'tid' => $tidArr, 'ticketInfo'=>$ticketInfo]));
//                $this->outApiReturn('0101', [], '未查到订单票信息');
//            }
//            $ticketInfo  = array_column($ticketInfo['data'], null, 'id');
//            $invoiceConf = $invoiceApi->getInvoiceConfigByTid($tid, $apply_did, true);
//
//            if ($invoiceConf['code'] != 200 || empty($invoiceConf['data'])) {
//                pft_log('invoice/hanxinCallbackMerge/error',
//                    '合并发票，企业开票配置获取失败' . json_encode(compact('tid', 'apply_did','invoiceConf')));
//                $this->outApiReturn('0101', [], '企业开票配置获取失败');
//            }
//            $invoiceConf = $invoiceConf['data'];
//            $invoiceInfo = $invoiceApi->getEnterpriseOpenRecordsList($apply_did,
//                $invoiceConf['billing_party']);
//            if ($invoiceInfo['code'] != 200 || empty($invoiceInfo['data'])) {
//                pft_log('invoice/hanxinCallbackMerge/error',
//                    '合并发票，企业开票信息获取失败' . json_encode([$tid, $invoiceConf['billing_party'], $invoiceInfo]));
//                $this->outApiReturn('0101', [], '企业开票信息获取失败');
//            }
//
//            $invoiceInfo = $invoiceInfo['data'][0];
//
//            //获取开票人相关配置
//            $billerInfo = $invoiceApi->getBillerConfig($apply_did);
//            if ($billerInfo['code'] != 200 || empty($billerInfo['data'])) {
//                pft_log('invoice/hanxinCallbackMerge/error',
//                    '合并发票，开票人相关配置获取失败' . json_encode(['apply_did'=>$apply_did, 'billerInfo'=>$billerInfo]));
//                $this->outApiReturn('0101', [], '开票人相关配置获取失败');
//            }
//
//            $billerInfo = $billerInfo['data'];
//            $ext        = $billerInfo['expand'] ? json_decode($billerInfo['expand'], true) : [];
//            $deptid     = isset($ext['deptid']) && !empty($ext['deptid']) ? strval($ext['deptid']) : '';
//            $dutyFree   = isset($ext['duty_free']) && !empty($ext['duty_free']) ? intval($ext['duty_free']) : 0;
//            //默认是免税的
//            $zzstsgl = '免税';
//            $lslbs   = 1;
//            $yhzcbs  = 1;
//            $taxRate = 0;
//
//            //税率标识 增值税特殊管理配置处理
//            if (!$dutyFree && $invoiceConf['tax_rate'] > 0) {
//                $yhzcbs  = 0;
//                $zzstsgl = '';
//                $lslbs   = 0;
//                $taxRate = round($invoiceConf['tax_rate'], 2);
//            }
//            $detail = [];
//            $hjje   = 0;
//            //获取订单对应得自定义商品名 进行合并
//            $stayOpenInvoiceData = $this->_handleMergeUpdateData($tmpOrderInfo, $ticketInfo);
//            foreach ($stayOpenInvoiceData as $item) {
//                //$tax = round(($item['je'] / 100) / (1 + $invoiceConf['tax_rate']) * $invoiceConf['tax_rate'], 2) . '';
//                //税额
//                $tax = 0;
//                if ($taxRate > 0) {
//                    $tax     = round(($item['je'] / 100) / (1 + $invoiceConf['tax_rate']) * $invoiceConf['tax_rate'], 2);
//                }
//
//                //不含税金额
//                $taxfreeamt = round($item['je'] / 100 - $tax, 2);
//                $detail[] = [
//                        'fphxz'      => 0,
//                        'goodsname'  => $item['spmc'], //票名称
//                        'hsbz'       => 1, //单价含税标sdf志   0 不含税 1 含税
//                        'lslbs'      => $lslbs, //单价含税标sdf志   0 不含税 1 含税
//                        // 'num'        => $item['tnum'], //数量
//                        // 'price'      => $item['tprice'] / 100, //单价
//                        'spbm'       => $invoiceConf['spbm'],
//                        'spec'       => '',
//                        'tax'        => $tax,
//                        'taxamt'     => $item['je'] / 100,
//                        'taxfreeamt' => $taxfreeamt,
//                        'taxrate'    => $taxRate,
//                        'unit'       => '',
//                        'yhzcbs'     => $yhzcbs,
//                        'zsbm'       => '',
//                        'zzstsgl'    => $zzstsgl,
//                ];
//                $hjje     += $item['je'];
//            }
//
//            $returnData = [
//                'buyerName'           => $ordername,
//                'buyerPhone'          => $ordertel,
//                'saletaxnum'          => $invoiceInfo['tariff_number'],
//                'buyerAccount'        => '',
//                'buyerAddress'        => '',
//                'buyerEmail'          => '',
//                'multiOrderInfo'      => [
//                    [
//                        'bhtaxtotal'  => '',
//                        'checker'     => isset($billerInfo['reviewer']) ? $billerInfo['reviewer'] : '',
//                        'clerk'       => isset($billerInfo['drawer']) ? $billerInfo['drawer'] : '管理员',
//                        'clerkid'     => '',
//                        'deptid'      => $deptid,
//                        'detail'      => $detail,
//                        'invoicedate' => $time,
//                        'kptype'      => 1,
//                        'message'     => '',
//                        'orderno'     => $mergeId, //订单号
//                        'ordertotal'  => $hjje / 100, //订单总价
//                        'payee'       => isset($billerInfo['payer']) ? $billerInfo['payer'] : '',
//                        'qdbz'        => 0,
//                        'qdxmmc'      => '',
//                        'saleaccount' => '',
//                        'salephone'   => '',
//                        'saletaxnum'  => $invoiceInfo['tariff_number'],
//                        'self_flag'   => '1',
//                        'taxtotal'    => $hjje,
//                        'tsfs'        => '-1',
//                    ],
//                ],
//                'phone'               => '',
//                'scanMultiInvoiceVos' => [],
//                'self_flag'           => '1',
//                'taxnum'              => '',
//                'telephone'           => '',
//            ];
//            pft_log('invoice/hanxinCallback',
//                '合并发票' . json_encode(['ordernum' => $mergeId, 'returnData' => $returnData, 'stayOpenInvoiceData' => $stayOpenInvoiceData]));
//            $this->outApiReturn('0000', $returnData, '请求成功');
//
//        } else if ($operation == 'deliverInvoice') {
//            //增加异常捕获
//            if (isset($content['errorMessage']) && !isset($content['invoiceImageUrl'])) {
//                $this->outApiReturn('0101', [], $content['errorMessage']);
//            }
//            $data = [
//                'image_url'      => $content['invoiceImageUrl'],
//                'invoice_code'   => $content['invoiceCode'],
//                'invoice_num'    => $content['invoiceNum'],
//                'invoice_status' => 2,
//                'invoice_state'  => 2,
//            ];
//
//            //更新开票记录
//            $result = $invoiceApi->updateMergeInvoiceRecordInfoBy($mergeId, $data);
//
//            if ($result['code'] == 200) {
//                $this->outApiReturn('0000', [], '通知成功');
//            } else {
//                $this->outApiReturn('0101', [], '参数或接口错误');
//            }
//        } else {
//            $this->outApiReturn('0101', [], '请求类型错误');
//        }
//    }

    /**
     * 获取企业扫码开票链接地址
     *
     * <AUTHOR> Li
     * @date 2018-12-04
     */
    public function getMergeOpenInvoiceUrl()
    {
        $sid          = I('sid'); //开票供应商ID
        $orderNumArr  = I('ordernum_arr', []); //开票订单号
        $mergeId      = I('merge_id'); //合并id
        $isNeedCerifi = I('is_need_cerifi', 0, 'intval'); //是否分销商开票页面过来的

        if (empty($sid)  || (empty($orderNumArr) && empty($mergeId))) {
            $this->apiReturn(201, [], '缺少必要参数');
        }
        if ($orderNumArr && !is_array($orderNumArr)) {
            $this->apiReturn(201, [], '参数异常');
        }

        $fid = $this->isLogin('ajax', false) ?: 0;

        $invoiceApi     = new InvoiceApi();
        $openInvoiceUrl = $invoiceApi->getMergeOpenInvoiceUrl($sid, $orderNumArr, false, $fid, 0, $mergeId, $isNeedCerifi);

        if (isset($openInvoiceUrl['code']) && $openInvoiceUrl['code'] == 200) {
            $this->apiReturn($openInvoiceUrl['code'], ['openInvoiceUrl' => $openInvoiceUrl['data']],
                $openInvoiceUrl['msg']);
        }

        $this->apiReturn($openInvoiceUrl['code'], [], $openInvoiceUrl['msg']);
    }
    /**
     * 处理合并订单开票信息数据
     * @deprecated 【电子发票二期】供应商开票废弃
     * <AUTHOR>
     * @param  array $orderInfos 订单信息
     * @param  array $ticketInfo 订单关联票信息
     * @date 2021-12-14
     */
//    private function _handleMergeUpdateData(array $orderInfos, array $ticketInfo)
//    {
//        $exist               = [];
//        $zdyIds              = [];
//        $zdyName             = [];
//        $stayOpenInvoiceData = [];
//        $invoiceApi          = new InvoiceApi();
//        //获取自定义商品名称
//        $tidArr     = array_unique(array_column($orderInfos, 'tid'));
//        $zdyNameRes = $invoiceApi->productDefinitionNameList(0, 0, $tidArr, '', '', 0, 0);
//        if ($zdyNameRes['code'] == 200 && $zdyNameRes['data']['list']) {
//            $zdyNameRes = $zdyNameRes['data']['list'];
//            $exist      = array_reduce(array_column($zdyNameRes, 'tids'), 'array_merge', array());
//            foreach ($zdyNameRes as $item) {
//                foreach ($item['tids'] as $v) {
//                    $zdyIds[$v]  = $item['id'];
//                    $zdyName[$v] = ['name' => $item['name']];
//                }
//            }
//        }
//
//        foreach ($orderInfos as $item) {
//            $key = 'zdy_';
//            //有自定义商品名称 订单合并
//            if (in_array($item['tid'], $exist)) {
//                $k = $key . $zdyIds[$item['tid']];
//                if (isset($stayOpenInvoiceData[$k])) {
//                    $stayOpenInvoiceData[$k]['je'] = $stayOpenInvoiceData[$k]['je'] + ($item['totalmoney']);
//                } else {
//                    $stayOpenInvoiceData[$k] = [
//                        'je'   => $item['totalmoney'],
//                        'spmc' => $zdyName[$item['tid']]['name'] ?? '',
//                    ];
//                }
//            } else {
//                if (isset($stayOpenInvoiceData[$item['tid']])) {
//                    $stayOpenInvoiceData[$item['tid']]['je'] = $stayOpenInvoiceData[$item['tid']]['je'] + ($item['totalmoney']);
//                } else {
//                    $stayOpenInvoiceData[$item['tid']] = [
//                        'je'   => $item['totalmoney'],
//                        'spmc' => $ticketInfo[$item['tid']]['title'],
//                    ];
//                }
//            }
//        }
//
//        return $stayOpenInvoiceData;
//    }

    /**
     * 生成20位的流水号
     *
     * @return string
     */
    public function getTradeNo()
    {
        $nums   = '0123456789';
        $letter = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';

        $nonce = substr(str_shuffle($nums . $letter), 0, 9);
        $sum       = 0;
        foreach (str_split($nonce) as $le) {
            if (is_numeric($le)) {
                $sum += $le;
            }
        }
        $nonce .= strrev($sum)[0];

        return $nonce . time();
    }

    /**
     * 校验订单是否开票成功，返回开票信息
     */
    public function checkInvoiceRecordByMixed()
    {
        $tmpOrder  = I('post.ordernum', '');
        $sid       = I('post.sid', '');
        $fid       = I('post.fid', '');
        $isM       = I('post.isMerge', '');
        $isN       = I('post.isN', 0);
        $orderType = I('post.type', 'w5');

        //解析下订单号
        $orderNum  = \Library\MessageNotify\OrderNotify::url_sms_decode($tmpOrder)[0];
        $sid       = \Library\MessageNotify\OrderNotify::url_sms_decode($sid)[0];
        $fid       = \Library\MessageNotify\OrderNotify::url_sms_decode($fid)[0] ?: 0;
        $isM       = \Library\MessageNotify\OrderNotify::url_sms_decode($isM)[0] ?: 0;
        $orderType = \Library\MessageNotify\OrderNotify::url_sms_decode($orderType)[0] ?: -1;

        if ($orderType == 3 && !$orderNum && $tmpOrder) {
            $orderNum = $tmpOrder;
        }

        if (!$orderNum || !$sid) {
            $this->apiReturn(203, [], '开票链接参数有误');
        }

        $invoiceBiz       = new invoiceBiz();
        $isSandBox        = (new \Business\ElectronicInvoices\InvoiceApi())->getSandBox();
        $invoiceRecordRes = $invoiceBiz->checkInvoiceRecordByMixed($orderNum, $sid, $fid, $isM, $isN, $isSandBox, $orderType);
        if ($invoiceRecordRes['code'] != 200) {
            $this->apiReturn($invoiceRecordRes['code'], $invoiceRecordRes['data'], $invoiceRecordRes['msg']);
        }

        $invoiceRecord = $invoiceRecordRes['data'];

        $this->apiReturn(200, $invoiceRecord, '发票信息获取成功');
    }

    /**
     * 返回开票信息
     */
    public function getInvoiceDetailByMixed()
    {
        $tmpOrder  = I('post.ordernum', '');
        $sid       = I('post.sid', '');
        $fid       = I('post.fid', '');
        $isN       = I('post.isN', 0);
        $orderType = I('post.type', 'w5');
        $source = I('post.source', '');

        $sid       = \Library\MessageNotify\OrderNotify::url_sms_decode($sid)[0];
        $fid       = \Library\MessageNotify\OrderNotify::url_sms_decode($fid)[0] ?: 0;
        $orderType = \Library\MessageNotify\OrderNotify::url_sms_decode($orderType)[0] ?: -1;

        //解析下订单号
        if ($orderType == 3) {
            $orderNum = $tmpOrder;
        } else {
            //按逗号分割后解析订单号并拼接成字符串
            $encodeOrderNumArr = array_map(function ($item) {
                return \Library\MessageNotify\OrderNotify::url_sms_decode($item)[0];
            }, explode(',', $tmpOrder));
            $orderNum = implode(',', $encodeOrderNumArr);
        }

        if (!$orderNum || !$sid) {
            $this->apiReturn(203, [], '开票链接参数有误');
        }

        $source = $source ? base64_decode($source) : '';
        $invoiceBiz       = new invoiceBiz();
        $invoiceRecordRes = $invoiceBiz->getInvoiceDetailByMixed($orderNum, $sid, $fid, $isN, $orderType, $source);
        if ($invoiceRecordRes['code'] != 200) {
            $this->apiReturn($invoiceRecordRes['code'], $invoiceRecordRes['data'], $invoiceRecordRes['msg']);
        }

        $invoiceRecord = $invoiceRecordRes['data'];

        $this->apiReturn(200, $invoiceRecord, '发票信息获取成功');
    }

    /**
     * 返回开票信息
     */
    public function getInvoiceRecordDetailByMixed()
    {
        $tmpOrder  = I('post.ordernum', '');
        $sid       = I('post.sid', '');
        $fid       = I('post.fid', '');
        $isMerge   = I('post.isMerge', '');
        $orderType = I('post.type', 'w5');
        $sysId     = I('post.sysId', 0);
        $major     = I('post.major', '');
        $source    = I('post.source', '');

        //解析下订单号
        $orderNum  = \Library\MessageNotify\OrderNotify::url_sms_decode($tmpOrder)[0];
        $sid       = \Library\MessageNotify\OrderNotify::url_sms_decode($sid)[0];
        $fid       = \Library\MessageNotify\OrderNotify::url_sms_decode($fid)[0] ?: 0;
        $isMerge   = \Library\MessageNotify\OrderNotify::url_sms_decode($isMerge)[0] ?: 0;
        $orderType = \Library\MessageNotify\OrderNotify::url_sms_decode($orderType)[0] ?: -1;
        $sysId     = \Library\MessageNotify\OrderNotify::url_sms_decode($sysId)[0] ?: 0;
        $source    = $source ? base64_decode($source) : '';
        $major     = \Library\MessageNotify\OrderNotify::url_sms_decode($major)[0] ?: '';

        if ($orderType == 3 && !$orderNum && $tmpOrder) {
            $orderNum = $tmpOrder;
        }

        if (!$orderNum || !$sid || !$sysId) {
            $this->apiReturn(203, [], '开票链接参数有误');
        }

        $invoiceBiz       = new invoiceBiz();
        $invoiceRecordRes = $invoiceBiz->getInvoiceRecordDetailByMixed($orderNum, $sid, $fid, $isMerge, $sysId, compact('major', 'source'));
        if ($invoiceRecordRes['code'] != 200) {
            $this->apiReturn($invoiceRecordRes['code'], $invoiceRecordRes['data'], $invoiceRecordRes['msg']);
        }

        $invoiceRecord = $invoiceRecordRes['data'];

        $this->apiReturn(200, $invoiceRecord, '发票信息获取成功');
    }

    /**
     * 重新开票
     * @return void
     */
    public function freshOpenInvoiceUrl() {
        $orderNum  = I('post.order_num', '', 'strval');
        $sid       = I('post.sid', '', 'strval');
        $fid       = I('post.fid', '', 'strval');
        $isMerge   = I('post.is_merge', '', 'strval');
        $orderType = I('post.order_type', '', 'strval');
        $source    = I('post.source', '', 'strval');

        $invoiceApi = new InvoiceApi();
        $invoiceRes = $invoiceApi->freshOpenInvoiceUrl($orderNum, $isMerge, $sid, $fid, $orderType, $source);
        if ($invoiceRes['code'] != 200) {
            $this->apiReturn($invoiceRes['code'], $invoiceRes['data'], $invoiceRes['msg']);
        }

        $this->apiReturn(200, $invoiceRes['data'], '开票提交成功');
    }

    /**
     * 航信开发票
     */
    public function writeReceipt()
    {
        $tariffNumber = I('post.tariff_number', '', 'strval');
        $orderNum     = I('post.order_num', '', 'strval');
        $postData     = I('post.post_data_arr', []);
        $isMerge      = I('post.isMerge');
        $sid          = I('post.sid');
        $fid          = I('post.fid');
        $outTradeNo   = I('post.out_trade_no');
        $orderType    = I('post.order_type', 'w5', 'strval');
        $source       = I('post.source', '', 'strval');
        $major        = I('post.major', '', 'strval');

        if (!$tariffNumber || !$orderNum || !$postData) {
            $this->apiReturn(203, [], '缺少必要参数');
        }
        $orderNumArr = explode(',', $orderNum);
        $lockKeyArr = [];
        $cache  = Cache::getInstance('redis');

        try {
            foreach ($orderNumArr as $item) {
                //防抖处理
                $locKey = 'electronic_invoice:writeReceipt:lock:'.$item .':'.$sid;
                $lockRet = $cache->lock($locKey, 1, 30);
                if (!$lockRet) {
                    $orderNum = \Library\MessageNotify\OrderNotify::url_sms_decode($item)[0] ?: $item;
                    throw new \Exception("订单[{$orderNum}]已提交，请勿重复操作");
                }
                $lockKeyArr[] = $locKey;
            }
        } catch (\Exception $e) {
            //释放锁
            foreach ($lockKeyArr as $item) {
                $cache->lock_rm($item);
            }
            $this->apiReturn(203, [], $e->getMessage());
        }

        $invoiceApi = new InvoiceApi();
        $invoiceRes = $invoiceApi->writeReceipt($tariffNumber, $orderNum, $postData, $sid, $fid, $outTradeNo, $isMerge, $orderType, $source, ['major' => $major]);
        if ($invoiceRes['code'] != 200) {
            //释放锁
            foreach ($lockKeyArr as $item) {
                $cache->lock_rm($item);
            }
            $this->apiReturn($invoiceRes['code'], $invoiceRes['data'], $invoiceRes['msg']);
        }

        $this->apiReturn(200, $invoiceRes['data'], '开票提交成功');
    }

    /**
     * 开票的回调处理
     *
     * <AUTHOR> Li
     */
    public function invoicesCallbackNew()
    {
        $params    = I('post.');

        $operation = $params['operater']; //操作类型   callback【开票结果回调】
        $orderNo   = $params['orderno']; //订单号
        $content   = json_decode(str_replace('&quot;', '"', $params['content']), true); //开票请求参数

        pft_log('Invoice/debug',
            json_encode([
                'ac'        => '回调数据',
                'param'     => $params,
                'operation' => $operation,
                'orderNo'   => $orderNo,
                'content'   => $content,
                '请求时间'   => date('Y-m-d H:i:m', time()),
            ], JSON_UNESCAPED_UNICODE));

        $invoiceApi = new InvoiceApi();
        $result     = $invoiceApi->invoicesCallbackHX($operation, $orderNo, $content);
        if ($result['code'] != 200) {
            $this->outApiReturn('0101', [], $result['msg']);
        }

        $this->outApiReturn('0000', [], '通知成功');

        ////开票结果回调
        //if ($operation == 'callback') {
        //    $invoiceApi = new InvoiceApi();
        //    //判断下开票结果
        //    //有返回状态时
        //    $invoiceCode  = '';
        //    $invoiceNum   = '';
        //    $invoiceUrl   = '';
        //    $invoicePdf   = '';
        //    $invoiceState = 1;
        //    if (isset($content['c_status']) && isset($content['c_url'])) {
        //        //开票完成
        //        if (in_array($content['c_status'], ['1', '2'])) {
        //            $invoiceCode  = $content['c_fpdm'];
        //            $invoiceNum   = $content['c_fphm'];
        //            $invoiceUrl   = $content['c_jpg_url'];
        //            $invoicePdf   = $content['c_url'];
        //            $invoiceState = 2;
        //        } elseif (in_array($content['c_status'], ['22'])) {
        //            //开票失败
        //            $invoiceState = 5;
        //        } else {
        //            //开票成功签章失败（电票时）
        //            //这种情况不处理  开票记录为开票中 后续让商家找航信确认结果
        //        }
        //
        //        $invoiceApi->updateInvoiceStatusByOrderNum($orderNo, $invoiceCode, $invoiceNum, $invoiceUrl, $invoicePdf, $invoiceState);
        //    } else {
        //        //没有状态的时候  需要自行查询一次开票结果
        //        $invoiceRes = $invoiceApi->queryInvoiceResult($content['c_saletaxnum'], [$orderNo], [], 0, $this->_isSandBox);
        //        if ($invoiceRes['code'] == 200 && in_array($invoiceRes['data'][0]['status'], ['1', '2'])) {
        //            //开票成功的 更新发票信息
        //            $invoiceCode  = $invoiceRes['data'][0]['invoiceCode'] ?? '';
        //            $invoiceNum   = $invoiceRes['data'][0]['invoiceNo'] ?? '';
        //            $invoiceUrl   = $invoiceRes['data'][0]['pictureUrl'] ?? '';
        //            $invoicePdf   = $invoiceRes['data'][0]['pdfUrl'] ?? '';
        //            $invoiceState = 2;
        //            $invoiceApi->updateInvoiceStatusByOrderNum($orderNo, $invoiceCode, $invoiceNum, $invoiceUrl, $invoicePdf, $invoiceState);
        //        } elseif ($invoiceRes['code'] == 200 && in_array($invoiceRes['data'][0]['status'], ['22'])) {
        //            //开票失败的 更新失败状态
        //            $invoiceCode  = $invoiceRes['data'][0]['invoiceCode'] ?? '';
        //            $invoiceNum   = $invoiceRes['data'][0]['invoiceNo'] ?? '';
        //            $invoiceUrl   = $invoiceRes['data'][0]['pictureUrl'] ?? '';
        //            $invoicePdf   = $invoiceRes['data'][0]['pdfUrl'] ?? '';
        //            $invoiceState = 5;
        //            $invoiceApi->updateInvoiceStatusByOrderNum($orderNo, $invoiceCode, $invoiceNum, $invoiceUrl, $invoicePdf, $invoiceState);
        //        }
        //    }
        //
        //    $this->outApiReturn('0000', [], '通知成功');
        //    //通过流水号获取下数据
        //} elseif ($operation == 'invoiceInvalid') {
        //    //回传发票作废结果
        //    $this->outApiReturn('0101', [], '商家作废回调未处理');
        //} elseif ($operation == 'invoiceApply') {
        //    //回传开票申请结果数据
        //    $this->outApiReturn('0101', [], '开票申请结果回调未处理');
        //} elseif ($operation == 'invoiceRedCallback') {
        //    //回传红字信息表申请结果
        //    $this->outApiReturn('0101', [], '红字信息表申请回调未处理');
        //} elseif ($operation == 'confirmCallback') {
        //    //回传红字确认单申请结果
        //    $this->outApiReturn('0101', [], '红字确认单申请回调未处理');
        //}
        //
        //$this->outApiReturn('0101', [], '当前操作不合法');
    }

    /**
     * 开票的回调处理
     *
     * <AUTHOR> Li
     */
    public function invoicesCallbackSD()
    {
        $params    = I('post.');

        $operation = $params['operater']; //操作类型   callback【开票结果回调】
        $content   = json_decode(str_replace('&quot;', '"', $params['content']), true); //开票请求参数
        $orderNo   = '';
        if ($operation == 'callback') {
            $orderNo = $params['orderno']; //订单号
        } elseif ($operation == 'confirmCallback') {
            $orderNo = $content['billId']; //订单号
        }

        pft_log('Invoice/debug',
            json_encode([
                'ac'        => '回调数据',
                'param'     => $params,
                'operation' => $operation,
                'orderNo'   => $orderNo,
                'content'   => $content,
                '请求时间'   => date('Y-m-d H:i:m', time()),
            ], JSON_UNESCAPED_UNICODE));

        if (!$orderNo) {
            $this->outApiReturn('0000', [], '通知成功');
        }

        $invoiceApi = new InvoiceApi();
        $result     = $invoiceApi->invoicesCallbackHX($operation, $orderNo, $content);
        if ($result['code'] != 200) {
            $this->outApiReturn('0101', [], $result['msg']);
        }

        $this->outApiReturn('0000', [], '通知成功');
    }

    /**
     * 将服务器上的文件输出给前端
     *
     * @return bool|false|int
     */
    public function getFileContent()
    {
        $url = I('post.url');
        if (!$url) {
            $this->apiReturn(203, [], '地址有误');
        }

        $stream_opts = [
            "ssl" => [
                "verify_peer"      => false,
                "verify_peer_name" => false,
            ],
        ];

        header('Content-Type:application/pdf');

        $invoicePdf = file_get_contents($url,false, stream_context_create($stream_opts));

        echo $invoicePdf;
    }

    /**
     * 开票的回调处理
     *
     * <AUTHOR> Li
     */
    public function invoicesCallbackJS()
    {
        $params  = @file_get_contents('php://input');
        $content = $params ? json_decode(base64_decode($params), true) : [];

        //老何说 先不用他们的回调 都以轮询结果为准
        $this->ajaxReturn(['code' => 0, 'message' => '成功',], 'JSON');

        pft_log('Invoice/debug',
            json_encode([
                'ac'        => '百旺金穗云-回调数据',
                'param'     => $params,
                'content'   => $content,
                '请求时间'   => date('Y-m-d H:i:m', time()),
            ], JSON_UNESCAPED_UNICODE));

        if (!$content) {
            $this->ajaxReturn(['code' => 203, 'message' => '失败'], 'JSON');
        }

        $invoiceApi = new InvoiceApi();
        $result     = $invoiceApi->invoicesCallbackJS($content);
        $data       = [
            'code'    => $result['code'] == 200 ? 0 : $result['code'],
            'message' => $result['code'] == 200 ? '成功' : '失败',
        ];
        $this->ajaxReturn($data, 'JSON');
    }
}
