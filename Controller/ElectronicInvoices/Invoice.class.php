<?php
/**
 * 诺诺发票控制层
 *
 * <AUTHOR> Li
 * @date   2018-11-22
 */

namespace Controller\ElectronicInvoices;

use Business\CommodityCenter\TicketAttribute;
use Business\ElectronicInvoices\Invoice as invoiceBiz;
use Business\MemberLogin\MemberLoginHelper;
use Business\Product\Get\EvoluteQuery;
use Business\Product\ProductList;
use Library\Cache\Cache as Cache;
use Library\Controller;
use Model\Member\Member as memberModel;
use Library\Business\NuoInvoice as invoiceLib;
use Business\JavaApi\Product\Ticket;
use Business\JavaApi\ProductApi;
use Business\JavaApi\TicketApi;
use Business\ElectronicInvoices\InvoiceApi;

class Invoice extends Controller
{
    private $memberId        = 0;
    private $sid             = 0;
    private $_configure      = [];
    /** @var InvoiceApi */
    private $_invoiceApi     = null;
    private $_invoiceBiz     = null;
    private $_specialChannel = [24, 44, 16];
    private $_isSandbox      = false;   //是否沙箱环境
    private $loginInfo      = [];

    public function getInvoiceApi()
    {
        if ($this->_invoiceApi == null) {
            $this->_invoiceApi = new InvoiceApi();
        }

        return $this->_invoiceApi;
    }

    public function getInvoiceBiz()
    {
        if ($this->_invoiceBiz == null) {
            $this->_invoiceBiz = new \Business\ElectronicInvoices\Invoice();
        }
        return $this->_invoiceBiz;
    }
    public function __construct()
    {
        $this->_configure = load_config('nuonuo', 'electronic_invoice');
        if (empty($this->_configure)) {
            $this->apiReturn(401, [], '配置文件出错');
        }

        $loginInfo = $this->getLoginInfo();
        if (empty($loginInfo['memberID'])) {
            $this->apiReturn(201, [], '请先登陆');
        }

        $this->memberId  = $loginInfo['memberID'];
        $this->sid       = $loginInfo['sid'];
        $this->loginInfo = $loginInfo;
    }

    /**
     * 通过企业名称模糊查询企业六位码
     *
     * <AUTHOR> Li
     * @date   2018-11-22
     */
    public function getEnterpriseCode()
    {
        $enterpriseName = I('get.enterprise_name', ''); //企业名称

        if (empty($enterpriseName)) {
            $this->apiReturn(201, [], '企业名称不能为空');
        }

        if (ENV == 'PRODUCTION') {
            $accessToken = $this->getAccessToken();

            $invoiceLib = new invoiceLib();
            $result     = $invoiceLib->getEnterpriseCode($enterpriseName, $accessToken);
        } else {
            $result = [
                [
                    'code'        => 'TTNSJA',
                    'name'        => '福建票付通信息科技有限公司',
                    'name_length' => '13',
                ],
            ];
        }

        if ($result !== false) {
            //处理该供应商是否有添加过该开票方
            $invoiceInfo = $this->getInvoiceApi()->getEnterpriseOpenRecordsByName($this->sid, $enterpriseName);
            if ($invoiceInfo['code'] != 200) {
                $this->apiReturn($invoiceInfo['code'], $invoiceInfo['data'], $invoiceInfo['msg']);
            }

            $invoiceInfo = array_column($invoiceInfo['data'], 'enterprise_name', 'enterprise_code');
            foreach ($result as $key => $value) {
                $result[$key]['disabled'] = isset($invoiceInfo['data'][$value['code']]) ? true : false;
            }
            $this->apiReturn(200, $result, 'success');
        } else {
            $this->apiReturn(400, [], 'fail');
        }
    }

    /**
     * 通过企业六位码查询企业税号
     *
     * <AUTHOR> Li
     * @date   2018-11-22
     */
    public function getEnterpriseTariffNumber()
    {
        $code = I('get.code'); //六位码

        if (empty($code)) {
            $this->apiReturn(201, [], '六位码不能为空');
        }

        if (ENV == 'PRODUCTION') {
            $accessToken = $this->getAccessToken();

            $invoiceLib = new invoiceLib();
            $result     = $invoiceLib->getEnterpriseTariffNumber($code, $accessToken);
        } else {
            $result = [
                'kpCode'  => '91350100097974396N',
                'code'    => 'TTNSJA',
                'kpName'  => '福建票付通信息科技有限公司',
                'systype' => '1',
            ];
        }

        if ($result !== false) {
            $this->apiReturn(200, $result, 'success');
        } else {
            $this->apiReturn(400, [], 'fail');
        }

    }

    public function getAccessToken()
    {
        $redis       = Cache::getInstance('redis');
        $accessToken = $redis->get("access_token");

        if (empty($accessToken)) {
            $enterPriseInfo = $this->getInvoiceApi()->getEnterpriseOpenRecordsByMixed(1,
                $this->_configure['userTax']);
            if ($enterPriseInfo['code'] == 200) {
                $accessToken = $enterPriseInfo['data']['access_token'];

                //保存access_tokens
                $access = "access_token";
                $redis->set($access, $accessToken);
            }
        }

        return $accessToken;
    }

    /**
     * 通过供应商id获取企业信息
     *
     * <AUTHOR> Li
     * @date   2018-12-03
     */
    public function getEnterpriseOpenRecordsBySid()
    {
        $sid = I('get.sid');

        if (empty($sid)) {
            $this->apiReturn(201, [], '缺少必要参数');
        }

        $enterPriseInfo = $this->getInvoiceApi()->getEnterpriseOpenRecordsByMixed($sid);
        if ($enterPriseInfo['code'] == 200) {
            $enterPriseInfo = $enterPriseInfo['data'];
            //实例模型s
            $memberModel = new memberModel();
            $memberInfo  = $memberModel->getMemberInfo($enterPriseInfo['sid'], 'id', 'dname,account');

            $enterPriseInfo['account'] = $memberInfo['account'];
            $enterPriseInfo['dname']   = $memberInfo['dname'];
            if (empty($enterPriseInfo['access_token'])) {
                $enterPriseInfo['auth_invoice'] = 0;
            } else {
                $enterPriseInfo['auth_invoice'] = 1;
            }
            unset($enterPriseInfo['access_token']);
        }

        $this->apiReturn(200, $enterPriseInfo, '获取成功');
    }

    /**
     * 新增企业开票信息
     *
     * <AUTHOR> Li
     * @date   2018-11-22
     */
    public function editEnterpriseInvoice()
    {
        $invoiceInfo = I('post.invoice_info');//开票信息数组
        $id          = I('post.id', 0);

        if (!$invoiceInfo) {
            $this->apiReturn(203, [], '请选择开票企业');
        }

        $result = $this->getInvoiceApi()->addEnterpriseInvoice($invoiceInfo, $this->sid, $this->memberId, $id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除企业开票信息 （软删除）
     *
     * <AUTHOR> Li
     * @date   2018-11-22
     */
    public function deleteEnterpriseInvoice()
    {
        $id = I('post.delete_id'); //企业开票记录id
        if (!$id) {
            $this->apiReturn(201, [], '缺少必要参数');
        }

        $operatorId = $this->memberId;

        $result = $this->getInvoiceApi()->deleteEnterpriseInvoice($id, $operatorId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取开票企业开通记录
     *
     * <AUTHOR> Li
     * @date   2018-11-22
     */
    public function getEnterpriseOpenRecords()
    {
        $identify = I('get.identify'); //查询条件
        $type     = I('get.type', 0, 'intval'); //查询类型 0 开票企业名称 1 开票方税号
        $isAuth   = I('get.is_auth', 0, 'intval'); //是否授权 0 未授权  1 已授权
        $page     = I('get.page', 1, 'intval'); //当前页数
        $pageSize = I('get.page_size', 10, 'intval'); //每页条数

        //实例模型
        $memberModel = new memberModel();

        $sid            = 0;
        $tariffNumber   = '';
        $enterpriseName = '';
        if (!empty($identify)) {
            //根据查询类型对应查询
            switch ($type) {
                case '1':
                    //开票方税号
                    $tariffNumber = $identify;
                    break;
                default:
                    //开票企业名称
                    $enterpriseName = $identify;
                    break;
            }
        }

        if ($this->sid != 1) {
            $sid = $this->sid;
        }

        $result = $this->getInvoiceApi()->getEnterpriseOpenRecords($sid, $tariffNumber, $enterpriseName, $isAuth, $page,
            $pageSize);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $list  = $result['data']['list'];
        $total = $result['data']['total'];
        //对结果值进行调整
        if (!empty($list)) {
            $staffIdArray = array_column($list, 'sid');
            $memberInfo   = $memberModel->getMemberByMixed('id', $staffIdArray, 'id, dname, account');
            foreach ($list as $key => $value) {
                $list[$key]['account'] = $memberInfo[$value['sid']]['account'];
                $list[$key]['dname']   = $memberInfo[$value['sid']]['dname'];
                if (empty($value['access_token'])) {
                    $list[$key]['auth_invoice'] = 0;
                } else {
                    $list[$key]['auth_invoice'] = 1;
                }
                unset($list[$key]['access_token']);

            }
        }

        $return = ['list' => $list, 'total' => $total];
        $this->apiReturn(200, $return, '获取成功');
    }

    /**
     * 获取企业开票记录
     *
     * <AUTHOR> Li
     * @date   2018-11-22
     */
    public function getEnterpriseInvoiceRecords()
    {
        $tariffNumber   = I('get.tariff_number' , '', 'strval,trim'); //开票方税号
        $ordernum       = I('get.ordernum' , '', 'strval,trim'); //订单号
        $page           = I('get.page', 1, 'intval'); //当前页数
        $pageSize       = I('get.page_size', 10, 'intval'); //每页条数
        $start          = I('get.start_time', '', 'strval,trim'); //查询开始时间
        $end            = I('get.end_time', '', 'strval,trim'); //查询结束时间
        $invoiceNum     = I('get.invoice_num', '', 'strval,trim');
        $handle         = I('get.handle', -1, 'intval');
        $enterpriseName = I('get.enterprise_name', '', 'strval,trim');
        $status         = I('get.status', -1, 'intval');
        $sysId          = I('get.sys_id', -1, 'intval');
        $tip            = I('get.tip', 1, 'intval');  //开票入口标识 1供应商开票记录列表请求  2分销商订单开票记录
        $memberId       = I('get.did', 0, 'intval');
        $source         = I('get.source', '', 'strval');

        $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage(\Model\Member\VacationMode::__GLOBAL_IDENTIFIER__, $this->loginInfo['saccount']);
        if ($vacationMode === false) {
            $this->apiReturn(403, [], '当前处于假日模式，该功能被限制使用');
        }

        if (!strtotime($start) || !strtotime($end)) {
            $beginTimeStamp = strtotime(date('Y-m-d', time()) . ' 00:00:00');
            $endTimeStamp   = strtotime(date('Y-m-d', time()) . ' 23:59:59');
        } else {
            $beginTimeStamp = strtotime($start);
            $endTimeStamp   = strtotime($end);
        }

        $beginTime    = 0;
        $endTime      = 0;
        if (!empty($start) && !empty($end)) {
            $beginTime = $beginTimeStamp;
            $endTime   = $endTimeStamp;
        }

        //实例模型
        $memberModel = new memberModel();

        if ($tip == 1) {
            // 供应商开票记录列表请求
            $sid = $this->sid;
        } else {
            // 分销商开票记录列表请求
            $sid = 0;
            $memberId = $this->sid;
        }

        //获取模型
        $result = $this->getInvoiceApi()->getEnterpriseInvoiceRecords($sid, $beginTime, $endTime, $memberId,
            $tariffNumber, $ordernum, $invoiceNum, $sysId, $handle, $enterpriseName, $status, $page, $pageSize, $tip, $source);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $list  = $result['data']['list'];
        $total = $result['data']['total'];

        //对结果值进行调整
        if (!empty($list)) {
            $orderNumArr = [];
            $staffIdArray = [];
            foreach ($list as $v) {
                $orderNumArr[] = $v['ordernum'];
                if (!empty($v['merge_list'])) {
                    $orderNumArr = array_merge($orderNumArr, $v['merge_list']);
                }
                $staffIdArray[] = $v['sid'];
                if ($v['member_id']) {
                    $staffIdArray[] = $v['member_id'];
                } else if (!empty($v['new_merge_list'])) {
                    foreach ($v['new_merge_list'] as $val) {
                        $staffIdArray[] = $val['did'];
                    }
                }
            }
            $subOrderModel   = new \Model\Order\SubOrderQuery();
            $tmpOrderInfo    = $subOrderModel->getOrderDetail($orderNumArr);
            if (count($tmpOrderInfo) != count($orderNumArr)) {
                //获取下旅游券的订单信息
                $tvOrderBiz  = new \Business\JsonRpcApi\TravelVoucherService\OrderQuery();
                $tvOrderInfo = $tvOrderBiz->getOrderInfoDetail($orderNumArr);
                if ($tvOrderInfo) {
                    $tmpOrderInfo = array_merge($tmpOrderInfo, $tvOrderInfo);
                }
            }

            $memberInfo   = $memberModel->getMemberByMixed('id', $staffIdArray, 'id, dname, account');
            $orderInfo       = array_column($tmpOrderInfo, null, 'ordernum');

            foreach ($list as $key => $value) {
                $list[$key]['total_money']     = 0;
                $list[$key]['add_time']        = intval($value['add_time']);
                $list[$key]['account']         = $memberInfo[$value['sid']]['account'];
                $list[$key]['dname']           = $memberInfo[$value['sid']]['dname'];
                if (isset($orderInfo[$value['ordernum']])) {
                    $list[$key]['tid'] = $orderInfo[$value['ordernum']]['tid'];
                }

                if (isset($value['merge_list']) && $value['merge_list']) {
                    foreach ($value['merge_list'] as $mergeOrder) {
                        $list[$key]['merge_list_map'][$mergeOrder] = isset($orderInfo[$mergeOrder]['order_type']) ? $orderInfo[$mergeOrder]['order_type'] : 1;
                    }
                }

                //订单已开票的情况 需要获取下订单的总金额
                //if ($value['invoice_status'] == 2) {
                if (isset($value['merge_list']) && $value['merge_list']) {
                    if (isset($value['original_order_info']) && isset($value['original_order_info']['totalmoney'])) {
                        $list[$key]['total_money'] = $value['original_order_info']['totalmoney'];
                    } else {
                        foreach ($value['merge_list'] as $mergeOrder) {
                            $list[$key]['total_money'] += isset($orderInfo[$mergeOrder]) ? $orderInfo[$mergeOrder]['totalmoney'] : 0;
                        }
                    }
                }

                if (!isset($value['merge_list'])) {
                    if (isset($value['original_order_info']) && isset($value['original_order_info']['totalmoney'])) {
                        $list[$key]['total_money'] = $value['original_order_info']['totalmoney'];
                    } else {
                        $list[$key]['total_money'] = isset($orderInfo[$value['ordernum']]) ? $orderInfo[$value['ordernum']]['totalmoney'] : 0;
                    }
                }
                //合并待开数据开票时间更新为0
                if ($value['records_type'] == 2 && !$value['image_url']) {
                    $list[$key]['add_time'] = 0;
                }
                //合并开票的记录可以没有固定的开票对象，需要从合并订单中获取
                $list[$key]['kpdx'] = [];
                if (!$value['member_id']) {
                    $newMergeList = $value['new_merge_list'] ?? [];
                    foreach ($newMergeList as $mergeOrder) {
                        $invoiceObject = $memberInfo[$mergeOrder['did']]['dname'];
                        $invoiceAccount = $mergeOrder['did'] == 112 ? $orderInfo[$mergeOrder['ordernum']]['contacttel'] :
                            $memberInfo[$mergeOrder['did']]['account'];
                        $_uuid = $invoiceObject . '-' . $invoiceAccount;
                        if (!isset($list[$key]['kpdx'][$_uuid])) {
                            $list[$key]['kpdx'][$_uuid] = [
                                'invoice_object' => $invoiceObject,
                                'invoice_account' => $invoiceAccount
                            ];
                        }
                    }
                    $list[$key]['kpdx'] = array_values($list[$key]['kpdx']);
                } else {
                    $list[$key]['invoice_object'] = $memberInfo[$value['member_id']]['dname'];
                    if ($value['member_id'] == 112) {
                        //使用订单手机号显示
                        if ($value['records_type'] == 2){
                            $list[$key]['invoice_account'] = $orderInfo[$value['merge_list'][0]]['contacttel'] ?? '';
                        }else{
                            $list[$key]['invoice_account'] = $orderInfo[$value['ordernum']]['contacttel'];
                        }
                    } else {
                        $list[$key]['invoice_account'] = $memberInfo[$value['member_id']]['account'];
                    }
                    $list[$key]['kpdx'][] = ['invoice_object' => $list[$key]['invoice_object'], 'invoice_account' => $list[$key]['invoice_account']];
                }
            }
        }

        $return = ['list' => $list, 'total' => $total];
        $this->apiReturn(200, $return, '获取成功');

    }

    /**
     * 保存开票配置
     * @deprecated 迁移至saveInvoiceConfigV2
     * <AUTHOR> Li
     * @date   2019-09-30
     */
    public function saveInvoiceConfig()
    {
        $id             = I('post.id', 0, 'intval');            //配置ID
        $billingParty   = I('post.billing_party', 0, 'intval'); //开票方ID
        $tid            = I('post.tid', 0, 'intval');           //门票ID
        $channel        = I('post.channel', '', 'strval');    //开票渠道
        $validityPeriod = I('post.validity_period', 0, 'intval');     //有效期
        $validityType   = I('post.validity_type', 0, 'intval');     //开票有效期 0长期有效 1有期限
        $status         = I('post.status', 0, 'intval');     //配置状态 0关闭 1开启
        $taxRate        = I('post.tax_rate');     //开票税率
        $spbm           = I('post.spbm');     //商品编码
        $distribution   = I('post.distribution', 0, 'intval');
        $otaUserList    = I('post.ota_user_list', '', 'strval');

        //获取模型
        $result = $this->getInvoiceApi()->saveInvoiceConfig($this->sid, $billingParty, $tid, $id, $channel,
            $this->memberId, $validityPeriod, $status, $validityType, $taxRate, $spbm, $distribution, $otaUserList);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取开票配置
     * @deprecated 迁移至getInvoiceConfigListV2
     * <AUTHOR> Li
     * @date   2019-09-30
     */
    public function getInvoiceConfigList()
    {
        $pName    = I('get.p_name', '', 'strval');      //产品名称
        $page     = I('get.page', 1, 'intval');         //当前页数
        $pageSize = I('get.page_size', 10, 'intval');   //每页条数
        $pType    = I('get.p_type', '', 'strval');      //产品类型
        $status   = I('get.status', 0, 'intval');       //门票状态 0全部 1上架 2下架

        if (!empty($pType) && !in_array($pType, ['A', 'F', 'I', 'Q'])) {
            $this->apiReturn(203, [], '当前搜索产品类型不支持，请确认');
        }

        $tmpPtye = empty($pType) ? ['A', 'F', 'I', 'Q'] : [$pType];

        // 获取自供应产品列表信息（包含门票）
        $productApi  = new \Business\JavaApi\Product\LandTicket();
        $productList = $productApi->querySimpleLandAndTicketPage($this->sid, $page, $pageSize, $pName, $tmpPtye, $status);
        if ($productList['code'] != 200) {
            $this->apiReturn(204, [], '产品获取出错，请稍后重试');
        }

        if ($productList['data']['total'] <= 0 || empty($productList['data']['list'])) {
            $this->apiReturn(204, [], '暂无数据');
        }

        $totalCount   = $productList['data']['total'];
        $return       = [];
        $ticketIdArr  = [];
        $productIdArr = [];
        foreach ($productList['data']['list'] as $key => $product) {
            $return[$key]['p_name'] = $product['title'];
            $return[$key]['lid']    = $product['id'];
            if (empty($product['ticketList'])) {
                continue;
            }
            if (!empty($product['moreTicketList'])) {
                //直接调取java获取票接口
                $moreTicket = $productApi->queryMoreSimpleTicket($product['moreTicketList']);
                if ($moreTicket['code'] != 200 || empty($moreTicket['data'])) {
                    $this->apiReturn(204, [], "获取失败");
                }
                $product['ticketList'] = array_merge($product['ticketList'], $moreTicket['data']);
            }
            foreach ($product['ticketList'] as $k => $ticket) {
                $channelEvolue                                  = !empty($ticket['channel']) ? explode(',',
                    $ticket['channel']) : '';
                $return[$key]['tickets'][$k]['tid']             = $ticket['id'];
                $return[$key]['tickets'][$k]['t_name']          = $ticket['title'];
                $return[$key]['tickets'][$k]['pid']             = $ticket['pid'];
                $return[$key]['tickets'][$k]['channel']         = $channelEvolue;
                $return[$key]['tickets'][$k]['enterprise_name'] = '';
                $return[$key]['tickets'][$k]['tariff_number']   = '';
                $return[$key]['tickets'][$k]['status']          = 0;
                $return[$key]['tickets'][$k]['config_id']       = 0;
                $return[$key]['tickets'][$k]['tax_rate']        = '';
                $return[$key]['tickets'][$k]['spbm']            = '';
                $ticketIdArr[]                                  = $ticket['id'];
                $productIdArr[]                                 = $ticket['pid'];
            }
        }

        if (empty($return)) {
            $this->apiReturn(204, [], '产品获取出错，请稍后重试1');
        }

        //返回门票渠道
        $channelMap     = \load_config('sale_channel');
        $channelMap[24] = '报团';
        $channelMap[44] = '计调报团';
        $channelMap[16] = 'OTA';
        $channelMap[11] = '微票房';

        //获取模型
        $invoiceConf = $this->getInvoiceApi()->getInvoiceConfigList($this->sid, $ticketIdArr);
        if ($invoiceConf['code'] == 200 && !empty($invoiceConf['data'])) {
            $invoiceConf = array_column($invoiceConf['data'], null, 'tid');
            //获取开票企业信息
            $enterpriseArr = array_unique(array_column($invoiceConf, 'billing_party'));
            if ($enterpriseArr) {
                $enterpriseInfo = $this->getInvoiceApi()->getEnterpriseInfoByIdArr($this->sid, $enterpriseArr);
                if ($enterpriseInfo['code'] == 200 && !empty($enterpriseInfo['data'])) {
                    $enterpriseInfo = array_column($enterpriseInfo['data'], null, 'id');
                    foreach ($invoiceConf as $key => $item) {
                        $invoiceConf[$key]['enterprise_name'] = $enterpriseInfo[$item['billing_party']]['enterprise_name'];
                        $invoiceConf[$key]['tariff_number']   = $enterpriseInfo[$item['billing_party']]['tariff_number'];
                    }
                }
            }

        }

        foreach ($return as $key => $product) {
            if (empty($product['tickets'])) {
                continue;
            }
            foreach ($product['tickets'] as $k => $ticket) {
                $channel = '';
                if (isset($invoiceConf[$ticket['tid']]['channel']) && !empty($ticket['channel'])) {
                    $invoiceChannel = explode(',', $invoiceConf[$ticket['tid']]['channel']);
                    $tmpChannel     = array_intersect($invoiceChannel, $ticket['channel']);
                    foreach ($this->_specialChannel as $chan) {
                        if (in_array($chan, $invoiceChannel)) {
                            $tmpChannel = array_merge($tmpChannel, [$chan]);
                        }
                    }

                    foreach ($tmpChannel as $item) {
                        $channel .= $channelMap[$item] . ' ';
                    }
                } elseif (isset($invoiceConf[$ticket['tid']]['channel']) && empty($ticket['channel'])) {
                    $tmpChannel = explode(',', $invoiceConf[$ticket['tid']]['channel']);
                    foreach ($tmpChannel as $item) {
                        $channel .= $channelMap[$item] . ' ';
                    }
                }

                $return[$key]['tickets'][$k]['channel']         = $channel;
                $return[$key]['tickets'][$k]['enterprise_name'] = $invoiceConf[$ticket['tid']]['enterprise_name'] ?? '';
                $return[$key]['tickets'][$k]['tariff_number']   = $invoiceConf[$ticket['tid']]['tariff_number'] ?? '';
                $return[$key]['tickets'][$k]['status']          = $invoiceConf[$ticket['tid']]['status'] ?? 0;
                $return[$key]['tickets'][$k]['config_id']       = $invoiceConf[$ticket['tid']]['id'] ?? 0;
                $return[$key]['tickets'][$k]['tax_rate']        = $invoiceConf[$ticket['tid']]['tax_rate'] ?? '';
                $return[$key]['tickets'][$k]['spbm']            = $invoiceConf[$ticket['tid']]['spbm'] ?? '';
                $return[$key]['tickets'][$k]['distribution']    = $invoiceConf[$ticket['tid']]['distribution'] ?? 0;
                $return[$key]['tickets'][$k]['ota_uset_list']   = $invoiceConf[$ticket['tid']]['ota_uset_list'] ?? [];
            }
        }

        $return = ['list' => $return, 'totalCount' => $totalCount];

        $this->apiReturn(200, $return, '获取成功');
    }

    /**
     * 批量开启关闭开票配置
     *
     * <AUTHOR> Li
     * @date   2019-09-30
     */
    public function saveInvoiceConfigBatch()
    {
        $idArr  = I('post.id_arr');            //配置ID
        $status = I('post.status', 0, 'intval');     //配置状态 0关闭 1开启
        $sid    = $this->sid;

        if (empty($idArr) || !is_array($idArr)) {
            $this->apiReturn(203, [], '参数有误');
        }

        if ($sid == 1) {
            $sid = 0;
        }

        //获取模型
        $result = $this->getInvoiceApi()->saveInvoiceConfigBatch($idArr, $sid, $status);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取开票方列表
     *
     * <AUTHOR> Li
     * @date   2019-09-30
     */
    public function getEnterpriseOpenRecordsList()
    {
        $sid = $this->sid;

        if ($sid == 1) {
            $sid = 0;
        }

        $enterPriseInfo = $this->getInvoiceApi()->getEnterpriseOpenRecordsList($sid);
        if ($enterPriseInfo['code'] == 200 && !empty([$enterPriseInfo['data']])) {
            $this->apiReturn(200, $enterPriseInfo['data'], '获取成功');
        }

        $this->apiReturn(200, [], '暂无数据');
    }

    /**
     * 获取开票配置
     *
     * <AUTHOR> Li
     * @date   2019-09-30
     */
    public function getInvoiceConfigByTid()
    {
        $tid = I('get.tid', 0, 'intval');//门票id

        //获取模型
        $ticketBiz     = new Ticket();
        $invoiceConfig = $this->getInvoiceApi()->getInvoiceConfigByTid($tid, $this->sid);

        if ($invoiceConfig['code'] != 200) {
            $this->apiReturn($invoiceConfig['code'], $invoiceConfig['data'], $invoiceConfig['msg']);
        }

        $invoiceConfig = $invoiceConfig['data'];

        $return = [];
        $filterChannel = \load_config('sale_channel');
        //$filterChannel = [
        //    5  => '分销后台',
        //    1  => '微商城',
        //    11 => '微票房',
        //    7  => '散客窗口',
        //    9  => '团队窗口',
        //    8  => 'POS终端',
        //    3  => '自助机',
        //    12 => '计调下单',
        //    10 => '微平台',
        //    18 => 'APP',
        //];
        $ticketInfo = $ticketBiz->queryTicketInfoById($tid);
        if ($ticketInfo['code'] != 200) {
            $this->apiReturn(204, [], '门票信息获取失败');
        }
        $productInfo = $ticketInfo['data'];
        $pid         = $productInfo['uuProductDTO']['id'];
        $pType       = $productInfo['uuLandDTO']['pType'];

        //$specialChannel = [];
        if (in_array($pType, ["A", "F"])) {
            $filterChannel[24] = '报团';
            $filterChannel[44] = '计调报团';
            $filterChannel[16] = 'OTA';
            //$specialChannel    = ',' . implode(',', $this->_specialChannel);
        }
        $saleChannel = implode(',', array_keys($filterChannel));
        foreach ($filterChannel as $idx => $name) {
            $return['channel_map'][] = ['id' => $idx, 'name' => $name,];
        }

        //获取销售渠道
        //$saleChannel       = [];
        //$productChannel    = $productInfo['uuJqTicketDTO']['shop'];
        //$ticketAttributeBz = new TicketAttribute();
        //$result            = $ticketAttributeBz->getTicketAttribute($this->sid, $this->sid, -1, '', -1, [],
        //    0, 0, '', [], 0, 0, [$pid], 1);
        //if (isset($result['code']) && $result['code'] == 200) {
        //    $tickets = $result['data']['list'];
        //    foreach ($tickets as $ticket) {
        //        $saleChannel[$ticket['tid']] = $ticket['channel'];
        //        if (in_array($pType, ["A", "F"])) {
        //            if (strpos($ticket['channel'], '16') === false) {
        //                $saleChannel[$ticket['tid']] .= ',16';
        //            }
        //            if (strpos($ticket['channel'], '24') === false) {
        //                $saleChannel[$ticket['tid']] .= ',24';
        //            }
        //            if (strpos($ticket['channel'], '44') === false) {
        //                $saleChannel[$ticket['tid']] .=  ',44';
        //            }
        //        }
        //    }
        //}

        //发票特殊渠道补充
        //if (!$saleChannel) {
        //    $productChannel .= $specialChannel;
        //}

        //$saleChannel  = [];
        //$evoluteModel = new \Model\Product\Ticket();
        //$channelInfo  = $evoluteModel->getApplyEvolute($tid, $this->sid, $this->sid, 'ticket_id, channel');
        //if ($channelInfo) {
        //    foreach ($channelInfo as $item) {
        //        $saleChannel[$item['ticket_id']] = $item['channel'];
        //    }
        //}
        $return['ticket_info'] = [
            'p_name'          => $productInfo['uuProductDTO']['pName'],
            't_name'          => $productInfo['uuJqTicketDTO']['title'],
            'shop'            => $saleChannel,
            'tid'             => $tid,
            'p_type'          => $pType,
            'channel'         => '',
            'validity_period' => 0,
            'validity_type'   => 0,
            'billing_party'   => 0,
            'status'          => 0,
            'tax_rate'        => '',
            'spbm'            => '',
            'definitio'       => $invoiceConfig['use_product_tag'],
        ];
        if (!empty($invoiceConfig)) {
            $return['ticket_info']['tid']             = $tid;
            $return['ticket_info']['channel']         = $invoiceConfig['channel'];
            $return['ticket_info']['billing_party']   = $invoiceConfig['billing_party'];
            $return['ticket_info']['validity_period'] = $invoiceConfig['validity_period'];
            $return['ticket_info']['validity_type']   = $invoiceConfig['validity_type'];
            $return['ticket_info']['config_id']       = $invoiceConfig['id'];
            $return['ticket_info']['status']          = $invoiceConfig['status'];
            $return['ticket_info']['tax_rate']        = $invoiceConfig['tax_rate'];
            $return['ticket_info']['spbm']            = $invoiceConfig['spbm'];
            $return['ticket_info']['ota_user_list']   = $invoiceConfig['ota_user_list'] ?? [];
            $return['ticket_info']['distribution']    = $invoiceConfig['distribution'] ?? 0;
            $return['ticket_info']['add_time']        = $invoiceConfig['add_time'];
            $return['ticket_info']['distribution_update'] = $invoiceConfig['distribution_update'];
        }

        $this->apiReturn(200, $return, '获取成功');
    }

    /**
     * 通过订单号获取出该笔订单是否能开票
     *
     * <AUTHOR> Li
     * @date   2019-10-11
     */
    public function invoiceCheckByOrderNum()
    {
        $orderNum = I('post.ordernum');//订单号
        if (empty($orderNum)) {
            $this->apiReturn(203, [], '订单参数缺失');
        }
        $return = [
            'ordernum'       => $orderNum,  //订单号
            'print_invoices' => false,      //是否能开票  false 不能 1 true 能
        ];

        $cheRes = $this->getInvoiceApi()->checkInvoiceAuth([$orderNum], $this->sid);

        if ($cheRes['code'] == 200 && $cheRes['data']['code'] == 200) {
            $return['print_invoices'] = true;
            $returnMsg                = '可以开票';
            $returnCode               = 200;
        } else {
            $returnMsg  = $cheRes['data']['msg'];
            $returnCode = 204;
        }

        $this->apiReturn($returnCode, $return, $returnMsg);
    }

    /**
     * 保存开票人相关信息
     *
     * <AUTHOR> Li
     * @date   2019-12-12
     */
    public function saveBillerConfig()
    {
        $id       = I('post.id', 0, 'intval');       //配置id
        $sid      = I('post.sid');                   //供应商id
        $drawer   = I('post.drawer', '', 'strval');  //开票方
        $payer    = I('post.payer', '', 'strval');   //收款人
        $reviewer = I('post.reviewer', '', 'strval');//复核人
        $expand   = I('post.expand', []);//扩展字段

        if (!$sid || !$drawer) {
            $this->apiReturn(203, [], '参数错误');
        }

        $result = $this->getInvoiceApi()->saveBillerConfig($sid, $id, $drawer, $payer, $reviewer, $expand);

        if ($result['code'] != 200) {
            $this->apiReturn(204, [], $result['msg'] ?: '保存失败');
        }

        $this->apiReturn(200, [], '保存成功');
    }

    /**
     * 保存开票配置扩展属性信息
     */
    public function saveExtensionConfig()
    {
        $id           = I('post.id', 0, 'intval');                  //配置id
        $billingParty = I('post.billing_party', 0, 'intval');       //开票方
        $type         = I('post.type', 'shared_lease', 'strval');   //配置标签  shared_lease 共享租赁
        $taxRate      = I('post.tax_rate', '0', 'strval');            //配置的税率
        $spbm         = I('post.spbm', '', 'strval');               //配置的商品编码
        $productTag   = I('post.product_tag', '', 'strval');        //配置的自定义标签

        if (!$id || !$spbm || !$billingParty) {
            $this->apiReturn(203, [], '参数错误');
        }

        $result = $this->getInvoiceApi()->saveExtensionConfig($this->sid, $id, $billingParty, $taxRate, $spbm, $type, $productTag);

        if ($result['code'] != 200) {
            $this->apiReturn(204, [], $result['msg'] ?: '保存失败');
        }

        $this->apiReturn(200, [], '保存成功');
    }


    /**
     * 获取开票人相关信息
     *
     * <AUTHOR> Li
     * @date   2019-12-12
     */
    public function getBillerConfig()
    {
        $sid = I('post.sid');     //供应商id

        if (!$sid) {
            $this->apiReturn(203, [], '供应商信息有误');
        }

        $billerInfo = $this->getInvoiceApi()->getBillerConfig($sid);
        if ($billerInfo['code'] != 200 || empty($billerInfo['data'])) {
            $this->apiReturn(200, [], '获取失败');
        }

        $this->apiReturn(200, $billerInfo['data'], '获取成功');

    }

    /**
     * 获取分销商可开票列表
     *
     * <AUTHOR> Li
     * @date   2020-03-26
     */
    public function getDistributorInvoiceList()
    {
        $sid          = I('post.sid', 0, 'intval');                     //供应商id
        $orderNum     = I('post.order_num', '', 'strval');               //查询订单号
        $startTime    = I('post.start_time', 0);              //下单开始时间
        $endTime      = I('post.end_time', 0);                //下单结束时间
        $type         = I('post.type', -1, 'intval');       //开票状态 -1全部 0未开票 1进行中 2已开票
        $page         = I('post.page', 1, 'intval');       //当前页数
        $pageSize     = I('post.page_size', 10, 'intval'); //每页条数
        $did          = $this->sid;                        //当前登录的用户
        $taxRate      = I('post.tax_rate', '', 'strval');
        $status       = I('post.status', -1, 'intval');
        $start        = I('post.start', 0);
        $end          = I('post.end', 0);
        $tariffNumber = I('post.tariff_number', '', 'strval');
        $tariffName   = I('post.tariff_name', '', 'strval');
        $spbm         = I('post.spbm', '', 'strval');
        $endTimeType  = I('post.end_time_time', 0, 'intval');
        $tid          = I('post.tid', 0, 'intval');
        $productTag   = I('post.product_tag', '', 'strval');  //商品标签
        $type         = 0;

        $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage(\Model\Member\VacationMode::__GLOBAL_IDENTIFIER__, $this->loginInfo['saccount']);
        if ($vacationMode === false) {
            $this->apiReturn(403, [], '当前处于假日模式，该功能被限制使用');
        }

        if (!$did) {
            $this->apiReturn(203, [], '用户信息有误');
        }

        if (!empty($startTime) && !empty($endTime) && strtotime($startTime) && strtotime($endTime)) {
            $beginTimeStamp = strtotime($startTime);
            $endTimeStamp   = strtotime($endTime);
        } else {
            $beginTimeStamp = 0;
            $endTimeStamp   = 0;
        }
        if (!empty($start) && !empty($end) && strtotime($start) && strtotime($end)) {
            $start = strtotime($start);
            $end   = strtotime($end);
        }else{
            $start = 0;
            $end   = 0;
        }

        $result = $this->getInvoiceApi()->getInvoicableList($this->sid, $page, $pageSize, $did, $sid, $orderNum,
            $beginTimeStamp, $endTimeStamp, $type, $taxRate, $status, $start, $end, $tariffNumber, $tariffName,
            $spbm, $endTimeType, $tid, $productTag);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $list  = $result['data']['list'];
        $total = $result['data']['total'];
        //对结果值进行调整
        if (!empty($list)) {
            //实例模型
            $memberModel  = new memberModel();
            $staffIdArray = array_merge(array_column($list, 'sid'), array_column($list, 'did'));
            $orderArray = array_column($list, 'ordernum');
            $orderInfo   = (new \Model\Order\OrderTools())->getOrderInfo($orderArray);
            if ($orderInfo) {
                $orderInfo = array_column($orderInfo, null, 'orderid');
                foreach ($orderInfo as $item) {
                    if (!empty($item['aids'])) {
                        $staffIdArray = array_unique(array_merge($staffIdArray, explode(',', $item['aids'])));
                    }
                }
            }
            $memberInfo    = $memberModel->getMemberByMixed('id', $staffIdArray, 'id, dname');

            $commonOrderNum = [];
            $tvOrderNum = [];
            foreach ($list as $item) {
                if ($item['order_type'] == 2) {
                    $tvOrderNum[] = $item['ordernum'];
                } else {
                    $commonOrderNum[] = $item['ordernum'];
                }
            }
            $tmpOrderInfo = $this->getInvoiceBiz()->getInvoiceOrderInfo('', $commonOrderNum, $tvOrderNum, '', $sid);
            if ($tmpOrderInfo['code'] != 200 || empty($tmpOrderInfo['data'])) {
                $this->apiReturn(204, [], '订单信息获取失败');
            }
            $orderInfo       = array_column($tmpOrderInfo['data'], null, 'ordernum');

            foreach ($list as $key => $value) {
                $list[$key]['s_name']     = $memberInfo[$value['sid']] ?? '';
                $list[$key]['d_name']     = $memberInfo[$value['did']] ?? '';
                $list[$key]['d_account']  = $memberInfo[$value['did']] ?? '';
                $orderItem = $orderInfo[$value['ordernum']];
                $distributor = array_filter($orderItem['aids_money'], function ($item) use ($value) {
                    return $value['did'] == $item['fid'] && $value['sid'] == $item['aid'];
                });
                if (!$distributor) {
                    $list[$key]['totalmoney'] = 0;
                } else {
                    $distributor = array_shift($distributor);
                    $list[$key]['totalmoney'] = $distributor['money'];
                }
                if ($value['did'] == 112) {
                    $list[$key]['tel'] = $orderInfo[$value['ordernum']]['contacttel'];
                }
                if (in_array($value['sys_id'], [1, 3])) { //1=诺诺普票 3=诺诺数电票
                    $list[$key]['uuid'] = $value['billing_party'];
                } else {
                    $kpdx = $value['did'] == 112 ? $orderInfo[$value['ordernum']]['contacttel'] : $value['did'];
                    $list[$key]['uuid'] = md5(implode('-', [$value['billing_party'], $value['spbm'], $value['tax_rate'], $kpdx]));
                }
            }
        }

        $return = ['list' => $list, 'total' => $total];
        $this->apiReturn(200, $return, '获取成功');

    }

    /**
     * 获取供应商可开票列表
     *
     * <AUTHOR> Li
     * @date   2020-03-26
     */
    public function getApplyInvoiceList()
    {
        $fid                     = I('post.fid', 0, 'intval');            //分销商id
        $orderNum                = I('post.order_num', '', 'strval');     //查询订单号
        $startTime               = I('post.start_time', '');              //下单开始时间
        $endTime                 = I('post.end_time', '');                //下单结束时间
        $type                    = I('post.type', 0, 'intval');           //开票状态 0全部 1未开票 2已开票
        $page                    = I('post.page', 1, 'intval');           //当前页数
        $pageSize                = I('post.page_size', 10, 'intval');     //每页条数
        $sid                     = $this->sid;                            //当前登录的用户
        $taxRate                 = I('post.tax_rate', '', 'strval');
        $status                  = I('post.status', -1, 'intval');
        $start                   = I('post.start', 0);
        $end                     = I('post.end', 0);
        $tariffNumber            = I('post.tariff_number', '', 'strval');
        $tariffName              = I('post.tariff_name', '', 'strval');
        $spbm                    = I('post.spbm', '', 'strval');
        $endTimeType             = I('post.end_time_time', 0, 'intval');
        $tid                     = I('post.tid', 0, 'intval');
        $productTag              = I('post.product_tag', '', 'strval');

        $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage(\Model\Member\VacationMode::__GLOBAL_IDENTIFIER__, $this->loginInfo['saccount']);
        if ($vacationMode === false) {
            $this->apiReturn(403, [], '当前处于假日模式，该功能被限制使用');
        }

        if (!empty($startTime) && !empty($endTime) && strtotime($startTime) && strtotime($endTime)) {
            $beginTimeStamp = strtotime($startTime);
            $endTimeStamp   = strtotime($endTime);
        }else{
            $beginTimeStamp = 0;
            $endTimeStamp   = 0;
        }
        if (!empty($start) || !empty($end) || strtotime($start) || strtotime($end)) {
            $start = strtotime($start);
            $end   = strtotime($end);
        }else{
            $start = 0;
            $end   = 0;
        }

        $result = $this->getInvoiceApi()->getInvoicableList($this->sid, $page, $pageSize, $fid, $sid, $orderNum,
            $beginTimeStamp, $endTimeStamp, $type, $taxRate, $status, $start, $end, $tariffNumber, $tariffName, $spbm,
            $endTimeType, $tid, $productTag);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $list  = $result['data']['list'];
        $total = $result['data']['total'];

        //对结果值进行调整
        if (!empty($list)) {
            //实例模型
            $memberModel  = new memberModel();
            $staffIdArray = array_merge(array_column($list, 'sid'), array_column($list, 'did'));
            $memberInfo   = $memberModel->getMemberByMixed('id', $staffIdArray, 'id, dname,account');

            $commonOrderNum = [];
            $tvOrderNum = [];
            foreach ($list as $item) {
                if ($item['order_type'] == 2) {
                    $tvOrderNum[] = $item['ordernum'];
                } else {
                    $commonOrderNum[] = $item['ordernum'];
                }
            }

            $tmpOrderInfo = $this->getInvoiceBiz()->getInvoiceOrderInfo('', $commonOrderNum, $tvOrderNum, '', $sid);
            if ($tmpOrderInfo['code'] != 200 || empty($tmpOrderInfo['data'])) {
                $this->apiReturn(204, [], '订单信息获取失败');
            }
            $orderInfoStatus = array_column($tmpOrderInfo['data'], 'status', 'ordernum');
            $orderInfo       = array_column($tmpOrderInfo['data'], null, 'ordernum');

            $orderStatusConf = load_config('order_status', 'orderSearch');
            foreach ($list as $key => $value) {
                $list[$key]['s_name']     = $memberInfo[$value['sid']]['dname'];
                $list[$key]['d_name']     = $memberInfo[$value['did']]['dname'];
                $list[$key]['d_account']  = $memberInfo[$value['did']]['account'];
                $list[$key]['status']     = $orderStatusConf[$orderInfoStatus[$value['ordernum']]] ?? '未知';
                $orderItem = $orderInfo[$value['ordernum']];
                $distributor = array_filter($orderItem['aids_money'], function ($item) use ($value) {
                   return $value['did'] == $item['fid'] && $value['sid'] == $item['aid'];
                });
                if (!$distributor) {
                    $list[$key]['totalmoney'] = 0;
                } else {
                    $distributor = array_shift($distributor);
                    $list[$key]['totalmoney'] = $distributor['money'];
                }
                if ($value['did'] == 112) {
                    $list[$key]['tel'] = $orderInfo[$value['ordernum']]['contacttel'];
                }
                if (in_array($value['sys_id'], [1, 3])) { //1=诺诺普票 3=诺诺数电票
                    $list[$key]['uuid'] = $value['billing_party'];
                } else {
                    $kpdx = $value['did'] == 112 ? $orderInfo[$value['ordernum']]['contacttel'] : $value['did'];
                    $list[$key]['uuid'] = $value['billing_party'] . '-' . $value['spbm'] . '-' . $value['tax_rate'] . '-' . $kpdx;
                }
            }
        }

        $return = ['list' => $list, 'total' => $total];
        $this->apiReturn(200, $return, '获取成功');

    }

    /**
     * 获取当前用户添加得开票系统列表
     * <AUTHOR>
     * @date 2021-08-31
     *
     * @return array
     */
    public function getUserSysList()
    {
        $result = $this->getInvoiceApi()->getUserSysList($this->sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg'], $result['data']);
    }

    /**
     * 切换当前用户使用开票系统
     * <AUTHOR>
     * @date 2021-08-31
     *
     * @return array
     */
    public function changeSys()
    {
        $sysId = I('post.sys_id', 0, 'intval');
        if (!$sysId) {
            $this->apiReturn(203, '参数错误', []);
        }
        $result = $this->getInvoiceApi()->changeSys($this->sid, $sysId, $this->memberId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 添加使用开票系统
     * <AUTHOR>
     * @date 2021-08-31
     *
     * @return array
     */
    public function addUserSys()
    {
        $sysId = I('post.sys_id', 0, 'intval');
        if (!$sysId) {
            $this->apiReturn(203, '参数错误', []);
        }
        $result = $this->getInvoiceApi()->addUserSys($this->sid, $sysId, $this->memberId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除使用开票系统
     * <AUTHOR>
     * @date 2021-08-31
     *
     * @return array
     */
    public function delUserSys()
    {
        $sysId = I('post.sys_id', 0, 'intval');
        if (!$sysId) {
            $this->apiReturn(203, '参数错误', []);
        }

        $result = $this->getInvoiceApi()->delUserSys($this->sid, $sysId, $this->memberId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取当前用户使用开票系统id
     * <AUTHOR>
     * @date 2021-08-31
     *
     * @return array
     */
    public function getUserNowSysId()
    {
        $result = $this->getInvoiceApi()->getUserNowSys($this->sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 手动百旺待开数据刷新
     *
     * <AUTHOR>
     * @date 2021-11-18
     */
    public function baiwangManualUpdate()
    {
        $opid = I('post.opid', 0, 'intval');
        $sid  = I('post.sid', 0, 'intval');
        $did  = I('post.did', 0, 'intval');

        $result = $this->getInvoiceApi()->baiwangManualUpdate($opid, $sid, $did);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 保存自定义商品名称信息
     *
     * <AUTHOR>
     * @date  2021-12-03
     * @param int $sid 供应商
     * @param string $type 操作类型  add 新增 update 更新
     * @param array $add  ['landid' => ['tids','tids1']]
     * @param array $del
     * @param string $name
     * @param string $taxRate
     * @param string $spbm
     * @param int $id
     * @param int $opid
     *
     * @return array
     */
    public function saveProductDefinitionNameInfo()
    {
        $submitData         = I('post.');
        $submitData['data'] = htmlspecialchars_decode($submitData['data']);
        $submitData         = json_decode($submitData['data'], true);

        //参数赋值
        $type    = $submitData['type'] ?? 'add';
        $name    = $submitData['name'] ?? '';
        $taxRate = $submitData['tax_rate'] ?? '0';
        $spbm    = $submitData['spbm'] ?? '0';
        $del     = $submitData['del'] ?? [];
        $id      = $submitData['id'] ?? 0;

        if (!in_array($type, ['add', 'update'])) {
            $this->apiReturn(203, '参数类型错误', []);
        }

        if (!$del && !is_array($del)) {
            $del = [];
        }

        //新增的数据做个处理
        $add = [];
        if (isset($submitData['add']) && $submitData['add'] && is_array($submitData['add'])) {
            foreach ($submitData['add'] as $idx => $item) {
                if (!is_array($item)) {
                    $this->apiReturn(203, '参数格式错误', []);
                }
                foreach ($item as $lid => $tidArr) {
                    $add[$lid] = $tidArr;
                }
            }
        }

        $result = $this->getInvoiceApi()->saveProductDefinitionNameInfo($type, $this->sid, $add, $del, $name, $taxRate,
            $spbm, $id, $this->memberId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取自定义商品记录
     * <AUTHOR>
     * @date   2021-11-11
     *
     * @param  int $sid 供应商id
     * @param  int $landid 景区id
     * @param  int|array $tid 票id
     * @param  string $spbm  商品编码
     * @param  string $name 定义商品名称
     *
     * @return array
     */
    public function productDefinitionNameList()
    {
        $landid = I('post.land_id', 0, 'intval');
        $tid    = I('post.tid', 0, 'intval');
        $name   = I('post.name', '', 'strval');
        $spbm   = I('post.spbm', '', 'strval');
        $page   = I('post.page', 1, 'intval');
        $size   = I('post.size', 10, 'intval');

        $this->apiReturn(200);
//        $result = $this->getInvoiceApi()->productDefinitionNameList($this->sid, $landid, $tid, $name, $spbm, $page, $size);
//
//         $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    /**
     * 获取自定义商品记录
     * <AUTHOR>
     * @date   2021-11-11
     *
     * @param  int $sid 供应商id
     * @param  int $landid 景区id
     * @param  int|array $tid 票id
     * @param  string $spbm  商品编码
     * @param  string $name 定义商品名称
     *
     * @return array
     */
    public function delDefinitionName()
    {
        $id    = I('post.id', 0, 'intval');

        $result = $this->getInvoiceApi()->delDefinitionName($id, $this->memberId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取销售产品列表(允许产品类型 A景区，F套餐、I年卡)
     * 直接复制以前的接口
     * <AUTHOR>
     * @date 2021/10/13
     *
     */
    public function getProTicketType()
    {
        $name = I('name');
        $type = 'A,F,I,Q';
        $pro  = new ProductList();

        $res = $pro->getListSupplierId($this->sid, 30, $name, $type, false);
        if ($res['code'] == 200) {

            $this->apiReturn(200, $res['data'], '获取成功');
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
    }


    public function test()
    {
        $result = $this->getInvoiceApi()->test();

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 判断供应商是否有开票的权限
     * <AUTHOR>
     * @date 2021/3/30
     */
    public function getEnterpriseOpenRecordsByMixed()
    {
        $sid = $this->sid;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $authInvoice = 0;
        //判断供应商是否有开票的权限
        $invoiceApi = new \Business\ElectronicInvoices\InvoiceApi();
        $result     = $invoiceApi->getEnterpriseOpenRecordsByMixed($sid);
        if ($result['code'] == 200) {
            $authInvoice = 1;
        }

        return $this->apiReturn(200, ['authInvoice' => $authInvoice], '');
    }

    /**
     * 获取当前用户使用开票系统
     * <AUTHOR>  Li
     * @date 2022-01-14
     *
     * @return array
     */
    public function getUserNowSysInfo()
    {
        $result = $this->getInvoiceApi()->getUserNowSysInfo($this->sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 分页模糊搜索商品名配置
     * <AUTHOR>  Li
     * @date   2022-01-18
     */
    public function getDefinitionRecordsListPage()
    {
        $name = I('post.name', '', 'strval');
        $page = I('post.page', 1, 'intval');
        $size = I('post.size', 10, 'intval');

        if (!$name) {
            $this->apiReturn(203, '商品名称不能为空', []);
        }

        $invoiceBiz = new \Business\ElectronicInvoices\Invoice();
        $result     = $invoiceBiz->getDefinitionRecordsListPage($this->sid, $name, $page, $size);
        if ($result['code'] != 200 || empty($result['data'])) {
            $this->apiReturn(204, [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '获取成功');
    }

    /**
     * 取消开发票
     * <AUTHOR>  Li
     * @date  2022-03-18
     */
    public function cancelInvoice()
    {
        $recordId = I('post.id');
        $sid      = I('post.sid', '0', 'intval');
        if (!$recordId) {
            $this->apiReturn(203, [], '参数有误');
        }

        $invoiceBiz = new \Business\ElectronicInvoices\Invoice();
        $result     = $invoiceBiz->invoiceCancel($recordId, $sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 红冲发票
     * <AUTHOR>  Li
     * @date  2022-11-10
     */
    public function invoiceRedDashed()
    {
        $recordId = I('post.id');
        $sid      = I('post.sid', '0', 'intval');
        if (!$recordId) {
            $this->apiReturn(203, [], '参数有误');
        }

        $invoiceBiz = new \Business\ElectronicInvoices\Invoice();
        $result     = $invoiceBiz->invoiceRedDashed($recordId, $sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 保存开票配置
     */
    public function saveInvoiceConfigV2()
    {
        $id             = I('post.id', 0, 'intval');            //配置ID
        $billingParty   = I('post.billing_party', 0, 'intval'); //开票方ID
        $tid            = I('post.tid', '', 'strval');           //门票ID列表，逗号分隔
        $channel        = I('post.channel', '', 'strval');    //开票渠道
        $validityPeriod = I('post.validity_period', 0, 'intval');     //有效期
        $validityType   = I('post.validity_type', 0, 'intval');     //开票有效期 0长期有效 1有期限
        $taxRate        = I('post.tax_rate');     //开票税率
        $spbm           = I('post.spbm');     //商品编码
        $distribution   = I('post.distribution', 0, 'intval');
        $otaUserList    = I('post.ota_user_list', '', 'strval');
        $useProductTag  = I('post.use_product_tag', 0, 'intval'); // 是否开启商品标签
        $productTag     = I('post.product_tag', '', 'strval'); //商品标签
        $favouredPolicy = I('post.favoured_policy', 0, 'intval'); //是否使用优惠政策 0=不使用 1=使用
        $sdFavouredPolicy = I('post.sd_favoured_policy', 0, 'intval'); //数电政策类型0=不使用 3=免税 8=3%简易征收 9=5%简易征收

        //获取模型
        $result = $this->getInvoiceApi()->saveInvoiceConfigV2($this->sid, $billingParty, $tid, $id, $channel,
            $this->memberId, $validityPeriod, $validityType, $taxRate, $spbm, $distribution, $otaUserList,
            $useProductTag, $productTag, $favouredPolicy, $sdFavouredPolicy);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取可配置的票列表
     * @return void
     */
    public function getInvoiceTicketList() {
        $pageNum = I('page_num', 1, 'intval');
        $pageSize = I('page_size', 20, 'intval');
        $keyword   = I('keyword', '', 'strval');
        //门票范围 1=在售的门票 2=下架的门票 6=已删除的门票 99已过期 -1=不限
        $ticketScope = I('ticket_scope', '-1', 'strval');
        //商品来源 2-自供应 1-转分销
        $supplyType = I('supply_type', 2, 'intval');
        $res = $this->getInvoiceApi()->getInvoiceTicketList($this->sid, $keyword, $ticketScope,
            $supplyType, $pageNum, $pageSize);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取配置中已选择的票
     * @return void
     */
    public function getSelectedTicketList() {
        $configId = I('config_id', 0, 'intval'); //配置ID
        $res = $this->getInvoiceApi()->getSelectedTicketList($this->sid, $configId);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 保存关联产品
     * @return void
     */
    public function saveInvoiceConfigRelation() {
        $id             = I('post.id', 0, 'intval');            //配置ID
        $tid            = I('post.tid', '', 'strval');           //门票ID列表，逗号分隔
        $res = $this->getInvoiceApi()->saveInvoiceConfigRelation($this->sid, $this->memberId, $id, $tid);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 配置启停
     * @return void
     */
    public function updateInvoiceConfigStatus() {
        $id = I('post.id', '', 'strval');            //配置ID
        $status = I('post.status', -1, 'intval');      //状态
        if (!$id || !in_array($status, [0, 1])) {
            $this->apiReturn(204, '参数错误');
        }
        $ids = explode(',', $id);
        $res = $this->getInvoiceApi()->updateInvoiceConfigStatus($this->sid, $ids, $status);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 删除开票配置
     * @return void
     */
    public function delInvoiceConfig() {
        $id = I('post.id', 0, 'strval'); //配置ID
        if (!$id) {
            $this->apiReturn(204, '参数错误');
        }
        $ids = explode(',', $id);
        $res = $this->getInvoiceApi()->delInvoiceConfig($this->sid, $ids);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取spu列表
     * @return void
     */
    public function getSpuList() {
        $keyword = I('keyword', '', 'strval');
        $pageNum = I('page_num', 1, 'intval');
        $pageSize = I('page_size', 20, 'intval');
        $res = $this->getInvoiceApi()->getSpuList($this->sid, $keyword, $pageNum, $pageSize);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取sku列表
     * @return void
     */
    public function getSkuList() {
        $spuId = I('spu_id', 0, 'intval');
        $keyword = I('keyword', '', 'strval');
        $pageNum = I('page_num', 1, 'intval');
        $pageSize = I('page_size', 20, 'intval');
        $res = $this->getInvoiceApi()->getSkuList($this->sid, $spuId, $keyword, $pageNum, $pageSize);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取发票配置列表
     * @return void
     */
    public function getInvoiceConfigListV2() {
        $spuId = I('spu_id', 0, 'intval');
        $skuId = I('sku_id', 0, 'intval');
        $spbm = I('spbm', '', 'strval');     //商品编码
        $productTag = I('product_tag', '', 'strval');
        $productTagStatus = I('product_tag_status', -1, 'intval');
        $status = I('status', -1, 'intval');
        $pageNum = I('page_num', 1, 'intval');
        $pageSize = I('page_size', 15, 'intval');
        $res = $this->getInvoiceApi()->getInvoiceConfigListV2($this->sid, $pageNum, $pageSize, $spuId, $skuId, $spbm, $productTag,
            $productTagStatus, $status);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }
        $this->apiReturn($res['code'], ['list' => $res['data']['list'] ?? [], 'total' => $res['data']['total'] ?? 0]);
    }

    /**
     * 获取可选渠道列表(下单渠道)
     * @return void
     */
    public function getChannelList()
    {
        $saleChannelMap = load_config('sale_channel', 'business');
        $saleChannelMap[24] = '报团';
        $saleChannelMap[44] = '计调报团';
        $saleChannelMap[16] = 'OTA';
        $list = [];
        foreach ($saleChannelMap as $id => $name) {
            $list[] = compact('id', 'name');
        }
        $this->apiReturn(200, $list);
    }

    /**
     * @return void
     */
    public function getEnterpriseInvoiceConditionList()
    {
        $receiptChannel = $this->getInvoiceApi()->getReceiptChannelList();
        $this->apiReturn(200, [
            'receiptChannel' => $receiptChannel
        ]);
    }
}
