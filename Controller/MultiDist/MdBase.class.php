<?php
/**
 * 分销专员控制基类
 * <AUTHOR>
 * @date 2020/3/27 0027
 */

namespace Controller\MultiDist;

use Library\Controller;

class MdBase extends Controller
{
    protected $memberInfo = [];//当前登录用户基础信息

    protected $uuid = null;//当前登录用户的分销专员唯一标识

    //分销专员身份信息 运营商/分销专员身份
    protected $currentRole;

    protected $noCheckLoginAction = ['sendMobileCode', 'register', 'pageInfo','mobileInvitePoster'];

    protected $isSuper = false;


    public function __construct()
    {
        parent::__construct();
        $this->isSuper = $this->isSuper();

        if (!in_array($_GET['a'], $this->noCheckLoginAction)) {
            //判断登录获取用户信息
            $this->memberInfo = $this->getLoginInfo();

            $result = (new \Business\MultiDist\Member())->getMultiDistCache($this->memberInfo['sid']);
            if (!$result || !$result['multi_dist']) {
                $this->apiReturn(400, [], '无分销专员相关权限!');
            }
            // $this->uuid =  $this->memberInfo['multi_dist_uuid'];
            // $this->currentRole = $this->memberInfo['multi_dist_role'];
            $this->uuid = $result['multi_dist']['uuid'];
            $this->currentRole = $result['multi_dist']['role'];

            if (!$this->isSuper && !$this->uuid) {
                $this->apiReturn(400, [], '无分销专员相关权限!');
            }
        }
    }
}