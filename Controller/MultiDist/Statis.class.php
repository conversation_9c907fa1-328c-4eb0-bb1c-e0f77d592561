<?php
/**
 * 分销专员收益汇总相关
 * <AUTHOR>
 * @date 2020/4/3
 */

namespace Controller\MultiDist;
use Business\MultiDist\Statis as StatisBiz;
use \Model\MultiDist\Member as MultiDistMemberModel;
use Business\MultiDist\Base as MultiDistBase;
use Model\MultiDist\BaseModel as MultiDistBaseModel;
use Business\MultiDist\Member as MemberBiz;

class Statis extends MdBase
{


    public function __construct()
    {
        parent::__construct();
    }


    /**
     * 推广订单
     * <AUTHOR>
     * @date    2020/4/14
     *
     * @return
     */
    public function orderList()
    {
        $code       = 200;
        $msg        = '';
        $orderList  = [];

        try {

            $ordernum       = I('ordernum', '', 'strval');
            $timeStart      = I('start_date', '2020-01-01', 'strval');
            $timeEnd        = I('end_date', '2030-01-01', 'strval');
            $timeType       = I('time_type', 1, 'int');
            $chainUuid      = I('chain_uuid', '', 'strval');
            $settleStatus   = I('settle_status', -1, 'int');

            $searchType     = I('key_type', 1, 'int');   //1=根据分销专员名称搜索， 2=根据手机号搜索
            $searchKeyWord  = I('key_word', '', 'strval');

            $page     = I('page', 1, 'intval');
            $pageSize = I('page_size', 20, 'intval');

            if (!in_array($timeType, [1, 2])) {
                throw new \Exception('时间参数类型错误');
            }

            $multiDistRole = 0;
            //推广的分销专员
            $populUuidList = [];
            $queryUuidList = [];

            if (!$this->isSuper) {

                if (!$chainUuid) {
                    throw new \Exception('非法请求');
                }

                //普通身份只能查询自己及下级的
                if ($this->currentRole == MultiDistBase::NOMAL_ROLE && $chainUuid != $this->uuid) {
                    $isAllowCheck = (new MemberBiz())->isLowerLevel($chainUuid, $this->uuid);
                    if (!$isAllowCheck) {
                        throw new \Exception('非法身份请求');
                    }
                }

                $queryUuidList = [$chainUuid];
                //运营商身份还是分销专员身份，管理员无视
                $multiDistRole = $this->currentRole;

            }

            //运营商身份和管理员可以搜索具体的某个分销专员推广的订单
            if  ($this->isSuper || MultiDistBase::TOP_ROLE == $multiDistRole) {

                if ($chainUuid) {
                    $queryUuidList = [$chainUuid];
                }

                if ($searchKeyWord) {
                    //账号名称搜索
                    $topUid = $this->isSuper ? 0 : $this->memberInfo['memberID'];
                    $searchResult  = (new MultiDistMemberModel())->getUserInfo($searchKeyWord, $topUid, $searchType);
                    $populUuidList = array_column($searchResult, 'uuid');
                    if (empty($populUuidList)) {
                        $this->apiReturn(200, ['total' => 0, 'list' => [], 'statis' => []], '为空');
                    }
                }
            }

            if ($timeStart && $timeEnd) {
                $timeStart = $timeStart . ' 00:00:00';
                $timeEnd   = $timeEnd . ' 23:59:59';
            }

            $orderList = (new StatisBiz())->orderList($this->isSuper, $multiDistRole, $queryUuidList, $populUuidList, $timeStart, $timeEnd, $timeType, $settleStatus, $ordernum, $page, $pageSize);

        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $code = 500;
        }

        $this->apiReturn($code, $orderList, $msg);
    }


    /**
     * 收益月汇总
     * <AUTHOR>
     * @date    2020/4/14
     *
     * @return
     */
    public function profit()
    {
        $code = 200;
        $msg  = '';
        $monthStatis = [];

        try {
            $year = I('year', 0, 'intval');

            if (!$this->uuid) {
                throw new \Exception('身份未知,重新登录尝试');
            }

            if (!$year) {
                throw new \Exception('时间参数缺失！');
            }

            $timeStart = $year . '0101';
            $timeEnd   = $year . '1231';

            //收益明细
            $monthStatis = (new \Business\MultiDist\Statis())->monthStatis($this->uuid, $timeStart, $timeEnd);

        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $code = 500;
        }

        $this->apiReturn($code, $monthStatis, $msg);
    }


    /**
     * 收益详情
     * <AUTHOR>
     * @date    2020/4/14
     *
     * @return
     */
    public function profitDetail()
    {
        $code = 200;
        $msg  = '';
        $profitDetail = [];

        try {

            $ordernum       = trim(I('ordernum', '', 'strval'));
            $timeStart      = I('start_date', '', 'strval');
            $timeEnd        = I('end_date', '', 'strval');
            $chainUuid      = I('chain_uuid', '', 'strval');
            $transType      = I('trans_type', 0, 'intval');
            $toChainUuid    = I('to_chain_uuid', '', 'strval');
            $page           = I('page', 1, 'intval');
            $pageSize       = I('page_size', 20, 'intval');

            if (!$this->currentRole) {
                throw new \Exception('非法身份！');
            }

            if (!$chainUuid) {
                throw new \Exception('chain_uuid参数错误！');
            }

            if (!$timeStart || !$timeEnd) {
                throw new \Exception('时间参数缺失！');
            }

            if (!strtotime($timeStart) || !strtotime($timeEnd)) {
                throw new \Exception('时间参数格式错误');
            }

            if ($chainUuid != $this->uuid) {
                throw new \Exception('越权请求');
            }

            if ($transType && !in_array($transType, [MultiDistBaseModel::FLOWING_INCOME, MultiDistBaseModel::FLOWING_EXPEND])) {
                throw new \Exception('错误的交易类型参数');
            }

            $timeEnd = date("Y-m-d",strtotime("+1 day", strtotime($timeEnd)));

            //收益明细
            $profitDetail = (new \Business\MultiDist\Statis())->profitDetail($this->currentRole, $chainUuid, $timeStart, $timeEnd, $ordernum, $transType,
                $toChainUuid, MultiDistBaseModel::IS_SETTLE, '', $page, $pageSize);

        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $code = 500;
        }

        $this->apiReturn($code, $profitDetail, $msg);
    }

}