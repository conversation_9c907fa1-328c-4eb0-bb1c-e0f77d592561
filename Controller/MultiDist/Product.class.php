<?php
/**
 * 分销专员产品相关控制器
 * <AUTHOR>
 * @date 2020/4/3
 */

namespace Controller\MultiDist;

use Business\MultiDist\Invite as InviteBus;
use \Business\MultiDist\Product as DistProductBus;
use \Business\MultiDist\Member as DistMemberBus;
use Model\MassData\ExcelTask;
use \Model\MultiDist\Product as DistProductModel;
use \Model\MultiDist\Member as DistMemberModel;

class Product extends MdBase
{
    protected $noCheckLoginAction = ['mobilePoster'];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 查询产品列表
     * <AUTHOR>
     * @date 2020/4/3
     *
     * @return array
     */
    public function list()
    {
        $source         = I('post.source', '', 'intval');//产品来源 1=自供应 2=转分销 3=资源中心 -1 全部
        $isPromote      = I('post.is_promote', '', 'strval');//产品是否推广 0=不推广 1=加入推广
        $isProfite      = I('post.is_profite', '', 'strval');//是否独立佣金 0=默认佣金 1=独立佣金
        $pType          = I('post.p_type', '', 'strval');//产品类型
        $pid            = I('post.pid', '', 'strval');//搜索下拉框选择的产品ID
        $lid            = I('post.lid', '', 'strval');//搜索下拉框选择的产品ID
        $tid            = I('post.tid', '', 'strval');//搜索下拉框选择的票ID
        $sortField      = I('post.sort_field', '', 'strval');//排序字段 sales_total:累计销量
        $sortType       = I('post.sort_type', 0, 'intval');//排序类型1：正序 2：倒序
        $page           = I('post.page', 1, 'intval');//当前页
        $pageSize       = I('post.page_size', 15, 'intval');//每页条数
        $uuid           = I('post.uuid', '', 'strval');//管理员查询运营商产品列表唯一标识
        $onlyZeroProfit = I('post.onlyZeroProfit', 0, 'intval');//管理员查询运营商产品列表唯一标识
        $isSuper        = $this->isSuper();        //判断登陆用户是不是管理员

        if (!in_array($source, [1, 2, 3, -1])) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $uuidQuery = $this->uuid;
        if ($isSuper) {
            if (!$uuid) {
                $this->apiReturn(203, [], '参数缺失');
            }
            $uuidQuery = $uuid;
        }

        $productModel = new DistProductBus('', $uuidQuery);

        $paramArr = [
            'source'     => $source,
            'is_promote' => $isPromote === '' ? -1 : $isPromote,
            'is_profite' => $isProfite === '' ? -1 : $isProfite,
            'p_type'     => $pType,
            'lid'        => $lid,
            'tid'        => $tid,
            'pid'        => $pid,
        ];

        $result = $productModel->getList($paramArr, $sortField, $sortType, $page, $pageSize, $onlyZeroProfit);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 可推广的产品
     * <AUTHOR>
     * @date 2020/4/14
     *
     * @return array
     */
    public function myList()
    {
        $pType    = I('post.p_type', '', 'strval');//产品类型
        $pid       = I('post.pid', '', 'strval');//搜索下拉框选择的产品ID
        $lid       = I('post.lid', '', 'strval');//搜索下拉框选择的产品ID
        $tid       = I('post.tid', '', 'strval');//搜索下拉框选择的票ID
        $uuid     = I('post.uuid', '', 'strval');//分销专员uuid
        $page     = I('post.page', 1, 'intval');//当前页
        $pageSize = I('post.page_size', 15, 'intval');//每页条数
        $isSuper  = $this->isSuper();        //判断登陆用户是不是管理员

        if (!$uuid) {
            $uuid = $this->uuid;
        }

        $productModel = new DistProductBus('', $this->uuid);

        $paramArr = [
            'p_type' => $pType,
            'pid'    => $pid,
            'lid'    => $lid,
            'tid'    => $tid,
        ];

        $result = $productModel->getMyList($page, $pageSize, $uuid, $paramArr, $isSuper);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 可推广的产品导出
     * <AUTHOR>
     * @date 2020/4/18
     *
     * @return array
     */
    public function myListJudge()
    {
        $pType     = I('post.p_type', '', 'strval');//产品类型
        $pid       = I('post.pid', '', 'strval');//搜索下拉框选择的产品ID
        $judgeType = 36;//报表类型

        $excelType = load_config('excelType', 'excel');
        if (empty($excelType[$judgeType])) {
            $this->apiReturn(203, [], '未知报表类型');
        }

        $distInviteBus = new InviteBus();
        $judgeRes = $distInviteBus->judgeUserModuleDist($this->uuid);
        if (!$judgeRes) {
            $this->apiReturn(400, [], '请联系运营商续费才可以使用');
        }

        $productModel = new DistProductBus('', $this->uuid);

        $paramArr = [
            'p_type' => $pType,
            'pid'    => $pid,
        ];
        $result   = $productModel->getMyList(1, 15, $this->uuid, $paramArr, false);
        if (empty($result['data']['list'])) {
            $this->apiReturn(400, [], '无数据');
        }

        //需要用到的登录信息
        $loginInfo = $this->getLoginInfo();
        $memberId  = $loginInfo['sid']; //id

        $distMemberModel = new \Model\MultiDist\Member();
        $topMember       = $distMemberModel->getChainByUuid($this->uuid);
        $topAccount      = $distMemberModel->getMemberByUid($topMember['top_uid']);
        $request         = [
            'p_type'    => $pType,
            'pid'       => $pid,
            'uuid'      => $this->uuid,
            'member_id' => $this->memberInfo['sid'],
            'account'   => $topAccount['account'] ?? '',
        ];

        try {
            $excelTask = new ExcelTask();
            // 产品列表 hash 目前按照时间戳进行hash 保证实时性
            $time = time();
            $hash = md5($memberId . json_encode($request) . $time);
            //如果有重复的哈希值 不重新执行任务
            $res = $excelTask->getTaskByHash($hash);

            if (!empty($res)) {
                throw new \Exception('你已经生成过该报表, 根据唯一标识符进入报表中心查看[' . $res['id'] . ']');
            }

            $excelTask->setTaskOperatorId($loginInfo['memberID']);
            $createTaskRes = $excelTask->createTask($memberId, $request, $hash, $judgeType);

            $this->apiReturn(200, ['id' => $createTaskRes], '成功');

        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $this->apiReturn(400, [], $msg);
        }
    }

    /**
     * 分销专员产品设置
     * <AUTHOR>
     * @date 2020/4/3
     *
     * @return array
     */
    public function set()
    {
        $isSupply     = I('post.is_supply', '', 'intval');//自供应产品是否默认加入推广 0：否 1：是
        $isDis        = I('post.is_distribution', '', 'intval');//转分销产品是否默认加入推广 0：否 1：是
        $isRes        = I('post.is_resources', '', 'intval');//资源中心产品是否默认加入推广 0：否 1：是

        if (!in_array($isSupply, [0, 1])) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (!in_array($isDis, [0, 1])) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (!in_array($isRes, [0, 1])) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $productModel = new DistProductModel();
        $configRes    = $productModel->getDefaultConfig($this->uuid);
        if (!$configRes) {
            $this->apiReturn(204, [], '参数错误!');
        }

        $profitConfig = json_decode($configRes['profit_config'], true);
        //var_dump($this->uuid, $isSupply, $isDis, $isRes, $profitConfig);exit;
        $modRes = $productModel->modDefaultConfig($this->uuid, $isSupply, $isDis, $isRes, $profitConfig);

        if (!$modRes) {
            $this->apiReturn(204, [], '设置失败!');
        }

        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 分销专员设置全局佣金
     * <AUTHOR>
     * @date 2020/4/3
     *
     * @return array
     */
    public function setDefaultProfit()
    {
        $one   = I('post.one', 0, 'intval');//一级佣金
        $two   = I('post.two', 0, 'intval');//二级佣金
        $three = I('post.three', 0, 'intval');//三级佣金

        //发展级数
        $growLevel = I('post.grow_level', 0, 'intval');//三级佣金

        $top = 100 - ($one + $two + $three);
        if ($top < 0) {
            $this->apiReturn(203, [], '参数错误');
        }

        $multiDistConfig = new \Business\MultiDist\Config($this->memberInfo['sid'], $this->uuid);
        $modRes = $multiDistConfig->setGroupLevelAndProfitRate($growLevel, $top, $one, $two, $three);

        if (!$modRes['res']) {
            $this->apiReturn(204, [], $modRes['msg']);
        }

        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 获取全局配置信息
     * <AUTHOR>
     * @date 2020/4/3
     *
     * @return array
     */
    public function defaultConfig()
    {
        $multiDistConfig = new \Business\MultiDist\Config($this->memberInfo['sid'], $this->uuid);
        $configRes = $multiDistConfig->getTopChainDefaultConfig();
        if (!$configRes) {
            $this->apiReturn(204, [], '没有全局配置信息!');
        }
        $profit = json_decode($configRes['profit_config'], true);
        array_shift($profit);
        $profitNew                  = [];
        $profitNew['one']           = $profit[0];
        $profitNew['two']           = $profit[1];
        $profitNew['three']         = $profit[2];
        $configRes['profit_config'] = $profitNew;

        $this->apiReturn(200, $configRes, '成功');
    }

    /**
     * 产品名称模糊搜索
     * <AUTHOR>
     * @date 2020/4/7
     *
     * @return array
     */
    public function nameLike()
    {
        $productName = I('post.keyword', '', 'strval');//产品名称
        $lid         = I('post.lid', 0, 'intval');//产品id
        $page        = I('post.page', 1, 'intval');//当前页
        $pageSize    = I('post.page_size', 100, 'intval');//每页条数
        $uuid        = I('post.uuid', '', 'strval');//分销专员uuid
        $source      = I('post.source', 0, 'intval');//来源 1=自供应 2=转分销 3=资源中心
        $isSuper     = $this->isSuper();        //判断登陆用户是不是管理员
        $isPromote   = -1;
        $tidNot      = [];
        $memberModel  = new DistMemberModel();
        $memberBus    = new DistMemberBus();
        $productModel = new DistProductModel();

        if ($isSuper) {
            //管理员查询
            if (empty($uuid)) {
                $this->apiReturn(204, [], '参数错误!');
            }
            $tmpUuid = $uuid;
        } else {
            //非管理员查询
            $tmpUuid = $this->uuid;
        }

        //获取运营商唯一标识
        $memberRes    = $memberModel->getChainByUuid($tmpUuid);
        if (!$memberRes) {
            $this->apiReturn(204, [], '数据错误!');
        }
        $memberResTop = $memberModel->getChainByUid($memberRes['top_uid'], $memberModel::TOP_LEVEL);
        $queryUuid    = $memberResTop[0]['uuid'];
        if ($memberRes['level'] != $memberModel::TOP_LEVEL) {
            $isPromote = 1;
            //禁止推广的产品 获取所有上级uuid
            $distChainList = $memberBus->getHigherChainListPublic($tmpUuid, $memberRes);
            $uuidList      = array_column($distChainList, 'uuid');
            $uuidList[]    = $tmpUuid;
            $forbidRes     = $productModel->getForbidByUuidAndForbidUuid($queryUuid, $uuidList);
            if ($forbidRes) {
                $tidNot = array_column($forbidRes, 'tid');
            }
        }

        //参数兼容
        $isResource = 1;
        if ($source == 3) {
            $source      = 2;
            $isResource  = 2;
        }
        if ($source == 0) {
            $isResource = 0;
        }
        //全部
        if ($source == -1) {
            $isResource = 0;
            $source     = 0;
        }

        $productRes = $productModel->getProductByUuidAndName($queryUuid, $source, $isResource, $productName, $isPromote, $tidNot, $page, $pageSize,false, $lid);

        if (!$productRes) {
            $this->apiReturn(200, ['list' => [], 'total' => 0], '没有数据!');
        }

        $total = $productModel->getProductByUuidAndName($queryUuid, $source, $isResource, $productName, $isPromote, $tidNot, $page, $pageSize, true, $lid);

        $this->apiReturn(200, ['list' => $productRes, 'total' => $total], '成功');
    }

    /**
     * 设置产品推广
     * <AUTHOR>
     * @date 2020/4/7
     *
     * @return array
     */
    public function setPromote()
    {
        $id   = I('post.id', '', 'intval');//自增ID
        $type = I('post.type', '', 'intval');//类型 0=不推广 1=加入推广

        $multiDistInviteBus = new InviteBus();
        $judgeRes = $multiDistInviteBus->judgeUserModuleDist($this->uuid);
        if (!$judgeRes) {
            $this->apiReturn(400, [], '分销专员应用续费才可以使用');
        }

        if (!$id) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (!in_array($type, [0, 1])) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $productModel = new DistProductModel();
        $modRes       = $productModel->setIsPromote($id, $this->uuid, $type);

        $log = [
            'id'   => $id,
            'type' => $type,
            'uuid' => $this->uuid,
            'opid' => $this->memberInfo['memberID'],
            'sid'  => $this->memberInfo['sid'],
        ];
        if (!$modRes) {
            pft_log('multi_dist/setPromote/error', '设置失败:' . json_encode($log, JSON_UNESCAPED_UNICODE));
            $this->apiReturn(204, [], '设置失败!');
        }

        \Business\MultiDist\Product::pushStatisProduct($this->uuid);

        pft_log('multi_dist/setPromote/debug', '设置成功:' . json_encode($log, JSON_UNESCAPED_UNICODE));
        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 设置产品佣金
     * <AUTHOR>
     * @date 2020/4/7
     *
     * @return array
     */
    public function setProfitRate()
    {
        $ids   = I('post.ids', '', 'strval');//多个票ID 逗号隔开 1,2,3
        $type  = I('post.type', '', 'intval');//类型 0=默认佣金 1=独立佣金
        $one   = I('post.one', '', 'strval');//一级
        $two   = I('post.two', '', 'strval');//二级
        $three = I('post.three', '', 'strval');//三级

        if (!$ids) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (!in_array($type, [0, 1])) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //if ($type == 1 && (!$one || !$two || !$three)) {
        //    $this->apiReturn(203, [], '参数缺失');
        //}

        $ids = explode(',', $ids);
        if (count($ids) > 50) {
            $this->apiReturn(203, [], '参数有误');
        }

        if ($type == 1) {
            $top = 100 - (intval($one) + intval($two) + intval($three));
            if ($top < 0) {
                $this->apiReturn(203, [], '参数错误');
            }
            $profit = json_encode([strval($top), strval($one), strval($two), strval($three)]);
        } else {
            $profit = json_encode([]);
        }

        $productModel = new DistProductModel();
        $modRes       = $productModel->setProfitBatch($ids, $this->uuid, $type, $profit);

        if (!$modRes) {
            $this->apiReturn(204, [], '设置失败!');
        }

        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 设置黑名单
     * <AUTHOR>
     * @date 2020/4/7
     *
     * @return array
     */
    public function setForbid()
    {
        $ids  = I('post.ids', '', 'strval');//多个票ID 逗号隔开 1,2,3
        $type = I('post.type', '', 'intval');//类型 1：加入 2：移除
        $uuid = I('post.uuid', '', 'strval');//加入/移除黑名单的分销专员的uuid

        if (!$ids) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if ($type != 1 && $type != 2) {
            $this->apiReturn(203, [], '参数错误');
        }

        $ids = explode(',', $ids);
        if (count($ids) > 50) {
            $this->apiReturn(203, [], '参数有误');
        }

        if ($uuid == $this->uuid) {
            $this->apiReturn(203, [], '不能添加自己到黑名单');
        }

        $time = time();

        //加入/移除黑名单的分销专员的uuid校验
        $memberModel = new DistMemberModel();
        $chainRes    = $memberModel->getChainByUuid($uuid);
        if (!$chainRes) {
            $this->apiReturn(203, [], '参数有误');
        }

        $productModel = new DistProductModel();
        $productRes   = $productModel->getConfigByTidsAndUuid($ids, $this->uuid);
        if (!$productRes || count($productRes) != count($ids)) {
            $this->apiReturn(204, [], '参数有误!');
        }

        try {
            $productModel->startTrans();

            if ($type == 1) {
                $forbidRes = $productModel->getForbidByTids($ids, $this->uuid, $uuid, 1);
                if ($forbidRes) {
                    $this->apiReturn(204, [], '已存在黑名单!');
                }
                //加入
                foreach ($productRes as $value) {
                    $data[] = [
                        'tid'               => $value['tid'],
                        'pid'               => $value['pid'],
                        'chain_uuid'        => $this->uuid,
                        'forbid_chain_uuid' => $uuid,
                        'operator'          => $this->memberInfo['memberID'],
                        'create_time'       => $time,
                        'update_time'       => $time,
                    ];
                }
                $res = $productModel->addForbid($data);

            } else {
                //移除
                $res = $productModel->deleteForbid($ids, $this->uuid, $uuid);
            }

            if (!$res) {
                throw new \Exception('失败');

            }

            $productModel->commit();
        } catch (\Exception $e) {
            $productModel->rollback();
            $this->apiReturn(204, [], $e->getMessage());
        }

        \Business\MultiDist\Product::pushStatisProduct($this->uuid);

        $this->apiReturn(200, [], '成功');
    }

    /**
     * 获取黑名单
     * <AUTHOR>
     * @date 2020/4/7
     *
     * @return array
     */
    public function forbidList()
    {
        $tid      = I('post.tid', '', 'intval');//票id
        $page     = I('post.page', 1, 'intval');//当前页
        $pageSize = I('post.page_size', 15, 'intval');//每页条数
        $uuid     = I('post.uuid', '', 'strval');//加入/移除黑名单的分销专员的uuid
        $isSuper  = $this->isSuper();        //判断登陆用户是不是管理员

        if (!$tid) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if ($isSuper) {
            $queryUuid = $uuid;
        } else {
            $queryUuid = $this->uuid;
        }

        $productBus = new DistProductBus();

        //获取黑名单
        $productModel = new DistProductModel();
        $res          = $productModel->getForbidList($tid, $queryUuid, $page, $pageSize);
        if (!$res) {
            $this->apiReturn(200, ['list' => [], 'total' => 0], '为空');
        }

        //获取用户ID
        $uuids       = array_column($res, 'forbid_chain_uuid');
        $memberModel = new DistMemberModel();
        $chainList   = $memberModel->getChainByUuid($uuids, '', 0);
        if (!$chainList) {
            $this->apiReturn(200, ['list' => [], 'total' => 0], '为空');
        }

        $chainListMap = $productBus->changeMap($chainList, 'uuid');

        //获取用户信息
        $uids       = array_column($chainList, 'uid');
        $memberList = $memberModel->getMemberByUids($uids);
        if (!$memberList) {
            $this->apiReturn(200, ['list' => [], 'total' => 0], '为空');
        }

        $memberMap = $productBus->changeMap($memberList, 'uid');

        foreach ($res as &$vaRes) {
            $forbid           = $vaRes['forbid_chain_uuid'];
            $vaRes['dname']   = $memberMap[$chainListMap[$forbid]['uid']]['dname'] ?? '';
            $vaRes['account'] = $memberMap[$chainListMap[$forbid]['uid']]['account'] ?? '';
            $vaRes['cname']   = $memberMap[$chainListMap[$forbid]['uid']]['cname'] ?? '';
            $vaRes['mobile']  = $memberMap[$chainListMap[$forbid]['uid']]['mobile'] ?? '';
            $vaRes['level']   = $chainListMap[$forbid]['level'] ?? '';
            $vaRes['uuid']    = $forbid;
        }

        $total = $productModel->getForbidList($tid, $queryUuid, $page, $pageSize, true);
        $this->apiReturn(200, ['list' => $res, 'total' => $total], '成功');
    }

    /**
     * 获取产品海报
     * <AUTHOR>
     * @date 2020/4/8
     *
     * @return array
     */
    public function poster()
    {
        $lid = I('get.lid', '', 'intval');//景区id
        $aid = I('get.sid', '', 'intval');//顶级供应商ID
        $sid = I('get.aid', '', 'intval');//上级供应商ID

        if (!$lid || !$sid || !$aid) {
            $this->apiReturn(203, [], '参数缺失!');
        }

        $distMemberModel = new \Model\MultiDist\Member();
        $topMember       = $distMemberModel->getChainByUuid($this->uuid);
        $topAccount      = $distMemberModel->getMemberByUid($topMember['top_uid']);

        $productBus             = new DistProductBus();
        $res                    = $productBus->getProductPoster($this->memberInfo['sid'], $lid, $sid, $aid);
        $res['data']['account'] = $topAccount['account'] ?? '';
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 手机查看产品海报
     * <AUTHOR>
     * @date 2020/4/8
     *
     * @return array
     */
    public function mobilePoster()
    {
        $lid  = I('post.lid', '', 'intval');//景区id
        $sid  = I('post.aid', '', 'intval');//上级供应商ID
        $aid  = I('post.sid', '', 'intval');//顶级供应商ID
        $mid  = I('post.mid', '', 'intval');//用户id
        $uuid = I('post.uuid', '', 'string');//唯一标识

        if (!$lid || !$sid || !$aid || !$mid || !$uuid) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $distMemberModel = new \Model\MultiDist\Member();
        $topMember       = $distMemberModel->getChainByUuid($uuid);
        $topAccount      = $distMemberModel->getMemberByUid($topMember['top_uid']);

        $productBus             = new DistProductBus();
        $res                    = $productBus->getProductPoster($mid, $lid, $sid, $aid);
        $res['data']['account'] = $topAccount['account'] ?? '';
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 批量设置产品推广
     * <AUTHOR>
     * @date 2020/8/10
     *
     * @return array
     */
    public function batchSetPromote()
    {
        $ids  = I('post.ids', '', 'strval');//自增ID
        $type = I('post.type', '', 'intval');//类型 0=不推广 1=加入推广

        $multiDistInviteBus = new InviteBus();
        $judgeRes           = $multiDistInviteBus->judgeUserModuleDist($this->uuid);
        if (!$judgeRes) {
            $this->apiReturn(400, [], '分销专员应用续费才可以使用');
        }

        if (!$ids) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (!in_array($type, [0, 1])) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $ids = explode(',', $ids);
        if (empty($ids)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $productModel = new DistProductModel();
        $modRes       = $productModel->batchSetIsPromote($ids, $this->uuid, $type);

        $log = [
            'id'   => $ids,
            'type' => $type,
            'uuid' => $this->uuid,
            'opid' => $this->memberInfo['memberID'],
            'sid'  => $this->memberInfo['sid'],
        ];
        if (!$modRes) {
            pft_log('multi_dist/setPromote/error', '设置失败:' . json_encode($log, JSON_UNESCAPED_UNICODE));
            $this->apiReturn(204, [], '设置失败!');
        }

        \Business\MultiDist\Product::pushStatisProduct($this->uuid);

        pft_log('multi_dist/setPromote/debug', '设置成功:' . json_encode($log, JSON_UNESCAPED_UNICODE));
        $this->apiReturn(200, [], '设置成功');
    }

}

