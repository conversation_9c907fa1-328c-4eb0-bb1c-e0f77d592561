<?php
/**
 * 分销专员邀请相关接口
 * <AUTHOR>
 * @date 2020/3/27 0027
 */

namespace Controller\MultiDist;

use Business\JavaApi\Member\MemberQuery;
use Business\Member\Member;
use Business\MultiDist\Invite as InviteBus;
use Library\Constants\MemberConst;
use Library\Tools\Helpers;
use Model\MultiDist\Member as MultiMemberModel;
use Process\Resource\Qiniu;
use Model\MultiDist\Invite as InviteModel;

class Invite extends MdBase
{

    //protected $noCheckLoginAction = ['sendMobileCode', 'register', 'pageInfo','mobileInvitePoster'];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 编辑团队名称
     * <AUTHOR>
     * @date 2020/3/28 0028
     *
     * @return array
     */
    public function setTeamName()
    {
        $teamName = I('post.team_name', '', 'strval');//团队名称

        if (empty($teamName)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $teamName = trim($teamName);

        // 验证参数长度
        if (mb_strlen($teamName, 'utf8') > 32) {
            $this->apiReturn(203, [], '名称参数格式不符合要求');
        }

        $multiDistInviteBus = new InviteBus();
        $modRes             = $multiDistInviteBus->setTeamName($this->uuid, $teamName);

        if (!$modRes || $modRes['code'] != $multiDistInviteBus::CODE_SUCCESS) {
            $this->apiReturn($modRes['code'], $modRes['data'], $modRes['msg']);
        }

        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 设置加入审核
     * <AUTHOR>
     * @date 2020/3/28 0028
     *
     * @return array
     */
    public function setCheck()
    {
        $multiDistInviteBus = new InviteBus();
        $judgeRes = $multiDistInviteBus->judgeUserModuleDist($this->uuid);
        if (!$judgeRes) {
            $this->apiReturn(400, [], '请联系运营商续费才可以使用');
        }

        $isCheck = I('post.is_check', '', 'intval');//加入是否需要审核 1=需要 0=不需要

        if (!in_array($isCheck, [0, 1])) {
            $this->apiReturn(203, [], '参数缺失');
        }


        $modRes             = $multiDistInviteBus->setCheck($this->uuid, $isCheck);

        if (!$modRes || $modRes['code'] != $multiDistInviteBus::CODE_SUCCESS) {
            $this->apiReturn($modRes['code'], $modRes['data'], $modRes['msg']);
        }

        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 获取邀请海报相关信息
     * <AUTHOR>
     * @date 2020/3/27 0027
     *
     * @return array
     */
    public function posterInfo()
    {
        $multiDistInviteBus = new InviteBus();
        $judgeRes = $multiDistInviteBus->judgeUserModuleDist($this->uuid);
        if (!$judgeRes) {
            $this->apiReturn(400, [], '请联系运营商续费才可以使用');
        }

        //能否加入团队
        $inviteCheckRes = (new \Business\MultiDist\Member())->isAllowGrowLowerlevel($this->uuid);
        if (!$inviteCheckRes) {
            $this->apiReturn(403, [], '当前暂不能加入该团队');
        }

        $inviteRes          = $multiDistInviteBus->getPosterInfo($this->uuid);

        if (!$inviteRes || $inviteRes['code'] != $multiDistInviteBus::CODE_SUCCESS) {
            $this->apiReturn($inviteRes['code'], $inviteRes['data'], $inviteRes['msg']);
        }

        $this->apiReturn(200, $inviteRes['data'], '成功');
    }

    /**
     * 上传邀请海报图片
     * <AUTHOR>
     * @date 2020/3/28 0028
     *
     * @return array
     */
    public function uploadPosterImage()
    {
        $multiDistInviteBus = new InviteBus();
        $judgeRes = $multiDistInviteBus->judgeUserModuleDist($this->uuid);
        if (!$judgeRes) {
            $this->apiReturn(400, [], '请联系运营商续费才可以使用');
        }

        if (!isset($_FILES['poster_image'])) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (empty($_FILES['poster_image']['tmp_name'])) {
            $this->apiReturn(203, [], '上传失败!');
        }

        ksort($_FILES);
        $qiniuProcess       = new Qiniu();
        $sizeMax            = $multiDistInviteBus::MULTI_DIST_IMAGE_SIZE;
        //$imageExt           = $multiDistInviteBus::MULTI_DIST_IMAGE_EXT;

        //海报图图片格式效验
        $posterImage     = $_FILES['poster_image'];

        $posterImageType = explode('/', $posterImage['type']);
        if ($posterImageType[0] != 'image') {
            $this->apiReturn(203, [], '海报图上传格式有误');
        }
        if ($posterImage['size'] > $sizeMax) {
            $this->apiReturn(203, [], '海报图大小限制2M');
        }

        //更新海报版本号
        $inviteModel  = new InviteModel();
        $modInviteRes = $inviteModel->modPosterVerByUuid($this->uuid);
        if (!$modInviteRes) {
            $this->apiReturn(204, [], '海报版本号更新失败');
        }

        //获取后缀
        $arr = explode('.', $posterImage['name']);
        $ext = array_pop($arr);
        $posterImgesRes = $multiDistInviteBus->getPosterDir($this->uuid, $ext);
        $filename       = [
            //$posterImgesRes['data']['posterBgDir'],
            $posterImgesRes['data']['posterDir'],
        ];
        $result         = $qiniuProcess->uploadFile($filename);

        if (!$result) {
            $this->apiReturn(204, [], $qiniuProcess->getError());
        }

        $posterImges = $multiDistInviteBus->getPosterImage($this->uuid);
        $res         = [
            //'bg_image'     => $posterImges['data']['posterBg'],
            'poster_image' => $posterImges['data']['poster'],
        ];

        $this->apiReturn(200, $res, '上传成功');

    }

    /**
     * 保存邀请海报信息
     * <AUTHOR>
     * @date 2020/3/28 0028
     *
     * @return array
     */
    public function setPosterData()
    {
        $xPosition              = I('post.x', -1, 'intval');//二维码位置相关参数
        $yPosition              = I('post.y', -1, 'intval');//二维码位置相关参数
        $wPosition              = I('post.w', -1, 'intval');//二维码位置相关参数
        $hPosition              = I('post.h', -1, 'intval');//二维码位置相关参数
        $textX                  = I('post.text_x', 0, 'intval');//文字位置相关参数
        $textY                  = I('post.text_y', 0, 'intval');//文字位置相关参数
        $fontSize               = I('post.font_size', 0, 'intval');//文字大小
        $textWidth              = I('post.text_width', 0, 'intval');//文字位置相关参数
        $textHeight             = I('post.text_height', 0, 'intval');//文字位置相关参数
        $fontColor              = I('post.font_color', '', 'strval');//文字颜色
        $text                   = I('post.text', '', 'strval');//文字内容
        $bgImage                = I('post.bg_image', '', 'strval');//背景图片
        $originDevicepixelratio = I('post.origin_devicepixelratio', '1', 'strval');//用户电脑缩放布局比例  正常为1=100%

        if ($xPosition == -1 || $yPosition  == -1 || $wPosition  == -1 || $hPosition  == -1) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $multiDistInviteBus = new InviteBus();
        $judgeRes = $multiDistInviteBus->judgeUserModuleDist($this->uuid);
        if (!$judgeRes) {
            $this->apiReturn(400, [], '请联系运营商续费才可以使用');
        }


        $inviteModel = new InviteModel();
        $res         = $inviteModel->getMulitInviteConfigByUuid($this->uuid);
        if (!$res) {
            $this->apiReturn(204, [], '数据错误！');
        }

        $extraParam = [
            'x'                       => $xPosition,
            'y'                       => $yPosition,
            'w'                       => $wPosition,
            'h'                       => $hPosition,
            'text_x'                  => $textX,
            'text_y'                  => $textY,
            'font_size'               => $fontSize,
            'text_width'              => $textWidth,
            'text_height'             => $textHeight,
            'font_color'              => $fontColor,
            'text'                    => $text,
            'origin_devicepixelratio' => $originDevicepixelratio,
        ];
        $extraParamJson = json_encode($extraParam, JSON_UNESCAPED_UNICODE);

        if ($extraParamJson == $res['extra'] && $bgImage == $res['poster_bg']) {
            $this->apiReturn(200, [], '成功');
        }

        $modParam     = ['extra' => $extraParamJson, 'poster_bg' => $bgImage];
        $modInviteRes = $inviteModel->modInviteConfig($this->uuid, $modParam);

        if (!$modInviteRes) {
            $this->apiReturn(204, [], '修改失败！');
        }

        $this->apiReturn(200, [], '成功');
    }

    /**
     * 发送分销专员邀请注册手机验证码
     * <AUTHOR>
     * @date 2020/3/28 0028
     *
     * @return array
     */
    public function sendMobileCode()
    {
        $mobile = I('post.mobile', '', 'strval');//手机号
        $sign   = I('post.sign', '', 'strval');//签名 md5(手机号+密钥)
        //var_dump(md5($mobile.'wodknvjkemsxos'));exit;

        if (empty($mobile) || empty($sign)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $multiDistInviteBus = new InviteBus();

        //签名效验
        if (md5($mobile . $multiDistInviteBus::MULTI_DIST_MOBILE_KEY) !== $sign) {
            $this->apiReturn(203, [], '非法请求！');
        }

        //手机号码验证
        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(406, [], '请输入正确的手机号码');
        }

        //判断手机号是否被列入黑名单
        $blackList   = load_config('black_list');
        $blackMobile = $blackList['mobile'];
        if (in_array($mobile, $blackMobile)) {
            $this->apiReturn(403, [], '该手机号已经被加入黑名单。');
        }

        $inviteRes = $multiDistInviteBus->sendMobileCode($mobile);

        if ($inviteRes['code'] != 200) {
            $msg = '对不起，短信服务器发生故障，造成的不便我们感到十分抱歉。请联系我们客服人员。';
            $this->apiReturn(500, [], $inviteRes['msg']);
        }

        $this->apiReturn(200, [], '发送成功');

    }

    /**
     * 分销专员邀请注册
     * <AUTHOR>
     * @date 2020/3/28 0028
     *
     * @return array
     */
    public function register()
    {
        $mobile = I('post.mobile', '', 'strval');//手机号
        $code   = I('post.code', '', 'strval');//短信验证码
        $remark = I('post.remark', '', 'strval');//备注 限制30字
        $uuid   = I('post.uuid', '', 'strval');//邀请人唯一标识

        if (empty($mobile) || empty($code) || empty($uuid)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $multiDistInviteBus = new InviteBus();

        //接口请求限制
        $cacheKey  = InviteBus::MULTI_DIST_REGISTER_LIMIT_PREFIX . $mobile;
        $limitRes  = $multiDistInviteBus->limitRedis($cacheKey, 10, 10);
        if (!$limitRes) {
            $this->apiReturn(203, [], '请求稍后重试!');
        }

        $judgeRes = $multiDistInviteBus->judgeUserModuleDist($uuid);
        if (!$judgeRes) {
            $this->apiReturn(400, [], '请联系运营商续费才可以使用');
        }

        //加入角色校验
        $memberModel = new \Model\MultiDist\Member();
        $chainRes    = $memberModel->getChainByUuid($uuid);
        if (!$chainRes) {
            $this->apiReturn(203, [], '参数有误。');
        }
        $checkRes = $multiDistInviteBus->checkMemberRole($mobile, $chainRes['top_uid']);
        if ($checkRes['code'] != 200) {
            $this->apiReturn($checkRes['code'], [], $checkRes['msg']);
        }

        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(203, [], '请输入正确的手机号码');
        }

        //校验注册验证码
        $codeRes = Helpers::ChkCode($mobile, $code);
        if ($codeRes !== true) {
            $this->apiReturn(203, '', '验证码错误');
        }

        $inviteRes = $multiDistInviteBus->register($mobile, $uuid, $remark, $checkRes);

        $this->apiReturn($inviteRes['code'], $inviteRes['data'], $inviteRes['msg']);
    }

    /**
     * 邀请页面信息
     * <AUTHOR>
     * @date 2020/4/17
     *
     * @return array
     */
    public function pageInfo()
    {
        $uuid = I('post.uuid', '', 'strval');//唯一标识
        if (!$uuid) {
            $this->apiReturn(203, [], '参数有误');
        }

        $inviteBus = new InviteBus();
        $cacheKey  = InviteBus::MULTI_DIST_PAGEINFO_LIMIT_PREFIX . $uuid;
        $limitRes  = $inviteBus->limitRedis($cacheKey, 10, 2);
        if (!$limitRes) {
            $this->apiReturn(203, [], '请求稍后重试!');
        }

        $multiDistMember = new \Business\MultiDist\Member();
        $chainInfo       = $multiDistMember->getUuidInfo($uuid);
        if (!$chainInfo) {
            $this->apiReturn(203, [], '该分销专员已被删除或者退出团队');
        }

        //能否加入团队
        $inviteCheckRes = (new \Business\MultiDist\Member())->isAllowGrowLowerlevel($uuid, $chainInfo);
        if (!$inviteCheckRes) {
            $this->apiReturn(403, [], '当前暂不能加入该团队');
        }

        //团队名称用运营商的
        $topChainInfo = $multiDistMember->getTopUuidInfoByUid($chainInfo['top_uid']);
        if (!$topChainInfo) {
            $this->apiReturn(203, [], '未查询到运营商!');
        }

        $inviteModel = new InviteModel();
        $inviteRes   = $inviteModel->getMulitInviteConfigByUuid($topChainInfo['uuid']);
        if (!$inviteRes) {
            $this->apiReturn(203, [], '参数有误');
        }

        //$memberInfo  = $multiDistMember->getMultiDistMemberInfo($chainInfo['uid']);
        //先直接请求中台下不然没头像 后面再加缓存方案。。
        $memberQuery = new \Business\Member\Member();
        $memberInfo  = $memberQuery->getInfo($chainInfo['uid']);

        $data = [
            'team_name' => $inviteRes['team_name'],
            'img'       => $memberInfo['headphoto'] ?? '',
            'dname'     => $memberInfo['dname'] ?? '',
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 查询审核列表
     * <AUTHOR>
     * @date 2020/4/2
     *
     * @return array
     */
    public function examineList()
    {
        $mobile   = I('post.mobile', '', 'strval');//手机号
        $dname    = I('post.dname', '', 'strval');//账号名称
        $account  = I('post.account', '', 'strval');//账号
        $state    = I('post.state', '', 'intval');//状态 -1：全部 0：待审核 1：已通过 2:已拒绝
        $page     = I('post.page', 1, 'intval');//当前页
        $pageSize = I('post.page_size', 15, 'intval');//每页条数

        $mobileArray = [];

        $memberQuery = new MemberQuery();
        if ($dname || $account || $mobile) {
            $res = $memberQuery->queryMemberInfoByAccountOrMobileOrName($account, $mobile, $dname);
            if ($res['code'] != 200 || empty($res['data'])) {
                $this->apiReturn(200, ['list' => [], 'total' => 0], '数据为空');
            }
            $mobileArray = array_column($res['data'], 'mobile');
        }

        $inviteModel = new InviteModel();

        $result = $inviteModel->getInviteByMobileAndState($this->uuid, $mobileArray, $state, '', $page, $pageSize);

        if (!$result) {
            $this->apiReturn(200, ['list' => [], 'total' => 0], '数据为空');
        }

        $total = $inviteModel->getInviteByMobileAndStateNum($this->uuid, $mobileArray, $state);

        $mobiles = array_column($result, 'mobile');

        $memberBus = new Member();
        $memberRes = $memberBus->getMemberInfoByMulti($mobiles, 'mobile');


        $queryRoles  = [
            MemberConst::ROLE_APPLY,
            MemberConst::ROLE_DISTRIBUTOR,
            MemberConst::ROLE_MULTIDIST,
        ];
        $memberResTemp = [];
        //过滤非供应商分销商分销专员
        foreach ($memberRes as &$item) {
            if (in_array($item['dtype'], $queryRoles)) {
                $memberResTemp[] = $item;
            }
        }

        $memberMap = array_key($memberResTemp, 'mobile');
        $memberIds = array_column($memberResTemp, 'id');
        $extRes    = $memberBus->getList($memberIds, true);
        foreach ($result as &$value) {
            $temMobile          = $value['mobile'];
            $value['dname']     = $memberMap[$temMobile]['dname'] ?? '';
            $value['account']   = $memberMap[$temMobile]['account'] ?? '';
            $value['cname']     = $memberMap[$temMobile]['cname'] ?? '';
            $value['dtype']     = $memberMap[$temMobile]['dtype'] ?? '';
            $value['corp_kind'] = $extRes[$memberMap[$temMobile]['id']]['corp_kind'] ?? 7;
        }

        //获取用户信息
        //$memeberModel = new MultiMemberModel();
        //$memberRes = $memeberModel->getMemberByMobiles($mobiles);
        //$memberMap = array_key($memberRes, 'mobile');

        $this->apiReturn(200, ['list' => $result, 'total' => intval($total)], '成功');
    }

    /**
     * 同意/拒绝审核
     * <AUTHOR>
     * @date 2020/4/2
     *
     * @return array
     */
    public function setExamine()
    {
        $id          = I('post.id', '', 'intval');//审核ID
        $checkResult = I('post.check_result', '', 'intval');//类型 1=同意 2=拒绝
        $remark      = I('post.remark', '', 'strval');//拒绝原因

        if (!$id) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if ($checkResult != 2 && $checkResult != 1) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if ($checkResult == 2 && empty($remark)) {
            $this->apiReturn(203, [], '参数错误');
        }

        // 验证参数长度
        if (mb_strlen($remark, 'utf8') > 30) {
            $this->apiReturn(203, [], '参数格式不符合要求');
        }

        $distInviteBus = new InviteBus();
        $judgeRes = $distInviteBus->judgeUserModuleDist($this->uuid);
        if (!$judgeRes) {
            $this->apiReturn(400, [], '请联系运营商续费才可以使用');
        }

        //能否加入团队
        $inviteCheckRes = (new \Business\MultiDist\Member())->isAllowGrowLowerlevel($this->uuid);
        if (!$inviteCheckRes) {
            $this->apiReturn(403, [], '当前暂不能加入该团队');
        }
        
        $remark = trim($remark);

        $modRes = $distInviteBus->setExamine($id, $checkResult, $this->uuid, $this->memberInfo['sid'], $this->memberInfo['memberID'], $remark);

        if (!$modRes || $modRes['code'] != $distInviteBus::CODE_SUCCESS) {
            $this->apiReturn($modRes['code'], $modRes['data'], $modRes['msg']);
        }

        $this->apiReturn(200, [], '审核成功');
    }

    /**
     * 手机查看邀请海报
     * <AUTHOR>
     * @date 2020/8/6
     *
     * @return array
     */
    public function mobileInvitePoster(){
        $uuid = I('post.uuid', '', 'string');//唯一标识
        if (!$uuid) {
            $this->apiReturn(203, [], '参数缺失');
        }
        $multiDistInviteBus = new InviteBus();
        $inviteRes = $multiDistInviteBus->getPosterInfo($uuid);
        $res['poster_image'] = $inviteRes['data']['poster_image'] ?? '';
        $this->apiReturn($inviteRes['code'], $res, $inviteRes['msg']);
    }

}
