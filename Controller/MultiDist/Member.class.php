<?php
/**
 * 分销专员用户
 * <AUTHOR>
 * @date 2020/3/27 0027
 */

namespace Controller\MultiDist;

use Business\AppCenter\Module;
use Model\AppCenter\ModuleList;
use Model\MultiDist\Member as MultiMemberModel;
use Business\MultiDist\Member as MultiMemberBiz;
use Business\MultiDist\Base as MultiDistBaseBiz;
use Model\MultiDist\Product as DistProductModel;
use Model\MultiDist\Invite as DistInviteModel;

class Member extends MdBase
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取团队列表
     * <AUTHOR>
     * @date 2020/4/28
     *
     * @return array
     */
    public function teamList()
    {
        $page      = I('get.page', 1, 'intval');//当前页
        $pageSize  = I('get.page_size', 15, 'intval');//每页条数
        $uid      = $this->memberInfo['sid'];
        $memberModel = new MultiMemberModel();
        $inviteModel = new DistInviteModel();
        $chainRes = $memberModel->getTeamList($uid, $page, $pageSize);
        if (!$chainRes) {
            $this->apiReturn(200, ['list' => [], 'total' => 0], '');
        }

        //团队名称获取
        $topUids    = array_column($chainRes, 'top_uid');
        $topRes = $memberModel->getChainByUid($topUids, $memberModel::TOP_LEVEL);
        if (!$topRes) {
            $this->apiReturn(200, ['list' => [], 'total' => 0], '');
        }
        $topUuids    = array_column($topRes, 'uuid');
        $inviteConfig = $inviteModel->getInviteConfigByUuids($topUuids);
        if (!$inviteConfig) {
            $this->apiReturn(200, ['list' => [], 'total' => 0], '');
        }
        $topMap = array_key($topRes, 'uid');
        $inviteConfigMap = array_key($inviteConfig, 'uuid');

        //上级信息获取
        $higherUids = array_column($chainRes, 'higher_uid');
        $memberRes = $memberModel->getMemberByUids($higherUids);
        $memberMap = array_key($memberRes, 'uid');

        //产品总数获取
        $productModel = new DistProductModel();

        $productCount = $productModel->getProductCountByUuids($topUuids);
        $productCountMap = array_key($productCount, 'uuid');

        foreach ($chainRes as &$value) {
            $value['team_name']     = $inviteConfigMap[$topMap[$value['top_uid']]['uuid']]['team_name'] ?? '';
            $value['mobile']        = $memberMap[$value['higher_uid']]['mobile'] ?? '';
            $value['account']       = $memberMap[$value['higher_uid']]['account'] ?? '';
            $value['dname']         = $memberMap[$value['higher_uid']]['dname'] ?? '';
            $value['cname']         = $memberMap[$value['higher_uid']]['cname'] ?? '';
            $value['product_num']   = (int)$productCountMap[$topMap[$value['top_uid']]['uuid']]['num'] ?? 0;
            $value['leave_time']    = intval($value['delete_time']);
            $value['state']         = intval($value['status']);

            //在团队中的身份 1=运营商 2=分销专员
            $value['role'] = 2;
            if ($value['top_uid'] == $uid) {
                $value['role'] = 1;
            }
        }

        $total = $memberModel->getTeamListTotal($uid);
        $this->apiReturn(200, ['list' => $chainRes, 'total' => $total], '');
    }

    /**
     * 获取加入的分销专员团队
     * <AUTHOR>
     * @date    2020/3/28 0028
     *
     * @return
     */
    public function roleList()
    {
        $memberModel = new MultiMemberModel();
        $inviteBus   = new \Business\MultiDist\Invite();
        $uid      = $this->memberInfo['sid'];
        $roleList = $memberModel->getAllHigherLevelList($uid);

        $topUids = array_column($roleList, 'top_uid');
        $topAccounts = $memberModel->getMemberByUids($topUids);
        $topAccountsMap = array_key($topAccounts, 'uid');

        //$domain = ENV == 'PRODUCTION' ? 'https://' : 'http://';

        foreach ($roleList as &$role) {
            //当前生效标识
            $role['active']     = ($role['uuid'] == $this->uuid) ? 1 : 0;

            $account = $topAccountsMap[$role['top_uid']]['account'] ?? '';
            $domain  = $inviteBus->getStoreUrlByMultiDist(ENV, $account, $this->uuid);
            $role['mall_link']  = $domain;

            $role['leave_time'] = $role['delete_time'];
        }

        $this->apiReturn(200, $roleList);
    }

    /**
     * 所属角色切换，session更新
     * <AUTHOR>
     * @date    2020/04/02
     *
     * @return
     */
    public function roleChange()
    {
        $uuid = I('post.uuid', '', 'string');
        $role = I('post.role', 0, 'int');

        $uid   = $this->memberInfo['sid'];
        $level = -1;

        if (empty($uuid) || empty($role)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (!in_array($role, [\Business\MultiDist\Base::TOP_ROLE, \Business\MultiDist\Base::NOMAL_ROLE])) {
            $this->apiReturn(203, [], '非法参数');
        }

        //判断uuid有效性
        $roleList = (new MultiMemberModel())->getAllHigherLevelList($uid);

        if (!$roleList) {
            $this->apiReturn(404, [], '无效的uuid');
        }

        foreach ($roleList as $tmp) {
            if ($tmp['uuid'] == $uuid) {
                $level      = $tmp['level'];
            }
        }

        if ($level == -1) {
            $this->apiReturn(404, [], '无效的uuid');
        }

        (new \Business\MultiDist\Member())->setMultiDistSession($uuid, $role, $level, $uid);
        $this->apiReturn(200, [], 'success');
    }


    /**
     * 分销专员列表
     * <AUTHOR>
     * @date 2020/4/15
     *
     * @return array
     */
    public function list()
    {
        $mobile    = I('post.mobile', '', 'strval');//手机号
        $dname     = I('post.dname', '', 'strval');//账号名称
        $account   = I('post.account', '', 'strval');//账号
        $level     = I('post.level', '', 'intval');//专员级别 1=一级 2=二级 3=三级
        $sortField = I('post.sort_field', '',
            'strval');//排序字段lower_num:直接下级 order_num:订单量 sale_amount:销售额 provide_profit:返佣
        $sortType  = I('post.sort_type', 0, 'intval');//排序类型1：正序 2：倒序
        $page      = I('post.page', 1, 'intval');//当前页
        $pageSize  = I('post.page_size', 15, 'intval');//每页条数
        $uuid      = I('post.uuid', '', 'strval');//管理员查询运营商产品列表唯一标识
        $topDname  = I('post.top_dname', '', 'strval');//管理员查询运营商名称筛选
        $isSuper = $this->isSuper();        //判断登陆用户是不是管理员

        $uuidQuery = $this->uuid;
        if ($isSuper) {
            if ($uuid) {
                $uuidQuery = $uuid;
                $isSuper  = false;
            }
        }

        $memberBus = new MultiMemberBiz('', $uuidQuery);

        $paramArr = [
            'mobile'  => $mobile,
            'dname'   => $dname,
            'account' => $account,
            'level'   => $level,
            'top_dname' => $topDname,
        ];

        $result = $memberBus->getList($page, $pageSize, $paramArr, $sortField, $sortType, $isSuper);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 分销专员详情
     * <AUTHOR>
     * @date 2020/4/16
     *
     * @return array
     */
    public function info()
    {
        $uuid    = I('post.uuid', '', 'strval');//分销专员uuid
        $isSuper = $this->isSuper();        //判断登陆用户是不是管理员
        $sid      = $this->memberInfo['sid'];
        $array = [];
        $object = (object)$array;

        if (!$uuid) {
            $this->apiReturn(203, $object, '参数缺失');
        }

        $memberModel = new MultiMemberModel();
        $inviteModel = new DistInviteModel();
        $memberQuery = new \Business\Member\Member();
        $statisModel = new \Model\MultiDist\Statis();

        $chainRes = $memberModel->getChainByUuid($uuid, 'uid, uuid, level_num as level, top_uid, higher_uid, create_time');
        if (!$chainRes) {
            $this->apiReturn(203, $object, '参数错误');
        }
        //数据权限校验
        if (!$isSuper && $sid != $chainRes['top_uid'] && $sid != $chainRes['higher_uid']) {
            if ($this->uuid != $chainRes['top_uid']) {
                $this->apiReturn(203, $object, '无权限');
            }
        }

        //用户基础信息
        $memberInfo = $memberQuery->getInfo($chainRes['uid']);
        if (!$memberInfo) {
            $this->apiReturn(200, $object, '用户数据为空');
        }

        $statisRes = $statisModel->getDistStatis([$uuid]);
        $teamName  = $inviteModel->getInviteConfigByUuid( $chainRes['top_uid']);

        $result = [
            'image'              => $memberInfo['headphoto'],
            'account'            => $memberInfo['account'],
            'dname'              => $memberInfo['dname'],
            'cname'              => $memberInfo['cname'],
            'mobile'             => $memberInfo['mobile'],
            'level'              => $chainRes['level'],
            'lower_level_num'    => $statisRes[0]['lower_level_num'] ?? 0,
            'order_num'          => $statisRes[0]['valid_order_num'] ?? 0,
            'total_order_amount' => $statisRes[0]['order_amount'] ?? 0,
            'total_profit'       => $statisRes[0]['real_income'] ?? 0,
            'create_time'        => $chainRes['create_time'],
            'team_name'          => $teamName[0]['team_name'] ?? '',
        ];

        $this->apiReturn(200, $result, '成功');
    }

    /**
     *  发展下级列表
     * <AUTHOR>
     * @date 2020/4/16
     *
     * @return array
     */
    public function lowList()
    {
        $uuid     = I('post.uuid', '', 'strval');//分销专员uuid
        $page     = I('post.page', 1, 'intval');//当前页
        $pageSize = I('post.page_size', 15, 'intval');//每页条数
        $mobile   = I('post.mobile', '', 'strval');//手机号
        $dname    = I('post.dname', '', 'strval');//账号名称
        $account  = I('post.account', '', 'strval');//账号
        $isSuper  = $this->isSuper();        //判断登陆用户是不是管理员
        $sid      = $this->memberInfo['sid'];

        if (!$uuid) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $memberModel = new MultiMemberModel();

        $chainRes = $memberModel->getChainByUuid($uuid, 'uid, uuid, level_num as level, top_uid, higher_uid, create_time');
        if (!$chainRes) {
            $this->apiReturn(203, [], '参数错误');
        }

        //数据权限校验
        if (!$isSuper && $sid != $chainRes['top_uid'] && $sid != $chainRes['higher_uid']) {
            if ($this->uuid != $chainRes['top_uid']) {
                $this->apiReturn(203, [], '无权限');
            }
        }

        $params = [
            'mobile'     => $mobile,
            'dname'      => $dname,
            'account'    => $account,
            'higher_uid' => $chainRes['uid'],
            'top_uid'    => $chainRes['top_uid'],
            'neq_uuid'   => $this->uuid
        ];

        $list = $memberModel->getList($page, $pageSize, $params);

        if (!$list) {
            $this->apiReturn(200, ['list' => [], 'total' => 0], '');
        }

        foreach ($list as &$value) {
            $value['lower_level_num'] = $value['lower_num'] ?? 0;
            unset($value['order_num'], $value['sale_amount'], $value['provide_profit'], $value['lower_num']);
        }

        $total = $memberModel->getList($page, $pageSize, $params, '', 0, true);
        $this->apiReturn(200, ['list' => $list, 'total' => $total], '成功');
    }

    /**
     * 运营商列表
     * <AUTHOR>
     * @date 2020/4/16
     *
     * @return array
     */
    public function topList()
    {
        pft_log('page_use/debug', '/r/MultiDist_Member/topList is use');
        $startTime  = I('post.start_time', '', 'strval');//开始时间
        $endTime    = I('post.end_time', '', 'strval');//结束时间
        $page       = I('post.page', 1, 'intval');//当前页
        $pageSize   = I('post.page_size', 15, 'intval');//每页条数
        $mobile     = I('post.mobile', '', 'strval');//手机号
        $dname      = I('post.dname', '', 'strval');//账号名称
        $account    = I('post.account', '', 'strval');//账号
        $isSuper    = $this->isSuper();        //判断登陆用户是不是管理员

        if (!$isSuper) {
            $this->apiReturn(203, [], '无权限');
        }

        if (!empty($startTime) && empty($endTime)) {
            $this->apiReturn(203, [], '请选择结束时间');
        }
        if (!empty($endTime) && empty($startTime)) {
            $this->apiReturn(203, [], '请选择开始时间');
        }

        $memberModel = new MultiMemberModel();
        $keyword = '';
        $keywordType = 0;
        $uids = [];

        if ($mobile) {
            $keyword = $mobile;
            $keywordType = 2;
        }
        if ($dname) {
            $keyword = $dname;
            $keywordType = 1;
        }
        if ($account) {
            $keyword = $account;
            $keywordType = 3;
        }
        if ($keyword) {
            $userList = $memberModel->getUserInfo($keyword, 0, $keywordType);
            if (!$userList) {
                $this->apiReturn(200, ['list' => [], 'total' => 0], '数据为空');
            }
            $uids = array_column($userList, 'uid');
        } else {
            $topUserList = $memberModel->getChainByTop();
            if ($topUserList) {
                $uids = array_column($topUserList, 'uid');
            }
        }

        $moduleListModel = new ModuleList();

        $list = $moduleListModel->getModuleListForModuleId(Module::getModuleIdByMenuId(MultiMemberBiz::APP_MENU_TAG), $page, $pageSize, $startTime, $endTime, $uids);
        if (empty($list['list'])) {
            $this->apiReturn(200, ['list' => [], 'total' => 0], '数据为空');
        }

        $memberIdArr = array_column($list['list'], 'member_id');
        $memberInfos = $memberModel->getMemberByUids($memberIdArr);
        $memberMaps  = array_key($memberInfos, 'uid');

        //获取uuid
        $chainInfos = $memberModel->getChainByUid($memberIdArr, $memberModel::TOP_LEVEL);
        $chainMaps  = array_key($chainInfos, 'uid');

        $distInviteModel = new DistInviteModel();
        $teaminfos       = $distInviteModel->getInviteConfigByUuid($memberIdArr);
        if ($teaminfos) {
            $teaminfos = array_key($teaminfos,'uid');
        }

        foreach ($list['list'] as &$value) {
            $value['begin_time']  = $value['begin_time'] ?? 0;
            $value['expire_time'] = $value['expire_time'] ?? 0;
            $value['buy_time']    = $this->_getBuyTime($value['expire_time'], $value['begin_time']);
            $value['mobile']      = $memberMaps[$value['member_id']]['mobile'] ?? '';
            $value['cname']       = $memberMaps[$value['member_id']]['cname'] ?? '';
            $value['account']     = $memberMaps[$value['member_id']]['account'] ?? '';
            $value['dname']       = $memberMaps[$value['member_id']]['dname'] ?? '';
            $value['uid']         = $memberMaps[$value['member_id']]['uid'] ?? '';
            $value['top_uid']     = $memberMaps[$value['member_id']]['uid'] ?? '';
            $value['uuid']        = $chainMaps[$value['member_id']]['uuid'] ?? '';
            $value['team_name']        = $teaminfos[$value['member_id']]['team_name'] ?? '';
            //unset($value['order_num'], $value['sale_amount'], $value['provide_profit'], $value['lower_num']);
        }

        $this->apiReturn(200, $list, '成功');
    }

    /**
     * 计算购买时长
     * <AUTHOR>
     * @date 2020/4/27
     *
     * @param int $start 开始时间
     * @param int $end 到期时间
     *
     * @return array
     */
    private function _getBuyTime($end, $start)
    {
        $day = 3600 * 24;
        $month = $day * 30;
        $quart = $day * 90;
        $year = $day * 365;

        $endStart = $end - $start;
        if ($endStart >= $year) {
            return round($endStart / $year) . '年';
        }

        if ($endStart >= $quart) {
            return round($endStart / $quart) . '季度';
        }

        if ($endStart >= $month) {
            return round($endStart / $month) . '月';
        }

        return round($endStart / $day) . '天';
    }

    /**
     * 运营商详情
     * <AUTHOR>
     * @date 2020/4/17
     *
     * @return array
     */
    public function topInfo()
    {
        pft_log('page_use/debug', '/r/MultiDist_Member/topInfo is use');
        $uuid    = I('post.uuid', '', 'strval');//分销专员uuid
        $isSuper = true;        //判断登陆用户是不是管理员
        $array = [];
        $object = (object)$array;

        if (!$isSuper) {
            $this->apiReturn(203, [], '无权限');
        }

        if (!$uuid) {
            $this->apiReturn(203, $object, '参数错误');
        }

        $memberQuery = new \Business\Member\Member();
        $statisModel = new \Model\MultiDist\Statis();
        $inviteModel = new DistInviteModel();
        $multiDistMemberBiz = new MultiMemberBiz();

        $chainRes = $multiDistMemberBiz->getUuidInfo($uuid);
        if (!$chainRes) {
            $this->apiReturn(203, $object, '参数错误');
        }

        //用户基础信息
        $memberInfo = $memberQuery->getInfo($chainRes['uid']);
        if (!$memberInfo) {
            $this->apiReturn(200, $object, '用户数据为空');
        }

        $statisRes = $statisModel->getDistStatis([$uuid]);

        //模块开通信息
        $moduleListModel = new ModuleList();
        $moduleRes = $moduleListModel->getInfoByUserModule($chainRes['uid'], Module::getModuleIdByMenuId(MultiMemberBiz::APP_MENU_TAG));
        $moduleResMap = array_key($moduleRes, 'member_id');

        //全局佣金比例
        $productModel = new DistProductModel();
        $configRes    = $productModel->getDefaultConfig($uuid);
        $profit = @implode(':', json_decode($configRes['profit_config'], true));
        $teamName  = $inviteModel->getInviteConfigByUuid($chainRes['top_uid']);
        $result = [
            'image'              => $memberInfo['headphoto'],
            'account'            => $memberInfo['account'],
            'dname'              => $memberInfo['dname'],
            'cname'              => $memberInfo['cname'],
            'mobile'             => $memberInfo['mobile'],
            'level'              => (int)$chainRes['level'],
            'lower_level_num'    => (int)$statisRes[0]['lower_level_num'] ?? 0,
            'level_num'          => (int)$statisRes[0]['level_num'] ?? 0,
            'real_expend'        => (int)$statisRes[0]['real_expend'] ?? 0,
            'product_num'        => $productModel->getProductList($uuid, 1, 15, [], '', 1, true),
            'begin_time'         => $moduleResMap[$chainRes['uid']]['begin_time'] ?? 0,
            'expire_time'        => $moduleResMap[$chainRes['uid']]['expire_time'] ?? 0,
            'profit_rate'        => $profit,
             'team_name'         => $teamName[0]['team_name'] ?? '',
        ];

        $this->apiReturn(200, $result, '成功');
    }


    /**
     * 根据关键词搜索下级用户
     * <AUTHOR>
     * @date    2020/4/16
     *
     * @return
     */
    public function search()
    {
        //搜索关键词
        $keyword     = trim(I('get.keyword', '', 'strval'));
        //1=根据账号名搜索 2=根据手机号搜索 3=根据账号搜索
        $keywordType = I('get.keyword_type', 0);

        if (!$keyword) {
            $this->apiReturn(200);
        }

        //参数校验
        if (!in_array($keywordType, [1, 2, 3])) {
            $this->apiReturn(400, [], '参数错误');
        }

        //身份校验
        if ($this->currentRole != MultiDistBaseBiz::TOP_ROLE) {
            $this->apiReturn(201, [], '非法请求');
        }

        $userList = (new \Model\MultiDist\Member())->getUserInfo($keyword, $this->memberInfo['memberID'], $keywordType, true);
        $this->apiReturn(200, $userList);
    }


    /**
     * 删除分销专员
     * <AUTHOR>
     * @date 2020/9/27
     *
     * @return array
     */
    public function delete()
    {
        $uuid = I('post.uuid', '', 'strval');//要删除的分销专员唯一标识

        if (empty($uuid)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $distMemberLib = new MultiMemberBiz($this->memberInfo['sid'], $this->uuid);
        $deleteRes = $distMemberLib->deleteMultiDist($uuid, 'delete');
        $this->apiReturn($deleteRes['code'], $deleteRes['data'], $deleteRes['msg']);
    }

    /**
     * 退出分销专员团队
     * <AUTHOR>
     * @date 2020/9/30
     *
     * @return array
     */
    public function outTeam()
    {
        $uuid = I('post.uuid', '', 'strval');//要退出的分销专员唯一标识

        if (empty($uuid)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $distMemberLib = new MultiMemberBiz($this->memberInfo['sid'], $this->uuid);
        $deleteRes = $distMemberLib->deleteMultiDist($uuid, 'out');
        $this->apiReturn($deleteRes['code'], $deleteRes['data'], $deleteRes['msg']);
    }


}