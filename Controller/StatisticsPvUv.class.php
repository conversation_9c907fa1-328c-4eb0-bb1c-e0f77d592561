<?php
/**
 * 统计PV
 * User: Lanlc
 * Date: 16-10-18
 * Time: 下午3:30
 */

namespace Controller;

use Library\Controller;
use Library\Cache\RedisCache;
use Model\SystemLog\Statistics;

class StatisticsPvUv extends Controller
{
    function __construct(){

    }
    /*
     * 统计页面的PV
     *
     * @param string $pageName      页面名称
     * @param string $pageIdentify  页面唯一标识
     * @param string $type          页面类型
     *
     * */
    public function statistics(){
        $pageName     = safe_str(I('get.pageName'));
        $pageIdentify = safe_str(I('get.pageIdentify'));
        $pageIdentify = safe_str(getUrl($pageIdentify));
        $type = intval(I('get.type'));
        $this->savePv($pageName, $pageIdentify, $type);
    }

    /**
     *
     * 广告链接中转统计PV
     *
     *
     */
    public function tempUrl()
    {
        $pageName     = safe_str(I('get.pageName'));
        $pageIdentify = safe_str(I('get.pageIdentify'));
        $pageIdentify = safe_str(getUrl($pageIdentify));
        $type         = intval(I('get.type'));
        $url          = safe_str(I('get.url'));
        $url          = str_replace('amp;', '', $url);
        $aid          = safe_str(I('get.aid'));

        $res = $this->savePv($pageName, $pageIdentify, $type, $aid);
        //var_dump($res);die;
        header("Location: $url");
    }

    private function savePv($pageName, $pageIdentify, $type, $aid=0)
    {
        $redis = \Library\Cache\Cache::getInstance('redis');
        $hitsInfo = $redis->get('hitsInfo');

        if ($hitsInfo) {
            $hitsInfo = json_decode($hitsInfo, true);
            $wait_update = $hitsInfo['wait_update'];
            if (isset($wait_update[$pageIdentify]) && $wait_update[$pageIdentify]['type'] == $type) {
                $wait_update[$pageIdentify]['hits']++;
            } else {
                $wait_update[$pageIdentify] = array('name' => $pageName, 'identify'=>$pageIdentify,  'type' => $type, 'hits' => 1);
            }
        } else {//首次访问，初始化数据
            $hitsInfo['last_update_time'] = time();
            $wait_update[$pageIdentify] = array('name' => $pageName, 'identify'=>$pageIdentify, 'type' => $type, 'hits' => 1);
        }
        $hitsInfo['wait_update'] = $wait_update;
        //var_dump(time(),'--',$hitsInfo['last_update_time']);
        if (time() - 15 > $hitsInfo['last_update_time']) {   //每半小时更新一次
            $statistics = new Statistics();
            foreach ($wait_update as $identify => $item) {
                $pageId = $statistics->savePageData($item['name'], $item['identify']);
                if (!$pageId){
                    exit('系统错误');
                }
                //$pageType = $item['type'];
                $date = strtotime(date('Y-m-d'));
                $res = $statistics->saveStatistice($pageId, $date, $item['hits'], $aid);
                //var_dump($res);
            }
            $hitsInfo['last_update_time'] = time();
            $hitsInfo['wait_update'] = array();
        }
        $res = $redis->set('hitsInfo', json_encode($hitsInfo), 60);
    }

    /**
     * 获取页面的统计量
     *
     * @date   2017-10-23
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function getPvCnt() {
        $pageIdentify = safe_str(I('get.pageIdentify'));
        $pageIdentify = safe_str(getUrl($pageIdentify));
        if (!$pageIdentify) {
            exit(0);
        }
        $statistics = new Statistics();
        $res = $statistics->getPvCnt($pageIdentify);

        if ($res) {
            exit($res['hits'] . '|' . $res['share']. '|' . $res['id']);
        }
        exit(0);
    }
} 