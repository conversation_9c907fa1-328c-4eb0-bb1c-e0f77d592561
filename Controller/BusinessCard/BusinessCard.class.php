<?php
/**
 * 名片业务
 * User: linchen
 * Date: 2020/04/03
 * Time: 10:27
 */

namespace Controller\BusinessCard;

use Library\Cache\Cache;
use Library\Controller;
use Library\Tools\JWT;

class BusinessCard extends Controller
{

    /**
     * 名片登录
     * <AUTHOR>
     * @Date 2020/04/03
     *
     * @param  string account 账号
     * @param  string password 密码
     * @param  int dtype 角色类型 0=供应商，1=分销商等
     */
    public function login()
    {
        $account  = I('post.account', '', 'strval');
        $password = I('post.password', '', 'strval');
        $dtype    = I('post.dtype',false);
        if (empty($account) || empty($password)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '账号或密码不能为空');
        }
        $loginBiz = new \Business\Member\Login();
        $res      = $loginBiz->loginByPasswd($account, md5(md5($password)), 14, $dtype, false);
        if ($res['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }
        $userInfo = $res['data'];
        $data     = [
            'member_id' => $userInfo['member_id'],
            'dtype'     => $userInfo['member_type'],
        ];
        $sessionkey = $this->cardsessionKey();
        $cacheRedis = Cache::getInstance('redis');
        $cacheRedis->set($sessionkey, $data, '', 60, true);
        $returnData = [
            'login_key'  => $sessionkey,
            'expire'     => 60,
        ];

        $this->apiReturn($res['code'], $returnData, $res['msg']);
    }

    /**
     * 生成session key
     *
     * @return string
     */
    private function cardSessionKey()
    {
        return 'card_login:' . md5(microtime() . rand());
    }
}