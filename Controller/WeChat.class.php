<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 11/23-023
 * Time: 10:34
 */

namespace Controller;

use Business\Member\MemberWx;
use Business\Notice\WxConfig;
use Library\Business\WechatSmallApp;
use Library\Cache\Cache;
use Library\Constants\RedisKey\TeamOrderConst;
use Library\Controller;
use Library\MessageNotify\OrderNotify;
use Library\wechat\core\OpenExt;
use Model\Member\Member;
use Business\JavaApi\Member\Login as LoginApi;
use Model\Wechat\WxMember;

class WeChat extends Controller
{
    private $request_object;

    public function __construct()
    {
//        parent::__construct();
        $input                = file_get_contents('php://input');
        $this->request_object = json_decode($input);
    }

    /**
     * 小程序自动登录
     * <AUTHOR> Chen
     * @date 2016-11-24
     * 参数：
     *      post.js_code        换取 session_key的code
     *      post.encryptedData  包括敏感数据在内的完整用户信息的加密数据
     *      post.iv             加密算法的初始向量
     * 成功返回：
     *      成功：{code:200,data:["third_session":"校验登录状态的session——key，之后的request请求都需要带上这个值"],msg:"消息"}
     *      失败：{code:xxx,msg:失败描述}
     */
    public function small_app_auto_login()
    {
        $lib      = new WechatSmallApp();
        $code     = $this->request_object->js_code;
        $sessInfo = $lib->getWxSession($code);
        $userInfo = $lib->getWxUserInfo($sessInfo['session_key'], $this->request_object->encryptedData,
            $this->request_object->iv);
        if ($userInfo['code'] == 200) {
            $unionid = $userInfo['data']['unionId'];
            $openid  = $userInfo['data']['openId'];
            //检测是否有绑定微信小程序帐号
            $modelWxMember = new \Model\Wechat\WxMember('localhost');
            $info          = $modelWxMember->GetWxInfoByOpenid($openid, WechatSmallApp::APPID);
            $sessionInfo   = ['openid' => $openid, 'unionid' => $unionid];
            $sess_key      = WechatSmallApp::session_key();
            //如果没有绑定过帐号，根据unionid判断是否绑定票付通公众号
            if (!$info) {
                $info = $modelWxMember->UnionIdCheck($unionid, PFT_WECHAT_APPID);
                if ($info) {
                    $modelWxMember->BindWxInfo($info['fid'], 0, $unionid, $openid, WechatSmallApp::APPID);
                } else {
                    $lib->setSession($sess_key, $sessionInfo);
                    parent::apiReturn(201, ['third_session' => $sess_key], '还未绑定过帐号，需要手动登录');
                }
            }
            $sessionInfo['memberID'] = $info['fid'];
            $sessionInfo['aid']      = $info['aid'];
            $lib->setSession($sess_key, $sessionInfo);
            parent::apiReturn(200, ['third_session' => $sess_key, 'txt' => 1], '自动登录成功');
        }
        parent::apiReturn(202, ['third_session' => ''], '获取身份信息出错');
    }

    /**
     * 小程序手动登录——第一次使用或之前未绑定小程序帐号或未绑定票付通公众号
     * <AUTHOR> Chen
     * @date 2016-11-24
     * 参数：
     *      post.account
     *      post.password
     *      post.third_session
     * 成功返回：
     *      成功：{code:200,data:["third_session":"校验登录状态的session——key，之后的request请求都需要带上这个值"],msg:"消息"}
     *      失败：{code:xxx,msg:失败描述}
     */
    public function small_app_manual_login()
    {
        $account  = $this->request_object->account;
        $password = $this->request_object->password;
        $sess_key = $this->request_object->third_session;
        $chk_code = '';
        $member   = new Member('slave');
        $lib      = new WechatSmallApp();

        $loginApi = new LoginApi();
        $res      = $loginApi->loginByAccount($account, $password);
        // $res = $member->login($account, $password, $chk_code);
        if ($res['code'] == 200) {
            $modelWxMember           = new \Model\Wechat\WxMember('localhost');
            $session_info            = $lib->getSession($sess_key, '*');
            $sessionInfo             = [];
            $sessionInfo['memberID'] = $res['data']['id'];
            $sessionInfo['aid']      = 0;
            $sessionInfo['dtype']    = $res['data']['dtype'];
            $lib->setSession($sess_key, $sessionInfo);
            $modelWxMember->BindWxInfo($res['data']['id'], 0, $session_info['unionid'], $session_info['openid'],
                WechatSmallApp::APPID);
            parent::apiReturn(200, ['third_session' => $sess_key], '登录成功');
        } else {
            parent::apiReturn($res['code'], ['third_session' => $sess_key], $res['msg']);
        }
    }

    /**
     * 删除微信平台账号与微信的绑定
     *
     * <AUTHOR>
     * @date   2017-03-29
     *
     */
    public function unbind()
    {
        $loginInfo = $this->getLoginInfo('ajax');

        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数缺失');
        }

        if ($loginInfo['dtype'] == 6) {
            $this->apiReturn(204, [], '没有权限删除');
        }

        //加载微信用户模型
        $wechatModel = new \Model\Wechat\WxMember();

        $info = $wechatModel->getBindInfo($id, 'fid,fromusername');

        if ($info['fid'] != $loginInfo['sid']) {
            $this->apiReturn(204, [], '没有权限删除');
        }

        if (!$info['fromusername']) {
            $this->apiReturn(204, [], '数据错误');
        }

        $where = [
            'id' => $id,
        ];

        $res = $wechatModel->deleteBind($where);

        if ($res) {
            $memberWx = new MemberWx();
            $memberWx->handleWxnotifyListCache(PFT_WECHAT_APPID,$info['fid'],'','del');

            $this->apiReturn(200, [], '删除成功');
        } else {
            $this->apiReturn(200, [], '删除失败');
        }

    }

    public function setWxNoticeUser()
    {
        $loginInfo = $this->getLoginInfo();
        $id        = I('post.id', 0);
        $code      = I('post.code', -1, 'intval');
        if (!$id) {
            $this->apiReturn(200, [], '操作失败');
        }
        if ($code != 0 && $code != 1) {
            $this->apiReturn(204, [], '参数错误');
        }
        //加载微信用户模型
        $wechatModel = new \Model\Wechat\WxMember();

        $info = $wechatModel->getBindInfo($id, 'fid,fromusername');
        if ($info['fid'] != $loginInfo['sid']) {
            $this->apiReturn(204, [], '没有权限操作');
        }
        $obj = new \Model\Wechat\WxMember();
        try {
            $obj->startTrans();
            $ret = $obj->setNotify($id, $code);
            if ($ret === false) {
                throw new \Exception('操作失败');
            }
            //关闭通知 把通知分类全都关闭
            if ($code == 0) {

                $data    = [
                    'orderNotice'          => 0,
                    'creditManage'         => 0,
                    'refundRecharge'       => 0,
                    'reimburse'            => 0,
                    'smsAccountNotice'     => 0,
                    'voucherAccountNotice' => 0,
                ];
                $addData = [
                    'notice_info' => json_encode($data),
                    'bind_id'     => $id,
                    'update_time' => time(),
                ];
                $result  = $obj->addWxNoticeType($id, $addData);

                if ($result === false) {
                    throw new \Exception('修改通知失败');
                }
            }
            $obj->commit();

        } catch (\Exception $e) {
            $obj->rollback();
            $this->apiReturn(400, [], $e->getMessage());
        }

        $memberWx = new MemberWx();
        $memberWx->handleWxnotifyListCache(PFT_WECHAT_APPID, $loginInfo['sid'], '', 'del');

        $cacheRedis = Cache::getInstance('redis');
        $wxModel    = new WxMember();
        $cacheKey   = sprintf($wxModel->wxBindListKey, $loginInfo['sid']);
        $cacheRedis->rm($cacheKey);
        //删除下团单那边的缓存
        $teamNotifyCacheKey = sprintf(TeamOrderConst::_TEAM_WX_MEMBER_LIST_, $loginInfo['sid']);
        $cacheRedis->rm($teamNotifyCacheKey);
        WxConfig::pushJobForUser($loginInfo['sid'], $id);
        $this->apiReturn(200, [], '操作成功');

    }

    public function notify()
    {
        $token     = 'R3R4U21BSmZvTmVXSWNZ';//$conf['token']
        $wechatLib = new \Library\wechat\core\Wechat($token);
        $wechatLib->run();
    }

    /**
     * 设置微信绑定通知分类
     * <AUTHOR>
     * @Date 2020/09/04
     *
     */
    public function setWxNoticeInfo()
    {
        $loginInfo = $this->getLoginInfo();
        //用户微信绑定表主键id

        $bindId               = I('post.bind_id', 0, 'intval');
        $orderNotice          = I('post.order_notice', -1, 'intval');
        $creditManage         = I('post.credit_manage', -1, 'intval');
        $refundRecharge       = I('post.refund_recharge', -1, 'intval');
        $reimburse            = I('post.reimburse', -1, 'intval');
        $smsAccountNotice     = I('post.sms_account_notice', -1, 'intval');
        $voucherAccountNotice = I('post.voucher_account_notice', -1, 'intval');
        $teamOrderNotice      = I('post.team_order_notice', 0, 'intval'); //团单通知
        $aliExpireNotice      = I('post.ali_expire_notice',0,'intval'); //阿里供销授权即将到期通知

        if (!$bindId) {
            $this->apiReturn(204, [], '参数错误');
        }
        if (!in_array($orderNotice, [0, 1]) || !in_array($creditManage, [0, 1]) || !in_array($refundRecharge, [0, 1])) {
            $this->apiReturn(204, [], '参数错误');
        }
        //加载微信用户模型
        $wechatModel = new \Model\Wechat\WxMember();

        $info = $wechatModel->getBindInfo($bindId, 'fid,fromusername');
        if ($info['fid'] != $loginInfo['sid']) {
            $this->apiReturn(204, [], '没有权限操作');
        }

        $obj     = new \Model\Wechat\WxMember();
        $data    = [
            'orderNotice'          => $orderNotice,
            'creditManage'         => $creditManage,
            'refundRecharge'       => $refundRecharge,
            'reimburse'            => $reimburse,
            'smsAccountNotice'     => $smsAccountNotice,
            'voucherAccountNotice' => $voucherAccountNotice,
            'teamOrderNotice'      => $teamOrderNotice,
            'aliExpireNotice'      => $aliExpireNotice,
        ];
        $addData = [
            'notice_info' => json_encode($data),
            'bind_id'     => $bindId,
            'update_time' => time(),
        ];
        $result  = $obj->addWxNoticeType($bindId, $addData);

        if ($result === false) {
            $this->apiReturn(204, [], '操作失败');
        }

        $memberWx = new MemberWx();
        $memberWx->handleWxnotifyListCache(PFT_WECHAT_APPID, $loginInfo['sid'], '', 'del');

        $cacheRedis = Cache::getInstance('redis');
        $wxModel    = new WxMember();
        $cacheKey   = sprintf($wxModel->wxBindListKey, $loginInfo['sid']);
        $cacheRedis->rm($cacheKey);
        //删除下团单那边的缓存
        $teamNotifyCacheKey = sprintf(TeamOrderConst::_TEAM_WX_MEMBER_LIST_, $loginInfo['sid']);
        $cacheRedis->rm($teamNotifyCacheKey);

        WxConfig::pushJobForUser($loginInfo['sid'], $bindId);

        $this->apiReturn(200, [], '操作成功');
    }


    /**
     * 设置用户微信自动登录
     * <AUTHOR>
     * @Date 2022/04/01
     *
     */
    public function setWxUserAutoLogin()
    {
        $id   = I('post.id', 0);
        $code = I('post.code', 0, 'intval');  //1=开启 2=关闭

        if (!$id) {
            $this->apiReturn(204, [], '操作失败');
        }

        if (empty($code) || !in_array($code, [1, 2])) {
            $this->apiReturn(204, [], '参数错误');
        }

        $loginInfo   = $this->getLoginInfo();
        $wechatModel = new \Model\Wechat\WxMember();
        $info        = $wechatModel->getBindInfo($id, 'fid, fromusername, auto_login');

        if (empty($info)) {
            $this->apiReturn(204, [], '操作失败');
        }

        if ($info['fid'] != $loginInfo['sid']) {
            $this->apiReturn(204, [], '没有权限操作');
        }

        if ($info['auto_login'] == $code) {
            $this->apiReturn(200, [], '操作成功');
        }

        if ($wechatModel->setAutoLogin($id, $code)) {
            return $this->apiReturn(200, [], '操作成功');
        }

        return $this->apiReturn(201, [], '操作失败');
    }
}