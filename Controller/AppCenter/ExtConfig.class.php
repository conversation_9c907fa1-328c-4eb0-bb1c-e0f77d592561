<?php
/**
 * 增值服务配置 管理员专用
 * 签单人员配置页面
 *
 * <AUTHOR>
 * @date   2020-07-10
 */

namespace Controller\AppCenter;
use Library\Controller;
use \Business\AppCenter\ExtConfig as ExtConfigBiz;

class ExtConfig extends Controller
{

    private $_loginInfo;

    private $_isAdmin = false;

    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo();
        if ($this->_loginInfo['sdtype'] == 9) {
            $this->_isAdmin = true;
        }
    }


    /**
     * 增值服务配置信息
     * <AUTHOR>
     * @date   2020-07-11
     *
     * @param  fid  integer 查询的商家id
     *
     */
    public function info()
    {
        $this->apiReturn(403, [], '无权限操作');
    }


    /**
     * 全部可用的渠道
     * <AUTHOR>
     * @date 2021/5/25
     *
     */
    public function channelAll()
    {
        $channel = trim(I('post.channel', '', 'strval'), ' ');
        //$channel = 'all_category';
        if (empty($channel)) {
            $this->apiReturn(203, [], '查询参数错误');
        }
        $res = (new ExtConfigBiz())->getChannelAll($channel);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);

    }

}