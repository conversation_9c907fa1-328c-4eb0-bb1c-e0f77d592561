<?php
/**
 * 应用 组合
 * <AUTHOR>
 * @date 2021-05-31
 */

namespace Controller\AppCenter;
use Business\AppCenter\BaseCall;
use Business\AppCenter\Payment as PaymentBiz;
use Library\Business\Alipay\F2FPay;
use Library\Business\WePay\WxPayLib;
use Library\Controller;
use Business\AppCenter\ModuleCombine as ModuleCombineBiz;
use Model\TradeRecord\OnlineTrade;

class ModuleCombine extends Controller
{

    //登录信息
    private $_loginInfo;
    //应用模块业务对象
    private $_moduleCombineBiz;

    //支付类型
    private $_payWay = [
        1 => '平台账户余额支付',
        2 => '微信支付',
        3 => '支付宝支付'
    ];

    //购买日志
    private $_logDir = 'package/user_buy/';

    public function __construct()
    {
        $this->_loginInfo  = $this->getLoginInfo();
        $this->_moduleCombineBiz = new ModuleCombineBiz();
    }


    /**
     * 组合列表
     * <AUTHOR>
     * @date 2021/06/04
     *
     */
    public function combineList()
    {
        $combineListRes = $this->_moduleCombineBiz->combineList();
        $this->apiReturn($combineListRes['code'], $combineListRes['data'], $combineListRes['msg']);
    }


    /**
     * 组合详情
     * <AUTHOR>
     * @date 2021/06/04
     *
     */
    public function baseInfo()
    {
        //组合id
        $combineId = I('get.combine_id', 0, 'intval');
        $memberId  = $this->_loginInfo['memberID'];

        if (!$combineId || !$memberId) {
            $this->apiReturn(203, [], '组合id参数错误');
        }

        $combineInfoRes = $this->_moduleCombineBiz->combineInfo($combineId, $memberId);
        $this->apiReturn($combineInfoRes['code'], $combineInfoRes['data'], $combineInfoRes['msg']);
    }


    /**
     * 购买模块
     * <AUTHOR>
     * @date 2021/06/04
     *
     */
    public function buyCombineOnline()
    {
        $code    = 200;
        $msg     = '';
        $url     = '';
        $result  = '';

        //请求id
        $requestId = str_replace('.', '', microtime(true));
        $cache     = \Library\Cache\Cache::getInstance('redis');

        try {

            $memberId  = $this->_loginInfo['memberID'];
            $dtype     = $this->_loginInfo['dtype'];

            if (!in_array($dtype, [0, 1])) {
                throw new \Exception("身份不符,不允许开通");
            }

            //组合id
            $combineId = I('post.combine_id', 0, 'intval');
            //购买时长 1=年 2=季度 3=月
            $priceMode = I('post.price_mode', 0, 'intval');
            //支付方式
            $payWay    = I('post.pay_way', 0, 'intval');

            if (!$combineId || !$priceMode) {
                $this->apiReturn(204, [], '参数错误');
            }

            if (empty($payWay) || !isset($this->_payWay[$payWay])) {
                throw new \Exception("支付方式参数错误");
            }

            //支付方式描述
            $payWayStr = $this->_payWay[$payWay];
            //开通申请 日志
            pft_log($this->_logDir, "{$requestId}:申请开通：用户ID：{$memberId}, 组合ID：{$combineId},资费ID：{$priceMode}|支付方式：{$payWayStr}");

            //加锁 防止恶意请求
            $locky    = "pay_combine:{$payWayStr}:{$requestId}:$memberId";
            $lock_ret = $cache->lock($locky, 1, 120);
            if (!$lock_ret) {
                throw new \Exception("请求正在处理中，请稍后");
            };

            //开通套餐前的检测  走新工程appcenterService
            $checkRes = $this->_moduleCombineBiz->openPreCheck($memberId, $dtype, $combineId, $priceMode, $payWay);
            if ($checkRes['code'] != 200) {
                throw new \Exception($checkRes['msg']);
            }

            //标题
            $subject   = $checkRes['data']['subject'];
            //订单号
            $orderNo   = $checkRes['data']['trade_no'];
            //费用 微信以分为单位
            $priceData = $checkRes['data']['fee'];
            //组合信息
            $combineInfo = $checkRes['data']['combine_info'] ?? [];

            if (empty($orderNo) || !isset($priceData['price'])) {
                throw new \Exception("订单生成出错, 请重试");
            }

            $fee = $priceData['price'];
            //套餐数据
            $combineData = [
                'combine_id'    => $combineId,
                'price_mode'    => $priceMode,
                'sale_info'     => [],
            ];

            $tmpRemarks = [
                'member_id'    => $memberId,
                'combine_data' => $combineData,
                'fee'          => $fee,
                'subject'      => $subject,
                'pay_type'     => 3,    //标识是购买应用组合
            ];

            $remarks = json_encode($tmpRemarks);

            if ($payWay == 1) {

                $handleAfterOpen = true;
                //平台账户余额支付
                $payRes = (new PaymentBiz())->platformPayModule($orderNo, $fee, $memberId, $subject);
                if ($payRes['code'] != 200) {
                    throw new \Exception($payRes['msg']);
                }

                //开通组合
                $openRes = $this->_moduleCombineBiz->openCombine($memberId, $combineId, $priceMode, $orderNo);
                //如果开通组合失败
                if ($openRes['code'] != 200) {
                    //记录余额支付异常日志
                    throw new \Exception($openRes['msg']);
                }

            } else {
                //默认支付方式：支付宝
                $sourceT = 0;
                $onLineTrade = new OnlineTrade();
                //微信支付或者支付宝支付
                if (defined('ENV') && in_array(ENV, ['PRODUCTION', 'TEST'])) {

                    if ($payWay == 2) {//微信
                        $getQrUrl = $this->_getWxPayQrCode($orderNo, $fee, $subject);
                        $sourceT = 1; //来源记录微信
                        $result .= '生成微信扫码支付，';
                    }

                    if ($payWay == 3) {//支付宝
                        $getQrUrl = $this->_getAliPayQrCode($orderNo, $fee, $subject);
                        $sourceT = 0; //来源记录支付宝
                        $result .= '生成支付宝扫码支付，';
                    }

                    if (empty($getQrUrl['code']) || $getQrUrl['code'] != 200) {
                        throw new \Exception($getQrUrl['msg']);
                    }

                    $url = $getQrUrl['url'];
                    //生成充值记录
                    $create = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception("充值记录生成失败");
                    }

                } else {

                    $handleAfterOpen = true;
                    //测试环境，模拟成功支付
                    $create = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception("充值记录生成失败");
                    }

                    //模拟支付成功
                    $tradeNo = 'online_' . time();
                    $res     = (new PaymentBiz())->onlinePayCombine($orderNo, $tmpRemarks, $tradeNo, 2);
                    if ($res['code'] != 200) {
                        throw new \Exception($res['msg']);
                    }

                    $result .= '测试模拟支付，';
                    //开通套餐
                    $openRes = $this->_moduleCombineBiz->openCombine($memberId, $combineId, $priceMode, $orderNo);
                    if ($openRes['code'] != 200) {
                        throw new \Exception($openRes['msg']);
                    }
                }
            }

            if (!empty($handleAfterOpen) && !empty($openRes['data'])) {
                //获取下组合包含的模块
                $getCombineRes = $this->_moduleCombineBiz->combineInfo($combineId, $memberId);
                if ($getCombineRes['code'] != 200) {
                    throw new \Exception($getCombineRes['msg']);
                }

                $combineInfoData = $getCombineRes['data'];
                if (empty($combineInfoData['base_info']['module_data'])) {
                    throw new \Exception('组合数据异常');
                }

                $moduleIdArr = array_column($combineInfoData['base_info']['module_data'],'module_id');

                $openData  = $openRes['data'];
                $handleRes = (new \Business\AppCenter\Notify())->openNotify($moduleIdArr, $memberId, $memberId, $openData['begin_time'],
                    $openData['end_time']);

                if (!$handleRes) {
                    throw new \Exception( $handleRes['msg']);
                }
            }

        } catch (\Exception $e) {

            $code = 400;
            $msg  = $e->getMessage();
        }

        if (!empty($locky)) {
            $cache->rm($locky);
        }

        //开通结果 日志
        if ($code == 200) {
            $result .= '成功';
        } else {
            $result .= '失败';
        }

        $fee = $fee / 100;
        pft_log($this->_logDir,"{$requestId}:申请结果：用户ID：{$memberId}, 组合ID：{$combineId},资费ID：{$priceMode}, 费用{$fee}元, 流水号{$orderNo}|结果|{$result}, 原因:{$msg}");

        $data = ['url' => $url, 'order_no' => $orderNo];
        $this->apiReturn($code, $data, $msg);
    }



    /**
     * 微信支付二维码
     *
     * @params  string      $orderNo    内部支付流水号
     * @params  integer     $fee        金额
     * @params  string      $subject    订单标题
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    private function _getWxPayQrCode(string $orderNo, int $fee, string $subject)
    {
        $code = 200;
        $msg  = '';
        $url  = '';

        if (!$orderNo || !$fee || !$subject) {
            return ['code' => 400, 'msg' => '', 'url' => ''];
        }

        $wxPayLib   = new WxPayLib(PFT_WECHAT_APPID);
        $parameters = $wxPayLib->qrPay(
            $fee,
            $subject,
            $orderNo,
            ModulePayment::WX_NOTICE_NOTIFY_URL,
            '微信二维码支付',
            'orderpay'
        );

        if ($parameters['return_code'] == 'SUCCESS') {
            $url = $parameters['code_url'];
        } else {
            $code = 500;
            $msg  = $parameters['return_msg'];
        }

        return ['code' => $code, 'msg' => $msg, 'url' => $url];
    }


    /**
     * 支付宝支付二维码
     *
     * @params  string      $orderNo    内部支付流水号
     * @params  integer     $fee        金额
     * @params  string      $subject     订单标题
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    private function _getAliPayQrCode(string $orderNo, int $fee, string $subject)
    {
        $code = 200;
        $msg  = '';
        $url  = '';

        if (!$orderNo || !$fee || !$subject) {
            return ['code' => 400, 'msg' => '', $url = ''];
        }

        //支付单位元
        $fee = $fee / 100;
        //生成二维码
        $f2fpay   = new F2FPay(PFT_ALIPAY_F2FPAY_ID);
        $response = $f2fpay->qrpay($orderNo, $fee, $subject, ModulePayment::ALI_NOTICE_NOTIFY_URL, $subject);

        if ($response->alipay_trade_precreate_response->code == 10000 && $response->alipay_trade_precreate_response->msg == 'Success') {
            $url = $response->alipay_trade_precreate_response->qr_code;
        }

        unset($f2fpay);

        if (empty($url)) {
            $code = 500;
            $msg  = "支付宝二维码生成错误, 请刷新页面";
        }

        return ['code' => $code, 'msg' => $msg, 'url' => $url];
    }


    /**
     * 开通记录
     *
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    public function openRecord()
    {
        $memberId     = $this->_loginInfo['sid'];
        $getDetailRes = $this->_moduleCombineBiz->openRecord($memberId);
        $this->apiReturn($getDetailRes['code'], $getDetailRes['data'], $getDetailRes['msg']);
    }


    /**
     * 购买记录
     *
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    public function buyRecord()
    {
        $memberId = $this->_loginInfo['sid'];
        $getDetailRes = $this->_moduleCombineBiz->buyRecord($memberId);
        $this->apiReturn($getDetailRes['code'], $getDetailRes['data'], $getDetailRes['msg']);
    }


    /**
     * 购买记录详情
     *
     * <AUTHOR>
     * @date   2021-06-04
     */
    public function buyRecordDetail()
    {
        //流水号
        $tradeNo  = I('get.trade_no', '', 'strval');
        $memberId = $this->_loginInfo['sid'];
        if (!$tradeNo) {
            $this->apiReturn(203, [], '交易流水参数错误');
        }

        $getDetailRes = $this->_moduleCombineBiz->buyDetail($tradeNo, $memberId);
        $this->apiReturn($getDetailRes['code'], $getDetailRes['data'], $getDetailRes['msg']);
    }

}