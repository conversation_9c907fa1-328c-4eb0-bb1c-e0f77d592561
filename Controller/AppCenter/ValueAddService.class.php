<?php
/**
* 应用中心-增值服务
* <AUTHOR>
* @date 2021-07-09
*/

namespace Controller\AppCenter;

use Library\Controller;
use Business\AppCenter\ValueAddService as vasBiz;
use Business\AppCenter\Payment as PaymentBiz;
use Library\Business\Alipay\F2FPay;
use Library\Business\WePay\WxPayLib;
use Model\TradeRecord\OnlineTrade;
use Business\JsonRpcApi\PayService\UnifiedPay;


class ValueAddService extends Controller
{

    //登录信息
    private $_loginInfo;
    private $_fid;

    //应用模块业务对象
    private $_vasBiz;

    //支付类型
    private $_payWay = [
        1 => '平台账户余额支付',
        2 => '微信支付',
        3 => '支付宝支付'
    ];

    //收银台支付方式配置（新）
    private $_payTypeMap = [
        1 => '支付宝支付',
        2 => '微信支付',
        3 => '平台账户余额支付'
    ];

    //购买日志
    private $_logDir = 'vas/user_buy/';

    const VAS_NOTIFY_URL = PAY_DOMAIN.'/r/pay_CommonPayNotify/platformPackageNotify';
    const VAS_SOURCET    = 41; // 与CommonPayNotify::platformPackageNotify


    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo();
        $this->_fid = $this->_loginInfo['sid'];
        $this->_vasBiz = new vasBiz();
    }


    /**
     * 分销商增量包列表
     * <AUTHOR>
     * @date 2021/07/04
     *
     */
    public function resellerAddPackList()
    {
        //增值服务类型
        $vasType = I('get.vas_type', -1, 'intval');
        $classify = I('get.classify', '', 'string');
        $params = [
            'role'     => $this->_loginInfo['dtype'],
            'vas_type' => $vasType,
            'classify' => $classify
        ];
        $combineListRes = $this->_vasBiz->resellerAddPackList($params);
        $this->apiReturn($combineListRes['code'], $combineListRes['data'], $combineListRes['msg']);
    }

    /**
     * 分销商增量包详情
     * <AUTHOR>
     * @date 2021/07/04
     *
     */
    public function resellerAddPackInfo()
    {
        //增值服务id
        $vasId   = I('get.vas_id', 0, 'intval');
        $vasType = I('get.vas_type', 1, 'intval');

        if (!$vasId || !$vasType) {
            $this->apiReturn(400,"增值服务id参数错误");
        }

        $vasInfo = $this->_vasBiz->resellerAddPackInfo($this->_loginInfo['sid'], $vasId, $vasType);
        $this->apiReturn($vasInfo['code'], $vasInfo['data'], $vasInfo['msg']);
    }


    /**
     * 在线购买
     * <AUTHOR>
     * @date 2021/07/04
     *
     */
    public function buyOnline()
    {
        $code    = 200;
        $msg     = '';
        $url     = '';
        $result  = '';

        //请求id
        $requestId = str_replace('.', '', microtime(true));
        $cache     = \Library\Cache\Cache::getInstance('redis');

        try {

            $memberId  = $this->_loginInfo['memberID'];
            $dtype     = $this->_loginInfo['dtype'];

            if (!in_array($dtype, [0, 1])) {
                throw new \Exception("身份不符,不允许开通");
            }

            //增值服务id
            $vasId   = I('post.vas_id', 0, 'intval');
            //增值服务类型
            $vasType = I('post.vas_type', 1, 'intval');
            //支付方式
            $payWay  = I('post.pay_way', 0, 'intval');
            $parentId  = I('post.parent_id', 0, 'intval');
            //服务开始生效时间
            $startDate = I('post.start_date', '', 'strval');
            //服务失效时间
            $endDate   = I('post.end_date', 0, 'strval');

            if (!$vasId || !$vasType) {
                throw new \Exception("增值服务相关参数错误");
            }

            if (empty($payWay) || !isset($this->_payWay[$payWay])) {
                throw new \Exception("支付方式参数错误");
            }

            $startTime = strtotime($startDate);
            $endTime   = strtotime($endDate);

            if (!$startTime || !$endTime || ($endTime - $startTime) < 0) {
                throw new \Exception("时间参数错误");
            }

            $endTime += 86399;

            //支付方式描述
            $payWayStr = $this->_payWay[$payWay];
           //开通申请 日志
            pft_log($this->_logDir, "{$requestId}:申请开通：用户ID：{$memberId}, 增值服务ID：{$vasId}, 支付方式：{$payWayStr}");

            //加锁 防止恶意请求
            $locky    = "pay_combine:{$payWayStr}:{$requestId}:$memberId";
            $lock_ret = $cache->lock($locky, 1, 120);
            if (!$lock_ret) {
                throw new \Exception("请求正在处理中，请稍后");
            };

            //开通前的检测  走新工程appcenterService
            $checkRes = $this->_vasBiz->openPreCheck($memberId, $dtype, $vasId, $vasType, $payWay, $startTime, $endTime);
            if ($checkRes['code'] != 200) {
                throw new \Exception($checkRes['msg']);
            }

            //标题
            $subject = $checkRes['data']['subject'];
            //订单号
            $orderNo = $checkRes['data']['trade_no'];
            //费用 微信以分为单位
            $fee    = $checkRes['data']['fee'];

            $tmpRemarks = [
                'member_id'    => $memberId,
                'vas_id'       => $vasId,
                'vas_type'     => $vasType,
                'fee'          => $fee,
                'subject'      => $subject,
                'start_time'   => $startTime,
                'end_time'     => $endTime,
                'pay_type'     => 4,    //标识是购买增值服务
            ];

            $remarks = json_encode($tmpRemarks);
            if ($payWay == 1) {
                //平台账户余额支付
                $payRes = (new PaymentBiz())->platformPayModule($orderNo, $fee, $memberId, $subject);
                if ($payRes['code'] != 200) {
                        throw new \Exception($payRes['msg']);
                }

                //开通
                $openRes = $this->_vasBiz->openVas($memberId, $orderNo, $vasId, $vasType, $payWay, $startTime, $endTime,$parentId);
                //如果开通失败
                if ($openRes['code'] != 200) {
                    //记录余额支付异常日志
                    throw new \Exception($openRes['msg']);
                }

            } else {
                //默认支付方式：支付宝
                $sourceT = 0;
                $onLineTrade = new OnlineTrade();

                //微信支付或者支付宝支付
                if (defined('ENV') && in_array(ENV, ['PRODUCTION', 'TEST'])) {

                    if ($payWay == 2) {//微信
                        $getQrUrl = $this->_getWxPayQrCode($orderNo, $fee, $subject);
                        $sourceT = 1; //来源记录微信
                        $result .= '生成微信扫码支付，';
                    }

                    if ($payWay == 3) {//支付宝
                        $getQrUrl = $this->_getAliPayQrCode($orderNo, $fee, $subject);
                        $sourceT = 0; //来源记录支付宝
                        $result .= '生成支付宝扫码支付，';
                    }

                    if (empty($getQrUrl['code']) || $getQrUrl['code'] != 200) {
                        throw new \Exception($getQrUrl['msg']);
                    }

                    $url = $getQrUrl['url'];
                    //生成充值记录
                    $create = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception("充值记录生成失败");
                    }

                } else {
                    //测试环境，模拟成功支付
                    $create = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception("充值记录生成失败");
                    }

                    //模拟支付成功
                    $tradeNo = 'online_' . time();
                    $res     = (new PaymentBiz())->onlinePayVas($orderNo, $tmpRemarks, $tradeNo, 2);
                    if ($res['code'] != 200) {
                        throw new \Exception($res['msg']);
                    }

                    $result .= '测试模拟支付，';
                    //开通套餐
                    //$openRes = $this->_vasBiz->openVas($memberId, $orderNo, $vasId, $vasType, $payWay, $startTime, $endTime);
                    //if ($openRes['code'] != 200) {
                    //    throw new \Exception($openRes['msg']);
                    //}
                }
            }

        } catch (\Exception $e) {

            $code = 400;
            $msg  = $e->getMessage();
        }

        if (!empty($locky)) {
            $cache->rm($locky);
        }

        //开通结果 日志
        if ($code == 200) {
            $result .= '成功';
        } else {
            $result .= '失败';
        }

        $fee = $fee / 100;
        pft_log($this->_logDir,"{$requestId}:申请结果：用户ID：{$memberId}, 增值服务ID：{$vasId}, 费用{$fee}元, 流水号{$orderNo}|结果|{$result}, 原因:{$msg}");

        $data = ['url' => $url, 'order_no' => $orderNo];
        $this->apiReturn($code, $data, $msg);
    }


    /**
     * 微信支付二维码
     *
     * @params  string      $orderNo    内部支付流水号
     * @params  integer     $fee        金额
     * @params  string      $subject    订单标题
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    private function _getWxPayQrCode(string $orderNo, int $fee, string $subject)
    {
        $code = 200;
        $msg  = '';
        $url  = '';

        if (!$orderNo || !$fee || !$subject) {
            return ['code' => 400, 'msg' => '', 'url' => ''];
        }

        $wxPayLib   = new WxPayLib(PFT_WECHAT_APPID);
        $parameters = $wxPayLib->qrPay(
            $fee,
            $subject,
            $orderNo,
            ModulePayment::WX_NOTICE_NOTIFY_URL,
            '微信二维码支付',
            'orderpay'
        );

        if ($parameters['return_code'] == 'SUCCESS') {
            $url = $parameters['code_url'];
        } else {
            $code = 500;
            $msg  = $parameters['return_msg'];
        }

        return ['code' => $code, 'msg' => $msg, 'url' => $url];
    }


    /**
     * 支付宝支付二维码
     *
    * @params  string      $orderNo    内部支付流水号
     * @params  integer     $fee        金额
     * @params  string      $subject     订单标题
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    private function _getAliPayQrCode(string $orderNo, int $fee, string $subject)
    {
        $code = 200;
        $msg  = '';
        $url  = '';

        if (!$orderNo || !$fee || !$subject) {
            return ['code' => 400, 'msg' => '', $url = ''];
        }

        //支付单位元
        $fee = $fee / 100;
        //生成二维码
        $f2fpay   = new F2FPay(PFT_ALIPAY_F2FPAY_ID);
        $response = $f2fpay->qrpay($orderNo, $fee, $subject, ModulePayment::ALI_NOTICE_NOTIFY_URL, $subject);

        if ($response->alipay_trade_precreate_response->code == 10000 && $response->alipay_trade_precreate_response->msg == 'Success') {
            $url = $response->alipay_trade_precreate_response->qr_code;
        }

        unset($f2fpay);

        if (empty($url)) {
            $code = 500;
            $msg  = "支付宝二维码生成错误, 请刷新页面";
        }

        return ['code' => $code, 'msg' => $msg, 'url' => $url];
    }


    /**
     * 增值服务购买记录
     * @date    2021/07/04
     * <AUTHOR>
     *
     * @return array
     */
    public function buyRecord()
    {
        $vasType  = I('get.vas_type', 1, 'intval');
        $page     = I('get.page', 1, 'intval');
        $pageSize = I('get.page_size', 15, 'intval');

        if (!$vasType) {
            $this->apiReturn(400,"流水号参数错误");
        }

        $buyDetail = $this->_vasBiz->buyRecord($this->_fid, $vasType, $page, $pageSize);
        $this->apiReturn($buyDetail['code'], $buyDetail['data'], $buyDetail['msg']);
    }


    /**
     * 增值服务购买详情
     * @date    2021/07/04
     * <AUTHOR>
     *
     * @return array
     */
    public function buyDetail()
    {
        $tradeNo = I('get.trade_no', 0, 'strval');
        if (!$tradeNo) {
            $this->apiReturn(400,"流水号参数错误");
        }

        $buyDetail = $this->_vasBiz->buyDetail($tradeNo, $this->_fid);
        $this->apiReturn($buyDetail['code'], $buyDetail['data'], $buyDetail['msg']);
    }


    /**
     * 增值服务开通记录
     * @date    2021/07/04
     * <AUTHOR>
     *
     * @return array
     */
    public function openRecord()
    {
        $vasType = I('get.vas_type', 1, 'intval');
        $page     = I('get.page', 1, 'intval');
        $pageSize = I('get.page_size', 15, 'intval');
        $classify = I('get.classify', '', 'string');
        if (!$vasType) {
            $this->apiReturn(400,"流水号参数错误");
        }

        $openRecord = $this->_vasBiz->openRecord($this->_fid, $vasType, $classify,$page, $pageSize);
        $this->apiReturn($openRecord['code'], $openRecord['data'], $openRecord['msg']);
    }

    /**
     * 根据过期时间与状态查询增值记录信息
     * <AUTHOR> lingfeng
     * 2022/8/15 16:41
     */
    public function expireInThreeMonths()
    {
        $result = $this->_vasBiz->expireInThreeMonths($this->_fid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    public function vasClassifyList()
    {
        $isShow  = I('get.is_show', -1, 'intval');
        $classifyName = I('get.classify_name', '', 'string');
        $params = [
            'classify_name' => $classifyName,
            'is_show' => $isShow
        ];
        $result = $this->_vasBiz->vasClassifyList($params);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    public function vasClassifyInfo()
    {
        $classify = I('get.classify', '', 'string');
        if (!$classify) {
            $this->apiReturn(400, '参数错误');
        }
        $params = [
            'classify' => $classify,
        ];
        $result = $this->_vasBiz->vasClassifyInfo($params);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
    /**
     * 购买增值服务
     */
    public function platformBuyAddServiceQr()
    {
        //请求id
        $requestId = str_replace('.', '', microtime(true));
        $cache     = \Library\Cache\Cache::getInstance('redis');

        $memberId  = $this->_loginInfo['memberID'];
        $dtype     = $this->_loginInfo['dtype'];

        if (!in_array($dtype, [0, 1])) {
            $this->apiReturn(203, [], '身份不符,不允许开通');
        }

        //增值服务id
        $vasId   = I('post.vas_id', 0, 'intval');
        //增值服务类型
        $vasType = I('post.vas_type', 1, 'intval');
        //支付方式
        $payWay  = I('post.pay_way', 0, 'intval');
        $parentId  = I('post.parent_id', 0, 'intval');
        //服务开始生效时间
        $startDate = I('post.start_date', '', 'strval');
        //服务失效时间
        $endDate   = I('post.end_date', 0, 'strval');

        if (!$vasId || !$vasType) {
            $this->apiReturn(203, [], '增值服务相关参数错误');
        }

        if (empty($payWay) || !isset($this->_payTypeMap[$payWay])) {
            $this->apiReturn(203, [], '支付方式参数错误');
        }

        $startTime = strtotime($startDate);
        $endTime   = strtotime($endDate);

        if (!$startTime || !$endTime || ($endTime - $startTime) < 0) {
            $this->apiReturn(203, [], '时间参数错误');
        }

        $endTime += 86399;

        //支付方式描述
        $payWayStr = $this->_payTypeMap[$payWay];

        //加锁 防止恶意请求
        $locky    = "pay_combine:{$payWayStr}:{$requestId}:$memberId";
        $lock_ret = $cache->lock($locky, 1, 120);
        if (!$lock_ret) {
            $this->apiReturn(203, [], '请求正在处理中，请稍后');
        }

        try {
            //开通前的检测  走新工程appcenterService
            $checkRes = $this->_vasBiz->openPreCheck($memberId, $dtype, $vasId, $vasType, $payWay, $startTime, $endTime);
            if ($checkRes['code'] != 200) {
                throw new \Exception($checkRes['msg']);
            }

            ///标题
            $subject = $checkRes['data']['subject'];
            //订单号
            $orderNo = $checkRes['data']['trade_no'];
            //费用 微信以分为单位
            $fee    = $checkRes['data']['fee'];

            $tmpRemarks = [
                'member_id'    => $memberId,
                'vas_id'       => $vasId,
                'vas_type'     => $vasType,
                'fee'          => $fee,
                'subject'      => $subject,
                'start_time'   => $startTime,
                'end_time'     => $endTime,
                'pay_type'     => 4,    //标识是购买增值服务
                'pay_way'      => $payWay,   //1支付宝 2微信 3余额
                'parent_id'    => $parentId,
            ];

            $remarks = json_encode($tmpRemarks);

            $data = [
                'outTradeNo' => $orderNo,
                'qrUrl'      => '',
            ];

            if ($payWay == 3) {
                $handleAfterOpen = true;
                //平台账户余额支付
                $payRes = (new PaymentBiz())->platformPayModule($orderNo, $fee, $memberId, $subject);
                if ($payRes['code'] != 200) {
                    throw new \Exception($payRes['msg']);
                }

                //开通
                $openRes = $this->_vasBiz->openVas($memberId, $orderNo, $vasId, $vasType, $payWay, $startTime, $endTime);
                //如果开通失败
                if ($openRes['code'] != 200) {
                    //记录余额支付异常日志
                    throw new \Exception($openRes['msg']);
                }
            } else {
                //默认支付方式：支付宝
                $sourceT = 0;
                $onLineTrade = new OnlineTrade();
                //微信支付或者支付宝支付
                if (defined('ENV') && in_array(ENV, ['PRODUCTION', 'TEST'])) {

                    // 调用支付中心收银台页面
                    $unifiedPay  = new UnifiedPay();
                    $rpcResponse = $unifiedPay->unifyQrPayRpcService($orderNo, $subject, $fee, 1, $payWay,
                        'platform', self::VAS_NOTIFY_URL, $_SERVER['REMOTE_ADDR'], '',
                        ['pft_member_id' => $memberId,'source' => 'platform_add_service']);

                    $ret   = $onLineTrade->addLog($orderNo, $fee, $subject, $remarks, self::VAS_SOURCET, OnlineTrade::PAY_METHOD_RECHARGE);
                    if (!$ret) {
                        throw new \Exception('记录发生错误,请联系客服人员');
                    }

                    if ($rpcResponse['code'] != 200) {
                        throw new \Exception($rpcResponse['msg']);
                    }

                    $data = [
                        'outTradeNo' => $orderNo,
                        'qrUrl'      => $rpcResponse['data']['url'],
                    ];
                } else {
                    //测试环境，模拟成功支付
                    $create = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception('充值记录生成失败');
                    }

                    //模拟支付成功
                    $tradeNo = 'online_' . time();
                    $res     = (new PaymentBiz())->onlinePayVas($orderNo, $tmpRemarks, $tradeNo, 2);
                    if ($res['code'] != 200) {
                        throw new \Exception($res['msg']);
                    }
                }
            }

        } catch (\Exception $e) {
            $cache->lock_rm($locky);
            $code = self::CODE_INVALID_REQUEST;
            $msg  = $e->getMessage();
            $this->apiReturn($code, [], $msg);
        }

        $this->apiReturn(200, $data);
    }

    /**
     * 收银台支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $this->isLogin('ajax');
        $ordernum = I('post.order_no');
        pft_log('yeepay/pay_result_check', json_encode($_POST, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        $model = new OnlineTrade();

        $payLog = $model->getLog($ordernum, self::VAS_SOURCET);
        if (!$payLog) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
        }

        if ($payLog['status'] == 1) {
            parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看', true);
    }

}