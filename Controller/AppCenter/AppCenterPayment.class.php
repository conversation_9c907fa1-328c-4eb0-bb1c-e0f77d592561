<?php
/**
 * 应用中心支付
 * <AUTHOR>
 * @date 2021-09-10
 */

namespace Controller\AppCenter;

use Library\Controller;
use Library\Cache\Cache;
use Model\TradeRecord\OnlineTrade;
use Business\AppCenter\Payment as PaymentBiz;
use Business\AppCenter\Module as ModuleBiz;
use Business\AppCenter\ModuleCombine as ModuleCombineBiz;

class AppCenterPayment extends Controller
{
    //** 日志相关地址 ************************************************
    const APPCENTER_PAY_LOG_DEBUG   = 'appcenter/payment/debug';
    const APPCENTER_PAY_LOG_SUCCESS = 'appcenter/payment/success';
    const APPCENTER_PAY_LOG_ERROR   = 'appcenter/payment/error';
    const LOG_DEBUG                 = 'debug';
    const LOG_SUCCESS               = 'success';
    const LOG_ERROR                 = 'error';

    //** 返回code ****************************************************
    const BACK_CODE_SUCCESS      = 200; //成功
    const BACK_CODE_PARAMS_ERROR = 203; //参数错误
    const BACK_CODE_STOP_ERROR   = 400; //逻辑错误
    const BACK_CODE_NOT_WX_AUTH  = 206; //微信未获取openid

    //** 支付回调 ****************************************************
    const NOTICE_NOTIFY_URL = PAY_DOMAIN . '/r/AppCenter_AppCenterPayment/afterPay'; //回调地址

    //** 支付相关 ****************************************************
    const CURL_PORT    = 80; //请求端口
    const CURL_TIMEOUT = 30; //超时时间 秒
    const PAY_TYPE_WX  = 2;  //支付类型 微信
    const PAY_TYPE_ZFB = 1; //支付类型 支付宝

    /**
     * 信公众号和小程序内支付（jsapi支付） 请求参数
     * order_id    string     业务方订单号
     * money    int    支付金额，单位为分
     * pay_type    ​int    ​支付渠道，2微信，当前仅支持微信
     * notify_url    string    服务端回调地址（业务方接收支付成功后的地址）
     * subject    string    订单信息描述
     * success_url    string    前端回调地址，可以为空
     * order_expire    int    订单超时的时间，单位是秒；eg:超时时间30分钟，那么传1800——2021/08/26新增
     * attach    string(127)    用户自定义数据，回调通知原样返回——2021/08/26新增
     * merchant_id    int    供应商ID，可以为空，如果独立收款，必须要传这个参数
     * open_id    string    用户的openid，公众号或小程序授权后得到的
     * appid    string  公众号或小程序的appid，可以为空，如果独立收款，必须要传这个参数 默认为“票付通”的appid(wxd72be21f7455640d)
     */
    const JSAPI_PAY_URL = PAY_DOMAIN . 'r/pay_MobilePay/jsApiPay';

    /**
     * 主扫支付 请求参数
     * order_id    string     业务方订单号
     * money    int    支付金额，单位为分
     *​pay_type    ​int    ​支付渠道，1支付，2微信
     *​notify_url    string    服务端回调地址（业务方接收支付成功后的地址）
     *​subject    string    订单信息描述
     *​success_url    string    前端回调地址，可以为空
     *​merchant_id    int    供应商ID，可以为空，如果独立收款，需要传这个参数
     *
     * return {"code":200,"data":{"outTradeNo":"订单号","qrUrl":"扫码支付的地址"},"msg":""}
     */
    const QR_PAY_URL = PAY_DOMAIN . 'r/pay_MobilePay/qrPay';

    const JSAPI_SOURCE = ['microplat']; //走微信js支付
    const QRUEL_SOURCE = ['platform']; //走二维码支付

    //支付回调参数校验 验证签名
    const NOTICE_PAY_CONF = [
        'f0ec96ad2c3848b5b810e7aadf369e2g' => [
            'appid'  => 'f0ec96ad2c3848b5b810e7aadf369e2g',
            'appkey' => '775481e2556e4564985f5439a5e6a27g',
        ],
    ];//目前全部环境共用这个id和key

    //** 其他参数 ****************************************************
    private $_fid;

    private $_dtype;

    private $_payWay;

    private $_source;

    private $_payWayMap;

    private $_openType;

    private $_loginInfo;

    private $_payWayToModel;

    private $_getRedisObject;

    public function __construct()
    {
        //注：不要在这里获取用户信息，这个类有支付回调接口，获取用户信息 调loginInfo()方法
        //支付方式
        $this->_payWayMap = [
            1 => '平台账户余额支付',
            2 => '微信支付',
            3 => '支付宝支付',
        ];
        //开通功能类型
        $this->_openType = [
            'module',
        ];
        //支付方式转换
        $this->_payWayToModel = [
            1 => 1,     //平台支付
            2 => 3,     //支付宝
            3 => 2,     //微信
        ];
    }

    /**
     * 获取登录信息
     * <AUTHOR>
     * @date 2021/9/15
     *
     * @return bool
     */
    private function loginInfo()
    {
        //登录信息
        $this->_loginInfo = $this->getLoginInfo();

        //用户id
        $this->_fid = $this->_loginInfo['memberID'];

        //用户类型
        $this->_dtype = $this->_loginInfo['dtype'];

        return true;
    }

    /**
     * 支付回调处理
     * <AUTHOR>
     * @date 2021/9/15
     *
     */
    public function afterPay()
    {
        $params = file_get_contents('php://input');
        self::addLog(['应用中心支付回调通知', $params], self::LOG_DEBUG);
        $params  = json_decode(html_entity_decode($params), true);
        $content = $params['content'] ?? [];
        $appId   = $content['appId'] ?? '';

        //验证参数
        if (empty($content) || empty($appId) || !isset(self::NOTICE_PAY_CONF[$appId]['appkey'])) {
            self::addLog(['应用中心支付回调参数错误', $params], self::LOG_ERROR);
            $this->apiReturn(200);
        }

        //验证签名
        $appKey = self::NOTICE_PAY_CONF[$appId]['appkey'];
        if (!$this->decryptNotifyData($content, $appKey)) {
            self::addLog(['验证签名错误', $params], self::LOG_ERROR);
            $this->apiReturn(200);
        }

        //业务参数
        $data        = json_decode($content['content'], true);
        $outTradeNo  = $data['ordernum'] ?? '';
        $money       = $data['amount'] ?? '';
        $tradeNo     = $data['tradeno'] ?? '';
        $subjectCode = $data['subject_code'] ?? '';
        $payStatus   = $data['status'] ?? '';

        if ($tradeNo === '' || $money === '' || $outTradeNo === '' || $subjectCode === '' || $payStatus === '') {
            self::addLog(['应用中心支付回调业务参数错误', $params], self::LOG_ERROR);
            $this->apiReturn(200);
        }

        if ($payStatus != 'SUCCESS') {
            self::addLog(['支付失败', $params], self::LOG_ERROR);
            $this->apiReturn(200);
        }

        //支付金额 单位：分
        $payTotalFee = (int)$money;

        //开通前的检测
        $onLineTradeModel = new OnlineTrade();
        $logInfo          = $onLineTradeModel->getLogByOrderId($outTradeNo);

        if (empty($logInfo)) {
            self::addLog(['找不到记录', $outTradeNo], self::LOG_ERROR);
            $this->apiReturn(200);
        }

        if ($logInfo['status'] == 0) {
            self::addLog(['该笔订单未支付', $outTradeNo], self::LOG_ERROR);
            $this->apiReturn(200);
        }

        if ($logInfo['total_fee'] * 100 != $payTotalFee) {
            self::addLog(['金额不正确', $logInfo['total_fee'], $payTotalFee], self::LOG_ERROR);
            $this->apiReturn(200);
        }

        $payInfo = @json_decode($logInfo['description'], true);
        if (!$payInfo || !is_array($payInfo)) {
            self::addLog(['支付信息错误', $outTradeNo, $tradeNo], self::LOG_ERROR);
            $this->apiReturn(200);
        }

        $paymentBiz = new PaymentBiz();

        $res = ['code' => 400];

        $payWay   = $payInfo['pay_way'] ?? 2;//默认展示微信支付
        $payModel = $this->_payWayToModel[$payWay];

        if ($payInfo['pay_type'] == 1) {
            //开通应用
            $res = $paymentBiz->onlinePayModule($outTradeNo, $payInfo, $tradeNo, $payModel);
        } elseif ($payInfo['pay_type'] == 2) {
            //开通套餐
            $res = $this->_openPackage($outTradeNo, $payInfo, $tradeNo, $payModel);

        } elseif ($payInfo['pay_type'] == 3) {
            //应用组合
            $res = $this->_openCombine($outTradeNo, $payInfo, $tradeNo, $payModel);

        } elseif ($payInfo['pay_type'] == 6) {
            //开通资源包
            $res = $paymentBiz->onLinePayResourcePack($outTradeNo, $payInfo, $tradeNo, $payModel);
        }

        if ($res['code'] == 200) {
            self::addLog(['开通成功', $outTradeNo, $tradeNo], self::LOG_SUCCESS);
        } else {
            self::addLog(['开通失败', $outTradeNo, $tradeNo, $res], self::LOG_ERROR);
        }

        $this->apiReturn(200);
    }

    /**
     * 应用中心在线支付 通用入口
     * <AUTHOR>
     * @date 2021/9/13
     *
     */
    public function buyOnLine()
    {
        $code = 200;
        $msg  = '';
        $data = [];

        $result   = '';
        $openType = '';
        $orderNo  = '';
        $subject  = '';

        $requestStr = '';

        $source = 'platform';

        try {
            $this->loginInfo();

            $memberId = $this->_loginInfo['memberID'];
            $dtype    = $this->_loginInfo['dtype'];

            if (!in_array($dtype, [0, 1])) {
                throw new \Exception("身份不符,不允许开通");
            }

            $request    = I('post.');
            $requestStr = is_array($request) ? json_encode($request) : $request;

            //支付方式
            $payWay = I('post.pay_way', 0, 'intval');
            //来源
            $source = I('post.source', 'platform'); //来源：platform平台
            //资源中心推广来源
            $resourceCenterSpreadChannel =I('post.rc_spread_channel', 0, 'intval');//资源中心推广渠道标识为1-127
            //开通类型
            $openType = I('post.open_type', 'module'); //开通功能类型

            if (empty($openType) || !in_array($openType, $this->_openType)) {
                throw new \Exception("开通类型错误");
            }

            if (empty($payWay) || !isset($this->_payWayMap[$payWay])) {
                throw new \Exception("支付方式参数错误");
            }

            $source = $source ?: 'platform';

            $this->_source = $source;

            $this->_payWay = $payWay;

            //支付方式描述
            $payWayStr = $this->_payWayMap[$payWay];

            //开通申请 日志
            $openLog = ["申请开通：用户ID：{$memberId}", "请求参数：{$requestStr}", "支付方式: {$payWayStr}"];
            self::addLog($openLog, self::LOG_DEBUG);

            //加锁 防止恶意请求
            $this->_checkBuyLock($memberId);

            $preCheckData = $this->_openBefore($openType);

            $remarks = $preCheckData['remarks'];
            $orderNo = $preCheckData['order_no'];
            $fee     = $preCheckData['fee'];
            $subject = $preCheckData['subject'];

            //来源判断
            $remarks['source'] = $source;
            //资源中心推广渠道标识
            $remarks['rc_spread_channel'] = $resourceCenterSpreadChannel;

            if (empty($orderNo) || empty($subject) || !is_numeric($fee) || empty($remarks)) {
                throw new \Exception("订单生成出错, 请重试");
            }

            $data['order_no'] = $orderNo;

            if ($payWay == 1) {
                //平台账户余额支付
                $paymentBiz = new PaymentBiz();
                $payRes     = $paymentBiz->platformPayModule($orderNo, $fee, $memberId, $subject);
                if ($payRes['code'] != 200) {
                    throw new \Exception($payRes['msg']);
                }

                $openRes = ['code' => 400, 'msg' => '未知开通类型'];

                //开通模块
                if ($openType == 'module') {
                    $openRes = (new ModuleBiz())->setNotifyParams($resourceCenterSpreadChannel)->openModule($memberId, $remarks['module_data'], $orderNo, $source);
                }

                //如果开通失败
                if ($openRes['code'] != 200) {
                    //记录余额支付异常日志
                    self::addLog(["余额支付失败", $memberId, $requestStr, $orderNo], self::LOG_ERROR);
                    throw new \Exception($openRes['msg']);
                }

                $result .= '平台余额支付，';

                //旧版钉钉通知
                (new \Business\AppCenter\BaseCall())->dingdingSendOpenMsg($memberId, $subject, $orderNo,
                    ($fee > 0 ? 0 : 1), 1); //开通应用 余额

            } else {//微信支付或者支付宝支付
                //金额限制
                if ($fee <= 0) {
                    throw new \Exception("金额错误, 请重试");
                }
                //默认支付方式：支付宝
                if (defined('ENV') && in_array(ENV, ['PRODUCTION', 'TEST'])) {

                    if ($payWay == 2) {//微信
                        $getPay = $this->_getWxPay($orderNo, $fee, $subject, $remarks);

                        $result .= '生成微信支付，';
                    }

                    if ($payWay == 3) {//支付宝
                        $getPay = $this->_getAliPay($orderNo, $fee, $subject, $remarks);

                        $result .= '生成支付宝支付，';
                    }

                    if (empty($getPay['code']) || $getPay['code'] != 200) {
                        throw new \Exception($getPay['msg']);
                    }

                    if (in_array($source, self::QRUEL_SOURCE)) {
                        //走二维码支付
                        $data['url'] = $getPay['data']['qrUrl'] ?? '';
                    } else if (in_array($source, self::JSAPI_SOURCE)) {
                        //走微信jsapi支付
                        $data             = $getPay['data'] ?? [];
                        $data['order_no'] = $orderNo;
                    }

                } else {

                    //测试环境，模拟成功支付
                    $this->_analogPay($memberId, $openType, $subject, $fee, $orderNo, $remarks);

                    $result .= '测试模拟支付，';
                }
            }

        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
            $this->_dingdingMsg($memberId, $openType, $orderNo, $subject, $msg);
        }

        //移除锁
        $this->_rmBuyLock($memberId);

        //开通结果 日志
        if ($code == 200) {
            $result .= '成功';
        } else {
            $result .= '失败';
            $msg    = empty($msg) ? $result : $msg;
        }

        $fee = ($fee ?? 0) / 100;

        //记录最后的参数值
        self::addLog([
            "结果记录",
            "用户ID：{$memberId}",
            "请求参数：{$requestStr}",
            "来源：{$source}",
            "费用：{$fee}",
            "流水号：{$orderNo}",
            "结果：{$result}",
            "原因：{$msg}",
        ], self::LOG_DEBUG);

        $this->apiReturn($code, $data, $msg);
    }

    /**
     *  解密 sha256加密
     * @author: Guangpeng Chen
     * @date: 2020/11/12
     *
     * @param $params array 待加密的数据
     * @param $appkey string 解密key
     *
     * @return bool
     */
    public function decryptNotifyData(array $params, string $appkey)
    {
        $signature = $params['signature'];
        unset($params['signature']);
        $sign       = bin2hex(hash('sha256', $params['content'], true));
        $signStr    = $params['appId'] . $params['timestamp'] . $params['nonce'] . $sign;
        $signResult = base64_encode(hash_hmac('sha256', $signStr, $appkey, true));
        if ($signature == $signResult) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 开通前置判断
     * <AUTHOR>
     * @date 2021/9/10
     *
     * @param  string  $openType  开通类型
     *
     * @return array
     * @throws
     */
    private function _openBefore(string $openType = 'module')
    {
        $checkRes = [];
        switch ($openType) {
            case 'module' :
                $checkRes = $this->_modulePreCheck();
                break;

            case 'package' :
                //套餐购买判断
                break;
        }

        return $checkRes;
    }

    /**
     * 应用前置判断
     * <AUTHOR>
     * @date 2021/9/10
     *
     * @return array
     * @throws
     */
    private function _modulePreCheck()
    {
        $memberId = $this->_fid;
        $dtype    = $this->_dtype;
        $payWay   = $this->_payWay;

        //开通数据
        $moduleData = I('post.module_data', []);
        if (empty($moduleData)) {
            throw new \Exception("请求参数错误");
        }
        $formatData = [];
        foreach ($moduleData as $tmpModule) {
            if (empty($tmpModule['appid']) || !is_numeric($tmpModule['priceid'])) {
                throw new \Exception("请求参数错误:" . json_encode($moduleData));
            }

//            //同业平台分销收费版和免费版开通限制
//            $sameRes = (new PaymentBiz())->_checkSameBusiness($memberId, (int)$tmpModule['appid'], 1);
//            if ($sameRes['code'] != 200) {
//                throw new \Exception($sameRes['msg'], $sameRes['code']);
//            }

            $formatData[] = ['moduleId' => $tmpModule['appid'], 'priceId' => $tmpModule['priceid']];
        }

        //开通模块前的数据检测  走新工程appcenterService
        $checkRes = (new ModuleBiz())->openPreCheck($this->_loginInfo['sid'], $dtype, $formatData, $payWay);
        if ($checkRes['code'] != 200) {
            throw new \Exception($checkRes['msg']);
        }

        //订单号
        $orderNo = $checkRes['data']['order_no'];
        //费用 微信以分为单位
        $fee = $checkRes['data']['fee'];
        //标题
        $subject = $checkRes['data']['subject'];

        if (empty($orderNo) || !is_numeric($fee)) {
            throw new \Exception("订单生成出错, 请重试");
        }

        $tmpRemarks = [
            'out_trade_no' => $orderNo,
            'member_id'    => $memberId,
            'module_data'  => $formatData,
            'fee'          => $fee,
            'subject'      => $subject,
            'pay_type'     => 1,    //标识是购买模块
            'pay_way'      => $this->_payWay,//支付方式标识
        ];

        return $this->_preCheckFormat($orderNo, $fee, $subject, $tmpRemarks);
    }

    /**
     * 前置返回数据格式
     * <AUTHOR>
     * @date 2021/9/10
     *
     * @param  string  $orderNo  订单号
     * @param  int  $fee  金额 单位分
     * @param  string  $subject  订单描述
     * @param  array  $remarks  备注信息
     *
     * @return array
     * @throws
     */
    private function _preCheckFormat(string $orderNo, int $fee, string $subject, array $remarks)
    {
        if (empty($orderNo) || empty($subject) || !is_numeric($fee) || empty($remarks)) {
            throw new \Exception("订单生成出错, 请重试");
        }

        return [
            'order_no' => $orderNo,
            'fee'      => $fee,
            'subject'  => $subject,
            'remarks'  => $remarks,
        ];
    }

    /**
     * 模拟支付
     * <AUTHOR>
     * @date 2021/9/10
     *
     * @param  int  $memberId  用户id
     * @param  string  $openType  开通类型
     * @param  string  $subject  订单描述
     * @param  int  $fee  金额
     * @param  string  $orderNo  订单号
     * @param  array  $remarks  备注信息
     *
     * @return array
     * @throws
     */
    private function _analogPay(int $memberId, string $openType, string $subject, int $fee, string $orderNo, array $remarks)
    {
        //测试环境，模拟成功支付 模拟支付：支付宝
        $onLineTrade = new OnlineTrade();
        $create      = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', 0, json_encode($remarks));

        if (empty($create)) {
            throw new \Exception("充值记录生成失败");
        }

        $res = ['code' => 400, 'msg' => '模拟支付失败'];

        //模拟支付成功
        if ($openType == 'module') {
            $paymentBiz = new PaymentBiz();
            $tradeNo    = 'online_' . time();
            $res        = $paymentBiz->onlinePayModule($orderNo, $remarks, $tradeNo, 2);
        }

        if ($openType == 'package') {
            //套餐模拟支付
        }

        if ($res['code'] != 200) {
            throw new \Exception($res['msg']);
        }

        return $res;
    }

    /**
     * 微信支付
     * <AUTHOR>
     * @date 2021/9/15
     *
     * @param  string  $orderNo  内部支付流水号
     * @param  int  $fee  金额
     * @param  string  $subject  订单标题
     * @param  array  $remarks  详情信息
     *
     * @return  array
     */
    private function _getWxPay(string $orderNo, int $fee, string $subject, array $remarks)
    {
        $code = 200;
        $msg  = '';
        $data = [];

        if (!$orderNo || !$fee || !$subject) {
            return ['code' => 400, 'msg' => '', 'url' => ''];
        }
        $notifyUrl = self::NOTICE_NOTIFY_URL;
        $payType   = 2;

        $parameters = ['code' => 400, 'msg' => '支付方式错误'];

        if (in_array($this->_source, self::QRUEL_SOURCE)) {
            //走二维码支付
            $parameters = $this->_orderPayByQr($fee, $orderNo, $payType, $subject, $notifyUrl, $remarks);
        } else if (in_array($this->_source, self::JSAPI_SOURCE)) {
            //走微信jsapi支付
            $openId = I('post.pft_openid', ''); //微信openId
            if (empty($openId)) {
                return ['code' => 400, 'msg' => '用户参数缺失'];
            }
            $parameters = $this->_orderPayByJsapi($fee, $orderNo, $subject, $openId, $notifyUrl, $remarks);
        }

        if ($parameters['code'] == 200) {
            $data = $parameters['data'];
        } else {
            $code = 500;
            $msg  = $parameters['msg'];
        }

        return ['code' => $code, 'msg' => $msg, 'data' => $data];
    }

    /**
     * 支付宝支付
     * <AUTHOR>
     * @date 2021/9/15
     *
     * @param  string  $orderNo  内部支付流水号
     * @param  int  $fee  金额
     * @param  string  $subject  订单标题
     * @param  array  $remarks  详情信息
     *
     *
     * @return array
     */
    private function _getAliPay(string $orderNo, int $fee, string $subject, array $remarks)
    {
        $code = 200;
        $msg  = '';
        $data = [];

        if (!$orderNo || !$fee || !$subject) {
            return ['code' => 400, 'msg' => '', $url = ''];
        }

        $notifyUrl = self::NOTICE_NOTIFY_URL;
        $payType   = 1;

        $parameters = ['code' => 400, 'msg' => '支付方式错误'];

        if (in_array($this->_source, self::QRUEL_SOURCE)) {
            //走二维码支付
            $parameters = $this->_orderPayByQr($fee, $orderNo, $payType, $subject, $notifyUrl, $remarks);
        } else if (in_array($this->_source, self::JSAPI_SOURCE)) {
            return ['code' => 400, 'msg' => '暂不支持支付宝支付'];
        }

        if ($parameters['code'] == 200) {
            $data = $parameters['data'];
        } else {
            $code = 500;
            $msg  = $parameters['msg'];
        }

        return ['code' => $code, 'msg' => $msg, 'data' => $data];
    }

    /**
     * 微信jsapi支付
     * <AUTHOR>
     * @date 2021/9/8
     *
     * @param  int  $totalFee  总金额 单位分
     * @param  string  $outTradeNo  平台订单号
     * @param  string  $subject  订单描述
     * @param  string  $openid  微信openid
     * @param  string  $notifyUrl  回调通知地址
     * @param  string  $attach  扩展信息
     * @param  int  $orderExpire  过期时间
     * @param  array  $detail  详情
     *
     * @return array
     */
    private function _orderPayByJsapi(
        int $totalFee,
        string $outTradeNo,
        string $subject,
        string $openid,
        string $notifyUrl = '',
        array $detail = [],
        string $attach = '',
        int $orderExpire = 7200
    )
    {
        if (!$totalFee || empty($outTradeNo) || empty($subject) || empty($openid)) {
            self::addLog(func_get_args(), self::LOG_ERROR);

            return ['code' => self::BACK_CODE_PARAMS_ERROR, 'msg' => '支付发起参数错误'];
        }
        $payType = 2; //仅支持微信支付
        $params  = [
            'order_id'     => $outTradeNo,
            'money'        => $totalFee,
            'pay_type'     => $payType,
            'notify_url'   => $notifyUrl ?: self::NOTICE_NOTIFY_URL,
            'subject'      => $subject,
            'attach'       => $attach,
            'open_id'      => $openid,
            'order_expire' => $orderExpire,
            'detail'       => json_encode($detail),
        ];
        $result  = self::_payCurl(self::JSAPI_PAY_URL, $params);

        $msg  = $result['msg'];
        $code = $result['code'];
        $data = $result['data'] ?? [];

        return ['code' => $code, 'msg' => $msg, 'data' => $data];
    }

    /**
     * 主扫支付/二维码支付
     * <AUTHOR>
     * @date 2021/9/10
     *
     * @param  int  $totalFee  金额 单位分
     * @param  string  $outTradeNo  业务订单号
     * @param  int  $payType  支付渠道 1支付宝 2微信
     * @param  string  $subject  订单描述信息
     * @param  string  $notifyUrl  回调地址
     * @param  array  $detail  详情
     *
     * @return array
     */
    private function _orderPayByQr(
        int $totalFee,
        string $outTradeNo,
        int $payType,
        string $subject,
        string $notifyUrl = '',
        array $detail = []
    )
    {
        if (!$totalFee || empty($outTradeNo) || !$payType || empty($subject)) {
            self::addLog(func_get_args(), self::LOG_ERROR);

            return ['code' => self::BACK_CODE_PARAMS_ERROR, 'msg' => '支付发起参数错误'];
        }
        $params = [
            'order_id'   => $outTradeNo,
            'money'      => $totalFee,
            'pay_type'   => $payType,
            'notify_url' => $notifyUrl ?: self::NOTICE_NOTIFY_URL,
            'subject'    => $subject,
            'detail'     => json_encode($detail),
        ];

        $result = self::_payCurl(self::QR_PAY_URL, $params);

        $msg  = $result['msg'];
        $code = $result['code'];
        $data = $result['data'] ?? [];

        return ['code' => $code, 'msg' => $msg, 'data' => $data];
    }

    /**
     * 获取redis实例
     * <AUTHOR>
     * @date 2021/9/10
     *
     * @return object
     */
    private function _getRedis()
    {
        if (!$this->_getRedisObject) {
            $this->_getRedisObject = Cache::getInstance('redis');
        }

        return $this->_getRedisObject;
    }

    /**
     * 获取请求ID
     * <AUTHOR>
     * @date 2021/9/10
     *
     * @return mixed
     */
    private function _getRequestId()
    {
        return str_replace('.', '', microtime(true));
    }

    /**
     * 获取在线支付锁名
     * <AUTHOR>
     * @date 2021/9/10
     *
     * @param  int  $memberId  用户id
     *
     * @return string
     */
    private function _getLockKey(int $memberId)
    {
        $payWayStr = $this->_payWayMap[$this->_payWay];
        $requestId = $this->_getRequestId();
        $locky     = "pay_module:{$payWayStr}:{$requestId}:$memberId";

        return $locky;
    }

    /**
     * 检测支付锁
     * <AUTHOR>
     * @date 2021/9/10
     *
     * @param  int  $memberId  用户id
     *
     * @return bool
     * @throws
     */
    private function _checkBuyLock(int $memberId)
    {
        $key = $this->_getLockKey($memberId);

        $lock_ret = $this->_getRedis()->lock($key, 1, 120);
        if (!$lock_ret) {
            self::addLog([$key, "操作频繁"], self::LOG_DEBUG);

            throw new \Exception("请求正在处理中，请稍后");
        };

        return true;
    }

    /**
     * 移除支付锁
     * <AUTHOR>
     * @date 2021/9/10
     *
     * @param  int  $memberId  用户id
     *
     * @return bool
     */
    private function _rmBuyLock(int $memberId)
    {
        $key = $this->_getLockKey($memberId);
        if (!empty($key)) {
            $this->_getRedis()->rm($key);
        }

        return true;
    }

    /**
     * 钉钉消息
     * <AUTHOR>
     * @date 2021/9/13
     *
     * @param $memberId
     * @param $openType
     * @param $orderNo
     * @param $subject
     * @param  string  $msg
     *
     * @return bool
     */
    private function _dingdingMsg($memberId, $openType, $orderNo, $subject, $msg = '')
    {
        if (empty($memberId) || empty($openType) || empty($orderNo)) {
            return true;
        }

        //异常报警
        if (in_array(ENV, ['PRODUCTION'])) {

            $queryParams = [[$memberId]];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                $queryParams);
            $memberName  = '';
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $memberName = array_column($queryRes['data'], null, 'id')[$memberId]['dname'];
            }
            $message   = [];
            $message[] = "用户ID:{$memberName}({$memberId})";
            $message[] = "开通类型: {$openType}";
            if (!empty($orderNo)) {
                $message[] = "订单号{$orderNo}";
            }
            if (!empty($subject)) {
                $message[] = "{$subject}";
            }
            $message[] = "{$msg}";
            (new ModuleBiz())->dingdingSendMsg($message);
        }

        return true;
    }

    /**
     * CURL触发支付
     * <AUTHOR>
     * @date 2021/9/10
     *
     * @param  string  $url  连接
     * @param  array  $params  参数
     *
     * @return array
     */
    protected static function _payCurl(string $url, array $params)
    {
        if (empty($url) || empty($params)) {
            return ['code' => self::BACK_CODE_PARAMS_ERROR, 'msg' => '支付参数缺失'];
        }

        $params = http_build_query($params);
        $result = curl_post($url, $params, self::CURL_PORT, self::CURL_TIMEOUT);

        $resultArr = @json_decode($result, true);
        if (empty($resultArr)) {
            self::addLog([$params, $result], self::LOG_ERROR);

            return ['code' => self::BACK_CODE_STOP_ERROR, 'msg' => '支付发起失败'];
        } else {
            $msg  = $resultArr['msg'] ?? '';
            $data = $resultArr['data'] ?? [];
            if (isset($resultArr['code']) && $resultArr['code'] == self::BACK_CODE_SUCCESS) {
                $code = self::BACK_CODE_SUCCESS;
            } else {

                $code = self::BACK_CODE_STOP_ERROR;
                $msg  = '支付发起失败，参数缺失';
                $data = [];
            }

            self::addLog([$params, $result], self::LOG_DEBUG);
        }

        return ['code' => $code, 'msg' => $msg, 'data' => $data];
    }

    /**
     * 统一添加日志
     * <AUTHOR>
     * @date 2021/9/10
     *
     * @param  array  $log  日志数据
     * @param  string  $type  日志类型
     *
     * @return bool
     */
    protected static function addLog(array $log, string $type = self::LOG_DEBUG)
    {
        if (empty($log) || empty($type)) {
            return true;
        }
        $data = json_encode($log, JSON_UNESCAPED_UNICODE);
        switch ($type) {
            case self::LOG_DEBUG :
                pft_log(self::APPCENTER_PAY_LOG_DEBUG, $data);
                break;
            case self::LOG_SUCCESS:
                pft_log(self::APPCENTER_PAY_LOG_SUCCESS, $data);
                break;
            case self::LOG_ERROR:
                pft_log(self::APPCENTER_PAY_LOG_ERROR, $data);
                break;
        }

        return true;
    }

    /**
     * 开通应用
     *
     * <AUTHOR>
     * @date   2016-12-14
     *
     * @params $price_id  资费ID
     * @params $module_id 模块ID
     */
    private function _openPackage(string $outTradeNo, array $payInfo, string $tradeNo, int $payModel = 2)
    {
        //写流水
        $payRes = (new PaymentBiz)->onlinePayPackage($outTradeNo, $payInfo, $tradeNo, $payModel);
        if ($payRes['code'] != 200) {
            return $payRes;
        }

        //开通套餐
        $packageData = $payInfo['package_data'];
        $memberId    = $payInfo['member_id'];
        $packageId   = $packageData['package_id'];
        $packageBiz  = new \Business\AppCenter\Package();

        $openRes = $packageBiz->openPackage($memberId, $packageId, $packageData['price_mode'],
            $packageData['product_type'], $outTradeNo, $packageData['sale_info']);

        if ($openRes['code'] != 200) {
            return $openRes;
        }

        $openData  = $openRes['data'];
        $handleRes = $packageBiz->handleAfterOpen($memberId, $packageId, $packageData['role'], $openData['begin_time'],
            $openData['end_time'], $payInfo['fee']);

        if ($handleRes['code'] != 200) {
            return $handleRes;
        }

        return true;
    }

    /**
     * 开通应用组合
     *
     * <AUTHOR>
     * @date   2021-06-22
     *
     * @params $price_id  资费ID
     * @params $module_id 模块ID
     */
    private function _openCombine(string $outTradeNo, array $payInfo, string $tradeNo, $payType)
    {
        $payRes = (new PaymentBiz)->onlinePayCombine($outTradeNo, $payInfo, $tradeNo, $payType);
        if ($payRes['code'] != 200) {
            return $payRes;
        }

        $combineData = $payInfo['combine_data'];
        $memberId    = $payInfo['member_id'];
        $combineId   = $combineData['combine_id'];
        $priceMode   = $combineData['price_mode'];

        $openRes = (new ModuleCombineBiz())->openCombine($memberId, $combineId, $priceMode, $outTradeNo);
        if ($openRes['code'] != 200) {
            return $openRes;
        }
    }
}