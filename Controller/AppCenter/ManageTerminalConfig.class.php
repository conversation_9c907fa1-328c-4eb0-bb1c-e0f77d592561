<?php
/**
 * 智能终端
 *
 *
 * <AUTHOR>
 * @date   2020-07-10
 */

namespace Controller\AppCenter;
use Library\Controller;
use Business\AppCenter\Service as ServiceBiz;

class ManageTerminalConfig extends Controller
{

    private $_loginInfo;
    private $_serviceBiz;

    private $_isAdmin = false;

    public function __construct()
    {
        $this->_loginInfo  = $this->getLoginInfo();
        $this->_serviceBiz = new ServiceBiz();
    }



    /**
     * 获取终端管理列表
     * <AUTHOR>
     * @date   2020-12-23
     *
     * @return array
     */
    public function getManageTerminal()
    {

        $result = $this->_serviceBiz->getManageTerminal();

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取终端相关终端列表
     * <AUTHOR>
     * @date   2020-12-23
     *
     * @return array
     */
    public function getTerminalRelationList()
    {
        $terminalId = I('post.terminal_id', 0, 'intval');//智能终端id
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');
        if (!$terminalId) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $result = $this->_serviceBiz->getTerminalRelationList($terminalId, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    /**
     * 获取终端类型
     * <AUTHOR>
     * @date   2020-12-23
     *
     * @return array
     */
    public function getTerminaltype()
    {
        $result = $this->_serviceBiz->getTerminaltype();

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

}