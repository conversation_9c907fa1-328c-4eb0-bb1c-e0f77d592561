<?php
/**
 * 权限模块
 *
 *
 */
namespace Controller\AppCenter;

use Library\Controller;
use Model\AppCenter;
use Business\AppCenter\Module as ModuleBiz;

class ModuleAuthor extends Controller
{

    //微信模块的ID
    private $_wxModuleId = 1;

    //登陆用户的session
    private $_loginInfo;

    public function __construct()
    {
        // 获取登陆的session信息
        $this->_loginInfo = $this->getLoginInfo();
    }

    /**
     * 获取某个登陆用户的左侧菜单显示
     * 用到参数的原因是 在员工管理的时候 可能需要获取对应员工的菜单列表
     *
     * <AUTHOR>
     * @date   2017-01-03
     *
     * @params int      $memberId  账号ID
     * @params boolean  $flag      是否是外部传参
     */
    public function getMenuList($memberId = '', $flag = false)
    {
        if ($flag == false) {
            $memberId = $this->_loginInfo['memberID'];
        }

        $moduleBiz = new ModuleBiz();
        $res = $moduleBiz->getMenuList($memberId);

        return ['code' => $res['code'], 'data' => $res['data'], 'msg' => $res['msg']];
    }

    /**
     * 判断某个登陆用户是否有权限对某个url进行访问
     * 微信端不限制url
     * <AUTHOR>
     * @date   2016-12-21
     *
     * @params int     $outSideMember  用户ID
     * @params string  $flag           ='pc' 平台请求 要对url进行判断   ='wx' 微信请求 只需要判断是否开通模块
     */
    public function getUrlAuth($params = '')
    {
        pft_log('page_use/debug', '/r/AppCenter_ModuleAuthor/getUrlAuth is use');
        $code = 200;
        $msg  = '';

        try {

            if (!in_array($this->_loginInfo['sdtype'], [0, 1]) && $this->_loginInfo['dtype'] != 6) {
                //如果不是供应商 分销商  不对链接进行判断
                return true;
            }

            if (empty($params['outSideMember'])) {
                $memberId = $this->_loginInfo['memberID'];
                $flag     = 'pc';
            } else {
                $memberId = $params['outSideMember'];
                $flag     = $params['flag'];
            }

            $authorModel = new AppCenter\ModuleAuthor();
            $data        = $authorModel->getCacheByUser($memberId, 2);

            if ($data) {
                $urlAuth = unserialize($data);
            } else {
                //获取该用户的开通模块
                $moduleCommonBiz = new \Business\AppCenter\ModuleCommon();
                $useInfo = $moduleCommonBiz->getModuleUsedByNow($this->_loginInfo['sid']);

                if (empty($useInfo)) {
                    throw new \Exception("您尚未开通该模块");
                }

                $moduleList = array_column($useInfo, 'module_id');
                $urlList    = load_config('module_url', 'appcenter');

                $urlAuth = [];

                foreach ($moduleList as $key => $item) {
                    if (isset($urlList[$item])) {
                        $urlAuth = array_merge($urlAuth, $urlList[$item]);
                    }
                }

                $authorModel->setCacheByUser($memberId, $urlAuth, 2);
            }

            if ($flag == 'pc') {
                $url = $_SERVER['REQUEST_URI'];
                $url = ltrim($url, '/');
                $url = str_replace('new/d/', '', $url);
                if (strpos($url, '?') !== false) {
                    $position = strpos($url, '?');
                    $url      = substr($url, 0, $position);
                }

                if (!in_array($url, $urlAuth)) {
                    throw new \Exception("您尚未开通该模块");
                }
            } elseif ($flag == 'wx') {
                $moduleArr = array_column($useInfo, 'module_id');
                if (!in_array($this->_wxModuleId, $moduleArr)) {
                    throw new \Exception("您尚未开通该模块");
                }
            } else {
                throw new \Exception("异常请求");
            }

        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        if ($code == 400) {
            exit($msg);
        }
    }
}
