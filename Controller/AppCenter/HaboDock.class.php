<?php
/**
 * 哈泊系统对接
 * Created by PhpStorm.
 * User: banjin
 * Date: 2017/6/15
 * Time: 14:29
 */

namespace Controller\AppCenter;

use Library\Controller;
use Model\Member\Member;
use Model\AdminConfig\AdminConfig;

class  HaboDock extends Controller {

    private $_account       = null;
    //登录哈泊系统请求
    private $_postUrlLogin = "http://121.43.103.81:8088/Home/PFTLogin";
    //向哈泊系统发送账号请求
    private $_postUrlUser  = "http://121.43.103.81:8088/Pft/PFTUser";
    //跳转哈泊登录系统网址
    private $_locationUrl  = "http://121.43.103.81:8088/Home/Index/";
    //获取哈泊停车场信息
    private $_getRecord    = "http://121.43.103.81:8088/pft/PFT_GetPayRecord";
    //获取指定停车场剩余车位
    private $_getRemaining = "http://zd.12301.cc:8088/pft/PFT_GetSurplus";
    //根据车牌号获取停车费信息
    private $_getOrder     = "http://zd.12301.cc:8088/pft/PFT_GetParkOrder";
    //支付成功后的回调给哈泊系统
    private $_setPaySign   = "http://zd.12301.cc:8088/pft/PFT_SetPaySign";

    /**
     * 获取用户平台账号 检验权限等
     * <AUTHOR>
     *
     * @return
     */
    public function checkInfo()
    {
        //判断是否登录 取账号
        $LoginInfo       = $this->getLoginInfo();
        $this->_account = $LoginInfo['saccount'];

        //验证账号是否有开通停车场功能权限
        $AdminConfig = new AdminConfig();
        $checkDock   = $AdminConfig->getAccountsByAppid(13);

        //如果没有开通 则跳转12301
        if (!$checkDock || !in_array($LoginInfo['saccount'], $checkDock)) {
            $url = 'http://'.$_SERVER["HTTP_HOST"];

            echo "<script> alert('抱歉您尚未开通此业务!'); </script>";
            echo "<meta http-equiv='Refresh' content='0;URL=$url'>";
            exit();
        }
    }

    /**
     * 获取用户openid
     * <AUTHOR>
     *
     * @return
     */
    public function getWxOpenId()
    {
        $authBiz = new \Business\Wechat\Authorization();

        if ( I('code') ) {

            $auth = $authBiz->parseAuthInfo(I('code'));

            if ($auth['code'] != 200) {
                $_SESSION['openids'] = '';
            }else {
                //获取用户的openid
                $openid = $auth['data']['openid'];
                //获取到的openid 写入缓存
                $_SESSION['openids'] = $openid;
            }
            //跳转回页面
            $this->_loginAction();
        } else {
            $callback = 'http://'.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
            $params   = '3385';
            //如果有错 捕获异常抛出
            try {
                $authBiz->requestForAuth($callback, $params);
            } catch (Exception $e) {
                print $e->getMessage();
                exit();
            }
        }
    }

    /**
     * 微信获取openid 成功后的跳转
     */
    private function _loginAction() {

        $redirect = MOBILE_DOMAIN . 'html/parkPay.html';

        header("Location: {$redirect}");
    }

    /**
     * 登录哈泊系统   确保哈泊系统有账号 才能执行登录 否则必须先执行生成账号
     * <AUTHOR>
     *
     * @return
     */
    public function haPoSystemPost()
    {
        //登录检查
        $this->checkInfo();

        $url       = $this->_postUrlLogin;
        $account   = $this->_account;

        $token     = md5(md5('pft-habo'.$account));
        $sign      = md5('PFT'.$account.$token);

        $postData = [
            'account' => $account,
            'token'   => $token,
            'sign'    => $sign,
        ];

        //发送curl post方式
        $curlArray   = curl_post($url, $postData);
        $outputArray = json_decode($curlArray,true);

        //验证成功 页面跳转至哈泊系统
        if ($outputArray['State'] == 1) {
            $tokens = $outputArray['Message'];
            $url    = $this->_locationUrl.'?token='.$tokens;
            header("Location: $url");
        } else {
            echo '登录失败！';
        }
    }

    /**
     * 向哈泊系统发送账号  执行生成账号操作
     * <AUTHOR>
     *
     * @return array
     */
    public function haPoSystemGet()
    {
        //登录检查
        $this->checkInfo();

        $account = $this->_account;
        $token   = md5(md5('pft-habo'.$account));
        $sign    = md5('PFT'.$account.$token);
        $url     = $this->_postUrlUser;

        $postData = [
            'account' => $account,
            'token'   => $token,
            'sign'    => $sign,
        ];

        //发送curl get方式

        $curlArray   = curl_post($url, $postData, 80, 30, '/api/habo_curl_get', [], false, '', 'get');
        $outputArray = json_decode($curlArray,true);

        //处理返回数据
        if ($outputArray['State'] == 1) {
            //生成账号成功进行跳转登录操作
            $this->haPoSystemPost();
        } else {
            $msg = $outputArray['Message'];
            $url = 'http://'.$_SERVER["HTTP_HOST"];
            echo "<script>alert('". $msg ."!');</script>";
            echo "<meta http-equiv='Refresh' content='0;URL=$url'>";
            exit();
        }
    }

    /**
     * 向哈泊系统获取停车场时间段内的信息
     * <AUTHOR>
     *
     * @return array
     */
    public function getRecordGet()
    {
        //登录检查
        $this->checkInfo();

        $account = $this->_account;
        $token   = md5(md5('pft-habo'.$account));
        $sign    = md5('PFT'.$account.$token);
        //测试数据
        $begins  = '2015-01-06 10:12:30';
        $ends    = '2017-01-06 15:12:30';
        //测试数据
        $begin   = str_replace(" ","%20",$begins);
        $end     = str_replace(" ","%20",$ends);
        $url     = $this->_getRecord;

        $postData = [
            'account' => $account,
            'token'   => $token,
            'sign'    => $sign,
            'begin'   => $begin,
            'end'     => $end,
        ];

        //发送curl get方式
        $curlArray   = curl_post($url, $postData, 80, 30, '/api/habo_curl_get', [], false, '', 'get');
        $outputArray = json_decode($curlArray,true);
        var_dump($outputArray);

        //处理返回数据
        if ($outputArray['State'] == 1 && $outputArray['Records']) {
            //生成账号成功进行跳转登录操作
            var_dump($outputArray['Records']);
        } else {
            $msg = $outputArray['Message'] == '成功' ? '抱歉获取失败' : $outputArray['Message'];
            $url = 'http://'.$_SERVER["HTTP_HOST"];
            echo "<script>alert('". $msg ."!');</script>";
            echo "<meta http-equiv='Refresh' content='0;URL=$url'>";
            exit();
        }
    }

    /**
     * 获取指定停车场剩余车位
     * <AUTHOR>
     *
     * @return
     */
    public function getRemainingPost()
    {
        //【开始】如果产品经理 规划 从平台 发起的 那么 执行下面 程序 获取 账号
            //登录检查
            $this->checkInfo();
            //账号
            $account = $this->_account;
        //【结束】

        //【开始】如果产品经理 规划 从微信 未登陆 情况下 查询 则 通过 前端传递过来获取
            $account   = I('account', '', 'strval');
        //【结束】


        //加密
        $sign    = md5('account='.$account.';post_url='.$this->_getRemaining);
        //请求地址
        $url     = $this->_getRemaining;
        //post 数据
        $postData = [
            'account' => $account,
            'sign'    => $sign,
        ];
        //发送curl post方式
        $curlArray   = curl_post($url, $postData);
        $outputArray = json_decode($curlArray,true);

        //处理返回数据
        if ($outputArray['State'] == 1 && $outputArray['Surplus']) {
            //剩余车位
            var_dump($outputArray['Surplus']);
            //账号
            var_dump($outputArray['Account']);
        } else {
            $msg = $outputArray['Message'] == '失败' ? '抱歉获取失败' : $outputArray['Message'];
            $url = 'http://'.$_SERVER["HTTP_HOST"];
            echo "<script>alert('". $msg ."!');</script>";
            echo "<meta http-equiv='Refresh' content='0;URL=$url'>";
            exit();
        }
    }

    /**
     * 根据车牌号获取停车费信息
     * <AUTHOR>
     *
     * @return
     */
    public function getOrderPost($carNoInfo = false)
    {
        //车牌号
        $carNo   = I('carNo', '', 'strval');
        //$carNo = '闽D3P585';
        //加密
        if (!$carNo && $carNoInfo) {
            $carNo = $carNoInfo;
        }
        if (!$carNoInfo && !$carNo) {
            exit("车牌信息有误");
        }
        //加密
        $sign    = md5('carNo='.$carNo.';post_url='.$this->_getOrder);

        //请求地址
        $url     = $this->_getOrder;
        //post 数据
        $postData = [
            'carNo'   => $carNo,
            'sign'    => $sign,
        ];

        //发送curl post方式
        $curlArray   = curl_post($url, $postData);
        $outputArray = json_decode($curlArray,true);

        //处理返回数据
        if ($outputArray['State'] == 1 && $outputArray['Order']) {
            $OrderArr = $outputArray['Order'];
            if ($carNoInfo) {
                return $OrderArr;
            }else {
                $Account = $OrderArr['Account'];
                $MemberModel = new Member();
                $fid = $MemberModel->getMemberInfo($Account, 'account', 'id');
                $OrderArr['fid'] = $fid['id'];
                $this->apiReturn(200, $OrderArr, '获取成功');
            }
        } else {
            if ($carNoInfo) {
                return $outputArray['Message'];
            }else{
                $msg = $outputArray['Message'] == '失败' ? '抱歉获取失败' : $outputArray['Message'];
                $url = 'http://'.$_SERVER["HTTP_HOST"];
                echo "<script>alert('". $msg ."!');</script>";
                echo "<meta http-equiv='Refresh' content='0;URL=$url'>";
                exit();
            }
        }
    }

    /**
     * 支付成功后回调给哈泊系统
     * <AUTHOR>
     *
     * @param  array $result 回调数据
     * @return
     */
    public function setPaySign(array $result, $tradeNo)
    {
        $carNo  = $result['carNo'];
        $parkId = $result['parkId'];
        $fee = $result['fee'];
        $inTime = $result['inTime'];
        $outTime = $result['outTime'];
        $payType = $result['payType'];

        $sign    = md5('carNo='.$carNo.';parkId='.$parkId.';post_url='.$this->_setPaySign);

        //请求地址
        $url = $this->_setPaySign;
        //post 数据
        $postData = [
            'carNo'   => $carNo,
            'fee'     => intval($fee)/100,
            'parkId'  => $parkId,
            'inTime'  => $inTime,
            'outTime' => $outTime,
            'payType' => $payType,
            'payNo'   => $tradeNo,
            'sign'    => $sign,
        ];

        //发送curl post方式
        $curlArray   = curl_post($url, $postData);
        $outputArray = json_decode($curlArray,true);

        //回调成功
        if ($outputArray['State'] == 1) {
            pft_log('wepay_habo/success', "支付成功回调哈泊接口:" . json_encode($outputArray, JSON_UNESCAPED_UNICODE));
            return true;
        }else {
            pft_log('wepay_habo/fail', "支付成功回调哈泊接口失败:   车牌号:" .$carNo.'微信支付流水号:'
                .$tradeNo.'停车场id:'.$parkId.'应支付费用:'.$fee.'支付方式:'.$payType );
            return false;
        }

    }

}