<?php
/**
 * 应用中心模块(新)
 *
 * <AUTHOR>
 * @date   2020-10-29
 */

namespace Controller\AppCenter;

use Business\AppCenter\ModuleCommon as ModuleCommonBiz;
use Business\MemberLogin\MemberLoginHelper;
use Business\SubMerchant\SubMerchant as SubMerchantBz;
use Business\ExportCode\Manage as ManageBiz;
use Library\Controller;
use Business\AppCenter\Module as ModuleBiz;
use Business\AppCenter\LimitBuy as LimitBuyBiz;

class Module extends Controller
{

    //登录信息
    private $_loginInfo;
    //主账号id
    private $_fid;

    private $_sDtype;
    private $_memberId;

    public function __construct()
    {

        $this->_loginInfo = $this->getLoginInfo();
        $this->_fid     = $this->_loginInfo['sid'];
        $this->_sDtype  = $this->_loginInfo['sdtype'];
        $this->_memberId = $this->_loginInfo['memberID'];
    }


    /**
     * 获取模块列表数据
     * <AUTHOR>
     * @date   2020-10-29
     *
     */
    public function getList()
    {
        //搜索的关键词
        $keyWord      = trim(I('get.key_word/s', ''));
        $getOpenState = I('get.get_open_state/b', true);
        $getPrice     = I('get.get_price/b', true);
        $tagCodes     = I('get.tag_code', '');
        $sid = $this->_fid;
        $sdtype = $this->_sDtype;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $sdtype = MemberLoginHelper::getLoginBusinessMember()->getSupplierDtype();
        }
        $moduleRes = (new ModuleBiz)->getListForSearch($sid, $sdtype, $keyWord,strval($tagCodes),$getOpenState, $getPrice);
        if ($moduleRes['code'] == 200) {
            $data = ['list' => $moduleRes['data']];
            $this->apiReturn(200, $data);

        } else {
            $this->apiReturn($moduleRes['code'], [], $moduleRes['msg']);
        }
    }


    /**
     * 获取模块详情
     * <AUTHOR>
     * @date   2020-10-29
     *
     */
    public function getInfo()
    {
        //模块id
        $moduleId     = I('get.module_id', 0, 'intval');
        $tagName      = I('get.tag', '', 'strval');
        $getOpenState = I('get.get_open_state/b', true);
        $getPrice     = I('get.get_price/b', true);

        if (!$moduleId && empty($tagName)) {
            $this->apiReturn(400, [], '模块参数错误');
        }

        //支持标识访问, 前提是id不存在的情况，不然还是使用id
        if (!empty($tagName) && !$moduleId) {
            $moduleCommonBiz = new \Business\AppCenter\ModuleCommon();
            $moduleRes       = $moduleCommonBiz->getModuleIdByMenu([$tagName]);
            $menuMap         = array_column($moduleRes, 'module_id', 'menu_id');
            $moduleId        = isset($menuMap[$tagName]) ? $menuMap[$tagName] : 0;
            if (!$moduleId) {
                $this->apiReturn(400, [], '模块参数错误');
            }
        }

        $moduleRes = (new ModuleBiz)->getInfo($moduleId, $this->_fid, $this->_sDtype, $getOpenState, $getPrice);
        if ($moduleRes['code'] == 200) {
            $data = ['info' => $moduleRes['data']];
            $this->apiReturn(200, $data);

        } else {
            $this->apiReturn($moduleRes['code'], [], $moduleRes['msg']);
        }
    }

    /**
     * 获取分类
     * <AUTHOR>
     * @date 2020/11/12
     *
     */
    public function getSuperiorClass()
    {
        $moduleRes = (new ModuleBiz)->getSuperiorClass();
        if ($moduleRes['code'] == 200) {
            $data = $moduleRes['data'];
            $this->apiReturn(200, $data);

        } else {
            $this->apiReturn($moduleRes['code'], [], $moduleRes['msg']);
        }
    }

    /**
     * 开通详情查询
     * <AUTHOR>
     * @date 2020/11/12
     *
     */
    public function getOpenInfo()
    {
        //模块id
        $moduleId = I('get.module_id', 0, 'intval');

        if (!$moduleId) {
            $this->apiReturn(400, [], '模块id参数错误');
        }

        $moduleRes = (new ModuleBiz)->getOpenInfo($moduleId, $this->_fid, $this->_sDtype);
        if ($moduleRes['code'] == 200) {
            $data = $moduleRes['data'];
            $this->apiReturn(200, $data);

        } else {
            $this->apiReturn($moduleRes['code'], [], $moduleRes['msg']);
        }
    }

    /**
     * 购买记录
     * <AUTHOR>
     * @date 2020/11/13
     *
     */
    public function getBuyRecord()
    {
        $page = I('get.page', 0, 'intval');
        $size = I('get.size', 10, 'intval');

        $moduleRes = (new ModuleBiz)->getBuyRecord($this->_fid, $page, $size);
        if ($moduleRes['code'] == 200) {
            $data = $moduleRes['data'];
            $this->apiReturn(200, $data);

        } else {
            $this->apiReturn($moduleRes['code'], [], $moduleRes['msg']);
        }
    }

    /**
     * 购买记录详情
     * <AUTHOR>
     * @date 2020/11/13
     *
     */
    public function getBuyInfo()
    {
        $tradeNo = I('get.trade_no', 0, 'strval');
        if (!$tradeNo) {
            $this->apiReturn(400, [], '订单号异常');
        }
        $moduleRes = (new ModuleBiz)->getBuyRecordInfo($this->_fid, $tradeNo);
        if ($moduleRes['code'] == 200) {
            $data = $moduleRes['data'];
            $this->apiReturn(200, $data);

        } else {
            $this->apiReturn($moduleRes['code'], [], $moduleRes['msg']);
        }
    }

    /**
     * 获取开通的应用
     * <AUTHOR>
     * @date 2020/11/19
     *
     */
    public function getOpenRecord()
    {
        $page = I('get.page', 0, 'intval');
        $size = I('get.size', 10, 'intval');

        $moduleRes = (new ModuleBiz)->getOpenRecord($this->_fid, $page, $size);
        if ($moduleRes['code'] == 200) {
            $data = $moduleRes['data'];
            $this->apiReturn(200, $data);

        } else {
            $this->apiReturn($moduleRes['code'], [], $moduleRes['msg']);
        }
    }

    /**
     * 获取用户限购配置详情
     * <AUTHOR>
     * @date 2021/9/6
     *
     */
    public function getUserLimitBuyInfo()
    {
        $targetId = I('get.target_id', 0, 'intval');

        $moduleRes = (new LimitBuyBiz)->getUserLimitBuyInfo($this->_fid, $targetId, 1);
        if ($moduleRes['code'] == 200) {
            $data = $moduleRes['data'];
            $this->apiReturn(200, $data);

        } else {
            $this->apiReturn($moduleRes['code'], [], $moduleRes['msg']);
        }
    }


    public function moduleUseState()
    {
        $module_tag = I('get.tag', '', 'strval');
        if (!$module_tag) {
            $this->apiReturn(200);
        }

        $sid = $this->_fid;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $useData = (new ModuleBiz())->getUserModuleUsed([$sid], [$module_tag], false);
        if (empty($useData[$sid][$module_tag])) {
            $this->apiReturn(200);
        }

        $moduleUseInfo = $useData[$sid][$module_tag];
        if ($moduleUseInfo['status'] == 1 && $moduleUseInfo['begin_time'] < time()) {
            $this->apiReturn(200, [ $module_tag => [$moduleUseInfo]]);
        }

        $this->apiReturn(200);
    }

    /**
     * 获取子商户应用的开通记录
     * @return void
     */
    public function subMerchantModuleOpened()
    {
        $useData       = (new ModuleBiz())->getUserModuleUsed([$this->_fid], [SubMerchantBz::SUB_MERCHANT_APP_CENTER_TAG], false);

        $moduleUseInfo = $useData[$this->_fid][SubMerchantBz::SUB_MERCHANT_APP_CENTER_TAG] ?? [];
        $returnData = [
            'opened' => true
        ];

        if (empty($moduleUseInfo)) {
            $returnData['opened'] = false;
        }

        $this->apiReturn(200, $returnData);
    }

    public function checkModuleAuth(){
        //应用权限校验
        $module_tag = I('get.tag', '', 'strval');
        if (!$module_tag) {
            $this->apiReturn(200);
        }
        $authBiz = new \Business\AppCenter\ModuleCommon();
        $auth = $authBiz->checkModuleAuth($this->_fid, $this->_memberId, $this->_sDtype, $module_tag);
        if (!$auth) {
            $this->apiReturn(401, [], '无权限');
        }
    }

    public function checkModuleAuthForApprovalManage(){
        //应用权限校验
        $authBiz = new \Business\AppCenter\ModuleCommon();
        $auth = $authBiz->checkModuleAuth($this->_fid, $this->_memberId, $this->_sDtype, ManageBiz::APPROVAL_MANAGE_TAG);
        if (!$auth) {
            $this->apiReturn(401, [], '无权限');
        }
        else{
            $this->apiReturn(200);
        }
    }

    /**
     * 验证主账号应用开通情况
     * <AUTHOR>
     * @date   2024/04/02
     *
     */
    public function checkModuleAuthByMain(){
        //应用权限校验
        $module_tag = I('get.tag', '', 'strval');
        if (empty($module_tag)) {
            $this->apiReturn(200, ['auth' => false], '参数错误');
        }
        $moduleCommonBiz = new ModuleCommonBiz();

        $auth = $moduleCommonBiz->checkUserIsCanUseApp($this->_fid, $module_tag);
        if (!$auth) {
            $this->apiReturn(200, ['auth' => false], '无可用服务');
        }

        $this->apiReturn(200, ['auth' => true], '服务可用');
    }
}
