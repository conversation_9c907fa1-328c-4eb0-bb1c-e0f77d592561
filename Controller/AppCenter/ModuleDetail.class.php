<?php
/**
 * 模块详情控制器
 * User: chenyanbin
 * Date: 2016/12/12
 * Time: 13:59
 */
namespace Controller\AppCenter;

use Business\AppCenter\Module;
use Business\AppCenter\Module as ModuleBiz;
use Library\Cache\Cache;
use Library\Constants\RedisKey\ModuleKeyConst;
use       Library\Controller;
use       Library\Exception;
use       Model\AppCenter;


class ModuleDetail extends Controller
{

    private $sid;
    private $dtype;
    private $_modeType;
    private $_menuList = null;
    private $_loginInfo;

    public function __construct()
    {
        // 判断登陆 并 赋值
        $this->_loginInfo = $this->getLoginInfo('ajax');
        $this->sid = $this->_loginInfo['sid'];
        $this->dtype = $this->_loginInfo['dtype'];

        $this->_modeType = load_config('pay_mode', 'appcenter');
        $this->_menuList = load_config('appcenter_menu', 'appcenter');
    }

    /**
     * 获得Member模型实例
     * <AUTHOR>
     * @date   2016-12-12
     */
    public function getModuleModel()
    {
        static $model;

        if (!$model) {
            $model = new \Model\AppCenter\ModuleDetail();
        }

        return $model;
    }

    /**
     * 获取当前模块的已有的模块信息(付费界面--模块描述)
     * <AUTHOR>
     * @date   2016-12-07
     *
     * @params int      $module_id       模块id
     * @return array
     */
    //public function getModuleDetail()
    //{
    //    pft_log('page_use/debug', '/r/AppCenter_ModuleDetail/getModuleDetail is use');
    //    $code = 200;
    //    $msg = '查询成功';
    //    $data = [];
    //
    //    try {
    //        $id = I('post.module_id', 0, 'intval');
    //
    //        if (!is_numeric($id) || $id == 0) {
    //            throw new \Exception('参数错误');
    //        }
    //
    //        //$model = new AppCenter\ModuleDetail();
    //        //$data = $model->getModuleDetail($id);
    //        $moduleCommonBiz = new \Business\AppCenter\ModuleCommon();
    //        $data        = $moduleCommonBiz->getModuleDetailById($id, 'module_id, name, summary, free_day, guide', true, 'introduce, parent, link_module');
    //
    //        if ($data) {
    //            //取出关联的模块名称和id
    //            $link = json_decode($data['link_module'], true);
    //            $linkInfo = [];
    //            if (count($link) > 0) {
    //                //$linkInfo = $model->getModuleNameByIdArr($link);
    //                $linkInfo = $moduleCommonBiz->getModuleById($link, 'module_id, name');
    //            }
    //            $data['link_info'] = $linkInfo;
    //        } else {
    //            $data = [];
    //            throw new \Exception('暂无数据');
    //        }
    //    } catch (\Exception $e) {
    //        $code = 400;
    //        $msg = $e->getMessage();
    //    }
    //
    //    $this->apiReturn($code, $data, $msg);
    //}

    /**
     * 获取该用户的模块详情
     * <AUTHOR>
     * @date   2016-12-12
     *
     * 已废弃 请调用新的 \Controller\AppCenter\Module::getInfo
     * @params int      $module_id       模块id
     * @return array
     */
    //public function getAppDetail()
    //{
    //    pft_log('page_use/debug', '/r/AppCenter_ModuleDetail/getAppDetail is use');
    //
    //    $mid = I('post.module_id', 0, 'intval');
    //    $code = 200;
    //    $msg = '成功';
    //    try {
    //        if ($mid == 0) {
    //            throw new \Exception("参数有误");
    //        }
    //        $Hardware = $this->getModuleModel()->getModuleInfoById($mid,true);
    //        if (empty($Hardware)){
    //            throw new \Exception("该模块不开放！");
    //        }
    //        if ($Hardware['category'] == 6) { //硬件中心
    //            //if ($mid == 9 || $mid == 10 || $mid == 11 || $mid == 15) {   //硬件中心
    //            $type = 0;
    //            $data = $this->getModuleModel()->appDetail($type, $mid, $this->dtype, $this->sid);
    //            $checkTypeA = $this->getModuleModel()->getAccountType($mid, $this->_loginInfo['sdtype']);
    //            if (!$checkTypeA) {
    //                $data = null;
    //                $data['prohibit'] = 0;
    //                throw new \Exception("该模块不开放！");
    //            }
    //            $link = $this->linkmodule($type, $mid);
    //        } else {
    //            $type = 1;
    //            $checkTypeA = $this->getModuleModel()->getAccountType($mid, $this->_loginInfo['sdtype']);
    //            if (!$checkTypeA) {
    //                $data = null;
    //                $data['prohibit'] = 0;
    //                throw new \Exception("该模块不开放！");
    //            }
    //            $data = $this->getModuleModel()->getModuleUsedByUid($this->sid, $mid);
    //            if ($data) {
    //                $data = $this->getModuleModel()->appDetail($type, $mid, $this->dtype, $this->sid);
    //            } else {
    //                $data = $this->getModuleModel()->appDetail($type, $mid, $this->dtype);
    //            }
    //            $data['open_num'] = Module::getOpenNum($mid,7);
    //            $link = $this->linkmodule($type, $mid);
    //            if (is_array($link)) {
    //                foreach ($link as $key => &$value){
    //                    $value['detail']['open_num'] = Module::getOpenNum($value['detail']['module_id'],7);
    //                }
    //                unset($value);
    //            }
    //            $data['checkData'] = $this->checkTimeType($mid);
    //        }
    //    } catch (\Exception $e) {
    //        $code = 400;
    //        $msg = $e->getMessage();
    //    }
    //
    //    //同业平台收费版和免费版限制
    //    $moduleLib = new ModuleBiz();
    //    $sameBusinessFreeModuleId = Module::getModuleIdByMenuId(Module::APP_SAME_BUSINESS_FREE);
    //    $data['is_samefree'] = 0;
    //    if (intval($data['module_id']) == $sameBusinessFreeModuleId) {
    //        $result = $moduleLib->checkSameBusinessModule($this->sid);
    //        if (!empty($result['data']) && $result['data']['is_open'] == 1 && $result['data']['is_charge'] == 1) {
    //            $data['is_samefree'] = 1;
    //        }
    //    }
    //
    //    //推荐同业平台收费版和免费版限制
    //    if (!empty($link)) {
    //        foreach ($link as &$valLink) {
    //            $valLink['detail']['is_samefree'] = 0;
    //            if (intval($valLink['detail']['module_id']) == $sameBusinessFreeModuleId) {
    //                $result = $moduleLib->checkSameBusinessModule($this->sid);
    //                if (!empty($result['data']) && $result['data']['is_open'] == 1 && $result['data']['is_charge'] == 1) {
    //                    $valLink['detail']['is_samefree'] = 1;
    //                }
    //            }
    //        }
    //    }
    //
    //    $info = [
    //        'data' => $data,
    //        'link' => $link,
    //        'type' => $type,
    //    ];
    //
    //    $this->apiReturn($code, $info, $msg);
    //}


    /**
     * 获取模块的推荐应用
     * <AUTHOR>
     * @date   2016-12-12
     *
     * @params int      $type      是否是硬件模块 0是 1不是
     * @params int      $mid       模块id
     * @return array    $info
     */
    //public function linkmodule($type, $mid)
    //{
    //    pft_log('page_use/debug', '/r/AppCenter_ModuleDetail/linkmodule is use');
    //
    //    $data = $this->getModuleModel()->linkmodule($mid);
    //    if ($data) {
    //        $link_module = $data['link_module'];
    //        $link_module = json_decode($link_module, true);
    //        if (is_array($link_module)) {
    //            $modulBiz = new Module();
    //            foreach ($link_module as $k => $v) {
    //                $data = $this->getModuleModel()->appDetail($type, $v, $this->dtype);
    //                if (empty($data)) {
    //                    continue;
    //                }
    //                $info[$k]['detail'] = $data;
    //                $info[$k]['detail']['flag_discount'] = 0;
    //                $info[$k]['detail']['checkData'] = $this->checkTimeType($v);
    //                $freeInfo = $this->getModuleModel()->getModulePrice($v);
    //                foreach ($freeInfo as $price){
    //                    if ($price['is_discount']){
    //                        if ($modulBiz->checkDiscountTime($price['discount_begin_time'],$price['discount_end_time'])){
    //                            $info[$k]['detail']['flag_discount'] = 1;
    //                            $info[$k]['detail']['discount'] = [
    //                                'now_price'           => $price['fee'] / 100,
    //                                'discount_price'      => $price['discount_price']/100,
    //                                'mode'                => $this->_modeType[$price['mode']],
    //                                'discount_begin_time' => $price['discount_begin_time'],
    //                                'discount_end_time'   => $price['discount_end_time']
    //                            ];
    //                            break;
    //                        }
    //                    }
    //                }
    //                $checkTypeA = $this->getModuleModel()->getAccountType($v, $this->_loginInfo['sdtype']);
    //                if (!$checkTypeA) {
    //                    unset($info[$k]);
    //                }
    //            }
    //        }
    //        if (is_array($info)) {
    //            $info = array_values($info);
    //        }
    //    } else {
    //        $info = [];
    //    }
    //
    //    return $info;
    //}

    /**
     * 判断用户对于该模块的一系列判断
     * <AUTHOR>
     * @date   2016-12-19
     *
     * @params int      $mid       模块id
     * @return array    $info
     */
    //public function checkTimeType($mid)
    //{
    //    pft_log('page_use/debug', '/r/AppCenter_ModuleDetail/checkTimeType is use');
    //
    //    $topData = $this->getModuleModel()->topModuleDeter($mid, $this->sid);  //判断顶级产品的
    //    if ($topData['topType'] == 1) {
    //        $data['topType'] = 1;
    //        $data['topName'] = null;
    //        $data['id'] = null;
    //    } else if ($topData['topType'] == 2) {
    //        $data['topType'] = 2;
    //        $data['topName'] = $topData['info'];
    //        $data['id'] = $topData['id'];
    //    }
    //
    //    $menu = $this->getModuleModel()->getLimitMenuByModule($mid);
    //    foreach ($this->_menuList as $key => $value) {
    //        if ($key == $menu['menu_id']) {
    //            $url = $value['url'][0];
    //        }
    //    };
    //    if (!$url) {
    //        $url = 'new/appcenter_details.html?module_id=' . $mid;
    //    }
    //    $buttonType = new \Controller\AppCenter\ModuleList();   //调用吴浩判断按钮得程序
    //    $buttonTypeInfo = $buttonType->getButtonType($mid);
    //    if ($buttonTypeInfo) {
    //        $button = $buttonTypeInfo['button_type'];
    //        $data['showextime'] = 0; //默认不显示到期日期
    //        if ($buttonTypeInfo['hide'] == 1) {
    //            if ($buttonTypeInfo['xufei'] == 1 && $button == 2) {
    //                $data['url'] = $url;
    //                $data['showType'] = -1;
    //            } elseif ($buttonTypeInfo['xufei'] == 0 && $button == 2) {
    //                $data['url'] = $url;
    //                $data['showType'] = 2;
    //                $data['show_hide'] = 1;//第一个按钮隐藏
    //            } else {
    //                $data['showType'] = -2;//不显示此模块
    //            }
    //        } else {
    //            if ($buttonTypeInfo['xufei'] == 1) {
    //                $data['showextime'] = 1;
    //                $data['showType'] = -1;
    //                $data['url'] = $url;
    //            } else {
    //                switch ($button) {
    //                    case 0:
    //                        $data['showType'] = 0;
    //                        break;
    //                    case 1:
    //                        $data['showType'] = 1;
    //                        break;
    //                    case 2:
    //                        $data['showType'] = 2;
    //                        $data['show_hide'] = 0;//推荐应用的使用不隐藏
    //                        $data['url'] = $url;
    //                        break;
    //                    case 3:
    //                        $data['showType'] = 3;
    //                        break;
    //                    default:
    //                }
    //                $data['showextime'] = 0;
    //            }
    //        }
    //        if ($button == 0 || $button == 1 || $data['showType'] == -1) {
    //            $freeInfo = $this->getModuleModel()->getTimeFree($mid);
    //            $modulBiz = new Module();
    //            foreach ($freeInfo as $key => $value) {
    //                $modaType = $this->_modeType;
    //                $mode = $value['mode'];
    //                $finfo[$key]['free'] = $value['fee'] / 100;
    //                $finfo[$key]['mode'] = $modaType[$mode];
    //                $checkTime = $modulBiz->checkDiscountTime($value['discount_begin_time'],$value['discount_end_time']);
    //                if ($checkTime){
    //                    $finfo[$key]['discount_price']      = $value['discount_price']/100;
    //                    $finfo[$key]['discount_begin_time'] = $value['discount_begin_time'];
    //                    $finfo[$key]['discount_end_time']   = $value['discount_end_time'];
    //                }
    //            }
    //
    //            $data['moduleFree'] = $finfo;
    //        }
    //
    //        //var_dump($data);die;
    //        return $data;
    //    } else {
    //        $data['showType'] = -2;
    //        return $data;
    //    }
    //}
}
