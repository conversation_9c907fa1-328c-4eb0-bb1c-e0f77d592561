<?php
/**
 * Created by PhpStorm.
 * User: 谢小勇
 * Date: 2020-12-17
 * Time: 14:15
 */

namespace Controller\AppCenter;

use Business\JsonRpcApi\PayService\UnifiedPay;
use Business\MemberLogin\MemberLoginHelper;
use Library\Container;
use Library\Controller;
use Business\AppCenter\Package as PackageBiz;
use Business\AppCenter\Payment as PaymentBiz;
use Model\TradeRecord\OnlineTrade;
use Library\Business\Alipay\F2FPay;
use Library\Business\WePay\WxPayLib;
use Business\AppCenter\LimitBuy as LimitBuyBiz;

class Package extends Controller
{
    //登录信息
    private $_loginInfo;
    //主账号id
    private $_fid;

    private $_sDtype;


    //支付类型
    private $_payWay = [
        1 => '平台账户余额支付',
        2 => '微信支付',
        3 => '支付宝支付'
    ];

    //收银台支付方式配置（新）
    private $_payTypeMap = [
        1 => '支付宝',
        2 => '微信',
        3 => '平台账户余额支付',
    ];

    private $_logDir = 'package/user_buy/';

    const PACKAGE_NOTIFY_URL = PAY_DOMAIN.'/r/pay_CommonPayNotify/platformPackageNotify';
    const PACKAGE_SOURCET    = 41; // 与CommonPayNotify::platformBuyPackageNotify保持一致

    public function __construct()
    {

        $this->_loginInfo = $this->getLoginInfo();
        $this->_fid     = $this->_loginInfo['sid'];
        $this->_sDtype  = $this->_loginInfo['sdtype'];
    }


    /**
     * 获取套餐详情
     * <AUTHOR>
     * @date   2020-12-19
     *
     */
    public function getInfo()
    {
        //模块id
        $packageId = I('get.id', 0, 'intval');

        if (!$packageId) {
            $this->apiReturn(400, [], '应用id参数错误');
        }

        $packageRes = (new PackageBiz)->packageInfo($packageId);
        $this->apiReturn($packageRes['code'], $packageRes['data'], $packageRes['msg']);
    }


    /**
     * 获取套餐列表
     * <AUTHOR>
     * @date   2020-12-19
     *
     */
    public function list()
    {
        $packageRes = (new PackageBiz)->packageList();
        $this->apiReturn($packageRes['code'], $packageRes['data'], $packageRes['msg']);
    }



    /**
     * 套餐升级信息接口
     * <AUTHOR>
     * @date   2020-12-19
     *
     */
    public function upgrade()
    {
        $upgradeRes = (new PackageBiz)->upgradeInfo($this->_fid);
        $this->apiReturn($upgradeRes['code'], $upgradeRes['data'], $upgradeRes['msg']);
    }


    /**
     * 基础功能列表
     * <AUTHOR>
     * @date 2020/12/18
     *
     */
    public function baseFun()
    {
        $result = (new PackageBiz)->getBaseFunList();
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }



    /**
     * 购买套餐
     * <AUTHOR>
     * @date 2020/12/18
     *
     */
    public function buyPackageOnLine()
    {
        $code    = 200;
        $msg     = '';
        $url     = '';
        $result  = '';

        //请求id
        $requestId = str_replace('.', '', microtime(true));
        $cache     = \Library\Cache\Cache::getInstance('redis');

        try {

            $loginInfo = $this->getLoginInfo();
            $memberId  = $loginInfo['memberID'];
            $dtype     = $loginInfo['dtype'];

            if (!in_array($dtype, [0, 1])) {
                throw new \Exception("身份不符,不允许开通");
            }

            //模块id
            $packageId   = I('post.package_id', 0, 'intval');
            //购买时长 1=年 2=季度 3=月
            $priceMode   = I('post.price_mode', 0, 'intval');
            //产品类型
            $productType = I('post.product_type', []);
            //支付方式
            $payWay      = I('post.pay_way', 0, 'intval');

            if (!$packageId || !$priceMode || !is_array($productType)) {
                $this->apiReturn(204, [], '参数错误');
            }

            if (empty($payWay) || !isset($this->_payWay[$payWay])) {
                throw new \Exception("支付方式参数错误");
            }

            //支付方式描述
            $payWayStr = $this->_payWay[$payWay];
            //开通申请 日志
            pft_log($this->_logDir, "{$requestId}:申请开通：用户ID：{$memberId}, 套餐ID：{$packageId},资费ID：{$priceMode}|支付方式：{$payWayStr}");

            //加锁 防止恶意请求
            $locky    = "pay_package:{$payWayStr}:{$requestId}:$memberId";
            $lock_ret = $cache->lock($locky, 1, 120);
            if (!$lock_ret) {
                throw new \Exception("请求正在处理中，请稍后");
            };

            $packageBiz = new \Business\AppCenter\Package();
            //开通套餐前的检测  走新工程appcenterService
            $checkRes = $packageBiz->openPreCheck($memberId, $dtype, $packageId, $priceMode, $productType, $payWay);
            if ($checkRes['code'] != 200) {
                throw new \Exception($checkRes['msg']);
            }

            //套餐角色
            $packageRole = $checkRes['data']['role'];
            //标题
            $subject = $checkRes['data']['subject'];
            //订单号
            $orderNo = $checkRes['data']['trade_no'];
            //费用 微信以分为单位
            $fee     = $checkRes['data']['fee'];

            if (empty($orderNo) || !isset($fee)) {
                throw new \Exception("订单生成出错, 请重试");
            }

            //客服签单人员数据
            $saleInfo    = [];
            //套餐数据
            $packageData = [
                'package_id'    => $packageId,
                'price_mode'    => $priceMode,
                'product_type'  => $productType,
                'sale_info'     => $saleInfo,
                'role'          => $packageRole,
            ];

            $tmpRemarks = [
              //  'out_trade_no' => $orderNo,
                'member_id'    => $memberId,
                'package_data' => $packageData,
                'fee'          => $fee,
                'subject'      => $subject,
                'pay_type'     => 2,    //标识是购买模块
            ];

            $remarks = json_encode($tmpRemarks);
            if ($payWay == 1) {

                $handleAfterOpen = true;
                //平台账户余额支付
                $payRes = (new PaymentBiz())->platformPayModule($orderNo, $fee, $memberId, $subject);
                if ($payRes['code'] != 200) {
                    throw new \Exception($payRes['msg']);
                }

                $result .= '平台余额支付，';
                //开通套餐
                $openRes = $packageBiz->openPackage($memberId, $packageId, $priceMode, $productType, $orderNo, $saleInfo);
                //如果开通模块失败
                if ($openRes['code'] != 200) {
                    //记录余额支付异常日志
                    throw new \Exception($openRes['msg']);
                }

                //钉钉通知
                (new \Business\AppCenter\BaseCall())->dingdingSendOpenMsg($memberId, $subject . ($fee / 100) . "元", $orderNo, ($fee > 0 ? 0 : 1), 2); //开通套餐 余额

            } else {
                //默认支付方式：支付宝
                $sourceT = 0;
                $onLineTrade = new OnlineTrade();
                //微信支付或者支付宝支付
                if (defined('ENV') && in_array(ENV, ['PRODUCTION', 'TEST'])) {

                    if ($payWay == 2) {//微信
                        $getQrUrl = $this->_getWxPayQrCode($orderNo, $fee, $subject);
                        $sourceT = 1; //来源记录微信

                        $result .= '生成微信扫码支付，';
                    }

                    if ($payWay == 3) {//支付宝
                        $getQrUrl = $this->_getAliPayQrCode($orderNo, $fee, $subject);
                        $sourceT = 0; //来源记录支付宝

                        $result .= '生成支付宝扫码支付，';
                    }

                    if (empty($getQrUrl['code']) || $getQrUrl['code'] != 200) {
                        throw new \Exception($getQrUrl['msg']);
                    }

                    $url = $getQrUrl['url'];
                    //生成充值记录
                    $create = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception("充值记录生成失败");
                    }

                } else {

                    $handleAfterOpen = true;
                    //测试环境，模拟成功支付
                    $create = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception("充值记录生成失败");
                    }

                    //模拟支付成功
                    $tradeNo = 'online_' . time();
                    $res     = (new PaymentBiz())->onlinePayPackage($orderNo, $tmpRemarks, $tradeNo, 2);
                    if ($res['code'] != 200) {
                        throw new \Exception($res['msg']);
                    }

                    $result .= '测试模拟支付，';

                    //开通套餐
                    $openRes = $packageBiz->openPackage($memberId, $packageId, $priceMode, $productType, $orderNo, $saleInfo);
                    if ($openRes['code'] != 200) {
                        throw new \Exception($openRes['msg']);
                    }
                }
            }

            if (!empty($handleAfterOpen) && !empty($openRes['data'])) {
                $openData  = $openRes['data'];
                $handleRes = $packageBiz->handleAfterOpen($memberId, $packageId, $packageRole, $openData['begin_time'],
                        $openData['end_time'], $fee);

                if (!$handleRes) {
                    throw new \Exception( $handleRes['msg']);
                }
            }

        } catch (\Exception $e) {

            $code = 400;
            $msg  = $e->getMessage();
        }

        if (!empty($locky)) {
            $cache->rm($locky);
        }

        //开通结果 日志
        if ($code == 200) {
            $result .= '成功';
        } else {
            $result .= '失败';
        }
        $fee = $fee / 100;
        pft_log($this->_logDir,
            "{$requestId}:申请结果：用户ID：{$memberId}, 套餐ID：{$packageId},资费ID：{$priceMode}, 费用{$fee}元, 流水号{$orderNo}|结果|{$result}, 原因:{$msg}");

        $data = ['url' => $url, 'order_no' => $orderNo];
        $this->apiReturn($code, $data, $msg);
    }


    /**
     * 升级套餐
     *
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    public function upgradePackageOnLine()
    {
        $code    = 200;
        $msg     = '';
        $url     = '';
        $result  = '';

        //请求id
        $requestId = str_replace('.', '', microtime(true));
        $cache     = \Library\Cache\Cache::getInstance('redis');

        try {

            $loginInfo = $this->getLoginInfo();
            $memberId  = $loginInfo['memberID'];
            $dtype     = $loginInfo['dtype'];

            if (!in_array($dtype, [0, 1])) {
                throw new \Exception("身份不符,不允许开通");
            }

            //源套餐id
            $originPackageId  = I('post.origin_package_id', 0, 'intval');
            //升级套餐id
            $upgradePackageId = I('post.upgrade_package_id', 0, 'intval');
            //抵扣金额
            $deductMoney = I('post.deduct_money', 0, 'intval');
            //最终支付金额
            $payMoney    = I('post.pay_money', 0, 'intval');

            //产品类型
            $productType = I('post.product_type', []);
            //支付方式
            $payWay      = I('post.pay_way', 0, 'intval');

            if (!$originPackageId || !$upgradePackageId || !is_array($productType)) {
                $this->apiReturn(204, [], '参数错误');
            }

            if (empty($payWay) || !isset($this->_payWay[$payWay])) {
                throw new \Exception("支付方式参数错误");
            }

            //支付方式描述
            $payWayStr = $this->_payWay[$payWay];
            //开通申请 日志
            pft_log($this->_logDir, "{$requestId}:升级套餐：用户ID：{$memberId}, 源套餐ID：{$originPackageId}, 
            升级套餐ID：{$upgradePackageId}, 抵扣金额：{$deductMoney}|支付金额：{$payMoney}|支付方式{$payWayStr}");

            //加锁 防止恶意请求
            $locky    = "upgrade_package:{$payWayStr}:{$requestId}:{$upgradePackageId}:$memberId";
            $lock_ret = $cache->lock($locky, 1, 120);
            if (!$lock_ret) {
                throw new \Exception("请求正在处理中，请稍后");
            };

            $packageBiz = new \Business\AppCenter\Package();
            //开通套餐前的检测  走新工程appcenterService
            $checkRes = $packageBiz->upgradePreCheck($memberId, $dtype, $upgradePackageId, $deductMoney, $payMoney, $productType, $payWay);
            if ($checkRes['code'] != 200) {
                throw new \Exception($checkRes['msg']);
            }

            //套餐角色
            $packageRole = $checkRes['data']['role'];
            //标题
            $subject = $checkRes['data']['subject'];
            //订单号
            $orderNo = $checkRes['data']['trade_no'];
            //费用 微信以分为单位
            $fee     = $checkRes['data']['fee'];

            if (empty($orderNo) || empty($fee)) {
                throw new \Exception("订单生成出错, 请重试");
            }

            //客服签单人员数据
            $saleInfo    = [];
            //套餐数据
            $packageData = [
                'origin_package_id'  => $originPackageId,
                'upgrade_package_id' => $upgradePackageId,
                'deduct_money'       => $deductMoney,
                'pay_money'          => $payMoney,
                'product_type'       => $productType,
                'pay_way'            => $payWay,
            ];

            $tmpRemarks = [
                'out_trade_no' => $orderNo,
                'member_id'    => $memberId,
                'package_data' => $packageData,
                'fee'          => $fee,
                'subject'      => $subject,
                'pay_type'     => 2,    //标识是购买模块
            ];

            $remarks = json_encode($tmpRemarks);

            if ($payWay == 1) {

                $handleAfterOpen = true;
                //平台账户余额支付
                $payRes = (new PaymentBiz())->platformPayModule($orderNo, $fee, $memberId, $subject);
                if ($payRes['code'] != 200) {
                    throw new \Exception($payRes['msg']);
                }

                $result .= '平台余额支付，';

                //开通套餐
                $openRes = $packageBiz->upgrade($memberId, $orderNo, $upgradePackageId, $productType, $saleInfo);
                //如果开通模块失败
                if ($openRes['code'] != 200) {
                    //记录余额支付异常日志
                    throw new \Exception($openRes['msg']);
                }

                //钉钉通知
                (new \Business\AppCenter\BaseCall())->dingdingSendOpenMsg($memberId, $subject . ($fee / 100) . "元", $orderNo, ($fee > 0 ? 0 : 1), 2); //升级套餐 余额
            } else {

                //默认支付方式：支付宝
                $sourceT = 0;
                $onLineTrade = new OnlineTrade();
                //微信支付或者支付宝支付
                if (defined('ENV') && in_array(ENV, ['PRODUCTION', 'TEST'])) {

                    if ($payWay == 2) {//微信
                        $getQrUrl = $this->_getWxPayQrCode($orderNo, $fee, $subject);
                        $sourceT = 1; //来源记录微信

                        $result .= '生成微信扫码支付，';
                    }

                    if ($payWay == 3) {//支付宝
                        $getQrUrl = $this->_getAliPayQrCode($orderNo, $fee, $subject);
                        $sourceT = 0; //来源记录支付宝

                        $result .= '生成支付宝扫码支付，';
                    }

                    if (empty($getQrUrl['code']) || $getQrUrl['code'] != 200) {
                        throw new \Exception($getQrUrl['msg']);
                    }

                    $url = $getQrUrl['url'];
                    //生成充值记录
                    $create = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception("充值记录生成失败");
                    }

                } else {

                    $handleAfterOpen = true;
                    //测试环境，模拟成功支付
                    $create = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception("充值记录生成失败");
                    }

                    //模拟支付成功
                    $tradeNo = 'online_' . time();
                    $res     = (new PaymentBiz())->onlinePayPackage($orderNo, $tmpRemarks, $tradeNo, 2);
                    if ($res['code'] != 200) {
                        throw new \Exception($res['msg']);
                    }

                    $result .= '测试模拟支付，';

                    //升级套餐
                    $openRes = $packageBiz->upgrade($memberId, $orderNo, $upgradePackageId, $productType, $saleInfo);
                    if ($openRes['code'] != 200) {
                        throw new \Exception($openRes['msg']);
                    }
                }
            }

            if (!empty($handleAfterOpen) && !empty($openRes['data'])) {
                $openData  = $openRes['data'];
                $handleRes = $packageBiz->handleAfterOpen($memberId, $upgradePackageId, $packageRole, $openData['begin_time'],
                    $openData['end_time'], $fee);

                if (!$handleRes) {
                    throw new \Exception($handleRes['msg']);
                }
            }

        } catch (\Exception $e) {

            $code = 400;
            $msg  = $e->getMessage();
        }

        if (!empty($locky)) {
            $cache->rm($locky);
        }

        //开通结果 日志
        if ($code == 200) {
            $result .= '成功';
        } else {
            $result .= '失败';
        }

        pft_log($this->_logDir,
            "{$requestId}:升级套餐结果：用户ID：{$memberId},  源套餐ID：{$originPackageId}, 升级套餐ID：{$upgradePackageId}, 
            抵扣金额：{$deductMoney}|支付金额：{$payMoney}|支付方式{$payWayStr}, 流水号{$orderNo}|结果|{$result}, 原因:{$msg}");

        $data = ['url' => $url, 'order_no' => $orderNo];
        $this->apiReturn($code, $data, $msg);
    }


    /**
     * 微信支付二维码
     *
     * @params  string      $orderNo    内部支付流水号
     * @params  integer     $fee        金额
     * @params  string      $subject    订单标题
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    private function _getWxPayQrCode(string $orderNo, int $fee, string $subject)
    {
        $code = 200;
        $msg  = '';
        $url  = '';

        if (!$orderNo || !$fee || !$subject) {
            return ['code' => 400, 'msg' => '', 'url' => ''];
        }

        $wxPayLib   = new WxPayLib(PFT_WECHAT_APPID);
        $parameters = $wxPayLib->qrPay(
            $fee,
            $subject,
            $orderNo,
            ModulePayment::WX_NOTICE_NOTIFY_URL,
            '微信二维码支付',
            'orderpay'
        );

        if ($parameters['return_code'] == 'SUCCESS') {
            $url = $parameters['code_url'];
        } else {
            $code = 500;
            $msg  = $parameters['return_msg'];
        }

        return ['code' => $code, 'msg' => $msg, 'url' => $url];
    }


    /**
     * 支付宝支付二维码
     *
     * @params  string      $orderNo    内部支付流水号
     * @params  integer     $fee        金额
     * @params  string      $subject     订单标题
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    private function _getAliPayQrCode(string $orderNo, int $fee, string $subject)
    {
        $code = 200;
        $msg  = '';
        $url  = '';

        if (!$orderNo || !$fee || !$subject) {
            return ['code' => 400, 'msg' => '', $url = ''];
        }

        //支付单位元
        $fee = $fee / 100;

        //生成二维码
        $f2fpay   = new F2FPay(PFT_ALIPAY_F2FPAY_ID);
        $response = $f2fpay->qrpay($orderNo, $fee, $subject, ModulePayment::ALI_NOTICE_NOTIFY_URL, $subject);

        if ($response->alipay_trade_precreate_response->code == 10000 && $response->alipay_trade_precreate_response->msg == 'Success') {
            $url = $response->alipay_trade_precreate_response->qr_code;
        }

        unset($f2fpay);

        if (empty($url)) {
            $code = 500;
            $msg  = "支付宝二维码生成错误, 请刷新页面";
        }

        return ['code' => $code, 'msg' => $msg, 'url' => $url];
    }


    /**
     * 用户购买记录
     * <AUTHOR>
     * @date 2020/12/18
     *
     */
    public function buyRecord()
    {
        $page = I('get.page', 1, 'intval');
        $size = I('get.page_size', 20, 'intval');

        if (!$page || !$size) {
            $this->apiReturn(400, [], '分页参数错误');
        }

        $result = (new PackageBiz())->getSelfBuyRecord($this->_fid, $page, $size);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 购买记录详情
     * <AUTHOR>
     * @date 2020/12/18
     *
     */
    public function buyRecordDetail()
    {
        $tradeNo = I('get.trade_no', 1, 'strval');
        if (!$tradeNo) {
            $this->apiReturn(400, [], '参数错误');
        }

        $result = (new PackageBiz())->getBuyDetail($this->_fid, $tradeNo);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    /**
     * 开通记录
     * <AUTHOR>
     * @date 2020/12/18
     *
     */
    public function openRecord()
    {
        $page = I('get.page', 1, 'intval');
        $size = I('get.page_size', 20, 'intval');

        if (!$page || !$size) {
            $this->apiReturn(400, [], '分页参数错误');
        }

        //模块id
        $result = (new PackageBiz())->getOpenRecord($this->_fid, $page, $size);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取用户限购配置详情
     * <AUTHOR>
     * @date 2021/9/6
     *
     */
    public function getUserLimitBuyInfo()
    {
        $targetId = I('get.target_id', 0, 'intval');

        $moduleRes = (new LimitBuyBiz())->getUserLimitBuyInfo($this->_fid, $targetId, 2);
        if ($moduleRes['code'] == 200) {
            $data = $moduleRes['data'];
            $this->apiReturn(200, $data);

        } else {
            $this->apiReturn($moduleRes['code'], [], $moduleRes['msg']);
        }
    }


    /**
     * 查询用户当前使用的套餐信息
     * <AUTHOR>
     * @date 2021/9/1
     *
     */
    public function queryUserCurrentPackageTime()
    {
        $result = (new PackageBiz())->getUserPackageInfoByApi([$this->_fid], 0);
        if ($result['code'] == 200) {
            $userPackageInfo = $result['data'][$this->_fid] ?? [];
            $packageData = [
                'package_id' => $userPackageInfo['package_id'] ?? 0,
                'begin_time' => $userPackageInfo['begin_time'] ?? 0,
                'end_time' => $userPackageInfo['end_time'] ?? 0,
            ];

            $this->apiReturn(200,$packageData);
        }

        $this->apiReturn(500, [], '获取用户套餐数据异常');
    }

    /**
     * 获取商户当前使用套餐配置的票属性配置
     * <AUTHOR>
     * @date   2022/10/11
     *
     */
    public function getPackageTicketAttr()
    {
        $sid = $this->_fid;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $result = (new PackageBiz())->getCurrentPackageTicketAttrConfig($sid);

        $this->apiReturn($result['code'] != 200 ? 500 : 200, $result['data'], $result['msg']);
    }

    /**
     * 购买套餐
     */
    public function platformBuyPackageQr()
    {
        //请求id
        $requestId = str_replace('.', '', microtime(true));
        $cache     = \Library\Cache\Cache::getInstance('redis');

        $loginInfo = $this->getLoginInfo();
        $memberId  = $loginInfo['memberID'];
        $dtype     = $loginInfo['dtype'];

        if (!in_array($dtype, [0, 1])) {
            $this->apiReturn(203, [], '身份不符,不允许开通');
        }

        //模块id
        $packageId   = I('post.package_id', 0, 'intval');
        //购买时长 1=年 2=季度 3=月
        $priceMode   = I('post.price_mode', 0, 'intval');
        //产品类型
        $productType = I('post.product_type', []);
        //支付方式 1支付宝 2微信 3余额
        $payWay      = I('post.pay_way', 0, 'intval');

        if (!$packageId || !$priceMode || !is_array($productType)) {
            $this->apiReturn(203, [], '参数错误');
        }

        if (empty($payWay) || !isset($this->_payTypeMap[$payWay])) {
            $this->apiReturn(203, [], '支付方式参数错误');
        }

        //支付方式描述
        $payWayStr = $this->_payTypeMap[$payWay];

        //加锁 防止恶意请求
        $locky    = "pay_package:{$payWayStr}:{$requestId}:$memberId";
        $lock_ret = $cache->lock($locky, 1, 120);
        if (!$lock_ret) {
            $this->apiReturn(203, [], '请求正在处理中，请稍后');
        }

        $packageBiz = new \Business\AppCenter\Package();
        //开通套餐前的检测  走新工程appcenterService
        $checkRes = $packageBiz->openPreCheck($memberId, $dtype, $packageId, $priceMode, $productType, $payWay);
        if ($checkRes['code'] != 200) {
            $this->apiReturn(203, [], $checkRes['msg']);
        }

        //套餐角色
        $packageRole = $checkRes['data']['role'];
        //标题
        $subject = $checkRes['data']['subject'];
        //订单号
        $orderNo = $checkRes['data']['trade_no'];
        //费用 微信以分为单位
        $fee     = $checkRes['data']['fee'];

        if (empty($orderNo) || !isset($fee)) {
            $this->apiReturn(203, [], '订单生成出错, 请重试');
        }

        //客服签单人员数据
        $saleInfo    = [];
        //套餐数据
        $packageData = [
            'package_id'    => $packageId,
            'price_mode'    => $priceMode,
            'product_type'  => $productType,
            'sale_info'     => $saleInfo,
            'role'          => $packageRole,
        ];

        $tmpRemarks = [
            'member_id'    => $memberId,
            'package_data' => $packageData,
            'fee'          => $fee,
            'subject'      => $subject,
            'pay_type'     => 2,    //标识是购买模块
            'pay_way'      => $payWay,  //1支付宝 2微信 3余额
        ];

        $remarks = json_encode($tmpRemarks);

        $data = [
            'outTradeNo' => $orderNo,
            'qrUrl'      => '',
        ];

        if ($payWay == 3) {
            $handleAfterOpen = true;
            //平台账户余额支付
            $payRes = (new PaymentBiz())->platformPayModule($orderNo, $fee, $memberId, $subject);
            if ($payRes['code'] != 200) {
                $this->apiReturn(204, [], $payRes['msg']);
            }

            //开通套餐
            $openRes = $packageBiz->openPackage($memberId, $packageId, $priceMode, $productType, $orderNo, $saleInfo);
            //如果开通模块失败
            if ($openRes['code'] != 200) {
                //记录余额支付异常日志
                $this->apiReturn(204, [], $openRes['msg']);
            }

            //钉钉通知
            (new \Business\AppCenter\BaseCall())->dingdingSendOpenMsg($memberId, $subject . ($fee / 100) . "元", $orderNo, ($fee > 0 ? 0 : 1), 2); //开通套餐 余额
        } else {
            //默认支付方式：支付宝
            $sourceT     = 0;
            $onLineTrade = new OnlineTrade();
            //微信支付或者支付宝支付
            if (defined('ENV') && in_array(ENV, ['PRODUCTION', 'TEST'])) {

                // 调用支付中心收银台页面
                $unifiedPay  = new UnifiedPay();
                $rpcResponse = $unifiedPay->unifyQrPayRpcService($orderNo, $subject, $fee, 1, $payWay,
                    'platform', self::PACKAGE_NOTIFY_URL, $_SERVER['REMOTE_ADDR'], '',
                    ['pft_member_id' => $memberId,'source' => 'platform_buy_package']);

                $ret = $onLineTrade->addLog($orderNo, $fee, $subject, $remarks, self::PACKAGE_SOURCET, OnlineTrade::PAY_METHOD_RECHARGE);
                if (!$ret) {
                    $this->apiReturn(204, [], '记录发生错误,请联系客服人员');
                }

                if ($rpcResponse['code'] != 200) {
                    $this->apiReturn(204, [], $rpcResponse['msg']);
                }

                $data = [
                    'outTradeNo' => $orderNo,
                    'qrUrl'      => $rpcResponse['data']['url'],
                ];
            } else {
                $handleAfterOpen = true;
                //测试环境，模拟成功支付
                $create = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);
                if (empty($create)) {
                    $this->apiReturn(204, [], '充值记录生成失败');
                }

                //模拟支付成功
                $tradeNo = 'online_' . time();
                $res     = (new PaymentBiz())->onlinePayPackage($orderNo, $tmpRemarks, $tradeNo, 2);
                if ($res['code'] != 200) {
                    $this->apiReturn(204, [], $res['msg']);
                }

                //开通套餐
                $openRes = $packageBiz->openPackage($memberId, $packageId, $priceMode, $productType, $orderNo, $saleInfo);
                if ($openRes['code'] != 200) {
                    $this->apiReturn(204, [], $openRes['msg']);
                }
            }
        }

        if (!empty($handleAfterOpen) && !empty($openRes['data'])) {
            $openData  = $openRes['data'];
            $handleRes = $packageBiz->handleAfterOpen($memberId, $packageId, $packageRole, $openData['begin_time'],
                $openData['end_time'], $fee);

            if (!$handleRes) {
                $this->apiReturn(204, [], $handleRes['msg']);
            }
        }

        $this->apiReturn(200, $data);
    }

    /**
     * 升级套餐
     */
    public function platformUpgradePackageQr()
    {
        //请求id
        $requestId = str_replace('.', '', microtime(true));
        $cache     = \Library\Cache\Cache::getInstance('redis');

        $loginInfo = $this->getLoginInfo();
        $memberId  = $loginInfo['memberID'];
        $dtype     = $loginInfo['dtype'];

        if (!in_array($dtype, [0, 1])) {
            $this->apiReturn(203, [], '身份不符,不允许开通');
        }

        //源套餐id
        $originPackageId  = I('post.origin_package_id', 0, 'intval');
        //升级套餐id
        $upgradePackageId = I('post.upgrade_package_id', 0, 'intval');
        //抵扣金额
        $deductMoney = I('post.deduct_money', 0, 'intval');
        //最终支付金额
        $payMoney    = I('post.pay_money', 0, 'intval');

        //产品类型
        $productType = I('post.product_type', []);
        //支付方式
        $payWay      = I('post.pay_way', 0, 'intval');

        if (!$originPackageId || !$upgradePackageId || !is_array($productType)) {
            $this->apiReturn(203, [], '参数错误');
        }

        if (empty($payWay) || !isset($this->_payTypeMap[$payWay])) {
            $this->apiReturn(203, [], '支付方式参数错误');
        }

        //支付方式描述
        $payWayStr = $this->_payTypeMap[$payWay];

        //加锁 防止恶意请求
        $locky    = "upgrade_package:{$payWayStr}:{$requestId}:{$upgradePackageId}:$memberId";
        $lock_ret = $cache->lock($locky, 1, 120);
        if (!$lock_ret) {
            $this->apiReturn(203, [], '请求正在处理中，请稍后');
        }

        try {
            $packageBiz = new \Business\AppCenter\Package();
            //开通套餐前的检测  走新工程appcenterService
            $checkRes = $packageBiz->upgradePreCheck($memberId, $dtype, $upgradePackageId, $deductMoney, $payMoney, $productType, $payWay);
            if ($checkRes['code'] != 200) {
                throw new \Exception($checkRes['msg']);
            }

            //套餐角色
            $packageRole = $checkRes['data']['role'];
            //标题
            $subject = $checkRes['data']['subject'];
            //订单号
            $orderNo = $checkRes['data']['trade_no'];
            //费用 微信以分为单位
            $fee     = $checkRes['data']['fee'];

            if (empty($orderNo) || empty($fee)) {
                throw new \Exception('订单生成出错, 请重试');
            }

            //客服签单人员数据
            $saleInfo    = [];
            //套餐数据
            $packageData = [
                'origin_package_id'  => $originPackageId,
                'upgrade_package_id' => $upgradePackageId,
                'deduct_money'       => $deductMoney,
                'pay_money'          => $payMoney,
                'product_type'       => $productType,
                'pay_way'            => $payWay,   //1支付宝 2微信 3余额
            ];

            $tmpRemarks = [
                'out_trade_no' => $orderNo,
                'member_id'    => $memberId,
                'package_data' => $packageData,
                'fee'          => $fee,
                'subject'      => $subject,
                'pay_type'     => 2,    //标识是购买模块
                'pay_way'      => $payWay,  //支付方式 1支付宝 2微信 3余额
            ];

            $remarks = json_encode($tmpRemarks);

            $data = [
                'outTradeNo' => $orderNo,
                'qrUrl'      => '',
            ];

            if ($payWay == 3) {
                $handleAfterOpen = true;
                //平台账户余额支付
                $payRes = (new PaymentBiz())->platformPayModule($orderNo, $fee, $memberId, $subject);
                if ($payRes['code'] != 200) {
                    throw new \Exception($payRes['msg']);
                }

                //开通套餐
                $openRes = $packageBiz->upgrade($memberId, $orderNo, $upgradePackageId, $productType, $saleInfo);
                //如果开通模块失败
                if ($openRes['code'] != 200) {
                    //记录余额支付异常日志
                    throw new \Exception($openRes['msg']);
                }

                //钉钉通知
                (new \Business\AppCenter\BaseCall())->dingdingSendOpenMsg($memberId, $subject . ($fee / 100) . "元", $orderNo, ($fee > 0 ? 0 : 1), 2); //升级套餐 余额
            } else {
                //默认支付方式：支付宝
                $sourceT     = 0;
                $onLineTrade = new OnlineTrade();
                //微信支付或者支付宝支付
                if (defined('ENV') && in_array(ENV, ['PRODUCTION', 'TEST'])) {

                    // 调用支付中心收银台页面
                    $unifiedPay  = new UnifiedPay();
                    $rpcResponse = $unifiedPay->unifyQrPayRpcService($orderNo, $subject, $fee, 1, $payWay,
                        'platform', self::PACKAGE_NOTIFY_URL, $_SERVER['REMOTE_ADDR'], '',
                        ['pft_member_id' => $memberId,'source' => 'platform_upgrade_package']);

                    $ret = $onLineTrade->addLog($orderNo, $fee, $subject, $remarks, self::PACKAGE_SOURCET, OnlineTrade::PAY_METHOD_RECHARGE);
                    if (!$ret) {
                        throw new \Exception('记录发生错误,请联系客服人员');
                    }

                    if ($rpcResponse['code'] != 200) {
                        $this->apiReturn(204, [], $rpcResponse['msg']);
                    }

                    $data = [
                        'outTradeNo' => $orderNo,
                        'qrUrl'      => $rpcResponse['data']['url'],
                    ];
                } else {
                    $handleAfterOpen = true;
                    //测试环境，模拟成功支付
                    $create = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception('充值记录生成失败');
                    }

                    //模拟支付成功
                    $tradeNo = 'online_' . time();
                    $res     = (new PaymentBiz())->onlinePayPackage($orderNo, $tmpRemarks, $tradeNo, 2);
                    if ($res['code'] != 200) {
                        throw new \Exception($res['msg']);
                    }

                    //升级套餐
                    $openRes = $packageBiz->upgrade($memberId, $orderNo, $upgradePackageId, $productType, $saleInfo);
                    if ($openRes['code'] != 200) {
                        throw new \Exception($openRes['msg']);
                    }
                }
            }

            if (!empty($handleAfterOpen) && !empty($openRes['data'])) {
                $openData  = $openRes['data'];
                $handleRes = $packageBiz->handleAfterOpen($memberId, $upgradePackageId, $packageRole, $openData['begin_time'],
                    $openData['end_time'], $fee);

                if (!$handleRes) {
                    throw new \Exception($handleRes['msg']);
                }
            }

        } catch (\Exception $e) {
            $cache->lock_rm($locky);
            $code = self::CODE_INVALID_REQUEST;
            $msg  = $e->getMessage();
            $this->apiReturn($code, [], $msg);
        }

        $this->apiReturn(200, $data);
    }

    /**
     * 收银台支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $this->isLogin('ajax');
        $ordernum = I('post.order_no');
        pft_log('yeepay/pay_result_check', json_encode($_POST, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        $model = new OnlineTrade();

        $payLog = $model->getLog($ordernum, self::PACKAGE_SOURCET);
        if (!$payLog) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
        }

        if ($payLog['status'] == 1) {
            parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看', true);
    }

    /**
     * 获取用户最后一个生效套餐的时间
     * @return void
     * @throws \Exception
     */
    public function getUserLastPackage() {
        $result = Container::pull(\Business\AppCenter\Package::class)
            ->getUserLastPackage($this->_loginInfo['sid'], true);
        parent::apiReturn($result['code'], $result['data'], $result['msg']);
    }
}