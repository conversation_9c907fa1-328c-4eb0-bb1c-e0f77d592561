<?php
/**
 * 应用中心模块列表页
 *
 * <AUTHOR>
 * @date   2016-12-07
 */
namespace Controller\AppCenter;
use Business\Advert\PlatAdvert;
use Business\AppCenter\Module;
use Business\AppCenter\Module as ModuleBiz;
use Library\Cache\Cache;
use Library\Constants\RedisKey\ModuleKeyConst;
use       Library\Controller;
use       Library\Exception;
use Library\Tools\BusinessCache;
use       Model\AppCenter;
use       Model\Member\Member;
use Model\Member\TradeJournal;

class ModuleList extends Controller
{

    private $_modeType     = null;
    private $_memberId     = null;
    private $_dtype        = null;
    private $_menuList     = null;
    private $_baseMenuList = null;
    private $_detailList   = null;
    private $_loginInfo    = null;
    // 模块配置信息模型
    private $_configModel;
    // 模块列表模型
    private $_listModel;


    public function __construct() {
        // 获取登陆用户session详情
        $this->_loginInfo = $this->getLoginInfo();
        $this->_memberId  = $this->_loginInfo['sid'];
        $this->_dtype     = $this->_loginInfo['sdtype'];
        // 加载配置信息
        $this->_modeType      = load_config('pay_mode', 'appcenter');
        $this->_menuList      = load_config('appcenter_menu', 'appcenter');
        $this->_baseMenuList  = load_config('menu_list', 'appcenter');
        $this->_detailList    = load_config('module_detail', 'appcenter');
    }

    /**
     * 实例化模块配置模型
     * @return AppCenter\ModuleConfig
     */
    private function _getConfigModel() {
        if (!$this->_configModel) {
            $this->_configModel = new AppCenter\ModuleConfig();
        }
        return $this->_configModel;
    }

    /**
     * 实例化模块列表模型
     * @return AppCenter\ModuleList
     */
    private function _getListModel() {
        if (!$this->_listModel) {
            $this->_listModel = new AppCenter\ModuleList();
        }
        return $this->_listModel;
    }



    /**
     * 付费模式接口提供
     * <AUTHOR>
     * @date   2016-12-12
     */
    public function payModeInfo() {
        $payMode = $this->_modeType;

        $this->apiReturn(200, $payMode, '');
    }




    /**
     * 获取应用中心首页的信息
     * <AUTHOR>
     * @date   2016-12-12
     * @deprecated
     * 不要调用  请调用新的 \Controller\AppCenter\Module::getList
     *
     * @params int    $type      页面类型(0=全部 1=新上线  2=未开通  3=已开通 4=分类)
     * @params int    $category  应用分类(分类查询 0=全部  1=直销模块 2=营销工具 3=对接平台 4=会员一卡通 5=票务系统 6=智能终端 7=其他)
     */
    public function getModuleList()
    {
        $moduleData = (new Module())->getList($this->_memberId, $this->_dtype, '',   true, false);
        if ($moduleData['code'] != 200) {
            $this->apiReturn($moduleData['code'], [], $moduleData['msg']);
        }

        //  button_type  0 => '免费试用', 1 => '开通', 2 => '使用'
        $returnData = [];
        foreach ($moduleData['data'] as $tmpData) {

            $buttonType = in_array($tmpData['is_expire'], [0, 1]) ? 2 : 1;
            if ($buttonType != 2) {
                $buttonType = $tmpData['free_day'] == 0 ? 1 : 0;
            }

            $returnData[] = [
                'name'        => $tmpData['module_name'],
                'module_id'   => $tmpData['module_id'],
                'url'         => $tmpData['link'],
                'button_type' => $buttonType,
            ];
        }
        $this->apiReturn(200, $returnData);
    }


    /**
     * 获取用户的过期套餐和模块列表
     * <AUTHOR>
     * @date   2019-1-12
     * @return array
     */
    public function getMemberPackModuleList(){

        pft_log('page_use/debug', '/r/AppCenter_ModuleList/getMemberPackModuleList is use');

        $list = [];
        $listModel  = new \Model\AppCenter\ModuleList();
        $data       = $listModel->getLastPackageByUser($this->_memberId);
        $moduleData = $listModel->getModuleStatusListByUid($this->_memberId,1,30,strtotime('+31 day'),true);
        $nowTimeStamp = time();
        $monthSeconds = 31* 24 *3600;
        if (!empty($data)){
            $packModuleInfos = $listModel->getModuleByTarget($data['package_id']);
            $packModuleIds   = array_column($packModuleInfos,'module_id');
        }else{
            $packModuleIds = [];
        }
        $buzCache = (new BusinessCache())->getBusinessCache($this->_memberId);
        if (!empty($data) && $data['is_ignore'] != 1){
            if ($data['end_time'] - $nowTimeStamp < $monthSeconds){
                $packInfo = $listModel->getInfoByPackageId($data['package_id']);
                if (!empty($packInfo)){
                    $packData = [
                        'id'         => $data['id'],
                        'name'       => $packInfo['name'],
                        'begin_time' => $data['begin_time'],
                        'end_time'   => $data['end_time'],
                        'is_pack'    => 1
                    ];
                    if ($nowTimeStamp > $data['end_time']){
                        $packData['is_expire'] = 1;
                    } else {
                        $packData['is_expire'] = 0;
                    }
                    $list[] = $packData;
                    //array_push($list,$packData);
                }
            }
        }elseif ($buzCache['renewJudge'] == -1 && empty($data)){
            $expirePackage = $listModel->getLastPackageByUserOne($this->_memberId);
            $packInfo = $listModel->getInfoByPackageId($expirePackage['package_id']);
            $packData = [
                'id'         => $expirePackage['id'],
                'name'       => $packInfo['name'],
                'begin_time' => $expirePackage['begin_time'],
                'end_time'   => $expirePackage['end_time'],
                'is_pack'    => 1,
                'is_expire'  => 1
            ];
            $list[] = $packData;
        }
        if (!empty($moduleData) && !empty($data)){
            $moduleIds  = array_column($moduleData,'module_id');
            //$moduleName = $listModel->getModuleName($moduleIds);
            $moduleCommonBiz = new \Business\AppCenter\ModuleCommon();
            $moduleName = $moduleCommonBiz->getModuleById($moduleIds);


            foreach ($moduleData as $key => $value){
                if ($value['module_id'] == 3 || ($nowTimeStamp - $value['expire_time'] > $monthSeconds) || in_array($value['module_id'],$packModuleIds)) {
                    continue;
                }
                foreach ($moduleName as $k => $v) {
                    if ($value['module_id'] == $v['id']) {
                        $moduleArr = [
                            'id'            => $v['id'],
                            'name'          => $v['name'],
                            'begin_time'    => $value['begin_time'],
                            'end_time'      => $value['expire_time'],
                            'is_pack'       => 0,
                            'icon'          => $v['icon']
                        ];
                        if ($listModel->checkModuleDiscount($v['id'])) {
                            $moduleArr['is_discount'] = 1;
                        } else {
                            $moduleArr['is_discount'] = 0;
                        }
                        if (time() > $value['expire_time']) {
                            $moduleArr['is_expire'] = 1;
                        } else {
                            $moduleArr['is_expire'] = 0;
                        }
                        $list[] = $moduleArr;
                        break;
                    }
                }
            }
        }
        $this->apiReturn('200', $list);
    }


    /**
     * 套餐配置的允许发布产品类型
     *
     * @param integer $packageId            套餐id
     *
     *
     * @return string
     */
    public function packageProductConfig()
    {
        $code = 200;
        $msg  = '';

        $returnData = [];
        $packageId  = I('get.package_id', 0);

        try {

            if (!$packageId) {
                throw new \Exception("添加套餐失败");
            }

            $productTypeDesc    = [];
            $packageProductType = $this->_getConfigModel()->getPackageProductConfig($packageId);

            $productType = isset($packageProductType[$packageId]) ? $packageProductType[$packageId] : [];

            if ($productType) {
                $productBiz      = new \Business\Product\Product();
                $productTypeDesc = $productBiz->getProductType($productType);
            }

            $returnData  = ['product_type' => $productTypeDesc];

        } catch (\Exception $e) {

            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $returnData, $msg);
    }


    /**
     * 获取用户已开通的模块列表，对模块进行分类返回（微商城）
     *
     * @return array
     * <AUTHOR>
     * @date 2020/10/1
     *
     *
     */
    public function getUserOpenModuleList()
    {
        pft_log('page_use/debug', '/r/AppCenter_ModuleList/getUserOpenModuleList is use');
        $module = new Module();
        $result = $module->getUserOpenModuleList($this->_memberId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}