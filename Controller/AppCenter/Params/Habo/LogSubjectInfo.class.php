<?php

namespace Controller\AppCenter\Params\Habo;

/**
 * @Author: CYQ19931115
 * @Date:   2017-09-28 13:27:32
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2017-10-09 14:38:26
 */
class LogSubjectInfo implements \JsonSerializable
{
    /** @var array 哈勃订单信息数组 json样例
    "CarNo": "闽DRV500",
    "State": 1,
    "Message": "成功",
    "Order": {
    "CarNo": "闽DRV500",
    "ParkId": "0B95306F-7AC2-4F70-BE20-F533A6829FC8",
    "ParkName": "123624",
    "Account": "123624",
    "CarImg": "",
    "Message": "外来车辆:闽DRV500|入场:09-07 14:32|停车:20天22小时56分|应收费:100元",
    "MemberType": "外来车辆",
    "InTime": "2017-09-07 14:32:48",
    "OutTime": "2017-09-28 13:28:20",
    "Price": "105.00",
    "Balance": "0.00",
    "PayPrice": "5.00",
    "NoPayPrice": "0.01"
    }
     */
    private $HaboOrderInfo;

    private function setHaboOrderInfo($HaboOrderInfo)
    {
        $this->HaboOrderInfo = $HaboOrderInfo;
    }

    public function getHaboOrderInfo()
    {
        return $this->HaboOrderInfo;
    }

    /** 
     * @var array member表的数据
     */
    private $userinfo;

    private function setUserinfo($userinfo)
    {
        $this->userinfo = $userinfo;
    }

    public function getUserinfo()
    {
        return $this->userinfo;
    }
    /**
     * 实例化
     * <AUTHOR>
     * @DateTime 2017-09-30T10:04:55+0800
     * @param    array $HaboOrderInfo 哈勃的停车信息
     * @param    array $userinfo 用户信息
     */
    public function __construct(array $HaboOrderInfo, array $userinfo)
    {
        if (!$userinfo["id"]) {
            throw new \Exception('用户id为必须的信息');
        }
        $userinfo      = [$userinfo["id"]];
        $HaboOrderInfo = [
            $HaboOrderInfo["Order"]['CarNo'],
            $HaboOrderInfo["Order"]['ParkId'],
            $HaboOrderInfo["Order"]['InTime'],
            $HaboOrderInfo["Order"]['OutTime'],
            $HaboOrderInfo["Order"]['NoPayPrice'],
        ];
        $this->setHaboOrderInfo($HaboOrderInfo);
        $this->setUserinfo($userinfo);
    }

    /**
     * 反序列化生成对象  记录在log的subject中
     * <AUTHOR>
     * @DateTime 2017-10-09T11:46:39+0800
     * @param    [type] $string 序列化对象
     * @return   LogSubjectInfo
     */
    public static function getObjBySerialize($string)
    {
        $unseri = json_decode($string, true);
        $userinfo=[
            "id"=>$unseri[0]
        ];
        $haboOrderInfo=[
            "Order"=>[
                "CarNo"=>$unseri[1],
                "ParkId"=>$unseri[2],
                "InTime"=>$unseri[3],
                "OutTime"=>$unseri[4],
                "NoPayPrice"=>$unseri[5],
            ]
        ];
        return new LogSubjectInfo($haboOrderInfo, $userinfo);
    }

    public function jsonSerialize()
    {
        return array_merge($this->getUserinfo(), $this->getHaboOrderInfo());
    }
}
