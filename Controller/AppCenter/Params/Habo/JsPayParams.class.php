<?php

namespace Controller\AppCenter\Params\Habo;

/**
 * @Author: CYQ19931115
 * @Date:   2017-09-27 09:54:48
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2017-09-27 10:04:39
 */
class JsPayParams
{
    /** @var float 支付的总共的金额 */
    private $totalFee;

    public function setTotalFee($totalFee)
    {$this->totalFee = $totalFee;}
    public function getTotalFee()
    {return $this->totalFee;}

    /** @var string 平台订单号 */
    private $outTradeNo;

    public function setOutTradeNo($outTradeNo)
    {$this->outTradeNo = $outTradeNo;}
    public function getOutTradeNo()
    {return $this->outTradeNo;}

    /** @var string 订单描述  */
    private $subject;

    public function setSubject($subject)
    {$this->subject = $subject;}
    public function getSubject()
    {return $this->subject;}

    /** @var string openid */
    private $openid;

    public function setOpenid($openid)
    {$this->openid = $openid;}
    public function getOpenid()
    {return $this->openid;}

    /** @var string 微信回调的url */
    private $notify_url;

    public function setnotifyUrl($notify_url)
    {$this->notify_url = $notify_url;}
    public function getnotifyUrl()
    {return $this->notify_url;}

    /**
     * <AUTHOR>
     * @DateTime 2017-09-27T10:01:26+0800
     * @param    [type] $totalFee 支付的总共的金额
     * @param    [type] $outTradeNo 平台订单号
     * @param    [type] $subject 描述信息
     * @param    [type] $openid openid
     * @param    [type] $notify_url 回调地址
     */
    public function __construct($totalFee, $outTradeNo, $subject, $openid, $notify_url)
    {
        $this->setTotalFee($totalFee);
        $this->setOutTradeNo($outTradeNo);
        $this->setSubject($subject);
        $this->setOpenid($openid);
        $this->setnotifyUrl($notify_url);
    }
}
