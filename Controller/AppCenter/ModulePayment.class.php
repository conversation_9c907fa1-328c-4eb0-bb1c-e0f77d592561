<?php
/**
 * 应用中心 模块支付
 *
 * <AUTHOR>
 * @date   2016-12-09
 */

namespace Controller\AppCenter;

use Business\AppCenter\Module;
use Business\AppCenter\ModuleCombine as ModuleCombineBiz;
use Business\AppCenter\Payment as PaymentBiz;
use Business\Member\Invoice;
use Jobby\Exception;
use Library\Business\Alipay\F2FPay;
use Library\Business\WePay\WxPayLib;
use Library\Business\WePay\WxPayNotifyResponse;
use Library\Controller;
use Library\Model;
use Library\Tools\Helpers;
use Model\AppCenter;
use Model\Member\Member;
use Model\Member\PftStaffModel;
use Model\TradeRecord\OnlineTrade;
use Model\TradeRecord\PftMemberJournal;
use Business\Member\Member as MemberBiz;
use Controller\AppCenter\ModuleList;
use Business\JavaApi\Product\ResourceCenterApi;
use WeChat\Pay\Notify_pub;
use Business\JsonRpcApi\PayService\UnifiedPay;

class ModulePayment extends Controller
{

    const ALI_NOTICE_NOTIFY_URL = PAY_DOMAIN . '/r/AppCenter_ModulePayment/afterAliPay';
    const WX_NOTICE_NOTIFY_URL = PAY_DOMAIN . '/r/AppCenter_ModulePayment/afterWxPay';
    //锁日志
    const LOKEY_LOG = '/appcenter/lock_log';
    //免费试用日志
    const PAY_ONLINE_FREE = '/appcenter/pay_online_free';
    //账户余额支付日志
    const PAY_ONLINE_PLA = '/appcenter/pay_online_pla';
    //支付宝下单日志
    const PAY_ONLINE_ALI = '/appcenter/pay_online_ali';
    //支付宝支付成功日志
    const PAY_ONLINE_ALI_OK = '/appcenter/pay_online_ali_ok';
    //支付宝支付失败日志
    const PAY_ONLINE_ALI_FAIL = '/appcenter/pay_online_ali_fail';
    //支付宝返回日志
    const PAY_ONLINE_ALI_RETURN = '/appcenter/pay_online_ali_return';
    //微信下单日志
    const PAY_ONLINE_WX = '/appcenter/pay_online_wx';
    //微信支付成功日志
    const PAY_ONLINE_WX_OK = '/appcenter/pay_online_wx_ok';
    //微信支付失败日志
    const PAY_ONLINE_WX_FAIL = '/appcenter/pay_online_wx_fail';
    //微信返回日志
    const PAY_ONLINE_WX_RETURN = '/appcenter/pay_online_wx_return';


    private $_modeType;

    //支付类型
    private $_payWay = [
        1 => '平台账户余额支付',
        2 => '微信支付',
        3 => '支付宝支付'
    ];

    //收银台支付类型
    private $_payTypeMap = [
        1 => '支付宝支付',
        2 => '微信支付',
        3 => '平台账户余额支付'
    ];

    //限制用户账号  [member_id => [moduleIds]]
    private $_limitModuleAccout = [
        '605468'  => [57], //账号
        '5436080' => [82, 83, 75, 78, 77], //用来测试
    ];

    const MODULE_NOTIFY_URL = PAY_DOMAIN.'/r/pay_CommonPayNotify/platformPackageNotify';
    const MODULE_SOURCET    = 41; // 与CommonPayNotify::platformBuyPackageNotify保持一致

    public function __construct()
    {
        //不要在__construct判断用户是否登陆 类里有支付宝的回调方法 会出错
        //付费模式
        $this->_modeType = load_config('pay_mode', 'appcenter');
    }


    /**
     * 支付宝支付成功后的回调方法
     * <AUTHOR>
     * @date   2016-12-13
     *
     */
    public function afterAliPay()
    {
        pft_log(self::PAY_ONLINE_ALI_RETURN, @json_encode($_REQUEST, JSON_UNESCAPED_UNICODE));

        $f2fPay = new F2Fpay(PFT_ALIPAY_F2FPAY_ID);

        $verifyResult = $f2fPay->verify($_POST, I('app_id'));

        $outTradeNo = $_POST['out_trade_no']; // 商户订单号
        $tradeNo = $_POST['trade_no']; // 支付宝交易号
        $tradeStatus = $_POST['trade_status']; // 交易状态

        if ($verifyResult) {
            //验证成功
            if ($tradeStatus == 'TRADE_FINISHED') {
                pft_log(self::PAY_ONLINE_ALI_OK, "1:$tradeNo:$outTradeNo:$tradeStatus");
            } elseif ($tradeStatus == 'TRADE_SUCCESS') {
                //判断该笔订单是否在商户网站中已经做过处理
                $payTotalFee = $_POST['total_fee'];
                if (isset($_POST['total_amount'])) {
                    $payTotalFee = $_POST['total_amount'];
                }
                //开通前的检测
                $onLineTradeModel = new OnlineTrade();
                $logInfo = $onLineTradeModel->getLogByOrderId($outTradeNo);

                if (empty($logInfo)) {
                    pft_log(self::PAY_ONLINE_ALI_FAIL, "1:$tradeNo:$outTradeNo:$tradeStatus|找不到记录");
                    exit('fail');
                }

                if ($logInfo['status'] == 1) {
                    pft_log(self::PAY_ONLINE_ALI_FAIL, "4:$tradeNo:$outTradeNo:$tradeStatus|该笔充值已处理");
                    exit('success');
                }

                if ($logInfo['total_fee'] != $payTotalFee) {
                    pft_log(self::PAY_ONLINE_ALI_FAIL, "5:$tradeNo:$outTradeNo:$tradeStatus|金额不正确");
                    exit('fail');
                }

                $payInfo = @json_decode($logInfo['description'], true);
                if (!$payInfo || !is_array($payInfo)) {
                    pft_log(self::PAY_ONLINE_ALI_FAIL, "6:$tradeNo:$outTradeNo:$tradeStatus|支付信息错误");
                    exit('fail');
                }

                //支付接口切换到JAVA
                $paymentBiz = new PaymentBiz();
                if ($payInfo['pay_type'] == 1) {
                    //开通应用
                    $res = $paymentBiz->onlinePayModule($outTradeNo, $payInfo, $tradeNo, 2);
                } elseif ($payInfo['pay_type'] == 2) {
                    //开通套餐
                    $res = $this->_openPackage($outTradeNo, $payInfo, $tradeNo);
                } elseif ($payInfo['pay_type'] == 3) {
                    //应用组合
                    $res = $this->_openCombine($outTradeNo, $payInfo, $tradeNo,2);
                } elseif ($payInfo['pay_type'] == 4) {
                    //增值服务
                    $paymentBiz->onlinePayVas($outTradeNo, $payInfo, $tradeNo, 2);

                } elseif ($payInfo['pay_type'] == 6) {
                    //开通资源包
                    $res = $paymentBiz->onLinePayResourcePack($outTradeNo, $payInfo, $tradeNo, 2);
                } else {
                    exit('fail');
                }

                if ($res['code'] == 200) {
                    pft_log(self::PAY_ONLINE_ALI_OK, "{$outTradeNo}|开通成功");
                    exit('success');
                } else {
                    pft_log(self::PAY_ONLINE_ALI_FAIL, "{$outTradeNo}|开通失败|{$res['msg']}");
                    exit('fail');
                }
            }
            pft_log(self::PAY_ONLINE_ALI_FAIL, "2:$tradeNo:$outTradeNo:$tradeStatus");
            //验证成功
            echo "success";
        } else {
            pft_log(self::PAY_ONLINE_ALI_FAIL, "3:$tradeNo:$outTradeNo:$tradeStatus");
            //验证失败
            echo "fail";
        }
    }


    /**
     * 开通应用组合
     *
     * <AUTHOR>
     * @date   2021-06-22
     *
     * @params $price_id  资费ID
     * @params $module_id 模块ID
     */
    private function _openCombine(string $outTradeNo, array $payInfo, string $tradeNo, $payType)
    {
        $payRes = (new PaymentBiz)->onlinePayCombine($outTradeNo, $payInfo, $tradeNo, $payType);
        if ($payRes['code'] != 200) {
            return $payRes;
        }

        $combineData = $payInfo['combine_data'];
        $memberId    = $payInfo['member_id'];
        $combineId   = $combineData['combine_id'];
        $priceMode   = $combineData['price_mode'];

        $openRes =  (new ModuleCombineBiz())->openCombine($memberId, $combineId, $priceMode, $outTradeNo);
        if ($openRes['code'] != 200) {
            return $openRes;
        }
    }



    /**
     * 开通应用
     *
     * <AUTHOR>
     * @date   2016-12-14
     *
     * @params $price_id  资费ID
     * @params $module_id 模块ID
     */
    private function _openPackage(string $outTradeNo, array $payInfo, string $tradeNo, int $payType = 2)
    {
        //写流水
        $payRes = (new PaymentBiz)->onlinePayPackage($outTradeNo, $payInfo, $tradeNo, $payType);
        if ($payRes['code'] != 200) {
            return $payRes;
        }

        //开通套餐
        $packageData = $payInfo['package_data'];
        $memberId    = $payInfo['member_id'];
        $packageId   = $packageData['package_id'];
        $packageBiz  = new \Business\AppCenter\Package();

        $openRes = $packageBiz->openPackage($memberId, $packageId, $packageData['price_mode'],
            $packageData['product_type'], $outTradeNo, $packageData['sale_info']);

        if ($openRes['code'] != 200) {
            return $openRes;
        }

        $openData  = $openRes['data'];
        $handleRes = $packageBiz->handleAfterOpen($memberId, $packageId, $packageData['role'], $openData['begin_time'],
            $openData['end_time'], $payInfo['fee']);

        if ($handleRes['code'] != 200) {
            return $handleRes;
        }

        return true;
    }



    /**
     * 微信支付成功后的回调方法
     * <AUTHOR>
     * @date   2016-12-14
     */
    public function afterWxPay()
    {
        $notify   = new WxPayNotifyResponse();
        //微信返回值
        $xml = file_get_contents('php://input');
        if (isset($_POST['notify_data'])) {
            $xml = $_POST['notify_data'];
        }
        pft_log(self::PAY_ONLINE_WX_RETURN, $xml);
        if (empty($xml)) {
            exit('Invalid Request!');
        }

        $notify->saveData($xml);
        $appId = isset($notify->data['sub_appid']) ? $notify->data['sub_appid'] : $notify->data['appid'];
        $WePayConf = include '/var/www/html/Service/Conf/WePay.conf.php';
        //订单号
        $outTradeNo = $notify->data['out_trade_no'];
        //微信订单号
        $tradeNo = $notify->data['transaction_id'];

        if ($notify->checkSign($WePayConf[$appId]['key']) == false) {
            $notify->SetReturn_code("FAIL"); //返回状态码
            $notify->SetReturn_msg("签名失败"); //返回信息
            pft_log(self::PAY_ONLINE_WX_FAIL, "【签名失败】:" . $xml);
            exit($notify->ToXml());
        } else {
            if ($notify->data["return_code"] == "FAIL") {
                pft_log(self::PAY_ONLINE_WX_FAIL, "【通信出错】:" . $xml);
                exit;
            } elseif ($notify->data["result_code"] == "FAIL") {
                pft_log(self::PAY_ONLINE_WX_FAIL, "【业务出错】:" . $xml);
                exit;
            } else {
                //金额用分为单位
                $payTotalFee = (int)$notify->data['total_fee'];

                //开通前的检测
                $onLineTradeModel = new OnlineTrade();
                $logInfo = $onLineTradeModel->getLogByOrderId($outTradeNo);

                if (empty($logInfo)) {
                    pft_log(self::PAY_ONLINE_WX_FAIL, "{$outTradeNo}|找不到记录");
                    exit('fail');
                }

                if ($logInfo['status'] == 1) {
                    pft_log(self::PAY_ONLINE_WX_FAIL, "{$outTradeNo}|该笔充值已处理");
                    exit('success');
                }

                if ($logInfo['total_fee'] * 100 != $payTotalFee) {
                    pft_log(self::PAY_ONLINE_WX_FAIL, "{$outTradeNo}|{$logInfo['total_fee']}|{$payTotalFee}|金额不正确");
                    exit('fail');
                }

                $payInfo = @json_decode($logInfo['description'], true);
                if (!$payInfo || !is_array($payInfo)) {
                    pft_log(self::PAY_ONLINE_WX_FAIL, "{$tradeNo}|{$outTradeNo}|支付信息错误");
                    exit('fail');
                }

                $paymentBiz = new PaymentBiz();
                if ($payInfo['pay_type'] == 1) {
                    //开通应用
                    $res = $paymentBiz->onlinePayModule($outTradeNo, $payInfo, $tradeNo, 3);
                } elseif ($payInfo['pay_type'] == 2) {
                    //开通套餐
                    $res = $this->_openPackage($outTradeNo, $payInfo, $tradeNo, 3);

                } elseif ($payInfo['pay_type'] == 3) {
                    //应用组合
                    $res = $this->_openCombine($outTradeNo, $payInfo, $tradeNo,3);

                } elseif ($payInfo['pay_type'] == 4) {
                    //增值服务
                    $paymentBiz->onlinePayVas($outTradeNo, $payInfo, $tradeNo, 3);

                } elseif ($payInfo['pay_type'] == 6) {
                    //开通资源包
                    $res = $paymentBiz->onLinePayResourcePack($outTradeNo, $payInfo, $tradeNo, 3);

                } else {
                    exit('fail');
                }

                if ($res['code'] == 200) {
                    pft_log(self::PAY_ONLINE_WX_OK, "{$outTradeNo}|开通成功");
                    exit('success');
                } else {
                    pft_log(self::PAY_ONLINE_WX_FAIL, "{$outTradeNo}|开通失败|{$res['msg']}");
                    exit('fail');
                }
            }
        }
    }

    /**
     * 模块免费试用
     * <AUTHOR>
     * @date   2016-12-12
     *
     * @params int        $module_id    模块ID
     * @params int        $type         类型    1功能模块  2套餐
     */
    public function freeTrial()
    {
        $code = 200;
        $msg = '';
        $cache = \Library\Cache\Cache::getInstance('redis');
        try {
            $loginInfo = $this->getLoginInfo();
            $dtype = $loginInfo['dtype'];
            if (!in_array($dtype, [0, 1])) {
                throw new \Exception("身份不符,不允许开通");
            }
            $memberId = $loginInfo['memberID'];

            //模块ID
            $moduleId = I('post.module_id');
            $type = I('post.type', 1);

            if (empty($moduleId)) {
                throw new \Exception("试用模块出错");
            }

            //加锁 防止恶意请求
            $locky = "freeTrial:$moduleId:$memberId";
            $lock_ret = $cache->lock($locky, 1, 120);
            if (!$lock_ret) {
                pft_log(self::LOKEY_LOG, "[$locky]操作频繁");
                throw new \Exception("请求正在处理中，请稍后");
            };

            //记录申请日志
            pft_log(self::PAY_ONLINE_FREE, "申请开通：用户ID:{$memberId}, 模块ID：{$moduleId}|免费试用");
            //插入数据
            $paymentModel = new \Business\AppCenter\Module();
            $res = $paymentModel->openModuleByFree($memberId, $loginInfo['sdtype'], $moduleId);

            if (empty($res)) {
                throw new \Exception("未知错误");
            } elseif (isset($res['code']) && $res['code'] != 200) {
                throw new \Exception($res['msg']);
            }
        } catch (\Exception $e) {
            $code = 400;
            $msg = $e->getMessage();
        }

        $cache->rm($locky);

        //开通结果 日志
        if ($code == 200) {
            $result = '成功';
        } else {
            $result = '失败';
        }

        pft_log(self::PAY_ONLINE_FREE, "申请结果：用户ID：{$memberId}, 模块ID：{$moduleId}|免费试用|{$result}, 原因:{$msg}");

        $this->apiReturn($code, '', $msg);
    }

    /**
     * 判断订单是否支付成功 用于支付页面跳转
     * <AUTHOR>
     * @date   2016-12-20
     *
     * @params string    $order_no
     */
    public function checkPayStatus()
    {
        try {
            $orderNo = I('post.order_no');

            if (empty($orderNo)) {
                throw new \Exception("订单号不能为空");
            }

            //在线交易记录表
            $onLineTradeModel = new OnlineTrade();
            $tradeRes = $onLineTradeModel->getLogByOrderId($orderNo);
            if (!empty($tradeRes) && isset($tradeRes['status']) && $tradeRes['status'] == 1) {
                $code = 200;
                $msg = 200;
            } else {
                $code = 202;
                $msg = '订单待支付';
            }
        } catch (\Exception $e) {
            $code = 400;
            $msg = $e->getMessage();
        }

        $this->apiReturn($code, '', $msg);
    }

    /**
     * 判断订单状态及更新后的订单信息
     * <AUTHOR>
     * @date   2016-12-21
     *
     * @params string   $order_no
     * @params int      $type 0 查询模块  1 查询套餐
     * @params
     */
    public function getOrderStatus()
    {
        $code = 200;
        $msg = '';
        $data = [];
        try {
            $this->isLogin('ajax');
            $loginInfo = $this->getLoginInfo('ajax', false, false);

            $orderNo = I('post.order_no');
            $type = I('post.type', 0, 'intval');

            if (empty($orderNo)) {
                throw new \Exception("订单号不能为空");
            }

            //在线交易记录表
            $modelPayment = new AppCenter\ModulePayment();
            $usedLog = $modelPayment->getOrderPayStatus($orderNo, $type);
            if (empty($usedLog)) {
                throw new \Exception("获取订单失败");
            }

            if ($type) {
                $data['begin'] = date('Y-m-d', $usedLog['begin_time']);
                $data['end'] = date('Y-m-d', $usedLog['end_time']);
                $packageId = $usedLog['package_id'];
                $listModel = new AppCenter\ModuleList();
                $packageInfo = $listModel->getInfoByPackageId($packageId);

                $data['name'] = empty($packageInfo['name']) ? '未知' : $packageInfo['name'];
                $data['fee'] = empty($packageInfo['fee']) ? 0 : $packageInfo['fee'] / 100;
            } else {
                //获取费用配置
                $modelConfig = new AppCenter\ModuleConfig();
                $tariffInfo = $modelConfig->getTariffById($usedLog['price_id']);
                $ModulBiz = new Module();
                $checkDiscountTime = $ModulBiz->checkDiscountTime($tariffInfo['discount_begin_time'], $tariffInfo['discount_end_time']);
                if ($checkDiscountTime) {
                    $data['fee'] = empty($tariffInfo['discount_price']) ? 0 : $tariffInfo['discount_price'] / 100;
                } else {
                    $data['fee'] = empty($tariffInfo['fee']) ? 0 : $tariffInfo['fee'] / 100;
                }

                //获取开通的模块名称
                $moduleInfo = $modelConfig->getModuleById($usedLog['module_id']);
                $data['name'] = empty($moduleInfo['name']) ? '未知' : $moduleInfo['name'];

                //获取有效期
                $uid = $loginInfo['memberID'];
                $mid = $usedLog['module_id'];

                $usedInfo = $modelConfig->getInfoByUidMid($uid, $mid);

                $data['begin'] = date('Y-m-d', $usedInfo['begin_time']);
                $data['end'] = date('Y-m-d', $usedInfo['expire_time']);

                $moduleUrl = load_config('module_url', 'appcenter');
                if (isset($moduleUrl[$mid])) {
                    $url = $moduleUrl[$mid][0];
                } else {
                    $moduleUrl = load_config('module_detail', 'appcenter');
                    if (isset($moduleUrl[$mid])) {
                        $url = $moduleUrl[$mid][0];
                    } else {
                        $url = '';
                    }
                }
                $data['url'] = $url;
            }
            //获取支付方式
            $tradeModel = new \Model\Finance\TradeRecord('slave');
            $orderInfo = $tradeModel->getOrderInfoInJournal($orderNo);
            $ptype = empty($orderInfo) ? '未知' : $orderInfo['ptype'];
            switch ($ptype) {
                case 0:
                    $data['pay'] = '余额支付';
                    break;
                case 1:
                    $data['pay'] = '支付宝';
                    break;
                case 4:
                    $data['pay'] = '微信';
                    break;
            }

        } catch (\Exception $e) {
            $code = 400;
            $msg = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 获取账户余额
     * <AUTHOR>
     * @date   2016-12-27
     */
    public function getBalance()
    {
        $balance = (new \Controller\Finance\WithDraw())->withDrawFre(true);
        $this->apiReturn(200, $balance, '');
    }



    /**
     * 根据订单号 获取套餐开通信息(订购成功页面)
     * @param int $order_no 订单号
     * <AUTHOR>
     * @date   2017-5-16
     *
     */
    public function getPackageStatusByOrderId()
    {
        $code = 200;
        $data = [];
        $msg = '';
        try {
            $orderNo = I('post.order_no');

            if (empty($orderNo)) {
                throw new \Exception("订单号错误");
            }

            $model = new AppCenter\ModulePayment();
            $res = $model->getPackageLogByOrderId($orderNo);
            if (empty($res)) {
                throw new \Exception("未找到该订单");
            }

            $data = [
                'add_time' => date('Y-m-d', $res['add_time']),
                'begin_time' => date('Y-m-d', $res['begin_time']),
                'end_time' => date('Y-m-d', $res['end_time']),
            ];

        } catch (\Exception $e) {
            $code = 400;
            $msg = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 根据 套餐开通记录 ID 获取外部交易流水号
     *
     * @param int $tid pft_module_package_log 表id
     * @return [
     *      'code' => 200 //  200正常 400不正常
     *      ‘msg’  => ''  //错误信息
     *      'data' => [
     *          'trade_no'     => ''// 外部交易流水号
     *          'invoice_no'   => ''// 发票编号
     *          'invoice_type' => ''// 发票类型
     *      ]
     * ]
     */
    public function getTradeNoOrInvoice()
    {
        $code = 200;
        $msg = '';
        $returnData = 0;
        try {
            $id = I('post.tid', 0, 'intval');
            //是否查看发票号
            $isInvoice = I('post.invoice', 0, 'intval');
            //1 套餐  2 模块
            $tradeType = I('post.type', 0, 'intval');

            if (empty($id)) {
                throw new \Exception("tid有误");
            }

            //登录人
            $loginInfo = $this->getLoginInfo();
            $sid = $loginInfo['sid'];

            //根据id和登录人id查询信息
            $model = new AppCenter\ModuleList();
            $info = $model->getInfoByIdInPackageLog($id);

            //取得订单号
            $orderNum = isset($info['out_trade_no']) ? $info['out_trade_no'] : 0;
            $memberId = isset($info['member_id']) ? $info['member_id'] : 0;

            if (empty($orderNum) || empty($memberId)) {
                throw new \Exception("未有订单号或者用户出错");
            }

            //取得交易记录信息
            $tradeModel = new \Model\Finance\TradeRecord('slave');
            $journalInfo = $tradeModel->getOrderInfoInJournal($orderNum, 'trade_no, ptype, dmoney');

            if (empty($journalInfo)) {
                throw new \Exception("未找到交易记录");
            }

            $tradeNo = isset($journalInfo['trade_no']) ? $journalInfo['trade_no'] : 0;

            $returnData = [
                'trade_no' => $tradeNo,
                'ptype' => $journalInfo['ptype'],
            ];

            if ($isInvoice) {
                if (empty($tradeType) || !in_array($tradeType, [1, 2])) {
                    throw new \Exception("请选择查看类型");
                }

                if ($tradeType == 1) {
                    //套餐
                    $tradeType = 26;
                } else {
                    //模块
                    $tradeType = 32;
                }

                //交易时间
                $transTime = date('Ymd', $info['add_time']);
                $money = $journalInfo['dmoney'];

                //获取发票信息
                $invoiceBusiness = new Invoice();
                $invoiceInfo = $invoiceBusiness->getOfflineInvoice($memberId, $transTime, $tradeType, $money);

                if ($invoiceInfo) {
                    $invoiceNo = isset($invoiceInfo['invoice_no']) ? $invoiceInfo['invoice_no'] : 0;
                    $invoiceType = isset($invoiceInfo['invoice_type']) ? $invoiceInfo['invoice_type'] : 0;

                    $returnData['invoice_no'] = $invoiceNo;
                    $returnData['invoice_type'] = $invoiceType;
                }
            }
        } catch (\Exception $e) {
            $code = 400;
            $msg = $e->getMessage();
        }

        $this->apiReturn($code, $returnData, $msg);
    }

    /**
     * 更新套餐交易记录或者发票
     *
     * @param int $tid pft_module_package_log表id
     * @param string $trade_no 外部交易流水号
     */
    public function updateJournal()
    {
        $code = 200;
        $msg = '';
        try {
            $id = I('post.tid', 0, 'intval');
            //更新类型 1更新交易记录 2更新发票编号类型
            $updateType = I('post.update_type', 1, 'intval');
            //外部交易号
            $tradeNo = I('post.trade_no', 0, 'strval');
            //发票类型
            $invoiceType = I('post.invoice_type', 0, 'intval');
            //发票编号
            $invoiceNum = I('post.invoice_no', 0, 'strval');
            //1 套餐  2 模块
            $tradeType = I('post.type', 0, 'intval');
            //打款到那个账户  默认使用线下打款
            $pType = I('post.ptype', 17, 'intval');

            if (empty($id)) {
                throw new \Exception("tid有误");
            }

            if (!in_array($updateType, [1, 2])) {
                throw new \Exception("更新类型错误");
            }

            if ($updateType == 1 && empty($tradeNo)) {
                throw new \Exception("交易流水号不能为空");
            }

            if ($updateType == 2 && empty($invoiceNum)) {
                throw new \Exception("发票编号不能为空");
            }

            //登录人
            $loginInfo = $this->getLoginInfo();
            $sid = $loginInfo['sid'];

            //根据id和登录人id查询信息
            $model = new AppCenter\ModuleList();
            $info = $model->getInfoByIdInPackageLog($id);

            //取得订单号
            $orderNum = isset($info['out_trade_no']) ? $info['out_trade_no'] : 0;
            $memberId = isset($info['member_id']) ? $info['member_id'] : 0;

            if (empty($orderNum)) {
                throw new \Exception("未有订单号");
            }

            if ($updateType == 1) {
                //根据订单号更新交易记录
                //$journalModel = new PftMemberJournal();
                //if (empty($pType)) {
                //    throw new \Exception("请在收取到用户的打款交易流水后，再进行开通套餐。");
                //} else {
                //    $updateRes = $journalModel->updateTradeNoAndPtypeByOrderId($orderNum, $tradeNo, $pType);
                //}
                //
                //if (empty($updateRes)) {
                //    throw new \Exception("更新失败");
                //}

                throw new \Exception("功能已经废弃");
            } else {
                //更新发票记录

                //获取价格
                $tradeModel = new \Model\Finance\TradeRecord('slave');
                $journalInfo = $tradeModel->getOrderInfoInJournal($orderNum, 'trade_no, dmoney');
                if (empty($journalInfo)) {
                    throw new \Exception("未找到交易记录");
                }

                $money = $journalInfo['dmoney'];
                $transTime = date('Ymd', $info['add_time']);
                if ($tradeType == 1) {
                    //套餐
                    $tradeType = 26;
                } else {
                    //模块
                    $tradeType = 32;
                }
                $invoiceBusiness = new Invoice();
                $invoiceBusiness->offlineInvoice($memberId, $transTime, $tradeType, $money, $invoiceType, $invoiceNum);
            }
        } catch (\Exception $e) {
            $code = 400;
            $msg = $e->getMessage();
        }

        $this->apiReturn($code, [], $msg);
    }


    /**
     * 用户购买应用模块，支付方式包括微信支付、支付宝支付、平台账户余额支付
     * 支持一次性购买多个应用，当支付方式是支付宝或微信支付此方法只返回支付
     * 二维码， 支付成功回调是afterAliPay和afterWxPay方法
     *
     *   处理流程：
     *      支付宝或微信支付：要购买应用的数据检测 => 生成支付二维码 => 支付成功回调 => 开通应用
     *      平台账户余额支付：要购买应用的数据检测 => 平台内支付 => 开通应用
     *
     *   其中 步骤 购买应用的数据检测和开通应用 是通过jsonRpc调用AppCenterService工程
     *
     *
     * @params  string      $orderNo    内部支付流水号
     * @params  integer     $fee        金额
     * @params  string      $subject    订单标题
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    public function buyModuleOnLine()
    {
        $code  = 200;
        $msg   = '';
        $url   = '';
        $result = '';

        $moduleIdStr = '';
        $priceIdStr  = '';
        $formatData  = [];

        //请求id
        $requestId = str_replace('.', '', microtime(true));
        $cache     = \Library\Cache\Cache::getInstance('redis');

        try {

            $loginInfo = $this->getLoginInfo();
            $memberId  = $loginInfo['memberID'];
            $dtype     = $loginInfo['dtype'];

            if (!in_array($dtype, [0, 1])) {
                throw new \Exception("身份不符,不允许开通");
            }

            //支付方式
            $payWay     = I('post.pay_way', 0, 'intval');
            $moduleData = I('post.module_data', []);
            //资源中心推广来源
            $resourceCenterSpreadChannel = I('post.rc_spread_channel', 0, 'intval'); //资源中心推广渠道标识为1-127

            //验证要开通的应用是否被限制了
            $moduleIdArr = array_column($moduleData, 'appid');
            $checkLimit  = $this->_checkLimitModuleByMemberId($memberId, $moduleIdArr);
            if (isset($checkLimit['code']) && $checkLimit['code'] != 200) {
                throw new \Exception($checkLimit['msg']);
            }

            if (empty($payWay) || !isset($this->_payWay[$payWay])) {
                throw new \Exception("支付方式参数错误");
            }

            if (empty($moduleData)) {
                throw new \Exception("请求参数错误");
            }

            foreach ($moduleData as $tmpModule) {
                if (empty($tmpModule['appid']) || !is_numeric($tmpModule['priceid'])) {
                    throw new \Exception("请求参数错误:" . json_encode($moduleData));
                }

//                //同业平台分销收费版和免费版开通限制
//                $sameRes = (new PaymentBiz())->_checkSameBusiness($memberId, (int)$tmpModule['appid'], 1);
//                if ($sameRes['code'] != 200) {
//                    throw new \Exception($sameRes['msg'], $sameRes['code']);
//                }

                $moduleIdStr .= $tmpModule['appid'] . ',';
                $priceIdStr  .= $tmpModule['priceid'] . ',';
                $formatData[] = ['moduleId' => $tmpModule['appid'], 'priceId' => $tmpModule['priceid']];
            }

            //支付方式描述
            $payWayStr = $this->_payWay[$payWay];
            //开通申请 日志
            pft_log(self::PAY_ONLINE_WX, "{$requestId}:申请开通：用户ID：{$memberId}, 模块ID：{$moduleIdStr},资费ID：{$priceIdStr}|支付方式: {$payWayStr}");

            //加锁 防止恶意请求
            $locky    = "pay_module:{$payWayStr}:{$requestId}:$memberId";
            $lock_ret = $cache->lock($locky, 1, 120);
            if (!$lock_ret) {
                pft_log(self::LOKEY_LOG, "[$locky]操作频繁");
                throw new \Exception("请求正在处理中，请稍后");
            };

            //开通模块前的数据检测  走新工程appcenterService
            $checkRes = (new Module())->openPreCheck($loginInfo['sid'], $dtype, $formatData, $payWay);
            if ($checkRes['code'] != 200) {
                throw new \Exception($checkRes['msg']);
            }

            //订单号
            $orderNo = $checkRes['data']['order_no'];
            //费用 微信以分为单位
            $fee     = $checkRes['data']['fee'];
            //标题
            $subject = $checkRes['data']['subject'];

            if (empty($orderNo) || !is_numeric($fee)) {
                throw new \Exception("订单生成出错, 请重试");
            }

            $tmpRemarks = [
                'out_trade_no'      => $orderNo,
                'member_id'         => $memberId,
                'module_data'       => $formatData,
                'fee'               => $fee,
                'subject'           => $subject,
                'pay_type'          => 1,    //标识是购买模块
                'rc_spread_channel' => $resourceCenterSpreadChannel//资源中心推广渠道标识
            ];

            $remarks = json_encode($tmpRemarks);

            if ($payWay == 1) {
                //平台账户余额支付
                $paymentBiz = new PaymentBiz();
                $payRes     = $paymentBiz->platformPayModule($orderNo, $fee, $memberId, $subject, $formatData);
                if ($payRes['code'] != 200) {
                    throw new \Exception($payRes['msg']);
                }

                //开通模块,这边需要传$formatData
                $openRes = (new Module())->setNotifyParams($resourceCenterSpreadChannel)->openModule($memberId, $formatData, $orderNo);
                //如果开通模块失败
                if ($openRes['code'] != 200) {
                    //记录余额支付异常日志
                    pft_log(self::PAY_ONLINE_PLA, "余额支付失败：" . json_encode([$memberId, $formatData, $orderNo], JSON_UNESCAPED_UNICODE));
                    throw new \Exception($openRes['msg']);
                }

                $result .= '平台余额支付，';

                //旧版钉钉通知
                (new \Business\AppCenter\BaseCall())->dingdingSendOpenMsg($memberId, $subject, $orderNo, ($fee > 0 ? 0 : 1), 1); //开通应用 余额

            } else {//微信支付或者支付宝支付
                //默认支付方式：支付宝
                $sourceT = 0;
                if (defined('ENV') && in_array(ENV, ['PRODUCTION', 'TEST'])) {

                    if ($payWay == 2) {//微信
                        $getQrUrl = $this->_getWxPayQrCode($orderNo, $fee, $subject);
                        $sourceT = 1; //来源记录微信

                        $result .= '生成微信扫码支付，';
                    }

                    if ($payWay == 3) {//支付宝
                        $getQrUrl = $this->_getAliPayQrCode($orderNo, $fee, $subject);
                        $sourceT = 0; //来源记录支付宝

                        $result .= '生成支付宝扫码支付，';
                    }

                    if (empty($getQrUrl['code']) || $getQrUrl['code'] != 200) {
                        throw new \Exception($getQrUrl['msg']);
                    }

                    $url = $getQrUrl['url'];

                    //生成充值记录
                    $onLineTrade = new OnlineTrade();
                    $create      = $onLineTrade->addRecord(
                        $orderNo,
                        $subject,
                        $fee,
                        '',
                        '',
                        $sourceT,
                        $remarks
                    );

                    if (empty($create)) {
                        throw new \Exception("充值记录生成失败");
                    }

                } else {

                    //测试环境，模拟成功支付 模拟支付：支付宝
                    $onLineTrade = new OnlineTrade();
                    $create      = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);

                    if (empty($create)) {
                        throw new \Exception("充值记录生成失败");
                    }

                    //模拟支付成功
                    $paymentBiz = new PaymentBiz();
                    $tradeNo    = 'online_' . time();
                    $res        = $paymentBiz->onlinePayModule($orderNo, $tmpRemarks, $tradeNo, 2);

                    if ($res['code'] != 200) {
                        throw new \Exception($res['msg']);
                    }

                    $result .= '测试模拟支付，';
                }
            }

        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();

            //异常报警
            if (in_array(ENV, ['PRODUCTION'])) {
                $memberModel = new Member('slave');
                //$memberName  = $memberModel->getMemberCacheById($memberId, 'dname');

                $queryParams = [[$memberId]];
                $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                    $queryParams);
                $memberName = '';
                if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                    $memberName = array_column($queryRes['data'], null, 'id')[$memberId]['dname'];
                }

                $moduleDataMsg = json_encode($moduleData, JSON_UNESCAPED_UNICODE);
                $message = [];
                $message[] = "用户ID:{$memberName}({$memberId})";
                $message[] = "开通模块{$moduleDataMsg}";
                if (!empty($orderNo)) {
                    $message[] = "订单号{$orderNo}";
                }
                if (!empty($subject)) {
                    $message[] = "{$subject}";
                }
                $message[] = "{$msg}";
                (new Module())->dingdingSendMsg($message);
            }
        }

        if (!empty($locky)) {
            $cache->rm($locky);
        }

        //开通结果 日志
        if ($code == 200) {
            $result .= '成功';
        } else {
            $result .= '失败';
        }

        $fee = $fee / 100;
        pft_log(self::PAY_ONLINE_WX,
            "{$requestId}:申请结果：用户ID：{$memberId}, 模块ID：{$moduleIdStr},资费ID：{$priceIdStr}, 费用{$fee}元, 流水号{$orderNo}|结果|{$result}, 原因:{$msg}");

        $data = ['url' => $url, 'order_no' => $orderNo];
        $this->apiReturn($code, $data, $msg);
    }



    /**
     * 微信支付二维码
     *
     * @params  string      $orderNo    内部支付流水号
     * @params  integer     $fee        金额
     * @params  string      $subject    订单标题
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    private function _getWxPayQrCode(string $orderNo, int $fee, string $subject)
    {
        $code = 200;
        $msg  = '';
        $url  = '';

        if (!$orderNo || !$fee || !$subject) {
            return ['code' => 400, 'msg' => '', 'url' => ''];
        }

        $wxPayLib   = new WxPayLib(PFT_WECHAT_APPID);
        $parameters = $wxPayLib->qrPay(
            $fee,
            $subject,
            $orderNo,
            self::WX_NOTICE_NOTIFY_URL,
            '微信二维码支付',
            'orderpay'
        );

        if ($parameters['return_code'] == 'SUCCESS') {
            $url = $parameters['code_url'];
        } else {
            $code = 500;
            $msg  = $parameters['return_msg'];
        }

        return ['code' => $code, 'msg' => $msg, 'url' => $url];
    }


    /**
     * 支付宝支付二维码
     *
     * @params  string      $orderNo    内部支付流水号
     * @params  integer     $fee        金额
     * @params  string      $subject     订单标题
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    private function _getAliPayQrCode(string $orderNo, int $fee, string $subject)
    {
        $code = 200;
        $msg  = '';
        $url  = '';

        if (!$orderNo || !$fee || !$subject) {
            return ['code' => 400, 'msg' => '', $url = ''];
        }

        //支付单位元
        $fee = $fee / 100;

        //生成二维码
        $f2fpay   = new F2FPay(PFT_ALIPAY_F2FPAY_ID);
        $response = $f2fpay->qrpay($orderNo, $fee, $subject, self::ALI_NOTICE_NOTIFY_URL, $subject);

        if ($response->alipay_trade_precreate_response->code == 10000 && $response->alipay_trade_precreate_response->msg == 'Success') {
            $url = $response->alipay_trade_precreate_response->qr_code;
        }

        unset($f2fpay);

        if (empty($url)) {
            $code = 500;
            $msg  = "支付宝二维码生成错误, 请刷新页面";
        }

        return ['code' => $code, 'msg' => $msg, 'url' => $url];
    }


    /**
     * 商家限制开通某些应用
     * <AUTHOR>
     * @date 2021/3/15
     *
     * @param  int  $memberId  商户id
     * @param  array  $moduleIdArr  正要开通的应用id数组
     *
     * @return array
     */
    private function _checkLimitModuleByMemberId(int $memberId, array $moduleIdArr)
    {
        if (isset($this->_limitModuleAccout[$memberId])) {
            $limitModule = is_array($this->_limitModuleAccout[$memberId]) ? $this->_limitModuleAccout[$memberId] : [];
            $limitCehck  = array_intersect($moduleIdArr, $limitModule);
            if (!empty($limitCehck)) {
                return ['code' => 400, 'msg' => '应用暂时无法开通'];
            }
        }
        return ['code' => 200, 'msg' => '允许开通'];
    }

    /**
     * 购买应用模块
     */
    public function platformBuyModuleQr()
    {
        //请求id
        $requestId = str_replace('.', '', microtime(true));
        $cache     = \Library\Cache\Cache::getInstance('redis');

        $loginInfo = $this->getLoginInfo();
        $memberId  = $loginInfo['memberID'];
        $dtype     = $loginInfo['dtype'];

        if (!in_array($dtype, [0, 1])) {
            $this->apiReturn(203, [], '身份不符,不允许开通');
        }

        //支付方式 1支付宝 2微信 3余额
        $payWay     = I('post.pay_way', 0, 'intval');
        $moduleData = I('post.module_data', []);
        //资源中心推广来源
        $resourceCenterSpreadChannel = I('post.rc_spread_channel', 0, 'intval'); //资源中心推广渠道标识为1-127

        //验证要开通的应用是否被限制了
        $moduleIdArr = array_column($moduleData, 'appid');
        $checkLimit  = $this->_checkLimitModuleByMemberId($memberId, $moduleIdArr);
        if (isset($checkLimit['code']) && $checkLimit['code'] != 200) {
            $this->apiReturn($checkLimit['code'], [], $checkLimit['msg']);
        }

        if (empty($payWay) || !isset($this->_payWay[$payWay])) {
            $this->apiReturn(203, [], '支付方式参数错误');
        }

        if (empty($moduleData)) {
            $this->apiReturn(203, [], '请求参数错误');
        }

        $moduleIdStr = '';
        $priceIdStr  = '';
        $formatData  = [];

        foreach ($moduleData as $tmpModule) {
            if (empty($tmpModule['appid']) || !is_numeric($tmpModule['priceid'])) {
                throw new \Exception("请求参数错误:" . json_encode($moduleData));
            }

//                //同业平台分销收费版和免费版开通限制
//                $sameRes = (new PaymentBiz())->_checkSameBusiness($memberId, (int)$tmpModule['appid'], 1);
//                if ($sameRes['code'] != 200) {
//                    throw new \Exception($sameRes['msg'], $sameRes['code']);
//                }

            $moduleIdStr .= $tmpModule['appid'] . ',';
            $priceIdStr  .= $tmpModule['priceid'] . ',';
            $formatData[] = ['moduleId' => $tmpModule['appid'], 'priceId' => $tmpModule['priceid']];
        }

        //支付方式描述
        $payWayStr = $this->_payWay[$payWay];

        if (empty($payWay) || !isset($this->_payTypeMap[$payWay])) {
            $this->apiReturn(203, [], '支付方式参数错误');
        }

        //支付方式描述
        $payWayStr = $this->_payTypeMap[$payWay];

        //加锁 防止恶意请求
        $locky    = "pay_module:{$payWayStr}:{$requestId}:$memberId";
        $lock_ret = $cache->lock($locky, 1, 120);
        if (!$lock_ret) {
            pft_log(self::LOKEY_LOG, "[$locky]操作频繁");
            $this->apiReturn(203, [], '请求正在处理中，请稍后');
        }

        try {

            //开通模块前的数据检测  走新工程appcenterService
            $checkRes = (new Module())->openPreCheck($loginInfo['sid'], $dtype, $formatData, $payWay);
            if ($checkRes['code'] != 200) {
                throw new \Exception($checkRes['msg']);
            }

            //订单号
            $orderNo = $checkRes['data']['order_no'];
            //费用 微信以分为单位
            $fee     = $checkRes['data']['fee'];
            //标题
            $subject = $checkRes['data']['subject'];

            if (empty($orderNo) || !is_numeric($fee)) {
                throw new \Exception('订单生成出错, 请重试');
            }

            $tmpRemarks = [
                'out_trade_no'      => $orderNo,
                'member_id'         => $memberId,
                'module_data'       => $formatData,
                'fee'               => $fee,
                'subject'           => $subject,
                'pay_type'          => 1,    //标识是购买模块
                'rc_spread_channel' => $resourceCenterSpreadChannel,//资源中心推广渠道标识
                'pay_way'           => $payWay,   //1支付宝 2微信 3余额
            ];

            $remarks = json_encode($tmpRemarks);

            $data = [
                'outTradeNo' => $orderNo,
                'qrUrl'      => '',
            ];

            if ($payWay == 3) {
                //平台账户余额支付
                $paymentBiz = new PaymentBiz();
                $payRes     = $paymentBiz->platformPayModule($orderNo, $fee, $memberId, $subject, $formatData);
                if ($payRes['code'] != 200) {
                    throw new \Exception($payRes['msg']);
                }

                //开通模块,这边需要传$formatData
                $openRes = (new Module())->setNotifyParams($resourceCenterSpreadChannel)->openModule($memberId, $formatData, $orderNo);
                //如果开通模块失败
                if ($openRes['code'] != 200) {
                    //记录余额支付异常日志
                    pft_log(self::PAY_ONLINE_PLA, "余额支付失败：" . json_encode([$memberId, $formatData, $orderNo], JSON_UNESCAPED_UNICODE));
                    throw new \Exception($openRes['msg']);
                }

                //旧版钉钉通知
                (new \Business\AppCenter\BaseCall())->dingdingSendOpenMsg($memberId, $subject, $orderNo, ($fee > 0 ? 0 : 1), 1); //开通应用 余额

            } else {
                //默认支付方式：支付宝
                $sourceT = 0;
                $onLineTrade = new OnlineTrade();
                //微信支付或者支付宝支付
                if (defined('ENV') && in_array(ENV, ['PRODUCTION', 'TEST'])) {

                    // 调用支付中心收银台页面
                    $unifiedPay  = new UnifiedPay();
                    $rpcResponse = $unifiedPay->unifyQrPayRpcService($orderNo, $subject, $fee, 1, $payWay,
                        'platform', self::MODULE_NOTIFY_URL, $_SERVER['REMOTE_ADDR'], '',
                        ['pft_member_id' => $memberId,'source' => 'platform_buy_module']);

                    $ret   = $onLineTrade->addLog($orderNo, $fee, $subject, $remarks, self::MODULE_SOURCET, OnlineTrade::PAY_METHOD_RECHARGE);
                    if (!$ret) {
                        throw new \Exception('记录发生错误,请联系客服人员');
                    }

                    if ($rpcResponse['code'] != 200) {
                        throw new \Exception($rpcResponse['msg']);
                    }

                    $data = [
                        'outTradeNo' => $orderNo,
                        'qrUrl'      => $rpcResponse['data']['url'],
                    ];
                } else {
                    //测试环境，模拟成功支付 模拟支付：支付宝
                    $onLineTrade = new OnlineTrade();
                    $create      = $onLineTrade->addRecord($orderNo, $subject, $fee, '', '', $sourceT, $remarks);

                    if (empty($create)) {
                        throw new \Exception('充值记录生成失败');
                    }

                    //模拟支付成功
                    $paymentBiz = new PaymentBiz();
                    $tradeNo    = 'online_' . time();
                    $res        = $paymentBiz->onlinePayModule($orderNo, $tmpRemarks, $tradeNo, 2);

                    if ($res['code'] != 200) {
                        throw new \Exception($res['msg']);
                    }
                }
            }

        } catch (\Exception $e) {
            $cache->lock_rm($locky);
            $code = self::CODE_INVALID_REQUEST;
            $msg  = $e->getMessage();
            $this->apiReturn($code, [], $msg);
        }

        $this->apiReturn(200, $data);
    }

    /**
     * 收银台支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $this->isLogin('ajax');
        $ordernum = I('post.order_no');
        pft_log('yeepay/pay_result_check', json_encode($_POST, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        $model = new OnlineTrade();

        $payLog = $model->getLog($ordernum, self::MODULE_SOURCET);
        if (!$payLog) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
        }

        if ($payLog['status'] == 1) {
            parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看', true);
    }
}
