<?php
namespace Controller\AppCenter;

use Business\Ota\HaboParkApiSender;
use Business\Ota\HaboPayBusiness;
use Controller\AppCenter\HaboDockApi;
use Controller\AppCenter\Params\Habo\LogSubjectInfo;
use Controller\pay\PayInterface;
use Library\Tools\Message;
use Process\Ota\HaboProcess;

/**
 * @Author: CYQ19931115
 * @Date:   2017-09-22 16:13:01
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2017-10-13 15:33:27
 */
class HaboDockPayApi extends HaboDockApi implements PayInterface
{

    /** @var Business\Ota\HaboPayBusiness 哈勃支付业务逻辑 */
    protected $payBusiness;

    /** @var string 用户登录的小程序部分信息 */
    private $sessionInfo;

    /** 通知的url */
    const NOTIFY_URL = MY_DOMAIN . "/r/AppCenter_HaboDockPayApi/orderNotify";

    public function __construct()
    {
        parent::__construct();
        //获取小程序的用户信息
        $this->sessionInfo = HaboProcess::getSmallLoginInfo($_SERVER['HTTP_SESSION_KEY']);
        //如果有的话就合并到请求参数中
        if ($this->sessionInfo) {
            $this->params = array_merge($this->params, $this->sessionInfo);
        }
        if ($this->params['appid']) {
            $this->payBusiness = HaboPayBusiness::getInstance(
                HaboProcess::getPayAppIdByParamsKeyIsPayAppid($this->params)
            );
            //设置消息发送者
            $this->payBusiness->setSender(new HaboParkApiSender());
        }
    }

    /**
     * 微信统一下单接口
     * <AUTHOR>
     * @DateTime 2017-09-26T16:22:19+0800
     * @return
     */
    public function order()
    {
        /** 下单操作 */
        try {
            if (!$this->payBusiness) {
                $this->ajaxReturn(Message::error("请传入appid"), "JSON");
            }

            /** @var Controller\AppCenter\Params\Habo\JsPayParams 获取需要的支付需要的参数 */
            $params = $this->payBusiness->getJsPayParams(
                $this->params,
                self::NOTIFY_URL
            );
            /** 下单并且记录用户信息以及哈勃信息到数据库log pft_alipay_rec*/
            $this->payBusiness->wechatJsPay($params, function ($data) use ($params) {
                //下单成功添加充值记录
                \pft_log("habo_pay", "js:信息：" . var_export($params, true));
                // HaboProcess::addRecord($params);
                HaboProcess::addLog(
                    $params,
                    new LogSubjectInfo(
                        $this->payBusiness->getHaboPayInfo($this->params["carNo"]), //habo支付返回的信息
                        $this->userinfo//当前用户的用户信息
                    )
                );
                $data['order_no'] = $params->getOutTradeNo();
                $this->ajaxReturn(Message::success($data, "获取成功"), "JSON");
            });
        } catch (\Exception $e) {
            $this->ajaxReturn(Message::error($e->getMessage()), "JSON");
        }
        //成功 返回下单信息
    }

    /**
     * 微信业务回调接口
     * 确认订单完成 吧订单log标记为已经支付
     * 发送支付完成的通知给哈勃
     * <AUTHOR>
     * @DateTime 2017-09-26T16:24:35+0800
     * @return
     */
    public function orderNotify()
    {
        ob_start();
        ob_end_clean(); //去除缓冲区的输出
        //请求太多次没有返回的话等处理完在解锁
        if (!HaboProcess::lockNotify($this->params['out_trade_no'])) {
            exit;
        }
        $backFlag = HaboProcess::notifyEchoSuccess();
        \pft_log("habo_pay", "哈勃停车场支付回调:" . var_export($this->params, true));
        if (!$this->payBusiness) {
            $this->ajaxReturn(Message::error("参数appid缺失"), "JSON");
        }
        $info = HaboProcess::getLog($this->params['out_trade_no']);
        try {
            /** 通知哈勃支付成功的消息 */
            $this->payBusiness->haboPayCallBack(
                /** 获取通知哈勃支付成功的消息包 */
                HaboProcess::getHaboPaySignPackage(
                    $this->params['out_trade_no'],
                    $this->params['transaction_id'],
                    $info
                ),
                function ($status) {
                    if (!$status) {
                        HaboProcess::payAlready($this->params['out_trade_no'], -1); //订单标记为哈勃回调失败 到时候从新回调
                    }
                }
            );

            //更新log记录为已经支付 账户资金要加上 {ps:不管是否回调成功}
            HaboProcess::updataLogInPaySuccess(
                $this->params['out_trade_no'], //平台订单号
                $this->params['transaction_id'], //外部订单号
                LogSubjectInfo::getObjBySerialize($info['subject']) //log记录 Controller\AppCenter\Params\Habo\LogSubjectInfo
            );
            if (HaboProcess::payAlreadyCheck($this->params['order_no']) !== -1) {
                HaboProcess::payAlready($this->params['out_trade_no'], -1);
            }
        } catch (\Exception $e) {
            \pft_log("habo_pay", "支付错误：" . $e->getMessage());
            $backFlag = HaboProcess::notifyEchoError();
        }
        echo $backFlag;
    }

    /**
     * 支付回调检查
     * <AUTHOR>
     * @DateTime 2017-09-26T16:24:45+0800
     * @return
     */
    public function payResultCheck()
    {
        $payStatus = HaboProcess::payAlreadyCheck($this->params['order_no']);
        if ($payStatus === false) {
            $this->ajaxReturn(Message::error("订单支付状态为待确定", 501), "JSON");
        } elseif ($payStatus === -1) {
            $this->ajaxReturn(Message::error("哈勃未收到支付通知，请点击刷新按钮", 502), "JSON");
        }
        //todo::检测支付状态
        $loginfo = HaboProcess::getLog(
            $this->params['order_no'],
            function ($status) {
                if ($status) {
                    HaboProcess::payAlreadyCacheDel($this->params['order_no']); //确认支付成功就删除缓存
                    $this->ajaxReturn(Message::success([], "支付成功"), "JSON");
                } else {
                    $this->ajaxReturn(Message::error("支付失败"), "JSON");
                }
            }
        );
    }

    /**
     * 哈勃主动调用校验
     * <AUTHOR>
     * @DateTime 2017-10-09T10:00:06+0800
     * @return   [type] [description]
     */
    public function payResultCheckHaboNotify()
    {
        $orderNo    = I("order_no");
        $submitSign = I("sign");
        $sign       = md5("$orderNo;http://api.12301.local/index.php?c=AppCenter_HaboDockPayApi&a=payResultCheckHaboNotify");
        if ($sign != $submitSign) {
            $this->ajaxReturn(Message::error("校验失败"), "JSON");
        }
        //todo::检测支付状态
        $loginfo = HaboProcess::getLog(
            $orderNo,
            function ($status) {
                $status ?
                $this->ajaxReturn(Message::success([], "支付成功"), "JSON") :
                $this->ajaxReturn(Message::error("支付失败"), "JSON");
            }
        );
    }

    public function micropay()
    {
        throw new \Exception('Method not implemented');
    }

    public function Query()
    {
        throw new \Exception('Method not implemented');
    }

    public function Refund()
    {
        throw new \Exception('Method not implemented');
    }

    public function rechargeNotify()
    {
        throw new \Exception('Method not implemented');
    }

    public function renew()
    {
        throw new \Exception('Method not implemented');
    }

    public function recharge()
    {
        throw new \Exception('Method not implemented');
    }
}
