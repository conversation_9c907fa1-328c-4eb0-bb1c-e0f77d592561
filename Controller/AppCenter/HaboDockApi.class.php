<?php
namespace Controller\AppCenter;

use Business\Ota\HaboBusiness;
use Business\Ota\HaboMessage\CreateHaboUserPackage;
use Business\Ota\HaboMessage\GetParkOrderPackage;
use Business\Ota\HaboMessage\GetSurplusPackage;
use Business\Ota\HaboMessage\IPackage;
use Business\Ota\HaboParkApiSender;
use Library\Controller;

/**
 *微信小程序后台对接的api
 */
class HaboDockApi extends Controller
{
    /** @var array 获取到的php input的参数 */
    protected $params = [];

    /** @var Business\Ota\HaboBusiness 业务逻辑类 */
    protected $business;

    /** @var array 用户信息 */
    protected $userinfo;

    /**
     * 构造函数
     * 初始化account
     * 以及缓存
     * <AUTHOR>
     * @DateTime 2017-09-21T18:29:27+0800
     */
    public function __construct()
    {
        if (ENV == "TEST") {
            ini_set("display_errors", 1);
            error_reporting(E_ERROR);
        }
        $this->business = HaboBusiness::getInstance();
        $this->business->setSender(new HaboParkApiSender());
        //安全的获取请求参数
        $this->params = $this->business->getParams();
        //通过scanCode获取小程序用户
        $this->userinfo = $this->business->getMemberInfoByScanCode(
            $this->params["scanCode"]
        );
        //生成habo的用户
        $this->business->haboUserGenerate(
            $this->userinfo
        );
    }

    /**
     * 获取Habo停车场的空余车位信息
     * <AUTHOR>
     * @DateTime 2017-09-18T17:37:00+0800
     * @return
     */
    public function getHaboParkSurplus()
    {
        $package = new GetSurplusPackage(
            $this->userinfo['account']
        );
        $this->_sender($package);
    }

    /**
     * 获取停车场的停车费用
     * <AUTHOR>
     * @DateTime 2017-09-19T16:54:44+0800
     * @return
     */
    public function getParkOrder()
    {
        $package = new GetParkOrderPackage(
            $this->params['carno']
        );
        $this->_sender($package, true, function (&$data) {
            $data['aid'] = $this->userinfo['id'];
        });
    }

    /**
     * 创建Habo用户
     * <AUTHOR>
     * @DateTime 2017-09-21T18:21:04+0800
     * @return   [type] [description]
     */
    public function createHaboUser()
    {
        $package = new CreateHaboUserPackage(
            $this->userinfo['account']
        );
        $this->_sender($package);
    }

    /**
     * controller 发送哈勃包
     * <AUTHOR>
     * @DateTime 2017-09-21T18:22:21+0800
     * @param    IPackage $package 数据包
     * @param    $httpResult 是否直接返回数据给html 或者函数形式的返回数据
     * @param    $closure  $data 附加处理函数
     *                           function(&$data){  .....   } $data是请求回来的参数
     * @return   返回数据请求到的数据  false为请求失败
     */
    protected function _sender(IPackage $package, $result = true, \Closure $closure = null)
    {
        try {
            $message = $this->business->sender($package, $closure);
            if ($result) {
                $this->ajaxReturn($message, "JSON");
            } else {
                return $message['data'];
            }
        } catch (\Exception $e) {
            $this->ajaxReturn($e->getMessage(), "JSON");
        }
    }
}
