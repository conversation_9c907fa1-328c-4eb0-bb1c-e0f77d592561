<?php
/**
 * 服务中心-精选服务
 * <AUTHOR>
 * @date 2020-10-26
 */

namespace Controller\AppCenter;

use Business\Authority\AuthLogic;
use Business\CommodityCenter\TerminalLand;
use Library\Cache\Cache;
use Library\Controller;
use Business\AppCenter\Service as ServiceBiz;

class Service extends Controller
{
    private static $_moveState = [1, 2]; //1是上移2是下移

    private $_loginInfo;
    private $_serviceBiz;

    public function __construct()
    {
        $this->_loginInfo  = $this->getLoginInfo();
        $this->_serviceBiz = new ServiceBiz();
    }

    /**
     * 获取精选服务信息
     * <AUTHOR>
     * @date 2020/10/28
     *
     */
    public function getChoiceData()
    {
        //获取上级用户id
        $sid    = $this->_loginInfo['sid'];
        $sdtype = $this->_loginInfo['sdtype'];

        $result = $this->_serviceBiz->getChoiceData($sid, $sdtype);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    /**
     * 应用中心到期服务通知数据
     * <AUTHOR>
     * @date 2021/10/22
     *
     */
    public function soonExpireNotify()
    {
        $sid      = $this->_loginInfo['sid'];
        $memberId = $this->_loginInfo['memberID'];
        $dtype    = $this->_loginInfo['dtype'];

        $notifySet = $this->_serviceBiz->soonExpireServiceNotify($memberId);
        $getModule  = in_array($dtype, [0, 1]) ? true : false;

        if (!empty($notifySet)) {
            foreach ($notifySet as $tmpSet) {
                if ($tmpSet['service_type'] == 1) {
                    //忽略全部
                   // $this->apiReturn(200, []);

                } elseif ($tmpSet['service_type'] == 2) {
                    //忽略套餐
                    $ignorePackage = $tmpSet['value'];

                } elseif ($tmpSet['service_type'] == 3) {
                    //忽略应用
                    $ignoreModuleId = $tmpSet['value'];
                }
            }
        }

        if (in_array($dtype, [2, 3])) {
            //资源账号
            $landInfo = (new TerminalLand())->terminalQuerySaleLandList(null, null, null, [$this->_loginInfo['saccount']]);
            if (empty($landInfo[0])) {
                $this->apiReturn(500, [], '查询资源账号信息接口错误');
            }

            $sid = $landInfo[0]['applyDid'];
        }

        $result = $this->_serviceBiz->getSoonExpireData($sid, true, $getModule);

        if (!empty($ignorePackage) && !empty($result['data']['package'])) {
            $packageLogId = $result['data']['package']['id'] ?? 0;
            if ($packageLogId && in_array($packageLogId, $ignorePackage)) {
                //$result['data']['package'] = [];
                unset($result['data']['package']);
            }
        }

        if (!empty($ignoreModuleId) && !empty($result['data']['module'])) {
            $moduleData =[];
            foreach ($result['data']['module'] as $tmpData) {
                !in_array($tmpData['id'], $ignoreModuleId) && $moduleData[] = $tmpData;
            }

            $result['data']['module'] = $moduleData;
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    /**
     * 应用中心服务到期弹窗忽略设置
     * <AUTHOR>
     * @date 2021/10/22
     *
     */
    public function soonExpireNotifySet()
    {
        $ignoreType = I('post.ignore_type/i', 0);
        $ignoreId   = (int) I('post.ignore_id');

        // 1=全部忽略 2=忽略套餐 3=忽略应用
        if (!in_array($ignoreType, [1, 2, 3])) {
            $this->apiReturn(400, [], 'type参数错误');
        }

        if (in_array($ignoreType, [2, 3]) && !$ignoreId) {
            $this->apiReturn(400, [], 'id参数错误');
        }

        if ($ignoreType == 1) {

            $getModule = in_array($this->_loginInfo['dtype'], [0, 1]) ? true : false;
            $sid = $this->_loginInfo['sid'];
            if (in_array($this->_loginInfo['dtype'], [2, 3])) {
                //资源账号
                $landInfo = (new TerminalLand())->terminalQuerySaleLandList(null, null, null, [$this->_loginInfo['saccount']]);
                if (empty($landInfo[0])) {
                    $this->apiReturn(500, [], '查询资源账号信息接口错误');
                }

                $sid = $landInfo[0]['applyDid'];
            }

            $result = $this->_serviceBiz->getSoonExpireData($sid, true, $getModule);

            if (empty($result['data'])) {
                $this->apiReturn(200);
            }

            if (!empty($result['data']['package'])) {
                $packageId = $result['data']['package']['id'] ?? 0;
                $setRes = $this->_serviceBiz->soonExpireServiceNotifySet($this->_loginInfo['memberID'], 2, [$packageId]);
            }

            if (!empty($result['data']['module'])) {
                $moduleIdList = array_column($result['data']['module'], 'id');
                $setRes = $this->_serviceBiz->soonExpireServiceNotifySet($this->_loginInfo['memberID'], 3, $moduleIdList);
            }

        } else {

            $setRes = $this->_serviceBiz->soonExpireServiceNotifySet($this->_loginInfo['memberID'], $ignoreType, [$ignoreId]);
        }

        $setRes ? $this->apiReturn(200) : $this->apiReturn(500, [], '设置失败');
    }


    /**
     * 应用中心服务到期弹窗忽略缓存清除
     * <AUTHOR>
     * @date 2021/10/22
     *
     */
    public function clearSoonExpireNotifyCache()
    {
        $memberId = I('get.member_id/i', 0);
        if ($memberId) {
            $this->_serviceBiz->soonExpireServiceNotifySetClear($memberId);
        }

        $this->apiReturn(200);
    }


    /**
     * 平台顶部导航栏
     * <AUTHOR>
     * @date 2021/10/22
     *
     */
    public function platTopBanner()
    {
        $sid      = $this->_loginInfo['sid'];
        $memberId = $this->_loginInfo['memberID'];

        //如果是新版权限体验用户，可以直接使用新版权限中心鉴权
        $isSupplier = $this->_loginInfo['dtype'] == 0;//标记下供应商
        $setRes     = (new AuthLogic())->getNavigationByUserId($sid, $memberId, $isSupplier, $this->_loginInfo['sdtype']);

        $this->apiReturn($setRes['code'], $setRes['data'], $setRes['msg']);
    }


    /**
     * 用户分销商限制信息
     * <AUTHOR>
     * @date 2021/06/15
     *
     */
    public function resellerLimitInfo()
    {
        $evoluteDistributorLimit = new \Business\JavaApi\Member\EvoluteDistributorLimit();
        $limitRes = $evoluteDistributorLimit->distributorLimitInfo($this->_loginInfo['sid']);

        if ($limitRes['code'] != 200) {
            $this->apiReturn(500, $limitRes, '获取限制信息失败:' . $limitRes['msg']);
        }

        $limitInfo = $limitRes['data'] ?? [];
        if (!isset($limitInfo['distributorTotal']) || !isset($limitInfo['limitTotal'])) {
            $this->apiReturn(500, $limitInfo, '分销商限制数据异常:' . $limitRes['msg']);
        }

        $data = [
            "current_num"     => $limitInfo['distributorTotal'],     //现有分销商总数
            "total_limit_num" => $limitInfo['limitTotal']
        ];

        $this->apiReturn(200, $data);
    }
}