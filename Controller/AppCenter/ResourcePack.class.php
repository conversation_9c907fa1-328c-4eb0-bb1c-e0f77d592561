<?php
/**
 * 资源中心资源包相关接口
 * User: 谢小勇
 * Date: 2021-07-27
 *
 */

namespace Controller\AppCenter;

use Library\Controller;
use Business\AppCenter\Payment as PaymentBiz;
use Model\TradeRecord\OnlineTrade;
use Library\Business\Alipay\F2FPay;
use Library\Business\WePay\WxPayLib;
use Business\JavaApi\Resource\ResourcePackOpen as ResourcePackOpen;
use Business\JavaApi\Resource\ResourcePack as ResourcePackBiz;
use Business\AppCenter\ResourcePack as ResourcePackService;
use Business\JsonRpcApi\PayService\UnifiedPay;

class ResourcePack extends Controller
{

    //登录信息
    private $_loginInfo;
    //主账号id
    private $_fid;

    private $_sDtype;

    //支付类型
    private $_payWay = [
        1 => '平台账户余额支付',
        2 => '微信支付',
        3 => '支付宝支付'
    ];

    //支付类型
    private $_payTypeMap = [
        1 => '支付宝支付',
        2 => '微信支付',
        3 => '平台账户余额支付'
    ];


    private $_logDir = 'resource_pack/user_buy/';

    const RESOURCE_PACKAGE_NOTIFY_URL = PAY_DOMAIN.'/r/pay_CommonPayNotify/platformPackageNotify';
    const RESOURCE_PACKAGE_SOURCET    = 41; // 与CommonPayNotify::platformBuyPackageNotify保持一致


    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo();
        $this->_fid     = $this->_loginInfo['sid'];
        $this->_sDtype  = $this->_loginInfo['sdtype'];
    }


    /**
     * 购买资源包
     * <AUTHOR>
     * @date 2021/07/18
     *
     */
    public function buyOnLine()
    {
        $code    = 200;
        $msg     = '';
        $url     = '';
        $result  = '';
        //日志记录结果
        $isRecordResult = false;


        //请求id
        $requestId = str_replace('.', '', microtime(true));
        $cache     = \Library\Cache\Cache::getInstance('redis');

        try {

            $loginInfo = $this->getLoginInfo();
            $memberId  = $loginInfo['memberID'];
            $dtype     = $loginInfo['dtype'];

            if (!in_array($dtype, [0, 1])) {
                throw new \Exception("身份不符,不允许开通");
            }

            //模块id
            $resourcePackId = I('post.pack_id', 0, 'intval');
            //支付方式
            $payWay   = I('post.pay_way', 0, 'intval');
            //支付金额
            $payMoney = I('post.fee', 0, 'intval');

            if (!$resourcePackId) {
                $this->apiReturn(204, [], '资源包id参数错误');
            }

            if (!$payMoney) {
                $this->apiReturn(204, [], '支付金额错误');
            }

            if (empty($payWay) || !isset($this->_payWay[$payWay])) {
                throw new \Exception("支付方式参数错误");
            }

            //获取资源包详情
            $getPackDetail = (new ResourcePackBiz())->resourcePackDetail($resourcePackId);
            if ($getPackDetail['code'] != 200 || empty($getPackDetail['data'])) {
                throw new \Exception("获取资源包数据失败：id=" . $resourcePackId);
            }

            //详情
            $packDetail = $getPackDetail['data'];
            //资源包名称
            $packName   = $packDetail['title'];
            //资源包价格
            $pricePrice = $packDetail['price'];

            if ($pricePrice != $payMoney) {
                throw new \Exception("资源包价格参数错误,应付 " . $pricePrice / 100);
            }

            //支付方式描述
            $payWayStr = $this->_payWay[$payWay];
            //开通申请 日志
            pft_log($this->_logDir, "{$requestId}:申请开通资源包，用户ID：{$memberId}, 资源包ID：{$resourcePackId},|支付方式：{$payWayStr}");

            //加锁 防止恶意请求
            $locky    = "pay_package:{$payWayStr}:{$requestId}:$memberId";
            $lock_ret = $cache->lock($locky, 1, 120);
            if (!$lock_ret) {
                throw new \Exception("请求正在处理中，请稍后");
            };

            $orderNo = 'app_' . str_replace('.', '', microtime(true));
            $subject = "开通资源包-$packName ,id:{$resourcePackId}, 资费:" . $payMoney / 100 . '元';

            $remarks = [
                'member_id'  => $memberId,
                'pack_id'    => $resourcePackId,
                'fee'        => $pricePrice,
                'subject'    => $subject,
                'pay_type'   => 6,    //标识是购买资源包
            ];

            $remarks = json_encode($remarks);
            if ($payWay == 1) {
                $isRecordResult = true;
                //平台账户余额支付
                $payRes = (new PaymentBiz())->platformPayModule($orderNo, $payMoney, $memberId, $subject);
                if ($payRes['code'] != 200) {
                    throw new \Exception($payRes['msg']);
                }

                //开通资源包
                $openRes = (new ResourcePackOpen())->openNotify($memberId, $resourcePackId, $orderNo, time(), $memberId);
                if ($openRes['code'] != 200) {
                    throw new \Exception('开通资源包失败:' . $openRes['msg']);
                }

                //开通资源中心应用
                $openResourceRes = ResourcePackService::openResourceAppByResourcePack($memberId, $orderNo);
                if ($openResourceRes['code'] != 200) {
                    throw new \Exception('开通资源中心应用失败:' . $openResourceRes['msg']);
                }

            } else {
                //默认支付方式：支付宝
                $sourceT = 0;
                $onLineTrade = new OnlineTrade();
                //微信支付或者支付宝支付
                if (defined('ENV') && in_array(ENV, ['PRODUCTION', 'TEST'])) {

                    if ($payWay == 2) {//微信
                        $getQrUrl = $this->_getWxPayQrCode($orderNo, $payMoney, $subject);
                        $sourceT = 1; //来源记录微信
                        $result .= '生成微信扫码支付，';
                    }

                    if ($payWay == 3) {//支付宝
                        $getQrUrl = $this->_getAliPayQrCode($orderNo, $payMoney, $subject);
                        $sourceT = 0; //来源记录支付宝
                        $result .= '生成支付宝扫码支付，';
                    }

                    if (empty($getQrUrl['code']) || $getQrUrl['code'] != 200) {
                        throw new \Exception($getQrUrl['msg']);
                    }

                    $url = $getQrUrl['url'];
                    //生成充值记录
                    $create = $onLineTrade->addRecord($orderNo, $subject, $payMoney, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception("充值记录生成失败");
                    }

                } else {
                    $isRecordResult = true;
                    //测试环境，模拟成功支付
                    $create = $onLineTrade->addRecord($orderNo, $subject, $payMoney, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception("充值记录生成失败");
                    }

                    $result .= '测试模拟支付，';
                    //开通资源包通知资源中心服务
                    $openRes = (new ResourcePackOpen())->openNotify($memberId, $resourcePackId, $orderNo, time(), $memberId);
                    if ($openRes['code'] != 200) {
                        throw new \Exception('开通资源包失败:' . $openRes['msg']);
                    }

                    //开通资源中心应用
                    $openResourceRes = ResourcePackService::openResourceAppByResourcePack($memberId, $orderNo);
                    if ($openResourceRes['code'] != 200) {
                        throw new \Exception('开通资源中心应用失败:' . $openResourceRes['msg']);
                    }
                }
            }

        } catch (\Exception $e) {

            $code = 400;
            $msg  = $e->getMessage();
        }

        if (!empty($locky)) {
            $cache->rm($locky);
        }

        //开通结果 日志
        if ($code == 200) {
            $result .= '成功';
        } else {
            $result .= '失败';
        }

        if ($isRecordResult) {
            $fee = $payMoney / 100;
            pft_log($this->_logDir,"{$requestId}:开通结果：用户ID：{$memberId}, 资源包ID：{$resourcePackId},|支付方式：{$payWayStr}, 费用{$fee}元, 流水号{$orderNo}|结果|{$result}, 原因:{$msg}");
        }

        $data = ['url' => $url, 'order_no' => $orderNo];
        $this->apiReturn($code, $data, $msg);
    }


    /**
     * 微信支付二维码
     *
     * @params  string      $orderNo    内部支付流水号
     * @params  integer     $fee        金额
     * @params  string      $subject    订单标题
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    private function _getWxPayQrCode(string $orderNo, int $fee, string $subject)
    {
        $code = 200;
        $msg  = '';
        $url  = '';

        if (!$orderNo || !$fee || !$subject) {
            return ['code' => 400, 'msg' => '', 'url' => ''];
        }

        $wxPayLib   = new WxPayLib(PFT_WECHAT_APPID);
        $parameters = $wxPayLib->qrPay(
            $fee,
            $subject,
            $orderNo,
            ModulePayment::WX_NOTICE_NOTIFY_URL,
            '微信二维码支付',
            'orderpay'
        );

        if ($parameters['return_code'] == 'SUCCESS') {
            $url = $parameters['code_url'];
        } else {
            $code = 500;
            $msg  = $parameters['return_msg'];
        }

        return ['code' => $code, 'msg' => $msg, 'url' => $url];
    }


    /**
     * 支付宝支付二维码
     *
     * @params  string      $orderNo    内部支付流水号
     * @params  integer     $fee        金额
     * @params  string      $subject     订单标题
     *
     * <AUTHOR>
     * @date   2020-10-15
     */
    private function _getAliPayQrCode(string $orderNo, int $fee, string $subject)
    {
        $code = 200;
        $msg  = '';
        $url  = '';

        if (!$orderNo || !$fee || !$subject) {
            return ['code' => 400, 'msg' => '', $url = ''];
        }

        //支付单位元
        $fee = $fee / 100;

        //生成二维码
        $f2fpay   = new F2FPay(PFT_ALIPAY_F2FPAY_ID);
        $response = $f2fpay->qrpay($orderNo, $fee, $subject, ModulePayment::ALI_NOTICE_NOTIFY_URL, $subject);

        if ($response->alipay_trade_precreate_response->code == 10000 && $response->alipay_trade_precreate_response->msg == 'Success') {
            $url = $response->alipay_trade_precreate_response->qr_code;
        }

        unset($f2fpay);

        if (empty($url)) {
            $code = 500;
            $msg  = "支付宝二维码生成错误, 请刷新页面";
        }

        return ['code' => $code, 'msg' => $msg, 'url' => $url];
    }

    /**
     * 购买资源包
     */
    public function platformBuyResourcePackageQr()
    {
        //请求id
        $requestId = str_replace('.', '', microtime(true));
        $cache     = \Library\Cache\Cache::getInstance('redis');

        $loginInfo = $this->getLoginInfo();
        $memberId  = $loginInfo['memberID'];
        $dtype     = $loginInfo['dtype'];

        if (!in_array($dtype, [0, 1])) {
            $this->apiReturn(203, [], '身份不符,不允许开通');
        }

        //模块id
        $resourcePackId = I('post.pack_id', 0, 'intval');
        //支付方式 1支付宝 2微信 3余额
        $payWay   = I('post.pay_way', 0, 'intval');
        //支付金额
        $payMoney = I('post.fee', 0, 'intval');

        if (!$resourcePackId) {
            $this->apiReturn(203, [], '资源包id参数错误');
        }

        if (!$payMoney) {
            $this->apiReturn(203, [], '支付金额错误');
        }

        if (empty($payWay) || !isset($this->_payTypeMap[$payWay])) {
            $this->apiReturn(203, [], '支付方式参数错误');
        }

        //获取资源包详情
        $getPackDetail = (new ResourcePackBiz())->resourcePackDetail($resourcePackId);
        if ($getPackDetail['code'] != 200 || empty($getPackDetail['data'])) {
            $this->apiReturn(203, [], "获取资源包数据失败：id=" . $resourcePackId);
        }

        //详情
        $packDetail = $getPackDetail['data'];
        //资源包名称
        $packName   = $packDetail['title'];
        //资源包价格
        $pricePrice = $packDetail['price'];

        if ($pricePrice != $payMoney) {
            $this->apiReturn(203, [], "资源包价格参数错误,应付 " . $pricePrice / 100);
        }

        //支付方式描述
        $payWayStr = $this->_payWay[$payWay];

        //加锁 防止恶意请求
        $locky    = "pay_package:{$payWayStr}:{$requestId}:$memberId";
        $lock_ret = $cache->lock($locky, 1, 120);
        if (!$lock_ret) {
            $this->apiReturn(203, [], '请求正在处理中，请稍后');
        }

        try {
            $orderNo = 'app_' . str_replace('.', '', microtime(true));
            $subject = "开通资源包-$packName ,id:{$resourcePackId}, 资费:" . $payMoney / 100 . '元';

            $remarks = [
                'member_id'  => $memberId,
                'pack_id'    => $resourcePackId,
                'fee'        => $pricePrice,
                'subject'    => $subject,
                'pay_type'   => 6,    //标识是购买资源包
                'pay_way'    => $payWay,   //1支付宝 2微信 3余额
            ];

            $remarks = json_encode($remarks);

            $data = [
                'outTradeNo' => $orderNo,
                'qrUrl'      => '',
            ];

            if ($payWay == 3) {
                //平台账户余额支付
                $payRes = (new PaymentBiz())->platformPayModule($orderNo, $payMoney, $memberId, $subject);
                if ($payRes['code'] != 200) {
                    throw new \Exception($payRes['msg']);
                }

                //开通资源包
                $openRes = (new ResourcePackOpen())->openNotify($memberId, $resourcePackId, $orderNo, time(), $memberId);
                if ($openRes['code'] != 200) {
                    throw new \Exception($openRes['msg']);
                }

                //开通资源中心应用
                $openResourceRes = ResourcePackService::openResourceAppByResourcePack($memberId, $orderNo);
                if ($openResourceRes['code'] != 200) {
                    throw new \Exception('开通资源中心应用失败:' . $openResourceRes['msg']);
                }

            } else {
                //默认支付方式：支付宝
                $sourceT     = 0;
                $onLineTrade = new OnlineTrade();
                //微信支付或者支付宝支付
                if (defined('ENV') && in_array(ENV, ['PRODUCTION', 'TEST'])) {

                    // 调用支付中心收银台页面
                    $unifiedPay  = new UnifiedPay();
                    $rpcResponse = $unifiedPay->unifyQrPayRpcService($orderNo, $subject, $payMoney, 1, $payWay,
                        'platform', self::RESOURCE_PACKAGE_NOTIFY_URL, $_SERVER['REMOTE_ADDR'], '',
                        ['pft_member_id' => $memberId,'source' => 'platform_buy_package']);

                    //生成充值记录
                    $ret   = $onLineTrade->addLog($orderNo, $payMoney, $subject, $remarks, self::RESOURCE_PACKAGE_SOURCET, OnlineTrade::PAY_METHOD_RECHARGE);
                    if (!$ret) {
                        throw new \Exception('记录发生错误,请联系客服人员');
                    }

                    if ($rpcResponse['code'] != 200) {
                        throw new \Exception($rpcResponse['msg']);
                    }

                    $data = [
                        'outTradeNo' => $orderNo,
                        'qrUrl'      => $rpcResponse['data']['url'],
                    ];

                } else {
                    //测试环境，模拟成功支付
                    $create = $onLineTrade->addRecord($orderNo, $subject, $payMoney, '', '', $sourceT, $remarks);
                    if (empty($create)) {
                        throw new \Exception('充值记录生成失败');
                    }

                    //开通资源包通知资源中心服务
                    $openRes = (new ResourcePackOpen())->openNotify($memberId, $resourcePackId, $orderNo, time(),
                        $memberId);
                    if ($openRes['code'] != 200) {
                        throw new \Exception('开通资源包失败:' . $openRes['msg']);
                    }

                    //开通资源中心应用
                    $openResourceRes = ResourcePackService::openResourceAppByResourcePack($memberId, $orderNo);
                    if ($openResourceRes['code'] != 200) {
                        throw new \Exception('开通资源中心应用失败:' . $openResourceRes['msg']);
                    }
                }
            }

        } catch (\Exception $e) {
            $cache->lock_rm($locky);
            $code = self::CODE_INVALID_REQUEST;
            $msg  = $e->getMessage();
            $this->apiReturn($code, [], $msg);
        }

        $this->apiReturn(200, $data);
    }

    /**
     * 收银台支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $this->isLogin('ajax');
        $ordernum = I('post.order_no');
        pft_log('yeepay/pay_result_check', json_encode($_POST, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        $model = new OnlineTrade();

        $payLog = $model->getLog($ordernum, self::RESOURCE_PACKAGE_SOURCET);
        if (!$payLog) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
        }

        if ($payLog['status'] == 1) {
            parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看', true);
    }
}