<?php
/**
 * 积分商城产品展示相关接口
 * <AUTHOR>
 */
namespace Controller\CreditsMall;

use Business\CreditsMall\Order;
use Business\CreditsMall\Product;
use Business\Mall\MemberVip;
use Controller\Mall\Mall;
use Library\Constants\ThemeConst;
use Business\CreditsMall\Points;
use Business\CreditsMall\Product as bizProduct;
use Business\CreditsMall\Manage;
use Business\JavaApi\TicketApi;
use Model\Product\Area;
use Model\Product\Ticket;
use Process\Resource\AreaFilter\AreaPinyinSettle;
use Process\Resource\AreaProcess;
use Business\JavaApi\StorageApi;

class PointsProduct extends Mall
{

    private $_sid;   // 商户ID

    private $_mid;   // 会员ID

    public function __construct() {
        parent::__construct();
        $this->_mid = $this->isLogin('ajax');
        $this->_sid = $this->_supplyId;
    }

    /**
     * 获取积分商城产品列表
     * 
     * <AUTHOR>
     * @date    2018-07-31
     */
    public function productList()
    {
        //产品类型
        $ptype = I('type', 'A');
        //上次查询的位置
        $page = I('page', 1, 'intval');
        //每页条数
        $pageSize = I('pageSize', 10, 'intval');
        // 产品关键字，无检索功能，不需要这个参数
        $keyword = '';
        // 主题
        $topic = I('topic', '');
        //城市代码
        $city = I('city', 0, 'intval');

        $bizProduct = new bizProduct($this->_sid);

        $listRes = $bizProduct->getProductList($ptype, $page, $pageSize, $keyword, $topic, $city);
        if ($listRes['code'] != 200) {
            $this->apiReturn($listRes['code'], [], $listRes['msg']);
        }

        // 查询当前账户的积分，并同步返回
        $vipBiz   = new MemberVip($this->_mid, $this->_sid);
        $pointRes = $vipBiz->getPoints();
        $points   = 0;
        if($pointRes['code'] == 200) {
            $points = $pointRes['data']['points'];
        }

        $return['list'] = $listRes['data'];
        $return['points'] = $points;
        //第一页的请求,前端需要缓存一些信息
        if ($page == 1) {
            $return['citys']  = $this->getAreaList(1);
            $return['themes'] = ThemeConst::THEMES;
        }

        $this->apiReturn(200, $return);
    }


    /**
     * 详情页-获取景区信息
     * <AUTHOR>
     * @date    2018-08-07
     * @param   intval  $lid  景区id
     */
    public function getLandInfo()
    {   
        $lid = I('lid', '', 'intval');
        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }
        
        $productBiz  = new bizProduct($this->_sid);
        $landInfoArr = $productBiz->getProductInfo($this->_sid, $lid, 0);
        
        if ($landInfoArr['code'] != 200) {
            $this->apiReturn(204, [], $landInfoArr['msg']);
        }

        if ($landInfoArr['data']['imgpathGrp']) {
        } else {
            $landInfoArr['data']['imgpathGrp'] = [];
        }
        $landInfoArr['data']['jqts'] = nl2br($landInfoArr['data']['jqts']);
        $landInfoArr['data']['jtzn'] = nl2br($landInfoArr['data']['jtzn']);
        $landInfoArr['data']['bhjq'] = htmlspecialchars_decode($landInfoArr['data']['bhjq']);
        $landInfoArr['data']['bhjq'] = \image_opt($landInfoArr['data']['bhjq'], 600);
        $landInfoArr['data']['venue_id'] = $landInfoArr['data']['venus_id'];
        
        $this->apiReturn(200, $landInfoArr['data']);
    }

    /**
     * 预定页面-日历价格
     * @return [type] [description]
     */
    public function getCalendarPrice()
    {
        $date = I('date', date('Y-m'));
        $tid  = I('tid', '', 'intval');

        if (!$tid || !$date) {
            $this->apiReturn(204, [], '参数错误');
        }

        // 计算请求的其实时间和结束时间
        $startDate = $date . '-01';
        $endDate   = $date . date('-t', strtotime($date));

        $calendar = TicketApi::getrRetailCalendar($this->_sid, $tid, $startDate, $endDate);

        $priceset  = [];
        foreach ($calendar as $key => $priceVal) {
            $priceset[$priceVal['time']] = $priceVal['lPrice'];
        }

        // 将金额转为积分数量
        $priceset = array_map(function ($val) {
            return $this->_changeRmbAmountToPoint($this->_sid, $val / 100);
        }, $priceset);

        $this->apiReturn(200, $priceset);
    }

    /**
     * 获取主题列表
     * @return [type] [description]
     */
    public function getThemes()
    {

        $config = $this->getMallConfig();
        $others = json_decode($config['others'], true);

        if (isset($others['themes'])) {
            if (!is_numeric(key($others['themes']))) {
                $return = [];
                foreach ($others['themes'] as $value) {
                    $return[] = $value['name'];
                }
            } else {
                $return = $others['themes'];
            }
        } else {

            $return = ThemeConst::THEMES;
        }

        return $return;
    }

    /**
     * 详情页-获取景区下的门票列表
     * 
     */
    public function getTicketList()
    {
        //景区id
        $lid = I('lid', 0, 'intval');
        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        // 从java获取了门票列表
        $productBiz = new bizProduct($this->_sid);
        $result = $productBiz->getLandTickets($lid);

        if (isset($result['code'])) {
            if ($result['code'] == 200) {
                $result['data'] = ['type' => 'A', 'list' => $result['data']];
            }
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 积分商品兑换记录
     */
    public function exchangeList(){
        $page   = I('page', 1, 'intval');
        $size   = I('size', 10, 'intval');

        $vipBiz  = new \Business\CreditsMall\Points($this->_mid, $this->_sid);

        $exchangeListRes = $vipBiz->getExchangeList($page, $size);

        $this->apiReturn(200, $exchangeListRes['data']);
    }

    /**
     * 预定页面-获取价格和库存
     * 
     * <AUTHOR>
     * @date    2018-08-07
     * @param   intval  $aid   供应商id
     * @param   string  $date  日期
     * @param   string  $tids  票id
     */
    public function getPriceAndStorage()
    {
        $aid    = I('aid', '', 'intval');
        $date   = I('date', date('Y-m-d'));
        $tids   = I('tids');
        $tidArr = explode('-', $tids);

        if (!$tidArr || !$date) {
            $this->apiReturn(201, [], '参数错误');
        }

        $date = date('Y-m-d', strtotime($date));

        if ($date < date('Y-m-d')) {
            $this->apiReturn(202, '请选择正确的日期');
        }

        $ticketStr = implode(',', $tidArr);

        //获取库存
        $storageMap = StorageApi::getBatchStorageByPlayDate($ticketStr, $date);

        if (empty($storageMap)) {
            $this->apiReturn(208, [], '获取库存信息出错，请稍后重试');
        }

        $productBiz  = new bizProduct($this->_sid);
        //获取价格
        $priceData = $productBiz->getPrice($aid, $date, $tidArr);

        if (!$priceData) {
            $this->apiReturn(205, [], '没有相关数据');
        }

        // 先利用tidArr获取pidArr
        $ticketModel = new Ticket();
        $tidPidArr   = $ticketModel->getMuchTicketInfo($tidArr,'id, pid');

        $return = [];
        foreach ($priceData as $tid => $item) {
            $store = $storageMap[$tid];
            $price = $this->_changeRmbAmountToPoint($this->_sid, $item['retail_price'] / 100);

            $return[$tidPidArr[$tid]] = [
                'point' => $price,
                'store' => $store >= -1 ? $store : 0,
            ];
        }

        $this->apiReturn(200, $return);
    }


    /**
     *  景区的预定信息
     * <AUTHOR>
     * @date   2018-08-07
     * 
     */
    public function getBookInfo() {
        $pid = I('pid', 0, 'intval');
        $aid = I('aid', 0 , 'intval');

        if ($pid < 1 || $aid < 0) {
            $this->apiReturn(204, [], '参数错误');
        }

        $productBiz = new bizProduct($this->_sid);
        $bookRes    = $productBiz->getBookInfo($pid, $aid);

        if (isset($bookRes['code']) && $bookRes['code'] == 200) {
            $this->apiReturn(200, $bookRes['data']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    public function getAreaList($result = false)
    {
        $area = new Area();

        $landInfo = AreaProcess::getAreaByMember($area, $this->_supplyId);
        AreaPinyinSettle::clearUp($landInfo);
        $landInfo = array_reverse($landInfo);
        $result   = [];
        foreach ($landInfo as $key => $value) {
            $result = array_merge($value, $result);
        }
        if ($result) {
            return $result;
        }
        $this->apiReturn(200, $result);
    }


    /**
     * 获取会员的积分总数
     */
    public function getPoint(){
        $vipBiz = new Points($this->_mid, $this->_sid);

        $pointRes = $vipBiz->getPoints();

        if($pointRes['code'] != 200) {
            $this->apiReturn(204, [], $pointRes['msg']);
        }

        $this->apiReturn(200, ['point' => $pointRes['data']['points']]);
    }

    /**
     * 查询对应供应商的积分汇率，并将商品金额转为积分数量
     * @param $aid
     * @param $amount 金额，单位为元
     * @return int
     */
    private function _changeRmbAmountToPoint($aid , $amount) {

        $pointBiz = new Points($this->_mid, $aid);
        $result = $pointBiz->changeRmbAmountToPoint($aid, $amount);

        if ($result['code'] == 200) {
            return $result['data'];
        } else {
            return 0;
        }
    }
}