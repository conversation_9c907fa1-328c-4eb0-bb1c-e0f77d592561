<?php

/**
 * 积分管理相关业务接口
 * 
 * <AUTHOR>
 * @date  	2018-07-31
 *
 */

namespace Controller\CreditsMall;

use Library\Controller;
use Business\CreditsMall\Manage;

/**
* 
*/
class PointsManage extends Controller
{	

	//请求参数
    private $_params = [];
	
	
	function __construct()
	{
		$this->_sid = $this->isLogin('ajax');
	}

	/**
	 * 获取积分管理设置数据
	 * 
	 * <AUTHOR>
	 * @date  	2018-07-31
	 *
	 */
	public function getPointsManage()
	{	
		$Manage = new Manage($this->_sid);
		//获取可使用积分与已消耗积分

		$result  = $Manage->getPointsConf();
		if ($result['code'] == 200) {
			//反序列化读出
			$result['data']['points_rule'] = unserialize($result['data']['points_rule']);
			$this->apiReturn($result['code'], $result['data'], $result['msg']);
		}
		//查询没有的话，生成默认数据并返回
		$list = $this->insertPoints();
		$list['points_rule'] = unserialize($list['points_rule']);
		$this->apiReturn(200, $list, '');
	}

	/**
	 * 初始生成积分管理数据
	 * 
	 * <AUTHOR>
	 * @date  	2018-07-31
	 *
	 */
	public function insertPoints()
	{	
		$Manage = new Manage($this->_sid);
		$this->_requestCheck();
		$result = $Manage->insertPoints($this->_params);
		if ($result['code'] == 200) {
			//返回的是生成后的默认数据
            return $result['data'];
        } else {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
	}

	/**
	 * 编辑积分管理配置信息
	 * 
	 * <AUTHOR>
	 * @date  	2018-07-31
	 * 
	 */
	public function editPointsInfo()
	{	
		$status   = I('status', '', 'intval');
		$exchange = I('exchange', '', 'intval');
		$rule     = I('rule', '', 'intval');

		$Manage = new Manage($this->_sid);
		//编辑存入的数组结构
		$data   = array('status' => $status, 'exchange_rate' => 100, 'points_rule' => $rule);
		$result = $Manage->editPointsInfo($data);

		if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
	}

	/**
	 * 默认参数定义
	 * <AUTHOR>
	 * @date  	2018-07-31
	 *
	 */
	private function _requestCheck()
	{
		$this->_params = [
	        'apply_id'      => (int)$this->_sid,
	        'status'     	=> (int)0,
	        'exchange_rate' => (int)100,
	        'workability'   => (int)0,
	        'consumed'      => (int)0,
	        'points_rule'   => (string)serialize(['1' => 100]),
	        'createtime'    => (string)time(),
	        'updatetime' 	=> (string)time(),
	    ];

	}
}