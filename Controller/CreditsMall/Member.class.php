<?php
namespace Controller\CreditsMall;

use Controller\Mall\Mall;
use Library\Controller;


/**
 * 会员相关接口
 * <AUTHOR>
 * @date  	2018-08-03
 *
 * 由于业务上还是有很多共同点，所以和微商城的Member继承同一个父类
 * Class Member
 * @package Controller\CreditsMall
 */
class Member extends Mall {

	/**
	 * 积分列表接口
	 */
	public function pointFlowList(){
		$page   = I('page', 1, 'intval');
		$size   = I('size', 10, 'intval');

		// 登陆会员id
		$mid = $this->isLogin('ajax');
		// 供应商id
		$sid = $this->_supplyId;

		$vipBiz  = new \Business\CreditsMall\Points($mid, $sid);

		$response = [];  // 应答结果

		$pointRes = $vipBiz->getPoints();
		if($pointRes['code'] != 200) {
			$this->apiReturn(204, [], '积分查询错误');
		}

		$pointFlowListRes = $vipBiz->getPointFlowList($page, $size);

		if($pointFlowListRes['code'] != 200) {
			$this->apiReturn(204, [], '积分详情查询错误');
		}

		$response['points']             = $pointRes['data']['points'];
		$response['point_flow_list']    = $pointFlowListRes['data']['list'];
		$response['total_record']       = $pointFlowListRes['data']['total_record'];

		$this->apiReturn(200, $response);
	}



}