<?php

namespace Controller\CreditsMall;

use Business\CreditsMall\Order as OrderBiz;
use Controller\Mall\Mall;

class Order extends Mall {

    public function __construct() {
        parent::__construct();
        $this->_mid = $this->isLogin('ajax');
    }


    /**
     * 提交订单
     * @return [type] [description]
     */
    public function order() {

        @pft_log('order/point_mall', json_encode($_REQUEST, JSON_UNESCAPED_UNICODE), $pathMode = 'day');

        //联系人手机号
        $mobile     = I('mobile', 0, 'intval');
        //联系人姓名
        $name       = I('name');
        //身份证数组
        $idcards    = I('idcards');
        //身份证姓名数组
        $tourists   = I('tourists');
        //门票pid
        $pid        = I('pid', 0, 'intval');
        //微商城所属供应商的上级供应商id
        $upAid      = I('aid', 0, 'intval');
        //购买张数
        $tnum       = I('tnum', 0, 'intval');
        //下单时间 yyyy-mm-dd
        $date       = I('date', date('Y-m-d'));
        //订单备注
        $memo       = I('memo', '', 'trim');

        if(empty($mobile)) {
            $this->apiReturn(204, [], '手机号不能为空');
        }

        if(empty($name)) {
            $this->apiReturn(204, [], '请填写姓名');
        }
        if(empty($pid)) {
            $this->apiReturn(204, [], 'pid缺失');
        }
        if(empty($upAid)) {
            $this->apiReturn(204, [],'aid缺失');
        }
        if(empty($tnum)) {
            $this->apiReturn(204, [], '请选择门票数量');
        }
        if(empty($date)) {
            $this->apiReturn(204,[], '请选择游玩日期');
        }

        $request = [
            'mobile'    => $mobile,
            'name'      => $name,
            'idcards'   => $idcards ?: [],
            'tourists'  => $tourists ?: [],
            'pid'       => $pid,
            'up_aid'    => $upAid,
            'tnum'      => $tnum,
            'date'      => $date,
            'memo'      => $memo
        ];

        // 测试用数据
        // $request = [
        //     'mobile' => 13123196340,
        //     'name' => '翁彬',
        //     'idcards' => ['350181199106012339'],
        //     'tourists' => ['aa'],
        //     'pid' => 174690,
        //     'up_aid' => 3385,
        //     'tnum' => 1,
        //     'date' => '2018-08-22',
        //     'memo' => ''
        // ];

        $orderBiz = new OrderBiz($this->_supplyId);
        $orderRes = $orderBiz->order($this->_mid, $request);

        if($orderRes['code'] == 200) {
            $this->apiReturn(200, ['orderid' => $orderRes['data']]);
        }else{
            $this->apiReturn(204, [], $orderRes['msg']);
        }
    }

    /**
     *  积分兑换 完成页面接口
     * <AUTHOR>
     * @date   2018-08-22
     */
    public function exchangeComplete() {

        $ordernum = I('ordernum');

        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }

        $orderBiz  = new orderBiz($this->_supplyId);
        $orderRes  = $orderBiz->exchangeComplete($this->_mid, $ordernum);

        if (isset($orderRes['code'])) {
            $this->apiReturn($orderRes['code'], $orderRes['data']);
        } else {
            $this->apiReturn(204, [], $orderRes['msg']);
        }
    }

}