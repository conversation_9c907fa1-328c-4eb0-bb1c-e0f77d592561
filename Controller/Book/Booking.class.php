<?php
/**
 * B端产品预定页接口
 *
 * User: Jason
 * Date: 2017/4/24
 * Time: 11:20
 */

namespace Controller\Book;

use Business\Order\Query;
use Business\Site\PaperTicketManage;
use Library\Controller;
use Library\SimpleExcel;
use Library\Tools\Helpers;
use Model\Product\PaperTicket;
use \Model\Member\FrequentContacts;

class Booking extends Controller
{

    private $_bookingBusiness;

    public function __construct()
    {
        $this->_bookingBusiness = new \Business\Order\Booking();
    }

    /**
     * 预订导入身份证信息
     */
    public function convertIdCardExcel()
    {
        $callBack    = I('post.callback', '', 'strval');
        $productType = I('post.product_type', '', 'strval');
        $version     = I('post.version', 'v1', 'strval');
        if (!$callBack) {
            $this->_fileUploadReturn($callBack, parent::CODE_NO_CONTENT, '缺少回调方法名');
        }

        if ($_FILES && isset($_FILES['convert_excel'])) {
            $file = $_FILES['convert_excel'];

            //加个判断文件大小不能超过5M
            if ($file['size'] > 5242880) {
                $this->_fileUploadReturn($callBack, parent::CODE_NO_CONTENT, '文件大小不能超过5M');
            }

            $this->_requireExcelFile();

            $fileExtent = $this->_getExtension($file['name']);
            if (!in_array($fileExtent, ['xls', 'xlsx','csv'])) {
                $this->_fileUploadReturn($callBack, parent::CODE_NO_CONTENT, '文件错误');
            }
            $return = [];
            try {
                $return     = $this->_readExcel($file['tmp_name'], $fileExtent, $productType, $version);
            } catch (\Exception $e) {
                $this->_fileUploadReturn($callBack, parent::CODE_NO_CONTENT, '无法识别的文件格式');
            }
            if (empty($return)) {
                $this->_fileUploadReturn($callBack, parent::CODE_NO_CONTENT, '身份证格式不正确或数据为空');
            }

            $return = array_values($return);
            $this->_fileUploadReturn($callBack, parent::CODE_SUCCESS, '导入成功', $return);

        }
        $this->_fileUploadReturn($callBack, parent::CODE_NO_CONTENT, '文件错误');
    }

    /**
     * 生成兑换码
     * @Author: zhujb
     * 2019/3/6
     */
    public function generateExchangeCode()
    {
        $tid             = I('get.tid', 0, 'intval');
        $fid             = I('get.fid', 0, 'intval');
        $price           = I('get.price', 0, 'intval');
        $count           = I('get.count', 0, 'intval');
        $entityBeginCode = I('get.entity_begin_code', '', 'strval');
        $entityEndCode   = I('get.entity_end_code', '', 'strval');
        $time            = I('get.expiretime');
        $sign            = I('get.sign');
        $isCloudExport   = I('get.is_cloud_export', 0, 'intval');
        $unStorage       = I('get.un_storage', 0, 'intval');
        $opId            = I('get.op_id', 0, 'intval');

        $checkSign = md5("{$tid}{$fid}{$price}{$count}{$time}");
        if ($sign != $checkSign) {
            exit('安全校验失败');
        }
        if (time() >= $time) {
            echo '链接已失效';
            exit;
        }

        $paperTicketBiz   = new PaperTicketManage($fid, $opId, 0, 1);
        $paperTicketModel = new PaperTicket();

        if (empty($unStorage)) {
            // 号段入库操作
            $ticketData       = [
                [
                    'tid'               => $tid,
                    'entity_begin_code' => $entityBeginCode,
                    'entity_end_code'   => $entityEndCode,
                ]
            ];

            if (!empty($opId)) {
                $storage = $paperTicketBiz->storagePaperTicket('云票务导码入库', $ticketData);
                if ($storage['code'] !== 200) {
                    exit($storage['msg']);
                }
            }
        }

        $m             = new \Model\Order\ExchangeCode('pft001');
        $batchNum      = $m->generateCodes($tid, $fid, $price, $count);
        if (empty($opId)) {
            $res = $m->QueryCodes($batchNum, 0, $count);
            $data = [];
            foreach ($res as $code) {
                $data[] = [
                    'code' => $code['code']
                ];
            }
        } else {
            $entityCodeArr = $paperTicketBiz->_getEntityCodeList($entityBeginCode, $entityEndCode);
            $codeList      = $paperTicketModel->getExchangeCodeByEntity($tid, $entityCodeArr, 'id,tid,entity_code,exchange_code');

            $data = [];
            foreach ($codeList as $key => $value) {
                $data[] = [
                    'entity_code'   => $value['entity_code'],
                    'exchange_code' => $value['exchange_code']
                ];
            }

            if ($isCloudExport) {
                $this->apiReturn(200, $data, '');
            }
        }

        $xls  = new SimpleExcel('UTF-8', true, 'orderList');
        $file = 'exchange-code';
        $xls->addArray($data);
        $xls->generateXML($file);
        die;
    }

    /**
     * excel文件上传回传
     * @param $callBack
     * @param $code
     * @param $msg
     * @param array $data
     */
    private function _fileUploadReturn($callBack, $code, $msg, $data = [])
    {
        $return = ['code' => $code, 'msg' => $msg, 'data' => $data];
        $return = json_encode($return);
        $script = '<script type="text/javascript"> window.parent.' . $callBack . '(' . $return . ') </script>';
        echo $script;
        exit;
    }

    /**
     * 读取并处理
     * @param file $filename 文件
     * @param sring $fileType 文件后缀 .xls
     * @param sring $productType 产品类型 H：演出类产品
     * @param string $version 版本 v1
     */
    private function _readExcel($filename, $fileType, $productType, $version = 'v1')
    {

        $excelDetail = $this->_readExcelDetail($filename, $fileType);

        $idCardInfoArray = [];
        $cardNumArray    = [];

        $order = new Query();
        $identityList = array_flip($order->getIdentityList());

        foreach ($excelDetail as $key => $value) {
            if ($key == 0) {
                continue;
            }
            $idCardInfoArray[$key]['name']    = $value[0];
            if ($version == 'v2') {
                $idCardInfoArray[$key]['voucherType'] = $identityList[$value[1]];
                $idCardInfoArray[$key]['cardNum'] = $value[2];
                $idCardInfoArray[$key]['mobile']  = $value[3];
                // 报团订单新增特性
                $idCardInfoArray[$key]['_excel_cardType']  = $value[4]??'';
                $idCardInfoArray[$key]['_excel_landTitle']  = $value[5]??'';
                $idCardInfoArray[$key]['_excel_ticketTitle']  = $value[6]??'';
                $cardNumArray[]                   = $value[2];
            } else {
                $idCardInfoArray[$key]['cardNum'] = $value[1];
                $idCardInfoArray[$key]['mobile']  = $value[2];
                // 报团订单新增特性
                $idCardInfoArray[$key]['_excel_cardType']  = $value[3]??'';
                $idCardInfoArray[$key]['_excel_landTitle']  = $value[4]??'';
                $idCardInfoArray[$key]['_excel_ticketTitle']  = $value[5]??'';
                $cardNumArray[]                   = $value[1];
            }
        }
        return $idCardInfoArray;
    }

    /**
     * 读取excel
     * @param file $filename 文件
     * @param sring $fileType 文件后缀 .xls
     * @return array
     */
    private function _readExcelDetail($filename, $fileType)
    {

        if ($fileType == 'xlsx') {
            $objReader = \PHPExcel_IOFactory::createReader('Excel2007');
            $objReader->setReadDataOnly(true);
            $objPHPExcel = $objReader->load($filename);
            $objPHPExcel->setActiveSheetIndex(0);
            $objWorksheet       = $objPHPExcel->getActiveSheet();
            $hightestrow        = $objWorksheet->getHighestRow();
            $highestColumn      = $objWorksheet->getHighestColumn();
            $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);
            $excelData          = array();
        } else {
            $objReader = \PHPExcel_IOFactory::createReader('Excel5');
            $objReader->setReadDataOnly(true);
            $objPHPExcel        = $objReader->load($filename);
            $objWorksheet       = $objPHPExcel->getActiveSheet();
            $hightestrow        = $objWorksheet->getHighestRow();
            $highestColumn      = $objWorksheet->getHighestColumn();
            $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);
            $excelData          = array();
        }

        for ($row = 1; $row <= $hightestrow; $row++) {
            for ($col = 0; $col < $highestColumnIndex; $col++) {
                $excelData[$row - 1][] = (string)$objWorksheet->getCellByColumnAndRow($col, $row)->getValue();
            }
        }

        return $excelData;

    }

    /**
     * 引入
     */
    private function _requireExcelFile()
    {
        Helpers::composerAutoload();
        //include_once HTML_DIR . '/Service/Library/Business/PHPExcel/PHPExcel.php';
    }

    /**
     * 获取文件类型
     * @param string $file 文件名
     * @return mixed
     */
    private function _getExtension($file)
    {
        $info = pathinfo($file);
        return $info['extension'];
    }

}
