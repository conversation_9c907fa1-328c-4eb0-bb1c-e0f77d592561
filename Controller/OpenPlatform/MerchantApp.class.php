<?php

namespace Controller\OpenPlatform;

use Business\OpenPlatform\OpenMerchantApp as OpenMerchantAppService;
use Business\Ota\OpenTicketConf;
use Library\Controller;

class MerchantApp extends Controller
{
    private $_memberInfo = [];
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];

    }

    /**
     * 获取应用列表
     *
     * Author : liucm
     * Date : 2021/9/28
     */
    public function getOpenAppList()
    {
        $keyword        = I('post.keyword', '', 'strval');
        $page           = I('post.page', '1', 'intval');
        $size           = I('post.size', '10', 'intval');
        $openTicketConf = new OpenMerchantAppService();
        $openAppList    = $openTicketConf->getOpenAppList($keyword, $page, $size);
        if ($openAppList['code'] == 200) {
            $this->apiReturn(200, $openAppList['data'], '获取列表成功');
        }

        $this->apiReturn(204, [], $openAppList['msg']);
    }

    /**
     * 获取应用列表
     *
     * Author : liucm
     * Date : 2021/10/12
     */
    public function getOpenMerchantAppList()
    {
        $appName = I('post.app_name', 1, 'strval,trim');
        $page    = I('post.page', 1, 'intval');
        $limit   = I('post.limit', 10, 'intval');

        $memberId = $this->_sid;

        $openAppService = new OpenMerchantAppService();
        $res            = $openAppService->getOpenMerchantAppList($memberId, $appName, $page, $limit);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(200, $res['data'] ?? [], '获取成功');
    }

    /**
     * 添加应用D
     *
     * Author : liucm
     * Date : 2021/10/12
     */
    public function addOpenMerchantApp()
    {
        $appId = I('post.app_id', '', 'strval,trim');
        //$appStatus  = I('post.app_status', '1', 'intval');
        $supplierId = I('post.app_member_id', '', 'intval');//开发者平台id
        $appStatus  = 1;
        //$merchantAppStatus = I('post.merchant_app_status', '1', 'intval');
        $merchantAppStatus = 1;
        //$tokenStatus       = I('post.token_status', '1', 'intval');
        $tokenStatus = 1;

        $verifyUrl        = I('post.verify_url', '', 'strval,trim');
        $refundUrl        = I('post.refund_url', '', 'strval,trim');
        $ticketUrl        = I('post.ticket_url', '', 'strval,trim');
        $productChangeUrl = I('post.product_change_url', '', 'strval,trim');
        $ticketChangeUrl  = I('post.ticket_change_url', '', 'strval,trim');
        $refundedUrl      = I('post.refunded_url', '', 'strval,trim');

        $implIdList       = [];

        $merchantId = $this->_sid;//使用者id

        $openAppService = new OpenMerchantAppService();

        // TODO 临时处理,获取开发者应用已勾选的接口方法
        $method = '/app/AppBase/getOpenMerchantQueryAppDetail';
        $params = [
            $appId,
        ];
        $module = 'admin_service';
        $callOpenApiRpc = $openAppService->callOpenApiRpc($method, $params, $module);
        if (!empty($callOpenApiRpc['data'])) {
            $data        = $callOpenApiRpc['data'];
            $appTypeList = $data['app_type_list'];
            foreach ($appTypeList['relation'] as $relation) {
                foreach ($relation['impl_list'] as $implMap) {
                    if ($implMap['is_selected'] == 1 && $implMap['is_enabled'] == 1) {
                        $implIdList[] = $implMap['impl_id'];
                    }
                }
            }
        }

        $res            = $openAppService->addOpenMerchantApp($appId, $merchantId, $appStatus, $supplierId,
            $merchantAppStatus, $tokenStatus, $verifyUrl, $refundUrl, $ticketUrl, $productChangeUrl, $ticketChangeUrl,
            $refundedUrl, $implIdList);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], '', $res['msg']);
        }

        $this->apiReturn(200, '', '添加成功');
    }

    /**
     * 更新应用
     *
     * Author : liucm
     * Date : 2021/10/12
     */
    public function updateOpenMerchantAppInfo()
    {
        $appStatus         = 1;
        $merchantAppStatus = 1;

        $appId             = I('post.app_id', '', 'strval,trim');
        $tokenStatus       = I('post.token_status', '', 'intval');
        $verifyUrl         = I('post.verify_url', '', 'strval,trim');
        $refundUrl         = I('post.refund_url', '', 'strval,trim');
        $ticketUrl         = I('post.ticket_url', '', 'strval,trim');
        $productChangeUrl  = I('post.product_change_url', '', 'strval,trim');
        $ticketChangeUrl   = I('post.ticket_change_url', '', 'strval,trim');
        $refundedUrl       = I('post.refunded_url', '', 'strval,trim');
        $merchantAppId     = I('post.merchant_app_id', '', 'strval,trim');

        $implIdList       = [];

        $openAppService = new OpenMerchantAppService();

        // TODO 临时处理,获取开发者应用已勾选的接口方法
        $method = '/app/AppBase/getOpenMerchantQueryAppDetail';
        $params = [
            $appId,
        ];
        $module = 'admin_service';
        $callOpenApiRpc = $openAppService->callOpenApiRpc($method, $params, $module);
        if (!empty($callOpenApiRpc['data'])) {
            $data        = $callOpenApiRpc['data'];
            $appTypeList = $data['app_type_list'];
            foreach ($appTypeList['relation'] as $relation) {
                foreach ($relation['impl_list'] as $implMap) {
                    if ($implMap['is_selected'] == 1 && $implMap['is_enabled'] == 1) {
                        $implIdList[] = $implMap['impl_id'];
                    }
                }
            }
        }

        $res            = $openAppService->updateOpenMerchantAppInfo($merchantAppId, $appStatus, $merchantAppStatus,
            $tokenStatus, $verifyUrl, $refundUrl, $ticketUrl, $productChangeUrl, $ticketChangeUrl, $refundedUrl, $implIdList);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], '', $res['msg']);
        }

        $this->apiReturn(200, '', '修改成功');
    }

}