<?php
/**
 * 团队限制
 * <AUTHOR>
 */
namespace Controller\TeamOrder;
use Business\TeamOrder\Booking as BookingBiz;
use Library\Controller;

class TeamLimit extends Controller {
    private $_sid;
    public function __construct()
    {
        $this->_sid = $this->isLogin('ajax');
    }
    /**
     * 保存编辑团队限制
     *
     * @author: linchen
     * @date: 2019/3/14
     * post.limit_id:          限制的id
     * post.limit_ticket:           票id ，隔开
     * post.group_ids:           分组id ，隔开
     * post.limit_type:         限制类型 0-不低于 1不高于
     * post.limit_num:   限制数量
     * post.limit_extra:   附加限制  0 不限 1限制
     * post.limit_distributor:   是否限制分销商 0 全部 1限制
     * post.extra_data:   [{"tid": 180165, "limit_condition": 0, "limit_proportion": 20}, {"tid": 124, "limit_condition": 1, "limit_proportion": 50}]
     * post.distributor_id:   限制分销商id，隔开
     */
    public function saveTeamLimitConfig(){
        $limitName        = I('post.limit_name', '', "strval");
        $teamLimitId      = I('post.limit_id',0);
        $LimitTicketId    = I('post.limit_ticket','');
        $limitType        = I('post.limit_type',0);    //限制类型 0-不低于 1不高于 2:不限
        $limitNum         = I('post.limit_num',0);
        $extraLimit       = I('post.limit_extra',0);    //附加限制
        $limitDistributor = I('post.limit_distributor',0);                          //是否限制分销商
        $extraDataJson    = I('post.extra_data','','strip_tags');    //获取json串
        $distributorId    = I('post.distributor_id','');//限制分销商id
        $limitLids        = I('post.lids','');//限制景区id
        $groupId          = I('post.group_ids','');//组id
        $channel          = I('post.team_channel','');   //销售指定渠道1：分销后台 2：团队窗口
        $channelType      = I('post.channel_type','');   //销售渠道类型1：全部渠道 2：指定渠道
        $limitPercentTnum = I('post.limit_percent_tnum',0);   //附加条件限制类型 0 限制占比 1 限制票数
        $limitMode        = I('post.limit_mode',1);   //限制模式 1：完全匹配  2：模糊匹配
        $splitTicket      = I('post.split_ticket','');   //凑单票id
        $arrTicket        = explode(',',$LimitTicketId);
        $arrGroupId       = explode(',',$groupId);
        if ($limitNum < 1 && $limitType != 2){
            $this->apiReturn('204','','数量之和不能小于1张');
        }
        if (!is_array($arrTicket) || count(array_filter($arrTicket)) < 1){
            $this->apiReturn('204','','票不能为空');
        }
        if ($groupId != '' && count(array_filter($arrGroupId)) < 1){
            $this->apiReturn('204','','组格式有问题');
        }
        $extraData        = json_decode($extraDataJson,true);
        if ($extraDataJson != '' && is_null($extraData)){
            $this->apiReturn('204','','附加限制产品格式有误');
        }
        if (!is_null($extraData)){
            foreach ($extraData as $key => $value){
                if (!isset($value['limit_proportion'])){
                    $this->apiReturn('204','','附加限制产品参数有误');
                }
                if ($value['limit_proportion'] < 1 || $value['limit_proportion'] > 99){
                    $this->apiReturn('204','','附加限制产品票数区间有误');
                }
            }
        }
        $arrDistributorId = explode(',', $distributorId);
        if ($limitDistributor == 1 && count($arrDistributorId) < 1) {
            $this->apiReturn('204', '', '限制分销商不能为空');
        }
        $TeamLimitBiz = new \Business\TeamOrder\TeamLimit();
        if (count(array_filter($arrGroupId)) > 0 && $groupId != '') {
            $checkGroup = $TeamLimitBiz->checkGroup($arrGroupId, $this->_sid);
            if (!$checkGroup) {
                $this->apiReturn('204', '', '分组参数错误');
            }
        }
        if ($teamLimitId) {
            $result = $TeamLimitBiz->editTeamLimit($teamLimitId, $this->_sid, $LimitTicketId, $limitType, $limitNum,
                $extraLimit, $limitDistributor, $extraDataJson, $distributorId, $groupId, $channel, $channelType, $limitLids,
                $limitName, $limitPercentTnum, $limitMode, $splitTicket);
        } else {
            $result = $TeamLimitBiz->saveTeamLimit($this->_sid, $LimitTicketId, $limitType, $limitNum, $extraLimit,
                $limitDistributor, $extraDataJson, $distributorId, $groupId, $channel, $channelType, $limitLids, $limitName,
                $limitPercentTnum, $limitMode, $splitTicket);
        }
        return $this->apiReturn($result['code'],'',$result['msg']);
    }
    /**
     * 获取供应商团单可售列表
     *
     * @author: linchen
     * @date: 2019/3/14
     * get.keyword:          关键字
     * get.page:           当前页
     * get.size:            个数
     */
    public function getProduct(){
        //产品类型
        // $type    = I('type', 'A');
        //搜索关键字
        $keyword = I('get.keyword', '');
        //当前页码
        $page    = I('get.page', 1, 'intval');
        //每页条数
        $size    = I('get.size', 20, 'intval');

        //报团审核修改产品调用
        $playTime = I('get.play_time');
        $sid      = I('get.sid');
        $fid      = I('get.fid');
        $type     = I('get.type', '', 'strval');

        $date = date('Y-m-d', time());
        $mid  = $this->_sid;

        if ($playTime) {
            $date = $playTime;
        }

        if (!$sid) {
            $sid = $this->_sid;
        }

        if ($sid && $fid) {
            $mid = $fid;
        }
        
        $bookingBiz = new BookingBiz();
        //$result = $bookingBiz->productSearchNoLimitTicketNum($mid, $sid, $date, $keyword, $page, $size, $type);
        $result = $bookingBiz->productSearchNoLimitTicketNumNew($mid, $sid, $date, $keyword, $page, $size, $type, 0);
        if (isset($result['code'])) {
            // 处理数据返回给前端
            $allData = [];
            foreach ($result['data'] as $key => $item) {
                $data = [
                    'id'   => $item['product_id'],
                    'name' => $item['product_name'],
                    'aid'  => $item['supplier_id'],
                ];
                $tData = [];
                $ticketList = $item['ticket_list'] ? $item['ticket_list'] :[];
                foreach ($ticketList as $k => $v){
                    $tData[] = [
                        'id'          => $v['ticket_id'],
                        'name'        => $v['ticket_name'],
                    ];
                }
                if (count($tData) <1){
                    continue;
                }
                $data['ticket_list'] = $tData;
                $allData[]           = $data;

            }
            $this->apiReturn($result['code'], $allData, $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }
    /**
     * 获取供应商分组列表
     *
     * @author: linchen
     * @date: 2019/3/14
     * get.keyword:          关键字
     * get.page:           当前页
     * get.size:            个数
     */
    public function getMemberGroupList(){
        $keyword = I('get.keyword', '');
        $TeamLimitBiz = new \Business\TeamOrder\TeamLimit();
        $result       = $TeamLimitBiz->getMemberGroupList($this->_sid,$keyword);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
    /**
     * 获取限制的配置
     *
     * @author: linchen
     * @date: 2019/3/14
     * get.id:          主键id
     */
    public function getLimitInfoById()
    {
        $limitId = I('get.id', 0);
        if ($limitId < 1) {
            $this->apiReturn(204, [], 'id不能为空');
        }
        $TeamLimitBiz = new \Business\TeamOrder\TeamLimit();
        $result       = $TeamLimitBiz->getLimitInfoById($limitId, $this->_sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
    /**
     * 获取供应商限制列表
     *
     * @author: linchen
     * @date: 2019/3/14
     * get.page:           当前页
     * get.size:            个数
     */
    public function getTeamLimitList()
    {
        $size         = I('get.size', 10);
        $page         = I('get.page', 1);
        $lid          = I('get.lid', 0);
        $limitName    = I('get.limit_name', 0);
        $TeamLimitBiz = new \Business\TeamOrder\TeamLimit();
        $result       = $TeamLimitBiz->getTeamLimitList($this->_sid, $lid, $limitName, $page, $size);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取订单的限制详情
     *
     * @author: linchen
     * @date: 2019/3/14
     * get.ticketIds:           票id
     * get.buyid:            购买者id
     * get.aid:            供应商id
     */
    public function getOrderLimitDetail()
    {
        $teamOrderId = I('get.team_order', '');
        $buyId       = I('get.buyid', '');
        $aid         = I('get.aid', '');
        if (!$teamOrderId || !$buyId || !$aid) {
            $this->apiReturn(204, '', '参数有误');
        }
        $TeamLimitBiz = new \Business\TeamOrder\TeamLimit();
        $result       = $TeamLimitBiz->getOrderLimitDetail($teamOrderId, $buyId, $aid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除限制
     *
     * @author: linchen
     * @date: 2019/3/14
     * get.limit_id:           主键id
     */
    public function deleteTeamLimitById(){
        $limitId = I('post.limit_id',0);
        if ($limitId < 1){
            $this->apiReturn(204, '', '参数错误');
        }
        $TeamLimitBiz = new \Business\TeamOrder\TeamLimit();
        $result = $TeamLimitBiz->delTeamLimit($limitId,$this->_sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 报团预定的预定按钮那边（平台）
     *
     * order_data数据格式
     * order_data[景区id][票类id]: 购买数量
     * order_data[192670][520636]: 2
     * order_data[192670][520703]: 2
     *
     * @author: linchen
     * @date: 2019/3/14
     */
    public function teamOrderReserveCheck()
    {
        $orderData = I('post.order_data', '', 'strval');
        $aid       = I('post.aid', 0, 'intval'); //上级供应商id
        $fid       = I('post.fid', 0, 'intval'); //报团计调下单的时候传这个id
        $teamOrderId = I('post.team_order_id', '', 'strval'); //团单号
        if (!$orderData || (!$aid && !$fid)) {
            $this->apiReturn(204, '', '参数错误');
        }
        $fid = empty($fid) ? $this->_sid : $fid; //报团计调的时候才有这个参数，预定的时候还是原来的逻辑
        $TeamLimitBiz = new \Business\TeamOrder\TeamLimit();
        $result       = $TeamLimitBiz->teamOrderReserveCheck($aid, $fid, $orderData, $teamOrderId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 查看供应商给分销商限制列表
     *
     * @author: linchen
     * @date: 2019/3/14
     * get.aid:           供应商id
     */
    public function getSupplyForMeLimit()
    {
        $aid  = I('get.aid', 0);
        $fid  = I('get.fid', 0); //分销商id，报团计调传这个参数
        $page = I('get.page', 1);
        $size = I('get.size', 10);
        if (!$aid) {
            $this->apiReturn(204, '', '参数错误');
        }
        $TeamLimitBiz = new \Business\TeamOrder\TeamLimit();
        if (!empty($fid)) {
            $result = $TeamLimitBiz->getSupplyForLimit($fid, $this->_sid, $page, $size);
        } else {
            $result = $TeamLimitBiz->getSupplyForLimit($this->_sid, $aid, $page, $size);
        }
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}