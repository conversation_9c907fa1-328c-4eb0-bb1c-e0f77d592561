<?php
/**
 * 团单激活身份证登记
 * 证件归集相关业务
 * <AUTHOR>
 * @date 2023.10.14
 */

namespace Controller\TeamOrder;

use Business\TeamOrder\TeamActiveBiz;
use Library\Controller;

class IdCardCollectConfig extends Controller
{
    private $sid;
    private $memberId;
    private $biz;

    public function __construct()
    {
        $loginInfo = $this->getLoginInfo();
        $this->sid = $loginInfo['sid'];
        $this->memberId = $loginInfo['memberID'];
        $this->biz = TeamActiveBiz::getInstance();
    }

    /**
     * 新增或修改证件归集配置
     * 1.非必填，但是每一行一旦有一个字段不为空时，当前行的所有字段都必填
     * 2.年龄归集：年龄的后值必须大于前值，多年龄段不可重叠
     * 3.地域归集：可以只选到省或者市。下一级未选，代表包含全省或全市；地域不可重叠
     * 4.年龄归集优先于地域归集，即一个身份证既满足年龄归集又满足地域归集时，归于年龄
     * 1)省市县都不重复,可保存
     * 2)省市重复,县不重复,可保存
     * 3)省重复,市县不重复,可保存
     * 4)省市重复,不可保存
     * 5)只填省,默认全省保存
     * 6)只填省市,默认市下全部保存
     * 7)添加了一个省的  再添加相同省市或省市区县的 也算重叠
     * 8)添加了一个省市的  再添加相同省市或省市区县的 也算重叠
     */
    public function addOrUpdateConfig()
    {
        //年龄归集配置json [{"age_start":"18","age_end":"35","collect_name":"少年组"}, {...}]
        $ageConfig = I('post.age_config', '', 'strval');
        //地域归集配置json [{"province_code":"","":"","collect_name":"华东区域"}, {...}]
        $areaConfig = I('post.area_config', '', 'strval');
        if (empty($ageConfig) && empty($areaConfig)) {
            $this->apiReturn(203, [], '请填写配置信息');
        }
        if (!empty($ageConfig)) {
            $check = $this->checkJsonParams($ageConfig);
            if ($check[0] != 200) {
                $this->apiReturn($check[0], [], $check[1]);
            }
        }
        if (!empty($areaConfig)) {
            $check = $this->checkJsonParams($areaConfig, 2);
            if ($check[0] != 200) {
                $this->apiReturn($check[0], [], $check[1]);
            }
        }
        //如果json里面有id就代表是更新 否则是新增
        $ageConfig = json_decode($ageConfig, true);
        $areaConfig = json_decode($areaConfig, true);
        $result = $this->biz->addOrUpdateConfig($this->sid, $this->memberId, $ageConfig, $areaConfig);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    private function checkJsonParams($params, $type = 1): array
    {
        $checkKeys = $type == 1 ? ['age_start', 'age_end', 'collect_name'] : ['province_code', 'city_code', 'country_code', 'collect_name'];
        $txt = $type == 1 ? '年龄' : '地域';
        $paramsArr = json_decode($params, true);
        if (json_last_error() != JSON_ERROR_NONE) {
            return[203, $txt . '归集配置有误，不是标准json'];
        }
        $ageGroup = $areaGroup = [];
        foreach ($paramsArr as $item) {
            $keys = array_keys($item);
            foreach ($keys as $val) {
                if (!in_array($val, $checkKeys)) {
                    return [203, $txt . '归集配置有误，缺少必要的key'];
                }
                if (empty($item['collect_name'])) {
                    return [203, $txt . '归集配置有误，归集名称不能为空'];
                }
            }
            if ($type == 1) {
                if (!is_numeric($item['age_start']) || !is_numeric($item['age_end'])) {
                    return [203, $txt . '归集配置有误，年龄设置有误01'];
                }
                if ($item['age_start'] < 1 || $item['age_end'] < 1 || $item['age_end'] < $item['age_start']) {
                    return [203, $txt . '归集配置有误，年龄设置有误02'];
                }
                //判断年龄是否交叉重叠(多条配置才可能存在重叠)
                $ageGroup[] = $item;
            } else {
                if (!is_numeric($item['province_code'])) {
                    return [203, $txt . '归集配置有误，province_code设置有误'];
                }
                if (!empty($item['city_code']) && !is_numeric($item['city_code'])) {
                    return [203, $txt . '归集配置有误，city_code设置有误'];
                }
                if (!empty($item['country_code']) && !is_numeric($item['country_code'])) {
                    return [203, $txt . '归集配置有误，country_code设置有误'];
                }
                $areaGroup[] = $item;
            }
        }
        //判断年龄是否重叠
        if (count($ageGroup) > 1) {
            foreach ($ageGroup as $key => $item) {
                if ($this->ifAgeCrossOver($item, $key, $ageGroup)) {
                    return [203, '年龄归集配置有误，存在重叠的年龄段，请检查'];
                }
            }
        }
        //判断地域是否重叠
        if (count($areaGroup) > 1) {
            foreach ($areaGroup as $key => $item) {
                if ($this->ifAreaCrossOver($item, $key, $areaGroup)) {
                    return [203, '地域归集配置有误，存在重叠的地域，请检查'];
                }
            }
        }
        return [200, ''];
    }

    //true-重叠
    private function ifAgeCrossOver($arr, $continueKey, $ageGroup): bool
    {
        foreach ($ageGroup as $key => $item) {
            if ($key == $continueKey) {
                continue;
            }
            if ($arr['age_start'] <= $item['age_start'] && $arr['age_end'] >= $item['age_start']) {
                return true;
            }
            if ($arr['age_start'] >= $item['age_start'] && $arr['age_start'] <= $item['age_end']) {
                return true;
            }
        }
        return false;
    }
    //true-重叠
    private function ifAreaCrossOver($arr, $continueKey, $areaGroup): bool
    {
        foreach ($areaGroup as $key => $item) {
            if ($key == $continueKey) {
                continue;
            }
            //至少都要配置province_code
            if ($item['province_code'] == $arr['province_code']) {
                //如果存在只配置了省 只要有第二条配置就代表重叠了
                if ((empty($arr['city_code']) && empty($arr['country_code'])) || (empty($item['city_code']) && empty($item['country_code']))) {
                    return true;
                }
                //如果存在只配置了省市 只要有第二条配置就代表重叠了
                if ((!empty($arr['city_code']) && empty($arr['country_code'])) || (!empty($item['city_code']) && empty($item['country_code']))) {
                    if ($arr['city_code'] == $item['city_code']) {
                        return true;
                    }
                }
                //如果省、市、区县存在重复就是重叠
                if ($arr['city_code'] == $item['city_code'] && $arr['country_code'] == $item['country_code']) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 证件归集配置回显
     * <AUTHOR>
     * @Date 2023/10/16 10:20
     * @return void
     */
    public function getConfig()
    {
        $result = $this->biz->getConfig($this->sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取激活登记记录
     * <AUTHOR>
     * @Date 2023/10/17 8:47
     * @return void
     */
    public function getActiveRecord()
    {
        //登记日期
        $st = I('post.start_time', '', 'strval');
        $et = I('post.end_time', '', 'strval');
        //游玩日期
        $playSt = I('post.play_start', '', 'strval');
        $playEt = I('post.play_end', '', 'strval');
        //操作人
        $optId = I('post.opt_id', 0, 'intval');
        //订单号
        $orderNum = I('post.ordernum', '', 'strval');
        //凭证码
        $vCode = I('post.code', '', 'strval');
        //团队订单号 team_开头
        $teamOrderNum = I('post.team_ordernum', '', 'strval');
        //激活码 team_act_开头
        $activeCode = I('post.active_code', '', 'strval');
        $page = I('post.page', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');
        $page = $page < 1 ? 1 : $page;
        $pageSize = $pageSize < 1 ? 10 : $pageSize;
        $sid = $this->sid;
        $params = compact('st', 'et', 'playSt', 'playEt', 'optId',
            'vCode', 'sid', 'page', 'pageSize', 'orderNum', 'teamOrderNum', 'activeCode');
        $res = $this->biz->getActiveRecord($params);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取激活登记操作记录
     * <AUTHOR>
     * @Date 2023/10/18 16:41
     * @return void
     */
    public function getActiveIdCardLog()
    {
        $activeId = I('active_id', 0, 'intval');
        if (!$activeId) {
            $this->apiReturn(203, [], '参数错误');
        }
        $sid = $this->sid;
        $page = I('post.page', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');
        $params = compact('sid', 'activeId', 'page', 'pageSize');
        $res = $this->biz->getActiveIdCardLog($params);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}