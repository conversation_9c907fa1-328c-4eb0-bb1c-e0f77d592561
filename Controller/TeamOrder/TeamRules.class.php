<?php

namespace Controller\TeamOrder;

use Library\Controller;
use Business\TeamOrder\TeamRules as TeamRulesBz;

class TeamRules extends Controller
{

    //供应商id
    private $_sid;

    public function __construct()
    {
        $this->_sid = $this->isLogin();
    }

    /**
     * 新增|修改报团订单修改限制
     *
     * @return array
     * @throws \Exception
     * <AUTHOR>
     * @date 2021/5/6
     *
     */
    public function saveTeamOrderEditLimit()
    {
        $id                     = I('post.id', 0, "intval");
        $limitName              = I('post.limit_name', '', "strval");
        $limitTids              = I('post.limit_tids', '', "strval"); //限制票id字符串，逗号隔开
        $limitConfig            = I('post.limit_config', '', "strval"); //限制配置
        $limitType              = I('post.limit_type', 1, "intval"); //限制类型 1：订单修改限制 2：报团类型限制
        $limitDistributorsGroup = I('post.limit_distributors_group', "", "strval"); //限制分销商分组id
        $limitDistributorsId    = I('post.limit_distributors_id', "", "strval"); //限制分销商id
        $limitDistributorsType  = I('post.limit_distributors_type', 1, "intval"); //限制分销商类型 1：全部 2：指定组
        $limitLidConfig         = I('post.limit_lid_config', '', "strval"); //景区限制配置
        $closeDistributeGroups  = I('post.close_distribute_group', '', 'strval'); //关闭分销组id
        $closeDistributeIds     = I('post.close_distribute_ids', '', 'strval'); //关闭分销商id
        $tmpLimitConfig         = json_decode($limitConfig, true);
        if (empty(trim($limitName)) || empty($limitConfig) || (empty($limitLidConfig) && $limitType == 2) ||
            (empty($tmpLimitConfig['incr_behind']) && empty($tmpLimitConfig['desc_behind']) && $limitType == 1)) {
            $this->apiReturn(203, [], "参数错误");
        }

        $teamRulesBz = new TeamRulesBz();
        $result      = $teamRulesBz->saveTeamOrderEditLimit($this->_sid, $id, $limitName, $limitTids, $limitConfig, $limitType,
            $limitDistributorsId, $limitDistributorsGroup, $limitDistributorsType, $limitLidConfig, $closeDistributeGroups,
            $closeDistributeIds);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 获取报团订单修改限制
     *
     * @return array
     * <AUTHOR>
     * @date 2021/5/6
     *
     */
    public function getTeamOrderEditLimitList()
    {
        $pageNum   = I('post.pageNum', 1, "intval");
        $pageSize  = I('post.pageSize', 10, "intval");
        $limitType = I('post.limit_type', 1, "intval");
        $limitName = I('post.limit_name', '', 'strval');   //限制名称

        $teamRulesBz = new TeamRulesBz();
        $result      = $teamRulesBz->getTeamOrderEditLimitList($this->_sid, $limitType, $limitName, $pageNum, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取单挑报团订单修改限制信息
     *
     * @return array
     * <AUTHOR>
     * @date 2021/5/6
     *
     */
    public function getTeamOrderEditLimit()
    {
        $id = I('post.id', 0, "intval");
        if (empty($id)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $teamRulesBz = new TeamRulesBz();
        $result      = $teamRulesBz->getTeamOrderEditLimit($this->_sid, $id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除报团订单修改限制
     *
     * @return array
     * <AUTHOR>
     * @date 2021/5/6
     *
     */
    public function deleteTeamOrderEditLimit()
    {
        $id = I('post.id', 0, "intval");
        if (empty($id)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $teamRulesBz = new TeamRulesBz();
        $result      = $teamRulesBz->deleteTeamOrderEditLimit($this->_sid, $id);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 新增或者更新团单消息通知配置
     *
     * @return array
     * <AUTHOR>
     * @date 2021/7/24
     *
     */
    public function addOrUpdateTeamNotifySetting()
    {
        $id          = I('post.id', 0, 'intval');
        $sendType    = I('post.send_type', '', 'strval');
        $sendConfig  = I('post.send_config', '', 'strval');
        if (empty($sendConfig)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $teamRulesBz = new TeamRulesBz();
        $result      = $teamRulesBz->addOrUpdateTeamNotifySetting($this->_sid, $id, $sendType, $sendConfig);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 获取团单消息通知配置
     *
     * @return array
     * <AUTHOR>
     * @date 2021/7/24
     *
     */
    public function getTeamNotifySetting()
    {
        $teamRulesBz = new TeamRulesBz();
        $result      = $teamRulesBz->getTeamNotifySetting($this->_sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取所有团队类型
     * User: lanwanhui
     * Date: 2023/4/7
     */
    public function getAllTeamOrderEditLimit()
    {
        $limitName   = I('post.limit_name', '', 'strval'); //限制名称

        $teamRulesBz = new TeamRulesBz();
        $result      = $teamRulesBz->getAllTeamOrderEditLimit($this->_sid, 2, $limitName);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}