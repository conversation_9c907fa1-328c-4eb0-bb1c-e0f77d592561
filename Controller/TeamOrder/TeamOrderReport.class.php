<?php
/**
 * 团队报表
 *
 * <AUTHOR>
 * @date   2018-06-22
 */
namespace Controller\TeamOrder;

use Business\JavaApi\DistributionCenter\GroupQuery;
use Business\Statistics\TeamReportSearch;
use Library\Constants\ThemeConst;
use Library\Controller;
use Model\Member\Guide;
use Model\Member\Member;
use Model\Product\Area;
use Library\SimpleExcel;

class TeamOrderReport extends Controller {

    private $_teamBusiness;
    private $_staV3Model;
    private $_guideModel;
    private $_areaModel;

    private $_buyerInfo;
    private $_guideInfo;

    // 团队验证报表团队人数
    private $_teamCheckNum = 0;
    //
    private $_teamCheckSaleMoney = 0;

    //需要统计的维度
    private $_needItem = [
        'date' => '日期',
        'buyer_id' => '分销商',
        'buyer_group_id' => '分销商组',
        'guide' => '导游',
        'province' => '省份',
        'city' => '城市',
        'team_lid_num' => '团队景区个数',
        'is_lodging' => '住宿',
    ];

    //需要统计的指标
    private $_needTarget = [
        'check_ticket' => '团队人数',
        'team_num'     => '团队数量',
    ];
    /**
     * @var array
     */
    private $_buyerGroupInfo;

    private function _getTeamOrderReportBusiness() {
        if (empty($this->_teamBusiness)) {
            $this->_teamBusiness = new TeamReportSearch();
        }

        return $this->_teamBusiness;
    }

    private function _getStaV3Model() {
        if (empty($this->_staV3Model)) {
            $this->_staV3Model = new \Model\Report\StatisticsV3();
        }

        return $this->_staV3Model;
    }

    private function _getGuideModel() {
        if (empty($this->_guideModel)) {
            $this->_guideModel = new Guide();
        }

        return $this->_guideModel;
    }

    private function _getAreaModel() {
        if (empty($this->_areaModel)) {
            $this->_areaModel = new Area();
        }

        return $this->_areaModel;
    }

    /**
     * 获取团队验证报表
     *
     * <AUTHOR>
     * @date   2018-06-22
     */
    public function getTeamReportCheck() {
        $code = 200;
        $data = [
            'list'  => [],
            'total' => 0,
            'sum'   => []
        ];
        $excel = 0;
        $msg   = '';

        try {
            $staV3Business = $this->_getTeamOrderReportBusiness();

            //获取数据
            $staV3Model    = $this->_getStaV3Model();
            $loginInfo     = $this->getLoginInfo();
            $sellerId      = $loginInfo['sid'];

            //参数过滤
            $params        = $_REQUEST;
            $res           = $staV3Business->handleParams($params);
            if ($res['code'] != 200) {
                throw new \Exception($res['msg'], $res['code']);
            }

            // 判断是否有传递查询条件
            if ($this->teamReportCheckCondition($params)) {
                $isCondition = true;
            } else {
                $isCondition = false;
            }

            $begin         = $params['begin'] ?? '';
            $end           = $params['end'] ?? '';
            $configId      = $params['config_id'] ?? 0;
            $buyerId       = $params['buyer_id'] ?? '';
            $guide         = $params['guide'] ?? '';
            $lidNum        = $params['lid_num'] ?? '';
            $province      = $params['province'] ?? '';
            $city          = $params['city'] ?? '';
            $county        = $params['county'] ?? '';
            $topic         = $params['topic'] ?? [];
            $page          = $params['page'] ?? 1;
            $size          = $params['size'] ?? 1;
            $excel         = $params['excel'] ?? 0;
            $isLodging     = $params['is_lodging'] ?? '';
            $buyerGroupId  = $params['buyer_group_id'] ?? '';

            $templateModel = new \Model\Report\Template();
            $template        = $templateModel->getTemplateById($sellerId, $configId);

            if (empty($template)) {
                throw new \Exception("模板配置未知");
            }

            //统计纬度
            $item = json_decode($template['item'], true);
            if (empty($item) || !is_array($item)) {
                throw new \Exception("配置有误, 统计纬度不能为空");
            }

            //报表名称
            $name   = $template['name'];

            //维度名称
            $titleArr = [];
            foreach ($item as $key => $value) {
                if (!array_key_exists($value, $this->_needItem)) {
                    unset($item[$key]);
                    continue;
                }

                $titleArr[] = $this->_needItem[$value];
            }

            $item = array_values($item);

            //统计指标
            $target = json_decode($template['target'], true);
            if (empty($target) || !is_array($target)) {
                $target = [];
            }

            $group = '';
            if (!empty($item)) {
                $group = implode(",", $item);
            }

            //主题组装下, 顺序, 并拼接次数
            $systemTopic = ThemeConst::THEMES;
            $topicArr    = [];
            if (!empty($topic)) {
                $topicKeys = array_keys($topic);
                foreach ($systemTopic as $key => $value) {
                    $tmpTopic = in_array($key, $topicKeys) ? $value : '';
                    if (!empty($tmpTopic)) {
                        $topicArr[] = $tmpTopic . $topic[$key];
                    }
                }
            }

            $topicMd5 = '';
            if (!empty($topicArr)) {
                $topicMd5 = md5(json_encode($topicArr));
            }

            $res  = $staV3Model->getTeamCheckReport(
                $begin, $end, $sellerId, $buyerId, $guide, $lidNum,
                $province, $city, $county, $topicMd5, $group, $page, $size,
                $isCondition, $excel, $isLodging, $buyerGroupId
            );

            $list      = $res['list'];
            $total     = $res['total'];
            $sum       = $res['sum'];
            $saleMoney = $res['sale_money'];
            $checkNum  = $res['check_num'];

            if (empty($list)) {
                throw new \Exception('未找到数据', 400);
            }

            //求出所有需要的纬度的信息
            $this->_getTitleInfo($list, $item);

            $itemNum = count($item);
            $res     = $this->_handleTeamCheckReportV1($list, $itemNum, $item);

            $sunNew  = [
                'check_ticket' => $checkNum,
                'team_num'     => $sum['team_num'],
                'real_money'   => $saleMoney / 100
            ];
         
            $data    = [
                'list'   => $res,
                'total'  => $total,
                'sum'    => $sunNew,
                'target' => $target,
                'title'  => $titleArr,
                'name'   => $name
            ];

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        if ($excel && $code == 200) {
            $this->_exportOrderTwoList($data, $itemNum);
        } else {
            $this->apiReturn($code, $data, $msg);
        }
    }

    /**
     * 获取团单预定报表数据
     *
     * <AUTHOR>
     *
     * @throws \Exception
     */
    public function getTeamOrderReport()
    {
        $loginInfo = $this->getLoginInfo();
        //date_type 1-按日 2-按月或年汇总 对应的begin和end会根据date_type变化
        $dateType = I('date_type',1, 'intval');
        //开始时间
        $begin = I('begin');
        //结束时间
        $end   = I('end');
        //模板id
        $configId  = I('search_config_id');
        //页数
        $pageNum = I('page', 1, 'intval');
        //每页数量
        $pageSize = I('page_size', 10, 'intval');
        //是否导出
        $excel = I('export_excel', 0, 'intval');
        //查询参数
        $lidParam         = I('post.lid', '');
        $tidParam         = I('post.tid', '');
        $orderSourceParam = I('post.order_source', '');
        $payModeParam     = I('post.pay_mode', -1);
        $operatorIdParam  = I('post.operator_id', '');
        $orderTypeParam   = I('post.order_type', '');
        $fidParam         = I('post.fid', '');
        $teamTypeIdParam  = I('post.team_type_id', '');
        $guideIdParam     = I('post.guide', '');
        $isLodgIngParam   = I('post.is_lodging', -1);
        $provinceParam    = I('post.province', '');
        $cityParam        = I('post.city', '');
        $buyerGroupIdParam = I('post.buyer_group_id', '');
        $teamOrderIdParam = I('post.team_order_id', '');
        if (empty($begin) || empty($end) || empty($configId)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $teamOrderReportModel = new \Business\TeamOrder\TeamOrderReport();
        $sid = $loginInfo['sid'];
        $params = compact(
            'configId', 'sid', 'begin', 'end', 'lidParam',
            'tidParam', 'orderSourceParam', 'payModeParam', 'operatorIdParam', 'orderTypeParam',
            'fidParam', 'teamTypeIdParam', 'guideIdParam', 'isLodgIngParam', 'provinceParam',
            'cityParam', 'buyerGroupIdParam', 'pageNum', 'pageSize', 'excel', 'dateType', 'teamOrderIdParam'
        );
        try {
            $result = $teamOrderReportModel->getTeamOrderReport($params);
            $code   = $result['code'];
            $msg    = $result['msg'];
        } catch (\Exception $exception) {
            $code = $exception->getCode();
            $msg  = $exception->getMessage();
        }

        $this->apiReturn($code, $result['data'] ?? [], $msg);

    }

    /**
     * 获取团单检票报表数据
     *
     * <AUTHOR>
     *
     * @throws \Exception
     */
    public function getTeamCheckOrderReportV2()
    {
        $loginInfo = $this->getLoginInfo();
        //date_type 1-按日 2-按月或年汇总 对应的begin和end会根据date_type变化
        $dateType = I('date_type',1, 'intval');
        //开始时间
        $begin = I('begin');
        //结束时间
        $end   = I('end');
        //模板id
        $configId  = I('search_config_id');
        //页数
        $pageNum = I('page', 1, 'intval');
        //每页数量
        $pageSize = I('page_size', 10, 'intval');
        //是否导出
        $excel = I('export_excel', 0, 'intval');
        if (empty($begin) || empty($end) || empty($configId)) {
            $this->apiReturn(203, [], "参数错误");
        }
        //查询参数
        $lidParam         = I('post.lid', '');
        $tidParam         = I('post.tid', '');
        $operatorIdParam  = I('post.operator_id', '');
        $orderTypeParam   = I('post.order_type', '');
        $fidParam         = I('post.fid', '');
        $teamTypeIdParam  = I('post.team_type_id', '');
        $guideIdParam     = I('post.guide', '');
        $isLodgIngParam   = I('post.is_lodging', -1);
        $provinceParam    = I('post.province', '');
        $cityParam        = I('post.city', '');
        $payMode          = I('post.pay_mode', -1);
        $buyerGroupIdParam     = I('post.buyer_group_id', '');
        $teamOrderIdParam      = I('post.team_order_id', '');
        $sid = $loginInfo['sid'];
        $params = compact(
            'configId', 'sid', 'begin', 'end', 'lidParam',
            'tidParam', 'operatorIdParam', 'orderTypeParam', 'fidParam', 'teamTypeIdParam',
            'guideIdParam', 'isLodgIngParam', 'provinceParam', 'cityParam', 'payMode',
            'buyerGroupIdParam', 'pageNum', 'pageSize', 'excel', 'dateType', 'teamOrderIdParam'
        );
        $teamOrderReportModel = new \Business\TeamOrder\TeamOrderReport();
        try {
            $result = $teamOrderReportModel->getTeamCheckOrderReportV2($params);
            $code   = $result['code'];
            $msg    = $result['msg'];
        } catch (\Exception $exception) {
            $code = $exception->getCode();
            $msg  = $exception->getMessage();
        }

        $this->apiReturn($code, $result['data'] ?? [], $msg);

    }

    /**
     * 导出报表某条记录的订单详情信息
     *
     * <AUTHOR>
     * @date 2021/11/18
     */
    public function getOneReportDetailInfo()
    {
        $loginInfo = $this->getLoginInfo();
        // 获取所有参数
        $params = I('param.');
        //查询跨度超过一个月，走报表下载中心
        if ((strtotime($params['begin']) - strtotime($params['end'])) > 2764800) {
            exit("<script>alert('查询跨度超过一个月，暂时关闭')</script>");
        }
        //查询参数
        $dateType         = I('param.date_type', 1, 'intval');
        $begin            = I('param.begin', '');
        $end              = I('param.end', '');
        $type             = I('param.type', 1);
        $lidParam         = I('param.lid', '');
        $tidParam         = I('param.tid', '');
        $payModeParam     = I('param.pay_mode', -1);
        $operatorIdParam  = I('param.operator_id', '');
        $orderTypeParam   = I('param.order_type', '');
        $fidParam         = I('param.fid', '');
        $teamTypeIdParam  = I('param.team_type_id', '');
        $guideIdParam     = I('param.guide', '');
        $isLodgIngParam   = I('param.is_lodging', -1);
        $provinceParam    = I('param.province', '');
        $cityParam        = I('param.city', '');
        $buyerGroupIdParam = I('param.buyer_group_id', '');
        $teamOrderIdParam = I('param.team_order_id', '');
        $sid = $loginInfo['sid'];
        $newParams = compact(
            'begin', 'end', 'type', 'lidParam', 'tidParam', 'payModeParam',
            'operatorIdParam', 'orderTypeParam', 'fidParam', 'teamTypeIdParam', 'guideIdParam', 'isLodgIngParam',
            'provinceParam', 'cityParam', 'buyerGroupIdParam', 'sid', 'dateType', 'teamOrderIdParam'
        );
        $teamOrderReportBz = new \Business\TeamOrder\TeamOrderReport();
        $result            = $teamOrderReportBz->getOneReportDetailInfo($sid, $params['search_config_id'], $newParams);
        if ($result['code'] != 200 || empty($result['data'])) {
            exit("<script>alert('暂无数据可导出')</script>");
        }
        $excelData = array_values($result['data']['list']);
        $head      = array_values($result['data']['head']);
        $xls       = new SimpleExcel('UTF-8', true, 'orderList');
        array_unshift($excelData, $head);

        $xls->addArray($excelData);
        switch ($params['type']) {
            case 1:
                $file = "团队销售报表";
                break;
            case 2:
                $file = "团队验证报表";
                break;
            default:
                $file = "未知";
                break;
        }
        $xls->generateXML($file);
    }


    /**
     * 获取团单结算报表数据
     *
     * <AUTHOR>
     *
     * @throws \Exception
     */
    public function getTeamOrderSettleReport()
    {
        $loginInfo = $this->getLoginInfo();
        //date_type 1-按日 2-按月或年汇总 对应的begin和end会根据date_type变化
        $dateType = I('date_type',1, 'intval');
        //开始时间
        $begin = I('begin');
        //结束时间
        $end   = I('end');
        //模板id
        $configId  = I('search_config_id');
        //页数
        $pageNum = I('page', 1, 'intval');
        //每页数量
        $pageSize = I('page_size', 10, 'intval');
        //查询参数
        $lidParam        = I('post.lid', '');
        $operatorIdParam = I('post.operator_id', '');
        $fidParam        = I('post.fid', '');
        $mainOrdernum    = I('post.main_ordernum', '');
        $policyId        = I('post.policy_id', '');
        if (empty($begin) || empty($end) || empty($configId)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $teamOrderReportModel = new \Business\TeamOrder\TeamOrderReport();
        $sid = $loginInfo['sid'];
        $params = compact(
            'configId', 'sid', 'begin', 'end', 'lidParam',
            'operatorIdParam', 'pageNum', 'pageSize', 'dateType', 'fidParam', 'mainOrdernum', 'policyId');
        try {
            $result = $teamOrderReportModel->getTeamOrderSettleReport($params);
            $code   = $result['code'];
            $msg    = $result['msg'];
        } catch (\Exception $exception) {
            $code = $exception->getCode();
            $msg  = $exception->getMessage();
        }

        $this->apiReturn($code, $result['data'] ?? [], $msg);
    }


    /**
     * 获取分销商团单结算报表数据
     *
     * <AUTHOR>
     *
     * @throws \Exception
     */
    public function getTeamOrderSettleReportForFid()
    {
        $loginInfo = $this->getLoginInfo();
        //date_type 1-按日 2-按月或年汇总 对应的begin和end会根据date_type变化
        $dateType = I('date_type',1, 'intval');
        //开始时间
        $begin = I('begin');
        //结束时间
        $end   = I('end');
        //模板id
        $configId  = I('search_config_id');
        //页数
        $pageNum = I('page', 1, 'intval');
        //每页数量
        $pageSize = I('page_size', 10, 'intval');
        //查询参数
        $lidParam        = I('post.lid', '');
        $operatorIdParam = I('post.operator_id', '');
        $aidParam        = I('post.sid', '');
        $mainOrdernum    = I('post.main_ordernum', '');
        $policyId        = I('post.policy_id', '');
        if (empty($begin) || empty($end) || empty($configId)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $teamOrderReportModel = new \Business\TeamOrder\TeamOrderReport();
        $sid                  = $loginInfo['sid'];
        $type                 = 2;
        $params = compact(
            'configId', 'sid', 'begin', 'end', 'lidParam', 'operatorIdParam', 'pageNum',
            'pageSize', 'dateType', 'aidParam', 'mainOrdernum', 'policyId', 'type');
        try {
            $result = $teamOrderReportModel->getTeamOrderSettleReport($params);
            $code   = $result['code'];
            $msg    = $result['msg'];
        } catch (\Exception $exception) {
            $code = $exception->getCode();
            $msg  = $exception->getMessage();
        }

        $this->apiReturn($code, $result['data'] ?? [], $msg);
    }


    private function _exportOrderTwoList($data, $itemNum) {
        $filename = date('Ymd') . '团队验证报表';

        //数据列表
        $list   = $data['list'];
        //报表名称
        $name   = $data['name'];
        //纬度
        $title  = $data['title'];
        //总和
        $sum    = $data['sum'];
        //统计指标
        $target = $data['target'];

        $excel[0] = [
            $name
        ];

        //配置的纬度
        foreach ($title as $value) {
            $excel[1][] = $value;
        }

        //配置的统计指标
        foreach ($target as $key => $value) {
            $excel[1][] = $this->_needTarget[$value];
        }

        $excel[1][] = '销售金额';

        //从第二行开始
        $i = 2;
        foreach ($list as $valueOne) {
            switch ($itemNum) {
                case 1:
                    $excel[$i][] = $valueOne['title'];
                    foreach ($target as $item) {
                        $excel[$i][] = $valueOne['list'][$item];
                    }

                    $excel[$i][] = $valueOne['list']['real_money'];

                    $i++;
                    break;
                case 2:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        $excel[$i][] = $valueOne['title'];
                        $excel[$i][] = $valueTwo['title'];
                        foreach ($target as $item) {
                            $excel[$i][] = $valueTwo['list'][$item];
                        }
                        $excel[$i][] = $valueTwo['list']['real_money'];

                        $i++;
                    }

                    break;
                case 3:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            $excel[$i][] = $valueOne['title'];
                            $excel[$i][] = $valueTwo['title'];
                            $excel[$i][] = $valueThree['title'];
                            foreach ($target as $item) {
                                $excel[$i][] = $valueThree['list'][$item];
                            }

                            $excel[$i][] = $valueThree['list']['real_money'];

                            $i++;
                        }
                    }
                    break;
                case 4:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                $excel[$i][] = $valueOne['title'];
                                $excel[$i][] = $valueTwo['title'];
                                $excel[$i][] = $valueThree['title'];
                                $excel[$i][] = $valueFour['title'];
                                foreach ($target as $item) {
                                    $excel[$i][] = $valueFour['list'][$item];
                                }

                                $excel[$i][] = $valueFour['list']['real_money'];

                                $i++;
                            }
                        }
                    }
                    break;
                case 5:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    $excel[$i][] = $valueOne['title'];
                                    $excel[$i][] = $valueTwo['title'];
                                    $excel[$i][] = $valueThree['title'];
                                    $excel[$i][] = $valueFour['title'];
                                    $excel[$i][] = $valueFive['title'];
                                    foreach ($target as $item) {
                                        $excel[$i][] = $valueFive['list'][$item];
                                    }

                                    $excel[$i][] = $valueFive['list']['real_money'];

                                    $i++;
                                }
                            }
                        }
                    }
                    break;
                case 6:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        $excel[$i][] = $valueOne['title'];
                                        $excel[$i][] = $valueTwo['title'];
                                        $excel[$i][] = $valueThree['title'];
                                        $excel[$i][] = $valueFour['title'];
                                        $excel[$i][] = $valueFive['title'];
                                        $excel[$i][] = $valueSix['title'];
                                        foreach ($target as $item) {
                                            $excel[$i][] = $valueSix['list'][$item];
                                        }

                                        $excel[$i][] = $valueSix['list']['real_money'];

                                        $i++;
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 7:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            $excel[$i][] = $valueOne['title'];
                                            $excel[$i][] = $valueTwo['title'];
                                            $excel[$i][] = $valueThree['title'];
                                            $excel[$i][] = $valueFour['title'];
                                            $excel[$i][] = $valueFive['title'];
                                            $excel[$i][] = $valueSix['title'];
                                            $excel[$i][] = $valueSeven['title'];
                                            foreach ($target as $item) {
                                                $excel[$i][] = $valueSeven['list'][$item];
                                            }

                                            $excel[$i][] = $valueSeven['list']['real_money'];

                                            $i++;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 8:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            foreach ($valueSeven['list'] as $keyEight => $valueEight) {
                                                $excel[$i][] = $valueOne['title'];
                                                $excel[$i][] = $valueTwo['title'];
                                                $excel[$i][] = $valueThree['title'];
                                                $excel[$i][] = $valueFour['title'];
                                                $excel[$i][] = $valueFive['title'];
                                                $excel[$i][] = $valueSix['title'];
                                                $excel[$i][] = $valueSeven['title'];
                                                $excel[$i][] = $valueEight['title'];
                                                foreach ($target as $item) {
                                                    $excel[$i][] = $valueEight['list'][$item];
                                                }

                                                $excel[$i][] = $valueEight['list']['real_money'];

                                                $i++;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 9:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            foreach ($valueSeven['list'] as $keyEight => $valueEight) {
                                                foreach ($valueEight['list'] as $keyNight => $valueNight) {
                                                    $excel[$i][] = $valueOne['title'];
                                                    $excel[$i][] = $valueTwo['title'];
                                                    $excel[$i][] = $valueThree['title'];
                                                    $excel[$i][] = $valueFour['title'];
                                                    $excel[$i][] = $valueFive['title'];
                                                    $excel[$i][] = $valueSix['title'];
                                                    $excel[$i][] = $valueSeven['title'];
                                                    $excel[$i][] = $valueEight['title'];
                                                    $excel[$i][] = $valueNight['title'];
                                                    foreach ($target as $item) {
                                                        $excel[$i][] = $valueNight['list'][$item];
                                                    }

                                                    $excel[$i][] = $valueNight['list']['real_money'];

                                                    $i++;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 10:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            foreach ($valueSeven['list'] as $keyEight => $valueEight) {
                                                foreach ($valueEight['list'] as $keyNight => $valueNight) {
                                                    foreach ($valueNight['list'] as $keyTen => $valueTen) {
                                                        $excel[$i][] = $valueOne['title'];
                                                        $excel[$i][] = $valueTwo['title'];
                                                        $excel[$i][] = $valueThree['title'];
                                                        $excel[$i][] = $valueFour['title'];
                                                        $excel[$i][] = $valueFive['title'];
                                                        $excel[$i][] = $valueSix['title'];
                                                        $excel[$i][] = $valueSeven['title'];
                                                        $excel[$i][] = $valueEight['title'];
                                                        $excel[$i][] = $valueNight['title'];
                                                        $excel[$i][] = $valueTen['title'];
                                                        foreach ($target as $item) {
                                                            $excel[$i][] = $valueTen['list'][$item];
                                                        }

                                                        $excel[$i][] = $valueTen['list']['real_money'];

                                                        $i++;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 11:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            foreach ($valueSeven['list'] as $keyEight => $valueEight) {
                                                foreach ($valueEight['list'] as $keyNight => $valueNight) {
                                                    foreach ($valueNight['list'] as $keyTen => $valueTen) {
                                                        foreach ($valueTen['list'] as $keyEleven => $valueEleven) {
                                                            $excel[$i][] = $valueOne['title'];
                                                            $excel[$i][] = $valueTwo['title'];
                                                            $excel[$i][] = $valueThree['title'];
                                                            $excel[$i][] = $valueFour['title'];
                                                            $excel[$i][] = $valueFive['title'];
                                                            $excel[$i][] = $valueSix['title'];
                                                            $excel[$i][] = $valueSeven['title'];
                                                            $excel[$i][] = $valueEight['title'];
                                                            $excel[$i][] = $valueNight['title'];
                                                            $excel[$i][] = $valueTen['title'];
                                                            $excel[$i][] = $valueEleven['title'];
                                                            foreach ($target as $item) {
                                                                $excel[$i][] = $valueEleven['list'][$item];
                                                            }
                                                            $excel[$i][] = $valueEleven['list']['real_money'];
                                                            $i++;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
            }
        }

        $xls = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }

    /**
     * 分析获取团队数量
     * @throws \Exception
     */
    private function _handleTeamCheckReportV1($list, $itemNum, $item): array
    {
        if (empty($list) || !is_array($list)) {
            throw new \Exception('数据为空', 400);
        }

        $newList = [];
        foreach ($list as $value) {

            // 团队人数总计
            $this->_teamCheckNum += $value['check_num'];
            $this->_teamCheckSaleMoney += round($value['sale_money'] / 100, 2);

            switch ($itemNum) {
                case 1:
                    $one = $item[0];
                    if (isset($newList[$value[$one]])) {
                        $newList[$value[$one]]['list']['check_ticket'] += $value['check_num'];
                        $newList[$value[$one]]['list']['real_money']   += round($value['sale_money'] / 100, 2);
                        $newList[$value[$one]]['list']['team_num']     += $value['team_num'];
                    } else {
                        $newList[$value[$one]]['title'] = $this->_switchTitle($one, $value[$one]);
                        $newList[$value[$one]]['list'] = [
                            'check_ticket' => $value['check_num'],
                            'real_money'   => round($value['sale_money'] / 100, 2),
                            'team_num'     => $value['team_num']
                        ];
                    }
                    break;
                case 2:
                    $one = $item[0];
                    $two = $item[1];
                    if (isset($newList[$value[$one]]['list'][$value[$two]])) {
                        $newList[$value[$one]]['list'][$value[$two]]['list']['check_ticket'] += $value['check_num'];
                        $newList[$value[$one]]['list'][$value[$two]]['list']['real_money'] += round($value['sale_money'] / 100, 2);
                        $newList[$value[$one]]['list'][$value[$two]]['list']['team_num']   += $value['team_num'];
                    } else {
                        $newList[$value[$one]]['title'] = $this->_switchTitle($one, $value[$one]);
                        $newList[$value[$one]]['list'][$value[$two]]['title'] = $this->_switchTitle($two, $value[$two]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'] = [
                            'check_ticket' => $value['check_num'],
                            'real_money'   => round($value['sale_money'] / 100, 2),
                            'team_num'     => $value['team_num']
                        ];
                    }
                    break;
                case 3:
                    $one = $item[0];
                    $two = $item[1];
                    $three = $item[2];
                    if (isset($newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]])) {
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['check_ticket'] += $value['check_num'];
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['real_money'] += round($value['sale_money'] / 100, 2);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['team_num'] += $value['team_num'];
                    } else {
                        $newList[$value[$one]]['title'] = $this->_switchTitle($one, $value[$one]);
                        $newList[$value[$one]]['list'][$value[$two]]['title'] = $this->_switchTitle($two, $value[$two]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['title'] = $this->_switchTitle($three, $value[$three]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'] = [
                            'check_ticket' => $value['check_num'],
                            'real_money'   => round($value['sale_money'] / 100, 2),
                            'team_num'     => $value['team_num']
                        ];
                    }
                    break;
                case 4:
                    $one = $item[0];
                    $two = $item[1];
                    $three = $item[2];
                    $four = $item[3];
                    if (isset($newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]])) {
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list']['check_ticket'] += $value['check_num'];
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list']['real_money'] += round($value['sale_money'] / 100, 2);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list']['team_num'] += $value['team_num'];
                    } else {
                        $newList[$value[$one]]['title'] = $this->_switchTitle($one, $value[$one]);
                        $newList[$value[$one]]['list'][$value[$two]]['title'] = $this->_switchTitle($two, $value[$two]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['title'] = $this->_switchTitle($three, $value[$three]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['title'] = $this->_switchTitle($four, $value[$four]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'] = [
                            'check_ticket' => $value['check_num'],
                            'real_money'   => round($value['sale_money'] / 100, 2),
                            'team_num'     => $value['team_num']
                        ];
                    }
                    break;
                case 5:
                    $one = $item[0];
                    $two = $item[1];
                    $three = $item[2];
                    $four = $item[3];
                    $five = $item[4];
                    if (isset($newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]])) {
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list']['check_ticket'] += $value['check_num'];
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list']['real_money'] += round($value['sale_money'] / 100, 2);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list']['team_num'] += $value['team_num'];
                    } else {
                        $newList[$value[$one]]['title'] = $this->_switchTitle($one, $value[$one]);
                        $newList[$value[$one]]['list'][$value[$two]]['title'] = $this->_switchTitle($two, $value[$two]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['title'] = $this->_switchTitle($three, $value[$three]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['title'] = $this->_switchTitle($four, $value[$four]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['title'] = $this->_switchTitle($five, $value[$five]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'] = [
                            'check_ticket' => $value['check_num'],
                            'real_money'   => round($value['sale_money'] / 100, 2),
                            'team_num'     => $value['team_num']
                        ];
                    }
                    break;
                case 6:
                    $one = $item[0];
                    $two = $item[1];
                    $three = $item[2];
                    $four = $item[3];
                    $five = $item[4];
                    $six = $item[5];
                    if (isset($newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]])) {
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list']['check_ticket'] += $value['check_num'];
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list']['real_money'] += round($value['sale_money'] / 100, 2);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list']['team_num'] += $value['team_num'];
                    } else {
                        $newList[$value[$one]]['title'] = $this->_switchTitle($one, $value[$one]);
                        $newList[$value[$one]]['list'][$value[$two]]['title'] = $this->_switchTitle($two, $value[$two]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['title'] = $this->_switchTitle($three, $value[$three]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['title'] = $this->_switchTitle($four, $value[$four]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['title'] = $this->_switchTitle($five, $value[$five]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['title'] = $this->_switchTitle($six, $value[$six]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'] = [
                            'check_ticket' => $value['check_num'],
                            'real_money'   => round($value['sale_money'] / 100, 2),
                            'team_num'     => $value['team_num']
                        ];
                    }
                    break;
                case 7:
                    $one = $item[0];
                    $two = $item[1];
                    $three = $item[2];
                    $four = $item[3];
                    $five = $item[4];
                    $six = $item[5];
                    $seven = $item[6];

                    if (isset($newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]])) {
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list']['check_ticket'] += $value['check_num'];
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list']['real_money'] += round($value['sale_money'] / 100, 2);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list']['team_num'] += $value['team_num'];
                    } else {
                        $newList[$value[$one]]['title'] = $this->_switchTitle($one, $value[$one]);
                        $newList[$value[$one]]['list'][$value[$two]]['title'] = $this->_switchTitle($two, $value[$two]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['title'] = $this->_switchTitle($three, $value[$three]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['title'] = $this->_switchTitle($four, $value[$four]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['title'] = $this->_switchTitle($five, $value[$five]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['title'] = $this->_switchTitle($six, $value[$six]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['title'] = $this->_switchTitle($seven, $value[$seven]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'] = [
                            'check_ticket' => $value['check_num'],
                            'real_money'   => round($value['sale_money'] / 100, 2),
                            'team_num'     => $value['team_num']
                        ];
                    }
                    break;
                case 8:
                    $one = $item[0];
                    $two = $item[1];
                    $three = $item[2];
                    $four = $item[3];
                    $five = $item[4];
                    $six = $item[5];
                    $seven = $item[6];
                    $eight = $item[7];

                    if (isset($newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]])) {
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]]['list']['check_ticket'] += $value['check_num'];
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]]['list']['real_money'] += round($value['sale_money'] / 100, 2);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]]['list']['team_num'] += $value['team_num'];
                    } else {
                        $newList[$value[$one]]['title'] = $this->_switchTitle($one, $value[$one]);
                        $newList[$value[$one]]['list'][$value[$two]]['title'] = $this->_switchTitle($two, $value[$two]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['title'] = $this->_switchTitle($three, $value[$three]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['title'] = $this->_switchTitle($four, $value[$four]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['title'] = $this->_switchTitle($five, $value[$five]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['title'] = $this->_switchTitle($six, $value[$six]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['title'] = $this->_switchTitle($seven, $value[$seven]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]]['title'] = $this->_switchTitle($eight, $value[$eight]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]]['list'] = [
                            'check_ticket' => $value['check_num'],
                            'real_money'   => round($value['sale_money'] / 100, 2),
                            'team_num'     => $value['team_num']
                        ];
                    }
                    break;
                case 9:
                    $one = $item[0];
                    $two = $item[1];
                    $three = $item[2];
                    $four = $item[3];
                    $five = $item[4];
                    $six = $item[5];
                    $seven = $item[6];
                    $eight = $item[7];
                    $nine = $item[8];

                    if (isset($newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]]['list'][$value[$nine]])) {
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]]['list'][$value[$nine]]['list']['check_ticket'] += $value['check_num'];
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]]['list'][$value[$nine]]['list']['real_money'] += round($value['sale_money'] / 100, 2);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]]['list'][$value[$nine]]['list']['team_num'] += $value['team_num'];
                    } else {
                        $newList[$value[$one]]['title'] = $this->_switchTitle($one, $value[$one]);
                        $newList[$value[$one]]['list'][$value[$two]]['title'] = $this->_switchTitle($two, $value[$two]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['title'] = $this->_switchTitle($three, $value[$three]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['title'] = $this->_switchTitle($four, $value[$four]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['title'] = $this->_switchTitle($five, $value[$five]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['title'] = $this->_switchTitle($six, $value[$six]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['title'] = $this->_switchTitle($seven, $value[$seven]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]]['title'] = $this->_switchTitle($eight, $value[$eight]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]]['list'][$value[$nine]]['title'] = $this->_switchTitle($nine, $value[$nine]);
                        $newList[$value[$one]]['list'][$value[$two]]['list'][$value[$three]]['list'][$value[$four]]['list'][$value[$five]]['list'][$value[$six]]['list'][$value[$seven]]['list'][$value[$eight]]['list'][$value[$nine]]['list'] = [
                            'check_ticket' => $value['check_num'],
                            'real_money'   => round($value['sale_money'] / 100, 2),
                            'team_num'     => $value['team_num']
                        ];
                    }
                    break;
            }
        }

        return $newList;
    }

    /**
     * 获取所有需要的纬度的信息
     * <AUTHOR>
     * @date   2017-10-24
     *
     * @param array  $res   从数据库中获取的结果集
     * @param array  $item  需要获取的纬度
     */
    private function _getTitleInfo($res, $item) {
        foreach ($item as $value) {
            switch ($value) {
                case 'buyer_id':
                    //分销商
                    $resellerArr  = array_column($res, 'buyer_id');
                    $memberModel  = new Member();
                    $resellerRes  = [];

                    if (is_array($resellerArr) && !empty($resellerArr)) {
                        $resellerRes = $memberModel->getMemberInfoByMulti($resellerArr, 'id', 'id, dname');
                    }

                    $resellerInfo = [];
                    foreach ($resellerRes as $v) {
                        $resellerInfo[$v['id']] = $v['dname'];
                    }

                    $this->_buyerInfo = $resellerInfo;
                    break;
                case 'guide':
                    //导游
                    $guideArr   = array_column($res, 'guide');
                    $guideModel = $this->_getGuideModel();
                    $guideInfo  = $guideModel->getGuideInfoById($guideArr);
                    $guideRes   = [];
                    if (is_array($guideInfo)) {
                        foreach ($guideInfo as $v) {
                            $guideRes[$v['id']] = $v['name'];
                        }
                    }
                    $this->_guideInfo = $guideRes;
                    break;
                case 'buyer_group_id':
                    //分销商组
                    $buyerGroupIdArr = array_values(array_unique(array_column($res, 'buyer_group_id')));
                    if (count($buyerGroupIdArr) == 1 && $buyerGroupIdArr[0] == 0) {
                        $this->_buyerGroupInfo = [];
                        break;
                    }
                    $biz = new GroupQuery();
                    $groupInfo = $biz->queryGroupByIdList($buyerGroupIdArr);
                    $this->_buyerGroupInfo = empty($groupInfo) ? [] : array_column($groupInfo, null, 'id');
                    break;
            }
        }
    }

    /**
     * 获取某个特定维度的具体信息
     * <AUTHOR>
     * @date   2017-10-24
     *
     * @param  string  $item   需要获取的纬度
     * @param  string  $value  需要获取的纬度里的具体某个值
     */
    private function _switchTitle($item, $value) {
        switch ($item) {
            case 'date':
                $res = date('Y-m-d', strtotime($value));
                break;

            case 'buyer_id':
                $res = $this->_buyerInfo[$value] ?? '未知';
                break;

            case 'guide':
                $res = $this->_guideInfo[$value] ?? '未知';
                break;

            case 'city':
            case 'province':
                $areaModel = $this->_getAreaModel();
                $res = $areaModel->getProvinceAndCityNameById($value);
                $res = $res ?: '';
                break;

            case 'team_lid_num':
                $res = $value;
                break;
            case 'is_lodging':
                $res = empty($value) ? '否' : '是';
                break;
            case 'buyer_group_id':
                $res = $this->_buyerGroupInfo[$value]['groupName'] ?? '未知';
                break;
            default:
                $res = '未知';
                break;
        }

        return $res;
    }


    /**
     * 团队验证查询的时候 判断是否传了团队景区数量和主题
     * <AUTHOR>
     * @date 2018-07-25
     */
    private function teamReportCheckCondition($params): bool
    {
        $lidNum        = $params['lid_num'] ?? '';
        $topic         = $params['topic'] ?? [];
        if (!empty($lidNum)) {
            return true;
        }
        if (!empty($topic)) {
            return true;
        }
        return false;
    }
}