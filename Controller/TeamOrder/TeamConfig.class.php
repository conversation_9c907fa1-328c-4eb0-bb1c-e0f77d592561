<?php

namespace Controller\TeamOrder;

use Library\Controller;
use Business\TeamOrder\TeamConfig as TeamConfigBz;

class TeamConfig extends Controller
{
    private $sid;

    public function __construct()
    {
        $loginInfo = $this->getLoginInfo();
        $this->sid = $loginInfo['sid'];
    }

    /**
     * 新增/修改团单配置
     *
     * @return array
     * <AUTHOR>
     * @date 2021/10/9
     *
     *
     */
    public function addOrUpdateConfig()
    {
        $id               = I('post.id', 0, 'intval');
        $acquireStatus    = I('post.acquire_status', 1, 'intval');
        $teamRequire      = I('post.team_require', 1, 'intval');
        $bookAdvance      = I('post.book_advance', 1, 'intval');
        $refundCheckPrint = I('post.refund_check_print', 2, 'intval'); //出票订单是否可退 1:不可退 2:可退
        $payWay           = I('post.pay_way', '', 'strval');
        $idcardImportWay  = I('post.idcard_import_way', '', 'intval');
        $modifyUseOrder   = I('post.modify_use_order', 1, 'intval');
        $activeCheck      = I('post.active_check', 2, 'intval');    //  是否抱团激活：1=需要激活 2=不需要激活
        $enableTeamCode   = I('post.enable_teamcode', 2, 'intval'); // 是否开启团单码 1开启 2=不开启
        $enableTeamCheck  = I('post.enable_team_check', 2, 'intval');   // 是否 抱团核销 1开启，2=不开启
        $teamOrderMode    = I('post.team_order_mode', 2, 'intval');   // 报团下单模式 1：默认 2：日历模式
        $takeIdPicture    = I('post.take_id_picture', 2, 'intval');// 采集游客证件 1 开启 2 关闭
        $idPictureRequired = I('post.id_picture_required', 2, 'intval');// 证件必传 1 开启 2 关闭
        $params = compact(
            'acquireStatus', 'teamRequire', 'bookAdvance', 'refundCheckPrint', 'payWay',
            'idcardImportWay', 'modifyUseOrder', 'activeCheck', 'enableTeamCode', 'enableTeamCheck',
            'teamOrderMode', 'takeIdPicture', 'idPictureRequired'
        );
        $configBz      = new TeamConfigBz();
        $result        = $configBz->addOrUpdateConfig($this->sid, $id, $params);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取配置信息
     *
     * @return array
     * <AUTHOR>
     * @date 2021/10/11
     *
     *
     */
    public function getConfig()
    {
        $configBz = new TeamConfigBz();
        $result   = $configBz->getConfig($this->sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取供应商配置信息
     *
     * @return array
     * <AUTHOR>
     * @date 2021/10/18
     *
     *
     */
    public function getSupplyConfig()
    {
        $sourceId = I('post.sourceId', 0, 'intval');
        if (empty($sourceId)) {
            $this->apiReturn(203, [], "参数错误");
        }

        $configBz = new TeamConfigBz();
        $result   = $configBz->getConfig($sourceId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}