<?php

/**
 * 团对订单预订接口
 * <AUTHOR>
 */

namespace Controller\TeamOrder;

use Business\CommodityCenter\Ticket;
use Business\TeamOrder\Booking as BookingBiz;
use Business\Member\MemberRelation;
use Business\TeamOrder\TeamOrderDiscount;
use Library\Controller;
use Model\Member\Member;
use Model\Team\TeamConfig as TeamConfigModel;

class Booking extends Controller
{

    //当前登良路账号主账号id
    private $_sid;

    public function __construct()
    {
        $this->_sid = $this->isLogin('ajax');
    }

    /**
     * 获取供应商列表
     * <AUTHOR>
     * @date   2018-05-04
     */
    public function getSupplierList()
    {
        //搜索关键字
        $keyword = I('keyword', '');
        //如果为报团计调下单，直接获取的当前供应商名称返回
        $newspaperGroup = I('newspapergroup', 0, 'intval');
        $page           = I('page', 1, 'intval');
        $size           = I('size', 100, 'intval');

        if (!empty($newspaperGroup)) {
            $userinfo = $this->getLoginInfo();
            //如果问员工账号，则获取上级供应商账户名
            if ($userinfo['dtype'] == 6) {
                $member   = new Member();
                $userinfo = $member->getMemberInfo($this->_sid, 'id', 'dname');
            }
            $data['list'][] = [
                'id'    => $this->_sid,
                'dname' => $userinfo['dname'],

            ];

            $this->apiReturn(200, $data, '');
        }

        $relationBiz = new MemberRelation();
        $result      = $relationBiz->getSupplierList($this->_sid, $page, $size, $keyword);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 产品搜索
     * <AUTHOR>
     * @date   2018-05-04
     */
    public function productSearch()
    {

        //指定日期
        $date = I('date', '');
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        //分销商id
        $fid = I('fid', 0, 'intval');
        //产品类型
        // $type    = I('type', 'A');
        //搜索关键字
        $keyword = I('keyword', '');
        //当前页码
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 5, 'intval');
        //报团类型id
        $teamTypeId  = I('team_type_id', 0, 'intval');
        $lids        = I('lids', '', 'strval'); //需要获取的景区id
        $excludeLids = I('exclude_lids', '', 'strval'); //不需要获取的景区id
        // $date = '2018-05-08';
        // $aid = 3385;
        if (!$date || (!$aid && !$fid)) {
            $this->apiReturn(204, [], '参数错误');
        }
        //如果选择了报团类型，这边要走另外的接口来获取产品信息
        if ($teamTypeId) {
            $teamSupply = 1; //是否为报团计调 1：是 0：否
            if (empty($fid)) {
                $fid        = $this->_sid;
                $teamSupply = 0;
            }
            $teamRuleBz = new \Business\TeamOrder\TeamRules();
            $result     = $teamRuleBz->getProductByTeamTypeId($aid, $fid, $teamTypeId, $date, $keyword, $page, $size, $teamSupply);

            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        if ($lids) {
            $lids = implode(',', array_unique(explode(',', $lids)));
        }
        if ($excludeLids) {
            $excludeLids = implode(',', array_unique(explode(',', $excludeLids)));
        }
        $bookingBiz       = new BookingBiz();
        $teamConfigModel  = new TeamConfigModel();
        $configInfo       = $teamConfigModel->getConfigBySid($aid, "config");
        $configInfo       = empty($configInfo['config']) ? [] : json_decode($configInfo['config'], true);
        $isQueryBeforeDay = $configInfo['book_advance'] == 1 ? 0 : 1;
        //如果分销商id不为空,aid为空，则为抱团计调下单，当前用户为供应商
        if (!empty($fid)) {
            $result = $bookingBiz->productSearch($fid, $this->_sid, $date, $keyword, $page, $size, 1, $lids, $excludeLids, $isQueryBeforeDay);
        } else {
            $result = $bookingBiz->productSearch($this->_sid, $aid, $date, $keyword, $page, $size, 2, $lids, $excludeLids);
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取剩余的全部门票
     * <AUTHOR>
     * @date   2018-05-08
     */
    public function showMoreTickets()
    {
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        //产品id
        $productId = I('product_id', 0, 'intval');
        //日期
        $date = I('date', '');
        //分销商id
        $fid = I('fid', 0, 'intval');

        if ((!$aid && !$fid) || !$productId || !$date) {
            $this->apiReturn(204, [], '参数错误');
        }

        $bookingBiz = new BookingBiz();
        //报团计调获取数据使用
        if (!empty($fid)) {
            $result = $bookingBiz->showMoreTickets($fid, $productId, $this->_sid, $date);
        } else {
            $result = $bookingBiz->showMoreTickets($this->_sid, $productId, $aid, $date);
        }

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     * 预定页面-获取演出场次信息
     * <AUTHOR>
     * @time   2017-01-15
     */
    public function getShowInfo()
    {
        $landId = I('lid', '', 'intval');
        $tid    = I('tid', '', 'intval');
        $aid    = I('aid', '', 'intval');
        $date   = I('date', '');

        if (!$landId || !$aid || !$date) {
            $this->apiReturn(201, [], '参数错误');
        }
        $bookingBiz = new BookingBiz();
        $result     = $bookingBiz->getShowInfo($aid, $this->_sid, $landId, $tid, $date);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取报团景区列表
     *
     * @date 2023/08/01
     * @auther yangjianhui
     * @return array
     */
    public function getTeamLandList()
    {
        $aid        = I('aid', 0, 'intval');
        $fid        = I('fid', 0, 'intval');
        $page       = I('page', 1, 'intval');
        $size       = I('size', 10, 'intval');
        $teamTypeId = I('team_type_id', 0, 'intval');
        $keyWord    = I('key_word', '', 'strval');
        if (empty($aid) || empty($fid)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $teamBookBz = new \Business\TeamOrder\Booking();
        $result     = $teamBookBz->getTeamLandList($aid, $fid, $keyWord, $teamTypeId, $this->_sid, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取报团产品价格跟库存
     *
     * @date 2023/08/01
     * @auther yangjianhui
     * @return array
     */
    public function getTicketPriceAndStorage()
    {
        $date = I('date', '', 'strval');
        $aid  = I('aid', 0, 'intval');
        $fid  = I('fid', 0, 'intval');
        $tids = I('tids', '', 'strval');
        if (empty($date)  || empty($aid) || empty($fid) || empty($tids)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $teamBookBz = new \Business\TeamOrder\Booking();
        $result     = $teamBookBz->getTicketPriceAndStorage($date, $aid, $fid, $tids);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取日历模式团单产品列表
     *
     * @date 2023/08/01
     * @auther yangjianhui
     * @return array
     */
    public function getCalendarTicketList()
    {
        $startDate = I('start_date', '', 'strval');
        $endDate   = I('end_date', '', 'strval');
        $aid       = I('aid', 0, 'intval');
        $fid       = I('fid', 0, 'intval');
        $lid       = I('lid', 0, 'intval');
        if (empty($startDate) || empty($endDate) || empty($aid) || empty($fid)) {
            $this->apiReturn(203, [], '参数错误');
        }
        $teamDiscountBz = new TeamOrderDiscount($this->_sid);
        $result         = $teamDiscountBz->getCalendarTicketList($startDate, $endDate, $aid, $fid, $lid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }



    /**
     *  获取演出场次列表
     * <AUTHOR>
     * @date   2018-02-05
     *
     * @param  int  $venueId  场馆ID
     * @param  int  $date  日期
     * @param  int  $topSellerId  一级分销商，用来获取分销库存
     *
     * @return array
     */
    private function _getRoundsInfo($venueId, $date, $topSellerId)
    {
        if (!$venueId || !$date || !$topSellerId) {
            return [];
        }

        $showBiz = new \Business\Product\Show();
        $date    = date('Y-m-d', strtotime($date));
        $tmpList = $showBiz->getRoundList($venueId, $date, $topSellerId, $zoneId = false, $isRemoveExpire = true);
        $code    = $tmpList['code'];

        //数据出错了
        if ($code == 0) {
            return [];
        }

        //数据格式处理
        $roundList = [];
        $tmpData   = $tmpList['data'];
        foreach ($tmpData as $item) {
            $roundInfo = [
                'id'           => intval($item['id']),
                'round_name'   => $item['round_name'],
                'bt'           => $item['bt'],
                'et'           => $item['et'],
                'venus_id'     => $item['venus_id'],
                'use_date'     => $item['use_date'],
                'storage'      => $item['storage'],
                'area_storage' => $item['area_storage'],
            ];

            $roundList[] = $roundInfo;
        }

        return $roundList;
    }

    /**
     * 获取票类信息
     *
     * @param  [type] $tickets 门票列表
     *
     * @return [type]          [description]
     */
    private function _getTicketsInfo($tickets)
    {
        $return = [];
        foreach ($tickets as $ticket) {

            if ($ticket['pay'] == 0) {
                continue;
            }

            $return[] = [
                'pid'          => $ticket['pid'],
                'tid'          => $ticket['tid'],
                'title'        => $ticket['title'],
                'price'        => $ticket['tprice'],
                'zone_id'      => $ticket['zone_id'],
                'tourist_info' => $ticket['tourist_info'],
            ];
        }

        return $return;
    }
}