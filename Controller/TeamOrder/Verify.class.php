<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/3/13
 * Time: 9:03
 */

namespace Controller\TeamOrder;

use Business\JavaApi\Product\EvoluteGroupSpecialityService;
use Business\Member\Member as MemberBiz;
use Business\TeamOrder\TeamOrderVerify;
use Library\Constants\OrderConst;
use Library\Controller;
use Model\Member\Member;
use Model\Order\OrderTools;
use Model\Order\TeamOrderSearch;
use Model\Product\Ticket;
use Model\TeamOrder\Verify as VerifyModel;

class Verify extends Controller
{
    //当前登录账号主账号id
    private $_sid         = null;
    private $_verifyModel = null;

    private $_status = [
        0 => '未审核',
        1 => '同意',
        2 => '拒绝',
        3 => '取消',
    ];

    public function __construct()
    {
        $this->_sid         = $this->isLogin('ajax');
        $this->_verifyModel = new VerifyModel();
    }

    /**
     * 获取审核配置列表
     * <AUTHOR> Li
     * @date   2019-03-14
     *
     * return array
     */
    public function getAuditConfList()
    {
        $page     = I('get.page', 1, 'intval');
        $pageSize = I('get.pageSize', 20, 'intval');

        $result = $this->_verifyModel->getAuditConfList($this->_sid, $page, $pageSize);

        $list  = $result['list'];
        $total = $result['total'];

        if ($list) {
            $confIdArr  = array_column($list, 'id');
            $detaildArr = $this->_verifyModel->getAuditConfDetail($this->_sid, $confIdArr);

            if (!$detaildArr) {
                $this->apiReturn(204, [], '配置未找到');
            }

            foreach ($list as $key => $value) {
                foreach ($detaildArr as &$detail) {
                    if ($value['id'] == $detail['conf_id']) {
                        $tmp                 = $this->_moreData($detail);
                        $list[$key]['group'] = $tmp['group_ids'] ?: [];
                        $list[$key]['fids']  = $tmp['fids'] ?: [];
                        $list[$key]['tids']  = $tmp['tids'] ?: [];
                        unset($tmp);
                    }
                }

                //配置权限检测
                $auditConf  = $this->_checkAuditConf($value['audit_conf']);
                $sellConf   = $this->_checkSellConf($value['sell_notice']);
                $buyerConf  = $this->_checkAuditConf($value['buyer_notice']);
                unset($list[$key]['audit_conf'], $list[$key]['sell_notice'], $list[$key]['buyer_notice']);

                $list[$key]['order']        = $auditConf['order'] == 0 ? '' : '下单需要审核';
                $list[$key]['addTicket']    = $auditConf['addTicket'] == 0 ? '' : '增加门票需要审核';
                $list[$key]['refundTicket'] = $auditConf['refundTicket'] == 0 ? '' : '退票需要审核';
                $list[$key]['cancelTicket'] = $auditConf['cancelTicket'] == 0 ? '' : '取消票需要审核';
                $list[$key]['pass']         = $sellConf['pass'] == 0 ? '' : '审核通过通知分销商';
                $list[$key]['refuse']       = $sellConf['refuse'] == 0 ? '' : '审核拒绝通知分销商';
                $list[$key]['change']       = $sellConf['change'] == 0 ? '' : '供应商修改报团申请通知分销商';
                $list[$key]['by_order']     = $buyerConf['order'] == 0 ? '' : '分销商申请下单通知供应商';
                $list[$key]['by_add']       = $buyerConf['addTicket'] == 0 ? '' : '分销商申请修改团单通知供应商';
                $list[$key]['by_refund']    = $buyerConf['refundTicket'] == 0 ? '' : '分销商申请退票通知供应商';
            }
        }

        $return = [
            'list'       => array_values($list),
            'page'       => $page,
            'page_size'  => $pageSize,
            'total_page' => ceil($total / $pageSize),
            'total'      => $total,
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 添加审核配置
     * <AUTHOR> Li
     * @date   2019-03-14
     *
     * return array
     */
    public function addAuditConf()
    {
        $id             = I('post.id', 0, 'intval');
        $title          = I('post.title');
        $auditConfArr   = I('post.audit_conf');
        $sellNoticeArr  = I('post.sell_notice');
        $buyerNoticeArr = I('post.buyer_notice');
        $groupIds       = I('post.group_ids');
        $fids           = I('post.fids');
        $tids           = I('post.tids');

        if (!$title) {
            $this->apiReturn(203, [], '参数错误');
        }

        $auditConf   = array_sum($auditConfArr);
        $sellNotice  = array_sum($sellNoticeArr);
        $buyerNotice = array_sum($buyerNoticeArr);

        //先判断审核配置之前是否有存在过  有存在直接提示存在的配置名称
        //取出每个票对应的配置
        $baseConfig = $this->_verifyModel->getFidConfig($this->_sid, true);
        $ticketMap  = [];
        if ($baseConfig) {
            foreach ($baseConfig as $conf) {
                if ($id == $conf['id']) {
                    continue;
                }
                $tidArr = json_decode($conf['tids'], true);
                foreach ($tidArr as $tid) {
                    $ticketMap[$tid][$conf['id']]['audit_conf'] = array_filter(array_values($this->_checkAuditConf($conf['audit_conf'], 0)));
                    $ticketMap[$tid][$conf['id']]['title']      = $conf['title'];
                }
            }
        }

        //当前配置了所有产品 并且原先有配置所有产品  需要判断配置是否冲突
        if (isset($ticketMap['all'])) {
            foreach ($ticketMap['all'] as $confId => $confInfo) {
                if ($id && $confId == $id && array_sum($confInfo['audit_conf']) == $auditConf) {
                    continue;
                }
                //配置了所有 需要再判断出审核配置是否冲突
                if ($this->_checkAuditConfig($confInfo['audit_conf'], $auditConfArr)) {
                    $this->apiReturn(204, [], "配置保存失败，已配置{$confInfo['title']}审核，请检查后重试");
                }
            }
        } elseif (empty($tids) && !isset($ticketMap['all']) && count($ticketMap) > 1) {
            //原先没配置所有产品 现在要配置所有产品 需要判断配置是否冲突
            foreach ($ticketMap as $tid => $configInfo) {
                foreach ($configInfo as $confId => $config) {
                    if ($id && $id == $confId && array_sum($config['audit_conf']) == $auditConf) {
                        continue;
                    }
                    if ($this->_checkAuditConfig($config['audit_conf'], $auditConfArr)) {
                        $this->apiReturn(204, [], "配置保存失败，已配置{$config['title']}审核，请检查后重试");
                    }
                }
            }
        } elseif (!empty($tids) && !isset($ticketMap['all'])) {
            $tidMap = [];
            $javaApi = new \Business\CommodityCenter\Ticket();
            $ticketInfo = $javaApi->queryTicketInfoByIds($tids);
            if ($ticketInfo) {
                foreach ($ticketInfo as $item) {
                    $tidMap[$item['ticket']['id']] = $item['ticket']['title'];
                }
            }

            foreach ($tids as $tid) {
                if (!isset($ticketMap[$tid])) {
                    continue;
                }

                foreach ($ticketMap[$tid] as $confId => $confInfo) {
                    if ($id && $id == $confId && array_sum($confInfo['audit_conf']) == $auditConf) {
                        continue;
                    }
                    if ($this->_checkAuditConfig($confInfo['audit_conf'], $auditConfArr)) {
                        $this->apiReturn(204, [], "配置保存失败，'{$tidMap[$tid]}'门票已配置'{$confInfo['title']}'审核，请检查后重试");
                    }
                }
            }
        }

        $detailData = [];
        if ($groupIds) {
            $detailData['group_ids'] = json_encode($groupIds);
        }

        if ($fids) {
            $detailData['fids'] = json_encode($fids);
        }

        if ($tids) {
            $detailData['tids'] = json_encode($tids);
        }

        $result = $this->_verifyModel->addAuditConf($id, $this->_sid, $title, $auditConf, $sellNotice, $buyerNotice,
            $detailData);
        if (!$result) {
            $this->apiReturn(204, [], '保存失败');
        }

        $this->apiReturn(200, [], '保存成功');
    }

    /**
     * 删除审核配置
     * <AUTHOR> Li
     * @date   2019-03-14
     *
     * return array
     */
    public function deleteAuditConf()
    {
        //将基础配置及门票配置表中status改为1
        $id = I('post.id');
        if (!$id) {
            $this->apiReturn('203', [], '参数错误');
        }

        $result = $this->_verifyModel->deleteAuditConf($id, $this->_sid);
        if (!$result) {
            $this->apiReturn(204, [], '删除失败');
        }

        $this->apiReturn(200, [], '删除成功');
    }

    /**
     * 获取分销商对应的配置
     * <AUTHOR> Li
     * @date   2019-03-17
     *
     * return array
     */
    public function getConfigDetail()
    {
        $id = I('post.id');

        if (!$id) {
            $this->apiReturn('203', [], '参数错误');
        }

        $result = $this->_verifyModel->getConfDetail($id);
        if (!$result) {
            $this->apiReturn(204, [], '暂无数据');
        }

        $tmp             = $this->_moreData($result);
        $result['group'] = $tmp['group_ids'] ?: [];
        $result['fids']  = $tmp['fids'] ?: [];
        $result['tids']  = $tmp['tids'] ?: [];
        unset($tmp, $result['group_ids']);

        //配置权限检测
        $auditConf = $this->_checkAuditConf($result['audit_conf'], 0);
        $sellConf  = $this->_checkSellConf($result['sell_notice'], 0);
        $buyerConf = $this->_checkAuditConf($result['buyer_notice'], 0);

        $result['audit_conf']   = [$auditConf['order'], $auditConf['addTicket'], $auditConf['refundTicket'], $auditConf['cancelTicket']];
        $result['sell_notice']  = [$sellConf['pass'], $sellConf['refuse'], $sellConf['change']];
        $result['buyer_notice'] = [$buyerConf['order'], $buyerConf['addTicket'], $buyerConf['refundTicket']];

        $this->apiReturn(200, $result);
    }

    /**
     * 获取分销商对应的配置
     * <AUTHOR> Li
     * @date   2019-03-17
     *
     * return array
     */
    public function getFidConfig()
    {
        $fid = I('post.fid');

        if (!$fid) {
            $this->apiReturn('203', [], '参数错误');
        }

        $result = $this->_verifyModel->getFidConfig($this->_sid);

        if (!$result) {
            $this->apiReturn(204, [], '暂无数据');
        }
        //检测分组
        $groupId  = 0;
        //判断分销商是否在新分组中
        $evoluteGroupApi  = new \Business\JavaApi\Product\EvoluteGroupUser();
        $userGroupInfoArr = $evoluteGroupApi->queryDistributor($this->_sid, $fid);
        if ($userGroupInfoArr['code'] == 200 && !empty($userGroupInfoArr['data'])) {
            $groupId = $userGroupInfoArr['data']['groupId'];
        }

        $idArr      = [];
        $detaildArr = $this->_verifyModel->getFidConfigDetail($fid, $this->_sid, $groupId);

        if (!$detaildArr) {
            $this->apiReturn(204, [], '配置未找到');
        }

        foreach ($result as $key => $value) {
            foreach ($detaildArr as &$detail) {
                if ($value['id'] == $detail['conf_id']) {
                    $idArr[]               = $value['id'];
                    $tmp                   = $this->_moreData($detail);
                    $result[$key]['group'] = $tmp['group_ids'] ?: [];
                    $result[$key]['fids']  = $tmp['fids'] ?: [];
                    $result[$key]['tids']  = $tmp['tids'] ?: [];
                    unset($tmp);
                }
            }
            if (in_array($value['id'], $idArr)) {

                //配置权限检测
                $auditConf = $this->_checkAuditConf($value['audit_conf']);
                $sellConf  = $this->_checkSellConf($value['sell_notice']);
                $buyerConf = $this->_checkAuditConf($value['buyer_notice']);
                unset($result[$key]['audit_conf'], $result[$key]['sell_notice'], $result[$key]['buyer_notice']);

                $result[$key]['order']        = $auditConf['order'];
                $result[$key]['addTicket']    = $auditConf['addTicket'];
                $result[$key]['refundTicket'] = $auditConf['refundTicket'];
                $result[$key]['cancelTicket'] = $auditConf['cancelTicket'];
                $result[$key]['pass']         = $sellConf['pass'];
                $result[$key]['refuse']       = $sellConf['refuse'];
                $result[$key]['change']       = $sellConf['change'];
                $result[$key]['by_order']     = $buyerConf['order'];
                $result[$key]['by_add']       = $buyerConf['addTicket'];
                $result[$key]['by_refund']    = $buyerConf['refundTicket'];
            } else {
                unset($result[$key]);
            }
        }

        $this->apiReturn(200, array_values($result));

    }

    /**
     * 获取报团审核申请列表
     * <AUTHOR> Li
     * @date   2019-03-19
     *
     * @return array
     */
    public function getTeamorderStashList()
    {
        $dtype      = I('post.dtype');   //用来判断是供应商的审核列表还是分销商的申请列表  1供应商 2分销商
        $playTime   = I('post.play_time', '', 'strval');
        $identify   = I('post.identify', '', 'strval');
        $sid        = I('post.sid', 0, 'intval');
        $fid        = I('post.fid', 0, 'intval');
        $type       = I('post.type', 0, 'intval');
        $status     = I('post.status', 4, 'intval');
        $applyTime  = I('post.apply_time', '', 'strval');
        $verifyTime = I('post.verify_time', '', 'strval');
        $page       = I('post.page', 1, 'intval');
        $pageSize   = I('post.size', 15, 'intval');
        $teamOrderVerifyBz = new TeamOrderVerify();
        $result = $teamOrderVerifyBz->getTeamorderStashList($this->_sid, $dtype, $playTime, $identify, $sid,
            $fid, $applyTime, $verifyTime, $page, $pageSize, $type, $status);


        $this->apiReturn($result['code'], $result['data']);
    }

    /**
     * 获取报团审核申请详情
     * <AUTHOR> Li
     * @date   2019-03-19
     *
     * @return array
     */
    public function getTeamorderStashById()
    {
        $id   = I('get.id');
        $type = I('get.type'); //查看类型 1：供应商产看 2：分销商查看
        if (!$id) {
            $this->apiReturn('203', [], '缺少必要参数');
        }

        $teamOrderVerifyBz = new TeamOrderVerify();
        $result = $teamOrderVerifyBz->getTeamorderStashById($this->_sid, $id, $type);


        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 修改报团申请产品
     * <AUTHOR> Li
     * @date   2019-03-20
     *
     * @return array
     */
    public function changeTeamorderProduct()
    {
        $id              = I('post.id');
        $ticketInfo      = I('post.ticket_info');
        $touristInfo     = I('post.tourist_info');
        $moreCredentials = I('post.moreCredentials', []);
        //游客取票人填写更多信息
        $moreTouristCredentials = I('post.moreTouristCredentials', []);
        $orderIdCard            = I('post.order_id_card', '');
        $voucherType            = I('post.voucherType', 1);
        $orderIdCardTickets     = I('post.orderIdCardTickets', []);
        $timeShareInfo          = I('post.time_share_info', '');
        if (!$id) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        if (!$ticketInfo) {
            $this->apiReturn(203, [], '您未做任何修改！');
        }

        //获取出相关门票的信息
        $tidArr = array_keys($ticketInfo);

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'title,id,pid', 'id', 'title,imgpath,id');
        $pidArray  = [];
        foreach ($ticketArr as $ticketInfos) {
            $pidArray[$ticketInfos['ticket']['id']] = [
                'id'      => $ticketInfos['ticket']['id'],
                'ttitle'  => $ticketInfos['ticket']['title'],
                'tid'     => $ticketInfos['ticket']['id'],
                'pid'     => $ticketInfos['ticket']['pid'],
                'title'   => $ticketInfos['land']['title'],
                'imgpath' => $ticketInfos['land']['imgpath'],
                'landid'  => $ticketInfos['land']['id'],
            ];
        }

        //先获取出原有产品记录
        $oldTeamirder = $this->_verifyModel->getTeamorderStashById($id, 'id,team_ordernum,sid,fid,group_products,type,order_mid,ext_content,evolute_info');
        $oldArray     = json_decode($oldTeamirder['group_products'], true);
        $oldTidArr    = array_keys($oldArray);
        $extContent   = $oldTeamirder['ext_content'] ? json_decode($oldTeamirder['ext_content'],true) : [];
        //取出所有相关的门票
        $allTidArr = array_unique(array_merge($tidArr, $oldTidArr));

        //获取该笔申请历史修改记录
        $logArr = $this->_verifyModel->getLastChangeRecordsByStashId($id);

        //处理修改记录数据
        $tmpSaveData   = [];
        $trackSaveData = [];

        $idCardArr = [];
        if ($touristInfo) {
            foreach ($touristInfo as $tid => $item) {
                $touristList = explode(',', $item);
                foreach ($touristList as $key => $value) {
                    $touristInfo = explode(':', $value);
                    if ($oldTeamirder['type'] == 1 && !empty($touristInfo[0]) && !empty($touristInfo[1])) {
                        $idCardArr[$tid][$key]['idcard'] = $touristInfo[0];
                        $idCardArr[$tid][$key]['name']   = $touristInfo[1];
                        $idCardArr[$tid][$key]['voucher_type'] = $touristInfo[2] ?? 1;
                    } elseif (!empty($touristInfo[0]) && !empty($touristInfo[1])) {
                        $idCardArr[$tid][$key]['idcard']  = $touristInfo[0];
                        $idCardArr[$tid][$key]['tourist'] = $touristInfo[1];
                    }
                }
            }
        }

        $sonOrderTNumArr = [];
        $sonOrderNumArr  = [];
        if ($oldTeamirder['team_ordernum']) {
            $teamOrderModel = new TeamOrderSearch();
            $teamOrderList  = $teamOrderModel->getSonOrderInfoByMainOrderNum($oldTeamirder['team_ordernum']);
            if ($teamOrderList) {
                $orderModel = new OrderTools();
                //通过子订单号获取出门票对应的票数
                $tmpSonOrderArr = array_column($teamOrderList, 'son_ordernum');
                $sonOrderInfo   = $orderModel->getOrderInfo($tmpSonOrderArr, 'tid,tnum');
                foreach ($sonOrderInfo as $item) {
                    if (!isset($sonOrderTNumArr[$item['tid']])) {
                        $sonOrderTNumArr[$item['tid']] = $item['tnum'];
                    }
                }
                foreach ($teamOrderList as $item) {
                    $sonOrderNumArr[$item['tid']] = $item['son_ordernum'];
                }
            }
        }

        //修改申请产品规则需要重新调整  $ticketInfo中的数量只是增加的笔数， 也总数无关。
        foreach ($ticketInfo as $tid => $num) {
            $num = (int)$num;   //增加的票数
            if (in_array($tid, $oldTidArr)) {
                if ($oldTeamirder['type'] == 1) {
                    $oldNum = (int)$oldArray[$tid];
                } else {
                    $oldNum = (int)$oldArray[$tid]['old_num'];
                }
                //第一次修改的
                if (!isset($logArr[$tid])) {
                    if (isset($sonOrderTNumArr[$tid])) {
                        $oldNum = $sonOrderTNumArr[$tid];
                    }
                    //上次操作的数量
                    $leftNum = isset($oldArray[$tid]['add_num']) ? $oldArray[$tid]['add_num'] : (isset($oldArray[$tid]['reduce_num']) ? $oldArray[$tid]['reduce_num'] : 0);

                    //下单申请处理
                    //如果在原有基础上则判断票数是否有做修改
                    if ($num != $oldNum) {
                        //获取出上一次操作的数量来比对
                        if ($oldTeamirder['type'] == 1) {
                            $newTnum = $oldArray[$tid];
                        } else {
                            $newTnum = $oldArray[$tid]['tnum'];
                        }

                        //增加门票
                        if ($oldTeamirder['type'] == 2) {
                            //本次数量大于上次数量 为增加
                            if ($num > $newTnum) {
                                //最终票数以修改票数为主
                                $tmpSaveData[$tid]['tnum']     = $num;
                                $tmpSaveData[$tid]['old_num']  = $oldNum;
                                $tmpSaveData[$tid]['add_num']  = $num - $oldNum;
                                $tmpSaveData[$tid]['left_num'] = $num - $newTnum;                                             //本次操作数量
                                $tmpSaveData[$tid]['mome']     = '报团申请增加' . abs($tmpSaveData[$tid]['left_num']) . '张票';    //操作备注
                            } elseif ($num < $newTnum) {
                                //最终票数以修改票数为主
                                $tmpSaveData[$tid]['tnum'] = $num;
                                //本次数量小于上次数量 为减少
                                $tmpSaveData[$tid]['old_num']  = $oldNum;
                                $tmpSaveData[$tid]['add_num']  = $num - $oldNum;
                                $tmpSaveData[$tid]['left_num'] = $num - $newTnum;                                             //本次操作数量
                                $tmpSaveData[$tid]['mome']     = '报团申请减少' . abs($tmpSaveData[$tid]['left_num']) . '张票';    //操作备注
                            } else {
                                $tmpSaveData[$tid]['tnum']       = $oldArray[$tid]['tnum'];
                                $tmpSaveData[$tid]['old_num']    = $oldArray[$tid]['old_num'];
                                $tmpSaveData[$tid]['add_num']    = $oldArray[$tid]['add_num'];
                                $tmpSaveData[$tid]['left_num']   = $oldArray[$tid]['left_num'];                                             //本次操作数量
                                $tmpSaveData[$tid]['reduce_num'] = $oldArray[$tid]['reduce_num'];
                                $tmpSaveData[$tid]['mome']       = '报团申请产品未修改';                                           //操作备注
                            }
                        } elseif ($oldTeamirder['type'] == 1) {
                            //最终票数以修改票数为主
                            $tmpSaveData[$tid]['tnum']    = $num;
                            $tmpSaveData[$tid]['old_num'] = $oldNum;
                            //下单走这边 通过本次操作数量和原始数量进行比对
                            if ($num > $oldNum) {
                                $tmpSaveData[$tid]['left_num'] = $num - $oldNum;
                                $tmpSaveData[$tid]['add_num']  = $num - $oldNum;                                             //本次操作数量
                                $tmpSaveData[$tid]['mome']     = '报团申请增加' . $tmpSaveData[$tid]['add_num'] . '张票';    //操作备注
                            } elseif ($num < $oldNum) {
                                $tmpSaveData[$tid]['left_num']   = $oldNum - $num;
                                $tmpSaveData[$tid]['reduce_num'] = $oldNum - $num;     //本次操作数量
                                $tmpSaveData[$tid]['mome']       = '报团申请减少' . abs($leftNum - $tmpSaveData[$tid]['reduce_num']) . '张票';  //操作备注
                            } else {
                                $tmpSaveData[$tid]['tnum']     = $oldArray[$tid]['tnum'];
                                $tmpSaveData[$tid]['old_num']  = $oldArray[$tid]['old_num'];
                                $tmpSaveData[$tid]['add_num']  = $oldArray[$tid]['add_num'];
                                $tmpSaveData[$tid]['left_num'] = $oldArray[$tid]['left_num'];                                             //本次操作数量
                                $tmpSaveData[$tid]['mome']     = '报团申请产品未修改';                                           //操作备注
                            }
                        } else {
                            //下单走这边 通过本次操作数量和原始数量进行比对
                            if ($num < $newTnum) {
                                $tmpSaveData[$tid]['tnum']       = $num;
                                $tmpSaveData[$tid]['old_num']    = $oldNum;
                                $tmpSaveData[$tid]['reduce_num'] = $oldNum - $num;     //本次操作数量
                                //最终票数以修改票数为主
                                $tmpSaveData[$tid]['left_num'] = -($newTnum - $num);
                                $tmpSaveData[$tid]['mome']     = '报团申请增加' . abs($tmpSaveData[$tid]['left_num']) . '张票';    //操作备注
                            } elseif ($num > $newTnum) {
                                $tmpSaveData[$tid]['tnum']       = $num;
                                $tmpSaveData[$tid]['old_num']    = $oldNum;
                                $tmpSaveData[$tid]['reduce_num'] = $oldNum - $num;     //本次操作数量
                                //最终票数以修改票数为主
                                $tmpSaveData[$tid]['left_num'] = $num - $newTnum;
                                $tmpSaveData[$tid]['mome']     = '报团申请减少' . $tmpSaveData[$tid]['left_num'] . '张票';  //操作备注
                            } else {
                                $tmpSaveData[$tid]['tnum']       = $oldArray[$tid]['tnum'];
                                $tmpSaveData[$tid]['old_num']    = $oldArray[$tid]['old_num'];
                                $tmpSaveData[$tid]['add_num']    = $oldArray[$tid]['add_num'];
                                $tmpSaveData[$tid]['left_num']   = $oldArray[$tid]['left_num'];                                             //本次操作数量
                                $tmpSaveData[$tid]['reduce_num'] = $oldArray[$tid]['reduce_num'];
                                $tmpSaveData[$tid]['mome']       = '报团申请产品未修改';                                           //操作备注
                            }
                        }
                    } else {
                        $newTnum = $oldArray[$tid]['tnum'];
                        if ($oldTeamirder['type'] == 1 || $oldTeamirder['type'] == 2) {
                            if ($oldTeamirder['type'] == 1) {
                                $newTnum = $oldArray[$tid];
                            }
                            $tmpSaveData[$tid]['tnum']    = $num;
                            $tmpSaveData[$tid]['old_num'] = $oldNum;
                            $tmpSaveData[$tid]['add_num'] = 0;
                            if ($num > $newTnum) {
                                $tmpSaveData[$tid]['left_num'] = $num - $newTnum;                                             //本次操作数量
                                $tmpSaveData[$tid]['mome']     = '报团申请增加' . abs($tmpSaveData[$tid]['left_num']) . '张票';    //操作备注
                            } elseif ($num < $newTnum) {
                                $tmpSaveData[$tid]['left_num'] = $newTnum - $num;                                             //本次操作数量
                                $tmpSaveData[$tid]['mome']     = '报团申请减少' . abs($tmpSaveData[$tid]['left_num']) . '张票';    //操作备注
                            } else {
                                $tmpSaveData[$tid]['left_num'] = $num - $newTnum;                                             //本次操作数量
                                $tmpSaveData[$tid]['mome']     = '报团申请产品未修改';    //操作备注
                            }
                        } else {
                            $tmpSaveData[$tid]['tnum']       = $num;
                            $tmpSaveData[$tid]['old_num']    = $oldNum;
                            $tmpSaveData[$tid]['add_num']    = 0;
                            $tmpSaveData[$tid]['reduce_num'] = 0;
                            if ($num > $newTnum) {
                                $tmpSaveData[$tid]['left_num'] = $num - $newTnum;                                             //本次操作数量
                                $tmpSaveData[$tid]['mome']     = '报团申请减少' . abs($tmpSaveData[$tid]['left_num']) . '张票';    //操作备注
                            } elseif ($num < $newTnum) {
                                $tmpSaveData[$tid]['left_num'] = -($newTnum - $num);                                             //本次操作数量
                                $tmpSaveData[$tid]['mome']     = '报团申请增加' . abs($tmpSaveData[$tid]['left_num']) . '张票';    //操作备注
                            } else {
                                $tmpSaveData[$tid]['left_num'] = $num - $newTnum;                                             //本次操作数量
                                $tmpSaveData[$tid]['mome']     = '报团申请产品未修改';    //操作备注
                            }
                        }
                    }
                } else {
                    //已有修改记录  通过与上次修改记录进行对比
                    $newNum  = isset($logArr[$tid]) ? $logArr[$tid]['left_num'] : 0;
                    $newTNum = $oldArray[$tid]['tnum'];
                    if ($oldTeamirder['type'] == 1) {
                        $newTNum = $oldArray[$tid];
                        //下单申请的操作
                        //本次增加的数量 = 上次操作数量 - 本次操作数量
                        $addNum = $newTNum - $num;
                        if ($addNum > 0) {
                            $tmpSaveData[$tid]['tnum']     = $num;
                            $tmpSaveData[$tid]['left_num'] = -$addNum;                                   //本次操作数量
                            $tmpSaveData[$tid]['mome']     = '报团申请减少' . abs($addNum) . '张票';    //操作备注
                        } elseif ($addNum == 0) {
                            $tmpSaveData[$tid]['tnum']     = $num;
                            $tmpSaveData[$tid]['left_num'] = $newNum;                                   //本次操作数量
                            $tmpSaveData[$tid]['mome']     = '报团申请产品未修改';                      //操作备注
                        } else {
                            $tmpSaveData[$tid]['tnum']     = $num;
                            $tmpSaveData[$tid]['left_num'] = abs($addNum);
                            $tmpSaveData[$tid]['mome']     = '报团申请增加' . abs($addNum) . '张票';      //操作备注
                        }
                    } elseif ($oldTeamirder['type'] == 2) {
                        //增加门票的操作
                        if ($newTNum > $num) {
                            $tmpSaveData[$tid]['tnum']     = $num;
                            $tmpSaveData[$tid]['add_num']  = $oldNum - $num;
                            $tmpSaveData[$tid]['left_num'] = -($newTNum - $num);
                            $tmpSaveData[$tid]['old_num']  = $oldNum;
                            $tmpSaveData[$tid]['mome']     = '报团申请减少' . abs($tmpSaveData[$tid]['left_num']) . '张票';  //操作备注
                        } elseif ($newTNum < $num) {
                            $tmpSaveData[$tid]['tnum']     = $num;
                            $tmpSaveData[$tid]['add_num']  = $num - $newTNum;
                            $tmpSaveData[$tid]['old_num']  = $oldNum;
                            $tmpSaveData[$tid]['left_num'] = $num - $newTNum;
                            $tmpSaveData[$tid]['mome']     = '报团申请增加' . abs($tmpSaveData[$tid]['left_num']) . '张票';
                        } else {
                            $tmpSaveData[$tid]['tnum']     = $oldArray[$tid]['tnum'];
                            $tmpSaveData[$tid]['old_num']  = $oldArray[$tid]['old_num'];
                            $tmpSaveData[$tid]['add_num']  = $oldArray[$tid]['add_num'];
                            $tmpSaveData[$tid]['left_num'] = $oldArray[$tid]['left_num'];                                             //本次操作数量
                            $tmpSaveData[$tid]['mome']     = '报团申请产品未修改';                                           //操作备注
                        }
                    } elseif ($oldTeamirder['type'] == 3) {
                        //退票的操作
                        //获取出上一次操作的数量来比对
                        $newTnum = $oldArray[$tid]['tnum'];
                        if ($num < $newTnum) {
                            //最终票数以修改票数为主
                            $tmpSaveData[$tid]['tnum']       = $num;
                            $tmpSaveData[$tid]['old_num']    = $oldNum;
                            $tmpSaveData[$tid]['left_num']   = -($newTnum - $num);
                            $tmpSaveData[$tid]['reduce_num'] = $oldNum - $num;     //本次操作数量
                            $tmpSaveData[$tid]['mome']       = '报团申请增加' . abs($tmpSaveData[$tid]['left_num']) . '张票';    //操作备注
                        } elseif ($num > $newTnum) {
                            //最终票数以修改票数为主
                            $tmpSaveData[$tid]['tnum']       = $num;
                            $tmpSaveData[$tid]['old_num']    = $oldNum;
                            $tmpSaveData[$tid]['left_num']   = $num - $newTnum;
                            $tmpSaveData[$tid]['add_num']    = $oldNum - $num;     //本次操作数量
                            $tmpSaveData[$tid]['reduce_num'] = $oldNum - $num;     //本次操作数量
                            $tmpSaveData[$tid]['mome']       = '报团申请减少' . $tmpSaveData[$tid]['left_num'] . '张票';  //操作备注
                        } else {
                            $tmpSaveData[$tid]['tnum']       = $oldArray[$tid]['tnum'];
                            $tmpSaveData[$tid]['old_num']    = $oldArray[$tid]['old_num'];
                            $tmpSaveData[$tid]['add_num']    = $oldArray[$tid]['add_num'];
                            $tmpSaveData[$tid]['reduce_num'] = $oldArray[$tid]['reduce_num'];
                            $tmpSaveData[$tid]['left_num']   = $oldArray[$tid]['left_num'];                                             //本次操作数量
                            $tmpSaveData[$tid]['mome']       = '报团申请产品未修改';                                           //操作备注
                        }
                    }
                    unset($newNum, $newTNum);
                }
            } else {
                $oldNum = isset($sonOrderTNumArr[$tid]) ? $sonOrderTNumArr[$tid] : 0;
                $addNum = $oldNum - $num;

                if ($oldTeamirder['type'] == 1) {
                    //下单申请的操作
                    //本次增加的数量 = 上次操作数量 - 本次操作数量
                    $tmpSaveData[$tid]['tnum']    = $num;
                    $tmpSaveData[$tid]['add_num'] = $addNum;
                    $tmpSaveData[$tid]['old_num'] = $oldNum;
                    if ($num == $oldNum || $addNum == 0) {
                        continue;
                    } elseif ($addNum > 0) {
                        $tmpSaveData[$tid]['tnum']     = $num;
                        $tmpSaveData[$tid]['left_num'] = -$addNum;                                   //本次操作数量
                        $tmpSaveData[$tid]['mome']     = '报团申请减少' . abs($addNum) . '张票';    //操作备注
                    } else {
                        $tmpSaveData[$tid]['tnum']     = $num;
                        $tmpSaveData[$tid]['left_num'] = abs($addNum);
                        $tmpSaveData[$tid]['add_num']  = abs($addNum);
                        $tmpSaveData[$tid]['mome']     = '报团申请增加' . abs($addNum) . '张票';      //操作备注
                    }
                } elseif ($oldTeamirder['type'] == 2) {
                    //增加门票的操作
                    //本次最终增加数  = 上次操作数量 - 本次操作数量
                    $addNum                       = abs($oldNum - $num);
                    $tmpSaveData[$tid]['tnum']    = $num;
                    $tmpSaveData[$tid]['add_num'] = $addNum;
                    $tmpSaveData[$tid]['old_num'] = $oldNum;
                    if ($num == $oldNum || $addNum == 0) {
                        //和上次一样  不做记录
                        continue;
                    } elseif ($addNum > 0) {
                        $tmpSaveData[$tid]['mome']     = '报团申请增加' . abs($addNum) . '张票';
                        $tmpSaveData[$tid]['left_num'] = $addNum;
                    } elseif ($addNum < 0) {
                        $tmpSaveData[$tid]['mome']     = '报团申请减少' . abs($addNum) . '张票';
                        $tmpSaveData[$tid]['left_num'] = -($addNum);
                    }
                } elseif ($oldTeamirder['type'] == 3) {
                    //退票的操作
                    //本次最终增加数  = -(原始数量 - 本次操作数量)
                    $reduceNum                       = $oldNum - $num;
                    $tmpSaveData[$tid]['tnum']       = $num;
                    $tmpSaveData[$tid]['reduce_num'] = abs($reduceNum);
                    $tmpSaveData[$tid]['old_num']    = $oldNum;
                    if ($reduceNum == $oldNum || $reduceNum == 0) {
                        continue;
                    } elseif ($reduceNum < 0) {
                        $tmpSaveData[$tid]['mome']     = '报团申请减少' . abs($reduceNum) . '张票';
                        $tmpSaveData[$tid]['left_num'] = $reduceNum;
                    } elseif ($reduceNum > 0) {
                        $tmpSaveData[$tid]['mome']     = '报团申请增加' . abs($reduceNum) . '张票';
                        $tmpSaveData[$tid]['left_num'] = -($reduceNum);
                    }
                }
            }
        }

        if (!$tmpSaveData) {
            $this->apiReturn(204, [], '您未做任何修改！');
        }

        $verifyBiz = new TeamOrderVerify();
        $config    = $verifyBiz->getConfig($oldTeamirder['sid'], $oldTeamirder['fid'], 4, $allTidArr);

        $tmpArr    = [];
        $orderData = [];
        foreach ($tmpSaveData as $tid => $item) {
            //处理最终下单的数据
            foreach ($pidArray as $value) {
                if ($tid == $value['tid']) {
                    if ($oldTeamirder['type'] == 1) {
                        $orderData[$value['landid']][$tid] = $item['tnum'];
                    } else {
                        $orderData[$value['landid']][$tid] = $item['add_num'] ? $item['old_num'] + $item['add_num'] : $item['old_num'] - $item['reduce_num'];
                    }
                }
            }
            if (!isset($item['mome']) || !$item['mome']) {
                continue;
            }

            $isRecord = $item['mome'] != '报团申请产品未修改' ? true : false;

            if ($isRecord) {
                $trackSaveData[$tid]['audit_id']      = $oldTeamirder['id'];
                $trackSaveData[$tid]['team_ordernum'] = $oldTeamirder['team_ordernum'];
                $trackSaveData[$tid]['sid']           = $oldTeamirder['sid'];
                $trackSaveData[$tid]['fid']           = $oldTeamirder['fid'];
                $trackSaveData[$tid]['tid']           = $tid;
                $trackSaveData[$tid]['operator_id']   = $this->_sid;
                $trackSaveData[$tid]['mome']          = $item['mome'] ?: '';
                $trackSaveData[$tid]['create_time']   = time();
            }

            //第一次操作时记录
            if (!isset($logArr[$tid])) {
                //下单申请
                if ($oldTeamirder['type'] == 1) {
                    if ($isRecord) {
                        $trackSaveData[$tid]['left_num'] = isset($item['add_num']) ? $item['add_num'] : $item['reduce_num'];
                        $trackSaveData[$tid]['tnum']     = $item['tnum'];
                    }
                } elseif ($oldTeamirder['type'] == 2) {
                    //增加门票
                    $tmpArr[$tid]['old_num'] = $item['old_num'];
                    $tmpArr[$tid]['add_num'] = $item['add_num'];
                    $tmpArr[$tid]['tnum']    = $item['old_num'] + $item['add_num'];
                    if ($isRecord) {
                        $trackSaveData[$tid]['left_num'] = $item['left_num'];
                        $trackSaveData[$tid]['tnum']     = $item['add_num'];
                    }
                } elseif ($oldTeamirder['type'] == 3) {
                    //退票
                    $tmpArr[$tid]['old_num']    = $item['old_num'];
                    $tmpArr[$tid]['reduce_num'] = $item['reduce_num'];
                    $tmpArr[$tid]['tnum']       = $item['old_num'] - $item['reduce_num'];
                    if ($isRecord) {
                        $trackSaveData[$tid]['left_num'] = $item['left_num'];
                        $trackSaveData[$tid]['tnum']     = $item['old_num'] - abs($item['reduce_num']);
                    }
                }
            } else {
                //已有修改记录  通过与上次修改记录进行对比
                if ($oldTeamirder['type'] == 1) {
                    //下单
                    if ($isRecord) {
                        $trackSaveData[$tid]['left_num'] = $item['left_num'];
                        $trackSaveData[$tid]['tnum']     = $item['tnum'];
                    }
                } elseif ($oldTeamirder['type'] == 2) {
                    //增加门票
                    $newTNum                 = isset($logArr[$tid]) ? $logArr[$tid]['tnum'] : 0;
                    $tmpArr[$tid]['old_num'] = $item['old_num'];
                    $tmpArr[$tid]['add_num'] = $item['add_num'];
                    $tmpArr[$tid]['tnum']    = $item['tnum'];

                    if ($isRecord) {
                        $trackSaveData[$tid]['left_num'] = $item['left_num'];
                        $trackSaveData[$tid]['tnum']     = $item['left_num'] + $newTNum;
                        if ($item['tnum'] == 0) {
                            $tmpArr[$tid]['add_num']         = $item['add_num'];
                            $tmpArr[$tid]['tnum']            = $item['tnum'];
                            $trackSaveData[$tid]['left_num'] = $item['left_num'];
                            $trackSaveData[$tid]['tnum']     = $item['left_num'] + $newTNum;
                        }
                    }
                } elseif ($oldTeamirder['type'] == 3) {
                    //退票
                    $newTNum                    = isset($logArr[$tid]) ? $logArr[$tid]['tnum'] : 0;
                    $tmpArr[$tid]['old_num']    = $item['old_num'];
                    $tmpArr[$tid]['reduce_num'] = $item['reduce_num'];
                    $tmpArr[$tid]['tnum']       = $item['old_num'] - $item['reduce_num'];

                    if ($isRecord) {
                        $trackSaveData[$tid]['left_num'] = $item['left_num'];
                        $trackSaveData[$tid]['tnum']     = $item['left_num'] + $newTNum;
                        if ($item['tnum'] == 0) {
                            $tmpArr[$tid]['tnum']            = 0;
                            $trackSaveData[$tid]['left_num'] = $item['left_num'];
                            $trackSaveData[$tid]['tnum']     = $item['left_num'] + $newTNum;
                        }
                    }
                }
            }
        }

        $groupProducts = json_encode($ticketInfo);
        $orderData     = json_encode($orderData);
        $idCardInfo    = json_encode($idCardArr);

        //假如类型为增加门票则特殊处理
        if ($oldTeamirder['type'] == 2 || $oldTeamirder['type'] == 3) {
            $groupProducts = json_encode($tmpArr);
        }
        if ($oldTeamirder['type'] == 1){ //更新下ext_tent的值和$orderIdCard
            $extContent['voucherType'] = $voucherType;
            $extContent['moreCredentials'] = $moreCredentials;
            $extContent['orderIdCardTickets'] = $orderIdCardTickets;
            $extContent['moreTouristCredentials'] = $moreTouristCredentials;
        }
        $extContent['timeShareInfo'] = json_encode(json_decode(html_entity_decode($timeShareInfo), true));
        $evoluteApi                  = new EvoluteGroupSpecialityService();
        $evoluteInfoData             = json_decode($oldTeamirder['evolute_info'], true);
        foreach ($pidArray as $value) {
            //调用java接口获取对于的分销层级关系
            $evoluteInfo = $evoluteApi->queryEvoluteLvl($oldTeamirder['order_mid'], $oldTeamirder['fid'], $value['pid']);
            if ($evoluteInfo['code'] != 200 || empty($evoluteInfo['data'])) {
                $this->apiReturn(204, '分销信息有误');
            }
            if (isset($evoluteInfoData[$value['id']])) {
                continue;
            }
            $evoluteInfoData[$value['id']] = $evoluteInfo['data'];
        }
        $extContent      = json_encode($extContent, JSON_UNESCAPED_UNICODE);
        $evoluteInfoData = json_encode($evoluteInfoData, JSON_UNESCAPED_UNICODE);
        $result          = $this->_verifyModel->changeTeamorderProduct($id, $groupProducts, $orderData,
            array_values($trackSaveData), $idCardInfo, $oldTeamirder['type'], $evoluteInfoData, $extContent,
            $orderIdCard);
        if ($result === false) {
            $this->apiReturn(204, [], '修改失败');
        }

        $memberBiz = new MemberBiz();
        if (4 & $config['sell_notice']) {
            $fid = empty($oldTeamirder['order_mid']) ? $oldTeamirder['fid'] : $oldTeamirder['order_mid'];
            //供应商修改报团申请通知分销商
            $memberMobile = $memberBiz->getInfo($fid);
            $verifyBiz->sendSMS($oldTeamirder['sid'], '尊敬的客户，供应商修改了您的报团申请单' . $id . '，请及时确认。', $memberMobile['mobile'], '供应商修改报团申请单');
        }
        if (2 & $config['buyer_notice']) {
            //分销商申请修改团单通知供应商
            $memberMobile = $memberBiz->getInfo($oldTeamirder['sid']);
            $verifyBiz->sendSMS($oldTeamirder['sid'], '尊敬的客户，分销商修改了报团申请单' . $id . '，请及时确认。', $memberMobile['mobile'],'分销商修改报团申请单');
        }

        $this->apiReturn(200, [], '修改成功');
    }

    /**
     * 审核申请
     * <AUTHOR> Li
     * @date   2019-03-19
     *
     * @return array
     */
    public function teamOrderAudit()
    {
        $id     = I('post.id');
        $type   = I('post.type', 2, 'intval');   //审核状态 2拒绝 1通过 3取消
        $remark = I('post.remark', '', 'strval');   //审核备注

        if (!$id) {
            $this->apiReturn(204, [], '缺少必要参数');
        }
        $teamOrderVerifyBz = new TeamOrderVerify();
        $result = $teamOrderVerifyBz->teamOrderAudit($this->_sid, $id, $type, $remark);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 团单修改审核判断
     * <AUTHOR> Li
     * @date   2019-03-23
     *
     * @return array
     */
    public function modifyOrderReview()
    {
        $aid        = I('post.aid');
        $fid        = I('post.fid');
        $ticketInfo = I('post.ticket_info');
        $teamOrder  = I('post.team_order');
        $type       = I('post.type', 0, 'intval'); // 0修改 1取消订单

        if (!$aid || !$fid || !$ticketInfo || !$teamOrder) {
            $this->apiReturn(204, [], '参数错误');
        }

        $verifyBis = new TeamOrderVerify();
        $checkRes  = $verifyBis->modifyOrderReview($aid, $fid, $ticketInfo, $teamOrder, $type, $this->_sid);

        if ($checkRes['code'] == 201) {
            $this->apiReturn(201, [], '已生成报团申请单，报团申请单号：' . $checkRes['data']['audit_id'] . '；可在分销商报团申请管理查询，供应商审核后生效。');
        }

        if ($checkRes['code'] != 200) {
            $this->apiReturn($checkRes['code'], $checkRes['data'], $checkRes['msg']);
        }

        $this->apiReturn(200, [], '无需审核');
    }

    /**
     * 重发短信
     * <AUTHOR> Li
     * @date   2019-03-23
     *
     * @return array
     */
    public function smsReSend()
    {
        $aid       = I('post.aid');
        $fid       = I('post.fid');
        $id        = I('post.id');
        $status    = I('post.status');
        $teamOrder = I('post.team_order');

        $memberBiz    = new MemberBiz();
        $verifyBis    = new TeamOrderVerify();


        //获取审核申请
        $orderArray = $this->_verifyModel->getTeamorderStashById($id);

        //获取审核相关的门票
        $tidArr  = [];
        foreach (json_decode($orderArray['order_data'], true) as $lid => $ticketInfo) {
            $tidArr[] = array_keys($ticketInfo)[0];
        }
        $config       = $verifyBis->getConfig($aid, $fid, 4, $tidArr);

        $memberMobile = $memberBiz->getInfo($fid);

        if ($status == 1 && 1 & $config['sell_notice']) {
            //供应商修改报团申请通知分销商
            $result = $verifyBis->sendSMS($aid,
                '尊敬的客户，您的报团申请单' . $id . '审核通过，团单下单成功。订单号：' . $teamOrder . '，详情及二维码12301.cc/', $memberMobile['mobile'],'审核通过团单下单成功');
        } elseif ($status == 2 && 2 & $config['sell_notice']) {
            //供应商修改报团申请通知分销商
            $result = $verifyBis->sendSMS($aid, '尊敬的客户，您的报团申请单' . $id . '审核拒绝，团单下单失败。', $memberMobile['mobile'],'审核拒绝团单下单失败');
        } elseif (!(1 & $config['sell_notice']) || !(2 & $config['sell_notice'])) {
            $this->apiReturn(203, [], '您没配置发送短信，请查看审核配置');
        }

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], '发送成功');
        }

        $this->apiReturn(204, [], '发送失败');

    }


    /**
     * 获取审核配置详情
     * <AUTHOR> Li
     * @date   2019-03-16
     *
     * @param  array  $detail  配置内容
     *
     * @return array
     */
    private function _moreData($detail)
    {
        $fids     = json_decode($detail['fids'], true);
        $tids     = json_decode($detail['tids'], true);
        $groupIds = json_decode($detail['group_ids'], true);

        //分组名称
        $groups = [];
        if ($groupIds) {
            if (!in_array('all', $groupIds)) {
                $groupBiz        = new \Business\Product\Update\EvoluteGroup();
                $originGroupInfo = $groupBiz->queryByGroupIds($groupIds);
                if ($originGroupInfo['code'] == 200 && $originGroupInfo['data']) {
                    $groups = array_column($originGroupInfo['data'], 'groupName', 'id');
                }
            }
        }

        $fidArr = [];
        if ($fids) {
            if (!in_array('all', $fids)) {
                $fidArr = $fids;
            }
        }

        //分销商名称
        $fids = [];
        if ($fidArr) {
            $memModel = new Member('slave');
            $nameMap  = $memModel->getMemberInfoByMulti($fidArr, 'id', 'id,dname', 1);
            foreach ($nameMap as $item) {
                $fids[$item['id']] = $item['dname'];
            }
        }

        if ($tids) {
            if (!in_array('all', $tids)) {
                $tidArr = [];
                foreach ($tids as $tid) {
                    $tidArr[] = (int)$tid;
                }
                $javaApi   = new \Business\CommodityCenter\Ticket();
                $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'title,id,pid', 'id', 'title,imgpath,id');

                $pidArray = [];
                foreach ($ticketArr as $ticketInfo) {
                    $pidArray[$ticketInfo['ticket']['id']] = [
                        'id'      => $ticketInfo['ticket']['id'],
                        'ttitle'  => $ticketInfo['ticket']['title'],
                        'tid'     => $ticketInfo['ticket']['id'],
                        'pid'     => $ticketInfo['ticket']['pid'],
                        'title'   => $ticketInfo['land']['title'],
                        'imgpath' => $ticketInfo['land']['imgpath'],
                        'landid'  => $ticketInfo['land']['id'],
                    ];
                }
            }
        }

        $detail['group_ids'] = $groups;
        $detail['fids']      = $fids;
        $detail['tids']      = $pidArray;

        return $detail;
    }

    /**
     * 审核配置检测及供应商通知配置检测
     * <AUTHOR> Li
     * @date   2019-03-14
     *
     * $params int $result  权限值
     */
    private function _checkAuditConf($result = 0, $type = 1)
    {
        if ($type) {
            $orderRes     = 1 & $result ? 1 : 0;
            $addTicketRes = 2 & $result ? 1 : 0;
            $refundRes    = 4 & $result ? 1 : 0;
            $cancelRes    = 8 & $result ? 1 : 0;
        } else {
            $orderRes     = 1 & $result;
            $addTicketRes = 2 & $result;
            $refundRes    = 4 & $result;
            $cancelRes    = 8 & $result;
        }

        return ['order' => $orderRes, 'addTicket' => $addTicketRes, 'refundTicket' => $refundRes, 'cancelTicket' => $cancelRes];
    }

    /**
     * 分销商通知配置检测
     * <AUTHOR> Li
     * @date   2019-03-14
     *
     * $params int $result  权限值
     */
    private function _checkSellConf($result = 0, $type = 1)
    {
        if ($type) {
            $passRes   = 1 & $result ? 1 : 0;
            $refuseRes = 2 & $result ? 1 : 0;
            $changeRes = 4 & $result ? 1 : 0;
        } else {
            $passRes   = 1 & $result;
            $refuseRes = 2 & $result;
            $changeRes = 4 & $result;
        }

        return ['pass' => $passRes, 'refuse' => $refuseRes, 'change' => $changeRes];
    }

    /**
     * 获取供应商团单可售列表
     *
     * @author: linchen
     * @date: 2019/3/14
     * get.keyword:          关键字
     * get.page:           当前页
     * get.size:            个数
     */
    public function getProduct()
    {
        //搜索关键字
        $keyword = I('get.keyword', '');
        //当前页码
        $page = I('get.page', 1, 'intval');
        //每页条数
        $size = I('get.size', 20, 'intval');
        //团单号
        $teamOrder = I('get.teamorder', '', 'strval');
        //报团审核的操作类型
        $type     = I('get.type', 2); // 1下单 2增加门票 3退票
        $orderMid = I('get.order_mid', 0, 'intval'); // 下单人id

        //报团审核修改产品调用
        $playTime = I('get.play_time');
        $sid      = I('get.sid');  //顶级供应商id
        $fid      = I('get.fid');  //上级供应商id
        //报团类型id
        $teamTypeId = I('team_type_id', 0, 'intval');

        $date = date('Y-m-d', time());
        $mid  = $this->_sid;

        if ($playTime) {
            $date = $playTime;
        }

        if (!$sid) {
            $sid = $this->_sid;
        }

        if ($sid && $orderMid) {
            $mid = $orderMid;
        }

        if ($teamTypeId) {
            $teamRuleBz = new \Business\TeamOrder\TeamRules();
            $result     = $teamRuleBz->getProductByTeamTypeId($sid, $mid, $teamTypeId, $date, $keyword, $page, $size);
        } else {
            $bookingBiz = new \Business\TeamOrder\Booking();
//            $result     = $bookingBiz->productSearchNoLimitTicketNum($mid, $sid, $date, $keyword, $page, $size);
            $result     = $bookingBiz->productSearchNoLimitTicketNumNew($mid, $sid, $date, $keyword, $page, $size, '', 0);
        }
        if (isset($result['code'])) {
            $tidArray = [];
            $lidArray = [];

            //通过团单号获取出订单中对应的门票id
            if ($teamOrder) {
                //通过团单号获取订单信息 存入申请表中
                $teamOrderModel = new \Model\Order\TeamOrderSearch();
                $sonOrderData   = $teamOrderModel->getSonOrderInfoByMainOrderNum($teamOrder, false, false,
                    'son_ordernum, lid, tid');
                $lidArray       = array_column($sonOrderData, 'lid');
                $tidArray       = array_column($sonOrderData, 'tid');
            }

            // 处理数据返回给前端
            $allData      = [];
            $ticketModel  = new Ticket();
            $timeShareBiz = new \Business\Order\TimeShareOrder();
            //对返回结果处理
            if (($type == 2 || $type == 3) && $lidArray && $tidArray) {
                $packTid      = [];
                foreach ($result['data'] as $key => $item) {
                    if (!in_array($item['product_id'], $lidArray)) {
                        continue;
                    }
                    $tmpTidArr    = array_column($item['ticket_list'], 'ticket_id');
                    $timeShareRes = $timeShareBiz->getTimeSlicesWithTidArr($tmpTidArr, $date, 9, true);
                    $ticketsLimit = $ticketModel->getTicketInfoMulti($tmpTidArr, 'id,refund_rule,batch_check,num_modify,pre_sale');
                    switch ($item['p_type']) {
                        case 'F':
                            $packTid = array_merge($packTid, array_column($item['ticket_list'],'ticket_id'));
                            break;
                    }
                    // 返回团队订单预定的规则属性
                    if (isset($item['group_limit']) && !empty($item['group_limit'])) {
                        $groupLimitArr = json_decode($item['group_limit'], true);
                    }
                    $groupNumLimit['is_limit']     = 0;
                    $groupNumLimit['limit_number'] = 0;
                    if (isset($item['group_number_limit']) && $item['group_number_limit'] == 1) {
                        $groupNumLimit['is_limit']     = 1;
                        $groupNumLimit['limit_number'] = isset($groupLimitArr['group_number_limit']['limit_min_num']) ? $groupLimitArr['group_number_limit']['limit_min_num'] : 0;
                    }
                    $groupTicketLimit['is_limit'] = 0;
                    $groupTicketLimit['tickets']  = [];
                    if (isset($item['group_ticket_limit']) && $item['group_ticket_limit'] == 1) {
                        $groupTicketLimit['is_limit'] = 1;
                        $groupTicketLimit['tickets']  = isset($groupLimitArr['group_ticket_limit']) ? $groupLimitArr['group_ticket_limit'] : [];
                    }

                    $item['group_ticket_limit'] = $groupTicketLimit;
                    $item['group_number_limit'] = $groupNumLimit;
                    $data                       = $item;
                    if (count($item['ticket_list']) < 1) {
                        continue;
                    }

                    $tData = [];
                    foreach ($item['ticket_list'] as $k => $v) {
                        if (!in_array($v['ticket_id'], $tidArray)) {
                            continue;
                        }
                        $v['pre_sale']        = $ticketsLimit[$v['ticket_id']]['pre_sale'];
                        $v['time_share_info'] = $timeShareRes['data'][$v['ticket_id']] ?? [];
                        if (!empty($ticketsLimit) && ($ticketsLimit[$v['ticket_id']]['refund_rule'] == '-1' || $ticketsLimit[$v['ticket_id']]['refund_rule'] == '2')) {
                            $v['refund_rule'] = false;
                        } else {
                            $v['refund_rule'] = true;
                        }
                        //判断门票属性是否是整批
                        if (!empty($ticketsLimit) && !in_array($ticketsLimit[$v['ticket_id']]['batch_check'], [2, 3])) {
                            $v['is_batch'] = true;
                        } else {
                            $v['is_batch'] = false;
                        }
                        $tData[$k] = $v;
                    }
                    if (count($tData) < 1) {
                        continue;
                    }
                    $data['ticket_list'] = array_values($tData);
                    $allData[]           = $data;
                }
                $packSonData = [];
                if ($packTid) {
                    $bookingBiz = new \Business\TeamOrder\Booking();
                    $packSonData = $bookingBiz->handlePackProductSonTicket($packTid);
                }
                foreach ($allData as $key => $value) {
                    $ticketList = $value['ticket_list'];
                    foreach ($ticketList as $k => $v) {
                        $tmpPackSonData  = $packSonData[$v['ticket_id']] ?? [];
                        $sonTidArr       = array_column($tmpPackSonData, 'tid');
                        $sonTimeShareRes = $timeShareBiz->getTimeSlicesWithTidArr($sonTidArr, $date, 9, true);
                        foreach ($tmpPackSonData as &$packageTicketItem) {
                            $packageTicketItem['time_share_info'] = $sonTimeShareRes['data'][$packageTicketItem['tid']] ?? [];
                        }
                        unset($packageTicketItem);
                        $allData[$key]['ticket_list'][$k]['son_ticket'] = $tmpPackSonData;
                    }
                }
            } else {
                $packTid = [];
                foreach ($result['data'] as $key => $item) {
                    switch ($item['p_type']){
                        case 'F':
                            $packTid = array_merge($packTid,array_column($item['ticket_list'],'ticket_id'));
                            break;
                    }
                    $tidArray     = array_column($item['ticket_list'], 'ticket_id');
                    $ticketsLimit = $ticketModel->getTicketInfoMulti($tidArray, 'id,refund_rule,batch_check,num_modify,more_credentials,pre_sale');
                    $timeShareRes = $timeShareBiz->getTimeSlicesWithTidArr($tidArray, $date, 9, true);
                    // 返回团队订单预定的规则属性
                    if (isset($item['group_limit']) && !empty($item['group_limit'])) {
                        $groupLimitArr = json_decode($item['group_limit'], true);
                    }
                    $groupNumLimit['is_limit']     = 0;
                    $groupNumLimit['limit_number'] = 0;
                    if (isset($item['group_number_limit']) && $item['group_number_limit'] == 1) {
                        $groupNumLimit['is_limit']     = 1;
                        $groupNumLimit['limit_number'] = isset($groupLimitArr['group_number_limit']['limit_min_num']) ? $groupLimitArr['group_number_limit']['limit_min_num'] : 0;
                    }
                    $groupTicketLimit['is_limit'] = 0;
                    $groupTicketLimit['tickets']  = [];
                    if (isset($item['group_ticket_limit']) && $item['group_ticket_limit'] == 1) {
                        $groupTicketLimit['is_limit'] = 1;
                        $groupTicketLimit['tickets']  = isset($groupLimitArr['group_ticket_limit']) ? $groupLimitArr['group_ticket_limit'] : [];
                    }

                    $item['group_ticket_limit'] = $groupTicketLimit;
                    $item['group_number_limit'] = $groupNumLimit;
                    $data                       = $item;
                    if (count($item['ticket_list']) < 1) {
                        continue;
                    }

                    $tData = [];
                    foreach ($item['ticket_list'] as $k => &$v) {
                        //判断门票属性是否是整批
                        if (!empty($ticketsLimit) && !in_array($ticketsLimit[$v['ticket_id']]['batch_check'], [2, 3])) {
                            $v['is_batch'] = true;
                        } else {
                            $v['is_batch'] = false;
                        }
                        $v['pre_sale']        = $ticketsLimit[$v['ticket_id']]['pre_sale'];
                        $v['time_share_info'] = $timeShareRes['data'][$v['ticket_id']] ?? [];
                        //门票退票规则
                        if (!empty($ticketsLimit) && ($ticketsLimit[$v['ticket_id']]['refund_rule'] == '-1' || $ticketsLimit[$v['ticket_id']]['refund_rule'] == '2')) {
                            $v['refund_rule'] = false;
                        } else {
                            $v['refund_rule'] = true;
                        }
                        $moreCredentials     = isset($ticketsLimit[$v['ticket_id']]['more_credentials']) && $ticketsLimit[$v['ticket_id']]['more_credentials'] ? json_decode($ticketsLimit[$v['ticket_id']]['more_credentials'],
                            true)['content'] : [];
                        $v['more_credentials'] = $moreCredentials ? 1 : 0;
                        $v['more_credential_content'] = $moreCredentials;
                        $tData[$k] = $v;
                    }
                    if (count($tData) < 1) {
                        continue;
                    }
                    $data['ticket_list'] = array_values($tData);
                    $allData[]           = $data;
                }
                $packSonData = [];
                if ($packTid){
                    $bookingBiz = new \Business\TeamOrder\Booking();
                    $packSonData = $bookingBiz->handlePackProductSonTicket($packTid);
                }
                foreach ($allData as $key => $value){
                    $ticketList = $value['ticket_list'];
                    foreach ($ticketList as $k => $v) {
                        $tmpPackSonData  = $packSonData[$v['ticket_id']] ?? [];
                        $sonTidArr       = array_column($tmpPackSonData, 'tid');
                        $sonTimeShareRes = $timeShareBiz->getTimeSlicesWithTidArr($sonTidArr, $date, 9, true);
                        foreach ($tmpPackSonData as &$packageTicketItem) {
                            $packageTicketItem['time_share_info'] = $sonTimeShareRes['data'][$packageTicketItem['tid']] ?? [];
                        }
                        unset($packageTicketItem);
                        $allData[$key]['ticket_list'][$k]['son_ticket'] = $tmpPackSonData;
                    }
                }
            }

            $this->apiReturn($result['code'], $allData, $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 根据订单号批量获取未验证游客身份信息
     *
     * @param  string  $orderNum  订单号
     *
     * @return array|bool
     */
    public function getOrderTouristInfoByOrderNumBatch()
    {
        $orderNumArr = I('post.ordernum_arr', '', 'string');

        if (empty($orderNumArr)) {
            $this->apiReturn(203, [], '订单号缺失');
        }

        $orderNumArr = explode(',', $orderNumArr);
        if (!is_array($orderNumArr)) {
            $this->apiReturn(203, [], '参数有误');
        }

        //$orderToolModel = new OrderTools();
        //$result         = $orderToolModel->getOrderTouristInfo($orderNumArr);

        $queryParams = [$orderNumArr];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo',
            'queryOrderTouristInfoByOrderId', $queryParams);
        $result        = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $result = $queryRes['data'];
        }

        if (empty($result)) {
            return false;
        }

        $return = [];
        foreach ($result as $key => $value) {
            if ($value['check_state'] == OrderConst::ORDER_TOURIST_NO_VERIFIED) {
                $return[$value['orderid']][] = $value;
            }
        }

        $this->apiReturn(200, $return, '获取成功');
    }

    /**
     * 检测新旧配置是否存在交集
     * <AUTHOR> Li
     * @date  2020-09-03
     *
     * @param  array  $oldConf  旧配置
     * @param  array  $newConf  新配置
     *
     * @return bool
     */
    public function _checkAuditConfig($oldConf, $newConf)
    {
        if (array_intersect($oldConf, $newConf)) {
            return true;
        }

        return false;
    }
}