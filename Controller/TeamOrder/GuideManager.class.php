<?php
/**
 * 导游管理
 * User: lanwanhui
 * Date: 2021/6/7
 */
namespace Controller\TeamOrder;

use Library\Controller;
use Business\TeamOrder\GuideManager as GuideManagerBusiness;

class GuideManager extends Controller
{
    private $_loginInfo;

    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo();
    }


    /**
     * 添加导游
     * User: lanwanhui
     * Date: 2021/6/8
     */
    public function addGuide()
    {

        $type   = I('post.type',0,'intval');   //type =1 供应商添加,type=2分销商添加

        if ($type == 1) {
            $this->supplierAddGuide();
        } elseif ($type == 2){
            $this->distributorAddGuide();
        } else {
            $this->apiReturn(2004, [], '添加类型错误');
        }

    }


    /**
     * 供应商添加导游
     * User: lanwanhui
     * Date: 2021/8/24
     */
    public function supplierAddGuide()
    {
        if ($this->_loginInfo['sdtype'] != 0) {
            $this->apiReturn(204, [], '非供应商无法使用该功能');
        }

        $guideName         = I('post.guide_name','','strval,trim');     //导游姓名
        $guideMobile       = I('post.guide_mobile','','strval,trim');   //导游手机号
        $guideIdCard       = I('post.guide_id_card','','strval,trim');  //导游身份证号
        $guideNumber       = I('post.guide_number','','strval,trim');   //导游证号
        $guideType         = I('post.guide_type',0,'intval');           //导游类型 0表示空，1全陪，2地接，3景区讲解
        $remarks           = I('post.remarks','','strval,trim');        //备注
        $fids              = I('post.fids','[]','strval');              //分销商id json格式
        $shareType         = I('post.share_type',1,'strval,trim');      //共享类型:1指定分销商,2全部一级分销商,3全部一二级分销商

        $guideBusiness = new GuideManagerBusiness();

        $rs = $guideBusiness->supplierAddGuide(
            $this->_loginInfo['sid'],
            $this->_loginInfo['memberID'],
            $fids,
            $guideName,
            $guideMobile,
            $guideIdCard,
            $guideNumber,
            $guideType,
            $shareType,
            $remarks
        );

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }

    /**
     * 分销商添加导游
     * User: lanwanhui
     * Date: 2021/8/24
     */
    public function distributorAddGuide()
    {

        $guideName         = I('post.guide_name','','strval,trim');     //导游姓名
        $guideMobile       = I('post.guide_mobile','','strval,trim');   //导游手机号
        $guideIdCard       = I('post.guide_id_card','','strval,trim');  //导游身份证号
        $guideNumber       = I('post.guide_number','','strval,trim');   //导游证号
        $guideType         = I('post.guide_type',0,'intval');           //导游类型 0表示空，1全陪，2地接，3景区讲解
        $remarks           = I('post.remarks','','strval,trim');        //备注
        $sid               = I('post.sid',0,'intval');                  //供应商

        $guideBusiness = new GuideManagerBusiness();

        $rs = $guideBusiness->distributorAddGuide(
            $this->_loginInfo['sid'],
            $this->_loginInfo['memberID'],
            $sid,
            $guideName,
            $guideMobile,
            $guideIdCard,
            $guideNumber,
            $guideType,
            $remarks
        );

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }


    /**
     * 获取审核记录列表
     * User: lanwanhui
     * Date: 2021/6/9
     */
    public function getAuditList()
    {
        $beginTime         = I('post.begin_time','','strval,trim');     //开始时间
        $endtime           = I('post.end_time','','strval,trim');        //结束时间
        $verifyState       = I('post.verify_state',-1,'intval');         //审核状态 0未审核，1审核被拒绝，2取消审核，3审核通过
        $guideName         = I('post.guide_name','','strval,trim');     //导游姓名
        $guideMobile       = I('post.guide_mobile','','strval,trim');   //导游手机号
        $guideIdCard       = I('post.guide_id_card','','strval,trim');  //导游身份证号
        $guideNumber       = I('post.guide_number','','strval,trim');   //导游证号
        $page              = I('post.page', 1, 'intval');               //页数
        $pageSize          = I('post.page_size', 10, 'intval');        //每页数量
        $fid               = I('post.fid',0,'intval');                //分销商id
        $sid               = I('post.sid',0,'intval');                //供应商

        $guideBusiness = new GuideManagerBusiness();

        $rs = $guideBusiness->getAuditList($sid, $beginTime, $endtime, $fid, $verifyState, $guideName, $guideMobile, $guideIdCard, $guideNumber, $page, $pageSize);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }

    /**
     * 审核取消
     * User: lanwanhui
     * Date: 2021/6/9
     */
    public function auditCancel(){

        $auditId       = I('post.audit_id',0,'intval');  //审核id

        $guideBusiness = new GuideManagerBusiness();

        $rs = $guideBusiness->auditCancel($this->_loginInfo['sid'], $this->_loginInfo['memberID'], $auditId);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }

    /**
     * 审核 同意,拒绝
     * User: lanwanhui
     * Date: 2021/6/9
     */
    public function guideAudit(){

        $auditId       = I('post.audit_id',0,'intval');          //审核id
        $verifyState   = I('post.verify_state',0,'intval');      //审核状态 0未审核，1审核被拒绝，2取消审核，3审核通过

        if ($this->_loginInfo['sdtype'] != 0) {
            $this->apiReturn(204, [], '无操作权限');
        }

        $guideBusiness = new GuideManagerBusiness();
        $rs = $guideBusiness->guideAudit($this->_loginInfo['sid'], $this->_loginInfo['memberID'], $auditId, $verifyState);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);
    }


    /**
     * 获取导游详细信息
     * User: lanwanhui
     * Date: 2021/8/24
     */
    public function getGuideInfoDetail()
    {
        $guideId       = I('get.guide_id',0,'intval'); //导游id

        $guideBusiness = new GuideManagerBusiness();

        $rs = $guideBusiness->getGuideInfoDetail($guideId, $this->_loginInfo['sid']);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);
    }


    /**
     * 分页获取导游下的分销商
     * 获取导游详细信息
     * User: lanwanhui
     * Date: 2021/9/18
     */
    public function getDistributorPaging()
    {
        $guideId       = I('post.guide_id',0,'intval'); //导游id
        $page          = I('post.page', 1, 'intval');               //页数
        $pageSize      = I('post.page_size', 10, 'intval');        //每页数量

        $guideBusiness = new GuideManagerBusiness();

        $rs = $guideBusiness->getDistributorPaging($guideId, $this->_loginInfo['sid'], $page, $pageSize);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);
    }


    /**
     * 修改导游信息
     * User: lanwanhui
     * Date: 2021/6/8
     */
    public function editGuideInfo()
    {
        $guideId           = I('post.guide_id',0,'intval');            //导游表id
        $guideName         = I('post.guide_name','','strval,trim');     //导游姓名
        $guideMobile       = I('post.guide_mobile','','strval,trim');   //导游手机号
        $guideIdCard       = I('post.guide_id_card','','strval,trim');  //导游身份证号
        $guideNumber       = I('post.guide_number','','strval,trim');   //导游证号
        $guideType         = I('post.guide_type',0,'intval');           //导游类型 0表示空，1全陪，2地接，3景区讲解
        $remarks           = I('post.remarks','','strval,trim');        //备注
        $fids              = I('post.fids','[]','strval');              //分销商id json格式
        $shareType         = I('post.share_type',1,'strval,trim');      //共享类型:1指定分销商,2全部一级分销商,3全部一二级分销商

        $guideBusiness = new GuideManagerBusiness();

        if ($this->_loginInfo['sdtype'] != 0) {
            $this->apiReturn(204, [], '无操作权限');
        }

        $rs = $guideBusiness->editGuideInfo($this->_loginInfo['sid'], $this->_loginInfo['memberID'],
            $guideId, $fids, $guideName, $guideMobile, $guideIdCard, $guideNumber, $guideType, $shareType, $remarks);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }


    /**
     * 启用，禁用，删除
     * User: lanwanhui
     * Date: 2021/6/9
     */
    public function editGuideState()
    {
        $guideId       = I('post.guide_id',0,'intval');   //导游表id
        $state         = I('post.state',0,'intval');     //导游状态

        if ($this->_loginInfo['sdtype'] != 0) {
            $this->apiReturn(204, [], '无操作权限');
        }

        $guideBusiness = new GuideManagerBusiness();

        $rs = $guideBusiness->editGuideState($this->_loginInfo['sid'], $guideId, $state);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }


    /**
     * 分销商版获取导游列表
     * User: lanwanhui
     * Date: 2021/6/9
     */
    public function distributorGetGuideList()
    {
        $beginTime         = I('post.begin_time','','strval,trim');     //开始时间
        $endtime           = I('post.end_time','','strval,trim');       //结束时间
        $state             = I('post.state',0,'intval');                //状态 3启用，4禁用
        $guideName         = I('post.guide_name','','strval,trim');     //导游姓名
        $guideMobile       = I('post.guide_mobile','','strval,trim');   //导游手机号
        $guideIdCard       = I('post.guide_id_card','','strval,trim');  //导游身份证号
        $guideNumber       = I('post.guide_number','','strval,trim');   //导游证号
        $page              = I('post.page', 1, 'intval');               //页数
        $pageSize          = I('post.page_size', 10, 'intval');        //每页数量
        $sid               = I('post.sid',0,'intval');                 //供应商

        $guideBusiness = new GuideManagerBusiness();

        $rs = $guideBusiness->distributorGetGuideList($this->_loginInfo['sid'], $beginTime, $endtime,
            $sid, $state, $guideName, $guideMobile, $guideIdCard, $guideNumber, $page, $pageSize
        );

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }


    /**
     * 供应商版获取导游列表
     * User: lanwanhui
     * Date: 2021/8/24
     */
    public function supplierGetGuideList()
    {
        $beginTime         = I('post.begin_time','','strval,trim');     //开始时间
        $endtime           = I('post.end_time','','strval,trim');       //结束时间
        $state             = I('post.state',0,'intval');                //状态 3启用，4禁用
        $guideName         = I('post.guide_name','','strval,trim');     //导游姓名
        $guideMobile       = I('post.guide_mobile','','strval,trim');   //导游手机号
        $guideIdCard       = I('post.guide_id_card','','strval,trim');  //导游身份证号
        $guideNumber       = I('post.guide_number','','strval,trim');   //导游证号
        $page              = I('post.page', 1, 'intval');               //页数
        $pageSize          = I('post.page_size', 10, 'intval');        //每页数量
        $fid               = I('post.fid',0,'intval');                //分销商id

        $guideBusiness = new GuideManagerBusiness();

        $rs = $guideBusiness->supplierGetGuideList($this->_loginInfo['sid'], $beginTime, $endtime,
            $fid, $state, $guideName, $guideMobile, $guideIdCard, $guideNumber, $page, $pageSize
        );

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }


    /**
     * 获取分销商
     * User: lanwanhui
     * Date: 2021/6/9
     */
    public function getDistributors()
    {
        $keyword         = I('post.keyword','','strval,trim'); //关键字
        if (empty($keyword)) {
            $this->apiReturn(204, [], '请输入搜索条件');
        }

        $guideBusiness = new GuideManagerBusiness();

        $rs = $guideBusiness->getDistributors($this->_loginInfo['sid'], $keyword);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }


    /**
     * 获取供应商
     * User: lanwanhui
     * Date: 2021/6/9
     */
    public function getSuperior()
    {

        $keyword    = I('post.keyword','','strval,trim'); //关键字
        if (empty($keyword)) {
            $this->apiReturn(204, [], '请输入搜索条件');
        }

        $guideBusiness = new GuideManagerBusiness();

        $rs = $guideBusiness->getSuperior($this->_loginInfo['sid'], $keyword);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);
    }


    /**
     * 搜索导游(抱团预定,计调下单调用)
     * User: lanwanhui
     * Date: 2021/6/9
     */
    public function getGuideInfo()
    {
        $fid      = I('post.fid',0,'intval');               //分销商id
        $sid      = I('post.sid',0,'intval');               //供应商
        $keyword  = I('post.keyword','','strval,trim'); //关键字
        if (empty($keyword)) {
            $this->apiReturn(204, [], '请输入搜索条件');
        }

        $guideBusiness = new GuideManagerBusiness();

        $rs = $guideBusiness->getGuideInfo($sid, $fid, $keyword);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }

}