<?php
/**
 * 团对订单查询
 */

namespace Controller\TeamOrder;

use Business\Member\MemberRelation;
use Business\TeamOrder\TeamOrderSearch as searchBusiness;
use Library\Controller;
use Model\Member\Guide;
use Model\Product\Area;

class TeamOrderSearch extends Controller
{
    private $_orderSearchBusiness;
    private $_memberGuide;

    private $_sid = null;
    private $_loginInfo = null;

    public function __construct()
    {
        $this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
        $this->_sid       = $this->_loginInfo['sid'];
    }
    
    /**
     *
     */
    private function _getOrderSearchBusiness()
    {
        if (empty($this->_orderSearchBusiness)) {
            $this->_orderSearchBusiness = new searchBusiness();
        }

        return $this->_orderSearchBusiness;
    }

    private function _getMemberGuideModel()
    {
        if (empty($this->_memberGuide)) {
            $this->_memberGuide = new Guide();
        }

        return $this->_memberGuide;
    }

    /**
     * 分销商报团预订查询
     *
     * <AUTHOR>
     * @date   2018-05-03
     */
    public function getBuyList()
    {
        $loginId  = $this->_sid;
        $business = $this->_getOrderSearchBusiness();

        //游玩日期
        $playDateBegin = I('post.play_date_begin', '');
        $playDateEnd   = I('post.play_date_end', '');
        //订单状态 0 未确认 1 已确认 2 已验证 3 已取消
        $status = I('post.status', false);
        //团单子单状态
        $sonStatus = I('post.son_status', false);
        //景区ID
        $lid = I('post.lid', false);
        //票类ID
        $tid = I('post.tid', false);
        //页数
        $page = I('post.page', 1, 'int');
        //数量
        $size = I('post.size', 15, 'int');
        //是否是汇总
        $total = I('post.total', false);
        //搜索类型 1团单 2子订单
        $searchType = I('post.search_type', false);
        //搜索的关键字
        $keyWord = I('post.key_word', '');
        //搜索订单订购渠道
        $orderType = I('post.ordertype', '');
        //下单开始时间
        $orderBeginTime = I('post.order_begin_time', '');
        //下单结束时间
        $orderEndTime = I('post.order_end_time', '');
        //是否住宿
        $isLodging = I('post.is_lodging', '');
        //团单类型
        $teamTypeId = I('post.team_type_id', '');
        //团单类型
        $payStatus = I('post.pay_status', false);
        $aid       = I('post.aid', false);

        if ((!$orderBeginTime || !$orderEndTime) && (!$playDateBegin || !$playDateEnd)) {
            $this->apiReturn(403, [], '下单时间段或者游玩日期必选其一');
        }

        $data = $business->getTeamOrderList(false, $loginId, $aid, $status, $lid, $tid, $payStatus, $page, $size,
            $total, $searchType, $keyWord, $orderType, $orderBeginTime, $orderEndTime, $isLodging, $teamTypeId,
            $playDateBegin, $playDateEnd, '', 2, $sonStatus);
        if ($data['data']['list']) {
            $aidArr            = array_column($data['data']['list'], 'aid');
            $tModel            = new \Model\Product\Ticket();
            $tPriceBiz         = new \Business\Product\Price();
            $memberRelationBus = new MemberRelation();
            $res               = $memberRelationBus->getAidClearingModels($aidArr, $loginId);
            $priceAndStorage   = [];
            foreach ($data['data']['list'] as &$value) {
                $aid                  = $value['aid'] ?? 0;
                $value['clearingway'] = $res[$aid] ?? 0;

                $tidArr = array_column($value['son'], 'tid');
                $priceAndStorageRes = $tPriceBiz->buyBatchGet($value['buyid'], $value['aid'], $value['playtime'],
                    implode(',', $tidArr));

                if ($priceAndStorageRes['code'] == 200 && !empty($priceAndStorageRes['data'])) {
                    $priceAndStorage = $priceAndStorageRes['data'];
                }
                //判断下当前商家有没有权限修改订单信息,这笔订单是他下单的，有权限又改
                $value['modify_auth'] = $value['buyid'] == $loginId ? 1 : 0;
                //是否可以添加子订单 false：不可添加  true：可添加
                $value['can_add_product'] = $value['can_add_sub_order'] && $value['modify_auth'];
                $ticketsLimit = $tModel->getTicketInfoMulti($tidArr, 'id,refund_rule');
                foreach ($value['son'] as &$item) {
                    $item['storage']     = $priceAndStorage[$item['tid']]['availableStorage'] ?? 0;
                    $item['refund_rule'] = ($ticketsLimit[$item['tid']] == '-1' || $ticketsLimit[$item['tid']] == '2') ? false : true;
                }
            }
            unset($value);
        }

        $this->apiReturn($data['code'], $data['data'], $data['msg']);
    }

    public function getTeamOrderInfo()
    {
        $teamOrderId = I('post.team_order_id', '', 'strval,trim');
        $aid = I('post.aid', 0, 'intval');
        $fid = I('post.fid', 0, 'intval');
        if (empty($teamOrderId)) {
            $this->apiReturn(403, [], 'team_order_id参数错误');
        }
        if (!$aid && !$fid) {
            $this->apiReturn(403, [], 'aid或fid参数不能同时为空');
        }
        if ($aid && !$fid) {
            $fid = $this->_sid;
        }
        if (!$aid && $fid) {
            $aid = $this->_sid;
        }
        $res = $this->_getOrderSearchBusiness()->getTeamOrderInfo($teamOrderId, $aid, $fid);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
    /**
     * 供应商报团预定查询
     *
     * <AUTHOR>
     * @date   2018-05-03
     */
    public function getSaleList()
    {
        $loginId  = $this->_sid;
        $business = $this->_getOrderSearchBusiness();

        //游玩日期
        $playDateBegin = I('post.play_date_begin', '');
        $playDateEnd   = I('post.play_date_end', '');
        //订单状态 0 未确认 1 已确认 2 已验证 3 已取消
        $status = I('post.status', false);
        //子单状态 和uu_ss_order里一致 来源参考Consumer/Order/SpecialTeamOrderConsumer.php
        $sonStatus = I('post.son_status', false);
        //景区ID
        $lid = I('post.lid', false);
        //票类ID
        $tid = I('post.tid', false);
        //页数
        $page = I('post.page', 1, 'int');
        //数量
        $size = I('post.size', 15, 'int');
        //分销商
        $fid = I('post.fid', false);
        //支付状态
        $payStatus = I('post.pay_status', false);
        //是否汇总
        $total = I('post.total', false);
        //搜索类型 1团单 2子订单
        $searchType = I('post.search_type', false);
        //搜索的关键字
        $keyWord = I('post.key_word', '');
        //搜索订单订购渠道
        $orderType = I('post.ordertype', '');
        //下单开始时间
        $orderBeginTime = I('post.order_begin_time', '');
        //下单结束时间
        $orderEndTime = I('post.order_end_time', '');
        //是否住宿
        $isLodging = I('post.is_lodging', '');
        //团队类型
        $teamTypeId = I('post.team_type_id', 0, 'intval');
        $groupIds   = I('post.group_ids', '', 'strval');

        if ((!$orderBeginTime || !$orderEndTime) && (!$playDateBegin || !$playDateEnd)) {
            $this->apiReturn(403, [], '下单时间段或者游玩日期必选其一');
        }
        $data = $business->getTeamOrderList(false, $fid, $loginId, $status, $lid, $tid, $payStatus, $page, $size,
            $total, $searchType, $keyWord, $orderType, $orderBeginTime, $orderEndTime, $isLodging, $teamTypeId,
            $playDateBegin, $playDateEnd, $groupIds, 1, $sonStatus);
        if ($data['data']['list']) {
            foreach ($data['data']['list'] as &$value) {
                //modify_auth修改权限 0：无  1：有
                $value['modify_auth'] = 0;
                //判断下当前商家有没有权限修改订单信息,登录者是这笔订单原始供应商，有权限又改
                if (isset($value['son']) && $value['son'][0]['apply_did'] == $loginId) {
                    $value['modify_auth'] = 1;
                }
                //是否可以添加子订单 false：不可添加  true：可添加
                $value['can_add_product'] = $value['can_add_sub_order'] && $value['modify_auth'];
            }
            unset($value);
        }

        $this->apiReturn($data['code'], $data['data'], $data['msg']);
    }

    /**
     * 计调下单根据供应商和分销商查询订单号
     *
     * <AUTHOR>
     * @date   2018-05-03
     */
    public function getTeamOrderByFid()
    {
        $fid       = I('post.fid');
        $playTime  = I('post.play_time');
        $keyWord   = I('post.key_word', '');
        $loginInfo = $this->getLoginInfo();
        $aid       = $loginInfo['sid'];

        if (empty($fid) || empty($playTime) || !strtotime($playTime)) {
            $this->apiReturn(403, [], '参数错误');
        }
        $teamOrderModel = new \Model\Order\TeamOrderSearch();
        $data           = $teamOrderModel->getTeamOrderByAidAndFidAndPlayTime($aid, $fid, $playTime, $keyWord);
        $this->apiReturn(200, $data);
    }

    /**
     * 添加新的导游
     *
     * <AUTHOR>
     * @date   2018-06-19
     */
    public function addTeamGuide()
    {
        //手机号
        $mobile = I('post.mobile');
        //姓名
        $name = I('post.name');
        //身份证号
        $idCard = I('post.id_card');
        //省份
        $province = I('post.province');
        //城市
        $city = I('post.city');
        //城镇
        $county = I('post.county', 0, 'int');

        if (empty($mobile)) {
            $this->apiReturn(400, [], '手机号不能为空');
        }

        if (empty($name)) {
            $this->apiReturn(400, [], '姓名不能为空');
        }

        if (empty($idCard)) {
            $this->apiReturn(400, [], '身份证不能为空');
        }

        if (empty($province)) {
            $this->apiReturn(400, [], '省份不能为空');
        }

        if (empty($city)) {
            $this->apiReturn(400, [], '城市不能为空');
        }

        $guideModel = $this->_getMemberGuideModel();

        //检测手机号唯一性别
        $data = $guideModel->getGuideInfoByMobile($mobile);
        if (!empty($data)) {
            $this->apiReturn(400, [], '该手机号已经添加过');
        }

        //添加导游
        $res = $guideModel->addGuide($mobile, $name, $idCard, $province, $city, $county);
        if ($res === false) {
            $this->apiReturn(400, [], '导游添加失败, 请重试');
        }

        $this->apiReturn(200);
    }

    /**
     * 修改导游信息
     *
     * <AUTHOR>
     * @date   2018-06-22
     */
    public function alterGuideInfo()
    {
        //导游ID
        $id = I('post.id');
        //手机号
        $mobile = I('post.mobile');
        //姓名
        $name = I('post.name');
        //身份证号
        $idCard = I('post.id_card');
        //省份
        $province = I('post.province');
        //城市
        $city = I('post.city');
        //城镇
        $county = I('post.county', 0, 'int');

        if (empty($mobile)) {
            $this->apiReturn(400, [], '手机号不能为空');
        }

        if (empty($name)) {
            $this->apiReturn(400, [], '姓名不能为空');
        }

        if (empty($idCard)) {
            $this->apiReturn(400, [], '身份证不能为空');
        }

        if (empty($province)) {
            $this->apiReturn(400, [], '省份不能为空');
        }

        if (empty($city)) {
            $this->apiReturn(400, [], '城市不能为空');
        }

        $guideModel = $this->_getMemberGuideModel();

        //检测手机号唯一性别
        $data = $guideModel->getGuideInfoByMobile($mobile);
        if (!empty($data) && $data['id'] != $id) {
            $this->apiReturn(400, [], '该手机号已经添加过');
        }

        //添加导游
        $res = $guideModel->saveGuide($id, $mobile, $name, $idCard, $province, $city, $county);
        if ($res === false) {
            $this->apiReturn(400, [], '导游修改失败, 请重试');
        }

        $this->apiReturn(200);
    }

    /**
     * 根据手机号查询导游
     *
     * <AUTHOR>
     * @date   2018-06-19
     */
    public function getGuideInfo()
    {
        //手机号
        $keyWord = I('post.key_word');
        if (empty($keyWord)) {
            $this->apiReturn(400, [], '关键字不能为空');
        }

        $guideModel = $this->_getMemberGuideModel();

        $mobile = '';
        $name   = '';
        if (strlen($keyWord) == 11 && mb_strlen($keyWord) == strlen($keyWord)) {
            $mobile = strval($keyWord);
        } else {
            $name = strval($keyWord);
        }

        //检测手机号唯一性别
        $data = $guideModel->getGuideInfoByMobileAndName($mobile, $name);
        if (empty($data)) {
            $this->apiReturn(400, [], '未找到导游信息');
        }

        $areaModel = new Area();

        foreach ($data as &$value) {
            $province = $areaModel->getProvinceAndCityNameById($value['province']);
            $city     = $areaModel->getProvinceAndCityNameById($value['city']);
            $county   = $areaModel->getTownNameById($value['city'], $value['county']);

            $value['province'] = empty($province) ? '' : $province;
            $value['city']     = empty($city) ? '' : $city;
            $value['county']   = empty($county) ? '' : $county;
        }

        $this->apiReturn(200, $data);
    }

    /**
     * 团单批量操作（验证/完结/取消）
     * <AUTHOR>
     * @date   2019-10-12
     */
    public function multiDealOrder()
    {
        $loginId = $this->_sid;
        $oper    = $this->_loginInfo['memberID'];

        //加个锁
        $redis = \Library\Cache\Cache::getInstance('redis');
        $key   = "team:multi_handle:{$loginId}";
        if (!$redis->lock($key, 1, 300)) {
            $this->apiReturn(204, [], '有任务正在执行当中 ，请勿重复执行');
        }

        //游玩日期
        $playDate = I('post.play_date', false);
        //订单状态 0 未确认 1 已确认 2 已验证 3 已取消
        $status = I('post.status', false);
        //景区ID
        $lid = I('post.lid', false);
        //票类ID
        $tid = I('post.tid', false);
        //分销商id
        $fid = I('post.fid', false);
        //订单支付状态
        $payStatus = I('post.pay_status', false);
        //搜索的关键字
        //搜索订单订购渠道
        $orderType = I('post.ordertype', '');
        //下单开始时间
        $orderBeginTime = I('post.order_begin_time', '');
        //下单结束时间
        $orderEndTime = I('post.order_end_time', '');
        //操作类型 1验证2完结3取消
        $dealType = I('deal_type', 1, 'intval');

        $searchParams = [
            'play_date'        => $playDate,
            'status'           => $status,
            'lid'              => $lid,
            'tid'              => $tid,
            'fid'              => $fid,
            'pay_status'       => $payStatus,
            'ordertype'        => $orderType,
            'order_begin_time' => $orderBeginTime,
            'order_end_time'   => $orderEndTime,
        ];

        $business = $this->_getOrderSearchBusiness();
        $result   = $business->multiDealOrder($loginId, $oper, $dealType, $searchParams);

        $redis->rm($key);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }
}
