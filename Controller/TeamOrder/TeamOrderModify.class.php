<?php
/**
 * 团队门票下单操作
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/5/3
 * Time: 16:33
 */

namespace Controller\TeamOrder;

use Business\Order\Modify;
use Business\TeamOrder\TeamOrderOperation;
use Library\Constants\OrderConst;
use Library\Controller;
use Library\Tools\Validate;

class TeamOrderModify extends Controller
{

    private $_mid;
    private $_sid;

    public function __construct()
    {
        parent::__construct();
        $loginInfo  = $this->getLoginInfo();
        $this->_sid = $loginInfo['sid'];
        $this->_mid = $loginInfo['memberID'];
    }

    /**
     * 团单取消
     *
     * @date   2019-09-03
     * <AUTHOR>
     *
     * @param
     *
     * @return string
     */
    public function teamOrderCancel()
    {
        //订单对应退票数后面流水号 {'24425694':24425694_123153645453,'24425695':24425694_123153645453}
        $tmpCancelList = I('post.order_cancel_list', []);
        $teamOrder     = I('post.team_order', '', 'strval');
        if (!$tmpCancelList || !$teamOrder) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '参数缺失');
        }

        //TODO:退款流水号的校验

        $cancelRemark  = '取消团队订单';
        $cancelChannel = OrderConst::TEAM_CANCEL;

        $modifyBiz = new Modify();
        $cancelRes = $modifyBiz->teamBaseCancel($tmpCancelList, $teamOrder, $this->_mid, $this->_sid, $cancelChannel,
            $cancelRemark);

        if (in_array($cancelRes['code'], [200, 1095])) {
            $resData = [
                'ordernum'          => $teamOrder,
                'req_serial_number' => $cancelRes['data']['req_serial_number'],
                'pft_serial_number' => $cancelRes['data']['pft_serial_number'],
            ];

            $succMsg = $cancelRes['code'] == 200 ? '退票成功' : '退票审核提交成功';
            $this->apiReturn($cancelRes['code'], $resData, $succMsg);
        } else {
            $this->apiReturn($cancelRes['code'], [], $cancelRes['msg']);
        }
    }

    /**
     * 团单修改
     *
     * @date   2019-09-03
     * <AUTHOR>
     *
     * @param
     *
     * @return string
     */
    public function teamOrderRefund()
    {
        //订单对应退票数 {'24425694':1,'24425695':2}
        $tmpCancelList = I('post.order_cancel_list', []);

        //退票游客身份证信息 ['24425694':'1502041962010111760,150204196201016137']
        $tmpTouristInfo = I('post.cancel_tourist_list');
        //减票的游客信息
        //tourist_info[0][tourist]: C
        //tourist_info[0][idcard]: J003
        //tourist_info[0][voucher_type]: 99
        $cancelTouristVoucherList = I('post.tourist_info');

        //退款流水号 ['24425694':'1502041962010111760,150204196201016137']

        $reqSerialNumber = I('post.req_serial_number', '', 'strval');
        $teamOrder       = I('post.team_order', '', 'strval');
        //参数校验
        if (empty($tmpCancelList)) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '订单号参数缺失');
        }
        $tmpTouristInfo = empty($tmpTouristInfo) ? [] : $tmpTouristInfo;
        //var_dump($tmpTouristInfo, $cancelTouristVoucherList);
        $cancelTouristVoucherListMap = [];
        $cancelOtherVoucherPersonInfoMap = [];
        if (!empty($cancelTouristVoucherList)) {
            $cancelTouristVoucherListMap = array_column($cancelTouristVoucherList, null, 'idcard');
        }
        //TODO:退款流水号的校验
        $cancelList = [];
        if ($tmpCancelList && is_array($tmpCancelList)) {
            foreach ($tmpCancelList as $subOrdernum => $cancelNum) {
                $subOrdernum = strval($subOrdernum);
                $cancelNum   = intval($cancelNum);

                if ($subOrdernum && $cancelNum > 0) {
                    $cancelData = [
                        'num' => $cancelNum,
                    ];
                    foreach ($tmpTouristInfo as $key => $value) {
                        if ($key == $subOrdernum) {
                            $touristInfo = explode(',', $value);
                            foreach ($touristInfo as $v) {
                                $idcard = strval($v);
                                if (!empty($cancelTouristVoucherListMap[$idcard])) {
                                    if ($idcard && $cancelTouristVoucherListMap[$idcard]['voucher_type'] == 1) {
                                        if (!Validate::isIDCard($idcard)) {
                                            $this->apiReturn(parent::CODE_NO_CONTENT, [], "身份证【{$idcard}】不合法");
                                        }
                                    }
                                    $cancelOtherVoucherPersonInfoMap[$subOrdernum][] = [
                                        'idcard' => $idcard,
                                        'idcard_type' => $cancelTouristVoucherListMap[$idcard]['voucher_type'],
                                    ];
                                } else {
                                    if (!Validate::isIDCard($idcard)) {
                                        $this->apiReturn(parent::CODE_NO_CONTENT, [], "身份证【{$idcard}】不合法");
                                    }
                                }
                            }
                            $cancelData['tourist_info'] = $touristInfo;
                            break;
                        }
                    }
                    foreach ($reqSerialNumber as $serialOrder => $serialNumber) {
                        if ($serialOrder == $subOrdernum) {
                            $cancelData['serial_number'] = $serialNumber;
                            break;
                        }
                    }
                    $cancelList[$subOrdernum] = $cancelData;
                }
            }
        }
        //var_dump($cancelPersonInfoMap);exit;
        if (!$cancelList) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '退票参数错误');
        }
        //这里组装非身份证类型退票的游客信息
        //https://12301-cc.feishu.cn/wiki/NhPYwj0uaiWUSgkPYACcE8WgnOe?from=from_copylink
        $cancelRemark  = '修改团队订单';
        $cancelChannel = OrderConst::TEAM_CANCEL;

        $modifyBiz = new Modify();
        $cancelRes = $modifyBiz->teamBaseRefund(
            $cancelList, $teamOrder, $this->_mid, $this->_sid, $cancelChannel,
            $cancelRemark, true, ['cancelOtherVoucherPersonInfoMap' => $cancelOtherVoucherPersonInfoMap]);

        if (in_array($cancelRes['code'], [200, 1095])) {
            $resData = [
                'ordernum'          => $teamOrder,
                'req_serial_number' => $cancelRes['data']['req_serial_number'],
                'pft_serial_number' => $cancelRes['data']['pft_serial_number'],
            ];

            $succMsg = $cancelRes['code'] == 200 ? '退票成功' : '退票审核提交成功';
            $this->apiReturn($cancelRes['code'], $resData, $succMsg);
        } else {
            $this->apiReturn($cancelRes['code'], [], $cancelRes['msg']);
        }
    }

    /**
     * 批量团单修改
     *
     * @date   2019-09-03
     * <AUTHOR>
     *
     * @param
     *
     * @return string
     */
    public function batchModifyTeamOrder()
    {
        $teamOrder = I('post.team_order', '', 'strval');
        //订单对应退票数 {'24425694':1,'24425695':2}
        $tmpCancelList = I('post.order_cancel_list', []);
        //退票游客身份证信息 ['订单号':'1502041962010111760,150204196201016137']
        //加票游客身份证信息 ['订单号':'林晨&1502041962010111760&1,翁彬&150204196201016137&1']
        $tmpTouristInfo = I('post.cancel_tourist_list', []);
        if (empty($tmpCancelList)) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '订单号参数缺失');
        }
        $modifyBiz = new Modify();
        $result    = $modifyBiz->batchModifyTeamOrder($teamOrder, $this->_mid, $this->_sid, $tmpCancelList,
            $tmpTouristInfo);
        if (isset($result['code'])) {
            // 处理数据返回给前端
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 修改团单导游信息
     *
     *
     * <AUTHOR>
     */
    public function changeTeamOrderGuide()
    {
        $teamOrderNum = I('post.team_order_num', '', 'strval');
        $guideId      = I('post.guide', 0, 'intval'); //新的导游id
        if (empty($guideId) || empty($teamOrderNum)) {
            $this->apiReturn(203, [], "导游id或者团单号不能为空");
        }
        $teamOrderOperationBz = new TeamOrderOperation();
        $result     = $teamOrderOperationBz->changeTeamOrderGuide($this->_sid, $teamOrderNum, $guideId, $this->_mid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}