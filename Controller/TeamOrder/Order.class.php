<?php
/**
 * 团队门票下单操作
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/5/3
 * Time: 16:33
 */

namespace Controller\TeamOrder;

use Business\TeamOrder\TeamOrderDiscount;
use Business\TeamOrder\TeamRules as TeamRulesBz;
use Library\Controller;
use Process\Order\TeamOrder;
use Business\TeamOrder\Application;
use Business\TeamOrder\TeamOrderVerify;

class Order extends Controller
{
    private $_memberId;
    private $_loginInfo;

    public function __construct()
    {
        $this->_memberId  = $this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
    }

    /**
     * 提交订单
     *
     * @date   2018-05-03
     * <AUTHOR>
     *
     * @param
     *
     * @return string
     */
    public function submit()
    {
        try {
            $orderData             = TeamOrder::submit($this->_loginInfo['memberID'], $this->_loginInfo['sid']);
            $orderData['memberId'] = $this->_memberId;
            $app                   = new Application();
            $order                 = $app->order;
            $prepare               = $app->prepare;
            //报团计调下单
            if ($orderData['aid'] && $orderData['fid']) {
                $orderData['memberId'] = $orderData['fid'];
                $orderData['aid']      = $this->_memberId;
                $subData               = $prepare->getOrderData($orderData);
            } else {
                $subData  = $prepare->getOrderData($orderData);
                //aid无论是计调下单、供应商下单、分销商下单都会传
                //fid是计调下单、分销商下单会传
                $verifyBis = new TeamOrderVerify();
                //检测下单是否需要审核
                $checkRes = $verifyBis->checkOrderReview($orderData['sourceId'], $orderData['aid'],
                    $orderData['memberId'], $orderData);
                if ($checkRes['code'] == 201) {
                    $this->apiReturn(201, [],
                        '已生成报团申请单，报团申请单号：' . $checkRes['data']['audit_id'] . '；可在分销商报团申请管理查询，供应商审核后生效。');
                }
                if ($checkRes['code'] != 200) {
                    $this->apiReturn($checkRes['code'], $checkRes['data'], $checkRes['msg']);
                }
            }
            $subData['team_data']['operator_id'] = $this->_loginInfo['memberID'];
            $res                                 = $order->submit($subData);
            $this->apiReturn(200,
                [
                    'team_order' => $res[0],
                    'qrUrl'      => \Business\TeamOrder\Booking::getTeamQrCode(
                        $subData['team_data']['ordernum'],
                        $subData['team_data']['tnum'],
                        $subData['team_data']['playtime']),
                    'trade_id'   => $res[2]['tradeId'],
                ],
                $res[1]);
        } catch (\Exception $e) {
            if ($e->getCode() == 1001) {
                $return = json_decode($e->getMessage(), true);
                $this->apiReturn($e->getCode(), $return['data'], $return['msg']);
            }
            $this->apiReturn($e->getCode(), [], $e->getMessage());
        }
    }

    /**
     * 将订单设置为已支付状态
     *
     * @date   2018-05-08
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    public function setTeamOrderPayed()
    {
        //支付方式:1=现金，2=授信，3=余额，4=到付
        $payType = I('post.pay_type', '', 'strval');
        //未支付的订单号
        $orderStr = I('post.order_ids', '', 'strval');
        //团队订单号
        $teamOrder = I('post.team_order', '', 'strval');

        if (empty($payType)) {
            $this->apiReturn(201, [], '请选择支付方式');
        }
        if (!($teamOrder && $orderStr)) {
            $this->apiReturn(201, [], '参数缺失');
        }

        $app = new Application();

        $payFail = ['son_ordernum' => ['IN', $orderStr]];

        $sonOrder = $app->order_class->getTeamModel()->getDataByMainOrder($teamOrder);

        $orderArr = array_column($sonOrder, 'son_ordernum');

        if (!$orderArr) {
            $this->apiReturn(201, [], '数据异常');
        }

        foreach ($orderArr as $orderId) {
            $res = $app->order->setOrderPayed($orderId, $payType, $teamOrder, 16);

            if ($res[0] != 200) {
                $payFail[] = $orderId . '支付失败，原因:' . $res[1];
            }

            if ($payType == 4) {
                $this->apiReturn($res[0], ['fail' => $payFail], $res[1]);
            }
        }

        $this->apiReturn($res[0], ['fail' => $payFail], $res[1]);
    }

    /**
     * 获取团队类型
     *
     * @return array
     * <AUTHOR>
     * @date 2021/6/10
     *
     *
     */
    public function getTeamTypeList()
    {
        $fid         = I('post.fid', 0, 'intval'); //分销商id
        $aid         = I('post.aid', 0, 'intval'); //供应商id
        $teamRulesBz = new TeamRulesBz();
        $result      = $teamRulesBz->getTeamTypeList($fid, $aid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取报团订单修改限制信息
     *
     * @return array
     * <AUTHOR>
     * @date 2021/6/10
     *
     *
     */
    public function getTeamTypeInfo()
    {
        $id          = I('post.id', 0, 'intval'); //限制id
        $teamRulesBz = new TeamRulesBz();
        $result      = $teamRulesBz->getTeamOrderEditLimitById($id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 判断团队类型是否必填
     *
     * @date 2022/02/10
     * @auther yangjianhui
     * @return array
     */
    public function judgeTeamTypeRequired()
    {
        $aid         = I('post.aid', 0, 'intval'); //上级供应商id
        $teamRulesBz = new TeamRulesBz();
        $result      = $teamRulesBz->judgeTeamTypeRequired($aid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    /**
     * 获取优惠信息
     *
     * @date 2023/06/09
     * @auther yangjianhui
     * @return array
     */
    public function getDiscountInfo()
    {
        $playDate   = I('post.play_date', '', 'strval'); //游玩日期
        $ticketInfo = I('post.ticket_info', '', 'strval'); //具体的下单参数
        if (empty($playDate) || empty($ticketInfo)) {
            $this->apiReturn(201, [], '参数缺失');
        }
        $teamDiscountBz = new TeamOrderDiscount($this->_memberId);
        $result         = $teamDiscountBz->getDiscountInfo($ticketInfo, $playDate);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * @date 2023/06/09
     * @auther yangjianhui
     * @return array
     */
    public function getDiscountRuleList()
    {
        $playDate   = I('post.play_date', '', 'strval'); //游玩日期
        $ticketInfo = I('post.ticket_info', '', 'strval'); //具体的下单参数
        if (empty($playDate) || empty($ticketInfo)) {
            $this->apiReturn(201, [], '参数缺失');
        }
        $teamDiscountBz = new TeamOrderDiscount($this->_memberId);
        $result         = $teamDiscountBz->getDiscountRuleList($ticketInfo, $playDate);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 抱团优惠详情
     *
     * @date 2023/06/26
     * @auther yangjianhui
     * @return array
     */
    public function getDiscountDetail()
    {
        $discountInfo = I('post.discount_info', '', 'strval'); //具体的下单参数
        if (empty($discountInfo)) {
            $this->apiReturn(201, [], '参数缺失');
        }
        $teamDiscountBz = new TeamOrderDiscount($this->_memberId);
        $result         = $teamDiscountBz->getDiscountDetail($discountInfo);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}