<?php
/**
 * 报团验证限制操作
 * User: lanwanhui
 * Date: 2021/2/7
 * Time: 11:25
 */
namespace Controller\TeamOrder;

use Library\Controller;
use Business\TeamOrder\TeamLimitVerify as TeamLimitVerifyBusiness;

class TeamLimitVerify extends Controller
{

    //供应商id
    private $_sid;


    public function __construct()
    {
        $this->_sid = $this->isLogin();
    }


    /**
     * 新增报团限制记录
     * User: lanwanhui
     * Date: 2021/2/7
     *
     * @return
     */
    public function addLimitVerify()
    {

        $limitName   = I('post.limit_name','', 'strval');   //限制名称
        $limitMinute = I('post.limit_minute',0, 'strval');  //限制的分钟数
        $LimitTid    = I('post.limit_ticket','', 'strval'); //票id用逗号隔开
        $limitType   = I('post.limit_type', 1, 'intval'); //限制类型 1订单首张门票，2团单首张门票

        //处理传过来的票id
        $tidArray    = explode(',',$LimitTid);
        foreach ($tidArray as $k => $tid) {
            if (!is_numeric($tid)) {
                unset($tidArray[$k]);
            }
        }
        $tidArray = array_values(array_unique($tidArray));

        $limitName = trim($limitName);

        if (empty($limitName) || mb_strlen($limitName) > 30) {
            $this->apiReturn(204, [], '限制名称不能为空或者长度不能大于30位');
        }

        if (!preg_match("/^[1-9]{1}[0-9]{0,2}$/", $limitMinute) || $limitMinute > 300) {
            $this->apiReturn(204, [], '限制规则分钟数必须是1-300的整数');
        }

        if (empty($tidArray)) {
            $this->apiReturn(204, [], '限制产品不能为空');
        }

        $teamLimitVerifyBiz = new TeamLimitVerifyBusiness();

        $rs = $teamLimitVerifyBiz->addTeamLimitVerify($this->_sid, $limitName, $limitMinute, $limitType, $tidArray);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }


    /**
     * 获取一条抱团限制记录
     * User: lanwanhui
     * Date: 2021/2/7
     *
     * @return
     */
    public function getLimitVerifyDetail()
    {
        $limitId = I('limit_id',0, 'intval'); //抱团验证限制表id
        if ($limitId < 1) {
            $this->apiReturn(204, [], 'limit_id错误');
        }

        $teamLimitVerifyBiz = new TeamLimitVerifyBusiness();

        $rs = $teamLimitVerifyBiz->getTeamLimitVerifyDetail($this->_sid, $limitId);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);
    }


    /**
     * 修改报团限制记录
     * User: lanwanhui
     * Date: 2021/2/7
     *
     * @return
     */
    public function editLimitVerify()
    {

        $limitName     = I('post.limit_name','', 'strval');    //限制名称
        $limitMinute   = I('post.limit_minute',0, 'strval');   //限制的分钟数
        $LimitTid      = I('post.limit_ticket','', 'strval');  //票id用逗号隔开
        $limitId       = I('post.limit_id',0, 'intval');       //抱团验证限制表id
        $limitType     = I('post.limit_type', 1, 'intval');    //限制类型 1订单首张门票，2团单首张门票

        $tidArray      = explode(',',$LimitTid);
        foreach ($tidArray as $k => $tid) {
            if (!is_numeric($tid)) {
                unset($tidArray[$k]);
            }
        }
        $tidArray = array_values(array_unique($tidArray));

        $limitName = trim($limitName);

        if (empty($limitName) || mb_strlen($limitName) > 30) {
            $this->apiReturn(204, [], '限制名称不能为空或者长度不能大于30位');
        }

        if (!preg_match("/^[1-9]{1}[0-9]{0,2}$/", $limitMinute) || $limitMinute > 300) {
            $this->apiReturn(204, [], '限制规则分钟数必须是1-300的整数');
        }

        if ($limitId < 1) {
            $this->apiReturn(204, [], 'limit_id错误');
        }

        if (empty($tidArray)) {
            $this->apiReturn(204, [], '限制产品不能为空');
        }

        $teamLimitVerifyBiz = new TeamLimitVerifyBusiness();

        $rs = $teamLimitVerifyBiz->editLimitVerify($this->_sid, $limitId, $limitName, $limitMinute, $limitType, $tidArray);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }


    /**
     * 删除报团限制记录
     * User: lanwanhui
     * Date: 2021/2/7
     *
     *
     */
    public function deleteLimitVerify()
    {
        $limitId  = I('post.limit_id',0, 'intval'); //抱团验证限制表id
        if ($limitId < 1) {
            $this->apiReturn(204, [], 'limit_id错误');
        }

        $teamLimitVerifyBiz = new TeamLimitVerifyBusiness();

        $rs = $teamLimitVerifyBiz->deleteLimitVerify($this->_sid, $limitId);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }


    /**
     * 获取报团限制列表
     * User: lanwanhui
     * Date: 2021/2/7
     *
     *@return
     */
    public function getLimitVerifyList()
    {
        $limitName = I('limit_name','', 'strval');   //限制名称
        $pageSize  = I('page_size', 10, 'intval'); //每页数量
        $page      = I('page', 1, 'intval');       //页数

        if ($pageSize < 1 || $page < 1) {
            $this->apiReturn(204, [], '搜索参数错误');
        }

        $teamLimitVerifyBiz = new TeamLimitVerifyBusiness();

        $rs = $teamLimitVerifyBiz->getLimitVerifyList($this->_sid, $limitName, $pageSize, $page);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }

}