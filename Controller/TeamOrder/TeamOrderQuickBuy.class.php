<?php
/**
 * 团单新版快速购票
 */

namespace Controller\TeamOrder;

use Business\TeamOrder\TeamOrderQuickBuyService;
use Business\TeamOrder\TeamRegexpService;
use Library\Controller;
use Throwable;

class TeamOrderQuickBuy extends Controller
{
    private $sid;
    private $memberId;
    private $biz;

    public function __construct()
    {
        if (ENV == 'LOCAL') {
            $loginInfo = [
                'sid' => 6970,
                'memberID' => 6970,
                'sdtype' => 0,
                'dtype' => 0,
                'customerId' => 6074,
            ];
        } else {
            $loginInfo = $this->getLoginInfo();
        }
        $this->sid = $loginInfo['sid'];
        $this->memberId = $loginInfo['memberID'];
        $this->biz = TeamOrderQuickBuyService::getInstance();
    }

    public function quickBuySolutionList()
    {
        $page = I('post.page', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');
        $page = $page < 1 ? 1 : $page;
        $pageSize = $page < 1 ? 10 : $pageSize;
        $keyword = I('post.keyword', '', 'trim,strval');
        $aid = I('post.aid', 0, 'intval');
        if (!$aid) {
            $this->apiReturn(400, [], '供应商aid参数必填');
        }
        $params = [
            'aid' => $aid,
            'sid' => $this->sid,
            'page' => $page,
            'pageSize' => $pageSize,
            'keyword' => $keyword,
        ];
        try {
            $data = $this->biz->quickBuySolutionList($params);
            $this->apiReturn(200, $data, 'success');
        } catch (\Throwable $e) {
            $this->apiReturn(400, [], $e->getMessage());
        }
    }

    public function importExcel()
    {
        $aid = I('post.aid', 0, 'intval');//供应商id
        $fid = I('post.fid', 0, 'intval');//分销商id
        $teamTypeId = I('post.team_type_id', 0, 'intval');//团队类型id
        $solutionIdJson = I('post.solution_id_arr', '', 'strval');//快捷购票方案json [{"solutionId":1,"memberId":2},...]
        $file = $_FILES['file'];//游客Excel文件
        $solutionIdArr = empty($solutionIdJson) ? [] : json_decode(html_entity_decode($solutionIdJson), true);
        try {
            $params = [
                'sid' => $this->sid,
                'aid' => $aid,
                'fid' => $fid,
                'team_type_id' => $teamTypeId,
                'solution_id_arr' => $solutionIdArr,
                'file' => $file,
            ];
            $data = $this->biz->importExcel($params);
            $this->apiReturn(200, $data, 'success');
        } catch (Throwable $e) {
            $this->apiReturn(400, [], $e->getMessage());
        }
    }

    //如果重新选择游玩时间需要重新匹配方案
    public function touristMatchSolution()
    {
        $aid = I('post.aid', 0, 'intval');//供应商id
        $fid = I('post.fid', 0, 'intval');//分销商id
        $teamTypeId = I('post.team_type_id', 0, 'intval');//团队类型id
        $solutionIdJson = I('post.solution_id_arr', '', 'strval');//方案json
        $date = I('post.date', '', 'strval');//游玩时间
        $touristListJson = I('post.tourist_list', '', 'strval');//用字符串 对象的话前端可能会有截断
        if (empty($date) || !strtotime($date)) {
            $this->apiReturn(400, [], '游玩日期不能为空');
        }
        $solutionIdArr = empty($solutionIdJson) ? [] : json_decode(html_entity_decode($solutionIdJson), true);
        if (!$solutionIdArr) {
            $this->apiReturn(400, [], '请选择方案');
        }
        $touristList = empty($touristListJson) ? [] : json_decode(html_entity_decode($touristListJson), true);
        if (empty($touristList)) {
            $this->apiReturn(400, [], '游客信息不能为空');
        }
        /*
        foreach ($touristList as $item) {
            if (empty($item['idCard']) || !isset($item['valid']) || !isset($item['cardTypeInt'])) {
                //$this->apiReturn(400, [], '游客信息不完整');
            }
        }*/
        try {
            $params = [
                'sid' => $this->sid,
                'aid' => $aid,
                'fid' => $fid,
                'team_type_id' => $teamTypeId,
                'solutionIdArr' => $solutionIdArr,
                'touristList' => $touristList,
                'date' => $date,
            ];
            $data = $this->biz->touristMatchSolution($params);
            $this->apiReturn(200, $data, 'success');
        } catch (Throwable $e) {
            $this->apiReturn(400, [], $e->getMessage());
        }
    }

    //Excel 模板下载接口
    public function downloadExcel()
    {
        $this->biz->downloadExcel();
    }

    //证件有效性校验
    public function cardVerify()
    {
        $cardType = I('post.card_type', 0, 'intval');
        $idCard = I('post.id_card', '', 'strval');
        try {
            $data = $this->biz->cardVerify($idCard, $cardType);
            $this->apiReturn(200, [
                'valid' => $data,
                'cardType' => $cardType,
                'idCard' => $idCard,
            ], 'success');
        } catch (Throwable $e) {
            $this->apiReturn(400, [], $e->getMessage());
        }
    }

    public function getGenderAndAgeByIdCard()
    {
        $idCard = I('post.id_card', '', 'strval');
        if (empty($idCard)) {
            $this->apiReturn(400, [], '证件号不能为空');
        }
        try {
            $isIdCard = TeamRegexpService::getInstance()->isIdCard($idCard);
            $isHKMTResidenceCard = TeamRegexpService::getInstance()->isHKMTResidenceCard($idCard);
            if (!$isIdCard && !$isHKMTResidenceCard) {
                throw new \Exception('证件格式错误');
            }
            $data = $this->biz->getGenderAndAgeByIdCard($idCard);
            $areaData = null;
            if ($isIdCard) {
                $areaData = $this->biz->getProvinceCityByCardId([$idCard]);
            }
            $ret = [
                'age' => $data[0] > 0 ? $data[0] : '',
                'gender' => in_array($data[1], ['male', 'female']) ? $data[1] : '',
                'areaInfo' => $areaData[$idCard] ?? null,
            ];
            $this->apiReturn(200, $ret, 'success');
        } catch (Throwable $e) {
            $this->apiReturn(400, [], $e->getMessage());
        }
    }

    public function getProvinceCityByCardId()
    {
        $idCard = I('post.id_card_str', '', 'strval');
        $idCardArr = explode(',', $idCard);
        try {
            if (empty($idCardArr)) {
                throw new \Exception('身份证不能为空');
            }
            foreach ($idCardArr as $item) {
                if (!TeamRegexpService::getInstance()->isIdCard($item)) {
                    throw new \Exception('身份证格式错误');
                }
            }
            $data = $this->biz->getProvinceCityByCardId($idCardArr);
            $this->apiReturn(200, $data, 'success');
        } catch (Throwable $e) {
            $this->apiReturn(400, [], $e->getMessage());
        }
    }
}