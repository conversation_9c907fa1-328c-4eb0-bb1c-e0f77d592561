<?php

namespace Controller\TeamOrder;

use Library\Constants\Team\TeamConst;
use Library\Controller;
use Library\Util\JsonUtil;
use Library\Util\Team\TeamUtil;

class TeamOrder extends Controller
{

    public function getTeamOrderInfo()
    {
        $teamOrderNum = I('team_order_num', '', 'strval');
        if (empty($teamOrderNum)) {
            $this->apiReturn(203, [], "订单号有误");
        }
        //获取团单信息
        $teamOrderModel = new \Model\Order\TeamOrderSearch();
        $teamOrderInfo = $teamOrderModel->getMainOrderInfoByOrderNum($teamOrderNum, 'status,pay_status,active_time,active_status,active_code,ext_content');
        $extContent = JsonUtil::decode($teamOrderInfo['ext_content']);
        list($enableTeamCode, $activeCheck, $enableTeamCheck) = TeamUtil::extractTeamCode($extContent);
        if ($enableTeamCode == TeamConst::ENABLE_TEAM_CODE_YES && $activeCheck == TeamConst::ENABLE_TEAM_ACTIVE_YES) {
            $activeCode = $teamOrderInfo['active_code'];
        } else {
            $activeCode = '';
        }
        $payStatusMap = TeamConst::PAY_STATUS_MESSAGE;
        $statusMap = TeamConst::STATUS_MESSAGE;
        $returnData = [
            'team_order_num' => $teamOrderNum,
            'status' => (int)$teamOrderInfo['status'],
            'pay_status' => (int)$teamOrderInfo['pay_status'],
            'status_text' => $statusMap[(int)$teamOrderInfo['status']] ?? '',
            'pay_status_text' => $payStatusMap[(int)$teamOrderInfo['pay_status']] ?? '',
            'active_time' => empty($teamOrderInfo['active_time']) ? "" : date('Y-m-d H:i:s', $teamOrderInfo['active_time']),
            'active_status' => (int)$teamOrderInfo['active_status'],
            'active_code' => $activeCode,
            'enable_teamcode' => $enableTeamCode,
            'active_check' => $activeCheck,
            'enable_team_check' => $enableTeamCheck,
        ];

        $this->apiReturn(200, $returnData, "查询成功");
    }
}
