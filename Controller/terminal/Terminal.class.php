<?php

namespace controller\terminal;

use Business\JsonRpcApi\ScenicLocalService\TerminalShare;
use Business\MemberLogin\MemberLoginHelper;
use Controller\Member\Invoice;
use Jobby\Exception;
use Library\Business\TerminalCache;
use Library\Controller;

class Terminal extends Controller
{

    public function __construct()
    {

    }

    /**
     * 获取终端列表
     *
     * <AUTHOR>
     * @date   2017-9-16
     */
    public function getTerminalList()
    {
        $code = 'success';
        $list = [];
        $msg  = '';
        try {
            $loginInfo = $this->getLoginInfo();
            $memberSID = $loginInfo['sid'];
            $terminal  = I('post.terminal', '', 'intval');
            if (empty($terminal)) {
                throw new \Exception("终端号不能为空");
            }

            $isSuper       = $this->isSuper();
            $terminalModel = new \Model\Product\Terminal();
            $list          = $terminalModel->getSubList($terminal, $isSuper, $memberSID);
        } catch (\Exception $e) {
            $code = 'fail';
            $msg  = $e->getMessage();
        }

        exit(json_encode(['status' => $code, 'list' => $list, 'msg' => $msg]));
    }

    /**
     * 添加分终端
     *
     * <AUTHOR>
     * @date   2017-9-16
     */
    public function addSubTerminal()
    {
        $code        = 'success';
        $msg         = '';
        $terminalArr = [];
        try {
            if (MemberLoginHelper::isSubMerchantLogin()){
                throw new \Exception('无使用权限');
            }
            $loginInfo = $this->getLoginInfo();
            $sid       = $loginInfo['sid'];
            $landId    = I('post.landid', '', 'intval');
            $num       = I('post.num', '', 'intval');
            $name      = I('post.name', '', 'strval');
            $terminalShareApi = new TerminalShare();
            $result    = $terminalShareApi->addSubTerminalService($landId,$sid,$name);
            if ($result['code'] != 200){
                throw new \Exception($result['msg']);
            }
//            $terminalModel = new \Model\Product\Terminal();
//            $return        = $terminalModel->addSubs($landId, $sid, 1, $name);
//            if ($return[0] == 0) {
//                throw new \Exception($return[1]);
//            }

            $terminalArr = $result['data'];
            $terminalId  = $terminalArr[0]['terminal_id'];

            //生成成功后 删除缓存
            $terminalCache = new TerminalCache();
            $terminalCache->DeleteTerminalInfo($terminalId);

        } catch (\Exception $e) {
            $code = 'fail';
            $msg  = $e->getMessage();
        }

        exit(json_encode(['status' => $code, 'msg' => $msg, 'terminals' => $terminalArr]));
    }

    /**
     * 删除终端
     * @todo 现在共享终端和分终端的数据没有区别开，导致显示会比较乱，后续需要整理下。
     *
     * <AUTHOR>
     * @date 2020/5/6
     *
     * @return array
     *
     */
    public function deleteSubTerminal()
    {
        $code = 'success';
        $msg  = '';
        try {
            $terminalId = I('post.terminal_id', '', 'intval');
            $landId     = I('post.land_id', '', 'intval');
            if (empty($terminalId)) {
                throw new \Exception("分终端号不能为空");
            }

            $loginInfo     = $this->getLoginInfo();
            $memberId      = $loginInfo['sid'];
            $isSuper       = $this->isSuper();
            $terminalShareApi = new TerminalShare();
            $res = $terminalShareApi->deleteSubTerminalService($terminalId, $landId, $isSuper, $memberId);
//            $terminalModel = new \Model\Product\Terminal();
//            $res           = $terminalModel->deleteTerminal($terminalId, $landId, $isSuper, $memberId);
//
//            if (!$res['code']) {
//                throw new \Exception($res['msg']);
//            }
            if ($res['code'] != 200){
                throw new \Exception($res['msg']);
            }
            //生成成功后 删除缓存
            $terminalId    = $res['data']['terminal_id'];
            $terminalCache = new TerminalCache();
            $terminalCache->DeleteTerminalInfo($terminalId);

        } catch (\Exception $e) {
            $code = 'fail';
            $msg  = $e->getMessage();
        }

        exit(json_encode(['status' => $code, 'msg' => $msg]));
    }
}