<?php
/**
 * User: lipeijun
 * Date: 2018-05.25
 * Time: 16:41
 */

namespace Controller\terminal;


use Library\Cache\Cache;
use Library\Controller;
use Model\Terminal\FaceCompare;

class Api extends Controller
{
    public function __construct() 
    {
        $this->isLogin('ajax');
    }

    /**
     * 获取Model类
     * @return FaceCompare
     */
    private function getFaceModel() 
    {
        if (!isset($this->_FaceModel)) {
            $this->_FaceModel = new FaceCompare();
        }

        return $this->_FaceModel;
    }
    /**
     * 获取人脸列表
     */
    public function getFaceList() 
    {
        $code = 200;
        $data = [];
        $msg  = '列表获取成功';
        $params     = I('get.');
        if(!isset($params['groupid'])){
            $params['groupid'] = 0;
        }
        if(!isset($params['mobile'])){
            $params['mobile'] = 0;
        }
        if(!isset($params['idcard'])){
            $params['idcard'] = 0;
        }
        $data  = $this->getFaceModel()->getFaceList($params['groupid'], $params['mobile'], $params['idcard'], $params['offset'], $params['page_size']);
        if (empty($data['list'])) {
            $this->apiReturn(202, [], '暂无数据');
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 获取分组列表
     */
    public function getFaceGroupList() 
    {
        $code = 200;
        $data = [];
        $msg  = '列表获取成功';
        $params  = I('get.');
        if(!isset($params['groupid'])){
            $params['groupid'] = 0;
        }
        if(!isset($params['groupName'])){
            $params['groupName'] = 0;
        }
        $data  = $this->getFaceModel()->getFaceGroupList($params['groupid'], $params['groupName'], $params['offset'], $params['page_size']);
        if (empty($data['list'])) {
            $this->apiReturn(202, [], '暂无数据');
        }

        $this->apiReturn($code, $data, $msg);
    }
    /**
     * 新增分组
     */
    public function newFaceGroup() 
    {
        $code = 200;
        $data = [];
        $msg  = '分组创建成功';
        $params  = I('post.');
        if(empty($params['groupid'])){
            $this->apiReturn(202, [], '缺少必要参数');
        }
        if(empty($params['groupName'])){
            $this->apiReturn(202, [], '缺少必要参数');
        }
        $lastId  = $this->getFaceModel()->newFaceGroup($params['groupid'], $params['groupName']);
        if (empty($lastId)) {
            $this->apiReturn(202, [], '创建失败');
        }

        $this->apiReturn($code, $data, $msg);
    }
    /**
     * 更新分组信息
     */
    public function updateFaceGroup() 
    {
        $code = 200;
        $data = [];
        $msg  = '资料修改成功';
        $params  = I('post.');
        if(empty($params['groupid'])){
            $this->apiReturn(202, [], '缺少必要参数');
        }
        if(empty($params['groupName'])){
            $this->apiReturn(202, [], '缺少必要参数');
        }
        //先检测一遍分组是否存在
        $res = $this->getFaceModel()->checkGroupExist($params['groupid']);
        if(empty($res)){
            $this->apiReturn(202, [], '分组信息有误');
        }
        //存在则更新分组信息
        $data  = $this->getFaceModel()->updateFaceGroup($params['groupid'], $params['groupName']);
        if (empty($data)) {
            $this->apiReturn(202, [], '资料修改失败');
        }

        $this->apiReturn($code, $data, $msg);
    }
    /**
     * 修改人脸信息
     */
    public function saveFaceDetailInfo()
    {
        $code = 200;
        $data = [];
        $msg  = '资料修改成功';
        $params  = I('post.'); 
        $data  = $this->getFaceModel()->saveFaceDetailInfo($params['groupid'], $params['faceId'], $params['idcard'], $params['mobile'], $params['openid'], $params['unionid'], $params['nickname'], $params['sex']);
        if (empty($data)) {
            $this->apiReturn(202, [], '资料修改失败');
        }

        $this->apiReturn($code, $data, $msg);
    }
    /**
     * 更新人脸照片
     */
    public function updateFaceDetailFaceUrl()
    {
        $code = 200;
        $data = [];
        $msg  = '资料修改成功';
        $params  = I('post.'); 
        $data  = $this->getFaceModel()->updateFaceDetailFaceUrl($params['faceId'], $params['faceUrl']);
        if (empty($data)) {
            $this->apiReturn(202, [], '资料修改失败');
        }

        $this->apiReturn($code, $data, $msg);
        
    }
}