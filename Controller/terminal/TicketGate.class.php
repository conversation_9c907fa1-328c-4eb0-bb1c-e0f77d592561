<?php
/**
 * 人脸识别入园整理的总接口
 * author: han<PERSON><PERSON>
 * Date: 2018/8/24
 */

namespace controller\terminal;

use Library\Controller;
use Model\TerminalManage\OrderTemperatureRecord;
use Model\Order\OrderQuery;

class TicketGate extends Controller
{
    /**
     * 获取测温记录列表
     * <AUTHOR>
     * @date 2020/04/10
     */
    public function getOrderTemperatureRecordList()
    {
        $aid = I('post.aid', 0, 'intval');
        $page = I('post.page', 1, 'intval');
        $pageSize = I('post.pageSize', 10, 'intval');
        $begin = I('post.begin', '', 'strval');
        $end = I('post.end', '', 'strval');
        $recordType = I('post.record_type', -1, 'intval');
        $type = I('post.type', -1, 'intval');
        $showUnusuaTemp = I('post.show_unusual_temp', 1, 'intval');//0不显示异常温度,1显示所有温度

        if (!$aid || !$begin || !$end) {
            $this->apiReturn(204, [], '参数错误');
        }

        if ($showUnusuaTemp == 0) {
            if ($type == -1) {
                $type = 0;
            } elseif ($type == 0) {
                $type = 0;
            } else {
                $res = [
                    'list'     => [],
                    'total'    => 0,
                    'pageSize' => $pageSize,
                ];
                $this->apiReturn(200 , $res, '');
            }
        }

        $orderTemperatureRecordModel = new OrderTemperatureRecord();
        $recordList = $orderTemperatureRecordModel->getOrderTemperatureRecordList($aid, $begin, $end, 'id,ordernum,tnum,card_no,type,create_time,temperature', $page, $pageSize, $recordType, $type);
        $recordTotal = $orderTemperatureRecordModel->getOrderTemperatureRecordCount($aid, $begin, $end, $recordType, $type);

        $orderNumArr = array_column($recordList, 'ordernum');
        $orderModel = new OrderQuery();
        $orderArr = $orderModel->getOrderInfoByMulti($orderNumArr, 'ordernum', 'ordernum,status,dtime', true);

        $statusText = load_config('order_status');

        foreach ($recordList as &$item) {
            $item['status'] = $statusText[$orderArr[$item['ordernum']]['status']] ?? '--';
            if ($item['ordernum']) {
                $item['dtime'] = $orderArr[$item['ordernum']]['dtime'] ?? '--';
            } else {
                $item['dtime'] = date('Y-m-d H:i:s', $item['create_time']);
            }
        }

        $res = [
            'list' => $recordList,
            'total' => $recordTotal,
            'pageSize' => $pageSize,
        ];

        $this->apiReturn(200 , $res, '');
    }

    /**
     * 测温记录汇总
     * <AUTHOR>
     * @date 2020/04/10
     */
    public function getOrderTemperatureRecordSummary()
    {
        $aid = I('post.aid', 0, 'intval');
        $begin = I('post.begin', '', 'strval');
        $end = I('post.end', '', 'strval');
        $recordType = I('post.record_type', -1, 'intval');
        $type = I('post.type', -1, 'intval');
        $export = I('post.export', 0, 'intval');
        $showUnusuaTemp = I('post.show_unusual_temp', 1, 'intval');//0不显示异常温度,1可以显示异常温度

        if (!$aid || !$begin || !$end) {
            $this->apiReturn(204, [], '参数错误');
        }
        $diffDays = ceil((strtotime($end)-strtotime($begin)) / 86400);
        if ($diffDays > 31) {
            $this->apiReturn(204, [], '抱歉，由于数据量大，汇总时间段不能超过1个月');
        }

        $orderTemperatureRecordModel = new OrderTemperatureRecord();
        $recordArr = $orderTemperatureRecordModel->selectOrderTemperatureRecord($aid, $begin, $end, 'tnum,ordernum,temperature,img,type,location', $recordType, $type, $showUnusuaTemp);

        $total = 0;
        $unNormal = 0;
        $normal = 0;

        foreach ($recordArr as $item) {
            if ($item['type'] == 0) {
                $normal += $item['tnum'];
            }
            if ($item['type'] == 1) {
                $unNormal += $item['tnum'];
            }
            $total += $item['tnum'];
        }

        $rate = $total == 0 ? 0 : round($unNormal/$total, 3) ?? 0;

        $res = [
            'total' => $total,
            'unNormal' => $unNormal,
            'unNormalRate' => ($rate * 100) . '%',
            'normal' => $normal,
        ];

        if ($export) {
            // 到处excel逻辑
            $this->_exportOrderTemperatureRecord($recordArr, $begin, $end, $res);
            exit;
        }

        $this->apiReturn(200, $res, '');
    }

    /**
     * 测温excel记录导出
     * <AUTHOR>
     * @date 2020/04/11
     * @param $recordArr
     * @param $begin
     * @param $end
     * @param $summary
     */
    private function _exportOrderTemperatureRecord($recordArr, $begin, $end, $summary)
    {
        $orderNumArr = array_column($recordArr, 'ordernum');
        $orderModel = new OrderQuery();

        $orderArr = $orderModel->getOrderInfoByMulti($orderNumArr, 'ordernum', 'ordernum,ordername,ordertel,personid', true);

        $exportData[] = [
            '统计时间段：',
            $begin,
            '至',
            $end,
        ];
        $exportData[] = [
            '总过检人数：',
            $summary['total'] . '人',
            '异常体温人数：',
            $summary['unNormal'] . '人',
            '正常体温人数：',
            $summary['normal'] . '人',
            '订单总共' . count($orderArr) . '笔',
        ];
        $exportData[] = [
            '订单号',
            '订单总人数',
            '人员姓名',
            '异常/正常',
            '体温',
            '电话',
            '身份证',
            '抓拍照片链接(可复制链接到浏览器下载)',
            '归属地(根据身份证/手机号解析)'
        ];

        foreach ($recordArr as $item) {
            $exportData[] = [
                'ordernum' => $item['ordernum'] . "\t",
                'tnum'     => $item['tnum'],
                'ordername'=> $orderArr[$item['ordernum']]['ordername'] ?? '--',
                'type'     => $item['type'] == 0 ? '温度正常' : '温度异常',
                'temperature' => $item['temperature'],
                'ordertel' => $orderArr[$item['ordernum']]['ordertel'] ?? '--',
                'personid' => $orderArr[$item['ordernum']]['personid'] ?? '--',
                'img' => $item['img'],
                'location' => $item['location'] ?? ''
            ];
        }

        $excel    = new \Library\SimpleExcel();
        $filename = '所有人员名单 ' . $begin . ' 至 ' . $end;
        $excel->addArray($exportData);
        $excel->generateXML($filename);
        exit;
    }


    /**
     * 获取异常温度展示配置
     * User: lanwanhui
     * Date: 2022/6/20
     */
    public function getTemperatureConfig()
    {
        $loginInfo  = $this->getLoginInfo();
        $sid        = $loginInfo['sid'];

        $orderTemperatureRecordModel = new OrderTemperatureRecord();
        $config  = $orderTemperatureRecordModel->getTemperatureConfig($sid);

        $data = [
            'show_unusual_temp' => $config['show_unusual_temp'] ??  1,
        ];

        $this->apiReturn(200, $data, '');

    }


    /**
     * 设置异常温度展示配置
     * User: lanwanhui
     * Date: 2022/6/20
     */
    public function setTemperatureConfig()
    {

        $showUnusuaTemp = I('post.show_unusual_temp', -1, 'intval');
        if (!in_array($showUnusuaTemp, [0, 1])) {
            $this->apiReturn(204, [], '参数错误');
        }

        $loginInfo  = $this->getLoginInfo();
        $sid        = $loginInfo['sid'];
        $memberId   = $loginInfo['memberID'];

        $orderTemperatureRecordModel = new OrderTemperatureRecord();
        $config  = $orderTemperatureRecordModel->getTemperatureConfig($sid);

        if (!empty($config)) {
            $rs = $orderTemperatureRecordModel->saveTemperatureConfig($config['id'], $memberId, $showUnusuaTemp);
            if ($rs === false) {
                $this->apiReturn(204, [], '保存失败');
            }
        } else {
            $rs = $orderTemperatureRecordModel->addTemperatureConfig($sid, $memberId, $showUnusuaTemp);
            if (!$rs) {
                $this->apiReturn(204, [], '增加失败');
            }
        }

        $this->apiReturn(200, [], '设置成功');

    }
}

