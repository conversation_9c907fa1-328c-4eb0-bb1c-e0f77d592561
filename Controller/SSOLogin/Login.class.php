<?php
/**
 * 单点登录
 * <AUTHOR>
 * @date 2021-10-29
 */

namespace Controller\SSOLogin;

use Library\Controller;
use Library\Cache\Cache as Cache;

class Login extends Controller
{

    public function __construct()
    {
    }

    /**
     * 回调处理
     *
     * <AUTHOR>
     * @date 2021-10-29
     */
    public function ssoLoginCallback()
    {
        $token   = I('get.token');
        $backUrl = I('get.back_url', '');
        if (!$token) {
            pft_log('sso/login/error', 'token错误');
            $backUrl = SSO_DOMAIN . "/#/login?login_error=true";
            header("Location: " . $backUrl);
            exit;
        }

        //token解析获取用户基础信息
        $ssoClient = new \Business\Member\SSOLogin();
        $callRes   = $ssoClient->getMemberByToken($token);
        if ($callRes['code'] != 200) {
            pft_log('sso/login/error', json_encode($callRes, JSON_UNESCAPED_UNICODE));
            $backUrl = SSO_DOMAIN . "/#/login?login_error=true";
            header("Location: " . $backUrl);
            exit;
        }

        $extraInfo = [];
        if ($callRes['data']['metadata']['linkIds']) {
            $extraInfo['sso_metadata_link_ids'] = json_encode($callRes['data']['metadata']['linkIds']);
        }

        //写session
        $sessionBiz = new \Business\Member\Session();
        $sessionRes = $sessionBiz->loginMemberId($callRes['data']['id'], 'pc', false, 0, 0, $extraInfo);
        if ($sessionRes['code'] != 200) {
            pft_log('sso/login/error', json_encode($sessionRes, JSON_UNESCAPED_UNICODE));
            $backUrl = SSO_DOMAIN . "/#/login?login_error=true";
            header("Location: " . $backUrl);
            exit;
        }

        $loginData = $sessionRes['data'];
        //是否是订单模式(应用中心需要判断)
        $api    = new \Business\JavaApi\Member\MemberConfig();
        $cfgRes = $api->getConfigWithMemberId($loginData['sid']);

        $controller = new \Library\Tools\BusinessCache();
        $bizData    = [];
        if ($cfgRes['code'] == 200 && $cfgRes['data']) {
            if (($cfgRes['data']['fee_platform'] || $cfgRes['data']['fee_code']) && $loginData['sdtype'] == 0) {
                $bizData['order_mode'] = 1;
            } else {
                $bizData['order_mode'] = 0;
            }
        } else {
            $bizData['order_mode'] = 0;
        }

        //设置登录来源
        $bizData['source'] = 'platform';
        $controller->setBusinessCache($bizData, $loginData['memberID']);

        if ($_SERVER['HTTP_HOST'] == '9117you.12301.cc') {
            if ($_SERVER['HTTP_REFERER'] != 'http://9117you.12301.cc/new/d/dlogin.html') {
                $url = 'http://fx.9117you.cn';
                echo "<script>window.parent.location='" . $url . "';</script>";
                exit;
            }
        }

        //分销专员session设置
        (new \Business\MultiDist\Member())->platformLoginSessionSet($loginData['sid']);

        //子商户处理跳转到订单查询
        $defaultBackUrl = ($loginData['dtype'] == 18) ? '/orderquery.html' : 'home.html';

        $backUrl = $backUrl ? urldecode($backUrl) : $defaultBackUrl;
        if ($backUrl) {
            $backUrl = MY_DOMAIN . trim($backUrl, '/');
        }

        //todo 前端用于单点登录请求头部导航 有问题问华健
        if (strpos($backUrl, '?') === false) {
            $backUrl .= '?bylogin=1';
        }else{
            $backUrl .= '&bylogin=1';
        }
        header("Location: " . $backUrl);
        exit;
    }


}
