<?php
/**
 * 景区人脸识别配置管理员操作控制器
 *
 * <AUTHOR>
 * @date 2018-10-12
 *
 * @editor zhujb
 * @date 2019-11-15  （修改继承父类，将权限控制统一归到zd下）
 */
namespace Controller\TerminalManage;

use Business\JsonRpcApi\ScenicLocalService\RedisHandle;
use Business\TerminalManage\TerminalFace;
use Library\Business\Uface\OpenPlatform;
use Library\Tools\Helpers;
use Model\Terminal\FaceCompare;
use Library\Business\Uface\MachineManage;
use Model\Product\Land;
use Library\Cache\Cache;
use Library\Tools\YarClient;

class Face extends BaseTerminalManage {

    public $_faceModel   = '';
    public $_deviceLib   = '';
    public $_memberId    = '';
    public $_yarClient   = '';

    public function __construct() {
        $loginInfo = $this->getLoginInfo();
        $this->_faceModel   = new FaceCompare();
        $this->_deviceLib = new MachineManage();
        $this->_memberId   = $loginInfo['memberID'];
    }


    /**
     * 管理员获取所有景区人脸平台
     * <AUTHOR>
     * @date   2018-10-17
     *
     * @return void
     */
    public function getFacePlatform()
    {
        $lid        = I('post.lid', 0);
        $page       = I('post.page', 1, 'intval');
        $pageSize   = I('post.page_size', 15, 'intval');
        if(strlen($lid) == 16) {
            //设备号查询景区id
            $lid  = $this->_faceModel->checkFaceDeviceExist($lid, 'lid');
            if(empty($lid)) {
                $this->apiReturn(200, [], 'success');
            }
        }
        if($pageSize >= 30) {
            $pageSize = 30;
        }

        $data  = $this->_faceModel->getFacePlatformList($lid,
            'id,face_platform,groupname,groupid,lid,score1,score2,score3,quality_control,distance,annual_medium_time,order_medium_time,annual_nomedium_time,order_nomedium_time,idcard_compare_time,staff_nomedium_time,biopsy,ext_info,reverse_face_time',
            $page, $pageSize);

        // 获取group id 对应的设备信息
        $groupIdArr = array_unique(array_column($data['list'], 'groupid'));
        $deviceRpcRes = $this->yarCall('Device/Device/getDeviceByGroupId', [$groupIdArr, 3, '']);
        if ($deviceRpcRes['code']!=200) {
            $this->apiReturn(204, [], 'error');
        }

        $deviceRes = $deviceRpcRes['res']['data'];

        /*$deviceResArr = [];
        foreach ($deviceRes as $item) {
            $deviceResArr[$item['groupid']] = $item;
        }*/

        foreach ($data['list'] as $key => $value) {
            $data['list'][$key]['biopsy']        = !($value['biopsy'] == 0);
            $data['list'][$key]['device_key']    = $deviceRes[$value['groupid']]['device_key'] ?? '';
            $data['list'][$key]['online_status'] = isset($deviceRes[$value['groupid']]) ? $deviceRes[$value['groupid']]['status'] == 1 ? '在线' : '离线' : '还未绑定';
            $extInfo                             = $data['list'][$key]['ext_info'] ? json_decode($data['list'][$key]['ext_info'],
                true) : [];
            $data['list'][$key]['ext_info']      = [
                'notice_info' => $extInfo['notice_info'] ?? '',
                'sms_notice'  => $extInfo['sms_notice'] ?? 0,
            ];
        }

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 设置景区的人脸平台，目前只支持百度和uface
     * <AUTHOR>
     * @date   2018-10-12
     *
     * @return void
     */
    public function setFacePlatform()
    {
        $lid                = I('post.lid', 0, 'intval');
        $platform           = I('post.platform', 1, 'intval');
        $score1             = I('post.score1', 60, 'intval');                   //有介质
        $score2             = I('post.score2', 80, 'intval');                   //无介质阀值
        $score3             = I('post.score3', 60, 'intval');                   //人证比对阀值
        $distance           = I('post.distance', 80, 'intval');                 //人脸识别距离(CM)
        $annualMediumTime   = I('post.annual_medium_time', 2, 'intval');         //年卡有介质验证时间间隔(秒)
        $orderMediumTime    = I('post.order_medium_time', 2, 'intval');          //订单有介质验证时间间隔(秒)
        $idcardCompareTime  = I('post.idcard_compare_time', 2, 'intval');       //人证比对时间间隔(秒)
        $annualNomediumTime = I('post.annual_nomedium_time', 600, 'intval');     //年卡无介质验证时间间隔(秒)
        $orderNomediumTime  = I('post.order_nomedium_time', 600, 'intval');      //订单无介质验证时间间隔(秒)
        $staffNomediumTime  = I('post.staff_nomedium_time', 60, 'intval');      //员工无介质验证时间间隔(秒)
        $biopsy             = I('post.biopsy', 'false'); //是否开启人脸活体检测  false关闭 true开启
        $reverseFaceTime    = I('post.reverse_face_time',300,'intval');        //反向采集人脸后过闸时间间隔
        $qualityControl     = I('post.quality_control','NONE','strval');        //图片质量控制

        if($score1<40 || $score2<40 || $score3<40) {
            $this->apiReturn(201, [], '阀值不能小于40分');
        }

        if($score1>90 || $score2>90 || $score3>90) {
            $this->apiReturn(201, [], '阀值不能大于90分');
        }
        
        if (!is_numeric($annualMediumTime) || !is_numeric($orderMediumTime)
            || !is_numeric($idcardCompareTime) || !is_numeric($annualNomediumTime)
            || !is_numeric($orderNomediumTime) || !is_numeric($staffNomediumTime)
            || !is_numeric($reverseFaceTime)) {
            $this->apiReturn(201, [], '输入时间间隔无效，必须是正整数');
        }

        $qualityControl = strtoupper($qualityControl);
        if (!in_array($qualityControl, ['NONE','LOW','NORMAL','HIGH'])) {
            $this->apiReturn(201, [], '图片质量控制值错误');
        }

        $landModel       = new Land('slave');
        $landInfo        = $landModel->getLandInfo($lid, false, 'terminal,title,apply_did');//根据lid取终端号
        if (empty($landInfo)) {
            $this->apiReturn(201, [], '景区不存在');
        }

        $terminalId      = $landInfo['terminal'];
        $nowTerminal     = $landModel->getNowTerminal($terminalId);
        if($nowTerminal){
            //如果有合并终端的，取合并后的终端
            $terminalId = $nowTerminal;
        }

        if (empty($terminalId)) {
            $this->apiReturn(201, [], '景区终端号有误,请确认景区信息');
        }

        if (empty($lid) || empty($platform)) {
            $this->apiReturn(201, [], '请选择景区和人脸平台');
        }

        $faceSetId = '';
        $checkInfo = $this->_faceModel->checkFacePlatformByLid($lid);
        // 使用开放平台创建人脸集合
        if ($platform == 2) {
            $openLib = new OpenPlatform();
            $faceSet = $openLib->createFaceSet();
            if ($faceSet['code'] == 'OP_SUS-1000') {
                $faceSetId = $faceSet['data']['facesetId'];
            } else {
                $this->apiReturn(400, [], $faceSet['msg']);
            }
        }

        $res   = $this->_faceModel->setFacePlatform(
            $terminalId, $landInfo['title'], $lid, $platform, $landInfo['apply_did'],
            $checkInfo, $faceSetId, $score1, $score2, $score3,
            $distance, $annualMediumTime, $orderMediumTime, $idcardCompareTime, $annualNomediumTime,
            $orderNomediumTime, $staffNomediumTime, $biopsy,$reverseFaceTime, $qualityControl
        );
        $redis = Cache::getInstance('redis');
        $redis->rm("land:faceplatform:".$terminalId);
        //删除下云票务项目的ts下的rediskey
        $scenicJsonApi = new RedisHandle();
        $scenicJsonApi->deleteTsRedisFaceCacheService([$terminalId]);
        // if (($platform == 2 || $platform == 3) && ((!empty($checkInfo[$terminalId]) && $checkInfo[$terminalId]['score3'] != $score3) || empty($checkInfo))) {
        //     //本地人证比对阀值推送给闸机
        //     $physicsNoPushBiz = new \Business\Product\TerminalProduct();
        //     $physicsNoPushBiz->saveRecording($landInfo['apply_did'], $score3, 10, 0, $terminalId);
        // }
        //百度人脸在newzd设置后同步更新下pft_ts.pft_face_platform
        $params = [
            //0-关闭 1-百度 2-支付宝
            'face_platform' => 1,
            'apply_did' => $landInfo['apply_did'],
            /*'score1' => $score1,//有介质阀值
            'score2' => $score2,//无介质阀值
            'score3' => $score3, //人证比对阀值
            'annual_medium_time' => $annualMediumTime,//年卡有介质验证时间间隔(秒)
            'order_medium_time' => $orderMediumTime,//订单有介质验证时间间隔(秒)
            'idcard_compare_time' => $idcardCompareTime,//人证比对时间间隔(秒)
            'annual_nomedium_time' => $annualNomediumTime,//年卡无介质验证时间间隔(秒)
            'order_nomedium_time' => $orderNomediumTime,//订单无介质验证时间间隔(秒)
            'staff_nomedium_time' => $staffNomediumTime,//员工无介质验证时间间隔(秒)
            'reverse_face_time' => $reverseFaceTime,//反向采集人脸后过闸时间间隔
            'quality_control' => $qualityControl,//百度人脸图片质量控制*/
        ];
        $ret = Helpers::callScenicRpc('Face/FacePlatform/setFacePlatform', [$params]);
        if ($ret['code'] != 200) {
            $this->apiReturn(400, [], '同步更新pft_ts.pft_face_platform时出错：' . $ret['msg']);
        }
        if ($res !== false) {
            $this->apiReturn(200, [], '人脸平台设置成功');
        } else {
            $this->apiReturn(400, [], '设置失败');
        }
    }

    /**
     * 设置人脸设备扩展信息
     * <AUTHOR>
     * @date   2020-11-11
     *
     * @return void
     */
    public function setFacePlatformSmsExtInfo()
    {
        $id              = I('post.id', 0);
        $isOpenSmsNotice = I('post.sms_notice', 0);
        $noticeInfo      = I('post.notice_info', '');
        if (!$id) {
            $this->apiReturn(204, [], '参数有误');
        }
        if ($isOpenSmsNotice != 0 && !$noticeInfo) {
            $this->apiReturn(204, [], '请输入提示');
        }
        $terminalFaceBiz = new TerminalFace();
        $extInfo         = [
            'sms_notice'  => $isOpenSmsNotice,
            'notice_info' => $noticeInfo,
        ];
        $result          = $terminalFaceBiz->editGroupExtInfo($id, $extInfo);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统异常');
        }
    }


    /**
     * 添加人脸设备
     * <AUTHOR>
     * @date   2018-10-12
     * 
     * @return void
     */
    public function addDevice() {
        $deviceId         = I('post.device_id', false);
        $lid              = I('post.lid', 0, 'intval');
        $deviceName       = I('post.device_name');
        $repeatCheckHour  = I('post.repeat_check_hour', 0, 'intval');

        if (empty($deviceId) || empty($lid) || empty($deviceName)) {
            $this->apiReturn(201, [], '请选择景区id、设备号和设备名称');
        }

        $checkDevice  = $this->_faceModel->checkFaceDeviceExist($deviceId);
        if($checkDevice){
            $this->apiReturn(400, [], '设备号已存在');
        }

        $createResult = $this->_deviceLib->create($deviceId, $deviceName);

        if($createResult['code'] =='GS_SUS300') {
            $landModel       = new Land('slave');
            $landInfo        = $landModel->getLandInfo($lid, false, 'terminal,apply_did');//根据lid取终端号
            $terminalId      = $landInfo['terminal'];
            $nowTerminal     = $landModel->getNowTerminal($terminalId);
            if($nowTerminal){
                //如果有合并终端的，取合并后的终端
                $terminalId = $nowTerminal;
            }
            $res  = $this->_faceModel->addFaceDevice($deviceId, $lid, $deviceName, $landInfo['apply_did'], $terminalId, $repeatCheckHour);
            $redis = Cache::getInstance('redis');
            $redis->rm("land:device:".$terminalId);
        } else {
            $this->apiReturn(400, [], $createResult['msg']);
        }

        if($res) {
            $this->apiReturn(200, [], '设备设置成功');
        } else {
             $this->apiReturn(400, [], '设置失败');
        }
    }

    /**
     * 删除人脸设备
     * <AUTHOR>
     * @date   2018-12-06     *
     *
     * @return
     */
    public function deleteDevice() {
        $deviceId   = I('post.device_id', false);
        $terminal   = I('post.terminal', 0, 'intval');
        $onlyDb     = I('post.onlyDb', 1, 'intval');//只删数据库不删uface平台

        if (empty($deviceId) || empty($terminal)) {
            $this->apiReturn(201, [], '请选择终端号和设备号');
        }

        $checkDevice  = $this->_faceModel->checkFaceDeviceExist($deviceId);
        if(!$checkDevice) {
            $this->apiReturn(400, [], '设备号不存在');
        }

        if(empty($onlyDb)) {
            $this->_deviceLib = new MachineManage();
            $result = $this->_deviceLib->deleteDevice($deviceId);
        }
        pft_log('orderface/op_log', 'delete_log:opid = '. $this->_memberId . 'terminal=' . $terminal . 'deviceId=' .$deviceId. 'result='. json_encode($result, JSON_UNESCAPED_UNICODE), 3);
        if($result['code'] =='GS_SUS302' || $onlyDb) {
            $res   = $this->_faceModel->deleteFaceDevice($deviceId, $terminal);
            $redis = Cache::getInstance('redis');
            $redis->rm('uface:device'.$terminal);
            $this->apiReturn(200, [], '删除成功');
        } else {
            $this->apiReturn(400, [], $result['msg']);
        }
    }

    /**
     * 删除人脸平台，测试用
     * <AUTHOR>
     * @date   2019-12-06     *
     *
     * @return
     */
    public function deleteGroup()
    {
        $aid           = I('post.aid', 0, 'intval');
        $facePlatform  = I('post.face_platform', 0, 'intval');

        if (empty($aid)) {
            $this->apiReturn(201, [], '参数错误');
        }

        $res = $this->_faceModel->deleteGroup($aid, $facePlatform);
        if ($res) {
            $this->apiReturn(200, [], '删除成功');
        } else {
            $this->apiReturn(201, [], '删除失败');
        }
    }

    /**
     * 修改pft_land_device_map表，测试用
     * <AUTHOR>
     * @date   2018-12-06     *
     *
     * @return
     */
    public function updateDevice() {
        $id                = I('post.id');//表id
        $terminal          = I('post.terminal' , false);
        $lid               = I('post.lid', false);
        $deviceKey         = I('post.deviceKey', false);
        $applyDid          = I('post.apply_did', false);
        $deviceName        = I('post.device_name', false);
        $repeat_check_hour = I('post.repeat_check_hour', false);

        if (empty($id)) {
            $this->apiReturn(201, [], 'id不能空');
        }

        $res   = $this->_faceModel->updateFaceDevice($id, $terminal, $lid, $deviceKey, $applyDid, $deviceName, $repeat_check_hour);

        if($res) {
            $this->apiReturn(200, [], '更新成功');
        }
    }

    /**
     * 设置人脸设备
     * <AUTHOR>
     * @date   2018-10-17     *
     *
     * @return
     */
    public function getDevice() {
        $lid        = I('post.lid', 0, 'intval');
        $page       = I('post.page', 1, 'intval');

        if(empty($lid)){
            $this->apiReturn(201, [], '请输入景区id');
        }
        $data  = $this->_faceModel->getFaceDeviceByLid($lid, $page);

        $this->apiReturn(200, $data, 'success');

    }


    /**
     * 人脸迁移，一代迁移二代
     * <AUTHOR>
     * @date   2018-10-17     *
     *
     * @return
     */
    public function moveFace()
    {
        $lid = I('get.lid', 0, 'intval');

        if(empty($lid)) {
            $this->apiReturn(201, [], '请输入景区id');
        }

        $faceBiz =  new \Business\Order\Face();
        $result  = $faceBiz->moveFace($lid);
        if($result['code'] == 200) {
            $this->apiReturn($result['code'], [], '景区id'.$lid.$result['msg']);
        } else {
            $this->apiReturn($result['code'], [], '景区id'.$lid.'人脸迁移失败'.$result['msg']);
        }
    }

    private function yarCall($method, $data)
    {
        if(empty($this->_yarClient)) {
            $this->_yarClient = new YarClient('face');
        }
        return $this->_yarClient->call($method, $data);
    }

}