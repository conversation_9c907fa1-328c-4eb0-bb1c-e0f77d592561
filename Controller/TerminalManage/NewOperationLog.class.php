<?php
/**
 * 自助机操作日志记录
 * Created by PhpStorm.
 * Author: zhujb
 * Date: 2018/6/22
 * Time: 10:57
 */

namespace Controller\TerminalManage;

use Business\JsonRpcApi\ScenicLocalService\LogAgg;
use Model\TerminalManage\ErrorLog;
use Model\Member\Member;

class NewOperationLog extends BaseTerminalManage
{

    public $content = [1=>'下单成功',2=>'支付成功',3=>'支付失败',4=>'取消订单',5=>'订单验证',6=>'订单打印',7=>'订单打印成功',8=>'订单打印失败',9=>'订单打印QR码失败',10=>'订单重打印'];

    /**
     * 获取操作日志列表
     * @Author: zhujb
     * 2018/7/16
     */
    public function getNewOperationLogList()
    {
        $terminalNo = I('post.terminalNo','','strval');
        $page = I('post.page',1,'intval');
        $orderNo = I('post.orderNo','','strval');

        if (empty($terminalNo) && empty($orderNo)) {
            $this->apiReturn(200);
        }

        $newOperationLogModel = new LogAgg();
        $res = $newOperationLogModel->getOperationLogList($terminalNo,$orderNo,$page);
        if(empty($res) && $orderNo) {
            //如果是合并付款订单，需要根据CMB订单号查询
            $payMentModel = new \Model\Order\Payment();
            $tradeInfo    = $payMentModel->getCombileLogByOrdernum($orderNo);
            if($tradeInfo['tradeid']) {
                $orderNo = $tradeInfo['tradeid'];
                $res     = $newOperationLogModel->getOperationLogList($terminalNo, $orderNo, $page);
            }
        }
        $list['total']     = $newOperationLogModel->getOperationLogCount($terminalNo, $orderNo);
        $list['page_size'] = 10;

        $memberModel = new Member();
        foreach ($res as $k => $v) {
            // 获取操作人
            $memberInfo = $memberModel->getMemberInfo($v['operator'],'id','dname,dtype');
            if ($memberInfo['dtype'] == 6) {
                $res[$k]['operator'] = $memberInfo['dname'];
                $parentInfo = $memberModel->getMemberInfo($v['aid'],'id','dname');
                $res[$k]['aid'] = $parentInfo['dname'];
            }else {
                $res[$k]['operator'] = $memberInfo['dname'];
                $res[$k]['aid'] = $memberInfo['dname'];
            }

            $res[$k]['content'] = $this->content[(int)$v['content']];
        }

        $list['list'] = $res;

        $this->apiReturn(200,$list);
    }

    /**
     * 合并付款交易记录查询
     * @Author: zhujb
     * 2018/7/24
     */
    public function orderPayCombine()
    {
        $orderNum = I('post.orderNum','','strval');
        $tradeId = I('post.tradeId','','strval');

        if (empty($orderNum) && empty($tradeId)) {
            $this->apiReturn(200,[]);
        }

        $payMentModel = new \Model\Order\Payment();
        // 如果是只有交易号
        if ($tradeId) {
            $orderPayCombineArr = $payMentModel->getCombileLogByTradeId($tradeId);
        } else {
        // 只有订单号  或  两者都有
            $tradeInfo = $payMentModel->getCombileLogByOrdernum($orderNum);
            $orderPayCombineArr = $payMentModel->getCombileLogByTradeId($tradeInfo['tradeid']);
        }

        $tradeRecordModel = new \Model\Finance\TradeRecord();

        foreach ($orderPayCombineArr as $k => $v) {
            $orderPayCombineArr[$k]['payDetail'] = $tradeRecordModel->getOrderAlipay($v['ordernum']);
        }

        $this->apiReturn(200,$orderPayCombineArr);
    }

    /**
     * 获取携程操作日志
     * <AUTHOR>
     * @date 2018-07-17
     *
     */
    public function getCtripOrderLog(){
        $ctripOrderId     = I('post.ctriporderid', "", 'strval'); //携程订单号
        $orderNum         = I('post.ordernum', "", 'strval');     //票付通订单号

        $Model = new \Model\Ota\CtripTicketMachine;
        $res = $Model ->getOrderInfo($ctripOrderId,$orderNum);

        if($res === false){
            $this->apiReturn(400, [], '查询出错，请稍后再试');
        }

        foreach ($res as $k => $v) {
            $res[$k]['create_time'] = date('Y-m-d h:i:s',$v['create_time']); //创建时间
            $res[$k]['update_time'] = date('Y-m-d h:i:s',$v['update_time']); //创建时间
        }

        $this->apiReturn(200, $res, "查询成功");
    }

    /**
     * 获取错误日志
     * @Author: zhujb
     * 2018/9/4
     */
    public function getErrorLog()
    {
        $source = I('post.source','','strval');
        $beginTime = I('post.begin_time','','strval');
        $endTime = I('post.end_time','','strval');
        $page = I('post.page',1,'intval');
        $pageSize = I('post.page_size',10,'strval');
        $errorInfo = I('post.errorInfo','','strval');

        if (empty($source) && empty($beginTime) && empty($endTime) && empty($errorInfo)) {
            $this->apiReturn(200);
        }
        $jsonRpcIns = new LogAgg();
        $result     = $jsonRpcIns->getMachineErrorLogs($source, $errorInfo, $beginTime, $endTime, $page, $pageSize);

//        $errorModel = new ErrorLog();
//        $res = $errorModel->getErrorLogList($source, $errorInfo, $beginTime, $endTime, $page, $pageSize);
//        $count = $errorModel->getErrorLogCount($source, $errorInfo, $beginTime, $endTime);

//        $list['list'] = $res;
//        $list['total'] = $count;
//        $list['totalPage'] = ceil($count/$pageSize);

        $this->apiReturn(200, $result['data'], $result['msg']);
    }
}