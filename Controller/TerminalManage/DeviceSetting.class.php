<?php
/**
 * 设备配置管理
 */

namespace Controller\TerminalManage;

use Business\Face\AliFaceBiz;
use Library\Controller;
use Library\Tools\YarClient;

class DeviceSetting extends Controller
{
    //闸机支付宝人脸配置相关
    public function getAliFaceConfig()
    {
        $deviceKey = I('device_key', '', 'strval,trim');
        if (empty($deviceKey)) {
            $this->apiReturn(401, '设备key不能为空');
        }
        //获取签到配置的商家id
        $yarClient = new YarClient('device');
        $signConfig = $yarClient->call('Device/Device/getDeviceSignSetting', ['device_key' => $deviceKey]);
        $sid = 0;
        if ($signConfig['code'] == 200) {
            $sid = (int)$signConfig['res']['data']['aid'];
        }
        if (!$sid) {
            $this->apiReturn(401, '请检查闸机签到是否配置了商家信息');
        }
        $params = ['apply_did' => $sid];
        $faceGroupList = AliFaceBiz::getInstance()->faceLidBindQuery($params);
        $ret = [
            'app_id' => '2021004182638423',//先写死
            'list' => $faceGroupList['data'] ?? [],
        ];
        $this->apiReturn(200, $ret);
    }
}