<?php
/**
 * 终端配置相关
 * User: zhujb
 * Date: 2018-06.11
 * Time: 16:41
 */

namespace Controller\TerminalManage;

use Model\Member\Member;
use Business\Site\SiteManage;
use Model\TerminalManage\TerminalScenicSpotR;
use Model\TerminalManage\TerminalSelfHelp;
use Model\TerminalManage\TerminalClientConfig;
use Model\TerminalManage\TerminalAccountR;
use Model\TerminalManage\TerminalSysConfig;
use Model\TerminalManage\TerminalSysServicesConfig;
use Model\TerminalManage\TicketConfig;
use Process\Report\ChangeShiftsProcess;
use Business\JavaApi\TicketApi;
use Business\TerminalManage\ProductConfig;

class Terminal extends BaseTerminalManage
{
    /**
     * 获取当前用户的终端列表
     * @Author: zhujb
     */
    public function getTerminalList()
    {
        $page       = I('post.page', 1, 'intval');
        $type       = I('post.type', '', 'trim');
        $terminalNo = I('post.TerminalNo', '', 'trim');
        $pageSize   = I('post.page_size', 10, 'intval');
        $searchAccount    = I('post.search_account', '', 'strval');

        $account = '';
        if (!in_array(strtolower($this->_memberInfo['account']), $this->_admin)) {
            $account = $this->_memberInfo['account'];
        }

        $terminalAccountModel  = new TerminalAccountR();
        $terminalSelfHelpModel = new TerminalSelfHelp();
        $data                  = $terminalSelfHelpModel->getTerminals($terminalNo, $type);

        $terminalNoArr = array_column($data, 'TerminalNo');
        $accountArr    = $terminalAccountModel->selectAccount($terminalNoArr);

        // 查找普通登录用户下 有哪些终端号
        $curTerminalNoList = [];
        foreach ($accountArr as $value) {
            if ($account == $value['UserName']) {
                array_push($curTerminalNoList, $value['TerminalNo']);
            }
        }

        $terminalList = [];
        if (!empty($curTerminalNoList)) {
            // 普通账号查询
            foreach ($data as $item) {
                if (in_array($item['TerminalNo'], $curTerminalNoList)) {
                    $terminalList[] = $item;
                }
            }
        } else {
            // 超级账户查询
            if (empty($account)) {
                if ($searchAccount) {
                    // 供应商编号搜索
                    foreach ($data as $item) {
                        if ($searchAccount == $item['SupplierNO']) {
                            $terminalList[] = $item;
                        }
                    }
                } else {
                    $terminalList = $data;
                }
            }
        }

        $start            = ($page - 1) * $pageSize;
        $list['list']     = array_slice($terminalList, $start, $pageSize);
        $list['total']    = count($terminalList);
        $list['page_size'] = $pageSize;

        $this->apiReturn(200, $list);
    }

    /**
     * 初始化部分终端信息
     * @Author: zhujb
     * @param string $terminalNo 终端号
     * @return mixed
     */
    private function _initTerminal($terminalNo)
    {
        if (empty($terminalNo)) {
            return [];
        }

        // 自助终端号配置
        $data['selfHelp']['TerminalNo']   = $terminalNo;    // 终端号
        $data['selfHelp']['SupplierName'] = I('post.SupplierName', '', 'trim');  // 供应商名称
        $data['selfHelp']['SupplierNO']   = I('post.SupplierNO', '', 'trim');    // 供应商编号
        $data['selfHelp']['Remarks']      = I('post.Remarks', '', 'trim');       // 备注
        $data['selfHelp']['TType']        = I('post.TType', 1, 'intval');        // 终端类型
        $data['selfHelp']['SharedTType']  = I('post.SharedTType', '', 'trim');   // 共享终端类型
        $data['selfHelp']['TrDateTime']   = date('Y-m-d H:i:s', time());
        // 终端账号信息配置
        $data['account']['TerminalNo']    = $terminalNo;
        $data['account']['UserName']      = $this->_memberInfo['account'];             // 当前登录的用户名
        $data['account']['Remarks']       = '';                                        // 当前用户的备注
        $data['account']['IsMainAccount'] = 0;                                         // 当前用户是否为主账号
        $data['account']['token']         = $this->_uuid();                            // 令牌
        $data['account']['TrDateTime']    = date('Y-m-d H:i:s', time());
        // 系统配置默认
        $data['sysConfig']['TerminalNo']         = $terminalNo;
        $data['sysConfig']['PlatformAccount']    = I('post.SupplierNO', '', 'trim');        // 平台服务账户
        $data['sysConfig']['OrderValidateUrl']   = 'http://my.12301.cc/module/zj_terminal/New_zd_terminal.php';  // 订单验证地址
        $data['sysConfig']['OrderValidateIP']    = '************,2347,************,5555';                        // TCP验证地址
        $data['sysConfig']['IDCardTimeInterval'] = 800;                                                          // 身份证扫描时间间隔
        $data['sysConfig']['MainScreenHeight']   = 955;                                                          // 主屏幕高
        $data['sysConfig']['IDCardType']         = 1;                                                            // ID卡类型
        $data['sysConfig']['IDCardPosition']     = 2;                                                            // ID卡显示位置：1左边，2右边
        $data['sysConfig']['FooterMessage']      = '技术支持：票付通团队                     400-99-22301                     www.12301.cc';   // 底部信息
        $data['sysConfig']['PrinterPages']       = 1;                                                            // 打印页数
        $data['sysConfig']['IphoneSysPwd']       = '12301';                                                      // 系统设置密码
        $data['sysConfig']['Lookup']             = 1;                                                            // 小票抬头
        $data['sysConfig']['TrDateTime']         = date('Y-m-d H:i:s', time());
        // 服务配置默认数据
        $data['serviceConfig']['TerminalNo']              = $terminalNo;
        $data['serviceConfig']['OpenOrderPrint']          = 1;                                                            // 打开订单打印服务(1打开、0关闭)
        $data['serviceConfig']['OpenOrderValidate']       = 0;                                                            // 打开取票订单验证服务(1打开、0关闭)
        $data['serviceConfig']['OpenQRCodeTickets']       = 0;                                                            // 打开二维码取票服务(0关闭,1凭证号+二维码，2凭证号，3二维码)
        $data['serviceConfig']['OpenPhoneTickets']        = 1;                                                            // 打开手机号取票服务(1打开、0关闭)
        $data['serviceConfig']['OpenIDCardTickets']       = 1;                                                            // 打开身份证取票服务(1打开、0关闭)
        $data['serviceConfig']['OpenTicketsTimeSet']      = 0;                                                            // 打开取票时间设置(1打开、0关闭)
        $data['serviceConfig']['LastTicketsTimeInterval'] = 60;                                                           // 最后取票时间间隔，单位秒
        $data['serviceConfig']['CloseDisableWin']         = 'M';                                                          // 关闭禁用窗口
        $data['serviceConfig']['OpenSellTickets']         = 0;                                                            // 打开售票功能
        $data['serviceConfig']['SellingDistProducts']     = 0;                                                            // 是否销售分销产品
        $data['serviceConfig']['PaymentType']             = '1,2';                                                        // 支付：1支付宝支付、2微信支付
        $data['serviceConfig']['PayBefBrushIDCard']       = 0;                                                            // 支付前是否刷身份证
        $data['serviceConfig']['OpenTheTickets']          = 1;                                                            // 打开取票功能
        $data['serviceConfig']['PrintEntityTickets']      = 1;                                                            // 打印实体票
        $data['serviceConfig']['SendEleTickets']          = 0;                                                            // 发送电子门票
        $data['serviceConfig']['PaperCounter']            = 1;                                                            // 打开虚拟纸张计数器
        $data['serviceConfig']['OpenSellOrderValidate']   = 1;                                                            // 打开购票订单验证服务(1打开、0关闭)
        $data['serviceConfig']['OpenDisPackage']          = 0;                                                            // 打开订单是否显示套票(1打开、0关闭)
        $data['serviceConfig']['SaleTickets']             = 0;                                                            // 打开预售功能
        $data['serviceConfig']['TrDateTime']              = date('Y-m-d H:i:s', time());
        // 客户端默认配置
        $data['clientConfig']['TerminalNo']     = $terminalNo;
        $data['clientConfig']['OpenPageSetCmd'] = '88888888';                                                 // 页面设置命令
        $data['clientConfig']['CloseSystemCmd'] = '00000000';                                                 // 终端退出命令
        $data['clientConfig']['BannerUrl']      = 'http://static.12301.cc/ticket_machine/index.html';         // 终端退出命令
        $data['clientConfig']['TicketsGridNum'] = 4;                                                          // 取票列表显示数量
        $data['clientConfig']['SysCmd']         = 'L';                                                        // 系统命令
        $data['clientConfig']['TrDateTime']     = date('Y-m-d H:i:s', time());

        return $data;
    }

    /**
     * 添加终端
     * @Author: zhujb
     */
    public function setTerminal()
    {
        if (empty(I('post.TerminalNo', '', 'trim'))) {
            $this->apiReturn(400, [], '请填写终端号');
        }

        if (empty(I('post.SupplierName', '', 'trim'))) {
            $this->apiReturn(400, [], '请填写供应商名称');
        }

        if (empty(I('post.SupplierNO', '', 'trim'))) {
            $this->apiReturn(400, [], '请填写供应商编号');
        }

        $terminalNo = I('post.TerminalNo', '', 'trim');

        $terminalNo = convert_char($terminalNo);

        $terminalSelfHelpModel = new TerminalSelfHelp();
        $isUnique              = $terminalSelfHelpModel->checkTerminalUnique($terminalNo);

        if ($isUnique['code'] !== 200) {
            $this->apiReturn(400, [], '终端号已存在');
        }

        $data = $this->_initTerminal($terminalNo);
        if (empty($data)) {
            $this->apiReturn(400, [], '无数据');
        }

        // 保存数据
        $res = $terminalSelfHelpModel->addTerminal($data);
        if ($res['code'] == 200) {
            //添加完终端配置，为终端默认配置一个票，只有云票务，win自助机，安卓自助机才有配置默认票
            if (in_array($data['selfHelp']['TType'], [1, 5, 9])) {
                $ticket['TerminalNo']       = $terminalNo;
                $ticketConfigModel = new TicketConfig();
                //根据不同的终端类型配置不同的票
                switch($data['selfHelp']['TType']) {
                    case 1:
                        $ticket['PrinType']         = 4;
                        $ticket['TicketsPrintWay']  = 90;
                        $ticket['TicketJsonConfig'] = "[{\"child\":0,\"cname\":\"门票名称\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":1,\"font_type\":\"Regular\",\"name\":\"UUttitle\",\"printseat\":\"0\",\"qrcode\":\"0\",\"qrcodex\":240,\"qrcodey\":20,\"time\":\"0\",\"title\":\"\",\"choose\":false,\"x\":530,\"y\":513},{\"child\":0,\"cname\":\"人数\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":1,\"font_type\":0,\"name\":\"PeopleNumber\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":300,\"qrcodey\":16.33334,\"time\":\"0\",\"title\":\"人数：\",\"choose\":false,\"x\":530,\"y\":473},{\"child\":0,\"cname\":\"门市价(元)\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":1,\"font_type\":\"Regular\",\"name\":\"UUMprice_yuan\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":300,\"qrcodey\":20,\"time\":\"0\",\"title\":\"单价：\",\"choose\":false,\"x\":529,\"y\":433},{\"child\":0,\"cname\":\"订单号\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":1,\"font_type\":\"Regular\",\"name\":\"UUordernum\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":300,\"qrcodey\":20,\"time\":\"0\",\"title\":\"订单号：\",\"choose\":false,\"x\":530,\"y\":393},{\"child\":0,\"cname\":\"有效日期\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":1,\"font_type\":0,\"name\":\"UUendtime\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":240,\"qrcodey\":18.33334,\"time\":\"2\",\"title\":\"有效日期：\",\"choose\":false,\"x\":530,\"y\":313},{\"child\":0,\"cname\":\"凭证号\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":1,\"font_type\":\"Regular\",\"name\":\"UUcode\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":300,\"qrcodey\":20,\"time\":\"0\",\"title\":\"凭证号：\",\"choose\":false,\"x\":529,\"y\":353},{\"child\":0,\"cname\":\"凭证号\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":1,\"font_type\":0,\"name\":\"UUcode\",\"printseat\":\"0\",\"qrcode\":\"2\",\"qrcodex\":97.33334,\"qrcodey\":98.33334,\"time\":\"0\",\"title\":\"\",\"choose\":false,\"x\":553.3334,\"y\":246.333374},{\"child\":0,\"cname\":\"订单号\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":1,\"font_type\":\"Regular\",\"name\":\"UUordernum\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":150,\"qrcodey\":20,\"time\":\"0\",\"title\":\"订单号\",\"choose\":true,\"x\":157,\"y\":55},{\"child\":0,\"cname\":\"凭证号\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":1,\"font_type\":\"Regular\",\"name\":\"UUcode\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":150,\"qrcodey\":20,\"time\":\"0\",\"title\":\"凭证号\",\"choose\":true,\"x\":314,\"y\":54}]";
                        $ticket['PrintName']        = "COM1";
                        $ticket['TempType']         = 0;
                        $ticket['salerid']          = "";
                        $ticket['title']            = "";
                        $ticket['tid']              = "";
                        $ticket['ttitle']           = "";
                        $ticket['Printer']          = "LN";
                        $ticket['TicketHeight']     = 0;
                        $ticket['TicketWeight']     = 0;
                        break;
                    case 5:
                        $ticket['PrinType']         = 2;
                        $ticket['TicketsPrintWay']  = 0;
                        $ticket['TicketJsonConfig'] = "[{\"child\":0,\"cname\":\"门票名称\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":10,\"font_type\":\"Regular\",\"name\":\"UUttitle\",\"printseat\":\"0\",\"qrcode\":\"0\",\"qrcodex\":120,\"qrcodey\":15,\"time\":\"0\",\"title\":\"\",\"choose\":false,\"x\":422,\"y\":56},{\"child\":0,\"cname\":\"门市价(元)\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":8,\"font_type\":\"Regular\",\"name\":\"UUMprice_yuan\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":120,\"qrcodey\":15,\"time\":\"0\",\"title\":\"单价：\",\"choose\":false,\"x\":422,\"y\":76},{\"child\":0,\"cname\":\"人数\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":8,\"font_type\":\"Regular\",\"name\":\"PeopleNumber\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":121,\"qrcodey\":15,\"time\":\"0\",\"title\":\"人数：\",\"choose\":false,\"x\":422,\"y\":96},{\"child\":0,\"cname\":\"凭证号\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":10,\"font_type\":\"Regular\",\"name\":\"UUcode\",\"printseat\":\"0\",\"qrcode\":\"2\",\"qrcodex\":100,\"qrcodey\":100,\"time\":\"0\",\"title\":\"\",\"choose\":false,\"x\":425,\"y\":196},{\"child\":0,\"cname\":\"订单号\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":8,\"font_type\":\"Regular\",\"name\":\"UUordernum\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":120,\"qrcodey\":15,\"time\":\"0\",\"title\":\"订单号：\",\"choose\":false,\"x\":422,\"y\":136},{\"child\":0,\"cname\":\"凭证号\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":8,\"font_type\":\"Regular\",\"name\":\"UUcode\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":120,\"qrcodey\":15,\"time\":\"0\",\"title\":\"凭证号：\",\"choose\":false,\"x\":422,\"y\":156},{\"child\":0,\"cname\":\"总门市价(元)\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":8,\"font_type\":\"Regular\",\"name\":\"UUMprice_yuan_sum\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":120,\"qrcodey\":15,\"time\":\"0\",\"title\":\"总价：\",\"choose\":false,\"x\":422,\"y\":116},{\"child\":0,\"cname\":\"有效日期\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":8,\"font_type\":\"Regular\",\"name\":\"UUendtime\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":120,\"qrcodey\":15,\"time\":\"2\",\"title\":\"有效期：\",\"choose\":false,\"x\":422,\"y\":176}]";
                        $ticket['PrintName']        = "";
                        $ticket['TempType']         = 0;
                        $ticket['salerid']          = "";
                        $ticket['title']            = "";
                        $ticket['tid']              = "";
                        $ticket['ttitle']           = "";
                        $ticket['Printer']          = "QD";
                        $ticket['TicketHeight']     = 173;
                        $ticket['TicketWeight']     = 80;
                        break;
                    case 9:
                        $ticket['PrinType']         = 1;
                        $ticket['TicketsPrintWay']  = 0;
                        $ticket['TicketJsonConfig'] = "[{\"child\":0,\"cname\":\"门票名称\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":10,\"font_type\":\"Regular\",\"name\":\"UUttitle\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":120,\"qrcodey\":16,\"time\":\"0\",\"title\":\"票类： \",\"choose\":false,\"x\":80,\"y\":7},{\"child\":0,\"cname\":\"预订数量\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":10,\"font_type\":\"Regular\",\"name\":\"UUtnum\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":120,\"qrcodey\":16,\"time\":\"0\",\"title\":\"人数： \",\"choose\":false,\"x\":80,\"y\":22},{\"child\":0,\"cname\":\"凭证号\",\"expression\":\"\",\"font\":\"黑体\",\"font_size\":12,\"font_type\":\"Regular\",\"name\":\"UUcode\",\"printseat\":\"0\",\"qrcode\":\"2\",\"qrcodex\":120,\"qrcodey\":90,\"time\":\"0\",\"title\":\"\",\"choose\":false,\"x\":80,\"y\":41},{\"child\":0,\"cname\":\"凭证号\",\"expression\":\"null\",\"font\":\"黑体\",\"font_size\":10,\"font_type\":\"Regular\",\"name\":\"UUcode\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":120,\"qrcodey\":16,\"time\":\"0\",\"title\":\"门票码：\",\"choose\":false,\"x\":80,\"y\":134.7167},{\"child\":0,\"cname\":\"订单号\",\"expression\":\"null\",\"font\":\"黑体\",\"font_size\":10,\"font_type\":\"Regular\",\"name\":\"UUordernum\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":120,\"qrcodey\":16,\"time\":\"0\",\"title\":\"订 单 号：\",\"choose\":false,\"x\":80,\"y\":151.5167},{\"child\":0,\"cname\":\"付款方式\",\"expression\":\"null\",\"font\":\"黑体\",\"font_size\":10,\"font_type\":\"Regular\",\"name\":\"UUpaywayName\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":120,\"qrcodey\":18,\"time\":\"0\",\"title\":\"支付方式：\",\"choose\":false,\"x\":80,\"y\":168.5167},{\"child\":0,\"cname\":\"系统时间\",\"expression\":\"null\",\"font\":\"黑体\",\"font_size\":10,\"font_type\":\"Regular\",\"name\":\"SystemTime\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":120,\"qrcodey\":16,\"time\":\"2\",\"title\":\"有效期：\",\"choose\":false,\"x\":80,\"y\":187.316681},{\"child\":0,\"cname\":\"文本\",\"expression\":\"null\",\"font\":\"黑体\",\"font_size\":2,\"font_type\":\"Regular\",\"name\":\"Text\",\"printseat\":\"1\",\"qrcode\":\"0\",\"qrcodex\":80,\"qrcodey\":10,\"time\":\"0\",\"title\":\"*注：验证日期当日有效\",\"choose\":false,\"x\":10,\"y\":211.1}]";
                        $ticket['PrintName']        = "";
                        $ticket['TempType']         = 0;
                        $ticket['salerid']          = "";
                        $ticket['title']            = "";
                        $ticket['tid']              = "";
                        $ticket['ttitle']           = "";
                        $ticket['Printer']          = "QD";
                        $ticket['TicketHeight']     = 0;
                        $ticket['TicketWeight']     = 0;
                        break;
                }

                $ticket['TrDateTime']       = date('Y-m-d H:i:s', time());
                $ticket['apply_did']        = $this->_sid;
                //配置票
                $result = $ticketConfigModel->addTicketConfig($ticket);
                if ($result['code'] == 200) {
                    $this->apiReturn(200, [], '设置成功');
                } else {
                    $this->apiReturn(400, [], '设置门票模板失败');
                }
            }
            $this->apiReturn(200, [], '设置成功');
        }
        $this->apiReturn(400, [], '设置失败');
    }

    /**
     * 删除终端
     * @Author: zhujb
     */
    public function delTerminal()
    {
        $terminalNo            = I('post.TerminalNo', '', 'trim');
        $terminalSelfHelpModel = new TerminalSelfHelp();
        $res                   = $terminalSelfHelpModel->delTerminal($terminalNo);

        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '删除成功');
        }
        $this->apiReturn(400, [], '删除失败');
    }

    /**
     * 编辑自助终端配置
     * @Author: zhujb
     */
    public function setSelfHelp()
    {
        $terminalNo           = I('post.TerminalNo', '', 'trim');
        $data['SupplierName'] = I('post.SupplierName', '', 'trim');  // 供应商名称
        $data['SupplierNO']   = I('post.SupplierNO', '', 'trim');    // 供应商编号
        $data['Remarks']      = I('post.Remarks', '', 'trim');       // 备注
        $data['TType']        = I('post.TType', 1, 'intval');        // 终端类型
        $data['SharedTType']  = I('post.SharedTType', '', 'trim');   // 共享终端类型

        $terminalSelfHelpModel = new TerminalSelfHelp();
        $res                   = $terminalSelfHelpModel->editSelfHelp($terminalNo, $data);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '设置成功');
        }
        $this->apiReturn(400, [], '设置失败');
    }

    /**
     * 获取终端号对应的景区列表
     * @Author: zhujb
     */
    public function getLandList()
    {
        $page       = I('post.page', 1, 'intval');
        $terminalNo = I('post.TerminalNo', '', 'trim');

        $terminalScenicSpotRModel = new TerminalScenicSpotR();
        $list['list']             = $terminalScenicSpotRModel->getScenicSpots($terminalNo, $page);
        $list['total']            = $terminalScenicSpotRModel->getScenicSpotsCount($terminalNo);
        $list['page_size']         = $terminalScenicSpotRModel->pageSize;

        $this->apiReturn(200, $list);
    }

    /**
     * 设置景区
     * @Author: zhujb
     */
    public function setLand()
    {
        $action                 = I('post.Action', 0, 'intval');
        $terminalScenicSpotId   = I('post.TerminalScenicSpotId', 0, 'intval');
        $data['TerminalNo']     = I('post.TerminalNo', '', 'trim');
        $data['ScenicSpotName'] = I('post.ScenicSpotName', '', 'trim');
        $data['ScenicSpotNo']   = I('post.ScenicSpotNo', '', 'trim');
        $data['ImageUrl']       = I('post.ImageUrl', '', 'trim');
        $data['Sorts']          = I('post.Sorts', 0, 'intval');
        $data['stype']          = I('post.stype', 0, 'intval');
        $data['TrDateTime']     = date('Y-m-d H:i:s', time());

        if (empty($action)) {
            $this->apiReturn(400, [], '参数错误');
        }

        if (empty($data['ScenicSpotName'])) {
            $this->apiReturn(400, [], '请填写景区名称');
        }

        if (empty($data['ScenicSpotNo'])) {
            $this->apiReturn(400, [], '请填写景区编号');
        }

        if (empty($data['ImageUrl'])) {
            $this->apiReturn(400, [], '请填写图片地址');
        }

        if (empty($data['Sorts'])) {
            $this->apiReturn(400, [], '请填写序号');
        }

        $terminalScenicSpotRModel = new TerminalScenicSpotR();
        if ($action == 1) {
            // 添加景区
            $res = $terminalScenicSpotRModel->addScenicSpot($data);
        } else if ($action == 2) {
            // 编辑景区
            $res = $terminalScenicSpotRModel->editScenicSpot($terminalScenicSpotId, $data);
        } else {
            // 删除景区
            $res = $terminalScenicSpotRModel->delScenicSpot($terminalScenicSpotId);
        }
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '设置成功');
        }
        $this->apiReturn(400, [], '设置失败');
    }

    /**
     * 系统配置
     * @Author: zhujb
     */
    public function sysConfig()
    {
        $terminalNo                 = I('post.TerminalNo', '', 'trim');
        $data['PlatformAccount']    = I('post.PlatformAccount', '', 'trim');
        $data['PlatformPwd']        = I('post.PlatformPwd', '', 'trim');
        $data['OrderValidateUrl']   = I('post.OrderValidateUrl', '', 'trim');
        $data['IDCardTimeInterval'] = I('post.IDCardTimeInterval', 300, 'intval');
        $data['MainScreenHeight']   = I('post.MainScreenHeight', 955, 'intval');
        $data['IDCardType']         = I('post.IDCardType', 1, 'intval');
        $data['IDCardPosition']     = I('post.IDCardPosition', 2, 'intval');
        $data['FooterMessage']      = I('post.FooterMessage', '', 'trim');
        $data['PrinterPages']       = I('post.PrinterPages', 1, 'intval');
        $data['MCardTicket']        = I('post.MCardTicket', 0, 'intval');
        $data['TicketTips']         = I('post.TicketTips', '', 'trim');
        $data['HomeImage']          = I('post.HomeImage', '', 'trim');
        $data['OfflineTickets']     = I('post.OfflineTickets', 0, 'intval');
        $data['Lookup']             = I('post.Lookup', 1, 'intval');
        $data['OrderValidateIP']    = I('post.OrderValidateIP', '', 'trim');
        $data['OfflineValTickets']  = I('post.OfflineValTickets', 0, 'intval');
        $data['IphoneSysPwd']       = I('post.IphoneSysPwd', '', 'trim');
        $data['ScanGunPort']        = I('post.ScanGunPort', '', 'strval');
        $data['TrDateTime']         = date('Y-m-d H:i:s', time());

        $terminalSysConfigModel = new TerminalSysConfig();
        $res                    = $terminalSysConfigModel->sysConfig($terminalNo, $data);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '设置成功');
        }
        $this->apiReturn(400, [], '设置失败');
    }

    /**
     * 服务管理
     * @Author: zhujb
     */
    public function setServer()
    {
        $terminalNo                    = I('post.TerminalNo', '', 'trim');
        $data['OpenSellTickets']       = I('post.OpenSellTickets', 0, 'intval'); // 打开售票功能
        $data['OpenTheTickets']        = I('post.OpenTheTickets', 1, 'intval');  // 打开取票功能
        $data['SaleTickets']           = I('post.SaleTickets', 0, 'intval');     // 打开预售功能
        $data['SellingDistProducts']   = I('post.SellingDistProducts', 0, 'intval'); // 打开退票功能
        $data['PaymentType']           = I('post.PaymentType', '1,2', 'trim');  // 支付类型
        $data['PayBefBrushIDCard']     = I('post.PayBefBrushIDCard', 0, 'intval'); // 售票刷身份证
        $data['SendEleTickets']        = I('post.SendEleTickets', 0, 'intval');  // 售票填写手机号
        $data['OpenQRCodeTickets']     = I('post.OpenQRCodeTickets', 0, 'intval'); // 凭证号/二维码取票
        $data['OpenPhoneTickets']      = I('post.OpenPhoneTickets', 1, 'intval'); // 手机号取票
        $data['OpenIDCardTickets']     = I('post.OpenIDCardTickets', 1, 'intval'); // 身份证取票
        $data['PrintEntityTickets']    = I('post.PrintEntityTickets', 1, 'trim'); // 打印实体门票
        $data['OpenDisPackage']        = I('post.OpenDisPackage', 0, 'intval'); // 取票套票显示
        $data['OpenOrderValidate']     = I('post.OpenOrderValidate', 0, 'intval'); // 取票订单验证
        $data['OpenSellOrderValidate'] = I('post.OpenSellOrderValidate', 1, 'intval'); // 购票订单验证
        $data['PaperCounter']          = I('post.PaperCounter', 1, 'intval');   // 虚拟纸张计数器
        $data['OpenOrderPrint']        = I('post.OpenOrderPrint', 0, 'intval');  // 订单打印服务

        $data['OpenTicketsTimeSet']      = I('post.OpenTicketsTimeSet', 0, 'intval'); // 取票时间设置
        $data['TicketsEndTime']          = I('post.TicketsEndTime', '', 'trim'); // 取票截止时间
        $data['LastTicketsTimeInterval'] = I('post.LastTicketsTimeInterval', 60, 'intval'); // 最后取票时间间隔
        $data['TicketsEndTimeMsg']       = I('post.TicketsEndTimeMsg', '', 'trim'); // 取票时间未到提示信息
        $data['CloseDisableWin']         = I('post.CloseDisableWin', 'M', 'trim'); // 关闭禁用窗口
        $data['OpenTiming']              = I('post.OpenTiming', 0, 'intval'); // 是否开启园区计时 0：否，1：是
        $data['OpenSummaryShowChange']   = I('post.OpenSummaryShowChange', 1, 'intval'); // 是否开启手持机汇总撤改数量展示 0：否，1：是

        $data['IsLackTicket']             = I('post.IsLackTicket', 0, 'intval');
        $data['IsVerifyCodeIdentityMode'] = I('post.IsVerifyCodeIdentityMode', 0, 'intval');
        $data['IsShowPickTicket']         = I('post.IsShowPickTicket', 0, 'intval');
        $data['IsLackSmallTicket']        = I('post.IsLackSmallTicket', 0, 'intval');
        $data['IsLackPaper']              = I('post.IsLackPaper', 0, 'intval');
        $data['TicketPrintMode']          = I('post.TicketPrintMode', 1, 'intval');
        $data['TrDateTime']               = date('Y-m-d H:i:s', time());

        // 如果关闭了 虚拟纸张计数器   就不能开启 缺少门票提示 和 缺少小票提示
        if ($data['PaperCounter'] == 0 && ($data['IsLackTicket'] == 1 || $data['IsLackSmallTicket'] == 1)) {
            $this->apiReturn(400, [], '要开启门票或小票提示，请先开启虚拟纸张计数器');
        }

        $terminalSysServicesConfigModel = new TerminalSysServicesConfig();
        $res                            = $terminalSysServicesConfigModel->serviceConfig($terminalNo, $data);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '设置成功');
        }
        $this->apiReturn(400, [], '设置失败');
    }

    /**
     * 获取终端号对应的账户列表
     * @Author: zhujb
     */
    public function getAccountList()
    {
        $terminalNo = I('post.TerminalNo', '', 'trim');
        $page       = I('post.page', 1, 'intval');

        $terminalAccountRModel = new TerminalAccountR();
        $list['list']          = $terminalAccountRModel->getAccounts($terminalNo, $page);
        $list['total']         = $terminalAccountRModel->getAccountsCount($terminalNo);
        $list['page_size']      = $terminalAccountRModel->pageSize;

        $this->apiReturn(200, $list);
    }

    /**
     * 设置终端用户绑定
     * @Author: zhujb
     */
    public function setAccount()
    {
        $action                = I('post.Action', 0, 'intval');
        $terminalAccountId     = I('post.TerminalAccountId', 0, 'intval');
        $data['TerminalNo']    = I('post.TerminalNo', '', 'trim');
        $data['UserName']      = I('post.UserName', '', 'trim');
        $data['Remarks']       = I('post.Remarks', '', 'trim');
        $data['IsMainAccount'] = I('post.IsMainAccount', 1, 'intval');
        $data['Token']         = $this->_uuid();
        $data['TrDateTime']    = date('Y-m-d H:i:s', time());

        if (empty($action)) {
            $this->apiReturn(400, [], '参数错误');
        }

        $terminalAccountRModel = new TerminalAccountR();
        if ($action == 1) {
            // 添加账户
            $res = $terminalAccountRModel->addAccount($data);
        } else {
            // 不能删除当前登录账户
            $currentAccount = $this->_memberInfo['account'];
            $accountInfo    = $terminalAccountRModel->findAccount('', '', '', $terminalAccountId);

            if ($accountInfo['UserName'] != $currentAccount) {
                // 删除景区
                $res = $terminalAccountRModel->delAccount($terminalAccountId);
            } else {
                $this->apiReturn(400, [], '不能删除当前登录账户');
            }
        }

        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '设置成功');
        }
        $this->apiReturn(400, [], '设置失败');
    }

    /**
     * 客户端配置
     * @Author: zhujb
     */
    public function clientConfig()
    {
        $terminalNo             = I('post.TerminalNo', '', 'trim');
        $data['TicketSysName']  = I('post.TicketSysName', '', 'trim');
        $data['OpenPageSetCmd'] = I('post.OpenPageSetCmd', '', 'trim');
        $data['CloseSystemCmd'] = I('post.CloseSystemCmd', '', 'trim');
        $data['MobileNumbers']  = I('post.MobileNumbers', '', 'trim');
        $data['BannerUrl']      = I('post.BannerUrl', '', 'trim');
        $data['TicketsGridNum'] = I('post.TicketsGridNum', 4, 'trim');
        $data['SysCmd']         = I('post.SysCmd', 'L', 'trim');

        $data['TrDateTime'] = date('Y-m-d H:i:s', time());
        $terminalClientConfigModel = new TerminalClientConfig();
        $res                       = $terminalClientConfigModel->setClient($terminalNo, $data);

        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '设置成功');
        }
        $this->apiReturn(400, [], '设置失败');
    }

    /**
     * 生成uuid
     * @param string $prefix
     * @return string
     */
    private function _uuid($prefix = '')
    {
        $chars = md5(uniqid(mt_rand(), true));
        $uuid  = substr($chars, 0, 8) . '-';
        $uuid  .= substr($chars, 8, 4) . '-';
        $uuid  .= substr($chars, 12, 4) . '-';
        $uuid  .= substr($chars, 16, 4) . '-';
        $uuid  .= substr($chars, 20, 12);
        return $prefix . $uuid;
    }

    /**
     * 获取站点列表
     * @Author: zhujb
     * 2018/9/30
     */
    public function getSiteList()
    {
        $account = I('post.account', '', 'strval');

        $siteManageBusiness = new SiteManage();
        $res                = $siteManageBusiness->getSiteListInTermial($account);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 闸机配置页面门票编码装换
     * @Author: zhujb
     * 2018/11/29
     */
    public function getJavaEncode()
    {
        $str    = I('post.str', '', 'strval');
        $split  = I('post.split', '', 'strval');
        $encode = I('post.encode', '', 'strval');

        $res           = TicketApi::getQrCode($str, $split, $encode);

        if (!empty($res)) {
            $this->apiReturn('200', ['res' => $res], '获取成功');
        }
        $this->apiReturn('400', [], '获取失败');
    }


    /**
     * 获取终端列表
     * User: lanwanhui
     * Date: 2021/4/6
     *
     * @return
     */
    public function getAllTerminal()
    {
        $terminal  = I('terminal', 0, 'intval');
        $applyDid  = I('apply_did', 0, 'intval');

        if (empty($terminal)) {
            $this->apiReturn(204, [], '终端号不能为空');
        }

        if (empty($applyDid)) {
            $this->apiReturn(204, [], '供应商id不能为空');
        }

        $productConfig = new ProductConfig();

        $rs = $productConfig->getAllTerminal($applyDid, $terminal);

        $this->apiReturn($rs['code'] ?? 204, $rs['data'] ?? [], $rs['msg'] ?? '');
    }


    /**
     * 获取自助终端详情
     * User: lanwanhui
     * Date: 2022/4/6
     *
     * @return
     */
    public function getTerminalDetail()
    {
        $terminalNo            = I('TerminalNo', '', 'trim');
        if (empty($terminalNo)) {
            $this->apiReturn(204, [], '终端号不能为空');
        }

        $terminalSelfHelpModel = new TerminalSelfHelp();

        $rerminalSelfHelp      = $terminalSelfHelpModel->findSelfHelp($terminalNo);
        if (empty($rerminalSelfHelp)) {
            $this->apiReturn(204, [], '终端配置不存在');
        }

        $this->apiReturn(200, $rerminalSelfHelp);

    }


}