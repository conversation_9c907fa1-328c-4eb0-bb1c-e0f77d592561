<?php

namespace Controller\TerminalManage;

use Business\TerminalManage\OrderFaceManage as TerminalManageOrderFaceManage;
use Library\Controller;


/**
 * 订单人脸管理
 *
 * <AUTHOR>
 */
class OrderFaceManage extends Controller
{

    private $mid;
    private $sid;

    public function __construct()
    {
        parent::__construct();

        $loginInfo = $this->getLoginInfo('ajax');
        $this->mid = $loginInfo['memberID'];
        $this->sid = $loginInfo['sid'];
    }

    /**
     * 获取订单列表
     *
     * <AUTHOR>
     */
    public function getOrderList()
    {
        $page = I('page/d', 1);
        $size = I('size/d', 10);
        $keyword = I('keyword/s', '');

        $deviceBiz = new TerminalManageOrderFaceManage();
        $result = $deviceBiz->getOrderList($this->sid, $keyword, $page, $size);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }


    /**
     * 获取人脸订单详情
     *
     * <AUTHOR>
     */
    public function getOrderDetail()
    {
        $ordernum = I('ordernum/s', '');

        $deviceBiz = new TerminalManageOrderFaceManage();
        $result = $deviceBiz->getOrderDetail($this->sid, $ordernum);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }


    /**
     * 解绑人脸
     *
     * <AUTHOR>
     */
    public function unBindFace()
    {
        $idx = I('idx/d', 0);
        $ordernum = I('ordernum/s', '');

        $deviceBiz = new TerminalManageOrderFaceManage();
        $result = $deviceBiz->unBindFace($this->sid, $this->mid, $ordernum, $idx, 4);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }


    /**
     * 人脸检测
     *
     * <AUTHOR>
     */
    public function faceDetect()
    {
        $imgUrl = I('img_url/s', '');

        $deviceBiz = new TerminalManageOrderFaceManage();
        $result = $deviceBiz->faceDetect($imgUrl, $this->sid);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], '接口异常,请重试');
    }


    /**
     * 绑定人脸
     *
     * <AUTHOR>
     */
    public function bindFace()
    {
        $idx      = I('idx/d', 0);
        $ordernum = I('ordernum/s', '');
        $imgUrl   = I('img_url', '');

        $deviceBiz = new TerminalManageOrderFaceManage();
        $result = $deviceBiz->bindFace($this->sid, $this->mid, $ordernum, $idx, $imgUrl);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }
}