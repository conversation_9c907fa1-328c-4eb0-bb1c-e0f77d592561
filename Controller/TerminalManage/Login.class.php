<?php
/**
 * 终端配置登录退出相关
 * Created by PhpStorm.
 * Author: zhujb
 * Date: 2018/6/22
 * Time: 10:57
 */

namespace Controller\TerminalManage;

use Model\TerminalManage\SysMenu as SysMenu;
use Business\Member\Session as SessionBiz;

class Login extends BaseTerminalManage
{
    /**
     * 在登录页面加载时检验是否平台登录，是的话直接跳转
     * @Author: zhujb
     * 2018/7/23
     */
    public function checkLogin()
    {
        $isLogin = $this->isLogin('ajax', false);
        if ($isLogin === false) {
            $this->apiReturn(202, [], '未登录');
        }

        //获取登入信息
        $loginInfo = $this->getLoginInfo();

        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];

        $this->apiReturn(200, $loginInfo);
    }

    /**
     * 终端项目登录接口
     * @Author: zhujb
     */
    public function login()
    {
        $requestUri = $_SERVER['REQUEST_URI'];

        pft_log('terminal-manage', 'request uri ' . $requestUri . ' param' . json_encode($_POST, JSON_UNESCAPED_UNICODE));

        $account  = I('post.account', '', 'strval');
        $password = I('post.password', '', 'strval');
        $picVcode = I('post.graph', '', 'strval');

        if (!$account || !$password) {
            $this->apiReturn(400, [], '参数错误');
        }

        $loginBiz = new \Business\Member\Login();
        $res = $loginBiz->loginByPasswd($account, md5($password), 1, false, $picVcode);

        //进行具体的登录
        // $sessionBiz = new SessionBiz();
        // $res        = $sessionBiz->loginCommon($account, $password, 'pc', $picVcode = false);

        $code = $res['code'];
        $msg  = $res['msg'];

        if ($code !== 200) {
            $this->apiReturn($code, [], $msg);
        }

        //登录成功
        $this->apiReturn(200, $res['data'], $msg);
    }

    /**
     * 退出登录
     * @Author: zhujb
     */
    public function logout()
    {
        $businessSession = (new \Library\Tools\BusinessCache())->getBusinessCache($this->_sid);

        if (isset($businessSession['ticket'])) {
            $ssoClient = new \Business\Member\SSOClient();
            $ssoClient->logoutNotify($businessSession['ticket']);
        }

        (new \Library\Tools\BusinessCache())->delBusinessCache('ticket', $this->_sid);
        session_destroy();
        $this->apiReturn(200, [], '退出成功');
    }

    /**
     * 获取用户菜单
     * @Author: zhujb
     * 2018/7/25
     */
    public function getMenuList()
    {
        $account = I('post.account', '', 'strval');

        $sysMenuModel = new SysMenu();
        $menuList     = $sysMenuModel->getSysMenusByUserName($account);

        if (isset($menuList['code']) && $menuList['code'] == 204) {
            $this->apiReturn(400, [], '菜单获取失败');
        }
        $this->apiReturn(200, $menuList);
    }
}