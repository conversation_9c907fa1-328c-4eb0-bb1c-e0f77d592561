<?php
/**
 * 终端平台基础控制器
 * User: zhujb
 * Date: 2018-06.11
 * Time: 16:41
 */

namespace Controller\TerminalManage;

use Library\Tools\YarClient;
use Model\TerminalManage\SysMenu;
use Library\Controller;

class BaseTerminalManage extends Controller
{
    protected $_memberInfo = [];
    protected $_sid;
    protected $_memberId;

    //单点登录的token
    const SSO_TOKEN_SESSION_KEY = 'NEWZDSSOToken';

    // 特殊的接口不需要验证登录
    private $_allowUri = [
        '/r/TerminalManage_Login/login',
        '/r/TerminalManage_Login/logout',
        '/r/TerminalManage_Login/getMenuList',
        '/r/TerminalManage_Ticket/syncData',
        '/r/TerminalManage_Terminal/getSiteList',
        '/r/TerminalManage_Terminal/getJavaEncode',
        '/r/TerminalManage_DeviceManage/createQrcodeImg',
        '/r/TerminalManage_VersionManage/setVersion',
        '/r/TerminalManage_VersionManage/uploadFile',
        '/r/TerminalManage_DeviceManage/registerDeviceMqttAccount',
        '/r/TerminalManage_DeviceManage/getLandListByApplyDid',
        '/r/TerminalManage_DeviceManage/getTicketListByLid',
        '/r/TerminalManage_DeviceManage/getQrCodeBySetting',
        '/r/TerminalManage_DeviceManage/getCloudConfigScenicList',
        '/r/TerminalManage_DeviceManage/getSignInfoLog',
        '/r/TerminalManage_Terminal/getAllTerminal',
        '/r/TerminalManage_DeviceManage/getHealthCodeRuleByProvince'
    ];

    // 超级账户
    protected $_admin = [
        'pftlijian', 'pftljm', 'kfblj', 'kfblyz', 'kfbsjj',
        'pftzwr', 'pftchenlu', 'pftzcj', 'pftljh', 'xmblzj',
        'pftfzw', 'pftchenhf', 'kfblqq', 'pftlinzh', 'pengjb',
        'kfbll', 'kfbwyy', 'kfbzyh', 'pftsxm', 'kfbxr',
        'kfbjx', "kfbtlc", 'kfbly', 'kkbylq', 'kkbylq',
        'kfbxsj', 'kfbly', 'kfblhj', 'kfblcm', 'kfbtlc',
        'pftzqr', 'jfblwj', 'linjh', 'xmbcjy', 'wangnj',
        'chenxueyi', 'pftjjj', 'zuoyaping', 'pftzhangting',
        'pftsgn', 'kfblmw', 'kfblmy', 'kfblyl', 'pftjjs',
        'kfbzpp', 'kfbyxm2', 'kfbztt', 'jfblzj', 'pftjjs',
    ];

    public function __construct()
    {
        $requestUri = $_SERVER['REQUEST_URI'];
        $paramIdx = strpos($_SERVER['REQUEST_URI'], '?');
        if ($paramIdx !== false) {
            $requestUri = substr($_SERVER['REQUEST_URI'], 0, $paramIdx);
        }
        // 不在允许的接口里，要验证是否登录
        if (!in_array($requestUri, $this->_allowUri)) {
            $this->handleSsoLogin();
            // 控制访问频率
            parent::__construct();
            //$isLogin = $this->isLogin('ajax', false);
            //if ($isLogin === false) {
            //    $this->apiReturn(202, [], '未登录');
            //}
            //获取登入信息
            $loginInfo = $this->getLoginInfo();
            //信息暂存
            $this->_memberInfo = $loginInfo;
            $this->_sid = $loginInfo['sid'];
            $this->_memberId = $loginInfo['memberID'];
            // 有登录权限，还要验证是否有访问权限
            $isAuth = $this->_checkAuth($requestUri);
            if ($isAuth !== true && $loginInfo['sdtype'] != 9) {
                $this->apiReturn(206, [], '没有权限访问');
            }
            $logData = [
                'action' => $requestUri,
                'data' => $_POST,
                'user_info' => [
                    'referer' => $_SERVER['HTTP_REFERER'],
                    'agent' => $_SERVER['HTTP_USER_AGENT'],
                    'remote_ip' => get_client_ip(),
                    'member_id' => $loginInfo['memberID'],
                ]
            ];
            pft_log('terminal_manage/all', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        } else {
            pft_log('terminal_manage', 'request uri ' . $requestUri . ' param' . json_encode($_POST, JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 检验权限
     * @Author: zhujb
     * 2018/6/22
     * @param $requestUri
     * @return bool
     */
    private function _checkAuth($requestUri): bool
    {
        if (in_array($requestUri, [
            '/r/TerminalManage_DeviceManage/getDeviceSystemVersionList',
            '/r/TerminalManage_DeviceManage/getDeviceEditionBySystemVersion',
            '/r/TerminalManage_DeviceManage/getScSocialSecurityAuth',
        ])) {
            return true;
        }
        $sysMenuModel = new SysMenu();

        $menuNodeList = $sysMenuModel->getSysMenusByUserName($this->_memberInfo['account'], 'sm.Name');
        $menuNodeArr = array_column($menuNodeList, 'Name');

        // 获取当前用户能够访问的uri列表
        $uriList = [];
        $nodeMapUri = $this->_authMap();
        foreach ($menuNodeArr as $v) {
            foreach ($nodeMapUri as $kk => $vv) {
                if ($v == $vv['title']) {
                    $uriList[] = $vv['uris'];
                }
            }
        }
        // 判断是否有访问权限
        foreach ($uriList as $v) {
            if (in_array($requestUri, $v)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 权限节点也方法映射关系
     * @Author: zhujb
     * 2018/7/5
     */
    public function _authMap()
    {
        $map = array(
            array(
                'title' => '终端配置',
                'uris' => array(
                    '/r/TerminalManage_Terminal/getTerminalList',
                    '/r/TerminalManage_Terminal/setTerminal',
                    '/r/TerminalManage_Terminal/delTerminal',
                    '/r/TerminalManage_Terminal/setSelfHelp',
                    '/r/TerminalManage_Terminal/getTerminalDetail',
                )
            ),
            array(
                'title' => '客户端配置',
                'uris' => array(
                    '/r/TerminalManage_Terminal/clientConfig'
                )
            ),
            array(
                'title' => '门票配置',
                'uris' => array(
                    '/r/TerminalManage_Ticket/getTicketList'
                )
            ),
            array(
                'title' => '门票编辑',
                'uris' => array(
                    '/r/TerminalManage_Ticket/setTicketConfig',
                    '/r/TerminalManage_Ticket/getTicketTemplateList',
                    '/r/TerminalManage_Ticket/delTicket',
                    '/r/TerminalManage_Ticket/setTicketTemplateCommon'
                )
            ),
            array(
                'title' => '用户管理',
                'uris' => array(
                    '/r/TerminalManage_SysMenu/getUserList',
                    '/r/TerminalManage_SysMenu/setPermissions',
                    '/r/TerminalManage_SysMenu/getMenusByUserName'
                )
            ),
            array(
                'title' => '添加景区',
                'uris' => array(
                    '/r/TerminalManage_Terminal/setLand',
                    '/r/TerminalManage_Terminal/getLandList'
                )
            ),
            array(
                'title' => '绑定用户',
                'uris' => array(
                    '/r/TerminalManage_Terminal/setAccount',
                    '/r/TerminalManage_Terminal/getAccountList'
                )
            ),
            array(
                'title' => '系统配置',
                'uris' => array(
                    '/r/TerminalManage_Terminal/sysConfig'
                )
            ),
            array(
                'title' => '服务管理',
                'uris' => array(
                    '/r/TerminalManage_Terminal/setServer'
                )
            ),
            array(
                'title' => '版本号管理',
                'uris' => array(
                    '/r/TerminalManage_VersionManage/getVersionList',
                    '/r/TerminalManage_VersionManage/setVersion'
                )
            ),
            array(
                'title' => '操作日志',
                'uris' => array(
                    '/r/TerminalManage_NewOperationLog/getNewOperationLogList',
                    '/r/TerminalManage_NewOperationLog/orderPayCombine',
                    '/r/TerminalManage_NewOperationLog/getCtripOrderLog'
                )
            ),
            array(
                'title' => '错误日志',
                'uris' => array(
                    '/r/TerminalManage_NewOperationLog/getErrorLog',
                )
            ),
            array(
                'title' => '终端类型管理',
                'uris' => array(
                    '/r/TerminalManage_DeviceManage/getDeviceTypeList',
                    '/r/TerminalManage_DeviceManage/addDeviceType',
                    '/r/TerminalManage_DeviceManage/editDeviceType',
                    '/r/TerminalManage_DeviceManage/getOneDeviceType',
                    '/r/TerminalManage_DeviceManage/getAllDeviceType'
                )
            ),
            array(
                'title' => '厂商管理',
                'uris' => array(
                    '/r/TerminalManage_DeviceManage/addDeviceCompany',
                    '/r/TerminalManage_DeviceManage/editDeviceCompany',
                    '/r/TerminalManage_DeviceManage/getDeviceCompanyList',
                    '/r/TerminalManage_DeviceManage/getAllDeviceCompany',
                    '/r/TerminalManage_DeviceManage/getOneDeviceCompany'
                )
            ),
            array(
                'title' => '配件管理',
                'uris' => array(
                    '/r/TerminalManage_DeviceManage/addDevicePart',
                    '/r/TerminalManage_DeviceManage/editDevicePart',
                    '/r/TerminalManage_DeviceManage/getDevicePartList',
                    '/r/TerminalManage_DeviceManage/getAllDevicePart',
                    '/r/TerminalManage_DeviceManage/getOneDevicePart',
                    '/r/TerminalManage_DeviceManage/saveDeviceCurParts'
                )
            ),
            array(
                'title' => '终端产品管理',
                'uris' => array(
                    '/r/TerminalManage_DeviceManage/addDeviceProduct',
                    '/r/TerminalManage_DeviceManage/editDeviceProduct',
                    '/r/TerminalManage_DeviceManage/delDeviceProduct',
                    '/r/TerminalManage_DeviceManage/getDeviceProductList',
                    '/r/TerminalManage_DeviceManage/getOneDeviceProduct',
                )
            ),
            array(
                'title' => '序列号入库',
                'uris' => array(
                    '/r/TerminalManage_DeviceManage/getProductsByType',
                    '/r/TerminalManage_DeviceManage/addDeviceSequence',
                    '/r/TerminalManage_DeviceManage/delDeviceSequence',
                    '/r/TerminalManage_DeviceManage/getDeviceSequenceList',
                    '/r/TerminalManage_DeviceManage/getOneDeviceSequence',
                    '/r/TerminalManage_DeviceManage/unBindSequenceOnDevice'
                )
            ),
            array(
                'title' => '人脸设备',
                'uris' => array(
                    '/r/TerminalManage_Face/getFacePlatform',
                    '/r/TerminalManage_Face/setFacePlatform',
                    '/r/TerminalManage_Face/addDevice',
                    '/r/TerminalManage_Face/deleteDevice',
                    '/r/TerminalManage_Face/deleteGroup',
                    '/r/TerminalManage_Face/updateDevice',
                    '/r/TerminalManage_Face/getDevice',
                    '/r/TerminalManage_Face/moveFace'
                )
            ),
            array(
                'title' => '数据总览',
                'uris' => array(
                    '/r/TerminalManage_DeviceManage/getDeviceSummary',
                    '/r/TerminalManage_DeviceManage/getDeviceNumSummary',
                    '/r/TerminalManage_DeviceManage/getDeviceDistributionSummary',
                    '/r/TerminalManage_DeviceManage/getDeviceLocationSummary',
                )
            ),
            array(
                'title' => '终端版本总览',
                'uris' => array(
                    '/r/TerminalManage_DeviceManage/getDeviceEditionSummaryInfo',
                    '/r/TerminalManage_DeviceManage/getDeviceEditionBySystemVersion',
                )
            ),
            array(
                'title' => '设备管理',
                'uris' => array(
                    '/r/TerminalManage_DeviceManage/saveDeviceUser',
                    '/r/TerminalManage_DeviceManage/deviceCheck',
                    '/r/TerminalManage_DeviceManage/getDeviceInfo',
                    '/r/TerminalManage_DeviceManage/getDeviceAuthList',
                    '/r/TerminalManage_DeviceManage/saveDeviceSettings',
                    '/r/TerminalManage_DeviceManage/saveDeviceAuth',
                    '/r/TerminalManage_DeviceManage/saveDeviceUser',
                    '/r/TerminalManage_DeviceManage/getDeviceManageList',
                    '/r/TerminalManage_DeviceManage/updateDeviceStatus',
                    '/r/TerminalManage_DeviceManage/summaryDeviceBySupplierId',
                    '/r/TerminalManage_DeviceManage/summaryDeviceByDeviceType',
                    '/r/TerminalManage_DeviceManage/resetDevice',
                    '/r/TerminalManage_DeviceManage/getVersionList',
                    '/r/TerminalManage_DeviceManage/pushVersion',
                    '/r/TerminalManage_DeviceManage/getDeviceSystemVersionList',
                    '/r/TerminalManage_DeviceManage/getDeviceIntroduction',
                    '/r/TerminalManage_DeviceManage/getDeviceLogList',
                    '/r/TerminalManage_DeviceManage/addDevicePackage',
                    '/r/TerminalManage_DeviceManage/saveDeviceName',
                    '/r/TerminalManage_DeviceManage/editDevicePackage',
                    '/r/TerminalManage_DeviceManage/delDevicePackage',
                    '/r/TerminalManage_DeviceManage/getDeviceFuncs',
                    '/r/TerminalManage_DeviceManage/addDeviceFunc',
                    '/r/TerminalManage_DeviceManage/getDevicePackageList',
                    '/r/TerminalManage_DeviceManage/pushDeviceGateCode',
                    '/r/TerminalManage_DeviceManage/destroyGateCode',
                    '/r/TerminalManage_DeviceManage/addGateCode',
                    '/r/TerminalManage_DeviceManage/editGateCode',
                    '/r/TerminalManage_DeviceManage/delGateCode',
                    '/r/TerminalManage_DeviceManage/getGateCodeList',
                    '/r/TerminalManage_DeviceManage/saveDeviceRemark',
                    '/r/TerminalManage_DeviceManage/saveDevicePackage',
                    '/r/TerminalManage_DeviceManage/searchDevice',
                    '/r/TerminalManage_DeviceManage/getDevicePackageInfo',
                    '/r/TerminalManage_DeviceManage/searchPackage',
                    '/r/TerminalManage_DeviceManage/setDevicePrintSetting',
                    '/r/TerminalManage_DeviceManage/getDeviceHandMachineOpLogList',
                    '/r/TerminalManage_DeviceManage/addDeviceBatchProject',
                    '/r/TerminalManage_DeviceManage/editDeviceBatchProject',
                    '/r/TerminalManage_DeviceManage/delDeviceBatchProject',
                    '/r/TerminalManage_DeviceManage/getDeviceBatchProjectList',
                    '/r/TerminalManage_DeviceManage/getDeviceBatchProjectDetail',
                    '/r/TerminalManage_DeviceManage/getDeviceByExcel',
                    '/r/TerminalManage_DeviceManage/saveDevicePrintSetting',
                    '/r/TerminalManage_DeviceManage/getDeviceHandMachineOpLogList',
                    '/r/TerminalManage_DeviceManage/saveGateDeviceSetting',
                    '/r/TerminalManage_DeviceManage/gateDeviceHomeData',
                    '/r/TerminalManage_DeviceManage/getDeviceNewVersion',
                    '/r/TerminalManage_DeviceManage/getNewUpgradeByDeviceV2',
                    '/r/TerminalManage_DeviceManage/saveDeviceSystemSetting',
                    '/r/TerminalManage_DeviceManage/saveDeviceClientSetting',
                    '/r/TerminalManage_DeviceManage/saveDeviceServiceSetting',
                    '/r/TerminalManage_DeviceManage/batchPushDeviceUpgrade',
                    '/r/TerminalManage_DeviceManage/uploadTerminalManageImg',
                    '/r/TerminalManage_DeviceManage/deleteTerminalManageImg',
                    '/r/TerminalManage_DeviceManage/getDeviceFaceSetting',
                    '/r/TerminalManage_DeviceManage/saveGateDeviceFaceSetting',
                    '/r/TerminalManage_DeviceManage/saveGateDeviceDataControlSetting',
                    '/r/TerminalManage_DeviceManage/saveDeviceProductId',
                    '/r/TerminalManage_DeviceManage/saveCloundDeviceSystemSetting',
                    '/r/TerminalManage_DeviceManage/getDeviceSystemSetting',
                    '/r/TerminalManage_DeviceManage/saveAndroidDeviceServiceSetting',
                    '/r/TerminalManage_DeviceManage/saveSelfMachineHardwareSetting',
                    '/r/TerminalManage_DeviceManage/saveLogSetting',
                    '/r/TerminalManage_DeviceManage/saveDetectionAddress',
                    '/r/TerminalManage_DeviceManage/getSettingFormTemplate',
                    '/r/TerminalManage_DeviceManage/getHealthCodeRuleByProvince',
                    '/r/TerminalManage_DeviceManage/getVasClassifyList',
                    '/r/TerminalManage_DeviceManage/unbindDeviceLicense',
                    '/r/TerminalManage_DeviceManage/unBindDeviceUser',
                    '/r/TerminalManage_DeviceManage/bindAuxiliaryMachine',
                    '/r/TerminalManage_DeviceManage/unbindAuxiliaryMachine',
                    '/r/TerminalManage_DeviceManage/getScSocialSecurityAuth',
                    '/r/TerminalManage_DeviceManageBatch/batchSaveDeviceSignConfig',//批量设置检票配置
                    '/r/TerminalManage_DeviceManageBatch/batchSaveDeviceViewConfig',//批量设置待机界面配置
                    '/r/TerminalManage_DeviceManageBatch/batchSaveDeviceMovementConfig',//批量设置硬件配置
                    '/r/TerminalManage_DeviceManageBatch/batchSaveDeviceDataControlConfig',//批量设置数据控制
                    '/r/TerminalManage_DeviceManageBatch/batchSaveDeviceFaceConfig',//批量设置人脸配置
                    '/r/TerminalManage_DeviceManageBatch/batchSaveDeviceSignSingleConfig',//批量设置检票配置单功能
                    '/r/TerminalManage_DeviceManageBatch/batchSaveDeviceViewSingleConfig',//批量设置待机界面配置单功能
                    '/r/TerminalManage_DeviceManageBatch/batchSaveDeviceMovementSingleConfig',//批量设置硬件配置单功能
                    '/r/TerminalManage_DeviceManageBatch/batchSaveDeviceDataControlSingleConfig',//批量设置数据控制配置单功能
                    '/r/TerminalManage_DeviceManageBatch/batchSaveDeviceFaceSingleConfig',//批量设置人脸配置单功能
                    '/r/TerminalManage_DeviceManageBatch/batchSaveDeviceFaceSingleConfig',//批量设置人脸配置单功能
                    '/r/TerminalManage_DevCom/addOrUpdateConfig',//添加或修改主板串口配置
                    '/r/TerminalManage_DevCom/getDetail',//获取主板串口配置详情
                    '/r/TerminalManage_DevCom/deleteConfig',//删除主板串口配置
                    '/r/TerminalManage_DevCom/getConfigList',//获取主板串口配置列表
                )
            ),
            array(
                'title' => '设备表单模板配置',
                'uris' => array(
                    '/r/TerminalManage_DeviceManage/setSettingFormTemplate',
                )
            ),
        );

        return $map;
    }

    /**
     * 对接登录
     *
     * @date 2024/01/30
     * @auther yangjianhui
     * @return array|bool
     */
    protected function handleSsoLogin()
    {
        $token = $_COOKIE['MANAGE_SYS_TOKEN'];//cookie-name 待定
        $domain = str_replace('my', 'newzd', MY_DOMAIN);
        $redirect = urlencode($domain . $_SERVER['REQUEST_URI']);
        $signUrl = str_replace('signin', 'admin-signin', SSO_DOMAIN);
        if (!$token) {
            $backUrl = $signUrl . "/#/login?login_error=true&redirect=" . $redirect;
            header("Location: " . $backUrl);
            exit;
        }

        //获取session里面的存在token
        $sessionToken = $this->_getSSOToken();
        if (!empty($sessionToken) && $sessionToken == $token) {
            return true;
        }

        //token解析获取用户基础信息
        $ssoClient = new \Business\Member\SSOLogin();
        $callRes = $ssoClient->getMemberByToken($token);
        if ($callRes['code'] != 200) {
            pft_log('sso/login/error', $token.':getMemberByToken:'.json_encode($callRes, JSON_UNESCAPED_UNICODE));
            $backUrl = $signUrl . "/#/login?login_error=true&redirect=" . $redirect;
            header("Location: " . $backUrl);
            exit;
        }

        $extraInfo = [];
        if ($callRes['data']['metadata']['linkIds']) {
            $extraInfo['sso_metadata_link_ids'] = json_encode($callRes['data']['metadata']['linkIds']);
        }

        //写session
        $sessionBiz = new \Business\Member\Session();
        $sessionRes = $sessionBiz->loginMemberId($callRes['data']['id'], 'pc', false, 0, 0, $extraInfo);
        if ($sessionRes['code'] != 200) {
            pft_log('sso/login/error', 'loginMemberId:'.json_encode($sessionRes, JSON_UNESCAPED_UNICODE));
            $backUrl = $signUrl . "/#/login?login_error=true&redirect=" . $redirect;
            header("Location: " . $backUrl);
            exit;
        }
        $url = parse_url($redirect);
        $backUrl = $domain . trim($url['path'], '/');
        //重置下session中的token
        $this->_setSSOToken($token);

        pft_log('sso/login/info', json_encode(['cookie'=>$_COOKIE['MANAGE_SYS_TOKEN'], 'sessionToken'=>$sessionToken, $sessionRes], JSON_UNESCAPED_UNICODE));

        header("Location: " . $backUrl);
        exit;
    }


    /**
     * 设置单点登录token
     *
     * @param string $token 登录token
     *
     * @return bool
     */
    private function _setSSOToken($token)
    {
        if (empty($token)) {
            return false;
        }

        $_SESSION[self::SSO_TOKEN_SESSION_KEY] = $token;

        return true;
    }

    /**
     * 获取单点登录的token
     *
     * @return mixed
     */
    private function _getSSOToken()
    {
        return $_SESSION[self::SSO_TOKEN_SESSION_KEY];
    }

    protected function handleResponse(string $method, array $paramArr)
    {
        if (empty($method)) {
            $this->apiReturn(204, [], 'method参数错误');
        }
        $yarClient = new YarClient('device');
        $res = $yarClient->call($method, $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错：' . $res['error_msg'], true);
        }
        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }
}