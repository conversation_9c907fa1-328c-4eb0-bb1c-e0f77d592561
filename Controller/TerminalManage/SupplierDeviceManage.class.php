<?php

namespace Controller\TerminalManage;

use Business\TerminalManage\SupplierDeviceManage as TerminalManageSupplierDeviceManage;
use Business\TerminalManage\TerminalManage;
use Library\Constants\Device;
use Library\Controller;
use Library\SimpleExcel;
use Business\TerminalManage\ProductConfig;
use Library\Util\DebugUtil;

/**
 * 供应商设备管理(newzd给管理员（内部）用,以示区分)
 *
 * <AUTHOR>
 */
class SupplierDeviceManage extends Controller
{

    private $mid;
    private $sid;

    public function __construct()
    {
        parent::__construct();

        $loginInfo = $this->getLoginInfo('ajax');
        $this->mid = $loginInfo['memberID'];
        $this->sid = $loginInfo['sid'];
    }


    /**
     * 获取设备数据概览
     *
     * <AUTHOR>
     */
    public function getDeviceOverview()
    {
        $deviceBiz = new TerminalManageSupplierDeviceManage();
        $result = $deviceBiz->getDeviceOverview($this->sid);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }


    /**
     * 前端搜索的一些配置项
     *
     * <AUTHOR>
     */
    public function getSearchConfig()
    {
        $keyword = [
            ['key' => 'device_name', 'val' => '设备名称'],
            ['key' => 'device_key', 'val' => '特征码'],
            ['key' => 'device_code', 'val' => '序列号'],
            ['key' => 'remark', 'val' => '备注模糊搜索'],
        ];
        $deviceType = [];
        foreach (Device::DEVICE_TYPE_ARR as $k => $v) {
            $deviceType[] = ['key' => $k, 'val' => $v];
        }

        // $deviceModel = ['Q1', 'V1', 'L2', 'S1000', 'NULL', 'STV8_4', 'RK3399_M', 'LDA8', 'SABRESD_', 'K9', 'SABRESD-', 'K1', 'SC3310J', 'RK3288', '.net', 'V2', 'Windows8', 'T2', 'Windows7', 'CHAINWAY', 'K2MINI'];

        $onlineState = [
            ['key' => 1, 'val' => '在线'],
            ['key' => 2, 'val' => '离线'],
        ];

        $return = [
            'keyword'       => $keyword,
            'device_type'   => $deviceType,
            'online_state'  => $onlineState
        ];

        $this->apiReturn(200, $return);
    }


    /**
     * 获取设备型号列表
     *
     * <AUTHOR>
     */
    public function getDeviceModelList()
    {
        $deviceType = I('device_type', 1);

        $deviceBiz = new TerminalManageSupplierDeviceManage();
        $result = $deviceBiz->getDeviceModelList($this->sid, $deviceType);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }


    /**
     * 获取软件版本号列表
     *
     * <AUTHOR>
     */
    public function getSoftwareVersions()
    {
        $deviceType = I('device_type', 1);
        $modelType  = I('model_type', '');

        $deviceBiz = new TerminalManageSupplierDeviceManage();
        $result = $deviceBiz->getSoftwareVersions($this->sid, $deviceType, $modelType);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }


    /**
     * 获取供应商设备列表
     *
     * <AUTHOR>
     */
    public function getDeviceList()
    {
        $page = I('page/d', 1);
        $size = I('size/d', 10);
        //搜索关键字
        $keyword = I('keyword/s', '');
        //关键字类型
        $keywordType = I('keyword_type/s', '');
        //设备类型
        $deviceType = I('device_type/d', -1);
        //设备型号取自产品类型的型号这里-1表示全部,0表示其他类型
        $productId       = I('product_id', '', 'strval');
        //软件版本
        $softwareVersion = I('software_version/s', '');
        //在线状态
        $onlineState = I('online_state/d', 0);
        //设备节点id
        $nodeId = I('post.node_id', 0, 'intval');

        $classify = I('post.classify', '', 'strval'); //模块包类型
        $authType = I('post.auth_type', 0, 'intval'); //授权状态  1:已授权 2：未授权

        $productId       = $productId === '' ? -1 : intval($productId);

        $deviceBiz = new TerminalManageSupplierDeviceManage();
        $result = $deviceBiz->getDeviceList($this->sid, $keyword, $keywordType, $deviceType, $productId, $softwareVersion, $onlineState,
            $nodeId, $classify, $authType, $page, $size);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }


    /**
     * 获取设备详情
     *
     * <AUTHOR>
     */
    public function getDeviceDetail()
    {
        $deviceKey = I('device_key', '');

        $deviceBiz = new TerminalManageSupplierDeviceManage();
        $result = $deviceBiz->getDeviceDetail($this->sid, $deviceKey);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }


    /**
     * 更新设备名称
     *
     * <AUTHOR>
     */
    public function updateDeviceName()
    {
        $deviceName = I('device_name', '');
        $deviceKey  = I('device_key', '');

        $deviceBiz = new TerminalManageSupplierDeviceManage();
        $result = $deviceBiz->updateDeviceName($this->sid, $deviceKey, $deviceName);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }


    /**
     * 更新设备备注
     *
     * <AUTHOR>
     */
    public function updateDeviceRemark()
    {
        $remark = I('remark', '');
        $deviceKey = I('device_key', '');

        $deviceBiz = new TerminalManageSupplierDeviceManage();
        $result = $deviceBiz->updateDeviceRemark($this->sid, $deviceKey, $remark);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }


    /**
     * 获取可升级信息
     *
     * <AUTHOR>
     */
    public function getUpgradeInfo()
    {
        $deviceType = I('device_type/d', 0);
        $productId  = I('product_id/d', 0);

        $deviceBiz = new TerminalManageSupplierDeviceManage();
        $result = $deviceBiz->getUpgradeInfo($deviceType, $productId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }


    /**
     * 升级推送
     *
     * <AUTHOR>
     */
    public function pushUpgradeMsg()
    {
        $deviceKey = I('device_key/s', '');
        $verionId  = I('version_id/d', 0);

        $deviceBiz = new TerminalManageSupplierDeviceManage();
        $result = $deviceBiz->pushUpgradeMsg($this->sid, $deviceKey, $verionId);
        
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常,请重试');
        }
    }

    /**
     * 修改设备密码
     * User: lanwanhui
     * Date: 2021/10/21
     */
    public function saveDiffDevicePasswd()
    {
        $deviceKey = I('device_key', '', 'strval,trim');
        $passwd    = I('passwd', '', 'strval,trim');

        if (empty($deviceKey) || empty($passwd)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $deviceBiz = new TerminalManageSupplierDeviceManage();

        $result = $deviceBiz->saveDiffDevicePasswd($deviceKey, $passwd, $this->mid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);

    }
    //手持机设置分终端密码
    //主要用于手持机的设置页面校验密码
    public function saveBranchTerminalPasswd()
    {
        $deviceKey = I('device_key', '', 'strval,trim');
        $passwd    = I('passwd', '', 'strval,trim');

        if (empty($deviceKey) || empty($passwd)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $deviceBiz = new TerminalManageSupplierDeviceManage();

        $result = $deviceBiz->saveBranchTerminalPasswd($deviceKey, $passwd, $this->mid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取验证数据
     *
     * @date 2022/03/02
     * @auther yangjianhui
     * @return array
     */
    public function getTicketVerifyData()
    {
        $nodeIds   = I('node_ids', '', 'strval');
        $beginDate = I('begin_date', '', 'strval');
        $endDate   = I('end_date', '', 'strval');
        $lids      = I('lids', '', 'strval');
        $tids      = I('tids', '', 'strval');
        $excel     = I('excel', '', 'strval');
        if (empty($beginDate) || empty($endDate)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $deviceBiz = new TerminalManageSupplierDeviceManage();
        $result    = $deviceBiz->getTicketVerifyData($this->sid, $nodeIds, $beginDate, $endDate, $lids, $tids, $excel);
        if ($excel) {
            $filename = date('YmdHis') . '节点验证数据统计';
            $excelData = $result['data']['list'] ?? [];
            $maxNode   = $result['data']['max_node'] ?? 0;
            $head = [];
            for ($i = 1; $i <= $maxNode; $i++) {
                $head[] = "{$i}级节点";
            }
            $head[] = "票名称";
            $head[] = "验证数量";
            array_unshift($excelData, $head);
            $xls = new SimpleExcel('UTF-8', true, '节点验证数据统计');
            $xls->addArray($excelData);
            $xls->generateXML($filename);
            exit;
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取出入园数据
     *
     * @date 2022/03/03
     * @auther yangjianhui
     * @return array
     */
    public function getInOutData()
    {
        $nodeIds     = I('node_ids', '', 'strval');
        $beginDate   = I('begin_date', '', 'strval');
        $endDate     = I('end_date', '', 'strval');
        $lids        = I('lids', '', 'strval');
        $tids        = I('tids', '', 'strval');
        $baseOnOrder = I('based_on_order', 2, 'intval');
        $type        = I('type', 1, 'intval');  //统计类型 1：按小时 2：按天 3：按月
        $hour        = I('hour', '', 'strval');  //小时筛选的字段
        $excel       = I('excel', '', 'strval');
        if (empty($beginDate) || empty($endDate)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $deviceBiz = new TerminalManageSupplierDeviceManage();
        if(!empty($nodeIds)){
            $nodeIds = explode(',', $nodeIds);
        } else {
            $nodeIds = [];
        }
        $result    = $deviceBiz->getInOutData($this->sid, $nodeIds, $beginDate, $endDate, $lids, $tids, $baseOnOrder, $type, $hour, $excel);
        if ($excel) {
            $filename = date('YmdHis') . '节点验证数据统计';
            $excelData = $result['data']['list'] ?? [];
            $maxNode   = $result['data']['max_node'] ?? 0;
            $head[] = "日期";
            for ($i = 1; $i <= $maxNode; $i++) {
                $head[] = "{$i}级节点";
            }
            $head[] = "入园";
            $head[] = "出园";
            array_unshift($excelData, $head);
            $xls = new SimpleExcel('UTF-8', true, '节点验证数据统计');
            $xls->addArray($excelData);
            $xls->generateXML($filename);
            exit;
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据供应商id 获取景区列表
     * @Author: lanwanhui
     * 2022/5/16
     */
    public function getLandListByApplyDid()
    {
        $applyDid = I('post.apply_did', 0, 'intval');
        $keyWord  = I('post.key_word', '', 'strval');
        $lid      = I('post.lid', 0, 'intval');
        $pageSize = I('post.page_size', 30, 'intval');
        $pType    = I('post.ptype', '', 'strval');

        if (empty($applyDid)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $productConfig = new ProductConfig();
        $rs = $productConfig->getLandListByApplyDid($applyDid, $keyWord, $lid, $pType, $pageSize);

        $this->apiReturn($rs['code'] ?? 204, $rs['data'] ?? [], $rs['msg'] ?? '');

    }

    /**
     * 根据景区id获取门票
     * @Author: lanwanhui
     * 2022/5/16
     */
    public function getTicketListByLid()
    {
        $lid = I('post.lid', 0, 'intval');
        if (empty($lid)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $productConfig = new ProductConfig();
        $rs = $productConfig->getTicketListByLid($lid);

        $this->apiReturn($rs['code'] ?? 204, $rs['data'] ?? [], $rs['msg'] ?? '');
    }

    /**
     * 获取终端列表
     * @Author: lanwanhui
     * 2022/5/16
     */
    public function getAllTerminal()
    {
        $terminal  = I('terminal', 0, 'intval');
        $applyDid  = I('apply_did', 0, 'intval');

        if (empty($terminal)) {
            $this->apiReturn(204, [], '终端号不能为空');
        }

        if (empty($applyDid)) {
            $this->apiReturn(204, [], '供应商id不能为空');
        }

        $productConfig = new ProductConfig();

        $rs = $productConfig->getAllTerminal($applyDid, $terminal);

        $this->apiReturn($rs['code'] ?? 204, $rs['data'] ?? [], $rs['msg'] ?? '');
    }


    /**
     *
     *
     * /r//TerminalManage_SupplierDeviceManage/gateDeviceHomeData
     * 获取闸机首页数据
     * @Author: zhujb
     * 2020/1/14
     */
    public function gateDeviceHomeData()
    {

        $deviceKey = I('post.device_key', '', 'strval,trim');
        $date = I('post.date', '', 'strval,trim');
        DebugUtil::debug(['🔴🟡🟢' => '🟣', '$date' => $date]);
        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }
        if(empty($date)) {
            $date = date('Y-m-d');
        }

        if(date('Y-m-d',strtotime($date)) != $date) {
            $this->apiReturn(204, [], '日期参数错误');
        }

        $terminalManageBiz = new TerminalManage();

        $result = $terminalManageBiz->gateDeviceHomeData($deviceKey, $date, false);
        if (isset($result['code'])){
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }else{
            $this->apiReturn(500, [], '服务异常');
        }
    }
}