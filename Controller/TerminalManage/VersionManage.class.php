<?php
/**
 * 版本发布
 * User: zhujb
 * Date: 2018-06.11
 * Time: 16:41
 */

namespace Controller\TerminalManage;

use Library\Tools\Helpers;
use Model\TerminalManage\VersionManage as VersionManageModel;
use Library\Tools\YarClient;

class VersionManage extends BaseTerminalManage
{
    /**
     * 终端版本号管理列表
     * @Author: zhujb
     */
    public function getVersionList()
    {
        $page      = I('post.page', 1, 'intval');
        $pageSize  = I('post.page_size', 10, 'intval');
        $cate      = I('post.Category', 0, 'intval');
        $versionNo = I('post.versionNo', '', 'trim');
        $typeId    = I('post.type_id', 0, 'intval');
        $productId = I('post.product_id', 0, 'intval');

        $versionManageModel = new VersionManageModel();

        $list = $versionManageModel->getVersionManages($page, $pageSize, $cate, $versionNo,'*', 'TrDateTime desc', $typeId, $productId);
        $total   = $versionManageModel->getVersionManagesCount($cate, $versionNo, $typeId, $productId);
        $pageSize = $versionManageModel->pageSize;

        $versionGroupData = $versionManageModel->selectVersionGroupByTypeAndSystemNo();

        //产品类型信息
        $productList = [];
        $yarClient = new YarClient('device');
        $res       = $yarClient->call('Device/Device/getDeviceSystemVersionList');
        if ($res['code'] == 200) {
            $productList = $res['res']['data'];
            $productList  = array_column($productList, 'pro_model_no', 'id');
        }

        foreach ($list as & $item) {
            $compareKey = $item['type_id'] . '_' . $item['product_id'];
            $item['is_new'] = false;
            if (isset($versionGroupData[$compareKey]['max_version']) && $item['versionCode'] == $versionGroupData[$compareKey]['max_version']) {
                $item['is_new'] = true;
            }
            $item['pro_model_no'] = isset($productList[$item['product_id']]) ? $productList[$item['product_id']] : '';
        }

        $res = [
            'list' => $list,
            'total' => $total,
            'pageSize' => $pageSize
        ];
        $this->apiReturn(200, $res);
    }

    /**
     * 设置版本号
     * @Author: zhujb
     */
    public function setVersion()
    {
        $action                    = I('post.Action', 0, 'intval');
        $versionManagerId          = I('post.VersionManagerId', 0, 'intval');
        $data['Category']          = I('post.Category', 1, 'intval');
        $data['versionCode']       = I('post.versionCode', '', 'trim');
        $data['VersionManagerNo']  = I('post.VersionManagerNo', '', 'trim');
        $data['UpdateDescription'] = I('post.UpdateDescription', '', 'trim');
        $data['AppUrl']            = I('post.AppUrl', '', 'trim');
        $data['TrDateTime']        = date('Y-m-d H:i:s', time());
        $data['type_id']           = I('post.type_id', 0, 'intval');
        $data['product_id']        = I('post.product_id', 0, 'intval');

        if (empty($action)) {
            $this->apiReturn(400, [], '参数错误');
        }

        if (in_array($action, [1,2]) && empty($data['product_id'])) {
            $this->apiReturn(400, [], '型号id不能为空');
        }

        $versionManageModel = new VersionManageModel();

        if ($action == 1) {
            $res = $versionManageModel->addVersion($data);
        } else if ($action == 2) {
            $res = $versionManageModel->editVersion($versionManagerId, $data);
        } else {
            $res = $versionManageModel->delVersion($versionManagerId);
        }

        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '设置成功');
        }
        $this->apiReturn(400, [], '设置失败');
    }

    /**
     * 新增版本号时先把文件上传至本地
     * <AUTHOR>
     * @date 2019/8/22
     */
    public function uploadFile()
    {
        $file = $_FILES['file'];
        //如果临时文件不存在
        if (!empty($file['tmp_name'])) {
            //如果是修改的话要把之前的文件路径上传
            $oldFilePath = I('post.oldFilePath', '', 'strval');
            //截取文件的后缀名
            $suffix = strrchr($file['name'], '.');
            //只允许上传压缩文件
            $zip = array('.rar','.zip','.tar','.cab','.uue','.jar','.iso','.z','.7-zip','.ace','.lzh','.arj','.gzip','.bz2','.tz');
            if (!in_array($suffix, $zip)) {
                $this->apiReturn(400, [], "只允许上传压缩文件");
            }
            //获取文件名
            $fileName = substr($file['name'], 0, strrpos($file['name'], '.'));
            //上传文件
            $res = Helpers::uploadFileAliOss($suffix, $fileName, $file['tmp_name']);
            if ($res['code'] != 200) {
                $this->apiReturn(400, [], $res['msg']);
            }
            //上传成功且上传的文件跟上次的不同，删除之前的文件
            if (!empty($oldFilePath) && !strncmp($res['data']['src'], $oldFilePath)) {
                $file = substr($oldFilePath, strrpos($oldFilePath, '/') + 1);
                //删除
                Helpers::deleteObject($file);
            }
            $this->apiReturn(200, $res['data'], "文件上传成功");
        }
        $this->apiReturn(400, [], "文件不存在");
    }
}