<?php
/**
 * 租赁柜管理
 */

namespace Controller\TerminalManage;


class LeaseDevice extends BaseTerminalManage
{
    //批量设置检票配置
    public function getDeviceInfo()
    {
        try {
            $deviceKey = I('post.device_key', '', 'strval,trim');
            if (empty($deviceKey)) {
                throw new \Exception('设备特征码参数缺失');
            }
            $params['device_key'] = $deviceKey;
            $this->handleResponse('Device/LeaseDevice/getDeviceInfo', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }
}