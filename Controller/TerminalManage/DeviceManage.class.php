<?php
/**
 * 设备管理
 * Created by PhpStorm.
 * Author: zhujb
 * Date: 2018/12/20
 * Time: 10:57
 */

namespace Controller\TerminalManage;

use Business\Product\TerminalProduct;
use Business\TerminalManage\TerminalManage;
use Library\MessageNotify\OrderNotify;
use Library\SimpleExcel;
use Library\Tools\Helpers;
use Library\Tools\YarClient;
use Library\Util\DebugUtil;
use Model\TerminalManage\CloudConfig;
use Business\TerminalManage\ProductConfig;

class DeviceManage extends BaseTerminalManage
{
    private $serviceType = 'device';
    private $imgPrifix   = 'newzd_img';
    /**
     * 设备审核
     * @Author: zhujb
     * 2018/12/27
     *
     * @param string  device_key        设备编号
     * @param int     origin_type       设备来源 1：票付通采购，2：客户自购
     * @param int     supplier_id       供应商id
     * @param string  relation_file     关联文件路径
     * @param int     package_id        套餐id
     * @param string  auth_start_time   授权开始时间
     * @param string  auth_end_time     授权结束时间
     * @param int     notice_early_days 过期前多天通知客户
     */
    public function deviceCheck()
    {
        $deviceKey       = I('device_key', '', 'strval');
        $originType      = I('origin_type', 1, 'intval');
        $supplierId      = I('supplier_id', 0, 'intval');
        $relationFile    = I('relation_file', '', 'strval');
        $packageId       = I('package_id', 0, 'intval');
        $authStartTime   = I('auth_start_time', '', 'strval');
        $authEndTime     = I('auth_end_time', '', 'strval');
        $noticeEarlyDays = I('notice_early_days', 10, 'intval');

        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $yarClient = new YarClient('device');

        $method1   = 'Device/Device/saveDeviceUser';
        $paramArr1 = [
            'device_key'    => $deviceKey,
            'supplier_id'   => $supplierId,
            'origin_type'   => $originType,
            'relation_file' => $relationFile,
        ];
        $res       = $yarClient->call($method1, $paramArr1);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }

        $method2   = 'Device/Device/saveDevicePackageId';
        $paramArr2 = [
            'device_key' => $deviceKey,
            'op_id'      => $this->_memberInfo['memberID'],
            'package_id' => $packageId,
        ];
        $res       = $yarClient->call($method2, $paramArr2);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }

        $method3   = 'Device/Device/saveDeviceAuth';
        $paramArr3 = [
            'device_key'        => $deviceKey,
            'auth_start_time'   => $authStartTime . ' 00:00:00',
            'auth_end_time'     => $authEndTime . ' 23:59:59',
            'notice_early_days' => $noticeEarlyDays,
            'op_id'             => $this->_memberInfo['memberID'],
        ];
        $res       = $yarClient->call($method3, $paramArr3);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }

        $this->apiReturn(200, [], '审核成功');
    }

    /**
     * 保存设备配置信息
     * @Author: zhujb
     * 2018/12/27
     *
     * @param string  device_key  设备编号
     * @param string  settings    设备配置
     */
    public function saveDeviceSettings()
    {
        $deviceKey = I('device_key', '', 'strval');
        $settings  = I('settings', '', 'strval');

        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'device_key' => $deviceKey,
            'op_id'      => $this->_memberInfo['memberID'],
            'settings'   => $settings,
        );

        $this->handleResponse('Device/Device/saveDeviceSettings', $paramArr);
    }

    /**
     * 保存设备系统配置
     * @Author: zhujb
     * 2019/6/4
     * @return mixed
     */
    public function saveDeviceSystemSetting()
    {
        $deviceKey     = I('post.device_key', '', 'strval');
        $systemSetting = I('post.system_setting', '', 'strval');
        $opId          = I('post.op_id', 0, 'intval');

        if (empty($deviceKey) || empty($systemSetting)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'device_key'     => $deviceKey,
            'op_id'          => $opId,
            'system_setting' => $systemSetting,
            'origin'         => 'web',
        ];

        $this->handleResponse('Device/Device/saveDeviceSystemSetting', $paramArr);
    }

    /**
     * 保存设备客户端配置
     * @Author: zhujb
     * 2019/6/4
     * @return mixed
     */
    public function saveDeviceClientSetting()
    {
        $deviceKey     = I('post.device_key', '', 'strval');
        $clientSetting = I('post.client_setting', '', 'strval');
        $opId          = I('post.op_id', 0, 'intval');

        if (empty($deviceKey) || empty($clientSetting)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'device_key'     => $deviceKey,
            'op_id'          => $opId,
            'client_setting' => $clientSetting,
            'origin'         => 'web',
        ];

        $this->handleResponse('Device/Device/saveDeviceClientSetting', $paramArr);
    }

    /**
     * 保存设备功能配置
     * @Author: zhujb
     * 2019/6/4
     * @return mixed
     */
    public function saveDeviceServiceSetting()
    {
        $deviceKey     = I('post.device_key', '', 'strval');
        $serviceSetting = I('post.service_setting', '', 'strval');
        $opId          = I('post.op_id', 0, 'intval');

        if (empty($deviceKey) || empty($serviceSetting)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'device_key'     => $deviceKey,
            'op_id'          => $opId,
            'service_setting' => $serviceSetting,
            'origin'         => 'web',
        ];

        $this->handleResponse('Device/Device/saveDeviceServiceSetting', $paramArr);
    }

    /**
     * 保存设备授权信息
     * @Author: zhujb
     * 2018/12/27
     *
     * @param string   device_key        设备编号
     * @param string   auth_start_time   授权开始时间
     * @param string   auth_end_time     授权结束时间
     * @param int      notice_early_days 过期前多天通知客户
     */
    public function saveDeviceAuth()
    {
        $deviceKey       = I('device_key', '', 'strval');
        $authStartTime   = I('auth_start_time', '', 'strval');
        $authEndTime     = I('auth_end_time', '', 'strval');
        $noticeEarlyDays = I('notice_early_days', 10, 'intval');

        if (empty($deviceKey) || empty($authStartTime) || empty($authEndTime)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'device_key'        => $deviceKey,
            'auth_start_time'   => $authStartTime . ' 00:00:00',
            'auth_end_time'     => $authEndTime . ' 23:59:59',
            'notice_early_days' => $noticeEarlyDays,
            'op_id'             => $this->_memberInfo['memberID'],
        );

        $this->handleResponse('Device/Device/saveDeviceAuth', $paramArr);
    }

    /**
     * 设备关联供应商
     * @Author: zhujb
     * 2019/1/7
     *
     * @param string   device_key    设备编号
     * @param int      supplier_id   供应商id
     * @param int      origin_type   设备来源 1：票付通采购，2：客户自购
     * @param string   relation_file 关联文件路径
     */
    public function saveDeviceUser()
    {
        $deviceKey    = I('device_key', '', 'strval');
        $supplierId   = I('supplier_id', 0, 'intval');
        $originType   = I('origin_type', 1, 'intval');
        $relationFile = I('relation_file', '', 'strval');

        if (empty($deviceKey) || empty($supplierId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'device_key'    => $deviceKey,
            'supplier_id'   => $supplierId,
            'origin_type'   => $originType,
            'relation_file' => $relationFile,
        );

        $this->handleResponse('Device/Device/saveDeviceUser', $paramArr);
    }

    /**
     * 解绑设备关联供应商
     * @Author: zhujb
     * 2019/1/7
     *
     * @param string   device_key    设备编号
     */
    public function unBindDeviceUser()
    {
        $deviceKey = I('device_key', '', 'strval');

        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'device_key' => $deviceKey,
        );

        $this->handleResponse('Device/Device/unBindDeviceUser', $paramArr);
    }

    /**
     * 保存设备配件
     * @Author: zhujb
     * 2019/1/7
     *
     * @param string   device_key    设备编号
     * @param int      part_ids   配件id
     */
    public function saveDeviceCurParts()
    {
        $deviceKey = I('device_key', '', 'strval');
        $partIds   = I('part_ids', '', 'strval');

        if (empty($deviceKey) || empty($partIds)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'device_key' => $deviceKey,
            'part_ids'   => $partIds,
        );

        $this->handleResponse('Device/Device/saveDeviceCurParts', $paramArr);
    }

    /**
     * 获取设备相关信息
     * @Author: zhujb
     * 2018/12/21
     *
     * @param  string  device_key  设备编号
     */
    public function getDeviceInfo()
    {
        $deviceKey = I('device_key', '', 'strval');
        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'deviceKey'   => $deviceKey,
            'id'          => 0,
            'origin_type' => 'web',
        );

        $this->handleResponse('Device/Device/getGatheringDeviceInfo', $paramArr);
    }

    /**
     * 获取设备管理列表
     * @Author: zhujb
     * 2018/12/27
     *
     * @param  int    page              页码
     * @param  int    page_size         每页条数
     * @param  int    supplier_id       供应商id
     * @param  string device_key        设备编号
     * @param  int    device_type       设备类型 1:手持机，2：自助机，3：闸机
     * @param  string system_version_no 设备型号
     * @param  string auth_status       授权状态 1：未生效，2：未过期，3：已过期，4：未授权 (逗号分隔)
     * @param  int    device_status     设备状态 1：正常，2：出票异常，3：打印机出问题，4：验票问题，5：其他问题
     * @param string  device_name 设备名称
     * @param string  remark 客服备注
     * @param int     status 1：在线2：下线
     * @param int     origin_type 1：票付通采购，2：客户自购
     * @param int     package_id 套餐id
     * @param string  create_begin_time 开始时间
     * @param string  create_end_time 结束时间
     * @param string  auth_begin_time 授权开始
     * @param string  auth_end_time 授权结束
     */
    public function getDeviceManageList()
    {
        $page            = I('page', 1, 'intval');
        $pageSize        = I('page_size', 10, 'intval');
        $supplierId      = I('supplier_id', 0, 'intval');
        $deviceKey       = I('device_key', '', 'strval');
        $deviceType      = I('device_type', 0, 'intval');
        $systemVersionNo = I('system_version_no', '', 'strval');
        $authStatus      = I('auth_status', '', 'strval');
        $deviceStatus    = I('device_status', 0, 'intval');
        $deviceName      = I('device_name', '', 'strval');
        $remark          = I('remark', '', 'strval');
        $status          = I('status', 0, 'intval');
        $originType      = I('origin_type', 0, 'intval');
        $packageId       = I('package_id', 0, 'intval');
        $createBeginTime = I('create_begin_time', '', 'strval');
        $createEndTime   = I('create_end_time', '', 'strval');
        $authBeginTime   = I('auth_begin_time', '', 'strval');
        $authEndTime     = I('auth_end_time', '', 'strval');
        $versionNo       = I('version_no', '', 'strval');
        $adcode          = I('adcode', 0, 'intval');
        $sequenceCode    = I('sequence_code', '', 'strval');
        $export          = I('export', 0, 'intval');
        $installType     = I('install_type', 0, 'intval');
        $productId       = I('product_id', '', 'strval');
        $classify        = I('classify', '', 'strval');
        $classifyStatus  = I('classify_status', '', 'intval');
        $productId       = $productId === '' ? -1 : intval($productId);

        $paramArr = array(
            'page'              => $page,
            'page_size'         => $pageSize,
            'order'             => 'create_time desc',
            'supplier_id'       => $supplierId,
            'device_key'        => $deviceKey,
            'device_type'       => $deviceType,
            'system_version_no' => $systemVersionNo,
            'auth_status'       => $authStatus,
            'device_status'     => $deviceStatus,
            'device_name'       => $deviceName,
            'remark'            => $remark,
            'status'            => $status,
            'origin_type'       => $originType,
            'package_id'        => $packageId,
            'create_begin_time' => $createBeginTime,
            'create_end_time'   => $createEndTime,
            'auth_begin_time'   => $authBeginTime,
            'auth_end_time'     => $authEndTime,
            'version_no'        => $versionNo,
            'adcode'            => $adcode,
            'sequence_code'     => $sequenceCode,
            'keyword'           => '',
            'implementation_id' => 0,
            'onlineId'          => [],
            'install_type'      => $installType,
            'product_id'        => $productId,
            'classify'          => $classify,
            'classify_status'   => $classifyStatus,
        );

        $yarClient = new YarClient($this->serviceType);
        $res       = $yarClient->call('Device/Device/getDeviceManageList', $paramArr);
        if ($res['code'] !== 200) {
            $this->apiReturn(400, [], '请求出错', true);
        }

        $newList = [];
        foreach ($res['res']['data']['list'] as $value) {
            $newList[] = [
                'id'                 => $value['deviceInfo']['id'],
                'sequence_code'      => $value['deviceInfo']['sequence_code'],
                'device_key'         => $value['deviceInfo']['device_key'],
                'device_name'        => $value['deviceInfo']['device_name'],
                'device_type'        => $value['deviceInfo']['device_type'],
                'device_type_text'   => $value['deviceInfo']['device_type_text'],
                'system_version_no'  => $value['deviceInfo']['system_version_no'],
                'version_no'         => $value['deviceInfo']['version_no'],
                'status_text'        => $value['deviceInfo']['status_text'],
                'device_status_text' => $value['deviceInfo']['device_status_text'],
                'create_time'        => $value['deviceInfo']['create_time'],
                'package_id'         => $value['deviceInfo']['package_id'],
                'package_name'       => $value['deviceInfo']['package_name'],
                'online_name'        => $value['deviceInfo']['online_name'],
                'online_account'     => $value['deviceInfo']['online_account'],
                'online_dtype'       => $value['deviceInfo']['online_dtype'],
                'product_id'         => $value['deviceInfo']['product_id'],
                'pro_model_no'       => $value['deviceInfo']['pro_model_no'],
                'auxiliary_status'   => $value['deviceInfo']['auxiliary_status'],
                'auxiliary_key'      => $value['deviceInfo']['auxiliary_key'],

                'supplier_name'    => $value['deviceUserInfo']['supplier_name'],
                'account'          => $value['deviceUserInfo']['account'],
                'accountType'      => $value['deviceUserInfo']['accountType'],
                'origin_type_text' => $value['deviceUserInfo']['origin_type_text'],

                'update_time'      => $value['deviceAuthInfo']['update_time'],
                'auth_start_time'  => $value['deviceAuthInfo']['auth_start_time'],
                'auth_end_time'    => $value['deviceAuthInfo']['auth_end_time'],
                'auth_status_text' => $value['deviceAuthInfo']['auth_status_text'],

                'address' => $value['deviceAddressInfo']['address'],

                'log_data' => $value['deviceUpgradeInfo'],
                'sign_setting' => $value['deviceSignSetting'],
            ];
        }

        $res['res']['data']['list'] = $newList;

        if ($export) {
            $data[0] = [
                '设备类型/型号',
                '设备别名',
                '状态',
                '在线状态',
                '设备唯一编号',
                '设备来源',
                '授权情况',
                '软件套餐',
                '授权开始时间',
                '授权结束时间',
                '所属供应商名称/账号',
                '软件版本/升级方式',
                '信息录入时间',
                '备注',
            ];

            $upgradeTypeText = [1 => '可选升级', 2 => '强制升级'];

            foreach ($res['res']['data']['list'] as $value) {
                $data[] = [
                    $value['device_type_text'] . '/' . $value['pro_model_no'],
                    $value['device_name'],
                    $value['device_status_text'],
                    $value['status_text'],
                    $value['device_key'],
                    $value['origin_type_text'],
                    $value['auth_status_text'],
                    $value['package_name'],
                    $value['auth_start_time'],
                    $value['auth_end_time'],
                    $value['supplier_name'] . '/' . $value['account'],
                    $value['version_no'] . '/' . $upgradeTypeText[$value['log_data']['upgrade_type']] ?: '',
                    $value['create_time'],
                    $value['remark'],
                ];
            }

            $excel    = new SimpleExcel();
            $filename = '设备列表-' . date('Y-m-d', time());
            $excel->addArray($data);
            $excel->generateXML($filename);
            exit;
        }

        $this->apiReturn($res['res']['code'], $res['res']['data'], $res['res']['msg'], true);
    }

    /**
     * 获取设备授权审核列表
     * @Author: zhujb
     * 2018/12/27
     *
     * @param  int       page              页码
     * @param  int       page_size         每页条数
     * @param  string    auth_status       授权状态 1：未生效，2：未过期，3：已过期，4：未授权 (逗号分隔)
     * @param  int       device_type       设备类型 1:手持机，2：自助机，3：闸机
     * @param  string    system_version_no 设备型号
     * @param  int       origin_type       设备来源 1：票付通采购，2：客户自购
     * @param  string    device_key        设备编号
     */
    public function getDeviceAuthList()
    {
        $page            = I('page', 1, 'intval');
        $pageSize        = I('page_size', 10, 'intval');
        $authStatus      = I('auth_status', '', 'strval');
        $deviceType      = I('device_type', 0, 'intval');
        $systemVersionNo = I('system_version_no', '', 'strval');
        $originType      = I('origin_type', 1, 'intval');
        $deviceKey       = I('device_key', '', 'strval');

        $paramArr = array(
            'page'              => $page,
            'page_size'         => $pageSize,
            'auth_status'       => $authStatus,
            'device_type'       => $deviceType,
            'system_version_no' => $systemVersionNo,
            'origin_type'       => $originType,
            'device_key'        => $deviceKey,
        );

        $this->handleResponse('Device/Device/getDeviceAuthList', $paramArr);
    }

    /**
     * 批量修改设备状态
     * @Author: zhujb
     * 2019/1/2
     *
     * @param string   device_key     设备编号
     * @param int      device_status  设备状态 1：正常，2：出票异常，3：打印机出问题，4：验票问题，5：其他问题
     */
    public function updateDeviceStatus()
    {
        $deviceKey    = I('device_key', '', 'strval');
        $deviceStatus = I('device_status', 1, 'intval');

        $paramArr = array(
            'device_key'    => $deviceKey,
            'op_id'         => $this->_memberInfo['memberID'],
            'device_status' => $deviceStatus,
            'memo'          => '',
        );

        $this->handleResponse('Device/Device/saveDeviceStatus', $paramArr);
    }
    /**
     * 双向闸机-主机切换（绑定主机or换绑主机)
     * 默认设备都是主机，如果被切换的主机有绑定关系 则需要解绑
     * 如果没有绑定关系 则直接绑定
     * <AUTHOR>
     * @param string $main_key  设为主机的特征码
     * @param string $follow_key  设为副机的特征码
     * @return mixed
     */
    public function bindAuxiliaryMachine()
    {
        $mainKey = I('main_key', '', 'strval');
        $followKey = I('follow_key', '', 'strval');
        $params = ['main_key' => $mainKey, 'follow_key' => $followKey, 'op_id' => $this->_memberInfo['memberID'] ?? 0];
        $this->handleResponse('Device/Device/bindAuxiliaryMachine', $params);
    }

    /**
     * 双向闸机-副机解绑
     * <AUTHOR>
     * @param string $device_key 要解绑设备特征码
     * @return mixed
     */
    public function unbindAuxiliaryMachine()
    {
        $deviceKey = I('device_key', '', 'strval');
        $this->handleResponse('Device/Device/unbindAuxiliaryMachine', ['device_key' => $deviceKey, 'op_id' => $this->_memberInfo['memberID'] ?? 0]);
    }
    /**
     * 根据供应商查看设备汇总
     * @Author: zhujb
     * 2019/1/2
     *
     * @param  int     supplier_id         供应商id
     * @param  int     device_type         设备类型 1:手持机，2：自助机，3：闸机
     * @param  string  system_version_no   设备型号
     * @param  int     auth_status         授权状态 1：未生效，2：未过期，3：已过期，4：未授权
     * @param  int     origin_type         设备来源 1：票付通采购，2：客户自购
     * @param  int     status              在线状态 1:在线,2:离线
     * @param  int     package_id          套餐id
     * @param  string  create_begin_time   创建开始时间
     * @param  string  create_end_time     创建结束时间
     * @param  string  auth_begin_time     授权开始时间
     * @param  string  auth_end_time       授权结束时间
     */
    public function summaryDeviceBySupplierId()
    {
        $supplierId      = I('supplier_id', 0, 'intval');
        $deviceType      = I('device_type', 0, 'intval');
        $systemVersionNo = I('system_version_no', '', 'strval');
        $authStatus      = I('auth_status', 0, 'intval');
        $originType      = I('origin_type', 0, 'intval');
        $status          = I('status', 0, 'intval');
        $packageId       = I('package_id', 0, 'intval');
        $createBeginTime = I('create_begin_time', '', 'strval');
        $createEndTime   = I('create_end_time', '', 'strval');
        $authBeginTime   = I('auth_begin_time', '', 'strval');
        $authEndTime     = I('auth_end_time', '', 'strval');

        $paramArr = array(
            'supplier_id'       => $supplierId,
            'device_type'       => $deviceType,
            'system_version_no' => $systemVersionNo,
            'auth_status'       => $authStatus,
            'origin_type'       => $originType,
            'status'            => $status,
            'package_id'        => $packageId,
            'create_begin_time' => $createBeginTime,
            'create_end_time'   => $createEndTime,
            'auth_begin_time'   => $authBeginTime,
            'auth_end_time'     => $authEndTime,
        );

        $this->handleResponse('Device/Device/getSummaryDeviceBySupplierId', $paramArr);
    }

    /**
     * 根据设备类型查询设备汇总信息
     * @Author: zhujb
     * 2019/1/2
     *
     * @param  int    device_type          设备类型 1:手持机，2：自助机，3：闸机
     * @param  string system_version_no    设备型号
     * @param  int    origin_type          设备来源 1：票付通采购，2：客户自购
     * @param  int    status               在线状态 1:在线,2:离线
     * @param  int    package_id           套餐id
     * @param  int    auth_status          在线状态 1:在线,2:离线
     */
    public function summaryDeviceByDeviceType()
    {
        $deviceType      = I('device_type', 0, 'intval');
        $systemVersionNo = I('system_version_no', '', 'strval');
        $originType      = I('origin_type', 0, 'intval');
        $status          = I('status', 0, 'intval');
        $packageId       = I('package_id', 0, 'intval');
        $authStatus      = I('auth_status', 0, 'intval');

        if (empty($deviceType)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'device_type'       => $deviceType,
            'system_version_no' => $systemVersionNo,
            'origin_type'       => $originType,
            'status'            => $status,
            'package_id'        => $packageId,
            'auth_status'       => $authStatus,
        );

        $this->handleResponse('Device/Device/getSummaryDeviceByDeviceType', $paramArr);
    }

    /**
     * 删除设备 （重置设备为未关联，未授权）
     * @Author: zhujb
     * 2019/1/2
     *
     * @param  string   device_key    设备编号
     */
    public function resetDevice()
    {
        $deviceKey = I('device_key', '', 'strval');

        $paramArr = array(
            'device_key' => $deviceKey,
            'op_id'      => $this->_memberInfo['memberID'],
        );

        $this->handleResponse('Device/Device/resetDevice', $paramArr);
    }

    /**
     * 获取更新的版本号列表
     * @Author: zhujb
     * 2019/1/3
     *
     * @param  string    device_type    设备类型 1:手持机，2：自助机，3：闸机
     * @param  int       page           页码
     * @param  int       page_size      每页条数
     */
    public function getVersionList()
    {
        $deviceType    = I('device_type', 1, 'intval');
        $productId     = I('product_id', 0, 'intval');
        $page          = I('page', 1, 'intval');
        $pageSize      = I('page_size', 10, 'intval');
        $versionManage = new \Model\TerminalManage\VersionManage();
        switch ($deviceType) {
            case 1:
                // 手持机
                $category = [0, 7, 21, 25];
                break;
            case 2:
                // 自助机
                $category = [14];
                break;
            case 3:
                //闸机
                $category = [8, 23];
                break;
            default:
                $category = [];
        }

        $versionList = $versionManage->getVersionManages($page, $pageSize, $category, '',
            'AppUrl,FileContent,versionCode,VersionManagerNo,Category,TrDateTime,UpdateDescription',
            'VersionManagerId desc', $deviceType, $productId);
        $total       = $versionManage->getVersionManagesCount($category, '', $deviceType, $productId);

        $res = [
            'list'      => $versionList,
            'total'     => $total,
            'page_size' => $pageSize,
        ];

        $this->apiReturn(200, $res, '获取成功');
    }

    /**
     * 推送版本数据进行升级
     * @Author: zhujb
     * 2019/1/3
     *
     * @param  string   update_description   更新描述
     * @param  string   app_url              更新包地址
     * @param  int      upgrade_type         升级类型：1：可选升级，2：强制升级
     * @param  string   version_code         数字软件版本号
     * @param  string   version_manager_no   文本软件版本号
     * @param  string   device_key           设备编号
     */
    public function pushVersion()
    {
        $updateDescription = I('update_description', '', 'strval');
        $appUrl            = I('app_url', '', 'strval');
        $updateType        = I('upgrade_type', 1, 'intval');
        $versionCode       = I('version_code', '', 'strval');
        $versionManagerNo  = I('version_manager_no', '', 'strval');
        $deviceKey         = I('device_key', '', 'strval,trim');

        if (empty($appUrl)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'app_url'            => $appUrl,
            'device_key'         => $deviceKey,
            'op_id'              => $this->_memberInfo['memberID'],
            'upgrade_type'       => $updateType,
            'version_code'       => $versionCode,
            'version_manager_no' => $versionManagerNo,
            'update_description' => $updateDescription,
        );

        $this->handleResponse('Device/Device/upgradeDevice', $paramArr);
    }

    /**
     * 注册mqtt连接账号
     * @Author: zhujb
     * 2019/1/4
     *
     * @param   string   username   用户名
     */
    public function registerDeviceMqttAccount()
    {
        $username = I('username', '', 'strval');
        if (empty($username)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'username' => $username,
        ];

        $this->handleResponse('Device/Device/registerDeviceMqttAccount', $paramArr);
    }

    /**
     * 获取设备型号的列表
     * @Author: zhujb
     * 2019/1/15
     */
    public function getDeviceSystemVersionList()
    {
        $deviceType = I('device_type', 0, 'intval');
        $paramArr   = [
            'device_type' => $deviceType,
        ];
        $this->handleResponse('Device/Device/getDeviceSystemVersionList', $paramArr);
    }

    /**
     * 获取设备套餐名称的列表
     * @Author: zhujb
     * 2019/1/15
     */
    public function getDevicePackageNameList()
    {
        $deviceType = I('device_type', 0, 'intval');
        if (empty($deviceType)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'device_type' => $deviceType,
        ];

        $this->handleResponse('Device/DevicePackage/getDevicePackageNameList', $paramArr);
    }

    /**
     * 获取设备简要信息 （不需要了）
     * @Author: zhujb
     * 2019/1/15
     *
     * @param string  id          设备id (逗号分隔)
     * @param string  device_key  设备编号(逗号分隔)
     */
    public function getDeviceIntroduction()
    {
        $this->apiReturn(200, [], '');
        //$id        = I('id', '', 'strval');
        //$deviceKey = I('device_key', '', 'strval');
        //
        //$paramArr = [
        //    'id'         => $id,
        //    'device_key' => $deviceKey,
        //];
        //
        //$this->handleResponse('Device/Device/getDeviceIntroduction', $paramArr);
    }

    /**
     * 获取设备操作日志
     * @Author: zhujb
     * 2019/1/24
     *
     * @param  int     page        页码
     * @param  int     page_size   每页条数
     * @param  string  device_key  设备编号
     * @param  int     op_type     操作类型 1：审核，2：修改设备权限，3：修改授权配置，4：重置设备，5：推送设备升级, 6:修改设备信息 7:推送二维码 8: 销毁二维码
     * @param string   begin_time  起始时间
     * @param string   end_time    结束时间
     */
    public function getDeviceLogList()
    {
        $page      = I('page', 1, 'intval');
        $pageSize  = I('page_size', 10, 'intval');
        $deviceKey = I('device_key', '', 'strval');
        $opType    = I('op_type', 0, 'intval');
        $beginTime = I('begin_time', '', 'strval');
        $endTime   = I('end_time', '', 'strval');

        if (empty($deviceKey) && empty($opType)) {
            $this->apiReturn(200, ['list' => [], 'total' => 0, 'page_size' => $pageSize], '获取成功');
        }

        $paramArr = [
            'page'       => $page,
            'page_size'  => $pageSize,
            'device_key' => $deviceKey,
            'op_type'    => $opType,
            'begin_time' => $beginTime,
            'end_time'   => $endTime,
        ];

        $this->handleResponse('Device/Device/getDeviceLogList', $paramArr);
    }

    /**
     * zd平台设置别名
     * @Author: zhujb
     * 2019/3/19
     *
     * @param  string    device_key     设备编号
     * @param  string    device_name    设备别名
     */
    public function saveDeviceName()
    {
        $deviceKey  = I('post.device_key', '', 'strval');
        $deviceName = I('post.device_name', '', 'strval');

        if (empty($deviceKey) || empty($deviceName)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'device_key'  => $deviceKey,
            'device_name' => $deviceName,
        ];

        $this->handleResponse('Device/Device/saveDeviceName', $paramArr);
    }

    /**
     * zd平台设置客服备注
     * @Author: zhujb
     * 2019/3/19
     *
     * @param    string     device_key    设备编号
     * @param    string     device_remark 设备客服备注
     */
    public function saveDeviceRemark()
    {
        $deviceKey    = I('post.device_key', '', 'strval');
        $deviceRemark = I('post.device_remark', '', 'strval');

        if (empty($deviceKey) || empty($deviceRemark)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'device_key' => $deviceKey,
            'remark'     => $deviceRemark,
        ];

        $this->handleResponse('Device/Device/saveDeviceRemark', $paramArr);
    }

    /**
     * zd平台设置设备套餐
     * @Author: zhujb
     * 2019/3/19
     *
     * @param   string      device_key    设备编号
     * @param   int         package_id    套餐id
     */
    public function saveDevicePackage()
    {
        $deviceKey = I('post.device_key', '', 'strval');
        $packageId = I('post.package_id', 0, 'intval');

        if (empty($deviceKey) || empty($packageId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'device_key' => $deviceKey,
            'op_id'      => $this->_memberInfo['memberID'],
            'package_id' => $packageId,
        ];

        $this->handleResponse('Device/Device/saveDevicePackageId', $paramArr);
    }

    /**
     * 添加套餐信息
     * @Author: zhujb
     * 2019/3/19
     *
     * @param   string    package_name    套餐名称
     * @param   int       device_type     设备类型 1:手持机，2：自助机，3：闸机
     * @param   string    description     套餐描述
     * @param   string    func_ids        功能id  (逗号分隔)
     * @param   int       status          套餐状态 1：上架，2：下架
     */
    public function addDevicePackage()
    {
        $packageName = I('package_name', '', 'strval');
        $deviceType  = I('device_type', 1, 'strval');
        $description = I('description', '', 'strval');
        $funcIds     = I('func_ids', '', 'strval');
        $status      = I('status', 1, 'intval');

        if (empty($packageName) || empty($deviceType)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'package_name' => $packageName,
            'device_type'  => $deviceType,
            'description'  => $description,
            'func_ids'     => $funcIds,
            'status'       => $status,
        ];

        $this->handleResponse('Device/DevicePackage/addDevicePackage', $paramArr);
    }

    /**
     * 编辑套餐信息
     * @Author: zhujb
     * 2019/3/19
     *
     * @param   int     package_id       套餐id
     * @param   int     status           套餐状态 1：上架，2：下架
     * @param   string  package_name     套餐名称
     * @param   string  description      套餐描述
     * @param   string  func_ids         功能id （逗号分隔）
     */
    public function editDevicePackage()
    {
        $packageId   = I('package_id', 0, 'intval');
        $status      = I('status', 1, 'intval');
        $packageName = I('package_name', '', 'strval');
        $description = I('description', '', 'strval');
        $funcIds     = I('func_ids', '', 'strval');

        $paramArr = [
            'package_id'   => $packageId,
            'package_name' => $packageName,
            'description'  => $description,
            'func_ids'     => $funcIds,
            'status'       => $status,
        ];

        $this->handleResponse('Device/DevicePackage/editDevicePackage', $paramArr);
    }

    /**
     * 删除套餐
     * @Author: zhujb
     * 2019/3/19
     *
     * @param  int   package_id   套餐id
     */
    public function delDevicePackage()
    {
        $packageId = I('package_id', 0, 'intval');
        $paramArr  = [
            'package_id' => $packageId,
        ];

        $this->handleResponse('Device/DevicePackage/delDevicePackage', $paramArr);
    }

    /**
     * 获取套餐列表
     * @Author: zhujb
     * 2019/3/22
     *
     * @param   int     device_type      设备类型 1:手持机，2：自助机，3：闸机
     * @param   int     status           套餐状态 1：上架，2：下架
     * @param   int     page             页码
     * @param   int     page_size        每页条数
     */
    public function getDevicePackageList()
    {
        $deviceType = I('post.device_type', 1, 'intval');
        $status     = I('post.status', 1, 'intval');
        $page       = I('post.page', 1, 'intval');
        $pageSize   = I('post.page_size', 10, 'intval');

        $paramArr = [
            'page'        => $page,
            'page_size'   => $pageSize,
            'device_type' => $deviceType,
            'status'      => $status,
        ];

        $this->handleResponse('Device/DevicePackage/getDevicePackageList', $paramArr);
    }

    /**
     * 根据package_id 获取套餐详情
     * @Author: zhujb
     * 2019/4/17
     *
     * @param int package_id 套餐id
     */
    public function getDevicePackageInfo()
    {
        $packageId = I('post.package_id', 0, 'intval');

        if (empty($packageId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'package_id' => $packageId,
        ];

        $this->handleResponse('Device/DevicePackage/getDevicePackageInfo', $paramArr);
    }

    /**
     * 添加功能
     * @Author: zhujb
     * 2019/3/22
     *
     * @param   string   func_name    功能名称
     * @param   string   func_field   功能字段
     * @param   int      device_type  设备类型 1:手持机，2：自助机，3：闸机
     * @param   string   remark       功能备注
     */
    public function addDeviceFunc()
    {
        $funcName   = I('post.func_name', '', 'strval');
        $funcField  = I('post.func_field', '', 'strval');
        $deviceType = I('post.device_type', 1, 'intval');
        $remark     = I('post.remark', '', 'strval');

        if (empty($funcName) || empty($funcField) || empty($deviceType)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'func_name'   => $funcName,
            'func_field'  => $funcField,
            'device_type' => $deviceType,
            'remark'      => $remark,
        ];

        $this->handleResponse('Device/DevicePackage/addDeviceFunc', $paramArr);
    }

    /**
     * 获取功能列表
     * @Author: zhujb
     * 2019/3/22
     *
     * @param   int    device_type   设备类型 1:手持机，2：自助机，3：闸机
     */
    public function getDeviceFuncs()
    {
        $deviceType = I('post.device_type', 0, 'intval');
        if (empty($deviceType)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'device_type' => $deviceType,
        ];

        $this->handleResponse('Device/DevicePackage/getDeviceFuncs', $paramArr);
    }

    /**
     * 推送二维码到闸机
     * @Author: zhujb
     * 2019/3/28
     *
     * @param  int     code_id     二维码id
     * @param  string  device_key  设备编号
     */
    public function pushDeviceGateCode()
    {
        $codeId    = I('post.code_id', 0, 'intval');
        $deviceKey = I('post.device_key', '', 'strval');
        $opId      = $this->_memberInfo['memberID'];

        if (empty($codeId) || empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'code_id'    => $codeId,
            'device_key' => $deviceKey,
            'op_id'      => $opId,
        ];

        $this->handleResponse('Device/Device/pushGateCode', $paramArr);
    }

    /**
     * 销毁二维码
     * @Author: zhujb
     * 2019/3/28
     *
     * @param   string  device_key   设备编号
     * @param   int     qrcode_type  1：签到码，2：购票码，3：扩展板波特率设置，4：机芯设置，5：闸机界面信息购票码
     */
    public function destroyGateCode()
    {
        $deviceKey  = I('post.device_key', '', 'strval');
        $qrcodeType = I('post.qrcode_type', 0, 'intval');
        $opId       = $this->_memberInfo['memberID'];

        if (empty($qrcodeType) || empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'device_key'  => $deviceKey,
            'qrcode_type' => $qrcodeType,
            'op_id'       => $opId,
        ];

        $this->handleResponse('Device/Device/destroyGateCode', $paramArr);
    }

    /**
     * 添加闸机二维码
     * @Author: zhujb
     * 2019/3/28
     *
     * @param   string    qrcode_name   二维码名称
     * @param   int       qrcode_type   1：签到码，2：购票码，3：扩展板波特率设置，4：机芯设置，5：闸机界面信息购票码
     * @param   string    qrcode_data   二维码数据
     * @param   string    remark        备注
     */
    public function addGateCode()
    {
        $qrcodeName = I('post.qrcode_name', '', 'strval');
        $qrcodeType = I('post.qrcode_type', 0, 'intval');
        $qrcodeData = I('post.qrcode_data', '', 'strval');
        $remark     = I('post.remark', '', 'strval');

        if (empty($qrcodeName) || empty($qrcodeType) || empty($qrcodeData)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'qrcode_name' => $qrcodeName,
            'qrcode_type' => $qrcodeType,
            'qrcode_data' => $qrcodeData,
            'remark'      => $remark,
        ];

        $this->handleResponse('Device/Device/addGateCode', $paramArr);
    }

    /**
     * 编辑闸机二维码
     * @Author: zhujb
     * 2019/3/28
     *
     * @param   int     code_id        二维码id
     * @param   string  qrcode_name    二维码名称
     * @param   string  qrcode_data    二维码数据
     * @param   string  remark         备注
     */
    public function editGateCode()
    {
        $codeId     = I('post.code_id', 0, 'intval');
        $qrcodeName = I('post.qrcode_name', '', 'strval');
        $qrcodeData = I('post.qrcode_data', '', 'strval');
        $remark     = I('post.remark', '', 'strval');

        if (empty($qrcodeName) || empty($codeId) || empty($qrcodeData)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'code_id'     => $codeId,
            'qrcode_name' => $qrcodeName,
            'qrcode_data' => $qrcodeData,
            'remark'      => $remark,
        ];

        $this->handleResponse('Device/Device/editGateCode', $paramArr);
    }

    /**
     * 删除闸机二维码
     * @Author: zhujb
     * 2019/3/28
     *
     * @param    int     code_id     二维码id
     */
    public function delGateCode()
    {
        $codeId = I('post.code_id', 0, 'intval');
        if (empty($codeId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'code_id' => $codeId,
        ];

        $this->handleResponse('Device/Device/delGateCode', $paramArr);
    }
    /**
     * 获取闸机设备key配置列表
     * @param string apply_did  供应商ID
     * @param string sn          设备sn
     * @param string device_key   设备key
     *
     * */
    public function getAliDeviceKeyConfig()
    {
        $applyDid   = I('post.apply_did', 0, 'intval');
        $sn         = I('post.sn', '', 'strval');
        $device_key = I('post.device_key', '', 'strval');
        $page       = I('post.page', 1, 'intval');
        $pageSize   = I('post.page_size', 10, 'intval');

        $paramArr = [
            'apply_did'  => $applyDid,
            'sn'         => $sn,
            'device_key' => $device_key,
            'page'       => $page,
            'page_size'  => $pageSize,
        ];

        $this->handleResponse('Device/Device/getAliDeviceKeyConfig', $paramArr);
    }

    /**
     * 新增或编辑闸机设备key配置
     * @param string apply_did  供应商ID
     * @param string sn          设备sn
     * @param string device_key   设备key
     *
     * */
    public function addOrUpdateAliDeviceKeyConfig()
    {
        $applyDid   = I('post.apply_did', '', 'intval');
        $sn         = I('post.sn', '', 'strval');
        $device_key = I('post.device_key', '', 'strval');
        $id         = I('post.id', 0, 'intval');
        $paramArr = [
            'id'         => $id,
            'apply_did'  => $applyDid,
            'sn'         => $sn,
            'device_key' => $device_key,
        ];

        $this->handleResponse('Device/Device/addOrUpdateAliDeviceKeyConfig', $paramArr);
    }

    public function delAliDeviceKeyConfig()
    {
        $id         = I('post.id', 0, 'intval');
        $paramArr = [
            'id'         => $id
        ];

        $this->handleResponse('Device/Device/delAliDeviceKeyConfig', $paramArr);
    }

    /**
     * 获取闸机二维码列表
     * @Author: zhujb
     * 2019/3/28
     *
     * @param    string     qrcode_name    二维码名称
     * @param    string     remark         备注
     * @param    int        qrcode_type    二维码类型 1：签到码，2：购票码，3：扩展板波特率设置，4：机芯设置，5：闸机界面信息购票码
     * @param    string     begin_time     开始时间
     * @param    string     end_time       结束时间
     * @param    int        page           页码
     * @param    int        page_size      每页条数
     */
    public function getGateCodeList()
    {
        $qrcodeName = I('post.qrcode_name', '', 'strval');
        $remark     = I('post.remark', '', 'strval');
        $qrcodeType = I('post.qrcode_type', 0, 'intval');
        $beginTime  = I('post.begin_time', '', 'strval');
        $endTime    = I('post.end_time', '', 'strval');
        $page       = I('post.page', 1, 'intval');
        $pageSize   = I('post.page_size', 10, 'intval');

        $paramArr = [
            'qrcode_name' => $qrcodeName,
            'remark'      => $remark,
            'qrcode_type' => $qrcodeType,
            'begin_time'  => $beginTime,
            'end_time'    => $endTime,
            'page'        => $page,
            'page_size'   => $pageSize,
        ];

        $this->handleResponse('Device/Device/getGateCodeList', $paramArr);
    }

    /**
     * 搜索设备
     * @Author: zhujb
     * 2019/4/4
     *
     * @param    int       supplier_id     供应商id
     * @param    string    device_key      设备编号
     * @param    string    device_name     设备名称
     * @param    int       device_type     1：手持机，2：自助机，3：闸机
     */
    public function searchDevice()
    {
        $supplierId = I('post.supplier_id', 0, 'intval');
        $deviceKey  = I('post.device_key', '', 'strval');
        $deviceName = I('post.device_name', '', 'strval');
        $deviceType = I('post.device_type', 0, 'intval');

        $paramArr = [
            'supplier_id' => $supplierId,
            'device_key'  => $deviceKey,
            'device_name' => $deviceName,
            'device_type' => $deviceType,
        ];

        $this->handleResponse('Device/Device/searchDevice', $paramArr);
    }

    /**
     * 搜索套餐
     * @Author: zhujb
     * 2019/4/18
     */
    public function searchPackage()
    {
        $packageName = I('post.package_name', '', 'strval');

        if (empty($packageName)) {
            $this->apiReturn(204, [], '无数据', true);
        }

        $paramArr = [
            'package_name' => $packageName,
        ];

        $this->handleResponse('Device/DevicePackage/searchPackage', $paramArr);
    }

    /**
     * 生成二维码
     * @Author: zhujb
     * 2019/4/11
     */
    public function createQrcodeImg()
    {
        $data  = $_GET['data'];
        $level = $_GET['size'];
        $size  = $_GET['size'];
        include_once HTML_DIR . 'Service/Library/Tools/phpqrcode.class.php';
        \QRcode::png($data, false, $level, $size);
    }

    /**
     * 设置设备的打印配置
     * @Author: zhujb
     * 2019/5/27
     * @return bool
     */
    public function saveDevicePrintSetting()
    {
        $deviceKey    = I('post.device_key', '', 'strval');
        $printSetting = I('post.print_setting', '', 'strval');

        if (empty($deviceKey) || empty($printSetting)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'device_key'    => $deviceKey,
            'print_setting' => $printSetting,
            'origin'        => 'web',
        ];

        $this->handleResponse('Device/Device/saveDevicePrintSetting', $paramArr);
    }

    /**
     * 获取手持机本地操作日志列表
     * @Author: zhujb
     * 2019/5/28
     */
    public function getDeviceHandMachineOpLogList()
    {
        $page        = I('post.page', 1, 'intval');
        $pageSize    = I('post.page_size', 10, 'intval');
        $deviceKey   = I('post.device_key', '', 'strval,trim');
        $ordernum    = I('post.ordernum', '', 'strval,trim');
        $token       = I('post.token', '', 'strval,trim');
        $tel         = I('post.tel', '', 'strval,trim');
        $opType      = I('post.op_type', 0, 'intval');
        $opTimeBegin = I('post.op_time_begin', '', 'strval,trim');
        $opTimeEnd   = I('post.op_time_end', '', 'strval,trim');

        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'device_key'    => $deviceKey,
            'page'          => $page,
            'page_size'     => $pageSize,
            'ordernum'      => $ordernum,
            'token'         => $token,
            'tel'           => $tel,
            'op_type'       => $opType,
            'op_time_begin' => $opTimeBegin,
            'op_time_end'   => $opTimeEnd,
        ];

        $this->handleResponse('Device/Device/getDeviceHandMachineOpLogList', $paramArr);
    }

    /**
     * 添加设备类型
     * @Author: zhujb
     * 2019/5/28
     */
    public function addDeviceType()
    {
        $typeName = I('post.type_name', '', 'strval,trim');
        $remark   = I('post.remark', '', 'strval,trim');

        if (empty($typeName)) {
            $this->apiReturn(204, [], '类型为必填');
        }

        $paramArr = [
            'type_name' => $typeName,
            'remark'    => $remark,
        ];

        $this->handleResponse('Device/Device/addDeviceType', $paramArr);
    }

    /**
     * 编辑设备类型
     * @Author: zhujb
     * 2019/5/28
     */
    public function editDeviceType()
    {
        $id       = I('post.id', 0, 'intval');
        $typeName = I('post.type_name', '', 'strval,trim');
        $remark   = I('post.remark', '', 'strval,trim');

        if (empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (empty($typeName)) {
            $this->apiReturn(204, [], '名称必填');
        }

        $paramArr = [
            'id'        => $id,
            'type_name' => $typeName,
            'remark'    => $remark,
        ];

        $this->handleResponse('Device/Device/editDeviceType', $paramArr);
    }

    /**
     * 获取设备类型列表
     * @Author: zhujb
     * 2019/5/28
     */
    public function getDeviceTypeList()
    {
        $page     = I('post.page', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');

        $paramArr = [
            'page'     => $page,
            'pageSize' => $pageSize,
        ];

        $this->handleResponse('Device/Device/getDeviceTypeList', $paramArr);
    }

    /**
     * 获取单条设备类型信息
     * @Author: zhujb
     * 2019/5/28
     */
    public function getOneDeviceType()
    {
        $id = I('post.id', 0, 'intval');

        if (empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'id' => $id,
        ];

        $this->handleResponse('Device/Device/getOneDeviceType', $paramArr);
    }

    /**
     * 获取所有设备类型
     * @Author: zhujb
     * 2019/5/28
     */
    public function getAllDeviceType()
    {
        $paramArr = [];
        $this->handleResponse('Device/Device/getAllDeviceType', $paramArr);
    }

    /**
     * 添加设备厂商
     * @Author: zhujb
     * 2019/5/28
     */
    public function addDeviceCompany()
    {
        $companyName = I('post.company_name', '', 'strval,trim');
        $typeIds     = I('post.type_ids', '', 'strval,trim');
        $logo        = I('post.logo', '', 'strval,trim');
        $address     = I('post.address', '', 'strval,trim');

        if (empty($companyName)) {
            $this->apiReturn(204, [], '厂商名称必填');
        }

        if (empty($typeIds)) {
            $this->apiReturn(204, [], '类型必填');
        }

        $paramArr = [
            'company_name' => $companyName,
            'type_ids'     => $typeIds,
            'logo'         => $logo,
            'address'      => $address,
        ];
        $this->handleResponse('Device/Device/addDeviceCompany', $paramArr);
    }

    /**
     * 编辑设备厂商
     * @Author: zhujb
     * 2019/5/28
     */
    public function editDeviceCompany()
    {
        $id          = I('post.id', 0, 'intval');
        $companyName = I('post.company_name', '', 'strval,trim');
        $typeIds     = I('post.type_ids', '', 'strval,trim');
        $logo        = I('post.logo', '', 'strval,trim');
        $address     = I('post.address', '', 'strval,trim');

        if (empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (empty($companyName)) {
            $this->apiReturn(204, [], '厂商名称必填');
        }

        if (empty($typeIds)) {
            $this->apiReturn(204, [], '类型必填');
        }

        $paramArr = [
            'id'           => $id,
            'company_name' => $companyName,
            'type_ids'     => $typeIds,
            'logo'         => $logo,
            'address'      => $address,
        ];
        $this->handleResponse('Device/Device/editDeviceCompany', $paramArr);
    }

    /**
     * 获取设备厂商列表
     * @Author: zhujb
     * 2019/5/28
     */
    public function getDeviceCompanyList()
    {
        $page     = I('post.page', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');

        $paramArr = [
            'page'      => $page,
            'page_size' => $pageSize,
        ];
        $this->handleResponse('Device/Device/getDeviceCompanyList', $paramArr);
    }

    /**
     * 获取所有厂商
     * @Author: zhujb
     * 2019/5/28
     */
    public function getAllDeviceCompany()
    {
        $paramArr = [];
        $this->handleResponse('Device/Device/getAllDeviceCompany', $paramArr);
    }

    /**
     * 获取单条厂商信息
     * @Author: zhujb
     * 2019/5/28
     */
    public function getOneDeviceCompany()
    {
        $id = I('post.id', 0, 'intval');

        if (empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'id' => $id,
        ];
        $this->handleResponse('Device/Device/getOneDeviceCompany', $paramArr);
    }

    /**
     * 添加设备配件
     * @Author: zhujb
     * 2019/5/28
     */
    public function addDevicePart()
    {
        $partName    = I('post.part_name', '', 'strval,trim');
        $partModelNo = I('post.part_model_no', '', 'strval,trim');
        $companyId   = I('post.company_id', 0, 'intval');
        $remark      = I('post.remark', '', 'strval');

        if (empty($partName)) {
            $this->apiReturn(204, [], '名称必填');
        }

        if (empty($partModelNo)) {
            $this->apiReturn(204, [], '型号必填');
        }

        if (empty($companyId)) {
            $this->apiReturn(204, [], '厂商必填');
        }

        $paramArr = [
            'part_name'     => $partName,
            'part_model_no' => $partModelNo,
            'company_id'    => $companyId,
            'remark'        => $remark,
        ];
        $this->handleResponse('Device/Device/addDevicePart', $paramArr);
    }

    /**
     * 编辑设备配件
     * @Author: zhujb
     * 2019/5/28
     */
    public function editDevicePart()
    {
        $id          = I('post.id', 0, 'intval');
        $partName    = I('post.part_name', '', 'strval,trim');
        $partModelNo = I('post.part_model_no', '', 'strval,trim');
        $companyId   = I('post.company_id', 0, 'intval');
        $remark      = I('post.remark', '', 'strval');

        if (empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (empty($partName)) {
            $this->apiReturn(204, [], '名称必填');
        }

        if (empty($partModelNo)) {
            $this->apiReturn(204, [], '型号必填');
        }

        if (empty($companyId)) {
            $this->apiReturn(204, [], '厂商必填');
        }

        $paramArr = [
            'id'            => $id,
            'part_name'     => $partName,
            'part_model_no' => $partModelNo,
            'company_id'    => $companyId,
            'remark'        => $remark,
        ];
        $this->handleResponse('Device/Device/editDevicePart', $paramArr);
    }

    /**
     * 获取设备配件列表
     * @Author: zhujb
     * 2019/5/28
     */
    public function getDevicePartList()
    {
        $page     = I('post.page', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');
        $keyword  = I('post.keyword', '', 'strval');

        $paramArr = [
            'page'      => $page,
            'page_size' => $pageSize,
            'keyword'   => $keyword,
        ];
        $this->handleResponse('Device/Device/getDevicePartList', $paramArr);
    }

    /**
     * 获取所有配件
     * @Author: zhujb
     * 2019/5/28
     */
    public function getAllDevicePart()
    {
        $paramArr = [];
        $this->handleResponse('Device/Device/getAllDevicePart', $paramArr);
    }

    /**
     * 查询单条配件
     * @Author: zhujb
     * 2019/5/28
     */
    public function getOneDevicePart()
    {
        $id = I('post.id', 0, 'intval');

        if (empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'id' => $id,
        ];
        $this->handleResponse('Device/Device/getOneDevicePart', $paramArr);
    }

    /**
     * 添加设备产品
     * @Author: zhujb
     * 2019/5/28
     */
    public function addDeviceProduct()
    {
        $productName = I('post.product_name', '', 'strval,trim');
        $proModelNo  = I('post.pro_model_no', '', 'strval,trim');
        $companyId   = I('post.company_id', 0, 'intval');
        $typeId      = I('post.type_id', 0, 'intval');
        $partIds     = I('post.part_ids', '', 'strval,trim');
        $remark      = I('post.remark', '', 'strval,trim');
        $img         = I('post.img', '', 'strval,trim');

        if (empty($productName)) {
            $this->apiReturn(204, [], '名称必填');
        }

        if (empty($proModelNo)) {
            $this->apiReturn(204, [], '型号必填');
        }

        if (empty($typeId)) {
            $this->apiReturn(204, [], '类型必填');
        }

        if (empty($companyId)) {
            $this->apiReturn(204, [], '厂商必填');
        }

        $paramArr = [
            'product_name' => $productName,
            'pro_model_no' => $proModelNo,
            'type_id'      => $typeId,
            'company_id'   => $companyId,
            'part_ids'     => $partIds,
            'remark'       => $remark,
            'img'          => $img,
        ];
        $this->handleResponse('Device/Device/addDeviceProduct', $paramArr);
    }

    /**
     * 编辑设备产品
     * @Author: zhujb
     * 2019/5/28
     */
    public function editDeviceProduct()
    {
        $id          = I('post.id', 0, 'intval');
        $productName = I('post.product_name', '', 'strval,trim');
        $proModelNo  = I('post.pro_model_no', '', 'strval,trim');
        $companyId   = I('post.company_id', 0, 'intval');
        $typeId      = I('post.type_id', 0, 'intval');
        $partIds     = I('post.part_ids', '', 'strval,trim');
        $remark      = I('post.remark', '', 'strval,trim');
        $img         = I('post.img', '', 'strval,trim');

        if (empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (empty($productName)) {
            $this->apiReturn(204, [], '名称必填');
        }

        if (empty($proModelNo)) {
            $this->apiReturn(204, [], '型号必填');
        }

        if (empty($typeId)) {
            $this->apiReturn(204, [], '类型必填');
        }

        if (empty($companyId)) {
            $this->apiReturn(204, [], '厂商必填');
        }

        $paramArr = [
            'id'           => $id,
            'product_name' => $productName,
            'pro_model_no' => $proModelNo,
            'type_id'      => $typeId,
            'company_id'   => $companyId,
            'part_ids'     => $partIds,
            'remark'       => $remark,
            'img'          => $img,
        ];
        $this->handleResponse('Device/Device/editDeviceProduct', $paramArr);
    }

    /**
     * 删除设备产品
     * @Author: zhujb
     * 2019/5/28
     */
    public function delDeviceProduct()
    {
        $id = I('post.id', 0, 'intval');

        if (empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'id' => $id,
        ];
        $this->handleResponse('Device/Device/delDeviceProduct', $paramArr);
    }

    /**
     * 获取设备产品列表
     * @Author: zhujb
     * 2019/5/28
     */
    public function getDeviceProductList()
    {
        $page     = I('post.page', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');
        $keyword  = I('post.keyword', '', 'strval');
        $typeId   = I('post.typeId',0,'intval');

        $paramArr = [
            'page'      => $page,
            'page_size' => $pageSize,
            'keyword'   => $keyword,
            'typeId'    => $typeId,
        ];
        $this->handleResponse('Device/Device/getDeviceProductList', $paramArr);
    }

    /**
     * 获取单条产品信息
     * @Author: zhujb
     * 2019/5/28
     */
    public function getOneDeviceProduct()
    {
        $id = I('post.id', 0, 'intval');

        if (empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'id' => $id,
        ];
        $this->handleResponse('Device/Device/getOneDeviceProduct', $paramArr);
    }

    /**
     * 根据类型获取产品列表
     * @Author: zhujb
     * 2019/5/28
     */
    public function getProductsByType()
    {
        $typeId = I('post.type_id', 0, 'intval');

        if (empty($typeId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'type_id' => $typeId,
        ];
        $this->handleResponse('Device/Device/getProductsByType', $paramArr);
    }

    /**
     * 批量添加设备序列码
     * @Author: zhujb
     * 2019/5/28
     */
    public function addDeviceSequence()
    {
        $sequenceData = I('post.sequence_data');
        $excelFile    = $_FILES['sequence_excel'];

        $tmpName   = file_get_contents($excelFile['tmp_name']);
        $excelData = iconv("gb2312", "utf-8//IGNORE", $tmpName);

        $sequenceExcel = [];
        $rows          = explode(PHP_EOL, $excelData);
        foreach ($rows as $key => $item) {
            if ($key > 0 && !empty($item)) {
                $tmpData         = explode(',', $item);
                $sequenceExcel[] = [
                    'sequence_code' => trim($tmpData[0]),
                    'type_name'     => trim($tmpData[1]),
                    'product_name'  => trim($tmpData[2]),
                    'pro_model_no'  => trim($tmpData[3]),
                ];
            }
        }

        if (empty($sequenceData) && empty($sequenceExcel)) {
            $this->apiReturn(204, [], '序列码信息不能为空');
        }

        $paramArr = [
            'sequence_data'  => $sequenceData,
            'sequence_excel' => $sequenceExcel,
        ];
        $this->handleResponse('Device/Device/addDeviceSequence', $paramArr);
    }

    /**
     * 删除设备序列码信息
     * @Author: zhujb
     * 2019/5/28
     */
    public function delDeviceSequence()
    {
        $id = I('post.id', 0, 'intval');

        if (empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'id' => $id,
        ];
        $this->handleResponse('Device/Device/delDeviceSequence', $paramArr);
    }

    /**
     * 获取设备序列码列表
     * @Author: zhujb
     * 2019/5/28
     */
    public function getDeviceSequenceList()
    {
        $page            = I('post.page', 1, 'intval');
        $pageSize        = I('post.page_size', 10, 'intval');
        $keyword         = I('post.keyword', '', 'strval');
        $typeId          = I('post.type_id', 0, 'intval');
        $state           = I('post.state', -1, 'intval');
        $updateBeginTime = I('post.update_begin_time', '', 'strval');
        $updateEndTime   = I('post.update_end_time', '', 'strval');

        $paramArr = [
            'page'              => $page,
            'page_size'         => $pageSize,
            'keyword'           => $keyword,
            'type_id'           => $typeId,
            'state'             => $state,
            'update_begin_time' => $updateBeginTime,
            'update_end_time'   => $updateEndTime,
        ];
        $this->handleResponse('Device/Device/getDeviceSequenceList', $paramArr);
    }

    /**
     * 获取单条序列码信息
     * @Author: zhujb
     * 2019/5/28
     */
    public function getOneDeviceSequence()
    {
        $id = I('post.id', 0, 'intval');

        if (empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'id'            => $id,
            'sequence_code' => '',
        ];
        $this->handleResponse('Device/Device/getOneDeviceSequence', $paramArr);
    }

    /**
     * 解绑序列号，特征码
     * @Author: zhujb
     * 2019/11/14
     */
    public function unBindSequenceOnDevice()
    {
        $deviceKey = I('post.device_key', '', 'strval,trim');
        $opId      = I('post.op_id', 0, 'intval');

        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '特征码必填');
        }

        $paramArr = [
            'device_key' => $deviceKey,
            'op_id'      => $opId,
        ];
        $this->handleResponse('Device/Device/unBindSequenceOnDevice', $paramArr);
    }

    /**
     * 获取云票务的相关本地信息（云票务特殊处理）
     * <AUTHOR>
     * @date 2019/9/5
     */
    public function getDeviceSettingInfoForCloud()
    {
        $memberInfo       = $this->_memberInfo;
        $cloudConfigModel = new CloudConfig();
        $res              = $cloudConfigModel->getCloudConfig($memberInfo['saccount']);
        $config           = json_decode($res['config'], true);
        $this->apiReturn(200, $config, '获取成功');
    }

    /**
     * 首页汇总数量
     * @Author: zhujb
     * 2019/12/24
     */
    public function getDeviceSummary()
    {
        $this->handleResponse('Device/Home/getDeviceSummary', []);
    }

    /**
     * 首页入库，新增，绑定数量汇总
     * @Author: zhujb
     * 2019/12/24
     */
    public function getDeviceNumSummary()
    {
        $this->handleResponse('Device/Home/getDeviceNumSummary', []);
    }

    /**
     * 首页分布数量汇总
     * @Author: zhujb
     * 2019/12/24
     */
    public function getDeviceDistributionSummary()
    {
        $this->handleResponse('Device/Home/getDeviceDistributionSummary', []);
    }

    /**
     * 首页地区数量汇总
     * @Author: zhujb
     * 2019/12/24
     */
    public function getDeviceLocationSummary()
    {
        $this->handleResponse('Device/Home/getDeviceLocationSummary', []);
    }

    /**
     * 添加设备批量项目
     * @Author: zhujb
     * 2020/3/24
     */
    public function addDeviceBatchProject()
    {
        $projectName = I('post.project_name', '', 'strval,trim');
        $remark      = I('post.remark', '', 'strval,trim');
        $projectData = I('post.project_data', []);
        $projectPic  = I('post.project_img','');
        if (empty($projectName)) {
            $this->apiReturn(204, [], '请填写项目名称');
        }

        if (empty($projectData)) {
            $this->apiReturn(204, [], '请选择设备');
        }

        $paramArr = [
            'project_name' => $projectName,
            'project_data' => $projectData,
            'remark'       => $remark,
            'project_pic'  => $projectPic
        ];

        $this->handleResponse('Device/Device/addDeviceBatchProject', $paramArr);
    }

    /**
     * 编辑设备批量项目
     * @Author: zhujb
     * 2020/3/24
     */
    public function editDeviceBatchProject()
    {
        $projectId   = I('post.project_id', 0, 'intval');
        $projectName = I('post.project_name', '', 'strval,trim');
        $remark      = I('post.remark', '', 'strval,trim');
        $projectData = I('post.project_data', []);
        $projectPic  = I('post.project_img','');

        if (empty($projectId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (empty($projectName)) {
            $this->apiReturn(204, [], '请填写项目名称');
        }

        if (empty($projectData)) {
            $this->apiReturn(204, [], '请选择设备');
        }

        $paramArr = [
            'project_id'   => $projectId,
            'project_name' => $projectName,
            'project_data' => $projectData,
            'remark'       => $remark,
            'project_pic'  => $projectPic
        ];

        $this->handleResponse('Device/Device/editDeviceBatchProject', $paramArr);
    }

    /**
     * 删除设备批量操作
     * @Author: zhujb
     * 2020/3/24
     */
    public function delDeviceBatchProject()
    {
        $projectId = I('post.project_id', 0, 'strval,trim');

        if (empty($projectId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'project_id' => $projectId,
        ];

        $this->handleResponse('Device/Device/delDeviceBatchProject', $paramArr);
    }

    /**
     * 获取设备批量项目列表
     * @Author: zhujb
     * 2020/3/24
     */
    public function getDeviceBatchProjectList()
    {
        $page        = I('post.page', 1, 'intval');
        $pageSize    = I('post.pageSize', 10, 'intval');
        $projectName = I('post.project_name', '', 'strval,trim');

        $paramArr = [
            'page'         => $page,
            'pageSize'     => $pageSize,
            'project_name' => $projectName,
        ];

        $this->handleResponse('Device/Device/getDeviceBatchProjectList', $paramArr);
    }

    /**
     * 获取设备批量项目详情
     * @Author: zhujb
     * 2020/3/24
     * @return bool
     */
    public function getDeviceBatchProjectDetail()
    {
        $projectId = I('post.project_id', 0, 'intval');
        if (empty($projectId)) {
            return $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'project_id' => $projectId
        ];

        $this->handleResponse('Device/Device/getDeviceBatchProjectDetail', $paramArr);
    }

    /**
     * 批量推送设备升级
     * @Author: zhujb
     *
     * @param int project_id 项目id
     * @param array push_data  [['item_id' => 1, 'version_id' => 2]]
     * @param int upgrade_type 1 :可选升级 2：强制升级
     *
     * 2020/3/25
     */
    public function batchPushDeviceUpgrade()
    {
        $projectId = I('post.project_id', 0, 'intval');
        $pushData = I('post.push_data', []);
        $upgradeType = I('post.upgrade_type', 1, 'intval');

        if (empty($projectId) || empty($pushData)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = [
            'project_id' => $projectId,
            'push_data'  => $pushData,
            'upgrade_type' => $upgradeType,
        ];

        $this->handleResponse('Device/Device/batchPushDeviceUpgrade', $paramArr);
    }

    /**
     * 根据excel文件读取设备信息
     * @Author: zhujb
     * 2020/4/3
     */
    public function getDeviceByExcel()
    {
        $excelFile = $_FILES['excel'];
        $excelData = file_get_contents($excelFile['tmp_name']);

        $deviceKeyArr = explode(PHP_EOL, $excelData);
        array_shift($deviceKeyArr);
        foreach($deviceKeyArr as &$item) {
            $item = trim(str_replace(',', '', $item));
        }

        $paramArr = [
            'deviceKeyArr' => $deviceKeyArr,
        ];

        $this->handleResponse('Device/Device/getDeviceByExcel', $paramArr);
    }

    /**
     * 保存闸机设备配置
     * @Author: zhujb
     * 2020/1/14
     */
    public function saveGateDeviceSetting()
    {
        $deviceKey = I('post.device_key', '', 'strval,trim');
        $setting   = I('post.setting', '', 'strval,trim');
        $auxiliary_status   = I('post.auxiliary_status', 0, 'intval');
        if (empty($deviceKey) || empty($setting)) {
            return $this->apiReturn(204, [], '参数错误');
        }
        $setting  = htmlspecialchars_decode($setting);
        $ip = get_client_ip();
        $paramArr = [
            'device_key' => $deviceKey,
            'op_id'      => $this->_memberId,
            'setting'    => $setting,
            'origin'     => 'web',
            'ip'         => ip2long($ip),
            'auxiliary_status' => $auxiliary_status,
        ];

        $this->handleResponse('Device/Device/saveGateDeviceSetting', $paramArr);
    }


    /**
     * 获取闸机首页数据
     * @Author: zhujb
     * 2020/1/14
     */
    public function gateDeviceHomeData()
    {

        $deviceKey = I('post.device_key', '', 'strval,trim');
        $date = I('post.date', '', 'strval,trim');
        DebugUtil::debug(['🔴' => '🟣', '$date' => $date]);
        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }
        if(empty($date)) {
            $date = date('Y-m-d');
        }

        if(date('Y-m-d',strtotime($date)) != $date) {
            $this->apiReturn(204, [], '日期参数错误');
        }

        $terminalManageBiz = new TerminalManage();

        $result = $terminalManageBiz->gateDeviceHomeData($deviceKey, $date);
        if (isset($result['code'])){
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }else{
            $this->apiReturn(500, [], '服务异常');
        }

    }

    /**
     * 获取最近的版本信息
     * @Author: zhujb
     * 2019/11/7
     */
    public function getDeviceNewVersion()
    {
        $page     = 1;
        $pageSize = 4;
        $typeId   = I('post.type_id', 0, 'intval');

        if (empty($typeId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'page'      => $page,
            'page_size' => $pageSize,
            'type_id'   => $typeId,
        );

        $this->handleResponse('Device/Device/getRecentVersionList', $paramArr);
    }

    /**
     * 拉取升级进度条
     * @Author: zhujb
     * 2020/1/16
     */
    public function getNewUpgradeByDeviceV2()
    {
        $deviceKey = I('post.device_key', '', 'strval,trim');

        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'device_key' => $deviceKey,
        );

        $this->handleResponse('Device/Device/getNewUpgradeByDeviceV2', $paramArr);
    }

    /**
     * 根据供应商id 获取景区列表
     * @Author: zhujb
     * 2020/3/13
     */
    public function getLandListByApplyDid()
    {
        $applyDid = I('post.apply_did', 0, 'intval');
        $keyWord  = I('post.key_word', '', 'strval');
        $lid      = I('post.lid', 0, 'intval');
        $pageSize = I('post.page_size', 30, 'intval');
        $pType    = I('post.ptype', '', 'strval');

        if (empty($applyDid)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $productConfig = new ProductConfig();
        $rs = $productConfig->getLandListByApplyDid($applyDid, $keyWord, $lid, $pType, $pageSize);

        $this->apiReturn($rs['code'] ?? 204, $rs['data'] ?? [], $rs['msg'] ?? '');

    }

    /**
     * 根据景区id获取门票
     * @Author: zhujb
     * 2020/3/13
     */
    public function getTicketListByLid()
    {
        $lid = I('post.lid', 0, 'intval');
        if (empty($lid)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $productConfig = new ProductConfig();
        $rs = $productConfig->getTicketListByLid($lid);

        $this->apiReturn($rs['code'] ?? 204, $rs['data'] ?? [], $rs['msg'] ?? '');
    }

    /**
     * 配置转二维码
     * @Author: zhujb
     * 2020/3/16
     */
    public function getQrCodeBySetting()
    {
        $tag = I('post.tag', '', 'strval');
        $subSetting = I('post.sub_setting', '', 'strval');
        if (empty($subSetting) || empty($tag)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'tag' => $tag,
            'sub_setting' => $subSetting,
        );

        $this->handleResponse('Device/Device/getQrCodeBySetting', $paramArr);
    }

    /**M
     * 获取云票务配置页面门票列表
     * @Author: zhujb
     * 2020/3/18
     */
    public function getCloudConfigScenicList()
    {
        $siteId = I('post.site_id', 0, 'intval');
        $account = I('post.account', '', 'strval');
        $aid = I('post.aid', 0, 'intval');

        $terminalProductBiz = new TerminalProduct();
        $res = $terminalProductBiz->getScenicList($account, $aid, $siteId, 1);

        $code       = $res['code'];
        $msg        = $res['msg'];
        $data       = $res['data'];
        if ($code !== 200) {
            $this->apiReturn(400, [], $msg);
        }
        $this->apiReturn(200, $data, $msg);
    }
    /**
     * 获取newzd终端版本汇总
     * @Author: linchen
     * 2020/09/15
     */
    public function getDeviceEditionSummaryInfo(){
        $this->handleResponse('Device/Device/getDeviceEditionSummaryInfo',[]);
    }
    /**
     * 获取newzd终端版本汇总根据设备版本
     * @Author: linchen
     * 2020/09/15
     */
    public function getDeviceEditionBySystemVersion(){
        $deviceType = I('device_type',0);
        if (!$deviceType){
            $this->apiReturn(400, [], '参数错误');
        }
        $productId = I('product_id',0);
        $this->handleResponse('Device/Device/getDeviceEditionBySystemVersion',[$deviceType,$productId]);
    }

    /**
     * 终端上传图片
     * @Author: linchen
     * 2020/11/10
     */
    public function uploadTerminalManageImg()
    {
        $baseImg = I('post.base_img', '');
        if (!$baseImg) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '图片为空');
        }
        //计算大小
        $base64    = str_replace('data:image/jpeg;base64,', '', $baseImg);
        $base64    = str_replace('=', '', $base64);
        $img_len   = strlen($base64);
        $file_size = $img_len - ($img_len / 8) * 2;
        $file_size = number_format(($file_size / 1024), 2);
        if ($file_size > 1024 * 3) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '图片上传请小于3M');
        }
        $imgResult = Helpers::uploadImage2AliOss($this->imgPrifix, $baseImg,'base64',false,false);
        if ($imgResult['code'] != 200 || empty($imgResult['data']['src'])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '图片上传失败');
        }
        $Url = $imgResult['data']['src'];
        $this->apiReturn(self::CODE_SUCCESS, ['url' => $Url], 'success');
    }
    /**
     * 终端删除图片
     * @Author: linchen
     * 2020/11/10
     */
    public function deleteTerminalManageImg(){
        $imgUrl = I('post.img_url','');
        if (!$imgUrl){
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '图片地址有误');
        }
        $file = substr($imgUrl, strrpos($imgUrl, '/') + 1);
        if (!$file){
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '图片文件有误');
        }
        $imgDeleteResult = Helpers::deleteObject($this->imgPrifix.'/'.$file);
        if (ENV == 'PRODUCTION' && isset($imgDeleteResult['code'])){
            $this->apiReturn(self::CODE_PARAM_ERROR, [], $imgDeleteResult['code']);
        }else{
            $this->apiReturn(200, [], 'success');
        }
    }
    /**
     * 获取签到码的信息
     * @Author: linchen
     * 2019/3/28
     *
     * @param int id 主键id
     */
    public function getSignInfoLog(){
        $id = I('get.id','');
        if (!$id){
            $this->apiReturn(204, [], '参数有误');
        }
        $id                = OrderNotify::url_sms_decode($id)[0];
        if (!$id){
            $this->apiReturn(204, [], '参数有误');
        }
        $terminalManageBiz = new TerminalManage();
        $result = $terminalManageBiz->getDeviceSignInfoLogService($id);
        if (isset($result['code'])){
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }else{
            $this->apiReturn(500, [], '服务异常');
        }
    }


    /**
     * 获取设备人脸配置
     * User: lanwanhui
     * Date: 2021/3/15
     *
     * @return
     */
    public function getDeviceFaceSetting()
    {
        $deviceKey = I('device_key', '', 'strval'); //设备序列号
        if (empty($deviceKey)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $paramArr = array(
            'deviceKey'   => $deviceKey,
        );

        $this->handleResponse('Device/Device/getDeviceFaceSetting', $paramArr);

    }

    /**
     * 保存闸机设备人脸配置
     * User: lanwanhui
     * Date: 2021/3/15
     *
     * @return
     */
    public function saveGateDeviceFaceSetting()
    {
        $deviceKey = I('post.device_key', '', 'strval,trim');//设备序列号
        $setting   = I('post.setting', '', 'strval,trim');   //配置类容,json数据格式

        if (empty($deviceKey) || empty($setting)) {
            return $this->apiReturn(204, [], '参数错误');
        }

        $setting  = htmlspecialchars_decode($setting);

        $ip = get_client_ip();

        $paramArr = [
            'device_key' => $deviceKey,
            'op_id'      => $this->_memberId,
            'setting'    => $setting,
            'ip'         => ip2long($ip),
        ];

        $this->handleResponse('Device/Device/saveGateDeviceFaceSetting', $paramArr);

    }

    /**
     * 保存闸机数据控制配置
     * User: lanwanhui
     * Date: 2022/5/20
     *
     * @return
     */
    public function saveGateDeviceDataControlSetting()
    {
        $deviceKey = I('post.device_key', '', 'strval,trim');//设备序列号
        $setting   = I('post.setting', '', 'strval,trim');   //配置类容,json数据格式

        if (empty($deviceKey) || empty($setting)) {
            return $this->apiReturn(204, [], '参数错误');
        }

        $setting  = htmlspecialchars_decode($setting);

        $ip = get_client_ip();

        $paramArr = [
            'device_key' => $deviceKey,
            'op_id'      => $this->_memberId,
            'setting'    => $setting,
            'ip'         => ip2long($ip),
        ];

        $this->handleResponse('Device/Device/saveDeviceDataControlSetting', $paramArr);

    }

    /**
     *
     * 更新设备对应的产品id
     * User: lanwanhui
     * Date: 2021/4/25
     */
    public function saveDeviceProductId()
    {
        $deviceKey = I('post.device_key', '', 'strval'); //设备序列号
        $productId = I('post.product_id', 0, 'intval');  //产品id

        if (empty($deviceKey)){
            $this->apiReturn(400, [], '设备序列号不能为空');
        }

        if (empty($productId)){
            $this->apiReturn(400, [], '产品id不能为空');
        }

        $this->handleResponse('Device/Device/saveDeviceProductId',[$deviceKey,$productId]);

    }


    /**
     * 保存云票务系统配置
     * User: lanwanhui
     * Date: 2021/8/20
     *
     * @return
     */
    public function saveCloundDeviceSystemSetting()
    {
        $deviceKey     = I('post.device_key', '', 'strval');     //设备序列号
        $systemSetting = I('post.system_setting', '', 'strval');  //系统配置json格式

        if (empty($deviceKey)){
            $this->apiReturn(400, [], '设备序列号不能为空');
        }

        if (empty($systemSetting)){
            $this->apiReturn(400, [], '配置不能为空');
        }

        $paramArr = [
            'device_key'      => $deviceKey,
            'op_id'           => $this->_memberId,
            'system_setting'  => $systemSetting,
            'ip'              => ip2long(get_client_ip()),
        ];

        $this->handleResponse('Device/Device/saveCloundDeviceSystemSetting',$paramArr);

    }


    /**
     * 获取设备系统设置
     * User: lanwanhui
     * Date: 2021/8/20
     *
     * @return
     */
    public function getDeviceSystemSetting()
    {
        $deviceKey = I('post.device_key', '', 'strval'); //设备序列号
        if (empty($deviceKey)){
            $this->apiReturn(400, [], '设备特征码不能为空');
        }

        $this->handleResponse('Device/Device/getDeviceSystemSetting',['device_key'=>$deviceKey]);

    }


    /**
     * 保存安卓自助机功能配置
     * User: lanwanhui
     * Date: 2021/8/30
     *
     * @return
     */
    public function saveAndroidDeviceServiceSetting()
    {
        $deviceKey      = I('post.device_key', '', 'strval');     //设备序列号
        $serviceSetting = I('post.service_setting', '', 'strval');  //安卓自助机功能配置json格式

        if (empty($deviceKey)){
            $this->apiReturn(400, [], '设备序列号不能为空');
        }

        if (empty($serviceSetting)){
            $this->apiReturn(400, [], '配置不能为空');
        }

        $paramArr = [
            'device_key'       => $deviceKey,
            'op_id'            => $this->_memberId,
            'service_setting'  => $serviceSetting,
            'ip'               => ip2long(get_client_ip()),
        ];
        TerminalManage::cacheHandler($deviceKey, 'DEL');
        $this->handleResponse('Device/Device/saveAndroidDeviceServiceSetting',$paramArr);

    }


    /**
     * 保存安卓自助机硬件配置
     * User: lanwanhui
     * Date: 2021/10/22
     *
     * @return
     */
    public function saveSelfMachineHardwareSetting()
    {
        $deviceKey      = I('post.device_key', '', 'strval');        //设备序列号
        $hardwareSetting = I('post.hardware_setting', '', 'strval');  //安卓自助机硬件配置json格式

        if (empty($deviceKey)){
            $this->apiReturn(400, [], '设备序列号不能为空');
        }

        if (empty($hardwareSetting)){
            $this->apiReturn(400, [], '配置不能为空');
        }

        $paramArr = [
            'device_key'       => $deviceKey,
            'service_setting'  => $hardwareSetting,
            'op_id'            => $this->_memberId,
            'ip'               => ip2long(get_client_ip()),
        ];

        $this->handleResponse('Device/Device/saveSelfMachineHardwareSetting',$paramArr);

    }

    /**
     * 保存闸机日志配置
     * User: lanwanhui
     * Date: 2022/1/3
     *
     * @return
     */
    public function saveLogSetting()
    {
        $deviceKey  = I('post.device_key', '', 'strval');   //设备序列号
        $logSetting = I('post.log_setting', '', 'strval');  //闸机日志配置json格式

        if (empty($deviceKey)){
            $this->apiReturn(400, [], '设备序列号不能为空');
        }

        if (empty($logSetting)){
            $this->apiReturn(400, [], '配置不能为空');
        }

        $paramArr = [
            'device_key'       => $deviceKey,
            'log_setting'      => $logSetting,
            'op_id'            => $this->_memberId,
            'ip'               => ip2long(get_client_ip()),
        ];

        $this->handleResponse('Device/Device/saveLogSetting',$paramArr);

    }


    /**
     * 保存闸机网络地址检测配置
     * User: lanwanhui
     * Date: 2022/1/3
     *
     * @return
     */
    public function saveDetectionAddress()
    {
        $deviceKey         = I('post.device_key', '', 'strval');   //设备序列号
        $detectionAddress  = I('post.detection_address', '', 'strval');  //闸机网络监测地址

        if (empty($deviceKey)){
            $this->apiReturn(400, [], '设备序列号不能为空');
        }

        //if (empty($detectionAddress)){
        //    $this->apiReturn(400, [], '配置不能为空');
        //}

        $paramArr = [
            'device_key'        => $deviceKey,
            'detection_address' => $detectionAddress,
            'op_id'             => $this->_memberId,
            'ip'                => ip2long(get_client_ip()),
        ];

        $this->handleResponse('Device/Device/saveDetectionAddress',$paramArr);

    }


    /**
     * 保存终端表单模板配置
     * User: lanwanhui
     * Date: 2022/2/10
     *
     * @return
     */
    public function setSettingFormTemplate()
    {
        $type      = I('post.type', 0, 'intval'); //配置类型.1闸机前端配置
        $remark    = I('post.remark', '', 'strval');  //备注
        $template  = I('post.template', '', 'strval');  //表单模板数据
        if (empty($type) || empty($template)) {
            $this->apiReturn(400, [], '参数错误');
        }

        $paramArr = [
            'type'      => $type,
            'remark'    => $remark,
            'template'  => $template,
            'op_id'     => $this->_memberId,
            'ip'        => ip2long(get_client_ip()),
        ];

        $this->handleResponse('Device/Device/setSettingFormTemplate',$paramArr);

    }

    /**
     * 获取终端表单模板配置
     * User: lanwanhui
     * Date: 2022/2/10
     *
     * @return
     */
    public function getSettingFormTemplate()
    {
        $type      = I('post.type', 0, 'intval'); //配置类型.1闸机前端配置
        if (empty($type)) {
            $this->apiReturn(400, [], '参数错误');
        }

        $paramArr = [
            'type'     => $type,
        ];

        $this->handleResponse('Device/Device/getSettingFormTemplate',$paramArr);
    }
    /**
     * 获取省份健康码规则
     * User: linchen
     * Date: 2022/06/06
     *
     * @return
     */
    public function getHealthCodeRuleByProvince()
    {
        $channel    = I('channel', 0, 'intval'); //对接渠道

        $paramArr = [
            'channel' => $channel,
        ];

        $this->handleResponse('Device/Device/getHealthProvinceConfig',$paramArr);
    }

    /**
     * 获取授权类型
     * User: lanwanhui
     * Date: 2023/3/16
     */
    public function getVasClassifyList()
    {
        $this->handleResponse('Device/DeviceLicense/getVasClassifyList',[]);
    }

    /**
     * 解绑增量包绑定信息
     *
     * @date 2023/03/09
     * @auther yangjianhui
     * @return array
     */
    public function unbindDeviceLicense()
    {
        $deviceKey = I('post.device_key', '', 'strval,trim');//设备序列号
        $id        = I('post.id', '', 'strval');//绑定的id
        if (empty($deviceKey) || empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $idList   = explode(',', $id);
        if (empty($idList)) {
            $this->apiReturn(204, [], '参数错误2');
        }

        $paramArr = [
            'device_key' => $deviceKey,
            'idList'     => $idList,
        ];

        $this->handleResponse('Device/DeviceLicense/unbindDeviceLicense',$paramArr);

    }

    /**
     * 获取闸机设备对应四川社保卡授权信息
     *
     * @auther yangjianhui
     * @return array
     */
    public function getScSocialSecurityAuth()
    {
        $deviceKey = I('post.device_key', '', 'strval,trim');//设备序列号
        $id        = I('post.id', '', 'strval');             //设备id
        if (empty($deviceKey) || empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $deviceManageJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\DeviceManage();
        $result              = $deviceManageJsonRpc->getScSocialSecurityAuth($this->_sid, $deviceKey, $id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 批量获取闸机设备对应四川社保卡授权信息
     * @return void
     */
    public function getScSocialSecurityAuthBatch()
    {
        //包含device_key和id的json
        $deviceInfo = I('post.device_info', '', 'strval,trim');
        $deviceInfoArr = json_decode($deviceInfo, true);
        if (json_last_error() != JSON_ERROR_NONE) {
            $this->apiReturn(204, [], '参数不是标准json格式');
        }
        foreach ($deviceInfoArr as $item) {
            if (empty($item['device_key']) || empty($item['id'])) {
                $this->apiReturn(204, [], '参数错误');
            }
        }
        $deviceManageJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\DeviceManage();
        $return = [];
        $errMsg = [];
        $error = 0;
        foreach ($deviceInfoArr as $item) {
            $result = $deviceManageJsonRpc->getScSocialSecurityAuth($this->_sid, $item['device_key'], $item['id']);
            if ($result['code'] != 200) {
                $error++;
                $errMsg[] = '特征码：'.$item['device_key'].' 请求getScSocialSecurityAuth出错：'.$result['msg'];
            }
            $return[] = [
                'device_key' => $item['device_key'],
                'id' => $item['id'],
                'data' => $result['data'],
            ];
        }
        $this->apiReturn(200, [
            'success' => count($deviceInfoArr) - $error,
            'error' => $error,
            'return' => $return
        ], implode("\n", $errMsg));
    }
}