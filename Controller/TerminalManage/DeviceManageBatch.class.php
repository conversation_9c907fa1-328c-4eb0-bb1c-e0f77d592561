<?php
/**
 * 设备批量管理
 */

namespace Controller\TerminalManage;


class DeviceManageBatch extends BaseTerminalManage
{
    //批量设置检票配置
    public function batchSaveDeviceSignConfig()
    {
        try {
            $params = $this->generateCommonParams();
            $params['method'] = 'batchSaveDeviceSignConfig';
            $this->handleResponse('Device/DeviceBatch/batchSaveDeviceConfig', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }
    //批量设置待机界面配置
    public function batchSaveDeviceViewConfig()
    {
        try {
            $params = $this->generateCommonParams();
            $params['method'] = 'batchSaveDeviceViewConfig';
            $this->handleResponse('Device/DeviceBatch/batchSaveDeviceConfig', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }

    //批量设置硬件配置
    public function batchSaveDeviceMovementConfig()
    {
        try {
            $params = $this->generateCommonParams();
            $params['method'] = 'batchSaveDeviceMovementConfig';
            $this->handleResponse('Device/DeviceBatch/batchSaveDeviceConfig', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }
    //批量设置数据控制
    public function batchSaveDeviceDataControlConfig()
    {
        try {
            $params = $this->generateCommonParams();
            $params['method'] = 'batchSaveDeviceDataControlConfig';
            $this->handleResponse('Device/DeviceBatch/batchSaveDeviceConfig', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }
    //批量设置人脸配置
    public function batchSaveDeviceFaceConfig()
    {
        try {
            $params = $this->generateCommonParams();
            $params['method'] = 'batchSaveDeviceFaceConfig';
            $this->handleResponse('Device/DeviceBatch/batchSaveDeviceConfig', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }

    //批量设置检票配置单功能
    public function batchSaveDeviceSignSingleConfig()
    {
        try {
            $params = $this->generateCommonParams();
            $params['method'] = 'batchSaveDeviceSignSingleConfig';
            $this->handleResponse('Device/DeviceBatch/batchSaveDeviceConfig', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }
    //批量设置待机界面配置单功能
    public function batchSaveDeviceViewSingleConfig()
    {
        try {
            $params = $this->generateCommonParams();
            $params['method'] = 'batchSaveDeviceViewSingleConfig';
            $this->handleResponse('Device/DeviceBatch/batchSaveDeviceConfig', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }

    //批量设置硬件配置单功能
    public function batchSaveDeviceMovementSingleConfig()
    {
        try {
            $params = $this->generateCommonParams();
            $params['method'] = 'batchSaveDeviceMovementSingleConfig';
            $this->handleResponse('Device/DeviceBatch/batchSaveDeviceConfig', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }
    //批量设置数据控制配置单功能
    public function batchSaveDeviceDataControlSingleConfig()
    {
        try {
            $params = $this->generateCommonParams();
            $params['method'] = 'batchSaveDeviceDataControlSingleConfig';
            $this->handleResponse('Device/DeviceBatch/batchSaveDeviceConfig', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }
    //批量设置人脸配置单功能
    public function batchSaveDeviceFaceSingleConfig()
    {
        try {
            $params = $this->generateCommonParams();
            $params['method'] = 'batchSaveDeviceFaceSingleConfig';
            $this->handleResponse('Device/DeviceBatch/batchSaveDeviceConfig', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }
    /**
     * @throws \Exception
     */
    private function generateCommonParams(): array
    {
        $deviceJson = I('post.device_info', '', 'strval,trim');
        $setting   = I('post.setting', '', 'strval,trim');
        $setting  = htmlspecialchars_decode($setting);
        if (empty($deviceJson) || empty($setting)) {
            throw new \Exception('参数错误');
        }
        $deviceInfo = json_decode($deviceJson, true);
        if (count($deviceInfo) > 100) {
            throw new \Exception('一次最多支持100台闸机配置');
        }
        return [
            'device_info' => $deviceInfo,
            'device_keys' => array_column($deviceInfo, 'device_key'),
            'op_id' => $this->_memberId,
            'setting' => $setting,
            'origin' => 'web',
            'ip' => ip2long(get_client_ip()),
            'method' => ''
        ];
    }
}