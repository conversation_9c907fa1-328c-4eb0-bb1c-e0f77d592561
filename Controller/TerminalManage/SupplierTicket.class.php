<?php
namespace Controller\TerminalManage;

use Library\Controller;

use Business\TerminalManage\SupplierTicket as SupplierTicketBiz;
use Model\TerminalManage\TerminalSelfHelp;

/**
 * 供应商门票模板管理
 *
 * <AUTHOR>
 */
class SupplierTicket extends Controller
{

    private $loginInfo;

    public function __construct()
    {
        parent::__construct();

        $this->loginInfo = $this->getLoginInfo('ajax');

    }

    /**
     * 获取门票列表
     * User: lanwanhui
     * Date: 2022/5/9
     */
    public function getTicketList()
    {
        $page           = I('post.page', 1, 'intval');
        $pageSize       = I('post.page_size', 10, 'intval');
        $terminalNo     = I('post.TerminalNo', '', 'trim');
        $ticketConfigId = I('post.TicketConfigId', 0, 'intval');

        $supplierTicketBiz = new SupplierTicketBiz();
        $rs = $supplierTicketBiz->getTicketList($this->loginInfo['sid'], $this->loginInfo['memberID'], $this->loginInfo['dtype'],
            $this->loginInfo['account'], $page, $pageSize, $terminalNo, $ticketConfigId);

        $this->apiReturn($rs['code'] ?? 204, $rs['data'] ?? [], $rs['msg'] ?? '');

    }


    /**
     * 编辑门票
     * User: lanwanhui
     * Date: 2022/5/9
     */
    public function editTicketConfig()
    {

        $ticketConfigId                       = I('post.TicketConfigId', 0, 'intval');
        $ticketConfigInfo['TerminalNo']       = I('post.TerminalNo', '', 'trim');
        $ticketConfigInfo['PrinType']         = I('post.PrinType', 1, 'intval');
        $ticketConfigInfo['TicketsPrintWay']  = I('post.TicketsPrintWay', 0, 'intval');
        $ticketConfigInfo['TicketJsonConfig'] = I('post.TicketJsonConfig', '', 'trim');
        $ticketConfigInfo['PrintName']        = I('post.PrintName', '', 'trim');
        $ticketConfigInfo['TempType']         = I('post.TempType', 0, 'trim');
        $ticketConfigInfo['salerid']          = I('post.salerid', '', 'trim');
        $ticketConfigInfo['title']            = I('post.title', '', 'trim');
        $ticketConfigInfo['tid']              = I('post.tid', '', 'trim');
        $ticketConfigInfo['ttitle']           = I('post.ttitle', '', 'trim');
        $ticketConfigInfo['Printer']          = I('post.Printer', 'QD', 'trim');
        $ticketConfigInfo['TicketHeight']     = I('post.TicketHeight', 173, 'intval');
        $ticketConfigInfo['TicketWeight']     = I('post.TicketWeight', 80, 'intval');
        $ticketConfigInfo['TrDateTime']       = date('Y-m-d H:i:s', time());
        $ticketConfigInfo['isFeedPrinting']   = I('post.isFeedPrinting', '0', 'strval');
        $ticketConfigInfo['orderType']        = I('post.orderType', 0, 'intval');
        $ticketConfigInfo['apply_did']        =  $this->loginInfo['sid'];

        if (empty($ticketConfigId)) {
            $this->apiReturn(400, [], '参数错误');
        }

        $supplierTicketBiz = new SupplierTicketBiz();

        $rs = $supplierTicketBiz->editTicketConfig($ticketConfigId, $ticketConfigInfo);

        $this->apiReturn($rs['code'] ?? 204, $rs['data'] ?? [], $rs['msg'] ?? '');

    }


    /**
     * 删除门票模板
     * User: lanwanhui
     * Date: 2022/5/9
     */
    public function delTicketConfig()
    {
        $ticketConfigId   = I('post.TicketConfigId', 0, 'intval');
        if (empty($ticketConfigId)) {
            $this->apiReturn(400, [], '参数错误');
        }

        $supplierTicketBiz = new SupplierTicketBiz();

        $rs = $supplierTicketBiz->delTicketConfig($ticketConfigId);

        $this->apiReturn($rs['code'] ?? 204, $rs['data'] ?? [], $rs['msg'] ?? '');

    }

    /**
     * 获取自助终端详情
     * User: lanwanhui
     * Date: 2022/5/9
     *
     * @return
     */
    public function getTerminalDetail()
    {
        $terminalNo            = I('TerminalNo', '', 'trim');
        if (empty($terminalNo)) {
            $this->apiReturn(204, [], '终端号不能为空');
        }

        $terminalSelfHelpModel = new TerminalSelfHelp();

        $rerminalSelfHelp      = $terminalSelfHelpModel->findSelfHelp($terminalNo);
        if (empty($rerminalSelfHelp)) {
            $this->apiReturn(204, [], '终端配置不存在');
        }

        $this->apiReturn(200, $rerminalSelfHelp);

    }
}