<?php
/**
 * 系统菜单设置
 * Created by PhpStorm.
 * Author: zhujb
 * Date: 2018/6/22
 * Time: 10:57
 */

namespace Controller\TerminalManage;

use Model\Member\Member;
use Model\TerminalManage\SysMenu as SysMenuModel;

class SysMenu extends BaseTerminalManage
{
    /**
     * 设置权限
     * @Author: zhujb
     * 2018/6/22
     */
    public function setPermissions()
    {
        $account    = I('post.account', '', 'strval');
        $menuIdsArr = I('post.menuIds/a', []);

        $sysMenuModel = new SysMenuModel();
        $res          = $sysMenuModel->savePermissions($account, $menuIdsArr);

        if ($res['code'] == 200) {
            $this->apiReturn(200, [], $res['msg']);
        }
        $this->apiReturn(400, [], $res['msg']);
    }

    /**
     * 根据账户名获取菜单列表 (能够展示出是否已被勾选)
     * @Author: zhujb
     * 2018/6/22
     */
    public function getMenusByUserName()
    {
        $userName = I('post.userName', '', 'strval');

        if (empty($userName)) {
            $this->apiReturn(400, [], '用户名不能为空');
        }

        $memberModel = new Member();
        $member      = $memberModel->getMemberInfo($userName, 'account');
        if (empty($member)) {
            $this->apiReturn(400, [], '用户不存在');
        }

        // 获取当前用户有权限的菜单
        $sysMenuModel = new SysMenuModel();
        $hasMenus     = $sysMenuModel->getSysMenusByUserName($userName);
        $hasMenusIds  = array_column($hasMenus, 'SysMenuId');

        // 获取所有菜单
        $sysMenuModel = new SysMenuModel();
        $allMenulist  = $sysMenuModel->getSysMenus();

        // 比对出哪些菜单是已勾选的
        foreach ($allMenulist as $k => $v) {
            if (in_array($allMenulist[$k]['SysMenuId'], $hasMenusIds)) {
                $allMenulist[$k]['IsCheck'] = 1;
            } else {
                $allMenulist[$k]['IsCheck'] = 0;
            }
        }

        $this->apiReturn(200, $allMenulist);
    }

    /**
     * 获取成员列表
     * @Author: zhujb
     * 2019/10/17
     */
    public function getUserList()
    {
        $account = I('post.account', '', 'strval');
        $page = I('post.page', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');


        $memberBiz = new \Business\Member\Member();
        $javaData  = $memberBiz->getMemberListBySize($account,$page,$pageSize);
        $list      = $javaData['list'] ? $javaData['list'] : [];
        $total     = $javaData['total'] ? $javaData['total'] : 0;

        if (!empty($list)) {
            $accountArr   = array_column($list, 'account');
            $sysMenuModel = new \Model\TerminalManage\SysMenu();
            $updateTimes  = $sysMenuModel->getDateByAccountArr($accountArr);

            foreach ($list as &$item) {
                $item['update_time'] = $updateTimes[$item['account']] ?? '';
            }
        }

        $res = [
            'list' => $list ?: [],
            'total' => $total ?: 0,
            'page_size' => $pageSize
        ];

        $this->apiReturn(200, $res, '');
    }
}