<?php
/**
 * 门票配置相关
 * User: zhujb
 * Date: 2018-06.11
 * Time: 16:41
 */

namespace Controller\TerminalManage;


use Model\TerminalManage\TerminalAccountR;
use Model\TerminalManage\TerminalClientConfig;
use Model\TerminalManage\TerminalScenicSpotR;
use Model\TerminalManage\TerminalSysConfig;
use Model\TerminalManage\TerminalSysServicesConfig;
use Model\TerminalManage\TicketConfig;
use Model\TerminalManage\TicketConfigTemp;
use Model\TerminalManage\TerminalSelfHelp;

class Ticket extends BaseTerminalManage
{
    /**
     * 获取门票列表
     * @Author: zhujb
     */
    public function getTicketList()
    {
        // TODO:: 需要增加查询条件，terminalId，apply_did等
        $page       = I('post.page', 1, 'intval');
        $terminalNo = I('post.TerminalNo', '', 'trim');
        $applyDid   = I('post.apply_did', '', 'trim');
        $pageSize   = I('post.page_size', 10, 'intval');

        $TicketConfigId = I('post.TicketConfigId', 0, 'intval');
        $terminalNoArr = [];

        if (!$TicketConfigId) {
            // 查找当前登录用户的终端号列表
            $terminalAccountModel = new TerminalAccountR();
            $terminals            = $terminalAccountModel->selectAccount('', $this->_memberInfo['account']);

            if (!empty($terminals)) {
                $terminalNoArr = array_column($terminals, 'TerminalNo');
            }
        }

        $ticketConfigModel = new TicketConfig();
        $res               = $ticketConfigModel->getTicketConfigs($terminalNo, $applyDid, $terminalNoArr, $TicketConfigId, $page, $pageSize);

        foreach ($res as $k => $v) {
            $res[$k]['TicketJsonConfig'] = json_decode($res[$k]['TicketJsonConfig'], true);
            $res[$k]['orderType']        = intval($v['orderType']);
        }

        $list['list']      = $res;
        $list['page_size'] = $pageSize;
        $list['total']     = $ticketConfigModel->getTicketConfigsCount($terminalNo, $applyDid, $terminalNoArr, $TicketConfigId);

        $this->apiReturn(200, $list);
    }

    /**
     * 新增门票
     * @Author: zhujb
     */
    public function setTicketConfig()
    {
        $action                   = I('post.Action', 0, 'intval');
        $ticketConfigId           = I('post.TicketConfigId', 0, 'intval');
        $data['TerminalNo']       = I('post.TerminalNo', '', 'trim');
        $data['PrinType']         = I('post.PrinType', 1, 'intval');
        $data['TicketsPrintWay']  = I('post.TicketsPrintWay', 0, 'intval');
        $data['TicketJsonConfig'] = I('post.TicketJsonConfig', '', 'trim');
        $data['PrintName']        = I('post.PrintName', '', 'trim');
        $data['TempType']         = I('post.TempType', 0, 'trim');
        $data['salerid']          = I('post.salerid', '', 'trim');
        $data['title']            = I('post.title', '', 'trim');
        $data['tid']              = I('post.tid', '', 'trim');
        $data['ttitle']           = I('post.ttitle', '', 'trim');
        $data['Printer']          = I('post.Printer', 'QD', 'trim');
        $data['TicketHeight']     = I('post.TicketHeight', 173, 'intval');
        $data['TicketWeight']     = I('post.TicketWeight', 80, 'intval');
        $data['TrDateTime']       = date('Y-m-d H:i:s', time());
        $data['isFeedPrinting']   = I('post.isFeedPrinting', '0', 'strval');
        $data['orderType']        = I('post.orderType', '0', 'strval');

        if (empty($action)) {
            $this->apiReturn(400, [], '参数错误');
        }

        //如果登录者为平台管理员，那么需要根据tid获取供应商id，可以到Ticket模型下找对应的方法
        //数据库表结构需要增加对应的字段：apply_did
        if ($this->_memberInfo['dtype'] == 9) {
            $platformTicketModel = new \Model\Product\Ticket('slave');
            $ticketInfo          = $platformTicketModel->getTicketInfoById($data['tid'], 'apply_did');
            $data['apply_did']   = $ticketInfo['apply_did'];
        } else {
            $data['apply_did'] = $this->_sid;
        }

        unset($platformTicketModel);

        // 如果勾选了添加到模板，则保存到模板列表中
        $temp       = [];
        $isSaveTemp = I('post.IsSaveTemp', 0, 'intval');
        if (!empty($isSaveTemp)) {
            $isCommon = I('post.is_common', 0, 'intval');
            if ($isCommon) {
                // 设置为常用模板
                $temp['is_common'] = $isCommon;
            }

            $temp['TempTitle']        = I('post.TempTitle', '', 'trim');
            $temp['Remarks']          = I('post.Remarks', '', 'trim');
            $temp['TicketJsonConfig'] = $data['TicketJsonConfig'];
            $temp['Printer']          = $data['Printer'];
            $temp['TicketHeight']     = $data['TicketHeight'];
            $temp['TicketWeight']     = $data['TicketWeight'];
            $temp['PrintName']        = $data['PrintName'];
            $temp['PrinType']         = $data['PrinType'];
            $temp['TicketsPrintWay']  = $data['TicketsPrintWay'];
            
            $ticketConfigTempModel = new TicketConfigTemp();
            $res                   = $ticketConfigTempModel->addTicketTemplate($temp);
            if ($res['code'] !== 200) {
                $this->apiReturn(400, [], '设置失败');
            }
        }

        $ticketConfigModel = new TicketConfig();

        if ($action == 1) {
            // 添加门票
            $res = $ticketConfigModel->addTicketConfig($data);
        } else if ($action == 2) {
            // 编辑门票
            $res = $ticketConfigModel->editTicketConfig($ticketConfigId, $data);
        } else {
            // 删除门票
            $res = $ticketConfigModel->delTicketConfig($ticketConfigId);
        }
        $logData = [
            'action'=>$action,
            'data'  => $data,
            'user_info'=>[
                'referer'  =>$_SERVER['HTTP_REFERER'],
                'agent'    =>$_SERVER['HTTP_USER_AGENT'],
                'remote_ip'=>get_client_ip(),
                'member_id'=>$this->getLoginInfo()['memberID'],
            ]
        ];
        pft_log('terminal_manage/ticket',  json_encode($logData, JSON_UNESCAPED_UNICODE));
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '设置成功');
        }
        $this->apiReturn(400, [], '设置失败');
    }

    /**
     * 模板常用配置
     * @Author: zhujb
     * 2019/4/22
     * @param int temp_id 模板id
     * @param int action  1:常用，2：不常用
     */
    public function setTicketTemplateCommon()
    {
        $tempId = I('post.temp_id', 0, 'intval');
        $action = I('post.action', 1, 'intval');

        $ticketConfigTempModel = new TicketConfigTemp();

        if ($action == 1) {
            $data['is_common'] = 1;
        } else {
            $data['is_common'] = 0;
        }
        $res = $ticketConfigTempModel->editTicketTemplate($tempId, $data);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '设置成功');
        }
        $this->apiReturn(400, [], '设置失败');
    }

    /**
     * 获取门票模板列表
     * @Author: zhujb
     */
    public function getTicketTemplateList()
    {
        $page      = I('post.page', 1, 'intval');
        $tempTitle = I('post.temp_title', '', 'strval');
        $isCommon  = I('post.is_common', 0, 'intval');

        $ticketConfigTempModel = new TicketConfigTemp();
        $list['list']          = $ticketConfigTempModel->getTicketTemplates($page, 'TicketConfigTempId,TempTitle,TicketJsonConfig,Remarks,IsDel,is_common,PrinType,TicketsPrintWay,
        PrintName,Printer,TicketHeight,TicketWeight', $tempTitle, $isCommon);
        $list['total']         = $ticketConfigTempModel->getTicketTemplatesCount($tempTitle, $isCommon);
        $list['page_size']     = $ticketConfigTempModel->pageSize;

        $this->apiReturn(200, $list);
    }

    /**
     * 删除模板
     * @Author: zhujb
     */
    public function delTicket()
    {
        $ticketConfigTempId = I('post.TicketConfigTempId', 0, 'intval');

        $ticketConfigTempModel = new TicketConfigTemp();
        $res                   = $ticketConfigTempModel->delTicketTemplate($ticketConfigTempId);

        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '设置成功');
        }
        $this->apiReturn(400, [], '设置失败');
    }

    /**
     * 旧zd数据同步
     * @Author: zhujb
     * 2018/9/5
     */
    public function syncData()
    {
        $dataJson   = file_get_contents('php://input');
        $encode     = mb_detect_encoding($dataJson, array("ASCII", 'UTF-8', "GB2312", "GBK", 'BIG5'));
        $str_encode = mb_convert_encoding($dataJson, 'UTF-8', $encode);
        $dataJson   = $str_encode;
        $dataArr    = json_decode($dataJson, true);

        $dataJson = json_encode($dataArr, JSON_UNESCAPED_UNICODE);

        pft_log('terminal-manage', 'request syncData param' . $dataJson);

        $data = json_decode($dataJson, true);

        $terminalSelfHelp      = (array)$data['TerminalSelfHelpInfo'];
        $terminalAccount       = (array)$data['TerminalAccount'];
        $terminalSysConfig     = (array)$data['TerminalSysConfigInfo'];
        $terminalServiceConfig = (array)$data['TerminalSysServicesConfigInfo'];
        $terminalClientConfig  = (array)$data['TerminalClientConfigInfo'];
        $terminalScenicSpotR   = (array)$data['TerminalScenics'];
        $ticketConfig          = $data['TicketConfigs'];


        /******终端号*********/
        $terminalSelfHelpModel = new TerminalSelfHelp();
        $terminalSelfHelpModel->startTrans();
        try {
            $terminalSelfHelpModel->table('TerminalSelfHelp')->where(['TerminalNo' => $terminalSelfHelp['TerminalNo']])->delete();
            $terminalSelfHelp['TrDateTime'] = $terminalSelfHelp['TrDate'];
            unset($terminalSelfHelp['TrDate']);
            $terminalSelfHelp['SharedTType'] = is_null($terminalSelfHelp['SharedTType']) ? '' : $terminalSelfHelp['SharedTType'];
            $terminalSelfHelp['TrDateTime']  = is_null($terminalSelfHelp['TrDateTime']) ? '' : $terminalSelfHelp['TrDateTime'];
            $res1                            = $terminalSelfHelpModel->table('TerminalSelfHelp')->add($terminalSelfHelp);
            if ($res1 === false) {
                throw new \Exception('终端号配置失败');
            }

            /********终端账号**********/
            $terminalAccountModel = new TerminalAccountR();
            $terminalAccountModel->table('TerminalAccountR')->where(['TerminalNo' => $terminalSelfHelp['TerminalNo']])->delete();
            $newAccount = [];
            foreach ($terminalAccount as $v) {
                $v['TrDateTime'] = $v['TrDate'];
                unset($v['TrDate']);
                unset($v['TerminalAccountId']);
                $newAccount[] = $v;
            }
            if (!empty($newAccount)) {
                $res2 = $terminalAccountModel->table('TerminalAccountR')->addAll($newAccount);
                if ($res2 === false) {
                    throw new \Exception('终端账号错误');
                }
            }

            /*********系统配置*************/
            $terminalSysConfigModel = new TerminalSysConfig();
            $terminalSysConfigModel->table('TerminalSysConfig')->where(['TerminalNo' => $terminalSelfHelp['TerminalNo']])->delete();
            $temp                            = strtotime($terminalSysConfig['TrDateTime']);
            $terminalSysConfig['TrDateTime'] = date('Y-m-d H:i:s', $temp);
            unset($terminalSysConfig['TerminalSysConfigId']);
            $terminalSysConfig['ScanGunPort'] = is_null($terminalSysConfig['ScanCom']) ? '' : $terminalSysConfig['ScanCom'];
            unset($terminalSysConfig['ScanCom']);
            $terminalSysConfig['TicketTips'] = is_null($terminalSysConfig['TicketTips']) ? '' : $terminalSysConfig['TicketTips'];
            $terminalSysConfig['HomeImage']  = is_null($terminalSysConfig['HomeImage']) ? '' : $terminalSysConfig['HomeImage'];
            $terminalSelfHelp['ScanGunPort'] = is_null($terminalSelfHelp['ScanGunPort']) ? '' : $terminalSelfHelp['ScanGunPort'];
            $res3                            = $terminalSysConfigModel->table('TerminalSysConfig')->add($terminalSysConfig);
            if ($res3 === false) {
                throw new \Exception('系统配置错误');
            }

            /***********服务配置***************/
            $terminalSysServiceConfigModel = new TerminalSysServicesConfig();
            $terminalSysServiceConfigModel->table('TerminalSysServicesConfig')->where(['TerminalNo' => $terminalSelfHelp['TerminalNo']])->delete();
            $temp                                = strtotime($terminalServiceConfig['TrDateTime']);
            $terminalServiceConfig['TrDateTime'] = date('Y-m-d H:i:s', $temp);
            unset($terminalServiceConfig['OpenVirtualPaper']);
            unset($terminalServiceConfig['TerminalSysServicesConfigId']);
            $res4 = $terminalSysServiceConfigModel->table('TerminalSysServicesConfig')->add($terminalServiceConfig);
            if ($res4 === false) {
                throw new \Exception('服务配置错误');
            }

            /**************客户端配置****************/
            $terminalClientConfigModel = new TerminalClientConfig();
            $terminalClientConfigModel->table('TerminalClientConfig')->where(['TerminalNo' => $terminalSelfHelp['TerminalNo']])->delete();
            $temp                               = strtotime($terminalClientConfig['TrDateTime']);
            $terminalClientConfig['TrDateTime'] = date('Y-m-d H:i:s', $temp);
            unset($terminalClientConfig['TerminalClientConfigId']);
            $res5 = $terminalClientConfigModel->table('TerminalClientConfig')->add($terminalClientConfig);
            if ($res5 === false) {
                throw new \Exception('客户端配置错误');
            }

            /***************景区配置******************/
            $terminalScenicSpotRModel = new TerminalScenicSpotR();
            $terminalScenicSpotRModel->table('TerminalScenicSpotR')->where(['TerminalNo' => $terminalSelfHelp['TerminalNo']])->delete();
            $scenicSpotR = [];
            foreach ($terminalScenicSpotR as $v) {
                $v['TrDateTime'] = $v['TrDate'];
                unset($v['TerminalScenicSpotId']);
                unset($v['TrDate']);
                unset($v['UPrice']);
                $scenicSpotR[] = $v;
            }

            if (!empty($scenicSpotR)) {
                $res6 = $terminalScenicSpotRModel->table('TerminalScenicSpotR')->addAll($scenicSpotR);
                if ($res6 === false) {
                    throw new \Exception('景区配置错误');
                }
            }

            /***************门票配置****************/
            $ticketConfigModel = new TicketConfig();
            $ticketConfigModel->table('TicketConfig')->where(['TerminalNo' => $terminalSelfHelp['TerminalNo']])->delete();
            $ticketArr = [];
            foreach ($ticketConfig as $v) {
                $data = [];
                foreach ($v['Ticket']['scence_names'] as $vv) {
                    $vv['expression'] = empty($vv['expression']) ? '' : $vv['expression'];
                    $vv['time']       = empty($vv['time']) ? '' : $vv['time'];
                    $vv['x']          = empty($vv['x']) ? 0 : intval($vv['x']);
                    $vv['y']          = empty($vv['y']) ? 0 : intval($vv['y']);
                    $vv['qrcodex']    = empty($vv['qrcodex']) ? 0 : intval($vv['qrcodex']);
                    $vv['qrcodey']    = empty($vv['qrcodey']) ? 0 : intval($vv['qrcodey']);
                    $data[]           = $vv;
                }

                foreach ($v['Ticket']['Childs'] as $vv) {
                    $vv['expression'] = empty($vv['expression']) ? '' : $vv['expression'];
                    $vv['time']       = empty($vv['time']) ? '' : $vv['time'];
                    $vv['x']          = empty($vv['x']) ? 0 : intval($vv['x']);
                    $vv['y']          = empty($vv['y']) ? 0 : intval($vv['y']);
                    $vv['qrcodex']    = empty($vv['qrcodex']) ? 0 : intval($vv['qrcodex']);
                    $vv['qrcodey']    = empty($vv['qrcodey']) ? 0 : intval($vv['qrcodey']);
                    $data[]           = $vv;
                }

                $newJson = json_encode($data);

                $v['TicketJsonConfig'] = $newJson;
                $v['TrDateTime']       = $v['TrDate'];
                unset($v['TrDate']);
                unset($v['TicketConfigId']);
                unset($v['Ticket']);
                $ticketArr[] = $v;
            }

            if (!empty($ticketArr)) {
                $res7 = $ticketConfigModel->table('TicketConfig')->addAll($ticketArr);
                if ($res7 === false) {
                    throw new \Exception('门票配置错误');
                }
            }

            $terminalSelfHelpModel->commit();
            $this->apiReturn(200);
        } catch (\Exception $e) {
            $terminalSelfHelpModel->rollback();
            $this->apiReturn(400, [], $e->getMessage());
        }
    }
}