<?php
/**
 * 主板串口管理
 */

namespace Controller\TerminalManage;


class DevCom extends BaseTerminalManage
{
    public function addOrUpdateConfig()
    {
        try {
            $params = [
                'id' => I('post.id'),
                'motherboard_name' => I('post.motherboard_name'),
                'manufacturer' => I('post.manufacturer'),
                'screen_remark' => I('post.screen_remark'),
                'remark' => I('post.remark'),
                'com_node_json' => I('post.com_node_json'),
            ];
            $this->handleResponse('Device/DevCom/addOrUpdateConfig', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }

    public function getDetail()
    {
        try {
            $params['id'] = I('post.id');
            $this->handleResponse('Device/DevCom/getDetail', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }

    public function deleteConfig()
    {
        try {
            $params['id'] = I('post.id');
            $this->handleResponse('Device/DevCom/deleteConfig', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }

    public function getConfigList()
    {
        try {
            $params['keyword'] = I('post.keyword');
            $params['page'] = I('post.page', 1, 'intval');
            $params['page_size'] = I('post.page_size', 15, 'intval');
            $this->handleResponse('Device/DevCom/getConfigList', [$params]);
        } catch (\Exception $e) {
            $this->apiReturn(204, [], $e->getMessage());
        }
    }
}