<?php

namespace Controller\RiskManagement;


use Library\Controller;

class ShumeiRiskCheck extends Controller
{
    public function __construct()
    {

    }

    public function mobileCheck()
    {
        $mobile = I('post.mobile', '', 'strval');
        $event = I('post.event', 1, 'intval');

        $shumeiRiskCheck = new \Business\RiskManagement\ShumeiRiskCheck();
        $checkResult = $shumeiRiskCheck->mobileCheck($mobile, $event);

        $this->apiReturn($checkResult['code'], $checkResult['data'], $checkResult['msg']);
    }
}