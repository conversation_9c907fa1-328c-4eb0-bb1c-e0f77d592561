<?php
/**
 * 票属性配置服务
 * <AUTHOR>
 * @date 2021/03/17
 */

namespace Controller\productbusiness;

use Business\Authority\AuthContext;
use Business\JavaApi\CommodityCenter\TicketAttrConfig;
use Business\MemberLogin\MemberLoginHelper;
use Library\Controller;

class ticketAttrService extends Controller
{

    private $_memberId;
    private $_operatorId;
    private $_account;
    private $_saccount;

    private $_dtype;
    private $_sdtype;

    public function __construct()
    {
        parent::__construct();

        $loginInfo         = $this->getLoginInfo();
        $this->_memberId   = $loginInfo['sid'];
        $this->_operatorId = $loginInfo['memberID'];
        $this->_account    = $loginInfo['account'];
        $this->_saccount   = $loginInfo['saccount'];

        $this->_dtype      = $loginInfo['dtype'];
        $this->_sdtype     = $loginInfo['sdtype'];
    }

    /**
     * 保存门票票属性
     * <AUTHOR>
     * @date 2021/3/17
     *
     * @return json
     */
    public function saveLandAttr()
    {

        if (!in_array($this->_dtype, [0, 2, 6, 9, 18])) {
            $this->apiReturn(203, [], '该类型账号不允许编辑票类');
        }

        if (in_array($this->_sdtype, [1])) {
            $this->apiReturn(203, [], '该类型账号不允许编辑票类');
        }

        $submitData         = I('post.');
        $submitData['data'] = htmlspecialchars_decode($submitData['data']);
        $submitData         = json_decode($submitData['data'], true);

        $sid = $this->_memberId;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $proTypeAuth = (new AuthContext())->memberProductTypeAuth($sid);
        if (!in_array('A', $proTypeAuth)) {
            $this->apiReturn(403, [], '无发布景区产品权限！');
        }

        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        $ticketAttrConfigBiz = new TicketAttrConfig();
        $result              = $ticketAttrConfigBiz->saveLandAttr($submitData, $sid, $this->_operatorId, $subSid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取票信息
     * <AUTHOR>
     * @date 2021/3/17
     *
     * @return json
     */
    public function getLandAttr()
    {
        $ticketId = I('post.ticketId', 0, 'intval');

        if (empty($ticketId)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $account = $this->_account;
        $saccount = $this->_saccount;
        $memberId = $this->_memberId;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $account = MemberLoginHelper::getLoginBusinessMember()->getSupplierAccount();
            $saccount = MemberLoginHelper::getLoginBusinessMember()->getSupplierAccount();
            $memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $ticketAttrConfigBiz = new TicketAttrConfig();
        $result              = $ticketAttrConfigBiz->getTicket($ticketId, $account, $saccount, $memberId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 保存特产属性
     * <AUTHOR>
     * @date 2021/3/17
     *
     * @return json
     */
    public function saveSpecialty()
    {
        $submitData         = I('post.');
        $submitData['data'] = htmlspecialchars_decode($submitData['data']);
        $submitData         = json_decode($submitData['data'], true);

        $sid = $this->_memberId;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $proTypeAuth = (new AuthContext())->memberProductTypeAuth($sid);
        if (!in_array('J', $proTypeAuth)) {
            $this->apiReturn(403, [], '无发布景区产品权限！');
        }

        $ticketAttrConfigBiz = new TicketAttrConfig();
        $result              = $ticketAttrConfigBiz->saveSpecialty($submitData, $sid, $this->_operatorId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取特产所有信息
     * <AUTHOR>
     * @date 2021/3/17
     *
     * @return json
     */
    public function getSpecGoods()
    {
        $params = I('param.');

        if (empty($params)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $ticketAttrConfigBiz = new TicketAttrConfig();
        $result              = $ticketAttrConfigBiz->getSpecGoods($params);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

}
