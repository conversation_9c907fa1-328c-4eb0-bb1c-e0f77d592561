<?php
/**
 * 快速发布门票
 * <AUTHOR>  Li
 * @date  2022-04-07
 */

namespace Controller\productbusiness;

use Business\Authority\AuthContext;
use Business\CommodityCenter\ProductBusiness;
use Business\CommodityCenter\Ticket;
use Business\CommodityCenter\Land;
use Business\MemberLogin\MemberLoginHelper;
use Library\Controller;
use Business\Product\SubProduct as SubProductBiz;

class QuickReleaseTickets extends Controller
{
    private $_loginInfo;
    public function __construct()
    {
        parent::__construct();
        $this->_loginInfo = $this->getLoginInfo();
    }

    /**
     * 快速发布门票
     * <AUTHOR>  Li
     * @date  2022-04-07
     */
    public function setQuickTicket()
    {
        if (!in_array($this->_loginInfo['dtype'], [0, 2, 6, 9, 18])) {
            $this->apiReturn(203, [], '该类型账号不允许编辑票类');
        }

        if (in_array($this->_loginInfo['sdtype'], [1])) {
            $this->apiReturn(203, [], '该类型账号不允许编辑票类');
        }

        $submitData         = I('post.');
        $submitData['data'] = htmlspecialchars_decode($submitData['data']);
        $submitData         = json_decode($submitData['data'], true);

        $sid = $this->_loginInfo['sid'];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $proTypeAuth = (new AuthContext())->memberProductTypeAuth($sid);
        if (!in_array('A', $proTypeAuth)) {
            $this->apiReturn(403, [], '无发布景区产品权限！');
        }

        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        $productBiz = new ProductBusiness();
        $result     = $productBiz->setQuickTicket($submitData, $sid, $this->_loginInfo['memberID'], $subSid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取票信息
     * <AUTHOR>  Li
     * @date  2022-04-07
     */
    public function getQuickTicket()
    {
        $ticketId = I('post.ticket_id', 0, 'intval');

        if (empty($ticketId)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $productBiz = new ProductBusiness();
        $result     = $productBiz->getQuickTicket($this->_loginInfo['sid'], $ticketId);

        if (!$result) {
            $this->apiReturn(204, [], '票信息获取异常');
        }

        $this->apiReturn(self::CODE_SUCCESS, $result, '门票信息获取成功');
    }

    /**
     * 获取票名称列表
     * <AUTHOR> Li
     * @date 2022-04-07
     */
    public function queryTicketIdAndNameByItemId()
    {
        $landId = I('post.land_id', 0, 'intval');
        $status = I('post.status', 0, 'intval');
        $title  = I('post.title', '', 'strval');

        if (!$landId || !$status) {
            $this->apiReturn(203, [], '参数有误');
        }

        $javaApi = new Ticket();
        $result  = $javaApi->queryTicketIdAndNameByItemId($landId, $status, $title);

        $this->apiReturn(self::CODE_SUCCESS, $result, '门票信息获取成功');
    }

    /**
     * 根据条件获取下拉产品列表
     * <AUTHOR> Li
     * @date 2022-04-07
     */
    public function selectLandByCond()
    {
        $page   = I('post.page', 1, 'intval');
        $size   = I('post.size', 10, 'intval');
        $status = I('post.status', 1, 'intval');
        $pType  = I('post.p_type', '', 'strval');
        $title  = I('post.title', '', 'strval');

        $sid = $this->_loginInfo['sid'];
        $subMerchantId = 0;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $subMerchantId = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        //子产品类型处理下ptype=A-1的情况
        list($pType, $subType) = SubProductBiz::decodeTypeAndSubType($pType);

        $javaApi = new Land();
        $result  = $javaApi->selectLandByCond($sid, $page, $size, $pType, $status, $title, $subMerchantId, $subType);

        $this->apiReturn(self::CODE_SUCCESS, $result, '产品信息获取成功');
    }

}
