<?php
/**
 * 门票产品线 - 门票新增修改接口
 * <AUTHOR>
 * @date 2023/02/17
 */

namespace Controller\productbusiness;

use Business\Authority\AuthContext;
use Business\JavaApi\CommodityCenter\LandTicketService as LandTicketServiceBiz;
use Library\Controller;

class landTicketService extends Controller
{

    private $_memberId;
    private $_operatorId;
    private $_account;
    private $_saccount;

    private $_dtype;
    private $_sdtype;

    public function __construct()
    {
        parent::__construct();

        $loginInfo         = $this->getLoginInfo();
        $this->_memberId   = $loginInfo['sid'];
        $this->_operatorId = $loginInfo['memberID'];
        $this->_account    = $loginInfo['account'];
        $this->_saccount   = $loginInfo['saccount'];

        $this->_dtype  = $loginInfo['dtype'];
        $this->_sdtype = $loginInfo['sdtype'];
    }

    /**
     * 保存门票票属性
     * <AUTHOR>
     * @date 2021/3/17
     *
     * @return json
     */
    public function saveLandTicket()
    {
        if (!in_array($this->_dtype, [0, 2, 6, 9])) {
            $this->apiReturn(203, [], '该类型账号不允许编辑票类');
        }

        if (in_array($this->_sdtype, [1])) {
            $this->apiReturn(203, [], '该类型账号不允许编辑票类');
        }

        $submitData         = I('post.');
        $submitData['data'] = htmlspecialchars_decode($submitData['data']);
        $submitData         = json_decode($submitData['data'], true);

        $proTypeAuth = (new AuthContext())->memberProductTypeAuth($this->_memberId);
        if (!in_array('A', $proTypeAuth)) {
            $this->apiReturn(403, [], '无发布景区产品权限！');
        }

        $landTicketService = new LandTicketServiceBiz();
        $result            = $landTicketService->saveLandTicket($submitData, $this->_memberId, $this->_operatorId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 保存快速票
     * <AUTHOR>
     * @date 2021/3/17
     *
     * @return json
     */
    public function saveQuickLandTicket()
    {
        if (!in_array($this->_dtype, [0, 2, 6, 9])) {
            $this->apiReturn(203, [], '该类型账号不允许编辑票类');
        }

        if (in_array($this->_sdtype, [1])) {
            $this->apiReturn(203, [], '该类型账号不允许编辑票类');
        }

        $submitData         = I('post.');
        $submitData['data'] = htmlspecialchars_decode($submitData['data']);
        $submitData         = json_decode($submitData['data'], true);

        $proTypeAuth = (new AuthContext())->memberProductTypeAuth($this->_memberId);
        if (!in_array('A', $proTypeAuth)) {
            $this->apiReturn(403, [], '无发布景区产品权限！');
        }

        $landTicketService = new LandTicketServiceBiz();
        $result            = $landTicketService->saveQuickLandTicket($submitData, $this->_memberId, $this->_operatorId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

}
