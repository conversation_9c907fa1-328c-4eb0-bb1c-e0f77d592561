<?php
/**
 * 系统设置中的日志服务
 * <AUTHOR>
 * @date 2019-11-07
 */

namespace Controller\SystemSetting;

use Library\Controller;
use Business\PftSystem\LogService as LogServiceBiz;
use Library\SimpleExcel;
use Model\Member\MemberRelationship;
use Model\Member\Member;
use Model\Product\Land;
use Business\Staff\OperatorRecord;

class LogService extends Controller
{
    // 登陆信息
    private $_loginInfo;

    public function __construct()
    {
        parent::__construct();
        $this->_loginInfo = $this->getLoginInfo();
    }

    /**
     * 产品信息日志
     * <AUTHOR>
     * @date 2019-11-07
     *
     * @param  int fid 商户ID
     * @param  string f_name 商户名称
     * @param  string start_time 开始时间
     * @param  string end_time 结束时间
     * @param  int operator_id 操作用户
     * @param  int pid 产品ID
     * @param  string p_name 产品名称
     * @param  int tid 门票ID
     * @param  string t_name 门票名称
     * @param  int operator_type 操作类型
     * @param  string field 修改的具体属性
     * @param  int page_number 页码,从0开始
     * @param  int page_size 每页显示数量
     *
     * @return array
     */
    public function productLog()
    {
        $fid          = I('get.fid', 0, 'intval');
        $fidName      = I('get.f_name', '', 'strval');
        $startTime    = I('get.start_time', '', 'strval');
        $endTime      = I('get.end_time', '', 'strval');
        $operatorId   = I('get.operator_id', 0, 'intval');
        $operatorName = I('get.operator_name', '', 'strval');
        $pid          = I('get.pid', 0, 'intval');
        $pname        = I('get.p_name', '', 'strval');
        $tid          = I('get.tid', 0, 'intval');
        $tname        = I('get.t_name', '', 'strval');
        $operatorType = I('get.operator_type', 0, 'intval');
        $field        = I('get.field', '', 'strval');
        $pageNumber   = I('get.page_number', 0, 'intval');
        $pageSize     = I('get.page_size', 15, 'intval');
        $lid          = I('get.lid', 0, 'intval');
        //分销商 供应商也能看对应名下的产品信息日志
        $ordinaryPermissions = I('get.permissions', 0, 'intval');

        $sid = $this->_loginInfo['sid'];
        if ($sid != 1 && $ordinaryPermissions === 0) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '非管理员无权操作');
        }

        $business = new LogServiceBiz();
        if ($sid != 1 && $ordinaryPermissions === 0) {
            $res = $business->productLog($this->_loginInfo['memberID'], $sid, $fid, $fidName, $pid, $pname, $tid,
                $tname, $operatorId, $operatorName, $operatorType, $field, $startTime, $endTime, $pageNumber,
                $pageSize);
        } else {
            //java供应商查下级用户日志
            $res = $business->productLog($this->_loginInfo['memberID'], $sid, $fid, $fidName, $pid, $pname, $tid,
                $tname, $operatorId, $operatorName, $operatorType, $field, $startTime, $endTime, $pageNumber, $pageSize,
                $ordinaryPermissions, $lid);

        }

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 价格日志查询
     * @author: zhangyz
     * @date: 2020/3/30
     *
     * @param  array uuids 上一个列表页接口返回
     * @param  int page_number 页码，从0开始
     * @param  int page_size 每页显示数量
     */
    public function priceLog()
    {
        $uuids      = I('post.uuids', []);
        $pageNumber = I('post.page_number', 0, 'intval');
        $pageSize   = I('post.page_size', 10, 'intval');
        $startTime  = I('post.start_time', '', 'strval');
        $endTime    = I('post.end_time', '', 'strval');
        //分销商 供应商也能看对应名下的产品信息日志
        $ordinaryPermissions = I('post.permissions', 0, 'intval');

        $sid = $this->_loginInfo['sid'];
        if ($sid != 1 && $ordinaryPermissions === 0) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '非管理员无权操作');
        }
        //供应商/供应商下的员工也可看
        if ($ordinaryPermissions !== 0) {
            $sid = 1;
        }

        $business = new LogServiceBiz();
        $res      = $business->priceLog($this->_loginInfo['memberID'], $sid, $uuids, $pageNumber, $pageSize, $startTime,
            $endTime);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取具体属性key => value
     * <AUTHOR>
     * @date 2019-11-08
     */
    public function getFieldKey()
    {
        $business = new LogServiceBiz();
        $res      = $business->getFieldKey();

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取操作员列表
     * <AUTHOR>
     */
    public function getOperator()
    {
        if (!$this->isSuper()) {
            $this->apiReturn(400, '', '无权限');
        }

        $memberId = I('get.member_id', 0);

        if (empty($memberId)) {
            $this->apiReturn(400, '', '供应商id不能为空');
        }

        //获取员工信息
        $memberRelationModel = new MemberRelationship();
        $memberRelationRes   = $memberRelationModel->getSonInfo($memberId);
        $memberIdArr         = [];
        if (!empty($memberRelationRes) && is_array($memberRelationRes)) {
            $memberIdArr = array_column($memberRelationRes, 'id');
        }
        $memberModel = new Member();
        $memberRes   = $memberModel->getMemberInfoByMultiAndKeyWord($memberIdArr, 'id, dname');

        $data = [];
        if (!empty($memberRes) && is_array($memberRelationRes)) {
            foreach ($memberRes as $item) {
                $data[] = [
                    'id'   => $item['id'],
                    'name' => $item['dname'],
                ];
            }
        }

        $this->apiReturn(200, $data, '');
    }

    /**
     * 管理员账号下搜索景区
     * @author: zhangyz
     * @date: 2020/4/23
     */
    public function searchLand()
    {
        //暂时日志查询页只允许管理员操作，后续开放给供应商的话，需要使用供应商ID进行查询
        //可以参考订单查询页，搜索景区的接口
        //供应商也能看对应名下的景区
        $ordinaryPermissions = I('post.permissions', 0, 'intval');

        if (!$this->isSuper() && $ordinaryPermissions === 0) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '非管理员无权操作');
        }

        $keyword = I('post.keyword', '', 'strval,trim');

        if (empty($keyword)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入关键字查询');
        }

        $lidArr     = [];
        $title      = '';
        $lettersArr = [];

        if (mb_strlen($keyword) == strlen($keyword)) {
            $lettersArr = [$keyword];
            //英文
            //$searchType = 2;
        } else {
            $title = $keyword;
            //汉字
            //$searchType = 1;
        }

        $sid = $this->_loginInfo['sid'];

        $landApi     = new \Business\CommodityCenter\Land();
        $landInfoRes = $landApi->queryLandMultiQueryByAdminAndPaging($lidArr, 1, 200, $title, 'id desc',
            false, [$sid], [], $lettersArr);

        $landInfo = [];
        if (!empty($landInfoRes['list'])) {
            $landInfo = $landInfoRes['list'];
        }

        //$landModel = new Land();
        //$landInfo  = $landModel->getProductInfoByKeyWord($keyword, $searchType, 'id, title, salerid', '', 0, $sid);

        $this->apiReturn(self::CODE_SUCCESS, $landInfo, 'success');
    }

    /**
     * 商家端登录日志
     * @author: xwh
     * @date: 2021/12/20
     */
    public function loginLog()
    {
        $pageSize    = I('pageSize', 20, 'intval');
        $currentPage = I('currentPage', 1, 'intval');
        $act         = I('act', 'View');
        $btime       = I('btime', '');
        $etime       = I('etime', '');
        $dname       = I('dname', '');
        $passRecord  = I('passRecord', 0);
        $sid         = $this->_loginInfo['sid'];
        $currentPage = $currentPage > 0 ? $currentPage : 1;
        $offset      = ($currentPage - 1) * $pageSize;
        $optRecUtil  = new OperatorRecord();
        $sonIdRes    = $optRecUtil->getSonIds($sid, $dname);
        $failFlag    = false;

        //限制下查询时间，只能查询最近180天的数据
        $limitDays       = 180;
        $lastedTimestamp = strtotime("-{$limitDays} day");
        $lastedDay       = date("Y-m-d", $lastedTimestamp);

        if (strtotime($btime) < strtotime($lastedDay)) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], "只能查询最近{$limitDays}天的数据");
        }

        if ($sonIdRes['code'] == 0) {
            //获取员工信息失败
            $failFlag = true;
        } else if ($sonIdRes['code'] == 1) {
            //获取匹配dname员工信息成功
            $sonIds     = $sonIdRes['data']['sonIds'];
            $memberInfo = $sonIdRes['data']['memberInfo'];
        } else {
            //获取全部员工信息成功
            $sonIds     = []; //查询全部员工，无需传入员工id
            $memberInfo = $sonIdRes['data']['memberInfo'];
        }

        if ($failFlag == false) {
            //员工信息查询正常
            if ($act == "Export") {
                //获取导出的数据
                $result = $optRecUtil->getRecords('Export', $sid, $sonIds, $memberInfo, $btime, $etime, null, null,
                    $passRecord);
                //导出excel
                $optRecUtil->exportExcel($result);
            } else {
                //获取展示的数据
                $result = $optRecUtil->getRecords('View', $sid, $sonIds, $memberInfo, $btime, $etime, $offset,
                    $pageSize, $passRecord);
                //获取总记录数
                $count = $optRecUtil->getTotalRecord($sid, $sonIds, $btime, $etime, $passRecord);

                $this->apiReturn(self::CODE_SUCCESS, ['list' => $result, 'count' => $count], 'success');
            }
        } else {
            //员工信息查询失败，返回无结果
            $result = [];
            if ($act == "Export") {
                $optRecUtil->exportExcel($result);
            } else {
                $this->apiReturn(self::CODE_SUCCESS, ['list' => $result, 'count' => 0], 'success');
            }
        }
    }
    /**
     * 合作伙伴日志查询接口
     * @author: xwh
     * @date: 2021/12/21
     *
     * @param  int $memberId 登录用户主ID
     * @param  int $partnerType 0是分销商 1是供应商
     * @param  int $fid
     * @param  int $opId 操作人id
     * @param  int $opType 操作类型 : 1 新增 2 修改 3 删除 4还款
     * @param  string $opTime 开始时间
     * @param  string $endTime 结束时间
     *
     * @return array
     */
    public function partnerLog()
    {
        $size        = I('size', 20, 'intval');
        $page        = I('page', 1, 'intval');
        $fid         = I('fid', 0, 'intval');
        $opId        = I('opid', 0, 'intval');
        $memberId    = $this->_loginInfo['sid'];
        // 0是分销商 1是供应商
        $partnerType = I('partner_type', -1, 'intval');
        //-1 全部 1 新增 2 修改 3 删除 4还款
        $opType      = I('op_type', -1, 'intval');
        //开始时间
        $opTime      = I('op_time', '', 'strval,trim');
        //结束时间
        $endTime     = I('end_time', '', 'strval,trim');
        $result = (new \Business\PftSystem\LogService())->partnerLog($memberId,  $partnerType,  $fid, $opId, $opType, $opTime, $endTime, $size, $page);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
    /**
     * 获取相关商家列表(供应商和分销商)
     * @return  mixed
     */
    public function getRelevantMerchants()
    {
        $dname    = \safe_str(I('dname'));
        $mobile   = \safe_str(I('mobile'));
        $account  = \safe_str(I('account'));
        $memberId = $this->_loginInfo['sid'];
        $limit    = intval(I('limit')) ?: 20;
        $result   = (new \Business\Member\MemberRelation())->getRelevantMerchants($memberId, $dname, $mobile, $account,
            $limit);

        $this->apiReturn(200, $result, '成功');
    }
}