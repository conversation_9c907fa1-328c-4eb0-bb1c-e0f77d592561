<?php
/**
 * 订单批量操作相关的
 * <AUTHOR>
 * @date   2022/5/9
 */

namespace Controller\Order;

use Business\MemberLogin\MemberLoginHelper;
use Library\Controller;
use Business\Order\BatchOperate as BatchOperateBiz;

class BatchOperate extends Controller
{
    private $_sid      = 0;
    private $_login    = null; //登录全部信息
    private $_memberId = 0; //登录账号id

    public function __construct()
    {
        parent::__construct();

        $this->_sid = $this->isLogin('ajax');

        $loginInfo       = $this->getLoginInfo();
        $this->_login    = $loginInfo;
        $this->_memberId = $loginInfo['memberID'];
    }

    /**
     * 批量记录列表
     * <AUTHOR>
     * @date   2022/5/12
     *
     */
    public function batchOrderList()
    {
        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }
        $oper   = I('post.oper', 0, 'intval'); //操作人id
        $begin  = I('post.begin', '', 'strval'); //开始时间
        $end    = I('post.end', '', 'strval'); //结束时间
        $status = I('post.status', -1, 'intval');//0未处理 1处理中 2处理完成 3已终止
        $ptype  = I('post.p_type', '', 'strval'); //产品类型
        $page   = I('post.page', 1, 'intval'); //页码
        $size   = I('post.size', 10, 'intval'); //页数
        $type   = I('post.type', 99, 'intval'); //1变更有效期 2取消 3验证 4完结 99批量下单

        if (!$type) {
            $this->apiReturn(400, [], '参数错误');
        }

        $result = (new BatchOperateBiz())->batchOrderList($this->_sid, $type, $begin, $end, $status, $ptype, $oper, $page, $size);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 批量操作记录（结果）导出
     * <AUTHOR>
     * @date   2022/4/27
     *
     */
    public function exportBatchRecord()
    {
        $mainId = I('post.id', 0, 'intval');
        $type   = I('post.type', 1, 'intval'); //1变更有效期 2取消 3验证 4完结 99批量下单
        if (!$mainId || !$type) {
            $this->apiReturn(400, [], '参数缺失，请重试');
        }

        $result = (new BatchOperateBiz())->exportBatchRecord($this->_sid, $type, $mainId, $this->_memberId);

        if (isset($result['code'])) {
            $fileName  = '批量操作记录_' . date("Y_m_d_H_i_s");
            $sheetName = '批量操作记录';
            $this->excelReturn($fileName, $sheetName, $result['data'] ?? []);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 批量操作终止
     * <AUTHOR>
     * @date   2022/5/12
     *
     */
    public function stopBatch()
    {
        $mainId = I('post.id', 0, 'intval');
        $type   = I('post.type', 1, 'intval'); //1变更有效期 2取消 3验证 4完结 99批量下单
        if (!$mainId || !$type) {
            $this->apiReturn(400, [], '参数缺失，请重试');
        }

        $result = (new BatchOperateBiz())->stopBatch($this->_sid, $type, $mainId, $this->_memberId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 批量取消操作接口
     * <AUTHOR>
     * @date   2022/5/9
     *
     */
    public function cannalOrder()
    {
        $orderStr = I('post.ordernum', '', 'string');
        $orders   = str_replace("，", ",", $orderStr);
        $orders   = explode(",", $orders);

        $list = [];
        foreach ($orders as $item) {
            if (empty($item)) {
                continue;
            }
            $list[] = [
                'ordernum' => $item,
            ];
        }

        if (empty($list)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $result = (new BatchOperateBiz())->createOperate($this->_sid, $this->_memberId, $list,
            BatchOperateBiz::ACTION_TYPE_CANCEL);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 处理订单查询 批量取消订单操作
     * 注：基本之前new/d/call/handle.php:batch_handle_orders 处理来的
     * <AUTHOR>
     * @date   2022/5/10
     *
     */
    public function batchHandleOrders()
    {
        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }
        $params = I('param.');

        $result = (new BatchOperateBiz())->handleSearchOrder($params, $this->_login);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}