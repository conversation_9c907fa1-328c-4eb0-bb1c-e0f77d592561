<?php


namespace Controller\Order;


use Business\Captcha\CaptchaApi;
use Business\JsonRpcApi\PayService\UnifiedPay;
use Business\Order\MergeOrder;
use Business\Pay\PayBase;
use Business\RiskManagement\ShumeiRiskCheck;
use Controller\Finance\Recharge;
use Library\Controller;
use Library\Kafka\KafkaProducer;
use Model\Order\OrderQuery;
use Model\TradeRecord\OnlineTrade;
use Process\Order\Validate\MergeOrderPreCheck\OrderPreCheckException;

class OnlinePay extends Controller
{

    const ORDER_PAY_NOTIFY_URL = PAY_DOMAIN.'r/pay_CommonPayNotify/payNotify';
    private $sourceT = 41; // 与CommonPayNotify::platformRechargeNotify保持一致

    public function __construct()
    {
        
    }

    /**
     * 平台订单在线支付（扫码付款，非独立收款）
     * url: my.12301.cc/r/Order_OnlinePay/platformPayQr
     * @throws \Exception
     */
    public function platformPayQr()
    {
        $loginInfo = $this->getLoginInfo('ajax');
        $logData = [
            'post'  => $_POST,
            'refer' => $_SERVER['HTTP_REFERER'],
            'agent' => $_SERVER['HTTP_USER_AGENT'],
            'reqid' => $_SERVER['REQUEST_ID'],
            'mid'   => $loginInfo['memberID'],
        ];
        pft_log('yeepay/order_req', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        $payType = I('post.pay_type', 2, 'intval');
        //平台订单号
        $outTradeNo = I('post.out_trade_no', 0, 'string');
        //是否二维码支付，0 公众号里面支付，1 二维码支付，2 H5支付（手机浏览器跳小程序）
        $qrPay = I('post.is_qr', 0, 'intval');
        $captchaCode = I('post.captchaCode', '', 'strval');

        //订单主体说明
        $subject = mb_substr(trim(I('subject')), 0, 20, 'utf-8') . "[{$outTradeNo}]";
        if (!$outTradeNo || !$subject) {
            parent::apiReturn(401, [], '参数缺失');
        }
        PayBase::setPayDailyRecord(3, '订单请求', I('post.'), []);
        $mergeOrder = new MergeOrder();
        if ($mergeOrder->isCombineOrder($outTradeNo)) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($outTradeNo);
            try {
                $mergeOrder->handlerCombinePayLog($outTradeNo, '', $this->sourceT);
            } catch (OrderPreCheckException $e) {
                parent::apiReturn(401, [], $e->getMessage());
            }
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string)$outTradeNo);
        }

        $money      = number_format($totalFee / 100, 2, '.', '');
        if (in_array($payType, [1, 2])){
            //数美风控校验
            $this->_riskShumeiRecordPayOrder($loginInfo['account'], $money, $outTradeNo, $captchaCode);
        }

        $frontUrl   = I('post.success_url', $_SERVER['HTTP_REFERER']);
        $goodsName  = "支付订单[{$outTradeNo}]";

        $payMethod  = OnlineTrade::PAY_METHOD_ORDER;
        $tradeModel = new OnlineTrade();
        $ret        = $tradeModel->addLog(
            $outTradeNo, $money, $subject, $subject, $this->sourceT,
            $payMethod
        );
        if (!$ret) {
            parent::apiReturn(401, [], '支付记录生成失败');
        }
        // 调用支付中心收银台页面
        $unifiedPay  = new UnifiedPay();
        $rpcResponse = $unifiedPay->unifyQrPayRpcService($outTradeNo, $goodsName, $money*100, 1, $payType,
            'platform', self::ORDER_PAY_NOTIFY_URL, $_SERVER['REMOTE_ADDR'], '',
            ['pft_member_id'=>$loginInfo['sid'],'source'=>'platform_order_pay']);

        if ($rpcResponse['code'] == 200) {
            $data = [
                'outTradeNo' => $outTradeNo,
                'qrUrl'      => $rpcResponse['data']['url'],
            ];
            parent::apiReturn(200, $data);
        }
        parent::apiReturn(401, [], $rpcResponse['msg']);
    }

    private function _riskShumeiRecordPayOrder($account, $money, $outTradeNo, $captchaCode)
    {
        if (empty($_SERVER['HTTP_DEVICEID'])){
            return;
        }
        //滑块验证
        if (empty($captchaCode)){
            $riskCheck = new ShumeiRiskCheck();
            $virtualOrderEventData = [
                'product'   => '购买商品',
                'orderId'   => $outTradeNo,
                'price'     => floatval($money),
            ];
            $orderCheckRes = $riskCheck->shumeiCheck('virtualOrder', 3, $account, $virtualOrderEventData);

            $paymentEventData = [
                'method'    => 'qrscan',
                'channel'   => 'qrscan',
                'amount'    => floatval($money),
                'orderId'   => $outTradeNo,
            ];
            $payCheckRes      = $riskCheck->shumeiCheck('payment', 3, $account, $paymentEventData);
            if (($payCheckRes['code'] == 200 && $payCheckRes['data']['riskLevel'] == 'REJECT') || ($orderCheckRes['code'] == 200 && $orderCheckRes['data']['riskLevel'] == 'REJECT')) {
                $this->apiReturn(200, ['need_captcha' => true], '请验证滑块');//前端那边判断非200就会弹窗, 所以改成200
            }
        }else{
            $captchaCheck = new CaptchaApi();
            $captchaRes   = $captchaCheck->handleSecondCheck($captchaCode);
            if ($captchaRes !== true) {
                $this->apiReturn(200, ['need_captcha' => true], '请验证滑块');//前端那边判断非200就会弹窗, 所以改成200
            }
        }
    }

    public function payResultCheck()
    {
        $this->isLogin('ajax');
        $ordernum = I('post.ordernum');
        pft_log('yeepay/pay_result_check', json_encode($_POST, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        $model = new OnlineTrade();

        $payLog = $model->getLog($ordernum, $this->sourceT);
        if (!$payLog) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
        }

        if ($payLog['status'] == 1) {
            parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看', true);
    }
}