<?php

namespace Controller\Order;

use Business\Authority\AuthLogic as AuthLogicBiz;
use Library\Controller;
use Business\Order\OrderInfoHiding as OrderInfoHidingBiz;

/**
 * 针对中间商隐藏订单部分信息
 * <AUTHOR>
 */
class OrderInfoHiding extends Controller
{

    private $_sid            = 0;
    private $_operId;
    private $_orderHidingBiz = null;
    // 登陆信息
    private $_loginInfo;


    public function __construct()
    {
        parent::__construct();
        $loginInfo             = $this->getLoginInfo();
        $this->_sid            = $loginInfo['sid'];
        $this->_operId         = $loginInfo['memberID'];
        $this->_orderHidingBiz = new OrderInfoHidingBiz();

        $this->_loginInfo = $loginInfo;
    }

    /**
     * 添加配置操作
     *
     * <AUTHOR>
     * @date 2021/4/22
     *
     * @return array
     */
    public function addConfigHiding()
    {
        $configName   = I('config_name', '', 'strval,trim');
        $configType   = I('config_type', 0, 'intval');//当前全部自供应票 0 个别自供应票 1
        $hiddenObject = I('hidden_object', []);
        $userData     = I('user_data', []);
        $lid          = I('lid', 0, 'intval');
        $ticketData   = I('ticket_data', []);
        $configStaff  = I('config_staff_type', 1, 'intval');//针对员工  1 个别 2全部
        $status       = I('config_status', 1, 'intval');//0 关闭1开启

        $result = $this->_orderHidingBiz->addConfigHiding($configName, $configType, $lid, $status, $this->_sid,
            $this->_operId,
            $hiddenObject, $userData, $ticketData, $configStaff);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 更新配置信息
     *
     * <AUTHOR>
     * @date 2021/4/22
     *
     * @param  array $hiddenObject
     * {
     *      "middleman":
     *      {
     *       "status" : 是否开启
     *       "taker_info": 0关闭,1开启 取票人信息
     *       "tourists_info": 0关闭,1开启 游客信息
     *       "code_info": 0关闭,1开启  凭证信息
     *       },
     *     "resource":
     *      {
     *       "status" : 是否开启
     *       "taker_info": 0,
     *       "tourists_info": 0,
     *       "code_info": 0
     *       },
     *    "employees":
     *      {
     *        "status" : 是否开启
     *       "taker_info": 0,
     *       "tourists_info": 0,
     *       "code_info": 0
     *       },
     *   }
     *
     * @return array
     */
    public function updateHidingConfig()
    {
        $configId        = I('config_id', 0, 'intval');
        $configName      = I('config_name', '', 'strval,trim');
        $configType      = I('config_type', 0, 'intval');//当前全部自供应票 0 个别自供应票 1
        $lid             = I('lid', 0, 'intval');
        $configStatus    = I('config_status', 0, 'intval'); // 0关闭 1开启
        $employeesStatus = I('employees_status', 0, 'intval');//针对员工是否开启
        $configStaff     = I('config_staff_type', 1, 'intval');//针对员工  1 个别 2全部
        $hiddenObject    = I('hidden_object', []);
        //[['member_id'=>888,'user_type'=>0]]
        $userData = I('user_data', []);
        //['add' => ['tid'=>888,'lid'=>111], 'del' => ['tid'=>888,...]]
        $ticketData = I('ticket_data', []);

        $result = $this->_orderHidingBiz->updateHidingConfig($configId, $configName, $this->_sid, $lid, $this->_operId,
            $configType,
            $hiddenObject, $userData, $ticketData, $configStatus, $configStaff, $employeesStatus);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 更新配置状态
     *
     * <AUTHOR>
     * @date 2021/4/22
     *
     * @param  array $hiddenObject
     *
     * @return array
     */
    public function updateHidingConfigStatus()
    {
        $configId     = I('config_id', 0, 'intval');//配置id
        $configStatus = I('config_status', 0, 'intval');//状态 0 关闭1开启 3删除

        if (!$configId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $result = $this->_orderHidingBiz->updateHidingConfigStatus($configId, $configStatus, $this->_operId, $this->_sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取订单信息隐藏配置列表
     *
     * <AUTHOR>
     * @date 2021/4/22
     *
     * @return array
     */
    public function getHidingConfigList()
    {
        $configId     = I('config_id', 0, 'intval');//配置id
        $configStatus = I('config_status', -1, 'intval');//状态 0 关闭1开启 3删除
        $configName   = I('config_name', '', 'strval,trim');
        $lid          = I('lid', 0, 'intval');
        $tid          = I('tid', 0, 'intval');
        $page         = I('page', 1, 'intval');
        $size         = I('size', 10, 'intval');

        $result = $this->_orderHidingBiz->getHidingConfigList($this->_sid, $configId, $configStatus, $configName, $lid,
            $tid,
            $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取配置关联的用户（员工、资源账号）
     *
     * <AUTHOR>
     * @date 2020-12-10
     *
     * @return array
     */
    public function getConfigRelatedUser()
    {
        $configId      = I('config_id', 0, 'intval');//配置id
        $type          = I('type', 0, 'intval');// 1员工
        $accountMobile = I('account_mobile', '', 'strval,trim');
        $page          = I('page', 1, 'intval');
        $size          = I('size', 10, 'intval');
        if (!$configId || !in_array($type, [0, 1])) {
            $this->apiReturn(203, [], '参数错误');
        }

        $authLogicBiz = new AuthLogicBiz();
        $isStaff      = $authLogicBiz->checkIsStaff($this->_sid, $this->_loginInfo['memberID'], $this->_loginInfo['dtype']);
        $result = $this->_orderHidingBiz->getConfigRelatedUser($this->_sid,$configId, $type, $accountMobile, 0,
            $page, $size, $isStaff);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取配置关联的票
     *
     * <AUTHOR>
     * @date 2020-12-10
     *
     * @return array
     */
    public function getConfigRelatedTickerList()
    {
        $configId = I('config_id', 0, 'intval');//配置id
        $status   = I('status', '', 'intval'); //0 正常 1 删除
        $tid      = I('tid', 0, 'intval');
        $ptype    = I('ptype', '', 'strval,trim');
        $lid      = I('lid', 0, 'intval');
        $page     = I('page', 1, 'intval');
        $size     = I('size', 10, 'intval');
        if (!$configId) {
            $this->apiReturn(203, [], '参数错误');
        }
        $result = $this->_orderHidingBiz->getConfigRelatedTickerList($configId, $tid, $status, $ptype, $lid, $page,
            $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取当前登录用户可以配置得票列表（上架的，没有配置过）
     *
     * <AUTHOR>
     * @date 2020-12-10
     *
     * @return array
     */
    public function getNotConfigRelatedTickerList()
    {
        // 景区id
        $landId              = I('post.lid', 0, 'intval');
        $notFiltrateConfigId = I('post.not_filtrate_config_id', 0, 'intval');

        if (empty($landId)) {
            $this->apiReturn(400, '', '景区不能为空');
        }
        $result = $this->_orderHidingBiz->getNotConfigTicket($landId, $this->_sid, $notFiltrateConfigId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取当前配置关联的资源方列表
     *
     * <AUTHOR>
     * @date 2020-12-10
     *
     * @return array
     */
    public function getRelatedResourcesList()
    {
        // 配置id
        $configId    = I('post.config_id', 0, 'intval');
        $resourcesId = I('post.resource_id', 0, 'intval');
        $ptype       = I('post.ptype', '', 'strval,trim');
        $page        = I('post.page', 1, 'intval');
        $size        = I('post.size', 10, 'intval');

        if (empty($configId)) {
            $this->apiReturn(400, '', '参数粗无');
        }
        $result = $this->_orderHidingBiz->getRelatedResourcesList($this->_sid, $configId, $ptype,
            $resourcesId, $page,
            $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}