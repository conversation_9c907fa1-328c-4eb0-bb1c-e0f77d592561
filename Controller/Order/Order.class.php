<?php
/**
 * PC平台合并付款下单接口
 * Author: hanwen<PERSON>
 * Date: 2018/11/25
 */

namespace Controller\Order;

use Business\Captcha\CaptchaApi;
use Business\Captcha\OrderCaptcha;
use Business\Order\MergeOrder;
use Business\Product\Specialty;
use Library\Constants\Order\OrderChannel;
use Library\Controller;
use Library\Kafka\KafkaProducer;
use Library\Tools\Helpers;
use Model\Member\Member;
use Model\Order\TeamOrderSearch;
use Model\Product\Ticket;
use Process\Order\OrderParams;
use Business\Order\PlatformSubmit;

class Order extends Controller
{
    //用户的登录信息
    private $_memberInfo;

    public function __construct()
    {
        $isLogin = $this->isLogin('auto', false);
        if (!$isLogin) {
            $this->apiReturn(204, [], '请先登录');
        }

        $this->_memberInfo = $this->getLoginInfo('auto', false, false);
    }

    /**
     * PC平台合并付款提交订单
     * date   2018-11-25
     * author hanwen<PERSON>
     *
     * return array
     */
    public function submitOrder()
    {
        // 处理滑动验证码
        //$this->handleCaptcha();

        //临时添加是否被限制
        $isLimit = $this->_isOrderLimit();
        if($isLimit) {
            pft_log('submitOrder', json_encode([$this->_memberInfo, $_SERVER, $_REQUEST], JSON_UNESCAPED_UNICODE));
            $this->apiReturn(205, [], '请求信息非法，暂时被限制下单');
        }

        //判断权限
        $isHaveOrderAuth = $this->_isHaveOrderAuth();
        if (!$isHaveOrderAuth) {
            $this->apiReturn(205, [], '该账号无购买权限');
        }

        //获取参数
        $paramsRes = OrderParams::getPlatformParams();
        if ($paramsRes[0] != 200) {
            $this->apiReturn(204, [], $paramsRes[1]);
        }

        //基础的下单参数
        $tmpOrderParam = $paramsRes[2];
        //下单参数处理
        $memberId = $this->_memberInfo['sid'];
        $opId     = $this->_memberInfo['memberID'];

        $baseChekRes = $this->_baseHandle($tmpOrderParam, $memberId, $opId);
        if ($baseChekRes[0] != 200) {
            $this->apiReturn(204, [], $baseChekRes[1]);
        }

        //处理过的下单参数
        $orderParam       = $baseChekRes[2];
        $platformOrderBiz = new PlatformSubmit();
        $orderRes         = $platformOrderBiz->combineSubmit($orderParam);

        if ($orderRes[0] != 200) {
            $errMsg     = $orderRes[1];
            $resultData = $orderRes[2];
            $this->apiReturn(402, $resultData, $errMsg);
        }

        $submitRes = $orderRes[2];
        $tradeId   = $submitRes['tradeId'];
        $orderList = $submitRes['list'];

        $resData = [
            'tradeId' => $tradeId,
            'list'    => [],
        ];
        foreach ($orderList as $item) {
            $resData['list'][] = [
                'ordernum' => $item['ordernum'],
            ];
            $this->_saveShippingAddr($item['ordernum'], $memberId);
        }

        $this->apiReturn(200, $resData, 'success');
    }

    /**
     * 校验是否启用验证码，用于处理前端时候显示滑动验证码组件
     */
    public function checkCaptchaEnable()
    {
        $isEnable = OrderCaptcha::checkOrderSubmitCaptchaEnable($this->_memberInfo['sid']);
        $result = ['is_enable' => $isEnable];
        $this->apiReturn(200, $result, 'success');
    }
    /**
     * 下单是否，校验验证码是否处理
     */
    protected function handleCaptcha()
    {
        $paymode = I('post.paymode', 0, 'intval');
        $captchaCode = I('post.captcha_code', '', 'string');
        $loginSid = $this->_memberInfo['sid'];

        $res = OrderCaptcha::handleOrderSubmitCaptcha($captchaCode, $loginSid, $paymode);
        if (!is_array($res)) {
            return;
        }
        list($code, $msg, $data) = $res;
        if (200 != $code) {
            // 验证码错误
            $this->apiReturn($code, $data, $msg);
        }
    }

    /**
     * 临时下单判断
     */
    private function _isOrderLimit() {
        $isLimit = false;

        $httpRefer = strval($_SERVER['HTTP_REFERER']);
        if(!$httpRefer || $httpRefer == '-') {
            $isLimit = true;
        }

        return $isLimit;
    }

    /**
     * 特产批量下单（excel）
     * <AUTHOR>
     * @date   2020-04-07
     */
    public function specialExcelOrder()
    {
        //判断权限
        $isHaveOrderAuth = $this->_isHaveOrderAuth();
        if (!$isHaveOrderAuth) {
            $this->apiReturn(204, [], '该账号无购买权限');
        }

        $excelString = I('excel_data', '', 'strval');
        $excelData   = json_decode($excelString, true);
        if (!is_array($excelData) || count($excelData) < 1) {
            $this->apiReturn(204, [], '参数错误');
        }
        //游玩日期
        $playdate = I('playdate', date('Y-m-d'));
        //支付方式
        $paymode = I('paymode', 0, 'intval');
        //上级id
        $aid = I('aid', 0, 'intval');
        //fid 计调下单是传递
        $fid = I('fid', 0, 'intval');

        $playdate = date('Y-m-d');

        if (!$playdate || !$aid) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($paymode == 1) {
            $this->apiReturn(204, [], '批量下单暂不支持在线支付');
        }

        $orderParams = [
            'playdate'  => date('Y-m-d', strtotime($playdate)),
            'paymode'   => $paymode,
            'ordermode' => 0,
            'aid'       => $aid,
            'fid'       => $fid,
        ];

        $biz    = new \Business\Order\BatchOrder();
        $result = $biz->specialBatchOrderFromExcel($this->_memberInfo['sid'], $this->_memberInfo['memberID'],
            $excelData, $orderParams);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 批量下单 订单提交
     * 特产批量下单  不走这边
     * <AUTHOR>
     * @date 2021/1/21
     *
     * @return array
     */
    public function batchOrderSubmit()
    {
        //判断权限
        $isHaveOrderAuth = $this->_isHaveOrderAuth();
        if (!$isHaveOrderAuth) {
            $this->apiReturn(204, [], '该账号无购买权限');
        }

        //支付方式:0=账户余额,1=支付宝,2=授信支付,3=产品自销
        $paymode   = I('post.paymode', 0, 'intval');
        $tid       = I('post.tid', 0, 'intval');
        $tnum      = I('post.tnum', 0, 'intval');
        $landId    = I('post.lid', 0, 'intval');
        $aid       = I('post.aid', 0, 'intval');
        $leavetime = I('post.leavetime', '', 'strval'); //离开时间 酒店独有参数
        $venusid   = I('post.venusid', 0, 'intval'); //场馆id 演出独有参数
        $roundid   = I('post.roundid', 0, 'intval'); //场次id 演出独有参数
        $zoneid    = I('post.zoneid', 0, 'intval'); //分区id 演出独有参数
        $seatIds   = I('post.seat_ids', 0, 'intval'); //座位id 演出独有参数
        $fid       = I('post.fsid', 0, 'intval'); //计调下单
        $excelData = I('post.excel_data', '', 'strval'); //批量下单数据
        $playdate  = I('playdate', '');//游玩日期
        $disId     = I('post.dis_id', '', 'strval');//分销专员推广id
        $sid       = $this->_memberInfo['sid'];
        $memberId  = $this->_memberInfo['memberID'];

        $excelData = json_decode($excelData, true);
        $excelData = is_array($excelData) ? $excelData : [];
        if (empty($excelData)) {
            $this->apiReturn(204, [], '批量下单数据不能为空');
        }

        //参数的合法性检测
        if (!chk_date($playdate)) {
            return [400, '时间格式不正确【格式：yyyy-mm-dd】'];
        }

        $channel = 5;
        if ($fid) {
            $channel = 12;
            $sid     = $fid;
        }

        if (!$tid || !$aid || !$landId) {
            $this->apiReturn(204, [], '票参数错误');
        }

        if (!$tnum || $tnum <= 0) {
            $this->apiReturn(204, [], '下单件数不能小于等于0');
        }

        //分销专员下单的下单人记为运营商
        $disId && $fid = $this->updateOrderMember($disId);

        if ($paymode == 1) {
            $this->apiReturn(204, [], '批量下单暂不支持在线支付');
        }

        $orderParams = [
            'playdate'  => date('Y-m-d', strtotime($playdate)),
            'paymode'   => $paymode,
            'ordermode' => $fid ? OrderChannel::OPERATOR_CHANNEL : 0,
            'aid'       => $aid,
            'fid'       => $fid,
            'lid'       => $landId,
            'tid'       => $tid,
            'tnum'      => $tnum,
            'channel'   => $channel,
            'leavetime' => $leavetime,
            'venusid'   => $venusid,
            'roundid'   => $roundid,
            'zoneid'    => $zoneid,
            'seat_ids'  => $seatIds,
        ];

        $biz    = new \Business\Order\BatchOrder();
        $result = $biz->batchOrderSubmitForExcel($sid, $aid, $excelData, $orderParams, $memberId, $landId, $tid, $tnum, $channel);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     *  计调下单调用
     * <AUTHOR>
     * @date 2020/2/20
     *
     * @param $orderNum 订单号
     * @param $memberId 下单用户
     * @param $memberId 计调用户id
     *
     * @return array
     */
    public function saveForNewFormSubmit($orderNum, $memberId, $replaceMemberId)
    {
        $this->_saveShippingAddr($orderNum, $memberId, $replaceMemberId);
    }

    /**
     *  保存收获地址
     * <AUTHOR>
     * @date 2020/2/18
     *
     * @param  string  $orderNum  订单号
     * @param  int  $memberId  用户id
     * @param  int  $replaceMemberId  计调用户id
     *
     * @return array
     */
    private function _saveShippingAddr($orderNum = '', $memberId = 0, $replaceMemberId = 0)
    {
        if ($replaceMemberId) {
            $memberId = $replaceMemberId;
        }
        $this->_saveLinkMan($memberId);
        $saveLinkMan = I('post.linkman', 0, 'intval'); // 1保存 0不保存

        if ($saveLinkMan == 1) {
            $name          = trim(I('post.ordername'));
            $mobile        = trim(I('post.ordertel'));
            $delivery      = I('post.delivery', '', 'strval');
            $expressType   = $delivery['express_type'];
            $expressPayWay = $delivery['express_pay_way'] ?? 0;
            $province      = $city = $town = '';
            if (!$expressType) {
                $province = $delivery['province'];
                if (empty($province) && !$expressType) {
                    return [400, '地址省份不能为空'];
                }
                $city = $delivery['city'];
                if (empty($city) && !$expressType) {
                    return [400, '地址市区不能为空'];
                }
                $town        = $delivery['town'];
                $detail_addr = $delivery['detail_addr'];
            }
            //if (empty($detail_addr) && !$expressType) {
            //    return [400, '详细地址不能为空'];
            //}

            if (!$name || !$mobile) {
                return false;
            }

            $paramsObj = new \stdClass;
            //参数对象
            $paramsObj->name        = $name;
            $paramsObj->mobile      = $mobile;
            $paramsObj->province    = $province;
            $paramsObj->city        = $city;
            $paramsObj->town        = $town;
            $paramsObj->postcode    = '';
            $paramsObj->detail_addr = $detail_addr;
            $paramsObj->add_type    = $expressType;

            $specialBiz = new Specialty();

            $series = $specialBiz->createShippingAddr($memberId, $paramsObj);
            if ($series['code'] == 200) {
                $series = $series['data'];
                $series = $series . '|' . $expressPayWay;
                $api    = new \Business\JavaApi\Order\OrderDetailUpdate();
                $api->orderDetailInfoUpdate($orderNum, ['series' => $series]);
            }
        }

        return true;
    }

    /**
     * 保存联系人
     * <AUTHOR>
     * @time   2017-08-17
     */
    private function _saveLinkMan($memberId)
    {
        $saveLinkMan = I('post.linkman', 0, 'intval'); // 1保存 0不保存

        if ($saveLinkMan == 1) {
            $name   = trim(I('post.ordername'));
            $mobile = trim(I('post.contacttel'));
            if (!$mobile) {
                $mobile = trim(I('post.ordertel'));
            }
            $delivery = I('post.delivery');
            $province = $city = $town = $detailAddr = '';
            if (!empty($delivery)) {
                $province   = $delivery['province'];
                $city       = $delivery['city'];
                $town       = $delivery['town'];
                $detailAddr = $delivery['detail_addr'];
            }

            if (!$name || !$mobile) {
                return false;
            }

            $linkman = [
                'name'        => $name,
                'tel'         => $mobile,
                'idcard'      => '',
                'province'    => $province,
                'city'        => $city,
                'town'        => $town,
                'detail_addr' => $detailAddr,
            ];

            // 得到现有联系人
            $memberBiz  = new \Business\Member\Member();
            $cinfos     = $memberBiz->getMemberExtInfoGetFieldToJava($memberId, 'cinfos');
            $linkmanArr = $this->parseLinkman($cinfos);

            // 联系人是否已存在
            if ($linkmanArr && in_array($linkman, $linkmanArr)) {
                return true;
            }

            // 限制常用联系人数量
            $maxLinkNum = 12;
            if (count($linkmanArr) >= $maxLinkNum) {
                return false;
            }

            // 增加
            if (!$cinfos) {
                $data['cinfos'] = implode(':', $linkman);
            } else {
                $data['cinfos'] = $cinfos . '|' . implode(':', $linkman);
            }

            $memberBiz = new \Business\Member\Member();
            $res       = $memberBiz->updateMemberExtInfo($memberId, $data);
            if (!$res) {
                return false;
            }

        }

        return true;
    }

    /**
     *  解析常用联系人字段
     * <AUTHOR>
     * @date 2020/2/24
     *
     * @param  string  $linkmanField  数据库中联系人字段
     *
     * @return array
     */
    protected function parseLinkman($linkmanField = '')
    {
        if (!$linkmanField || !is_string($linkmanField)) {
            return [];
        }

        $linkmanArr = explode('|', $linkmanField);
        foreach ($linkmanArr as $key => $linkman) {
            list($name, $tel, $idcard, $province, $city, $town, $detailAddr) = explode(':', $linkman);
            $info[$key]['name']        = $name;
            $info[$key]['tel']         = $tel;
            $info[$key]['idcard']      = $idcard;
            $info[$key]['province']    = $province;
            $info[$key]['city']        = $city;
            $info[$key]['town']        = $town;
            $info[$key]['detail_addr'] = $detailAddr;
        }

        return $info ?: [];
    }

    /**
     * PC平台合并付款支付成功列表
     * date   2018-11-29
     * author hanwenlin
     *
     * return array
     */
    public function paySuccess()
    {
        $tradeId = I('post.ordernum');
        $disId   = I('post.dis_id', '', 'strval');

        $mergeOrder = new MergeOrder();
        if ($mergeOrder->isCombineOrder($tradeId)) {
            $orderNums = $mergeOrder->getMainOrderNumByTradeId($tradeId);
            $isCombine = 1;
        } else {
            $orderNums[] = $tradeId;
        }

        if (!$orderNums) {
            $this->apiReturn(204, [], '参数错误');
        }

        //$Ticket     = new Ticket('slave');
        $landModel  = new \Model\Product\Land('slave');
        $Order      = new \Model\Order\OrderTools();
        $orderInfos = $Order->getOrderInfo($orderNums,
            'status,ordernum,paymode,pay_status,tid,ordertime,member,ordermode,tnum,lid,aid,ordername,ordertel,code,pid,totalmoney,begintime,endtime,playtime');
        if (empty($orderInfos)) {
            $this->apiReturn(204, [], '订单信息不存在');
        }

        $needPay = false;
        $teamOrder = false;
        foreach ($orderInfos as $item) {
            if ($item['pay_status'] == 2) {
                $needPay = true;
            }
            if (in_array($item['ordermode'],[24,44])){  //团单的情况
                $teamOrder = true;
            }
        }

        $payMode   = $orderInfos[0]['paymode'];
        $payStatus = $needPay ? 2 : 1;
        $onlinePay = 0;
        if ($payMode == 1 && $payStatus == 0) {
            $onlinePay = 1;
        }

        if ($disId) {
            $topUid = $this->updateOrderMember($disId);
        }

        if ($this->_memberInfo['sid'] != $orderInfos[0]['member'] && $this->_memberInfo['sid'] != $orderInfos[0]['aid']) {
            $this->apiReturn(204, [], '非购买者不允许查看订单');
        }

        if ($isCombine == 1) {
            $totalMoney = $mergeOrder->getTradeIdTotleMoney($tradeId);
        } else {
            $orderQuery = new \Model\Order\OrderQuery('localhost');
            $totalMoney = $orderQuery->get_order_total_fee((string)$tradeId);
            // $totalMoney = $orderInfos[0]['totalmoney'];
        }

        $orderData = [];
        foreach ($orderInfos as $item) {
            $orderData[$item['ordernum']] = $item;
        }

        $tids      = array_column($orderInfos, 'tid');
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($tids);
        if (!$ticketArr) {
            $this->apiReturn(204, [], '门票信息获取失败');
        }
        $tickets = [];
        foreach ($ticketArr as $ticketInfos) {
            $tickets[$ticketInfos['ticket']['id']] = [
                'id'                => $ticketInfos['ticket']['id'],
                'title'             => $ticketInfos['ticket']['title'],
                'cancel_auto_onMin' => $ticketInfos['ticket']['cancel_auto_onMin'],
                'apply_did'         => $ticketInfos['ticket']['apply_did'],
                'pre_sale'          => $ticketInfos['ticket']['pre_sale'],
            ];
        }

        $cancelMin = 120;
        if (is_array($tickets)) {
            $cancelMin = array_column($tickets, 'cancel_auto_onMin');
            $cancelMin = min($cancelMin);
        }

        $ticketData = [];
        foreach ($tickets as $v) {
            $ticketData[$v['id']] = $v;
        }

        $land = $landModel->getLandInfo($orderInfos[0]['lid'], false, 'p_type,title');
        $teamInfo = [];
        if ($teamOrder){
            $teamOrderSearchMdl = new TeamOrderSearch();
            $teamOrderInfo = $teamOrderSearchMdl->getMainOrderInfoBySonOrder($orderInfos[0]['ordernum']);
            $teamInfo = [
                'team_order' => $teamOrderInfo['main_ordernum'],
            ];
        }
        $specialInfo = [];
        if ($land['p_type'] == 'H' || $land['p_type'] == 'J') {
            //演出类获取场次
            $series = $Order->getOrderDetailInfo($orderNums, 'series,orderid,product_ext');
            $series = array_key($series, 'orderid');
            if ($land['p_type'] == 'H') {
                foreach ($series as $v) {
                    if (!empty($v)) {
                        $seriesData[$v['orderid']] = unserialize($v['series']);
                    }
                }
            }
            if ($land['p_type'] == 'J') {
                foreach ($orderNums as $tmpOrdernum) {
                    //特产类型，需要展示收货/取货信息
                    $productExt = json_decode($series[$tmpOrdernum]['product_ext'], true);
                    //快递还是自提,0快递自提
                    $deliveryType = $productExt['deliveryType'] ?? 1;
                    $expInfo      = $selfPickup = [];
                    if ($deliveryType == 0) {
                        //获取收货人信息
//                        $orderUserModel = new \Model\Order\OrderUser();
//                        $orderUserInfo  = $orderUserModel->getOneOrderUserByOrdernum($orderNums[0],
//                            'province_code,city_code,town_code,address');
                        $queryParams = [[$orderNums[0]]];
                        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderUserInfo','getOrderUserInfoByOrderNum', $queryParams);
                        $orderUserInfo = [];
                        if ($queryRes['code'] == 200) {
                            $orderUserInfo = $queryRes['data'][0];
                        }
                        $areaCodeArr    = [
                            $orderUserInfo['province_code'],
                            $orderUserInfo['city_code'],
                            $orderUserInfo['town_code'],
                        ];
                        $areaCodeArr    = array_filter($areaCodeArr);
                        if ($areaCodeArr) {
                            $areaModel = new \Model\Product\Area();
                            $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
                        } else {
                            $codeMap = [];
                        }

                        //快递物流信息
                        $expInfo = [
                            'carriage'      => $productExt['carriage'] ?? 0,
                            'province_code' => $codeMap[$orderUserInfo['province_code']] ?? '',
                            'city_code'     => $codeMap[$orderUserInfo['city_code']] ?? '',
                            'town_code'     => $codeMap[$orderUserInfo['town_code']] ?? '',
                            'address'       => $orderUserInfo['address'] ?? '',
                        ];
                    } else {
                        $pickPointApi = new \Business\JavaApi\Express\PickPoint();
                        $pickData     = $pickPointApi->queryExpressPickPointByTid($tids[0]);
                        if ($pickData) {
                            $areaCodeArr = explode('|', $pickData['areaCode']);
                            $areaCodeArr = array_filter($areaCodeArr);
                            if ($areaCodeArr) {
                                $areaModel = new \Model\Product\Area();
                                $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
                            } else {
                                $codeMap = [];
                            }
                            //自提点信息
                            $selfPickup = [
                                'area'    => implode('', array_values($codeMap)),
                                'address' => $pickData['address'],
                            ];
                        } else {
                            $selfPickup = [];
                        }
                    }

                    $specialInfo[$tmpOrdernum] = [
                        'delivery_way' => $deliveryType,
                        'exp_info'     => $expInfo,
                        'pick_info'    => $selfPickup,
                    ];
                }
            }
        }

        if ($land['p_type'] == 'C') {
            $extInfo = $Order->getOrderDetailInfo($orderInfos[0]['ordernum'], 'ext_content');
            $extArr  = json_decode($extInfo['ext_content'], true);
            if (isset($extArr['jointTicketDate'])) {
                $orderData[$orderInfos[0]['ordernum']]['begintime'] = $extArr['jointTicketDate']['startDate'];
                $orderData[$orderInfos[0]['ordernum']]['endtime']   = $extArr['jointTicketDate']['endDate'];
            }
        }

        $externalCode = [];
        if ($land['p_type'] == 'A') {
            $extInfo    = $Order->getOrderDetailInfo($orderInfos[0]['ordernum'], 'product_ext');
            $productExt = json_decode($extInfo['product_ext'], true);
            if (isset($productExt['externalSendCode']) && $productExt['externalSendCode'] == 1) {  //代表有发码
                $externalBiz = new \Business\ExternalCode\CodeManage();
                $externalRes = $externalBiz->getExternalCodeInfoByOrdernum($ticketData[$orderData[$orderInfos[0]['ordernum']]['tid']]['apply_did'],
                    $orderInfos[0]['ordernum']);
                if (isset($externalRes['code']) && $externalRes['code'] == 200) {
                    //查询是否设置游玩日期
                    $javaApi   = new \Business\CommodityCenter\Ticket();
                    $ticketArr = $javaApi->batchQueryTicketByTicketIds([$orderInfos[0]['tid']], 'pre_sale');
                    $preSale   = $ticketArr[0]['pre_sale']; //是否选择游玩日期 1是2否

                    //有效期开始和结束时间的处理
                    $expireType = $externalRes['data']['expire_type']; //发送给游客的有效期：1导码设置的有效期；2下单选择的游玩日期；3固定有效期
                    $orderTime  = $orderInfos[0]['ordertime']; //下单时间
                    $playTime   = $orderInfos[0]['playtime']; //游玩时间
                    $expireTime = $externalRes['data']['expire_time'];  //外部码过期时间， 0为长期有效
                    $startDt    = $externalRes['data']['start_dt']; //固定日期的开始时间
                    $endDt      = $externalRes['data']['end_dt']; //固定日期的结束时间
                    //有效期处理
                    $handleRes = $externalBiz->handleExternalCodeOrderTime($expireType, $preSale, $orderTime, $playTime, $expireTime, $startDt, $endDt);
                    if (isset($handleRes['code']) && $handleRes['code'] == 200) {
                        $externalCode[$orderInfos[0]['ordernum']]['begintime']  = $handleRes['data']['begin_time'];
                        $externalCode[$orderInfos[0]['ordernum']]['endtime']    = $handleRes['data']['end_time'] != 0 ? $handleRes['data']['end_time'] : '长期有效';
                    }
                }
            }
        }

        foreach ($orderNums as $key => $ordernum) {
            $data['ordernum']   = $ordernum;
            $data['status']     = intval($orderData[$ordernum]['status']);
            $data['lid']        = $orderData[$ordernum]['lid'];
            $data['tid']        = $ticketData[$orderData[$ordernum]['tid']]['id'];
            $data['ticket']     = $ticketData[$orderData[$ordernum]['tid']]['title'];
            $data['tnum']       = $orderData[$ordernum]['tnum'];
            $data['totalmoney'] = $orderData[$ordernum]['totalmoney'];
            $data['ordername']  = $orderData[$ordernum]['ordername'];
            $data['ordertel']   = $orderData[$ordernum]['ordertel'];
            $data['mobile_area'] = $orderData[$ordernum]['mobile_area'];
            $data['mobile_region'] = $orderData[$ordernum]['mobile_region'];
            $data['begintime']  = !empty($externalCode) ? $externalCode[$ordernum]['begintime'] : $orderData[$ordernum]['begintime'];
            $data['endtime']    = !empty($externalCode) ? $externalCode[$ordernum]['endtime'] : $orderData[$ordernum]['endtime'];
            $data['special']    = $specialInfo[$ordernum] ?? [];
            if ($land['p_type'] == 'H') {
                $data['showtime'] = $seriesData[$ordernum][4] ?? '';
            }
            $returns['list'][] = $data;
        }
        //用来确认是不是第三方产品，调用Java接口, sourcet= [2,3]就是第三方
        $ticketIds = array_column($orderInfos,'tid'); //票ids
        $ticketIds = implode(',', $ticketIds);
        $isThird = (new \Business\JavaApi\Ticket\ThirdAttr())->isThirdTickets($ticketIds);

        $returns['land']       = $land['title'];
        $returns['p_type']     = $land['p_type'];
        $returns['totalmoney'] = $totalMoney;
        $returns['cancelMin']  = $cancelMin;
        $returns['onlinePay']  = $onlinePay;
        $returns['lid']        = $orderInfos[0]['lid'];
        $returns['tid']        = $orderInfos[0]['tid'];
        $returns['payStatus']  = $payStatus;
        $returns['is_third']  = $isThird;
        if ($teamOrder){
            $returns['team_info'] = $teamInfo;
        }

        // $returns['special']    = $specialInfo;
        $this->apiReturn(200, $returns);
    }

    /**
     *  是否有下单的权限
     * <AUTHOR>
     * @date 2019-03-30
     *
     * @return
     */
    private function _isHaveOrderAuth()
    {
        if ($this->_memberInfo['dtype'] == 6) {
            //员工账号
            $memberBiz = new \Business\Member\Member();
            $sonInfo   = $memberBiz->getInfo($this->_memberInfo['memberID']);
            if (!$sonInfo || $sonInfo['status'] != 0) {
                return false;
            }
            if (!in_array('pro', explode(',', $sonInfo['member_auth']))) {
                // 当前员工账号 没有产品预订权限
                return false;
            }

            return true;
        } else {
            //主账号
            return true;
        }
    }

    /**
     * 参数的基础判断和处理
     * <AUTHOR>
     * @date 2019-03-30
     *
     * @param  array  $orderParam
     * @param  int  $memberId
     * @param  int  $opId
     *
     * @return []
     */
    private function _baseHandle($orderParam, $memberId, $opId)
    {
        //基础判断
        if (!isset($orderParam['combine_pids']) || !$orderParam['combine_pids']) {
            return [204, '产品数据异常'];
        }

        //TODO:其他的一些判断

        //下单用户和下单渠道等参数
        $orderParam['member_id'] = $memberId;
        $orderParam['op_id']     = $opId;
        $orderParam['is_sale']   = false;
        $orderParam['channel']   = 0;

        //分销专员下单需要修改下单参数
        if (!empty($orderParam['dis_id'])) {
            $result = (new \Business\MultiDist\Member())->getMultiDistCache($this->_memberInfo['sid']);
            // $multiDistBiz = new \Business\MultiDist\Member($this->_memberInfo['memberID'],
            //     $this->_memberInfo['multi_dist_uuid']);
            $multiDistBiz = new \Business\MultiDist\Member($this->_memberInfo['memberID'],
                $result['multi_dist']['uuid']);
            $topUid       = $multiDistBiz->getTopUid($orderParam['dis_id']);
            if ($topUid) {
                $orderParam['is_sale']        = true;
                $orderParam['upper_supplier'] = $orderParam['aid'];

                if ($orderParam['combine_pids']) {
                    foreach ($orderParam['combine_pids'] as &$tmpParam) {
                        //分销专员作为散客下单，upper_supplier记为运营商的分销上级uid
                        isset($tmpParam['upper_supplier']) && $tmpParam['upper_supplier'] = $orderParam['aid'];
                        //分销专员作为散客下单，aid记为运营商的uid
                        isset($tmpParam['aid']) && $tmpParam['aid'] = $topUid;
                    }
                }
            }
        }
        
        //计调下单参数兼容
        if (!empty($orderParam['tour_operator'])) {
            $orderParam['member_id'] = $orderParam['tour_operator'];
            $orderParam['channel']   = OrderChannel::OPERATOR_CHANNEL;
        }

        return [200, '', $orderParam];
    }

    /**
     * 从缓存获取票快照数据
     * @author: xwh
     * @date: 2021/11/03
     *
     */
    public function getTicketSnapShotByCache()
    {
        $tid = I('post.tid', 0, 'intval');
        $version = I('post.version', -1, 'intval');
        if (!$tid || $version < 0) {
            $this->apiReturn(203, [], '参数错误');
        }

        $result = (new \Business\Product\Ticket())->getTicketSnapShotByCache($tid, $version);

        if (!$result) {
            $this->apiReturn(400, [], '获取失败');
        }
        $this->apiReturn(200, $result, '成功');
    }

    /**
     * 下单优化中对检测规则做了优化
     */
    public function submitPreCheckV2()
    {
        $this->submitPreCheck(['version' => 'v2']);
    }

    /**
     * 平台下单前参数检验
     * <AUTHOR>
     * @date 2022/1/18
     *
     */
    public function submitPreCheck($options = [])
    {
        //判断权限
        $isHaveOrderAuth = $this->_isHaveOrderAuth();
        if (!$isHaveOrderAuth) {
            $this->apiReturn(205, [], '该账号无购买权限');
        }

        //获取参数
        $paramsRes = OrderParams::getPlatformParams($options);
        if ($paramsRes[0] != 200) {
            $this->apiReturn(204, [], $paramsRes[1]);
        }

        //基础的下单参数
        $tmpOrderParam = $paramsRes[2];

        //下单参数处理
        $memberId = $this->_memberInfo['sid'];
        $opId     = $this->_memberInfo['memberID'];

        $baseChekRes = $this->_baseHandle($tmpOrderParam, $memberId, $opId);
        if ($baseChekRes[0] != 200) {
            $this->apiReturn(204, [], $baseChekRes[1]);
        }

        //处理过的下单参数
        $orderParam       = $baseChekRes[2];
        $platformOrderBiz = new \Business\Order\OrderBook();
        if ($options['version'] == 'v2') {
            $orderRes         = $platformOrderBiz->submitOrderPreCheckV2($orderParam);
        } else {
            $orderRes         = $platformOrderBiz->submitOrderPreCheck($orderParam);
        }

        if ($orderRes['code'] != 200) {
            $errMsg     = $orderRes['msg'] ?? '验证失败';
            $this->apiReturn(402, [], $errMsg);
        }

        $data = $orderRes['data'] ?? [];

        $this->apiReturn(200, $data);
    }

    public function identity(){
        $queryBiz = new \Business\Order\Query();
        $res = $queryBiz->getIdentity();
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 批量下单 订单提交
     * 特产批量下单  不走这边
     *
     * @return array
     */
    public function batchOrderSubmitV2()
    {
        //判断权限
        $isHaveOrderAuth = $this->_isHaveOrderAuth();
        if (!$isHaveOrderAuth) {
            $this->apiReturn(204, [], '该账号无购买权限');
        }

        //支付方式:0=账户余额,1=支付宝,2=授信支付,3=产品自销
        $paymode   = I('post.paymode', 0, 'intval');
        $tid       = I('post.tid', 0, 'intval');
        $orderNum  = I('post.order_num', 0, 'intval');  //下单订单数
        $tnum      = I('post.tnum', 0, 'intval');
        $landId    = I('post.lid', 0, 'intval');
        $aid       = I('post.aid', 0, 'intval');
        $leavetime = I('post.leavetime', '', 'strval'); //离开时间 酒店独有参数
        $venusid   = I('post.venusid', 0, 'intval'); //场馆id 演出独有参数
        $roundid   = I('post.roundid', 0, 'intval'); //场次id 演出独有参数
        $zoneid    = I('post.zoneid', 0, 'intval'); //分区id 演出独有参数
        $seatIds   = I('post.seat_ids', 0, 'intval'); //座位id 演出独有参数
        $fid       = I('post.fsid', 0, 'intval'); //计调下单
        $excelData = I('post.excel_data', '', 'strval'); //批量下单数据
        $playdate  = I('playdate', '', 'strval');//游玩日期
        $disId     = I('post.dis_id', '', 'strval');//分销专员推广id
        $sid       = $this->_memberInfo['sid'];
        $memberId  = $this->_memberInfo['memberID'];

        $excelData = json_decode($excelData, true);
        $excelData = is_array($excelData) ? $excelData : [];
        if (empty($excelData)) {
            $this->apiReturn(204, [], '批量下单数据不能为空');
        }

        //参数的合法性检测
        if (!chk_date($playdate)) {
            return [400, '时间格式不正确【格式：yyyy-mm-dd】'];
        }

        $channel = 5;
        if ($fid) {
            $channel = 12;
            $sid     = $fid;
        }

        if (!$tid || !$aid || !$landId) {
            $this->apiReturn(204, [], '票参数错误');
        }

        if (!$orderNum || $orderNum <= 0) {
            $this->apiReturn(204, [], '下单件数不能小于等于0');
        }
        if (!$tnum || $tnum <= 0) {
            $this->apiReturn(204, [], '每单票数不能小于等于0');
        }

        //分销专员下单的下单人记为运营商
        $disId && $fid = $this->updateOrderMember($disId);

        if ($paymode == 1) {
            $this->apiReturn(204, [], '批量下单暂不支持在线支付');
        }

        $orderParams = [
            'playdate'  => date('Y-m-d', strtotime($playdate)),
            'paymode'   => $paymode,
            'ordermode' => $fid ? OrderChannel::OPERATOR_CHANNEL : 0,
            'aid'       => $aid,
            'fid'       => $fid,
            'lid'       => $landId,
            'tid'       => $tid,
            'order_num' => $orderNum,
            'tnum'      => $tnum,
            'channel'   => $channel,
            'leavetime' => $leavetime,
            'venusid'   => $venusid,
            'roundid'   => $roundid,
            'zoneid'    => $zoneid,
            'seat_ids'  => $seatIds,
        ];

        $biz    = new \Business\Order\BatchOrder();
        $result = $biz->batchOrderSubmitForExcelV2($sid, $aid, $excelData, $orderParams, $memberId, $landId, $tid, $orderNum, $tnum, $channel);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }
}
