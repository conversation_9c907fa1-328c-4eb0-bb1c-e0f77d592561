<?php

namespace Controller\Order;
use Business\MemberLogin\MemberLoginHelper;
use Library\Controller;
use Business\Order\OrderDeliver as OrderDeliverBiz;

/**
 * 物流发货相关接口
 * <AUTHOR>
 */
class OrderDeliver extends Controller {

    private $_sid = 0;
    private $_operId;
    private $_sdtype;
    private $_saccount;
    private $_orderDeliverBiz = null;

    public function __construct()
    {
        parent::__construct();
        $this->_sid = $this->isLogin('ajax');
        $loginInfo  = $this->getLoginInfo('ajax', false, false);

        $this->_operId          = $loginInfo['memberID'];
        $this->_sdtype          = $loginInfo['sdtype'];
        $this->_saccount        = $loginInfo['saccount'];
        $this->_orderDeliverBiz = new OrderDeliverBiz();
    }


    /**
     * 特产订单发货接口
     * <AUTHOR>
     * @date   2020-04-13
     */
    public function deliver()
    {
        $ordernum    = I('ordernum', '', 'strval');
        $expCompany  = I('exp_company', '');
        $expNo       = I('exp_no', '');
        if (!$ordernum) {
            $this->apiReturn(204, '订单号缺失');
        }

        $sid      = $this->_sid;
        $sdtype   = $this->_sdtype;
        $saccount = $this->_saccount;
        $memberId = $this->_operId;
        $moreData = [];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid                 = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $memberId            = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $moreData['subSid']  = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $moreData['subOpId'] = MemberLoginHelper::getLoginBusinessMember()->getMemberId();
        }

        $result = $this->_orderDeliverBiz->deliver($sid, $memberId, $ordernum, $expCompany, $expNo, 16, $saccount, $sdtype, $moreData);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 特产订单修改物流信息接口
     * <AUTHOR>
     * @date   2020-04-13
     */
    public function modifyLogistics()
    {
        $ordernum    = I('ordernum', '', 'strval');
        $expCompany  = I('exp_company', '');
        $expNo       = I('exp_no', '');
        if (!$ordernum) {
            $this->apiReturn(204, '订单号缺失');
        }

        $sid = $this->_sid;
        $sdtype = $this->_sdtype;
        $saccount = $this->_saccount;
        $memberId = $this->_operId;
        $moreData = [];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid                 = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $memberId            = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $moreData['subSid']  = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $moreData['subOpId'] = MemberLoginHelper::getLoginBusinessMember()->getMemberId();
        }

        $result = $this->_orderDeliverBiz->modifyLogistics($sid, $memberId, $ordernum, $expCompany, $expNo, 16, $saccount, $sdtype, $moreData);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取批量发货记录
     * <AUTHOR>
     * @date   2020-04-08
     */
    public function batchDeliverList()
    {
        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }
        $oper   = I('oper', 0, 'intval');
        $begin  = I('begin', '');
        $end    = I('end', '');
        $status = I('status', -1, 'intval');
        $page   = I('page', 1, 'intval');
        $size   = I('size', 10, 'intval');

        $result = $this->_orderDeliverBiz->batchDeliverList($this->_sid, $begin, $end, $status, $oper, $page, $size);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 导出批量发货记录
     * <AUTHOR>
     * @date   2020-04-08
     */
    public function exportDeliverList()
    {
        $deliverNo  = I('deliver_no', '');

        $result = $this->_orderDeliverBiz->exportDeliverList($this->_sid, $deliverNo);
        if (isset($result['code']) && $result['code'] == 200) {
            $this->excelReturn('发货记录', '发货记录', $result['data']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 批量发货
     * <AUTHOR>
     * @date   2020-04-13
     */
    public function deliverFromExcel()
    {
        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }
        if (!isset($_FILES['deliver_excel'])) {
            $this->apiReturn(204, '请上传excel');
        }
         //取得扩展名
        $filetype = strtolower(strrchr($_FILES['deliver_excel']['name'], "."));
        if($filetype !='.xlsx') {
            $this->apiReturn(204, '文件格式不正确,请下载模板文件进行填写');
        }
        $excelProcess = new \Process\Order\DeliverFromExcel();
        $res = $excelProcess->parse($_FILES['deliver_excel']['tmp_name']);
        if (is_array($res)) {
            $sdtype = $this->_sdtype;
            $saccount = $this->_saccount;
            $result = $this->_orderDeliverBiz->deliverFromExcel($this->_sid, $this->_operId, $res, 16, $saccount, $sdtype);
            if (isset($result['code'])) {
                $this->apiReturn($result['code'], $result['data'], $result['msg']);
            } else {
                $this->apiReturn(500, [], '接口异常');
            }
        } else {
            $this->apiReturn(204, [], $res);
        }
    }


    /**
     * 批量修改物流信息
     * <AUTHOR>
     * @date   2020-04-13
     */
    public function modifyLogisticsFromExcel()
    {
        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }
        if (!isset($_FILES['deliver_excel'])) {
            $this->apiReturn(204, '请上传excel');
        }
         //取得扩展名
        $filetype = strtolower(strrchr($_FILES['deliver_excel']['name'], "."));
        if($filetype !='.xlsx') {
            $this->apiReturn(204, '文件格式不正确,请下载模板文件进行填写');
        }
        $excelProcess = new \Process\Order\DeliverFromExcel();
        $res = $excelProcess->parse($_FILES['deliver_excel']['tmp_name']);
        if (is_array($res)) {
            $sdtype = $this->_sdtype;
            $saccount = $this->_saccount;
            $result = $this->_orderDeliverBiz->modifyLogisticsFromExcel($this->_sid, $this->_operId, $res, 16, $saccount, $sdtype);
            if (isset($result['code'])) {
                $this->apiReturn($result['code'], $result['data'], $result['msg']);
            } else {
                $this->apiReturn(500, [], '接口异常');
            }
        } else {
            $this->apiReturn(204, [], $res);
        }
    }

}