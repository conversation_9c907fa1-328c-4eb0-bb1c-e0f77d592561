<?php

namespace Controller\Order;
use Business\Order\BatchOperate as BatchOperateBiz;
use Library\Controller;
use Business\Order\OrderDelay as OrderDelayBiz;
use Model\Order\OrderLog;

/**
 * 订单延期相关
 * <AUTHOR>
 */
class OrderDelay extends Controller {

    private $_sid = 0;
    private $_operId;
    private $_limit = 1000;

    public function __construct()
    {
        parent::__construct();
        $loginInfo = $this->getLoginInfo();
        $this->_sid = $loginInfo['sid'];
        $this->_operId = $loginInfo['memberID'];
        $this->_orderDelayBiz = new OrderDelayBiz();
    }


    /**
     * 单笔订单延期
     *
     * <AUTHOR>
     * @date 2021/3/29
     *
     * @param  int $sid 供应商
     * @param  int $opid  操作人
     * @param  string $ordernum 订单号
     * @param  string $delay_time 延期时间
     *
     * @return array
     */
    public function oneOrderDelay()
    {
        $ordernum  = I('ordernum', '', 'strval,trim');
        $startTime = I('start_time', '', 'strval,trim');
        $endTime   = I('delay_time', '', 'strval,trim');
        $isChange  = I('is_change', 0, 'int');
        $productType = I('p_type', '');
        if (!$ordernum) {
            $this->apiReturn(204, '订单号缺失');
        }
        if (!$startTime && !$endTime) {
            $this->apiReturn(204, '订单延期至时间参数缺失');
        }
        if($productType == 'Q'){
//        if(1){
            $tvInvoiceApi = new \Business\JsonRpcApi\TravelVoucherService\SpecialOrders();
            $result = $tvInvoiceApi->delayOrderForDelay($ordernum, $this->_operId, $startTime, $endTime);
            if ($result['code'] != 200) {
                pft_log('/order/delay/error',
                    '延期' . json_encode(['ordernum' => $ordernum, 'sid' => $this->_sid, 'tmpOrderInfo'=>$result]));
                $this->apiReturn($result['code'], [], $result['msg']);
            }
            $this->apiReturn(200, $result['data'], '处理完成');
        }
        else{
            $result = $this->_orderDelayBiz->oneOrderDelay($this->_sid, $this->_operId, $ordernum, $startTime, $endTime, $isChange);
        }
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 批量订单延期
     *
     * <AUTHOR>
     * @date 2021/3/29
     *
     * @return array
     */
    public function batchOrOneOrderDelay()
    {
        $isChange  = I('is_change', 0, 'int');
        $file = $_FILES['file'];
        if (empty($file)) {
            $this->apiReturn(203, '请上传延期文件');
        }

        $fileInfo = $this->_orderDelayBiz->fileCheck($file);
        if ($fileInfo['code'] != 200) {
            $this->apiReturn($fileInfo['code'], $fileInfo['msg']);
        }
        $orders = $fileInfo['data'];

        //检测下处理文件的数量
        if (count($orders) > $this->_limit) {
            $this->apiReturn(203, "每次导入最多支持{$this->_limit}条记录");
        }

        //$result = $this->_orderDelayBiz->batchOrOneOrderDelay($this->_sid, $this->_operId, $file);
        $type   = BatchOperateBiz::ACTION_TYPE_VAIL_DATE;
        $result = (new BatchOperateBiz())->createOperate($this->_sid, $this->_operId, $orders, $type,['is_change'=> $isChange]);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 导出批量订单延期记录
     *
     * <AUTHOR>
     * @date 2021/3/29
     */
    public function excelOrderDelay()
    {
        $id  = I('task_id', '', 'strval,trim');

        if (!$id) {
            $this->apiReturn(204, '参数缺失');
        }

        $result = (new \Model\Order\OrderDelay())->getOrderDelayRecording($id);
        $status = [
            '失败','成功'
        ];

        $data = [['订单号', '延期开始时间（年月日）', '延期结束时间（年月日）', '状态', '备注']];
        if ($result) {
            foreach ($result as $item){
                $startTime = !empty($item['delay_start_time']) ? date("Y-m-d",$item['delay_start_time']) : '';
                $endTime   = !empty($item['delay_time']) ? date("Y-m-d",$item['delay_time']) : '';
                $data[] = [
                    $item['order_num']."\t",
                    $startTime,
                    $endTime,
                    $status[$item['handle_status']],
                    $item['remarks']."\t",
                ];
            }
        }

        $this->excelReturn('批量延期记录','批量延期记录', $data);
    }

}