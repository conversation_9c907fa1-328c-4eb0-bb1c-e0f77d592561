<?php
/**
 * 退票审核相关的控制器
 *
 * User: Fang
 * Time: 15:03 2016/3/11
 * @modified dwer.cn 2019-06-26
 */

namespace Controller\Order;

use Business\Authority\DataAuthLogic;
use Business\Common\CommonConfig;
use Business\JsonRpcApi\ScenicLocalService\TimeCard;
use Business\MemberLogin\MemberLoginHelper;
use Business\NewJavaApi\TradeCenter\DivideRecord;
use Business\Order\Modify;
use Business\Order\OrderTourist;
use Business\Order\OrderTrackQuery;
use Business\Order\Query;
use Business\Order\Refund;
use Business\TeamOrder\Application;
use Controller\Order\Traits\AnnualCardTrait;
use Controller\Order\Traits\TravelVoucherTrait;
use Library\Constants\Order\OrderStatus\CommonOrderStatus;
use Library\Constants\OrderConst;
use Library\Controller;
use Library\JsonRpc\Base\Responses\ErrorResponse;
use Model\Member\Member;
use Model\Member\RefundNotice;
use Model\Order\OrderRefer;
use Model\Order\OrderSonRefundLog;
use Model\Order\OrderTools;
use Model\Order\OrderTrack;
use Model\Order\RefundAuditModel;
use Model\Order\SubOrderQuery;
use Model\Order\TeamOrderSearch;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\TradeRecord\OrderRefund;
use Library\Kafka\KafkaProducer;


class RefundAudit extends Controller
{
    use TravelVoucherTrait;
    use AnnualCardTrait;
    const MODIFY_CODE        = 2; //退款审核表中修改申请的stype值
    const CANCEL_CODE        = 3; //退款审核表中取消申请的stype值
    const APPLY_AUDIT_CODE   = 11; //订单追踪表中表示发起退款审核
    const OPERATE_AUDIT_CODE = 10; //订单追踪表中表示退款审核已处理
    const AGREE_AUDIT_CODE   = 13;
    const REFUSE_AUDIT_CODE  = 14;

    private $operatorID = null;

    //是否已经通过授权判断
    private $_isPassAuth = false;

    private $refundBiz = null;

    private $loginInfo;

    /**
     * 初始化
     * <AUTHOR>
     * @date   2016-09-18
     *
     * @param  bool $isPassAuth 是否已经授权：如果是直接内部调用=true，外部调用就是=false
     */
    public function __construct($isPassAuth = false)
    {
        $this->_isPassAuth = $isPassAuth ? true : false;
        //权限控制
        $this->_auth();
        $this->refundBiz = new Refund();
    }

    /**
     * 添加退票审核记录
     * jh_refund_audit.php迁移过来的方法，如果前端有修改的话，那边可以删除
     *
     * <AUTHOR>
     * @date   2016-09-12
     *
     */
    public function add_audit()
    {   //ClickVisual 一年内都没记录，此方法也脱离版本，直接注释
        $this->apiReturn(220, [], '暂不支持此方法');
        $orderNum    = I('post.ordernum');
        $targetTnum  = I('post.tnum');
        $modifyNum   = I('post.modify_num');
        $modifyType  = I('post.stype');
        $sn          = I('post.sn');
        $modifyType  = $modifyType ? $modifyType : ($targetTnum == 0) ? 3 : 2; //2-修改 3-取消
        $requestTime = date('Y-m-d H:i:s');
        $checkCode   = $this->refundBiz->addRefundAudit($orderNum, $modifyNum, $targetTnum, $this->operatorID, 18,
            $requestTime, [], false, $sn);
        $this->apiReturn($checkCode);
    }

    /**
     * 更新退票审核结果
     * jh_refund_audit.php迁移过来的方法，如果前端有修改的话，那边可以删除
     *
     * <AUTHOR>
     * @date   2016-09-12
     *
     */
    public function update_audit()
    {
        $orderNum       = I('post.ordernum'); //订单号
        $sType          = I('post.stype'); //变更类型   0：撤改   1：撤销  2：修改  3：取消 4：改签
        $auditID        = I('post.auditid'); //审核ID
        $auditResult    = I('post.dstatus'); //审核结果
        $auditNote      = I('post.dreason'); //备注
        $auditTime      = date('Y-m-d H:i:s');
        $sTime          = I('post.stime'); //申请退票时间
        $sTime          = '申请时间：' . $sTime;
        $msg            = '';
        $refundAuditBiz = new \Business\Order\RefundAudit();
        $refundAuditBiz->setLoginInfo($this->getLoginInfo());
        $memberId = $this->getLoginInfo()['memberID'];
        if ($sType == 4) {
            //新增改签审核--by Cai Yiqiang 2018-09-25
            $modifyBiz = new Modify();
            $result    = $modifyBiz->ticketChangingAudit($orderNum, $auditResult, $auditNote, $memberId, $auditID);
        } else {
            //如果为取消退票
            $orderOrderToolsMode = new OrderTools();
            $info                = $orderOrderToolsMode->getOrderInfo($orderNum, 'ordermode, apply_did');
            $operatorID          = 0;
            if ($sType == 3) {

                if ($info['apply_did'] != $this->operatorID) {
                    $this->apiReturn(404, [], '非法操作');
                }

                //如果订票的渠道来自云票务
                if ($info['ordermode'] == 10) {
                    //去订单追踪表取出提交退票申请操作人员id
                    //$orderTrackMode = new OrderTrack();
                    //$trackInfo      = $orderTrackMode->getTrackInfoByOrderAction($orderNum, 'oper_member', 11);

                    //订单查询迁移二期
//                    $orderTrackQueryLib = new OrderTrackQuery();
//                    $trackInfo          = $orderTrackQueryLib->getTrackInfoByOrderAction([strval($orderNum)], [11]);

//                    $operatorID = $trackInfo['oper_member'];
                }
            }
            //如果为云票务取消订单，则退票时操作人员id还是为下单时的操作人员id
            if (!empty($operatorID)) {
                $result = $refundAuditBiz->updateRefundAudit($orderNum, $auditResult, $auditNote, $operatorID,
                    $auditID);
            } else {
                $moreData = [];
                if (MemberLoginHelper::isSubMerchantLogin()){
                    $memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                    $moreData['subSid'] = MemberLoginHelper::getLoginBusinessMember()->getSId();
                    $moreData['subOpId'] = MemberLoginHelper::getLoginBusinessMember()->getMemberId();
                }
                $result = $refundAuditBiz->updateRefundAudit($orderNum, $auditResult, $auditNote, $memberId, $auditID, $moreData);
            }
        }
        if (is_numeric($result)) {
            $data['code'] = $result;
            $data['msg']  = $this->refundBiz->getMsg($result);
        } else {
            $data = $result;
        }
        if ($data['code'] == 200 && $auditResult == 2 && $orderNum && $sType == 3) {
            //如果是人脸订单，拒绝取消要重新授权人脸
            $faceModel = new \Model\Terminal\FaceCompare();
            $orderFace = $faceModel->getFaceByOrder([$orderNum], 2);
            if ($orderFace) {
                $faceModel->unbindFaceOrder($orderNum, 0, '', 0, 1);
                $orderModel  = new OrderTools('slave');
                $orderInfo   = $orderModel->getOrderInfo($orderNum, 'lid,begintime,salerid'); //根据订单号取lid
                $landModel   = new \Model\Product\Land('slave');
                $terminal    = $landModel->getTerminalByLid($orderInfo['lid']);
                $nowTerminal = $landModel->getNowTerminal($terminal);
                if ($nowTerminal) {
                    //如果有合并终端的，取合并后的终端
                    $terminal = $nowTerminal;
                }
                $facePlatform = $faceModel->getFacePlatform(false, $terminal);
                if ($facePlatform['face_platform'] == 2) {
                    $playDate   = date('Ymd', strtotime($orderInfo['begintime']));
                    $faceIds    = array_column($orderFace, 'face_info_id');
                    $faceData   = $faceModel->getFaceInfoByFaceIds($faceIds, 'guid');
                    $guidArray  = array_column($faceData, 'guid');
                    $deviceData = $faceModel->getDeviceByTerminalId($terminal);
                    $deviceKeys = array_column($deviceData, 'deviceKey');
                    $deviceStr  = implode(',', $deviceKeys);
                    if ($playDate > date('Ymd')) {
                        $faceModel->updateFaceClearTask(false, 0, '拒绝退款重新人脸授权', $guidArray, $deviceStr, $orderNum);
                    } elseif ($playDate == date('Ymd')) {
                        $ufaceLib = new \Library\Business\Uface\Person();
                        foreach ($guidArray as $value) {
                            $result = $ufaceLib->devicesAuth($value, $deviceStr);
                        }
                    }
                }
            }
        }

        //团单的判断
        if ($data['code'] == 200 && isset($info) && $info['ordermode'] == 24) {
            $app          = new Application();
            $teamOrderMdl = new TeamOrderSearch();
            $order        = $app->order;
            $teamOrder    = $teamOrderMdl->getMainOrderInfoBySonOrder($orderNum);
            $res          = $order->modifyTeamTicketNum($teamOrder['main_ordernum'], $result['data']['change']);
            if (!$res) {
                $this->apiReturn(260, [], '更新团队订单人数失败');
            }
        }

        $this->apiReturn($data['code'], [], $data['msg']);
    }

    /**
     * 获取退票审核记录
     * jh_refund_audit.php迁移过来的方法，如果前端有修改的话，那边可以删除
     *
     * <AUTHOR>
     * @date   2016-09-12
     *
     */
    public function get_audit_list()
    {
        $orderNum    = I('param.ordernum');
        $page        = I('param.page');
        $limit       = I('param.limit');
        $noticeType  = is_numeric(I('param.stype')) ? I('param.stype') : null;
        $landTitle   = I('param.ltitle');
        $landId      = I('param.lid',0);
        $tid         = I('param.tid',0);
//        $applyTime   = I('param.stime');
        $auditStatus = is_numeric(I('param.dstatus')) ? I('param.dstatus') : null;
//        $auditTime   = I('param.dtime');
        $timeType = I('param.time_type',0);//申请时间：1   审核时间：2
        $beginTime = I('param.begin_time',0);
        $endTime = I('param.end_time',0);
        $auditType   = I('audit_type', 0); //0-普通订单业务 1-特产订单业务
        $subSid      = I('param.sub_sid', 0, 'intval');
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }
        //数据权限过滤
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition();
        if ($condition === false) {
            $this->apiReturn(self::CODE_SUCCESS, ['audit_list' => [], 'limit' => $limit, 'page' => $page, 'total' => 0]);
        }
        $condition['is_sub_merchant'] = MemberLoginHelper::isSubMerchantLogin();
        //防止慢语句，不允许like检索land_tile
        $landTitle = '';
        $loginInfo = $this->getLoginInfo();
        $memberId  = $loginInfo['memberID'];
        $this->getAuditList($this->operatorID, $landTitle, $noticeType, $timeType, $beginTime, $endTime,$auditStatus, $orderNum,
            $page, $limit, $auditType, $landId, $tid, $subSid, $condition,$memberId);
    }

    /**
     * 根据订单号获取管理员特殊需要强制操作的订单
     *
     * <AUTHOR>
     * @date   2018-05-17
     *
     * @param  string ordernum 平台订单号
     *
     */
    public function getSpecialOrder()
    {
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        $memberId  = $loginInfo['memberID'];

        if ($sid != 1) {
            // 供应商员工是没有权限的 所以用登录账号的id
            $adminConfModel = new \Model\AdminConfig\AdminConfig();
            $checkPowerRes  = $adminConfModel->havePermission($sid, 25);
            if (!$checkPowerRes) {
                $this->apiReturn(400, [], '只有管理员或开通权限的账号主账号可以强制取消');
            }
            if ($loginInfo['dtype'] == 6) {
                if (empty($loginInfo['qx'])) {
                    $this->apiReturn(400, [], '员工账号未开通权限');
                } else {
                    $staffAuthArr = explode(',', $loginInfo['qx']);
                    if (!in_array('special_order', $staffAuthArr)) {
                        $this->apiReturn(400, [], '员工账号未开通权限');
                    }
                }
            }
        }

        //目前前端只支持订单号查询，预接收其他字段以后扩展
        $orderNum = I('param.ordernum', '', 'strval');
        $size = I('param.size', 0, 'intval');
        if (empty($orderNum)) {
            $this->apiReturn(400, [], '订单号不能为空');
        }

        //查询是否为旅游券，若是，走旅游券的接口
        $isTravelVoucher = $this->judgeTravelVoucher($orderNum);
        if($isTravelVoucher){
            $tvInvoiceApi = new \Business\JsonRpcApi\TravelVoucherService\SpecialOrders();
            if($size){
                $tvOrderInfo  = $tvInvoiceApi->getOrderForDelay($orderNum, $memberId);
            }
            else{
                $tvOrderInfo  = $tvInvoiceApi->getOrderForForceRefund($orderNum, $sid, $memberId);
            }
            if ($tvOrderInfo['code'] != 200) {
                pft_log('/Order/getSpecialOrder/error',
                    '强制退票查询，未查到订单信息' . json_encode(['ordernum' => $orderNum, 'sid' => $sid, 'tmpOrderInfo'=>$tvOrderInfo]));
                $this->apiReturn($tvOrderInfo['code'], [], $tvOrderInfo['msg']);
            }
            $orderList[] = $tvOrderInfo['data'];
            $data      = ['list' => $orderList, 'total' => 1];
        } else {
            //默认就写死只有一页数据，之前逻辑只查询前10条数据
            $orderList = $this->_getSpecialInfo($orderNum, $sid, $memberId);
            $data      = ['list' => $orderList, 'total' => 1];
        }
        $this->apiReturn('200', $data, '查询成功');
    }

    /**
     * @param  string  $ordernum  平台订单号
     *
     * @return string | array
     *
     */
    private function _getSpecialInfo($ordernum, $loginSid, $memberId)
    {
        $ordernum = trim($ordernum);
        
        if (empty($ordernum)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单号不能为空');
        }
        if (strlen($ordernum) <= 3) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入合理的订单号');
        }

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        if ($dataAuthLimit->isAllForbid()) {
            return [];
        }

        $queryArr       = [$ordernum];
        $allOrdernumArr = $queryArr;

        $orderModel       = new OrderTools();
        $packModel        = new OrderRefer();
        $linkOrder        = $orderModel->getLinkSubOrderInfo($queryArr, 'orderid'); //联票订单
        $packOrder        = $orderModel->getPackSubOrder($queryArr, 'orderid, pack_order'); //套票订单
        $mainPaceOrderArr = []; // 套票主订单号

        if ($linkOrder) {
            $linkOrder        = array_column($linkOrder, 'orderid');
            $mainPaceOrderArr = array_unique(array_column($packOrder, 'pack_order'));
            $allOrdernumArr   = array_merge($linkOrder, $queryArr);
        }

        if ($packOrder) {
            $packOrder      = array_column($packOrder, 'orderid');
            $allOrdernumArr = array_merge($packOrder, $queryArr);
        }

        $allOrdernumArr = array_unique($allOrdernumArr);

        //$orderInfo      = $orderModel->getOrderInfos($option,
        //    'ordernum,ordertime,status,pay_status,lid,tid,tnum,aid,code,salerid,dtime,ordermode,member,ifpack,pack_order',
        //    1, 10, true);

        $orderInfo      = $orderModel->getOrderListNew($allOrdernumArr, $field = 'ordernum,ordertime,status,pay_status,lid,tid,tnum,aid,code,salerid,dtime,ordermode,member,endtime,begintime,apply_did', false, 'ifpack,pack_order','verified_num');

        if (empty($orderInfo)) {
            $this->apiReturn(400, [], '找不到符合条件的订单');
        }

        $payConf     = load_config('pay_status', 'orderSearch');
        $orderStatus = load_config('order_status', 'orderSearch');

        $landIds   = array_column($orderInfo, 'lid');
        $ticketIds = array_column($orderInfo, 'tid');
        $aids      = array_column($orderInfo, 'aid');

        //$landModel   = new Land();
        $ticketModel = new Ticket();
        $memberModel = new Member();
        //$landInfo    = $landModel->getLandInfoByMuli($landIds, 'id, title, p_type', 1);

        $javaAPi  = new \Business\CommodityCenter\Land();
        $landInfo = $javaAPi->queryLandMultiQueryById($landIds);
        $landInfo = array_column($landInfo, null, 'id');

        $ticketInfo   = $ticketModel->getTicketList($ticketIds, 'id, title, apply_did, refund_rule');
        $supplierInfo = $memberModel->getMemberInfoByMulti($aids, 'id', 'id, dname', true);

        $orderTerminalModel = new \Model\Order\OrderTerminal();
        $refundAuditInfo    = $packModel->getRefundAuditByOrderId($queryArr);
        $alertAuditInfo     = $orderTerminalModel->getAlertAuditByOrderId($queryArr);

        $apiOrderModel   = new \Model\Ota\AllApiOrderModel();
        $apiOrderInfoArr = $apiOrderModel->selectInfo('id,apiOrder,tempOrder',
            ['tempOrder' => ['IN', $queryArr]]);
        $apiOrderArr     = [];
        if ($apiOrderInfoArr) {
            $apiOrderArr = array_column($apiOrderInfoArr, 'tempOrder');
        }
        //获取计时订单信息
        $timeCardJsonRpc = new TimeCard();
        $timeOrderInfo   = $timeCardJsonRpc->getTimeOrderInfo($allOrdernumArr);
        $timeOrderInfo   = $timeOrderInfo['data'];
        //获取允许撤销7天后的订单的供应商ID
        //$allowRevokeMembers = \Business\Order\RefundAudit::getRevokeMemberList();
        $refundAuditBiz = new \Business\Order\RefundAudit();
        foreach ($orderInfo as $k => $v) {
            if ($dataAuthLimit->hasLidBeenLimit($v['lid'])) {
                // 如果是主票订单被禁止则不展示子票信息
                if ($v['ordernum'] == $ordernum) {
                    return [];
                }
                unset($orderInfo[$k]);
                continue;
            }
            $pType        = $landInfo[$v['lid']]['p_type'];
            $isRevoke     = false;
            $isCancel     = false;
            $isAudit      = false;
            $isOta        = false;
            $isRefund     = false;
            $isWithdrawal = false;
            //特定用户允许他撤销多次
            //允许撤销场景: 1=已使用, 5=撤改, 7=部分使用
            $canRevoke = $refundAuditBiz->isCanRevoke($v['ordernum'], $v['status'], $v['tnum'], $v['apply_did']);
            if ($canRevoke && $v['verified_num'] > 0) {
                $isRevoke     = $v['status'] == 1; //只有已使用允许全部撤销
                $isWithdrawal = true;
                //验证时间大于7七天则无法撤销撤改
                $isAllowExpireRevoke = \Business\Order\RefundAudit::isAllowExpireRevoke($v['apply_did']);
                if (isset($v['dtime']) && !$isAllowExpireRevoke) {
                    if ($v['dtime'] && $v['dtime'] != '0000-00-00 00:00:00') {
                        if (time() - strtotime($v['dtime']) > 604800) {
                            $isRevoke     = false;
                            $isWithdrawal = false;
                        }
                    }
                }
            }

            if ($loginSid != 1 && $loginSid != $ticketInfo[$v['tid']]['apply_did']) {
                unset($orderInfo[$k]);
                continue;
            }

            // 未使用，已过期，部分使用 允许取消
            if (in_array($v['status'], [
                    0,
                    2,
                    7,
                    CommonOrderStatus::WAIT_APPOINTMENT_CODE,
                    CommonOrderStatus::WAIT_PRINTTICKET_CODE,
                ]) && $v['tnum'] != 0) {
                $isCancel = true;
                if ($v['ifpack'] == 2 && $v['pay_status'] == 1) {
                    $isRefund = true;
                }
            }

            // 有正在退款审核中的数据
            if ($refundAuditInfo && in_array($v['ordernum'], $refundAuditInfo)) {
                $isAudit = true;
            }

            // 有正在撤销审核中的数据
            if ($alertAuditInfo && in_array($v['ordernum'], $alertAuditInfo)) {
                $isAudit = true;
            }

            // 年卡订单不让操作
            //下单方式:0=正常分销商下单,1=普通用户支付,2=用户手机支付,3=会员卡购票,10=云票务,11=微信商城,12=自助机,13=二级店铺,14=闸机购票,15=智能终端,16=计调下单,17=淘宝码商,18=年卡,19=微信端,20=外部接口OTA下单,22=一卡通,23=套票子票
            if (in_array($v['ordermode'], [18])) {
                $isRevoke     = false;
                $isWithdrawal = false;
                $isCancel     = false;
                $isAudit      = false;
            }

            // 隐藏套票的撤销功能
            if ($v['ordermode'] == 23 || in_array($v['ordernum'], $mainPaceOrderArr)) {
                $orderQuery = new Query();
                $isBindTicket = $orderQuery->isBindTicketForOrderNum($v['ordernum']);
                if($isBindTicket && !in_array($v['ordernum'], $mainPaceOrderArr)){
                    //需要重新获取指定订单是否绑定三方
                    $apiOrderInfoArr = $apiOrderModel->selectInfo('id,apiOrder,tempOrder',
                        ['tempOrder' => ['IN', [$v['ordernum']]]]);
                    if ($apiOrderInfoArr) {
                        $linkOrderArr = array_column($apiOrderInfoArr, 'tempOrder');
                        array_push($apiOrderArr,reset($linkOrderArr));
                    }
                }
                if(!$isBindTicket){
                    $isRevoke     = false;
                    $isWithdrawal = false;
                }
            }
            //特产不允许撤销撤改
            if($pType == 'J') {
                $isRevoke     = false;
                $isWithdrawal = false;
            }

            // 判断是否对接第三方的订单
            if (in_array($v['ordernum'], $apiOrderArr)) {
                $isOta        = true;
                $isRefund     = false;
            }
            $refundSonLogMdl = new OrderSonRefundLog();
            $refundMoney     = 0;
            if ($v['ifpack'] == 1) {
                $refundMoney = $refundSonLogMdl->getPackOrderRefundMoney($v['ordernum']);
            } elseif ($v['ifpack'] == 2) {
                $refundMoney = $refundSonLogMdl->getPackOrderRefundMoney($v['pack_order']);
            }
            if ($refundMoney > 0) {
                $isRefund = false;
            }

            //年卡类型订单 都不让展示任何操作
            if ($pType == 'I') {
                //https://www.tapd.cn/67423817/prong/stories/view/1167423817001126948
                //如果年卡订单是已使用的展示撤销操作按钮
                //订单状态：0=未使用,1=已使用,2=已过期,3=被取消,4=待确认(酒店)，待收货(特产),5=被终端修改,6=被终端撤销,7=部分使用,8=订单完结,9=被删除
                $isRevoke     = $v['status'] == 1 && $v['ordermode'] != 18;
                $isCancel     = false;
                $isAudit      = false;
                $isOta        = false;
                $isRefund     = false;
                $isWithdrawal = false;
            }
            if ($isCancel || $isRevoke){
                //这边判断下能不能用门票码取消
                $orderTouristMdl = new \Model\Order\OrderTourist();
                $ticketCodeNum = $orderTouristMdl->getTouristNumByStatusGroupChkCode($v['ordernum']);
                if ($ticketCodeNum > 1) {
                    $canUsedTicketCode = true;
                } else {
                    $canUsedTicketCode = false;
                }
            }
            $orderInfo[$k]['pay_status']  = $payConf[$v['pay_status']]; //订单支付状态  eg：已支付
            $orderInfo[$k]['status_info'] = $orderStatus[$v['status']]; //订单状态 eg：已过期
            $orderInfo[$k]['aid']         = $supplierInfo[$v['aid']]['dname'];//商户名称
            $orderInfo[$k]['lid']         = $landInfo[$v['lid']]['title'];//景区名称
            $orderInfo[$k]['tid']         = $ticketInfo[$v['tid']]['title'];//景区ID
            $orderInfo[$k]['cancel']      = $isCancel;//是否取消
            $orderInfo[$k]['revoke']      = $isRevoke;//是否撤销
            $orderInfo[$k]['auditing']    = $isAudit;//是否审核
            $orderInfo[$k]['isOta']       = $isOta;//是否OTA
            $orderInfo[$k]['is_refund']   = $isRefund;//是否退款
            $orderInfo[$k]['withdrawal']  = $isWithdrawal;//是否撤改
            $orderInfo[$k]['p_type']      = $pType;//产品类型
            $orderInfo[$k]['can_cancel_num'] = $isCancel ? $v['tnum'] - $v['verified_num'] : 0;//允许取消数量
            $orderInfo[$k]['can_revoke_num'] = $v['verified_num']; //允许撤改的数量
            $orderInfo[$k]['can_used_ticket_code'] = $canUsedTicketCode ?? false;//允许使用门票码
            //计时订单票属性
            $orderInfo[$k]['time_data'] = [
                'time_order_deposit' => ($timeOrderInfo[$v['ordernum']]['unit_deposit'] ?? 0) * $v['tnum'], //计时订单押金
                'time_money_cost'    => $timeOrderInfo[$v['ordernum']]['money_cost'] ?? 0,  //计时订单超时补费金额
                'refund_state'       => $timeOrderInfo[$v['ordernum']]['refund_state'] ?? 2,  //计时订单超时补费退款状态 1：未退款 2：已退款 3：部分退款
            ];
            //非原始供应商查看订单，不展示押金跟超时补费金额
            if ($v['apply_did'] != $loginSid) {
                $orderInfo[$k]['time_data'] = [
                    'time_order_deposit' => 0, //计时订单押金
                    'time_money_cost'    => 0,  //计时订单超时补费金额
                    'refund_state'       => 2
                ];
            }
            if ($loginSid != 1) {
                $orderInfo[$k]['refund_rule'] = $ticketInfo[$v['tid']]['refund_rule'];//退款规则
            }
        }
        $orderInfo = array_values($orderInfo);
        return $orderInfo ? $orderInfo : [];
    }

    /**
     * 获取能取消或者撤销的码
     *
     * @param  string revoke_num 撤销数量
     * @param  string code       凭证码
     * @param  int    salerid
     *
     */
    public function getCanCancelTicketCode()
    {
        $type     = I('get.type', 0, 'intval');// 1-取消 2-撤销
        $orderNum = I('get.ordernum', '');
        $productType = I('get.p_type', '');
        if (!in_array($type, [1, 2])) {
            $this->apiReturn(400, [], '类型有误');
        }
        if (!$orderNum) {
            $this->apiReturn(400, [], '订单号有误');
        }
        //旅游券逻辑
        if($productType == 'Q'){
            $loginInfo = $this->getLoginInfo();
            $sid       = $loginInfo['sid'];
            $memberId  = $loginInfo['memberID'];
            $tvInvoiceApi = new \Business\JsonRpcApi\TravelVoucherService\SpecialOrders();
            $tvOrderInfo  = $tvInvoiceApi->getVerifiesForForceRefund($orderNum, $sid, $memberId, $type);
            if ($tvOrderInfo['code'] != 200) {
                pft_log('/Order/getSpecialOrder/error',
                    '指定门票撤改查询' . json_encode(['ordernum' => $orderNum, 'sid' => $sid, 'tmpOrderInfo'=>$tvOrderInfo]));
                $this->apiReturn($tvOrderInfo['code'], [], $tvOrderInfo['msg']);
            }
            else{
                $this->apiReturn('200', $tvOrderInfo['data'], '查询成功');
            }
        }
        else{
            $orderTouristBiz = new OrderTourist();
            $result = $orderTouristBiz->getCanCancelTicketCode($orderNum, $type);
            if (isset($result['code'])) {
                $this->apiReturn($result['code'], $result['data'], $result['msg']);
            } else {
                $this->apiReturn(500, [], '服务异常');
            }
        }
    }

    /**
     * 旅游券 强制撤销以及强制撤改
     * @param $orderNum
     * @param $memberId
     * @param $sid
     * @param $opId
     * @param $cancelIdxStr
     * @param $revokeNum
     */
    public function adminRevokeOrderForVoucher($orderNum,$memberId,$sid,$cancelIdxStr,$revokeNum)
    {
        //查询订单是否存在分账记录，且分账记录处于处理中状态
        $divideRecord = new DivideRecord();
        $divideRecordInfo = $divideRecord->queryDivideRecordByOrderId($orderNum);
        if ($divideRecordInfo['code'] != 200) {
            $this->apiReturn(203, [], '查询分账记录失败');
        }
        if (isset($divideRecordInfo['data']['ruleId']) && $divideRecordInfo['data']['ruleId'] > 0) {
            $this->apiReturn(203, [], '当前订单为分账订单，无法撤销，请使用售后功能');
        }

        $tvInvoiceApi = new \Business\JsonRpcApi\TravelVoucherService\SpecialOrders();
        $res = $tvInvoiceApi->getOrderForForceRefund($orderNum, $sid, $memberId);
        if($res['data']['tnum'] == $revokeNum){
            //撤销
            $tvOrderInfo  = $tvInvoiceApi->forceCancelAllForForceRefund($orderNum, $sid, $memberId);
            if ($tvOrderInfo['code'] != 200) {
                pft_log('/Order/getSpecialOrder/error',
                    '旅游券强制撤销' . json_encode(['ordernum' => $orderNum, 'sid' => $sid, 'tmpOrderInfo'=>$tvOrderInfo]));
                $this->apiReturn($tvOrderInfo['code'], [], $tvOrderInfo['msg']);
            }
            else{
                $this->apiReturn(200, [], '强制撤销申请成功');
            }
        }
        else{
            //撤改
            $cancelIdxArr =[];
            if(!empty($cancelIdxStr)){
                $cancelIdxArr = explode(',', $cancelIdxStr);
                $revokeNum = count($cancelIdxArr);
            }
            $tvOrderInfo  = $tvInvoiceApi->forceCancelSectionForForceRefund($orderNum, $sid, $memberId,$revokeNum,$cancelIdxArr);
            if ($tvOrderInfo['code'] != 200) {
                pft_log('/Order/getSpecialOrder/error',
                    '旅游券强制撤改' . json_encode(['ordernum' => $orderNum, 'sid' => $sid, 'tmpOrderInfo'=>$tvOrderInfo]));
                $this->apiReturn($tvOrderInfo['code'], [], $tvOrderInfo['msg']);
            }
            else{
                $this->apiReturn(200, [], '强制撤改申请成功');
            }
        }
    }

    /**
     * 强制撤销  强制撤改
     * 商户端供应商强制撤销撤改功能
     * @param string revoke_num 撤销数量
     * @param string code       凭证码
     * @param int    salerid
     *
     */
    public function adminRevokeOrder()
    {
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        $memberId  = $loginInfo['memberID'];
        $opId      = $memberId;


        // 供应商员工是没有权限的 所以用登录账号的id
        $adminConfModel = new \Model\AdminConfig\AdminConfig();
        $checkPowerRes  = $adminConfModel->havePermission($sid, 25);
        if (!$checkPowerRes) {
            $this->apiReturn(400, [], '只有管理员或开通权限的账号主账号可以强制取消');
        }
        if ($loginInfo['dtype'] == 6) {
            if (empty($loginInfo['qx'])) {
                $this->apiReturn(400, [], '员工账号未开通权限');
            } else {
                $staffAuthArr = explode(',', $loginInfo['qx']);
                if (!in_array('special_order', $staffAuthArr)) {
                    $this->apiReturn(400, [], '员工账号未开通权限');
                }
            }
        }

        $revokeNum = I('revoke_num', 0);
        $orderNum  = I('ordernum', 0);
//        $code      = I('code', 0);
        $salerId         = I('salerid', 0);
        $productType     = I('p_type', '');
        $isRefundDeposit = I('post.refund_deposit', 0, 'intval'); //是否退押金 0：不退押金 1:退押金（计时特有）
        $isRefundOverPay = I('post.refund_over_pay', 0, 'intval'); //是否退超时补费金额 0：不退 1:退（计时特有）
        $cancelAuditRemark =I('post.cancel_audit_remark', '');//退票申请备注
        $cancelIdxStr = I('cancel_idx_str','');
        $isSmsNotice = I('is_sms_notice',0);
        if ($revokeNum < 0  || $salerId < 1 || empty($orderNum)) {
            $this->apiReturn(203, [], '参数有误');
        }
        //年卡类型撤销校验
        if ($productType == 'I') {
            $ret = $this->annualCanRevoke($orderNum);
            if (!$ret['res']) {
                $this->apiReturn(203, [], $ret['msg']);
            }
        }
        //旅游券逻辑
        if($productType == 'Q'){
            self::adminRevokeOrderForVoucher($orderNum,$memberId,$sid,$cancelIdxStr,$revokeNum);
        }

        $code = I('code', 0);
        if ($code < 1) {
            $this->apiReturn(203, [], '参数有误');
        }
        $landApi     = new \Business\CommodityCenter\Land();
        $landInfoArrRes = $landApi->queryLandMultiQueryByAdminAndPaging([], 1, 1, '', 'id desc', false, [], [], [], [],
            [$salerId]);

        $landInfoArr = [];
        if ($landInfoArrRes['list']) {
            $landInfoArr = $landInfoArrRes['list'][0];
        }

        $orderToolModel = new OrderTools();
        $orderInfoArr   = $orderToolModel->getOrderInfo($orderNum, 'id,tnum,aid,member,tid', false, false, 'apply.verified_num');

        if (empty($landInfoArr) || empty($landInfoArr['terminal']) || empty($orderInfoArr['tnum']) || empty($orderInfoArr['verified_num'])) {
            $this->apiReturn(203, [], '参数有误');
        }

        $ticketModel   = new Ticket();
        $ticketInfoArr = $ticketModel->getTicketInfoById($orderInfoArr['tid'], 'id, apply_did');

        if ($sid != 1 && $sid != $ticketInfoArr['apply_did']) {
            $this->apiReturn(203, [], '非自供应订单不可退');
        }
        $cancelIdxArr = [];
        if ($cancelIdxStr) {
            //简单做根据游客表里面有多少个门票码确定是不是要用idx指定取消
            $touristMdl = new \Model\Order\OrderTourist();
            $touristNum = $touristMdl->getTouristNumByStatusGroupChkCode($orderNum);
            //todo 实际上这边产品需求是要根据票属性是否是一票一码还是一票种一张票判断，这边直接简单判断下门票码生成的数量
            if ($touristNum > 1) {
                $cancelIdxArr = explode(',', $cancelIdxStr);
                $revokeNum = count($cancelIdxArr);
            } else {
                $revokeNum = $orderInfoArr['verified_num'];
            }
        }

        $leftNum = $orderInfoArr['tnum'] - $revokeNum;

        if($revokeNum > $orderInfoArr['verified_num']) {
            $this->apiReturn(203, [], '撤改数量超过已验证数');
        }

        if ($leftNum < 0) {
            $this->apiReturn(203, [], '修改数量有误');
        }
        //增加退票审核备注校验
        $commonConfig = new CommonConfig();
        $resConfig = $commonConfig->getRefundApplyConfig($sid);
        if($resConfig['code'] !=200){
            return $resConfig;
        }
        $isNeedRefundApply = $resConfig['data']['need_refund_apply'];
        if($isNeedRefundApply && empty($cancelAuditRemark)){
            $this->apiReturn(213, [], '需填写退票申请备注！');
        }
        //查询订单是否存在分账记录，且分账记录处于处理中状态
        $divideRecord = new DivideRecord();
        $divideRecordInfo = $divideRecord->queryDivideRecordByOrderId($orderNum);
        if ($divideRecordInfo['code'] != 200) {
            $this->apiReturn(203, [], '查询分账记录失败');
        }
        if (isset($divideRecordInfo['data']['ruleId']) && $divideRecordInfo['data']['ruleId'] > 0) {
            $this->apiReturn(203, [], '当前订单为分账订单，无法撤销，请使用售后功能');
        }
        //撤销时 订单中有票若非未售后或者售后关闭时 需提示
        //撤改时 选择的票非未售后或者售后关闭 需提示
        list($isAllowRevoke,$msg)=self::judgeRevokeOfAfterSale($orderNum,$leftNum,$revokeNum,$cancelIdxArr);
        if(!$isAllowRevoke){
            $this->apiReturn(203, [], $msg);
        }
        $refundBiz    = new \Business\Order\Refund();
        $revokeResArr = $refundBiz->TerminalRevoke($orderNum, $leftNum, $landInfoArr['terminal'], $opId, $cancelIdxArr, $isRefundDeposit, $isRefundOverPay, $revokeNum, $cancelAuditRemark, $isSmsNotice);

        if ($revokeResArr['code'] == '0901' || $revokeResArr['code'] == '0802') {
            $this->apiReturn(200, [], '撤销申请成功');
        }

        $this->apiReturn(205, [], $revokeResArr['msg']);
    }

    /**
     * 检查平台上的门票是否需要退票审核（针对联票优化独立出来)
     */
    public function checkRefundAuditFromWeb()
    {
        $operatorID  = $this->loginInfo['sid'];
        $orderNum    = trim(I('param.ordernum')); //订单号
        $modifyType  = trim(I('param.stype')); //修改类型
        $targetTnums = [];
        if (!$operatorID) {
            $this->apiReturn(203);
        }

        if (!in_array($modifyType, [2, 3])) {
            $this->apiReturn(208);
        }

        //格式化订单号与修改后票数
        if ($modifyType == self::MODIFY_CODE) {
            //修改后票数  [订单号]=变更后票数
            $targetTnums = I('param.tids');
        } elseif ($modifyType == self::CANCEL_CODE) {
            $orderModel = new OrderTools();
            if ($subOrders = $orderModel->getLinkSubOrder($orderNum)) {
                foreach ($subOrders as $subOrderNum) {
                    $targetTnums[$subOrderNum] = 0;
                }
            } else {
                $targetTnums[$orderNum] = 0;
            }
        }
        if (!is_array($targetTnums) || count($targetTnums) == 0) {
            $this->apiReturn(208);
        }
        //检验所有门票的退票审核属性，需要退票的要查出票类名称
        $checkCode = 100;
        foreach ($targetTnums as $subOrder => $subOrderTnum) {
            $subCheckCode = $this->refundBiz->checkRefundAudit($subOrder, $subOrderTnum, $operatorID, $modifyType);
            if ($subCheckCode == 100) {
                continue;
            } elseif ($subCheckCode == 200) {
                $checkCode = $subCheckCode;
                continue;
            } else {
                $checkCode = $subCheckCode;
                break;
            }
        }
        $msg = ($checkCode == 200) ? '该订单退票需审核' : '';
        $this->apiReturn($checkCode, [], $msg);
    }

    /**
     * 取消订单操作
     */
    public function orderCancel()
    {
        // 获取订单
        $ordernum = I('ordernum', '', 'intval');

        if (!$ordernum) {
            $this->apiReturn('403', [], '参数缺失,无法操作');
        }

        $ordernum = strval($ordernum);

        $queryParams = [$ordernum];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder', 'queryOrderInfoByOrdernum',
            $queryParams);
        if ($queryRes['code'] != 200) {
            $this->apiReturn('403', [], '接口错误请重试');
        }
        if (!$queryRes['data']) {
            $this->apiReturn('403', [], '订单不存在');
        }
        $orderInfo = $queryRes['data'];
        if (!$orderInfo) {
            $this->apiReturn('403', [], '订单不存在');
        }
        $orderInfo = $queryRes['data'];

        $loginInfoArr = $this->getLoginInfo();

        // 判断用户是否是员工， 如果是员工则判断父类用户是否有权操作
        if ($loginInfoArr['dtype'] == 6) {
            $sid = $loginInfoArr['sid'];
        }
        $sid = isset($sid) ? $sid : null;

        // 判断权限， 管理员无权退票
        if ($orderInfo['aid'] != $loginInfoArr['memberID'] && $orderInfo['aid'] != $sid) {
            $this->apiReturn('401', [], '您不是团购订单所属者，请确认登录身份');
        }

        // 如果当前订单是未使用，则传递当前订单的mid
        $memberID = in_array($orderInfo['status'], [0, 7]) ? $orderInfo['member'] : $loginInfoArr['sid'];

        $memType = $loginInfoArr['dtype'];
        if ($loginInfoArr['sid'] == 1) {
            $memType = 100;
        }

        $modifyBiz       = new Modify();
        $reqSerialNumber = $modifyBiz->getAuditInfoByOrder($ordernum);
        $res             = $modifyBiz->baseCancel($ordernum, $memberID, $loginInfoArr['sid'],
            OrderConst::GROUP_PURCHASE_CANCEL, ''
            , $reqSerialNumber);
        //$res       = $modifyBiz->cancel($ordernum, $memberID, $loginInfoArr['memberID'], $memType);

        if ($res['code'] === 200) {
            $this->apiReturn('200', [], '退票成功');
        } else {
            $this->apiReturn('403', [], $res['msg']);
        }
    }

    /**
     *  修改订单数
     */
    public function orderAlter()
    {
        $tids     = I('tids', '', 'intval');
        $ordernum = I('ordernum', '', 'intval');

        if (!$tids && !$ordernum) {
            $this->apiReturn('403', [], '参数缺失,无法操作');
        }
        $ordernum = strval($ordernum);

        $queryParams = [$ordernum];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder', 'queryOrderInfoByOrdernum',
            $queryParams);
        if ($queryRes['code'] != 200 || !$queryRes['data']) {
            $this->apiReturn('403', [], '订单不存在');
        }
        $orderInfo = $queryRes['data'];

        if (!$orderInfo) {
            $this->apiReturn('403', [], '订单不存在');
        }
        $orderInfo = $queryRes['data'];

        $loginInfoArr = $this->getLoginInfo();

        // 判断用户是否是员工， 如果是员工则判断父类用户是否有权操作
        if ($loginInfoArr['dtype'] == 6) {
            $sid = $loginInfoArr['sid'];
        }
        $sid = isset($sid) ? $sid : null;

        // 判断权限， 管理员无权退票
        if ($orderInfo['aid'] != $loginInfoArr['memberID'] && $orderInfo['aid'] != $sid) {
            $this->apiReturn('401', [], '您不是团购订单所属者，请确认登录身份');
        }

        // 如果当前订单是使用中，则传递当前订单的mid
        $memberID = in_array($orderInfo['status'], [0, 2, 7]) ? $orderInfo['member'] : $loginInfoArr['memberID'];

        $modifyBiz    = new Modify();
        $orderToolMdl = new OrderTools();
        foreach ($tids as $key => &$value) {
            if ($key != $ordernum) {   //联票情况，正常只会传一个进来
                $queryParams = [$ordernum];
                $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder', 'queryOrderInfoByOrdernum',
                    $queryParams);
                if ($queryRes['code'] != 200) {
                    $this->apiReturn('400', [], '查询联票情况失败');
                }
                $orderInfo = $queryRes['data'];
            }
            $checkNum = $orderToolMdl->getVerifiedNum($key);
            $value    = $orderInfo['tnum'] - ($value + $checkNum);
        }
        $reqSerialNumber  = $modifyBiz->getAuditInfoByOrder($ordernum);
        $res              = $modifyBiz->baseRefund($ordernum,$tids,$memberID,$loginInfoArr['sid'],OrderConst::GROUP_PURCHASE_CANCEL,[],'',$reqSerialNumber);

        if ($res['code'] === 200) {
            $this->apiReturn('200', [], '修改成功');
        } else {
            $this->apiReturn('403', [], $res['msg']);
        }

    }

    /**
     * 获取撤改审核列表数据
     *
     * @param null $operatorID
     * @param null $landTitle
     * @param null $noticeType
     * @param null $applyTime
     * @param null $auditStatus
     * @param null $auditTime
     * @param null $orderNum
     * @param int  $page
     * @param int  $limit
     * @param int  $auditType  审核类型 1-特产
     * @param array $extCondition ['lidList' => '允许产品列表', 'notLidList' => '排除的产品列表']
     */
    public function getAuditList($operatorID = null, $landTitle = null, $noticeType = null, $timeType = null, $beginTime = null, $endTime = null,
        $auditStatus = null, $orderNum = null, $page = 1, $limit = 20, $auditType = 0, $landId = 0, $tid = 0,$subSid = 0,array $extCondition  = [],$memberId='')
    {
        //迁移至业务层
        $refundAudit = new \Business\Order\RefundAudit();
        $res = $refundAudit->getAuditList($operatorID, $landTitle, $noticeType, $timeType, $beginTime, $endTime,$auditStatus, $orderNum,
            $page, $limit, $auditType, $landId, $tid, $subSid, $extCondition,$memberId);
        $this->apiReturn($res['code'],$res['data']);
    }

    /**
     * 接口数据返回 - 进行重写
     * <AUTHOR>
     * @DateTime 2016-02-16T13:48:27+0800
     *
     * @param    int                      $code   返回码
     * @param    array                    $data   接口返回数据
     * @param    string                   $msg    错误说明，默认为空
     * @param    boolean                  $object 当data为空时，是否返回空对象
     * @return
     */
    public function apiReturn($code, $data = [], $msg = '', $object = false)
    {
        $code    = intval($code);
        $msgList = load_config('refund_error', 'order');

        if (!$msg && array_key_exists($code, $msgList)) {
            $msg = $msgList[$code];
        }
        if (!$msg) {
            $msg = '';
        }
        exit(json_encode(array(
            "code" => $code,
            "data" => $data,
            "msg"  => $msg,
        ), JSON_UNESCAPED_UNICODE));
    }

    /**
     * 获取特产审核订单的详情
     * <AUTHOR>
     * @date 2020-04-16
     */
    public function getSpecialVerifyDetail()
    {
        $auditId = I('post.audit_id', 0);
        if (!$auditId) {
            $this->apiReturn(204, [], '参数不能为空');
        }
        $refundAuditBiz = new \Business\Order\RefundAudit();
        $res            = $refundAuditBiz->getSpecialVerifyDetailService($auditId);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 更新订单审核时候的运费
     * <AUTHOR>
     * @date 2020-04-16
     */
    public function updateSpecialCarriage()
    {
        $auditId  = I('post.audit_id', 0);
        $carriage = I('post.carriage', 0); //分
        if (!$auditId || $carriage < 0) {
            $this->apiReturn(204, [], '参数不能为空');
        }
        $opId           = $this->operatorID;
        $refundAuditBiz = new \Business\Order\RefundAudit();
        $res            = $refundAuditBiz->updateSpecialCarriageService($auditId, $carriage, $opId);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 获取退票通知配置
     * <AUTHOR>
     * @date   2020-9-15
     *
     * @return
     */
    public function getRefundNoticeConfig()
    {
        $sid       = $this->loginInfo['sid'];
        $refundBuz = new \Business\Order\RefundAudit();
        $result    = $refundBuz->getRefundNoticeConfig($sid);
        if ($result['code'] != 200) {
            $this->apiReturn(400, $result['data'], '失败');
        }
        $this->apiReturn(200, $result['data'], '成功');
    }

    /**
     * 添加修改退票通知配置
     * <AUTHOR>
     * @date   2020-9-15
     *
     * @return
     */
    public function addRefundNoticeConfig()
    {
        $sid         = $this->loginInfo['sid'];
        $mobile      = I('post.mobile','','strval,trim');
        $state       = I('post.state',-1,'intval');
        $notice_type = I('post.notice_type',-1,'intval');

        if ($mobile && !ismobile($mobile)) {
            $this->apiReturn(203, [], '手机号格式错误，请重新输入');
        }
        if (!in_array($state, [0, 1]) || !in_array($notice_type, [0, 1])) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if ($notice_type === 0) {
            $wxMemberModel = new \Model\Wechat\WxMember();
            $openList      = $wxMemberModel->getOpenList($sid, $page = 1, $size = 500);
            if ($openList == null && $state == 1) {
                $this->apiReturn(203, [], '请先绑定微信公众号再进行设置');
            }
        }
        $data        = [
            'fid'         => $sid,
            'mobile'      => $mobile,
            'state'       => $state,
            'notice_type' => $notice_type,
        ];
        $refundModel = new RefundNotice();
        $result      = $refundModel->addNoticeConfig($sid, $data);
        if (!$result) {
            $this->apiReturn(400, $result, '设置失败');
        }
        $this->apiReturn(200, $result, '成功');
    }

    /**
     * 访问权限控制
     * <AUTHOR>
     * @date   2016-09-18
     *
     * @return
     */
    private function _auth()
    {
        //如果是外部调用，还要判断是否登录
        $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo('ajax', false, false);

        $fid = I('get.fid');
        if ($fid) {
            $this->operatorID = $fid;
        } else {
            $sid = $this->loginInfo['sid'];
            if (MemberLoginHelper::isSubMerchantLogin()) {
                $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            }
            $this->operatorID = $sid;
        }
    }

    /**
     * 设置退票申请配置
     */
    public function setRefundApplyConfig(){
        $needRefundApply = I('post.need_refund_apply', 0, 'intval');
        $sid         = $this->loginInfo['sid'];
        $commonConfig = new CommonConfig();
        $result = $commonConfig->setRefundApplyConfig($sid,$needRefundApply);

        if($result['code'] == 200 && $result['msg'] != '没有变更，无需修改'){
            $dataKafka = [
                "metadata" =>"",
                "parentMemberId" => $this->loginInfo['sid'],
                "description" => $needRefundApply ? '退票申请配置修改退票申请理由为必填' : '退票申请配置修改退票申请理由为选填',
                "operator" => $this->loginInfo['memberID'],
                "landId" => 0,
                "createTime"=> self::getMillisecond(),
                "action" => 101,
                "memberId" => $this->loginInfo['memberID']
            ];
            \Library\Kafka\KafkaProducer::push('sub_merchant', 'sub_merchant_log', $dataKafka, strval($sid));
        }
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取退票申请配置
     */
    public function getRefundApplyConfig(){
        $sid         = $this->loginInfo['sid'];
        $commonConfig = new CommonConfig();
        $result = $commonConfig->getRefundApplyConfig($sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    private function getMillisecond()
    {
        list($msec, $sec) = explode(' ', microtime());
        $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

        return $msectime;
    }

    /**
     * 校验是否允许撤销撤改
     * @param $orderNum
     */
    private function judgeRevokeOfAfterSale($orderNum,$leftNum,$revokeNum,$cancelIdxArr){
        $stype = $leftNum == 0 ? 1 : 0;//1为撤销 0为撤改
        $productTag = new \Business\NewJavaApi\AfterSale\AfterSaleRecord();
        $res = $productTag->queryListCodeStateByOrder($orderNum);
        if($res['code'] == 200 && empty($res['data'])){
            //无售后
            return [true,''];
        }
        elseif($res['code'] == 200 && !empty($res['data'])){
            $afterSaleRecord = array_column($res['data'],null,'id');
            if($stype){
                //撤销时 订单中有票若非未售后或者售后关闭时 需提示 订单处于售后申请中或者已完成售后，不可再发起撤销
                //售后状态（0.未售后 1.售后中 2.售后成功 3.售后关闭）
                foreach ($afterSaleRecord as $item){
                    if(in_array($item['afterSaleStatus'],[1,2])){
                        return [false,'订单处于售后申请中或者已完成售后，不可再发起撤销'];
                    }
                }
            }
            else{
                //撤改时 选择的票非未售后或者售后关闭 需提示 所选票券处于售后申请中或者已完成售后，不可发起撤改
                if(empty($cancelIdxArr)){
                    //按顺序退
                    $afterSaleNum = 0;
                    //取售后审核中数量，售后成功，tnum会减少。售后关闭以及审核中tunm不会减少
                    foreach ($afterSaleRecord as $item){
                        if(in_array($item['afterSaleStatus'],[1])){
                            $afterSaleNum++;
                        }
                    }
                    //按顺序撤改时，可撤改数量不足（订单数量—售后中/售后成功的数量）在提交时限制不允许发起
                    //代码逻辑就成为，如果当前剩余数量 - 售后审核中的数量 大于 撤销数量，则不成立
                    if(($leftNum+$revokeNum) - $afterSaleNum > $revokeNum){
                        return [false,'所选数量包含处于售后申请中或者已完成售后，不可发起撤改'];
                    }
                }
                else{
                    foreach ($afterSaleRecord as $item){
                        if(in_array($item['idx'],$cancelIdxArr)){
                            return [false,'所选票券处于售后申请中或者已完成售后，不可发起撤改'];
                        }
                    }
                }
            }
            return [true,''];
        }
        else{
            return [false,'售后接口异常：queryListCodeStateByOrder'];
        }
    }
    
}
