<?php
/**
 * 新版订单查询
 * <AUTHOR>
 * @date   2017-11-6
 */

namespace Controller\Order;

use Business\AnnualCard\Task;
use Business\Authority\DataAuthLimit;
use Business\Authority\DataAuthLogic;
use Business\Authority\StaffCheckAuthBiz;
use Business\Common\CommonConfig as CommonConfigBiz;
use Business\Common\DictPayModeService;
use Business\Member\ScenicCheckUser;
use Business\MemberLogin\MemberLoginHelper;
use Business\Order\OrderAidsSplitQuery;
use Business\Order\OrderProductService;
use Business\Order\OrderSearchExport as OrderSearchExportBiz;
use Business\Product\Get\EvoluteQuery;
use Business\Product\Specialty;
use Business\Product\SubProduct as SubProductBiz;
use Library\Constants\BizCode;
use Library\Constants\MemberConst;
use Library\Exception;
use Model\Member\Member;
use Model\Member\MemberRelationship;
use Model\Member\QueryConditionRecord;
use Model\Order\OrderTools;
use Model\Order\SubOrderQuery\SubOrderSplit;
use Model\Product\Evolute;
use Model\Product\Land;
use Model\Product\Round;
use Model\Product\Ticket;
use Library\Resque\Queue;
use Model\SmsDiy\SmsDiyApply;

class OrderList extends \Library\Controller
{
    private $_orderBusiness;
    private $_isSuper    = false;
    private $_selectType = false;
    private $_loginInfo  = null;
    private $_checkInPtype; //取票状态支持的产品类型

    public function __construct()
    {
        //统一进行初始化，有接口频率控制的业务
        parent::__construct();

        $this->_selectType    = I('post.time_type');
        $this->_orderBusiness = new \Business\Order\OrderList($this->_selectType);
        $this->_isSuper       = $this->isSuper();

        $this->_loginInfo    = $this->getLoginInfo('ajax', false, false);
        $this->_checkInPtype = load_config('print_state_allow_type', 'orderSearch');
    }

    private function getOrderBusiness()
    {
        if (!isset($this->_orderBusiness)) {
            $this->_orderBusiness = new \Business\Order\OrderList($this->_selectType);
        }

        return $this->_orderBusiness;
    }

    private function getOrderModel()
    {
        if (!isset($this->_orderReferModel)) {
            $this->_orderReferModel = new \Model\Order\OrderRefer($this->_selectType);
        }

        return $this->_orderReferModel;
    }

    /**
     * 获取订单列表
     */
    public function getList()
    {
        $code = 200;
        $data = [];
        $msg  = '';

        try {
            $params = I('param.');

            //获取
            $loginInfo  = $this->getLoginInfo();
            $sid        = $loginInfo['sid'];
            $memberType = $loginInfo['dtype'];
            $sdtype     = $loginInfo['sdtype'];

            if ($this->isSuper()) {
                throw new \Exception('身份异常');
            }

            //身份证照片的查询搜索项，只支持供应商和他的员工查询该字段
            if ($sdtype != 0 && isset($params['identity_photo'])) {
                unset($params['identity_photo']); //全部不传或传0，是传1，否传2
            }

            $vacationModeCheck = $this->_vacationModeCheck($params, $sid);
            if (!$vacationModeCheck['result']) {
                throw new \Exception($vacationModeCheck['msg']);
            }

            //记录订单查询
            $queryConditionRecordBiz = new \Business\Member\QueryConditionRecord();
            $queryConditionRecordBiz->recordQueryCondition($loginInfo['memberID'], 1, $params, time());

            if ($memberType == 2) {
                //景区账号数据查询
                $orderRes = $this->_getScenicList($params);
            } else {
                //供应商/分销商/集团账号查询
                $orderRes = $this->_getBusinessList($params);
            }

            $total = $orderRes['total'];
            $list  = $orderRes['list'];

            //将查询参数进行处理，后续批量过期之类等操作需要
            $cryptKey    = 'orderqur';
            $xcryptLib   = new \Library\Business\PFTcrypt($cryptKey);
            $batchParams = $xcryptLib->encrypt(json_encode($params));

            $data = [
                'list'        => $list,
                'batchParams' => $batchParams,
                'total'       => $total,
            ];
        } catch (\Throwable $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 查询景区账号下面的数据
     * <AUTHOR>
     * @date 2020/12/18
     *
     * @param  array $paramArr 查询参数
     *
     * @return array
     */
    private function _getScenicList(array $paramArr)
    {
        //登录信息
        $loginInfo  = $this->getLoginInfo();
        $sid        = $loginInfo['sid'];
        $memberType = $loginInfo['dtype'];
        $account    = $loginInfo['account'];
        $memberId   = $loginInfo['memberID'];

        //景区数据查询
        $paramArr['salerid'] = $account;

        //参数处理
        $res = $this->getOrderBusiness()->handleOrderParam($paramArr, $sid);
        if (empty($res['code'])) {
            throw new \Exception($res['msg']);
        }

        //查询时间段处理
        $timeTypeConf = load_config('time_type', 'orderSearch');
        $selectType   = $paramArr['time_type'];
        if (isset($timeTypeConf[$selectType])) {
            $dbStart = $timeTypeConf[$selectType][0];
            $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
        } else {
            $dbStart = false;
            $dbEnd   = false;
        }

        //预留数组，后期会扩展成数组查询
        $pidArr = $paramArr['pid'] ? [$paramArr['pid']] : false;

        //多票查询，逗号分隔处理
        $paramArr['tid'] = $this->getOrderBusiness()->paramParseCommaSeparated($paramArr['tid']);
        $tidArr          = $paramArr['tid'] ?: false;
        if (is_array($tidArr) && count($tidArr) > 20) {
            throw new \Exception('一次最多可选20个票种，已超过，请重试');
        }

        $orderNumArr = false;
        if (isset($paramArr['ordernum']) && $paramArr['ordernum']) {
            $orderNumArr = is_array($paramArr['ordernum']) ? $paramArr['ordernum'] : [$paramArr['ordernum']];
        }

        if ($paramArr['order_mode'] !== false) {
            $orderModeIn = is_array($paramArr['order_mode']) ? $paramArr['order_mode'] : [$paramArr['order_mode']];
        } else {
            $orderModeIn = false;
        }

        if ($paramArr['pay_mode'] !== false) {
            $pmodeIn = is_array($paramArr['pay_mode']) ? $paramArr['pay_mode'] : [$paramArr['pay_mode']];
        } else {
            $pmodeIn = false;
        }

        if (isset($paramArr['if_print']) && $paramArr['if_print'] !== false && $paramArr['p_type'] !== false && !in_array($paramArr['p_type'],
                $this->_checkInPtype)) {
            throw new \Exception('当前产品类型不支持取票状态搜索');
        }

        //新版本数据查询
        $queryRes = $this->getOrderBusiness()->getScenicListByOrderService($memberId, $sid, $memberType,
            $paramArr['salerid'], $dbStart, $dbEnd, $paramArr['page'], $paramArr['size'], $orderNumArr, $paramArr['ordername'],
            $paramArr['person_id'], $paramArr['userMobileSubject'], $paramArr['order_time_start'], $paramArr['order_time_end'],
            $paramArr['play_time_start'], $paramArr['play_time_end'], $paramArr['dtime_start'], $paramArr['dtime_end'],
            $paramArr['begin_time_start'], $paramArr['begin_time_end'], $paramArr['status'], $paramArr['pay_status'],
            $pmodeIn, $orderModeIn, $paramArr['p_type'], $paramArr['operate_id'], $paramArr['check_resource'], $pidArr, $tidArr,
            $paramArr['order_source'], $paramArr['if_print'], $paramArr['mark'], $paramArr['begin_first_time_start'],
            $paramArr['begin_first_time_end'], $paramArr['p_type_arr'],true, $paramArr['upstreamOrderId'],
            (int)$selectType,$paramArr['afterSaleState'],$paramArr['check_code'],$paramArr['round_id'],
            $paramArr['cancel_time_start'],$paramArr['cancel_time_end'],$paramArr['touristMobileSubject'],
            $paramArr['touristIdentificationCode'], $paramArr['cmbId'], $paramArr['personid'], $paramArr['remotenum'], $paramArr['apiOrder']);

        if ($queryRes['code'] != 200) {
            throw new \Exception($queryRes['msg']);
        }
        if ($queryRes['data']['total'] <= 0) {
            throw new \Exception("没有符合条件的订单");
        }

        $list  = $queryRes['data']['list'];
        $total = $queryRes['data']['total'];

        return ['list' => $list, 'total' => $total];
    }

    /**
     * 供应商/分销商/集团账号查询
     * <AUTHOR>
     * @date 2020/12/18
     *
     * @param  array $paramArr 查询参数
     *
     * @return array
     */
    private function _getBusinessList(array $paramArr)
    {
        //登录信息
        $loginInfo  = $this->getLoginInfo();
        $sid        = $loginInfo['sid'];
        $memberType = $loginInfo['dtype'];
        $memberId   = $loginInfo['memberID'];

        if (MemberLoginHelper::isSubMerchantLogin()){
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $paramArr['sub_sid'] = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        //参数处理
        $res = $this->getOrderBusiness()->handleOrderParam($paramArr, $sid);
        if (empty($res['code'])) {
            throw new \Exception($res['msg']);
        }

        //完整产品类型
        $completePtype = SubProductBiz::encodeTypeAndSubType($paramArr['p_type'], $paramArr['sub_type']);

        //查询时间段处理
        $timeTypeConf = load_config('time_type', 'orderSearch');
        $selectType   = $paramArr['time_type'];
        if (isset($timeTypeConf[$selectType])) {
            $dbStart = $timeTypeConf[$selectType][0];
            $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
        } else {
            $dbStart = false;
            $dbEnd   = false;
        }

        $lidArr = [];
        if ($paramArr['lid']) {
            //直接按选定的景区查询
            $lidArr = [$paramArr['lid']];
        } else {
            //如果有按资源库查询的话
            if ($paramArr['source_id']) {
                $landApi      = new \Business\CommodityCenter\Land();
                $resourceList = $landApi->queryLandMultiQueryByResourceId([$paramArr['source_id']]);
                $lidArr       = array_column($resourceList, 'id');
            }
        }

        if ($memberType == 7) {
            //集团账号
            $orderSearchBusiness = new \Business\Order\OrderSearch();
            $memberArr           = $orderSearchBusiness->getRelationMember($sid);
        } else {
            $memberArr = [$sid];
        }

        //预留数组，后期会扩展成数组查询
        $pidArr = $paramArr['pid'] ? [$paramArr['pid']] : false;

        //多票查询，逗号分隔处理
        $paramArr['tid'] = $this->getOrderBusiness()->paramParseCommaSeparated($paramArr['tid']);
        $tidArr          = $paramArr['tid'] ?: false;
        if (is_array($tidArr) && count($tidArr) > 20) {
            throw new \Exception('一次最多可选20个票种，已超过，请重试');
        }

        //供应商、分销商数组，预留数组，后期会扩展成数组查询
        $sellerIdArr = $paramArr['aid'] ? [$paramArr['aid']] : false;

        if ($paramArr['reseller_id']) {
            //数组转换
            $buyerIdArr = is_array($paramArr['reseller_id']) ? $paramArr['reseller_id'] : [$paramArr['reseller_id']];
        } else {
            $buyerIdArr = false;
        }

        $ordernumArr = false;
        if (isset($paramArr['ordernum']) && $paramArr['ordernum']) {
            $ordernumArr = is_array($paramArr['ordernum']) ? $paramArr['ordernum'] : [$paramArr['ordernum']];
        }

        if ($paramArr['order_mode'] !== false) {
            $ordermodeIn = is_array($paramArr['order_mode']) ? $paramArr['order_mode'] : [$paramArr['order_mode']];
        } else {
            $ordermodeIn = false;
        }

        if ($paramArr['pay_mode'] !== false) {
            $pmodeIn = is_array($paramArr['pay_mode']) ? $paramArr['pay_mode'] : [$paramArr['pay_mode']];
        } else {
            $pmodeIn = false;
        }

        if ($paramArr['if_print'] !== false && $completePtype === false) {
            $paramArr['p_type_arr'] = $this->_checkInPtype;
        }

        if (isset($paramArr['if_print']) && $paramArr['if_print'] !== false && $completePtype !== false && !in_array($completePtype,
                $this->_checkInPtype)) {
            throw new \Exception('当前产品类型不支持取票状态搜索');
        }

        //数据权限过滤
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $dataAuthCondition = $dataAuthLimit->transInOrNotCondition(['lidList' => $lidArr]);
        if ($dataAuthCondition === false) {
            throw new \Exception("没有符合条件的订单");
        }
        $lidArr = $dataAuthCondition['lidList'] ?? false;
        $notLidArr = $dataAuthCondition['notLidList'] ?? false;
        $identityPhoto = $paramArr['identity_photo'] ?? 0;  //身份证照片，0或不传=>不勾选，1=>勾选
        //新版本数据查询
        $queryRes = $this->getOrderBusiness()->getBusinessListByOrderService($memberId, $sid, $memberType,
            $memberArr, $dbStart, $dbEnd, $paramArr['page'], $paramArr['size'], $ordernumArr, $paramArr['ordername'],
            $paramArr['person_id'], $paramArr['userMobileSubject'], $paramArr['order_time_start'], $paramArr['order_time_end'],
            $paramArr['play_time_start'], $paramArr['play_time_end'], $paramArr['dtime_start'], $paramArr['dtime_end'],
            $paramArr['begin_time_start'], $paramArr['begin_time_end'], $paramArr['status'], $paramArr['pay_status'],
            $pmodeIn, $ordermodeIn, $paramArr['p_type'], $paramArr['operate_id'],
            $paramArr['check_resource'], $lidArr, $pidArr, $sellerIdArr, $buyerIdArr, $tidArr,
            $paramArr['order_source'], $paramArr['if_print'], $paramArr['mark'], $paramArr['begin_first_time_start'],
            $paramArr['begin_first_time_end'], $paramArr['p_type_arr'], true, $paramArr['isCombine'], $paramArr['upstreamOrderId'],
            $paramArr['sub_sid'],(int)$selectType,$paramArr['afterSaleState'],$paramArr['check_code'], $notLidArr, $paramArr['sub_type'],
            $paramArr['round_id'],$paramArr['cancel_time_start'],$paramArr['cancel_time_end'], $paramArr['touristMobileSubject'],
            $paramArr['touristIdentificationCode'], $paramArr['cmbId'], $paramArr['personid'], $paramArr['remotenum'], $paramArr['apiOrder'], $identityPhoto);

        if ($queryRes['code'] != 200) {
            throw new \Exception($queryRes['msg']);
        }

        if ($queryRes['data']['total'] <= 0) {
            throw new \Exception("没有符合条件的订单");
        }

        // 套票子票，报团子票也需要做数据权限过滤
        $list = array_map(function ($order) use ($dataAuthLimit) {
            if (!empty($order['team_list'])) {
                $order['team_list'] = array_map(function ($teamOrder) use ($dataAuthLimit) {
                    return $this->formatPackOrder($teamOrder, $dataAuthLimit);
                }, $order['team_list']);
            } else {
                $order = $this->formatPackOrder($order, $dataAuthLimit);
            }
            return $order;
        }, $queryRes['data']['list']);
        $total = $queryRes['data']['total'];

        return ['list' => $list, 'total' => $total];
    }

    protected function formatPackOrder(array $order, \Business\Authority\DataAuthLimit $dataAuthLimit)
    {
        $order['lidLimit'] = $dataAuthLimit->hasLidBeenLimit($order['lid']['id']) ? 1 : 0;
        if (!empty($order['pack_info'])) {
            $order['pack_info'] = array_map(function ($sonOrder) use ($dataAuthLimit) {
                $sonOrder['lidLimit'] = $dataAuthLimit->hasLidBeenLimit($sonOrder['lid']['id']) ? 1 : 0;
                return $sonOrder;
            }, $order['pack_info']);
        }
        return $order;
    }

    /**
     * 获取汇总数据
     */
    public function getTotal()
    {
        $code = 200;
        $msg  = '';
        $data = [
            'total_cost_money' => 0,
            'total_sale_money' => 0,
            'total_order_num'  => 0,
            'total_ticket_num' => 0,
        ];

        try {
            $params = I('param.');

            //获取
            $loginInfo  = $this->getLoginInfo();
            $sid        = $loginInfo['sid'];
            $memberType = $loginInfo['dtype'];
            $account    = $loginInfo['account'];
            $memberId   = $loginInfo['memberID'];

            if ($this->isSuper()) {
                throw new \Exception('身份异常');
            }

            if (MemberLoginHelper::isSubMerchantLogin()){
                $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $params['sub_sid'] = MemberLoginHelper::getLoginBusinessMember()->getSId();
            }

            //参数处理
            $this->getOrderBusiness()->handleOrderParam($params, $sid);

            if ($memberType == 2) {
                //景区账号数据查询
                $params['salerid'] = $account;

                //查询时间段处理
                $timeTypeConf = load_config('time_type', 'orderSearch');
                $selectType   = $params['time_type'];
                if (isset($timeTypeConf[$selectType])) {
                    $dbStart = $timeTypeConf[$selectType][0];
                    $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
                } else {
                    $dbStart = false;
                    $dbEnd   = false;
                }

                //预留数组，后期会扩展成数组查询
                $pidArr = $params['pid'] ? [$params['pid']] : false;

                //多票查询，逗号分隔处理
                $params['tid'] = $this->getOrderBusiness()->paramParseCommaSeparated($params['tid']);
                $tidArr        = $params['tid'] ?: false;
                if (is_array($tidArr) && count($tidArr) > 20) {
                    throw new \Exception('一次最多可选20个票种，已超过，请重试');
                }

                $ordernumArr = false;
                if (isset($params['ordernum']) && $params['ordernum']) {
                    $ordernumArr = is_array($params['ordernum']) ? $params['ordernum'] : [$params['ordernum']];
                }

                if ($params['order_mode'] !== false) {
                    $ordermodeIn = is_array($params['order_mode']) ? $params['order_mode'] : [$params['order_mode']];
                } else {
                    $ordermodeIn = false;
                }

                if ($params['pay_mode'] !== false) {
                    $pmodeIn = is_array($params['pay_mode']) ? $params['pay_mode'] : [$params['pay_mode']];
                } else {
                    $pmodeIn = false;
                }

                $queryRes = $this->getOrderBusiness()->getScenicTotalByOrderService($memberId, $sid, $memberType,
                    $params['salerid'], $dbStart, $dbEnd, $params['page'], $params['size'], $ordernumArr, $params['ordername'],
                    $params['person_id'], $params['userMobileSubject'], $params['order_time_start'], $params['order_time_end'],
                    $params['play_time_start'], $params['play_time_end'], $params['dtime_start'], $params['dtime_end'],
                    $params['begin_time_start'], $params['begin_time_end'], $params['status'], $params['pay_status'],
                    $pmodeIn, $ordermodeIn, $params['p_type'], $params['operate_id'], $params['check_resource'], $pidArr, $tidArr,
                    $params['order_source'], $params['if_print'], $params['mark'], $params['begin_first_time_start'],$params['begin_first_time_end'],
                    $params['upstreamOrderId'],$params['afterSaleState'],$params['check_code'], $params['sub_type'],
                    $params['round_id'],$params['cancel_time_start'] ?? false,$params['cancel_time_end'] ?? false,
                    $params['touristMobileSubject'] ?? false,$params['touristIdentificationCode'] ?? false,
                    $params['cmbId'], $params['personid'], $params['remotenum'], $params['apiOrder']
                );

                $res = $queryRes['data'];
            } else {
                //供应商/分销商/集团账号查询

                //查询时间段处理
                $timeTypeConf = load_config('time_type', 'orderSearch');
                $selectType   = $params['time_type'];
                if (isset($timeTypeConf[$selectType])) {
                    $dbStart = $timeTypeConf[$selectType][0];
                    $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
                } else {
                    $dbStart = false;
                    $dbEnd   = false;
                }

                $lidArr = [];
                if ($params['lid']) {
                    //直接按选定的景区查询
                    $lidArr = [$params['lid']];
                } else {
                    //如果有按资源库查询的话
                    if ($params['source_id']) {
                        //$LandModel    = new \Model\Product\Land();
                        //$resourceList = $LandModel->getListByResourceID($params['source_id']);

                        $landApi      = new \Business\CommodityCenter\Land();
                        $resourceList = $landApi->queryLandMultiQueryByResourceId([$params['source_id']]);
                        $lidArr       = array_column($resourceList, 'id');
                    }
                }

                if ($memberType == 7) {
                    //集团账号
                    $orderSearchBusiness = new \Business\Order\OrderSearch();
                    $memberArr           = $orderSearchBusiness->getRelationMember($sid);
                } else {
                    $memberArr = [$sid];
                }

                //预留数组，后期会扩展成数组查询
                $pidArr = $params['pid'] ? [$params['pid']] : false;

                //多票查询，逗号分隔处理
                $params['tid'] = $this->getOrderBusiness()->paramParseCommaSeparated($params['tid']);
                $tidArr        = $params['tid'] ?: false;
                if (is_array($tidArr) && count($tidArr) > 20) {
                    throw new \Exception('一次最多可选20个票种，已超过，请重试');
                }

                //供应商、分销商数组，预留数组，后期会扩展成数组查询
                $sellerIdArr = $params['aid'] ? [$params['aid']] : false;

                if ($params['reseller_id']) {
                    //数组转换
                    $buyerIdArr = is_array($params['reseller_id']) ? $params['reseller_id'] : [$params['reseller_id']];
                } else {
                    $buyerIdArr = false;
                }

                $ordernumArr = false;
                if (isset($params['ordernum']) && $params['ordernum']) {
                    $ordernumArr = is_array($params['ordernum']) ? $params['ordernum'] : [$params['ordernum']];
                }

                if ($params['order_mode'] !== false) {
                    $ordermodeIn = is_array($params['order_mode']) ? $params['order_mode'] : [$params['order_mode']];
                } else {
                    $ordermodeIn = false;
                }

                if ($params['pay_mode'] !== false) {
                    $pmodeIn = is_array($params['pay_mode']) ? $params['pay_mode'] : [$params['pay_mode']];
                } else {
                    $pmodeIn = false;
                }

                $queryRes = $this->getOrderBusiness()->getBusinessTotalByOrderService($memberId, $sid, $memberType,
                    $memberArr, $dbStart, $dbEnd, $params['page'], $params['size'], $ordernumArr, $params['ordername'],
                    $params['person_id'], $params['userMobileSubject'], $params['order_time_start'], $params['order_time_end'],
                    $params['play_time_start'], $params['play_time_end'], $params['dtime_start'], $params['dtime_end'],
                    $params['begin_time_start'], $params['begin_time_end'], $params['status'], $params['pay_status'],
                    $pmodeIn, $ordermodeIn, $params['p_type'], $params['operate_id'], $params['check_resource'], $lidArr,
                    $pidArr, $sellerIdArr, $buyerIdArr, $tidArr, $params['order_source'], $params['if_print'], $params['mark'],
                    $params['begin_first_time_start'],$params['begin_first_time_end'], $params['upstreamOrderId'],
                    $loginInfo['saccount'], $params['sub_sid'],$params['afterSaleState'],$params['check_code'],$params['sub_type'],$params['round_id'],
                    $params['cancel_time_start'] ?? false,$params['cancel_time_end'] ?? false,
                    $params['touristMobileSubject'] ?? false,$params['touristIdentificationCode'] ?? false,
                    $params['cmbId'], $params['personid'], $params['remotenum'], $params['apiOrder']
                );

                $res = $queryRes['data'];
            }
            if (200 != $queryRes['code']) {
                throw new \Exception($queryRes['msg']);
            }

            ////DB交互获取数据
            //$list = $this->getOrderModel()->getTotal($currentFid, $params['ordernum'], $params['ordername'],
            //    $params['person_id'], $params['tel'],
            //    $params['order_time_start'], $params['order_time_end'], $params['play_time_start'],
            //    $params['play_time_end'], $params['dtime_start'],
            //    $params['dtime_end'], $params['begin_time_start'], $params['begin_time_end'], $params['status'],
            //    $params['pay_status'], $params['pay_mode'],
            //    $params['order_mode'], $params['p_type'], $params['operate_id'], $params['check_resource'],
            //    $params['lid'], $params['pid'], $params['aid'],
            //    $params['reseller_id'], $params['salerid'], $params['tid'], $params['order_source']);
            //
            //if (empty($list)) {
            //    throw new \Exception("没有符合条件的订单..");
            //}
            //
            //$res = $this->getOrderBusiness()->handleOrderTotal($currentFid, $list);

            //查看汇总和订单列表中的买入卖出金额是通过同一个方法计算的，所以在这里单独处理为0，modify by zhangyangzhen 2019-03-28
            if ($memberType == 2) {
                $res['total_cost_money'] = 0;
            }

            //总和
            $data = [
                'total_cost_money' => $res['total_cost_money'],
                'total_sale_money' => $res['total_sale_money'],
                'total_order_num'  => $res['total_order_num'],
                'total_ticket_num' => $res['total_ticket_num'],
            ];
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    // 旧版本
    ///**
    // * 获取订单列表
    // */
    //public function getList()
    //{
    //    $code = 200;
    //    $data = [];
    //    $msg  = '';
    //
    //    try {
    //        $params = I('param.');
    //
    //        //获取
    //        $loginInfo  = $this->getLoginInfo();
    //        $sid        = $loginInfo['sid'];
    //        $memberType = $loginInfo['dtype'];
    //        $account    = $loginInfo['account'];
    //        $currentFid = $loginInfo['sid'];
    //
    //        //判断身份 取当前符合要求的会员id集合
    //        if ($this->isSuper()) {
    //            //管理员
    //            $currentFid = 1;
    //        } elseif ($memberType == 7) {
    //            //集团账号
    //            $orderSearchBusiness = new \Business\Order\OrderSearch();
    //            $currentFid          = $orderSearchBusiness->getRelationMember($sid);
    //        } elseif ($memberType == 2) {
    //            //景区账号
    //            $params['salerid'] = $account;
    //        }
    //
    //        //参数处理
    //        $res = $this->getOrderBusiness()->handleOrderParam($params, $sid);
    //        if (empty($res['code'])) {
    //            throw new \Exception($res['msg']);
    //        }
    //
    //        $vacationModeCheck = $this->_vacationModeCheck($params, $sid);
    //        if (!$vacationModeCheck['result']) {
    //            throw new \Exception($vacationModeCheck['msg']);
    //        }
    //
    //        //DB交互获取数据
    //        $res = $this->getOrderModel()->getList($currentFid, $params['ordernum'], $params['ordername'],
    //            $params['person_id'], $params['tel'],
    //            $params['order_time_start'], $params['order_time_end'], $params['play_time_start'],
    //            $params['play_time_end'],
    //            $params['dtime_start'], $params['dtime_end'], $params['begin_time_start'], $params['begin_time_end'],
    //            $params['status'], $params['pay_status'], $params['pay_mode'], $params['order_mode'], $params['p_type'],
    //            $params['operate_id'], $params['check_resource'], $params['lid'], $params['pid'], $params['aid'],
    //            $params['reseller_id'],
    //            $params['salerid'], $params['tid'], $params['page'], $params['size'], $params['excel'], true,
    //            $params['source_id'], $params['order_source'], $params['if_print'], $params['mark']);
    //
    //        //列表
    //        $list = $res['list'];
    //        if (empty($list)) {
    //            throw new \Exception("没有符合条件的订单..");
    //        }
    //
    //        //总条数
    //        $total = $res['total'];
    //
    //        //数据处理
    //        $list = $this->getOrderBusiness()->handleOrderResult($list, $sid, $currentFid, $memberType, 'pc');
    //
    //        //特殊
    //        $batchParams = '';
    //        if (!$this->_isSuper) {
    //            //管理员没有权限
    //            //将查询参数进行处理
    //            $cryptKey    = 'orderqur';
    //            $xcryptLib   = new \Library\Business\PFTcrypt($cryptKey);
    //            $batchParams = $xcryptLib->encrypt(json_encode($params));
    //        }
    //        $data = [
    //            'list'        => $list,
    //            'batchParams' => $batchParams,
    //            'total'       => $total,
    //        ];
    //    } catch (\Exception $e) {
    //        $code = 400;
    //        $msg  = $e->getMessage();
    //    }
    //
    //    $this->apiReturn($code, $data, $msg);
    //}

    // 旧版本
    ///**
    // * 获取汇总数据
    // */
    //public function getTotal()
    //{
    //    $code = 200;
    //    $msg  = '';
    //    $data = [
    //        'total_cost_money' => 0,
    //        'total_sale_money' => 0,
    //        'total_order_num'  => 0,
    //        'total_ticket_num' => 0,
    //    ];
    //
    //    try {
    //        $params = I('param.');
    //
    //        //获取
    //        $loginInfo  = $this->getLoginInfo();
    //        $sid        = $loginInfo['sid'];
    //        $memberType = $loginInfo['dtype'];
    //        $account    = $loginInfo['account'];
    //        $currentFid = $loginInfo['sid'];
    //
    //        //判断身份 取当前符合要求的会员id集合
    //        if ($this->isSuper()) {
    //            //管理员
    //            $currentFid = 1;
    //        } elseif ($memberType == 7) {
    //            //集团账号
    //            $orderSearchBusiness = new \Business\Order\OrderSearch();
    //            $currentFid          = $orderSearchBusiness->getRelationMember($sid);
    //        } elseif ($memberType == 2) {
    //            //景区账号
    //            $params['salerid'] = $account;
    //        }
    //
    //        //参数处理
    //        $this->getOrderBusiness()->handleOrderParam($params, $sid);
    //
    //        //临时加上个日志
    //        pft_log('deubg/orderquery', json_encode([
    //            'ac'         => 'getTotal',
    //            'currentFid' => $currentFid,
    //            'params'     => $params,
    //        ]));
    //        //DB交互获取数据
    //        $list = $this->getOrderModel()->getTotal($currentFid, $params['ordernum'], $params['ordername'],
    //            $params['person_id'], $params['tel'],
    //            $params['order_time_start'], $params['order_time_end'], $params['play_time_start'],
    //            $params['play_time_end'], $params['dtime_start'],
    //            $params['dtime_end'], $params['begin_time_start'], $params['begin_time_end'], $params['status'],
    //            $params['pay_status'], $params['pay_mode'],
    //            $params['order_mode'], $params['p_type'], $params['operate_id'], $params['check_resource'],
    //            $params['lid'], $params['pid'], $params['aid'],
    //            $params['reseller_id'], $params['salerid'], $params['tid'], $params['order_source']);
    //
    //        if (empty($list)) {
    //            throw new \Exception("没有符合条件的订单..");
    //        }
    //
    //        $res = $this->getOrderBusiness()->handleOrderTotal($currentFid, $list);
    //
    //        //查看汇总和订单列表中的买入卖出金额是通过同一个方法计算的，所以在这里单独处理为0，modify by zhangyangzhen 2019-03-28
    //        if ($memberType == 2) {
    //            $res['total_cost_money'] = 0;
    //        }
    //
    //        //总和
    //        $data = [
    //            'total_cost_money' => $res['total_cost_money'],
    //            'total_sale_money' => $res['total_sale_money'],
    //            'total_order_num'  => $res['total_order_num'],
    //            'total_ticket_num' => $res['total_ticket_num'],
    //        ];
    //
    //    } catch (\Exception $e) {
    //        $code = 400;
    //        $msg  = $e->getMessage();
    //    }
    //
    //    $this->apiReturn($code, $data, $msg);
    //}

    /**
     * 获取详情
     */
    public function getDetail()
    {
        $code = 200;
        $data = [];
        $msg  = '';

        try {
            $orderId = I('post.ordernum', '', 'strval');
            //是否获取入园记录
            $getPlayRecord = boolval(I('post.get_play_record', 0));

            if (empty($orderId)) {
                throw new \Exception("订单号不能为空");
            }

            //获取
            $loginInfo  = $this->getLoginInfo();
            $sid        = $loginInfo['sid'];
            $memberType = $loginInfo['dtype'];
            $account    = $loginInfo['account'];
            $currentFid = $loginInfo['sid'];
            $userId     = $loginInfo['memberID'];
            $subSid     = 0;
            if (MemberLoginHelper::isSubMerchantLogin()){
                $userId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $currentFid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
            }

            //判断身份 取当前符合要求的会员id集合
            $salerId = false;
            if ($this->isSuper()) {
                //管理员
                $currentFid = 1;
            } elseif ($memberType == 7) {
                //集团账号
                $orderSearchBusiness = new \Business\Order\OrderSearch();
                $currentFid          = $orderSearchBusiness->getRelationMember($sid);
            } elseif (in_array($memberType, [2, 3])) {
                //景区账号
                $salerId    = $account;
                $orderTools = new OrderTools();
                $orderInfo  = $orderTools->getOrderInfo($orderId, 'salerid');

                if ($salerId != $orderInfo['salerid']) {
                    throw new \Exception('无权查看此订单');
                }
            } else {
                //对当前查询订单的用户做权限校验
                //$chainModel = new SubOrderSplit();
                //$orderChain = $chainModel->getListByOrderSingle($orderId, 'id,buyerid,sellerid');

                //订单查询迁移三期
                $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
                $orderChain             = $orderAidsSplitQueryLib->getListByOrderSingleNew(strval($orderId));

                if (empty($orderChain)) {
                    throw new \Exception('订单分销链数据不存在');
                }

                $buyerid  = array_column($orderChain, 'buyerid');
                $sellerid = array_column($orderChain, 'sellerid');
                $members  = array_unique(array_merge($buyerid, $sellerid));

                if ($currentFid != 1 && !in_array($currentFid, $members)) {
                    throw new \Exception('无权查看此订单');
                }
            }

            $data                 = $this->getOrderBusiness()->handleOrderDetail($orderId, $currentFid, $memberType,
                $sid, $getPlayRecord, $userId, $subSid);
            //数据权限过滤
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
            if (!empty($data['land_module'][0]['lid']) && $dataAuthLimit->hasLidBeenLimit($data['land_module'][0]['lid'])) {
                $this->apiReturn(403, [], '无权查看此订单');
            }
            $scenicCheckUser      = new ScenicCheckUser();
            $data['is_chk_white'] = $scenicCheckUser->judgeChkWhite($loginInfo['account'], $loginInfo['saccount']);
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 获取查询条件配置信息
     */
    public function getSearchConfig()
    {
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        //订单状态
        $orderState = load_config('order_status', 'orderSearch');
        //支付状态
        $payState = load_config('pay_status', 'orderSearch');
        //支付方式
        // $payWay = load_config('pay_mode_two', 'orderSearch');
        $payWay = DictPayModeService::getInstance()->orderSearchPayModeTwoConf($sid, true);
        //产品类型
        $productType = load_config('land_type', 'orderSearch');
        //销售渠道
        $saleChannel = load_config('order_mode_two', 'orderSearch');
        //验证方式
        $validationWay = load_config('validation_way', 'orderSearch');
        //取票状态
        $printState = load_config('print_state', 'orderSearch');

        $validationWayNew = [];
        foreach ($validationWay as $key => $item) {
            $validationWayNew[$key] = $item['name'];
        }
        //分库下单时间区间的配置
        $timeType = load_config('time_type', 'orderSearch');

        //分销商分组
        $distributorGroup = [];

        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $groupRes           = $getEvoluteGroupBiz->getFidCountBySid($sid, false, false);
        $group              = [];
        if ($groupRes['code'] == 200 && !empty($groupRes['data'])) {
            $group = $groupRes['data'];
        }

        if (is_array($group) && !empty($group)) {
            foreach ($group as $item) {
                $distributorGroup[$item['id']] = $item['name'];
            }
        }

        //下单员工
        $bookOrderStaff = [];
        $memberModel    = new \Model\Member\Member();
        $staffList      = $memberModel->getStaffByParentId($sid);
        if (is_array($staffList) && !empty($staffList)) {
            $sonIdArr  = array_column($staffList, 'son_id');
            $staffInfo = $memberModel->getMemberInfoByMulti($sonIdArr, 'id', 'id, dname');
            foreach ($staffInfo as $item) {
                $bookOrderStaff[$item['id']] = $item['dname'];
            }
        }

        //产品
        $products = [];

//        $evoluteQueryLib = new EvoluteQuery();
//        $evoluteInfo     = $evoluteQueryLib->queryLandTitleList(intval($sid));
//        if ($evoluteInfo['code'] != 200 || empty($evoluteInfo['data']['list'])) {
//            $evoluteInfo = [];
//        } else {
//            $evoluteInfo = $evoluteInfo['data']['list'];
//        }

//        if (is_array($evoluteInfo) && !empty($evoluteInfo)) {
//            foreach ($evoluteInfo as $item) {
//                $products[$item['id']] = $item['title'];
//            }
//        }

        //产品资源ID登入，订单查询能按票类筛选查询数据
//        if (empty($evoluteInfo) && $loginInfo['dtype'] == 2) {
//            $landApi     = new \Business\CommodityCenter\Land();
//            $landInfoDir = $landApi->queryLandMultiQueryBySalerid([$loginInfo['account']])[0];
//            if ($landInfoDir) {
//                $products[$landInfoDir['id']] = $landInfoDir['title'];
//            }
//        }


        //场次
        $rounds = [
            1  => '场次ID:1',
            2  => '场次ID:2',
            3  => '场次ID:3',
            4  => '场次ID:4',
            5  => '场次ID:5',
            6  => '场次ID:6',
            7  => '场次ID:7',
            8  => '场次ID:8',
            9  => '场次ID:9',
            10 => '场次ID:10',
            11 => '场次ID:11',
            12 => '场次ID:12',
            13 => '场次ID:13',
            14 => '场次ID:14',
            15 => '场次ID:15',
        ];
        //供应商对订单标记
        $mark = [
//            -1 => '未标记',
            1 => '红',
            2 => '绿',
            3 => '蓝',
        ];

        // 订单来源
        $orderSource = [2 => '资源中心'];

        //登录人的类型
        $loginInfo = $this->getLoginInfo();
        $loginType = $loginInfo['sdtype'];

        $data = [
            'order_state'       => $orderState,
            'pay_state'         => $payState,
            'pay_way'           => $payWay,
            'product_type'      => $productType,
            'sale_channel'      => $saleChannel,
            'validation_way'    => $validationWayNew,
            'distributor_group' => $distributorGroup,
            'book_order_staff'  => $bookOrderStaff,
            'time_type'         => $timeType,
            'lid'               => $products,
            'round_name'        => $rounds,
            'login_type'        => $loginType,
            'order_source'      => $orderSource,
            'if_print'          => $printState,
            'mark'              => $mark,
            'annual_task_type'  => (object)Task::TASK_MAP_TITLE,
        ];

        //是否开启假日模式
        $vacationBiz = new \Business\PftSystem\VacationModeBiz();
        $open        = $vacationBiz->judgeForPage('orderQuery', $this->_loginInfo['saccount']);

        if ($open === false) {
            $data['in_vacation'] = 1;
        } else {
            $data['in_vacation'] = 0;
        }

        $this->apiReturn(200, $data, '');
    }

    /**
     * 获取查询条件的可变信息
     */
    public function getSearchConfigOther()
    {
        //搜索类型  1 产品  2 票类  3 供应商  4 分销商
        $type  = I('post.type');
        $ptype = I('post.p_type');  //产品类型
        //搜索的关键字
        $keyWord  = I('post.key_word');
        $isOnline = I('post.is_online', 0);  //0 全部  1 只获取已上架门票
        $filterType = I('post.filter_type', 0, 'intval');

        if (empty($keyWord)) {
            $this->apiReturn(200, []);
        }

        $data      = [];
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];

        switch ($type) {
            case 1:
                $status       = I('status', 0, 'intval');
                $evoluteModel = new Evolute();
                if (mb_strlen($keyWord) == strlen($keyWord)) {
                    //英文
                    $searchType = 2;
                } else {
                    //汉字
                    $searchType = 1;
                }
                //产品id
                if (is_numeric($keyWord)) {
                    $searchType = 3;
                }
                $evoluteInfo = $evoluteModel->getInfoByFidAndLandKeyTitle($sid, $keyWord, $searchType, $ptype,
                    'u.id, u.title, u.p_type', false, $status);
                if (empty($evoluteInfo) && $loginInfo['dtype'] == 2){
                    $landApi = new \Business\CommodityCenter\Land();
                    $res     = $landApi->queryLandMultiQueryBySalerid([$loginInfo['account']], $keyWord);
                    if (!empty($res)){
                        $evoluteInfo[] = [
                            'id' => $res[0]['id'],
                            'title' => $res[0]['title']
                        ];
                    }
                }
                foreach ($evoluteInfo as $item) {
                    $data[$item['id']] = $item['title'];
                }
                break;

            case 2:
                //景区ID
                $lid          = I('post.land_id');
                $evoluteModel = new Evolute();

                $evoluteInfo = $evoluteModel->getInfoByFidAndLandIdAndKeyTitle($sid, $lid, $keyWord);
                foreach ($evoluteInfo as $item) {
                    $data[$item['id']] = $item['title'];
                }
                break;

            case 3:

                if (mb_strlen($keyWord) == strlen($keyWord)) {
                    //英文
                    $searchType = 2;
                } else {
                    //汉字
                    $searchType = 1;
                }
                $memberRelationShipModel = new MemberRelationship();
                $memberRelationShipInfo  = $memberRelationShipModel->getResellerInfoByFidAndTitle($sid, $keyWord,
                    true, $searchType);

                $data = $memberRelationShipInfo;
                break;
            case 4:
                if (mb_strlen($keyWord) == strlen($keyWord)) {
                    //英文
                    $searchType = 2;
                } else {
                    //汉字
                    $searchType = 1;
                }
                $memberRelationShipModel = new MemberRelationship();
                $memberRelationShipInfo  = $memberRelationShipModel->getResellerInfoByFidAndTitle($sid, $keyWord,
                    false, $searchType, true);

                $data = $memberRelationShipInfo;
                break;
            case 5:
                $keyWord = intval($keyWord);//keyWord是景区id
                if (!empty($keyWord)) {
                    //产品资源ID登入，订单查询能按票类筛选查询数据
                    if ($loginInfo['dtype'] == 2) {
                        // 资源方账号登录，获取供应商ID
                        //$landModel   = new Land('slave');
                        //$landInfoDir = $landModel->getLandInfoBySalerId($loginInfo['account'], false, 'apply_did');

                        $landApi     = new \Business\CommodityCenter\Land();
                        $landInfoDir = $landApi->queryLandMultiQueryBySalerid([$loginInfo['account']])[0];
                        if ($landInfoDir) {
                            $sid = $landInfoDir['apply_did'];
                        }
                    }

                    //根据产品id联动票类
                    $ticketsModel = new Evolute();
                    //只保留售出不激活的
                    $extParams = $filterType == 1 ? ['book_inactive' => true] : [];
                    $evoluteInfo = $ticketsModel->getTicketInfoByFidAndLandId($sid, $keyWord, '', $isOnline, $extParams);
                    if (is_array($evoluteInfo) && !empty($evoluteInfo)) {
                        foreach ($evoluteInfo as $item) {
                            $data[$item['id']] = $item['title'];
                        }
                    }

                }
                break;
            case 6:
                //非管理员分页获取产品
                $page = intval(I('post.page'));
                if ($page <= 0) {
                    $page = 1;
                }

                $evoluteQueryLib = new EvoluteQuery();
                $evoluteInfo     = $evoluteQueryLib->queryLandTitleList(intval($sid), $page);
                if ($evoluteInfo['code'] != 200 || empty($evoluteInfo['data']['list'])) {
                    $evoluteInfo = [];
                } else {
                    $evoluteInfo = $evoluteInfo['data']['list'];
                }

                if (is_array($evoluteInfo) && !empty($evoluteInfo)) {
                    foreach ($evoluteInfo as $item) {
                        $data[$item['id']] = $item['title'];
                    }
                }
                break;
        }

        $this->apiReturn(200, $data);
    }

    public function listProduct()
    {
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        $name = I('post.name', '', 'strval');

        $result = ['data' => []];
        if ($loginInfo['dtype'] == 2){
            $landApi = new \Business\CommodityCenter\Land();
            $res     = $landApi->queryLandMultiQueryBySalerid([$loginInfo['account']], $name);
            if (!empty($res)){
                $result['data'][] = [
                    'id' => $res[0]['id'],
                    'name' => $res[0]['title']
                ];
            }
        }else{
            if (MemberLoginHelper::isSubMerchantLogin()){
                $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            }
            //数据权限过滤
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
            $condition = $dataAuthLimit->transInOrNotCondition();
            // 全禁的情况下不需要调用接口
            if ($condition === false) {
                $this->apiReturn(self::CODE_SUCCESS);
            }
            $orderProductServiceBz = new OrderProductService();
            $result = $orderProductServiceBz->listProduct($sid, $name, 1, 1000, $condition);
        }
        $data = array_column($result['data'], 'name', 'id');

        $this->apiReturn(200, $data);
    }

    public function listTicket()
    {
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        $lid = I('post.lid', 0, 'intval');
        $name = I('post.name', '', 'strval');

        if (empty($lid)) {
            $this->apiReturn(parent::CODE_PARAM_ERROR, [], 'lid不能为空');
        }

        $data = [];
        //产品资源ID登入，订单查询能按票类筛选查询数据
        if ($loginInfo['dtype'] == 2) {
            $landApi     = new \Business\CommodityCenter\Land();
            $landInfoDir = $landApi->queryLandMultiQueryBySalerid([$loginInfo['account']])[0];
            if ($landInfoDir) {
                $sid = $landInfoDir['apply_did'];
            }
            //根据产品id联动票类
            $ticketsModel = new Evolute();

            $evoluteInfo = $ticketsModel->getTicketInfoByFidAndLandId($sid, $lid, $name, 0);
            if (is_array($evoluteInfo) && !empty($evoluteInfo)) {
                foreach ($evoluteInfo as $item) {
                    $data[$item['id']] = $item['title'];
                }
            }
        }else{
            $orderProductServiceBz = new OrderProductService();
            $result = $orderProductServiceBz->listTicket($sid, $name, $lid);
            $data = array_column($result['data'], 'name', 'id');
        }

        $this->apiReturn(200, $data);
    }

    public function listSeller()
    {
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        $name = I('post.name', '', 'strval');

        $orderProductServiceBz = new OrderProductService();
        $result = $orderProductServiceBz->listSeller($sid, $name);

        $this->apiReturn(200, $result['data']);
    }

    public function listBuyer()
    {
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        $name = I('post.name', '', 'strval');

        $orderProductServiceBz = new OrderProductService();
        $result = $orderProductServiceBz->listBuyer($sid, $name);

        $this->apiReturn(200, $result['data']);
    }

    public function listSubSupplier()
    {
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        $name      = I('post.name', '', 'strval');
        $page      = I('post.page', 1, 'intval');
        $size      = I('post.size', 1000, 'intval');//前端是旧组件，不支持分页

        $orderProductServiceBz = new OrderProductService();
        $result                = $orderProductServiceBz->listSubSupplier($sid, $name, $page, $size);

        $this->apiReturn(200, $result['data']?:[]);
    }

    /**
     * 获取指定用户下的子商户列表
     *
     * @date 2024/02/23
     * @auther yangjianhui
     * @return array
     */
    public function getSubMemberListPage()
    {
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        $name      = I('post.name', '', 'strval');
        $page      = I('post.page', 1, 'intval');
        $size      = I('post.size', 10, 'intval');//前端是旧组件，不支持分页

        $orderProductServiceBz = new OrderProductService();
        $result                = $orderProductServiceBz->getSubMemberListPage($sid, $name, $page, $size);

        $this->apiReturn(200, $result['data']?:[]);
    }

    /**
     * 获取订单票属性快照
     * <AUTHOR>
     * @date   2019-12-20
     */
    public function getOrderTicketSnapshot()
    {
        $ordernum = I('ordernum', '', 'strval');
        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }

        $sid = $this->isLogin();
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $result = $this->_orderBusiness->getOrderTicketSnapshot($sid, $ordernum);
        //数据权限过滤
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        if (!empty($result['data']['ticket']['land_id']) && $dataAuthLimit->hasLidBeenLimit($result['data']['ticket']['land_id'])) {
            $this->apiReturn(403, [], '无权查看此订单');
        }
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常，请重试');
        }

    }

    /**
     * 特产订单确认收货
     * @author: zhangyz
     * @date: 2020/6/12
     */
    public function confirmReceipt()
    {
        $loginInfo = $this->getLoginInfo();
        $mid       = $loginInfo['sid'];
        $memberType = $loginInfo['dtype'];
        $memberId = $loginInfo['memberID'];

        $ordernum = I('post.ordernum', '', 'strval,trim');

        if (!$ordernum) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        if($memberType == MemberConst::ROLE_STAFF) {
            if(!StaffCheckAuthBiz::getInstance()->hasOrderCheckPermission($memberId, $ordernum)){
                $this->apiReturn(BizCode::CODE_AUTH_ERROR, [], '该员工没有此订单的核销权限');
            }
        }

        $specialBiz = new Specialty();

        $result = $specialBiz->confirmReceipt($mid, $ordernum);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 订单查询假日模式限制
     * @author: xiexy
     * @date: 2020/09/23
     *
     */
    private function _vacationModeCheck(array $orderParams, int $sid)
    {
        $limitTimeStamp = 3600 * 24 * 14;
        $limitMsg       = '当前处于假日模式，最大查询14天数据';

        $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('orderquery',
            $this->_loginInfo['saccount']);

        if ($vacationMode === true) {
            return ['result' => true, 'msg' => ''];
        }

        if ($orderParams['order_time_end'] && $orderParams['order_time_start']) {
            $orderTimeEnd   = strtotime($orderParams['order_time_end']);
            $orderTimeStart = strtotime($orderParams['order_time_start']);

            if ($orderTimeEnd - $orderTimeStart > $limitTimeStamp) {
                return ['result' => false, 'msg' => $limitMsg];
            }
        }

        if ($orderParams['dtime_start'] && $orderParams['dtime_end']) {
            $orderTimeEnd   = strtotime($orderParams['dtime_end']);
            $orderTimeStart = strtotime($orderParams['dtime_start']);

            if ($orderTimeEnd - $orderTimeStart > $limitTimeStamp) {
                return ['result' => false, 'msg' => $limitMsg];
            }
        }

        if ($orderParams['begin_time_end'] && $orderParams['begin_time_start']) {
            $orderTimeEnd   = strtotime($orderParams['begin_time_end']);
            $orderTimeStart = strtotime($orderParams['begin_time_start']);

            if ($orderTimeEnd - $orderTimeStart > $limitTimeStamp) {
                return ['result' => false, 'msg' => $limitMsg];
            }
        }

        if ($orderParams['play_time_end'] && $orderParams['play_time_start']) {
            $orderTimeEnd   = strtotime($orderParams['play_time_start']);
            $orderTimeStart = strtotime($orderParams['play_time_end']);

            if ($orderTimeEnd - $orderTimeStart > $limitTimeStamp) {
                return ['result' => false, 'msg' => $limitMsg];
            }
        }

        //取消时间cancel_time_start,cancel_time_end
        if ($orderParams['cancel_time_end'] && $orderParams['cancel_time_start']) {
            $cancelTimeStart = strtotime($orderParams['cancel_time_start']);
            $cancelTimeEnd   = strtotime($orderParams['cancel_time_end']);
            if ($cancelTimeEnd - $cancelTimeStart > $limitTimeStamp) {
                return ['result' => false, 'msg' => $limitMsg];
            }
        }

        return ['result' => true, 'msg' => ''];
    }

    /**
     * 查询条件记录
     * <AUTHOR>
     * @date 2021/10/14
     *
     * @return array
     */
    public function queryConditionRecordList()
    {
        $loginInfo = $this->getLoginInfo();
        $memberID  = $loginInfo['memberID'];
        $queryConditionRecordBiz = new \Business\Member\QueryConditionRecord();
        $list = $queryConditionRecordBiz->queryRecordList($memberID, 1);
        $this->apiReturn(200, ['list' => $list], '');
    }

    /**
     * 重发代发
     * <AUTHOR>
     * @date 2021/12/29
     *
     * @return array
     */
    public function agencySend()
    {
        $ordernum = I('post.ordernum', '', 'strval');
        $mobile   = I('post.mobile', '', 'strval');


        $queryParams      = [[$ordernum]];
        $mainOrderInfoArr = \Business\JavaApi\Order\Query\Container::query('orderQuery', 'completeOrder', $queryParams);
        if ($mainOrderInfoArr['code'] != 200 || empty($mainOrderInfoArr['data'])) {
            $this->apiReturn(203, [], '参数错误!');
        }

        if ($mainOrderInfoArr['data'][0]['remsg'] >= 2) {
            $this->apiReturn(203, [], '短信重发次数不能超过2次!');
        }

        $applyDid = $mainOrderInfoArr['data'][0]['addon']['apply_did'];
        $tids[] = $mainOrderInfoArr['data'][0]['tid'];
        $orderOpenBiz = new \Business\Ota\Open\Order();
        $result = $orderOpenBiz->getThirdTicketExtInfo($tids);

        if ($result['code'] != 200 || empty($result['data'])) {
            $this->apiReturn(203, [], '代发配置不存在');
        }
        $ticketExArr = $result['data'];
        if ($ticketExArr['send_type'] == 1) {
            $this->apiReturn(203, [], '不代发短信');
        }

        $resultRes = $orderOpenBiz->getThirdOrderInfo([$ordernum]);
        if ($resultRes['code'] != 200 || empty($resultRes['data'])) {
            $this->apiReturn(203, [], '获取三方订单信息失败');
        }
        $thirdOrderInfo = $resultRes['data'][0];

        if (empty($thirdOrderInfo['codeInfo']) || empty($thirdOrderInfo['codeInfo']['apiCode'])) {
            $this->apiReturn(203, [], '没有三方凭证码无需发送');
        }
        $appid = $thirdOrderInfo['appId'];
        $apiCode = implode(',', $thirdOrderInfo['codeInfo']['apiCode']);

        $template         = '';
        $smsModel         = new SmsDiyApply();
        $applyingTemplate = $smsModel->getSmsInfoById($ticketExArr['template_id']);
        if (!empty($applyingTemplate['content']) && $applyingTemplate['use_state'] == 1 && $applyingTemplate['is_delete'] != 1) {
            $template = $applyingTemplate['content'];
        }

        $smsParams = [
            'appId'       => $appid,
            'orderNum'    => $ordernum,
            'apiCode'     => $apiCode,
            'sendSms'     => 2,
            'applyDid'    => $applyDid,
            'apiQrCode'   => '',
            'isAgentSend' => true,
            'template'    => $template,
            'isResend'    => true,
            'mobile'      => $mobile
        ];

        Queue::push('OtaSms', 'OtaSms_Job', $smsParams);
        $this->apiReturn(200, [], '');
    }

    /**
     * 获取展示配置信息
     * <AUTHOR>
     * @date   2024/09/04
     *
     */
    public function getDisplayConfig()
    {
        //获取
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        $memberId  = $loginInfo['memberID'];

        $tagKey = CommonConfigBiz::ORDER_SEARCH_ID_ACCOUNT_SHOW;

        $result = (new CommonConfigBiz())->getUserConfig($sid, $memberId, $tagKey);

        !empty($result['data']) && $result['data'] = array_map('intval', $result['data']);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 设置展示配置信息
     * <AUTHOR>
     * @date   2024/09/04
     *
     */
    public function setDisplayConfig()
    {
        $params = I('post.');
        if (!isset($params['is_show']) || !is_numeric($params['is_show']) || !in_array($params['is_show'], [0, 1])) {
            $this->apiReturn(203, [], '参数错误');
        }

        $isShow = $params['is_show'];

        //获取
        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        $memberId  = $loginInfo['memberID'];

        $tagKey = CommonConfigBiz::ORDER_SEARCH_ID_ACCOUNT_SHOW;

        $tagVal = ['is_show' => $isShow];

        $result = (new CommonConfigBiz())->setUserConfig($sid, $memberId, $tagKey, $tagVal);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 订单查询导出excel
     * <AUTHOR>
     * @date   2025/02/19
     *
     */
    public function exportExcel()
    {
        $request = I('post.');

        $loginInfo = $this->getLoginInfo();
        $sid       = $loginInfo['sid'];
        $memberId  = $loginInfo['memberID'];
        $account   = $loginInfo['account'];
        $dtype     = $loginInfo['dtype'];
        $sdtype    = $loginInfo['sdtype'];

        if (!extension_loaded('qconf')) {
            //不存在，默认走旧的
            (new \Controller\MassData\ExportListen())->Judge();

            return;
        }
        $whitelist = \qconf::getConf("/php/platform/order_search_new_export_whitelist");
        $whitelist = json_decode($whitelist, true);
        $whitelist = $whitelist ?: [6970, 3385];
        if (!in_array($sid, $whitelist) && !in_array("*", $whitelist)) {
            //不存在，默认走旧的
            (new \Controller\MassData\ExportListen())->Judge();

            return;
        }

        // 假日模式管控
        $vocationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('Judge', $loginInfo['saccount']);
        if ($vocationMode === false) {
            $this->apiReturn(401, [], '当前处于假日模式，该功能被限制使用');
        }

        $result = (new OrderSearchExportBiz())->createExportTask($request, $sid, $memberId, $sdtype, $dtype, $account);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}
