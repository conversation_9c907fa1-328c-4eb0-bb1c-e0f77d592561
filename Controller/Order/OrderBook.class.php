<?php
/**
 * 订单预定页
 * Created by PhpStorm.
 * User: luo<PERSON>hen <PERSON>n
 * Date: 2017/10/18 0018
 * Time: 10:01
 */

namespace Controller\Order;

use Business\AgreementTicket\Storage;
use Business\CommodityCenter\Ticket;
use Business\Finance\AmountLedgerFreeze as AmountLedgerFreezeBiz;
use Business\JavaApi\Product\PftTicketPriceStorage;
use Business\Member\MemberRelation;
use Business\NewJavaApi\NewPms\StockQuery;
use Business\Order\Booking;
use Business\Order\ReservationOrder;
use Business\Product\Hotel;
use Library\Constants\Order\OrderChannel;
use Library\Container;
use Library\Controller;
use Business\Member\MemberMoney;
use Business\JavaApi\TicketApi;
use Business\PftSystem\SysConfig;
use Business\Order\OrderBook as businessLib;
use Business\Order\BatchOrder;
use Business\Product\Show as ShowBiz;

class OrderBook extends Controller
{
    private $_memberInfo;
    private $_memberId;

    private $_commonUseKey = 'guide:common:';

    public function __construct()
    {
        $this->_memberInfo = $this->getLoginInfo();
        $this->_memberId   = $this->_memberInfo['sid'];
    }

    /**
     * 获取预定数据
     *
     * @date   2017-10-18
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function getBookData()
    {
        $type     = I('post.type', '', 'strval');
        $ticketId = I('post.ticket_id', '', 'intval');
        $landId   = I('post.land_id', '', 'intval');
        $applyId  = I('post.apply_id', '', 'intval');
        $isNew    = I('post.is_new', 0, 'intval');

        $fSid = I('post.fsid', '', 'intval');
        //分销专员推广id
        $disId = I('post.dis_id', '', 'strval');

        if ($ticketId == '' || $applyId == '') {
            $this->apiReturn(201, '', '参数错误');
        }

        $contactAid = $this->_memberId;

        $channel = 5;

        if ($fSid) {
            $this->_memberId = $fSid;
            $channel         = 12;
        }

        //分销专员下单的下单人记为运营商
        $disId && $this->_memberId = $this->updateOrderMember($disId);

        $requestTicket = 0;

        //后续这里会调用上个版本的验证分销链功能

        //酒店类型需要返回当前门票即可
        //if (in_array($type, ['C'])) {
        //    $requestTicket = $ticketId;
        //}

        //获取门票数据
        $data = TicketApi::getBookTickets($this->_memberId, $applyId, $landId, $requestTicket, $channel);
        if ($data['code'] != 200) {
            $this->apiReturn($data['code'], '', $data['sub_msg']);
        }

        $ticketData = $data['data'];

        if (empty($ticketData)) {
            $this->apiReturn(400, [], '产品未通过审核或无权限购买');
        }
        $subSid  = $ticketData[$ticketId]['subSid'] ?? 0;
        $subType = $ticketData[$ticketId]['subType'] ?? 0;

        $earlyDate = TicketApi::getEarliestTwoDayPriceDate($ticketId, $this->_memberId, $channel, $applyId);

        if (!$earlyDate || empty($earlyDate)) {
            $this->apiReturn(204, [], '该产品暂不可售');
        }

        $firstDate  = $earlyDate[0]['date'];
        $secondDate = $earlyDate[1]['date'];

        if ($isNew) {
            $ticketData = businessLib::handleBookTicket($ticketData, $ticketId, $this->_memberId, $applyId, $channel, ['unifiedStorage' => true]);
        } else {
            $ticketData = businessLib::handleBookData($ticketData, $ticketId, $this->_memberId, $applyId, $channel, ['unifiedStorage' => true]);
        }

        if ($ticketData[0] != 0) {
            $this->apiReturn($ticketData[0], [], $ticketData[1]);
        }

        $ticketData = $ticketData[1];

        $contacts = $this->_getContacts($contactAid);

        $memberMoney = MemberMoney::getAllMoney($this->_memberId, $applyId, true);

        //可用余额覆盖
        $amountLedgerFreezeBiz = new AmountLedgerFreezeBiz();
        if ($amountLedgerFreezeBiz->isFrozenEnabled($this->_memberId)) {
            $memberMoney['balance'] = money_fmt($amountLedgerFreezeBiz->getAvailableBalance($this->_memberId));
        }

        //用户二期 - 信息获取修改 - 2 - modification-a
        $MemberBus    = new \Business\Member\Member();
        $CustomerBus  = new \Business\Member\Customer();
        //$memberInfo   = $MemberBus->getInfo($applyId);

        $memberIds = [$applyId];
        $subSid && $memberIds[] = $subSid;
        $memberInfoMap = $MemberBus->getMemberInfoByMulti($memberIds, 'id', true);
        $customerInfo = $CustomerBus->getCustomerInfoByMemberId($applyId);
        $applyInfo    = [
            'dname' => $memberInfoMap[$applyId]['dname'],
            'cname' => $memberInfoMap[$applyId]['cname'],
            'qq'    => $customerInfo['qq'],
            'sub_sname' => '',
        ];

        //自供应子商户名称展示
        if ($subSid) {
            //这个产品的顶级供应商跟登录用户主账号是同一个，才会显示子商户
            $ticketInfo = (new \Business\JavaApi\Product\Ticket())->queryTicketInfoById($ticketId);
            $applyDid = $ticketInfo['data']['uuJqTicketDTO']['applyDid'] ?: '0';
            if ($applyDid == $this->_memberInfo['sid']) {
                $applyInfo['sub_sname'] = $memberInfoMap[$subSid]['dname'] ?? '';
            }
        }

        //不允许使用余额支付的账号
        $banList = SysConfig::getOneSetting('ban_account_pay', $this->_memberId != 3385);

        $accountPay = 1;
        if ($banList) {
            $banArr = explode(',', $banList);
            if (in_array($this->_memberInfo['saccount'], $banArr)) {
                $accountPay = 0;
            }
        }
        //结算方式
        $memberRelationBus = new MemberRelation();
        $res               = $memberRelationBus->isExsitRecord($applyId, $this->_memberId);
        $clearingWay       = isset($res['clearing_mode']) ? $res['clearing_mode'] : 0;

        //酒店添加房型数据
        $hotelLib = new Hotel();
        if ($ticketData['p_type'] == 'C') {
            $ticketData['tickets'] = $hotelLib->handleRoomParamsBook($ticketData['tickets']);
        }
        //套票子票为酒店添加房型数据
        if ($ticketData['p_type'] == 'F') {
            $ticketData['tickets'] = $hotelLib->handleRoomParamsBookSon($ticketData['tickets']);
        }

        //演出产品需要判断是否需要选座
        $selectSeat = 0;
        if ($ticketData['p_type'] == 'H') {
            //判断顶级供应商是否有开通选座功能
            $ticketInfo = (new \Business\JavaApi\Product\Ticket())->queryTicketInfoById($ticketId);
            //演出的需要判断下供应商是否有选座的功能
            $configModel = new \Model\AdminConfig\AdminConfig();
            $result      = $configModel->havePermission($ticketInfo['data']['uuJqTicketDTO']['applyDid'], 44);
            $selectSeat  = $result ? 1 : 0;
        }

        //自定义标签根据渠道过滤
        $ticketData['tickets'] = (new \Business\Order\OrderBook())->handleCustomTag($ticketData['tickets'],$channel);

        $responseData = [
            'clearingWay' => $clearingWay,
            'status'      => 'success',
            'capital'     => $memberMoney,
            'contacts'    => $contacts,
            'suppler'     => $applyInfo,
            'account_pay' => $accountPay,
            'land'        => array(
                'lid'        => $ticketData['lid'],
                'pay'        => $ticketData['pay'],
                'ltitle'     => $ticketData['land_name'],
                'p_type'     => $ticketData['p_type'],
                'subType'    => $subType,
                'land_note'  => nl2br($ticketData['land_note']),
                'details'    => nl2br($ticketData['details']),
                'memberSID'  => $this->_memberId,
                'begintime'  => $ticketData['begin_time'],
                'firstDate'  => $firstDate,
                'secondDate' => $secondDate,
                'round_list' => $ticketData['round_list'] ? $ticketData['round_list'] : [],
                'tickets'    => $ticketData['tickets'],
                'selectSeat' => $selectSeat,
            ),
        ];

        $this->apiReturn(200, $responseData, 'success');
    }

    private function _getContacts($memberId)
    {
        $contactsBiz = new \Business\Member\FrequentContacts();
        $contactsRes = $contactsBiz->getAllContacts($memberId);

        $list = [];
        //兼容旧数据格式
        if ($contactsRes['code'] == 200 && $contactsRes['data']) {
            foreach ($contactsRes['data'] as $item) {
                $list[] = [
                    'id'             => $item['id'],
                    'name'           => $item['name'],
                    'tel'            => $item['mobile'],
                    'idCard'         => $item['person_id'],
                    'person_id_type' => $item['person_id_type'],
                    'province'       => $item['province'],
                    'province_code'  => $item['province_code'],
                    'city'           => $item['city'],
                    'city_code'      => $item['city_code'],
                    'town'           => $item['town'],
                    'town_code'      => $item['town_code'],
                    'detail_addr'    => $item['address'],
                    'mobile_area'    => $item['mobile_area'],
                    'mobile_region'  => $item['mobile_region'],
                ];
            }
        }

        return $list;
    }

    /**
     * 或群常用联系人
     * <AUTHOR>
     * @date   2018-05-08
     */
    public function getContacts()
    {
        $contacts = $this->_getContacts($this->_memberId);
        $this->apiReturn(200, $contacts);
    }

    /**
     * 保存至常用导游
     * <AUTHOR>
     * @date   2018-08-14
     */
    public function addCommonUseGuide()
    {

        //导游ID
        $guideId = I('post.id',0,'intval');
        //分销商id
        $fid     = I('post.fid',0,'intval');
        //供应商id
        $sid     = I('post.sid',0,'intval');

        if (empty($guideId)) {
            $this->apiReturn(403, [], '导游id不能为空');
        }
        if (empty($fid)) {
            $this->apiReturn(403, [], 'fid不能为空');
        }
        if (empty($sid)) {
            $this->apiReturn(403, [], 'sid不能为空');
        }

        if (!in_array($this->_memberId,[$fid,$sid])){
            $this->apiReturn(403, [], 'sid,fid跟当前账号无关联');
        }

        $guideManagerModel    = new \Model\Team\GuideManager();

        $guideInfo = $guideManagerModel->getGuideInfoById($guideId,3);
        if (empty($guideInfo)) {
            $this->apiReturn(403, [], '导游不存在或未启用');
        }

        $guideCommonInfo   = $guideManagerModel->getCommonGuideExits($this->_memberId, $sid, $fid, $guideId);
        if (!empty($guideCommonInfo)) {
            if ($guideCommonInfo['state'] == 1) {
                $this->apiReturn(204, [], '该常用导游已经添加过了');
            } else {
                $rs = $guideManagerModel->updateCommonGuideStateById($guideCommonInfo['id'], 1);
                if ($rs === false) {
                    $this->apiReturn(204, [], '添加常用导游失败');
                }
            }
        } else {
            $addCommonData = [
                'member_id'   => $this->_memberId,
                'sid'         => $sid,
                'fid'         => $fid,
                'create_time' => time(),
                'update_time' => 0,
                'state'       => 1,
                'guide_id'    => $guideId,
            ];
            $rs = $guideManagerModel->addCommonGuide($addCommonData);
            if (!$rs) {
                $this->apiReturn(204, [], '添加常用导游失败');
            }
        }

        $guideInfoList = $guideManagerModel->getCommonGuideList($this->_memberId, $sid, $fid);

        $guideInfoList = array_column($guideInfoList,null, 'id');

        $this->apiReturn(200, $guideInfoList);

    }

    /**
     * 获取常用导游
     * <AUTHOR>
     * @date   2018-08-14
     */
    public function getCommonUseGuide()
    {
        //分销商id
        $fid     = I('post.fid',0,'intval');
        //供应商id
        $sid     = I('post.sid',0,'intval');

        if (empty($fid)) {
            $this->apiReturn(403, [], 'fid不能为空');
        }
        if (empty($sid)) {
            $this->apiReturn(403, [], 'sid不能为空');
        }

        if (!in_array($this->_memberId,[$fid,$sid])){
            $this->apiReturn(403, [], 'sid,fid跟当前账号无关联');
        }

        $guideManagerModel = new \Model\Team\GuideManager();
        //根据导游ID获取导游手机号
        $guideInfoList = $guideManagerModel->getCommonGuideList($this->_memberId, $sid, $fid);

        $guideInfoList = array_column($guideInfoList,null, 'id');

        $this->apiReturn(200, $guideInfoList);

    }

    /**
     * 删除常用导游
     * <AUTHOR>
     * @date   2018-08-14
     */
    public function deleteCommonUseGuide()
    {
        //导游ID
        $guideId = I('post.id',0,'intval');
        //分销商id
        $fid     = I('post.fid',0,'intval');
        //供应商id
        $sid     = I('post.sid',0,'intval');

        if (empty($guideId)) {
            $this->apiReturn(403, [], '导游id不能为空');
        }
        if (empty($fid)) {
            $this->apiReturn(403, [], 'fid不能为空');
        }
        if (empty($sid)) {
            $this->apiReturn(403, [], 'sid不能为空');
        }

        if (!in_array($this->_memberId,[$fid,$sid])){
            $this->apiReturn(403, [], 'sid,fid跟当前账号无关联');
        }

        $guideManagerModel = new \Model\Team\GuideManager();

        $guideCommonInfo   = $guideManagerModel->getCommonGuideExits($this->_memberId, $sid, $fid, $guideId);
        if (empty($guideCommonInfo)) {
            $this->apiReturn(403, [], '该常用导游还未添加');
        }

        if ($guideCommonInfo['state'] != 1) {
            $this->apiReturn(403, [], '该导游已从常用列表中移除');
        }

        $rs = $guideManagerModel->updateCommonGuideStateById($guideCommonInfo['id'],2);
        if ($rs === false) {
            $this->apiReturn(403, [], '删除常用导游失败');
        } else {
            $this->apiReturn(200);
        }

    }

    /**
     * 获取账户和授信信息
     * <AUTHOR>
     * @date   2018-05-09
     */
    public function getAccountBalance()
    {
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        //为抱团下单时需要传分销商ID
        $fid = I('fid', 0, 'intval');
        if (!$aid) {
            $this->apiReturn(204, [], '参数错误');
        }
        $memberRelationBus     = new MemberRelation();
        $amountLedgerFreezeBiz = new AmountLedgerFreezeBiz();
        //判断是报团计调下单还是报团下单
        if ($fid) {
            $res         = $memberRelationBus->getDisClearingModels($this->_memberId, [$fid]);
            $clearingway = isset($res[$fid]) ? $res[$fid] : 0;
            $balance     = MemberMoney::getAllMoney($fid, $this->_memberId, true);
            if ($amountLedgerFreezeBiz->isFrozenEnabled($fid)) {
                $balance['balance'] = money_fmt(intval($amountLedgerFreezeBiz->getAvailableBalance($fid)));
            }
        } else {
            $res         = $memberRelationBus->getDisClearingModels($aid, [$this->_memberId]);
            $clearingway = isset($res[$this->_memberId]) ? $res[$this->_memberId] : 0;
            $balance     = MemberMoney::getAllMoney($this->_memberId, $aid, true);
            if ($amountLedgerFreezeBiz->isFrozenEnabled($this->_memberId)) {
                $balance['balance'] = money_fmt(intval($amountLedgerFreezeBiz->getAvailableBalance($this->_memberId)));
            }
        }
        $balance['clearingway'] = $clearingway;
        $this->apiReturn(200, $balance);
    }

    /**
     * 设置联系人
     *
     * @date   2017-10-18
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function setContact()
    {
        $name         = trim(I('post.name', ''));
        $mobile       = trim(I('post.mobile', ''));
        $phone        = trim(I('post.phone', ''));  //兼容旧业面的手机号提交
        $idcard       = I('post.idcard', '');
        $province     = I('post.province', '');
        $provinceCode = I('post.province_code', 0, 'intval');
        $city         = I('post.city', '');
        $cityCode     = I('post.city_code', 0, 'intval');
        $town         = I('post.town', '');
        $townCode     = I('post.town_code', 0, 'intval');
        $detailAddr   = trim(I('post.detail_addr', ''));
        $mobileArea   = I('post.mobile_area', '', 'strval');
        $mobileRegion = I('post.mobile_region', '', 'strval');
        $mobile       = empty($mobile) ? $phone : $mobile;
        if ($name == '' || $mobile == '') {
            $this->apiReturn(201, [], '参数有误');
        }

        $contact               = new \Entity\Member\FrequentContacts($this->_memberId, $name, $mobile);
        $contact->personId     = $idcard;
        $contact->province     = $province;
        $contact->provinceCode = $provinceCode;
        $contact->city         = $city;
        $contact->cityCode     = $cityCode;
        $contact->town         = $town;
        $contact->townCode     = $townCode;
        $contact->address      = $detailAddr;
        $contact->operId       = $this->_memberInfo['memberID'];
        $contact->mobileArea   = $mobileArea;
        $contact->mobileRegion = $mobileRegion;

        $contactsBiz = new \Business\Member\FrequentContacts();
        $result      = $contactsBiz->add($this->_memberId, $contact);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除常用联系人
     * <AUTHOR>
     * @date   2020-04-01
     */
    public function deleteContact()
    {
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数有误');
        }

        $contactsBiz = new \Business\Member\FrequentContacts();
        $result      = $contactsBiz->delete($this->_memberId, $id);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 获取库存和价格
     *
     * @date   2017-11-09
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function getStorageAndPrice()
    {
        $applyId   = I('post.aid', '', 'intval');
        $ticketIds = I('post.ticket_ids', '', 'strval, trim');
        $playDate  = I('post.play_date', '', 'strval, trim');
        $fsid      = I('post.fsid', '', 'intval');
        //分销专员推广id
        $disId = I('post.dis_id', '', 'strval');
        $supportCustomTime = I('post.support_custom_time', false, 'boolval');

        if ($fsid) {
            $this->_memberId = $fsid;
        }

        if (!$applyId || !$ticketIds || !$playDate) {
            $this->apiReturn(201, [], '参数错误');
        }

        //分销专员下单的下单人记为运营商
        $disId && $this->_memberId = $this->updateOrderMember($disId);

        $playDate = date('Y-m-d', strtotime($playDate));

        $tidArr             = explode(',', $ticketIds);
        $normalTicket       = [];
        $promissoryNote     = [];
        $commodityTicketBiz = new Ticket();
        $ticketInfoRes      = $commodityTicketBiz->queryTicketInfoByIds($tidArr, 'id,pre_sale', '', '');

        foreach ($ticketInfoRes as $key => $value) {
            if ($value['ticket']['pre_sale'] == 1) {
                $promissoryNote[] = $value['ticket']['id'];
            } else {
                $normalTicket[] = $value['ticket']['id'];
            }
        }
        if (empty($normalTicket) && empty($promissoryNote)) {
            $this->apiReturn(201, [], '票属性获取失败');
        }

        $tPriceBiz          = new \Business\Product\Price();


        $normalData         = [];
        $promissoryNoteData = [];
        if (!empty($normalTicket)){
            $normalDataRes = $tPriceBiz->buyBatchGet($this->_memberId, $applyId, $playDate, implode(',',$normalTicket), true);
            $normalData    = $normalDataRes['data'];
            //$normalData = TicketApi::getPriceAndStorageByPlayDate(implode(',',$normalTicket), $this->_memberId, $applyId, $playDate);
            if (empty($normalData)) {
                $this->apiReturn(203, [], '获取价格出错，请稍后重试');
            }
        }
        if (!empty($promissoryNote)){
            $promissoryNoteDataRes = $tPriceBiz->buyBatchGet($this->_memberId, $applyId, date('Y-m-d'), implode(',',$promissoryNote), true);
            $promissoryNoteData    = $promissoryNoteDataRes['data'];
            //$promissoryNoteData = TicketApi::getPriceAndStorageByPlayDate(implode(',',$promissoryNote), $this->_memberId, $applyId, date('Y-m-d'));
            if (empty($promissoryNoteData)) {
                $this->apiReturn(203, [], '获取价格出错，请稍后重试');
            }
        }
        $data = $normalData + $promissoryNoteData;

        $storageLib = new Storage();
        $ticketInfoMap = [];
        foreach ($ticketInfoRes as $valueNew) {
            if (!empty($valueNew['ticket']['id'])) {
                $ticketInfoMap[$valueNew['ticket']['id']] = [
                    'pid' => $valueNew['product']['id'] ?? 0,
                    'aid' => $valueNew['land']['apply_did'] ?? 0,
                    'is_agreement_ticket' => $valueNew['ext']['is_agreement_ticket'] ?? 0,
                ];
            }
        }

        //协议票库存处理
        foreach ($data as &$value) {
            $fid = $this->_memberId;
            $isAgreement = $ticketInfoMap[$value['ticketId']]['is_agreement_ticket'] ?? 0;
            $aid = $ticketInfoMap[$value['ticketId']]['aid'] ?? 0;

            if (!$isAgreement || $fid == $aid) {
                continue;
            }

            $sid = $applyId;
            $pid = $ticketInfoMap[$value['ticketId']]['pid'] ?? 0;
            $time = strtotime($playDate);
            $storageRes = $storageLib->queryStorageByOrderPage($fid, $sid, $pid, $time);

            if ($storageRes['code'] != 200 || empty($storageRes['data'])) {
                continue;
            }

            if ($storageRes['data']['storage'] == -1) {
                continue;
            }

            $value['availableStorage'] = $storageRes['data']['storage'];
        }

        $channel = 5; //分销后台
        if ($fsid) {
            $channel = 12; //计调下单
        }
        //获取票类型,套票要返回子票的分时配置
        $timeShareBiz  = new \Business\Order\TimeShareOrder();
        $timeShareRes  = $timeShareBiz->getTimeSlicesWithTidArr($tidArr, $playDate, $channel, $supportCustomTime);
        if ($timeShareRes['code'] == 200) {
            foreach ($data as $tid => &$item) {
                if ($timeShareRes['data'][$tid]) {
                    $item['time_share_info'] = $timeShareRes['data'][$tid];
                }
            }
        }

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 获取日历价格等信息
     *
     * @date   2017-11-08
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function getCalendarData()
    {
        $ticketId = I('post.ticket_id', '', 'intval');
        $month    = I('post.month', '', 'strval');
        $aid      = I('post.aid', '', 'intval');
        $fsid     = I('post.fsid', '', 'intval');
        //分销专员推广id
        $disId = I('post.dis_id', '', 'strval');

        if (!$ticketId || !$month || !$aid) {
            $this->apiReturn(201, [], '参数有误');
        }

        $startDate = $month . '-01';
        $endDate   = $month . date('-t', strtotime($month));

        //日历模式的请求就是自己的
        if ($aid == -1) {
            $aid = $this->_memberId;
        }

        if ($fsid) {
            $this->_memberId = $fsid;
        }

        //分销专员下单的下单人记为运营商
        $disId && $this->_memberId = $this->updateOrderMember($disId);

        $calendar = TicketApi::getOrderCalendar($this->_memberId, $aid, $ticketId, $startDate, $endDate);

        if (empty($calendar)) {
            $this->apiReturn(202, [], '暂无日历信息');
        }

        $data = [];
        foreach ($calendar as $key => $value) {
            $data[$value['time']] = $value;
        }

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 预定页面 -- 获取日历价格
     * Create by zhangyangzhen
     * Date: 2019/6/21
     * Time: 10:25
     *
     * @param  int ticket_id 票ID
     * @param  string month 月份，格式 2019-06
     * @param  int   aid 上级供应商ID
     */
    public function getCalendarDataV2()
    {
        $ticketId = I('post.ticket_id', '', 'intval');
        $month    = I('post.month', '', 'strval');
        $aid      = I('post.aid', '', 'intval');
//        $channel  = I('post.shop', 0, 'intval');
        $fsid     = I('post.fsid', 0, 'intval');//计调下单用

        if (!$ticketId || !$month || !$aid) {
            $this->apiReturn(201, [], '参数有误');
        }

        $channel = \Library\Constants\Product\ProductConst::CHANNEL_OF_PC; //5=分销后台
        //计调下单兼容
        if (!empty($fsid)) {
            $this->_memberId = $fsid;
            $channel = 12; //12=计调下单
        }

        // 最大限制为50天, 因产品需求，需要获取这个月前后7天的数据，为了方便，定义每个月为31天
        $startDate = date('Y-m-d', strtotime('-7 days', strtotime($month . '-01')));
        $endDate   = date('Y-m-d', strtotime('+38 days', strtotime($month . '-01')));

        //日历模式的请求就是自己的
        if ($aid == -1) {
            $aid = $this->_memberId;
        }

        //$calendar = TicketApi::getOrderCalendarV2($this->_memberId, $aid, $ticketId, $startDate, $endDate, $channel);
        $tPriceBiz          = new \Business\Product\Price();
        $calendar = $tPriceBiz->buyGetCalendar($this->_memberId, $aid, $startDate, $endDate, $ticketId, true);

        if ($calendar['code'] == self::CODE_SUCCESS) {
            $dateArr   = array_keys($calendar['data']);
            $endDate   = end($dateArr);
            $startDate = reset($dateArr);
            $timeShareBiz  = new \Business\Order\TimeShareOrder();
            $timeShareRes = $timeShareBiz->getTimeSlicesWithDateRange($ticketId, $startDate, $endDate, $channel);
            if ($timeShareRes['code'] == 200) {
                foreach ($calendar['data'] as $date => &$item) {
                    if ($timeShareRes['data'][$date]) {
                        if ($timeShareRes['data'][$date]['use_time_share']) {
                            $item['time_share_info'] = $timeShareRes['data'][$date];
                        }
                    }
                }
            }
            $this->apiReturn(self::CODE_SUCCESS, $calendar['data'], 'success');
        } else {
            $this->apiReturn(202, [], '暂无日历信息');
        }
    }

    /**
     * 酒店价格库存获取
     * <AUTHOR>
     * @date 2020/9/16
     *
     * @return array
     */
    public function getHotelStorageAndPrice()
    {
        $sid       = I('post.aid', 0, 'intval');
        $ticketIds = I('post.ticket_ids');
        $start     = I('post.start_date', '', 'strval');
        $end       = I('post.end_date', '', 'strval');
        $fsid      = I('post.fsid', 0, 'intval');//计调下单用

        if (empty($ticketIds) || empty($start) || empty($end) || empty($sid)) {
            $this->apiReturn(203, [], '参数有误');
        }

        //计调下单兼容
        if (!empty($fsid)) {
            $this->_memberId = $fsid;
        }

        $hotelLib = new Hotel();
        $res = $hotelLib->hotelCalendarGet($this->_memberId, $sid, $start, $end, $ticketIds);

        if ($res['code'] != 200 || empty($res['data'])) {
            $this->apiReturn(400, [], '获取价格库存失败');
        }

        $this->apiReturn($res['code'], $res['data'], $res['msg']);

    }

    /**
     * 切换日期，获取相应日期的场次列表
     * <AUTHOR>
     * @date   2018-12-14
     *
     * @return
     */
    public function getRoundList()
    {
        $venueId = I('post.venue_id', 0, 'intval');
        $date    = I('post.date', 0, 'strval');

        if (!$venueId || !$date || !strtotime($date)) {
            $this->apiReturn(203, [], '参数错误');
        }

        //获取指定日期的场次
        $date    = date('Y-m-d', strtotime($date));
        $nowtime = time();

        $showModel = new \Model\Product\Show();
        $showBiz  = new \Business\PftShow\Show();
        $roundList = [];

        $tmpList = $showModel->getRoundList($venueId, $field = 'id, round_name, bt, et, round_mode', $status = 0, $date, $date, 'use_date, bt, et');
        foreach ($tmpList as $item) {
            //将过期的场次屏蔽
            if (strtotime($date . ' ' . $item['et']) < $nowtime) {
                continue;
            }

            $roundTimeRes = $showBiz->getRoundTime($item['round_mode'], $item['bt'], $item['et']);
            $roundTime    = '';
            if ($roundTimeRes['code'] == 200) {
                $roundTime = $roundTimeRes['data'];
            }

            $roundList[] = [
                'round_id'   => intval($item['id']),
                'round_name' => $item['round_name'],
                'begin_time' => $item['bt'],
                'end_time'   => $item['et'],
                'round_mode' => $item['round_mode'],
                'round_time' => $roundTime,
            ];
        }

        //返回
        $this->apiReturn(200, $roundList, '获取成功');
    }

    /**
     * 通过门票和场次获取库存
     * <AUTHOR>
     * @date   2018-12-13
     *
     * @return
     */
    public function getShowStorage()
    {
        $venueId = I('post.venue_id', 0, 'intval');
        $roundId = I('post.round_id', 0, 'intval');

        $aids  = trim(I('post.aids', '', 'strval'), ',');
        $zones = trim(I('post.zones', '', 'strval'), ',');
        $pids  = trim(I('post.pids', '', 'strval'), ',');
        $fsid  = I('post.fsid', 0, 'intval');//计调下单用

        if (!$venueId || !$roundId || !$aids || !$zones || !$pids) {
            $this->apiReturn(203, [], '参数错误');
        }

        $aidList  = explode(',', $aids);
        $zoneList = explode(',', $zones);
        $pidList  = explode(',', $pids);

        $aidNum  = count($aidList);
        $zoneNum = count($zoneList);
        $pidNum  = count($pidList);

        if ($aidNum != $zoneNum || $zoneNum != $pidNum) {
            $this->apiReturn(203, [], '产品和分区不匹配');
        }

        //计调下单兼容
        if (!empty($fsid)) {
            $this->_memberId = $fsid;
        }

        $memberId = $this->_memberId;

        //实例化模型
        $sellerModel = new \Model\Member\Reseller();
        $showBiz     = new \Business\Product\Show();

        //返回数据
        $resData = [];

        for ($i = 0; $i < $aidNum; $i++) {
            $aid    = $aidList[$i];
            $zoneId = $zoneList[$i];
            $pid    = $pidList[$i];

            if ($aid && $zoneId && $pid) {
                if ($memberId == $aid) {
                    $topSellerId = $memberId;
                } else {
                    $topSellerId = $sellerModel->getTopResellerId($memberId, $aid, $pid);
                    $topSellerId = $topSellerId ? $topSellerId : $memberId;
                }

                $tmp  = $showBiz->getZoneLeftStorage($roundId, $zoneId, $topSellerId);
                $code = $tmp['code'];

                if ($code == 0) {
                    //获取库存失败
                    pft_log('order_show/error', 'PC_getShowStorage:'.json_encode(['getStorage', $roundId, $zoneId, $topSellerId, $tmp]));
                    $storage = 0;
                } else {
                    $storage = intval($tmp['data']['storage']);
                }

                $resData[] = [
                    'aid'     => $aid,
                    'pid'     => $pid,
                    'storage' => intval($storage),
                ];
            }
        }

        //返回
        $this->apiReturn(200, $resData, '获取成功');
    }

    /**
     * 获取开启分时预约最近的日期
     * <AUTHOR>
     * @date   2020-03-02
     */
    public function getLastestTimeShareDate()
    {
        $ticketId = I('tid', 0, 'intval');
        $fsid     = I('post.fsid', 0, 'intval');//计调下单用

        $channel = 5;
        if (!empty($fsid)) {
            $channel = 12;
        }

        $timeShareBiz = new \Business\Order\TimeShareOrder();
        $res          = $timeShareBiz->getLastestTimeShareDate($ticketId, $channel);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    public function getLastReservationDate()
    {
        $ordernum = I('ordernum', '', 'strval');
        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }
        $result = (new ReservationOrder())->getEarlyCanReservationDateService($ordernum);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 特产批量下单excel信息解析
     * <AUTHOR>
     * @date   2020-04-02
     */
    public function parseExcelForSpecialBatchOrder()
    {
        if (!isset($_FILES['special_excel'])) {
            $this->apiReturn(204, '请上传excel');
        }
        //计调下单
        $fid = I('fid/d', 0);
        if ($fid) {
            $aid = $this->_memberId;
        } else {
            $fid = $this->_memberId;
            $aid = I('aid/d', 0);
        }

         //取得扩展名
        $filetype = strtolower(strrchr($_FILES['special_excel']['name'], "."));
        if($filetype !='.xlsx') {
            $this->apiReturn(204, [], '文件格式不正确,请下载模板文件进行填写');
        }
        $excelProcess = new \Process\Order\OrderFromExcel();
        $res = $excelProcess->parse($fid, $aid, $_FILES['special_excel']['tmp_name']);

        if (is_array($res)) {
            $this->apiReturn(200, $res);
        } else {
            $this->apiReturn(204, [], $res);
        }
    }

    /**
     * 计算运费
     * <AUTHOR>
     * @date   2020-04-10
     */
    public function calculateCarriage()
    {
        //['tid' => 'num', 'tid2' => 'num']
        $tidNumMap    = I('tid_num_map', []);

        $provinceCode = I('province_code', 0, 'intval');
        $cityCode     = I('city_code', 0, 'intval');
        $aid          = I('aid', 0, 'intval');

        if (!$provinceCode || !$cityCode) {
            $this->apiReturn(204, [], '参数错误');
        }
        if (!is_array($tidNumMap) || empty($tidNumMap)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $aidArr = [];
        $fidArr = [];
        foreach ($tidNumMap as $key => $value) {
            $aidArr[$key] = $aid;
            $fidArr[$key] = $this->_memberId;
        }

        $api = new \Business\JavaApi\LogisticsCenter\Carriage();
        $result = $api->countBatchTicket($tidNumMap, $provinceCode, $cityCode, $fidArr, $aidArr); //平台计算运费

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 批量下单模板下载
     * <AUTHOR>
     * @date 2021/1/20
     *
     */
    public function batchOrderExcelDown()
    {
        $tid     = I('get.tid', 0, 'intval');
        $tnum    = I('get.tnum', 0, 'intval');
        $landId  = I('get.lid', 0, 'intval');
        $applyId = I('get.aid', 0, 'intval');
        $fSid    = I('get.fsid', 0, 'intval');
        //分销专员推广id
        $disId = I('get.dis_id', '', 'strval');
        $sid   = $this->_memberId;
        //是否返回文件0不返回文件 1返回文件
        $backFile = I('get.back_file', 0, 'intval');
        $type = I('get.type', 2, 'intval');//模版类型 1=横型 2=竖型
        if (!isset($backFile)) {
            $backFile = 0;
        }

        if (!$tid || !$applyId || !$landId) {
            $this->apiReturn(204, [], '票参数错误');
        }
        if (!$tnum || $tnum <= 0) {
            $this->apiReturn(204, [], '下单件数不能小于等于0');
        }

        $channel = 5;

        if ($fSid) {
            $sid     = $fSid;
            $channel = 12;
        }

        //分销专员下单的下单人记为运营商
        $disId && $sid = $this->updateOrderMember($disId);

        $bacthOrder = new BatchOrder();
        $result     = $bacthOrder->batchOrderExcelCreate($sid, $applyId, $landId, $tid, $tnum, $channel);

        if (isset($result['code'])) {
            if ($result['code'] == 200 && $backFile) {

                if ($type == 2) {
                    //竖版
                    foreach ($result['data'] as $value) {
                        $resultExcel[] = [$value];
                    }
                } else {
                    //横版
                    $resultExcel = [$result['data']];
                }

                $this->excelReturn('批量下单模板', '批量下单', $resultExcel);
            } else {
                $this->apiReturn($result['code'], $result['data'], $result['msg']);
            }
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 解析批量下单 Excel文件
     * <AUTHOR>
     * @date 2021/1/20
     *
     */
    public function parseExcelForBatchOrder()
    {
        if (!isset($_FILES['excel'])) {
            $this->apiReturn(204, [], '请上传excel');
        }
        $tid    = I('post.tid', 0, 'intval');
        $tnum   = I('post.tnum', 0, 'intval');
        $landId = I('post.lid', 0, 'intval');
        $aid    = I('post.aid', 0, 'intval');
        $fid    = I('post.fsid', 0, 'intval'); //计调下单
        //分销专员推广id
        $disId = I('post.dis_id', '', 'intval');
        $sid   = $this->_memberId;

        $channel = 5;

        if ($fid) {
            $channel = 12;
            $sid     = $fid;
        }

        if (!$tid || !$aid || !$landId) {
            $this->apiReturn(204, [], '票参数错误');
        }
        if (!$tnum || $tnum <= 0) {
            $this->apiReturn(204, [], '下单件数不能小于等于0');
        }

        //分销专员下单的下单人记为运营商
        $disId && $fid = $this->updateOrderMember($disId);

        //取得扩展名
        $filetype = strtolower(strrchr($_FILES['excel']['name'], "."));
        if (!in_array($filetype, ['.xlsx', '.xls'])) {
            $this->apiReturn(204, [], '文件格式不正确,请下载模板文件进行填写');
        }
        $excelProcess = new \Process\Order\OrderFromExcel();
        $excelData    = $excelProcess->parseForBatchOrder($_FILES['excel']['tmp_name']);

        //判断是横版还是竖版
        if (!empty($excelData) && !empty($excelData[0])) {
            //竖版转化
            $excelDataTmp = [];
            $nameTmp = $excelData[0][0] ?? '';
            $mobileTmp = $excelData[1][0] ?? '';

            if (strstr($nameTmp, '姓名') && strstr($mobileTmp, '手机号')) {
                foreach ($excelData as $keyData => $valueData) {
                    foreach ($valueData as $keySon => $valueSon) {
                        $excelDataTmp[$keySon][] = $valueSon;
                    }
                }
                $excelData = $excelDataTmp;
            }

        }

        $bacthOrder = new BatchOrder();
        $result     = $bacthOrder->batchOrderExcelDataVlidate($sid, $aid, $excelData, $landId, $tid, $tnum, $channel);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 下单获取演出信息
     * <AUTHOR>
     * @date 2021/4/12
     *
     */
    public function getShowInfoList()
    {
        $memberId = $this->_memberId;
        $venues = I('post.venues', '', 'strval');
        $date   = I('post.date', '', 'strval');

        if (empty($venues) || empty($date)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $showBiz = new ShowBiz();
        $result  = $showBiz->getShowInfoList($memberId, $venues, $date);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 批量计算订单的门票金额
     * <AUTHOR>
     * @date 2021/8/14
     *
     * @return array
     */
    public function batchComputeOrderTicketTotalPrice()
    {
        $priceRequestList = I('post.priceRequestList', []);
        $memberId = $this->_memberId;

        if (empty($priceRequestList)) {
            $this->apiReturn(203, [], '参数错误');
        }

        foreach ($priceRequestList as &$value) {
            if (empty($value['fid'])) {
                $value['fid'] = $memberId;
            }

            $value['sid'] = (int)$value['sid'];
            $value['ticketId'] = (int)$value['ticketId'];
            $value['num'] = (int)$value['num'];
        }

        $pftTTicketPriceStorageService = new PftTicketPriceStorage();
        $result = $pftTTicketPriceStorageService->batchComputeOrderTicketTotalPrice($priceRequestList);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 校验批量下单库存数
     */
    public function checkBatchStorage()
    {
        $applyId   = I('post.aid', 0, 'intval');                //上级供应商id
        $tid       = I('post.tid', 0, 'trim');                  //票id
        $playDate  = I('post.play_date', '', 'strval, trim');   //游玩日期
        $fsid      = I('post.fsid', 0, 'intval');               //计调下单时传的下单人的用户id
        $disId     = I('post.dis_id', '', 'strval');            //分销专员推广id
        $startDate = I('post.start_date', '', 'strval');        //酒店门票的开始时间
        $endDate   = I('post.end_date', '', 'strval');          //酒店门票的离店时间
        $orderNum  = I('post.order_num', 0, 'trim');            //下单数
        $tnum      = I('post.tnum', 0, 'trim');                 //每单票数
        $roundId   = I('post.round_id', 0, 'trim');             //演出场次id

        if ($fsid) {
            $this->_memberId = $fsid;
        }

        if (!$applyId || !$tid || !$playDate || !$orderNum || !$tnum) {
            $this->apiReturn(201, [], '参数错误');
        }

        if (!$orderNum || $orderNum <= 0) {
            $this->apiReturn(204, [], '下单件数不能小于等于0');
        }
        if (!$tnum || $tnum <= 0) {
            $this->apiReturn(204, [], '每单票数不能小于等于0');
        }

        //分销专员下单的下单人记为运营商
        $disId && $this->_memberId = $this->updateOrderMember($disId);

        $playDate = date('Y-m-d', strtotime($playDate));

        //下单票数
        $orderTnum = $orderNum * $tnum;

        $checkRes = (new \Business\Order\Booking())->checkBatchStorage($this->_memberId, $tid, $applyId, $playDate, $orderTnum, $startDate, $endDate, $roundId);
        if ($checkRes['code'] != 200) {
            $this->apiReturn($checkRes['code'], $checkRes['data'], $checkRes['msg']);
        }

        $this->apiReturn(200, [], '校验通过');
    }

    /**
     * 批量下单模板下载
     */
    public function batchOrderExcelDownV2()
    {
        $tid      = I('post.tid', 0, 'intval');
        $tnum     = I('post.tnum', 0, 'intval');
        $orderNum = I('post.order_num', 0, 'intval');
        $landId   = I('post.lid', 0, 'intval');
        $applyId  = I('post.aid', 0, 'intval');
        $fSid     = I('post.fsid', 0, 'intval');
        //分销专员推广id
        $disId = I('post.dis_id', '', 'strval');
        $sid   = $this->_memberId;
        //是否返回文件0不返回文件 1返回文件
        $backFile = I('post.back_file', 0, 'intval');
        if (!isset($backFile)) {
            $backFile = 0;
        }

        if (!$tid || !$applyId || !$landId) {
            $this->apiReturn(204, [], '票参数错误');
        }
        if (!$orderNum || $orderNum <= 0) {
            $this->apiReturn(204, [], '下单件数不能小于等于0');
        }
        if (!$tnum || $tnum <= 0) {
            $this->apiReturn(204, [], '每单票数不能小于等于0');
        }

        $channel = 5;

        if ($fSid) {
            $sid     = $fSid;
            $channel = 12;
        }

        //分销专员下单的下单人记为运营商
        $disId && $sid = $this->updateOrderMember($disId);

        $bacthOrder = new BatchOrder();
        $result     = $bacthOrder->batchOrderExcelCreateV2($sid, $applyId, $landId, $tid, $orderNum, $tnum, $channel);
        if (isset($result['code'])) {
            if ($result['code'] == 200 && $backFile) {
                $resultExcel = $result['data'];
                $bacthOrder->batchOrderExcelResultCreate('批量下单模板', '批量下单', $resultExcel, $orderNum, $tnum);
            } else {
                $resultExcel = array_column($result['data'], 'name');
                $this->apiReturn($result['code'], $resultExcel, $result['msg']);
            }
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 解析批量下单 Excel文件
     *
     */
    public function parseExcelForBatchOrderV2()
    {
        if (!isset($_FILES['excel'])) {
            $this->apiReturn(204, [], '请上传excel');
        }
        $tid      = I('post.tid', 0, 'intval');  //门票id
        $orderNum = I('post.order_num', 0, 'trim');      //下单数
        $tnum     = I('post.tnum', 0, 'trim');           //每单票数
        $landId   = I('post.lid', 0, 'intval');  //景区id
        $aid      = I('post.aid', 0, 'intval');  //上级供应商id
        $fid      = I('post.fsid', 0, 'intval'); //计调下单时传的下单人的用户id
        //分销专员推广id
        $disId = I('post.dis_id', '', 'intval');  //分销专员推广id
        $sid   = $this->_memberId;

        $channel = 5;

        if ($fid) {
            $channel = 12;
            $sid     = $fid;
        }

        if (!$tid || !$aid || !$landId) {
            $this->apiReturn(204, [], '票参数错误');
        }
        if (!$orderNum || $orderNum <= 0) {
            $this->apiReturn(204, [], '下单件数不能小于等于0');
        }
        if (!$tnum || $tnum <= 0) {
            $this->apiReturn(204, [], '每单票数不能小于等于0');
        }

        //分销专员下单的下单人记为运营商
        $disId && $fid = $this->updateOrderMember($disId);

        //取得扩展名
        $filetype = strtolower(strrchr($_FILES['excel']['name'], "."));
        if (!in_array($filetype, ['.xlsx', '.xls'])) {
            $this->apiReturn(204, [], '文件格式不正确,请下载模板文件进行填写');
        }
        $excelProcess = new \Process\Order\OrderFromExcel();
        $excelData    = $excelProcess->parseForBatchOrder($_FILES['excel']['tmp_name'], true);
        if (!$excelData) {
            $this->apiReturn(204, [], '文件内容异常');
        }

        $bacthOrder = new BatchOrder();
        $result     = $bacthOrder->batchOrderExcelDataVlidateV2($sid, $aid, $excelData, $landId, $tid, $orderNum, $tnum, $channel);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 演出下单前置校验
     */
    public function showPreCheck()
    {
        $tids     = I('tids', '', 'strval');    //门票id 多个以逗号隔开
        $playDate = I('play_date', '', 'strval');   //游玩日期
        if (!$tids || !$playDate || !strtotime($playDate)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $tidArr = explode(',', $tids);

        $result = (new \Business\PftShow\Order())->ticketOrderLimitCheck($tidArr, $playDate);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 计算票价以及优惠信息
     */
    public function batchComputeOrderTicketPrice()
    {
        $priceRequestList = I('post.priceRequestList', []);
        $orderMode = I('post.orderMode', OrderChannel::PLATFORM_CHANNEL, 'intval');
        if (empty($priceRequestList)) {
            $this->apiReturn(203, [], '参数错误');
        }
        $dtype = $this->_memberInfo['dtype'];
        $formatRequestList = [];
        $sidSet = [];
        $fidSet = [];
        foreach ($priceRequestList as $value) {
            $tmp = [];
            $tmp['fid'] = $value['fid'] ?: $this->_memberId;
            $tmp['sid'] = (int)$value['sid'];
            $tmp['ticketId'] = (int)$value['ticketId'];
            $tmp['num'] = (int)$value['num'];
            $tmp['playDate'] = $value['playDate'];
            $formatRequestList[] = $tmp;
            if (!isset($sidSet[$tmp['sid']])) $sidSet[$tmp['sid']] = 1;
            if (!isset($fidSet[$tmp['fid']])) $fidSet[$tmp['fid']] = 1;
        }
        $sidSet = array_keys($sidSet);
        if (count($sidSet) > 1) {
            $this->apiReturn(203, [], '只支持相同供应商产品下单');
        }
        $fidSet = array_keys($fidSet);
        if (count($fidSet) > 1) {
            $this->apiReturn(203, [], '只支持相同分销商产品下单');
        }
        $bookingBz = Container::pull(Booking::class);
        $result = $bookingBz->batchComputeOrderTicketPrice($sidSet[0], $fidSet[0], $formatRequestList, $orderMode, $dtype);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}
