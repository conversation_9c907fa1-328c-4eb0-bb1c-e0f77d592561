<?php

/**
 * 订单返利活动
 *
 * <AUTHOR>
 * @date 2017-09-18
 */

namespace Controller\Order;

use Business\Product;
use Library\Controller;
use Library\SimpleExcel;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\Member\Member;
use Model\Product\PriceGroup;

class Rebate extends Controller
{

    //主账号id
    private $_sid;

    //请求参数
    private $_params = [];

    //参数释义
    private $_paramsMap = [
        'name'         => ['活动名称', 'strval'],
        'target'       => ['返利对象', 'strval'],
        'pid'          => ['返利产品', 'strval'],
        'cycle'        => ['返利周期', 'intval'],
        'money'        => ['返利额度', 'strval'],
        'begin'        => ['时间开始', 'strval'],
        'end'          => ['结束时间', 'strval'],
        'money_type'   => ['返利额度类型', 'intval'],
        'paymode'      => ['支付类型', 'strval'],
        'deal_type'    => ['返利处理方式', 'intval'],
        'account_type' => ['返利账户类型', 'intval'],
    ];

    public function __construct()
    {

        $this->_sid = $this->isLogin('ajax');

    }

    /**
     * 新建返利活动
     * <AUTHOR>
     * @date   2017-09-18
     */
    public function create()
    {

        //参数检测
        $this->_requestCheck();

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->create(
            $this->_sid,
            $this->_params['name'],
            $this->_params['target'],
            $this->_params['pid'],
            $this->_params['cycle'],
            $this->_params['money'],
            $this->_params['money_type'],
            $this->_params['paymode'],
            $this->_params['deal_type'],
            $this->_params['account_type'],
            $this->_params['begin'],
            $this->_params['end']
        );

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }
    }

    /**
     * 编辑返利活动
     * <AUTHOR>
     * @date   2017-09-18
     */
    public function update()
    {

        //参数检测
        $this->_requestCheck();

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->update(
            $this->_sid,
            $this->_params['id'],
            $this->_params['name'],
            $this->_params['target'],
            $this->_params['pid'],
            $this->_params['cycle'],
            $this->_params['money'],
            $this->_params['money_type'],
            $this->_params['paymode'],
            $this->_params['deal_type'],
            $this->_params['account_type'],
            $this->_params['begin'],
            $this->_params['end']
        );

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }

    }

    /**
     * 获取返利活动详情
     * <AUTHOR>
     * @date   2017-09-20
     */
    public function getActivityDetail()
    {

        //活动详情id
        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $rebateBiz = new \Business\Order\Rebate();
        $getRes    = $rebateBiz->getActivityDetail($this->_sid, $id);

        if ($getRes['code'] == 200) {

            $detail = $getRes['data'];
            if ($detail['sid'] != $this->_sid) {
                $this->apiReturn(204, [], '无权操作');
            }

            $this->apiReturn(200, $detail);
        } else {
            $this->apiReturn(204, [], '活动详情获取失败');
        }
    }

    /**
     * 返利活动列表
     * <AUTHOR>
     * @date   2017-09-18
     */
    public function getActivityList()
    {

        $page   = I('page', 1, 'intval');
        $size   = I('size', 10, 'intval');
        $status = I('status', -1, 'intval');

        if ($size > 100) {
            $this->apiReturn(204, [], '每页最多请求100条');
        }

        if (!$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->getActivityList($this->_sid, $status, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }
    }

    /**
     * 获取返利对象(分销商和分组)
     * <AUTHOR>
     * @date   2017-09-18
     */
    public function getGroupList()
    {
        $getMore = I('post.get_more', 0, 'intval'); //是否获取总数 + 列表  0否 1是
        $page    = I('post.page', 1, 'intval');        //get_more = 1时生效
        $size    = I('post.size', 300, 'intval');      //get_more = 1时生效

        $relationBiz = new \Business\Member\MemberEvoluteRelation();
        $result      = $relationBiz->queryGroupNameListBySidAndGroupName($this->_sid, '', $size, $page);

        $total = 0;
        $list  = [];
        if ($result['code'] == 200 && !empty($result['data']['list'])) {
            $total = $result['data']['total'];
            foreach ($result['data']['list'] as $group) {
                $list[] = [
                    'id'   => $group['group_id'],
                    'name' => $group['group_name'],
                ];
            }
        }

        $return = $list;

        if ($getMore) {
            $return = [
                'list'  => $list,
                'total' => $total,
            ];
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 搜索分销商
     * <AUTHOR>
     * @date   2017-09-19
     */
    public function searchDistributors()
    {

        $keyword = I('keyword', '');
        $page    = I('page', 1, 'intval');
        $size    = I('size', 100, 'intval');
        $getMore = I('post.get_more', 0, 'intval'); //是否获取总数 + 列表  0否 1是  默认0

        if (!$keyword) {
            $this->apiReturn(200, [], '请输入关键字搜索');
        }

        $memberBiz = new \Business\Member\MemberRelation();

        $result = $memberBiz->searchDistributors($this->_sid, $keyword, $page, $size, $getMore);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }

    }

    /**
     * 返利产品(自供应+转分销)
     * <AUTHOR>
     * @date   2017-09-18
     */
    public function getProducts()
    {

        $res = $this->_getNewProducts();
        $this->apiReturn(200, $res);

        ////搜索关键字
        //$keyword = I('keyword', '', 'strval');
        //
        //if (!$keyword) {
        //    $this->apiReturn(204, [], '请输入关键字搜索');
        //}
        //
        //$ticModel = new Ticket('slave');
        //
        //$where = [
        //    'p.p_name' => ['like', "%{$keyword}%"]
        //];
        //
        //$field = 'p.id,l.title,t.title as ttitle';
        //
        //$option = [
        //    'where' => $where,
        //    'field' => $field
        //];
        //
        //$self = $ticModel->getSaleProducts($this->_sid, $option);
        //
        ////$evoWhere = [
        ////    'active' => 1
        ////];
        //
        ////$dis  = $ticModel->getSaleDisProducts($this->_sid, $evoWhere, $option);
        //$dis  = $ticModel->getSaleDisProductsByFidSourceIdStatusActive($this->_sid, 1, $option);
        //
        //$list = array_merge($self, $dis);
        //
        //if ($list) {
        //    //上级供应商名称
        //    $memArr = array_column($list, 'apply_sid');
        //    $memArr = array_unique($memArr);
        //
        //    $memModel = new Member('slave');
        //    $nameMap = $memModel->getMemberInfoByMulti($memArr, 'id', 'id,dname', 1);
        //
        //    foreach ($list as &$item) {
        //        $item['supply_name'] = $nameMap[$item['apply_sid']]['dname'];
        //    }
        //}
        //
        //$this->apiReturn(200, $list);
    }

    /**
     * 新的返利页面产品列表
     * @Author: zhujb
     * 2020/3/3
     */
    public function _getNewProducts()
    {
        $keyword = I('post.keyword', '', 'strval,trim');
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 10, 'intval');
        if (empty($keyword)) {
            return [];
        }

        $sid        = $this->_sid;
        $productBiz = new \Business\Product\Product();
        //$evoluteArr = $productBiz->getSaleProductByCondition($sid, $sid, 0, 0, $keyword);
        $evoluteArrInfo = $productBiz->querySaleProductList(0, $sid, 0, $keyword, $page, $size);
        if ($evoluteArrInfo['code'] != 200) {
            return [];
        }

        $evoluteArr = $evoluteArrInfo['data']['list'];

        // 用户信息
        $supplyIdArr = array_column($evoluteArr, 'sid');
        $memberModel = new Member('slave');
        $memberData  = $memberModel->getMemberInfoByMulti($supplyIdArr, 'id', 'id,dname', 1);

        // 门票信息
        $tidArr      = array_column($evoluteArr, 'ticket_id');
        $ticketModel = new Ticket('slave');
        $ticketData  = [];
        $ticketArr   = $ticketModel->getTicketInfoByIdArr($tidArr, 'id,pid,title');
        foreach ((array)$ticketArr as $ticket) {
            $ticketData[$ticket['id']] = $ticket;
        }

        // 景区信息
        $lidArr = array_column($evoluteArr, 'lid');
        //$landModel = new Land('slave');
        $landData = [];
        //$landArr   = $landModel->getLandInfoByLids($lidArr, 'id,title');

        $javaAPi = new \Business\CommodityCenter\Land();
        $landArr = $javaAPi->queryLandMultiQueryById($lidArr);
        foreach ((array)$landArr as $land) {
            $landData[$land['id']] = $land;
        }

        $res = [];
        foreach ($evoluteArr as $item) {
            $res[] = [
                'supply_name' => $memberData[$item['sid']]['dname'],
                'title'       => $landData[$item['lid']]['title'],
                'ttitle'      => $ticketData[$item['ticket_id']]['title'],
                'px'          => $item['px'],
                'tx'          => $item['tx'],
                'channel'     => $item['channel'],
                'level'       => $item['lvl'],
                'eid'         => $item['id'],
                'id'          => $ticketData[$item['ticket_id']]['pid'],
                'apply_sid'   => $item['sid'],
                'sapply_sid'  => $item['sourceid'],
                'aids'        => $item['aids'],
                'evo_active'  => $item['active'],
            ];
        }

        $return = [
            'list'      => $res,
            'page_num'  => $evoluteArrInfo['data']['pageNum'],
            'page_size' => $evoluteArrInfo['data']['pageSize'],
            'pages'     => $evoluteArrInfo['data']['pages'],
            'total'     => $evoluteArrInfo['data']['total'],
        ];

        return $return;
    }

    /**
     * 开启返利活动
     * <AUTHOR>
     * @date   2017-09-21
     */
    public function open()
    {

        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->open($this->_sid, $id);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }
    }

    /**
     * 暂停返利活动
     * <AUTHOR>
     * @date   2017-09-19
     */
    public function stop()
    {

        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->stop($this->_sid, $id);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }
    }

    /**
     * 删除返利活动
     * <AUTHOR>
     * @date   2017-09-19
     */
    public function delete()
    {

        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->delete($this->_sid, $id);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }

    }

    /**
     * 返利报表
     * <AUTHOR>
     * @date   2017-09-23
     */
    public function rebateReport()
    {
        //结算开始时间
        $begin = I('begin', '');
        //结算结束时间
        $end = I('end', '');
        //当前页数
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 10, 'intval');
        //针对某个分销商
        $fid = I('fid', 0);

        if ($size > 100) {
            $this->apiReturn(204, [], '每页条数超出限制');
        }

        if (!$begin || !$end) {
            $this->apiReturn(204, [], '参数错误');
        }

        $begin = strtotime($begin);
        $end   = strtotime($end);

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->rebateReport($this->_sid, $begin, $end, $page, $size, $fid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }

    }

    /**
     * 返利汇总
     * <AUTHOR>
     * @date   2017-09-23
     */
    public function rebateCollect()
    {

        //结算开始时间
        $begin = I('begin', '');
        //结算结束时间
        $end = I('end', '');
        //针对某个活动
        $fid = I('fid', 0);

        if (!$begin || !$end) {
            $this->apiReturn(204, [], '参数错误');
        }

        $begin = strtotime($begin);
        $end   = strtotime($end);

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->rebateCollect($this->_sid, $begin, $end, $fid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }
    }

    /**
     * 返利对象列表
     * <AUTHOR>
     * @date   2017-09-23
     */
    public function rebateTargetList()
    {

        //返利活动id
        $fid = I('fid', 0, 'intval');
        //返利日期
        $date = I('date', '');
        //返利周期
        $cycle = I('cycle', 1, 'intval');
        //当前页数
        $page = I('page', 1, 'intval');
        //每页条数
        $size = I('size', 5, 'intval');

        if ($size > 100) {
            $this->apiReturn(204, [], '每页条数超出限制');
        }

        if (!$cycle || !$date) {
            $this->apiReturn(204, [], '参数错误');
        }

        $date = date('Ymd', strtotime($date));

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->rebateTargetList($this->_sid, $fid, $date, $cycle, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }

    }

    /**
     * 返利报表门票展开
     * <AUTHOR>
     * @date   2017-10-12
     */
    public function rebateProductList()
    {

        //返利活动id
        $fid = I('fid', 0, 'intval');
        //返利日期
        $date = I('date', '');
        //返利周期
        $cycle = I('cycle', 1, 'intval');

        if (!$fid || !$date || !$cycle) {
            $this->apiReturn(204, [], '参数错误');
        }

        $date = date('Ymd', strtotime($date));

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->rebateProductList($this->_sid, $fid, $date, $cycle);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }

    }

    /**
     * 返利活动搜索
     * <AUTHOR>
     * @date   2017-09-23
     */
    public function searchRebateActivity()
    {

        $keyword = I('keyword');

        if (!$keyword) {
            $this->apiReturn(204, [], '请输入搜索关键字');
        }

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->searchRebateActivity($this->_sid, $keyword);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }

    }

    /**
     * 搜索待返利的分销商
     * <AUTHOR>
     * @date   2017-09-25
     */
    public function searchTarget()
    {

        //搜索关键字
        $keyword = I('keyword');
        //返利结算日期
        $date = I('date');
        //返利周期
        $cycle = I('cycle', 0, 'intval');

        if (!$keyword || !$date || !$cycle) {
            $this->apiReturn(204, [], '参数错误');
        }

        $date = date('Ymd', strtotime($date));

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->searchTarget($this->_sid, $date, $cycle, $keyword);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }

    }

    /**
     * 获取批量返利的金额
     * <AUTHOR>
     * @date   2017-09-26
     */
    public function getRebateMoneyForMulti()
    {

        //返利结算日期
        $date = I('date');
        //返利周期
        $cycle = I('cycle', 0, 'intval');

        if (!$date || !$cycle) {
            $this->apiReturn(204, [], '参数错误');
        }

        $date = date('Ymd', strtotime($date));

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->getRebateMoneyForMulti($this->_sid, $date, $cycle);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }
    }

    /**
     * 人工发放返利
     * <AUTHOR>
     * @date   2017-09-25
     */
    public function doRebateForOne()
    {

        //返利记录id
        $id = I('id', 0, 'intval');
        //返利金额
        $money = I('money', 0, 'intval');
        //返利到余额|授信|现金
        $accountType = I('account_type', 1, 'intval');

        if (!$id || !$money) {
            $this->apiReturn(204, [], '参数错误');
        }

        $date = date('Ymd', strtotime($date));

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->doRebateForOne($this->_sid, $id, $money, $accountType);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }
    }

    /**
     * 批量返利
     * <AUTHOR>
     * @date   2017-09-27
     */
    public function doRebateForMulti()
    {

        //不返利名单id,  81,82
        $black = I('black', '');
        //返利结算日期
        $date = I('date');
        //返利周期
        $cycle = I('cycle', 0, 'intval');
        //返利到余额|授信|现金
        $accountType = I('account_type', 1, 'intval');

        if (!$cycle || !$date) {
            $this->apiReturn(204, [], '参数错误');
        }

        $black = explode(',', $black);
        $date  = date('Ymd', strtotime($date));

        $rebateBiz = new \Business\Order\Rebate();

        $result = $rebateBiz->doRebateForMulti($this->_sid, $black, $accountType, $date, $cycle);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }

    }

    /**
     * 下载返利报表
     * <AUTHOR>
     * @date   2017-09-28
     * @return [type]     [description]
     */
    public function downloadReport()
    {

        //返利结算日期
        $date = I('date');
        //返利周期
        $cycle = I('cycle', 0, 'intval');
        //分销商id
        $fid = I('fid', 0, 'intval');

        if (!$date || !$cycle) {
            $this->apiReturn(204, [], '参数错误');
        }

        $rebateBiz = new \Business\Order\Rebate();

        $format = date('Ymd', strtotime($date));

        $result = $rebateBiz->getReportDetail($this->_sid, $format, $cycle, $fid);

        if (isset($result['code']) && $result['code'] == 200) {
            $cycleMap = $rebateBiz->getCycleMap();
            $filename = "{$date}-{$cycleMap[$cycle]}-返利报表";

            $xls = new SimpleExcel('UTF-8', true, 'rebate');
            $xls->addArray($result['data']);
            $xls->generateXML($filename);
            exit;
        } else {
            $this->apiReturn(500, [], '未知错误');
        }

    }

    /**
     * 网页打印数据接口
     * <AUTHOR>
     * @date   2017-10-03
     */
    public function printWeb()
    {

        //返利结算日期
        $date = I('date');
        //返利周期
        $cycle = I('cycle', 0, 'intval');
        //分销商id
        $fid = I('fid', 0, 'intval');

        if (!$date || !$cycle) {
            $this->apiReturn(204, [], '参数错误');
        }

        $rebateBiz = new \Business\Order\Rebate();

        $date = date('Ymd', strtotime($date));

        $result = $rebateBiz->printWeb($this->_sid, $date, $cycle, $fid);

        if (isset($result['code']) && $result['code'] == 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '未知错误');
        }

    }

    /**
     * 参数检测
     * <AUTHOR>
     * @date   2017-09-18
     */
    private function _requestCheck()
    {

        foreach ($this->_paramsMap as $key => $intro) {

            if (!I($key)) {
                $this->apiReturn(204, [], "请选择或填写{$intro[0]}");
            }
            $this->_params[$key] = I($key, '', $intro[1]);
        }

        $this->_params['end']    = strtotime($this->_params['end']);
        $this->_params['begin']  = strtotime($this->_params['begin']);
        $this->_params['target'] = json_encode($this->_params['target']);
        $this->_params['money']  = json_encode($this->_params['money']);
        $this->_params['pid']    = json_encode($this->_params['pid']);

        parse_str($_SERVER['QUERY_STRING'], $query);

        if ($query['a'] == 'update') {
            if (!I('id', 0)) {
                $this->apiReturn(204, [], 'id参数缺失');
            }
            $this->_params['id'] = I('id', 0, 'intval');
        }

    }

}
