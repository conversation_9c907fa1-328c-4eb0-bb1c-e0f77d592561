<?php
/**
 * 订单修改操作
 *
 * <AUTHOR>
 * @date    2017-04-05
 */

namespace Controller\Order;

use Business\Member\MemberRelation;
use Business\MemberLogin\MemberLoginHelper;
use Business\Order\BaseRefundCheck;
use Business\Order\Modify;
use Business\Order\Refund\QConfigSwitchController;
use Business\Order\ReservationOrder;
use Business\Order\ShowModify;
use Business\Product\Specialty;
use Business\TeamOrder\Application;
use Controller\Order\Traits\TravelVoucherTrait;
use Library\Constants\Card\TouristCardEnum;
use Library\Constants\OrderConst;
use Library\Controller;
use Library\Kafka\KafkaProducer;
use Library\Resque\Queue;
use Library\Tools\Helpers;
use Library\Tools\Validate;
use Model\Order\OrderHandler;
use Model\Order\OrderRefer;
use Model\Order\OrderTools;
use Model\Order\OrderTrack;
use Model\Order\SubOrderQuery;
use Model\Product\Land;
use Model\Product\Ticket;
use Business\Order\OrderDeliver as OrderDeliverBiz;

class OrderModify extends Controller
{
    use TravelVoucherTrait;
    //当前登陆的主账号id
    private $_sid;
    //当前登陆的账号id
    private $_mid;
    //当前的账号类型
    private $_loginInfo;

    public function __construct()
    {
        $loginInfo = $this->getLoginInfo();

        $this->_sid       = $loginInfo['sid'];
        $this->_mid       = $loginInfo['memberID'];
        $this->_loginInfo = $loginInfo;
    }

    /**
     * 取消订单，退全票
     * <AUTHOR>
     * @date 2019/7/15
     *
     * @param  string  $ordernum  需要取消的订单号
     *
     * @return array
     */
    public function cancel()
    {
        //参数处理
        $ordernum        = I('post.ordernum', '', 'strval');
        $reqSerialNumber = I('post.req_serial_number', '', 'strval');
        $isRefundDeposit = I('post.refund_deposit', 0, 'intval'); //是否退押金 0：不退押金 1:退押金（计时特有）
        $isRefundOverPay = I('post.refund_over_pay', 0, 'intval'); //是否退超时补费金额 0：不退 1:退（计时特有）

        if (!$ordernum) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '参数缺失');
        }

        //TODO:退款流水号的校验

        $cancelRemark  = '取消订单';
        $cancelChannel = OrderConst::PC_CANCEL;
        $memberId      = $this->_mid;
        $sid           = $this->_sid;
        $moreData      = [];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $moreData['subSid']   = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $moreData['subOpId']   = MemberLoginHelper::getLoginBusinessMember()->getMemberId();
            $memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $sid      = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }
        $modifyBiz = new Modify();
        $cancelRes = $modifyBiz->baseCancel($ordernum, $memberId, $sid, $cancelChannel, $cancelRemark,
            $reqSerialNumber, true, false, false, $isRefundDeposit, $isRefundOverPay, $moreData);

        if (in_array($cancelRes['code'], [200, 1095])) {
            $resData = [
                'ordernum'          => $ordernum,
                'req_serial_number' => $cancelRes['data']['req_serial_number'],
                'pft_serial_number' => $cancelRes['data']['pft_serial_number'],
            ];

            $succMsg = $cancelRes['code'] == 200 ? '退票成功' : '退票审核提交成功';
            $this->apiReturn($cancelRes['code'], $resData, $succMsg);
        } else {
            $this->apiReturn($cancelRes['code'], [], $cancelRes['msg']);
        }
    }

    /**
     * 退部分票
     * <AUTHOR>
     * @date 2019/7/15
     *
     * @return array
     */
    public function refund()
    {
        //订单号
        $ordernum = I('post.ordernum', '', 'strval');

        //订单对应退票数 {'24425694':1,'24425695':2}
        $tmpCancelList = I('post.order_cancel_list', []);

        //退票游客身份证信息 ['150204196201011176', '150204196201016137']
        $tmpTouristInfo = I('post.cancel_tourist_list', []);

        //退款流水号
        $reqSerialNumber = I('post.req_serial_number', '', 'strval');

        //参数校验
        if (!$ordernum) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '订单号参数缺失');
        }

        //TODO:退款流水号的校验

        $cancelList = [];
        if ($tmpCancelList && is_array($tmpCancelList)) {
            foreach ($tmpCancelList as $subOrdernum => $cancelNum) {
                $subOrdernum = strval($subOrdernum);
                $cancelNum   = intval($cancelNum);

                if ($subOrdernum && $cancelNum > 0) {
                    $cancelList[$subOrdernum] = $cancelNum;
                }
            }
        }

        if (!$cancelList) {
            $this->apiReturn(static::CODE_NO_CONTENT, [], '退票参数错误');
        }

        $otherParams = [];
        $touristList = [];
        //增加放量改造，保留原逻辑所有都当做身份证校验的错误逻辑
        $qConfigSwitch = new QConfigSwitchController();
        if ($qConfigSwitch::getRefundFixUsedWithTenant($ordernum)) {
            foreach ($tmpTouristInfo as $item) {
                $voucherTypeObj = TouristCardEnum::find(intval($item['voucher_type']));
                //仅支持有的枚举类型退票，对应的uu_order_tourist_info表中的voucher_type字段
                if (isset($item['id_card']) && $voucherTypeObj){
                    if ($voucherTypeObj->key == TouristCardEnum::ID_CARD) {
                        $idcard = strval($item['id_card']);
                        if (strlen($idcard) == 15) {
                            $idcard = Validate::idcard_15to18($idcard);
                        }
                        if (!Validate::isIDCard($idcard)) {
                            $this->apiReturn(static::CODE_PARAM_ERROR, [], "身份证【{$item['id_card']}】不合法");
                        }
                        $touristList[] = $item['id_card'];
                    }
                    //往退票的person参数中写入新增的结构体：person_info_list
                    $otherParams['person']['person_info_list'][] = [
                        'idcard'=>$item['id_card'],
                        'idcard_type'=>$item['voucher_type'],
                        'ticket_code'=>$item['chk_code'],
                        'person_index'=>$item['idx']
                    ];
                }
            }
        } else {
            //放量改造前原逻辑
            foreach ($tmpTouristInfo as $item) {
                $idcard = strval($item['id_card']);
                if ($idcard) {
                    if (!Validate::isIDCard($idcard)) {
                        $this->apiReturn(static::CODE_PARAM_ERROR, [], "身份证【{$item['id_card']}】不合法");
                    }
                    $touristList[] = $item['id_card'];
                }
            }
        }

        $cancelChannel = OrderConst::PC_CANCEL;
        $cancelRemark  = "订单退票";

        $memberId      = $this->_mid;
        $sid           = $this->_sid;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $otherParams['subSid']   = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $otherParams['subOpId']   = MemberLoginHelper::getLoginBusinessMember()->getMemberId();
            $memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $sid      = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }
        $modifyBiz = new Modify();
        $cancelRes = $modifyBiz->baseRefund($ordernum, $cancelList, $memberId, $sid, $cancelChannel,
            $touristList, $cancelRemark, $reqSerialNumber, true, false, [], $otherParams);

        if (in_array($cancelRes['code'], [200, 1095])) {
            $resData = [
                'ordernum'          => $ordernum,
                'req_serial_number' => $cancelRes['data']['req_serial_number'],
                'pft_serial_number' => $cancelRes['data']['pft_serial_number'],
            ];
            $succMsg = $cancelRes['code'] == 200 ? '退票成功' : '退票审核提交成功';
            $this->apiReturn($cancelRes['code'], $resData, $succMsg);
        } else {
            if(!empty($cancelRes['msg'])){
                $errMsg = $cancelRes['msg'];
            }
            else{
                $errMsg = $cancelRes['data']['err_msg'] . "【{$cancelRes['data']['err_code']}】";
            }
            $this->apiReturn($cancelRes['code'], [], $errMsg);
        }
    }

    /**
     * 指定idx退票
     * <AUTHOR>
     * @date 2021/7/22
     *
     * @return array
     */
    public function appointIdxRefund()
    {
        $orderNum = I('post.ordernum', '');
        $idx      = I('post.idx', 0);
        if (!$orderNum || !$idx) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '取消参数有误');
        }
        $memberId = $this->_mid;
        $sid = $this->_sid;
        $otherParams = [];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $otherParams['subSid']   = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $otherParams['subOpId']   = MemberLoginHelper::getLoginBusinessMember()->getMemberId();
            $memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $sid      = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $cancelChannel = OrderConst::PLATFORM_APPOINT_CANCEL;
        $cancelRemark  = "订单退票";
        $modifyBiz     = new Modify();
        $cancelRes     = $modifyBiz->appointIdxRefundService($orderNum, $idx, $memberId, $sid,
            $cancelChannel, $cancelRemark, $otherParams);
        if (in_array($cancelRes['code'], [200, 1095])) {
            $resData = [
                'ordernum'          => $orderNum,
                'req_serial_number' => $cancelRes['data']['req_serial_number'],
                'pft_serial_number' => $cancelRes['data']['pft_serial_number'],
            ];
            $succMsg = $cancelRes['code'] == 200 ? '退票成功' : '退票审核提交成功';
            $this->apiReturn($cancelRes['code'], $resData, $succMsg);
        } else {
            $errMsg = isset($cancelRes['data']['err_msg']) ? $cancelRes['data']['err_msg'] . "【{$cancelRes['data']['err_code']}】" : $cancelRes['msg'];
            $this->apiReturn($cancelRes['code'], [], $errMsg);
        }
    }

    /**
     * 强制取消
     * <AUTHOR>
     * @date 2019/09/30
     *
     * @return array
     */
    public function forceCancel()
    {
        //订单号
        $ordernum = I('post.ordernum', '', 'strval');
        if (!$ordernum) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '参数缺失');
        }
        $orderType = I('post.p_type', '');

        $cancelType      = I('post.cancel_type', 0); //0完全取消 1-数量取消 2-门票
        $isRefundDeposit = I('post.refund_deposit', 0, 'intval'); //是否退押金 0：不退押金 1:退押金（计时特有）
        $isSkipExchangeTicket = I('post.is_skip_exchange_ticket', false);
        $ticketCodeArr = [];
        if($orderType == 'Q'){
            if ($cancelType == 2){
                $cancelTicketCodeStr = I('post.ticket_code_str','');//门票码逗号隔开
                $ticketCodeArr = explode(',', $cancelTicketCodeStr);
                if ($ticketCodeArr > 1) {
                    $cancelNum = count($ticketCodeArr);
                } else {
                    $cancelNum = -1;
                }
            }elseif ($cancelType == 1){
                $cancelNum = I('post.cancel_num',0);
            }else{
                $cancelNum = -1;
            }
            $tvInvoiceApi = new \Business\JsonRpcApi\TravelVoucherService\SpecialOrders();
            $cancelRes  = $tvInvoiceApi->cancelForForceRefund($ordernum, $this->_sid, $this->_mid,$cancelType,$cancelNum,$ticketCodeArr);
            if($cancelRes['code'] == 200){
                $this->apiReturn($cancelRes['code'], [], '退票成功');
            }
            else{
                $this->apiReturn($cancelRes['code'], [], $cancelRes['msg']);
            }
        }
        if ($cancelType == 2){
            $cancelTicketCodeStr = I('post.ticket_code_str','');//门票码逗号隔开
            //简单做根据游客表里面有多少个门票码确定是不是要用idx指定取消
            $touristMdl = new \Model\Order\OrderTourist();
            $touristNum = $touristMdl->getTouristNumByStatusGroupChkCode($ordernum);
            //todo 实际上这边产品需求是要根据票属性是否是一票一码还是一票种一张票判断，这边直接简单判断下门票码生成的数量
            if ($touristNum > 1) {
                $ticketCodeArr = explode(',', $cancelTicketCodeStr);
                $cancelNum = count($ticketCodeArr);
            } else {
                $cancelNum = -1;
            }
        }elseif ($cancelType == 1){
            $cancelNum = I('post.cancel_num',0);
        }else{
            $cancelNum = -1;
        }

        $cancelRemark  = '【强制取消操作】';
        $cancelChannel = OrderConst::PC_CANCEL;
        $ip            = get_client_ip();
        $ip            = ip2long($ip);
        $otherData = [
            'cancelNum'        => $cancelNum,
            'cancelTicketCode' => $ticketCodeArr ?? [],
            'isRefundDeposit'  => $isRefundDeposit,
            'isSkipExchangeTicket' => $isSkipExchangeTicket
        ];
        $modifyBiz     = new Modify();
        $cancelRes     = $modifyBiz->forceCancel($ordernum, $this->_mid, $this->_sid, $cancelChannel, $cancelRemark,
            false, $ip,$otherData);

        if (in_array($cancelRes['code'], [200, 1095])) {
            $resData = [
                'ordernum'          => $ordernum,
                'req_serial_number' => $cancelRes['data']['req_serial_number'],
                'pft_serial_number' => $cancelRes['data']['pft_serial_number'],
            ];

            $this->apiReturn($cancelRes['code'], $resData, $cancelRes['msg']);
        } else {
            if($cancelRes['data']['err_code'] == OrderConst::err10001){
                $cancelRes['code'] = $cancelRes['data']['err_code'];
            }
            $this->apiReturn($cancelRes['code'], $cancelRes['data'], $cancelRes['msg']);
        }
    }

    /**
     * 往订单里面添加票数
     * <AUTHOR>
     * @date 2019/7/15
     *
     * @return array
     */
    public function addTicket()
    {
        $ordernum = I('post.ordernum');
        //修改后的门票数量映射
        $numMapping = I('post.num_mapping');
        //游客身份证信息
        $touristInfo = I('post.tourist_info', []);
        //团单订单号
        $teamOrder = I('post.team_order', '', 'strval');

        if (!$ordernum || !$numMapping) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '参数缺失');
        }

        $business = new Modify();

        $result = $business->orderAddNum($ordernum, $numMapping, $this->_sid, $this->_mid, $touristInfo, true);

        if ($result['code'] == 200) {
            if ($teamOrder) {
                $order     = (new Application())->order;
                $changeNum = $result['data']['change'];
                $res       = $order->modifyTeamTicketNum($teamOrder, $changeNum);
                if (!$res) {
                    $this->apiReturn(400, [], '更新团队订单人数失败');
                }
            }
        }

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 获取订单游客身份证信息
     *
     * @param  string   ordernum   订单号
     * @param  array   tids   门票id数组   [123, 35435]
     */
    public function getOrderTouristInfo()
    {
        //订单号
        $ordernum = I('post.ordernum');
        if (!$ordernum) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '参数缺失');
        }
        $businessModify = new Modify();
        //游客身份证信息
        $idCardArray = $businessModify->getOrderTouristInfoByOrderNum($ordernum);
        if (!$idCardArray) {
            $idCardArray = [];
        }
        $data = [
            'idCardArray' => $idCardArray,
        ];

        $this->apiReturn(parent::CODE_SUCCESS, $data, '获取成功');
    }

    /**
     * 获取订单各票原始票数
     */
    public function getOriginTicketNum()
    {
        //订单号
        $ordernum = I('post.order_num');
        if (!$ordernum) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '参数缺失');
        }
        $businessModify       = new Modify();
        $originTicketNumArray = $businessModify->getOriginTnumArrayByMainOrderNum($ordernum);
        if (!$originTicketNumArray) {
            $this->apiReturn(parent::CODE_CREATED, [], '获取失败');
        }
        $this->apiReturn(parent::CODE_SUCCESS, $originTicketNumArray, '获取成功');
    }

    /**
     * 修改订单总金额
     */
    public function updateOrderTotalMoney()
    {
        //优化分销商不可修改订单价格
        $this->apiReturn(parent::CODE_NO_CONTENT, [], '不支持修改价格');

        $orderNum   = I('post.order_num');
        $totalMoney = I('post.total_money');
        if ($totalMoney == 0) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '价格不可为0');
        }
        if (!$orderNum || !$totalMoney) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '参数缺失');
        }
        $memberId = $this->_mid;
        $sid      = $this->_sid;
        $moreData = [];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $moreData['subSid']  = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $moreData['subOpId'] = MemberLoginHelper::getLoginBusinessMember()->getMemberId();
            $sid                 = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $memberId            = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $businessModify = new Modify();
        $result         = $businessModify->updateOrderTotalMoney($orderNum, $totalMoney, $sid, $memberId, $moreData);
        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 修改订单游玩日期
     */
    public function updateOrderPlayDate()
    {
        $orderNum = I('post.order_num');
        $playDate = I('post.play_date');
        if (!$orderNum || !$playDate) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '参数缺失');
        }

        $businessModify = new Modify();
        $result         = $businessModify->updateOrderPlayDate($orderNum, $playDate, $this->_mid);
        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 特产订单发货接口
     * 新版特产发货接口，发货触发Java接口
     * -移植了pc端的wengbin的deliver接口
     *
     * 注：订单编号为必传参数
     *
     * <AUTHOR>
     * @date   2020-06-24
     */
    public function deliverGoods()
    {
        $ordernum   = I('ordernum', '', 'strval');//订单编号 必须
        $expCompany = I('company', '', 'strval');//快递公司
        $expNo      = I('out_trade_no', '', 'strval');//快递号

        $shippingId = I('shipping_id', 0, 'intval');//收货人记录id *不需要了
        $memo       = I('memo', '');//备注  *不需要了

        //订单号验证
        if (!$ordernum) {
            $this->apiReturn(204, '订单号缺失');
        }

        $operId = $this->_mid;//操作人id
        $sid    = $this->_sid;//会员id
        $source = 22; //订单追踪记录来源  微平台归微商城

        //资源账号处理
        $sdtype  = $this->_loginInfo['sdtype'];
        $sacount = $this->_loginInfo['saccount'];

        $_orderDeliverBiz = new OrderDeliverBiz(); //发货
        $result           = $_orderDeliverBiz->deliver($sid, $operId, $ordernum, $expCompany, $expNo, $source, $sacount,
            $sdtype);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /***
     * 订单改签
     * <AUTHOR> Yiqiang
     * @date   2018-09-18
     */
    public function ticketChanging()
    {
        //订单号
        $ordernum = I('post.ordernum');
        //改签日期
        $changeTime = I('post.play_date', '');
        //分时预约id
        $shareTimeId = I('post.section_time_id', 0);
        //分时预约字符串
        $shareTimeStr = I('post.section_time_str', '');
        //改签的分时预约
        if (!$ordernum || !$changeTime) {
            $this->apiReturn(204, [], '缺少参数');
        }

        $info     = $this->getLoginInfo();
        $opId     = $info['memberID'];
        $sid      = $this->_sid;
        $moreData = [];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $moreData['subSid'] = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $moreData['subOpId'] = MemberLoginHelper::getLoginBusinessMember()->getMemberId();
            $sid    = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $opId   = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();

        }
        $source   = 16;
        $orderBiz = new Modify();
        $res      = $orderBiz->ticketChanging($ordernum, $changeTime, $sid, $opId, $source, $shareTimeId,
            $shareTimeStr, false, $moreData);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 获取订单待确认信息
     * @Author: zhujb
     * 2018/11/26
     *
     * 之前的逻辑都写在这里，已经移到业务层，\Business\Order\Modify.class.php
     * 关于获取订单待确认信息可以直接调用
     * <AUTHOR>
     * @date  2020/09/28
     */
    public function getOrderConfirmInfo()
    {
        $ordernum = I('post.ordernum', '', 'strval');
        if (empty($ordernum)) {
            $this->apiReturn(203, [], '订单号不存在');
        }

        $hotelBiz = new \Business\Order\Modify();
        $result   = $hotelBiz->getOrderConfirmInfoByOrdernum($ordernum);
        if (!isset($result['code'])) {
            $this->apiReturn(500, [], '异常出错');
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 确认订单
     * @Author: zhujb
     * 2018/11/26
     *
     * 之前的逻辑都写在这里，已经移到业务层，\Business\Order\Modify.class.php
     * 关于订单确认可以直接调用
     * <AUTHOR>
     * @date  2020/09/28
     */
    public function orderConfirm()
    {
        $orderNumStr = I('ordernum', '', 'strval');
        $stuffId     = I('stuff_id', 0, 'intval');
        $siteId      = I('site_id', 0, 'intval');

        $info = $this->getLoginInfo();

        $sid     = $info['sid'];
        $mid     = $info['memberID'];
        $account = $info['account'];

        if (empty($orderNumStr)) {
            $this->apiReturn(203, [], '订单号不存在');
        }

        $hotelBiz = new \Business\Order\Modify();
        $result   = $hotelBiz->handleOrderConfirm($orderNumStr, $sid, $mid, $account, $stuffId, $siteId);
        if (!isset($result['code'])) {
            $this->apiReturn(500, [], '异常出错');
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 订单拒绝确认
     * @Author: zhujb
     * 2018/11/26
     *
     * 之前的逻辑都写在这里，已经移到业务层，\Business\Order\Modify.class.php
     * 关于订单拒绝可以直接调用
     * <AUTHOR>
     * @date  2020/09/28
     */
    public function orderRefundConfirm()
    {
        $orderNumStr     = I('ordernum', '', 'strval');
        $reqSerialNumber = I('req_serial_number', '', 'strval');

        $info = $this->getLoginInfo();

        $sid     = $info['sid'];
        $mid     = $info['memberID'];
        $account = $info['account'];

        if (empty($orderNumStr)) {
            $this->apiReturn(203, [], '订单号不存在');
        }

        $hotelBiz = new \Business\Order\Modify();
        $result   = $hotelBiz->handleOrderRefundConfirm($orderNumStr, $sid, $mid, $account, $reqSerialNumber);
        if (!isset($result['code'])) {
            $this->apiReturn(500, [], '异常出错');
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 套票子票强行退钱
     * @Author: linchen
     * 2019/08/09
     */
    public function forcedRefundMoneyForPacketSonTicket()
    {
        $orderNum   = I('post.order_num', 0);
        $num        = I('post.num', 0, 'intval');
        $totalMoney = I('post.total_money', 0);
        $totalMoney = float2int($totalMoney);
        if (!$orderNum) {
            $this->apiReturn(204, [], '订单号错误');
        }
        if ($num <= 0 || $totalMoney <= 0) {
            $this->apiReturn(204, [], '数量或者金额不能为0');
        }
        $sid      = $this->_sid;
        $memberId = $this->_mid;
        if ($sid != 1) {
            // 供应商员工是没有权限的 所以用登录账号的id
            $adminConfModel = new \Model\AdminConfig\AdminConfig();
            $checkPowerRes  = $adminConfModel->havePermission($sid, 25);
            if (!$checkPowerRes) {
                $this->apiReturn(400, [], '只有管理员或开通权限的账号主账号可以强制取消');
            }
            if ($this->_loginInfo['dtype'] == 6) {
                if (empty($this->_loginInfo['qx'])) {
                    $this->apiReturn(400, [], '员工账号未开通权限');
                } else {
                    $staffAuthArr = explode(',', $this->_loginInfo['qx']);
                    if (!in_array('special_order', $staffAuthArr)) {
                        $this->apiReturn(400, [], '员工账号未开通权限');
                    }
                }
            }
        }
        $ip        = get_client_ip();
        $ip        = ip2long($ip);
        $modifyBiz = new Modify();
        $result    = $modifyBiz->forcedRefundMoneyAndCancel($orderNum, $num, $totalMoney, $memberId, $ip, $sid);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }

    /**
     * 是否有取消的权限
     * <AUTHOR>
     * @date 2019/7/16
     *
     * @param $memberId
     * @param $sid
     *
     * @return array
     */
    private function _isHaveRefundAuth($memberId, $sid)
    {

    }

    /**
     * 根据订单号获取套票子票的打包成本价格
     * <AUTHOR>
     * @date   2019-08-12
     */
    public function getPackTicketCostMoneyByOrder()
    {
        $orderNum = I('get.order_num', '');
        if (!$orderNum) {
            $this->apiReturn(204, [], '订单号不能为空');
        }
        $modifyBiz = new Modify();
        $result    = $modifyBiz->getSonOrderCostMoney($orderNum);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }

    /**
     * 订单预约
     * <AUTHOR>
     * @date   2020-06-08
     */
    public function orderReservation()
    {
        $orderNum        = I('post.order_num', '');
        $sectionTimeId   = I('post.section_time_id', 0);
        $sectionTimeStr  = I('post.section_time_str', '');
        $reservationDate = I('post.reservation_time', '');
        if (!$orderNum) {
            $this->apiReturn(204, [], '订单号不能为空');
        }
        if (!strtotime($reservationDate)) {
            $this->apiReturn(204, [], '预约时间错误');
        }
        $moreData = [];
        $sid      = $this->_sid;
        $memberId = $this->_mid;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $moreData['subSid']   = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $moreData['subOpId']   = MemberLoginHelper::getLoginBusinessMember()->getMemberId();
            $sid      = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }
        $channel   = 16;
        $modifyBiz = new ReservationOrder();
        $result    = $modifyBiz->orderReservationService($orderNum, $sid, $memberId, $reservationDate,
            $channel, $sectionTimeId, $sectionTimeStr, false, '', $moreData);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }

    /**
     * 供应商/员工根据订单号设置订单标记
     * <AUTHOR>
     * @date   2020-7-3
     */
    public function setMark()
    {
        if (!in_array($this->_loginInfo['dtype'], [0, 1, 6, 18])) {
            $this->apiReturn(203, [], '非供应商或员工，无权限设置');
        }
        $orderNum = I('post.ordernum', '');
        $markType = I('post.mark_type', 0, 'intval');
        if (!$orderNum || !in_array($markType, [0, 1, 2, 3])) {
            $this->apiReturn(204, [], '参数错误');
        }
        //调用java接口
        $api = new \Business\JavaApi\Order\Modify();
        $res = $api->orderMarkModify($orderNum, $markType, $this->_loginInfo['sid'], $this->_mid);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 供应商/员工根据订单号设置订单备注
     * <AUTHOR>
     * @date   2020-7-3
     */
    public function setRemarks()
    {
        if (!in_array($this->_loginInfo['dtype'], [0, 1, 6, 18])) {
            $this->apiReturn(203, [], '非供应商或员工，无权限设置');
        }
        $orderNum = I('post.ordernum', '');
        $remarks  = I('post.remarks', '', 'strval,trim');
        if (!$orderNum) {
            $this->apiReturn(204, [], '订单号不能为空');
        }
        //调用java接口
        $api = new \Business\JavaApi\Order\Modify();
        $res = $api->orderRemarksModify($orderNum, $remarks, $this->_loginInfo['sid'], $this->_mid);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
        $this->apiReturn(200, [], '设置成功');
    }

    /***
     * 获取已加票数量
     * <AUTHOR>
     * @date 2021/11/5
     *
     * @return array
     */
    public function getAddedTicket()
    {
        $orderNum = I('post.order_num');
        if (!$orderNum) {
            $this->apiReturn(203, [], '参数缺失');
        }
        $queryParams = [[$orderNum], [16]];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderTrack', 'findByParams', $queryParams);

        $addNum = 0;
        if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
            $this->apiReturn(200, ['add_num' => $addNum], '获取成功');
        }

        $addNum = 0;
        foreach ($queryRes['data'] as $value) {
            $addNum = $addNum + $value['tnum'];
        }

        $this->apiReturn(200, ['add_num' => $addNum], '获取成功');
    }

    /**
     * 获取订单游客座位信息
     * author queyourong
     * date 2022/5/23
     */
    public function getShowTouristList()
    {
        $orderNum = I('ordernum');
        if (!$orderNum) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $showBiz = new ShowModify();
        $res = $showBiz->getShowTouristList($orderNum);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
        $this->apiReturn(200, $res['data'], '获取成功');

    }
}
