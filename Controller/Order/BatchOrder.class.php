<?php

namespace Controller\Order;
use Library\Controller;
use Business\Order\BatchOrder as BatchOrderBiz;

/**
 * 批量下单接口
 * <AUTHOR>
 */
class BatchOrder extends Controller {

    private $_sid           = 0;
    private $_batchOrderBiz = null;
    private $_login         = null; //登录全部信息
    private $_memberId      = 0; //登录账号id

    public function __construct()
    {
        parent::__construct();

        $this->_sid = $this->isLogin('ajax');

        $loginInfo       = $this->getLoginInfo();
        $this->_login    = $loginInfo;
        $this->_memberId = $loginInfo['memberID'];

        $this->_batchOrderBiz = new BatchOrderBiz();
    }


    /**
     * 获取操作人列表
     * <AUTHOR>
     * @date   2020-04-10
     */
    public function getOperatorList()
    {
        $result = $this->_batchOrderBiz->getOperatorList($this->_sid);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 获取批量下单记录
     * <AUTHOR>
     * @date   2020-04-08
     */
    public function batchOrderList()
    {
        $oper   = I('oper', 0, 'intval');
        $begin  = I('begin', '');
        $end    = I('end', '');
        $status = I('status', -1, 'intval');
        $ptype  = I('p_type', '', 'strval');
        $page   = I('page', 1, 'intval');
        $size   = I('size', 10, 'intval');

        $result = $this->_batchOrderBiz->batchOrderList($this->_sid, $begin, $end, $status, $ptype, $oper, $page, $size);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 批量下单记录导出
     * <AUTHOR>
     * @date 2020/04/09
     *
     */
    public function exportBatchOrderRecord()
    {
        $id = I('id', 0 , 'intval');

        $result = $this->_batchOrderBiz->exportBatchOrderRecord($this->_sid, $id);

        if (isset($result['code']) && $result['code'] == 200) {
            $this->excelReturn('批量下单记录', '批量下单记录', $result['data']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 批量下单提交成功页面信息接口
     * <AUTHOR>
     * @date   2020-04-09
     */
    public function batchOrderSubmitSuccessInfo()
    {
        //批量下单流水号
        $searialCode = I('serial_code', '', 'strval');

        $result = $this->_batchOrderBiz->batchOrderSubmitSuccessInfo($this->_sid, $searialCode);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }
}