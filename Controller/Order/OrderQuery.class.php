<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 5/9-009
 * Time: 16:37
 */

namespace Controller\Order;

use Business\JavaApi\Order\OrderTouristInfoExtendService;
use Business\Order\OrderTourist;
use Business\Order\OrderTrackQuery;
use Library\Controller;
use Model\Member\Member;
use Model\Member\MemberRelationship;
use Model\Order;
use Model\Order\OrderTools;
use Model\Order\OrderTrack;
use Model\Ota;
use Model\Product\Land;
use Model\Product\Ticket;

class OrderQuery extends Controller
{
    const ADMIN_ID       = 1;
    const MAX_EXCEL_ROWS = 30000; //允许导出的最大Excel条数
    private $members;
    private $lands;
    private $tickets;
    private $pt_chk; //部分验证的订单
    private $orders;
    private $memberId;
    private $output; //最终输出的数据
    private $machine_order_mode = [10, 12, 15, 16];
    private $order_mode         = [0, 20, 21, 22, 23];

    //用户登录数据
    private $loginInfo = null;

    public function __construct()
    {
        //统一进行初始化，有接口频率控制的业务
        parent::__construct();

        $this->isLogin('ajax', true);
        $this->loginInfo = $this->getLoginInfo('ajax', false, false);

        C(CONF_DIR . '/business.conf.php');
        $this->setCurrentMember();
    }

    public function __destruct()
    {
        unset($this->lands);
        unset($this->members);
        unset($this->tickets);
        unset($this->orders);
        unset($this->pt_chk);
    }

    /**
     * 设置当前用户ID
     *
     * @param  int  $memberId
     *
     * @return void
     */
    public function setCurrentMember($memberId = -1)
    {
        if ($memberId > 0) {
            $this->memberId = $memberId;
        } else {
            $this->memberId = $this->loginInfo['sid'];
        }

    }

    /**
     * 第三方订单查询
     * <AUTHOR>
     *
     * @param  string  $bDate  搜索起始时间
     * @param  string  $eDate  搜索结束时间
     * @param  string  $orderid  搜索的订单ID
     * @param  string  $thirdid  搜索的第三方订单ID
     * @param  int  $pageSize  搜索的一页数量
     * @param  int  $currentPage  搜索的当前页
     *
     * @date   2016-09-12
     *
     */
    public function third_order()
    {
        //参数过滤
        $bDate       = I('post.bDate');
        $eDate       = I('post.eDate');
        $orderId     = I('post.orderid');
        $thirdId     = I('post.thirdid');
        $pageSize    = I('post.pageSize');
        $currentPage = I('post.currentPage');

        if ($bDate == '') {
            $bDate = date('Y-m-d', time() - 3600 * 24);
        } else {
            $bDate = safe_str($bDate);
        }
        if ($eDate == '') {
            $eDate = date('Y-m-d');
        } else {
            $eDate = safe_str($eDate);
        }
        if ($orderId == '') {
            $orderId = 'null';
        } else {
            $orderId = safe_str($orderId);
        }
        if ($thirdId == '') {
            $thirdId = 'null';
        } else {
            $thirdId = safe_str($thirdId);
        }
        if ($pageSize == '') {
            $pageSize = 15;
        } else {
            $pageSize = intval($pageSize);
        }
        if ($currentPage == '') {
            $currentPage = 1;
        } else {
            $currentPage = intval($currentPage);
        }

        $bDate .= ' 00:00:00';
        $eDate .= ' 23:59:59';

        //这两个账号门票数据太多会导致报错，先限制查询时间，只支持订单号查询 - xiexy
        if ($orderId == 'null' && in_array($this->loginInfo['sid'], [4871146, 5961260])) {
            self::apiReturn(self::CODE_CREATED, [], '获取数据失败，请重试');
        }

        $offSet = ($currentPage - 1) * $pageSize;

        $allApiOrderModel = new Order\AllApiOrderModel();
        $sysConfigModel   = new \Model\Ota\SysConfig();
        $memberModel      = new Member();

        $fid     = $this->loginInfo['sid'];
        $ifAdmin = false;

        //分销商或者供应商
        $lidArr      = $sysConfigModel->getCsysLidArrByApplyDid($fid);
        $ticketIdArr = [];
        if ($lidArr) {
            //该登录人供应商身份时拥有的票类ID  根据票类来限制供应商范围
            $lidArrList = array_chunk($lidArr, 50);
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketArr  = [];
            foreach ($lidArrList as $lidArrItem) {
                if (!empty($lidArrItem)) {
                    $tempTicketIdArr = $javaApi->queryTicketBylandIdAndPay($lidArrItem, 'id', $fid);
                    $ticketArr       = array_merge($ticketArr, $tempTicketIdArr);
                }
            }

            if (empty($ticketArr)) {
                return $this->apiReturn(204, '无数据');
            }
            $ticketIdArr = array_unique(array_column($ticketArr, 'id'));
        }

        $res = $allApiOrderModel->getOrderNotForManager($fid, $bDate, $eDate, $orderId, $thirdId,
            $offSet, $pageSize, $ticketIdArr);

        $data = [];

        if (isset($res['data']) && is_array($res['data'])) {
            //返回的数据
            $data = $res['data'];

            foreach ($data as $k => $v) {
                $name              = $sysConfigModel->getInfoByCoopB($v['coopB']); //获取合作系统
                //$dname             = $memberModel->getMemberCacheById($v['fid'], 'dname');

                //根据订单详细信息获取分销链, 获取需要查询的用户(供应商直接下级分销商)
                $detailInfo = (new OrderTools('slave'))->getOrderDetailInfo($v['pftOrder'],
                    'orderid,aids');
                if (!empty($detailInfo)) {
                    $aids = $detailInfo['aids'];
                    $aidArr = explode(',', $aids);
                    if (count($aidArr) >= 2) {
                        $aid = $aidArr[1];
                    } else {
                        $aid = $v['fid'];
                    }
                } else {
                    $aid = $v['fid'];
                }
                // 查询用户信息
                $queryRes   = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds', [[$aid]]);
                if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                    $memberInfo = $queryRes['data'];
                }

                $data[$k]['fname'] = $memberInfo[0]['dname'] ?? '';
                $data[$k]['name']  = $name;
                $type = true;
                if ($this->loginInfo['sid'] != $v['fid']) {
                    $type = false;
                }
                $data[$k]['type']  = $type;
                $errMsg = $v['errormsg'];
                if (!is_null(json_decode($v['errormsg']))) {
                    $errMsgArr = json_decode($v['errormsg'], true);
                    if (isset($errMsgArr['msg'])) {
                        $errMsg = $errMsgArr['msg'];
                    }
                }
                $data[$k]['errormsg'] = $errMsg;
            }
        } else {
            self::apiReturn(self::CODE_CREATED, $res, '获取数据失败，请重试');
        }

        $count = 0;
        if (isset($res['count'])) {
            $count = $res['count'];
        } else {
            self::apiReturn(self::CODE_CREATED, $res, '获取数据失败，请重试');
        }
        unset($res);
        //票类名称
        $ticketArr = array_column($data, 'bCode');
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($ticketArr, 'title,id', '', 'title');

        $ticketName = [];
        if (!empty($ticketArr)) {
            foreach ($ticketArr as $ticket) {
                $ticketName[$ticket['ticket']['id']] = $ticket['land']['title'] . '(' . $ticket['ticket']['title'] . ')';
            }
        }

        $res = [
            'data'       => $data,
            'count'      => $count,
            'ticketName' => $ticketName,
            'ifAdmin'    => $ifAdmin,
            //'type'       => $type,
        ];
        if ($res['count'] > 0) {
            self::apiReturn(self::CODE_SUCCESS, $res, '获取成功');
        } else {
            self::apiReturn(self::CODE_SUCCESS, $res, '暂无数据');
        }
    }

    /**
     * 团购订单查询
     * <AUTHOR>
     *
     * @param  string  $bDate  搜索起始时间
     * @param  string  $eDate  搜索结束时间
     * @param  string  $orderid  搜索的团购订单ID
     * @param  int  $pageSize  搜索的一页数量
     * @param  int  $currentPage  搜索的当前页
     *
     * @date   2016-09-12
     *
     */
    public function group_order()
    {
        //参数过滤
        $orderId = I('post.orderid');

        // 如果订单为空， 则提示需要订单
        if (empty($orderId)) {
            self::apiReturn(self::CODE_PARAM_ERROR, [], '需要输入订单号');
        }

        $allApiOrderModel = new Order\AllApiOrderModel();

        //通过订单号搜索
        $res = $allApiOrderModel->getGrouponOrderById($orderId);
        if ($res) {
            $data  = $res['data'];
            $count = 1;
        } else {
            // res 返回 false
            self::apiReturn(self::CODE_SUCCESS, $res, '暂无数据');
        }

        //票类名称
        $ticketArr = array_column($data, 'bCode');
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($ticketArr, 'title,id', '', 'title');

        $ticketName = [];
        if (!empty($ticketArr)) {
            foreach ($ticketArr as $ticket) {
                $ticketName[$ticket['ticket']['id']] = $ticket['land']['title'] . '(' . $ticket['ticket']['title'] . ')';
            }
        }

        unset($res);
        $res = ['data' => $data, 'count' => $count, 'ticketName' => $ticketName];

        if ($res['count'] > 0) {
            //商家设置
            $shopType        = load_config('group_order_shop_typpe', 'business');
            $res['shopType'] = $shopType;
            self::apiReturn(self::CODE_SUCCESS, $res, '获取成功');
        } else {
            self::apiReturn(self::CODE_SUCCESS, $res, '暂无数据');
        }
    }

    /**
     *
     * 强制核销与退票
     * <AUTHOR>
     *
     * @param  string  $orderNum  订单ID
     * @param  int  $mode  操作类型  1、强制核销，2、强制退票
     * @param  int  $tnum  强制退票数量
     *
     * @date 2016-09-14
     *
     */
    public function force_chk()
    {
        $orderNum = I('post.orderNum');
        $mode     = I('post.mode');
        $tnum     = I('post.tnum');

        if ($orderNum == '' || $mode == '' || $tnum == '') {
            self::apiReturn(self::CODE_NO_CONTENT, [], '错误参数');
        }

        // 获取登陆者信息
        $loginInfoArr = $this->getLoginInfo();

        $tnum = intval($tnum);

        include_once '/var/www/html/ota/ALLFunction.php';
        $PFT_infunction = new \AllFunction();
        $pft_order_xml  = $PFT_infunction->Select_PFTorder($orderNum);

        if (!$pft_order_xml) {
            self::apiReturn(self::CODE_NO_CONTENT, [], '无此订单信息，请重新操作');
        }

        $UUtnum = (int)$pft_order_xml->Rec->UUtnum;
        $UUaids = (array)$pft_order_xml->Rec->UUaids;
        if ($UUaids[0] == 0) {
            $UUaids = (int)$pft_order_xml->Rec->UUaid;
        } else {
            $UUaids = explode(',', $UUaids[0]);
            $UUaids = $UUaids[0];
        }
        if ($UUaids != $loginInfoArr['sid'] && $loginInfoArr['sid'] != 1) {
            self::apiReturn(self::CODE_NO_CONTENT, [], '您不是订单直属销售者，无权限操作');
        }
        if ($mode == 1) {
            $res = $PFT_infunction->api_OrderCheck_IN($orderNum);
            if ($res == 200 || $res == 1077) {
                self::apiReturn(self::CODE_SUCCESS, [], '强制核销成功');
            } else {
                self::apiReturn(self::CODE_NO_CONTENT, [], '强制核销失败');
            }
        } else {
            if ($UUtnum < $tnum) {
                self::apiReturn(self::CODE_NO_CONTENT, [], '退票数量不能大于持有数量');
            }
            //剩余的票数
            $oStnum = 0;

            $oStatus     = $oStnum > 0 ? 4 : 3;
            $data        = [
                'oStatus'      => $oStatus,
                'oStnum'       => $oStnum,
                'handleStatus' => 0,
            ];
            $params      = ['pftOrder' => $orderNum];
            $allApiOrder = new Ota\AllApiOrderModel();
            $allApiOrder->updateTable($data, $params);

            $res = $PFT_infunction->Modify_Order($orderNum, $oStnum, true, -1, $loginInfoArr['memberID'], '执行强制退票');
            if ($res == '100') {
                self::apiReturn(self::CODE_SUCCESS, [], '强制退票成功');
            } else {
                self::apiReturn(self::CODE_NO_CONTENT, [], '强制退票失败');
            }
        }
    }

    /**
     * 订单操作记录查询
     * <AUTHOR>
     * @date   2016-11-14
     *
     * @param  string  $btime  搜索开始时间
     * @param  string  $etime  搜索截止时间
     * @param  int  $action  操作类型
     * @param  int  $source  操作终端
     * @param  int  $ordernum  订单号
     * @param  string  $fxsname  分销商名称
     * @param  string  $ename  操作员姓名
     * @param  int  $page  页数
     * @param  int  $size  每页显示个数
     * @param  int  $excel  是否导出数据
     * @param  int  $allPage  总页数
     *
     * @return array
     */
    public function getOrderRecord()
    {
        $btime    = I('btime', '', 'strval');
        $etime    = I('etime', '', 'strval');
        $action   = I('action', '', 'strval');
        $source   = I('source', '', 'strval');
        $ordernum = I('ordernum', '', 'intval');
        $fxsname  = I('fxsname', '', 'strval');
        $ename    = I('ename', '', 'strval');
        $page     = I('page', 1, 'intval');
        $size     = I('size', 10, 'intval');
        $excel    = I('excel', 0, 'intval');

        if ($size <= 0 || $size > 500 || $page <= 0) {
            $this->apiReturn(400, [], '参数有误');
        }
        $orderTrackModel = new OrderTrack();
        $relationModel   = new MemberRelationship();
        $memberModel     = new Member();
        $landModel       = new Land();
        $ticketModel     = new Ticket();
        $orderQueryModel = new Order\OrderQuery();

        //获取订单所有的操作类型和操作终端
        $orderAction = $orderTrackModel->getActionList();
        $orderSource = $orderTrackModel->getSourceList();

        $where    = [];
        $dtype    = $this->loginInfo['dtype'];
        $memberId = $this->loginInfo['memberID'];

        if ($ordernum != '' && is_numeric($ordernum)) {
            //没有联票数据
            $where['ordernum'] = strval($ordernum);
            //}
        }
        if ($btime != '' && $etime != '') {
            if (!strtotime($btime) || !strtotime($etime)) {
                self::apiReturn(self::CODE_NO_CONTENT, [], '请输入正确的时间格式');
            }
            $btime               = date('Y-m-d H:i:s', strtotime($btime));
            $etime               = date('Y-m-d H:i:s', strtotime($etime) + 86399);
            $where['insertTime'] = [['egt', $btime], ['elt', $etime]];
        }
        if (is_numeric($action)) {
            $where['action'] = intval($action);
        } else {
            $where['action'] = ['in', [0, 1, 2, 3, 5, 6, 7, 8]];
        }
        if (is_numeric($source)) {
            $where['source'] = intval($source);
        } else {
            $where['source'] = ['in', [0, 1, 2, 4, 5, 16, 20, 22, 23, 24, 25, 26]];
        }

        if ($dtype == 0 || $dtype == 1 || $dtype == 6) {

            // 现在员工拥有与供应商或者分销商同样的权限
            if ($dtype == 6) {
                $memberId = $this->memberId;
            }

            $idArr = $relationModel->getSonInfo($memberId);
            //员工ID和自己的ID
            $idArr[]['id'] = $memberId;
            $ids           = [];
            foreach ($idArr as $k => $v) {
                $ids[] = intval($v['id']);
            }
            if ($ename == '') {
                //没输入员工名字查询全部员工
                $where['oper_member'] = ['in', $ids];
            } elseif ($ename != '') {
                //输入的员工姓名
                $ename = strval($ename);
                //获取员工信息
                $member = $memberModel->getMemberInfoBySonName($ename, $memberId);
                if (!$member) {
                    //有可能输入的是供应商主账号的名称
                    $memberInfo = $memberModel->getMemberInfo($memberId, 'id');
                    if ($memberInfo['dname'] == $ename) {
                        $oper = $memberId;
                    } else {
                        self::apiReturn(self::CODE_NO_CONTENT, [], '操作人姓名有误！');
                    }
                } else {
                    $oper = $member['id'];
                }
                //判断员工id是否在可查询的员工id内
                if (in_array($oper, $ids)) {
                    $where['oper_member'] = $oper;
                } else {
                    self::apiReturn(self::CODE_NO_CONTENT, [], '操作人姓名有误！');
                }
            }
        }

        //admin账号查询
        if ($memberId == 1) {
            //有输入分销商名
            if ($fxsname != '') {
                $api = new \Business\JavaApi\Member\MemberQuery();
                $res = $api->isNameExists($fxsname);
                if ($res['code'] != 200 || !$res['data']) {
                    throw new \Exception("分销商名称有误！");
                }
                $fxsId     = $res['data'];
                $memberBiz = new \Business\Member\Member();
                $fxsInfo   = $memberBiz->getInfo($fxsId);

                if ($fxsInfo['dtype'] != 1) {
                    //找不到分销商
                    self::apiReturn(self::CODE_NO_CONTENT, [], '分销商名称有误！');
                } else {
                    //查询分销商下属员工
                    $idArr = $relationModel->getSonInfo($fxsId);
                    //员工ID和自己的ID
                    $idArr[]['id'] = $fxsId;
                    $ids           = [];
                    foreach ($idArr as $k => $v) {
                        $ids[] = intval($v['id']);
                    }
                    if ($ename == '') {
                        //没输入员工名字查询全部员工
                        $where['oper_member'] = ['in', $ids];
                    } elseif ($ename != '') {
                        //输入的员工姓名
                        $ename = strval($ename);
                        //获取员工信息
                        $member = $memberModel->getMemberInfoBySonName($ename, $memberId);
                        if (!$member) {
                            $memberInfo = $memberModel->getMemberInfo($memberId, 'id');
                            if ($memberInfo['dname'] == $ename) {
                                $oper = $memberId;
                            } else {
                                self::apiReturn(self::CODE_NO_CONTENT, [], '操作人姓名有误！');
                            }
                        } else {
                            $oper = $member['id'];
                        }
                        //判断员工id是否在可查询的员工id内
                        if (in_array($oper, $ids)) {
                            $where['oper_member'] = $oper;
                        } else {
                            self::apiReturn(self::CODE_NO_CONTENT, [], '操作人姓名有误！');
                        }
                    }
                }
            }

            //没输入分销商名输入了员工名，查询自己的员工
            if ($fxsname == '' && $ename != '') {
                $idArr         = $relationModel->getSonInfo($memberId);
                $idArr[]['id'] = $memberId;
                $ids           = [];
                foreach ($idArr as $k => $v) {
                    $ids[] = intval($v['id']);
                }
                //输入的员工姓名
                $ename = strval($ename);
                //获取员工信息
                $member = $memberModel->getMemberInfoBySonName($ename, $memberId);
                if (!$member) {
                    $memberInfo = $memberModel->getMemberInfo($memberId, 'id');
                    if ($memberInfo['dname'] == $ename) {
                        $oper = $memberId;
                    } else {
                        self::apiReturn(self::CODE_NO_CONTENT, [], '操作人姓名有误！');
                    }
                } else {
                    $oper = $member['id'];
                }
                //判断员工id是否在可查询的员工id内
                if (in_array($oper, $ids)) {
                    $where['oper_member'] = $oper;
                } else {
                    self::apiReturn(self::CODE_NO_CONTENT, [], '操作人姓名有误！');
                }
            }

            //分销商名和员工名都没输入，则查询所有自己的员工
            if ($fxsname == '' && $ename == '') {
                $idArr         = $relationModel->getSonInfo($memberId);
                $idArr[]['id'] = $memberId;
                $ids           = [];
                foreach ($idArr as $k => $v) {
                    $ids[] = intval($v['id']);
                }
                $where['oper_member'] = ['in', $ids];
            }
        }

        $orderTrackQueryLib = new OrderTrackQuery();

        if ($excel == 1) {
            //导出
            //$res = $orderTrackModel->getOrderRecord($where, 0, 10);

            //订单查询迁移二期
            $res = $orderTrackQueryLib->getOrderRecordNew(1, 1000, $where, true);
        } else {
            //查询
            //$res = $orderTrackModel->getOrderRecord($where, $page, $size);

            //订单查询迁移二期
            $res = $orderTrackQueryLib->getOrderRecordNew($page, $size, $where, true);
        }

        if ($res) {
            $operList     = array_unique(array_column($res, 'oper_member'));
            $tidList      = array_unique(array_column($res, 'tid'));
            $orderNumList = array_values(array_unique(array_column($res, 'ordernum')));

            //获取操作员姓名列表
            $dnameList    = [];
            $tmpDnameList = $memberModel->getMemberInfoByMulti($operList, 'id', 'id, dname');
            foreach ($tmpDnameList as $item) {
                $dnameList[$item['id']] = $item;
            }
            unset($tmpDnameList);

            //获取票名称列表
            $ticketList = [];
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketArr  = $javaApi->queryTicketInfoByIds($tidList, 'id, title, landid');
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticket) {
                    $ticketList[$ticket['ticket']['id']] = $ticket['ticket'];
                }
            }
            unset($ticketArr);

            //获取取票人信息列表
            $userList    = [];
//            $tmpUserList = $orderQueryModel->getOrderUserInfo($orderNumList);
            $queryParams = [$orderNumList];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder','queryOrderInfoByOrdernumList', $queryParams);
            $tmpUserList = [];
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $tmpUserList = $queryRes['data'];
                foreach ($tmpUserList as $item) {
                    $userList[$item['ordernum']] = $item;
                }
            }

            unset($tmpUserList);

            //获取景区名称列表
            $landIdList  = array_column($ticketList, 'landid');
            $landList    = [];
            //$tmpLandList = $landModel->getLandInfoByMuli($landIdList, 'id, title');

            $javaAPi     = new \Business\CommodityCenter\Land();
            $tmpLandList = $javaAPi->queryLandMultiQueryById($landIdList);
            foreach ($tmpLandList as $item) {
                $landList[$item['id']] = $item;
            }
            unset($tmpLandList);

            foreach ($res as $key => $value) {
                $operMember = $value['oper_member'];
                $tid        = $value['tid'];
                $ordernum   = $value['ordernum'];

                //操作员姓名
                if (isset($dnameList[$operMember])) {
                    $res[$key]['oper_member_name'] = $dnameList[$operMember]['dname'];
                } else {
                    $res[$key]['oper_member_name'] = '';
                }

                //票名称
                if (isset($ticketList[$tid])) {
                    $res[$key]['ticket_title'] = $ticketList[$tid]['title'];
                    $res[$key]['landid']       = $ticketList[$tid]['landid'];
                } else {
                    $res[$key]['ticket_title'] = '';
                    $res[$key]['landid']       = 0;
                }

                //景区ID
                $landId = $res[$key]['landid'];

                //获取取票人信息
                if (isset($userList[$ordernum])) {
                    $res[$key]['ordername'] = $userList[$ordernum]['ordername'];
                    $res[$key]['ordertel']  = $userList[$ordernum]['ordertel'];
                } else {
                    $res[$key]['ordername'] = '';
                    $res[$key]['ordertel']  = '';
                }

                //获取景区名称
                if (isset($landList[$landId])) {
                    $res[$key]['lid']        = $landList[$landId]['id'];
                    $res[$key]['land_title'] = $landList[$landId]['title'];
                } else {
                    $res[$key]['lid']        = 0;
                    $res[$key]['land_title'] = '';
                }

                $res[$key]['action_name'] = $orderAction[$value['action']];
                $res[$key]['source_name'] = $orderSource[$value['source']];
                if ($res[$key]['source_name'] == '内部接口') {
                    $res[$key]['source_name'] = '平台';
                }
                if ($res[$key]['action'] != 0 && $res[$key]['action'] != 5 && $res[$key]['action'] != 8) {
                    $res[$key]['tnum'] = -$res[$key]['tnum'];
                }

                if ($excel == 0) {
                    $res[$key]['oper'] = $res[$key]['oper_member_name'] . '(ID:' . $res[$key]['oper_member'] . ')';
                }
            }

            //结果
            if ($excel == 1) {
                $this->getOrderRecordExcel($res);
            } else {
                //$sum   = $orderTrackModel->countOrderRecord($where);

                //订单查询迁移二期
                $sum = $orderTrackQueryLib->countOrderRecordNew($where);

                $total = ceil($sum / $size);
                $data  = ['list' => $res, 'total' => $total, 'sum' => $sum];
                $this->apiReturn(self::CODE_SUCCESS, $data, '查询成功');
            }

        } else {
            $this->apiReturn(self::CODE_NO_CONTENT, '', '没有相关的数据');
        }
    }

    /**
     * 订单操作日志导出成excel
     * <AUTHOR>
     * @date   2016-11-17
     *
     * @param  array  $array  数据
     */
    public function getOrderRecordExcel($array)
    {
        $objPHPExcel = new \PHPExcel();// load_excel();
        $objWriter   = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getActiveSheet()->setTitle('订单操作日志查询');
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(20);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(20);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(10);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(16);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setAutoSize(true);
        $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setAutoSize(true);
        $objPHPExcel->getActiveSheet()->getColumnDimension('G')->setAutoSize(true);
        $objPHPExcel->getActiveSheet()->setCellValue('A1', '操作员');
        $objPHPExcel->getActiveSheet()->setCellValue('B1', '操作终端');
        $objPHPExcel->getActiveSheet()->setCellValue('C1', '操作类型');
        $objPHPExcel->getActiveSheet()->setCellValue('D1', '操作时间');
        $objPHPExcel->getActiveSheet()->setCellValue('E1', '订单号');
        $objPHPExcel->getActiveSheet()->setCellValue('F1', '景区+票类产品');
        $objPHPExcel->getActiveSheet()->setCellValue('G1', '数量');
        $objPHPExcel->getActiveSheet()->setCellValue('H1', '下单人');
        $objPHPExcel->getActiveSheet()->setCellValue('I1', '联系电话');

        $num = 2;
        foreach ($array as $k => $v) {
            $objPHPExcel->getActiveSheet()->setCellValue('A' . $num, $v['oper_member_name']);
            $objPHPExcel->getActiveSheet()->setCellValue('B' . $num, $v['source_name']);
            $objPHPExcel->getActiveSheet()->setCellValue('C' . $num, $v['action_name']);
            $objPHPExcel->getActiveSheet()->setCellValue('D' . $num, $v['insertTime']);
            $objPHPExcel->getActiveSheet()->setCellValue('E' . $num, $v['ordernum']);
            $objPHPExcel->getActiveSheet()->setCellValue('F' . $num, $v['land_title'] . '|' . $v['ticket_title']);
            $objPHPExcel->getActiveSheet()->setCellValue('G' . $num, $v['tnum']);
            $objPHPExcel->getActiveSheet()->setCellValue('H' . $num, $v['ordername']);
            $objPHPExcel->getActiveSheet()->setCellValue('I' . $num, $v['ordertel']);
            $num++;
        }

        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");
        header('Content-Disposition:attachment;filename="订单操作日志查询.xls"');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');
    }

    /**
     * 获取游客信息使用情况
     * <AUTHOR>
     * @date   2021-01-19
     *
     * @return array
     */
    public function getTicketCodeEnterLog()
    {
        $ordernum = I('ordernum', '', 'strval');
        $idx      = I('idx', 0, 'intval');
        if (!$ordernum || !$idx) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        //当前订单号，订单详情页实际进入的订单号
        $currentOrderNum = I('current_ordernum', '', 'strval');

        $sid = $this->loginInfo['sid'];
        $memberId = $this->loginInfo['memberID'];
        $result = (new OrderTourist())->getTicketCodeEnterLog($ordernum, $idx, $sid, $memberId, $currentOrderNum);
        $result['data']['pick_ticket_time'] = 0;
        $ext    = (new OrderTouristInfoExtendService())->queryOrderTouristInfoExtendByParam($ordernum,[$idx]);

        //有效期
        $result['data']['end_time']       = 0;
        //取票方式
        $result['data']['take_way']       = '未知';
        //取票方式  手机号取票的take_way_id = 手机号
        $result['data']['take_way_id']    = '';

        if ($ext['code'] == 200 && $ext['data']) {
            $printWayConf                       = load_config('print_way', 'orderSearch');
            $ext                                = $ext['data'][0];
            $extInfo                            = $ext['extInfo'] ? json_decode($ext['extInfo'], true) : [];
            $result['data']['pick_ticket_time'] = $extInfo['takeTime'] ?? 0;
            $result['data']['take_way']         = $printWayConf[$ext['takeWay']] ?? '未知';
            $result['data']['end_time']         = $ext['endTime'];
            $result['data']['take_way_id']      = $ext['takeContent'] ?? '';
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 通过订单号获取订单对应的产品类型
     * <AUTHOR>  Li
     * @date  2022-05-16
     */
    public function getPtypeByOrderNumArr()
    {
        $orderNumStr = I('order_num_str', '', 'strval');  //订单号信息  多个以逗号隔开
        if (!$orderNumStr) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单号缺失');
        }
        $orderNumArr  = explode(',', $orderNumStr);
        $orderTypeRes = (new \Business\NewJavaApi\Order\OrderDistribution())->getPtypeByOrderNumArr($orderNumArr);
        if ($orderTypeRes['code'] =! 200 || empty($orderTypeRes['data'])) {
            $this->apiReturn($orderTypeRes['code'], $orderTypeRes['data'], $orderTypeRes['msg']);
        }

        $this->apiReturn(self::CODE_SUCCESS, $orderTypeRes['data'], '订单数据获取成功');
    }
}
