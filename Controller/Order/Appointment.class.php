<?php

namespace Controller\Order;

use Business\CommodityCenter\Land;
use Library\Controller;
use Library\Tools;
use \Business\Order\Appointment as AppointmentBz;

class Appointment extends Controller
{

    private $_landList = [
        //商家id => [景区列表]
        3385    => [81274],
        8745933 => [287684],
        9943224 => [260269],
    ];
    /**
     * 提交团队预约信息
     *
     * @param array $orderParams 下单票类 [
     *  string $orderName 负责人名称
     *  string $idCard 负责人身份证
     *  string $telephoneNumber 负责人联系方式
     *  string $teamName 团队名称
     *  int $businessType 团队类型
     *  int $appointmentNum 预约人数
     *  int $explainNum 讲解人数
     *  string $dateTime 预约时间
     *  string $timeQuantum 分时预约时间段
     *  array $touristInfo 游客信息
     * ]
     *
     * @return array
     * <AUTHOR>
     * @date 2021/8/23
     *
     * */
    public function makeAppointment()
    {
        $orderParams       = I('post.');            //负责人名字
        $loginInfo = $this->getLoginInfo();
        //$idCard          = I('post.id_card', '', 'strval');                   //身份证号码
        //$telephoneNumber = I('post.telephone_number', '', 'strval'); //手机号
        //$teamName        = I('post.team_name', '', 'strval'); //团队名称
        //$businessType    = I('post.business_type', 12, 'intval'); //单位性质
        //$appointmentNum  = I('post.appointment_num', 0, 'intval'); //预约人数
        //$explainNum      = I('post.explain_num', 0, 'intval'); //讲解人数
        //$orderParams     = I('post.order_params', 0, 'intval'); //下单参数
        //$dateTime        = I('post.date_time', '', 'strval'); //票id
        //$timeQuantum     = I('post.time_quantum', '', 'strval'); //预约时间段
        //$touristInfo     = I('post.tourist_info', '', 'strval'); //游客信息
        $touristFile       = $_FILES['tourist_file']; //批量导入的文件
        if (empty($touristFile) && empty($orderParams['tourist_info'])){
            $this->apiReturn(203, "游客信息不能为空");
        }
        $orderParams['tourist_info'] = json_decode(html_entity_decode($orderParams['tourist_info']), true);
        $orderParams['proidList']    = json_decode(html_entity_decode($orderParams['proidList']), true);
        $orderParams = $this->_checkParams($orderParams);
        $appointmentBz = new AppointmentBz();
        $result = $appointmentBz->makeAppointment($orderParams, $loginInfo['sid'], $touristFile);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取预约记录
     *
     * @return array
     * <AUTHOR>
     * @date 2021/8/25
     *
     */
    public function getAppointmentList()
    {
        $loginInfo = $this->getLoginInfo();
        $keyWord      = I('post.key_word', '', 'strval'); //查询关键字
        $searchType   = I('post.search_type', 1, 'intval'); //查询类型 1：预约负责人 2：手机号 3：团队名称 4：订单号
        $orderStatus  = I('post.order_status', -1, 'intval'); //订单状态
        $beginTime    = I('post.begin_time', '', 'strval'); //开始时间
        $endTime      = I('post.end_time', '', 'strval'); //结束时间
        $timeType     = I('post.time_type', 1, 'intval'); //查询时间类型
        $businessType = I('post.business_type', -1, 'intval'); //单位性质
        $pageNum      = I('post.pageNum', 1, 'intval');
        $pageSize     = I('post.pageSize', 10, 'intval');
        $appointmentBz = new AppointmentBz();
        $result        = $appointmentBz->getAppointmentList($loginInfo['sid'], $keyWord, $searchType, $orderStatus, $beginTime,
            $endTime, $timeType, $businessType, $pageNum, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取景区列表信息
     *
     * @return array
     * <AUTHOR>
     * @date 2021/8/28
     *
     *
     */
    public function getLandList()
    {
        $loginInfo = $this->getLoginInfo();
        $landList  = $this->_landList[$loginInfo['sid']];
        if (empty($landList)) {
            $this->apiReturn(200, [], "商家未配置对应的景区信息");
        }
        $landApi   = new Land();
        $landInfo  = $landApi->queryLandInfoByIds($landList);

        $this->apiReturn(200, $landInfo, "获取成功");
    }

    /**
     * 获取票类列表
     *
     * @return array
     * <AUTHOR>
     * @date 2021/8/28
     *
     *
     */
    public function getTicketList()
    {
        $loginInfo = $this->getLoginInfo();
        $landId = I('post.land_id', 0, 'intval');
        if (empty($landId)) {
            $this->apiReturn(203,  [], "景区id不能为空");
        }
        $appointmentBz = new AppointmentBz();
        $result        = $appointmentBz->getTicketList($loginInfo['sid'], $landId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
    
    private function _checkParams($orderParams)
    {
        if (empty($orderParams['principal_name'])) {
            $this->apiReturn(203, [], "负责人姓名不能为空");
        }
        if (empty($orderParams['id_card']) || Tools::idcard_verify_number($orderParams['id_card'])) {
            $this->apiReturn(203,  [], "负责人身份证信息有误");
        }
        if (empty($orderParams['telephone_number']) || !isMobile($orderParams['telephone_number'])) {
            $this->apiReturn(203, [], "负责人手机号信息有误");
        }
        if (empty($orderParams['team_name'])) {
            $this->apiReturn(203,  [], "团队名称不能为空");
        }
        if (empty($orderParams['business_type'])) {
            $this->apiReturn(203,  [], "团队类型不能为空");
        }
        if (empty($orderParams['appointment_num'])) {
            $this->apiReturn(203,  [], "预约人数不能为空不能为空");
        }
        if (empty($orderParams['explain_num']) && $orderParams['explain_num'] !=0) {
            $this->apiReturn(203,  [], "讲解不能为空不能为空");
        }
        if (empty($orderParams['date_time'])) {
            $this->apiReturn(203,  [], "预约日期不能为空");
        }
        if (strtotime($orderParams['date_time']) < strtotime(date('Y-m-d', time()))) {
            $this->apiReturn(203,  [], "预约日期有误，请检查");
        }
        if (!$orderParams['proidList'] || !is_array($orderParams['proidList'])) {
            $this->apiReturn(400,  [], '合并付款参数错误');
        }

        return $orderParams;
    }
}