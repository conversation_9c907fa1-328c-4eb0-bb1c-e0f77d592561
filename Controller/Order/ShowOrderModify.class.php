<?php
/**
 * 演出订单改签
 *
 * <AUTHOR>  Li
 * @date    2021-08-24
 */

namespace Controller\Order;

use Business\Order\ShowModify;
use Library\Controller;

class ShowOrderModify extends Controller
{
    //当前登陆的主账号id
    private $_sid;
    //当前登陆的账号id
    private $_mid;
    //当前的账号类型
    private $_loginInfo;

    public function __construct()
    {
        $loginInfo = $this->getLoginInfo();

        $this->_sid       = $loginInfo['sid'];
        $this->_mid       = $loginInfo['memberID'];
        $this->_loginInfo = $loginInfo;
    }

    /**
     * 演出订单改签
     * <AUTHOR>  Li
     * @date   2021-08-18
     */
    public function showTicketChanging()
    {
        $ordernum   = I('post.ordernum', '', 'strval');     //订单号
        $changeTime = I('post.play_date', '', 'strval');    //改签日期
        $venueId    = I('post.venue_id', 0, 'intval');      //演出场馆id
        $aid        = I('post.aid', 0, 'intval');           //演出门票上级供应商id
        $roundId    = I('post.round_id', 0, 'intval');       //演出场次id
        $seats      = I('post.seat_ids', '', 'strval');       //演出座位id 多个以逗号隔开

        if (!$ordernum || !$changeTime || !$aid || !$venueId || !$roundId) {
            $this->apiReturn(204, [], '缺少参数');
        }
        $seatIdList = explode(',', $seats);

        $info     = $this->getLoginInfo();
        $opId     = $info['memberID'];
        $source   = 16;
        $orderBiz = new ShowModify();
        $res      = $orderBiz->showTicketChanging($ordernum, $changeTime, $this->_sid, $aid, $venueId, $roundId, $opId,
            $source, $seatIdList);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 获取演出改签库存信息
     * <AUTHOR>  Li
     * @date   2021-08-26
     */
    public function getShowInfoList()
    {
        $venues    = I('post.venues', '', 'strval');
        $date      = I('post.date', '', 'strval');
        $isReserve = I('post.is_reserve', 0, 'strval');    //是否是预约获取库存

        if (empty($venues) || empty($date)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $orderBiz = new ShowModify();
        $result  = $orderBiz->getShowInfoList($this->_sid, $venues, $date, $isReserve);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 演出预约
     * <AUTHOR>  Li
     * @date   2022-04-13
     */
    public function showReserve()
    {
        $ordernum   = I('post.ordernum', '', 'strval');     //订单号
        $changeTime = I('post.play_date', '', 'strval');    //预约日期
        $venueId    = I('post.venue_id', 0, 'intval');      //演出场馆id
        $aid        = I('post.aid', 0, 'intval');           //演出门票上级供应商id
        $roundId    = I('post.round_id', 0, 'intval');      //演出场次id
        $seats      = I('post.seat_ids', '', 'strval');     //演出座位id 多个以逗号隔开

        if (!$ordernum || !$changeTime || !$aid || !$venueId || !$roundId) {
            $this->apiReturn(204, [], '缺少参数');
        }
        $seatIdList = [];
        if ($seats) {
            $seatIdList = explode(',', $seats);
        }

        $info     = $this->getLoginInfo();
        $opId     = $info['memberID'];
        $source   = 16;
        $orderBiz = new ShowModify();
        $res      = $orderBiz->showReserve($ordernum, $changeTime, $this->_sid, $aid, $venueId, $roundId, $opId,
            $source, $seatIdList);

        $this->apiReturn($res['code'], [], $res['msg']);
    }
}
