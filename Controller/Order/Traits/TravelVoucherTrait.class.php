<?php
namespace Controller\Order\Traits;

use Business\NewJavaApi\Order\FeignClient;

/**
 * 旅游券相关trait
 */
trait TravelVoucherTrait
{
    public function judgeTravelVoucher($orderNum)
    {
        $ordersPTypeMap = (new FeignClient())->mapOrdersPType([$orderNum]);
        return $ordersPTypeMap[$orderNum] == 'Q';
    }

    public function bathJudgeTravelVoucher($orderNumArr)
    {
        return (new FeignClient())->mapOrdersPType($orderNumArr);
    }
}