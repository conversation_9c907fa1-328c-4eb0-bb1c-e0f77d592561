<?php
namespace Controller\Order\Traits;

use Library\Business\ReadExcel;

/**
 * @Author: CYQ19931115
 * @Date:   2018-01-04 10:52:54
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2018-01-04 11:07:20
 */
/**
 * 财务导出控制器trait
 */
trait WithdrawDepositTrait
{
    /**
     * 解析上传的体现报表数据excel数据
     * <AUTHOR>
     * @DateTime 2017-08-30T11:35:02+0800
     * @return   [type]                   [description]
     */
    public function processExcel()
    {
        $tmp_file = $this->getTempUploadFile();
        if ($tmp_file == false) {
            return [false, "没有上传文件！"];
        }
        $excelReader = new ReadExcel();
        $excel_data  = $excelReader->mirrorRead($tmp_file);
        unlink($tmp_file);
        return $excel_data;
    }
    /**
     * 获取临时上传文件
     * <AUTHOR>
     * @DateTime 2017-08-30T11:35:27+0800
     * @return   [string]                   [返回上传文件的临时文件名称]
     */
    private function getTempUploadFile()
    {
        if (count($_FILES) == 0) {
            return false;
        }
        return array_shift($_FILES)['tmp_name'];
    }
}