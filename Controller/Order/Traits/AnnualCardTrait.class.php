<?php

namespace Controller\Order\Traits;

use Business\AnnualCard\Package as AnnualPackageBiz;
use Business\AnnualCard\Package as PackageBiz;
use Business\JavaApi\Order\Query\OrderAidsSplitQuery;
use Business\Pay\PayBase;
use Business\Product\AnnualCardConst;
use Exception;
use Library\Kafka\KafkaProducer;
use Library\Util\AnnualUtil;
use Model\Product\AnnualCard;

/**
 * 年卡相关trait
 */
trait AnnualCardTrait
{

    /**
     * 年卡是否可以强制撤销
     * 一期：订单状态要是已使用、年卡下面只有一个正常（未生效或已生效）套餐才可以撤销
     * <AUTHOR>
     * @Date 2023/8/8 9:17
     * @param $orderNum
     * @return array
     */
    public function annualCanRevoke($orderNum): array
    {
        $annualModel = new AnnualCard();
        $virtualNo = $annualModel->getVirtualNoByOrdernum($orderNum);
        if (!$virtualNo) {
            return ['res' => false, 'msg' => '未找到相关年卡信息'];
        }
        $annualCard = $annualModel->getAnnualCard($virtualNo, 'virtual_no');
        if (!$annualCard) {
            return ['res' => false, 'msg' => '未找到相关年卡信息'];
        }
        if ($annualCard['status'] == AnnualCardConst::STATUS_REVOKE) {
            return ['res' => false, 'msg' => '年卡已被撤销，请不要重复执行'];
        }
        //如果是升级套餐的订单 不允许撤销
        $result = (new PackageBiz())->getPackageRecordList($annualCard['sid'], $annualCard['id'], $virtualNo, 1, 1, '', 0, 0, 0, 6);
        if ($result['code'] != 200) {
            return ['res' => false, 'msg' => '检测是否为升级套餐类型的订单时收到错误响应，请稍后重试'];
        }
        if (isset($result['data']['total']) && $result['data']['total']) {
            return ['res' => false, 'msg' => '升级套餐类型的订单不允许撤销'];
        }
        //如果是退未激活 不校验下面的多套餐情况
        if ($annualCard['status'] == AnnualCardConst::STATUS_NOT_ACTIVE) {
            return ['res' => true, 'msg' => ''];
        }
        //校验套餐是否只有一个正常的 参考/r/AnnualCard_Manage/getCardDetail
        $annualPackageBiz = new AnnualPackageBiz();
        //套餐状态 1未生效 2已生效 3已过期 4撤销 5失效
        $privilegeRes = $annualPackageBiz->getPeriodPackageByCardAndSid($annualCard['id'], $annualCard['sid'], '', [1, 2, 3, 5]);
        if ($privilegeRes['code'] != 200) {
            return ['res' => false, 'msg' => $privilegeRes['msg']];
        }
        //只要是多套餐 一期都不让撤销
        if (count($privilegeRes['data']) > 1) {
            //一期  多套餐不允许撤销
            //return ['res' => false, 'msg' => '当前订单对应年卡套餐非唯一套餐，撤销失败'];
        }
        //订单非最近的一笔，不允许撤销，需要按照顺序来撤销
        if ($privilegeRes['data'][0]['ordernum'] != $orderNum) {
            return ['res' => false, 'msg' => '当前订单对应年卡套餐非最新续费套餐，撤销失败'];
        }
        return ['res' => true, 'msg' => ''];
    }

    //年卡核销时候发送解冻的kafka消息
    public function judgeAndProceedThawAnnualFundingWhenCheck(string $orderNum, array $orderParams)
    {
        //自供自销不处理冻结和解冻
        if ($orderParams['payMode'] == 3) {
            return;
        }
        //判断是否是独立收款
        $isIndependent = $this->isIndependentReceiptAccount($orderNum, $orderParams['orderAid'], $orderParams['payMode']);
        if ($isIndependent) {
            return;
        }
        //不是独立收款的只处理末级分销
        //此场景，用于分销链上层没有优惠变动，由中台计算分销链解冻金额。
        //计算分销链解冻关键要素
        //- 末级分销商核销金额
        //- 末级分销商核销票数量
        //- 订单号
        $onlinePayModeList = load_config('online_pay_mode');
        $verificationMoney = 0;//不解冻金额
        //(在线支付+非独立收款)+余额的需要解冻
        if (in_array($orderParams['payMode'], $onlinePayModeList) || $orderParams['payMode'] == 0) {
            $verificationMoney = $orderParams['totalMoney'];
        }
        $dataKafka = [
            //订单号；为原下单订单号，会通过该订单号获取分销链和票数量信息 必填
            'orderId' => $orderNum,
            //交易号;本次核销的新交易号；会基于orderId+tradeNo做防重处理 必填
            'tradeNo' => genRandomString(10),
            //核销时间 Y-m-d H:i:s 必填
            'tradeDate' => date('Y-m-d H:i:s'),
            //操作人；memberId
            'operatorId' => $orderParams['opId'],
            //卖家核销金额；单位/分；不能小于0 必填
            //只有进了平台余额的这种，票付通收款的，才有金额， 其他的没有进平台账本的，是不会涉及到冻结解冻的，就都是0
            'verificationMoney' => $verificationMoney,
            //核销数量；不能小于1 必填
            'verificationNum' => $orderParams['ticketNum'],
        ];
        $this->thawAnnualFunding($dataKafka);
    }

    //退款时候计算需要解冻的金额 这里需要考虑分销链
    public function judgeAndProceedThawAnnualFundingWhenRefund(array $orderInfo, array $refundData): array
    {
        //自供自销的不处理
        if ($orderInfo['visitors'] == 3) {
            return [];
        }
        //先判断是否存在分销商下单
        //visitors 订单客户类型：1=游客,2=分销商,3=自供自销
        if ($orderInfo['visitors'] == 2) {
            $orderChain = (new OrderAidsSplitQuery())->getOrderDistributionChain([$orderInfo['ordernum']]);
            if ($orderChain['code'] != 200) {
                pft_log('annual_card_trait', '查询分销链失败：orderNum=' . $orderInfo['ordernum'] . ' msg=' . $orderChain['msg']);
                return [];
            }
            $tradeChain = $this->collateOrderChainThaw($orderChain['data']);
        } else {
            //不是分销的0元订单不处理
            //$refundData['refund_amount'] 末级退票金额
            //$refundData['refund_fee'] 末级退票手续费
            //结合Business/Order/OrderAsync.class.php的_marketingRefund
            //结合Business/Order/Refund.class.php的orderTradeRefund来看refundData的参数
            if ($refundData['refund_amount'] <= 0) {
                pft_log('annual_card_trait', 'judgeAndProceedThawAnnualFundingWhenRefund零元订单解冻orderNum=' . $orderInfo['ordernum']);
                return [];
            }
            //和上面数据结构统一下
            $tradeChain[$orderInfo['aid']] = [
                'memberId' => $orderInfo['aid'],
                'saleMoney' => $refundData['refund_amount'],
                'unfreeze' => $refundData['refund_amount'],
                //此人作为买家的结算金额是否大于他作为卖家的结算金额
                'isNegativeProfit' => false,
                'asBuyerMoney' => 0,
                'asSellerMoney' => $refundData['refund_amount'],
            ];
        }
        //需要过滤掉解冻金额为0的数据
        $tradeChain = array_values($tradeChain);
        AnnualUtil::debug(['购买年卡订单orderNum=' . $orderInfo['ordernum'] . '退款时需要解冻的计算结果', $tradeChain]);
        return array_filter($tradeChain, function ($item) {
            return $item['unfreeze'] > 0;
        });
    }

    //解冻
    public function thawAnnualFunding(array $dataKafka)
    {
        AnnualUtil::info($dataKafka);
        KafkaProducer::push('platform', 'transaction_verification_order', $dataKafka, $dataKafka['orderId']);
    }

    //判断订单收款账户是否是独立收款类型
    //参考Business/Order/BaseRefund::_isSelfReceive
    public function isIndependentReceiptAccount($orderNum, $orderAid, $orderPayMode): bool
    {
        $isSelfReceive = false;
        $onlinePayModeList = load_config('online_pay_mode');
        if (in_array($orderPayMode, $onlinePayModeList)) {
            $payBiz = new PayBase();
            try {
                $isSelfReceive = $payBiz->isIndependentCollection($orderNum, $orderAid);
            } catch (Exception $e) {
                pft_log('annual_card_trait', '判定是否独立收款时发生异常：orderNum=' . $orderNum . ' msg=' . $e->getMessage());
            }
        }
        return $isSelfReceive;
    }

    //退款需要算出分销链要解冻的金额 不需要发送kafka消息
    //冻结逻辑：分销订单各级仅冻结用金额交易的利润，授信支付不冻结，负利润不冻结
    //解冻逻辑反着来
    //整理分销链 明确退票时候每级需要冻结和解冻的金额
    //level所处的级别：1=第一级，0=既是1级也是末级，-1=最末级，2=中间级别
    //uu_ss_order.visitors 订单客户类型：1=游客,2=分销商,3=自供自销
    //这里的orderInfo参考(new OrderTools('localhost'))->getInfoForCancel 注意要取结果索引order_info
    //这里的orderChain参考(new OrderAidsSplitQuery())->getOrderDistributionChain
    //orderChain里的pmode参考business.conf.php的order_pay_mode 0-余额支付 2-授信支付 3-产品自销
    //visitors=2 level=0的订单 比如[{"orderid":"70009730317470","buyerid":"2379069","sellerid":"277078","level":"0","cost_money":"12800","sale_money":"12800","pmode":"2"}]
    //三级分销链的orderChain示例：[{"applyDid":13702234,"buyerid":13999333,"costMoney":2,"id":552741545,"level":-1,"memberRelationship":0,"orderMonth":1,"orderTime":1706087714,"orderid":"70608771418536","pmode":2,"saleMoney":2,"sellerid":13995818,"syncState":0,"updateTime":1706087714},{"applyDid":13702234,"buyerid":13995818,"costMoney":1,"id":552741546,"level":1,"memberRelationship":0,"orderMonth":1,"orderTime":1706087714,"orderid":"70608771418536","pmode":2,"saleMoney":2,"sellerid":13702234,"syncState":0,"updateTime":1706087714}]
    //三级分销链的orderInfo示例：{"aid":13995818,"apply_did":13702234,"begintime":"2024-01-24","callback":0,"certnum":0,"code":990354,"contacttel":"***********","ctime":null,"dtime":"2024-01-24 17:15:14","endtime":"2024-02-23","id":486207352,"lid":376694,"member":13999333,"onsale":0,"order_month":1,"order_time":1706087714,"ordermode":0,"ordername":"zt","ordernum":"70608771418536","ordertel":"***********","ordertime":"2024-01-24 17:15:13","pay_status":1,"pay_time":1706087714,"paymode":2,"personid":"","pid":1602816,"playtime":"2024-01-24","product_type":"I","remotenum":"","remsg":0,"salerid":70810608,"smserror":0,"status":1,"sync_state":0,"tid":1605411,"tnum":1,"totalmoney":2,"tprice":2,"unionCheck":0,"update_time":1706087714,"visitors":2,"confirm_op":13999333,"dstatus":0,"ifpack":0,"ifprint":0,"link_ordernum":"","orderid":"70608771418536","pack_order":"","pack_process":"","partner_mark":0,"sysorder":0,"tordernum":"","vcode":"","addtime":"2024-01-24 17:15:14","apply_id":13702234,"aprice":1,"can_refund":0,"can_take":1,"counter_price":1,"current_num":1,"flag":1,"lprice":1,"origin_num":1,"refund_num":0,"sale_price":2,"verified_num":1,"aids":"13702234,13995818","aids_money":"[{\"aid\":13702234,\"fid\":13995818,\"money\":2,\"payway\":0,\"type\":0}]","aids_price":"2"}
    //cost_money成本价 sale_money销售价 buyerid购买用户ID sellerid供应商ID apply_did顶级供应商ID
    public function collateOrderChainThaw(array $orderChain): array
    {
        $tradeChain = [];
        if (count($orderChain) == 1) {
            //简单的二级分销 判断供应商需要解冻的金额
            $tradeChain[$orderChain[0]['sellerid']] = [
                'memberId' => $orderChain[0]['sellerid'],
                'saleMoney' => $orderChain[0]['saleMoney'],
                //sellerid需要解冻的金额 取决于下级给他的入账和支付方式
                //自供自销的也不参与冻结和解冻
                'unfreeze' => $orderChain[0]['pmode'] == 0 ? $orderChain[0]['saleMoney'] : 0,
                //此人作为买家的结算金额是否大于他作为卖家的结算金额
                'isNegativeProfit' => 0 > $orderChain[0]['saleMoney'],
                //此人作为买家的结算金额
                'asBuyerMoney' => 0,
                //此人作为卖家的结算金额
                'asSellerMoney' => $orderChain[0]['saleMoney'],
            ];
        } else {
            //多级分销 需要计算顶级 末级 中间级 不通支付方式的解冻金额
            //年卡不需要考虑一个订单多张票（因为目前都是一个订单一张票）和优惠
            //分销链先按照level升序排列下
            usort($orderChain, function ($a, $b) {
                if ($a['level'] == $b['level']) {
                    return 0;
                }
                return $a['level'] < $b['level'] ? -1 : 1;
            });
            $buyerArr = array_column($orderChain, null, 'buyerid');
            $sellerArr = array_column($orderChain, null, 'sellerid');
            foreach ($orderChain as $item) {
                //最末级
                if ($item['level'] == -1) {
                    //计算上级需要解冻
                    //这时的buyerid不需要解冻
                    //0-余额支付 2-授信支付 3-产品自销
                    $sellerMargins = $item['saleMoney'] - $buyerArr[$item['sellerid']]['saleMoney'];
                    $tradeChain[$item['sellerid']] = [
                        'memberId' => $item['sellerid'],
                        'saleMoney' => $item['saleMoney'],
                        //sellerid需要解冻的金额 取决于下级给他的入账和支付方式
                        //如果是授信支付或者负利润则解冻金额为0否则就是解冻利润
                        //这里组装好后需要使用tradeChain的时候判断解冻金额为0的就不传了，这里计算0的目的是便于调试
                        'unfreeze' => ($item['pmode'] == 2 || $sellerMargins < 0) ? 0 : $sellerMargins,
                        //此人作为买家的结算金额是否大于他作为卖家的结算金额
                        'isNegativeProfit' => $sellerMargins < 0,
                        'asBuyerMoney' => $buyerArr[$item['sellerid']]['saleMoney'],
                        'asSellerMoney' => $item['saleMoney'],
                    ];
                } elseif ($item['sellerid'] == $item['applyDid']) {
                    //顶级
                    $sellerMargins = $item['saleMoney'] - 0;
                    $tradeChain[$item['sellerid']] = [
                        'memberId' => $item['sellerid'],
                        //此人给下级的结算金额
                        'saleMoney' => $item['saleMoney'],
                        //sellerid需要解冻的金额 取决于下级给他的入账和支付方式
                        'unfreeze' => ($item['pmode'] == 2 || $sellerMargins < 0) ? 0 : $sellerMargins,
                        'isNegativeProfit' => $sellerMargins < 0,
                        'asBuyerMoney' => 0,
                        'asSellerMoney' => $item['saleMoney']
                    ];
                    //顶级的下级
                    $margins = $sellerArr[$item['buyerid']]['saleMoney'] - $buyerArr[$item['buyerid']]['saleMoney'];
                    $tradeChain[$item['buyerid']] = [
                        'memberId' => $item['buyerid'],
                        //此人给下级的结算金额
                        'saleMoney' => $buyerArr[$item['buyerid']]['saleMoney'],
                        //需要解冻的金额 取决于下级给他的入账和支付方式 也就是他作为卖家给买家的结算价
                        'unfreeze' => ($sellerArr[$item['buyerid']]['pmode'] == 2 || $margins < 0) ? 0 : $margins,
                        //是否负利润 此人作为买家的结算金额是否大于他作为卖家的结算金额
                        'isNegativeProfit' => $margins < 0,
                        'asBuyerMoney' => $buyerArr[$item['buyerid']]['saleMoney'],
                        'asSellerMoney' => $sellerArr[$item['buyerid']]['saleMoney']
                    ];
                } else {
                    //剩余的中间级
                    if (!isset($tradeChain[$item['sellerid']])) {
                        $sellerMargins = $item['saleMoney'] - $buyerArr[$item['sellerid']]['saleMoney'];
                        $tradeChain[$item['sellerid']] = [
                            'memberId' => $item['sellerid'],
                            //此人给下级的结算金额
                            'saleMoney' => $item['saleMoney'],
                            //sellerid需要解冻的金额 取决于下级给他的入账和支付方式
                            'unfreeze' => ($item['pmode'] == 2 || $sellerMargins < 0) ? 0 : $sellerMargins,
                            //此人作为买家的结算金额是否大于他作为卖家的结算金额
                            'isNegativeProfit' => $sellerMargins < 0,
                            'asBuyerMoney' => $buyerArr[$item['sellerid']]['saleMoney'],
                            'asSellerMoney' => $item['saleMoney']
                        ];
                    }
                    if (!isset($tradeChain[$item['buyerid']])) {
                        $margins = $sellerArr[$item['buyerid']]['saleMoney'] - $buyerArr[$item['buyerid']]['saleMoney'];
                        $tradeChain[$item['buyerid']] = [
                            'memberId' => $item['buyerid'],
                            //此人给下级的结算金额（此人作为卖家给的结算金额）
                            'saleMoney' => $sellerArr[$item['buyerid']]['saleMoney'],
                            //sellerid需要解冻的金额 取决于下级给他的入账和支付方式
                            'unfreeze' => ($sellerArr[$item['buyerid']]['pmode'] == 2 || $margins < 0) ? 0 : $margins,
                            //此人作为买家的结算金额是否大于他作为卖家的结算金额
                            'isNegativeProfit' => $margins < 0,
                            'asBuyerMoney' => $buyerArr[$item['buyerid']]['saleMoney'],
                            'asSellerMoney' => $sellerArr[$item['buyerid']]['saleMoney']
                        ];
                    }
                }
            }
        }
        return $tradeChain;
    }
}