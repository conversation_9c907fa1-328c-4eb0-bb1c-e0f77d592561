<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 3/8-008
 * Time: 18:18
 */

namespace Controller\Order;

use Business\Authority\DataAuthLogic;
use Business\Authority\StaffCheckAuthBiz;
use Business\Order\Refund;
use Business\ReservationSpecialTeam\TeamOrder;
use Controller\Order\Traits\TravelVoucherTrait;
use Library\ApplicationContext;
use Library\Constants\MemberConst;
use Library\Controller;
use Model\AdminConfig\AdminConfig;
use Model\Member\Member;
use Model\Order\OrderHandler;
use Model\Order\OrderRefer;
use Model\Order\OrderTools;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\TradeRecord\OrderRefund;

class Handler extends Controller
{
    use TravelVoucherTrait;
    const __SPECIAL_ORDER__      = 25; //特殊订单管理目录权限
    const __FORCE_CHECK_SOURCE__ = 46; //强制核销track_source
    const __LOG_PATH__           = 'force_check'; //日志记录目录

    private $_loginInfo;
    private $_sid;
    private $_memberId;
    private $_dtype;

    //未使用，已过期，撤改，部分使用的订单可以被强制核销
    private $_checkStatus = [0, 2, 5, 7, 10];

    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo();
        $this->_sid       = $this->_loginInfo['sid'];
        $this->_memberId  = $this->_loginInfo['memberID'];
        $this->_dtype     = $this->_loginInfo['dtype'];
    }

    public function refundList()
    {
        $ordernum             = I('post.ordernum');
        $status               = I('post.refund_status', -1);
        $cancel_time_begin    = I('post.cancel_time_begin', 0, 'strtotime');
        $cancel_time_end      = I('post.cancel_time_end', 0, 'strtotime');
        $handler_tm_begin     = I('post.handler_tm_begin', 0, 'strtotime');
        $handler_tm_end       = I('post.handler_tm_end', 0, 'strtotime');
        $auto_refund_tm_begin = I('post.auto_refund_tm_begin', 0, 'strtotime');
        $auto_refund_tm_end   = I('post.auto_refund_tm_end', 0, 'strtotime');
        $apply_did            = $this->_sid;
        $model                = new OrderRefund();
        $data                 = $model->getRefundJournalList($cancel_time_begin, $cancel_time_end,
            $handler_tm_begin, $handler_tm_end, $auto_refund_tm_begin, $auto_refund_tm_end,
            $status, $ordernum, $apply_did);
        $code                 = parent::CODE_SUCCESS;
        $msg                  = '';
        if (!$data) {
            $code = parent::CODE_NO_CONTENT;
            $msg  = "查询数据为空";
        }
        parent::apiReturn($code, $data, $msg);
    }

    /**
     * 拒绝退款操作
     */
    public function refuseRefund()
    {
        $refund_id = I('post.id');
        $memo      = ('post.memo');
        if (empty($memo)) {
            parent::apiReturn(parent::CODE_AUTH_ERROR, [], '请输入备注信息');
        }
        $business = new Refund();
        $result   = $business->refuseRefund($refund_id, $memo, $this->_loginInfo['memberID']);
        parent::apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 手工操作退款
     */
    public function manualRefund()
    {
        $refund_id = I('post.id');
        $memo      = "操作员ID:{$this->_loginInfo['memberID']},登录IP:" . get_client_ip(0, true);
        $business  = new Refund();
        $result    = $business->orderTradeRefund($refund_id, [], $memo);
        if ($result['code'] == 200) {
            parent::apiReturn(parent::CODE_SUCCESS, [], $result['msg']);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '操作失败,错误信息:' . $result['msg']);
    }

    /**
     * 获取可以强制核销的订单
     * Create by zhangyangzhen
     * Date: 2019/5/31
     * Time: 18:01
     *
     * @param string ordernum 订单号
     *
     * @return array
     */
    public function getSpecialOrder()
    {
        //用户权限判断
        if ($this->_sid != 1) {
            // 供应商员工是没有权限的 所以用登录账号的id
            $adminConfModel = new AdminConfig();
            $checkPowerRes  = $adminConfModel->havePermission($this->_sid, self::__SPECIAL_ORDER__);
            if (!$checkPowerRes) {
                $this->apiReturn(self::CODE_AUTH_ERROR, [], '只有管理员或开通权限的账号主账号可以强制取消');
            }
            if ($this->_dtype == 6) {
                if (empty($this->_loginInfo['qx'])) {
                    $this->apiReturn(self::CODE_AUTH_ERROR, [], '员工账号未开通权限');
                } else {
                    $staffAuthArr = explode(',', $this->_loginInfo['qx']);
                    if (!in_array('special_order', $staffAuthArr)) {
                        $this->apiReturn(self::CODE_AUTH_ERROR, [], '员工账号未开通权限');
                    }
                }
            }
        }

        $orderNum = I('post.ordernum');

        if (empty($orderNum)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单号不能为空');
        }
        //旅游券逻辑
        $isTravelVoucher = $this->judgeTravelVoucher($orderNum);
        $ordernum = (string)$orderNum;
        if($isTravelVoucher){
            $tvInvoiceApi = new \Business\JsonRpcApi\TravelVoucherService\SpecialOrders();
            $tvOrderInfo  = $tvInvoiceApi->getOrderForVerify($orderNum, $this->_memberId ,$this->_sid);
            if ($tvOrderInfo['code'] != 200) {
                pft_log('/Handler/getSpecialOrder/error',
                    '强制核销查询，未查到订单信息' . json_encode(['ordernum' => $orderNum, 'sid' => $this->_sid, 'tmpOrderInfo'=>$tvOrderInfo]));
                $this->apiReturn($tvOrderInfo['code'], [], $tvOrderInfo['msg']);
            }
            $orderList['list'] = $tvOrderInfo['data']['data'];
            $orderList['total'] = $tvOrderInfo['data']['total'];
            $this->apiReturn(self::CODE_SUCCESS, $orderList, '查询成功');
        }
        else{
            $data     = $this->_getSpecialInfo($ordernum, $this->_sid);
            $this->apiReturn(self::CODE_SUCCESS, $data, '查询成功');
        }
    }

    /**
     * 获取可以强制核销的订单信息
     * Create by zhangyangzhen
     * Date: 2019/5/31
     * Time: 17:01
     *
     * @param string $ordernum 订单号
     * @param int $sid //登录用户的主账号ID
     *
     * @return array
     */
    private function _getSpecialInfo($searchOrdernum, $sid)
    {
        $searchOrdernum = trim($searchOrdernum);
        if (empty($searchOrdernum)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '订单号不能为空');
        }

        $ordernum   = [$searchOrdernum];
        $orderModel = new OrderTools();
        $linkOrder  = $orderModel->getLinkSubOrderInfo($ordernum, 'orderid'); //联票订单
        $packOrder  = $orderModel->getPackSubOrder($ordernum, 'orderid, pack_order'); //套票订单

        if ($linkOrder) {
            $linkOrder = array_column($linkOrder, 'orderid');
            $ordernum  = array_merge($linkOrder, $ordernum);
        }

        if ($packOrder) {
            $packOrder = array_column($packOrder, 'orderid');
            $ordernum  = array_merge($packOrder, $ordernum);
        }
        $ordernum = array_unique($ordernum);

        $packOrderRes = $orderModel->getOrderAddonInfo($ordernum, 'orderid, pack_order');

        $packOrderArr = [];
        foreach ($packOrderRes as $key => $val) {
            if (isset($val['pack_order']) && !empty($val['pack_order'])) {
                $packOrderArr[$val['orderid']] = $val['pack_order'];
            }
        }

        $orderInfo = $orderModel->getOrderListNew($ordernum, 'ordernum,ordertime,status,pay_status,lid,tid,tnum,aid,code,salerid,dtime,ordermode,member');

        if (empty($orderInfo)) {
            $this->apiReturn(self::CODE_NO_CONTENT, [], '找不到符合条件的订单');
        }

        $payConf     = load_config('pay_status', 'orderSearch');
        $orderStatus = load_config('order_status', 'orderSearch');

        $landIds   = array_column($orderInfo, 'lid');
        $ticketIds = array_column($orderInfo, 'tid');
        $aids      = array_column($orderInfo, 'aid');

        //$landModel   = new Land();
        $ticketModel = new Ticket();
        $memberModel = new Member();

        //获取景区、门票、用户信息
        //$landInfo     = $landModel->getLandInfoByMuli($landIds, 'id, title', 1);

        $javaAPi      = new \Business\CommodityCenter\Land();
        $landInfo     = $javaAPi->queryLandMultiQueryById($landIds);
        $landInfo     = array_column($landInfo, 'title', 'id');
        $ticketInfo   = $ticketModel->getTicketList($ticketIds, 'id, title, apply_did');
        $supplierInfo = $memberModel->getMemberInfoByMulti($aids, 'id', 'id, dname', true);

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $data = [];
        foreach ($orderInfo as $key => $val) {
            // 数据权限限制
            if ($dataAuthLimit->hasLidBeenLimit($val['lid'])) {
                // 如果是主票订单被禁止则不展示子票信息
                if ($val['ordernum'] == $searchOrdernum) {
                    return ['list' => [], 'total' => 0];
                }
                continue;
            }
            $isCheck = true;
            $isPack  = false;

            if (!empty($packOrderArr) && $packOrderArr[$val['ordernum']] != 1) {
                $isPack = true;
            }

            //非自供应订单不展示
            if ($sid != 1 && $sid != $ticketInfo[$val['tid']]['apply_did']) {
                unset($orderInfo[$key]);
                $orderInfo['total'] = $orderInfo['total'] - 1;
                continue;
            }

            //未支付订单不能核销
            if ($val['pay_status'] == 2) {
                $isCheck = false;
            }

            //未使用，已过期，撤改，部分使用的订单可以被强制核销
            if (!in_array($val['status'], $this->_checkStatus)) {
                $isCheck = false;
            }

            $data['list'][$key]['ordernum']    = $val['ordernum'];
            $data['list'][$key]['tnum']        = $val['tnum'];
            $data['list'][$key]['ordertime']   = $val['ordertime'];
            $data['list'][$key]['pay_status']  = $payConf[$val['pay_status']];
            $data['list'][$key]['status_info'] = $orderStatus[$val['status']];
            $data['list'][$key]['aid']         = $supplierInfo[$val['aid']]['dname'];
            $data['list'][$key]['lid']         = $landInfo[$val['lid']];
            $data['list'][$key]['tid']         = $ticketInfo[$val['tid']]['title'];
            $data['list'][$key]['isCheck']     = $isCheck;
            $data['list'][$key]['isPack']      = $isPack;
            $data['list'][$key]['p_type']      = $ticketInfo[$val['tid']]['p_type'];
        }
        $data['list'] = array_values($data['list'] ?? []);
        $data['total'] = count($data['list']);

        return $data ? $data : [];
    }

    /**
     * 强制核销 -- 走快速验证接口
     * Create by zhangyangzhen
     * Date: 2019/6/5
     * Time: 14:23
     *
     * @param string ordernum 订单号
     */
    public function check()
    {
        $ordernum = I('post.ordernum', '', 'strval,trim');
        $date     = date('Y-m-d H:i:s');
        $productType = I('p_type', '');

        //旅游券逻辑
        if($productType == 'Q'){
            $tvInvoiceApi = new \Business\JsonRpcApi\TravelVoucherService\SpecialOrders();
            $tvOrderInfo  = $tvInvoiceApi->forceVerifyOrderForVerify($ordernum, $this->_memberId,$this->_sid);
            if ($tvOrderInfo['code'] != 200) {
                pft_log('/Handler/check/error',
                    '强制核销操作' . json_encode(['ordernum' => $ordernum, 'sid' => $this->_sid, 'tmpOrderInfo'=>$tvOrderInfo]));
                $this->apiReturn($tvOrderInfo['code'], [], $tvOrderInfo['msg']);
            }
            else{
                $this->apiReturn(200, [], '强制核销成功');
            }
        }


        $orderTools = new OrderTools();
        $orderInfo  = $orderTools->getOrderListNew([$ordernum], 'ordernum, aid, pay_status, status, lid');
        $orderInfo  = $orderInfo[0];

        //判断订单支付状态
        if ($orderInfo['pay_status'] == 2) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '未支付订单不能核销');
        }

        //判断订单状态
        if (!in_array($orderInfo['status'], $this->_checkStatus)) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '该订单状态不能核销');
        }

        $landId    = $orderInfo['lid'];
        $landModel = new Land();
        $landInfo  = $landModel->getLandInfo($landId, false, 'apply_did, salerid, p_type');
        $ptype     = $landInfo['p_type'];

        if($this->_dtype == MemberConst::ROLE_STAFF) {
            // 员工要额外判断权限
            if(!StaffCheckAuthBiz::getInstance()->isStaffHasCheckPermission($this->_memberId, $landId)){
                $this->apiReturn(self::CODE_AUTH_ERROR, [], '该员工账号无此订单的核销权限');
            }
        }
        
        //用户权限判断
        //在特殊团队预定订单列表员工可以直接验证
        if ($this->_dtype == 6 && $orderInfo['ordermode'] != TeamOrder::RESERVATION_SPECIAL_CHANNEL) {
            if (empty($this->_loginInfo['qx'])) {
                $this->apiReturn(self::CODE_AUTH_ERROR, [], '员工账号未开通权限');
            } else {
                $staffAuthArr = explode(',', $this->_loginInfo['qx']);
                if (!in_array('special_order', $staffAuthArr)) {
                    $this->apiReturn(self::CODE_AUTH_ERROR, [], '员工账号未开通权限');
                }
            }
        } else {
            if ($landInfo['apply_did'] != $this->_sid && !$this->isSuper()) {
                $this->apiReturn(self::CODE_AUTH_ERROR, [], '非产品供应商无权核销');
            }
        }

        $orderModel = new OrderHandler();
        ApplicationContext::set('device_key', "10000000000000000000000000000001");
        //演出捆绑的不同步验证子票或主票
        if ($ptype == 'H') {
            $result = $orderModel->CheckOrderSimply($ordernum, $this->_memberId, null, '强制核销',
                self::__FORCE_CHECK_SOURCE__, $date, 0, 0, false, '', false);
        } else {
            $result = $orderModel->CheckOrderSimply($ordernum, $this->_memberId, null, '强制核销',
                self::__FORCE_CHECK_SOURCE__, $date, 0, 0, false, '', true);
        }

        $content = "订单号：$ordernum|操作员ID：$this->_memberId|订单信息：" . json_encode($orderInfo) . '|核销结果：' . json_encode($result);
        pft_log(self::__LOG_PATH__, $content);

        if ($result === true) {
            $this->apiReturn(self::CODE_SUCCESS, [], '强制核销成功');
        } else {
            $this->apiReturn($result['code'], [], $result['msg']);
        }
    }
}
