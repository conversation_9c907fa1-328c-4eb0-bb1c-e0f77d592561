<?php

namespace Controller\OpenPlatformMiniApp;

use Library\Controller;
use \Business\OpenPlatformMiniApp\OpenPlatformMiniApp as OpenPlatformMiniAppBs;

class OpenPlatformMiniApp extends Controller
{
    /**
     * 获取可以设置的所有类目
     *
     * @return array
     * <AUTHOR>
     * @date 2020/7/13
     */
    public function getAllCategories()
    {
        $id = I('post.id', 0, 'intval');           //托管小程序的主键id
        if (empty($id)) {
            $this->apiReturn(203, "参数错误");
        }
        $openPlatFormMiniAppBs = new OpenPlatformMiniAppBs();
        $result                = $openPlatFormMiniAppBs->getAllCategories($id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取已设置的所有类目
     *
     * @return array
     * <AUTHOR>
     * @date 2020/7/13
     */
    public function getCategories()
    {
        $id = I('post.id', 0, 'intval');           //托管小程序的主键id
        if (empty($id)) {
            $this->apiReturn(203, "参数错误");
        }
        $openPlatFormMiniAppBs = new OpenPlatformMiniAppBs();
        $result                = $openPlatFormMiniAppBs->getCategories($id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 添加类目
     *
     * @return array
     * <AUTHOR>
     * @date 2020/7/13
     *
     *
     */
    public function addCategory()
    {
        $id           = I('post.id', '', 'intval');           //托管小程序的主键id
        $categoryInfo = I('post.categoryInfo', '', 'strval'); //类目配置信息
        if (empty($id) || empty($categoryInfo)) {
            $this->apiReturn(203, "参数错误");
        }
        $categoryInfo          = json_decode($categoryInfo, true);
        $openPlatFormMiniAppBs = new OpenPlatformMiniAppBs();
        $result                = $openPlatFormMiniAppBs->addCategory($id, $categoryInfo);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除类目
     *
     * @return array
     * <AUTHOR>
     * @date 2020/7/14
     *
     *
     */
    public function deleteCategory()
    {
        $id                    = I('post.id', '');           //托管小程序的主键id
        $first                 = I('post.first', '');           //一级类目id
        $second                = I('post.second', '');          //二级类目id
        $openPlatFormMiniAppBs = new OpenPlatformMiniAppBs();
        $result                = $openPlatFormMiniAppBs->deleteCategory($id, $first, $second);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 上传临时素材到微信
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/13
     *
     */
    public function uploadImageToWx()
    {
        $appId = I('post.appId', '');           //托管小程序的appid
        if (empty($appId)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $fileSrc = IMAGE_UPLOAD_DIR . $_FILES["image"]["name"];
        //将上传的图片移动至临时路径
        move_uploaded_file($_FILES["image"]["tmp_name"], IMAGE_UPLOAD_DIR . $_FILES["image"]["name"]);
        $openPlatFormMiniAppBs = new OpenPlatformMiniAppBs();
        $result                = $openPlatFormMiniAppBs->uploadImageToWx($appId, $fileSrc);
        $returnData['mediaId'] = $result['data'];
        unlink($fileSrc);
        $this->apiReturn($result['code'], $returnData, $result['msg']);
    }
}