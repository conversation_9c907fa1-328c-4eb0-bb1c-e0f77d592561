<?php

namespace Controller\DistributionCenter;

use Business\DistributionCenter\DistributorOperation;
use Library\Controller;
use Business\Member\MemberRelation as MemberRelationBiz;

class Distributor extends Controller
{
    protected $loginInfo;

    public function __construct()
    {
        // 判断是否登录
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 创建分销商查询任务
     */
    public function createDistributorQueryTask()
    {
        $requestId = I('rid', '', 'strval');
        $expirationType = I('expiration_type', [], 'intval');
        $queryAccount = I('query_account', '', 'strval');
        if (!$requestId && !$expirationType && !is_array($expirationType)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '有效期类型必传');
        }
        $sid = intval($this->loginInfo['sid']);
        $queryAccount = trim($queryAccount);
        $queryAccount = array_map('trim', $queryAccount ? explode(PHP_EOL, $queryAccount) : []);
        if (count($queryAccount) > 500) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '账号数量不能超过500个');
        }
        $distributorOperationBz = new DistributorOperation();
        $res = $distributorOperationBz->createDistributorQueryTask($sid, $requestId, [
            'sid' => $sid,
            'expiration_type' => $expirationType,
            'query_account' => $queryAccount
        ]);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 查询过期/即将过期分销商列表
     */
    public function queryDistributorResult()
    {
        $requestId = I('rid', '', 'strval');
        if (!$requestId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        $queryDname = I('query_dname', '', 'strval');
        $queryAccount = I('query_account', '', 'strval');
        $pageNum = I('page_num', 1, 'intval');
        $pageSize = I('page_size', 10, 'intval');
        $sid = intval($this->loginInfo['sid']);

        $distributorOperationBz = new DistributorOperation();
        $res = $distributorOperationBz->queryDistributorResult($sid, $requestId, $queryDname, $queryAccount, $pageNum, $pageSize);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 移除分销商查询结果
     */
    public function removeDistributorQueryResult()
    {
        $requestId = I('rid', '', 'strval');
        $account = I('account', '', 'strval');
        if (!$requestId || !$account) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        $sid = intval($this->loginInfo['sid']);

        $distributorOperationBz = new DistributorOperation();
        $res = $distributorOperationBz->removeDistributorQueryResult($sid, $requestId, $account);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 创建批量分销商续费任务
     */
    public function createBatchDistributionRenewTask()
    {
        $requestId = I('rid', '', 'strval');
        $endDate = I('date', '', 'strval');
        if (!$requestId || !$endDate) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        if ($endDate < date('Y-m-d')) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '续费日期不能小于今天');
        }
        $sid = intval($this->loginInfo['sid']);
        $opId = intval($this->loginInfo['memberID']);

        $distributorOperationBz = new DistributorOperation();
        $res = $distributorOperationBz->createBatchDistributionRenewTask($sid, $requestId, $endDate, $opId);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 查询商户分销商分页数据
     * <AUTHOR>
     * @date   2025/03/20
     *
     */
    public function queryMerchantDistributorPaginate()
    {
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 10, 'intval');
        $keyWord = I('post.key_word', '', 'strval');
        $sid     = intval($this->loginInfo['sid']);
        $res     = (new MemberRelationBiz())->getAllValidReseller($sid, $keyWord, $page, $size);
        $list    = [];
        foreach ($res['data']['list'] ?? [] as $value) {
            $list[] = [
                'id'      => $value['id'],
                'account' => $value['account'],
                'dname'   => $value['dname'],
                'cname'   => $value['cname'],
            ];
        }
        $this->apiReturn($res['code'], ['list' => $list, 'total' => $res['data']['total'] ?? 0], $res['msg']);
    }
}