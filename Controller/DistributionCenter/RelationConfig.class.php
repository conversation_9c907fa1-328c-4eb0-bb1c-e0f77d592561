<?php

namespace Controller\DistributionCenter;

use Business\RiskManagement\ShumeiRiskCheck;
use Library\Controller;
use Library\Tools\Helpers;

class RelationConfig extends Controller
{
    public function registerToSupplyShip()
    {
        $params              = I('param.');

        $ip                  = get_client_ip();
        $params['requestIp'] = $ip;
        $params['deviceId']  = $_SERVER['HTTP_DEVICEID'];

        $result = Helpers::DistributionCenterCall('registerToSupplyShip', $params, 'relationConfig', false,
            'user-center');

        Helpers::DistributionCenterResponse($result);
    }
}