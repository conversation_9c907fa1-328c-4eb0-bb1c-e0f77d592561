<?php

namespace Controller\DistributionCenter;

use Library\Controller;
use Library\Tools\Helpers;

class DistributionProductQuery extends Controller
{
    public function queryDistributionLandTitlePage()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('queryDistributionLandTitlePage', $params, 'distribution_product_query');

        Helpers::DistributionCenterResponse($result);
    }

    public function queryDistributionTicketTitlePage()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('queryDistributionTicketTitlePage', $params, 'distribution_product_query');

        Helpers::DistributionCenterResponse($result);
    }
}