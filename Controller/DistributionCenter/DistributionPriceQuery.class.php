<?php

namespace Controller\DistributionCenter;

use Library\Controller;
use Library\Tools\Helpers;

class DistributionPriceQuery extends Controller
{
    public function queryOneChainAndBasePrice()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('queryOneChainAndBasePrice', $params, 'distribution_price_query');

        Helpers::DistributionCenterResponse($result);
    }

    public function queryGroupSelfProductPriceByGroupId()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('queryGroupSelfProductPriceByGroupId', $params, 'distribution_price_query');

        Helpers::DistributionCenterResponse($result);
    }

    public function queryGroupTransferProductPriceByGroupId()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('queryGroupTransferProductPriceByGroupId', $params, 'distribution_price_query');

        Helpers::DistributionCenterResponse($result);
    }


}