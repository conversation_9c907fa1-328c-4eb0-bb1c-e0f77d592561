<?php

namespace Controller\DistributionCenter;

use Library\Controller;
use Library\Tools\Helpers;

class GroupDistributorQuery extends Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->getLoginInfo('ajax');
    }

    public function queryGroupAllDistributorByGroupId()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('queryGroupAllDistributorByGroupId', $params, 'group_distributor_query', true);

        Helpers::DistributionCenterResponse($result);
    }

    public function queryOneGroupAndDistributor()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('queryOneGroupAndDistributor', $params, 'group_distributor_query', true);

        Helpers::DistributionCenterResponse($result);
    }
}