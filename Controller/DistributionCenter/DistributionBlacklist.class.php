<?php

namespace Controller\DistributionCenter;

use Library\Controller;
use Library\Tools\Helpers;

class DistributionBlacklist extends Controller
{
    public function queryBlacklistFidByPaging()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('queryBlacklistFidByPaging', $params, 'distribution_blacklist');

        Helpers::DistributionCenterResponse($result);
    }

    public function configureDowngradeEvoluteLimit()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('configureDowngradeEvoluteLimit', $params, 'distribution_blacklist');

        Helpers::DistributionCenterResponse($result);
    }
}