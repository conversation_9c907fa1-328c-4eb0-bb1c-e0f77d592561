<?php

namespace Controller\DistributionCenter;

use Library\Controller;
use Library\Tools\Helpers;

class DistributionChannelHandle extends Controller
{
    private $_loginInfo    = null;
    public function __construct()
    {
        $this->_sid       = $this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
    }

    public function queryChannelList()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('queryChannelList', $params, 'distribution_channel_handle');

        Helpers::DistributionCenterResponse($result);
    }

    public function queryChannelListMoreData()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('queryChannelListMoreData', $params, 'distribution_channel_handle');

        Helpers::DistributionCenterResponse($result);
    }

    public function landSortConfig()
    {
        $params = I('param.');

        $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('channelConf',
            $this->_loginInfo['saccount']);
        if ($vacationMode === false) {
            $result = [
                'code' => 403,
                'data' => [],
                'message' => '当前处于假日模式，该功能被限制使用'
            ];
            Helpers::DistributionCenterResponse($result);
        }

        $result = Helpers::DistributionCenterCall('landSortConfig', $params, 'distribution_channel_handle');

        Helpers::DistributionCenterResponse($result);
    }

    public function ticketSortConfig()
    {
        $params = I('param.');

        $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('channelConf',
            $this->_loginInfo['saccount']);
        if ($vacationMode === false) {
            $result = [
                'code' => 403,
                'data' => [],
                'message' => '当前处于假日模式，该功能被限制使用'
            ];
            Helpers::DistributionCenterResponse($result);
        }

        $result = Helpers::DistributionCenterCall('ticketSortConfig', $params, 'distribution_channel_handle');

        Helpers::DistributionCenterResponse($result);
    }

    public function queryChannelLandListByPaging()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('queryChannelLandListByPaging', $params, 'distribution_channel_handle');

        Helpers::DistributionCenterResponse($result);
    }

    public function queryChannelTicketInfoByPaging()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('queryChannelTicketInfoByPaging', $params, 'distribution_channel_handle');

        Helpers::DistributionCenterResponse($result);
    }

    public function batchConfigChannel()
    {
        $params = I('param.');

        if (count($params['ids']) > 1){
            $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('channelConf',
                $this->_loginInfo['saccount']);
            if ($vacationMode === false) {
                $result = [
                    'code' => 403,
                    'data' => [],
                    'message' => '当前处于假日模式，该功能被限制使用'
                ];
                Helpers::DistributionCenterResponse($result);
            }
        }

        $result = Helpers::DistributionCenterCall('batchConfigChannel', $params, 'distribution_channel_handle');

        Helpers::DistributionCenterResponse($result);
    }

    public function memberChannelDefaultModify()
    {
        $params = I('param.');

        $result = Helpers::DistributionCenterCall('memberChannelDefaultModify', $params, 'distribution_channel_handle');

        Helpers::DistributionCenterResponse($result);
    }
}