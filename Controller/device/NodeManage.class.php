<?php

namespace Controller\device;
use Library\Controller;

/**
 * Created by PhpStorm.
 * User: huangxiaolong
 * Date: 2022/9/6
 * Time: 18:17
 */
class NodeManage extends Controller{
	// http://my.12301.local/r/device_NodeManage/getNodeTree
	// http://my.12301.local/r/test_Test/getcode
	public function getNodeTree(){
		$loginInfo          = $this->getLoginInfo();
		//$sid = I('post.apply_did');
		// $sid        = $loginInfo['sid'];//上级用户ID
		$sid = I('post.apply_did', 0);
		$manage = new \Business\TerminalManage\SupplierDeviceManage();
		$result = $manage->getNodeTree($sid);
		$this->apiReturn($result['code'], $result['data'],$result['msg']);
	}
}
