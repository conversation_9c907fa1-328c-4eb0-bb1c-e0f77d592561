<?php
/***
 * 演出分销库存相关
 */

namespace Controller\Show;

use Library\Controller;
use Business\PftShow\DisStorage as StorageBiz;
use Business\PftShow\Storage;

class DisStorage extends Controller
{
    private $sid;
    private $memberId;
    private $storageBiz;
    private $isOpen;

    public function __construct()
    {
        $loginInfo        = $this->getLoginInfo();
        $this->sid        = $loginInfo['sid'];
        $this->memberId   = $loginInfo['memberID'];
        $this->storageBiz = new StorageBiz();
        $checkRes         = $this->storageBiz->checkDisStorageAuth($this->sid);
        $this->isOpen     = false;
        if ($checkRes['code'] == 200 && isset($checkRes['data']['is_open']) && $checkRes['data']['is_open']) {
            $this->isOpen = true;
        }
    }

    /**
     * 模板/场次-设置分销库存开关
     */
    public function setStatus()
    {
        $zoneId = I('post.zone_id', 0, 'intval');
        //$templateId = I('post.id', 0, 'intval');
        $status  = I('post.status', 0, 'intval');    //开关状态 0关闭 1开启
        $roundId = I('post.round_id', 0, 'intval');  //场次id 场次分销库存设置的必填

        if (!$zoneId) {
            $this->apiReturn(203, [], '分区id不能为空');
        }

        $result = (new Storage())->setStatus($this->sid, $zoneId, $status, $roundId);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 查询分销商列表
     */
    public function memberQuery()
    {
        $searchType = I('post.type', 0, 'intval');   //搜索类型 1账号名称 2账号 3联系人 4手机号
        $keyWord    = I('post.keyword', '', 'strval');
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');

        if (!$keyWord || !in_array($searchType, [1, 2, 3, 4])) {
            return $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = (new \Business\Member\Member())->memberQuery($this->sid, $keyWord, $searchType, $page, $size);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 查询商家的员工列表
     */
    public function getSonInfoList()
    {
        $searchType = I('post.type', 0, 'intval');   //搜索类型 1账号名称 2账号
        $keyWord    = I('post.keyword', '', 'strval');
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');

        if (!$keyWord || !in_array($searchType, [1, 2])) {
            return $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = (new \Business\Member\Member())->getSonInfoList($this->sid, $keyWord, $searchType, $page, $size);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    //===============模板部分==================

    /**
     * 创建模板
     */
    public function createTemplate()
    {
        $zoneId  = I('post.zone_id', 0, 'intval');
        $venusId = I('post.venus_id', 0, 'intval');
        if (!$venusId) {
            $this->apiReturn(203, [], '场馆id不能为空');
        }

        if (!$zoneId) {
            $this->apiReturn(203, [], '分区id不能为空');
        }

        if (!$this->isOpen) {
            $this->apiReturn(203, [], '您未开通新版演出分销库存功能，请联系对应业务人员');
        }

        $venueInfo  = (new \Model\Product\Show())->getVenuesInfo($venusId, 'id as venue_id,apply_did, status');
        //场馆被删除的情况
        if ($venueInfo['status'] == 1) {
            $this->apiReturn(203, [], '场馆不存在');
        }

        //校验一次场馆是否在锁定中
        $isLock = (new \Business\PftShow\Show())->venueCopyLockCheck($venusId);
        if ($isLock['code'] != 200) {
            $this->apiReturn($isLock['code'], [], $isLock['msg']);
        }

        $itemTag = $this->storageBiz::createItemTag($zoneId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        //查询当前分区下的模板数量
        $templateRes = $this->storageBiz->queryTemplateByPaging($itemTag, $this->sid, 1, 1, '', 0, 2);
        if ($templateRes['code'] != 200) {
            $this->apiReturn($templateRes['code'], $templateRes['data'], $templateRes['msg']);
        }

        //已有模板数量
        $total = intval($templateRes['data']['total']);

        if ($total == 10) {
            $this->apiReturn(203, [], '模板最多只能创建10个');
        }

        //模板下标
        $num = $total + 1;
        //先查询一次模板名称是否可用
        $checkRes = $this->storageBiz->queryTemplateByName($this->sid, $itemTag, $this->storageBiz::ADD_TEMPLATE_NAME . "({$num})");
        if ($checkRes['code'] == 200 && $checkRes['data']) {
            $num += 1;
        }

        //查询一次分区下可售库存数
        $storage = (new \Model\Product\Show())->getZoneSeatsNums($venusId, $zoneId);

        $templateArr[] = [
            'defaultTemplate' => false,                                                //bool 默认模板
            'isEnable'        => true,                                                 //bool 是否启用 0:废弃 1:启用
            'itemTag'         => $itemTag,                                             //string 业务标签
            'productType'     => $this->storageBiz::PRODUCT_TYPE,                      //int 产品
            'templateName'    => $this->storageBiz::ADD_TEMPLATE_NAME . "({$num})",   //string 模板名称
            'templateType'    => $this->storageBiz::MEMBER_TEMPLATE,                   //int 模板类型： 1:系统模板 2:用户模 板
            'storageNum'      => $storage,                                             //int 模板类型： 1:系统模板 2:用户模板
        ];

        $result = $this->storageBiz->createTemplate($this->sid, $this->memberId, $templateArr);

        //查询一次分区下不可售库存数
        $storage = (new \Model\Product\Show())->getZoneSeatsNums($venusId, $zoneId, 5);

        $storageConfArr[] = [
            'item_tag'    => $itemTag,
            'zone_id'     => $zoneId,
            'un_sale_num' => $storage,
            'ext_conf'    => ['template_id' => $result['data'][0]],
        ];

        //创建初始模板的库存配置
        $storageConfRes = (new Storage())->batchAddStorageConf($this->sid, $storageConfArr);
        if ($storageConfRes['code'] != 200) {
            pft_log('show_dis_storage', json_encode([
                'ac'     => 'batchAddStorageConf',
                'params' => ['memberId' => $this->sid, 'storageConfArr' => $storageConfArr],
                'res'    => $storageConfRes,
            ], JSON_UNESCAPED_UNICODE), 2);
        }

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $return = [
            'id'   => $result['data'][0],
            'name' => $this->storageBiz::ADD_TEMPLATE_NAME . "({$num})",
        ];

        $this->apiReturn(200, $return, '模板创建成功');
    }

    /**
     * 模板列表
     */
    public function queryTemplateByPaging()
    {
        $zoneId       = I('post.zone_id', 0, 'intval');
        $page         = I('post.page', 1, 'intval');
        $size         = I('post.size', 10, 'intval');
        $templateName = I('post.template_name', '', 'strval');
        $templateId   = I('post.template_id', 0, 'intval');

        if (!$this->isOpen) {
            $this->apiReturn(203, [], '您未开通新版演出分销库存功能，请联系对应业务人员');
        }

        if (!$zoneId) {
            $this->apiReturn(203, [], '分区id不能为空');
        }

        $itemTag = $this->storageBiz::createItemTag($zoneId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        $result = $this->storageBiz->queryTemplateByPaging($itemTag, $this->sid, $page, $size, $templateName,
            $templateId, 2);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '模板列表获取成功');
    }

    /**
     * 修改模板信息【设置默认模板/修改模板名称】
     */
    public function updateTemplate()
    {
        $zoneId       = I('post.zone_id', 0, 'intval');
        $templateId   = I('post.id', 0, 'intval');
        $templateName = I('post.template_name', '', 'strval');
        $isDefault    = I('post.is_default', -1, 'intval');
        $isEnable     = I('post.is_enable', -1, 'intval');

        if (!$zoneId || !$templateId) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $itemTag = $this->storageBiz::createItemTag($zoneId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        $result = $this->storageBiz->updateTemplate($templateId, $this->sid, $this->memberId, $templateName, $isDefault,
            $isEnable);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '修改成功');
    }

    /**
     * 批量删除模板
     */
    public function deleteTemplate()
    {
        $zoneId     = I('post.zone_id', 0, 'intval');
        $templateId = I('post.id', 0, 'intval');

        if (!$zoneId || !$templateId) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $itemTag = $this->storageBiz::createItemTag($zoneId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        $result = $this->storageBiz->deleteTemplate($itemTag, $this->sid, $this->memberId, [$templateId]);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '删除成功');
    }

    //===============分销模板部分==================

    /**
     * 通过模板id获取模板信息
     */
    public function queryOneTemplateById()
    {
        $templateId = I('post.id', 0, 'intval');

        if (!$templateId) {
            $this->apiReturn(203, [], '模板id不能为空');
        }

        $result = $this->storageBiz->queryOneTemplateById($templateId, $this->sid);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '信息获取成功');
    }

    /**
     * 直售库存设置查询
     */
    public function queryTemplateOneItemStorageSetting()
    {
        $templateId = I('post.id', 0, 'intval');
        $groupId    = I('post.group_id', 0, 'intval');
        $fid        = I('post.fid', 0, 'intval');

        if (!$templateId) {
            $this->apiReturn(203, [], '模板id不能为空');
        }

        if (!$fid && !$groupId) {
            $fid = $this->sid;
        }

        $result = $this->storageBiz->queryTemplateOneItemStorageSetting($templateId, $this->sid, $fid, $groupId);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '库存信息获取成功');
    }

    /**
     * 直售/个人库存设置
     */
    public function updateTemplateDistributorStorage()
    {
        $templateId  = I('post.id', 0, 'intval');
        $fid         = I('post.fid', 0, 'intval');
        $storageNum  = I('post.storage_num', 0, 'intval');
        $storageType = I('post.storage_type', 0, 'intval');

        //直售库存设置时 fid给重置下
        if (!$fid) {
            $fid = $this->sid;
        }

        if (!$templateId || !is_numeric($storageNum) || !in_array($storageType, [1, 2, 3])) {
            $this->apiReturn(203, [], '模板id不能为空');
        }

        if ($storageNum) {
            $storageNum = intval($storageNum);
            if (strlen($storageNum) > 6) {
                $this->apiReturn(203, [], '库存数量超过限制');
            }
        }

        $result = $this->storageBiz->updateTemplateDistributorStorage($templateId, $fid, $this->sid, $this->memberId,
            $storageNum, $storageType);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '库存信息配置成功');
    }

    /**
     * 分组列表
     */
    public function queryTemplateGroupByPaging()
    {
        $templateId = I('post.id', 0, 'intval');
        $groupName  = I('post.group_name', '', 'strval');
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');
        $fid        = I('post.fid', 0, 'intval');

        if (!$templateId) {
            $this->apiReturn(203, [], '模板id不能为空');
        }

        $result = $this->storageBiz->queryTemplateGroupByPaging($templateId, $this->sid, $fid, $groupName, $page,
            $size);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '库存配置分组列表获取成功');
    }

    /**
     * 分销商列表
     */
    public function queryTemplateGroupDistributorByPaging()
    {
        $zoneId     = I('post.zone_id', 0, 'intval');
        $templateId = I('post.id', 0, 'intval');
        $groupId    = I('post.group_id', 0, 'intval');
        $fid        = I('post.fid', 0, 'intval');
        $groupName  = I('post.group_name', '', 'strval');
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');

        if (!$zoneId || !$templateId) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $itemTag = $this->storageBiz::createItemTag($zoneId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        $result = $this->storageBiz->queryTemplateGroupDistributorByPaging($templateId, $groupId, $itemTag, $this->sid,
            $fid, $groupName, $page, $size);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '库存配置分销商列表获取成功');
    }

    /**
     * 创建模板分组
     */
    public function createTemplateGroup()
    {
        $zoneId     = I('post.zone_id', 0, 'intval');
        $templateId = I('post.id', 0, 'intval');
        $groupName  = I('post.group_name', '', 'strval');

        if (!$templateId || !$groupName) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $itemTag = $this->storageBiz::createItemTag($zoneId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        $result = $this->storageBiz->createTemplateGroup($templateId, $groupName, $itemTag, $this->sid,
            $this->memberId);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '分组创建成功');
    }

    /**
     * 模板-编辑分组/分组库存设置
     */
    public function updateTemplateGroup()
    {
        $templateId  = I('post.id', 0, 'intval');
        $groupId     = I('post.group_id', 0, 'intval');
        $groupName   = I('post.group_name', '', 'strval');
        $storageNum  = I('post.storage_num', null);
        $storageType = I('post.storage_type', -1, 'intval');

        if (!$templateId || !$groupId || !in_array($storageType,
                [-1, 1, 2, 3]) || (!$groupName && !in_array($storageType, [1, 2, 3]))) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        if ($storageNum) {
            $storageNum = intval($storageNum);
            if (strlen($storageNum) > 6) {
                $this->apiReturn(203, [], '库存数量超过限制');
            }
        }

        $result = $this->storageBiz->updateTemplateGroup($templateId, $groupId, $this->sid, $this->memberId, $groupName,
            $storageNum, $storageType);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '配置成功');
    }

    /**
     * 移动分组
     */
    public function moveMixTemplateGroupDistributor()
    {
        $templateId       = I('post.id', 0, 'intval');
        $fidArr           = I('post.fid_arr', []);
        $originGroupIdArr = I('post.ori_group_id_arr', []);
        $newDistriGroupId = I('post.group_id', 0, 'intval');
        $hasNoGroup       = I('post.has_no_group', 0, 'intval');

        if (!$templateId || (!$fidArr && !$originGroupIdArr)) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->storageBiz->moveMixTemplateGroupDistributor($templateId, $this->sid, $this->memberId, $fidArr,
            $originGroupIdArr, $newDistriGroupId, $hasNoGroup);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '分组移动成功');
    }

    /**
     * 删除分组
     */
    public function deleteTemplateGroup()
    {
        $templateId = I('post.id', 0, 'intval');
        $groupId    = I('post.group_id', 20, 'intval');
        if (!$templateId || !$groupId) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->storageBiz->deleteTemplateGroup($templateId, $groupId, $this->sid, $this->memberId);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '分组删除成功');
    }

    /**
     * 查询模板汇总信息
     */
    public function queryTemplateStorageSummary()
    {
        $templateId = I('post.id', 0, 'intval');
        $zoneId     = I('post.zone_id', 0, 'intval');
        if (!$templateId) {
            $this->apiReturn(203, [], '模板id不能为空');
        }

        $result = $this->storageBiz->queryTemplateStorageSummary($templateId, $this->sid, $zoneId);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '汇总信息获取成功');
    }

    //===============操作记录部分==================

    /**
     * 分页查询分销库存操作记录列表
     */
    public function queryOperateRecordByPaging()
    {
        $templateId     = I('post.id', 0, 'intval');
        $zoneId         = I('post.zone_id', 0, 'intval');
        $roundId        = I('post.round_id', 0, 'intval');
        $opId           = I('post.opid', 0, 'intval');
        $page           = I('post.page', 1, 'intval');
        $size           = I('post.size', 10, 'intval');
        $startDate      = I('post.start_date', '', 'strval');
        $endDate        = I('post.end_date', '', 'strval');
        $objectType     = I('post.object_type', '', 'strval');
        $operateType    = I('post.operate_type', -1, 'intval');

        $itemTag = '';
        if (!$templateId && $zoneId && $roundId) {
            $itemTag = $this->storageBiz::createItemTag($zoneId, $roundId);
        }

        if (!$templateId && !$itemTag) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->storageBiz->queryOperateRecordByPaging($this->sid, $page, $size, $startDate, $endDate,
            $opId, $templateId, $itemTag, $objectType, $operateType);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作记录列表获取成功');
    }

    /**
     * 分页查询分销库存操作记录详情列表
     */
    public function queryOperateRecordDetailByPaging()
    {
        $opRecordId    = I('post.record_id', '', 'strval');
        $page          = I('post.page', 1, 'intval');
        $size          = I('post.size', 10, 'intval');
        $operateStatus = I('post.operate_status', -1, 'intval');

        if (!$opRecordId) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->storageBiz->queryOperateRecordDetailByPaging($opRecordId, $this->sid, $operateStatus, $page,
            $size);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作记录详情列表获取成功');
    }

    //===============场次配置部分==================

    /**
     * 分页查询分销库存操作记录详情列表
     */
    public function queryItemTagOneItemStorageSetting()
    {
        $zoneId      = I('post.zone_id', 0, 'intval');
        $roundId     = I('post.round_id', 0, 'intval');
        $fid         = I('post.fid', 0, 'intval');
        $groupId     = I('post.group_id', 0, 'intval');
        $storageDate = I('post.storage_date', '', 'strval');

        $itemTag = $this->storageBiz::createItemTag($zoneId, $roundId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        if (!$fid && !$groupId) {
            $fid = $this->sid;
        }

        $result = $this->storageBiz->queryItemTagOneItemStorageSetting($storageDate, $itemTag, $this->sid, $fid, $groupId);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '配置获取成功');
    }

    /**
     * 修改分销库存分组分销商个人库存配置
     */
    public function updateStorageDistributorSetting()
    {
        $zoneId      = I('post.zone_id', 0, 'intval');
        $roundId     = I('post.round_id', 0, 'intval');
        $fid         = I('post.fid', 0, 'intval');
        $storageNum  = I('post.storage_num', 0, 'intval');
        $storageType = I('post.storage_type', 3, 'intval');
        $storageDate = I('post.storage_date', '', 'strval');

        $itemTag = $this->storageBiz::createItemTag($zoneId, $roundId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        if (!$fid) {
            $fid = $this->sid;
        }

        if ($storageNum) {
            $storageNum = intval($storageNum);
            if (strlen($storageNum) > 6) {
                $this->apiReturn(203, [], '库存数量超过限制');
            }
        }

        $result = $this->storageBiz->updateStorageDistributorSetting($storageDate, $itemTag, $this->sid, $fid,
            $this->memberId, $storageNum, $storageType);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '库存配置设置成功');
    }

    /**
     * 修改分销库存分组分销商个人库存配置
     */
    public function queryItemTagStorageSummary()
    {
        $zoneId      = I('post.zone_id', 0, 'intval');
        $roundId     = I('post.round_id', 0, 'intval');
        $storageDate = I('post.storage_date', '', 'strval');

        $itemTag = $this->storageBiz::createItemTag($zoneId, $roundId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        $result = $this->storageBiz->queryItemTagStorageSummary($itemTag, $this->sid, $storageDate, $zoneId, $roundId);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '汇总信息获取成功');
    }

    /**
     * 分页查询分销库存分组列表
     */
    public function queryStorageGroupByPaging()
    {
        $zoneId      = I('post.zone_id', 0, 'intval');
        $roundId     = I('post.round_id', 0, 'intval');
        $fid         = I('post.fid', 0, 'intval');
        $groupName   = I('post.group_name', '', 'strval');
        $page        = I('post.page', 1, 'intval');
        $size        = I('post.size', 10, 'intval');
        $storageDate = I('post.storage_date', '', 'strval');

        $itemTag = $this->storageBiz::createItemTag($zoneId, $roundId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        $result = $this->storageBiz->queryStorageGroupByPaging($storageDate, $itemTag, $this->sid, $fid, $groupName,
            $page, $size);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '分组列表获取成功');
    }

    /**
     * 分页查询分销库存分组分销商列表
     */
    public function queryStorageGroupDistributorByPaging()
    {
        $zoneId      = I('post.zone_id', 0, 'intval');
        $roundId     = I('post.round_id', 0, 'intval');
        $groupId     = I('post.group_id', 0, 'intval');
        $fid         = I('post.fid', 0, 'intval');
        $groupName   = I('post.group_name', '', 'strval');
        $page        = I('post.page', 1, 'intval');
        $size        = I('post.size', 10, 'intval');
        $storageDate = I('post.storage_date', '', 'strval');

        $itemTag = $this->storageBiz::createItemTag($zoneId, $roundId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        $result = $this->storageBiz->queryStorageGroupDistributorByPaging($storageDate, $itemTag, $groupId, $this->sid,
            $fid, $groupName, $page, $size);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '分销商列表获取成功');
    }

    /**
     * 创建分销库存分组
     */
    public function createStorageGroup()
    {
        $zoneId      = I('post.zone_id', 0, 'intval');
        $roundId     = I('post.round_id', 0, 'intval');
        $groupName   = I('post.group_name', '', 'strval');
        $storageDate = I('post.storage_date', '', 'strval');

        $itemTag = $this->storageBiz::createItemTag($zoneId, $roundId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        $result = $this->storageBiz->createStorageGroup($storageDate, $itemTag, $this->sid, $this->memberId,
            $groupName);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '分组创建成功');
    }

    /**
     * 修改分销库存分组
     */
    public function updateStorageGroup()
    {
        $zoneId      = I('post.zone_id', 0, 'intval');
        $roundId     = I('post.round_id', 0, 'intval');
        $groupId     = I('post.group_id', 0, 'intval');
        $groupName   = I('post.group_name', '', 'strval');
        $storageNum  = I('post.storage_num', null);
        $storageType = I('post.storage_type', -1, 'intval');
        $storageDate = I('post.storage_date', '', 'strval');

        $itemTag = $this->storageBiz::createItemTag($zoneId, $roundId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        if ($storageNum) {
            $storageNum = intval($storageNum);
            if (strlen($storageNum) > 6) {
                $this->apiReturn(203, [], '库存数量超过限制');
            }
        }

        $result = $this->storageBiz->updateStorageGroup($storageDate, $groupId, $itemTag, $this->sid, $this->memberId,
            $groupName, $storageNum, $storageType);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '分组更新成功');
    }

    /**
     * 删除分销库存分组
     */
    public function deleteStorageGroup()
    {
        $zoneId  = I('post.zone_id', 0, 'intval');
        $roundId = I('post.round_id', 0, 'intval');
        $groupId = I('post.group_id', 0, 'intval');

        $itemTag = $this->storageBiz::createItemTag($zoneId, $roundId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        $result = $this->storageBiz->deleteStorageGroup($groupId, $itemTag, $this->sid, $this->memberId);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '分组更新成功');
    }

    /**
     * 移动分组
     */
    public function moveMixStorageGroupDistributor()
    {
        $zoneId           = I('post.zone_id', 0, 'intval');
        $roundId          = I('post.round_id', 0, 'intval');
        $fidArr           = I('post.fid_arr', []);
        $originGroupIdArr = I('post.ori_group_id_arr', []);
        $newDistriGroupId = I('post.group_id', 0, 'intval');
        $hasNoGroup       = I('post.has_no_group', 0, 'intval');

        $itemTag = $this->storageBiz::createItemTag($zoneId, $roundId);
        if (!$itemTag) {
            $this->apiReturn(203, [], '业务标签异常');
        }

        if (!$fidArr && !$originGroupIdArr) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->storageBiz->moveMixStorageGroupDistributor($itemTag, $this->sid, $this->memberId, $fidArr,
            $originGroupIdArr, $newDistriGroupId, $hasNoGroup);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '分组移动成功');
    }
}