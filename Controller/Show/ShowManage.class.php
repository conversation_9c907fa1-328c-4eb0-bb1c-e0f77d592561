<?php
/***
 * 演出管理入口
 */

namespace Controller\Show;

use Library\Controller;
use Business\PftShow\DisStorage as StorageBiz;
use Business\PftShow\Storage;

class ShowManage extends Controller
{
    private $sid;
    private $memberId;

    public function __construct()
    {
        $loginInfo        = $this->getLoginInfo();
        $this->sid        = $loginInfo['sid'];
        $this->memberId   = $loginInfo['memberID'];
    }

    /**
     * 获取演出场馆列表
     */
    public function getVenuesList()
    {
        $venueName = I('post.venue_name', '', 'strval');
        $page      = I('post.page', 1, 'intval');
        $size      = I('post.size', 10, 'intval');

        $venuesList = (new \Business\PftShow\ShowManage())->getVenuesList($this->sid, $page, $size, $venueName);

        return $this->apiReturn($venuesList['code'], $venuesList['data'], $venuesList['msg']);
    }
}