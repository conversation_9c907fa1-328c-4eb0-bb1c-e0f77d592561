<?php
/**
 * 营销管理
 *
 * <AUTHOR>
 * @since  2018-11-19
 */

namespace Controller\Market;

use Library\Controller;
use Model\Member\MemberRelationship;
use Model\Order\Coupon;
use Model\Product\Ticket;

class Market extends Controller
{
    private $_memberId;
    private $_supplyId;

    public function __construct()
    {
        $loginInfo       = $this->getLoginInfo('ajax');
        $this->_memberId = $loginInfo['memberID'];
        $this->_supplyId = $this->_memberId;
        $dtype          = $loginInfo['dtype'];
        if($dtype == 6){
            $shipModel  = new MemberRelationship();
            $parentInfo = $shipModel->getStaffParentId($this->_memberId);
            $this->_supplyId = $parentInfo;
        }
    }

    /**
     * 获取营销活动列表
     * @Author: zhujb
     * @param int pageSize 每页条数
     * @param int page     页码
     * @param int fid      分销商id
     * @param string activity_name 活动名称
     * @param int activity_status 新版营销活动状态  0 旧版活动   1 全部 2 未开始 3 进行中 4 已结束 5 已删除
     * 2018/11/7
     */
    public function getMarketingList()
    {
        $pageSize       = I('pageSize', 15, 'intval');
        $page           = I('page', 1, 'intval');
        $fid            = $this->_memberId;
        // $fid            = $this->_supplyId;
        $activityName   = I('activity_name', '', 'strval');
        $activityStatus = I('activity_status', 1, 'intval');

        if (empty($fid) || $activityStatus < 0) {
            $this->apiReturn(400, [], '参数错误');
        }

        $marketModel = new \Model\Market\Market();
        $marketArr   = $marketModel->getMarketingsOrderByStatus($fid, $activityName, $activityStatus, $page, $pageSize);
        $marketTotal = $marketModel->getMarketingCount($fid, $activityName, $activityStatus);

        $sharePageArr = array_column($marketArr, 'share_page');
        $marketSpArr  = $marketModel->selectMarketingSp($sharePageArr);
        $couponBiz    = new \Business\Product\Coupon();
        $marketArr    = $couponBiz->selectCanUseCouponForMarket($marketArr);
        foreach ((array)$marketSpArr as $marketSp) {
            $marketSpList[$marketSp['id']] = $marketSp;
        }

        $res['list'] = [];
        foreach ((array)$marketArr as $market) {
            $market['share_page'] = $marketSpList[$market['share_page']];
            $res['list'][]        = $market;
        }

        $res['total']    = $marketTotal;
        $res['pageSize'] = $pageSize;

        $this->apiReturn(200, $res, '获取成功');
    }

    /**
     * 创建营销活动
     * @Author: zhujb
     * @param string activity_name 活动名称
     * @param string activity_bt   活动开始时间
     * @param string activity_et   活动结束时间
     * @param int only_member  适用对象 2 所有用户 3 新用户 4 老用户
     * @param string limit_product 0 全部产品 A 景区  123244,123452,243234 产品id
     * @param int give_mode 送券方式：0-手动领取，1-系统发放;
     * @param string image_path 活动banner
     * @param int is_banner_show 是否展示banner图
     * @param int is_only_uper_limit 是否只送满足最高条件的优惠券0否1是
     * @param string content  活动内容
     * @param int activity_type  活动类型  （旧的 activity_type：1秒杀2限量打折3分享立减  share_type：1送优惠券2送红包3买即送） 新的 activity_type：4 免费送优惠券 5 下单送优惠劵
     * @param int activity_status 新版营销活动状态  0 旧版活动  2 未开始 3 进行中 4 已结束 5 已删除
     * @param array coupon  绑定优惠券信息 [0=>['coupon_id'=>1,'coupon_num'=>2],1=>['coupon_id'=>2,'coupon_num'=>2]]
     * 2018/11/7
     */
    public function createMarketing()
    {
        // $market['fid']             = $this->_memberId;
        $market['fid']             = $this->_supplyId;
        $market['activity_name']   = I('activity_name', '', 'strval');
        $market['activity_bt']     = strtotime(I('activity_bt', 0, 'strval'));
        $market['activity_et']     = strtotime(I('activity_et', 0, 'strval'));
        $market['activity_type']   = I('activity_type', 4, 'intval');
        $market['only_member']     = I('only_member', 0, 'intval');
        $market['limit_product']   = I('limit_product', '', 'strval');
        $market['is_banner_show']  = I('is_banner_show', 0, 'intval');
        $market['activity_status'] = 2; // 未开始
        $market['give_mode']       = I('give_mode', 0, 'intval');
        $market['is_only_uper_limit'] = I('is_only_uper_limit', 0, 'intval');
        $sharePage['title']        = I('activity_name', '', 'strval');
        // $sharePage['fid']          = $this->_memberId;
        $sharePage['fid']          = $this->_supplyId;
        $sharePage['image_path']   = I('image_path', '', 'strval');
        $sharePage['content']      = I('content', '', 'strval');
        $sharePage['share_type']   = I('activity_type', 4, 'intval');
        $couponInfo                = I('coupon', []);

        $marketModel = new \Model\Market\Market();
        $marketModel->startTrans();
        try {

            $sharePageId = $marketModel->addMarketingSharePage($sharePage);
            if ($sharePageId === false) {
                throw new \Exception('保存分享页信息失败');
            }

            $market['share_page'] = $sharePageId;
            $marketingId          = $marketModel->addMarketing($market);
            if ($marketingId === false) {
                throw new \Exception('保存活动失败');
            }

            $marketingCouponArr = [];
            foreach ($couponInfo as $value) {


                if($market['activity_type'] == 4 ){
                    //免费送优惠券
                    if($market['give_mode'] == 1){
                        //系统发放
                        $userGetLimit = $value['coupon_num'];
                    }else{
                        //手动领取
                        $userGetLimit = 1;
                    }
                }else{
                    if($value['coupon_num']< $value['user_get_limit']){
                        throw new \Exception('限领数量不能小于每单可领数量');
                    }
                    //下单送
                    if($market['give_mode'] == 1){
                        $userGetLimit = $value['coupon_num'];
                    }else{
                        $userGetLimit = empty($value['user_get_limit']) ? 1 : $value['user_get_limit'];
                    }
                }
                $marketingCouponArr[] = array(
                    'coupon_id'   => $value['coupon_id'],
                    'coupon_num'  => $value['coupon_num'],
                    'price'       => isset($value['price']) ? $value['price'] : 0,
                    'user_get_limit' => $userGetLimit,
                    'status'      => 1,
                    'activity_id' => $marketingId,
                    'share_id'    => $sharePageId,
                    // 'op_id'       => $this->_memberId,
                    'op_id'       => $this->_supplyId,
                    'create_time' => time(),
                );
            }
            $bindRes = $marketModel->addAllMarketingCoupon($marketingCouponArr);
            if ($bindRes === false) {
                throw new \Exception('保存活动优惠券绑定信息失败');
            }

            $marketModel->commit();
            $this->apiReturn(200, [], '创建成功');
        } catch (\Exception $e) {
            $marketModel->rollback();
            $this->apiReturn(400, [], $e->getMessage());
        }
    }

    /**
     * 获取营销活动详情信息
     * @Author: zhujb
     * 2018/11/13
     */
    public function getMartingInfo()
    {
        $activityId  = I('activity_id', 0, 'intval');
        $sharePageId = I('share_page_id', 0, 'intval');

        if (empty($activityId) || empty($sharePageId)) {
            $this->apiReturn(400, [], '参数错误');
        }

        $marketModel  = new \Model\Market\Market();
        $couponModel  = new Coupon('remote_1');
        $productModel = new Ticket();

        $data['marking']    = $marketModel->findMarketing($activityId, '*');
        $data['share_page'] = $marketModel->findMarketingSp($sharePageId);

        $bindCouponList = $marketModel->selectMarketingCoupon($activityId);
        $couponIdArr    = array_column($bindCouponList, 'coupon_id');

        $couponList = [];
        $couponArr  = $couponModel->getCoupons('id', $couponIdArr, '*');
        foreach ((array)$couponArr as $coupon) {
            $couponList[$coupon['id']] = $coupon;
        }

        $data['marking_coupon'] = [];
        foreach ((array)$bindCouponList as $key => $value) {
            $value['coupon_name']     = $couponList[$value['coupon_id']]['coupon_name'];
            $value['coupon_value']     = $couponList[$value['coupon_id']]['coupon_value'];
            $value['condition']     = $couponList[$value['coupon_id']]['condition'];
            $data['marking_coupon'][] = $value;
        }

        // 获取产品信息
        $data['productList'] = [];
        if (!empty($data['marking']['limit_product'])) {
            $productIdArr        = explode(',', $data['marking']['limit_product']);
            $productList = $productModel->getProductList($productIdArr, 'p_type,p_name,id');
            $data['productList'] = array_values($productList);
        }

        $this->apiReturn(200, $data, '获取成功');
    }

    /**
     * 编辑营销活动
     * @Author: zhujb
     * @param int  activity_id  活动id
     * @param int  share_page_id  分享页id
     * @param string activity_name 活动名称
     * @param string activity_bt   活动开始时间
     * @param string activity_et   活动结束时间
     * @param int only_member  适用对象 2 所有用户 3 新用户 4 老用户
     * @param string limit_product 0 全部产品 A 景区  123244,123452,243234 产品id
     * @param int give_mode 送券方式：0-手动领取，1-系统发放;
     * @param int is_banner_show 是否展示banner图
     * @param string image_path 活动banner
     * @param string content  活动内容
     * @param int activity_type  活动类型  （旧的 activity_type：1秒杀2限量打折3分享立减  share_type：1送优惠券2送红包3买即送） 新的 activity_type：4 免费送优惠券 5 下单送优惠劵
     * @param int activity_status 新版营销活动状态  0 旧版活动  2 未开始 3 进行中 4 已结束 5 已删除
     * @param array coupon  绑定优惠券信息 [0=>['coupon_id'=>1,'coupon_num'=>2],1=>['coupon_id'=>2,'coupon_num'=>2]]
     * 2018/11/7
     */
    public function editMarketing()
    {
        $activityId                = I('activity_id', 0, 'intval');
        $sharePageId               = I('share_page_id', 0, 'intval');
        $market['activity_name']   = I('activity_name', '', 'strval');
        $market['activity_bt']     = strtotime(I('activity_bt', 0, 'strval'));
        $market['activity_et']     = strtotime(I('activity_et', 0, 'strval'));
        $market['activity_type']   = I('activity_type', 4, 'intval');
        $market['only_member']     = I('only_member', 0, 'intval');
        $market['limit_product']   = I('limit_product', '');
        $market['is_banner_show']  = I('is_banner_show', 0, 'intval');
        $market['give_mode']       = I('give_mode', 0, 'intval');
        $market['is_only_uper_limit'] = I('is_only_uper_limit', 0, 'intval');
        $sharePage['title']        = I('activity_name', '', 'strval');
        $sharePage['image_path']   = I('image_path', '', 'strval');
        $sharePage['content']      = I('content', '', 'strval');
        $sharePage['share_type']   = I('activity_type', 4, 'intval');
        $sharePage['fid']          = $this->_supplyId;
        $couponInfo                = I('coupon', []);
        $market['fid']             = $this->_supplyId;

        $marketModel               = new \Model\Market\Market('remote_1');
        $marketInfoArr = $marketModel->findMarketing($activityId, 'id, activity_status');
        if (empty($marketInfoArr)) {
            $this->apiReturn(203, [], '无该活动');
        }
        if ($marketInfoArr['activity_status'] == 4) {
            $this->apiReturn(203, [], '该活动已经过期');
        }

        $marketModel->startTrans();
        try {
            $res1 = $marketModel->updateMarketing($activityId, $market);
            if ($res1 === false) {
                throw new \Exception('保存活动失败');
            }

            $res2 = $marketModel->updateMarketingSharePage($sharePageId, $sharePage);
            if ($res2 === false) {
                throw new \Exception('保存分享页信息失败');
            }

            if (!empty($couponInfo)) {
                $marketingCouponArr = [];
                foreach ($couponInfo as $value) {
                    if($market['activity_type'] == 4 ){
                        //免费送优惠券
                        if($market['give_mode'] == 1){
                            //系统发放
                            $userGetLimit = $value['coupon_num'];
                        }else{
                            //手动领取
                            $userGetLimit = 1;
                        }
                    }else{
                        //下单送
                        if($value['coupon_num']< $value['user_get_limit']){
                            throw new \Exception('限领数量不能小于每单可领数量');
                        }
                        if($market['give_mode'] == 1){
                            $userGetLimit = $value['coupon_num'];
                        }else{
                            $userGetLimit = empty($value['user_get_limit']) ? 1 : $value['user_get_limit'];
                        }
                    }
                    $marketingCouponArr[] = array(
                        'coupon_id'   => $value['coupon_id'],
                        'coupon_num'  => $value['coupon_num'],
                        'price'       => isset($value['price']) ? $value['price'] : 0,
                        'user_get_limit' => $userGetLimit,
                        'status'      => 1,
                        'activity_id' => $activityId,
                        'share_id'    => $sharePageId,
                        // 'op_id'       => $this->_memberId,
                        'op_id'       => $this->_supplyId,
                        'create_time' => time(),
                    );
                }

                $res3 = $marketModel->updateAllMarketingCoupon($activityId, $marketingCouponArr);
                if ($res3 === false) {
                    throw new \Exception('保存活动优惠券绑定信息失败');
                }
            }

            $marketModel->commit();
            $this->apiReturn(200, [], '编辑成功');
        } catch (\Exception $e) {
            $marketModel->rollback();
            $this->apiReturn(400, [], $e->getMessage());
        }
    }

    /**
     * 删除营销活动
     * @Author: zhujb
     * @param int activity_id 活动id
     * 2018/11/7
     */
    public function delMarketing()
    {
        $activityId  = I('activity_id', 0, 'intval');

        $marketModel = new \Model\Market\Market('remote_1');
        $marketModel->startTrans();
        try {
            $res1 = $marketModel->deleteMarketing($activityId);
            $res3 = $marketModel->deleteMarketingCoupon($activityId);
            if ($res1 === false|| $res3 === false) {
                throw new \Exception('删除失败');
            }

            $marketModel->commit();
            $this->apiReturn(200, [], '删除成功');
        } catch (\Exception $e) {
            $marketModel->rollback();
            $this->apiReturn(400, [], $e->getMessage());
        }

    }
}