<?php
/**
 * 活动数据
 *
 * <AUTHOR>
 * @since  2018-11-19
 */

namespace Controller\Market;

use Library\Controller;
use Model\Member\Member;
use Model\Order\Coupon;
use Model\Product\Ticket;
use Library\SimpleExcel;

class Activity extends Controller
{
    private $_memberId;
    private $_sid;

    public function __construct()
    {
        $loginInfo       = $this->getLoginInfo('ajax');
        $this->_memberId = $loginInfo['memberID'];
        $this->_sid      = $loginInfo['sid'];
    }

    /**
     * 获取活动数据列表
     * @Author: zhujb
     *
     * @param  int pageSize 每页条数
     * @param  int page     页码
     * @param  string fid   领劵人
     * @param  string activity_name 活动名称
     * @param  string coupon_name 优惠券名称
     * @param  string create_time 领用时间
     * @param  string use_time 使用时间
     * @param  int status -状态 -1:全部,0:正常,1:已过期,2:已使用,3:人工销毁,4:退票回收
     * 2018/11/7
     */
    public function getActivitiesList()
    {
        $pageSize     = I('pageSize', 15, 'intval');
        $page         = I('page', 1, 'intval');
        $aid          = $this->_sid;
        $activityName = I('activity_name', '', 'strval');
        $couponName   = I('coupon_name', '', 'strval');
        $createTime   = I('create_time', '', 'strval');
        $useTime      = I('use_time', '', 'strval');
        $status       = I('status', -1, 'intval');
        $fid          = I('fid', 0, 'intval');
        $isExport     = I('is_export', 0, 'intval');
        $userName     = I('userName', '', 'strval');    //用户名称或用户账号
        $userType     = I('userType', 0, 'intval');    //用户搜素类型：0用户名称1用户账号
        //$userName = "测试一下";
        //$userType = 0;
        //$userName = "********";
        //$userType = 1;

        $memberIds = [];
        if ($userName) {
            $memberApi = new \Business\JavaApi\Member\MemberQuery();

            if ($userType) {

                $memberList = $memberApi->queryMemberByMemberQueryInfo(['accountList' => [$userName]]);
                if ($memberList['code'] != 200 || empty($memberList['data'])) {

                    $res['list']     = [];
                    $res['total']    = 0;
                    $res['pageSize'] = $pageSize;
                    $this->apiReturn(200, $res, '获取成功');
                }
                $memberList = $memberList['data'];

                foreach ($memberList as $key => $value) {
                    $memberIds[] = $value['memberInfo']['id'];
                }

            } else {

                $memberList = $memberApi->queryMemberInfoPagingByName($userName);
                if ($memberList['code'] != 200 || empty($memberList['data'])) {

                    $res['list']     = [];
                    $res['total']    = 0;
                    $res['pageSize'] = $pageSize;
                    $this->apiReturn(200, $res, '获取成功');
                }

                $memberList = $memberList['data']['list'];
                if(empty($memberList)){

                    $res['list']     = [];
                    $res['total']    = 0;
                    $res['pageSize'] = $pageSize;
                    $this->apiReturn(200, $res, '获取成功');
                }

                foreach ($memberList as $key => $value) {
                    $memberIds[] = $value['id'];
                }
            }
            if (empty($memberIds)) {
                $res['list']     = [];
                $res['total']    = 0;
                $res['pageSize'] = $pageSize;
                $this->apiReturn(200, $res, '获取成功');
            }

            $fid = $memberIds;
        }

        $memberCouponModel = new Coupon('remote_1');
        $marketModel       = new \Model\Market\Market();
        $memberModel       = new Member();

        $activityIdArr = [];
        if (!empty($activityName)) {
            // 模糊搜索活动名字
            $marketArr     = $marketModel->selectMarketing($this->_sid, $activityName, 0, 1, 100, 'id');
            $activityIdArr = array_column($marketArr, 'id');
            if (empty($activityIdArr)) {
                $res['list']     = [];
                $res['total']    = 0;
                $res['pageSize'] = $pageSize;
                $this->apiReturn(200, $res, '获取成功');
            }
        }

        $couponIdArr = [];
        if (!empty($couponName)) {
            $couponArr   = $memberCouponModel->getCouponInfoByNames($couponName, 'id', $aid);
            $couponIdArr = array_column($couponArr, 'id');
            if (empty($couponIdArr)) {
                $res['list']     = [];
                $res['total']    = 0;
                $res['pageSize'] = $pageSize;
                $this->apiReturn(200, $res, '获取成功');
            }
        }

        $field           = 'fid,coupon_id,dstatus,create_time,use_time,start_date,end_date,ordernum,id,use_order,coupon_code';
        $memberCouponArr = $memberCouponModel->getMemberCoupons($aid, $fid, $couponIdArr, $activityIdArr, $createTime,
            $useTime, $status, $field, $page, $pageSize);
        $total           = $memberCouponModel->getMemberCouponCount($aid, $fid, $couponIdArr, $activityIdArr,
            $createTime, $useTime, $status);

        // 获取优惠券名称信息
        $couponList        = [];
        $memberCouponIdArr = array_column($memberCouponArr, 'coupon_id');
        $couponData        = $memberCouponModel->getCoupons('id', $memberCouponIdArr, 'id,coupon_name');
        foreach ((array)$couponData as $coupon) {
            $couponList[$coupon['id']] = $coupon;
        }

        // 获取领劵用户信息
        $memberIdArr = array_column($memberCouponArr, 'fid');
        $memberList  = $memberModel->getMemberInfoByMulti($memberIdArr, 'id', 'id,dname,account', true);

        $list = [];

        $hadUseNum = 0;
        foreach ((array)$memberCouponArr as $value) {
            $value['coupon_name']    = $couponList[$value['coupon_id']]['coupon_name'];
            $value['member_account'] = $memberList[$value['fid']]['account'];
            $value['member_dname']   = $memberList[$value['fid']]['dname'];
            $list[]                  = $value;

            if ($value['dstatus'] == 2) {
                $hadUseNum += 1;
            }
        }

        $res['list']      = $list;
        $res['total']     = $total;
        $res['pageSize']  = $pageSize;
        $res['num_count'] = array('issue_num' => '', 'member_coupon_num' => $total, 'had_use_num' => $hadUseNum);

        if (!empty($isExport)) {
            $this->_exportMemberCoupon($list);
            exit;
        } else {
            $this->apiReturn(200, $res, '获取成功');
        }

    }

    /**
     * 撤销销毁
     * @Author: zhujb
     * 2018/11/19
     */
    public function rollDestruction()
    {
        $memberCouponId = I('id', 0, 'intval');
        $aid            = $this->_memberId;
        $data           = array(
            'dstatus' => 3,
        );

        // 撤销用户优惠券
        $memberCouponModel = new Coupon('remote_1');
        $res               = $memberCouponModel->updateMemberCoupon($memberCouponId, $data, $aid, 0);

        if ($res !== false) {
            $this->apiReturn(200, [], '修改成功');
        }
        $this->apiReturn(400, [], '修改失败');
    }

    /**
     * 导出活动数据报表
     * @Author: zhujb
     * 2018/11/19
     *
     * @param $data
     */
    private function _exportMemberCoupon($data)
    {
        $exportData    = array();
        $exportData[0] = array('优惠券名称', '优惠券ID', '领劵用户', '用户账号', '领取时间', '有效期开始时间', '有效期结束时间', '使用时间', '使用订单号');
        foreach ($data as $value) {
            $exportData[] = array(
                $value['coupon_name'],
                $value['coupon_id'],
                $value['member_dname'],
                $value['member_account'],
                $value['create_time'],
                $value['start_date'],
                $value['end_date'],
                $value['use_time'],
                $value['ordernum'],
            );
        }

        $xls = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($exportData);
        $xls->generateXML(date('Ymd') . '活动数据报表');
        die;
    }

    /**
     * 获取领用用户列表
     * @Author: zhujb
     * 2018/11/19
     */
    public function getCollarUserList()
    {
        $this->apiReturn(203, [], "该接口已作废");
        $aid = $this->_memberId;
        $userName     = I('userName', '', 'strval');    //用户名称或用户账号
        $userType     = I('userType', 0, 'intval');    //用户搜素类型：0用户名称1用户账号
        //$userName = "测试一下";
        //$userType = 0;
        //$userName = "********";
        //$userType = 1;

        $memberIds = [];
        if ($userName) {
            $memberApi = new \Business\JavaApi\Member\MemberQuery();

            if ($userType) {

                $memberList = $memberApi->queryMemberByMemberQueryInfo(['accountList' => [$userName]]);
                if ($memberList['code'] != 200) {
                    $this->apiReturn(200, [], '获取成功');
                }
                $memberList = $memberList['data'];

                foreach ($memberList as $key => $value) {
                    if ($value['memberInfo']['dtype'] == 5) {
                        $memberIds[] = $value['memberInfo']['id'];
                    }
                }

            } else {

                $memberList = $memberApi->queryMemberInfoByName($userName, 0);
                if ($memberList['code'] != 200) {
                    $this->apiReturn(200, [], '获取成功');
                }
                $memberList = $memberList['data'];
                foreach ($memberList as $key => $value) {
                    if ($value['dtype'] == 5) {
                        $memberIds[] = $value['id'];
                    }
                }
            }
        }
        if(empty($memberIds)){
            $this->apiReturn(200, [], "没有相关用户");
        }

        $page = I('page', 1, 'intval');
        $pageSize = I('pageSize', 10, 'intval');

        $memberCouponModel = new Coupon('remote_1');
        $memberModel       = new Member();

        $list              = $memberCouponModel->getCouponsByMixedForHistoryData($aid, $memberIds, 'fid', 'fid', $page, $pageSize);

        if(empty($list)){
            $this->apiReturn(200, [], "没有相关用户");
        }
        $fidArr            = array_column($list, 'fid');

        $memberList = $memberModel->getMemberInfoByMulti($fidArr, 'id', 'id,dname,account', true);
        $res        = array_values($memberList);

        $this->apiReturn(200, $res, '获取成功');
    }
}
