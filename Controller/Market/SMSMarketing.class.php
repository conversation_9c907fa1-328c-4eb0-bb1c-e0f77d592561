<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: guanpeng
 * Date: 2019/6/27
 * Time: 9:52
 * 营销短信控制器
 */

namespace Controller\Market;


use Library\Business\DreamNet\sdk as DreamNetSdk;
use Library\Controller;

class SMSMarketing extends Controller
{
    public function __construct()
    {
        $loginInfo = parent::getLoginInfo('ajax');
        if (!$loginInfo || $loginInfo['sdtype']!=9) {
            parent::apiReturn(403, [], '非法访问');
        }
    }

    /**
     * 发送普通文本的营销短信
     * @author: guanpeng
     * @date: 2019/6/27
     */
    public function sendTextSms()
    {
        $proId  = I('post.province_id', 0, 'intval'); // 省份ID
        $total  = I('post.total_send', 0, 'intval'); // 发送条数
        $smsTxt = I('post.sms_content');// 短信文案，如果需要有url地址，那么需要加上 {url} 参数
        $smsSign= I('post.sms_sign');//短信签名
        $url    = I('post.sms_url');// 短信文案内需要用户点击的url地址
        $isTest = I('post.is_test', 0);// 是否测试短信

        $reqid  = md5(uniqid());
        if (strpos($smsTxt, '{url}')!==false) {
            $smsTxt = str_replace('{url}', $url, $smsTxt);
        }
        if ($isTest) {
            $testMobile = I('post.test_mobile');
            $mobiles = [];
            $mobiles[] = [
                'id'                 => -1,
                'ticket_taker_mobile'=>$testMobile,
            ];
        } else {
            $model  = new \Model\Market\RestSMSManage();
            $mobiles = $model->getSendData($proId, $total);
        }

        $redis     = \Library\Cache\Cache::getInstance('redis');
        $smdSdk    = new \Library\Business\FzZwx\sdk();
        $successId = $failId = $chinaTel = $delIdList = [];

        foreach ($mobiles as $item) {
            if (!\Library\Tools\Helpers::isMobile($item['ticket_taker_mobile'])) {
                $delIdList[] = $item['id'];
                continue;
            }
            if (isChinaTel($item['ticket_taker_mobile'])) {
                $chinaTel[] = $item['id'];
                continue;
            }
            $cacheKey = "sms_mobile:{$reqid}:{$item['ticket_taker_mobile']}";
            if (!$isTest && $redis->get($cacheKey)) {
                $delIdList[] = $item['id'];
                continue;
            }
            if (!$isTest) {
                $redis->set($cacheKey, 1, '', 3600);
            }
            $res = $smdSdk->sendSmsEncrypt($item['ticket_taker_mobile'], $smsTxt, $nativeCode=86, $type=0,'',$smsSign);
            if ($res['code'] == 200) {
                $successId[] = $item['id'];
            } else {
                $failId[] = $item['id'];
            }
        }
        if (isset($model)) {
            $model->updateSendTime($successId, time());
        }
        $response = [
            'success'=> count($successId),
            'fails'  => count($failId),
            'unsend' => count($chinaTel),
        ];
        parent::apiReturn(200, $response, 'success');
    }

    /**
     * 发送超级短信（富信）的营销短信
     * @author: guanpeng
     * @date: 2019/6/27
     * @throws \Exception
     */
    public function sendRestSms()
    {
        $tplId  = I('post.tplid');
        $isTest = I('post.is_test', 0, 'intval');
        $proId  = I('post.province_id', 0, 'intval');
        $total  = I('post.total_send', 0, 'intval');
        $reqid  = md5(uniqid());
        $smsObj = new DreamNetSdk();
        $model  = new \Model\Market\RestSMSManage();
        $hashObj= new \Library\Hashids\Hashids('sms');

        $mobiles = $model->getSendData($proId, $total);
        $redis   = \Library\Cache\Cache::getInstance('redis');
        $successId = $failId = $chinaTel = $delIdList = [];
        foreach ($mobiles as $item) {
            if (!\Library\Tools\Helpers::isMobile($item['ticket_taker_mobile'])) {
                $delIdList[] = $item['id'];
                continue;
            }
            if (isChinaTel($item['ticket_taker_mobile'])) {
                $chinaTel[] = $item['id'];
                continue;
            }
            $cacheKey = "sms_mobile:{$reqid}:{$item['ticket_taker_mobile']}";
            if (!$isTest && $redis->get($cacheKey)) {
                $delIdList[] = $item['id'];
                continue;
            }
            if (!$isTest) {
                $redis->set($cacheKey, 1, '', 3600);
            }
            $content = '{"p1":"'.$hashObj->encode($item['id']).'"}';
            $res = $smsObj->restSmsTplSend($tplId, $item['ticket_taker_mobile'], $content);
            if ($res['code'] == 200) {
                $successId[] = $item['id'];
            } else {
                $failId[] = $item['id'];
            }
        }
        $model->updateSendTime($successId, time());
        if (count($delIdList)) {
            $model->deleteNoUseItems($delIdList);
        }
        $response = [
            'success'=> count($successId),
            'fails'  => count($failId),
            'unsend' => count($chinaTel),
        ];
        parent::apiReturn(200, $response, 'success');
    }
}