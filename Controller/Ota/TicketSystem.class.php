<?php

namespace Controller\Ota;

use Library\Controller;
use Business\Ota\Upstream\SystemMember;
use Business\Ota\Upstream\SystemMemberSecret;
use Business\Ota\Upstream\SystemLandId;
use Business\Ota\Upstream\SystemTicket;

class TicketSystem extends Controller
{
    protected $_sid;
    protected $_opid;

    public function __construct()
    {
        $loginInfo   = $this->getLoginInfo();
        $this->_sid  = $loginInfo['sid'];
        $this->_opid = $loginInfo['memberID'];
    }

    /**
     * 用户开通的上游系统列表
     *
     * Author : liucm
     * Date : 2022/3/9
     */
    public function systemList()
    {
        $page     = I('post.page', '1', 'intval');
        $size     = I('post.size', '10', 'intval');
        $name     = I('post.name', '', 'strval,trim');
        $state    = I('post.state', '', 'intval');
        $memberId = $this->_sid;
        $sysType  = I('post.sys_type', '', 'strval,trim');

        $systemServie  = new SystemMember();
        $returnDataArr = $systemServie->getSystemList($memberId, $page, $size, $name, $state, $sysType);
        $this->apiReturn(200, $returnDataArr['data'], 'success');
    }

    /**
     * 用户开通的上游系统列表
     *
     * Author : liucm
     * Date : 2022/3/9
     */
    public function bindSystemList()
    {
        $page    = I('post.page', '1', 'intval');
        $size    = I('post.size', '10', 'intval');
        $name    = I('post.name', '', 'strval,trim');
        $sysType = I('post.sys_type', 'A', 'strval,trim');
        $lid     = I('post.lid', 0, 'intval');

        $memberId      = $this->_sid;
        $systemServie  = new SystemMember();
        $returnDataArr = $systemServie->getBindSystemList($memberId, $page, $size, $name, $sysType, $lid);
        $this->apiReturn(200, $returnDataArr['data'], 'success');
    }

    /**
     * 用户开通上游系统数量
     *
     * Author : liucm
     * Date : 2022/3/9
     */
    public function systemNum()
    {
        $memberId      = $this->_sid;
        $systemServie  = new SystemMember();
        $returnDataArr = $systemServie->getSystemNum($memberId);
        $this->apiReturn(200, $returnDataArr['data'], 'success');
    }

    /**
     * 获取绑定系统列表&未绑定系统列表
     *
     * Author : liucm
     * Date : 2022/3/9
     */
    public function getMemberSystem()
    {
        $memberId      = $this->_sid;
        $systemServie  = new SystemMember();
        $returnDataArr = $systemServie->getSystemListByMemberId($memberId);
        if ($returnDataArr['code'] != 200) {
            $this->apiReturn($returnDataArr['code'], [], $returnDataArr['msg']);
        }
        $this->apiReturn(200, $returnDataArr['data'], $returnDataArr['msg']);
    }

    /**
     * 用户添加票务系统
     *
     * Author : liucm
     * Date : 2022/3/10
     * @throws \Exception
     */
    public function addSystem()
    {
        $memberId   = $this->_sid;
        $systemId   = I('post.system_id', '', 'intval');
        $account    = I('post.account', '', 'strval,trim');
        $secret     = I('post.secret', '', 'strval,trim');
        $ext        = I('post.ext', '', 'strval,trim');
        $id         = I('post.secret_id', '', 'intval');
        $configName = I('post.config_name', '', 'strval,trim');

        if (!empty($account) && empty($secret)) {
            $this->apiReturn(200, [], '秘钥信息不能为空');
        }

        if (!empty($secret) && empty($account)) {
            $this->apiReturn(200, [], '账号信息不能为空');
        }

        if (!empty($ext)) {
            foreach ($ext as $v) {
                if (empty($v)) {
                    $this->apiReturn(400, [], '扩展参数错误');
                }
            }
            $extStr = json_encode($ext);
            if (!empty($extStr)) {
                //获取分隔符
                $separator = $this->_getseparator();
                $secret    = $secret . $separator . $extStr;
            }
        }

        if (empty($systemId)) {
            $this->apiReturn(200, [], '参数缺失');
        }

        $opId          = $this->_opid;
        $systemServie  = new SystemMember();
        $returnDataArr = $systemServie->addSystem($memberId, $systemId, $account, $secret, $configName, $id, $opId);
        if ($returnDataArr['code'] != 200) {
            $this->apiReturn($returnDataArr['code'], [], $returnDataArr['msg']);
        }
        $this->apiReturn(200, [], $returnDataArr['msg']);
    }

    /**
     * 用户添加票务系统前检测
     *
     * Author : liucm
     * Date : 2022/3/10
     * @throws \Exception
     */
    public function addSystemCheck()
    {
        $memberId = $this->_sid;

        $systemServie  = new SystemMember();
        $returnDataArr = $systemServie->checkSysNum($memberId);
        if ($returnDataArr['code'] != 200) {
            $this->apiReturn($returnDataArr['code'], [], $returnDataArr['msg']);
        }
        $this->apiReturn(200, [], $returnDataArr['msg']);
    }

    /**
     * 用户添加系统配置
     *
     * Author : liucm
     * Date : 2022/3/10
     */
    public function addOrEditSystemConfig()
    {
        $memberId   = $this->_sid;
        $opId       = $this->_opid;
        $systemId   = I('post.system_id', '', 'intval');
        $account    = I('post.account', '', 'strval,trim');
        $secret     = I('post.secret', '', 'strval,trim');
        $configName = I('post.config_name', '', 'strval,trim');
        $ext        = I('post.ext', '', 'strval,trim');
        $id         = I('post.secret_id', '', 'intval');

        if (empty($account) || empty($systemId)) {
            $this->apiReturn(200, [], '参数缺失');
        }

        if (!empty($ext)) {
            foreach ($ext as $v) {
                if (empty($v)) {
                    $this->apiReturn(400, [], '扩展参数错误');
                }
            }
            $extStr = json_encode($ext, JSON_UNESCAPED_UNICODE);
            if (!empty($extStr)) {
                //获取分隔符
                $separator = $this->_getseparator();
                $secret    = $secret . $separator . $extStr;
            }
        }

        $systemServie  = new SystemMemberSecret();
        $returnDataArr = $systemServie->addOrEditSystemConfig($memberId, $systemId, $opId, $account, $secret,
            $configName,
            $id);

        if ($returnDataArr['code'] != 200) {
            $this->apiReturn($returnDataArr['code'], [], $returnDataArr['msg']);
        }
        $this->apiReturn(200, [], $returnDataArr['msg']);
    }

    /**
     * 已绑定产品列表
     *
     * Author : liucm
     * Date : 2022/3/10
     */
    public function bindProductList()
    {
        $memberId   = $this->_sid;
        $landId     = I('post.land_id', '', 'intval');
        $page       = I('post.page', '1', 'intval');
        $size       = I('post.size', '10', 'intval');
        $landName   = I('post.land_name', '', 'strval,trim');
        $systemName = I('post.system_name', '', 'strval,trim');
        $landStatus = I('post.land_status', '1', 'strval');

        $systemServie  = new SystemLandId();
        $returnDataArr = $systemServie->bindProductList($memberId, $landId, $page, $size, $landName, $systemName,
            $landStatus);
        if ($returnDataArr['code'] != 200) {
            $this->apiReturn($returnDataArr['code'], [], $returnDataArr['msg']);
        }
        $this->apiReturn(200, $returnDataArr['data'], $returnDataArr['msg']);
    }

    /**
     * 绑定产品详情
     *
     * Author : liucm
     * Date : 2022/3/15
     */
    public function bindProductDetail()
    {
        $memberId = $this->_sid;
        $landId   = I('post.land_id', '', 'intval');
        if (empty($landId)) {
            $this->apiReturn(200, [], '参数缺失');
        }
        $systemServie  = new SystemLandId();
        $returnDataArr = $systemServie->bindProductDetail($memberId, $landId);
        if ($returnDataArr['code'] != 200) {
            $this->apiReturn($returnDataArr['code'], [], $returnDataArr['msg']);
        }
        $this->apiReturn(200, $returnDataArr['data'], $returnDataArr['msg']);
    }

    /**
     * 绑定产品
     *
     * Author : liucm
     * Date : 2022/3/16
     */
    public function addOrEditBindProduct()
    {
        $memberId   = $this->_sid;
        $landId     = I('post.land_id', '', 'intval');
        $systemId   = I('post.system_id', '', 'intval');
        $secretId   = I('post.secret_id', '', 'intval');//秘钥id
        $noticeCode = I('post.notice_code', '0', 'intval');//返码方式
        $opId       = $this->_opid;

        if (empty($landId) || empty($systemId) || empty($secretId)) {
            $this->apiReturn(200, [], '参数缺失');
        }

        $systemServie  = new SystemLandId();
        $returnDataArr = $systemServie->addOrEditBindProduct($memberId, $landId, $systemId, $secretId, $noticeCode,
            $opId);
        if ($returnDataArr['code'] != 200) {
            $this->apiReturn($returnDataArr['code'], [], $returnDataArr['msg']);
        }
        $this->apiReturn(200, $returnDataArr['data'], $returnDataArr['msg']);
    }

    /**
     * 获取系统配置
     *
     * Author : liucm
     * Date : 2022/3/16
     */
    public function getSystemConfig()
    {
        $memberId      = $this->_sid;
        $systemServie  = new SystemMemberSecret();
        $returnDataArr = $systemServie->getSystemConfig($memberId);
        if ($returnDataArr['code'] != 200) {
            $this->apiReturn($returnDataArr['code'], [], $returnDataArr['msg']);
        }
        $this->apiReturn(200, $returnDataArr['data'], $returnDataArr['msg']);
    }

    /**
     * 系统排序
     *
     * Author : liucm
     * Date : 2022/3/16
     */
    public function systemSort()
    {
        $memberId = $this->_sid;
        $id       = I('post.id', '', 'intval');
        $type     = I('post.type', '', 'intval');

        $systemServie  = new SystemMember();
        $returnDataArr = $systemServie->systemSort($memberId, $id, $type);

        if ($returnDataArr['code'] != 200) {
            $this->apiReturn($returnDataArr['code'], [], $returnDataArr['msg']);
        }
        $this->apiReturn(200, $returnDataArr['data'], $returnDataArr['msg']);
    }

    /**
     * 绑定门票
     *
     * Author : liucm
     * Date : 2022/3/16
     */
    public function bindTicket()
    {
        $memberId = $this->_sid;
        $opId     = $this->_opid;
        $systemId = I('post.system_id', 0, 'intval');
        //门票id
        $tid    = I('post.ticket_id', 0, 'intval');
        $landId = I('post.land_id', 0, 'intval');
        //短信模板id
        $templateId = I('post.template_id', 0, 'intval');
        //第三方产品编码
        if ($systemId == 91) {
            $thirdProductCode = I('post.third_product_code');
        } else {
            $thirdProductCode = I('post.third_product_code', 0, 'strval,trim');
        }
        // 发送短信  1:不发短信 2:代发短信
        $sendSms = I('post.send_sms', 1, 'intval');

        // 下单可验证：1  需要延迟验证：2
        $delayCheck = I('post.delay_check', 1, 'intval');
        // 延迟验证秒数
        $delayCheckTime = I('post.delay_check_time', 0, 'intval');

        if (empty($systemId) || empty($tid) || empty($landId)) {
            $this->apiReturn(201, [], '参数错误');
        }
        //获取业务层
        $systemTicket = new SystemTicket();
        //绑定门票
        $res = $systemTicket->bindTicket($memberId, $systemId, $tid, $landId, $thirdProductCode, $opId, $sendSms,
            $templateId, $delayCheck, $delayCheckTime);

        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 解绑门票
     *
     * Author : liucm
     * Date : 2022/3/14
     */
    public function unbindTicket()
    {
        $memberId = $this->_sid;
        $opId     = $this->_opid;
        $systemId = I('post.system_id', 0, 'intval');
        $tid      = I('post.ticket_id', 0, 'intval');//门票id

        if (empty($tid)) {
            $this->apiReturn(200, [], '参数缺失');
        }

        $systemTicket = new SystemTicket();
        $res          = $systemTicket->unbindTicket($memberId, $systemId, $tid, $opId);

        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 解绑产品下所有门票
     *
     * Author : liucm
     * Date : 2022/3/14
     */
    public function unbindAllTicket()
    {
        $memberId = $this->_sid;
        $opId     = $this->_opid;
        $systemId = I('post.system_id', 0, 'intval');
        $landId   = I('post.land_id', 0, 'intval');

        if (empty($landId)) {
            $this->apiReturn(200, [], '参数缺失');
        }
        $systemTicket = new SystemTicket();
        $res          = $systemTicket->unbindAllTicket($memberId, $systemId, $landId, $opId);

        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 解绑系统下所有产品及所有门票
     *
     * Author : liucm
     * Date : 2022/3/14
     */
    public function unbindSystemTicket()
    {
        $memberId = $this->_sid;
        $opId     = $this->_opid;
        $systemId = I('post.system_id', 0, 'intval');
        $unbind   = I('post.unbind', 0, 'intval');
        $id       = I('post.id', 0, 'intval');

        if (empty($systemId)) {
            $this->apiReturn(400, [], '参数缺失');
        }

        if ($unbind == 1 && empty($id)) {
            $this->apiReturn(400, [], '参数缺失');
        }

        $systemTicket = new SystemTicket();
        $res          = $systemTicket->unbindSystemTicket($memberId, $systemId, $opId, $unbind, $id);

        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 获取系统固定信息
     *
     * Author : liucm
     * Date : 2022/3/18
     */
    public function getSystemInfo()
    {
        $systemId = I('post.system_id', '', 'intval');
        $landId   = I('post.land_id', '', 'intval');
        $memberId = $this->_sid;
        $opId     = $this->_opid;

        if (empty($systemId)) {
            $this->apiReturn(200, [], '参数缺失');
        }

        $systemServie = new SystemMember();
        $res          = $systemServie->getSystemInfo($memberId, $systemId, $landId);
        if ($res['code'] != 200) {
            //失败
            $this->apiReturn($res['code'], [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 绑定门票数量
     *
     * Author : liucm
     * Date : 2022/3/18
     */
    public function bindTicketNum()
    {
        $systemId = I('post.system_id', '', 'intval');
        $landId   = I('post.land_id', '', 'intval');
        $memberId = $this->_sid;

        if (empty($systemId) || empty($landId)) {
            $this->apiReturn(200, [], '参数缺失');
        }
        //获取业务层
        $systemTicket = new SystemTicket();
        //绑定门票
        $res = $systemTicket->bindTicketNum($memberId, $systemId, $landId);
        if ($res['code'] != 200) {
            //失败
            $this->apiReturn($res['code'], [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }

    }

    /**
     * 绑定产品列表
     *
     * Author : liucm
     * Date : 2022/3/18
     */
    public function productList()
    {
        $memberId = $this->_sid;

        //处理入参
        //景区关键字
        $keyword = I('post.keyword', '', 'strval');
        //页码
        $page = I('post.page', 1, 'intval');
        //条数
        $size = I('post.size', 10, 'intval');
        $type = I('post.type', 1, 'intval');

        $systemServie = new SystemLandId();
        $res          = $systemServie->productList($memberId, $page, $size, $keyword, $type);

        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }

    }

    /**
     * 绑定产品列表(自动拉取业务使用，增加绑定状态字段)
     *
     * Author : jinshansan
     * Date : 2023/8/10
     */
    public function bindProductListForAutoSync()
    {
        $memberId   = $this->_sid;
        $landId     = I('post.land_id', '', 'intval');
        $page       = I('post.page', '1', 'intval');
        $size       = I('post.size', '10', 'intval');
        $landName   = I('post.land_name', '', 'strval,trim');
        $systemName = I('post.system_name', '', 'strval,trim');
        $landStatus = I('post.land_status', '1', 'strval');
        $confId     = I('post.conf_id', 0, 'intval');
        $systemId   = I('post.system_id', 0, 'intval');

        $systemServie  = new SystemLandId();
        $returnDataArr = $systemServie->bindProductListForAutoSync($memberId, $landId, $page, $size, $landName,
            $systemName,
            $landStatus, $confId, $systemId);
        if ($returnDataArr['code'] != 200) {
            $this->apiReturn($returnDataArr['code'], [], $returnDataArr['msg']);
        }
        $this->apiReturn(200, $returnDataArr['data'], $returnDataArr['msg']);

    }

    /**
     * 绑定门票列表
     *
     * Author : liucm
     * Date : 2022/3/23
     */
    public function bindTicketList()
    {
        $memberId = $this->_sid;
        //处理入参
        //景区关键字
        $title = I('post.title', '', 'strval,trim');
        //页码
        $pageNum = I('post.page', 1, 'intval');
        //条数
        $pageSize = I('post.size', 10, 'intval');
        $landId   = I('post.land_id', 0, 'intval');
        $ticketId = I('post.ticket_id', 0, 'intval');
        //状态 -1 全部，0绑定，1绑定
        $status       = I('post.status', 0, 'intval');
        $ticketStatus = I('post.ticket_status', '1', 'strval');

        $systemServie = new SystemTicket();

        $res = $systemServie->bindTicketList($memberId, $pageNum, $pageSize, $landId, $ticketId, $title, $status,
            $ticketStatus);

        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 绑定门票列表
     *
     * Author : liucm
     * Date : 2022/3/23
     */
    public function bindTicketListForAutoSync()
    {
        $memberId = $this->_sid;
        //处理入参
        //景区关键字
        $title = I('post.title', '', 'strval,trim');
        //页码
        $pageNum = I('post.page', 1, 'intval');
        //条数
        $pageSize = I('post.size', 10, 'intval');
        $landId   = I('post.land_id', 0, 'intval');
        $ticketId = I('post.ticket_id', 0, 'intval');
        //状态 -1 全部，0绑定，1绑定
        $status          = I('post.status', 0, 'intval');
        $ticketStatus    = I('post.ticket_status', '1', 'strval');
        $confId          = I('post.conf_id', '');
        $bindRuleState   = I('post.bind_rule_state', false);
        $notFilterBinded = I('post.not_filter_binded', false);

        $systemServie = new SystemTicket();

        $res = $systemServie->bindTicketListForAutoSync($memberId, $pageNum, $pageSize, $landId, $ticketId, $title,
            $status, $ticketStatus, $confId, $bindRuleState, $notFilterBinded);

        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 删除系统配置
     *
     * Author : liucm
     * Date : 2022/3/23
     */
    public function delSystemConfig()
    {
        $secretId = I('post.secret_id', 0, 'intval');
        $memberId = $this->_sid;
        $opId     = $this->_opid;
        if (empty($secretId)) {
            $this->apiReturn(200, [], '参数缺失');
        }
        $systemServie = new SystemMemberSecret();
        $res          = $systemServie->delSystemConfig($memberId, $secretId, $opId);

        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 分页获取线下票务系统操作日志
     */
    public function getSystemLogListPaging()
    {
        $startTime   = I('post.start_time', '', 'strval');
        $endTime     = I('post.end_time', '', 'strval');
        $systemId    = I('post.system_id', 0, 'intval');
        $operateType = I('post.operate_type', 0, 'intval');
        $lid         = I('post.lid', 0, 'intval');
        $tid         = I('post.tid', 0, 'intval');
        $opid        = I('post.opid', 0, 'intval');
        $page        = I('post.page', 1, 'intval');
        $pageSize    = I('post.size', 10, 'intval');

        if (!$startTime || !$endTime || !strtotime($startTime) || !strtotime($endTime)) {
            $this->apiReturn(203, [], '时间参数异常');
        }

        $systemServie = new SystemMember();
        $res          = $systemServie->getSystemLogListPaging($this->_sid, $startTime, $endTime, $systemId, $operateType, $lid, $tid, $opid, $page, $pageSize);

        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 商家配置下单失败，系统超时告警规则
     *
     * Author : cyw
     * Date : 2024/12/23
     */
    public function addUserRiskAlertConf()
    {
        $queryData = I('post.');
        if (empty($queryData['system_id'])) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $queryData['apply_id'] = $this->_sid;

        $systemServie  = new SystemMember();
        $res           = $systemServie->addUserRiskAlertConf($queryData, $this->_opid);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
        $this->apiReturn(200, $res['data'], $res['msg']);
    }

    /**
     * 商家获取下单失败，系统超时告警规则
     *
     * Author : cyw
     * Date : 2024/12/23
     */
    public function getUserRiskAlertConf()
    {
        $queryData = I('post.');
        if (empty($queryData['system_id'])) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $queryData['apply_id'] = $this->_sid;

        $systemRiskBusiness = new SystemMember();
        $res                = $systemRiskBusiness->getUserRiskAlertConf($queryData);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
        $this->apiReturn(200, $res['data'], $res['msg']);
    }


    /**
     * 分隔符定义
     *
     * @return string
     * <AUTHOR>
     * @date 2021-06-19 10:42
     */
    private function _getseparator()
    {
        return "|-_-|";
    }

}