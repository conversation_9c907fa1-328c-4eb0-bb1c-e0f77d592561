<?php

namespace Controller\Ota;

use Business\Ota\KuaiShouLocalBusiness;
use Library\Controller;

class KuaiShouLocal extends Controller
{
    /**
     * 获取店铺下所有门店
     *
     * <AUTHOR>
     * @date 2023-02-21
     */
    public function getPoiList()
    {
        $page      = I('post.page/d', 1);
        $pageSize  = I('post.page_size/d', 10);
        $accountId = I('post.account_id', '', 'strval,trim');

        $kuaiShouLocalBusiness = new KuaiShouLocalBusiness();
        $jsonRpcRes            = $kuaiShouLocalBusiness->getPoiList($page, $pageSize, $accountId);
        return $this->apiReturn($jsonRpcRes['code'], $jsonRpcRes['data'], $jsonRpcRes['msg']);
    }
}