<?php

namespace Controller\Ota;

use Business\Ota\OtaOrderVerify as OtaOrderVerifyService;
use Library\Controller;

class OtaOrderVerify extends Controller
{
    private $_memberInfo = [];
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];

    }

    /**
     * 获取列表
     *
     * Author : liucm
     * Date : 2021/8/20
     */
    public function getConfList()
    {
        $sid   = $this->_sid;
        $page  = I('post.page', '1', 'intval');
        $limit = I('post.size', '10', 'intval');
        if ($limit > 20) {
            $limit = 10;
        }
        $otaOrderVerifyService = new OtaOrderVerifyService();
        $listRes               = $otaOrderVerifyService->getList($sid, $page, $limit);
        $list                  = [];
        $total                 = 0;
        if ($listRes['code'] == 200) {
            $list = $listRes['data'] ?? [];
        }
        $totalRes = $otaOrderVerifyService->getListCount($sid);
        if ($totalRes['code'] == 200) {
            $total = $totalRes['data']['total'];
        }
        $return = [
            'list'  => $list,
            'total' => $total,
        ];
        $this->apiReturn(200, $return, '获取成功');
    }

    /**
     * 设置停用/启用状态
     *
     * Author : liucm
     * Date : 2021/8/20
     */
    public function setConfStatus()
    {
        $id    = I('post.id', '', 'intval');
        $state = I('post.state', '1', 'intval');
        $sid   = $this->_sid;
        if (empty($id) || !in_array($state, [1, 2])) {
            $this->apiReturn(500, [], '参数不正确');
        }
        $otaOrderVerifyService = new OtaOrderVerifyService();
        $res                   = $otaOrderVerifyService->setState($id, $state, $sid);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '修改成功');
        }
        $this->apiReturn(200, [], $res['msg']);
    }

    /**
     * 获取配置详情
     *
     * Author : liucm
     * Date : 2021/8/20
     */
    public function getConfDetail()
    {
        $id  = I('post.id', '', 'intval');
        $sid = $this->_sid;
        if (empty($id)) {
            $this->apiReturn(500, [], '参数不完整');
        }
        $otaOrderVerifyService = new OtaOrderVerifyService();
        $res                   = $otaOrderVerifyService->getDetail($id, $sid);
        if ($res['code'] == 200) {
            $this->apiReturn(200, $res['data']);
        }
        $this->apiReturn(500, [], $res['msg']);
    }

    /**
     * 新增配置规则
     *
     * Author : liucm
     * Date : 2021/8/20
     */
    public function addConf()
    {
        $sid         = $this->_sid;
        $sysid       = I('post.client_id', '', 'intval');
        $landId      = I('post.land_id', '', 'intval');
        $noticeType  = I('post.notice_type', '1', 'intval');
        $noticePhone = I('post.notice_phone', '', 'strval');
        $noticeToken = I('post.notice_token', '', 'strval');
        $noticeSign  = I('post.notice_sign', '', 'strval');
        $ruleArr     = I('post.rule');
        $state       = I('post.state', 1);

        if (empty($sysid) || empty($landId) || empty($ruleArr)) {
            $this->apiReturn(500, [], '参数不完整');
        }

        if ($noticeType == 1) {
            if (empty($noticeToken) || empty($noticeSign)) {
                $this->apiReturn(500, [], 'token或者秘钥参数不完整1');
            }
        }

        if (!is_array($ruleArr)) {
            $this->apiReturn(500, [], '配置格式不正确1');
        }

        foreach ($ruleArr as $rule) {
            if (empty($rule['ticket_id'])) {
                $this->apiReturn(500, [], '门票配置格式不正确');
            }
        }

        $otaOrderVerifyService = new OtaOrderVerifyService();
        if ($noticeType == 2) {
            if (empty($noticePhone)) {
                $this->apiReturn(500, [], '手机号参数不完整');
            }
            $noticePhoneTmp = explode(',', $noticePhone);
            if (count($noticePhoneTmp) != count(array_unique($noticePhoneTmp))) {
                $this->apiReturn(500, [], '手机号重复');
            }
            $noticePhoneArr = [];
            foreach ($noticePhoneTmp as $noticePhoneStr) {
                $res = $otaOrderVerifyService->ismobile(strval($noticePhoneStr));
                if ($res == false) {
                    $this->apiReturn(500, [], '手机号错误');
                }
                $noticePhoneArr[] = strval($noticePhoneStr);
            }
            $noticePhone = implode(',', $noticePhoneArr);
        }
        $opid = $this->_memberId;
        $res  = $otaOrderVerifyService->addConf($opid, $sid, $sysid, $landId, $noticeType,
            $noticePhone, $noticeToken, $noticeSign, $state, $ruleArr);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], $res['msg']);
        }
        $this->apiReturn(500, [], $res['msg']);

    }

    /**
     * 修改配置规则
     *
     * Author : liucm
     * Date : 2021/8/20
     */
    public function editConf()
    {
        $sid         = $this->_sid;
        $confId      = I('post.conf_id', '', 'intval');
        $landId      = I('post.land_id', '', 'intval');
        $noticeType  = I('post.notice_type', '', 'intval');
        $noticePhone = I('post.notice_phone', '', 'strval');
        $noticeToken = I('post.notice_token', '', 'strval');
        $noticeSign  = I('post.notice_sign', '', 'strval');
        $ruleArr     = I('post.rule');
        $state       = I('post.state', 1);

        if (empty($confId) || empty($landId) || empty($ruleArr)) {
            $this->apiReturn(500, [], '参数不完整');
        }

        if ($noticeType == 1) {
            if (empty($noticeToken) || empty($noticeSign)) {
                $this->apiReturn(500, [], 'token或者秘钥参数不完整1');
            }
        }

        if (!is_array($ruleArr)) {
            $this->apiReturn(500, [], '配置格式不正确');
        }

        foreach ($ruleArr as $rule) {
            if (empty($rule['ticket_id'])) {
                $this->apiReturn(500, [], '门票配置格式不正确');
            }
        }
        $otaOrderVerifyService = new OtaOrderVerifyService();
        if ($noticeType == 2) {
            if (empty($noticePhone)) {
                $this->apiReturn(500, [], '手机号参数不完整');
            }
            $noticePhoneTmp = explode(',', $noticePhone);
            if (count($noticePhoneTmp) != count(array_unique($noticePhoneTmp))) {
                $this->apiReturn(500, '手机号重复', []);
            }
            $noticePhoneArr = [];
            foreach ($noticePhoneTmp as $noticePhoneStr) {
                $res = $otaOrderVerifyService->ismobile(strval($noticePhoneStr));
                if ($res == false) {
                    $this->apiReturn(500, [], '手机号错误');
                }
                $noticePhoneArr[] = strval($noticePhoneStr);
            }
            $noticePhone = implode(',', $noticePhoneArr);
        }

        $opid = $this->_memberId;
        $res  = $otaOrderVerifyService->editConf($opid, $confId, $sid, $landId, $noticeType,
            $noticePhone, $noticeToken, $noticeSign, $state, $ruleArr);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], $res['msg']);
        }
        $this->apiReturn(500, [], $res['msg']);
    }

    /**
     * 删除配置
     *
     * Author : liucm
     * Date : 2021/8/20
     */
    public function delectConf()
    {
        $sid    = $this->_sid;
        $confId = I('post.conf_id', '', 'intval');
        if (empty($confId)) {
            $this->apiReturn(500, [], '参数不完整');
        }
        $otaOrderVerifyService = new OtaOrderVerifyService();
        $res                   = $otaOrderVerifyService->deleteConfAndRule($confId, $sid);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '删除成功');
        }
        $this->apiReturn(500, [], $res['msg']);
    }

    /**
     * 删除规则
     *
     * Author : liucm
     * Date : 2021/8/20
     */
    public function deleteRule()
    {
        $sid    = $this->_sid;
        $ruleId = I('post.rule_id', '', 'intval');
        if (empty($ruleId)) {
            $this->apiReturn(500, [], '参数不完整');
        }
        $otaOrderVerifyService = new OtaOrderVerifyService();
        $res                   = $otaOrderVerifyService->deleteRule($ruleId, $sid);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], $res['msg']);
        }
        $this->apiReturn(500, [], $res['msg']);
    }

    /**
     * 获取系统列表
     *
     * Author : liucm
     * Date : 2021/8/20
     */
    public function getSystemList()
    {
        $otaOrderVerifyService = new OtaOrderVerifyService();
        $list                  = $otaOrderVerifyService->systemList();
        $this->apiReturn(200, $list);
    }

    /**
     * 获取景区列表---最好改改
     *
     * Author : liucm
     * Date : 2021/8/20
     */
    public function getLand()
    {
        $keyword = I('post.keyword', '', 'strval');
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 10, 'intval');
        if ($size > 20) {
            $size = 10;
        }
        if (empty($keyword)) {
            $this->apiReturn(200, [], 'success');
        }

        $sid = $this->_sid;

        // 获取可售景区信息
        $javeApi     = new \Business\JavaApi\Ticket\Listing();
        $landInfoArr = $javeApi->getLandListPage($sid, $keyword, $page, $size);

        if ($landInfoArr['code'] != 200 || empty($landInfoArr['data']['data'])) {
            $this->apiReturn(204, [], '暂无数据');
        }
        $lidArr = $landInfoArr['data']['data'];
        $this->apiReturn(200, $lidArr, 'success');
    }

    /**
     * 获取门票列表--最好改改
     *
     * Author : liucm
     * Date : 2021/8/20
     */
    public function getTicket()
    {
        $lid  = I('post.lid', 0, 'intval');
        $page = I('post.page', 1, 'intval');
        $size = I('post.size', 10, 'intval');
        if ($size > 500) {
            $size = 500;
        }
        if (empty($lid) && empty($combinedId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        // 获取指定景区的可售产品
        $productBiz = new \Business\Product\Product();
        $sid        = $this->_sid;

        $evoluteArr = $productBiz->getSaleProductByCondition(0, $sid, $lid, null, null, null, null, null, null,
            $page, $size);
        $total      = $evoluteArr['total'] ?? 0;
        $tidArr     = array_column($evoluteArr['list'], 'ticket_id');

        // 获取门票属性
        $ticketModel = new \Model\Product\Ticket('slave');
        $ticketArr   = $ticketModel->getTicketInfoByIdArr($tidArr, 'id,title,pid');
        $ticketData  = [];
        if ($ticketArr) {
            foreach ($ticketArr as $ticket) {
                $ticketData[$ticket['id']] = $ticket;
            }
        }
        $list = [];

        $memberBiz         = new \Business\Member\Member();
        $memberRelationBiz = new \Business\JavaApi\Member\MemberRelationQuery();
        $suppliers         = [];
        $javaData          = [
            'sonIds'     => [$sid],
            'sonIdTypes' => [0],
        ];
        $javaRes           = $memberRelationBiz->queryMemberRelation($javaData);
        $supplyIdArr       = [];
        if ($javaRes['code'] == 200) {
            $supplyIdArr = array_column($javaRes['data'], 'parent_id');
        }
        if ($supplyIdArr) {
            $suppliers = $memberBiz->getList($supplyIdArr);
        }
        $suppliers[$sid] = ['dname' => $this->_memberInfo['dname'], 'account_id' => $this->_memberInfo['account']];

        foreach ((array)$evoluteArr['list'] as $item) {
            $list[] = array(
                'apply_did'        => $item['sid'],
                'apply_name'       => $suppliers[$item['sid']]['dname'],
                'apply_account_id' => $suppliers[$item['sid']]['account_id'],
                'title'            => $ticketData[$item['ticket_id']]['title'],
                'id'               => $item['ticket_id'],
                'pid'              => $ticketData[$item['ticket_id']]['pid'],
            );
        }

        $res = [
            'list'  => $list,
            'total' => $total,
        ];
        $this->apiReturn(200, $res, 'success');
    }

}