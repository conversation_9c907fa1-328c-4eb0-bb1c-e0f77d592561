<?php
/**
 * Created by Notepad++.
 * User: cyb
 * Date: 16/7/22
 * Time: 14:29
 */

namespace Controller\Ota;

use Library\Controller;
use Library\JsonRpc\PftRpcClient;
use Throwable;


class CalendarTicketHook extends Controller
{

    public function __construct()
    {
//        $this->isLogin('ajax');
    }

    /**
     * 审核结果回调
     *
     * <AUTHOR>
     * @date   2023-4-4
     */
    public function audit()
    {
        $this->apiReturn(200, [], 'success');
        $douyinAccountId  = I('douyin_account_id', 0, 'intval');
        $douyinPoiId      = I('douyin_poi_id', 0, 'intval');
        $douyinPoiName    = I('douyin_poi_name', '', 'string');
        $page               = I('page', 1, 'intval');
        $limit              = I('limit', 10, 'intval');

        $method  = 'calendarTicket/DouYinMerchantPoi/auditResult';
        $rpcData = [
            'douyin_account_id'     => $douyinAccountId,
            'douyin_poi_id'         => $douyinPoiId,
            'douyin_poi_name'       => $douyinPoiName,
            'page'                  => $page,
            'limit'                 => $limit
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        $this->apiReturn(200, $rpcRes['data'], 'success');
    }

    /**
     * rpc 请求
     *
     * <AUTHOR>
     * @date 2022-08-31
     *
     * @param string $method
     * @param array $rpcData
     *
     * @return array
     */
    protected function callRpc(string $method, array $rpcData): array
    {
        $result = [
            'code' => 200,
            'msg'  => 'success',
            'data' => [],
        ];
        try {
            $lib            = new PftRpcClient('open_platform_api');
            $jsonRpcRes     = $lib->call($method, [
                $rpcData,
            ], 'admin_service');
            $result['data'] = $jsonRpcRes['data'];
//            $result['code'] = $jsonRpcRes['code'];
//            $result['msg']  = $jsonRpcRes['msg'];
        } catch (Throwable $e) {
            $result['code'] = $e->getCode();
            $result['msg']  = $e->getMessage();
        }

        return $result;
    }
}

