<?php

namespace Controller\Ota;

use Business\Ota\CtripTicketCodeConfModel;
use Library\Controller;

class CtripTicketCodeConf extends Controller
{
    private $_memberInfo = [];
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];

    }

    /**
     * 获取门票配置
     *
     * Author : liucm
     * Date : 2021/9/24
     */
    public function getTicketCodeConf()
    {
        $returnData = [
            'notice_type' => 1,
            'is_show'     => 1,
            'is_notice'   => 1,
        ];

        $sid   = $this->_sid;
        $tid   = I('post.tid', 0, 'intval');

        if (empty($tid)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model = new \Model\Ota\CtripTicketCodeConfModel();
        $res   = $model->getAppTicketCodeConf($sid, $tid);
        if (!empty($res)) {
            $returnData = [
                'notice_type' => $res['notice_type'],
                'is_show'     => $res['is_show'],
                'is_notice'   => $res['is_notice'],
            ];
        }

        $this->apiReturn(200, $returnData, 'success');
    }

    /**
     * 增加或修改门票配置
     *
     * Author : liucm
     * Date : 2021/9/24
     */
    public function addOrUpdateTbTicketCodeConf()
    {
        $tid        = I('post.tid', 0, 'intval');
        $noticeType = I('post.notice_type', -1, 'intval');
        $isShow     = I('post.is_show', -1, 'intval');
        $isNotice   = I('post.is_notice', -1, 'intval');
        $memberId   = $this->_sid;
        $opid       = $this->_memberId;
        if (empty($tid) || $noticeType == -1 || $isShow == -1) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model   = new \Model\Ota\CtripTicketCodeConfModel();
        $ConfRes = $model->getAppTicketCodeConf($memberId, $tid);
        if (!empty($ConfRes)) {
            $res = $model->updateAppTicketCodeConf($memberId, $tid, $noticeType, $isShow, $isNotice, $opid);
            if ($res !== false) {
                $this->apiReturn(200, [], '修改成功');
            }
            $this->apiReturn(204, [], '修改操作失败，请重试');
        }

        $res = $model->addAppTicketCodeConf($memberId, $tid, $noticeType, $isShow, $isNotice, $opid);
        if ($res !== false) {
            $this->apiReturn(200, [], '新增成功');
        }

        $this->apiReturn(204, [], '新增操作失败，请重试');
    }

}