<?php

namespace Controller\Ota;

use Library\Controller;
use Business\Member\Login;
use Model\DataCollection\DataCollection;
use Library\Cache\Cache;
use Model\Ota\GovernmentDataSys;

class Dologin extends Controller
{
    private $_accKey = 'wuyuan_account:';

    public function dologin()
    {
        $yzm      = I('post.yzm', false, 'strval');
        $passport = I('post.passport', '', 'htmlspecialchars');
        $password = I('post.password', '', 'strval');
        $roleType = I('role_type', false);

        $loginBiz = new Login();
        $res      = $loginBiz->loginByPasswd($passport, md5($password), 1, $roleType, $yzm);
        if ($res['code'] == 200) {
            $memberInfo = $this->getLoginInfo();
            if (!in_array($memberInfo['memberID'], [7920916, 113])) {
                $this->apiReturn(203, [], '非婺源账号不可登录');
            }

            $cache = Cache::getInstance('redis');
            $cache->set($this->_accKey . $memberInfo['memberID'], $res['data']['account'], '', 3600);
            $this->apiReturn('200', [], '登录成功');
        } else if ($res['code'] == 2002) {
            $this->apiReturn('205', [], $res['msg']);
        } else {
            $this->apiReturn('201', [], $res['msg']);
        }
    }

    /**
     * 上饶市文旅数据平台推送增量数据配置
     *
     * @param string start_date 开始日期
     * @param string end_date  结束日期
     * @param string start_time  开始时间点 0-24
     * @param string end_time  结束时间点  0-24
     *
     * @return string | json
     *
     */
    public function setShangRaoFalseData()
    {
        $memberInfo = $this->getLoginInfo();
        if (!in_array($memberInfo['memberID'], [7920916, 113])) {
            $this->apiReturn(203, [], '非婺源账号不可登录');
        }

        $startDate = I('post.start_date');
        $endDate   = I('post.end_date');
        $startTime = I('post.start_time', '', 'intval');
        $endTime   = I('post.end_time', '', 'intval');
        $isable    = I('post.isable');
        $randList  = I('post.rand_list');

        if ($isable == 2) {
            $redis     = Cache::getInstance('redis');
            $isableKey = "government:report:config:shangrao:isable";
            $redis->set($isableKey, $isable);
            $this->apiReturn(200, [], '设置成功');
        }

        if (!in_array($isable, [1, 2])) {
            $this->apiReturn(201, [], '有效值设置错误');
        }

        if (!chk_date($startDate) || !chk_date($endDate)) {
            $this->apiReturn(201, [], '日期格式错误');
        }

        if ($startDate > $endDate) {
            $this->apiReturn(201, [], '开始日期不得晚于结束日期');
        }

        if ($startTime > 24 || $endTime > 24 || $startTime < 0 || $endTime < 0 || $startTime > $endTime) {
            $this->apiReturn(201, [], '时间设置有误');
        }
        $randList    = htmlspecialchars_decode($randList);
        $randListArr = json_decode($randList, true);
        if (empty($randListArr)) {
            $this->apiReturn(201, [], '请填写所有景点的数值');
        }

        foreach ($randListArr as $randItem) {
            if (!isset($randItem['min']) || !isset($randItem['max']) || !isset($randItem['outmin']) || !isset($randItem['outmax'])) {
                $this->apiReturn(201, [], '请设置最大值与最小值');
            }

            if (!is_numeric($randItem['min']) || !is_numeric($randItem['max']) || !is_numeric($randItem['outmin']) || !is_numeric($randItem['outmax'])) {
                $this->apiReturn(201, [], '最大值或最小值设置有误');
            }

            if ($randItem['max'] < $randItem['min'] || $randItem['min'] < 0) {
                $this->apiReturn(201, [], '入园最小值不可为负数且需小于最大值');
            }

            if ($randItem['outmax'] < $randItem['outmin'] || $randItem['outmin'] < 0) {
                $this->apiReturn(201, [], '出园最小值不可为负数且需小于最大值');
            }
        }

        $confStr     = "{$startDate}|{$endDate}|{$startTime}|{$endTime}|{$randList}";
        $srConfigkey = "government:report:config:shangrao:startDate|endDate|startTime|endTime|max|min";
        $isableKey   = "government:report:config:shangrao:isable";
        $redis       = Cache::getInstance('redis');

        $redis->set($isableKey, $isable);
        $redis->set($srConfigkey, $confStr);
        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 上饶市文旅数据平台推送增量数据配置获取
     * @return string | json
     *
     */
    public function getShangRaoFalseData()
    {
        $memberInfo = $this->getLoginInfo();
        $cache      = Cache::getInstance('redis');
        $account    = $cache->get($this->_accKey . $memberInfo['memberID']);
        if (!$account) {
            $this->apiReturn(203, [], '暂未登录');
        }

        if (!in_array($memberInfo['memberID'], [7920916, 113])) {
            $this->apiReturn(203, [], '非婺源账号不可登录');
        }

        $dataCollectionModel = new DataCollection();
        $redis               = Cache::getInstance('redis');
        $srConfigkey         = "government:report:config:shangrao:startDate|endDate|startTime|endTime|max|min";
        $isableKey           = "government:report:config:shangrao:isable";
        $isable              = $redis->get($isableKey);
        $confStr             = $redis->get($srConfigkey);

        //list($startDate, $endDate, $startTime, $endTime, $numMax, $numMin, $outnumMax, $outnumMin) = explode('|', $confStr);
        //$applyId = $memberInfo['sid'];

        list($startDate, $endDate, $startTime, $endTime, $randList) = explode('|', $confStr);
        $applyId = $memberInfo['sid'];

        if (!empty($applyId)) {
            $Collerction = new DataCollection();
            $totalNum    = $Collerction->getParkStatisticsDeviceDatatoDay($applyId);
        }
        $totalNumIn  = $totalNum['0']['totalNum'] ?? 0;
        $totalNumOut = $totalNum['1']['totalNum'] ?? 0;
        if (empty($startDate) || empty($endDate) || !isset($startTime) || empty($endTime) || empty($randList)) {
            $this->apiReturn(200, [
                'totalTrueIn'  => $totalNumIn,
                'totalTrueOut' => $totalNumOut,
                'isable'       => 3,
            ], '数据未设置或设置有误,请重新配置');
        }

        $govWithAttrListMap = $this->_getLandConfList(16);
        $statisticArr       = [];
        foreach ($govWithAttrListMap as $applyItem) {
            $deviceInfo = json_decode($applyItem['device_info'], true);
            if (!empty($deviceInfo)) {
                $deviceKeyArr = array_column($deviceInfo, 'device_key');
                if (!empty($deviceKeyArr)) {
                    //根据闸机地址搜索今日入园总数
                    $totalDIn  = $dataCollectionModel->getParkStatisticsDatatoDayByDeviceAndRoll($deviceKeyArr, 0);
                    $totalDOut = $dataCollectionModel->getParkStatisticsDatatoDayByDeviceAndRoll($deviceKeyArr, 1);
                }

                $cameraKeyArr = array_column($deviceInfo, 'camera_key');
                if (!empty($cameraKeyArr)) {
                    //根据摄像头mac地址搜索今日入园总数
                    $totalCIn  = $dataCollectionModel->getParkStatisticsCameraDataTodayByDeviceAndRoll($deviceKeyArr,
                        0);
                    $totalCOut = $dataCollectionModel->getParkStatisticsCameraDataTodayByDeviceAndRoll($deviceKeyArr,
                        1);
                }

                $statisticArr[$applyItem['land_code']] =
                    [
                        'In'  => (int)($totalDIn[0]['totalNum'] ?? 0 + $totalCIn[0]['totalNum'] ?? 0),
                        'Out' => (int)($totalDOut[0]['totalNum'] ?? 0 + $totalCOut[0]['totalNum'] ?? 0),
                    ];
            }
        }

        $i    = intval((date('i') / 5)) * 5;
        $data = [
            'start_date' => $startDate,
            'end_date'   => $endDate,
            'start_time' => $startTime,
            'end_time'   => $endTime,
            'rand_list'  => $randList,
            'isable'     => $isable,
            'trueData'   => $statisticArr,
            'date_time'  => date("Y-m-d H:{$i}"),
        ];

        $this->apiReturn(200, $data, '获取成功');
    }

    private function _getLandConfList($type = 0, $field = '*', $logPath = 'government/error')
    {
        if (empty($type)) {
            return [];
        }
        $govModel = new GovernmentDataSys();
        $govList  = $govModel->getGdListByType($type, $field);
        if (empty($govList)) {
            $errorData = [
                'key' => '获取对应配置列表为空',
                'sql' => $govModel->getLastSql(),
            ];
            pft_log($logPath, json_encode($errorData));

            return [];
        }

        $gdsIdArr = [];
        foreach ($govList as $item) {
            $gdsIdArr[] = $item['id'];
        }

        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);

        if (empty($govAttrList)) {
            $errorData = [
                'key' => '获取对应的data_attr表数据为空',
                'sql' => $govModel->getLastSql(),
            ];
            pft_log($logPath, json_encode($errorData));

            return [];
        }

        //将配置列表和配置列表属性整合成同一个列表
        foreach ($govList as &$list) {
            foreach ($govAttrList as $attrList) {
                if ($list['id'] == $attrList['gds_id']) {
                    $list[$attrList['attr_key']] = $attrList['attr_val'];
                }
            }
        }

        return $govList;
    }
}