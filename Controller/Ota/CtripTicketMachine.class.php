<?php
/**
 * 携程售票机
 *
 * <AUTHOR>
 * @date   2018-05-29
 */
namespace Controller\Ota;

use Library\Controller;
use Model\Ota\CtripTicketMachine as CtripMachine;

class CtripTicketMachine extends Controller
{
    /**
     * 获取Model类
     * @return CtripMachine
     */
    private function getCtripModel()
    {
        if (!isset($this->_CtripModel)) {
            $this->_CtripModel = new CtripMachine();
        }

        return $this->_CtripModel;
    }

    /**
     * 获取携程自助机项目的景点ID列表
     *
     * @return array
     */
    public function getCtripLandIdList()
    {
        $code = 200;
        $data = [];
        $msg  = '列表获取成功';

        $data = $this->getCtripModel()->getCtripLandIdList();
        if (empty($data)) {
            $this->apiReturn(202, [], '暂无数据');
        }
        $this->apiReturn($code, $data, $msg);

    }
    /**
     * 新增景点配置
     *
     * @return array
     */
    public function saveCtripMachine()
    {
        $code = 200;
        $data = [];
        $msg  = '景点添加成功';
        $params = I('post.');
        if(empty($params['pft_lid'])){
            $this->apiReturn(203, [], '缺少必要参数');
        }
        if(empty($params['ctrip_lid'])){
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $data = $this->getCtripModel()->saveCtripMachine($params['pft_lid'],$params['ctrip_lid']);
        if (empty($data)) {
            $this->apiReturn(202, [], '景点添加失败');
        }
        $this->apiReturn($code, $data, $msg);

    }

    /**
     * 获取携程订单
     *
     * @return array
     */
    public function getCtripMachineOrderList()
    {
        $code = 200;
        $data = [];
        $msg  = '列表获取成功';

        $data = $this->getCtripModel()->getCtripMachineOrderList();
        if (empty($data['list'])) {
            $this->apiReturn(202, [], '暂无数据');
        }
        $this->apiReturn($code, $data, $msg);

    }

    /**
     * 新增携程订单
     *
     * @return array
     */
    public function saveCtripMachineOrder()
    {
        $code = 200;
        $data = [];
        $msg  = '订单添加成功';
        $params = I('post.');
        if(empty($params['ctriporderid'])){
            $this->apiReturn(203, [], '缺少必要参数');
        }
        if(empty($params['pft_ordernum'])){
            $this->apiReturn(203, [], '缺少必要参数');
        }
        if($params['orderstatus'] ==  ''){
            $this->apiReturn(203, [], '缺少必要参数');
        }
        if(empty($params['operationtype'])){
            $this->apiReturn(203, [], '缺少必要参数');
        }
        if(empty($params['orderitems'])){
            $this->apiReturn(203, [], '缺少必要参数');
        }
        if(empty($params['orderstatus'])){
            $params['orderstatus'] = 0;
        }

        $data = $this->getCtripModel()->saveCtripMachineOrder($params['ctriporderid'], $params['pft_ordernum'], $params['orderstatus'], $params['operationtype'], $params['orderitems']);
        if (empty($data)) {
            $this->apiReturn(202, [], '订单添加失败');
        }
        $this->apiReturn($code, $data, $msg);

    }

    /**
     * 取消携程订单
     *
     * @return array
     */
    public function getUnpayOrder()
    {
        $code = 200;
        $data = [];
        $msg  = '列表获取成功';

        $idList = $this->getCtripModel()->getUnpayOrder();
        echo '<pre>';print_r($idList);die();
        /*if (empty($data['list'])) {
            $this->apiReturn(202, [], '暂无数据');
        }
        $this->apiReturn($code, $data, $msg);*/

    }

    /**
     * 批量更新携程订单
     *
     * @return array
     */
    public function UnpayOrder()
    {
        $code = 200;
        $data = [];
        $msg  = '列表获取成功';
        $params = I('get.');
        $idList = $this->getCtripModel()->UnpayOrder($params['pft_ordernum'], $params['orderstatus'], $params['operationtype']);
        /*if (empty($data['list'])) {
            $this->apiReturn(202, [], '暂无数据');
        }
        $this->apiReturn($code, $data, $msg);*/

    }
}