<?php

namespace Controller\Ota;

use Business\Ota\Order\CTripRefundReviewBusiness;
use Library\Controller;

/**
 * 携程玩乐退款审核
 *
 * Class CTripRefundReview
 *
 * <AUTHOR>
 * @date 2021-12-16
 * @package Controller\Ota\Order
 */
class CTripRefundReview extends Controller
{
    protected $_sid;

    public function __construct()
    {
        $loginInfo  = $this->getLoginInfo();
        $this->_sid = $loginInfo['sid'];
    }

    /**
     * 获取列表
     *
     * <AUTHOR>
     * @date 2021-12-16
     *
     */
    public function getList()
    {
        $pftOrder    = trim(I('post.ordernum', '', 'string'));
        $remoteOrder = trim(I('post.remote_order', '', 'string'));
        $page        = I('post.page', 1, 'intval');
        $pageSize    = I('post.page_size', 10, 'intval');
        $status      = I('post.status', 1, 'intval');

        $business   = new CTripRefundReviewBusiness();
        $jsonRpcRes = $business->getList($page, $pageSize, $this->_sid, $pftOrder, $remoteOrder, $status);
        return $this->apiReturn($jsonRpcRes['code'], $jsonRpcRes['data'], $jsonRpcRes['msg']);
    }
}