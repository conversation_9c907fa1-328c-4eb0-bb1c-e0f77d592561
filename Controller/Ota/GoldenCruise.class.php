<?php

namespace Controller\Ota;

use Library\Controller;
use Library\JsonRpc\PftRpcClient;
use Throwable;

class GoldenCruise extends Controller
{
    private $loginInfo = [];

    public function __construct()
    {
        $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 航次详情
     *
     * <AUTHOR>
     * @date   2023-07-09
     */
    public function getVoyagePlan()
    {
        $id = I('id', 0, 'intval');

        $method  = 'System/SystemGoldenCruise/getVoyagePlan';
        $rpcData = [
            'id' => $id,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 航次列表
     *
     * <AUTHOR>
     * @date   2023-07-09
     */
    public function getPlanList()
    {
        $page       = I('page', 1, 'intval');
        $limit      = I('limit', 10, 'intval');
        $expired    = I('expired', 0,'intval');
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $sid        = $this->loginInfo['sid'];        //供应商id

        $method  = 'System/SystemGoldenCruise/getPlanList';
        $rpcData = [
            'page'        => $page,
            'limit'       => $limit,
            'expired'     => $expired,
            'operator_id' => $operatorId,
            'sid'         => $sid,
        ];
        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 票种列表
     *
     * <AUTHOR>
     * @date   2023-07-09
     */
    public function getTicketList()
    {
        $planId = I('plan_id', 0, 'intval');

        $method  = 'System/SystemGoldenCruise/getTicketList';
        $rpcData = [
            'plan_id' => $planId,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 航次添加
     *
     * <AUTHOR>
     * @date   2023-07-09
     */
    public function addVoyagePlan()
    {
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $sid        = $this->loginInfo['sid'];        //供应商id
        $postData   = I('post.');

        $method  = 'System/SystemGoldenCruise/addVoyagePlan';
        $rpcData = [
            'post_data'   => $postData,
            'operator_id' => $operatorId,
            'sid'         => $sid,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 航次修改
     *
     * <AUTHOR>
     * @date   2023-07-09
     */
    public function modifyVoyagePlan()
    {
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $postData   = I('post.');

        $method  = 'System/SystemGoldenCruise/modifyVoyagePlan';
        $rpcData = [
            'post_data'   => $postData,
            'operator_id' => $operatorId,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 航次删除
     *
     * <AUTHOR>
     * @date   2023-07-09
     */
    public function batchDeletePlan()
    {
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $postData   = I('post.');

        $method  = 'System/SystemGoldenCruise/batchDeletePlan';
        $rpcData = [
            'post_data'   => $postData,
            'operator_id' => $operatorId,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 航次时间添加
     * 2023-10-19
     */
    public function addVoyagePlanTimeConf()
    {
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $sid        = $this->loginInfo['sid'];        //供应商id
        $postData   = I('request.');

        $method  = 'System/SystemGoldenCruise/addVoyagePlanTimeConf';
        $rpcData = [
            'departure_time' => $postData['departure_time'],
            'arrive_time'    => $postData['arrive_time'],
            'operator_id'    => $operatorId,
            'sid'            => $sid,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 航次时间状态修改
     * 2023-10-19
     */
    public function editVoyagePlanTimeConfState()
    {
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $sid        = $this->loginInfo['sid'];        //供应商id
        $postData   = I('request.');

        $method  = 'System/SystemGoldenCruise/editVoyagePlanTimeConfState';
        $rpcData = [
            'ids'         => [$postData['id']],
            'state'       => $postData['state'],
            'operator_id' => $operatorId,
            'sid'         => $sid,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 航次时间列表
     * 2023-10-19
     */
    public function voyagePlanTimeConfList()
    {
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $sid        = $this->loginInfo['sid'];        //供应商id
        $postData   = I('request.');

        $method  = 'System/SystemGoldenCruise/voyagePlanTimeConfList';
        $rpcData = [
            'page'           => $postData['page_num'],
            'limit'          => $postData['page_size'],
            'departure_time' => $postData['departure_time'],
            'arrive_time'    => $postData['arrive_time'],
            'state'          => $postData['state'],
            'sid'            => $sid,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 房型票种添加
     * 2023-10-19
     */
    public function addRoomTypeTicketConf()
    {
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $sid        = $this->loginInfo['sid'];        //供应商id
        $postData   = I('request.');

        $method  = 'System/SystemGoldenCruise/addRoomTypeTicketConf';
        $rpcData = [
            'room_name'         => $postData['room_name'],
            'room_code'         => $postData['room_code'],
            'ticket_name'       => $postData['ticket_name'],
            'room_type_conf_id' => $postData['room_type_conf_id'] ?? false,
            'operator_id'       => $operatorId,
            'sid'               => $sid,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 航次时间列表
     * 2023-10-19
     */
    public function roomTypeConfList()
    {
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $sid        = $this->loginInfo['sid'];        //供应商id
        $postData   = I('request.');

        $method  = 'System/SystemGoldenCruise/roomTypeConfList';
        $rpcData = [
            'page'      => $postData['page_num'],
            'limit'     => $postData['page_size'],
            'room_name' => $postData['room_name'] ?? false,
            'id'        => $postData['id'] ?? false,
            'state'     => $postData['state'] ?? false,
            'sid'       => $sid,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }
    
    /**
     * 航次时间状态修改
     * 2023-10-19
     */
    public function editRoomTypeTicketConfState()
    {
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $sid        = $this->loginInfo['sid'];        //供应商id
        $postData   = I('request.');

        $method  = 'System/SystemGoldenCruise/editRoomTypeTicketConfState';
        $rpcData = [
            'ids'         => [$postData['id']],
            'state'       => $postData['state'],
            'operator_id' => $operatorId,
            'sid'         => $sid,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 航次时间列表
     * 2023-10-19
     */
    public function roomTypeTicketConfList()
    {
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $sid        = $this->loginInfo['sid'];        //供应商id
        $postData   = I('request.');

        $method  = 'System/SystemGoldenCruise/roomTypeTicketConfList';
        $rpcData = [
            'page'              => $postData['page_num'],
            'limit'             => $postData['page_size'],
            'room_name'         => $postData['room_name'] ?? false,
            'ticket_name'       => $postData['ticket_name'] ?? false,
            'state'             => $postData['state'] ?? false,
            'room_type_conf_id' => $postData['room_type_conf_id'] ?? false,
            'sid'               => $sid,
        ];

        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 获取门票分时配置信息
     * 
     */
    public function getTicketTimeShareList()
    {
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $sid        = $this->loginInfo['sid'];        //供应商id
        $postData   = I('request.'); 

        $ticketId   = $postData['tid'];
        if (empty($ticketId)) {
            $this->apiReturn(203, [], 'tid有误'); 
        }

        $timeShareJavaApi = new \Business\JavaApi\Order\TimeShareOrder();
        $timeShareRes = $timeShareJavaApi->getTimeSlicesWithDateRange((int)$ticketId, date("Y-m-d"), date("Y-m-d"), 1);
        if ($timeShareRes['code'] != 200) {
            $this->apiReturn(500, [], $timeShareRes['msg']);  
        }

        $returnList = [];
        if (!empty($timeShareRes['data'])) {
            foreach ($timeShareRes['data'] as $item) {
                foreach ($item['time_slices'] as $timeItem) {
                    $returnList['list'][] = [
                        'id'         => $timeItem['id'],
                        'begin_time' => $timeItem['begin'],
                        'end_time'   => $timeItem['end'],
                    ]; 
                }
            }
        }

        $this->apiReturn(200, $returnList, 'success');  
    }

    /**
     * rpc 请求
     *
     * <AUTHOR>
     * @date 2022-08-31
     *
     * @param  string  $method
     * @param  array  $rpcData
     *
     * @return array
     */
    protected function callRpc(string $method, array $rpcData): array
    {
        $result = [
            'code' => 200,
            'msg'  => 'success',
            'data' => [],
        ];
        try {
            $lib        = new PftRpcClient('ota_supplier');
            $jsonRpcRes = $lib->call($method, [
                $rpcData,
            ], 'ota_supplier');

            $result['data'] = $jsonRpcRes['data'];
            $result['code'] = $jsonRpcRes['code'];
            $result['msg']  = $jsonRpcRes['msg'];
        } catch (Throwable $e) {
            $result['code'] = $e->getCode();
            $result['msg']  = $e->getMessage();
        }

        return $result;
    }
}

?>