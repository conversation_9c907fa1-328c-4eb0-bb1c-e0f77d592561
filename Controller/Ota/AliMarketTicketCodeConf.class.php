<?php

namespace Controller\Ota;

use Library\Controller;

class AliMarketTicketCodeConf extends Controller
{
    private $_sid;
    private $_memberId;
    private $_memberInfo;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 获取门票配置
     */
    public function getTicketCodeConf()
    {
        $returnData = [
            'notice_type' => 1,
            'is_show'     => 1,
        ];

        $sid = $this->_sid;
        $tid = I('post.tid', 0, 'intval');

        if (empty($tid)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model = new \Model\Ota\ApiAliMarketTicketCodeConfModel();
        $res   = $model->getApiAliMarketTicketCodeConf($sid, $tid);
        if (!empty($res)) {
            $returnData = [
                'notice_type' => $res['notice_type'],
                'is_show'     => $res['is_show'],
            ];
        }

        $this->apiReturn(200, $returnData, 'success');
    }

    /**
     * 增加或修改门票配置
     * 
     */
    public function addOrUpdateTbTicketCodeConf()
    {
        $tid        = I('post.tid', 0, 'intval');
        $noticeType = I('post.notice_type', -1, 'intval');
        $isShow     = I('post.is_show', -1, 'intval');
        $memberId   = $this->_sid;
        $opid       = $this->_memberId;

        if (empty($tid) || $noticeType == -1 || $isShow == -1) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model   = new \Model\Ota\ApiAliMarketTicketCodeConfModel();
        $ConfRes = $model->getApiAliMarketTicketCodeConf($memberId, $tid);
        if (!empty($ConfRes)) {
            $res = $model->updateApiAliMarketTicketCodeConf($memberId, $tid, $noticeType, $isShow, $opid);
            if ($res !== false) {
                $this->apiReturn(200, [], '修改成功');
            }
            $this->apiReturn(204, [], '修改操作失败，请重试');
        }

        $res = $model->addApiAliMarketTicketCodeConf($memberId, $tid, $noticeType, $isShow, $opid);
        if ($res !== false) {
            $this->apiReturn(200, [], '新增成功');
        }

        $this->apiReturn(204, [], '新增操作失败，请重试');
    }
}