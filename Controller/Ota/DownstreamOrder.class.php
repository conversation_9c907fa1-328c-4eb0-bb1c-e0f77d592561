<?php

/**
 * 下游订单服务
 *
 * Class DownstreamOrder
 * @package Controller\Ota
 */

namespace Controller\Ota;

use Business\Order\TerminalOrderSearch;
use Library\Controller;
use Business\Ota\DownstreamOrder\TaobaoOrder as TaobaoOrderBusiness;
use Business\Ota\DownstreamOrder\OrderFail as OrderFailBusiness;
use Business\Ota\DownstreamOrder\Order as OrderBusiness;
use Business\Ota\DownstreamOrder\OrderNotice as OrderNoticeBusiness;
use Model\Order\RefundAuditModel;
use Process\Order\OrderFromExcel;

class DownstreamOrder extends Controller
{
    private $_memberInfo = [];
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 获取淘宝订单列表
     * http://admin2014.12301dev.com/r/Order_OrderList/getSearchConfigOther
     * Author : liucm
     * Date : 2021/12/15
     */
    public function taobaoOrderList()
    {
        $pagesize      = I('post.page_size', 10, 'intval');
        $pagenum       = I('post.page_num', 1, 'intval');
        $timeBegin     = I('request.begin_time', '', 'strval'); // 起始时间
        $timeEnd       = I('request.end_time', '', 'strval'); // 结束时间
        $taobaoOrderNo = trim(I('request.taobao_order_no', '', 'strval')); // 淘宝订单
        $pftOrderNo    = trim(I('request.pft_order_no', '', 'strval')); // 票付通订单
        $status        = I('request.status', '', 'strval'); // 0=全部 1=下单成功 2-下单失败 3=发码失败 4=发码失败+下单失败
        $sid           = $this->_sid;

        $taobaoOrderService = new TaobaoOrderBusiness();
        $orderListRes       = $taobaoOrderService->getTaobaoOrderList($sid, $pagesize, $pagenum, $pftOrderNo,
            $taobaoOrderNo, $timeBegin, $timeEnd, $status);
        if ($orderListRes['code'] != 200) {
            $this->apiReturn(204, [], $orderListRes['msg']);
        }
        if (empty($orderListRes['data'])) {
            $this->apiReturn(200, [], '数据为空');
        }
        $this->apiReturn(200, $orderListRes['data'], '拉取成功');
    }

    /**
     * 失败订单列表
     *
     * Author : liucm
     * Date : 2021/12/17
     */
    public function orderFailList()
    {
        $pagesize    = I('post.page_size', 10, 'intval');
        $pagenum     = I('post.page_num', 1, 'intval');
        $timeBegin   = I('request.begin_time', '', 'strval'); // 起始时间
        $timeEnd     = I('request.end_time', '', 'strval'); // 结束时间
        $downOrderNo = trim(I('request.down_order_no', '', 'strval')); // 下游订单
        $pftOrderNo  = trim(I('request.pft_order_no', '', 'strval')); // 票付通订单
        $orderName   = trim(I('request.order_name', '', 'strval')); // 下单人姓名
        $orderMobile = trim(I('request.order_mobile', '', 'strval')); // 下单人手机
        $logId       = trim(I('request.log_id', '', 'strval')); // 日志id
        $sid         = $this->_sid;

        $orderFailService = new OrderFailBusiness();
        $orderListRes     = $orderFailService->getOrderFailList($sid, $pagesize, $pagenum, $pftOrderNo,
            $downOrderNo, $timeBegin, $timeEnd, $orderName, $orderMobile, $logId);
        if ($orderListRes['code'] != 200) {
            $this->apiReturn(204, [], $orderListRes['msg']);
        }
        if (empty($orderListRes['data'])) {
            $this->apiReturn(200, [], '数据为空');
        }
        $this->apiReturn(200, $orderListRes['data'], '拉取成功');
    }

    /**
     * 获取订单详情
     *
     * Author : liucm
     * Date : 2021/12/18
     */
    public function getOrderInfo()
    {
        $pftOrderNo = trim(I('request.pft_order_no', '', 'strval')); // 票付通订单
        if (empty($pftOrderNo)) {
            $this->apiReturn(204, '', '参数有误');
        }
        $sid        = $this->_sid;
        $orderService = new OrderBusiness();
        $orderListRes = $orderService->getOrderInfo($sid, $pftOrderNo);

        if ($orderListRes['code'] != 200) {
            $this->apiReturn(204, [], $orderListRes['msg']);
        }
        if (empty($orderListRes['data'])) {
            $this->apiReturn(200, [], '数据为空');
        }
        $this->apiReturn(200, $orderListRes['data'], '拉取成功');
    }

    /****************订单核销、退票通知、出票通知 *******************/

    public function getVerifyChangeLog()
    {
        $pftOrderNo      = trim(I('request.pft_order_no', '', 'strval')); // 票付通订单
        $orderService    = new OrderBusiness();
        $verifyChangeLog = $orderService->getVerifyChangeLog($pftOrderNo, $this->_memberInfo);
        $this->apiReturn(200, $verifyChangeLog['data'], 'success');
    }

    /**
     * 核销通知
     *
     * Author : liucm
     * Date : 2021/12/20
     */
    public function verifyNotice()
    {
        $pftOrderNo = trim(I('request.pft_order_no', '', 'strval')); // 票付通订单
        $tikTokPoiId = I('request.tik_tok_poi_id', '', 'strval,trim'); // 抖音团购门店id
        $trackId = I('request.track_id', 0, 'intval'); // 订单追踪记录表 id
        $code = I('request.code', '', 'strval'); // 需要通知的消费码

        if (empty($pftOrderNo)) {
            $this->apiReturn(204, '', '参数有误');
        }

        if (!empty($code)) {
            $code = explode(',', $code);
        } 

        $sid             = $this->_sid;
        $memberId        = $this->_memberId;
        $ip              = get_client_ip();
        $orderService    = new OrderNoticeBusiness();
        $verifyNoticeRes = $orderService->verifyNotice($sid, $pftOrderNo, $memberId, $ip, $tikTokPoiId, $trackId, $this->_memberInfo, $code);

        if ($verifyNoticeRes['code'] != 200) {
            $this->apiReturn(204, [], $verifyNoticeRes['msg']);
        }

        $this->apiReturn(200, $verifyNoticeRes['data'], $verifyNoticeRes['msg']);
    }

    /**
     * 审核列表
     *
     * Author : liucm
     * Date : 2021/12/28
     */
    public function auditList()
    {
        $pagesize     = I('post.page_size', 10, 'intval');
        $pagenum      = I('post.page_num', 1, 'intval');
        $pftOrderNo   = trim(I('request.pft_order_no', '', 'strval')); // 票付通订单
        $sid          = $this->_sid;
        $orderService = new OrderNoticeBusiness();
        $auditListRes = $orderService->auditList($sid, $pftOrderNo, $pagesize, $pagenum);

        if ($auditListRes['code'] != 200) {
            $this->apiReturn(204, [], $auditListRes['msg']);
        }
        if (empty($auditListRes['data'])) {
            $this->apiReturn(205, [], '数据为空');
        }
        $this->apiReturn(200, $auditListRes['data'], '拉取成功');
    }

    /**
     * 退票审核通知
     *
     * Author : liucm
     * Date : 2021/12/20
     */
    public function auditNotice()
    {
        $pftOrderNo = trim(I('request.pft_order_no', '', 'strval')); // 票付通订单
        $auditId    = trim(I('request.audit_id', '', 'strval')); //退票流水号
        $sid        = $this->_sid;
        $memberId   = $this->_memberId;
        $ip         = get_client_ip();
        if (empty($pftOrderNo) || empty($auditId)) {
            $this->apiReturn(204, '', '参数有误');
        }

        $orderService   = new OrderNoticeBusiness();
        $auditNoticeRes = $orderService->auditNotice($sid, $pftOrderNo, $auditId, $memberId, $ip);

        if ($auditNoticeRes['code'] != 200) {
            $this->apiReturn(204, [], $auditNoticeRes['msg']);
        }

        $this->apiReturn(200, [], $auditNoticeRes['msg']);
    }

    /**
     * 出票通知
     *
     * Author : liucm
     * Date : 2021/12/20
     */
    public function ticketNotice()
    {
        $pftOrderNo = trim(I('request.pft_order_no', '', 'strval')); // 票付通订单
        $sid        = $this->_sid;
        $memberId   = $this->_memberId;
        $ip         = get_client_ip();

        if (empty($pftOrderNo)) {
            $this->apiReturn(204, '', '参数有误');
        }

        $orderService    = new OrderNoticeBusiness();
        $ticketNoticeRes = $orderService->ticketNotice($sid, $pftOrderNo, $memberId, $ip);

        if ($ticketNoticeRes['code'] != 200) {
            $this->apiReturn(204, [], $ticketNoticeRes['msg']);
        }

        $this->apiReturn(200, $ticketNoticeRes['data'], $ticketNoticeRes['msg']);
    }

    /**
     * 获取有赞订单详情
     *
     * Author : liucm
     * Date : 2022/4/21
     */
    public function getYouZanOrderDetail()
    {
        $orderid   = I('request.orderid', '', 'strval,trim'); // 票付通订单
        $companyID = I('request.companyid', '', 'intval'); //有赞账号

        //if (ENV != 'PRODUCTION') {
        //    $companyID = 28227;
        //}

        $loginInfo = $this->_memberInfo;
        $sid       = $this->_sid;
        if (in_array($loginInfo['sdtype'], [2, 3])) {
            $isResource = true;
        } else {
            $isResource = false;
        }

        $orderMove = new \Business\Order\OrderQueryMove();

        //订单查询迁移
        $orderInfo = $orderMove->getListByOrderNumNew([$orderid], null, null, null, (int)$companyID);
        $orderInfo = $orderInfo[0] ?? [];

        if (empty($orderInfo)) {
            $this->apiReturn(204, '', '没有相关数据');
        }

        $orderLid = $orderInfo['lid'];
        if (!empty($orderLid)) {
            $landModel = new \Model\Product\Land();
            $landInfo  = $landModel->getLandInfo($orderLid, false, 'salerid');
        }

        $voucher  = $orderInfo['code'];
        $salerid  = isset($landInfo['salerid']) ? $landInfo['salerid'] : '';
        $aid      = $orderInfo['aid'];
        $member   = $orderInfo['member'];
        $apiOrder = $orderInfo['remotenum'];

        if ($aid != $loginInfo['memberID'] && $loginInfo['sid'] != 1 && $sid != $aid) {
            $this->apiReturn(204, '', '您不是团购订单所属者，请确认登录身份');
        }

        $auditModel = new RefundAuditModel();
        $auditInfo  = $auditModel->getTerminalChangeInfo($orderid, 0);
        $audit_id   = 0;
        if ($auditInfo) {
            $audit_id = $auditInfo['audit_id'];
        }

        $orders = (new TerminalOrderSearch())->getOrderByOrderCode($salerid, $voucher);
        if (empty($orders)) {
            $this->apiReturn(204, '', '查无匹配订单');
        }
        $orders = is_array($orders) ? $orders : [];

        foreach ($orders as $key => $oneOrder) {
            foreach ($oneOrder['tickets'] as $k2 => $val) {
                if (in_array($val['status'], [1, 3, 5, 6])) {
                    $oneOrder['tickets'][$k2]['tnum'] = 0;
                }
            }

            $oneOrder['isResource'] = $isResource;
            if (isset($_REQUEST['orderid']) && isset($_REQUEST['companyid'])) {
                if ($oneOrder['ordernum'] != $_REQUEST['orderid']) {
                    unset($orders[$key]);
                    continue;
                }

                $oneOrder['apiOrder'] = $apiOrder;
                $oneOrder['salerid']  = $salerid;
                $oneOrder['sid']      = $loginInfo['sid'];
                $oneOrder['is_audit'] = (bool)($audit_id && in_array($oneOrder['status'], [0, 2, 7]));
            }
            $orders[$key] = $oneOrder;
        }

        $data = $orders[$orderid];


        $this->apiReturn(200, $data, '');
    }

    /**
     * 获取批量推送列表
     *
     * <AUTHOR>
     * @date 2022-09-14
     */
    public function getBatchNoticeList()
    {
        $page       = I('request.page', 1, 'intval'); // 请求请求页码
        $startedAt  = I('request.started_at', date('Y-m-d 00:00:00'), 'strval,trim'); // 查询开始时间
        $endedAt    = I('request.ended_at', date('Y-m-d H:i:s'), 'strval,trim'); // 查询开始时间
        $operatorId = I('request.operator_id', '', 'strval,trim'); // 操作人 id
        $status     = I('request.status', '', 'strval,trim'); // 状态(0: 未处理 1: 处理中 2: 处理完成 3: 已终止)

        if ($operatorId == '') {
            $operatorId = null;
        }

        if ($status == '') {
            $status = null;
        }

        $orderNotice     = new OrderNoticeBusiness();
        $result = $orderNotice->getBatchNoticeList($this->_sid, $page, $startedAt, $endedAt, $operatorId, $status);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 中止任务
     *
     * <AUTHOR>
     * @date 2022-09-15
     *
     * @return mixed
     */
    public function abortTask()
    {
        $taskId      = I('request.task_id', 0, 'intval'); // 任务 id
        $orderNotice = new OrderNoticeBusiness();
        $result = $orderNotice->abortTask($this->_sid, $taskId);
        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 下载批量推送记录
     *
     * <AUTHOR>
     * @date 2022-09-15
     */
    public function batchExportVerifyNoticeRecord()
    {
        $taskId      = I('request.task_id', 0, 'intval'); // 任务 id
        $orderNotice = new OrderNoticeBusiness();
        $result      = $orderNotice->getVerifyNoticeRecord($this->_sid, $taskId);
        if ($result['code'] == 200) {
            $fileName  = '批量操作记录_' . date("Y_m_d_H_i_s");
            $sheetName = '批量操作记录';
            $this->excelReturn($fileName, $sheetName, $result['data'] ?? [], false);
        } else {
            $this->apiReturn(500, [], $result['msg']);
        }
    }

    /**
     * 批量推送 excel 模板下载
     *
     * <AUTHOR>
     * @date 2022-09-14
     */
    public function batchVerifyNoticeExcelTemplateDownload()
    {
        $excelTemplateList = [
            ['票付通订单号 (必填)', '抖音 POI/美团玩乐shopid (选填)', '注: 若订单需修改抖音 POI/美团玩乐shopid 后重推, 请在抖音 POI/美团玩乐shopid 一列填写正确信息']
        ];
        $this->excelReturn('批量推送模板', '批量推送', $excelTemplateList);
    }

    /**
     * 批量推送 excel 文件上传
     *
     * <AUTHOR>
     * @date 2022-09-14
     */
    public function batchVerifyNoticeExcelTemplateUpload()
    {
        if (!isset($_FILES['excel'])) {
            $this->apiReturn(204, [], '请上传excel');
        }

        $excel    = $_FILES['excel'];
        $filetype = strtolower(strrchr($excel['name'], "."));
        if (!in_array($filetype, ['.xlsx', '.xls'])) {
            $this->apiReturn(204, [], '文件格式不正确,请下载模板文件进行填写');
        }

        $excelProcess = new OrderFromExcel();
        $excelData    = $excelProcess->parseForBatchOrder($excel['tmp_name']);

        $orderNotice      = new OrderNoticeBusiness();
        $verifyNoticeTask = $orderNotice->createVerifyNoticeTask($excelData, $this->_memberInfo);

        if ($verifyNoticeTask['code'] != 200) {
            $this->apiReturn(204, [], $verifyNoticeTask['msg']);
        }

        $this->apiReturn(200, [], '上传成功');
    }
}
