<?php

namespace Controller\Ota;

use Library\Controller;

class YouZanTicketCodeConf extends Controller
{
    private $_sid;
    private $_memberId;
    private $_memberInfo;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 获取门票配置
     */
    public function getTicketCodeConf()
    {
        $sid        = $this->_sid;
        $tid        = I('post.tid', 0, 'intval');
        $youzanType = I('post.youzan_type', -1, 'intval');

        if (empty($tid)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        // 根据门票id获取产品类型
        $ticketInfo = (new \Business\CommodityCenter\Ticket)->queryTicketInfoById($tid);
        $pType      = $ticketInfo['land']['p_type'];

        // 年卡产品notice_type都是只能是3
        if ($pType == 'I') {
            $returnData = [
                'notice_type'  => 3,
                'is_show'      => 1,
                'product_type' => $pType
            ];
            $this->apiReturn(200, $returnData, 'success');
        }

        $returnData = [
            'notice_type'  => 1,
            'is_show'      => 1,
            'product_type' => $pType
        ];

        $model = new \Model\Ota\ApiYouZanTicketCodeConfModel();
        $res   = $model->getApiYouZanTicketCodeConf($sid, $tid, $youzanType);
        if (!empty($res)) {
            $returnData = [
                'notice_type'  => $res['notice_type'],
                'is_show'      => $res['is_show'],
                'product_type' => $pType
            ];
        }

        $this->apiReturn(200, $returnData, 'success');
    }

    /**
     * 增加或修改门票配置
     * 
     */
    public function addOrUpdateTbTicketCodeConf()
    {
        $tid        = I('post.tid', 0, 'intval');
        $noticeType = I('post.notice_type', -1, 'intval');
        $isShow     = I('post.is_show', 1, 'intval');
        $youzanType = I('post.youzan_type', -1, 'intval');
        $memberId   = $this->_sid;
        $opid       = $this->_memberId;

        if (empty($tid) || $noticeType == -1 || $isShow == -1) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model   = new \Model\Ota\ApiYouZanTicketCodeConfModel();
        $ConfRes = $model->getApiYouZanTicketCodeConf($memberId, $tid, $youzanType);
        if (!empty($ConfRes)) {
            $res = $model->updateApiYouZanTicketCodeConf($memberId, $tid, $noticeType, $isShow, $youzanType, $opid);
            if ($res !== false) {
                $this->apiReturn(200, [], '修改成功');
            }
            $this->apiReturn(204, [], '修改操作失败，请重试');
        }

        $res = $model->addApiYouZanTicketCodeConf($memberId, $tid, $noticeType, $isShow, $youzanType, $opid);
        if ($res !== false) {
            $this->apiReturn(200, [], '新增成功');
        }

        $this->apiReturn(204, [], '新增操作失败，请重试');
    }
}