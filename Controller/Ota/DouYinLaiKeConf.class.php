<?php

namespace Controller\Ota;

use Library\Controller;
use Business\OtaApi\OtaBase;

class DouYinLaiKeConf extends Controller
{
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 获取poi信息
     */
    public function queryUserPoiIdList()
    {
        $accountId = I('request.account_id', 0);

        if (empty($accountId)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $otaBase = new OtaBase();
        $res = $otaBase->call('ThirdPartner/DouYinTuanGou/getAccountPoiIdList', ['account_id' => $accountId]);

        $returnData = [];
        if ($res['code'] == 200) {
            $returnData = $res['data']['list'];
        }

        $this->apiReturn(200, $returnData, 'success');
    }

    /**
     * 获取门票配置
     *
     * Author : liucm
     * Date : 2021/9/24
     */
    public function getTicketCodeConf()
    {
        $returnData = [
            'notice_type'  => 1,
            'is_notice'    => 1,
            'extra_config' => '',
        ];

        $sid   = $this->_sid;
        $tid   = I('post.tid', 0, 'intval');

        if (empty($tid)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model = new \Model\Ota\DouYinLaiKeTicketCodeConfModel();
        $res   = $model->getAppTicketCodeConf($sid, $tid);
        if (!empty($res)) {
            $noticeTypeArr = explode('+', $res['notice_type']);

            $returnData = [
                'notice_type'  => $noticeTypeArr[0],
                'is_notice'    => $res['is_notice'],
                'extra_config' => $noticeTypeArr[1] ?? '',
            ];
        }

        $this->apiReturn(200, $returnData, 'success');
    }

    /**
     * 增加或修改门票配置
     *
     * Author : liucm
     * Date : 2021/9/24
     */
    public function addOrUpdateTbTicketCodeConf()
    {
        $tid        = I('post.tid', 0, 'intval');
        $noticeType = I('post.notice_type', -1, 'intval');
        $isNotice   = I('post.is_notice', -1, 'intval');
        $extConfig  = I('post.extra_config', '');
        $memberId   = $this->_sid;
        $opid       = $this->_memberId;
        if (empty($tid) || $noticeType == -1) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        // 门票码 | 配置身份证 | 凭证码+身份证 | 门票码+身份证
        if (in_array($noticeType, [2, 3, 4, 5])) {
            $javaApi   = new \Business\CommodityCenter\Ticket();
            $ticketArr = $javaApi->queryTicketInfoByIds([$tid], 'id,batch_check', '', '', 'tourist_info');

            if (in_array($noticeType, [2, 5]) && $ticketArr[0]['ext']['print_mode'] == 1) {
                $this->apiReturn(204, [], '门票属性设置不打印，不可配置门票码返码方式'); 
            }

            // 配置身份证 | 凭证码+身份证 | 门票码+身份证
            if (in_array($noticeType, [3, 4, 5])) {
                if (!empty($ticketArr[0]['land_f']) && in_array($ticketArr[0]['land_f']['tourist_info'], [0, 3])) {
                    $this->apiReturn(204, [], '门票属性未设置下单需要证件号，不可配置身份证返码方式'); 
                }
            }
        }

        if (!empty($extConfig)) {
            $noticeType = $noticeType. '+'. $extConfig;
        }

        $model   = new \Model\Ota\DouYinLaiKeTicketCodeConfModel();
        $ConfRes = $model->getAppTicketCodeConf($memberId, $tid);
        if (!empty($ConfRes)) {
            $res = $model->updateAppTicketCodeConf($memberId, $tid, $noticeType, $isNotice, $opid);
            if ($res !== false) {
                $this->apiReturn(200, [], '修改成功');
            }
            $this->apiReturn(204, [], '修改操作失败，请重试');
        }

        $res = $model->addAppTicketCodeConf($memberId, $tid, $noticeType, $isNotice, $opid);
        if ($res !== false) {
            $this->apiReturn(200, [], '新增成功');
        }

        $this->apiReturn(204, [], '新增操作失败，请重试');
    }

    /**
     * 检查是否开启urls配置
     *
     * @return string
     */
    // public function checkIsOpenUrlConf()
    // {
    //     $param = [
    //         'sid'  => $this->_sid,
    //         'code' => 'douyinlaile_url_code', // 抖音来客url配置
    //     ];

    //     $lib      = new \Library\JsonRpc\PftRpcClient('platform_to_open');
    //     $queryRes = $lib->call('Authority/AccessConfig/checkAccess', [$param], 'open_downstream');

    //     if ($queryRes['code'] != 200) {
    //         $this->apiReturn(205, [], '获取权限配置失败');
    //     }

    //     if ($queryRes['data']['douyinlaile_url_code'] == true) {
    //         $this->apiReturn(200, [], 'urls已配置开白');
    //     }

    //     $this->apiReturn(205, [], 'urls已配置未开白'); 
    // }
}