<?php
/**
 * 马蜂窝自营配置管理
 */

namespace Controller\Ota;

use Library\Controller;
use Model\Ota\MaFengWoZiYingConfigModel;

class MaFengWoZiYingConfig extends Controller
{
    private $_sid;

    public function __construct()
    {
        parent::__construct();

        $loginInfo  = $this->getLoginInfo();
        $this->_sid = $loginInfo['sid'];
    }

    /**
     * 设置用户的马蜂窝信息
     * <AUTHOR>
     * @date 2021-09-03
     */
    public function setConfig()
    {
        $clientId     = I('post.client_id', '');
        $clientSecret = I('post.client_secret', '');
        $aesKey       = I('post.aes_key', '');
        $sid          = $this->_sid;

        if (empty($clientId)) {
            $this->apiReturn(205, [], '店铺编码不能为空');
        }

        $confModel = new MaFengWoZiYingConfigModel();
        $config    = $confModel->getConfByClientId($clientId);
        if (!empty($config)) {
            if ($config['client_id'] == $clientId && $config['member_id'] != $sid) {
                $this->apiReturn(205, [], '该 client_id 已被使用,请使用其他 client_id');
            }
        }

        $configArr = $confModel->getConfByMemberId($sid);
        if (empty($configArr)) {
            $insData = [
                'member_id'     => $sid,
                'client_id'     => $clientId,
                'client_secret' => $clientSecret,
                'aes_key'       => $aesKey,
            ];
            $res     = $confModel->insertConf($insData);
        } else {
            $upData = [
                'client_id'     => $clientId,
                'client_secret' => $clientSecret,
                'aes_key'       => $aesKey,
            ];
            $res    = $confModel->editConfByMemberId($sid, $upData);
        }
        if ($res) {
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    /**
     * 获取用户的马蜂窝配置信息
     * <AUTHOR>
     * @date 2021-09-03
     */
    public function getConfig()
    {
        $sid        = $this->_sid;
        $confModel  = new MaFengWoZiYingConfigModel();
        $configArr  = $confModel->getConfByMemberId($sid);
        $returnList = [];
        if (!empty($configArr)) {
            $returnList = [
                'client_id'     => $configArr['client_id'],
                'client_secret' => $configArr['client_secret'],
                'aes_key'       => $configArr['aes_key'],
            ];
        }

        $this->apiReturn(200, $returnList, 'success');
    }
}