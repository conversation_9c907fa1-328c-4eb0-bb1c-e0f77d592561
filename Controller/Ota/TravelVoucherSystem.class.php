<?php

namespace Controller\Ota;

use Library\Controller;
use Business\Ota\Upstream\SystemTravelVoucherLandId;

class TravelVoucherSystem extends Controller
{
    protected $_sid;
    protected $_opid;

    public function __construct()
    {
        $loginInfo   = $this->getLoginInfo();
        $this->_sid  = $loginInfo['sid'];
        $this->_opid = $loginInfo['memberID'];
    }

    /**
     * 获取绑定产品列表
     *
     * <AUTHOR>
     * @date 2022-10-26
     */
    public function getBindProductList()
    {
        $memberId   = $this->_sid;
        $page       = I('post.page', '1', 'intval');
        $pageSize   = I('post.page_size', '10', 'intval');
        $landId     = I('post.land_id', '', 'intval');
        $landName   = I('post.land_name', '', 'strval,trim');
        $systemId   = I('post.system_id', '', 'strval,trim');
        $landStatus = I('post.land_status', '1', 'strval');

        $systemTravelVoucherLandId = new SystemTravelVoucherLandId();
        $result                    = $systemTravelVoucherLandId->getBindProductList($page, $pageSize, $memberId,
            $landId, $landName, $systemId, $landStatus);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取绑定产品详情
     *
     * <AUTHOR>
     * @date 2022-10-26
     */
    public function getBindProductDetail()
    {
        $memberId = $this->_sid;
        $landId   = I('post.land_id', '', 'intval');

        $systemTravelVoucherLandId = new SystemTravelVoucherLandId();
        $result                    = $systemTravelVoucherLandId->getBindProductDetail($memberId, $landId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 添加、编辑绑定产品
     *
     * <AUTHOR>
     * @date 2022-10-26
     */
    public function editBindProduct()
    {
        $memberId   = $this->_sid;
        $operatorId = $this->_opid;
        $landId     = I('post.land_id', '', 'intval');
        $systemId   = I('post.system_id', '', 'intval');
        $secretId   = I('post.secret_id', '', 'intval');

        $systemTravelVoucherLandId = new SystemTravelVoucherLandId();
        $result                    = $systemTravelVoucherLandId->editBindProduct($memberId, $operatorId, $systemId,
            $secretId, $landId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 产品解绑
     *
     * <AUTHOR>
     * @date 2022-10-26
     */
    public function unbindProduct()
    {
        $memberId   = $this->_sid;
        $operatorId = $this->_opid;
        $landId     = I('post.land_id', '', 'intval');

        $systemTravelVoucherLandId = new SystemTravelVoucherLandId();
        $result                    = $systemTravelVoucherLandId->unbindProduct($memberId, $operatorId, $landId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取已绑定券列表
     *
     * <AUTHOR>
     * @date 2022-10-26
     */
    public function getBindTicketList()
    {
        $memberId     = $this->_sid;
        $page         = I('post.page', '1', 'intval');
        $pageSize     = I('post.page_size', '10', 'intval');
        $landId       = I('post.land_id', '', 'intval');
        $ticketId     = I('post.ticket_id', '', 'intval');
        $ticketName   = I('post.ticket_name', '', 'strval,trim');
        $bindStatus   = I('post.bind_status', '1', 'intval');
        $ticketStatus = I('post.ticket_status', '1', 'strval');

        $systemTravelVoucherLandId = new SystemTravelVoucherLandId();
        $result                    = $systemTravelVoucherLandId->getBindTicketList($page, $pageSize, $memberId,
            $landId, $ticketId, $ticketName, $bindStatus, $ticketStatus);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 券绑定
     *
     * <AUTHOR>
     * @date 2022-10-26
     */
    public function bindTicket()
    {
        $memberId         = $this->_sid;
        $operatorId       = $this->_opid;
        $landId           = I('post.land_id', '', 'intval');
        $ticketId         = I('post.ticket_id', '', 'intval');
        $thirdProductCode = I('post.third_product_code', '', 'strval,trim');
        $systemId         = I('post.system_id', '', 'intval');
        $sendSms          = I('post.send_sms', '', 'intval');
        $opType           = I('post.op_type ', 0, 'intval');

        $systemTravelVoucherLandId = new SystemTravelVoucherLandId();
        $result                    = $systemTravelVoucherLandId->bindTicket($memberId, $operatorId, $landId, $ticketId,
            $thirdProductCode, $systemId, $sendSms, $opType);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 券解绑
     *
     * <AUTHOR>
     * @date 2022-10-26
     */
    public function unbindTicket()
    {
        $memberId   = $this->_sid;
        $operatorId = $this->_opid;
        $landId     = I('post.land_id', '', 'intval');
        $ticketId   = I('post.ticket_id', '', 'intval');

        $systemTravelVoucherLandId = new SystemTravelVoucherLandId();
        $result                    = $systemTravelVoucherLandId->unbindTicket($memberId, $operatorId, $landId,
            $ticketId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}