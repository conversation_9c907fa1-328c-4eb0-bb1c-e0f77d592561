<?php

/**
 * 文惠通配置文件
 *
 * Class WenHuiTong
 * @package Controller\Ota
 */

namespace Controller\Ota;

use Library\Controller;
use Model\Ota\WenHuiTongConf;

class WenHuiTong extends Controller
{
    private $_memberInfo = [];
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 设置文惠通配置信息
     *
     * Author : liucm
     * Date : 2021/7/24
     */
    public function setWenHuiTongConf()
    {
        $mid        = I('post.mid', '', 'strval,trim');
        $userName   = I('post.user_name', '', 'strval,trim');
        $scurityKey = I('post.scurity_key', '', 'strval,trim');
        $sid        = $this->_sid;

        if (empty($mid) || empty($userName) || empty($scurityKey)) {
            $this->apiReturn(203, [], '参数错误');
        }

        if (strlen($mid) > 20) {
            $this->apiReturn(203, [], '分销商渠道id参数错误');
        }
        if (strlen($userName) > 50) {
            $this->apiReturn(203, [], '登录用户名参数错误');
        }
        if (strlen($scurityKey) > 100) {
            $this->apiReturn(203, [], '商户秘钥参数错误');
        } 

        $otherSysConfigModel = new WenHuiTongConf();
        $WenHuiTongConf      = $otherSysConfigModel->getWenHuiTongConfByMemberId($sid);
        if (empty($WenHuiTongConf)) {
            $res = $otherSysConfigModel->insertWenHuiTongConf($sid, $mid, $userName, $scurityKey);
        } else {
            $res = $otherSysConfigModel->editWenHuiTongConfByMemberId($sid, $mid, $userName, $scurityKey);
        }
        if ($res !== false) {
            $this->apiReturn(200, [], '保存成功');
        } else {
            $this->apiReturn(205, [], '保存失败');
        }
    }

    /**
     * 获取用户的文惠通配置信息
     * 
     */
    public function getWenHuiTongInfo()
    {
        $sid                 = $this->_sid;
        $otherSysConfigModel = new WenHuiTongConf();
        $WenHuiTongConf      = $otherSysConfigModel->getWenHuiTongConfByMemberId($sid);
        $returnList          = [];
        if (!empty($WenHuiTongConf)) {
            $returnList['mid']         = $WenHuiTongConf['mid'];
            $returnList['user_name']   = $WenHuiTongConf['user_name'];
            $returnList['scurity_key'] = $WenHuiTongConf['scurity_key'];
        }

        $this->apiReturn(200, $returnList, 'success');
    }

}