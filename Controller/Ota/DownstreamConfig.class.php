<?php

/**
 * 下游绑定编码
 *
 * Class DownstreamConfig
 * @package Controller\Ota
 */

namespace Controller\Ota;

use Library\Controller;
use Business\Ota\DownstreamConfig\Config as ConfigBusiness;
use Library\JsonRpc\PftRpcClient;
use Throwable;

class DownstreamConfig extends Controller
{
    private $_memberInfo = [];
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 绑定门票编码
     *
     * Author : liucm
     * Date : 2022/4/11
     */
    public function bindTicketCode()
    {
        $identity                    = I('post.Identity', '', 'strval,trim');//账号
        $sign                        = I('post.sign', '', 'strval,trim');//秘钥
        $dockingMode                 = I('post.conf_type', '', 'intval');//对应pft_con_sys.id
        $handOnSingle                = I('post.hand_on_single', '', 'intval');//判断是否推送美团,0-推送,1-不推送
        $cooperationWay              = I('post.cooperation_way', '', 'intval');//不知道干啥的
        $thirdPartTeamworkId         = I('post.third_part_teamwork_id', '', 'strval,trim');//
        $thirdPartTeamworkIdOriginal = I('post.third_part_teamwork_id_original', '', 'strval,trim');//
        $tid                         = I('post.tid', '', '');//门票id
        $tidAid                      = I('post.tid_aid', '', '');//tid_aid
        $memberId                    = $this->_sid;//id
        $opid                        = $this->_memberId;//操作人id
        $ticketType                  = I('post.ticket_type', 10, 'intval');// 10-团购券 30-半直连 50-预约团购券
        $ptype                       = I('post.ptype', 'A', 'strval,trim');// 门票类型：A-景区 H-演出 F-套票
        $supplierId                  = I('post.superior_id', 0);
        $kuaishouStoreId             = I('post.kuaishouStoreId', 0);
        $dockingForm                 = I('post.docking_form', 0, 'intval');    //门票对接形态 0：门票类型【对接旧接口】 1：演出类型【V2接口】
        if (empty($supplierId)) {
            $supplierId = I('post.pftSupplierId', 0);
        }
        $configBusiness              = new ConfigBusiness();

        $res = $configBusiness->bindTicketCode($identity, $sign, $dockingMode, $handOnSingle,
            $cooperationWay, $thirdPartTeamworkId, $thirdPartTeamworkIdOriginal, $tid, $tidAid, $memberId,
            $opid, $supplierId, $kuaishouStoreId, $dockingForm);

        if ($res['code'] != 200) {
            $this->apiReturn(204, [], $res['msg']);
        }

        if (($dockingMode == 408 && ENV == 'PRODUCTION') ||
            ($dockingMode == 246 && ENV == 'TEST') ||
            ($dockingMode == 253 && ENV == 'DEVELOP')) {
            // 调用开放平台写入绑定数据
            $operatorId = $this->_memberInfo['memberID'];
            $bind       = $this->callRpc('calendarTicket/DouYinTicket/createTicket', [
                'detail'       => [
                    'fid'               => $this->_sid,
                    'bind_product_code' => $res['data']['bind_product_code'],
                    'ticket_id'         => $res['data']['ticket_id'],
                    'product_id'        => $res['data']['product_id'],
                    'product_code'      => $res['data']['product_code'],
                    'supplier_id'       => $res['data']['supplier_id'],
                    'douyin_poi_id'     => $res['data']['douyin_poi_id'],
                    'bind_id'           => $res['data']['bind_id'],
                    'operator_id'       => $operatorId,
                    'ticket_type'       => $ticketType,
                    'ticket_name'       => $res['data']['douyin_ticket_name'],
                    'goods_name'        => $res['data']['douyin_goods_name'],
                    'ptype'             => $ptype,
                ],
                'product_data' => [],
                'sku_data'     => [],
                'douyin'       => [],
            ]);
        }

        $this->apiReturn(200, $res['data'], $res['msg']);
    }

    /**
     * 解绑门票编码
     *
     * Author : liucm
     * Date : 2022/4/11
     */
    public function unBindTicketCode()
    {
        $id = I('post.bind_id', '', '');//id

        $memberId = $this->_sid;//id
        $opid     = $this->_memberId;//操作人id

        if (empty($id)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $configBusiness = new ConfigBusiness();
        $res            = $configBusiness->unBindTicketCode($id, $memberId, $opid);
        if ($res['code'] != 200) {
            $this->apiReturn(204, [], $res['msg']);
        }

        // 获取dockingMode
        foreach ($res['data'] as $data) {
            $dockingMode = $data['docking_mode'];//对应pft_con_sys.id
            if (($dockingMode == 408 && ENV == 'PRODUCTION')
                || ($dockingMode == 246 && ENV == 'TEST')) {
                // 调用开放平台写入解绑数据
                $unBindSyncRes = $this->callRpc('calendarTicket/DouYinTicket/unbindGroupTicket', [
                    'fid'         => $this->_sid,
                    'ticket_id'   => $data['ticket_id'],
                    'supplier_id' => $data['supplier_id'],
                    'bind_id'     => $data['bind_id'],
                ]);
                // 多规格的解绑的bindid 多个逗号分割在表里的，所以多做一次重试多规格的解绑
                if ($unBindSyncRes['code'] != 200) {
                    $unBindSyncRes = $this->callRpc('calendarTicket/DouYinTicket/unbindGroupTicket', [
                        'fid'         => $this->_sid,
                        'ticket_id'   => $data['ticket_id'],
                        'supplier_id' => $data['supplier_id'],
                        'bind_id'     => $id,
                    ]); 
                }
            }

            // 重置快手返码配置
            if ((ENV == 'PRODUCTION' && $dockingMode == 406) || (ENV == 'TEST' && $dockingMode == 403)) {
                $model   = new \Model\Ota\ApiKuaiShouLocalTicketCodeConfModel();
                $ConfRes = $model->getApiKuaiShouLocalTicketCodeConf($memberId, $data['ticket_id']);
                if (!empty($ConfRes)) {
                    $model->updateApiKuaiShouLocalTicketCodeConf($memberId, $data['ticket_id'], 2, 1, $opid);
                }
            }
        }

        $this->apiReturn(200, $res['data'], $res['msg']);
    }

    /**
     * 哈希加密
     *
     * Author : liucm
     * Date : 2022/4/11
     */
    public function encryptCode()
    {
        $thirdPartTeamworkId = I('post.third_part_teamwork_id', '', '');
        $dockingMode         = I('post.systenId', '', '');

        if (empty($thirdPartTeamworkId)) {
            $this->apiReturn(204, [], '字段错误');
        }
        $str = '_';
        [$tmp, $tmp1] = explode('_', $thirdPartTeamworkId);

        if (empty($tmp1)) {
            [$tmp, $tmp1] = explode('+', $thirdPartTeamworkId);
            $str = '+';
        }

        if (empty($tmp1)) {
            [$tmp, $tmp1] = explode('#', $thirdPartTeamworkId);
            $str = '#';
        }

        $encryptCode = $this->encrypt($dockingMode);

        if (!empty($tmp1)) {
            $encryptCode = $encryptCode . $str . $tmp1;
        }

        $this->apiReturn(200, ['encryptCode' => $encryptCode], '');
    }

    /**
     * 生成随机
     *
     * @param $dockingMode
     *
     * @return false|string
     * Author : liucm
     * Date : 2022/4/12
     */
    private function encrypt($dockingMode)
    {
        $code           = substr(md5(uniqid() . rand(100000, 999999)), 8, 16);
        $sysConfigModel = new \Model\Ota\SysConfig();
        //重复校验
        while ($sysConfigModel->findQunarInfoByModeThirdId($dockingMode, $code)) {
            return $this->encrypt($dockingMode);
        }

        return $code;
    }

    /**
     * rpc 请求
     *
     * <AUTHOR>
     * @date 2022-08-31
     *
     * @param  string  $method
     * @param  array  $rpcData
     *
     * @return array
     */
    protected function callRpc(string $method, array $rpcData): array
    {
        $result = [
            'code' => 200,
            'msg'  => 'success',
            'data' => [],
        ];
        try {
            $lib            = new PftRpcClient('open_platform_api');
            $jsonRpcRes     = $lib->call($method, [
                $rpcData,
            ], 'admin_service');
            $result['data'] = $jsonRpcRes['data'];
            $result['code'] = $jsonRpcRes['code'];
            $result['msg']  = $jsonRpcRes['msg'];
        } catch (Throwable $e) {
            $result['code'] = $e->getCode();
            $result['msg']  = $e->getMessage();
        }

        return $result;
    }
}