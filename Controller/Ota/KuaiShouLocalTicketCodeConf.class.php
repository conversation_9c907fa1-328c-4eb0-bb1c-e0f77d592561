<?php

namespace Controller\Ota;

use Library\Controller;

class KuaiShouLocalTicketCodeConf extends Controller
{
    private $_sid;
    private $_memberId;
    private $_memberInfo;

    private $_newOpenUrl = [
        'TEST'       => 'http://open-distribution-manage-internal.12301dev.com:9080',
        'GRAY'       => 'http://open-distribution-manage-internal-gray.pft12301.com',
        'PRODUCTION' => 'http://open-distribution-manage-internal.pft12301.com',
    ];

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 获取门票配置
     */
    public function getTicketCodeConf()
    {
        $returnData = [
            'notice_type' => 2,
            'is_show'     => 1,
        ];

        $sid = $this->_sid;
        $tid = I('post.tid', 0, 'intval');

        if (empty($tid)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model = new \Model\Ota\ApiKuaiShouLocalTicketCodeConfModel();
        $res   = $model->getApiKuaiShouLocalTicketCodeConf($sid, $tid);
        if (!empty($res)) {
            $returnData = [
                'notice_type' => $res['notice_type'],
                'is_show'     => $res['is_show'],
            ];
        }

        $this->apiReturn(200, $returnData, 'success');
    }

    /**
     * 增加或修改门票配置
     * 
     */
    public function addOrUpdateTbTicketCodeConf()
    {
        $tid        = I('post.tid', 0, 'intval');
        $noticeType = I('post.notice_type', -1, 'intval');
        $isShow     = I('post.is_show', 1, 'intval');
        $memberId   = $this->_sid;
        $opid       = $this->_memberId;
        
        if (empty($tid) || $noticeType == -1 || $isShow == -1) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        // 判断是否对接了外部码
        $codeInfo = (new \Business\ExternalCode\CodeManage())->getExternalCodeInfoByTids((string)$tid);
        if ($codeInfo['code'] == 200 && !empty($codeInfo['data'][0])) {
            if ($codeInfo['data'][0]['send_type'] != $noticeType)  {
                $this->apiReturn(204, [], '对接的外部码通知类型与门票配置通知类型不一致');
            }
        }

        $model   = new \Model\Ota\ApiKuaiShouLocalTicketCodeConfModel();
        $ConfRes = $model->getApiKuaiShouLocalTicketCodeConf($memberId, $tid);
        if (!empty($ConfRes)) {
            $res = $model->updateApiKuaiShouLocalTicketCodeConf($memberId, $tid, $noticeType, $isShow, $opid);
            if ($res !== false) {

                $requestParmsArr = [
                    'tid' => $tid,
                    'sid' => $memberId,
                ];

                // 获取通知地址
                $noticeUrl = $this->_newOpenUrl[ENV];
                if (defined('IS_PFT_GRAY')) {
                    $noticeUrl = $this->_newOpenUrl['GRAY'];
                }

                curl_post($noticeUrl . '/kuaishou/locallife/goods/updateTime', 
                            $requestParmsArr, 80, 25, '/api/curl_post', [], false, '', 'get');

                $this->apiReturn(200, [], '修改成功');
            }
            $this->apiReturn(204, [], '修改操作失败，请重试');
        }

        $res = $model->addApiKuaiShouLocalTicketCodeConf($memberId, $tid, $noticeType, $isShow, $opid);
        if ($res !== false) {

            $requestParmsArr = [
                'tid' => $tid,
                'sid' => $memberId,
            ];

            curl_post($this->_newOpenUrl[ENV] . '/kuaishou/locallife/goods/updateTime', 
                        $requestParmsArr, 80, 25, '/api/curl_post', [], false, '', 'get');

            $this->apiReturn(200, [], '新增成功');
        }

        $this->apiReturn(204, [], '新增操作失败，请重试');
    }
}