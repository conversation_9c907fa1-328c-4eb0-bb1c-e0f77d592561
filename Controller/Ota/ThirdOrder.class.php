<?php
/**
 * 第三方订单的推送查看
 *
 * <AUTHOR>
 * @date   2017-08-15
 */

namespace Controller\Ota;

use Business\Ota\Upstream\CommonOrder;
use Library\Controller;
use Model\Order\AllApiOrderModel;
use Model\Order\OrderTools;
use Model\Ota\SysConfig;



use Model\Product\Ticket;

class ThirdOrder extends Controller
{
    private $_memberInfo = [];
    private $_sid;
    private $_memberId;
    private $_isSuper;
    //ota数据模型
    protected $_otaModel = null;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
        $this->_isSuper    = $this->isSuper();
    }

    /**
     * 获取设置了对接第三方的订单列表  https://my.12301.cc/third_order.html
     * <AUTHOR>
     * @date   17-08-14
     * @return string json
     *
     */
    public function thirdOrderList()
    {
        $landId     = I('request.land_id', 0, 'intval'); // 景区id
        $page       = I('request.current_page', 1, 'intval'); // 当前页
        $pageSize   = I('request.page_size', 15, 'intval'); // 显示条数
        $timeBegin  = I('request.btime', '', 'strval'); // 起始时间
        $timeEnd    = I('request.etime', '', 'strval'); // 结束时间
        $selectType = I('request.select_type', 1, 'intval'); // 1:订单号 2:联系人 3:手机号 4:第三方订单号
        $selectText = I('request.select_text', '', 'strval'); // 搜索的内容
        $isSend     = I('request.is_send', '', 'intval'); // 1:选择可以推送的

        // 参数判断
        if ($landId == 0) {
            echo $this->_return(false, '请选择景区');
            exit;
        }

        // 判断该景区是否设置对接
        $sysConfig = new SysConfig();
        $csysId    = $sysConfig->getCsysByLid($landId);

        if (empty($csysId)) {
            echo $this->_return(false, '该景区未设置对接系统');
            exit;
        }

        // 如果没有设置时间 默认选择七天内
        if (!$timeBegin) {
            $today     = date('Y-m-d');
            $timeBegin = date('Y-m-d 00:00:00', strtotime("$today -7 day"));
        } else {
            $timeBegin = $timeBegin . " 00:00:00";
        }

        if (!$timeEnd) {
            $timeEnd = date('Y-m-d 23:59:59');
        } else {
            $timeEnd = $timeEnd . " 23:59:59";
        }

        // 选择是否可以推送的
        if ($isSend == 1) {
            $isSend = true;
        } else {
            $isSend = false;
        }

        $orderNum  = '';
        $orderName = '';
        $orderTel  = '';
        switch ($selectType) {
            case '1':
                $orderNum = $selectText;
                break;
            case '2':
                $orderName = $selectText;
                break;
            case '3':
                $orderTel = $selectText;
            case '4':
                // 根据第三方订单号
                $apiModel     = new AllApiOrderModel();
                $apiOrderInfo = $apiModel->getListInfo('pftOrder', ['apiOrder' => $selectText], 0, 1);
                $orderNum     = $apiOrderInfo[0]['pftOrder'] ?: 0;
        }

        // 页码处理
        $page     = max($page, 1);
        $pageSize = ($pageSize > 100 || $pageSize < 1) ? 20 : $pageSize;

        $bizOrderStatusArr = [];

        $queryRes = (new \Business\Order\QueryForES())->getOrderInfoList($this->_sid, $timeBegin, $timeEnd, $landId, $orderNum, $orderName, $orderTel, $isSend, $bizOrderStatusArr, $page, $pageSize);
        if ($queryRes['code'] != 200) {
            echo $this->_return(false, $queryRes['msg']);
            exit;
        }

        $count = $queryRes['data']['total'] ?? 0;
        $list  = $queryRes['data']['list'] ?? [];

        // 获取该景区的对接系统名称
        $csysInfoArr = $sysConfig->getCsysInfoByids($csysId['csysid'], 'name');

        // 数据格式处理
        $data = [];
        if ($list) {
            $statusConfig = load_config('order_status');
            foreach ($list as $listKey => $listValue) {
                // 获取 ltitle.ttitle
                $javaApi       = new \Business\CommodityCenter\Ticket();
                $ticketArr     = $javaApi->queryTicketInfoByIds([$listValue['tid']], 'id,title', '', 'title');
                $ticketInfoArr = [];
                if (!empty($ticketArr)) {
                    foreach ($ticketArr as $ticket) {
                        $ticketInfoArr[$ticket['ticket']['id']] = $ticket['land']['title'] . $ticket['ticket']['title'];
                    }
                }

                $data[$listKey]['is_super']  = false;
                $data[$listKey]['csys_name'] = $csysInfoArr[0]['name'];
                $data[$listKey]['ordernum']  = $listValue['ordernum'];
                $data[$listKey]['ordername'] = $listValue['ordername'];
                $data[$listKey]['ordertel']  = $listValue['ordertel'];
                $data[$listKey]['tnum']      = $listValue['tnum'];
                $data[$listKey]['ordertime'] = $listValue['ordertime'];
                $data[$listKey]['status']    = $listValue['status'];
                $data[$listKey]['lttitle']   = $ticketInfoArr[$listValue['tid']];
                $data[$listKey]['tordernum'] = $listValue['tordernum'];
                $data[$listKey]['is_send']   = 1;
                if (strpos($listValue['tordernum'], '&') !== false) {
                    $orderNumArr                 = explode('&', $listValue['tordernum']);
                    $data[$listKey]['tordernum'] = $orderNumArr[0];
                    $data[$listKey]['is_send']   = 0;
                }

                $data[$listKey]['status'] = $statusConfig[$listValue['status']];
            }

            if ($data) {
                $pageNum    = ceil($count / $pageSize);
                $returnData = ['data_num' => $count, 'page_num' => $pageNum, 'data_list' => $data];
                echo $this->_return(true, '数据取得成功', $returnData);
                exit;
            } else {
                echo $this->_return(false, '无数据');
                exit;
            }

        } else {
            echo $this->_return(false, '无数据');
            exit;
        }
    }

    /**
     * 推送第三方订单
     * <AUTHOR>
     * @date    17-08-17
     * @return  json
     *
     */
    public function sendThirdOrder()
    {
        // 获取需要处理的订单号
        $orderNum = I('request.order_num', '', 'strval');

        if ($orderNum === '') {
            echo $this->_return(false, '订单号不能为空', ['pft_order' => $orderNum]);
            exit;
        }

        // 获取订单详情
        //$orderTools   = new OrderTools();
        //$orderInfoArr = $orderTools->getOrderJoinAddonList('', '', '', $orderNum);

        //$orderNumArr = [];
        //if ($orderNum) {
        //    $orderNumArr = [$orderNum];
        //}
        //
        //$queryParams = ['', '', 0, $orderNumArr];
        //$queryRes    = \Business\JavaApi\Order\Query\Container::query('addonPage','getThirdPartyOrders', $queryParams);

        $queryRes = (new \Business\Order\QueryForES())->getOrderInfoDetail($this->_sid, $orderNum);
        if ($queryRes['code'] != 200) {
            echo $this->_return(false, $queryRes['msg']);
            exit;
        }

        $orderInfoArr = $queryRes['data']['list'];
        // $orderInfoArr = $list[0];

        if (empty($orderInfoArr)) {
            echo $this->_return(false, '无该订单', ['pft_order' => $orderNum]);
            exit;
        }

        // 获取门票绑定三方信息
        $otaProductBiz      = new \Business\Ota\Product();
        $thirdTicketConfArr = $otaProductBiz->getTicketConfigByTid($orderInfoArr[0]['tid']);

        $Mdetails = $thirdTicketConfArr['Mdetails'];
        $uuid     = $thirdTicketConfArr['uuid'];

        // 判断票是否绑定
        if (!$uuid || $Mdetails != 1) {
            echo $this->_return(false, '该票未绑定第三方门票', ['pft_order' => $orderNum]);
            exit;
        }
        $otaOpenOrderBiz = new \Business\Ota\Open\Order();
        $repushResultArr = $otaOpenOrderBiz->repushOrder(['orderNum' => $orderNum, 'LandId' => $orderInfoArr[0]['lid']]);

        if ($repushResultArr['code'] == 200) {
            if ($repushResultArr['data'][0]['code'] == 200) {
                $apiOrder = $repushResultArr['data'][0]['data']['VTordernum'];
                $msg      = '推送成功';
                $code     = true;
            } else {
                $code = false;
                $msg  = $repushResultArr['data'][0]['msg'];
            }
        } else {
            $code = false;
            $msg  = $repushResultArr['msg'];
        }

        $data = [
            'api_order' => $apiOrder,
            'pft_order' => $orderNum,
        ];
        echo $this->_return($code, $msg, $data);
    }

    /**
     * 第三方订单返回
     * <AUTHOR>
     * @date  17-08-04
     * $code  bool
     * $msg   string
     * $data  array|''
     * @return mixed
     *
     */
    private function _return($code, $msg, $data = '')
    {
        return json_encode([
            'code' => $code,
            'msg'  => $msg,
            'data' => $data,
        ]);
    }

    /**
     * 查看订单第三方订单的详情
     * <AUTHOR>
     * @date   17-08-17
     *
     * @param  int 订单号
     *
     * @return string
     *
     */
    public function thirdOrderView()
    {
        // 获取订单号参数
        $orderNum = I('request.order_num', '', 'strval');

        if ($orderNum === '') {
            echo $this->_return(false, '订单号不能为空', ['pft_order' => $orderNum]);
            exit;
        }

        $otaSysConfigBiz = new \Business\Ota\SystemConfig();
        $orderViewArr    = $otaSysConfigBiz->thirdOrderView($orderNum);

        if ($orderViewArr['code'] == 200) {
            echo $this->_return(true, $orderViewArr['msg'], $orderViewArr['data']);
            exit;
        } else {
            echo $this->_return(false, $orderViewArr['msg']);
            exit;
        }
    }

    /**
     * 获取all_api_order中 出票中得订单
     *
     * @param  int  $page  页码
     * @param  int  $pageNum  显示条数目
     * @param  string  $beginTime  开始时间
     * @param  string  $endTime  结束时间
     *
     * @return array
     */
    public function getTicketingOrderList()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $page      = I('post.page', 1);
        $pageNum   = I('post.pageNum', 10);
        $beginTime = I('post.beginTime', '');
        $endTime   = I('post.endTime', '');

        $otaOrderBiz = new \Business\Ota\Order();
        $list        = $otaOrderBiz->apiOrderTicketingList($loginInfoArr['sid'], $beginTime, $endTime, $page, $pageNum);

        if ($list['code'] == 200) {
            $this->apiReturn(200, $list['data'], 'success');
        }
        $this->apiReturn(204, [], $list['msg']);
    }

    /**
     * 获取出票中订单新版
     * <AUTHOR>
     * @date 2021/12/20
     *
     * @return array
     */
    public function getTicketingOrderListNew()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $orderNum   = I('post.order_num', "", 'trim');
        $searchType = I('post.search_type', "");
        $page       = I('post.page', 1);
        $beginTime  = I('post.begin_time', '');
        $endTime    = I('post.end_time', '');

        $otaOrderBiz = new \Business\Ota\Order();
        $list        = $otaOrderBiz->apiOrderTicketingListNew($loginInfoArr['sid'], $beginTime, $endTime, $page,
            $searchType, $orderNum);

        if ($list['code'] == 200) {
            $this->apiReturn(200, $list['data'], 'success');
        }
        $this->apiReturn(204, [], $list['msg']);
    }

    /**
     * 手动出票
     * <AUTHOR>
     * @date 2021/12/20
     *
     * @param  int  $ticket_type  1:出票并通知下游  2:仅更新pft订单状态
     * @param  int  $order_num  订单号
     * @param  string  $apiCode  凭证码
     * @param  string  $apiQrCode  凭证二维码
     *
     * @return array
     */
    public function manualTicket()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $ticketType       = I('post.ticket_type', "");
        $orderNum         = I('post.order_num', "");
        $apiCode          = I('post.apiCode', "", 'trim');
        $apiQrCode        = I('post.apiQrCode', "", 'trim');
        $noticeDownStream = I('post.notice_down_stream', "");

        $otaOrderBiz = new \Business\Ota\Order();
        $ticketRes   = $otaOrderBiz->manualTicket($loginInfoArr['sid'], $loginInfoArr['memberID'], $orderNum,
            $ticketType, $noticeDownStream, $apiCode, $apiQrCode);

        $this->apiReturn($ticketRes['code'], $ticketRes['data'], $ticketRes['msg']);
    }

    /**
     * 出票超时短信通知配置获取
     */
    public function timeoutSmsConfigGet()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }
        $otaOrderBiz      = new \Business\Ota\Order();
        $result = $otaOrderBiz->timeoutSmsConfigGet($loginInfoArr['sid']);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 出票超时短信通知配置
     */
    public function timeoutSmsConfig()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }
        $noticeType   = I('post.notice_type', "");
        $noticeMobile = I('post.notice_mobile', "");
        $status       = I('post.status', 0, "intval");
        $otaOrderBiz  = new \Business\Ota\Order();
        $result = $otaOrderBiz->timeoutSmsConfig($loginInfoArr['sid'], $noticeType, $noticeMobile, $status);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取手动出票记录表中的记录
     * <AUTHOR>
     * @date 2021/12/25
     *
     * @return array
     */
    public function getManualTicketList()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $orderNum  = I('post.order_num', "", 'trim');
        $opId      = I('post.op_id', "");
        $page      = I('post.page', 1);
        $beginTime = I('post.begin_time', '');
        $endTime   = I('post.end_time', '');

        $otaOrderBiz      = new \Business\Ota\Order();
        $manualTicketList = $otaOrderBiz->getManualTicketList($beginTime, $endTime, $loginInfoArr['sid'], $orderNum,
            $opId, $page);

        $this->apiReturn($manualTicketList['code'], $manualTicketList['data'], $manualTicketList['msg']);
    }


    /**
     * 刷新订单的出票状态
     * <AUTHOR>
     * @date 2021/12/25
     *
     * @return array
     */
    public function refreshOrderTicketStatus()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $orderNum         = I('post.order_num', "");
        $otaOrderBiz      = new \Business\Ota\Order();
        $manualTicketList = $otaOrderBiz->refreshOrderTicketStatus($orderNum);

        $this->apiReturn($manualTicketList['code'], $manualTicketList['data'], $manualTicketList['msg']);
    }


    /**
     * 用户自主发送淘宝码
     *
     * @param  string  $pftOrder  票付通订单号
     * @param  string  $code  消费码
     * @param  int  $orderType  1:票付通订单号 2:淘宝订单号
     *
     * @return string | json
     *
     */
    public function userSendTaoCode()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $orderNum  = I('request.order_num', '');
        $code      = I('request.code', '');
        $orderType = I('request.order_type', 1); // 1:票付通订单号 2:淘宝订单号

        // 判断参数
        if (!($orderNum && $code && $orderType)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $sid = $loginInfoArr['sid'];

        $orderTools = new \Model\Order\OrderTools();
        switch ($orderType) {
            case 1:
                $pftOrder = $orderNum;
                break;
            case 2:
                $pftOrder = $orderTools->getOrdernumByRemote($orderNum);
        }

        if (empty($pftOrder)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $orderInfoArr = $orderTools->getOrderInfo($pftOrder,
            'member, aid, lid, tid, ordermode, remotenum, tnum, status');

        if (empty($orderInfoArr)) {
            $this->apiReturn(204, [], '订单信息有误!');
        }

        if ($sid != $orderInfoArr['member'] && $sid != 1) {
            $this->apiReturn(204, [], '您不是订单直属者,无权限操作!');
        }

        // 不是淘宝订单号不进行处理
        if ($orderInfoArr['ordermode'] != 17) {
            $this->apiReturn(204, [], '非淘宝订单,不允许操作!');
        }

        if (empty($orderInfoArr['remotenum'])) {
            $this->apiReturn(204, [], '无远端订单号, 无需操作!');
        }

        // 如果订单不是未使用 或 已使用不让操作   --- 增加一个被取消的(客户要求)
        if (!in_array($orderInfoArr['status'], [0, 1, 3, 11])) {
            $this->apiReturn(204, [], '该订单状态, 无需操作!');
        }

        // 取消状态要取值原来的数量
        if ($orderInfoArr['status'] == 3) {
            $subOrderModel        = new \Model\Order\SubOrderQuery();
            $applyRes             = $subOrderModel->getInfoInApplyInfoByOrder($pftOrder, 'orderid, origin_num');
            $orderInfoArr['tnum'] = $applyRes['origin_num'];
        }

        // 验证是否有必要继续发码
        $notifyModel   = new \Model\Ota\Notify();
        $notifyInfoArr = $notifyModel->getAsyncDataByOrderType($pftOrder, 1, 'id, notice_num, status, notice_time');

        if (!empty($notifyInfoArr) && $notifyInfoArr['status'] == 2) {
            // 通知失败的订单 可能已经取消订单了
            // $this->apiReturn(204, [], '该订单为已发码失败订单,不再处理!!');
        }

        if (!empty($notifyInfoArr) && $notifyInfoArr['status'] == 1) {
            // $this->apiReturn(204, [], '该订单为已发码成功订单,不再处理!');
        }

        // 记录操作日志
        pft_log('order/taobaoUserOper',
            '用户发送淘宝码-操作人:' . $loginInfoArr['memberID'] . '|订单号:' . $pftOrder . '|码:' . $code);

        $systemConfigBiz = new \Business\Ota\Order();
        $noticeRes       = $systemConfigBiz->userAsyncTaobaoCode($orderInfoArr['remotenum'], $pftOrder, $code,
            $orderInfoArr['tnum'], $orderInfoArr['member'], $orderInfoArr['aid']);

        if ($noticeRes['code'] == 200) {
            $this->apiReturn(200, [], '发码成功!');
        }

        $this->apiReturn(205, [], '操作失败!' . $noticeRes['msg']);
    }

    /**
     * 淘宝订单手动核销操作
     *
     * @param  string  $pftOrder  票付通订单号
     * @param  int  $orderType  1:票付通订单号 2:淘宝订单号
     *
     * @return string | json
     *
     */
    public function userTaobaoConsume()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $orderNum  = I('request.order_num', '');
        $orderType = I('request.order_type', 1); // 1:票付通订单号 2:淘宝订单号
        $checkNum  = I('request.check_num', ''); // 核销数量

        // 判断参数
        if (!($orderNum && $orderType)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $sid = $loginInfoArr['sid'];

        $orderTools = new \Model\Order\OrderTools();
        switch ($orderType) {
            case 1:
                $pftOrder = $orderNum;
                break;
            case 2:
                $pftOrder = $orderTools->getOrdernumByRemote($orderNum);
        }

        if (empty($pftOrder)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $orderInfoArr = $orderTools->getOrderInfo($pftOrder,
            'member, aid, lid, tid, ordermode, remotenum, tnum, status');

        if (empty($orderInfoArr)) {
            $this->apiReturn(204, [], '订单信息有误!');
        }

        if ($sid != $orderInfoArr['member'] && $sid != 1) {
            $this->apiReturn(204, [], '您不是订单直属者,无权限操作!');
        }

        // 不是淘宝订单号不进行处理
        if ($orderInfoArr['ordermode'] != 17) {
            $this->apiReturn(204, [], '非淘宝订单,不允许操作!');
        }

        if (empty($orderInfoArr['remotenum'])) {
            $this->apiReturn(204, [], '无远端订单号, 无需操作!');
        }

        // 如果订单不是已使用不让操作  --- 应客户要求,增加取消的,用户自己手动补单
        if (!in_array($orderInfoArr['status'], [1, 3, 7])) {
            $this->apiReturn(204, [], '非已使用订单, 不允许操作!');
        }

        if (!empty($checkNum) && $checkNum < 0) {
            $this->apiReturn(204, [], '核销数量有误!');
        }

        $logMsgArr = [
            'operateId' => $loginInfoArr['memberID'],
            'pftOrder'  => $pftOrder,
            'checkNum'  => $checkNum,
        ];

        // 记录操作日志
        pft_log('order/taobaoUserOper', '用户手动核销淘宝订单:' . json_encode($logMsgArr, JSON_UNESCAPED_UNICODE));

        $verifyArr = $this->_taobaoConsume($pftOrder, $checkNum);

        if ($verifyArr['code'] == 200) {
            $this->apiReturn(200, [], $verifyArr['msg']);
        }

        // 记录失败日志
        pft_log('order/taobaoUserOper',
            '用户手动核销淘宝失败:' . $pftOrder . '||' . json_encode($verifyArr, JSON_UNESCAPED_UNICODE));

        $msg = '操作失败';
        if (isset($verifyArr['msg']) && $verifyArr['msg']) {
            $msg = $verifyArr['msg'];
        }
        $this->apiReturn(205, [], $msg);
    }

    /**
     * 根据票付通订单号或远程订单号获取订单信息
     *
     * @param  string  $pftOrder  票付通订单号
     * @param  int  $orderType  1:票付通订单号 2:淘宝订单号
     *
     * @return string | json
     *
     */
    public function searchRemoteOrder()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $orderNum  = I('request.order_num', '');
        $orderType = I('request.order_type', 1); // 1:票付通订单号 2:淘宝订单号

        // 判断参数
        if (!($orderNum && $orderType)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $sid = $loginInfoArr['sid'];

        $orderTools = new \Model\Order\OrderTools();
        switch ($orderType) {
            case 1:
                $pftOrder = $orderNum;
                break;
            case 2:
                $pftOrder = $orderTools->getOrdernumByRemote($orderNum);
        }

        $taobaoModel   = new \Model\Taobao\Taobao();
        $taobaoInfoArr = $taobaoModel->getTaobaoErrorInfoByOrderId($orderNum, $orderType);
        if (empty($pftOrder)) {
            if (!empty($taobaoInfoArr) && !empty($taobaoInfoArr['error'])){
                $this->apiReturn(204, [], $taobaoInfoArr['error']);
            }
            $this->apiReturn(203, [], '订单不存在!');
        }

        $orderInfoArr = $orderTools->getOrderInfo($pftOrder,
            'member, ordernum, playtime, ordertime, ordername, ordertel, ordermode, remotenum, tnum, status');

        // 不是淘宝订单号不进行处理
        if ($orderInfoArr['ordermode'] != 17) {
            $this->apiReturn(204, [], '非淘宝订单,不允许操作!');
        }

        if (empty($orderInfoArr)) {
            $this->apiReturn(204, [], '无该订单数据!');
        }

        if ($sid != $orderInfoArr['member'] && $sid != 1) {
            $this->apiReturn(204, [], '您不是订单直属者,无权限操作!');
        }

        if (!empty($taobaoInfoArr) && !empty($taobaoInfoArr['error'])){
            $orderInfoArr = array_merge($orderInfoArr, $taobaoInfoArr);
        }

        $this->apiReturn(200, $orderInfoArr, 'success');
    }

    /**
     * @param $pftOrder          票付通订单号
     * @param  string  $verifyNum  通知验证数量
     *
     * @return array
     *
     */
    private function _taobaoConsume($pftOrder, $verifyNum = '')
    {
        include '/var/www/html/taobao/taobao/TopSdk.php';

        $topClient            = new \TopClient();
        $topClient->appkey    = '21622370';
        $topClient->secretKey = '0aa565f5e9cca3119a588260fc857e66';
        $sessionKey           = '6100e07b6105eef112fc7b8404e5cea24a30cafcd0b858a1098166678';
        $merchantId           = '1098166678';

        $taobaoModel = new \Model\Taobao\Taobao();
        $taoLogArr   = $taobaoModel->pftTaobaoLog($pftOrder, 2);

        if (empty($taoLogArr)) {
            return ['code' => 203, 'msg' => '淘宝订单信息不存在!'];
        }

        $remoteNum  = $taoLogArr['order_id'];
        $verifyCode = $taoLogArr['ecode'];
        $token      = $taoLogArr['token'];
        // 核销数量处理
        if ($verifyNum == '') {
            $verifyNum = $taoLogArr['num'];
        }

        // 验证核销数量
        if ($verifyNum > $taoLogArr['num'] || $verifyNum < 1) {
            return ['code' => 203, 'msg' => '核销数量有误!'];
        }

        if (ENV != 'PRODUCTION') {
            return ['code' => 200, 'msg' => '测试环境默认核销成功'];
        }

        $req_before = new \VmarketEticketBeforeconsumeRequest;

        $req_before->setOrderId($remoteNum);
        $req_before->setVerifyCode($verifyCode);
        $req_before->setToken($token);
        $req_before->setCodemerchantId($merchantId);
        $req_before->setPosid("01");
        $req_before->setMobile("");

        $resp = $topClient->execute($req_before, $sessionKey);
        $ar   = (array)$resp;

        if (isset($ar['ret_code']) && $ar['ret_code'] == 1) {
            $req = new \VmarketEticketConsumeRequest;
            $req->setOrderId($remoteNum);
            $req->setVerifyCode($verifyCode);
            $req->setConsumeNum($verifyNum);
            $req->setToken($token);
            $req->setCodemerchantId($merchantId);
            $req->setPosid("01");
            $req->setMobile("");
            $req->setNewCode("");
            $req->setSerialNum($pftOrder);
            $resp = $topClient->execute($req, $sessionKey);

            $ar       = (array)$resp;
            $str_back = json_encode($ar, JSON_UNESCAPED_UNICODE);
            if (isset($ar['ret_code']) && $ar['ret_code'] == 1) {
                pft_log('api/taobao/debug',
                    "eticketConsume:{$_SERVER['REMOTE_ADDR']}:订单号:$pftOrder|核销成功|淘宝返回数据:" . $str_back, 'month');
                $taobaoModel->savePftTaobaologInfo($pftOrder, '', '', '', 4);

                return ['code' => 200, 'msg' => '核销成功', 'data' => $str_back];
            } else {
                $ar['PFTorderID'] = $pftOrder;
                pft_log('api/taobao/debug',
                    "eticketConsume:{$_SERVER['REMOTE_ADDR']}:订单号:$pftOrder|淘宝核销失败|淘宝返回数据:" . $str_back, 'month');
                $taobaoModel->savePftTaobaologInfo($pftOrder, '', '', '', 5);
                $taobaoModel->addTaobaoDetectLog($remoteNum, $verifyCode, $token, $pftOrder, $verifyNum, 'consume');

                return ['code' => 400, 'msg' => '淘宝核销失败', 'data' => $str_back];
            }
        } else {
            $errMsg = isset($ar['sub_msg']) ? $ar['sub_msg'] : $ar['msg'];
            $errMsg = $errMsg ?: '淘宝核销接口异常，请重试';

            pft_log('api/taobao/debug',
            "eticketConsume:{$_SERVER['REMOTE_ADDR']}:订单号:$pftOrder|淘宝核销失败|淘宝返回数据:" . json_encode([$ar]), 'month');

            return ['code' => 204, 'msg' => $errMsg, 'data' => $ar];
        }
    }

    /**
     * 获取供应商审核退款列表
     *
     */
    public function getCtripRefundMoneyList()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $pftOrder    = I('post.ordernum', '', 'string');
        $remoteOrder = I('post.remote_order', '', 'string');
        $page        = I('post.page', 1, 'intval');
        $pageSize    = I('post.page_size', 10, 'intval');

        $otherSysModel = new \Model\Ota\OtherSysConfig();
        $refundListArr = $otherSysModel->getCtripRefundLogByAid($loginInfoArr['sid'], $pftOrder, $remoteOrder, 2, $page,
            $pageSize, 1, 'pftOrder, otaOrder, itemId, sequenceId, status, time, amount');

        if (empty($refundListArr)) {
            $this->apiReturn(204, [], '无该订单数据!');
        }

        $total = $otherSysModel->countCtripRefundLogByAid($loginInfoArr['sid'], $pftOrder, $remoteOrder, 2, 1);

        $this->apiReturn(200, ['list' => $refundListArr, 'total' => $total], 'success');
    }

    /**
     * 审核仅携程退款
     *
     */
    public function handleRefundLog()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $sequenceId = I('post.sequence_id', '', 'string');
        $refundtype = I('post.refund_type', 2, 'string');   // 1同意 2拒绝

        if (empty($sequenceId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        if (!in_array($refundtype, [1, 2])) {
            $this->apiReturn(203, [], '参数错误');
        }

        $otherSysModel = new \Model\Ota\OtherSysConfig();
        $logArr        = $otherSysModel->getCtripRefundLogBySequenceId($sequenceId,
            'pftOrder, otaOrder, itemId, sequenceId, status, aid');

        if (empty($logArr)) {
            $this->apiReturn(203, [], '无该需要审核记录');
        }

        if ($logArr['aid'] != $loginInfoArr['sid']) {
            $this->apiReturn(203, [], '无权限操作');
        }

        $sourceArr = [
            // 获取参数
            'pftOrder'    => $logArr['pftOrder'],
            'remoteOrder' => $logArr['otaOrder'],
            'sequenceId'  => $sequenceId,
            'Refundtype'  => $refundtype,   // 1.同意退款 2.拒绝退款
            'operatorId'  => $this->_memberId, // 操作人 id
        ];

        // 成功后入队列通知携程玩乐处理该退款
        $jobId = \Library\Resque\Queue::push('cooperation_system', 'GroupOrder_Job',
            [
                'groupKey' => 5,
                'method'   => 'refundOrderConfirmQueue',
                'source'   => $sourceArr,
            ]
        );

        // 记录携程玩乐审核操作
        $logDataArr = json_encode([
            'key'     => '携程玩乐退款审核通知',
            'request' => $sourceArr,
            'jobid'   => $jobId,
            'operid'  => $loginInfoArr['memberID'],
        ], JSON_UNESCAPED_UNICODE);
        pft_log('other_systerm_request', $logDataArr, 3);

        $this->apiReturn(200, [], '正在通知处理中,请稍后查看');
    }

    /**
     * 获取订单消费码列表
     * /r/Ota_ThirdOrder/getApiCodeList
     * 
     */
    public function getApiCodeList()
    {
        $loginInfoArr = $this->getLoginInfo();
        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '参数错误');
        } 

        $orderNum = I('request.ordernum', '', 'string');
        $commonOrderBiz = new CommonOrder();
        $dataInfoArr = $commonOrderBiz->queryOrder($orderNum);
        $this->apiReturn(200, $dataInfoArr['data'], 'success');
    }
}
