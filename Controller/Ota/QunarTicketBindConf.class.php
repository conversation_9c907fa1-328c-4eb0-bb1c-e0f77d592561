<?php

namespace Controller\Ota;

use Library\Controller;

class QunarTicketBindConf extends Controller
{
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 获取去哪儿门票绑定信息配置
     *
     * @param  int  $tid  门票id
     *
     */
    public function getQunarTicketBindConf()
    {
        $sid = $this->_sid;
        $tid = I('post.tid', 0, 'intval');

        if (empty($tid)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model = new \Model\Ota\ApiQunarTicketBindConfModel();
        $res   = $model->getApiQunarTicketBindConf($sid, $tid);
        if (!empty($res)) {
            $returnData = [
                'sale_type' => $res['sale_type'],
            ];
        } else {
            $returnData = [
                'sale_type' => 1,
            ];
        }

        $this->apiReturn(200, $returnData, 'success');
    }

    /**
     * 设置去哪儿门票绑定信息
     *
     * @param  int  $tid  门票id
     * @param  int  $saletype  1：B2C 2：B2C 3：ALL
     *
     */
    public function setQunarTicketBindConf()
    {
        $tid      = I('post.tid', 0, 'intval');
        $saleType = I('post.sale_type', 1, 'intval');
        $memberId = $this->_sid;
        $opid     = $this->_memberId;

        if ($tid < 0 || !in_array($saleType, [1, 2, 3])) {
            $this->apiReturn(203, [], '参数有误');
        }

        $model   = new \Model\Ota\ApiQunarTicketBindConfModel();
        $confRes = $model->getApiQunarTicketBindConf($memberId, $tid);

        if (empty($confRes)) {
            $res = $model->addQunarTicketBindConf($memberId, $tid, $saleType, $opid);
        } else {
            if ($confRes['sale_type'] == $saleType) {
                $res = true;
            } else {
                $res = $model->updateApiQunarBindConf($memberId, $tid, $saleType, $opid);
            }
        }

        // 返回操作结果
        if ($res !== false) {
            $this->apiReturn(200, [], '操作成功');
        } else {
            $this->apiReturn(205, [], '操作失败');
        }
    }

    /**
     * 获取去哪儿门票绑定信息配置
     *
     * @param  int  $tid  门票id
     *
     */
    public function getQunarVoucherTypeConf()
    {
        $sid = $this->_sid;
        $tid = I('post.tid', 0, 'intval');

        if (empty($tid)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model = new \Model\Ota\ApiQunarTicketBindConfModel();
        $res   = $model->getApiQunarTicketBindConf($sid, $tid);
        if (!empty($res)) {
            $returnData = [
                'voucher_type' => $res['voucher_type'],
            ];
        } else {
            $returnData = [
                'voucher_type' => 2,
            ];
        }

        $this->apiReturn(200, $returnData, 'success');
    }

    /**
     * 设置去哪儿门票绑定信息
     *
     * @param  int  $tid  门票id
     * @param  int  $saletype  1：B2C 2：B2C 3：ALL
     *
     */
    public function setQunarVoucherTypeConf()
    {
        $tid         = I('post.tid', 0, 'intval');
        $voucherType = I('post.voucher_type', 1, 'intval');
        $memberId    = $this->_sid;
        $opid        = $this->_memberId;

        if ($tid < 0 || !in_array($voucherType, [1, 2])) {
            $this->apiReturn(203, [], '参数有误');
        }

        $model   = new \Model\Ota\ApiQunarTicketBindConfModel();
        $confRes = $model->getApiQunarTicketBindConf($memberId, $tid);

        if (empty($confRes)) {
            $res = $model->addBindConf($memberId, $tid, $voucherType, $opid);
        } else {
            $res = $model->updateBindConf($memberId, $tid, $voucherType, $opid);
        }

        // 返回操作结果
        if ($res !== false) {
            $this->apiReturn(200, [], '操作成功');
        } else {
            $this->apiReturn(205, [], '操作失败');
        }
    }
}