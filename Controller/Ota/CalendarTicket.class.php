<?php

namespace Controller\Ota;

use Business\Ota\DouYinCalendarTicket;
use Library\Controller;
use Library\JsonRpc\PftRpcClient;
use Model\Member\Member;
use Model\Member\MemberRelationship;
use Throwable;

class CalendarTicket extends Controller
{
    private $loginInfo = [];

    public function __construct()
    {
        $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 已绑定抖音门店POI列表
     *
     * <AUTHOR>
     * @date   2023-3-29
     */
    public function getDouYinMerchantPoiList()
    {
        $fid             = $this->loginInfo['sid'];               //用户id
        $douyinAccountId = I('douyin_account_id', 0, 'intval');
        $douyinPoiId     = I('douyin_poi_id', 0, 'intval');
        $douyinPoiName   = I('douyin_poi_name', '', 'string');
        $page            = I('page', 1, 'intval');
        $limit           = I('limit', 10, 'intval');

        $method  = 'calendarTicket/DouYinMerchantPoi/getList';
        $rpcData = [
            'fid'               => $fid,
            'douyin_account_id' => $douyinAccountId,
            'douyin_poi_id'     => $douyinPoiId,
            'douyin_poi_name'   => $douyinPoiName,
            'page'              => $page,
            'limit'             => $limit,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        $this->apiReturn(200, $rpcRes['data'], 'success');
    }

    /**
     * 添加抖音门店POI
     *
     * <AUTHOR>
     * @date   2023-3-29
     */
    public function addDouYinMerchantPoi()
    {
        $fid        = $this->loginInfo['sid'];               //用户id
        $operatorId = $this->loginInfo['memberID'];   //操作人id

        $method  = 'calendarTicket/DouYinMerchantPoi/add';
        $rpcData = I('post.list');
        // 拼接fid
        foreach ($rpcData as &$data) {
            $data['fid']         = $fid;
            $data['operator_id'] = $operatorId;
        }

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, [], '操作成功');
        }

        $this->apiReturn(500, [], $rpcRes['msg']);
    }

    /**
     * 抖音短信通知配置获取
     *
     * <AUTHOR>
     * @date   2023-3-30
     */
    public function getDouYinSmsNoticeConfig()
    {
        $fid = $this->loginInfo['sid'];               //用户id

        $rpcRes = (new DouYinCalendarTicket())->getDouYinSmsNoticeConfig($fid);

        $this->apiReturn(200, $rpcRes['data'], 'success');
    }

    /**
     * 保存抖音短信通知配置
     *
     * <AUTHOR>
     * @date   2023-3-30
     */
    public function saveDouYinSmsNoticeConfig()
    {
        $fid        = $this->loginInfo['sid'];        //用户id
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $noticeType = I('post.notice_type', '', 'string');
        $status     = I('post.status', '', 'string');
        $toPhone    = I('post.to_phone', null, 'intval');

        $method  = 'calendarTicket/DouYinSmsNoticeConfig/saveNoticeConfig';
        $rpcData = [
            'fid'         => $fid,
            'status'      => $status,
            'to_phone'    => $toPhone,
            'notice_type' => $noticeType,
            'operator_id' => $operatorId,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);

        $this->apiReturn(200, $rpcRes['data'], 'success');
    }

    /**
     * 适用人群列表
     *
     * <AUTHOR>
     * @date   2023-3-29
     */
    public function getTargetUsersList()
    {
        $fid             = $this->loginInfo['sid'];               //商户id
        $douyinAccountId = I('douyin_account_id', 0, 'intval');
        $douyinPoiId     = I('douyin_poi_id', 0, 'intval');
        $douyinPoiName   = I('douyin_poi_name', '', 'string');
        $userType        = I('user_type', 0, 'intval');
        $page            = I('page', 1, 'intval');
        $limit           = I('limit', 10, 'intval');
        $name            = I('name', '', 'string');
        $id              = I('id', 0, 'intval');

        $method  = 'calendarTicket/DouYinMerchantPoi/getTargetUsersList';
        $rpcData = [
            'fid'               => $fid,
            'douyin_account_id' => $douyinAccountId,
            'douyin_poi_id'     => $douyinPoiId,
            'douyin_poi_name'   => $douyinPoiName,
            'user_type'         => $userType,
            'page'              => $page,
            'limit'             => $limit,
            'id'                => $id,
            'name'              => $name,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], '操作成功');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 新增/跟新适用人群
     *
     * <AUTHOR>
     * @date   2023-3-30
     */
    public function saveTargetUsers()
    {
        $fid        = $this->loginInfo['sid'];    //用户id
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        // 数据处理
        $list     = I('post.list');
        $isCreate = I('post.is_create', 0, 'intval');

        // 拼接fid
        foreach ($list as &$data) {
            $data['fid']         = $fid;
            $data['operator_id'] = $operatorId;
        }

        $rpcData = [
            'list'      => $list,
            'is_create' => $isCreate,
        ];

        $method = 'calendarTicket/DouYinMerchantPoi/saveTargetUsers';

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], '操作成功');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 删除人群信息
     * /r/Ota_CalendarTicket/removeTargetUsers
     * 
     */
    public function removeTargetUsers()
    {
        $fid        = $this->loginInfo['sid'];    //用户id
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $id         = I('post.id', 0, 'intval');

        if (empty($id)) {
            $this->apiReturn(500, [], '缺少参数');
        }

        $rpcData = [
            'id'          => $id,
            'fid'         => $fid,
            'operator_id' => $operatorId,
        ];
        $method = 'calendarTicket/DouYinMerchantPoi/removeTargetUsers';
        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], '操作成功');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 更新绑定标识
     *
     * <AUTHOR>
     * @date   2023-4-11
     */
    public function updateTag()
    {
        // 数据处理
        $rpcData = [
            'tid' => I('post.tid'),
        ];

        $method = 'calendarTicket/DouYinTicket/updateTag';

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, [], 'success');
        }

        $this->apiReturn(500, $rpcRes, 'failed');
    }

    /**
     * 查询景区列表
     *
     * <AUTHOR>
     * @date   2023-3-31
     */
    public function queryDistributionLand()
    {
        $fid           = 113;               //商户id
        $sid           = $this->loginInfo['sid'];               //登录账户id
        $pageNum       = I('pageNum', 1, 'intval');
        $pageSize      = I('pageSize', 10, 'intval');
        $landTitle     = I('landTitle', '', 'string');
        $productType   = I('productType', '', 'string');
        $sectionStatus = I('sectionStatus', false);

        $method  = 'calendarTicket/DouYinTicket/queryDistributionLand';
        $rpcData = [
            'fid'         => $fid,
            'sid'         => $sid,
            'pageNum'     => $pageNum,
            'pageSize'    => $pageSize,
            'landTitle'   => $landTitle,
            'productType' => $productType,
        ];

        if ($sectionStatus !== false) {
            $rpcData['sectionStatus'] = $sectionStatus;
        }

        $rpcRes = $this->callRpc($method, $rpcData);

        $this->apiReturn(200, $rpcRes['data'], 'success');
    }

    /**
     * 查询景区门票列表
     *
     * <AUTHOR>
     * @date   2023-3-31
     */
    public function queryDistributionTicket()
    {
        $fid           = 113;               //商户id
        $sid           = $this->loginInfo['sid'];       //商户id
        $lid           = I('lid', 0, 'intval');
        $pageNum       = I('pageNum', 1, 'intval');
        $pageSize      = I('pageSize', 10, 'intval');
        $productType   = I('productType', '', 'string');
        $sectionStatus = I('sectionStatus', false);

        $method  = 'calendarTicket/DouYinTicket/queryDistributionTicket';
        $rpcData = [
            'fid'         => $fid,
            'sid'         => $sid,
            'lid'         => $lid,
            'pageNum'     => $pageNum,
            'pageSize'    => $pageSize,
            'productType' => $productType,
        ];

        if ($sectionStatus !== false) {
            $rpcData['sectionStatus'] = $sectionStatus;
        }

        $rpcRes = $this->callRpc($method, $rpcData);

        $this->apiReturn(200, $rpcRes['data'], 'success');
    }

    /**
     * 手动修改已对接票状态
     *
     * <AUTHOR>
     * @date   2023-3-31
     */
    public function changeTicketStatus()
    {
        $id     = I('id', 0, 'intval');
        $status = I('status', 0, 'intval');

        $method  = 'calendarTicket/DouYinTicket/changeTicketStatus';
        $rpcData = [
            'id'     => $id,
            'status' => $status,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        $this->apiReturn(200, $rpcRes, 'success');
    }

    /**
     * 抖音来客列表
     *
     * <AUTHOR>
     * @date   2023-3-31
     */
    public function queryDouyinLikePage()
    {
        $fid           = 113; // 抖音来客账号id
        $sid           = $this->loginInfo['sid']; // 登录者账号id 本系统的fid
        $evoluteStatus = I('evoluteStatus');
        $productType   = I('productType');
        $pageNum       = I('pageNum', 1, 'intval');
        $pageSize      = I('pageSize', 10, 'intval');
        $tid           = I('tid');
        $preSale       = I('preSale');
        $lid           = I('lid');

        $method  = 'calendarTicket/DouYinTicket/queryDouyinLikePage';
        $rpcData = [
            'fid'           => $fid,
            'evoluteStatus' => $evoluteStatus,
            'sid'           => $sid,
            'productType'   => $productType,
            'pageNum'       => $pageNum,
            'pageSize'      => $pageSize,
            'tid'           => $tid,
            'lid'           => $lid,
            'preSale'       => $preSale,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        $this->apiReturn(200, $rpcRes['data'], 'success');
    }

    /**
     * 校验门票是否已录入
     *
     * <AUTHOR>
     * @date   2023-3-31
     */
    public function isDetail()
    {
        $fid        = $this->loginInfo['sid'];               //商户id
        $ticketId   = I('ticket_id', 0, 'intval');
        $supplierId = I('supplier_id', 0, 'intval');

        $method  = 'calendarTicket/DouYinTicket/isDetail';
        $rpcData = [
            'fid'         => $fid,
            'ticket_id'   => $ticketId,
            'supplier_id' => $supplierId,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 获取门票信息
     *
     * <AUTHOR>
     * @date   2023-3-31
     */
    public function getTicketDetail()
    {
        $fid = $this->loginInfo['sid'];               //商户id
        $tid = I('tid');               //商户id

        $method  = 'calendarTicket/DouYinTicket/getTicketInfo';
        $rpcData = [
            'tid' => $tid,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        $this->apiReturn(200, $rpcRes['data'], 'success');
    }

    /**
     * 获取抖音商品类目列表
     *
     * <AUTHOR>
     * @date   2023-4-28
     */
    public function getDouYinGoodsCats()
    {
        $rpcRes = [
            ['cat_id' => 18004001, 'name' => '单景点门票'],
            ['cat_id' => 18004003, 'name' => '园内交通'],
            // ['cat_id' => 18004004, 'name' => '园内游玩项目'],
            ['cat_id' => 18004008, 'name' => '园内套票'],
            ['cat_id' => 18004009, 'name' => '园内套票（不含门票）'],
            ['cat_id' => 18004002, 'name' => '园内餐饮'],
            ['cat_id' => 18004005, 'name' => '园内讲解'],
            ['cat_id' => 18003001, 'name' => '水上体验'],
            ['cat_id' => 18003002, 'name' => '城市/高空观光'],
            ['cat_id' => 18003003, 'name' => '其他游玩项目'],
            ['cat_id' => 18003004, 'name' => '温泉'],
            ['cat_id' => 18003006, 'name' => '观光巴士'],
            ['cat_id' => 4015004, 'name' => '滑雪场'],
            ['cat_id' => 18003007, 'name' => '国内短途游轮'],
            ['cat_id' => 18003008, 'name' => '国内长途游轮'],
            ['cat_id' => 18003009, 'name' => '演出'],
            ['cat_id' => 18009001, 'name' => '营位票'],
            ['cat_id' => 18009002, 'name' => '露营套餐'],
            ['cat_id' => 18009003, 'name' => '营地服务'],
            ['cat_id' => 18009004, 'name' => '营地活动'],
        ];

        $this->apiReturn(200, $rpcRes, 'success');
    }

    /**
     * 获取分时信息
     *
     * <AUTHOR>
     * @date   2023-3-31
     */
    public function getDouYinTicketSectionTime()
    {
        $fid = $this->loginInfo['sid'];               //商户id
        $tid = I('tid', 0, 'intval');               //商户id

        $method  = 'calendarTicket/DouYinTicket/getTicketSectionTime';
        $rpcData = [
            'tid' => $tid,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data']['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 票类是否开启分时预约
     *
     * <AUTHOR>
     * @date   2023-3-31
     */
    public function querySectionEnableOfTicket()
    {
        $fid = $this->loginInfo['sid'];               //商户id
        $tid = I('tid');               //商户id

        $method  = 'calendarTicket/DouYinTicket/querySectionEnableOfTicket';
        $rpcData = [
            'tid' => $tid,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 景区（产品）是否开启分时预约
     *
     * <AUTHOR>
     * @date   2023-3-31
     */
    public function queryStorageSection()
    {
        $tid = I('lid', 0, 'intval');               //商户id

        $method  = 'calendarTicket/DouYinTicket/queryStorageSection';
        $rpcData = [
            'lid' => $tid,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 创建/保存日历票
     *
     * <AUTHOR>
     * @date   2023-4-5
     */
    public function douYinTicketCreate()
    {
        $fid         = $this->loginInfo['sid'];        //商户id
        $detail      = I('detail');
        $productData = I('product_data');
        $skuData     = I('sku_data');
        $douyin      = I('douyin');
        $operatorId  = $this->loginInfo['memberID'];   //操作人id

        $detail['fid']         = $fid;
        $detail['operator_id'] = $operatorId;

        $method  = 'calendarTicket/DouYinTicket/createTicket';
        $rpcData = [
            'detail'       => $detail,
            'product_data' => $productData,
            'sku_data'     => $skuData,
            'douyin'       => $douyin,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 获取价格库存列表
     *
     * <AUTHOR>
     * @date   2023-4-5
     */
    public function syncDouYinTicketCalendarList()
    {
        $fid        = $this->loginInfo['sid'];               //商户id
        $detailId   = I('douyin_ticket_detail_id');
        $page       = I('page', 1);
        $limit      = I('limit', 10);
        $dateStart  = I('date_start', '', 'string');
        $dateEnd    = I('date_end', '', 'string');
        $saleStatus = I('sale_status');
        $status     = I('status');

        //$method  = 'calendarTicket/DouYinTicket/getPriceStorageList';
        //$rpcData = [
        //    'douyin_ticket_detail_id' => $detailId,
        //    'page'                    => $page,
        //    'limit'                   => $limit,
        //];
        //
        //$rpcRes = $this->callRpc($method, $rpcData);

        $rpcRes = (new \Business\Ota\DouYinCalendarTicket())->getDouYinTicketCalendarList($detailId, $page, $limit,
            $dateStart, $dateEnd, $saleStatus, $status);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, empty($rpcRes['data']) ? $rpcRes : $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }


    /**
     * 获取演出价格库存列表
     *
     * <AUTHOR>
     * @date   2023-4-5
     */
    public function syncDouYinTicketCalendarShowList()
    {
        $fid        = $this->loginInfo['sid'];               //商户id
        $detailId   = I('douyin_ticket_detail_id');
        $page       = I('page', 1, 'intval');
        $limit      = I('limit', 10, 'intval');
        $saleStatus = I('sale_status', 0, 'intval');
        $status = I('status', 0, 'intval');

        $method  = 'calendarTicket/DouYinTicket/getShowPriceStorageList';
        $rpcData = [
            'supplierId'           => $fid,
            'douyinTicketDetailId' => $detailId,
            'saleStatus'           => $saleStatus,
            'status'           => $status,
            'page'                 => $page,
            'limit'                => $limit,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, empty($rpcRes['data']) ? $rpcRes : $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }


    /**
     * 分时库存详情
     *
     * <AUTHOR>
     * @date   2023-5-19
     */
    public function sectionStorageList()
    {
        $fid = $this->loginInfo['sid'];
        $id  = I('id');

        $rpcRes = (new \Business\Ota\DouYinCalendarTicket())->sectionStorageList($id);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, empty($rpcRes['data']) ? $rpcRes : $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 获取价格库存列表（通过ids）
     *
     * <AUTHOR>
     * @date   2023-4-13
     */
    public function syncDouYinTicketCalendarListByIds()
    {
        $detailId = I('douyin_ticket_detail_id');
        $ids      = I('ids');

        $rpcRes = (new \Business\Ota\DouYinCalendarTicket())->getDouYinTicketCalendarListByIds($ids, $detailId);

        $this->apiReturn(200, $rpcRes['data'], 'success');
    }

    /**
     * 编辑日历票
     *
     * <AUTHOR>
     * @date   2023-4-5
     */
    public function douYinTicketEdit()
    {
        $id         = I('id');
        $detail     = I('detail');
        $douyin     = I('douyin');
        $operatorId = $this->loginInfo['memberID'];   //操作人id
        $fid        = $this->loginInfo['sid']; // 登录者账号id 本系统的fid

        $detail['operator_id'] = $operatorId;
        $detail['fid']         = $fid;

        $method  = 'calendarTicket/DouYinTicket/editTicket';
        $rpcData = [
            'detail' => $detail,
            'douyin' => $douyin,
            'id'     => $id,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 重新对接已解绑门票
     *
     * <AUTHOR>
     * @date   2023-4-5
     */
    public function editUnbindTicket()
    {
        $fid        = $this->loginInfo['sid'];        //商户id
        $id         = I('id');
        $detail     = I('detail');
        $douyin     = I('douyin');
        $operatorId = $this->loginInfo['memberID'];   //操作人id

        $detail['fid']         = $fid;
        $detail['operator_id'] = $operatorId;

        $method  = 'calendarTicket/DouYinTicket/editUnbindTicket';
        $rpcData = [
            'detail' => $detail,
            'douyin' => $douyin,
            'id'     => $id,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 提交需要同步价格库存
     *
     * <AUTHOR>
     * @date   2023-4-5
     */
    public function syncDouYinPriceStorage()
    {
        $ids                  = I('ids');
        $operatorId           = $this->loginInfo['memberID'];   //操作人id
        $douyinTicketDetailId = I('douyin_ticket_detail_id');
        $opType               = I('op_type');

        $rpcRes = (new DouYinCalendarTicket())->syncPriceStorage($ids, $operatorId, $douyinTicketDetailId, $opType);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }


    /**
     * 同步抖音演出价格库存
     */
    public function syncDouyinShowPriceStorage()
    {
        $ids                  = I('ids');
        $operatorId           = $this->loginInfo['memberID'];
        $douyinTicketDetailId = I('douyin_ticket_detail_id');
        $fid     = $this->loginInfo['sid'];
        $method  = 'calendarTicket/DouYinTicket/syncDouyinShowPriceStorage';
        $rpcData = [
            'supplierId'                  => $fid,
            'ids'                  => $ids,
            'operatorId'           => $operatorId,
            'douyinTicketDetailId' => $douyinTicketDetailId
        ];
        $rpcRes = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }
        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 下架抖音商品
     *
     * <AUTHOR>
     * @date   2023-4-5
     */
    public function douYinTicketRemove()
    {
        $id         = I('id');
        $operatorId = $this->loginInfo['memberID'];   //操作人id

        $rpcRes = (new DouYinCalendarTicket())->douYinTicketDown($id, $operatorId);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 解绑已下架商品
     *
     * <AUTHOR>
     * @date   2023-4-19
     */
    public function douYinTicketUnbind()
    {
        $id         = I('id');
        $operatorId = $this->loginInfo['memberID'];   //操作人id

        $rpcRes = (new DouYinCalendarTicket())->douYinTicketUnbind($id, $operatorId);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 门票信息价格库存信息获取（通过sku_out_id）
     *
     * @return void
     */
    public function getTicketInfoBySkuOutId()
    {
        $id = I('id');

        $rpcRes = (new DouYinCalendarTicket())->getTicketInfoBySkuOutId($id);

        $this->apiReturn(200, $rpcRes, 'success');
    }

    /**
     * 已对接商品列表
     *
     * <AUTHOR>
     * @date   2023-4-5
     */
    public function getDouYinTicketList()
    {
        $fid              = $this->loginInfo['sid'];               //商户id
        $page             = I('page', 1, 'intval');
        $limit            = I('limit', 10, 'intval');
        $productId        = I('product_id', 0, 'intval');
        $ticketId         = I('ticket_id', 0, 'intval');
        $douyinPoiId      = I('douyin_poi_id', 0, 'intval');
        $skuSync          = I('sku_sync', 0, 'intval');
        $douyinPoiName    = I('douyin_poi_name', '', 'string');
        $ticketType       = I('ticket_type', 0, 'intval');
        $status           = I('status', 0, 'intval');
        $productCode      = I('product_code', '', 'string');
        $supplierId       = I('supplier_id', 0, 'intval');
        $douyinTicketName = I('douyin_ticket_name', '', 'string');
        $douyinGoodsName  = I('douyin_goods_name', '', 'string');

        $method  = 'calendarTicket/DouYinTicket/getTicketList';
        $rpcData = [
            'fid'                => $fid,
            'page'               => $page,
            'limit'              => $limit,
            'productId'          => $productId,
            'ticketId'           => $ticketId,
            'douyinPoiId'        => $douyinPoiId,
            'skuSync'            => $skuSync,
            'douyinPoiName'      => $douyinPoiName,
            'ticketType'         => $ticketType,
            'status'             => $status,
            'productCode'        => $productCode,
            'supplierId'         => $supplierId,
            'douyin_ticket_name' => $douyinTicketName,
            'douyin_goods_name'  => $douyinGoodsName,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 已对接商品列表
     *
     * <AUTHOR>
     * @date   2023-4-5
     */
    public function getUnbindDouYinTicketList()
    {
        $fid              = $this->loginInfo['sid'];               //商户id
        $page             = I('page', 1, 'intval');
        $limit            = I('limit', 10, 'intval');
        $productId        = I('product_id', 0, 'intval');
        $ticketId         = I('ticket_id', 0, 'intval');
        $douyinPoiId      = I('douyin_poi_id', 0, 'intval');
        $douyinPoiName    = I('douyin_poi_name', '', 'string');
        $ticketType       = I('ticket_type', 0, 'intval');
        $productCode      = I('product_code', 0, 'intval');
        $supplierId       = I('supplier_id', 0, 'intval');
        $douyinTicketName = I('douyin_ticket_name', '', 'string');
        $douyinGoodsName  = I('douyin_goods_name', '', 'string');

        $method  = 'calendarTicket/DouYinTicket/getUnbindTicketList';
        $rpcData = [
            'fid'                => $fid,
            'page'               => $page,
            'limit'              => $limit,
            'productId'          => $productId,
            'ticketId'           => $ticketId,
            'douyinPoiId'        => $douyinPoiId,
            'douyinPoiName'      => $douyinPoiName,
            'ticketType'         => $ticketType,
            'productCode'        => $productCode,
            'supplierId'         => $supplierId,
            'douyin_ticket_name' => $douyinTicketName,
            'douyin_goods_name'  => $douyinGoodsName,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 已对接商品列表(绑定抖音侧信息)
     *
     * <AUTHOR>
     * @date   2023-4-16
     */
    public function getDouYinTicketListWithInfo()
    {
        $fid           = $this->loginInfo['sid'];               //商户id
        $page          = I('page', 1, 'intval');
        $limit         = I('limit', 10, 'intval');
        $productId     = I('product_id', 0, 'intval');
        $ticketId      = I('ticket_id', 0, 'intval');
        $douyinPoiId   = I('douyin_poi_id', 0, 'intval');
        $skuSync       = I('sku_sync', 0, 'intval');
        $douyinPoiName = I('douyin_poi_name', '', 'string');
        $ticketType    = I('ticket_type', 0, 'intval');
        $status        = I('status', 0, 'intval');
        $productCode   = I('product_code', 0, 'intval');
        $supplierId    = I('supplier_id', 0, 'intval');

        $method  = 'calendarTicket/DouYinTicket/getDouYinTicketList';
        $rpcData = [
            'fid'           => $fid,
            'page'          => $page,
            'limit'         => $limit,
            'productId'     => $productId,
            'ticketId'      => $ticketId,
            'douyinPoiId'   => $douyinPoiId,
            'skuSync'       => $skuSync,
            'douyinPoiName' => $douyinPoiName,
            'ticketType'    => $ticketType,
            'status'        => $status,
            'productCode'   => $productCode,
            'supplierId'    => $supplierId,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 获取已对接票详情
     *
     * <AUTHOR>
     * @date   2023-4-6
     */
    public function getDouYinTicketDetail()
    {
        $id = I('id');

        //$method  = 'calendarTicket/DouYinTicket/getTicketDetail';
        //$rpcData = [
        //    'id' => $id,
        //];
        //
        //$rpcRes = $this->callRpc($method, $rpcData);

        $rpcRes = (new \Business\Ota\DouYinCalendarTicket())->getDouYinTicketDetail($id);

        if ($rpcRes['code'] == 200) {
            // 处理空数据
            if (empty($rpcRes['data']['ticketInfo']['poi'])) {
                $rpcRes['data']['ticketInfo']['poi'] = new \stdClass();
            }
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 同步抖音侧商品状态
     *
     * <AUTHOR>
     * @date   2023-5-10
     */
    public function syncPoiDouYinTicket()
    {
        $id         = I('id', 0, 'intval');
        $plantform  = I('plantform', '', 'string');
        $operatorId = $this->loginInfo['memberID'];   //操作人id

        $rpcRes = (new \Business\Ota\DouYinCalendarTicket())->syncPoiDouYinTicket($id, $plantform, $operatorId);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 获取推送日志列表
     *
     * <AUTHOR>
     * @date   2023-4-13
     */
    public function getDouYinTicketPushList()
    {

        $status     = I('status', 0, 'intval');
        $type       = I('type', 0, 'intval');
        $poiId      = I('douyin_poi_id', '', 'string');
        $productId  = I('product_id', 0, 'intval');
        $ticketId   = I('ticket_id', 0, 'intval');
        $operatorId = I('operator_id', 0, 'intval');
        $startTime  = I('create_time_start', '', 'string');
        $endTime    = I('create_time_end', '', 'string');
        $page       = I('page', 0, 'intval');
        $limit      = I('limit', 0, 'intval');
        $fid        = $this->loginInfo['sid'];               //商户id

        //获取员工信息
        $memberRes = (new Member())->getStaffList($fid, '', 1, 1000);
        $staffArr  = array_column($memberRes['list'], 'id');

        $method  = 'calendarTicket/DouYinTicketPush/getDouYinTicketPushList';
        $rpcData = [
            'fid'        => $fid,
            'status'     => $status,
            'type'       => $type,
            'poiId'      => $poiId,
            'productId'  => $productId,
            'ticketId'   => $ticketId,
            'operatorId' => $operatorId,
            'startTime'  => $startTime,
            'endTime'    => $endTime,
            'page'       => $page,
            'limit'      => $limit,
            'staffArr'   => $staffArr,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 获取推送日志详情
     *
     * <AUTHOR>
     * @date   2023-4-17
     */
    public function getDouYinTicketPushDetail()
    {
        $pushId = I('push_id', 0, 'intval');
        $fid    = $this->loginInfo['sid'];               //商户id
        $page   = I('page', 0, 'intval');
        $limit  = I('limit', 0, 'intval');
        $status = I('status', 0, 'intval');

        $method  = 'calendarTicket/DouYinTicketPush/getDouYinTicketPushDetail';
        $rpcData = [
            'push_id' => $pushId,
            'fid'     => $fid,
            'page'    => $page,
            'limit'   => $limit,
            'status'  => $status,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 抖音审核结果回调(测试)
     *
     * <AUTHOR>
     * @date   2023-4-18
     */
    public function douYinTicketAuditHook()
    {
        $method  = 'calendarTicket/DouYinTicket/douYinTicketAuditHook';
        $rpcData = [
            'content' => json_decode('{"id":"84","status":"FAIL","msg":"您好，本次审核不通过：【商品内容】您好，素材中商品描述不清楚或无实质内容，需详细说明产品或服务信息，请修改，谢谢。【票11111改】审核不通过：【商品内容】您好，素材中商品描述不清楚或无实质内容，需详细说明产品或服务信息，请修改，谢谢。 ","ext":{"product_id":"1763505116425219","product_out_id":"111","ticket_type_id":"1763502383628347","ticket_type_out_id":"152523","ticket_specifications":[{"sku_id":1763505116425235,"sku_out_id":"270","sku_name":"00:00-23:59","settle_type":1,"ticket_session":{"ticket_session_name":"00:00-23:59","ticket_session_time":null},"ticket_seat":null,"ticket_area":null}],"status":"FAIL","reason":"您好，本次审核不通过：【商品内容】您好，素材中商品描述不清楚或无实质内容，需详细说明产品或服务信息，请修改，谢谢。【票11111改】审核不通过：【商品内容】您好，素材中商品描述不清楚或无实质内容，需详细说明产品或服务信息，请修改，谢谢。 "},"log_id":"test-20230418-55f3629ccd452fac","log_time":"2023-04-18 17:33:39.511824","log_sort":3}',
                true),
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 获取推送日志请求信息
     *
     * <AUTHOR>
     * @date   2023-4-17
     */
    public function getDouYinTicketPushRequest()
    {
        $pushId = I('push_id', 0, 'intval');
        $fid    = $this->loginInfo['sid'];               //商户id

        $method  = 'calendarTicket/DouYinTicketPush/getDouYinTicketPushRequest';
        $rpcData = [
            'push_id' => $pushId,
            'fid'     => $fid,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 获取供应商+资源中心供应商列表
     *
     * Author : liucm
     * Date : 2023/5/16
     */
    public function getSuppliersList()
    {
        $keyWord = I('keyword', '', 'strval,trim');
        $sid     = $this->loginInfo['sid'];    //用户id
        if (mb_strlen($keyWord) == strlen($keyWord)) {
            //英文
            $searchType = 2;
        } else {
            //汉字
            $searchType = 1;
        }

        $memberRelationShipModel = new MemberRelationship();
        $memberRelationShipInfo  = $memberRelationShipModel->getResellerInfoByFidAndTitleAndAccount($sid, $keyWord,
            true,
            $searchType, true, 1);

        $data = $memberRelationShipInfo;
        $this->apiReturn(200, $data, '获取成功');
    }

    /**
     * 获取缓存key线上测试使用
     *
     * <AUTHOR>
     * @date   2023-4-24
     */
    public function getCache()
    {
        $key = I('key', '', 'string');

        $method  = 'calendarTicket/DouYinTicket/getCache';
        $rpcData = [
            'key' => $key,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }


    /**
     * 演出场次库存同步配置
     */
    public function showPriceStockSyncConfig()
    {
        $syncType = I('sync_type', 0, 'intval');
        $ticketDetailId  = I('ticket_detail_id', 0, 'intval');
        $fid    = $this->loginInfo['sid'];               //商户id
        $method  = 'calendarTicket/DouYinTicket/showPriceStockSyncConfig';
        $rpcData = [
            'syncType' => $syncType,
            'supplierId'     => $fid,
            'ticketDetailId' => $ticketDetailId,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }
        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 演出场次库存同步配置
     */
    public function getPriceStockSyncConfig()
    {
        $ticketDetailId  = I('ticket_detail_id', 0, 'intval');
        $fid    = $this->loginInfo['sid'];
        $method  = 'calendarTicket/DouYinTicket/getPriceStockSyncConfig';
        $rpcData = [
            'supplierId' => $fid,
            'ticketDetailId' => $ticketDetailId,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }
        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }


    /**
     * 场次组查询
     */
    public function queryRoundGroup()
    {
        $fid    = $this->loginInfo['sid'];
        $pageNum       = I('page_num', 1, 'intval');
        $pageSize      = I('page_size', 10, 'intval');
        $id = I('id', 0, 'intval');
        $ticketId  = I('ticket_id', 0, 'intval');
        $groupName = I('group_name', '', 'string');
        $method  = 'calendarTicket/DouYinTicketRoundGroup/queryRoundGroup';
        $rpcData = [
            'id' => $id,
            'supplierId' => $fid,
            'ticketId' => $ticketId,
            'groupName' => $groupName,
            'pageNum'     => $pageNum,
            'pageSize'     => $pageSize,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }
        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 查询场次组价格库存
     * @return void
     */
    public function queryRoundGroupPriceStorage()
    {
        $fid    = $this->loginInfo['sid'];
        $roungGroupId       = I('round_group_id', 0, 'intval');
        $method  = 'calendarTicket/DouYinTicketRoundGroup/queryRoundGroupPriceStorage';
        $rpcData = [
            'roundGroupId' => $roungGroupId,
            'supplierId' => $fid,
        ];
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }
        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 演出场次组保存
     */
    public function saveRoundGroup()
    {
        $fid    = $this->loginInfo['sid'];
        $memberId   = $this->loginInfo['memberID'];
        $ticketId  = I('ticket_id', 0, 'intval');
        $roundGroupName = I('round_group_name', '', 'string');
        $startTime    = I('start_time', '', 'string');
        $endTime      = I('end_time', '', 'string');
        $roundDayIds = I('round_day_ids', '', 'string');
        $rpcData = [
            'supplierId'     => $fid,
            'ticketId' => $ticketId,
            'roundGroupName' => $roundGroupName,
            'startTime'    => $startTime,
            'endTime'      => $endTime,
            'roundDayIds'  => $roundDayIds,
            'operatorId' => $memberId,
        ];
        $method  = 'calendarTicket/DouYinTicketRoundGroup/saveRoundGroup';
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }
        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 演出场次组更新
     */
    public function updateRoundGroup()
    {
        $fid    = $this->loginInfo['sid'];
        $id  = I('id', 0, 'intval');
        $roundGroupName = I('round_group_name', '', 'string');
        $startTime    = I('start_time', '', 'string');
        $endTime      = I('end_time', '', 'string');
        $roundDayIds = I('round_day_ids', '', 'string');
        $rpcData = [
            'id' => $id,
            'supplierId'     => $fid,
            'roundGroupName' => $roundGroupName,
            'startTime'    => $startTime,
            'endTime'      => $endTime,
            'roundDayIds'  => $roundDayIds,
        ];
        $method  = 'calendarTicket/DouYinTicketRoundGroup/updateRoundGroup';
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }
        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 演出场次组删除k
     */
    public function deleteRoundGroup()
    {
        $fid    = $this->loginInfo['sid'];
        $operatorId = $this->loginInfo['memberID'];
        $id  = I('id', 0, 'intval');
        $rpcData = [
            'supplierId' => $fid,
            'id' => $id,
            'operatorId' => $operatorId
        ];
        $method  = 'calendarTicket/DouYinTicketRoundGroup/deleteRoundGroup';
        $rpcRes  = $this->callRpc($method, $rpcData);
        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }
        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 获取国家编码列表
     * 
     */
    public function getCountryCodeList()
    {

        $res = (new DouYinCalendarTicket())->getCountryCodeList();
        $this->apiReturn(200, $res['data'], 'success'); 
    }

    /**
     * 获取省份编码列表
     * 
     */
    public function getProvinceCodeList()
    {
        $countryCode = I('country_code');
        $res = (new DouYinCalendarTicket())->getProvinceCodeList($countryCode);
        $this->apiReturn(200, $res['data'], 'success'); 
    }

    /**
     * 获取城市编码列表
     * 
     */
    public function getCityCodeList()
    {
        $provinceCode = I('province_code');
        $res = (new DouYinCalendarTicket())->getCityCodeList($provinceCode);
        $this->apiReturn(200, $res['data'], 'success'); 
    }

    /**
     * rpc 请求
     *
     * <AUTHOR>
     * @date 2022-08-31
     *
     * @param  string  $method
     * @param  array  $rpcData
     *
     * @return array
     */
    protected function callRpc(string $method, array $rpcData): array
    {
        $result = [
            'code' => 200,
            'msg'  => 'success',
            'data' => [],
        ];
        try {
            $lib            = new PftRpcClient('open_platform_api');
            $jsonRpcRes     = $lib->call($method, [
                $rpcData,
            ], 'admin_service');
            $result['data'] = $jsonRpcRes['data'];
            $result['code'] = $jsonRpcRes['code'];
            $result['msg']  = $jsonRpcRes['msg'];
        } catch (Throwable $e) {
            $result['code'] = $e->getCode();
            $result['msg']  = $e->getMessage();
        }

        return $result;
    }
}

?>