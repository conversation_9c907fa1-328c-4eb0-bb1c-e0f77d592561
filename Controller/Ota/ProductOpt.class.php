<?php
/**
 * 对接分销第三方产品操作
 *
 * <AUTHOR>
 * @date   2017-07-17
 */

namespace Controller\Ota;

use Business\JavaApi\Product\TimeShareTicketSection;
use Business\PackTicket\PackRelation;
use Library\Controller;
use \Business\Ota\SystemConfig as BizOtaConf;
use \Business\Ota\Product as BizOtaProduct;
use \Business\JavaApi\TicketApi;
use \Business\Product\Product as BizProduct;
use \Business\Product\HandleTicket as BizHandleTicket;
use \Business\Product\Show as BizShow;
use Library\JsonRpc\PftRpcClient;
use Model\Ota\SysConfig;
use Model\Product\CsysTicket;
use Model\qunaer\qunaer;
use Model\Product\Ticket;
use Model\Product\Land;
use Model\Ota\UpStreamRelationModel;
use Library\Cache\Cache;
use \Business\JavaApi\Product\PackageTicket;
use \Business\JavaApi\commodityCenter\Ticket as TicketBiz;
use \Business\JavaApi\Order\TimeShareOrder;
use Throwable;

class ProductOpt extends Controller
{
    private $_memberInfo = [];
    private $_sid;
    private $_memberId;
    private $_sysId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 获取有权限操作的对接系统id列表
     *
     * @param  int  $sid  主帐号id
     *
     * @return array 对接系统id列表 [1,2,3,4,....]
     */
    private function _getAuthSystem($sid)
    {
        //获取业务层
        $bizOtaConf = new BizOtaConf();
        $res        = $bizOtaConf->getAuthSystem($sid);
        if ($res['code'] != 200) {
            return [];
        } else {
            return $res['data'];
        }
    }

    /**
     * 获取第三方景点列表
     *
     * @param  int post.csys 对接系统id
     * @param  string post.uuid 第三方编码
     * @param  int post.page 页码
     * @param  int post.size 条数
     */
    public function fetchApiPOIs()
    {
        $sid = $this->_sid;
        //处理入参
        //对接系统id（DockingMode）
        $csysId = I('csys', 80, 'intval');
        //页码
        $page = I('page', 1, 'intval');
        //条数
        $size = I('size', 10, 'intval');

        if (!$csysId) {
            $this->apiReturn(400, [], '系统id无效');
        }

        $page = $page < 1 ? 1 : $page;
        $size = $size < 1 ? 10 : $size;

        //获取有权限对接的系统id
        $csysIdArr = $this->_getAuthSystem($sid);
        if (!in_array($csysId, $csysIdArr)) {
            $this->apiReturn(400, [], '无权限对接此系统');
        }

        //统一获取模型
        $sysConfigModel  = new SysConfig();
        $csysTicketModel = new CsysTicket();

        //查询对接系统基本信息
        $csysField   = 'id, name';
        $csysInfoRes = $sysConfigModel->getCsysInfoByids($csysId, $csysField);
        if (!$csysInfoRes) {
            $this->apiReturn(400, [], '对接系统查询失败');
        }
        $csysInfo = $csysInfoRes[0];
        //对接系统名称
        $csysName = $csysInfo['name'];

        //获取对接系统中已配置的帐号密码信息
        $csysAuthDetails = $sysConfigModel->getAccSysAuthDetailByCsysIds($sid, $csysId);
        if (!$csysAuthDetails) {
            $this->apiReturn(400, [], '对接系统配置信息获取失败');
        }
        //对接系统安全信息
        $authDetail = $csysAuthDetails[$csysId];
        //认证信息
        $identity = $authDetail['supplierIdentity'];
        //安全信息
        $signkey = $authDetail['signkey'];

        if (empty($identity) || empty($signkey)) {
            $this->apiReturn(400, [], '对接系统的帐号安全信息不能为空');
        }

        //获取业务层
        $bizOtaProduct = new BizOtaProduct();
        //获取第三方的产品
        $apiProdRes = $bizOtaProduct->getPOIs($csysId, $sid, [], $page, $size);

        if ($apiProdRes['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $apiProdRes['msg']);
        }

        //第三方的门票列表信息
        $pois = $apiProdRes['data']['POIs'];
        if (empty($pois)) {
            //没有第三方门票产品
            $this->apiReturn(200, [], '');
        }

        //处理后的产品列表
        $productList = [];
        //构造产品列表
        foreach ($pois as $ap) {
            //产品信息
            $tmpProduct    = [
                'csys'        => $csysId, //系统id
                'csys_name'   => $csysName, //系统名称
                'ltitle'      => $ap['ltitle'], //第三方景点名称
                'status'      => $ap['status'], //第三方产品状态， 1-上架， 0-下架
                'status_desc' => $ap['status'] == 1 ? '上架' : '下架', //第三方产品状态文字描述
                'api_lid'     => $ap['api_lid'], //第三方景点id
            ];
            $productList[] = $tmpProduct;
        }

        $this->apiReturn(200, $productList, '');
    }

    /**
     * 获取第三方产品
     *
     * @param  int post.csys 对接系统id
     * @param  string post.uuid 第三方编码
     * @param  int post.page 页码
     * @param  int post.size 条数
     */
    public function fetchApiProducts()
    {
        $sid = $this->_sid;
        //处理入参
        //对接系统id（DockingMode）
        $csysId = I('csys', 0, 'intval');
        //第三方编码
        $uuid = I('uuid', '', 'strval');
        //$uuid = false; //暂时屏蔽
        //页码
        $page = I('page', 1, 'intval');
        //条数
        $size = I('size', 10, 'intval');
        //第三方景区id
        $poiId = I('api_lid', '', 'strval');

        if (!$csysId) {
            $this->apiReturn(400, [], '系统id无效');
        }

        $page = $page < 1 ? 1 : $page;
        $size = $size < 1 ? 10 : $size;

        //第三方抓取时的id列表，全部抓取为空
        $dealId = [];
        if ($uuid) {
            //指定uuid只查1条
            $page   = 1;
            $size   = 1;
            $dealId = [$uuid];
        }
        // 抓取的景点id
        $POIIds = [$poiId];

        //获取有权限对接的系统id
        $csysIdArr = $this->_getAuthSystem($sid);
        if (!in_array($csysId, $csysIdArr)) {
            $this->apiReturn(400, [], '无权限对接此系统');
        }

        //统一获取模型
        $sysConfigModel  = new SysConfig();
        $csysTicketModel = new CsysTicket();

        //查询对接系统基本信息
        $csysField   = 'id, name';
        $csysInfoRes = $sysConfigModel->getCsysInfoByids($csysId, $csysField);
        if (!$csysInfoRes) {
            $this->apiReturn(400, [], '对接系统查询失败');
        }
        $csysInfo = $csysInfoRes[0];
        //对接系统名称
        $csysName = $csysInfo['name'];

        //获取对接系统中已配置的帐号密码信息
        $csysAuthDetails = $sysConfigModel->getAccSysAuthDetailByCsysIds($sid, $csysId);
        if (!$csysAuthDetails) {
            $this->apiReturn(400, [], '对接系统配置信息获取失败');
        }
        //对接系统安全信息
        $authDetail = $csysAuthDetails[$csysId];
        //认证信息
        $identity = $authDetail['supplierIdentity'];
        //安全信息
        $signkey = $authDetail['signkey'];

        if (empty($identity) || empty($signkey)) {
            $this->apiReturn(400, [], '对接系统的帐号安全信息不能为空');
        }

        //获取业务层
        $bizOtaProduct = new BizOtaProduct();
        //获取第三方的产品
        $apiProdRes = $bizOtaProduct->getProducts($csysId, $sid, $dealId, $POIIds, $page, $size);

        if ($apiProdRes['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $apiProdRes['msg']);
        }

        //第三方的门票列表信息
        $deals = $apiProdRes['data']['deals'];
        if (empty($deals)) {
            //没有第三方门票产品
            $this->apiReturn(200, [], '');
        }

        //已绑该系统的第三方门票
        $csysTicInfo = [];
        //处理后的产品列表(最终)
        $productList = [];

        //第三方产品编码列表
        $uuids = array_column($deals, 'uuid');

        //获取该系统绑定的景区ids
        $lids = $sysConfigModel->getLidsByCsysIdAndApplyId($csysId, $sid);
        if ($lids) {
            $thirdAttrApiBiz = new \Business\JavaApi\Ticket\ThirdAttr();
            $csysTickets     = [];

            foreach ($lids as $lid) {
                $thirdBindListArr = $thirdAttrApiBiz->getThirdBindList($lid);
                if ($thirdBindListArr['code'] == 200 && !empty($thirdBindListArr['data'])) {
                    $thirdBindList = [];
                    array_walk($thirdBindListArr['data'], function ($value) use (&$thirdBindList, $lid) {
                        array_push($thirdBindList, array_merge($value, ['lid' => $lid]));
                    });

                    $csysTickets = array_merge($csysTickets, $thirdBindList);
                }
            }

            if ($csysTickets) {
                //获取到已绑定的票，进行处理
                foreach ($csysTickets as $ct) {
                    $csysTicInfo[$ct['uuid']] = [
                        'tid'  => $ct['ticketId'],
                        'uuid' => $ct['uuid'],
                        'lid'  => $ct['lid'],
                    ];
                }
            }
        }

        //构造产品列表
        foreach ($deals as $ap) {
            //第三方产品编码
            $tmpid = $ap['uuid'];
            //产品信息
            $tmpProduct    = [
                'uuid'        => $tmpid,  //第三方产品编码
                'csys'        => $csysId, //系统id
                'csys_name'   => $csysName, //系统名称
                'ttitle'      => $ap['ttitle'], //第三方门票名称
                'ltitle'      => $ap['ltitle'], //第三方景点名称
                'status'      => $ap['status'], //第三方产品状态， 1-上架， 0-下架
                'status_desc' => $ap['status'] == 1 ? '上架' : '下架', //第三方产品状态文字描述
                'pft_tid'     => !empty($csysTicInfo[$tmpid]['tid']) ? $csysTicInfo[$tmpid]['tid'] : '', //票付通门票id
                'pft_lid'     => !empty($csysTicInfo[$tmpid]['lid']) ? $csysTicInfo[$tmpid]['lid'] : '', //票付通景点id
            ];
            $productList[] = $tmpProduct;
        }

        $this->apiReturn(200, $productList, '');
    }

    /**
     * 自动绑定产品（后续会拆分为几种绑定方式）
     *
     * @param  int post.csys 对接系统id
     * @param  string post.uuid 第三方编码
     */
    public function autoBind()
    {
        $sid      = $this->_sid;
        $memberId = $this->_memberId;

        //对接系统id（DockingMode）
        $csysId = I('post.csys', 0, 'intval');
        //第三方编码
        $uuid = I('post.uuid', '', 'strval');

        if (!$csysId) {
            $this->apiReturn(400, [], '系统id无效');
        }

        $this->_sysId = $csysId;

        //获取有权限对接的系统id
        $csysIdArr = $this->_getAuthSystem($sid);
        if (!in_array($csysId, $csysIdArr)) {
            $this->apiReturn(400, [], '无权限对接此系统');
        }

        //统一获取模型
        $sysConfigModel  = new SysConfig();
        $csysTicketModel = new CsysTicket();
        $landApi         = new \Business\CommodityCenter\Land();

        //获取对接系统中已配置的帐号密码信息
        $csysAuthDetails = $sysConfigModel->getAccSysAuthDetailByCsysIds($sid, $csysId);
        if (!$csysAuthDetails) {
            $this->apiReturn(400, [], '对接系统配置信息获取失败');
        }
        //对接系统安全信息
        $authDetail = $csysAuthDetails[$csysId];
        //认证信息
        $identity = $authDetail['supplierIdentity'];
        //安全信息
        $signkey = $authDetail['signkey'];

        if (empty($identity) || empty($signkey)) {
            $this->apiReturn(400, [], '对接系统的帐号安全信息不能为空');
        }

        //获取业务层，todo
        $bizOtaProduct = new BizOtaProduct();

        //Step 1:获取要绑定的第三方门票具体信息
        $dealId = [$uuid];
        //获取第三方的产品，todo
        $apiProdRes = $bizOtaProduct->getProducts($csysId, $sid, $dealId, [], 1, 1);
        if ($apiProdRes['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $apiProdRes['msg']);
        }

        //第三方门票列表信息
        $deals = $apiProdRes['data']['deals'];
        //第三方景点列表信息
        $pois = $apiProdRes['data']['POIs'];

        if (empty($deals) || empty($pois)) {
            //没有第三方门票产品
            $this->apiReturn(400, [], '获取第三方产品信息失败');
        }

        //此对接商品
        $deal = $deals[0];
        //第三方景区id
        $apiLid = $deal['lid'];
        //第三方门票名称
        $apiTtitle = $deal['ttitle'];

        $poi = $pois[$apiLid];
        if (empty($poi)) {
            $this->apiReturn(400, [], '获取第三方景区信息失败');
        }

        //第三方景区名称
        $apiLtitle = $poi['title'];

        //已存在绑定的景区id
        $existLid = false;

        //Step 2:查看该系统已绑定的景区中有没有此名称的景区，没有则新建景区
        //获取该系统绑定的景区ids
        $lids = $sysConfigModel->getLidsByCsysId($csysId);
        if ($lids) {
            //已有绑定的景区，查询是否有已绑定的产品（查看已绑定的景点是否有和第三方景区名称相同的景区）
            $landInfo = $landApi->queryLandMultiQueryByAdminAndPaging($lids, 1, 1, $apiLtitle, 'id desc', false, [$sid],
                [1]);
            $existLid = false;
            if ($landInfo['list']) {
                $existLid = $landInfo['list'][0]['id'];
            }
        }

        //获取票付通产品业务层
        $bizProduct = new BizProduct();

        if (!$existLid) {
            //没有景区id，需要先创建景区
            //新建景区
            $notice = ' 【开放时间】

                        【取票地点】

                        【入园凭证】

                        【优惠政策】

                        【发票说明】

                        【退改说明】

                        【温馨提示】

                        【联系电话】';
            // 默认预定须知，默认在售状态
            $addLandRes = $bizProduct->publish($sid, $memberId, $poi['title'], $poi['ptype'], $level = 'AAAAA',
                $poi['address'], $poi['province'],
                $poi['city'], $poi['notice'] ?: $notice, $poi['details'] ?: '', $poi['thumbImg'], $poi['status'] = 1,
                $optional = [], $internal = [], $extend = []);
            if ($addLandRes['code'] != 200) {
                $this->apiReturn(400, [], '新建景区失败,错误:' . $addLandRes['msg']);
            }
            //新建的景区id
            $existLid = $addLandRes['data'];
        }

        $javaApi     = new \Business\CommodityCenter\Ticket();
        $existTicket = $javaApi->queryTicketInfoByLandIdAndTitleAndApplyDidAndApplyLimitAndPStatus($existLid, 'id', '',
            '', '', $sid, 1, 0, $apiTtitle, 1, 1);
        //已有景区id，处理门票
        //Step 3: 新建门票/挑选门票
        if ($existTicket) {
            //景区已存在同名门票，无需添加
            $tid = $existTicket['data']['list'][0]['ticket']['id'];
        } else {
            //没有同名门票，需要添加
            $addTicketRes = $this->_saveTicket($sid, $existLid, $deal, $csysId);
            if ($addTicketRes['code'] != 200) {
                $this->apiReturn(400, [], $addTicketRes['msg']);
            }
            $tid = $addTicketRes['data']['tid'];
        }

        //第三方配置业务层
        $bizOtaConf = new BizOtaConf();

        //Step 4: 绑定门票
        //绑定门票
        $bindRes = $bizOtaConf->bindCsysToTicket($sid, $csysId, $tid, $uuid, 0, $memberId);

        if ($bindRes['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $bindRes['msg']);
        } else {
            //成功
            $this->apiReturn(200, $bindRes['data'], $bindRes['msg']);
        }
    }

    /**
     * 保存门票信息
     *
     * @param  int  $sid  主账号id
     * @param  int  $lid  景区id
     * @param  array  $apiProdInfo  第三方门票信息
     * @param  int  $csysId  coop_b 对接系统 id
     *
     * @return array
     */
    private function _saveTicket($sid, $lid, $apiProdInfo, $csysId)
    {

        // 转化参数为java入库的参数
        $ticketData = $this->_handleTicketToJava($apiProdInfo);

        //补上参数
        //景区id
        $ticketData['item_id'] = $lid;

        //参数处理,防止为空
        foreach ($ticketData as $attr => $value) {
            if ($value === null) {
                $ticketData[$attr] = '';
            }
        }

        // 保存门票和价格
        $priceData       = $ticketData['price_section'];
        $bizHandleTicket = new BizHandleTicket($this->_sid);
        $checkResult     = $bizHandleTicket->beforeHandel($ticketData, $priceData);

        if ($checkResult[0] != 0) {
            return [
                'code' => 400,
                'data' => [],
                'msg'  => $checkResult[1],
            ];
        }

        // 验证成功后 开始录入门票信息
        $addRes = $bizHandleTicket->setTicketNew($checkResult[1], $this->_sid);

        if ($addRes['code'] != 200) {
            return [
                'code' => 400,
                'data' => [],
                'msg'  => '保存门票失败,' . $addRes['msg'],
            ];
        }

        $addRes = $addRes['data'];

        $pid = $addRes['productId'];
        $tid = $addRes['ticketId'];
        $lid = $checkResult[1]['item_id'];

        $bizHandleTicket->setChannel($pid);

        // 保存价格信息
        if ($priceData) {

            $range = $daily = [];
            foreach ($priceData as $item) {
                unset($item['id']);
                if ($item['ptype'] == 1) {
                    //日历价格
                    $daily[] = $item;
                } else {
                    //区间段价格
                    $range[] = $item;
                }
            }

            if ($range) {
                $addPriceRes = $bizHandleTicket->savePrice($pid, $priceData, $tid);

                if ($addPriceRes['code'] != 200) {
                    return [
                        'code' => 400,
                        'data' => [],
                        'msg'  => '保存价格失败 - 错误:' . $addPriceRes['code'] . ', 信息:' . $addPriceRes['msg'],
                    ];
                }
            }

            $sysList = [80]; // 小岛旅行,区间价格特殊处理
            if (in_array($csysId, $sysList) && !empty($daily)) {

                $dateList = array_column($priceData, 'sdate');
                array_multisort($dateList, SORT_ASC, $priceData);

                $lastIndex      = count($priceData) - 1;
                $priceSection[] = [
                    'js'       => $priceData[0]['js'],
                    'm_price'  => $priceData[0]['m_price'],
                    'w_price'  => $priceData[0]['w_price'],
                    'ptype'    => 0,
                    'ls'       => $priceData[0]['ls'],
                    'id'       => $priceData[0]['id'],
                    'weekdays' => '0,1,2,3,4,5,6',
                    'sdate'    => $priceData[0]['sdate'],
                    'edate'    => $priceData[$lastIndex]['edate'],
                    'storage'  => $priceData[0]['storage'],
                ];

                $javaApiRes = $bizHandleTicket->savePrice($pid, $priceSection, $tid, 1);
                if ($javaApiRes['code'] != 200) {
                    return [
                        'code' => 400,
                        'data' => [],
                        'msg'  => '保存区间价格失败 - 错误:' . $javaApiRes['code'] . ', 信息:' . $javaApiRes['msg'],
                    ];
                }
            }

            if ($daily) {
                $ticketBiz = new TicketApi();
                $ticketBiz->setOperator($this->_sid, $this->_memberId);

                $addPriceRes = $ticketBiz->setDailyPrice($pid, $tid, $daily);

                if ($addPriceRes['code'] != 200) {
                    return [
                        'code' => 400,
                        'data' => [],
                        'msg'  => '保存价格失败 - 错误:' . $addPriceRes['code'] . ', 信息:' . $addPriceRes['msg'],
                    ];
                }
            }

        }

        //返回门票tid
        $data = [
            'tid' => $tid,
            'pid' => $pid,
            'lid' => $lid,
        ];

        return [
            'code' => 200,
            'data' => $data,
            'msg'  => '新建门票成功',
        ];
    }

    /**
     * 系统根据参数特殊处理归类
     *
     */
    private function _cysParamRefConf()
    {
        $data = [
            'verifyAbleWeek' => [73], // order_limit 传的是可验证的时间
            'identity_info'  => [80], // 需要传是否要身份证的
        ];

        return $data;
    }

    /**
     * 处理抓取来的数据为java的参数
     *
     * @param  array  $data
     *
     * @return array
     *
     */
    private function _handleTicketToJava($data)
    {
        // 上下架状态：
        $status = 1; // 默认上架
        if ($data['apply_limit'] === 0) {
            $status = 2;
        }

        $cysParamRefConf = $this->_cysParamRefConf();

        // 不可购买的时间
        $verifyDisableWeek = '';
        if (in_array($this->_sysId, $cysParamRefConf['verifyAbleWeek'])) {
            if ($data['order_limit']) {
                // 可购买时间转换不可购买时间
                if (strpos($data['order_limit'], '7') === false) {
                    $verifyDisableWeek = '0,';
                }
                for ($i = 1; $i < 7; $i++) {
                    if (strpos($data['order_limit'], (string)$i) === false) {
                        $verifyDisableWeek .= $i . ',';
                    }
                }
                $verifyDisableWeek = trim($verifyDisableWeek, ',');
            }
        } else {
            $verifyDisableWeek = $data['order_limit'];
        }

        // 是否需要身份证
        if (in_array($this->_sysId, $cysParamRefConf['identity_info'])) {
            $identityInfo = $data['tourist_info'];
        } else {
            $identityInfo = 0;
        }

        return [
            //'item_id'              => $data['lid'],
            'account_id'           => $this->_sid,
            'operater_id'          => $this->_memberId,
            'id'                   => $data['tid'] ?: '',
            'name'                 => $data['ttitle'],
            'ticket_price'         => $data['tprice'] / 100,
            'introduction'         => $data['notes'] ? $data['notes'] : '',
            'get_ticket_info'      => $data['getaddr'],
            'pay_way'              => $data['pay'],
            'nopay_max_minu'       => $data['cancel_auto_onMin'],
            'valid_period_type'    => $data['delaytype'],
            'valid_period_days'    => $data['delaydays'],
            'valid_period_start'   => $data['order_start'],
            'valid_period_end'     => $data['order_end'],
            'preorder_early_days'  => $data['ddays'],
            'preorder_expire_time' => $data['dhour'],
            'buy_max_amount'       => $data['buy_limit_up'],
            'buy_min_amount'       => $data['buy_limit_low'],

            'buy_limit_type'   => $data['buy_limit'],
            //购买限制类型,0:不限（默认值）,1:手机号,2:身份证,3:手机号+身份证
            'buy_limit_unit'   => $data['buy_limit_unit'],
            //0=张票，1=笔订单
            'buy_limit_period' => $data['buy_limit_date'],
            //购买限制周期,购票限制类型<>0时必传,0:整个销售期,1:每日,2:每周,3:每月
            'buy_limit_num'    => $data['buy_limit_num'],
            //限购票数（>0），购买限制类型>0时必填

            'buy_limit_min_age'         => $data['age_limit_min'] ? $data['age_limit_min'] : '',
            //
            'buy_limit_max_age'         => $data['age_limit_max'] ? $data['age_limit_max'] : '',
            //
            'num_modify'                => $data['num_modify'],
            //
            'verify_delay_hour'         => $data['vtimehour'],
            'verify_delay_minu'         => $data['vtimeminu'],
            'verify_disable_week'       => $verifyDisableWeek,
            'verify_time'               => $data['v_time_limit'],
            'verify_way'                => $data['batch_check'],
            //
            'verify_limit_amount'       => $data['batch_day_check'],
            //
            'expire_action'             => $data['expire_action'],
            //
            'expire_action_days'        => $data['expire_action_days'],
            //
            'expire_cancel_fee'         => $data['expire_action_fee'],
            //
            'revoke_audit'              => $data['revoke_audit'],
            //
            'refund_rule'               => $data['refund_rule'],
            'refund_early_minu'         => $data['refund_early_time'],
            'refund_after_time'         => $data['refund_after_time'] ?? 0,
            'refund_type'               => $data['reb_type'] ?: 0,
            'refund_value'              => $data['reb'] ?: 0,
            'refund_stair'              => $data['cancel_cost'] ? $data['cancel_cost'] : '',
            'identity_info'             => $identityInfo,
            // 游客信息0不需要填写1需要填写2需要填写所有游客身份证--[让客户自己抓取后再设置是否需要]
            'order_sms_buyer'           => $data['sendVoucher'] ? 0 : 1,
            'order_sms_supplier'        => $data['confirm_sms'],
            'order_wx_supplier'         => $data['confirm_wx'],
            'cancel_sms_supplier'       => $data['cancel_notify_supplier'],
            'cancel_sms_buyer'          => $data['cancel_sms'],
            'zone_id'                   => $data['zone_id'],
            //
            'play_days'                 => $data['rdays'],
            //
            'assembling_place'          => $data['ass_station'],
            //
            'touring_party_no'          => $data['series_model'],
            //
            'preorder_max_days'         => $data['max_order_days'],
            'type'                      => $data['p_type'],
            'docking_url'               => $data['mpath'],
            'refund_audit'              => $data['refund_audit'],
            'uuid'                      => $data['uuid'],
            // 新建立门票后的绑定需要
            'price_section'             => $data['price_section'],
            'shop'                      => $data['shop'],
            // 可销售渠道
            'print_ticket_limit'        => $data['print_ticket_limit'] ? $data['print_ticket_limit'] : '',
            // 取票时间，游玩当日可取，
            'status'                    => $status,
            'valid_period_timecancheck' => $data['if_verify'] ? $data['if_verify'] : 0,
            'extAttribute'              => $data['extAttribute'] ?? [],
        ];
    }

    /**
     * 绑定演出门票分销给ota
     *
     * @param  int session_time 场次  1 或者 2
     * @param  string tid_aid   门票编码
     */
    public function bindShowTicketToOta()
    {
        $fid         = $this->_sid;
        $tid         = I('request.tid');
        $tidCode     = I('request.tid_aid');
        $sessionTime = I('request.session_time');
        $dockingMode = I('request.docking_mode');  // 0=去哪儿,1=美团,2=百度直达

        if (empty($fid) || empty($tid) || empty($tidCode) || empty($sessionTime)) {
            $this->apiReturn(203, [], '参数错误');
        }

        // 根据fid coopid = 0 dockingMode 等于对应的  获取用户的首个配置
        $sysConfigModel = new \Model\Ota\SysConfig();
        $userKeyInfoArr = $sysConfigModel->findQunarInfoByModeAndFid($fid, $dockingMode);

        if (empty($userKeyInfoArr)) {
            $this->apiReturn(203, [], '用户绑定秘钥获取失败');
        }

        $supplierIdentity    = $userKeyInfoArr['supplierIdentity'];
        $signkey             = $userKeyInfoArr['signkey'];
        $thirdPartTeamworkId = $tidCode . '_' . $sessionTime;
        $tidAid              = $tidCode;
        $cooperationWay      = $userKeyInfoArr['cooperation_way'];

        $qunaerModel = new Qunaer();
        $bindInfoArr = $qunaerModel->getOtaBindingTicketByThird($fid, $dockingMode, $thirdPartTeamworkId);
        if (empty($bindInfoArr)) {
            $res = $qunaerModel->addBindShowTicketToOta($fid, $tidAid, $supplierIdentity, $signkey, $dockingMode, $tid,
                $thirdPartTeamworkId, $cooperationWay);
        } else {
            $res = $qunaerModel->upbindShowTicketToOta($bindInfoArr['id'], $fid, $tidAid,
                $supplierIdentity, $signkey, $dockingMode, $tid, $thirdPartTeamworkId, $cooperationWay);
        }

        if ($res) {
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '演出场次对接绑定编码失败');
        }
    }

    /**
     * 绑定演出门票分销给ota
     *
     * @param  int tid 门票id
     * @param  string ota_code_list   门票编码
     *                  [
     *                     "3385|204571|1035725_2","3385|204571|1035725_3"
     *                  ]
     * @param  int    docking_mode  // 0=去哪儿,1=美团,2=百度直达
     * @param  string tid_aid 列表上的 3385|204571|1035725
     *
     * @return string json
     *
     */
    public function bindManyShowTicketToOta()
    {
        $fid         = $this->_sid;
        $tid         = I('request.tid');
        $otaCodeList = I('request.ota_code_list');
        $dockingMode = I('request.docking_mode');  // 0=去哪儿,1=美团,2=百度直达
        $tidAid      = I('request.tid_aid');       // 列表上的门票编码
        $identity    = I('request.identity', '');  // 供应商通信标识
        $sign        = I('request.sign', '');      // 交互密钥
        $poiId       = I('request.poi_id', '');      // 抖音poi
        $ticketType  = I('post.ticket_type', 30, 'intval');// 10-团购券 30-半直连 50-预约团购券

        if (empty($fid) || empty($tid) || empty($otaCodeList) || empty($tidAid)) {
            $this->apiReturn(203, [], '参数错误');
        }

        // 美团的绑定需要判断下是否填写了编码
        if ($dockingMode == 1 && (empty($identity) || empty($sign))) {
            $this->apiReturn(203, [], '供应商通信标识或交互秘钥缺失');
        }

        // 快手本地生活不支持游玩日期
        if ((ENV == 'PRODUCTION' && $dockingMode == 406) || (ENV == 'TEST' && $dockingMode == 403)) {
            $this->apiReturn(203, [], '演出产品【需要选择游玩日期】不支持对接快手本地生活');
        }

        // 根据fid coopid = 0 dockingMode 等于对应的  获取用户的首个配置
        if (($dockingMode != 408 && ENV == 'PRODUCTION') || ($dockingMode != 246 && ENV == 'TEST') || ($dockingMode == 253 && ENV == 'DEVELOP')) {
            $sysConfigModel = new \Model\Ota\SysConfig();
            $userKeyInfoArr = $sysConfigModel->findQunarInfoByModeAndFid($fid, $dockingMode);
            if (empty($userKeyInfoArr)) {
                $this->apiReturn(203, [], '用户绑定秘钥获取失败');
            }
        }

        if (count($otaCodeList) != count(array_unique($otaCodeList))) {
            $this->apiReturn(203, [], '绑定编码重复');
        }

        if (($dockingMode == 408 && ENV == 'PRODUCTION')
            || ($dockingMode == 246 && ENV == 'TEST') || ($dockingMode == 253 && ENV == 'DEVELOP')) {
            // 抖音来客填充密钥数据
            $userKeyInfoArr['supplierIdentity'] = '';
            $userKeyInfoArr['signkey']          = '';
            $userKeyInfoArr['cooperation_way']  = 0;
            // 获取门票是否是演出期票
            //$otaQueryModel = new \Model\Ota\OtaQueryModel();
            //$ticketInfoArr = $otaQueryModel->getTicketInfoById($tid, 'pre_sale, p_type');
            //if ($ticketInfoArr['pre_sale'] == 0) {
            //    $this->apiReturn(203, [], '抖音来客暂不支持演出日历票的人工绑定');
            //}
        }
        $ticketBiz  = new \Business\JavaApi\CommodityCenter\Ticket();
        $ticketInfo = $ticketBiz->queryTicketInfoById($tid);
        $lid = $ticketInfo['data']['uuLandDTO']['id'];

        $bindIds = '';
        foreach ($otaCodeList as $item) {

            // 跳过空值
            if (empty($item)) {
                continue;
            }

            $supplierIdentity    = $userKeyInfoArr['supplierIdentity'];
            $signkey             = $userKeyInfoArr['signkey'];
            $thirdPartTeamworkId = $item;
            $cooperationWay      = $userKeyInfoArr['cooperation_way'];

            $qunaerModel = new Qunaer();
            $bindInfoArr = $qunaerModel->getOtaBindingTicketByThird($fid, $dockingMode, $thirdPartTeamworkId);
            if (empty($bindInfoArr)) {
                $res     = $qunaerModel->addBindShowTicketToOta($fid, $tidAid, $supplierIdentity, $signkey,
                    $dockingMode, $tid, $thirdPartTeamworkId, $cooperationWay, $this->_memberId, "H", $lid);
                $bindIds = $bindIds . ',' . $res;
            } else {
                $res     = $qunaerModel->upbindShowTicketToOta($bindInfoArr['id'], $fid, $tidAid,
                    $supplierIdentity, $signkey, $dockingMode, $tid, $thirdPartTeamworkId, $cooperationWay);
                $bindIds = $bindIds . ',' . $bindInfoArr['id'];
            }
        }

        if ($res) {
            if (($dockingMode == 408 && ENV == 'PRODUCTION')
                || ($dockingMode == 246 && ENV == 'TEST') || ($dockingMode == 253 && ENV == 'DEVELOP')) {
                // 调用开放平台写入绑定数据
                $operatorId = $this->_memberId;
                // lid解析
                $tidAidArr = explode('|', $tidAid);

                $bind         = $this->callRpc('calendarTicket/DouYinTicket/createTicket', [
                    'detail'       => [
                        'fid'               => $this->_sid,
                        'bind_product_code' => $thirdPartTeamworkId,
                        'ticket_id'         => $tid,
                        'product_id'        => $tidAidArr[1],
                        'product_code'      => $tidAid,
                        'supplier_id'       => $this->_sid,
                        'douyin_poi_id'     => $poiId,
                        'bind_id'           => trim($bindIds, ','),
                        'operator_id'       => $operatorId,
                        'ticket_type'       => $ticketType,
                        'ticket_name'       => '',
                        'goods_name'        => '',
                        'ptype'             => 'H'
                    ],
                    'product_data' => [],
                    'sku_data'     => [],
                    'douyin'       => [],
                ]);
            }
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '演出场次对接绑定编码失败');
        }
    }

    /**
     * 绑定给ota 产品编码
     * <AUTHOR>
     * @date 2021/4/26
     *
     * @return array
     */
    public function bindShowAndTimeSharePackTicketToOta()
    {
        $fid         = $this->_sid;
        $memberId    = $this->_memberId;
        $tid         = I('request.tid');
        $contentStr  = I('request.content_str');          // 套票组合内容
        $dockingMode = I('request.docking_mode');         // 0=去哪儿,1=美团,2=百度直达 408-抖音半直连
        $tidAid      = I('request.tid_aid');              // 列表上的门票编码
        $identity    = I('request.Identity', '');  // 供应商通信标识
        $sign        = I('request.sign', '');      // 交互密钥
        $poiId       = I('request.poi_id', '');      // 抖音poi
        $ticketType  = I('request.ticket_type', 30, 'intval'); // 门票类型 10-团购券 30-半直连 50-预约团购券
        $dockingForm = I('request.docking_form', 0, 'intval');
        $extInfoQuery = ['dockingForm' => $dockingForm];
        $extInfo = ['docking_form' => $dockingForm];
        if (empty($fid) || empty($memberId) || empty($tid) || empty($contentStr) || empty($tidAid)) {
            $this->apiReturn(203, [], '参数错误');
        }

        // 美团的绑定需要判断下是否填写了编码
        if ($dockingMode == 1 && (empty($identity) || empty($sign))) {
            $this->apiReturn(203, [], '供应商通信标识或交互秘钥缺失');
        }

        // 快手本地生活不支持游玩日期
        if ((ENV == 'PRODUCTION' && $dockingMode == 406) || (ENV == 'TEST' && $dockingMode == 403)) {
            $this->apiReturn(203, [], '演出产品【需要选择游玩日期】不支持对接快手本地生活');
        }

        $contentArr = json_decode(htmlspecialchars_decode($contentStr), true);
        if (empty($contentArr)) {
            $this->apiReturn(203, [], '绑定格式有误');
        }

        // 通过tid获取门票产品类型和lid
        $ticketBiz  = new \Business\JavaApi\CommodityCenter\Ticket();
        // 获取门票属性
        $ticketInfo = $ticketBiz->queryTicketInfoById($tid);
        if ($ticketInfo['code'] != 200) {
            $this->apiReturn(203, [], '门票信息获取失败');
        }

        $ptype = $ticketInfo['data']['uuLandDTO']['p_type']; 
        $lid   = $ticketInfo['data']['uuLandDTO']['id']; 

        //编码格式校验(抖音来客屏蔽校验，格式+poi)
        if (($dockingMode != 408 && ENV == 'PRODUCTION') || ($dockingMode != 246 && ENV == 'TEST') || ($dockingMode == 253 && ENV == 'DEVELOP')) {
            foreach ($contentArr as $thirdCode => $value) {
                if (count(explode('#', $thirdCode)) != 3) {
                    $this->apiReturn(203, [], '绑定格式有误');
                }
                list($tidAid, $dockMode, $packId) = explode('#', $thirdCode);
                if (!is_numeric($dockMode) || !is_numeric($packId) || !isset($dockMode) || !isset($packId)) {
                    $this->apiReturn(203, [], '编码需设置为数字类型');
                }
            }
        }
        $bindIds = '';
        foreach ($contentArr as $key => $item) {
            // 跳过空值
            if (empty($item)) {
                continue;
            }

            $supplierIdentity    = $identity;
            $signkey             = $sign;
            $thirdPartTeamworkId = $key;
            $cooperationWay      = 0;
            $contentStr          = json_encode($item, JSON_UNESCAPED_UNICODE);
            $qunaerModel         = new Qunaer();
            $bindInfoArr         = $qunaerModel->getOtaBindingTicketByThird($fid, $dockingMode, $thirdPartTeamworkId, $extInfoQuery);
            if (empty($bindInfoArr)) {
                $res = $qunaerModel->addBindShowTicketToOta($fid, $tidAid, $supplierIdentity, $signkey, $dockingMode,
                    $tid, $thirdPartTeamworkId, $cooperationWay, $this->_memberId, $ptype, $lid, $extInfo);
                // uu_qunar_use 表添加成功后,往演出场次绑定表也新增一条记录
                if ($res) {
                    $bindIds = $bindIds . ',' . $res;
                    $qunaerModel->addBindSectionAndTimeShareTicketToOta($tid, $fid, $contentStr, $dockingMode,
                        $thirdPartTeamworkId, $memberId);
                    $cache = Cache::getInstance('redis');
                    $cache->set("OTA:SkuCode:{$key}:DockMode:{$dockingMode}", $contentStr);
                }
            } else {
                $noticeMsg = '';
                if ($bindInfoArr['tid'] != $tid) {
                    $noticeMsg .= "此编码已绑定其他门票,绑定失败,门票编码:{$key}";
                    continue;
                }
                $res = $qunaerModel->upbindShowTicketToOta($bindInfoArr['id'], $fid, $tidAid,
                    $supplierIdentity, $signkey, $dockingMode, $tid, $thirdPartTeamworkId, $cooperationWay, $this->_memberId, $ptype, $lid);
                if ($res) {
                    $bindIds = $bindIds . ',' . $bindInfoArr['id'];
                    $qunaerModel->addBindSectionAndTimeShareTicketToOta($tid, $fid, $contentStr, $dockingMode,
                        $thirdPartTeamworkId, $memberId);
                    $cache = Cache::getInstance('redis');
                    $cache->set("OTA:SkuCode:{$key}:DockMode:{$dockingMode}", $contentStr);
                }
            }
        }

        if ($res) {
            if (($dockingMode == 408 && ENV == 'PRODUCTION')
                || ($dockingMode == 246 && ENV == 'TEST') || ($dockingMode == 253 && ENV == 'DEVELOP')) {
                // 调用开放平台写入绑定数据
                $operatorId = $this->_memberId;
                // lid解析
                $tidAidArr = explode('|', $tidAid);
                $bind         = $this->callRpc('calendarTicket/DouYinTicket/createTicket', [
                    'detail'       => [
                        'fid'               => $this->_sid,
                        'bind_product_code' => $thirdPartTeamworkId,
                        'ticket_id'         => $tid,
                        'product_id'        => $tidAidArr[1],
                        'product_code'      => $tidAid,
                        'supplier_id'       => $this->_sid,
                        'douyin_poi_id'     => $poiId,
                        'bind_id'           => trim($bindIds, ','),
                        'operator_id'       => $operatorId,
                        'ticket_type'       => $ticketType,
                        'ticket_name'       => '',
                        'goods_name'        => '',
                        'ptype'             => $ptype
                    ],
                    'product_data' => [],
                    'sku_data'     => [],
                    'douyin'       => [],
                ]);
            }
            $this->apiReturn(200, [], '绑定成功' . $noticeMsg);
        } else {
            $this->apiReturn(205, [], '演出场次对接绑定编码失败' . $noticeMsg);
        }
    }

    /**
     * 获取已绑定的产品编码的绑定详情
     * <AUTHOR>
     * @date 2021/4/26
     *
     * @return array
     */
    public function getBindSectionAndTimeshareInfoByTid()
    {
        $bindId = I('post.bind_id');
        if (empty($bindId)) {
            $this->apiReturn(203, [], '绑定id不可为空');
        }
        $qunaerModel = new Qunaer();
        $uuBindInfo  = $qunaerModel->getuuQunaerInfoByIdArr($bindId);
        if (!empty($uuBindInfo)) {
            $thirdPartArr = array_column($uuBindInfo, 'third_part_teamwork_id');
        }
        $bindDetail = $qunaerModel->getBindSectionAndTimeShareTicketByPartCode($thirdPartArr);
        if (empty($bindDetail)) {
            $this->apiReturn(203, [], '未获取到门票相关绑定场次信息');
        }

        $data = [];
        foreach ($bindDetail as $item) {
            $data[$item['third_part_teamwork_id']] = [
                'content_str'            => $item['content_str'],
                'bind_date'              => $item['bind_date'],
                'docking_mode'           => $item['docking_mode'],
                'third_part_teamwork_id' => $item['third_part_teamwork_id'],
                'tid'                    => $item['tid'],
                'sid'                    => $item['sid'],
            ];
        }

        $this->apiReturn(200, $data, '获取成功');
    }

    /**
     * 获取绑定的数据
     *
     * @param  int  $tid
     * @param  int  $dockingMode
     *
     * @return string json
     *
     */
    public function getOtaTicketBindList()
    {
        $fid         = $this->_sid;
        $tid         = I('request.tid');
        $dockingMode = I('request.dockingMode');  // 0=去哪儿,1=美团,2=百度直达
        if (empty($tid)) {
            $this->apiReturn(203, [], '参数传输错误');
        }

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoById($tid, 'landid,apply_did,pid');
        if (empty($ticketArr)) {
            // 返回失败
            $this->apiReturn(205, [], '门票数据获取失败');
        }

        $pType         = $ticketArr['land']['p_type'];
        $returnDataArr = [
            'bindList'  => [],
            'roundList' => [],
            'pType'     => $pType,
        ];

        $roundInfoArr = [];
        $roundSortArr = [];
        if ($pType == 'H' && !empty($ticketArr['land']['venus_id'])) {
            // 演出产品获取场次信息
            $showBiz      = new \Business\Product\Show();
            $roundInfoArr = $showBiz->getLastedRoundListForBindOta($ticketArr['land']['venus_id']);
            if ($roundInfoArr['code'] == 1 && !empty($roundInfoArr['data'])) {
                $returnDataArr['roundList'] = $roundInfoArr['data']['round_list'];
                $roundSortArr               = array_column($returnDataArr['roundList'], 'round_sort_id');
            }
        }

        $qunaerModel = new Qunaer();
        $bindInfoArr = $qunaerModel->getOtaBindingTicket($fid, $dockingMode, $tid);
        if (!empty($bindInfoArr)) {
            foreach ($bindInfoArr as $item) {
                $thirdPartTeamworkIdArr = explode('_', $item['third_part_teamwork_id']);
                $tempSortId             = empty(end($thirdPartTeamworkIdArr)) ? '' : end($thirdPartTeamworkIdArr);
                if (in_array($tempSortId, $roundSortArr) || empty($tempSortId)) {
                    $returnDataArr['bindList'][] = [
                        'tid'                    => $item['tid'],
                        'third_part_teamwork_id' => $item['third_part_teamwork_id'],
                        'round_sort_id'          => $tempSortId,
                    ];
                }
            }
        }

        $this->apiReturn(200, $returnDataArr, 'success');
    }

    /**
     * 获取指定时间的演出场次
     *
     * @param  int tid
     * @param  string date
     *
     * @return json
     *
     */
    public function getShowBindList()
    {
        $fid         = $this->_sid;
        $tid         = I('request.tid');
        $date        = I('request.date');
        $dockingMode = I('request.dockingMode', 1);  // 0=去哪儿,1=美团,2=百度直达
        if (empty($tid)) {
            $this->apiReturn(203, [], '参数传输错误');
        }

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoById($tid, 'landid,apply_did,pid');
        if (empty($ticketArr)) {
            // 返回失败
            $this->apiReturn(205, [], '门票数据获取失败');
        }

        $pType = $ticketArr['land']['p_type'];
        if ($pType == 'H' && !empty($ticketArr['land']['venus_id'])) {
            // 获取当天的场次
            $field     = 'id,round_name,bt,et,round_sort_id';
            $showModel = new \Model\Product\Show();
            $tmpList   = $showModel->getRoundList($ticketArr['land']['venus_id'], $field, $status = 0, $date, $date);
            $roundList = [];
            if (!empty($tmpList)) {
                // 查询出该门票已经绑定的门票列表
                $qunaerModel  = new Qunaer();
                $bindInfoArr  = $qunaerModel->getOtaBindingTicket($fid, $dockingMode, $tid);
                $bindCodeList = [];
                if (!empty($bindInfoArr)) {
                    foreach ($bindInfoArr as $item) {
                        // 兼容半直连数据
                        $teamWorkIdArr = explode('+',$item['third_part_teamwork_id']);
                        $thirdPartTeamworkIdArr             = explode('_', $teamWorkIdArr[0]);
                        $tempSortId                         = empty(end($thirdPartTeamworkIdArr)) ? '' : end($thirdPartTeamworkIdArr);
                        $bindCodeList['tmp_' . $tempSortId] = $item['third_part_teamwork_id'];
                    }
                }

                $isConfirm = false;
                foreach ($tmpList as $item) {

                    $thirdPartTeamworkId = '';
                    if (!empty($bindCodeList['tmp_' . $item['round_sort_id']])) {
                        $thirdPartTeamworkId = $bindCodeList['tmp_' . $item['round_sort_id']];
                    } else {
                        $isConfirm = true;
                    }

                    $roundList[] = [
                        'round_id'               => intval($item['id']),
                        'round_name'             => $item['round_name'],
                        'begin_time'             => $item['bt'],
                        'end_time'               => $item['et'],
                        'round_sort_id'          => $item['round_sort_id'],
                        'third_part_teamwork_id' => $thirdPartTeamworkId,
                    ];
                }
                $returnList = [
                    'is_confirm' => $isConfirm,
                    'list'       => $roundList,
                ];
            } else {
                $returnList = [
                    'is_confirm' => false,
                    'list'       => [],
                ];
            }

            $this->apiReturn(200, $returnList, 'success');
        }
        $this->apiReturn(200, [], '无数据');
    }

    /**
     * 根据产品id获取产品列表
     */
    public function getProductByLindId()
    {
        //景区id
        $lid        = I('post.lid', 0, 'intval');
        $csysId     = I('csys', 0, 'intval');
        $searchtype = I('p_type');

        if (!$lid) {
            $this->apiReturn(400, [], '景区id错误');
        }

        $ticketModel = new \Model\Product\Ticket('slave');
        //获取景区名称
        $landModel = new Land();
        $landInfo  = $landModel->getLandInfo($lid);
        if (!empty($landInfo) && $searchtype == 'H') {
            if ($landInfo['p_type'] != 'H') {
                $this->apiReturn(400, [], '请输入演出类型产品');
            }
        }
        $product_name = $landInfo['title'] ?? '';

        // 查询已绑定的门票
        $bizOtaConf    = new BizOtaConf();
        $bindTicketRes = $bizOtaConf->bindTicketList($lid);
        if ($bindTicketRes['code'] != 200) {
            $this->apiReturn(400, [], '无匹配门票');
        }

        $allBindTicketArr = $bindTicketRes['data'];
        $total            = 0;
        $ticketList       = [];

        if (!empty($allBindTicketArr)) {
            // 只查询绑定的门票
            $ticketIdArr = array_column($allBindTicketArr, 'ticketId');
            $ticketData  = $ticketModel->getTicketInfoMulti($ticketIdArr, 'id,title');
            foreach ($allBindTicketArr as $item) {
                $ticketList[] = [
                    'tid'    => $item['ticketId'],
                    'ttitle' => $ticketData[$item['ticketId']],
                    'uuid'   => $item['uuid'],
                ];
            }
            $total = count($ticketIdArr);
        }

        $return = [
            'list'  => $ticketList,
            'total' => $total,
            'lname' => $product_name,
            'lid'   => $lid,
        ];

        $this->apiReturn(200, $return, '');
    }

    /**
     * 拉取第三方价格库存
     * User: Liucm
     * Date: 2020/12/11
     * Time: 18:30
     */
    public function fetchApiPrice()
    {
        //第三方产品id
        $uuid   = I('post.uuid', 0, 'strval');
        $tid    = I('post.tid', 0, 'intval');
        $lid    = I('post.lid', 0, 'intval');
        $bTime  = I('post.btime', '', 'strval');
        $eTime  = I('post.etime', '', 'strval');
        $pname  = I('post.pname', '', 'strval');
        $tname  = I('post.tname', '', 'strval');
        $csysId = I('csys', 0, 'intval');

        //判断是否开启分时，开启分时库存
        $productApi = new \Business\JavaApi\Product\TimeShareTicketSection();
        $queryRes   = $productApi->querySectionDisableOfTicket($tid);
        //$queryRes['data'] 返回值为true/false,false为开启分时，true为关闭分时
        if ($queryRes['data'] == false) {
            // $this->apiReturn(400, [], '该票类已开启分时，请先关闭分时再拉取');
        }

        [$year, $month, $day] = explode('-', $bTime);
        $bTimeCheck = checkdate((int)$month, (int)$day, (int)$year);
        if (!$bTimeCheck) {
            $this->apiReturn(400, [], '开始时间格式错误');
        }
        [$year, $month, $day] = explode('-', $eTime);

        $eTimeCheck = checkdate((int)$month, (int)$day, (int)$year);
        if (!$eTimeCheck) {
            $this->apiReturn(400, [], '结束时间格式错误');
        }
        if (empty($uuid) || empty($tid) || empty($lid) || empty($csysId)) {
            $this->apiReturn(400, [], '参数错误');
        }
        if (strtotime($eTime) < strtotime($bTime)) {
            $this->apiReturn(400, [], '结束时间不可小于开始时间');
        }
        $time = strtotime($eTime) - strtotime($bTime);
        $time = $time / 86400;

        if ($time > 30) {
            $this->apiReturn(400, [], '时间不可超过31天');
        }
        //限制一个拉取未结束，不能进行下一次拉取，如果服务器原因导致没有进行拉取，5分钟后可继续拉取
        $logModel = new \Model\Ota\ApiPullThirdLog();
        $logRes   = $logModel->getApiPullThirdLogInfoByLidAndTidAndStatusAndMemberId($this->_sid, $lid, $tid, 0,
            $field = '*');

        if ($logRes) {
            $this->apiReturn(400, [], '正在拉取中');
        }

        //TODO 字段错了 导致本地只有10位，到时候改一下
        if (ENV == 'PRODUCTION') {
            $requestId = str_replace('.', '', microtime(true));
        } else {
            $requestId = time();
        }

        $request = [
            'uuid'      => $uuid,
            'tid'       => $tid,
            'lid'       => $lid,
            'aid'       => $this->_sid,
            'bTime'     => $bTime,
            'eTime'     => $eTime,
            'requestId' => $requestId,
        ];

        $data = [
            'request_id'  => $requestId,
            'lid'         => $lid,
            'tid'         => $tid,
            'pname'       => $pname,
            'tname'       => $tname,
            'state'       => 0,
            'operid'      => $this->_memberId,
            'sid'         => $this->_sid,
            'csys_id'     => $csysId,
            'request'     => json_encode($request),
            'pull_type'   => 0,
            'create_time' => time(),
        ];

        $logModel = new \Model\Ota\ApiPullThirdLog();
        $logModel->insertApiPullThirdLogData($data);

        //拉取库存推送
        \Library\Resque\Queue::push('cooperation_system', 'OtaSupplierNotice_Job',
            [
                'job_type'  => 'pull_price_storage',
                'uuid'      => $uuid,
                'tid'       => $tid,
                'lid'       => $lid,
                'aid'       => $this->_sid,
                'operid'    => $this->_memberId,
                'startDate' => $bTime,
                'endDate'   => $eTime,
                'requestId' => $requestId,
            ]
        );

        $this->apiReturn(200, [], '拉取中');
    }

    /**
     * 获取日历价格拉取日志
     * User: Liucm
     * Date: 2020/12/11
     * Time: 18:30
     */
    public function pullApiPriceLog()
    {
        //对接系统id（DockingMode）
        $csysId = I('csys', 0, 'intval');
        //门票id
        $tid = I('tid', 0, 'intval');
        //页码
        $page = I('page', 1, 'intval');
        //条数
        $size = I('size', 10, 'intval');

        if (!$csysId) {
            $this->apiReturn(400, [], '系统id无效');
        }

        $page = $page < 1 ? 1 : $page;
        $size = $size < 1 ? 10 : $size;

        $logModel = new \Model\Ota\ApiPullThirdLog();
        $list     = $logModel->getApiPullThirdLogListByCsysIdAndSid($this->_sid, $csysId, $page, $size, $tid);

        $logListArr = $list['list'] ?? [];
        $totalNum   = $list['total'] ?? 0;
        $logList    = [];

        if ($logListArr) {
            $state = [
                '0' => '拉取中',
                '1' => '拉取成功',
                '2' => '拉取失败',
                '3' => '部分失败',
            ];
            foreach ($logListArr as $list) {
                $logList[] = [
                    'pname' => $list['pname'] . '|' . $list['lid'],
                    'tname' => $list['tname'] . '|' . $list['tid'],
                    'log'   => $list['response'],
                    'state' => $state[$list['state']],
                    'time'  => date('Y-m-d H:i:s', $list['create_time']),
                ];
            }
        }

        $returnDataArr = ['total' => (int)$totalNum, 'list' => $logList];

        $this->apiReturn(200, $returnDataArr, 'success');
    }

    /**
     * 获取日历价格拉取日志
     * User: Cr
     * Date: 2021/04/11
     * Time: 18:30
     */
    public function pullSectionShowLog()
    {
        //对接系统id（DockingMode）
        $csysId = I('csys', 0, 'intval');
        //门票id
        $tid = I('tid', 0, 'intval');
        //页码
        $page = I('page', 1, 'intval');
        //条数
        $size = I('size', 10, 'intval');

        if (!$csysId) {
            $this->apiReturn(400, [], '系统id无效');
        }

        $page = $page < 1 ? 1 : $page;
        $size = $size < 1 ? 10 : $size;

        $logModel = new \Model\Ota\ApiPullThirdLog();
        $list     = $logModel->getApiPullThirdSectionLogListByCsysIdAndSid($this->_sid, $csysId, $page, $size, $tid);

        $logListArr = $list['list'] ?? [];
        $totalNum   = $list['total'] ?? 0;
        $logList    = [];

        if ($logListArr) {
            $state = [
                '0' => '拉取中',
                '1' => '拉取成功',
                '2' => '拉取失败',
                '3' => '部分失败',
            ];
            foreach ($logListArr as $list) {
                $dateRange = json_decode($list['request'], true);
                $startDate = $dateRange['date_range']['startDate'] ?? "未知";
                $endDate   = $dateRange['date_range']['endDate'] ?? "未知";;

                $logList[] = [
                    'pname'      => $list['pname'] . '|' . $list['lid'],
                    'tname'      => $list['tname'] . '|' . $list['tid'],
                    'date_range' => $startDate . '至' . $endDate,
                    'log'        => $list['response'],
                    'state'      => $state[$list['state']],
                    'time'       => date('Y-m-d H:i:s', $list['create_time']),
                ];
            }
        }

        $returnDataArr = ['total' => (int)$totalNum, 'list' => $logList];

        $this->apiReturn(200, $returnDataArr, 'success');
    }

    /**
     * 拉取第三方分时库存
     * User: Liucm
     * Date: 2021/1/5
     * Time: 17:02
     */
    public function fetchApiTimeStock()
    {
        //第三方产品id
        $uuid      = I('post.uuid', 0, 'strval');
        $tid       = I('post.tid', 0, 'intval');
        $lid       = I('post.lid', 0, 'intval');
        $startDate = I('post.btime', '', 'strval');
        $endDate   = I('post.etime', '', 'strval');
        $pname     = I('post.pname', '', 'strval');
        $tname     = I('post.tname', '', 'strval');
        $csysId    = I('csys', 0, 'intval');
        $month     = I('post.month', '', 'strval');
        $weekDays  = I('post.weekdays', '0,1,2,3,4,5,6', 'strval');

        $lockRes = $this->_isLockPull($tid);
        if ($lockRes == true) {
            $this->apiReturn(400, [], '请求过于频繁,5秒后重试');
        }
        if ($month) {
            $startDate = date('Y-m-01', strtotime($month));
            $endDate   = date('Y-m-d', strtotime("$startDate +1 month -1 day"));
        }

        if ($startDate < date('Y-m-d')) {
            $startDate = date('Y-m-d');
        }

        if ($endDate < date('Y-m-d')) {
            $endDate = date('Y-m-d');
        }

        [$year, $month, $day] = explode('-', $startDate);
        $startDateCheck = checkdate((int)$month, (int)$day, (int)$year);
        if (!$startDateCheck) {
            $this->apiReturn(400, [], '开始时间格式错误');
        }
        [$year, $month, $day] = explode('-', $endDate);

        $endDateCheck = checkdate((int)$month, (int)$day, (int)$year);
        if (!$endDateCheck) {
            $this->apiReturn(400, [], '结束时间格式错误');
        }
        if (empty($uuid) || empty($tid) || empty($lid) || empty($csysId)) {
            $this->apiReturn(400, [], '参数错误');
        }
        if (strtotime($endDate) < strtotime($startDate)) {
            $this->apiReturn(400, [], '结束时间不可小于开始时间');
        }
        $time     = strtotime($endDate) - strtotime($startDate);
        $time     = $time / 86400;
        $checkRes = $this->_checkType($tid);
        if (!$checkRes) {
            $this->apiReturn(400, [], '景区类型门票才可拉取');
        }
        if ($time > 30) {
            $this->apiReturn(400, [], '时间不可超过31天');
        }
        //限制一个拉取未结束，不能进行下一次拉取，如果服务器原因导致没有进行拉取，5分钟后可继续拉取
        $logModel = new \Model\Ota\ApiPullThirdLog();
        $logRes   = $logModel->getApiPullThirdLogInfoByLidAndTidAndStatusAndMemberId($this->_sid, $lid, $tid, 1,
            $field = '*');

        if ($logRes) {
            $this->apiReturn(400, [], '正在拉取中');
        }

        //TODO 字段错了 导致本地只有10位，到时候改一下
        if (ENV == 'PRODUCTION') {
            $requestId = str_replace('.', '', microtime(true));
        } else {
            $requestId = time();
        }

        $request = [
            'lid'       => $lid,
            'tid'       => $tid,
            'goodsNo'   => $uuid,
            'startDate' => $startDate,
            'endDate'   => $endDate,
            'applyDid'  => $this->_sid,
            'requestId' => $requestId,
        ];

        $openOtaBiz = new \Business\Ota\Open\Product();
        $result     = $openOtaBiz->pullThirdTimeStorage($request);
        $msg        = $result['msg'];
        $state      = 0;
        if ($result['code'] != 200 || empty($result['data'])) {
            $state = 2;
            $msg   = '拉取失败或数据为空#' . $result['msg'];
        }
        $data = [
            'request_id'  => $requestId,
            'lid'         => $lid,
            'tid'         => $tid,
            'pname'       => $pname,
            'tname'       => $tname,
            'state'       => $state,
            'operid'      => $this->_memberId,
            'sid'         => $this->_sid,
            'csys_id'     => $csysId,
            'request'     => json_encode($request),
            'pull_type'   => 1, // 0 拉取价格+库存, 1 拉取分时库存
            'create_time' => time(),
            'response'    => "拉取结果:" . $msg,
        ];

        $logModel = new \Model\Ota\ApiPullThirdLog();
        $logModel->insertApiPullThirdLogData($data);

        if ($result['code'] == 200 && !empty($result['data'])) {
            //拉取库存推送
            \Library\Resque\Queue::push('cooperation_system', 'OtaSupplierNotice_Job',
                [
                    'job_type'  => 'pull_time_storage',
                    'uuid'      => $uuid,
                    'tid'       => $tid,
                    'lid'       => $lid,
                    'aid'       => $this->_sid,
                    'operid'    => $this->_memberId,
                    'startDate' => $startDate,
                    'endDate'   => $endDate,
                    'requestId' => $requestId,
                    'data'      => $result['data'],
                    'weekDays'  => $weekDays,
                ]
            );

            $timeStorageArr = $result['data'];

            $this->apiReturn(200, $timeStorageArr, '拉取信息异步返回，结果成功失败的具体日志请点击“查看日志”获取');
        }

        $this->apiReturn(400, [], '拉取失败');
    }

    /**
     * 拉取通用接口演出场次信息
     * User: Cr
     * Date: 2021/04/06
     * Time: 17:02t
     */
    public function fetchApiShowProductSeason()
    {
        $uuid      = I('post.uuid', '', 'strval');
        $tid       = I('post.tid', '', 'intval');
        $lid       = I('post.lid', '', 'intval');
        $startDate = I('post.bg_time', '', 'strval');
        $endDate   = I('post.ed_time', '', 'strval');
        $pName     = I('post.pname', '', 'strval');
        $tName     = I('post.tname', '', 'strval');
        $csysId    = I('post.csys_id', '', 'intval');

        if (empty($uuid) || empty($tid) || empty($lid) || empty($startDate) || empty($endDate) || empty($csysId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        [$year, $month, $day] = explode('-', $startDate);
        $startDateCheck = checkdate((int)$month, (int)$day, (int)$year);
        if (!$startDateCheck) {
            $this->apiReturn(203, [], '开始时间格式错误');
        }

        [$year, $month, $day] = explode('-', $endDate);
        $endDateCheck = checkdate((int)$month, (int)$day, (int)$year);
        if (!$endDateCheck) {
            $this->apiReturn(203, [], '结束时间格式错误');
        }

        $version = $this->_getSysVersion($lid);
        if (!in_array($version, ['Inside', 'V1'])) {
            $this->apiReturn(203, [], '不支持的拉取模式');
        }

        if (ENV == 'PRODUCTION') {
            $requestId = str_replace('.', '', microtime(true));
        } else {
            $requestId = time();
        }

        //不能进行多次拉取
        $redisService = Cache::getInstance('redis');
        $lockRes      = $redisService->lock("FETCH_SECTION_TASK{$tid}", 1, 3600);
        if (!$lockRes) {
            $this->apiReturn(400, [], '上次拉取任务未完成,请稍后重试');
        }

        //根据景点id匹配场馆id
        $productBiz  = new bizProduct();
        $landInfoArr = $productBiz->getProductInfo($this->_sid, $lid, 0);
        if (empty($landInfoArr['data']['venus_id'])) {
            $this->apiReturn(203, [], "未匹配到对应场馆信息");
        }
        $venusId = $landInfoArr['data']['venus_id'];

        //拉取库存推送
        \Library\Resque\Queue::push('cooperation_system', 'OtaSupplierNotice_Job',
            [
                'job_type'  => 'pull_section_show',
                'uuid'      => $uuid,
                'tid'       => $tid,
                'lid'       => $lid,
                'aid'       => $this->_sid,
                'operid'    => $this->_memberId,
                'startDate' => $startDate,
                'endDate'   => $endDate,
                'requestId' => $requestId,
                'venusId'   => $venusId,
                'pName'     => $pName,
                'tName'     => $tName,
                'csysId'    => $csysId,
                'version'   => $version,
            ]
        );

        $this->apiReturn(200, [], '拉取任务开始,请等待');
    }

    /**
     * 拉取两江游的产品列表接口
     * User: cyw
     * Date: 2023/03/17
     * /r/Ota_ProductOpt/fetchApiProductListForLiangJiangYou
     *
     */
    public function fetchApiProductListForLiangJiangYou()
    {
        $systemId = I('request.system_id', '', 'intval');
        $secretId = I('request.secret_id', '', 'intval');

        if (empty($systemId) || empty($secretId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (ENV == 'PRODUCTION') {
            $requestId = str_replace('.', '', microtime(true));
        } else {
            $requestId = time();
        }
        $productBiz = new \Business\Ota\Open\Product();
        $data       = [
            'sysSecretId' => $secretId,
            'systemId'    => $systemId,
            'applyDid'    => $this->_sid,
            'requestId'   => $requestId,
            'requestData' => ['sonMethod' => 'pull_product_list'],
        ];

        $result = $productBiz->ProductCustomizedInterface($data);

        if ($result['code'] != 200) {
            $this->apiReturn(203, [], $result['msg']);
        } else {
            $this->apiReturn(200, $result['data'], '获取成功');
        }
    }

    /**
     * 拉取两江游的库存列表接口
     * User: cyw
     * Date: 2023/03/17
     * /r/Ota_ProductOpt/fetchApiStocktListForLiangJiangYou
     *
     */
    public function fetchApiStocktListForLiangJiangYou()
    {
        $systemId = I('request.system_id', '', 'intval');
        $secretId = I('request.secret_id', '', 'intval');
        $goodsNo  = I('request.product_id', '');

        if (empty($systemId) || empty($secretId) || empty($goodsNo)) {
            $this->apiReturn(203, [], '参数缺失1');
        }

        if (ENV == 'PRODUCTION') {
            $requestId = str_replace('.', '', microtime(true));
        } else {
            $requestId = time();
        }
        $productBiz = new \Business\Ota\Open\Product();
        $data       = [
            'sysSecretId' => $secretId,
            'systemId'    => $systemId,
            'applyDid'    => $this->_sid,
            'requestId'   => $requestId,
            'requestData' => [
                'sonMethod' => 'pull_stock',
                'goodsNo'   => $goodsNo,
            ],
        ];

        $result = $productBiz->ProductCustomizedInterface($data);

        if ($result['code'] != 200) {
            $this->apiReturn(203, [], $result['msg']);
        } else {
            $this->apiReturn(200, $result['data'], '获取成功');
        }
    }

    public function filterParams($dateWithSeasonListMap, $startDate, $endDate)
    {
        $dateError    = '';
        $sortError    = '';
        $sectionError = '';

        foreach ($dateWithSeasonListMap as $key => $checkItemDay) {
            $sectionIds = [];
            $sortIds    = [];
            foreach ($checkItemDay as $checkItem) {
                //过滤掉上游返回的小于开始日期,大于结束日期的数据
                if (strtotime($checkItem['date']) < strtotime($startDate) || strtotime($checkItem['date']) > strtotime($endDate) || !(strtotime($checkItem['date'] . $checkItem['startTime'])) || !(strtotime($checkItem['date'] . $checkItem['endTime']))) {
                    $dateError .= "{$checkItem['date']}.时间段超出或存在错误,|";
                    unset($dateWithSeasonListMap[$key]);
                };

                //检验场次序号是否有重复
                if (in_array($checkItem['sortId'], $sortIds)) {
                    $sortError .= "{$checkItem['date']}.当日序号重复,取消当日场次|";
                    unset($dateWithSeasonListMap[$key]);
                }
                $sortIds[] = $checkItem['sortId'];

                //检验上游场次id是否有重复
                if (in_array($checkItem['sectionId'], $sectionIds)) {
                    $sectionError .= "{$checkItem['date']}.当日Id冲突,取消当日场次|";
                    unset($dateWithSeasonListMap[$key]);
                }
                $sectionIds[] = $checkItem['sectionId'];
            }
        }

        $msg = $dateError . $sortError . $sectionError;

        return [
            'msg'  => $msg,
            'data' => $dateWithSeasonListMap,
        ];
    }

    /**
     * 判断景区的类型
     * User: Liucm
     * Date: 2021/1/22
     * Time: 15:48
     */
    public function _checkType($tid)
    {
        //获取票属性
        $ticketBz   = new \Business\CommodityCenter\Ticket();
        $ticketInfo = $ticketBz->queryTicketInfoById($tid, 'p_type');
        if ($ticketInfo && $ticketInfo['product']['p_type'] == 'A') {
            return true;
        }

        return false;
    }

    /**
     * 限制并发点击
     *
     */
    private function _isLockPull($tid)
    {
        $lockey  = "productOpt:tid:$tid";
        $cache   = \Library\Cache\Cache::getInstance('redis');
        $lockRes = $cache->lock($lockey, 1, 5);
        if (!$lockRes) {
            return true;
        }

        return false;
    }

    /**
     * 获取套票底下的景区票分时段与演出票场次
     *
     * @param  int post.parant_id 套票id
     * @param  string post.aid 供应商Id
     *
     * @return string
     */
    public function getPackageShowAndTimeShareInfoByTid()
    {
        $parantId = I('post.parant_id', '', 'intval');
        $aid      = I('post.aid', '', 'intval');
        if (empty($parantId)) {
            $this->apiReturn(203, [], '主票id不可为空');
        }

        $packageBiz   = new PackRelation();
        $ticketBiz    = new \Business\JavaApi\CommodityCenter\Ticket();
        $timeShareBiz = new TimeShareOrder();
        $ticketSectionBiz = new TimeShareTicketSection();
        $packageInfo  = $packageBiz->getPackageTicketAttrList($parantId);
        if ($packageInfo['code'] == 200 && !empty($packageInfo['data'])) {
            $tidArr         = array_column($packageInfo['data'], 'tid');
            $ticketAttrList = $ticketBiz->queryTicketInfoByIds($tidArr);
            $tname          = [];
            foreach ($ticketAttrList['data'] as $attrItem) {
                if ($attrItem['uuLandDTO']['p_type'] == "A") {
                    $typeA[] = (int)$attrItem['confs']['tid'];
                    $typeALidArr[(int)$attrItem['confs']['tid']] = (int)$attrItem['uuJqTicketDTO']['landid'];
                }
                if ($attrItem['uuLandDTO']['p_type'] == "H") {
                    $typeH[] = [
                        'tid'    => (int)$attrItem['confs']['tid'],
                        'lid'    => (int)$attrItem['uuJqTicketDTO']['landid'],
                        'zoneId' => (int)$attrItem['uuLandFDTO']['zone_id'],
                        'tname'  => $attrItem['uuJqTicketDTO']['title'],
                    ];
                }
                $tname[(int)$attrItem['confs']['tid']] = $attrItem['uuJqTicketDTO']['title'];
            }

            $date              = date('Y-m-d');
            $ticketSectionList = [];
            if (!empty($typeA)) {
                $sectionDetail = $timeShareBiz->getTimeSlicesWithTidArr($typeA, $date, 1);
                if ($sectionDetail['code'] == 200 && !empty($sectionDetail['data'])) {
                    foreach ($sectionDetail['data'] as $tid => $timeSectionItem) {
                        if (!empty($timeSectionItem['time_slices'])) {
                            foreach ($timeSectionItem['time_slices'] as $timesectionDet) {
                                $ticketSectionList[$tid][] = [
                                    'begin' => $timesectionDet['begin'],
                                    'end'   => $timesectionDet['end'],
                                    'tname' => $tname[$tid],
                                    'id'    => $timesectionDet['id'],
                                ];
                            }
                        } else {
                            // 无分时数据判断是否开启分时
                            $sectionEnable = $ticketSectionBiz->querySectionEnableOfTicket($typeALidArr[$tid],$tid);
                            if (isset($sectionEnable['data']) && $sectionEnable['data'] === true) {
                                // 开启分时无分时数据，则提示配置
                                $this->apiReturn(203, [], "当天子票{$tname[$tid]}存在开启分时未配置分时规则,请先添加分时配置");
                            }
                        }
                    }
                }
            }

            $roundList = [];
            if (!empty($typeH)) {
                //根据景点id匹配场馆id
                $productBiz = new bizProduct();
                foreach ($typeH as $item) {
                    $landInfoArr = $productBiz->getProductInfo($this->_sid, $item['lid'], 0);
                    if (empty($landInfoArr['data']['venus_id']) || empty($landInfoArr['data']['apply_did'])) {
                        continue;
                    }
                    $venusId            = $landInfoArr['data']['venus_id'];
                    $applyId            = $landInfoArr['data']['apply_did'];
                    $bizShow            = new BizShow();
                    $oldVenesSeasonInfo = $bizShow->getRoundInfoByVenusIdAndDate($applyId, $venusId, $date, $date);
                    if ($oldVenesSeasonInfo['code'] == 200 && !empty($oldVenesSeasonInfo['data'][$date])) {
                        foreach ($oldVenesSeasonInfo['data'][$date] as $roundItem) {
                            $roundList[$item['tid']][] = [
                                'begin'   => $roundItem['bt'],
                                'end'     => $roundItem['et'],
                                'zoneId'  => $item['zoneId'],
                                'roundId' => $roundItem['id'],
                                'venusid' => $roundItem['venus_id'],
                                'tname'   => $tname[$item['tid']],
                                'rname'   => $roundItem['round_name'],
                                'sortId'  => $roundItem['round_sort_id'],
                            ];
                        }
                    } else {
                        $emptyRoundList[] = $item['tname'];
                    }
                }
            }

            if (!empty($emptyRoundList)) {
                $emptyTickets = implode(',', $emptyRoundList);
                $this->apiReturn(203, [], "当天子票{$emptyTickets}存在无场次,请先添加场次");
            }

            $data = [
                'timeShareList' => $ticketSectionList,
                'roundList'     => $roundList,
            ];

            $this->apiReturn(200, $data, '获取成功');
        } else {
            $this->apiReturn(203, [], '主票id不可为空');
        }

    }

    /**
     * 获取获取门票规格类型（单规格-景区无分时、套票子票都是景区无分时；多规格-演出，套票子票含分时/演出）
     *
     * @param  int post.parant_id 套票id
     * @param  string post.aid 供应商Id
     *
     * @return array is_single 0-多规格 1-单规格
     */
    public function getTicketSkuType()
    {
        $tid = I('post.tid', '', 'intval');
        $lid = I('post.lid', '', 'intval');
        if (empty($tid)) {
            $this->apiReturn(203, [], '主票id不可为空');
        }

        $packageBiz       = new PackRelation();
        $ticketBiz        = new \Business\JavaApi\CommodityCenter\Ticket();
        $ticketSectionBiz = new TimeShareTicketSection();
        // 获取门票属性
        $ticketInfo = $ticketBiz->queryTicketInfoById($tid);
        if ($ticketInfo['code'] != 200 || empty($ticketInfo['data'])) {
            throw new \Exception("产品数据查询失败!");
        }
        $pType = $ticketInfo['data']['uuLandDTO']['p_type'];

        if ($pType == 'H') { // 演出走多规格流程
            $isSingle = 0;
        }
        if ($pType == 'A') { // 景区判断逻辑
            $isSingle = 1; // 景区未开启分时则为单规格
            // 是否开启分时预约
            $sectionEnable = $ticketSectionBiz->querySectionEnableOfTicket($lid,$tid);
            if (isset($sectionEnable['data']) && $sectionEnable['data'] === true) {
                // 开启分时则为多规格
                $isSingle = 0;
            }
        }

        if ($pType == 'F') { // 套票判断逻辑
            $isSingle = 1; // 套票默认单规格
            // 获取套票下子票列表
            $packageInfo  = $packageBiz->getPackageTicketAttrList($tid);
            if ($packageInfo['code'] == 200 && !empty($packageInfo['data'])) {
                $tidArr         = array_column($packageInfo['data'], 'tid');
                $ticketAttrList = $ticketBiz->queryTicketInfoByIds($tidArr);
                $tname          = [];
                foreach ($ticketAttrList['data'] as $attrItem) {
                    if ($attrItem['uuLandDTO']['p_type'] == "A") {
                        // 景区类型判断是否开启分时
                        $sectionEnable = $ticketSectionBiz->querySectionEnableOfTicket($attrItem['uuJqTicketDTO']['landid'],$attrItem['uuJqTicketDTO']['id']);
                        if (isset($sectionEnable['data']) && $sectionEnable['data'] === true) {
                            // 开启分时则为多规格
                            $isSingle = 0;
                            break;
                        }
                    }
                    if ($attrItem['uuLandDTO']['p_type'] == "H") {
                        // 演出类型直接返回多规格
                        $isSingle = 0;
                        break;
                    }
                }


            } else {
                $this->apiReturn(203, [], '主票id不可为空');
            }
        }

        if (!isset($isSingle)) {
            $this->apiReturn(203, [], '票类型 '.$pType.' 不支持该业务');
        }

        $this->apiReturn(200, ['p_type' => $pType, 'is_single' => $isSingle], '获取成功');
    }

    /**
     * 获取景区票分时段
     *
     * @return void
     * Author : jinshansan
     * Date : 2023/8/16
     */
    public function getSectionTimes()
    {
        $tid = I('tid','');
        $ticketSectionList = $this->getTicketSectionTimes($tid, 16);
        // 兼容历史微商城渠道
        if (empty($ticketSectionList)) {
            $ticketSectionList = $this->getTicketSectionTimes($tid, 1);
        }
        if (empty($ticketSectionList)) {
            $this->apiReturn(203, [], "当前门票未配置分时规则,请先添加分时配置");
        }
        $this->apiReturn(200, $ticketSectionList, '获取成功');
    }

    private function getTicketSectionTimes(int $tid, int $channel)
    {
        $timeShareBiz = new TimeShareOrder();
        $sectionDetail = $timeShareBiz->getTimeSlicesWithTidArr([$tid], date('Y-m-d'), $channel); // 1-微商城 16-ota渠道
        $ticketSectionList = [];
        if ($sectionDetail['code'] == 200 && !empty($sectionDetail['data'])) {
            foreach ($sectionDetail['data'] as $tid => $timeSectionItem) {
                if (!empty($timeSectionItem['time_slices'])) {
                    foreach ($timeSectionItem['time_slices'] as $timesectionDet) {
                        $ticketSectionList[] = [
                            'begin'   => $timesectionDet['begin'],
                            'end'     => $timesectionDet['end'],
                            'id'      => $timesectionDet['id'],
                        ];
                    }
                }
            }
        } else {
            $this->apiReturn(203, $sectionDetail, '分时数据获取失败');
        }
        return $ticketSectionList;
    }

    /**
     * 根据景点id获取绑定的系统的版本号
     */
    private function _getSysVersion($lid)
    {
        $otaQueryModel = new \Model\Ota\OtaQueryModel();
        $coopInfo      = $otaQueryModel->getCsysid($lid);
        $csysId        = $coopInfo['csysid'];

        // 获取系统的版本号
        $sysConfigModel   = new \Model\Ota\SysConfig();
        $configField      = 'id, version';
        $sysConfigInfoArr = $sysConfigModel->getSysConfigInfo($csysId, $configField);

        return empty($sysConfigInfoArr['version']) ? '' : $sysConfigInfoArr['version'];
    }

    /**
     * rpc 请求
     *
     * <AUTHOR>
     * @date 2022-08-31
     *
     * @param  string  $method
     * @param  array  $rpcData
     *
     * @return array
     */
    protected function callRpc(string $method, array $rpcData): array
    {
        $result = [
            'code' => 200,
            'msg'  => 'success',
            'data' => [],
        ];
        try {
            $lib            = new PftRpcClient('open_platform_api');
            $jsonRpcRes     = $lib->call($method, [
                $rpcData,
            ], 'admin_service');
            $result['data'] = $jsonRpcRes['data'];
            $result['code'] = $jsonRpcRes['code'];
            $result['msg']  = $jsonRpcRes['msg'];
        } catch (Throwable $e) {
            $result['code'] = $e->getCode();
            $result['msg']  = $e->getMessage();
        }

        return $result;
    }
}