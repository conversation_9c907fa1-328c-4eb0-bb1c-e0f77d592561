<?php
/**
 * 对接分销第三方配置管理
 *
 * <AUTHOR>
 * @date   2017-07-13
 */

namespace Controller\Ota;

use Business\JavaApi\Product\TimeShareTicketSection;
use Business\Ota\DownstreamConfig\Config as ConfigBusiness;
use Library\Constants\DockerModeConst;
use Library\Controller;
use \Business\Ota\SystemConfig as BizOtaConf;
use Model\Ota\AliMemberConf;
use Model\Ota\PftConConf;
use Model\Ota\SysConfig;
use Model\Product\Ticket;
use Business\JavaApi\CommodityCenter\Ticket as TicketBiz;
use Model\Ota\UpStreamRelationModel;
use Business\AppCenter\ModuleCommon;
use Business\SmsDiy\Manage;
use Model\SmsDiy\SmsDiyApply;

class SystemConfig extends Controller
{
    private $_memberInfo = [];
    private $_sid;
    private $_memberId;

    // 特殊的对接系统
    private $_specialCsy = [
        5,      // 天时同城
        7,      // 自我游
        196,    // 畅游通2.0
        228,    // 环企  生产228
        278,    // 鑫海慧科 生产278
        294,    // 票在旅途2.0 本地的 268
    ];

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 获取有权限操作的对接系统id列表
     *
     * @param  int  $sid  主帐号id
     *
     * @return array 对接系统id列表 [1,2,3,4,....]
     */
    private function _getAuthSystem($sid)
    {
        //获取业务层
        $bizOtaConf = new BizOtaConf();
        $res        = $bizOtaConf->getAuthSystem($sid);
        if ($res['code'] != 200) {
            return [];
        } else {
            return $res['data'];
        }
    }

    /**
     * 获取对接系统配置
     *
     * @param  int post.lid 景区id （可选，若有lid则优先查询是否已有绑定在该景区的系统）
     */
    public function getSystem()
    {
        $sid = $this->_sid;
        //处理入参
        $lid = I('post.lid', 0, 'intval');

        //默认已绑定的对接系统(在传入lid情况下，该值才有可能被改变)
        $bindCsys = 0;

        //统一获取模型
        $sysConfigModel = new SysConfig();

        //获取有权限对接的系统id
        $csysIdArr = $this->_getAuthSystem($sid);

        if (!$csysIdArr) {
            $this->apiReturn(400, [], '无可对接系统');
        }

        if ($lid) {
            //若有景区id，查询现有绑定对接系统
            $csysIdRes = $sysConfigModel->getCsysByLid($lid, 'csysid, notice_code, sms');
            if ($csysIdRes === false) {
                $this->apiReturn(400, [], '对接系统查询错误');
            }

            //已有绑定的对接系统id，进行检测是否有权限
            //已绑定系统id
            $bindCsys = !empty($csysIdRes['csysid']) ? $csysIdRes['csysid'] : 0;
            //判断权限
            if (in_array($bindCsys, $csysIdArr)) {
                //已绑定的系统在授权列表中，则直接替换
                $csysIdArr = [$bindCsys];
            } else {
                //已绑定了对接系统，但是却不在该用户可对接列表中
                if ($bindCsys) {
                    $this->apiReturn(400, [], '该产品配置对接的系统现无权使用');
                }
            }
        }

        //根据id查询对接系统
        $csysField   = 'id, name, coopB, account_name, password_name, encoding_products, notice_code, ext';
        $csysInfoRes = $sysConfigModel->getCsysInfoByids($csysIdArr, $csysField);
        if (!$csysInfoRes) {
            $this->apiReturn(400, [], '对接系统查询失败');
        }

        unset($csysIdArr);
        //提取查询出的对接系统id列表
        $csysIdArr = array_column($csysInfoRes, 'id');

        // 2018-7-25 zhujb  如果是对接天时同城，乐乐酷 从 pft_csy_lid_config 中获取配置信息
        // 获取  属于特殊系统的 csyId  并将  特殊系统id 从 $csysIdArr 中剔除
        $specialCsysIdArr = [];
        foreach ($csysIdArr as $k => $v) {
            if (in_array($v, $this->_specialCsy)) {
                unset($csysIdArr[$k]);
                $specialCsysIdArr[] = $v;
            }
        }

        $csysAuthDetailsOne = $sysConfigModel->getAccSysAuthDetailByCsysIds($sid, $csysIdArr);   // 普通对接系统
        $csysAuthDetailsTwo = $sysConfigModel->getCsysLidConfigList($specialCsysIdArr, $lid, $sid);    // 特殊对接系统

        $temp = array_merge($csysAuthDetailsOne, $csysAuthDetailsTwo);

        $csysAuthDetails = [];
        foreach ($temp as $k => $v) {
            $csysAuthDetails[$v['DockingMode']] = $v;
        }

//        //获取具体对接系统中已配置的帐号密码信息
//        $csysAuthDetails = $sysConfigModel->getAccSysAuthDetailByCsysIds($sid, $csysIdArr);

        //获取对接系统通知地址
        $csysUrlRes = $sysConfigModel->getSysNoticeUrl($csysIdArr);
        if ($specialCsysIdArr) {
            $specitalCsysUrlRes = $sysConfigModel->getSysNoticeUrl($specialCsysIdArr);
            if ($specitalCsysUrlRes) {
                $csysUrlRes = array_merge($csysUrlRes, $specitalCsysUrlRes);
            }
        }

        $csysNoticeTypeList = [
            0 => '订单确认通知',
            1 => '退款通知',
            2 => '消费通知',
            3 => '订单推送请求',
        ];

        //对接系统通知地址，格式 ['系统id' => ['通知类型'=>数据， '通知类型'=>数据], '系统id' => ['通知类型'=>数据， '通知类型'=>数据], ...]
        $csysUrls = [];

        if ($csysUrlRes) {
            foreach ($csysUrlRes as $row) {
                $tmpCsys                   = $row['notify_sys'];
                $type                      = $row['notify_type'];
                $csysUrls[$tmpCsys][$type] = [
                    'type' => $type,
                    'url'  => $row['notify_url'],
                    'desc' => !empty($csysNoticeTypeList[$type]) ? $csysNoticeTypeList[$type] : '无',
                ];
            }
        }

        //对接系统详细信息列表
        $csysList = [];

        //构建对接系统列表信息，以及每个对接系统的具体内容
        foreach ($csysInfoRes as $csysInfo) {
            //组合对接系统配置信息与帐号信息
            $tmpId      = $csysInfo['id'];
            $authDetail = isset($csysAuthDetails[$tmpId]) ? $csysAuthDetails[$tmpId] : [];

            $identity   = isset($authDetail['supplierIdentity']) ? $authDetail['supplierIdentity'] : ''; //供应商通信标识
            $signkeyStr = isset($authDetail['signkey']) ? $authDetail['signkey'] : ''; //交互密钥
            //大于1 说明有附加字段
            $separator   = $this->_getseparator();
            $signkeyArr  = explode($separator, $signkeyStr);
            $signkey     = $signkeyArr[0] ?? '';
            $extValueStr = $signkeyArr[1] ?? '';
            $extValueArr = [];
            if (!empty($extValueStr)) {
                $extValueArr = json_decode($extValueStr, true);
            }
//var_dump($extValueStr);
            $ext = [];
            //$ext = [
            //    [
            //        'key'     => 'supplierIdentity',
            //        'name'    => $csysInfo['account_name'],
            //        'value'   => $identity,
            //        'type'    => 'text',
            //        'default' => '',
            //    ],
            //    [
            //        'key'     => 'signkey',
            //        'name'    => $csysInfo['password_name'],
            //        'value'   => $signkey,
            //        'type'    => 'text',
            //        'default' => '',
            //    ],
            //];
            //
            //
            //$csysInfo['ext'] = [
            //    [
            //        'name'    => '支付方式',
            //        'key'     => 'payMode',
            //        'type'    => 'select',
            //        'default' => [
            //            '1' => '备佣金',
            //            '2' => '智游宝支付',
            //            '3' => '现场支付',
            //        ],
            //    ],
            //];
            //
            //$csysInfo['ext'] = json_encode($csysInfo['ext']);
            //echo $csysInfo['ext'];
            if (!empty($csysInfo['ext'])) {
                $extArr = json_decode($csysInfo['ext'], true);
                foreach ($extArr as $v) {
                    $values = '';
                    if (!empty($extValueArr)) {
                        $values = $extValueArr[$v['key']] ?? '';
                    }
                    $ext[] = [
                        'key'     => $v['key'],
                        'name'    => $v['name'],
                        'value'   => $values,
                        'type'    => $v['type'],
                        'default' => $v['default'],
                    ];
                }
            }

            //组合参数
            $tmpList = [
                'name'              => $csysInfo['name'],
                'csys'              => $tmpId,
                'coopB'             => $csysInfo['coopB'],
                'account_name'      => $csysInfo['account_name'],
                'password_name'     => $csysInfo['password_name'],
                'encoding_products' => $csysInfo['encoding_products'],
                'identity'          => $identity,
                'signkey'           => $signkey,
                'notice_info'       => isset($csysUrls[$tmpId]) ? $csysUrls[$tmpId] : '',
                'is_bind'           => $bindCsys == $tmpId ? 1 : 0,
                'select_first'      => $this->_selectFirstWhat($tmpId), // 是否选择景点的
                'sys_notice_code'   => $csysInfo['notice_code'] ?: 0,   // 系统级得异步同步设置
                'send_sms'          => $csysIdRes['sms'] ?? 1,   // 系统级得异步同步设置
                'show_delay_check'  => $csysInfo['coopB'] == 17 ? true : false, // 九天达需要显示是否验证的属性
                'land_notice_code'  => isset($csysIdRes['notice_code']) ? $csysIdRes['notice_code'] : 0,  // 景点级得异步同步
                'grasp_product'     => in_array($tmpId, [73, 80]) ? 1 : 0,
                'ext'               => $ext,
            ];

            $csysList[] = $tmpList;
        }

        $this->apiReturn(200, $csysList, '');
    }

    /**
     * 获取ota 配置信息getTickets
     */
    public function getOtaSystem()
    {
        $sid    = $this->_sid;
        $coopId = I('post.coopId', 0, 'intval');

        if (empty($coopId)) {
            $this->apiReturn(400, [], '请求参数错误');
        }

        //统一获取模型
        $sysConfigModel = new SysConfig();

        $res = $sysConfigModel->getOtaSystemConfig($coopId, $sid);
        if ($res) {
            $this->apiReturn(200, $res, '');
        }
        $this->apiReturn(200, [], '暂无数据');
    }

    /**
     * 根据系统id 返回是否先 选择景点 然后选择门票
     *
     * @param  int  $csys  景点id
     *
     * @return int 1:景点  0: 直接门票
     *
     */
    private function _selectFirstWhat($csys)
    {
        $poiCsys = [80];

        if (in_array($csys, $poiCsys)) {
            return 1;
        }

        return 0;
    }

    /**
     * 修改对接系统配置
     *
     * @param  int post.csys 对接系统id
     * @param  string post.identity 身份帐号信息
     * @param  string post.signkey 密钥安全信息
     * @param  string post.ext 扩展属性
     */
    public function modSystem()
    {
        $sid = $this->_sid;
        //处理入参
        //对接系统id（DockingMode）
        $csysId   = I('post.csys', 0, 'intval');
        $identity = I('post.identity', '', 'strval');
        $signkey  = I('post.signkey', '', 'strval');
        $lid      = I('post.lid', '', 'strval');
        $ext      = I('post.ext', '');
        $identity = trim($identity);
        $signkey  = trim($signkey);

        if ($csysId != 90) {
            $this->apiReturn(12301, [], '线下票务系统升级中');
        }
        
        if (!$csysId) {
            $this->apiReturn(400, [], '对接系统ID错误');
        }

        if (!$identity || !$signkey) {
            $this->apiReturn(400, [], '参数错误');
        }

        //扩展参数校验
        if (!empty($ext)) {
            foreach ($ext as $v) {
                if (empty($v)) {
                    $this->apiReturn(400, [], '扩展参数错误');
                }
            }
            $extStr = json_encode($ext);
            if (!empty($extStr)) {
                //获取分隔符
                $separator = $this->_getseparator();
                $signkey   = $signkey . $separator . $extStr;
            }
        }

        // 如果是马蜂窝, 不判断对接权限
        if ($csysId !== 90) {
            //获取有权限对接的系统id
            $csysIdArr = $this->_getAuthSystem($sid);
            if (!in_array($csysId, $csysIdArr)) {
                $this->apiReturn(400, [], '无权限对接此系统');
            }
        }

        //统一获取模型
        $sysConfigModel = new SysConfig();

        $csysField   = 'id, coopB';
        $csysInfoRes = $sysConfigModel->getCsysInfoByids($csysId, $csysField);
        if (!$csysInfoRes) {
            $this->apiReturn(400, [], '对接系统查询失败');
        }

        //对接系统coopB
        $coopB = $csysInfoRes[0]['coopB'];

        //存储的数据
        $data = [
            'fid'              => $sid,
            'supplierIdentity' => $identity,
            'signkey'          => $signkey,
            'coop_id'          => $coopB,
            'DockingMode'      => $csysId,
            'cooperation_way'  => 0,
        ];

        // 兼容马蜂窝ota配置,判断通信标识符是否被占用
        if ($csysId == 90) {
            //根据fid和dockingMode定位已有配置信息
            $isExist = $sysConfigModel->findQunarInfoByModeAndFid($sid, $csysId);
            if ($isExist) {
                $info = $sysConfigModel->findQunarInfoByIdentityAndMode($identity, $csysId);
                if (!empty($info) && $info['fid'] != $sid) {
                    $this->apiReturn(400, [], '系统标识符已被占用');
                }
            } else {
                $info = $sysConfigModel->findQunarInfoByIdentityAndMode($identity, $csysId);
                if ($info) {
                    $this->apiReturn(400, [], '系统标识符已被占用');
                }
            }
            // 马蜂窝修改做解绑操作
            $this->unBindProduct(['dockerMode' => DockerModeConst::MAFENGWO]);
        }

        // 2018-7-25  保存天时同城，乐乐酷这些特殊的对接系统  需要前端多传一个 lid
        if (in_array($csysId, $this->_specialCsy)) {
            $saveRes = $sysConfigModel->saveCsyLidConfig($sid, $lid, $csysId, $identity, $signkey);
        } else {
            //更新配置
            $saveRes = $sysConfigModel->saveAccSysAuthDetail($data);
        }

        if ($saveRes === false) {
            $this->apiReturn(400, [], '更新信息失败');
        } else if ($saveRes === 0) {
            $this->apiReturn(400, [], '配置信息无变化');
        } else {
            $this->apiReturn(200, [], '更新成功');
        }
    }

    /**
     * 分隔符定义
     *
     * @return string
     * <AUTHOR>
     * @date 2021-06-19 10:42
     */
    private function _getseparator()
    {
        return "|-_-|";
    }

    /**
     * 绑定门票
     *
     * @param  int post.csys 对接系统id
     * @param  string post.tid 门票id
     * @param  string post.uuid 第三方产品编码
     */
    public function bindTicket()
    {
        $sid  = $this->_sid;
        $opId = $this->_memberId;
        //处理入参
        //对接系统id（DockingMode）
        $csysId = I('post.csys', 0, 'intval');
        //门票id
        $tid = I('post.tid', 0, 'intval');
        //短信模板id
        $templateId = I('post.template_id', 0, 'intval');
        //第三方产品编码
        if ($csysId == 91) {
            $uuid = I('post.uuid');
        } else {
            $uuid = I('post.uuid', 0, 'strval');
        }

        // 返码方式  1:同步发码 2:异步发码 3:不发码
        $noticeCode = I('post.notice_code', 0, 'intval');
        // 发送短信  1:不发短信 2:代发短信
        $sendSms = I('post.send_sms', 1, 'intval');
        // 下单可验证：1  需要延迟验证：2
        $delayCheck = I('post.delay_check', 1, 'intval');
        // 延迟验证秒数
        $delayCheckTime = I('post.delay_check_time', 0, 'intval');
        //获取业务层
        $bizOtaConf = new BizOtaConf();
        //绑定门票
        $res = $bizOtaConf->bindCsysToTicket($sid, $csysId, $tid, $uuid, $noticeCode, $opId, $sendSms, $templateId, $delayCheck, $delayCheckTime);

        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 解绑门票
     *
     * @param  string post.tid 门票id
     */
    public function unBindTicket()
    {
        $sid  = $this->_sid;
        $opId = $this->_memberId;
        //处理入参
        //门票id
        $tid = I('post.tid', 0, 'intval');
        pft_log('ota/unbind',
            json_encode(['action'   => 'unBindTicket',
                         'sid'      => $this->_sid,
                         'tid'      => $tid,
                         'memberID' => $this->_memberId,
            ]));
        //获取业务层
        $bizOtaConf = new BizOtaConf();
        //解绑门票
        $res = $bizOtaConf->unBindCsysFromTicket($sid, $tid, $opId);

        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 解绑景区
     *
     * @param  string post.lid 景区id
     */
    public function unBindLand()
    {
        $sid  = $this->_sid;
        $opId = $this->_memberId;
        //处理入参
        //门票id
        $lid = I('post.lid', 0, 'intval');
        pft_log('ota/unbind',
            json_encode(['action'   => 'unBindLand',
                         'sid'      => $this->_sid,
                         'lid'      => $lid,
                         'memberID' => $this->_memberId,
            ]));
        //获取业务层
        $bizOtaConf = new BizOtaConf();
        //解绑景区
        $res = $bizOtaConf->unBindCsysFromLand($sid, $lid, $opId);

        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 获取在售产品景区列表
     *
     * @param  string post.keyword 景区关键字
     * @param  string post.page 页码
     * @param  string post.size 条数
     */
    public function getOnLandList()
    {
        $sid = $this->_sid;

        // 增加判断是否是管理员账号
        $isSuper = $this->isSuper();

        if ($isSuper) {
            // 如果是管理员 不限制账号搜索
            $sid = 0;
        }

        //处理入参
        //景区关键字
        $keyword = I('post.keyword', '', 'strval');
        //页码
        $page = I('post.page', 1, 'intval');
        //条数
        $size = I('post.size', 10, 'intval');

        //获取业务层
        $bizOtaConf = new BizOtaConf();

        $res = $bizOtaConf->getOnLandList($sid, $keyword, $page, $size);
        if ($res['code'] != 200) {
            //失败
            $this->apiReturn(400, [], $res['msg']);
        } else {
            //成功
            $this->apiReturn(200, $res['data'], $res['msg']);
        }
    }

    /**
     * 获取景区的门票
     *
     * @param  int post.lid 景区id
     * @param  int post.type 查询类型  0-查询全部， 1-查询绑定
     */
    public function getTickets()
    {
        $new = true;
        if ($new) {
            $res = $this->_getNewTickets();
            $this->apiReturn(200, $res, '');
        }

        $sid = $this->_sid;
        //景区id
        $lid = I('post.lid', 0, 'intval');
        //查询类型  0-查询全部， 1-查询绑定
        $type = I('post.type', 0, 'intval');

        //是否查询绑定 true-查询绑定， false-不查询
        $isBind     = $type ? true : false;
        $handleType = $type ? 'bind' : 'all';

        if (!$lid) {
            $this->apiReturn(400, [], ' 景区id错误');
        }


        if ($isBind) {
            // 调用java 接口获取 已经绑定的列表
            //获取业务层
            $bizOtaConf    = new BizOtaConf();
            $bindTicketArr = $bizOtaConf->bindTicketList($lid);
            if ($bindTicketArr['code'] != 200) {
                $this->apiReturn(400, [], '无匹配门票');
            }

            $ticketRes        = $bindTicketArr['data'];
            $allBindTicketArr = $ticketRes;
        } else {
            // 查找所有的
            $ticketModel = new Ticket();

            //获取在售的产品
            $productList = $ticketModel->getSaleProducts($sid);
            //在售产品id
            $pids = array_keys($productList);

            //查询景区门票
            $javaApi   = new \Business\CommodityCenter\Ticket();
            $ticketArr = $javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable('id, title', '', '', '', false,
                null, [$lid], null, $pids);
            $ticketRes = array_column($ticketArr, 'ticket');

            if (!$ticketRes) {
                $this->apiReturn(400, [], '无匹配门票');
            }

            // 获取已经被绑定的门票数据
            $bizOtaConf       = new BizOtaConf();
            $bindTicketArr    = $bizOtaConf->bindTicketList($lid);
            $allBindTicketArr = [];
            if ($bindTicketArr['code'] == 200) {
                $allBindTicketArr = $bindTicketArr['data'];
            }
        }

        // 调用数据处理
        $ticketList = $this->_handleListData($ticketRes, $handleType, $allBindTicketArr);

        $this->apiReturn(200, $ticketList, '');
    }

    public function _getNewTickets()
    {
        //景区id
        $lid = I('post.lid', 0, 'intval');
        //查询类型  0-查询全部， 1-查询绑定
        $type = I('post.type', 0, 'intval');

        //分页处理
        $page = I('post.page', 1, 'intval');
        $size = I('post.size', 10, 'intval');
        //景区名称搜索
        $title = I('post.title', '', 'strval');

        if (!$lid) {
            $this->apiReturn(400, [], '景区id错误');
        }

        $sid         = $this->_sid;
        $ticketModel = new \Model\Product\Ticket('slave');

        // 查询已绑定的门票
        $bizOtaConf    = new BizOtaConf();
        $bindTicketRes = $bizOtaConf->bindTicketList($lid);
        if ($bindTicketRes['code'] != 200) {
            $this->apiReturn(400, [], '无匹配门票');
        }
        $allBindTicketArr = $bindTicketRes['data'];

        $total = 0;

        $ticketList = [];
        if ($type) {
            if (!empty($allBindTicketArr)) {
                // 只查询绑定的门票
                $ticketIdArr = array_column($allBindTicketArr, 'ticketId');
                $ticketData  = $ticketModel->getTicketInfoMulti($ticketIdArr, 'id,title');

                foreach ($allBindTicketArr as $item) {
                    $ticketList[] = [
                        'tid'    => $item['ticketId'],
                        'ttitle' => $ticketData[$item['ticketId']],
                        'uuid'   => $item['uuid'],
                    ];
                }
                $total = count($ticketIdArr);
            }
        } else {
            // 查找所有的
            $ticketModel = new Ticket();

            //获取在售的自供应产品
            $productBiz = new \Business\Product\Product();
            $evoluteArr = $productBiz->getSaleProductByCondition($sid, $sid, $lid, 0, $title, [], null, [], null, $page,
                $size);
            if (!isset($evoluteArr['list']) || !is_array($evoluteArr['list'])) {
                $this->apiReturn(400, [], '无匹配门票');
            }

            //获取在售门票信息
            $tidArr    = array_column($evoluteArr['list'], 'ticket_id');
            $ticketRes = $ticketModel->getTicketInfoByIdArr($tidArr, 'id,title');
            if (empty($ticketRes)) {
                $this->apiReturn(400, [], '无匹配门票');
            }

            $total = $evoluteArr['total'];

            $allTicketToUUidArr = [];
            if (!empty($allBindTicketArr)) {
                foreach ($allBindTicketArr as $bindTicket) {
                    $allTicketToUUidArr[$bindTicket['ticketId']] = $bindTicket['uuid'];
                }
            }

            foreach ($ticketRes as $item) {
                $ticketList[] = [
                    'tid'    => $item['id'],
                    'ttitle' => $item['title'],
                    'uuid'   => $allTicketToUUidArr[$item['id']] ?? '',
                ];
            }
        }

        $return = [
            'list'  => $ticketList,
            'total' => $total,
        ];

        $this->apiReturn(200, $return, '');
    }

    /**
     * 列表查询数据处理
     * <AUTHOR>
     * @date   17-08-22
     *
     */
    private function _handleListData($list, $type = 'all', $allBindTicketArr = [])
    {
        if (!$list || !$type) {
            return [];
        }

        $ticketList = [];
        if ($type == 'all') {
            $allBindTicketArrTidKey = [];
            if (!empty($allBindTicketArr)) {
                foreach ($allBindTicketArr as $item) {
                    $allBindTicketArrTidKey[$item['ticketId']] = [
                        'uuid' => $item['uuid'],
                    ];
                }
            }

            foreach ($list as $tic) {
                $ticketList[] = [
                    'tid'    => $tic['id'],
                    'ttitle' => $tic['title'],
                    'uuid'   => isset($allBindTicketArrTidKey[$tic['id']]) ? $allBindTicketArrTidKey[$tic['id']]['uuid'] : '',
                ];
            }
        } elseif ($type == 'bind') {

            $ticketIdArr = array_column($list, 'ticketId');
            //进行查询
            $ticketModel = new \Model\Product\Ticket();
            $ticketRes   = $ticketModel->getTicketInfoMulti($ticketIdArr, 'id, title');

            foreach ($list as $tic) {
                $ticketList[] = [
                    'tid'    => $tic['ticketId'],
                    'ttitle' => $ticketRes[$tic['ticketId']],
                    'uuid'   => $tic['uuid'],
                ];
            }
        }

        return $ticketList;
    }

    /**
     * 用户对接系统列表
     * 统计了上个月对接订单数量和当天对接产品数量
     * @return string
     *
     */
    public function userSystemList()
    {
        return $this->userSystemListNew();
    }

    /**
     * 用户对接系统列表
     * 统计了上个月对接订单数量和当天对接产品数量
     * @return string
     *
     */
    public function userSystemListNew()
    {
        $sid      = $this->_sid;
        $page     = I('post.page', 1);
        $pageSize = I('post.page_size', 10);

        $beginDate = date('Y-m-01', strtotime('-1 month'));
        $endDate   = date('Y-m-t', strtotime('-1 month'));

        $otaStatistics = new \Business\Ota\OtaStatistics();
        //获取所有的景区列表
        $sysOrderListArr = $otaStatistics->getSystemList($sid, $page, $pageSize);
        $sysOrderList    = $sysOrderListArr['data'];

        //统计授权的对接的第三方系统列表总数
        $sysNumArr = $otaStatistics->countAuthSystemNum($sid);
        $sysNum    = $sysNumArr['data']['num'];

        $sysListArr = [];
        foreach ($sysOrderList as $key => $item) {
            $csyId                         = $item['id'];
            $landNum                       = $otaStatistics->getCountSystemLandNum($sid, $csyId);
            $orderNum                      = $otaStatistics->getLastMonthOrder($sid, $beginDate, $endDate, $csyId);
            $sysListArr[$key]              = $item;
            $sysListArr[$key]['land_num']  = $landNum;
            $sysListArr[$key]['order_num'] = $orderNum;

            if (ENV == 'PRODUCTION') {
                $pullProductCSysList = [7, 20, 68, 73, 80]; // 拉取第三方产品
                $csysArr             = [207, 226]; //拉取日历价格库存
                $pullCsysArr         = [211, 212, 226, 248]; //拉取分时
                $pullV1SecArr        = [237, 147, 174, 228]; //拉取通用接口上游演出场次
            } elseif (ENV == 'TEST') {
                $pullProductCSysList = [7, 20, 68, 73, 80]; // 拉取第三方产品
                $csysArr             = [203, 217];
                $pullCsysArr         = [207, 211, 217, 223];
                $pullV1SecArr        = [227, 173]; //拉取通用接口上游演出场次
            } else {
                $pullProductCSysList = [7, 20, 68, 73, 80]; // 拉取第三方产品
                $csysArr             = [222, 228];
                $pullCsysArr         = [224, 225, 228, 232];
                $pullV1SecArr        = [233, 202, 230]; //拉取通用接口上游演出场次
            }

            if (in_array($item['id'], $csysArr)) {
                $sysListArr[$key]['pull'] = 1;
            } else {
                $sysListArr[$key]['pull'] = 0;
            }

            if (in_array($item['id'], $pullCsysArr)) {
                $sysListArr[$key]['pull_time_storage'] = 1;
            } else {
                $sysListArr[$key]['pull_time_storage'] = 0;
            }

            if (in_array($item['id'], $pullV1SecArr)) {
                $sysListArr[$key]['sessionsync'] = 1;
            } else {
                $sysListArr[$key]['sessionsync'] = 0;
            }

            if (in_array($item['id'], $pullProductCSysList)) {
                $sysListArr[$key]['grasp'] = 1;
            } else {
                $sysListArr[$key]['grasp'] = 0;
            }
        }

        $returnDataArr = ['total' => $sysNum, 'list' => $sysListArr];

        $this->apiReturn(200, $returnDataArr, 'success');
    }

    /**
     * 获取系统描述
     *
     * Author : liucm
     * Date : 2021/11/27
     */
    public function getSysDesc()
    {
        $sysId          = I('post.sys_id', '', 'intval');
        $sysConfigModel = new PftConConf();
        $descArr        = $sysConfigModel->getCsysDescByid($sysId, 'create_time,sys_desc, question_desc');

        if (!empty($descArr)) {
            $createTime = '-';
            if (!empty($descArr['create_time'])) {
                $createTime = date('Y-m-d', $descArr['create_time']);
            }
            $returnDataArr = [
                'create_time'   => $createTime,
                'sys_desc'      => $descArr['sys_desc'],
                'question_desc' => $descArr['question_desc'],
            ];
            $this->apiReturn(200, $returnDataArr, 'success');
        }
        $this->apiReturn(200, [], '暂无描述');
    }

    /**
     * 绑定有赞店铺id获取信息
     * @return array
     */
    public function getYouZanKdtIdInfo()
    {
        $sid                 = $this->_sid;
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $youzanTokenInfoArr  = $otherSysConfigModel->getYouZanTokenByMemberIdList($sid);
        if (empty($youzanTokenInfoArr)) {
            $this->apiReturn(205, [], '无数据');
        }

        // 整理数据成字符串
        $kdtIdStr = '';
        if (is_array($youzanTokenInfoArr)) {
            $kdtIdArr = array_column($youzanTokenInfoArr, 'kdt_id');
            $kdtIdStr = implode(',', $kdtIdArr);
        }

        $this->apiReturn(200, ['kdt_id' => $kdtIdStr], 'success');
    }

    /**
     * 绑定有赞店铺id
     * @return array
     */
    public function setYouZanKdtIdToPft()
    {
        $kdtId = I('post.kdt_id');
        $sid   = $this->_sid;
        if (empty($kdtId)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $field = 'id,access_token,refresh_token,expires_time,member_id';
        $youzanTokenInfoArr  = $otherSysConfigModel->getYouZanTokenByKdtId($kdtId, $field);
        if (empty($youzanTokenInfoArr)) {
            $this->apiReturn(203, [], '请先在有赞平台完成应用购买授权操作');
        }

        if (!empty($youzanTokenInfoArr['member_id'])) {
            $this->apiReturn(203, [], '该有赞店铺kdt_id已绑定');
        }

        $res = $otherSysConfigModel->editYouZanTokenMemberId($kdtId, $sid);
        $otherSysConfigModel->insertYouZanTokenLog($kdtId, $youzanTokenInfoArr['id'], 1, $this->_sid, $this->_memberId, 1);
        if ($res) {
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    /**
     * 绑定携程接口账号秘钥等信息
     *
     */
    public function setCtripKeyConf()
    {
        $ctripAccountId = I('post.ctrip_account_id', '', 'strval');
        $aesKey         = I('post.aes_key', '', 'strval');
        $aesIv          = I('post.aes_iv', '', 'strval');
        $securyKey      = I('post.secury_key', '', 'strval');
        $sid            = $this->_sid;
        if (empty($ctripAccountId) || empty($aesKey) || empty($aesIv) || empty($securyKey)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $conf = $otherSysConfigModel->getCtripKeyByCtripAccountId($ctripAccountId);
        if (!empty($conf) && $conf['member_id'] != $sid) {
            $this->apiReturn(203, [], '账号已被使用，请使用其他账号');
        }

        $ctripKeyInfoArr     = $otherSysConfigModel->getCtripKeyByMemberId($sid);
        if (empty($ctripKeyInfoArr)) {
            $res = $otherSysConfigModel->insertCtripKey($sid, $ctripAccountId, $aesKey, $aesIv, $securyKey);
        } else {
            $res = $otherSysConfigModel->editCtripKeyByMemberId($sid, $ctripAccountId, $aesKey, $aesIv, $securyKey);
        }

        if ($res) {
            $this->unBindProduct(['dockerMode' => DockerModeConst::CTRIP]);
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    /**
     * 获取携程绑定配置信息
     *
     */
    public function getCtripKeyConf()
    {
        $sid                 = $this->_sid;
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $youzanTokenInfoArr  = $otherSysConfigModel->getCtripKeyByMemberId($sid);
        if (empty($youzanTokenInfoArr)) {
            $data = [
                'ctrip_account_id' => '',
                'aes_key'          => '',
                'aes_iv'           => '',
                'secury_key'       => '',
            ];
        } else {
            $data = [
                'ctrip_account_id' => $youzanTokenInfoArr['ctrip_account_id'],
                'aes_key'          => $youzanTokenInfoArr['aes_key'],
                'aes_iv'           => $youzanTokenInfoArr['aes_iv'],
                'secury_key'       => $youzanTokenInfoArr['secury_key'],
            ];
        }

        $this->apiReturn(200, $data, '绑定成功');
    }

    /**
     * 修改系统的发码值
     * param int $id 系统id
     * param int $noticeCodeType 发码类型
     * return string | json
     *
     */
    public function setConsNoticeCode()
    {
        if ($this->_sid != 1) {
            $this->apiReturn(204, [], '无权限操作');
        }

        $id             = I('request.id', 0);
        $noticeCodeType = I('request.notice_code_type', 0);

        if (empty($id)) {
            $this->apiReturn(204, [], '系统id错误');
        }

        $otaSysConfigModel = new \Model\Ota\SysConfig();
        $setRes            = $otaSysConfigModel->setCsysNoticeCodeById($id, $noticeCodeType);
        if ($setRes) {
            $this->apiReturn(200, [], '修改成功');
        }
        $this->apiReturn(205, [], '修改失败');
    }

    /**
     * 设置用户的京东信息
     *
     */
    public function setJDUserConf()
    {
        $appId     = I('post.identity');
        $secretKey = I('post.signkey');
        $sid       = $this->_sid;
        if (empty($appId) || empty($secretKey)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $jdConfArr           = $otherSysConfigModel->getJDConfByMemberId($sid);
        if (empty($jdConfArr)) {
            $res = $otherSysConfigModel->insertJDConf($sid, $appId, $secretKey);
        } else {
            $res = $otherSysConfigModel->editJDConfByMemberId($sid, $appId, $secretKey);
        }
        if ($res) {
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    /**
     * 获取用户的京东配置信息
     */
    public function getJDConfInfo()
    {
        $sid                 = $this->_sid;
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $jdConfArr           = $otherSysConfigModel->getJDConfByMemberId($sid);
        $returnList          = [];
        if (!empty($jdConfArr)) {
            $returnList['identity'] = $jdConfArr['app_id'];
            $returnList['signkey']  = $jdConfArr['secret_key'];
        }

        $this->apiReturn(200, $returnList, 'success');
    }

    // --------------- 阿里供销 -------------------//

    /**
     * 设置用户的阿里配置信息
     *
     */
    public function setAliUserConf()
    {
        $partnerId    = I('post.partner_id', '', 'trim');
        $accessSecret = I('post.access_secret', '', 'trim');
        $sid          = $this->_sid;
        $opid         = $this->_memberId;
        if (empty($partnerId) || empty($accessSecret)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $sysConfigModel      = new \Model\Ota\SysConfig();
        $aliConfArr          = $otherSysConfigModel->getAliConfByMemberId($sid);
        $partnerInfo         = $sysConfigModel->getAliMemberInfoByPartnerId($partnerId);
        if (!empty($partnerInfo)) {
            if ($partnerInfo['partner_id'] == $partnerId && $partnerInfo['member_id'] != $sid) {
                $this->apiReturn(205, [], '该ID已被使用,请使用其他ID');
            }
        }
        if (empty($aliConfArr)) {
            $res = $otherSysConfigModel->insertAliConf($sid, $partnerId, $accessSecret, $opid);
        } else {
            if ($aliConfArr['partner_id'] == $partnerId && $aliConfArr['access_secret'] == $accessSecret && $aliConfArr['opid'] == $opid && $aliConfArr['member_id'] == $sid) {
                $this->apiReturn(200, [], '绑定成功');
            }

            $res = $otherSysConfigModel->editAliConfByMemberId($sid, $partnerId, $accessSecret, $opid);
        }
        if ($res) {
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    /**
     * 保存预警信息
     *
     * Author : CR
     * Date : 2021-11-10
     */
    public function setAliMemberExpireNotice()
    {
        $status     = I('post.notice_status', 0, 'intval');
        $type       = I('post.notice_type', 0, 'intval');
        $mobile     = I('post.mobile', '');
        $noticeDays = I('post.notice_days', 0, 'intval');

        if (!isset($status) || empty($type)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        if (!is_numeric($noticeDays) || $noticeDays < 1) {
            $this->apiReturn(204, [], '提示日期不可小于1天');
        }

        $check = '/^(1(([35789][0-9])|(47)))\d{8}$/';
        if (!empty($mobile) && !preg_match($check, $mobile) && $type == 2) {
            $this->apiReturn(204, [], '请输入正确的手机号');
        }

        //手机号为空时,取账号绑定的默认手机号
        if (empty($mobile) && $type == 2) {
            $member = new \Model\Member\Member();
            $fdata  = $member->getMemberInfo($this->_sid);
            $mobile = $fdata['mobile'];
            if (empty($mobile)) {
                $this->apiReturn(204, [], '手机号为空且未绑定默认手机号');
            }
        }

        $model = new AliMemberConf();
        if (!$model->getAliMemberInfoByMemberId($this->_sid)) {
            $this->apiReturn(204, [], '账号未进行授权');
        }

        //通知方式为微信通知时,检查一下是否有绑定微信
        $wxMemberModel = new \Model\Wechat\WxMember();
        $openList      = $wxMemberModel->getOpenList($this->_sid, $page = 1, $size = 1);
        if ($openList == null && $type == 1 && $status == 1) {
            $this->apiReturn(204, [], '请先绑定微信');
        }

        $res = $model->saveAliMemberNoticeConf($this->_sid, $status, $type, $noticeDays, $mobile);
        pft_log('ota/ali_notice_conf',
            json_encode(['notice_status' => $status, 'notice_type' => $type, 'mobile' => $mobile, 'res' => $res]));
        if ($res === false) {
            $this->apiReturn(200, [], '配置失败');
        }

        $cache        = \Library\Cache\Cache::getInstance('redis');
        $sendCacheKey = "ali_market:expire:noticed:memberid:{$this->_sid}";
        $cache->del($sendCacheKey);

        pft_log('ota/ali_market/delCache', '重新配置成功删除缓存' . $this->_sid);

        $this->apiReturn(200, [], 'success');
    }

    /**
     * 获取用户的阿里配置信息
     */
    public function getAliConfInfo()
    {
        $sid                 = $this->_sid;
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $aliConfArr          = $otherSysConfigModel->getAliConfByMemberId($sid);
        $returnList          = [];
        if (!empty($aliConfArr)) {
            $returnList['partner_id']    = $aliConfArr['partner_id'];
            $returnList['access_secret'] = $aliConfArr['access_secret'];
            $returnList['member_id']     = $aliConfArr['member_id'];
            $returnList['notice_status'] = (int)$aliConfArr['notice_status'];
            $returnList['notice_type']   = (int)$aliConfArr['notice_type'];
            $returnList['mobile']        = $aliConfArr['mobile'];
            $returnList['notice_days']   = $aliConfArr['notice_days'];
            if (empty($aliConfArr['expire_time'])) {
                $returnList['expire_time'] = '';
            } else {
                $returnList['expire_time'] = '授权过期时间：' . date("Y-m-d H:i:s", $aliConfArr['expire_time']);
            }
        }

        $this->apiReturn(200, $returnList, 'success');
    }

    /**
     * 获取阿里供销授权
     *
     */
    public function getAliMarketAccessTokenUrl()
    {
        $memberInfoArr = $this->_memberInfo;
        $requestArr    = [
            'account' => $memberInfoArr['account'],
        ];

        if (!in_array($memberInfoArr['dtype'], [0, 1])) {
            $this->apiReturn(205, [], '需使用主账号登陆授权');
        }

        $result = \Library\Tools\Helpers::callGroupSystem(13, $requestArr, 'Get_MemberKey');
        if ($result['code'] == 200) {
            $this->apiReturn(200, $result['data'], 'success');
        }

        $this->apiReturn(205, [], $result['msg']);
    }

    // --------------- 美团圈圈 -------------------//

    /**
     * 设置用户的美团圈圈配置信息
     *
     */
    public function setMtqqUserConf()
    {
        $partnerId    = I('post.partner_id', '', 'trim');
        $accessSecret = I('post.access_secret', '', 'trim');
        $sid          = $this->_sid;
        $opid         = $this->_memberId;
        if (empty($partnerId) || empty($accessSecret)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $sysConfigModel      = new \Model\Ota\SysConfig();
        $mtqqConfArr         = $otherSysConfigModel->getMtqqConfByMemberId($sid);
        $partnerInfo         = $sysConfigModel->getMtqqMemberInfoByPartnerId($partnerId);
        if (!empty($partnerInfo)) {
            if ($partnerInfo['partner_id'] == $partnerId && $partnerInfo['member_id'] != $sid) {
                $this->apiReturn(205, [], '该ID已被使用,请使用其他ID');
            }
        }
        if (empty($mtqqConfArr)) {
            $res = $otherSysConfigModel->insertMtqqConf($sid, $partnerId, $accessSecret, $opid);
        } else {
            if ($mtqqConfArr['partner_id'] == $partnerId && $mtqqConfArr['access_secret'] == $accessSecret && $mtqqConfArr['opid'] == $opid && $mtqqConfArr['member_id'] == $sid) {
                $this->apiReturn(200, [], '绑定成功');
            }
            $res = $otherSysConfigModel->editMtqqConfByMemberId($sid, $partnerId, $accessSecret, $opid);
        }
        if ($res) {
            $this->unBindProduct(['dockerMode' => DockerModeConst::MEITUAN_QUANQUAN]);
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    /**
     * 获取用户的美团圈圈配置信息
     */
    public function getMtqqConfInfo()
    {
        $sid                 = $this->_sid;
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $aliConfArr          = $otherSysConfigModel->getMtqqConfByMemberId($sid);
        $returnList          = [];
        if (!empty($aliConfArr)) {
            $returnList['partner_id']    = $aliConfArr['partner_id'];
            $returnList['access_secret'] = $aliConfArr['access_secret'];
            $returnList['member_id']     = $aliConfArr['member_id'];
        }

        $this->apiReturn(200, $returnList, 'success');
    }


    /******************口碑***************/

    /**
     * 设置用户的口碑信息
     */
    public function setKBUserConf()
    {
        $shopId              = I('post.shop_id', '');
        $sid                 = $this->_sid;
        $shopId              = trim($shopId);
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $kbConfArr           = $otherSysConfigModel->getKBConfByMemberId($sid);
        $kbConf  = $otherSysConfigModel->getKBConfByShopId($shopId);
        if (!empty($kbConf) && $kbConf['member_id'] != $sid) {
            $this->apiReturn(203, [], '口碑通信标识已被使用，请使用其他标识');
        }
        if (empty($kbConfArr)) {
            $res = $otherSysConfigModel->insertKBConf($sid, $shopId);
        } else {
            $res = $otherSysConfigModel->editKBConfByMemberId($sid, $shopId);
        }
        if ($res) {
            $this->unBindProduct(['dockerMode' => DockerModeConst::KOUBEI]);
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    /**
     * 获取用户的口碑配置信息
     */
    public function getKBConfInfo()
    {
        $sid                 = $this->_sid;
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $kbConfArr           = $otherSysConfigModel->getKBConfByMemberId($sid);
        $returnList          = [];
        if (!empty($kbConfArr)) {

            $returnList['shop_id'] = $kbConfArr['shop_id'];
        }

        $this->apiReturn(200, $returnList, 'success');
    }


    /******************美团***************/
    /**
     * 设置美团直连conf
     * @return void
     */
    public function setMeituanConf()
    {
        $identity       = I('post.identity', '', 'strval');
        $clientId       = I('post.client_id', '', 'strval');
        $clientSecret   = I('post.client_secret', '', 'strval');
        $version        = I('post.version', '1', 'intval');
        $sid            = $this->_sid;
        if (empty($clientId)) {
            $this->apiReturn(203, [], '供应商通信标识不能为空');
        }
        if (empty($clientSecret) || empty($identity)) {
            $this->apiReturn(203, [], '交互密钥不能为空或格式有误');
        }
        $otherSysConfigModel = new \Model\Ota\ApiMeiTuanZhiLianConf();
        $meiTuanZhiLianConf  = $otherSysConfigModel->getMeiTuanZhiLianConfByPartnerId($identity, $version);
        if (!empty($meiTuanZhiLianConf) && $meiTuanZhiLianConf['member_id'] != $sid) {
            $this->apiReturn(203, [], '美团直连partnerId已被其他商户绑定');
        }
        $meituanService = new \Business\Ota\MeiTuanZhiLian();
        $res = $meituanService->setMeiTuanZhiLianConf($sid, $identity, $clientId, $clientSecret, $version);
        if ($res) {
            $dockingForm = ($version == 2) ? 1 : 0;
            $this->unBindProduct(['dockerMode' => DockerModeConst::MEITUAN_ZHILIAN, 'dockingForm' => $dockingForm]);
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    /**
     * 获取美团配置信息
     */
    public function getMeituanConfInfo()
    {
        $sid                 = $this->_sid;
        $version        = I('post.version', '1', 'intval');
        $sysConfigModel = new \Model\Ota\SysConfig();
        $otherSysConfigModel = new \Model\Ota\ApiMeiTuanZhiLianConf();
        $meituanConf  = $otherSysConfigModel->getMeiTuanZhiLianConfByMemberId($sid, $version);
        $returnList  = [];
        if (!empty($meituanConf)) {
            $returnList['identity'] = $meituanConf['partner_id'];
            $returnList['client_id'] = $meituanConf['client_id'];
            $returnList['client_secret'] = $meituanConf['client_secret'];
            $returnList['version'] = $meituanConf['version'];
            $returnList['cooperation_way'] = 0;
            // 兼容旧数据
            $dbRow = $sysConfigModel->findQunarInfoByModeAndFidIdentity($sid, 1);
            if (!empty($dbRow)) {
                $returnList['cooperation_way'] = $dbRow['cooperation_way'];
            }
        }
        $this->apiReturn(200, $returnList, 'success');
    }

    /******************去哪儿***************/
    /**
     * 设置去哪儿config
     * @return void
     */
    public function setQunarConf()
    {
        $identity       = I('post.identity', '', 'strval');
        $signkey        = I('post.signkey', '', 'strval');
        $cooperationWay = I('post.cooperation_way', '', 'intval');
        $sid            = $this->_sid;
        if (empty($identity) || empty($signkey)) {
            $this->apiReturn(203, [], '参数有误');
        }
        $apiQunarConfModel = new \Model\Ota\ApiQunarConfModel();
        $partnerInfo = $apiQunarConfModel->getConfByClientId($identity);
        if (!empty($partnerInfo) && $partnerInfo['fid'] != $sid) {
            $this->apiReturn(205, [], '供应商通信标识已被使用,请使用其他标识');
        }
        $res = $apiQunarConfModel->setConfig($sid, $identity, $signkey, $cooperationWay);
        if ($res) {
            $this->unBindProduct(['dockerMode' => DockerModeConst::QUNAR]);
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    public function getQunarConfInfo()
    {
        $sid = $this->_sid;
        $apiQunarConfModel = new \Model\Ota\ApiQunarConfModel();
        $qunarConf = $apiQunarConfModel->getFirstByFid($sid, "*");
        $returnList = [];
        if (!empty($qunarConf)) {
            $returnList['identity'] = $qunarConf['client_id'];
            $returnList['signkey'] = $qunarConf['client_secret'];
            $returnList['cooperationWay'] = $qunarConf['cooperation_way'];
        }
        $this->apiReturn(200, $returnList, 'success');
    }


    /**
     * 解绑商品
     * @return void
     */
    private function unBindProduct($param) {
        $memberId         = $this->_sid;
        $opid             = $this->_memberId;
        // 获取参数
        $dockingMode      = $param['dockerMode'];
        $dockingForm      = $param['dockingForm'];
        $extInfoQuery  = ['dockingForm' => $dockingForm];
        // 已绑定商品获取
        $qunaerModel = new \Model\qunaer\qunaer();
        $bindInfoArr = $qunaerModel->getOtaBindingTicket($memberId, $dockingMode, '', $extInfoQuery);
        if (empty($bindInfoArr)) {
            return;
        }
        $configBusiness = new ConfigBusiness();
        foreach ($bindInfoArr as $bindInfo) {
            // tid是0， 不做处理， 去掉配置项
            if ($bindInfo['tid'] == 0) {
                continue;
            }
            // 商品解绑
            $res = $configBusiness->unBindTicketCode($bindInfo['id'], $memberId, $opid);
            if ($res['code'] != 200) {
                $this->apiReturn(204, [], $res['msg']);
            }
        }
    }

    /**
     * 根据门票Id获取门票配置的短信发送详情
     * <AUTHOR>
     * @date 2021/5/21
     *
     * @return array
     */
    public function getTicketSmsSendConfigByTid()
    {
        $ticketId = I('post.ticket_id');
        $pageSize = I('post.page_size', '10', 'intval');
        $pageNum  = I('post.page_num', '1', 'intval');
        $memberId = $this->getLoginInfo()['sid'];
        if (empty($ticketId)) {
            $this->apiReturn(205, [], '门票id不可空');
        }

        $ticketBiz      = new TicketBiz();
        $timeshareBiz   = new TimeShareTicketSection();
        $extensionModel = new UpStreamRelationModel();
        $appCenterBiz   = new ModuleCommon();
        $manageBiz      = new Manage();
        $smsModel       = new SmsDiyApply();
        $ticketAttrList = $ticketBiz->queryTicketInfoByIds([$ticketId]);
        if (isset($ticketAttrList['code']) && $ticketAttrList['code'] == 200 && !empty($ticketAttrList['data'])) {
            if ($ticketAttrList['data'][0]['uuLandFDTO']['sendVoucher'] == 0) {
                $isShowNotice = true;
            }
            $timeShare     = 0;
            $landId        = $ticketAttrList['data'][0]['uuJqTicketDTO']['landid'];
            $pType         = $ticketAttrList['data'][0]['uuProductDTO']['p_type'];
            $sectionDetail = $timeshareBiz->querySectionEnableByTids([(int)$ticketId]);
            if (isset($sectionDetail['code']) && $sectionDetail['code'] == 200 && !empty($sectionDetail['data'])) {
                if ($sectionDetail['data'][0]['status'] == false) {
                    $timeShare = 1;
                }
            }

            $moduleCommonBiz = new \Business\AppCenter\ModuleCommon();
            $moduleRes       = $moduleCommonBiz->getModuleIdByMenu(['sms_diy_temp']);
            $moduleId        = 0;
            foreach ($moduleRes as $item) {
                if ($item['menu_id'] == 'sms_diy_temp') {
                    $moduleId = $item['module_id'];
                }
            }

            $moduleInfo    = $appCenterBiz->getModuleUsedByUidAndMid($memberId, $moduleId);
            $templateArr   = [];
            $extensionInfo = $extensionModel->getTicketSmsSendConfigByTicketId($ticketId);
            if (!empty($extensionInfo)) {
                $smsDiyConten = [
                    //'template'   => '',
                    //'templateId' => $extensionInfo['template_id'],
                    'sendType' => $extensionInfo['send_type'],
                ];

                $applyingTemplate = $smsModel->getSmsInfoById($extensionInfo['template_id']);
                if (!empty($applyingTemplate['content'])) {
                    $applyingTemplate['content'] = $manageBiz->revFormat($applyingTemplate['content'], $pType);
                }
                if (!empty($applyingTemplate)) {
                    $smsDiyConten = [
                        'template'   => $applyingTemplate['content'] ?? "",
                        'templateId' => $extensionInfo['template_id'],
                        'sendType'   => $extensionInfo['send_type'],
                    ];
                }
            }
            if (!empty($moduleInfo[0])) {
                if ($moduleInfo[0]['status'] == 1) {
                    //如果模块状态等于1的话, 说明是正在开通使用的（短信自定义功能）,获取当前用户在用的所有短信模板跟id
                    $usableTemplate = $manageBiz->usableTempList($this->_sid, $pType, 0, $pageNum, $pageSize);
                    if (isset($usableTemplate['code']) && $usableTemplate['code'] == 200 && !empty($usableTemplate['data'])) {
                        foreach ($usableTemplate['data']['list'] as $key => $temItem) {
                            if ($temItem['temp_state'] == 3 || !in_array($temItem['p_type'], ["A", "H"])) {
                                continue;
                            }

                            $templateArr['list'][] = [
                                'template_id' => $temItem['sms_id'],
                                'template'    => $temItem['content'],
                                'title'       => $temItem['title'],
                            ];
                        }
                        $templateArr['total']        = $usableTemplate['data']['total'];
                        $templateArr['current_page'] = $usableTemplate['data']['current_page'];
                        $templateArr['last_page']    = $usableTemplate['data']['last_page'];
                    }
                    $expire = 1;
                } else {
                    $expire = 2;
                }
            } else {
                $expire = 3;
            }
        } else {
            $this->apiReturn(205, [], '未获取门票详情内容');
        }

        $retuenData = [
            'isShowNotice'    => $isShowNotice ?? false,
            'ticket_id'       => $ticketId,
            'land_id'         => $landId,
            'prod_time_share' => $timeShare,
            'p_type'          => $pType,
            'send_type'       => $smsDiyConten['sendType'] ?? "1",
            'template_id'     => $smsDiyConten['templateId'] ?? "",
            'template'        => $smsDiyConten['template'] ?? "",
            'template_arr'    => $templateArr,
            'expire'          => $expire,                           // 1:未过期  2:已过期 3:未开通过
        ];
        $this->apiReturn(200, $retuenData, '获取成功');
    }

    /**
     * 获取绑定门票的扩展信息
     * @param ticket_id 门票id
     * @return string json
     * 
     */
    public function getBindTicketExtInfo()
    {
        $ticketId = I('post.ticket_id');
        $memberId = $this->getLoginInfo()['memberID'];
        if (empty($ticketId)) {
            $this->apiReturn(205, [], '门票id不可空');
        }
        // 获取门票供应商id 判断权限

        // 获取门票配置的扩展信息
        $extArr         = [];
        $extensionModel = new UpStreamRelationModel();
        $bindTicketExtInfoArr = $extensionModel->getBindTicketExtByTicketId($ticketId);
        if (!empty($bindTicketExtInfoArr) && !empty($bindTicketExtInfoArr['ext'])) {
            $extArr = json_decode($bindTicketExtInfoArr['ext'], true);
        }
        $this->apiReturn(200, $extArr, 'success');
    }

    /******************三清山***************/

    /**
     * 设置用户的三清山信息
     */
    public function setSQSUserConf()
    {
        $userId              = I('post.user_id', '', 'trim');
        $userKey             = I('post.user_key', '', 'trim');
        $sid                 = $this->_sid;
        $opid                = $this->_memberId;
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $sqsConfArr          = $otherSysConfigModel->getSQSConfByMemberId($sid);
        if (empty($sqsConfArr)) {
            $res = $otherSysConfigModel->insertSQSConf($sid, $opid, $userId, $userKey);
        } else {
            $res = $otherSysConfigModel->editSQSConfByMemberId($sid, $userId, $userKey);
        }
        if ($res !== false) {

            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    /**
     * 获取用户的三清山配置信息
     */
    public function getSQSConfInfo()
    {
        $sid                 = $this->_sid;
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $sqsConfArr          = $otherSysConfigModel->getSQSConfByMemberId($sid);
        $returnList          = [];
        if (!empty($sqsConfArr)) {
            $returnList['user_id']  = $sqsConfArr['partner_id'];
            $returnList['user_key'] = $sqsConfArr['partner_key'];
        }

        $this->apiReturn(200, $returnList, 'success');
    }

    /******************微盟***************/

    /**
     * 设置用户的微盟信息
     */
    public function setWMUserConf()
    {
        $publicAccountId     = I('post.public_account_id', '');
        $sid                 = $this->_sid;
        $publicAccountId     = trim($publicAccountId);
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $wmConfArr           = $otherSysConfigModel->getWeiMengTokenByAccountId($publicAccountId);
        if (empty($wmConfArr)) {
            $this->apiReturn(205, [], "此账号[{$publicAccountId}]未在微盟授权，请前往微盟授权再绑定");
        } else {
            $res = $otherSysConfigModel->editWMConfByAccountId($publicAccountId, $sid);
        }
        if ($res) {
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    /**
     * 获取用户的微盟配置信息
     */
    public function getWMConfInfo()
    {
        $sid                 = $this->_sid;
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $wmConfArr           = $otherSysConfigModel->getWMConfByMemberId($sid);
        $returnList          = [];
        if (!empty($wmConfArr)) {

            $returnList['public_account_id'] = $wmConfArr['public_account_id'];
        }

        $this->apiReturn(200, $returnList, 'success');
    }

    /****************************抖店开始***********************************************/

    /**
     * 设置抖店配置信息
     *
     * <AUTHOR>
     * @date 2021-06-15 11:33
     */
    public function getDDAuthorizationUrl()
    {
        $sid       = $this->_sid;
        $opid      = $this->_memberId;
        $serviceId = '21098'; //TODO 正式上线才有
        $url       = "https://fuwu.jinritemai.com/authorize?service_id={$serviceId}&state={$sid}|{$opid}";
        $data      = [
            'url' => $url,
        ];
        $this->apiReturn(200, $data, '获取成功');
    }

    /**
     * 获取抖店配置信息
     *
     * <AUTHOR>
     * @date 2021-06-15 11:33
     */
    public function getDDConfInfo()
    {
        $sid                 = $this->_sid;
        $otherSysConfigModel = new \Model\Ota\ApiDouDianTokenModel();
        $kbConfArr           = $otherSysConfigModel->getApiDouDianTokenByMemberId($sid);
        $returnList          = [];
        if (!empty($kbConfArr)) {
            $returnList['shop_id']   = $kbConfArr['shop_id'];
            $returnList['shop_name'] = $kbConfArr['shop_name'];
        }

        $this->apiReturn(200, $returnList, 'success');
    }

    /****************************抖店结束***********************************************/

    /*
     * 将时间换算成天数
     *
     * @param $times
     *
     * @return string
     * <AUTHOR>
     * @date 2021-06-24 10:09
     */
    public function getDouYinExpiresDay($times)
    {
        $day = floor($times / 86400);// 天数

        $hour = floor(($times - 86400 * $day) / 3600);// 小时

        $minute = floor(($times - 86400 * $day - 3600 * $hour) / 60);// 分钟
        return '授权过期还剩:' . $day . '天' . $hour . '小时' . $minute . '分钟';
    }
}