<?php
/**
 * 政府数据平台接入配置
 * <AUTHOR>
 * @date 2019/11/27 0027
 */

namespace Controller\Ota;

use Business\Ota\GovernmentDataPush;
use Business\Product\TimeShare;
use Library\Controller;
use Model\Member\Member;
use Model\Ota\GovernmentDataSys;
use Model\Product\Land;
use Business\Government\GovernmentDataPush as HbGovernmentDataPush;
use Model\Product\Evolute;
use Business\JavaApi\Product\Ticket;
use Business\JavaApi\Product\Land as Landinfo;

class GovernmentDataConfig extends Controller
{
    private $memberInfo = [];
    private $HBtimeurl  = 'reservation-info/queryReservationInfoByScenicId';

    public function __construct()
    {
        $this->memberInfo = $this->getLoginInfo();

        if (!$this->isSuper()) {
            $this->apiReturn(203, [], '无权限操作');
        }
    }

    /**
     * 获取数据平台接入配置列表
     * <AUTHOR>
     * @date 2019/11/27 0027
     *
     * @return array
     */
    public function getGovernmentConfigList()
    {
        $searchKey  = I('post.search_key', '', 'string');//搜索关键字
        $searchType = I('post.search_type', 0, 'intval');//搜索类型 1：景区名称 2：供应商id 3：景区编码
        $status     = I('post.status', -1, 'intval');//筛选 -1：全部 0：上线 1：下线
        $page       = I('post.page', 1, 'intval');
        $pageSize   = I('post.page_size', 15, 'intval');
        $sysType    = I('post.sys_type', 1, 'intval');//平台类型 1：江西省数据平台 2:山东省数据平台

        if ($status != -1 && !in_array($status, [0, 1])) {
            $this->apiReturn(203, [], '参数错误!');
        }

        $governmentDataSysModel = new GovernmentDataSys();

        $switchType = $searchType;
        if (empty($searchKey)) {
            $switchType = -1;
        }

        switch ($switchType) {
            case 0:
                //景区名称
                $result = $governmentDataSysModel->getDataListByLandName($searchKey, $status, $page, $pageSize,
                    $sysType);
                break;
            case 1:
                //供应商id
                $result = $governmentDataSysModel->getDataListByApplyId($searchKey, $status, $page, $pageSize,
                    $sysType);
                break;
            case 2:
                //景区编码
                $result = $governmentDataSysModel->getDataListByLandCode($searchKey, $status, $page, $pageSize,
                    $sysType);
                break;
            case -1:
                //默认
                if ($status == -1) {
                    $result = $governmentDataSysModel->getDataList($page, $pageSize, $sysType);
                } else {
                    $result = $governmentDataSysModel->getDataListByState($status, $page, $pageSize, $sysType);
                }
                break;
            default:
                //参数错误
                $this->apiReturn(203, [], '参数错误');
                break;
        }

        $total = $governmentDataSysModel->getNumBySearchKey($searchKey, $searchType, $status, $sysType);

        if (!$total) {
            $this->apiReturn(200, ['total' => 0, 'list' => []], '无数据!');
        }

        if (empty($result)) {
            $this->apiReturn(200, ['total' => $total, 'list' => []], '无数据');
        }

        //组装供应商 ID-名称 映射数组
        $memberIds   = array_column($result, 'apply_id');
        $memberModel = new Member();
        $memberRes   = [];
        $memberInfo  = $memberModel->getMemberInfoByMulti($memberIds, 'id', 'id, dname');
        if (is_array($memberInfo)) {
            foreach ($memberInfo as $item) {
                $memberRes[$item['id']] = $item['dname'];
            }
        }

        foreach ($result as &$value) {
            $value['apply_name']  = $memberRes[$value['apply_id']] ?? '';
            $value['gds_id']      = $value['id'];
            $value['create_time'] = date("Y-m-d H:i:s", $value['create_time']);
            unset($value['id']);
        }

        $this->apiReturn(200, ['total' => $total, 'list' => $result], 'success');
    }

    /**
     * 新增数据平台接入配置
     * <AUTHOR>
     * @date 2019/11/27 0027
     *
     * @return array
     */
    public function addGovernmentConfig()
    {
        $landName = I('post.land_name', '', 'string');
        $applyId  = I('post.apply_id', '', 'intval');
        $landCode = I('post.land_code', '', 'string');
        $landKey  = I('post.land_key', '', 'string');

        if (empty($landName) || empty($applyId) || empty($landCode) || empty($landKey)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        // 验证参数长度
        if (mb_strlen($landName, 'utf8') > 50) {
            $this->apiReturn(203, [], '参数格式不符合要求');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //唯一约束
        $governmentDataSysModel = new GovernmentDataSys();
        $governmentInfo         = $governmentDataSysModel->getInfoByApplyIdType($applyId);
        if ($governmentInfo) {
            $this->apiReturn(203, [], '该供应商已配置接入!');
        }

        //添加数据
        $configDataArr = [
            'land_name' => $landName,
            'apply_id'  => $applyId,
            'land_code' => $landCode,
            'land_key'  => $landKey,
            'sys_type'  => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_JX,
        ];

        $result = $governmentDataSysModel->addParams($configDataArr);

        if (!$result) {
            $this->apiReturn(205, [], '配置信息保存失败');
        }

        $this->apiReturn(200, [], '添加成功');
    }

    /**
     * 编辑数据平台接入配置
     * <AUTHOR>
     * @date 2019/11/27 0027
     *
     * @return array
     */
    public function modGovernmentConfig()
    {
        $gdsId    = I('post.gds_id', '', 'intval');
        $landName = I('post.land_name', '', 'string');
        $applyId  = I('post.apply_id', '', 'intval');
        $landCode = I('post.land_code', '', 'string');
        $landKey  = I('post.land_key', '', 'string');

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数错误!');
        }

        if (empty($landName) || empty($applyId) || empty($landCode) || empty($landKey)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        // 验证参数长度
        if (mb_strlen($landName, 'utf8') > 50) {
            $this->apiReturn(203, [], '参数格式不符合要求');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //更新数据
        $configDataArr = [
            'land_name' => $landName,
            'apply_id'  => $applyId,
            'land_code' => $landCode,
            'land_key'  => $landKey,
        ];

        $governmentDataSysModel = new GovernmentDataSys();
        $result                 = $governmentDataSysModel->modParams($gdsId, $configDataArr);

        if (!$result) {
            $this->apiReturn(205, [], '更新失败!');
        }

        $this->apiReturn(200, [], '更新成功');
    }

    /**
     * 获取数据平台接入配置数据
     * <AUTHOR>
     * @date 2019/11/27 0027
     *
     * @return array
     */
    public function getGovernmentConfigInfo()
    {
        $gdsId = I('post.gds_id', '', 'intval');

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $governmentDataConfigModel = new GovernmentDataSys();
        $memberModel               = new Member();
        $governmentInfo            = $governmentDataConfigModel->getInfoByGdsId($gdsId);

        if (!$governmentInfo) {
            $this->apiReturn(205, [], '获取信息失败');
        }

        $memberInfo = $memberModel->getMemberInfo($governmentInfo['apply_id'], 'id', 'dname');

        $governmentInfo['gds_id']      = $governmentInfo['id'];
        $governmentInfo['apply_name']  = $memberInfo['dname'] ?? '';
        $governmentInfo['create_time'] = date("Y-m-d H:i:s", $governmentInfo['create_time']);

        unset($governmentInfo['id']);

        $this->apiReturn(200, $governmentInfo, '成功');
    }

    /**
     * 更新上下线状态
     * <AUTHOR>
     * @date 2019/11/28 0028
     *
     * @return array
     */
    public function modGovernmentStatus()
    {
        $gdsId  = I('post.gds_id', '', 'intval');
        $status = I('post.state', '', 'intval');

        if (empty($gdsId) || !in_array($status, [0, 1])) {
            $this->apiReturn(203, [], '参数错误');
        }

        $governmentDataConfig = new GovernmentDataSys();
        $updateRes            = $governmentDataConfig->modState($gdsId, $status);

        if ($updateRes) {
            $this->apiReturn(200, [], 'success');
        }
        $this->apiReturn(205, [], '更新状态失败');
    }

    /**
     * 通过产品名称搜索产品列表
     * <AUTHOR>
     * @date 2020/6/6
     *
     * @return array
     */
    public function getLandFromGovernment()
    {
        $applyId       = I('post.apply_id', 0, 'intval');//供应商ID
        $keyword       = I('post.keyword', '', 'strval');//搜索关键字
        $getEvoluteBiz = new \Business\Product\Get\Evolute();
        $data          = $getEvoluteBiz->getEvoluteProductBySid($applyId, $keyword, 1, '', '', '', '100');
        if (empty($data)) {
            $this->apiReturn(200, ['list' => []], '获取成功');
        }
        $list = [];
        foreach ($data as $value) {
            $list[] = [
                'land_id' => $value['landDetail']['id'] ?? '',
                'name'    => $value['landDetail']['name'] ?? '',
            ];
        }
        $this->apiReturn(200, ['list' => $list], '获取成功');
    }

    /**
     * 山东省数据平台接入景区新增
     * <AUTHOR>
     * @date 2020/6/6
     *
     * @return array
     */
    public function addGovernmentConfigFromSd()
    {
        $applyId          = I('post.apply_id', 0, 'intval');//供应商ID
        $landName         = I('post.land_name', '', 'strval');//接入景区名称
        $landId           = I('post.land_id', 0, 'intval');//票付通平台的景区ID
        $scenicId         = I('post.scenic_id', '', 'strval');//山东省数据平台的景区唯一ID
        $appkey           = I('post.appkey', '', 'strval');//景区分配的appkey
        $appsecret        = I('post.appsecret', '', 'strval');//景区分配的appsecret
        $scenicStatus     = I('post.scenic_status', 0, 'intval');//景区状态(0:闭园 1:正常开放 2:限流预警 3:停止入园)
        $maxCapacity      = I('post.max_capacity', '', 'strval');//景区最大承载
        $realtimeCapacity = I('post.realtime_capacity', '', 'strval');//景区瞬时承载量
        $source           = I('post.source', '', 'strval');//信息上报来源
        $contact          = I('post.contact', '', 'strval');//预约预订联系方式
        $notice           = I('post.notice', '', 'strval');//景区通知公告
        $website          = I('post.website', '', 'strval');//PC端预约跳转地址
        $mWebsite         = I('post.m_website', '', 'strval');//手机端预约跳转地址
        $wechatAppid      = I('post.wechat_appid', '', 'strval');//小程序appid
        $wechatUrl        = I('post.wechat_url', '', 'strval');//小程序跳转路径
        $subNotice        = I('post.sub_notice', '', 'strval');//景区预约相关通告

        $governmentDataSysBus   = new \Business\Government\GovernmentDataPush();
        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($landName) || empty($applyId) || empty($scenicId) || empty($appkey) || empty($appsecret) || empty($landId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        // 验证参数长度
        if (mb_strlen($landName, 'utf8') > 50) {
            $this->apiReturn(203, [], '参数格式不符合要求');
        }

        //判断是否开启分时预约
        $timeShareBus = new TimeShare();
        $confRes      = $timeShareBus->getConfByLid($landId);
        if (!$confRes) {
            $this->apiReturn(203, [], '对接山东省预约平台请先开启分时预约!');
        }

        //分时预约时间段判断
        $nowTime     = date('Y-m-d');
        $booktimeRes = $governmentDataSysBus->getbooktimeSet($landId, $nowTime);
        if (empty($booktimeRes)) {
            $this->apiReturn(203, [], '对接山东省预约平台请先配置分时预约时间段!');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //唯一约束
        $governmentInfo = $governmentDataSysModel->getInfoByApplyIdType($applyId,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SD);
        if ($governmentInfo) {
            $this->apiReturn(203, [], '该供应商已配置接入!');
        }

        //添加基础数据
        $configDataArr = [
            'land_name' => $landName,
            'apply_id'  => $applyId,
            'land_code' => $scenicId,
            'land_key'  => "{$appkey}|{$appsecret}",
            'sys_type'  => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SD,
        ];

        $result = $governmentDataSysModel->addParams($configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '配置信息保存失败');
        }

        //添加属性数据
        $attrData    = [
            'max_capacity'      => $maxCapacity,//景区最大承载
            'realtime_capacity' => $realtimeCapacity,//景区瞬时承载量
            'source'            => $source,//信息上报来源
            'contact'           => $contact,//预约预订联系方式
            'notice'            => $notice,//景区通知公告
            'website'           => $website,//PC端预约跳转地址
            'm_website'         => $mWebsite,//手机端预约跳转地址
            'wechat_appid'      => $wechatAppid,//小程序appid
            'wechat_url'        => $wechatUrl,//小程序跳转路径
            'scenic_status'     => $scenicStatus,//景区状态(0:闭园 1:正常开放 2:限流预警 3:停止入园)
            'sub_notice'        => $subNotice,//景区预约相关通告
            'land_id'           => $landId,//景区ID
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($result, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SD);
        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        //通知山东数据平台初始化更新
        $initRes = $governmentDataSysBus->initializeFromSd($result);
        if ($initRes['code'] != $governmentDataSysBus::CODE_SUCCESS) {
            pft_log('government/shangdon/config', 'param:' . $result . '|msg:' . $initRes['msg']);
        }

        $this->apiReturn(200, [], '添加成功');
    }

    /**
     * 山东省数据平台接入景区编辑
     * <AUTHOR>
     * @date 2020/6/6
     *
     * @return array
     */
    public function saveGovernmentConfigFromSd()
    {
        $gdsId            = I('post.gds_id', 0, 'intval');//接入配置ID
        $applyId          = I('post.apply_id', 0, 'intval');//供应商ID
        $landName         = I('post.land_name', '', 'strval');//接入景区名称
        $landId           = I('post.land_id', 0, 'intval');//票付通平台的景区ID
        $scenicId         = I('post.scenic_id', '', 'strval');//山东省数据平台的景区唯一ID
        $appkey           = I('post.appkey', '', 'strval');//景区分配的appkey
        $appsecret        = I('post.appsecret', '', 'strval');//景区分配的appsecret
        $scenicStatus     = I('post.scenic_status', 0, 'intval');//景区状态(0:闭园 1:正常开放 2:限流预警 3:停止入园)
        $maxCapacity      = I('post.max_capacity', '', 'strval');//景区最大承载
        $realtimeCapacity = I('post.realtime_capacity', '', 'strval');//景区瞬时承载量
        $source           = I('post.source', '', 'strval');//信息上报来源
        $contact          = I('post.contact', '', 'strval');//预约预订联系方式
        $notice           = I('post.notice', '', 'strval');//景区通知公告
        $website          = I('post.website', '', 'strval');//PC端预约跳转地址
        $mWebsite         = I('post.m_website', '', 'strval');//手机端预约跳转地址
        $wechatAppid      = I('post.wechat_appid', '', 'strval');//小程序appid
        $wechatUrl        = I('post.wechat_url', '', 'strval');//小程序跳转路径
        $subNotice        = I('post.sub_notice', '', 'strval');//景区预约相关通告

        $governmentDataSysModel = new GovernmentDataSys();
        $governmentDataSysBus   = new \Business\Government\GovernmentDataPush();

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数错误!');
        }

        if (empty($landName) || empty($applyId) || empty($scenicId) || empty($appkey) || empty($appsecret) || empty($landId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        // 验证参数长度
        if (mb_strlen($landName, 'utf8') > 50) {
            $this->apiReturn(203, [], '参数格式不符合要求');
        }

        //判断是否开启分时预约
        $timeShareBus = new TimeShare();
        $confRes      = $timeShareBus->getConfByLid($landId);
        if (!$confRes) {
            $this->apiReturn(203, [], '对接山东省预约平台请先开启分时预约!');
        }

        //分时预约时间段判断
        $nowTime     = date('Y-m-d');
        $booktimeRes = $governmentDataSysBus->getbooktimeSet($landId, $nowTime);
        if (empty($booktimeRes)) {
            $this->apiReturn(203, [], '对接山东省预约平台请先配置分时预约时间段!');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //更新数据
        $configDataArr = [
            'land_name'   => $landName,
            'apply_id'    => $applyId,
            'land_code'   => $scenicId,
            'land_key'    => "{$appkey}|{$appsecret}",
            'sys_type'    => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SD,
            'update_time' => time(),
        ];
        $result        = $governmentDataSysModel->modParams($gdsId, $configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '更新失败!');
        }

        //保存属性数据
        $attrData    = [
            'max_capacity'      => $maxCapacity,//景区最大承载
            'realtime_capacity' => $realtimeCapacity,//景区瞬时承载量
            'source'            => $source,//信息上报来源
            'contact'           => $contact,//预约预订联系方式
            'notice'            => $notice,//景区通知公告
            'website'           => $website,//PC端预约跳转地址
            'm_website'         => $mWebsite,//手机端预约跳转地址
            'wechat_appid'      => $wechatAppid,//小程序appid
            'wechat_url'        => $wechatUrl,//小程序跳转路径
            'scenic_status'     => $scenicStatus,//景区状态(0:闭园 1:正常开放 2:限流预警 3:停止入园)
            'sub_notice'        => $subNotice,//景区预约相关通告
            'land_id'           => $landId,//景区ID
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($gdsId, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SD);
        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        //通知山东数据平台初始化更新
        $initRes = $governmentDataSysBus->initializeFromSd($gdsId);
        if ($initRes['code'] != $governmentDataSysBus::CODE_SUCCESS) {
            pft_log('government/shangdon/config', 'param:' . $result . '|msg:' . $initRes['msg']);
        }

        $this->apiReturn(200, [], '更新成功');
    }

    /**
     * 山东省数据平台接入景区数据详情
     * <AUTHOR>
     * @date 2020/6/6
     *
     * @return array
     */
    public function getGovernmentConfigFromSd()
    {
        $gdsId = I('post.gds_id', '', 'intval');

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $landModel                 = new Land();
        $governmentDataConfigBus   = new \Business\Government\GovernmentDataPush();
        $governmentDataConfigModel = new GovernmentDataSys();
        $memberModel               = new Member();
        $governmentInfo            = $governmentDataConfigModel->getInfoByGdsId($gdsId);
        if (!$governmentInfo) {
            $this->apiReturn(205, [], '获取信息失败');
        }

        $memberInfo                    = $memberModel->getMemberInfo($governmentInfo['apply_id'], 'id', 'dname');
        $governmentInfo['gds_id']      = $governmentInfo['id'];
        $governmentInfo['apply_name']  = $memberInfo['dname'] ?? '';
        $governmentInfo['scenic_id']   = $governmentInfo['land_code'];
        $landKey                       = explode('|', $governmentInfo['land_key']);
        $governmentInfo['appkey']      = $landKey[0] ?? '';
        $governmentInfo['appsecret']   = $landKey[1] ?? '';
        $governmentInfo['create_time'] = date("Y-m-d H:i:s", $governmentInfo['create_time']);
        unset($governmentInfo['id']);

        //配置属性数据获取
        $result = $governmentDataConfigModel->getAttrByGdsId($gdsId);
        if ($result) {
            $governmentInfo = array_merge($governmentInfo, $result);
        }

        //获取景区名称
        $landInfo                       = $landModel->getLandInfo($result['land_id']);
        $governmentInfo['product_name'] = $landInfo['title'] ?? '';

        //分时预约序列化后的数据
        $nowTime     = date('Y-m-d');
        $booktimeRes = $governmentDataConfigBus->getbooktimeSet($result['land_id'], $nowTime);
        if ($booktimeRes) {
            $governmentInfo['time_json'] = json_encode($booktimeRes, JSON_UNESCAPED_UNICODE);
        }

        $this->apiReturn(200, $governmentInfo, '成功');
    }

    /**
     * 四川省数据平台接入景区新增
     * <AUTHOR>
     * @date 2020/6/17
     *
     * @return array
     */
    public function addGovernmentConfigFromSc()
    {
        $applyId      = I('post.apply_id', 0, 'intval');//供应商ID
        $landName     = I('post.land_name', '', 'strval');//接入景区名称
        $landId       = I('post.land_id', 0, 'intval');//票付通平台的景区ID
        $appid        = I('post.appid', '', 'strval');//景区分配的appid
        $appsecret    = I('post.appsecret', '', 'strval');//景区分配的appsecret
        $resourceType = I('post.resource_type', 1, 'intval');//资源类型 1=景区 2=文化场馆
        $code         = I('post.code', '', 'strval');//资源编码
        $totalStock   = I('post.total_stock', '', 'strval');//最大承载量

        $governmentDataSysBus   = new \Business\Government\GovernmentDataPush();
        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($landName) || empty($applyId) || empty($code) || empty($appid) || empty($appsecret) || empty($landId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (empty($code) || empty($totalStock)) {
            $this->apiReturn(203, [], '参数缺失!');
        }

        // 验证参数长度
        if (mb_strlen($landName, 'utf8') > 50) {
            $this->apiReturn(203, [], '参数格式不符合要求');
        }

        //判断是否开启分时预约
        $timeShareBus = new TimeShare();
        $confRes      = $timeShareBus->getConfByLid($landId);
        if (!$confRes) {
            $this->apiReturn(203, [], '对接四川省预约平台请先开启分时预约!');
        }

        //分时预约时间段判断
        $nowTime     = date('Y-m-d');
        $booktimeRes = $governmentDataSysBus->getbooktimeSet($landId, $nowTime);
        if (empty($booktimeRes)) {
            $this->apiReturn(203, [], '对接四川省预约平台请先配置分时预约时间段!');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //唯一约束
        $governmentInfo = $governmentDataSysModel->getInfoByApplyIdType($applyId,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SC);
        if ($governmentInfo) {
            $this->apiReturn(203, [], '该供应商已配置接入!');
        }

        //添加基础数据
        $configDataArr = [
            'land_name' => $landName,
            'apply_id'  => $applyId,
            'land_code' => $code,
            'land_key'  => "{$appid}|{$appsecret}",
            'sys_type'  => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SC,
        ];

        $result = $governmentDataSysModel->addParams($configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '配置信息保存失败');
        }

        //添加属性数据
        $attrData    = [
            'resource_type' => $resourceType,//资源类型 1=景区 2=文化场馆
            'total_stock'   => $totalStock,//最大承载量
            'land_id'       => $landId,//景区ID
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($result, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SC);
        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $this->apiReturn(200, [], '添加成功');
    }

    /**
     * 四川省数据平台接入景区编辑
     * <AUTHOR>
     * @date 2020/6/17
     *
     * @return array
     */
    public function saveGovernmentConfigFromSc()
    {
        $gdsId        = I('post.gds_id', 0, 'intval');//接入配置ID
        $applyId      = I('post.apply_id', 0, 'intval');//供应商ID
        $landName     = I('post.land_name', '', 'strval');//接入景区名称
        $landId       = I('post.land_id', 0, 'intval');//票付通平台的景区ID
        $appid        = I('post.appid', '', 'strval');//景区分配的appid
        $appsecret    = I('post.appsecret', '', 'strval');//景区分配的appsecret
        $resourceType = I('post.resource_type', 1, 'intval');//资源类型 1=景区 2=文化场馆
        $code         = I('post.code', '', 'strval');//资源编码
        $totalStock   = I('post.total_stock', '', 'strval');//最大承载量

        $governmentDataSysModel = new GovernmentDataSys();
        $governmentDataSysBus   = new \Business\Government\GovernmentDataPush();

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数错误!');
        }

        if (empty($landName) || empty($applyId) || empty($code) || empty($appid) || empty($appsecret) || empty($landId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (empty($code) || empty($totalStock)) {
            $this->apiReturn(203, [], '参数缺失!');
        }

        // 验证参数长度
        if (mb_strlen($landName, 'utf8') > 50) {
            $this->apiReturn(203, [], '参数格式不符合要求');
        }

        //判断是否开启分时预约
        $timeShareBus = new TimeShare();
        $confRes      = $timeShareBus->getConfByLid($landId);
        if (!$confRes) {
            $this->apiReturn(203, [], '对接四川省预约平台请先开启分时预约!');
        }

        //分时预约时间段判断
        $nowTime     = date('Y-m-d');
        $booktimeRes = $governmentDataSysBus->getbooktimeSet($landId, $nowTime);
        if (empty($booktimeRes)) {
            $this->apiReturn(203, [], '对接四川省预约平台请先配置分时预约时间段!');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //更新数据
        $configDataArr = [
            'land_name'   => $landName,
            'apply_id'    => $applyId,
            'land_code'   => $code,
            'land_key'    => "{$appid}|{$appsecret}",
            'sys_type'    => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SC,
            'update_time' => time(),
        ];
        $result        = $governmentDataSysModel->modParams($gdsId, $configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '更新失败!');
        }

        //添加属性数据
        $attrData    = [
            'resource_type' => $resourceType,//资源类型 1=景区 2=文化场馆
            'total_stock'   => $totalStock,//最大承载量
            'land_id'       => $landId,//景区ID
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($gdsId, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SC);
        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $this->apiReturn(200, [], '更新成功');
    }

    /**
     * 四川省数据平台接入景区数据详情
     * <AUTHOR>
     * @date 2020/6/17
     *
     * @return array
     */
    public function getGovernmentConfigFromSc()
    {
        $gdsId = I('post.gds_id', '', 'intval');

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $landModel                 = new Land();
        $governmentDataConfigModel = new GovernmentDataSys();
        $memberModel               = new Member();
        $governmentInfo            = $governmentDataConfigModel->getInfoByGdsId($gdsId);
        if (!$governmentInfo) {
            $this->apiReturn(205, [], '获取信息失败');
        }

        $memberInfo                    = $memberModel->getMemberInfo($governmentInfo['apply_id'], 'id', 'dname');
        $governmentInfo['gds_id']      = $governmentInfo['id'];
        $governmentInfo['apply_name']  = $memberInfo['dname'] ?? '';
        $governmentInfo['code']        = $governmentInfo['land_code'];
        $landKey                       = explode('|', $governmentInfo['land_key']);
        $governmentInfo['appid']       = $landKey[0] ?? '';
        $governmentInfo['appsecret']   = $landKey[1] ?? '';
        $governmentInfo['create_time'] = date("Y-m-d H:i:s", $governmentInfo['create_time']);
        unset($governmentInfo['id']);

        //配置属性数据获取
        $result = $governmentDataConfigModel->getAttrByGdsId($gdsId);
        if ($result) {
            $governmentInfo = array_merge($governmentInfo, $result);
        }

        //获取景区名称
        $landInfo                       = $landModel->getLandInfo($result['land_id']);
        $governmentInfo['product_name'] = $landInfo['title'] ?? '';

        $this->apiReturn(200, $governmentInfo, '成功');
    }

    /**
     * 河北省数据平台接入景区新增
     * <AUTHOR>
     * @date 2020/6/18
     *
     * @return array
     */
    public function addGovernmentConfigFromHb()
    {
        $applyId        = I('post.apply_id', 0, 'intval');//供应商ID
        $landId         = I('post.land_id', 0, 'intval');//票付通平台的景区ID
        $scenicId       = I('post.scenic_id', '', 'intval');//河北省数据平台的景区唯一ID
        $times          = I('post.times', '');//时间数组
        $landName       = I('post.landname', '', 'strval');//景区名
        $landticketinfo = I('post.landticketinfo', '');//景区下所有门票信息
        $scenicStatus   = I('post.scenicStatus', 1);//景区开放状态
        $appkey         = I('post.appkey', '');//
        $appsecret      = I('post.appsecret', '');//
        $resourceId     = I('post.resource_id', '');//景区资源id

        $governmentDataSysBus   = new \Business\Government\GovernmentDataPush();
        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($landId) || empty($applyId) || empty($scenicId) || empty($appkey) || empty($appsecret)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //唯一约束
        $governmentInfo = $governmentDataSysModel->getInfoByApplyIdType($applyId,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_HB);
        if ($governmentInfo) {
            $this->apiReturn(203, [], '该供应商已配置接入!');
        }

        //添加基础数据
        $configDataArr = [
            'land_name'   => $landName,
            'apply_id'    => $applyId,
            'land_code'   => $scenicId,
            'land_key'    => "{$appkey}|{$appsecret}",
            'resource_id' => $resourceId,
            'sys_type'    => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_HB,
        ];

        $result = $governmentDataSysModel->addParams($configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '配置信息保存失败');
        }

        //添加属性数据
        $attrData    = [
            'times'            => json_encode($times),
            'land_ticket_info' => json_encode($landticketinfo),
            'scenic_status'    => $scenicStatus,//景区状态(0:闭园 1:正常开放 2:限流预警 3:停止入园)
            'land_id'          => $landId,//景区ID
            'resource_id'      => $resourceId//景区资源id
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($result, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_HB);
        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $this->apiReturn(200, [], '添加成功');
    }

    /**
     * 河北省数据平台接入景区编辑
     * <AUTHOR>
     * @date 2020/6/18
     *
     * @return array
     */
    public function getGovernmentConfigFromHb()
    {
        $gdsId = I('post.gds_id', '', 'intval');

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $landModel                 = new Land();
        $governmentDataConfigBus   = new \Business\Government\GovernmentDataPush();
        $governmentDataConfigModel = new GovernmentDataSys();
        $memberModel               = new Member();
        $governmentInfo            = $governmentDataConfigModel->getInfoByGdsId($gdsId);

        if (!$governmentInfo) {
            $this->apiReturn(205, [], '获取信息失败');
        }

        $memberInfo                    = $memberModel->getMemberInfo($governmentInfo['apply_id'], 'id', 'dname');
        $governmentInfo['gds_id']      = $governmentInfo['id'];
        $governmentInfo['apply_name']  = $memberInfo['dname'] ?? '';
        $governmentInfo['scenic_id']   = $governmentInfo['land_code'];
        $landKey                       = explode('|', $governmentInfo['land_key']);
        $governmentInfo['appkey']      = $landKey[0] ?? '';
        $governmentInfo['appsecret']   = $landKey[1] ?? '';
        $governmentInfo['create_time'] = date("Y-m-d H:i:s", $governmentInfo['create_time']);
        unset($governmentInfo['id']);

        //配置属性数据获取
        $result = $governmentDataConfigModel->getAttrByGdsId($gdsId);
        if ($result) {
            $governmentInfo = array_merge($governmentInfo, $result);
        }

        //票付通分时时间获取
        $pfttime = new TimeShare();
        $res     = $pfttime->getConfList($result['land_id']);

        //获取景区名称
        $landInfo                       = $landModel->getLandInfo($result['land_id']);
        $governmentInfo['product_name'] = $landInfo['title'] ?? '';
        //分时预约序列化后的数据
        $nowTime     = date('Y-m-d');
        $booktimeRes = $governmentDataConfigBus->getbooktimeSet($result['land_id'], $nowTime);
        if ($booktimeRes) {
            $governmentInfo['time_json'] = json_encode($booktimeRes, JSON_UNESCAPED_UNICODE);
        }
        //$hbTimeArr为获取河北省分时预约时间配置，$pftTimeArr为pft分时时间配置
        $hbTimeArr  = json_decode($governmentInfo['times'], true);
        $pftTimeArr = $res['list'][0]['timeList'];

        $timearr = [];
        foreach ($hbTimeArr as $item) {
            foreach ($pftTimeArr as $item2) {
                if ($item['time_id'] == $item2['sectionTimeId']) {
                    $timearr[] = array_merge($item, $item2);
                }
            }
        }
        $governmentInfo['times'] = $timearr;
        $this->apiReturn(200, $governmentInfo, '成功');
    }

    /**
     * 河北省数据平台接入景区编辑
     * <AUTHOR>
     * @date 2020/6/18
     *
     * @return array
     */
    public function saveGovernmentConfigFromHb()
    {
        $gdsId          = I('post.gds_id', 0, 'intval');//接入配置ID
        $applyId        = I('post.apply_id', 0, 'intval');//供应商ID
        $landcode       = I('post.land_code');//
        $land_key       = I('POST.land_key');
        $landId         = I('post.land_id', 0, 'intval');//票付通平台的景区ID
        $scenicId       = I('post.scenic_id', '', 'intval');//河北省数据平台的景区唯一ID
        $times          = I('post.times', '');//时间数组
        $landName       = I('post.landname', '', 'strval');//景区名
        $scenicStatus   = I('post.scenic_status', 1);//景区开放状态
        $appkey         = I('post.appkey', '');//
        $appsecret      = I('post.appsecret', '');//
        $resourceId     = I('post.resource_id', '');//景区资源id
        $landticketinfo = I('post.landticketinfo', '');//景区下所有门票信息

        $governmentDataSysModel = new GovernmentDataSys();
        $governmentDataSysBus   = new \Business\Government\GovernmentDataPush();

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数错误!');
        }

        if (empty($applyId) || empty($scenicId) || empty($landcode) || empty($land_key) || empty($resourceId) || empty($times)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //更新数据
        $configDataArr = [
            'apply_id'    => $applyId,
            'land_code'   => $landcode,
            'sys_type'    => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_HB,
            'update_time' => time(),
            'resource_id' => $resourceId,
            'land_key'    => "{$appkey}|{$appsecret}",
        ];
        $result        = $governmentDataSysModel->modParams($gdsId, $configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '更新失败!');
        }

        //保存属性数据
        $attrData    = [
            'times'            => json_encode($times),
            'land_id'          => $landId,
            'scenic_status'    => $scenicStatus,
            'resource_id'      => $resourceId,
            'scenic_id'        => $scenicId,
            'land_ticket_info' => json_encode($landticketinfo),
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($gdsId, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_HB);

        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $this->apiReturn(200, [], '更新成功');
    }

    /**
     * 获取河北省分时数据
     * <AUTHOR>
     * @date 2020/6/18
     *
     * @return array
     */
    public function getGovernmentConfigHB()
    {
        $scenicId  = I('post.scenicId', 0, 'intval');//河北省平台预约系统对应的景区id
        $appid     = I('post.appid', '', 'strval');
        $appsecret = I('post.appsecret', '', 'strval');
        //河北分时时间获取
        $data  = [
            'scenicId' => $scenicId ?: 7,
        ];
        $hbgo  = new HbGovernmentDataPush();
        $times = $hbgo->postHebeiReservation($appid, $appsecret, $this->HBtimeurl, $data);
        $times = json_decode($times, true);
        $arr   = [];
        foreach ($times['data']['sessionList'] as $v) {
            $arr[] = [
                'begin_time'    => $v['beginTime'],
                'end_time'      => $v['endTime'],
                'reservationId' => $times['data']['id'],
                'SessionsId'    => $v['id'],
            ];
        }
        $this->apiReturn(200, $arr, 'success');
    }

    /**
     * 获取河北省分时数据
     * <AUTHOR>
     * @date 2020/6/18
     *
     * @return array
     */
    public function getPftTime()
    {
        $sid             = I('post.salerid', 0, 'intval');//河北省平台预约系统对应的景区id
        $evolute         = new Evolute();
        $ticket          = new Ticket();
        $landTerminalApi = new \Business\CommodityCenter\TerminalLand();
        $landInfo        = $landTerminalApi->terminalQuerySaleLandList(null, null, null, [$sid]);
        $result          = $landInfo[0];
        //$result   = $evolute->getlid($sid);
        $lid      = $result['id'];
        $landname = $result['title'];
        //票付通分时时间获取
        $pfttime = new TimeShare();
        $res     = $pfttime->getConfList($lid);

        $timearr = [];
        if (empty($res['list'][0]['timeList'])) {
            $this->apiReturn(203, '', '此资源id下无对应配置，请重新确认');
        } else {
            foreach ($res['list'] as $item) {
                foreach ($item['timeList'] as $item2) {
                    $tmp               = [];
                    $tmp['begin_time'] = $item2['beginTime'];
                    $tmp['end_time']   = $item2['endTime'];
                    $tmp['time_id']    = $item2['sectionTimeId'];
                    $timearr[]         = $tmp;
                }
            }
            $ticketinfo = $ticket->queryTicketListByLandId($lid);
            $ids        = [];
            foreach ($ticketinfo['data'] as $item) {
                $ids[] = $item['id'];
            }
            $returndata = [
                'times'          => $timearr,
                'landname'       => $landname,
                'landticketinfo' => $ids,
                'land_id'        => $lid,
            ];

            $this->apiReturn(200, $returndata, '获取成功');
        }
    }

    /**
     * 保定文旅 数据配置添加
     * <AUTHOR>
     * @date 2020/6/18
     *
     * @return array
     */
    public function addGovernmentConfigFromBd()
    {
        $applyId          = I('post.apply_id', 0, 'intval');//供应商ID
        $landId           = I('post.lid', 0, 'intval');//票付通平台的景区ID
        $scenic_code      = I('post.scenic_code');//保定文旅景区编码
        $goods_code       = I('post.goods_code');//保定文旅产品编码
        $goods_name       = I('post.goods_name');//保定文旅产品名称
        $distributor_code = I('post.distributor_code');//分销商编码
        $distributor_name = I('post.distributor_name');//分销商名称
        $landName         = I('post.landname', '', 'strval');//景区名
        $landticketinfo   = I('post.landticketinfo', '');//景区下所有门票信息
        $resourceId       = I('post.resource_id', '');//景区资源id
        $appkey           = I('post.app_key');//景区appkey
        $appsecret        = I('post.app_secret');//景区appsecret
        $resource_type    = I('post.resource_type');//景区资源类型

        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($applyId) || empty($landId) || empty($scenic_code) || empty($goods_code) || empty($goods_name)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if (empty($distributor_code) || empty($distributor_name) || empty($landName) || empty($landticketinfo) || empty($resourceId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //唯一约束
        $governmentInfo = $governmentDataSysModel->getInfoByApplyIdType($applyId,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_BD);
        if ($governmentInfo) {
            $this->apiReturn(203, [], '该供应商已配置接入!');
        }

        //添加基础数据
        $configDataArr = [
            'land_name'   => $landName,
            'apply_id'    => $applyId,
            'land_code'   => $scenic_code,
            'land_key'    => $appkey . '|' . $appsecret,
            'resource_id' => $resourceId,
            'sys_type'    => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_BD,
        ];

        $result = $governmentDataSysModel->addParams($configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '配置信息保存失败');
        }

        //添加属性数据
        $attrData    = [
            'land_ticket_info' => json_encode($landticketinfo),
            'land_id'          => $landId,//景区ID
            'goods_code'       => $goods_code,
            'goods_name'       => $goods_name,
            'distributor_code' => $distributor_code,
            'distributor_name' => $distributor_name,
            'resource_type'    => $resource_type,
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($result, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_BD);
        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $this->apiReturn(200, [], '添加成功');
    }

    /**
     * 保定文旅 数据配置获取
     * <AUTHOR>
     * @date 2020/6/18
     *
     * @return array
     */
    public function getGovernmentConfigFromBd()
    {
        $gdsId = I('post.gds_id', '', 'intval');
        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $landModel                 = new Land();
        $governmentDataConfigBus   = new \Business\Government\GovernmentDataPush();
        $governmentDataConfigModel = new GovernmentDataSys();
        $memberModel               = new Member();
        $governmentInfo            = $governmentDataConfigModel->getInfoByGdsId($gdsId);

        if (!$governmentInfo) {
            $this->apiReturn(205, [], '获取信息失败');
        }

        $memberInfo                    = $memberModel->getMemberInfo($governmentInfo['apply_id'], 'id', 'dname');
        $governmentInfo['gds_id']      = $governmentInfo['id'];
        $governmentInfo['apply_name']  = $memberInfo['dname'] ?? '';
        $governmentInfo['scenic_code'] = $governmentInfo['land_code'];
        $governmentInfo['scenic_name'] = $governmentInfo['land_name'];
        list($governmentInfo['app_key'], $governmentInfo['app_secret']) = explode('|', $governmentInfo['land_key']);
        $governmentInfo['create_time'] = date("Y-m-d H:i:s", $governmentInfo['create_time']);
        unset($governmentInfo['id']);

        //配置属性数据获取
        $result = $governmentDataConfigModel->getAttrByGdsId($gdsId);
        if ($result) {
            $governmentInfo = array_merge($governmentInfo, $result);
        }

        $this->apiReturn(200, $governmentInfo, '成功');
    }

    /**
     * 保定文旅 数据配置修改
     * <AUTHOR>
     * @date 2020/6/18
     *
     * @return array
     */
    public function saveGovernmentConfigFromBd()
    {
        $gdsId            = I('post.gds_id', 0, 'intval');//接入配置ID
        $applyId          = I('post.apply_id', 0, 'intval');//供应商ID
        $landId           = I('post.lid', 0, 'intval');//票付通平台的景区ID
        $scenic_code      = I('post.scenic_code');//保定文旅景区编码
        $goods_code       = I('post.goods_code');//保定文旅产品编码
        $goods_name       = I('post.goods_name');//保定文旅产品名称
        $distributor_code = I('post.distributor_code');//分销商编码
        $distributor_name = I('post.distributor_name');//分销商名称
        $landName         = I('post.landname', '', 'strval');//景区名
        $landticketinfo   = I('post.landticketinfo', '');//景区下所有门票信息
        $resourceId       = I('post.resource_id', '');//景区资源id
        $appkey           = I('post.app_key');//景区appkey
        $appsecret        = I('post.app_secret');//景区appsecret
        $resource_type    = I('post.resource_type');//景区资源类型

        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数错误!');
        }

        if (empty($applyId) || empty($landId) || empty($scenic_code) || empty($goods_code) || empty($goods_name)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if (empty($distributor_code) || empty($distributor_name) || empty($landName) || empty($landticketinfo) || empty($resourceId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //更新数据
        $configDataArr = [
            'land_name'   => $landName,
            'apply_id'    => $applyId,
            'land_code'   => $scenic_code,
            'land_key'    => $appkey . '|' . $appsecret,
            'resource_id' => $resourceId,
            'sys_type'    => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_BD,
        ];
        $result        = $governmentDataSysModel->modParams($gdsId, $configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '更新失败!');
        }

        //保存属性数据
        $attrData    = [
            'land_ticket_info' => json_encode($landticketinfo),
            'land_id'          => $landId,//景区ID
            'goods_code'       => $goods_code,
            'goods_name'       => $goods_name,
            'distributor_code' => $distributor_code,
            'distributor_name' => $distributor_name,
            'resource_type'    => $resource_type,
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($gdsId, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_BD);

        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $this->apiReturn(200, [], '更新成功');
    }

    /**
     * 保定文旅 数据配置修改
     * <AUTHOR>
     * @date 2020/6/18
     *
     * @return array
     */
    public function getTicketfromBd()
    {
        $sid = I('post.salerid');//河北省平台预约系统对应的景区id
        if (empty($sid)) {
            $this->apiReturn(203, [], 'salerid不可为空');
        }
        $evolute = new Evolute();
        $ticket  = new Ticket();
        $landTerminalApi = new \Business\CommodityCenter\TerminalLand();
        $landInfo        = $landTerminalApi->terminalQuerySaleLandList(null, null, null, [$sid]);
        $result          = $landInfo[0];
        //$result  = $evolute->getlid($sid);
        if (empty($result)) {
            $this->apiReturn(203, [], '此资源id下无关联的景区id');
        }
        $lid      = $result['id'];
        $landname = $result['title'];

        $ticketinfo = $ticket->queryTicketListByLandId($lid);
        $ids        = [];
        foreach ($ticketinfo['data'] as $item) {
            $ids[] = $item['id'];
        }
        $returndata = [
            'landname'       => $landname,
            'landticketinfo' => $ids,
            'lid'            => $lid,
        ];

        $this->apiReturn(200, $returndata, '获取成功');

    }

    public function addGovernmentConfigFromCQ()
    {
        $applyId      = I('post.apply_id', 0, 'intval');//供应商ID
        $landId       = I('post.lid', 0, 'intval');//票付通平台的景区ID
        $landName     = I('post.landname', '', 'strval');//景区名
        $resourceCode = I('post.resource_code', '');//景区编码
        $appkey       = I('post.app_key');//景区appkey
        $appsecret    = I('post.app_secret');//景区appsecret
        $times        = I('post.times');//景区的开放时间

        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($applyId) || empty($landId) || empty($landName) || empty($resourceCode) || empty($appkey) || empty($appsecret)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //唯一约束
        $governmentInfo = $governmentDataSysModel->getInfoByApplyIdType($applyId,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_CQ);
        if ($governmentInfo) {
            $this->apiReturn(203, [], '该供应商已配置接入!');
        }

        //添加基础数据
        $configDataArr = [
            'land_name' => $landName,
            'apply_id'  => $applyId,
            'land_code' => $resourceCode,
            'land_key'  => $appkey . '|' . $appsecret,
            'sys_type'  => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_CQ,
        ];

        $result = $governmentDataSysModel->addParams($configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '配置信息保存失败');
        }

        foreach ($times as &$tItem) {
            $tItem = substr($tItem, 0, 4);
        }

        //添加属性数据
        $attrData    = [
            'land_id' => $landId,//景区ID
            'times'   => json_encode($times), //景区的开放时间
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($result, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_CQ);
        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $this->apiReturn(200, [], '添加成功');
    }

    public function getGovernmentConfigFromCQ()
    {
        $gdsId = I('post.gds_id', '', 'intval');

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $governmentDataConfigModel = new GovernmentDataSys();
        $memberModel               = new Member();
        $governmentInfo            = $governmentDataConfigModel->getInfoByGdsId($gdsId);

        if (!$governmentInfo) {
            $this->apiReturn(205, [], '获取信息失败');
        }

        $memberInfo                    = $memberModel->getMemberInfo($governmentInfo['apply_id'], 'id', 'dname');
        $governmentInfo['gds_id']      = $governmentInfo['id'];
        $governmentInfo['apply_name']  = $memberInfo['dname'] ?? '';
        $landKey                       = explode('|', $governmentInfo['land_key']);
        $governmentInfo['appkey']      = $landKey[0] ?? '';
        $governmentInfo['appsecret']   = $landKey[1] ?? '';
        $governmentInfo['create_time'] = date("Y-m-d H:i:s", $governmentInfo['create_time']);
        unset($governmentInfo['id']);
        //配置属性数据获取
        $result          = $governmentDataConfigModel->getAttrByGdsId($gdsId);
        $result['times'] = json_decode(htmlspecialchars_decode($result['times']), true);
        foreach ($result['times'] as &$tItem) {
            $tItem = $tItem . "00";
        }
        $governmentInfo = array_merge($governmentInfo, $result);
        $this->apiReturn(200, $governmentInfo, '成功');
    }

    public function saveGovernmentConfigFromCQ()
    {
        $gdsId        = I('post.gds_id', 0, 'intval');//接入配置ID
        $applyId      = I('post.apply_id', 0, 'intval');//供应商ID
        $landId       = I('post.lid', 0, 'intval');//票付通平台的景区ID
        $landName     = I('post.landname', '', 'strval');//景区名
        $resourceCode = I('post.resource_code', '');//景区编码
        $appkey       = I('post.app_key');//景区appkey
        $appsecret    = I('post.app_secret');//景区appsecret
        $times        = I('post.times');//景区的开放时间

        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数错误!');
        }

        if (empty($applyId) || empty($landId) || empty($landName) || empty($resourceCode) || empty($appkey) || empty($appsecret)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        foreach ($times as &$tItem) {
            $tItem = substr($tItem, 0, 4);
        }

        //更新数据
        $configDataArr = [
            'land_name' => $landName,
            'apply_id'  => $applyId,
            'land_code' => $resourceCode,
            'land_key'  => $appkey . '|' . $appsecret,
            'sys_type'  => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_CQ,
        ];
        $result        = $governmentDataSysModel->modParams($gdsId, $configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '更新失败!');
        }

        //保存属性数据
        $attrData    = [
            'land_id' => $landId,//景区ID
            'times'   => json_encode($times), //景区的开放时间
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($gdsId, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_CQ);

        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $this->apiReturn(200, [], '更新成功');
    }

    public function addGovernmentConfigFromMY()
    {
        $applyId   = I('post.apply_id', 0, 'intval');  //供应商ID
        $landId    = I('post.lid', 0, 'intval');       //票付通平台的景区ID
        $landName  = I('post.landname', '', 'strval'); //景区名
        $appkey    = I('post.app_key');                             //景区appkey
        $appsecret = I('post.app_secret');                          //景区appsecret
        $landAddr  = I('post.land_addr');                           //景区地址

        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($applyId) || empty($landId) || empty($landName) || empty($appkey) || empty($appsecret) || empty($landAddr)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //唯一约束
        $governmentInfo = $governmentDataSysModel->getInfoByApplyIdType($applyId,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_MY);
        if ($governmentInfo) {
            $this->apiReturn(203, [], '该供应商已配置接入!');
        }

        //添加基础数据
        $configDataArr = [
            'land_name' => $landName,
            'apply_id'  => $applyId,
            'land_code' => $appkey,
            'land_key'  => $appsecret,
            'sys_type'  => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_MY,
        ];

        $result = $governmentDataSysModel->addParams($configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '配置信息保存失败');
        }

        //添加属性数据
        $attrData    = [
            'land_id'   => $landId,                   //景区ID
            'land_addr' => $landAddr,                 //景区地址
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($result, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_MY);
        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $this->apiReturn(200, [], '添加成功');
    }

    public function getGovernmentConfigFromMY()
    {
        $gdsId = I('post.gds_id', '', 'intval');

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $governmentDataConfigModel = new GovernmentDataSys();
        $memberModel               = new Member();
        $governmentInfo            = $governmentDataConfigModel->getInfoByGdsId($gdsId);

        if (!$governmentInfo) {
            $this->apiReturn(205, [], '获取信息失败');
        }

        $memberInfo                    = $memberModel->getMemberInfo($governmentInfo['apply_id'], 'id', 'dname');
        $governmentInfo['gds_id']      = $governmentInfo['id'];
        $governmentInfo['apply_name']  = $memberInfo['dname'] ?? '';
        $landKey                       = explode('|', $governmentInfo['land_key']);
        $governmentInfo['appkey']      = $governmentInfo['land_code'];
        $governmentInfo['appsecret']   = $governmentInfo['land_key'];
        $governmentInfo['create_time'] = date("Y-m-d H:i:s", $governmentInfo['create_time']);
        unset($governmentInfo['id']);
        //配置属性数据获取
        $result         = $governmentDataConfigModel->getAttrByGdsId($gdsId);
        $governmentInfo = array_merge($governmentInfo, $result);
        $this->apiReturn(200, $governmentInfo, '成功');
    }

    public function saveGovernmentConfigFromMY()
    {
        $gdsId     = I('post.gds_id', 0, 'intval');   //接入配置ID
        $applyId   = I('post.apply_id', 0, 'intval'); //供应商ID
        $landId    = I('post.lid', 0, 'intval');      //票付通平台的景区ID
        $landName  = I('post.landname', '', 'strval');//景区名
        $appkey    = I('post.app_key');                            //景区appkey
        $appsecret = I('post.app_secret');                         //景区appsecret
        $landAddr  = I('post.land_addr');                          //景区地址

        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数错误!');
        }

        if (empty($applyId) || empty($landId) || empty($landName) || empty($appkey) || empty($appsecret) || empty($landAddr)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //更新数据
        $configDataArr = [
            'land_name' => $landName,
            'apply_id'  => $applyId,
            'land_code' => $appkey,
            'land_key'  => $appsecret,
            'sys_type'  => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_MY,
        ];
        $result        = $governmentDataSysModel->modParams($gdsId, $configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '更新失败!');
        }

        //保存属性数据
        $attrData    = [
            'land_id'   => $landId,                  //景区ID
            'land_addr' => $landAddr,                //景区地址
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($gdsId, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_MY);

        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $this->apiReturn(200, [], '更新成功');
    }

    /**
     * 河南文旅 数据配置添加
     * <AUTHOR>
     * @date 2020/9/27
     *
     * @return array
     */
    public function addGovernmentConfigFromHN()
    {
        $applyId           = I('post.apply_id', 0, 'intval');     //供应商ID
        $appkey            = I('post.app_key');                                //景区appkey
        $landId            = I('post.lid', 0, 'intval');          //票付通平台的景区ID
        $land_code         = I('post.land_code');                              //景区编码
        $landName          = I('post.landname', '', 'strval');    //景区名
        $landticketinfo    = I('post.landticketinfo', '');              //景区下所有门票信息
        $max_capacity      = I('post.max_capacity');                           //景区最大承载量
        $realtime_capacity = I('post.realtime_capacity');                      //景区瞬时承载量
        $times             = I('post.times');                                  //景区分时时间
        $land_status       = I('post.land_status');                            //景区状态 (0:闭园 1:正常开放 2:限流预警 3:停止入园) 需要发送给对方

        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($applyId) || empty($landId) || empty($appkey) || empty($land_code) || !isset($land_status)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if (empty($landName) || empty($landticketinfo) || empty($max_capacity) || empty($realtime_capacity)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //唯一约束
        $governmentInfo = $governmentDataSysModel->getInfoByApplyIdType($applyId,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_HN);
        if ($governmentInfo) {
            $this->apiReturn(203, [], '该供应商已配置接入!');
        }

        //添加基础数据
        $configDataArr = [
            'land_name'   => $landName,
            'apply_id'    => $applyId,
            'land_code'   => $land_code,
            'land_key'    => $appkey,
            'resource_id' => 0,
            'state'       => 0,
            'sys_type'    => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_HN,
        ];

        $result = $governmentDataSysModel->addParams($configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '配置信息保存失败');
        }

        //添加属性数据
        $attrData    = [
            'land_ticket_info'  => json_encode($landticketinfo),     //该景区下所有门票信息
            'land_id'           => $landId,                          //景区ID
            'max_capacity'      => $max_capacity,                    //景区最大承载量
            'realtime_capacity' => $realtime_capacity,               //景区瞬间承载量
            'times'             => json_encode($times),              //该景区的分时时间段
            'land_status'       => $land_status                      //景区状态 (0:闭园 1:正常开放 2:限流预警 3:停止入园)
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($result, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_HN);
        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $booktime_set = [];
        if (!empty($times)) {
            foreach ($times as $timeItem) {
                $booktime_set[] = [
                    "label"      => $timeItem['time'],
                    "maxBook"    => $timeItem['max'],
                    "startClock" => substr($timeItem['time'], 0, strripos($timeItem['time'], "-")),
                    "endClock"   => substr($timeItem['time'], strripos($timeItem['time'], "-") + 1),
                ];
            }
        }

        $pushData = json_encode(
            [
                'scenic_id'         => $land_code,
                'max_capacity'      => $max_capacity,
                'realtime_capacity' => $realtime_capacity,
                'booktime_set'      => $booktime_set,
            ]
        );
        $header   = ['Content-Type:application/json', "appKey:{$appkey}"];
        $response = curl_post('http://bigd.oindata.cn/data-api/api/bookSet', $pushData, 80, 25, 'api/curl_post',
            $header);
        pft_log('government/HeNan/set',
            json_encode(['push' => $pushData, 'header' => $header, 'response' => $response], JSON_UNESCAPED_UNICODE));

        if (isset($response['status']) && ($response['status'] == false || $response['status'] == 28)) {
            $this->apiReturn(200, $response, '添加成功，向省平台上报失败');
        }
        $response = json_decode($response, true);
        if ($response['code'] == 200) {
            $this->apiReturn(200, [], '添加成功');
        }
        $this->apiReturn(200, $response, '添加成功，向省平台上报失败');
    }

    /**
     * 河南文旅 数据配置添加
     * <AUTHOR>
     * @date 2020/9/27
     *
     * @return array
     */
    public function getGovernmentConfigFromHN()
    {
        $gdsId = I('post.gds_id', '', 'intval');

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $governmentDataConfigModel = new GovernmentDataSys();
        $memberModel               = new Member();
        $governmentInfo            = $governmentDataConfigModel->getInfoByGdsId($gdsId);

        if (!$governmentInfo) {
            $this->apiReturn(205, [], '获取信息失败');
        }

        $memberInfo                    = $memberModel->getMemberInfo($governmentInfo['apply_id'], 'id', 'dname');
        $governmentInfo['gds_id']      = $governmentInfo['id'];
        $governmentInfo['apply_name']  = $memberInfo['dname'] ?? '';
        $landKey                       = explode('|', $governmentInfo['land_key']);
        $governmentInfo['appkey']      = $landKey[0] ?? '';
        $governmentInfo['appsecret']   = $landKey[1] ?? '';
        $governmentInfo['create_time'] = date("Y-m-d H:i:s", $governmentInfo['create_time']);
        unset($governmentInfo['id']);

        //配置属性数据获取
        $result         = $governmentDataConfigModel->getAttrByGdsId($gdsId);
        $governmentInfo = array_merge($governmentInfo, $result);
        $this->apiReturn(200, $governmentInfo, '成功');
    }

    /**
     * 河南文旅 数据配置修改
     * <AUTHOR>
     * @date 2020/9/27
     *
     * @return array
     */
    public function saveGovernmentConfigFromHN()
    {
        $gdsId             = I('post.gds_id', 0, 'intval');       //接入配置ID
        $applyId           = I('post.apply_id', 0, 'intval');     //供应商ID
        $appkey            = I('post.app_key');                                //景区appkey
        $landId            = I('post.lid', 0, 'intval');          //票付通平台的景区ID
        $land_code         = I('post.land_code');                              //景区编码
        $landName          = I('post.landname', '', 'strval');    //景区名
        $landticketinfo    = I('post.landticketinfo', '');              //景区下所有门票信息
        $max_capacity      = I('post.max_capacity');                           //景区最大承载量
        $realtime_capacity = I('post.realtime_capacity');                      //景区瞬时承载量
        $times             = I('post.times');                                  //景区分时时间
        $land_status       = I('post.land_status');                            //景区状态 (0:闭园 1:正常开放 2:限流预警 3:停止入园) 需要发送给对方

        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($applyId) || empty($landId) || empty($appkey) || empty($land_code) || empty($land_status)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if (empty($landName) || empty($landticketinfo) || empty($max_capacity) || empty($realtime_capacity)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //更新数据
        $configDataArr = [
            'land_name'   => $landName,
            'apply_id'    => $applyId,
            'land_code'   => $land_code,
            'land_key'    => $appkey,
            'resource_id' => 0,
            'state'       => 0,
            'sys_type'    => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_HN,
        ];
        $result        = $governmentDataSysModel->modParams($gdsId, $configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '更新失败!');
        }

        //保存属性数据
        $attrData    = [
            'land_ticket_info'  => json_encode($landticketinfo),     //该景区下所有门票信息
            'land_id'           => $landId,                          //景区ID
            'max_capacity'      => $max_capacity,                    //景区最大承载量
            'realtime_capacity' => $realtime_capacity,               //景区瞬间承载量
            'times'             => json_encode($times),              //该景区的分时时间段
            'land_status'       => $land_status                      //景区状态 (0:闭园 1:正常开放 2:限流预警 3:停止入园)
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($gdsId, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_HN);

        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $booktime_set = [];
        if (!empty($times)) {
            foreach ($times as $timeItem) {
                $booktime_set[] = [
                    "label"      => $timeItem['time'],
                    "maxBook"    => $timeItem['max'],
                    "startClock" => substr($timeItem['time'], 0, strripos($timeItem['time'], "-")),
                    "endClock"   => substr($timeItem['time'], strripos($timeItem['time'], "-") + 1),
                ];
            }
        }

        $pushData = json_encode(
            [
                'scenic_id'         => $land_code,
                'max_capacity'      => $max_capacity,
                'realtime_capacity' => $realtime_capacity,
                'booktime_set'      => $booktime_set,
            ]
        );
        $header   = ['Content-Type:application/json', "appKey:{$appkey}"];
        $response = curl_post('http://bigd.oindata.cn/data-api/api/bookSet', $pushData, 80, 25, 'api/curl_post',
            $header);
        pft_log('government/HeNan/set',
            json_encode(['push' => $pushData, 'header' => $header, 'response' => $response], JSON_UNESCAPED_UNICODE));

        if (isset($response['status']) && ($response['status'] == false || $response['status'] == 28)) {
            $this->apiReturn(200, $response, '添加成功，向省平台上报失败');
        }
        $response = json_decode($response, true);
        if ($response['code'] == 200) {
            $this->apiReturn(200, [], '更新成功');
        }
        $this->apiReturn(200, $response, '更新成功，向省平台上报失败');
    }

    /**
     * 陕西文旅 返回一个带分时和门票id的数组
     * <AUTHOR>
     * @date 2020/8/27
     *
     * @return array
     */
    public function getTimeSections()
    {
        $lid     = I('post.lid');
        $ticket  = new Ticket();
        $pfttime = new TimeShare();

        //查询该景区下所有门票的分时信息
        $timeres = $pfttime->getConfList($lid);
        if (!empty($timeres['list'])) {
            foreach ($timeres['list'] as $timeItem) {
                foreach ($timeItem['sectionTimes'] as $timeListitem) {
                    $times[] = [
                        'time' => substr($timeListitem['beginTime'], 0, 2) . ':' . substr($timeListitem['beginTime'], 2,
                                2) . '-' . substr($timeListitem['endTime'], 0,
                                2) . ':' . substr($timeListitem['endTime'], 2, 2),
                        'max'  => $timeListitem['timeTicketRules'][0]['storage'] ?? 0,
                    ];
                }
            }
        } else {
            $this->apiReturn(203, [], '该景区未开启分时预约配置');
        }

        //查询该景区下的所有门票id
        $ticketinfo = $ticket->queryTicketListByLandId($lid);
        if (!empty($ticketinfo)) {
            foreach ($ticketinfo['data'] as $item) {
                $ticketIds[] = $item['id'];
            }
        }

        $result = [
            'landticketinfo' => $ticketIds,
            'times'          => $times,
        ];
        $this->apiReturn(200, $result, '获取成功');
    }

    /**
     * 陕西文旅 数据配置添加
     * <AUTHOR>
     * @date 2020/9/27
     *
     * @return array
     */
    public function addGovernmentConfigFromSX()
    {
        $applyId           = I('post.apply_id', 0, 'intval');     //供应商ID
        $appkey            = I('post.app_key');                                //景区appkey
        $landId            = I('post.lid', 0, 'intval');          //票付通平台的景区ID
        $land_code         = I('post.land_code');                              //景区编码
        $landName          = I('post.landname', '', 'strval');    //景区名
        $landticketinfo    = I('post.landticketinfo', '');              //景区下所有门票信息
        $max_capacity      = I('post.max_capacity');                           //景区最大承载量
        $realtime_capacity = I('post.realtime_capacity');                      //景区瞬时承载量
        $times             = I('post.times');                                  //景区分时时间
        $land_status       = I('post.land_status');                            //景区状态 (0:闭园 1:正常开放 2:限流预警 3:停止入园) 需要发送给对方

        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($applyId) || empty($landId) || empty($appkey) || empty($land_code) || !isset($land_status)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if (empty($landName) || empty($landticketinfo) || empty($max_capacity) || empty($realtime_capacity)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //唯一约束
        $governmentInfo = $governmentDataSysModel->getInfoByApplyIdType($applyId,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SX);
        if ($governmentInfo) {
            $this->apiReturn(203, [], '该供应商已配置接入!');
        }

        //添加基础数据
        $configDataArr = [
            'land_name'   => $landName,
            'apply_id'    => $applyId,
            'land_code'   => $land_code,
            'land_key'    => $appkey,
            'resource_id' => 0,
            'state'       => 0,
            'sys_type'    => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SX,
        ];

        $result = $governmentDataSysModel->addParams($configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '配置信息保存失败');
        }

        //添加属性数据
        $attrData    = [
            'land_ticket_info'  => json_encode($landticketinfo),     //该景区下所有门票信息
            'land_id'           => $landId,                          //景区ID
            'max_capacity'      => $max_capacity,                    //景区最大承载量
            'realtime_capacity' => $realtime_capacity,               //景区瞬间承载量
            'times'             => json_encode($times),              //该景区的分时时间段
            'land_status'       => $land_status                      //景区状态 (0:闭园 1:正常开放 2:限流预警 3:停止入园)
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($result, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SX);
        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $booktime_set = [];
        if (!empty($times)) {
            foreach ($times as $timeItem) {
                $booktime_set[] = [
                    "label"      => $timeItem['time'],
                    "maxBook"    => $timeItem['max'],
                    "startClock" => substr($timeItem['time'], 0, strripos($timeItem['time'], "-")),
                    "endClock"   => substr($timeItem['time'], strripos($timeItem['time'], "-") + 1),
                ];
            }
        }

        $pushData = json_encode(
            [
                'scenic_id'         => $land_code,
                'max_capacity'      => $max_capacity,
                'realtime_capacity' => $realtime_capacity,
                'booktime_set'      => $booktime_set,
            ]
        );
        $header   = ['Content-Type:application/json', "appKey:{$appkey}"];
        $response = curl_post('https://bigd.tourage.cn/upload-data/scenicReserveData/reserveInfo', $pushData, 80, 25,
            'api/curl_post',
            $header);
        pft_log('government/ShangXi/set',
            json_encode(['push' => $pushData, 'header' => $header, 'response' => $response], JSON_UNESCAPED_UNICODE));

        if (isset($response['status']) && ($response['status'] == false || $response['status'] == 28)) {
            $this->apiReturn(200, $response, '添加成功，向省平台上报失败');
        }
        $response = json_decode($response, true);
        if ($response['code'] == 200) {
            $this->apiReturn(200, [], '添加成功');
        }
        $this->apiReturn(200, $response, '添加成功，向省平台上报失败');
    }

    /**
     * 陕西文旅 数据配置获取
     * <AUTHOR>
     * @date 2020/9/27
     *
     * @return array
     */
    public function getGovernmentConfigFromSX()
    {
        $gdsId = I('post.gds_id', '', 'intval');

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $governmentDataConfigModel = new GovernmentDataSys();
        $memberModel               = new Member();
        $governmentInfo            = $governmentDataConfigModel->getInfoByGdsId($gdsId);

        if (!$governmentInfo) {
            $this->apiReturn(205, [], '获取信息失败');
        }

        $memberInfo                    = $memberModel->getMemberInfo($governmentInfo['apply_id'], 'id', 'dname');
        $governmentInfo['gds_id']      = $governmentInfo['id'];
        $governmentInfo['apply_name']  = $memberInfo['dname'] ?? '';
        $landKey                       = explode('|', $governmentInfo['land_key']);
        $governmentInfo['appkey']      = $landKey[0] ?? '';
        $governmentInfo['appsecret']   = $landKey[1] ?? '';
        $governmentInfo['create_time'] = date("Y-m-d H:i:s", $governmentInfo['create_time']);
        unset($governmentInfo['id']);

        //配置属性数据获取
        $result         = $governmentDataConfigModel->getAttrByGdsId($gdsId);
        $governmentInfo = array_merge($governmentInfo, $result);
        $this->apiReturn(200, $governmentInfo, '成功');
    }

    /**
     * 陕西文旅 数据配置修改
     * <AUTHOR>
     * @date 2020/9/27
     *
     * @return array
     */
    public function saveGovernmentConfigFromSX()
    {
        $gdsId             = I('post.gds_id', 0, 'intval');       //接入配置ID
        $applyId           = I('post.apply_id', 0, 'intval');     //供应商ID
        $appkey            = I('post.app_key');                                //景区appkey
        $landId            = I('post.lid', 0, 'intval');          //票付通平台的景区ID
        $land_code         = I('post.land_code');                              //景区编码
        $landName          = I('post.landname', '', 'strval');    //景区名
        $landticketinfo    = I('post.landticketinfo', '');              //景区下所有门票信息
        $max_capacity      = I('post.max_capacity');                           //景区最大承载量
        $realtime_capacity = I('post.realtime_capacity');                      //景区瞬时承载量
        $times             = I('post.times');                                  //景区分时时间
        $land_status       = I('post.land_status');                            //景区状态 (0:闭园 1:正常开放 2:限流预警 3:停止入园) 需要发送给对方

        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($applyId) || empty($landId) || empty($appkey) || empty($land_code) || !isset($land_status)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if (empty($landName) || empty($max_capacity) || empty($realtime_capacity)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //更新数据
        $configDataArr = [
            'land_name'   => $landName,
            'apply_id'    => $applyId,
            'land_code'   => $land_code,
            'land_key'    => $appkey,
            'resource_id' => 0,
            'state'       => 0,
            'sys_type'    => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SX,
        ];
        $result        = $governmentDataSysModel->modParams($gdsId, $configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '更新失败!');
        }

        //保存属性数据
        $attrData    = [
            'land_ticket_info'  => json_encode($landticketinfo),     //该景区下所有门票信息
            'land_id'           => $landId,                          //景区ID
            'max_capacity'      => $max_capacity,                    //景区最大承载量
            'realtime_capacity' => $realtime_capacity,               //景区瞬间承载量
            'times'             => json_encode($times),              //该景区的分时时间段
            'land_status'       => $land_status                      //景区状态 (0:闭园 1:正常开放 2:限流预警 3:停止入园)
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($gdsId, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SX);

        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $booktime_set = [];
        if (!empty($times)) {
            foreach ($times as $timeItem) {
                $booktime_set[] = [
                    "label"      => $timeItem['time'],
                    "maxBook"    => $timeItem['max'],
                    "startClock" => substr($timeItem['time'], 0, strripos($timeItem['time'], "-")),
                    "endClock"   => substr($timeItem['time'], strripos($timeItem['time'], "-") + 1),
                ];
            }
        }

        $pushData = json_encode(
            [
                'scenic_id'         => $land_code,
                'max_capacity'      => $max_capacity,
                'realtime_capacity' => $realtime_capacity,
                'booktime_set'      => $booktime_set,
            ]
        );
        $header   = ['Content-Type:application/json', "appKey:{$appkey}"];
        $response = curl_post('https://bigd.tourage.cn/upload-data/scenicReserveData/reserveInfo', $pushData, 80, 25,
            'api/curl_post',
            $header);
        pft_log('government/ShangXi/set',
            json_encode(['push' => $pushData, 'header' => $header, 'response' => $response], JSON_UNESCAPED_UNICODE));

        if (isset($response['status']) && ($response['status'] == false || $response['status'] == 28)) {
            $this->apiReturn(200, $response, '添加成功，向省平台上报失败');
        }
        $response = json_decode($response, true);
        if ($response['code'] == 200) {
            $this->apiReturn(200, [], '更新成功');
        }
        $this->apiReturn(200, $response, '更新成功，向省平台上报失败');
    }

    /**
     * 陕西文旅 数据配置添加
     * <AUTHOR>
     * @date 2020/9/27
     *
     * @return array
     */
    public function addGovernmentConfigFromJKB()
    {
        $applyId           = I('post.apply_id', 0, 'intval');     //供应商ID
        $appkey            = I('post.app_key');                                //景区appkey
        $landId            = I('post.lid', 0, 'intval');          //票付通平台的景区ID
        $land_code         = I('post.land_code');                              //景区编码
        $landName          = I('post.landname', '', 'strval');    //景区名
        $landticketinfo    = I('post.landticketinfo', '');              //景区下所有门票信息
        $max_capacity      = I('post.max_capacity');                           //景区最大承载量
        $realtime_capacity = I('post.realtime_capacity');                      //景区瞬时承载量
        $times             = I('post.times');                                  //景区分时时间
        $land_status       = I('post.land_status');                            //景区状态 (0:闭园 1:正常开放 2:限流预警 3:停止入园) 需要发送给对方

        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($applyId) || empty($landId) || empty($appkey) || empty($land_code) || !isset($land_status)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if (empty($landName) || empty($landticketinfo) || empty($max_capacity) || empty($realtime_capacity)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //唯一约束
        $governmentInfo = $governmentDataSysModel->getInfoByApplyIdType($applyId,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SX);
        if ($governmentInfo) {
            $this->apiReturn(203, [], '该供应商已配置接入!');
        }

        //添加基础数据
        $configDataArr = [
            'land_name'   => $landName,
            'apply_id'    => $applyId,
            'land_code'   => $land_code,
            'land_key'    => $appkey,
            'resource_id' => 0,
            'state'       => 0,
            'sys_type'    => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SX,
        ];

        $result = $governmentDataSysModel->addParams($configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '配置信息保存失败');
        }

        //添加属性数据
        $attrData    = [
            'land_ticket_info'  => json_encode($landticketinfo),     //该景区下所有门票信息
            'land_id'           => $landId,                          //景区ID
            'max_capacity'      => $max_capacity,                    //景区最大承载量
            'realtime_capacity' => $realtime_capacity,               //景区瞬间承载量
            'times'             => json_encode($times),              //该景区的分时时间段
            'land_status'       => $land_status                      //景区状态 (0:闭园 1:正常开放 2:限流预警 3:停止入园)
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($result, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SX);
        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $booktime_set = [];
        if (!empty($times)) {
            foreach ($times as $timeItem) {
                $booktime_set[] = [
                    "label"      => $timeItem['time'],
                    "maxBook"    => $timeItem['max'],
                    "startClock" => substr($timeItem['time'], 0, strripos($timeItem['time'], "-")),
                    "endClock"   => substr($timeItem['time'], strripos($timeItem['time'], "-") + 1),
                ];
            }
        }

        $pushData = json_encode(
            [
                'scenic_id'         => $land_code,
                'max_capacity'      => $max_capacity,
                'realtime_capacity' => $realtime_capacity,
                'booktime_set'      => $booktime_set,
            ]
        );
        $header   = ['Content-Type:application/json', "appKey:{$appkey}"];
        $response = curl_post('https://bigd.tourage.cn/upload-data/scenicReserveData/reserveInfo', $pushData, 80, 25,
            'api/curl_post',
            $header);
        pft_log('government/jikebao/set',
            json_encode(['push' => $pushData, 'header' => $header, 'response' => $response], JSON_UNESCAPED_UNICODE));

        if (isset($response['status']) && ($response['status'] == false || $response['status'] == 28)) {
            $this->apiReturn(200, $response, '添加成功，向省平台上报失败');
        }
        $response = json_decode($response, true);
        if ($response['code'] == 200) {
            $this->apiReturn(200, [], '添加成功');
        }
        $this->apiReturn(200, $response, '添加成功，向省平台上报失败');
    }

    /**
     * 陕西文旅 数据配置获取
     * <AUTHOR>
     * @date 2020/9/27
     *
     * @return array
     */
    public function getGovernmentConfigFromJKB()
    {
        $gdsId = I('post.gds_id', '', 'intval');

        if (empty($gdsId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $governmentDataConfigModel = new GovernmentDataSys();
        $memberModel               = new Member();
        $governmentInfo            = $governmentDataConfigModel->getInfoByGdsId($gdsId);

        if (!$governmentInfo) {
            $this->apiReturn(205, [], '获取信息失败');
        }

        $memberInfo                    = $memberModel->getMemberInfo($governmentInfo['apply_id'], 'id', 'dname');
        $governmentInfo['gds_id']      = $governmentInfo['id'];
        $governmentInfo['apply_name']  = $memberInfo['dname'] ?? '';
        $landKey                       = explode('|', $governmentInfo['land_key']);
        $governmentInfo['appkey']      = $landKey[0] ?? '';
        $governmentInfo['appsecret']   = $landKey[1] ?? '';
        $governmentInfo['create_time'] = date("Y-m-d H:i:s", $governmentInfo['create_time']);
        unset($governmentInfo['id']);

        //配置属性数据获取
        $result         = $governmentDataConfigModel->getAttrByGdsId($gdsId);
        $governmentInfo = array_merge($governmentInfo, $result);
        $this->apiReturn(200, $governmentInfo, '成功');
    }

    /**
     * 陕西文旅 数据配置修改
     * <AUTHOR>
     * @date 2020/9/27
     *
     * @return array
     */
    public function saveGovernmentConfigFromJKB()
    {
        $gdsId             = I('post.gds_id', 0, 'intval');       //接入配置ID
        $applyId           = I('post.apply_id', 0, 'intval');     //供应商ID
        $appkey            = I('post.app_key');                                //景区appkey
        $landId            = I('post.lid', 0, 'intval');          //票付通平台的景区ID
        $land_code         = I('post.land_code');                              //景区编码
        $landName          = I('post.landname', '', 'strval');    //景区名
        $landticketinfo    = I('post.landticketinfo', '');              //景区下所有门票信息
        $max_capacity      = I('post.max_capacity');                           //景区最大承载量
        $realtime_capacity = I('post.realtime_capacity');                      //景区瞬时承载量
        $times             = I('post.times');                                  //景区分时时间
        $land_status       = I('post.land_status');                            //景区状态 (0:闭园 1:正常开放 2:限流预警 3:停止入园) 需要发送给对方

        $governmentDataSysModel = new GovernmentDataSys();

        if (empty($applyId) || empty($landId) || empty($appkey) || empty($land_code) || !isset($land_status)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if (empty($landName) || empty($max_capacity) || empty($realtime_capacity)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        //效验供应商id是否正确
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($applyId, 'id', 'dname');
        if (!$memberInfo) {
            $this->apiReturn(203, [], '供应商不存在!');
        }

        //更新数据
        $configDataArr = [
            'land_name'   => $landName,
            'apply_id'    => $applyId,
            'land_code'   => $land_code,
            'land_key'    => $appkey,
            'resource_id' => 0,
            'state'       => 0,
            'sys_type'    => $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SX,
        ];
        $result        = $governmentDataSysModel->modParams($gdsId, $configDataArr);
        if (!$result) {
            $this->apiReturn(205, [], '更新失败!');
        }

        //保存属性数据
        $attrData    = [
            'land_ticket_info'  => json_encode($landticketinfo),     //该景区下所有门票信息
            'land_id'           => $landId,                          //景区ID
            'max_capacity'      => $max_capacity,                    //景区最大承载量
            'realtime_capacity' => $realtime_capacity,               //景区瞬间承载量
            'times'             => json_encode($times),              //该景区的分时时间段
            'land_status'       => $land_status                      //景区状态 (0:闭园 1:正常开放 2:限流预警 3:停止入园)
        ];
        $saveAttrRes = $governmentDataSysModel->saveAttr($gdsId, $attrData,
            $governmentDataSysModel::GOVERNMENT_SYS_TYPE_SX);

        if (!$saveAttrRes) {
            $this->apiReturn(205, [], '配置属性保存失败');
        }

        $booktime_set = [];
        if (!empty($times)) {
            foreach ($times as $timeItem) {
                $booktime_set[] = [
                    "label"      => $timeItem['time'],
                    "maxBook"    => $timeItem['max'],
                    "startClock" => substr($timeItem['time'], 0, strripos($timeItem['time'], "-")),
                    "endClock"   => substr($timeItem['time'], strripos($timeItem['time'], "-") + 1),
                ];
            }
        }

        $pushData = json_encode(
            [
                'scenic_id'         => $land_code,
                'max_capacity'      => $max_capacity,
                'realtime_capacity' => $realtime_capacity,
                'booktime_set'      => $booktime_set,
            ]
        );
        $header   = ['Content-Type:application/json', "appKey:{$appkey}"];
        $response = curl_post('https://bigd.tourage.cn/upload-data/scenicReserveData/reserveInfo', $pushData, 80, 25,
            'api/curl_post',
            $header);
        pft_log('government/qicikou/set',
            json_encode(['push' => $pushData, 'header' => $header, 'response' => $response], JSON_UNESCAPED_UNICODE));

        if (isset($response['status']) && ($response['status'] == false || $response['status'] == 28)) {
            $this->apiReturn(200, $response, '添加成功，向省平台上报失败');
        }
        $response = json_decode($response, true);
        if ($response['code'] == 200) {
            $this->apiReturn(200, [], '更新成功');
        }
        $this->apiReturn(200, $response, '更新成功，向省平台上报失败');
    }

}