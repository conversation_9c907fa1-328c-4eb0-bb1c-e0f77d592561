<?php
/**
 * 快手配置管理
 */

namespace Controller\Ota;

use Library\Controller;

class KuaiShouConfig extends Controller
{
    private $_memberInfo = [];
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 设置用户的快手信息
     * <AUTHOR>
     * @date 2021-07-10
     */
    public function setKuaiShouUserConf()
    {
        $shopId = I('post.shop_id', '');
        $sid    = $this->_sid;

        if (empty($shopId)) {
            $this->apiReturn(205, [], '店铺编码不能为空');
        }

        $otherSysConfigModel = new \Model\Ota\KuaiShouConfig();
        $config              = $otherSysConfigModel->getKuaiShouConfByShopId($shopId);
        if (!empty($config)) {
            if ($config['shop_id'] == $shopId && $config['member_id'] != $sid) {
                $this->apiReturn(205, [], '该shopId已被使用,请使用其他shopId');
            }
        }

        $kuaiShouConfArr = $otherSysConfigModel->getKuaiShouConfByMemberId($sid);
        if (empty($kuaiShouConfArr)) {
            $insData = [
                'member_id'  => $sid,
                'shop_id'    => $shopId,
                'app_id'     => '',
                'app_secret' => '',
            ];
            $res     = $otherSysConfigModel->insertKuaiShouConf($insData);
        } else {
            $upData = [
                'shop_id'    => $shopId,
                'app_id'     => '',
                'app_secret' => '',
            ];
            $res    = $otherSysConfigModel->editKuaiShouConfByMemberId($sid, $upData);
        }
        if ($res) {
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    /**
     * 获取用户的快手配置信息
     * <AUTHOR>
     * @date 2021-07-10
     */
    public function getKuaiShouConfInfo()
    {
        $sid                 = $this->_sid;
        $otherSysConfigModel = new \Model\Ota\KuaiShouConfig();
        $kuaiShouConfArr     = $otherSysConfigModel->getKuaiShouConfByMemberId($sid);
        $returnList          = [];
        if (!empty($kuaiShouConfArr)) {
            $returnList = [
                'shop_id' => $kuaiShouConfArr['shop_id'],
            ];
        }

        $this->apiReturn(200, $returnList, 'success');
    }

    /**
     * 设置快手是否发送短信通知配置
     * /r/Ota_KuaiShouConfig/setExtConf
     * @param string is_sms 1:开 2：关
     * @param string notice_type 1:退单
     * @param string phone 手机号
     * 
     */
    public function setExtConf()
    {
        $isSms      = I('post.is_sms', '');
        $noticeType = I('post.notice_type', '');
        $phone      = I('post.phone', '');
        $sid        = $this->_sid;
        $memberId   = $this->_memberId;

        if (empty($isSms) || empty($noticeType) || empty($phone)) {
            $this->apiReturn(205, [], '店铺编码不能为空');
        }

        $otherSysConfigModel = new \Model\Ota\ApiKuaiShouLocalTokenModel();
        // 配置发送短信和短信类型
        $res = $otherSysConfigModel->setKuaiShouLocalExtConfig($sid, [
            'is_sms'      => $isSms, 
            'notice_type' => $noticeType,
            'phone'       => $phone
        ], $memberId);
        
        if ($res) {
            $this->apiReturn(200, [], '配置成功');
        } else {
            $this->apiReturn(200, [], '配置失败');
        }
    }

    /**
     * 获取快手短信配置
     * /r/Ota_KuaiShouConfig/getExtConf
     * @param
     * @return code:200 data:["is_sms" , "notice_type", "phone"]
     * 
     */
    public function getExtConf()
    {
        $sid                 = $this->_sid;
        $otherSysConfigModel = new \Model\Ota\ApiKuaiShouLocalTokenModel();
        $kuaiShouConfArr     = $otherSysConfigModel->getKuaiShouLocalExtConfig($sid);
        $returnList          = [];
        if (!empty($kuaiShouConfArr['ext'])) {
            $returnList = json_decode($kuaiShouConfArr['ext']);
        }

        $this->apiReturn(200, $returnList, 'success');
    }
}