<?php
/**
 * @desc 美团统一预定系统及用户配置
 *
 * <AUTHOR>
 * @date   2020-05
 *
 */

namespace Controller\Ota;

use Library\Controller;
use Library\JsonRpc\Client;
use Model\Ota\MtComTimeShare;

class MtAllOrder extends Controller
{

    private $_sid;
    private $_memberId;
    //ota数据模型
    protected $_otaModel = null;

    // 对接系统配置

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_sid      = $loginInfo['sid'];
        $this->_memberId = $loginInfo['memberID'];
    }

    /**
     * http://my.12301.local/r/Ota_MtAllOrder/allMTChannelList
     * 返回美团现有所有的渠道
     *
     */
    public function allMTChannelList()
    {
        $channelListArr = load_config('mt_channel', 'mtAllOrder');
        $this->apiReturn(200, $channelListArr, '数据获取成功');
    }

    /**
     * my.12301.local/r/Ota_MtAllOrder/getMTConfByMemberId
     * <AUTHOR>
     * @date   2020-5
     * @desc   获取系统配置数据
     *
     */
    public function getMTConfByMemberId()
    {
        $sid = $this->_sid;
        if (empty($sid)) {
            $this->apiReturn(203, [], '参数有误');
        }

        // 获取绑定数据详情
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $configArr           = $otherSysConfigModel->getMtConfByMemberId($sid, '*');
        if (!empty($configArr)) {
            $configArr['client_info'] = $configArr['client_id'] .'|'. $configArr['client_secret'];
            $this->apiReturn(200, $configArr, 'success');
        }

        $this->apiReturn(205, [], '未配置');
    }

    /**
     * my.12301.local/r/Ota_MtAllOrder/editMTUserConfig
     * <AUTHOR>
     * @date 2020-05
     * @desc 添加编辑系统配置数据
     *
     * @param  int    id              列表id
     * @param  int    partner_id      商家id
     * @param  string client_info     秘钥信息
     * @param  int    tenant_id       景区id
     * @param  string tenant_name     景区名
     * @param  string open_mt_channel 开通渠道
     * @param  string
     *
     */
    public function editMTUserConfig()
    {
        $partnerId     = I('post.partner_id', '', 'int');
        $clientInfo    = I('post.client_info', '', 'string');
        $tenantId      = I('post.tenant_id', '', 'int');
        $tenantName    = I('post.tenant_name', '', 'string');
        $openMTChannel = 4;

        // 参数验证
        if (empty($partnerId) || empty($clientInfo)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $clientInfoArr = explode('|', $clientInfo);
        if (count($clientInfoArr) < 2) {
            $this->apiReturn(203, [], '秘钥参数格式错误');
        }

        $clientId     = $clientInfoArr[0];
        $clientSecret = $clientInfoArr[1];
        $applyDid     = $this->_sid;
        $opId         = $this->_memberId;

        // 实例化模型
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
        $configRes           = $otherSysConfigModel->getMtConfByMemberId($applyDid, 'id');
        if (empty($configRes)) {
            $configRes = $otherSysConfigModel->addMtConfig($applyDid, $partnerId, $clientId, $clientSecret, $tenantId,
                $tenantName, $openMTChannel);

            // 生产环境才需要配置
            if (ENV == 'PRODUCTION') {
                // 如果是第一次加的数据则加入到定时任务 --- 生成90天全量数据
                $mtPriceApi = new \Business\JavaApi\Product\MTPrice();
                $mtPriceApi->addMeituanPriceSyncMember($applyDid, 5269593);
            }

        } else {
            $configRes = $otherSysConfigModel->editMtConfigById($configRes['id'], $applyDid, $partnerId, $clientId,
                $clientSecret, $tenantId, $tenantName, $openMTChannel);
        }

        if ($configRes) {
            if (ENV == 'PRODUCTION') {
                $host = 'http://ota.12301.cc';
            } elseif (ENV == 'TEST') {
                $host = 'http://ota.12301dev.com';
            } else {
                $host = 'http://ota.12301.local';
            }

            $requestArr = [
                'applyDid' => $applyDid
            ];

            $headerArr = ['Content-Type: application/json;charset=utf-8'];
            curl_post("{$host}/group/MT_Gate/Channel_NoticeChannelChange", json_encode($requestArr), 80, 25, 'api/curl_post', $headerArr, true);
        
            $this->apiReturn(200, ['sys_id' => $configRes], '保存成功');
        }

        $this->apiReturn(205, [], '保存失败');
    }

    /**
     * my.12301.local/r/Ota_MtAllOrder/getRefreshStatusInfo
     * 查询门票更新状态
     * @param int tid 门票id  多个逗号分隔
     * @param int docking_mode 页面使用的DockIngMode
     *  
     * @return code = 200 成功
     * 
     */
    public function getRefreshStatusInfo()
    {
        $tids        = I('post.tid', '', 'strval');
        $dockingMode = I('post.docking_mode', '156', 'strval');
        if (empty($tids)) {
            $this->apiReturn(203, [], '查询出错');
        }

        if ($dockingMode == 1) {
            if (ENV == 'TEST') {
                $fid = 28227;
            } elseif (ENV == 'PRODUCTION') {
                $fid = 28227;
            } else {
                $fid = 28227;
            }
        } elseif ($dockingMode == 156) {
            if (ENV == 'TEST') {
                $fid = 3149614;
            } elseif (ENV == 'PRODUCTION') {
                $fid = 5269593;
            } else {
                $fid = 28227;
            }
        } else {
            $this->apiReturn(203, [], '未匹配到模式'); 
        }

        $tidArr     = explode(',', $tids);
        $mtPriceApi = new \Business\JavaApi\Product\MTPrice();
        $resultArr  = $mtPriceApi->queryMeituanPriceSaveStatus($this->_sid, $fid, $tidArr);
        if ($resultArr['code'] == 200) {
            $returnDataArr = [];
            foreach ($resultArr['data'] as $item) {
                $lastDoneTime = '';
                if (!empty($item['updateTime'])) {
                    $lastDoneTime = date('Y-m-d H:i:s', $item['updateTime']);
                }

                $isComplete = false;
                if (in_array($item['status'], [2, 3])) {
                    $isComplete = true;
                }

                $returnDataArr[] = [
                    'tid'            => $item['ticketId'],
                    'last_done_time' => $lastDoneTime,
                    'is_complete'    => $isComplete
                ];
            }
        } else {
            $this->apiReturn(205, [], '查询出错');
        }
        $this->apiReturn(200, $returnDataArr, 'success');
    }

    /**
     * my.12301.local/r/Ota_MtAllOrder/refreshTimeShareData
     * 更新分时最近30天的数据
     * @param int tid 门票id
     * @param int docking_mode 页面使用的DockIngMode
     *  
     * @return code = 200 成功
     * 
     */
    public function refreshTimeShareData()
    {
        $tid         = I('post.tid');
        $dockingMode = I('post.docking_mode', '156');
        $startDate   = I('post.start_date', '');
        $endDate     = I('post.end_date', '');
        if (empty($tid) || empty($dockingMode)) {
            $this->apiReturn(203, [], '参数缺失'); 
        }

        if ($dockingMode == 1) {
            if (ENV == 'TEST') {
                $fid = 28227;
            } elseif (ENV == 'PRODUCTION') {
                $fid = 28227;
            } else {
                $fid = 28227;
            }
        } elseif ($dockingMode == 156) {
            if (ENV == 'TEST') {
                $fid = 3149614;
            } elseif (ENV == 'PRODUCTION') {
                $fid = 5269593;
            } else {
                $fid = 28227;
            }
        } else {
            $this->apiReturn(203, [], '未匹配到模式'); 
        }

        if (empty($startDate)) {
            $startDate = date('Y-m-d', time());
            $endDate   = date("Y-m-d",strtotime("+60 day")); 
        }

        $mtPriceApi = new \Business\JavaApi\Product\MTPrice();
        $resultArr  = $mtPriceApi->queryMeituanPriceSaveStatus($this->_sid, $fid, [$tid]);
        if ($resultArr['code'] == 200 && $resultArr['data'][0]['status'] == 1) {
            $this->apiReturn(205, [], '数据正常生成中，请稍后'); 
        }

        $result = $mtPriceApi->queryTimeShareTicketPriceStorageToSave($this->_sid, $fid, $startDate, $endDate, [$tid]);

        if ($result['code'] == 200) {
            if (empty($result['data'])) {
                $this->apiReturn(205, [], '更新失败! 请依次排查:1.产品分时配置需勾选OTA渠道 2.产品需分销给美团直连/美团统一预定 3.产品分时需存在未来日期');
            }
            $this->apiReturn(200, $result['data'], 'success');
        } else {
            $this->apiReturn(205, [], $result['msg']);
        }
    }

    /**
     * @param int $tid
     * @param int $notice_type
     *
     * @return code = 200 成功
     *
     */
    public function addMtTicketCodeConfig()
    {
        $tid        = I('post.tid', '', 'intval');
        $noticeType = I('post.notice_type', '', 'intval');
        $otaType    = I('post.ota_type', '', 'intval');
        $version    = I('post.version', 1, 'intval');
        $otaTypeMap = [1, 2, 3, 4]; // 1: 美团统一预定 2: 美团直连 3:美团玩乐 4:美团到综
        if (empty($tid) || empty($noticeType) || empty($otaType)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if (!in_array($otaType, $otaTypeMap)) {
            $this->apiReturn(203, [], '参数类型有误');
        }

        $memberId     = $this->_sid;
        $mtTicketConf = new MtComTimeShare();
        $result       = $mtTicketConf->getMtTicketCodeConfigBytid($tid, $otaType, $memberId, $version);
        if (!empty($result)) {
            $this->apiReturn(203, [], '请勿重复添加');
        }

        $result = $mtTicketConf->addMtTicketCodeConfig($tid, $noticeType, $this->_sid, $this->_memberId, $otaType, $version);
        if (!$result) {
            $this->apiReturn(203, [], '配置失败');
        }
        $this->apiReturn(200, [], '配置成功');
    }

    /**
     * @param int $id
     *
     * @return code = 200 成功
     *
     */
    public function queryMtTicketCodeConfigbyId()
    {
        $id = I('post.id', '', 'intval');
        if (empty($id)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        $mtTicketConf = new MtComTimeShare();
        $result       = $mtTicketConf->getMtTicketCodeConfigById($id);

        if (empty($result)) {
            $this->apiReturn(203, [], '无数据');
        }
        $this->apiReturn(200, $result, '获取成功');
    }

    /**
     * @param int $id
     *
     * @return code = 200 成功
     *
     */
    public function queryMtTicketCodeConfigbytid()
    {
        $tid      = I('post.tid', '', 'intval');
        $memberId = $this->_sid;
        if (empty($tid)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        $mtTicketConf = new MtComTimeShare();
        $result       = $mtTicketConf->getMtTicketCodeConfigBytid($tid,1, $memberId);

        if (empty($result)) {
            $this->apiReturn(203, [], '无数据');
        }
        $this->apiReturn(200, $result[0], '获取成功');
    }

    /**
     * @param int $id
     *
     * @return code = 200 成功
     *
     */
    public function updateMtTicketCodeConfig()
    {
        $tid        = I('post.tid', '', 'intval');
        $noticeType = I('post.notice_type', '', 'intval');
        $otaType    = I('post.ota_type', '', 'intval');
        $version    = I('post.version', 1, 'intval');
        $otaTypeMap = [1, 2, 3, 4]; // 1: 美团统一预定 2: 美团直连 3: 美团玩乐 4:美团到综
        if (empty($tid) || empty($noticeType) || empty($otaType)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if (!in_array($otaType, $otaTypeMap)) {
            $this->apiReturn(203, [], '参数类型有误');
        }

        $memberId     = $this->_sid;
        $mtTicketConf = new MtComTimeShare();
        $result       = $mtTicketConf->getMtTicketCodeConfigBytid($tid, $otaType, $memberId, $version);
        if (empty($result)) {
            $this->apiReturn(203, [], '更新失败');
        }

        if ($result[0]['notice_type'] == $noticeType) {
            $this->apiReturn(200, [], '更新成功');
        }

        $result = $mtTicketConf->upMtTicketCodeConfig($tid, $noticeType, $this->_memberId, $this->_sid, $otaType, $version);
        if (!$result) {
            $this->apiReturn(203, [], '更新失败');
        }
        $this->apiReturn(200, [], '更新成功');
    }

    /**
     * 更新美团退款规则
     * 
     */
    public function editMtRefundConf()
    {
        $tid        = I('post.tid', '', 'intval');
        $refundType = I('post.refund_type', '', 'intval');
        $otaType    = I('post.ota_type', '', 'intval');
        $otaTypeMap = [1, 2, 3]; // 1: 美团统一预定 2: 美团直连 3:美团玩乐
        if (empty($tid) || empty($refundType) || empty($otaType)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        if (!in_array($otaType, $otaTypeMap)) {
            $this->apiReturn(203, [], '参数类型有误');
        }

        // 配置实名制退的时候验证是否需要所有游客证件
        if ($refundType == 2) {
            $otaQueryModel    = new \Model\Ota\OtaQueryModel();
            $ticketExtInfoArr = $otaQueryModel->getLandExtInfo($tid, '', 'tid, tourist_info');
            if (!empty($ticketExtInfoArr) && $ticketExtInfoArr['tourist_info'] != 2) {
                $this->apiReturn(203, [], '非实名制门票不允许配置实名制退'); 
            }
        }

        $mtTicketConf = new MtComTimeShare();
        $result       = $mtTicketConf->getMtRefundConfigByTidMemberIdOtaType($tid, $this->_sid, $otaType);

        if (empty($result)) {
            $editRes = $mtTicketConf->addMtRefundConfig($tid, $refundType, $this->_sid, $this->_memberId, $otaType);
        } else {
            $editRes = $mtTicketConf->upMtRefundConfig($tid, $refundType, $this->_memberId, $this->_sid, $otaType);
        }

        if ($editRes) {
            $this->apiReturn(200, [], '配置成功'); 
        } else {
            $this->apiReturn(205, [], '配置失败');  
        }
    }

    /**
     * 获取美团门票配置退款信息
     * 
     */
    public function getMtRefundConf()
    {
        $tid        = I('post.tid', '', 'intval');
        $otaType    = I('post.ota_type', '', 'intval');
        if (empty($tid) || empty($otaType)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $mtTicketConf = new MtComTimeShare();
        $result       = $mtTicketConf->getMtRefundConfigByTidMemberIdOtaType($tid, $this->_sid, $otaType);
        if (!empty($result)) {
            $this->apiReturn(200, ['refund_type' => $result['refund_type'], 'ota_type' => $otaType, 'tid' => $tid], '数据为空'); 
        } else {
            $this->apiReturn(200, ['refund_type' => 1, 'ota_type' => $otaType, 'tid' => $tid], '数据为空'); 
        }
    }

    /**
     * my.12301.local/r/Ota_MtAllOrder/getSiteManageList
     * 现金窗口站点配置是否回传出票通知给美团统一预定
     * <AUTHOR>
     * @date   2020-05
     *
     * @param  $sid  登录供应商id
     * @param  $sname 站点名称
     * @param  $page  当前页
     * @param  $size  每页条数
     */
    // public function getSiteManageList()
    // {
    //     $page  = I('page', 1, 'intval');
    //     $size  = I('size', 15, 'intval');
    //     $sname = I('sname');
    //     $sid   = $this->_sid;

    //     if (!$sid || !$page || !$size) {
    //         $this->apiReturn(204, [], '参数错误');
    //     }
    //     if ($size > 15) {
    //         $this->apiReturn(204, [], '每页条数超出限制');
    //     }

    //     $siteModel = new \Model\Terminal\SiteManage();

    //     //获取站点列表信息
    //     if ($sname) {
    //         $list = $siteModel->getStieBySname($sid, $sname, $page, $size);
    //     } else {
    //         $list = $siteModel->getSiteList($sid, $page, $size);
    //     }

    //     $returnArr = ['total' => $list['total'], 'list' => []];
    //     if (!empty($list['list'])) {
    //         $siteIdArr        = array_column($list['list'], 'id');
    //         $otherSysModel    = new \Model\Ota\OtherSysConfig();
    //         $mtSiteConfArr    = $otherSysModel->getMtSiteConfListBySiteIds($siteIdArr);
    //         $tmpMtSiteConfArr = [];
    //         foreach ($mtSiteConfArr as $conf) {
    //             $tmpMtSiteConfArr[$conf['site_id']] = $conf;
    //         }

    //         foreach ($list['list'] as $key => $item) {
    //             $returnArr['list'][$key]               = $item;
    //             $returnArr['list'][$key]['conf_type']  = $tmpMtSiteConfArr[$item['id']]['conf_type'] ?? 0;
    //             $returnArr['list'][$key]['ticket_str'] = $tmpMtSiteConfArr[$item['id']]['ticket_str'] ?? '';
    //         }

    //         $this->apiReturn(200, $returnArr, '数据为空');
    //     }

    //     $this->apiReturn(200, [], '数据为空');
    // }

    /**
     * my.12301.local/r/Ota_MtAllOrder/editMtSiteConf
     * 添加修改美团统一预定渠道配置
     *
     * @param  int conf_type 1. 站点全部数据， 2.指定门票参考字段 ticket 多个英文逗号分隔
     * @param  int site_id 站点id
     * @param  string ticket_str 门票逗号分隔
     *
     */
    // public function editMtSiteConf()
    // {
    //     $confType  = I('conf_type', 1, 'intval');
    //     $siteId    = I('site_id', 0, 'intval');
    //     $ticketStr = I('ticket_str', '', 'strval');
    //     $sid       = $this->_sid;

    //     if ($confType == 2 && empty($ticketStr)) {
    //         $this->apiReturn(203, [], '特定门票信息类型，门票需选择');
    //     }

    //     if ($confType == 1) {
    //         $ticketStr = '';
    //     }

    //     // 通过站点id 获取到是否已经配置
    //     $otherSysConfigModel = new \Model\Ota\OtherSysConfig();
    //     $confResArr          = $otherSysConfigModel->getMtSiteConfListBySiteIds([$siteId]);
    //     if (empty($confResArr)) {
    //         $res = $otherSysConfigModel->addMtSiteConfig($sid, $confType, $siteId, $ticketStr);
    //     } else {
    //         $res = $otherSysConfigModel->editMtSiteConfigBySiteId($sid, $confType, $siteId, $ticketStr);
    //     }

    //     if ($res) {
    //         $this->apiReturn(200, [], '配置成功');
    //     } else {
    //         $this->apiReturn(205, [], '配置失败');
    //     }
    // }

}