<?php

namespace Controller\Ota;

use Business\Ota\Order\ExceptionFailOrderBusiness;
use Library\Controller;

/**
 * 失败订单
 *
 * Class ExceptionFailOrder
 *
 * <AUTHOR>
 * @date 2021-12-16
 * @package Controller\Ota\Order
 */
class ExceptionFailOrder extends Controller
{
    protected $_sid;

    public function __construct()
    {
        $loginInfo  = $this->getLoginInfo();
        $this->_sid = $loginInfo['sid'];
    }

    /**
     * 获取列表
     *
     * <AUTHOR>
     * @date 2021-12-16
     *
     */
    public function getList()
    {
        $page        = I('request.page/d', 1); // 第几页
        $pftOrder    = trim(I('request.pft_order/s')); // 票付通订单号
        $remoteOrder = trim(I('request.remote_order/s')); // 第三方订单号
        $startedAt   = I('request.started_at'); // 开始时间 2021-11-23 17:20:02
        $endedAt     = I('request.ended_at'); // 结束时间 2021-11-25 17:20:05
        $systemCoopB = trim(I('request.system_coopb/d', 0)); // 上游系统coopB

        $business   = new ExceptionFailOrderBusiness();
        $jsonRpcRes = $business->getList($page, $this->_sid, $pftOrder, $remoteOrder, $startedAt, $endedAt, $systemCoopB);
        return $this->apiReturn($jsonRpcRes['code'], $jsonRpcRes['data'], $jsonRpcRes['msg']);
    }
}