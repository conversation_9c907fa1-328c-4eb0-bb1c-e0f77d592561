<?php
/**
 * 有赞云
 */

namespace Controller\Ota;

use Business\Ota\YouZanCloudBusiness;
use Library\Controller;

class YouZanCloud extends Controller
{
    private $_sid;

    private $_memberId;

    private $_labelId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();

        //信息暂存
        $this->_sid      = $loginInfo['sid'];
        $this->_memberId = $loginInfo['memberID'];

        $this->_labelId  = 1716361836483;
        if (ENV == 'PRODUCTION') {
            $this->_labelId = 1716361836483;
        }
    }

    /**
     * 有赞平台点击去使用,跳转至票付通平台登陆成功后,票付通账号与有赞商家关系自动绑定
     *
     * <AUTHOR>
     * @date 2022-08-31
     */
    public function autoBind()
    {
        $newUserToken = I('post.new_user_token', '', 'strval,trim');

        $youZanCloudBusiness = new YouZanCloudBusiness();
        $result              = $youZanCloudBusiness->autoBind($this->_sid, $newUserToken, $this->_memberId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 在票付通平台登陆后,手动输入 kdt_id 进行绑定
     *
     * <AUTHOR>
     * @date 2022-08-31
     */
    public function manualBind()
    {
        $kdtId = I('post.kdt_id', 0, 'intval');

        $youZanCloudBusiness = new YouZanCloudBusiness();
        $result              = $youZanCloudBusiness->manualBind($kdtId, $this->_sid, $this->_memberId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取票付通账号与有赞商家关系绑定信息
     *
     * <AUTHOR>
     * @date 2022-08-31
     */
    public function getBindInfo()
    {
        $youZanCloudBusiness = new YouZanCloudBusiness();
        $result              = $youZanCloudBusiness->getBindInfo($this->_sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取有赞的商品列表
     * 
     */
    public function getProductList()
    {
        $pageNo   = I('request.page_no', 1, 'intval');
        $pageSize = I('request.page_size', 10, 'intval');
        $kdtId    = I('request.kdt_id', 0, 'intval');

        if (empty($kdtId)) {
            $this->apiReturn(203, '', '有赞店铺id不能为空');
        }

        $youZanCloudBusiness = new YouZanCloudBusiness();
        $result              = $youZanCloudBusiness->getProductList($this->_sid, $kdtId, $pageNo, $pageSize);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除有赞商品标识
     * 
     */
    public function deleteLabelEntity()
    {
        $entityId = I('request.entity_id', '', 'string');
        $kdtId    = I('request.kdt_id', 0, 'intval');

        if (empty($kdtId)) {
            $this->apiReturn(203, '', '有赞店铺id不能为空');
        }

        $youZanCloudBusiness = new YouZanCloudBusiness();
        $result              = $youZanCloudBusiness->deleteLabelEntity($this->_sid, $kdtId, (string)$entityId, $this->_labelId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);  
    }

    /**
     * 批量添加有赞商品标识
     * 
     */
    public function addBatchLabelEntity()
    {
        $entityIds    = I('request.entity_ids', '');
        $kdtId        = I('request.kdt_id', 0, 'intval');
        $productName  = I('request.product_name', '', 'string');
        $productId    = I('request.product_id', 0, 'intval');
        $supplierName = I('request.supplier_name', '', 'string');
        $supplierId   = I('request.supplier_id', 0, 'intval');

        if (empty($kdtId)) {
            $this->apiReturn(203, '', '有赞店铺id不能为空');
        }

        $youZanCloudBusiness = new YouZanCloudBusiness();
        $result              = $youZanCloudBusiness->addBatchLabelEntity($this->_sid, $kdtId, (array)$entityIds, $this->_labelId
                                                                        , $productName, $productId, $supplierName, $supplierId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 批量移除有赞商品标识
     * 
     */
    public function deleteBatchLabelEntity()
    {
        $entityIds = I('request.entity_ids', '');
        $kdtId     = I('request.kdt_id', 0, 'intval');

        if (empty($kdtId)) {
            $this->apiReturn(203, '', '有赞店铺id不能为空');
        }

        $youZanCloudBusiness = new YouZanCloudBusiness();
        $result              = $youZanCloudBusiness->deleteBatchLabelEntity($this->_sid, $kdtId, (array)$entityIds, $this->_labelId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);  
    }

    /**
     * 获取已经打标的门票列表
     * /r/Ota_YouZanCloud/getTagTicketList
     * 
     */
    public function getTagTicketList()
    {
        $pageNo       = I('request.page_no', 1, 'intval');
        $pageSize     = I('request.page_size', 10, 'intval');
        $entityId     = I('request.entity_id', '', 'string');
        $kdtId        = I('request.kdt_id', 0, 'intval');
        $productName  = I('request.product_name', '', 'string');
        $supplierName = I('request.supplier_name', '', 'string');
        $productId    = I('request.product_id', 0, 'intval');
        $supplierId   = I('request.supplier_id', 0, 'intval');

        if (!empty($ticketIds) && !is_array($ticketIds)) {
            $this->apiReturn(203, '', 'ticket_ids 参数格式错误'); 
        }

        $productIds = [];
        if (!empty($productId)) {
            $productIds = [$productId];
        }

        $youZanCloudBusiness = new YouZanCloudBusiness();
        $result = $youZanCloudBusiness->getTagTicketList($this->_sid, $pageNo, $pageSize, 
                                                        $entityId, (int)$kdtId , $productName, 
                                                        $supplierName, $productIds, $supplierId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}