<?php

/**
 * 美团直连配置文件
 * @deprecate 弃用
 * Class MeiTuanZhiLian
 * @package Controller\Ota
 */

namespace Controller\Ota;

use Library\Controller;

class MeiTuanZhiLian extends Controller
{
    private $_memberInfo = [];
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
    }

    /**
     * 设置美团配置信息
     *
     * Author : liucm
     * Date : 2021/7/24
     */
    public function setMeiTuanZhiLianConf()
    {
        $partnerId    = I('post.partner_id', '', 'strval');
        $clientId     = I('post.client_id', '', 'strval');
        $clientSecret = I('post.client_secret', '', 'strval');
        $version      = I('post.version', 1, 'intval');
        $sid          = $this->_sid;
        $partnerId    = trim($partnerId);
        $clientId     = trim($clientId);
        $clientSecret = trim($clientSecret);

        $otherSysConfigModel = new \Model\Ota\ApiMeiTuanZhiLianConf();
        $MeiTuanZhiLianConf  = $otherSysConfigModel->getMeiTuanZhiLianConfByPartnerId($partnerId, $version);
        if (empty($MeiTuanZhiLianConf)) {
            $res = $otherSysConfigModel->insertMeiTuanZhiLianConf($sid, $partnerId, $clientId, $clientSecret, $version);
        } else {
            $res = $otherSysConfigModel->editMeiTuanZhiLianConfByMemberId($sid, $partnerId, $clientId, $clientSecret, $version);
        }
        if ($res !== false) {
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(205, [], '绑定失败');
        }
    }

    /**
     * 获取用户的美团配置信息
     */
    public function getMeiTuanZhiLianInfo()
    {
        $sid                 = $this->_sid;
        $version      = I('post.version', 1, 'intval');
        $otherSysConfigModel = new \Model\Ota\ApiMeiTuanZhiLianConf();
        $MeiTuanZhiLianConf  = $otherSysConfigModel->getMeiTuanZhiLianConfByMemberId($sid, $version);
        $returnList          = [];
        if (!empty($MeiTuanZhiLianConf)) {
            $returnList['partner_id']    = $MeiTuanZhiLianConf['partner_id'];
            $returnList['client_id']     = $MeiTuanZhiLianConf['public_account_id'];
            $returnList['client_secret'] = $MeiTuanZhiLianConf['public_account_id'];
        }

        $this->apiReturn(200, $returnList, 'success');
    }

}