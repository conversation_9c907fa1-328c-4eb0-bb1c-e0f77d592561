<?php
/**
 * 快手开放平台
 */

namespace Controller\Ota;

use Library\Controller;
use Model\Ota\ApiKuaiShouOpenTokenModel;

class KuaiShouOpen extends Controller
{
    private $_sid;

    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();

        //信息暂存
        $this->_sid      = $loginInfo['sid'];
        $this->_memberId = $loginInfo['memberID'];
    }

    /**
     * 获取授权商家信息
     *
     * <AUTHOR>
     * @date 2022-02-09
     */
    public function getSellerInfo()
    {
        $apiKuaiShouOpenTokenModel = new ApiKuaiShouOpenTokenModel();
        $firstByAid                = $apiKuaiShouOpenTokenModel->getFirstByAid($this->_sid, 'seller_id,seller_name');
        if (empty($firstByAid)) {
            $this->apiReturn(200, [], 'success');
        }
        $data = [
            'seller_id'   => $firstByAid['seller_id'],
            'seller_name' => $firstByAid['seller_name'],
        ];

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 获取授权 URL
     *
     * <AUTHOR>
     * @date 2021-07-10
     */
    public function getAuthorizationUrl()
    {
        if (ENV == 'PRODUCTION') {
            $appKey      = 'ks693554346418940377';
            $redirectUri = 'https://ota.12301.cc/group/KuaiShouOpenGate/authCallback';
        } elseif (ENV == 'IS_PFT_GRAY') {
            $appKey      = 'ks693554346418940377';
            $redirectUri = 'https://ota.12301.cc/group/KuaiShouOpenGate/authCallback';
        } else {
            $appKey      = 'ks693554346418940377';
            $redirectUri = 'http://ota.12301dev.com/group/KuaiShouOpenGate/authCallback';
        }

        $url  = sprintf('https://open.kwaixiaodian.com/oauth/authorize?app_id=%s&redirect_uri=%s&scope=user_info,merchant_user&response_type=code&state=%s,%s',
            $appKey, $redirectUri, $this->_sid, $this->_memberId);
        $data = [
            'url' => $url,
        ];
        $this->apiReturn(200, $data, 'success');
    }
}