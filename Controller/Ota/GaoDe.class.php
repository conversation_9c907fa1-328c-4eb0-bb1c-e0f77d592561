<?php

namespace Controller\Ota;

use Business\Ota\GaoDeBusiness;
use Library\Controller;

class GaoDe extends Controller
{
    private $_sid;

    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();

        //信息暂存
        $this->_sid      = $loginInfo['sid'];
        $this->_memberId = $loginInfo['memberID'];
    }

    /**
     * 获取绑定信息
     *
     * <AUTHOR>
     * @date 2022-12-08
     */
    public function getBindInfo()
    {
        $gaoDeBusiness = new GaoDeBusiness();
        $result        = $gaoDeBusiness->getBindInfo($this->_sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 绑定
     *
     * <AUTHOR>
     * @date 2022-12-08
     */
    public function bind()
    {
        $gaoDeBusiness = new GaoDeBusiness();
        $result        = $gaoDeBusiness->bind($this->_sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 解绑
     *
     * <AUTHOR>
     * @date 2022-12-08
     */
    public function unBind()
    {
        $gaoDeBusiness = new GaoDeBusiness();
        $result        = $gaoDeBusiness->unBind($this->_sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}