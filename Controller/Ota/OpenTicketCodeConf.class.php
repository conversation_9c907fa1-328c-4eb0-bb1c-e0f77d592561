<?php

namespace Controller\Ota;

use Business\JavaApi\ListService\LandListQueryService;
use Business\Ota\OpenTicketConf;
use Library\Controller;

class OpenTicketCodeConf extends Controller
{
    private $_memberInfo = [];
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];

    }

    /**
     * 获取可售门票配置列表
     *
     * Author : liucm
     * Date : 2021/9/28
     */
    public function getOpenTicketList()
    {
        $page           = I('post.page', 1, 'intval');
        $size           = I('post.size', 10, 'intval');
        $lid            = I('post.lid', '', 'intval');
        $ticketId       = I('post.tid', '', 'intval');
        $fid            = I('post.fid', '', 'intval');

        if (empty($fid)) {
            $this->apiReturn(204, [], '分销商id不能为空');
        }
        $openTicketConf = new OpenTicketConf();
        $memberInfo     = $this->_memberInfo;
        $res            = $openTicketConf->getTicketList($memberInfo, $page, $size, $lid, $ticketId, $fid);

        if ($res['code'] == 200) {
            $this->apiReturn(200, $res['data'], '获取列表成功');
        }

        $this->apiReturn(204, [], $res['msg']);
    }

    /**
     * 获取门票配置
     *
     * Author : liucm
     * Date : 2021/9/24
     */
    public function getTicketCodeConf()
    {
        $returnData = [
            'notice_type' => 1,
            'is_show'     => 1,
            'is_notice'   => 1,
        ];

        $sid   = $this->_sid;
        $appId = I('post.app_id', 0, 'strval');
        $tid   = I('post.tid', 0, 'intval');

        if (empty($tid) || empty($appId)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model = new \Model\Ota\PftOpenTicketCodeConfModel();
        $res   = $model->getAppTicketCodeConf($appId, $sid, $tid);
        if (!empty($res)) {
            $returnData = [
                'notice_type' => $res['notice_type'],
                'is_show'     => $res['is_show'],
                'is_notice'   => $res['is_notice'],
            ];
        }

        $this->apiReturn(200, $returnData, 'success');
    }

    /**
     * 增加或修改门票配置
     *
     * Author : liucm
     * Date : 2021/9/24
     */
    public function addOrUpdateTbTicketCodeConf()
    {
        $tid        = I('post.tid', 0, 'intval');
        $appId      = I('post.app_id', 0, 'strval');
        $noticeType = I('post.notice_type', -1, 'intval');
        $isShow     = I('post.is_show', -1, 'intval');
        $isNotice   = I('post.is_notice', -1, 'intval');
        $memberId   = $this->_sid;
        $opid       = $this->_memberId;
        if (empty($tid) || $noticeType == -1 || $isShow == -1) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model   = new \Model\Ota\PftOpenTicketCodeConfModel();
        $ConfRes = $model->getAppTicketCodeConf($appId, $memberId, $tid);
        if (!empty($ConfRes)) {
            $res = $model->updateAppTicketCodeConf($appId, $memberId, $tid, $noticeType, $isShow, $isNotice, $opid);
            if ($res !== false) {
                $this->apiReturn(200, [], '修改成功');
            }
            $this->apiReturn(204, [], '修改操作失败，请重试');
        }

        $res = $model->addAppTicketCodeConf($appId, $memberId, $tid, $noticeType, $isShow, $isNotice, $opid);
        if ($res !== false) {
            $this->apiReturn(200, [], '新增成功');
        }

        $this->apiReturn(204, [], '新增操作失败，请重试');
    }

    /***
     * 分销价格配置列表联想搜索
     * <AUTHOR> Yiqiang
     * @date   2018-09-03
     *
     */
    public function getProductSearchList()
    {
        $type = I('post.type', '', 'strval');
        //搜索关键字
        $keyword = I('post.keyword', '', 'strval');
        //产品id
        $productId = I('post.productId', 0, 'intval');
        $page      = I('post.page', 1, 'intval');
        $size      = I('post.size', 1000, 'intval');
        if (!$type) {
            $this->apiReturn(204, [], '缺少参数');
        }
        $return = [];
        switch ($type) {
            //获取可售卖产品列表
            case 'self_product':
                $memberId           = $this->_sid;
                $data['fid']        = $memberId;
                $data['pTypeList']  = [];
                $data['title']      = $keyword;
                $data['pageNumber'] = $page;
                $data['pageSize']   = $size;
                //$data['shop']      = 10;

                $landListQueryService = new LandListQueryService();
                $result               = $landListQueryService->queryEvoluteLandTitleList($data);
                //对返回回来的数据进行处理
                if ($result['code'] == 200 && count($result['data']['list']) > 0) {
                    foreach ($result['data']['list'] as $item) {
                        $return['list'][$item['landId']] = [
                            'name' => $item['title'],
                            'lid'  => $item['landId'],

                        ];
                    }
                    $return['list'] = array_values($return['list']);
                }
                break;
            //获取产品下所有票类列表
            case 'self_ticket':
                $javaApi = new \Business\CommodityCenter\Ticket();
                $status  = 1; //上架
                $result  = $javaApi->queryTicketListByLandId($productId, 'id,title', false, $status);
                if (count($result) > 0) {
                    foreach ($result as $item) {
                        $return['list'][$item['id']] = [
                            'tid'  => $item['id'],
                            'name' => $item['title'],
                            'lid'  => $productId,
                        ];
                    }
                    $return['list'] = array_values($return['list']);
                }
                break;
            default:
                break;
        }
        $this->apiReturn(200, $return, 'success');
    }
}