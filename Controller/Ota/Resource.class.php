<?php
/**
 * 资源中心对接模块操作
 * 
 */

namespace Controller\Ota;
use Library\Controller;

class Resource extends Controller
{
    /**
     * 调用hyperf资源中心
     * @param int $lid
     * @return array
     * 
     */
    public function refreshProductData()
    {
        if (!$this->isSuper()) {
            $this->apiReturn(203, [], '无权限');
        }

        $lid = I('request.lid', 0);
        if (empty($lid) || !is_numeric($lid)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $resourceModel      = new \Model\Ota\Resource();
        // 调用之前要先判断是否是资源中心拉到的数据
        $relationResInfoDict = $resourceModel->getThirdLandRelationInfoByPftLid($lid);
        if (empty($relationResInfoDict)) {
            $this->apiReturn(205, [], '非资源对接拉取数据'); 
        }

        $pftThirdLid  = $relationResInfoDict['third_product_id'];
        $pullConfigId = $relationResInfoDict['resource_pull_conf_id'];

        if (!in_array($pullConfigId, [2, 3])) {
            $this->apiReturn(205, [], '暂不支持该系统');  
        }

        // 判断调用是对应系统的拉取方法
        $method = 'xiecheng/pull';
        $module = 'ctrip';
        if ($pullConfigId == 3) {
            $method = 'lvmama/pull';
            $module = 'lvmama';
        }

        try {
            $lib  = new \Library\JsonRpc\PftRpcClient('resource_product');
            $res  = $lib->call($method, [$lid], $module);
            $data = $res['data'];
            $code = $res['code'];
            $msg  = $res['message'];
        } catch (\Exception $e) {
            $msg  = $e->getMessage();
            $code = $e->getCode();
            $data = [];
        }

        // 接口没返回有效信息的时候
        if (empty($msg)) {
            if ($code == 200) {
                $msg = '更新进行中';
            } else {
                $msg = '更新失败';
            }
        }

        $this->apiReturn($code, $data, $msg);
    }
}