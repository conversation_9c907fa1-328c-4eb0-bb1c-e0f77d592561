<?php

namespace Controller\Ota;

use Business\Ota\Order\ThirdOrderNewBusiness;
use Library\Controller;

/**
 * 第三方订单重推
 *
 * Class ThirdOrderNew
 *
 * <AUTHOR>
 * @date 2021-12-16
 * @package Controller\Ota\Order
 */
class ThirdOrderNew extends Controller
{
    protected $_sid;

    public function __construct()
    {
        $loginInfo  = $this->getLoginInfo();
        $this->_sid = $loginInfo['sid'];
    }

    /**
     * 获取列表
     *
     * <AUTHOR>
     * @date 2021-12-16
     *
     */
    public function getList()
    {
        $landId     = I('request.land_id', 0, 'intval'); // 景区id
        $page       = I('request.current_page', 1, 'intval'); // 当前页
        $pageSize   = I('request.page_size', 10, 'intval'); // 显示条数
        $selectType = I('request.select_type', 1, 'intval'); // 1:订单号 2:联系人 3:手机号 4:第三方订单号
        $selectText = trim(I('request.select_text', '', 'strval')); // 搜索的内容
        $timeBegin  = I('request.btime', '', 'strval'); // 起始时间
        $timeEnd    = I('request.etime', '', 'strval'); // 结束时间
        $isSend     = I('request.is_send', '', 'intval'); // 1:选择可以推送的

        $business   = new ThirdOrderNewBusiness();
        $jsonRpcRes = $business->getList($page, $pageSize, $landId, $selectType, $selectText, $timeBegin,
            $timeEnd, $isSend, $this->_sid);
        return $this->apiReturn($jsonRpcRes['code'], $jsonRpcRes['data'], $jsonRpcRes['msg']);
    }
}