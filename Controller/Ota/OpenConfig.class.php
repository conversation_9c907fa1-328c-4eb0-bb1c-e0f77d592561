<?php
/**
 * @desc 开放接口系统及用户配置
 *
 * <AUTHOR>
 * @date   2019-02
 *
 */
namespace Controller\Ota;
use Library\Controller;
use Library\Tools\Helpers;
use Library\Cache\Cache;
use Model\DataCollection\DataCollection;

class OpenConfig extends Controller {
    private   $_memberInfo = [];
    private   $_sid;
    private   $_memberId;
    private   $_isSuper;
    //ota数据模型
    protected $_otaModel = null;
    // 对接系统配置

    public function __construct() {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_memberInfo = $loginInfo;
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];
        $this->_isSuper    = $this->isSuper();
    }

    /**
     * <AUTHOR>
     * @date 2019-02
     * @desc 验证操作人员的权限
     *
     */
    private function _checkOperAuth($operId)
    {
        if (empty($operId)) {
            return false;
        }

        // 获取操作人员身份
        if (!$this->_isSuper) {
            return false;
        }
        return true;
    }

    /**
     * <AUTHOR>
     * @date 2019-02
     * @desc 确认是否通知的接口通顺
     * @param string $noticeURL
     * @return json
     *
     */
    public function checkUrlAlive()
    {
        if (!$this->_checkOperAuth($this->_memberId)) {
            $this->apiReturn(203, [], '无权限操作');
        }

        $url        = I('post.url', 'http://ota.12301dev.com/notice/wuliuren/test', 'string');
        $openOtaBiz = new \Business\Ota\Open\Order();
        $result     = $openOtaBiz->checkAlive($url);

        if ($result['code'] != 200) {
            $this->apiReturn(203, [], $result['msg']);
        }
        $this->apiReturn(200, $result['data'], $result['msg']);
    }

    /**
     * <AUTHOR>
     * @date 2019-02
     * @desc 添加系统配置数据
     * @param string sys_name   系统名称
     * @param string notice_url 通知交互地址
     * @param string white_ip   多个ip地址用逗号分隔
     * @param string
     *
     */
    public function addSysConfig()
    {
        $sysName         = I('post.sys_name', '', 'string');
        $noticeUrl       = I('post.notice_url', '', 'string');
        $whiteIpStr      = I('post.white_ip', '', 'string');
        $appkey          = I('post.appKey', '', 'string');
        $version         = I('post.version', 'V1', 'string');
        $clientIdDes     = I('post.clientIdDes', 'client_id', 'string');
        $clientSecretDes = I('post.clientSecretDes', 'client_id', 'string');

        // 参数验证
        if (empty($sysName) || empty($noticeUrl)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        // 验证参数长度
        if (mb_strlen($sysName, 'utf8') > 20 || mb_strlen($noticeUrl, 'utf8') > 120) {
            $this->apiReturn(203, [], '参数格式不符合要求');
        }

        $contactInfo = empty($appkey) ? '' : json_encode([['appKey' => $appkey]]);

        // 配置数据
        $configDataArr = [
            'name'          => $sysName,
            'Mpath'         => 'http://ota.12301.cc/open',
            'sourceT'       => 3,
            'Mdetails'      => 1,
            'system_type'   => 0,
            'account_name'  => $clientIdDes,
            'password_name' => $clientSecretDes,
            'if_Pftcode'    => 1,
            'sms'           => 1,
            'coopB'         => '',
            'timeout'       => 25,
            'errortime'     => 0,
            'is_offline'    => 0,
            'notice_code'   => 2,
            'white_ip'      => $whiteIpStr,
            'create_time'   => time(),
            'contact_info'  => $contactInfo,
            'encoding_products' => '填写格式：由三方系统提供',
            'version'       => $version
        ];

        $sysConfigModel = new \Model\Ota\SysConfig();
        $addConfigRes = $sysConfigModel->addConSys($configDataArr);

        if ($addConfigRes == false) {
            $this->apiReturn(205, [], '系统信息保存失败');
        }

        // 更新coopB 和 列表id是同一个
        $sysConfigModel->saveSysConfig($addConfigRes, ['coopB' => $addConfigRes]);

        // 添加系统通知地址
        $configNotifyArr = [
            'notify_sys'  => $addConfigRes,
            'notify_type' => 3,
            'notify_url'  => $noticeUrl
        ];

        $addConNotifyRes = $sysConfigModel->addConSysNotify($configNotifyArr);

        if ($addConNotifyRes == false) {
            $this->apiReturn(200, ['sys_id' => $addConfigRes], '系统添加成功,通知地址配置出错,请重新编辑保存');
        }

        $this->apiReturn(200, ['sys_id' => $addConfigRes], '添加成功');
    }

    /**
     * <AUTHOR>
     * @date   2019-02
     * @desc   获取系统配置数据
     * @param  int  sys_id   系统id
     *
     */
    public function getSysConfigInfo()
    {
        $sysId = I('post.sys_id', '', 'intval');
        if (empty($sysId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $sysConfigModel   = new \Model\Ota\SysConfig();
        $configField      = 'id,name,sourceT,account_name,password_name,if_Pftcode,coopB,is_offline,white_ip,create_time,version, contact_info';
        $sysConfigInfoArr = $sysConfigModel->getSysConfigInfo($sysId, $configField);
        if (empty($sysConfigInfoArr)) {
            $this->apiReturn(203, [], '无数据');
        } else {
            $data                    = $sysConfigInfoArr;
            $data['appKey']          = '';
            $data['clientIdDes']     = $data['account_name'];
            $data['clientSecretDes'] = $data['password_name'];
            if (!empty($data['contact_info'])) {
                $contactInfoArr = json_decode($data['contact_info'], true);
                $data['appKey']  = $contactInfoArr[0]['appKey'];
            }
        }

        $sysNotifyField   = 'notify_url, notify_type';
        $sysNotifyInfoArr = $sysConfigModel->getSysConfigNotifyInfo($sysId, 3, $sysNotifyField);
        if (!empty($sysNotifyInfoArr)) {
            $data = array_merge($sysNotifyInfoArr, $data);
        } else {
            $data['notify_url'] = '';
        }

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * <AUTHOR>
     * @date 2019-02
     * @desc 编辑系统配置数据
     * @param int   sys_id   系统id
     * @param sting sys_name 系统名称
     * @param sting notice_url 通知地址
     * @param sting white_ip   白名单ip地址
     * @param string
     *
     */
    public function editSysConfig()
    {
        $sysId      = I('post.sys_id', '', 'intval');
        $sysName    = I('post.sys_name', '', 'string');
        $noticeUrl  = I('post.notice_url', '', 'string');
        $whiteIpStr = I('post.white_ip', '', 'string');
        $appkey     = I('post.appKey', '', 'string');
        $version    = I('post.version', 'V1', 'string');
        $clientIdDes     = I('post.clientIdDes', 'client_id', 'string');
        $clientSecretDes = I('post.clientSecretDes', 'client_id', 'string');

        if (empty($sysId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $sysConfigArr = [];
        if (!empty($sysName)) {
            $sysConfigArr['name'] = $sysName;
        }
        if (!empty($whiteIpStr)) {
            $sysConfigArr['white_ip'] = $whiteIpStr;
        }

        $sysConfigNotifyArr = [];
        if (!empty($noticeUrl)) {
            $sysConfigNotifyArr['notify_url'] = $noticeUrl;
        }

        if (!empty($version)) {
            $sysConfigArr['version'] = $version;
        }

        if (!empty($clientIdDes)) {
            $sysConfigArr['account_name'] = $clientIdDes;
        }

        if (!empty($clientSecretDes)) {
            $sysConfigArr['password_name'] = $clientSecretDes;
        }
        $isSuccess = true;
        $sysConfigModel = new \Model\Ota\SysConfig();

        $configField      = 'id,name,white_ip,contact_info,account_name,password_name,version,notify_url';
        $sysConfigInfoArr = $sysConfigModel->getSysConfigInfo($sysId, $configField);

        if (!empty($sysConfigArr) && ($sysName != $sysConfigInfoArr['name'] || $whiteIpStr != $sysConfigInfoArr['white_ip'] || $version != $sysConfigInfoArr['version']) || $clientIdDes != $sysConfigInfoArr['account_name'] || $clientSecretDes != $sysConfigInfoArr['password_name']) {

            if (!empty($appkey)) {
                if (!empty($sysConfigInfoArr['contact_info'])) {
                    $contactInfoArr               = json_decode($sysConfigInfoArr['contact_info'], true);
                    $contactInfoArr[0]['appKey']  = $appkey;
                    $sysConfigArr['contact_info'] = json_encode($contactInfoArr);
                } else {
                    $sysConfigArr['contact_info'] = json_encode([['appKey' => $appkey]]);
                }
            }

            $sysConfigRes = $sysConfigModel->saveSysConfig($sysId, $sysConfigArr);
            if ($sysConfigRes == false) {
                $isSuccess = false;
            }
        }

        $sysNotifyField   = 'notify_url, notify_type';
        $sysNotifyInfoArr = $sysConfigModel->getSysConfigNotifyInfo($sysId, 3, $sysNotifyField);
        if (!empty($sysConfigNotifyArr) && $noticeUrl != $sysNotifyInfoArr['notify_url']) {
            $sysConfigNotifyRes = $sysConfigModel->saveConSysNotifyBySysIdAndType($sysId, 3, $sysConfigNotifyArr);
            if ($sysConfigNotifyRes == false) {
                $isSuccess = false;
            }
        }

        if ($isSuccess) {
            $this->apiReturn(200, [], '保存成功');
        } else {
            $this->apiReturn(205, [], '保存失败');
        }
    }

    /**
     * ota 对接系统列表
     * @param int page
     * @param int page_size
     * @param string sys_name
     * @return string | json
     *
     */
    public function systemList()
    {
        $page     = I('post.page', 1, 'intval');
        $pageSize = I('post.pageSize', 15, 'intval');
        $sysName  = I('post.sys_name', '', 'string');
        $status   = I('post.status', 0 , 'intval');

        if (empty($page) || empty($pageSize)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $sysConfigModel = new \Model\Ota\SysConfig();
        $systemNum      = $sysConfigModel->countSysConfigNum($sysName, $status, 3);
        $systemListArr  = $sysConfigModel->getSysConfigList($sysName, $page, $pageSize, $status, 3);
        if (empty($systemListArr)) {
            $this->apiReturn(200, ['total' => $systemNum, 'data' => []], '无数据');
        }

        $data = [];
        foreach ($systemListArr as $item) {
            $isOffLineDesc = '上线';
            if ($item['is_offline'] == 1) {
                $isOffLineDesc = '下线';
            }

            $data[] = [
                'id'         => $item['id'],
                'sys_name'   => $item['name'],
                'if_offline' => $item['is_offline'],
                'status'     => $isOffLineDesc,
                'create_time' => empty($item['create_time']) ? '' : date("Y-m-d H:i:s", $item['create_time'])
            ];
        }
        $this->apiReturn(200, ['total' => $systemNum, 'data' => $data], 'success');
    }

    /**
     * 更新系统上下线
     * @param int $systemId 系统id
     * @param string status 状态 on 上线 off 下线
     * @return string | json
     *
     */
    public function changeSystemStatus()
    {
        $systemId = I('post.sys_id', '', 'intval');
        $status   = I('post.status', '', 'string');

        if (empty($systemId) || !in_array($status, ['on', 'off'])) {
            $this->apiReturn(203, [], '参数错误');
        }

        $otaModel  = new \Model\Ota\OtaQueryModel();
        $updateRes = $otaModel->updateStatus($systemId, $status);

        if ($updateRes) {
            $this->apiReturn(200, [], 'success');
        }
        $this->apiReturn(205, [], '更新状态失败');
    }

    /**
     * 获取系统错误信息
     * @param int sys_id 系统id
     * @return string | json
     *
     */
    public function showSystemErrInfo()
    {
        $sysId = I('sys_id', '', 'intval');
        if (empty($sysId)) {
            $this->apiReturn(203, [], '参数错误');
        }
        // 查找系统下线原因

        // 返回错误信息
        $this->apiReturn(200, [], 'success');
    }
}