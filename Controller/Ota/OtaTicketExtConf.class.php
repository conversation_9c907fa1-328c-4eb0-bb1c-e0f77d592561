<?php

/**
 * ota 门票扩展信息配置 
 *
 * Class OtaTicketExtConf
 * @package Controller\Ota
 */
namespace Controller\Ota;

use Library\Controller;

class OtaTicketExtConf extends Controller
{
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        //获取登入信息
        $loginInfo = $this->getLoginInfo();
        //信息暂存
        $this->_sid        = $loginInfo['sid'];
        $this->_memberId   = $loginInfo['memberID'];

    }

    /**
     * 获取门票扩展配置
     * 
     */
    public function getTicketCodeConf()
    {
        $sid         = $this->_sid;
        $tid         = I('request.tid', 0, 'intval');
        $dockingMode = I('request.docking_mode', -1, 'intval');

        if (empty($tid) || empty($dockingMode)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $res = (new \Model\Ota\ApiOtaTicketExtConfModel())->getApiTicketExtConf($sid, $tid, $dockingMode);
        if (empty($res)) {
            return $this->apiReturn(200, [], '暂无配置');
        }

        $contentArr = json_decode($res['content'], true);
        return $this->apiReturn(200, $contentArr, 'success');
    }

    /**
     * 添加或修改抖音来客门票的扩展信息配置
     * 
     */
    public function addOrUpdateDouYinLaiKeTicketExtConf()
    {
        $tid          = I('request.tid', 0, 'intval');
        $checkWithPoi = I('request.check_with_poi', -1, 'intval');
        $memberId     = $this->_sid;
        $opid         = $this->_memberId;
        if (empty($tid) || empty($checkWithPoi)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $dockingMode = 408;
        if (ENV != 'PRODUCTION') {
            $dockingMode = 246;
        }

        $res = $this->_addOrUpdateTbTicketCodeConf($memberId, $tid, $dockingMode, ['check_with_poi' => $checkWithPoi], $opid);
        if ($res) {
            return $this->apiReturn(200, [], '操作成功');
        } else {
            return $this->apiReturn(204, [], '操作失败，请重试');
        }
    }

    /**
     * 增加或修改门票扩展配置
     *
     */
    private function _addOrUpdateTbTicketCodeConf($memberId, $tid, $dockingMode, $newContentArr, $opid)
    {
        try {
            $otaTicketExtConfModel = new \Model\Ota\ApiOtaTicketExtConfModel();
            $queryRes              = $otaTicketExtConfModel->getApiTicketExtConf($memberId, $tid, $dockingMode);
            if (empty($queryRes)) {
                $res = $otaTicketExtConfModel->addApiTicketExtConf($memberId, $tid, $dockingMode, $newContentArr, $opid);
            } else {
                $oldContentArr = json_decode($queryRes['content'], true);
                $contentArr    = array_merge($oldContentArr, $newContentArr);
                $res = $otaTicketExtConfModel->updateApiTicketExtConf($memberId, $tid, $dockingMode, $contentArr, $opid);
            }
    
            return $res;
        } catch (\Exception $e) {
            var_dump($e->getMessage());
        }
    }

}