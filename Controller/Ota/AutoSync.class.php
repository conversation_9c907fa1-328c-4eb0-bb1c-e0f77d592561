<?php

namespace Controller\Ota;

use Library\Controller;
use Library\JsonRpc\PftRpcClient;
use Throwable;

class AutoSync extends Controller
{
    private $loginInfo = [];

    public function __construct()
    {
        $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 创建规则
     *
     * <AUTHOR>
     * @date   2023-08-07
     */
    public function createRule()
    {
        $ruleName = I('rule_name', '', 'string');
        $lid      = I('lid', 0, 'intval');
        $tid      = I('tid', '', 'string');
        $ruleMode = I('rule_mode', 10, 'intval');
        $csysId   = I('csys_id', 0, 'intval');

        $rules      = I('rules');
        $state      = I('state', 0, 'intval');
        $autoBind   = I('auto_bind', 0, 'intval');
        $sid        = $this->loginInfo['sid'];               // 商户id
        $operatorId = $this->loginInfo['memberID'];   //操作人id

        $method  = 'System/SystemAutoSync/createRule';
        $rpcData = [
            'rule_name'   => $ruleName,
            'rule_mode'   => $ruleMode,
            'sid'         => $sid,
            'lid'         => $lid,
            'tid'         => $tid,
            'csys_id'     => $csysId,
            'rules'       => $rules,
            'state'       => $state,
            'operator_id' => $operatorId,
            'auto_bind'   => $autoBind
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 规则详情
     *
     * <AUTHOR>
     * @date   2023-08-07
     */
    public function getRule()
    {
        $id         = I('id', 0, 'intval');
        $sid        = $this->loginInfo['sid'];               // 商户id
        $operatorId = $this->loginInfo['memberID'];   //操作人id

        $method  = 'System/SystemAutoSync/getRule';
        $rpcData = [
            'id'          => $id,
            'sid'         => $sid,
            'operator_id' => $operatorId,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 修改规则
     *
     * <AUTHOR>
     * @date   2023-08-07
     */
    public function editRule()
    {
        $id       = I('id', 0, 'intval');
        $ruleName = I('rule_name', '', 'string');
        $lid      = I('lid', 0, 'intval');
        $tid      = I('tid', '', 'string');
        $ruleMode = I('rule_mode', 10, 'intval');

        $rules      = I('rules');
        $state      = I('state', 0, 'intval');
        $autoBind   = I('auto_bind', 0, 'intval');
        $sid        = $this->loginInfo['sid'];               // 商户id
        $operatorId = $this->loginInfo['memberID'];   //操作人id

        $method  = 'System/SystemAutoSync/editRule';
        $rpcData = [
            'id'          => $id,
            'rule_name'   => $ruleName,
            'rule_mode'   => $ruleMode,
            'sid'         => $sid,
            'lid'         => $lid,
            'tid'         => $tid,
            'rules'       => $rules,
            'state'       => $state,
            'operator_id' => $operatorId,
            'auto_bind'   => $autoBind
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 删除规则
     * 
     */
    public function delRule()
    {
        $id         = I('id', 0, 'intval');
        $sid        = $this->loginInfo['sid'];        // 商户id
        $operatorId = $this->loginInfo['memberID'];   // 操作人id

        if (empty($id)) {
            return $this->apiReturn(203, '参数错误');
        }

        $method  = 'System/SystemAutoSync/delRule';
        $rpcData = [
            'id'          => $id,
            'operator_id' => $operatorId,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 启动/停用
     *
     * <AUTHOR>
     * @date   2023-08-09
     */
    public function modifyState()
    {
        $id         = I('id', 0, 'intval');
        $state      = I('state', false);
        $sid        = $this->loginInfo['sid'];               // 商户id
        $operatorId = $this->loginInfo['memberID'];   //操作人id

        $method  = 'System/SystemAutoSync/modifyState';
        $rpcData = [
            'id'          => $id,
            'sid'         => $sid,
            'state'       => $state,
            'operator_id' => $operatorId,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 规则配置列表
     *
     * <AUTHOR>
     * @date   2023-08-09
     */
    public function getRuleList()
    {
        $page     = I('page', 1, 'intval');
        $limit    = I('limit', 10, 'intval');
        $ruleName = I('rule_name', '', 'string');
        $sid      = $this->loginInfo['sid'];               // 商户id
        $state    = I('state');
        $lid      = I('lid', 0, 'intval');
        $tid      = I('tid', 0, 'intval');
        $csysId   = I('csys_id');

        $method  = 'System/SystemAutoSync/getRuleList';
        $rpcData = [
            'page'      => $page,
            'limit'     => $limit,
            'sid'       => $sid,
            'rule_name' => $ruleName,
            'state'     => $state,
            'lid'       => $lid,
            'tid'       => $tid,
            'csys_id'   => $csysId,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 获取日志
     *
     * <AUTHOR>
     * @date   2023-08-07
     */
    public function getRuleLogList()
    {
        $page      = I('page', 1, 'intval');
        $limit     = I('limit', 10, 'intval');
        $dateStart = I('date_start', false);
        $dateEnd   = I('date_end', false);
        $sid       = $this->loginInfo['sid'];               // 商户id
        $csysId    = I('csys_id');

        $method  = 'System/SystemAutoSync/getRuleLogList';
        $rpcData = [
            'page'       => $page,
            'limit'      => $limit,
            'sid'        => $sid,
            'date_start' => $dateStart,
            'date_end'   => $dateEnd,
            'csys_id'   => $csysId,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 任务列表（规则详情-同步记录）
     *
     * <AUTHOR>
     * @date   2023-08-07
     */
    public function getTaskList()
    {
        $ruleId    = I('rule_id', 0, 'intval');
        $page      = I('page', 1, 'intval');
        $limit     = I('limit', 10, 'intval');
        $dateStart = I('date_start', '', 'string');
        $dateEnd   = I('date_end', '', 'string');
        $state     = I('pull_state');
        $lid       = I('lid', 0, 'intval');
        $tid       = I('tid', 0, 'intval');
        $systemId  = I('system_id', 0, 'intval');
        $sid       = $this->loginInfo['sid'];               // 商户id

        $method  = 'System/SystemAutoSync/getTaskList';
        $rpcData = [
            'rule_id'    => $ruleId,
            'page'       => $page,
            'limit'      => $limit,
            'sid'        => $sid,
            'date_start' => $dateStart,
            'date_end'   => $dateEnd,
            'pull_state' => $state,
            'lid'        => $lid,
            'tid'        => $tid,
            'csys_id'    => $systemId,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 任务详情列表
     *
     * <AUTHOR>
     * @date   2023-08-07
     */
    public function getTaskDetailList()
    {
        $page   = I('page', 1, 'intval');
        $limit  = I('limit', 10, 'intval');
        $sid    = $this->loginInfo['sid'];               // 商户id
        $state  = I('state');
        $taskId = I('task_id', 0, 'intval');

        $method  = 'System/SystemAutoSync/getTaskDetailList';
        $rpcData = [
            'page'    => $page,
            'limit'   => $limit,
            'sid'     => $sid,
            'state'   => $state,
            'task_id' => $taskId,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']);
    }

    /**
     * 门票应用规则
     * 
     */
    public function bindTicket2RuleConf()
    {
        $tid        = I('tid');
        $ruleId     = I('rule_id');
        $isBind     = I('is_bind');  // 0：取消应用   1：应用
        $sid        = $this->loginInfo['sid']; // 商户id
        $operatorId = $this->loginInfo['memberID'];   // 操作人id

        $method  = 'System/SystemAutoSync/bindTicket2RuleConf';
        $rpcData = [
            'tid'         => $tid,
            'rule_id'     => $ruleId,
            'is_bind'     => $isBind,
            'operator_id' => $operatorId
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']); 
    }

    /**
     * 用户人工操作拉取
     * 
     */
    public function pullShowSectionPriceStorageByUser()
    {
        $lid       = I('lid');
        $tidArr    = I('tids');
        $dateStart = I('date_start');
        $dateEnd   = I('date_end'); 
        $pullType  = I('pull_type');
        $sid       = $this->loginInfo['sid']; // 商户id
        $operId    = $this->loginInfo['memberID']; // 商户id

        $method  = 'System/SystemAutoSync/pullShowSectionPriceStorageByUser';
        $rpcData = [
            'lid'         => $lid,
            'tids'        => $tidArr,
            'date_start'  => $dateStart,
            'date_end'    => $dateEnd,
            'pull_type'   => $pullType,
            'operator_id' => $operId,
            'sid'         => $sid
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']); 
    }

    /**
     * 拉取log明细request response
     * 
     */
    public function pullShowSectionPriceStorageLog()
    {
        $taskId = I('task_id');
        $sid    = $this->loginInfo['sid']; // 商户id

        $method  = 'System/SystemAutoSync/pullShowSectionPriceStorageLog';
        $rpcData = [
            'task_id' => $taskId,
        ];

        $rpcRes = $this->callRpc($method, $rpcData);

        if ($rpcRes['code'] == 200) {
            $this->apiReturn(200, $rpcRes['data'], 'success');
        }

        $this->apiReturn(500, $rpcRes['data'], $rpcRes['msg']); 
    }

    /**
     * rpc 请求
     *
     * <AUTHOR>
     * @date 2022-08-31
     *
     * @param  string  $method
     * @param  array  $rpcData
     *
     * @return array
     */
    protected function callRpc(string $method, array $rpcData): array
    {
        $result = [
            'code' => 200,
            'msg'  => 'success',
            'data' => [],
        ];
        try {
            $lib        = new PftRpcClient('ota_supplier');
            $jsonRpcRes = $lib->call($method, [$rpcData], 'ota_supplier');

            $result['data'] = $jsonRpcRes['data'];
            $result['code'] = $jsonRpcRes['code'];
            $result['msg']  = $jsonRpcRes['msg'];
        } catch (Throwable $e) {
            $result['code'] = $e->getCode();
            $result['msg']  = $e->getMessage();
        }

        return $result;
    }
}

?>