<?php

namespace Controller\Ota;

use Library\Controller;

class XinMeiDaV2 extends Controller
{
    private $loginInfo = [];

    //请求参数
    private $_reqParamArr = [];

    public function __construct()
    {
        $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 获取可用库存列表
     *
     * <AUTHOR>
     * @date   2023-08-30
     */
    public function queryStorage()
    {
        //参数校验
        $this->_checkParam();

        $sid       = $this->loginInfo['sid'];//上级用户ID
        $lid       = $this->_reqParamArr['land_id'];
        $tid       = $this->_reqParamArr['ticket_id'];
        $startDate = $this->_reqParamArr['startDate'];
        $endDate   = $this->_reqParamArr['endDate'];

        //todo 需要判断下供应商是否有这个门票的权限

        $biz    = new \Business\Ota\Open\Product();
        $rpcRes = $biz->pullThirdStorage([
            'lid'       => $lid,
            'tid'       => $tid,
            'startDate' => $startDate,
            'endDate'   => $endDate,
            'applyDid'  => $sid,
        ]);

        if ($rpcRes['code'] == 200) {
            //整理数据后返回
            $resData = $this->_dataHandle($rpcRes['data']);
            $this->apiReturn(200, $resData, 'success');
        } else {
            $this->apiReturn(204, $rpcRes['data'], $rpcRes['msg']);
        }
    }

    private function _checkParam()
    {
        $lid       = I('land_id', 0, 'intval');//产品id
        $tid       = I('ticket_id', 0, 'intval');//门票id
        $startDate = I('start_date', date('Y-m-d'));
        $endDate   = I('end_date', date('Y-m-d'));

        //参数判断
        if (!$lid) {
            $this->apiReturn(203, [], '请选择产品');
        }
        if (!$tid) {
            $this->apiReturn(203, [], '请选择门票');
        }

        if (!chk_date($startDate)) {
            $this->apiReturn(203, [], '请选择合法的查询开始日期');
        }

        if (!chk_date($endDate)) {
            $this->apiReturn(203, [], '请选择合法的查询结束日期');
        }

        //日期跨度判断
        $startTimeStamp = strtotime($startDate);
        $endTimeStamp   = strtotime($endDate);
        $todayTimeStamp = strtotime(date("Y-m-d 00:00"));
        if (($endTimeStamp < $startTimeStamp) || ($startTimeStamp < $todayTimeStamp)) {
            $this->apiReturn(203, [], '不支持过去日期查询');
        }

        //最多可查近3个月内的剩余库存 - 按92天算
        $limitDays      = 90;
        $limitTimeStamp = $todayTimeStamp + $limitDays * 86400 - 1;

        if (strtotime($endDate . " 23:59:59") > $limitTimeStamp) {
            $this->apiReturn(203, [], '最多可查近90天内的剩余库存');
        }

        $this->_reqParamArr = [
            'land_id'   => $lid,
            'ticket_id' => $tid,
            'startDate' => $startDate,
            'endDate'   => $endDate,
        ];

        return true;
    }

    /**
     * 处理返回的数据
     * 这个接口返回的数据 - //https://developer-distribution.meituan.com/#/apiDocs/3/37/44
     *
     * @param $dataArr
     *
     * @return array
     */
    private function _dataHandle($dataArr)
    {
        $totalSize     = intval($dataArr['totalSize']);
        $priceInfosArr = $dataArr['priceInfos'] ?? [];

        $resPriceInfosArr = [];
        foreach ($priceInfosArr as $item) {
            $resPriceInfosArr[] = [
                'dealId'    => intval($item['dealId']), //分销开放平台产品ID

                //日期，格式为yyyy-MM-dd。当stockMode=0时，date取固定值1970-01-01
                'date'      => strval($item['date']),

                //库存类型：0：有无库存 1：具体库存
                'stockType' => intval($item['stockType']),

                //当stockType=0时：
                // 0：无库存
                // 1：有库存
                // 当stockType=1时：
                // stock表示具体的库存值，stock=-1表示不限制库存
                'stock'     => intval($item['stock']), //分销开放平台产品ID

                //价格库存模式，含义与产品的预定模式bookType对应：
                //0：有效期模式
                //1：预约模式
                'stockMode' => intval($item['stockMode']),
            ];
        }

        $resData = [
            'totalSize'  => $totalSize,
            'priceInfos' => $resPriceInfosArr,
        ];

        return $resData;
    }

}

?>