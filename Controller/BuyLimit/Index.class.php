<?php
/**
 * 购票限制应用
 * 入口页
 *
 * <AUTHOR>
 * @date 2020-09-02
 *
 */

namespace Controller\BuyLimit;

use Business\MemberLogin\MemberLoginHelper;
use Library\Controller;
use Business\BuyLimit\Index as indexBiz;

class Index extends Controller
{
    private $loginInfo;
    private $_modeArr        = [1, 2]; //限制方式：1:黑名单；2：白名单
    private $_typeArr        = [1, 2]; //黑白名单配置类型： 1区域  2个人
    private $_target         = [1, 2, 3]; //限购配置类型：1：每个手机号；2：每个身份证；3:每个手机号+身份证
    private $_people         = [1, 2]; //限购配置 1是取票人  2是游客
    private $_rules          = ['order', 'order_day', 'tickets', 'tickets_day', 'interval', 'people', 'target','target_dateType'];
    private $_rules_personal = ['name', 'idcard', 'mobile']; //黑白名单限制，按人限制：姓名、身份证、手机号
    private $_buyLimitBiz;

    public function __construct()
    {
        //登录信息
        $this->loginInfo = $this->getLoginInfo('ajax');
        //购票限制业务处理
        $this->_buyLimitBiz = new indexBiz();
    }

    /**
     * 获取限制管理列表
     *
     * <AUTHOR>
     * @date 2020/9/2
     *
     * @return bool
     */
    public function getLimitList()
    {
        $sid   = $this->loginInfo['sid'];
        $mid   = $this->loginInfo['memberID'];
        $lid   = I('get.lid', 0, 'intval'); //景区id
        $tid   = I('get.tid', 0, 'intval'); //门票id
        $type  = I('get.type', -1, 'intval'); // 限购规则类型：1=票限购，2=产品限购
        $state = I('get.state', 0, 'intval'); //状态 1启用 2禁用
        $page  = I('get.page', 1, 'intval'); //页码
        $size  = I('get.size', 10, 'intval'); //页数

        //业务处理
        $result = $this->_buyLimitBiz->getlimitListByLid($sid, $mid, $lid, $tid, $state, $type, $page, $size);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 保存/新增 限制设置的配置
     * 注：后续可以考虑将保存和修改放到同一个接口上
     * <AUTHOR>
     * @date 2020/9/2
     *
     * @return bool
     */
    public function saveLimit()
    {
        $sid   = $this->loginInfo['sid'];
        $mid   = $this->loginInfo['memberID'];
        $lids  = I('post.lids', '', 'strval'); //景区id 多个以逗号隔开
        $tid   = I('post.tid', -1, 'intval'); //门票id
        $rules = I('post.rules'); //配置规则：至少勾选一个

        if (!$lids || !$tid) {
            return $this->apiReturn(203, [], '参数错误');
        }

        //景区id数组
        $lidArr = explode(',', $lids);

        //规则为空，返回提醒
        if (empty($rules)) {
            return $this->apiReturn(203, [], '配置规则至少要勾选一个');
        }

        //验证规则字段
        foreach ($rules as $k => $v) {
            $key = array_keys($v);
            if (!empty(array_diff($key, $this->_rules))) {
                //只要存在一个错误，直接报错返回
                return $this->apiReturn(203, [], '规则参数错误');
            }
            $target = !empty($rules[$k]['target']) ? $rules[$k]['target'] : 0;
            $people = !empty($rules[$k]['people']) ? $rules[$k]['people'] : 0;
            //验证类型
            if (!in_array($target, $this->_target) && $target != 0) {
                return $this->apiReturn(203, [], '类型选择错误');
            }
            if (!in_array($people, $this->_people)) {
                return $this->apiReturn(203, [], '配置选择错误');
            }
            if ($people == 2 && $target != 2 && $target != 0) {
                return $this->apiReturn(203, [], '游客限购只能选择身份证类型');
            }
        }

        //业务处理
        $result = $this->_buyLimitBiz->saveLimit($sid, $mid, $lidArr, $tid, $rules);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除限制配置
     *
     * <AUTHOR>
     * @date 2020/9/2
     *
     * @return bool
     */
    public function delLimit()
    {
        $sid = $this->loginInfo['sid'];
        $mid = $this->loginInfo['memberID'];
        $id  = I('post.id', 0, 'intval'); //配置id

        if (!$id) {
            return $this->apiReturn(203, [], '参数出错');
        }

        //业务处理
        $result = $this->_buyLimitBiz->delLimitById($sid, $mid, $id);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 更新/修改 限制配置
     *
     * <AUTHOR>
     * @date 2020/9/2
     *
     * @return bool
     */
    public function updateLimit()
    {
        $sid   = $this->loginInfo['sid'];
        $mid   = $this->loginInfo['memberID'];
        $id    = I('post.id', 0, 'intval'); //配置id
        $lids  = I('post.lids', '', 'strval'); //景区id 多个以逗号隔开
        $tid  = I('post.tid', -1, 'intval'); //门票id 多个以逗号隔开
        $rules = I('post.rules'); //配置规则：至少勾选一个

        if (!$id || !$tid || !$lids) {
            return $this->apiReturn(203, [], '参数错误');
        }

        //景区id数组
        $lidArr = explode(',', $lids);

        //参数验证
        $this->_verifyParams($rules);

        //业务处理
        $result = $this->_buyLimitBiz->updateLimitById($sid, $mid, $id, $lidArr, $tid, $rules);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取名单列表
     *
     * <AUTHOR>
     * @date 2020/9/2
     *
     * @return bool
     */
    public function getRosterList()
    {
        $sid       = $this->loginInfo['sid'];
        $mid       = $this->loginInfo['memberID'];
        $lid       = I('get.lid', 0, 'intval'); //景区id
        $limitMode = I('get.limit_mode', 0, 'intval'); //1是黑名单；2是白名单
        $page      = I('get.page', 1, 'intval'); //页码
        $size      = I('get.size', 10, 'intval'); //页数
        $itemNum   = I('get.item_num', 20, 'intval'); //显示的名单条目数 默认值 20
        //验证限制方式
        if (!$limitMode || !in_array($limitMode, $this->_modeArr)) {
            return $this->apiReturn(203, [], '限制方式错误');
        }

        //业务处理
        $result = $this->_buyLimitBiz->getRosterList($sid, $mid, $lid, $limitMode, $itemNum, $page, $size);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取名单详情
     *
     * <AUTHOR>
     * @date 2020/9/2
     *
     * @return bool
     */
    public function getRosterInfo()
    {
        $sid       = $this->loginInfo['sid'];
        $mid       = $this->loginInfo['memberID'];
        $lid       = I('get.lid', 0, 'intval'); //景区id
        $limitMode = I('get.limit_mode', 0, 'intval'); //1是黑名单；2是白名单
        $itemNum   = I('get.item_num', 0, 'intval'); //显示的名单条目数 默认值 0

        //验证限制方式
        if (!$limitMode || !in_array($limitMode, $this->_modeArr)) {
            return $this->apiReturn(203, [], '限制方式错误');
        }

        //业务处理
        $result = $this->_buyLimitBiz->getRosterInfoByLid($sid, $mid, $lid, $limitMode, $itemNum);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 保存/新增 名单配置
     *
     * <AUTHOR>
     * @date 2020/9/2
     *
     * @return bool
     */
    public function saveRoster()
    {
        $sid       = $this->loginInfo['sid'];
        $mid       = $this->loginInfo['memberID'];
        $lid       = I('post.lid', 0, 'intval'); //景区id
        $limitMode = I('post.limit_mode', 0, 'intval'); //限制方式：1是黑名单；2是白名单
        $limitType = I('post.limit_type', 0, 'intval'); //限制类型：2个人 1区域
        $rules     = I('post.rules'); //配置规则：至少勾选一个

        if (!$lid || !$limitMode || !$limitType) {
            return $this->apiReturn(203, [], '参数错误');
        }

        //验证规则
        if (empty($rules)) {
            return $this->apiReturn(203, [], '配置规则为空');
        }

        //验证限制类型
        if (!in_array($limitType, $this->_typeArr)) {
            return $this->apiReturn(203, [], '限制类型错误');
        }

        //验证限制方式
        if (!in_array($limitMode, $this->_modeArr)) {
            return $this->apiReturn(203, [], '限制方式错误');
        }

        //业务处理
        $result = $this->_buyLimitBiz->saveRoster($sid, $mid, $lid, $limitMode, $limitType, $rules);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除 名单配置
     *
     * <AUTHOR>
     * @date 2020/9/2
     *
     * @return bool
     */
    public function delRoster()
    {
        $sid       = $this->loginInfo['sid'];
        $mid       = $this->loginInfo['memberID'];
        $id        = I('post.id', 0, 'intval'); //名单配置id
        $limitMode = I('post.limit_mode', 0, 'intval'); //限制方式：1是黑名单；2是白名单

        if (!$limitMode || !$id) {
            return $this->apiReturn(203, [], '参数错误');
        }

        //验证限制方式
        if (!in_array($limitMode, $this->_modeArr)) {
            return $this->apiReturn(203, [], '限制方式错误');
        }

        //业务处理
        $result = $this->_buyLimitBiz->delRosterById($sid, $mid, $id, $limitMode);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 更新/修改 名单配置
     *
     * <AUTHOR>
     * @date 2020/9/2
     *
     * @return bool
     */
    public function updateRoster()
    {
        $sid       = $this->loginInfo['sid'];
        $mid       = $this->loginInfo['memberID'];
        $id        = I('post.id', 0, 'intval'); //名单配置id
        $lid       = I('post.lid', 0, 'intval'); //景区id
        $limitMode = I('post.limit_mode', 0, 'intval'); //限制方式：1是黑名单；2是白名单
        $limitType = I('post.limit_type', 0, 'intval'); //限制类型：1个人 2区域
        $createArr = I('post.create_rules'); //新增
        $updateArr = I('post.update_rules'); //更新
        $removeArr = I('post.remove_rules'); //移除

        if (!$id || !$limitMode || !$limitType || !$lid) {
            return $this->apiReturn(203, [], '参数错误');
        }

        //验证规则
        if (empty($createArr) && empty($updateArr) && empty($removeArr)) {
            return $this->apiReturn(203, [], '无效操作');
        }

        //验证限制类型
        if (!in_array($limitType, $this->_typeArr)) {
            return $this->apiReturn(203, [], '限制类型错误');
        }

        //验证限制方式
        if (!in_array($limitMode, $this->_modeArr)) {
            return $this->apiReturn(203, [], '限制方式错误');
        }
        //规则组合
        $rules = [
            'create' => empty($createArr) ? [] : $createArr,
            'update' => empty($updateArr) ? [] : $updateArr,
            'remove' => empty($removeArr) ? [] : $removeArr,
        ];
        //业务处理
        $result = $this->_buyLimitBiz->updateRosterById($sid, $mid, $lid, $id, $limitMode, $limitType, $rules);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 导入名单
     *
     * <AUTHOR>
     * @date 2020/9/2
     *
     * @return bool
     */
    public function importRoster()
    {
        $sid       = $this->loginInfo['sid'];
        $mid       = $this->loginInfo['memberID'];
        $id        = I('post.id', 0, 'intval'); //名单配置id
        $lid       = I('post.lid', 0, 'intval'); //景区id
        $limitType = I('post.limit_type', 0, 'intval'); //限制类型：1区域 2个人
        $limitMode = I('post.limit_mode', 0, 'intval'); //限制方式：1是黑名单；2是白名单
        $file      = $_FILES['file'];

        //验证限制类型
        if (!in_array($limitType, [1, 2])) {
            return $this->apiReturn(203, [], '限制类型错误');
        }

        //验证限制方式
        if (!in_array($limitMode, $this->_modeArr)) {
            return $this->apiReturn(203, [], '请选择限制方式');
        }

        //验证景区id不为空
        if (!$lid) {
            return $this->apiReturn(203, [], '请选择产品');
        }

        //验证文件
        if (!$file || $file['error'] != UPLOAD_ERR_OK) {
            return $this->apiReturn(203, [], '上传文件异常，请重新上传');
        }

        //新增文件大小限制检查（2MB）
        $maxFileSize = 2 * 1024 * 1024; // 2MB
        if ($file['size'] > $maxFileSize) {
            return $this->apiReturn(203, [], '文件大小不能超过2MB');
        }

        //请求处理
        $result = $this->_buyLimitBiz->importExcel($sid, $mid, $lid, $limitType, $limitMode, $file, $id);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 下载Excel模板
     *
     * <AUTHOR>
     * @date 2020/9/2
     *
     *   type：1是黑名单模板；2是白名单模板
     *   excel_type：1区域 2个人
     *
     */
    public function downloadExcel()
    {
        $type      = I('get.type', 0, 'intval');
        $excelType = I('get.excel_type', 0, 'intval');

        if (!$type || !in_array($type, [1, 2])) {
            $this->apiReturn('203', [], '参数缺失');
        }

        if (!$excelType || !in_array($excelType, [1, 2])) {
            $this->apiReturn('203', [], '参数缺失');
        }

        if ($type == 1) {
            $sheetName = '导入黑名单模板';
        } else {
            $sheetName = '导入白名单模板';
        }

        $areaData = [];
        $isArea   = false;
        $subtitle = '';
        if ($excelType == 2) {
            $sheetName .= '-个人-身份证、手机号有填写一个即可';
            $title     = ['身份证(防止数字被格式化,先填个\'+身份证号)', '手机号'];
        } else {
            $isArea    = true;
            $subtitle  = '区域参数';
            $sheetName .= '-区域';
            $title     = ['省', '市', '区'];
            $areaData  = $this->_getAreaData();
        }

        $this->_excelFileCreate($sheetName, $subtitle, $title, $isArea, $areaData);
    }

    /**
     * 购票限制 启用/停用
     * <AUTHOR>
     * @date 2021/2/4
     *
     */
    public function setLimitState()
    {
        $id    = I('post.id', 0, 'intval'); //配置id
        $state = I('post.state', 0, 'intval'); //状态1启用，2停用
        if (!$id || !$state) {
            $this->apiReturn('203', [], '参数缺失');
        }
        if (!in_array($state, $this->_buyLimitBiz::LIMIT_STATE)) {
            $this->apiReturn('203', [], '状态错误');
        }
        $sid = $this->loginInfo['sid'];
        $mid = $this->loginInfo['memberID'];

        //请求处理
        $result = $this->_buyLimitBiz->setLimitStateById($sid, $mid, $id, $state);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取名单详情
     * <AUTHOR>
     * @date 2021/2/7
     *
     */
    public function getRosterDetails()
    {
        $id        = I('get.id', 0, 'intval'); //配置id
        $limitMode = I('get.limit_mode', 0, 'intval'); //限制方式：1是黑名单；2是白名单

        if (!$id) {
            $this->apiReturn(203, [], '名单信息错误');
        }
        //验证限制方式
        if (!in_array($limitMode, $this->_modeArr)) {
            $this->apiReturn(203, [], '请选择限制方式');
        }
        $sid = $this->loginInfo['sid'];
        $mid = $this->loginInfo['memberID'];
        //请求处理
        $result = $this->_buyLimitBiz->getRosterDetailsById($sid, $mid, $id, $limitMode);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取配置内数据列表
     * <AUTHOR>
     * @date 2021/2/7
     */
    public function getRosterDetailsInfoList()
    {
        $id       = I('get.id', 0, 'intval'); //配置id
        $name     = I('get.name', 0, 'strval'); //姓名搜索
        $idnum    = I('get.idnum', 0, 'strval'); //身份证搜索
        $province = I('get.province', 0, 'intval'); //省Code搜索
        $city     = I('get.city', 0, 'intval'); //市Code搜索
        $county   = I('get.county', 0, 'intval'); //区县Code搜索
        $page     = I('get.page', 1, 'intval'); //页码
        $size     = I('get.size', 10, 'intval'); //页数
        $phone    = I('get.phone', '', 'strval'); //手机号
        $isExcel  = I('get.is_excel', 0, 'intval'); //是否导出

        $page = $page < 1 ? 1 : $page;
        $size = $size < 1 ? 10 : $size;
        if (!$id) {
            $this->apiReturn(203, [], '名单信息错误');
        }
        $sid = $this->loginInfo['sid'];
        $mid = $this->loginInfo['memberID'];

        $searchData = [
            'name'     => $name,
            'idnum'    => $idnum,
            'province' => $province,
            'city'     => $city,
            'county'   => $county,
            'phone'   => $phone,
        ];

        //请求处理
        $result = $this->_buyLimitBiz->getRosterDetailsInfoListById($sid, $mid, $id, $searchData, $isExcel, $page,
            $size);

        if ($isExcel && $result['code'] == 200) {
            $exportData  = empty($result['data']['list']) ? [] : $result['data']['list'];
            $subject     = '限制名单';
            $subtitle    = '区域参数';
            $titlePeople = ['身份证号', '手机号', '操作人(账号/名称)', '启用时间'];
            $titleArea   = ['省', '市', '区', '操作人(账号/名称)', '启用时间'];
            //excel抬头定义
            $limitType = $result['data']['limitType'];
            $areaData  = [];
            $isArea    = false;
            if ($limitType == 1) {
                $title    = $titleArea;
                $areaData = $this->_getAreaData();
                $isArea   = true;
            } else {
                $title = $titlePeople;
            }
            $this->_excelFileCreate($subject, $subtitle, $title, $isArea, $areaData, $exportData);
        } else {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
    }

    /**
     * 名单 启用/停用
     * <AUTHOR>
     * @date 2021/2/8
     *
     */
    public function setRosterState()
    {
        $id        = I('post.id', 0, 'intval'); //配置id
        $limitMode = I('post.limit_mode', 0, 'intval'); //限制方式：1是黑名单；2是白名单
        $state     = I('post.state', 0, 'intval'); //状态1启用，2停用

        //参数验证
        if (!$id || !$state || !$limitMode) {
            $this->apiReturn('203', [], '参数缺失');
        }
        //状态验证
        if (!in_array($state, $this->_buyLimitBiz::LIMIT_STATE)) {
            $this->apiReturn('203', [], '状态错误');
        }
        //验证限制方式
        if (!in_array($limitMode, $this->_modeArr)) {
            $this->apiReturn(203, [], '请选择限制方式');
        }

        $sid = $this->loginInfo['sid'];
        $mid = $this->loginInfo['memberID'];
        //请求处理
        $result = $this->_buyLimitBiz->setRosterStateById($sid, $mid, $id, $limitMode, $state);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 票属性展示
     * 注：票属性查询是否展示购票限制
     * <AUTHOR>
     * @date 2021/2/8
     *
     */
    public function getLandLimit()
    {
        $lid = I('post.lid', 0, 'intval'); //景区id
        $gid = I('post.gid', 0, 'intval'); //商品id

        //参数验证
        if (!$lid && !$gid) {
            $this->apiReturn('203', [], '参数缺失');
        }

        $sid = $this->loginInfo['sid'];
        $mid = $this->loginInfo['memberID'];

        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $mid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        //请求处理
        $result = $this->_buyLimitBiz->getLandLimitByLandId($sid, $mid, $lid, $gid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取景区下的全部可设置的票信息
     * <AUTHOR>
     * @date 2021/2/8
     *
     */
    public function getLandTickets()
    {
        $lid    = I('post.lid', 0, 'intval'); //景区id
        $filter = I('post.is_filter', 1, 'intval'); //是否过滤 1是0否

        //参数验证
        if (!$lid) {
            $this->apiReturn('203', [], '参数缺失');
        }

        $sid = $this->loginInfo['sid'];
        $mid = $this->loginInfo['memberID'];
        //请求处理
        $result = $this->_buyLimitBiz->getLandTicketsByLandId($sid, $mid, $lid, $filter);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * Excel 文件生成，导出文件
     * <AUTHOR>
     * @date 2021/2/4
     *
     * @param  string  $subject  主题
     * @param  string  $subtitle  下拉数据表名称
     * @param  array  $title  字段数组
     * @param  bool  $isArea  是否下拉
     * @param  array  $areaData  地区数据
     * @param  array  $exportData  导出数据
     *
     * @throws
     */
    private function _excelFileCreate(string $subject, string $subtitle, array $title = [], bool $isArea = false, array $areaData = [], array $exportData = [])
    {
        $data        = $areaData;
        $high        = 0;
        $objPHPExcel = new \PHPExcel();
        $col         = [
            'A',
            'B',
            'C',
            'D',
            'E',
            'F',
            'G',
            'H',
            'I',
            'J',
            'K',
            'L',
            'M',
            'N',
            'O',
            'P',
            'Q',
            'R',
            'S',
            'T',
            'U',
            'V',
            'W',
            'X',
            'Y',
            'Z',
        ];
        $colA        = $col;
        $colB        = $col;
        //获取全部列
        foreach ($colA as $c) {
            foreach ($colB as $d) {
                $col[] = $c . $d;
            }
        }
        //获取第一行
        $titleRow = [];
        foreach ($col as $l) {
            $titleRow[] = $l . '1';
        }

        //需要下拉的列
        $cityRow     = 'A'; //市
        $countyRow   = 'B'; //区
        $provinceRow = 'C'; //省

        //第一行赋值 抬头
        for ($a = 0; $a < count($title); $a++) {
            //超出处理了
            if (!isset($titleRow[$a])) {
                continue;
            }
            if ($isArea) {
                if ($title[$a] == '省' && isset($col[$a])) {
                    $provinceRow = $col[$a];
                }
                if ($title[$a] == '区' && isset($col[$a])) {
                    $countyRow = $col[$a];
                }
                if ($title[$a] == '市' && isset($col[$a])) {
                    $cityRow = $col[$a];
                }
            }

            $objPHPExcel->setActiveSheetIndex(0)->setCellValue($titleRow[$a], $title[$a]);
        }
        //重命名表
        $objPHPExcel->getActiveSheet()->setTitle($subject);
        if ($isArea && !empty($data)) {
            //创建下拉数据表
            $supportSheet = new \PHPExcel_Worksheet($objPHPExcel, $subtitle); //创建一个工作表
            $objPHPExcel->addSheet($supportSheet); //插入工作表
            //开启表保护
            $objPHPExcel->getSheetByName($subtitle)->getProtection()->setSheet(true);
            //获取使用的最大列数
            $maxfiled = 0;
            foreach ($data as $key => $first) {
                $objPHPExcel->getSheetByName($subtitle)->setCellValue($col[0] . ($key + 2 + $high), $first['name']);
                $objPHPExcel->getSheetByName($subtitle)->getStyle($col[0] . ($key + 2 + $high))->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_WHITE);
                $max            = 0; //重置max
                $first['child'] = (array)$first['child'];
                $secondNum      = count($first['child']);
                //对比获取最大列数
                if ($maxfiled < $secondNum) {
                    $maxfiled = $secondNum;
                }
                foreach ($first['child'] as $index => $second) {
                    $objPHPExcel->getSheetByName($subtitle)->setCellValue($col[$index + 1] . ($key + 2 + $high),
                        $second['name']);
                    $objPHPExcel->getSheetByName($subtitle)->getStyle($col[$index + 1] . ($key + 2 + $high))->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_WHITE);
                    $second['child'] = (array)$second['child'];
                    $thirdNum        = count($second['child']);
                    if ($thirdNum > $max) {
                        $max = $thirdNum;
                    }
                    foreach ($second['child'] as $id => $third) {
                        $objPHPExcel->getSheetByName($subtitle)->setCellValue($col[$index + 1] . ($key + 2 + $high + $id + 1),
                            $third);
                        $objPHPExcel->getSheetByName($subtitle)->getStyle($col[$index + 1] . ($key + 2 + $high + $id + 1))->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_WHITE);
                    }
                    //定义三级名称
                    $objPHPExcel->addNamedRange(
                        new \PHPExcel_NamedRange(
                            $second['name'],
                            $objPHPExcel->getSheetByName($subtitle),
                            $col[$index + 1] . ($key + 2 + $high + 1) . ':' . $col[$index + 1] . ($key + 2 + $high + 1 + $thirdNum - 1)
                        )
                    );
                }
                //定义二级名称
                $objPHPExcel->addNamedRange(
                    new \PHPExcel_NamedRange(
                        $first['name'],
                        $objPHPExcel->getSheetByName($subtitle),
                        $col[1] . ($key + 2 + $high) . ':' . $col[1 + $secondNum - 1] . ($key + 2 + $high)
                    )
                );
                $high += $max;
            }

            //写入一级数据  放在第一行，从第2列开始
            foreach ($data as $var => $content) {
                $objPHPExcel->getSheetByName($subtitle)->setCellValue($col[$var + 1] . '1', $content['name']);
                $objPHPExcel->getSheetByName($subtitle)->getStyle($col[$var + 1] . '1')->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_WHITE);
            }
            $total    = count($data);
            $maxfiled = $maxfiled > $total ? $maxfiled : $total;
            //设置下拉数据保护密码  66666
            $objPHPExcel->getSheetByName($subtitle)->protectCells("A:" . $col[$maxfiled], '666666');

            //定义一级名称
            $objPHPExcel->addNamedRange(
                new \PHPExcel_NamedRange(
                    '全部省',
                    $objPHPExcel->getSheetByName($subtitle),
                    $col[1] . '1' . ':' . $col[$total] . '1'
                )
            );

            //下拉数据写入
            $maxLine = 1001; //设置上限1000行
            for ($i = 2; $i <= $maxLine; $i++) {
                $objValidation = $objPHPExcel->getSheetByName($subject)->getCell($provinceRow . $i)->getDataValidation();
                $objValidation->setType(\PHPExcel_Cell_DataValidation::TYPE_LIST);
                $objValidation->setErrorStyle(\PHPExcel_Cell_DataValidation::STYLE_INFORMATION);
                $objValidation->setAllowBlank(false);
                $objValidation->setShowInputMessage(true);
                $objValidation->setShowErrorMessage(true);
                $objValidation->setShowDropDown(true);
                $objValidation->setErrorTitle('输入错误');
                $objValidation->setError('不在列表中的值');
                $objValidation->setPromptTitle('请选择');
                $objValidation->setPrompt('请从列表中选择一个值.');
                $objValidation->setFormula1("=全部省");

                $objValidation = $objPHPExcel->getSheetByName($subject)->getCell($cityRow . $i)->getDataValidation();
                $objValidation->setType(\PHPExcel_Cell_DataValidation::TYPE_LIST);
                $objValidation->setErrorStyle(\PHPExcel_Cell_DataValidation::STYLE_INFORMATION);
                $objValidation->setAllowBlank(false);
                $objValidation->setShowInputMessage(true);
                $objValidation->setShowErrorMessage(true);
                $objValidation->setShowDropDown(true);
                $objValidation->setErrorTitle('输入错误');
                $objValidation->setError('不在列表中的值');
                $objValidation->setPromptTitle('请选择');
                $objValidation->setPrompt('请从列表中选择一个值.');
                $objValidation->setFormula1('=INDIRECT($' . $provinceRow . '$' . $i . ')');

                $objValidation = $objPHPExcel->getSheetByName($subject)->getCell($countyRow . $i)->getDataValidation();
                $objValidation->setType(\PHPExcel_Cell_DataValidation::TYPE_LIST);
                $objValidation->setErrorStyle(\PHPExcel_Cell_DataValidation::STYLE_INFORMATION);
                $objValidation->setAllowBlank(false);
                $objValidation->setShowInputMessage(true);
                $objValidation->setShowErrorMessage(true);
                $objValidation->setShowDropDown(true);
                $objValidation->setErrorTitle('输入错误');
                $objValidation->setError('不在列表中的值');
                $objValidation->setPromptTitle('请选择');
                $objValidation->setPrompt('请从列表中选择一个值.');
                $objValidation->setFormula1('=INDIRECT($' . $cityRow . '$' . $i . ')');
            }
        }

        //写入表数据
        if (!empty($exportData)) {
            foreach ($exportData as $l => $content) {
                foreach ($content as $k => $v) {
                    $rowNum  = $l + 2;
                    $rowName = $col[$k];
                    $objPHPExcel->getSheetByName($subject)->setCellValue("$rowName$rowNum", (string)$v);
                }
            }
        }

        $objPHPExcel->setActiveSheetIndex(0);
        //输出表格
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename=' . $subject . '' . date('Ymd') . '.xlsx');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $objWriter->save('php://output');
    }

    /**
     * 获取区域数据
     * <AUTHOR>
     * @date 2021/2/20
     *
     * @return array
     */
    private function _getAreaData()
    {
        $areaData = [];
        $result   = $this->_buyLimitBiz->getAllArea();
        $city     = !empty($result['city']) ? $result['city'] : [];
        $county   = !empty($result['county']) ? $result['county'] : [];
        $province = !empty($result['province']) ? $result['province'] : [];
        foreach ($province as $k => $v) {
            $item         = [];
            $item['name'] = $v;
            if (!isset($city[$k])) {
                continue;
            }
            foreach ($city[$k] as $x => $c) {
                $child         = [];
                $child['name'] = $c;
                if (!isset($county[$x])) {
                    continue;
                }
                foreach ($county[$x] as $g) {
                    $child['child'][] = $g;
                }
                $item['child'][] = $child;
            }
            $areaData[] = $item;
        }

        return $areaData;
    }

    /**
     * 购票限制 参数验证
     * <AUTHOR>
     * @date 2021/2/20
     *
     * @param  array  $rules
     *
     * @return bool
     */
    private function _verifyParams(array $rules)
    {
        //规则为空，返回提醒
        if (empty($rules)) {
            $this->apiReturn(203, [], '配置规则至少要勾选一个');
        }

        //验证规则字段
        foreach ($rules as $k => $v) {
            $key = array_keys($v);
            if (!empty(array_diff($key, $this->_rules))) {
                //只要存在一个错误，直接报错返回
                $this->apiReturn(203, [], '规则参数错误');
            }
            $target = !empty($rules[$k]['target']) ? $rules[$k]['target'] : 0;
            $people = !empty($rules[$k]['people']) ? $rules[$k]['people'] : 0;
            //验证类型
            if (!in_array($target, $this->_target) && $target != 0) {
                $this->apiReturn(203, [], '类型选择错误');
            }
            if (!in_array($people, $this->_people)) {
                $this->apiReturn(203, [], '配置选择错误');
            }
            if ($people == 2 && $target != 2 && $target != 0) {
                $this->apiReturn(203, [], '游客限购只能选择身份证类型');
            }
        }

        return true;
    }
    /**
     * 是否开通应用
     * <AUTHOR>
     * @date 2021/11/01
     */
    public function checkUseModule()
    {
        $sid = $this->loginInfo['sid'];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }
        $result = (new \Business\BuyLimit\Index())->checkUseModule($sid);

        $this->apiReturn(200, $result);
    }

    /**
     * 根据票id获取购票限制信息
     * <AUTHOR>
     * @date 2021/11/01
     */
    public function getProductBuyLimitInfo()
    {
        $tid       = I('post.tid', 0, 'intval'); //票id
        $cacheKay  = I('post.flag', '', 'strval');

        $result = (new \Business\BuyLimit\Index())->getProductBuyLimitInfo($tid, $cacheKay);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    /**
     * 查询景区ID批量查询景区限购规则（返回关联限购产品名称,过滤掉当前查询景区）
     * <AUTHOR>
     * @date 2021/11/01
     */
    public function queryLandConfigAndRelationByLandIds()
    {
        $lands = I('post.lids', []); //景区id
        if (!is_array($lands) || !$lands) {
            $this->apiReturn(203, [], '参数错误');
        }else{
            $landsArr = [];
            foreach ($lands as $v){
                $landsArr[] = intval($v);
            }
        }
        $result = (new \Business\JavaApi\Ota\Product())->queryLandConfigAndRelationByLandIds($landsArr);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}