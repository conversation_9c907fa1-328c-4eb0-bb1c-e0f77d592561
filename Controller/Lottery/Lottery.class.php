<?php
/**
 * 抽奖相关接口
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 2018/7/23
 * Time: 18:22
 */

namespace Controller\Lottery;

use Business\JavaApi\Member\MemberQuery;
use Endroid\QrCode\Exceptions\ImageFunctionFailedException;
use Library\Controller;
use Model\Lottery\Lottery as LotteryModel;
use Business\Lottery\Lottery as LotteryBiz;
use Model\Member\Member;
use Process\Lottery\Lottery as LotteryProcess;

class Lottery extends Controller
{

    private $_memberId;
    private $_sid;
    private $_activityId;
    private $_lotteryBiz;

    public function __construct()
    {
        //为了便于测试做并发测试，留test参数判断是否测试并发
//        if(I('test', 0, 'intval')){
//            $this->_memberId = I('memberID', 0, 'intval');
//        }else{
        parent::__construct();
        $loginInfo       = $this->getLoginInfo('ajax');
        $this->_memberId = $loginInfo['memberID'];
        $this->_sid      = $loginInfo['sid'];
//        }
        $activityId        = I('activityId', 0, 'int');
        $this->_activityId = $activityId;
    }

    /***
     * 创建保存抽奖活动以及奖项信息
     */
    public function saveLotteryInfo()
    {
        $request = I('post.');

        if (!$request || !$request['activityInfo']) {
            $this->apiReturn(203, [], '未接收到活动信息');
        }

        if (!$request['prizeList'] || !$request['winProbability']) {
            $this->apiReturn(203, [], '未接收到奖项信息');
        }

        $activityInfo   = $request['activityInfo'];
        $prizeList      = $request['prizeList'];
        $winProbability = $request['winProbability'];

        if (!is_array($prizeList) || count($prizeList) < 1) {
            $this->apiReturn(203, [], '奖项信息为空');
        }

        // 检查安慰奖与不限奖设置以及中奖率的合法性
        $checkRes = LotteryProcess::checkUnlimitedAndConsolationPrize($prizeList, $winProbability);
        if ($checkRes['code'] != 200) {
            $this->apiReturn(203, [], $checkRes['msg']);
        }

        // 计算各个奖项中奖率
        $probabilityList = LotteryProcess::calculatePrizeProbability($prizeList, $winProbability);

        $lotteryBiz = new LotteryBiz();

        $activityData = [
            'title'      => $activityInfo['title'],
            'content'    => $activityInfo['content'],
            'startTime'  => $activityInfo['startTime'],
            'endTime'    => $activityInfo['endTime'],
            'mid'        => $this->_sid,
            'percentage' => $winProbability,
        ];

        //保存或添加活动信息
        if ($this->_activityId == 0) {
            $activityRes = $lotteryBiz->createLotteryActivity($this->_sid, $activityData);
        } else {
            $activityRes = $lotteryBiz->updateLotteryActivity($this->_sid, $this->_activityId, $activityData);
        }

        if (false === $activityRes) {
            $this->apiReturn(201, [], '创建或修改抽奖活动信息失败');
        }

        //保存奖品信息
        $newPrizeList = array();
        foreach ($prizeList as $tmpPrize) {
            $prize = [
                'prize'         => $tmpPrize['rank'] + 1,
                'activityId'    => $this->_activityId ?: $activityRes,
                'probability'   => $probabilityList[$tmpPrize['rank']],
                'remain'        => $tmpPrize['num'],
                'total'         => $tmpPrize['num'],
                'memo'          => $tmpPrize['memo'],
                'title'         => $tmpPrize['title'],
                'type'          => $tmpPrize['type'],
                'isConsolation' => $tmpPrize['isConsolation'],
                'status'        => 0,
            ];

            $addRes = $lotteryBiz->addOnePrizeData($this->_activityId ?: $activityRes, $prize);
            if (false === $addRes) {
                $this->apiReturn(201, [], '添加或保存奖项信息失败');
            }
            $newPrizeList[] = $prize;
        }
        // 将旧的多余的奖项状态改为删除
        $delRes = $lotteryBiz->deleteRedundancyPrize($newPrizeList, $this->_activityId);
        if ($delRes) {
            //建立奖池
            $lotteryBiz->createOrUpdatePrizePool($this->_activityId ?: $activityRes);
        } else {
            $this->apiReturn(201, [], '修改奖项信息失败');
        }

        $this->apiReturn(200, [], 'success');
    }

    /***
     * 获取抽奖活动列表
     */
    public function getLotteryActivityList()
    {

        $lotteryBiz   = $this->_getLotteryBiz();
        $activityList = $lotteryBiz->getActivityList($this->_sid);
        $this->apiReturn(200, $activityList, 'success');
    }

    /***
     * 获取一次抽奖活动的信息
     */
    public function getLotteryAndPrizeInfo()
    {
        $manager = I('manager', 0, 'int');
        if ($this->_activityId == 0) {
            $this->apiReturn(203, [], '无效活动id');
        }
        $lotteryBiz       = new LotteryBiz();
        $activityInfoList = $lotteryBiz->getOneLotteryActivityInfo($this->_activityId, $manager, $this->_sid);
        $this->apiReturn(200, $activityInfoList, 'success');
    }

    /***
     * 进行一次抽奖
     */
    public function lotteryResult()
    {
        if ($this->_activityId == 0) {
            $this->apiReturn(203, [], '无效活动id');
        }

        $lotteryBiz = new LotteryBiz();

        // 验证活动有效性
        $activityInfo = $lotteryBiz->getOneActivityInfo($this->_activityId);
        if (time() > strtotime($activityInfo['endTime']) || time() < strtotime($activityInfo['startTime']) || $activityInfo['status'] != 0) {
            $this->apiReturn(204, [], '活动已过期或未开始');
        }

        // 验证抽奖资格获取用户可抽奖信息
        $memberLotterInfo = $lotteryBiz->hasLotteryQualification($this->_memberId, $this->_activityId);
        if (!$memberLotterInfo) {
            $this->apiReturn(204, [], '该用户没有抽奖权限');
        }

        $remainNum = $memberLotterInfo['remainNum'];
        if ($remainNum <= 0) {
            $this->apiReturn(204, [], '该用户抽奖次数用完');
        }

        // 用户抽奖锁--3秒防刷
        $lock  = "lock:lottery|activity:{$this->_activityId}|memberId:{$this->_memberId}";
        $cache = \Library\Cache\Cache::getInstance('redis');
        if (!$cache->lock($lock, 1, 3)) {
            $this->apiReturn(204, [], '抽奖中请勿重复提交');
        }

        // 生成抽奖结果
        $prizeResult = $lotteryBiz->getOneLotteryResult($this->_activityId);
        if ($prizeResult['code'] != 200) {
            $this->apiReturn($prizeResult['code'], [], $prizeResult['msg']);
        }

        $prize = $prizeResult['data'];
        if (!$prize) {
            $this->apiReturn(200, [], '所有奖品已发放完');
        }

        //更新本次抽奖相关数据
        $updateRes = $lotteryBiz->updateOneTimeLotteryData($this->_activityId, $this->_memberId, $prize);
        if ($updateRes['code'] != 200) {
            pft_log('lottery/error',
                "抽奖数据更新失败:活动id:{$this->_activityId}|memberId:{$this->_memberId}|msg:{$updateRes['msg']}");
            $this->apiReturn(201, [], $updateRes['msg']);
        }

        $result = [
            'title'   => $prize['title'],
            'prize'   => $prize['prize'],
            'content' => $prize['memo'],
            'remain'  => --$remainNum // 用户剩余抽奖次数
        ];

        pft_log('lottery/result', "中奖记录:活动id:{$this->_activityId}|memberId:{$this->_memberId} " . json_encode($result));

        $this->apiReturn(200, $result, 'success');
    }

    /***
     * 获取抽奖记录列表
     */
    public function getLotteryResultList()
    {

        $page = I('page', 1, 'int');
        $size = I('size', 10, 'int');
        if ($this->_activityId == 0) {
            $this->apiReturn(203, [], '无效活动id');
        }
        $lotteryBiz = $this->_getLotteryBiz();
        $recordList = $lotteryBiz->getLotteryRecordList($this->_sid, $this->_activityId, 0, true, $page, $size);
        $this->apiReturn(200, $recordList, 'success');
    }

    /***
     * 获取抽奖页面展示用的中奖记录
     */
    public function getLotteryRecordForShow()
    {
        $showFlag = I('showFlag', 0, 'int');

        if ($this->_activityId == 0) {
            $this->apiReturn(203, [], '无效活动id');
        }

        if ($showFlag != 1) {
            $this->apiReturn(203, [], '缺少展示标识');
        }

        $lotteryBiz = $this->_getLotteryBiz();

        // 生成包含虚拟的中奖记录
        $returnList = $lotteryBiz->getMixRecordList($this->_memberId, $this->_activityId, $showFlag, 300);

        $this->apiReturn(200, $returnList, 'success');
    }

    /***
     * 添加可以参与抽奖的会员或者增加次数
     */
    public function addQualifiedMember()
    {

        $participant = I('participant', 0, 'int');
        if (!$participant) {
            $this->apiReturn(203, [], '抽奖用户缺失');
        }
        $lotteryBiz = new LotteryBiz();
        // 添加参与者或者增加次数
        $res = $lotteryBiz->addQualifiedMemberOrNum($this->_activityId, $participant);

        if (false === $res) {
            $this->apiReturn(201, [], '添加抽奖用户失败');
        }

        $this->apiReturn(200, [], 'success');
    }

    /***
     * 添加参与人处搜索用户
     */
    public function searchMember()
    {
        $keyword = I('keyword');
        $page    = I('page', 1);
        $size    = I('size', 10);

        $memberModel = new MemberQuery();
        $memberInfos = $memberModel->queryMemberListByFidsOrDnameAndDTypes($keyword, [0, 1, 5, 6, 9], [], $page, $size);

        $this->apiReturn(200, $memberInfos['list'], 'success');
    }

    /***
     * 获取有资格参与抽奖的用户
     */
    public function getQualifiedMemberList()
    {

        if ($this->_activityId == 0) {
            $this->apiReturn(203, [], '无效活动id');
        }
        $lotteryBiz   = $this->_getLotteryBiz();
        $activityList = $lotteryBiz->getQualifiedMemberList($this->_sid, $this->_activityId);
        $this->apiReturn(200, $activityList, 'success');
    }

    /***
     * 中奖活动兑奖标记
     */
    public function expriyAPrize()
    {

        $recordId = I('recordId', 0, 'int');
        if ($this->_activityId == 0) {
            $this->apiReturn(203, [], '无效活动id');
        }

        if (!$recordId) {
            $this->apiReturn(203, [], '记录id缺失');
        }
        $lotteryBiz = new LotteryBiz();
        $res        = $lotteryBiz->expriyOneRecord($this->_activityId, $recordId, $this->_sid);
        if (false === $res) {
            $this->apiReturn(201, [], '兑奖信息修改失败');
        }

        $this->apiReturn(200, [], 'success');
    }

    /***
     * 修改抽奖活动状态
     */
    public function changePutawayStatus()
    {

        $status = I('status', 0, 'int');
        if ($this->_activityId == 0) {
            $this->apiReturn(203, [], '无效活动id');
        }

        $lotteryBiz = new LotteryBiz();
        $res        = $lotteryBiz->changeActivityStatus($this->_sid, $this->_activityId, $status);
        if (false === $res) {
            $this->apiReturn(201, [], '修改失败');
        }

        $this->apiReturn(200, [], 'success');
    }

    /***
     * 获取用户自己的中奖记录
     */
    public function getMyLotteryRecords()
    {
        if ($this->_activityId == 0) {
            $this->apiReturn(203, [], '无效活动id');
        }
        $lotteryBiz    = new LotteryBiz();
        $myRecords     = $lotteryBiz->getMyLotteryRecords($this->_memberId, $this->_activityId);
        $myLotteryInfo = $lotteryBiz->hasLotteryQualification($this->_memberId, $this->_activityId);

        $return = [
            'myRecords'     => $myRecords,
            'myLotteryInfo' => $myLotteryInfo,
        ];

        $this->apiReturn(200, $return, 'success');
    }

    /***
     * 有权限登录后台的账号
     *
     * @param $fid
     *
     * @return bool
     */
    private function _checkBackAuthor($fid)
    {
        if (in_array(ENV, ['PRODUCTION', 'TEST'])) {
            $admin = [3385, 4396, 39924];
        } else {
            $admin = [3385, 6970];
        }

        return in_array($fid, $admin);
    }

    /***
     * 获取biz
     * @return LotteryBiz
     */
    private function _getLotteryBiz()
    {
        if (!isset($this->_lotteryBiz)) {
            $this->_lotteryBiz = new LotteryBiz();
        }

        return $this->_lotteryBiz;
    }

}