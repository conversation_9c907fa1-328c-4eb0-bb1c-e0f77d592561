<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 4/8-008
 * Time: 10:01
 */

namespace Controller;

class Login
{

    function random($len) {
        $srcstr="ABCDEFGHIJKLMNPQRSTUVWXYZ123456789";
        mt_srand();
        $strs="";
        for($i=0;$i<$len;$i++) {
            $strs.=$srcstr[mt_rand(0,33)];
        }
        return ($strs);
    }

    //生成验证码方法
    public function getCode(){
        //判断来源地址
        $referer = I('server.HTTP_REFERER');
        $host    = I('server.HTTP_HOST');
        if(strrpos($referer, $host) === false) {
            //exit('非法请求 d');
        }
        
        $this->_setHeader();

        $loginBiz = new \Business\Member\Login();
        $graphInfo = $loginBiz->getGraphVcode();
        $im  = $graphInfo[0];
        $str = $graphInfo[1];

        $_SESSION["auth_code"] = $str;
        imagepng($im);//建立一张PNG格式图形
        imagedestroy($im);//将图片handle解构，释于内存空间
    }


    /**
     * 终端获取图形验证码
     * <AUTHOR>
     * @date   2019-05-20
     */
    public function getCodeForTerminal() {
        $uuid = I('uuid', '', 'strval');
        if (!$uuid) {
            exit('uuid参数缺失');
        }

        $this->_setHeader();

        $loginBiz = new \Business\Member\Login();
        $graphInfo = $loginBiz->getGraphVcode();

        $im  = $graphInfo[0];
        $str = $graphInfo[1];

        $redis = \Library\Cache\Cache::getInstance('redis');
        $redis->set('auth_code:'.$uuid, $str, '', 300);

        imagepng($im);//建立一张PNG格式图形
        imagedestroy($im);//将图片handle解构，释于内存空间
    }

    private function _setHeader() {
        header("Expires:Mon,26 Jul 1997 05:00:00 GMT");
        //always modified
        header("Last-Modified:".gmdate("D,d M Y H:i:s")."GMT");
        //HTTP/1.1
        header("Cache-Control:no-store,no-cache,must-revalidate");
        header("Cache-Control:post-check=0,pre-check=0",false);
        //HTTP/1.0
        header("Pragma:no-cache");
        header("Content-Type:image/png");
    }

}