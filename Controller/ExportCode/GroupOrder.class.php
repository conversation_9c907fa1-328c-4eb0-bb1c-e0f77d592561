<?php
/**
 * 团购导码 商户端
 */

namespace Controller\ExportCode;

use Library\Controller;
use Library\Cache\Cache;
use Business\CommodityCenter\Ticket as TicketBiz;
use Business\ExportCode\Order as OrderBiz;
use Business\ExportCode\Manage as ManageBiz;

class GroupOrder extends Controller
{
    /***************** 登录信息 ****************************/

    private $_login; //登录全部信息
    private $_sid; //上级id
    private $_memberId; //登录账号id
    private $_dtype; //登录账号类型
    private $_dname; //登录用户名称
    private $_options = []; //参数集

    public function __construct()
    {
        $loginInfo       = $this->getLoginInfo();
        $this->_login    = $loginInfo;
        $this->_sid      = $loginInfo['sid'];
        $this->_memberId = $loginInfo['memberID'];
        $this->_dtype    = $loginInfo['dtype'];
        $this->_dname    = $loginInfo['dname'];

        //应用权限校验
        $authBiz = new \Business\AppCenter\ModuleCommon();
        $auth = $authBiz->checkModuleAuth($this->_sid, $this->_memberId, $this->_dtype, ManageBiz::EXPORT_CODE_TAG);
        if (!$auth) {
            $this->apiReturn(401, [], '无权限');
        }
    }

    /**
     * 4.取消订单接口
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function cannalOrder()
    {
        $ordernum = I('ordernum', '', 'strval'); //订单数据，逗号隔开

        $result = (new OrderBiz())->cancelOrder($this->_sid, $ordernum);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 5.导出票码
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function exportCode()
    {
        //记录请求参数
        pft_log('export_code/order/request', json_encode([$this->_memberId, I('post.')]));

        $orderBiz = new OrderBiz();

        $params              = I("post.");
        $params['sid']       = $this->_sid;
        $params['member_id'] = $this->_memberId;

        $options = $orderBiz->submitPreCheck($params);
        if ($options['code'] != 200) {
            $this->apiReturn(400, [], $options['msg'] ?? '获取参数错误');
        }

        $this->_options = $options['data'] ?? [];

        //拼装下单参数
        //$this->_combineOptions();

        if (empty($this->_options)) {
            $this->apiReturn(400, [], '获取参数错误!');
        }

        //如果是相同的导码参数，做下并发控制
        $lockKey = md5(json_encode($this->_options));
        $cache   = Cache::getInstance('redis');
        $lockRet = $cache->lock($lockKey, 1, 60);
        if (!$lockRet) {
            pft_log('export_code/order', json_encode(['业务处理中', $this->_memberId, $this->_options]));

            $this->apiReturn(400, [], '业务处理中');
        }

        $result = $orderBiz->submitOrder(
            $this->_options['sid'],
            $this->_options['aid'],
            $this->_options['member_id'],
            $this->_options['config_id'],
            $this->_options['paymode'],
            $this->_options['playtime'],
            $this->_options['time_share_info'],
            $this->_options['package_show_info'],
            $this->_options['show_info'],
            $this->_options['package_time_share_info'],
            $this->_options['total'],
            $this->_options['tnum'],
            $this->_options['ordername'],
            $this->_options['ordertel'],
            $this->_options['idcard'],
            $this->_options['tourist']
        );

        //清除锁数据
        $cache->rm($lockKey);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 6.导出票码，结果查询
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function exportResult()
    {
        $batchId = I('batch_id', '', 'strval'); //批次id

        $result = (new OrderBiz())->exportResult($this->_sid, $batchId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 6.1批次失败原因
     * <AUTHOR>
     * @date 2022/4/19
     *
     */
    public function errorResult()
    {
        $batchId = I('post.batch_id', '', 'strval'); //批次id

        $result = (new OrderBiz())->errorResult($this->_sid, $batchId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 11.批次取消接口
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function batchCancel()
    {
        $configId = I('config_id', 0, 'intval'); //配置id
        $batchId  = I('batch_id', '', 'strval'); //批次id 逗号隔开
        if (empty($batchId) || !$configId) {
            $this->apiReturn(203, [], '参数错误');
        }

        $batchIds = explode(',', $batchId);

        $result = (new OrderBiz())->cancelBatchCode($this->_sid, $this->_memberId, $configId, $batchIds);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 17.码列表取消票码接口
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function codeCancel()
    {
        $codeId  = I('code_id', '', 'strval'); //码id 逗号隔开
        $batchId = I('batch_id', 0, 'intval'); //批次id 逗号隔开
        if (empty($codeId) || !$batchId) {
            $this->apiReturn(203, [], '参数错误');
        }

        $codeIds = explode(',', $codeId);

        $result = (new OrderBiz())->cancelOrderCode($this->_sid, $this->_memberId, $batchId, $codeIds);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 组合下单接口需要的参数
     */
    private function _combineOptions()
    {
        $configId      = I('config_id', 0, 'intval'); //团购导码配置ID
        $aid           = I('aid', 0, 'intval'); //供应商id
        $tid           = I('tid', 0, 'intval'); //票id
        $playTime      = I('play_time', '', 'strval'); //游玩时间
        $name          = I('name', '', 'strval'); //名称
        $ordertel      = I('ordertel', '', 'strval'); //电话
        $orderNum      = I('order_num', 0, 'intval'); //单数
        $ticketNum     = I('ticket_num', 0, 'intval'); //票数
        $timeShareInfo = I('time_share_info', '', 'strval'); //分时json串
        $packageShowInfo = I('package_show_info', '', 'strval'); //套票演出json串
        $packageTimeShareInfo = I('package_time_share_info', '', 'strval'); //套票分时json串
        $idcard        = I('personid', '', 'strval'); //身份信息
        $tourist       = I('tourist/a');//游客实名信息，参数如下备注
        $paymode       = I('paymode', '', 'intval'); //支付方式：/Conf/business.conf.php:order_pay_mode
        $lid_aid       = I('lid_upaid', '', 'strval'); //景区id_上级供应商id

        $las = explode("_", $lid_aid);
        $aid = $aid == $las[1] ? $aid : $las[1]; //供应商id

        //上限200单
        if ($orderNum > 200) {
            $this->apiReturn(203, [], '最多200笔订单');
        }

        //每笔订单上限500张票
        if ($ticketNum > 500) {
            $this->apiReturn(203, [], '每笔订单最多500张票');
        }

        if (!($aid && $orderNum && $ticketNum && $playTime && $name && $tid && $configId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        //处理游客信息
        $tourist = is_array($tourist) ? $tourist : [];
        foreach ($tourist as $key => $val) {
            if (empty($val)) {
                unset($tourist[$key]);
            }
        }

        $ticketBiz  = new TicketBiz();
        $ticketInfo = $ticketBiz->queryTicketInfoById($tid);
        if (empty($ticketInfo) || empty($ticketInfo['ticket'])) {
            $this->apiReturn(400, [], '门票不存在');
        }
        $ticketsStatus = $ticketInfo['ticket']['status'] == 1 ? true : false; //1有效 其他无效
        //已过期需要单独验证下
        if ($ticketsStatus) {
            if (date("Ymd", strtotime($ticketInfo['ticket']['max_expiration_date'])) < date("Ymd")) {
                $ticketsStatus = false; //已过期，无效
            }
        }
        if (!$ticketsStatus) {
            $this->apiReturn(400, [], '门票已失效，无法导出');
        }

        //配置了门票码，但是又设置不打印
        $printMode = $ticketInfo['ext']['print_mode'] ?? 0;
        $manageBiz = new ManageBiz($this->_sid);
        $configInfo = $manageBiz->getConfigInfo($configId);
        if ($configInfo['code'] != 200 || empty($configInfo['data'])) {
            $this->apiReturn(400, $configInfo, '团购导码配置不存在');
        }
        //1凭证码 2门票码 不存在0
        $configExportType = $configInfo['data']['export_type'] ?? 0;
        if ($configExportType == 2 && $printMode == 1) {
            $this->apiReturn(400, [], '团购导码配置导出门票码，票属性不能设置不打印');
        }

        $this->_options = [
            'sid'                     => $this->_sid,
            'config_id'               => $configId,
            'aid'                     => $aid,
            'member_id'               => $this->_memberId,
            'paymode'                 => $paymode,
            'playtime'                => $playTime,
            'time_share_info'         => $timeShareInfo,
            'package_show_info'       => $packageShowInfo,
            'package_time_share_info' => $packageTimeShareInfo,
            'total'                   => $orderNum,
            'tnum'                    => $ticketNum,
            'ordername'               => $name,
            'ordertel'                => empty($ordertel) ? "12301" : $ordertel,
            'idcard'                  => $idcard,
            'tourist'                 => is_array($tourist) ? $tourist : [],
        ];

        return true;
    }
}