<?php
/**
 * 团购导码 商户端
 */

namespace Controller\ExportCode;

use Library\Controller;
use Library\SimpleExcel;
use Business\ExportCode\Manage as ManageBiz;
use Business\Product\ProductList;

class CodeManage extends Controller
{
    /***************** 登录信息 ****************************/

    private $_login; //登录全部信息
    private $_sid; //上级id
    private $_memberId; //登录账号id
    private $_dtype; //登录账号类型
    private $_dname; //登录用户名称

    public function __construct()
    {
        $loginInfo       = $this->getLoginInfo();
        $this->_login    = $loginInfo;
        $this->_sid      = $loginInfo['sid'];
        $this->_memberId = $loginInfo['memberID'];
        $this->_dtype    = $loginInfo['dtype'];
        $this->_dname    = $loginInfo['dname'];

        //应用权限校验
        $authBiz = new \Business\AppCenter\ModuleCommon();
        $auth = $authBiz->checkModuleAuth($this->_sid, $this->_memberId, $this->_dtype, ManageBiz::EXPORT_CODE_TAG);
        if (!$auth) {
            $this->apiReturn(401, [], '无权限');
        }
    }

    /**
     * 1.团购导码管理列表
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function getConfig()
    {
        $title    = I('title', '', 'strval'); //搜索名称
        $landId   = I('land_id', 0, 'intval'); //景区id
        $ticketId = I('ticket_id', 0, 'intval'); //门票id
        $page     = I('page', 1, 'intval'); //页码
        $size     = I('size', 10, 'intval'); //当页数量

        $result = (new ManageBiz($this->_sid))->getConfigList($page, $size, $title, $landId, $ticketId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 2.删除导码配置
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function delConfig()
    {
        $configId = I('config_id', 0, 'intval'); //配置id

        $result = (new ManageBiz($this->_sid))->delConfig($configId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 3.创建团购
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function addConfig()
    {
        $title        = I('title', '', 'strval'); //团购名称
        $aid          = I('aid', 0, 'intval'); //供应商id
        $ticketId     = I('ticket_id', 0, 'intval'); //门票id
        $landId       = I('land_id', 0, 'intval'); //景区id
        $exportType   = I('export_type', 0, 'intval'); //导码类型 1凭证码 2门票码
        $sendSystem   = I('send_system', 0, 'intval'); //凭证发码方：1票付通发码 2三方系统发码
        $exportQrCode = I('export_qr_code', 0, 'intval'); //导出二维码：1否 2是

        $result = (new ManageBiz($this->_sid, $this->_memberId))->createConfig($title, $aid, $landId, $ticketId,
            $exportType, $sendSystem, $exportQrCode);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 7.码管理列表
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function getBatch()
    {
        $configId   = I('config_id', 0, 'intval'); //配置id
        $ordernum   = I('ordernum', '', 'strval'); //订单号
        $vcode      = I('vcode', '', 'strval'); //票码
        $page       = I('page', 1, 'intval'); //页码
        $size       = I('size', 10, 'intval'); //当页数量
        $thirdOrder = I('third_order', '', 'strval'); //第三方订单号

        $result = (new ManageBiz($this->_sid))->getBatchList($configId, $page, $size, $ordernum, $vcode, $thirdOrder);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 8.团购配置信息查询接口
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function getConfigInfo()
    {
        $configId = I('config_id', 0, 'intval'); //配置id

        $result = (new ManageBiz($this->_sid))->getConfigInfo($configId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 9.码批次备注接口
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function batchRemark()
    {
        $batchId = I('batch_id', 0, 'intval'); //批次id
        $remark  = I('remark', '', 'strval'); //备注信息

        $result = (new ManageBiz($this->_sid))->saveBatchRemarks($batchId, $remark);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 10.导出码（txt/excel）接口
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function exportToFile()
    {
        $batchId  = I('batch_id', '', 'strval'); //批次id 多个的逗号隔开
        $codeId   = I('code_id', '', 'strval'); //码id 多个的逗号隔开
        $fileType = I('file_type', '', 'strval'); //文件类型 excel txt

        if (empty($batchId) && empty($codeId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $batchId = !empty($batchId) ? explode(',', $batchId) : [];
        $codeId  = !empty($codeId) ? explode(',', $codeId) : [];

        $result = (new ManageBiz($this->_sid))->exportDetailsCode($batchId, $codeId, $fileType);
        $data   = $result['data'] ?? [];

        //TODO::后续导出标题可以优化
        $filename = date('YmdHis') . '团购导码';
        switch ($fileType) {
            case 'txt':
                $data = array_column($data, 'vcode');
                $this->_txtOutput($filename, $data);
                break;
            case 'excel':
                $this->_excelOutput($filename, $data);
                break;
        }
    }

    /**
     * 12.码列表
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function getCode()
    {
        $batchId    = I('batch_id', 0, 'intval'); //批次id
        $ordernum   = I('ordernum', '', 'strval'); //订单号
        $vcode      = I('vcode', '', 'strval'); //票码
        $state      = I('state', -1, 'intval'); //票码状态
        $stateOrder = I('state_order', -1, 'intval'); //订单状态
        $page       = I('page', 1, 'intval'); //页码
        $size       = I('size', 10, 'intval'); //当页数量

        $result = (new ManageBiz($this->_sid))->getCodeList($batchId, $page, $size, $state, $stateOrder, $ordernum, $vcode);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 14.批次信息查询
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function getBatchInfo()
    {
        $batchId  = I('batch_id', 0, 'intval'); //批次id
        $configId = I('config_id', 0, 'intval'); //配置id
        $getAll   = I('get_all_id', 1, 'intval'); //是否获取全部批次id
        $getAll   = $getAll == 1 ? true : false;
        $result   = (new ManageBiz($this->_sid))->getBatchInfo($configId, $batchId, $getAll);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 15.票码备注
     * <AUTHOR>
     * @date 2021-08-02
     *
     */
    public function codeRemark()
    {
        $codeId = I('code_id', 0, 'intval'); //码id
        $remark = I('remark', '', 'strval'); //备注信息

        $result = (new ManageBiz($this->_sid))->saveCodeRemarks($codeId, $remark);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取团购导码可销售产品列表(允许产品类型 A景区，B线路、G餐饮)
     * 直接复制以前的接口,返回的票要进行过滤
     * <AUTHOR>
     * @date 2021/10/13
     *
     */
    public function getProTicketType()
    {
        $name            = I('name', '', 'strval');
        $type            = I('type', 0, 'intval');

        $res = (new ManageBiz($this->_sid))->getProTicketType($name, $type);

        if ($res['code'] == 200) {
            $this->apiReturn(200, $res['data'], '获取成功');
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
    }

    /**
     * 终止导出
     * <AUTHOR>
     * @date 2022/4/19
     *
     */
    public function stopExport()
    {
        $batchId = I('post.batch_id', 0, 'intval'); //批次id
        if (!$batchId) {
            $this->apiReturn(400, [], '参数错误');
        }
        $result = (new ManageBiz($this->_sid, $this->_memberId))->stopExportByBatchId($batchId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 导出txt文件
     * <AUTHOR>
     * @date 2021/9/28
     *
     * @param  string  $filename  文件名
     * @param  array  $data  数据
     */
    private function _txtOutput(string $filename, array $data)
    {
        foreach ($data as $v) {
            print_r($v);
            echo "\r\n";
        }
        header("Content-type: text");
        header("Content-Disposition: attachment; filename=$filename.txt");
        header("Expires: 0");
        header("Cache-Control: must-revalidate, post-check=0,pre-check=0");
        header("Pragma: public");

    }

    /**
     * excel导出
     * <AUTHOR>
     * @date 2021/9/28
     *
     * @param  string  $filename  文件名名
     * @param  array  $data  数据
     */
    private function _excelOutput(string $filename, array $data)
    {
        $xls = new SimpleExcel('UTF-8', false, 'lt');

        $xls->addArray($data);
        $xls->generateXML($filename);

        exit;
    }
}