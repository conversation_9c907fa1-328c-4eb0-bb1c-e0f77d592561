<?php

namespace Controller\OpenplatformOfficialAccount;

use Library\Controller;
use \Business\OpenplatformOfficialAccount\OfficialAccountMsgSetting as OfficialAccountMsgSettingBs;
use Library\wechat\core\Media;
use \Business\Cooperator\WeChat\OfficialAccountMsgSetting as OfficialAccountMsgSettingBsCoo;

class OfficialAccountMsgSetting extends Controller
{

    private $templatemodule = [];
    private $sid = 0;
    public function __construct()
    {
        parent::__construct();
        $memberInfo = $this->getLoginInfo();
        if (empty($memberInfo)) {
            $this->apiReturn(203, [], "请先登录");
        }
        $this->sid = $memberInfo['sid'];
    }

    /**
     * 获取商家微信公众号信息
     *
     * <AUTHOR>
     * @date 2020/4/23
     *
     * @return array
     */
    public function getOfficialAccountInfo()
    {
        $officialAccountMsgSettingBz = new OfficialAccountMsgSettingBs();
        $result                      = $officialAccountMsgSettingBz->getOfficialAccountInfo($this->sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
    /**
     * 获取用户开通模板信息
     *
     * <AUTHOR>
     * @date 2020/4/23
     *
     * @return array
     */
    public function getTemplateIdShortList()
    {
        //工程独立化，主体逻辑已迁往Cooperator
        /*$officialAccountMsgSettingBz = new OfficialAccountMsgSettingBs();
        $result                      = $officialAccountMsgSettingBz->getMsgSetting($this->sid);*/
        //判断商家是否已开通过对应的模板消息
        $OfficialAccountMsgSettingBsCoo = new OfficialAccountMsgSettingBsCoo();
        $result                         = $OfficialAccountMsgSettingBsCoo->getMsgSetting($this->sid);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取推送模板展示详情
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/24
     *
     *
     */
    public function getTemplateDetail()
    {
        $templateIdShort = I('template_id_short', '', 'strval');  //模板库中模板的编号
        $templateConfig  = load_config('template_config', 'wechat_tplmsg');
        
        $this->apiReturn(200, $templateConfig[$templateIdShort], "获取成功");
    }

    /**
     * 为用户开启/开通/关闭行业模板
     *
     * <AUTHOR>
     * @date 2020/4/24
     *
     * @return array
     */
    public function addOrUpdateMsgSetting()
    {
        $id              = I('id', 0, 'intval');                  //已开启的商家配置信息主键id
        $templateIdShort = I('template_id_short', '', 'strval');  //模板库中模板的编号
        $templateStatus  = I('template_status', 1, 'intval');    //状态  1:开启  2:关闭
        if (empty($id) && empty($templateIdShort)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], "参数错误");
        }
        /*$officialAccountMsgSettingBz = new OfficialAccountMsgSettingBs();
        $result                      = $officialAccountMsgSettingBz->addOrUpdateMsgSetting($this->sid, $id, $templateIdShort, $templateStatus);*/

        $OfficialAccountMsgSettingBsCoo = new OfficialAccountMsgSettingBsCoo();
        $result                         = $OfficialAccountMsgSettingBsCoo->addOrUpdateMsgSetting($this->sid, $id,
            $templateIdShort, $templateStatus);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取客服消息开关状态
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/28
     *
     */
    public function getCustomerStatusSetting()
    {
        //1:定时托送  2:粉丝关注推送 3:粉丝对话推送 4:操作菜单推送
        $type = I('push_type', 1, 'intval');
        if (!in_array($type, [1, 2, 3, 4])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], "参数错误");
        }

        /*$officialAccountMsgSettingBz = new OfficialAccountMsgSettingBs();
        $result                      = $officialAccountMsgSettingBz->getCustomerStatusSetting($this->sid, $type);*/

        $OfficialAccountMsgSettingBsCoo = new OfficialAccountMsgSettingBsCoo();
        $result                         = $OfficialAccountMsgSettingBsCoo->getCustomerStatusSetting($this->sid, $type);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 开启/关闭客服消息推送功能
     *
     * <AUTHOR>
     * @date 2020/4/27
     *
     * @return array
     */
    public function updateOrAddCustomerStatusSetting()
    {
        $id = I('id', 0, 'intval'); //主键id
        //1:定时托送  2:粉丝关注推送 3:粉丝对话推送 4:操作菜单推送
        $type  = I('push_type', 1, 'intval');
        $state = I('state', 1, 'intval');  //1:开启  2:关闭
        if (!in_array($type, [1, 2, 3, 4]) || empty($state)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], "参数错误");
        }

        /*$officialAccountMsgSettingBz = new OfficialAccountMsgSettingBs();
        $result                      = $officialAccountMsgSettingBz->updateOrAddCustomerStatusSetting($id, $this->sid, $type, $state);*/

        $OfficialAccountMsgSettingBsCoo = new OfficialAccountMsgSettingBsCoo();
        $result                         = $OfficialAccountMsgSettingBsCoo->updateOrAddCustomerStatusSetting($id,
            $this->sid, $type, $state);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 设置客服消息的间隔时间
     * @return array
     * <AUTHOR>
     * @date 2020/4/30
     *
     *
     */
    public function setIntervalTime()
    {
        $id           = I('id', 0, 'intval'); //主键id
        $intervalTime = I('interval_time', 0, 'intval'); //间隔时间
        if (empty($id)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], "参数错误");
        }
        $officialAccountMsgSettingBz = new OfficialAccountMsgSettingBs();
        $result                      = $officialAccountMsgSettingBz->setIntervalTime($id, $intervalTime);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 更新或者新增客服消息
     *
     * <AUTHOR>
     * @date 2020/4/26
     *
     * @return array
     */
    public function addOrUpdateCustomerServiceMsgSetting()
    {
        //主键id
        $id    = I('id', 0, 'intval');
        //标题
        $title = I('title', '', 'strval');
        //推送类型 1：粉丝活跃定时推送   2：粉丝关注推送 3：粉丝对话推送  4：操作菜单推送
        $pushType = I('push_type', 1, 'intval');
        //活跃粉丝范围1：关注  2：对话  3：扫码  4：操作菜单（多个逗号隔开）
        $activeFans = I('active_fans', '', 'strval');
        //信息类型 1：文字  2：图片  3：图文消息  4：微信文章  5：小程序
        $msgType = I('msg_type', 1, 'intval');
        //推送方式 1：每日推送  2：单次推送 3：延迟推送
        $pushMode = I('push_mode', '', 'strval');
        //发送时间(当为延迟推送是为延迟推送时间)
        $sendTime = I('send_time', '', 'strval');
        //推送配置数据
        $config = I('config', '', 'strval');
        if (empty($pushType) || empty($msgType) || empty($pushMode) || empty($config) || empty($sendTime)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], "参数错误");
        }
        if ($pushMode == 1) {
            $sendTime = date('Hi', strtotime($sendTime));
        } elseif ($pushMode == 2) {
            $sendTime = strtotime($sendTime);
        }

        /*$officialAccountMsgSettingBz = new OfficialAccountMsgSettingBs();
        $result                      = $officialAccountMsgSettingBz->addOrUpdateCustomerServiceMsgSetting($this->sid, $id, $title, $pushType,
            $msgType, $pushMode, $sendTime, $config, $activeFans);*/

        $OfficialAccountMsgSettingBsCoo = new OfficialAccountMsgSettingBsCoo();
        $result = $OfficialAccountMsgSettingBsCoo->addOrUpdateCustomerServiceMsgSetting($this->sid, $id, $title, $pushType,
            $msgType, $pushMode, $sendTime, $config, $activeFans);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取客服消息配置信息列表
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/27
     *
     */
    public function getCustomerServiceMsgSettingList()
    {
        $pageNum  = I('pageNum', 1, 'intval');
        $pageSize = I('pageSize', 10, 'intval');
        $type     = I('type', 1, 'intval');     //列表类型 1：粉丝活跃定时推送   2：粉丝关注推送 3：粉丝对话推送  4：操作菜单推送

        /*$officialAccountMsgSettingBz = new OfficialAccountMsgSettingBs();
        $result                      = $officialAccountMsgSettingBz->getCustomerServiceMsgSettingList($this->sid, $type, $pageNum, $pageSize);*/

        $OfficialAccountMsgSettingBsCoo = new OfficialAccountMsgSettingBsCoo();
        $result                         = $OfficialAccountMsgSettingBsCoo->getCustomerServiceMsgSettingList($this->sid,
            $type, $pageNum, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取客服消息配置详情
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/28
     *
     *
     */
    public function getCustomerServiceMsgSettingDetail()
    {
        $id = I('id', 0, 'intval'); //客服消息配置主键id
        if (empty($id)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], "参数错误");
        }

        $officialAccountMsgSettingBz = new OfficialAccountMsgSettingBs();
        $result                      = $officialAccountMsgSettingBz->getCustomerServiceMsgSettingDetail($id, $this->sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除客服消息配置
     *
     * <AUTHOR>
     * @date 2020/4/28
     *
     * @return array
     */
    public function deleteCustomerServiceMsgSetting()
    {
        $id = I('id', 0, 'intval'); //客服消息配置主键id
        if (empty($id)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], "参数错误");
        }

        /*$officialAccountMsgSettingBz = new OfficialAccountMsgSettingBs();
        $result                      = $officialAccountMsgSettingBz->deleteCustomerServiceMsgSetting($id, $this->sid);*/

        $OfficialAccountMsgSettingBsCoo = new OfficialAccountMsgSettingBsCoo();
        $result                         = $OfficialAccountMsgSettingBsCoo->deleteCustomerServiceMsgSetting($id,
            $this->sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    /**
     * 开启/关闭客服消息配置
     *
     * <AUTHOR>
     * @date 2020/4/28
     *
     * @return array
     */
    public function openOrCloseCustomerServiceMsgSetting()
    {
        $id        = I('id', 0, 'intval');          //客服消息配置主键id
        $msgStatus = I('msg_status', 1, 'intval');  //消息状态  1：开启  2：关闭
        if (empty($id)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], "参数错误");
        }

        /*$officialAccountMsgSettingBz = new OfficialAccountMsgSettingBs();
        $result                      = $officialAccountMsgSettingBz->openOrCloseCustomerServiceMsgSetting($id, $this->sid, $msgStatus);*/

        $OfficialAccountMsgSettingBsCoo = new OfficialAccountMsgSettingBsCoo();
        $result                         = $OfficialAccountMsgSettingBsCoo->openOrCloseCustomerServiceMsgSetting($id,
            $this->sid, $msgStatus);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 上传永久图片素材到微信
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/28
     */
    public function AddMaterial()
    {
        $path = I('url');
        if(!preg_match('/\/([^\/]+\.[a-z]{3,4})$/i',$path,$matches)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $image_name = strtolower($matches[1]);
        $ch = curl_init ($path);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_BINARYTRANSFER,1);
        $img = curl_exec ($ch);
        curl_close ($ch);
        //$image_name就是要保存到什么路径,默认只写文件名的话保存到根目录
        $fp = fopen(IMAGE_UPLOAD_DIR . $image_name,'w');//保存的文件名称用的是链接里面的名称
        fwrite($fp, $img);
        fclose($fp);
        $src = IMAGE_UPLOAD_DIR . $image_name;
        $officialAccountMsgSettingBz = new OfficialAccountMsgSettingBs();
        $result                      = $officialAccountMsgSettingBz->AddMaterial($this->sid, $src);
        unlink($src);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}