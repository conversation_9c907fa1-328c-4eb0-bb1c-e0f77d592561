<?php

/**
 * 微商城公用的一些接口
 * <AUTHOR>
 */

namespace Controller\AliMiniApp;

use Library\Business\RedisGeo;
use Library\Business\WechatSmallApp;
use Library\Cache\Cache;
use Library\Controller;
use Model\AppCenter\ModuleList;
use Model\Product\Ticket;
use Model\Member\Member;
use Model\Subdomain\SubdomainInfo;
use Model\TradeRecord\OnlineTrade;
use Process\Resource\Qiniu;
use Qiniu\Auth;
use Qiniu\Http\Request;
use Qiniu\Storage\UploadManager;
use Business\Wechat\Authorization;

class AliMall extends Controller {

    const __MALL_CHANNEL__      = 1;  //微商城渠道

    const __APP_CHANNEL__       = 11;  //小程序渠道

    const __ORDER_MODE__        = 11;  //微商城订单来源标识

    const __DEFAULT_ACCOUNT     = 124078;

    const __DEFAULT_MEMBERID    = 22647;

    const __DEFAULT_APPID__     = 'wxd72be21f7455640d';

    const __FREE_PACKAGE_ID__   = 36;

    protected $_allDisMan       = false; //是否是全民分销用户

    protected $_promoter        = false; //是否以推广者的视角浏览

    protected $_supplyId;       //供应商id

    protected $_supplyAccount;  //供应商账号

    protected $_sessionKey;     //微信小程序sessionKey

    protected $_smallOpenid;    //微信小程序openid

    protected $supplyIds=[];   //前端传入的多用户的数据

    protected $_unionid;         //微信用户unionid

    private $microMallModuleId = 1; //微商城模块id

    private $session_key = 'lastpackage:';

    protected $aliMiniAppid = '';
    protected $saleChannel = null;

    public function __construct() {
        // 标记支付宝小程序应用，目前支付宝小程序仅应用于自助机付款，所有只做了简单的header判断
        if ($this->inAlipaySmallApp(true)) {
            return true;
        }
        $this->_supplyId       = $this->parseSupplyMemberId();

        if ($this->_supplyId != self::__DEFAULT_MEMBERID && $this->_supplyId != 0) {
            $key = $this->session_key . $this->_supplyId;
            $data = session($key);
            if (!$data) {
                session($key, json_encode($data));
            }
        }

        $this->_supplyAccount  = $this->parseSupplyAccount();

        $businessCache = (new \Library\Tools\BusinessCache())->getBusinessCache($_SESSION['memberID']);

        if (isset($_SESSION['memberID']) && isset($businessCache['identify']) && $businessCache['identify'] == 'allDis') {
            $this->_promoter = true;
            $this->_allDisMan = true;
        }
        $this->saleChannel = I('saleChannel', 0, 'intval');
    }

    protected function setSupplyIds($supplyIds){
        $this->supplyIds=$supplyIds;
    }
    protected function getSupplyIds(){
        if(!$this->supplyIds){
            return false;
        }
        return implode(',',$this->supplyIds);
    }
    /**
     * 获取id数组
     * <AUTHOR>
     * @dateTime 2018-04-21T10:34:22+0800
     * @throws   \Exception                             可能抛出异常
     * @return   [type]                   [description]
     */
    protected function getArrSupplyIds(){
        return $this->supplyIds;
    }

    /**
     * 判断是不是已经登陆(为小程序做兼容)
     * 
     * @param    string  $type 指定请求类型
     *                         ajax : ajax请求
     *                         html : 页面请求
     *                         auto : 自动判定
     * @param bool $isAutoHandle
     * @return   mixed
     *  
     */
    public function isLogin($type = 'auto',  $isAutoHandle = true) {

        if ($this->inWechatSmallApp()) {

            $account = substr($this->_smallOpenid, 0, 11);

            $member = $this->getMemberModel()->getMemberInfo($account, 'account', 'id');

            if ($member) {
                return $member['id'];
            } else {
                if (isset($_POST['dateType'])) {
                    $this->apiReturn(204, array(), '暂无订单');
                } else {
                    $this->apiReturn(102, array(), '未登录');
                }
            }
        }

        return parent::isLogin($type);
    }

    /**
     * 获取郑州园区一卡通小程序得用户id
     * @return int | string - json
     *
     */
    public function isLoginCardSolution()
    {
        if ($this->inWechatSmallApp()) {
            $cardMemberModel = new \Model\CardSolution\Member();
            $cardMemberInfo  = $cardMemberModel->getFidByOpenId($this->_smallOpenid);
            if ($cardMemberInfo) {
                return $cardMemberInfo['fid'];
            }
        }
        $this->apiReturn(102, array(), '未登录');
    }

    private function getMemberModel()
    {
        static $memberModel;
        if ($memberModel) return $memberModel;
        return new Member('slave');
    }

    /**
     * 初始化小程序参数
     * 
     * @return [type] [description]
     */
    private function _initWechatSmallApp() {

        //微信小程序请求数据源获取
        $input  = file_get_contents('php://input');
        $_POST  = json_decode($input, true);

        if (isset($_SERVER['HTTP_SESSION_KEY'])) {

            $smallLib = new \Library\Business\WechatSmallApp;

            $sk = $_SERVER['HTTP_SESSION_KEY'];

            $openid   = $smallLib->getSession($sk, 'openid');

            if (!$openid) {
                $this->apiReturn(202, [], '登陆状态过期');
            }

            $this->_smallOpenid = $openid;

            $this->_sessionKey = $smallLib->getSession($sk, 'session_key');

            $this->_unionid     = $smallLib->getSession($sk, 'unionid');
        }

    }

    /**
     * 微商城渠道过滤
     * @param  array    $data  产品数据,[['pid' => 3026]]
     * @param  bool     $inWechatSmApp 是否为小程序访问，如果是小程序访问在没有配置销售渠道的情况下，默认不限。
     * @return $data    经过排序后的数据
     */
    public function channelFilter($data, $inWechatSmApp) {

        $host = explode('.', $_SERVER['HTTP_HOST']);

        if ($host[0] == 'seckill') {
            return $data;
        }

        if ($inWechatSmApp) {
            $channelVal = self::__APP_CHANNEL__;
        } else {
            $channelVal = self::__MALL_CHANNEL__;
        }

        foreach ($data as $key => $item) {

            //判断票属性是否允许在微商城售卖
            $tmpShop = explode(',', $item['shop']);

            if (!in_array($channelVal, $tmpShop)) {
                unset($data[$key]);
                continue;
            }

            //判断销售渠道是否配置
            $tmpChannel = explode(',', $item['channel']);

            if (!in_array($channelVal, $tmpChannel)) {
                 if ($item['apply_sid'] != 57302 || $this->_supplyAccount == '502860' || $this->_supplyAccount == '124900') {
                    unset($data[$key]);
                    continue;
                }
            }
        }

        return $data;
    }

    /**
     * 选择渠道判断
     * <AUTHOR>
     * @Date   2018-03-08
     * @param  bool $inWechatSmApp
     * @rerturn '' | int
     *
     */
    public function checkChannel($inWechatSmApp)
    {
        $host = explode('.', $_SERVER['HTTP_HOST']);
        // 秒杀返回无渠道 -- 选择全部渠道产品
        if ($host[0] == 'seckill') {
            return '';
        }

        if ($inWechatSmApp) {
            return self::__APP_CHANNEL__;
        }
        
        return self::__MALL_CHANNEL__;
    }

    /**
     * 产品排序
     * @param  [type] $data 产品数据,[['pid' => 3026]]
     * @return [type]       [description]
     */
    public function productOrder($data) {
        usort($data, function($val1, $val2) {
            if ($val1['px'] == $val2['px']) {

                $uptime1 = strtotime($val1['uptime']);
                $uptime2 = strtotime($val2['uptime']);

                if ($uptime1 == $uptime2) {
                    if ($val1['tx'] == $val2['tx']) {
                        if ($val1['pid'] > $val2['pid']) {
                            return -1;
                        } elseif ($val1['pid'] < $val2['pid']) {
                            return 1;
                        } else {
                            return 0;
                        }
                    } elseif ($val1['tx'] > $val2['tx']) {
                        return -1;
                    } else {
                        return 1;
                    }
                } elseif ($uptime1 > $uptime2) {
                    return -1;
                } else {
                    return 1;
                }
            } elseif ($val1['px'] > $val2['px']) {
                return -1;
            } else {
                return 1;
            }
        });
        return $data;
    }

    /**
     * 一次性获取多个产品的零售价
     * @param  mix $pidArr [3026,3027] | 3026
     * @return [type]          [description]
     */
    public function getMuchRetailPrice($pidArr, $discount = false) {

        $model = new Ticket('slave');

        if (is_numeric($pidArr)) {
            $pidArr = [$pidArr];
        }

        return $model->getMuchRetailPrice($pidArr);

    }

    /**
     * 是否在微信app内
     * @return [type] [description]
     */
    public function inWechatApp() {
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        $pos = strpos($user_agent, 'MicroMessenger');

        return $pos === false ? false : true;
    }

    /**
     * 是否在微信小程序内
     * @return [type] [description]
     */
    public function inWechatSmallApp() {
        //暂时根据前端请求的标识判断
        $header = [];
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
        } else {
            foreach ($_SERVER as $key => $value) {
                if ('HTTP_' == substr($key, 0, 5)) {
                    $headers[str_replace('_', '-', substr($key, 5))] = $value; 
                }
            }
        }
        if (isset($headers['SMALL-APP']) || isset($headers['ALIPAY-SMALL-APP'])) {
            return true;
        } else {
            return false;
        }
    }
    public function inAlipaySmallApp()
    {
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
        } else {
            foreach ($_SERVER as $key => $value) {
                if ('HTTP_' == substr($key, 0, 5)) {
                    $headers[str_replace('_', '-', substr($key, 5))] = $value;
                }
            }
        }
        if (isset($headers['ALIPAY-SMALL-APP'])) {
            $this->aliMiniAppid = $headers['ALIPAY-SMALL-APP'];
            $miniAppConfig = load_config($this->aliMiniAppid, 'alipay');
            $this->_supplyId = isset($miniAppConfig['apply_did']) ? $miniAppConfig['apply_did'] : 0;
            return true;
        }
        return false;
    }

    /**
     * 获取微信版本号
     * @return [type] [description]
     */
    public function getWechatVersion() {
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        $pos = strpos($user_agent, 'MicroMessenger');

        return floatval(substr($user_agent, $pos + 15, 4));

    }

    /**
     * 授权验证
     * @return bool
     */
    private function _auth() {

        $query = 'c='.I('get.c').'&a='.I('get.a');
        if (!in_array($query, $this->_blackInteface())) {
            return true;
        }

        //微信小程序无需token验证
         if ($this->inWechatSmallApp()) {
            return true;
         }

        $clientToken = I('post.token');
        $serverToken = I('session.token');

        if(!$serverToken || !$clientToken) {
            return false;
        }

        if($clientToken == $serverToken) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 必须验证的接口
     * <AUTHOR>
     * @date   2017-11-21
     */
    private function _blackInteface() {
        return [
            'c=Mall_Order&a=order',
            'c=Mall_Order&a=seckillOrder',
        ];
    }

    /**
     * 无需验证token的接口
     * @return [type] [description]
     */
    private function _whiteInterface() {

        return [
            'c=Mall_Member&a=autoLogin',
            'c=Mall_Member&a=logout',
            'c=Mall_Member&a=joinAllDis',
            'c=Mall_Member&a=redirectForSession',
            'c=Mall_Member&a=autoLoginByOpenId',
            'c=Mall_AllDis&a=issueCommission',
            'c=Mall_AllDis&a=redpackList',
            'c=Mall_AllDis&a=getShareQrcode',
            'c=Mall_AllDis&a=cash'
        ];
    }

    /**
     * 解析微商城所属供应商的账号
     * 
     * @return [type] [description]
     */
    protected function parseSupplyAccount() {

        if ($this->_supplyAccount) {
            return $this->_supplyAccount;
        }

        //是否在微信小程序内
        if ($this->inWechatSmallApp()) {
            $wxAppScenCode = I('post.scenCode', 0);

            if ( $wxAppScenCode ) {
                $supplyId = $this->parseSupplyMemberId();
                //return $this->getMemberModel()->getMemberCacheById($supplyId, 'account');

                $queryParams = [[$supplyId]];
                $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                    $queryParams);
                $account = '';
                if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                    $account = array_column($queryRes['data'], null, 'id')[$supplyId]['account'];
                }

                return $account;
            }

            //根据客户端的请求参数判断供应商账号
            return \safetxt($_SERVER['HTTP_SMALL_APP']);

        } else {
            //指定账号
            if (I('account')) {
                return I('account', '', 'intval');
            }

            $host_info = explode('.', $_SERVER['HTTP_HOST']);
            if ($host_info[0] == 'wx' || $host_info[0] == 'm') {
                return self::__DEFAULT_ACCOUNT;
            }

            return \safetxt($host_info[0]);
        }
        
    }

    /**
     * 解析微商城所属供应商的ID
     * @return [type] [description]
     */
    protected function parseSupplyMemberId() {
        if ($this->_supplyId) {
            return $this->_supplyId;
        }

        if ($this->inWechatSmallApp() || $this->aliMiniAppid !='') {
            $wxAppScenCode = I('post.scenCode', 0);
            // 支付宝小程序根据小程序appid获取供应商ID
            if ($this->aliMiniAppid) {
                $miniAppConfig = load_config($this->aliMiniAppid, 'alipay');
                return isset($miniAppConfig['apply_did']) ? $miniAppConfig['apply_did'] : 0;
            }
            if ($wxAppScenCode) {
                $wxLib      = new WechatSmallApp();
                $supplyId   = $wxLib->decodeShopCode($wxAppScenCode);
                unset($wxLib);
                return is_numeric($supplyId) ? $supplyId : 0; 
            }
        }

        $account = $this->parseSupplyAccount();
        if ($account == self::__DEFAULT_ACCOUNT) {
            return self::__DEFAULT_MEMBERID;
        }

        $memModel = new Member('slave');
        $member = $memModel->getMemberInfo((string)$account, 'account');

        //供应商账号不存在,退出程序
        if (!$member) {
            exit('非法请求');
        }

        return $member['id'];
    }

    /**
     * 获取微商城的配置
     * @return [type] [description]
     */
    protected function getMallConfig($memberId = 0) {

        $memberId = $memberId ?: $this->_supplyId;

        static $config;

        if ($config) {
            return $config;
        }

        $shopConfigModel = new \Model\Subdomain\ShopConfig();
        $config          = $shopConfigModel->getMallConfig($memberId);

        $others = json_decode($config['others'], true);

        if (!isset($others['pay_wx'])) {
            $others['pay_wx'] = $others['pay_ali'] = $others['pay_uni'] = 1;
        }

        return $config;

    }

    /**
     * 判断一个用户是否是散客
     * 
     * @param  mix  $memberId 用户id|用户资料
     * @return boolean
     */
    protected function _isSanke($memberId) {
        if (is_numeric($memberId)) {
            $memModel = new Member('slave');
            $member = $memModel->getMemberInfo($memberId);
        } else {
            $member = $memberId;
        }
        //账号类型为散客
        if ($member['dtype'] == 5) {
            return true;
        }
        //账号等于手机号
        if ($member['account'] == $member['mobile']) {
            return true;
        }

        return false;
    }

    /**
     * 保存openid和appid信息
     * @param  [type] $openid [description]
     * @param  [type] $appid  [description]
     * @return [type]         [description]
     */
    protected function _storeOpenInfo($openid, $appid) {
        $string = 'abcdefghijklmnopqrstuvwxyz';

        $head = substr(str_shuffle($string), 0, 5);
        $tail = substr(str_shuffle($string), 0, 7);

        $data = base64_encode($head . $openid . $appid . $tail);

        switch (\parse_env()) {
            case 'LOCAL':
                $domain = '.12301.local';
                break;
            case 'DEVELOP':
                $domain = '.12301.test';
                break;
            case 'TEST':
                $domain = '.12301dev.com';
                break;
            default:
                $domain = '.12301.cc';
        }

        setcookie('totoro', $data, time() + 3600, '/', $domain);
    }

    /**
     * 获取存储在cookie中openid和appid
     * @return [type] [description]
     */
    protected function _getOpenInfo() {

        $data = $_COOKIE['totoro'];

        if (!$data) return false;

        $data = base64_decode($data);

        return [
            'openid' => substr($data, 5, 28),
            'appid'  => substr($data, 33, 18)
        ];

    }

    /**
     * 获取不同店铺或景点的经纬度数据用于定位
     * post.scen 场景信息，即店铺的二维码中的数据，可以根据WechatSmallApp::encodeShopCode获得
     * <AUTHOR> Chen
     */
    public function getScenLocation()
    {
        $scen  = I('post.scen');
        $lib   = new WechatSmallApp();
        $mId   = $lib->decodeShopCode($scen);
        $model = new SubdomainInfo();
        $location = $model->getLocation($mId);
        if ($location['longitude'] && $location['latitude']) {
            $this->apiReturn(200, $location, '获取位置成功');
        }
        $this->apiReturn(parent::CODE_NO_CONTENT, [], '获取位置失败');
    }

    public function getShopInfo()
    {
        $subModel = new SubdomainInfo();
        $info = $subModel->getBindedSubdomainInfo($this->_supplyId, 'id', 'M_name as name,M_banner as img,M_tel as tel,longitude,latitude');
        if ($info) parent::apiReturn(200, $info);
        parent::apiReturn(parent::CODE_NO_CONTENT, [], '配置不存在');
    }

    /**
     * 获取附近的店铺、景点
     */
    public function getNearbyShop()
    {
        //ini_set('display_errors','On');
        $longitude = I('post.longitude') +0 ;
        $latitude  = I('post.latitude') + 0;
        if (!is_double($longitude) || !is_double($latitude)) {
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '获取附近的景点失败，经纬度格式不正确');
        }
        $output     = [];
        try {
            $redisGeo = new RedisGeo();
            $wsa      = new WechatSmallApp();
            $subModel = new SubdomainInfo();
            $geoKey   = 'shopGeoInfo';
            $tmpPos   = 'tmp:' . md5($longitude + $latitude);
            //临时保存游客的位置信息——此处可能会有问题！！！
            $redisGeo->geoAdd($geoKey, $longitude, $latitude, $tmpPos);
            $nearByList = $redisGeo->geoRadius($geoKey, $longitude, $latitude, 10, 'km');
            foreach ($nearByList as $item) {
                if (strpos($item[0],"tmp:")!==false) continue;
                //获取距离
                $dist = $redisGeo->geoDist($geoKey, $item[0], $tmpPos);
                //获取加密的程序码
                $scen = $wsa->encodeShopCode($item[0]);
                $info1 = $subModel->getBindedSubdomainInfo($item[0], 'id', 'M_banner');

                $shopConfigModel = new \Model\Subdomain\ShopConfig();
                $info2 = $shopConfigModel->getMallConfig($item[0]);

                $output[] = [
                    'scenCode'=> $scen,
                    'name'=> $info2['name'] ?: '',
                    'img' => $info1['M_banner'] ?: '',
                    'tel' => $info2['telephone'] ?: '',
                    'dist'=> $dist,
                    'aboutus'=> strip_tags(htmlspecialchars_decode($info2['about_us'])) ?: '',
                ];
            }
            usort($output, function($a, $b) {
                if ($a['dist']==$b['dist']) return 0;
                return $a['dist'] > $b['dist'] ? 1 : -1;
            });
            //删除游客的临时位置信息
            $redisGeo->remGeoInfo('shopGeoInfo', $tmpPos);
        } catch (\Exception $e) {
            pft_log('sys_error', $e->getMessage());
        }
        parent::apiReturn(200, $output);
    }

    /**
     * 生成商家或者景点的小程序码
     */
    public function getPageAppCode()
    {
        $scenCode   = I('post.scenCode', '', 'trim');
        $account    = I('post.account');
        $page       = I('post.page');
        $codeType   = I('post.codeType',0,'intval');
        if (!$account || !$scenCode) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST,[], '请求出错');
        }
        /**
         * @var $cacheRedis \Redis
         */
        $cacheRedis = Cache::getInstance('redis');
        $cacheKey   = "appcodev2:$account:$codeType:$page:$scenCode";
        $url = $cacheRedis->get($cacheKey);
        if ($url) {
            parent::apiReturn(200, ['url'=>$url]);
        }
        $lib = new WechatSmallApp();

        if ($codeType==1) {
            $res = $lib->getWxACode($account, $scenCode);
        } else {
            $res     = $lib->getWxCodeUnlimit($account, $scenCode, $page);
        }
        $prefix = substr($res['data'], 0, 1);
        if ($prefix == '{') {
            $decodeRes = json_decode($res['data'], true);
            $this->apiReturn(401, $res, $decodeRes['errmsg']);
        }
        $mdscenCode   = md5($scenCode.$page);
        $fileName  = "wacv2_$mdscenCode.png";
        $config = load_config('qiniu', 'Qiniu');
        $qiniu  = new Qiniu($config);
        $fileUrl = $qiniu->hasFileExist($fileName);
        if ($fileUrl != false) {
            $ret['url'] = $fileUrl;
            parent::apiReturn(200, $ret);
        }

        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();
        
        $upManager = new UploadManager();
        $auth   = new Auth($config['accessKey'], $config['secretKey']);
        $token  = $auth->uploadToken($config['images']['bucket'], null, 3600);
        [$ret, $error] = $upManager->put($token, $fileName, $res['data']);
        $ret['url'] = $config['images']['domain'] . $fileName;
        $cacheRedis->set($cacheKey, $ret['url']);
        parent::apiReturn(200, $ret);
    }

    /**
     * 支付成功后发送模板消息
     *
     * @param $account
     * @param $openid
     * @param $prepayId
     * @param $title
     * @param $code
     * @param $orderNum
     * @param $orderTime
     * @param $endTime
     * @param string $tips
     * @return array
     */
    public function sendMsgAfterPay()
    {
        $account    = I('post.account');
        $openid     = I('post.openid');
        $prepayId   = I('post.prepayId');
        $code       = I('post.code');
        $title      = I('post.title');
        $orderNum   = I('post.orderNum');
        $orderTime  = I('post.orderTime');
        if (!$orderTime) $orderTime = date('Y-m-d');
        $endTime    = I('post.endTime');
        $tips       = I('post.tips');
        pft_log('wechat/smallapp', json_encode($_POST, JSON_UNESCAPED_UNICODE));
        /*        景区名称{{keyword1.DATA}}
                 取票码 {{keyword2.DATA}}
                订单号 {{keyword3.DATA}}
                购票时间 {{keyword4.DATA}}
                有效期限 {{keyword5.DATA}}
                温馨提示 {{keyword6.DATA}}
        */
        $conf = load_config($account, 'wechat_sm_app');
        $data = [
            'keyword1'=>['value'=>$title],
            'keyword2'=>['value'=>$code],
            'keyword3'=>['value'=>$orderNum],
            'keyword4'=>['value'=>$orderTime],
            'keyword5'=>['value'=>$endTime],
            'keyword6'=>['value'=>$tips],
        ];
        $lib = new WechatSmallApp();
        $result = $lib->sendTemplateMsg($account, $openid, $conf['tmpMsgIdList']['AT0041'], $prepayId,
                                                 $data,"pages/checkqrcode/checkqrcode?code={$code}",'', 'keyword2.DATA');
        if ($result['errcode']==0) {
            parent::apiReturn(200);
        }
        parent::apiReturn($result['errcode'], [], $result['errmsg']);
    }

    /**
     * 微商城绑定的公众号是否有授权权限
     * <AUTHOR>
     * @date   2018-02-06
     * @return boolean
     */
    public function isLicensed()
    {
        //获取托管的公众号信息
        $wxOpenModel = new \Model\Wechat\WxOpen();
        $wxInfo      = $wxOpenModel->getWechatOffiAccInfo($this->_supplyAccount, 'account');
        $appid       = $wxInfo['appid'];
        if (!$appid) {
            $this->apiReturn(200, 0);
        } else {
            //是否有授权权限
            $authBiz    = new Authorization();
            $permission = $authBiz->hasAuthPermission($appid);
            if ($permission) {
                $this->apiReturn(200, 1);
            } else {
                $this->apiReturn(200, 0);
            }
        }
    }

    /**
     * 增加小程序扫码支付的日志
     * <AUTHOR>
     * @date 2018-08-31
     *
     * https://pay.12301.cc/r/Mall_Mall/addMiniAppScanLog
     *  ordernum: 订单号
     *  channel : 支付通道
     */
    public function addMiniAppScanLog()
    {
        //addQrCodeScanLog
        $ordernum = I("post.ordernum");
        $channel  = I('post.channel', 2);
        $model = new OnlineTrade();
        if (!$model->checkQrCodeScan($ordernum, $channel)) {
            $model->addQrCodeScanLog($ordernum, $channel);
        }
        parent::apiReturn(200);
    }
}