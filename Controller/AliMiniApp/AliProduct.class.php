<?php
/**
 * 微商城产品展示相关接口
 * <AUTHOR>
 */

namespace Controller\AliMiniApp;

use Business\JavaApi\ProductApi;
use Business\JavaApi\TicketApi;
use Business\JavaApi\Ticket\Price;
use Business\Product\Product as bizProduct;
use Business\Product\ProductList as bizProductList;
use Business\Product\Show as ShowBiz;
use Business\Mall\Poster as PosterBiz;
use Business\Product\Ticket as bizTicket;
use Library\Cache\Cache;
use Library\Constants\ThemeConst;
use Library\Tools\Helpers;
use Model\Mall\AllDis as AllDisModel;
use Model\Mall\MemberSmallAppConfig;
use Model\Product\AnnualCard;
use Model\Product\Area;
use Model\Product\Evolute;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\Subdomain\SubdomainInfo;
use Process\Mall\AllDis as AllDisProcess;
use Process\Product\Settle\GroupByTopicOrPtypeSettle;
use Process\Resource\AreaFilter\AreaPinyinSettle;
use Process\Resource\AreaProcess;
use Business\JavaApi\StorageApi;

class AliProduct extends AliMall
{
    //首页返回数据条数
    protected $_indexShow = 10;

    //允许售卖的产品类型
    // protected $_allowType = ['A', 'B', 'C', 'F', 'H', 'G', 'I', 'J'];
    protected $_allowType = ['A']; // 允许售卖全部先写个空数组

    //上次获取到的最后一个景区在数组中的索引，用于分页
    private $_lastPos = 0;

    protected function getSupplyIdsByAccounts(array $accounts)
    {
        foreach ($accounts as $key => $value) {
            if (empty($value)) {
                unset($accounts[$key]);
            }
        }
        $member      = new \Model\Member\Member();
        $where       = ["account" => ['in', $accounts]];
        $field       = "id";
        $memberInfos = $member->getMemberInfoByMulti($accounts, 'account', 'id');

        return array_column($memberInfos, "id");
    }

    /**
     * 微商城产品列表页
     */
    public function productList()
    {
        //产品类型
        $ptype = I('type', 'A');
        //上次查询的位置
        $lastPos = I('lastPos', 0, 'intval');
        //每页条数
        $pageSize = I('pageSize', 10, 'intval');
        //产品关键字
        $keyword = I('keyword', '');
        //主题
        $topic = I('topic', '');
        //城市代码
        $city = I('city', 0, 'intval');
        //小程序扫码
        $wxAppScenCode = I('post.scenCode', 0);

        $supplyAccounts = I('post.supplyIds', '');
        $supplyIds      = $this->getSupplyIdsByAccounts(explode(',', $supplyAccounts));
        $this->setSupplyIds($supplyIds);

        if ($topic == -1) {
            $topic       = '';
            $productList = $this->_getProductList($ptype, $lastPos, $pageSize, $keyword, $topic, $city);
            GroupByTopicOrPtypeSettle::clearUp($productList);
        } else {
            $productList = $this->_getProductList($ptype, $lastPos, $pageSize, $keyword, $topic, $city);
        }

        $return = [
            'list'    => $productList,
            'lastPos' => $this->_lastPos,
        ];
        //第一页的请求,前端需要缓存一些信息
        if ($lastPos == 0) {
            $return['citys']  = [];//$this->getAreaList(true);
            $return['themes'] = [];//$this->getThemes();
            $return['type']   = [];//$this->getTypeList(true);

            if ($wxAppScenCode) {
                $subModel           = new SubdomainInfo();
                $field              = 'M_name as name,M_banner as img,M_tel as tel,longitude,latitude';
                $info               = $subModel->getBindedSubdomainInfo($this->_supplyId, 'id', $field);
                $return['shopInfo'] = $info;
            }
        }
        // 加入轮播图(小程序用到)
        if ($this->inAlipaySmallApp()) {
            $memberSmallAppConfig = new MemberSmallAppConfig();
            $configItem           = $memberSmallAppConfig->getConfigByMemberId($this->_supplyId);
            if ($configItem->id) {
                $smallAppConfig = $configItem->getImages('banner');
                foreach ($smallAppConfig as $value) {
                    $banner[] = $value['path'];
                }
                $return['shopInfo']['imgUrls']   = $banner;
                $return['shopInfo']['name']      = $configItem->store_name;
                $return['shopInfo']['longitude'] = $configItem->longitude;
                $return['shopInfo']['latitude']  = $configItem->latitude;
                $return['shopInfo']['tel']       = $configItem->service_mobile;
            } else {
                $config = $this->getCustomConfig(true);

                $banner = [];
                foreach ($config['banner'] as $item) {
                    $banner[] = key($item);
                }

                $return['shopInfo']['imgUrls'] = $banner;
                $return['shopInfo']['name']    = $config['name'];
            }
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 获取产品列表页的景区信息
     *
     * @param  string  $ptype  产品类型
     * @param  int  $lastPos  上一次搜索位置(改成页数)
     * @param  int  $pageSize  每页条数
     * @param  string  $keyword  关键字
     * @param  string  $topic  主题
     * @param  string  $city  城市code
     * @param  string  $isTopicNN  topic不为空
     *
     * @return [type]
     */
    private function _getProductList($ptype, $lastPos, $pageSize, $keyword, $topic, $city)
    {

        $option = [];
        $type   = [];
        if ($ptype != 'all') {
            $type = [$ptype];
        }

        if ($keyword) {
            $option['title'] = $keyword;
        }

        //城市筛选
        if ($city) {
            if (in_array($city, [1, 2, 3, 4])) {
                $option['province'] = $city;
            } else {
                $option['city'] = $city;
            }
        }

        //主题筛选
        if ($topic) {
            $option['topic'] = $topic;
        }

        // 利用当前条数计算当前页面数量
        if ($lastPos == '') {
            $this->_lastPos = $pageNum = 1;
        } else {
            $this->_lastPos = $pageNum = $lastPos + 1;
        }

        $products = $this->_getProductSet($option, $type, $pageNum, $pageSize);

        if (empty($products['lists'])) {
            return [];
        }

        //整合前端需要的数据
        $list = $this->_productDeal($products['lists']);

        if ($this->_allDisMan && $list) {
            //全民分销价格优惠
            $list = AllDisProcess::getAllDisPrice($this->_supplyId, $list, 'list');
            if ($this->_promoter) {
                //计算推广佣金
                $list = AllDisProcess::calRecommendCommission($this->_supplyId, $list);
                //是否有制作海报
                $lidArr    = array_column($list, 'lid');
                $posterBiz = new PosterBiz();
                $getRes    = $posterBiz->getMyProPosterBylidArr($this->_supplyId, $lidArr, 'id,lid,begin,end');
                if ($getRes['code'] == 200 && $getRes['data']) {
                    $posters = array_key($getRes['data'], 'lid');
                } else {
                    $posters = [];
                }
                foreach ($list as &$item) {
                    if (isset($posters[$item['lid']]) && ($posters[$item['lid']]['end'] == 0 || ($posters[$item['lid']]['end'] > time() && $posters[$item['lid']]['begin'] < time()))) {
                        $item['has_poster'] = 1;
                    } else {
                        $item['has_poster'] = 0;
                    }
                }
            }
        }

        return $list;
    }

    /**
     * 详情页-获取景区信息
     */
    public function getLandInfo()
    {
        header("Access-Control-Allow-Origin: *"); // 允许任意域名发起的跨域请求
        $lid = I('lid', '', 'intval');
        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        // 转换到java接口
        $productBiz  = new bizProduct();
        $landInfoArr = $productBiz->getProductInfo($this->_supplyId, $lid, 0);

        if ($landInfoArr['code'] != 200) {
            $this->apiReturn(204, [], $landInfoArr['msg']);
        }

        if ($landInfoArr['data']['imgpathGrp']) {
        } else {
            $landInfoArr['data']['imgpathGrp'] = [];
        }
        $landInfoArr['data']['jqts']     = nl2br($landInfoArr['data']['jqts']);
        $landInfoArr['data']['jtzn']     = nl2br($landInfoArr['data']['jtzn']);
        $landInfoArr['data']['bhjq']     = htmlspecialchars_decode($landInfoArr['data']['bhjq']);
        $landInfoArr['data']['bhjq']     = \image_opt($landInfoArr['data']['bhjq'], 600);
        $landInfoArr['data']['venue_id'] = $landInfoArr['data']['venus_id'];

        if (strpos($landInfoArr['data']['lng_lat_pos'], ',') !== false) {
            list($longitude, $latitude) = explode(',', $landInfoArr['data']['lng_lat_pos']);
            $landInfoArr['data']['latitude']  = $latitude;
            $landInfoArr['data']['longitude'] = $longitude;
        } else {
            //没有的话，定位到北京天安门....
            $landInfoArr['data']['latitude']  = 39.915119;
            $landInfoArr['data']['longitude'] = 116.403963;
        }

        //如果是场次类产品，添加额外的场次数据
        if ($landInfoArr['data']['jtype'] == 'H') {
            $showBiz = new ShowBiz();
            $tmpInfo = $showBiz->getLastedRoundList($landInfoArr['data']['venue_id']);
            $code    = $tmpInfo['code'];

            if ($code == 1) {
                //有获取到数据
                $data = $tmpInfo['data'];

                $landInfoArr['data']['venue_img']       = $data['venue_img'];
                $landInfoArr['data']['show_start_date'] = $data['lasted_date'];
                $landInfoArr['data']['show_round_list'] = $data['round_list'];

            } else {
                //获取场次数据错误
                pft_log('order_show/error', json_encode(['getLandInfo', $landInfoArr['data']['venue_id'], $tmpInfo]));

                $landInfoArr['data']['venue_img']       = '';
                $landInfoArr['data']['show_start_date'] = '';
                $landInfoArr['data']['show_round_list'] = [];
            }
        }

        $this->apiReturn(200, $landInfoArr['data']);
    }

    /**
     * 详情页-获取景区下的门票列表
     */
    public function getTicketList()
    {
        header("Access-Control-Allow-Origin: *"); // 允许任意域名发起的跨域请求
        //景区id
        $lid = I('lid', 0, 'intval');
        //上级供应商id
        $aid = I('aid', 0, 'intval');

        //页面上面选定的日期
        $date = I('date', '', 'strval');
        $date = strtotime($date) ? date('Y-m-d', strtotime($date)) : date('Y-m-d');

        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $inWechatSmApp = $this->inAlipaySmallApp();
        $channelVal    = $this->checkChannel($inWechatSmApp);
        $productList   = $this->_getLandTickets($lid, $channelVal);
        $tags          = $this->_parseTicketsTags($productList);

        //是否根据上级id过滤
        $aidFilter = false;
        if ($aid) {
            $applySidArr = array_column($productList, 'superior_id');
            if (in_array($aid, $applySidArr)) {
                $aidFilter = true;
            }
        }
        //从java获取指定日期票类价格
        $ticketIds = array_column($productList, 'id');
        $ticketIds = implode(',', $ticketIds);
        $priceData = TicketApi::getSinglePrices($ticketIds, $date);

        // 从java获取了门票列表
        $list = [];
        foreach ($productList as $item) {
            if ($aidFilter && $item['superior_id'] != $aid) {
                continue;
            }

            $ptype  = $item['type'];
            $tPrice = $item['counter_price'] / 100;

            if (\inWechatSmallApp()) {
                $jsPrice = $item['window_price'] / 100;
            } else {
                $jsPrice = $item['retail_price'] / 100;
            }
            if ($ptype == 'H') {
                $tPrice = $priceData[$item['id']]['counter_price'];
                $tPrice = (isset($tPrice)) ? $tPrice / 100 : '';
                if (\inWechatSmallApp()) {
                    $jsPrice = $priceData[$item['id']]['window_price'];
                    $jsPrice = (isset($jsPrice)) ? $jsPrice / 100 : '';
                } else {
                    $jsPrice = $priceData[$item['id']]['retail_price'];
                    $jsPrice = (isset($jsPrice)) ? $jsPrice / 100 : '';
                }
            }

            $tmp = [
                'ticket'      => $item['name'],
                'pid'         => $item['product_id'],
                'tid'         => $item['id'],
                // 'px'      => $item['px'],
                'aid'         => $item['superior_id'],
                'sid'         => $item['supplier_id'],
                'jsprice'     => $jsPrice,
                'tprice'      => $tPrice,
                'tags'        => $tags[$item['id']] ?: '',
                'intro'       => explode('<br />', nl2br($item['introduction'])),
                'refund_rule' => $item['refund_rule'],
                'riskWarning' => $item['riskWarning'],
            ];

            $list[] = $tmp;
        }

        if ($list) {
            $list = $this->_fillExtraForTicketList($list, $ptype);
        }

        // 待确定 todo java
        if ($this->_allDisMan) {
            $list = AllDisProcess::getAllDisPrice($this->_supplyId, $list, 'list');
            $list = AllDisProcess::calRecommendCommission($this->_supplyId, $list);
        }
        //微信抢购活动
        //$list = $this->_seckillDecorate($list);

        $this->apiReturn(200, ['type' => $ptype, 'list' => $list]);
    }

    /**
     * 门票列表根据产品类型填充额外信息
     * <AUTHOR>
     * @date   2017-11-20
     *
     * @param  array  $list  门票列表数据
     * @param  string  $type  产品类型
     *
     * @return arrray
     */
    private function _fillExtraForTicketList($list, $type)
    {

        switch ($type) {

            case 'F':
                //套票
                $list = $this->_fillExtraForTicketListByF($list, $type);
                break;

            case 'H':
                //由于支付宝小程序暂无酒店年卡的相关业务，原有代码就会报错，为了不再报错，粗暴的先返回空数组
                //$list = $this->_fillExtraForTicketListByH($list, $type);
                $list = [];
                break;

            case 'I':
                //$list = $this->_fillExtraForTicketListByI($list, $type);
                $list = [];
                break;

            case 'default':
                break;
        }

        return $list;
    }

    /**
     * 门票列表填充套票信息
     * <AUTHOR>
     * @date   2017-11-20
     *
     * @param  array  $list  门票列表数据
     * @param  string  $type  产品类型
     *
     * @return arrray
     */
    private function _fillExtraForTicketListByF($list, $type)
    {

        //套票的话需要返回子票信息
        $list = $this->_parseSonTickets($list);

        return $list;

    }

    /**
     * 解析票类的一些标签属性
     *
     * @param  [type] $tickets 门票列表
     *
     * @return [type]          [description]
     */
    private function _parseTicketsTags($tickets)
    {
        $tags = [];

        foreach ($tickets as $item) {
            if ($item['preorder_early_days'] > 0) {
                $tags[$item['id']][] = "提前{$item['preorder_early_days']}天";
            }

            if ($item['pay_way'] == 0) {
                $tags[$item['id']][] = '现场支付';
            }

            if ($item['pay'] == 3) {
                $tags[$item['pid']][] = '会员卡支付';
            }

            //退票规则：添加了这个值 -1=不可退且是可提现
            if ($item['refund_rule'] == 2 || $item['refund_rule'] == -1) {
                $tags[$item['id']][] = '不可退';
            }
        }

        return $tags;
    }

    /**
     * 解析子票信息
     * <AUTHOR>
     * @time   2017-01-15
     *
     * @param  array  $tickets  门票列表
     *
     * @return array              [description]
     */
    private function _parseSonTickets($tickets = [])
    {
        if (!is_array($tickets) || !$tickets) {
            return [];
        }

        //$ticketApi = new TicketApi();
        //$javaApi = new \Business\JavaApi\Product\PackageTicket();
        $packApi = new \Business\PackTicket\PackRelation();
        foreach ($tickets as $key => $item) {
            //$sonTickets = $javaApi->queryPageTicketInfoListByParentId($item['tid']);
            //$sonTickets = $ticketApi->getSonTicketList($item['tid']);
            $sonTickets = $packApi->queryPageTicketInfoListByParentId($item['tid']);
            // 兼容写法 ，有改版记得修改简单点
            if ($sonTickets['code'] == 200) {
                foreach ($sonTickets['data'] as $sonKey => $sonVal) {
                    $tickets[$key]['sonTickets'][$sonKey]['num']   = $sonVal['num'];
                    $tickets[$key]['sonTickets'][$sonKey]['title'] = $sonVal['item_name'] . $sonVal['ticket_name'];
                    $tickets[$key]['sonTickets'][$sonKey]['lid']   = $sonVal['item_id'];
                }
            }
        }

        return $tickets ?: [];
    }

    /**
     * 解析年卡是否需要填写身份证
     * <AUTHOR>
     * @date   2017-12-01
     *
     * @param  array  $list  门票列表数据
     *
     * @return array
     */
    private function _parseCertLimit($list)
    {
        $javaApi = new \Business\CommodityCenter\Ticket();
        foreach ($list as &$item) {
            $tidAttr = $javaApi->queryTicketAttrsById($item['tid']);
            $needNo  = 1;
            foreach ($tidAttr as $ext) {
                if ($ext['key'] == 'annual_identity_info') {
                    $needNo = $ext['val'];
                }
            }
            $item['need_id'] = $needNo;
        }

        return $list;
    }

    /**
     * 获取景区门票列表
     *
     * @param  int  $lid  景区id
     *
     * @return [type]      [description]
     */
    private function _getLandTickets($lid, $channel = '', $operId = '')
    {
        if (!$lid) {
            return [];
        }
        // 获取景区下的门票列表
        $ticketBiz   = new bizTicket();
        $ticketsList = $ticketBiz->getRetailTicketList($this->_supplyId, $lid, $channel, $operId);

        if ($ticketsList['code'] != 200) {
            return [];
        }

        return $ticketsList['data'];
    }

    /**
     * 预订页面-景区以及相关订单信息
     */
    public function getBookInfo()
    {
        header("Access-Control-Allow-Origin: *"); // 允许任意域名发起的跨域请求
        //获取预定须知的类型
        $this->_getBookInfoForCommon();
    }

    /**
     *  共性产品的预定信息(景区，线路，酒店等等)
     * <AUTHOR>
     * @date   2017-12-08
     */
    private function _getBookInfoForCommon()
    {
        // 根据平台规则重新写
        $tid    = I('tid', '', 'intval');
        $type   = I('type', '', 'strval');
        $landId = I('lid', '', 'intval');
        $aid    = I('aid', '', 'intval');
        $pid    = I('pid', '', 'intval');

        //页面上面选定的日期
        $date = I('date', '', 'strval');
        $date = strtotime($date) ? date('Y-m-d', strtotime($date)) : false;

//        if ($landId < 1 || $applyDid < 1 || $tid < 1) {
//            $this->apiReturn(204, [], '参数错误');
//        }

        if ($pid < 1 || $aid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $bookInfo = $this->_getBookInfo($pid, $tid, $aid);
        // 没传tid 的特殊处理
        if (!$tid) {
            $tid = $bookInfo['tid'];
        }
        $tickets = $this->_getBookList($tid, $bookInfo['startDate'], $aid);

        if (!$tickets) {
            $this->apiReturn(204, [], '票类不存在');
        }

        if (!empty($tickets)) {
            $tickets = array_key($tickets, 'pid');
            foreach ($tickets as $key => $value) {
                if ($value['pid'] == $pid) {
                    $mainTicket = $value;
                    unset($tickets[$key]);
                    array_unshift($tickets, $mainTicket);
                }
            }
        }

        $bookInfo['tickets'] = array_values($tickets);

        //获取价格的日期
        $startDate = $date ? $date : date('Y-m-d', strtotime($bookInfo['startDate']));
        $ticketIds = array_column($bookInfo['tickets'], 'tid');
        $ticketIds = implode(',', $ticketIds);
        $priceData = TicketApi::getSinglePrices($ticketIds, $startDate);

        foreach ($bookInfo['tickets'] as $key => $val) {
            if (!isset($priceData[$val['tid']])) {
                continue;
            }
            $bookInfo['tickets'][$key]['tprice']       = $priceData[$val['tid']]['counter_price'] / 100;
            $bookInfo['tickets'][$key]['jsprice']      = $priceData[$val['tid']]['window_price'] / 100;
            $bookInfo['tickets'][$key]['retail_price'] = $priceData[$val['tid']]['retail_price'] / 100;
        }

        $businessCache = (new \Library\Tools\BusinessCache())->getBusinessCache($this->_supplyId);

        if (!$_SESSION['memberID'] || (isset($businessCache['identify']) && $businessCache['identify'] == 'allDis')) {
            $bookInfo['alldis'] = 1;
        } else {
            $bookInfo['alldis'] = 0;
        }

        $this->apiReturn(200, $bookInfo);
    }

    /**
     * 预定页面-门票列表
     * @param  int  $pid  产品id
     * @param  int  $aid  上级供应商id
     * @param  string  $date  可预订日期
     */
    private function _getBookList($tid, $date, $aid = 0)
    {
        // java 接口获取门票信息
        $product    = [];
        $ticketBiz  = new \Business\CommodityCenter\Ticket();
        $ticketData = $ticketBiz->fieldConversion($ticketBiz->queryTicketInfoById($tid, '', '', '', '', false, $aid,
            $this->_supplyId, $this->saleChannel));
        //$ticketApi  = new TicketApi();
        //$ticketData = $ticketApi->getTickets($tid, $this->_supplyId, $aid, $this->saleChannel);
        $product['id']     = $ticketData['id'];
        $product['landid'] = $ticketData['item_id'];
        // 获取个景点信息
        $productBiz  = new bizProduct();
        $landInfoArr = $productBiz->getProductInfo($this->_supplyId, $ticketData['item_id'], 0);
        $ptype       = $landInfoArr['data']['p_type'];
        $tickets     = $this->_getLinkProduct($product['id'], $product['landid'], $ptype, $date);

        if (!$tickets) {
            return [];
        }

        if ($this->_allDisMan) {
            $tickets = AllDisProcess::getAllDisPrice($this->_supplyId, $tickets, 'book');
        }

        if ($ptype == 'F') {
            //获取子票列表
            $tickets = $this->_parseSonTickets($tickets);
        }

        if ($ptype == 'I') {
            //解析是否需要身份证
            $tickets = $this->_parseCertLimit($tickets);
        }

        if ($ptype == 'H') {
            //获取演出区域id
            $tickets = $this->_getZoneId($tickets);

            $tidArr      = array_column($tickets, 'tid');
            $zoneMapping = (new \Model\Product\Ticket())->getZoneName($tidArr);

            foreach ($tickets as &$item) {
                $item['zone_name'] = $zoneMapping[$item['tid']]['zone_name'];
                $item['zone_id']   = $zoneMapping[$item['tid']]['id'];
            }
        }

//        pft_log('yunv/test', 'getBookList 的返回' . print_r($tickets, 1));

        return $tickets;
    }

    /**
     * 景区以及相关订单信息
     *
     * @param  [type] $pid 产品id
     *
     * @return [type]      [description]
     */
    private function _getBookInfo($pid = '', $tid = '', $aid)
    {
        // 同时为空就有问题了
        if (!$pid && !$tid) {
            return [];
        }

        $Ticket    = new Ticket('slave');
        $ticketBiz = new \Business\Product\Ticket();
        //$ticketApi = new TicketApi();

        // 如果tid为空 pid 有值要进行一层转换
        if ($pid && $tid == '') {
            $tidPidArr = $Ticket->getTicketInfoByPid($pid, 'id,pid');
            $tid       = $tidPidArr['id'];
        }

        $ticketApi  = new \Business\CommodityCenter\Ticket();
        $ticketData = $ticketApi->fieldConversion($ticketApi->queryTicketInfoById($tid, '', '', '', '', false, $aid,
            $this->_supplyId, $this->saleChannel));
        //$ticketData = $ticketApi->getTickets($tid, $this->_supplyId, $this->saleChannel, $aid);
        $cancelCost = json_decode($ticketData['refund_stair'], true) ?: [];

        $bookInfo = [
            'refund_rule'       => $ticketData['refund_rule'],
            'refund_early_time' => $ticketData['refund_early_minu'],
            'refund_after_time' => $ticketData['refund_after_time'] ?? 0,
            'reb'               => $ticketData['refund_value'],
            'reb_type'          => $ticketData['refund_type'],
            'cancel_cost'       => $cancelCost,
            'batch_check'       => $ticketData['verify_way'],
            'batch_day'         => $ticketData['verify_limit_amount'],
            //最近可售日期
            'startDate'         => $ticketBiz->getHasRetailPriceDate($pid, 'min'),
            'tid'               => $ticketData['id'],
        ];

        //提前预定天数
        if ($ticketData['preorder_early_days']) {
            $preDate = date('Y-m-d', strtotime("+{$ticketData['preorder_early_days']} days"));
            if ($preDate > $bookInfo['startDate']) {
                $bookInfo['startDate'] = $preDate;
            }
        }

        //有效时间
        $validTime = '';
        if (strtotime($ticketData['valid_period_start']) > 0) {
            //时间段内有效
            $tmpS = date('Y-m-d', strtotime($ticketData['valid_period_start']));
            $tmpE = date('Y-m-d', strtotime($ticketData['valid_period_end']));

            if ($tmpS > date('Y-m-d')) {
                $bookInfo['startDate'] = $tmpS;
            }
            if ($ticketData['valid_period_timecancheck']) {
                $tmpS = '随游玩日期变化';
            }
            $validTime = $tmpS . '~' . $tmpE;
        } elseif ($ticketData['valid_period_days']) {
            //多少天内有效
            $validTime = $ticketData['valid_period_days'];
        } else {
            //当天有效
            $validTime = 0;
        }
        $bookInfo['validTime'] = (string)$validTime;
        $bookInfo['validType'] = $ticketData['valid_period_type'];

        if ($ticketData['preorder_expire_time'] && in_array($bookInfo['startDate'], [date('Y-m-d')])) {
            //需要在几点前预定
            if (date('H:i:s') > $ticketData['preorder_expire_time']) {
                //预定日期+1
                $bookInfo['startDate'] = date('Y-m-d', strtotime($bookInfo['startDate']) + 3600 * 24);
            }
        }

        //年卡身份证信息  0 非必填 1激活必填 2下单必填
        $annualIdentityInfo = isset($ticketData['annual_identity_info']) ? (int)$ticketData['annual_identity_info'] : 0;
        $bookInfo['needID'] = $annualIdentityInfo;

        //可验证时间
        $verifyTime = '';
        if ($ticketData['verify_time'] == 0) {

            $verifyMap = [1, 2, 3, 4, 5, 6, 7];
            //按星期验证
            if ($ticketData['verify_disable_week']) {
                $verifyTime = array_diff($verifyMap, explode(',', $ticketData['verify_disable_week']));
                foreach ($verifyTime as &$item) {
                    $item = $item == 0 ? 6 : $item - 1;
                }
                $verifyTime = array_values($verifyTime);
            } else {
                //不限制验证时间
                $verifyTime = -1;
            }

        } else {
            //时间段内有效
            $vTimeLimit = $ticketData['verify_time'];
            $verifyTime = str_replace('|', '~', $vTimeLimit);
        }
        $bookInfo['verifyTime'] = $verifyTime;

        // 获取个景点信息
        $productBiz         = new bizProduct();
        $landInfoArr        = $productBiz->getProductInfo($this->_supplyId, $ticketData['item_id'], 0);
        $bookInfo['title']  = $landInfoArr['data']['title'];
        $bookInfo['p_type'] = $landInfoArr['data']['p_type'];

        //线路获取集合地点
        if ($landInfoArr['data']['p_type'] == 'B') {
            $bookInfo['assStation'] = json_decode($ticketData['assembling_place'], true) ?: [];
            $bookInfo['assStation'] = array_values($bookInfo['assStation']);
        }

        return $bookInfo;
    }

    /**
     * 获取联票产品
     *
     * @param  int  $tid  门票id
     * @param  int  $landid  景区id
     * @param  string  $type  景区类型
     * @param  string  $date  可预订日期
     *
     * @return array
     */
    private function _getLinkProduct($tid, $landid, $type, $date)
    {
        $inWechatSmApp = $this->inWechatSmallApp();
        $channelVal    = $this->checkChannel($inWechatSmApp);
        $tickets       = $this->_getLandTickets($landid, $channelVal);

        $unLinkType = ['C', 'H', 'I', 'J']; //不能下联票

        $tidArr = [];
        foreach ($tickets as $ticket) {
            if (in_array($type, $unLinkType) && $ticket['id'] != $tid) {
                continue;
            }
            $tidArr[] = $ticket['id'];
        }

        $orderUnityBusiness = new \Business\Order\OrderUnity;
        //获取可以联票的门票id
        $idArrayInfo = $orderUnityBusiness->linkProductFilterCommon($tid, $tidArr, 'mobile');
        if ($idArrayInfo['code'] == 200) {
            $linkTidArr = $idArrayInfo['data'];
        } else {
            $linkTidArr = [];
        }

        $return = [];
        foreach ($tickets as $key => $item) {
            // 属性不一致无法联票
            if (!in_array($item['id'], $linkTidArr)) {
                continue;
            }

            $pos      = array_search($item['id'], $idArrayInfo['data']);
            $tmpPrice = $item['retail_price'];

            if (\inWechatSmallApp()) {
                $tmpPrice = $item['window_price'] ?: $item['counter_price'];
            }

            $return[$pos] = [
                'title'         => $item['name'],
                'jsprice'       => $tmpPrice / 100,
                'tprice'        => $item['counter_price'] / 100,
                'pid'           => $item['product_id'],
                'tid'           => $item['id'],
                'aid'           => $item['superior_id'],
                'buy_low'       => (int)$item['buy_min_amount'],
                'buy_up'        => (int)$item['buy_max_amount'],
                'retail_price'  => $item['retail_price'] / 100,
                'age_limit_max' => $item['age_limit_max'] ? (string)$item['age_limit_max'] : '',
                'age_limit_min' => $item['age_limit_min'] ? (string)$item['age_limit_min'] : '',
            ];
        }
        ksort($return);

        return array_values($return);
    }

    /**
     * 预定页面-日历价格
     * @return [type] [description]
     */
    public function getCalendarPrice()
    {
        $pid  = I('pid', '', 'intval');
        $date = I('date', date('Y-m'));
        $aid  = I('aid', '', 'intval');
        $tid  = I('tid', '', 'intval');

        if (!$pid || !$date) {
            $this->apiReturn(204, [], '参数错误');
        }

        // 计算请求的其实时间和结束时间
        $startDate = $date . '-01';
        $endDate   = $date . date('-t', strtotime($date));

        $calendar = TicketApi::getrRetailCalendar($this->_supplyId, $tid, $startDate, $endDate);
        $priceset = [];
        foreach ($calendar as $key => $priceVal) {
            $dateKey            = $priceVal['time'];
            $priceset[$dateKey] = $priceVal['lPrice'];
            if (\inWechatSmallApp()) {
                // 取的是窗口价 或 门市价
                $priceset[$dateKey] = $priceVal['windowPrice'] ?: $priceVal['mPrice'];
            }
        }

        //单位转换成为元
        $priceset = array_map(function ($val) {
            return $val / 100;
        }, $priceset);

        if ($this->_allDisMan) {
            $tmp      = [
                'pid'  => $pid,
                'aid'  => $aid,
                'list' => $priceset,
            ];
            $priceset = AllDisProcess::getAllDisPrice($this->_supplyId, $tmp, 'calendar');
            $priceset = $priceset['list'];
        }

        $this->apiReturn(200, $priceset);
    }

    /**
     * 预定页面-获取价格和库存
     */
    public function getPriceAndStorage()
    {
        $aid    = I('aid', '', 'intval');
        $date   = I('date', date('Y-m-d'));
        $tids   = I('tids');
        $tidArr = explode('-', $tids);

        if (!$tidArr || !$date) {
            $this->apiReturn(201, [], '参数错误');
        }

        $date = date('Y-m-d', strtotime($date));

        if ($date < date('Y-m-d')) {
            $this->apiReturn(202, '请选择正确的日期');
        }

        $ticketStr = implode(',', $tidArr);

        //获取库存
        $storageMap = StorageApi::getBatchStorageByPlayDate($ticketStr, $date);

        if (empty($storageMap)) {
            $this->apiReturn(208, [], '获取库存信息出错，请稍后重试');
        }

        //获取价格
        $priceData = $this->_getPrice($aid, $date, $tidArr);

        if (!$priceData) {
            $this->apiReturn(205, [], '没有相关数据');
        }

        // 先利用tidArr获取pidArr
        $ticketModel = new Ticket();
        $tidPidArr   = $ticketModel->getMuchTicketInfo($tidArr, 'id, pid');

        if (isset($_COOKIE['dwercn'])) {
            var_dump($ticketStr, $date, $storageMap, $tidPidArr, $priceData);
            die;
        }

        $return = [];
        foreach ($priceData as $tid => $item) {
            $store = $storageMap[$tid];
            $price = $item['retail_price'] / 100;
            if (\inWechatSmallApp() || $this->aliMiniAppid != '') {
                $price = $item['window_price'] / 100;
            }
            $return[$tidPidArr[$tid]] = [
                'price' => $price,
                'store' => $store >= -1 ? $store : 0,
            ];
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 预定页面-获取价格
     * @param  array  $tidArr  tid数组   [1026, 1027]
     * @param  int  $aid  上级供应商id
     * @param  string  $date  日期 2016-08-10
     *
     * @return  [1026 => 1, 1027 => 2]
     */
    private function _getPrice($aid, $date, $tidArr = '', $pidArr = '')
    {
        $priceset = [];
        if ($pidArr != '') {
            $tickModel = new Ticket('slave');
            $priceset  = $tickModel->getMuchRetailPrice($pidArr, $date);

            if ($this->_allDisMan) {
                foreach ($pidArr as $pid) {
                    $tmp[] = [
                        'pid'   => $pid,
                        'aid'   => $aid,
                        'price' => $priceset[$pid],
                    ];
                }
                $tmpPrice = AllDisProcess::getAllDisPrice($this->_supplyId, $tmp, 'interface');
                foreach ($tmpPrice as $item) {
                    $priceset[$item['pid']] = $item['price'];
                }
                // $priceset = array_values($priceset);
            }
        }

        // 返回从java获取到的价格数组
        if ($tidArr != '') {
            $ticketIds = implode(',', $tidArr);
            $priceset  = TicketApi::getSinglePrices($ticketIds, date('Y-m-d', strtotime($date)));
        }

        return $priceset;
    }

    /**
     * 获取产品集合
     *
     * @param  [type] $option 筛选参数
     *
     * @return [type]         [description]
     */
    private function _getProductSet($option, $allType = [], $pageNo = 1, $pageNum = 10)
    {
        if (empty($allType)) {
            $allType = $this->_allowType;
        }

        $productArr    = [];
        $inWechatSmApp = $this->inWechatSmallApp();
        $channelVal    = $this->checkChannel($inWechatSmApp);
        $productName   = isset($option['title']) ? $option['title'] : '';
        $province      = isset($option['province']) ? $option['province'] : '';
        $city          = isset($option['city']) ? $option['city'] : '';
        $topic         = isset($option['topic']) ? $option['topic'] : '';
        $level         = isset($option['level']) ? $option['level'] : '';
        $allowTypeStr  = $allType ? implode(',', $allType) : '';

        $productListBiz = new bizProductList();
        if ($inWechatSmApp || $this->aliMiniAppid) {
            // 自供应产品
            $productArr = $productListBiz->getRetailList($this->_supplyId, $pageNo, $pageNum,
                $channelVal, $productName, $allowTypeStr,
                $level, $topic, $province, $city, '', false);
        } else {
            $supplyIds = $this->_supplyId;
            if ($supplyIdsStr = $this->getSupplyIds()) {
                $supplyIds = $supplyIdsStr;
            }
            // 转分销+自供应
            $productArr = $productListBiz->getRetailList($supplyIds, $pageNo, $pageNum,
                $channelVal, $productName, $allowTypeStr,
                $level, $topic, $province, $city, '', true);
        }

        // 无数据提前返回
        if ($productArr['code'] != 200) {
            return [];
        }

        // 返回取得数据
        return $productArr['data'];
    }

    /**
     * 整合前端需要的数据
     *
     * @param  array  $lands  景区信息
     * @param  array  $pidArr  门票pid数组
     *
     * @return
     */
    private function _productDeal($LandTicketData)
    {
        // 获取产品数据中最低价格数据
        $data = [];
        if ($LandTicketData && is_array($LandTicketData)) {
            foreach ($LandTicketData as $landVal) {
                $data[] = array(
                    'lid'      => $landVal['id'],
                    // 'tid'      => $landVal['ticket'][$minKey]['tid'],
                    'title'    => $landVal['name'],
                    // 'area'    => $tickets[0]['area'],
                    'pid'      => $landVal['product_id'],
                    'province' => $landVal['province'],
                    'city'     => $landVal['city'],
                    'address'  => $landVal['address'],
                    'aid'      => $landVal['supplier_id'],
                    'imgpath'  => $landVal['img_path'],
                    'topic'    => $landVal['topics'],
                    'lptype'   => $landVal['type'],
                    'jsprice'  => sprintf('%.2f', ($landVal['retail_price'] / 100)),
                    'tprice'   => sprintf('%.2f', ($landVal['counter_price'] / 100)),
                );
            }
        }

        return $data;
    }

    /**
     * 获取微商城配置
     * @return [type] [description]
     */
    public function getCustomConfig($return = false)
    {

        $config = $this->getMallConfig();
        $name   = $config['name'];
        $others = json_decode($config['others'], true);
        $banner = $others['banner'];

        if (!$banner) {
            $banner[0] = [
                'http://images.12301.cc/123624/14786829442552.jpg' => '',
            ];
        }

        $data = [
            'name'   => $name ?: '微商城',
            'banner' => $banner,
            'tel'    => $config['telephone'],
            'theme'  => $others['template'] ?: 'default',
        ];

        if ($return) {
            return $data;
        }

        $this->apiReturn(200, $data);
    }

    /**
     * 获取产品类型列表
     *
     * @param  $return bool 是否当作函数返回值返回
     *
     * @return [type] [description]
     */
    public function getTypeList($back = false)
    {
        $config = $this->getMallConfig();
        $others = json_decode($config['others'], true);

        $return = [];
        if (isset($others['type'])) {
            foreach ($others['type'] as $identify => $name) {
                $return[] = [
                    'name'      => $name,
                    'identify'  => $identify,
                    "isChecked" => "true",
                ];
            }
        } else {
            $return = [
                // ['name' => '景区', 'identify' => 'A'],
                // ['name' => '酒店', 'identify' => 'C'],
                // ['name' => '周边游', 'identify' => 'B'],
                // ['name' => '套票', 'identify' => 'F'],
                // ['name' => '演出', 'identify' => 'H'],
            ];
        }
        //加入主题
        if (isset($others['themes'])) {
            foreach ($others['themes'] as $key => $name) {
                if (is_array($name)) {
                    $isChecked = $name['isChecked'];
                    $name      = $name['name'];
                }
                if ($isChecked == 'false') {
                    continue;
                }
                $return[] = [
                    'name'      => $name,
                    'show_name' => $key,
                    'identify'  => 'theme',
                    "isChecked" => $isChecked,
                ];
            }
        }

        //加入营销活动
        if (isset($others['marketing'])) {
            foreach ($others['marketing'] as $key => $name) {
                if (is_array($name)) {
                    $isChecked = $name['isChecked'];
                    $name      = $name['name'];
                }
                if ($isChecked == 'false') {
                    continue;
                }
                $return[] = [
                    'name'      => $name,
                    'show_name' => $key,
                    'identify'  => 'marketing',
                    "isChecked" => $isChecked,
                ];
            }
        }

        if ($back) {
            return $return;
        }

        $this->apiReturn(200, $return);
    }
}
