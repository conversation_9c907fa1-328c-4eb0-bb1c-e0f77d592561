<?php
/**
 * 专项预存配置相关
 * User: xwh
 * Date: 2020-09-25
 * Time: 下午11:11
 */

namespace Controller\ExclusiveAccount;

use Library\Controller;
use Library\Constants\Account\BookSubject;

class ExclusiveDeposited extends Controller
{
    /**
     * 用户专项预存使用记录
     * <AUTHOR>
     * @date   2020-09-16
     *
     * @return array
     */
    public function userRecordQuery()
    {
        $login       = $this->getLoginInfo();
        $orderNum    = I('order_num', '', 'strval,trim');
        $beginDate   = I('begin_date', '', 'strval,trim');
        $endDate     = I('end_date', '', 'strval,trim');
        $subjectCode = I('subject_code', -1, 'intval');
        $page        = I('page', 1, 'intval');
        $pageSize    = I('size', 20, 'intval');
        $excel       = I('excel', 0, 'intval');

        if ($subjectCode > -1 && !in_array($subjectCode, [
                BookSubject::SMS_ACCOUNT_CODE,
                BookSubject::VOUCHER_ACCOUNT_CODE,
            ])) {
            $this->apiReturn(204, '交易类型参数错误');
        }

        $speciledAccountBuz = new \Business\ExclusiveAccount\ExclusiveDeposited();
        $result             = $speciledAccountBuz->userRecordQuery($login['sid'], $orderNum, $beginDate, $endDate,
            $subjectCode, $page, $pageSize);

        if ($excel) {
            array_unshift($result['data'], [
                '交易时间',
                '订单号',
                '第三方交易号',
                '流水号',
                '交易类型',
                '交易数量（条）',
                '剩余数量（条）',
                '备注',
            ]);
            $this->excelReturn('用户专项预存使用记录', '用户专项预存使用记录 ' . $beginDate . '-' . $endDate, $result['data']);
            die;
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 使用记录汇总使用条数
     * <AUTHOR>
     * @date   2020-09-16
     *
     * @return array
     */
    public function summaryUseNum()
    {
        $login = $this->getLoginInfo();
        $orderNum  = I('post.order_num', '', 'strval,trim');
        $beginDate = I('post.begin_date', '', 'strval,trim');
        $endDate   = I('post.end_date', '', 'strval,trim');

        $speciledAccountBuz = new \Business\ExclusiveAccount\ExclusiveDeposited();
        $result             = $speciledAccountBuz->summaryUseNum($login['sid'],$orderNum,$beginDate,$endDate);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 用户专项预存统计
     * <AUTHOR>
     * @date   2020-09-16
     *
     * @return array
     */
    public function statistics()
    {
        $login              = $this->getLoginInfo();
        $speciledAccountBuz = new \Business\ExclusiveAccount\ExclusiveDeposited();
        $result             = $speciledAccountBuz->statistics($login['sid']);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}