<?php
/**
 * 专项预存配置相关(管理端)
 * User: xwh
 * Date: 2020-09-25
 * Time: 下午11:11
 */

namespace Controller\ExclusiveAccount;

use Library\Controller;
use Library\Constants\Account\BookSubject;

class SpecialedAccount extends Controller
{
    private function authority()
    {
        if (!$this->isSuper()) $this->apiReturn(400, '无权限');
    }

    /**
     * 资费配置列表
     * <AUTHOR>
     * @date   2020-09-24
     *
     * @param  integer $configType 资费类型
     * @param  integer $state 资费状态
     * @param  integer $page 页数
     * @param  integer $pageSize 每页数量
     *
     * @return array
     */
    public function configList()
    {
        $configType   = I('post.rate_type', -1, 'intval');    //默认传-1 0 电子凭证 1 短信
        $state        = I('post.state', -1, 'intval');          //1有效，0无效"
        $purchaseType = I('post.purchase_type', -1, 'intval');  //购买类型 0固定 1阶梯
        $page         = I('post.page', 1, 'intval');
        $pageSize     = I('post.size', 20, 'intval');

        if ($configType > -1 && !in_array($configType, [0, 1])) {
            $this->apiReturn(204, '账本科目编码参数错误');
        }
        if ($state > -1 && !in_array($state, [0, 1])) {
            $this->apiReturn(204, '状态参数错误');
        }
        if ($purchaseType > -1 && !in_array($purchaseType, [0, 1])) {
            $this->apiReturn(204, '购买类型错误');
        }

        $speciledAccountBuz = new \Business\ExclusiveAccount\SpecialedAccount();
        $result             = $speciledAccountBuz->configList($configType, $state, $purchaseType, $page, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 资费配置详情
     * <AUTHOR>
     * @date   2020-09-16
     *
     * @return array
     */
    public function configDetail()
    {
        //身份验证
        $this->authority();
        $configId = I('post.id', 0, 'intval');

        if (!$configId) {
            $this->apiReturn(204, '参数错误');
        }
        $speciledAccountBuz = new \Business\ExclusiveAccount\SpecialedAccount();
        $result             = $speciledAccountBuz->configDetail($configId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    /**
     * 通过数量及配置类型获取对应阶梯价套餐
     * <AUTHOR>  Li
     * @date   2020-10-11
     *
     * @return array
     */
    public function prestoredQuery()
    {
        $nums     = I('post.nums', 0, 'intval');        //购买阶梯配置的数量
        $rateType = I('post.rate_type', 0, 'intval');   //配置类型  0凭证 1短信

        if (!is_numeric($nums) || $nums <= 0) {
            $this->apiReturn(203, [], '购买数量不能小于0');
        }
        if (!in_array($rateType, [0, 1])) {
            $this->apiReturn(203, [], '金额类型传输有误');
        }

        $speciledAccountBuz = new \Business\ExclusiveAccount\SpecialedAccount();
        $result             = $speciledAccountBuz->queryLadderConfigByNumAndSubjectCode($nums, $rateType);

        if (empty($result['data'])) {
            $this->apiReturn(204, [], '未获取到对应数量的配置，请重新输入');
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}