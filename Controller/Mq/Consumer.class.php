<?php
/**
 * mq代理推送数据接口
 * (因为目前java回调过来是通过http,现在约定大于三秒就会超时，要注意下这点)
 * <AUTHOR>
 * @time   2019-12-16
 */

namespace Controller\Mq;

use Library\Controller;
use Library\Cache\Cache;

class Consumer extends Controller
{

    //主题列表
    private $_topicList = ['orderdataSync','GroupLocal','GroupLocal1'];

    private $_cache;

    const SUCCESS_KEY_NAME = 'mq:consumer:success';

    /**
     * mq代理回调统一接口
     * <AUTHOR>
     * @date   2019-12-14
     *
     * @param  string    $producerUuid   生产者uuid
     * @param  string    $topicUuid      主题uuid
     * @param  string    $data           业务数据 json格式
     * @param  integer   $storeTime      消费时间
     * @param  integer   $tag            主题下的标签
     * @return
     */
    public function index()
    {
        $postJson = file_get_contents('php://input');
        $postData = json_decode($postJson, true);

        $returnFail = 'fail';
        $returnSucc = 'success';

        if (empty($postJson)) {
            $this->apiReturn(500, [], 'fail');
        }

        $producerUuid = $postData['producerUuid'];
        $topicUuid    = $postData['topicUuid'];
        $data         = $postData['data'];
        $storeTime    = $postData['storeTime'];
        $msgId        = $postData['transactionId'];
        $tag          = $postData['tag'];

        if (!$producerUuid || !$topicUuid || !$data) {
            $this->apiReturn(500, [], $returnFail);
        }

        $this->_cache = Cache::getInstance('redis');
        $cacheKey = self::SUCCESS_KEY_NAME . $msgId;

        //重复消费
        if ($this->_cache->get($cacheKey)) {
            pft_log('mq_proxy/consumer_msg_repeat', json_encode([$postJson, $cacheKey]));
            $this->apiReturn(200, [], $returnSucc);
        }

        $configModel = new \Model\MqProxy\ProxyConfig();
        $topicConfig = $configModel->getTopicConfigByUUid($topicUuid);

        if (!$topicConfig) {
            $this->apiReturn(500, [], $returnFail);
        }

        //主题
        $topic = $topicConfig['topic'];

        if (!in_array($topic, $this->_topicList)) {
            $this->apiReturn(500, [], '');
        }

        $postArr = json_decode($data, true);
        $result  = $this->$topic($postArr);
        pft_log('mq_proxy/consumer_msg', json_encode([$postJson, $result]));

        if ($result['rs']) {
            $this->_cache->set($cacheKey, $storeTime, 86400);
            $this->apiReturn(200, [], $returnSucc);
        } else {
            $this->apiReturn(500, [], $returnFail);
        }
    }


    /**
     * orderdata库的数据同步
     * <AUTHOR>
     * @date   2019-12-14
     *
     * @param  string    $data           业务数据 json格式
     *
     * @return
     */
    protected function orderdataSync($data = [], $tag = '')
    {

        return ['rs' => true, 'err' => 'success'];
    }



    protected function GroupLocal($data = [], $tag = '')
    {
        return ['rs' => true, 'err' => 'success'];
    }



    protected function GroupLocal1($data = [], $tag = '')
    {
        return ['rs' => true, 'err' => 'success'];
    }
}