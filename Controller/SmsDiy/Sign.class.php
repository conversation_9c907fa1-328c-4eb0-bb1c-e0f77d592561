<?php
/**
 * 短信抬头管理(应用)
 * @date 2020-12-11
 * <AUTHOR>
 */

namespace Controller\SmsDiy;

use Library\Controller;
use Business\SmsDiy\Sign as SignBiz;

class Sign extends Controller
{
    //用户信息定义在这里
    private $mid; //当前账号id
    private $sid; //上级id
    private $_signBiz;

    public function __construct()
    {
        $loginInfo = $this->getLoginInfo('ajax');
        $this->mid = $loginInfo['memberID'];
        $this->sid = $loginInfo['sid'];

        $this->_signBiz = new SignBiz();
    }

    /**
     * 检测是否在使用
     * <AUTHOR>
     * @date 2021/1/5
     *
     * @return bool
     */
    private function checkUse()
    {
        $signBiz = $this->_signBiz;
        if (!$signBiz->checkUseModule($this->sid)) {
            $this->apiReturn(401, [], '无权限');
        }
        return true;
    }

    /**
     * 短信抬头申请
     * <AUTHOR>
     * @date 2020/12/14
     *
     */
    public function applySubmit()
    {
        $this->checkUse();
        $memberId    = $this->mid;
        $sid         = $this->sid;
        $signName    = I('post.sign_name', '', 'strval'); //抬头名
        $customer    = I('post.customer', '', 'strval'); //客户名
        $trade       = I('post.trade', '', 'intval'); //行业
        $prove       = I('post.prove', []); //资质证明
        $legalName   = I('post.name', '', 'strval'); //责任人姓名
        $legalNumber = I('post.number', '', 'strval'); //责任人身份证号码
        //额外增加的申请补充材料
        $applicantIdCard = I('post.applicant_id_card', ''); //申请人身份证复印件
        $applicantPortrait = I('post.applicant_portrait', ''); //申请人半身照
        $applicantAuthorization = I('post.applicant_authorization', ''); //申请人授权委托书
        $businessLicense = I('post.business_license', ''); //营业执照
        $signedPowerOfAttorney = I('post.signed_power_of_attorney', ''); //签名授权书

        $checkMsg = $this->_checkParam($signName, $customer, $trade, $prove);
        if (!empty($checkMsg)) {
            $this->apiReturn(203, [], $checkMsg);
        }
        $signName = trim($signName);
        $res      = $this->_signBiz->applySubmit($sid, $memberId, $signName, $customer, $trade, $prove, $legalName,
            $legalNumber,$applicantIdCard,$applicantPortrait,$applicantAuthorization,$businessLicense,$signedPowerOfAttorney);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 抬头编辑  就等于再次提交
     * <AUTHOR>
     * @date 2020/12/14
     *
     */
    public function applyEditor()
    {
        $this->checkUse();
        $memberId    = $this->mid;
        $sid         = $this->sid;
        $signId      = I('post.sign_id', '0', 'intval'); //抬头申请id
        $signName    = I('post.sign_name', '', 'strval'); //抬头名
        $customer    = I('post.customer', '', 'strval'); //客户名
        $trade       = I('post.trade', '', 'intval'); //行业
        $prove       = I('post.prove', []); //资质证明
        $legalName   = I('post.name', '', 'strval'); //责任人姓名
        $legalNumber = I('post.number', '', 'strval'); //责任人身份证号码
        //额外增加的申请补充材料
        $applicantIdCard = I('post.applicant_id_card', ''); //申请人身份证复印件
        $applicantPortrait = I('post.applicant_portrait', ''); //申请人半身照
        $applicantAuthorization = I('post.applicant_authorization', ''); //申请人授权委托书
        $businessLicense = I('post.business_license', ''); //营业执照
        $signedPowerOfAttorney = I('post.signed_power_of_attorney', ''); //签名授权书
        if (empty($signId)) {
            $this->apiReturn(203, [], '抬头申请不存在');
        }

        $checkMsg = $this->_checkParam($signName, $customer, $trade, $prove);
        if (!empty($checkMsg)) {
            $this->apiReturn(203, [], $checkMsg);
        }

        $signName = trim($signName);
        $res      = $this->_signBiz->applyEditor($sid, $memberId, $signId, $signName, $customer, $trade, $prove, $legalName, $legalNumber,
            $applicantIdCard,$applicantPortrait,$applicantAuthorization,$businessLicense,$signedPowerOfAttorney);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取当前有效的抬头
     * <AUTHOR>
     * @date 2020/12/14
     *
     */
    public function getMySign()
    {
        $sid = $this->sid;

        $res = $this->_signBiz->getSignBySid($sid);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 抬头申请列表
     * <AUTHOR>
     * @date 2020/12/14
     *
     */
    public function applyRecord()
    {
        $this->checkUse();
        $sid  = $this->sid;
        $page = I('get.page', '1', 'intval'); //页码
        $size = I('get.sige', '10', 'intval'); //页数

        $res = $this->_signBiz->applyRecord($sid, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取行业类型
     * <AUTHOR>
     * @date 2020/12/15
     *
     */
    public function getTrade()
    {
        $res = $this->_signBiz->getTrade();
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 参数校验
     * <AUTHOR>
     * @date 2020/12/14
     *
     * @param  string  $signName  抬头名
     * @param  string  $customer  企业名称
     * @param  string  $trade  行业信息
     * @param  array  $prove  资质证明
     *
     * @return string
     */
    private function _checkParam($signName, $customer, $trade, $prove)
    {
        if (empty($signName)) {
            return '抬头内容不为空';
        }
        if (!empty($signName) && mb_strlen($signName) > 20) {
            return '抬头字数要<=20个字';
        }
        if (empty($customer)) {
            return '企业名称不为空';
        }
        if (empty($trade)) {
            return '行业信息不为空';
        }
//        if (empty($prove)) {
//            return '证明材料不为空';
//        }
        if (count($prove) > 10) {
            return '证明材料不能超过10张';
        }

        return '';
    }
}