<?php
/**
 * 短信自定义管理(应用)
 * @date 2020-12-11
 * <AUTHOR>
 */

namespace Controller\SmsDiy;

use Business\MemberLogin\MemberLoginHelper;
use Library\Controller;
use Business\SmsDiy\Manage as ManageBiz;

class Manage extends Controller
{
    private static $_tempState = [1, 2, 3]; //模板状态：1应用中、2未应用、3禁用

    //用户信息定义在这里
    private $mid; //当前账号id
    private $sid; //上级id
    private $_manageBiz;
    private $_loginInfo;

    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo('ajax');
        $this->mid        = $this->_loginInfo['memberID'];
        $this->sid        = $this->_loginInfo['sid'];
        $this->_manageBiz = new ManageBiz();
    }

    /**
     * 检测是否在使用
     * <AUTHOR>
     * @date 2021/1/5
     *
     * @return bool
     */
    private function checkUse()
    {
        $manageBiz = $this->_manageBiz;
        if (!$manageBiz->checkUseModule($this->sid)) {
            $this->apiReturn(401, [], '无权限');
        }
        return true;
    }

    /**
     * 申请短信模板
     * <AUTHOR>
     * @date 2020/12/16
     *
     */
    public function applySubmit()
    {
        $this->checkUse();
        $memberId = $this->mid;
        $sid      = $this->sid;
        $ptype    = I('post.p_type', '', 'strval'); //景区类型
        $content  = I('post.content', ''); //短信模板内容
        $title    = I('post.title', '', 'strval'); //模板名称
        $checkMsg = $this->_checkParam($ptype, $content, $title);
        if (!empty($checkMsg)) {
            $this->apiReturn(203, [], $checkMsg);
        }
        $res = $this->_manageBiz->applySubmit($sid, $memberId, $ptype, $content, $title);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 未通过的短信模板编辑
     * <AUTHOR>
     * @date 2020/12/16
     *
     */
    public function applyEditor()
    {
        $this->checkUse();
        $memberId = $this->mid;
        $sid      = $this->sid;
        $smsId    = I('post.sms_id', '', 'strval'); //模板id
        $ptype    = I('post.p_type', '', 'strval'); //景区类型
        $content  = I('post.content', ''); //短信模板内容
        $title    = I('post.title', ''); //模板名称
        if (!$smsId) {
            $this->apiReturn(203, [], '模板信息不存在');
        }
        $checkMsg = $this->_checkParam($ptype, $content, $title);
        if (!empty($checkMsg)) {
            $this->apiReturn(203, [], $checkMsg);
        }
        $res = $this->_manageBiz->applyEditor($sid, $memberId, $smsId, $ptype, $content, $title);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 模板删除
     * <AUTHOR>
     * @date 2020/12/16
     *
     */
    public function applyDelete()
    {
        $this->checkUse();
        $memberId = $this->mid;
        $sid      = $this->sid;
        $smsId    = I('post.sms_id', '', 'strval'); //模板id
        if (!$smsId) {
            $this->apiReturn(203, [], '模板信息不存在');
        }
        $res = $this->_manageBiz->applyDelete($sid, $memberId, $smsId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 模板切换
     * <AUTHOR>
     * @date 2020/12/16
     *
     */
    public function tempSwitch()
    {
        $this->checkUse();
        $memberId = $this->mid;
        $sid      = $this->sid;
        $smsId    = I('post.sms_id', '', 'intval'); //模板id
        $pid      = I('post.pid', '', 'intval'); //产品id
        if (!$smsId) {
            $this->apiReturn(203, [], '模板信息不存在');
        }
        if (!$pid) {
            $this->apiReturn(203, [], '产品信息不存在');
        }
        $res = $this->_manageBiz->tempSwitch($sid, $memberId, $smsId, $pid);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 应用票类
     * <AUTHOR>
     * @date 2020/12/17
     *
     */
    public function displace()
    {
        $this->checkUse();
        $memberId = $this->mid;
        $sid      = $this->sid;
        $smsId    = I('post.sms_id', '', 'intval'); //模板id
        $pids     = I('post.pid', ''); //产品id组合
        if (!$smsId) {
            $this->apiReturn(203, [], '模板信息不存在');
        }
        if (!$pids) {
            $this->apiReturn(203, [], '产品信息组合不存在');
        }
        $res = $this->_manageBiz->tempDisplace($sid, $memberId, $smsId, $pids);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 可用模板列表
     * <AUTHOR>
     * @date 2020/12/17
     *
     */
    public function usableTemp()
    {
        $this->checkUse();
        $sid       = $this->sid;
        $ptype     = I('get.p_type', '', 'strval'); //产品类型
        $tempState = I('get.temp_state', '0', 'intval'); //模板状态：1应用中、2未应用、3禁用
        $page      = I('get.page', '1', 'intval'); //页码
        $size      = I('get.sige', '10', 'intval'); //页数

        if ($tempState && !in_array($tempState, self::$_tempState)) {
            $this->apiReturn(203, [], '搜索状态不存在');
        }

        $res = $this->_manageBiz->usableTempList($sid, $ptype, $tempState, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 应用自定义模板的产品列表
     * <AUTHOR>
     * @date 2020/12/18
     *
     */
    public function useTempProduct()
    {
        $this->checkUse();
        $sid   = $this->sid;
        $smsId = I('get.sms_id', '0', 'intval'); //模板pid
        $pid   = I('get.pid', '0', 'intval'); //产品pid
        $page  = I('get.page', '1', 'intval'); //页码
        $size  = I('get.sige', '10', 'intval'); //页数
        if (!$smsId) {
            $this->apiReturn(203, [], '模板信息不存在');
        }

        $res = $this->_manageBiz->useTempProduct($sid, $smsId, $pid, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 短信发送列表
     * <AUTHOR>
     * @date 2020/12/18
     *
     */
    public function sendRecord()
    {
        $this->checkUse();
        $lid       = I('get.lid', '0', 'intval'); //景区id
        $tid       = I('get.tid', '0', 'intval'); //票id
        $startTime = I('get.order_time_start', '', 'strval'); //开始时间
        $endTime   = I('get.order_time_end', '', 'strval'); //结束时间
        $ordernum  = I('get.ordernum', '', 'strval'); //订单号
        $mobile    = I('get.mobile', '', 'strval'); //手机号
        $page      = I('get.page', '1', 'intval'); //页码
        $size      = I('get.sige', '10', 'intval'); //页数、

        if (!$lid || !$tid) {
            $this->apiReturn(203, [], '景区信息不能为空');
        }

        if (!$startTime || !$endTime) {
            $startTime = date("Y-m-d") . ' 00:00:00';
            $endTime   = date("Y-m-d") . ' 23:59:59';
        } else {
            $startTime = $startTime . ' 00:00:00';
            $endTime   = $endTime. ' 23:59:59';
        }

        //获取
        $sid        = $this->_loginInfo['sid'];
        $memberType = $this->_loginInfo['dtype'];
        $account    = $this->_loginInfo['account'];

        $res = $this->_manageBiz->sendRecord($sid, $memberType, $account, $this->_loginInfo, false, $lid, $tid,
            $startTime, $endTime, $ordernum, $mobile, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 审核通过模板详情
     * <AUTHOR>
     * @date 2020/12/17
     *
     */
    public function tempInfo()
    {
        $this->checkUse();
        $memberId = $this->mid;
        $sid      = $this->sid;
        $smsId    = I('get.sms_id', '0', 'intval'); //可用短信模板id
        if (!$smsId) {
            $this->apiReturn(203, [], '模板信息不存在');
        }

        $res = $this->_manageBiz->getTempInfo($sid, $memberId, $smsId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 可用模板和编号查询 （返回模板名称和id）
     * <AUTHOR>
     * @date 2020/12/18
     *
     */
    public function allUseTemp()
    {
        $sid   = $this->sid;
        $ptype = I('get.p_type', '', 'strval'); //产品类型
        if (!$ptype) {
            $this->apiReturn(203, [], '产品类型错误');
        }

        $res = $this->_manageBiz->allUseTemp($sid, $ptype);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取可以添加的自供应产品
     * <AUTHOR>
     * @date 2020/12/18
     *
     */
    public function getTypeProduct()
    {
        $sid      = $this->sid;
        $smsId    = I('get.sms_id', '0', 'intval'); //模板id
        $ptype    = I('get.p_type', '', 'strval'); //产品类型
        $landName = I('get.name', '', 'strval'); //景区名称
        $page     = I('get.page', '1', 'intval'); //页码
        $size     = I('get.sige', '10', 'intval'); //页数
        if (!$smsId) {
            $this->apiReturn(203, [], '模板信息不存在');
        }

        $res = $this->_manageBiz->getTypeProduct($sid, $smsId, $ptype, $landName, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取自供应景区下的票
     * <AUTHOR>
     * @date 2020/12/18
     *
     */
    public function getTypeProductTicket()
    {
        $sid   = $this->sid;
        $lid   = I('get.lid', '0', 'intval'); //景区id
        $smsId = I('get.sms_id', '0', 'intval'); //模板id
        $page  = I('get.page', '1', 'intval'); //页码
        $size  = I('get.sige', '10', 'intval'); //页数

        if (!$lid) {
            $this->apiReturn(203, [], '产品信息错误');
        }

        $res = $this->_manageBiz->getTypeProductTicket($sid, $lid, $smsId, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 搜索自供应产品
     * <AUTHOR>
     * @date 2020/12/18
     *
     */
    public function getSelfProduct()
    {
        $sid      = $this->sid;
        $landName = I('get.name', '', 'strval'); //景区名称
        $ptype    = I('get.p_type', '', 'strval'); //产品类型
        $page     = 1; //页码
        $size     = 20; //页数

        $res = $this->_manageBiz->getSelfProduct($sid, $landName, $ptype, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 搜索自供应景区下的票
     * <AUTHOR>
     * @date 2020/12/18
     *
     */
    public function getSelfProductTicket()
    {
        $sid        = $this->sid;
        $lid        = I('get.lid', '0', 'intval'); //景区id
        $ticketName = I('get.name', '', 'strval'); //景区名称
        $page       = 1; //页码
        $size       = 20; //页数

        if (!$lid) {
            $this->apiReturn(203, [], '产品信息错误');
        }

        $res = $this->_manageBiz->getSelfProductTicket($sid, $lid, $ticketName, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 产品门票应用模板列表
     * <AUTHOR>
     * @date 2020/12/22
     *
     */
    public function ticketTemp()
    {
        $this->checkUse();
        $sid   = $this->sid;
        $ptype = I('get.p_type', '', 'strval'); //产品类型
        $lid   = I('get.lid', '0', 'intval'); //景区id
        $tid   = I('get.tid', '0', 'intval'); //票id
        $page  = I('get.page', '1', 'intval'); //页码
        $size  = I('get.sige', '10', 'intval'); //页数

        $res = $this->_manageBiz->getProductTicketTemp($sid, $ptype, $lid, $tid, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 模板申请记录
     * <AUTHOR>
     * @date 2020/12/22
     *
     */
    public function applyRecordTemp()
    {
        $this->checkUse();
        $sid   = $this->sid;
        $state = I('get.state', '0', 'intval'); //1审核通过 2审核未通过 3待审核
        $page  = I('get.page', '1', 'intval'); //页码
        $size  = I('get.sige', '10', 'intval'); //页数

        if (!$state) {
            $this->apiReturn(203, [], '审核状态错误');
        }

        $res = $this->_manageBiz->getTempApplyRecord($sid, $state, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取产品类型对应的模板配置信息
     * <AUTHOR>
     * @date 2020/12/23
     *
     */
    public function getTempConfig()
    {
        $res = $this->_manageBiz->getTempConfig($this->sid);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取票对应是否自定义模板信息
     * <AUTHOR>
     * @date 2020/12/30
     *
     */
    public function getTickeSmsDiyInfo()
    {
        $sid = $this->sid;
        $tid = I('get.tid', '0', 'intval'); //票id

        if (!$tid) {
            $this->apiReturn(200, ['sms_use_diy_temp' => 0, 'pid' => 0, 'app_expire' => 0, 'module_id' => ''], '票信息错误');
        }

        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $res = $this->_manageBiz->getSmsDiyTempByTid($sid, $tid);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 短信模板申请参数验证
     * <AUTHOR>
     * @date 2020/12/16
     *
     * @param $ptype
     * @param $content
     * @param $title
     *
     * @return string
     */
    private function _checkParam($ptype, $content, $title)
    {
        if (empty($ptype)) {
            return '模板类型不能为空';
        }
        if (empty($content)) {
            return '模板内容不能为空';
        }
        if (empty($title)) {
            return '模板名称不能为空';
        }
        if (in_array($title, ['通用模板', '通用模板-无链接'])) {
            return '模板名称，不能设置为系统模板名称';
        }

        return '';
    }
}