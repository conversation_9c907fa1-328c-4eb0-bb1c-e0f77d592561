<?php
/**
 * Created by Notepad++.
 * User: cyb
 * Date: 16/7/22
 * Time: 14:29
 */

namespace Controller\qunaer;

use Business\JavaApi\Product\ProductDistributionService;
use Business\JavaApi\ProductApi;
use Business\JavaApi\Ota\OtaResource as OtaResourceApi;
use Library\Controller;
use Library\JsonRpc\PftRpcClient;
use Model\Member\MemberRelationship;
use Model\qunaer\qunaer;
use Model\Ota\MtComTimeShare;
use Business\JavaApi\CommodityCenter\Ticket;
use Throwable;

class getQunaer extends Controller
{
    private $loginInfo = [];

    public function __construct()
    {
        $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 获取去哪儿美团ota上架的产品
     * <AUTHOR>
     * @date   2018-10-19
     *
     * <AUTHOR>
     * @modify  2020-07-03
     *
     * 请求参数说明
     * int  $memberSID  登录的id
     * int  $page  当前页
     * int  $size  一页个数
     * string  $search  搜索条件
     * int  $type  搜索类型0-产品名 1-产品id
     * string  $DockingMode  0=去哪儿,1=美团,2=百度直达，81=有赞，90=马蜂窝',
     *
     */
    public function getAllInfo()
    {
        $memberSID   = $this->loginInfo['sid']; //用户id
        $DockingMode = I('post.DockingMode', -1, 'intval');        //0=去哪儿,1=美团,2=百度直达,81=有赞，90=马蜂窝',156 美团统一预定
        $otaType     = I('post.ota_type', 1, 'intval');
        $otaTypeMap  = [1, 2, 3, 4]; // 1: 美团统一预定 2: 美团直连 3:美团玩乐 4:美团到综
        if ($DockingMode < 0) {
            $this->apiReturn(204, '参数错误');
        }
        if (!in_array($otaType, $otaTypeMap)) {
            $this->apiReturn(203, [], '参数类型有误');
        }
        $page        = I('post.page', 1, 'intval');
        $size        = I('post.size', 15, 'intval');
        $search      = I('post.title', '', 'strval');
        $type        = I('post.type', 0, 'intval');
        $sid         = I('post.sid', 0, 'intval');
        $dockingForm = I('post.docking_form', 0, 'intval');
        $extInfoQuery = ['dockingForm' => $dockingForm];
        $qunaerModel = new Qunaer();
        if (!in_array($type, [0, 1, 2])) {
            $this->apiReturn(204, '参数错误');
        }

        $bindingData = $qunaerModel->getOtaBindingTicket($memberSID, $DockingMode, '', $extInfoQuery);

        $lid = '';
        $tid = '';

        if ($type == 1 && $search != '') {
            $result = $this->formatData($DockingMode, $search, $page);
            $lid    = $result[0];
            $tid    = $result[1];
            $search = $result[2];
        }
        // 获取所有产品
        $arrTids = array_column($bindingData, 'tid');
        $arrLids = array_column($bindingData, 'lid');
        // 获取搜索的tids
        if (!empty($tid)) {
            $arrTids = [$tid];
        }

        if (!empty($lid)) {
            $arrLids = [$lid];
        }

        // 有赞授权打标需要过滤id
        // $tagTicketList = [];
        // $isYouZanCloud = false;
        // if (ENV == 'TEST' && $DockingMode == 372) {
        //     $isYouZanCloud = true;
        // }

        // if (ENV == 'PRODUCTION' && $DockingMode == 376) {
        //     $isYouZanCloud = true;
        // }
        
        // if ($isYouZanCloud && !empty($arrLids)) {
        //     $youZanCloudBusiness = new \Business\Ota\YouZanCloudBusiness();
        //     $result              = $youZanCloudBusiness->getTagTicketList($memberSID, 1, 100, '', 0, '', '', $arrLids);
        //     if ($result['data']['total'] != 0) {
        //         foreach ($result['data']['list'] as $tagItem) {
        //             $tagTicketList[$tagItem['product_id']][] = $tagItem;
        //         }
        //     }
        // }

        $evoluteGroupSpecialityService = new \Business\JavaApi\Product\EvoluteGroupSpecialityService();
        $pTypes = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'];
        // 美团直连、抖音团购需要查询出旅游券信息
        if ($this->isDockingToTravelVoucher($DockingMode)) {
            $pTypes[] = 'Q';
        }
        // 美团直连v2只限定支持门票、演出、套票、年卡产品
        if ($DockingMode == 1 && $dockingForm == 1) {
            $pTypes = ['A', 'H', 'F', 'I'];
        }
        $data = $evoluteGroupSpecialityService->otaTicketAllList($memberSID, $page, $size, $arrTids, $search, $lid, $tid, $sid, $pTypes);

        if ($data['code'] != 200) {
            $this->apiReturn(200, ['list' => [], 'page' => $page, 'count' => 0, 'page_count' => 0]);
        }

        $landMap         = [];
        $isOpenTimeShare = false;
        if ($DockingMode == 156 || $DockingMode == 1) {
            //判断登录用户是否有开通分时预约         1
            // $moduleModel  = new \Model\AppCenter\ModuleList();
            // $moduleConfig = $moduleModel->getPackageLogInfoByUserIdArr($memberSID, 61);
            // if ($moduleConfig && !in_array($moduleConfig['status'], [1, 3, 7])) {
                $isOpenTimeShare = true;
                $lidArr          = array_unique(array_column($data['data']['list'], 'lid'));
                $lidArr          = array_values($lidArr);
                $javaApi         = new \Business\Product\TimeShare();
                $landMap         = $javaApi->getConfByLidArr($lidArr);
            // }
        }

        $tidArr = [];
        $pageCount = ceil($data['data']['total'] / $size);
        foreach ($data['data']['list'] as $key => $value) {
            $data['data']['list'][$key]['bind']        = 0;
            $data['data']['list'][$key]['aid']         = $memberSID;
            $data['data']['list'][$key]['timeShare']   = 0;
            $data['data']['list'][$key]['refund_type'] = 1;
            $tidArr[]                                             = $value['ticketId'];
            $data['data']['list'][$key]['bind_time'] = '--';
            $data['data']['list'][$key]['third_id']  = '';
            //$data['data']['list'][$key]['youzan_tag_list']  = [];
            foreach ($bindingData as $bValue) {
                if ($bValue['tid'] == $value['ticketId']) {
                    $data['data']['list'][$key]['bind']    = 1;
                    if (empty($data['data']['list'][$key]['bind_id'])) {
                        $data['data']['list'][$key]['bind_id'] = $bValue['id'];
                    } else {
                        $data['data']['list'][$key]['bind_id'] .= ',' . $bValue['id'];
                    }
                    $data['data']['list'][$key]['bind_time'] = $bValue['itime'];
                    $data['data']['list'][$key]['third_id']  = $bValue['third_part_teamwork_id'];//下游虚拟编码
                    $data['data']['list'][$key]['third_id_original']  = $bValue['third_part_teamwork_id_original'];//下游原始编码
                    if ($DockingMode == 0) {
                        $data['data']['list'][$key]['third_id']  = $bValue['tid_aid'];//去哪儿特殊编码
                    }

                    //扩展字段解析处理
                    //docking_form 0：门票类型【对接旧接口】 1：演出类型【V2接口】 默认值为0
                    $data['data']['list'][$key]['docking_form'] = 0;
                    if (isset($bValue['ext_info'])) {
                        $extInfo = json_decode($bValue['ext_info'], true);
                        if (isset($extInfo['docking_form'])) {
                            $data['data']['list'][$key]['docking_form'] = intval($extInfo['docking_form']);
                        }
                    }
                    // break;
                }
            }

            $supplySid = $data['data']['list'][$key]['sid'] ?? 0;

            //验证是否禁用OTA渠道
            $otaInfo = $this->checkChannel($DockingMode, $memberSID, $value['pid'], $value['lid'], $supplySid);

            $data['data']['list'][$key]['disableOTA'] = $otaInfo == true ? 0 : 1;

            if ($isOpenTimeShare && isset($landMap[$value['lid']])) {

                $data['data']['list'][$key]['timeShare'] = $landMap[$value['lid']];
            }
        }

        //$bindingData = $qunaerModel->getOtaBindingTicket($memberSID, $DockingMode);
        if (!empty($bindingData)) {
            $thirdPartCodeArr = array_column($bindingData, 'third_part_teamwork_id');
            $bindInfo         = $qunaerModel->getBindSectionAndTimeShareTicketByPartCode($thirdPartCodeArr);
            if (!empty($bindInfo)) {
                $bindedArr = array_column($bindInfo, 'third_part_teamwork_id');
            }

            if (!empty($bindedArr)) {
                foreach ($bindingData as $key => $item) {
                    if (in_array($item['third_part_teamwork_id'], $bindedArr)) {
                        foreach ($data['data']['list'] as $listkey => $listitem) {
                            if ($listitem['ticketId'] == $item['tid']) {
                                $data['data']['list'][$listkey]['bind_pack'] = 1;
                            }
                        }
                    }
                }
            }
        }

        if (!empty($tidArr)) {
            //前端调用列表的，有查询的接口不用。
            $memberId        = $this->loginInfo['sid'];
            $ticketCodeModel = new MtComTimeShare();
            $mtVersion = ($dockingForm == 1) ? 2 : 1;
            $ticketconfArr   = $ticketCodeModel->getMtTicketCodeConfigBytid($tidArr, $otaType, $memberId, $mtVersion);
            if (!empty($ticketconfArr)) {
                foreach ($data['data']['list'] as $key => $value) {
                    foreach ($ticketconfArr as $ticketconf) {
                        if ($value['ticketId'] == $ticketconf['tid']) {
                            $data['data']['list'][$key]['notice_type'] = $ticketconf['notice_type'];
                            $data['data']['list'][$key]['ota_type'] = $ticketconf['ota_type'];
                        }
                    }
                }
            }

            $mtRefundConfigArr = $ticketCodeModel->getMtRefundConfigByTidArrMemberIdOtaType($tidArr, $memberSID, $otaType);
            if (!empty($mtRefundConfigArr)) {
                foreach ($data['data']['list'] as $key => $value) {
                    foreach ($mtRefundConfigArr as $refundconf) {
                        if ($value['ticketId'] == $refundconf['tid']) {
                            $data['data']['list'][$key]['refund_type'] = $refundconf['refund_type'];
                        }
                    }
                }
            }
        }

        // 判断票是否绑定上游
        $ticket         = new Ticket();
        $ticketAttrList = $ticket->queryTicketInfoByIds($tidArr);
        if ($ticketAttrList['code'] == 200) {
            foreach ($data['data']['list'] as $key => $value) {
                foreach ($ticketAttrList['data'] as $ticketAttr) {
                    if ($value['ticketId'] == $ticketAttr['uuJqTicketDTO']['id']) {
                        if ($ticketAttr['thridTicketAttributesDTO']['uuid'] != '') {
                            $data['data']['list'][$key]['is_bind_third'] = true; // 是否绑定第三方
                            $data['data']['list'][$key]['uuid']          = $ticketAttr['thridTicketAttributesDTO']['uuid']; // 第三方产品编号
                        }

                        $data['data']['list'][$key]['pre_sale'] = $ticketAttr['uuJqTicketDTO']['pre_sale']; // 是否是期票
                    }
                }
            }
        }

        // 判断门票是否绑定外部码
        $codeManageBiz    = new \Business\ExternalCode\CodeManage();
        $externalCodeList = $codeManageBiz->checkIsExternalCodeByTid($memberSID, $tidArr);
        if ($externalCodeList['code'] == 200) {
            foreach ($data['data']['list'] as $key => $value) {
                foreach ($externalCodeList['data'] as $ticketIdKey => $codeNum) {
                    if ($value['ticketId'] == $ticketIdKey) {
                        if (!empty($codeNum)) {
                            $data['data']['list'][$key]['is_bind_external_code'] = true; // 是否使用外部码
                        }
                    }
                }
            } 
        }

        // 判断有赞打标标识
        // if (!empty($tagTicketList)) {
        //     foreach ($data['data']['list'] as $key => $value) {
        //         foreach ($tagTicketList as $landIdKey => $tagItem) {
        //             if ($value['lid'] == $landIdKey) {
        //                 if (!empty($tagItem)) {
        //                     $data['data']['list'][$key]['youzan_tag_list'] = $tagItem; // 是否使用外部码
        //                 }
        //             }
        //         }
        //     }  
        // }

        $this->apiReturn(200, [
            'list'       => $data['data']['list'],
            'page'       => $page,
            'count'      => $data['data']['total'],
            'page_count' => $pageCount,
        ]);
    }

    /**
     * 获取去哪儿美团ota绑定的产品
     * <AUTHOR>
     * @date   2018-10-19
     *
     * <AUTHOR>
     * @modify  2020-07-03
     *
     * 请求参数说明
     * int  $memberSID  登录的id
     * int  $page  当前页
     * int  $size  一页个数
     * string  $search  搜索条件
     * string  $DockingMode  0=去哪儿,1=美团,2=百度直达，81=有赞，90=马蜂窝',
     *
     */
    public function getUseInfo()
    {
        $memberSID   = $this->loginInfo['sid'];               //用户id
        $DockingMode = I('post.DockingMode', -1, 'intval');        //0=去哪儿,1=美团,2=百度直达,81=有赞，90=马蜂窝',
        $otaType     = I('post.ota_type', 1, 'intval');
        $otaTypeMap  = [1, 2, 3, 4]; // 1: 美团统一预定 2: 美团直连 3:美团玩乐 4:美团到综
        if ($DockingMode < 0) {
            $this->apiReturn(204, '参数错误');
        }
        if (!in_array($otaType, $otaTypeMap)) {
            $this->apiReturn(203, [], '参数类型有误');
        }
        $page   = I('post.page', 1, 'intval');
        $size   = I('post.size', 15, 'intval');
        $search = I('post.title', '', 'strval');
        $type   = I('post.type', 0, 'intval');
        $sid    = I('post.sid', 0, 'intval');
        $dockingForm = I('post.docking_form', 0, 'intval');
        $extInfoQuery = ['dockingForm' => $dockingForm];

        if (!in_array($type, [0, 1, 2])) {
            $this->apiReturn(204, '参数错误');
        }

        $lid = '';
        $tid = '';

        $qunaerModel = new Qunaer();
        if ($type == 1 && $search != '') {
            $result      = $this->formatData($DockingMode, $search, $page);
            $bindingData = $qunaerModel->getOtaBindingTicket($memberSID, $DockingMode, $result[1], $extInfoQuery);
            $lid         = $result[0];
            $tid         = $result[1];
            $search      = $result[2];
        } else {
            $bindingData = $qunaerModel->getOtaBindingTicket($memberSID, $DockingMode, '', $extInfoQuery);
        }

        if (empty($bindingData)) {
            $this->apiReturn(200, ['list' => [], 'page' => $page, 'count' => 0, 'page_count' => 0]);
        }
        $arrTids = array_column($bindingData, 'tid');
        $arrLids = array_column($bindingData, 'lid');
        // $data    = ProductApi::getOtaBindingTicket($memberSID, $page, $size, $search, $arrTids, $result[0], $result[1]);
        if (!empty($tid)) {
            $arrTids = $tid;
        }

        if (!empty($lid)) {
            $arrLids = [$lid];
        }

        // 有赞授权打标需要过滤id
        //$tagTicketList = [];
        //$isYouZanCloud = false;
        //if (ENV == 'TEST' && $DockingMode == 372) {
        //    $isYouZanCloud = true;
        //}
        //
        //if (ENV == 'PRODUCTION' && $DockingMode == 376) {
        //    $isYouZanCloud = true;
        //}
        // if ($isYouZanCloud && !empty($arrLids)) {
        //     $youZanCloudBusiness = new \Business\Ota\YouZanCloudBusiness();
        //     $result              = $youZanCloudBusiness->getTagTicketList($memberSID, 1, 100, '', 0, '', '', $arrLids);
        //     if ($result['data']['total'] != 0) {
        //         foreach ($result['data']['list'] as $tagItem) {
        //             $tagTicketList[$tagItem['product_id']][] = $tagItem;
        //         }
        //     }
        // }

        $evoluteGroupSpecialityService = new \Business\JavaApi\Product\EvoluteGroupSpecialityService();
        $data = $evoluteGroupSpecialityService->otaTicketBindingPage($memberSID, $page, $size, $arrTids, $lid, $search, $sid);
        // var_dump($data);exit;
        if ($data['code'] != 200) {
            $this->apiReturn(200, ['list' => [], 'page' => $page, 'count' => 0, 'page_count' => 0]);
        }
        $pageCount = ceil($data['data']['total'] / $size);
        $list      = $data['data']['list'];

        $landMap         = [];
        $isOpenTimeShare = false;
        if ($DockingMode == 156 || $DockingMode == 1) {
            //判断登录用户是否有开通分时预约
            // $moduleModel  = new \Model\AppCenter\ModuleList();
            // $moduleConfig = $moduleModel->getPackageLogInfoByUserIdArr($memberSID, 61);
            // if ($moduleConfig && !in_array($moduleConfig['status'], [1, 3, 7])) {
                $isOpenTimeShare = true;
                $lidArr          = array_unique(array_column($data['data']['list'], 'lid'));
                $lidArr          = array_values($lidArr);
                $javaApi         = new \Business\Product\TimeShare();
                $landMap         = $javaApi->getConfByLidArr($lidArr);
            // }
        }

        $tidArr = [];
        foreach ($list as $key => $value) {
            $tidArr[] = $value['ticketId'];
            $list[$key]['bind'] = 0;
            $list[$key]['refund_type'] = 1;
            //$list[$key]['youzan_tag_list'] = [];
            $bindIdArr = [];
            foreach ($bindingData as $bKey => $bValue) {
                if ($bValue['tid'] == $value['ticketId']) {
                    $list[$key]['bind']    = 1;
                    $bindIdArr[] = $bValue['id'];
                    $list[$key]['third_id']  = $bValue['third_part_teamwork_id'];//下游虚拟编码
                    $list[$key]['third_id_original']  = $bValue['third_part_teamwork_id_original'];//下游原始编码
                    if ($DockingMode == 0) {
                        $list[$key]['third_id']  = $bValue['tid_aid'];//去哪儿特殊编码
                    }
                    $list[$key]['bind_time'] = $bValue['itime'];//绑定时间
                    //扩展字段解析处理
                    //docking_form 0：门票类型【对接旧接口】 1：演出类型【V2接口】 默认值为0
                    $list[$key]['docking_form'] = 0;
                    if (isset($bValue['ext_info'])) {
                        $extInfo = json_decode($bValue['ext_info'], true);
                        if (isset($extInfo['docking_form'])) {
                            $list[$key]['docking_form'] = intval($extInfo['docking_form']);
                        }
                    }
                }
            }
            $list[$key]['bind_id'] = implode(',',$bindIdArr);

            // $supplySid = $list[$key]['sid'] ?? 0;
            //验证是否禁用OTA渠道
            // $otaInfo                  = $this->checkChannel($DockingMode, $memberSID, $value['pid'], $value['lid'], $supplySid);
            // $list[$key]['disableOTA'] = $otaInfo == true ? 0 : 1;
            $list[$key]['disableOTA'] = 0;

            $list[$key]['aid'] = $memberSID;

            if ($isOpenTimeShare && isset($landMap[$value['lid']])) {
                $list[$key]['timeShare'] = $landMap[$value['lid']];
            }
        }

        $thirdPartCodeArr = [];
        foreach ($list as $bindedItem){
            if ($bindedItem['ptype'] == 'F' && $bindedItem['bind'] == 1){
                $thirdPartCodeArr[] = $bindedItem['third_id'];
            }
        }

        if (!empty($thirdPartCodeArr)) {
            $bindInfo = $qunaerModel->getBindSectionAndTimeShareTicketByPartCode($thirdPartCodeArr);
            if (!empty($bindInfo)) {
                $thirdPartArr = array_column($bindInfo, 'third_part_teamwork_id');
                foreach ($list as $key => &$listItem) {
                    if (in_array($listItem['third_id'],$thirdPartArr)) {
                        $listItem['bind_pack'] = 1;
                    }
                }
            }
        }

        if (!empty($tidArr)) {

            $memberId = $this->loginInfo['sid'];
            //界面变更，没实际作用了
            $ticketCodeModel = new MtComTimeShare();
            $mtVersion = $dockingForm == 1 ? 2 : 1;
            $ticketconfArr   = $ticketCodeModel->getMtTicketCodeConfigBytid($tidArr, $otaType, $memberId, $mtVersion);
            if (!empty($ticketconfArr)) {
                foreach ($ticketconfArr as $confItem) {
                    foreach ($list as &$ticketItem) {
                        if ($confItem['tid'] == $ticketItem['ticketId']) {
                            $ticketItem['notice_type'] = $confItem['notice_type'];
                            $ticketItem['ota_type'] = $confItem['ota_type'];
                        }
                    }
                }
            }

            $mtRefundConfigArr = $ticketCodeModel->getMtRefundConfigByTidArrMemberIdOtaType($tidArr, $memberSID, $otaType);
            if (!empty($mtRefundConfigArr)) {
                foreach ($mtRefundConfigArr as $refundItem) {
                    foreach ($list as &$ticketItem) {
                        if ($refundItem['tid'] == $ticketItem['ticketId']) {
                            $ticketItem['refund_type'] = $refundItem['refund_type'];
                        }
                    }
                }
            }

            if (!empty($mtRefundConfigArr)) {
                foreach ($data['data']['list'] as $key => $value) {
                    foreach ($mtRefundConfigArr as $refundconf) {
                        if ($value['ticketId'] == $refundconf['tid']) {
                            $data['data']['list'][$key]['refund_type'] = $refundconf['refund_type'];
                        }
                    }
                }
            }
        }

        // 判断票是否绑定上游
        $ticket         = new Ticket();
        $ticketAttrList = $ticket->queryTicketInfoByIds($tidArr);
        if ($ticketAttrList['code'] == 200) {
            foreach ($list as &$ticketItem) {
                foreach ($ticketAttrList['data'] as $ticketAttr) {
                    if ($ticketItem['ticketId'] == $ticketAttr['uuJqTicketDTO']['id']) {
                        if ($ticketAttr['thridTicketAttributesDTO']['uuid'] != '') {
                            $ticketItem['is_bind_third'] = true; // 是否绑定第三方
                            $ticketItem['uuid']          = $ticketAttr['thridTicketAttributesDTO']['uuid']; // 第三方产品编号
                        }

                        $ticketItem['pre_sale'] = $ticketAttr['uuJqTicketDTO']['pre_sale']; // 是否是期票
                    }
                }
            }
        }

        // 判断门票是否绑定外部码
        $codeManageBiz    = new \Business\ExternalCode\CodeManage();
        $externalCodeList = $codeManageBiz->checkIsExternalCodeByTid($memberSID, $tidArr);
        if ($externalCodeList['code'] == 200) {
            foreach ($list as &$ticketItem) {
                foreach ($externalCodeList['data'] as $ticketIdKey => $codeNum) {
                    if ($ticketItem['ticketId'] == $ticketIdKey) {
                        if (!empty($codeNum)) {
                            $ticketItem['is_bind_external_code'] = true; // 是否使用外部码
                        }
                    }
                }
            }
        }

        // 判断是否有有赞打标
        //if (!empty($tagTicketList)) {
        //    foreach ($list as &$ticketItem) {
        //        foreach ($tagTicketList as $landIdKey => $tagList) {
        //            if ($ticketItem['lid'] == $landIdKey) {
        //                if (!empty($tagList)) {
        //                    $ticketItem['youzan_tag_list'] = $tagList;
        //                }
        //            }
        //        }
        //    }
        //}

        $this->apiReturn(200,
            ['list' => $list, 'page' => $page, 'count' => $data['data']['total'], 'page_count' => $pageCount]);
    }

    /**
     * 获取去哪儿美团ota未绑定的产品
     * <AUTHOR>
     * @date   2018-10-19
     *
     * <AUTHOR>
     * @modify  2020-07-03
     *
     * 请求参数说明
     * int  $memberSID  登录的id
     * int  $page  当前页
     * int  $size  一页个数
     * string  $search  搜索条件
     * string  $DockingMode  0=去哪儿,1=美团,2=百度直达，81=有赞，90=马蜂窝',
     *
     */
    public function getUnuseInfo()
    {
        $memberSID   = $this->loginInfo['sid'];               //用户id
        $DockingMode = I('post.DockingMode', -1, 'intval');        //0=去哪儿,1=美团,2=百度直达,81=有赞，90=马蜂窝',
        $otaType     = I('post.ota_type', 1, 'intval');
        $otaTypeMap  = [1, 2, 3, 4]; // 1: 美团统一预定 2: 美团直连 3:美团玩乐 4:美团到综
        if ($DockingMode < 0) {
            $this->apiReturn(204, '参数错误');
        }
        if (!in_array($otaType, $otaTypeMap)) {
            $this->apiReturn(203, [], '参数类型有误');
        }
        $page   = I('post.page', 1, 'intval');
        $size   = I('post.size', 15, 'intval');
        $search = I('post.title', '', 'strval');
        $type   = I('post.type', 0, 'intval');
        $sid    = I('post.sid', 0, 'intval');
        $dockingForm = I('post.docking_form', 0, 'intval');
        $extInfoQuery = ['dockingForm' => $dockingForm];

        if (!in_array($type, [0, 1, 2])) {
            $this->apiReturn(204, '参数错误');
        }
        $lid = '';
        $tid = '';

        $qunaerModel = new Qunaer();
        if ($type == 1 && $search != '') {
            $result      = $this->formatData($DockingMode, $search, $page);
            $bindingData = $qunaerModel->getOtaBindingTicket($memberSID, $DockingMode, $result[1], $extInfoQuery);
            $lid         = $result[0];
            $tid         = $result[1];
            $search      = $result[2];
        } else {
            $bindingData = $qunaerModel->getOtaBindingTicket($memberSID, $DockingMode, '', $extInfoQuery);
        }

        $arrTids = array_column($bindingData, 'tid');
        $pTypes = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'];
        // 美团直连、抖音团购需要查询出旅游券信息
        if ($this->isDockingToTravelVoucher($DockingMode)) {
            $pTypes[] = 'Q';
        }
        // 美团直连v2只限定支持门票、演出、套票、年卡产品
        if ($DockingMode == 1 && $dockingForm == 1) {
            $pTypes = ['A', 'H', 'F', 'I'];
        }
        $data    = ProductApi::getOtaUnBindingTicket($memberSID, $page, $size, $search, $arrTids, $lid, $tid, $sid, $pTypes);
        if ($data['code'] != 200) {
            $this->apiReturn(204, '获取失败');
        }
        $pageCount = ceil($data['data']['totalCount'] / $size);
        $list      = $data['data']['otaTicketItemList'];

        $landMap         = [];
        $isOpenTimeShare = false;
        if ($DockingMode == 156 || $DockingMode == 1) {
            //判断登录用户是否有开通分时预约
            $moduleModel  = new \Model\AppCenter\ModuleList();
            $moduleConfig = $moduleModel->getPackageLogInfoByUserIdArr($memberSID, 61);
            if ($moduleConfig && !in_array($moduleConfig['status'], [1, 3, 7])) {
                $isOpenTimeShare = true;
                $lidArr          = array_unique(array_column($data['data']['otaTicketItemList'], 'lid'));
                $lidArr          = array_values($lidArr);
                $javaApi         = new \Business\Product\TimeShare();
                $landMap         = $javaApi->getConfByLidArr($lidArr);
            }
        }
        $tidArr = [];
        foreach ($list as $key => $value) {
            $tidArr[] = $value['ticketId'];
            $list[$key]['bind'] = 0;
            $list[$key]['refund_type'] = 1;
            foreach ($bindingData as $bKey => $bValue) {
                if ($bValue == $value['ticketId']) {
                    $list[$key]['bind']    = 1;
                    $list[$key]['bind_id'] = $bValue['id'];
                    break;
                }
            }

            $supplySid = $list[$key]['sid'] ?? 0;

            //验证是否禁用OTA渠道
            $otaInfo                  = $this->checkChannel($DockingMode, $memberSID, $value['pid'], $value['lid'], $supplySid);
            $list[$key]['disableOTA'] = $otaInfo == true ? 0 : 1;

            $list[$key]['aid'] = $memberSID;

            if ($isOpenTimeShare && isset($landMap[$value['lid']])) {
                $list[$key]['timeShare'] = $landMap[$value['lid']];
            }
        }

        if (!empty($tidArr)) {
            $memberId        = $this->loginInfo['sid'];
            $ticketCodeModel = new MtComTimeShare();
            $mtVersion = ($dockingForm == 1) ? 2 : 1;
            $ticketconfArr   = $ticketCodeModel->getMtTicketCodeConfigBytid($tidArr, $otaType, $memberId, $mtVersion);
            if (!empty($ticketconfArr)) {
                foreach ($ticketconfArr as $confItem) {
                    foreach ($list as &$ticketItem) {
                        if ($confItem['tid'] == $ticketItem['ticketId']) {
                            $ticketItem['notice_type'] = $confItem['notice_type'];
                            $ticketItem['ota_type'] = $confItem['ota_type'];
                        }
                    }
                }
            }

            $mtRefundConfigArr = $ticketCodeModel->getMtRefundConfigByTidArrMemberIdOtaType($tidArr, $memberSID, $otaType);
            if (!empty($mtRefundConfigArr)) {
                foreach ($mtRefundConfigArr as $refundItem) {
                    foreach ($list as &$ticketItem) {
                        if ($refundItem['tid'] == $ticketItem['ticketId']) {
                            $ticketItem['refund_type'] = $refundItem['refund_type'];
                        }
                    }
                }
            }
        }

        // 判断票是否绑定上游
        $ticket         = new Ticket();
        $ticketAttrList = $ticket->queryTicketInfoByIds($tidArr);
        if ($ticketAttrList['code'] == 200) {
            foreach ($list as &$ticketItem) {
                foreach ($ticketAttrList['data'] as $ticketAttr) {
                    if ($ticketItem['ticketId'] == $ticketAttr['uuJqTicketDTO']['id']) {
                        if ($ticketAttr['thridTicketAttributesDTO']['uuid'] != '') {
                            $ticketItem['is_bind_third'] = true; // 是否绑定第三方
                            $ticketItem['uuid']          = $ticketAttr['thridTicketAttributesDTO']['uuid']; // 第三方产品编号
                        }

                        $ticketItem['pre_sale'] = $ticketAttr['uuJqTicketDTO']['pre_sale']; // 是否是期票
                    }
                }
            }
        }

        // 判断门票是否绑定外部码
        $codeManageBiz    = new \Business\ExternalCode\CodeManage();
        $externalCodeList = $codeManageBiz->checkIsExternalCodeByTid($memberSID, $tidArr);
        if ($externalCodeList['code'] == 200) {
            foreach ($list as &$ticketItem) {
                foreach ($externalCodeList['data'] as $ticketIdKey => $codeNum) {
                    if ($ticketItem['ticketId'] == $ticketIdKey) {
                        if (!empty($codeNum)) {
                            $ticketItem['is_bind_external_code'] = true; // 是否使用外部码
                        }
                    }
                }
            }
        }

        $this->apiReturn(200,
            ['list' => $list, 'page' => $page, 'count' => $data['data']['totalCount'], 'page_count' => $pageCount]);
    }

    private function formatData($DockingMode, $search, $page)
    {
        if ($DockingMode == 0) {
            $arrIds = explode('_', $search);
            if (count($arrIds) != 2) {
                $this->apiReturn(200, ['list' => [], 'page' => $page, 'count' => 0, 'page_count' => 0]);
            }
            $lid      = '';
            $ticketId = is_numeric($arrIds[0]) ? $arrIds[0] : '';
        } else {
            //TODO  后期如果有赞和马蜂窝需要看看是否需要改
            $arrIds = explode('|', $search);
            if (count($arrIds) != 3) {
                $this->apiReturn(200, ['list' => [], 'page' => $page, 'count' => 0, 'page_count' => 0]);
            }
            $ticketId = is_numeric($arrIds[2]) ? $arrIds[2] : '';
            $lid      = is_numeric($arrIds[1]) ? $arrIds[1] : '';
        }

        return [$lid, $ticketId, ''];
    }

    /**
     * 获取去哪儿商家管理列表
     * @date   2018-11-28
     * <AUTHOR>
     */
    public function getQunaerList()
    {
        if (!$this->isSuper()) {
            $this->apiReturn(400, '', '没有权限');
        }

        $currentPage = I('currentPage', 1, 'intval'); //页码
        $pageSize    = I('pageSize', 20, 'intval');   //条数
        $fid         = I('fid', 0, 'intval');     //会员id

        $Qunaer = new \Business\Admin\Qunaer();
        $res    = $Qunaer->getQunaerList($fid, $currentPage, $pageSize);
        $total  = $res['total'];
        $list   = $res['list'];
        if ($list && is_array($list)) {
            foreach ($list as $key => $value) {
                $list[$key]['dname']       = (mb_strlen($value['dname'], 'utf8') > 12) ? mb_substr($value['dname'], 0,
                        12, 'utf8') . '...' : $value['dname'];
                $list[$key]['DockingMode'] = $value['DockingMode'] == 0 ? '去哪儿' : '美团';
            }
        }

        $output = [
            'total' => $total,
            'list'  => $list,
        ];

        $this->apiReturn(200, $output, '');
    }

    /**
     * 检测OTA渠道是否被禁用 统一调用
     *
     * @date   2020-07-02
     * <AUTHOR>
     *
     * @param  int  $DockingMode  0=去哪儿,1=美团,2=百度直达，81=有赞，90=马蜂窝',
     * @param  int  $memberSID  分销商ID
     * @param  int  $pid  产品id
     * @param  int  $lid  景点id
     * @param  int  $sid  供应商id（上一级供应的）
     *
     * @return bool
     */
    private function checkChannel($DockingMode, $memberSID, $pid, $lid, $sid = 0)
    {
        if(!isset($pid)){
            return true;
        }
        //验证是否禁用OTA渠道
        $otaResourceApi = new OtaResourceApi();
        $otaInfo        = $otaResourceApi->checkDisableOTA($DockingMode, $memberSID, $pid, $lid, $sid);

        if (isset($otaInfo['code']) && $otaInfo['code'] == 200 && isset($otaInfo['data']['disableOTA'])) {

            return $otaInfo['data']['disableOTA'];

        } else {

            return true;
        }
    }

    /**
     * 检查是否存在分销关系
     *
     * http://git.12301.io/PFT/app-doc/src/master/开放平台文档/下游服务/OTA配置/OTA绑定.md
     * Author : liucm
     * Date : 2022/1/13
     */
    public function checkDistributionInfo()
    {
        $aid = I('aid', 0, 'intval');
        $lid = I('lid', 0, 'intval');
        $ticketId = I('ticket_id', 0, 'intval'); //
        $coopB    = I('coop_b', -1, 'intval');   //分销商系统标识
        $memberId = $this->loginInfo['sid'];    //用户id

        if (empty($ticketId) || $coopB == -1) {
            $this->apiReturn(500, [], '请求参数错误');
        }
        $downSysListRes = (new \Business\Ota\DownstreamSystemConfig)->getDownListConfigListArrByfield('system_tag');
        $sysListItem    = $downSysListRes[$coopB];

        if (empty($sysListItem)) {
            if (ENV != 'PRODUCTION') {
                $this->apiReturn(200, [], '参数错误，找不到系统,测试环境默认通过');
            }
            $this->apiReturn(500, [], '参数错误，找不到系统');

        }

        $accountId              = $sysListItem['member_id'];
        $productDistributionApi = new ProductDistributionService();
        //获取对应的分销链
        $result = $productDistributionApi->queryDistributionInfoNew($ticketId, $accountId, $memberId);

        if ($result['code'] != 200) {
            if (ENV != 'PRODUCTION') {
                $this->apiReturn(200, [], "未分销给{{$sysListItem['sys_name']}/{$sysListItem['account']}}，请分销成功再操作绑定门票");
            }
            $this->apiReturn(500, [], "未分销给{{$sysListItem['sys_name']}/{$sysListItem['account']}}，请分销成功再操作绑定门票");
        }

        // 美团直连、抖音团购需要查询出旅游券信息
        if ($this->isDockingToTravelVoucher($coopB)) {
            $ticket     = new Ticket();
            $javaApiRes = $ticket->queryTicketInfoByIds([$ticketId]);
            if ($javaApiRes['code'] != 200) {
                $this->apiReturn(500, [], '获取票信息失败');
            }
            $pType = $javaApiRes['data'][0]['uuProductDTO']['p_type'];

            // 请求旅游券获取绑定权限
            if ($pType == 'Q') {
                try {
                    $param = [
                        [
                            'business' => 'travelVoucher',
                            'system'   => $this->getSysNameByDockingMode($coopB),
                            'bizData'  => [
                                'partner_deal_id' => sprintf('%s|%s|%s', $aid, $lid, $ticketId),
                            ],
                        ],
                    ];

                    $lib            = new PftRpcClient('open_platform_api');
                    $res            = $lib->call('insideBusiness/Product/checkTicketIfBind', $param, 'common_service');
                    $result['code'] = $res['code'];
                    $result['msg']  = $res['msg'];
                    $result['data'] = $res['data'];
                } catch (Throwable $e) {
                    $result['code'] = $e->getCode();
                    $result['msg']  = $e->getMessage();
                }

                if ($result['code'] != 200) {
                    $this->apiReturn(500, [], '该旅游券商品未开启 OTA 分销,不支持绑定');
                }
            }
        }

        $this->apiReturn(200, [], '验证成功');
    }

    /**
     * 获取系统标识
     *
     * <AUTHOR>
     * @date 2022-06-09
     *
     * @param int $dockingMode
     *
     * @return string
     */
    protected function getSysNameByDockingMode(int $dockingMode): string
    {
        if (ENV == 'LOCAL' || ENV == 'DEVELOP') {
            $sysMap = [
                1   => 'meiTuanV2',
                229 => 'douYin',
            ];
        } elseif (ENV == 'TEST') {
            $sysMap = [
                1   => 'meiTuanV2',
                246 => 'douYin',
            ];
        } else {
            $sysMap = [
                1   => 'meiTuanV2',
                277 => 'douYin',
                408 => 'douYinLaiKe',
            ];
        }

        return $sysMap[$dockingMode] ?? '';
    }

    /**
     * 是否对接旅游券
     *
     * <AUTHOR>
     * @date 2022-06-09
     *
     * @param int $dockingMode
     *
     * @return bool
     */
    protected function isDockingToTravelVoucher(int $dockingMode): bool
    {
        if (ENV == 'LOCAL' || ENV == 'DEVELOP') {
            $dockingModeList = [1, 229];
        } elseif (ENV == 'TEST') {
            $dockingModeList = [1, 246];
        } else {
            $dockingModeList = [1, 277, 408];
        }
        return in_array($dockingMode, $dockingModeList);
    }

    /**
     * 获取供应商+资源中心供应商列表
     *
     * Author : liucm
     * Date : 2022/1/20
     */
    public function getSuppliersList()
    {
        $keyWord = I('keyword', '', 'strval,trim');
        $sid     = $this->loginInfo['sid'];    //用户id
        if (mb_strlen($keyWord) == strlen($keyWord)) {
            //英文
            $searchType = 2;
        } else {
            //汉字
            $searchType = 1;
        }
        //$res = $this->_checknum($keyWord);
        //if ($res == true) {
        //    $searchType = 1;
        //}
        $memberRelationShipModel = new MemberRelationship();
        $memberRelationShipInfo  = $memberRelationShipModel->getResellerInfoByFidAndTitle($sid, $keyWord, true,
            $searchType, true);

        $data = $memberRelationShipInfo;
        $this->apiReturn(200, $data, '获取成功');
    }

    /**
     * 检查是否有数字
     *
     * @param $str
     *
     * @return bool
     * Author : liucm
     * Date : 2022/1/20
     */
    private function _checknum($str)
    {
        $arr = str_split($str, 1);
        $num = count($arr);
        for ($i = 0; $i < $num; $i++) {
            if (is_numeric($arr[$i])) {
                return true;
            }
        }
        return false;
    }

}

?>