<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 5/5-005
 * Time: 8:49
 */

namespace Controller\Finance;

use Business\Finance\MultiCredit;
use Library\Controller;
use Library\Exception;
use Library\Resque\Queue;

class MultiFinaceTask extends Controller
{
    /**
     * @var \Model\Finance\MultiFinaceTask
     */
    private $taskModel;
    private $_memberModel;
    private $_tradeModel;

    private $_sid;
    private $_authList;
    private $_loginInfo;

    public function __construct()
    {
        parent::isLogin();
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
        $this->_sid       = $this->_loginInfo['sid'];
        $this->_authList  = [55, 94, 3385];

        if (!in_array($this->_sid, $this->_authList)) {
            //parent::apiReturn(parent::CODE_AUTH_ERROR, [], "身份认证错误");
        }
        $this->taskModel = new \Model\Finance\MultiFinaceTask('pft001');
    }

    /**
     * 获取任务列表
     */
    public function getTaskList()
    {
        $bt       = I('get.bt', 0);
        $et       = I('get.et', 0);
        $bt       = !$bt ? strtotime(date('Y-m-d 00:00:00')) : strtotime($bt . ' 00:00:00');
        $et       = !$et ? strtotime(date('Y-m-d 23:59:59')) : strtotime($et . ' 23:59:59');
        $memberId = $this->_sid;
        $dataList = $this->taskModel->getTaskList($memberId, $bt, $et);
        parent::apiReturn(200, $dataList);
    }
    /**
     * 根据任务id获取详情列表
     */
    public function getTaskDetailList()
    {
        $taskId   = I('get.taskid', 0, 'intval');
        $dataList = $this->taskModel->getTaskDetailList($taskId);
        if (count($dataList)) {
            parent::apiReturn(200, $dataList);
        }
        parent::apiReturn(parent::CODE_NO_CONTENT, [], '查询数据为空');
    }

    /**
     * 授信查询
     */
    public function getCreditDetailInfo()
    {
        $cardNo    = I('get.cardno', 0, 'intval');
        $beginTime = I('get.bt', 0);
        $endTime   = I('get.et', 0);
        if (!$cardNo) {
            parent::apiReturn(parent::CODE_NO_CONTENT, [], '参数错误');
        }
        $benginFormat = !$beginTime ? date('Y-m-d 00:00:00') : trim($beginTime) . ' 00:00:00';
        $endFormat    = !$endTime ? date('Y-m-d 23:59:59') : trim($endTime) . ' 23:59:59';
        $this->_memberModel = new \Model\Product\MemberCard();
        //卡号权限判断
        $cardInfo = $this->_memberModel->getBindedMemberCard($cardNo, 'card_no', 0, 'apply_did');
        if (!$cardInfo || $cardInfo['apply_did'] != $this->_sid) {
            parent::apiReturn(parent::CODE_NO_CONTENT, [], '会员卡号不在你的权限内');
        }

        //获取会员卡办卡用户ID
        $cardInfo = $this->_memberModel->getMemberCardInfo(['card_no' => $cardNo], $filed = 'id,memberID');
        if(!$cardInfo) {
            parent::apiReturn(parent::CODE_NO_CONTENT, [], '查询数据为空');
        }
        $memberId = $cardInfo['memberID'];

        //交易列表
        $tradeModel  = new \Model\Finance\TradeRecord('slave');
        $memberJournalArray = $tradeModel->getFormatMemberJournalArray($memberId, '4,27', '2,3', $benginFormat, $endFormat);
        if (empty($memberJournalArray)) {
            parent::apiReturn(parent::CODE_NO_CONTENT, [], '查询数据为空');
        }
        parent::apiReturn(200, $memberJournalArray);
    }

    /**
     * 增加批量授信任务
     */
    public function createTask()
    {
        //保存任务
        $taskName   = I('post.task_name');
        $fid        = $this->_sid;
        $taskOpName = $this->_loginInfo['dname'] . '|员工ID:' . $this->_loginInfo['memberID'];
        $taskMemo   = I('post.task_memo');
        $taskId     = $this->taskModel->addTask($taskName, $fid, $taskOpName, $taskMemo);
        if (!$taskId) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST);
        }
        $taskBuss = new MultiCredit();
        try {
            $taskDetail = $taskBuss->csvHandler($taskId, $fid);
            $result     = $this->taskModel->addTaskDetail($taskDetail);
            if ($result) {
                $jobid = Queue::push('backend', 'MultiFinaceTask_Job', ['taskid' => $taskId]);
                parent::apiReturn(200, ['jobid' => $jobid], '任务添加成功，后台执行中');
            } else {
                parent::apiReturn(201, [], '任务添加失败:' . $this->taskModel->getDbError());
            }
        } catch (Exception $e) {
            parent::apiReturn(201, [], '任务添加失败:' . $e->getMessage());
        }
    }

    /**
     * 批量授信查询，获取交易记录详情
     * @param int post.trade_id 交易记录表id
     * @param int post.fid   交易记录表fid
     * @author: Jason
     */
    public function getJournalDetail() {
        $tradeId    = I('post.trade_id', 0, 'intval');
        $fid        = I('post.fid', 0, 'intval');
        if (!$tradeId || !$fid) {
            parent::apiReturn(parent::CODE_NO_CONTENT, [], '参数错误');
        }
        $this->_tradeModel = new \Model\Finance\TradeRecord();
        $tradeInfo = $this->_tradeModel->getDetails($tradeId, $fid, $this->_sid);
        if (empty($tradeInfo)) {
            parent::apiReturn(parent::CODE_NO_CONTENT, [], '获取数据失败');
        }
        //数据处理
        \Process\Finance\TradeRecord::handleDetailData($tradeInfo);
        parent::apiReturn(200, $tradeInfo);
    }
}

