<?php
    namespace Controller\Finance;
    use Library\Controller;
    use Model\TradeRecord;
    set_time_limit(0);
    class TradeTask extends Controller {
        // 初始化数据表
        public function index() {

            $searchTime = I('post.time', 0);
            if (strtotime($searchTime) && $searchTime) {
                $getFidTime = $time = strtotime($searchTime);
            } else {
                $time = time();
                $getFidTime = '';
            }

            $tmpPath = BASE_LOG_DIR.'/TransReport/';
            $tmpPath .= date('Y', $time) . '/' . date('m', $time) . '/';
            $failTmpPath = date('d', $time).'_fail.log';
            $sucTmpPath = date('d', $time).'_suc.log';
            if (file_exists($tmpPath.$failTmpPath) || file_exists($tmpPath.$sucTmpPath)) {
                echo json_encode(array('code' => 2, 'msg' => '该天日志已存在，不能重新跑'));
                die();
            }

            $this->pftMemberJournal = new TradeRecord\PftMemberJournal();
            $this->pftTransReport = new TradeRecord\PftTransReport();
            
            //执行脚本前先读取昨天错误日志数据
            $logTime = $time - 36 * 2400;
            $this->pftTransReport->readTransReportLog($logTime);

            //获取脚本执行当日的前一天所有有交易记录的fid和信用账户里的aid
            $res = $this->pftMemberJournal->getTransFids($getFidTime);

            //获取所有账户类型的键
            $accountType = load_config('account_type', 'trade_record');
            $itemCategoryIds = array_keys($accountType);
            //通过每个fid和账户类型（$b）搜索昨天的交易记录 信用账户比较特殊 还要搜索aid
            $result = array();
            foreach ($res as $key => $val) {
                foreach ($itemCategoryIds as $a => $b) {
                    if ($b == 2 || $b == 3) {
                        $data = $this->pftMemberJournal->getListInfoByFidAndActype((int)$val, $b, true, $time);
                    } else {
                        $data = $this->pftMemberJournal->getListInfoByFidAndActype((int)$val, $b, false, $time);
                    }
                    if ($data) {
                        $resultTotal[] = $data['totalArr'];
                        $resultDetail[] = $data['detailArr'];
                    }
                }
                $this->pftTransReport->insertTotalDataIntoTable($resultTotal, $time);
                $this->pftTransReport->insertDetailDataIntoTable($resultDetail, $time);
                unset($resultTotal);
                unset($resultDetail);
            }
            if (file_exists($failTmpPath)) {
                $logString = file_get_contents($failTmpPath);
                echo json_encode(['code' => 0, 'msg' => $logString]);
            } else {
                echo json_encode(['code' => 1, 'msg' => '操作成功']);
            }
        }
    }