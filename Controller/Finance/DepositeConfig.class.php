<?php

namespace Controller\Finance;

use Model\Finance\Deposite;
use Model\Member\Member;
use Model\Member\PftStaffModel;
use Library\Controller;
use Library\SimpleExcel;

/**
 * 预存费用配置控制器
 *
 * <AUTHOR>
 * @date 2018-01-23
 */
class DepositeConfig extends Controller
{
    // 当前用户id
    private $memberId;
    private $sid;
    // 当前用户信息
    private $loginInfo;
    // pftstaff模型
    private $pftStaffModel;

    // 查询权限, 用来做条件区分
    private $searchPermissions;

    public function __construct()
    {
        // 判断是否登录
        $this->loginInfo = $this->getLoginInfo();
        $this->memberId  = $this->loginInfo['memberID'];
        //$this->memberId  = $this->loginInfo['sid'];
        $this->sid       = $this->loginInfo['sid'];
    }




    /**
     * 添加预存配置
     * <AUTHOR>
     * @date 2018-01-19
     */
    public function addDepositeConfig()
    {
        // 管理员权限判断,或者是市场部用户
        if (!$this->_checkAuth()) {
            $this->apiReturn(400, [], '无权限');
        }

        $data = I('post.data');

        if (empty($data) || !is_array($data)) {
            $this->apiReturn(400, [], '要添加的配置不能为空');
        }

        $arr = [];
        $depositeModel = new Deposite();

        try {

            foreach ($data as $item) {
                if (empty($item['amount']) || empty($item['fee']) || empty($item['type']) || !$this->_checkNumber($item['fee'])  || $item['amount'] <= 0 || $item['fee'] <= 0) {
                    throw new \Exception('参数错误');
                }

                $arr[] = [
                    //充值人
                    'fid'           => $this->memberId,
                    //预存金额
                    'amount'        => $item['amount'] * 100,
                    //单条费用
                    'fee'           => $item['fee'] * 100,
                    //1电子凭证费 2短信费
                    'type'          => $item['type'],
                    //1电子凭证费 2短信费
                    'account_type'  => $item['account_type'],
                    // 失效
                    'status'        => 1,
                    'created_at'    => time(),
                    'updated_at'    => time(),
                ];
            }

            // 插入数据库
            $res = $depositeModel->addDepositeConfigByArr($arr);

            if ($res === false) {
                throw new \Exception('添加配置失败，请稍后重试');
            }

        } catch(\Exception $e) {
            $this->apiReturn(400, [], $e->getMessage());
        }

        $this->apiReturn(200, [], '添加成功');
    }


    /**
     *  预存配置列表
     */
    public function depositeConfigList()
    {
//        if (!$this->_checkAuth()) {
//            $this->apiReturn(400, [], '无权限');
//        }

        // 类型， 1凭证， 2短信
        $type = I('post.type', '', 'strval');
        // 状态, 1为未生效， 2为生效
        $status = I('post.status', '', 'strval');
        // 当前页
        $page = I('post.page', '1', 'strval');
        // 页显示数
        $pageSize = I('post.page_size', '15', 'strval');

        $depositeModel = new Deposite();

        $res = $depositeModel->getDepositeConfigList($type, $status, 'id, fid, amount, fee, type, status, created_at, updated_at, account_type', $page, $pageSize);

        if ($res === false) {
            $this->apiReturn(400, [], '获取数据失败');
        }

        // 处理分
        foreach ($res['list'] as &$item) {
            $item['fee'] = strval(round($item['fee'] / 100, 2));
            $item['amount'] = strval(round($item['amount'] / 100, 2));
        }

        $this->apiReturn(200, $res, '获取数据成功');
    }


    /**
     * 获取预存配置类型以及专用账户类型
     * <AUTHOR>
     * @date 2018-01-25
     */
    public function getDepositeConfigType()
    {
        // 预存配置类型
        $type = load_config('type', 'deposite');
        // 专用账户类型
        $accountType = load_config('account_type', 'deposite');

        $res = [
            'type' => $type,
            'account_type' => $accountType,
        ];

        $this->apiReturn(200, $res, '获取数据成功');
    }


    /**
     * 编辑预存配置的状态
     * <AUTHOR>
     * @date 2018-01-25
     */
    public function editDepositeConfigStatus()
    {
        if (!$this->_checkAuth()) {
            $this->apiReturn(400, [], '无权限');
        }

        // 生效列表
        $active  = I('post.active');
        // 失效列表
        $failure = I('post.failure');

        //参数为空
        if (empty($active) && empty($failure)) {
            $this->apiReturn(400, [], '非法参数');
        }
//        // 不是数组
//        if (!is_array($active) || !is_array($failure)) {
//            $this->apiReturn(400, [], '非法参数');
//        }

        $depositeModel = new Deposite();

        try{
            // 开启事务
            $depositeModel->startTrans();

            if (!empty($active)) {
                // 如果激活列表不为空,更新激活
                $res = $depositeModel->editDepositeConfig($active, 2);

                if ($res === false) {
                    throw new \Exception('更新生效列表失败');
                }
            }

            if (!empty($failure)) {
                // 如果失效列表不为空,更新失效
                $res = $depositeModel->editDepositeConfig($failure, 1);

                if ($res === false) {
                    throw new \Exception('更新失效列表失败');
                }
            }

            // 提交事务
            $depositeModel->commit();
        } catch(\Exception $e) {
            // 回滚
            $depositeModel->rollback();
            $this->apiReturn(400, [], $e->getMessage());
        }

        $this->apiReturn(200, [], '更新列表成功');
    }

    /**
     * 获取用户专用账户信息
     * <AUTHOR>
     * @date 2018-01-31
     */
    public function getMemberAccount()
    {
        $biz = new \Business\Finance\AccountMoney();
        $res = $biz->getAccountSmsAndCode($this->loginInfo['sid']);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
    /**
     * 获取用户专用账户信息和配置金额信息
     * <AUTHOR>
     * @date 2018-01-31
     */
    public function getMemberSpecialDeposits(){
        $biz = new \Business\Finance\AccountMoney();
        $res = $biz->getMemberSpecialDepositsService($this->loginInfo['sid']);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
    /**
     * 搜索获取客服，签单人员，区域等信息
     * <AUTHOR>
     * @date 2018-01-30
     */
    public function getSearchInfo()
    {
        $PftStaffModel = $this->getPftStaffModel();
        $Member        = new Member();
        //所以签单人  角色 = 签单人员+区域经理+部门经理
        $wherelist['st_type']  = array('in', '0,2,3');
        //选择正常得员工
        $wherelist['st_status']  = 0;
        //签单人员排除 客服经理 写死
        $wherelist['st_memberid']  = array('NEQ', '24322');
        $list     = $PftStaffModel->getSpeatStaffList('st_memberid', $wherelist);
        //所有客服
        $listName = $Member->getMemberInfoByMulti($list, 'id', 'dname,id');
        //客服经理
        $customerId   = $PftStaffModel->getKefuList();
        $kefuManage = $PftStaffModel->getStaffInfo('24322', 'st_memberid', '*');
        if ($kefuManage) {
            $customerId[$kefuManage['id']] = [
                'id'            => $kefuManage['id'],
                'st_personid'  => $kefuManage['st_personid'],
                'st_name'      =>  $kefuManage['st_name'],
                'st_memberid'  => $kefuManage['st_memberid'],
            ];
        }

        // 获取区域
        $group = load_config('sale_group', 'account');

        // 获取商户类型
        $merchantType = load_config('merchant_type', 'account');

        // 获取预存类型
        $type = load_config('type', 'deposite');

        //代码报错
        $customerId = $customerId ? $customerId : [];

        $data = [
            'sale' => $listName,
            'kefu' => array_values($customerId),
            'group'=> $group,
            'merchant_type' => $merchantType,
            'type' => $type
        ];

        $this->apiReturn(200, $data, '获取成功');
    }

    /**
     * 查询预存账户
     * <AUTHOR>
     * @date 2018-01-26
     */
    public function searchExclusiveAccount()
    {
        // 判断是否有权限搜索
        //        if (!$this->_checkSearchAuth()) {
        //            $this->apiReturn(103, [], '无权限');
        //        }

        // 开始时间
        $begin      = I('param.begin');
        // 结束时间
        $end        = I('param.end');
        // 区域
        $region     = I('param.region');
        // 签单人员
        $salesId    = I('param.sales_id');
        // 客服
        $kefuId     = I('param.kefu_id');
        // 商户类型
        $dtype      = I('param.dtype');
        // 生于可能金额, 小于
        $lmoney     = I('param.lmoney');
        // 是否excel导出
        $isExcel    = I('param.is_excel');
        // 选择类型, 1 企业名称, 2账户名称, 3账号, 4姓名, 5手机号, 6ID, 7接口编码, 8支付宝账号
        $selectType = I('param.select_type', '1', 'strval');
        // 所选类型的值
        $selectValue= I('param.select_value');
        // 预存类型
        $type       = I('param.type', '', 'strval');
        // 是否排序， 1是，降序， 0不排序, 默认不排序, 2升序
        $isSort     = I('param.is_sort', 0, 'intval');
        // 当前页
        $page       = I('param.page', '1', 'strval');
        // 页显示数
        $pageSize   = I('param.page_size', '15', 'strval');

        $begin .= ' 00:00:00';
        $end   .= ' 23:59:59';

        $memberModel = new Member();

        $regionInfo = [];

        // 对权限进行修正搜索条件， 对于管理员跟市场部没有判断限制， 如果是客服人员则限制kefuId是自己， 签单人员则salesId是自己
        switch ($this->searchPermissions) {
            case 1: // 市场部权限目前是相同的, 不做限制
                break;
            case 2: // 签单人员
                $region = '';
                $salesId = $this->memberId;
                break;
            case 3: // 客服人员
                $region = '';
                $kefuId = $this->memberId;
                break;
        }

        // 判断是否需要先获取region
        if ($region !== '') {
            $regionInfo = $this->getRegionMemberInfo($region);
        }

        // 获取预存账户数据
        $res = $memberModel->getExclusiveAccountList($salesId, $kefuId, $selectType, $selectValue, $regionInfo, $begin,
            $end, $type, $dtype, $lmoney, $isExcel, $isSort, $page, $pageSize);

        $res['list'] = $this->_handleExclusiveAccountList($res['list']);
        $res['money']= round($res['money'] / 100, 2);

        if ($isExcel) {
            // 导出
            $this->_exportExclusiveAccountList($res['list']);
        }

        $this->apiReturn(200, $res, '获取数据成功');
    }

    /**
     * 根据区域获取签认单跟客服信息
     * <AUTHOR>
     * @date 2018-01-29
     * @param $region
     * @return array
     */
    public function getRegionMemberInfo($region)
    {
        $PftStaffModel = $this->getPftStaffModel();
        $res = $PftStaffModel->getInfoByGroup(intval($region), true);
        return $res;
    }

    /**
     * 查询权限验证市场部指定人拥有全部查询权限，  签单人员跟客服人员仅拥有限定查询条件
     * <AUTHOR>
     * @date 2018-02-01
     * @return boolean
     */
    private function _checkSearchAuth()
    {
        // 判断是否是签单人员
        if ($this->_checkSale($this->memberId)) {
            $this->searchPermissions = 2;
            return true;
        }

        // 判断是否是客服人员
        if ($this->_checkKefu($this->memberId)) {
            $this->searchPermissions = 3;
            return true;
        }

        // 判断是否是市场部, 或者指定查询账号权限
        if ($this->_checkMarket() || $this->_checkSearchAccount()) {
            $this->searchPermissions = 1;
            return true;
        }

        return false;
    }



    /**
     * 判断是否是客服人员
     * <AUTHOR>
     * @date 2018-02-01
     * @param $fid
     * @return boolean
     */
    private function _checkKefu($fid)
    {
        $pftStaffModel = $this->getPftStaffModel();
        $res = $pftStaffModel->getKefuByMemberId($fid);

        if (empty($res)) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否签单人员
     * <AUTHOR>
     * @date 2018-02-01
     * @param $fid
     * @return boolean
     */
    private function _checkSale($fid)
    {
        $pftStaffModel = $this->getPftStaffModel();
        $res = $pftStaffModel->getSaleByMemberId($fid);

        if (empty($res)) {
            return false;
        }

        return true;
    }


    /**
     * 获取pftstaffmodel
     * <AUTHOR>
     * @date 2018-02-01
     */
    private function getPftStaffModel()
    {
        if (!$this->pftStaffModel) {
            $this->pftStaffModel = new PftStaffModel();
        }

        return $this->pftStaffModel;
    }


    /**
     * 判断是否有权限， 市场部指定人
     * <AUTHOR>
     * @return bool
     */
    private function _checkAuth()
    {
        // 判断是否管理员
        if ($this->_checkMarket()) {
            return true;
        }

        return false;
    }

    /**
     * 是否是市场部指定人判断
     * <AUTHOR>
     * @date 2018-01-19
     */
    private function _checkMarket()
    {
        $manager = load_config('manager', 'deposite');

        if (!in_array($this->memberId, $manager)) {
            return false;
        }

        return true;
    }

    /**
     * 是否是指定查询人员
     */
    private function _checkSearchAccount()
    {
        $member = load_config('search', 'dposite');

        if (!in_array($this->memberId, $member)) {
            return false;
        }

        return true;
    }

    /**
     * 处理预存列表数据
     * <AUTHOR>
     * @date 2018-01-30
     *
     * @param array $data
     * @return array
     */
    private function _handleExclusiveAccountList($data)
    {
        // 获取所有客服及销售的信息
        $memberModel = new Member();
        $mids = array_merge(array_column($data, 'salesId'), array_column($data, 'kefuId'));
        $res = $memberModel->getMemberInfoByMulti($mids, 'id', 'id, dname');
        $memberInfo = [];
        foreach ($res as $item) {
            $memberInfo[$item['id']] = $item['dname'];
        }

        // 获取总金额
        $userIdArr = array_column($data, 'user_id');
        $moneyRes = $memberModel->getExclusiveMoneyTotal($userIdArr);
        $moneyResArr = [];
        foreach ($moneyRes as $item) {
            $moneyResArr[$item['user_id']] = $item['money'];
        }

        // 获取商户类型
        $merchantType = load_config('merchant_type', 'account');

        // 获取预存类型
        $type = load_config('type', 'deposite');

        foreach ($data as &$item) {
            $item['dtype']          = $merchantType[$item['dtype']];
            $item['update_time']    = date('Y-m-d H:i:s', $item['create_time']);
            $item['saleId']         = isset($memberInfo[$item['salesId']]) ? $memberInfo[$item['salesId']] : '未知';
            $item['kefuId']         = isset($memberInfo[$item['kefuId']]) ? $memberInfo[$item['kefuId']]: '未知';
            $item['type']           = $type[$item['type']];
            $item['total_use']      = round(($item['dmoney'] - $item['lmoney']) / 100, 2);
            $item['lmoney']         = round($item['lmoney'] / 100, 2);
            $item['total_money']    = isset($moneyResArr[$item['user_id']]) ?round($moneyResArr[$item['user_id']] / 100, 2) : 0;
        }

        return $data;
    }

    /**
     * 预存配置导出
     * <AUTHOR>
     * @date 2018-01-30
     * @param $data
     */
    private function _exportExclusiveAccountList($data)
    {
        $filename = date('Ymd') . '预存配置报表';
        $excel[0] = [
            '商户id', '商户类型', '企业名称', '手机号', '账号名称', '账号', '预存时间', '预存类型', '预存金额', '剩余可用金额', '签单人', '客服',
        ];
        $i = 1;

        foreach ($data as $item) {
            $excel[$i] = [
                $item['user_id'],
                $item['dtype'],
                $item['com_name'],
                $item['mobile'],
                $item['dname'],
                $item['account'],
                $item['update_time'],
                $item['type'],
                $item['total_use'] + $item['lmoney'],
                $item['lmoney'],
                $item['salesId'],
                $item['kefuId'],
            ];

            $i++;
        }

        $xls = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }


    /**
     * 判断是否是数字
     * <AUTHOR>
     * @date 2018-01-26
     * @param $number
     * @return bool
     */
    private function _checkNumber($number)
    {
        if(preg_match('/^[0-9]+(.[0-9]{1,2})?$/', $number)) {
            return true;
        }

        return false;
    }





}