<?php
/**
 * 银行相关控制器
 *
 * <AUTHOR>
 * @date 2016-07-08
 * 
 */
namespace Controller\Finance;

use Library\Controller;

class Banks extends Controller{
    private $memberId = null;

    public function __construct() {
        //$this->memberId = $this->isLogin('ajax');
    }

    /**
     * 获取银行列表
     * <AUTHOR>
     * @date   2016-07-08
     *
     * @return
     */
    public function getList() {
        $page = I('post.page', 1);
        $size = I('post.size', 700);
        $name = I('post.name', '', 'strval');

        $model = new \Model\Finance\Banks();

        //银行列表
        $list = $model->getBanks($page, $size, $name);

        //省份列表
//        $province = $model->getBankProvince();
        $areaApi  = new \Business\JavaApi\Area\Area();
        $provinceData = $areaApi->getList(null, null, 1);
        $province = [];

        if ($provinceData['code'] && $provinceData['data']['list']) {
            foreach ($provinceData['data']['list'] as $areaItem){
                $data = [];
                $data['code'] = $areaItem['areaCode'];
                $data['name'] = $areaItem['areaName'];
                $province[] = $data;
            }
        }
        if($list === false) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            $this->apiReturn(200, ['list' => $list, 'province' => $province]);
        }
    }

    /**
     * 获取城市
     * <AUTHOR>
     * @date   2016-07-08
     *
     * @return
     */
    public function cityList() {
        $province = I('post.province_id', false);
        if(!$province) {
            $this->apiReturn(400, [], '参数错误');
        }

//        $model = new \Model\Finance\Banks();
//
//        //城市列表
//        $list = $model->getCity($province);
        $AreaApi = new \Business\JavaApi\Area\Area();
        $provinceData  = $AreaApi->getList(null, null, 2, $province, null, null, null);
        $list = [];

        if ($provinceData['code'] && $provinceData['data']['list']) {
            foreach ($provinceData['data']['list'] as $areaItem){
                $data = [];
                $data['code'] = $areaItem['areaCode'];
                $data['name'] = $areaItem['areaName'];
                $list[] = $data;
            }
        }
        if($list === false) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            $this->apiReturn(200, ['list' => $list]);
        }
    }

    /**
     * 获取支行信息
     * <AUTHOR>
     * @date   2016-07-08
     *
     * @return  
     */
    public function subbranchList() {
        $page = I('post.page', 1);
        $size = I('post.size', 50);
        $name = I('post.name', '');

        $cityId     = I('post.city_id', 0);
        $bankId     = I('post.bank_id', 0);
        $provinceId = I('post.province_id', 0);

        if ((!$cityId && !$provinceId) || !$bankId) {
            $this->apiReturn(400, [], '参数错误');
        }

        $model = new \Model\Finance\Banks();

        $res = $model->getSubbranch($cityId, $bankId, $provinceId, $name, $page, $size);

        if($res === false) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            $this->apiReturn(200, $res);
        }
    }
}