<?php
/**
 * 用户余额清分后台配置控制器
 * 清分模型：1=日结，2=周结，3=月结
 * 资金冻结类型：1=冻结未使用的总额，2=按比例或是具体数额冻结
 * 
 *
 * <AUTHOR>
 * @date 2016-01-20 
 * 
 */

namespace Controller\Finance;

use Library\Controller;
use Model\Member\Member;

class SettleBlance extends Controller {
    private $_memberId = null;
    private $_sid      = null;
    private $_logPath  = 'auto_withdraw/setting';

    private $_modeArr = [
        1, // 日结
        2, // 周结
        3, // 月结
    ];

    private $_freezeArr = [
        1, // 冻结未使用的总额
        2, // 按比例冻结
    ];

    private $_accountArr = [
        1, //银行账户1
        2, //银行账号2
    ]; 

    private $_moneyArr = [
        1, //按比例冻结
        2, //按具体的金额冻结
    ];

    private $_cutArr = [
        0, //提现金额中扣除
        1, //账户余额扣除
    ];

    public function __construct()
    {
        //只有管理员才能进行操作
        $this->_sid      = $this->isLogin('ajax');
        $loginInfo       = $this->getLoginInfo('ajax', false, false);
        $this->_memberId = $loginInfo['memberID'];
    }

    /**
     * 获取具体的转账记录
     * <AUTHOR>
     * @date   2016-06-21
     *
     * @return  
     */
    public function getRecords() {
        $page = intval(I('post.page'));
        $size = intval(I('post.size', 20));

        //是管理员才能传参数
        if($this->_sid == 1) {
            $fid  = intval(I('post.fid', '')) ? intval(I('post.fid', '')) : $this->_sid;
        } else {
            $fid = $this->_sid;
        }

        if(!$fid) {
            $this->apiReturn(400, [], '参数错误');
        }

        //获取用户禁止提现配置
        $limitApi      = new \Business\JavaApi\Member\MemberLimit();
        $withdrawLimit = $limitApi->queryFunctionLimitByFidAndLimitType($fid, $limitType = 2);
        $withdrawData  = [];
        if ($withdrawLimit['code'] == 200 && !empty($withdrawLimit['data'])) {
            $withdrawData = $withdrawLimit['data'];
        }

        $page = max($page, 1);
        if ($size < 0) {
            $size = 20;
        } elseif ($size > 100) {
            $size = 100;
        }
        $settleBlanceModel = new \Model\Finance\SettleBlance();
        $tmp = $settleBlanceModel->getRecords($fid, $page, $size);

        $count = $tmp['count'];
        $list  = $tmp['list'];

        //获取总页数
        $totalPage = ceil($count / $size);

        $res = ['count' => $count, 'page' => $page, 'total_page' => $totalPage, 'list' => [], 'withdrawData' => $withdrawData];
        foreach($list as $item) {
            //获取状态
            if($item['status'] != 0 && $item['status'] != 1) {
                //清算或是清分出现问题,具体见备注
                $status = 4;
            } else {
                if($item['is_settle'] == 0) {
                    //待清算
                    $status = 2;
                } else if($item['is_transfer'] == 0) {
                    //清算,待转账
                    $status = 3;
                } else {
                    //清分成功
                    $status = 1;
                }
            }

            $frozeData  = @json_decode($item['froze_data'], true);
            if(is_array($frozeData)) {
                $freezeType = $frozeData['freeze_type'] == 1 ? 0 : $frozeData['type'];
            } else {
                $freezeType = 0;
            }

            $res['list'][] = [
                'fid'            => $item['fid'],
                'settle_time'    => $item['settle_time'],
                'transfer_time'  => $item['transfer_time'],
                'status'         => $status,
                'init_money'     => $item['init_money'],
                'freeze_money'   => $item['freeze_money'],
                'transfer_money' => $item['transfer_money'] / 100,   //数据库以分为单位，除以100
                'settle_money'   => $item['settle_time_money'],
                'settle_remark'  => str_replace('账户余额', '未结票款', $item['remark']),
                'trans_remark'   => $item['trans_remark'],
                'update_time'    => $item['update_time'],
                'mode'           => $item['mode'],
                'cycle_mark'     => $item['cycle_mark'],
                'freeze_type'    => $freezeType
            ];
        }

        $this->apiReturn(200, $res);
    }

    /**
     * 获取冻结订单汇总信息
     * <AUTHOR>
     * @date   2016-06-21
     *
     * @return  
     */
    public function getFrozeSummary() {
        $mode = intval(I('post.mode'));
        $mark = intval(I('post.cycle_mark'));

        if(!$mode || !$mark) {
            $this->apiReturn(400, [], '参数错误');
        }

        //是管理员才能传参数
        if($this->_sid == 1) {
            $fid  = intval(I('post.fid', '')) ? intval(I('post.fid', '')) : $this->_sid;
        } else {
            $fid = $this->_sid;
        }

        $settleBlanceModel = new \Model\Finance\SettleBlance();
        $tmp = $settleBlanceModel->getFrozeOrders($fid, $mode, $mark, true);

        if($tmp === false) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            //['orders' : 100, 'tickets' : 200, 'money' : 14000]
            $this->apiReturn(200, $tmp);
        }
    }

    /**
     * 获取冻结订单汇总信息
     * <AUTHOR>
     * @date   2016-06-21
     *
     * @return
     * {
     *      'count' : 27,
     *      'list'  : [{
     *          ltitle : '【测试】没那么简单',
     *          ttitle : '成人测试测试票',
     *          ordernum : '3316099',
     *          money : '200',
     *          tickets : '2',
     *      }]
     * }
     */
    public function getFrozeOrders() {
        $mode = intval(I('post.mode'));
        $mark = intval(I('post.cycle_mark'));
        $page = intval(I('post.page', 1));
        $size = intval(I('post.size', 20));

        if(!$mode || !$mark) {
            $this->apiReturn(400, [], '参数错误');
        }

        //是管理员才能传参数
        if($this->_sid == 1) {
            $fid  = intval(I('post.fid', '')) ? intval(I('post.fid', '')) : $this->_sid;
        } else {
            $fid = $this->_sid;
        }

        $page = max($page, 1);
        if ($size < 0) {
            $size = 20;
        } elseif ($size > 100) {
            $size = 100;
        }
        $settleBlanceModel = new \Model\Finance\SettleBlance();
        $res = $settleBlanceModel->getFrozeOrdersInfo($fid, $mode, $mark, $page, $size);

        //获取总页数
        $totalPage = ceil($res['count'] / $size);

        //添加页数等信息
        $res['total_page'] = $totalPage;
        $res['page']       = $page;

        $this->apiReturn(200, $res);
    }
}