<?php
/**
 * 平台账户余额充值
 */

namespace Controller\Finance;


use Business\Captcha\CaptchaApi;
use Business\JsonRpcApi\PayService\UnifiedPay;
use Business\RiskManagement\ShumeiRiskCheck;
use Controller\pay\CommonPayNotify;
use Library\Controller;
use Library\Kafka\KafkaProducer;
use Model\TradeRecord\OnlineTrade;

class Recharge extends Controller
{
    const RECHARGE_NOTIFY_URL = PAY_DOMAIN.'r/pay_CommonPayNotify/platformRechargeNotify';
    const RECHARGE_SOURCET = 68; // 与CommonPayNotify::platformRechargeNotify保持一致
    /**
     * 获取平台账户余额充值二维码
     * 请求支付中心的收银台地址
     * url:https://my.12301.cc/r/Finance_Recharge/platformRechargeQr
     */
    public function platformRechargeQr()
    {
        $money    = I('post.money');
        $money    = floatval(number_format($money, 2, '.', ''));
        $aid      = I('post.aid');
        $did      = I('post.did', 0);

        $memo     = I('post.memo', '', 'trim');
        $shopId   = I('post.shop_id', '', 'intval');
        $frontUrl = I('post.success_url', $_SERVER['HTTP_REFERER']);
        $payType  = I('post.pay_type', 2, 'intval');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        $sid       = $loginInfo['sid'] ?? 0;

        if ($loginInfo['dtype'] == 2) {
            $this->apiReturn(401, [], '景区账号不能充值，请退出重登');
        }

        if ($loginInfo['dtype'] == 5) {
            $this->apiReturn(401, [], '散客账号不能充值，请退出重登');
        }

        $did = $did ? $did : $sid;
        if (!$did) {
            $this->apiReturn(401, [], '用户身份获取错误');
        }

        if (!is_numeric($money) || $money < 0) {
            $this->apiReturn(401, [], '请输入大于0的金额，金额必须是数字');
        }
        $queryParams = [[$did, $shopId, $aid]];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
            $queryParams);

        if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
            $this->apiReturn(401, [], '用户信息获取失败，请稍后重试');
        }

        $memberInfo = array_column($queryRes['data'], null, 'id');
        $sellName = str_replace('|', '', $memberInfo[$did]['dname']);

        if ($aid > 0) {
            $boss_name = str_replace('|', '', $memberInfo[$aid]['dname']);
            if ($shopId) {
                $through_name = str_replace('|', '', $memberInfo[$shopId]['dname']);
                $body         = "[$sellName]通过{$through_name}给{$boss_name}充值{$money}元|{$did}|$aid|$shopId";
            } else {
                $body = "[$sellName]给{$boss_name}充值{$money}元|{$did}|$aid";
            }
        } else {
            $body = "[{$sellName}]账户充值{$money}元|{$did}";
        }

        if ($memo) {
            $body .= '|' . $memo;
        }
        //支付订单号,如果前端有传的话用前端传过来的。
        $outTradeNo = I('post.rechage_ordernum', time() . $did . mt_rand(1000, 9999));
        //滑块验证
        $captchaCode = I('post.captchaCode', '', 'strval');
        if (empty($captchaCode)){
            $riskCheck = new ShumeiRiskCheck();
            $virtualOrderEventData = [
                'product'   => '账本充值',
                'orderId'   => $outTradeNo,
                'price'     => floatval($money),
            ];
            $orderCheckRes = $riskCheck->shumeiCheck('virtualOrder', 2, $loginInfo['account'], $virtualOrderEventData);

            $paymentEventData = [
                'method'  => 'qrscan',
                'channel' => 'qrscan',
                'amount'  => floatval($money),
                'orderId' => $outTradeNo,
            ];
            $payCheckRes      = $riskCheck->shumeiCheck('payment', 2, $loginInfo['account'], $paymentEventData);
            if (($payCheckRes['code'] == 200 && $payCheckRes['data']['riskLevel'] == 'REJECT') || ($orderCheckRes['code'] == 200 && $orderCheckRes['data']['riskLevel'] == 'REJECT')) {
                $this->apiReturn(209, ['need_captcha' => true], '请验证滑块');
            }
        }else{
            $captchaCheck = new CaptchaApi();
            $captchaRes   = $captchaCheck->handleSecondCheck($captchaCode);
            if ($captchaRes !== true) {
                $this->apiReturn(209, ['need_captcha' => true], '请验证滑块');
            }
        }

        $model = new OnlineTrade();
        $ret   = $model->addLog($outTradeNo, $money, $body, $body, self::RECHARGE_SOURCET,OnlineTrade::PAY_METHOD_RECHARGE);
        $model = null;
        if (!$ret) {
            parent::apiReturn(401, [], '记录发生错误,请联系客服人员');
        }
        // 调用支付中心收银台页面
        $unifiedPay  = new UnifiedPay();
        $rpcResponse = $unifiedPay->unifyQrPayRpcService($outTradeNo, $body, $money*100, PFT_RECHARGE_MERCHANT_ID, $payType,
            'platform', self::RECHARGE_NOTIFY_URL, $_SERVER['REMOTE_ADDR'], '',
            ['pft_member_id'=>$did,'source'=>'platform_recharge'], true);

        if ($rpcResponse['code'] == 200) {
            $data = [
                'outTradeNo' => $outTradeNo,
                'qrUrl'      => $rpcResponse['data']['url'],
            ];
            parent::apiReturn(200, $data);
        }
        parent::apiReturn(401, [], $rpcResponse['msg']);
    }

    /**
     * 微信端支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $this->isLogin('ajax');
        $ordernum = I('post.ordernum');
        pft_log('yeepay/pay_result_check', json_encode($_POST, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        $model = new OnlineTrade();

        $payLog = $model->getLog($ordernum, self::RECHARGE_SOURCET);
        if (!$payLog) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在', true);
        }

        if ($payLog['status'] == 1) {
            parent::apiReturn(parent::CODE_SUCCESS, [], '支付成功', true);
        }
        parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看', true);
    }

}
