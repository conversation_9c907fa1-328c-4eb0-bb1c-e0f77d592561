<?php
/**
 * 微信会员交易记录查询
 * @since 2016-12-16
 * <AUTHOR>
 */
namespace Controller\Finance;

use Library\Controller;
use Model\Finance\TradeRecord;
use Model\Member\Member;
use Model\Order\OrderTools;

class wxTradeRecord extends Controller
{
    private $_memberModel; //会员模型
    private $_memberId; //会员id
    private $_aid; //被关注的商户id

    public function __construct()
    {
        C(include HTML_DIR . '/Service/Conf/trade_record.conf.php');
        $this->_memberModel = new Member();
        $this->_memberId    = $this->isLogin();
        $this->_aid         = $this->getAid();
        $this->authMember();
    }

    /**
     * 会员卡会员认证
     */
    public function authMember()
    {
        $memberCardModel = new \Model\Product\MemberCard();
        $res = $memberCardModel->checkBindedMemberCard($this->_memberId, $this->_aid);
        if (empty($res) || $res['num'] == 0) {
            $this->apiReturn(0, array(), '非会员卡会员登录');
        }
    }

    /**
     * 获取商户ID
     * @param  fid    商户ID
     * @param  aAcc   商户帐号   eg. http://123624.12301.cc,  aAcc = 123624
     */
    public function getAid()
    {
        $aid = I('fid', 0, 'intval');
        if ($aid == 0) {
            //获取不到商户ID传参fid时，尝试获取商户帐号aAcc
            $aAcc = I('aAcc', null);
            if ($aAcc && $aAcc != 'wx') {
                $accInfo = $this->_memberModel->getMemberInfo($aAcc, 'account');
                if ($accInfo) {
                    $aid = $accInfo['id'];
                }
            }
            if ($aid == 0) {
                $this->apiReturn(400, array(), '关注帐号错误');
            }
        }
        return $aid;
    }

    /**
     * 交易记录订单预处理
     * @param string  $action 操作类别， all - 交易记录， product - 产品交易, pay - 充值记录
     * @return array  $params 参数数组
     *                  $params['action']    操作类别
     *                  $params['memberId']  会员ID
     *                  $params['aid']       商户ID
     *                  $params['offset']    分页开始条数
     *                  $params['size']      分页显示条数
     *                  $params['dtype']     dtype数组
     *                $orderList        订单记录数组（主）
     *                $productOrderList 产品交易订单数组（分）
     *                $payOrderList     充值记录订单数组（分）
     *
     */
    private function _preproccessOrderList($action = 'all')
    {
        $accType  = I('accType', 0, 'intval');
        $offset   = I('offset', 1, 'intval');
        $size     = I('size', 10, 'intval');
        $memberId = $this->_memberId;

        // accType: 0 - 账户余额， 2 - 授信
        if ($accType == 2) {
            //授信 需要指定被关注的商户id
            $aid = $this->_aid;
        } else {
            $accType = 0;
            //账户余额 不需要指定被关注的商户id
            $aid = 0;
        }

        //交易分类， all-所有交易， product-产品交易-dtype-2， pay-充值交易-dtype-3
        $item_cat = C('wx_member_item_category');
        $dtypeArr = [];

        foreach ($item_cat as $cat) {
            //交易记录 dtype
            $dtypeArr['all'][] = $cat[0];
            if ($cat[2] == 2) {
                //产品交易 dtype
                $dtypeArr['product'][] = $cat[0];
            } else if ($cat[2] == 3) {
                //充值交易 dtype
                $dtypeArr['pay'][] = $cat[0];
            }
        }

        switch ($action) {
            case 'product':
                $dtype = $dtypeArr['product'];
                break;
            case 'pay':
                $dtype = $dtypeArr['pay'];
                break;
            case 'all':
                $dtype = $dtypeArr['all'];
                break;
        }

        $productDtype = $dtypeArr['product'];
        $payDtype     = $dtypeArr['pay'];

        $params = [
            'action'   => $action,
            'memberId' => $memberId,
            'aid'      => $aid,
            'accType'  => $accType,
            'offset'   => $offset,
            'size'     => $size,
            'dtype'    => $dtype,
        ];

        //暂时这边只查询最近半年的数据，如果需要查询之前的数据，请设置相应的开始和结束时间
        $startTime = false;
        $endTime   = false;

        $tradeModel = new TradeRecord('slave', $startTime, $endTime);
        $orderList  = $tradeModel->getMemberTradeRecordOrderList($memberId, $accType, $dtype, $aid, $offset, $size);

        if (!is_array($orderList)) {
            $this->apiReturn(201, array(), '查询失败');
        }

        if (empty($orderList)) {
            $this->apiReturn(204, array(), '没有数据');
        }

        $realOrderList    = [];
        $productOrderList = [];
        $payOrderList     = [];

        foreach ($orderList as $order) {
            $order['realOrderId'] = $order['orderid'];
            //获取真实订单号，联票子票订单号从备注中获取
            if (!empty($order['memo'])) {
                //eg. 联票子票订单(4001590)【【测试】景区优惠票*(2)】
                //$pattern = '/^联票子票订单\(([A-Za-z0-9]+)\)/';
                //之前是从 "联票子票订单" 开始匹配 会有匹配不到的情况 2019年第一次购票联票子票订单(([A-Za-z0-9]+))
                $pattern = '/联票子票订单\(([A-Za-z0-9]+)\)/';
                $match   = preg_match($pattern, $order['memo'], $matches);
                if ($match) {
                    $order['realOrderId'] = $matches[1];
                }
            }

            //归类交易类型
            if (in_array($order['dtype'], $productDtype)) {
                //产品交易为类别2
                $order['cat'] = 2;
                //提取产品交易订单列表
                $productOrderList[] = $order;
            } elseif (in_array($order['dtype'], $payDtype)) {
                //充值记录为类别3
                $order['cat'] = 3;
                //提取充值记录订单列表
                $payOrderList[] = $order;
            }

            //交易记录订单主列表
            $realOrderList[] = $order;
        }

        return ['params' => $params, 'orderList' => $realOrderList, 'productOrderList' => $productOrderList, 'payOrderList' => $payOrderList];
    }

    /**
     * 交易记录查询  - 交易记录=产品交易+充值记录
     */
    public function allTransaction()
    {
        $data             = $this->_preproccessOrderList('all');
        $params           = $data['params'];
        $orderList        = $data['orderList'];
        $productOrderList = $data['productOrderList'];
        $payOrderList     = $data['payOrderList'];
        $productOrderInfo = $this->_getProductOrderInfoList($params, $productOrderList);
        $payOrderInfo     = $this->_getPayOrderInfoList($params, $payOrderList);

        //交易记录=产品交易+充值记录
        //此处因为键名均为数字，所以不能使用array_merge
        $orderInfo = $productOrderInfo + $payOrderInfo;


        //填充订单信息到订单列表
        $list = $this->_fillOrderInfo($params, $orderList, $orderInfo);

        $this->apiReturn(200, $list, '');
    }

    /**
     * 产品交易查询
     */
    public function productTransaction()
    {
        $data             = $this->_preproccessOrderList('product');
        $params           = $data['params'];
        $orderList        = $data['orderList'];
        $productOrderList = $data['productOrderList'];
        $productOrderInfo = $this->_getProductOrderInfoList($params, $productOrderList);

        $list = $this->_fillOrderInfo($params, $orderList, $productOrderInfo);
        $this->apiReturn(200, $list, '');
    }

    /**
     * 充值记录查询
     */
    public function payTransaction()
    {
        $data         = $this->_preproccessOrderList('pay');
        $params       = $data['params'];
        $orderList    = $data['orderList'];
        $payOrderList = $data['payOrderList'];
        $payOrderInfo = $this->_getPayOrderInfoList($params, $payOrderList);

        $list = $this->_fillOrderInfo($params, $orderList, $payOrderInfo);

        $this->apiReturn(200, $list, '');
    }

    /**
     * 获得产品交易订单信息
     * @param $params   参数数组
     * @param $productOrderList  产品交易订单列表
     * @return array
     */
    private function _getProductOrderInfoList($params, $productOrderList)
    {
        if (empty($params)) {
            return [];
        }

        if (empty($productOrderList)) {
            return [];
        }

        //提取真实订单号进行查询
        $orderIdList = array_unique(array_column($productOrderList, 'realOrderId'));

        //获取订单下单时信息
        $orderModel     = new OrderTools('slave');
        $orderApplyInfo = $orderModel->getOrderListNew($orderIdList, 'ss.ordernum, ss.lid, ss.tid', $detailField = false, $addonField = false, $applyField = 'apply.origin_num');

        if (empty($orderApplyInfo)) {
            return [];
        }
        $orderList = [];
        $lidList   = [];
        $tidList   = [];

        //提取下单时景区产品id和票id列表
        foreach ($orderApplyInfo as $orderInfo) {
            if (!in_array($orderInfo['lid'], $lidList)) {
                $lidList[] = $orderInfo['lid'];
            }

            if (!in_array($orderInfo['tid'], $tidList)) {
                $tidList[] = $orderInfo['tid'];
            }

            $orderList[$orderInfo['ordernum']] = $orderInfo;

        }

        //获取景区产品信息
        //$landModel        = new \Model\Product\Land();
        //$landField        = 'id, title';
        //$landFilter['id'] = ['in', $lidList];
        //$landRes          = $landModel->getInfoInLand($landField, $landFilter);

        $javaAPi = new \Business\CommodityCenter\Land();
        $landRes = $javaAPi->queryLandMultiQueryById($lidList);

        //获取票类信息
        $ticketField = 'id, title';
        $javaApi     = new \Business\CommodityCenter\Ticket();
        $ticketArr   = $javaApi->queryTicketInfoByIds($tidList, $ticketField);
        $ticketRes   = array_column($ticketArr, 'ticket');

        $landInfo = [];
        foreach ($landRes as $land) {
            $landInfo[$land['id']] = $land['title'];
        }

        $ticketInfo = [];
        foreach ($ticketRes as $ticket) {
            $ticketInfo[$ticket['id']] = $ticket['title'];
        }

        //dtype详细列表
        $dtypeDetailList = $this->_getDtypeDetailList();

        $resList = [];
        //订单信息汇总
        foreach ($productOrderList as &$orderInfo) {
            $orderId = $orderInfo['realOrderId'];
            if (isset($orderList[$orderId])) {
                $land   = isset($landInfo[$orderList[$orderId]['lid']]) ? $landInfo[$orderList[$orderId]['lid']] : '未知';
                $ticket = isset($ticketInfo[$orderList[$orderId]['tid']]) ? $ticketInfo[$orderList[$orderId]['tid']] : '未知';
                //产品交易标题
                $orderInfo['title'] = $land;
                //产品交易内容
                if ($orderInfo['dtype'] == 0) {
                    //购买产品-记录票类购买信息
                    $orderInfo['context'] = $ticket . '*' . $orderList[$orderId]['origin_num'];
                } else {
                    //修改/取消订单、退款手续费、撤销/撤改订单 不显示具体信息
                    $orderInfo['context'] = '';
                }

            } else {
                $orderInfo['title']   = '';
                $orderInfo['context'] = '';
            }

            //显示操作内容-dtype操作说明
            $orderInfo['action'] = $dtypeDetailList[$orderInfo['dtype']]['comment'];

            //删除无用变量
            unset($orderInfo['dmoney'], $orderInfo['lmoney'], $orderInfo['daction'], $orderInfo['dtype'], $orderInfo['ptype'], $orderInfo['memo'], $orderInfo['realOrderId'], $orderInfo['cat']);

            $resList[$orderInfo['journalid']] = $orderInfo;
        }

        return $resList;
    }

    /**
     * 获得充值记录订单信息
     * @param $params   参数数组
     * @param $payOrderList  充值记录订单列表
     * @return array
     */
    private function _getPayOrderInfoList($params, $payOrderList)
    {
        if (empty($params)) {
            return [];
        }

        if (empty($payOrderList)) {
            return [];
        }
        $accType  = $params['accType'];
        $pay_type = C('pay_type');

        $resList = [];
        foreach ($payOrderList as &$orderInfo) {
            if ($accType == 0) {
                //账户余额，根据ptype显示充值或者扣款方式，eg. 微信 支付宝
                $text = $pay_type[$orderInfo['ptype']][1];
            } else {
                //授信，无需显示方式，只显示充值或者扣款
                $text = '';
            }

            if ($orderInfo['daction'] == 0) {
                $orderInfo['action'] = '充值';
            } else {
                $orderInfo['action'] = '扣款';
            }

            //充值记录标题
            $orderInfo['title']   = $text . $orderInfo['action'];
            $orderInfo['context'] = '';

            //删除无用变量
            unset($orderInfo['dmoney'], $orderInfo['lmoney'], $orderInfo['daction'], $orderInfo['dtype'], $orderInfo['ptype'], $orderInfo['memo'], $orderInfo['realOrderId'], $orderInfo['cat']);

            $resList[$orderInfo['journalid']] = $orderInfo;
        }

        return $resList;
    }

    /**
     * 填充订单列表的订单信息
     * @param $params   参数数组
     * @param $orderList  订单列表
     * @param $orderInfo  订单信息列表
     * @return array
     */
    private function _fillOrderInfo($params, $orderList, $orderInfoList)
    {
        if (empty($params)) {
            return [];
        }

        $action = $params['action'];
        foreach ($orderList as &$olist) {
            $info             = $orderInfoList[$olist['journalid']];
            $olist['title']   = $info['title'];
            $olist['context'] = $info['context'];
            $olist['action']  = $info['action'];
            $timestamp        = strtotime($olist['rectime']);
            $olist['date']    = date('m-d', $timestamp);
            $olist['time']    = date('H:i', $timestamp);

            if ($olist['daction'] == 0) {
                $symbol = '+';
            } else {
                $symbol = '-';
            }
            $olist['fee'] = $symbol . number_format($olist['dmoney'] / 100, 2, '.', '');

            if ($action == 'all') {
                //只有交易记录才显示余额
                $olist['balance'] = '余额：' . number_format($olist['lmoney'] / 100, 2, '.', '') . '元';
            }

            unset($olist['dmoney'], $olist['lmoney'], $olist['rectime'], $olist['dtype'], $olist['ptype'], $olist['memo'], $olist['realOrderId'], $olist['cat']);

        }
        return $orderList;
    }

    /**
     * 获取dtype详细列表
     * @return array
     */
    private function _getDtypeDetailList()
    {
        $item_cat        = C('wx_member_item_category');
        $dtypeDetailList = [];
        foreach ($item_cat as $item) {
            $dtypeDetailList[$item[0]] = ['comment' => $item[1], 'category' => $item[2]];
        }
        return $dtypeDetailList;
    }
}
