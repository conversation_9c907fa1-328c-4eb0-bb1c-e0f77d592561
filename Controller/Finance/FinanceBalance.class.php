<?php
/**
 * 财务平衡
 * <AUTHOR>
 * @date   2018-03-22
 */

namespace Controller\Finance;

use Business\Member\Member;
use Model\Finance\Withdraws;
use Business\Finance\FinanceBalance as FinanceBalanceBiz;

class FinanceBalance extends \Library\Controller
{
    private $_loginInfo;

    public function __construct()
    {
        parent::__construct();
        $this->_loginInfo = $this->getLoginInfo();
    }

    /**
     * 获取某一用户列表
     */
    public function getSingleUserList()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            if ($this->isSuper()) {
                $userId = I('post.user_id', 0, 'int');
                if (empty($userId)) {
                    throw new \Exception('用户ID不能为空');
                }
            } else {
                $userInfo = $this->getLoginInfo();
                $userId   = $userInfo['sid'];
            }

            $date = I('post.date', 0, 'string');

            if (!strtotime($date)) {
                throw new \Exception('日期错误');
            }

            $date        = date('Ymd', strtotime($date));
            $memberModel = new \Model\Member\Member();
            //用户二期 - 信息获取修改 - modification-a
            $MemberBus  = new \Business\Member\Member();
            $memberInfo = $MemberBus->getInfo($userId, true);
            if (!$memberInfo) {
                throw new \Exception('不存在该用户');
            }
            //企业名称
            $comName = isset($memberInfo["com_name"]) ? $memberInfo["com_name"] : '';

            //获取交易情况
            $reportModel = new \Model\Report\FinanceBalance();
            $reportInfo  = $reportModel->getTransInfoByFidAndDate($userId, $date);
            $inCome      = [];
            $outCome     = [];

            $cateGory = load_config('item_category', 'trade_record');
            foreach ($cateGory as $key => $value) {
                $cateGoryTmp[$key] = $value[1];
            }
            $totalInCome  = 0;
            $totalOutCome = 0;

            foreach ($reportInfo as $value) {
                if ($value['action'] == 0) {
                    $inCome[$value['dtype']] = [
                        'money' => $value['dmoney'],
                        'name'  => isset($cateGoryTmp[$value['dtype']]) ? $cateGoryTmp[$value['dtype']] : '',
                        'dtype' => $value['dtype'],
                    ];
                    $totalInCome             += $value['dmoney'];
                } else {
                    $outCome[$value['dtype']] = [
                        'money' => $value['dmoney'],
                        'name'  => isset($cateGoryTmp[$value['dtype']]) ? $cateGoryTmp[$value['dtype']] : '',
                        'dtype' => $value['dtype'],
                    ];
                    $totalOutCome             += $value['dmoney'];
                }
            }

            //获取本期余额和上期余额
            $preLastMoney = 0;
            $nowLastMoney = 0;
            $lastMoney    = $reportModel->getLastTwoMoneyByFidAndDate($userId, $date);
            if (!empty($lastMoney)) {
                $lastDate = $lastMoney[0]['date'];

                $nowLastMoney = $lastMoney[0]['lmoney'];
                if ($lastDate == $date) {
                    $preLastMoney = $lastMoney[1]['lmoney'];
                } elseif ($lastDate < $date) {
                    $preLastMoney = $lastMoney[0]['lmoney'];
                }
            }

            //提现列表
            $data = [
                'com_name'     => $comName,
                'inCome'       => $inCome,
                'outCome'      => $outCome,
                'totalInCome'  => $totalInCome,
                'totalOutCome' => $totalOutCome,
                'preLastMoney' => $preLastMoney,
                'nowLastMoney' => $nowLastMoney,
                'wd_list'      => [],
            ];
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 获取全部列表
     */
    public function getList()
    {
        $code   = 200;
        $msg    = '';
        $return = [];
        try {
            if (!$this->isSuper()) {
                throw new \Exception('非管理员没有权限');
            }

            $date = I('post.date', 0, 'string');
            if (!strtotime($date)) {
                throw new \Exception('日期错误');
            }

            $page   = I('post.page', 1, 'int');
            $userId = I('post.user_id', 0, 'int');

            //获取数据
            $financeModel = new \Model\Report\FinanceBalance();
            $data         = $financeModel->getLmoneyByDate($date, $page, 10, $userId);
            if (empty($data)) {
                throw new \Exception('数据为空');
            }

            //获取总数
            if ($userId) {
                $total = 1;
            } else {
                $total = $financeModel->getLmoneyCountByDate($date);
            }

            //获取本次数据里的用户
            $fidArr = array_column($data, 'fid');

            //本期余额
            foreach ($data as $value) {
                $nowLastMoney[$value['fid']] = $value['lmoney'];
            }

            //本次交易数据
            $data = $financeModel->getFinanceLmoneyByFidAndDate($fidArr, $date);
            if (empty($data)) {
                throw new \Exception('数据为空');
            }

            //交易类型
            $cateGory = load_config('item_category', 'trade_record');
            foreach ($cateGory as $key => $value) {
                $cateGoryTmp[$key] = $value[1];
            }

            //企业名称
            $memberBiz = new Member();
            $memberExt = $memberBiz->getMemberExtListGetFieldToJava($fidArr, 'fid, com_name');
            //用户二期 - 信息获取修改 - modification-a
//            $CustomerBus      = new \Business\Member\Customer();
//            $customerExtList  = $CustomerBus->getCustomerExtListByMemberIdArr($fidArr, 'com_name');
//            $memberExt = [];
//            if(!empty($customerExtList)){
//                foreach ($customerExtList as $k => $v){
//                    $memberExt[$k] = $v["com_name"];
//                }
//            }

            $list = [];
            foreach ($data as $value) {
                if ($value['action'] == 0) {
                    $list[$value['fid']]['inCome'][] = [
                        'money' => $value['dmoney'],
                        'dtype' => $value['dtype'],
                        'name'  => isset($cateGoryTmp[$value['dtype']]) ? $cateGoryTmp[$value['dtype']] : '',
                    ];
                    if (!isset($list[$value['fid']])) {
                        $list[$value['fid']]['totalInCome'] = $value['dmoney'];
                    } else {
                        $list[$value['fid']]['totalInCome'] += $value['dmoney'];
                    }
                } else {
                    $list[$value['fid']]['outCome'][] = [
                        'money' => $value['dmoney'],
                        'dtype' => $value['dtype'],
                        'name'  => isset($cateGoryTmp[$value['dtype']]) ? $cateGoryTmp[$value['dtype']] : '',
                    ];
                    if (!isset($list[$value['fid']])) {
                        $list[$value['fid']]['totalOutCome'] = $value['dmoney'];
                    } else {
                        $list[$value['fid']]['totalOutCome'] += $value['dmoney'];
                    }
                }

                if (!isset($list[$value['fid']]['com_name'])) {
                    //企业名称
                    $comName                         = isset($memberExt[$value['fid']]) ? $memberExt[$value['fid']] : '';
                    $list[$value['fid']]['com_name'] = $comName;
                    $list[$value['fid']]['id']       = $value['fid'];
                }

                if (!isset($list[$value['fid']]['nowLastMoney'])) {
                    //本期余额
                    $list[$value['fid']]['nowLastMoney'] = isset($nowLastMoney[$value['fid']]) ? $nowLastMoney[$value['fid']] : 0;
                }

                if (!isset($list[$value['fid']]['preLastMoney'])) {
                    //上期余额
                    $preInfo                             = $financeModel->getPreLastMoneyByFidAndDate($value['fid'],
                        $date);
                    $list[$value['fid']]['preLastMoney'] = isset($preInfo['lmoney']) ? $preInfo['lmoney'] : 0;
                }
            }

            $return = [
                'list'  => $list,
                'total' => $total,
            ];
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $return, $msg);
    }

    /**
     * 查询配置信息
     * <AUTHOR>
     * @date   2025/03/20
     *
     * @return true|null
     */
    public function queryConfig()
    {
        $res = (new FinanceBalanceBiz())->queryConfig($this->_loginInfo['sid']);

        return $this->apiReturn($res['code'], $res['data'] ?? [], $res['msg'] ?? '');
    }

    /**
     * 收支汇总
     * <AUTHOR>
     * @date   2025/03/24
     *
     * @return true|null
     */
    public function statistics()
    {
        $startDt        = I('post.start_dt', '', 'strval');
        $endDt          = I('post.end_dt', '', 'strval');
        $accountBook    = I('post.account_book', 0, 'intval');
        $twoAccountBook = I('post.two_account_book', 0, 'intval');
        $distributorId  = I('post.distributor_id', 0, 'intval');
        if (!$startDt || !$endDt) {
            return $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        $res = (new FinanceBalanceBiz())->statistics($this->_loginInfo['sid'], $startDt, $endDt, $accountBook,
            $twoAccountBook, $distributorId);

        return $this->apiReturn($res['code'], $res['data'] ?? [], $res['msg'] ?? '');
    }

    /**
     * 明细查询
     * <AUTHOR>
     * @date   2025/03/24
     *
     * @return true|null
     */
    public function details()
    {
        $startDt        = I('post.start_dt', '', 'strval');
        $endDt          = I('post.end_dt', '', 'strval');
        $accountBook    = I('post.account_book', 0, 'intval');
        $twoAccountBook = I('post.two_account_book', 0, 'intval');
        $distributorId  = I('post.distributor_id', 0, 'intval');
        if (!$startDt || !$endDt) {
            return $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        $res = (new FinanceBalanceBiz())->details($this->_loginInfo['sid'], $startDt, $endDt, $accountBook,
            $twoAccountBook, $distributorId);

        return $this->apiReturn($res['code'], $res['data'] ?? [], $res['msg'] ?? '');
    }

    /**
     * 导出明细
     * <AUTHOR>
     * @date   2025/03/20
     *
     * @return true|null
     */
    public function exportDetails()
    {
        $request        = I('post.');
        $financeBalance = new FinanceBalanceBiz();
        $res            = $financeBalance->createExportTask($this->_loginInfo['sid'], $this->_loginInfo['memberID'],
            $request);

        return $this->apiReturn($res['code'], $res['data'] ?? '', $res['msg'] ?? '');
    }
}