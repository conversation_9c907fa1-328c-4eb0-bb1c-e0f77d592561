<?php

namespace Controller\Finance;

use Library\Controller;
use Library\SimpleExcel;
use Business\Finance\TradeQuery as TradeQueryBiz;
use Business\JavaApi\Trade\Query as TradeQueryApi;

class tradeQuery extends Controller {

    private $_tardeQueryBiz;

    private $_memberId;
    private $_loginInfo;

    public function __construct() {
        parent::__construct();

        $this->_memberId = $this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
        $this->_tardeQueryBiz = new TradeQueryBiz();
    }


    /**
     * 交易记录查询配置
     * <AUTHOR>
     * @date   2019-03-08
     */
    public function queryConfig() {
        $result = $this->_tardeQueryBiz->queryConfig($this->_memberId, $this->_loginInfo['saccount']);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取交易记录列表
     * <AUTHOR>
     *
     * @date   2019-03-08
     */
    public function getTradeList() {
        //账本科目类型
        $subjectCode = I('subject_code', 0, 'intval');
        //费用项类型
        $itemCode    = I('item_code', 0, 'intval');
        //开始时间
        $beginTime   = I('begin_time');
        //结束时间
        $endTime     = I('end_time');
        //企业商户id
        $fid         = I('fid', 0, 'intval');
        //对方商户id
        $aid         = I('aid', 0, 'intval');
        //全部/支出/收入
        $tradeType   = I('trade_type', -1, 'intval');
        //内部订单号
        $orderId     = I('order_id', '', 'strval,trim');
        //外部订单号
        $tradeNo     = I('trade_no', '', 'strval,trim');
        //当前页码
        $page        = I('page', 1, 'intval');
        //每页条数
        $size        = I('size', 10, 'intval');
        //查询热数据
        $queryHot    = I('query_hot', 0, 'intval');

        if (!$beginTime || !$endTime) {
            $this->apiReturn(204, '参数错误');
        }

        //假日模式验证
        $this->judgeTradeVacationMode($beginTime, $endTime);

        $option = [];
        if ($fid) $option['fid'] = $fid;
        if ($aid) $option['aid'] = $aid;
        if ($orderId) $option['order_id'] = $orderId;
        if ($tradeNo) $option['trade_no'] = $tradeNo;

        //交易范围可用金额
        $option['tradeScope'] = TradeQueryApi::TRADE_SCOPE_TOTAL;

        $result = $this->_tardeQueryBiz->getTradeList($this->_memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option, $queryHot);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 获取交易记录详情
     * <AUTHOR>
     * @date   2019-03-08
     */
    public function getTradeDetail() {

        $tradeId = I('trade_id', 0, 'intval');

        if (!$tradeId) {
            $this->apiReturn(204, '参数错误');
        }

        $result = $this->_tardeQueryBiz->getTradeDetail($this->_memberId, $tradeId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }

    }

    /**
     * 交易记录统计
     * <AUTHOR>
     * @date   2019-03-11
     */
    public function summary() {
        //账本科目类型
        $subjectCode = I('subject_code', 0, 'intval');
        //费用项类型
        $itemCode    = I('item_code', 0, 'intval');
        //开始时间
        $beginTime   = I('begin_time');
        //结束时间
        $endTime     = I('end_time');
        $fid         = I('fid', 0, 'intval');
        //对方id
        $aid         = I('aid', 0, 'intval');
        //全部/支出/收入
        $tradeType   = I('trade_type', -1, 'intval');
        //内部订单号
        $orderId     = I('order_id', '', 'strval');
        //外部订单号
        $tradeNo     = I('trade_no', '', 'strval');
        //查询热数据
        $queryHot    = I('query_hot', 0, 'intval');

        if (!$beginTime || !$endTime) {
            $this->apiReturn(204, '参数错误');
        }

        $option = [];
        if ($fid) $option['fid'] = $fid;
        if ($aid) $option['aid'] = $aid;
        if ($orderId) $option['order_id'] = $orderId;
        if ($tradeNo) $option['trade_no'] = $tradeNo;

        //交易范围-总金额
        $option['tradeScope'] = TradeQueryApi::TRADE_SCOPE_TOTAL;
        $result = $this->_tardeQueryBiz->summary($this->_memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option, $queryHot);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取用户转换记录
     * <AUTHOR>  Li
     * @date   2020-10-27
     */
    public function getTransFormLog()
    {
        $transFormModel = new \Model\Finance\MemberSpecialAccount();
        $list           = $transFormModel->getTransFormLogByMemberId($this->_memberId);
        if (empty($list)) {
            $this->apiReturn(204, [], '未获取到数据');
        }

        foreach ($list as &$log) {
            $balance = (int)$log['balance']/100;
            $price   = (int)$log['unit_price']/100;
            $log['total_num'] = ceil($balance/$price);
        }

        $this->apiReturn(200, $list, '数据获取成功');
    }

    /**
     * 设置用户转换记录状态
     * <AUTHOR>  Li
     * @date   2020-10-27
     */
    public function setTransFormLogStatus()
    {
        $transFormModel = new \Model\Finance\MemberSpecialAccount();
        $result         = $transFormModel->setTransFormLogStatus($this->_memberId);

        if (!$result) {
            $this->apiReturn(204, [], '数据更新异常');
        }
        $this->apiReturn(200, [], '成功');
    }

    /**
     * 获取可用金额列表
     * <AUTHOR>
     * @date   2024/1/23
     *
     */
    public function getAvailableBalanceList() {
        //费用项类型
        $itemCode    = I('item_code', 0, 'intval');
        //开始时间
        $beginTime   = I('begin_time');
        //结束时间
        $endTime     = I('end_time');
        //全部/支出/收入
        $tradeType   = I('trade_type', -1, 'intval');
        //内部订单号
        $orderId     = I('order_id', '', 'strval,trim');
        //外部订单号
        $tradeNo     = I('trade_no', '', 'strval,trim');
        //当前页码
        $page        = I('page', 1, 'intval');
        //每页条数
        $size        = I('size', 10, 'intval');
        //查询热数据
        $queryHot    = I('query_hot', 0, 'intval');

        if (!$beginTime || !$endTime) {
            $this->apiReturn(204, '参数错误');
        }

        //假日模式验证
        $this->judgeTradeVacationMode($beginTime, $endTime);

        $option = [];
        if ($orderId) $option['order_id'] = $orderId;
        if ($tradeNo) $option['trade_no'] = $tradeNo;

        //交易范围可用金额
        $option['tradeScope'] = TradeQueryApi::TRADE_SCOPE_AVAILABLE;

        //账本只查询平台账户
        $subjectCode = \Library\Constants\Account\BookSubject::PLATFORM_SUBJECT_CODE;
        $result = $this->_tardeQueryBiz->getTradeList($this->_memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option, $queryHot);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(500, [], '接口异常');
    }

    /**
     * 获取冻结金额列表
     * <AUTHOR>
     * @date   2024/1/23
     *
     */
    public function getFrozenBalanceList() {
        //费用项类型
        $itemCode    = I('item_code', 0, 'intval');
        //开始时间
        $beginTime   = I('begin_time');
        //结束时间
        $endTime     = I('end_time');
        //全部/支出/收入
        $tradeType   = I('trade_type', -1, 'intval');
        //内部订单号
        $orderId     = I('order_id', '', 'strval,trim');
        //外部订单号
        $tradeNo     = I('trade_no', '', 'strval,trim');
        //当前页码
        $page        = I('page', 1, 'intval');
        //每页条数
        $size        = I('size', 10, 'intval');
        //查询热数据
        $queryHot    = I('query_hot', 0, 'intval');

        if (!$beginTime || !$endTime) {
            $this->apiReturn(204, '参数错误');
        }

        //假日模式验证
        $this->judgeTradeVacationMode($beginTime, $endTime);

        $option = [];
        if ($orderId) $option['order_id'] = $orderId;
        if ($tradeNo) $option['trade_no'] = $tradeNo;

        //交易范围可用金额
        $option['tradeScope'] = TradeQueryApi::TRADE_SCOPE_FROZEN;

        //账本只查询平台账户
        $subjectCode = \Library\Constants\Account\BookSubject::PLATFORM_SUBJECT_CODE;
        $result = $this->_tardeQueryBiz->getTradeList($this->_memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option, $queryHot);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(500, [], '接口异常');
    }

    /**
     * 可用金额汇总
     * <AUTHOR>
     * @date   2024/1/23
     *
     */
    public function availableBalanceSummary() {
        //费用项类型
        $itemCode    = I('item_code', 0, 'intval');
        //开始时间
        $beginTime   = I('begin_time');
        //结束时间
        $endTime     = I('end_time');
        //全部/支出/收入
        $tradeType   = I('trade_type', -1, 'intval');
        //内部订单号
        $orderId     = I('order_id', '', 'strval');
        //外部订单号
        $tradeNo     = I('trade_no', '', 'strval');
        //查询热数据
        $queryHot    = I('query_hot', 0, 'intval');

        if (!$beginTime || !$endTime) {
            $this->apiReturn(204, '参数错误');
        }

        $option = [];
        if ($orderId) $option['order_id'] = $orderId;
        if ($tradeNo) $option['trade_no'] = $tradeNo;

        //交易范围可用金额
        $option['tradeScope'] = TradeQueryApi::TRADE_SCOPE_AVAILABLE;

        $subjectCode = \Library\Constants\Account\BookSubject::PLATFORM_SUBJECT_CODE;
        $result = $this->_tardeQueryBiz->summary($this->_memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option, $queryHot);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(500, [], '接口异常');
    }

    /**
     * 冻结金额汇总
     * <AUTHOR>
     * @date   2024/1/23
     *
     */
    public function frozenBalanceSummary() {
        //费用项类型
        $itemCode    = I('item_code', 0, 'intval');
        //开始时间
        $beginTime   = I('begin_time');
        //结束时间
        $endTime     = I('end_time');
        //全部/支出/收入
        $tradeType   = I('trade_type', -1, 'intval');
        //内部订单号
        $orderId     = I('order_id', '', 'strval');
        //外部订单号
        $tradeNo     = I('trade_no', '', 'strval');
        //查询热数据
        $queryHot    = I('query_hot', 0, 'intval');

        if (!$beginTime || !$endTime) {
            $this->apiReturn(204, '参数错误');
        }

        $option = [];
        if ($orderId) $option['order_id'] = $orderId;
        if ($tradeNo) $option['trade_no'] = $tradeNo;

        //交易范围可用金额
        $option['tradeScope'] = TradeQueryApi::TRADE_SCOPE_FROZEN;

        $subjectCode = \Library\Constants\Account\BookSubject::PLATFORM_SUBJECT_CODE;
        $result = $this->_tardeQueryBiz->summary($this->_memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option, $queryHot);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(500, [], '接口异常');
    }

    /**
     * 验证交易记录是否为假日模式
     * <AUTHOR>
     * @date   2024/04/23
     *
     * @param string $beginTime 开始时间
     * @param string $endTime 结束时间
     * @param string $identifier 查询标识，默认tradeRecord
     *
     * @return true
     */
    private function judgeTradeVacationMode($beginTime, $endTime, $identifier = 'tradeRecord')
    {
        if (!strtotime($beginTime) || !strtotime($endTime)) {
            return true;
        }

        //查询时间大于14天，则判断是否为假日模式
        $saccount = $this->_loginInfo['saccount'];
        $vacation = (new \Business\PftSystem\VacationModeBiz())->judgeForPage($identifier, $saccount);
        if ($vacation === false) {
            if (strtotime($endTime) - strtotime($beginTime) >= 3600 * 24 * 14) {
                $this->apiReturn(400, [], '假日模式只限制查询14日数据');
            }
        }

        return true;
    }
}