<?php
/**
 * 交易流水相关控制器
 *
 * <AUTHOR>
 * @time 2017-06-12 14:22
 */

namespace Controller\Finance;

use Library\Controller;
use Library\Exception;
use Library\Tools\Helpers;
use Model\Member\Member;
use Model\Member\MemberRelationship;
use Model\TradeRecord\PftRealTimeTransReport;
use Model\TradeRecord\PftTransReport;
use Model\TradeRecord\PftMemberJournal;
use Process\Finance\TradeRecord as TradeProcess;
use Business\Finance\AccountMoney;
use Library\SimpleExcel;
use Business\Member\Member as MemberQueryBiz;
use Library\Cache\Cache;

class TradeRecord extends Controller
{
    private $tradeModel; //交易记录模型
    private $pftTransReport = null; //交易记录汇总模型
    private $transReportBiz = null; //交易记录汇总业务层
    private $memberId;
    private $_isGroup  = 0; //是否是集团账号
    private $loginInfo = null;// 用户登录信息


    public function __construct()
    {
        //统一进行初始化，有接口频率控制的业务
        parent::__construct();

        C(include HTML_DIR . '/Service/Conf/trade_record.conf.php');
        $this->memberId  = $this->isLogin();
        $this->loginInfo = $this->getLoginInfo();

        if ($this->loginInfo['dtype'] == 7) {
            $this->_isGroup = 1;
        }
    }

    private function getPftTransReportModel()
    {
        if ($this->pftTransReport == null) {
            $this->pftTransReport = new \Model\TradeRecord\PftTransReport();
        }

        return $this->pftTransReport;
    }

    private function getPftTransReportBiz()
    {
        if ($this->transReportBiz == null) {
            $this->transReportBiz = new \Business\Finance\TradeRecord();
        }

        return $this->transReportBiz;
    }

    /**
     * 获取交易记录详情
     *
     * @param  string [trade_id]     交易记录id
     * @param  string [orderid]      交易号
     * @param  string [btime]        开始时间         yy-mm-dd hh:ii:ss
     * @param  string [etime]        结束时间         yy-mm-dd hh:ii:ss
     *
     */
    public function getDetails()
    {
        $trade_id = \safe_str(I('trade_id'));

        if (!$trade_id) {
            $this->apiReturn(201, [], '传入参数不合法');
        }
        $fid = (($this->memberId == 1 || $this->_isGroup) && isset($_REQUEST['fid'])) ? intval(I('fid')) : $this->memberId;

        $partner_id = intval(I('partner_id')) ?: 0;

        if ($this->memberId == 1 && !$fid && $partner_id) {
            $this->apiReturn(220, [], '请先选择企业名称');
        }

        //根据传进来的时间和订单号确定数据所在的表
        $queryTimeArr = TradeProcess::parseTime();

        //这两个时间用来确定的数据是在哪个表中
        $startTime = $queryTimeArr[0];
        $endTime   = $queryTimeArr[1];

        //订单号
        $orderId = \safe_str(I('orderid'));

        //判断起始时间是不是在可查询的段内
        $preTmp = TradeProcess::getDataPosition($startTime, $endTime);
        $code   = $preTmp['code'];
        if ($code == 0) {
            $this->apiReturn(202, [], $preTmp['msg']);
        }

        $record = $this->_getTradeModel('slave', $startTime, $endTime)->getDetails($trade_id, $fid, $partner_id);

        //数据处理
        TradeProcess::handleDetailData($record);

        //集团账号查看
        if ($this->_isGroup) {
            $queryParams = [$this->memberId];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberRelationQuery',
                'queryPageGroupSonIds', $queryParams);
            $sonArr      = [$this->memberId];
            if ($queryRes['code'] == 200 && !empty($queryRes['data']['list'])) {
                $sonArr = array_merge($sonArr, $queryRes['data']['list']);
            }
            //$Relation = new MemberRelationship('slave');// new \Model\Member\MemberRelationship();
            //$list     = $Relation->getResellers($this->memberId, 7, true, true, 1, 10);
            //$sonArr   = array_column($list, 'id');
        }

        //无权查看时返回数据为空
        if (isset($record['fid'], $record['aid']) && (
                in_array($this->memberId, [1, $record['fid'], $record['aid']]) ||
                in_array($record['fid'], $sonArr) || in_array($record['aid'], $sonArr)
            )) {
            unset($record['fid'], $record['aid']);
        } else {
            $record = [];
        }

        $this->apiReturn(200, $record, '操作成功');
    }

    /**
     * 获取对方商户信息
     *
     *
     * @param  string  [srch]  查询关键字
     * @param  integer [limit] 单页限制数
     *
     */
    public function getPartner()
    {
        $srch = \safe_str(I('srch'));

        if ($this->memberId == 1) {
            $fid = \safe_str(I('fid'));
            $this->srchMem($srch);
            exit;
        } else {
            $memberId = $this->memberId;
        }
        $limit       = intval(I('limit')) ?: 20;
        $memberModel = new MemberRelationship($memberId);
        $field       = ['distinct m.id as fid', 'm.account', 'm.dname'];
        $data        = $memberModel->getRelevantMerchants($srch, $field, $limit);
        $data        = $data ?: [];

        //兼容分销专员账号名称搜索
        $count = count($data);
        if ($count >= $limit) {
            $this->apiReturn(200, $data, '操作成功');
        }

        $memberInfo = $this->getLoginInfo();
        $multiDist = (new \Business\MultiDist\Member())->getMultiDistCache($memberInfo['sid']);
        if (empty($multiDist['multi_dist']['uuid'])) {
            $this->apiReturn(200, $data, '操作成功');
        }

        $size           = $limit - $count;
        $memberDisModel = new \Model\MultiDist\Member();
        $disChainRes    = $memberDisModel->getChainByUid($memberInfo['sid']);
        if (!$disChainRes) {
            $this->apiReturn(200, $data, '操作成功');
        }

        //处于运营商身份时，可选项有所有下级专员
        //处于专员身份时，可选项只能有运营商，不能搜到其他同团队专员
        //既有专员身份，又有运营商身份时，取并集
        $topIds = $distIdsTmp = $distIds = [];
        foreach ($disChainRes as $val) {
            if ($val['level'] == $memberDisModel::TOP_LEVEL) {
                $topIds[] = $val['top_uid'];
            } else {
                $distIdsTmp[] = $val['top_uid'];
            }

        }
        $fids = array_column($data, 'fid');
        if (!empty($distIdsTmp)) {
            foreach ($distIdsTmp as $key => $tmp) {
                if (!in_array($tmp, $fids)) {
                    $distIds[] = $tmp;
                }
            }
        }
        $userList = $memberDisModel->getMemberInfoByDname($srch, $topIds, 1, $size, $fids, $distIds);
        if (empty($userList)) {
            $this->apiReturn(200, $data, '操作成功');
        }
        foreach ($userList as $value) {
            $data[] = [
                'fid'     => $value['uid'],
                'account' => $value['account'],
                'dname'   => $value['dname'],
            ];
        }

        $this->apiReturn(200, $data, '操作成功');
    }

    /**
     * 管理员模糊搜索会员
     *
     * @param  string [srch]       会员名称/会员id/会员账号
     * @param  string [ptypes]     交易账户类型                0-查看当前用户授信; 1-查看分销商授信
     */
    public function srchMem($srch = null)
    {
        if (!$srch) {
            $this->memberId = $this->isLogin('ajax');
            $srch           = \safe_str(I('srch'));
        }

        $data = [];

        try {
            if (empty($srch)) {
                $this->apiReturn(200, [], '查询结果为空');
            }

            if ($this->memberId != 1 && !$this->_isGroup) {
                throw new Exception('没有查询权限', 210);
            }

            //显示条目数
            $limit = 10;

            if ($this->_isGroup == 1) {
                $dname   = '';
                $letters = '';
                $mobile  = '';
                $account = '';
                if (is_numeric($srch) && strlen($srch) < 11) {
                    $srch    = str_replace("\_", "", $srch);
                    $account = $srch;
                } else {
                    $dname = $srch;
                }

                $queryParams = [$this->memberId, $dname, $letters, $mobile, $account, -1, 1, $limit];
                $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberRelationQuery',
                    'queryPageGroupSonIds', $queryParams);

                $data = [];
                if ($queryRes['code'] == 200 && !empty($queryRes['data']['list'])) {
                    $sonArr      = [$this->memberId];
                    $sonArr      = array_merge($sonArr, $queryRes['data']['list']);
                    $queryParams = [$sonArr];
                    $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery',
                        'batchMemberInfoByIds',
                        $queryParams);
                    if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                        foreach ($queryRes['data'] as $memberInfo) {
                            $data[] = [
                                'fid'     => $memberInfo['id'],
                                'account' => $memberInfo['account'],
                                'dname'   => $memberInfo['dname'],
                            ];
                        }
                    }
                }

                ////集团账号查询
                //$relationModel = new MemberRelationship();
                //$list          = $relationModel->getResellers($this->_groupId, 7, true, true, 1, $limit, $srch);
                //
                //$data = [];
                //foreach ($list as $item) {
                //    $srch[] = [
                //        'fid'     => $item['id'],
                //        'dname'   => $item['dname'],
                //        'account' => $item['account'],
                //    ];
                //}
            } else {
                //管理员账号查询
                //$memberModel = new Member();
                //$data        = $memberModel->searchForReport($srch, $limit);
                $memberApi = new MemberQueryBiz();
                $memberRes = $memberApi->getInfoByComName($srch);
                if ($memberRes['code'] == 200) {
                    foreach ($memberRes['data'] as $tmpMember) {
                        $data[] = [
                            'fid'     => $tmpMember['memberInfo']['id'],
                            'account' => $tmpMember['memberInfo']['account'],
                            'dname'   => $tmpMember['memberExtInfo']['com_name'],
                        ];
                    }
                }
            }

            $data = is_array($data) ? $data : [];

            if ($this->isSuper()) {
                if (stripos($srch, '票付通') !== false) {
                    array_unshift($data, ['fid' => 1, 'account' => 'admin', 'dname' => '票付通信息科技']);
                }
            }

            $this->apiReturn(200, $data, '操作成功');

        } catch (Exception $e) {
            \pft_log('trade_record/err', 'srch_mem|' . $e->getCode() . "|" . $e->getMessage(), 'month');
            $this->apiReturn($e->getCode(), [], $e->getMessage());
        }
    }

    /**
     * 交易记录汇总查询接口
     *
     * <AUTHOR>
     */
    public function getListForTradeRecord($flag = '')
    {
        try {
            $flag = I('flag', 0);
            //搜索方式 0账户汇总 1 交易类型
            $searchType = I('search_type') ? '1' : '0';
            //fid
            $fid = ($this->memberId == 1 && !empty($_REQUEST['searchFid'])) ? intval($_REQUEST['searchFid']) : $this->memberId;
            //起止时间
            $btime = I('btime');
            $etime = I('etime');
            //测试数据
            // $fid = 3385;

            $btime = (int)date('Ymd', strtotime($btime));
            $etime = (int)date('Ymd', strtotime($etime));

            $res = $this->getPftTransReportBiz()->getTransRecordSummary($this->memberId, $fid, $btime, $etime,
                $searchType);

            if ($flag) {
                return $res['data'];
            } else {
                $this->apiReturn($res['code'], $res['data'], $res['msg']);
            }
        } catch (Exception $e) {
            \pft_log('trade_record/err', 'getListForTradeRecord|' . $e->getCode() . "|" . $e->getMessage(), 'month');
            $this->apiReturn($e->getCode(), [], $e->getMessage());
        }
    }

    /**
     * 获取交易明细
     *
     * <AUTHOR>
     */
    public function getListDetail()
    {
        try {
            //是否导出成excel
            $flag = I('flag', 0);
            //搜索方式 账户类型=0 交易明细类型=1
            $searchType = I('search_type') ? '1' : '0';
            //是否是管理员  并能查看指定商户
            $fid = $this->memberId;
            //起止时间
            $btime = I('btime');
            $etime = I('etime');
            $page  = I('page', 1);
            $size  = I('size', 10);

            $subjectBooks = I('book_subject', '', 'strval');    //多个以逗号隔开
            $templateItem = I('template_item', '', 'strval');   //多个以逗号隔开

            $btime = (int)date('Ymd', strtotime($btime));
            $etime = (int)date('Ymd', strtotime($etime));

            $res = $this->getPftTransReportBiz()->getTransRecordDetail($this->memberId, $fid, $btime,
                $etime, $searchType, $subjectBooks, $templateItem, $page, $size, $flag);
            if (!$flag) {
                $this->apiReturn(200, $res['data']);
            } else {
                return $res['data'];
            }
        } catch (Exception $e) {
            \pft_log('trade_record/err', 'getListForTradeRecord|' . $e->getCode() . "|" . $e->getMessage(), 'month');
            $this->apiReturn($e->getCode(), [], $e->getMessage());
        }
    }

    /**
     * 获取交易记录模型
     *
     * @param  string  $defaltDb
     * @param  bool|string  $startTime  开始时间
     * @param  bool|string  $endTime  截止时间
     *
     * @return \Model\Finance\TradeRecord
     */
    private function _getTradeModel($defaltDb = 'slave', $startTime = false, $endTime = false)
    {
        if (null === $this->tradeModel) {
            $this->tradeModel = new \Model\Finance\TradeRecord($defaltDb, $startTime, $endTime);
            //设置集团账号标识
            if ($this->_isGroup) {
                $this->tradeModel->setGroupIdentify($this->memberId);
            }
        }

        return $this->tradeModel;
    }

    /**
     * 按指定格式和指定顺序重排数组
     *
     * @param  array  $data  数据
     * @param  array  $format  格式
     *
     * @return  array
     */
    public static function array_recompose(array $data, array $format)
    {
        $format_data = array_fill_keys($format, '');
        foreach ($data as $key => $value) {
            if (!in_array($key, $format)) {
                continue;
            }
            $format_data[$key] = $data[$key];
        }

        return $format_data;
    }

    /**
     * 判断是不是管理员登录
     *
     */
    public function isMaster()
    {
        echo json_encode(array('code' => 1));
    }

    public function convert($size)
    {
        $unit = array('b', 'kb', 'mb', 'gb', 'tb', 'pb');

        return @round($size / pow(1024, ($i = floor(log($size, 1024)))), 2) . ' ' . $unit[$i];
    }

    /**
     * 交易汇总导出成excel
     *
     * <AUTHOR>
     */
    public function exportExcelTrade()
    {
        $totalData  = $this->getListForTradeRecord();
        $detailData = $this->getListDetail();
        Helpers::composerAutoload();
        $objPHPExcel = new \PHPExcel();//load_excel();
        $objWriter   = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objPHPExcel->setActiveSheetIndex(0);

        $searchType = I('search_type') ? '1' : '0';
        if ($searchType == 0) {
            $objPHPExcel->getActiveSheet()->setTitle('账户类型汇总');
        } else {
            $objPHPExcel->getActiveSheet()->setTitle('交易类型汇总');
        }

        $objPHPExcel->getActiveSheet()->mergeCells('C1:D1');
        $objPHPExcel->getActiveSheet()->mergeCells('I1:J1');
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(20);
        $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(30);
        $objPHPExcel->getActiveSheet()->getColumnDimension('K')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('L')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('M')->setWidth(20);
        $objPHPExcel->getActiveSheet()->getColumnDimension('N')->setWidth(20);
        $objPHPExcel->getActiveSheet()->setCellValue('A2', '日期');
        $objPHPExcel->getActiveSheet()->setCellValue('B2', ' ');
        $objPHPExcel->getActiveSheet()->setCellValue('B1', '总收入');
        $objPHPExcel->getActiveSheet()->setCellValue('B1', '总收入');
        $objPHPExcel->getActiveSheet()->setCellValue('C1', $totalData['income'] / 100);
        $objPHPExcel->getActiveSheet()->setCellValue('E1', '总支出');
        $objPHPExcel->getActiveSheet()->setCellValue('F1', $totalData['expense'] / 100);
        $objPHPExcel->getActiveSheet()->setCellValue('I1', '单位:元');

        $lineArr = array(
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',
        );

        $cacheConfig = (new \Business\Finance\TradeRecord())->getCacheConfig($this->memberId);

        if (isset($detailData['account']) && $detailData['account']) {
            //获取出对应用户的缓存配置
            $bookSubjectCodeArr  = $cacheConfig['book_subject'];
            $subjectBookMap      = array_column(load_config('book_subject', 'account_book'), 'name', 'code');
            $excelAccountTypeNum = count($bookSubjectCodeArr);

            $i = 0;
            foreach ($lineArr as $k => $v) {
                if ($i == 0 || $i == 1) {
                    //跳过头两个 放置日期
                    $i++;
                    continue;
                }

                if ($i - 2 > $excelAccountTypeNum) {
                    break;
                }
                $objPHPExcel->getActiveSheet()->setCellValue($v . '2', $subjectBookMap[$bookSubjectCodeArr[$i - 2]]);
                $i++;
            }

            foreach ($detailData['account'] as $time => $list) {
                foreach ($bookSubjectCodeArr as $key => $subjectCode) {
                    if (isset($list[$subjectCode])) {
                        $incomeArr[$key]  = $list[$subjectCode]['income'] / 100;
                        $expenseArr[$key] = $list[$subjectCode]['expense'] / 100;
                    } else {
                        $incomeArr[$key]  = 0;
                        $expenseArr[$key] = 0;
                    }
                }
                $incomeArr['time']  = $time;
                $expenseArr['time'] = $time;

                $excelData[] = $incomeArr;
                $excelData[] = $expenseArr;
                unset($incomeArr);
                unset($expenseArr);
            }

            unset($k);
            unset($v);
            unset($key);
            unset($val);

            $i = 3;
            foreach ($excelData as $k => $v) {
                $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $v['time']);
                if ($k % 2 == 0) {
                    $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, "收入");
                } else {
                    $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, "支出");
                }
                foreach ($lineArr as $key => $val) {
                    if ($key == 0 || $key == 1) {
                        continue;
                    }
                    $objPHPExcel->getActiveSheet()->setCellValue($val . $i, $v[$key - 2]);
                }
                $i++;
            }
            unset($excelData);
        } elseif (isset($detailData['category']) && $detailData['category']) {

            $templateItemArr = $cacheConfig['template_item'];
            $templateItemMap = array_column(load_config('template_item', 'account_book'), 'name', 'code');
            $categoryNum     = count($templateItemArr);

            $i = 0;
            foreach ($lineArr as $k => $v) {
                if ($i == 0 || $i == 1) {
                    //跳过头两个 放置日期
                    $i++;
                    continue;
                }
                if ($i - 2 > $categoryNum) {
                    break;
                }

                $objPHPExcel->getActiveSheet()->setCellValue($v . '2', $templateItemMap[$templateItemArr[$i - 2]]);
                $i++;
            }

            foreach ($detailData['category'] as $time => $list) {
                foreach ($templateItemArr as $key => $templateItem) {
                    if (isset($list[$templateItem])) {
                        $incomeArr[$key]  = $list[$templateItem]['income'] / 100;
                        $expenseArr[$key] = $list[$templateItem]['expense'] / 100;
                    } else {
                        $incomeArr[$key]  = 0;
                        $expenseArr[$key] = 0;
                    }
                }
                $incomeArr['time']  = $time;
                $expenseArr['time'] = $time;


                $excelData[] = $incomeArr;
                $excelData[] = $expenseArr;
                unset($incomeArr);
                unset($expenseArr);
            }
            unset($k);
            unset($v);
            unset($key);
            unset($val);
            $i = 3;

            foreach ($excelData as $k => $v) {
                $objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $v['time']);
                if ($k % 2 == 0) {
                    $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, "收入");
                } else {
                    $objPHPExcel->getActiveSheet()->setCellValue('B' . $i, "支出");
                }
                foreach ($lineArr as $key => $val) {
                    if ($key == 0 || $key == 1) {
                        continue;
                    }
                    $objPHPExcel->getActiveSheet()->setCellValue($val . $i, $v[$key - 2]);
                }
                $i++;
            }
            unset($excelData);
        }

        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");
        header('Content-Disposition:attachment;filename="交易记录汇总.xls"');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');
    }

    public function exportExcelBalance($data, $sum)
    {
        $lineArr = ['A', 'B', 'C', 'D'];

        $objPHPExcel = new \PHPExcel();//load_excel();
        $objWriter   = new \PHPExcel_Writer_Excel5($objPHPExcel);
        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getActiveSheet()->setTitle('账户余额查询');
        //$objPHPExcel->getActiveSheet()->mergeCells('C1:D1');
        //$objPHPExcel->getActiveSheet()->mergeCells('I1:J1');
        $objPHPExcel->getActiveSheet()->setCellValue('A1', '总商户数');
        $objPHPExcel->getActiveSheet()->setCellValue('B1', $sum['shopSum']);
        $objPHPExcel->getActiveSheet()->setCellValue('C1', '总余额');
        $objPHPExcel->getActiveSheet()->setCellValueExplicit('D1', $sum['lmoneySum']);
        $objPHPExcel->getActiveSheet()->setCellValue('A2', '商户ID');
        $objPHPExcel->getActiveSheet()->setCellValue('B2', '企业名称');
        $objPHPExcel->getActiveSheet()->setCellValue('C2', '账户名称');
        $objPHPExcel->getActiveSheet()->setCellValue('D2', '账户余额');

        $i = 3;
        foreach ($data as $value) {
            foreach ($lineArr as $k => $v) {
                if ($k == 0) {
                    $objPHPExcel->getActiveSheet()->setCellValue($v . $i, $value['fid']);
                    continue;
                }
                if ($k == 1) {
                    $objPHPExcel->getActiveSheet()->setCellValue($v . $i, $value['com_name']);
                    continue;
                }
                if ($k == 2) {
                    $objPHPExcel->getActiveSheet()->setCellValue($v . $i, $value['dname']);
                    continue;
                }
                if ($k == 3) {
                    $objPHPExcel->getActiveSheet()->setCellValueExplicit($v . $i, $value['lmoney']);
                    continue;
                }
            }
            $i++;
        }

        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");
        header('Content-Disposition:attachment;filename="账户余额.xls"');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');
    }

    /**
     * 导出成Excel文件
     *
     * @param $recodes 日志数据
     */
    public function exportExcel($recodes)
    {
        $filename                    = date('YmdHis') . '欠费用户记录';
        $Excel                       = [];
        $line                        = 0;
        $Excel[$line]['fid']         = '商户id';
        $Excel[$line]['name']        = '账户名称';
        $Excel[$line]['comName']     = '公司名称';
        $Excel[$line]['amoney']      = '账户余额';
        $Excel[$line]['arrearstime'] = '欠费日期';
        $Excel[$line]['lasttime']    = '还款日期';
        if ($recodes) {
            foreach ($recodes as $row) {
                $line++;
                $Excel[$line]['fid']         = $row['fid'];
                $Excel[$line]['name']        = $row['name'];
                $Excel[$line]['comName']     = $row['comName'];
                $Excel[$line]['amoney']      = $row['amoney'];
                $Excel[$line]['arrearstime'] = $row['arrearstime'];
                $Excel[$line]['lasttime']    = $row['lasttime'];

            }
        }
        $xls = new SimpleExcel('UTF-8', true, '欠费用户记录');
        $xls->addArray($Excel);
        $xls->generateXML($filename);
        exit;
    }

    /*
     * 获取交易记录统计汇总
     *
     * @param   $bTime   开始时间
     * @param   $eTime   结束时间
     * @param   $reseller_id  分销商ID
     * @param   $ignoreType   过滤类型 false 不过滤   1正式账户  2测试账户
     *
     * <AUTHOR>
     * @since  2016-09-18
     */
    public function getRecordCountInfo()
    {

        $bTime = I('bTime', false);
        $eTime = I('eTime', false);

        $resellerId = I('reseller_id', false);
        $ignoreType = I('ignoreType', false);
        $page       = I('page', 1);
        $pageSize   = I('pageSize', 15);

        if (!strtotime($bTime) || !strtotime($eTime)) {
            exit(json_encode(['code' => 2, 'msg' => '请输入正确的时间格式']));
        }

        if (empty($resellerId)) {
            $testFid = load_config('test_uids', 'account');
            $testFid = explode(",", $testFid);

            $fidArr = $this->getPftTransReportModel()->getAllFidInRecord();
            if ($fidArr === false) {
                exit(json_encode(['code' => 0, 'data' => [], "msg" => "获取用户信息失败"]));
            }

            $tmpFidArr = array_column($fidArr, 'fid');

            if ($ignoreType == 1) {
                //取正式账户
                $tmpFidArr = array_diff($tmpFidArr, $testFid);
            } elseif ($ignoreType == 2) {
                //取测试账户
                $tmpFidArr = array_intersect($tmpFidArr, $testFid);
            }

            //总数
            $total      = count($tmpFidArr);
            $offset     = ($page - 1) * $pageSize;
            $resellerId = array_splice($tmpFidArr, $offset, $pageSize);
        } else {
            $resellerId = [$resellerId];
            //总数
            $total = 1;
        }

        $data = $this->getPftTransReportModel()->getRecordCountInfo($bTime, $eTime, $resellerId);

        if (!is_array($data)) {
            exit(json_encode(['code' => 2, 'msg' => '查询出错']));
        }

        foreach ($data as $key => $item) {
            $dataRes[$item['fid']] = $item;
        }
        unset($data);

        //获取起初余额和期末余额
        $bTime = date('Y-m-d', strtotime($bTime) - 3600 * 24);

        $bLmoneyRes = $this->getPftTransReportModel()->getLmoneyByFid($resellerId, $bTime);
        if ($bLmoneyRes === false) {
            exit(json_encode(['code' => 2, 'msg' => '查询余额出错']));
        }
        foreach ($bLmoneyRes as $item) {
            $bLmoney[$item['fid']] = $item;
        }

        $eLmoneyRes = $this->getPftTransReportModel()->getLmoneyByFid($resellerId, $eTime);
        if ($eLmoneyRes === false) {
            exit(json_encode(['code' => 2, 'msg' => '查询余额出错']));
        }
        foreach ($eLmoneyRes as $item) {
            $eLmoney[$item['fid']] = $item;
        }

        //获取商户名称
        $memberModel     = new Member();
        $memberBiz       = new \Business\Member\Member();
        $nameResMerchant = $memberModel->getMemberInfoByMulti($resellerId, 'id', 'id, dname');
        $nameRes         = $memberBiz->getInfoMemberExtListFromJava($resellerId, 'fid, com_name');

        $nameInfo = [];
        foreach ((array)$nameResMerchant as $key => $item) {
            if (!isset($item['id']) || !isset($item['dname'])) {
                continue;
            }
            $nameInfo[$item['id']]['dname'] = $item['dname'];
        }
        foreach ((array)$nameRes as $key => $item) {
            if (empty($item['fid']) || empty($item['com_name'])) {
                continue;
            }
            $nameInfo[$item['fid']]['com_name'] = $item['com_name'];
        }

        unset($nameRes);
        unset($nameResMerchant);

        foreach ($nameInfo as $key => $item) {

            $tmpData = isset($dataRes[$key]) ? $dataRes[$key] : '';

            if ($tmpData) {
                $res[$key]['income']  = round($tmpData['income'] / 100, 2);
                $res[$key]['expense'] = round($tmpData['expense'] / 100, 2);
                $res[$key]['blmoney'] = round($bLmoney[$key]['lmoney'] / 100, 2);
                $res[$key]['elmoney'] = round($eLmoney[$key]['lmoney'] / 100, 2);
            } else {
                $res[$key]['income']  = 0;
                $res[$key]['expense'] = 0;
                $res[$key]['blmoney'] = round($bLmoney[$key]['lmoney'] / 100, 2);
                $res[$key]['elmoney'] = round($eLmoney[$key]['lmoney'] / 100, 2);
            }
            $res[$key]['name']     = $item['dname'];
            $res[$key]['com_name'] = $item['com_name'];

        }

        echo json_encode(['code' => 0, 'list' => $res, 'total' => $total]);
    }

    /**
     * 获取交易记录统计汇总新版
     * <AUTHOR>
     * @since  2020-05-20
     */
    public function getRecordCountInfoNew()
    {
        $bTime      = I('get.bTime', false);
        $eTime      = I('get.eTime', false);
        $resellerId = I('get.reseller_id', 0);
        $ignoreType = I('get.ignoreType', 0);
        $page       = I('get.page', 1);
        $pageSize   = I('get.pageSize', 3);
        $dtype      = I('get.dtype', -1);
        if (!strtotime($bTime) || !strtotime($eTime)) {
            $this->apiReturn(204, [], '时间错误');
        }
        $tradeBiz = new \Business\Finance\TradeQuery();
        $res      = $tradeBiz->getTradeCountInfoService($bTime, $eTime, $dtype, $ignoreType, $resellerId, $page,
            $pageSize);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 获取交易记录统计详情新版
     * <AUTHOR>
     * @since  2020-05-20
     */
    public function getRecordCountInfoDetailNew()
    {
        $bTime      = I('get.bTime', false);
        $eTime      = I('get.eTime', false);
        $resellerId = I('get.reseller_id', 0);
        if (!strtotime($bTime) || !strtotime($eTime)) {
            $this->apiReturn(204, [], '请输入正确的时间格式');
        }
        if (!$resellerId) {
            $this->apiReturn(204, [], '用户不能为空');
        }
        $tradeBiz = new \Business\Finance\TradeQuery();
        $res      = $tradeBiz->getTradeCountInfoDetailService($bTime, $eTime, $resellerId);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 获取交易记录统计的详细信息
     *
     * @param   $bTime   开始时间
     * @param   $eTime   结束时间
     * @param   $reseller_id  分销商ID
     * @param
     * @param
     */
    public function getRecordCountDetail()
    {
        $bTime      = I('bTime', false);
        $eTime      = I('eTime', false);
        $resellerId = I('reseller_id', false);
        if (!strtotime($bTime) || !strtotime($eTime)) {
            exit(json_encode(['code' => 2, 'msg' => '请输入正确的时间格式']));
        }

        if (empty($resellerId)) {
            exit(json_encode(['code' => 2, 'msg' => '分销商ID不能为空']));
        }

        $res = $this->getPftTransReportModel()->getRecordCountDetail($bTime, $eTime, $resellerId);

        $pftMemberJournal = new PftMemberJournal();

        $field      = 'fid, dtype, sum(dmoney) as money';
        $resBalance = $pftMemberJournal->getSpecialByTime($resellerId, $bTime, $eTime, $field);

        if (empty($res) && empty($resBalance)) {
            exit(json_encode(['code' => 0]));
        }

        $nameInfo = load_config('item_category', 'trade_record');
        if ($res) {
            foreach ($res as $item) {
                if (!isset($data[$item['fid']][$item['dtype']]['income'])) {
                    $data[$item['fid']][$item['dtype']]['income'] = 0;
                }
                if (!isset($data[$item['fid']][$item['dtype']]['expense'])) {
                    $data[$item['fid']][$item['dtype']]['expense'] = 0;
                }

                $data[$item['fid']][$item['dtype']]['income']  += round($item['income'] / 100, 2);
                $data[$item['fid']][$item['dtype']]['expense'] += round($item['expense'] / 100, 2);
                $data[$item['fid']][$item['dtype']]['name']    = isset($nameInfo[$item['dtype']][1]) ? $nameInfo[$item['dtype']][1] : '未知';
            }
        }

        if ($resBalance) {
            foreach ($resBalance as $value) {
                $data[$value['fid']][$value['dtype'] . '_i']['income']  = 0;
                $data[$value['fid']][$value['dtype'] . '_i']['expense'] = round($value['money'] / 100, 2);
                $data[$value['fid']][$value['dtype'] . '_i']['name']    = isset($nameInfo[$value['dtype']][1]) ? $nameInfo[$value['dtype']][1] . '(专项款)' : '未知';

            }

        }

        echo json_encode(['code' => 0, 'res' => $data]);
    }

    /**
     * 获取默认账本类型及交易类型配置项
     * <AUTHOR> Li
     * @date  2021-03-16
     */
    public function getTransationInfo()
    {
        $subjectBooks = load_config('book_subject', 'account_book');
        $templateItem = load_config('template_item', 'account_book');

        $return = [
            'book_subject'  => $subjectBooks,
            'template_item' => $templateItem,
        ];

        $this->apiReturn(200, $return, '配置获取成功');
    }

    /**
     * 设置交易汇总类型
     * <AUTHOR> Li
     * @date  2021-03-16
     */
    public function saveTransationConfig()
    {
        $subjectBooks = I('book_subject', '', 'strval');    //多个以逗号隔开
        $templateItem = I('template_item', '', 'strval');   //多个以逗号隔开

        if (!$subjectBooks && !$templateItem) {
            $this->apiReturn(204, [], '配置有误，请确认');
        }

        $data = [
            'book_subject'  => $subjectBooks,
            'template_item' => $templateItem,
        ];

        $key   = "cache:transation_config:{$this->loginInfo['memberID']}";
        $cache = Cache::getInstance('redis');

        //将查到的数据记录到缓存中
        $request = $cache->hMSet($key, $data);
        //有效期设置为365天
        $expireTime = 60 * 60 * 24 * 365;
        $cache->expire($key, $expireTime);
        if ($request) {
            $this->apiReturn(200, [], '保存成功');
        }

        $this->apiReturn(204, [], '保存失败');
    }

    /**
     * 获取交易汇总类型
     * <AUTHOR> Li
     * @date  2021-03-16
     */
    public function getTransationConfig()
    {
        $key      = "cache:transation_config:{$this->loginInfo['memberID']}";
        $cache    = Cache::getInstance('redis');
        $getField = ['book_subject', 'template_item'];

        $baseSubjectBooks = load_config('book_subject', 'account_book');
        $baseTemplateItem = load_config('template_item', 'account_book');

        $return = [
            'book_subject'  => array_slice(array_column($baseSubjectBooks, 'code'), 0, 5),
            'template_item' => array_slice(array_column($baseTemplateItem, 'code'), 0, 10),
        ];

        $request = $cache->hMGet($key, $getField);
        if ($request['book_subject']) {
            $subjectBooks = explode(',', $request['book_subject']);
        } else {
            $subjectBooks = $return['book_subject'];
        }

        if ($request['template_item']) {
            $templateItem = explode(',', $request['template_item']);
        } else {
            $templateItem = $return['template_item'];
        }

        if (!$request['book_subject'] && !$request['template_item']) {
            $key   = "cache:transation_config:{$this->loginInfo['memberID']}";
            $cache = Cache::getInstance('redis');

            $data = [
                'book_subject'  => implode(',', $return['book_subject']),
                'template_item' => implode(',', $return['template_item']),
            ];

            //将查到的数据记录到缓存中
            $cache->hMSet($key, $data);
            //有效期设置为365天
            $expireTime = 60 * 60 * 24 * 365;
            $cache->expire($key, $expireTime);
        }

        $return = [
            'base_book_subject'  => $baseSubjectBooks,
            'base_template_item' => $baseTemplateItem,
            'book_subject'       => $subjectBooks,
            'template_item'      => $templateItem,
        ];

        $this->apiReturn(200, $return, '获取成功');
    }

    /**
     * 获取交易汇总类型-新
     * 交易类型不含：
     * {code: 1105, name: "核销收入"}
     * {code: 1102, name: "历史冻结"}
     * <AUTHOR>
     * @date   2024/03/26
     *
     */
    public function getTransationConfigNew()
    {
        $key      = "cache:transation_config:{$this->loginInfo['memberID']}";
        $cache    = Cache::getInstance('redis');
        $getField = ['book_subject', 'template_item'];

        $baseSubjectBooks = load_config('book_subject', 'account_book');
        $baseTemplateItem = load_config('template_item', 'account_book');

        foreach ($baseTemplateItem as $templateItemKey => $templateItemVal) {
            if (!in_array($templateItemVal['code'], [1105, 1102])) {
                continue;
            }

            unset($baseTemplateItem[$templateItemKey]);
        }
        $baseTemplateItem = array_values($baseTemplateItem);

        $return = [
            'book_subject'  => array_slice(array_column($baseSubjectBooks, 'code'), 0, 5),
            'template_item' => array_slice(array_column($baseTemplateItem, 'code'), 0, 10),
        ];

        $request = $cache->hMGet($key, $getField);
        if ($request['book_subject']) {
            $subjectBooks = explode(',', $request['book_subject']);
        } else {
            $subjectBooks = $return['book_subject'];
        }

        if ($request['template_item']) {
            $templateItem = explode(',', $request['template_item']);
        } else {
            $templateItem = $return['template_item'];
        }

        if (!$request['book_subject'] && !$request['template_item']) {
            $key   = "cache:transation_config:{$this->loginInfo['memberID']}";
            $cache = Cache::getInstance('redis');

            $data = [
                'book_subject'  => implode(',', $return['book_subject']),
                'template_item' => implode(',', $return['template_item']),
            ];

            //将查到的数据记录到缓存中
            $cache->hMSet($key, $data);
            //有效期设置为365天
            $expireTime = 60 * 60 * 24 * 365;
            $cache->expire($key, $expireTime);
        }

        $return = [
            'base_book_subject'  => $baseSubjectBooks,
            'base_template_item' => $baseTemplateItem,
            'book_subject'       => $subjectBooks,
            'template_item'      => $templateItem,
        ];

        $this->apiReturn(200, $return, '获取成功');
    }

    /**
     * 获取交易汇总列表
     * <AUTHOR>
     * @date  2021-03-16
     */
    public function sumTradeRecordList()
    {
        $searchType = I('type', 0, 'intval');
        $outZero    = I('out_zero', 0, 'intval');
        $startTime  = I('start_time', 0, 'intval');
        $endTime    = I('end_time', 0, 'intval');
        $size       = I('size', 10, 'intval');
        $pageSize   = I('page', 1, 'intval');
        $fid        = $this->loginInfo['memberID'];
        $sid        = $this->loginInfo['sid'];

        //假日模式验证
        $this->judgeTradeVacationMode($startTime, $endTime);

        $result = (new \Business\Finance\TradeRecord())->getTransRecordDetailNew($fid, $sid, $outZero,$startTime, $endTime,$searchType,'', '',$pageSize,$size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取交易汇总数据
     * <AUTHOR>
     * @date  2021-03-16
     */
    public function sumTransRecordDetailNew()
    {
        $searchType = I('type', 0, 'intval');
        $startTime  = I('start_time', 0, 'intval');
        $endTime    = I('end_time', 0, 'intval');

        $fid        = $this->loginInfo['memberID'];
        $sid        = $this->loginInfo['sid'];

        //假日模式验证
        $this->judgeTradeVacationMode($startTime, $endTime);

        $result = (new \Business\Finance\TradeRecord())->sumTransRecordDetailNew($fid, $sid, $startTime, $endTime,$searchType);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 验证交易记录是否为假日模式
     * <AUTHOR>
     * @date   2024/04/23
     *
     * @param string $beginTime 开始时间
     * @param string $endTime 结束时间
     * @param string $identifier 查询标识，默认tradeRecord
     *
     * @return true
     */
    private function judgeTradeVacationMode($beginTime, $endTime,  $identifier = 'tradeRecord')
    {
        if (!strtotime($beginTime) || !strtotime($endTime)) {
            return true;
        }

        //查询时间大于14天，则判断是否为假日模式
        $saccount = $this->loginInfo['saccount'];
        $vacation = (new \Business\PftSystem\VacationModeBiz())->judgeForPage($identifier, $saccount);
        if ($vacation === false) {
            if (strtotime($endTime) - strtotime($beginTime) >= 3600 * 24 * 14) {
                $this->apiReturn(400, [], '假日模式只限制查询14日数据');
            }
        }

        return true;
    }
}
