<?php
/**
 * 专款专用账户
 *
 * <AUTHOR>
 * @date   2018-1-23
 */

namespace Controller\Finance;

use Business\JavaApi\Account\AccountBook;
use Business\JsonRpcApi\PayService\UnifiedPay;
use Library\Business\WePay\WxPayNotifyResponse;
use Library\Constants\Account\BookSubject;
use Model\Finance\Deposite;
use Model\AppCenter\ModuleList;
use Model\TradeRecord\OnlineTrade;
use Business\Finance\ExclusiveAccount as ExclusiveAccountBusiness;

class ExclusiveAccount extends \Library\Controller
{

    //锁日志
    const LOKEY_LOG = 'exclusive/lock';
    //支付回调数据Key
    const DATA_KEY = 'exclusive:url:data:uid:';
    //微信支付请求日志
    const PAY_ONLINE_WX = 'exclusive/wx_request';
    //支付宝支付请求日志
    const PAY_ONLINE_ALI = 'exclusive/ali_request';
    //微信支付的回调日志
    const PAY_ONLINE_WX_RETURN = 'exclusive/wx_response';
    //支付宝支付的回调日志
    const PAY_ONLINE_ALI_RETURN = 'exclusive/ali_response';

    //微信支付的回调错误日志
    const PAY_ONLINE_WX_FAIL = 'exclusive/wx_response_error';
    //微信支付的回调成功日志
    const PAY_ONLINE_WX_OK = 'exclusive/wx_response_success';
    //支付宝支付的回调错误日志
    const PAY_ONLINE_ALI_FAIL = '/exclusive/ali_response_error';
    //支付宝支付的回调成功日志
    const PAY_ONLINE_ALI_OK = 'exclusive/ali_response_success';

    //微信回调地址
    const WX_NOTICE_NOTIFY_URL = PAY_DOMAIN . '/r/Finance_ExclusiveAccount/afterWxPay';
    //支付宝回调地址
    const ALI_NOTICE_NOTIFY_URL = PAY_DOMAIN . '/r/Finance_ExclusiveAccount/afterAliPay';

    //收银台支付回调地址
    const PRESTORE_NOTIFY_URL = PAY_DOMAIN. '/r/pay_CommonPayNotify/platformSpecialDepositsNotify';
    const PRESTORE_SOURCET    = 41; // 与CommonPayNotify::platformSpecialDepositsNotify保持一致

    private $_exclusiveModel;

    private $_moduleListModel;

    private $_depositeModel;

    private $_specialedAccountBiz;

    //支付方式转换处理
    private $_payWayHandle = [
        1 => 1,
        2 => 13,
    ];

    private function _getExclusiveModel()
    {
        if (empty($this->_exclusiveModel)) {
            $this->_exclusiveModel = new \Model\Finance\ExclusiveAccount();
        }

        return $this->_exclusiveModel;
    }

    private function _getModuleListModel()
    {
        if (empty($this->_moduleListModel)) {
            $this->_moduleListModel = new ModuleList();
        }

        return $this->_moduleListModel;
    }

    private function _getDepositeModel()
    {
        if (empty($this->_depositeModel)) {
            $this->_depositeModel = new Deposite();
        }

        return $this->_depositeModel;
    }

    private function getSpecialedAccountBiz()
    {
        if (empty($this->_specialedAccountBiz)) {
            $this->_specialedAccountBiz = new \Business\ExclusiveAccount\SpecialedAccount();
        }

        return $this->_specialedAccountBiz;
    }

    /**
     * 微信生成二维码
     */
    public function payByWx()
    {
        $code  = 200;
        $msg   = '';
        $cache = \Library\Cache\Cache::getInstance('redis');
        //微信二维码链接
        $url     = '';
        $orderNo = '';

        //配置ID
        $configId = I('post.config_id', 0, 'int');
        //充值类型  0 凭证费 1短信费
        $type = I('post.type', 0, 'int');
        //资费类型  0 固定 1 阶梯
        $purchaseType = I('post.purchase_type', 0, 'int');
        //购买数量
        $purchaseNum = I('post.purchase_num', 0, 'int');

        //用户ID
        $loginInfo = $this->getLoginInfo();
        $loginId   = $loginInfo['sid'];
        $operateId = $loginInfo['memberID'];

        try {
            //加锁 防止恶意请求
            $lock    = "payByWx:$loginId:$configId";
            $lockRes = $cache->lock($lock, 1, 120);

            if (!$lockRes) {
                pft_log(self::LOKEY_LOG, "[$lockRes]操作频繁");
                //throw new \Exception("请求正在处理中，请稍后");
            };

            //通过配置id 获取到对应配置明细 校验一次配置类型  配置金额是否一致
            $configInfoRes = $this->getSpecialedAccountBiz()->configDetail($configId);

            if ($configInfoRes['code'] != 200 || empty($configInfoRes['data'])) {
                throw new \Exception("配置获取失败，请确认后重试");
            }

            $configInfo = $configInfoRes['data'];

            //校验科目及配置和对应的数量是否符合规则
            if (($type == 0 && $configInfo['subject_code'] != BookSubject::VOUCHER_ACCOUNT_CODE) || ($type == 1 && $configInfo['subject_code'] != BookSubject::SMS_ACCOUNT_CODE)) {
                throw new \Exception("账本类目传输有误，请确认后重试");
            }
            if ($purchaseType != $configInfo['purchase_type'] || ($purchaseType == 0 && $purchaseNum != $configInfo['nums']) || ($purchaseType == 1 && ($purchaseNum > $configInfo['max_number'] || $purchaseNum < $configInfo['min_number']))) {
                throw new \Exception("购买数量与配置不匹配，请确认后重试");
            }

            if ($purchaseType == 0) {
                if ($configInfo['discount'] == 100) {
                    $fee = $configInfo['price'];
                } else {
                    $fee = $configInfo['price'] * ($configInfo['discount'] / 100);
                }
            } else {
                $fee = $configInfo['unit_price'] * $purchaseNum;
            }

            //开通申请 日志
            pft_log(self::PAY_ONLINE_WX, "申请开通：用户ID：{$loginId}, 配置ID：{$configId}|申请生成微信二维码");

            //费用 微信以分为单位
            //$fee = $configInfo['fee'];
            $amount = $fee;

            if ($type == 0) {
                $account = '电子凭证费账户';
            } elseif ($type == 1) {
                $account = '短信费账户';
            } else {
                throw new \Exception("未知类型账户");
            }

            //生成虚拟唯一订单号
            $orderNo = 'exc_' . str_replace('.', '', microtime(true));

            $appId    = PFT_WECHAT_APPID;
            $wxPayLib = new \Library\Business\WePay\WxPayLib($appId);

            //微信支付备注 转换成元
            $price = $amount / 100;

            if (empty($price)) {
                throw new \Exception("费用出错, 请充值");
            }

            if (defined('ENV') && in_array(ENV, ['PRODUCTION'])) {
                $subject    = "预存{$account}费用:{$price}元";
                $parameters = $wxPayLib->qrPay(
                    $amount,
                    $subject,
                    $orderNo,
                    self::WX_NOTICE_NOTIFY_URL,
                    '微信二维码支付',
                    'orderpay'
                );

                if ($parameters['return_code'] == 'SUCCESS') {
                    $url = $parameters['code_url'];
                } else {
                    throw new \Exception($parameters['return_msg']);
                }

                if (empty($url)) {
                    throw new \Exception("微信二维码生成错误, 请刷新页面");
                }

                $remarks = [
                    'uid'        => $loginId,
                    'operate_id' => $operateId,
                    'amount'     => $amount,
                    'config_id'  => $configId,
                    'pay_way'    => 13,
                    //'remarks'    => $subject,
                    'type'       => $type,
                    'p_num'      => $purchaseNum,
                    'p_type'     => $purchaseType,
                ];

                // 将数据记录在缓存中
                $dataKey  = self::DATA_KEY . $loginId;
                $redisRes = $cache->setex($dataKey, 3600, json_encode($remarks));
                if (empty($redisRes)) {
                    throw new \Exception("缓存记录生成失败");
                }

                $remarks = json_encode($remarks);
                //生成充值记录
                $onLineTrade = new \Model\TradeRecord\OnlineTrade();
                $create      = $onLineTrade->addRecord(
                    $orderNo,
                    $subject,
                    $amount,
                    '',
                    '',
                    1,
                    $remarks
                );

                if (empty($create)) {
                    throw new \Exception("充值记录生成失败");
                }
            } else {
                //如果是测试环境
                //TODO 后面如果需要在测试环境测试，可以添加模拟测试，可以参考 Controller\AppCenter\ModulePayment->wxPayCreQrCode
                throw new \Exception("测试环境不支持在线支付");
            }
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        if (!empty($lock)) {
            $cache->rm($lock);
        }

        //开通结果 日志
        if ($code == 200) {
            $result = '成功';
        } else {
            $result = '失败';
        }
        pft_log(self::PAY_ONLINE_WX,
            "申请结果：用户ID：{$loginId}, 配置ID：{$configId}, 订单号：{$orderNo}|微信二维码生成|{$result}, 原因:{$msg}");
        if (!empty($price) && in_array(ENV, ['LOCAL', 'DEVELOP'])) {
            //迁移到Java新版充值的接口
            $res = $this->getSpecialedAccountBiz()->rechargeAccount($type, $loginId, $purchaseNum, $amount, $configId, $purchaseType, $operateId,
                4, "购买条数{$purchaseNum}", $orderNo,time() . rand(100000, 9999999));
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $data = ['url' => $url, 'order_no' => $orderNo];
        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 支付宝生成二维码
     */
    public function payByAli()
    {
        $code  = 200;
        $msg   = '';
        $cache = \Library\Cache\Cache::getInstance('redis');
        //微信二维码链接
        $url = '';

        //配置ID
        $configId = I('post.config_id', 0, 'int');
        //充值类型  0 凭证费 1短信费
        $type = I('post.type', 0, 'int');
        //资费类型  0 固定 1 阶梯
        $purchaseType = I('post.purchase_type', 0, 'int');
        //购买数量
        $purchaseNum = I('post.purchase_num', 0, 'int');

        //用户ID
        $loginInfo = $this->getLoginInfo();
        $loginId   = $loginInfo['sid'];
        $operateId = $loginInfo['memberID'];

        //加锁 防止恶意请求
        $lock    = "payByAli:$loginId:$configId";
        $lockRes = $cache->lock($lock, 1, 120);
        if (!$lockRes) {
            pft_log(self::LOKEY_LOG, "[$lockRes]操作频繁");
            throw new \Exception("请求正在处理中，请稍后");
        };

        try {
            //通过配置id 获取到对应配置明细 校验一次配置类型  配置金额是否一致
            $configInfoRes = $this->getSpecialedAccountBiz()->configDetail($configId);

            if ($configInfoRes['code'] != 200 || empty($configInfoRes['data'])) {
                throw new \Exception("配置获取失败，请确认后重试");
            }

            $configInfo = $configInfoRes['data'];

            //校验科目及配置和对应的数量是否符合规则
            if (($type == 0 && $configInfo['subject_code'] != BookSubject::VOUCHER_ACCOUNT_CODE) || ($type == 1 && $configInfo['subject_code'] != BookSubject::SMS_ACCOUNT_CODE)) {
                throw new \Exception("账本类目传输有误，请确认后重试");
            }
            if ($purchaseType != $configInfo['purchase_type'] || ($purchaseType == 0 && $purchaseNum != $configInfo['nums']) || ($purchaseType == 1 && ($purchaseNum > $configInfo['max_number'] || $purchaseNum < $configInfo['min_number']))) {
                throw new \Exception("购买数量与配置不匹配，请确认后重试");
            }

            //判断配置中是否配置了折扣价 假如配置折扣价  价格需要重新计算
            if ($purchaseType == 0) {
                if ($configInfo['discount'] == 100) {
                    $fee = $configInfo['price'];
                } else {
                    $fee = $configInfo['price'] * ($configInfo['discount'] / 100);
                }
            } else {
                $fee = $configInfo['unit_price'] * $purchaseNum;
            }

            //开通申请 日志
            pft_log(self::PAY_ONLINE_ALI, "申请开通：用户ID：{$loginId}, 配置ID：{$configId}|申请生成支付宝二维码");

            //单条费用 分
            //$fee = $configInfo['fee'];
            //支付费用
            $amount = $fee;
            //支付宝需要元
            $price = bcdiv($amount, 100, 2);

            if (empty($price)) {
                throw new \Exception("费用出错, 请充值");
            }

            if ($type == 0) {
                $account = '电子凭证费账户';
            } elseif ($type == 1) {
                $account = '短信费账户';
            } else {
                throw new \Exception("未知类型账户");
            }

            //生成虚拟唯一订单号
            $orderNo = 'exc_' . str_replace('.', '', microtime(true));

            $subject = "预存{$account}费用:{$price}元";

            $remarks = [
                'uid'        => $loginId,
                'operate_id' => $operateId,
                'amount'     => $amount,
                'config_id'  => $configId,
                'pay_way'    => 1,
                //'remarks'    => $subject,
                'type'       => $type,
                'p_num'      => $purchaseNum,
                'p_type'     => $purchaseType,
            ];

            // 将数据记录在缓存中
            $dataKey  = self::DATA_KEY . $loginId;
            $redisRes = $cache->setex($dataKey, 3600, json_encode($remarks));
            if (empty($redisRes)) {
                throw new \Exception("缓存记录生成失败");
            }

            $remarks = json_encode($remarks);

            if (defined('ENV') && in_array(ENV, ['PRODUCTION'])) {
                //生成二维码
                $f2fPay   = new \Library\Business\Alipay\F2FPay(PFT_ALIPAY_F2FPAY_ID);
                $response = $f2fPay->qrpay($orderNo, $price, $subject, self::ALI_NOTICE_NOTIFY_URL, $subject);
                if ($response->alipay_trade_precreate_response->code == 10000 && $response->alipay_trade_precreate_response->msg == 'Success') {
                    $url = $response->alipay_trade_precreate_response->qr_code;
                }
                unset($f2fPay);

                if (empty($url)) {
                    throw new \Exception("支付宝二维码生成错误, 请刷新页面");
                }

                //生成充值记录
                $onLineTrade = new \Model\TradeRecord\OnlineTrade();
                $create      = $onLineTrade->addRecord(
                    $orderNo,
                    $subject,
                    $amount,
                    '',
                    '',
                    0,
                    $remarks
                );

                if (empty($create)) {
                    throw new \Exception("充值记录生成失败");
                }
            } else {
                //如果是测试环境
                //TODO 后面如果需要在测试环境测试，可以添加模拟测试，可以参考 Controller\AppCenter\ModulePayment->wxPayCreQrCode
                throw new \Exception("测试环境不支持在线支付");
            }
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        if (!empty($lock)) {
            $cache->rm($lock);
        }

        //开通结果 日志
        if ($code == 200) {
            $result = '成功';
        } else {
            $result = '失败';
        }

        pft_log(self::PAY_ONLINE_ALI,
            "申请结果：用户ID：{$loginId}, 模块ID：{$configId}, 订单号{$orderNo}|支付宝二维码生成|{$result}, 原因:{$msg}");

        $data = ['url' => $url, 'order_no' => $orderNo];
        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 微信成功后的回调方法
     */
    public function afterWxPay()
    {
//        include_once "/var/www/html/Service/Conf/WePay.conf.php";
//        $notify = new \Notify_pub();
        $notify   = new WxPayNotifyResponse();
        //微信返回值
        $xml = file_get_contents('php://input');
        if (isset($_POST['notify_data'])) {
            $xml = $_POST['notify_data'];
        }

        pft_log(self::PAY_ONLINE_WX_RETURN, $xml);
        if (empty($xml)) {
            exit('Invalid Request!');
        }

        $notify->saveData($xml);
        $appId     = isset($notify->data['sub_appid']) ? $notify->data['sub_appid'] : $notify->data['appid'];
        $WePayConf = include '/var/www/html/Service/Conf/WePay.conf.php';
        //订单号
        $outTradeNo = $notify->data['out_trade_no'];
        //微信订单号
        $tradeNo = $notify->data['transaction_id'];

        $cache = \Library\Cache\Cache::getInstance('redis');
        //加锁 防止恶意请求
        $lock    = "payByAli:$outTradeNo:$tradeNo";
        $lockRes = $cache->lock($lock, 1, 120);
        if (!$lockRes) {
            pft_log(self::LOKEY_LOG, "[$lockRes]操作频繁");
            exit('fail');
        };

        if ($notify->checkSign($WePayConf[$appId]['key']) == false) {
            $notify->SetReturn_code("FAIL"); //返回状态码
            $notify->SetReturn_msg("签名失败"); //返回信息
            pft_log(self::PAY_ONLINE_WX_FAIL, "【签名失败】:" . $xml);
            exit($notify->ToXml());
        } else {
            if ($notify->data["return_code"] == "FAIL") {
                pft_log(self::PAY_ONLINE_WX_FAIL, "【通信出错】:" . $xml);
                exit;
            } elseif ($notify->data["result_code"] == "FAIL") {
                pft_log(self::PAY_ONLINE_WX_FAIL, "【业务出错】:" . $xml);
                exit;
            } else {
                //金额用分为单位
                $payTotalFee = (int)$notify->data['total_fee'];

                //开通前的检测
                $onLineTradeModel = new \Model\TradeRecord\OnlineTrade();
                $logInfo          = $onLineTradeModel->getLogByOrderId($outTradeNo);


            }
        }
    }

    /**
     * 支付宝成功后的回调方法
     */
    public function afterAliPay()
    {
        pft_log(self::PAY_ONLINE_ALI_RETURN, @json_encode($_REQUEST, JSON_UNESCAPED_UNICODE));

        $f2fPay = new \Library\Business\Alipay\F2FPay(PFT_ALIPAY_F2FPAY_ID);

        $verifyResult = $f2fPay->verify($_POST, I('app_id'));

        $outTradeNo  = $_POST['out_trade_no']; // 商户订单号
        $tradeNo     = $_POST['trade_no']; // 支付宝交易号
        $tradeStatus = $_POST['trade_status']; // 交易状态

        $cache = \Library\Cache\Cache::getInstance('redis');
        //加锁 防止恶意请求
        $lock    = "payByAli:$outTradeNo:$tradeNo:$tradeStatus";
        $lockRes = $cache->lock($lock, 1, 120);
        if (!$lockRes) {
            pft_log(self::LOKEY_LOG, "[$lockRes]操作频繁");
            exit('fail');
        };

        if ($verifyResult) {
            //验证成功
            if ($tradeStatus == 'TRADE_FINISHED') {
                pft_log(self::PAY_ONLINE_ALI_OK, "1:$tradeNo:$outTradeNo:$tradeStatus");
                exit('success');
            } elseif ($tradeStatus == 'TRADE_SUCCESS') {
                //判断该笔订单是否在商户网站中已经做过处理
                $payTotalFee = $_POST['total_fee'];
                if (isset($_POST['total_amount'])) {
                    $payTotalFee = $_POST['total_amount'];
                }
                //开通前的检测
                $onLineTradeModel = new \Model\TradeRecord\OnlineTrade();
                $logInfo          = $onLineTradeModel->getLogByOrderId($outTradeNo);

                if (empty($logInfo)) {
                    pft_log(self::PAY_ONLINE_ALI_FAIL, "1:$tradeNo:$outTradeNo:$tradeStatus|找不到记录");
                    exit('fail');
                }

                if ($logInfo['status'] == 1) {
                    pft_log(self::PAY_ONLINE_ALI_FAIL, "4:$tradeNo:$outTradeNo:$tradeStatus|该笔充值已处理");
                    exit('success');
                }

                if ($logInfo['total_fee'] != $payTotalFee) {
                    pft_log(self::PAY_ONLINE_ALI_FAIL,
                        "5:{$logInfo['total_fee']}:$payTotalFee:$tradeNo:$outTradeNo:$tradeStatus|金额不正确");
                    exit('fail');
                }

                //请求java
                $description = $logInfo['description'];
                $description = json_decode($description, true);

                if (empty($description)) {
                    pft_log(self::PAY_ONLINE_ALI_FAIL,
                        "6:$payTotalFee:$tradeNo:$outTradeNo:$tradeStatus|信息解析失败");
                    exit('fail');
                }
                $uid         = $description['uid'];
                $operateId   = $description['operate_id'];
                $fee         = $description['amount'];
                $configId    = $description['config_id'];
                $orderId     = $outTradeNo;
                $payWay      = $description['pay_way'];
                //$remarks      = $description['remarks'];
                $type         = $description['type'];
                $purchaseNum  = $description['p_num'];
                $purchaseType = $description['p_type'];

                //迁移到Java新版充值的接口
                $res = $this->getSpecialedAccountBiz()->rechargeAccount($type, $uid, $purchaseNum, $fee, $configId, $purchaseType, $operateId,
                    $payWay, "购买条数{$purchaseNum}", $orderId, $tradeNo);

                if ($res['code'] == 200) {
                    $data     = [
                        'trade_no' => $tradeNo,
                        'dtime'    => date('Y-m-d H:i:s'),
                        'status'   => 1,
                    ];
                    $tradeRes = $onLineTradeModel->updateRecord('out_trade_no', $outTradeNo, $data);
                    if (!$tradeRes) {
                        pft_log(self::PAY_ONLINE_ALI_FAIL, "更新在线交易记录表失败");
                        exit('fail');
                    }

                    $this->_addCache($uid, $type);
                    pft_log(self::PAY_ONLINE_ALI_OK, "{$outTradeNo}|开通成功");
                    exit('success');
                } else {
                    pft_log(self::PAY_ONLINE_ALI_FAIL, "{$outTradeNo}|开通失败|{$res['msg']}");
                    exit('fail');
                }
            }
            pft_log(self::PAY_ONLINE_ALI_FAIL, "2:$tradeNo:$outTradeNo:$tradeStatus");
            //验证成功
            echo "success";
        } else {
            pft_log(self::PAY_ONLINE_ALI_FAIL, "3:$tradeNo:$outTradeNo:$tradeStatus");
            //验证失败
            echo "fail";
        }
    }

    /**
     * 验证是否完成充值
     */
    public function isSuccessPay()
    {
        try {
            $orderNo = I('post.order_no');

            if (empty($orderNo)) {
                throw new \Exception("订单号不能为空");
            }

            //在线交易记录表
            $onLineTradeModel = new OnlineTrade();
            $tradeRes         = $onLineTradeModel->getLogByOrderId($orderNo);
            if (!empty($tradeRes) && isset($tradeRes['status']) && $tradeRes['status'] == 1) {
                $code = 200;
                $msg  = 200;
            } else {
                $code = 202;
                $msg  = '订单待支付';
            }
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, '', $msg);
    }

    /**
     * 添加已开通缓存
     */
    private function _addCache($uid, $type)
    {
        if ($type == 1) {
            //凭证码
            $cType = 2;
        } else {
            //短信费
            $cType = 1;
        }
        $exclusiveBusiness = new \Business\Finance\ExclusiveAccount();
        $exclusiveBusiness->addOpenCache($uid, $cType);
    }

    /**
     * 专项预存
     */
    public function platformSpecialDepositsQr()
    {
        //配置ID
        $configId = I('post.config_id', 0, 'int');
        //充值类型  0 凭证费 1短信费
        $type = I('post.type', 0, 'int');
        //资费类型  0 固定 1 阶梯
        $purchaseType = I('post.purchase_type', 0, 'int');
        //购买数量
        $purchaseNum = I('post.purchase_num', 0, 'int');
        //支付方式  1 支付宝  2 微信
        $payType  = I('post.pay_type', 2, 'intval');

        //用户ID
        $loginInfo = $this->getLoginInfo();
        $loginId   = $loginInfo['sid'];
        $operateId = $loginInfo['memberID'];

        //加锁 防止恶意请求
        $lock    = "prestore:$loginId:$configId:$payType";
        $cache   = \Library\Cache\Cache::getInstance('redis');
        $lockRes = $cache->lock($lock, 1, 120);

        if (!$lockRes) {
            pft_log(self::LOKEY_LOG, "[$lockRes]操作频繁");
            $this->apiReturn(203, [], '请求正在处理中，请稍后');
        }

        //通过配置id 获取到对应配置明细 校验一次配置类型  配置金额是否一致
        $configInfoRes = $this->getSpecialedAccountBiz()->configDetail($configId);

        if ($configInfoRes['code'] != 200 || empty($configInfoRes['data'])) {
            $this->apiReturn(203, [], '配置获取失败，请确认后重试');
        }

        $configInfo = $configInfoRes['data'];

        //校验科目及配置和对应的数量是否符合规则
        if (($type == 0 && $configInfo['subject_code'] != BookSubject::VOUCHER_ACCOUNT_CODE) || ($type == 1 && $configInfo['subject_code'] != BookSubject::SMS_ACCOUNT_CODE)) {
            $this->apiReturn(203, [], '账本类目传输有误，请确认后重试');
        }
        if ($purchaseType != $configInfo['purchase_type'] || ($purchaseType == 0 && $purchaseNum != $configInfo['nums']) || ($purchaseType == 1 && ($purchaseNum > $configInfo['max_number'] || $purchaseNum < $configInfo['min_number']))) {
            $this->apiReturn(203, [], '购买数量与配置不匹配，请确认后重试');
        }

        if ($purchaseType == 0) {
            if ($configInfo['discount'] == 100) {
                $amount = $configInfo['price'];
            } else {
                $amount = $configInfo['price'] * ($configInfo['discount'] / 100);
            }
        } else {
            $amount = $configInfo['unit_price'] * $purchaseNum;
        }

        $price = $amount / 100;

        if (empty($price)) {
            $this->apiReturn(203, [], '费用出错, 请充值');
        }

        $account = '';
        if ($type == 0) {
            $account = '电子凭证费账户';
        } elseif ($type == 1) {
            $account = '短信费账户';
        } else {
            $this->apiReturn(203, [], '未知类型账户');
        }

        //生成虚拟唯一订单号
        $outTradeNo = 'exc_' . str_replace('.', '', microtime(true));

        $subject = "预存{$account}费用:{$price}元";

        $remarks = [
            'uid'        => $loginId,
            'operate_id' => $operateId,
            'amount'     => $amount,
            'config_id'  => $configId,
            'pay_way'    => $this->_payWayHandle[$payType],
            'type'       => $type,
            'p_num'      => $purchaseNum,
            'p_type'     => $purchaseType,
        ];
        $remarks = json_encode($remarks);

        // 调用支付中心收银台页面
        $unifiedPay  = new UnifiedPay();
        $rpcResponse = $unifiedPay->unifyQrPayRpcService($outTradeNo, $subject, $amount, 1, $payType,
            'platform', self::PRESTORE_NOTIFY_URL, $_SERVER['REMOTE_ADDR'], '',
            ['pft_member_id' => $loginId,'source'=>'platform_special_deposits']);

        if ($rpcResponse['code'] != 200) {
            $this->apiReturn(204, [], $rpcResponse['msg']);
        }

        $model = new OnlineTrade();
        $ret   = $model->addLog($outTradeNo, $amount, $subject, $remarks, self::PRESTORE_SOURCET,OnlineTrade::PAY_METHOD_RECHARGE);
        unset($model);
        if (!$ret) {
            $this->apiReturn(204, [], '记录发生错误,请联系客服人员');
        }

        $data = [
            'outTradeNo' => $outTradeNo,
            'qrUrl'      => $rpcResponse['data']['url'],
        ];

        $this->apiReturn(200, $data);
    }

    /**
     * 收银台支付成功后发起支付状态检测
     */
    public function payResultCheck()
    {
        $this->isLogin('ajax');
        $ordernum = I('post.order_no');
        pft_log('yeepay/pay_result_check', json_encode($_POST, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        $model = new OnlineTrade();

        $payLog = $model->getLog($ordernum, self::PRESTORE_SOURCET);
        if (!$payLog) {
            $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '支付记录不存在');
        }

        if ($payLog['status'] == 1) {
            $this->apiReturn(parent::CODE_SUCCESS, [], '支付成功');
        }
        $this->apiReturn(parent::CODE_INVALID_REQUEST, [], '支付未到账，请稍后查看');
    }
}