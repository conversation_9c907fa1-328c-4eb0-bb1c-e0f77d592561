<?php

namespace Controller\Finance;

use Library\Controller;
use Business\Finance\AmountLedgerFreeze as AmountLedgerFreezeBiz;

class Member extends Controller
{
    private $_loginInfo;

    private $_memberId;

    private $_sid;

    public function __construct()
    {
        parent::__construct();

        $this->_loginInfo = $this->getLoginInfo('ajax');

        $this->_sid      = $this->_loginInfo['sid'];
        $this->_memberId = $this->_loginInfo['memberID'];
    }

    /**
     * 验证财务权限
     * <AUTHOR>
     * @date   2024/1/23
     *
     */
    public function checkAuth()
    {
        //是否启动余额冻结
        $amountLedgerFreezeBiz = new AmountLedgerFreezeBiz();
        $frozenEnabled = $amountLedgerFreezeBiz->isFrozenEnabled($this->_sid);

        $result = [
            'frozenEnabled' => $frozenEnabled
        ];

        $this->apiReturn(200, $result);
    }

}