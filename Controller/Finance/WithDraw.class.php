<?php
/**
 * 提现相关功能
 *
 * <AUTHOR>
 * @since  2016年11月1日
 */

namespace Controller\Finance;

use Business\Finance\Withdraw as WithdrawBiz;
use Business\RiskManagement\ShumeiRiskCheck;
use Controller\Tpl\publicnumcon;
use Library\Cache\Cache;
use Library\Controller;
use Library\Kafka\KafkaProducer;
use Model\Finance\SettleBlance;
use Model\Member\Member;
use Business\Finance\AccountMoney as BizAccountMoney;
use Business\JavaApi\Finance\WithdrawQuery;
use Business\Finance\AmountLedgerFreeze as AmountLedgerFreezeBiz;
use Business\Member\FeesConfig as FeesConfigBiz;

class WithDraw extends Controller
{
    const MEM_DIFFERENT_TIME = '2017-09-08 00:00:00'; // 新旧用户区别时间
    private $loginInfo = [];

    public function __construct()
    {
        $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo(); //需要用到的登录信息

    }

    /**
     * 提现冻结
     * 在提现的页面中 计算出用户大概可提现金额
     *
     * <AUTHOR>
     * @since  2016-11-01
     */
    public function withDrawFre($result = false)
    {
        $memberId = $this->isLogin();

        $amountLedgerFreezeBiz = new AmountLedgerFreezeBiz();
        if ($amountLedgerFreezeBiz->isFrozenEnabled($memberId)) {
            //冻结金额
            $freMoney = $amountLedgerFreezeBiz->getFrozenBalance($memberId) / 100;
            //可用金额
            $withdrawMoney = $amountLedgerFreezeBiz->getAvailableBalance($memberId) / 100;
            //账户余额
            $balance = $amountLedgerFreezeBiz->getAccountBalance($memberId) / 100;
            //获取提现中的金额
            $withdrawingTotalMoney = $amountLedgerFreezeBiz->getWithdrawingTotalMoney($memberId) / 100;
            //是否启用余额冻结
            $isFrozenEnabled = true;
            //清分配置文本
            $datePeriodDescRes = (new FeesConfigBiz())->getSettingDatePeriod($memberId);
            //开启清分则展示配置
            $datePeriodDescStatus = intval($datePeriodDescRes['data'][0]['status'] ?? 0);
            if ($datePeriodDescStatus) {
                $datePeriodDesc    = $datePeriodDescRes['data'][0]['description'] ?? '';
            }
        } else {
            //提现冻结页面放开条件
            $hasSaleMoney  = true;
            $settleBalance = new SettleBlance();
            $data          = $settleBalance->unusedOrderDetail($memberId, false, [0, 2, 7, 4, 10, 11, 80, 81],
                $hasSaleMoney);
            if (empty($data) || !is_array($data)) {
                $this->apiReturn('fail', '', '查询出错');
            }

            //数据
            $info       = $data['total_data'];
            $detailData = $data['page_data'];

            //将详细冻结数据缓存一份，方便后面排查问题
            $cacheLib  = Cache::getInstance('redis');
            $cacheData = json_encode(['time' => time(), 'detail' => $detailData]);
            $cacheLib->set('tx_froze:' . $memberId, $cacheData, '', 7200);

            //冻结金额
            $freMoney = $info['money'];

            //释放
            unset($data);

            //账户余额
            $accountMoneyBiz = new BizAccountMoney();
            $accountMoneyArr = $accountMoneyBiz->getAccountMoney($memberId);

            if ($accountMoneyArr['code'] != 200) {
                $this->apiReturn('fail', '', '查询出错');
            }

            $accountMoney = $accountMoneyArr['data']['money'];

            //可提现金额
            $withdrawMoney = ($accountMoney - $freMoney) / 100;
        }

        //比较
        if (bccomp($withdrawMoney, 0, 5) === -1) {
            $money = 0;
        } elseif (bccomp($withdrawMoney, 3000000, 5) === 1) {
            $money = $withdrawMoney;
        } else {
            $money = $withdrawMoney;
        }

        //开启冻结则展示具体可用的，可能为负数
        if ($isFrozenEnabled ?? false) {
            $money = $withdrawMoney;
        }

        if ($result) {
            return $money; //自己接返回可用的money
        }

        $this->apiReturn('success', [
            //可用金额
            'wdMoney'        => $money,
            //冻结金额
            'freMoney'       => $freMoney,
            //实际可提现金额
            'realWdMoney'    => $withdrawMoney,
            //账户余额
            'balance'        => $balance ?? 0,
            //提现中金额
            'withdrawMoney'  => $withdrawingTotalMoney ?? 0,
            //是否启用余额冻结
            'frozenEnabled'  => $isFrozenEnabled ?? false,
            //清分配置文本
            'datePeriodDesc' => FeesConfigBiz::getSettingDatePeriodDesc($datePeriodDesc ?? ''),
            //是否开启清分配置
            'datePeriodEnabled' => ($datePeriodDescStatus ?? 0) == 1,
        ]);
    }

    /**
     * 获取提现记录列表
     *
     * <AUTHOR>
     * @date  2018-07-28
     */
    public function getRecordList()
    {
        $page       = I('post.page', 1, 'intval');        //页码  1
        $size       = I('post.size', 10, 'intval');       //条数  10
        $start_date = I('post.start_date', '', 'strval'); //开始时间 【yyyy-MM-dd】
        $end_date   = I('post.end_date', '', 'strval');   //结束时间 【yyyy-MM-dd】

        if (!empty($start_date)) {
            if (!chk_date($start_date)) {
                $this->apiReturn('400', '', '查询开始时间错误');
            }
            $start_date .= ' 00:00:00';
        }

        if (!empty($end_date)) {
            if (!chk_date($end_date)) {
                $this->apiReturn('400', '', '查询结束时间错误');
            }
            $end_date .= ' 23:59:59';
        }

        $sid = $this->loginInfo['sid'];  //上级ID

        //获取会员详情
        $Member     = new Member();
        $memberInfo = $Member->getMemberInfo($sid, 'id', 'id,dname', [], false); //TODO fee_bank第三方支付费用 好像没有用到
        if ($memberInfo === false) {
            $this->apiReturn('500', '', '会员信息查询出错');
        }

        //提现记录
        //$Withdraws  = new \Model\Finance\Withdraws();
        //$fleid      = "id,wd_money,apply_time,bank_name,wd_name,bank_accuont,wd_operator,wd_status,memo,cut_fee_way,service_charge";
        //$recordData = $Withdraws ->getRecordList($sid,$page,$size,$start_date,$end_date,$fleid);
        //if($recordData === false){
        //    $this->apiReturn('500', '', '查询出错');
        //}

        $WithdrawBiz = new \Business\Finance\Withdraw();
        $queryRes    = $WithdrawBiz->queryRecordList($sid, $start_date, $end_date, 'apply_time desc', -1, 1, [],
            $page, $size);
        if ($queryRes['code'] != 200) {
            $this->apiReturn('500', '', $queryRes['msg']);
        }

        $recordList = $queryRes['data']["list"];  //列表
        $total      = $queryRes['data']["total"]; //统计

        //返回列表
        $resList = [];

        if (!empty($recordList) && is_array($recordList)) {
            foreach ($recordList as $k) {
                $tmpWdMoney       = $k['wd_money'] / 100;
                $tmpServiceCharge = $k['service_charge'] / 100;
                $transMoney       = ($k['cut_fee_way'] == 0) ? ($tmpWdMoney - $tmpServiceCharge) : $tmpWdMoney;

                $tmpInfo = [
                    'id'               => $k['id'],
                    'wd_name'          => $k['wd_name'],
                    'wd_status'        => strval($k['wd_status']), //前端是按string去判断，所以在这边做下兼容
                    'cut_from'         => $k['cut_fee_way'] ? '未结票款扣除' : '提现金额扣除',
                    'apply_time'       => substr($k['apply_time'], 0, 10),//申请提现时间
                    'withdraw_deposit' => number_format($tmpWdMoney, 2), //提现金额
                    'tfee'             => number_format($tmpServiceCharge, 2),//手续费
                    'transfer_money'   => sprintf("%.2f", $transMoney),//应收金额
                    'bank_accuont'     => substr_replace($k['bank_accuont'], "***", 4, 11),//账号
                    'bank_name'        => $k['bank_name'],
                    'wd_operator'      => $k['wd_operator'] ?: $memberInfo["dname"],//操作用户
                    'memo'             => str_replace('账户余额', '未结票款', $k['memo']),//备注
                ];

                $resList[] = $tmpInfo;
            }
        }

        $output = [
            "list"  => $resList,
            "total" => $total,
        ];
        $this->apiReturn('200', $output, '查询成功');
    }

    /**
     * 获取累计提现金额取实际转账金额
     *
     * <AUTHOR>
     * @date  2018-07-30
     */
    public function getWithdrawMoneyInfo()
    {
        $memberID      = $this->loginInfo['sid'];      //上级id
        $dtype         = $this->loginInfo['dtype'];    //登录账号类型
        $loginMemberID = $this->loginInfo['memberID']; //登录用户ID (判断是否有线下充值按钮)
        $sdtype        = $this->loginInfo['sdtype'];   //上级账号类型

        if (in_array($dtype, [2, 7])) {
            $this->apiReturn('401', [], '没有权限');
        }

        $WithdrawB = new \Business\Finance\Withdraw();
        $getmoney  = $WithdrawB->getOverlayMoney($memberID, $loginMemberID); //累计计算资金
        if ($getmoney === false) {
            $this->apiReturn('500', [], '查询累计资金错误，清稍后重试');
        }

        //获取会员详情
        $Member = new Member();
        $money  = $Member->getPlatformBalanceByMemberId($memberID); //账户余额
        $memberInfo = $Member->getMemberInfo($memberID, 'id', 'creattime,customer_id,account_id', [], false);
        //获取账户id
        $api    = new \Business\JavaApi\Account\Account;
        $accRes = $api->getAccountInfoWithAccountId($memberInfo['account_id']);
        if ($accRes['code'] != 200 || !$accRes['data']) {
            $this->apiReturn('500', [], '账户信息获取信息');
        }
        $memberInfo['alipay']        = $accRes['data']['alipay_account'];
        $memberInfo['bank_account1'] = $accRes['data']['bank_account_1'];
        $memberInfo['bank_account2'] = $accRes['data']['bank_account_2'];
        //用户二期 - 信息获取修改 - 2
        $CustomerBus         = new \Business\Member\Customer();
        $customerInfo        = $CustomerBus->getCustomerInfo($memberInfo['customer_id']);
        $memberInfo['cname'] = $customerInfo["cname"];
        //添加第几个银行卡
        $addType = 2;
        if (!$memberInfo['bank_account2']) {
            $bankaccount          = "bank_account2";
            $addType              = 2;
            $bankCardaCcountInfo2 = "";
        } else {
            $bankCardaCcountInfo2 = $WithdrawB->getBankCardInfo($memberInfo['bank_account2'], $memberID,
                2); //获取相应的银行，省，市等信息
        }

        if (!$memberInfo['bank_account1']) {
            $bankaccount          = "bank_account1";
            $addType              = 1;
            $bankCardaCcountInfo1 = "";
        } else {
            $bankCardaCcountInfo1 = $WithdrawB->getBankCardInfo($memberInfo['bank_account1'], $memberID,
                1); //获取相应的银行，省，市等信息
        }

        //这个好像没什么用  这个SESSION 没有写入的地方
        $checkMobile = $dtype == 5 ? 1 : 0;

        //获取清分配置
        $settleBlanceModel = new \Model\Finance\SettleBlance();
        $tmp               = $settleBlanceModel->getSettingByFids([$memberID]);
        if ($tmp) {
            $info   = $tmp[$memberID];
            $status = $info['status'] == 1 ? 'on' : 'off';
        } else {
            $status = 'none'; //判断是否有清分记录的按钮
        }

        //判断是否有线下充值按钮
        $offlineBtn = 0;  //无
        //if($loginMemberID == 23444 && $sdtype == 9 && $memberID == 1){
        //    $offlineBtn = 1;  //有
        //}else{
        //    $offlineBtn = 0;  //无
        //}

        //判断支付宝的框框
        if (strtotime($memberInfo["creattime"]) < strtotime(self::MEM_DIFFERENT_TIME) && $memberInfo['alipay']) {
            $alipayBtn = 1;  //有
        } else {
            $alipayBtn = 0;
        }

        $api = new \Business\JavaApi\Member\MemberConfig();
        $res = $api->getConfigWithMemberId($memberID);
        if ($res['code'] == 200 && $res['data']) {
            $feeBank = $res['data']['fee_bank'];
        } else {
            $this->apiReturn('401', [], '费率配置获取失败');
        }

        $memberInfo["fee_bank"] = $feeBank / 10; //手续费
        $money                  /= 100;  //未结票款
        $getmoney               /= 100;  //累计计算资金

        //独立收款配置
        $independentData = [];
        $independentInfo = (new \Business\NewJavaApi\Independent\StandaloneAccount())->getMemberStatus([$memberID]);
        if ($independentInfo['code'] == 200 && !empty($independentInfo['data'])) {
            foreach ($independentInfo['data'] as $item) {
                foreach ($item as $memberId => $memberConf) {
                    foreach ($memberConf as $value) {
                        $independentData[$memberId][] = [
                            'settle_fee'       => $value['settleFee'],
                            'balance_amount'   => $value['balanceAmount'],
                            'service_provider' => $value['serviceType'],
                            'status'           => $value['status'],
                            'is_open'          => $value['status'] == 5 ? true : false,
                        ];
                    }
                }
            }
        }

        //资质认证
        $certification     = [];
        $certificationInfo = (new \Business\Member\CertificationInfo())->getCertificationStatus($memberID);
        if ($certificationInfo['code'] == 200 && !empty($certificationInfo['data'])) {
            $certification = $certificationInfo['data'];
        }

        $output = [
            'OverlayMoney'   => $getmoney,    //累计计算资金
            'money'          => $money,       //未结票款
            'offlineBtn'     => $offlineBtn,  //判断是否有线下充值按钮
            'addType'        => $addType,     //添加第几张银行卡
            'status'         => $status,      // 判断是否有清分记录的按钮
            'bankaccount'    => $bankaccount, //添加银行卡时候一个隐藏的值
            'checkMobile'    => $checkMobile, //页面刚开始的一个隐藏域
            'memberInfo'     => $memberInfo,  //会员详情
            'bankCardaInfo1' => $bankCardaCcountInfo1, //第一张银行卡信息
            'bankCardaInfo2' => $bankCardaCcountInfo2, //第二张银行卡信息
            'alipayBtn'      => $alipayBtn,    //判断支付宝的框框
            'independent'    => isset($independentData[$memberID]) ? $independentData[$memberID] : [],  //独立收款配置项
            'certification'  => $certification ?? [],  //资质认证
        ];

        $this->apiReturn('200', $output, '查询成功');
    }

    /**
     * 平台商户提现
     *
     * <AUTHOR>
     * @date  2020-02-04
     */
    public function merchantWithDraw()
    {
        $feeCutWay    = I('post.account_type', 0, 'intval'); //手续费扣款方式  -0 提现金额 -1 平台余额
        $bankType     = I('post.account', ''); // 提现到哪个账号：alipay=支付宝，bank_account1=第一个银行账户，bank_account2=第二个银行账户
        $tmpMoney     = I('post.wd_money', 0, 'floatval'); //提现金额 - 单位元
        $remark       = I('post.remark', '', 'strval'); // 备注
        $wdMoney      = float2int($tmpMoney, 2, 100);
        $feeCutWayArr = [0, 1];
        $bankTypeArr  = [
            'alipay'        => 'alipay',
            'bank_account1' => 'bank1',
            'bank_account2' => 'bank2',
        ];

        $sid          = $this->loginInfo['sid'];
        $opId         = $this->loginInfo['memberID'];
        $operatorName = $this->loginInfo['dname'];
        $dType        = $this->loginInfo['dtype'];
        $qx           = $this->loginInfo['qx'];

        //获取用户禁止提现配置
        $limitApi      = new \Business\JavaApi\Member\MemberLimit();
        $withdrawLimit = $limitApi->queryFunctionLimitByFidAndLimitType($sid, $limitType = 2);
        if ($withdrawLimit['code'] == 200 && !empty($withdrawLimit['data'])) {
            $this->apiReturn(204, [], '该账户处于提现禁用状态，不可操作');
        }

        $riskCheck = new ShumeiRiskCheck();
        $eventData = [
            'withdrawAmount'      => floatval($wdMoney),
            'withdrawAccountId'   => (string)$sid,
            'withdrawAccountType' => 'bank',
        ];
        $checkRes =$riskCheck->shumeiCheck('withdraw', 4, $this->loginInfo['account'], $eventData);
        if ($checkRes['code'] == 200 && $checkRes['data']['riskLevel'] == 'REJECT'){
            $this->apiReturn(210, [], '该设备/账号提现金额或次数已达到上限，请稍后再进行操作。若有疑问，请联系：400-99-22301');
        }
        if (!in_array($feeCutWay, $feeCutWayArr) || !array_key_exists($bankType, $bankTypeArr)) {
            $this->apiReturn(204, [], '提现参数错误');
        }
        $realBankType = $bankTypeArr[$bankType];
        if ($wdMoney <= 0) {
            $this->apiReturn(204, [], '提现金额错误');
        }
        $checkToken = $this->checkCSRFToken();
        if (!$checkToken) {
            $this->apiReturn(204, [], 'token无效');
        }

        //具体提现
        $withdrawBiz = new WithdrawBiz();
        $checkRes    = $withdrawBiz->checkWithdrawAuth($dType, $sid, $opId, $qx);
        if ($checkRes['code'] != 200) {
            $this->apiReturn(204, [], $checkRes['msg']);
        }

        $source   = 1;  //平台提现
        $applyRes = $withdrawBiz->apply($sid, $wdMoney, $realBankType, $feeCutWay, $opId, $operatorName, $source, [0, 2, 7, 4, 10, 11, 80, 81], $remark);

        if ($applyRes['code'] == 200) {
            $this->apiReturn(200, [], '提现成功');
        } else {
            $errMsg = $applyRes['msg'];
            $this->apiReturn(204, [], $errMsg);
        }
    }

    /**
     * 平台商户取消提现
     *
     * <AUTHOR>
     * @date  2020-02-04
     */
    public function merchantWithDrawCancel()
    {
        $id = I('post.id', 0);
        if (!$id) {
            $this->apiReturn(204, [], '参数错误，请联系客服处理');
        }

        $checkToken = $this->checkCSRFToken();
        if (!$checkToken) {
            //$this->apiReturn(204, [], 'token无效');
        }

        $withdrawBiz = new WithdrawBiz();
        //先进行权限校验
        $sid      = $this->loginInfo['sid'];
        $opId     = $this->loginInfo['memberID'];
        $dType    = $this->loginInfo['dtype'];
        $qx       = $this->loginInfo['qx'];
        $checkRes = $withdrawBiz->checkWithdrawAuth($dType, $sid, $opId, $qx);
        if ($checkRes['code'] != 200) {
            $this->apiReturn(204, [], $checkRes['msg']);
        }
        $res = $withdrawBiz->merchantWithDrawCancel($id, $sid, $opId);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], [], $res['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }
}
