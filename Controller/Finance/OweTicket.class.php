<?php
/**
 * 欠票
 * <AUTHOR>
 * @date   2021-03-31
 */

namespace Controller\Finance;

use Business\Finance\OweTicketAmount;
use Library\Controller;

class OweTicket extends Controller
{

    private $_oweTicketBiz;

    private function _getOweTicketBiz()
    {
        if (empty($this->_oweTicketBiz)) {
            $this->_oweTicketBiz = new OweTicketAmount();
        }

        return $this->_oweTicketBiz;
    }

    /**
     * 查询用户欠票记录
     * <AUTHOR>
     * @date 2021-03-31
     *
     * @return array
     */
    public function queryInvoiceOweTicketRecord()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $loginInfo = $this->getLoginInfo();
            $userId    = $loginInfo['sid'];
            $startTime = I('post.start_time', '', 'strval,trim');
            $endTime   = I('post.end_time', '', 'strval,trim');
            $page      = I('post.page', 1, 'int');
            $size      = I('post.size', 10, 'int');
            $expTime   = 600;
            $key       = "member_info:account_id". $userId;
            $cache     = \Library\Cache\Cache::getInstance('redis');
            $cacheData = $cache->get($key);
            if ($cacheData) {
                $accountId = $cacheData;
            }else{
                $memberInfo = (new \Model\Member\Member())->getMemberInfo($userId, 'id', 'account_id');
                $accountId  = (int)$memberInfo['account_id'];
                $cache->set($key, $memberInfo['account_id'], '', $expTime);
            }

            $result = $this->_getOweTicketBiz()->queryInvoiceOweTicketRecord($startTime, $endTime, $userId,  $accountId,
                $page, $size);
            $code   = $result['code'];
            $data   = $result['data'];
            $msg    = $result['msg'];

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 查询用户欠票
     * <AUTHOR>
     * @date 2021-03-31
     *
     * @return array
     */
    public function queryOweTicketAmountByAccountId()
    {
        $loginInfo = $this->getLoginInfo();
        $result    = $this->_getOweTicketBiz()->getOweTiketSum($loginInfo['sid']);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}