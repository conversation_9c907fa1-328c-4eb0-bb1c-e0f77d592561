<?php
/**
 * 冻结余额查询功能
 * <AUTHOR>
 * @date    2019-01-15
 */
namespace Controller\Finance;

use Library\Cache\Cache;
use Library\Controller;
use Business\Finance\FreezeMonthlyBalance as BalanceBus; //月份冻结余额业务层
use Model\Finance\FreezeMonthlyBalance as BalanceModel;  //月份冻结余额数据模型
use Business\Member\Member as MemberBus;//用户业务层
use Library\SimpleExcel;

class FreezeMonthlyBalance extends Controller
{
    private $_loginInfo = [];     //登录信息
    private $_isSuper   = false;

    public function __construct() {
        parent:: __construct();
        $this->_loginInfo = $this->getLoginInfo('ajax');  //需要用到的登录信息
        $this->_isSuper   = $this->isSuper();             //是否是管理员

        //目前这个功能是给财务的
//        if(!$this->_checkAuth()){
//            $this->apiReturn(self::CODE_AUTH_ERROR, [],'没有权限' );
//        }
    }

    /**
     * 获取冻结资金列表
     * <AUTHOR>
     * @date   2019-01-15
     */
    public function getFreezeList(){
        $memberId   = I('post.member_id','', 'int');      //用户id
        $fid        = I('post.com_fid','', 'int');        //公司名称查询出来的用户id
        $account    = I('post.account','', 'strval');     //账号信息
        $date       = I('post.date', '', 'strval');        //查询的日期
        $page       = I('page', 1, 'intval');              //页码
        $size       = I('size', 10, 'intval');             //条数
        $type       = I('type',0,'intval');                // 0提现金额1冻结金额
        if(!$date){
            $this->apiReturn(self::CODE_PARAM_ERROR, [],'查询日期不能为空' );
        }

        $BalanceBus = new BalanceBus(); //业务层
        if($this->_checkAuth()){
            $result     = $BalanceBus->getFreezeListBus($date, $memberId, $fid, $account, $page ,$size,$type);
        }else{
            $result     = $BalanceBus->getFreezeListBus($date, $this->_loginInfo['memberID'], $this->_loginInfo['memberID'], $this->_loginInfo['saccount'], $page ,$size,$type);
        }
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 冻结资金列表导出
     * <AUTHOR>
     * @date   2019-01-15
     */
    public function getFreezeExport(){
        $memberId   = I('member_id','', 'int');      //用户id
        $fid        = I('com_fid','', 'int');        //公司名称查询出来的用户id
        $account    = I('account','', 'strval');     //账号信息
        $date       = I('date', '', 'strval');        //查询的日期
        $type       = 2;
        $page = 1;
        $size = 9999999;
        if(!$date){
            $this->apiReturn(self::CODE_PARAM_ERROR, [],'查询日期不能为空' );
        }

        $BalanceBus = new BalanceBus(); //业务层
        if($this->_checkAuth()){
            $info  = $BalanceBus->getFreezeListBus($date, $memberId, $fid, $account, $page ,$size, $type);
        }else{
            $info  = $BalanceBus->getFreezeListBus($date, $this->_loginInfo['memberID'], $this->_loginInfo['memberID'], $this->_loginInfo['saccount'], $page ,$size, $type);
        }

        $list  = $info["data"]['list'];

        //导出数据
        $day = $this->_dayByDate($date);
        $filename      = $day . '冻结用户资金列表';
        $excelData[0]['a'] = '分销商ID';
        $excelData[0]['b'] = '商户账号';
        $excelData[0]['c'] = '分销商企业名称';
        $excelData[0]['d'] = '分销商账户名称';
        $excelData[0]['e'] = '提现冻结金额(单位：元)';
        $excelData[0]['f'] = '提现金额(单位：元)';
        if(!empty($list)){
            $i = 2;
            $money = 0;
            $withdrawMoney = 0;
            foreach ($list as $value) {
                $tmpMoney = $value["money"] / 100;
                $tmpWithdrawMoney = $value['withdrawal_amount'] / 100;
                $excelData[$i]['a'] = $value["fid"];
                $excelData[$i]['b'] = $value["account"];
                $excelData[$i]['c'] = $value["com_name"];
                $excelData[$i]['d'] = $value["dname"];
                $excelData[$i]['e'] = $tmpMoney;
                $excelData[$i]['f'] = $tmpWithdrawMoney;
                $i++;
                $money += $tmpMoney;
                $withdrawMoney += $tmpWithdrawMoney;
            }

            //汇总的数据
            $excelData[] = ['', '', '', '', '',''];
            $excelData[] = ['', '', '', '', '金额汇总：'.$money,'提现金额汇总'.$withdrawMoney];
        }

        $xls = new SimpleExcel('UTF-8', true, 'freezeList');
        $xls->addArray($excelData);
        $xls->generateXML($filename);
    }

    /**
     * 获取可查询的日期
     * <AUTHOR>
     * @date   2019-01-15
     */
    public function getDateArray(){
        $BalanceModel = new BalanceModel();
        $result       = $BalanceModel->getBalanceDayArr();

        if(!empty($result) && is_array($result)){
            foreach ($result as $key => $value){
                $result[$key]['value'] = $this->_dayByDate($value['date']);
            }
        }
        $this->apiReturn(self::CODE_SUCCESS, $result, '查询成功');
    }

    /**
     * 获取冻结用户id列表
     * <AUTHOR>
     * @date   2019-01-15
     */
    public function getFreezeFidList(){
        $comName = I('com_name','', 'strval');  //查询值
        if(!$comName){
            $this->apiReturn(self::CODE_SUCCESS, [], '');
        }

        $BalanceBus = new BalanceBus(); //业务层
        $result     = $BalanceBus->getFreezeFidListBus($comName);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取冻结资金汇总
     * <AUTHOR>
     * @date   2019-01-15
     */
    public function getBalanceCount(){
        $date     = I('post.date', '', 'strval');   //查询的日期
        $memberId = I('post.member_id','', 'int');  //用户id
        $fid      = I('post.com_fid','', 'int');    //公司名称查询出来的用户id
        $account  = I('post.account','', 'strval'); //账号信息
        $type     = I('post.type',0); //0提现 1冻结
        if(!$date){
            $this->apiReturn(self::CODE_PARAM_ERROR, [],'查询日期不能为空' );
        }

        $BalanceBus = new BalanceBus(); //业务层
        if($this->_checkAuth()){
            $result = $BalanceBus->getBalanceCountByDateBus($date, $memberId, $fid, $account,$type);
        }else{
            $this->apiReturn(204, [], '没权限');
        }
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 简单的日期转换 201812 -> 2018年12月
     * <AUTHOR>
     * @date   2019-01-15
     */
    private function _dayByDate($day){
        if(!$day){
            return false;
        }

        $tmp  = (string)$day;
        $year = substr($tmp, 0, 4);
        $month = substr($tmp, -2);
        $date  = $year.'年'.$month.'月';
        return $date;
    }

    //权限判断
    private function _checkAuth(){
        if(!$this->_isSuper){
            return false;
        }

        if (!strpos($this->_loginInfo['qx'], 'account_frozen')){
            return false;
        }

        $MemberBus  = new MemberBus();
        $memberInfo = $MemberBus->getInfo($this->_loginInfo["memberID"] ,true);
        if($memberInfo["position"] === '2'){
            return true;
        }else{
            return false;
        }
    }
    public function exportWithdraw(){
        $mid   =  $this->_loginInfo['memberID'];
        if($this->_checkAuth()){
            $mid   =  I('get.member_id','', 'int');      //用户id;
        }
        $type  =  I('get.type',0);//0提现 1冻结
        $month = I('get.month','');
        if (!$mid || !$month){
            $this->apiReturn(self::CODE_PARAM_ERROR, [],'查询日期或者分销商ID不能为空');
        }
        $locky = 'withraw:'.$this->_loginInfo['memberID'];
        $cache  = Cache::getInstance('redis');
        $lock_ret = $cache->lock($locky, 1, 120);
        if (!$lock_ret) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [],'处理中请稍后');
        };
        $withdrawBiz = new \Business\Finance\Withdraw();
        if ($type){
            $result = $withdrawBiz->getFreezeOrderByMid($mid,$month);
            header('Content-Type:text/html;charset=utf-8');
            header("Content-disposition:attachment;filename={$month}freeze.csv");
            $fileSize = filesize($result);
            header('Content-length:'.$fileSize);
            readfile($result);
            unlink($result);
            $cache->rm($locky);
            exit;
        }else{
            $result = $withdrawBiz->getSettleWithdrawByMid($mid,$month);
            header('Content-Type:text/html;charset=utf-8');
            header('Content-disposition:attachment;filename=blance.zip');
            $fileSize = filesize($result);
            readfile($result);
            header('Content-length:'.$fileSize);
            $cache->rm($locky);
            exit;
        }
    }
}
