<?php
/**
 * 管理员配置开放功能配置
 *
 * <AUTHOR>
 * @date 2016-10-14
 *
 */

namespace Controller\Admin;

use Business\JavaApi\Member\MemberQuery;
use Business\Member\MemberRelation;
use Business\NewJavaApi\Member\SubMerchant as SubMerchantApi;
use Library\Controller;

class Config extends Controller
{
    public function __construct()
    {
        $this->memberId = $this->isLogin('ajax');
    }

    /**
     * 搜索列表
     *
     * @param  int  $type  搜索类型 0 账号名称 1账号 2手机号 3商户ID 4 公司名称
     * @param  string  $keyword  关键词
     *
     * @return  array
     */
    public function getSearch()
    {
        $type      = \safe_str(I('type', ''));
        $keyword   = \safe_str(I('keyword', ''));
        $checkType = ["0", "1", "2", "3", "4"];
        if (!in_array($type, $checkType)) {
            $this->apiReturn(202, [], '参数有误请重新输入！');
        }
        $comName = '';
        $dname   = '';
        $fid     = 0;
        $mobile  = '';
        $account = '';
        if ($type == 0) {
            $dname = $keyword;
        } else if ($type == 1) {
            $account = $keyword;
        } else if ($type == 2) {
            $mobile = $keyword;
        } else if ($type == 3) {
            $fid = $keyword;
        } else if ($type == 4) {
            $comName = $keyword;
        }

        $memberQueryApi = new MemberQuery();
        $res            = $memberQueryApi->queryMemberIdBySearch($comName, $dname, $account, $fid, $mobile);
        if ($res['code'] != 200) {
            $this->apiReturn(204, [], '服务异常');
        }

        if ($res['data']) {
            $memberIdArray = array_column($res['data'], 'id');
            $accountRes = $memberQueryApi->batchMemberInfoByIds($memberIdArray);
            if ($accountRes['code'] == self::CODE_SUCCESS && !empty($accountRes['data'])){
                $accountRes = array_column($accountRes['data'], 'account', 'id');
            }
            foreach ($res['data'] as &$value){
                $value['account'] = $accountRes[$value['id']];
            }
            //产品价格日志 角色：供应商 获取下级用户
            if (I('post.permissions', 0, 'intval')) {
                $info = $this->getLoginInfo();

                //员工id
                $staffIds       = [];
                //子商户id
                $subMerchantIds = [];
                foreach ($res['data'] as $tmp) {
                    if ($tmp['dtype'] == 18) {
                        $subMerchantIds[] = $tmp['id'];
                        continue;
                    }

                    //非子商户即员工
                    $staffIds[] = $tmp['id'];
                }

                //员工错误标识
                $staffResErr = false;
                //子商户错误标识
                $subMerchantResErr = false;

                //员工判断处理
                if (!empty($staffIds)) {
                    $memberRelationBus = new MemberRelation();
                    //获取keyword的供应商id
                    $arr = $memberRelationBus->getMemberStaffInfoBySonIdToJava($staffIds[0], 'parent_id');
                    if ($arr['parent_id'] != $info['sid'] && $info['memberID'] != $staffIds[0]) {
                        $staffResErr = true;
                        //$this->apiReturn(200, [], '无结果！');
                    }
                }

                //子商户处理
                if (empty($subMerchantIds)) {
                    $subMerchantRes = (new SubMerchantApi())->getBatchMemberInfo($subMerchantIds);
                    if (!empty($subMerchantRes['data'])) {
                        $subMerchantData = $subMerchantRes['data'];
                        $subMerchantParentMap = array_column($subMerchantData, 'parentMemberId', 'memberId');
                        foreach ($res['data'] as $key => $val){
                            if (!isset($subMerchantParentMap[$val['id']])) {
                                continue;
                            }

                            $subMerchantParentId = $subMerchantParentMap[$val['id']];
                            if ($subMerchantParentId != $info['sid']) {
                                unset($res['data'][$key]);
                            }
                        }

                        $res['data'] = array_values($res['data']);

                        if (empty($res['data'])) {
                            $subMerchantResErr = true;
                        }
                    } else {
                        $subMerchantResErr = true;
                    }
                }

                //两个都错误的情况下，返回错误
                if ($staffResErr && $subMerchantResErr) {
                    $this->apiReturn(200, [], '无结果！');
                }
            }
            $this->apiReturn(200, [$res['data']], '成功！');
        } else {
            $this->apiReturn(200, [], '无结果！');
        }
    }
}