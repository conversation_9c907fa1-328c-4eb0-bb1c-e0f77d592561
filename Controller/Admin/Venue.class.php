<?php
/**
 * 演出场馆管理页面
 *
 *
 * <AUTHOR>
 * @date   2019-01-17
 */
namespace Controller\Admin;

use Library\Controller;
use \Model\Product\Show as ShowModel;

class Venue extends Controller
{

    //操作 1=添加操作 2=修改操作
    const ADD_ACTION    = 1;
    const UPDATE_ACTION = 2;

    public function __construct()
    {
        $this->isLogin('ajax');
    }


    /**
     * 更新场馆信息(新)
     * @param   integer     $venueId        场馆ID
     * @param   string      $venueName      场馆名称
     * @param   string      $venueThumb     场馆分布图
     * @param   array       $areaNameList   分区列表
     * @param   array       $showStockRule  库存显示规则
     * @return  array
     */
    public function getInfo()
    {
        $venueId = I('get.venue_id/d');

        if (!$venueId) {
            $this->apiReturn(202, [], '场馆id不能为空');
        }

        //演出模型
        $showModel = new ShowModel();
        //场馆基础信息
        $baseInfo  = $showModel->getVenuesInfo($venueId, 'venue_name, venue_thumb, apply_did');
        if (!$baseInfo) {
            $this->apiReturn(404, [], '无该场所信息');
        }

        //场馆分区信息
        $zoneInfo      = $showModel->getZoneList($venueId);
        //场馆库存显示规则
        $showStockRule = $showModel->getShowStockConfig($venueId);

        $data = [
            'venue_info'      => $baseInfo,
            'vanue_zone'      => $zoneInfo,
            'show_stock_rule' => $showStockRule
        ];

        $this->apiReturn(200, $data, '获取场馆信息成功');
    }


    /**
     * 新增修改场所
     * <AUTHOR>
     * @date    2019-01-18
     *
     * @param   integer     $venueId        场馆ID
     * @param   string      $venueName      场馆名称
     * @param   string      $venueThumb     场馆分布图
     * @param   string      $areaNameList   分区名称列表
     * @param   array       $areaIdList     分区id列表（编辑），为0新增
     * @param   array       $deleteList     分区id列表（删除）, 为0删除
     * @param   integer     $showStockRule  库存显示规则
     * @param   integer     $vagueShowA     模糊显示时小于该数值显示余票紧张
     * @param   integer     $vagueShowB     模糊显示时小于该数值显示仅剩当前库存数
     *
     * @return
     */
    public function update()
    {
        $venueId      = I('post.venue_id/d');
        $venueName    = I('post.venue_name/s');
        $venueThumb   = I('post.venue_thumb/s');
        $areaNameList = I('post.area_name_list/a');
        $areaIdList   = I('post.area_id_list/a', []);
        $deleteList   = I('post.area_delete/a', []);
        $action       = I('post.action/d', 0);

        $showStockRule = I('post.show_rule/d', ShowModel::SHOW_STOCK_REAL);
        $vagueShowA    = I('post.vague_show_a/d');
        $vagueShowB    = I('post.vague_show_b/d');

        if (!$venueName) {
            $this->apiReturn(202, [], '场馆名称不能为空');
        }

        if (!$areaNameList) {
            $this->apiReturn(202, [], '没有分区信息');
        }

        if (!$venueThumb) {
            $this->apiReturn(202, [], '请上传场馆分布图');
        }

        if (!in_array($action, [self::ADD_ACTION, self::UPDATE_ACTION])) {
            $this->apiReturn(202, [], '操作参数错误');
        }

        if (self::UPDATE_ACTION == $action && !$venueId) {
            $this->apiReturn(202, [], '场馆id参数缺失');
        }

        //管理员只可查看场馆数据
        if ($this->isSuper()) {
            $this->apiReturn(201, [], '管理员不能操作场馆数据');
        }

        $loginInfo    = $this->getLoginInfo('auto', false, false);
        //上级ID
        $memberId     = $loginInfo['sid'];
        //演出场所库存显示规则
        $showStockRule = [
            'show_rule'    => $showStockRule,
            'vague_show_a' => $vagueShowA,
            'vague_show_b' => $vagueShowB,
        ];

        $returnData = [];
        //演出模型
        $showModel  = new ShowModel();

        if (self::UPDATE_ACTION == $action) {//修改
            //场所信息
            $venueInfo = $showModel->getVenuesInfo($venueId, 'id as venue_id,apply_did,venue_name,venue_thumb');

            if (!$venueInfo) {
                $this->apiReturn(404, [], '不存在的场馆');
            }

            if ($venueInfo['apply_did'] != $memberId) {
                $this->apiReturn(400, [], '当前用户非场馆供应商');
            }

            //新增的分区信息
            $newZoneList    = [];
            //修改的分区信息
            $updateZoneList = [];
            //删除的分区信息
            $delZoneIdList  = [];

            foreach ($areaIdList as $key => $zoneId) {
                //分区名称
                $zoneName = $areaNameList[$key];

                if ($zoneId > 0) { //更新分区信息
                    // 1添加 0删除
                    $deleteList[$key] == 0 ? $delZoneIdList[] = $zoneId : $updateZoneList[] = ['zone_id' => $zoneId, 'zone_name' => $zoneName];
                } else {
                    //新增分区信息
                    $newZoneList[] = $zoneName;
                }
            }

            //更新
            $result = $showModel->updateVenue($venueId, $venueName, $venueThumb, $newZoneList, $updateZoneList, $delZoneIdList, $showStockRule);

            //有新增分区的情况  需要判断用户是否有开通分销库存开放功能 有开通的需要调用中台创建系统模板
            if ($result && $newZoneList) {
                $disStorageBiz = new \Business\PftShow\DisStorage();
                $disStorageBiz->createSystemTemplage($loginInfo['sid'], $loginInfo['memberID'], $venueId, $newZoneList);
            }

            //删除分区的情况  调用中台接口批量删除分区模板配置
            if ($result && $delZoneIdList) {
                $disStorageBiz = new \Business\PftShow\DisStorage();
                $itemTagArr = [];
                foreach ($delZoneIdList as $zoneId) {
                    $itemTagArr[] = $disStorageBiz::createItemTag($zoneId);
                }

                //批量删除分区模板配置
                //$deleteRes = $disStorageBiz->deleteItemTagStorageSetting($itemTagArr, $loginInfo['sid'], $loginInfo['memberID']);
                $deleteRes = $disStorageBiz->batchDeleteTemplate($itemTagArr, $loginInfo['sid'], $loginInfo['memberID']);
                if ($deleteRes['code'] != 200) {
                    pft_log('show_dis_storage', json_encode([
                        'ac'     => 'batchDeleteTemplate',
                        'params' => ['itemTagArr' => $itemTagArr, 'memberId' => $loginInfo['sid'], 'opid' => $loginInfo['memberID']],
                        'res'    => $deleteRes,
                    ], JSON_UNESCAPED_UNICODE), 2);
                }
            }

        } else {//新增
            //场所名称是否存在
            if ($showModel->isVenueExist($memberId, $venueName)) {
                $this->apiReturn(201, [], '场馆名称已存在');
            }

            $result     = $showModel->addVenue($memberId, $venueName, $venueThumb, $areaNameList, $showStockRule);
            $returnData = ['venue_id' => (int) $result];

            //有新增分区的情况  需要判断用户是否有开通分销库存开放功能 有开通的需要调用中台创建系统模板
            if ($result && $areaNameList) {
                $disStorageBiz = new \Business\PftShow\DisStorage();
                $disStorageBiz->createSystemTemplage($loginInfo['sid'], $loginInfo['memberID'], (int)$result, $areaNameList);
            }
        }

        //返回数据
        $result ? $this->apiReturn(200, $returnData, '更新成功') : $this->apiReturn(500, $returnData, '更新失败');
    }

}