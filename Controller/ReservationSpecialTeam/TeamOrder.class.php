<?php
/**
 * 特殊团队订单预约
 * Author: xwh
 * Date: 2021-03-25
 */

namespace Controller\ReservationSpecialTeam;

use Business\Authority\AuthContext;
use Business\JavaApi\TicketApi;
use Business\Order\OrderBook;
use Library\Controller;
use Library\SimpleExcel;
use Process\Order\OrderParams;
use Business\Authority\AuthLogic as AuthLogicBiz;

class TeamOrder extends Controller
{
    //用户的登录信息
    private $_memberInfo;
    private $_channel = 19;

    public function __construct()
    {
        $isLogin = $this->isLogin('auto', false);
        if (!$isLogin) {
            $this->apiReturn(204, [], '请先登录');
        }

        $this->_memberInfo = $this->getLoginInfo('auto');
    }

    /**
     * 特殊团队预约订单提交
     * date   2021-03-24
     * author xwh
     *
     * return
     */
    public function reservationSpecialTeamOrder()
    {
        $this->isLogin('auto');

        @pft_log('order/reservation/specialTeamOrder', json_encode($_REQUEST, JSON_UNESCAPED_UNICODE), $pathMode = 'day');

        //是否是下单权限
        $specialType = I('post.special_type', 0, 'intval');
        //权限校验  供应商与员工
        if (in_array($this->_memberInfo['dtype'],[0,6])) {
            $check = $this->_checkAuthoritySpecialTeamOrder($this->_memberInfo['memberID'],'reservation_special_team_order',$this->_memberInfo['dtype'], $specialType);
            if (!$check) {
                $this->apiReturn(204, [], '无权限');
            }
        }else{
            $this->apiReturn(204, [], '无权限');
        }

        //产品id
        $pid = I('post.pid', 0, 'intval');
        //供应商ID
        $aid = I('post.aid', 0, 'intval');
        //游玩时间
        $begintime = I('post.begintime', '', 'strval');
        //取票人姓名
        $ordername = I('post.ordername', 0, 'strval');
        //取票人联系方式
        $ordertel = I('post.contacttel', 0, 'strval');
        //合并付款门票参数
        $proidList = I('post.proid', []);

        $memberId     = $this->_memberInfo['sid'];
        $opid         = $this->_memberInfo['memberID'];
        $unitName     = I('post.unit_name', '', 'strval,trim');
        $specialLevel = I('post.special_level', 0, 'intval');



        $platformOrderBiz = new \Business\ReservationSpecialTeam\TeamOrder();

        //库存为0时可预约权限效验
        $authRes = $platformOrderBiz->checkBtnAuthMenuStorage($specialType, $opid, $proidList, $memberId, $this->_memberInfo['dtype']);
        if (!$authRes) {
            $this->apiReturn(400, [], '无权限！');
        }

        $orderRes         = $platformOrderBiz->reservationSpecialTeamOrder($memberId, $opid, $pid, $aid, $begintime, $ordername, $unitName, $specialLevel, $specialType, $ordertel, $proidList);

        $this->apiReturn($orderRes['code'], $orderRes['data'], $orderRes['msg']);
    }



    /**
     * 根据条件获取特殊团队接待订单信息
     * @author: xwh
     * @date: 2021/3/23
     */
    public function querySpecialTeamOrderList()
    {
        $sid       = $this->_memberInfo['sid'];
        //特殊团队类型
        $type      = I('post.type', -1, 'intval');
        $timeType  = I('post.time_type', -1, 'intval');
        $startTime = I('post.start_time', '', 'strval,trim');
        $endTime   = I('post.end_time', '', 'strval,trim');
        $status    = I('post.status', -1, 'intval');
        $lid       = I('post.lid', 0, 'intval');
        $tid       = I('post.tid', 0, 'intval');
        $page      = I('post.page', 1, 'intval');
        $size      = I('post.size', 10, 'intval');
        $keyword   = I('post.key', '', 'strval,trim');
        $word      = I('post.word', '', 'strval,trim');
        $sortName  = I('post.sort_name', '', 'strval,trim');
        $sortType  = I('post.sort_type', -1, 'intval');
        if (!in_array($keyword,['phone','unitName','name','ordernum'])) {
            $this->apiReturn(203, [], '参数错误');
        }
        $key = ['phone' => '','ordernum' => '','name' => '','unitName' => ''];
        $key[$keyword] = $word;

        $result = (new \Business\ReservationSpecialTeam\TeamOrder())->querySpecialTeamOrderList($sid, $type, $timeType, $startTime, $endTime, $status, $key['ordernum'], $lid, $tid,  $key['phone'], $key['unitName'], $key['name'], $page, $size, $sortName, $sortType);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }
    private function _checkAuthoritySpecialTeamOrder($mid, $menu, $dtype, $specialType)
    {
        if (!$mid || !$menu || !in_array($dtype,[0,6]) || !in_array($specialType,[0,1])) {
            return false;
        }
        if (!$dtype) {
            $isOpen = (new \Business\AppCenter\Module())->checkUserIsCanUseApp($mid, 'reservation_special_team_order',false);
            if (!$isOpen) {
                $this->apiReturn(204, [], '无权限');
            }

        }
        if ($dtype == 6){
            $specialTypes = [
                0 => "governmentReception",    //政务接待
                1 => "cadreAcademy",        //干部学院
            ];

            $res = (new AuthLogicBiz())->resource($this->_memberInfo['sid'], $mid, $menu, $dtype, 1);
            if (!in_array($specialTypes[$specialType],$res)) {
                return false;
            }
        }
        return true;
    }
    /**
     * 获取票填写更多信息
     * @author: xwh
     * @date: 2021/3/23
     */
    public function getTicketMoveInfo()
    {
        $ticketId   = I('post.ticket_id', '', 'intval');
        $landId     = I('post.land_id', '', 'intval');

        if (!$ticketId || !$landId) {
            $this->apiReturn(201, '', '景区ID与门票ID和供应商ID必传');
        }
        $memberId       = $this->_memberInfo['sid'];
        //获取门票数据
        $data = TicketApi::getBookTickets($memberId, $memberId, $landId, $ticketId, 10);
        if ($data['code'] != 200) {
            $this->apiReturn($data['code'], '', $data['sub_msg']);
        }

        $ticketData = $data['data'];
        if (empty($ticketData)) {
            $this->apiReturn(400, [], '产品未通过审核或无权限购买');
        }

        $data = [];
        $result = (new OrderBook())->handleMoveInfo($ticketData,$ticketId);
        if ($result) {
            $data['type']                    = $result['type'];
            $data['more_credential_content'] = $result['more_credential_content'] ?? [];
            $data['son_ticket']              = $result['son_ticket'] ?? [];
        }
        $this->apiReturn(200, $data, '成功');
    }

    public function orderExcel()
    {
        $date              = I('date', 0, 'intval');
        $lid               = I('lid', 0, 'intval');
        $tid               = I('tid', 0, 'intval');
        $operateId         = I('operate_id', 0, 'intval');
        $participatePeriod = I('participate_period', '', 'strval,trim');

        $excel = [[
            '预约订单号',
            '接待产品及票种',
            '联系人',
            '手机号',
            '班级/单位名称',
            '级别',
            '人数',
            '参观日期',
            '参观时段',
            '录入时间',
            '填报人',
            '核销人',
            '状态',
            '更多',
            '备注',]
        ];
        $filename = '特殊团队预约接待明细';
        if ($date && $lid) {
            $result = (new \Business\ReservationSpecialTeam\TeamOrder())->excelList($date, $date, $this->_memberInfo['sid'], strval($lid), 0, strval($tid), strval($operateId), $participatePeriod,0,0);
            if ($result['code'] == 200) {
                $excel = array_merge($excel, $result['data']['list']);
            }
        }

        $xls = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
    }

}