<?php
/**
 * 特殊团队订单预约
 * Author: xwh
 * Date: 2021-03-25
 */

namespace Controller\ReservationSpecialTeam;

use Business\Authority\AuthContext;
use Business\JavaApi\TicketApi;
use Business\Order\OrderBook;
use Library\Controller;
use Process\Order\OrderParams;

class Member extends Controller
{
    //用户的登录信息
    private $_memberInfo;

    public function __construct()
    {
        $isLogin = $this->isLogin('auto', false);
        if (!$isLogin) {
            $this->apiReturn(204, [], '请先登录');
        }

        $this->_memberInfo = $this->getLoginInfo('auto');
    }

    /**
     * 获取当前用户特殊团队预约常用联系人
     * <AUTHOR>
     * @date 2021/8/12
     */
    public function getContactsList()
    {
        $sid  = $this->_memberInfo['sid'];
        $page = I('post.page', 1, 'intval');
        $size = I('post.size', 30, 'intval');

        $result = (new \Business\ReservationSpecialTeam\Member())->getContactsList($sid, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据id删除特殊团队预约常用联系人
     * <AUTHOR>
     * @date 2021/8/12
     */
    public function delContacts()
    {
        $sid = $this->_memberInfo['sid'];
        $id  = I('post.id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(203, '', '参数错误');
        }
        $result = (new \Business\ReservationSpecialTeam\Member())->delContacts($sid, $id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 创建特殊团队预约常用联系人
     * <AUTHOR>
     * @date  2021-03-22
     * @throws
     */
    public function addContacts()
    {
        $sid      = $this->_memberInfo['sid'];
        $opid     = $this->_memberInfo['memberID'];
        $name     = I('post.name', '', 'strval,trim');
        $tel      = I('post.tel', '', 'strval,trim');
        $unitname = I('post.unitname', '', 'strval,trim');
        $level    = I('post.level', 0, 'intval');
        if (!$name || !$tel || !$unitname) {
            $this->apiReturn(203, '', '参数错误');
        }
        if (!ismobile($tel)) {
            $this->apiReturn(203, '', '手机号参数错误');
        }
        $result = (new \Business\ReservationSpecialTeam\Member())->addContacts($sid, $opid, $name, $tel, $unitname,
            $level);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}