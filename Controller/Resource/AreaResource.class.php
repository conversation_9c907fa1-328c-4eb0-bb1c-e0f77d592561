<?php

namespace Controller\Resource;

use Library\Controller;
use Model\Product\Area;
use Process\Resource\AreaFilter\AreaLikeSettle;
use Process\Resource\AreaFilter\AreaPinyinSettle;
use Process\Resource\AreaProcess;

/**
 * @Author: CYQ19931115
 * @Date:   2017-09-22 15:50:01
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2017-11-24 11:27:14
 */
class AreaResource extends Controller
{
    /** @var Model\Product\Area 地区model */
    private $area;
    private $overSea;

    public function __construct()
    {
        $isOverSea     = I('oversea', 0, 'intval');
        $this->overSea = $isOverSea;
        $this->area    = new Area($this->overSea);

    }

    /**
     * 获取省市二级包含的数据
     * <AUTHOR>
     * @DateTime 2017-09-22T16:02:06+0800
     * @return
     */
    public function secondLevelArea()
    {
//        if ($this->overSea == 0) {
//            $result = $this->area->getAreaCache("area:secondLevelArea");
//            if (!$result) {
//                $result = AreaProcess::childerCity($this->area);
//                $this->area->setAreaCache("area:secondLevelArea", $result);
//            }
//        }
//        if ($this->overSea == 1) {
//            $result = $this->area->getAreaCache("area:overSeaArea");
//            if (!$result) {
//                $result = AreaProcess::childerCity($this->area);
//                $this->area->setAreaCache("area:overSeaArea", $result);
//            }
//
//        }
//        if ($this->overSea == 2) {
//            $result = $this->area->getAreaCache("area:allArea");
//            if (!$result) {
//                $result = AreaProcess::childerCity($this->area);
//                $this->area->setAreaCache("area:allArea", $result);
//            }
//
//        }
//        //省份id排序
//        usort($result, function ($area1, $area2) {
//            if ($area1['area_id'] > $area2['area_id']) {
//                return 1;
//            } else {
//                return -1;
//            }
//        });
//        $this->apiReturn(200, $result, "获取地址成功");
        $all     = false;
        $AreaApi = new \Business\JavaApi\Area\Area();

        if ($this->overSea == 0) {
            $result = $this->area->getAreaCache("area:secondLevelArea");
            if (!$result) {
                $result = $AreaApi->tree(2, 86, $all);
                if ($result['code'] != 200 && empty($result['data'])) {
                    $this->apiReturn($result['code'], [], $result['msg']);
                }
                $result = $result['data'];
                $this->area->setAreaCache("area:secondLevelArea", $result);
            }
        }
        if ($this->overSea == 1) {
            $result = $this->area->getAreaCache("area:overSeaArea");
            if (!$result) {
                $result = $AreaApi->tree(2, -86, $all);
                if ($result['code'] != 200 && empty($result['data']['list'])) {
                    $this->apiReturn($result['code'], [], $result['msg']);
                }
                $result = $result['data'];
                $this->area->setAreaCache("area:overSeaArea", $result);
            }
        }
        if ($this->overSea == 2) {
            $all    = true;
            $result = $this->area->getAreaCache("area:allArea");
            if (!$result) {
                $result = $AreaApi->tree(2, null, $all);
                if ($result['code'] != 200 && empty($result['data']['list'])) {
                    $this->apiReturn($result['code'], [], $result['msg']);
                }
                $result = $result['data'];
                $this->area->setAreaCache("area:allArea", $result);
            }
        }

        //省份id排序
        usort($result, function ($area1, $area2) {
            if ($area1['area_id'] > $area2['area_id']) {
                return 1;
            } else {
                return -1;
            }
        });
        $this->apiReturn(200, $result, "获取地址成功");
    }

    /**
     * 获取区县级数据
     * author queyourong
     * date 2022/10/27
     */
    public function townLevelArea()
    {
        $superiorId = I('superior_id', 0);

        $AreaApi = new \Business\JavaApi\Area\Area();

        //$result = $this->area->getAreaCache("area:townLevelArea");
        //if (!$result) {
        //    $result = $AreaApi->findByParentAreaCodeId($superiorId);
        //    if ($result['code'] != 200 && empty($result['data'])) {
        //        $this->apiReturn($result['code'], [], $result['msg']);
        //    }
        //    $result = $result['data'];
        //    $this->area->setAreaCache("area:townLevelArea", $result);
        //}

        if (strpos($superiorId, ',')) {
            $superiorId  = explode(',', $superiorId);
            $result = ['data'=>[]];
            foreach ($superiorId as $id) {
                $r = $AreaApi->findByParentAreaCodeId($id);
                if ($r['code'] == 200) {
                    $result['data'][$id] = $r['data'];
                }
            }
        } else {
            $result = $AreaApi->findByParentAreaCodeId($superiorId);
        }

        $this->apiReturn(200, $result['data'], "获取地址成功");
    }

    /**
     * 获取所有省市数据
     * <AUTHOR>
     * @DateTime 2017-09-22T16:02:21+0800
     * @return
     */
    public function oneLevelProvince()
    {
        $data = $this->area->data[0];
        $this->apiReturn(200, $data, "获取地址成功");
    }

    /**
     * 获取城市地址通过省市分组
     * <AUTHOR>
     * @DateTime 2017-10-04T11:04:41+0800
     * @return   [type] [description]
     */
    public function oneLevelCity()
    {
        $data = $this->area->data[1];
        $this->apiReturn(200, $data, "获取地址成功");
    }

    /**
     * 获取所有id和地区名称
     * <AUTHOR>
     * @DateTime 2017-10-01T18:17:32+0800
     * @return
     */
    public function getAllAreas()
    {
        $data = $this->area->areas;
        $this->apiReturn(200, $data, "获取地址成功");
    }

    /**
     * 获取乡镇列表
     * <AUTHOR>
     * @DateTime 2017-10-04T16:33:20+0800
     * @return
     */
    public function getTown()
    {
        $data = $this->area->town;
        $this->apiReturn(200, $data, "获取地址成功");
    }

    /**
     * 获取县一级列表
     * <AUTHOR>
     * @date   2018-05-07
     */
    public function getTownList()
    {

        //上级地区id
        $superiorId = I('superior_id', 0, 'intval');
        if (!$superiorId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $data = $this->area->getTownList($superiorId);
        $this->apiReturn(200, $data);
    }

    /**
     * 获取多个县一级列表
     * Create by zhangyangzhen
     * Date: 2018/8/12
     * Time: 14:36
     */
    public function getTownListArr()
    {
        //上级地区id
        $superiorId = I('superior_id', 0);
        if (!$superiorId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $data = $this->area->getTownListArr($superiorId);
        $this->apiReturn(200, $data);
    }


    /**
     * 通过供应商id获取景区的地址
     * <AUTHOR>
     * @DateTime 2017-10-04T12:03:31+0800
     * @return
     */
    public function getAreaListByMember()
    {
        $supply_id = I("condition_user");
        $keyword   = I("keyword");
        if ($supply_id) {
            $supply_id = AreaProcess::getUserMemberInWxPlatformByUrl();
        }
        //如果supply_id为空就返回所有的
        $areaList = AreaProcess::getAreaByMember($this->area, 0);
        //拼音过滤
        AreaPinyinSettle::filtration($areaList, $keyword);
        //地区名称like过滤
        AreaLikeSettle::filtration($areaList, $keyword);
        //按照拼音分组
        AreaPinyinSettle::clearUp($areaList);
        $this->apiReturn(200, $areaList, "获取成功");
    }

    /**
     * 客源地搜索
     * <AUTHOR>
     * @DateTime 2017-10-01T11:25:42+0800
     * @return   返回匹配的客源地
     */
    public function searchPhoenixAddress()
    {
        $keyword = I("keyword");
        $list    = $this->area->getAreaCache("area:searchPhoenixAddress");
        if (!$list) {
            $list = AreaProcess::phoenixAddress($this->area);
            $this->area->setAreaCache("area:searchPhoenixAddress", $list);
        }
        if (!$keyword) {
            $this->apiReturn(200, $list, "全部的县级地区");
        }
        $list = AreaProcess::searchKeyWord($list, $keyword);
        $this->apiReturn(200, $list, "搜索");
    }

    /**
     * 清除缓存数据
     * <AUTHOR>
     * @DateTime 2017-09-24T10:59:34+0800
     * @return
     */
    public function clearCache()
    {
        $isLogin = $this->isLogin();
        if (!$isLogin) {
            $this->apiReturn(500, [], "未登录无法清除缓存");
        }
        $wwwProCityKey = md5("pro_and_city");
        foreach (
            [
                "area:overSeaArea",
                "area:pro_and_city",
                "area:pro_and_city_one_list",
                "area:town_list",
                "area:secondLevelArea",
                //"area:townLevelArea",
                "area:searchPhoenixAddress",
                "area:sea_pro_and_city_one_list",
                "area:all_pro_and_city_one_list",
                "area:sea_pro_and_city",
                "area:all_pro_and_city",
                "area:allArea",
                "$wwwProCityKey",
            ] as $value
        ) {
            $this->area->clearAreaCache($value);
        }
        $this->apiReturn(200, [], "清除成功");
    }

    /**
     * 获取地区列表 --地区管理
     * <AUTHOR>
     * @date 2020/6/12
     *
     * @return array
     */
    public function getArea()
    {
        // 86 境内 -86 境外
        $type       = I('area_type', null, 'intval');
        $keyword    = I('key_word', null, 'strval,trim');
        $parentCode = I('parent_code', null, 'intval');
        $areaCode   = I('area_code', null, 'intval');
        $page       = I('page', null, 'intval');
        $size       = I('size', null, 'intval');

        if (!is_chinese($keyword)) {
            $this->apiReturn(400, [], "请输入正确地区名");
        }

        $AreaApi = new \Business\JavaApi\Area\Area();
        $result  = $AreaApi->getList($keyword, $type, null, $parentCode, $areaCode, $page, $size);
        if ($result['code'] == 200 && !empty($result['data']['list'])) {
            foreach ($result['data']['list'] as &$areaOneData) {
                $areaOneData['children'] = [];
                if ($areaOneData['areaType'] != 3) {
                    $childrentwo = $AreaApi->getList(null, null, null, $areaOneData['areaCode'], null, null, null);
                    if ($childrentwo['code'] == 200 && !empty($childrentwo['data']['list'])) {
                        $areaOneData['children'] = $childrentwo['data']['list'];
                    }
                }
            }
        }

        $this->apiReturn($result['code'], $result['data'], "");
    }

    /**
     * 编辑地区 --地区管理
     * <AUTHOR>
     * @date 2020/6/12
     *
     * @return array
     */
    public function editArea()
    {
        $super = $this->isSuper();
        if (!$super) {
            $this->apiReturn(202, [], "非管理员");
        }

        $areaId   = I('area_id', null, 'intval');
        $areaName = I('area_name', null, 'strval,trim');

        if (empty($areaId)) {
            $this->apiReturn(400, [], "参数错误");
        }

        if (!empty($areaName) && !is_chinese($areaName)) {
            $this->apiReturn(400, [], "参数错误");
        }

        $getInfo = $this->getLoginInfo();
        $AreaApi = new \Business\JavaApi\Area\Area();
        $result  = $AreaApi->update($areaId, $getInfo['memberID'], $areaName);
        if ($result['code'] == 200) {
            //清空缓存
            $this->_clearCache();
        }
        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 新增地区列表 --地区管理
     * <AUTHOR>
     * @date 2020/6/12
     *
     * @return array
     */
    public function addArea()
    {
        $super = $this->isSuper();
        if (!$super) {
            $this->apiReturn(202, [], "非管理员");
        }
        $getInfo   = $this->getLoginInfo();
        $areaType  = I('area_type', null, 'intval');
        $areaFid   = I('area_fid', null, 'intval');
        $areaName  = I('area_name', null, 'strval,trim');
        $areaCode  = I('area_code', null, 'intval');
        $areaState = I('area_state', null, 'intval');
        if ($areaFid == null || empty($areaName) || empty($areaState) || empty($areaType) || !is_chinese($areaName) || !in_array($areaType, [1, 2, 3])) {
            $this->apiReturn(400, [], "参数错误");
        }

        $AreaApi = new \Business\JavaApi\Area\Area();
        $result  = $AreaApi->create($areaName, $areaCode, $areaFid, $areaType, $areaState, '', $getInfo['memberID']);
        if ($result['code'] == 200) {
            //清空缓存
            $this->_clearCache();
        }
        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 删除地区列表 --地区管理
     * <AUTHOR>
     * @date 2020/6/12
     *
     * @return array
     */
    public function delArea()
    {
        $super = $this->isSuper();
        if (!$super) {
            $this->apiReturn(202, [], "非管理员");
        }
        $getInfo  = $this->getLoginInfo();
        $areaCode = I('area_code', null, 'intval');
        if (empty($areaCode)) {
            $this->apiReturn(400, [], "参数错误");
        }

        $AreaApi = new \Business\JavaApi\Area\Area();
        $result  = $AreaApi->delete($areaCode, $getInfo['memberID']);
        if ($result['code'] == 200) {
            //清空缓存
            $this->_clearCache();
        }
        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 获取地区树
     * <AUTHOR>
     * @date 2020/6/12
     *
     * @return array
     */
    public function tree()
    {
        //最大深度 相当于地区类型;地址类型 1省 直辖市 2 市区 3 区县
        $maxDepth  = I('max_depth', 1, 'intval');
        $areaState = I('area_state', 86, 'intval');
        if (empty($maxDepth)) {
            $this->apiReturn(400, [], "参数错误");
        }

        $AreaApi = new \Business\JavaApi\Area\Area();
        $result  = $AreaApi->tree($maxDepth, $areaState);
        //省份id排序
        usort($result['data'], function ($area1, $area2) {
            if ($area1['area_id'] > $area2['area_id']) {
                return 1;
            } else {
                return -1;
            }
        });
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据地区类型地区列表 --地区管理
     * 地区类型 1省;2 市区 3 区县
     * <AUTHOR>
     * @date 2020/6/12
     *
     * @return array
     */
    public function getAreaByType()
    {
        // 86 境内 -86 境外
        $areaState = I('area_types', null, 'intval');
        //地区类型 ;2 市区 3 区县
        $areaType   = I('area_type', null, 'intval');
        $parentCode = I('parent_code', null, 'intval');
        $page       = I('page', null, 'intval');
        $size       = I('size', null, 'intval');
        if (!$areaState || !$areaType) {
            $this->apiReturn(400, [], "参数错误");
        }
        $AreaApi = new \Business\JavaApi\Area\Area();
        $result  = $AreaApi->getList(null, $areaState, $areaType, $parentCode, null, $page, $size);

        $this->apiReturn($result['code'], $result['data'], "");
    }

    /**
     * 清除缓存数据
     * <AUTHOR>
     * @DateTime 2020/8/13
     * @return
     */
    private function _clearCache()
    {
        $wwwProCityKey = md5("pro_and_city");
        foreach (
            [
                "area:overSeaArea",
                "area:pro_and_city",
                "area:pro_and_city_one_list",
                "area:town_list",
                "area:secondLevelArea",
                //"area:townLevelArea",
                "area:searchPhoenixAddress",
                "area:sea_pro_and_city_one_list",
                "area:all_pro_and_city_one_list",
                "area:sea_pro_and_city",
                "area:all_pro_and_city",
                "area:allArea",
                "$wwwProCityKey",
            ] as $value
        ) {
            $this->area->clearAreaCache($value);
        }
    }

}
