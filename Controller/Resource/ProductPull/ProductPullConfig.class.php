<?php

namespace Controller\Resource\ProductPull;

use Exception;
use Model\Ota\SysConfig;

trait ProductPullConfig
{
    protected function getSysNameById(int $conSysId): string
    {
        $sysMap = $this->getSysMap();
        return $sysMap[$conSysId] ?? '';
    }

    /**
     * @throws Exception
     */
    protected function checkSystemStatus(int $conSysId)
    {
        $hasSysById = $this->hasSysById($conSysId);
        if (!$hasSysById) {
            throw new Exception('con_sys_id 有误');
        }

        $sysConfig     = new SysConfig();
        $field         = 'status,system_status';
        $sysMemberInfo = $sysConfig->getSysMemberByMemberIdAndCysId($this->sid, $conSysId, $field);
        if (empty($sysMemberInfo)) {
            throw new Exception('未绑定系统');
        }
        if ($sysMemberInfo['status'] == 1) {
            throw new Exception('系统已解绑');
        }
        if ($sysMemberInfo['system_status'] == 2) {
            throw new Exception('系统功能受限');
        }
    }

    protected function hasSysById(int $conSysId): bool
    {
        $sysConfig     = new SysConfig();
        $field         = 'id,sys_type';
        $sysConfigInfo = $sysConfig->getSysConfigInfo($conSysId, $field);
        $this->sysType = $sysConfigInfo['sys_type'];
        if ($this->sysType == 'Q') {
            return true;
        }

        $sysMap = $this->getSysMap();
        return array_key_exists($conSysId, $sysMap);
    }

    protected function getSysUrlById(int $conSysId): string
    {
        $sysName   = $this->getSysNameById($conSysId);
        $sysUrlMap = $this->getSysUrlMap();
        return $sysUrlMap[$sysName] ?? '';
    }

    protected function hasSysUrlById(int $conSysId): bool
    {
        $sysName   = $this->getSysNameById($conSysId);
        $sysUrlMap = $this->getSysUrlMap();
        return array_key_exists($sysName, $sysUrlMap);
    }

    /**
     * 请求的 api_secret 转化
     *
     * <AUTHOR>
     * @date 2022-05-20
     *
     * @param int $conSysId
     * @param string $apiSecret
     *
     * @return array
     */
    protected function requestApiSecretSwap(int $conSysId, string $apiSecret): array
    {
        $apiSecretList    = explode('|', $apiSecret);
        $sysApiSecretList = $this->getSysApiSecretList($conSysId);
        $apiSecretMap     = [];
        foreach ($sysApiSecretList as $key => $value) {
            if (isset($apiSecretList[$key])) {
                $apiSecretMap[$value] = trim($apiSecretList[$key]);
            }
        }

        if (count($apiSecretList) != count($sysApiSecretList)) {
            return [];
        }

        return $apiSecretMap;
    }

    /**
     * 响应的 api_secret 转化
     *
     * <AUTHOR>
     * @date 2022-05-20
     *
     * @param array $apiSecret
     *
     * @return string
     */
    protected function responseApiSecretSwap(array $apiSecret): string
    {
        $apiSecretList = [];
        foreach ($apiSecret as $value) {
            $apiSecretList[] = $value;
        }
        if (count($apiSecretList) > 1) {
            return implode('|', $apiSecretList);
        } else {
            return implode('', $apiSecretList);
        }
    }

    /**
     * 系统标识映射
     *
     * <AUTHOR>
     * @date 2022-05-20
     *
     * @return string[]
     */
    protected function getSysMap(): array
    {
        if (ENV == 'LOCAL' || ENV == 'DEVELOP') {
            $map = [
                7   => 'ziwoyou',
                20  => 'lvmama',
                68  => 'xiecheng',
                272 => 'xinmeida',
            ];
        } elseif (ENV == 'TEST') {
            $map = [
                7   => 'ziwoyou',
                20  => 'lvmama',
                68  => 'xiecheng',
                299 => 'xinmeida',
                515 => 'ctripgds',
            ];
        } else {
            $map = [
                7   => 'ziwoyou',
                20  => 'lvmama',
                68  => 'xiecheng',
                302 => 'xinmeida',
                529 => 'ctripgds',
            ];
        }

        return $map;
    }

    /**
     * 不需要动态配置 url 的系统对应的 url 映射
     *
     * <AUTHOR>
     * @date 2022-05-20
     *
     * @return string[]
     */
    protected function getSysUrlMap(): array
    {
        return [
            'lvmama'   => 'http://api.lvmama.com/distributorApi/2.0/api/',
            'xiecheng' => 'https://OpenServiceAuth.Ctrip.com',
            'xinmeida' => 'https://fenxiao.meituan.com/opdtor/api',
        ];
    }

    /**
     * apiSecret 入库后包含的字段
     *
     * <AUTHOR>
     * @date 2022-05-20
     *
     * @param int $conSysId
     *
     * @return array|string[]
     */
    protected function getSysApiSecretList(int $conSysId): array
    {
        $apiSecretMap = [
            'ziwoyou'  => [
                'api_key',
                'base_uri',
            ],
            'lvmama'   => [
                'secret',
            ],
            'xiecheng' => [
                'sid',
                'key',
            ],
            'xinmeida' => [
                'access_key',
                'secret_key',
            ],
        ];

        if (strpos($this->sysType, 'A') !== false) {
            $apiSecret = $apiSecretMap[$this->getSysNameById($conSysId)] ?? [];
        } else {
            $apiSecret = [
                'api_secret',
            ];
        }

        return $apiSecret;
    }

    /**
     * 获取系统配置对应页面描述
     *
     * <AUTHOR>
     * @date 2022-05-20
     *
     * @param int $conSysId
     *
     * @return array
     */
    protected function getSysViewDescById(int $conSysId): array
    {
        $configViewDescMap = [
            'ziwoyou'  => [
                'api_key_desc'    => '（填写分销商账号）',
                'api_secret_desc' => '（填写分销商验证码和分销域名, 二者以|隔开）',
            ],
            'lvmama'   => [
                'api_key_desc'    => '（填写分销商编号）',
                'api_secret_desc' => '（填写分销商密钥）',
            ],
            'xiecheng' => [
                'api_key_desc'    => '（填写 aid, 联盟id）',
                'api_secret_desc' => '（填写 sid 和 key , 二者以|隔开）',
            ],
            'xinmeida' => [
                'api_key_desc'    => '（填写 partnerId, 平台分配给分销商的分销业务ID）',
                'api_secret_desc' => '（填写 accessKey 安全凭证公钥 和 secretKey 安全凭证秘钥, 二者以|隔开）',
            ],
        ];

        if (strpos($this->sysType, 'A') !== false) {
            $configViewDesc = $configViewDescMap[$this->getSysNameById($conSysId)] ?? [];
        } else {
            $configViewDesc = [
                'api_key_desc'    => 'client_id',
                'api_secret_desc' => 'client_id(同上)',
            ];
        }

        return $configViewDesc;
    }
}