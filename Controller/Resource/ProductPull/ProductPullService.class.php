<?php

namespace Controller\Resource\ProductPull;

use Business\Ota\SystemConfig as BizOtaConf;
use Library\JsonRpc\PftRpcClient;
use Library\JsonRpc\TVRpcClient;
use Library\Tools\Helpers;
use Model\Ota\SysConfig;
use Model\Ota\UpStreamResourceModel;
use Exception;

trait ProductPullService
{
    /**
     * 检测账号秘钥
     *
     * <AUTHOR>
     * @date 2021-12-08
     *
     * @param int $conSysId
     *
     * @return array
     */
    public function pullCheckService(int $conSysId = 0): array
    {
        try {
            if ($this->sysType == 'Q') {
                $sysConfig = new SysConfig();

                $sysNotifyField   = 'notify_url, notify_type';
                $sysNotifyInfoArr = $sysConfig->getSysConfigNotifyInfo($conSysId, 3, $sysNotifyField);
                if (empty($sysNotifyInfoArr['notify_url'])) {
                    throw new Exception('票付通内部系统:未配置对接系统通知地址');
                }

                $res = Helpers::callOpenApiSystemJsonRpc('ServerConfig.checkAlive', [
                    'url' => $sysNotifyInfoArr['notify_url'],
                ]);
                $msg = $res['msg'];
            } else {
                $method      = $this->getSysNameById($conSysId) . '/pullCheck';
                $jsonRpcData = [
                    $this->sid, // 供应商 id
                ];
                $lib         = new PftRpcClient('resource_pull_product');
                $res         = $lib->call($method, $jsonRpcData, '');
                $msg         = $res['message'];
            }

            $data = $res['data'];
            $code = $res['code'];
        } catch (Exception $e) {
            $msg  = $e->getMessage();
            $code = $e->getCode();
            $data = [];
        }

        return [
            'code' => $code,
            'msg'  => $msg,
            'data' => $data,
        ];
    }

    /**
     * 拉取产品
     *
     * <AUTHOR>
     * @date 2021-11-29
     *
     * @param int $conSysId
     * @param array $thirdProductIdList
     * @param int $page
     *
     * @return bool|void
     */
    public function pullProductService(int $conSysId = 0, array $thirdProductIdList = [], int $page = 1)
    {
        try {
            if ($this->sysType == 'Q') {
                $firstThirdProductId = current($thirdProductIdList);
                $code                = 200;
                $msg                 = 'success';
                $productMap          = [
                    'product_map' => [],
                    'ticket_list' => [],
                    'total'       => 0,
                ];
                $data                = [];
                $rpcData             = [
                    'merchant_id'      => $this->sid,
                    'third_partner_id' => $conSysId,
                    'third_spu_id'     => $firstThirdProductId,
                    'page'             => $page,
                    'page_size'        => 10,
                ];
                $lib                 = new TVRpcClient('travel_voucher_service');
                $res                 = $lib->call('intranet/v1/third_product/list', $rpcData, 'scenic');
                if (!empty($res['list'])) {
                    foreach ($res['list'] as $value) {
                        if (empty($productMap['product_map'])) {
                            $productMap['product_map'] = [
                                'pft_product_id'   => 0,
                                'third_product_id' => $firstThirdProductId,
                                'title'            => $value['third_spu_name'],
                            ];
                        }

                        $status = $value['status'];
                        if ($value['operate_status'] == 1) {
                            $status = 0; // 处理中
                        }
                        if ($status == 1) { // 未绑定
                            $status = 2; // 已绑定
                        } elseif ($status == 2) { // 已绑定
                            $status = 1; // 未绑定
                        }
                        $productMap['ticket_list'][] = [
                            'pft_ticket_id'   => 0,
                            'status'          => $status,
                            'third_ticket_id' => $value['third_sku_id'],
                            'title'           => $value['third_sku_name'],
                            'uuid'            => $value['third_code'],
                        ];
                    }
                    $productMap['total'] = $res['total'];
                }
                $data[] = $productMap;
            } else {
                $method = $this->getSysNameById($conSysId) . '/pullProduct';

                foreach ($thirdProductIdList as &$thirdProductId) {
                    $thirdProductId = trim($thirdProductId);
                }

                $jsonRpcData = [
                    $this->sid, // 供应商 id
                    $this->memberId, // 登录用户 id
                    $thirdProductIdList, // 第三方产品 id 列表
                ];
                $lib         = new PftRpcClient('resource_pull_product');
                $res         = $lib->call($method, $jsonRpcData, '');
                $msg         = $res['message'];
                $data        = $res['data'];
                foreach ($data as $key => $value) {
                    $data[$key]['total'] = count($value['ticket_list']);
                }
                $code = $res['code'];
            }

        } catch (Exception $e) {
            $msg  = $e->getMessage();
            $code = $e->getCode();
            $data = [];
        }

        return $this->apiReturn($code, $data, $msg);
    }

    /**
     * 创建产品
     *
     * <AUTHOR>
     * @date 2021-11-29
     *
     * @param int $conSysId
     * @param array $thirdProductIdList
     * @param array $thirdTicketIdMap
     * @param int $opType
     *
     * @return bool|void
     * @throws Exception
     */
    public function createProductService(int $conSysId = 0, array $thirdProductIdList = [],
        array $thirdTicketIdMap = [], int $opType = 0)
    {
        try {
            if ($this->sysType == 'Q') {
                $firstThirdProductId = current($thirdProductIdList);
                $rpcData             = [
                    'merchant_id'      => $this->sid,
                    'third_partner_id' => $conSysId,
                    'third_spu_id'     => $firstThirdProductId,
                    'third_sku_ids'    => $thirdTicketIdMap[$firstThirdProductId],
                    'operator_id'      => $this->memberId,
                ];
                $lib                 = new TVRpcClient('travel_voucher_service');
                if ($opType == 0) {
                    $method = 'intranet/v1/third_product/pull';
                } else {
                    $method = 'intranet/v1/third_product/update';
                }

                $lib->call($method, $rpcData, 'scenic');
                $code = 200;
                $msg  = 'success';
                $data = [];
            } else {
                $method = $this->getSysNameById($conSysId) . '/createProduct';

                foreach ($thirdProductIdList as &$thirdProductId) {
                    $thirdProductId = trim($thirdProductId);
                    if (!isset($thirdTicketIdMap[$thirdProductId])) {
                        $thirdTicketIdMap[$thirdProductId] = ['*'];
                    }
                }

                $jsonRpcData = [
                    $this->sid, // 供应商 id
                    $this->memberId, // 登录用户 id
                    $thirdProductIdList, // 第三方产品 id 列表
                    $thirdTicketIdMap, // 第三方门票 id 映射
                    $opType, // 操作类型(0: 绑定 1: 更新)
                ];
                $lib         = new PftRpcClient('resource_pull_product');
                $res         = $lib->call($method, $jsonRpcData, '');
                $code        = $res['code'];
                $msg         = $res['message'];
                $data        = $res['data'];
            }
        } catch (Exception $e) {
            $msg  = $e->getMessage();
            $code = $e->getCode();
            $data = [];
        }

        return $this->apiReturn($code, $data, $msg);
    }

    /**
     * 解绑门票
     *
     * <AUTHOR>
     * @date 2021-11-29
     *
     * @param array $unBindList
     * @param int $conSysId
     *
     * @return void
     */
    public function unbindTicketService(array $unBindList = [], int $conSysId = 0)
    {
        if ($this->sysType == 'Q') {
            try {
                $firstUnBind   = current($unBindList);
                $thirdCodeList = [];
                foreach ($firstUnBind['ticket_list'] as $ticket) {
                    $thirdCodeList[] = $ticket['third_ticket_id'];
                }
                $rpcData = [
                    'merchant_id'      => $this->sid,
                    'third_partner_id' => $conSysId,
                    'third_spu_id'     => $firstUnBind['product_map']['third_product_id'],
                    'third_sku_ids'    => $thirdCodeList,
                    'operator_id'      => $this->memberId,
                ];
                $lib     = new TVRpcClient('travel_voucher_service');
                $lib->call('intranet/v1/third_product/unbind', $rpcData, 'scenic');
                $msg  = 'success';
                $code = 200;
            } catch (Exception $e) {
                $msg  = $e->getMessage();
                $code = $e->getCode();
            }
            return $this->apiReturn($code, [], $msg);
        }

        $upStreamResourceModel = new UpStreamResourceModel();
        $field                 = 'id';
        $pullConfigMap         = $upStreamResourceModel->getConfigFirstByApplyId($this->sid, $conSysId, $field);
        if (empty($pullConfigMap)) {
            return $this->apiReturn(203, [], '请先添加配置信息');
        }

        $bizOtaConf = new BizOtaConf();
        foreach ($unBindList as $value) {
            $productMap     = $value['product_map'];
            $thirdProductId = $productMap['third_product_id'];
            $productTitle   = $productMap['title'];

            $ticketList = $value['ticket_list'];
            $opMsg      = '解除绑定关系';
            if (count($ticketList) > 1) {
                $opMsg = '批量解除绑定关系';
            }

            // 产品记录
            $insertData = [
                'apply_id'    => $this->sid,
                'operator_id' => $this->memberId,
                'conf_id'     => $pullConfigMap['id'],
                'third_id'    => $thirdProductId,
                'title'       => $productTitle,
                'status'      => 1, // 状态 (0: 拉取中 1: 成功 2: 失败 3: 部分成功)
                'op_type'     => 2, // 操作类型 (0: 绑定 1: 更新 2: 解绑)
                'request'     => json_encode($value, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE),
                'op_msg'      => $opMsg,
            ];
            $mainInsId  = $upStreamResourceModel->insertRecord($insertData);

            $failTicketList = [];
            foreach ($ticketList as $ticketMap) {
                $pftTicketId   = $ticketMap['pft_ticket_id'];
                $thirdTicketId = $ticketMap['third_ticket_id'];
                $ticketTitle   = $ticketMap['title'];
                $result        = $bizOtaConf->unBindCsysFromTicket($this->sid, $pftTicketId, $this->memberId);

                // 票记录
                $status = 1;
                if ($result['code'] != 200) {
                    $status           = 2;
                    $failTicketList[] = $ticketMap;
                }

                $requestData = [
                    'product_map' => $productMap,
                    'ticket_list' => [
                        $ticketMap,
                    ],
                ];
                $insertData  = [
                    'parent_id'   => $mainInsId,
                    'apply_id'    => $this->sid,
                    'operator_id' => $this->memberId,
                    'conf_id'     => $pullConfigMap['id'],
                    'third_id'    => $thirdTicketId,
                    'title'       => $ticketTitle,
                    'status'      => $status,
                    'op_type'     => 2,
                    'request'     => json_encode($requestData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE),
                    'op_msg'      => $result['msg'],
                ];
                $upStreamResourceModel->insertRecord($insertData);
            }

            if (!empty($failTicketList)) {

                if (count($failTicketList) == count($ticketList)) {
                    $status = 2; // 失败
                } else {
                    $status = 3; // 部分成功
                }
                $upData = [
                    'status' => $status,
                ];
                $upStreamResourceModel->updateRecord($upData, $mainInsId);
            }
        }
    }
}