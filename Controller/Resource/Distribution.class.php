<?php
/**
 * 资源中心分销相关接口
 * <AUTHOR>
 */
namespace Controller\Resource;

use Business\JavaApi\TicketApi;
use Library\Controller;

class Distribution extends Controller
{
    // 登陆信息
    private $_loginInfo;

    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo();
    }

    /**
     * 批量开启分销关系
     * <AUTHOR>
     */
    public function productBatchOpen()
    {
        $data = I('post.data');

        if (!is_array($data)) {
            $this->apiReturn(400, [], '参数缺失');
        }

        //验证分销商是否有权限
        $bondBiz   = new \Business\ResourceCenter\Bond();
        $bondState = $bondBiz->operateVerify($this->_loginInfo['sid'], $this->_loginInfo['sdtype']);
        if (isset($bondState['code'])  && $bondState['code'] !== 200) {
            return $this->apiReturn(400, [], $bondState['msg']);
        }

        foreach ($data as &$item) {
            $item['fid'] = $this->_loginInfo['memberID'];
            $item['priceSpread'] *= 100;
        }

        $ticketApi = new TicketApi();

        $res = $ticketApi->resourceProductDistributionBatchOpen($data);

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

}
