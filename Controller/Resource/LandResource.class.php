<?php
/**
 * 资源库 控制器
 */

namespace Controller\Resource;

use Business\JavaApi\Product\ResourceCenterApi;
use Business\JavaApi\TicketApi;
use Business\Order\OrderQueryJavaService;
use Business\Order\OrderQueryMove;
use Library\Cache\Cache;
use Library\Controller;
use Model\Product\AliCountryScenic;
use Model\Product\Area;
use Business\Product\Product;
use Business\Product\LandResource as ResourceBiz;
use Business\ResourceCenter\Product as ProductBiz;
use Model\Product\LandResource as ResourceModel;
use Library\SimpleExcel;

class LandResource extends Controller
{
    // 账户上级id
    private $_sid;
    // 是否管理员
    private $_isSuper;
    // 业务层实例化
    private $_resourceBiz;
    // 登陆用户id
    private $_mid;
    //上级id
    private $sid;

    //登录信息
    private $_loginInfo;

    private $_selectType = false;

    private $_orderBusiness;
    private $_orderReferModel;

    /*-- 审核类型：-1全部 0: 未供应未审核,1:审核中,2:审核通过,3:审核失败--*/
    const CHECK_TYPE_ALL             = -1;
    const CHECK_TYPE_UNREVIEWED      = 0;
    const CHECK_TYPE_REVIEWEDING     = 1;
    const CHECK_TYPE_REVIEWED_PASS   = 2;
    const CHECK_TYPE_REVIEWED_FAILED = 3;

    /**getMyProductList接口 checkType参数数组
     * @var array
     */
    public static $checkTypeArr = [
        self::CHECK_TYPE_ALL,
        self::CHECK_TYPE_UNREVIEWED,
        self::CHECK_TYPE_REVIEWEDING,
        self::CHECK_TYPE_REVIEWED_PASS,
        self::CHECK_TYPE_REVIEWED_FAILED,

    ];

    /**
     * 初始化信息
     * <AUTHOR>
     * @date   2018-2-5
     */
    public function __construct()
    {
        // 判断是否已经登陆
        $this->_sid = $this->isLogin('ajax');
        // 赋值是否是管理员
        $this->_isSuper = $this->isSuper();
        // 获取登陆用户信息
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
        $this->_mid   = $this->_loginInfo['memberID'];
        $this->sid    = $this->_loginInfo['sid'];

        $this->_selectType = I('post.time_type', 0);
    }

    ///**
    // * 根据资源ID获取相关联的景区信息
    // * <AUTHOR>
    // * @date   2017-2-20
    // *
    // * @param  int  $resource_id  资源ID
    // */
    //public function getLandInfoByResourceId()
    //{
    //    $resourceId = I('post.resource_id', '', 'intval');
    //
    //    if (empty($resourceId)) {
    //        $this->apiReturn(400, '', '资源ID出错');
    //    }
    //
    //    $model = new \Model\Product\AliCountryScenic();
    //    $data  = $model->getLandInfoBySourceId($resourceId, 'id, title');
    //
    //    if (empty($data)) {
    //        $this->apiReturn(200, '', '没有查找到关联数据');
    //    } else {
    //    }
    //}

    /**
     * 根据首字母简写或者汉字搜索资源  模糊搜索
     * <AUTHOR>
     * @date   2017-2-20
     *
     * @param  string  $keyword  搜索的字符
     */
    public function getSourceInfoByKeyWord()
    {

        $keyword = I('post.keyword');
        $pType   = I('post.p_type');

        $firstLength  = mb_strlen($keyword);
        $secondLength = strlen($keyword);
        if ($firstLength == $secondLength) {
            //是否是英文的标识  true  是
            $flag = true;
        } else {
            $flag = false;
        }

        $model     = new AliCountryScenic();
        $areaModel = new Area();
        $data      = $model->getSourceInfoByKeyWord($keyword, $flag, $pType);

        $returnData = [];
        if (is_array($data) && !empty($data)) {
            foreach ($data as $dataKey => $dataVal) {
                $area = '';
                if ($dataVal['area']) {
                    $areaCodeArr = explode('|', $dataVal['area']);
                    $areaNameArr = $areaModel->getInfoByCodeFromCache($areaCodeArr);
                    $area        = trim(implode('-', $areaNameArr), '-');
                }
                $returnData[$dataKey]['area']  = $area;
                $returnData[$dataKey]['id']    = $dataVal['id'];
                $returnData[$dataKey]['title'] = $dataVal['title'];
            }

            $this->apiReturn(200, $returnData, '');
        } else {
            $this->apiReturn(400, '', '没有相关信息');
        }
    }

    /**
     * 根据资源ID获取相关资料
     * <AUTHOR>
     * @date   2017-2-20
     */
    public function getInfoBySourceId()
    {
        $sourceId = I('post.resource_id', '', 'intval');

        if (empty($sourceId)) {
            return false;
        }

        $model = new AliCountryScenic();
        $data  = $model->getInfoBySourceId($sourceId);

        if (!empty($data)) {
            $this->apiReturn(200, $data, '');
        } else {
            $this->apiReturn(400, '', '无数据');
        }
    }

    /**
     * 关联产品表的资源id
     * <AUTHOR>
     * @date   2017-2-27
     */
    public function updateLandSourceId()
    {
        $landId   = I('post.land_id', '');
        $sourceId = I('post.source_id', '', 'intval');

        if (empty($landId) || empty($sourceId)) {
            $this->apiReturn(400, '', '产品id和资源id不能为空');
        }
        if (is_array($landId)) {
            $landIdArr = $landId;
        } else {
            $landIdArr = explode(',', $landId);
            foreach ($landIdArr as $land) {
                if (empty($land)) {
                    $this->apiReturn(400, '', '批量id错误');
                }
            }
        }

        // 实例化相关的业务和模型
        $productBiz        = new Product();
        //$orderCommonModel  = new \Model\Order\OrderCommon();
        $orderQueryJavaBiz = new OrderQueryJavaService();
        $status            = [0, 7];
        $otherParam        = [
            'status' => [$status],
        ];
        $selectParams      = [
            'field' => 'id',
        ];

        // 对数组进行循环处理
        foreach ($landIdArr as $landIdVal) {
            $res = $productBiz->releateSource($landIdVal, $sourceId);

            if ($res['code'] != 200) {
                $this->apiReturn(400, '', $res['msg']);
            } else {

                //订单查询迁移
                $page = 1;
                $size = 3000;
                while (true) {
                    $orderQueryLib = new OrderQueryMove();
                    $idArr = $orderQueryLib->getOrderInfByLidStatusPage([$landIdVal], $status, $page, $size);
                    if (empty($idArr)) {
                        break;
                    }
                    $orderIds   = array_column($idArr, 'ordernum');
                    $queryParams = [$sourceId, $orderIds, $this->sid];
                    $queryRes    = \Business\JavaApi\Order\Query\Container::query('UuSsOrderModify',
                        'updateOrderCertnumByOrderNum', $queryParams);
                    if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                        pft_log('updatePayMode/error', json_encode([
                            'ac'       => 'updateOrderCertnumByOrderNum',
                            'orderIdList' => $orderIds,
                            'sourceId'  => $sourceId,
                            'queryRes' => $queryRes,
                        ]));
                    }

                    $page++;
                }
            }
        }

        // 批量转移成功
        $this->apiReturn(200);
    }

    /**
     * 实例化资源库业务层
     * <AUTHOR>
     * @date   2018-01-12
     * @return object
     *
     */
    private function _instanceResouceBiz()
    {
        if (!$this->_resourceBiz) {
            $this->_resourceBiz = new ResourceBiz;
        }

        return $this->_resourceBiz;
    }

    /**
     * 获取资源库列表
     * <AUTHOR>
     *
     * @param  int  $page  页码
     * @param  int  $pageSize  每页显示条数
     * @param  string  $resourceName  资源名称
     * @param  int  $signType  直签 间签
     * @param  int  $resourceLevel  资源等级
     * @param  int  $area  资源所在区域
     * @param  int  $ticketSystem  票务系统
     * @param  int  $status  资源状态
     * @param  int  $distributionSystem  分销系统
     * @param  int  $province  省份id
     * @param  int  $city  市区id
     * @param  string  $mainTitle  申报主体
     * @param  int  $isTest  是否测试资源 0 正式 1测试
     * @param  int  $applyDid  经营单位
     * return string | json
     *
     */
    public function getResourceList()
    {
        // 对用户传入的数据进行处理整合
        $page               = I('request.page', 1, 'intval');
        $pageSize           = I('request.pageSize', 10, 'intval');
        $resourceName       = I('request.resourceName', '');
        $signType           = I('request.signType', '');
        $resourceType       = I('request.resourceType', '');
        $resourceLevel      = I('request.resourceLevel', '');
        $ticketSystem       = I('request.ticketSystem', '');
        $status             = I('request.status', '');
        $distributionSystem = I('request.distributionSystem', '');
        $province           = I('request.province', '', 'intval');
        $city               = I('request.city', '', 'intval');
        $mainTitle          = I('request.mainTitle', '');
        $isTest             = I('request.isTest', '');
        $applyDid           = I('request.applyDid', '', 'intval');
        $isExport           = I('request.isExport', 0, 'intval');
        $operateId          = I('request.operateId', 0, 'intval');//操作人id

        //页面上就三种类型添加一下限制
        if ($resourceType) {
            if (!in_array($resourceType, ['A', 'H', 'C'])) {
                $this->apiReturn(203, [], '资源类型错误');
            }
        }

        // 实例化
        $resourceBiz = $this->_instanceResouceBiz();

        $whereParamArr = [
            'resourceName'       => $resourceName,
            'signType'           => $signType,
            'resourceType'       => $resourceType,
            'resourceLevel'      => $resourceLevel,
            'province'           => $province,
            'city'               => $city,
            'ticketSystem'       => $ticketSystem,
            'status'             => $status,
            'distributionSystem' => $distributionSystem,
            'mainTitle'          => $mainTitle,
            'isTest'             => $isTest,
            'applyDid'           => $applyDid,
            'operateId'          => $operateId,
        ];

        if ($isExport) {
            // 导出数据
            // 业务层获取列表
            $resourceArr = $resourceBiz->getResourceData($page, $pageSize, 'desc', $whereParamArr);
            if ($resourceArr['code'] == 200) {
                $this->_handleExportData($resourceArr['data']);
            }
        } else {
            // 业务层获取列表
            $resourceArr = $resourceBiz->getResourceData($page, $pageSize, 'desc', $whereParamArr);
        }
        // 返回给前端页面数据
        if ($resourceArr['code'] == 200) {
            $this->apiReturn(200, $resourceArr['data'], 'success');
        } else {
            $this->apiReturn(203, [], '无数据');
        }
    }

    /**
     * 处理导出的数据格式
     *
     * @param  array  $data
     *
     */
    private function _handleExportData($data)
    {
        $head       = ['申报主体', '资源名称', '地区', '级别', '票务系统', '分销系统', '状态', '直签/间签', '最后操作时间', '操作员'];
        $reportDesc = [''];

        if ($data) {
            foreach ($data as $key => $val) {
                if ($val['sign_type'] == 1) {
                    $signType = '间签';
                } elseif ($val['sign_type'] == 2) {
                    $signType = '直签';
                } else {
                    $signType = '';
                }

                $excelData[] = [
                    $val['mainTitle'],
                    $val['title'],
                    $val['area'],
                    $val['level'],
                    $val['ticketSystemName'],
                    $val['distributionName'],
                    $val['status'],
                    $signType,
                    $val['operateTime'],
                    $val['operateName'],
                ];
            }

            array_unshift($excelData, $head);

            $file = '资源库';
            $xls  = new SimpleExcel('UTF-8', true, 'orderList');
            $xls->addArray($excelData);
            $xls->generateXML($file);
        }
    }

    /**
     * 获取资源库数据信息详情
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  int  $resourceId  资源id
     *
     * @return string | json
     *
     */
    public function getResourceView()
    {
        $resourceId = I('request.resourceId', '');
        if (empty($resourceId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        // 实例化
        $resourceBiz   = $this->_instanceResouceBiz();
        $whereParamArr = ['id' => $resourceId];
        // 业务层获取列表
        $resourceArr = $resourceBiz->getResourceData('', '', '', $whereParamArr);

        if ($resourceArr['code'] == 200) {
            $this->apiReturn(200, $resourceArr, '数据取得成功');
        } else {
            $this->apiReturn(203, [], '无数据');
        }
    }

    /**
     * 添加资源库信息
     * <AUTHOR>
     * @date   2018-01-29
     *
     * @param  string  $resourceName  资源名字
     * @param  string  $resourceType  资源类型
     * @param  string  $resourceLevel  资源等级
     * @param  int  $province  省id
     * @param  int  $city  市id
     * @param  string  $Tel  联系电话
     * @param  string  $address  地址
     * @param  int  $signType  签订方式   1：间签 2:直签
     * @param  string  $topic  资源主题
     * @param  int  $applyDid  经营单位id
     * @param  string  $jtzn  交通指南
     * @param  string  $bhjq  景点详情
     * @param  string  $jqts  景区提示
     * @param  int  $terminal  终端id
     * @param  int  $ticketSystem  票务系统id
     * @param  int  $distributionSystem  分销系统id
     * @param  string  $imgPath  图片地址
     * @param  string  $runtime  营业时间
     * @param  string  $mainTitle  申报主体名字
     *
     * @return string | json
     *
     */
    public function addResource()
    {
        // 获取参数
        $resourceName  = I('request.resourceName', '');
        $resourceType  = I('request.resourceType', '');
        $resourceLevel = I('request.resourceLevel', '');
        $province      = I('request.province', '', 'intval');
        $city          = I('request.city', '', 'intval');
        $address       = I('request.address', '');
        $signType      = I('request.signType', 0, 'intval');

        if (!$resourceName || !$resourceType || !$province || !$city || !$address) {
            $this->apiReturn(203, [], '必填参数不可少');
        }

        if (!$this->_isSuper) {
            $this->apiReturn(203, [], '非管理员无权限');
        }

        // 获取操作人
        $optionalParamArr['operateId']          = $this->_mid;
        $optionalParamArr['Tel']                = I('request.tel', '');
        $optionalParamArr['topic']              = I('request.topic', '');
        $optionalParamArr['applyDid']           = I('request.applyDid', '');
        $optionalParamArr['jtzn']               = I('request.jtzn', '');
        $optionalParamArr['bhjq']               = I('request.bhjq', '');
        $optionalParamArr['jqts']               = I('request.jqts', '');
        $optionalParamArr['terminal']           = I('request.terminal', '');
        $optionalParamArr['ticketSystem']       = I('request.ticketSystem', '');
        $optionalParamArr['distributionSystem'] = I('request.distributionSystem', '');
        $optionalParamArr['imgPath']            = I('request.imgPath', '');
        $optionalParamArr['runtime']            = I('request.runtime', '');
        $optionalParamArr['mainTitle']          = I('request.mainTitle', '');
        $optionalParamArr['verifyStatus']       = I('request.isVerify', '');
        $optionalParamArr['lngLatPos']          = I('request.lngLatPos', '');

        // 实例化
        $resourceBiz    = $this->_instanceResouceBiz();
        $addResourceRes = $resourceBiz->addResource($resourceName, $resourceType, $resourceLevel, $province,
            $city, $address, $signType, $optionalParamArr);

        if ($addResourceRes['code'] == 200) {
            $this->apiReturn(200, [], '添加成功');
        } else {
            $this->apiReturn(203, [], $addResourceRes['msg']);
        }
    }

    /**
     * 编辑资源库数据信息
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  int  $id  资源id
     * @param  string  $resourceName  资源名字
     * @param  string  $resourceType  资源类型
     * @param  string  $resourceLevel  资源等级
     * @param  int  $province  省id
     * @param  int  $city  市id
     * @param  string  $Tel  联系电话
     * @param  string  $address  地址
     * @param  string  $topic  资源主题
     * @param  int  $applyDid  经营单位id
     * @param  string  $jtzn  交通指南
     * @param  string  $bhjq  景点详情
     * @param  string  $jqts  景区提示
     * @param  int  $terminal  终端id
     * @param  int  $ticketSystem  票务系统id
     * @param  int  $distributionSystem  分销系统id
     * @param  string  $imgPath  图片地址
     * @param  string  $runtime  营业时间
     * @param  string  $mainTitle  申报主体名字
     * @param  int  $signType  签订方式   1：间签 2:直签
     *
     * @return  string | json
     *
     */
    public function editResource()
    {
        $resourceId = I('request.id', '');
        if (!$resourceId) {
            $this->apiReturn(203, [], '参数错误');
        }
        // 判断操作人员权限
        if (!$this->_isSuper) {
            $this->apiReturn(203, [], '非管理员 ，无权限');
        }

        // 地区
        $province = I('request.province', '');
        $city     = I('request.city', '');

        $optionalParamArr['resourceName']       = I('request.resourceName', 0);
        $optionalParamArr['mainTitle']          = I('request.mainTitle', 0);
        $optionalParamArr['resourceType']       = I('request.resourceType', 0);
        $optionalParamArr['resourceLevel']      = I('request.resourceLevel', 0);
        $optionalParamArr['areaCode']           = I('request.areaCode', 0);
        $optionalParamArr['Tel']                = I('request.tel', 0);
        $optionalParamArr['address']            = I('request.address', 0);
        $optionalParamArr['topic']              = I('request.topic', 0);
        $optionalParamArr['applyDid']           = I('request.applyDid', '');
        $optionalParamArr['signType']           = I('request.signType', '');
        $optionalParamArr['jtzn']               = I('request.jtzn', 0);
        $optionalParamArr['bhjq']               = I('request.bhjq', 0);
        $optionalParamArr['jqts']               = I('request.jqts', 0);
        $optionalParamArr['terminal']           = I('request.terminal', '');
        $optionalParamArr['ticketSystem']       = I('request.ticketSystem', '');
        $optionalParamArr['distributionSystem'] = I('request.distributionSystem', '');
        $optionalParamArr['imgPath']            = I('request.imgPath', 0);
        $optionalParamArr['runtime']            = I('request.runtime', 0);
        $optionalParamArr['verifyStatus']       = I('request.isVerify', 0);
        $optionalParamArr['lngLatPos']          = I('request.lngLatPos', 0);
        if ($province && $city) {
            $optionalParamArr['area'] = $province . '|' . $city . '|0';
        }
        $optionalParamArr['areaCode'] = 0;

        // 实例化
        $resourceBiz       = $this->_instanceResouceBiz();
        $updateResourceRes = $resourceBiz->editResource($resourceId, $this->_mid, $optionalParamArr);

        if ($updateResourceRes['code'] == 200) {
            $this->apiReturn(200, [], '修改成功');
        } else {
            $this->apiReturn(203, [], '修改失败');
        }
    }

    /**
     * 删除资源数据
     * <AUTHOR>
     * @dater 2018-01-29
     *
     * @param  array  $id  资源id数组
     *
     * @return string | json
     *
     */
    public function delResource()
    {
        $resourceIdArr = I('request.id', '');
        if (!is_array($resourceIdArr)) {
            $this->apiReturn(203, [], '参数错误');
        }

        // 判断操作人员权限
        if (!$this->_isSuper) {
            $this->apiReturn(203, [], '非管理员 ，无权限');
        }

        // 调用删除资源库数据业务
        $resourceBiz    = $this->_instanceResouceBiz();
        $delResourceRes = $resourceBiz->delResource($resourceIdArr);

        if ($delResourceRes['code'] == 200) {
            $this->apiReturn(200, [], 'success');
        } elseif ($delResourceRes['code'] == 204) {
            $this->apiReturn(204, $delResourceRes['data'], $delResourceRes['msg']);
        } else {
            $this->apiReturn(203, [], '删除失败');
        }
    }

    /**
     * 获取票务系统，分销系统信息，员工列表数据
     * <AUTHOR>
     * @date   2019-04-09
     */
    public function systemData()
    {
        if (!$this->_isSuper) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '非管理员无权限');
        }

        $resourceBiz = $this->_instanceResouceBiz();
        $dataArr     = $resourceBiz->systemData();
        $systemData  = $dataArr['data'];

        //获取员工列表数据 - 数据不多，前端要求合并接口
        $PftStaffModel       = new \Model\Member\PftStaffModel();
        $systemData['staff'] = $PftStaffModel->getStaffListByName();

        $this->apiReturn(200, $systemData, '数据取得成功');
    }

    /**
     *  统计资源数据入口
     */
    public function countResourceNum()
    {
        $resourceBiz        = $this->_instanceResouceBiz();
        $resourceNumDataArr = $resourceBiz->countResourceNum();

        if ($resourceNumDataArr['code'] == 200) {
            $this->apiReturn(200, $resourceNumDataArr['data'], 'success');
        } else {
            $this->apiReturn(203, [], '无数据');
        }
    }

    public function addResourceTags()
    {
        $resourceTags = I('post.tags', '');
        $resourceId   = I('post.source_id', '');
        if (empty($resourceTags) || empty($resourceId) || !is_numeric($resourceId)) {
            $this->apiReturn(203, [], '参数错误');
        }
        // 判断操作人员权限
        if (!$this->_isSuper) {
            $this->apiReturn(203, [], '非管理员 ，无权限');
        }
        $resourceBiz        = $this->_instanceResouceBiz();
        $resourceNumDataArr = $resourceBiz->addResourceTags($resourceId, $resourceTags);
        if ($resourceNumDataArr['code'] == 200) {
            $this->apiReturn(200, '', 'success');
        } else {
            $this->apiReturn(204, [], '添加失败');
        }
    }

    /**
     * 获取资源库关联产品列表
     * <AUTHOR> - 补充相应产品类型
     * @date 2019-04-08
     */
    public function getResourceProductList()
    {
        $province = I('post.province', '');
        $city     = I('post.city', '');
        $title    = I('post.title', '');
        $page     = I('post.page', 1);
        $size     = I('post.size', 6);
        $id       = I('post.id', 0, 'intval'); //资源id

        $errorTmp = ['list' => [], 'total' => 0, 'page' => $page, 'page_total' => 0];
        if (empty($title)) {
            $this->apiReturn(200, $errorTmp, '');
        }

        if (!$id) {
            $this->apiReturn(204, $errorTmp, '资源id不能为空');
        }

        $ResourceModel = new ResourceModel();
        $resourceInfo  = $ResourceModel->findResourceById($id, 'p_type');
        if (!$resourceInfo || !$resourceInfo['p_type']) {
            $this->apiReturn(204, $errorTmp, '资源信息获取错误');
        }

        $productBiz = new Product();
        $res        = $productBiz->getResourceProductList($province, $city, $title, $page, $size,
            $resourceInfo['p_type']);
        if ($res['code'] != 200) {
            $this->apiReturn(204, ['list' => [], 'total' => 0, 'page' => $page, 'page_total' => 0], '');
        }

        $data      = $res['data']['resourceLibraryList'];
        $pageCount = ceil($res['data']['totalCount'] / $size);
        if ($res['code'] == 200) {
            $this->apiReturn(200,
                ['list' => $data, 'total' => $res['data']['totalCount'], 'page' => $page, 'page_total' => $pageCount],
                'success');
        } else {
            $this->apiReturn(204, $errorTmp, '获取失败');
        }
    }

    /**
     * 资源名称获取资源列表ID
     * <AUTHOR>
     * @date   2019-04-09
     */
    public function getResourceTitleList()
    {
        $title = I('post.title', '', 'strval,trim');

        if ($this->_isSuper) {
            $aid = 0;
        } else {
            $aid = $this->_sid;
        }
        $aid           = 0;
        $ResourceModel = new \Model\Product\LandResource();
        $res           = $ResourceModel->getResourceListByTitle($title, $aid, $field = 'id,title');
        $this->apiReturn(200, $res, '');
    }

    /**
     * 资源中心产品列表-只展示门票
     * <AUTHOR>
     */
    public function ticketQuery()
    {
        // 产品名称
        $landName = I('get.landName', '', 'strval');
        // 境内外
        $oversea = I('get.oversea', -1, 'intval');
        // 省份
        $province = I('get.province', 0, 'intval');
        // 城市
        $city = I('get.city', 0, 'intval');
        // 供应商id
        $supplierId = I('get.supplierId', 0, 'intval');
        // 供应商名称
        $supplierName = I('get.supplierName', '', 'strval');
        // 供应商所在省份
        $supplierProvince = I('get.supplierProvince', 0, 'intval');
        // 供应商所在城市
        $supplierCity = I('get.supplierCity', 0, 'intval');
        // 合作状态2=全部供应商, 1=已合作的供应商, 0=未合作的供应商
        $cooperation = I('get.cooperation', 2, 'intval');
        // 产品类型
        $type = I('get.type', '', 'strval');
        // 购买状态
        $purchaseStatus = I('get.purchaseStatus', 2, 'intval');
        // 当前页数
        $pageNum = I('get.pageNum', 1, 'intval');
        // 每页显示条数
        $pageSize = I('get.pageSize', 10, 'intval');
        // 景点ID
        $landId = I('get.lid', 0, 'intval');
        // 分销状态 0=全部,1=已分销，2=未分销
        $distributionStatus = I('get.distributionStatus', 0, 'intval');

        $ticketBiz = new TicketApi();

        $res = $ticketBiz->resourceTicketQuery($this->_mid, $pageNum, $pageSize, $landName,
            $oversea, $province, $city, $supplierId, $supplierName, $supplierProvince, $supplierCity,
            $cooperation, $type, $purchaseStatus, $landId, $distributionStatus);

        if ($res['code'] == 200) {
            if (!empty($res['data']['resultList'])) {
                foreach ($res['data']['resultList'] as &$item) {
                    $special = $item['ticketDetailDTO']['special_product'];
                    if (!empty($special)) {
                        $item['ticketDetailDTO']['special_product'] = $this->jsonParseSpecial($special);
                    }
                    $item['tags'] = array_values(array_values($this->_parseTicketsTags([$item['ticketDetailDTO']]))[0]);
                }
            }
        }
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 根据景区id 获取可以采购票列表-供应商
     * <AUTHOR>
     */
    public function mayPurchaseTicketBySupplier()
    {
        $sids               = I('get.sids', '', 'strval');  // 供应商id列表
        $pageNum            = I('get.pageNum', 1, 'intval');    // 页数
        $pageSize           = I('get.pageSize', 1, 'intval');  // 页码
        $type               = I('get.type', 0, 'intval'); // 0:获取可以采购,1:获取取消采购票
        $distributionStatus = I('get.distributionStatus', 0, 'intval'); // 分销状态 0: 不过滤分销，1：获取已分销列表，2：获取未分销
        $oversea            = I('get.oversea', 0, 'intval');   // 境内外
        $province           = I('get.province', 0, 'intval'); // 省份
        $city               = I('get.city', 0, 'intval'); // 城市
        $supplierProvince   = I('get.supplierProvince', 0, 'intval'); // 供应商所在省份
        $supplierCity       = I('get.supplierCity', 0, 'intval'); // 供应商所在城市
        $cooperation        = I('get.cooperation', 2, 'intval'); // 合作状态:2=全部供应商,1=已合作的供应商, 0=未合作的供应商
        $pType              = I('get.pType', '', 'strval');

        $sids = explode(',', $sids);

        $biz = new ResourceCenterApi();
        $res = $biz->mayPurchaseTicketBySupplierIds($this->_mid, $sids, $type, $distributionStatus, $oversea,
            $province, $city, $supplierProvince, $supplierCity, $cooperation, $pType, $pageNum, $pageSize);

        if (empty($res['data'])) {
            $res['data'] = [
                'list'  => [],
                'total' => 0,
            ];
        }

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 根据景区id 获取可以采购票列表
     * <AUTHOR>
     */
    public function mayPurchaseTicket()
    {
        $list               = I('get.list');  // 景区id列表
        $pageNum            = I('get.pageNum', 1, 'intval');    // 页数
        $pageSize           = I('get.pageSize', 1, 'intval');  // 页码
        $type               = I('get.type', 0, 'intval'); // 0:获取可以采购,1:获取取消采购票
        $distributionStatus = I('get.distributionStatus', 0, 'intval'); // 分销状态 0: 不过滤分销，1：获取已分销列表，2：获取未分销
        $oversea            = I('get.oversea', 0, 'intval');   // 境内外
        $province           = I('get.province', 0, 'intval'); // 省份
        $city               = I('get.city', 0, 'intval'); // 城市
        $supplierProvince   = I('get.supplierProvince', 0, 'intval'); // 供应商所在省份
        $supplierCity       = I('get.supplierCity', 0, 'intval'); // 供应商所在城市
        $cooperation        = I('get.cooperation', 2, 'intval'); // 合作状态:2=全部供应商,1=已合作的供应商, 0=未合作的供应商
        $pType              = I('get.pType', '', 'strval');
        $lidsid             = [];
        foreach ($list as $value) {
            $ids      = htmlspecialchars_decode($value);
            $lidsid[] =  json_decode($ids, true);
        }

        $ticketBiz = new TicketApi();
        $res       = $ticketBiz->resourceMayPurchaseTicket($this->_mid, $lidsid, $type, $distributionStatus, $oversea,
            $province, $city, $supplierProvince, $supplierCity, $cooperation, $pType, $pageNum, $pageSize);

        if (empty($res['data'])) {
            $res['data'] = [
                'list'  => [],
                'total' => 0,
            ];
        }

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 资源中心首页推荐位列表
     * <AUTHOR>
     */
    public function recommendIndexList()
    {
        $pageNum  = I('get.pageNum', 1, 'intval'); // 页数
        $pageSize = I('get.pageSize', 15, 'intval'); // 页码

        $ticketBiz = new TicketApi();
        $res       = $ticketBiz->resourceRecommendConfigQueryIndexList($this->sid, $pageNum, $pageSize);

        if ($res['code'] == 200) {
            if (empty($res['data']['list'])) {
                $res['data']['list']  = [];
                $res['data']['total'] = 0;
            } else {
                foreach ($res['data']['list'] as &$firstItem) {
                    if (empty($firstItem['list'])) {
                        $firstItem['list'] = [
                            'list'  => [],
                            'total' => 0,
                        ];
                    } else {
                        foreach ($firstItem['list']['list'] as &$secondItem) {
                            $special = $secondItem['ticketInfo']['special_product'];
                            if (!empty($special)) {
                                $secondItem['ticketInfo']['special_product'] = $this->jsonParseSpecial($special);
                            }
                            $secondItem['tags'] = array_values(array_values($this->_parseTicketsTags([$secondItem['ticketInfo']]))[0]);
                        }
                    }
                }
            }

            //if (!empty($res['data']['list'])) {
            //    foreach ($res['data']['list'] as &$item) {
            //        if (!empty($res['data']['list']['list'])) {
            //            foreach ($item['list']['list'] as &$list) {
            //                $list['tags'] = array_values(array_values($this->_parseTicketsTags([$list['ticketInfo']]))[0]);
            //            }
            //        }
            //    }
            //}
        }

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * Notes:处理特产商品的json数据
     *
     * @param $special
     *
     * @return mixed
     * <AUTHOR>
     * @date 2020/2/27 13:58
     */
    public function jsonParseSpecial($special)
    {
        if (!empty($special)) {
            $special     = json_decode($special, true);
            $spec_params = $special['spec_params'];
            if (!empty($spec_params)) {
                $special['spec_params'] = json_decode($spec_params, true);
            }
        }

        return $special;
    }

    /**
     * 获取资源中心推荐位产品列表
     * <AUTHOR>
     */
    public function recommendConfigProductQueryList()
    {
        $recommendId = I('get.recommendId', 0, 'intval'); // 推荐专题Id
        $pageNum     = I('get.pageNum', 1, 'intval'); // 页数
        $pageSize    = I('get.pageSize', 15, 'intval'); // 页码

        $ticketBiz = new TicketApi();
        $res       = $ticketBiz->resourceRecommendConfigProductQueryList($this->_mid, $recommendId, $pageNum,
            $pageSize);

        if ($res['code'] == 200) {
            if (empty($res['data']['list'])) {
                $res['data']['list'] = [
                    'list'  => [],
                    'total' => 0,
                ];
            } else {
                foreach ($res['data']['list']['list'] as &$item) {
                    $special = $item['ticketInfo']['special_product'];
                    if (!empty($special)) {
                        $item['ticketInfo']['special_product'] = $this->jsonParseSpecial($special);
                    }
                    $item['tags'] = array_values(array_values($this->_parseTicketsTags([$item['ticketInfo']]))[0]);
                }
            }
        }

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 资源中心首页分销价格
     * <AUTHOR>
     */
    public function evoluteQueryDistribution()
    {
        $data = I('post.data');

        if (empty($data)) {
            $this->apiReturn(200, [], 'success');
        }

        foreach ($data as &$item) {
            $item['fid'] = $this->_mid;
        }

        $ticketBiz = new TicketApi();
        $res       = $ticketBiz->resourceEvoluteQueryDistribution($data);

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 订单数据汇总
     * <AUTHOR>
     */
    public function orderStatistics()
    {
        $this->apiReturn(500, [], '接口废弃');
    }

    /**
     * 获取我的资源详情
     * <AUTHOR>
     */
    public function getMyDetail()
    {
        $ticketBiz = new TicketApi();
        $res       = $ticketBiz->resourceMyDetail($this->_sid);

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取自供应的资源中心产品列表
     * <AUTHOR>
     */
    public function getMyProductList()
    {
        $type        = I('get.type', '', 'strval'); // 产品类型
        $landId      = I('get.landId', 0, 'intval'); // 产品id
        $supplyState = I('get.supplyState', 0, 'intval'); // 供应状态:0 全部,1已供应，2未供应
        $checkType   = I('get.checkType', self::CHECK_TYPE_ALL); // 审核类型：-1:全部, 0: 未供应未审核,1:审核中,2:审核通过,3:审核失败
        if (!in_array($checkType, self::$checkTypeArr)) {
            return $this->apiReturn(422, [], '参数错误！');
        }
        $pageSize = I('get.pageSize', 10, 'intval');
        $pageNum  = I('get.pageNum', 1, 'intval');

        $ticketBiz = new TicketApi();
        $res       = $ticketBiz->resourceMyProductList($this->sid, $type, $landId,
            $supplyState, $checkType, $pageNum, $pageSize);

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取分销的资源产品列表-目前一级分销产品
     * <AUTHOR>
     */
    public function getProductDistributionList()
    {
        $supplierId  = I('get.supplierId', 0, 'intval'); // 供应商Id
        $type        = I('get.type', '', 'strval'); // 产品类型
        $landId      = I('get.landId', 0, 'intval'); // 产品id
        $supplyState = I('get.supplyState', 0, 'intval'); // 供应状态
        $checkType   = I('get.checkType', 0, 'intval'); // 审核类型0: 全部,1:已审核,2:未审核
        $pageNum     = I('get.pageNum', 1, 'intval');
        $pageSize    = I('get.pageSize', 10, 'intval');

        $ticketBiz = new TicketApi();
        $res       = $ticketBiz->resourceProductDistributionList($this->_sid, $supplierId, $type, $landId,
            $supplyState, $checkType, $pageNum, $pageSize);

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 解析票类的一些标签属性
     *
     * @param  [type] $tickets 门票列表
     *
     * @return [type]          [description]
     */
    private function _parseTicketsTags($tickets)
    {
        $tags           = [];
        $orderBookModel = new \Business\Order\OrderBook();
        foreach ($tickets as $item) {
            if ($item['preorder_early_days'] > 0) {
                $tags[$item['id']]['useTimeStr'] = "最早可订{$item['preorder_early_days']}天票";
                //} elseif ($item['preorder_early_days'] == 0 && time()<strtotime(date('Y-m-d'). $item['preorder_expire_time '] . ':00')) {
            } elseif ($item['preorder_early_days'] == 0) {
                $tags[$item['id']]['useTimeStr'] = "最早可订今日票";
            }
            $refundRuleStr                  = $orderBookModel::handleRefundRule($item['refund_audit'],
                $item['refund_rule'], $item['refund_early_minu']);
            $tags[$item['id']]['refundStr'] = $refundRuleStr;

            $expireStr                      = $orderBookModel::getValidityDate($item);
            $tags[$item['id']]['expireStr'] = $expireStr;

            if ($item['pay_way'] == 0) {
                $tags[$item['id']]['cash'] = '现场支付';
            }

            if ($item['pay'] == 3) {
                $tags[$item['pid']]['card'] = '会员卡支付';
            }

            //退票规则：添加了这个值 -1=不可退且是可提现
            // if ($item['refund_rule'] == 2 || $item['refund_rule'] == -1) {
            //     $tags[$item['id']][] = '不可退';
            // }
        }

        return $tags;
    }

    private function getOrderBusiness()
    {
        if (!isset($this->_orderBusiness)) {
            $this->_orderBusiness = new \Business\Order\OrderList($this->_selectType);
        }

        return $this->_orderBusiness;
    }

    private function getOrderModel()
    {
        if (!isset($this->_orderReferModel)) {
            $this->_orderReferModel = new \Model\Order\OrderRefer($this->_selectType);
        }

        return $this->_orderReferModel;
    }

    /**
     * Notes:已采购产品列表(按景区分类获取产品)
     * <AUTHOR>
     * @date 2020/3/18 10:58
     */
    public function purchaseProductsByScenic()
    {
        $landId = I('get.landId/d'); // 产品id
        // 产品名称
        $landName = I('get.landName', '', 'string');
        // 境内外
        $oversea = I('get.oversea', -1, 'int');
        // 省份
        $province = I('get.province', 0, 'int');
        // 城市
        $city = I('get.city', 0, 'int');
        // 供应商id
        $supplierId = I('get.supplierId', 0, 'int');
        // 供应商名称
        $supplierName = I('get.supplierName', '', 'string');
        // 供应商所在省份
        $supplierProvince = I('get.supplierProvince', 0, 'int');
        // 供应商所在城市
        $supplierCity = I('get.supplierCity', 0, 'int');
        // 合作状态2=全部供应商, 1=已合作的供应商, 0=未合作的供应商
        $cooperation = I('get.cooperation', 2, 'int');
        // 采购状态 2=全部,1=已采购, 0=未采购
        $purchaseStatus = I('get.purchaseStatus', 2, 'int');
        // 产品类型
        $type = I('get.type', '', 'string');
        // 当前页数
        $pageNum = I('get.pageNum', 1, 'int');
        // 每页显示条数
        $pageSize = I('get.pageSize', 10, 'int');
        //子列表展示条数
        $subPageSize = I('get.subPageSize/d');
        //分销状态 0=全部,1=已分销，2=未分销
        $distributionStatus = I('get.distributionStatus/d');
        //时间状态: 0:全部 1:正常 2:即将过期 3:已过期 [默认：1] 非必填
        $timeStatus = I('get.timeStatus',1,'int');

        $ticketBiz = new TicketApi();
        $res       = $ticketBiz->getPurchaseProductsByScenic(
            $this->sid, $landId, $landName, $oversea, $province, $city, $supplierId,
            $supplierName, $supplierProvince, $supplierCity, $cooperation, $purchaseStatus,
            $type, $pageNum, $pageSize, $subPageSize, $distributionStatus,$timeStatus);

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * Notes:已采购产品列表(按供应商展示)
     * @return bool
     * <AUTHOR>
     * @date 2020/3/18 11:49
     */
    public function purchaseProductsBySupplier()
    {
        $landId = I('get.landId/d'); // 产品id
        // 产品名称
        $landName = I('get.landName', '', 'string');
        // 境内外
        $oversea = I('get.oversea', -1, 'int');
        // 省份
        $province = I('get.province', 0, 'int');
        // 城市
        $city = I('get.city', 0, 'int');
        // 供应商名称
        $supplierName = I('get.supplierName', '', 'string');
        // 供应商所在省份
        $supplierProvince = I('get.supplierProvince', 0, 'int');
        // 供应商所在城市
        $supplierCity = I('get.supplierCity', 0, 'int');
        // 合作状态2=全部供应商, 1=已合作的供应商, 0=未合作的供应商
        $cooperation = I('get.cooperation', 2, 'int');
        // 采购状态 2=全部,1=已采购, 0=未采购
        $purchaseStatus = I('get.purchaseStatus', 2, 'int');
        // 产品类型
        $type = I('get.type', '', 'string');
        // 当前页数
        $pageNum = I('get.pageNum', 1, 'int');
        // 每页显示条数
        $pageSize = I('get.pageSize', 10, 'int');
        //子列表展示条数
        $subPageSize = I('get.subPageSize/d');
        //分销状态 0=全部,1=已分销，2=未分销
        $distributionStatus = I('get.distributionStatus/d');
        //时间状态: 0:全部 1:正常 2:即将过期 3:已过期 [默认：1] 非必填
        $timeStatus = I('get.timeStatus',1,'int');

        $ticketBiz = new TicketApi();
        $res       = $ticketBiz->getPurchaseProductsBySupplier($this->_mid, $landId, $landName, $oversea, $province,
            $city, $supplierName, $supplierProvince,
            $supplierCity, $cooperation, $purchaseStatus, $type, $pageNum, $pageSize, $subPageSize,
            $distributionStatus, $timeStatus);

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * Notes:资源中心产品列表-按门票展示(展开更多)
     * @return bool
     * <AUTHOR>
     * @date 2020/3/18 14:01
     */
    public function productTickets()
    {
        $landId = I('get.landId/d'); // 产品id
        // 产品名称
        $landName = I('get.landName', '', 'string');
        // 境内外
        $oversea = I('get.oversea', -1, 'int');
        // 省份
        $province = I('get.province', 0, 'int');
        // 城市
        $city = I('get.city', 0, 'int');
        // 供应商id
        $supplierId = I('get.supplierId', 0, 'int');
        // 供应商名称
        $supplierName = I('get.supplierName', '', 'string');
        // 供应商所在省份
        $supplierProvince = I('get.supplierProvince', 0, 'int');
        // 供应商所在城市
        $supplierCity = I('get.supplierCity', 0, 'int');
        // 合作状态2=全部供应商, 1=已合作的供应商, 0=未合作的供应商
        $cooperation = I('get.cooperation', 2, 'int');
        // 采购状态 2=全部,1=已采购, 0=未采购
        $purchaseStatus = I('get.purchaseStatus', 2, 'int');
        // 产品类型
        $type = I('get.type', '', 'string');
        // 当前页数
        $pageNum = I('get.pageNum', 1, 'int');
        // 每页显示条数
        $pageSize = I('get.pageSize', 10, 'int');
        //时间状态: 0:全部 1:正常 2:即将过期 3:已过期 [默认：1]
        $timeStatus= I('get.timeStatus',1);

        $ticketBiz = new TicketApi();
        $res       = $ticketBiz->getProductTickets($this->_mid, $landId, $landName, $oversea, $province, $city,
            $supplierId, $supplierName, $supplierProvince,
            $supplierCity, $cooperation, $purchaseStatus, $type, $pageNum, $pageSize,$timeStatus);

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 资源中心产品列表-只展示产品
     * <AUTHOR>
     */
    public function productQuery()
    {
        // 产品名称
        $landName = I('get.landName', '', 'strval');
        // 境内外
        $oversea = I('get.oversea', -1, 'intval');
        // 省份
        $province = I('get.province', 0, 'intval');
        // 城市
        $city = I('get.city', 0, 'intval');
        // 供应商id
        $supplierId = I('get.supplierId', 0, 'intval');
        // 供应商名称
        $supplierName = I('get.supplierName', '', 'strval');
        // 供应商所在省份
        $supplierProvince = I('get.supplierProvince', 0, 'intval');
        // 供应商所在城市
        $supplierCity = I('get.supplierCity', 0, 'intval');
        // 合作状态2=全部供应商, 1=已合作的供应商, 0=未合作的供应商
        $cooperation = I('get.cooperation', 2, 'intval');
        // 产品类型
        $type = I('get.type', '', 'strval');
        // 当前页数
        $pageNum = I('get.pageNum', 1, 'intval');
        // 每页显示条数
        $pageSize = I('get.pageSize', 10, 'intval');
        // 购买状态
        $purchaseStatus = I('get.purchaseStatus', 2, 'intval');

        if (!empty($landName) || !empty($supplierName)) {
            $searchType = empty($landName) ? 1 : 2;
            $searchName = empty($landName) ? $supplierName : $landName;

            $productBiz = new ProductBiz();
            $productBiz->productHistoryRecord($this->_mid, $searchName, $searchType);
        }

        $ticketBiz = new TicketApi();
        $res       = $ticketBiz->resourceProductQuery($this->_mid, $pageNum, $pageSize, $landName,
            $oversea, $province, $city, $supplierId, $supplierName, $supplierProvince, $supplierCity,
            $cooperation, $type, $purchaseStatus);

        foreach ($res['data']['resultList'] as &$item) {
            $item['url'] = $item['land_imgpath'] ?? '';
        }

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 资源中心-批量推荐位景区推荐添加
     * <AUTHOR>
     */
    public function RecommendLandBatchSave()
    {
        $data = I('post.data');
        $api  = new ResourceCenterApi();
        $opId = $this->_loginInfo['memberID'];
        $res  = $api->resourceRecommendLandBatchSave($data, $opId);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 资源中心-资源中心首页景区拓展信息
     * <AUTHOR>
     */
    public function QueryIndexLandExpand()
    {
        $data = I('post.data');
        $api  = new ResourceCenterApi();
        $fid  = $this->_loginInfo['memberID'];
        $res  = $api->resourceQueryIndexLandEXPAND($data, $fid);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}