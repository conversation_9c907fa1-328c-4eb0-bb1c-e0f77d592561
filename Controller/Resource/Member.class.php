<?php

namespace Controller\Resource;

use Business\JavaApi\Product\ResourceCenterApi;
use Library\Controller;

class Member extends Controller
{

    private $loginInfo;

    public function __construct()
    {
        // 判断是否已经登陆
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 获取商家认证
     * <AUTHOR>
     */
    public function getSupplierCert()
    {
        $biz = new ResourceCenterApi();
        $res = $biz->resourceSupplierAuthentication($this->loginInfo['memberID']);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }


}