<?php
/**
 * 文件上传统一接口
 * 
 * <AUTHOR>
 * @date 2017/4/10
 */
namespace Controller\Resource;

use Library\Controller;
use Process\Resource\Qiniu;

class FileUpload extends Controller {

    // 允许上传的文件类型
    private $_allowType = ['xls', 'xlsx', 'doc', 'docx', 'ppt', 'rar','pdf'];

    // 本次上传文件的拓展名
    private $_ext;

    // 本次上传文件的原始文件名
    private $_rowName;

    // 最大文件大小 2m
    const __MAX_FILE_SIZE__  = 1048576*2;

    private $_loginInfo = null;

    public function __construct()
    {
        $this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
    }

    /**
     * 文件上传
     * <AUTHOR>
     * @date   2017-04-10
     *
     * @param string $identify 来源标识
     * @return array 上传成功后，返回文件URL [102未登录|200成功|204错误弹框]
     */
    public function upload()
    {
        $identify = I('identify');

        // 来源标识校验
        if (!$identify) {
            $this->apiReturn(204, [], '未找到本次上传的标识');
        }

        // 各个来源标识的特殊操作
        $this->_identifyOperate($identify);

        // 文件类型校验
        $file = $this->_validCheck();

        // 文件保存路径
        $savePath = $this->_parseSavePath($identify);
        if ($savePath ===  false) {
            $this->apiReturn(204, [], '来源错误');
        }

        // 文件上传
        $Qiniu          = new Qiniu;
        $qiniuConfig    = $Qiniu->getConfig();

        // debug 暂不开启真正上传，给前端调试
        $result         = $Qiniu->uploadFile($savePath, 'static');
        if (!$result) {
            $this->apiReturn(204, [], $Qiniu->getError());
        }

        // 文件地址        
        $fileUrl = $qiniuConfig['static']['domain'] . $savePath;

        $this->apiReturn(200,  ['src' => $fileUrl]);
    }

    /**
     * 上传合法性检测
     * <AUTHOR>
     * @date 2017/4/10
     */
    private function _validCheck()
    {
        if (count($_FILES) == 0) {
            $this->apiReturn(204, [], '请选择要上传的文件');
        }

        // 单文件上传
        $file = current($_FILES);

        if ($file['error'] != 0) {
            $this->apiReturn(204, [], '错误代码:'.$file['error']);
        }

        if ($file['size'] == 0) {
            $this->apiReturn(204, [], '文件数据为0');
        }

        if ($file['size'] > self::__MAX_FILE_SIZE__) {
            $this->apiReturn(204, [], '文件大小超出限制');
        }

        // 类型检测
        $fileName   = $file['name'];
        $fileInfo   = pathinfo($fileName);
        $this->_ext = $fileInfo['extension'] ?: '';
        if (!in_array($this->_ext, $this->_allowType)) {
            $this->apiReturn(204, [], '非法文件类型');
        }

        // 原始文件名
        $this->_rowName = $fileName;

        return $file;
    }

    /**
     * 获取文件保存路径
     * 
     * @param  string $identify 来源标识
     * @return realPath
     */
    private function _parseSavePath($identify)
    {
        $pathRule = load_config($identify, 'upload_file');

        if (!$pathRule) {
            return false;
        }

        $replaceRule = $this->_getRepalceRule();

        $find       = array_keys($replaceRule);
        $replace    = array_values($replaceRule);
        $realPath   = str_replace($find, $replace, $pathRule);

        // 存在不在预定义规则中的占位符
        if (stripos($realPath, '{') !== false) {
            return false;
        }
        // 是否需要手动加上文件扩展名
        if (stripos($realPath, '.') === false) {
            $realPath .= '.' . $this->_ext;
        }

        return $realPath;
    }

    /**
     * 获取路径替换规则
     * 
     * @return [type] [description]
     */
    private function _getRepalceRule()
    {
        return [
            '{time}'        => time(),
            '{date}'        => date('Y-m-d'),
            '{datetime}'    => date('YmdHis'),
            '{account}'  => $this->_loginInfo['account'],
            '{mid}'      => $this->_loginInfo['memberID'],
            '{saccount}' => $this->_loginInfo['saccount'],
            '{sid}'      => $this->_loginInfo['sid'],
            '{rowName}'     => $this->_rowName
        ];
    }

    /**
     * 针对上传文件各个来源标识的特殊操作
     * <AUTHOR>
     * @date   2017-04-10
     *
     * @param string $identify 上传来源标识
     */
    private function _identifyOperate($identify = '')
    {
        if ($identify && is_string($identify)) {

            if ($identify == 'batSendMsg') {
                // 批量发送设置
                // 文件内容合法性校验
                // ...
                // 只允许xls
                $this->_allowType = ['xls'];
            }elseif($identify == 'helpCenter'){
                $this->_allowType = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'rar','pdf'];
            }
        }
    }
}