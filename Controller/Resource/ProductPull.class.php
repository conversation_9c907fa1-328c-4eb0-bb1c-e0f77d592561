<?php

namespace Controller\Resource;

use Controller\Resource\ProductPull\ProductPullConfig;
use Controller\Resource\ProductPull\ProductPullService;
use Library\Controller;
use Model\Ota\UpStreamResourceModel;
use Exception;
use Throwable;

/**
 * 资源中心产品拉取
 * Class ProductPull
 *
 * <AUTHOR>
 * @date 2021-11-08
 * @package Controller\Resource
 */
class ProductPull extends Controller
{
    use ProductPullService, ProductPullConfig;

    /**
     * 操作类型
     * @var string[]
     */
    protected $opTypeMap = [
        0 => '拉取',
        1 => '更新',
        2 => '绑定',
        3 => '解绑',
    ];

    protected $sid = 0;

    protected $memberId = 0;

    protected $sysType = 'A';

    public function __construct()
    {
        parent::__construct();
        $loginInfo = $this->getLoginInfo();

        $this->sid      = $loginInfo['sid']; // 供应商 id
        $this->memberId = $loginInfo['memberID']; // 登录用户 id
    }

    /**
     * 获取系统映射
     *
     * <AUTHOR>
     * @date 2021-11-08
     *
     * @return bool|void
     */
    public function getConSysIdMap()
    {
        $data = [
            'tag_list' => $this->getSysMap(),
        ];
        return $this->apiReturn(200, $data, 'success');
    }

    /**
     * 获取配置
     *
     * <AUTHOR>
     * @date 2021-11-16
     *
     * @return bool|void
     */
    public function getConfig()
    {
        $conSysId = I('request.con_sys_id/d', 0);

        if (!$this->hasSysById($conSysId)) {
            return $this->apiReturn(203, [], 'con_sys_id 有误');
        }

        $upStreamResourceModel = new UpStreamResourceModel();
        $field                 = 'api_key,api_secret,base_uri,status';
        $pullConfigMap         = $upStreamResourceModel->getConfigFirstByApplyId($this->sid, $conSysId, $field);
        $data                  = array_merge($this->getSysViewDescById($conSysId), [
            'api_key'    => '',
            'api_secret' => '',
        ]);
        if (!empty($pullConfigMap['api_key'])) {
            // 如果地址不是写死的,需要把配置的地址反给前端
            $apiSecret = json_decode($pullConfigMap['api_secret'], true);
            if ($this->sysType == 'A' && !$this->hasSysUrlById($conSysId)) {
                $apiSecret['base_uri'] = $pullConfigMap['base_uri'];
            }

            $data = array_merge($data, [
                'api_key'    => $pullConfigMap['api_key'],
                'api_secret' => $this->responseApiSecretSwap($apiSecret),
                'status'     => $pullConfigMap['status'],
            ]);
        }
        return $this->apiReturn(200, $data, '获取成功');
    }

    /**
     * 添加、编辑配置
     *
     * <AUTHOR>
     * @date 2021-11-16
     *
     * @return bool|void
     */
    public function editConfig()
    {
        $conSysId  = I('request.con_sys_id/d', 0);
        $apiKey    = I('request.api_key');
        $apiSecret = I('request.api_secret');

        if (empty($apiKey)) {
            return $this->apiReturn(203, [], 'api_key 不能为空');
        }

        if (empty($apiSecret)) {
            return $this->apiReturn(203, [], 'api_secret 不能为空');
        }

        if (!$this->hasSysById($conSysId)) {
            return $this->apiReturn(203, [], 'con_sys_id 有误');
        }

        if ($this->sysType == 'Q' && $apiKey != $apiSecret) {
            return $this->apiReturn(203, [], '帐号、秘钥不一致');
        }

        $apiSecret = $this->requestApiSecretSwap($conSysId, $apiSecret);
        if (!$this->hasSysUrlById($conSysId) && !isset($apiSecret['base_uri']) && $this->sysType == 'A') {
            return $this->apiReturn(203, [], 'base_uri 不能为空');
        }

        $upStreamResourceModel = new UpStreamResourceModel();
        $field                 = 'id';
        $pullConfigMap         = $upStreamResourceModel->getConfigFirstByApplyId($this->sid, $conSysId, $field);

        $baseUri = '';
        if (strpos($this->sysType, 'A') !== false) {
            $baseUri = $this->hasSysUrlById($conSysId) ? $this->getSysUrlById($conSysId) : $apiSecret['base_uri'];
        }
        if (empty($pullConfigMap)) {
            $insertData = [
                'apply_id'   => $this->sid,
                'api_key'    => trim($apiKey),
                'api_secret' => json_encode($apiSecret, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
                'base_uri'   => $baseUri,
                'timeout'    => 30,
                'con_sys_id' => $conSysId,
                'status'     => 1, // 状态(0: 启用, 1: 禁用)
            ];
            $result     = $upStreamResourceModel->insertConfig($insertData);
        } else {
            // 每次修改账号秘钥,都需要把状态修改为不可用,通过检测后修改为启用
            $upData = [
                'api_key'    => trim($apiKey),
                'api_secret' => json_encode($apiSecret, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
                'base_uri'   => $baseUri,
                'status'     => 1, // 状态(0: 启用, 1: 禁用)
            ];
            $result = $upStreamResourceModel->updateConfig($upData, $pullConfigMap['id']);
        }

        // 数据库异常导致的失败
        if ($result === false) {
            return $this->apiReturn(205, [], '操作失败');
        }

        $id = $pullConfigMap['id'] ?? $result;

        // json-rpc 调用检测账号密钥接口
        $jsonRpcRes = $this->pullCheckService($conSysId);
        if ($jsonRpcRes['code'] == 200) {
            $upData = [
                'status' => 0, // 状态(0: 启用, 1: 禁用)
            ];
            $upStreamResourceModel->updateConfig($upData, $id);
        } else {
            // 清空失败数据
            $upData = [
                'api_key'    => '',
                'api_secret' => '',
                'base_uri'   => '',
                'status'     => 1, // 状态(0: 启用, 1: 禁用)
            ];
            $upStreamResourceModel->updateConfig($upData, $id);
            return $this->apiReturn($jsonRpcRes['code'], [], $jsonRpcRes['msg']);
        }

        return $this->apiReturn(200, [], '操作成功');
    }

    /**
     * 获取指定 id 产品
     *
     * <AUTHOR>
     * @date 2021-11-16
     *
     * @return bool|void
     * @throws Exception
     */
    public function pull()
    {
        $thirdProductIdList = I('request.third_product_id_list', []);
        $conSysId           = I('request.con_sys_id/d', 0);
        $page               = I('request.page/d', 1); // 页码

        if (empty($thirdProductIdList)) {
            return $this->apiReturn(203, [], '第三方产品 id 不能为空');
        }

        foreach ($thirdProductIdList as $thirdProductId) {
            if (!is_numeric($thirdProductId)) {
                return $this->apiReturn(203, [], '产品id有误');
            }
        }

        if (count($thirdProductIdList) > 20) {
            return $this->apiReturn(203, [], '单次拉取产品最大数量为 20');
        }

        if (!$this->hasSysById($conSysId)) {
            return $this->apiReturn(203, [], 'con_sys_id 有误');
        }

        $upStreamResourceModel = new UpStreamResourceModel();
        $field                 = 'id,status';
        $pullConfigMap         = $upStreamResourceModel->getConfigFirstByApplyId($this->sid, $conSysId, $field);
        if (empty($pullConfigMap)) {
            return $this->apiReturn(203, [], '请先添加配置信息');
        }

        if ($pullConfigMap['status'] != 0) {
            return $this->apiReturn(203, [], '账号密钥有误');
        }

        return $this->pullProductService($conSysId, array_unique($thirdProductIdList), $page);
    }

    /**
     * 创建产品
     *
     * <AUTHOR>
     * @date 2021-11-16
     *
     * @return bool|void
     * @throws Exception
     */
    public function create()
    {
        $thirdProductIdList = I('request.third_product_id_list', []);
        $thirdTicketIdMap   = I('request.third_ticket_id_map', []);
        $conSysId           = I('request.con_sys_id/d', 0);
        $opType             = I('request.op_type');

        if (empty($thirdProductIdList)) {
            return $this->apiReturn(203, [], '第三方产品 id 不能为空');
        }

        foreach ($thirdProductIdList as $thirdProductId) {
            if (!is_numeric($thirdProductId)) {
                return $this->apiReturn(203, [], '产品id有误');
            }
        }

        if (count($thirdProductIdList) > 20) {
            return $this->apiReturn(203, [], '单次拉取产品最大数量为 20');
        }

        try {
            $this->checkSystemStatus($conSysId);
        } catch (Throwable $e) {
            return $this->apiReturn(203, [], $e->getMessage());
        }

        if (empty($this->opTypeMap[$opType])) {
            return $this->apiReturn(203, [], 'op_type 有误');
        }

        $upStreamResourceModel = new UpStreamResourceModel();
        $field                 = 'id,status';
        $pullConfigMap         = $upStreamResourceModel->getConfigFirstByApplyId($this->sid, $conSysId, $field);
        if (empty($pullConfigMap)) {
            return $this->apiReturn(203, [], '请先添加配置信息');
        }

        if ($pullConfigMap['status'] != 0) {
            return $this->apiReturn(203, [], '账号密钥有误');
        }

        return $this->createProductService($conSysId, array_unique($thirdProductIdList), $thirdTicketIdMap, $opType);
    }

    /**
     * 门票解绑,日志记录
     *
     * <AUTHOR>
     * @date 2021-11-19
     *
     * @return bool|void
     */
    public function unbindTicket()
    {
        $unBindList = I('request.unbind_list', []);
        $conSysId   = I('request.con_sys_id/d', 0);

        if (empty($unBindList)) {
            return $this->apiReturn(203, [], '解绑信息不能为空');
        }

        if (!$this->hasSysById($conSysId)) {
            return $this->apiReturn(203, [], 'con_sys_id 有误');
        }

        $this->unbindTicketService($unBindList, $conSysId);
        return $this->apiReturn(200, [], '请求成功');
    }
}