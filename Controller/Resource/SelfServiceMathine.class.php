<?php

namespace Controller\Resource;

use Business\TerminalManage\TerminalManage;
use Library\Constants\Resource\SelfService;
use Library\Controller;
use Process\Resource\Qiniu;

/**
 * @Author: CYQ19931115
 * @Date:   2018-01-11 14:19:03
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2018-05-09 09:52:39
 */

/**
 * 自助机banner上传控制器
 */
class SelfServiceMathine extends Controller
{
    private $qiniu;

    public function __construct()
    {
        $this->qiniu = new Qiniu();
    }

    /**
     * 上传自助机图片
     * 上传的路径为selfservice/
     * <AUTHOR>
     * @dateTime 2018-01-11T14:21:56+0800
     * @throws   \Exception                             可能抛出异常
     * @return   [type]                   [description]
     */
    public function upload()
    {
        try {
            $this->isLogin('ajax');
            $machineCode = I("machineCode");
            if (!$machineCode) {
                $this->apiReturn(500, [], "没有自助机号码");
            }
            $qiniuFilenames = [];
            foreach ($_FILES as $key => $value) {
                $filePath = $value['tmp_name'];
                $this->checkUploadImage($filePath);
                $qiniuFilenames[] = SelfService::FOLDER . $machineCode . DIRECTORY_SEPARATOR . time() . "_".$key . '.' . substr(strrchr($value['name'], '.'), 1);
            }
            $result = $this->qiniu->uploadFile($qiniuFilenames);
            if ($result) {
                $this->apiReturn(200, [], "上传成功!");
            } else {
                $this->apiReturn(500, [], $this->qiniu->getError());
            }
        } catch (\Exception $e) {
            $this->apiReturn(500, [], $e->getMessage());
        }
    }

    /**
     * 展示自助机的广告页面
     * <AUTHOR>
     * @dateTime 2018-01-15T17:10:14+0800
     */
    public function getImages()
    {
        // $this->isLogin('ajax');
        $machineCode = I("machineCode");
        $type        = I('type','');
        if (!$machineCode) {
            $this->apiReturn(500, [], "没有自助机号码");
        }
        $image_url = [];
        if ($type){
            $terminalManageBiz = new TerminalManage();
            $newResult = $terminalManageBiz->getDevicePictureService($machineCode,$type);
            if (isset($newResult['data']['list']) && $newResult['data']['list']){

                foreach ($newResult['data']['list'] as $value) {
                    $image_url[] = [
                        "imgPath" => $value,
                    ];
                }
                $this->apiReturn(200, $image_url, "获取成功");
            }else{
                $this->apiReturn(500, [], "没有图片");
            }
        }
        $result    = $this->qiniu->getResourceList(SelfService::FOLDER . $machineCode);
        $config    = $this->qiniu->getConfig();
        $items     = $result['items']['items'];
        if ($items) {
            foreach ($items as $value) {
                $image_url[] = [
                    "imgPath" => $config['images']['domain'] . $value['key'],
                    "path"    => $value['key'],
                ];
            }
            $this->apiReturn(200, $image_url, "获取成功");
        } else {
            $this->apiReturn(500, [], "没有图片");
        }
    }

    /**
     * 删除指定文件
     * <AUTHOR>
     * @dateTime 2018-01-17T10:17:52+0800
     * @throws   \Exception                             可能抛出异常
     * @return   [type]                   [description]
     */
    public function delete()
    {
        $this->isLogin('ajax');
        $imageName = I('path');
        if(!strstr($imageName,SelfService::FOLDER)){
            $this->apiReturn(500, [], "不是自助机文件,请重新确认!");
        }
        if (!$imageName) {  
            $this->apiReturn(500, [], "没有文件路径");
        }
        $result = $this->qiniu->deleteResource($imageName);
        if (is_null($result)) {
            $this->apiReturn(200, [], "删除成功");
        }
        $this->apiReturn(500, [], $result->message());
    }

    /**
     * 检查文件的像素大小以及类型
     * <AUTHOR>
     * @dateTime 2018-01-15T17:20:56+0800
     * @throws   \Exception                         可能抛出异常
     * @param    [type]                   $filePath [description]
     * @return   [type]                             [description]
     */
    private function checkUploadImage($filePath)
    {
        $allow_ext = SelfService::ALLOW_EXT;
        if (is_file($filePath)) {
            $img      = @getimagesize($filePath);
            $ext      = image_type_to_extension($img['2']);
            $fileSize = filesize($filePath) / 1024;
            if ($fileSize > 1025) {
                throw new \Exception('文件应该小于1m,请适当降质!');
            }
            if (!in_array($ext, $allow_ext)) {
                throw new \Exception('文件类型错误! 只允许 jpg jpeg png 以及 gif');
            }
            if ($img[0] != 1080 || $img[1] != 970) {
                throw new \Exception('文件尺寸错误,请处理成1080x970');
            }
        }
    }
}
