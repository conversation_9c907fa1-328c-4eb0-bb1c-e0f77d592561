<?php

namespace Controller\Resource;

use Business\JavaApi\Product\ResourceCenterApi;
use Business\ResourceCenter\Product as ProductBiz;
use Library\Controller;
use Process\Resource\AreaProcess;

class Product extends Controller
{

    private $loginInfo;

    /**
     * 初始化信息
     * <AUTHOR>
     * @date   2018-2-5
     */
    public function __construct()
    {
        // 判断是否已经登陆
        $this->isLogin('ajax');
        // 获取登陆用户信息
        $this->loginInfo = $this->getLoginInfo();

        parent::__construct();
    }

    /**
     * 获取产品搜索省份配置
     * <AUTHOR>
     */
    public function getQueryProvinceConfig()
    {
        $data = [
            'product' => [
                [
                    'id'   => 14,
                    'name' => '山东省',
                ],
                [
                    'id'   => 5,
                    'name' => '河北省',
                ],
                [
                    'id'   => 19,
                    'name' => '海南省',
                ],
                [
                    'id'   => 13,
                    'name' => '江西省',
                ],
                [
                    'id'   => 7,
                    'name' => '辽宁省',
                ],
                [
                    'id'   => 17,
                    'name' => '湖南省',
                ],
                [
                    'id'   => 18,
                    'name' => '广东省',
                ],
                [
                    'id'   => 4,
                    'name' => '重庆市',
                ],
                [
                    'id'   => 12,
                    'name' => '福建省',
                ],
                [
                    'id'   => 29,
                    'name' => '广西壮族自治区',
                ],
                [
                    'id'   => 23,
                    'name' => '陕西省',
                ],
                [
                    'id'   => 11,
                    'name' => '安徽省',
                ],
                [
                    'id'   => 9,
                    'name' => '江苏省',
                ],
                [
                    'id'   => 3,
                    'name' => '上海市',
                ],
                [
                    'id'   => 20,
                    'name' => '四川省',
                ],
                [
                    'id'   => 1,
                    'name' => '北京市',
                ],
                [
                    'id'   => 10,
                    'name' => '浙江省',
                ],
                [
                    'id'   => 15,
                    'name' => '河南省',
                ],
                [
                    'id'   => 16,
                    'name' => '湖北省',
                ],
                [
                    'id'   => 22,
                    'name' => '云南省',
                ],
                [
                    'id'   => 6,
                    'name' => '山西省',
                ],
                [
                    'id'   => 26,
                    'name' => '黑龙江省',
                ],
                [
                    'id'   => 8,
                    'name' => '吉林省',
                ],
                [
                    'id'   => 24,
                    'name' => '甘肃省',
                ],
                [
                    'id'   => 21,
                    'name' => '贵州省',
                ],
                [
                    'id'   => 2,
                    'name' => '天津市',
                ],
                [
                    'id'   => 28,
                    'name' => '内蒙古自治区',
                ],
                [
                    'id'   => 31,
                    'name' => '新疆维吾尔自治区',
                ],
                [
                    'id'   => 30,
                    'name' => '宁夏回族自治区',
                ],
                [
                    'id'   => 25,
                    'name' => '青海省',
                ],
                [
                    'id'   => 27,
                    'name' => '西藏自治区',
                ],
                [
                    'id'   => 32,
                    'name' => '香港',
                ],
                [
                    'id'   => 33,
                    'name' => '澳门',
                ],
                [
                    'id'   => 34,
                    'name' => '台湾',
                ],
            ],

            'supplier' => [
                [
                    'id'   => 14,
                    'name' => '山东省',
                ],
                [
                    'id'   => 12,
                    'name' => '福建省',
                ],
                [
                    'id'   => 7,
                    'name' => '辽宁省',
                ],
                [
                    'id'   => 5,
                    'name' => '河北省',
                ],
                [
                    'id'   => 19,
                    'name' => '海南省',
                ],
                [
                    'id'   => 13,
                    'name' => '江西省',
                ],
                [
                    'id'   => 23,
                    'name' => '陕西省',
                ],
                [
                    'id'   => 9,
                    'name' => '江苏省',
                ],
                [
                    'id'   => 10,
                    'name' => '浙江省',
                ],
                [
                    'id'   => 18,
                    'name' => '广东省',
                ],
                [
                    'id'   => 15,
                    'name' => '河南省',
                ],
                [
                    'id'   => 29,
                    'name' => '广西壮族自治区',
                ],
                [
                    'id'   => 1,
                    'name' => '北京市',
                ],
                [
                    'id'   => 11,
                    'name' => '安徽省',
                ],
                [
                    'id'   => 20,
                    'name' => '四川省',
                ],
                [
                    'id'   => 16,
                    'name' => '湖北省',
                ],
                [
                    'id'   => 3,
                    'name' => '上海市',
                ],
                [
                    'id'   => 17,
                    'name' => '湖南省',
                ],
                [
                    'id'   => 4,
                    'name' => '重庆市',
                ],
                [
                    'id'   => 22,
                    'name' => '云南省',
                ],
                [
                    'id'   => 6,
                    'name' => '山西省',
                ],
                [
                    'id'   => 26,
                    'name' => '黑龙江省',
                ],
                [
                    'id'   => 8,
                    'name' => '吉林省',
                ],
                [
                    'id'   => 24,
                    'name' => '甘肃省',
                ],
                [
                    'id'   => 21,
                    'name' => '贵州省',
                ],
                [
                    'id'   => 2,
                    'name' => '天津市',
                ],
                [
                    'id'   => 28,
                    'name' => '内蒙古自治区',
                ],
                [
                    'id'   => 31,
                    'name' => '新疆维吾尔自治区',
                ],
                [
                    'id'   => 30,
                    'name' => '宁夏回族自治区',
                ],
                [
                    'id'   => 25,
                    'name' => '青海省',
                ],
                [
                    'id'   => 27,
                    'name' => '西藏自治区',
                ],
                [
                    'id'   => 32,
                    'name' => '香港',
                ],
                [
                    'id'   => 33,
                    'name' => '澳门',
                ],
                [
                    'id'   => 34,
                    'name' => '台湾',
                ],
            ],
        ];

        return $this->apiReturn(200, $data);
    }

    /**
     * 获取热门旅游等标签
     * <AUTHOR>
     */
    public function getHotTag()
    {
        $data = [
            'city' => [
                [
                    'id'   => 475,
                    'pid'  => '5',
                    'name' => '石家庄',
                ],
                [
                    'id'   => 370,
                    'pid'  => '13',
                    'name' => '南昌',
                ],
                [
                    'id'   => 356,
                    'pid'  => '14',
                    'name' => '枣庄',
                ],
                [
                    'id'   => 365,
                    'pid'  => '14',
                    'name' => '临沂',
                ],
                [
                    'id'   => 441,
                    'pid'  => '7',
                    'name' => '大连',
                ],
                [
                    'id'   => 372,
                    'pid'  => '13',
                    'name' => '九江',
                ],
                [
                    'id'   => 256,
                    'pid'  => '19',
                    'name' => '三亚',
                ],
                [
                    'id'   => 358,
                    'pid'  => '14',
                    'name' => '烟台',
                ],
                [
                    'id'   => 303,
                    'pid'  => '17',
                    'name' => '长沙',
                ],
            ],

            'topic' => [
                '乐游山水',
                '主题乐园',
                '水世界',
            ],
        ];

        return $this->apiReturn(200, $data);
    }

    /**
     * 资源中心商品界面搜索-供应商
     * <AUTHOR>
     */
    public function productQueryForTicket()
    {
        // 产品名称
        $landName = I('get.landName', '', 'strval');
        // 境内外
        $oversea = I('get.oversea', -1, 'intval');
        // 省份
        $province = I('get.province', 0, 'intval');
        // 城市
        $city = I('get.city', 0, 'intval');
        // 供应商id
        $supplierId = I('get.supplierId', 0, 'intval');
        // 供应商名称
        $supplierName = I('get.supplierName', '', 'strval');
        // 供应商所在省份
        $supplierProvince = I('get.supplierProvince', 0, 'intval');
        // 供应商所在城市
        $supplierCity = I('get.supplierCity', 0, 'intval');
        // 合作状态2=全部供应商, 1=已合作的供应商, 0=未合作的供应商
        $cooperation = I('get.cooperation', 2, 'intval');
        // 产品类型
        $type = I('get.type', '', 'strval');
        // 景区主题
        $topic = I('get.topic', '', 'strval');
        // 景区级别
        $landLevel = I('get.landLevel', '', 'strval');
        // 当前页数
        $pageNum = I('get.pageNum', 1, 'intval');
        // 每页显示条数
        $pageSize = I('get.pageSize', 10, 'intval');
        // 购买状态
        $purchaseStatus = I('get.purchaseStatus', 2, 'intval');
        // 景点ID
        $landId = I('get.lid', 0, 'intval');
        // 分销状态 0=全部,1=已分销，2=未分销
        $distributionStatus = I('get.distributionStatus', 0, 'intval');
        // 排序 0 销量排序（综合排序),1:最新上架排序
        $orderByType = I('get.orderByType', -1, 'intval');

        if (!empty($landName) || !empty($supplierName)) {
            $searchType = empty($landName) ? 1 : 2;
            $searchName = empty($landName) ? $supplierName : $landName;

            $productBiz = new ProductBiz();
            $productBiz->productHistoryRecord($this->loginInfo['memberID'], $searchName, $searchType);
        }

        $biz = new ResourceCenterApi();
        $res = $biz->resourceProductQueryForTicket($this->loginInfo['sid'], $landName,
            $oversea, $province, $city, $supplierId, $supplierName, $supplierProvince, $supplierCity,
            $cooperation, $type, $topic, $landLevel, $purchaseStatus, $landId, $distributionStatus,
            $orderByType, $pageNum, $pageSize);

        if ($res['code'] == 200) {
            if (!empty($res['data']['resultList'])) {
                foreach ($res['data']['resultList'] as &$item) {
                    $special = $item['ticketDetailDTO']['special_product'];
                    if (!empty($special)) {
                        $item['ticketDetailDTO']['special_product'] = $this->jsonParseSpecial($special);
                    }
                    $item['tags'] = array_values(array_values($this->_parseTicketsTags([$item['ticketDetailDTO']]))[0]);
                }
            }
        }

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 资源中心商品界面搜索-供应商
     * <AUTHOR>
     */
    public function productQueryForProduct()
    {
        // 产品名称
        $landName = I('get.landName', '', 'strval');
        // 境内外
        $oversea = I('get.oversea', -1, 'intval');
        // 省份
        $province = I('get.province', 0, 'intval');
        // 城市
        $city = I('get.city', 0, 'intval');
        // 供应商名称
        $supplierName = I('get.supplierName', '', 'strval');
        // 供应商所在省份
        $supplierProvince = I('get.supplierProvince', 0, 'intval');
        // 供应商所在城市
        $supplierCity = I('get.supplierCity', 0, 'intval');
        // 合作状态2=全部供应商, 1=已合作的供应商, 0=未合作的供应商
        $cooperation = I('get.cooperation', 2, 'intval');
        // 产品类型
        $type = I('get.type', '', 'strval');
        // 景区主题
        $topic = I('get.topic', '', 'strval');
        // 景区级别
        $landLevel = I('get.landLevel', '', 'strval');
        // 当前页数
        $pageNum = I('get.pageNum', 1, 'intval');
        // 每页显示条数
        $pageSize = I('get.pageSize', 10, 'intval');
        // 购买状态
        $purchaseStatus = I('get.purchaseStatus', 2, 'intval');
        // 标签id
        $tagId = I('get.tagId', 0, 'intval');
        // 排序 0 销量排序（综合排序),1:最新上架排序
        $orderByType = I('get.orderByType', -1, 'intval');

        if (!empty($landName) || !empty($supplierName)) {
            $searchType = empty($landName) ? 1 : 2;
            $searchName = empty($landName) ? $supplierName : $landName;

            $productBiz = new ProductBiz();
            $productBiz->productHistoryRecord($this->loginInfo['memberID'], $searchName, $searchType);
        }

        $biz = new ResourceCenterApi();
        $res = $biz->resourceProductQueryForProduct($this->loginInfo['sid'], $landName,
            $oversea, $province, $city, $supplierName, $supplierProvince, $supplierCity,
            $cooperation, $type, $topic, $landLevel, $purchaseStatus, $tagId, $orderByType, $pageNum, $pageSize);
        
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 资源中心商品界面搜索-供应商
     * <AUTHOR>
     */
    public function productQueryForSupplier()
    {
        // 产品名称
        $landName = I('get.landName', '', 'strval');
        // 境内外
        $oversea = I('get.oversea', -1, 'intval');
        // 省份
        $province = I('get.province', 0, 'intval');
        // 城市
        $city = I('get.city', 0, 'intval');
        // 供应商名称
        $supplierName = I('get.supplierName', '', 'strval');
        // 供应商所在省份
        $supplierProvince = I('get.supplierProvince', 0, 'intval');
        // 供应商所在城市
        $supplierCity = I('get.supplierCity', 0, 'intval');
        // 合作状态2=全部供应商, 1=已合作的供应商, 0=未合作的供应商
        $cooperation = I('get.cooperation', 2, 'intval');
        // 产品类型
        $type = I('get.type', '', 'strval');
        // 景区主题
        $topic = I('get.topic', '', 'strval');
        // 景区级别
        $landLevel = I('get.landLevel', '', 'strval');
        // 当前页数
        $pageNum = I('get.pageNum', 1, 'intval');
        // 每页显示条数
        $pageSize = I('get.pageSize', 10, 'intval');
        // 购买状态
        $purchaseStatus = I('get.purchaseStatus', 2, 'intval');
        // 排序 0 销量排序（综合排序),1:最新上架排序
        $orderByType = I('get.orderByType', -1, 'intval');
        if (!empty($landName) || !empty($supplierName)) {
            $searchType = empty($landName) ? 1 : 2;
            $searchName = empty($landName) ? $supplierName : $landName;

            $productBiz = new ProductBiz();
            $productBiz->productHistoryRecord($this->loginInfo['memberID'], $searchName, $searchType);
        }

        $biz = new ResourceCenterApi();
        $res = $biz->resourceProductQueryForSupplier($this->loginInfo['sid'], $landName,
            $oversea, $province, $city, $supplierName, $supplierProvince, $supplierCity,
            $cooperation, $type, $topic, $landLevel, $purchaseStatus,
            $orderByType, $pageNum, $pageSize);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取搜索历史记录
     * <AUTHOR>
     */
    public function getProductHistoryList()
    {
        // 1供应商，2产品
        $type = I('get.type', 1, 'intval');

        $productBiz = new ProductBiz();
        $data       = $productBiz->getProductHistoryList($this->loginInfo['memberID'], $type);

        return $this->apiReturn(200, $data);
    }

    /**
     * 删除搜索历史记录
     * <AUTHOR>
     */
    public function delProductHistoryRecord()
    {
        // 1供应商，2产品
        $type = I('get.type', 1, 'intval');

        $productBiz = new ProductBiz();
        $productBiz->delHistoryRecord($this->loginInfo['memberID'], $type);

        return $this->apiReturn(200, [], 'success');
    }

    /**
     * 解析票类的一些标签属性
     *
     * @param  [type] $tickets 门票列表
     *
     * @return [type]          [description]
     */
    private function _parseTicketsTags($tickets)
    {
        $tags           = [];
        $orderBookModel = new \Business\Order\OrderBook();
        foreach ($tickets as $item) {
            if ($item['preorder_early_days'] > 0) {
                $tags[$item['id']]['useTimeStr'] = "最早可订{$item['preorder_early_days']}天票";
                //} elseif ($item['preorder_early_days'] == 0 && time()<strtotime(date('Y-m-d'). $item['preorder_expire_time '] . ':00')) {
            } elseif ($item['preorder_early_days'] == 0) {
                $tags[$item['id']]['useTimeStr'] = "最早可订今日票";
            }
            $refundRuleStr                  = $orderBookModel::handleRefundRule($item['refund_audit'],
                $item['refund_rule'], $item['refund_early_minu']);
            $tags[$item['id']]['refundStr'] = $refundRuleStr;

            $expireStr                      = $orderBookModel::getValidityDate($item);
            $tags[$item['id']]['expireStr'] = $expireStr;

            if ($item['pay_way'] == 0) {
                $tags[$item['id']]['cash'] = '现场支付';
            }

            if ($item['pay'] == 3) {
                $tags[$item['pid']]['card'] = '会员卡支付';
            }

            //退票规则：添加了这个值 -1=不可退且是可提现
            // if ($item['refund_rule'] == 2 || $item['refund_rule'] == -1) {
            //     $tags[$item['id']][] = '不可退';
            // }
        }

        return $tags;
    }

    /**
     * Notes:处理特产商品的json数据
     *
     * @param $special
     *
     * @return mixed
     * <AUTHOR>
     * @date 2020/2/27 13:58
     */
    public function jsonParseSpecial($special)
    {
        if (!empty($special)) {
            $special     = json_decode($special, true);
            $spec_params = $special['spec_params'];
            if (!empty($spec_params)) {
                $special['spec_params'] = json_decode($spec_params, true);
            }
        }

        return $special;
    }
    /**
     * 资源中心景区列表拓展信息
     * <AUTHOR>
     */
    public function queryforLandExpand()
    {
        $data = I("post.data");
        $biz = new ResourceCenterApi();
        $res = $biz->resourceProductQueryForLandExpand($this->loginInfo['sid'], $this->loginInfo['memberID'], $data);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }


}