<?php
/**
 * 管理后端
 */

namespace Controller\Resource;

use Business\JavaApi\TicketApi;
use Library\Controller;
use Business\ResourceCenter\ManageBackend as ManageBackendBusiness;

class ManageBackend extends Controller
{
    /**
     * 登陆信息
     * @var array
     */
    private $_loginInfo;

    /**
     * 初始化信息
     * <AUTHOR>
     * @date   2018-2-5
     */
    public function __construct()
    {
        // 判断是否已经登陆
        $this->isLogin('ajax');
        // 获取登陆用户信息
        $this->_loginInfo = $this->getLoginInfo();

        parent::__construct();
    }

    /**
     * 查询banner
     * <AUTHOR>
     */
    public function queryBannerList()
    {
        //if ($this->_loginInfo['sid'] != 1) {
        //    $this->apiReturn(400, [], '无权限');
        //}
        $enabledState = I('get.enabledState', 0, 'intval'); // 启用状态:1=是,0=不是
        $deleteType   = I('get.deteleType', 0, 'intval');  // 显示历史删除 1:是,0:否
        $pageNum      = I('get.pageNum', 1, 'intval'); // 页数
        $pageSize     = I('get.pageSize', 15, 'intval'); // 页码
        $place        = I('get.place', -1, 'intval'); // 位置:0=top,1=right,2=left,3=bottom

        $biz = new TicketApi();
        $res = $biz->resourceQueryBannerList($this->_loginInfo['memberID'], $enabledState, $deleteType,
            $place, $pageNum, $pageSize);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 资源中心产品配置-批量下架产品
     * <AUTHOR>
     */
    public function productBatchSoldOut()
    {
        $data = I('post.data');
        $opid = $this->_loginInfo['memberID'];

        $biz = new TicketApi();
        $res = $biz->resourceAdminProductBatchSoldOut($data, $opid);

        $this->apiReturn($res['code'], [], $res['msg']);
    }
}