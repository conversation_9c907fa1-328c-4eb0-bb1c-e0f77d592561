<?php

namespace Controller\Resource;

use Business\JavaApi\TicketApi;
use Library\Controller;

class ReviewProduct extends Controller
{
    const HTTP_PARAMETER_VALIDATE_CODE = 422;//参数校验错误
    const HTTP_METHOD_ERROR_CODE       = 405;//请求方法错误

    /*--------修改审核状态 status-------*/
    const UPDATE_REVIEW_PASS = 2;//审核通过
    const UPDATE_REVIEW_FAIL = 3;//审核失败

    /*--------资源中心审核列表 status-------*/
    const INDEX_All_REVIEW_STATUS    = 0;//全部
    const INDEX_ALEADY_REVIEW_STATUS = 1;//已审核
    const INDEX_UNREVIEW_STATUS      = 2;//未审核

    /*-------资源中心审核列表 checkType--------*/
    const CHECK_TYPE_REVIEW_ALL  = 0;//全部
    const CHECK_TYPE_REVIEW_PASS = 1;//审核通过
    const CHECK_TYPE_REVIEW_FAIL = 2;//审核不通过

    /**返回码消息
     * @var array
     */
    public static $codeMsg = [
        self::HTTP_PARAMETER_VALIDATE_CODE => '参数错误!',
        self::HTTP_METHOD_ERROR_CODE       => '方法错误!',
    ];

    /**修改审核状态 审核数组
     * @var array
     */
    public static $updateReviewStatusArr = [
        self::UPDATE_REVIEW_PASS,
        self::UPDATE_REVIEW_FAIL,
    ];

    /**资源中心审核列表 status
     * @var array
     */
    public static $checkTypeReviewStatusArr = [
        self::CHECK_TYPE_REVIEW_ALL,
        self::CHECK_TYPE_REVIEW_PASS,
        self::CHECK_TYPE_REVIEW_FAIL,
    ];

    /**资源中心审核列表 checkType
     * @var array
     */
    public static $indexReviewStatusArr = [
        self::INDEX_All_REVIEW_STATUS,
        self::INDEX_ALEADY_REVIEW_STATUS,
        self::INDEX_UNREVIEW_STATUS,
    ];

    private $reviewApi;//审核api
    private $userInfo;//用户信息

    public function __construct()
    {
        parent::__construct();
        if (!$this->reviewApi) {
            $this->reviewApi = new TicketApi();
        }
        if (!$this->userInfo) {
            $this->userInfo = $this->getLoginInfo();
        }
    }

    /**
     * Notes:获取审核列表
     * @return bool
     * <AUTHOR>
     * @date 2020/2/24 14:18
     */
    public function index()
    {
        $status = I('get.status/d');
        if (!in_array($status, self::$indexReviewStatusArr)) {
            return $this->varify();
        }
        $checkType = I('get.checkType/d', self::CHECK_TYPE_REVIEW_ALL);
        if (!in_array($checkType, self::$checkTypeReviewStatusArr)) {
            return $this->varify();
        }
        $landName     = I('get.landName/s');
        $supplierName = I('get.supplierName/s');
        $startDate    = strtotime(I('get.startDate'));
        $endDate      = strtotime(I('get.endDate'));
        $pType        = I('get.pType', '', 'strval');
        //结束时间为此日期23:59:59
        if (!empty($endDate)) {
            $endDate += 24 * 3600 - 1;
        }
        $page_num  = I('get.page_num/d');
        $page_size = I('get.page_size/d');
        if (!$page_num || !$page_size) {
            return $this->varify();
        }
        $result = $this->reviewApi->reviewIndex($status, $checkType, $landName, $supplierName, $startDate, $endDate,
            $pType, $page_num, $page_size);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * Notes:修改审核状态
     * @return bool
     * <AUTHOR>
     * @date 2020/2/24 16:12
     */
    public function update()
    {
        if (!self::isPost()) {
            return $this->methodFail();
        }
        $id     = I('post.id', '', 'strval');
        $status = I('post.status/d');
        if (!in_array($status, self::$updateReviewStatusArr)) {
            return $this->varify();
        }
        $msg  = I('post.msg/s');
        $opid = $this->userInfo['memberID'];
        if (!$id || !$status || !$opid) {
            return $this->varify();
        }

        $id = explode(',', $id);

        $result = $this->reviewApi->reviewUpdate($id, $status, $msg, $opid);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * Notes:添加审核记录
     * @return bool
     * <AUTHOR>
     * @date 2020/2/24 16:25
     */
    public function store()
    {
        if (!self::isPost()) {
            return $this->methodFail();
        }

        $lid              = I('post.lid/d');
        $pid              = I('post.pid/d');
        $tid              = I('post.tid/d');
        $priceSpreadFloat = I('post.priceSpread/f');
        $sid              = $this->userInfo['sid'];
        $opid             = $this->userInfo['memberID'];
        if (!$sid || !$lid || !$pid || !$tid || !$opid || $priceSpreadFloat === '') {
            return $this->varify();
        }

        //验证分销商是否有权限
        $bondBiz   = new \Business\ResourceCenter\Bond();
        $bondState = $bondBiz->operateVerify($sid, $this->userInfo['sdtype']);
        if (isset($bondState['code'])  && $bondState['code'] !== 200) {
            return $this->apiReturn(400, [], $bondState['msg']);
        }

        $priceSpread = intval(strval($priceSpreadFloat * 100));
        $result      = $this->reviewApi->reviewStore($sid, $lid, $pid, $tid, $priceSpread, $opid);

        return $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**参数验证失败
     * Notes:
     * @return bool
     * <AUTHOR>
     * @date 2020/2/24 14:22
     */
    public function varify()
    {
        return $this->apiReturn(self::HTTP_PARAMETER_VALIDATE_CODE, [],
            self::$codeMsg[self::HTTP_PARAMETER_VALIDATE_CODE]);
    }

    /**
     * Notes:请求方式错误
     * @return bool
     * <AUTHOR>
     * @date 2020/2/24 15:04
     */
    public function methodFail()
    {
        return $this->apiReturn(self::HTTP_METHOD_ERROR_CODE, [], self::$codeMsg[self::HTTP_METHOD_ERROR_CODE]);
    }
}