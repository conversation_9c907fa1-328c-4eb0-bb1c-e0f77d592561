<?php
/**
 * 资源中心
 * OTA渠道禁用设置
 *
 * <AUTHOR>
 * @date    2020-07-08
 * @modify  2020-07-08
 */

namespace Controller\Resource;

use Business\MemberLogin\MemberLoginHelper;
use Library\Controller;
use Business\JavaApi\Ota\OtaResource as OtaResourceApi;

class Ota extends Controller
{
    private $loginInfo = [];

    public function __construct()
    {
        //验证是否登录
        $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 配置OTA渠道信息
     * 旧版-废弃
     *
     * @date   2020-07-09
     * <AUTHOR>
     *
     * @deprecated
     */
    public function updateConfig()
    {
        $memberSID = $this->loginInfo['sid']; //上级id
        $memberID  = $this->loginInfo['memberID']; //操作人员id
        $sid       = I('post.sid', 0, 'intval'); //供应商id, 直接获取当前登录信息，仅注释
        $pid       = I('post.pid', 0, 'intval'); //产品ID
        $otaUserId = I('post.fid', 0, 'intval'); //这里的fid是先前Java接口返回的渠道id
        $status    = I('post.status', 0, 'intval'); //0不禁用渠道；1时禁用渠道

        //验证要更新的状态
        if (!in_array($status, [0, 1])) {
            $this->apiReturn(204, [], '状态错误');
        }

        //验证
        if (!$pid || !$otaUserId) {
            $this->apiReturn(204, [], '参数错误');
        }

        //配置更新
        $otaResourceApi = new OtaResourceApi();
        $config         = $otaResourceApi->configHandle($memberSID, $otaUserId, $pid, $memberID, $status);

        if (isset($config['code']) && $config['code'] == 200) {
            $this->apiReturn(200, $config['data'], 'success');
        } else {
            $this->apiReturn(500, [], $config['msg']);
        }
    }

    /**
     * 资源中心供货管理保存
     * 注： 由于资源中心禁售ota需要解绑已绑定的（采购来源是资源中心的）ota渠道产品，这边做下解绑处理。
     * 处理逻辑：所有参数转发给资源中心->资源中心返回成功后->获取需要解绑的信息->过滤掉非资源中心过来的->解绑
     * <AUTHOR>
     * @date 2021/11/19
     *
     */
    public function batchBuildResourceProductInfo()
    {
        $sid = $this->loginInfo['sid']; //商户id
        $memberId  = $this->loginInfo['memberID']; //当前用户id

        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }
        $data = I('post.');
        if (empty($data)){
            $this->apiReturn(203, [], '参数缺失');
        }

        if (!is_array($data)) {
            $this->apiReturn(203, [], '参数格式错误');
        }


        $otaResourceApi = new OtaResourceApi();
        $config         = $otaResourceApi->batchBuildResourceProductInfoHandel($sid, $memberId, $data);

        if (isset($config['code']) && $config['code'] == 200) {
            $this->apiReturn(200, $config['data'], 'success');
        } else {
            $this->apiReturn(500, [], $config['msg']);
        }
    }
}