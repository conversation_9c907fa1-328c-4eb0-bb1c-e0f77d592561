<?php

namespace Controller\Resource;

use Library\Controller;
use Model\AdminConfig\SystemSetting;

/**
 * @Author: CYQ19931115
 * @Date:   2018-01-11 14:17:44
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2018-01-12 16:54:47
 */
/**
 * 独立域名配置控制器
 */
class IndependentDomain extends Controller
{
    /**
     * 添加独立域名关联
     * <AUTHOR>
     * @dateTime 2018-01-11T14:22:16+0800
     * @throws   \Exception                可能抛出异常
     */
    public function addRelationDomain()
    {
        $isLogin = $this->isLogin();
        if (!$isLogin) {
            $this->apiReturn(500, [], '请登录');
        }
        $domain = I("key");
        $url    = I("url");
        $title  = I("title");
        if (empty($domain) || empty($url)) {
            $this->apiReturn(500, [], "参数错误");
        }
        $value         = [$domain, $url, $title];
        $value         = json_encode($value, JSON_UNESCAPED_UNICODE);
        $systemSetting = new SystemSetting();
        $result        = $systemSetting->addConfig($domain, $value);
        if ($result) {
            $this->apiReturn(200, [], "成功");
        } else {
            $this->apiReturn(500, [], '失败');
        }

    }

    /**
     * 获取相关域名对应的映射关系
     * <AUTHOR>
     * @dateTime 2018-01-11T14:51:45+0800
     * @throws   \Exception                             可能抛出异常
     * @return   [type]                   [description]
     */
    public function getDomainConfig()
    {
        $key           = I("key");
        $key           = md5($key);
        $systemSetting = new SystemSetting();
        $result        = $systemSetting->getConfig($key);
        if ($result) {
            $this->apiReturn(200, $result, "获取成功");
        } else {
            $this->apiReturn(500, [], "没有数据");
        }
    }
}
