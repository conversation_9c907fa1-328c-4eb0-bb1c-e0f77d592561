<?php

namespace Controller\Resource;

use Business\Authority\AuthContext;
use Business\JavaApi\Member\MemberQuery;
use Controller\Resource\ProductPull\ProductPullConfig;
use Controller\Resource\ProductPull\ProductPullService;
use Library\Controller;
use Library\JsonRpc\TVRpcClient;
use Model\Ota\UpStreamResourceModel;
use Exception;
use Throwable;

/**
 * 资源中心产品拉取日志记录列表
 *
 * Class ProductPullRecord
 *
 * <AUTHOR>
 * @date 2021-11-25
 * @package Controller\Resource
 */
class ProductPullRecord extends Controller
{
    use ProductPullService, ProductPullConfig;

    protected $sid = 0;

    protected $memberId = 0;

    protected $sysType = 'A';

    protected $dtype;

    protected $daySecond = 86400;

    protected $dayInterval = 30; // 查询间隔 (天)

    public function __construct()
    {
        parent::__construct();
        $loginInfo = $this->getLoginInfo();

        $this->sid      = $loginInfo['sid']; // 供应商 id
        $this->memberId = $loginInfo['memberID']; // 登录用户 id
        $this->dtype    = $loginInfo['dtype']; // 登录账号类型
    }

    /**
     * 获取操作人列表
     * <AUTHOR>
     * @date 2021-11-29
     *
     * @return bool|void
     */
    public function operatorList()
    {
        $page         = I('request.page/d', 1); // 第几页
        $pageSize     = I('request.page_size/d', 15); // 每页数量
        $operatorList = [];

        // 获取上级账号
        if ($page == 1) {
            $memberQueryApi = new MemberQuery();
            $javaApiRes     = $memberQueryApi->queryMemberByMemberId($this->sid);
            if ($javaApiRes['code'] == 200) {
                $operatorList[] = [
                    'operator_id' => $javaApiRes['data']['memberInfo']['id'],
                    'name'        => $javaApiRes['data']['memberInfo']['dname'],
                    'account'     => $javaApiRes['data']['memberInfo']['account'],
                ];
            }
        }

        // 获取所有员工
        $business   = new AuthContext();
        $memberId   = $business->getMemberId($this->sid, $this->memberId, $this->dtype);
        $memberList = $business->getMemberList('', $memberId, $page, $pageSize);
        $total      = $memberList['count'];
        if ($page == 1) {
            $total++;
        }
        foreach ($memberList['list'] as $member) {
            $operatorList[] = [
                'operator_id' => $member['id'],
                'name'        => $member['name'],
                'account'     => $member['account'],
            ];
        }

        $data = [
            'list'      => $operatorList,
            'total'     => $total,
            'page'      => $page,
            'page_size' => $pageSize,
        ];

        return $this->apiReturn(200, $data, '获取成功');
    }

    /**
     * 获取记录列表
     *
     * <AUTHOR>
     * @date 2021-11-25
     *
     * @return bool|void
     */
    public function recordList()
    {
        $conSysId   = I('request.con_sys_id/d', 0);
        $page       = I('request.page/d', 1); // 第几页
        $pageSize   = I('request.page_size/d', 15); // 每页数量
        $operatorId = I('request.operator_id/d', 0); // 操作人 id
        $opType     = I('request.op_type/s'); // 操作类型 (0: 绑定 1: 更新 2: 解绑)
        $status     = I('request.status/s'); // 操作状态 (0: 拉取中 1: 成功 2: 失败 3: 部分成功)
        $startedAt  = I('request.started_at'); // 开始时间 2021-11-23 17:20:02
        $endedAt    = I('request.ended_at'); // 结束时间 2021-11-25 17:20:05

        if (empty($startedAt) || empty($endedAt)) {
            $startedAt = date('Y-m-d 00:00:00', time());
            $endedAt   = date('Y-m-d 23:59:59', time());
        }
        $startTime = strtotime($startedAt);
        $endTime   = strtotime($endedAt);

        if ($startTime > $endTime) {
            return $this->apiReturn(203, [], '开始时间不能大于结束时间');
        }

        $timeInterval = $endTime - $startTime;
        if (floor($timeInterval / $this->daySecond) > $this->dayInterval) {
            return $this->apiReturn(203, [], '查询的时间间隔不能超过 ' . $this->dayInterval . ' 天');
        }

        $this->hasSysById($conSysId);
        if ($this->sysType == 'Q') {
            $data = [
                'list'      => [],
                'total'     => 0, // 总数
                'page'      => $page, // 当前页
                'page_size' => $pageSize, // 每页数量
            ];

            try {
                $statusMap = [
                    0 => 2,
                    1 => 10,
                    2 => 20,
                    3 => 3,
                ];
                $rpcData   = [
                    'merchant_id'        => $this->sid,
                    'third_partner_id'   => $conSysId,
                    'operate_start_time' => $startedAt,
                    'operate_end_time'   => $endedAt,
                    'page'               => $page,
                    'page_size'          => $pageSize,
                ];

                if ($opType != '') {
                    $rpcData['operate_type'] = ++$opType;
                }

                if ($status != '') {
                    $rpcData['status'] = $statusMap[$status];
                }

                if (!empty($operatorId)) {
                    $rpcData['operator_id'] = $operatorId;
                }

                $lib = new TVRpcClient('travel_voucher_service');
                $res = $lib->call('intranet/v1/third_product/pull_jobs', $rpcData, 'scenic');
                if (!empty($res['list'])) {
                    $data['total'] = $res['total'];
                    $list          = [];

                    $statusTextMap = [
                        2  => '进行中',
                        10 => '成功',
                        20 => '失败',
                        3  => '部分成功',
                    ];

                    $memberQueryApi = new MemberQuery();
                    $operatorMap    = [];
                    foreach ($res['list'] as $value) {
                        $operatorId = $value['operator_id'];
                        if (!isset($operatorMap[$operatorId])) {
                            $javaApiRes = $memberQueryApi->queryMemberByMemberId($operatorId);
                            if ($javaApiRes['code'] == 200) {
                                $operatorMap[$operatorId] = [
                                    'name'    => $javaApiRes['data']['memberInfo']['dname'],
                                    'account' => $javaApiRes['data']['memberInfo']['account'],
                                ];
                            }
                        }
                        $account = $operatorMap[$operatorId]['account'] ?? '';

                        $list[] = [
                            'id'               => $value['id'],
                            'account'          => $account,
                            'name'             => $value['operator_name'],
                            'op_msg'           => $value['remark'],
                            'op_type'          => $value['operate_type'],
                            'op_type_text'     => $value['operate_name'],
                            'pft_product_id'   => $value['old_centre_lid'],
                            'status'           => array_flip($statusMap)[$value['status']] ?? '',
                            'status_text'      => $statusTextMap[$value['status']],
                            'third_product_id' => $value['third_spu_id'],
                            'title'            => $value['spu_name'],
                            'updated_at'       => $value['operated_at'],
                        ];
                    }
                    $data['list'] = $list;
                }
            } catch (Exception $e) {

            }

            return $this->apiReturn(200, $data, '获取成功');
        }

        $upStreamResourceModel = new UpStreamResourceModel();
        $pullConfigMap         = $upStreamResourceModel->getConfigFirstByApplyId($this->sid, $conSysId, 'id');
        if (empty($pullConfigMap)) {
            return $this->apiReturn(200, [], '获取成功');
        }
        $confId = $pullConfigMap['id'];

        $recordList      = [];
        $recordListCount = $upStreamResourceModel->getRecordListCountByConfId($confId, $operatorId, $startedAt,
            $endedAt, $opType, $status);
        if (!empty($recordListCount)) {
            $upStreamResourceModel->setPage($page)
                                  ->setPageSize($pageSize);
            $field      = 'id,operator_id,pft_lid_or_tid as pft_product_id,third_id as third_product_id,
            title,op_type,op_msg,status,updated_at';
            $recordList = $upStreamResourceModel->getRecordListByConfId($confId, $operatorId, $startedAt, $endedAt,
                $opType, $status, $field);
        }

        $statusMap = [
            '进行中',
            '成功',
            '失败',
            '部分成功',
        ];

        $opTypeMap = [
            '绑定',
            '更新',
            '解绑',
        ];

        $memberQueryApi  = new MemberQuery();
        $operatorName    = '';
        $operatorAccount = 0;
        $tmpOperatorId   = 0;
        foreach ($recordList as &$record) {
            if ($tmpOperatorId != $record['operator_id']) {
                $javaApiRes    = $memberQueryApi->queryMemberByMemberId($record['operator_id']);
                $tmpOperatorId = $record['operator_id'];

                if ($javaApiRes['code'] == 200) {
                    $operatorName    = $javaApiRes['data']['memberInfo']['dname'];
                    $operatorAccount = $javaApiRes['data']['memberInfo']['account'];
                }
            }

            $record['name']         = $operatorName;
            $record['account']      = $operatorAccount;
            $record['status_text']  = $statusMap[$record['status']];
            $record['op_type_text'] = $opTypeMap[$record['op_type']];
            unset($record['operator_id']);
        }

        $data = [
            'list'      => $recordList,
            'total'     => intval($recordListCount), // 总数
            'page'      => $page, // 当前页
            'page_size' => $pageSize, // 每页数量
        ];

        return $this->apiReturn(200, $data, '获取成功');
    }

    /**
     * 详情
     *
     * <AUTHOR>
     * @date 2021-11-25
     *
     * @return bool|void
     */
    public function detail()
    {
        $id       = I('request.id/d'); // 记录 id
        $page     = I('request.page/d', 1); // 第几页
        $pageSize = I('request.page_size/d', 15); // 每页数量
        $conSysId = I('request.con_sys_id/d', 0);
        $status   = I('request.status/s'); // 操作状态 (0: 拉取中 1: 成功 2: 失败)
        if (empty($id)) {
            return $this->apiReturn(203, [], 'id 有误');
        }

        $this->hasSysById($conSysId);
        if ($this->sysType == 'Q') {
            $data = [
                'list'      => [],
                'total'     => 0, // 总数
                'page'      => $page, // 当前页
                'page_size' => $pageSize, // 每页数量
            ];
            try {
                $statusMap = [
                    0 => 2,
                    1 => 10,
                    2 => 20,
                ];
                $rpcData   = [
                    'merchant_id' => $this->sid,
                    'job_id'      => $id,
                    'page'        => $page,
                    'page_size'   => $pageSize,
                ];

                if ($status != '') {
                    $rpcData['status'] = $statusMap[$status];
                }

                $lib = new TVRpcClient('travel_voucher_service');
                $res = $lib->call('intranet/v1/third_product/pull_subjobs', $rpcData, 'scenic');
                if (!empty($res['list'])) {
                    $data['total'] = $res['total'];
                    $list          = [];

                    $statusTextMap = [
                        2  => '进行中',
                        10 => '成功',
                        20 => '失败',
                    ];

                    foreach ($res['list'] as $value) {
                        $list[] = [
                            'id'              => $value['id'],
                            'op_msg'          => $value['error_message'],
                            'pft_ticket_id'   => $value['old_centre_tid'],
                            'status'          => array_flip($statusMap)[$value['status']] ?? '',
                            'status_text'     => $statusTextMap[$value['status']],
                            'third_ticket_id' => $value['third_sku_id'],
                            'title'           => empty($value['third_sku_name']) ? $value['sku_name'] : $value['third_sku_name'],
                            'updated_at'      => $value['finished_at'],
                        ];
                    }
                    $data['list'] = $list;
                }
            } catch (Exception $e) {

            }

            return $this->apiReturn(200, $data, '获取成功');
        }

        $upStreamResourceModel = new UpStreamResourceModel();
        $pullConfigMap         = $upStreamResourceModel->getConfigFirstByApplyId($this->sid, $conSysId, 'id');
        if (empty($pullConfigMap)) {
            return $this->apiReturn(203, [], '请先添加配置信息');
        }

        $recordDetailListCount = $upStreamResourceModel->getDetailListCountByParentId($pullConfigMap['id'], $id,
            $status);

        $upStreamResourceModel->setPage($page)
                              ->setPageSize($pageSize);
        $field            = 'id,pft_lid_or_tid as pft_ticket_id,third_id as third_ticket_id,
        title,op_msg,status,updated_at';
        $recordDetailList = $upStreamResourceModel->getDetailListByParentId($pullConfigMap['id'], $id, $status, $field);
        $statusMap        = [
            '进行中',
            '成功',
            '失败',
        ];
        foreach ($recordDetailList as &$RecordDetail) {
            $RecordDetail['status_text'] = $statusMap[$RecordDetail['status']];
        }

        $data = [
            'list'      => $recordDetailList,
            'total'     => intval($recordDetailListCount), // 总数
            'page'      => $page, // 当前页
            'page_size' => $pageSize, // 每页数量
        ];
        return $this->apiReturn(200, $data, '获取成功');
    }

    /**
     * 重试
     *
     * <AUTHOR>
     * @date 2021-11-25
     *
     * @return bool|void
     * @throws Exception
     */
    public function retry()
    {
        $id       = I('request.id/d'); // 记录 id
        $conSysId = I('request.con_sys_id/d', 0);

        if (empty($id)) {
            return $this->apiReturn(203, [], 'id 不能为空');
        }

        try {
            $this->checkSystemStatus($conSysId);
        } catch (Throwable $e) {
            return $this->apiReturn(203, [], $e->getMessage());
        }
        if ($this->sysType == 'Q') {
            try {
                $rpcData = [
                    'merchant_id' => $this->sid,
                    'subjob_id'   => $id,
                ];

                $lib = new TVRpcClient('travel_voucher_service');
                $lib->call('intranet/v1/third_product/pull_subjob/retry', $rpcData, 'scenic');
                $result['code'] = 200;
                $result['msg']  = 'success';
            } catch (Exception $e) {
                $result['code'] = $e->getCode();
                $result['msg']  = $e->getMessage();
            }

            return $this->apiReturn($result['code'], [], $result['msg']);
        }

        $upStreamResourceModel = new UpStreamResourceModel();
        $field                 = 'conf_id,status,op_type,request';
        $recordMap             = $upStreamResourceModel->getRecordFirstById($id, $this->sid, $field);
        if (empty($recordMap)) {
            return $this->apiReturn(203, [], 'id 有误');
        }

        if ($recordMap['status'] != 2) {
            return $this->apiReturn(203, [], '状态有误,无法发起重试');
        }
        $opType        = $recordMap['op_type']; // 操作类型 (0: 绑定 1: 更新 2: 解绑)
        $confId        = $recordMap['conf_id'];
        $requestDecode = json_decode($recordMap['request'], true);
        $pullConfigMap = $upStreamResourceModel->getConfigFirstById($confId, 'con_sys_id');
        $conSysId      = $pullConfigMap['con_sys_id'];

        // 需要区分操作类型
        if ($opType == 2) {
            $this->unbindTicketService($requestDecode, $conSysId);
            return $this->apiReturn(200, [], '请求成功');
        } else {
            $thirdProductIdList = [
                $requestDecode['third_product_id'],
            ];
            $thirdTicketIdMap   = $requestDecode['third_ticket_id_map'];
            return $this->createProductService($conSysId, $thirdProductIdList, $thirdTicketIdMap, $opType);
        }
    }
}