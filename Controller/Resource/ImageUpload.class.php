<?php

/**
 * 图片上传统一接口
 *
 * <AUTHOR>
 * @date 2017/2/15
 */

namespace Controller\Resource;

use Library\Controller;
use Process\Resource\Qiniu;
use Business\Wechat\WechatTrusteeship;

class ImageUpload extends Controller
{
    private $_loginInfo = null;

    public function __construct()
    {
        if (in_array($_REQUEST['a'], ['getUploadToken'])) {
            //七牛云token获取的时候不进行登录判断
        } else {
            $this->isLogin('ajax');
            $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
        }
    }

    //允许上传的图片类型
    private $_allowType = ['png', 'jpg', 'jpeg', 'gif', 'x-png', 'pjpeg', 'bmp'];
    //本次上传图片的拓展名
    private $_ext;
    //本次上传图片的原始文件名
    private $_rowName;

    //最大文件大小 2m
    const __MAX_FILE_SIZE__ = 1048576;

    /**
     * 获取上传token
     *
     * <AUTHOR>
     * @date   2017-04-21
     *
     */
    public function getUploadToken()
    {
        //实例化七牛存储对象
        $Qiniu  = new Qiniu;
        $config = $Qiniu->getConfig();
        $token  = $Qiniu->getUploadToken($config['images']['bucket']);

        $data = [
            'uptoken' => $token,
        ];

        exit(json_encode($data));
    }

    /**
     *  图片上传接口
     *
     * @return img url
     */
    public function upload()
    {
        //上传来源标识
        $params = I('extra');
        //额外参数
        parse_str($params, $params);

        //来源标识
        $identify = isset($params['identify']) ? $params['identify'] : '';

        if (!$identify) {
            $this->apiReturn(204, [], '未找到本次上传的标识');
        }
        //上传合法性检测
        $image = $this->_validCheck();

        $savePath = $this->_parseSavePath($identify);

        if ($savePath === false) {
            $this->apiReturn(204, [], '来源错误');
        }
        //实例化七牛存储对象
        $Qiniu       = new Qiniu;
        $qiniuConfig = $Qiniu->getConfig();
        $result      = $Qiniu->uploadFile($savePath);

        if (!$result) {
            $this->apiReturn(204, [], $Qiniu->getError());
        }
        //图片链接        
        $src = $qiniuConfig['images']['domain'] . $savePath;
        $this->apiReturn(200, ['src' => $src]);
    }

    /**
     * 上传合法性检测
     *
     * @return [type] [description]
     */
    private function _validCheck()
    {
        if (!$_FILES) {
            $this->apiReturn(204, [], '请选择要上传的图片');
        }

        //暂不支持多图上传
        $image = current($_FILES);

        if ($image['error'] != 0) {
            $this->apiReturn(204, [], '错误代码:' . $image['error']);
        }

        if ($image['size'] == 0) {
            $this->apiReturn(204, [], '文件数据为0');
        }

        if ($image['size'] > self::__MAX_FILE_SIZE__) {
            $this->apiReturn(204, [], '图片大小超出限制');
        }

        //类型检测
        $this->_ext = explode('/', $image['type'])[1];
        if (!in_array($this->_ext, $this->_allowType)) {
            $this->apiReturn(204, [], '非法图片类型');
        }
        //原始文件名
        $this->_rowName = $image['name'];

        return $image;
    }

    /**
     * 获取文件保存路径
     *
     * @param  string  $identify  来源标识
     * @param  int  $key  当前图片下标
     *
     * @return mixed
     */
    private function _parseSavePath($identify, $key = 0)
    {
        $pathRule = load_config($identify, 'image');

        if (!$pathRule) {
            return false;
        }

        $replaceRule = $this->_getRepalceRule();

        $find     = array_keys($replaceRule);
        $replace  = array_values($replaceRule);
        $realPath = str_replace($find, $replace, $pathRule);

        //存在不在预定义规则中的占位符
        if (stripos($realPath, '{') !== false) {
            return false;
        }

        //是否需要手动加上文件扩展名
        if (stripos($realPath, '.') === false) {
            $realPath .= $key . '.' . $this->_ext;
        }

        return $realPath;
    }

    /**
     * 获取路径替换规则
     *
     * @return [type] [description]
     */
    private function _getRepalceRule()
    {
        return [
            '{time}'     => time(),
            '{date}'     => date('Y-m-d'),
            '{account}'  => $this->_loginInfo['account'],
            '{mid}'      => $this->_loginInfo['memberID'],
            '{saccount}' => $this->_loginInfo['saccount'],
            '{sid}'      => $this->_loginInfo['sid'],
            '{rowName}'  => $this->_rowName,
        ];
    }

    /**
     * 前端图片上传插件需要的返回格式，类似jsonp
     *
     * @param  int  $code  结果代码
     * @param  array  $data  数据
     * @param  string  $msg  消息
     *
     * @return [type]       [description]
     */
    public function apiReturn($code, $data = [], $msg = '', $type = false)
    {
        $r = [
            'code' => $code,
            'data' => $data,
            'msg'  => $msg,
        ];

        if (!$type) {
            $callback_id = I('callback_id', 0, 'intval');

            $r = json_encode($r);
            echo '<script type="text/javascript">
                    var FileuploadCallbacks=window.parent.FileuploadCallbacks[' . $callback_id . '];
                    for(var i in FileuploadCallbacks) FileuploadCallbacks[i](' . $r . ');
                    </script>';
        } else {
            echo json_encode($r);
        }
        die;
    }

    /**
     * 获取图片资源列表
     *
     * @return [type] [description]
     */
    public function getImageList()
    {
        //上次列举返回的位置标记
        $marker = I('marker', '');
        //返回条数
        $limit = I('limit', 10, 'intval');
        //搜索前缀
        $prefix = I('prefix', '');

        $Qiniu  = new Qiniu;
        $config = $Qiniu->getConfig();
        $domain = $config['images']['domain'];

        $result = $Qiniu->getResourceList($prefix, $marker, $limit);

        $return = [];
        if ($result['err'] == null) {

            foreach ($result['items'] as $item) {
                $return['list'][] = [
                    //图片地址
                    'url'  => $domain . $item['key'],
                    //图片大小
                    'size' => $item['fsize'],
                    //创建时间
                    'time' => $item['putTime'],
                ];
            }

            $return['marker'] = $result['marker'];

            parent::apiReturn(200, $return);
        } else {
            parent::apiReturn(204, [], '资源获取出错');
        }
    }

    /**
     * 年卡身份证头像上传
     * <AUTHOR>
     * @date   2017-03-02
     *
     * @return array 上传成功后，返回图片地址URL
     */
    public function uploadIdCard()
    {
        // $this->isLogin('ajax');

        // $id       = I('id');        // 身份证号
        $data     = I('data');      // 图片数据流
        $identify = I('identify');  // 来源标识 年卡 = annualActive
        // 来源标识校验
        if (!$identify) {
            $this->apiReturn(204, [], '未找到本次上传的标识', true);
        }

        // 身份证校验
        // if (!\idcard_checksum18($id)){
        //     $this->apiReturn(204, [], '身份证号错误', true);
        // }

        // 校验图片数据
        if ($data == base64_encode(base64_decode($data))) {
            $data = 'data:image/bmp;base64,' . $data;
        } else {
            $this->apiReturn(204, [], '图片数据错误', true);
        }

        $this->_ext = 'jpg';
        $fileName   = $this->_parseSavePath($identify);
        if ($fileName === false) {
            $this->apiReturn(204, [], '来源错误', true);
        }

        // 七牛存储图片
        $Qiniu = new Qiniu;
        $src   = $Qiniu->base64Upload($fileName, $data);
        if (!$src) {
            $this->apiReturn(204, [], $Qiniu->getError(), true);
        }
        $this->apiReturn(200, ['src' => $src], '', true);
    }

    /**
     * 重新生成公众号的二维码链接
     * <AUTHOR>
     * @date   2018-04-21
     */
    public function wechatQrcodeFix()
    {
        $sid = $this->_loginInfo['sid'];

        $wechatBiz = new WechatTrusteeship();
        $result    = $wechatBiz->wechatQrcodeFix($sid);

        if (isset($result['code'])) {
            parent::apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            parent::apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 年卡身份证多张头像上传
     * <AUTHOR>
     * @date   2020-04-26
     *
     * @return array
     */
    public function uploadIdCards()
    {
        $data     = I('data');      // 图片数据流
        $identify = I('identify');  // 来源标识 年卡 = annualActive
        $srcArr   = [];
        $errorArr = [];
        // 来源标识校验
        if (!$identify) {
            $this->apiReturn(204, [], '未找到本次上传的标识', true);
        }

        if (!$data) {
            $this->apiReturn(204, [], '图片数据错误', true);
        }

        //验证是否有多张
        if (strpos($data, ',')) {
            $dataArr = explode(',', $data);
            foreach ($dataArr as $key => $item) {
                $res = $this->uploadIdCardAvatar($item, $identify, $key);
                if ($res['code'] === 200) {
                    $srcArr[] = $res['data'];
                } else {
                    $errorArr[] = $res['msg'];
                }
            }
        } else {
            $res = $this->uploadIdCardAvatar($data, $identify);
            if ($res['code'] === 200) {
                $srcArr[] = $res['data'];
            } else {
                $errorArr[] = $res['msg'];
            }
        }
        $this->apiReturn(200, ['srcArr' => $srcArr, 'error' => $errorArr], '', true);
    }

    /**
     * 头像上传
     * <AUTHOR>
     * @date   2020-04-26
     *
     * @return array
     */
    public function uploadIdCardAvatar($data, $identify, $key = 0)
    {
        if (!$data || !$identify) {
            return ['code' => 204, 'msg' => '参数错误'];
        }
        // 校验图片数据
        if ($data == base64_encode(base64_decode($data))) {
            $data = 'data:image/bmp;base64,' . $data;
        } else {
            return ['code' => 204, 'data' => '', 'msg' => '图片数据错误'];
        }

        $this->_ext = 'jpg';
        $fileName   = $this->_parseSavePath($identify, $key);
        if ($fileName === false) {
            return ['code' => 204, 'data' => '', 'msg' => '来源错误'];
        }

        // 七牛存储图片
        $Qiniu = new Qiniu;
        $src   = $Qiniu->base64Upload($fileName, $data);
        if (!$src) {
            return ['code' => 204, 'data' => '', 'msg' => $Qiniu->getError()];
        }

        return ['code' => 200, 'data' => $src];
    }
}