<?php

namespace Controller\Resource;

use Business\MemberLogin\MemberLoginHelper;
use Business\ResourceCenter\Forward;
use Library\Controller;

class Index extends Controller
{

    private $loginInfo;

    public function __construct()
    {
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 通用的转接方法
     * <AUTHOR>
     */
    public function index()
    {
        $params = I('param.');

        if (!isset($params['tag'])) {
            $this->apiReturn(400, [], '标识为空');
        }

        $memberId = $this->loginInfo['memberID'];
        $sid = $this->loginInfo['sid'];
        $sdtype = $this->loginInfo['sdtype'];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $sdtype = MemberLoginHelper::getLoginBusinessMember()->getSupplierDtype();
        }

        $biz = new Forward();
        $res = $biz->forward($memberId, $sid, $params, $sdtype);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 通用的管理员转接方法
     * <AUTHOR>
     */
    public function indexForSuper()
    {
        if (!$this->isSuper()) {
            $this->apiReturn(400, '', '无权限');
        }

        $params = I('param.');

        if (!isset($params['tag'])) {
            $this->apiReturn(400, [], '标识为空');
        }

        $biz = new Forward();
        $res = $biz->forward($this->loginInfo['memberID'], $this->loginInfo['sid'], $params, $this->loginInfo['sdtype']);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 通用的转接方法
     * <AUTHOR>
     */
    public function indexForJson()
    {
        $params['tag'] = I('param.tag');
        $data = I('param.data');
        if (!empty($data)){
            $data = json_decode(htmlspecialchars_decode($data),true);
            $params = array_merge($params,$data);
        }
        
        if (!isset($params['tag'])) {
            $this->apiReturn(400, [], '标识为空');
        }

        $biz = new Forward();
        $res = $biz->forward($this->loginInfo['memberID'], $this->loginInfo['sid'], $params, $this->loginInfo['sdtype']);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

}