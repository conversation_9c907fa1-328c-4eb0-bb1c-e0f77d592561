<?php
/**
 *纸票库存
 */

namespace Controller\Site;

use Business\Product\TerminalProduct;
use Library\Controller;
use Business\Site\PaperTicketManage as PaperTicketBiz;
use Library\SimpleExcel;
use Model\Member\Member;
use Model\Product\Land;
use Model\Product\PaperTicket;
use Model\Product\Ticket;
use Model\TerminalManage\PaperTicketStock;

class PaperTicketManage extends Controller
{
    private $sid;
    private $memberId;

    public function __construct()
    {
        $this->sid      = $this->isLogin();
        $loginInfo      = $this->getLoginInfo('ajax', false, false);
        $this->memberId = $loginInfo['memberID'];
    }

    /**
     * 入库
     * author  leafzl
     * Date: 2018-11-2
     */
    public function storagePaperTicket()
    {
        $memo = I('post.memo', '', 'strval');
        $data = I('post.data', []);
        $isExchange = I('post.is_exchange', 0, 'intval');

        $paperTicket = new PaperTicketBiz($this->sid, $this->memberId, 0, $isExchange);
        $res = $paperTicket->storagePaperTicket($memo, $data);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '入库成功');
        } else {
            $this->apiReturn(204, [], $res['msg']);
        }
    }

    /**
     * 领取
     * author  leafzl
     * Date: 2018-11-2
     */
    public function getPaperTicket()
    {
        $memo = I('post.memo', '', 'strval');
        $mid  = I('post.mid', 0, 'intval');
        $data = I('post.data', []);
        $isExchange = I('post.is_exchange', 0, 'intval');

        $paperTicket = new PaperTicketBiz($this->sid, $this->memberId, $mid, $isExchange);
        $res = $paperTicket->getPaperTicket($data, $memo);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '领取成功');
        } else {
            $this->apiReturn(204, [], $res['msg']);
        }
    }

    /**
     * 回收纸票
     * @Author: zhujb
     * 2019/6/17
     */
    public function recoveryPaperTicket()
    {
        $data = I('post.data', []);
        $memo = I('post.memo', '', 'strval');
        $isExchange = I('post.is_exchange', 0, 'intval');

        $paperTicket = new PaperTicketBiz($this->sid, $this->memberId, 0, $isExchange);
        $res         = $paperTicket->recoveryPaperTicket($data, $memo);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '回收成功');
        }
        $this->apiReturn(204, [], $res['msg']);
    }

    /**
     * 删除纸票
     * @Author: zhujb
     * 2020/1/8
     */
    public function delPaperTicket()
    {
        $type = I('post.type', 1, 'intval');
        $data = I('post.data', []);
        $memo = I('post.memo', '', 'strval');

        $paperModel = new PaperTicket();
        // 先获取总库存
        $totalStorage = $paperModel->getLastRecord($this->sid, 'repertory_num,repertory_entity,repertory_tid', 0);
        if (empty($totalStorage)) {
            $this->apiReturn(204, [], '没有库存信息');
        }

        $paperBiz = new \Business\Site\PaperTicketManage($this->sid, $this->memberId, $this->memberId, 0);

        if ($type == 1) {
            // 删除总库存
            $repertoryTidArr = json_decode($totalStorage['repertory_tid'], true);
            $repertoryEntityArr = json_decode($totalStorage['repertory_entity'], true);
            $repertoryEntityTidArr = array_keys($repertoryEntityArr);
            $repertoryNum = $totalStorage['repertory_num'];
            $tidArr = array_keys($repertoryTidArr);

            $saveData = [];
            foreach ($data as $item) {
                $item['num'] = abs($item['num']);
                if (in_array($item['tid'], $tidArr)) {
                    // 删除数量
                    $oldNum = $repertoryTidArr[$item['tid']];
                    $newNum = $oldNum - $item['num'];
                    $repertoryNum = $repertoryNum - $item['num'];
                    if ($repertoryNum < 0) {
                        $this->apiReturn(204, [], '删除数量不能大于总库存');
                    }
                    if ($newNum <= 0) {
                        unset($repertoryTidArr[$item['tid']]);
                    } else {
                        $repertoryTidArr[$item['tid']] = $newNum;
                    }
                }
                if (in_array($item['tid'], $repertoryEntityTidArr)) {
                    $tidEntityArr = $repertoryEntityArr[$item['tid']];
                    $entityPre = $paperBiz->_getPre($item['entity_begin_code']);
                    $tidEntity = $tidEntityArr[$entityPre];
                    $numArr = [];
                    foreach ($tidEntity as $tmp) {
                        $beginNum = $paperBiz->_getNum($tmp[0]);
                        $endNum = $paperBiz->_getNum($tmp[1]);
                        $numArr = array_merge($numArr, range($beginNum, $endNum));
                    }

                    $delBeginNum = $paperBiz->_getNum($item['entity_begin_code']);
                    $delEndNum = $paperBiz->_getNum($item['entity_end_code']);
                    $delNumArr = range($delBeginNum, $delEndNum);
                    $resNumArr = array_diff($numArr, $delNumArr);
                    if (!empty($resNumArr)) {
                        $res = [
                            $entityPre . reset($resNumArr),
                            $entityPre . end($resNumArr),
                        ];
                        $repertoryEntityArr[$item['tid']][$entityPre] = $res;
                    } else {
                        unset($repertoryEntityArr[$item['tid']][$entityPre]);
                    }

                    if (empty($repertoryEntityArr[$item['tid']])) {
                        unset($repertoryEntityArr[$item['tid']]);
                    }
                }

                $saveData[] = [
                    'tid' => $item['tid'],
                    'num' => -$item['num'],
                    'repertory_num' => $repertoryNum,
                    'repertory_tid' => json_encode($repertoryTidArr, JSON_UNESCAPED_UNICODE),
                    'repertory_entity' => json_encode($repertoryEntityArr, JSON_UNESCAPED_UNICODE),
                    'sid' => $this->sid,
                    'type' => 5, // 删除
                    'opt_id' => $this->memberId,
                    'is_exchange' => 0,
                    'create_time' => time(),
                    'memo' => $memo,
                ];
            }

            $res = $paperModel->storagePaperTicket($saveData);
            if ($res) {
                $this->apiReturn(200, [], '操作成功');
            }
            $this->apiReturn(204, [], '操作失败');
        } else {
            // 删除员工库存
            $paperTicktetStockModel = new PaperTicketStock();
            $saveData = [];
            foreach ($data as &$item) {
                $item['num'] = abs($item['num']);
                // 先获取总库存
                $totalStorage = $paperModel->getLastRecord($this->sid, 'repertory_num,repertory_entity,repertory_tid', 0);
                if (empty($totalStorage)) {
                    $this->apiReturn(204, [], '没有库存信息');
                }

                if (!empty($item['entity_begin_code']) && !empty($item['entity_end_code'])) {
                    $item['num'] = $paperBiz->_getNum($item['entity_end_code']) - $paperBiz->_getNum($item['entity_begin_code']) + 1;
                    $item['entity_code'] = json_encode([$item['entity_begin_code'], $item['entity_end_code']]);
                } else {
                    $item['entity_code'] = json_encode([]);
                }

                $item['mid'] = $item['staff_id'];

                $stockInfo = $paperTicktetStockModel->getPaperTicketStock($this->sid, $item['mid'], $item['tid'], 0);
                if ($stockInfo['num'] < $item['num']) {
                    $this->apiReturn(204, [], '删除数量不能大于该员工库存');
                }

                $saveData[] = [
                    'tid' => $item['tid'],
                    'mid' => $item['mid'],
                    'num' => -$item['num'],
                    'repertory_num' => $totalStorage['repertory_num'],
                    'repertory_tid' => $totalStorage['repertory_tid'],
                    'repertory_entity' => $totalStorage['repertory_entity'],
                    'sid' => $this->sid,
                    'type' => 5, // 删除
                    'opt_id' => $this->memberId,
                    'is_exchange' => 0,
                    'create_time' => time(),
                    'memo' => $memo,
                ];
            }

            $res = $paperBiz->handleStaffEntityData($data, 3);
            $res2 = $paperModel->storagePaperTicket($saveData);
            if ($res && $res2) {
                $this->apiReturn(200, [], '操作成功');
            }
            $this->apiReturn(204, [], '操作失败');
        }
    }

    /**
     * 搜索纸票记录
     * author  leafzl
     * Date: 2018-11-2
     */
    public function searchPaperTicket()
    {
        $end   = I('post.end', date('Y-m-d 23:59:59'));
        $begin = I('post.begin', date('Y-m-d', time() - 7 * 24 * 3600));
        $type  = I('post.type', '', 'strval');
        $mid   = I('post.mid', '', 'strval');
        $optId = I('post.opt_id', '', 'intval');
        $excel = I('post.excel', 0, 'intval');
        $page  = I('post.page', 1, 'intval');
        $limit = I('post.limit', 15, 'intval');
        $isExchange = I('post.is_exchange', 0, 'intval');
        $begin = strtotime($begin);
        $end   = strtotime($end) + 24 * 3600 - 1;
        if ($limit && $limit > 15) {
            $this->apiReturn(204, [], '查询条数过多');
        }
        $paperTicket = new PaperTicketBiz($this->sid, $optId, $mid, $isExchange);
        if ($excel == 1) {
            $res = $paperTicket->getPaperTicketData($type, $begin, $end);
            $this->exportExcelForPaperTicket($res['data']['list'], $begin, $end);
        }
        $res = $paperTicket->getPaperTicketData($type, $begin, $end, $page, $limit);

        if ($res['code'] == 200) {
            $this->apiReturn(200, $res['data'], '成功');
        } else {
            $this->apiReturn(204, [], $res['msg']);
        }
    }

    /**
     * 获取剩余库存
     * author  leafzl
     * Date: 2018-11-2
     */
    public function getResidueNum()
    {
        // $paperTicket = new PaperTicket();
        $isExchange  = I('post.is_exchange', 0, 'intval');
        //$res         = [];//$paperTicket->getLastRecord($this->sid, 'repertory_num', $isExchange);
        $paperTicket = new PaperTicketBiz($this->sid, 0, 0, $isExchange);
        $res = $paperTicket->getPaperTicketResidueList($this->sid);

        if ($res) {
            $this->apiReturn(200, $res);
        } else {
            $this->apiReturn(200, ['repertory_num' => 0]);
        }
    }

    /**
     * 汇总纸票
     * author  leafzl
     * Date: 2018-11-2
     */
    public function summaryPaperTicket()
    {
        $begin = I('post.begin', date('Y-m-01', strtotime(date("Y-m-d"))));
        $end   = I('post.end', date('Y-m-d', strtotime("$begin +1 month -1 day")));
        $mid   = I('post.mid', '', 'strval');
        $excel = I('post.excel', 0, 'intval');
        $page  = I('post.page', 1, 'intval');
        $limit = I('post.limit', 15, 'intval');
        $isExchange = I('post.is_exchange', 0, 'intval');

        $begin = strtotime($begin);
        $end   = strtotime($end) + 3600 * 24 - 1;
        if ($limit && $limit > 15) {
            $this->apiReturn(204, [], '查询条数过多');
        }
        $paperTicket = new PaperTicketBiz($this->sid, 0, $mid, $isExchange);
        if ($excel == 1) {
            // 获取导出数据
            $res = $paperTicket->summaryPaperTicket($begin, $end);
            $this->exportExcelForSummaryPaperTicket($res['data']['list'], $begin, $end);
        }
        $res = $paperTicket->summaryPaperTicket($begin, $end, $page, $limit);

        if ($res['code'] == 200) {
            $this->apiReturn(200, $res['data'], '成功');
        } else {
            $this->apiReturn(204, [], $res['msg']);
        }
    }

    /**
     * 获取员工领取纸票的情况
     * author  leafzl
     * Date: 2018-11-2
     */
    public function getStaffUsePaperTicket()
    {
        $begin = I('post.begin', date('Y-m-01', strtotime(date("Y-m-d"))));
        $end   = I('post.end', date('Y-m-d', strtotime("$begin +1 month -1 day")));
        $mid   = I('post.mid', '', 'intval');
        $page  = I('post.page', 1, 'intval');
        $limit = I('post.limit', 15, 'intval');
        $isExchange = I('post.is_exchange', 0, 'intval');

        if ($limit && $limit > 15) {
            $this->apiReturn(204, [], '查询条数过多');
        }

        $paperTicket = new PaperTicketBiz($this->sid, 0, $mid, $isExchange);
        $res         = $paperTicket->getStaffUsePaperTicket($begin, $end, $page, $limit);
        if ($res['code'] == 200) {
            $this->apiReturn(200, $res['data'], '成功');
        } else {
            $this->apiReturn(204, [], $res['msg']);
        }
    }

    /**
     * 导出成Excel文件
     * author  leafzl
     * @param $recodes 日志数据
     */
    public function exportExcelForPaperTicket($recodes, $begin, $end)
    {
        $begin    = date('Y-m-d', $begin);
        $end      = date('Y-m-d', $end);
        $now      = date('Y-m-d H:i:s');
        $filename = date('YmdHis') . '纸票入库领取记录表';
        $Excel    = [];
        $Excel[0] = ['纸票库存', '', "汇总时间：{$begin} ~ {$end}  打印时间： {$now}  "];
        $Excel[1] = [
            'date'          => '操作日期',
            'type'          => '操作类型',
            'num'           => '数量',
            'repertory_num' => '剩余数量',
            'opt'           => '操作员',
            'mid'           => '领用人',
            'memo'          => '备注'
        ];
        $line     = 2;

        $typeText = [1=> '入库', 2 => '领取', 3 => '回收', 4 => '使用'];

        if ($recodes) {
            foreach ($recodes as $row) {
                $line++;
                $Excel[$line]['date']          = $row['date'];
                $Excel[$line]['type']          = $typeText[$row['type']];
                $Excel[$line]['num']           = $row['num'];
                $Excel[$line]['repertory_num'] = $row['repertory_num'];
                $Excel[$line]['opt']           = $row['optName'];
                $Excel[$line]['mid']           = $row['midName'];
                $Excel[$line]['memo']          = urldecode($row['memo']);

            }
        }
        $xls = new SimpleExcel('UTF-8', true, '纸票库存');
        $xls->addArray($Excel);
        $xls->generateXML($filename);
        exit;
    }

    /**
     * 导出数据
     * author  leafzl
     * Date: 2018-11-7
     * @param $recodes
     * @param $begin
     * @param $end
     */
    public function exportExcelForSummaryPaperTicket($recodes, $begin, $end)
    {

        $begin    = date('Y-m-d', $begin);
        $end      = date('Y-m-d', $end);
        $now      = date('Y-m-d H:i:s');
        $filename = date('YmdHis') . '纸票汇总记录表';
        $Excel    = [];
        $Excel[0] = ['纸票汇总', '', "汇总时间：{$begin} ~ {$end}  打印时间： {$now}  "];
        $Excel[1] = [
            'name'    => '售票员名称',
            'total'   => '领用数量',
            'print'   => '售票打印',
            'reprint' => '重打印',

        ];
        $line     = 2;

        if ($recodes) {
            foreach ($recodes as $row) {
                $line++;
                $Excel[$line]['name']    = $row['dname'];
                $Excel[$line]['total']   = $row['total'];
                $Excel[$line]['print']   = $row['print'];
                $Excel[$line]['reprint'] = $row['reprint'];
            }
        }
        $xls = new SimpleExcel('UTF-8', true, '纸票汇总');
        $xls->addArray($Excel);
        $xls->generateXML($filename);
        exit;
    }


    /**
     * 所有员工
     * author  leafzl
     * Date:2018-11-2
     */
    public function getAllStaff()
    {
        $isHide      = I('post.hide', 1, 'intval');
        $memberModel = new \Model\Member\Member();
        $staffList   = $memberModel->getStaffByParentId($this->sid);
        $sonIdArr    = array_column($staffList, 'son_id');
        if ($isHide == 0) {
            array_unshift($sonIdArr, $this->sid);
        }
        $staffInfo = $memberModel->getMemberInfoByMulti($sonIdArr, 'id', 'id, dname,status');
        foreach ($staffInfo as $item) {
            if ($item['status'] == 0) {
                $staff[$item['id']] = $item['dname'];
            }
        }
        $this->apiReturn(200, $staff, 'success');
    }

    /**
     * 获取类型
     * author  leafzl
     * Date: 2018-11-2
     */
    public function getType()
    {

        $data = [
            '1' => '入库',
            '2' => '领取'
        ];
        $this->apiReturn(200, $data, 'success');

    }

    /**
     * 获取产品列表
     * @Author: zhujb
     * 2019/6/17
     */
    public function getLandList()
    {
        $keyword   = I('post.keyword', '', 'strval');

        $teminalBiz = new TerminalProduct();
        $landList = $teminalBiz->getProductListForCasual('N', $this->sid, 1, 100000, 7, 'cloud_ticket_system', '', 0, $keyword);
        if ($landList['code'] == 200) {
            $res = $landList['data']['list'];
            $data = [];
            foreach ($res as $item) {
                if (in_array($item['ptype'], ['A', 'B', 'F'])) {
                    $data[] = $item;
                }
            }
            $this->apiReturn(200, $data, '');
        } else {
            $this->apiReturn($landList['code'], [], $landList['msg']);
        }
    }

    /**
     * 获取票列表
     * @Author: zhujb
     * 2019/6/18
     */
    public function getTicketList()
    {
        $lid = I('post.lid', 0, 'intval');
        $aid = I('post.aid', 0, 'intval');

        if (empty($lid)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $terminalBiz = new TerminalProduct();
        $ticketList = $terminalBiz->getTicketListV1($lid, $aid, '', '', 7, 0);
        if ($ticketList['code'] !== 200) {
            $this->apiReturn($ticketList['code'], $ticketList['data'], $ticketList['msg']);
        }
        $this->apiReturn(200, $ticketList['data']['list'], '');
    }

    /**
     * 获取能回收的号段列表
     * @Author: zhujb
     * 2019/6/19
     */
    public function getRecoveryList()
    {
        $isExchange = I('post.is_exchange', 0, 'intval');
        $paperTicketBiz = new PaperTicketBiz($this->sid, 0, 0, $isExchange);

        $paperTicketStockModel = new PaperTicketStock();
        $allEntity = $paperTicketStockModel->getAllPaperTicketStock($this->sid, $isExchange);

        // 门票信息
        $tidArr = array_column($allEntity, 'tid');
        $ticketData = $paperTicketBiz->getTicketTitleByTidArr($tidArr);

        // 员工信息
        $memberModel = new Member();
        $midArr = array_column($allEntity, 'mid');
        $memberArr = $memberModel->getMemberListByIds($midArr, 'id,dname');
        $memberData = [];
        foreach ($memberArr as $member) {
            $memberData[$member['id']] = $member['dname'];
        }

        $res = [];
        foreach ($allEntity as $item) {
            $ltitle = isset($ticketData[$item['tid']]['ltitle']) ? $ticketData[$item['tid']]['ltitle'] : '通用产品';
            $ttitle = isset($ticketData[$item['tid']]['ttitle']) ? $ticketData[$item['tid']]['ttitle'] : '通用票';

            $res[] = [
                'mid' => $item['mid'],
                'username' => $memberData[$item['mid']],
                'title' => $ltitle . '-' . $ttitle,
                'tid' => $item['tid'],
                'tnum' => $item['num'],
                'tid_num' => $item['num'],
                'entity_num' => $item['num'],
                'entity_code' => !empty($item['entity_data']) ? json_decode($item['entity_data']) : [],
            ];
        }

        $this->apiReturn(200, $res, '');
    }
}
