<?php
/***
 * 提供帮助中心中的相关对外接口地址
 * <AUTHOR>
 * @date   2019-02-13
 */

namespace Controller\Site;

use Controller\Tpl\userInfo;
use Library\Controller;
use Model\Site\HelpCenter as helpCenterModel;
use Business\Site\HelpCenter as helpCenterBz;

/**
 * 帮助中心
 * <AUTHOR> Li
 * @date   2019-02-13
 */
class HelpCenter extends Controller
{
    private $helpCenterBz = null;
    private $param        = null;
    private $memberId     = null;

    public function __construct()
    {
        $this->helpCenterBz = new helpCenterBz();
        $loginInfo          = $this->getLoginInfo();
        $this->memberId     = $loginInfo['memberID'];
        $operator_id        = $loginInfo['sid'];
        $this->param        = I('');
        if (!empty($operator_id)) {
            $this->param['operator_id'] = $operator_id;
        }
        $this->param['sdtype'] = $loginInfo['sdtype'];
    }

    /***
     * 获取帮助中心渠道列表
     * 说明:获取帮助中心渠道列表
     *
     * User: xujinyao
     * Date: 2019-02-18
     *
     */
    public function getShopList()
    {

        $result = $this->helpCenterBz->getShopList($this->param);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "获取问题目录列表成功");
    }

    /**
     * 获取问题目录列表
     * <AUTHOR> Li
     * @date   2019-02-13
     *
     * @return array
     */
    public function getProblemDirectoryList()
    {
        $result = $this->helpCenterBz->getProblemDirectoryList($this->param);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "获取问题目录列表成功");
    }

    /**
     * 获取问题目录树
     * <AUTHOR> Li
     * @date   2019-02-13
     *
     * @return array
     */
    public function getProblemDirTree()
    {
        $result = $this->helpCenterBz->getProblemDirTree($this->param);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "获取问题目录树列表成功");
    }

    /**
     * 获取右侧帮助栏菜单列表
     * <AUTHOR> Li
     * @date   2019-02-14
     *
     * @return array
     */
    public function getMenuListForRightHelp()
    {
        $result = $this->helpCenterBz->getMenuListForRightHelp($this->param);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "获取右侧帮助栏菜单列表成功");
    }

    /**
     * 获取右侧帮助栏问题列表
     * <AUTHOR> Li
     * @date   2019-02-14
     *
     * @return array
     */
    public function getRightProblemList()
    {
        $result = $this->helpCenterBz->getRightProblemList($this->param, $this->param['sdtype']);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "获取右侧帮助栏问题列表成功");
    }

    /***
     * 获取问题详情
     * 说明:获取问题详情
     *
     * User: xujinyao
     * Date: 2019-02-15
     */
    public function getProblemInfo()
    {

        $result = $this->helpCenterBz->getProblemInfo($this->param);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "获取问题详情成功");
    }

    /***
     * 问题的分类选择
     *
     * User: xujinyao
     * Date: 2019-02-15
     */
    public function chooseProblemDir()
    {

        $result = $this->helpCenterBz->changeProblem($this->param);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "操作成功");
    }

    /***
     * 问题关联右侧帮助栏
     *
     * User: xujinyao
     * Date: 2019-02-15
     */
    public function relateProblem()
    {
        $result = $this->helpCenterBz->changeProblem($this->param);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "操作成功");
    }

    /***
     * 获取问题列表
     * 说明:获取问题列表并分页
     *
     * User: xujinyao
     * Date: 2019-02-18
     *
     */
    public function getProblemList()
    {
        $result = $this->helpCenterBz->getProblemList($this->param, $this->param['sdtype']);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "获取问题列表成功");
    }

    /***
     * 获取前台问题列表(分页)
     * 说明:获取前台问题列表
     *
     * User: xujinyao
     * Date: 2019-02-18
     *
     * @return array
     */
    public function getProblemListForFront()
    {
        $result = $this->helpCenterBz->getProblemListForFront($this->param, $this->param['sdtype']);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "获取前台问题列表成功");
    }

    /***
     * 获取前台问题搜索的热搜列表
     * 说明:获取前台问题搜索的热搜列表
     *
     * User: xujinyao
     * Date: 2019-02-18
     *
     * @return array
     */
    public function getHotProblemSearchList()
    {

        $result = $this->helpCenterBz->getHotProblemSearchList();

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "获取问题列表成功");
    }

    /***
     * 反馈问题是否有用
     * 说明:用于反馈所浏览的问题答案是否有用
     *
     * User: xujinyao
     * Date: 2019-02-18
     *
     */
    public function feedBackForProblem()
    {

        $result = $this->helpCenterBz->feedBackForProblem($this->param);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /***
     * 获取问题详情（前台）
     * 说明:获取问题详情并更新浏览量（前台）
     *
     * User: xujinyao
     * Date: 2019-02-18
     *
     */
    public function getVisitProblemInfo()
    {

        $result = $this->helpCenterBz->getVisitProblemInfo($this->param);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "获取问题详情成功");
    }

    /***
     *  获取热搜列表
     * 说明:获取热搜列表
     *
     * User: xujinyao
     * Date: 2019-02-19
     *
     */
    public function getHotSearchList()
    {
        $result = $this->helpCenterBz->getHotSearchList($this->param, $this->param['sdtype']);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], "获取获取热搜列表成功");
    }

    /***
     * 获取一级目录列表
     *
     * <AUTHOR> Li
     * @date: 2020-04-08
     *
     * @param  int  $id  一级目录ID
     * @param  string  $title  一级目录名称
     * @param  string  $field  查询字段
     *
     * @return array
     */
    public function getDirectoryParentsList()
    {
        $result = $this->helpCenterBz->getDirectoryParentsList();

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

}