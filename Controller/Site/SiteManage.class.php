<?php

namespace Controller\Site;

use Business\Ota\MtSite;
use Library\Controller;

use Business\Site\SiteManage as SiteSupervise;
use Model\Ota\SiteSuperviseMtRelation;

/**
 * 系统设置，站点管理类
 * <AUTHOR>
 * @date   2018-06-7
 */
class SiteManage extends Controller
{
    private $sid;

    private $loginInfo;

    /**
     * 判断是否已有默认主站点
     * <AUTHOR>
     * @date   2018-06-7
     *
     * @param  $sid  登录供应商id
     */
    public function __construct()
    {
        $this->sid       = $this->isLogin();
        $this->loginInfo = $this->getLoginInfo();
    }

    /**
     * 获取登录用户站点列表
     * <AUTHOR>
     * @date   2018-06-7
     *
     * @param  $sid  登录供应商id
     * @param  $sname 站点名称
     * @param  $page  当前页
     * @param  $size  每页条数
     */
    public function getSiteManageList()
    {
        $page  = I('page', 1, 'intval');
        $size  = I('size', 15, 'intval');
        $sname = I('sname');
        $sid   = $this->sid;

        if (!$sid || !$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($size > 15) {
            $this->apiReturn(204, [], '每页条数超出限制');
        }

        $siteManage = new SiteSupervise();

        //获取站点列表信息
        if ($sname) {
            $result = $siteManage->getStieBySname($sid, $sname, $page, $size);
        } else {
            $result = $siteManage->getSiteManageList($sid, $page, $size);
        }
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 编辑站点获取单个站点数据
     * <AUTHOR>
     * @date   2018-06-7
     *
     * @param  $sid     登录供应商id
     * @param  $siteId  站点id
     */
    public function getSiteListById()
    {
        $siteId = I('siteId', '', 'intval');
        $sid    = $this->sid;

        if (!$sid || !$siteId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $siteManage = new SiteSupervise();

        //获取站点详情
        $result = $siteManage->getSiteListById($sid, $siteId);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 新增站点
     * <AUTHOR>
     * @date   2018-06-7
     *
     * @param  $sid  登录供应商id
     * @param  array  $data
     *
     */
    public function createSite()
    {
        $sid  = $this->sid;
        $data = I('');

        if (!$sid) {
            $this->apiReturn(204, [], '参数错误，请先登录');
        }

        //0不限制分销商 1限制分销商
        $limitPartnerStatus    = isset($data['limit_partner_status']) ? $data['limit_partner_status'] : '';
        //分销商id逗号隔开
        $limitPartnerIds       = isset($data['limit_partner_ids']) ? $data['limit_partner_ids'] : '';
        //分销商分组id逗号隔开
        $limitPartnerGroupIds  = isset($data['limit_partner_group_ids']) ? $data['limit_partner_group_ids'] : '';
        if (!in_array($limitPartnerStatus, ['0','1'], true)) {
            $this->apiReturn(204, [], '请选择是否限制分销商');
        }

        if ($limitPartnerStatus == 1) {

            //处理传过来的分销商id
            $limitPartnerIdArray  = explode(',',$limitPartnerIds);
            foreach ($limitPartnerIdArray as $k => $partnerId) {
                if (!preg_match("/^[1-9]{1}[0-9]{0,9}$/", $partnerId)) {
                    unset($limitPartnerIdArray[$k]);
                }
            }
            $limitPartnerIdArray = array_values(array_unique($limitPartnerIdArray));

            //处理传过来的分销商分组id
            $limitPartnerGroupIdArray    = explode(',',$limitPartnerGroupIds);
            foreach ($limitPartnerGroupIdArray as $k => $partnerGroupId) {
                if (!preg_match("/^[1-9]{1}[0-9]{0,9}$/", $partnerGroupId)) {
                    unset($limitPartnerGroupIdArray[$k]);
                }
            }
            $limitPartnerGroupIdArray = array_values(array_unique($limitPartnerGroupIdArray));

            //必须有一个限制
            if (empty($limitPartnerIdArray) && empty($limitPartnerGroupIdArray)) {
                $this->apiReturn(204, [], '请选择要限制的分销商');
            }

            $data['limit_partner_ids']       = implode(',', $limitPartnerIdArray);
            $data['limit_partner_group_ids'] = implode(',', $limitPartnerGroupIdArray);

            if(strlen($data['limit_partner_ids']) > 1000 || strlen($data['limit_partner_group_ids']) > 1000) {
                $this->apiReturn(204, [], '添加分销商或分组过多');
            }

        } else {
            $data['limit_partner_ids'] = '';
            $data['limit_partner_group_ids'] = '';
        }

        $siteManage = new SiteSupervise();
        $result     = $siteManage->createSite($sid, $data);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 编辑站点
     * <AUTHOR>
     * @date   2018-06-7
     *
     * @param  $sid  登录供应商id
     * @param  $siteId  站点id
     * @param  array  $data
     *
     */
    public function editSite()
    {
        $sid    = $this->sid;
        $data   = I('');
        $siteId = $data['siteId'];

        if (!$sid || !$siteId) {
            $this->apiReturn(204, [], '参数错误，请先登录');
        }

        //0不限制分销商 1限制分销商
        $limitPartnerStatus    = isset($data['limit_partner_status']) ? $data['limit_partner_status'] : '';
        //分销商id逗号隔开
        $limitPartnerIds       = isset($data['limit_partner_ids']) ? $data['limit_partner_ids'] : '';
        //分销商分组id逗号隔开
        $limitPartnerGroupIds  = isset($data['limit_partner_group_ids']) ? $data['limit_partner_group_ids'] : '';

        if (!in_array($limitPartnerStatus, ['0','1'], true)) {
            $this->apiReturn(204, [], '请选择是否限制分销商');
        }

        if ($limitPartnerStatus == 1) {

            //处理传过来的分销商id
            $limitPartnerIdArray  = explode(',',$limitPartnerIds);
            foreach ($limitPartnerIdArray as $k => $partnerId) {
                if (!preg_match("/^[1-9]{1}[0-9]{0,9}$/", $partnerId)) {
                    unset($limitPartnerIdArray[$k]);
                }
            }
            $limitPartnerIdArray = array_values(array_unique($limitPartnerIdArray));

            //处理传过来的分销商分组id
            $limitPartnerGroupIdArray    = explode(',',$limitPartnerGroupIds);
            foreach ($limitPartnerGroupIdArray as $k => $partnerGroupId) {
                if (!preg_match("/^[1-9]{1}[0-9]{0,9}$/", $partnerGroupId)) {
                    unset($limitPartnerGroupIdArray[$k]);
                }
            }
            $limitPartnerGroupIdArray = array_values(array_unique($limitPartnerGroupIdArray));

            //必须有一个限制
            if (empty($limitPartnerIdArray) && empty($limitPartnerGroupIdArray)) {
                $this->apiReturn(204, [], '请选择要限制的分销商');
            }

            $data['limit_partner_ids']       = implode(',', $limitPartnerIdArray);
            $data['limit_partner_group_ids'] = implode(',', $limitPartnerGroupIdArray);

            if(strlen($data['limit_partner_ids']) > 1000 || strlen($data['limit_partner_group_ids']) > 1000) {
                $this->apiReturn(204, [], '添加分销商或分组过多');
            }

        } else {
            $data['limit_partner_ids'] = '';
            $data['limit_partner_group_ids'] = '';
        }

        $siteManage = new SiteSupervise();
        $result     = $siteManage->editSiteInfo($sid, $data, $siteId);
        if (isset($result['code'])) {
            if ($result['code'] == 200) {
                pft_log('site_edit',json_encode(['ip'=>get_client_ip(),'fid'=>$this->loginInfo['memberID'],'data'=>$_POST],JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));
            }
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取产品（可售，可鉴，可取）
     * author  leafzl
     * Date:2018-6-8
     */

    public function getProduct()
    {
        $keyword = I('keyword');
        $seaType = I('type', '', 'intval');
        $size    = I('size', 50);

        //产品类型
        $productType    = I('product_type', '', 'strval');

        $siteManage = new SiteSupervise();

        // 可检产品配置不允许展示转分销的
        if ($seaType == 1) {
            $seaType = false;
        } else {
            $seaType = true;
        }
        $res = $siteManage->getProduct($this->sid, $keyword, $size, $seaType, $productType);

        if ($res['code'] == 200) {
            $this->apiReturn(200, $res['data']);
        } else {
            $this->apiReturn(204, [], $res['msg']);
        }

    }

    /**
     * 获取员工信息
     * author  leafzl
     * Date: 2018-6-8
     */

    public function getStaff()
    {
        $includeSid = I('post.include_sid', 0, 'intval'); //是否包含商家自己

        $siteManage = new SiteSupervise();
        $res        = $siteManage->getStaff($this->sid, $includeSid);

        if ($res['code'] == 200) {
            foreach ($res['data'] as $key => $item) {
                if ($item['status'] == 2) {
                    unset($res['data'][$key]);
                }
            }
            $res['data'] = array_values($res['data']);
            $this->apiReturn(200, $res['data']);
        } else {
            $this->apiReturn(204, [], $res['msg']);
        }

    }

    /**
     * 停用、启用、删除  站点
     * <AUTHOR>
     * @date   2018-06-7
     *
     * @param  $siteId  站点id
     * @param  $sid    登录供应商id
     * @param  $type    更改状态
     *
     */
    public function editSiteType()
    {
        $sid    = $this->sid;
        $siteId = I('siteId', '', 'intval');
        $type   = I('type', '', 'intval');

        if (!$sid || !$siteId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $siteManage = new SiteSupervise();
        $result     = $siteManage->editSiteType($sid, $siteId, $type);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 站点名称模糊搜索
     * <AUTHOR>
     * @date   2018-06-7
     *
     * @param  $sname   站点名称
     * @param  $sid     登录供应商id
     *
     */
    public function getStieBySname()
    {
        $sid   = $this->sid;
        $sname = I('sname');
        $page  = I('page', 1, 'intval');
        $size  = I('size', 15, 'intval');

        if (!$sid || !$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }
        $siteManage = new SiteSupervise();
        $result     = $siteManage->getStieBySname($sid, $sname, $page, $size);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }


    /***************************************************美团渠道站点*****************************************************/

    /**
     * 获取美团渠道体系列表
     * <AUTHOR>
     * @date 2020/1/7 0007
     *
     * @return array
     */
    public function getMtChannelList()
    {
        $list = load_config("mt_channel_enum", "third");
        $res  = [];
        foreach ($list as $key => $value) {
            $res[] = [
                'value' => $key,
                'label' => $value,
            ];
        }
        $this->apiReturn(200, ['options' => $res]);
    }

    /**
     * 获取美团渠道站点开放账号供应商ID
     * <AUTHOR>
     * @date 2020/1/8 0008
     *
     * @return array
     */
    public function getAccountByMt()
    {
        $list = load_config("mt_channel_account", "third");
        $this->apiReturn(200, ['sids' => $list]);
    }

    /**
     * 新增美团渠道站点
     * <AUTHOR>
     * @date 2020/1/7 0007
     *
     * @return array
     */
    public function createSiteByMt()
    {
        $sid             = $this->sid;
        $data            = I('');
        $mtChannelDetail = I('post.mt_channel_detail', '', 'string');
        $data['status']  = 0;

        if (!$sid) {
            $this->apiReturn(204, [], '参数错误，请先登录');
        }

        if (!$mtChannelDetail) {
            $this->apiReturn(204, [], '请填写设备号!');
        }

        $siteMtRelationModel = new SiteSuperviseMtRelation();
        $is                  = $siteMtRelationModel->getIsBySidAndDetail($sid, $mtChannelDetail);

        if ($is) {
            $this->apiReturn(204, [], '设备号重复!');
        }

        $siteManage = new SiteSupervise();
        $result     = $siteManage->createSiteByMt($sid, $data);

        if (!isset($result['code'])) {
            $this->apiReturn(500, [], '接口出错');
        }

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $params  = [
            'sid'               => intval($sid),
            'site_id'           => intval($result['data']),
            'mt_channel_detail' => strval($mtChannelDetail),
        ];
        $resSite = $siteMtRelationModel->addParams($params);
        $log     = [
            'key'             => 'createSiteByMt',
            'sid'             => $sid,
            'data'            => $data,
            'mtChannelDetail' => $mtChannelDetail,
            'mtSiteRes'       => $resSite,
            'siteRes'         => $result,
            'loginInfo'       => $this->loginInfo,
        ];
        pft_log('mt_site_channel', json_encode($log, JSON_UNESCAPED_UNICODE));
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 编辑美团渠道站点
     * <AUTHOR>
     * @date 2020/1/7 0007
     *
     * @return array
     */
    public function editSiteByMt()
    {
        $sid             = $this->sid;
        $data            = I('');
        $siteId          = $data['siteId'];
        $mtChannelDetail = I('post.mt_channel_detail', '', 'string');
        $data['status']  = 0;

        if (!$sid || !$siteId) {
            $this->apiReturn(204, [], '参数错误，请先登录');
        }

        if (!$mtChannelDetail) {
            $this->apiReturn(204, [], '请填写设备号!');
        }

        $siteMtRelationModel = new SiteSuperviseMtRelation();
        $siteManage          = new SiteSupervise();

        $is = $siteMtRelationModel->getIsBySidAndDetail($sid, $mtChannelDetail);
        if ($is && $is['site_id'] != $siteId) {
            $this->apiReturn(204, [], '设备号重复!');
        }

        $resMod = $siteMtRelationModel->getSiteMtInfoBySiteId($siteId);
        if (!$resMod) {
            $this->apiReturn(204, [], '站点ID不存在！!');
        }

        $result = $siteManage->editSiteInfo($sid, $data, $siteId);
        if (!isset($result['code'])) {
            $this->apiReturn(500, [], '接口出错');
        }
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $params  = [
            'mt_channel_detail' => strval($mtChannelDetail),
        ];
        $resSite = $siteMtRelationModel->modParams($resMod['id'], $params);

        $log = [
            'key'             => 'editSiteByMt',
            'sid'             => $sid,
            'data'            => $data,
            'mtChannelDetail' => $mtChannelDetail,
            'siteId'          => $siteId,
            'mtSiteRes'       => $resSite,
            'siteRes'         => $result,
            'loginInfo'       => $this->loginInfo,
        ];
        pft_log('mt_site_channel', json_encode($log, JSON_UNESCAPED_UNICODE));
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 编辑美团渠道站点获取单个站点数据
     * <AUTHOR>
     * @date 2020/1/7 0007
     *
     * @return array
     */
    public function getSiteListByIdByMt()
    {
        $siteId = I('siteId', '', 'intval');
        $sid    = $this->sid;

        if (!$sid || !$siteId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $siteManage = new SiteSupervise();

        //获取站点详情
        $result = $siteManage->getSiteListById($sid, $siteId);

        if (!isset($result['code'])) {
            $this->apiReturn(500, [], '接口出错');
        }

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $siteMtRelationModel                 = new SiteSuperviseMtRelation();
        $resMt                               = $siteMtRelationModel->getSiteMtInfoBySiteId($siteId);
        $result['data']['mt_channel_detail'] = '';
        if ($resMt) {
            $result['data']['mt_channel_detail'] = $resMt['mt_channel_detail'];
        }
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取登录用户站点列表-美团渠道站点
     * <AUTHOR>
     * @date 2020/1/7 0007
     *
     * @param  $sid  登录供应商id
     * @param  $sname 站点名称
     * @param  $page  当前页
     * @param  $size  每页条数
     */
    public function getSiteManageListByMt()
    {
        $page  = I('page', 1, 'intval');
        $size  = I('size', 15, 'intval');
        $sname = I('sname');
        $sid   = $this->sid;

        if (!$sid || !$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($size > 15) {
            $this->apiReturn(204, [], '每页条数超出限制');
        }

        $siteManage = new SiteSupervise();

        //获取站点列表信息
        if ($sname) {
            $result = $siteManage->getStieBySname($sid, $sname, $page, $size);
        } else {
            $result = $siteManage->getSiteManageList($sid, $page, $size);
        }
        if (!isset($result['code'])) {
            $this->apiReturn(500, [], '接口出错');
        }

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        if (empty($result['data'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $ids = array_column($result['data']['data'], 'id');

        $mtSiteBus = new MtSite();
        $dataMap   = $mtSiteBus->getSiteMtpByIdsMap($ids);

        if ($dataMap['code'] != 200) {
            foreach ($result['data']['data'] as &$value) {
                $value['is_mt'] = 0;
            }
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        //获取成功
        foreach ($result['data']['data'] as &$value) {
            $value['is_mt'] = isset($dataMap['data'][$value['id']]) ? 1 : 0;
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除 美团站点
     * <AUTHOR>
     * @date 2020/1/9 0009
     *
     * @return array
     */
    public function editSiteTypeByMt()
    {
        $sid    = $this->sid;
        $siteId = I('siteId', '', 'intval');
        $type   = I('type', '', 'intval');

        if (!$sid || !$siteId) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (!in_array($type, [2])) {
            $this->apiReturn(204, [], '参数错误!');
        }

        $siteManage = new SiteSupervise();
        $result     = $siteManage->editSiteType($sid, $siteId, $type);

        if (!isset($result['code'])) {
            $this->apiReturn(500, [], '接口出错');
        }

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $siteMtRelationModel = new SiteSuperviseMtRelation();

        if ($type == 2) {
            $deleteRes = $siteMtRelationModel->deleteDataBySiteId($siteId);
        }
        $log = [
            'key'       => 'editSiteTypeByMt',
            'sid'       => $sid,
            'siteId'    => $siteId,
            'type'      => $type,
            'mtSiteRes' => $deleteRes,
            'siteRes'   => $result,
            'loginInfo' => $this->loginInfo,
        ];
        pft_log('mt_site_channel', json_encode($log, JSON_UNESCAPED_UNICODE));
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 判断下站点相关服务
     * User: linchen
     * Date: 2021/5/24
     * @return array
     */
    public function checkSiteInfo()
    {
        $result = (new SiteSupervise())->checkSiteInfoService($this->sid);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }
}
