<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/6/21
 * Time: 18:09
 */
namespace Controller\UnionCloudFlash;

use Business\UnionCloud\UnionCloudMember as UnionMember;

class UnionCloudMember extends UnionCloud {
    /**
     * 银联用户登陆
     * <AUTHOR>
     * @date 2019-06-28
     */
    public function unionLogin(){
        $isLogin   = $this->isLogin('ajax', false);
        if (!$isLogin) {
            $info = $this->getUnionUserInfo();
            if (ENV == 'DEVELOP' || ENV == 'LOCAL'){
                $info = [
                    'phone' => ***********,
                    'openid'=> 'union1'
                ];
            }

            if (!$info['phone'] || !$info['openid']){
                $this->apiReturn(204,0,'授权参数获取失败，请重新授权');
            }

            $unionMemberBiz = new UnionMember();
            $result         = $unionMemberBiz->unionMemberCreate($info['phone'],$info['openid']);
            if (isset($result['code'])){
                $this->apiReturn($result['code'],$result['data'],$result['msg']);
            }else{
                $this->apiReturn(500,[],'系统异常');
            }
        }else{
            $loginInfo = $this->getLoginInfo('ajax', false, false);
            $memberId  = $loginInfo['memberID'];

            $this->apiReturn(200,$memberId,'登录成功');
        }
    }

    /**
     * 获取银联用户基本信息
     * <AUTHOR>
     * @date 2019-06-28
     */
    public function getUnionMemberInfo(){
        $memberId   = $this->isLogin('ajax');
        $memberBiz  = new \Business\Member\Member();
        $memberInfo = $memberBiz->getInfo($memberId);
        $this->apiReturn(200,$memberInfo);
    }
}