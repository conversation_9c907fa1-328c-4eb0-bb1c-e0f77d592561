<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/6/19
 * Time: 15:20
 */
namespace Controller\UnionCloudFlash;

use Library\Business\RedisGeo;
use Library\Business\WechatSmallApp;
use Library\CloudFlash\Core;
use Model\Member\MemberRelationship;
use Model\Product\Area;
use Model\Subdomain\SubdomainInfo;

class UnionCloudBase extends UnionCloud{
    /**
     * 获取前端sdk使用的token
     * <AUTHOR>
     * @date 2019-06-28
     */
    public function getFrontToken(){
        $url = I('url','');
        if (!$url){
            if (ENV == 'PRODUCTION'){
                $url = 'https://'.$_SERVER['HTTP_HOST'].$_SERVER['PHP_SELF'].'?'.$_SERVER['QUERY_STRING'];
            }else{
                $url = 'http://'.$_SERVER['HTTP_HOST'].$_SERVER['PHP_SELF'].'?'.$_SERVER['QUERY_STRING'];
            }
        }
        $url = urldecode($url);
        $cloudLib = new Core();
        if (ENV == 'PRODUCTION'){
            $result = $cloudLib->getUnionJs($url);
        }else{
            $result = '';
        }
        $this->apiReturn(200,$result);
    }
    /**
     * 银联授权登陆
     * <AUTHOR>
     * @date 2019-06-28
     */
    public function unionUserAuthorize(){
        $code = I('code', '');
        if (I('errmsg','')){
            exit(I('errmsg',''));
        }
        if ($code) {
            try {
                $state = I('state','');
                if ($state){
                    $params = json_decode(base64_decode($state), true);
                }
                $authBiz   = new \Business\UnionCloud\UnionCloudBase();
                if ($params['mode'] == 1){
                    $auth      = $authBiz->unionParseAuthInfo($code);
                    if (empty($auth['data'])){
                        $extra = ['mode' => 2];
                        if (isset($params['lid']) && $params['lid'] > 0){
                            $extra['lid'] = $params['lid'];
                        }
                        if (isset($params['account']) && $params['account']){
                            $extra['account'] = $params['account'];
                        }
                        if (isset($params['ordernum']) && $params['ordernum']){
                            $extra['ordernum'] = $params['ordernum'];
                        }
                        $this->unionAuth('UnionCloudFlash_UnionCloudBase', 'unionUserAuthorize', 'error', $extra, 'upapi_mobile');
                    }
                }elseif ($params['mode'] == 2){
                    $auth      = $authBiz->getUnionPhoneByCode($code);
                }else{
                    throw new \Exception('授权模式错误');
                }
                if ($auth['code'] != 200) {
                    throw new \Exception($auth['msg']);
                }
                $_SESSION['union_phone'] = $auth['data']['phone'];
                $_SESSION['union_openid'] = $auth['data']['openId'];
                $userAccount = isset($params['account']) ? $params['account'] : '';
                $skipLid     = isset($params['lid']) ? $params['lid'] : 0;
                $orderId     = isset($params['ordernum']) ? $params['ordernum'] : '';
                if ($skipLid > 0 && !$userAccount){
                    $this->_redirectToPage('detail',$skipLid);
                }elseif ($skipLid > 0 && $userAccount){
                    $this->_redirectToPage('user_detail',$skipLid,$userAccount);
                }elseif ($orderId && $userAccount){
                    $this->_redirectToPage('order_detail',$skipLid,$userAccount,$orderId);
                }else{
                    $this->_redirectToPage('index');
                }

            } catch (\Exception $e) {
                pft_log('unioncloud/error',$e->getMessage());
                //跳到错误提示页面
                $this->_redirectToPage( 'error');
            }
        } else {
            $lid     = I('lid',0,'intval');    //现有规则  lid 单个跳转闪付游景区
            $account = I('account','');        // lid + account 跳转数字旅游指定供应商景区
            $extra = ['mode' => 1];
            $orderNum = I('ordernum','');      //  account +ordernum 跳转数字旅游指定订单详情
            if ($lid > 0){
                $extra['lid'] = $lid;
            }
            if ($orderNum){
                $extra['ordernum'] = $orderNum;
            }
            if ($account > 0){
                $extra['account'] = $account;
                //这边代表是数字景区那边的合作，判断下是不是存在session，存在就直接重定向走
                if ($this->_isGetOpenId()){
                    if ($lid){
                        $this->_redirectToPage('user_detail',$lid,$account);
                    }elseif ($orderNum){
                        $this->_redirectToPage('order_detail',$lid,$account,$orderNum);
                    }
                }
            }
            $this->unionAuth('UnionCloudFlash_UnionCloudBase', 'unionUserAuthorize', 'error', $extra, 'upapi_base');
        }
    }
    /**
     * 授权跳转
     * <AUTHOR>
     * @date 2019-06-28
     */
    private function unionAuth($callC,$callA,$exceptionPage,$extra = [],$type = 'upapi_base'){
        try {
            //回调时候要用的参数
            $params = [
            ];
            if ($extra) {
                $params = array_merge($params, $extra);
            }
            //回调地址

            $callback = MY_DOMAIN . "r/{$callC}/{$callA}";

            //发起授权
            $authBiz = new \Business\UnionCloud\UnionCloudBase();
            $authBiz->unionRequestForAuth($callback, $params, $type);

        } catch (\Exception $e) {
            if ($exceptionPage) {
                //跳转到指定页面
                $this->_redirectToPage($exceptionPage);
            }
        }
    }
    /**
     * 授权跳转页面
     * <AUTHOR>
     * @date 2019-06-28
     * @param string $exceptionPage 跳转的标识
     * @param int $lid 景区id
     * @param string $account 账号
     */
    private function _redirectToPage($exceptionPage,$lid = 0,$account = '',$ordernum = ''){
        $domain = MOBILE_DOMAIN;
        switch ($exceptionPage){
            case 'index':
                $url = $domain . 'union/index.html';
                break;
            case 'error':
                $url = $domain . 'union/error.html';
                break;
            case 'detail':
                $url = $domain. 'union/pdetail.html?lid='.$lid;
                break;
            case 'user_detail':
                $newDomain = str_replace('wx', $account, $domain);
                $url = $newDomain. 'wx/uniontour/pdetail.html?lid='.$lid;
                break;
            case 'order_detail':
                $newDomain = str_replace('wx', $account, $domain);
                $url = $newDomain. 'wx/uniontour/userorder.html#/detail/'.$ordernum;
                break;
            default:
                $url = $domain . 'union/index.html';
                break;
        }
        header('Location:' . $url);
        exit();
    }
    /**
     * 根据银联获取的位置转换成数据库配置id
     * <AUTHOR>
     * @date 2019-06-28
     */
    public function getPositionInfo(){
        $cityId = I('get.city_id',0);
        if (!$cityId){
            $this->apiReturn(parent::CODE_NO_CONTENT, [], '定位获取失败');
        }
        $data = [
            'name'    => '福州',
            'area_id' => '381'
        ];
        $idCityCode     = substr($cityId, 0, 4);
        $idCardCity         = load_config('id_card_city', 'account');
        $areaMdl = new Area();
        $areaArr = $areaMdl->getAreaList();
        $areaArr   = array_flip($areaArr);
        $city         = $idCardCity[$idCityCode];
        $cityCode     = $areaArr[$city];
        if (!$cityCode){
            $this->apiReturn(200, $data);
        }
        $data = [
            'name'    => $city,
            'area_id' => $cityCode
        ];
        $this->apiReturn(200, $data);
    }
    /**
     * 获取城市信息（目前银联只想线上福建省的）
     * <AUTHOR>
     * @date 2019-06-28
     */
    public function getCityInfo(){
        $cityConfig     = load_config('cities', 'account');
        $provinceConfig = load_config('province','account');
        $province[0]       = $provinceConfig[12];
        $province[0]['city'] = $cityConfig[12];
        $data  = [
            'list' => $province,
        ];
        $this->apiReturn(200, $data);
    }
    private function _isGetOpenId(){
        if ($_SESSION['union_phone'] && $_SESSION['union_openid']){
            return true;
        }
        return false;
    }
}