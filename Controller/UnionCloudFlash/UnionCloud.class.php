<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/6/19
 * Time: 15:13
 */
namespace Controller\UnionCloudFlash;

use Library\Controller;
use Model\Member\Member;

class UnionCloud extends Controller {
    /**
     * 银联用户session
     * <AUTHOR>
     * @date 2019-06-28
     */
    protected function getUnionUserInfo(){
        $phone  = $_SESSION['union_phone'];
        $openId = $_SESSION['union_openid'];
        return ['phone' => $phone,'openid' => $openId];
    }
    protected function getSupplyInfo(){
        //指定账号
        if (I('account')) {
            return I('account', '', 'intval');
        }

        $host_info = explode('.', $_SERVER['HTTP_HOST']);
        $account   = \safetxt($host_info[0]);
        $memModel = new Member('slave');
        $member = $memModel->getMemberInfo((string)$account, 'account');
        //供应商账号不存在,退出程序
        if (!$member) {
            exit('非法请求');
        }

        return $member['id'];
    }
}