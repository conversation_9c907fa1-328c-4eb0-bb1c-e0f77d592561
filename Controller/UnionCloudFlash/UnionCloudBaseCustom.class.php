<?php
/**
 * 云闪付 - 商家定制
 */

namespace Controller\UnionCloudFlash;

use Library\CloudFlash\CoreCustom;
use Library\CloudFlash\UnionPayCustomHelp;

class UnionCloudBaseCustom extends UnionCloud
{
    /**
     * 获取前端sdk使用的token
     */
    public function getFrontToken()
    {
        $url = I('url', '', 'strval');
        if (!$url) {
            $url = $_SERVER['HTTP_REFERER'];
        }
        $url      = urldecode($url);
        $cloudLib = new CoreCustom(UnionPayCustomHelp::getAccount());
        if (ENV == 'PRODUCTION') {
            $result = $cloudLib->getUnionJs($url);
        } else {
            $result = $cloudLib->getUnionJs($url);
        }
        $this->apiReturn(200, $result);
    }

    /**
     *
     * @return array
     * <AUTHOR>
     * @date 2021/5/28
     *
     *
     */
    public function unionUserAuthorize()
    {
        $code  = I('code', '');
        $state = I('state', '');
        if (empty($code) || empty($state)) {
            $this->apiReturn(204, [], '参数缺失');
        }
        try {
            $params  = json_decode(base64_decode($state), true);
            $authBiz = new \Business\UnionCloud\UnionCloudBaseCustom();
            if ($params['mode'] == 1) {
                $auth = $authBiz->getUnionCustomParseAuthInfo($code);
                if (empty($auth['data'])) {
                    //初步授权成功，返回特殊code 让前端调起授权电话号码sdk
                    $this->apiReturn($authBiz::AUTHORIZED_MOBILE_CODE, [], '电话号码授权');
                }
            } elseif ($params['mode'] == 2) {
                $auth = $authBiz->getUnionPhoneByCode($code);
            } else {
                throw new \Exception('授权模式错误');
            }
            if ($auth['code'] != 200) {
                throw new \Exception($auth['msg']);
            }
            //后期优化一下写法 todo 这边可以不这么取appid
            $_SESSION['union_custom_phone_' . $auth['data']['appId']]  = $auth['data']['phone'];
            $_SESSION['union_custom_openid_' . $auth['data']['appId']] = $auth['data']['openId'];

            //授权成功，默认跳转首页
            $this->apiReturn(200, [], '成功');
        } catch (\Exception $e) {
            pft_log('unioncloud/error', $e->getMessage());
            //跳到错误提示页面
            $this->apiReturn(204, [], $e->getMessage());
        }
    }

}