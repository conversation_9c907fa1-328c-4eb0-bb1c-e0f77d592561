<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/6/21
 * Time: 18:09
 */

namespace Controller\UnionCloudFlash;

use Business\UnionCloud\UnionCloudMemberCustom as UnionMember;
use Library\CloudFlash\CoreCustom;
use Library\CloudFlash\UnionPayCustomHelp;

class UnionCloudMemberCustom extends UnionCloud
{
    /**
     *
     * @return array
     * <AUTHOR>
     * @date 2021/5/31
     *
     *
     */
    public function unionLogin()
    {
        $account = I('account', '123624', 'strval'); //分销商账号account
        if (empty($account)) {
            $this->apiReturn(204, [], '参数缺失');
        }
        $info = $this->getUnionCustomUserInfo();
        if (ENV == 'DEVELOP' || ENV == 'LOCAL') {
            $info = [
                'phone'  => ***********,
                'openid' => 'unionTest',
            ];
        }
        if (empty($info['phone']) || empty($info['openid'])) {
            $this->apiReturn(204, 0, '授权参数获取失败，请重新授权');
        }
        $unionMemberBiz = new UnionMember();
        $result         = $unionMemberBiz->unionMemberCreate($info['phone'], $info['openid']);
        if (!isset($result['code'])) {
            $this->apiReturn(500, [], '系统异常');
        }
        $memberId = $result['data'];
        if (empty($memberId)) {
            $this->apiReturn(204, [], '登录失败');
        }
        //获取微商城token
        $cooAccountBz = new \Business\Cooperator\Account\LoginMall();
        $list         = $cooAccountBz->unionPayLoginMall($memberId, $info['phone'], $account);
        if ($list['code'] != 200 || empty($list['data']['access_token'])) {
            $this->apiReturn($list['code'], $list['data'], $list['msg']);
        }
        $returnData = [
            'access_token' => $list['data']['access_token'],
            'member_id'    => $memberId,
        ];

        $this->apiReturn(200, $returnData, '登录成功');
    }

    /**
     * 银联用户session
     * <AUTHOR>
     * @date 2019-06-28
     */
    protected function getUnionCustomUserInfo()
    {
        $cloudLib = new CoreCustom(UnionPayCustomHelp::getAccount());
        $phone    = $_SESSION['union_custom_phone_' . $cloudLib->appid];
        $openId   = $_SESSION['union_custom_openid_' . $cloudLib->appid];

        return ['phone' => $phone, 'openid' => $openId];
    }

    /**
     * 获取银联用户基本信息
     * <AUTHOR>
     * @date 2019-06-28
     */
    public function getUnionMemberInfo()
    {
        $memberId   = $this->isLogin('ajax');
        $memberBiz  = new \Business\Member\Member();
        $memberInfo = $memberBiz->getInfo($memberId);

        $this->apiReturn(200, $memberInfo);
    }
}