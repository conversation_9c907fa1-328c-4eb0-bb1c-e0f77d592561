<?php
/**
 * e福州 茉莉分折扣
 *
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 2018/8/5
 * Time: 14:32
 */

namespace Controller\Product;

use Library\Controller;
use Business\Product\FzJasmine as JasminBiz;

class FzJasmine extends Controller
{
    private $_memberId;
    private $_sid;
    private $_jasminBiz;

    public function __construct()
    {
        parent::__construct();
        $loginInfo        = $this->getLoginInfo();
        $this->_memberId  = $loginInfo['memberID'];
        $this->_sid       = $loginInfo['sid'];
        $this->_jasminBiz = new JasminBiz($this->_memberId, $this->_sid);
    }

    /***
     * 编辑保存折扣配置 -- 保存用于折扣的产品
     */
    public function saveJasmineDiscount()
    {
        $request      = I('post.');
        $discountList = $request['discountList'];
        $productList  = $request['productList'];

        //折扣配置数组
        if (!$discountList || !is_array($discountList) || count($discountList) < 1) {
            $this->apiReturn(204, '', '折扣信息为空!');
        }

        if (count($discountList) > 10) {
            $this->apiReturn(203, '', '折扣方案不能超过10个!');
        }

        //检查分数区间重叠问题
        $overlap = $this->_jasminBiz->checkSectionOverlap($discountList);
        if ($overlap) {
            $this->apiReturn(203, '', '折扣方案分数区间有误!');
        }

        $disList = array();
        //全部成功的话 返回id列表给产品设置用
        foreach ($discountList as $index => $disData) {
            $disRes    = $this->_setDiscount($disData, $index, $this->_memberId);
            $disList[] = $disRes;
        }

        $delRes = $this->_jasminBiz->delRedundantDiscounts($disList, $this->_memberId);
        if (!$delRes) {
            $this->apiReturn(201, '', '删除多余折扣方案失败!');
        }

        //产品可以不先设置 有设置再处理
        $proRes = [];
        if ($productList && is_array($productList)) {
            $proRes = $this->_setTicketDis($productList, $this->_memberId, $disList);
        }

        $return = [
            'discountsRes' => $disList,
            'productsRes'  => $proRes,
        ];

        pft_log('fzcitycard/jasmine',
            "保存配置操作: operId:{$this->_memberId}|折扣方案:" . json_encode($disList) . "|选择的产品:" . json_encode($proRes));
        $this->apiReturn(200, $return, 'success');
    }

    /***
     * 获取折扣列表
     */
    public function getJasmineDiscount()
    {
        $list = $this->_jasminBiz->getJasmineList();
        $this->apiReturn(200, $list, 'success');
    }

    /***
     * 获取使用了折扣的产品
     */
    public function getDiscountProducts()
    {
        $list = $this->_jasminBiz->getDiscountProducts($this->_memberId);
        $this->apiReturn(200, $list, 'success');
    }

    /***
     * 删除产品已选的折扣方案
     */
    public function deleteTicketDiscount()
    {
        $tid       = I('tid', 0, 'intval');
        $discounts = I('discounts', []);

        if (!$tid || !is_array($discounts)) {
            $this->apiReturn(203, [], '缺少删除信息');
        }

        $res = $this->_jasminBiz->saveNowTicketDiscount($tid, $discounts);

        if (false === $res) {
            $this->apiReturn(201, [], '删除失败');
        }
        pft_log('fzcitycard/jasmine', "删除配置操作: operId:{$this->_memberId}|tid:$tid|剩余的折扣方案:" . json_encode($discounts));
        $this->apiReturn(200, [], 'success');
    }

    /***
     * 保存折扣方案
     *
     * @param $data
     * @param $sid
     * @param $type
     * @param  array  $afterDisList
     *
     * @return mixed
     */
    private function _setDiscount($data, $index, $sid)
    {
        $returnData['code'] = 200;
        $returnData['msg']  = 'success';

        $disList = $this->_jasminBiz->getJasmineList();
        //数据预处理
        $checkRes = $this->_jasminBiz->beforeDiscountData($data, $index, $disList);

        if ($checkRes['code'] != 200) {
            $returnData['code'] = $checkRes['code'];
            $returnData['msg']  = $checkRes['msg'];

            return $returnData;
        }

        $checkData = $checkRes['data'];
        //根据id区分保存还是添加新的
        if ($checkData['id'] > 0) {
            //更新
            $res = $this->_jasminBiz->updateDiscount($checkData);
        } else {
            //添加新的
            $res = $this->_jasminBiz->addDiscount($checkData, $sid);
        }

        if (false !== $res) {
            $returnData['data'] = ['id' => $res];
        } else {
            $returnData['code'] = 201;
            $returnData['msg']  = '保存失败';
            $returnData['data'] = ['id' => 0];
        }

        return $returnData;
    }

    /***
     * 保存配置的折扣产品信息
     *
     * @param $productList
     * @param $aid
     * @param $disList
     *
     * @return mixed
     */
    private function _setTicketDis($productList, $aid, $disList)
    {
        $returnData['code'] = 200;
        $returnData['msg']  = 'success';
        $return             = array();

        //数据处理
        $checkRes = $this->_jasminBiz->checkData($productList, $aid, $disList);

        if ($checkRes['code'] != 200) {
            $returnData['code'] = $checkRes['code'];
            $returnData['msg']  = $checkRes['msg'];

            return $returnData;
        }

        $checkData = $checkRes['data'];

        //检查产品合法性
        $proCheck = $this->_jasminBiz->checkProduct($productList);
        foreach ($checkData as $data) {
            //检查产品信息
            if (isset($proCheck[$data['tid']])) {
                $err                = $proCheck[$data['tid']];
                $returnData['code'] = $err['code'];
                $returnData['msg']  = $err['msg'];
                $returnData['data'] = $returnData['data'] = [
                    'title' => $data['title'],
                    'tid'   => $data['tid'],
                ];
            } else {
                //根据id区分保存还是添加新的
                if ($data['id'] > 0) {
                    //更新
                    $res = $this->_jasminBiz->updateDisTicket($data);
                } else {
                    //添加新的
                    $res = $this->_jasminBiz->addDisTicket($data, $aid);
                }

                if (false !== $res) {
                    $returnData['data'] = [
                        'id'    => $res,
                        'title' => $data['title'],
                        'tid'   => $data['tid'],
                    ];
                } else {
                    $returnData['code'] = 201;
                    $returnData['msg']  = '保存失败';
                    $returnData['data'] = [
                        'id'    => $res,
                        'title' => $data['title'],
                        'tid'   => $data['tid'],
                    ];
                }
            }

            $return[] = $returnData;
        }

        return $return;
    }
}