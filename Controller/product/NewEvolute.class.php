<?php

namespace Controller\Product;

use Business\Authority\DataAuthLogic;
use Business\CommodityCenter\Ticket;
use Business\Hotel\Intranet\RatePlan;
use Business\Hotel\Intranet\RoomType;
use Business\NewJavaApi\DistributionCenter\TransferProductQuery;
use Business\Product\NewEvolute as NewEvoluteBusiness;
use Business\Product\SubProduct as SubProductBiz;
use Library\Controller;

class NewEvolute extends Controller
{
    private $_sid;
    private $_mid;
    private $_loginInfo;

    public function __construct()
    {
        parent::__construct();

        $this->_sid = $this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
        $this->_mid = $this->_loginInfo['memberID'];

        $this->specialUserLimit();
    }

    /**
     * 获取转分销产品列表
     */
    public function getProList(): void
    {
        $page = I('page', 1, 'intval'); // 当前页数
        $pageSize = I('pageSize', 10, 'intval'); // 每页显示条数
        $city = I('city') ? intval(I('city')) : null; // 城市code
        $oversea = I('oversea') != '' ? intval(I('oversea')) : null; // 境内外
        $province = I('province') != '' ? intval(I('province')) : null; // 省份code
        $title = I('title') ?: null; // 景区标题
        $supplier = I('supplier') ?: null; // 供应商名称
        $type = I('type') ?: null; // 产品类型
        $subType = I('subType') ?: null;// 产品子类型
        // 开启转分销状态 -1=全部 0=关闭 1=开启
        $disOpen = I('openDistribution') != -1 ? boolval(I('openDistribution')) : null;

        $skuTags = I('sku_tags', '', 'strval,trim');
        $skuTags = json_decode($skuTags, true);
        if (json_last_error() != JSON_ERROR_NONE || empty($skuTags)) {
            $skuTags = null;
        }

        [$type, $subType] = SubProductBiz::decodeTypeAndSubType($type, $subType);
        $subType = $subType ?: ($type ? 0 : null);

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition();
        // 全禁的情况下不需要调用接口
        if ($condition === false) {
            $this->apiReturn(self::CODE_SUCCESS, [
                'sid'       => $this->_sid,
                'lists'     => [],
                'page'      => $page,
                'pageSize'  => $pageSize,
                'totalPage' => 0,
                'totalNum'  => 0,
            ]);
        }

        // 1. 分销中心加载全部产品
        try {
            $result = TransferProductQuery::querySaleTransferProductByPaging(3, 3, $page,
                $pageSize, $city, $this->_sid, $title, $disOpen, $oversea, $type, $subType, $province,
                true, $supplier, $condition['lidList'], $condition['notLidList'], $skuTags);
        } catch (\Throwable $e) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], $e->getMessage());
            return;
        }
        $distributionCenterProducts = $result['rows'];
        if (empty($distributionCenterProducts)) {
            $this->apiReturn(self::CODE_SUCCESS, [
                'sid'       => $this->_sid,
                'lists'     => [],
                'page'      => $page,
                'pageSize'  => $pageSize,
                'totalPage' => 0,
                'totalNum'  => 0,
            ]);
            return;
        }

        // 2. 拆分为酒店产品和其他产品
        $distributionCenterHotelProducts = $distributionCenterOtherProducts = [];
        foreach ($distributionCenterProducts as $item) {
            if ($item['productType'] == 'N') {
                $distributionCenterHotelProducts[] = $item;
            } else {
                $distributionCenterOtherProducts[] = $item;
            }
        }

        // 3. 根据分销中心提供的字段，查询产品详情，组装合并为新的数据结构
        // 3.1 原二级结构产品
        $otherProductsData = NewEvoluteBusiness::formatOtherProductsByDistributionCenter(
            $distributionCenterOtherProducts, $this->_sid, $this->_mid);
        // 3.2 三级结构产品（酒店）
        $hotelProductsData = NewEvoluteBusiness::formatHotelProductsByDistributionCenter(
            $distributionCenterHotelProducts, $this->_mid);

        // 4. 根据顺序组装回分销中心提供的数据结构
        $mapOtherProductsData = array_key($otherProductsData, 'lid');
        $mapHotelProductsData = array_key($hotelProductsData, 'centre_poi_id');
        $lists = [];
        foreach ($distributionCenterProducts as $item) {
            if ($item['productType'] == 'N') {
                $lists[] = $mapHotelProductsData[$item['itemId']] ?? [];
            } else {
                $lists[] = $mapOtherProductsData[$item['itemId']] ?? [];
            }
        }

        $return = [
            'sid'       => $this->_sid,
            'lists'     => $lists,
            'page'      => $page,
            'pageSize'  => $pageSize,
            'totalPage' => ceil($result['total'] / $pageSize),
            'totalNum'  => $result['total'],
        ];

        $this->apiReturn(self::CODE_SUCCESS, $return);
    }

    public function getRoomTypes(): void
    {
        $ids = $this->getIds();
        $supplier = I('supplier') ?: null; // 供应商名称
        try {
            $result = TransferProductQuery::querySaleTransferSecondProductList(3, $ids, 3,
                $this->_sid, null, true, $supplier);
        } catch (\Throwable $e) {
            $this->apiReturn(500, [], $e->getMessage());
            return;
        }
        $secondList = $result['rows'];
        if (empty($secondList)) {
            $this->apiReturn(200);
            return;
        }

        $roomTypeIds = $skuIds = [];
        foreach ($secondList as $roomTypeInfo) {
            $roomTypeIds[] = $roomTypeInfo['itemId'];
            foreach ($roomTypeInfo['skuList'] as $skuInfo) {
                $skuIds[] = $skuInfo['skuId'];
            }
        }

        $roomTypes = (new RoomType())->listByIds($roomTypeIds);
        $mapRoomTypes = array_key($roomTypes, 'id');
        $ratePlans = (new RatePlan())->listByIds($skuIds);
        $mapRatePlans = array_key($ratePlans, 'centre_sku_id');

        $data = [];
        foreach ($secondList as $roomTypeInfo) {
            $roomType = $mapRoomTypes[$roomTypeInfo['itemId']] ?? [];
            $roomTypeData = NewEvoluteBusiness::formatRoomType($roomType, $roomTypeInfo);
            foreach ($roomTypeInfo['skuList'] as $skuInfo) {
                $ratePlan = $mapRatePlans[$skuInfo['skuId']] ?? [];
                $roomTypeData['rate_plans'][] = NewEvoluteBusiness::formatRatePlan($ratePlan, $skuInfo);
            }
            $data[] = $roomTypeData;
        }

        $this->apiReturn(200, $data);
    }

    public function getRatePlans(): void
    {
        $ids = $this->getIds();
        try {
            $result = TransferProductQuery::querySaleTransferSkuList($ids, $this->_sid, null, true);
        } catch (\Throwable $e) {
            $this->apiReturn(500, [], $e->getMessage());
            return;
        }
        $skuList = $result['rows'];
        if (empty($skuList)) {
            $this->apiReturn(200);
            return;
        }
        $skuIds = [];
        foreach ($skuList as $skuInfo) {
            $skuIds[] = $skuInfo['skuId'];
        }
        $ratePlans = (new RatePlan())->listByIds($skuIds);
        $mapRatePlans = array_key($ratePlans, 'centre_sku_id');

        $data = [];
        foreach ($skuList as $skuInfo) {
            $ratePlan = $mapRatePlans[$skuInfo['skuId']] ?? [];
            $data[] = NewEvoluteBusiness::formatRatePlan($ratePlan, $skuInfo);
        }

        $this->apiReturn(200, $data);
    }

    public function getTickets(): void
    {
        $skuIds = $this->getIds();

        $skuTags = I('sku_tags', '', 'strval,trim');
        $skuTags = json_decode($skuTags, true);
        if (json_last_error() != JSON_ERROR_NONE || empty($skuTags)) {
            $skuTags = null;
        }

        try {
            $result = TransferProductQuery::querySaleTransferSkuList($skuIds, $this->_sid, null, true, $skuTags);
        } catch (\Throwable $e) {
            $this->apiReturn(self::CODE_SUCCESS, [], "查询转分销门票sku列表失败：" . $e->getMessage());
            return;
        }
        $ticketList = $result['rows'];
        if (empty($ticketList)) {
            $this->apiReturn(self::CODE_SUCCESS, [], 'empty');
            return;
        }

        $transferTicketList = NewEvoluteBusiness::handleTransferTicketList($ticketList, $skuIds, $this->_sid, $this->_mid);
        $this->apiReturn(self::CODE_SUCCESS, $transferTicketList);
    }

    public function isEnabled(): void
    {
        $this->apiReturn(200, ['isEnabled' => NewEvoluteBusiness::isEnabled($this->_sid)]);
    }

    private function getIds(): array
    {
        $ids = I('ids', '', 'strval');
        if (!$ids) {
            $this->apiReturn(400, [], 'ids不能为空');
        }
        $ids = array_filter(explode(',', $ids));
        if (!$ids) {
            $this->apiReturn(400, [], 'ids不能为空');
        }
        if (count($ids) > 100) {
            $this->apiReturn(400, [], 'ids不能超过100个');
        }
        foreach ($ids as &$id) {
            if (!is_numeric($id)) {
                $this->apiReturn(400, [], 'ids必须为数字');
            }
            $id = intval($id);
        }
        return $ids;
    }

    private function specialUserLimit(): void
    {
        //临时在这边做下特殊用户的访问控制
        $limitUserList = [
            684493,
            4454288,
            131887,
            57801,
            315436,
            4196846,
            91441,
            919790,
            755911,
            4747316,
            2155259,
            675904,
            216206,
            256068,
            110845,
            433028,
            522628,
            6970,  //加一个测试账号
        ];
        $limitTime = 60;
        $limitKey = "pft_evolute:limit:{$this->_sid}";
        $redisCache = \Library\Cache\Cache::getInstance('redis');

        if (in_array($this->_sid, $limitUserList)) {
            //设置一个60秒的锁，60秒之类只能访问这个接口一次
            $lockRes = $redisCache->lock($limitKey, 1, $limitTime);

            if (!$lockRes) {
                $this->apiReturn(500, [], '请求过于频繁，请稍后重试');
            }
        }

        //查询完成之后删除锁
        if (in_array($this->_sid, $limitUserList)) {
            $redisCache->rm($limitKey);
        }
    }
}
