<?php
/**
 * Created by PhpStorm.
 * User: cgp
 * Date: 2018/12/22
 * Time: 14:46
 */

namespace Controller\product;


use Business\AnnualCard\CardManage;
use Business\Face\AnnualAliFaceBiz;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\PftSystem\FaceCompare;
use Business\Product\AnnualCardConst;
use Library\Container;
use Library\Controller;
use Library\ctid\sdk;
use Library\MessageNotify\PFTSMSInterface;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Tools\Helpers;
use Library\Util\EnvUtil;
use Library\Util\AnnualUtil;
use Model\Member\Member;

class AnnualCardPhoto extends Controller
{

    private function hashId($token, $action)
    {
        $hashLib = new \Library\Hashids\Hashids('cab');
        if ($action == 'decode') {
            $res = $hashLib->decode($token);
            if ($res) {
                $res = array_shift($res);
            }
        } elseif ($action == 'encode') {
            $res = $hashLib->encode($token);
        }
        return $res;
    }

    private function checkFaceRegisterState($faceNum, $sid, $cardVirtualNo)
    {
        // 白水洋的年卡原先注册的照片用的是身份证的，像素太低需要重新采集
        if ($sid ==2749) {
           return false;
        }
        $fc         = new FaceCompare('baidu', $sid);
        $biz        = new \Business\Product\AnnualCard();
        $hasRegister = $fc->checkFaceExistByPftFaceId($biz->getFaceGroupId($sid), $cardVirtualNo);
        if ($hasRegister) {
            $res = $fc->faceGetlist($cardVirtualNo, $biz->getFaceGroupId($sid));
            if ($res['error_code'] == 0) {
                $faceCnt = count($res['result']['face_list']);
                if ($faceNum < $faceCnt ) {
                    $hasRegister = false;
                }
            }
        }
        return $hasRegister;
    }

    private function getMemberCardInfo($sid, $mobile, $cardVirtualNo='', $isCheck = false)
    {
        //$member = new Member('slave');
        //直接根据手机号查，需要修改
        //$memberInfo = $member->getMemberInfo($mobile, 'mobile','id,dname,mobile');
        //if (!$memberInfo['id']) {
        //    $this->apiReturn(400, [], '会员信息不存在');
        //}
        $annualCard         = new \Model\Product\AnnualCard();
        $memberInfo         = $annualCard->getCardInfoByVirtual([$cardVirtualNo], 'memberid,dname,mobile');
        if (!$memberInfo[0]['memberid']) {
            $this->apiReturn(400, [], '会员信息不存在');
        }

        $memberInfo = $memberInfo[0];

        $cardVirtualNoArr   = $annualCard->getActivedCardList($sid, $memberInfo['memberid']);
        if ($cardVirtualNoArr) {
            if ($isCheck) {
                //用于检测是否存在年卡信息  存在则直接返回
                $this->apiReturn(200, [], 'success');
            }

            $ticketModel   = new \Model\Product\Ticket();
            //获取产品id查询出对应家庭卡数量  0表示非家庭卡
            $pidArr        = array_column($cardVirtualNoArr, 'pid');

            $javaApi       = new \Business\CommodityCenter\LandF();
            $familyCardNum = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([], [], $pidArr, 'pid,family_card_num', true);

            //获取出对应的产品名称
            $pName         = $ticketModel->getCardName($pidArr);
            $output        = [];
            if ($cardVirtualNo) {
                $pid = $annualCard->getCardsByVirtualNoArr([$cardVirtualNo], 'pid', true);
                // 调用百度接口，检测是否已经在百度上面注册过
                $hasRegister = $this->checkFaceRegisterState($familyCardNum[$pid], $sid, $cardVirtualNo);
                $photos = $annualCard->getGroupPhotos($cardVirtualNo);
                if (isset($photos['id'])) {
                    $photos = json_decode($photos['photos'], true);
                }
                $output = [
                    'dname'           => $memberInfo['dname'],
                    'mobile'          => substr_replace($memberInfo['mobile'],'****',3,4),
                    'telephone'       => $memberInfo['mobile'],
                    'virtual_no'      => $cardVirtualNo,
                    'family_card_num' => $familyCardNum[$pid],
                    'title'           => $pName[$pid],
                    'photos'          => $photos,
                    'register'        => $hasRegister, // 这个参数用来判断是否已经在百度平台注册过人脸了，如果是false或0，那么要允许重新注册人脸
                ];
            } else {
                foreach ($cardVirtualNoArr as $key => $value) {
                    // 调用百度接口，检测是否已经在百度上面注册过
                    $hasRegister = $this->checkFaceRegisterState($familyCardNum[$value['pid']], $sid, $value['virtual_no']);
                    $photos = $annualCard->getGroupPhotos($value['virtual_no']);
                    if (isset($photos['id'])) {
                        $photos = json_decode($photos['photos'], true);
                    }
                    $output[] = [
                        'dname'           => $memberInfo['dname'],
                        'mobile'          => substr_replace($memberInfo['mobile'],'****',3,4),
                        'telephone'       => $memberInfo['mobile'],
                        'virtual_no'      => $value['virtual_no'],
                        'family_card_num' => $familyCardNum[$value['pid']],
                        'title'           => $pName[$value['pid']],
                        'photos'          => $photos,
                        'register'        => $hasRegister, // 这个参数用来判断是否已经在百度平台注册过人脸了，如果是false或0，那么要允许重新注册人脸
                    ];
                }
            }

            return $output;
        } else {
            $this->apiReturn(400, [], '年卡信息不存在');
        }
    }

    /**
     * 判断手机号是否有绑定年卡
     * <AUTHOR> Chen
     * @date 2018/12/22
     *
     * url:my.12301.cc/r/product_AnnualCardPhoto/checkCardInfoByMobile
     * post.token: 加密的手机号，页面接收到的数据原样提交给后端
     * post.sid:   供应商ID
     * 成功返回：{"code":200,"data":{"dname":"姓名","mobile":"手机号","virtual_no":"虚拟卡号"},"msg":"success"}
     * 失败返回：{"code":400,"data":[],"msg":"错误描述"}
     */
    public function checkCardInfoByMobile()
    {
        $token           = I('post.token');
        $sid             = I('post.sid', 2010497, 'intval');
        $cardVirtualNo   = I('post.virtual_no', '', 'strval');
        $cardVirtualNo   = str_replace(['(', ')','（', '）'], '', $cardVirtualNo);
        AnnualUtil::info([
            'tag' => 'checkCardInfoByMobile',
            'data'   => $_POST,
            'Referer' => $_SERVER['HTTP_REFERER'],
        ]);
        $mobile = $this->hashId($token, 'decode');
        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(400, [], 'error token');
        }
        $this->setTokenSession($mobile);
        $output = $this->getMemberCardInfo($sid, $mobile, $cardVirtualNo);
        $this->apiReturn(200, $output, 'success');
    }

    /**
     * 根据手机号和短信验证码查询年卡信息
     *
     * @author: guanpeng
     * @date: 2019/1/18
     */
    public function checkCardInfoByMobileAndToken()
    {
        // 短信验证码，一定要注意，验证码要有防刷的功能！
        $token           = I('post.sms_token');
        // 手机号
        $mobile          = I('post.mobile');
        // 每个景区对应的供应商ID，生成二维码的时候，需要把这个参数加入到二维码的链接里面
        $sid             = I('post.sid', 0, 'intval');
        AnnualUtil::info([
            'tag' => 'checkCardInfoByMobileAndToken',
            'data'   => $_POST,
            'Referer' => $_SERVER['HTTP_REFERER'],
        ]);
        if (empty($token)) {
            $this->apiReturn(400, [], '短信验证码必填');
        }
        // 参考系统上其他地方发送验证码的功能
        if(Helpers::ChkCode($mobile, $token) !== true) {
            $this->apiReturn(204, [], '验证码错误');
        }
         // 此处增加一个服务端的session，用来上传人脸时候的安全校验
        $this->setTokenSession($mobile);
        $output = $this->getMemberCardInfo($sid, $mobile);
        $this->apiReturn(200, $output, 'success');
    }

    private function setTokenSession($mobile)
    {
        $_SESSION['annual_face_token'] = md5($mobile);
    }

    /**
     * 前端安全校验
     *
     * @author: guanpeng
     * @date: 2019/1/23
     * @param $mobile
     * @return bool
     */
    private function checkTokenSession($mobile)
    {
        if (!isset($_SESSION['annual_face_token'])) {
            return false;
        }
        return md5($mobile) == $_SESSION['annual_face_token'];
    }

    /**
     * 上传年卡人脸头像——Step1
     * <AUTHOR> Chen
     * @date   2019/01/22
     * url:my.12301.cc/r/Product_AnnualCardPhoto/uploadAnnualPhoto
     * post.mobile:    手机号
     * post.imageBase64:   base64的图片
     *
     * 成功返回：{"code":200,"data":{"src":"图片地址"},"msg":"success"}
     * 失败返回：{"code":400,"data":[],"msg":"错误描述"}
     */
    public function uploadAnnualPhoto()
    {
        $mobile     = I('post.mobile');
        if (empty($mobile)) {
            $this->apiReturn(400, [], '手机号错误');
        }
        if ( !$this->checkTokenSession($mobile) ) {
            $this->apiReturn(400, [], '安全校验失败');
        }
        $faceBase64 = I('post.imageBase64');
        if (!$faceBase64) {
            $this->apiReturn(400, [], '头像不能为空');
        }
        $result     = Helpers::uploadImage2AliOss('annual_card', $faceBase64);
        if ($result['code'] != 200 ) {
            $this->apiReturn(400, [], '人脸上传失败');
        }

        $this->apiReturn(200, $result['data'], '人脸上传成功');
    }

    /**
     * 保存年卡人脸头像——Step2
     * <AUTHOR> Chen
     * @date   2018/12/22
     * url:my.12301.cc/r/Product_AnnualCardPhoto/savePhoto
     * post.mobile:        年卡手机号
     * post.virtual_no:    年卡虚拟卡号，由checkCardInfoByMobile接口返回的
     * post.imageBase64:   base64的图片,可以传空，如果有imageUrl参数的话。
     * post.imageUrlArr:   图片url地址;先调用uploadAnnualPhoto方法得到图片url地址（imageBase64用于旧版）
     *
     * 成功返回：{"code":200,"data":[],"msg":"success"}
     * 失败返回：{"code":400,"data":[],"msg":"错误描述"}
     */
    public function savePhoto()
    {
        $virtualNo  = I('post.virtual_no');
        $faceBase64 = I('post.imageBase64');
        $faceUrlArr = I('post.imageUrlArr');
        $mobile     = I('post.mobile');
        $idCardNo   = I('post.idcard', '', 'strval');
        AnnualUtil::info([
            'tag' => 'savePhoto',
            'data'=>$_POST,
            'referer'=>$_SERVER['HTTP_REFERER'],
        ]);
        if ( !$this->checkTokenSession($mobile) ) {
            $this->apiReturn(400, [], '安全校验失败');
        }
        if (!$virtualNo) {
            $this->apiReturn(400, [], '虚拟卡号不能为空');
        }
        if (!$faceBase64 && !$faceUrlArr) {
            $this->apiReturn(400, [], '头像不能为空');
        }
        //检查是否已经有在photos里面绑定了人脸
        $annualCard = new \Model\Product\AnnualCard('slave');
        $cheRes     = $annualCard->getGroupPhotos($virtualNo);

        $memberCard = $annualCard->getAnnualCard($virtualNo, 'virtual_no');
        $memberId   = $memberCard['memberid'];
        $pid        = $memberCard['pid'];

        if (!$memberId) {
            $this->apiReturn(400, [], '该虚拟卡未绑定用户,卡号:'.$virtualNo);
        }

        //如果有绑定人脸，那么根据虚拟卡号获取身份证和姓名；没有绑定过的，不用判断。
        if ($cheRes && $idCardNo) {
            if (!\idcard_checksum18($idCardNo)) {
                $this->apiReturn(400, [], '身份证号码格式错误');
            }
            //获取出用户的姓名、身份证
            //用户二期
            $MemberBus   = new \Business\Member\Member();
            $CustomerBus = new \Business\Member\Customer();
            $memberInfo   = $MemberBus->getInfo($memberId);
            $customerInfo = $CustomerBus->getCustomerInfoByMemberId($memberId);

            if ($idCardNo == $customerInfo['id_card_no']) {
                $dname    = $memberInfo['dname'];
                $memberCard['dname'] = $memberInfo['dname'];
                //调用新大陆实名制校验的接口
                try {
                    $sdk  = new sdk('0x40');
                } catch (\Exception $e) {
                    $this->apiReturn(400, [], '实名制校验接口调用失败，请稍后重试');
                }
                $applyRes = $sdk->apply();
                if (!empty($applyRes) && $applyRes['code'] == 0) {
                    $bizNum      = $applyRes['data']['bizSerialNum'];
                    //$photoBase64 = substr(strstr($faceBase64,','),1);
                    $res         = $sdk->identify($bizNum, $dname, $idCardNo);
                    //如果不是同一个人，不允许更新人脸
                    if ($res['code'] != 0) {
                        AnnualUtil::info([
                            'tag' => 'savePhoto',
                            'applyRes' => $applyRes,
                            'dname' => $dname,
                            'idCardNo' => $idCardNo,
                            'msg'  => '身份证与姓名不匹配，请确认',
                            'virtualNo'  => $virtualNo,
                            'res'  => $res,
                        ]);
                        $this->apiReturn(400, [], '身份证与姓名不匹配，请确认');
                    }
                } else {
                    AnnualUtil::info([
                        'tag' => 'savePhoto',
                        'applyRes' => $applyRes,
                        'dname' => $dname,
                        'idCardNo' => $idCardNo,
                        'virtualNo'  => $virtualNo,
                    ]);
                    $this->apiReturn(400, [], '接口错误，请稍后重试');
                }
            } elseif($customerInfo['id_card_no'] != '' ) {
                $this->apiReturn(400, [], '您输入的身份证号和办理年卡的身份证号不一致，请确认');
            }
        }

        if (!empty($faceBase64)) {
            $result     = Helpers::uploadImage2AliOss('annual_card', $faceBase64);
            if ($result['code'] != 200 ) {
                $this->apiReturn(400, [], '人脸上传失败');
            }
            $faceUrlArr = [$result['data']['src']];
        }

        foreach ($faceUrlArr as $key => $value) {
            if (strpos($value, 'http') === false) {
                $this->apiReturn(400, [], '头像格式非法,请重新采集照片上传');
            }
        }

        $faceCnt     = count($faceUrlArr);

        //判断是否家庭年卡，如果是家庭年卡最大允许上传几个人脸——For LeePj
        $javaApi       = new \Business\CommodityCenter\LandF();
        $ticketArr     = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([], [], [$pid], 'family_card_num');
        $familyCardNum = $ticketArr[0];

        if ( $familyCardNum['family_card_num'] > 0 ) {
            $maxFaceNumber = $familyCardNum['family_card_num'];
        } else {
            $maxFaceNumber = 1;
        }
        if ( $familyCardNum['family_card_num'] > 0 && $maxFaceNumber < $faceCnt) {
            $this->apiReturn(400, [], "多人卡只允许保存{$maxFaceNumber}人，人数超出");
        } else if ($familyCardNum['family_card_num'] == 0  && $maxFaceNumber < $faceCnt) {
            $this->apiReturn(400, [], "上传人脸超出限制，非多人卡只允许保存1人");
        }

        $member = new Member();
        $save   = $member->updateMemberInfo($memberId, ['headphoto' => $faceUrlArr[0]]);

        if (!$save) {
            AnnualUtil::info([
                'tag' => 'savePhoto',
                'headphoto' => $faceUrlArr[0],
                'memberId' => $memberId,
                'virtualNo'  => $virtualNo,
            ]);
        }
        $callIsAliFacePlatform = AnnualAliFaceBiz::getInstance()->callIsAliFacePlatform($memberCard['sid'], $memberCard['pid']);
        if ($callIsAliFacePlatform) {
            $params = [
                'sid' => $memberCard['sid'],
                'virtual_no' => $memberCard['virtual_no'],
                'tourist_info' => [
                    [
                        'name' => $memberCard['dname'],
                        'id_card_no' => $idCardNo,
                    ]
                ],
            ];
            AnnualAliFaceBiz::getInstance()->annualFaceRegisterProcess($params);
        }
        $annalBiz = new \Business\Product\AnnualCard();
        $saveRes  = $annalBiz->saveGroupPhotos($virtualNo, $faceUrlArr);

        $this->apiReturn($saveRes['code'], [], $saveRes['msg']);
    }

    /**
     * 发送年卡人脸绑定邀请链接短信，需要登录成功后才能调用
     *
     * <AUTHOR> Chen
     * @date 2018/12/22
     * url:my.12301.cc/r/product_AnnualCardPhoto/sendSms
     * post.mobile:    年卡绑定的手机号
     * post.virtual_no:  年卡虚拟卡号（/r/product_AnnualCard/getMemberList/接口里面返回的virtual_no字段）
     */
    public function sendSms()
    {
        $mobile        = I('post.mobile');
        $virtualNo     = I('post.virtual_no');

        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(400, [], '手机号格式错误');
        }
        $loginInfo  = $this->getLoginInfo('ajax');
        $annualModel = new \Model\Product\AnnualCard();
        $card = $annualModel->getAnnualCard($virtualNo, 'virtual_no');
        //手机号可能会被其他页面操作修改掉 这里取最新的覆盖下
        $mobile = empty($card['mobile']) ? $mobile : $card['mobile'];
        $token      = $this->hashId($mobile, 'encode');
        $url        = "https://wx.12301.cc/c/annual_face.html?token={$token}&sid={$loginInfo['sid']}&cardno={$virtualNo}";

        if ($loginInfo['sid'] == 2010497) {
            $template   = "池州市民旅游卡全面升级，线上信息录入，线下刷脸入园，速度采集人脸（{$url}），更多详情关注“乐GO池州”公众号或拨打电话0566-2319868。";
        } else {
            //$template   = "年卡功能全面升级，线上信息录入线下刷脸入园，速度采集人脸体验吧，采集地址:{$url}";
            $ticketInfo = (new CardManage())->getTicketAndPrivileges($card['sid'], $card['pid'], $card['id'], false, false);
            $template   = "您持有的{$ticketInfo['tInfo'][$card['pid']]['lname']}{$ticketInfo['tInfo'][$card['pid']]['tname']}{$card['virtual_no']}上传人像照片可刷脸快速入园，采集地址：{$url}";
        }
        $messageServiceApi = Container::pull(MessageService::class);
        [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, PFTSMSInterface::ISDIY, $mobile,
            [$template], $loginInfo['sid'], '', '人脸采集', '票付通', '', 1, true);
        if ($approval) {
            if (!EnvUtil::isRelease()) {
                pft_log('message_service', json_encode(['人脸采集', __METHOD__, [$mobile, [$template], $loginInfo['sid']], $res], JSON_UNESCAPED_UNICODE));
            }
        } else {
            /** @depreciate 放量结束后删除 */
            $smsLib     = SmsFactory::getFactory($mobile);
            $res = $smsLib->customMsg($loginInfo['sid'], '票付通', $template, $mobile, '', '', true, '人脸采集');
            if (!EnvUtil::isRelease()) {
                pft_log('message_service', json_encode(['人脸采集.old', __METHOD__, [$mobile, [$template], $loginInfo['sid']], $res], JSON_UNESCAPED_UNICODE));
            }
        }
        //年卡管理操作记录
        $optMember = $loginInfo['memberID'] ?? $loginInfo['sid'];
        $cardManageService = new \Business\AnnualCard\AnnualCardManage();
        $cardManageService->addCardManageRecordWithCardInfo(AnnualCardConst::ANNUAL_OPT_FACE_SMS, [
            'opt_sid' => $loginInfo['sid'],
            'opt_member' => $optMember
        ], $card);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '发送通知成功');
        }
        $this->apiReturn(400, [], '发送通知失败');
    }

    /**
     * 获取绑定年卡人脸的URL地址，用来生成二维码
     * url:my.12301.cc/r/product_AnnualCardPhoto/getQrCode
     * @author: Guangpeng Chen
     * @date: 2023/5/15
     */
    public function getQrCode()
    {
        $loginInfo  = $this->getLoginInfo('ajax');
        $token      = $this->hashId($loginInfo['sid'], 'encode');
        if (ENV=='PRODUCTION') {
            $prefix = 'https://wx.12301.cc';
        } elseif (ENV=='TEST') {
            $prefix = 'http://wx.12301dev.com';
        } else {
            $prefix = 'http://wx.12301.test';
        }
        $url        = "{$prefix}/c/annual_face.html?token={$token}";
        $this->apiReturn(200, ['qr_url'=>$url], 'success');
    }
    /**
     * 根据手机号检测用户是否绑定年卡
     *
     * @author: guanpeng
     * @date: 2019/1/18
     */
    public function getCardInfoByMobile()
    {
        $mobile = I('post.mobile');
        $sid    = I('post.sid', 0, 'intval');
        if (!$mobile || !$sid) {
            $this->apiReturn(400, [], '缺少必要参数');
        }

        $output = $this->getMemberCardInfo($sid, $mobile, '', true);
        $this->apiReturn(200, $output, 'success');
    }
}