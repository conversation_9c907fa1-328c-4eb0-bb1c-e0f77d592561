<?php

namespace Controller\product;

use Library\Controller;

class EvoluteGroupUser extends Controller
{
    private $_sid      = null;
    private $_memberId = null;

    public function __construct()
    {
        $loginInfoArr    = $this->getLoginInfo();
        $this->_sid      = $loginInfoArr['sid'];
        $this->_memberId = $loginInfoArr['memberID'];
    }

    /**
     * 移动分销分组中成员    http://my.12301.local/r/product_EvoluteGroupUser/moveFidToNewEvoluteGroup
     * @param string fids 要移动的分销商id   分隔符 , 分隔
     * @param int source_group_id 原在分组id
     * @param int to_group_id 要到分组id
     * @return string | json
     *
     */
    public function moveFidToNewEvoluteGroup()
    {
        // 需要支持批量移动
        $fids          = I('post.fids', '', 'strval');
        $sourceGroupId = I('post.source_group_id', 0, 'intval');
        $toGroupId     = I('post.to_group_id', 0, 'intval');

        if (empty($fids) || empty($sourceGroupId) || empty($toGroupId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $fidArr = explode(',', $fids);

        $upEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $moveRes = $upEvoluteGroupBiz->moveDistributorGroupNew($this->_sid, $fidArr, $sourceGroupId, $toGroupId, $this->_memberId);

        $this->apiReturn($moveRes['code'], [], $moveRes['msg']);
    }

    /**
     * 移动分销分组中所有成员   http://my.12301.local/r/product_EvoluteGroupUser/moveAllGroupUser
     * @param int source_group_id 原在分组id
     * @param int to_group_id 要到分组id
     * @return string | json
     *
     */
    public function moveAllGroupUser()
    {
        $sourceGroupId = I('post.source_group_id', 0, 'intval');
        $toGroupId     = I('post.to_group_id', 0, 'intval');

        if (empty($sourceGroupId) || empty($toGroupId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $upEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $moveRes = $upEvoluteGroupBiz->moveGroupAllDistributorNew($this->_sid, $sourceGroupId, $toGroupId, $this->_memberId);

        $this->apiReturn($moveRes['code'], [], $moveRes['msg']);
    }

    /**
     * 分销分组中删除分销商    http://my.12301.local/r/product_EvoluteGroupUser/delFidGFromEvoluteGroup
     * @param int fid 分销商id
     * @return string | json
     *
     */
    public function delFidGFromEvoluteGroup()
    {
        $fid = I('post.fid', 0, 'intval');

        if (empty($fid)) {
            $this->apiReturn(203, [], '参数错误');
        }

        // 1.断开分销链之前先清理下级资源中心数据
        //$resourceCenterBiz = new \Business\ResourceCenter\ResourceCenter();
        //$re                = $resourceCenterBiz->delDisRCData($this->_sid, $fid);

        //2.把用户从新版分组中删除该用户
        //$upEvoluteGroupUserBiz = new \Business\Product\Update\EvoluteGroupUser();
        //$delRes                = $upEvoluteGroupUserBiz->deleteDistributorForEvouteGroup($this->_sid, $fid, $this->_sid);
        //if ($delRes['code'] != 200) {
        //    $this->apiReturn($delRes['code'], [], $delRes['msg']);
        //}

        // 3.断开用户之间的关联关系
        $relationApi = new \Business\NewJavaApi\Member\MemberRelation();
        $res         = $relationApi->breakSupplyShip($this->_sid, $fid, $this->_memberId);
        //$relationApi = new \Business\JavaApi\Member\MemberRelation();
        //$res         = $relationApi->breakRelation($this->_sid, $fid, 0);

        if ($res['code'] == 200) {
            // 4.添加操作记录
            $RelationModel = new \Model\Member\MemberRelationship;
            $RelationModel->partnerChangeRecord($this->_sid, $fid, 1);

            // <AUTHOR> | @date 2018/11/09 | 会员关系取消时候 重置授信预警表
            $jobData = [
                'action' => 'cancel',
                'params' => [
                    'parent_id' => $this->_sid,
                    'son_id'    => $fid,
                ],
            ];
            //插入队列
            $jobId = \Library\Resque\Queue::push('member_system', 'MemberRelation_Job', $jobData);
            //记录日志
            pft_log('/balanceWarn/debug', json_encode(['MemberRelation_Job', $jobId, $jobData]));

            // 5.清除给分销商配置的分销库存
            $storageModel = new \Model\Product\YXStorage();
            $storageModel->removeReseller($this->_sid, $fid);
        }

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 批量删除分销商
     * <AUTHOR> lingfeng
     * 2022/5/23 19:13
     */
    public function batchDeleteDistributor()
    {
        $fidAndGroupIdArr = I('post.fidAndGroupIdList', []);
        if (empty($fidAndGroupIdArr)){
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        $upEvoluteGroupUserBiz = new \Business\Product\Update\EvoluteGroupUser();
        $delRes = $upEvoluteGroupUserBiz->batchDeleteDistributor($this->_sid, $fidAndGroupIdArr, $this->_sid);

        $this->apiReturn($delRes['code'], $delRes['data'], $delRes['msg']);
    }

    /**
     * 分页查询组内成员
     * /r/product_EvoluteGroupUser/groupUserList
     * 
     * @param group_id 新分组id
     * @param page_num 页码
     * @param page_size 页数
     * 
     */
    public function groupUserList()
    {
        $groupId  = I('post.group_id', 0, 'intval');
        $pageNum  = I('post.page_num', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');
        if (empty($groupId)) {
            $this->apiReturn(203, [], '参数错误'); 
        }

        $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
        $getResArr          = $getEvoluteGroupBiz->queryGroupDistributorListByPage($this->_sid, $groupId, $pageNum, $pageSize);
        if ($getResArr['code'] != 200 || empty($getResArr['data']['list'])) {
            $this->apiReturn(203, [], '无数据');  
        }

        $fidArrs = array_column($getResArr['data']['list'], 'fid');

        $groupMemberInfoArr = [];
        if (!empty($fidArrs)) {
            $memberQueryApi = new \Business\JavaApi\Member\MemberQuery();
            $memberInfoArr  = $memberQueryApi->queryMemberByMemberQueryInfo(['idList' => $fidArrs]);
            if ($memberInfoArr['code'] == 200) {
                foreach ($memberInfoArr['data'] as $item) {
                    $groupMemberInfoArr[] = [
                        'account' => $item['memberInfo']['account'],
                        'cname'   => $item['memberInfo']['cname'], 
                        'dname'   => $item['memberInfo']['dname'], 
                    ];
                }
            }
        }

        $this->apiReturn(200, ['list' => $groupMemberInfoArr], 'success');
    }
}