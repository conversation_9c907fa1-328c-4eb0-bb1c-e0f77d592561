<?php
/**
 * 产品预定列表
 * Created by PhpStorm.
 * User: luo<PERSON><PERSON>n
 * Date: 2017/9/19 0019
 * Time: 9:25
 */

namespace Controller\Product;

use Business\Authority\DataAuthLogic;
use Business\JavaApi\Product\EvoluteListQuery;
use Business\JavaApi\Ticket\TicketPrice;
use Business\JavaApi\TicketApi;
use Business\MemberLogin\MemberLoginHelper;
use Business\Product\Hotel;
use Business\Product\ProductList as businessLib;
use Library\Controller;
use Model\Product\Land;
use Process\Product\ProductList as processLib;

class ProductList extends Controller
{
    private $_memberId;
    private $_sid;
    private $_type;

    public function __construct()
    {
        $loginInfo       = $this->getLoginInfo('ajax');
        $this->_memberId = $loginInfo['memberID'];
        $this->_sid      = $loginInfo['sid'];
        $this->_type     = $loginInfo['dtype'];
    }

    /**
     * 获取预定列表
     *
     * @date   2017-09-19
     * <AUTHOR>
     *
     * @return string
     */
    public function getList()
    {
        $params = processLib::getParamsOfPlatform();

        if ($params[0] !== 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }
        $params = $params[1];
        $page   = array_shift($params);

        if ($this->_type == 6) {
            if ($params['superior_id']) {
                $params['superior_id'] = $this->_sid;
            } else {
                $params['account_id'] = $this->_sid;
            }
        }
        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }
        //如果登录的是资方  仅展示资方对应景点的产品
        $params['item_id'] = '';
        if ($this->_type == 2) {
            //$land              = new Land();
            //$land_id_arr       = $land->getLandInfoBySalerId($this->_loginMember['account'], false, 'id');

            $landApi           = new \Business\CommodityCenter\Land();
            $land_id_arr       = $landApi->queryLandMultiQueryBySalerid([$this->_loginMember['account']])[0];
            $params['item_id'] = $land_id_arr['id'];
        }

        $checkRes = businessLib::auth($params['superior_id'], $this->_sid, $params['account_id']);
        if ($checkRes[0] !== 0) {
            $this->apiReturn($checkRes[0], [], $checkRes[1]);
        }

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition($params);
        // 全禁的情况下不需要调用接口
        if ($condition === false) {
            $this->apiReturn(200, ['lands' => [], 'currentPage' => $page], 'success');
        }

        if ($params['superior_id']) {
            $params['shop'] = 12;
        }

        //判断是否是独立域名
        $isDomain = false;

        $params['domain_id'] = $this->getSuperiorIdByAcount();
        if ($params['domain_id']) {
            //获取独立域名用户配置
            $configure = load_config('member_list', 'domain_member');
            if (in_array($params['domain_id'], $configure)) {
                $isDomain = true;
            }
        }

        //供应商名称
        $searchSupplier = I('post.supplier_name', '', 'strval');
        //是独立域名
        if ($isDomain) {
            switch ($params['shop']) {
                //计调下单
                case 12:
                    $result = businessLib::getTransferOrdersList(
                        $params['domain_id'],
                        $params['superior_id'],
                        $params['account_id'],
                        $params['province'],
                        $params['city'],
                        $params['title'],
                        $params['type'],
                        $params['topic'],
                        $params['rating'],
                        $params['pagerno'],
                        $params['pagerrows'],
                        $params['item_id'],
                        '',
                        $params['subType'],
                        $condition
                    );
                    break;
                default:
                    $result = businessLib::getDomainList(
                        $params['domain_id'],
                        $params['account_id'],
                        $params['pagerno'],
                        $params['pagerrows'],
                        $params['shop'],
                        $params['title'],
                        $params['type'],
                        $params['rating'],
                        $params['topic'],
                        $params['province'],
                        $params['city'],
                        $params['item_id'],
                        $params['oversea'],
                        $params['supplier_name'],
                        $params['source_id'],
                        null,
                        null,
                        null,
                        $params['subType'],
                        $condition
                    );
            }
        } else {
            // <AUTHOR>
            // @date 2018/06/19
            //非独立域名
            switch ($params['shop']) {
                //计调下单
                case 12:
                    $result = businessLib::getPlanOrderList(
                        $params['superior_id'],
                        $params['account_id'],
                        $params['pagerno'],
                        $params['pagerrows'],
                        $params['title'],
                        $params['type'],
                        $params['rating'],
                        $params['topic'],
                        $params['province'],
                        $params['city'],
                        $params['oversea'],
                        $params['landId'],
                        $params['subType'],
                        $condition
                    );
                    break;
                default:
                    $result = businessLib::getListV2(
                        $params['account_id'],
                        $params['pagerno'],
                        $params['pagerrows'],
                        $params['shop'],
                        $params['title'],
                        $params['type'],
                        $params['rating'],
                        $params['topic'],
                        $params['province'],
                        $params['city'],
                        $params['oversea'],
                        $searchSupplier,
                        $params['source_id'],
                        $params['landId'],
                        null,
                        null,
                        null,
                        $params['subType'],
                        $subSid,
                        $condition
                    );
            }
        }

        if (empty($result)) {
            $this->apiReturn(201, $result, 'fail');
        }

        //pc平台预定页更多 添加 是否设置了不同价格 票说明，提前预订，延迟验证
        $productBuz      = new \Business\Product\Product();
        $result['lands'] = $productBuz->handleReturnConfigMuchPriceList($result['lands']);

        //酒店添加房型参数
        $hotelLib        = new Hotel();
        $result['lands'] = $hotelLib->handleRoomParamsBookList($result['lands']);

        //资源中心禁售ota标签
        $resourceCenterProductBiz = new \Business\ResourceCenter\Product();  ///handleBookListParams
        $result['lands'] = $resourceCenterProductBiz->handleBookListParams($result['lands']);

        $result['currentPage'] = $page;
        $this->apiReturn(200, $result, 'success');
    }

    /**
     * 下拉获取门票信息
     *
     * @date   2017-09-20
     * <AUTHOR> Lan
     *
     * @return array
     */
    public function getTickets()
    {
        $params = processLib::getTickets();

        if ($params[0] !== 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $params = $params[1];

        if ($this->_type == 6) {
            if ($params['superior_id']) {
                //如果是员工账号作为分销商自己查看
                if ($params['account_id'] == $this->_memberId) {
                    $params['account_id'] = $this->_sid;
                }

                //员工账号作为供应商-计调下单
                if ($params['superior_id'] == $this->_memberId) {
                    $params['superior_id'] = $this->_sid;
                }
            } else {
                $params['account_id'] = $this->_sid;
            }
        }

        $checkRes = businessLib::auth($params['superior_id'], $this->_sid, $params['account_id'],
            $params['product_ids']);
        if ($checkRes[0] !== 0) {
            $this->apiReturn($checkRes[0], [], $checkRes[1]);
        }

        //$params['operator_id'] = $this->_memberId;
        //计调下单时，拆分接口
        if (isset($params['did']) && !empty($params['did'])) {
            $result = businessLib::getTransferOrderTickets($params['superior_id'], $params['account_id'],
                $params['superior_id'], $params['ticket_ids']);
        } else {
            $result = businessLib::getMoreTicket($params['account_id'], $params['superior_id'], $params['ticket_ids']);
        }

        if ($result === false) {
            $this->apiReturn(201, $result, 'fail');
        }
        //pc平台预定页更多 添加 是否设置了不同价格 票说明，提前预订，延迟验证
        $productBuz = new \Business\Product\Product();
        $result     = $productBuz->handleReturnConfigMuchPriceListMore($result);

        //酒店添加房型参数
        $hotelLib = new Hotel();
        $result   = $hotelLib->handleRoomParamsBookListMore($result);

        $this->apiReturn(200, $result, 'success');
    }

    /**
     * 刷新门票价格
     *
     * @date   2017-09-20
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function refreshPrice()
    {
        $params = processLib::getRefreshParams();

        if ($params[0] !== 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        $params = $params[1];

        if ($this->_type == 6) {
            if ($params['superior_id']) {
                //员工账号作为供应商-计调下单
                if ($params['superior_id'] == $this->_memberId) {
                    $params['superior_id'] = $this->_sid;
                }
                //如果是员工账号自己查看
                if ($params['account_id'] == $this->_memberId) {
                    $params['account_id'] = $this->_sid;
                }
            } else {
                $params['account_id'] = $this->_sid;
            }
        }

        $checkRes = businessLib::auth($params['superior_id'], $this->_sid, $params['account_id'],
            $params['product_id']);
        if ($checkRes[0] !== 0) {
            $this->apiReturn($checkRes[0], [], $checkRes[1]);
        }

        $result = businessLib::refreshPrice($params['account_id'], $params['superior_id'], $params['ticket_id']);

        if (empty($result)) {
            $this->apiReturn(201, $result, 'fail');
        }
        $this->apiReturn(200, $result, 'success');
    }

    /**
     * 获取自供应分销价格配置产品列表
     * <AUTHOR>
     * @date   2019-01-28
     */
    public function getSelfProPriceList()
    {
        $landId             = I('post.landId', 0, 'intval');                //景区Id
        $groupId            = I('post.groupId', 0, 'intval');               //分组Id
        $distributorId      = I('post.distributorId', 0, 'intval');         //分销商Id
        $pageNum            = I('post.pageNum', 1, 'intval');               //当前页
        $pageSize           = I('post.pageSize', 5, 'intval');             //每页显示大小
        $productType        = I('post.productType', '', 'strval');          //产品类型
        $ticketId           = I('post.ticketId', 0, 'intval');              //票类Id
        $distributionActive = I('post.distributionActive', 2, 'intval');    //分销开启状态 1：开启，0：未开启 2全部
        if (!$groupId && !$distributorId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分组Id或者分销商Id必须要有一个');
        }

        $pageNum  = max(1, $pageNum);
        $pageSize = min(30, max(1, $pageSize));

        $ProListBus = new \Business\Product\ProListPrice();
//        $result     = $ProListBus->getSelfProPriceListBus($this->_sid, $landId, $groupId, $distributorId, $pageNum,
//            $pageSize, $productType, $ticketId, $distributionActive);
        $result = $ProListBus->getSelfOrDistributeProPriceListBusNew(1, $this->_sid, $landId, $groupId,
            $this->_memberId, $distributorId, $pageNum, $pageSize, $productType, $ticketId, $distributionActive);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取自供应分销价格配置产品列表
     * <AUTHOR>
     * @date   2020-03-29
     */
    public function getSelfProPriceListNew()
    {
        $landId             = I('post.landId', 0, 'intval');                //景区Id
        $groupId            = I('post.groupId', 0, 'intval');               //分组Id
        $distributorId      = I('post.distributorId', 0, 'intval');         //分销商Id
        $pageNum            = I('post.pageNum', 1, 'intval');               //当前页
        $pageSize           = I('post.pageSize', 5, 'intval');             //每页显示大小
        $productType        = I('post.productType', '', 'strval');          //产品类型
        $pid                = I('post.pid', 0, 'intval');                   //票类pid
        $distributionActive = I('post.distributionActive', 2, 'intval');    //分销开启状态 1：开启，0：未开启 2全部
        if (!$groupId && !$distributorId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分组Id或者分销商Id必须要有一个');
        }

        $pageNum  = max(1, $pageNum);
        $pageSize = min(30, max(1, $pageSize));

        // 新接口 -1 才是全部
        if ($distributionActive == 2) {
            $distributionActive = -1;
        }

        $evoluteQueryHandleBiz = new \Business\Product\Get\EvoluteQueryHandle();
        $result                = $evoluteQueryHandleBiz->getSelfLandList($this->_sid, $groupId, $productType, $pid,
            $landId, $distributionActive, $pageNum, $pageSize);

        $name = '';
        if ($groupId) {
            //分组是否存在
            $groupBiz        = new \Business\Product\Update\EvoluteGroup();
            $originGroupInfo = $groupBiz->queryByGroupId($groupId);
            if ($originGroupInfo['code'] == 200 && !empty($originGroupInfo['data'])) {
                $name = $originGroupInfo['data']['groupName'];
            }
        } else {
            $MemberBus  = new \Business\Member\Member();
            $memberInfo = $MemberBus->getInfo($distributorId);
            if (!empty($memberInfo)) {
                $name = $memberInfo['dname'];
            }
        }

        $returnListArr = [
            'list'             => [],
            'totalCount'       => $result['data']['total'],
            'multi_auth'       => false,
            'multi_auth_allow' => false,
            'name'             => $name,
        ];
        $listArr       = [];
        if (!empty($result['data']['list'])) {
            foreach ($result['data']['list'] as $item) {
                $listArr[$item['landId']]['lid']       = $item['landId'];
                $listArr[$item['landId']]['p_name']    = $item['landName'];
                $listArr[$item['landId']]['tickets'][] = [
                    'supply_price'    => sprintf("%01.2f", $item['ticketActualTimePriceDTO']['costPrice'] / 100),
                    'allow'           => $item['active'] ? true : false,
                    'lid'             => $item['landId'],
                    'p_name'          => $item['landName'],
                    'dprice'          => 0,
                    'pid'             => $item['productId'],
                    'ticket_name'     => $item['ticketName'],
                    'sid'             => '',
                    'aids'            => '',
                    'apply_did'       => $item['supplierId'],
                    'recom_price'     => sprintf("%01.2f", $item['ticketActualTimePriceDTO']['retailPrice'] / 100),
                    'settle_price'    => sprintf("%01.2f", $item['ticketActualTimePriceDTO']['settlementPrice'] / 100),
                    'is_top_confirm'  => false,
                    'use_multi_right' => false,
                ];
            }
            $returnListArr['list'] = array_values($listArr);
        }

        $this->apiReturn($result['code'], $returnListArr, $result['msg']);
    }

    /**
     * 获取转分销分销价格配置产品列表
     * <AUTHOR>
     * @date   2018-11-27
     */
    public function getTransfreProPriceList()
    {
        $landId        = I('post.landId', 0, 'intval');                //景区Id
        $groupId       = I('post.groupId', 0, 'intval');               //分组Id
        $distributorId = I('post.distributorId', 0, 'intval');          //分销商Id
        $supplierId    = I('post.supplierId', 0, 'intval');             //供应商Id
        $pageNum       = I('post.pageNum', 1, 'intval');               //当前页
        $pageSize      = I('post.pageSize', 10, 'intval');             //每页显示大小
        $productType   = I('post.productType', '', 'strval');          //产品类型
        $ticketId      = I('post.ticketId', 0, 'intval');             //票类Id
//        $distributionActive = I('post.distributionActive', 2, 'intval');    //分销开启状态 1：开启，0：未开启
        if (!$groupId && !$distributorId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分组Id或者分销商Id必须要有一个');
        }
        $pageNum  = max(1, $pageNum);
        $pageSize = min(30, max(1, $pageSize));

        $ProListBus = new \Business\Product\ProListPrice();
//        $result     = $ProListBus->getTransfreProPriceListBus($this->_sid, $landId, $groupId, $distributorId,
//            $supplierId, $pageNum, $pageSize, $productType, $ticketId);
        $result = $ProListBus->getSelfOrDistributeProPriceListBusNew(0, $this->_sid, $landId, $groupId,
            $this->_memberId, $distributorId, $pageNum,
            $pageSize, $productType, $ticketId, 2, $supplierId);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取转分销分销价格配置产品列表
     * <AUTHOR>
     * @date   2020-03-29
     */
    public function getTransfreProPriceListNew()
    {
        $landId        = I('post.landId', 0, 'intval');                //景区Id
        $groupId       = I('post.groupId', 0, 'intval');               //分组Id
        $distributorId = I('post.distributorId', 0, 'intval');          //分销商Id
        $supplierId    = I('post.supplierId', 0, 'intval');             //供应商Id
        $pageNum       = I('post.pageNum', 1, 'intval');               //当前页
        $pageSize      = I('post.pageSize', 10, 'intval');             //每页显示大小
        $productType   = I('post.productType', '', 'strval');          //产品类型
        $pid           = I('post.pid', 0, 'intval');                   //票类pid
//        $distributionActive = I('post.distributionActive', 2, 'intval');    //分销开启状态 1：开启，0：未开启
        if (!$groupId && !$distributorId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分组Id或者分销商Id必须要有一个');
        }
        $pageNum  = max(1, $pageNum);
        $pageSize = min(30, max(1, $pageSize));

        $evoluteQueryHandleBiz = new \Business\Product\Get\EvoluteQueryHandle();
        $result                = $evoluteQueryHandleBiz->getDistributionLandList($supplierId, $this->_sid, $groupId,
            $productType, $pid, $landId, -1, $pageNum, $pageSize);

        if ($groupId) {
            //分组是否存在
            $groupBiz        = new \Business\Product\Update\EvoluteGroup();
            $originGroupInfo = $groupBiz->queryByGroupId($groupId);
            if ($originGroupInfo['code'] == 200 && !empty($originGroupInfo['data'])) {
                $name = $originGroupInfo['data']['groupName'];
            }

        } else {
            $MemberBus  = new \Business\Member\Member();
            $memberInfo = $MemberBus->getInfo($distributorId);
            if (!empty($memberInfo)) {
                $name = $memberInfo['dname'];
            }
        }

        $returnListArr = [
            'list'             => [],
            'totalCount'       => $result['data']['total'],
            'multi_auth'       => false,
            'multi_auth_allow' => false,
            'name'             => $name,
        ];
        $listArr       = [];
        if (!empty($result['data']['list'])) {
            foreach ($result['data']['list'] as $item) {
                $listArr[$item['landId']]['lid']       = $item['landId'];
                $listArr[$item['landId']]['p_name']    = $item['landName'];
                $listArr[$item['landId']]['tickets'][] = [
                    'supply_price'    => sprintf("%01.2f", $item['ticketActualTimePriceDTO']['costPrice'] / 100),
                    'allow'           => $item['active'] ? true : false,
                    'lid'             => $item['landId'],
                    'p_name'          => $item['landName'],
                    'dprice'          => 0,
                    'pid'             => $item['productId'],
                    'ticket_name'     => $item['ticketName'],
                    'sid'             => $item['supplierId'],
                    'aids'            => $item['groupIds'],
                    'apply_did'       => $item['sourceid'],
                    'recom_price'     => sprintf("%01.2f", $item['ticketActualTimePriceDTO']['retailPrice'] / 100),
                    'settle_price'    => sprintf("%01.2f", $item['ticketActualTimePriceDTO']['settlementPrice'] / 100),
                    'is_top_confirm'  => false,
                    'use_multi_right' => false,
                    'dname'           => $item['supplierName'],
                ];
            }
            $returnListArr['list'] = array_values($listArr);
        }

        $this->apiReturn($result['code'], $returnListArr, $result['msg']);
    }

    /**
     * 配置分终端验证时限产品列表
     * <AUTHOR>
     * @date   2020-10-15
     */
    public function queryTermainalLandList()
    {
        $landName = I('post.land_name', '', 'strval,trim');                //景区Id
        $pageNum  = I('post.page', 1, 'intval');               //当前页
        $pageSize = I('post.size', 10, 'intval');             //每页显示大小

        $ProListBus = new \Business\Product\ProductZgyList();
        $result     = $ProListBus->queryTermainalLandList($this->_sid, $landName, $pageNum, $pageSize);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 更新配置分终端验证时限产品分终端信息
     * <AUTHOR>
     * @date   2020-10-15
     */
    public function updateTicketTermainalInfo()
    {
        //int $fid,array $tids,int $ope, int $status = -1, int $spaceTime = null, array $jumpTerminal = null, string $errorMark = null
        $tids         = I('post.tids');//票id 数组
        $status       = I('post.status', -1, 'intval'); //状态 1开启 0关闭
        $spaceTime    = I('post.space_time', null, 'intval'); //验证间隔时间
        $jumpTerminal = I('post.jump_terminal', null);//不受验证时间间隔限制终端号 数组
        $errorMark    = I('post.error_mark', null, 'strval');//验证时间间隔内的异常文字/语音提示

        if (!$tids || !in_array($status, [0, 1])) {
            $this->apiReturn(204, [], '参数错误');
        }
        $ProListBus = new \Business\Product\ProductZgyList();
        $result     = $ProListBus->updateTicketTermainalInfo($this->_sid, $tids, $this->_memberId, $status, $spaceTime,
            $jumpTerminal, $errorMark);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取在售产品景区列表
     * <AUTHOR>
     * @date   2021-03-24
     */
    public function queryLandDetailList()
    {
        $landType   = I('post.land_type', "", 'strval,trim');
        $type       = I('post.type', 1, 'intval');//0 全部 1自供应 2转分销
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');
        $landName   = I('post.land_name', "", 'strval,trim');
        $ProListBus = new EvoluteListQuery();
        if (!in_array($type, [0, 1, 2])) {
            $this->apiReturn(203, [], '参数错误');
        }

        if (!$landType) {
            $landType = [];
        } else {
            $landType = explode(',', $landType);
        }
        $result = $ProListBus->queryLandDetailList($this->_sid, $landName, $type, $landType, $page, $size);
        $this->apiReturn(200, $result, '成功');
    }

}
