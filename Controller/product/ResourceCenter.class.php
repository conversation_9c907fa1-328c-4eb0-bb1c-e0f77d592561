<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 2018/11/30
 * Time: 16:57
 */

namespace Controller\Product;

use Business\AppCenter\Package;
use Business\JavaApi\Product\ResourceCenterApi;
use Business\Product\MultiLevelPrice;
use Library\Controller;
use Business\ResourceCenter\ResourceCenter as ResourceCenterBiz;
use Model\Product\Ticket;
use Library\Cache\Cache;

class ResourceCenter extends Controller
{
    private $_sid;
    private $_memberid;
    private $_dtype;
    private $_sdtype;
    private $_qx;
    private $_loginInfo;
    private $_rcBizApi;
    private $_rcBiz;
    private $_ticModel;
    protected $cache;

    public function __construct()
    {
        parent::__construct();
        $this->_loginInfo       = $this->getLoginInfo();
        $this->_sid      = $this->_loginInfo['sid'];
        $this->_memberid = $this->_loginInfo['memberID'];
        $this->_dtype    = $this->_loginInfo['dtype'];
        $this->_sdtype   = $this->_loginInfo['sdtype'];
        $this->_qx       = $this->_loginInfo['qx'];

        $this->_rcBizApi = new ResourceCenterApi();
        $this->_rcBiz    = new ResourceCenterBiz();
    }

    protected function cacheInstance()
    {
        if (empty($this->cache)) {
            $this->cache = Cache::getInstance('redis');
        }

        return $this->cache;
    }

    /***
     * 供应产品到资源中心
     * @author: Cai Yiqiang
     * @date: 2019/1/4
     */
    public function saveProduct()
    {
        $pid   = I('post.pid', 0, 'intval');
        $price = I('post.price', 0, 'floatval');

        if (!$pid) {
            $this->apiReturn(204, [], '参数错误');
        }

        //判断是否有开通资源中心的权限
        $isOpenResourceCenter = $this->_rcBiz->hasOpenedResourceCenter($this->_sid, $this->_dtype, $this->_qx);
        if (!$isOpenResourceCenter) {
            pft_log('ResourceCenter', json_encode(['没有开通资源中心', $this->_memberid]));
            $this->apiReturn(204, [], '没有开通资源中心');
        }

        $ticModel = $this->_getTicketModel();
        $ticInfo  = $ticModel->getTicketInfoByPid($pid, 'id,landid');
        $tid      = $ticInfo['id'];
        $lid      = $ticInfo['landid'];

        $diffPrice = $price * 100;

        $result = $this->_rcBizApi->supplyProductToResourceCenter($this->_sid, $pid, $tid, $lid, $this->_memberid,
            $diffPrice, $this->_sdtype);

        if (isset($result['data']) && $result['data'] == true) {
            $this->apiReturn(200, [], '保存成功');
        } else {
            $this->apiReturn(201, [], (isset($result['msg']) ? $result['msg'] : '保存失败'));
        }

    }

    /***
     * 合作采购
     * @author: Cai Yiqiang
     * @date: 2019/1/15
     */
    public function purchaseProduct()
    {
        $pid = I('post.pid', 0, 'intval');
        $aid = I('post.sid', 0, 'intval');
        if ($aid == $this->_memberid) {
            return $this->apiReturn(422, [], '您不能采购自己的产品');
        }
        $ticModel = $this->_getTicketModel();
        $ticInfo  = $ticModel->getTicketInfoByPid($pid, 'id');
        $tid      = $ticInfo['id'];

        //保存资源合作关系
        $shipRe = $this->_rcBiz->saveResourceCenterShip($aid, $this->_sid);
        if (false === $shipRe) {
            $this->apiReturn(201, [], '该产品供应商为您的平台直供供应商，联系供应商采购');
        }

        $result = $this->_rcBizApi->partnerPurchase($this->_sid, $aid, $tid, $pid, $this->_memberid, $this->_sdtype);

        if ($result) {

            if ($result['reason']) {
                $this->apiReturn(201, [], $result['reason']);
            }

            $return = [
                'pid'            => $result['pid'],
                'tid'            => $result['ticketId'],
                'price_diff'     => $result['priceSpread'] / 100,
                'price_dis_diff' => $result['priceSpreadToOthers'] / 100,
                'cost_price'     => $result['costPrice'] / 100,
                'reason'         => $result['reason'],
                'status'         => $result['status'],
            ];
            $this->apiReturn(200, $return, 'success');
        }

        $this->apiReturn(201, [], '服务错误');

    }

    /***
     * 获取采购的合作分销商列表信息
     * @author: Cai Yiqiang
     * @date: 2019/1/2
     */
    public function getDistributorSaleList()
    {
        $page     = I('post.page', 1, 'intval');
        $size     = I('post.size', 10, 'intval');
        $keyword  = I('post.keyword', '', 'strval');
        $province = I('post.province', 0, 'intval');
        $city     = I('post.city', 0, 'intval');

        $filters['city']     = $city;
        $filters['province'] = $province;
        if ($keyword) {
            if (mb_strlen($keyword) == strlen($keyword)) {
                //手机号或者账号
                if (isMobile($keyword)) {
                    $filters['mobile'] = $keyword;
                } else {
                    $filters['account'] = $keyword;
                }
            } else {
                //分销商名称或联系人名称
                $filters['cname'] = $keyword;
                $filters['dname'] = $keyword;
            }
        }

        $result = $this->_rcBizApi->getDistributorList($this->_sid, $page, $size, $filters);

        $return['list'] = [];
        if ($result) {
            if ($result['resultList']) {
                foreach ($result['resultList'] as $item) {
                    $return['list'][] = [
                        'province_name' => $item['provinceName'],
                        'city_name'     => $item['cityName'],
                        'mobile'        => $item['mobile'],
                        'dname'         => $item['dname'],
                        'linkman'       => $item['cname'],
                        'comName'       => $item['comName'],
                        'recent_sale'   => $item['saleroomRecent'],
                        'sid'           => $item['sid'],
                        'fid'           => $item['fid'],
                        'hotline'       => $item['hotline'] ?? '',
                    ];
                }
            }
            $return['total'] = $result['recordtotal'];
        }

        $this->apiReturn(200, $return, 'success');
    }

    /***
     * 获取合作供应商列表信息
     * @author: Cai Yiqiang
     * @date: 2019/1/2
     */
    public function getSupplierList()
    {
        $page     = I('post.page', 1, 'intval');
        $size     = I('post.page_size', 10, 'intval');
        $keyword  = I('post.keyword', '', 'strval');
        $province = I('post.province', 0, 'intval');
        $city     = I('post.city', 0, 'intval');

        $filters['city']     = $city;
        $filters['province'] = $province;
        if ($keyword) {
            if (mb_strlen($keyword) == strlen($keyword)) {
                //手机号或者账号
                if (isMobile($keyword)) {
                    $filters['mobile'] = $keyword;
                } else {
                    $filters['account'] = $keyword;
                }
            } else {
                //分销商名称或联系人名称
                $filters['cname'] = $keyword;
                $filters['dname'] = $keyword;
            }
        }

        $result = $this->_rcBizApi->getSupplierList($this->_sid, $page, $size, $filters);

        $return['list'] = [];
        if ($result['resultList']) {
            foreach ($result['resultList'] as $item) {
                $return['list'][] = [
                    'province_name'       => $item['provinceName'],
                    'city_name'           => $item['cityName'],
                    'mobile'              => $item['mobile'],
                    'dname'               => $item['dname'],
                    'linkman'             => $item['cname'],
                    'comName'             => $item['comName'],
                    'sid'                 => $item['sid'],
                    'product_num'         => $item['productNum'],
                    'ticket_num'          => $item['ticketNum'],
                    'purchase_ticket_num' => $item['purchaseTicketNum'],
                    'hotline'             => $item['hotline'] ?? '',
                ];
            }
            $return['total'] = $result['recordtotal'];
        }

        $this->apiReturn(200, $return, 'success');
    }

    /***
     * 获取自供应的资源产品列表
     * @author: Cai Yiqiang
     * @date: 2019/1/3
     */
    public function getSelfProducts()
    {
        $page   = I('post.page', 1, 'intval');
        $size   = I('post.size', 10, 'intval');
        $type   = I('post.type', '', 'strval');
        $landId = I('post.landid', 0, 'intval');

        $result = $this->_rcBizApi->getSelfProducts($this->_sid, $page, $size, $type, $landId);

        $return['list'] = [];
        if ($result) {
            if ($result['ticketList']) {
                foreach ($result['ticketList'] as $item) {
                    $return['list'][] = [
                        'cost_price'    => $item['costPrice'] / 100,
                        'retail_price'  => $item['retailPrice'] / 100,
                        'settle_price'  => $item['settlement'] / 100,
                        'land_name'     => $item['landName'],
                        'ticket_name'   => $item['ticketName'],
                        'supplier_name' => $item['supplierName'],
                        'tid'           => $item['ticketId'],
                        'pid'           => $item['productId'],
                        'lid'           => $item['lid'],
                        'is_support'    => $item['supportDistribution'],
                        'status'        => $item['status'],
                    ];
                }
            }
            $return['total'] = $result['totalCount'];
        }

        $this->apiReturn(200, $return, 'success');
    }

    /***
     * 获取分销的资源产品列表-目前一级分销产品
     * @author: Cai Yiqiang
     * @date: 2019/1/3
     */
    public function getDisProducts()
    {
        $page   = I('post.page', 1, 'intval');
        $size   = I('post.size', 10, 'intval');
        $type   = I('post.type', '', 'strval');
        $landId = I('post.landid', 0, 'intval');
        $aid    = I('post.aid', 0, 'intval');

        $result = $this->_rcBizApi->getDisFilterProducts($this->_sid, $page, $size, $type, $landId, $aid);

        $return['list'] = [];
        if ($result) {
            if ($result['ticketList']) {
                foreach ($result['ticketList'] as $item) {
                    $return['list'][] = [
                        'cost_price'    => $item['costPrice'] / 100,
                        'retail_price'  => $item['retailPrice'] / 100,
                        'settle_price'  => $item['settlement'] / 100,
                        'land_name'     => $item['landName'],
                        'ticket_name'   => $item['ticketName'],
                        'supplier_name' => $item['supplierName'],
                        'tid'           => $item['ticketId'],
                        'pid'           => $item['productId'],
                        'lid'           => $item['settlement'],
                        'is_support'    => $item['supportDistribution'],
                        'status'        => $item['status'],
                    ];
                }
            }
            $return['total'] = $result['totalCount'];
        }

        $this->apiReturn(200, $return, 'success');
    }

    /***
     * 取消采购
     * @author: Cai Yiqiang
     * @date: 2019/1/16
     */
    public function cancelPurchaseProduct()
    {
        $pid = I('post.pid', 0, 'intval');
        $aid = I('post.sid', 0, 'intval');

        if (!($pid && $aid)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $ticModel = $this->_getTicketModel();
        $tid      = $ticModel->getTicketInfoByPid($pid, 'id')['id'];

        if (!$tid) {
            $this->apiReturn(204, [], '产品不存在');
        }

        $result = $this->_rcBizApi->cancelProductPurchase($aid, $this->_sid, $pid, $tid, $this->_memberid);

        if ($result) {
            $this->apiReturn(200, [], '取消采购成功');
        }

        $this->apiReturn(201, [], '取消采购失败');

    }

    /***
     * 删除分销商
     * @author: Cai Yiqiang
     * @date: 2019/1/9
     */
    public function deleteDistributor()
    {
        $fid  = I('fid', 0, 'intval');
        $type = I('type', 1, 'intval');
        $note = I('note', '');

        $query = [
            'fid' => $fid,
        ];

        $queryRe = $this->_rcBizApi->getDistributorList($this->_sid, 1, 1, $query);

        if ($queryRe['resultList']) {
            $info   = array_shift($queryRe['resultList']);
            $id     = $info['id'];
            $result = $this->_rcBizApi->deleteDistributor($id, $this->_sid, $type, $note);
            $result ? $this->apiReturn(200, [], '删除成功') : $this->apiReturn(201, [], '删除失败');
        }

        $this->apiReturn(203, [], '未与该分销商建立资源合作关系');

    }

    /***
     * 删除供应商
     * @author: Cai Yiqiang
     * @date: 2019/1/9
     */
    public function deleteSupplier()
    {
        $aid = I('sid', 0, 'intval');

        $query = [
            'sid' => $aid,
        ];

        $queryRe = $this->_rcBizApi->getSupplierList($this->_sid, 1, 1, $query);

        if ($queryRe['resultList']) {
            $info   = array_shift($queryRe['resultList']);
            $id     = $info['id'];
            $result = $this->_rcBizApi->deleteSupplier($id, $this->_sid);
            $result ? $this->apiReturn(200, [], '删除成功') : $this->apiReturn(201, [], '删除失败');
        }

        $this->apiReturn(203, [], '未与该供应商建立资源合作关系');

    }

    /***
     * 获取黑名单列表
     * @author: Cai Yiqiang
     * @date: 2018/12/25
     */
    public function getBlackList()
    {
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 10, 'intval');
        $keyword = I('post.keyword', '', 'strval');
        $filters = $this->keywordSearch($keyword);

        $result = $this->_rcBizApi->getBlackList($this->_sid, $page, $size, $filters);

        $return['list'] = [];
        if ($result['recordtotal']) {
            foreach ($result['resultList'] as $item) {
                $return['list'][] = [
                    'dname'       => $item['dname'],
                    'cname'       => $item['cname'],
                    'com_name'    => $item['comName'],
                    'fid'         => $item['fid'],
                    'mobile'      => $item['mobile'],
                    'note'        => $item['note'],
                    'opid'        => $item['opid'],
                    'op_name'     => $item['opName'],
                    'update_time' => $item['updateTime'],
                    'hotline'     => $item['hotline'] ?? '',
                ];
            }
            $return['total'] = $result['recordtotal'];
        }

        $this->apiReturn(200, $return, 'success');
    }

    /***
     * 添加分销商到黑名单
     * @author: Cai Yiqiang
     * @date: 2018/12/25
     */
    public function addDistributorToBlacklist()
    {
        $fid = I('post.fid', 0, 'intval');

        $result = $this->_rcBizApi->addDistributorToBlacklist($this->_sid, $fid, $this->_memberid);

        if ($result) {
            $this->apiReturn(200, [], '添加成功');
        } else {
            $this->apiReturn(201, [], '添加失败');
        }
    }

    /***
     * 将分销商从黑名单移除
     * @author: Cai Yiqiang
     * @date: 2018/12/25
     */
    public function removeBlacklist()
    {
        $fid = I('post.fid', 0, 'intval');

        $filter = [];
        if ($fid) {
            $filter['fid'] = $fid;
        }

        $info = $this->_rcBizApi->getBlackList($this->_sid, 1, 1, $filter);

        if ($info && $info['recordtotal']) {
            $blackInfo = array_pop($info['resultList']);
            $idx       = $blackInfo['id'];
            $result    = $this->_rcBizApi->removeDistributorFromBlacklist($idx, $this->_memberid);
            if ($result) {
                $this->apiReturn(200, [], '移除成功');
            }
            $this->apiReturn(201, [], '移除失败');
        }

        $this->apiReturn(204, [], '黑名单中不存在该分销商');
    }

    /**
     * Notes:关键字根据手机号，账号，分销商名称搜索
     *
     * @param $keyword
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/11 9:22
     */
    public function keywordSearch($keyword)
    {
        $filters = [];
        if ($keyword) {
            if (mb_strlen($keyword) == strlen($keyword)) {
                //手机号或者账号
                if (isMobile($keyword)) {
                    $filters['mobile'] = $keyword;
                } else {
                    $filters['account'] = $keyword;
                }
            } else {
                //分销商名称
                $filters['dname'] = $keyword;
            }
        }

        return $filters;
    }

    /**
     * Notes:供应商黑名单产品
     * @return bool
     * <AUTHOR>
     * @date 2020/4/10 11:54
     */
    public function blackProducts()
    {
        $distributorId = I('get.fid/d');
        $ticketId      = I('get.tid/d');
        $landName      = I('get.landName/s');
        $pageNum       = I('get.pageNum/d', 1);
        $pageSize      = I('get.pageSize/d', 5);
        $keyword       = I('get.keyword/s');
        $filters       = $this->keywordSearch($keyword);
        $result        = $this->_rcBizApi->blackListProducts($this->_sid, $distributorId, $ticketId, $landName,
            $filters, $pageNum, $pageSize);
        if ($result && $result['code'] == 200) {
            return $this->apiReturn($result['code'], $result['data'] ?? [], $result['msg'] ?? 'success');
        }

        return $this->apiReturn($result['code'] ?? 400, [], $result['msg'] ?? '获取黑名单产品失败');
    }

    /**
     * Notes:获取指定分销商的产品列表
     * @return bool
     * <AUTHOR>
     * @date 2020/4/10 11:54
     */
    public function distributorProducts()
    {
        $pageNum       = I('get.pageNum/d', 1);
        $pageSize      = I('get.pageSize/d', 5);
        $distributorId = I('get.fid/d');
        $ticketId      = I('get.tid/d');
        $landName      = I('get.landName/s');
        if ($distributorId === '') {
            return $this->apiReturn(422, [], '参数错误');
        }
        $result = $this->_rcBizApi->distributorProducts($this->_sid, $distributorId, $ticketId, $landName, $pageNum,
            $pageSize);
        if ($result && $result['code'] == 200) {
            return $this->apiReturn($result['code'], $result['data'] ?? [], $result['msg'] ?? 'success');
        }

        return $this->apiReturn($result['code'] ?? 400, [], $result['msg'] ?? '获取分销商的产品列表失败');
    }

    /**
     * Notes:将分销商指定产品加入黑名单
     * @return bool
     * <AUTHOR>
     * @date 2020/4/9 16:47
     */
    public function productAddBlack()
    {
        $distributorId = I('post.fid/d');
        $ticketId      = I('post.tid/d');
        $lid           = I('post.lid/d');
        $note          = I('post.note/s');
        if ($distributorId === '' || $ticketId === '' || $lid === '') {
            return $this->apiReturn(422, [], '参数错误');
        }
        $result = $this->_rcBizApi->addBlackForDistributorProduct($this->_sid, $distributorId, $ticketId, $lid,
            $this->_memberid, $note);
        if ($result && $result['code'] == 200) {
            return $this->apiReturn($result['code'], $result['data'] ?? [], $result['msg'] ?? '已加入黑名单');
        }

        return $this->apiReturn($result['code'] ?? 400, [], $result['msg'] ?? '加入失败');
    }

    /**
     * Notes:将分销商指定产品移出黑名单
     * @return bool
     * <AUTHOR>
     * @date 2020/4/9 16:47
     */
    public function productRemoveBlack()
    {
        $id = I('post.id/d');
        if ($id === '') {
            return $this->apiReturn(422, [], '参数错误');
        }
        $result = $this->_rcBizApi->removeBlackForDistributorProduct($id, $this->_memberid);
        if ($result && $result['code'] == 200) {
            return $this->apiReturn($result['code'], $result['data'] ?? [], $result['msg'] ?? '已移出黑名单');
        }

        return $this->apiReturn($result['code'] ?? 400, [], $result['msg'] ?? '移出失败');
    }

    /***
     * 添加采购规则
     * @author: Cai Yiqiang
     * @date: 2019/1/7
     *
     * @params = [
     *
     * ]
     */
    public function addPurchaseRule()
    {
        $params = I('post.');

        $result = $this->_rcBizApi->savePurchaseRule($this->_sid, $this->_memberid, $params, $this->_sdtype);

        if (isset($result['data']) && $result['data'] == true) {
            $this->apiReturn(200, $result['data'], $result['msg']);
        } else {
            $this->apiReturn(201, [], is_array($result) ? '保存失败' : $result);
        }
    }

    /***
     * 获取采购规则列表
     * @author: Cai Yiqiang
     * @date: 2019/1/8
     */
    public function getPurchaseRules()
    {
        $result = $this->_rcBizApi->getPurchaseRule($this->_sid);
        if ($result) {
            array_walk($result, function (&$v, $k) {
                $v['priceSpread'] = $v['priceSpread'] / 100;
                $v['type']        = trim($v['type']);
            });
        }
        $this->apiReturn(200, $result, 'success');
    }

    /***
     * 删除一条采购规则
     * @author: Cr
     * @date: 2020/07/14
     */
    public function delPurchaseRule()
    {
        $id     = I('post.id', 0, 'intval');
        $tagId  = I('post.tagId');
        $result = $this->_rcBizApi->delPurchaseRule($this->_sid, $id, $tagId);

        if ($result) {
            $this->apiReturn(200, [], '删除成功');
        } else {
            $this->apiReturn(201, [], '删除失败');
        }
    }

    /***
     * 资源中心产品列表-按产品
     * @author: Cai Yiqiang
     * @date: 2019/1/8
     */
    public function getResourceCenterProducts()
    {
        $params = I('post.');
        $result = $this->_rcBizApi->getResourceCenterProducts($this->_sid, $params);
        $return = [
            'recordtotal' => 0,
            'resultList'  => [],
        ];
        if ($result) {
            if ($result['resultList']) {
                $return['recordtotal'] = $result['recordtotal'];
                $list                  = [];
                array_walk($result['resultList'], function (&$item, $key) use ($list) {
                    $ticList = $item['ticketList'];
                    $type    = $item['type'];
                    if ($ticList) {
                        array_walk($ticList, function (&$ticket, $k) use ($type) {
                            $ticket['usetime'] = $type != 'H' ? $ticket['usetime'] : '';
                            $priceInfo         = [];
                            foreach ($ticket['resourceProductList'] as $price) {
                                $price['costPrice']           = $price['costPrice'] / 100;
                                $price['priceResourceSpread'] = $price['priceResourceSpread'] / 100;
                                $price['retailPrice']         = $price['retailPrice'] / 100;
                                $price['profitPrice']         = $price['profitPrice'] / 100;
                                $priceInfo[]                  = $price;
                            }
                            $ticket['resourceProductList'] = $priceInfo;
                        });
                        $item['ticketList'] = $ticList;
                    }
                });
            }
            $return['resultList'] = $result['resultList'];
        }
        $this->apiReturn(200, $return, 'success');
    }

    /***
     * 资源中心产品列表-供应商
     * @author: Cai Yiqiang
     * @date: 2019/1/8
     */
    public function getResourceCenterProductsByApply()
    {
        $params = I('post.');
        $result = $this->_rcBizApi->getResourceCenterProductsByApply($this->_sid, $params);
        $this->apiReturn(200, $result, 'success');
    }

    /**
     * Notes:资源中心商品是否供应接口
     * <AUTHOR>
     * @date 2020/3/4 16:30
     */
    public function soldOutProduct()
    {
        $tid = I('tid', 0, 'intval');

        if (!$tid) {
            $this->apiReturn(203, [], '缺少tid');
        }

        $ticketModel = new Ticket();
        $tInfo       = $ticketModel->getTicketInfoById($tid, 'pid,landid');

        if ($tInfo) {
            $pid = $tInfo['pid'];
            $lid = $tInfo['landid'];

            // $result = $this->_rcBizApi->takeOffProduct($this->_sid, $this->_memberid, $lid, $tid, $pid);
            $result = (new \Business\JavaApi\ResourceCenter\ResourceProductConfig)->takeOffProduct($this->_sid, $this->_memberid, $lid, $tid, $pid);

            $result ? $this->apiReturn(200, [], '下架成功') : $this->apiReturn(201, [], '下架失败');
        }

        $this->apiReturn(204, [], '没有相关产品信息');
    }

    /***
     * 获取区域资源列表
     * @author: Cai Yiqiang
     * @date: 2019/1/10
     */
    public function getAreaResourceList()
    {
        $result = $this->_rcBizApi->getAreaResourceList($this->_sid);

        $return = [];
        if ($result) {
            foreach ($result as $item) {
                $return[] = [
                    'province_code' => $item['provinceId'],
                    'province_name' => $item['provinceName'],
                    'resource_num'  => $item['resourceCount'],
                ];
            }
        }

        $this->apiReturn(200, $return, 'success');
    }

    /***
     * 获取我的相关资源详情
     * @author: Cai Yiqiang
     * @date: 2019/1/13
     */
    public function getMyResourceDetail()
    {
        $result = $this->_rcBizApi->getMyResourceDetail($this->_sid);

        $return = [];
        if ($result) {
            $return = [
                'coop_dis_count'  => $result['cooperationDistributionTotalCount'],
                'coop_sup_count'  => $result['cooperationSupplierTotalCount'],
                'purchased_count' => $result['purchaseTotalCount'],
                'resource_count'  => $result['resourceTotalCount'],
            ];
        }

        $this->apiReturn(200, $return, 'success');

    }

    /***
     * 批量采购
     * @author: Cai Yiqiang
     * @deprecated
     * @vacation resource_batch
     * @date: 2019/1/13
     */
    public function batchPurchase()
    {
        $params = I('post.');

        $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('resource_batch', $this->_loginInfo['saccount']);

        if ($vacationMode === false) {
            $this->apiReturn(403, [], '当前处于假日模式，该功能被限制使用');
        }

        if (!$params || !is_array($params)) {
            $this->apiReturn(203, [], '缺少采购信息');
        }

        $result = $this->_rcBizApi->batchPurchase($this->_sid, $this->_memberid, $params, $this->_sdtype);

        $return = [];
        if ($result && is_array($result)) {
            foreach ($result as $item) {
                $return['list'][] = [
                    'pid'          => $item['pid'],
                    'tid'          => $item['ticket_id'],
                    'price_diff'   => $item['price_spread'] / 100,
                    'price_dis'    => $item['price_spread_to_others'] / 100,
                    'cost_price'   => $item['cost_price'] / 100,
                    'reason'       => $item['reason'],
                    'status'       => $item['status'],
                    'ticket_name'  => $item['tName'] ?? '',
                    'product_name' => $item['pName'] ?? '',
                    'sid'          => $item['sid'],
                ];
            }

            $this->apiReturn(200, $return, 'success');
        }

        $this->apiReturn(204, [], !is_array($result) ? $result : '批量采购失败');

    }

    /**
     * Notes:单个产品是否可采购
     * @return array|bool
     * <AUTHOR>
     * @date 2020/4/2 15:52
     */
    public function canPurchase()
    {
        $ticketId  = I('post.ticketId/d');
        $productId = I('post.pid/d');
        $sellerId  = I('post.sid/d');
        if ($ticketId === '' || $sellerId === '' || $productId === '') {
            return $this->apiReturn(422, [], '参数错误');
        }
        $res = $this->_rcBizApi->canPurchase($sellerId, $productId, $ticketId, $this->_sid, $this->_memberid);

        return $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /***
     * 获取可采购列表
     * @author: Cai Yiqiang
     * @date: 2019/1/13
     */
    public function queryPurchasableList()
    {
        $params = I('post.');

        $result = $this->_rcBizApi->getPurchasableList($this->_sid, $params);

        $return = [];
        if ($result['recordtotal'] && $result['resultList']) {
            $return['total'] = $result['recordtotal'];
            foreach ($result['resultList'] as $item) {
                $return['list'][] = [
                    'land_name'    => $item['landName'],
                    'ticket_name'  => $item['ticketName'],
                    'tid'          => $item['ticketId'],
                    'pid'          => $item['pid'],
                    'status'       => $item['status'],
                    'cost_price'   => $item['costPrice'],
                    'retail_price' => $item['retailPrice'],
                    'price_diff'   => $item['priceSpread'],
                    'sid'          => $item['supplierId'],
                ];
            }
        }

        $this->apiReturn(200, $return, 'success');
    }

    /***
     * 查询自动采购记录列表
     * @author: CR
     * @date: 2020-7-10
     */
    public function queryAutoPurchaseList()
    {
        $startTime = I('post.startTime');
        $endTime   = I('post.endTime');
        $pageNum   = I('post.pageNum');
        $pageSize  = I('post.pageSize');
        $startTime = strtotime(date($startTime));
        $endTime   = strtotime(date($endTime . '23:59:59'));
        $params    = [
            'startTime' => intval($startTime),
            'endTime'   => intval($endTime),
            'pageNum'   => intval($pageNum),
            'pageSize'  => intval($pageSize),
        ];
        $result    = $this->_rcBizApi->getAutoPurchaseList($this->_memberid, $params);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /***
     * 添加后台采购任务
     * @author: Cai Yiqiang
     * @date: 2019/1/13
     */
    public function addBackPurchaseTask()
    {
        $ruleId = I('id', 0, 'intval');

        if (!$ruleId) {
            $this->apiReturn(203, [], '缺少规则id');
        }

        $result = $this->_rcBizApi->backstagePurchase($this->_sid, $ruleId);

        if ($result) {
            $this->apiReturn(200, [], 'success');
        }

        $this->apiReturn(201, [], 'fail');
    }

    /***
     * 提供给前端查询的是否开通了资源中心
     * @author: Cai Yiqiang
     * @date: 2019/1/21
     */
    public function hasOpenedResourceCenter()
    {
        $result = $this->_rcBiz->hasOpenedResourceCenter($this->_sid, $this->_dtype, $this->_qx);
        $this->apiReturn(200, ['status' => $result], 'success');
    }

    /**
     * 打开激活资源中心界面停留日志
     * <AUTHOR>
     */
    public function openResourceCenterLog()
    {
        // 记录行为日志
        $logModel = new \Model\SystemLog\PftLog();
        $res      = $logModel->addResourceCenterLog($this->_sid, 1);

        $this->apiReturn(200, $res, 'success');
    }

    /**
     * 判断用户是否激活资源中心
     * <AUTHOR>
     */
    public function checkActiveResource()
    {
        // 获取用户是否开通记录
        $key   = 'resource:center:open:' . $this->_sid;
        $cache = $this->cacheInstance();
        $res   = $cache->get($key);
        if ($res === false) {
            $logModel = new \Model\SystemLog\PftLog();
            $data     = $logModel->getResourceCenterLog($this->_sid, 2);
            if (!empty($data)) {
                $res = 1;
                $cache->setex($key, 86400, $res);
            } else {
                $res = 0;
            }
        }
        $needRemind = false;
        // 用户未激活资源中心
        if (!$res) {
            //是否不再提醒
            $never = $cache->sismember($this->neverRemindSortKeyName(), $this->neverRemindUserValue());
            if (!$never) {
                //是否首次登陆
                $today = $cache->get($this->firstLoginTodayKey());
                if (!$today) {
                    //是首次登陆
                    $needRemind = true;
                    $cache->setex($this->firstLoginTodayKey(), $this->todayRemainSeconds(), $this->_memberid);
                }
            }
        }

        // 获取用户客服热线
        $memberQueryBiz = new \Business\JavaApi\Member\MemberQuery();
        $memberInfo     = $memberQueryBiz->queryMemberByMemberId($this->_sid, true);

        $data = [
            'status'     => $res,
            'needRemind' => $needRemind,
            'hotline'    => $memberInfo['data']['memberExtInfo']['hotline'] ?? '',
        ];

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 激活资源中心
     * <AUTHOR>
     */
    public function activeResourceCenter()
    {
        // 记录行为日志
        $logModel = new \Model\SystemLog\PftLog();
        $logModel->addResourceCenterLog($this->_sid, 2);

        $this->apiReturn(200, [], '');
    }

    private function _getTicketModel()
    {
        if (!$this->_ticModel) {
            $this->_ticModel = new Ticket();
        }

        return $this->_ticModel;
    }

    /**不再弹窗提示用户加入
     * Notes:
     * @return bool
     * <AUTHOR>
     * @date 2020/3/10 10:39
     */
    public function neverReminded()
    {
        $cache     = $this->cacheInstance();
        $userExist = $cache->sismember($this->neverRemindSortKeyName(), $this->neverRemindUserValue());
        if ($userExist) {
            return $this->apiReturn(400, [], '已关闭提醒,请勿重复提交');
        }
        $addResult = $cache->sadd($this->neverRemindSortKeyName(), $this->neverRemindUserValue());
        if (!$addResult) {//不存在此情况
            return $this->apiReturn(500, [], '服务器错误请重试！');
        }

        return $this->apiReturn(200, [], '已不再提醒!');
    }

    /**
     * Notes:用户今日登陆缓存key
     * @return string
     * <AUTHOR>
     * @date 2020/3/10 9:34
     */
    protected function firstLoginTodayKey()
    {
        return 'resource:center:first_login_today:user_' . $this->_memberid;
    }

    /**
     * Notes:至今日凌晨时间戳
     * @return false|int
     * <AUTHOR>
     * @date 2020/3/10 9:35
     */
    protected function todayRemainSeconds()
    {
        return strtotime(date('Y-m-d 23:59:59')) - time();
    }

    /**
     * Notes:不再提醒缓存值
     * @return string
     * <AUTHOR>
     * @date 2020/3/10 9:35
     */
    protected function neverRemindUserValue()
    {
        return 'user_' . $this->_memberid;
    }

    /**
     * Notes:不再提醒缓存集合key
     * @return string
     * <AUTHOR>
     * @date 2020/3/10 9:36
     */
    protected function neverRemindSortKeyName()
    {
        return 'resource:center:never_remind:users';
    }

    /**
     * Notes:同步分销价格
     * <AUTHOR>
     * @date 2020/4/3 14:30
     */
    public function syncCostPrice()
    {
        $sellerId  = I('sid/d');
        $productId = I('pid/d');
        if ($sellerId === '' || $productId === '') {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        $res = $this->_rcBizApi->syncCostPriceForDistributor($sellerId, $this->_sid, $productId, $this->_memberid);
        if ($res && $res['code'] == 200) {
            $this->apiReturn(self::CODE_SUCCESS, [], '同步成功');
        } else {
            $this->apiReturn($res['code'] ?? self::CODE_INVALID_REQUEST, [], $res['msg'] ?? '同步失败');
        }
    }

}