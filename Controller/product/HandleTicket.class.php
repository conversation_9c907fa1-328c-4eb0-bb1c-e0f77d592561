<?php
/**
 * 门票相关操作
 * @TODO：以后加代码到这个文件的，请注意，尽量不要被PhpStorm报规范提示
 * User: LuoChen Lan
 * Date: 2017/7/21
 * Time: 16:48
 */

namespace controller\product;

use Business\Face\AliFaceBiz;
use Business\JavaApi\Context\Product\TicketReserveStorageContext;
use Business\JavaApi\Product\PackageTicket;
use Business\JavaApi\Product\ReserveStorage;
use Business\JavaApi\Product\TicketDeputyService;
use Business\JavaApi\TicketApi;
use Business\JsonRpcApi\ScenicLocalService\TerminalShare;
use Business\MemberLogin\MemberLoginHelper;
use Business\PackTicket\PackRelation;
use Business\Product\Hotel;
use Business\Product\Ticket as TicketBiz;
use Library\Cache\Cache;
use Library\Constants\TerminalConst;
use Library\Controller;
use Business\Product\HandleTicket as businessLib;
use Business\Product\Specialty;
use Model\CardSolution\TimingProduct;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\Product\TimingEquip;
use Business\JavaApi\StorageApi;
use Business\JavaApi\Ticket\Ext as ticketExtConf;
use Business\Authority\AuthContext;
use Business\ElectronicInvoices\InvoiceApi;

class HandleTicket extends Controller
{
    private $_memberId;
    private $_businessLib;
    private $_operatorId;
    private $_dtype;
    private $_sdtype;

    private $_isWriteOriginLog = true;
    //年卡特权支持的产品类型
    private $_annualPrivPtype  = ['A','B','G'];

    public function __construct($loginInfo = [])
    {
        if(empty($loginInfo)){
            $loginInfo         = $this->getLoginInfo();
        }
        $this->_memberId   = $loginInfo['sid'];
        $this->_dtype      = $loginInfo['dtype'];
        $this->_sdtype     = $loginInfo['sdtype'];
        $this->_operatorId = $loginInfo['memberID'];

        $sid = $this->_memberId;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $this->_businessLib = new businessLib($sid, $this->_operatorId);
    }

    /**
     * 门票属性设置
     * @TODO:平台特性的逻辑放在PHP层处理(beforeHandel，afterHandle)，其他的在JAVA层
     *
     * @date   2017-07-20
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function setTicket()
    {
        if (!in_array($this->_dtype, [0, 2, 6, 9, 18])) {
            $this->apiReturn(203, [], '该类型账号不允许编辑票类');
        }

        if (in_array($this->_sdtype, [1])) {
            $this->apiReturn(203, [], '该类型账号不允许编辑票类');
        }

        // 由于前端字段个数限制，所以以字符串形式（‘data’ : ''）传到php，php再转成对象
        //qiuxi - 2020-06-18
        $submitData         = I('post.');
		// echo '<pre>';
		//var_dump($submitData); exit;
        $submitData['data'] = htmlspecialchars_decode($submitData['data']);
        $submitData         = json_decode($submitData['data'], true);

        if (count($submitData) < 1) {
            $this->apiReturn(204, [], '未接收到门票信息');
        }

        $sid = $this->_memberId;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $proTypeAuth = (new AuthContext())->memberProductTypeAuth($sid);

        //记录原始的日志
        if($this->_isWriteOriginLog) {
            pft_log('HandleTicket/setTicket', json_encode([$this->_operatorId, '编辑', $submitData], JSON_UNESCAPED_UNICODE));
        }

        //因为后面新产品类型的特异性差异比较大，
        //所以单独出来针对不同的产品类型写接口
        //wengbin - 2017-12-11
        if (isset($submitData['type'])) {
            $type = $submitData['type'];
            if (!in_array('J', $proTypeAuth)) {
                $this->apiReturn(403, $this->_preApiReturn(403, '无发布特产产品权限！'));
            }

        } else {
            //发布产品权限校验  -xiexy 2020-01-03
            foreach ($submitData as $tid => $item) {
                if (!in_array($item['ticket']['type'], $proTypeAuth)) {
                    $this->apiReturn(403, $this->_preApiReturn(403, '无发布该类型产品门票权限！'));
                }
            }

            $type = 'common';
        }
        $result = [];
        switch ($type) {
            //特产门票发布（特产产品已经不走这个接口了）
            case 'specialty':
                if (count($submitData['list']) < 1) {
                    $this->apiReturn(204, [], '未接收到规格信息');
                }

                $result = $this->_setTicketForSpecialty($submitData['list']);
                //清除下单设置的缓存
                foreach ($submitData['list'] as $k => $v) {
                    TicketBiz::delRedisLandInfo($v["tid"], $v["pid"]);
                }
                break;

            //早期产品类型都公用一个接口
            default:
                foreach ($submitData as $tid => $item) {
                    //var_dump($item);exit;
                    //$item['ticket']['refund_cost_fixed_type'] = 0;
                    //$item['ticket']['refund_type'] = 0;
                    //$item['ticket']['refund_value'] = 10;
                    if ($item['ticket']['type'] == 'I') {
                        $ticketResult = $this->_setTicketForAnnual($item, $tid);
                    } else {
                        $ticketResult = $this->_setTicketForCommon($item, $tid);
                    }
                    $ticketResult['isNew'] = empty($tid) || $tid < 0;
                    $result[] = $ticketResult;
                    //清除下单设置的缓存
                    TicketBiz::delRedisLandInfo($item["ticket"]["id"], $item["ticket"]["product_id"]);
                }
        }
        //获取微商城配置
        $sid         = $this->_memberId;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }
        $allDisMode  = new \Business\Mall\AllDis();
        $res         = $allDisMode->getAllDisConfig($sid, 'config, close_pids');
        //$ticket      = array_column($submitData, 'ticket');
        //$productIArr = array_column($ticket, "product_id");
        if ($res && $type == 'common') {
            // 0=新增产品/门票默认不参与全民营销
            $configData = json_decode($res['config'], true);
            if (empty($configData['product_setting']) && isset($configData['product_setting'])) {
                $productConfig = [];
                foreach ($result as $value) {
                    //不处理编辑产品，只处理新增产品
                    if (!$value['isNew']) continue;
                    $productConfig[] = [
                        'sid' => $sid,
                        'pid' => $value['pid'],
                        'aid' => $sid,
                        'is_close' => 1,
                    ];
                }
                if ($productConfig) {
                    $data       = [
                        'product_config' => $productConfig,
                    ];
                    $allDisMode = new \Model\Mall\AllDis();

                    $res        = $allDisMode->saveAllDisConfig($sid, $data);
                    if ($res === false) {
                        pft_log('debug/close_pid', "保存错误的语句：" . $allDisMode->getLastSql());
                    }

                    //清除全民营销缓存
                    $allDisBiz = new \Business\Mall\AllDis();
                    $allDisBiz->delAllDisConfigCache($sid);
                }
            }
        }
        //设置了期停售时间，通知Coo创建活动
        $activityBz = new \Business\Cooperator\Activity\Activity();
        $tidArr     = array_column($result, "tid");
        $activityBz->createSeckillActivity($tidArr);

        $this->apiReturn(200, $result, '');
    }

    /**
     * 特殊票种门票属性设置
     * Create by zhangyangzhen
     * Date: 2018/8/3
     * Time: 9:24
     */
    public function setSpecialTicket()
    {
        $submitData = I('post.');

        if (count($submitData) < 1) {
            $this->apiReturn(204, [], '未接收到门票信息');
        }

        $result = $this->_setSpecialTicketForCommon($submitData, $this->_memberId, $this->_operatorId);
        $this->apiReturn($result['code'], $result['data'] ?: [], $result['msg']);
    }

    /**
     * 特殊票种门票属性设置
     * Create by zhangyangzhen
     * Date: 2018/8/3
     * Time: 9:40
     *
     * @param $submitData 门票属性
     *      [
     *          'ptype'         => 'A'      //A=景区，F=套票，I=年卡
     *          'specialType'   => 1,       //1=年龄，2=性别，3=区域，4=免费
     *          'minAge'        => 18,      //最小年龄
     *          'maxAge'        => 60,      //最大年龄
     *          'area'          => [        //区域票限制地区，多选
     *              [15,323,3234] //省市区
     *              [15,323,3234]
     *          ],
     *          'price'         => 1,       //特殊票价格
     *          'tid'           => [        //门票id，多选
     *                                  123456,
     *                                  234567
     *                             ]
     *          ]
     */
    private function _setSpecialTicketForCommon($submitData, $accountId, $operaterId)
    {
        $beforeData = $this->_businessLib->beforeHandleSpecial($submitData);

        if ($beforeData['code'] != 200) {
            $this->apiReturn($beforeData['code'], [], $beforeData['msg']);
        }

        $ptype            = $submitData['ptype'];
        $title            = $submitData['name'];
        $ticketIds        = $submitData['tid'];
        $specialConfigArr = $beforeData['data'];

        $sid = $this->_memberId;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        //特殊票种权限控制（根据套餐中的允许发布的产品）
        $proTypeAuth = (new AuthContext())->memberProductTypeAuth($sid);
        if (!in_array($ptype, $proTypeAuth)) {
            $this->apiReturn(403, [], '无发布该类型产品门票权限！');
        }

        $addRes = $this->_businessLib->addSpecialTicket($ticketIds, $title, $specialConfigArr, $accountId, $operaterId);
        if ($addRes['code'] != 200) {
            return $addRes;
        } else {
            $data      = $addRes['data'];
            $afterData = $this->_businessLib->afterHandleSpecial($data, $ptype);

            if ($afterData['code'] != 200) {
                return $afterData;
            }

            $postData = [];
            foreach ($data as $key => $val) {
                $postData['dataList'][] = [
                    'oldTicketId'  => $val['oldTicketId'],
                    'ticketId'     => $val['ticketId'],
                    'productId'    => $val['productId'],
                    'costPrice'    => $submitData['price'][$val['oldTicketId']][0],
                    'retailPrice'  => $submitData['price'][$val['oldTicketId']][1],
                    'windowPrice'  => $submitData['price'][$val['oldTicketId']][2],
                    'counterPrice' => $submitData['price'][$val['oldTicketId']][3],
                ];
            }

            $result = $this->_businessLib->saveSpecialPrice($postData);

            return $result;
        }
    }

    /**
     * 门票属性设置
     * 平台特性的逻辑放在PHP层处理(beforeHandle，afterHandle)，其他的在JAVA层
     *
     * @date   2017-07-20
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function _setTicketForCommon($submitData, $ticketId)
    {
        $ticketData      = $submitData['ticket'];
        $priceData       = $submitData['price_section'];
        $packageCacheKey = $submitData['flag'];

        // 预约库存
        $reserveStorageData = [];
        if (isset($submitData['ticket']['reserve_storage'])) {
            $reserveStorageData = $submitData['ticket']['reserve_storage'];
            unset($submitData['ticket']['reserve_storage']);
        }
        //年卡没有设置窗口价，传0过来了，新的java保存价格接口会提示“窗口价不得小于成本价”，旧接口不会
        //暂时处理成窗口价=门市价 update by zhangyz -- 2020-01-19
        if ($ticketData['type'] == 'I') {
            foreach ($priceData as $key => $price) {
                $priceData[$key]['w_price'] = $price['m_price'];
            }
        }

        $ticketPrintCheck = $submitData['ticket']['ticket_print_check_rule'];

        $ticketPrintCheck = $ticketPrintCheck == 'null' ? [] : $ticketPrintCheck;
        if (!empty($ticketPrintCheck)) {
            $orderMode = isset($ticketPrintCheck['order_mode']) ? $ticketPrintCheck['order_mode'] : 0;
            $printMode = isset($ticketPrintCheck['print_mode']) ? $ticketPrintCheck['print_mode'] : 3;
            $checkMode = isset($ticketPrintCheck['check_mode']) ? $ticketPrintCheck['check_mode'] : 1;

            // 如果用户选择了 一人一票的打印规则 和 分批入园， 则验证方式改为 分批入园，一人一票
            if ($printMode == 4 && $checkMode == 2) {
                $checkMode = 3;
            }
            $ticketData['check_mode'] = $checkMode;
            $ticketData['order_mode'] = $orderMode;
            $ticketData['print_mode'] = $printMode;
        }

        //演出的一些属性判断
        if ($ticketData['pType'] == 'H') {
            //判断下演出是否有被打包成子票
            $parentTids = (new \Business\Product\PackTicket())->getParentsByTid($ticketId);
            if ($parentTids && $ticketData['pre_sale'] == 1) {
                //验证是否是套票，而不是捆绑票 (捆绑票主票类型是H)
                $landModel   = new \Business\CommodityCenter\Land();
                $landInfoArr = $landModel->getLandInfoByArrTidToJava($parentTids);
                //验证是否有套票类型
                $checkShowType = array_column($landInfoArr, 'p_type');
                if (in_array('F', $checkShowType)) {
                    $returnData['code'] = 201;
                    $returnData['msg']  = '已经被打包子票不可选择"无需游玩日期"';
                    return $returnData;
                }
            }

            //演出场次时间校验
            if ($ticketData['auto_checked'] > 0 && $ticketData['auto_checked'] <= 24) {
                //获取下演出场次信息
                $roundList = (new \Model\Product\Show())->getRoundList($ticketData['venus_id'], 'id,round_name,bt,et',
                    $status = 0, date( 'Y-m-d'), date('Y-m-d'));
                if ($roundList) {
                    //拿出最晚场次的结束时间
                    $etArr = array_column($roundList, 'et');
                    //按照结束时间倒叙下
                    arsort($etArr);
                    //解析时间点
                    $etArr  = array_values($etArr);
                    $etHour = explode(':', $etArr[0])[0];

                    $timeDif = $ticketData['auto_checked'];

                    //跨天验证的不判断场次时间
                    if ($timeDif <= $etHour) {
                        $returnData['code'] = 201;
                        $returnData['msg']  = '自动验证时间需晚于最晚的场次结束的时间';

                        return $returnData;
                    }
                }
            }

            //需要判断下 预检方式为分批时 && 打印纸票配置非一人一票不让保存
            if ($ticketData['pre_check_rule'] == 2 && $ticketData['print_mode'] != 4){
                $returnData['code'] = 201;
                $returnData['msg']  = '分批预检仅支持打印纸票：一人一票';

                return $returnData;
            }
        }

        $returnData['price'] = ['code' => 202, 'msg' => '价格未保存'];
        $returnData['code']  = 200;
        $returnData['msg']   = 'success';
        $returnData['name']  = $ticketData['name'];

        //多景点配置日志记录
        $this->addCheckTerminalInfoLog($ticketData);

        $checkResult = $this->_businessLib->beforeHandel($ticketData, $priceData);

        if ($checkResult[0] != 0) {
            $returnData['code'] = $checkResult[0];
            $returnData['msg']  = $checkResult[1];

            return $returnData;
        }

        $ticketData                = $checkResult[1];
        $ticketData['operater_id'] = $this->_operatorId;

        //处理演出类有效期
        if ($ticketData['type'] == 'H') {
            if (isset($ticketData['entrance_time_limit']) && is_numeric($ticketData['entrance_time_limit'])) {
                $ticketData['entrance_time_limit'] = json_encode(['s' => (int)$ticketData['entrance_time_limit']]);
            }

            //判断演出 需在场次开始时间 多少分钟可预订
            if ((isset($ticketData['before_show_hour']) && $ticketData['before_show_hour'] >= 0) || (isset($ticketData['before_show_minute']) && $ticketData['before_show_minute'] >= 0)) {
                $hour                           = $ticketData['before_show_hour'] ?? 0;
                $minute                         = $ticketData['before_show_minute'] ?? 0;
                $ticketData['before_show_time'] = $hour * 60 + $minute;
                //删除无用参数
                unset($ticketData['before_show_hour'], $ticketData['before_show_minute']);
            }

            //限时售卖字段处理
            if (isset($ticketData['show_order_limit_time'])) {
                if (isset($ticketData['show_order_limit_time']['start_time']) && isset($ticketData['show_order_limit_time']['end_time'])) {
                    if (($ticketData['show_order_limit_time']['start_time'] && !$ticketData['show_order_limit_time']['end_time']) || (!$ticketData['show_order_limit_time']['start_time'] && $ticketData['show_order_limit_time']['end_time'])) {
                        $returnData['code'] = 204;
                        $returnData['msg']  = '限时售卖时间配置异常，请检查';

                        return $returnData;
                    }

                    if ($ticketData['show_order_limit_time']['start_time'] > $ticketData['show_order_limit_time']['end_time']) {
                        $returnData['code'] = 204;
                        $returnData['msg']  = '限时售卖配置开始时间不能大于结束时间';

                        return $returnData;
                    }
                }

                $ticketData['show_order_limit_time'] = $ticketData['show_order_limit_time'] ? json_encode($ticketData['show_order_limit_time']) : json_encode([]);
            }
        }

        //处理年卡下单限制
        if ($ticketData['type'] == 'I') {
            if (isset($ticketData['card_order_limit']) && $ticketData['card_order_limit']) {
                $ticketData['card_order_limit'] = json_encode($ticketData['card_order_limit']);
            }

            //需要对特权门票的类型做个校验
            if (isset($ticketData['fe_ori_group_priv']) && $ticketData['fe_ori_group_priv']) {
                $tmpTidArr = [];
                foreach ($ticketData['fe_ori_group_priv'] as $groupPriv) {
                    $tmpTidArr = array_merge(array_column($groupPriv['list'], 'tid'), $tmpTidArr);
                }
                if ($tmpTidArr) {
                    //判断特权门票类型，如果非景区餐饮线路，给出提示
                    $javaApi   = new \Business\CommodityCenter\Ticket();
                    $ticketRes = $javaApi->queryTicketInfoByIds(array_values($tmpTidArr));
                    if ($ticketRes) {
                        foreach ($ticketRes as $item) {
                            if (!in_array($item['land']['p_type'], $this->_annualPrivPtype)) {
                                $returnData['code'] = 204;
                                $returnData['msg']  = '部分特权产品不可添加，请重新选择。';
                                return $returnData;
                            }
                        }
                    }
                }
            }
        }

        //票类到期前7天，短信通知供应商
        if (isset($ticketData['ticket_sms_expire'])) {
            $ticketData['expire_before_notice_to_supplier'] = $ticketData['ticket_sms_expire'];
            unset($ticketData['ticket_sms_expire']);
        }

        if ($ticketData['type'] == 'F') {
            pft_log('test/pack', "保存套票数据: " . json_encode($submitData, JSON_UNESCAPED_UNICODE));

            //是演出子票验证,打包子票只允许未捆绑有其他门票的演出票
            $packBiz = new \Business\Product\PackTicket();
            $isValid = $packBiz->checkShowBindForPackSonTicket($packageCacheKey);
            if ($isValid['code'] != 200) {
                $returnData['code'] = $isValid['code'];
                $returnData['msg']  = $isValid['msg'];

                return $returnData;
            }
        }

        //自动上下架
        $ticketData = $this->_businessLib->handleAutoParam($ticketData, $priceData);

        // 根据用户设置可用日期和不可用日期判断 是启用了哪种类型 期票
        // if ($ticketData['pre_sale'] == 1 && $ticketData['valid_period_type'] == 2) {  // 预售门票要这么判断
        $validRuleSelfType = 0;
        if (!empty($ticketData['verify_disable_week'])) {
            $validRuleSelfType = 1;
        }
        if (!empty($ticketData['valid_rule_self_ex'])) {
            $ticketData['valid_rule_self_ex'] = htmlspecialchars_decode($ticketData['valid_rule_self_ex']);
            $ticketData['valid_rule_self_ex'] = json_decode($ticketData['valid_rule_self_ex'], true);
            if (!is_array($ticketData['valid_rule_self_ex'])) {
                $returnData['code'] = 201;
                $returnData['msg']  = '预售不可用时间参数有误';

                return $returnData;
            }
            $ticketData['valid_rule_self_ex'] = json_encode($ticketData['valid_rule_self_ex']);
            if ($validRuleSelfType == 1) {
                $validRuleSelfType = 3;
            } else {
                $validRuleSelfType = 2;
            }
        }
        if (!empty($ticketData['advance_reserve_time'])) {
            $ticketData['advance_reserve_time'] = htmlspecialchars_decode($ticketData['advance_reserve_time']);
            $ticketData['advance_reserve_time'] = json_decode($ticketData['advance_reserve_time'], true);
            $ticketData['advance_reserve_time'] = json_encode($ticketData['advance_reserve_time']);
        }
        if (!empty($ticketData['reserve_after_verify_time'])) {
            $ticketData['reserve_after_verify_time'] = htmlspecialchars_decode($ticketData['reserve_after_verify_time']);
            $ticketData['reserve_after_verify_time'] = json_decode($ticketData['reserve_after_verify_time'], true);
            $ticketData['reserve_after_verify_time'] = json_encode($ticketData['reserve_after_verify_time']);
        }

        if (!empty($ticketData['effective_limit'])) {
            $ticketData['effective_limit'] = htmlspecialchars_decode($ticketData['effective_limit']);
            $ticketData['effective_limit'] = json_decode($ticketData['effective_limit'], true);
            if (!is_array($ticketData['effective_limit'])) {
                $returnData['code'] = 201;
                $returnData['msg']  = '预售有效期时间参数有误';

                return $returnData;
            }
            $ticketData['effective_limit'] = json_encode($ticketData['effective_limit']);
        }
        $ticketData['valid_rule_self_type'] = $validRuleSelfType;
        // }
        // 设置本次操作的uuid
        $ticketData['versionUuid'] = uniqid();

        //处理套票下单限制
        if ($ticketData['type'] == 'F' && $ticketData['ticket_shelf_rule'] == 3) {
            if (isset($ticketData['start_sale_datetime']) && $ticketData['start_sale_datetime']) {
                $ticketData['start_sale_datetime'] = date('Y-m-d H:i', strtotime($ticketData['start_sale_datetime']));
            }

            if (isset($ticketData['stop_sale_datetime']) && $ticketData['stop_sale_datetime']) {
                $ticketData['stop_sale_datetime'] = date('Y-m-d H:i', strtotime($ticketData['stop_sale_datetime']));
            }
        }

        if ($ticketData['id'] > 0) {
            $res = $this->_businessLib->setTicketNew($ticketData, $this->_memberId);

            if ($res['code'] != 200) {
                $returnData['code'] = 201;
                $returnData['msg']  = '保存失败,' . $res['msg'];

                return $returnData;
            }

            $returnData['tid'] = $ticketData['id'];
            $returnData['lid'] = $ticketData['item_id'];
            $returnData['pid'] = $ticketData['product_id'];

            $productId = $ticketData['product_id'];

            //人脸配置检测
            $lid         = $ticketData['item_id'];
            $landModel   = new Land();
            $landInfo    = $landModel->getLandInfo($lid, false, 'terminal');//根据lid取终端号
            $isAliFacePlatform = AliFaceBiz::getInstance()->isAliFacePlatform($landInfo['apply_did'], 0);
            $facePlatform = [];
            if (!$isAliFacePlatform) {
                $terminalId  = $landInfo['terminal'];
                $nowTerminal = $landModel->getNowTerminal($terminalId);
                if ($nowTerminal) {
                    //如果有合并终端的，取合并后的终端
                    $terminalId = $nowTerminal;
                }
                $faceModel    = new \Model\Terminal\FaceCompare();
                $facePlatform = $faceModel->getFacePlatform(false, $terminalId);
            }
            if (!$facePlatform && !$isAliFacePlatform && $ticketData['face_open'] == 1) {
                unset($ticketData['face_open']);
                unset($ticketData['face_idcard_compare']);
                unset($ticketData['face_repeat_enter']);
                unset($ticketData['face_enter_time']);
                unset($ticketData['face_valid_time']);
            }

            //获取票类扩展属性
            $extParams = $this->_getExtParams($ticketData, $ticketData['id']);
			//echo '<pre>';
	        //var_dump($ticketData['id']); exit;
	        // var_dump($ticketData['custom_check_voice']);
			// var_dump($extParams); exit;
            if (!empty($extParams)) {
                //保存票类扩展属性
                $extRes = $this->_saveExtParams($this->_memberId, $this->_operatorId, $extParams,
                    $ticketData['versionUuid']);
                if ($extRes['code'] !== 200) {
                    $returnData['ext'] = ['code' => 201, 'msg' => '票类扩展属性保存失败'];
                } else {
                    $returnData['ext'] = ['code' => 200, 'msg' => '扩展保存成功'];
                }
            }

            if (count($reserveStorageData) > 0) {
                $reserveStorageRes = $this->saveReserveStorageParams($reserveStorageData, $ticketId, 0);
                if ($reserveStorageRes['code'] !== 200) {
                    $returnData['reserve_storage'] = [
                        'code' => 201,
                        'msg'  => '票类预约库存属性保存失败 : ' . $reserveStorageRes['msg'],
                    ];
                } else {
                    $returnData['reserve_storage'] = ['code' => 200, 'msg' => '票类预约库存保存成功'];
                }
            }

        } else {
            $res = $this->_businessLib->setTicketNew($ticketData, $this->_memberId);

            if ($res['code'] != 200) {
                $returnData['code'] = 201;
                $returnData['msg']  = '保存失败,' . $res['msg'];

                return $returnData;
            }
            $res               = $res['data'];
            $productId         = $res['productId'];
            $returnData['tid'] = $res['ticketId'];
            $ticketId          = $res['ticketId'];

            $returnData['lid'] = $ticketData['item_id'];
            $returnData['pid'] = $productId;

            $this->_businessLib->setChannel($productId);

            //获取票类扩展属性
            $extParams = $this->_getExtParams($ticketData, $ticketId);
            if (!empty($extParams)) {
                //保存票类扩展属性
                $extRes = $this->_saveExtParams($this->_memberId, $this->_operatorId, $extParams,
                    $ticketData['versionUuid']);
                if ($extRes['code'] !== 200) {
                    $returnData['ext'] = ['code' => 201, 'msg' => '票类扩展属性保存失败'];
                } else {
                    $returnData['ext'] = ['code' => 200, 'msg' => '扩展保存成功'];
                }
            }
            if (count($reserveStorageData) > 0) {
                $reserveStorageRes = $this->saveReserveStorageParams($reserveStorageData, $ticketId, 0);
                if ($reserveStorageRes['code'] !== 200) {
                    $returnData['reserve_storage'] = [
                        'code' => 201,
                        'msg'  => '票类预约库存属性保存失败 : ' . $reserveStorageRes['msg'],
                    ];
                } else {
                    $returnData['reserve_storage'] = ['code' => 200, 'msg' => '票类预约库存保存成功'];
                }
            }

            // 设置演出票的sourceT等绑定参数
            // 因为演出现在有对接上游  所以这边不能做三方的绑定
            //if ($ticketData['type'] == 'H') {
            //    $thirdAttrBiz = new \Business\JavaApi\Ticket\ThirdAttr();
            //    $res          = $thirdAttrBiz->thirdBind($ticketId, $ticketData['docking_url'], 0, 1, ' ', 0,
            //        $ticketData['account_id']);
            //    if ($res['code'] != 200) {
            //        pft_log('yunv/test', var_export($res, 1));
            //    }
            //}
        }

        $afterData = $this->_businessLib->afterHandle($productId, $ticketId, $ticketData, $this->_memberId,
            $packageCacheKey);

        if ($afterData[0] != 0) {
            $returnData['code'] = $afterData[0];
            $returnData['msg']  = $afterData[1];
        }

        if ($priceData) {
            $priceData = $this->handleInsurePrice($priceData, $ticketData);
            $res       = $this->_businessLib->savePrice($productId, $priceData, $ticketId);
//            //同步分销专员产品价格记录表
//            if ($res['code'] == 200) {
//                \Library\Resque\Queue::push('independent_system', 'MultiDist_Job',
//                    ['action' => 'update_product_price', 'data' => ['tid' => $ticketId]]);
//            }
            if ($res['code'] == 50051) {
                $cache = Cache::getInstance('redis');
                foreach ($priceData as $key => $price) {
                    $cacheData[] = [
                        'sid'            => $this->_memberId,
                        'pid'            => $productId,
                        'newDPrice'      => $price['ls'] - $price['js'],
                        'retailPrice'    => $price['old_retail_price'] ?? $price['ls'],
                        'costPrice'      => $price['old_cost_price'] ?? $price['js'],
                    ];
                }

                $tocken   = md5(json_encode($cacheData) . time());
                $cacheKey = "{$productId}:{$tocken}";
                $cache->set($cacheKey, json_encode($cacheData), '', 7210); // 两个小时多些
                $res['data'] = ['gid' => $productId, 'tocken' => $tocken];
            }

            $returnData['price'] = $res;
        }

        return $returnData;
    }

    /**
     * 年卡属性设置
     * 平台特性的逻辑放在PHP层处理(beforeHandle，afterHandle)，其他的在JAVA层
     *
     * <AUTHOR>  Li
     * @date   2022-06-09
     *
     * @param  array  $submitData  门票信息数组
     * @param  array  $ticketId  门票ID
     *
     * @return  array
     * @throws \Exception
     */
    public function _setTicketForAnnual($submitData, $ticketId)
    {
        if ($ticketId <= -1) {
            //创建门票
            $annualTicketRes = (new \Business\AnnualCard\TicketService())->createAnnualTicket($submitData, $this->_memberId, $this->_operatorId);
        } else {
            //编辑门票
            $annualTicketRes = (new \Business\AnnualCard\TicketService())->updateAnnualTicket($submitData, $ticketId, $this->_memberId, $this->_operatorId);
        }

        return $annualTicketRes;
    }

    /**
     * 特产门票(规格)发布接口
     * <AUTHOR>
     * @date   2017-12-11
     *
     * @param  array  $submitData  前端提交的门票信息
     */
    public function _setTicketForSpecialty($submitData)
    {
        $ticketData = [];
        foreach ($submitData as $item) {
            //规格参数
            $paramsObj    = $this->_getSpecialParams($item);
            $ticketData[] = $paramsObj;
        }

        $specialBiz = new Specialty();

        //保存规格
        $speRes = $specialBiz->saveSpecificationBatch(
            $this->_memberId,
            $this->_operatorId,
            $ticketData
        );

        $return = [
            'ticket' => $speRes,
            'price'  => ['code' => 200],
        ];

        if ($speRes['code'] == 200) {

            $tmpData = $speRes['data'];
            //保存价格
            $priceData = [];
            foreach ($submitData as $index => $item) {

                if ($item['tid'] && $item['pid']) {
                    [$tid, $pid] = [$item['tid'], $item['pid']];
                } else {
                    $tmp = array_shift($tmpData);
                    [$tid, $pid] = [$tmp['ticketId'], $tmp['productId']];
                }

                //对票类扩展属性修改或新增
                $item['type'] = 'J';
                //获取票类扩展属性
                $extParams = $this->_getExtParams($item, $tid);

                if (!empty($extParams)) {
                    //新增票类扩展属性
                    $extRes = $this->_saveExtParams($this->_memberId, $this->_operatorId, $extParams);
                }

                if ($extRes['code'] !== 200) {
                    pft_log('ticket_ext/fail', json_encode(['result:' . json_encode($extRes)]));
                }

                $priceData[] = [
                    'tid'          => (int)$tid,
                    'pid'          => (int)$pid,
                    'id'           => (int)$item['price_id'],
                    'storage'      => $item['storage'],
                    'retail_price' => $item['retail_price'],
                    'cost_price'   => $item['cost_price'],
                    'count_price'  => $item['market_price'],
                    'window_price' => $item['w_price'] * 100,
                ];

                //分销专员自供应产品更新通知
                if (\Business\MultiDist\Product::SELF_ACTION_SWITCH) {
                    \Business\MultiDist\Product::pushUpdataSelfByTon($tid, $this->_memberId);
                }

            }
            //保存价格信息
            $priceRes = $specialBiz->saveSpecialtyPriceBatch(
                $this->_memberId,
                $this->_operatorId,
                $priceData
            );

            // * <AUTHOR>
            // * @date   2018/07/12
            // * 如果将门票的退票属性修改位"不可退，而且可提现"，需要将已经生成的订单设置为可提现
            foreach ($ticketData as $ticketObj) {
                if ($ticketObj->tid > 0 && $ticketObj->refundRule == -1 && $ticketObj->refundRedisCheck == 1) {
                    $jobData = [
                        'job_type' => 'withdraw_order',
                        'job_data' => [
                            'tid' => $ticketObj->tid,
                        ],
                    ];
                    //插入队列
                    $jobId = \Library\Resque\Queue::push('ticket_system', 'Ticket_Job', $jobData);
                    //记录传递参数到日志中
                    pft_log('ticket_withdraw/debug', json_encode(['Ticket_Job', $jobId, $jobData]));
                }
            }
            $return['price'] = $priceRes;
        }

        return $return;
    }

    /**
     * 新建立套票的时候 获取选择打包的子票的信息
     *
     * @param $tids  门票id 逗号分隔
     *
     * @return string | json
     *
     */

    public function getSetPackSonTicketInfo()
    {
        $tids       = I('post.tids', '', 'strval');
        $parent_tid = I('post.parent_tid', 0, 'intval');
        $cacheKay   = I('post.flag', '', 'strval');

        if (empty($tids)) {
            $this->apiReturn(203, [], '参数缺少');
        }
        //新建套票，选择几个子票,在主票还没保存情况下使用，tids会传入null字符串
        if ($tids === 'null' && !$cacheKay) {
            $this->apiReturn(203, [], '获取数据失败，参数缺少');
        }
        $packBiz           = new \Business\Product\PackTicket();
        $sonTicketInfoJson = $packBiz->getCache($cacheKay);
        // $sonTicketInfoJson = $packBiz->getCache($this->_operatorId);
        $sonTicketInfoArr = [];
        if ($sonTicketInfoJson) {

            $tmpSonTicketInfoArr = json_decode($sonTicketInfoJson, true);
            $tmpSonTicketInfoArr = $tmpSonTicketInfoArr['package_data'];

            foreach ($tmpSonTicketInfoArr as $keyDel => $valueDel) {
                if ($valueDel['num'] == -1) {
                    unset($tmpSonTicketInfoArr[$keyDel]);
                }
            }

            $tidNumArr = [];
            if (is_array($tmpSonTicketInfoArr)) {
                foreach ($tmpSonTicketInfoArr as $sonVal) {
                    $tidNumArr[$sonVal['tid']]        = $sonVal['num'];
                    $sonTicketInfoArr[$sonVal['tid']] = $sonVal['num'] * $sonVal['cost_price'];
                }
            }
            $tidArr = array_column($tmpSonTicketInfoArr, 'tid');
        } else {
            if ($parent_tid) {
                //获取主票下子票的打包成本价
                $tmpSonTicketInfoArr = $packBiz->getTickets($parent_tid);
                if ($tmpSonTicketInfoArr) {
                    foreach ($tmpSonTicketInfoArr as $item) {
                        $tidNumArr[$item['tid']]        = $item['num'];
                        $sonTicketInfoArr[$item['tid']] = $item['num'] * $item['cost_price'];
                    }
                }
            }
            $tidArr = explode(',', $tids);
        }

        if (empty($tidArr)) {
            $this->apiReturn(203, [], '参数缺少');
        }

        $ticketModel   = new \Model\Product\Ticket();
        $ticketInfoArr = $ticketModel->getTicketInfoMulti($tidArr,
            'id, landid, title, delaytype, delaydays, order_start, order_end, if_verify, use_early_days, apply_did,
             ddays, buy_limit_low, buy_limit_up, chk_terminal_info, max_expiration_date, status, pid,refund_rule,
             refund_before_early_time,refund_after_early_time,refund_audit,refund_num,pre_sale,
             effective_limit,refund_least_num,ifprint_refund_rule');

        if (empty($ticketInfoArr)) {
            $this->apiReturn(205, [], '数据有误');
        }

        $landIdArr   = array_column($ticketInfoArr, 'landid');
        $javaAPi     = new \Business\CommodityCenter\Land();
        $landRes     = $javaAPi->queryLandMultiQueryById($landIdArr);
        $landInfoArr = array_column($landRes, null, 'id');
        $buyLimitRes = (new \Business\BuyLimit\Index())->queryLandLimitConfigByTicketIds($tidArr);
        $buyLimit = [];
        if ($buyLimitRes['code'] == 200 && $buyLimitRes['data']) {
            foreach ($buyLimitRes['data'] as $buyitem){
                if (isset($buyitem['ticketId'])) {
                    $buyLimit[$buyitem['ticketId']] = $buyitem;
                }
            }
            // $buyLimit = array_column($buyLimitRes['data'], null, 'ticketId');
        }

        //获取是否开启分时
        $timeShareBiz        = new \Business\Product\TimeShare();
        $ticketIds   = array_column($ticketInfoArr, 'id');
        $timeTypeMap = [];
        $resultTime  = $timeShareBiz->querySectionEnableByTids($ticketIds);
        if ($resultTime['code'] == 200 && !empty($resultTime['data'])) {
            $timeTypeMap = array_key($resultTime['data'], 'ticketId');
        }

        $packageInfoMap = [];
        $packageBiz   = new PackRelation();
        $packageInfoRes  = $packageBiz->getPackageTicketAttrList($parent_tid);
        if ($packageInfoRes['code'] == 200 && !empty($packageInfoRes['data'])) {
            $packageInfoMap = array_key($packageInfoRes['data'], 'tid');
        }

        $data                     = [];
        $validTime                = [];
        $tmpS                     = 0;
        $tmpE                     = 0;
        $delaydays                = 0;
        $delaydaysAfter           = 0;
        $useEarlyDays             = 0;
        $data['is_self_supply']   = 1;
        $data['total_cost_price'] = 0;
        $isChange                 = false;
        foreach ($ticketInfoArr as $item) {
            $num = '';
            if (isset($tidNumArr[$item['id']])) {
                $num = $tidNumArr[$item['id']];
            }

            $ltitle = '';
            $type   = 'A';
            if (isset($landInfoArr[$item['landid']]['title'])) {
                $ltitle = $landInfoArr[$item['landid']]['title'];
            }

            if (isset($landInfoArr[$item['landid']]['p_type'])) {
                $type = $landInfoArr[$item['landid']]['p_type'];
            }

            $isMy = 2;//是否自供应
            if ($this->_memberId == $item['apply_did']) {
                $isMy = 1;
            }

            //多景点配置参数转换
            $checkScenicInfo = $this->_businessLib->handleCheckScenicInfo($item['chk_terminal_info']);

            //多终端新版参数转换
            $checkTerminalInfoV2 = !empty($item['chk_terminal_info']) ? json_decode($item['chk_terminal_info'],
                JSON_UNESCAPED_UNICODE) : 0;

            $checkTerminalInfoRes  = $this->_businessLib->handleTerminalInfoBySelect($item['chk_terminal_info']);
            $checkTerminalInfo     = $checkTerminalInfoRes['info'];
            $checkTerminalTimeInfo = $checkTerminalInfoRes['checkTimeInfo'];

            //判断是否过期
            $isExpire = 0;
            if (!empty($item['max_expiration_date']) && time() > strtotime(date('Y-m-d 23:59:59', strtotime($item['max_expiration_date'])))) {
                $isExpire = 1;
            }

            //分时标识
            $timeShare = 0;
            if (isset($timeTypeMap[$item['id']]) && !$timeTypeMap[$item['id']]['status']) {
                $timeShare = 1;
            }
            $itmeBuyLimiInfo = [];
            //赋值购票限制类型 排除掉产品限制
            if (isset($buyLimit[$item['id']]) && $buyLimit[$item['id']]['limitConfigType'] == 1) {
                $itmeBuyLimiInfo = $buyLimit[$item['id']];
            }
            $data['ticket'][$item['id']] = [
                'ttitle'                   => $item['title'],
                'ltitle'                   => $ltitle,
                'valid_period_type'        => $item['delaytype'],
                'valid_period_days'        => $item['delaydays'],
                'valid_period_start'       => $item['order_start'],
                'valid_period_end'         => $item['order_end'],
                'use_early_days'           => $item['use_early_days'],
                'use_period_timecancheck'  => $item['if_verify'],
                'num'                      => $num,
                'lid'                      => $item['landid'],
                'p_type'                   => $type,
                'tid'                      => $item['id'],
                'is_my'                    => $isMy,
                'cost_price'               => isset($sonTicketInfoArr[$item['id']]) ? $sonTicketInfoArr[$item['id']] : 0,
                'preorder_early_days'      => $item['ddays'],
                'preorder_expire_time'     => $item['dhour'],
                'buy_min_amount'           => $item['buy_limit_low'],
                'buy_max_amount'           => $item['buy_limit_up'],
                'buy_limit_type'           => $item['buy_limit'],
                'buy_limit_period'         => $item['buy_limit_date'],
                'buy_limit_num'            => $item['buy_limit_num'],
                'identity_info'            => $item['tourist_info'],
                'age_limit_max'            => $item['age_limit_max'],
                'age_limit_min'            => $item['age_limit_min'],
                'more_credentials'         => !empty($item['more_credentials']) ? 1 : 0,
                'more_credentials_content' => empty($item['more_credentials']) ? [] : json_decode($item['more_credentials'],
                    true),
                'check_scenic_info'        => $checkScenicInfo,
                'check_terminal_info_v2'   => $checkTerminalInfoV2,
                'check_terminal_info'      => $checkTerminalInfo,
                'check_terminal_time_info' => $checkTerminalTimeInfo,
                'timeShare'                => $timeShare,//是否开启分时 0=否 1=是
                'status'                   => $item['status'], //2=下架
                'isExpire'                 => $isExpire, //是否过期 0=否 1=是
                'pid'                      => $item['pid'],
                'aid'                      => $packageInfoMap[$item['id']]['aid'] ?? 0,
                'buy_limit_info'           => $itmeBuyLimiInfo,
                'refund_rule'               => $item['refund_rule'] ?? 0,
                'refund_before_early_time'  => $item['refund_before_early_time'] ?? 0,
                'refund_after_early_time'   => $item['refund_after_early_time'] ?? 0,
                'refund_audit'              => $item['refund_audit'] ?? 0,
                'refund_num'                => (int)$item['refund_num'] ?? -1,
                'effective_limit'           => $item['effective_limit'] ?? '',
                'pre_sale'                  => $item['pre_sale'] ?? 0,
                'refund_least_num'          => $item['refund_least_num'] ?? 0,
                'ifprint_refund_rule'       => $item['ifprint_refund_rule'] ?? 1,
            ];


            $data['total_cost_price'] += $data['ticket'][$item['id']]['cost_price'];

            //子票不是自供应
            if ($isMy == 2 && !$isChange) {
                $data['is_self_supply'] = 2;
                $isChange               = true;
            }

            //todo 期票和非期票的场景需要拆分下
            //这次只处理下单后X天有效的区分逻辑，其他逻辑和之前保持一致
            if ($item['pre_sale'] == 1) {
                //期票的情况
                if (strtotime($item['order_start']) > 0) {
                    //时间段内有效
                    if ($tmpS == 0) {
                        $tmpS = date('Y-m-d', strtotime($item['order_start']));
                    }
                    $tmpS         = date('Y-m-d', strtotime($item['order_start'])) > $tmpS ? $tmpS : date('Y-m-d',
                        strtotime($item['order_start']));
                    $tmpE         = date('Y-m-d', strtotime($item['order_end'])) > $tmpE ? date('Y-m-d',
                        strtotime($item['order_end'])) : $tmpE;
                    $validTime[1] = $tmpS . '~' . $tmpE . '有效';
                } elseif ($item['delaytype'] == 1) {
                    //下单后X天有效
                    $delaydaysAfter = $delaydaysAfter < $item['delaydays'] ? $item['delaydays'] : $delaydaysAfter;
                    if ($delaydaysAfter == 0) {
                        $validTime[2] = "下单后当天有效";
                    } elseif ($item['delaydays'] && !$item['use_early_days']) {
                        $validTime[2]   = "下单后{$delaydaysAfter}天内有效";
                    }
                } elseif ($item['delaydays'] || $item['use_early_days']) {
                    $useEarlyDays = $useEarlyDays < $item['use_early_days'] ? $item['use_early_days'] : $useEarlyDays;
                    $delaydays    = $delaydays < $item['delaydays'] ? $item['delaydays'] : $delaydays;
                    $validTime[3] = "游玩日期前(含){$useEarlyDays}天有效,后{$delaydays}天有效";
                } else {
                    //当天有效
                    $validTime[4] = '游玩日期当天有效';
                }
            } else {
                //非期票的情况
                if (strtotime($item['order_start']) > 0) {
                    //时间段内有效
                    if ($tmpS == 0) {
                        $tmpS = date('Y-m-d', strtotime($item['order_start']));
                    }
                    $tmpS         = date('Y-m-d', strtotime($item['order_start'])) > $tmpS ? $tmpS : date('Y-m-d',
                        strtotime($item['order_start']));
                    $tmpE         = date('Y-m-d', strtotime($item['order_end'])) > $tmpE ? date('Y-m-d',
                        strtotime($item['order_end'])) : $tmpE;
                    $validTime[1] = $tmpS . '~' . $tmpE . '有效';
                } elseif ($item['delaytype'] == 1) {
                    //下单后X天有效
                    $delaydaysAfter = $delaydaysAfter < $item['delaydays'] ? $item['delaydays'] : $delaydaysAfter;
                    if ($delaydaysAfter == 0) {
                        $validTime[2] = "游玩日期当天有效";
                    } elseif ($item['delaydays'] && !$item['use_early_days']) {
                        $validTime[2]   = "下单后{$delaydaysAfter}天内有效";
                    }
                } elseif ($item['delaydays'] || $item['use_early_days']) {
                    $useEarlyDays = $useEarlyDays < $item['use_early_days'] ? $item['use_early_days'] : $useEarlyDays;
                    $delaydays    = $delaydays < $item['delaydays'] ? $item['delaydays'] : $delaydays;
                    $validTime[3] = "游玩日期前(含){$useEarlyDays}天有效,后{$delaydays}天有效";
                } else {
                    //当天有效
                    $validTime[4] = '游玩日期当天有效';
                }
            }
        }

        $validTime         = array_unique($validTime);
        $validTimeStr      = implode('+', $validTime);
        $data['valid_tip'] = $validTimeStr;

        //酒店添加房型数据
        $hotelLib       = new Hotel();
        $data['ticket'] = $hotelLib->handleRoomParamsPack($data['ticket']);

        //子票包含演出票处理
        $data = $packBiz->handlePacketSonShowParams($data, $this->_memberId);

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 获取特产规格参数
     * <AUTHOR>
     * @date   2017-12-11
     *
     * @param  array  $submitData  一种规格的数据
     *
     * @return object
     */
    private function _getSpecialParams($submitData)
    {

        $paramsObj = new \stdClass();
        //特产名称
        $paramsObj->name = $submitData['name'] ?: '';
        //支付方式
        $paramsObj->payWay = $submitData['pay_way'] ?: 0;
        //多少分支未支付自动取消
        $paramsObj->cancelMinutes = $submitData['cancel_minutes'] ?: 120;
        //退票规则
        $paramsObj->refundRule = $submitData['refund_rule'] ?: 2;
        //快递配送方式
        $paramsObj->expressWay = $submitData['express_way'] ?: 1;
        //是否开启定点取货
        $paramsObj->specialDot = $submitData['special_dot'] ?: 0;
        //是否开启快递
        $paramsObj->checkedExpress = $submitData['checked_express'] ?: 0;
        //收货地址
        $paramsObj->pickUpAddr = $submitData['pick_up_addr'] ?: '';
        //收货联系人
        $paramsObj->pickUpContact = $submitData['pick_up_contact'] ?: '';
        //过期自动取消天数
        $paramsObj->expireDays = $submitData['expire_days'] ?: 0;
        // 过期处理方式
        $paramsObj->expireAction = $submitData['expire_action'] ?: 2;
        //规格信息
        $paramsObj->specification = $submitData['specification'] ?: '';
        //上架、仓库
        $paramsObj->status = (int)$submitData['status'];
        //供应商联系手机
        $paramsObj->supplierTel = $submitData['supplier_tel'] ?: '';
        //下单通知买家
        $paramsObj->orderBuyerSms = $submitData['order_buyer_sms'] ?: 0;
        //下单通知卖家
        $paramsObj->orderSupplierSms = $submitData['order_supplier_sms'] ?: 0;
        //下单微信通知卖家
        $paramsObj->orderSupplierWx = $submitData['order_supplier_wx'] ?: 0;
        //取消通知买家
        $paramsObj->cancelBuyerSms = $submitData['cancel_buyer_sms'] ?: 0;
        //取消通知卖家
        $paramsObj->cancelSupplierSms = $submitData['cancel_supplier_sms'] ?: 0;
        //发货通买家
        $paramsObj->deliveryBuyerSms = $submitData['delivery_buyer_sms'] ?: 0;
        //发货通知卖家
        $paramsObj->deliverySupplierSms = $submitData['delivery_supplier_sms'] ?: 0;
        //单份重量
        $paramsObj->singleWeight = $submitData['single_weight'] ?: 0;
        //门市价
        $paramsObj->marketPrice = $submitData['market_price'] ?: 0;
        //成本价
        $paramsObj->costPrice = $submitData['cost_price'] ?: 0;
        //零售价
        $paramsObj->retailPrice = $submitData['retail_price'] ?: 0;
        //窗口价
        $paramsObj->windowPrice = $submitData['w_price'] ? $submitData['w_price'] * 100 : 0;
        //库存
        $paramsObj->storage = $submitData['storage'] ?: 0;
        //产品id
        $paramsObj->lid = $submitData['lid'] ?: 0;
        //门票tid
        $paramsObj->tid = $submitData['tid'] ?: 0;
        //记录id
        $paramsObj->id = $submitData['id'] ?: 0;
        //销售渠道
        $paramsObj->shop = $submitData['shop'] ?: '';

        // * <AUTHOR>
        // * @date   2018/07/06
        //退票规则新增  -1不可退，而且是可以提现
        if ($submitData["refund_rule"] == 2 && $submitData["refund_rule_bool"] == 1) {
            $paramsObj->refundRule = -1;            //表示不可退，而且是可以提现
        }

        if ($submitData["id"] > 0) {
            $ticketBiz = new \Business\CommodityCenter\Ticket();
            $queryInfo = $ticketBiz->fieldConversion($ticketBiz->queryTicketInfoById($submitData['tid']));
            //$ticketApi = new \Business\JavaApi\TicketApi;
            //$queryInfo = $ticketApi->getTickets($submitData['tid']);
            if ($queryInfo["refund_rule"] == -1) {
                $paramsObj->refundRule = $queryInfo["refund_rule"];  //退票规则
            } else {
                $paramsObj->refundRedisCheck = 1;    //用在后置方法这个判断是否加入redis队列
            }
        }

        return $paramsObj;
    }

    /**
     * 上下架恢复删除门票
     *
     * @date   2017-07-20
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function setTicketStatus()
    {
        $ticketId = I('post.ticket_id', '', 'intval');
        $action   = I('post.action', '', 'strval');

        if ($ticketId === '' || !in_array($action, ['deleteTicket', 'recoveryTicket', 'listTicket', 'deListTicket'])) {
            $this->apiReturn(203, [], '参数错误');
        }

        $ticketBiz  = new \Business\CommodityCenter\Ticket();
        $ticketInfo = $ticketBiz->queryTicketById($ticketId, 'landid, pre_sale, apply_did');

        if (empty($ticketInfo)) {
            $this->apiReturn(500, [], '查询票信息错误');
        }
        $subSid = 0;
        $sid    = $this->_memberId;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $sid    = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        if ($ticketInfo['apply_did'] != $sid) {
            $this->apiReturn(404, [], '非法操作，不是当前门票的供应商');
        }

        // 上架的时候判断预售票 和分时预约
        if ($action == 'listTicket' && $ticketInfo['pre_sale'] == 1) {
            $timeShareOrderBiz  = new \Business\Order\TimeShareOrder();
            $isOpenShareTimeArr = $timeShareOrderBiz->ifOpenShareTime([$ticketInfo['landid']]);
            if ($isOpenShareTimeArr['data'][$ticketInfo['landid']] == 1) {
                $this->apiReturn(203, [], '产品已开启分时预约');
            }
        }

        $res = $this->_businessLib->setTicketStatus($ticketId, $this->_operatorId, $action, $subSid);

        //删除票类扩展 -- 上架后不再删除票扩展属性
        //$ticketExtConf = new \Business\JavaApi\Ticket\TicketExtendAttr();
        //$result        = $ticketExtConf->delete($ticketId);

        if ($res) {
            $this->apiReturn(200, [], 'success');
        }
        $this->apiReturn(204, [], 'fail');
    }

    /**
     * 批量删除门票
     * <AUTHOR>
     * @date   2017-12-18
     */
    public function batchDelete()
    {
        $tids = I('tids', '');

        if (!$tids) {
            $this->apiReturn(203, [], '参数错误');
        }

        $result = $this->_businessLib->batchDelete($tids, $this->_memberId, $this->_operatorId);

        //批量删除票类扩展
        $ticketExtConf = new \Business\JavaApi\Ticket\TicketExtendAttr();
        $ticketIdArr   = explode(',', $tids);
        $res           = $ticketExtConf->batchDelete($ticketIdArr, $this->_memberId, $this->_operatorId);

        if ($res['code'] !== 200) {
            pft_log('ticket_batchDelete/fail',
                json_encode(['result:' . json_encode($res), 'ticketIdArr:' . json_encode($ticketIdArr)]));
        }

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取门票信息
     *
     * @date   2017-07-20
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function getTickets()
    {
        $ticketId = I('post.ticket_id', 0, 'intval');
        $delFlag  = I('post.del_flag', 0, 'intval');//默认不显示
        $type     = I('type', 'common', 'strval');//产品类型
        $lid      = I('lid', 0, 'intval');

        switch ($type) {
            case 'specialty':
                //特产产品
                if (!$lid) {
                    $this->apiReturn(203, [], '参数错误');
                }
                //访问来源，0仓库/1出售中
                $source = I('source', 1, 'intval');
                $result = $this->_getTicketsForSpecialty($lid, $ticketId);
                break;

            default:
                if (!$ticketId || $ticketId <= 0) {
                    $this->apiReturn(203, [], '参数错误');
                }
                $result = $this->_getTicketsForCommon($ticketId, $delFlag);
                break;
        }
        if ($result['status'] == 0) {
            $this->apiReturn(204, [], $result['msg']);
        } else {
            $this->apiReturn(200, $result['data'], 'success');
        }

    }

    public function getTicketSnapshot()
    {
        $ticketId = I('post.ticket_id', 0, 'intval');
        $version  = I('version', 1, 'intval');

        $snapShotRes = (new \Business\JavaApi\Ticket\TicketSnapshot())->queryTicketSnapshot($ticketId, $version);
        if ($snapShotRes['code'] !== 200) {
            $this->apiReturn(500, [], '接口出错');
        }

        if ($snapShotRes['data']['uuJqTicketDTO']['applyDid'] !== $this->_memberId) {
            $this->apiReturn(401, [], '非法操作，不是当前门票的供应商');
        }

        $formatData = [];
        // 字段映射配置
        $fieldMapping = [
            'imgpathgrp' => 'img_path_group',
            'resourceid' => 'resource_id',
            'sale16u' => 'sale_16u',
            'sendvoucher' => 'send_voucher',
            'cancelAutoOnmin' => 'nopay_max_minu',
            'expire_before_notice_to_supplier' => 'ticket_sms_expire',
        ];
        foreach ($snapShotRes['data'] as $k => $v) {
            if (!is_array($v)) {
                continue;
            }
            foreach ($v as $kk => $vv) {
                // 从配置中获取映射后的字段名
                $mappedKey = $fieldMapping[$kk] ?? camelToSnake($kk);
                $formatData[$k][$mappedKey] = $vv;
            }
        }
        $data = [];
        $data['ticket'] = (new \Business\CommodityCenter\Ticket())->fieldConversion([
            'ticket'  => $formatData['uuJqTicketDTO'],
            'product' => $formatData['uuProductDTO'],
            'land'    => $formatData['uuLandDTO'],
            'land_f'  => $formatData['uuLandFDTO'],
            'ext'     => $formatData['confs'],
            'special' => $formatData['specialTicketDTO'],
        ]);
        foreach ($data['ticket']['ext'] as $k => $v) {
            $data['ticket'][$k] = $v;
        }

        // 根据产品类型获取产品的额外信息
        $otherData = $this->_businessLib->_getDataByType($ticketId, $data['ticket']);
        if ($otherData[0] == 0) {
            if ($otherData[1]) {
                if ($data['ticket']['refund_rule'] == -1) {
                    $otherData[1]['refund_rule'] = $data['ticket']['refund_rule'];
                }
                $data['ticket'] = array_merge($data['ticket'], $otherData[1]);
                unset($data['ticket']['touring_party_no']);
            }
        }

        // 取票打印规则
        $printMode = $data['ticket']['ext']['print_mode'] ?? 3;
        $checkMode = $data['ticket']['ext']['check_mode'] ?? 1;
        $orderMode = $data['ticket']['ext']['order_mode'] ?? 0;
        // 如果用户选择了 一人一票的打印规则 和 分批入园， 则验证方式改为 分批入园，一人一票，输出数据时反过来
        if ($printMode == 4 && $checkMode == 3) {
            $checkMode = 2;
        }
        $data['ticket']['ticket_print_check_rule'] = [
            'print_mode' => $printMode,
            'check_mode' => $checkMode,
            'order_mode' => $orderMode,
        ];

        unset($data['ticket']['ext']);
        $data['ticket_title'] = $data['ticket']['name'];

        foreach (load_config('sale_channel') as $k => $v) {
            $data['channel_map'][] = ['id' => $k, 'name' => $v];
        }

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 获取门票信息
     *
     * @date   2017-07-20
     * @return array|string
     *<AUTHOR> Lan
     *
     */
    private function _getTicketsForCommon($ticketId, $delFlag)
    {

        $result = $this->_businessLib->getTickets($ticketId, $delFlag);

        if ($result === false) {
            return ['status' => 0, 'msg' => '没有相关数据'];
        } elseif ($result[0] != 0) {
            return ['status' => 0, 'msg' => $result[1]];
        }

        $data = $result[1];

        //人脸配置检测
        $data['ticket']['is_face'] = 0;
        $lid                       = $data['ticket']['item_id'];
        $landModel                 = new Land();
        $landInfo                  = $landModel->getLandInfo($lid, false, 'terminal');//根据lid取终端号
        $terminalId                = $landInfo['terminal'];
        $nowTerminal               = $landModel->getNowTerminal($terminalId);
        if ($nowTerminal) {
            //如果有合并终端的，取合并后的终端
            $terminalId = $nowTerminal;
        }
        $faceModel    = new \Model\Terminal\FaceCompare();
        $facePlatform = $faceModel->getFacePlatform(false, $terminalId);
        if ($facePlatform) {
            $data['ticket']['is_face'] = 1;
        }

        $data['ticket']['time_share'] = 0;
        if (isset($data['ticket']['type']) && $data['ticket']['type'] == 'A') {
            //判断票是否有开始分时预约功能 0=不开启 1=开启
            $timeShareBiz = new \Business\Product\TimeShare();
            $timeShare    = $timeShareBiz->judgeOpenTimeForTicket($lid, $ticketId);
            if ($timeShare['code'] == 200 && !empty($timeShare['data'])) {
                $data['ticket']['time_share'] = $timeShare['data']['is_open'];
            }
        }

        // 获取期票设置的总库存和已售数量
        if (!empty($data['ticket']['pre_sale'])) {
            $storageInfo = StorageApi::getTotalStorage($ticketId);
            if (empty($storageInfo) || ($storageInfo['storage'] == -1 && $storageInfo['storage_open'] == null) || empty($storageInfo['status'])) {
                $data['ticket']['pre_storage'] = [
                    'storage'   => -1,
                    'soldCount' => -1,
                    'openDate'  => '',
                ];
            } else {
                $data['ticket']['pre_storage'] = [
                    'storage'   => $storageInfo['storage'],
                    'soldCount' => $storageInfo['soldCount'] - $storageInfo['returnCount'],
                    'openDate'  => $storageInfo['openDate'],
                ];
            }
        }

        //处理票类扩展属性
        if (isset($data['ticket']['ext']) && !empty($data['ticket']['ext'])) {
            //年卡扩展属性特殊处理
            if ($data['ticket']['type'] == 'I') {
                //年卡身份证信息  0 非必填 1激活必填 2下单必填
                $annualIdentityInfo                               = isset($data['ticket']['ext']['annual_identity_info']) ? (int)$data['ticket']['ext']['annual_identity_info'] : 0;
                $data['ticket']['ext']['is_need_wirte']           = isset($data['ticket']['ext']['is_need_wirte']) ? (int)$data['ticket']['ext']['is_need_wirte'] : 0;
                $data['ticket']['ext']['annual_identity_info']    = $annualIdentityInfo;
                $data['ticket']['ext']['book_active']             = isset($data['ticket']['ext']['book_active']) ? (int)$data['ticket']['ext']['book_active'] : 1;
                $data['ticket']['ext']['book_active_channels']    = isset($data['ticket']['ext']['book_active_channels']) ? $data['ticket']['ext']['book_active_channels'] : '2,3';
                $data['ticket']['ext']['annual_pact']             = isset($data['ticket']['ext']['annual_pact']) ? $data['ticket']['ext']['annual_pact'] : '';
                $data['ticket']['ext']['annual_valid_start']      = isset($data['ticket']['ext']['annual_valid_start']) ? $data['ticket']['ext']['annual_valid_start'] : '';
                $data['ticket']['ext']['annual_valid_end']        = isset($data['ticket']['ext']['annual_valid_end']) ? $data['ticket']['ext']['annual_valid_end'] : '';
                $data['ticket']['ext']['annual_active_to_member'] = isset($data['ticket']['ext']['annual_active_to_member']) ? $data['ticket']['ext']['annual_active_to_member'] : 0;
                $data['ticket']['ext']['available_time_period']   = isset($data['ticket']['ext']['available_time_period']) && $data['ticket']['ext']['available_time_period'] != '' ? array_map('intval', explode(',', $data['ticket']['ext']['available_time_period'])) : [];
                $data['ticket']['ext']['annual_renewal_type']     = isset($data['ticket']['ext']['annual_renewal_type']) ? (int)$data['ticket']['ext']['annual_renewal_type'] : 1;

                $annualPackageBiz = new \Business\AnnualCard\Package();
                $annualProductBiz = new \Business\AnnualCard\ProductBiz();

                //指定套餐的情况 需要获取一次续费关联套餐
                $renewPackageTidArr = [];
                if ($data['ticket']['ext']['annual_renewal_type'] == 2) {
                    //获取续费关联套餐
                    $packageRes = $annualPackageBiz->getTicketRelationByParentTid($ticketId, 1, [0, 1, 2]);
                    if ($packageRes['code'] == 200 && !empty($packageRes['data'])) {
                        $renewPackageTidArr[] = $packageRes['data'][0]['tid'];
                    }
                }

                //处理关联关系门票
                $data['ticket']['package_relation']['renewal'] = [];
                if ($renewPackageTidArr) {
                    $packageTicket = $annualProductBiz->checkTickerRelationStatus($ticketId, $renewPackageTidArr);
                    $data['ticket']['package_relation']['renewal'] = $packageTicket['code'] == 200 && !empty($packageTicket['data']) ? $packageTicket['data'] : [];
                }

                //获取升级关联套餐
                $upgradeTidArr = [];
                $packageRes = $annualPackageBiz->getTicketRelationByParentTid($ticketId, 2, [0, 1, 2]);
                if ($packageRes['code'] == 200 && !empty($packageRes['data'])) {
                    $upgradeTidArr = array_column($packageRes['data'], 'tid');
                }

                //处理关联关系门票
                $data['ticket']['package_relation']['upgrade'] = [];
                if ($upgradeTidArr) {
                    $packageTicket = $annualProductBiz->checkTickerRelationStatus($ticketId, $upgradeTidArr, 2);
                    $data['ticket']['package_relation']['upgrade'] = $packageTicket['code'] == 200 && !empty($packageTicket['data']) ? $packageTicket['data'] : [];
                }
            }

            foreach ($data['ticket']['ext'] as $key => $value) {

                /* 由于入园核销分离版本中的核销规则未对所有用户开放，
                 * 目前使用该字段值区分是否使用了入园核销分离版本
                 * 如果返回空字符串其实是未设置该选项，不返回前端，否则前端会提交上来
                 */
                if ($key == 'charge_off_rule' && $value === '') {
                    continue;
                }

                $data['ticket'][$key] = $value;
            }

            //演出扩展属性特殊处理
            $data['ticket']['before_show_hour']      = 0;
            $data['ticket']['before_show_minute']    = 0;
            $data['ticket']['show_order_limit_time'] = [];
            if ($data['ticket']['type'] == 'H') {
                if (!empty($data['ticket']['ext']['before_show_time'])) {
                    $data['ticket']['before_show_hour']   = floor($data['ticket']['ext']['before_show_time'] / 60);
                    $data['ticket']['before_show_minute'] = ($data['ticket']['ext']['before_show_time'] % 60);
                }

                if (isset($data['ticket']['ext']['show_order_limit_time'])) {
                    $data['ticket']['show_order_limit_time'] = json_decode($data['ticket']['ext']['show_order_limit_time'], true);
                }
            }

            unset($data['ticket']['ext']);
        } else {
            unset($data['ticket']['ext']);
        }

        if ($data['ticket']['type'] == 'H') {
            //获取下演出场次信息
            $roundList = (new \Model\Product\Show())->getRoundList($data['ticket']['venus_id'], 'id,round_name,bt,et',
                $status = 0, date('Y-m-d'), date('Y-m-d'));
            if ($roundList) {
                //拿出最晚场次的结束时间
                $etArr = array_column($roundList, 'et');
                //按照结束时间倒叙下
                arsort($etArr);
                //解析时间点
                $etArr                            = array_values($etArr);
                $data['ticket']['round_end_time'] = explode(':', $etArr[0])[0];
            }
        }

        if (!$this->isSuper() && $data['ticket']['account_id'] != $this->_memberId) {
            return ['status' => 0, 'msg' => '非自身供应产品，无权限查看'];
        }
        //多景点配置白名单判断
        $data['is_chk_white'] = $this->judgeChkWhite();

        //处理票类填写更多用户信息
        if ($data['ticket']['more_credentials']) {
            $data['ticket']['more_credentials'] = json_decode($data['ticket']['more_credentials'], true) ?? '';
            if (!empty($data['ticket']['more_credentials'])) {
                $moreCredentialStatus                       = $data['ticket']['more_credentials']['type'];
                $data['ticket']['more_credentials_content'] = $data['ticket']['more_credentials']['content'];
                $data['ticket']['more_credentials']         = $moreCredentialStatus;
                foreach ($data['ticket']['more_credentials_content'] as &$item) {
                    $item['target'] = intval($item['target']);
                    $item['name']   = intval($item['name']);
                }
            }
        } else {
            $data['ticket']['more_credentials'] = 0;
        }

        //处理票类自动起售停售
        if ($data['ticket']['auto_sale_rule']) {
            $data['ticket']['auto_sale_rule'] = json_decode($data['ticket']['auto_sale_rule'], true) ?? '';
            if (!empty($data['ticket']['auto_sale_rule']['start']) && !empty($data['ticket']['auto_sale_rule']['end'])) {
                $data['ticket']['rule_status'] = 5;
            }
        }
        //自定义标签处理
        $data = $this->_handleCustumTab($data);

        if (!isset($data['ticket']['quick_verify'])) {
            $data['ticket']['quick_verify'] = 0;
        }

        //附加票
        $data = $this->_handleTicketDeputy($ticketId, $data);

        return ['status' => 1, 'data' => $data];
    }

    /**
     * 获取特产规格列表
     * <AUTHOR>
     * @date   2017-12-12
     *
     * @param  int  $lid  产品id
     * @param  int  $ticketId  门票id
     *
     * @return array
     */
    private function _getTicketsForSpecialty($lid, $ticketId)
    {
        //获取产品名称
        $landModel = new Land('slave');
        $landInfo  = $landModel->getLandInfo($lid, false, 'title');

        if (!$landInfo) {
            return [
                'status' => 0,
                'msg'    => '未找到产品信息',
            ];
        }

        $ticModel = new Ticket('slave');

        $source = 1;
        if ($ticketId) {
            $ticket = $ticModel->getTicketInfoById($ticketId, 'status');
            if (!$ticket) {
                return [
                    'status' => 0,
                    'msg'    => '未找到规格信息',
                ];
            }

            if ($ticket['status'] != 1) {
                $source = 0;
            }
        }

        $specialBiz = new Specialty();
        $result     = $specialBiz->getSpecificationList($this->_memberId, $lid, $source);

        //规格列表
        $data['list'] = $result['data'] ?: [];
        //商品名称
        $data['title'] = $landInfo['title'];

        return [
            'status' => 1,
            'data'   => $data,
        ];
    }

    /**
     * 获取景区基本信息
     *
     * @date   2017-07-28
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function getLandInfo()
    {
        $landId = I('post.lid', '', 'intval');

        if ($landId === '') {
            $this->apiReturn(202, [], '参数错误');
        }

        //$landModel = new Land();
        //
        //$res = $landModel->getInfoInLand('order_flag, fax,title as ltitle,p_type, apply_did, venus_id',
        //    ['id' => $landId]);

        $landApi = new \Business\CommodityCenter\Land();
        $res  = $landApi->queryLandMultiQueryById([$landId]);

        if ($res = $res[0]) {
            $res = [
                'order_flag' => $res['order_flag'],
                'fax'        => $res['fax'],
                'ltitle'     => $res['title'],
                'p_type'     => $res['p_type'],
                'apply_did'  => $res['apply_did'],
                'venus_id'   => $res['venus_id'],
            ];

            if ($res['p_type'] == 'H') {
                $venusArr = $this->_businessLib->getVenue($res['venus_id']);
                $res      = array_merge($res, $venusArr);
            }

            //返回门票渠道
            $channelMap = \load_config('sale_channel');
            foreach ($channelMap as $idx => $name) {
                $res['channel_map'][] = [
                    'id'   => $idx,
                    'name' => $name,
                ];
            }

            //判断供应商是否有开票的权限
            $invoiceApi = new InvoiceApi();
            $result     = $invoiceApi->getEnterpriseOpenRecordsByMixed($res['apply_did']);
            if ($result['code'] == 200) {
                $res['auth_invoice'] = 1;
            }

            $res['is_chk_white'] = $this->judgeChkWhite();
            // 获取景区是否有开启产品限制购票限制
            $landBuyLimit = (new \Business\BuyLimit\Index())->getBuyLimitInfoByLandId($res['apply_did'], $landId);
            if ($landBuyLimit['code'] == 200 && $landBuyLimit['data']) {
                $res['land_buy_limit'] = $landBuyLimit['data'];
                $res['land_buy_limit']['limitConfType'] = 2;
            }

            $this->apiReturn(200, $res, '');
        }

        $this->apiReturn(201, [], '没有相关数据');

    }

    /**
     * 获取分终端列表
     *
     * @date   2017-09-18
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function getSubTerminal()
    {
        $landId = I('post.item_id', '', 'intval');

        if ($landId === '') {
            $this->apiReturn(201, [], '景区ID不能为空');
        }

        $landModel = new Land();

        //$terminal = $landModel->getInfoInLand('terminal', ['id' => $landId], 1);
        $landApi  = new \Business\CommodityCenter\Land();
        $terminal = $landApi->queryLandMultiQueryById([$landId]);

        $terminalId = $terminal[0]['terminal'];
        $terminalShareApi  = new TerminalShare();
        $terminalInfo = [];
        $apiResult = $terminalShareApi->getSubTerminalListByMainTerminalOrderByPreTerminal($terminalId, true);
        if ($apiResult['code'] == 200){
            $terminalList = $apiResult['data'];
            foreach ($terminalList as $key => $value){
                if(!empty($value['preSalerID']) && ($value['preTerminal'] != $value['nowTerminal']) ){
                    // 过滤掉共享的终端。
                    continue;
                }
                // 需要把共享的给剔除出去。
                // if ($value['preTerminal'] != $terminalId){
//                    $terminalInfo[] = [
//                        'preTerminal' => $value['preTerminal'],
//                        'name'        => $value['name'],
//                    ];
                // }
                $terminalInfo[] = [
                    'preTerminal' => $value['preTerminal'],
                    'name'        => $value['name'],
                ];
            }
        }
        //$terminalInfo = $landModel->getSubTerminal($terminalId, 1);
        if(empty($terminalInfo) && !empty($terminalId)){
            // 如果为空说明没有开启子景点
            $terminalInfo[] = [
                'preTerminal' => strval($terminalId),
                'name' => TerminalConst::MAIN_TERMINAL_NAME,
            ];
        }

        if (!$terminalInfo) {
            $this->apiReturn(201, [], '暂无分终端信息');
        }
        $this->apiReturn(200, $terminalInfo, 'success');
    }

    /**
     * 获取日历价格等信息
     *
     * @date   2017-11-08
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function getCalendarData()
    {
        $ticketId = I('post.ticket_id', '', 'intval');
        $month    = I('post.month', '', 'strval');
        $aid      = I('post.aid', '', 'intval');
//        $ticketId = 31863;
//        $month = '2017-11';
        if (!$ticketId || !$month || !$aid) {
            $this->apiReturn(201, [], '参数有误');
        }
        $startDate = $month . '-01';
        $endDate   = $month . date('-t', strtotime($month));

        //日历模式的请求就是自己的
        if ($aid == -1) {
            $aid = $this->_memberId;
        }

        //$calendar = TicketApi::getCalendarPriceAndStorage($this->_memberId, $aid, $ticketId, $startDate, $endDate);  java参数字段调整  PeiJun Li 2018-09-27
        $calendar = TicketApi::getCalendarPriceAndStorage($ticketId, $startDate, $endDate, false);
        if (empty($calendar)) {
            $this->apiReturn(202, [], '暂无日历信息');
        }

        $reserveStorageService       = new ReserveStorage();
        $ticketReserveStorageContext = new TicketReserveStorageContext([
            'tid'     => $ticketId,
            'start'   => $calendar[0]['time'],
            'end'     => date('Y-m-d', mktime(23, 59, 59, date('m', strtotime($calendar[0]['time'])) + 1, 00, date('Y', strtotime($calendar[0]['time'])))),
            'channel' => 0,
            'querySale' => false
        ]);
        $reserveStorageResult        = $reserveStorageService->queryCalendarReserveStorage($ticketReserveStorageContext);
        if ($reserveStorageResult['code'] == 200) {
            $reserveStorageResult = $reserveStorageResult['data'];
        } else {
            $this->apiReturn(204, [], $reserveStorageResult['msg']);
        }

        //获取一次门票产品类型
        $ticketInfo = (new \Business\CommodityCenter\Ticket())->queryTicketInfoById($ticketId);
        if (empty($ticketInfo)) {
            $this->apiReturn(204, [], '门票信息异常');
        }
        $pType = $ticketInfo['land']['p_type'];

        //新接口返回数据字段不一样了，转换成旧接口的字段
        $calendarData = [];
        foreach ($calendar as $key => $value) {
            $calendarData[$value['time']] = [
                'id'               => $value['id'],
                'day_of_storage'   => $value['dayOfStorage'],
                'sold_storage'     => $value['soldStorage'],
                'settlement_price' => $value['settlementPrice'],
                'retail_price'     => $value['retailPrice'],
                'window_price'     => $value['windowPrice'],
                'counter_price'    => $value['counterPrice'],
            ];
            foreach ($reserveStorageResult as $reserveStorageKey => $reserveStorageValue) {
                if ($reserveStorageValue['time'] == $value['time']) {
                    $reserveStorage                                   = $reserveStorageValue['storage'] ?? 0;
                    $reservationCount                                 = $reserveStorageValue['reservationCount'] ?? 0;
                    $calendarData[$value['time']]['section_flag']     = $reserveStorageValue['sectionFlag'] ?? 0;
                    $calendarData[$value['time']]['reserve_storage']  = $pType != 'H' ? $reserveStorage : '';  //如果是演出就不返回这个期票预约库存
                    $calendarData[$value['time']]['reserved_storage'] = $reservationCount;
                }
            }
        }

        $this->apiReturn(200, $calendarData, 'success');
    }

    /**
     * 获取总库存信息
     *
     * @date   2017-11-08
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function getTotalstorage()
    {
        $ticketId = I('post.ticket_id', '', 'intval');

        if (!$ticketId) {
            $this->apiReturn('204', '', '参数有误');
        }

        $data = [];

        $storageInfo = StorageApi::getTotalStorage($ticketId);

        if (empty($storageInfo) || $storageInfo['storage'] == -1 && $storageInfo['storage_open'] == null) {
            $this->apiReturn('204', $data, '未配置总库存');
        } else {
            //获取固定有效期配置，如有返回库存配置操作屏蔽标识
//            $ticketBiz = new TicketBiz();
//            $hasSet = $ticketBiz->hasSetStorageInfoTicket($ticketId);
//            $storageInfo['setStorageInTicket'] = $hasSet;

            if (isset($storageInfo['openDate']) && $storageInfo['openDate']) {
                $timeStamp               = substr($storageInfo['openDate'], 0, 10);
                $storageInfo['openDate'] = date('Y-m-d', $timeStamp);
            } else {
                $storageInfo['openDate'] = '';
            }

            if (isset($storageInfo['storage'])) {
                if ($storageInfo['storage'] == -1) {
                    $storageInfo['storage'] = "";
                }
            }
            $data['storage'] = $storageInfo;
            $this->apiReturn('200', $data, '数据取得成功');
        }
    }

    /**
     * 设置计时设备
     * @Author: zhujb
     * 2018/9/13
     */
    public function setTimingEquips()
    {
        $action                  = I('post.action', 1, 'intval');
        $id                      = I('post.id', 0, 'intval');
        $data['equip_no']        = I('post.equip_no', '', 'strval');
        $data['equip_name']      = I('post.equip_name', '', 'strval');
        $data['equip_unit']      = I('post.equip_unit', '', 'strval');
        $data['equip_ac']        = I('post.equip_ac', '', 'strval,trim');
        $data['apply_did']       = $this->_memberId;
        $data['charge_standard'] = I('post.charge_standard', '', 'strval');

        // 处理收费数据
        $timingProductBiz        = new \Business\CardSolution\TimingProduct();
        $chargeData              = $timingProductBiz->_handleChargeData($data['charge_standard']);
        $data['charge_standard'] = json_encode($chargeData);

        if (!empty($data['equip_ac'])) {
            //判断显示设置的账号是否属于该账号的员工账号
            $mModel   = new \Model\Member\Member();
            $sonIds   = $mModel->getStaffByParentId($this->_memberId);
            $sonIdArr = array_column($sonIds, 'son_id');

            $api = new \Business\JavaApi\Member\MemberQuery();
            $res = $api->queryMemberByMemberQueryInfo(['idList' => $sonIdArr]);
            if ($res['code'] != 200 || !$res['data']) {
                $sonAcArr = [];
            } else {
                $sonAcArr = array_column(array_column($res['data'], 'memberInfo'), 'account');
            }

            $equipAc = explode(',', $data['equip_ac']);

            foreach ($equipAc as $key => $ac) {
                if (!in_array($ac, $sonAcArr)) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], $ac . '不是您的员工账号');
                }
            }
        }

        $timingEquipModel = new TimingProduct();

        switch ($action) {
            case 1:
                // 添加
                $equipsInfo = $timingEquipModel->selectTimingEquipment([], $data['equip_no']);
                if (!empty($equipsInfo)) {
                    $this->apiReturn(400, [], '设备号已存在');
                }
                $res = $timingEquipModel->addTimingEquip($data);
                break;
            case 2:
                //编辑
                $res = $timingEquipModel->editTimingEquip($id, $data);
                break;
            case 3:
                //删除
                $res = $timingEquipModel->deleteTimingEquip($id);
                break;
            default :
                $res = false;
        }

        if ($res !== false) {
            $this->apiReturn(200, [], '设置成功');
        }
        $this->apiReturn(400, [], '设置失败');
    }

    /**
     * 计时设备列表
     * @Author: zhujb
     * 2018/9/13
     * @return array|mixed
     */
    public function getTimingEquipsList()
    {
        $equipName = I('post.equip_name', '', 'strval');
        $equipNo   = I('post.equip_no', '', 'strval');
        $page      = I('post.page', 1, 'intval');
        $pageSize  = I('post.page_size', 10, 'intval');

        $timingEquipModel = new TimingProduct();
        if ($this->_dtype == 9) {
            $applyDid = 0;
        } else {
            $applyDid = $this->_memberId;
        }

        $list           = $timingEquipModel->getTimingEquips($applyDid, $equipName, $equipNo, $page, $pageSize);
        $total          = $timingEquipModel->getTimingEquipsCount($applyDid, $equipName, $equipNo);
        $res['list']    = $list;
        $res['total']   = $total;
        $res['maxPage'] = ceil($total / $pageSize);

        $this->apiReturn(200, $res);
    }

    /**
     * 获取票类扩展属性保存参数
     * @Author: PeiJun Li
     * @date   2018-11-27
     *
     * @param  array  $ticketData  票属性
     * @param  int  $ticketId  票ID
     *
     * @return array
     */
    private function _getExtParams($ticketData, $ticketId)
    {
        //$ticketData['is_start_stop_sale_limit'] = 1;
        //获取票类扩展属性字典
        $extInfo = load_config('ext', 'ticket_field');
        $extData = [];    //需要保存的数组

        //查询字典中是否有需要扩展的字段  对应的字段和ext中对应的字段的值  存入pft_ticket_attr_conf表中
        if (!empty($extInfo)) {
            foreach ($extInfo as $key => $value) {
                foreach ($ticketData as $k => $v) {

                    $ptypeArr = explode(',', $value['p_type']);
                    if ($k == $key && ($value['p_type'] == '0' || in_array($ticketData['type'], $ptypeArr))) {
                        if ($value['data_type']) {
                            $v = (int)$v;
                        } else {
                            $v = (string)$v;
                        }

                        $extData[] = [
                            'ticketId' => $ticketId,
                            'key'      => $key,
                            'val'      => $v,
                        ];
                    }
                }
            }
        }

        return $extData;
    }

    /**
     * 保存票类扩展属性
     * @Author: PeiJun Li
     * @date   2018-11-27
     *
     * @param  int  $accountId  用户Id
     * @param  int  $operaterId  操作者ID
     * @param  array  $params  票属性
     * @param  string  $versionUuid  本次操作的uuid
     *
     * @return array
     */
    private function _saveExtParams($accountId, $operaterId, $params, $versionUuid = '')
    {
        if (empty($params)) {
            return ['code' => 202, 'msg' => '扩展未保存'];
        }
        //获取票类扩展属性字典
        $ticketExtConf = new \Business\JavaApi\Ticket\TicketExtendAttr();
        $result        = $ticketExtConf->save($accountId, $operaterId, $params, $versionUuid);
        pft_log('ticket_ext/debug', json_encode(['params' => $params, 'result' => $result]));

        return $result;
    }

    private function saveReserveStorageParams($params, $ticketId, $pType = 0)
    {
        if (count($params) == 0) {
            return ['code' => 202, 'msg' => '预约库存未保存'];
        }
        if (empty($params['start_time'])) {
            return ['code' => 202, 'msg' => '预约库存开始时间未保存'];
        }
        if (empty($params['end_time'])) {
            return ['code' => 202, 'msg' => '预约库存结束时间未保存'];
        }
        $reserveStorageService = new \Business\JavaApi\Product\ReserveStorage();
        if (isset($params['id']) && $params['id'] != '') {
            $requestData = array(
                'id'         => $params['id'],
                'startDate'  => $params['start_time'],
                'endDate'    => $params['end_time'],
                'operaterId' => $this->_memberId,
            );
            if (isset($params['remark'])) {
                $requestData['remark'] = $params['remark'];
            }
            if (isset($params['storage_status'])) {
                $requestData['storageStatus'] = $params['storage_status'];
            }
            if (isset($params['storage'])) {
                $requestData['storage'] = $params['storage'];
            }
            $ticketReserveStorageContext = new TicketReserveStorageContext($requestData);
            $result                      = $reserveStorageService->update($ticketReserveStorageContext);
        } else {
            $requestData = array(
                'pType'      => $pType,
                'tid'        => $ticketId,
                'startDate'  => $params['start_time'],
                'endDate'    => $params['end_time'],
                'operaterId' => $this->_memberId,
            );
            if (isset($params['remark'])) {
                $requestData['remark'] = $params['remark'];
            }
            if (isset($params['storageStatus'])) {
                $requestData['storageStatus'] = $params['storage_status'];
            }
            if (isset($params['storage'])) {
                $requestData['storage'] = $params['storage'];
            }
            $ticketReserveStorageContext = new TicketReserveStorageContext($requestData);
            $result                      = $reserveStorageService->save($ticketReserveStorageContext);
        }
        pft_log('ticket_ext/debug', json_encode(['params' . json_encode($params), 'result' . json_encode($result)]));

        return $result;
    }

    /**
     * 自动上下架参数处理
     * <AUTHOR>
     * @date 2019/10/10 0010
     *
     * @param $ticketData
     * @param $priceData
     *
     * @deprecated
     * @see \Business\Product\HandleTicket::handleAutoParam();
     *
     * @return array
     */
    //private function handleAutoParam($ticketData, $priceData)
    //{
    //    // var_dump($ticketData['status'], $ticketData['auto_grounding_status'], $ticketData['auto_undercarriage_status']);
    //    // var_dump('*******');
    //    $timeNow = strtotime(date("Y-m-d H:i:s"), time());
    //    //自动上架方式票状态为自动上下架的时候
    //    if (isset($ticketData['auto_grounding_status']) && $ticketData['status'] == 4) {
    //        $ticketData['auto_grounding_date'] = date('Y-m-d H:i:s', strtotime($ticketData['auto_grounding_date']));
    //        if ($ticketData['auto_grounding_status'] == 1) {
    //            //预定时间自动上架
    //            $startDate = '';
    //            foreach ($priceData as $dateValue) {
    //                if (empty($startDate)) {
    //                    $startDate = $dateValue['sdate'];
    //                    continue;
    //                }
    //                if (strtotime($dateValue['sdate']) < strtotime($startDate)) {
    //                    $startDate = $dateValue['sdate'];
    //                }
    //            }
    //            $ticketData['preinstall_listing_date_rule'] = $ticketData['auto_grounding_status'] . ',' . date('Y-m-d',
    //                    strtotime($startDate)) . ' 00:00:00';
    //        }
    //        if ($ticketData['auto_grounding_status'] == 2) {
    //            //发布后自动上架
    //            $ticketData['preinstall_listing_date_rule'] = $ticketData['auto_grounding_status'] . ',' . date('Y-m-d') . ' 00:00:00';
    //        }
    //        if ($ticketData['auto_grounding_status'] == 3) {
    //            //指定时间上架
    //            $ticketData['preinstall_listing_date_rule'] = $ticketData['auto_grounding_status'] . ',' . date('Y-m-d H:i:s',
    //                    strtotime($ticketData['auto_grounding_date']));
    //        }
    //    } else {
    //        $ticketData['auto_grounding_date'] = date('Y-m-d') . ' 00:00:00';
    //    }
    //
    //    //自动下架方式票状态为自动上下架的时候
    //    if (isset($ticketData['auto_undercarriage_status']) && $ticketData['status'] == 4) {
    //        $ticketData['auto_undercarriage_date'] = date('Y-m-d H:i:s',
    //            strtotime($ticketData['auto_undercarriage_date']));
    //        if ($ticketData['auto_undercarriage_status'] == 1) {
    //            //预定时间到期自动下架
    //            $endDate = '';
    //            foreach ($priceData as $dateValue) {
    //                if (empty($startDate)) {
    //                    $endDate = $dateValue['edate'];
    //                    continue;
    //                }
    //                if (strtotime($dateValue['edate']) > strtotime($endDate)) {
    //                    $endDate = $dateValue['edate'];
    //                }
    //            }
    //            $ticketData['preinstall_delisting_date_rule'] = $ticketData['auto_undercarriage_status'] . ',' . date('Y-m-d',
    //                    strtotime($endDate)) . ' 23:59:59';
    //        }
    //
    //        if ($ticketData['auto_undercarriage_status'] == 2) {
    //            $ticketData['preinstall_delisting_date_rule'] = $ticketData['auto_undercarriage_status'] . ',' . date('Y-m-d H:i:s',
    //                    strtotime($ticketData['auto_undercarriage_date']));
    //        }
    //        unset($ticketData['auto_undercarriage_status'], $ticketData['auto_undercarriage_date']);
    //    } else {
    //        $ticketData['auto_undercarriage_date'] = date('Y-m-d') . ' 23:59:59';
    //    }
    //
    //    //状态转换兼容java接口
    //    if ($ticketData['rule_status'] == 1) {
    //        $ticketData['ticket_shelf_rule'] = 0;
    //        $ticketData['status']            = 1;
    //    }
    //
    //    if ($ticketData['rule_status'] == 2) {
    //        $ticketData['ticket_shelf_rule'] = 1;
    //        $ticketData['status']            = 2;
    //    }
    //
    //    //自动上下架特殊处理
    //    if ($ticketData['rule_status'] == 4) {
    //        $ticketData['ticket_shelf_rule'] = 2;
    //        $ticketData['status']            = 2;
    //        //立即上架的几种情况
    //        // 1、设置预定时间开始自动上架的时候 预定日期开始时间小于等于当前时间
    //        // 2、设置立即上架的时候
    //        // 3、设置指定日期上架的时候 指定的日期小于等于当前时间
    //        if ($ticketData['auto_grounding_status'] == 1) {
    //            //预定时间自动上架
    //            if (strtotime($startDate) <= $timeNow) {
    //                $ticketData['status'] = 1;
    //            }
    //        }
    //        if ($ticketData['auto_grounding_status'] == 2) {
    //            //发布后立即上架
    //            $ticketData['status'] = 1;
    //        }
    //        if ($ticketData['auto_grounding_status'] == 3) {
    //            //指定时间上架
    //            if (strtotime($ticketData['auto_grounding_date']) <= $timeNow) {
    //                $ticketData['status'] = 1;
    //            }
    //        }
    //
    //    }
    //
    //    // var_dump($ticketData['status'], $ticketData['ticket_shelf_rule'], $ticketData['preinstall_delisting_date_rule'], $ticketData['preinstall_listing_date_rule']);exit;
    //    return $ticketData;
    //}

    private function handleAutoParam($ticketData, $priceData)
    {
        // var_dump($ticketData['status'], $ticketData['auto_grounding_status'], $ticketData['auto_undercarriage_status']);
        // var_dump('*******');
        $timeNow       = strtotime(date("Y-m-d H:i:s"), time());
        $oldRuleStatus = $ticketData['rule_status'];
        //自动上架方式票状态为自动上下架的时候
        if (isset($ticketData['auto_grounding_status']) && $ticketData['status'] == 4) {
            $ticketData['auto_grounding_date'] = date('Y-m-d H:i:s', strtotime($ticketData['auto_grounding_date']));
            if ($ticketData['auto_grounding_status'] == 1) {
                //预定时间自动上架
                $startDate = '';
                foreach ($priceData as $dateValue) {
                    if (empty($startDate)) {
                        $startDate = $dateValue['sdate'];
                        continue;
                    }
                    if (strtotime($dateValue['sdate']) < strtotime($startDate)) {
                        $startDate = $dateValue['sdate'];
                    }
                }
                $ticketData['preinstall_listing_date_rule'] = $ticketData['auto_grounding_status'] . ',' . date('Y-m-d',
                        strtotime($startDate)) . ' 00:00:00';
            }
            if ($ticketData['auto_grounding_status'] == 2) {
                //发布后自动上架
                $ticketData['preinstall_listing_date_rule'] = $ticketData['auto_grounding_status'] . ',' . date('Y-m-d') . ' 00:00:00';
            }
            if ($ticketData['auto_grounding_status'] == 3) {
                //指定时间上架
                $ticketData['preinstall_listing_date_rule'] = $ticketData['auto_grounding_status'] . ',' . date('Y-m-d H:i:s',
                        strtotime($ticketData['auto_grounding_date']));
            }
        } else {
            $ticketData['auto_grounding_date'] = date('Y-m-d') . ' 00:00:00';
        }

        //自动下架方式票状态为自动上下架的时候
        if (isset($ticketData['auto_undercarriage_status']) && $ticketData['status'] == 4) {
            $ticketData['auto_undercarriage_date'] = date('Y-m-d H:i:s',
                strtotime($ticketData['auto_undercarriage_date']));
            if ($ticketData['auto_undercarriage_status'] == 1) {
                //预定时间到期自动下架
                $endDate = '';
                foreach ($priceData as $dateValue) {
                    if (empty($startDate)) {
                        $endDate = $dateValue['edate'];
                        continue;
                    }
                    if (strtotime($dateValue['edate']) > strtotime($endDate)) {
                        $endDate = $dateValue['edate'];
                    }
                }
                $ticketData['preinstall_delisting_date_rule'] = $ticketData['auto_undercarriage_status'] . ',' . date('Y-m-d',
                        strtotime($endDate)) . ' 23:59:59';
            }

            if ($ticketData['auto_undercarriage_status'] == 2) {
                $ticketData['preinstall_delisting_date_rule'] = $ticketData['auto_undercarriage_status'] . ',' . date('Y-m-d H:i:s',
                        strtotime($ticketData['auto_undercarriage_date']));
            }
            unset($ticketData['auto_undercarriage_status'], $ticketData['auto_undercarriage_date']);
        } else {
            $ticketData['auto_undercarriage_date'] = date('Y-m-d') . ' 23:59:59';
        }

        //状态转换兼容java接口
        if ($ticketData['rule_status'] == 1) {
            $ticketData['ticket_shelf_rule'] = 0;
            $ticketData['status']            = 1;
        }

        if ($ticketData['rule_status'] == 2) {
            $ticketData['ticket_shelf_rule'] = 1;
            $ticketData['status']            = 2;
        }

        //自动上下架特殊处理
        if ($ticketData['rule_status'] == 4) {
            $ticketData['ticket_shelf_rule'] = 2;
            $ticketData['status']            = 2;
            //立即上架的几种情况
            // 1、设置预定时间开始自动上架的时候 预定日期开始时间小于等于当前时间
            // 2、设置立即上架的时候
            // 3、设置指定日期上架的时候 指定的日期小于等于当前时间
            if ($ticketData['auto_grounding_status'] == 1) {
                //预定时间自动上架
                if (strtotime($startDate) <= $timeNow) {
                    $ticketData['status'] = 1;
                }
            }
            if ($ticketData['auto_grounding_status'] == 2) {
                //发布后立即上架
                $ticketData['status'] = 1;
            }
            if ($ticketData['auto_grounding_status'] == 3) {
                //指定时间上架
                if (strtotime($ticketData['auto_grounding_date']) <= $timeNow) {
                    $ticketData['status'] = 1;
                }
            }

        }
        if (isset($ticketData['auto_sale_rule']) && $ticketData['rule_status'] == 5) {
            $ticketData['auto_sale_rule'] = !empty($ticketData['auto_sale_rule']) ? json_encode($ticketData['auto_sale_rule']) : '';
        }
        //自动起售停售
        if ($ticketData['rule_status'] == 5) {
            $ticketData['rule_status']       = 1;
            $ticketData['ticket_shelf_rule'] = 0;
            $ticketData['status']            = 1;
        }

        if ($oldRuleStatus == 5 && (!empty($ticketData['start_sale_datetime']) || !empty($ticketData['stop_sale_datetime']))) {
            $ticketData['rule_status']       = 5;
            $ticketData['ticket_shelf_rule'] = 3;
            $ticketData['status']            = 1;
        }

        // var_dump($ticketData['status'], $ticketData['ticket_shelf_rule'], $ticketData['preinstall_delisting_date_rule'], $ticketData['preinstall_listing_date_rule']);exit;
        return $ticketData;
    }

    /**
     * 保险价格参数兼容处理
     * <AUTHOR>
     * @date 2019/12/25 0025
     *
     * @param $priceParam
     * @param $ticketParam
     *
     * @return array
     */
    private function handleInsurePrice($priceParam, $ticketParam)
    {
        if ($ticketParam['type'] == 'L') {
            $priceParam[0]['js']      = $ticketParam['ticket_price'];
            $priceParam[0]['ls']      = $ticketParam['ticket_price'];
            $priceParam[0]['m_price'] = $ticketParam['ticket_price'];
            $priceParam[0]['w_price'] = $ticketParam['ticket_price'];
        }

        return $priceParam;
    }

    /**
     * 票属性页面修改返回数据结构兼容前端错误提示弹窗
     * <AUTHOR>
     * @date 2020/01/19
     *
     * @param $code
     * @param $msg
     *
     * @return array
     */
    public function _preApiReturn($code, $msg)
    {
        $data[] = [
            'ext' => [
                'msg'  => $msg,
                'code' => $code,
            ],

            'price' => [
                'msg'  => $msg,
                'code' => $code,
            ],
        ];

        return $data;
    }

    /**
     * 多景点配置相关参数日志
     * <AUTHOR>
     * @date 2020/3/10 0010
     *
     * @return array
     */
    private function addCheckTerminalInfoLog($ticketData)
    {
        //多景点配置日志记录
        if ($ticketData['id'] > 0) {
            $checkTerminalInfo = $ticketData['check_terminal_info'];
            if (!is_string($ticketData['check_terminal_info'])) {
                $checkTerminalInfo = json_encode($ticketData['check_terminal_info']);
            }
            $log = '多景点票ID:' . $ticketData['id'] . '|check_terminal_info:' . $checkTerminalInfo;
            pft_log('debug/ticket_debug', $log);
        }
    }

    /**
     * 多景点配置白名单判断
     * <AUTHOR>
     * @date 2020/3/12 0012
     *
     * @return array
     */
    private function judgeChkWhite()
    {
        $memberInfo = $this->getLoginInfo();

//        $list = load_config("chk_white", "white_list");
        $parAccount = $memberInfo['saccount'];//父账号
        $account    = $memberInfo['account'];//账号
        $res        = 0;
        $memberMdl  = new \Model\Member\ScenicCheckUser();

        if ($memberMdl->isMemberWhith($account)) {
            $res = 1;
        }

        if ($memberMdl->isMemberWhith($parAccount)) {
            $res = 1;
        }
//        //列表用户添加完删除
//        if (in_array($account, $list)) {
//            $res = 1;
//        }
//
//        if (in_array($parAccount, $list)) {
//            $res = 1;
//        }

        return $res;
    }

    /**
     * 自定义标签处理
     * <AUTHOR>
     * @date 2020-12-08
     *
     * @return array
     */
    private function _handleCustumTab($data)
    {
        if (!$data) {
            return $data;
        }

        if ($data['ticket']['custom_tag_json']) {
            $data['ticket']['custom_tag_json'] = json_decode($data['ticket']['custom_tag_json'], true) ?? [];
        } else {
            $data['ticket']['custom_tag_json'] = [];
        }

        return $data;
    }

    /***
     * 附加票参数处理
     * <AUTHOR>
     * @date 2021/9/14
     *
     * @param  int  $ticketId  票ID
     * @param  array  $data  票数据
     *
     * @return array
     */
    public function _handleTicketDeputy(int $ticketId, array $data)
    {
        if (empty($data)) {
            return $data;
        }

        $data['ticket']['ticket_deputy_sets'] = [];

        $ticketDeputyService = new TicketDeputyService();
        $timeShareBiz        = new \Business\Product\TimeShare();

        $result = $ticketDeputyService->queryTicketDeputy($this->_memberId, $ticketId);
        if ($result['code'] != 200 || empty($result['data'])) {
            return $data;
        }

        //获取是否开启分时
        $ticketIds   = array_column($result['data'], 'ticketId');
        $timeTypeMap = [];
        $resultTime  = $timeShareBiz->querySectionEnableByTids($ticketIds);
        if ($resultTime['code'] == 200 && !empty($resultTime['data'])) {
            $timeTypeMap = array_key($resultTime['data'], 'ticketId');
        }

        foreach ($result['data'] as &$value) {
            $timeShare = 0;
            if (isset($timeTypeMap[$value['ticketId']]) && !$timeTypeMap[$value['ticketId']]['status']) {
                $timeShare = 1;
            }
            $value['isOpenTime'] = $timeShare;
            $value['preSale']    = $value['preSale'];
        }

        $data['ticket']['ticket_deputy_sets'] = $result['data'];

        return $data;
    }

    /**
     * 人脸配置检测(保存票属性迁移解耦)
     * <AUTHOR>
     * @date 2021/11/25
     *
     * @return array
     */
    public function checkFacePlatform()
    {
        $lid = I('post.lid', 0, 'intval');
        if (empty($lid)) {
            $this->apiReturn(203, [], '参数错误');
        }

        //人脸配置检测
        $landModel   = new Land();
        $landInfo    = $landModel->getLandInfo($lid, false, 'terminal');//根据lid取终端号
        $terminalId  = $landInfo['terminal'];
        $nowTerminal = $landModel->getNowTerminal($terminalId);
        if ($nowTerminal) {
            //如果有合并终端的，取合并后的终端
            $terminalId = $nowTerminal;
        }
        $faceModel    = new \Model\Terminal\FaceCompare();
        $facePlatform = $faceModel->getFacePlatform(false, $terminalId);
        if (empty($facePlatform)) {
            $this->apiReturn(200, ['hasFacePlatform' => 0], '');
        }

        $this->apiReturn(200, ['hasFacePlatform' => 1], '');

        //if (!$facePlatform && $ticketData['face_open'] == 1) {
        //    unset($ticketData['face_open']);
        //    unset($ticketData['face_idcard_compare']);
        //    unset($ticketData['face_repeat_enter']);
        //    unset($ticketData['face_enter_time']);
        //    unset($ticketData['face_valid_time']);
        //}
    }

    /**
     * 预览退票配置
     * <AUTHOR>
     * @date 2021/12/14
     *
     * @return array
     */
    public function previewRefundConfig()
    {

    }


    public function judgePackPrice()
    {
        $submitData         = I('post.');
        $submitData['data'] = htmlspecialchars_decode($submitData['data']);
        $submitData         = json_decode($submitData['data'], true);
        if (count($submitData) < 1) {
            $this->apiReturn(204, [], '未接收到门票信息');
        }
        //冗余校验开放功能
        $isAllowPackPriceCheck = \Business\Order\RefundAudit::isAllowPackPriceCheck($this->_memberId);
        if(!$isAllowPackPriceCheck){
            $this->apiReturn(200);
        }
        $result = [];
        //获取主票价格
        $judgeDataList = [];


        foreach ($submitData as $mainPriceList){
            $linkData['tid'] = $mainPriceList['tid'];
            $linkData['flag'] = $mainPriceList['flag'] ?? '';
            $linkData['child_ids'] = $mainPriceList['child_ids'];
            $linkData['name'] = $mainPriceList['name'];
            $timestamp = time();
            $priceSection = [];
            $dayOfWeek = date("N");
            foreach ($mainPriceList['price_section'] as $priceItem){
                $sdate = strtotime( $priceItem['sdate']);
                $edate = strtotime( $priceItem['edate']);
                if($sdate <= $timestamp && $edate >= $timestamp){
                    array_push($priceSection,$priceItem);
                    $weekdays = explode(',',$priceItem['weekdays']);
                    if(in_array($dayOfWeek,$weekdays)){
                        array_unshift($priceSection,$priceItem);
                    }
                }
            }
            $priceSection = empty($priceSection) ?  reset($mainPriceList['price_section']) : reset($priceSection);
            $linkData['price_section'] = $priceSection;
            array_push($judgeDataList,$linkData);
        }
        //获取子票价格
        foreach ($judgeDataList as $judgeItem){
            $res = self::getSetPackSonTicketPrice($judgeItem['child_ids'],$judgeItem['tid'],$judgeItem['flag']);
            $totalCostPrice = 0;
            if($res['code']== 200){
                $totalCostPrice = $res['data']['total_cost_price'];
            }
            //对比差异
            if($totalCostPrice != $judgeItem['price_section']['js']){
                $result[] = ['name'=> $judgeItem['name']];
            }
        }
        if(!empty($result)){
            $this->apiReturn(200,$result);
        }
        else{
            $this->apiReturn(200);
        }
    }

    private function getSetPackSonTicketPrice($tids,$parent_tid,$cacheKay)
    {
        if (empty($tids)) {
            return  ['code' => 203, 'data' => [], 'msg'  => '获取数据失败，参数缺少'];
        }
        //新建套票，选择几个子票,在主票还没保存情况下使用，tids会传入null字符串
        if ($tids === 'null' && !$cacheKay) {
            return  ['code' => 203, 'data' => [], 'msg'  => '获取数据失败，参数缺少'];
        }
        $packBiz           = new \Business\Product\PackTicket();
        $sonTicketInfoJson = $packBiz->getCache($cacheKay);
        // $sonTicketInfoJson = $packBiz->getCache($this->_operatorId);
        $sonTicketInfoArr = [];
        if ($sonTicketInfoJson) {

            $tmpSonTicketInfoArr = json_decode($sonTicketInfoJson, true);
            $tmpSonTicketInfoArr = $tmpSonTicketInfoArr['package_data'];

            foreach ($tmpSonTicketInfoArr as $keyDel => $valueDel) {
                if ($valueDel['num'] == -1) {
                    unset($tmpSonTicketInfoArr[$keyDel]);
                }
            }

            $tidNumArr = [];
            if (is_array($tmpSonTicketInfoArr)) {
                foreach ($tmpSonTicketInfoArr as $sonVal) {
                    $tidNumArr[$sonVal['tid']]        = $sonVal['num'];
                    $sonTicketInfoArr[$sonVal['tid']] = $sonVal['num'] * $sonVal['cost_price'];
                }
            }
            $tidArr = array_column($tmpSonTicketInfoArr, 'tid');
        } else {
            if ($parent_tid) {
                //获取主票下子票的打包成本价
                $tmpSonTicketInfoArr = $packBiz->getTickets($parent_tid);
                if ($tmpSonTicketInfoArr) {
                    foreach ($tmpSonTicketInfoArr as $item) {
                        $tidNumArr[$item['tid']]        = $item['num'];
                        $sonTicketInfoArr[$item['tid']] = $item['num'] * $item['cost_price'];
                    }
                }
            }
            $tidArr = explode(',', $tids);
        }

        if (empty($tidArr)) {
            return  ['code' => 203, 'data' => [], 'msg'  => '参数缺少'];
        }

        $ticketModel   = new \Model\Product\Ticket();
        $ticketInfoArr = $ticketModel->getTicketInfoMulti($tidArr,
            'id, landid, title, delaytype, delaydays, order_start, order_end, if_verify, use_early_days, apply_did, ddays, buy_limit_low, buy_limit_up, chk_terminal_info, max_expiration_date, status, pid,refund_rule,refund_before_early_time,refund_after_early_time,refund_audit,refund_num,pre_sale,effective_limit,refund_least_num');

        if (empty($ticketInfoArr)) {
            return  ['code' => 205, 'data' => [], 'msg'  => '数据有误'];
        }

        $landIdArr   = array_column($ticketInfoArr, 'landid');
        $javaAPi     = new \Business\CommodityCenter\Land();
        $landRes     = $javaAPi->queryLandMultiQueryById($landIdArr);
        $landInfoArr = array_column($landRes, null, 'id');
        $buyLimitRes = (new \Business\BuyLimit\Index())->queryLandLimitConfigByTicketIds($tidArr);
        $buyLimit = [];
        if ($buyLimitRes['code'] == 200 && $buyLimitRes['data']) {
            foreach ($buyLimitRes['data'] as $buyitem){
                if (isset($buyitem['ticketId'])) {
                    $buyLimit[$buyitem['ticketId']] = $buyitem;
                }
            }
            // $buyLimit = array_column($buyLimitRes['data'], null, 'ticketId');
        }

        //获取是否开启分时
        $timeShareBiz        = new \Business\Product\TimeShare();
        $ticketIds   = array_column($ticketInfoArr, 'id');
        $timeTypeMap = [];
        $resultTime  = $timeShareBiz->querySectionEnableByTids($ticketIds);
        if ($resultTime['code'] == 200 && !empty($resultTime['data'])) {
            $timeTypeMap = array_key($resultTime['data'], 'ticketId');
        }

        $packageInfoMap = [];
        if($parent_tid){
            $packageBiz   = new PackRelation();
            $packageInfoRes  = $packageBiz->getPackageTicketAttrList($parent_tid);
            if ($packageInfoRes['code'] == 200 && !empty($packageInfoRes['data'])) {
                $packageInfoMap = array_key($packageInfoRes['data'], 'tid');
            }
        }

        $data                     = [];
        $validTime                = [];
        $data['is_self_supply']   = 1;
        $data['total_cost_price'] = 0;
        $isChange                 = false;
        foreach ($ticketInfoArr as $item) {
            $num = '';
            if (isset($tidNumArr[$item['id']])) {
                $num = $tidNumArr[$item['id']];
            }

            $ltitle = '';
            $type   = 'A';
            if (isset($landInfoArr[$item['landid']]['title'])) {
                $ltitle = $landInfoArr[$item['landid']]['title'];
            }

            if (isset($landInfoArr[$item['landid']]['p_type'])) {
                $type = $landInfoArr[$item['landid']]['p_type'];
            }

            $isMy = 2;//是否自供应
            if ($this->_memberId == $item['apply_did']) {
                $isMy = 1;
            }

            //判断是否过期
            $isExpire = 0;
            if (!empty($item['max_expiration_date']) && time() > strtotime(date('Y-m-d 23:59:59', strtotime($item['max_expiration_date'])))) {
                $isExpire = 1;
            }

            //分时标识
            $timeShare = 0;
            if (isset($timeTypeMap[$item['id']]) && !$timeTypeMap[$item['id']]['status']) {
                $timeShare = 1;
            }
            $itmeBuyLimiInfo = [];
            //赋值购票限制类型 排除掉产品限制
            if (isset($buyLimit[$item['id']]) && $buyLimit[$item['id']]['limitConfigType'] == 1) {
                $itmeBuyLimiInfo = $buyLimit[$item['id']];
            }
            $data['ticket'][$item['id']] = [
                'ttitle'                   => $item['title'],
                'ltitle'                   => $ltitle,
                'valid_period_type'        => $item['delaytype'],
                'valid_period_days'        => $item['delaydays'],
                'valid_period_start'       => $item['order_start'],
                'valid_period_end'         => $item['order_end'],
                'use_early_days'           => $item['use_early_days'],
                'use_period_timecancheck'  => $item['if_verify'],
                'num'                      => $num,
                'lid'                      => $item['landid'],
                'p_type'                   => $type,
                'tid'                      => $item['id'],
                'is_my'                    => $isMy,
                'cost_price'               => isset($sonTicketInfoArr[$item['id']]) ? $sonTicketInfoArr[$item['id']] : 0,
                'preorder_early_days'      => $item['ddays'],
                'preorder_expire_time'     => $item['dhour'],
                'buy_min_amount'           => $item['buy_limit_low'],
                'buy_max_amount'           => $item['buy_limit_up'],
                'buy_limit_type'           => $item['buy_limit'],
                'buy_limit_period'         => $item['buy_limit_date'],
                'buy_limit_num'            => $item['buy_limit_num'],
                'identity_info'            => $item['tourist_info'],
                'age_limit_max'            => $item['age_limit_max'],
                'age_limit_min'            => $item['age_limit_min'],
                'more_credentials'         => !empty($item['more_credentials']) ? 1 : 0,
                'more_credentials_content' => empty($item['more_credentials']) ? [] : json_decode($item['more_credentials'],
                    true),
                'status'                   => $item['status'], //2=下架
                'isExpire'                 => $isExpire, //是否过期 0=否 1=是
                'pid'                      => $item['pid'],
                'aid'                      => $packageInfoMap[$item['id']]['aid'] ?? 0,
                'buy_limit_info'           => $itmeBuyLimiInfo,
                'refund_rule'               => $item['refund_rule'] ?? 0,
                'refund_before_early_time'  => $item['refund_before_early_time'] ?? 0,
                'refund_after_early_time'   => $item['refund_after_early_time'] ?? 0,
                'refund_audit'              => $item['refund_audit'] ?? 0,
                'refund_num'                => (int)$item['refund_num'] ?? -1,
                'effective_limit'           => $item['effective_limit'] ?? '',
                'pre_sale'                  => $item['pre_sale'] ?? 0,
                'refund_least_num'          => $item['refund_least_num'] ?? 0,
            ];


            $data['total_cost_price'] += $data['ticket'][$item['id']]['cost_price'];

            //子票不是自供应
            if ($isMy == 2 && !$isChange) {
                $data['is_self_supply'] = 2;
                $isChange               = true;
            }
        }
        $validTime         = array_unique($validTime);
        $validTimeStr      = implode('+', $validTime);
        $data['valid_tip'] = $validTimeStr;
        //酒店添加房型数据
        $hotelLib       = new Hotel();
        $data['ticket'] = $hotelLib->handleRoomParamsPack($data['ticket']);
        //子票包含演出票处理
        $data = $packBiz->handlePacketSonShowParams($data, $this->_memberId);
        return  ['code' => 200, 'data' => $data, 'msg'  => 'success'];
    }
}
