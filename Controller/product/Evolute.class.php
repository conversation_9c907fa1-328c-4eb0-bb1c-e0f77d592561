<?php

namespace Controller\product;

use Business\Authority\DataAuthLogic;
use Business\MemberLogin\MemberLoginHelper;
use Business\NewJavaApi\Distribution\BatchOperateDistributor;
use Business\JavaApi\Product\EvoluteLimiterService;
use Business\JavaApi\ProductApi;
use Business\JavaApi\Ticket\TicketExtendAttr;
use Business\Order\OrderBook;
use Business\Product\MultiLevelPrice;
use Business\Product\ProductTag as ProductTagBiz;
use Business\Product\SubProduct as SubProductBiz;
use Library\Controller;
use Model\Member\Member;
use Model\Product;
use Business\JavaApi\TicketApi;

/**
 * 转分销产品相关接口
 *
 * <AUTHOR>
 * @date 2017/2/27
 *
 */
class Evolute extends Controller
{
    private $_sid       = null;
    private $_mid       = null;
    private $_loginInfo = null;

    public function __construct()
    {
        $this->_sid       = $this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
        $this->_mid       = $this->_loginInfo['memberID'];
    }

    /**
     * 获取转分销产品列表
     *
     * @return [type] [description]
     */
    public function getProList()
    {
        //当前页数
        $page = I('page', 1, 'intval');
        //每页显示条数
        $pageSize = I('pageSize', 5, 'intval');
        //城市code
        $city = I('city', '', 'intval');
        //境内外
        $oversea = I('oversea', '');
        //省份code
        $province = I('province', 0, 'intval');
        //景区标题
        $title = I('title', '', 'strval');
        //供应商名称
        $supplier = I('supplier', '', 'strval');
        //产品类型
        $type    = I('type', '');
        $subType = I('sub_type', 0, 'intval'); //计时产品子类型
        //转分销开启状态
        $disOpen = I('openDistribution', -1, 'intval');
        // 标签查询
        $skuTags = I('sku_tags', '', 'strval,trim');
        $skuTags = json_decode($skuTags, true);
        if (json_last_error() != JSON_ERROR_NONE || empty($skuTags)) {
            $skuTags = null;
        }

        //指定的账号允许开启现场支付票类的转分销product_Evolute
        $showOnsite = false;

        //临时在这边做下特殊用户的访问控制
        $limitUserList = [
            684493,
            4454288,
            131887,
            57801,
            315436,
            4196846,
            91441,
            919790,
            755911,
            4747316,
            2155259,
            675904,
            216206,
            256068,
            110845,
            433028,
            522628,
            6970,  //加一个测试账号
        ];
        $limitTime     = 60;
        $limitKey      = "pft_evolute:limit:{$this->_sid}";
        $redisCache    = \Library\Cache\Cache::getInstance('redis');

        if (in_array($this->_sid, $limitUserList)) {
            //设置一个60秒的锁，60秒之类只能访问这个接口一次
            $lockRes = $redisCache->lock($limitKey, 1, $limitTime);

            if (!$lockRes) {
                $this->apiReturn(500, [], '请求过于频繁，请稍后重试');
            }
        }

        //强制处理下
        if ($oversea == '') {
            $oversea = -1;
        }

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition();
        // 全禁的情况下不需要调用接口
        if ($condition === false) {
            $this->apiReturn(self::CODE_SUCCESS, [

                'sid'       => $this->_sid,
                'lists'     => [],
                'page'      => $page,
                'pageSize'  => $pageSize,
                'totalPage' => 0,
                'totalNum'  => 0,
            ]);
        }

        //标签查询
        $condition['tags'] = $skuTags;

        //ptype含子产品类型解析
        list($type, $subType) = SubProductBiz::decodeTypeAndSubType($type, $subType);

        $result = ProductApi::getDisProList($this->_sid, $page, $pageSize, $title, $supplier, $province, $city,
            $showOnsite, $type, (int)$oversea, $disOpen, $subType, $condition);

        //查询完成之后删除锁
        if (in_array($this->_sid, $limitUserList)) {
            $redisCache->rm($limitKey);
        }

        $landList = isset($result['landList']) ? $result['landList'] : [];
        $tvTagArr = [];
        //旅游券门票标签
        $tvTagRes  = [];

        $ticketMap    = [];
        $ticketExtMap = [];
        if (!empty($landList)) {
            //处理开启营销属性启停售的时候数据返回
            $tidArr     = [];
            $ticketData = array_column($landList, 'ticketList');
            foreach ($ticketData as $item) {
                $tidArr = array_merge(array_column($item, 'tid'), $tidArr);
            }
            // 额外填写更多信息
            $ticketExtendAttr    = new TicketExtendAttr();
            $ticketSpecialValRes = $ticketExtendAttr->queryTicketSpecialVal($tidArr,
                ['is_start_stop_sale_limit', 'auto_sale_rule', 'refund_before_early_time', 'refund_after_early_time', 'before_show_time']);
            $ticketSpecialValRes = $ticketSpecialValRes['code'] == 200 ? $ticketSpecialValRes['data'] : [];
            $autoArr               = [];
            $ticketAttr            = [];
            $refundBeforeEarlyTime = [];
            $refundAfterEarlyTime  = [];
            $beforeShowTime        = [];
            foreach ($ticketSpecialValRes as $ticketConf) {
                if ($ticketConf['key'] == 'auto_sale_rule') {
                    $autoArr[] = $ticketConf;
                }
                if ($ticketConf['key'] == 'is_start_stop_sale_limit') {
                    $ticketAttr[] = $ticketConf;
                }
                if ($ticketConf['key'] == 'refund_before_early_time') {
                    $refundBeforeEarlyTime[] = $ticketConf;
                }
                if ($ticketConf['key'] == 'refund_after_early_time') {
                    $refundAfterEarlyTime[] = $ticketConf;
                }
                if ($ticketConf['key'] == 'before_show_time') {
                    $beforeShowTime[] = $ticketConf;
                }
            }
            $autoArr    = array_key($autoArr, 'ticketId');
            $ticketAttr = array_key($ticketAttr, 'ticketId');
            $refundBeforeEarlyTime = array_key($refundBeforeEarlyTime, 'ticketId');
            $refundAfterEarlyTime  = array_key($refundAfterEarlyTime, 'ticketId');
            $beforeShowTime        = array_key($beforeShowTime, 'ticketId');

            //旅游券标签获取下
            if ($tidArr) {
                $tvTagRes = (new \Business\JsonRpcApi\TravelVoucherService\ProductTag())->getTicketTag($tidArr);
                if ($tvTagRes['code'] == 200 && !empty($tvTagRes['data'])) {
                    $tvTagArr = array_column($tvTagRes['data'], 'tag_name', 'ticket_id');
                }
            }

            $ticketInfo = (new \Business\CommodityCenter\Ticket())->queryTicketInfoByIds($tidArr);
            if (!empty($ticketInfo)) {
                foreach ($ticketInfo as $ticket) {
                    $ticketMap[$ticket['ticket']['id']]    = $ticket['ticket'];
                    $ticketExtMap[$ticket['ticket']['id']] = $ticket['ext'];
                }
            }
        }

        $ticketBiz = new \Business\Product\Ticket();
        foreach ($landList as &$item) {
            foreach ($item['ticketList'] as &$value) {
                $value['autoSaleRule']         = json_decode($autoArr[$value['tid']]['val'], true) ?? [];
                $value['isStartStopSaleLimit'] = $ticketAttr[$value['tid']]['val'] ?? 0;
                //参数转换
                $value['valid_period_type']          = $value['validType'];
                $value['valid_period_days']          = $value['validDays'];
                $value['type']                       = $value['pType'];
                $value['use_early_days']             = $ticketMap[$value['tid']]['use_early_days'] ?? $value['useEarlyDays'];
                $value['valid_period_start']         = $value['validStartDateStr'];
                $value['valid_period_end']           = $value['validEndDateStr'];
                $value['ext']['son_ticket_validity'] = $ticketExtMap[$value['tid']]['son_ticket_validity'] ?? 0;
                $value['ext']['effective_limit']     = $ticketExtMap[$value['tid']]['effective_limit'] ?? '';

                //添加期票标识
                $value['pre_sale'] = $ticketMap[$value['tid']]['pre_sale'] ?? null;

                //演出票类有效期特殊处理
                if ($value['type'] != 'H') {
                    $value['validityDate'] = OrderBook::getValidityDate($value);        //计算有效期
                } else {
                    $value['validityDate'] = OrderBook::getShowValidityDate($value);   //计算有效期
                }

                if (in_array($value['type'], ['A', 'B', 'C', 'F', 'G'])) {
                    $ticketInfoData = [
                        'refund_rule'               => $value['refundRule'],
                        'refund_before_early_time'  => $refundBeforeEarlyTime[$value['tid']]['val'] ?? 0,
                        'refund_after_early_time'   => $refundAfterEarlyTime[$value['tid']]['val'] ?? 0,
                    ];
                    $value['refundRules'] = $ticketBiz->refundRuleTag($ticketInfoData);
                } else {
                    $value['verifyTime']   = OrderBook::getVerifyTime($value);          //计算验证时间
                    $value['refundRules'] = OrderBook::handleRefundRule($value['refundAudit'], $value['refundRule'],
                        $value['refundEarlyTime'], $ticketExtMap[$value['tid']]['refund_after_time'] ?? 0);       //处理退款规则描述
                }

                if ($value['type'] == 'H' && isset($ticketMap[$value['tid']])) {
                    $beforeTime = $beforeShowTime[$value['tid']]['val'] ?? 0;
                    if ($beforeTime) {
                        $value['before_show_hour']   = floor($beforeTime / 60);
                        $value['before_show_minute'] = ($beforeTime % 60);
                    }

                    $value['pre_sale']     = $ticketMap[$value['tid']]['pre_sale'];
                    $value['reserve_time'] = $ticketBiz->showReserveTime($ticketMap[$value['tid']]);
                }

                //旅游券的标签补充
                if ($value['type'] == 'Q' && isset($tvTagArr[$value['tid']])) {
                    $value['tv_tag'] = $tvTagArr[$value['tid']];
                }
            }

            //资源中心 供应按钮展示 产品类型：门票、酒店、特产、演出、套票、线路
            $allowShowResourceCenterBtnType = ['A', 'B', 'C', 'J', 'H', 'F'];
            if (in_array($item['pType'], $allowShowResourceCenterBtnType)) {
                $item['showResourceCenterBtn'] = true;
            } else {
                $item['showResourceCenterBtn'] = false;
            }
        }
        $product = new \Business\Product\Product();
        $landList = $product->getProductTagForDistribution($this->_sid,$this->_mid,$landList);

        //SKU标签
        $landList = (new ProductTagBiz())->handleDistributionProductsSkuTag($this->_sid, $landList);

        $return = [
            'sid'       => $this->_sid,
            'lists'     => $landList,
            'page'      => $page,
            'pageSize'  => $pageSize,
            'totalPage' => $result['totalPage'],
            'totalNum'  => $result['totalNum'],
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 获取转分销产品列表
     *
     * @return [type] [description]
     */
    public function getProListNew()
    {
        //当前页数
        $page = I('page', 1, 'intval');
        //每页显示条数
        $pageSize = I('pageSize', 5, 'intval');
        //城市code
        $city = I('city', 0, 'intval');
        //境内外
        $oversea = I('oversea', -1, 'intval');
        //省份code
        $province = I('province', 0, 'intval');
        //景区标题
        $title = I('title', '', 'strval');
        //供应商名称
        $supplierName = I('supplier', '', 'strval');
        //产品类型
        $type = I('type', '', 'strval');
        //转分销开启状态
        $disOpen = I('openDistribution', -1, 'intval');

        $subType = I('sub_type', 0, 'intval'); //计时产品子类型

        //指定的账号允许开启现场支付票类的转分销
        $showOnsite = false;

        $evoluteQueryApi = new \Business\Product\Get\EvoluteQuery();
        $result          = $evoluteQueryApi->getDistributionProductList($this->_sid, $oversea, $province, $city, $type,
            $title, $disOpen, $showOnsite, $supplierName, $page, $pageSize, $subType);

        if (empty($result) || $result['code'] != 200) {
            $this->apiReturn(203, [], '无数据');
        }

        $landList = [];
        if (!empty($result['data']['landEvoluteProductDTO'])) {
            foreach ($result['data']['landEvoluteProductDTO'] as $landKey => $item) {
                $landList[$landKey] = $item;

                if (!empty($item['list'])) {
                    foreach ($item['list'] as $value) {

                        $tmpArr = [
                            //参数转换
                            'valid_period_type'  => $value['uuJqTicket']['delaytype'],
                            'valid_period_days'  => $value['uuJqTicket']['delaydays'],
                            'type'               => $item['pType'],
                            'use_early_days'     => $value['uuJqTicket']['useEarlyDays'],
                            'valid_period_start' => $value['uuJqTicket']['orderStart'],
                            'valid_period_end'   => $value['uuJqTicket']['orderEnd'],
                            'tid'                => $value['ticketId'],
                            'title'              => $value['uuJqTicket']['title'],
                            'distributionList'   => [
                                [
                                    'costPrice'         => $value['ticketActualTimePriceDTO']['costPrice'],
                                    'distributionLevel' => $value['lvl'],
                                    'distributionLower' => $value['active'],
                                    'openDistribution'  => $value['status'],
                                    'retailPrice'       => $value['ticketActualTimePriceDTO']['retailPrice'],
                                    'supplierId'        => $item['supplierId'],
                                    'supplierName'      => $item['sourceName'],
                                    'tid'               => $value['ticketId'],
                                ],
                            ],
                        ];

                        //演出票类有效期特殊处理
                        if ($value['type'] != 'H') {
                            $validityDate = OrderBook::getValidityDate(array_merge($value, $tmpArr));        //计算有效期
                        } else {
                            $validityDate = OrderBook::getShowValidityDate(array_merge($value, $tmpArr));   //计算有效期
                        }
                        $refundRules = OrderBook::handleRefundRule($value['refundAudit'], $value['refundRule'],
                            $value['refundEarlyTime']);       //处理退款规则描述

                        $tmpArr['validityDate'] = $validityDate;
                        $tmpArr['refundRules']  = $refundRules;

                        $landList[$landKey]['ticketList'][] = array_merge($value, $tmpArr);
                    }
                    unset($landList[$landKey]['list']);
                }
            }
        }

        // 计算总共有多少页
        $totalPage = ceil($result['data']['totalRow'] / $pageSize);

        $return = [
            'sid'       => $this->_sid,
            'lists'     => $landList,
            'page'      => $page,
            'pageSize'  => $pageSize,
            'totalPage' => $totalPage,
            'totalNum'  => $result['data']['totalRow'],
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 开启转分销
     *
     * @return [type] [description]
     */
    public function openEvolute()
    {
        //产品id
        $pid = I('pid', 0, 'intval');
        //上级供应商id
        $sid = I('sid', 0, 'intval');

        if (!$pid || !$sid) {
            $this->apiReturn(204, [], '参数错误');
        }

        // 调用新版分销链 开启分销链设置
        $upEvoluteProductBiz = new \Business\Product\Update\EvoluteProduct();
        $res                 = $upEvoluteProductBiz->openEvolute($pid, $sid, $this->_sid, $this->_mid);
        if ($res['code'] == 200) {
            $result = true;
            //ProductApi::openEvolute($this->_sid, $sid, $pid);
        } else {
            $result = false;
        }

        if ($result) {
            $this->apiReturn(200, [], '开启成功');
        } else {
            $this->apiReturn(204, [], '开启失败');
        }
    }

    /**
     * 关闭转分销
     *
     * @return [type] [description]
     */
    public function closeEvolute()
    {

        //产品id
        $pid = I('pid', 0, 'intval');
        //上级供应商id
        $sid          = I('sid', 0, 'intval');
        $closeDown    = I('closeDown', 0, 'int');
        $closeDownArr = [0, 1];//0.关闭 1.不关闭 [默认：0]
        if (!$pid || !$sid || !in_array($closeDown, $closeDownArr)) {
            $this->apiReturn(204, [], '参数错误');
        }

        // 调用新版关闭分销链方法
        $upEvoluteProductBiz = new \Business\Product\Update\EvoluteProduct();

        $res = $upEvoluteProductBiz->closeEvolute($pid, $sid, $this->_sid, $this->_mid, $closeDown);
        if ($res['code'] == 200) {
            $result = true;
            //ProductApi::closeEvolute($this->_sid, $sid, $pid, $closeDown);
        } else {
            $result = false;
        }

        if ($result) {
            pft_log('distribute/notice', "供应商: $this->_sid 操作人员: $this->_mid 产品: $pid 转分销开始处理自供应断开资源中心专享产品操作");
            //调用新的接口获取tid
            $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
            $titleMap           = $commodityTicketBiz->getTicketAndLandInfoByArrPidHandle([$pid]);
            $tid                = empty($titleMap[$pid]['tid']) ? 0 : $titleMap[$pid]['tid'];
            $tids               = [$tid];
            $sids               = [$this->_sid];
            $resourceCenterBiz  = new \Business\JavaApi\Product\ResourceCenterEnjoyAlone();
            $resourceCenterBiz->batchCloseResourceProductPriceByTidsAndFids($sids, $tids, $this->_mid);
        }

        if ($result) {
            $this->apiReturn(200, [], '关闭成功');
        } else {
            $this->apiReturn(204, [], '关闭失败');
        }

    }

    /***
     * 查询是否开通了转分销页面
     *
     */
    public function hasOpenedProdSale()
    {
        $sid = $this->_sid;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        //应用中心套餐是否开通
        $hasOpen = (new \Business\AppCenter\Module())->checkUserIsCanUseApp($sid, 'prod_sale');
        if ($hasOpen) {
            $this->apiReturn(200, ['hasOpened' => true], 'success');
        }

        if ($this->isSuper()) {
            $this->apiReturn(200, ['hasOpened' => true], 'success');
        }

        $this->apiReturn(200, ['hasOpened' => false], 'success');
    }

    /**
     * 获取更多门票信息
     * <AUTHOR>
     * @date    2020/08/27
     *
     */
    public function getTickets()
    {
        $ticketIdStr = I('get.ticket_id_str', '', 'strval');
        if (empty($ticketIdStr)) {
            $this->apiReturn(201, [], '门票Id为空');
        }

        $ticketIdArr = explode(',', $ticketIdStr);
        if (count($ticketIdArr) > 10) {
            $this->apiReturn(201, [], '一次性最多获取10条数据');
        }

        $skuTags = I('sku_tags', '', 'strval,trim');
        $skuTags = json_decode($skuTags, true);
        if (json_last_error() != JSON_ERROR_NONE || empty($skuTags)) {
            $skuTags = null;
        }

        $ticketList = \Business\JavaApi\TicketApi::getDisProductTicket($ticketIdArr, $this->_sid, $skuTags);
        // 额外填写更多信息
        $ticketExtendAttr    = new TicketExtendAttr();
        $ticketSpecialValRes = $ticketExtendAttr->queryTicketSpecialVal($ticketIdArr,
            ['is_start_stop_sale_limit', 'auto_sale_rule', 'refund_before_early_time', 'refund_after_early_time']);
        $ticketSpecialValRes = !empty($ticketSpecialValRes['data']) ? $ticketSpecialValRes['data'] : [];
        $autoArr             = [];
        $ticketAttr          = [];
        $refundBeforeEarlyTime = $refundAfterEarlyTime = [];
        foreach ($ticketSpecialValRes as $ticketConf) {
            if ($ticketConf['key'] == 'auto_sale_rule') {
                $autoArr[$ticketConf['ticketId']] = $ticketConf;
            }
            if ($ticketConf['key'] == 'is_start_stop_sale_limit') {
                $ticketAttr[$ticketConf['ticketId']] = $ticketConf;
            }
            if ($ticketConf['key'] == 'refund_before_early_time') {
                $refundBeforeEarlyTime[$ticketConf['ticketId']] = $ticketConf;
            }
            if ($ticketConf['key'] == 'refund_after_early_time') {
                $refundAfterEarlyTime[$ticketConf['ticketId']] = $ticketConf;
            }
        }

        $ticketMap = [];
        $ticketInfo = (new \Business\CommodityCenter\Ticket())->queryTicketInfoByIds($ticketIdArr);
        if (!empty($ticketInfo)) {
            foreach ($ticketInfo as $ticket) {
                $ticketMap[$ticket['ticket']['id']]    = $ticket['ticket'];
            }
        }

        $ticketExtMap = [];
        $ticketInfo = (new \Business\CommodityCenter\Ticket())->queryTicketInfoByIds($ticketIdArr);
        if (!empty($ticketInfo)) {
            foreach ($ticketInfo as $ticket) {
                $ticketExtMap[$ticket['ticket']['id']] = $ticket['ext'];
            }
        }

        $ticketBiz = new \Business\Product\Ticket();
        foreach ($ticketList as &$value) {
            $value['autoSaleRule']         = empty($autoArr[$value['tid']]['val']) ? [] : json_decode($autoArr[$value['tid']]['val'],
                true);
            $value['isStartStopSaleLimit'] = $ticketAttr[$value['tid']]['val'] ?? 0;
            //参数转换
            $value['valid_period_type']  = $value['validType'];
            $value['valid_period_days']  = $value['validDays'];
            $value['type']               = $value['pType'];
            $value['use_early_days']     = $value['useEarlyDays'];
            $value['valid_period_start'] = $value['validStartDateStr'];
            $value['valid_period_end']   = $value['validEndDateStr'];

            //添加期票标识
            $value['pre_sale'] = $ticketMap[$value['tid']]['pre_sale'] ?? null;

            $value['ext']['son_ticket_validity'] = $ticketExtMap[$value['tid']]['son_ticket_validity'] ?? 0;
            $value['ext']['effective_limit']     = $ticketExtMap[$value['tid']]['effective_limit'] ?? '';

            //演出票类有效期特殊处理
            if ($value['type'] != 'H') {
                $value['validityDate'] = OrderBook::getValidityDate($value);
            } else {
                $value['validityDate'] = OrderBook::getShowValidityDate($value);
            }

            if (in_array($value['type'], ['A', 'B', 'C', 'F', 'G'])) {
                $ticketInfoData = [
                    'refund_rule'               => $value['refundRule'],
                    'refund_before_early_time'  => $refundBeforeEarlyTime[$value['tid']]['val'] ?? 0,
                    'refund_after_early_time'   => $refundAfterEarlyTime[$value['tid']]['val'] ?? 0,
                ];
                $value['refundRules'] = $ticketBiz->refundRuleTag($ticketInfoData);
            } else {
                //处理退款规则描述
                $value['refundRules'] = OrderBook::handleRefundRule($value['refundAudit'], $value['refundRule'],
                    $value['refundEarlyTime'], $ticketExtMap[$value['tid']]['refund_after_time'] ?? 0);
            }

        }

        //SKU标签
        $ticketList = (new ProductTagBiz())->handleDistributionProductsSkuTagMoreTickets($this->_sid, $ticketList);

        $product = new \Business\Product\Product();
        $ticketList = $product ->getProductTagForDistributionMoreTickets($this->_sid,$this->_mid,$ticketList);

        $this->apiReturn(200, ['list' => $ticketList], 'success');
    }

    /**
     * 批量获取结算价
     * <AUTHOR>
     * @date 2020/10/9
     *
     * @return array
     */
    public function queryBatchSettlement()
    {
        $params = I('post.params');

        if (empty($params)) {
            $this->apiReturn(201, [], '参数缺失');
        }

        $paramArr = [];

        foreach ($params as $item) {
            $paramArr[] = [
                'ticketId' => intval($item['ticketId']),
                'fid'      => intval($this->_sid),
                'sid'      => intval($item['sid']),
            ];
        }

        $priceLib = new \Business\Product\Price();
        $priceRes = $priceLib->batchSettlementQuery($paramArr);

        $this->apiReturn($priceRes['code'], $priceRes['data'], $priceRes['msg']);
    }
    /**
     * 批量获取结算价
     * @aurhtor  PeiJun Li
     * @data  2023-04-04
     *
     * @return array
     */
    public function queryBatchSettlementNew()
    {
        $params = I('post.params');

        if (empty($params)) {
            $this->apiReturn(201, [], '参数缺失');
        }

        $paramArr = [];

        foreach ($params as $item) {
            $paramArr[] = [
                'ticketId' => intval($item['ticketId']),
                'fid'      => intval($this->_sid),
                'sid'      => intval($item['sid']),
            ];
        }

        $priceLib = new \Business\Product\Price();
        $priceRes = $priceLib->batchSettlementQueryNew($paramArr);

        $this->apiReturn($priceRes['code'], $priceRes['data'], $priceRes['msg']);
    }

    /**
     * 配置分销向下权限
     * <AUTHOR>
     * @date 2021/9/26
     *
     * @return array
     */
    public function configureDowngradeEvoluteLimit()
    {
        $fid        = I('fid', 0, 'intval');
        $tid        = I('tid', 0, 'intval');
        $openStatus = I('openStatus', 0, 'intval');
        $level      = I('level', 0, 'intval');
        if (empty($fid)) {
            $this->apiReturn(201, [], '参数缺失');
        }

        //$evoluteLimiterService = new EvoluteLimiterService();
        //$result = $evoluteLimiterService->configureDowngradeEvoluteLimit($this->_sid, $fid, $tid, $openStatus, $level, $this->_mid);
        $evoluteLimiterService = new BatchOperateDistributor();
        $result = $evoluteLimiterService->configureDowngradeEvoluteLimit($this->_sid, $fid, $tid, $openStatus, $level, $this->_mid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 批量加入黑名单
     * <AUTHOR> lingfeng
     * 2022/5/23 20:20
     */
    public function batchConfigureDowngradeEvoluteLimit()
    {
        $fidArr = I('post.fidList');
        if (empty($fidArr)){
            $this->apiReturn(201, [], '参数缺失');
        }
        $fidArr = array_map(function ($fid){
            return ['fid' => $fid];
        }, $fidArr);
        $batchOperate = new BatchOperateDistributor();
        $result = $batchOperate->batchConfigureDowngradeEvoluteLimit($this->_sid, $this->_sid, $fidArr);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 分页查询黑名单中的分销商
     * <AUTHOR>
     * @date 2021/9/26
     *
     * @return array
     */
    public function queryBlacklistFidByPaging()
    {
        $pageNum    = I('pageNum', 0, 'intval');
        $pageSize   = I('pageSize', 0, 'intval');
        $openStatus = I('openStatus', 0, 'intval');
        $fidList    = I('fidList', null);
        if (empty($pageNum) || empty($pageSize)) {
            $this->apiReturn(201, [], '参数缺失');
        }

        $evoluteLimiterService = new EvoluteLimiterService();
        $result = $evoluteLimiterService->queryBlacklistFidByPaging($this->_sid, $openStatus, $pageNum, $pageSize, $fidList);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}
