<?php
/**
 * 打包套票
 *
 * <AUTHOR>
 * @date 2017-05-04
 */

namespace Controller\product;

use Library\Controller;
use Model\Member\Member;
use Model\Product\Ticket;
use Model\Product\Land;
use Model\Product\PriceRead;

class BindTicket extends Controller
{
    private $_sid;
    private $_memberID;
    //支持绑定的子产品类型
    private $_childPType = [
        'A', //景区
        'B', //线路
        'G', //餐饮
        'H', //演出
    ];
    //票模型
    private $_ticketModel;
    //套票模型
    private $_packTicketModel;

    public function __construct()
    {
        $loginInfo          = $this->getLoginInfo();
        $this->_sid         = $loginInfo['sid'];
        $this->_memberID    = $loginInfo['memberID'];
        $this->_memberModel = new Member();
    }

    /**
     * 获取票模型
     * @return Ticket
     */
    private function _getTicketModel()
    {
        if (empty($this->_ticketModel)) {
            $this->_ticketModel = new Ticket();
        }

        return $this->_ticketModel;
    }

    /**
     * 获取已被绑定的票信息
     *
     * @param  int tid    主票ID
     *
     * return array
     */
    public function getBoundTicket()
    {
        //会员主id
        $sid = $this->_sid;
        //主票ID
        $mainTid = I('post.tid', 0, 'intval');
        if (!$mainTid) {
            $this->apiReturn(203, [], '票ID错误');
        }

        //票id数组,先把主票放入后续一起查询
        $tidArr = [$mainTid];
        //已绑定的票
        $bTickets = [];
        //统一初始化模型
        $ticketModel    = $this->_getTicketModel();
        $packBiz        = new \Business\Product\PackTicket();
        $priceReadModel = new PriceRead();

        //获取主票绑定的子票
        $boundRes = $packBiz->getTickets($mainTid);
        if ($boundRes) {
            //若有绑定记录，取出所有绑定票id
            foreach ($boundRes as $bind) {
                //提取已绑定票的数据
                $bTickets[] = $bind;
                //提取票id，便于统一查询
                $tidArr[] = $bind['tid'];
            }
        }
        //去重
        $tidArr = array_unique($tidArr);

        //统一获取门票属性
        $filed     = [
            't.id as tid',
            't.pid',
            't.landid',
            't.title as ttitle',
            'l.title as ltitle',
            'p.apply_limit',
        ];
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,pid,landid,title', 'apply_limit', 'title');

        if (!$ticketArr) {
            $this->apiReturn(204, [], '票信息查询失败');
        }

        $ticketsInfo = [];
        foreach ($ticketArr as $ticketInfo) {
            $ticketsInfo[$ticketInfo['ticket']['id']] = [
                'tid'         => $ticketInfo['ticket']['id'],
                'pid'         => $ticketInfo['ticket']['pid'],
                'landid'      => $ticketInfo['ticket']['landid'],
                'ttitle'      => $ticketInfo['ticket']['title'],
                'ltitle'      => $ticketInfo['land']['title'],
                'apply_limit' => $ticketInfo['product']['apply_limit'],
            ];
        }

        if (empty($ticketsInfo[$mainTid])) {
            //无主票信息，查询失败
            $this->apiReturn(204, [], '查无主票信息，查询失败');
        }

        //主票信息
        $mainTicket = [
            'tid'    => $mainTid, //票id
            'pid'    => $ticketsInfo[$mainTid]['pid'], //产品id
            'ttitle' => $ticketsInfo[$mainTid]['ttitle'], //票名
            'lid'    => $ticketsInfo[$mainTid]['landid'], //景区id
            'ltitle' => $ticketsInfo[$mainTid]['ltitle'], //景区名
        ];
        unset($ticketsInfo[$mainTid]);

        //子票信息
        $childTicket = [];
        //日期
        $today = date('Y-m-d');

        //获取产品状态配置列表
        $prodState = load_config('product_status', 'product');

        foreach ($bTickets as $bt) {
            $tmpTid = $bt['tid'];
            $tmpPid = $bt['pid'];
            //当日供应价
            $fpriceRes = $priceReadModel->Dynamic_Price_And_Storage($sid, $tmpPid, $today, $mode = 1, 0, 0, $bt['aid'],
                '', '', 'id');
            $fprice    = ($fpriceRes['code'] == 200 && $fpriceRes['price'] != -1) ? ($fpriceRes['price'] / 100) : false;
            //当日零售价
            $uprice = $ticketModel->getRetailPrice($tmpPid, $today);

            //判断子票状态
            if ($uprice === false) {
                //无零售价，表明已过期
                $status = '过期';
            } else {
                if ($fprice === false) {
                    //无供应价，为分销链解除
                    $uprice = false;
                    $status = '分销失效';
                } else {
                    //其他状态
                    $status = $prodState[$ticketsInfo[$tmpTid]['apply_limit']];
                }
            }

            $childTicket[] = [
                'tid'    => $tmpTid, //票id
                'pid'    => $tmpPid, //产品id
                'aid'    => $bt['aid'], //上级供应商id
                'ttitle' => $ticketsInfo[$tmpTid]['ttitle'], //票名
                'lid'    => $ticketsInfo[$tmpTid]['landid'], //景区id
                'ltitle' => $ticketsInfo[$tmpTid]['ltitle'], //景区名
                'status' => $status, //票状态
                'fprice' => $fprice === false ? '-' : $fprice, //供应价
                'uprice' => $uprice === false ? '-' : $uprice, //零售价
            ];
            unset($tmpTid);
        }

        $data = [
            'main_ticket'  => $mainTicket,  //主票
            'child_ticket' => $childTicket, //子票
        ];
        $this->apiReturn(200, $data, '');
    }

    /**
     * @deprecated  (接口已迁移 /r/product_Product/getPacketProducts)
     * 查询可以绑定的景点
     *
     * @param  int tid    主票ID
     * @param  string ltitle 景点名称关键字
     *
     * return array
     */
    public function queryLand()
    {
        //主票ID
        $mainTid = I('post.tid', 0, 'intval');
        if (!$mainTid) {
            $this->apiReturn(203, [], '票ID错误');
        }

        $ltitle = I('post.ltitle', '', 'strval');
        $ltitle = trim($ltitle);

        //统一初始化模型
        $landModel   = new Land();
        $ticketModel = $this->_getTicketModel();

        //获取可销售的产品(直销+分销),需要排除主票id
        $saleProduct = $this->_getSaleProduct('', $mainTid);

        //提取景区lid
        $lidArr = array_unique(array_column($saleProduct, 'landid'));
        if (!$lidArr) {
            $this->apiReturn(200, [], '无可以绑定产品');
        }

        //若有景区名称关键词，进行搜索
        if ($ltitle) {
            $size  = count($lidArr);
            //$field = 'id,title';
            ////在已有的景区列表里搜索匹配关键字的景区
            //$landRes = $landModel->searchByName($ltitle, 1, $size, $field, '', $lidArr);

            $landRes  = [];
            $statusArr = [1];

            $javaApi     = new \Business\CommodityCenter\Land();
            $landInfoArr = $javaApi->queryLandMultiQueryByAdminAndPaging($lidArr, 1, $size, $ltitle,
                $orderByClause = '', false, [], $statusArr);
            if ($landInfoArr['list']) {
                $landRes = $landInfoArr['list'];
            }
            if (!$landRes) {
                $this->apiReturn(200, [], '无可以绑定产品');
            }

            $lidArr = array_column($landRes, 'id');
        }

        //反转景区id列表，便于后续利用key快速查询
        $landDict = array_flip($lidArr);
        //候选票tid
        $tidArr = [];
        //候选票pid
        $pidArr = [];
        //pid和tid对照
        $pidRel = [];
        foreach ($saleProduct as $product) {
            $tmpLid = $product['landid'];
            if (isset($landDict[$tmpLid])) {
                //提取有效的候选票pid
                $pidArr[]                = $product['pid'];
                $pidRel[$product['pid']] = $product['tid'];
            }
            unset($tmpLid);
        }

        //去除过期的门票
        $pidArr = $this->_removeExpiredPids($pidArr);
        //去除对应的tid
        foreach ($pidArr as $tmpPid) {
            //取出未过期的票id
            $tidArr[] = $pidRel[$tmpPid];
        }

        //过滤出可以绑定的子票id
        $tidArr = $this->_filterMatchChildTicket($mainTid, $tidArr);
        if (!$tidArr) {
            $this->apiReturn(200, [], '无可以绑定产品');
        }

        //反转票id列表，便于后续利用key快速查询
        $tidDict     = array_flip($tidArr);
        $productList = [];

        foreach ($saleProduct as $product) {
            $tmpLid = $product['landid'];
            $tmpTid = $product['tid'];
            if (isset($tidDict[$tmpTid])) {
                $ticketInfo = [
                    'tid'       => $product['tid'],  //票id
                    'pid'       => $product['pid'],  //产品id
                    'lid'       => $product['landid'],  //景区id
                    'ttitle'    => $product['ttitle'],  //门票名
                    'ltitle'    => $product['title'],   //景区名
                    'apply_sid' => $product['apply_sid'], //上级供应商id
                ];
                if (isset($productList[$tmpLid])) {
                    $productList[$tmpLid]['tickets'][] = $ticketInfo;
                } else {
                    $productList[$tmpLid] = [
                        'lid'     => $product['landid'],
                        'ltitle'  => $product['title'],
                        'tickets' => [$ticketInfo],
                    ];
                }
            }
            unset($tmpLid, $tmpTid);
        }

        $this->apiReturn(200, $productList, '');
    }

    /**
     * 绑定主票与子票
     *
     * @param  int tid    主票ID
     * @param  string child 子票json字串
     *
     * return array
     */
    public function bindTicket()
    {
        //会员主帐号id
        $sid = $this->_sid;
        //每个票绑定上的张数
        $bindNum = 1; //暂时只能绑定1张

        //主票ID
        $mainTid = I('post.tid', 0, 'intval');
        if (!$mainTid) {
            $this->apiReturn(203, [], '主票ID错误');
        }

        $bindAvail = $this->_checkBindLandType($mainTid);
        if ($bindAvail == false) {
            $this->apiReturn(203, [], '该产品类型无法绑定门票');
        }

        $childStr = I('post.child', '', 'strval');
        $childArr = json_decode($childStr, true);
        if (!is_array($childArr)) {
            $this->apiReturn(203, [], '子票格式错误');
        }

        //票ID数组
        $tidArr      = [$mainTid];
        $childTidArr = [];
        foreach ($childArr as $child) {
            //检测参数是否正确
            if (empty($child['tid']) || empty($child['aid'])) {
                $this->apiReturn(203, [], '子票参数错误');
            }
            $tidArr[]      = $child['tid'];
            $childTidArr[] = $child['tid'];
        }

        $tidArr      = array_unique($tidArr);
        $childTidArr = array_unique($childTidArr);

        //统一初始化模型
        $ticketModel = $this->_getTicketModel();
        $packBiz     = new \Business\Product\PackTicket();

        //获取门票属性
        $filed = [
            't.id as tid',
            't.pid',
            't.landid',
            't.apply_did',
            't.ddays',
            't.buy_limit_up',
            't.buy_limit_low',
            'f.dhour',
            'f.buy_limit',
            'f.buy_limit_date',
            'f.buy_limit_num',
        ];

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,pid,landid,ddays,buy_limit_up,buy_limit_low,apply_did',
            '', 'p_type',
            'dhour,buy_limit,buy_limit_date,buy_limit_num');

        if (!$ticketArr) {
            $this->apiReturn(204, [], '票查询失败');
        }

        $ticketInfo = [];
        if (!empty($ticketArr)) {
            foreach ($ticketArr as $ticket) {
                $ticketInfo[$ticket['ticket']['id']]['tid']            = $ticket['ticket']['id'];
                $ticketInfo[$ticket['ticket']['id']]['pid']            = $ticket['ticket']['pid'];
                $ticketInfo[$ticket['ticket']['id']]['landid']         = $ticket['ticket']['landid'];
                $ticketInfo[$ticket['ticket']['id']]['apply_did']      = $ticket['ticket']['apply_did'];
                $ticketInfo[$ticket['ticket']['id']]['ddays']          = $ticket['ticket']['ddays'];
                $ticketInfo[$ticket['ticket']['id']]['buy_limit_up']   = $ticket['ticket']['buy_limit_up'];
                $ticketInfo[$ticket['ticket']['id']]['dhour']          = $ticket['land_f']['dhour'];
                $ticketInfo[$ticket['ticket']['id']]['buy_limit']      = $ticket['land_f']['buy_limit'];
                $ticketInfo[$ticket['ticket']['id']]['buy_limit_date'] = $ticket['land_f']['buy_limit_date'];
                $ticketInfo[$ticket['ticket']['id']]['buy_limit_num']  = $ticket['land_f']['buy_limit_num'];
                $ticketInfo[$ticket['ticket']['id']]['p_type']         = $ticket['land']['p_type'];
            }
        }

        // 主票id和绑定的子票id存在则不让绑定
        if (in_array($mainTid, $childTidArr)) {
            $this->apiReturn(204, [], '子票参数错误:不能绑定本票主票');
        }

        //已经是套票的不能让继续绑定成为子票
        $childData = $packBiz->getTickets($childTidArr);
        if (!empty($childData)) {
            $this->apiReturn(204, [], '子票参数错误:子票不可绑定(子票已被绑定成为主票)');
        }

        //已经是子票的不让成为主票
        $existChildMainTid = $packBiz->getParentsByTid($mainTid);
        if (!empty($existChildMainTid)) {
            $this->apiReturn(204, [], '主票参数错误:主票不可绑定(主票已被绑定成为子票)');
        }

        //演出票已经是套票子票的不让成为子票
        $isValid = $packBiz->checkPackSonTicketForShowBind($ticketInfo);
        if ($isValid['code'] != 200) {
            $this->apiReturn($isValid['code'], [], $isValid['msg']);
        }

        if (empty($ticketInfo[$mainTid])) {
            $this->apiReturn(204, [], '主票查询失败');
        }

        //只有主票的顶级供应商才可以绑定子票
        if ($ticketInfo[$mainTid]['apply_did'] != $sid) {
            $this->apiReturn(203, [], '无权限绑定');
        }

        //主票信息
        $mainInfo = $ticketInfo[$mainTid];

        unset($tidArr);

        //提取待绑定tid数组
        //$tidArr = $childArr['tid'];
        $tidArr = array_column($childArr, 'tid');

        //获取可销售的产品(直销+分销),在待绑定的票id中查询
        $saleProduct = $this->_getSaleProduct($tidArr);
        if (!$saleProduct) {
            $this->apiReturn(203, [], '非可销售票，绑定失败');
        }

        unset($tidArr);
        //销售中的票tid
        $saleTidArr = [];
        //销售中的票pid
        $salePidArr = [];
        //pid和tid对照
        $salePidRel = [];
        foreach ($saleProduct as $product) {
            $salePidArr[]                = $product['pid'];
            $salePidRel[$product['pid']] = $product['tid'];
        }

        //去除过期的门票
        $salePidArr = $this->_removeExpiredPids($salePidArr);
        foreach ($salePidArr as $tmpPid) {
            //取出未过期的票tid
            $saleTidArr[] = $salePidRel[$tmpPid];
        }

        //待插入表的绑定数据
        $packageData = [];

        foreach ($childArr as $child) {
            if (empty($ticketInfo[$child['tid']])) {
                $this->apiReturn(204, [], '子票查询失败');
            }

            $tmpTid     = $child['tid'];
            $tmpBindNum = $child['bind_num'];
            if (empty($tmpBindNum)) {
                $this->apiReturn(204, [], '子票绑定数量设置错误');
            }

            if (!in_array($tmpTid, $saleTidArr)) {
                $this->apiReturn(203, [], '非可销售票，绑定失败');
            }

            //子票信息
            $childInfo = $ticketInfo[$tmpTid];

            if (!$this->_checkBindAvail($mainInfo, $childInfo)) {
                $this->apiReturn(203, [], '票属性冲突，绑定失败');
            }
            //添加子票绑定数据
            $packageData[] = [
                'parent_tid' => $mainTid,
                'lid'        => $childInfo['landid'],
                'tid'        => $tmpTid,
                'pid'        => $childInfo['pid'],
                'aid'        => $child['aid'],
                'num'        => $tmpBindNum,
            ];
            unset($tmpTid);
        }

        $rmRes = $this->_removeBound($mainTid);
        if ($rmRes == false) {
            $this->apiReturn(201, [], '解除原绑定失败');
        }

        $saveRes = $packBiz->savePackageTickets($packageData);
        if ($saveRes === false) {
            $this->apiReturn(201, [], '绑定失败');
        }

        //绑定日志
        $logMsg = "捆绑门票,主票ID:{$mainTid}";
        foreach ($packageData as $idx => $pData) {
            $num    = $idx + 1;
            $logMsg .= ";【子票{$num}】票ID:{$pData['tid']},供应商ID:{$pData['aid']},绑定数量:{$pData['num']}";
        }
        pft_log('product/bind_ticket', $logMsg);

        $this->apiReturn(200, [], '绑定成功');
    }

    /**
     * 解除绑定（对外入口）
     *
     * @param  int tid     主票ID，必传，只传tid则解除所有绑定的子票
     * @param  int ctid    子票ID，并需与caid同时使用，可以解除对应绑定的子票。
     * @param  int caid    子票供应商ID，并需与ctid同时使用，可以解除对应绑定的子票。
     */
    public function removeBindTicket()
    {
        //主票ID
        $mainTid = I('post.tid', 0, 'intval');
        if (!$mainTid) {
            $this->apiReturn(203, [], '主票ID错误');
        }

        //子票ID
        $childTid = I('post.ctid', 0, 'intval');
        //子票供应商id
        $childAid = I('post.caid', 0, 'intval');

        //尝试解绑
        $rmRes = $this->_removeBound($mainTid, $childTid, $childAid);
        if ($rmRes == false) {
            $this->apiReturn(201, [], '解除绑定失败');
        }

        $this->apiReturn(200, [], '解除绑定成功');

    }

    /**
     * 检测该票的产品类型是否可以绑定子票（目前只接受演出产品）
     *
     * @param  int  $tid  票id
     *
     * @return bool
     */
    private function _checkBindLandType($tid)
    {
        //可以绑定的主产品类型，目前只接受演出产品
        $pTypeArr = [
            'H', //演出产品
        ];

        //获取景点信息
        $commodityLandBiz = new \Business\CommodityCenter\Land();
        $landInfo         = $commodityLandBiz->getLandInfoByTidToJava($tid);
        //景点产品类型
        $pType = $landInfo['p_type'];
        if (!in_array($pType, $pTypeArr)) {
            return false;
        }

        return true;
    }

    /**
     * 解除绑定
     *
     * @param $mainTid   主票id（只有主票id则删除所有绑定的子票）
     * @param $childTid  子票id
     * @param $childAid  子票供应商id
     *
     * @return bool
     */
    private function _removeBound($mainTid, $childTid = 0, $childAid = 0)
    {
        $sid = $this->_sid;
        //统一初始化模型
        $ticketModel = $this->_getTicketModel();
        $packBiz     = new \Business\Product\PackTicket();

        $mainTicket = $ticketModel->getTicketInfoById($mainTid, 'id, apply_did');
        if (!$mainTicket) {
            //未查询到主票信息不能删除
            return false;
        }

        if ($mainTicket['apply_did'] != $sid) {
            //只有主票的顶级供应商才可以解绑子票
            return false;
        }

        //获取目前已绑定的信息
        $boundRes = $packBiz->getTickets($mainTid);
        if ($boundRes === false) {
            //sql错误,查询失败
            return false;
        }

        //是否只删除单条记录标记，默认为false
        $rmSingle = false;
        if (!empty($childTid) && !empty($childAid)) {
            //如果指定了子票id和子票供应商，则只删除对应的单条数据
            $rmSingle = true;
        }

        if ($rmSingle) {
            //需删除单条记录，则进行筛选
            $rmInfo = [];
            foreach ($boundRes as $boundData) {
                if ($boundData['tid'] == $childTid && $boundData['aid'] == $childAid) {
                    //根据子票id和子票供应商id匹配到需要删除的唯一数据
                    $rmInfo[] = $boundData;
                    break;
                }
            }

            if (!$rmInfo) {
                //若未匹配到数据，则表示参数有误，删除失败
                return false;
            }
        } else {
            //需删除所有绑定记录
            $rmInfo = $boundRes;
        }

        //删除记录非空则进行删除
        if ($rmInfo) {
            $rmIds    = array_column($rmInfo, 'id');
            $boundIds = implode(',', $rmIds);
            //进行删除
            $rmRes = $packBiz->rmChildTicket($boundIds);
            if ($rmRes === false) {
                //sql错误,删除失败
                return false;
            }

            //执行到此已解绑成功，记录解除绑定日志
            $logMsg = "解除绑定,主票ID:{$mainTid}";
            foreach ($rmInfo as $idx => $pData) {
                $num    = $idx + 1;
                $logMsg .= ";【子票{$num}】票ID:{$pData['tid']},供应商ID:{$pData['aid']}";
            }
            pft_log('product/bind_ticket', $logMsg);
        }

        return true;
    }

    /**
     * 获取可销售的产品(直销+转分销)
     *
     * @param  array  $includeTidArr  包含的票id数组 (优先)
     * @param  int  $excludeTid  排除的票id  （一般是主票id）
     *
     * @return array
     */
    private function _getSaleProduct($includeTidArr = [], $excludeTid = 0)
    {
        $sid        = $this->_sid;
        $childPType = $this->_childPType;
        $pay        = 1; // 在线支付

        //通过门票id 获取出产品id
        $javaApi       = new \Business\CommodityCenter\Ticket();
        $includePidArr = $javaApi->queryProductIdsByTicketIds($includeTidArr);
        $excludePidArr = $javaApi->queryProductIdsByTicketIds($excludeTid);
        $excludePid    = !empty($excludePidArr) ? $excludePidArr[0] : 0;

        $productBiz  = new \Business\Product\Product();
        $saleProduct = $productBiz->getSaleProductByCondition($sid, 0, 0, 0, '', $childPType, $pay, $includePidArr,
            $excludePid)['list'];

        foreach ($saleProduct as &$product) {
            $product['tid'] = $product['ticket_id'];
        }

        //$ticketModel = $this->_getTicketModel();
        //$option      = [];

        //额外需查询的条件
        //$where = [
        //    'l.p_type' => ['in', $childPType], //产品类型
        //    't.pay'    => 1, //只可以绑在线支付
        //];

        //if ($includeTidArr) {
        //    //包含的票id数组
        //    if (is_array($includeTidArr)) {
        //        $where['t.id'] = ['in', $includeTidArr];
        //    } else {
        //        $where['t.id'] = ['eq', $includeTidArr];
        //    }
        //} elseif ($excludeTidArr) {
        //    //排除的票id数组
        //    if (is_array($excludeTidArr)) {
        //        $where['t.id'] = ['not in', $excludeTidArr];
        //    } else {
        //        $where['t.id'] = ['neq', $excludeTidArr];
        //    }
        //}

        //$option['where'] = $where;
        ////直销产品
        //$selfProduct = $ticketModel->getSaleProducts($sid, $option);
        ////转分销产品
        ////$disProduct  = $ticketModel->getSaleDisProducts($sid, [], $option);
        //$disProduct  = $ticketModel->getSaleDisProductsByFidSourceIdStatus($sid, $option);
        ////可销售产品
        //$saleProduct = array_merge($selfProduct, $disProduct);

        return $saleProduct;
    }

    /**
     * 去除已过期的pid
     *
     * @param $pidArr 待处理的pid数组
     *
     * @return array
     */
    private function _removeExpiredPids($pidArr)
    {
        $ticketModel = $this->_getTicketModel();
        //产品当日零售价数组（用来过滤过期的产品）
        $retailPriceArr = $ticketModel->getMuchRetailPrice($pidArr);
        //未过期的门票可以查询到零售价，因此有数据的都是未过期门票
        $resPidArr = array_keys($retailPriceArr);

        return $resPidArr;
    }

    /**
     * 过滤出可以绑定的产品
     *
     * @param  int  $mainTid  主票ID
     * @param  array  $childTidArr  候选子票ID数组
     *
     * @return array
     */
    private function _filterMatchChildTicket($mainTid, $childTidArr)
    {
        $ticketModel = $this->_getTicketModel();

        //获取门票属性
        $filed = [
            't.id as tid',
            't.ddays',
            't.buy_limit_up',
            't.buy_limit_low',
            'f.dhour',
            'f.buy_limit',
            'f.buy_limit_date',
            'f.buy_limit_num',
        ];

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoById($mainTid, 'id,ddays,buy_limit_up,buy_limit_low', '', '',
            'dhour,buy_limit,buy_limit_date,buy_limit_num');
        if (!$ticketArr) {
            return [];
        }

        $mainInfo = [
            'tid'            => $ticketArr['ticket']['id'],
            'ddays'          => $ticketArr['ticket']['ddays'],
            'buy_limit_up'   => $ticketArr['ticket']['buy_limit_up'],
            'buy_limit_low'  => $ticketArr['ticket']['buy_limit_low'],
            'dhour'          => $ticketArr['land_f']['dhour'],
            'buy_limit'      => $ticketArr['land_f']['buy_limit'],
            'buy_limit_date' => $ticketArr['land_f']['buy_limit_date'],
            'buy_limit_num'  => $ticketArr['land_f']['buy_limit_num'],
        ];

        $javaApi        = new \Business\CommodityCenter\Ticket();
        $childTicketArr = $javaApi->queryTicketInfoByIds($childTidArr, 'id,ddays,buy_limit_up,buy_limit_low', '', '',
            'dhour,buy_limit,buy_limit_date,buy_limit_num');

        if (!$childTicketArr) {
            $this->apiReturn(204, [], '票查询失败');
        }

        //待选子票属性
        $childRes = [];
        foreach ($childTicketArr as $ticketInfos) {
            $childRes[$ticketInfos['ticket']['id']] = [
                'tid'            => $ticketInfos['ticket']['id'],
                'ddays'          => $ticketInfos['ticket']['ddays'],
                'buy_limit_up'   => $ticketInfos['ticket']['buy_limit_up'],
                'buy_limit_low'  => $ticketInfos['ticket']['buy_limit_low'],
                'dhour'          => $ticketInfos['land_f']['dhour'],
                'buy_limit'      => $ticketInfos['land_f']['buy_limit'],
                'buy_limit_date' => $ticketInfos['land_f']['buy_limit_date'],
                'buy_limit_num'  => $ticketInfos['land_f']['buy_limit_num'],
            ];
        }

        if (!$childRes) {
            return [];
        }

        $availTidArr = [];
        //检测并提取出可以绑定的子票id
        foreach ($childRes as $childInfo) {
            $bindAvail = $this->_checkBindAvail($mainInfo, $childInfo);
            if ($bindAvail) {
                $availTidArr[] = $childInfo['tid'];
            }
        }

        return $availTidArr;
    }

    /**
     * 检测子票是否可以绑定
     *
     * @param  array  $mainInfo  主票信息
     * @param  array  $childInfo  子票信息
     *
     * @return bool
     */
    private function _checkBindAvail($mainInfo, $childInfo)
    {
        /*
        //需要比较的属性
        [
            'tid',  //票id
            'ddays', //提前天数
            'buy_limit_up',  //购买上限
            'buy_limit_low',  //购买下限
            'dhour',  //提前的时间点
            'buy_limit', //购票限制
            'buy_limit_date', //购票限制日期
            'buy_limit_num', //购票限制数量
        ]
        */
        //绑定检测，以主票的规则为严格标准。
        //预定限制
        //提前天数
        if ($mainInfo['ddays'] < $childInfo['ddays']) {
            //主票提前预定天数不能小于子票提前预定天数
            return false;
        }

        if ($mainInfo['ddays'] == $childInfo['ddays'] && strtotime($mainInfo['dhour']) > strtotime($childInfo['dhour'])) {
            //提前预定天数相同，主票规定时间点不能迟于子票时间点
            return false;
        }

        if ($mainInfo['buy_limit_low'] < $childInfo['buy_limit_low']) {
            //主票购买下限不能小于子票
            return false;
        }

        if ($childInfo['buy_limit_up'] != 0 && ($mainInfo['buy_limit_up'] == 0 || $mainInfo['buy_limit_up'] > $childInfo['buy_limit_up'])) {
            //主票购买上限不能大于子票
            return false;
        }

        /*
        //购票限制 暂不限制
        if ($childInfo['buy_limit'] != 0) {
            //子票有购票限制的才需要检测
            if ($mainInfo['buy_limit'] != $childInfo['buy_limit']) {
                //主票与子票购票限制类型必须相同
                return false;
            }
            //todo: 其他限制待添加
        }
        */

        return true;
    }

}