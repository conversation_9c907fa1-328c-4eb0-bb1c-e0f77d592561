<?php
/**
 * 快捷购票
 * 下单前置规则处理（产品下单信息预设）
 * @date 2021-07-07
 */

namespace Controller\Product;

use Library\Cache\Cache;
use Library\Controller;
use Business\Product\FastBuyTicketsPreOrder AS FastBuyBiz;
use Business\Member\PartnerSuppliers as PartnerSuppliersBiz;

class FastBuyTicketsPreOrder extends Controller
{
    private $_sid      = null;
    private $_memberId = null;
    private $_dtype    = null;
    private $_dname    = null;

    public function __construct()
    {
        $loginInfoArr    = $this->getLoginInfo();
        $this->_sid      = $loginInfoArr['sid'];
        $this->_memberId = $loginInfoArr['memberID'];
        $this->_dtype    = $loginInfoArr['dtype'];
        $this->_dname    = $loginInfoArr['dname'];
    }

    /***************************** 快速购票 - 规则配置 ****************************************/

    /**
     * 新增规则
     * <AUTHOR>
     * @date 2021/7/7
     *
     */
    public function addRule()
    {
        $code = 200;
        $msg = '';
        $data = [];

        try {
            $sid      = $this->_sid;
            $memberId = $this->_memberId;
            $dtype    = $this->_dtype;

            //请求参数
            $title            = $this->_checkTitle(); //规则名称
            $landId           = $this->_checkLandId();//景区id
            $rulesInfo        = $this->_checkRules(); //规则信息
            $type             = $this->_checkType(); //下单方式 1:下单预定 2：报团预定
            $distributeType   = I('post.distribute_type', 0, 'intval'); //分销类型 1：全部一级分销商 2：全部一级+二级分销商 3：指定分销商
            $distributeGroups = I('post.distribute_group', '', 'strval'); //分销组
            $distributeIds    = I('post.distribute_ids', '', 'strval'); //分销商
            if (empty($distributeType)) {
                throw new \Exception("分消息信息有误", 203);
            }

            $fastBuyBiz = new FastBuyBiz($sid, $memberId, $dtype);

            $fastBuyBiz->createRule($title, $landId, $rulesInfo, $type, $distributeType, $distributeGroups, $distributeIds);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 删除规则
     * <AUTHOR>
     * @date 2021/7/8
     *
     */
    public function delRule()
    {
        $code = 200;
        $msg = '';
        $data = [];

        try {
            $sid      = $this->_sid;
            $memberId = $this->_memberId;
            $dtype    = $this->_dtype;

            //请求参数
            $ruleId = I('post.rule_id', 0, 'intval');
            if (!$ruleId) {
                throw new \Exception('参数错误', 203);
            }

            $fastBuyBiz = new FastBuyBiz($sid, $memberId, $dtype);

            $fastBuyBiz->deleteRule($ruleId);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);

    }

    /**
     * 编辑规则
     * <AUTHOR>
     * @date 2021/7/8
     *
     */
    public function editRule()
    {
        $code = 200;
        $msg = '';
        $data = [];

        try {
            $sid      = $this->_sid;
            $memberId = $this->_memberId;
            $dtype    = $this->_dtype;

            //请求参数
            $title     = $this->_checkTitle(); //规则名称
            $landId    = $this->_checkLandId();//景区id
            $rulesInfo = $this->_checkRules(); //规则信息
            $type      = $this->_checkType(); //下单方式 1:下单预定 2：报团预定

            $ruleId                = I('post.id', 0, 'intval');
            $distributeGroups      = I('post.distribute_group', '', 'strval'); //分销组
            $closeDistributeGroups = I('post.close_distribute_group', '', 'strval'); //分销组
            $distributeIds         = I('post.distribute_ids', '', 'strval'); //分销商
            $closeDistributeIds    = I('post.close_distribute_ids', '', 'strval'); //分销商
            if (!$ruleId) {
                throw new \Exception('参数错误', 203);
            }

            $fastBuyBiz = new FastBuyBiz($sid, $memberId, $dtype);

            $fastBuyBiz->editRuleById($ruleId, $title, $landId, $rulesInfo, $type, $distributeGroups, $distributeIds, $closeDistributeGroups,
                $closeDistributeIds);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /***************************** 快速购票 - 验证 ****************************************/

    /**
     * 验证规则特殊票种
     * <AUTHOR>
     * @date 2021/7/7
     *
     */
    public function checkTicketRule()
    {
        $sid      = $this->_sid;
        $memberId = $this->_memberId;
        $ticketId = I('post.ticket_id', 0, 'intval');
        if (!$ticketId) {
            $this->apiReturn(203, [], '参数错误');
        }

        $fastBuyBiz = new FastBuyBiz($sid, $memberId);

        $result = $fastBuyBiz->checkSpecial($ticketId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 标题验证
     * <AUTHOR>
     * @date 2021/7/7
     *
     * @return mixed
     */
    private function _checkTitle()
    {
        $title = I('post.title', '', 'strval');
        if (empty($title)) {
            $this->apiReturn(203, [], '名称参数错误');
        }
        //长度计算
        $max = 10;
        if (mb_strlen($title) > $max) {
            $this->apiReturn(203, [], "名称长度不能超过 $max 个字");
        }

        return $title;
    }

    /**
     * 产品验证
     * <AUTHOR>
     * @date 2021/7/7
     *
     * @return mixed
     */
    private function _checkLandId()
    {
        $landId = I('post.land_id', '', 'strval');
        if (empty($landId)) {
            $this->apiReturn(203, [], '产品参数错误');
        }

        return $landId;
    }

    /**
     * 规则验证
     * <AUTHOR>
     * @date 2021/7/7
     *
     * @return mixed
     */
    private function _checkRules()
    {
        $rulesInfo = I('post.rules_info');
        if (empty($rulesInfo)) {
            $this->apiReturn(203, [], '规则错误');
        }

        if (!is_array($rulesInfo)) {
            $this->apiReturn(203, [], '规则参数错误');
        }

        return $rulesInfo;
    }

    /**
     * 规则验证
     * <AUTHOR>
     * @date 2021/7/7
     *
     * @return mixed
     */
    private function _checkType()
    {
        $type = I('post.type');
        if (empty($type)) {
            $this->apiReturn(203, [], '规则错误');
        }

        return $type;
    }

    /**
     * 导入解析匹配票类
     * <AUTHOR>
     * @date 2021/7/10
     *
     * @return bool
     */
    public function importFile()
    {
        $sid      = $this->_sid;
        $memberId = $this->_memberId;

        $ruleId       = I('post.rule_id', 0, 'intval'); //规则id
        $distributeId = I('post.distribute_id', 0, 'intval'); //分销规则id
        $palyTime     = I('post.paly_time', '', 'strval'); //游玩时间
        $timeShare    = I('post.time_share', '', 'strval'); //分时时间/具体时间
        $file         = $_FILES['file']; //导入游客信息文件

        if (!$ruleId || empty($palyTime) || empty($timeShare)) {
            $this->apiReturn(203, [], '参数错误');
        }

        //验证文件
        if (!$file) {
            return $this->apiReturn(203, [], '上传文件异常，请重新上传');
        }

        $fastBuyBiz = new FastBuyBiz($sid, $memberId);

        $result = $fastBuyBiz->importFileMateTicket($distributeId, $ruleId, $palyTime, $timeShare, $file);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 去下单前置判断接口
     * <AUTHOR>
     * @date 2021/7/13
     *
     */
    public function submitToOrder()
    {
        $sid      = $this->_sid;
        $memberId = $this->_memberId;

        $distributeIid = I('post.distribute_id', 0, 'intval'); //分销id
        $ruleId        = I('post.rule_id', 0, 'intval'); //规则id
        $palyTime      = I('post.paly_time', '', 'strval'); //游玩时间
        $timeShare     = I('post.time_share', '', 'strval'); //分时时间/具体时间
        $tourists      = I('post.tourists', '', 'strval'); //游客信息json串

        if (!$ruleId || empty($palyTime) || empty($timeShare) || empty($tourists)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $fastBuyBiz = new FastBuyBiz($sid, $memberId);

        $result = $fastBuyBiz->submitToOrderCache($distributeIid, $ruleId, $palyTime, $timeShare, $tourists);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取下单信息
     * <AUTHOR>
     * @date 2021/7/13
     *
     */
    public function getOrderTicketsAndTourists()
    {
        $sid      = $this->_sid;
        $memberId = $this->_memberId;

        $token = I('post.token', '', 'strval'); //下单信息缓存标识
        if (empty($token)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $fastBuyBiz = new FastBuyBiz($sid, $memberId);

        $result = $fastBuyBiz->getOrderTicketsAndTouristsByToken($token);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /***************************** 快速购票 - 查询 ****************************************/

    /**
     * 购票规则列表
     * <AUTHOR>
     * @date 2021/7/8
     *
     */
    public function ruleConfig()
    {
        $code = 200;
        $msg = '';
        $data = [];

        try {
            $sid      = $this->_sid;
            $memberId = $this->_memberId;
            $dtype    = $this->_dtype;

            //请求参数
            $title  = I('post.title', '', 'strval');
            $landId = I('post.land_id', 0, 'intval');
            $type   = I('post.type', 0, 'intval');  //下单类型1:下单预定 2：报团预定 （不传代表获取全部）
            $page   = I('post.page', 1, 'intval');
            $size   = I('post.size', 10, 'intval');
            $fid   = I('post.fid', 0, 'intval');//分销商id

            $fastBuyBiz = new FastBuyBiz($sid, $memberId, $dtype);

            $data = $fastBuyBiz->ruleConfigList($fid, $title, $landId, $type, $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 根据规则id获取规则信息
     * <AUTHOR>
     * @date 2021/7/8
     *
     */
    public function getRuleInfo()
    {
        $sid = $this->_sid;

        //请求参数
        $ruleId = I('post.rule_id', 0, 'intval');
        $landId = I('post.land_id', 0, 'intval');
        if (!$ruleId && !$landId) {
            $this->apiReturn(203, [], '参数错误');
        }

        $fastBuyBiz = new FastBuyBiz($sid);

        $result = $fastBuyBiz->getRuleInfoById($ruleId, $landId, $sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 搜索配置
     * <AUTHOR>
     * @date 2021/7/9
     *
     */
    public function getRuleConfig()
    {
        $sid = $this->_sid;

        //请求参数
        $title    = I('post.title', '', 'strval');
        $landName = I('post.land_name', '', 'strval');
        $aName    = I('post.a_name', '', 'strval');
        $page     = I('post.page', 1, 'intval');
        $size     = I('post.size', 10, 'intval');
        $landName = trim($landName, ' ');
        $aName    = trim($aName, ' ');

        $fastBuyBiz = new FastBuyBiz($sid);

        //当前如果是供应商，需要支持本账号
        $selfId = false;
        if ($this->_dtype === 0 && (($aName != '' && strstr($this->_dname, $aName) || $landName != ''))) {
            $selfId = true;
        }
        if ($this->_dtype === 0 && empty($aName) && empty($landName)) {
            $selfId = true;
        }

        if (empty($title) && empty($landName) && empty($aName)) {
            $this->apiReturn(203, [], '搜索条件不能为空');
        }

        $result = $fastBuyBiz->getRuleConfig($title, $landName, $aName, $selfId, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);

    }

    /**
     * 身份号解析
     * <AUTHOR>
     * @date 2021/7/28
     *
     */
    public function getIdcardInfo()
    {
        $code = 200;
        $msg = '';
        $data = [];

        try {
            $sid      = $this->_sid;
            $memberId = $this->_memberId;

            //请求参数
            $idcard       = I('post.idcard', '', 'strval'); //身份号解析， 多个逗号隔开
            $ruleId       = I('post.rule_id', 0, 'intval'); //规则id
            $palyTime     = I('post.paly_time', '', 'strval'); //游玩时间
            $timeShare    = I('post.time_share', '', 'strval'); //分时时间/具体时间
            $distributeId = I('post.distribute_id', 0, 'intval'); //规则id

            if (empty($idcard) || !$ruleId || empty($palyTime) || empty($timeShare)) {
                throw new \Exception('参数错误', 203);
            }

            $fastBuyBiz = new FastBuyBiz($sid, $memberId);

            $data = $fastBuyBiz->getIdcardInfo($distributeId, $idcard, $ruleId, $palyTime, $timeShare);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /***************************** 快速购票 - excel ****************************************/

    /**
     * Excel 模板下载
     * <AUTHOR>
     * @date 2021/7/8
     *
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    public function downloadExcel()
    {
        $title       = ['姓名(必填)', '身份证(必填)', '手机号'];
        $subject     = '游客身份证信息模板';
        $objPHPExcel = new \PHPExcel();
        $col         = [
            'A',
            'B',
            'C',
            'D',
        ];
        //重命名表
        $objPHPExcel->getActiveSheet()->setTitle($subject);
        //获取第一行
        $titleRow = [];
        foreach ($col as $l) {
            $titleRow[] = $l . '1';
        }

        //第一行赋值 抬头
        for ($a = 0; $a < count($title); $a++) {
            //超出处理了
            if (!isset($titleRow[$a])) {
                continue;
            }
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue($titleRow[$a], $title[$a]);
        }

        //设置为文本格式
        $objPHPExcel->setActiveSheetIndex(0)->getStyle('B')->getNumberFormat()->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_TEXT);

        $objPHPExcel->setActiveSheetIndex(0);
        //输出表格
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename=' . $subject . '-' . date('Ymd') . '.xlsx');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $objWriter->save('php://output');
    }

    /**
     * 获取供应商快捷下单配置列表
     *
     * @return array
     * <AUTHOR>
     * @date 2021/9/3
     *
     *
     */
    public function getSupplyFastOrderRuleList()
    {
        $title      = I('post.title', '', 'strval');
        $landId     = I('post.land_id', 0, 'intval');
        $applyId    = I('post.apply_id', 0, 'intval');
        $type       = I('post.type', 0, 'intval'); //获取类型 0：全部 1：下单预定  2：报团预定
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');
        $fastBuyBiz = new FastBuyBiz($this->_sid);
        $result     = $fastBuyBiz->getSupplyFastOrderRuleList($title, $landId, $applyId, $type, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 修改分销商向下推送状态
     *
     * @return array
     * <AUTHOR>
     * @date 2021/9/18
     *
     *
     */
    public function changeDistributeState()
    {
        $id               = I('post.distribute_id', 0, "intval"); //分销配置id
        $distributeStatus = I('post.distribute_status', 0, "intval"); //推送状态 1：开启 2：关闭
        if (empty($id) || empty($distributeStatus)) {
            $this->apiReturn(203, [], "参数错误");
        }
        /**
         * @var $redis \Library\Cache\CacheRedis
         */
        $redis      = Cache::getInstance('redis');
        $key        = "buy_rule_dis:" . $id;
        if (!$redis->lock($key, 1, 10)) {
            $this->apiReturn(204, [], "请勿频繁操作！");
        }
        $fastBuyBiz = new FastBuyBiz($this->_sid);
        $result     = $fastBuyBiz->changeDistributeState($id, $distributeStatus);
        $redis->hdel($key, '');

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 修改分销商向下分销的分组信息
     *
     * @return array
     * <AUTHOR>
     * @date 2021/9/18
     *
     *
     */
    public function changeDistributeLowerLevel()
    {
        $id                    = I('post.distribute_id', 0, "intval"); //分销配置id
        $distributeGroups      = I('post.distribute_group', '', 'strval'); //新增分销组
        $closeDistributeGroups = I('post.close_distribute_group', '', 'strval'); //关闭分销组
        $distributeIds         = I('post.distribute_ids', '', 'strval'); //新增分销商
        $closeDistributeIds    = I('post.close_distribute_ids', '', 'strval'); //关闭分销商
        if (empty($id) || (empty($distributeGroups) && empty($closeDistributeGroups) && empty($distributeIds) && empty($closeDistributeIds))) {
            $this->apiReturn(203, [], "参数错误");
        }
        $fastBuyBiz = new FastBuyBiz($this->_sid);
        $result     = $fastBuyBiz->changeDistributeLowerLevel($id, $distributeGroups, $closeDistributeGroups, $distributeIds, $closeDistributeIds);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取分销商向下分销的分组信息
     *
     * @return array
     * <AUTHOR>
     * @date 2021/9/18
     *
     *
     */
    public function getDistributeLowerLevelInfo()
    {
        $id = I('post.distribute_id', 0, "intval"); //分销配置id
        if (empty($id)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $fastBuyBiz = new FastBuyBiz($this->_sid);
        $result     = $fastBuyBiz->getDistributeLowerLevelInfo($id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}