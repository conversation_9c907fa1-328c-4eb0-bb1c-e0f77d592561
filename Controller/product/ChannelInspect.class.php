<?php

/**
 * 渠道规则相关操作
 * <AUTHOR>
 * @date 2019-09-11
 *
 * @deprecated  2022/04/30 渠道属性都没有用户使用，直接下线掉去
 */

namespace Controller\product;

use Library\Controller;
use Business\JavaApi\Product\ChannelTicketAttrDict;
use Business\Product\ChannelInspect as ChannelInspectBusiness;
use Model\Product\PriceGroup as GroupModel;

class ChannelInspect extends Controller
{
    // 登陆信息
    private $_loginInfo;

    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo('ajax');
        parent::__construct();
    }

    /**
     * 渠道门票属性目录字典表
     * <AUTHOR>
     * @date 2019-09-11
     */
    public function attrDict()
    {
        //渠道规则特性已经下线F
        $this->apiReturn(400, '', '渠道规则特性已经下线');

        // 景区类型
        $pType = I('get.p_type', '0', 'strval');

        $biz = new ChannelTicketAttrDict();

        $res = $biz->query($pType);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取分销商价格分组列表 , 只返回已分组用户数据
     * 二期需要精确到组里用户， 所以新写一个方法
     *
     * <AUTHOR>
     * @date 2019-09-12
     */
    public function getGrpList()
    {
        //渠道规则特性已经下线F
        $this->apiReturn(400, '', '渠道规则特性已经下线');

        $name = I('get.name', '', 'strval');

        $loginInfoArr = $this->getLoginInfo();
        $sid          = $loginInfoArr['sid'];

        $page        = I('post.page', 1, 'intval');
        $pageSize    = I('post.size', 300, 'intval');
        $relationBiz = new \Business\Member\MemberEvoluteRelation();
        $result      = $relationBiz->queryGroupNameListBySidAndGroupName($sid, $name, $pageSize, $page);

        if ($result['code'] != 200 || empty($result['data']['list'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $groupInfo = [];
        foreach ($result['data']['list'] as $group) {
            $groupInfo[] = [
                'id'   => $group['group_id'],
                'name' => $group['group_name'],
            ];
        }

        //加载分组模型
        //$grpModel = new GroupModel();
        //分组列表
        //$groupInfo = $grpModel->getGroupBySidAndKeyword($sid, $name, 'id, name');

        $this->apiReturn(200, $groupInfo, 'success');
    }

    /**
     * 添加渠道售检规则
     * <AUTHOR>
     * @date 2019-09-12
     */
    public function createOrUpdate()
    {
        //渠道规则特性已经下线F
        $this->apiReturn(400, '', '渠道规则特性已经下线');


        // 规则id
        $ruleId = I('post.rule_id', 0, 'intval');
        // 规则名称
        $name = I('post.name', '', 'strval');
        // 启用时间
        $enableDate = I('post.enable_date', '', 'strval');
        // 失效时间
        $invalidDate = I('post.invalid_date', '', 'strval');
        // 渠道id 列表, string 逗号分割
        $channelList = I('post.channel_list', []);
        // 状态, 1开，0关
        $status = I('post.status', 0, 'intval');
        // 客户类型列表 string 逗号分割
        $customerTypeList = I('post.customer_type_list', []);
        // 分销商分组列表
        $groupIdList = I('post.group_list', []);
        // 门票景区JSon数组{lid:xxx,ticket:[{tid:'xxx'}]}
        $landTicketIdList = I('land_ticket_list', []);
        // 分销商成员列表
        $groupMemberIdList = I('group_member_id_list', []);
        // 属性列表
        $attrList = I('post.attr_list', []);

        $biz = new ChannelInspectBusiness();
        $res = $biz->createOrUpdate($this->_loginInfo['sid'], $ruleId, $name, $enableDate, $invalidDate,
            $this->_loginInfo['memberID'],
            $status, $channelList, $customerTypeList, $landTicketIdList, $groupIdList, $groupMemberIdList, $attrList);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取售捡规则详情
     * <AUTHOR>
     * @date 2019-09-12
     */
    public function getDetail()
    {
        //渠道规则特性已经下线F
        $this->apiReturn(400, '', '渠道规则特性已经下线');


        $ruleId = I('get.rule_id', 0, 'intval');
        if (empty($ruleId)) {
            $this->apiReturn(400, '', '渠道id不能为空');
        }

        $biz = new ChannelInspectBusiness();
        $res = $biz->detail($this->_loginInfo['sid'], $ruleId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取渠道售检规则列表
     * <AUTHOR>
     * @date 2019-09-12
     */
    public function getList()
    {
        //渠道规则特性已经下线F
        $this->apiReturn(400, '', '渠道规则特性已经下线');


        // 规则名称
        $name = I('get.name', '', 'strval');
        // 状态
        $status = I('get.status', -1, 'intval');
        // 规则包含门票id
        $ticketId = I('get.ticket_id', 0, 'intval');
        // 渠道
        $channel = I('get.channel', 0, 'intval');
        // 客户类型
        $customerType = I('get.customer_type', 0, 'intval');
        // 景区ID
        $landId = I('get.land_id', 0, 'intval');
        // 分组id
        $groupId = I('get.group_id', 0, 'intval');
        // 分组用户ID
        $groupMemberId = I('get.group_member_id', 0, 'intval');
        // 页数
        $pageNum = I('get.page_num', 0, 'intval');
        // 页码
        $pageSize = I('get.page_size', 0, 'intval');

        $biz = new ChannelInspectBusiness();
        $res = $biz->detailList($this->_loginInfo['sid'], $name, $status, $ticketId, $channel,
            $customerType, $landId, $groupId, $groupMemberId, $pageNum, $pageSize);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取订单对应的票属性(包含)渠道规则
     * <AUTHOR>
     * @date 2019-09-30
     */
    public function getOrderAttr()
    {
        //渠道规则特性已经下线F
        $this->apiReturn(400, '', '渠道规则特性已经下线');

        // 订单号
        $ordernum = I('get.ordernum', '', 'strval');

        if (empty($ordernum)) {
            $this->apiReturn(400, '', '订单号不能为空');
        }

        $biz = new ChannelInspectBusiness();
        $res = $biz->orderAttrDetail($this->_loginInfo['sid'], $ordernum);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取渠道能配置的票类列表
     * <AUTHOR>
     * @date 2019-09-30
     */
    public function getSaleList()
    {
        //渠道规则特性已经下线F
        $this->apiReturn(400, '', '渠道规则特性已经下线');


        // 客户类型 , 1直销，2分销商
        $customerType = I('get.customer_type', '', 'strval');
        // 渠道
        $channel = I('get.channel', '', 'strval');
        // 景区ID
        $lid = I('get.lid', 0, 'intval');

        $biz = new ChannelInspectBusiness();
        $res = $biz->saleList($customerType, $channel, $this->_loginInfo['sid'], $lid);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 关闭或开启规则
     * <AUTHOR>
     * @date 2019-10-11
     */
    public function openOrCloseInspectRule()
    {
        //渠道规则特性已经下线F
        $this->apiReturn(400, '', '渠道规则特性已经下线');

        // 规则Id
        $ruleId = I('post.rule_id', 0, 'intval');
        // 开启或者关闭
        $status = I('post.status', -1, 'intval');

        $biz = new ChannelInspectBusiness();
        $res = $biz->openOrCloseInspectRule($this->_loginInfo['sid'], $ruleId, $status);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取景区上架的门票信息接口
     * <AUTHOR>
     * @date 2019-10-16
     */
    public function getTicketList()
    {
        // 景区id
        $landId = I('get.land_id', 0, 'intval');
        // 状态
        $status = I('get.status', -1, 'intval');

        if (empty($landId)) {
            $this->apiReturn(400, '', '景区不能为空');
        }

        $biz = new ChannelInspectBusiness();
        $res = $biz->ticketList($landId, $status);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

}