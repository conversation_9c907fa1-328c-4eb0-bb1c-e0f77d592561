<?php
/**
 * 业态相关控制器
 * <AUTHOR>
 * @date 2020/3/6 0006
 */

namespace Controller\product;

use Business\JavaApi\Product\ManagementLand;
use Library\Controller;
use Business\Product\Management;
use Model\Product\Area as AreaModel;
use Business\JavaApi\Member\MemberQuery;
use Business\JavaApi\Product\Ticket as TicketJavaBusiness;
use Business\JavaApi\TicketApi;

class LandManager extends Controller
{
    private $memberInfo = [];

    // 登陆用户id
    private $mid;

    public function __construct()
    {
        parent::__construct();
        $this->memberInfo = $this->getLoginInfo();
        $this->mid        = $this->memberInfo['memberID'];
    }


    /**
     * 获取景区业态信息
     * <AUTHOR>
     * @date 2020/2/12 0012
     *
     * @return array
     */
    public function getLandManagerInfo()
    {
        $id = I('post.id', '', 'intval');

        if (empty($id)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $managementBus     = new Management();
        $governmentInfoRes = $managementBus->getLandManagerInfoBus($id);

        if (!$governmentInfoRes || $governmentInfoRes['code'] != 200) {
            $this->apiReturn($governmentInfoRes['code'], $governmentInfoRes['data'], $governmentInfoRes['msg']);
        }
        $governmentInfo                  = $governmentInfoRes['data'];
        if (!empty($governmentInfo['lngLatPos'])) {
            $lngLat = explode(',', $governmentInfo['lngLatPos']);
        }

        $lng = $lat = 0;
        if (isset($lngLat[0])) {
            $lng = number_format($lngLat[0], 8);
        }
        if (isset($lngLat[1])) {
            $lat = number_format($lngLat[1], 8);
        }
        $governmentInfo['lng']           = $lng;
        $governmentInfo['lat']           = $lat;
        $governmentInfo['city']          = $governmentInfo['municipality'];
        $governmentInfo['apply_name']    = $governmentInfo['applyName'];
        $governmentInfo['apply_account'] = $governmentInfo['applyAccount'];
        $governmentInfo['apply_id']      = $governmentInfo['applyDid'];
        $governmentInfo['create_time']   = $governmentInfo['createTime'];
        $governmentInfo['update_time']   = $governmentInfo['updateTime'];

        //地区信息
        $areaModel    = new AreaModel();
        $areaSeaModel = new AreaModel(1);

        $proCity = [$governmentInfo['province'], $governmentInfo['municipality'], '0'];
        if ($governmentInfo['oversea'] == 0) {
            $areaNameArr = $areaModel->getInfoByCodeFromCache($proCity);
        } else {
            $areaNameArr = $areaSeaModel->getInfoByOverSeaCodeFromCache($proCity);
        }

        $governmentInfo['city_name']     = $areaNameArr[1] ?? '';
        $governmentInfo['province_name'] = $areaNameArr[0] ?? '';

        unset(
            $governmentInfo['createTime'],
            $governmentInfo['updateTime'],
            $governmentInfo['municipality'],
            $governmentInfo['applyAccount'],
            $governmentInfo['applyName'],
            $governmentInfo['applyDid'],
            $governmentInfo['lngLatPos']
        );

        $this->apiReturn(200, $governmentInfo, '成功');
    }


    /**
     * 获取景区业态列表
     * <AUTHOR>
     * @date 2020/2/12 0012
     *
     * @param  int  $page  页码
     * @param  int  $pageSize  每页显示条数
     * @param  string  $title  名称
     * @param  string  $id  ID
     * @param  int  $jtype  级别
     * @param  int  $oversea  境内外 0:境内 1：境外
     * @param  int  $state  关联状态 0：未关联 1：关联
     * @param  int  $province  省份id
     * @param  int  $city  市区id
     *
     * @return array
     */
    public function getLandManagerList()
    {
        $page     = I('request.page', 1, 'intval');
        $pageSize = I('request.pageSize', 10, 'intval');
        $title    = I('request.title', '', 'string');
        $id       = I('request.id', '', 'string');
        $jtype    = I('request.jtype', '', 'string');
        $oversea  = I('request.oversea', '', 'string');
        $state    = I('request.state', '', 'string');
        $province = I('request.province', '', 'string');
        $city     = I('request.city', '', 'string');

        $managementBus = new Management();

        $whereParamArr = [
            'title'        => $title,
            'id'           => $id,
            'jtype'        => $jtype,
            'oversea'      => $oversea,
            'state'        => $state,
            'province'     => $province,
            'municipality' => $city,
            'mtype'        => 'A',
        ];

        $resourceArr = $managementBus->getLandManagerListBus($page, $pageSize, $whereParamArr);

        if ($resourceArr['code'] != 200) {
            $this->apiReturn($resourceArr['code'], $resourceArr['data'], $resourceArr['msg']);
        }

        if (empty($resourceArr['data']['list'])) {
            $this->apiReturn(200, ['list' => [], 'totalNum' => '0'], '成功');
        }

        $areaModel    = new AreaModel();
        $areaSeaModel = new AreaModel(1);

        $list = [];
        foreach ($resourceArr['data']['list'] as $key => $value) {
            $proCity = [$value['province'], $value['municipality'], '0'];
            if ($value['oversea'] == 0) {
                $areaNameArr = $areaModel->getInfoByCodeFromCache($proCity);
            } else {
                $areaNameArr = $areaSeaModel->getInfoByOverSeaCodeFromCache($proCity);
            }
            $lngLat                      = explode(',', $value['lngLatPos']);
            $list[$key]['id']            = $value['id'];
            $list[$key]['title']         = $value['title'];
            $list[$key]['jtype']         = $value['jtype'];
            $list[$key]['address']       = $value['address'];
            $list[$key]['topic']         = $value['topic'];
            $list[$key]['state']         = $value['state'];
            $list[$key]['create_time']   = $value['createTime'];
            $list[$key]['update_time']   = $value['updateTime'];
            $list[$key]['province_name'] = $areaNameArr[0] ?? '';
            $list[$key]['province']      = $value['province'];
            $list[$key]['city_name']     = $areaNameArr[1] ?? '';
            $list[$key]['city']          = $value['municipality'];
            $list[$key]['lng']           = $lngLat[0];
            $list[$key]['lat']           = $lngLat[1];
            $list[$key]['apply_name']    = $value['applyName'];
            $list[$key]['apply_account'] = $value['applyAccount'];
            $list[$key]['mtype']         = $value['mtype'];
            $list[$key]['apply_id']      = $value['applyDid'];
            $list[$key]['oversea']       = $value['oversea'];
        }

        $this->apiReturn(200, ['list' => $list, 'total' => $resourceArr['data']['total']], 'success');
    }

    /**
     * 关联/变更景区业态经营主体
     * <AUTHOR>
     * @date 2020/2/12 0012
     *
     * @return array
     */
    public function relevanceLandManager()
    {
        $applyId = I('request.apply_id', '', 'string');
        $id      = I('request.id', '', 'string');

        if (empty($applyId) || empty($id)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        $managementBus = new Management();
        $res           = $managementBus->relevanceLandManagerBus($id, $applyId, $this->mid);
        if (!$res) {
            $this->apiReturn(400, [], '内部接口错误!');
        }

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }

        $this->apiReturn(200, [], '成功');

    }

    /**
     * 取消主体关联
     * <AUTHOR>
     * @date 2020/2/17 0017
     *
     * @return array
     */
    public function cancelRelevanceLandManager()
    {
        $mtId = I('request.id', '', 'string');

        if (empty($mtId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $managementBus = new Management();
        $res           = $managementBus->cancelRelevanceLandManagerBus($mtId, $this->mid);
        if (!$res) {
            $this->apiReturn(400, [], '内部接口错误!');
        }

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }

        $this->apiReturn(200, [], '成功');
    }

    /**
     * 获取主体变更记录列表
     * <AUTHOR>
     * @date 2020/2/12 0012
     *
     * @return array
     */
    public function getSubjectRecordList()
    {
        $id = I('request.id', '', 'string');
        if (empty($id)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $managementBus = new Management();
        $res           = $managementBus->getSubjectRecordListBus($id);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }

        if (empty($res['data'])) {
            $this->apiReturn(200, ['list' => []], '成功');
        }

        $list = [];
        foreach ($res['data'] as $key => $value) {
            $list[$key]['old_name']         = $value['origName'];
            $list[$key]['old_account']      = $value['origAccount'];
            $list[$key]['new_name']         = $value['newName'];
            $list[$key]['new_account']      = $value['newAccount'];
            $list[$key]['operator_name']    = $value['operatorName'];
            $list[$key]['operator_account'] = $value['operatorAccount'];
            $list[$key]['create_time']      = date('Y-m-d H:i:s', $value['createTime']);
            $list[$key]['id']               = $value['id'];
            $list[$key]['operator_id']      = $value['operatorId'];
            $list[$key]['mt_id']            = $value['mtId'];
            $list[$key]['mtype']            = $value['mtype'];
            $list[$key]['orig_value']       = $value['origValue'];
            $list[$key]['new_value']        = $value['newValue'];
        }

        $this->apiReturn(200, ['list' => $list], 'success');
    }

    /**
     * 景区业态关联资源
     * <AUTHOR>
     * @date 2020/2/13 0013
     *
     * @return array
     */
    public function relevanceLandResource()
    {
        $landId = I('request.land_id', '', 'string');
        $mtId   = I('request.mt_id', '', 'string');
        $mtype  = I('request.mtype', '', 'string');

        if (empty($landId) || empty($mtId) || empty($mtype)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (!in_array($mtype, ['A'])) {
            $this->apiReturn(203, [], '参数错误');
        }

        $managementBus = new Management();
        $res           = $managementBus->relevanceLandResourceBus($mtId, $landId, $this->mid);

        if (!$res) {
            $this->apiReturn(400, [], '内部接口错误!');
        }

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }

        $this->apiReturn(200, [], '成功');
    }


    /**
     * 获取出售中业态资源关联列表
     * <AUTHOR>
     * @date 2020/2/12 0012
     *
     * @return array
     */
    public function getManagementResourceList()
    {
        $mtId     = I('request.mt_id', '', 'string');//业态ID
        $mtype    = I('request.mtype', '', 'string');//业态类型
        $page     = I('request.page', 1, 'intval');
        $pageSize = I('request.pageSize', 10, 'intval');

        if (empty($mtId) || empty($mtype)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $managementBus = new Management();
        $areaModel     = new AreaModel();
        $areaSeaModel  = new AreaModel(1);
        $res           = $managementBus->getManagementResourceListBus($mtId, $page, $pageSize);
        if (!$res || $res['code'] != 200 || empty($res['data']['list'])) {
            $this->apiReturn(200, ['list' => [], 'total' => $res['data']['total']], '成功');
        }

        foreach ($res['data']['list'] as &$value) {
            $areaCodeArr = explode('|', $value['area']);
            if ($value['oversea'] == 0) {
                $areaNameArr = $areaModel->getInfoByCodeFromCache($areaCodeArr);
            } else {
                $areaNameArr = $areaSeaModel->getInfoByOverSeaCodeFromCache($areaCodeArr);
            }
            $value['province_name'] = $areaNameArr[0] ?? '';
            $value['province']      = $areaCodeArr[0];
            $value['city_name']     = $areaNameArr[1] ?? '';
            $value['city']          = $areaCodeArr[1];
            $value['supplierName']  = $value['applyName'];
            $value['id']            = $value['lid'];
            $value['imgpath']       = $value['imgPath'];
        }

        $this->apiReturn(200, ['list' => $res['data']['list'], 'total' => $res['data']['total']], '成功');
    }

    /**
     * 通过手机号/账号搜索供应商
     * <AUTHOR>
     * @date 2020/2/14 0014
     *
     * @return array
     */
    public function getApplyInfoByAccountAndMobile()
    {
        $search = I('request.search', '', 'string');//搜索

        if (empty($search)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $account = $mobile = '';
        //判断是否手机号
        if (ismobile($search)) {
            $mobile = $search;
        } else {
            $account = $search;
        }

        $memberQueryModel = new MemberQuery();
        $res              = $memberQueryModel->queryMemberInfoByAccountOrMobileOrName($account, $mobile);

        if ($res['code'] != 200 || empty($res['data'])) {
            $this->apiReturn(200, [], '成功');
        }

        //获取公司名称
        $resEx = $memberQueryModel->queryMemberByMemberId($res['data'][0]['id'], true);

        if ($resEx['code'] != 200 || empty($resEx['data'])) {
            $this->apiReturn(200, [], '成功');
        }

        $resultData = [
            'id'            => $res['data'][0]['id'] ?? '',
            'dname'         => $res['data'][0]['dname'] ?? '',
            'apply_account' => $res['data'][0]['account'] ?? '',
            'com_name'      => $resEx['data']['memberExtInfo']['com_name'] ?? '',
        ];

        $this->apiReturn(200, $resultData, '成功');
    }

    /**
     * 通过景区ID获取票列表
     * <AUTHOR>
     * @date 2020/2/14 0014
     *
     * @return array
     */
    public function getTickeListsByLandId()
    {
        $landId = I('request.land_id', '', 'string');//业态ID
        $page   = I('post.page', 1);
        //$size           = I('post.size',5);
        $size = 5;
        if (empty($landId)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        $biz = new TicketJavaBusiness();
        $res = $biz->queryTicketListByLandId($landId);

        if ($res['code'] != 200) {
            $this->apiReturn(205, [], '失败');
        }

        $ticketIdArray = array_column($res['data'], 'id');
        $start         = ($page - 1) * $size;
        $cutTicketIds  = array_splice($ticketIdArray, $start, $size);
        $strTicketIds  = join(',', $cutTicketIds);

        //直接调取java获取票接口
        $result = TicketApi::getSupplyMoreTickets($strTicketIds);

        if (empty($result)) {
            $this->apiReturn(204, [], "获取失败");
        }

        foreach ($result as &$t) {
            if ($t["costPrice"] == -1 && $t["retailPrice"] == -1) {
                $t["status"] = -1;
            }

            if ($t["costPrice"] == -1) {         //成本价
                $t["costPrice"] = "--";
            }
            if ($t["counterPrice"] == -1) {      //门市价
                $t["counterPrice"] = "--";
            }
            if ($t["retailPrice"] == -1) {
                $t["retailPrice"] = "--";
            }
            if ($t["windowPrice"] == -1) {
                $t["windowPrice"] = "--";
            }
            unset($t);
        }

        $this->apiReturn(200, ['list' => $result, 'total' => count($res['data'])], "获取成功");
    }


    /**
     * 删除景区业态关联资源
     * <AUTHOR>
     * @date 2020/2/13 0013
     *
     * @return array
     */
    public function deleteRelevanceLandResource()
    {
        $landId = I('request.land_id', '', 'string');
        $mtId   = I('request.mt_id', '', 'string');
        $mtype  = I('request.mtype', '', 'string');

        if (empty($landId) || empty($mtId) || empty($mtype)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $managementBus = new Management();
        $res           = $managementBus->deleteRelevanceLandResourceBus($mtId, $landId, $this->mid);
        if (!$res) {
            $this->apiReturn(400, [], '内部接口错误!');
        }

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        }

        $this->apiReturn(200, [], '成功');
    }

    
    /**
     * 通过景区ID获取票列表
     * <AUTHOR>
     * @date    2021/04/26
     *
     * @return array
     */
    public function getLandResource()
    {
        //景区id
        $landIdStr = I('get.land_id_list', '', 'strval');
        //景区类型
        $mType = I('get.m_type/s', 'A');

        if (empty($landIdStr)) {
            $this->apiReturn(400, [], '参数错误');
        }

        $landIdList = explode(',', $landIdStr);
        if (empty($landIdList)) {
            $this->apiReturn(400,  [], '参数错误');
        }

        $res = (new Management())->getLandResourceByLids($landIdList, $mType);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }



    /**
     * 更新业态关联资源
     * <AUTHOR>
     * @date    2021/04/26
     *
     * @return array
     */
    public function updateResourceByIdAndLandId()
    {
        //原业态ID
        $orgmtId = I('post.orgmtId/i', 0);
        //新业态ID
        $mtId = I('post.mtId/i', 0);
        //关联景区ID
        $landId = I('post.landId/i', 0);

        if (!$orgmtId || !$mtId || !$landId) {
            $this->apiReturn(400, [], '参数错误');
        }

        $res = (new Management())->updateResourceByIdAndLandId($orgmtId, $mtId, $landId, $this->mid);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }


}
