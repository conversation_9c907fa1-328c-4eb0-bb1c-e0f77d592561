<?php

namespace Controller\Product;

use Bean\Request\Pay\PayNotifyRequestBean;
use Business\CardSolution\Order;
use Business\Member\Member;
use Business\MemberLogin\MemberLoginHelper;
use Business\Pay\UnifiedPayment;
use Library\Constants\AllInOneCardConst;
use Library\Constants\CardType;
use Library\Controller;
use \Business\JsonRpcApi\ScenicLocalService\AllInOneCard as AllInOneCardJsonRpc;
use Library\SimpleExcel;
use Library\Tools\Vcode;
use Business\Authority\AuthLogic as AuthLogicBiz;

class AllInOneCard extends Controller
{
    private $_sid;
    private $_memberId;
    private $_subMerchantId;

    public function __construct()
    {
        $memberInfo = $this->getLoginInfo();
        if (empty($memberInfo)) {
            $this->apiReturn(0, [], "未登录");
        }
        $this->_sid           = $memberInfo['sid'];
        $this->_memberId      = $memberInfo['memberID'];
        $this->_subMerchantId = 0;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $this->_subMerchantId = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $this->_memberId      = $this->_sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }
    }

    //查询纬度和纬度对应的名称
    private $_needItem = [
        'date_time'       => '日期',
        'card_no'         => '实体卡号',
        'ordernum'        => '订单号',
        'mobile'          => '手机号',
        'operation_type'  => '操作类型',
        'operate_id'      => '操作员',
        'pay_type'        => '支付方式',
        'site_id'         => '站点',
        'channel'         => '渠道',
        'sub_merchant_id' => '子商户名称',
    ];

    private $_targetItem = [
        'physical_principal' => '实体卡本金消费',
        'physical_bonus'     => '实体卡奖励金消费',
        'recharge_amount'    => '充值金额',
        'award_amount'       => '赠送金额',
        'repair_amount'      => '补卡金额',
        'refund_amount'      => '退款金额',
        'land_name'          => '景区',
        'ticket_name'        => '门票',
        'change_money'       => '修改/取消订单金额(当前页)',
        'sub_merchant_name'  => '子商户名称',
    ];

    /**
     * 获取开卡记录列表
     *
     * @date 2022/01/01
     * @auther yangjianhui
     * @return array
     */
    public function getActiveList()
    {
        $beginDate       = I('begin_date', '', "strval");
        $endDate         = I('end_date', '', "strval");
        $status          = I('status');
        $opId            = I('op_id', 0, "intval");
        $physicsNo       = I('physics_no');
        $mobile          = I('mobile', '', "strval");
        $cardNo          = I('card_no');
        $pageNum         = I('page_num', 1, 'intval');
        $pageSize        = I('page_size', 10, 'intval');
        $excel           = I('excel', 0, 'intval');  //是否是excel导出 0 : 否 1：是
        $statusArr       = empty($status) ? [] : explode(',', $status);
        $allInOneJsonRpc = new AllInOneCardJsonRpc();
        $result          = $allInOneJsonRpc->getActiveList($this->_sid, $beginDate, $endDate, $statusArr, $opId, $physicsNo,
            $mobile, $cardNo, $excel, $pageNum, $pageSize);
        if ($excel) {
            $this->_exportActiveList($result['data']);
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 挂失发送验证码
     *
     * @date   2022/106
     * <AUTHOR>
     *
     * @param
     *
     * @return string
     */
    public function sendCardCode()
    {
        $mobile = I('mobile');
        if (!ismobile($mobile)) {
            $this->apiReturn(205, [], '请输入正确的手机号');
        }

        $data = [
            '{1}'  => '一卡通挂失',
            'code' => '{2}',
        ];

        $sendRes = Vcode::sendVcode($mobile, 'one_park_loss', 'one_park_loss', $data, 6, false, 300, 60);
        if ($sendRes['code'] == 200) {
            $this->apiReturn(200, [], '验证码发送成功');
        } else {
            $this->apiReturn(207, [], '验证码发送失败');
        }
    }

    /**
     * 修改卡状态
     *
     * @date 2022/01/04
     * @auther yangjianhui
     * @return array
     */
    public function setCardStatus()
    {
        $id     = I('post.id', 0, "intval");
        $status = I('post.status', 0);
        $mobile = I('post.mobile', '', 'strval');
        $code   = I('post.validCode', '', 'strval');
        if (empty($id) || !isset(AllInOneCardConst::STATUS_MSG[$status])) {
            $this->apiReturn(203, [], "参数有误");
        }
        if ($status == AllInOneCardConst::STATUS_LOSS && (!$mobile || !$code)) {
            $this->apiReturn(203, [], "挂失信息有误");
        }
        //挂失先检测验证码
        if ($status == AllInOneCardConst::STATUS_LOSS) {
            $res  = Vcode::verifyVcode($mobile, $code, 'one_park_loss');
            //登录验证码不正确
            if ($res['code'] != 200) {
                $this->apiReturn($res['code'], [], $res['msg']);
            }
        }

        $allInOneJsonRpc = new AllInOneCardJsonRpc();
        $result          = $allInOneJsonRpc->setCardStatus($this->_sid, $this->_memberId, $id, $status, $mobile);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取某个激活记录消费记录明细
     *
     * @date 2022/01/02
     * @auther yangjianhui
     * @return array
     */
    public function getCardTradeDetailList()
    {
        $id       = I('post.id', 0, "intval");
        $type     = I('post.type', 1, "intval"); //获取记录类型 1：园区卡 2：年卡
        $pageNum  = I('post.pageNum', 1, "intval");
        $pageSize = I('post.pageSize', 10, "intval");
        if (empty($id)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $allInOneJsonRpc = new AllInOneCardJsonRpc();
        $result          = $allInOneJsonRpc->getCardTradeDetailList($this->_sid, $id, $type, $pageNum, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取开卡记录列表
     *
     * @date 2022/01/01
     * @auther yangjianhui
     * @return array
     */
    public function getTransactionDetail()
    {
        //当前页码
        $page = I('page', 0, 'intval');
        //每页条数
        $size = I('size', 0, 'intval');
        //卡标识
        $identify = I('identify');
        //卡标识类型
        $type = I('type');
        //查询开始日期
        $dateBegin = I('date_begin');
        //查询结束日期
        $dateEnd = I('date_end');
        // 输出类型 0:正常输出 1，导出 2,打印
        $outputType = I('output_type');
        //支付方式
        $ptype = I('ptypes', '-1', 'strval'); //1:现金 2：支付宝 3：微信
        //交易类型
        $dtype = I('dtypes', '-1', 'strval'); //28：一卡通充值 75：充值赠送  41：押金充值 47：工本费 0：刷卡消费 29：一卡通提现
        if (!$identify || !$type || !$dateBegin || !$dateEnd) {
            $this->apiReturn(204, [], '参数错误');
        }
        $dtypeArr        = strlen($dtype) == 0 ? [] : explode(',', $dtype);
        $ptypeArr        = strlen($ptype) == 0 ? [] : explode(',', $ptype);
        $allInOneJsonRpc = new AllInOneCardJsonRpc();
        $result          = $allInOneJsonRpc->getTransactionDetail($this->_sid, $identify, $type, $dateBegin, $dateEnd, $page,
            $size, CardType::NEW_PARK_CARD, $outputType, $dtypeArr, $ptypeArr);
        if ($outputType) {
            $this->_exportParkCard($result['data']);
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取年卡套餐消费列表
     *
     * @date 2022/01/05
     * @auther yangjianhui
     * @return array
     */
    public function getPackageTradeList()
    {
        //当前页码
        $page = I('page', 0, 'intval');
        //每页条数
        $size = I('size', 0, 'intval');
        //卡标识
        $identify = I('identify');
        //卡标识类型 查询类型  1 实体卡 2手机号 3:物理卡号
        $type = I('type');
        //查询开始日期
        $dateBegin = I('date_begin');
        //查询结束日期
        $dateEnd = I('date_end');
        // 输出类型 0:正常输出 1，导出 2,打印
        $outputType = I('output_type');
        //支付方式
        $ptype = I('ptypes', '', 'strval'); //1:现金 2：支付宝 3：微信
        //交易类型
        $dtype = I('dtypes', '', 'strval'); //交易类型：0=售卡，1=续费，2=补卡，3=被取消,  4=激活，5=验证（特权支付）
        $dtypeArr = strlen($dtype) == 0 ? [] : explode(',', $dtype);
        $ptypeArr = strlen($ptype) == 0 ? [] : explode(',', $ptype);
        if (!strtotime($dateBegin) || !strtotime($dateEnd)) {
            $this->apiReturn(203, [], "时间参数格式有误");
        }
        if (strtotime($dateEnd) - strtotime($dateBegin) > 3600 * 24 * 90) {
            $this->apiReturn(203, [], "时间跨度不能超过90天");
        }
        $allInOneJsonRpc = new AllInOneCardJsonRpc();
        $result          = $allInOneJsonRpc->getAnnualTradeList($this->_sid, $identify, $type, $dateBegin, $dateEnd,
            $outputType, $dtypeArr, $ptypeArr, $page, $size);
        if ($outputType) {
            $this->_exportAnnualTradeList($result['data']);
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    /**
     * 获取汇总数据
     *
     * @date 2022/01/06
     * @auther yangjianhui
     * @return array
     */
    public function getSummaryInfo()
    {
        $begin    = I('date_begin', '');
        $end      = I('date_end', '');
        $opId     = I('operate_id', 0);
        $showOper = I('show_operate', 0);  //是否展示操作员  0不展示 1展示
        //是否导出
        $excel = I('export_excel', 0, 'intval');
        if (!strtotime($begin) || !strtotime($end)) {
            $this->apiReturn(204, [], '日期格式错误');
        }
        $isSuper         = $this->_memberId == $this->_sid ? 1 : 0;
        $opId            = $isSuper != 1 && empty($opId) ? $opId = $this->_memberId : $opId;
        $allInOneJsonRpc = new AllInOneCardJsonRpc();
        $result          = $allInOneJsonRpc->getSummaryInfo($this->_sid, $begin, $end, $opId, $showOper);
        if ($excel) {
            $this->_exportOneCardStatisticsReport($result['data'], $showOper);
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 导出数据
     *
     * @param array $data 数据
     * @param int $showOper 是否展员工
     *
     * @date 2022/01/10
     * @auther yangjianhui
     * @return array
     */
    private function _exportOneCardStatisticsReport($data, $showOper)
    {
        $filename = date('Ymd') . '新一卡通交易报表';

        $sum = [
            'total'        => $data['total_money'] / 100,
            'total_cash'   => $data['total_cash'] / 100,
            'total_wx'     => $data['total_wx'] / 100,
            'total_alipay' => $data['total_alipay'] / 100,
            'total_pos'    => $data['total_pos'] / 100,
            //'total_reward' => $data['total_reward'] / 100,
        ];
        $list  = $data['list'];
        if (!$showOper) {
            //不展示营业员
            $title = [
                //'total_operators',
                'total',
                'total_cash',
                'total_wx',
                'total_alipay',
                'total_pos',
                //'total_reward',
            ];
        } else {
            //展示营业员
            $title = [
                'total_operators',
                'total',
                'total_cash',
                'total_wx',
                'total_alipay',
                'total_pos',
                //'total_reward',
            ];

        }
        $excel[0] = [
            $filename,
        ];

        //从第二行开始
        $i = 2;
        foreach ((array)$list as $key => $item) {
            if (!$showOper) {
                $excel[2] = ['类型', '笔数', '现金', '微信', '支付宝', 'POS支付'];
                $i++;
                $excel[$i][] = $item['name'];
                $excel[$i][] = $item['total'];
                $excel[$i][] = $item['cash'] / 100;
                $excel[$i][] = $item['wx'] / 100;
                $excel[$i][] = $item['alipay'] / 100;
                $excel[$i][] = $item['pos'] / 100;
                //$excel[$i][] = $item['reward'] / 100;
                $i++;
            } else {
                $excel[$i][] = '合计';
                $excel[$i][] = '';
                $excel[$i][] = $item['total_money'] / 100;
                $excel[$i][] = $item['total_cash'] / 100;
                $excel[$i][] = $item['total_wx'] / 100;
                $excel[$i][] = $item['total_alipay'] / 100;
                $excel[$i][] = $item['total_pos'] / 100;
                //$excel[$i][] = $item['total_reward'] / 100;
                $i++;
                $excel[$i] = ['营业员', '类型', '笔数', '现金', '微信', '支付宝', 'POS支付'];
                $i++;
                foreach ($item['details'] as $type => $value) {
                    $excel[$i][] = $item['user_name'];
                    $excel[$i][] = $value['name'];
                    $excel[$i][] = $value['total'];
                    $excel[$i][] = $value['cash'] / 100;
                    $excel[$i][] = $value['wx'] / 100;
                    $excel[$i][] = $value['alipay'] / 100;
                    $excel[$i][] = $value['pos'] / 100;
                    //$excel[$i][] = $value['reward'] / 100;
                    $i++;
                }
            }
        }

        $total[] = '总计';
        foreach ($title as $temp) {
            if ($temp == 'total_operators') {
                $total[] = '';
            }
            if (isset($sum[$temp])) {
                array_push($total, $sum[$temp]);
            }
        }

        $excel[] = $total;
        $xls     = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }

    /**
     * 导出销售信息列表
     *
     * @param array $data 处理数据
     *
     * @date 2022/01/02
     * @auther yangjianhui
     * @return array
     */
    private function _exportActiveList($data)
    {
        $filename = date('Ymd') . '新一卡通管理列表';
        $xls     = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($data);
        $xls->generateXML($filename);
        exit;
    }

    /**
     * 导出一卡通明细
     *
     * @param array $data 导出的数据
     *
     * @date 2022/01/10
     * @auther yangjianhui
     * @return array
     */
    private function _exportParkCard($data)
    {
        $filename = date('Ymd') . '新一卡通消费明细';
        $xls     = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($data);
        $xls->generateXML($filename);
        exit;
    }


    /**
     * 导出年卡信息列表
     *
     * @param array $data 处理数据
     *
     * @date 2022/01/02
     * @auther yangjianhui
     * @return array
     */
    private function _exportAnnualTradeList($data)
    {
        $filename = date('Ymd') . '套餐消费明细';

        $xls     = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($data);
        $xls->generateXML($filename);
        exit;
    }
	
	// 供应商获取一卡通配置
	// http://my.12301.local/r/product_AllInOneCard/getPreferences
	public function getPreferences(){
		$applyDid = $this->_sid;
		$allInOneJsonRpc = new AllInOneCardJsonRpc();
		$result = $allInOneJsonRpc->getPreferences($applyDid);
		$this->apiReturn($result['code'], $result['data'], $result['msg']);
	}
	
	// // http://my.12301.local/r/product_AllInOneCard/editCardPreferences
	public function editCardPreferences(){
		$cardName = I('card_name', '');
		//每页条数
		$selfServiceRefund = I('self_service_refund', 0, 'intval');
		$protocalTitle = I('protocal_title', '');
		// $protocalContent = I('protocal_content', '');
		$protocalContent = $_POST['protocal_content'] ?? '';
		
		$sid = $this->_sid;
		$opId = $this->_memberId;
		$allInOneJsonRpc = new AllInOneCardJsonRpc();
		$preferencesParams = [
			'card_name' => $cardName,
			'protocal_title' => $protocalTitle,
			'protocal_content' => $protocalContent,
			'self_service_refund' => $selfServiceRefund,
		];
		$result = $allInOneJsonRpc->editCardPreferences($sid,$preferencesParams,$opId);
		$this->apiReturn($result['code'], $result['data'], $result['msg']);
	}
	
	public function editProtocal(){
		$title = I('title', '');
		$content = I('content', '');
		$sid = $this->_sid;
		$opId = $this->_memberId;
		$allInOneJsonRpc = new AllInOneCardJsonRpc();
		$result = $allInOneJsonRpc->editProtocal($sid,$title,$content,$opId);
		$this->apiReturn($result['code'], $result['data'], $result['msg']);
	}

    /**
     * 新增或者修改比例支付配置
     *
     *
     * @date 2022/11/28
     * @auther yangjianhui
     * @return array
     */
	public function addOrUpdatePercentPayConfig()
    {
        $id              = I('post.id', '', 'intval');
        $payConfigType   = I('post.pay_config_type', '', 'intval');
        $deductRule      = I('post.deduct_rule', '');
        $smartPay        = I('post.smart_pay', 0, 'intval');
        $limitTid        = I('post.limit_tid', '', 'strval');
        $balanceLimitTid = I('post.balance_limit_tid', '', 'strval');
        $awardUseType    = I('post.award_use_type', 1, 'intval');
        $balanceUseType  = I('post.balance_use_type', 1, 'intval');

        $limitTid        = html_entity_decode($limitTid);
        $balanceLimitTid = html_entity_decode($balanceLimitTid);
        $allInOneJsonRpc = new AllInOneCardJsonRpc();
        $deductRule      = html_entity_decode($deductRule);
        if ($balanceUseType == 2 && (!empty(array_diff($limitTid, $balanceLimitTid)) || $awardUseType == 1)) {
            $this->apiReturn(203, [], "配置有误: 奖励金可支付的产品必须可以用本金支付");
        }

        $result          = $allInOneJsonRpc->addOrUpdatePercentPayConfig($this->_sid, $id, $payConfigType, $deductRule,
            $smartPay, $limitTid, $awardUseType, $balanceLimitTid, $balanceUseType);
        
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    /**
     * 获取比例支付配置
     *
     *
     * @date 2022/11/28
     * @auther yangjianhui
     * @return array
     */
    public function getPercentPayConfig()
    {
        $allInOneJsonRpc = new AllInOneCardJsonRpc();
        $result          = $allInOneJsonRpc->getPercentPayConfig($this->_sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * @date 2023/05/06
     * @auther yangjianhui
     * @return array
     */
    public function getSummaryOrderList()
    {
        //开始时间
        $beginTime = I('begin_time', '', 'strval,trim');
        //结束时间
        $endTime = I('end_time', '', 'strval,trim');
        //页数
        $page = I('page', 1, 'intval');
        //每页数量
        $size = I('size', 10, 'intval');
        //模板id
        $configId = I('search_config_id', 0, 'intval');
        //是否导出
        $excel = I('export_excel', 0, 'intval');
        //1 按日  2 按月
        $dateType = I('date_type', 1, 'intval');
        //操作员id
        $operateId = I('operate_id', 0, 'intval,trim');
        //卡号
        $cardNo = I('card_no', '', 'strval,trim');
        //手机号
        $mobile = I('mobile', '', 'strval,trim');
        //支付方式
        $payType = I('pay_type', 0, 'intval');
        //交易类型
        $operationType = I('operation_type', 0, 'intval');
        //订单号
        $ordernum      = I('ordernum', '', 'strval');
        $fid           = I('fid', 0, 'intval');
        $siteId        = I('site_id', 0, 'intval');
        $channel       = I('channel', 0, 'intval');
        $subMerchantId = I('sub_merchant_id', 0, 'intval'); //子商户id

        $code = 200;
        $data = [];
        $msg  = '';

        try {
            if (!$beginTime || !$endTime || !$configId) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '缺少必要参数');
            }

            $templateModel = new \Model\Report\Template();
            $template      = $templateModel->getTemplateById('', $configId);

            if (empty($template)) {
                throw new \Exception("模板配置未知");
            }

            //统计纬度
            $item = json_decode($template['item'], true);
            if (empty($item) || !is_array($item)) {
                throw new \Exception("配置有误, 统计纬度不能为空");
            }

            //可以被统计的纬度 清除掉没用的 整理纬度对应的汉字描述
            foreach ($item as $key => $value) {
                if (!array_key_exists($value, $this->_needItem)) {
                    unset($item[$key]);
                }

                $titleArr[] = $this->_needItem[$value];
            }

            $item = array_values($item);
            //报表名称
            $name = "{$beginTime}~{$endTime} {$template['name']}";
            //统计指标
            $target = json_decode($template['target'], true);
            if (empty($target) || !is_array($target)) {
                $target = [];
            }
            $finance = $this->_checkFinance($this->_memberId);
            $sid     = $this->_sid == $this->_memberId ? 1 : 0;
            //员工的数据权限
            $dateScope = (new AuthLogicBiz())->memberDataResource($this->_sid, $this->_memberId, 6, 'one_card_order_statis');
            $isSuper   = 0;
            if ($finance || $sid || $dateScope) {
                $isSuper = 1;
            }
            $subMerchantId     = empty($subMerchantId) ? $this->_subMerchantId : $subMerchantId;
            $allOneCardJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\AllInOneCard();
            $res               = $allOneCardJsonRpc->getSummaryOrderList($beginTime, $endTime, $this->_sid, $this->_memberId,
                $cardNo, $mobile, $dateType, $payType, $operationType, $ordernum,  $siteId, $channel, $isSuper, $item,
                $operateId, $excel, $page, $size, $subMerchantId);
            //列表
            $data['list'] = $res['data']['list'] ?? [];
            //总和
            $data['sum'] = $res['data']['sum'] ?? [];
            //最细纬度下的总条数
            $data['total'] = $res['data']['total'] ?? 0;
            //纬度名称
            $data['title'] = $titleArr;
            //报表名称
            $data['name'] = $name;
            //统计指标
            $data['target'] = $target;
            //统计方式 按月/按日
            $data['date_type'] = $dateType;
        } catch (\Exception $e) {
            $this->apiReturn($e->getCode(), [], $e->getMessage());
        }

        if ($excel && $code == 200) {
            $this->_cardConsumeDayExportData($data);
        } else {

            $this->apiReturn($code, $data, $res['msg']);
        }
    }

    /**
     * 查询终端收款订单列表信息
     *
     * @date 2023/11/22
     * @auther yangjianhui
     * @return array
     */
    public function searchTerminalCollectOrder()
    {
        $orderNum  = I('post.order_num', '', 'strval');
        $mobile    = I('post.mobile', '', 'strval');
        $physicsNo = I('post.physics_no', '', 'strval');
        $page      = I('post.page', 1, 'intval');
        $size      = I('post.size', 10, 'intval');
        if (empty($orderNum) && empty($mobile) && empty($physicsNo)) {
            $this->apiReturn(203, [], "参数有误");
        }
        $sid           = $this->_sid;
        $allInOneBz    = new AllInOneCardJsonRpc();
        $result        = $allInOneBz->searchTerminalCollectOrder($sid, $orderNum, $physicsNo, $mobile, $this->_subMerchantId, $this->_memberId, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 查询终端收款订单详情信息
     *
     * @date 2023/11/22
     * @auther yangjianhui
     * @return array
     */
    public function getTerminalCollectOrderDetail()
    {
        $orderNum  = I('post.order_num', '', 'strval');
        if (empty($orderNum)) {
            $this->apiReturn(203, [], "参数有误");
        }
        $sid            = $this->_sid;
        $allInOneBz = new AllInOneCardJsonRpc();
        $result     = $allInOneBz->getTerminalCollectOrderDetail($sid, $orderNum, $this->_subMerchantId);


        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 终端收款退款
     *
     * @date 2023/11/21
     * @auther yangjianhui
     * @return array
     */
    public function refundTerminalCollectMoney()
    {
        $orderNum    = I('post.order_num', '', 'strval');
        $refundMoney = I('post.refund_money', 0, 'intval');
        $remark      = I('post.remark', '', 'strval');
        if (empty($orderNum) || empty($refundMoney)) {
            $this->apiReturn(203, [], "参数有误");
        }
        $opId = $this->_memberId;
        if (!empty($this->_subMerchantId)) {
            $opId = $this->_subMerchantId;
        }
        $allInOneBz = new AllInOneCardJsonRpc();
        $result     = $allInOneBz->refundTerminalCollectMoney($orderNum, $this->_sid, $opId, $refundMoney, $this->_subMerchantId, $remark);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取汇总数据
     *
     * @date 2023/11/24
     * @auther yangjianhui
     * @return array
     */
    public function getTerminalCollectSummaryInfo()
    {
        $beginTime     = I('begin_time', '', 'strval');
        $endTime       = I('end_time', '', 'strval');
        $deviceKey     = I('device_key', '', 'strval');
        $opId          = I('op_id', '', 'strval');
        $payType       = I('pay_type', '', 'strval');
        $excel         = I('excel', 0, 'intval');
        $page          = I('page', 1, 'intval');
        $size          = I('page_size', 10, 'intval');
        $subMerchantId = I('sub_merchant_id', 0, 'intval');
        if (empty($beginTime) || empty($endTime)) {
            $this->apiReturn(203, [], "参数有误");
        }
        if (strtotime($endTime) - strtotime($beginTime) > 92 * 86400) {
            $this->apiReturn(203, [], "查询时间不能大于92天");
        }
        $memberInfo = $this->getLoginInfo();
        if ($memberInfo['dtype'] == 6) {
            $opId = $this->_memberId;
        }
        if ($subMerchantId) {
            $this->_subMerchantId = $subMerchantId;
        }
        $allInOneBz = new AllInOneCardJsonRpc();
        $result     = $allInOneBz->getTerminalCollectSummaryInfo($this->_sid, $beginTime, $endTime, $opId, $deviceKey, $payType, $page, $size, $excel,
            $this->_subMerchantId);
        if ($excel) {
            $this->_exportTerminalCollectSummaryInfo($result['data']['list'], $beginTime, $endTime);
        }


        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 导出单条汇总数据
     *
     * @date 2023/11/24
     * @auther yangjianhui
     * @return array
     */
    public function exportTerminalCollectSummaryInfo()
    {
        $beginTime     = I('begin_time', '', 'strval');
        $endTime       = I('end_time', '', 'strval');
        $deviceKey     = I('device_key', '', 'strval');
        $opId          = I('op_id', '', 'strval');
        $payType       = I('pay_type', '', 'strval');
        $subMerchantId = I('sub_merchant_id', 0, 'intval');
        if (empty($beginTime) || empty($endTime) || empty($opId) || empty($payType)) {
            $this->apiReturn(203, [], "参数有误");
        }
        if (strtotime($endTime) - strtotime($beginTime) > 92 * 86400) {
            $this->apiReturn(203, [], "时间不能大于92天");
        }
        $memberInfo = $this->getLoginInfo();
        if ($memberInfo['dtype'] == 6) {
            $opId = $this->_memberId;
        }
        $subMerchantId = empty($subMerchantId) ? $this->_subMerchantId : $subMerchantId;
        $allInOneBz    = new AllInOneCardJsonRpc();
        $result        = $allInOneBz->exportTerminalCollectSummaryInfo($this->_sid, $beginTime, $endTime, $opId,
            $deviceKey, $payType, 0, 1, 10, $subMerchantId);

        if (empty($result['data'])) {
            $this->apiReturn(203, "暂无可导出数据");
        }
        $excel[] = [
            '支付订单号',
            '退款订单号',
            '第三方流水号',
            '支付方式',
            '支付时间',
            '取消时间',
            '一卡通本金收款',
            '一卡通奖励金收款',
            '收款金额',
            '一卡通本金退款',
            '一卡通奖励金退款',
            '退款金额',
            '操作人',
            '商户名称',
            '手持机特征码',
            '订单备注',
            '代操作人',
        ];
        foreach ($result['data'] as $value) {
            $excel[] = [
                $value['ordernum'] . "\t",
                $value['refund_order_num'] . "\t",
                $value['trade_no']. "\t",
                $value['pay_type_name'],
                $value['create_time'],
                $value['refund_time'],
                $value['balance_pay_money'] / 100,
                $value['award_pay_money'] / 100,
                $value['money'] / 100,
                $value['balance_refund_money'] / 100,
                $value['award_refund_money'] / 100,
                $value['refund_money'] / 100,
                $value['op_name'],
                empty($value['sub_merchant_name']) ? $value['dname'] : $value['sub_merchant_name'],
                $value['device_key'],
                $value['remark'],
                $value['replace_op_name'],
            ];
        }
        $filename = "{$beginTime}~{$endTime} 终端收款交易详细信息";
        $xls     = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;

    }


    private function _exportTerminalCollectSummaryInfo($data, $beginTime, $endTime)
    {
        $excel[] = [
            '商户',
            '操作人',
            '手持机特征码',
            '支付方式',
            '收款笔数',
            '一卡通本金收款',
            '一卡通奖励金收款',
            '收款金额',
            '退款笔数',
            '一卡通本金退款',
            '一卡通奖励金退款',
            '退款金额',
            '实收金额',
        ];
        foreach ($data as $value) {
            $excel[] = [
                empty($value['sub_merchant_name']) ? $value['dname'] : $value['sub_merchant_name'],
                $value['op_name'] . "/" . $value['account'],
                $value['device_key'],
                $value['pay_type_name'],
                $value['collect_num'],
                $value['balance_money'],
                $value['award_money'],
                $value['collect_amount'],
                $value['refund_collect_num'],
                $value['refund_balance_money'],
                $value['refund_award_money'],
                $value['refund_collect_amount'],
                $value['received_money'],
            ];
        }
        $filename = "{$beginTime}~{$endTime} 终端收款交易详细信息";
        $xls     = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }

    /**
     * 判断是不是财务
     *
     * @param  string  $mid  操作员
     *
     * @return bool
     */
    public function _checkFinance($mid)
    {
        $MemberBus  = new Member();
        $memberInfo = $MemberBus->getInfo($mid, true);
        if ($memberInfo["position"] === '2') {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 计时订单报表导出
     *
     * <AUTHOR>
     * @date   2019-12-10
     *
     * @params  array       $data       数据
     *
     * @return
     */
    private function _cardConsumeDayExportData($data)
    {
        $filename = date('Ymd') . '新一卡通订单报表';

        //数据列表
        $list = $data['list'];
        //报表名称
        $name = $data['name'];
        //纬度
        $title = $data['title'];
        //总和
        $sum = $data['sum'];
        //统计指标
        $target = $data['target'];

        if (empty($target)) {
            $target = [];
        }

        $excel[0] = [
            $name,
        ];

        // $excel[1] 包含Excel 表头列['日期','实体卡号',,,,,]
        //配置的纬度
        foreach ((array)$title as $value) {
            $excel[1][] = $value;
        }

        //配置的统计指标
        if (!empty($target)) {
            foreach ((array)$target as $key => $value) {
                if (isset($this->_targetItem[$value])) {
                    $excel[1][] = $this->_targetItem[$value];
                }

            }
        }

        //从第二行开始
        $i = 2;
        foreach ((array)$list as $valueOne) {
            switch (count($title)) {
                case 1:
                    $excel[$i][] = $valueOne['title'];
                    foreach ($target as $item) {
                        $excel[$i][] = $valueOne['list'][$item];
                    }
                    $i++;
                    break;
                case 2:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        $excel[$i][] = $valueOne['title'];
                        $excel[$i][] = $valueTwo['title'];
                        foreach ($target as $item) {
                            $excel[$i][] = $valueTwo['list'][$item];
                        }
                        $i++;
                    }
                    break;
                case 3:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            $excel[$i][] = $valueOne['title'];
                            $excel[$i][] = $valueTwo['title'];
                            $excel[$i][] = $valueThree['title'];
                            foreach ($target as $item) {
                                $excel[$i][] = $valueThree['list'][$item];
                            }
                            $i++;
                        }
                    }
                    break;
                case 4:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                $excel[$i][] = $valueOne['title'];
                                $excel[$i][] = $valueTwo['title'];
                                $excel[$i][] = $valueThree['title'];
                                $excel[$i][] = $valueFour['title'];
                                foreach ($target as $item) {
                                    $excel[$i][] = $valueFour['list'][$item];
                                }
                                $i++;
                            }
                        }
                    }
                    break;
                case 5:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    $excel[$i][] = $valueOne['title'];
                                    $excel[$i][] = $valueTwo['title'];
                                    $excel[$i][] = $valueThree['title'];
                                    $excel[$i][] = $valueFour['title'];
                                    $excel[$i][] = $valueFive['title'];
                                    foreach ($target as $item) {
                                        $excel[$i][] = $valueFive['list'][$item];
                                    }
                                    $i++;
                                }
                            }
                        }
                    }
                    break;
                case 6:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        $excel[$i][] = $valueOne['title'];
                                        $excel[$i][] = $valueTwo['title'];
                                        $excel[$i][] = $valueThree['title'];
                                        $excel[$i][] = $valueFour['title'];
                                        $excel[$i][] = $valueFive['title'];
                                        $excel[$i][] = $valueSix['title'];
                                        foreach ($target as $item) {
                                            $excel[$i][] = $valueSix['list'][$item];
                                        }

                                        $i++;
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 7:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            $excel[$i][] = $valueOne['title'];
                                            $excel[$i][] = $valueTwo['title'];
                                            $excel[$i][] = $valueThree['title'];
                                            $excel[$i][] = $valueFour['title'];
                                            $excel[$i][] = $valueFive['title'];
                                            $excel[$i][] = $valueSix['title'];
                                            $excel[$i][] = $valueSeven['title'];
                                            foreach ($target as $item) {
                                                $excel[$i][] = $valueSeven['list'][$item];
                                            }
                                            $i++;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 8:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            foreach ($valueSeven['list'] as $keyEight => $valueEight) {
                                                $excel[$i][] = $valueOne['title'];
                                                $excel[$i][] = $valueTwo['title'];
                                                $excel[$i][] = $valueThree['title'];
                                                $excel[$i][] = $valueFour['title'];
                                                $excel[$i][] = $valueFive['title'];
                                                $excel[$i][] = $valueSix['title'];
                                                $excel[$i][] = $valueSeven['title'];
                                                $excel[$i][] = $valueEight['title'];
                                                foreach ($target as $item) {
                                                    $excel[$i][] = $valueEight['list'][$item];
                                                }
                                                $i++;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 9:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            foreach ($valueSeven['list'] as $keyEight => $valueEight) {
                                                foreach ($valueEight['list'] as $keyNine => $valueNine) {
                                                    $excel[$i][] = $valueOne['title'];
                                                    $excel[$i][] = $valueTwo['title'];
                                                    $excel[$i][] = $valueThree['title'];
                                                    $excel[$i][] = $valueFour['title'];
                                                    $excel[$i][] = $valueFive['title'];
                                                    $excel[$i][] = $valueSix['title'];
                                                    $excel[$i][] = $valueSeven['title'];
                                                    $excel[$i][] = $valueEight['title'];
                                                    $excel[$i][] = $valueNine['title'];
                                                    foreach ($target as $item) {
                                                        $excel[$i][] = $valueNine['list'][$item];
                                                    }
                                                    $i++;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 10:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            foreach ($valueSeven['list'] as $keyEight => $valueEight) {
                                                foreach ($valueEight['list'] as $keyNine => $valueNine) {
                                                    foreach ($valueNine['list'] as $keyTen => $valueTen) {
                                                        $excel[$i][] = $valueOne['title'];
                                                        $excel[$i][] = $valueTwo['title'];
                                                        $excel[$i][] = $valueThree['title'];
                                                        $excel[$i][] = $valueFour['title'];
                                                        $excel[$i][] = $valueFive['title'];
                                                        $excel[$i][] = $valueSix['title'];
                                                        $excel[$i][] = $valueSeven['title'];
                                                        $excel[$i][] = $valueEight['title'];
                                                        $excel[$i][] = $valueNine['title'];
                                                        $excel[$i][] = $valueTen['title'];
                                                        foreach ($target as $item) {
                                                            $excel[$i][] = $valueTen['list'][$item];
                                                        }
                                                        $i++;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
                default:
            }
        }

        $total[] = '总计';
        for ($i = 0; $i < count($title) - 1; $i++) {
            $total[] = '';
        }
        foreach ($target as $temp) {
            array_push($total, $sum[$temp]);
        }

        $excel[] = $total;
        $xls     = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }
}
