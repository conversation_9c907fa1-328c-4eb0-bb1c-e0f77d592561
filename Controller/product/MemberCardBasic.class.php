<?php
/**
 * Created by PhpStor<PERSON>.
 * User: lanc
 * Date: 16-12-9
 * Time: 上午11:46
 */

namespace Controller\product;

use Business\JsonRpcApi\MessageService\MessageService;
use Business\NewJavaApi\Member\MemberRegister;
use Library\Container;
use Library\Controller;
use Library\MessageNotify\Platform\FzZwxSms;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Util\EnvUtil;
use Model\Product\MemberCard;
use Model\Member\Member;
use Model\Member\MemberRelationship;
use pft\member\MemberAccount;
use Library\Tools\Helpers;
use Qiniu\Auth;
use Qiniu\Storage\UploadManager;
use Model\Product\Land;
use Library\Tools;
use Library\Business\ReadExcel;
use Model\Wechat\WxMember;
use Business\Member\Member as MemberBiz;
use Business\Member\RegisterAction;
use Library\Constants\MemberConst;
use Business\AppCenter\Package as PackageBiz;

class MemberCardBasic extends Controller
{
    private $_memberId;
    private $_carType;
    private $_loginInfo;

    public function __construct()
    {
        //composer初始化
        \Library\Tools\Helpers::composerAutoload();

        $source = I('post.source', '', 'intval');
        if (!$source) {
            $this->_memberId = $this->isLogin('ajax');
            $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
        }

        $this->_carType = load_config('car_type', 'member_card');
    }

    /**
     * 发送短信验证码
     * @date   2016-12-9
     * <AUTHOR>
     *
     * @param  int $mobile 手机号
     *
     * @return
     */
    public function SendVcode()
    {
        $mobile = I('post.mobile', '', 'intval');
        //$mobile = ***********;
        if (!$mobile) {
            $this->apiReturn(103, [], '请求接口没有传入所有必须参数', true);
        }
        $redis    = \Library\Cache\Cache::getInstance('redis');
        $codeTime = $redis->get("vc:$mobile");
        if ($codeTime) {
            list($code, $time) = explode('|', $codeTime);
            if ($_SERVER['REQUEST_TIME'] - $time < 120) {
                $this->apiReturn(141, [], '短信发送间隔低于120秒', true);
            }
        }

        $code  = genRandomNum(6);
        $cache = $code . '|' . $_SERVER['REQUEST_TIME'];
        pft_log('debug/lee', json_encode(['code'=>$code], JSON_UNESCAPED_UNICODE));
        $redis->set("vc:$mobile", $cache, '', 300); //5分钟内有效
        $messageServiceApi = Container::pull(MessageService::class);
        [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'VCODE', $mobile,
            ['授信预存', $code], 0, '', '授信预存');
        if ($approval) {
            if (!EnvUtil::isRelease()) {
                pft_log('message_service', json_encode(['授信预存', __METHOD__, [$mobile, ['授信预存', $code]], $res], JSON_UNESCAPED_UNICODE));
            }
        } else {
            /** @deprecated 放量结束后删除 */
            $smsLib = SmsFactory::getFactory($mobile);
            $res = $smsLib->veifyCode('授信预存', $code);
            if (!EnvUtil::isRelease()) {
                pft_log('message_service', json_encode(['授信预存.old', __METHOD__, [$mobile, ['授信预存', $code]], $res], JSON_UNESCAPED_UNICODE));
            }
        }
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '发送成功', true);
        } else {
            $this->apiReturn(142, [], '短信发送失败', true);
        }
    }

    /**
     * 校验短信验证码
     * @date   2016-1-5
     * <AUTHOR>
     *
     * @param  string $mobile 手机号
     * @param  int $vcode 验证码
     *
     * @return
     */
    private function ChkVerifyCode($mobile, $vcode)
    {
        $redis = \Library\Cache\Cache::getInstance('redis');
        $code  = $redis->get("vc:$mobile");
        if ($code) {
            $chk_code = array_shift(explode('|', $code));
            if ($vcode == $chk_code) {
                $redis->rm($mobile);

                return true;
            }

            return false;
        }

        return -1;
    }

    /**
     * 会员卡开卡  这里apiReturn统一使用对象格式，适应app的解析
     * @date   2016-12-13
     * <AUTHOR>
     *
     *
     * @return
     */
    public function Register()
    {
        $logTime  = microtime(true);
        $mobile   = I('post.mobile', '', 'strval');                 //注册手机号
        $vcode    = I('post.vcode', '', 'intval');                  //验证码
        $IdCardNo = I('post.id_card_no', '', 'strval');             //身份证
        $dname    = I('post.dname', '', 'strval');                  //姓名

        $province = I('post.province', '', 'intval');               //省
        $city     = I('post.city', '', 'intval');                   //市
        $sex      = I('post.sex', '', 'strval');                   //市

        $address = I('post.address', '', 'strval');               //地址
        $headImg = I('post.headImg', '', 'strval');               //头像
        $carInfo = I('post.carInfo', '', 'strval');               //车辆信息
        $remarks = I('post.remarks', '', 'strval');               //备注

        $phyNo      = I('post.phy_no', '', 'strval');                 //物理卡号
        $cardNo     = I('post.card_no');                              //会员卡号
        $noticeType = I('post.notice_type', '', 'intval');           //通知类型

        $dtype  = I('post.dtype', '', 'intval');                  //账号类型
        $aid    = I('post.invId', '', 'intval');                  //供应商ID
        $ac     = I('post.ac', '', 'strval');                //登录者账号，B端直接拿sid
        $source = I('post.source', '', 'intval');

        $card = I('post.card', '', 'intval');                   //开卡标记
        $fid  = I('post.fid', '', 'intval');                   //编辑时传的会员ID

        $cid = I('post.cid', '', 'intval');                   //编辑时传的会员卡表ID

        /*
        $mobile = ***********;
        $IdCardNo = '530626199307219711';
        $cardNo = '006320';
        $phyNo = 'f0d0a936';
        $dname = '普通会员散客测试';
        $sex = 'M';
        $province = 12;
        $city = 385;
        $address = '测试地址';
        $headImg = 'http://images.12301dev.com/123933/2017-02-16/1487210850429.jpg';
        $carInfo = 0;
        $remarks = '测试账号转换';
        $noticeType = 1;
        $vcode = 148959;
        $card = 1;
        */

        if (filter_var($headImg, FILTER_VALIDATE_URL) === false) {
            //将base64图片上传至Oss上
            $faceResult = Helpers::uploadImage2AliOss('annual_card', $headImg);
            if ($faceResult['code'] == 200) {
                $headImg = $_POST['face_url'] = $faceResult['data']['src'];
            }
        }

        unset($_POST['headImg']);
        pft_log('memberCard/insert_card_data', print_r($_POST, true));
        if (!$card && !$fid) {
            $this->apiReturn(104, [], '错误参数', true);
        }

        if (!$fid && !$vcode) {
            $this->apiReturn(103, [], '请求接口没有传入所有必须参数', true);
        }

        if (!$mobile || !$dname) {
            $this->apiReturn(103, [], '请求接口没有传入所有必须参数', true);
        }

        $dname    = safe_str($dname);
        $dtype    = ($dtype != '') ? $dtype : 1;
        $dtypeArr = [0, 1, 5];

        if (!in_array($dtype, $dtypeArr)) {
            $this->apiReturn(154, [], '注册的分销商类型错误', true);
        }

        if ($aid && $dtype != 1) {
            $this->apiReturn(153, [], '传了邀请人id但是注册的会员类型不是分销商', true);
        }

        //如果是内网测试环境
        if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL', 'TEST'])) {
            //内网不校验验证码
        } else {
            if (isset($vcode) && $vcode != '') {
                $code = $this->ChkVerifyCode($mobile, $vcode);
                if ($code === -1) {
                    $this->apiReturn(151, [], '短信验证码已失效或不存在', true);
                } elseif ($code === false) {
                    $this->apiReturn(152, [], '短信验证码错误', true);
                }
            }
        }

        //B端开卡
        if ($source == '') {
            $aid = $this->_loginInfo['sid'] ?? 0;
        } else {
            $noticeType = 1;    //nfc默认为微信
        }

        if (!$aid || !$phyNo || !$cardNo || !$IdCardNo) {
            $this->apiReturn(156, [], '会员卡信息传入不足', true);
        }

        if (!Tools::idcard_checksum18($IdCardNo)) {
            $this->apiReturn(157, [], '身份证号码无效', true);
        }
        //适应前端的校验，转换为大写
        if (!is_numeric($IdCardNo) && substr($IdCardNo, 17, 1) == 'x') {
            $IdCardNo{17} = 'X';
        }

        if (!ctype_alnum($cardNo)) {
            $this->apiReturn(159, [], '卡号只能是数字和字母', true);
        }

        //16进制的开头如果有要去掉
        if (substr(trim($phyNo), 0, 2) == '0x') {
            $phyNo = substr(trim($phyNo), 2);
        }

        $phy_no_arr = str_split($phyNo, 2);
        krsort($phy_no_arr);
        $phyNo = hexdec(implode('', $phy_no_arr));
        $memberModel     = new \Model\Member\Member();
        $memberCardModel = new \Model\Product\MemberCard();
        $info            = $memberCardModel->getCardInfoByIdCardNo($IdCardNo, 'id,mobile,memberID');
        if (!$card && !empty($info) && $info['id'] != $cid) {
            $this->apiReturn(201, [], '身份证号码已被使用，无法保存！', true);
        }

        if ($info && (($fid && $info['memberID'] != $fid) || $card)) {
            $this->apiReturn(158, [], '身份证号码已被使用', true);
        }

        //外面实体卡号是允许重复的。实体卡号和供应商形成唯一
        $cardInfo = $memberCardModel->getCardInfoByCardNoId($cardNo, false, $aid, 'id,memberID');
        if ($cardInfo && (($fid && $cardInfo['memberID'] != $fid) || $card)) {
            $this->apiReturn(160, [], 'IC卡编号重复', true);
        }
        $cardInfo = $memberCardModel->getInfoByphysics((string)$phyNo);
        if ($cardInfo && (($fid && $cardInfo['memberID'] != $fid) || $card)) {
            $this->apiReturn(161, [], 'IC卡物理卡号重复', true);
        }

        if ($headImg) {
            if ($source && filter_var($headImg, FILTER_VALIDATE_URL) === false) {
                $file       = "shops/{$fid}__touxiang.jpg";
                $uploadInfo = $this->uploadImageToQiniu($file, $headImg);
                if ($uploadInfo['code'] == 200) {
                    $headphoto = $uploadInfo['url'];
                } else {
                    $headphoto = $headImg;
                }
            } else {
                $headphoto = $headImg;
            }
        } else {
            $headphoto = '';
        }

        if ($card) {
            //开卡的时候手机号和供应商形成唯一
            $res = $memberCardModel->getCardInfoByMobile($mobile, $aid);
            if ($res) {
                $this->apiReturn(162, [], '该手机号已经被其他会员卡所使用', true);
            }

            $isRegister = [];
            $memberBiz  = new MemberBiz();
            //客户是否存在
            $customerId = $memberBiz->parseCustomerIdByMobile($mobile);
            if ($customerId) {
                //角色是否存在
                $isRegister = $memberModel->getTheRoleInfo($customerId, MemberConst::ROLE_DISTRIBUTOR,
                    'id,status,account');
            }

            if ($isRegister && in_array($isRegister['status'], [0, 3])) {
                $fid      = $isRegister['id'];
                $where    = ['memberID' => $fid, 'status' => ['IN', [0, 1, 2, 3]]];
                $cardInfo = $memberCardModel->getMemberCardInfo($where);

                if ($cardInfo) {
                    $this->apiReturn(162, [], '该分销商已办理会员卡', true);
                }

                $account  = $isRegister['account'];
                $orderPwd = substr($mobile, 5, 6);
                //产品需求：开卡覆盖原来的信息
                $memberInfo = [
                    'dname'     => $dname,
//                    'password'  => md5(md5($orderPwd)),
//                    'address'   => $address,
                    'headphoto' => $headphoto,
                ];

                //如果是散客账号，得更新为分销商账号
                if (strlen($account) == 11) {
                    $this->apiReturn(162, [], '改手机号已注册其他类型账号', true);
                    // $businessProcess = new \Process\Business\Business();
                    // $account = $businessProcess->_generateAccount(1);
                    // $memberInfo['account'] = $account;
                    // $memberInfo['dtype'] = 1;
                }

                $memberModel->updateMemberInfo($fid, $memberInfo, true);

                $extensionInfo = ['province' => $province, 'city' => $city];

                $other_les = $carInfo;

                if ($remarks) {
                    $other_les .= '|' . $remarks;
                }

                $extensionInfo['other_les'] = $other_les;

                $memberBiz->updateMemberExtInfo($fid, $extensionInfo, true);

                //用户二期 - 信息获取修改
                $memberBiz->updateMemberPassword($fid, $orderPwd);//密码
                $CustomerBus = new \Business\Member\Customer();
                //客户信息
                $CustomerInfo['id_card_no'] = $IdCardNo;
                $CustomerInfo['sex']        = $sex;
                $CustomerInfo['address']    = $address;
                $CustomerBus->updateCustomerInfoByMemberId($fid, $CustomerInfo);

                $javaApi = new \Business\NewJavaApi\Member\MemberRelation();
                $res     = $javaApi->createSupplyShip($aid, $fid, $this->_loginInfo['memberID']);
                pft_log('memberCard/register_move_group', json_encode([$aid, $fid, $this->_loginInfo['memberID'], $res]));

            } else {
                //获取客户id
                $customerId = $memberBiz->parseCustomerIdByMobile($mobile);

                $other_les = $carInfo;
                if ($remarks) {
                    $other_les .= '|' . $remarks;
                }
                //
                //$request = [
                //    'name'        => $dname,
                //    'address'     => $address,
                //    'avatar'      => $headphoto,
                //    'type'        => MemberConst::ROLE_DISTRIBUTOR,
                //    'status'      => MemberConst::STATUS_NORMAL,
                //    'identifier'  => $mobile,
                //    'passwd'      => substr($mobile, 5, 6),
                //    'customer_id'  => $customerId,
                //    'inviter_id'  => $aid,
                //    'province'    => $province,
                //    'city'        => $city,
                //    'sex'         => $sex,
                //    'other_les'   => $other_les,
                //    'id_card'     => $IdCardNo,
                //    'info_source' => MemberConst::INFO_MOBILE,
                //    'page_source' => MemberConst::PAGE_PLATFORM,
                //];
                //
                //$registerAction = new RegisterAction();
                //$regRes         = $registerAction->register((object)$request);

                $request = [
                    'address'           => $address,
                    'avatar'            => $headphoto,
                    'city'              => $city,
                    'customerId'        => $customerId,
                    'distributorName'   => $dname,
                    'distributorMobile' => $mobile,
                    'groupId'           => 0,
                    'idCard'            => $IdCardNo,
                    'operatorId'        => $this->_loginInfo['memberID'],
                    'otherLes'          => $other_les,
                    'passwd'            => substr($mobile, 5, 6),
                    'province'          => $province,
                    'sex'               => $sex == "M" ? 1 : 2,
                    'supplierId'        => $aid,
                ];
                $registerAction = new MemberRegister();
                $regRes         = $registerAction->registerSanyaDistributor($request);

                if ($regRes['code'] != 200) {
                    $this->apiReturn(168, [], '注册失败，请稍后重试', true);
                } else {
                    //$fid     = $regRes['data']['member_id'];
                    $fid     = $regRes['data'][0]['memberId'];
                    $account = $regRes['data'][0]['account'];

                    $where    = ['memberID' => $fid, 'status' => ['IN', [0, 1, 2, 3]]];
                    $cardInfo = $memberCardModel->getMemberCardInfo($where);
                    if ($cardInfo) {
                        $this->apiReturn(162, [], '该分销商已办理会员卡', true);
                    }

                    if ($headImg) {
                        if ($source && filter_var($headImg, FILTER_VALIDATE_URL) === false) {
                            $file       = "shops/{$fid}__touxiang.jpg";
                            $uploadInfo = $this->uploadImageToQiniu($file, $headImg);
                            if ($uploadInfo['code'] == 200) {
                                $data['headphoto'] = $uploadInfo['url'];
                            }
                        } else {
                            $data = ['headphoto' => $headImg];
                        }
                        //重新给用户头像赋值   PeiJun Li 2019-04-09
                        $headphoto = $data['headphoto'];
                        $memberModel->setMemberInfoById($fid, $data);
                    }
                    //开通会员开通套餐
                    $packageBiz = new PackageBiz();
                    $operId = $this->_loginInfo['memberID'];
                    $openPackageRes = $packageBiz->openPackageByMemberOpen($fid, $operId);
                    if ($openPackageRes['code'] != 200) {
                        $this->apiReturn(203, [], '会员套餐开通失败', true);
                    }
                }
            }

            ////创建关系并且移动到分组中
            //$javaApi = new \Business\NewJavaApi\Member\MemberRelation();
            //$res     = $javaApi->createSupplyShip($aid, $fid, $this->_loginInfo['memberID']);
            //pft_log('memberCard/register_move_group', json_encode([$aid, $fid, $this->_loginInfo['memberID'], $res]));

        }

        if ($ac) {
            $opMember = $memberModel->getMemberInfo($ac, 'account');
            $opId     = $opMember['id'];
        } else {
            $opId = $this->_loginInfo['memberID'];
        }
        $unionTime = time();
        $time      = date('Y-m-d H:i:s', $unionTime);
        $orderPwd  = 123456;
        $other_les = $carInfo;
        if ($remarks) {
            $other_les .= '|' . $remarks;
        }
        $data = array(
            'memberID'    => $fid,                  //用户ID
            'apply_did'   => $aid,                  //开卡供应商ID
            'card_no'     => strval($cardNo),               //IC卡上显示的编号
            'physics_no'  => (string)$phyNo,        //IC卡的物理卡号
            'notice_type' => $noticeType,           //消费通知类型
            'password'    => md5(md5($orderPwd)),   //消费密码
            'op_id'       => $opId,                 //开卡人员id
            'status'      => 0,                     //IC卡状态 0 正常
            'address'     => $address,              //地址
            'headphoto'   => $headphoto,            //头像
            'province'    => $province,             //省
            'city'        => $city,                 //市
            'other_les'   => $other_les,            //车辆信息
            'sex'         => $sex,                  //性别
        );

        if ($card) {
            $data['id_card_no'] = $IdCardNo;
            $data['card_time']  = $time;
            $data['usetime']    = $unionTime;
            $data['dname']      = $dname;
            $data['mobile']     = $mobile;
            $result             = $memberCardModel->saveMemberCard($data);
            if (!$result) {
                pft_log('memberCard/insert_card_fail', print_r($data, true), 'month');
                $this->apiReturn(202, [], '开卡失败，请重试', true);
            }

            $openTime = microtime(true) - $logTime;
            pft_log('member_card/openTime', print_r('cardNo:' . $cardNo . '#orderTime:' . $openTime, true));
            $this->apiReturn(200, ['fid' => $fid, 'account' => $account, 'dname' => $dname], '开卡成功');
        } else {
            $data['updatetime'] = $time;
            $data['id_card_no'] = $IdCardNo;
            unset($data['id']);
            $memberCardModel->startTrans();
            $result = $memberCardModel->upMemberCard($data, $cid);
            //这个会员名下的所有会员卡的dname都要一样
            $dnameUpdate = $memberCardModel->updateMemberCardDnameEveryCard($fid, $dname);
            //dname同步到会员信息
            $memberDname = $memberModel->updateMemberInfo($fid, ['dname' => $dname], true);

            if ($result && $dnameUpdate && $memberDname) {
                $memberCardModel->commit();
                $this->apiReturn(200, [], '编辑成功', true);
            } else {
                $memberCardModel->rollback();
                $this->apiReturn(202, [], '编辑失败，请重试', true);
            }
        }
    }

    /**
     * 获取车辆类型信息-传给前端
     * @date   2017-2-23
     * <AUTHOR>
     *
     * @return
     */
    public function getCarList()
    {
        $data = [];
        foreach ($this->_carType as $car) {
            $tmp['type'] = array_keys($this->_carType, $car)[0];
            $tmp['name'] = $car;
            $data[]      = $tmp;
        }

        $this->apiReturn(200, $data, '查询成功');
    }

    /**
     * 获取相关景区信息
     * @date   2016-12-12
     * <AUTHOR>
     * @return
     */
    public function Lands()
    {
        $new = true;
        if ($new) {
            $res = $this->_getNewLands();
            if (empty($res)) {
                $this->apiReturn(201, [], '暂无数据');
            }
            $this->apiReturn(200, $res, '获取成功');
        }

        $ticketModel = new \Model\Product\Ticket();
        $lands       = $ticketModel->getSaleProducts($this->_memberId);      //获取供应产品
        if ($lands) {
            $tempLid = [];
            foreach ($lands as $val) {
                if (!isset($response[$val['landid']])) {
                    $response[$val['landid']] = array(
                        'pid'             => $val['landid'],        //景区ID （三亚业务特殊处理）
                        'p_name'          => $val['title'],
                        'daily_buy_limit' => '',
                        'buy_interval'    => '',
                        'buy_num_limit'   => '',
                        'dstatus'         => '',
                        'rid'             => '',
                    );
                    $tempLid[]                = $val['landid'];     //景区ID （三亚业务特殊处理）
                }
            }

            $memberCardModel = new \Model\Product\MemberCardConf();
            $limitLand       = $memberCardModel->getLandLimit($tempLid);

            if ($limitLand) {
                foreach ($response as $val) {
                    foreach ($limitLand as $v) {
                        if (isset($val['pid']) && isset($v['pid']) && ($val['pid'] == $v['pid'])) {     //景区ID （三亚业务特殊处理）
                            $response[$val['pid']]['rid']             = $v['id'];
                            $response[$val['pid']]['daily_buy_limit'] = $v['daily_buy_limit'];
                            $response[$val['pid']]['buy_interval']    = $v['buy_interval'];
                            $response[$val['pid']]['buy_num_limit']   = $v['buy_num_limit'];
                            $response[$val['pid']]['dstatus']         = $v['dstatus'];
                        }
                    }
                }
            }
            $total = count($response);
            $data  = ['list' => $response, 'total' => $total];
            $this->apiReturn(200, $data, '获取成功');
        } else {
            $this->apiReturn(201, [], '暂无数据');
        }

    }

    public function _getNewLands()
    {
        $page     = I('post.page', 1, 'intval');
        $pageSize = I('post.pageSize', 10, 'intval');
        $keyword  = I('post.keyword', '', 'strval,trim');

        $productBiz        = new \Business\Product\Product();
        $getSaleProductRes = $productBiz->getSaleLandList($this->_memberId, $keyword, null, 1, '', $page, $pageSize);
        $saleProductList   = $getSaleProductRes['list'];
        $total             = $getSaleProductRes['total'];

        if (empty($saleProductList)) {
            return [];
        }

        $response = [];
        if ($saleProductList) {
            $tempLid = [];
            foreach ($saleProductList as $val) {
                if (!isset($response[$val['id']])) {
                    $response[$val['id']] = array(
                        'pid'             => $val['id'],                   //景区ID （三亚业务特殊处理）
                        'p_name'          => $val['title'],
                        'daily_buy_limit' => '',
                        'buy_interval'    => '',
                        'buy_num_limit'   => '',
                        'dstatus'         => '',
                        'rid'             => '',
                    );
                    $tempLid[]             = $val['id'];
                }
            }

            $memberCardModel = new \Model\Product\MemberCardConf();
            $limitLand       = $memberCardModel->getLandLimit($tempLid);

            if ($limitLand) {
                foreach ($response as $val) {
                    foreach ($limitLand as $v) {
                        if (isset($val['pid']) && isset($v['pid']) && ($val['pid'] == $v['pid'])) {   //景区ID （三亚业务特殊处理）
                            $response[$val['pid']]['rid']             = $v['id'];
                            $response[$val['pid']]['daily_buy_limit'] = $v['daily_buy_limit'];
                            $response[$val['pid']]['buy_interval']    = $v['buy_interval'];
                            $response[$val['pid']]['buy_num_limit']   = $v['buy_num_limit'];
                            $response[$val['pid']]['dstatus']         = $v['dstatus'];
                        }
                    }
                }
            }
        }

        $list = $response;

        $res = [
            'list'  => $list,
            'total' => $total,
        ];

        return $res;
    }

    /**
     * 获取会员卡编辑信息
     * @date   2018-11-15
     * <AUTHOR>
     */
    public function memberCardInfos()
    {
        $fid    = I('post.fid', '', 'intval');
        $cardNo = I('post.card_no', '');
        $aid    = $this->_memberId;

        if (!$fid) {
            $this->apiReturn(201, [], '非法操作');
        }
        $memberCardModel = new \Model\Product\MemberCard();
        $field           = 'physics_no as phy_no,notice_type,status,card_no,id as cid,mobile,dname,headphoto,
            mobile, dname, headphoto, address, id_card_no, province, city, other_les, sex';
        $info            = $memberCardModel->getCardInfoByCardNoId($cardNo, $fid, $aid, $field);
        if (!$info) {
            $this->apiReturn(203, [], '会员卡信息不存在');
        }

        if (in_array($info['status'], [2, 3])) {
            $this->apiReturn(202, [], '会员卡被禁用或已冻结');
        }

        if ($info['phy_no']) {
            //封装物理卡号为十六进制
            $info['phy_no'] = dechex($info['phy_no']);
            $new            = '';
            $strlen         = strlen($info['phy_no']) - 1;
            for ($i = $strlen; $i >= 0; $i -= 2) {
                $new .= $info['phy_no'][$i - 1];
                $new .= $info['phy_no'][$i];
            }
            if (($strlen % 2) == 0) {
                $new = substr($new, 0, $strlen) . substr($new, -1, 1);
            }
            $info['phy_no'] = '0x' . $new;
        }

        if ($info['other_les']) {
            $others          = explode('|', $info['other_les']);
            $info['car']     = $others[0];
            $info['remarks'] = $others[1];
            unset($info['other_les']);
        }

        $info['openMan'] = $this->_loginInfo['dname'] ?? '';
        $data['list']    = $info;
        $this->apiReturn(200, $data, '获取成功');
    }

    /**
     * 会员卡修改手机号码
     * <AUTHOR>
     *
     * @param  int $fid 会员id
     * @param  string $password 密码
     * @param  int $phone 手机号码
     *
     * @return
     */
    public function modifyPhone()
    {
        /**
         * <AUTHOR> | @date 2018/11/20
         * 之前这块逻辑是用手机号查询用户表是否有被使用的用过
         * ①id与mobile查询出来dname不一致 || 普通用户(dtype == 5) 账号不等于手机号
         * ②新手机号账号余额为0
         * ③登录时间判断          --- 很早就被注释了
         * ④无供应商 或供应商仅登陆账号的主账号
         * ----------
         * 现在会员卡手机号已经不牵连用户表 且 会员卡表手机号唯一
         * ①单一改成判断会员卡表 - 使用IC编号识别新手机号是否被使用
         */
        $loginInfo = $this->getLoginInfo();
        $aid       = $loginInfo['sid'];
        $fid       = I('post.fid', '', 'intval');       //会员id --会员关系是一对多用会员id会有问题
        $passWord  = I('post.password', '', 'strval'); //密码  -- 验证注释已经没用看后期要不要密码验证
        $phone     = I('post.phone', '', 'intval');    //修改后的手机号
        $cardNo    = I('post.cardNo', '', 'strval');   //添加参数作为识别

        if (!$fid || !$passWord || !$phone) {
            $this->apiReturn(201, [], '非法操作', true);
        }

        if (!$cardNo) {
            $this->apiReturn(201, [], 'IC编号不能为空', true);
        }

        if (!isMobile($phone)) {
            $this->apiReturn(202, [], '手机号码格式有误', true);
        }

        $MemberCard  = new \Model\Product\MemberCard(); //会员卡模型
        $Member      = new \Business\Member\Member();   //会员业务模型
        $MemberModel = new \Model\Member\Member();      //会员模型

        $cardInfo = $MemberCard->getCardInfoByCardNoId($cardNo, false, $aid, 'id,mobile,memberID');
        if (!$cardInfo) {
            $this->apiReturn(203, [], '您无权修改手机号码!', true);
        }
        $fid        = $cardInfo['memberID']; //会员id
        $memberInfo = $Member->getInfo($fid);
        if (!$memberInfo) {
            $this->apiReturn(203, [], '用户信息发生错误', true);
        }
        //之前好早密码验证就被注释了
//        if($passWord != $memberInfo['password']) {
//            $this->apiReturn(203, [], '您无权修改手机号码!', true);
//        }
        if ($memberInfo['dtype'] == 6) {
            $fid = $MemberModel->getStaffBySonId($fid);
        }

        //手机号是否有被使用的判断
        $checkCard = $MemberCard->checkCardByMobile($fid, $phone);

        $existCustomerId = $Member->parseCustomerIdByMobile($phone);
        if ($existCustomerId && $existCustomerId != $memberInfo['customer_id']) {
            $checkMember = true;
        } else {
            $checkMember = false;
        }

        if (($checkCard || $checkMember) && !$this->checkNewMobile($cardInfo['mobile'], $phone, $checkCard, $aid)) {
            $this->apiReturn(204, [], '新手机号已经被其他用户使用!', true);
        }

        //修改手机号-使用用户id为条件，改一张卡手机号，改用户下所有卡手机号都要改
        $res = $MemberCard->updateMemberCardMobileByCardNo($fid, $phone);
        //同步会员信息
        $syncRes = $Member->updateMemberMobile($fid, $phone);

        if ($res && $syncRes) {
            $pwd = substr($phone, 5, 6);//修改手机号密码重置为手机号后六位
            $Member->updateMemberPassword($fid, $pwd);
            $this->apiReturn(200, [], '手机号码修改成功!', true);
        } else {
            $this->apiReturn(400, [], '手机号码修改失败!', true);
        }
    }

    /**
     * 上传图片到七牛云
     *
     * @param string $file_name 图片名称
     * @param string $base64 base64编码的图片
     *
     * @return array
     */
    private function uploadImageToQiniu($file_name, $base64)
    {
        if (empty($base64)) {
            return ['code' => 0, 'msg' => '数据为空'];
        }

        $base64 = str_replace(["\n", " "], ['', '+'], $base64);
        if (strpos($base64, 'data:') === false) {
            $base64 = 'data:image/jpg;base64,' . $base64;
        }

        $qiniu = new \Process\Resource\Qiniu();
        $res   = $qiniu->base64Upload($file_name, $base64);
        if ($res) {
            return ['code' => 200, 'url' => $res, 'msg' => 'ok'];
        } else {
            return ['code' => 0, 'msg' => $qiniu->getError()];
        }
    }

    /**
     * 加密会员卡数据
     *
     * <AUTHOR>
     * @date   2017-04-25
     *
     */
    public function encryptCardData()
    {

        $data = I('data', '', 'strval');

        if (!$data) {
            $this->apiReturn(204, [], '参数错误');
        }

        $key = load_config('android_terminal', 'auth');

        $base64   = base64_encode($data);
        $safeData = substr_replace($base64, $key, 5, 0);
//        $arr = ['\n' =>  '', '\r' => ''];
//        $safeData = strtr($safeData, $arr);
        $this->apiReturn(200, ['data' => $safeData]);

    }

    /**
     * 更新写卡状态
     *
     * <AUTHOR>
     * @date   2017-04-25
     *
     */
    public function isWrited()
    {
        //物理卡号
        $physics = I('physics', '', 'strval');

        if (!$physics) {
            $this->apiReturn(201, [], '缺少参数');
        }

        //16进制的开头如果有要去掉
        if (substr(trim($physics), 0, 2) == '0x') {
            $physics = substr(trim($physics), 2);
        }

        $phy_no_arr = str_split($physics, 2);
        krsort($phy_no_arr);
        $physics = hexdec(implode('', $phy_no_arr));

        $cardModel = new \Model\Product\MemberCard();
        $cardInfo  = $cardModel->getInfoByphysics($physics);
        if (!$cardInfo) {
            $this->apiReturn(202, [], '会员卡信息不存在');
        }
        //如果已经标记
        if ($cardInfo['info_writed']) {
            $this->apiReturn(200, [], '更新成功');
        }

        $res = $cardModel->updateWriteStatus($physics, 1);
        if (!$res) {
            pft_log('memberCard/offline_ticket/isWrited', $cardModel->getLastSql());
            $this->apiReturn(203, [], '更新失败');
        }
        $this->apiReturn(200, [], '更新成功');
    }

    /**
     * 会员筛选
     *
     * @date   2017-07-31
     * <AUTHOR> Lan
     *
     */
    public function collectMemberByExcel()
    {
        $lockKey = 'lock:sendBYBatch' . $this->_memberId;

        if (!empty ($_FILES['file_member']['name'])) {
            $redis = \Library\Cache\Cache::getInstance('redis');
            $lock  = $redis->get($lockKey);
            if ($lock) {
                self::uploadReturn(207, [], '请勿频繁操作');
            }
            $redis->lock($lockKey, 1, 60);
            $tempFile  = $_FILES['file_member']['tmp_name'];
            $fileTypes = explode(".", $_FILES['file_member']['name']);
            $fileType  = $fileTypes[count($fileTypes) - 1];
            //判别是不是.xls文件，判别是不是excel文件
            if (!in_array(strtolower($fileType), ['xls', 'xlsx'])) {
                $redis->rm($lockKey);
                self::uploadReturn(201, [], '不是Excel文件，重新上传');
            }
        } else {
            self::uploadReturn(204, [], '请选择文件');
        }

        $readExcel = new ReadExcel();
        $data      = $readExcel->read($tempFile);

        if (count($data) > 1000) {
            self::uploadReturn(204, [], '单次操作不许超过1000条');
        }

        if ($data) {
            $wxMemberModel = new WxMember();
            $MemberCard    = new \Model\Product\MemberCard();
            $Member        = new \Business\Member\Member();

            $csvStr    = '手机号,姓名,次数,数量,会员ID,开卡手机号,微信绑定,开卡日期,上次使用日期,实体卡号' . "\n";
            $filed     = 'mobile, memberID as id,card_time,usetime,card_no';
            $apply_did = intval($this->_memberId);
            pft_log('/memberCard/debug', json_encode($data));

            foreach ($data as $val) {
                $csvStr .= $val[0] . "\t" . ',' . $val[1] . "\t" . ',' . $val[2] . "\t" . ',' . $val[3];
                $val[0] = trim(strval($val[0]));
                $val[1] = trim(strval($val[1]));

                $memberInfo = $MemberCard->getCardInfoByAid($apply_did, $val[0], false, false, true, $filed);
                if (!$memberInfo) {
                    $member = $Member->getInfoByAccount($val[0]);
                    if ($member) {
                        $memberInfo = $MemberCard->getCardInfoByAid($apply_did, false, false, $member["id"], true,
                            $filed);
                    }
                }
                if (!$memberInfo) {
                    $memberInfo = $MemberCard->getCardInfoByAid($apply_did, false, $val[1], false, true, $filed);
                }

                if (!$memberInfo) {
                    $csvStr .= "\n";
                    continue;
                }
                if (!$memberInfo['usetime'] || $memberInfo['usetime'] == strtotime($memberInfo['card_time'])) {
                    $useTime = '不曾使用';
                } else {
                    $useTime = date('Y-m-d H:i:s', $memberInfo['usetime']);
                }

                $isBind = $wxMemberModel->getDataByFiaAid($memberInfo['id'], $this->_memberId);

                $bindFlag = '';
                if ($isBind) {
                    $bindFlag = '绑定';
                }

                $csvStr .= ',' . $memberInfo['id'] . "\t" . ',' . $memberInfo['mobile'] . "\t" . ',' . $bindFlag . "\t" . ',' . $memberInfo['card_time'] . "\t" . ',' . $useTime . "\t" . ',' . $memberInfo['card_no'] . "\t" . "\n";
            }

            $fileName = date('Y-m-d H:i:s') . '匹配结果.csv';

            $redis->rm($lockKey);

            unlink($tempFile);

            header("Content-type:text/csv");
            header("Content-Disposition:attachment;filename=" . $fileName);
            header('Cache-Control:must-revalidate,post-check=0,pre-check=0');
            header('Expires:0');
            header('Pragma:public');
            echo chr(0xEF) . chr(0xBB) . chr(0xBF);
            echo $csvStr;
        } else {
            $redis->rm($lockKey);

            unlink($tempFile);
            self::uploadReturn(204, [], '格式错误');
        }

    }

    /**
     * 上传文件返回
     * @date   2017-05-05
     * <AUTHOR> Lan
     *
     * @param  int $code 返回码
     * @param  array $data 返回数据
     * @param  $msg   string  具体说明
     *
     * @return string
     */
    static private function uploadReturn($code, $data, $msg)
    {
        if ($data == []) {
            $data = new \stdClass;
        }

        $data   = array(
            'code' => $code,
            'data' => $data,
            'msg'  => $msg,
        );
        $list   = json_encode($data, JSON_UNESCAPED_UNICODE);
        $string = "
                 <script type=\"text/javascript\">
                    var excelFileUpLoaded=window.parent.excelFileUpLoaded;
                    excelFileUpLoaded([201, $list,\"\u4e0d\u662fExcel\u6587\u4ef6\uff0c\u91cd\u65b0\u4e0a\u4f20\"]);
                </script>
                ";
        echo $string;
        exit();
    }

    /**
     * 提供给100005账号强制修改手机号的
     *
     * @date   2017-11-03
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function updateMobile()
    {
        if ($this->_memberId != 55) {
            $this->apiReturn(201, [], '无权操作');
        }

        $newMobile = I('post.new_phone', '', 'strval,trim');
        $oldMobile = I('post.old_phone', '', 'strval,trim');

        if ($newMobile === '' || $oldMobile === '') {
            $this->apiReturn(201, [], '手机号必填');
        }
        if (!Tools::ismobile($newMobile) || !Tools::ismobile($oldMobile)) {
            $this->apiReturn(201, [], '手机号格式有误');
        }

        pft_log('member_card/updateMobile', $_POST);

        $memberModel = new Member();

        $field = 'id,dname';
        //todo::会员卡多角色情况下，有取错角色可能
        $oldInfo = $memberModel->getMemberInfo($oldMobile, 'mobile', $field);
        //增加比对旧手机号
        $checkPhone = $memberModel->getMemberInfo($newMobile, 'mobile', 'mobile');
        if ($checkPhone['mobile']) {
            $this->apiReturn(204, [], '此手机号码已使用!', true);
        }
        if (!$oldInfo) {
            $this->apiReturn(202, [], '手机号' . $oldMobile . '对应的会员信息不存在');
        }

        $memberCardModel = new \Model\Product\MemberCard();
        $where           = [
            'memberID'  => intval($oldInfo['id']),
            'apply_did' => 55,
        ];
        $isMemberCard    = $memberCardModel->getMemberCardInfo($where, 'id');

        if (!$isMemberCard) {
            $this->apiReturn(203, [], '非三亚会员，无权操作');
        }

        $newInfo = $memberModel->getMemberInfo($newMobile, 'mobile', $field);

        if ($newInfo && trim($newInfo['dname']) != trim($oldInfo['dname'])) {
            $this->apiReturn(203, [], '新手机号已经被使用，且新旧手机号名称不一致，无权修改');
        }

        $resOld = $memberModel->setMemberInfoById($oldInfo['id'], ['mobile' => $newMobile]);
        if (!$resOld) {
            pft_log('member_card/updateMobile', $memberModel->_sql());
        }
        if ($newInfo) {
            $resNew = $memberModel->setMemberInfoById($newInfo['id'], ['mobile' => $oldMobile]);
            if (!$resNew) {
                pft_log('member_card/updateMobile', $memberModel->_sql());
            }
        }

        if (!$resOld || isset($resNew) && !$resNew) {
            $this->apiReturn(205, [], '更新失败，请联系技术员');
        }
        $this->apiReturn(200, [], 'success');
    }

    /**
     * 提供给100005账号转移授信资金的
     *
     * @date   2017-11-03
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function transferMoney()
    {
        $this->apiReturn(201, [], '功能暂时不开放');
    }

    /**
     * 重置密码
     * <AUTHOR>
     * @date   2018-11-28
     */
    public function resetPassword()
    {
        //会员卡绑定的人id
        $fid = I('fid', 0, 'intval');
        if (!$fid) {
            $this->apiReturn(204, [], '参数错误');
        }

        //是否是专属会员
        //$shipModel = new MemberRelationship();
        //if (!$shipModel->isOnlySupplier($fid, $this->_memberId)) {
        //    //$this->apiReturn(204, [], '非专属会员，无法重置');
        //}
        //是否开通了会员卡
        $cardModel = new \Model\Product\MemberCard();
        $where     = [
            'memberID'  => $fid,
            'apply_did' => $this->_memberId,
        ];
        $card      = $cardModel->getMemberCardInfo($where);
        if (!$card) {
            $this->apiReturn(204, [], '非会员卡用户，无法修改');
        }
        //获取会员信息
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($fid, 'id', 'mobile');

        if ($memberInfo['mobile']) {
            $password = substr($memberInfo['mobile'], 5);
        } else {
            $password = 'pft@12301';
        }

        //用户二期 - 信息获取修改
        $MemberBus = new \Business\Member\Member();
        $result    = $MemberBus->updateMemberPassword($fid, $password);
//        $data = ['password' => md5(md5($password))];
//        $result = $memberModel->updateMemberInfo($fid, $data);
        if ($result) {
            $this->apiReturn(200, [], "更新成功, 密码【{$password}】");
        } else {
            $this->apiReturn(204, [], '更新失败');
        }
    }

    /**
     * 检测三亚手机号是否是同一人、是否可释放新手机号
     * @date   2019-07-01
     * <AUTHOR> Li
     *
     * @param $oldMobile    用户旧手机号
     * @param $newMobile    用户新手机号
     * @param $newMobile    用户新手机号
     * @param $sid          供应商id
     *
     * @return bool|array
     */
    private function checkNewMobile($oldMobile, $newMobile, $checkCard, $sid)
    {
        if ($checkCard) {
            $this->apiReturn(204, [], '新手机号已经被其他用户使用!');
        }
        //三亚会员卡现存在这种情况,同一个用户有两个手机号,其中卡A办理会员卡,卡B为平台用户
        //用户要将会员卡,卡A手机号更换为卡B,这时就提示“新手机号已经被其他用户使用”
        //现调整三亚会员卡修改手机号规则,判断如下：
        //1. 验证卡A手机号用户的姓名是否和卡B手机号姓名相同. 如不同不做修改
        $memberModel = new Member();
        //获取手机号用户姓名
        $field      = 'mobile,id,dname,customer_id';
        $memberArr  = $memberModel->getMemberInfoByMulti([$newMobile, $oldMobile], 'mobile', $field);
        $memberInfo = [];
        foreach ($memberArr as $item) {
            $memberInfo[$item['mobile']] = $item;
        }

        if ($memberInfo[$oldMobile]['mobile'] == $memberInfo[$newMobile]['mobile']) {
            $this->apiReturn(204, [], '新手机号与旧手机号相同，无需操作！');
        }

        //判断用户名是否相同
        if ($memberInfo[$oldMobile]['dname'] !== $memberInfo[$newMobile]['dname']) {
            $this->apiReturn(204, [], '用户名不同，无法修改', true);
        }
        //2. 校验卡B手机号用户与三亚供应商是否存在唯一供销关系.查询B手机号用户是否有余额或者授信金额.如果有给出"新手机号用户存在余额，请联系管理员处理"，不做修改

        //是否是专属会员
        //$shipModel = new MemberRelationship();
        //if (!$shipModel->isOnlySupplier($memberInfo[$newMobile]['id'], $this->_memberId)) {
        //    $this->apiReturn(204, [], '非专属会员，无法重置');
        //}

        //查询用户账户是否有余额
        $accountBook = new \Business\JavaApi\Account\AccountBook();
        $memberMoney = $accountBook->queryBookMany2One($sid, [$memberInfo[$newMobile]['id']],
            \Library\Constants\Account\BookSubject::CREDIT_SUBJECT_CODE);

        if ($memberMoney['code'] != 200) {
            $this->apiReturn($memberMoney['code'], $memberMoney['data'], $memberMoney['msg']);
        }

        $list = [];
        if ($memberMoney['data']) {
            foreach ($memberMoney['data'] as $item) {
                $list[$item['member_id']] = $item['balanceMoney'] + $item['tradeLimitMoney'];
            }
        }
        if ($list[$memberInfo[$newMobile]['id']]) {
            $this->apiReturn(204, [], '新手机号用户存在余额，请联系管理员处理');
        }

        //3. 假如以上条件都满足,则将卡B手机号用户修改如下：member表`mobile`=''、customer表`mobile`=''、customer_auth表`identifier`='卡B-del',后续程序继续往下走
        $memberBiz = new \Business\Member\Member();
        $updateRes = $memberBiz->updateMemberBaseInfo($memberInfo[$newMobile]['id'], ['mobile' => ''], false);

        //用户表修改完成 再修改 customer表及customer_auth表
        if ($updateRes) {
            $api = new \Business\JavaApi\Member\CustomerUpdate();
            $res = $api->deleteMobile($memberInfo[$newMobile]['customer_id']);
            if ($res['code'] != 200) {
                $this->apiReturn(204, [], '新手机号用户异常，请联系管理员处理');
            }

            return true;
        }

        return false;
    }
}