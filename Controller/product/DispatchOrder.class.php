<?php
/**
 * 计调下单
 * <AUTHOR>
 * @date 2020/8/31
 */

namespace Controller\Product;

use Business\Finance\Credit;
use Library\Controller;
use Model\Member\MemberRelationship;

class DispatchOrder extends Controller
{
    private $_sid;//上级用户ID
    private $_memberId;//用户ID
    private $_dtype;//当前账号类型
    private $_sdtype;//上级账号类型

    private $_businessLib;
    private $_object;

    public function __construct()
    {
        parent::__construct();
        $loginInfo       = $this->getLoginInfo();
        $this->_sid      = $loginInfo['sid'];//上级用户ID
        $this->_dtype    = $loginInfo['dtype'];//当前账号类型
        $this->_sdtype   = $loginInfo['sdtype'];//上级账号类型
        $this->_memberId = $loginInfo['memberID'];//用户ID

        $this->_businessLib = new \Business\Product\DispatchOrder();

        $array         = [];
        $this->_object = (object)$array;
    }

    /**
     * 计调下单列表
     * <AUTHOR>
     * @date 2020/9/4
     *
     * @return array
     */
    public function list()
    {
        $search = I('param.search', '', 'strval');//搜索关键字
        $page   = I('param.page', 1, 'intval');//当前页
        $limit  = I('param.limit', 20, 'intval');//每页大小

        if ($limit > 200) {
            $this->apiReturn(203, [], '参数错误');
        }

        //权限效验
        $dispatchOrderLib = new \Business\Product\DispatchOrder();
        $moduleRes        = $dispatchOrderLib->checkAuth($this->_sid);
        if ($moduleRes['code'] != 200) {
            $this->apiReturn(400, [], '无权限');
        }
        if ($moduleRes['data']['is_open'] == 0) {
            $this->apiReturn(400, [], '无权限');
        }

        $relationModel = new MemberRelationship($this->_sid);
        $result        = $relationModel->getDispatchDistributor($search, $page, $limit);
        $total         = reset($result);
        $distributor   = end($result);

        if (empty($distributor)) {
            $data = ['page' => $page, 'limit' => $limit, 'total' => $total, 'list' => $distributor];
            $this->apiReturn(200, $data, '操作成功');
        }

        //添加授信数据
        $creditLib   = new Credit();
        $ids         = array_column($distributor, 'id');
        $dids        = array_values(array_unique($ids));
        $distributor = $creditLib->handleCreditInfoByList($this->_sid, $dids, $distributor);

        //添加产品数据
        $countMap = [];
        $countRes = $dispatchOrderLib->countPlanProduct($this->_sid, $dids);
        if ($countRes['code'] == 200 && !empty($countRes['data'])) {
            $countMap = array_key($countRes['data'], 'fid');
        }
        foreach ($distributor as $countKey => $countVal) {
            $distributor[$countKey]['ticket_num'] = $countMap[$countVal['id']]['ticketCount'] ?? 0;
            $distributor[$countKey]['product_num'] = $countMap[$countVal['id']]['productCount'] ?? 0;
        }

        $data = ['page' => $page, 'limit' => $limit, 'total' => $total, 'list' => $distributor];
        $this->apiReturn(200, $data, '操作成功');
    }

    /**
     * 判断是否开通了计调下单应用
     * <AUTHOR>
     * @date 2020/8/31
     *
     * @return array
     */
    public function checkAuth()
    {
        $dispatchOrderLib = new \Business\Product\DispatchOrder();
        $moduleRes        = $dispatchOrderLib->checkAuth($this->_sid);
        if ($moduleRes['code'] != 200) {
            $this->apiReturn($moduleRes['code'], $this->_object, '无权限');
        }
        $this->apiReturn($moduleRes['code'], $moduleRes['data'], $moduleRes['msg']);
    }

}