<?php

namespace Controller\Product;

use Business\Authority\AuthLogic as AuthLogicBiz;
use Business\Authority\DataAuthLogic;
use Business\Base;
use Business\JavaApi\TicketApi;
use Business\Member\Authority;
use Business\MemberLogin\MemberLoginHelper;
use Business\NewJavaApi\NewPms\Query;
use Business\PftShow\DisStorage;
use Business\Product\PoiProduct as BusinessPoiProduct;
use Business\Product\ProductZgyList;
use Exception;
use Library\Controller;
use Model\AppCenter\ModuleList;

class PoiProduct extends Controller
{
    private $memberId;
    private $isSuper;
    private $dtype;
    private $qx;
    private $sdtype;
    private $sid;
    private $subMerchantId;

    public function __construct()
    {
        parent::__construct();

        $this->isLogin('ajax');

        $loginInfo = $this->getLoginInfo();

        $this->memberId = $loginInfo['memberID'];
        $this->isSuper = $this->isSuper();
        $this->dtype = $loginInfo['dtype'];
        $this->qx = $loginInfo['qx'];
        $this->sdtype = $loginInfo['sdtype'];
        $this->sid = $loginInfo['sid'];

        // 子商户特殊处理
        $this->subMerchantId = 0;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $this->subMerchantId = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $this->memberId = $this->sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        // 员工情况所属上级id
        if ($this->dtype == 6 && !$this->isSuper) {
            $this->memberId = $this->sid;
        }

        // 权限判断
        $check = (new ProductZgyList())->judgeAuth($this->dtype, $this->qx, $this->sdtype);
        if ($check['code'] !== 0) {
            $this->apiReturn($check['code'], [], $check['msg']);
        }
    }

    /**
     * 产品列表
     */
    public function getList(): void
    {
        $searchType = I('search_type', 1, 'intval');
        $isOnlyProduct = $this->getIsOnlyProduct();

        // 组装 new pms 请求参数
        try {
            $input = BusinessPoiProduct::checkInput();
            $input['sub_merchant_id'] = $this->subMerchantId;
            $requestParams = BusinessPoiProduct::convertInputToNewPmsRequestParams($this->isSuper, $this->memberId, $input);
        } catch (Exception $e) {
            $this->apiReturn(400, [], $e->getMessage());
            return;
        }
        // 数据权限校验
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition();
        if ($condition === false) {
            $result = Base::returnData(200);
        } else {
            if (!empty($condition['lidList'])) {
                $requestParams['spuInfo']['includeSpuIdList'] = $condition['lidList'];
            }
            if (!empty($condition['notLidList'])) {
                $requestParams['spuInfo']['excludeSpuIdList'] = $condition['notLidList'];
            }
            // 默认固定展示至多 3 条 spu
            $requestParams['spuInfo']['size'] = 3;
            // 加载出全部 sku_ids
            $requestParams['skuInfo']['from'] = 0;
            $requestParams['skuInfo']['size'] = Query::ES_MAX_SIZE;

            $result = (new Query())->chunkQueryPage($requestParams);
            if ($result['code'] != 200) {
                $this->apiReturn($result['code'], [], $result['msg']);
            }
        }

        try {
            $result = BusinessPoiProduct::convertNewPmsResult($result, $this->isSuper, $this->memberId, $isOnlyProduct, true);
        } catch (Exception $e) {
            $this->apiReturn(400, [], $e->getMessage());
        }

        [$isTeamOrder, $result] = $this->handleExpireInfo($result, $searchType);

        if (empty($result['data']['products'])) {
            $this->apiReturn($result['code'], $result['data'], $result['message']);
        }

        // 处理酒店以外的产品
        $result = BusinessPoiProduct::bypassHandleProductsByResult($result, ['N'], function ($newResult) use ($isTeamOrder) {
            return BusinessPoiProduct::handleOtherProducts($newResult, $this->isSuper, $this->sid, $this->memberId, $isTeamOrder);
        });

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 加载更多Spu
     */
    public function loadMoreSpus(): void
    {
        $poiId = I('poi_id', 0, 'intval');
        if (!$poiId) {
            $this->apiReturn(400, [], 'poi_id 参数错误');
        }
        $from = I('from', 0, 'intval');
        $size = I('size', 3, 'intval');
        $ratePlanCode = I('rate_plan_code', '', 'trim');
        $isOnlyProduct = $this->getIsOnlyProduct();

        // 组装 new pms 请求参数
        try {
            $input = BusinessPoiProduct::checkInput();
            $input['sub_merchant_id'] = $this->subMerchantId;
            $requestParams = BusinessPoiProduct::convertInputToNewPmsRequestParams($this->isSuper, $this->memberId, $input);
        } catch (Exception $e) {
            $this->apiReturn(400, [], $e->getMessage());
            return;
        }

        $requestParams['poiId'] = $poiId;
        $requestParams['spuInfo']['from'] = $from;
        $requestParams['spuInfo']['size'] = $size;
        $requestParams['skuInfo']['from'] = 0;
        $requestParams['skuInfo']['size'] = 3;
        if ($ratePlanCode) {
            $arr = explode('|', $ratePlanCode);
            if (count($arr) != 3) {
                $this->apiReturn(200);
            }
            [$aid, $centreSpuId, $centreSkuId] = $arr;
            if ($this->isSuper) {
                $requestParams['spuInfo']['sid'] = $aid;
            } elseif ($aid != $this->sid) {
                $this->apiReturn(200);
            }
            $requestParams['spuInfo']['spuId'] = $centreSpuId;
            $requestParams['skuInfo']['skuId'] = $centreSkuId;
        }

        if ($this->isSuper) {
            $supplierId = i('supplier_id', 0, 'intval');
            if ($supplierId) {
                $requestParams['supplierId'] = $supplierId;
            }
        }
        if ($spuId = I('spu_id', 0, 'intval')) {
            $requestParams['spuInfo']['spuId'] = $spuId;
        }
        if ($skuId = I('sku_id', 0, 'intval')) {
            $requestParams['skuInfo']['skuId'] = $skuId;
        }

        $result = (new Query())->queryPage($requestParams);
        if ($result['code'] != 200) {
            $this->apiReturn(400, [], $result['msg']);
        }
        if (empty($result['data']['records'])) {
            $this->apiReturn(200, [], '没有更多数据');
        }

        // 只取第一个POI
        $result['data']['records'] = [$result['data']['records'][0]];
        try {
            $result = BusinessPoiProduct::convertNewPmsResult($result, $this->isSuper, $this->memberId, $isOnlyProduct, true);
        } catch (Exception $e) {
            $this->apiReturn(400, [], $e->getMessage());
            return;
        }

        ['code' => $code, 'data' => $data, 'message' => $message] = $result;
        $data = $data['products'][0]['room_products'] ?? [];
        $this->apiReturn($code, $data, $message);
    }

    /**
     * 加载更多Sku
     */
    public function loadMoreSkus(): void
    {
        $poiId = I('poi_id', 0, 'intval');
        $spuId = I('spu_id', 0, 'intval');
        if (!$poiId || !$spuId) {
            $this->apiReturn(400, [], 'poi_id、spu_id 是必填项');
        }
        $from = I('from', 0, 'intval');
        $size = I('size', 3, 'intval');

        // 组装 new pms 请求参数
        try {
            $input = BusinessPoiProduct::checkInput();
            $input['sub_merchant_id'] = $this->subMerchantId;
            $requestParams = BusinessPoiProduct::convertInputToNewPmsRequestParams($this->isSuper, $this->memberId, $input);
        } catch (Exception $e) {
            $this->apiReturn(400, [], $e->getMessage());
            return;
        }

        $requestParams['poiId'] = $poiId;
        $requestParams['spuInfo']['spuId'] = $spuId;
        $requestParams['skuInfo']['from'] = $from;
        $requestParams['skuInfo']['size'] = $size;

        $result = (new Query())->queryPage($requestParams);
        if ($result['code'] != 200) {
            $this->apiReturn(400, [], $result['msg']);
        }
        if (empty($result['data']['records'])) {
            $this->apiReturn(200, [], '没有更多数据');
        }

        $result['data']['records'] = [$result['data']['records'][0]];
        try {
            $result = BusinessPoiProduct::convertNewPmsResult($result, $this->isSuper, $this->memberId);
        } catch (Exception $e) {
            $this->apiReturn(400, [], $e->getMessage());
            return;
        }

        ['code' => $code, 'data' => $data, 'message' => $message] = $result;
        $data = $data['products'][0]['room_products'][0]['rate_plans'] ?? [];
        $this->apiReturn($code, $data, $message);
    }

    /**
     * 处理过期票数和即将过期票数
     */
    private function handleExpireInfo(array $result, int $searchType): array
    {
        $res = (new AuthLogicBiz())->getAuthTag($this->sid, $this->memberId);
        $ownAuth = $res['data'] ?? [];

        //校验下报团预订管理的权限是否有开启
        $isTeamOrder = 0;
        if (in_array('baotuan_ordergong', $ownAuth) || in_array('baotuan_manage_s', $ownAuth)) {
            $isTeamOrder = 1;
        }

        $isOpen = false;
        try {
            //更新成功之后 且有需要更新分销库存的
            $checkRes = (new DisStorage())->checkDisStorageAuth($this->sid);
            //有开通开放功能的 再为新创建分区的创建初始模板
            if ($checkRes['code'] == 200 && isset($checkRes['data']['is_open']) && $checkRes['data']['is_open']) {
                $isOpen = true;
            }
        } catch (\Throwable $exception) {
        }

        //设置过期票数和即将过期票数默认值
        $result['data']['expireInfo'] = [
            'aboutExpireCount' => 0,
            'expireCount'      => 0,
            'team_order_flag'  => $isTeamOrder,
            'open_dis_storage' => $isOpen,
        ];

        if ($searchType == 1 && !$this->isSuper) {
            //获取用户过期或即将过期票数量
            $expireTicketInfo = TicketApi::getExpireCount($this->memberId, $this->subMerchantId);
            if ($expireTicketInfo['code'] == 200) {
                $result['data']['expireInfo'] = [
                    'aboutExpireCount' => $expireTicketInfo['data']['aboutExpireCount'] ?? 0,
                    'expireCount'      => $expireTicketInfo['data']['expireCount'] ?? 0,
                    'team_order_flag'  => $isTeamOrder,
                    'open_dis_storage' => $isOpen,
                ];
            }
        }

        //判断登录用户是否有开通分时预约
        $moduleConfig = (new ModuleList())->getPackageLogInfoByUserIdArr($this->memberId, 61);
        if ($moduleConfig && !in_array($moduleConfig['status'], [1, 3, 7])) {
            $result['data']['expireInfo']['timeShare'] = 1;
        }
        return [$isTeamOrder, $result];
    }

    private function getIsOnlyProduct(): bool
    {
        $searchType = I('search_type', 1, 'intval');
        $isOnlyProduct = I('is_only_product', 0, 'boolval');
        if ($isOnlyProduct) {
            return true;
        }
        if ($searchType == BusinessPoiProduct::SEARCH_TYPE_WAIT_PUBLISH) {
            return true;
        }
        return false;
    }
}