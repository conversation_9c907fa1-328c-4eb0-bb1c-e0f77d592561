<?php
/**
 * 附加票
 * <AUTHOR>
 * @date 2021/9/13
 */

namespace Controller\product;

use Business\MemberLogin\MemberLoginHelper;
use Business\Product\TicketDeputyBiz;
use Library\Controller;

class TicketDeputy extends Controller
{
    private $_sid;//上级用户ID
    private $_memberId;//用户ID
    private $_dtype;//当前账号类型
    private $_sdtype;//上级账号类型

    private $_businessLib;
    private $_object;

    private $_time;

    public function __construct()
    {
        parent::__construct();
        $loginInfo          = $this->getLoginInfo();
        $this->_sid         = $loginInfo['sid'];//上级用户ID
        $this->_dtype       = $loginInfo['dtype'];//当前账号类型
        $this->_sdtype      = $loginInfo['sdtype'];//上级账号类型
        $this->_memberId    = $loginInfo['memberID'];//用户ID
        $this->_businessLib = new TicketDeputyBiz();

        $array         = [];
        $this->_object = (object)$array;

        $this->_time = time();
    }

    /**
     * 获取自供应产品
     * <AUTHOR>
     * @date 2021/9/13
     *
     * @return array
     */
    public function getProductList()
    {
        $keyword = I('keyword', '', 'strval');
        $page    = I('page', 1, 'intval');     //当前页数
        $size    = I('size', 10, 'intval');    //每页条数
        $subSid  = 0;
        $sid = $this->_sid;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }
        $result = $this->_businessLib->getProductList($sid, $keyword, $page, $size, $subSid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /***
     * 根据附属票查询主票
     * <AUTHOR>
     * @date 2021/9/13
     *
     * @return array
     */
    public function queryTicketMaster()
    {
        $tid = I('tid', 1, 'intval');

        if (empty($tid)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数缺失');
        }

        $sid = $this->_sid;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $result = $this->_businessLib->queryTicketMaster($sid, $tid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据时间和主票id查询有库存的附属票
     * <AUTHOR>
     * @date 2021/9/14
     *
     * @return array
     */
    public function queryTicketDeputyByTidAndDate()
    {

        $tid     = I('tid', 0, 'intval');
        $applyId = I('apply_id', 0, 'intval');
        $date    = I('date', '', 'strval');
        $fsid    = I('fsid', 0, 'intval');

        if (!empty($fsid)) {
            $memberId = $fsid;
            $channel = 12;
        } else {
            $memberId = $this->_sid;
            $channel = 5;
        }

        if (empty($tid) || empty($applyId) || empty($date)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数缺失');
        }

        $ticketDeputyBiz = new TicketDeputyBiz();

        $result = $ticketDeputyBiz->queryTicketDeputyByTidAndDate($memberId, $applyId, $date, $tid, $channel);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 批量获取票附加票时段限制
     * <AUTHOR>
     * @date 2021/9/14
     *
     * @return array
     */
    public function queryTicketDeputyInfos()
    {
        $tids = I('ids');
        if (empty($tids)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数缺失');
        }

        $ticketDeputyBiz = new TicketDeputyBiz();
        $result          = $ticketDeputyBiz->queryTicketDeputyInfos($tids);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}