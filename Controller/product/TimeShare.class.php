<?php
/**
 * 分时预约相关
 *
 * <AUTHOR> Li
 * @date    2020-02-22
 */

namespace Controller\product;

use Business\Member\MemberRelation;
use Business\MemberLogin\MemberLoginHelper;
use Business\Product\Get\EvoluteQuery;
use Business\Product\TicketDeputyBiz;
use Library\Controller;
use Business\Product\TimeShare as TimeShareBiz;
use Model\Product\Evolute;
use Model\Product\Land;

class TimeShare extends Controller
{
    private $_sid          = null;
    private $_loginInfo    = null;
    private $_timeShareBiz = null;

    public function __construct()
    {
        $this->_sid       = $this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo();

    }

    /**
     * 获取分时预约操作接口
     */
    public function getTimeShareBiz()
    {
        if (!$this->_timeShareBiz) {
            $this->_timeShareBiz = new TimeShareBiz();
        }

        return $this->_timeShareBiz;
    }

    //====================分时预约规则=======================

    /**
     * 创建分时预约应用规则
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function createRule()
    {
        $ruleName   = I('post.rule_name');
        $lid        = I('post.lid');
        $channel    = I('post.channel');
        $stores     = I('post.stores', '');
        $totalStore = I('post.total_store');
        $mergeFlag  = I('post.merge_flag', 1, 'intval');   //库存规则:0:渠道独立库存,1:渠道合并库存

        if (empty($ruleName) || empty($lid) || empty($channel) || !is_numeric($totalStore)) {
            parent::apiReturn(204, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->createRule($this->_sid, $ruleName, $lid, $channel, $stores,
            $totalStore, $this->_loginInfo['memberID'], $state = 1, $mergeFlag);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取分时预约应用规则
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function getRuleList()
    {
        $lid      = I('post.lid');
        $pageNum  = I('post.page_num', 1, 'intval');
        $pageSize = I('post.page_size', 5, 'intval');

        if (empty($lid)) {
            parent::apiReturn(203, [], '缺少景区id');
        }

        $list = $this->getTimeShareBiz()->getRuleList($lid, $pageNum, $pageSize);

        if (empty($list)) {
            parent::apiReturn(204, [], '暂无数据');
        }
        parent::apiReturn(200, $list, '获取成功');
    }

    /**
     * 通过规则id获取规则信息
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function getRuleById()
    {
        $id = I('post.id');

        if (empty($id)) {
            parent::apiReturn(203, [], '缺少规则id');
        }

        $ruleInfo = $this->getTimeShareBiz()->getRuleById($id);

        if (empty($ruleInfo)) {
            parent::apiReturn(204, [], '规则获取失败');
        }
        parent::apiReturn(200, $ruleInfo, '规则获取成功');
    }

    /**
     * 编辑分时预约应用规则
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function editRule()
    {
        $ruleId     = I('post.rule_id');
        $ruleName   = I('post.rule_name');
        $lid        = I('post.lid');
        $channel    = I('post.channel');
        $stores     = I('post.stores', '');
        $totalStore = I('post.total_store');
        $mergeFlag  = I('post.merge_flag', 1, 'intval');   //库存规则:0:渠道独立库存,1:渠道合并库存

        if (empty($ruleId) || empty($ruleName) || empty($lid) || empty($channel) || !is_numeric($totalStore)) {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->editRule($ruleId, $this->_sid, $ruleName, $lid, $channel,
            $stores, $totalStore, $this->_loginInfo['memberID'], $state = 1, $mergeFlag);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除分时预约应用规则
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function delRule()
    {
        $idArr = I('post.ids');

        if (empty($idArr)) {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        $tmpIdArr = explode(',', $idArr);
        if (!is_array($tmpIdArr)) {
            $idArr = [$tmpIdArr];
        } else {
            $idArr = $tmpIdArr;
        }

        $result = $this->getTimeShareBiz()->delRule($idArr, $this->_loginInfo['memberID']);
        if (!$result) {
            parent::apiReturn(204, [], '删除失败');
        }
        parent::apiReturn(200, [], '删除成功');
    }

    /**
     * 获取分时预约应用规则
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function getRuleListByRuleIds()
    {
        $ruleIds = I('post.rule_ids');  //规则id 多个以逗号隔开

        if (empty($ruleIds)) {
            parent::apiReturn(203, [], '缺少规则id');
        }

        $idArr = explode(',', $ruleIds);
        if (!is_array($idArr)) {
            parent::apiReturn(203, [], '规则id有误');
        }

        $list = $this->getTimeShareBiz()->queryStorageSectionReservationRuleByIds($idArr);

        if (empty($list)) {
            parent::apiReturn(204, [], '暂无数据');
        }
        parent::apiReturn(200, $list, '获取成功');
    }

    /**
     * 通过规则id计算出总共的库存
     * <AUTHOR> Li
     * @date   2020-02-26
     *
     */
    public function sumStorageByRuleIds()
    {
        $ruleIds = I('post.rule_ids');  //规则id 多个以逗号隔开

        if (empty($ruleIds)) {
            parent::apiReturn(203, [], '缺少规则id');
        }

        $idArr = explode(',', $ruleIds);
        if (!is_array($idArr)) {
            parent::apiReturn(203, [], '规则id有误');
        }

        $list = $this->getTimeShareBiz()->sumStorageByRuleIds($idArr);

        if (empty($list)) {
            parent::apiReturn(204, [], '暂无库存');
        }
        parent::apiReturn(200, $list, '获取成功');
    }

    /**
     * 通过景区id获取规则列表
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function getRuleListByLid()
    {
        $lid = I('post.lid');

        if (empty($lid)) {
            parent::apiReturn(203, [], '缺少景区id');
        }

        $list = $this->getTimeShareBiz()->queryStorageSectionReservationRule($lid);

        if (empty($list)) {
            parent::apiReturn(204, [], '暂无数据');
        }
        parent::apiReturn(200, $list, '获取成功');
    }

    /**
     * 通过景区id获取规则对应的库存
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function getRuleStorageList()
    {
        $lid = I('post.lid');

        if (empty($lid)) {
            parent::apiReturn(203, [], '缺少景区id');
        }

        $list = $this->getTimeShareBiz()->queryStorageSectionReservationRule($lid);

        if (empty($list)) {
            parent::apiReturn(204, [], '暂无数据');
        }

        $map = [];
        foreach ($list as $item) {
            $map[$item['id']] = $item['totalStore'];
        }
        parent::apiReturn(200, $map, '获取成功');
    }

    //====================分时预约配置=======================

    /**
     * 创建分时预约配置
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function createConf()
    {
        $lid          = I('post.lid');                    //景区id
        $startDate    = I('post.start_data');             //启用开始时间
        $endDate      = I('post.end_data');               //启结束时间
        $sectionTimes = I('post.section_times');          //分时预约配置时间段信
        $weekDays     = I('post.weekDays', '');               //星期

        //[
        //    ['beginTime' => '1000', 'endTime' => '1100', 'sectionRuleIds' => '1,2', 'type' => 1],
        //    ['beginTime' => '1100', 'endTime' => '1200', 'sectionRuleIds' => '1,2', 'type' => 1]
        //];

        if (empty($lid) || empty($startDate) || empty($endDate) || empty($sectionTimes) || $weekDays === '') {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->createConf($lid, $this->_loginInfo['memberID'], $startDate, $endDate,
            $sectionTimes, $weekDays);

        if ($result['code'] != 200) {
            parent::apiReturn(204, [], $result['msg']);
        }
        parent::apiReturn(200, [], '配置创建成功');
    }

    /**
     * 编辑分时预约配置
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function editConf()
    {
        $lid           = I('post.lid');                    //景区id
        $sectionDateId = I('post.section_date_id');        //预约日期段ID
        $startDate     = I('post.start_data');             //启用开始时间
        $endDate       = I('post.end_data');               //启结束时间
        $sectionTimes  = I('post.section_times');          //分时预约配置时间段信
        $weekDays      = I('post.weekDays', '');               //星期

        //[
        //    ['beginTime' => '1000', 'endTime' => '1100', 'sectionRuleIds' => '1,2', 'type' => 1],
        //    ['beginTime' => '1100', 'endTime' => '1200', 'sectionRuleIds' => '1,2', 'type' => 1]
        //];

        if (empty($lid) || empty($sectionDateId) || empty($startDate) || empty($endDate) || $weekDays === '') {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->editConf($lid, $sectionDateId, $this->_loginInfo['memberID'], $startDate,
            $endDate, $sectionTimes, $weekDays);

        if ($result['code'] != 200) {
            parent::apiReturn(204, [], $result['msg']);
        }
        parent::apiReturn(200, [], '配置修改成功');
    }

    /**
     * 删除分时预约配置
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function delConf()
    {
        $lid           = I('post.lid');
        $sectionDateId = I('post.section_date_id');

        if (empty($lid) || empty($sectionDateId)) {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->delConf($lid, $sectionDateId, $this->_loginInfo['memberID']);

        if (!$result) {
            parent::apiReturn(204, [], '删除失败');
        }
        parent::apiReturn(200, [], '删除成功');
    }

    /**
     * 获取分时预约配置列表
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function getConfList()
    {
        $lid         = I('post.lid');
        $reserveDate = I('post.reserve_data', '');
        $beginTime   = I('post.begin_time', '');    //开始时间段HHmm
        $endTime     = I('post.end_time', '');      //结束时间段HHmm
        $ruleId      = I('post.rule_id', 0);
        $pageNum     = I('post.page_num', 1, 'intval');
        $pageSize    = I('post.page_size', 5, 'intval');
        $state       = I('post.state', -1, 'intval');

        if (empty($lid)) {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        $list = $this->getTimeShareBiz()->getConfList((int)$lid, $reserveDate, $beginTime, $endTime, (int)$ruleId,
            $pageNum, $pageSize, $state);

        if (empty($list)) {
            parent::apiReturn(204, [], '暂无数据');
        }
        parent::apiReturn(200, $list, '获取成功');
    }

    /**
     * 指定时间段分时库存失效
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function invalidConf()
    {
        $lid           = I('post.lid');
        $sectionDateId = I('post.section_date_id');

        if (empty($lid) || empty($sectionDateId)) {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->invalidConf($lid, $sectionDateId, $this->_loginInfo['memberID']);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 查看分时预约配置详情
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function getConfDetail()
    {
        $lid           = I('post.lid');
        $sectionDateId = I('post.section_date_id');

        if (empty($lid) || empty($sectionDateId)) {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->queryConf($lid, $sectionDateId);

        if (!$result) {
            parent::apiReturn(204, [], '详情获取失败');
        }
        parent::apiReturn(200, $result, '详情获取成功');
    }

    /**
     * 根据景区获取景区下最新一条已存在规则配置
     * <AUTHOR> Li
     * @date   2020-04-15
     *
     */
    public function queryLastConfigStorageSectionByLid()
    {
        $lid = I('post.lid');

        if (empty($lid)) {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->queryLastConfigStorageSectionByLid($lid);

        if (!$result) {
            parent::apiReturn(204, [], '配置获取失败');
        }
        parent::apiReturn(200, $result, '配置获取成功');
    }

    //============分时预约查询及操作============

    /**
     * 开启景区分时预约配置
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function openConf()
    {
        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }
        $lid        = I('post.lid');
        $isContinue = I('post.isContinue', '');

        if (empty($lid)) {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        // 判断 期票 开启分时预约 条件
        $TicketApi          = new \Business\JavaApi\Product\Ticket();
        $validTicketInfo    = $TicketApi->queryListingTicketIdAndNameByItemId($lid);
        $otherTicketInfoArr = [];
        if ($validTicketInfo['code'] == 200 && $validTicketInfo['data']) {
            $otherTicketInfoArr = $validTicketInfo['data'];
        }
        //$ticketApi          = new \Business\JavaApi\TicketApi();
        //$otherTicketInfoArr = $ticketApi->getOtherTickets($lid);
        if (!empty($otherTicketInfoArr)) {
            $returnRes = $this->checkSectionReserve($otherTicketInfoArr);
            if ($returnRes == -1) {
                $msgName = [];
                foreach ($otherTicketInfoArr as $value) {
                    if ($value['preSale'] == 1 && $value['isOnlineReserve'] == 0) {
                        $msgName[] = $value['name'];
                    }
                }
                $msgName = implode('、', $msgName);
                return parent::apiReturn(204, [], "{$msgName}票属性配置为无需选择游玩日期且无需线上预约，无法开启分时预约。");
            } else if ($returnRes == 1 && $isContinue != 1) {
                return parent::apiReturn(200, ['isContinue' => 2],
                    '开启后立即生效，下单将按照现配置的库存，预约将按照分时配置的库存，不影响已生成的订单，是否开启分时预约？');
            } else if ($returnRes == 2 && $isContinue != 1) {
                return parent::apiReturn(200, ['isContinue' => 2], '开启后立即生效，下单将按照分时配置的库存，不影响已生成的订单，是否开启分时预约？');
            }
        }

        $result = $this->getTimeShareBiz()->openStorageSection($lid, $this->_loginInfo['memberID']);

        if (!$result) {
            return parent::apiReturn(204, [], '开启失败');
        }

        return parent::apiReturn(200, [], '开启成功');

    }

    /**
     * 关闭景区分时预约配置
     * <AUTHOR> Li
     * @date   2020-02-25
     *
     */
    public function closeConf()
    {
        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }
        $lid        = I('post.lid');
        $isContinue = I('post.isContinue', '');
        $hasDeputyLimit = I('post.hasDeputyLimit', 0, 'intval');
        if (empty($lid)) {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        // 判断 期票 开启分时预约 条件
        $TicketApi          = new \Business\JavaApi\Product\Ticket();
        $validTicketInfo    = $TicketApi->queryListingTicketIdAndNameByItemId($lid);
        $otherTicketInfoArr = [];
        if ($validTicketInfo['code'] == 200 && $validTicketInfo['data']) {
            $otherTicketInfoArr = $validTicketInfo['data'];
        }
        //if (!empty($otherTicketInfoArr)) {
        //    $returnRes = $this->checkSectionReserve($otherTicketInfoArr);
        //    if ($returnRes == -1) {
        //        return parent::apiReturn(204, [], '无该场景');
        //    } else if ($returnRes == 1 && $isContinue != 1) {
        //        return parent::apiReturn(200, ['isContinue' => 2],
        //            '关闭后恢复至原下单和预约库存的配置，已售出订单占用库存，请及时调整，避免因库存不足影响下单及预约。是否开启分时预约？');
        //    } else if ($returnRes == 2 && $isContinue != 1) {
        //        return parent::apiReturn(200, ['isContinue' => 2], '关闭后恢复至原库存配置，已售出订单占用库存，请及时调整，避免因库存不足影响下单。是否关闭分时预约？');
        //    }
        //}

        if (!empty($otherTicketInfoArr)) {
            $returnRes = $this->checkSectionReserve($otherTicketInfoArr);

            //附加票分时预约限制处理
            $ticketIds = array_column($otherTicketInfoArr, 'id');
            $ticketDeputyBiz = new TicketDeputyBiz();
            $ticketInfos = $ticketDeputyBiz->queryTicketDeputyInfosMap($ticketIds);

            $msgTwo = 0;
            if (!empty($ticketInfos)) {
                foreach ($ticketInfos as $item) {
                    if ($item['enable_deputy_ticket'] == 1 && $item['enable_deputy_ticket_limit'] != 0) {
                        $msgTwo = 1;
                        break;
                    }
                }
            }

            if ($isContinue != 1) {
                //要再次确认
                return parent::apiReturn(200, ['isContinue' => 2, 'msgOne' => $returnRes, 'msgTwo' => $msgTwo], '');
            }


        }

        $result = $this->getTimeShareBiz()->closeStorageSection($lid, $this->_loginInfo['memberID'], $hasDeputyLimit);
        if ($result['code'] != 200) {
            parent::apiReturn($result['code'], $result['data'], $result['msg']);
        }
        parent::apiReturn(200, [], '关闭成功');
    }

    /**
     * 通过景区id查询是否有开启分时预约
     * <AUTHOR> Li
     * @date   2020-02-26
     *
     */
    public function getConfByLid()
    {
        $lid = I('post.lid', 0, 'intval');
        $tid = I('post.tid', 0, 'intval');

        if (empty($lid)) {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        if (empty($tid)) {
            //兼容景区判断
            $result = $this->getTimeShareBiz()->getConfByLid($lid);

            if (!$result) {
                parent::apiReturn(204, [], '该景区未开启分时预约');
            }
            parent::apiReturn(200, [], '该景区已开启分时预约');
        } else {
            //新增票判断
            $result = $this->getTimeShareBiz()->judgeOpenTimeForTicket((int)$lid, $tid);
            if ($result['code'] != 200 || empty($result['data'])) {
                parent::apiReturn(204, [], '该票未开启分时预约');
            }

            if ($result['data']['is_open'] == 0) {
                parent::apiReturn(204, [], '该票未开启分时预约');
            }
            parent::apiReturn(200, [], '该票已开启分时预约');
        }

    }


    //===========分时预约库存相关=============

    /**
     * 获取分时预约配置详情中规则对应库存使用情况
     * <AUTHOR> Li
     * @date   2020-02-26
     *
     */
    public function getRuleConfDetail()
    {
        $sectionDateId = I('post.section_date_id');
        $playDate      = I('post.play_data');

        if (empty($sectionDateId) || empty($playDate)) {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->querySectionDeductionRecordForDetail($sectionDateId, $playDate);
        if (!$result || empty($result['list'])) {
            parent::apiReturn(204, [], '详情获取失败');
        }

        //列表根据时间段分类
        $list = [];
        foreach ($result['list'] as $value) {
            $key = $value['beginTime'] . '-' . $value['endTime'];
            if (!isset($list[$key])) {
                $list[$key]['beginTime'] = $value['beginTime'];
                $list[$key]['endTime']   = $value['endTime'];
                $list[$key]['beginDate'] = $value['beginDate'];
                $list[$key]['endDate']   = $value['endDate'];
            }
            $list[$key]['sonList'][]       = $value;
            $list[$key]['ticketNum']       = $list[$key]['ticketNum'] + count($value['ticketIds']);
            $list[$key]['soldStorage']     = $list[$key]['soldStorage'] + $value['soldStorage'];
            $list[$key]['totalStorage']    = $list[$key]['totalStorage'] + $value['totalStorage'];
            $list[$key]['reservedStorage'] = $list[$key]['reservedStorage'] + $value['reservedStorage'];
        }

        $list = array_values($list);

        $result['list'] = $list;

        parent::apiReturn(200, $result, '详情获取成功');
    }

    //==============分时预约操作日志=================

    /**
     * 分时预约库存变更日志查询
     * <AUTHOR> Li
     * @date   2020-04-14
     *
     */
    public function querySectionStorageLogList()
    {
        $landId     = I('post.lid', 0, 'intval');
        $operatorId = I('post.operator_id', 0, 'intval');
        $startDate  = I('post.start_date', '', 'strval');
        $endDate    = I('post.end_date', '', 'strval');
        $pageNum    = I('post.page_num', 1, 'intval');
        $pageSize   = I('post.page_size', 10, 'intval');

        if (!strtotime($startDate) || !strtotime($endDate)) {
            $startTime = strtotime(date('Y-m-d') . ' 00:00:00');
            $endTime   = strtotime(date('Y-m-d') . ' 23:59:59');
        } else {
            $startTime = strtotime($startDate . ' 00:00:00');
            $endTime   = strtotime($endDate . ' 23:59:59');
        }

        $result = $this->getTimeShareBiz()->querySectionStorageLogList($this->_loginInfo['sid'], $landId,
            $operatorId, $startTime,
            $endTime, $pageNum, $pageSize);

        parent::apiReturn($result['code'], $result['data'], $result['msg']);
    }

    //==============分时预约管理=============================

    /**
     * 分时预约管理列表
     * <AUTHOR> Li
     * @date   2020-04-15
     *
     */
    public function querySaleLandStorageSectionList()
    {
        $landName   = I('post.land_name', '', 'strval');
        $openStatus = I('post.open_status', 2, 'intval');   //分时预约状态 0 未开启分时预约 1 开启分时预约 2 全部
        $pageNum    = I('post.page_num', 1, 'intval');
        $pageSize   = I('post.page_size', 10, 'intval');

        $result = $this->getTimeShareBiz()->querySaleLandStorageSectionList($this->_loginInfo['sid'], $landName,
            $openStatus, $pageNum, $pageSize);

        if (!$result) {
            parent::apiReturn(204, [], '列表获取失败');
        }
        parent::apiReturn(200, $result, '列表获取成功');
    }

    /**
     * 通过供应商id获取供应商和员工信息列表
     * <AUTHOR> Li
     * @date   2020-04-20
     *
     */
    public function getSupplyAndStaffList()
    {
        $memberRelationBiz = new MemberRelation();
        $staffInfo         = $memberRelationBiz->getMemberStaffListByParentIdToJava($this->_loginInfo['sid'], 'son_id');

        $staffIdArray = array_column($staffInfo, 'son_id');
        array_unshift($staffIdArray, $this->_loginInfo['sid']);

        $memberModel = new \Model\Member\Member();
        $memberInfo  = $memberModel->getMemberByMixed('id', $staffIdArray, 'id, dname, account');
        $this->apiReturn(200, $memberInfo ? array_values($memberInfo) : [], '列表获取成功');
    }

    /**
     * 通过供应商id获取出员工信息列表
     * <AUTHOR> Li
     * @date   2020-04-20
     *
     */
    public function getMemberStaffList()
    {
        $staffName         = I('post.staff_name', '', 'strval');
        $memberRelationBiz = new MemberRelation();
        $staffInfo         = $memberRelationBiz->getMemberStaffListByParentIdToJava($this->_loginInfo['sid'], 'son_id');

        if (empty($staffInfo)) {
            parent::apiReturn(204, [], '员工信息获取失败');
        }

        $staffIdArray = array_column($staffInfo, 'son_id');
        array_unshift($staffIdArray, $this->_loginInfo['sid']);

        $map = [];

        //if ($staffName) {
        //    $map['dname'] = ['like', "%{$staffName}%"];
        //}

        $memberModel = new \Model\Member\Member();
        $memberInfo  = $memberModel->getMemberByMixed('id', $staffIdArray, 'id, dname, account', $map);
        if (empty($memberInfo)) {
            parent::apiReturn(204, [], '用户信息获取失败');
        }

        parent::apiReturn(200, array_values($memberInfo), '列表获取成功');
    }

    /**
     * 通过供应商id获取出自供应景区列表
     * <AUTHOR> Li
     * @date   2020-04-20
     *
     */
    public function getLandInfoList()
    {
        $landName = I('post.land_name', '', 'strval');
        $products = [];

        $evoluteQueryLib = new EvoluteQuery();
        $evoluteInfo     = $evoluteQueryLib->queryLandTitleList($this->_loginInfo['sid'], 1, 100, $landName);
        if ($evoluteInfo['code'] != 200 || empty($evoluteInfo['data']['list'])) {
            $evoluteInfo = [];
        } else {
            $evoluteInfo = $evoluteInfo['data']['list'];
        }

        if (is_array($evoluteInfo) && !empty($evoluteInfo)) {
            foreach ($evoluteInfo as $item) {
                if ($landName) {
                    if (strpos($item['title'], $landName) !== false) {
                        $products[$item['id']] = $item['title'];
                    }
                } else {
                    $products[$item['id']] = $item['title'];
                }
            }
        }

        //产品资源ID登入，订单查询能按票类筛选查询数据
        if (empty($evoluteInfo) && $this->_loginInfo['dtype'] == 2) {
            //$landModel   = new Land('slave');
            //$landInfoDir = $landModel->getLandInfoBySalerId($this->_loginInfo['account'], false, 'id,title');

            $landApi     = new \Business\CommodityCenter\Land();
            $landInfoDir = $landApi->queryLandMultiQueryBySalerid([$this->_loginInfo['account']])[0];
            if ($landInfoDir) {
                if ($landName) {
                    if (strpos($landInfoDir['title'], $landName) !== false) {
                        $products[$landInfoDir['id']] = $landInfoDir['title'];
                    }
                } else {
                    $products[$landInfoDir['id']] = $landInfoDir['title'];
                }
            }
        }


        parent::apiReturn(200, $products, '列表获取成功');
    }

    private function checkSectionReserve($otherTicketInfoArr)
    {
        $returnRes = 0;
        foreach ($otherTicketInfoArr as $k => $v) {
            // preSale  是否需要选择游玩日期 0:需要 1:不需要
            // isOnlineReserve   0：无需线上预约 1：需线上预约
            if ($v['preSale'] == 1 && $v['isOnlineReserve'] == 0) {
                return -1;
            }
            if ($v['preSale'] == 1 && $v['isOnlineReserve'] == 1) {
                $returnRes = 1;
                break;
            }
            if ($v['preSale'] == 0) {
                $returnRes = 2;
                continue;
            }
        }

        return $returnRes;
    }

    /**
     * 指定时间段分时库存生效
     * <AUTHOR>
     * @date   2020-07-23
     *
     */
    public function effectiveConf()
    {
        $lid           = I('post.lid');
        $sectionDateId = I('post.section_date_id');

        if (empty($lid) || empty($sectionDateId)) {
            parent::apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->effectiveConf($lid, $sectionDateId, $this->_loginInfo['memberID']);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取生效确认信息
     * <AUTHOR>
     * @date 2020/7/24
     *
     * @return array
     */
    public function getConfirm()
    {
        $lid = I('post.lid');

        if (empty($lid)) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->getConfirm($lid);

        //if (!$result) {
        //    parent::apiReturn(204, [], '设置失效失败');
        //}
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 票类不启用管理
     * <AUTHOR>
     * @date 2020/7/28
     *
     * @return array
     */
    public function disableManagerList()
    {
        $lid = I('post.lid');
        if (empty($lid)) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->queryTicketOnStatus($lid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 票类不启用管理设置
     * <AUTHOR>
     * @date 2020/7/28
     *
     * @return array
     */
    public function setDisableManager()
    {
        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }
        $lid  = I('post.lid');
        $tids = I('post.tids', []);
        $hasDeputyLimit = I('post.hasDeputyLimit', 0, 'intval');

        if (empty($lid)) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->updateSectionDisableOfTicket($lid, $this->_sid, $tids, $hasDeputyLimit);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 失效设置前置判断
     * <AUTHOR>
     * @date 2020/7/29
     *
     * @return array
     */
    public function invalidConfPre()
    {
        $lid = I('lid', 0, 'intval');//景区ID

        if (empty($lid)) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->queryCountValidStorageSectionConfig($lid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 生效设置前置判断
     * <AUTHOR>
     * @date 2020/7/29
     *
     * @return array
     */
    public function effectiveConfPre()
    {
        $lid       = I('lid', 0, 'intval');//景区ID
        $startDate = I('startDate', '', 'strval,trim');//开始日期[yyyy-MM-dd]
        $endDate   = I('endDate', '', 'strval,trim');//结束日期[yyyy-MM-dd]
        $weekDays  = I('weekDays', '', 'strval,trim');//星期

        if (empty($lid) || empty($startDate) || empty($endDate)) {
            $this->apiReturn(203, [], '缺少必要参数');
        }

        $result = $this->getTimeShareBiz()->queryCountValidStorageSectionConfig($lid, $startDate, $endDate, $weekDays);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据时间段id查询分时配置列表
     */
    public function querySectionStorageListByTimeId()
    {
        $landId   = I('landId', 0, 'intval'); // 景区id （spuId）
        $sectionTimeIdList = I('sectionTimeIdList/a', []); // 分时时间段id

        $result = $this->getTimeShareBiz()->querySectionStorageListByTimeId($landId, $sectionTimeIdList);

        $this->apiReturn(200, $result, '列表获取成功');
    }
}
