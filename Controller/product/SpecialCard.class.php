<?php
/**
 * 特殊卡类型管理
 * User: lanwan<PERSON>
 * Date: 2021/1/18
 * Time: 12:12
 */

namespace Controller\product;

use Library\Controller;
use Business\Product\SpecialCard as SpecialCardBusiness;

class SpecialCard extends Controller
{
    //供应商id int
    private $_memberId ;

    public function __construct()
    {

        $loginInfo         = $this->getLoginInfo();

        $this->_memberId   = $loginInfo['sid'];

    }


    /**
     * 获取供应商已经配置的特殊卡类型
     * User: lanwanhui
     * Date: 2021/01/18
     *
     * @return
     */
    public function getCardTypes()
    {

        $specialCardBiz = new SpecialCardBusiness();

        $rs = $specialCardBiz->getCardTypes($this->_memberId);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }


    /**
     * 获取过闸记录列表
     * User: lanwanhui
     * Date: 2021/01/19
     *
     * @return
     */
    public function getSpecailCardPassrecords()
    {

        $pageSize     = I('page_size', 10, 'intval');   //每页数量
        $page         = I('page', 1, 'intval');         //页数
        $startTime    = I('start_time', '', 'strval');  //开始时间
        $endTime      = I('end_time', '', 'strval');    //结束时间
        $physicsNo    = I('physics_no', '', 'strval');  //卡号
        $cardTypeId   = I('card_type_id', 0, 'intval'); //卡类型id

        $physicsNo = mb_substr($physicsNo, 0, 50);

        if ($pageSize < 1 || $page < 1 || $cardTypeId < 0) {
            $this->apiReturn(204, [], '搜索参数错误');
        }

        $specialCardBiz = new SpecialCardBusiness();

        $rs = $specialCardBiz->getSpecailCardPassrecords(
            $this->_memberId,
            $pageSize,
            $page,
            $startTime,
            $endTime,
            $physicsNo,
            $cardTypeId
        );

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }


    /**
     * 导出过闸记录
     * User: lanwanhui
     * Date: 2021/01/19
     *
     * @return
     */
    public function exportSpecailCardPassrecord()
    {

        $startTime         = I('start_time', '', 'strval');          //开始时间
        $endTime           = I('end_time', '', 'strval');            //结束时间
        $physicsNo         = I('physics_no', '', 'strval');          //卡号
        $cardTypeId        = I('card_type_id', 0, 'intval');        //卡类型id

        $physicsNo = mb_substr($physicsNo, 0, 50);

        if ($cardTypeId < 0) {
            $this->apiReturn(204, [], '搜索参数错误');
        }

        $specialCardBiz = new SpecialCardBusiness();

        return $specialCardBiz->exportSpecailCardPassrecord(
            $this->_memberId,
            $startTime,
            $endTime,
            $physicsNo,
            $cardTypeId
        );

    }

}