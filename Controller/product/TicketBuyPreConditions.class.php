<?php
/**
 * 购票前置信息
 * <AUTHOR>
 * @date 2020/12/1
 */

namespace Controller\Product;

use Business\JavaApi\CommodityCenter\Land;
use Library\Controller;

class TicketBuyPreConditions extends Controller
{
    private $_sid;//上级用户ID
    private $_memberId;//用户ID
    private $_dtype;//当前账号类型
    private $_sdtype;//上级账号类型

    private $_businessLib;
    private $_object;

    private $_time;

    public function __construct()
    {
        parent::__construct();
        $loginInfo          = $this->getLoginInfo();
        $this->_sid         = $loginInfo['sid'];//上级用户ID
        $this->_dtype       = $loginInfo['dtype'];//当前账号类型
        $this->_sdtype      = $loginInfo['sdtype'];//上级账号类型
        $this->_memberId    = $loginInfo['memberID'];//用户ID
        $this->_businessLib = new \Business\JavaApi\Product\TicketBuyPreConditions();

        $array         = [];
        $this->_object = (object)$array;

        $this->_time = time();
    }

    /**
     * 保存购票前置规则
     * <AUTHOR>
     * @date 2020/12/1
     *
     * @return array
     */
    public function saveTicketBuyPreConditionsRule()
    {
        $params               = I('param.');
        $params['memberId']   = $this->_sid;
        $params['operatorId'] = $this->_memberId;
        $params['updateTime'] = $this->_time;
        $params['createTime'] = $this->_time;
        $javaResult           = $this->_businessLib->saveTicketBuyPreConditionsRule($params);
        $this->apiReturn($javaResult['code'], $javaResult['data'], $javaResult['msg']);
    }

    /**
     * 删除购票前置规则
     * <AUTHOR>
     * @date 2020/12/1
     *
     * @return array
     */
    public function deleteTicketBuyPreConditionsRule()
    {
        $ruleId = I('ruleId', 0, 'intval');//规则id
        if (empty($ruleId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数缺失');
        }
        $javaResult = $this->_businessLib->deleteTicketBuyPreConditionsRule($ruleId);
        $this->apiReturn($javaResult['code'], $javaResult['data'], $javaResult['msg']);
    }

    /**
     * 修改购票前置规则状态
     * <AUTHOR>
     * @date 2020/12/1
     *
     * @return array
     */
    public function changePreConditionsRuleStatus()
    {
        $params               = I('param.');
        $params['operatorId'] = $this->_memberId;
        $javaResult           = $this->_businessLib->changePreConditionsRuleStatus($params);
        $this->apiReturn($javaResult['code'], $javaResult['data'], $javaResult['msg']);
    }

    /**
     * 根据门票id查询购票前置规则
     * <AUTHOR>
     * @date 2020/12/1
     *
     * @return array
     */
    public function queryTicketBuyPreConditionsRuleByTicketId()
    {
        $ticketId   = I('ticketId', 0, 'intval');//门票id
        $ruleStatus = I('ruleStatus', null);//门票id

        if (empty($ticketId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数缺失');
        }

        if ($ruleStatus !== null) {
            $ruleStatus = intval($ruleStatus);
        }

        $javaResult = $this->_businessLib->queryTicketBuyPreConditionsRuleByTicketId($ticketId, $ruleStatus);
        $this->apiReturn($javaResult['code'], $javaResult['data'], $javaResult['msg']);
    }

    /**
     * 根据条件查询购票前置规则
     * <AUTHOR>
     * @date 2020/12/1
     *
     * @return array
     */
    public function queryTicketBuyPreConditionsRuleById()
    {
        $ruleId     = I('ruleId', null);
        $memberId   = I('memberId', null);
        $landId     = I('landId', null);
        $ticketId   = I('ticketId', null);
        $ruleStatus = I('ruleStatus', null);

        if ($ruleId !== null) {
            $ruleId = intval($ruleId);
        }

        if ($memberId !== null) {
            $memberId = intval($memberId);
        }

        if ($landId !== null) {
            $landId = intval($landId);
        }

        if ($ticketId !== null) {
            $ticketId = intval($ticketId);
        }

        if ($ruleStatus !== null) {
            $ruleStatus = intval($ruleStatus);
        }

        $javaResult = $this->_businessLib->queryTicketBuyPreConditionsRuleById($ruleId, $memberId, $landId, $ticketId,
            $ruleStatus);
        $this->apiReturn($javaResult['code'], $javaResult['data'], $javaResult['msg']);
    }

    /**
     * 分页查询购票前置规则
     * <AUTHOR>
     * @date 2020/12/1
     *
     * @return array
     */
    public function queryTicketBuyPreConditionsRuleByPaging()
    {
        $ruleId     = I('ruleId', null);
        //$memberId   = I('memberId', null);
        $landId     = I('landId', null);
        $ticketId   = I('ticketId', null);
        $ruleStatus = I('ruleStatus', null);
        $pageNum    = I('pageNum', null);
        $pageSize   = I('pageSize', null);

        $memberId = $this->_sid;

        if ($ruleId !== null) {
            $ruleId = intval($ruleId);
        }

        if ($landId !== null) {
            $landId = intval($landId);
        }

        if ($ticketId !== null) {
            $ticketId = intval($ticketId);
        }

        if ($ruleStatus !== null) {
            $ruleStatus = intval($ruleStatus);
        }

        if ($pageNum !== null) {
            $pageNum = intval($pageNum);
        }

        if ($pageSize !== null) {
            $pageSize = intval($pageSize);
        }

        $javaResult = $this->_businessLib->queryTicketBuyPreConditionsRuleByPaging($ruleId, $memberId, $landId,
            $ticketId, $ruleStatus, $pageNum, $pageSize);
        $this->apiReturn($javaResult['code'], $javaResult['data'], $javaResult['msg']);
    }

    /**
     * 根据规则id或票id查询对应的前置项
     * <AUTHOR>
     * @date 2020/12/1
     *
     * @return array
     */
    public function queryTicketBuyPreConditionsItemByRuleId()
    {
        $ticketId   = I('ticketId', null);
        $itemStatus = I('itemStatus', null);

        if ($ticketId === null || $itemStatus === null) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数缺失');
        }

        $ticketId   = intval($ticketId);
        $itemStatus = intval($itemStatus);

        $javaResult = $this->_businessLib->queryTicketBuyPreConditionsItemByRuleId($ticketId, $itemStatus);
        $this->apiReturn($javaResult['code'], $javaResult['data'], $javaResult['msg']);
    }

    /**
     * 景区搜索下拉查询
     * <AUTHOR>
     * @date 2020/12/1
     *
     * @return array
     */
    public function searchConditionByLand()
    {
        $title  = I('title', null);
        $pTypes = I('pTypes', null);

        if ($pTypes !== null && !is_array($pTypes)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], 'pTypes参数有误');
        }

        $likeLetters = null;
        if ($title !== null) {
            if (mb_strlen($title) == strlen($title)) {
                //英文
                $likeLetters = $title;
                $lTitle      = null;
            }
        }

        $applyDid = $this->_sid;

        $javaApi = new Land();
        $result  = $javaApi->queryLandMultiQueryByAdminAndPaging($title, $likeLetters, null, null, null, 1, 100, null,
            null, [$applyDid], [1], null, $pTypes);

        if (empty($result['data']['list'])) {
            $this->apiReturn(204, [], '未找到相关数据');
        }

        $dataRes = [];
        foreach ($result['data']['list'] as $value) {
            $dataRes[$value['id']] = $value['title'];
        }

        $this->apiReturn($result['code'], $dataRes, $result['msg']);
    }

    /**
     * 票搜索下来查询
     * <AUTHOR>
     * @date 2020/12/1
     *
     * @return array
     */
    public function searchConditionByTicket()
    {
        $title  = I('title', null);
        $lid    = I('land_id', 0);
        //$pTypes = I('pTypes', null);

        if (empty($lid)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], 'land_id参数有误');
        }

        //if ($pTypes !== null && !is_array($pTypes)) {
        //    $this->apiReturn(self::CODE_PARAM_ERROR, [], 'pTypes参数有误');
        //}

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByApplyDidAndLandIdAndPTypeAndApplyLimitAndPStatusAndStatus(null,
            [$lid], 'id,title', '', '', '', null, null, null, null, null, $title, 1, 100);

        if (empty($ticketArr['data']['list'])) {
            $this->apiReturn(self::CODE_SUCCESS, [], '');
        }

        $evoluteInfo = array_column($ticketArr['data']['list'], 'ticket');

        $data = [];
        if (!empty($evoluteInfo)) {
            foreach ($evoluteInfo as $item) {
                $data[$item['id']] = $item['title'];
            }
        }

        $this->apiReturn(self::CODE_SUCCESS, $data, '');
    }
}