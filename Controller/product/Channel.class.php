<?php
/**
 * 渠道相关操作
 *
 * <AUTHOR>
 * @time   2016-11-29
 */

namespace Controller\product;

use Business\JavaApi\TicketApi;
use Business\NewJavaApi\Distribution\DistributionChannelHandle;
use Library\Controller;
use Model\Member\Member;
use Model\Product\Ticket;
use Library\Tools\Helpers;
use Business\JavaApi\ProductApi;

class Channel extends Controller
{
    private $_sid          = 0;
    private $_loginInfo    = null;
    private $_taskStartNum = 100; // 启动任务的最低数量

    // 统一销售渠道名称
    private $_channel_map   = [];
    private $annual_channel = [
        5  => '分销后台',
        12 => '计调下单',
        7  => '散客窗口',
        9  => '团队窗口',
        1  => '微商城',
        20 => '小程序',
    ];

    public function __construct()
    {
        $this->_sid       = $this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);

        $channelConfig = load_config('sale_channel');

        if (!$channelConfig) {
            $this->apiReturn(204, '渠道配置加载失败');
        }

        $this->_channel_map = $channelConfig;
    }

    /**
     * 设置批量任务
     * <AUTHOR>
     * @time   2016-11-29
     *
     * @param  array channel 渠道数组 [1, 2, 3, ...]
     */
    public function setBatChannelTask()
    {
        if (!$this->isPost()) {
            $this->apiReturn(601, [], '非法访问');
        }

        if (!is_array(I('post.channel')) || empty(I('post.channel'))) {
            $this->apiReturn(601, [], '请您至少选择一个渠道');
        } else {
            $channel = implode(',', I('post.channel'));
        }

        $result = ProductApi::batchSetChannels($this->_sid, $channel, $this->_loginInfo['memberID']);

        if ($result) {
            $this->apiReturn(200, [], '设置成功');
        } else {
            $this->apiReturn(200, [], '设置失败');
        }
    }

    /**
     * 任务进度查询
     * <AUTHOR>
     * @time   2016-12-01
     */
    public function getTaskSchedule()
    {

        if (!$this->isPost()) {
            $this->apiReturn(601, [], '非法访问');
        }

        $taskID = I('post.id', 0, 'intval');
        if (!$taskID) {
            $this->apiReturn(601, [], '请指明任务');
        }

        $task = (new \Model\Product\Task())
            ->getUserTask(['id' => $taskID], 'ratio', 1);

        if ($task) {
            $this->apiReturn(200,
                [
                    'ratio' => $task[0]['ratio'],
                ],
                '已完成进度' . $task[0]['ratio'] . '%'
            );

        } else {
            $this->apiReturn(601, [], '您的渠道设置任务已完成或者您还未设置任务');
        }
    }

    /**
     * 获取所有可出售产品
     * <AUTHOR>
     * @date   2016-11-30
     *
     * @param  int  $sid  用户ID
     * @param  bool  $onlyResetKey  商品筛选 [true重置key值|默认false重置key值并简化数据]
     *
     * @return array  可销商品数组
     */
    public function getAllSaleProduct($sid = 0, $onlyResetKey = false)
    {
        if (intval($sid) <= 0) {
            return -1;
        }

        $ticketModel     = new \Model\Product\Ticket();
        $saleProducts    = $ticketModel->getSaleProducts($sid);
        $saleDisProducts = $ticketModel->getSaleDisProductsByFidSourceIdStatus($sid);
        $allPro          = array_merge($saleProducts, $saleDisProducts);
        $proArr          = [];
        foreach ($allPro as $pro) {
            $key          = $pro['apply_sid'] . '_' . $pro['pid'];
            $proArr[$key] = $onlyResetKey ? $pro : [
                'aid' => $pro['apply_sid'],
                'pid' => $pro['pid'],
                'lid' => $pro['landid'],
            ];
        }

        return $proArr;
    }

    /**
     * 销售渠道产品列表
     *
     * 维护时间
     * @date   2018-08-10
     * <AUTHOR>
     * <AUTHOR>
     *
     * @return [type] [description]
     */
    public function getProductList()
    {
        $page       = I('page', 1, 'intval');      //当前页码
        $pageSize   = I('pageSize', 8, 'intval');  //每页条数
        $title      = I('title', '');              //景区名称
        $supplier   = I('supplier', '');           //供应商名称
        $province   = I('province', '');           //省份
        $city       = I('city', '');               //城市
        $oversea    = I('oversea', '');               //境内外
        $channelId  = I('channelId', 0, 'intval');//销售渠道
        $tid        = I('tid', 0, 'intval');//门票ID
        $type       = I('type', '', 'strval');//产品类型
        $ticketSize = I('ticketSize', 10, 'intval');  //票的每页条数
        $showPrice  = I('showPrice', 0, 'intval');  //是否返回结算价 0=不显示 1=显示

        if ($showPrice != 0 && $showPrice != 1) {
            $this->apiReturn(203, [], '参数错误');
        }

        $isShow = false;
        if ($showPrice == 1) {
            $isShow = true;
        }

        //请求java接口
        $result = ProductApi::getProListForChannel(
            $this->_sid,
            $page,
            $pageSize,
            $title,
            $supplier,
            $province,
            $city,
            $oversea,
            $channelId,
            $tid,
            $type,
            $isShow
        );

        //处理返回数据 尽量和重构前一样
        $return = [];
        if (!empty($result['data'])) {
            $return = [
                'list'      => $result['data']['lists'] ?: [],
                'map'       => $this->_channel_map,
                'total'     => $result['data']['total'] ?: 0,
                'totalPage' => $result['data']['pages'] ?: 0,
                'page'      => $result['data']['pageNum'] ?: 0,
                'pageSize'  => $result['data']['pageSize'] ?: 0,

            ];

            $javaApi = new \Business\CommodityCenter\Land();
            //票分页处理
            foreach ($return['list'] as &$value) {
                $idArray = [];
                $lidArr  = array_filter(array_unique(array_column($value['list'], 'lid')));
                $landInfo = $javaApi->queryLandInfoByIds($lidArr);
                $landMap  = [];
                if ($landInfo) {
                    $landMap = array_column($landInfo, 'pType', 'id');
                }

                foreach ($value['list'] as &$tmp) {
                    $idArray[] = [
                        'id'  => $tmp['id'],
                        'pid' => $tmp['pid'],
                    ];

                    //年卡销售渠道特殊处理
                    if (isset($landMap[$tmp['lid']]) && $landMap[$tmp['lid']] == 'I') {
                        $ticketShopArr = explode(',', $tmp['ticketShop']);
                        $channelArr    = explode(',', $tmp['channelId']);
                        $tmpShop       = [];
                        $tmpChannel    = [];
                        $channelMap    = [];
                        foreach ($channelArr as $idx => $channel) {
                            if (in_array($channel, array_keys($this->annual_channel))) {
                                $tmpChannel[] = $channel;
                                $channelMap[] = strval($this->annual_channel[$channel]);
                            }
                        }

                        foreach ($ticketShopArr as $idx => $shop) {
                            if (in_array($shop, array_keys($this->annual_channel))) {
                                $tmpShop[] = $shop;
                            }
                        }
                        $tmp['channel']  = implode(',', $channelMap);
                        $tmp['channelId']  = implode(',', $tmpChannel);
                        $tmp['ticketShop'] = implode(',', $tmpShop);
                    }
                }
                if (!empty($idArray) && !empty($value['ids'])) {
                    $mergeTmp = array_merge($idArray, $value['ids']);
                    $idsNew   = [];
                    $keyNew   = 0;
                    foreach ($mergeTmp as $val) {
                        $idsNew[$keyNew][] = $val;
                        if (count($idsNew[$keyNew]) >= $ticketSize) {
                            $keyNew++;
                        }
                    }

                    $value['ids'] = $idsNew;
                }
            }

        }

        $this->apiReturn($result['code'], $return, $result['msg']);

    }

    /**
     * 获取更多的销售渠道
     * <AUTHOR>
     * @date 2020/9/8
     *
     * @return string | json
     */
    public function getMoreList()
    {
        $ids       = I('ids', '', 'strval');//销售渠道ID数组逗号分隔
        $showPrice = I('showPrice', 0, 'intval');  //是否返回结算价 0=不显示 1=显示

        if ($showPrice != 0 && $showPrice != 1) {
            $this->apiReturn(203, [], '参数错误');
        }

        if (empty($ids)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (is_array($ids)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $isShow = false;
        if ($showPrice == 1) {
            $isShow = true;
        }

        $return = ['list' => []];

        $idsArray = explode(',', $ids);

        /*$channelLib = new \Business\Product\Channel();
        $result     = $channelLib->getMoreListForChannel($this->_sid, $idsArray, $isShow);*/

        $channelHandle = new DistributionChannelHandle();
        $result = $channelHandle->queryChannelListMoreData($idsArray, $this->_sid, $isShow);
        if (isset($result['data']['list'])){
            $result['data'] = $result['data']['list'];
        }
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        if (empty($result['data'])) {
            $this->apiReturn(200, $return, '为空');
        }

        $return['list'] = $result['data'];

        $this->apiReturn(200, $return, '获取成功');
    }

    /**
     * 销售渠道产品列表
     * @date   2020-03-30
     * <AUTHOR>
     *
     * @return string | json
     */
    public function getProductListNew()
    {
        $page      = I('page', 1, 'intval');      //当前页码
        $pageSize  = I('pageSize', 8, 'intval');  //每页条数
        $title     = I('title', '', 'strval');              //景区名称
        $supplier  = I('supplier', '', 'strval');           //供应商名称
        $province  = I('province', 0, 'intval');           //省份
        $city      = I('city', 0, 'intval');               //城市
        $oversea   = I('oversea', 0, 'intval');               //境内外
        $channelId = I('channelId', 0, 'intval');//销售渠道
        $pid = I('pid', 0, 'intval');//门票PID

        // 参数类型严格规定，不然传值就报错了 TODO

        $evoluteChannelBiz = new \Business\Product\Get\EvoluteChannelHandle();
        $result            = $evoluteChannelBiz->getChannelList($this->_sid, $title, $supplier, $oversea, $province,
            $city, $channelId, $pid, $page, $pageSize);

        //处理返回数据 尽量和重构前一样
        $return = [];
        if (!empty($result['data'])) {
            if (empty($result['data']['total'])) {
                $totalPate = 0;
            } else {
                $totalPate = ceil($result['data']['total'] / $pageSize);
            }

            $returnList = [];
            if (!empty($result['data']['list'])) {
                foreach ($result['data']['list'] as $key => $item) {
                    $returnList[$key]        = $item;
                    $returnList[$key]['pxx'] = $item['px'];
                }
            }

            $return = [
                'list'      => $returnList,
                'map'       => $this->_channel_map,
                'total'     => $result['data']['total'] ?: 0,
                'totalPage' => $totalPate,
                'page'      => $page ?: 0,
                'pageSize'  => $pageSize ?: 0,
            ];
        }

        $this->apiReturn($result['code'], $return, $result['msg']);
    }

    /**
     * 设置产品的排序 px
     *
     * @param   $lid    int 产品id
     * @param   $setVal int 推荐值
     */
    public function setProductSort()
    {

        $lid    = I('lid', 0, 'intval');
        $setVal = I('setVal', 0, 'intval');

        if (!$lid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('channelConf',
            $this->_loginInfo['saccount']);
        if ($vacationMode === false) {
            $this->apiReturn(403, [], '当前处于假日模式，该功能被限制使用');
        }

        if ($setVal > 65535) {
            $this->apiReturn(204, [], '推荐值不能大于65535');
        }

        //$result = ProductApi::setProductSort($this->_sid, $lid, $setVal, $this->_loginInfo['memberID']);
        $evoluteChannelBiz = new \Business\Product\Update\EvoluteChannelHandle();
        $result            = $evoluteChannelBiz->landSortConfig($lid, $this->_sid, $setVal,
            $this->_loginInfo['memberID']);

        // 接入设置推荐级别
        //if ($result['code'] == 200) {
        //
        //}

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '位置错误');
        }
    }

    /**
     * 设置票类的排序 tx(待优化)
     *
     * @param  array @sortList [['id-aid'], 'id2-aid2']
     */
    public function setTicketSort()
    {

        $sortList      = I('sortList');
        $sortListByPid = I('sortListByPid');

        if (!is_array($sortList) || !is_array($sortListByPid)) {
            $this->apiReturn(204, [], '参数格式错误');
        }

        $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('channelConf',
            $this->_loginInfo['saccount']);
        if ($vacationMode === false) {
            $this->apiReturn(403, [], '当前处于假日模式，该功能被限制使用');
        }

        $setMapping = [];
        $count      = count($sortList);
        foreach ($sortList as $tx => $map) {
            $setMapping[] = [
                'id'          => intval($map),
                'ticket_sort' => $count - $tx,
            ];
        }

        // 新版门票排序需要的数组
        $setMappingByPid = [];
        foreach ($sortListByPid as $tx => $map) {
            $setMappingByPid[] = [
                'id'         => intval($sortList[$tx]),
                'pid'        => intval($map),
                'ticketSort' => $count - $tx,
            ];
        }

        //$result = ProductApi::setTicketSort($this->_sid, $setMapping, $this->_loginInfo['memberID']);

        $evoluteChannelBiz = new \Business\Product\Update\EvoluteChannelHandle();
        $result            = $evoluteChannelBiz->ticketSortConfig($this->_sid, $setMappingByPid,
            $this->_loginInfo['memberID']);
        // 配置新版门票排序
        //if ($result['code'] == 200) {
        //
        //}

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '位置错误');
        }
    }

    /**
     * 配置销售渠道
     *
     * @param  array  $setMapping  [{id : '1,3,4'}, 'id' : '2,3']
     * @param  array  $setMappingByPid  [{pid : '1,3,4'}, 'id' : '2,3']
     */
    public function setChannel()
    {

        $setMapping      = I('setMapping');
        $setMappingByPid = I('setMappingByPid');

        if (!is_array($setMapping) || count($setMapping) < 1 || count($setMappingByPid) < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $EvoModel = new \Model\Product\Evolute();

        $idArr    = array_keys($setMapping);
        $channels = array_values($setMapping)[0];

        if (count($idArr) > 1) {
            $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('channelConf',
                $this->_loginInfo['saccount']);
            if ($vacationMode === false) {
                $this->apiReturn(403, [], '当前处于假日模式，该功能被限制使用');
            }
        }

        //开启事务
        $EvoModel->startTrans();

        //$result = ProductApi::setChannel($this->_sid, $idArr, $channels, $this->_loginInfo['memberID']);

        // 设置销售渠道
        $pidArr           = array_keys($setMappingByPid);
        $updateChannelBiz = new \Business\Product\Update\EvoluteChannelHandle();
        $result           = $updateChannelBiz->batchConfigChannel($pidArr, $this->_sid, $channels,
            $this->_loginInfo['memberID'], $idArr);

        //通知分销专员
        \Business\MultiDist\Product::pushChannelConfig($this->_sid, $pidArr, $channels);


        //渠道名称
        $intersect   = explode(',', $channels);
        $channelName = [];
        foreach ($intersect as $id) {
            $channelName[$id] = $this->_channel_map[$id];
        }

        if (isset($result['code'])) {
            if ($result['code']) {
                $result['data']                = $setMapping;
                $result['data']['channelName'] = $channelName;
            }
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '未知错误');
        }
    }

    /**
     * 获取渠道名称
     * <AUTHOR>
     * @date   2017-1-17
     *
     * @param  bool  $return  true=ajax返回|false=返回数组
     *
     * @return array 销售渠道名称映射表
     */
    public function getChannelMap($return = true)
    {
        if (!$return) {
            return $this->_channel_map;
        } else {
            $this->apiReturn(200, [$this->_channel_map]);
        }
    }

    /**
     * 获取渠道名称 -- 美团PMS新增接口 -- zhangyz -- 2019-11-24
     * 直接按配置中的顺序输出到前端，会按照key值重新排序一遍
     * 这边希望是按照当前配置中的顺序输出给前端
     * 做个处理，弄成二维数组的形式输出给前端，把key值放到数组ID中
     *
     * Create by zhangyangzhen
     * Date: 2019/11/24
     * Time: 14:49
     */
    public function getChannelMapV2()
    {
        $channelArr = [];
        foreach ($this->_channel_map as $key => $channel) {
            $channelArr[] = [
                'id'   => (string)$key,
                'name' => $channel,
            ];
        }

        $this->apiReturn(self::CODE_SUCCESS, $channelArr, 'success');
    }

    /**
     * 获取自动设置状态
     *
     * <AUTHOR>
     * @date   2017-05-09
     */
    public function getDefaultSet()
    {
        $sid = parent::isLogin('ajax');

        $getRes = ProductApi::getDefaultChannel($this->_sid);

        if ($getRes['code'] == 200) {

            $shop = $getRes['data']['shop'];
            $this->apiReturn(200, ['shop' => $shop]);

        } else {

            $this->apiReturn($getRes['code'], [], $getRes['msg']);
        }

    }

    /**
     * 开启新产品默认设置渠道
     *
     * <AUTHOR>
     * @date   2017-05-08
     */
    public function saveDefaultSet()
    {
        $sid = parent::isLogin('ajax');

        //默认渠道id
        $ids = I('ids', '');

        if ($ids) {
            $idArr = explode(',', $ids);
            if (array_diff($idArr, array_keys($this->_channel_map))) {
                $this->apiReturn(204, [], '渠道值非法');
            }
        }

        $setRes = ProductApi::setDefaultChannel($this->_sid, $ids, $this->_loginInfo['memberID']);

        if ($setRes['code'] == 200) {
            $this->apiReturn(200, [], '设置成功');
        } else {
            $this->apiReturn($setRes['code'], [], $setRes['msg']);
        }
    }

    /**
     * 批量获取渠道结算价
     *
     * <AUTHOR> Li
     * @date   2019-12-12
     */
    public function getChannelSettlementPrice()
    {
        $params   = I('post.params');
        $paramArr = [];
        $date     = date('Y-m-d');

        foreach ($params as $item) {
            $paramArr[] = [
                'ticketId' => $item['ticketId'],
                'fid'      => $item['fid'],
                'sid'      => $item['sid'],
                'date'     => $date,
                'check'    => true,
            ];
        }

        $javaApi = new \Business\JavaApi\Ticket\Price();
        $result  = $javaApi->getActualtimePriceBatch($paramArr);

        //$javaApi = new TicketApi();
        //$result  = $javaApi->batchSettlementQuery($paramArr);

        if ($result['code'] !== 200 || empty($result['data'])) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        //拼接特定格式返回前端
        $return = [];
        foreach ($result['data'] as $item) {
            if ((!isset($item['fid']) || empty($item['fid'])) || (!isset($item['sid']) || empty($item['sid'])) || (!isset($item['ticketId']) || empty($item['ticketId']))) {
                continue;
            }

            $return["{$item['fid']}_{$item['sid']}_{$item['ticketId']}"] = $item['costPrice'];
        }

        $this->apiReturn(200, $return, '获取成功');
    }

    /**
     * 分销链渠道配置列表查询 -- 获取景区下的门票
     * @date   2021-11-05
     * <AUTHOR>
     *
     */
    public function queryChannelTicketList()
    {
        $page         = I('page', 1, 'intval');      //当前页码
        $pageSize     = I('page_size', 3, 'intval');  //每页条数
        $landId       = I('land_id', 0, 'intval');//景区id
        $showPrice    = I('show_price', 0, 'intval');//是否显示结算价 默认值：0
        $orderByPrice = I('order_by_price', 0, 'intval');//是否按结算价排序 默认值：0
        $channel      = I('channel', 0, 'intval');//渠道
        $supplierName = I('supplier_name', '', 'strval,trim');//供应商名称

        $evoluteChannelBiz = new \Business\Product\Get\EvoluteChannelHandle();
        $result            = $evoluteChannelBiz->queryChannelTicketList($this->_sid, $landId, $channel, $supplierName, $showPrice, $orderByPrice,
            $page, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 分销链渠道配置列表查询 -- 获取景区产品列表
     * @date   2021-11-12
     * <AUTHOR>
     */
    public function queryChannelLandListByPaging()
    {
        $page         = I('page', 1, 'intval');      //当前页码
        $pageSize     = I('page_size', 8, 'intval');  //每页条数
        $province     = I('province', 0, 'intval');           //省份
        $city         = I('city', 0, 'intval');               //城市
        $oversea      = I('oversea', 0, 'intval');               //境内外
        $channelId    = I('channel_id', 0, 'intval');//销售渠道
        $pid          = I('pid', 0, 'intval');//门票PID
        $pType        = I('p_type', '', 'strval');
        $landName     = I('land_name', '', 'strval');
        $supplierName = I('supplier_name', '', 'strval');
        $showPrice    = I('show_price', 0, 'intval');
        $orderByPrice = I('order_by_price', 0, 'intval');

        // 参数类型严格规定，不然传值就报错了 TODO

        $evoluteChannelBiz = new \Business\Product\Get\EvoluteChannelHandle();
        $result            = $evoluteChannelBiz->queryChannelLandList($this->_sid, $oversea, $city, $province, $pid,
            $pType, $landName, $channelId, $supplierName, $showPrice, $orderByPrice, $page, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}
