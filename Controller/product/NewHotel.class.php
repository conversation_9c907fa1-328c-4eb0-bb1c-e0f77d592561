<?php

namespace Controller\Product;

use Business\Hotel\Intranet as HotelIntranetApi;
use Library\Controller;
use Throwable;

class NewHotel extends Controller
{
    private $memberId;
    private $isSuper;
    private $sid;

    public function __construct()
    {
        parent::__construct();

        $this->isLogin('ajax');

        $loginInfo = $this->getLoginInfo();

        $this->memberId = $loginInfo['memberID'];
        $this->isSuper = $this->isSuper();
        $this->sid = $loginInfo['sid'];
    }

    /**
     * 酒店房型产品列表
     */
    public function roomProductList(): void
    {
        $ratePlanStatus = I('status', 1);
        $centrePoiId = I('poi_id', 0, 'intval');
        $name = I('name', '', 'trim');
        $page = I('page', 1, 'intval');
        $pageSize = I('page_size', 10, 'intval');

        $sid = $this->isSuper ? null : $this->sid;

        try {
            $data = (new HotelIntranetApi\RoomProduct())->paginationByName($centrePoiId, $sid, $name, null,
                $ratePlanStatus, $page, $pageSize);
        } catch (Throwable $e) {
            $this->apiReturn(400, [], $e->getMessage());
            return;
        }

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 酒店价格计划列表
     */
    public function ratePlanList(): void
    {
        $centrePoiId = I('poi_id', 0, 'intval');
        $centreSpuId = I('spu_id', 0, 'intval');
        $ratePlanCode = I('rate_plan_code', '', 'trim');
        $status = I('status', 1);

        $sid = $this->isSuper ? null : $this->sid;
        $centreSkuId = 0;
        if ($ratePlanCode) {
            $arr = explode('|', $ratePlanCode);
            if (count($arr) != 3) {
                $this->apiReturn(200);
            }
            [$aid, $centreSpuId, $centreSkuId] = $arr;
            if ($this->isSuper) {
                $sid = $aid;
            }
        }
        $name = I('name', '', 'trim');
        $page = I('page', 1, 'intval');
        $pageSize = I('page_size', 10, 'intval');

        try {
            $data = (new HotelIntranetApi\RatePlan())->paginationByName($centrePoiId, $centreSpuId, $centreSkuId, $sid,
                $name, $status, $page, $pageSize);
        } catch (Throwable $e) {
            $this->apiReturn(400, [], $e->getMessage());
            return;
        }

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 酒店产品批量删除/恢复-包含价格计划-根据PoiID
     */
    public function batchModifyRoomProductDeletionStatus(): void
    {
        $centrePoiId = I('poi_id', 0, 'intval');
        $deletionStatus = I('deletion_status', 0, 'intval');

        try {
            $data = (new HotelIntranetApi\RoomProduct())->batchModifyDeletionStatusWithRatePlanByCentrePoiId(
                $centrePoiId, $deletionStatus, $this->memberId);
        } catch (Throwable $e) {
            $this->apiReturn(400, [], $e->getMessage());
            return;
        }

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 酒店价格计划停售/起售
     */
    public function modifyRatePlanStatus(): void
    {
        $centreSkuId = I('sku_id', 0, 'intval');
        $status = I('status', 0, 'intval');

        try {
            $data = (new HotelIntranetApi\RatePlan())->modifyStatus($centreSkuId, $status, $this->memberId);
        } catch (Throwable $e) {
            $this->apiReturn(400, [], $e->getMessage());
            return;
        }

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 酒店价格计划删除/恢复
     */
    public function modifyRatePlanDeletionStatus(): void
    {
        $centreSkuId = I('sku_id', 0, 'intval');
        $deletionStatus = I('deletion_status', 0, 'intval');

        try {
            $data = (new HotelIntranetApi\RatePlan())->modifyDeletionStatus($centreSkuId, $deletionStatus, $this->memberId);
        } catch (Throwable $e) {
            $this->apiReturn(400, [], $e->getMessage());
            return;
        }

        $this->apiReturn(200, $data, 'success');
    }
}