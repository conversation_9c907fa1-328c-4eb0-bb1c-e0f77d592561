<?php
/**
 * 酒店产品接口
 * <AUTHOR>
 * @date 2020/7/1
 */

namespace Controller\Product;

use Business\Authority\DataAuthLogic;
use Business\MemberLogin\MemberLoginHelper;
use Business\Product\Hotel as HotelLib;
use Library\Controller;

class Hotel extends Controller
{
    private $_sid;//上级用户ID
    private $_memberId;//用户ID
    private $_dtype;//当前账号类型
    private $_sdtype;//上级账号类型

    private $_businessLib;
    private $_object;

    public function __construct()
    {
        parent::__construct();
        $loginInfo          = $this->getLoginInfo();
        $this->_sid         = $loginInfo['sid'];//上级用户ID
        $this->_dtype       = $loginInfo['dtype'];//当前账号类型
        $this->_sdtype      = $loginInfo['sdtype'];//上级账号类型
        $this->_memberId    = $loginInfo['memberID'];//用户ID
        $this->_businessLib = new HotelLib();

        $array         = [];
        $this->_object = (object)$array;
    }

    /**
     * 新增/修改酒店产品
     * <AUTHOR>
     * @date 2020/7/1
     *
     * @return array
     */
    public function saveProduct()
    {
        $proId        = I('lastid', 0, 'intval');//编辑时用到ID
        $ptype        = I('ptype', 'C', 'strval');//产品类型
        $province     = I('d_province', 0, 'intval');//省份代码
        $city         = I('d_city', 0, 'intval');//城市代码
        $oversea      = I('d_oversea', 0, 'intval');//境内外 0=境内 1=境外
        $title        = I('mainTitle', '', 'strval,trim');//产品名称
        $notice       = I('buyTips', '', 'strval,trim');//预定须知
        $details      = I('detailInfo', '', 'strval,trim');//景点详情
        $thumbImg     = I('thumb_img', '', 'strval');//景点缩略图
        $address      = I('mainAddress', '', 'strval,trim');//景点详细地址
        $level        = I('jtype', '非A级', 'strval');//景点级别
        $device       = I('device', '', 'strval');//酒店设施 逗号隔开
        $service      = I('service', '', 'strval');//酒店服务 逗号隔开
        $tel          = I('mainPhoneNum', '', 'strval,trim');//联系电话
        $lngLatPos    = I('lngLatPos', '0,0', 'strval');//经纬度 逗号隔开
        $traffic      = I('trafficInfo', '', 'strval,trim');//公共交通
        $resourceId   = I('resource_id', 0, 'intval');//资源ID
        $detailImg    = I('detail_img');//轮播图
        $firstPicture = I('first_picture', '', 'strval');//首图
        $videoUrl     = I('videoUrl', '', 'strval');//视频

        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '无发布酒店类型产品权限');
        }
        if ($ptype != 'C') {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数有误');
        }
        if (empty($province) || empty($city)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请选择省份城市');
        }
        if (empty($title)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写酒店名称');
        }
        if (empty($notice)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写预定须知');
        }
        if (mb_strlen($notice, 'utf-8') > 2000) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '预定须知超出2000个字符');
        }
        if (empty($details)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写产品详情');
        }
        if (empty($thumbImg)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请上传产品缩略图');
        }
        if (empty($address)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写产品详细地址');
        }
        if (empty($tel)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写联系电话');
        }
        if (empty($lngLatPos)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请选择经纬度');
        }

        $params = [
            'id'         => $proId,//编辑时用到ID
            'pType'      => $ptype,//产品类型
            'provinceId' => $province,//省份代码
            'address'    => $address,//景点详细地址
            'cityId'     => $city,//城市代码
            'oversea'    => $oversea,//境内外 0=境内 1=境外
            'title'      => $title,//产品名称
            'jtype'      => $level,//景点级别
            'jqts'       => $notice,//预定须知
            'bhjq'       => $details,//景点详情
            'imgpath'    => $thumbImg,//景点缩略图
            'device'     => $device,//酒店设施
            'service'    => $service,//酒店服务
            'tel'        => $tel,//联系电话
            'lngLatPos'  => $lngLatPos,//经纬度 逗号隔开
            'resourceId' => $resourceId,//资源ID
            'applyDid'   => $this->_sid,//当前上级ID
            'operaterId' => $this->_memberId,//当前用户ID
            'jtzn'       => $traffic,//公共交通
            'ext'        => [
                [
                    'key'   => 'first_picture',
                    'val'   => $firstPicture,
                ]
            ],   //景区扩展信息
            'videoUrl'   => $videoUrl
        ];

        if (!empty($detailImg)) {
            if (!is_array($detailImg)) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '轮播图参数有误');
            }
            $params['imgpathgrp'] = json_encode($detailImg);
        } else {
            $params['imgpathgrp'] = '';
        }

        $sid = $this->_sid;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        //发布产品权限校验
        $proTypeAuth = (new \Business\Authority\AuthContext())->memberProductTypeAuth($sid);
        if (!in_array('C', $proTypeAuth)) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无发布酒店类型产品权限！');
        }

        //数据权限
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();

        if (empty($params['id'])) {
            if ($dataAuthLimit->hasLimit()) {
                $this->apiReturn(403, [], '无相关数据权限，不可发布产品！');
            }
            //创建酒店产品
            $result = $this->_businessLib->createHotel($params);
        } else {
            if ($dataAuthLimit->hasLidBeenLimit($params['id'])) {
                $this->apiReturn(403, [], '无相关数据权限，不可发布产品！');
            }
            //修改酒店产品
            $result = $this->_businessLib->updateHotel($params);
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);

    }

    /**
     * 查询酒店信息
     * <AUTHOR>
     * @date 2020/7/2
     *
     * @return array
     */
    public function queryInfo()
    {
        $hotelId = I('id', 0, 'intval');//酒店ID

        if (empty($hotelId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数有误');
        }

        $result = $this->_businessLib->queryHotelInfoById($hotelId);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $this->_object, $result['msg']);
        }

        //参数转换
        $data = $result['data'];
        $info = [
            'address'    => $data['address'] ?? '',
            'area'       => $data['area'] ?? '',
            'id'         => $data['id'] ?? 0,
            'tel'        => $data['tel'] ?? '',
            'title'      => $data['title'] ?? '',
            'type'       => $data['pType'] ?? '',
            'jtype'      => $data['jtype'] ?? '',
            'resourceID' => $data['resourceid'] ?? 0,
            'apply_did'  => $data['applyDid'] ?? 0,
            'bhjq'       => $data['bhjq'] ?? '',
            'jqts'       => $data['jqts'] ?? '',
            'jtzn'       => $data['jtzn'] ?? '',
            'imgUrl'     => $data['imgpath'] ?? '',
            'lngLatPos'  => $data['lngLatPos'] ?? '',
            'service'    => $data['service'] ?? '',
            'device'     => $data['device'] ?? '',
        ];

        $this->apiReturn(200, $info, '');

    }

    /**
     * 选项参数查询
     * <AUTHOR>
     * @date 2020/7/3
     *
     * @return array
     */
    public function queryOption()
    {
        $hotelId = I('type', 0, 'intval');//参数类型 0：酒店设施 1：酒店服务 2：床型 3：房间设施 4：窗户类型

        $result = $this->_businessLib->queryByApplyDidAndType($this->_sid, $hotelId);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], ['list' => []], $result['msg']);
        }

        $this->apiReturn(200, ['list' => $result['data']], '');
    }

    /**
     * 选项参数多个查询
     * <AUTHOR>
     * @date 2020/9/9
     *
     * @return array
     */
    public function queryBatchOption()
    {
        $types = I('types');//参数类型 0：酒店设施 1：酒店服务 2：床型 3：房间设施 4：窗户类型

        if (empty($types)) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $count = count($types);
        if ($count <= 1) {
            $this->apiReturn(204, [], '获取单条数据请使用获取单条数据的接口');
        }

        //最多5个
        if ($count > 5) {
            $this->apiReturn(204, [], '参数有误!');
        }

        foreach ($types as $key => $tmp) {
            if (!in_array($tmp, $this->_businessLib::HOTEL_OPTION_PARAMS)) {
                $this->apiReturn(204, [], '参数错误');
            }
            $types[$key] = intval($tmp);
        }

        $queryData = [];

        foreach ($types as $value) {
            $queryData[$value]  = [];
            $result             = $this->_businessLib->queryByApplyDidAndType($this->_sid, $value);
            if ($result['code'] == 200) {
                $queryData[$value] = $result['data'];
            }
        }
        $this->apiReturn(200, $queryData, '');
    }

    /**
     * 创建酒店房型
     * <AUTHOR>
     * @date 2020/7/3
     *
     * @return array
     */
    public function createRoom()
    {
        //参数效验
        $params = $this->_handleParamsSaveRoom();

        $result = $this->_businessLib->createRoomType($params);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 创建酒店房型
     * <AUTHOR>
     * @date 2020/7/3
     *
     * @return array
     */
    public function updateRoom()
    {
        $roomId = I('id', 0, 'intval');//房型ID

        if (empty($roomId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        //参数效验
        $params = $this->_handleParamsSaveRoom();

        $result = $this->_businessLib->updateRoomType($roomId, $params);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 查询酒店房型信息
     * <AUTHOR>
     * @date 2020/7/3
     *
     * @return array
     */
    public function queryRoom()
    {
        $roomId = I('id', 0, 'intval');//房型ID

        if (empty($roomId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数有误');
        }

        $result = $this->_businessLib->queryRoomTypeById($roomId);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $this->_object, $result['msg']);
        }

        unset($result['data']['ifDel']);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除酒店房型
     * <AUTHOR>
     * @date 2020/7/3
     *
     * @return array
     */
    public function deleteRoom()
    {
        $roomId = I('id', 0, 'intval');//房型ID

        if (empty($roomId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $result = $this->_businessLib->deleteRoomType($roomId, $this->_memberId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 房型列表
     * <AUTHOR>
     * @date 2020/7/3
     *
     * @return array
     */
    public function roomList()
    {
        $name      = I('name', '', 'strval,trim');//房型名称
        $pageStart = I('pageStart', 1, 'intval');//开始页数
        $pageSize  = I('pageSize', 10, 'intval');//每页记录数

        $params = [];
        if (!empty($name)) {
            $params['name'] = $name;
            $pageStart      = 1;
        }

        $result = $this->_businessLib->queryRoomTypeByPage($this->_sid, $pageStart, $pageSize, $params);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $this->_object, $result['msg']);
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 保存房型参数效验
     * <AUTHOR>
     * @date 2020/7/3
     *
     * @return array
     */
    private function _handleParamsSaveRoom()
    {
        $remark      = I('remark', '', 'strval,trim');//房型说明
        $typeName    = I('typeName', '', 'strval,trim');//名称
        $roomNum     = I('roomNum', 0, 'intval');//房间数
        $maxCustomer = I('maxCustomer', 0, 'intval');//最大入住人数
        $bed         = I('bed', 0, 'intval');//床型
        $bedNum      = I('bedNum', 0, 'intval');//床数
        $area        = I('area', '', 'strval,trim');//面积
        $device      = I('device', '', 'strval');//房间设施,多值以逗号分隔
        $windows     = I('windows', null, 'intval');//窗户类型
        $imagePath   = I('imagePath', '非A级', 'strval');//图片路径,最多5张
        //$hotelId     = I('hotelId', 0, 'intval');//酒店ID

        //if (empty($hotelId)) {
        //    $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        //}
        //if (empty($remark)) {
        //    $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写房型说明');
        //}
        if (empty($typeName)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写房型名称');
        }
        //if (empty($roomNum)) {
        //    $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写房间数');
        //}
        if (empty($maxCustomer)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写最大入住人数');
        }
        if (mb_strlen($remark, 'utf-8') > 2000) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '房型说明超出2000个字符');
        }
        if (empty($bed)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写床型');
        }
        if (empty($bedNum)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写床数');
        }
        if (empty($area)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写面积');
        }
        if (empty($device)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写房间设施');
        }
        if ($windows === null) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请填写窗户类型');
        }
        if (empty($imagePath)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请上传图片');
        }
        if (mb_strlen($imagePath, 'utf-8') > 2000) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '图片上传数量限制');
        }

        $params = I('param.');

        $params['applyDid'] = $this->_sid;
        $params['operator'] = $this->_memberId;

        return $params;
    }

}