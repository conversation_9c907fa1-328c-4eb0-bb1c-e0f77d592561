<?php
/**
 * 团购导码接口业务
 *
 * <AUTHOR>
 * Date: 2018-07-16
 */

namespace Controller\Product;

use Business\Order\Modify;
use Business\Order\OrderQueryJavaService;
use Business\Order\OrderQueryMove;
use Library\Constants\OrderConst;
use Library\Controller;
use Library\Tools\Helpers;
use Model\Order\OrderTools;
use Model\Product\Land;
use Model\Product\Ticket;
use Library\SimpleExcel;
use Business\Order\PlatformSubmit;
use Library\Cache;

class GuideCode extends Controller
{
    private $_memberId;
    private $_loginInfo;

    private $_cacheObj;
    private $_orderLockCacheKey = "order:group_order:";
    private $_orderGroupDataCacheKey = "order:group_order:data:"; //临时存储结果

    public function __construct()
    {
        $this->_memberId  = $this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
        $this->_soap      = $this->getSoap();
    }

    /**
     * 团购导码下单接口
     * <AUTHOR>
     * @date   2018-07-16
     * @param aid            供应商id
     * @param Tlid             景区id_上级供应商id（74030_3385）
     * @param tid           门票id
     * @param playTime     游玩时间
     * @param oddNum             单数
     * @param tensorNum         张数
     * @param paymode           支付方式
     * @param output         输出方式
     * @param name          团购姓名
     * @param tel             手机号
     * @param regiment         团号
     */
    public function groupOrder()
    {
        $Tnum   = I('oddNum', 0, 'intval');
        $output = I('output', 1, 'intval');
        $export = I('export', 0, 'intval'); //是否导出 1是0否
        if ($Tnum > 30) {
            echo "订单总数不能超过30,注意!";exit;
        }

        //记录请求参数
        pft_log('order/tgdm_request', json_encode([$this->_memberId, I('post.')]));

        //拼装下单参数
        $this->_combineOptions();

        //如果是相同的导码参数，做下并发控制
        $lockKey         = md5(json_encode($this->_options));
        $cache           = \Library\Cache\Cache::getInstance('redis');
        $this->_cacheObj = Cache\Cache::getInstance('redis');

        $this->_orderGroupDataCacheKey = $this->_orderGroupDataCacheKey. $this->_memberId  . ':' . $lockKey;

        //导出数据
        if ($export == 1) {
            $lockFinishRet = $this->_cacheObj->get($this->_orderGroupDataCacheKey);
            if ($lockFinishRet) {
                $lockFinishRet = json_decode($lockFinishRet, true);
                $lockFinishRet = is_array($lockFinishRet) ? $lockFinishRet : [];
                //生成导出文件
                $landModel  = new land();
                $landInfo                = $landModel->getLandInfo($this->_options['lid'], false, 'title');
                $ltitle                  = $landInfo ? $landInfo['title'] : '';
                //导出对应格式
                if ($output == 1) {
                    //txt导出
                    $this->_TxtOutput($ltitle, $Tnum);
                    foreach ($lockFinishRet as $k => $v) {
                        print_r($v['code']);
                        echo "\r\n";
                    }

                    exit;
                } elseif ($output == 2) {
                    //excel导出
                    $filename = $ltitle . '(' . $Tnum . ')' . date('Ymd') . '团购导码';
                    $xls = new SimpleExcel('UTF-8', false, 'lt');

                    $xls->addArray($lockFinishRet);
                    $xls->generateXML($filename);

                    exit;
                }
            } else {
                $this->apiReturn(203, '', '文件已失效，无法导出');
            }
        }

        $lockRet = $cache->lock($lockKey, 1, 60);
        if (!$lockRet) {
            pft_log('order/tgdm', json_encode(['业务处理中', $this->_memberId, $this->_options]));

            echo "业务处理中，请重试";exit;
        }

        if (!$this->_options) {
            echo "下单参数错误,请检查!";exit;
        }

        $this->_orderLockCacheKey = $this->_orderLockCacheKey . $this->_memberId;
        $isLock = $this->_cacheObj->get($this->_orderLockCacheKey);

        if ($isLock) {
            $this->apiReturn(203, '', '操作频繁请稍后再试');
        }

        $this->_cacheObj->set($this->_orderLockCacheKey, 1, 5);

        $orderArr  = [];
        $errMsgArr = [];
        //循环下单
        for ($i = 0; $i < $Tnum; $i++) {
            //简易远端订单号
            $this->_options['remotenum'] = date('YmdHis') . $i;
            try {
                //下单动作
                $orderNum = $this->_orderAct($this->_soap, $this->_options);
            } catch (\Exception $e) {
                $errMsgArr[$i]['code'] = $e->getMessage();
                continue;
            }
            $orderArr[] = $orderNum;
        }

        //清除锁数据
        $cache->rm($lockKey);
        //拼装返回数据
        $this->_assemblyGuideCode($orderArr, $output,$errMsgArr);
        
        $this->apiReturn(200, '', '处理完成');
    }

    /**
     * 组合下单接口需要的参数
     */
    private function _combineOptions()
    {

        $lid_aid        = I('Tlid'); //景区id_供应商id
        $las            = explode("_", $lid_aid);
        $aid            = I('aid') == $las[1] ? I('aid') : $las[1]; //供应商id
        $this->_options = [
            'pid'        => I('tid', 0, 'intval'),
            'lid'        => $las[0],
            'tnum'       => I('tensorNum', 0, 'intval'),
            'begintime'  => date('Y-m-d', strtotime(I('playTime', ''))),
            'ordername'  => I('name', ''),
            'contacttel' => I('tel') ? I('tel') : "12301",
            'ordertel'   => I('tel') ? I('tel') : "12301",
            'memo'       => I('memo', ''),
            'paymode'    => I('paymode', 2),
            'aid'        => $aid,
            'ordermode'  => 20,
            'Ttuan'      => I('regiment', ''),
            'upAid'      => I('aid', 0, 'intval'),
        ];

        if (!($this->_options['pid'] &&
            $this->_options['tnum'] &&
            $this->_options['begintime'] &&
            $this->_options['ordername'] &&
            $this->_options['contacttel'])) {
            echo "参数错误";exit;
        }

        $ticModel = new Ticket('slave');

        $ticketInfo =  $ticModel->getTicketInfoById($this->_options['pid'], 'pid');
        $this->_options['real_pid'] = $ticketInfo['pid'];

        //获取结算价
        $price = $ticModel->getSettlePrice($this->_options['pid'], $this->_memberId, $this->_options['aid'], $this->_options['begintime']);
        if ($price || $price === 0) {
            $this->_options['price'] = $price * 100;
        }
    }

    /**
     * 下单动作
     * @param  object   $s   内部接口对象
     * @return string/int   订单号/错误提示码
     */
    private function _orderAct($s, $option)
    {
        $member          = $this->_memberId; //会员ID（唯一）
        $tid             = $option['pid']; //票类ID
        $pid             = $option['real_pid'];
        $begintime       = date('Y-m-d', strtotime($option['begintime'])); //开始时间
        $tnum            = $option['tnum']; //数量
        $ordername       = $option['ordername']; //取票人姓名
        $ordertel        = $option['ordertel']; //取票人手机
        $aid             = $option['aid']; //供应商ID（唯一）
        $paymode         = $option['paymode']; //支付方式
        $ordermode       = $option['ordermode']; //下单方式
        $series          = $option['Ttuan']; //团号
        $memo            = ''; //订单备注信息

        if ($member == $aid) {
            //如果购买用户的分销商相同，支付方式重置位自供自销
            $paymode = 3;
        }

        if ($series) {
            $tmpSeries = json_decode($series, true);
            $venueId = $tmpSeries[0];
            $roundId = $tmpSeries[1];
            $areaId  = $tmpSeries[2];
        } else {
            $venueId = 0;
            $roundId = 0;
            $areaId  = 0;
        }

        $orderParams = [
            'pid'       => $pid,
            'begintime' => $begintime,
            'leavetime' => $begintime,
            'channel'   => $ordermode,
            'tnum'      => $tnum,
            'paymode'   => $paymode,
            'member_id' => $member,
            'aid'       => $aid,
            'ordername' => $ordername,
            'ordertel'  => $ordertel,
            'op_id'     => $this->_loginInfo['memberID'],
            'memo'      => $memo,
            'venue_id'  => $venueId,
            'round_id'  => $roundId,
            'area_id'   => $areaId,
            'is_sale'   => false,
            'uniqe'     => microtime() . rand(10000, 99999),
            'is_send_sms' => false
        ];

        $platformSubmit = new PlatformSubmit();
        $res = $platformSubmit->submit($orderParams);

        if ($res[0] == 200) {
            $orderNum = $res[2]['orderNum'];
            pft_log('order/tgdm', '团购导码下单成功，订单号：' . $orderNum);
            return $orderNum;
        } else {
            throw new \Exception("下单失败,错误信息：" . $res[1]);
        }
    }

    /**
     * 拼装订单凭证码并导出模块（txt/excel）
     * @param  array $orderArr  订单凭证码数组
     * @param  int   $echoT     导出格式
     * @param  array   $errMsgArr     错误信息数组
     * @return string  对应格式文件流（下载地址）
     */
    private function _assemblyGuideCode($orderArr, $echoT,$errMsgArr = [])
    {
        //$landModel  = new land();
        $orderQueryJavaBiz = new OrderQueryJavaService('master');

        //$Tnum                    = count($orderArr);
        //$landInfo                = $landModel->getLandInfo($this->_options['lid'], false, 'title');
        //$ltitle                  = $landInfo ? $landInfo['title'] : '';
        $orderCode               = [];
        if ($orderArr){
            //$selectParams            = [
            //    'field'        => 'code'
            //];
            //$orderCode       = $orderQueryJavaBiz->getOrderInfoByOrderNumAndOtherParam($orderArr,[],$selectParams);

            //订单查询迁移
            $orderMove = new OrderQueryMove();
            $orderCode = $orderMove->getOrderCodeByOrderNum($orderArr);
        }
        $orderCode       = array_merge($orderCode,$errMsgArr);

        $this->_cacheObj->delete($this->_orderLockCacheKey);

        //暂存数据
        $this->_cacheObj->set($this->_orderGroupDataCacheKey, json_encode($orderCode), 180); //3分钟

//        //导出对应格式
//        if ($echoT == 1) {
//            //txt导出
//            $this->_TxtOutput($ltitle, $Tnum);
//            foreach ($orderCode as $k => $v) {
//                print_r($v['code']);
//                echo "\r\n";
//            }
//        } elseif ($echoT == 2) {
//            //excel导出
//            $filename = $ltitle . '(' . $Tnum . ')' . date('Ymd') . '团购导码';
//            $xls = new SimpleExcel('UTF-8', false, 'lt');
//
//            $xls->addArray($orderCode);
//            $xls->generateXML($filename);
//        }
    }

    //导出模块txt
    private function _TxtOutput($ltitle, $Tnum)
    {
        $filename = $ltitle . '(' . $Tnum . ')' . date('Ymd') . '团购导码.txt';
        header("Content-type: text");
        header("Content-Disposition: attachment; filename=$filename");
        header("Expires: 0");
        header("Cache-Control: must-revalidate, post-check=0,pre-check=0");
        header("Pragma: public");
    }

    /**
     * 团购导码改单接口
     * <AUTHOR>
     * @date   2018-07-16
     *
     * @param  string    order       订单号（多个请用逗号隔开）
     */
    public function OrderChangePro()
    {
        $orderStr = I('order', '', 'string');
        $orders   = str_replace("，", ",", $orderStr);
        $orders   = explode(",", $orders);

        define('IN_PFT', true);
        //获取内部接口类
        $serverInside = $this->getServerInside();
        //获取订单模型接口
        $orderModel = new OrderTools();
        $modifyBiz  = new Modify();
        $orderFruit = '';
        foreach ($orders as $key => $value) {
            //获取订单信息
            $orderInfo = $orderModel->getOrderInfo($value, 'member,status');

            if (!empty($orderInfo) && $orderInfo['member'] == $this->_memberId) {
                if ($orderInfo['status'] == 3) {
                    $orderFruit .= '订单：' . $value . '订单已是取消的状态，请勿重复操作' . "\n";
                } else {
                    $reqSerialNumber = $modifyBiz->getAuditInfoByOrder($value);
                    //取消订单动作
                    $res = Helpers::platformRefund($value, -1, $this->_memberId, OrderConst::PURCHASE_CANCEL, 'common',
                        $reqSerialNumber);

                    if ($res['code'] == 200) {
                        $orderFruit .= '订单：' . $value . '取消成功!' . "\n"; //取消成功
                    } else {
                        $errorMsg = isset($res['err_msg']) ? $res['err_msg'] : $res['msg'];
                        $orderFruit .= '订单：' . $value . '取消失败!' . "\n" . '错误信息：' . $errorMsg; //取消失败
                    }
                }

            } else {
                $orderFruit .= '订单：' . $value . '不属于购买者的导码订单，无法执行取消操作' . "\n";
            }

        }

        $this->apiReturn(200, ['data' => $orderFruit], '');
    }
}
