<?php

namespace Controller\Product;

use Library\Controller;
use \Model\Product\BaseCard;
use Business\Product\BaseCard as cardBiz;

class Card extends Controller
{
    private $_sid        = 0;
    private $_cardModel  = null;
    private $_actionType = [
        1 => 'staffCard',       //员工卡
        2 => 'memberCard',      //会员卡
        3 => 'annualCard',       //年卡
        4 => 'presimingCard',   //温泉卡
        5 => 'packCard',         //园区卡
        6 => 'timingCard',      //计时卡
        7 => 'memberIdCard',    //身份证一卡通
    ];

    public function __construct()
    {
        $userInfo         = $this->getLoginInfo();
        $this->_sid       = $userInfo['sid'];
        $this->_cardModel = new BaseCard();
    }

    /**
     * 释放卡入口
     * <AUTHOR> Li
     * @data  2019-12-05
     *
     * return array
     */
    public function releaseCard()
    {
        $physicsNo  = I('get.physics_no', '');
        $actionType = I('get.action', 0, 'intval');  //操作类型 0 查询 1 普通删除 2 强制删除（不处理业务表， 删除pft_card表）


        if (!in_array($actionType, [0, 1])) {
            $this->clientReturn(203, [], '操作类型有误');
        }

        if (!$physicsNo || !preg_match('/^[0-9a-zA-Z]{1,20}$/i', $physicsNo)) {
            $this->clientReturn(203, [], '物理卡号参数错误');
        }

        $cardInfo = $this->_cardModel->getCardInfo($physicsNo);
        if (empty($cardInfo)) {
            $this->clientReturn(203, [], '没有该卡片信息');
        }
        if ($this->_sid != 1 && $cardInfo['type'] != 6) {
            $this->clientReturn(203, [], '没有权限');
        }

        $cardBiz = new cardBiz();
        $action   = $this->_actionType[$cardInfo['type']];
        $result   = $cardBiz->$action($cardInfo, $actionType);

        $this->clientReturn($result['code'], $result['data'], $result['msg']);
    }


}