<?php
/**
 * 演出类产品分销商库存控制器
 *
 * <AUTHOR>
 * @date 2016-01-03
 *
 */
namespace Controller\product;

use Business\JavaApi\Order\ReservationOrder;
use Business\JavaApi\Product\ReservationOrderStorage;
use Business\JavaApi\Product\ReservationStorage;
use Business\Product\ProductStorage;
use Library\Controller;
use Model\Product\Show;

class storage extends Controller
{
    private $memberId = null;

    public function __construct()
    {
        $this->memberId = $this->isLogin('ajax');
    }

    /**
     * 获取默认的分销商列表
     * <AUTHOR>
     * @date   2016-03-15
     *
     * @return
     */
    public function getListDefault()
    {
        $areaId   = $this->getParam('area_id');
        $memberId = $this->memberId;
        $venusId  = $this->getParam('venus_id');

        if (!$areaId || !$venusId) {
            $this->apiReturn(203, '', '参数错误');
        }

        $yxModel   = new \Model\Product\YXStorage();
        $showModel = new Show();
        $tmp       = $yxModel->getResellerList($memberId);

        $list = array();
        foreach ($tmp as $item) {
            $resellerId = $item['son_id'];

            //获取用户的分销数量
            $storageTmp        = $yxModel->totalDefaultNumber($resellerId, $areaId);
            $item['total_num'] = $storageTmp;

            $list[] = array(
                'name'      => $item['dname'],
                'id'        => $item['son_id'],
                'account'   => $item['account'],
                'total_num' => $item['total_num'],
            );
        }

        //获取默认的汇总数据
        $storageInfo = $yxModel->getDefaultInfo($areaId);
        $status      = 1;
        if ($storageInfo) {
            $status = $storageInfo['status'];
        }

        //获取综合数据
        $summary = array();
        $total   = $showModel->getZoneSeatsNums($venusId, $areaId);

        //处理数据
        $summary = array(
            'total'  => $total,
            'status' => $status,
        );

        $data = array(
            'summary' => $summary,
            'list'    => $list,
        );

        $this->apiReturn(200, $data);
    }

    /**
     * 获取具体场次下面的分销商列表
     * <AUTHOR>
     * @date   2016-03-15
     *
     * @param  [type] $memberSID
     * @return [type]
     */
    public function getList()
    {
        $roundId  = $this->getParam('round_id');
        $areaId   = $this->getParam('area_id');
        $venusId  = $this->getParam('venus_id');
        $memberId = $this->memberId;

        if (!$roundId || !$areaId || !$venusId) {
            $this->apiReturn(203, '', '参数错误');
        }

        //获取分销商数据
        $storageModel = new \Model\Product\YXStorage();

        //判断是否配置了具体场次的分销商库存信息
        $storageInfo  = $storageModel->getInfo($areaId, $roundId);
        $isUseDefault = $storageInfo ? false : true;

        //获取默认的配置
        if (!$storageInfo) {
            $storageInfo = $storageModel->getDefaultInfo($areaId);
        }

        //如果之前都没有设置，默认是1
        $status = $storageInfo ? $storageInfo['status'] : 1;

        $tmp = $storageModel->getResellerList($memberId);

        //已经分配的数量
        $allocatedNum = 0;

        //获取分销商的销售数据
        $list = array();
        foreach ($tmp as $item) {
            $resellerId = $item['son_id'];

            //获取用户的分销数量
            if ($isUseDefault) {
                //默认配置
                $storageTmp = $storageModel->totalDefaultNumber($resellerId, $areaId);
            } else {
                //针对场次配置
                $storageTmp = $storageModel->totalNumber($resellerId, $roundId, $areaId);
            }
            $item['total_num'] = $storageTmp;

            $sales = $storageModel->getResellerNums($roundId, $areaId, $resellerId);
            $sales = intval($sales);

            $list[] = array(
                'name'       => $item['dname'],
                'id'         => $item['son_id'],
                'account'    => $item['account'],
                'total_num'  => $item['total_num'],
                'selled_num' => $sales,
            );

            if ($item['total_num'] > 0) {
                $allocatedNum += $item['total_num'];
            }
        }

        //获取综合数据
        $summary = array();
        $tmp     = $storageModel->getSummary($roundId, $areaId);

        //未分配数量
        $unallocated = ($tmp['total'] - $allocatedNum) >= 0 ? ($tmp['total'] - $allocatedNum) : 0;

        //处理数据
        $summary = array(
            'total'            => $tmp['total'],
            'selled'           => $tmp['saled'],
            'reserve'          => $tmp['reserve'],
            'unallocated'      => $unallocated,
            'used_unallocated' => $tmp['used_unallocated'],
            'status'           => $status,
        );

        $data = array(
            'summary' => $summary,
            'list'    => $list,
        );

        $this->apiReturn(200, $data);
    }

    /**
     * 获取默认的分区配置信息
     * <AUTHOR>
     * @date   2016-03-15
     *
     * @return
     */
    public function getConfigDefault()
    {
        $venusId  = $this->getParam('venus_id');
        $memberId = $this->memberId;

        if (!$venusId) {
            $this->apiReturn(203, '', '参数错误');
        }

        $showModel = new Show();
        $tmp       = $showModel->getZoneList($venusId);

        $areaList = array();
        foreach ($tmp as $item) {
            $areaList[] = array('id' => $item['zone_id'], 'name' => $item['zone_name']);
        }

        $data = array(
            'area_list' => $areaList,
        );

        $this->apiReturn(200, $data);
    }

    /**
     * 获具体场次下的分区配置信息
     * <AUTHOR>
     * @date   2016-03-15
     *
     * @return
     */
    public function getConfig()
    {
        $date    = $this->getParam('date');
        $venusId = $this->getParam('venus_id');

        if (!$date || !$venusId) {
            $this->apiReturn(203, '', '参数错误');
        }

        //加载模型
        $storageModel = new \Model\Product\YXStorage();
        $showModel    = new Show();

        //获取分区
        $tmp      = $showModel->getZoneList($venusId);
        $areaList = array();
        foreach ($tmp as $item) {
            $areaList[] = array('id' => $item['zone_id'], 'name' => $item['zone_name']);
        }

        //获取所有的场次
        $tmp       = $showModel->getRoundList($venusId, $field = 'id, round_name', 0, $date, $date);
        $roundList = array();
        foreach ($tmp as $item) {
            $roundList[] = array('id' => $item['id'], 'name' => $item['round_name']);
        }

        //获取场馆信息
        $venueInfo = $showModel->getVenuesInfo($venusId);

        $data = array(
            'round_list' => $roundList,
            'area_list'  => $areaList,
            'venue_info' => $venueInfo,
        );

        $this->apiReturn(200, $data);
    }

    /**
     * 默认情况下设置分销商库存
     * <AUTHOR>
     * @date   2016-03-16
     *
     * @return [type]
     */
    public function setListDefault()
    {
        $data     = $this->getParam('data');
        $areaId   = $this->getParam('area_id');
        $venusId  = $this->getParam('venus_id');
        $status   = intval($this->getParam('status'));
        $memberId = $this->memberId;

        if (!$areaId || !$data || !$venusId || !$venusId || !in_array($status, [0, 1])) {
            $this->apiReturn(203, '', '参数错误');
        }

        //加载模型
        $storageModel = new \Model\Product\YXStorage();
        $showModel    = new Show();
        $showBiz = new \Business\Product\Show();

        //权限验证
        $isAuth = $storageModel->isAuth($venusId, $memberId);
        if (!$isAuth) {
            $this->apiReturn(205, '', '没有权限配置库存');
        }

        $data = @json_decode($data);
        if (!$data) {
            $this->apiReturn(203, '', '设置数据错误');
        }

        //获取分区详情
        $zoneInfo = $showBiz->getZoneBaseInfo($areaId);
        if (!$zoneInfo) {
            $this->apiReturn(203, '', '分区数据错误');
        }
        $venusId = $zoneInfo['venue_id'];

        //根据场馆ID获取供应商ID
        $venusInfo = $showModel->getVenuesInfo($venusId);
        if (!$venusInfo) {
            $this->apiReturn(203, '', '分区数据错误');
        }
        $setterId = $venusInfo['apply_did'];

        //获取分销商的数据，核实数据是否合法
        $resellerListArr = [];
        $tmp             = $storageModel->getResellerList($memberId);

        foreach ($tmp as $item) {
            $resellerListArr[] = $item['son_id'];
        }

        $allocateNum = 0;
        $resData     = array();

        foreach ($data as $item) {
            $item = is_array($item) ? $item : (array) $item;

            if (isset($item['reseller_id']) && $item['reseller_id'] && in_array($item['reseller_id'], $resellerListArr)) {
                $totalNum = isset($item['total_num']) ? intval($item['total_num']) : 0;
                $totalNum = $totalNum < -1 ? -1 : $totalNum;

                $resData[$item['reseller_id']] = $totalNum;

                if ($totalNum > 0) {
                    $allocateNum += $totalNum;
                }
            }
        }

        if (!$resData) {
            $this->apiReturn(203, '', '设置数据错误');
        }

        //判断总是是不是超过
        $allSeats = $showModel->getZoneSeatsNums($venusId, $areaId);

        if ($allocateNum > $allSeats) {
            $this->apiReturn(204, '', '保留库存之和超过总库存');
        }

        $res = $storageModel->setDefaultResellerStorage($areaId, $resData, $status, $setterId);

        if ($res) {
            $this->apiReturn(200, array());
        } else {
            $this->apiReturn(500, array(), '服务器错误');
        }
    }

    /**
     * 具体场次下设置分销商库存
     * <AUTHOR>
     * @date   2016-03-16
     *
     * @return [type]
     */
    public function setList()
    {
        $data     = $this->getParam('data');
        $roundId  = $this->getParam('round_id');
        $venusId  = $this->getParam('venus_id');
        $areaId   = $this->getParam('area_id');
        $memberId = $this->memberId;

        if (!$roundId || !$areaId || !$venusId || !$data) {
            $this->apiReturn(203, '', '参数错误');
        }

        //加载模型
        $storageModel = new \Model\Product\YXStorage();
        $showModel    = new Show();

        //权限验证
        $isAuth = $storageModel->isAuth($venusId, $memberId);
        if (!$isAuth) {
            $this->apiReturn(205, '', '没有权限配置库存');
        }

        $data = @json_decode($data);
        if (!$data) {
            $this->apiReturn(203, '', '设置数据错误');
        }

        //获取分区详情
        $roundInfo = $showModel->getRoundInfo($roundId);
        if (!$roundInfo) {
            $this->apiReturn(203, '', '场次数据错误');
        }
        $venusId  = $roundInfo['venue_id'];
        $useDate  = $roundInfo['use_date'];
        $useDate  = intval(str_replace('-', '', $useDate));
        $setterId = $roundInfo['opid'];

        //获取分销商的数据，核实数据是否合法
        $resellerListArr = [];
        $tmp             = $storageModel->getResellerList($memberId);

        foreach ($tmp as $item) {
            $resellerListArr[] = $item['son_id'];
        }

        //获取之前的配置
        $originSetting = $storageModel->getOriginSetting($roundId, $areaId);

        $resData     = array();
        $allocateNum = 0;

        foreach ($data as $item) {
            $item = is_array($item) ? $item : (array) $item;

            if (isset($item['reseller_id']) && $item['reseller_id'] && in_array($item['reseller_id'], $resellerListArr)) {
                $totalNum                      = isset($item['total_num']) ? intval($item['total_num']) : 0;
                $totalNum                      = $totalNum < -1 ? -1 : $totalNum;
                $resData[$item['reseller_id']] = $totalNum;

                //如果之前配置的不是-1，重新配置的时候，需要比对已经设置值
                if (isset($originSetting[$item['reseller_id']])) {
                    //获取已经销售的数据
                    $sales = $storageModel->getResellerNums($roundId, $areaId, $item['reseller_id']);
                    $sales = intval($sales);

                    if ($originSetting[$item['reseller_id']] == -1) {
                        //之前设定库存库存是-1的情况下，如果变更为固定的库存时，需要和之前销售的量进行比对
                        if ($totalNum != -1) {
                            if ($sales > 0) {
                                if ($totalNum < $sales) {
                                    $this->apiReturn(206, '', '保留库存数据配置错误');
                                }
                            }
                        }
                    } else {
                        //之前就设置固定库存的，再次设置的话，就要和之前销售的量进行比对
                        if ($sales > 0) {
                            if ($totalNum < $sales) {
                                $this->apiReturn(206, '', '保留库存数据配置错误');
                            }
                        }
                    }
                }

                if ($totalNum > 0) {
                    $allocateNum += $totalNum;
                }
            }
        }

        if (!$resData) {
            $this->apiReturn(203, '', '保留库存数据配置错误');
        }

        //判断总是是不是超过
        $allSeatsArr = $storageModel->getRoundSeats($roundId, $areaId);
        $allSeats    = $allSeatsArr['total'];

        if ($allocateNum > $allSeats) {
            $this->apiReturn(204, '', '保留库存之和超过总库存');
        }

        $res = $storageModel->setResellerStorage($roundId, $areaId, $resData, $useDate, $setterId);
        if ($res) {
            $this->apiReturn(200, array());
        } else {
            $this->apiReturn(500, array(), '服务器错误');
        }
    }

    /**
     * 开启场次的分销库存
     * <AUTHOR>
     * @date   2016-03-16
     *
     * @return [type]
     */
    public function open()
    {
        $roundId  = $this->getParam('round_id');
        $areaId   = $this->getParam('area_id');
        $venusId  = $this->getParam('venus_id');
        $memberId = $this->memberId;

        if (!$roundId || !$venusId || !$areaId) {
            $this->apiReturn(203, '', '参数错误');
        }

        //加载模型
        $storageModel = new \Model\Product\YXStorage();
        $showModel    = new Show();

        //权限验证
        $isAuth = $storageModel->isAuth($venusId, $memberId);
        if (!$isAuth) {
            $this->apiReturn(205, '', '没有权限配置库存');
        }

        //获取场次信息
        $roundInfo = $showModel->getRoundInfo($roundId);
        if (!$roundInfo) {
            $this->apiReturn(203, '', '场次参数错误');
        }
        $setterId = $roundInfo['opid'];

        //判断是否配置了具体场次的分销商库存信息
        $storageInfo  = $storageModel->getInfo($areaId, $roundId);
        $isUseDefault = $storageInfo ? false : true;

        if ($isUseDefault) {
            $showModel = new \Model\Product\Show();
            $roundInfo = $showModel->getRoundInfoById($roundId, 'lid,venus_id,opid,status,use_date');
            $useDate   = $roundInfo ? $roundInfo['use_date'] : date('Ymd');
            $useDate   = intval(str_replace('-', '', $useDate));

            $res       = $storageModel->copyDataFromDefault($areaId, $roundId, $useDate);

            if (!$res) {
                $this->apiReturn(205, '', '初始化场次的分销库存数据错误');
            }
        }

        $res = $storageModel->setInfo($roundId, $areaId, $setterId, 1);

        if ($res) {
            $this->apiReturn(200, array());
        } else {
            $this->apiReturn(500, array(), '服务器错误');
        }
    }

    /**
     * 关闭场次的分销库存
     * <AUTHOR>
     * @date   2016-03-16
     *
     * @return [type]
     */
    public function close()
    {
        $roundId  = $this->getParam('round_id');
        $areaId   = $this->getParam('area_id');
        $venusId  = $this->getParam('venus_id');
        $memberId = $this->memberId;

        if (!$roundId || !$venusId || !$areaId) {
            $this->apiReturn(203, '', '参数错误');
        }

        //加载模型
        $storageModel = new \Model\Product\YXStorage();
        $showModel    = new Show();

        //权限验证
        $isAuth = $storageModel->isAuth($venusId, $memberId);
        if (!$isAuth) {
            $this->apiReturn(205, '', '没有权限配置库存');
        }

        //获取场次信息
        $roundInfo = $showModel->getRoundInfo($roundId);
        if (!$roundInfo) {
            $this->apiReturn(203, '', '场次参数错误');
        }
        $setterId = $roundInfo['opid'];

        //判断是否配置了具体场次的分销商库存信息
        $storageInfo  = $storageModel->getInfo($areaId, $roundId);
        $isUseDefault = $storageInfo ? false : true;

        if ($isUseDefault) {
            $showModel = new \Model\Product\Show();
            $roundInfo = $showModel->getRoundInfoById($roundId, 'lid,venus_id,opid,status,use_date');
            $useDate   = $roundInfo ? $roundInfo['use_date'] : date('Ymd');
            $useDate   = intval(str_replace('-', '', $useDate));

            $res       = $storageModel->copyDataFromDefault($areaId, $roundId, $useDate);

            if (!$res) {
                $this->apiReturn(205, '', '初始化场次的分销库存数据错误');
            }
        }

        $res = $storageModel->setInfo($roundId, $areaId, $setterId, 0);

        if ($res) {
            $this->apiReturn(200, array());
        } else {
            $this->apiReturn(500, array(), '服务器错误');
        }
    }

    /**
     * 开启关闭默认的分销库存
     * <AUTHOR>
     * @date   2016-03-16
     *
     * @return [type]
     */
    public function openDefault()
    {
        $areaId   = $this->getParam('area_id');
        $venusId  = $this->getParam('venus_id');
        $memberId = $this->memberId;

        if (!$venusId || !$areaId) {
            $this->apiReturn(203, '', '参数错误');
        }

        //加载模型
        $storageModel = new \Model\Product\YXStorage();

        $defaultInfo = $storageModel->getDefaultInfo($areaId);
        if (!$defaultInfo) {
            $this->apiReturn(206, '', '请先保存默认库存配置');
        }

        //权限验证
        $isAuth = $storageModel->isAuth($venusId, $memberId);
        if (!$isAuth) {
            $this->apiReturn(205, '', '没有权限配置库存');
        }

        if ($defaultInfo['status'] == 1) {
            $this->apiReturn(200, array());
        }

        $res = $storageModel->setInfoDefault($areaId, $memberId, 1);

        if ($res) {
            $this->apiReturn(200, array());
        } else {
            $this->apiReturn(500, array(), '服务器错误');
        }
    }

    /**
     * 关闭默认的分销库存
     * <AUTHOR>
     * @date   2016-03-16
     *
     * @return [type]
     */
    public function closeDefault()
    {
        $areaId   = $this->getParam('area_id');
        $venusId  = $this->getParam('venus_id');
        $memberId = $this->memberId;

        if (!$venusId || !$areaId) {
            $this->apiReturn(203, '', '参数错误');
        }

        //加载模型
        $storageModel = new \Model\Product\YXStorage();

        $defaultInfo = $storageModel->getDefaultInfo($areaId);
        if (!$defaultInfo) {
            $this->apiReturn(206, '', '请先保存默认库存配置');
        }

        //权限验证
        $isAuth = $storageModel->isAuth($venusId, $memberId);
        if (!$isAuth) {
            $this->apiReturn(205, '', '没有权限配置库存');
        }

        if ($defaultInfo['status'] == 0) {
            $this->apiReturn(200, array());
        }

        $res = $storageModel->setInfoDefault($areaId, $memberId, 0);

        if ($res) {
            $this->apiReturn(200, array());
        } else {
            $this->apiReturn(500, array(), '服务器错误');
        }
    }

    /**
     * 根据输入的时间段获取当前最大的已预订数
     * TODO: Product/SellerStorage->getSelledStorage这边直接从订单表获取销售量要命啊，需要切换到java的库存接口去
     * <AUTHOR>
     * @date   2016-11-21
     *
     * @param  $tid        票id
     * @param  $btime      开始时间
     * @param  $etime      结束时间
     * @param  $weekdays   选定周几
     * @return
     */
    public function getMaxSelledStorage()
    {
        $tid       = I('post.tid', '', 'intval');
        $btime     = I('post.btime', '', 'strval');
        $etime     = I('post.etime', '', 'strval');
        $weekdays  = I('post.weekdays', '', 'strval');
        $checkDays = explode(',', $weekdays);
        //$model     = new \Model\Product\SellerStorage();
        $getStoragesBiz = new \Business\Product\Get\Storages();

        $maxBooked = 0;
        $days      = ceil((strtotime($etime) - strtotime($btime)) / 3600 / 24) + 1;
        for ($i = 0; $i < $days; $i++) {
            $date = date("Y-m-d", strtotime($btime) + $i * 24 * 3600);
            if (in_array(date('w', $date), $checkDays)) {
                //$tmp = $model->getSelledStorage($date, $tid);

                $tmp = $getStoragesBiz->getUsedStroageByTidDate($tid, $date);
                if ($tmp > $maxBooked) {
                    $maxBooked = $tmp;
                }
            }
        }
        $this->apiReturn(200, ['maxBooked' => $maxBooked], '返回成功');
    }

    /**
     * 根据日期数组获取当前最大的已预订数
     * TODO: Product/SellerStorage->getSelledStorage这边直接从订单表获取销售量要命啊，需要切换到java的库存接口去
     * <AUTHOR>
     * @date   2016-11-24
     *
     * @param  $tid          票id
     * @param  $checkedDays  日期数组
     * @return
     */
    public function getMaxSelledStorageByTimeArray()
    {
        $tid       = I('post.tid', '', 'intval');
        $dateArray = I('post.checkedDays', []);
        //$model     = new \Model\Product\SellerStorage();

        $getStoragesBiz = new \Business\Product\Get\Storages();
        
        $maxBooked = 0;
        $days      = count($dateArray);
        if ($days == 0) {
            $this->apiReturn(200, ['maxBooked' => $maxBooked], '返回成功');
        }
        for ($i = 0; $i < $days; $i++) {
            $date = date('Y-m-d', strtotime($dateArray[$i]));
            //$tmp  = $model->getSelledStorage($date, $tid);
            $tmp = $getStoragesBiz->getUsedStroageByTidDate($tid, $date);
            if ($tmp > $maxBooked) {
                $maxBooked = $tmp;
            }
        }
        $this->apiReturn(200, ['maxBooked' => $maxBooked], '返回成功');
    }
    /**
     * 获取订单普通库存和预约库存列表（分时预约库存最大）
     *
     * <AUTHOR>
     * @date   2020-06-10
     *
     */
    public function getOrderStorageByReservationOrNormal(){
        $orderNum  = I('get.order_num','');
        $month     = I('get.month','');
        if (!$month || !$orderNum){
            $this->apiReturn(204, [], '参数错误');
        }
        $startDate = date('Y-m-d',  strtotime($month . '-01'));
        $endDate   = date('Y-m-d', strtotime('+31 days', strtotime($month . '-01')));
        $productStorageBiz = new ProductStorage();
        $res = $productStorageBiz->getOrderStorageByReservationOrNormalService($orderNum,$startDate,$endDate);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'],$res['data'],$res['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }
    /**
     * 获取单天的预约库存或者普通库存
     *
     * <AUTHOR>
     * @date   2020-06-13
     *
     * @return
     */
    public function getOrderThisDayStorage(){
        $orderNum  = I('get.order_num','');
        $playDate  = I('get.play_date','');
        $supportCustomTime = I('get.support_custom_time',false, 'boolval'); //是否支持查询分时预约可售时间
        if (!$orderNum || !$playDate){
            $this->apiReturn(204, [], '参数错误');
        }
        $productStorageBiz = new ProductStorage();
        $res = $productStorageBiz->getOrderStorageByReservationOrNormalService($orderNum,$playDate,$playDate, [
            'unifiedStorage' => true, 'support_custom_time' => $supportCustomTime]);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'],$res['data'],$res['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }
}
