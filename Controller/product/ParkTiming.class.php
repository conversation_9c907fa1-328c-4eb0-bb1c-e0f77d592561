<?php
/**
 * Created by PhpStorm.
 * User: zhujb
 * Date: 2018/12/07
 * Time: 09:29
 */

namespace Controller\product;

use Business\CommodityCenter\Ticket;
use Business\JsonRpcApi\ScenicLocalService\TimeCard;
use Library\Controller;
use Library\SimpleExcel;
use Model\CardSolution\BlankCard;
use Model\CardSolution\TimeCardReport;
use Model\CardSolution\TimingCardDetail;
use Model\CardSolution\TimingEquipBind;
use Model\CardSolution\TimingOrder;
use Model\CardSolution\TimingOrderDetail;
use Model\CardSolution\TimingProduct;
use Model\CardSolution\Vjournal;
use Model\CardSolution\Vuser;
use Model\Member\Member;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\Product\BaseCard;
use function GuzzleHttp\Psr7\str;

class ParkTiming extends Controller
{

    private $_memberInfo;
    private $_opIdArr     = [];
    private $_vuserStatus = [0 => '待生效', 1 => '已生效', 2 => '已失效', 3 => '挂失卡'];
    private $_isSubsidy   = [0 => '未补费', 1 => '已补费', 2 => '无需补费', 3 => '超时金额免结算补费'];
    private $_payTypeText = [
        0  => '微信支付',
        1  => '支付宝支付',
        2  => '现金支付',
        3  => '计时卡余额',
        4  => '一卡通支付',
        5  => '虚拟卡支付',
        6  => '免费结算',
        7  => '奖励金支付',
        8  => 'POS支付',
        9  => '授信支付',
        29 => '易宝云企',
        26 => '银联商务支付',
        27 => '银联商务POS通',
        28 => '威富通',
        30 => '云闪付',
        31 => '农行',
        32 => '建行',
        39 => '江西农商行',
        43 => '青海银行',
        45 => '招商银行聚合支付',
        48 => '中国银行',
        49 => '易宝独立收款',
        50 => '工商银行',
        52 => '农行(云BMP)',
        54 => '安徽农商行',
        55 => '易宝平台收款',
    ];

    private $memberData = [];
    private $landData   = [];
	/**
	 * 存储票信息。
	 * 结构
	 * [
	 *    'tid' => ['id' => tid, 'title' => '票名称1' ],
	 *    'tid' => ['id' => tid, 'title' => '票名称2' ],
	 *    'tid' => ['id' => tid, 'title' => '票名称3' ],
	 * ]
	 * @var array
	 */
    private $ticketData = [];

    public function __construct()
    {
        $this->_memberInfo = $this->getLoginInfo('ajax');
        //if ($this->_memberInfo['dtype'] == 6) {
        //    $this->_opIdArr[] = $this->_memberInfo['memberID'];
        //} else {
        $memberModel      = new Member();
        $staffId          = $memberModel->getStaffByParentId($this->_memberInfo['sid']);
        $this->_opIdArr   = array_column($staffId, 'son_id');
        $this->_opIdArr[] = $this->_memberInfo['memberID'];
        //}
    }

    /**
     * 获取计时列表
     * @Author: zhujb
     * 2018/12/7
     */
    public function getTimingList()
    {
        $activeBeginTime = I('active_begin_time', '', 'strval');
        $activeEndTime   = I('active_end_time', '', 'strval');
        $mobile          = I('mobile', '', 'strval');
        $cardNo          = I('card_no', '', 'strval');
        $physicsNo       = I('physics_no', '', 'strval');
        $opId            = I('op_id', '', 'strval');
        $page            = I('page', 1, 'intval');
        $pageSize        = I('pageSize', 10, 'intval');
        $status          = I('status', 0, 'intval');
        $excel           = I('export', 0, 'intval');

        $timingOrderModel = new TimingOrder();
        $baseCardModel    = new BaseCard();

        if (empty($cardNo) && $physicsNo) {
            $cardInfo = $baseCardModel->getCardInfoByType('', $physicsNo);
            if (empty($cardInfo)) {
                $this->apiReturn(204, [], '物理卡号不存在');
            }

            $tmpStatus = $status;
            if (empty($status)) {
                $tmpStatus = -1;
            }

            $vuBindInfo = $timingOrderModel->getParkCardVuser('', $this->_memberInfo['sid'], $cardInfo['id'], 0,
                $tmpStatus);
            if (empty($vuBindInfo)) {
                $this->apiReturn(204, [], '计时卡使用记录不存在');
            }
            $cardNo = $vuBindInfo['card_no'] ?: '';
        }

        $vuserList  = $timingOrderModel->getParkCardVuserList($this->_memberInfo['sid'], '', $cardNo, $activeBeginTime,
            $activeEndTime, $mobile, $opId,
            'id,card_id,card_no,mobile,unbind_time,refund_op_id, create_time,op_id,status,member_id,sid', $page,
            $pageSize, $status);
        $vuserTotal = $timingOrderModel->getParkCardVuserCount($this->_memberInfo['sid'], '', $cardNo, $activeBeginTime,
            $activeEndTime, $mobile, $opId, $status);

        if (empty($vuserList)) {
            $this->apiReturn(204, [], '无绑定信息');
        }

        $memberModel   = new Member();
        $opIdArr       = array_column($vuserList, 'op_id');
        $refundOpIdArr = array_column($vuserList, 'refund_op_id');
        $memberIdArr   = array_unique(array_merge($opIdArr, $refundOpIdArr));
        $memberData    = $memberModel->getMemberInfoByMulti($memberIdArr, 'id', 'dname', true);

        $depositApi = new \Business\JavaApi\Fund\DepositApi();
        foreach ($vuserList as $key => $item) {
            $vuserList[$key]['dname']          = isset($memberData[$item['op_id']]['dname']) ? $memberData[$item['op_id']]['dname'] : '--';
            $vuserList[$key]['refund_card_op'] = isset($memberData[$item['refund_op_id']]['dname']) ? $memberData[$item['refund_op_id']]['dname'] : '--';
            $vuserList[$key]['create_time']    = !empty($item['create_time']) ? date('Y-m-d H:i:s',
                $item['create_time']) : '--';
            $vuserList[$key]['unbind_time']    = !empty($item['unbind_time']) ? date('Y-m-d H:i:s',
                $item['unbind_time']) : '--';
            $vuserList[$key]['physics_no']     = $baseCardModel->getCardInfoByType($item['card_id'], '')['physics_no'];
            $vuserList[$key]['status_text']    = $this->_vuserStatus[$item['status']];

            // 获取用户押金金额
            $depositArr                 = $depositApi->getMemberDeposit($item['member_id'], $item['sid'], 1);
            $vuserList[$key]['deposit'] = empty($depositArr['data']['remainingAmount']) ? 0 : $depositArr['data']['remainingAmount'];

            // 获取用户余额
            $moneyArr                  = $memberModel->balanceAndCredit($item['member_id'], $item['sid'], false);
            $vuserList[$key]['credit'] = $moneyArr['credit'];

            // 总金额
            $vuserList[$key]['total_money'] = $vuserList[$key]['deposit'] + $vuserList[$key]['credit'];
        }

        if ($excel) {
            $this->_handleTimingListExcel($vuserList);
        }

        $res['list']     = $vuserList;
        $res['total']    = $vuserTotal;
        $res['pageSize'] = $pageSize;

        $this->apiReturn(200, $res, '获取成功');
    }

    /**
     * 计时一卡通明细导出
     * @Author: zhujb
     * 2019/12/11
     *
     * @param $data
     */
    public function _handleTimingListExcel($data)
    {
        $filename = date('Ymd') . '-计时一卡通汇总';

        $excel[] = [
            '账号id',
            '卡号',
            '物理卡号',
            '手机号',
            '开卡时间',
            '开卡员工',
            '解绑时间',
            '解绑员工',
            '账户余额',
            '状态',
        ];
        foreach ($data as $item) {
            $excel[] = [
                'id'             => $item['id'],
                'card_no'        => $item['card_no'],
                'physics_no'     => $item['physics_no'],
                'mobile'         => $item['mobile'],
                'create_time'    => $item['create_time'],
                'open_card_op'   => $item['dname'],
                'unbind_time'    => $item['unbind_time'],
                'refund_card_op' => $item['refund_card_op'],
                'credit'         => $item['credit'],
                'status_text'    => $item['status_text'],
            ];
        }

        $xls = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }

    /**
     * 获取计时交易明细
     * @Author: zhujb
     * 2018/12/7
     */
    public function getTradeDetail()
    {
        $vuId     = I('vuId', 0, 'intval');
        $page     = I('page', 1, 'intval');
        $pageSize = I('pageSize', 10, 'intval');

        $vjournalModel        = new Vjournal();
        $timingOrderModel     = new TimingOrder();
        $orderQueryModel      = new OrderTools();
        $ticketModel          = new \Model\Product\Ticket();
        $memberModel          = new Member();
        $timingEquipBindModel = new TimingEquipBind();

        $vjournalList = $vjournalModel->getJournalList($vuId, '', '', '', '', $page, $pageSize,
            'ordernum,memo,money,type,op_id,sid,vuid,create_time');

        if (empty($vjournalList)) {
            $this->apiReturn(204, [], '无交易记录');
        }

        $total = $vjournalModel->getJournalList($vuId, '', '', '', '', 0, 0, '', true);

        $orderNumArr = array_column($vjournalList, 'ordernum');
        $orderList   = $orderQueryModel->getOrderInfo($orderNumArr,
            'ordernum,tid,pid,tnum,tprice,ordertime');
        $orderList   = array_key($orderList, 'ordernum');
        $pidArr      = array_column($orderList, 'pid');
        $productArr  = $ticketModel->getProductList($pidArr, 'id,p_name');
        $opIdArr     = array_column($vjournalList, 'op_id');
        $memberArr   = $memberModel->getMemberAccountByIdArr($opIdArr);

        foreach ($vjournalList as $key => $item) {
            if ($item['type'] == 1) {
                // 查询订单计时信息
                $timingOrderInfo                  = $timingOrderModel->getTimingOrder($item['ordernum']);
                $vjournalList[$key]['begin_time'] = empty($timingOrderInfo['begin_time']) ? '' : date('Y-m-d H:i:s',
                    $timingOrderInfo['begin_time']);
                $vjournalList[$key]['end_time']   = empty($timingOrderInfo['end_time']) ? '' : date('Y-m-d H:i:s',
                    $timingOrderInfo['end_time']);
                $vjournalList[$key]['tnum']       = $orderList[$item['ordernum']]['tnum'];
                $vjournalList[$key]['tprice']     = $orderList[$item['ordernum']]['tprice'];
                $vjournalList[$key]['ordertime']  = $orderList[$item['ordernum']]['ordertime'];
                $pName                            = '';
                foreach ($productArr as $p) {
                    if ($p['id'] == $orderList[$item['ordernum']]['pid']) {
                        $pName = $p['p_name'];
                    }
                }
                $vjournalList[$key]['memo'] = $pName;
            } else if (strpos($item['ordernum'], '_') !== false) {
                // 查询设备计时信息
                $ordernum = explode('_', $item['ordernum']);
                if (isset($ordernum[1]) && !empty($ordernum[1])) {
                    $equipId                          = $ordernum[1];
                    $timingEquipBindInfo              = $timingEquipBindModel->getEquipBindInfo($equipId, $item['sid'],
                        $item['vuid']);
                    $vjournalList[$key]['begin_time'] = empty($timingEquipBindInfo['get_time']) ? '' : date('Y-m-d H:i:s',
                        $timingEquipBindInfo['get_time']);
                    $vjournalList[$key]['end_time']   = empty($timingEquipBindInfo['back_time']) ? '' : date('Y-m-d H:i:s',
                        $timingEquipBindInfo['back_time']);
                    $vjournalList[$key]['tnum']       = $timingEquipBindInfo['num'];
                    $vjournalList[$key]['tprice']     = $timingEquipBindInfo['money_cost'];
                    $vjournalList[$key]['ordertime']  = date('Y-m-d H:i:s', $item['create_time']);
                }
            } else {
                $vjournalList[$key]['begin_time'] = '';
                $vjournalList[$key]['end_time']   = '';
                $vjournalList[$key]['tnum']       = '';
                $vjournalList[$key]['tprice']     = $item['money'];
                $vjournalList[$key]['ordertime']  = date('Y-m-d H:i:s', $item['create_time']);
            }

            $vjournalList[$key]['op_name'] = $memberArr[$item['op_id']];
        }

        $res['list']     = $vjournalList;
        $res['total']    = $total;
        $res['pageSize'] = $pageSize;

        $this->apiReturn(200, $res, '获取成功');
    }

    /**
     * 获取账户明细
     * @Author: zhujb
     * 2018/12/7
     */
    public function getAccountDetail()
    {
        $vuId = I('vuId', 0, 'intval');

        // 获取绑定信息
        $vuserModel               = new Vuser();
        $cardModel                = new BaseCard();
        $vuserInfo                = $vuserModel->getInfoByVuserId($vuId,
            'member_id,card_id,id,card_no,create_time,mobile,status,sid');
        $vuserInfo['status_text'] = $this->_vuserStatus[$vuserInfo['status']];

        // 获取用户信息
        $memberModel = new Member();
        $memberName  = $memberModel->getMemberInfo($vuserInfo['member_id'], 'id', 'dname')['dname'];

        // 获取计时卡信息
        $physicsNo = $cardModel->getCardInfoById($vuserInfo['card_id'], 'physics_no')['physics_no'];

        // 获取金额
        $depositApi  = new \Business\JavaApi\Fund\DepositApi();
        $depositArr  = $depositApi->getMemberDeposit($vuserInfo['member_id'], $vuserInfo['sid'], 1);
        $deposit     = empty($depositArr['data']['remainingAmount']) ? 0 : $depositArr['data']['remainingAmount'];
        $moneyArr    = $memberModel->balanceAndCredit($vuserInfo['member_id'], $vuserInfo['sid'], false);
        $credit      = $moneyArr['credit'];
        $total_money = $deposit + $credit;

        $vjournalModel    = new \Model\CardSolution\Vjournal();
        $allRechargeMoney = $vjournalModel->sumMoney($vuId, 0, [2, 3]);
        $allSpendMoney    = $vjournalModel->sumMoney($vuId, 1, [1, 4, 5, 6, 7], 3);

        $res = array(
            'member_id'          => $vuserInfo['member_id'],
            'card_no'            => $vuserInfo['card_no'],
            'create_time'        => date('Y-m-d H:i:s', $vuserInfo['create_time']),
            'mobile'             => $vuserInfo['mobile'],
            'dname'              => $memberName,
            'physics_no'         => $physicsNo,
            'deposit'            => $deposit,
            'credit'             => $credit,
            'total_money'        => $total_money,
            'all_recharge_money' => $allRechargeMoney,
            'all_spend_money'    => is_null($allSpendMoney) ? 0 : $allSpendMoney,
            'status_text'        => $vuserInfo['status_text'],
        );

        $this->apiReturn(200, $res, '获取成功');
    }

    /**
     * 计时报表汇总
     * @Author: zhujb
     * 2018/12/7
     */
    public function summaryParkTiming()
    {
        $summaryType = I('summary_type', 1, 'intval'); // 1：订单汇总，2：计时卡汇总

        $res = [];
        if ($summaryType == 1) {
            // 订单汇总
            $res = $this->_summaryOrder();
        }
        if ($summaryType == 2) {
            // 计时卡汇总
            $res = $this->_summaryTimingCard();
        }
        if ($summaryType == 3) {
            // 计时设备汇总
            $res = $this->_summaryTimingEquip();
        }

        $this->apiReturn(200, $res, '获取成功');
    }

    public function _summaryTimingEquip()
    {
        $beginTime = I('begin_time', '', 'strval');
        $endTime   = I('end_time', '', 'strval');
        $cardNo    = I('card_no', '', 'strval');
        $ordernum  = I('ordernum', '', 'strval');
        $isExport  = I('is_export', 0, 'strval');
        $sid       = I('sid', 0, 'strval');

        if (empty($sid) || empty($beginTime) || empty($endTime)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $timingModel    = new TimingOrder();
        $blankCardModel = new BlankCard();
        $baseCardModel  = new BaseCard();

        $cardId = 0;
        if ($cardNo) {
            $cardInfo  = $blankCardModel->getBlankCardInfoByCardNo($cardNo, 'physics_no');
            $physicsNo = $cardInfo['physics_no'] ?: '';
            $cardData  = $baseCardModel->getCardInfoByType('', $physicsNo);
            $cardId    = $cardData['id'] ?: 0;
        }

        $timeCardReportModel = new TimeCardReport();
        $summaryData         = $timeCardReportModel->getTimeCardReportData($sid, $beginTime, $endTime, $ordernum,
            $cardId, 'id,card_id,ordernum,equip_id,active_time,end_time,time_cost,
        money_cost,pay_type,money,total_money,create_time');

        // 获取实体卡号信息
        $cardIdArr           = array_column($summaryData, 'card_id');
        $physicsNoData       = $baseCardModel->getMulitCardInfo($cardIdArr, 'id,physics_no');
        $physicsNoArr        = array_column($physicsNoData, 'physics_no');
        $physicsNoWithCardId = [];
        foreach ($physicsNoData as $tmp) {
            $physicsNoWithCardId[$tmp['physics_no']] = $tmp['id'];
        }
        $blackCardData       = $blankCardModel->getAllBlankCardInfoByPhysicsNo($physicsNoArr, 'physics_no,entity_no');
        $physicsNoWithCardNo = [];
        foreach ($blackCardData as $tmp) {
            $physicsNoWithCardNo[$tmp['physics_no']] = $tmp['entity_no'];
        }
        $cardNoArr = [];
        foreach ($physicsNoWithCardNo as $physicsNo => $tmp) {
            $cardNoArr[$physicsNoWithCardId[$physicsNo]] = $tmp;
        }

        // 获取设备信息
        $equipIdArr       = array_column($summaryData, 'equip_id');
        $timingEquipModel = new TimingProduct();
        $timingEquipArr   = $timingEquipModel->selectTimingEquipment($equipIdArr, '');
        $timingEquipList  = [];
        foreach ($timingEquipArr as $equip) {
            $timingEquipList[$equip['id']] = $equip['equip_name'];
        }

        $payTypeText = [
            0 => '微信',
            1 => '支付宝',
            2 => '现金',
            3 => '计时卡',
            4 => '一卡通实体卡',
            5 => '一卡通虚拟卡',
            6 => '免费结算',
            7 => '奖励金支付',
        ];

        $res = [];
        foreach ($summaryData as $item) {
            $timeCost  = json_decode($item['time_cost'], true);
            $moneyCost = json_decode($item['money_cost'], true);

            $res[] = [
                'date'        => date('Y年m月d日', $item['create_time']),
                'card_no'     => $cardNoArr[$item['card_id']],
                'ordernum'    => $item['ordernum'] . "\t",
                'active_time' => date('Y/m/d H:i:s', $item['active_time']),
                'end_time'    => date('Y/m/d H:i:s', $item['end_time']),
                'equip_name'  => $timingEquipList[$item['equip_id']],
                'less_time'   => $timeCost['less_time'],
                'cache_time'  => $timeCost['cache_time'],
                'over_time'   => $timeCost['over_time'],
                'less_money'  => $moneyCost['less_money'],
                'cache_money' => $moneyCost['cache_money'],
                'over_money'  => $moneyCost['over_money'],
                'money'       => $item['money'],
                'pay_type'    => $payTypeText[$item['pay_type']] ?: '--',
                'total_money' => $item['total_money'],
            ];
        }

        // 取得列的列表
        $cardNo  = [];
        $dateArr = [];
        foreach ($res as $key => $row) {
            $cardNo[$key]  = $row['card_no'];
            $dateArr[$key] = $row['date'];
        }

        array_multisort($dateArr, SORT_ASC, $cardNo, SORT_ASC, $res);

        if ($isExport) {
            $file    = '计时订单汇总报表' . date('Y-m-d', strtotime($beginTime)) . '-' . date('Y-m-d', strtotime($endTime));
            $xls     = new SimpleExcel('UTF-8', true, 'orderList');
            $table[] = [
                '日期',
                '实体卡号',
                '订单号',
                '激活时间',
                '退卡时间',
                '设备',
                '消费时长（分钟）',
                '',
                '',
                '消费金额（元）',
                '',
                '',
                '小计',
                '支付方式',
                '总金额',
            ];
            $table[] = ['', '', '', '', '', '', '正常时长', '超时第一段时长', '超时第二段时长', '正常计费', '超时第一段计费', '超时第二段计费', '', '', ''];

            foreach ($res as $col => $row) {
                $row['less_money']  = $row['less_money'] / 100;
                $row['cache_money'] = $row['cache_money'] / 100;
                $row['over_money']  = $row['over_money'] / 100;
                $row['money']       = $row['money'] / 100;
                $row['total_money'] = $row['total_money'] / 100;
                $table[]            = $row;
            }

            $xls->addArray($table);
            $xls->generateXML($file);
            die;
        }

        return $res;
    }

    protected function _summaryOrder()
    {
        $beginTime = I('begin_time', '', 'strval');
        $endTime   = I('end_time', '', 'strval');
        $opId      = I('op_id', '', 'strval');
        $isSubsidy = I('is_subsidy', '', 'strval');
        $payType   = I('pay_type', '', 'strval');
        $productId = I('product_id', '', 'strval');
        $page      = I('page', 1, 'intval');
        $pageSize  = I('page_size', 10, 'intval');
        $isExport  = I('is_export', 0, 'intval');

        $page     = 1;
        $pageSize = 10000;

        $beginTime = $beginTime . ' 00:00:00';
        $endTime   = $endTime . ' 23:59:59';

        if (isset($_REQUEST['op_id'])) {
            $opId = explode(',', $opId);
        }

        if (isset($_REQUEST['is_subsidy'])) {
            $isSubsidy = explode(',', $isSubsidy);
        }

        if (isset($_REQUEST['pay_type'])) {
            $payType = explode(',', $payType);
        }

        if (isset($_REQUEST['product_id'])) {
            $productId = explode(',', $productId);
        }

        $vjournalModel    = new Vjournal();
        $timingOrderModel = new TimingOrder();
        $orderQueryModel  = new OrderTools();
        $memberModel      = new Member();
        $ticketModel      = new \Model\Product\Ticket();

        $vjournalList = $vjournalModel->getJournalOrders('', $beginTime, $endTime, [1, 6],
            'ordernum,pay_type,op_id,create_time,money,type');
        $orderNumArr  = array_column($vjournalList, 'ordernum');
        $opIdArr      = array_column($vjournalList, 'op_id');
        $orderList    = $orderQueryModel->getOrderInfo($orderNumArr,
            'ordernum,tnum,totalmoney,pid');
        $orderList    = array_key($orderList, 'ordernum');

        $pidArr         = array_column($orderList, 'pid');
        $timingOrderArr = $timingOrderModel->getTimingOrders($orderNumArr);
        $memberArr      = $memberModel->getMemberAccountByIdArr($opIdArr);
        $productArr     = $ticketModel->getProductList($pidArr, 'id,p_name');

        $timingOrderList = [];
        foreach ((array)$timingOrderArr as $value) {
            $timingOrderList[$value['ordernum']] = $value;
        }

        foreach ((array)$vjournalList as $key => $item) {
            $vjournalList[$key]                = $item;
            $vjournalList[$key]['create_time'] = date('Ymd', $item['create_time']);
            $vjournalList[$key]['op_name']     = $memberArr[$item['op_id']];
            if ($item['type'] == 1) {
                $vjournalList[$key]['tnum']       = $orderList[$item['ordernum']]['tnum'];
                $vjournalList[$key]['ordermoney'] = $orderList[$item['ordernum']]['totalmoney'];
            } else {
                $vjournalList[$key]['tnum']       = 0;
                $vjournalList[$key]['ordermoney'] = 0;
            }

            $vjournalList[$key]['pid']             = $orderList[$item['ordernum']]['pid'];
            $vjournalList[$key]['product_name']    = $productArr[$orderList[$item['ordernum']]['pid']]['p_name'];
            $vjournalList[$key]['is_subsidy']      = $timingOrderList[$item['ordernum']]['is_subsidy'];
            $vjournalList[$key]['is_subsidy_text'] = $this->_isSubsidy[$timingOrderList[$item['ordernum']]['is_subsidy']];
            $vjournalList[$key]['pay_type_text']   = $this->_payTypeText[$item['pay_type']];

            $vjournalList[$key]['pay_type_text'] = $this->_payTypeText[$item['pay_type']];
            $vjournalList[$key]['free']          = 0;
            if ($item['type'] == 6) {
                foreach ($vjournalList as $temp) {
                    if ($temp['ordernum'] == $item['ordernum'] && $temp['type'] == 1) {
                        $vjournalList[$key]['pay_type_text'] = $this->_payTypeText[$temp['pay_type']];
                    }
                    if ($temp['ordernum'] == $item['ordernum'] && $temp['type'] == 1) {
                        $vjournalList[$key]['op_name'] = $memberArr[$temp['op_id']];
                    }
                }
                $vjournalList[$key]['free'] = $item['money'];
            }
        }

        // 默认所有员工
        $staffArr     = $memberModel->getStaffByParentId($this->_memberInfo['sid']);
        $staffIdArr   = array_column($staffArr, 'son_id');
        $staffIdArr[] = $this->_memberInfo['sid'];

        // 根据条件过滤
        foreach ((array)$vjournalList as $key => $order) {
            if (!in_array($order['op_id'], $staffIdArr)) {
                unset($vjournalList[$key]);
            }
            if (!empty($opId) && !in_array($order['op_id'], $opId)) {
                unset($vjournalList[$key]);
            }
            if (!empty($payType) && !in_array($order['pay_type'], $payType)) {
                unset($vjournalList[$key]);
            }
            if (!empty($productId) && !in_array($order['pid'], $productId)) {
                unset($vjournalList[$key]);
            }
            if (!empty($isSubsidy) && !in_array($order['is_subsidy'], $isSubsidy)) {
                unset($vjournalList[$key]);
            }
        }

        $orderData = array_values($vjournalList);
        // 分页
        $total     = count($orderData);
        $orderData = array_slice($orderData, ($page - 1) * $pageSize, $pageSize);

        $data       = [];
        $totalTnum  = 0;
        $free       = 0;
        $orderMoney = 0;
        $totalMoney = 0;

        foreach ((array)$orderData as $item) {
            // 售票员数据
            $data['list'][$item['create_time']]['list'][$item['op_name']]['list'][$item['is_subsidy_text']]['list'][$item['pay_type_text']]['list'][$item['product_name']]['tnum']       += $item['tnum'];
            $data['list'][$item['create_time']]['list'][$item['op_name']]['list'][$item['is_subsidy_text']]['list'][$item['pay_type_text']]['list'][$item['product_name']]['free']       += $item['free'];
            $data['list'][$item['create_time']]['list'][$item['op_name']]['list'][$item['is_subsidy_text']]['list'][$item['pay_type_text']]['list'][$item['product_name']]['ordermoney'] += $item['ordermoney'];
            $data['list'][$item['create_time']]['list'][$item['op_name']]['list'][$item['is_subsidy_text']]['list'][$item['pay_type_text']]['list'][$item['product_name']]['totalmoney'] += $item['ordermoney'] + $item['free'];

            // 当天数据
            $data['list'][$item['create_time']]['date_total']['tnum']       += $item['tnum'];
            $data['list'][$item['create_time']]['date_total']['free']       += $item['free'];
            $data['list'][$item['create_time']]['date_total']['ordermoney'] += $item['ordermoney'];
            $data['list'][$item['create_time']]['date_total']['totalmoney'] += $item['ordermoney'] + $item['free'];

            // 总计数据
            $totalTnum  += $item['tnum'];
            $free       += $item['free'];
            $orderMoney += $item['ordermoney'];
            $totalMoney += $item['ordermoney'] + $item['free'];
        }

        $data['page_total'] = array(
            'span'        => '总计：',
            'total_tnum'  => $totalTnum,
            'order_money' => $orderMoney,
            'free'        => $free,
            'total_money' => $totalMoney,
        );

        // 表格处理
        $file    = '计时订单汇总报表' . date('Y-m-d', strtotime($beginTime)) . '-' . date('Y-m-d', strtotime($endTime));
        $table[] = array(
            date('Y-m-d', strtotime($beginTime)) . '-' . date('Y-m-d', strtotime($endTime)) . '订单汇总',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
        );
        $table[] = array('日期', '售票员', '订单计时状态', '支付方式', '产品', '总票数', '订单金额', '补费金额', '总金额');
        $table[] = array(
            $data['page_total']['span'],
            '',
            '',
            '',
            '',
            $data['page_total']['total_tnum'],
            $data['page_total']['order_money'],
            $data['page_total']['free'],
            $data['page_total']['total_money'],
        );
        foreach ((array)$data['list'] as $k1 => $v1) {
            foreach ($v1['list'] as $k2 => $v2) {
                foreach ($v2['list'] as $k3 => $v3) {
                    foreach ($v3['list'] as $k4 => $v4) {
                        foreach ($v4['list'] as $k5 => $v5) {
                            $table[] = array(
                                $k1,
                                $k2,
                                $k3,
                                $k4,
                                $k5,
                                $v5['tnum'],
                                $v5['ordermoney'],
                                $v5['free'],
                                $v5['totalmoney'],
                            );
                        }
                    }
                }
            }
            $table[] = array(
                $k1,
                '合计',
                '',
                '',
                '',
                $v1['date_total']['tnum'],
                $v1['date_total']['ordermoney'],
                $v1['date_total']['free'],
                $v1['date_total']['totalmoney'],
            );
        }

        if ($isExport) {
            $xls = new SimpleExcel('UTF-8', true, 'orderList');
            foreach ($table as $col => &$row) {
                if ($col >= 2) {
                    $row[6] = sprintf("%.2f", $row[6] / 100);
                    $row[7] = sprintf("%.2f", $row[7] / 100);
                    $row[8] = sprintf("%.2f", $row[8] / 100);
                }
            }
            $xls->addArray($table);
            $xls->generateXML($file);
            die;
        }

        unset($table[0]);
        unset($table[1]);
        $res = array(
            'table'    => array_values($table),
            'total'    => $total,
            'pageSize' => $pageSize,
        );

        return $res;
    }

    protected function _summaryTimingCard()
    {
        $beginTime = I('begin_time', '', 'strval');
        $endTime   = I('end_time', '', 'strval');
        $opId      = I('op_id', 0, 'intval');
        $page      = I('page', 1, 'intval');
        $pageSize  = I('page_size', 10, 'intval');
        $isExport  = I('is_export', 0, 'intval');

        $page     = 1;
        $pageSize = 10000;

        $beginTime = $beginTime . ' 00:00:00';
        $endTime   = $endTime . ' 23:59:59';

        if (isset($_REQUEST['op_id'])) {
            $opId = strpos($opId, ',') !== false ? explode(',', $opId) : [$opId];
        }

        $vjournalModel    = new Vjournal();
        $vuserModel       = new Vuser();
        $timingOrderModel = new TimingOrder();
        $orderQueryModel  = new OrderQuery();
        $memberModel      = new Member();
        $ticketModel      = new \Model\Product\Ticket();

        $vuserList = $timingOrderModel->getParkCardVuserList($this->_memberInfo['sid'], '', '', $beginTime, $endTime,
            '', $opId, 'id,op_id,status,create_time,unbind_time', $page, $pageSize);
        $total     = $timingOrderModel->getParkCardVuserCount($this->_memberInfo['sid'], '', '', $beginTime, $endTime,
            '', $opId);

        $vuserIdArr   = array_column($vuserList, 'id');
        $vjournalList = $vjournalModel->getJournalOrders($vuserIdArr, $beginTime, $endTime, [2, 3, 4, 5, 7],
            'vuid,type,money,op_id,pay_type,action');
        foreach ((array)$vjournalList as $temp) {
            $vjournalArr[$temp['vuid']][] = $temp;
        }

        $vuserData = [];
        foreach ((array)$vuserList as $vuser) {
            $vuser['vjournal'] = $vjournalArr[$vuser['id']];
            $vuserData[]       = $vuser;
        }

        $vuserArr = [];

        foreach ((array)$vuserData as $key => $value) {
            $vuserArr[$key]['create_time'] = date('Ymd', $value['create_time']);
            $vuserArr[$key]['op_name']     = $memberModel->getMemberInfo($value['op_id'], 'id', 'dname')['dname'];
            // 开卡数
            if (strtotime($beginTime) <= $value['create_time'] && $value['create_time'] <= strtotime($endTime)) {
                $vuserArr[$key]['active_num'] = 1;
            }
            // 退卡数
            if (strtotime($beginTime) <= $value['unbind_time'] && $value['unbind_time'] <= strtotime($endTime)) {
                $vuserArr[$key]['refund_num'] = 1;
            }

            $vuserArr[$key]['cash']           = 0;
            $vuserArr[$key]['wxpay']          = 0;
            $vuserArr[$key]['alipay']         = 0;
            $vuserArr[$key]['money']          = 0;
            $vuserArr[$key]['refund_money']   = 0;
            $vuserArr[$key]['repairing_card'] = 0;
            foreach ((array)$value['vjournal'] as $subItem) {
                // 现金
                if ($subItem['action'] == 0 && $subItem['pay_type'] == 2 && in_array($subItem['type'], [2, 3])) {
                    $vuserArr[$key]['cash'] += $subItem['money'];
                }
                // 微信
                if ($subItem['action'] == 0 && $subItem['pay_type'] == 0 && in_array($subItem['type'], [2, 3])) {
                    $vuserArr[$key]['wxpay'] += $subItem['money'];
                }
                // 支付宝
                if ($subItem['action'] == 0 && $subItem['pay_type'] == 1 && in_array($subItem['type'], [2, 3])) {
                    $vuserArr[$key]['alipay'] += $subItem['money'];
                }
                // 总金额
                if ($subItem['action'] == 0 && in_array($subItem['type'], [2, 3])) {
                    $vuserArr[$key]['money'] += $subItem['money'];
                }
                // 退卡金额
                if ($subItem['action'] == 1 && in_array($subItem['type'], [4, 5])) {
                    $vuserArr[$key]['refund_money'] += $subItem['money'];
                }
                // 补卡费
                if ($subItem['action'] == 1 && in_array($subItem['type'], [7])) {
                    $vuserArr[$key]['repairing_card'] += $subItem['money'];
                }
            }
        }

        $data               = [];
        $totalActiveNum     = 0;
        $totalCash          = 0;
        $totalAlipay        = 0;
        $totalWxpay         = 0;
        $totalMoney         = 0;
        $totalRefundNum     = 0;
        $totalRefundMoney   = 0;
        $totalRepairingCard = 0;
        foreach ((array)$vuserArr as $item) {
            // 售票员数据
            $data['list'][$item['create_time']]['list'][$item['op_name']]['active_num']     += $item['active_num'];
            $data['list'][$item['create_time']]['list'][$item['op_name']]['cash']           += $item['cash'];
            $data['list'][$item['create_time']]['list'][$item['op_name']]['wxpay']          += $item['wxpay'];
            $data['list'][$item['create_time']]['list'][$item['op_name']]['alipay']         += $item['alipay'];
            $data['list'][$item['create_time']]['list'][$item['op_name']]['money']          += $item['money'];
            $data['list'][$item['create_time']]['list'][$item['op_name']]['refund_num']     += $item['refund_num'];
            $data['list'][$item['create_time']]['list'][$item['op_name']]['refund_money']   += $item['refund_money'];
            $data['list'][$item['create_time']]['list'][$item['op_name']]['repairing_card'] += $item['repairing_card'];
            // 当天合计
            $data['list'][$item['create_time']]['date_total']['active_num']     += $item['active_num'];
            $data['list'][$item['create_time']]['date_total']['cash']           += $item['cash'];
            $data['list'][$item['create_time']]['date_total']['wxpay']          += $item['wxpay'];
            $data['list'][$item['create_time']]['date_total']['alipay']         += $item['alipay'];
            $data['list'][$item['create_time']]['date_total']['money']          += $item['money'];
            $data['list'][$item['create_time']]['date_total']['refund_num']     += $item['refund_num'];
            $data['list'][$item['create_time']]['date_total']['refund_money']   += $item['refund_money'];
            $data['list'][$item['create_time']]['date_total']['repairing_card'] += $item['repairing_card'];
            // 当前页合计
            $totalActiveNum     += $item['active_num'];
            $totalCash          += $item['cash'];
            $totalWxpay         += $item['wxpay'];
            $totalAlipay        += $item['alipay'];
            $totalMoney         += $item['money'];
            $totalRefundNum     += $item['refund_num'];
            $totalRefundMoney   += $item['refund_money'];
            $totalRepairingCard += $item['repairing_card'];
        }

        $data['page_total'] = array(
            'span'                 => '总计：',
            'total_active_num'     => $totalActiveNum,
            'total_cash'           => $totalCash,
            'total_wxpay'          => $totalWxpay,
            'total_alipay'         => $totalAlipay,
            'total_money'          => $totalMoney,
            'total_repairing_card' => $totalRepairingCard,
            'total_refund_num'     => $totalRefundNum,
            'total_refund_money'   => $totalRefundMoney,
        );

        // 表格处理
        $file    = '计时卡汇总报表' . date('Y-m-d', strtotime($beginTime)) . '-' . date('Y-m-d', strtotime($endTime));
        $table[] = array(
            date('Y-m-d', strtotime($beginTime)) . '-' . date('Y-m-d', strtotime($endTime)) . '计时卡汇总',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
        );
        $table[] = array('日期', '售票员', '开卡数', '现金充值金额', '微信充值金额', '支付宝充值金额', '充值总金额', '补卡金额', '退卡数', '退卡金额');
        $table[] = array(
            $data['page_total']['span'],
            '',
            $data['page_total']['total_active_num'],
            $data['page_total']['total_cash'],
            $data['page_total']['total_wxpay'],
            $data['page_total']['total_alipay'],
            $data['page_total']['total_money'],
            $data['page_total']['total_repairing_card'],
            $data['page_total']['total_refund_num'],
            $data['page_total']['total_refund_money'],
        );

        foreach ((array)$data['list'] as $k1 => $v1) {
            foreach ($v1['list'] as $k2 => $v2) {
                $table[] = array(
                    $k1,
                    $k2,
                    $v2['active_num'],
                    $v2['cash'],
                    $v2['wxpay'],
                    $v2['alipay'],
                    $v2['money'],
                    $v2['repairing_card'],
                    $v2['refund_num'],
                    $v2['refund_money'],
                );
            }
            $table[] = array(
                $k1,
                '合计',
                $v1['date_total']['active_num'],
                $v1['date_total']['cash'],
                $v1['date_total']['wxpay'],
                $v1['date_total']['alipay'],
                $v1['date_total']['money'],
                $v1['date_total']['repairing_card'],
                $v1['date_total']['refund_num'],
                $v1['date_total']['refund_money'],
            );
        }

        if ($isExport) {
            foreach ($table as $col => &$row) {
                if ($col >= 2) {
                    $row[3] = round($row[3] / 100, 2);
                    $row[4] = round($row[4] / 100, 2);
                    $row[5] = round($row[5] / 100, 2);
                    $row[6] = round($row[6] / 100, 2);
                    $row[7] = round($row[7] / 100, 2);
                    $row[9] = round($row[9] / 100, 2);
                }
            }
            $xls = new SimpleExcel('UTF-8', true, 'orderList');
            $xls->addArray($table);
            $xls->generateXML($file);
            die;
        }

        unset($table[0]);
        unset($table[1]);
        $res = array(
            'table'    => array_values($table),
            'total'    => $total,
            'pageSize' => $pageSize,
        );

        return $res;
    }

    /**************************新版计时报表************************************/

    public function timingOrderDetailSummary()
    {
        //开始时间
        $begin = I('begin', '', 'strval,trim');
        //结束时间
        $end = I('end', '', 'strval,trim');
        //模板id
        $configId = I('search_config_id', 0, 'intval');
        //页数
        $page = I('page', 1, 'intval');
        //每页数量
        $pageSize = I('page_size', 15, 'intval');
        //是否导出
        $excel = I('export_excel', 0, 'intval');
        //1 按日  2 按月
        $dateType = I('date_type', 1, 'intval');

        $code = 200;
        $data = [];
        $msg  = '';

        try {
            $templateModel = new \Model\Report\Template();
            $template      = $templateModel->getTemplateById('', $configId);
            if (empty($template)) {
                throw new \Exception("模板配置未知");
            }

            //统计纬度
            $item = json_decode($template['item'], true);
            if (empty($item) || !is_array($item)) {
                throw new \Exception("配置有误, 统计纬度不能为空");
            }

            //可以被统计的纬度 清除掉没用的 整理纬度对应的汉字描述
            $needItem = [
                'date_time'        => '日期',
                'operate_id'       => '售票员',
                'lid'              => '景区',
                'tid'              => '票类',     // @date 2021年07月02日 增加门票维度
                'pay_way'          => '订单支付方式',
                'overtime_pay_way' => '超时补费支付方式',
            ];
            foreach ($item as $key => &$value) {
                if ($value == 'date') {
                    $value = 'date_time';
                }
                if (!array_key_exists($value, $needItem)) {
                    unset($item[$key]);
                    continue;
                }

                $titleArr[] = $needItem[$value];
            }
            $item = array_values($item);
            //报表名称
            $name = $template['name'];

            //统计指标
            $target = json_decode($template['target'], true);
            if (empty($target) || !is_array($target)) {
                $target = [];
            }
            //var_dump($item);exit;

            $group = empty($item) ? '' : implode(",", $item);

            $timingOrderDetailModel = new TimingOrderDetail();
            $orderDetailArr         = $timingOrderDetailModel->getTimingOrderDetailList($this->_opIdArr, $begin, $end,
                $group, $excel, $page, $pageSize);
            //$total                  = $timingOrderDetailModel->getTimingOrderDetailCount($this->_opIdArr, $begin, $end);

            $list  = $orderDetailArr['list'];
            //var_dump($list);exit;
            $total = $orderDetailArr['total'];
            $sum   = $orderDetailArr['sum'];

            $lidArr    = array_unique(array_column($list, 'lid'));
            //$landModel = new \Model\Product\Land('slave');
            //$landArr   = $landModel->getLandInfoByLids($lidArr, 'id,title', 1, 1000);

            $javaAPi = new \Business\CommodityCenter\Land();
            $landArr = $javaAPi->queryLandMultiQueryById($lidArr);
            foreach ((array)$landArr as $land) {
                $this->landData[$land['id']] = $land;
            }
            $tidArr = array_unique(array_column($list, 'tid'));
            if(is_array($tidArr) && count($tidArr)){    // 如果票ID数组不为空，这填充 $this->ticketData 数组
            	$this->_fillTicketData($tidArr);
            }
            
            $opIdArr          = array_unique(array_column($list, 'operate_id'));
            $memberBiz        = new \Business\Member\Member();
            $this->memberData = $memberBiz->getMemberInfoByMulti($opIdArr, 'id', true);
            $newList          = $this->_handleTimingOrderList($list, $item, $dateType);

            //处理总和
            $sumNew['tnum']        = isset($sum['tnum']) ? intval($sum['tnum']) : 0;
            $sumNew['order_money'] = round($sum['order_money'] / 100, 2);
            $sumNew['fee_money']   = round($sum['fee_money'] / 100, 2);
            $sumNew['money']       = round($sum['money'] / 100, 2);
            $sumNew['deposit_amount']       = round($sum['deposit_amount'] / 100, 2);
            

            //列表
            $data['list'] = $newList;
            //总和
            $data['sum'] = $sumNew;
            //最细纬度下的总条数
            $data['total'] = $total;
            //纬度名称
            $data['title'] = $titleArr;
            //报表名称
            $data['name'] = $name;
            //统计指标
            $data['target'] = $target;
            //统计方式 按月/按日
            $data['date_type'] = $dateType;

        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        if ($excel && $code == 200) {
            $this->_timingOrderExportData($data);
        } else {
            $this->apiReturn($code, $data, $msg);
        }
    }

    /**
     * 获取免结算的计时订单列表
     *
     * @date 2022/05/10
     * @auther yangjianhui
     * @return array
     */
    public function getFreeTimingOrderList()
    {
        $physicsNo = I('post.physics_no', '', 'strval');
        $orderNum  = I('post.order_num', '', 'strval');
        $begin     = I('post.begin', '', 'strval');
        $end       = I('post.end', '', 'strval');
        $page      = I('post.page', 1, 'intval');
        $size      = I('post.size', 10, 'intval');
        $opId      = I('post.op_id', 0, 'intval');
        $state     = I('post.state', 0, 'intval');
        
        $timeCardJsonRpc = new TimeCard();
        $result          = $timeCardJsonRpc->getFreeTimingOrderList($this->_memberInfo['sid'], $begin, $end, $physicsNo, $orderNum,
            $opId, $state, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 修改免结算的计时订单状态
     *
     *
     * @date 2022/05/11
     * @auther yangjianhui
     * @return array
     */
    public function changeFreeOrderStatus()
    {
        $id    = I('post.id', 0, 'intval');
        $state = I('post.state', 1, 'intval');
        if (empty($id) || !in_array($state, [2, 3])) {
            $this->apiReturn(203, [], "参数错误");
        }
        $timeCardJsonRpc = new TimeCard();
        $result          = $timeCardJsonRpc->changeFreeOrderStatus($this->_memberInfo['sid'], $this->_memberInfo['memberID'], $id, $state);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


    public function _handleTimingOrderList($list, $latitude, $dataType = 0)
    {
        $newList = [];
        
        foreach ($list as $item) {
            // 倒序拼接
            $itemPoint = end($latitude);
            $resKey    = $item[$itemPoint] . ' ';
            $title     = $this->_setTitle($item[$itemPoint], $itemPoint, $dataType);

            $resArr = [
                $resKey => [
                    'title' => $title,
                    'list'  => [
                        'tnum'        => $item['tnum'],
                        'order_money' => round($item['order_money'] / 100, 2),
                        'fee_money'   => round($item['fee_money'] / 100, 2),
                        'money'       => round($item['money'] / 100, 2),
                        'deposit_amount'       => round($item['deposit_amount'] / 100, 2),
                    ],
                ],
            ];
            while (prev($latitude)) {
                $itemPoint = current($latitude);
                $resKey    = $item[$itemPoint] . ' ';
                $tmpArr    = [
                    $resKey => [
                        'title' => $this->_setTitle($item[$itemPoint], $itemPoint, $dataType),
                        'list'  => $resArr,
                    ],
                ];

                $resArr = $tmpArr;
            }

            if (empty($newList)) {
                $newList[current(array_keys($resArr))] = current(array_values($resArr));
            } else {
                $newList = array_merge_recursive($newList, $resArr);
            }
        }

        foreach ($newList as $key => &$value) {
            $this->_handleReportFinishData($value);
        }

        return $newList;
    }

    public function _setTitle($value, $key, $dateType)
    {
        switch ($key) {
            case 'date_time':
                if ($dateType == 1) {
                    $value = date('Ym', strtotime($value));
                }
                break;
            case 'lid':
                $value = isset($this->landData[$value]['title']) ? $this->landData[$value]['title'] : '未知';
                break;
	        case 'tid':
		        $value = isset($this->ticketData[$value]['title']) ? $this->ticketData[$value]['title'] : '未知';
		        break;
            case 'operate_id':
                $value = isset($this->memberData[$value]['dname']) ? $this->memberData[$value]['dname'] : '未知';;
                break;
            case 'pay_way':
                $value = isset($this->_payTypeText[$value]) ? $this->_payTypeText[$value] : '未知';
                break;
            case 'overtime_pay_way':
                $value = $value > -1 ? (isset($this->_payTypeText[$value]) ? $this->_payTypeText[$value] : '未知') : '无需超时补费';
                break;
        }

        return $value;
    }

    public function timingCardDetailSummary()
    {
        //开始时间
        $begin = I('begin', '', 'strval,trim');
        //结束时间
        $end = I('end', '', 'strval,trim');
        //模板id
        $configId = I('search_config_id', 0, 'intval');
        //页数
        $page = I('page', 1, 'intval');
        //每页数量
        $pageSize = I('page_size', 15, 'intval');
        //是否导出
        $excel = I('export_excel', 0, 'intval');
        //1 按日  2 按月
        $dateType = I('date_type', 1, 'intval');
        $pageSize = 10000;
        $code = 200;
        $data = [];
        $msg  = '';

        try {
            $templateModel = new \Model\Report\Template();
            $template      = $templateModel->getTemplateById('', $configId);
            if (empty($template)) {
                throw new \Exception("模板配置未知");
            }

            //统计纬度
            $item = json_decode($template['item'], true);
            if (empty($item) || !is_array($item)) {
                throw new \Exception("配置有误, 统计纬度不能为空");
            }

            //可以被统计的纬度 清除掉没用的 整理纬度对应的汉字描述
            $needItem = ['date_time' => '日期', 'operate_id' => '售票员'];
            foreach ($item as $key => &$value) {
                if ($value == 'date') {
                    $value = 'date_time';
                }
                if (!array_key_exists($value, $needItem)) {
                    unset($item[$key]);
                }

                $titleArr[] = $needItem[$value];
            }

            $item = array_values($item);
            //报表名称
            $name = $template['name'];

            //统计指标
            $target = json_decode($template['target'], true);
            if (empty($target) || !is_array($target)) {
                $target = [];
            }

            $timingCardDetailModel = new TimingCardDetail();
            $cardDetailArr         = $timingCardDetailModel->getTimingCardDetailList($this->_opIdArr, $begin, $end,
                $page, $pageSize);
            $total                 = $timingCardDetailModel->getTimingCardDetailCount($this->_opIdArr, $begin, $end);

            $opIdArr          = array_unique(array_column($cardDetailArr, 'operate_id'));
            $memberModel      = new Member('slave');
            $this->memberData = $memberModel->getMemberInfoByMulti($opIdArr, 'id', 'id,dname', true);
			
            $res  = $this->_handleTimingCardList($cardDetailArr, $item, $dateType);
            $list = $res['list'];
            $sum  = $res['sum'];
            
            //列表
            $data['list'] = $list;
            //总和
            $data['sum'] = $sum;
            //最细纬度下的总条数
            $data['total'] = $total;
            //纬度名称
            $data['title'] = $titleArr;
            //报表名称
            $data['name'] = $name;
            //统计指标
            $data['target'] = $target;
            //统计方式 按月/按日
            $data['date_type'] = $dateType;

        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }
        if ($excel && $code == 200) {
            $this->_timingCardExportData($data);
        } else {
            $this->apiReturn($code, $data, $msg);
        }
    }
    
    // 添加一个方法把 pft_timing_order_detail 中的tid全部填充了。
	// pft_timing_order_detail 有ordernum， 通过 ordernum 获取对应的tid填充。
	//
	
	

    public function _handleTimingCardList($list, $latitude, $dataType = 0)
    {
        $newList  = [];
        $sum      = [];
        $latCount = count($latitude);
        foreach ($list as $item) {
            $item['cash_recharge']      = round($item['cash_recharge'] / 100, 2);
            $item['cash_refund_card']   = round($item['cash_refund_card'] / 100, 2);
            $item['cash_income']        = round($item['cash_income'] / 100, 2);
            $item['wx_recharge']        = round($item['wx_recharge'] / 100, 2);
            $item['wx_refund_card']     = round($item['wx_refund_card'] / 100, 2);
            $item['wx_income']          = round($item['wx_income'] / 100, 2);
            $item['alipay_recharge']    = round($item['alipay_recharge'] / 100, 2);
            $item['alipay_refund_card'] = round($item['alipay_refund_card'] / 100, 2);
            $item['alipay_income']      = round($item['alipay_income'] / 100, 2);
            $item['recharge_money']     = round($item['recharge_money'] / 100, 2);
            $item['refund_card_money']  = round($item['refund_card_money'] / 100, 2);
            $item['income_money']       = round($item['income_money'] / 100, 2);
            $item['patch_money']        = round($item['patch_money'] / 100, 2);
            $item['open_card_money']    = round($item['open_card_money'] / 100, 2);
            $item['award_card']         = round($item['award_card'] / 100, 2);
            switch ($latCount) {
                case 1:
                    $one = $latitude[0];

                    $newList[$item[$one]]['title'] = $this->_setTitle($item[$one], $one, $dataType);

                    $newList[$item[$one]]['list']['cash_recharge']      += $item['cash_recharge'];
                    $newList[$item[$one]]['list']['cash_refund_card']   += $item['cash_refund_card'];
                    $newList[$item[$one]]['list']['cash_income']        += $item['cash_income'];
                    $newList[$item[$one]]['list']['wx_recharge']        += $item['wx_recharge'];
                    $newList[$item[$one]]['list']['wx_refund_card']     += $item['wx_refund_card'];
                    $newList[$item[$one]]['list']['wx_income']          += $item['wx_income'];
                    $newList[$item[$one]]['list']['alipay_recharge']    += $item['alipay_recharge'];
                    $newList[$item[$one]]['list']['alipay_refund_card'] += $item['alipay_refund_card'];
                    $newList[$item[$one]]['list']['alipay_income']      += $item['alipay_income'];
                    $newList[$item[$one]]['list']['recharge_money']     += $item['recharge_money'];
                    $newList[$item[$one]]['list']['refund_card_money']  += $item['refund_card_money'];
                    $newList[$item[$one]]['list']['income_money']       += $item['income_money'];
                    $newList[$item[$one]]['list']['open_card']          += $item['open_card'];
                    $newList[$item[$one]]['list']['refund_card']        += $item['refund_card'];
                    $newList[$item[$one]]['list']['award_card']         += $item['award_card'];
                    $newList[$item[$one]]['list']['patch_card']         += $item['patch_card'];
                    $newList[$item[$one]]['list']['patch_money']        += $item['patch_money'];
                    $newList[$item[$one]]['list']['open_card_money']    += $item['open_card_money'];
                    break;
                case 2:
                    $one = $latitude[0];
                    $two = $latitude[1];

                    $newList[$item[$one]]['title']                      = $this->_setTitle($item[$one], $one,
                        $dataType);
                    $newList[$item[$one]]['list'][$item[$two]]['title'] = $this->_setTitle($item[$two], $two,
                        $dataType);

                    $newList[$item[$one]]['list'][$item[$two]]['list']['cash_recharge']      += $item['cash_recharge'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['cash_refund_card']   += $item['cash_refund_card'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['cash_income']        += $item['cash_income'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['wx_recharge']        += $item['wx_recharge'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['wx_refund_card']     += $item['wx_refund_card'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['wx_income']          += $item['wx_income'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['alipay_recharge']    += $item['alipay_recharge'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['alipay_refund_card'] += $item['alipay_refund_card'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['alipay_income']      += $item['alipay_income'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['recharge_money']     += $item['recharge_money'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['refund_card_money']  += $item['refund_card_money'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['income_money']       += $item['income_money'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['open_card']          += $item['open_card'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['refund_card']        += $item['refund_card'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['award_card']         += $item['award_card'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['patch_card']         += $item['patch_card'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['patch_money']        += $item['patch_money'];
                    $newList[$item[$one]]['list'][$item[$two]]['list']['open_card_money']    += $item['open_card_money'];
                    break;
            }

            // 这是总计
            $sum['cash_recharge']      += $item['cash_recharge'];
            $sum['cash_refund_card']   += $item['cash_refund_card'];
            $sum['cash_income']        += $item['cash_income'];
            $sum['wx_recharge']        += $item['wx_recharge'];
            $sum['wx_refund_card']     += $item['wx_refund_card'];
            $sum['wx_income']          += $item['wx_income'];
            $sum['alipay_recharge']    += $item['alipay_recharge'];
            $sum['alipay_refund_card'] += $item['alipay_refund_card'];
            $sum['alipay_income']      += $item['alipay_income'];
            $sum['recharge_money']     += $item['recharge_money'];
            $sum['refund_card_money']  += $item['refund_card_money'];
            $sum['income_money']       += $item['income_money'];
            $sum['open_card']          += $item['open_card'];
            $sum['refund_card']        += $item['refund_card'];
            $sum['award_card']         += $item['award_card'];
            $sum['patch_card']         += $item['patch_card'];
            $sum['patch_money']        += $item['patch_money'];
            $sum['open_card_money']    += $item['open_card_money'];
        }

        $res = [
            'list' => $newList,
            'sum'  => $sum,
        ];

        return $res;
    }

    /**
     * 计时订单报表导出
     *
     * <AUTHOR>
     * @date   2019-12-10
     *
     * @params  array       $data       数据
     *
     * @return
     */
    private function _timingOrderExportData($data)
    {
        $filename = date('Ymd') . '计时订单报表';

        //数据列表
        $list = $data['list'];
        //报表名称
        $name = $data['name'];
        //纬度
        $title = $data['title'];
        //总和
        $sum = $data['sum'];
        //统计指标
        $target = $data['target'];

        if (empty($target)) {
            $target = [];
        }

        // var_dump($name);exit;
        $excel[0] = [
            $name,
        ];

        //配置的纬度
        foreach ((array)$title as $value) {
            $excel[1][] = $value;
        }

        //配置的统计指标
        if (!empty($target)) {
            $needItem = ['tnum' => '票数', 'order_money' => '订单金额', 'fee_money' => '补费金额', 'money' => '总金额', 'deposit_amount' => '订单押金'];
            foreach ((array)$target as $key => $value) {
                if (isset($needItem[$value])) {
                    $excel[1][] = $needItem[$value];
                }

            }
        }
        //var_dump($list);exit;

        //从第二行开始
        $i = 2;
        foreach ((array)$list as $valueOne) {
            switch (count($title)) {
                case 1:
                    $excel[$i][] = $valueOne['title'];

                    foreach ($target as $item) {
                        $excel[$i][] = $valueOne['list'][$item];
                    }

                    $i++;
                    break;

                case 2:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        $excel[$i][] = $valueOne['title'];
                        $excel[$i][] = $valueTwo['title'];

                        foreach ($target as $item) {
                            $excel[$i][] = $valueTwo['list'][$item];
                        }

                        $i++;
                    }

                    break;
                case 3:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            $excel[$i][] = $valueOne['title'];
                            $excel[$i][] = $valueTwo['title'];
                            $excel[$i][] = $valueThree['title'];

                            foreach ($target as $item) {
                                $excel[$i][] = $valueThree['list'][$item];
                            }

                            $i++;
                        }
                    }
                    break;
                case 4:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                $excel[$i][] = $valueOne['title'];
                                $excel[$i][] = $valueTwo['title'];
                                $excel[$i][] = $valueThree['title'];
                                $excel[$i][] = $valueFour['title'];

                                foreach ($target as $item) {
                                    $excel[$i][] = $valueFour['list'][$item];
                                }

                                $i++;
                            }
                        }
                    }
                    break;
	            case 5:
		            foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
			            foreach ($valueTwo['list'] as $keyThree => $valueThree) {
				            foreach ($valueThree['list'] as $keyFour => $valueFour) {
					            foreach ($valueFour['list'] as $keyFive => $valueFive) {
						            $excel[$i][] = $valueOne['title'];
						            $excel[$i][] = $valueTwo['title'];
						            $excel[$i][] = $valueThree['title'];
						            $excel[$i][] = $valueFour['title'];
						            $excel[$i][] = $valueFive['title'];
						
						            foreach ($target as $item) {
							            $excel[$i][] = $valueFive['list'][$item];
						            }
						
						            $i++;
					            }
				            }
			            }
		            }
		            break;
	            case 6:
		            foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
			            foreach ($valueTwo['list'] as $keyThree => $valueThree) {
				            foreach ($valueThree['list'] as $keyFour => $valueFour) {
					            foreach ($valueFour['list'] as $keyFive => $valueFive) {
						            foreach ($valueFive['list'] as $keySix => $valueSix){
							            $excel[$i][] = $valueOne['title'];
							            $excel[$i][] = $valueTwo['title'];
							            $excel[$i][] = $valueThree['title'];
							            $excel[$i][] = $valueFour['title'];
							            $excel[$i][] = $valueFive['title'];
							            $excel[$i][] = $valueSix['title'];
							
							            foreach ($target as $item) {
								            $excel[$i][] = $valueSix['list'][$item];
							            }
							
							            $i++;
						            }
					            }
				            }
			            }
		            }
		            break;
            }
        }
        $total[] = '总计';
        for ($i = 0; $i < count($title) - 1; $i++) {
            $total[] = '';
        }
        foreach ($target as $temp) {
            array_push($total, $sum[$temp]);
        }

        $excel[] = $total;
        $xls     = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }

    /**
     * 计时卡报表导出
     *
     * <AUTHOR>
     * @date   2019-12-10
     *
     * @params  array       $data       数据
     *
     * @return
     */
    private function _timingCardExportData($data)
    {
        $filename = date('Ymd') . '计时卡报表';

        //数据列表
        $list = $data['list'];
        //报表名称
        $name = $data['name'];
        //纬度
        $title = $data['title'];
        //总和
        $sum = $data['sum'];
        //统计指标
        $target = $data['target'];

        if (empty($target)) {
            $target = [];
        }

        $excel[0] = [
            $name,
        ];

        //配置的纬度
        foreach ($title as $value) {
            $excel[1][] = $value;
        }

        //配置的统计指标
        if (!empty($target)) {
            $needItem = [
                'cash_recharge'      => '现金充值金额',
                'cash_refund_card'   => '现金退卡金额',
                'cash_income'        => '现金收入金额',
                'wx_recharge'        => '微信充值金额',
                'wx_refund_card'     => '微信退卡金额',
                'wx_income'          => '微信收入金额',
                'alipay_recharge'    => '支付宝充值金额',
                'alipay_refund_card' => '支付宝退卡金额',
                'alipay_income'      => '支付宝收入',
                'recharge_money'     => '充值金额',
                'refund_card_money'  => '退卡金额',
                'income_money'       => '收入金额',
                'open_card'          => '开卡数量',
                'refund_card'        => '退卡数量',
                'award_card'         => '充值奖励金金额',
                'patch_card'         => '补卡数量',
                'patch_money'        => '补卡金额',
                'open_card_money'    => '开卡工本费',
            ];
            foreach ((array)$target as $key => $value) {
                if (isset($needItem[$value])) {
                    $excel[1][] = $needItem[$value];
                }
            }
        }

        //从第二行开始
        $i = 2;
        foreach ($list as $valueOne) {
            switch (count($title)) {
                case 1:
                    $excel[$i][] = $valueOne['title'];

                    foreach ($target as $item) {
                        $excel[$i][] = $valueOne['list'][$item];
                    }
                    $i++;
                    break;

                case 2:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        $excel[$i][] = $valueOne['title'];
                        $excel[$i][] = $valueTwo['title'];

                        foreach ($target as $item) {
                            $excel[$i][] = $valueTwo['list'][$item];
                        }
                        $i++;
                    }

                    break;
            }
        }

        $total[] = '总计';
        for ($i = 0; $i < count($title) - 1; $i++) {
            $total[] = '';
        }

        foreach ($target as $temp) {
            array_push($total, $sum[$temp]);
        }

        $excel[] = $total;
        $xls     = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }

    /**
     * 递归处理数据
     *
     * @param $arr
     */
    private function _handleReportFinishData(&$arr)
    {
        if (is_array($arr['title'])) {
            $arr['title'] = $arr['title'][0];
        }

        // 判断是否最后一层
        if (!is_numeric(trim(current(array_keys($arr['list']))))) {
            $arr['list'] = array_map(function ($v) {
                if (is_array($v)) {
                    return array_sum($v);
                }

                return $v;
            }, $arr['list']);

            return;
        } else {
            foreach ($arr['list'] as &$value) {
                $this->_handleReportFinishData($value);
            }
        }
    }
	
	/**
	 * 通过tid数组获取票信息。保存票id和titile到 ticketData
	 * @param $tids
	 */
    private function _fillTicketData($tids){
	    $ticketApi = new Ticket();
	    $ticketList = $ticketApi->queryTicketInfoByIds($tids, 'id,title');
	    $ret = [];
	    if(count($ticketList)){
		    foreach($ticketList as $ticketInfo){
			    $tid = $ticketInfo['ticket']['id'];
			    $ret[$tid] = $ticketInfo['ticket'];
		    }
	    }
	    $this->ticketData = $ret;
    }
}
