<?php

namespace Controller\Product;

use Business\Authority\AuthContext;
use Business\Authority\DataAuthLogic;
use Business\MemberLogin\MemberLoginHelper;
use Business\Product\SpecialtyV2;
use Library\Cache\Cache;
use Library\Controller;

/**
 * 特产产品相关接口，包括商品信息、票属性、价格库存、规格属性、自提点管理、物流运费模板处理等
 * Class Specialty
 * @package Controller\Product
 */
class Specialty extends Controller
{
    private $_sid;
    private $_memberId;
    private $_businessLib;
    private $_dtype;
    private $_sdtype;

    public function __construct()
    {
        $loginInfo       = $this->getLoginInfo();
        $this->_sid      = $loginInfo['sid'];
        $this->_dtype    = $loginInfo['dtype'];
        $this->_sdtype   = $loginInfo['sdtype'];
        $this->_memberId = $loginInfo['memberID'];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $this->_sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
//            $this->_memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $this->_businessLib = new SpecialtyV2($this->_sid, $this->_memberId);
    }

    /**
     * 保存特产规格、价格、票属性
     * @author: zhangyz
     * @date: 2020/4/17
     */
    public function saveGoods()
    {
        $inputData = file_get_contents('php://input');
        $params    = json_decode($inputData, true);

        if (!in_array($this->_dtype, [0, 2, 6, 9, 18]) || in_array($this->_sdtype, [1])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '该类型账号不允许编辑');
        }

        $sid = $this->_sid;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        //发布产品权限校验
        $proTypeAuth = (new \Business\Authority\AuthContext())->memberProductTypeAuth($sid);
        if (!in_array('J', $proTypeAuth)) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无发布特产类型产品权限！');
        }

        $ticketData = $params['ticket'];
        $priceData  = $params['price'];
        $specsData  = $params['specs'];
        $delSpecs   = $params['delSpecs'];

        $gid = $params['gid'];

        if ($gid < 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        //规格属性处理
        if (!empty($specsData) || !empty($delSpecs)) {
            $specsResult = $this->_businessLib->createSpecsValue($gid, $specsData, $delSpecs);
            if ($specsResult['code'] != self::CODE_SUCCESS) {
                $this->apiReturn($specsResult['code'], [], $specsResult['msg']);
            }
            $newSpecsData = $specsResult['data'];
        }

        //对票属性进行处理
        $ticketResult = $this->_businessLib->beforeHandleTicket($ticketData);
        if ($ticketResult['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($ticketResult['code'], [], $ticketResult['msg']);
        }
        $ticketData    = $ticketResult['data'];
        $newTicketData = $this->_businessLib->createGoodsAttribute($gid, $ticketData);

        //价格库存处理
        $priceResult = $this->_businessLib->createPriceData($gid, $priceData);
        if ($priceResult['code'] != self::CODE_SUCCESS) {
            $this->apiReturn($priceResult['code'], [], $priceResult['msg']);
        }

        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        $newPriceData = $priceResult['data'];
        $res          = $this->_businessLib->saveGoods($newTicketData, $newPriceData, $newSpecsData, $subSid);

        if ($res['code'] != 200) {
            //当零售价小于最大结算价时
            if ($res['code'] == 50051) {
                //门票id数组
                $tidArr = array_column($priceData, 'ticket_id');
                //通过门票id 获取到对应的pid
                $javaApi   = new \Business\CommodityCenter\Ticket();
                $cache     = Cache::getInstance('redis');
                $ticketRes = $javaApi->batchQueryTicketByTicketIds($tidArr);
                $pidMap    = array_column($ticketRes, 'pid', 'id');

                //处理下本次操作的数据
                $specialtyCache = [];
                foreach ($newPriceData as $item) {
                    $specialtyCache[] = [
                        'sid'         => $this->_sid,
                        'pid'         => $pidMap[$item['ticketId']],
                        'newDPrice'   => $item['retailPrice'] - $item['costPrice'],
                        'retailPrice' => $item['oldRetailPrice'],
                        'costPrice'   => $item['oldCostPrice'],
                    ];
                }
                $tocken   = md5(json_encode($specialtyCache) . time());
                $cacheKey = "{$gid}:{$tocken}";
                $cache->set($cacheKey, json_encode($specialtyCache), '', 7210); // 两个小时多些

                $this->apiReturn($res['code'], ['gid' => $gid, 'tocken' => $tocken], $res['msg']);
            } else {
                pft_log('ticket/specialty/fail', json_encode($res));
                $this->apiReturn($res['code'], $res['data'], $res['msg']);
            }
        }

        $this->apiReturn(self::CODE_SUCCESS, [], '保存成功');
    }

    /**
     * 特产商品信息新增 or 修改
     * @author: zhangyz
     * @date: 2020/4/13
     */
    public function saveProduct()
    {
        $subSid = 0;
        $sid = $this->_sid;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $sid    = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }
        //发布产品权限校验
        $proTypeAuth = (new \Business\Authority\AuthContext())->memberProductTypeAuth($sid);
        if (!in_array('J', $proTypeAuth)) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无发布特产类型产品权限！');
        }

        $params           = $this->_handleProductParams();
        $params['subSid'] = $subSid;

        //数据权限
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        if (!$params['pid']) {
            if ($dataAuthLimit->hasLimit()) {
                $this->apiReturn(403, [], '无相关数据权限，不可发布产品！');
            }
        }

        $res    = $this->_businessLib->saveProduct($params);

        $msg = (int)$params['pid'] > 0 ? '修改' : '新增';
        if ($res) {
            $this->apiReturn(self::CODE_SUCCESS, $res, $msg . '成功');
        } else {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], $msg . '失败');
        }
    }

    /**
     * 处理商品信息参数
     * @author: zhangyz
     * @date: 2020/4/13
     * @return array
     *
     * @param  int lastid 商品ID，用于修改接口
     * @param  string title 商品名称
     * @param  string goods_type 商品类型
     * @param  int province 省份
     * @param  int city 城市
     * @param  int area 区县
     * @param  string tel  联系电话
     * @param  array media 商品图数组
     * @param  string detail 商品详情
     * @param  string address 详细地址
     * @param  string sales 商品卖点
     *
     */
    private function _handleProductParams()
    {
        $proId        = I('post.product_id', 0, 'intval');
        $title        = I('post.title', '', 'strval,trim');
        $goodsType    = I('post.goods_type', '', 'strval,trim');
        $province     = I('post.province', 0, 'intval');
        $city         = I('post.city', 0, 'intval');
        $area         = I('post.area', 0, 'intval');
        $telphone     = I('post.tel', '', 'strval,trim');
        $medias       = I('post.medias', [], '');
        $delMedias    = I('post.delMedias', '', 'strval,trim');
        $detail       = I('post.detail', '', 'strval,trim');
        $address      = I('post.address', '', 'strval,trim');
        $sales        = I('post.sale_point', '', 'strval,trim');
        $oversea      = I('post.oversea', 0, 'intval');
        $firstPicture = I('post.first_picture', '', 'strval');
        $videoUrl     = I('post.videoUrl', '', 'strval');
        $imgPath      = I('post.imgPath', '', 'strval');

        if (!isset($title) || empty($title)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '商品名称不能为空');
        }

        if (!isset($goodsType) || empty($goodsType)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '商品类型不能为空');
        }

        if (!isset($telphone) || empty($telphone)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '联系电话不能为空');
        }

        //if (!isset($medias) || empty($medias)) {
        //    $this->apiReturn(self::CODE_PARAM_ERROR, [], '商品图不能为空');
        //}

        if (!isset($detail) || empty($detail)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '商品详情不能为空');
        }

        if (!empty($sales) && mb_strlen($sales, 'utf-8') > 60) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '商品卖点信息不得超过60字');
        }

        if (!in_array($oversea, [0, 1])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '商品境内境外参数错误');
        }
        $landExtAttr = [];  //景区扩展属性
        if (!empty($firstPicture)) {
            $landExtAttr[] = [
                'key' => 'first_picture',
                'val' => $firstPicture,
            ];
        }

        //需要删除的图片ID。多个之间由逗号隔开
        if (!empty($delMedias)) {
            $delMediaArr = explode(',', $delMedias);

            $delIdArr = [];
            foreach ($delMediaArr as $delId) {
                $delIdArr[] = [
                    'id' => $delId,
                ];
            }

            $medias = array_merge($medias, $delIdArr);
        }

        $areaStr = $province . '|' . $city . '|' . $area;

        $data = [
            'pid'         => $proId,
            'title'       => $title,
            'sales'       => $sales,
            'detail'      => $detail,
            'tel'         => $telphone,
            'goodsType'   => $goodsType,
            'medias'      => $medias,
            'area'        => $areaStr,
            'address'     => $address,
            'oversea'     => $oversea,
            'landExtAttr' => $landExtAttr,
            'videoUrl'    => $videoUrl,
            'imgPath'     => $imgPath,
        ];

        return $data;
    }

    /**
     * 发布特产产品
     * @author: zhangyz
     * @date: 2020/4/3
     */
    public function setTicket()
    {
        if (!in_array($this->_dtype, [0, 2, 6, 9]) || in_array($this->_sdtype, [1])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '该类型账号不允许编辑');
        }

        $sid = $this->_memberId;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        //权限判断
        $proTypeAuth = (new AuthContext())->memberProductTypeAuth($sid);
        if (!in_array('J', $proTypeAuth)) {
            $this->apiReturn(403, [], '无发布特产产品权限！');
        }

        $submitData = I('post.');

        if (count($submitData) < 1) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '未接收到特产信息');
        }

        $gid = $submitData['gid'];
        if (empty($gid) || !$gid) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '商品ID不能为空');
        }

        $ticketData = $submitData['ticket'];
        $priceData  = $submitData['price'];

        //$ticketData = (new \Business\Product\HandleTicket($this->_sid))->handleAutoParam($ticketData, $priceData);
        $ticketRes = $this->_businessLib->beforeHandleTicket($ticketData);

        if ($ticketRes['code'] != 200) {
            $this->apiReturn($ticketRes['code'], [], $ticketRes['msg']);
        }

        $res = $this->_businessLib->setTicket($ticketRes['data']);

        if ($res[0] != 200) {
            $this->apiReturn($res[0], [], $res[1]);
        }

        //门票属性后置操作
        $this->_businessLib->afterHandleTicket($ticketData);

        $res = $this->_businessLib->setPrice($gid, $priceData);
        if ($res[0] != 200) {
            $this->apiReturn($res[0], [], $res[1]);
        }

        $this->apiReturn(self::CODE_SUCCESS, [], 'success');
    }

    /**
     * 批量修改价格库存
     * @author: zhangyz
     * @date: 2020/4/16
     */
    public function batchUpdatePrice()
    {
        $gid   = I('post.gid', 0, 'intval');
        $type  = I('post.type', '', 'strval.trim');
        $value = I('post.value', '', 'strval,trim');
        $acId  = I('post.account_id', 0, 'intval');

        if (!in_array($this->_dtype, [0, 2, 6, 9]) || in_array($this->_sdtype, [1])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '该类型账号不允许编辑');
        }

        if ($acId != $this->_sid) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无操作权限');
        }

        $res = $this->_businessLib->batchUpdatePrice($gid, $type, $value);

        if ($res) {
            $this->apiReturn(self::CODE_SUCCESS, [], '修改成功');
        } else {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '修改失败');
        }
    }

    /**
     * 保存规格属性
     * @author: zhangyz
     * @date: 2020/4/15
     */
    public function saveSpecs()
    {
        $gid   = I('post.gid', 0, 'intval');
        $specs = I('post.specs', []);

        if (!$gid) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '商品ID不能为空');
        }

        if (count($specs) < 1) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '规格项目不能为空');
        }

        $res = $this->_businessLib->handleSpecsValue($gid, $specs);

        if ($res[0] != self::CODE_SUCCESS) {
            $this->apiReturn($res[0], [], $res[1]);
        }

        $result = $this->_businessLib->saveSpecs($gid, $res[1]);

        if ($result !== false) {
            $this->apiReturn(self::CODE_SUCCESS, $result, 'success');
        } else {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '保存失败');
        }
    }

    /**
     * 删除规格属性
     * @author: zhangyz
     * @date: 2020/4/15
     */
    public function delSpecs()
    {
        $id = I('post.id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $res = $this->_businessLib->deleteSpecs($id);

        if ($res) {
            $this->apiReturn(self::CODE_SUCCESS, [], '删除成功');
        } else {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '删除失败');
        }
    }

    /**
     * 通过商品ID获取商品所有信息
     * @author: zhangyz
     * @date: 2020/4/16
     */
    public function getGoods()
    {
        $gid = I('post.gid', 0, 'intval');
        $lid = I('post.lid', 0, 'intval');

        if (!$gid && !$lid) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '商品ID和景区ID至少传一个');
        }

        $ticketInfo = $this->_businessLib->getGoodsByGidOrLid($gid, $lid);
        if (empty($ticketInfo)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '未找到特产产品信息');
        }

        $ticket = $this->_handleTicketInfo($ticketInfo);
        $price  = $this->_businessLib->queryPriceByGoodsId($ticket['product']['gid']);

        $data = [
            'product' => $ticket['product'],
            'ticket'  => $ticket['ticket'],
            'specs'   => $ticket['specs'],
            'price'   => $price,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $data, 'success');
    }

    /**
     * 对java返回的票属性做一层处理给前端
     * @author: zhangyz
     * @date: 2020/5/12
     *
     * @param $data
     */
    private function _handleTicketInfo($data)
    {
        $ticket = [];
        foreach ($data as $key => $val) {
            $attribute              = $val['attribute'];
            $goodsOrderAttribute    = $val['goodsOrderAttribute'];
            $goodsVerifyAttribute   = $val['goodsVerifyAttribute'];
            $goodsRefundAttribute   = $val['goodsRefundAttribute'];
            $goodsDeliveryAttribute = $val['goodsDeliveryAttribute'];
            $landExtAttr            = $val['landExtAttr'] ?? (object)[];

            $specsData = $val['specItemList'];

            $buyLimit            = json_decode($goodsOrderAttribute['buy_limit'], true);
            $refundRule          = json_decode($goodsRefundAttribute['refund_rule_conf'], true);
            $sendBeforeFee       = json_decode($goodsRefundAttribute['send_before_fee'], true);
            $sendAfterFee        = json_decode($goodsRefundAttribute['send_after_fee'], true);
            $advanceSaleRule     = json_decode($goodsOrderAttribute['advance_sale_rule'], true);
            $orderGoodsLimit     = json_decode($goodsOrderAttribute['order_goods_limit'], true);
            $ticketShelfRuleConf = json_decode($attribute['ticket_shelf_rule_conf'], true);

            $refundRuleOri  = (int)$goodsRefundAttribute['refund_rule'];
            $refundRuleCode = $refundRuleOri == -1 ? 2 : $refundRuleOri;
            $refundRuleBool = $refundRuleOri == -1 ? 1 : 0;

            $area     = explode('|', $val['area']);
            $ticket[] = [
                'product' => [
                    'address'     => (string)$val['address'] ?? '',
                    'province'    => (int)$area[0] ?? 0,
                    'city'        => (int)$area[1] ?? 0,
                    'area'        => (int)$area[2] ?? 0,
                    'gid'         => (int)$val['id'] ?? 0,
                    'land_id'     => (int)$val['landId'] ?? 0,
                    'goods_type'  => (string)$val['goodsType'] ?? '',
                    'medias'      => $val['medias'] ?? [],
                    'tel'         => (string)$val['tel'] ?? '',
                    'title'       => (string)$val['title'] ?? '',
                    'sell_point'  => (string)$attribute['selling_point'] ?? '',
                    'goodsDetail' => $val['goodsDetail'] ?? '',
                    'ext'         => $landExtAttr,
                    'videoUrl'    => $val['videoUrl'] ?? '',
                    'imgPath'     => $val['imgPath'] ?? '',
                    'oversea'     => $val['oversea'] ?? 0,
                ],
                'ticket'  => [
                    'shop'                      => (string)$val['shop'],
                    'mobile'                    => (string)$val['mobile'] ?? '',
                    'accountId'                 => (int)$val['applyDid'],
                    'operatorId'                => (int)$val['operaterId'],

                    //常规票属性
                    'auto_grounding_status'     => (int)$ticketShelfRuleConf['listing']['type'],
                    'auto_grounding_date'       => (string)$ticketShelfRuleConf['listing']['time'],
                    'auto_undercarriage_status' => (int)$ticketShelfRuleConf['delisting']['type'],
                    'auto_undercarriage_date'   => (string)$ticketShelfRuleConf['delisting']['time'],
                    'cancel_sms_buyer'          => (int)$attribute['cancel_sms_buyer'],
                    'cancel_sms_supplier'       => (int)$attribute['cancel_sms_supplier'],
                    'order_sms_supplier'        => (int)$attribute['order_sms_supplier'],
                    'order_sms_buyer'           => (int)$attribute['order_sms_buyer'],
                    'delivery_sms_buyer'        => (int)$attribute['delivery_sms_buyer'],
                    'express_sms_buyer'         => (int)$attribute['express_sms_buyer'],
                    'confirm_wx'                => (int)$attribute['confirm_wx'],
                    'selling_point'             => (string)$attribute['selling_point'],
                    'ticket_shelf_rule'         => (int)$attribute['ticket_shelf_rule'],
                    'buy_success_distributor'   => (int)$attribute['buy_success_distributor'],

                    //订单相关 $goodsOrderAttribute
                    'buy_limit_type'            => (int)$buyLimit['type'],
                    'buy_limit_num'             => (int)$buyLimit['num'],
                    'buy_limit_period'          => (int)$buyLimit['days'],
                    'if_special_limit'          => (bool)$goodsOrderAttribute['if_special_limit'],
                    'cancel_auto_onmin'         => (int)$goodsOrderAttribute['cancel_auto_onmin'],
                    'pre_sale_type'             => (int)$advanceSaleRule['type'],
                    'pre_sale_value'            => (string)$advanceSaleRule['value'],
                    'buy_min_amount'            => (int)$orderGoodsLimit[0],
                    'buy_max_amount'            => (int)$orderGoodsLimit[1],
                    'special_config'            => $goodsOrderAttribute['special_limit_rule'],

                    //快递相关 $goodsDeliveryAttribute
                    'express_template_id'       => (int)$goodsDeliveryAttribute['express_template_id'],
                    'pick_point_id'             => (int)$goodsDeliveryAttribute['pick_point_id'],
                    'no_pick_auto_cancel'       => (int)$goodsDeliveryAttribute['no_pick_auto_cancel'],

                    //验证相关 $goodsVerifyAttribute
                    'mdays'                     => (int)$goodsVerifyAttribute['mdays'],

                    //退款相关 $goodsRefundAttribute
                    'refund_audit'         => (int)$refundRule['ifAudit'] ?? 0,
                    'refund_rule_bool'     => (int)$refundRuleBool,
                    'refund_rule'          => (int)$refundRuleCode,
                    'send_before_type'     => (int)$sendBeforeFee['t'],
                    'send_before_value'    => (int)$sendBeforeFee['v'],
                    'send_after_type'      => (int)$sendAfterFee['t'],
                    'send_after_value'     => (int)$sendAfterFee['v'],
                    'first_evolute_sms_distributor' => $attribute['first_evolute_sms_distributor'],
                ],
                'specs'   => $specsData,
            ];
        }

        return $ticket[0];
    }

    /**
     * 删除商品
     * @author: zhangyz
     * @date: 2020/4/16
     */
    public function deleteGoods()
    {
        $gid = I('post.gid', 0, 'intval');

        if (!$gid) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '商品ID不能为空');
        }

        $goods = $this->_businessLib->getGoodsByGidOrLid($gid, 0);

        if ($goods[0]['applyDid'] != $this->_sid) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无权操作');
        }

        $res = $this->_businessLib->deleteGoods($gid);

        if ($res) {
            $this->apiReturn(self::CODE_SUCCESS, [], '删除成功');
        } else {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '删除失败');
        }
    }

    /**
     * 新增/编辑自提点
     * @author: zhangyz
     * @date: 2020/4/16
     */
    public function savePickPoint()
    {
        $id         = I('post.id', 0, 'intval');
        $name       = I('post.name', '', 'strval,trim');
        $province   = I('post.province', 0, 'intval');
        $city       = I('post.city', 0, 'intval');
        $area       = I('post.area', 0, 'intval');
        $address    = I('post.address', '', 'strval,trim');
        $linkName   = I('post.link_name', '', 'strval,trim');
        $mobile     = I('post.mobile', '', 'strval,trim');
        $orderBy    = I('post.orderBy', 0, 'intval');
        $acceptType = I('post.type', 0, 'intval');
        $weekdays   = I('post.weekdays', '', 'strval,trim');
        $beginTime  = I('post.begin_time', '', 'strval,trim');
        $endTime    = I('post.end_time', '', 'strval,trim');

        if (!$name || !$province || !$address || !$linkName || !$mobile) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        if ($id > 0) {
            $pickPoint = $this->_businessLib->getPickPoint($id);
            if (empty($pickPoint) || $pickPoint['ifDel'] == 1) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '自提点不存在或已删除');
            }

            if ($pickPoint['applyDid'] != $this->_sid) {
                $this->apiReturn(self::CODE_AUTH_ERROR, [], '无修改权限');
            }
        }

        $res = $this->_businessLib->savePickPoint($id, $name, $province, $city, $area, $address, $linkName, $mobile,
            $orderBy, $acceptType, $weekdays, $beginTime, $endTime);

        if ($res['code'] == self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_SUCCESS, $res, '保存成功');
        } else {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '保存失败：' . $res['msg']);
        }
    }

    /**
     * 获取供应商下的自提点
     * @author: zhangyz
     * @date: 2020/4/17
     */
    public function getPickPointByApply()
    {
        $sid = I('post.sid', $this->_sid, 'intval');
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $res = $this->_businessLib->getPickPointByApply($sid);

        $message = !empty($res) ? 'success' : '暂无数据';

        $this->apiReturn(self::CODE_SUCCESS, $res, $message);
    }

    /**
     * 删除自提点
     * @author: zhangyz
     * @date: 2020/4/17
     */
    public function deletePickPoint()
    {
        $id = I('post.id', 0, 'intval,trim');

        if (!$id) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $res = $this->_businessLib->getPickPoint($id);

        if (!$this->isSuper() && $res['applyDid'] != $this->_sid) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无权限删除');
        }

        $res = $this->_businessLib->deletePickPoint($id);

        if ($res) {
            $this->apiReturn(self::CODE_SUCCESS, [], '删除成功');
        } else {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '删除失败');
        }
    }

    /**
     * 通过ID获取自提点信息
     * @author: zhangyz
     * @date: 2020/4/16
     */
    public function getPickPoint()
    {
        $id = I('post.id', 0, 'intval,trim');

        $res = $this->_businessLib->getPickPoint($id);

        if (!$this->isSuper() && $res['applyDid'] != $this->_sid) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无权限查看');
        }

        $this->apiReturn(self::CODE_SUCCESS, $res, 'success');
    }

    /**
     * 新增、编辑物流运费模板
     * @author: zhangyz
     * @date: 2020/4/17
     */
    public function saveExpressTemplate()
    {
        $id       = I('post.id', 0, 'intval');
        $name     = I('post.name', '', 'strval,trim');
        $charging = I('post.charging', 0, 'intval,trim'); //0=按件数，1=按重量
        $express  = I('post.templateAreaCreates', []);

        if (!$name) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '物流模板名称不能为空');
        }

        if (!$express) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '配送区域不能为空');
        }

        if ((int)$id > 0) {
            $result = $this->_businessLib->getExpressTemplateById($id);
            if ($result['applyDid'] != $this->_sid) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '无权限修改');
            }
        }

        $res = $this->_businessLib->saveExpressTemplate($id, $name, $charging, $express);

        if ($res['code'] == self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_SUCCESS, $res['data'], '保存成功');
        } else {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '保存失败：' . $res['msg']);
        }
    }

    /**
     * 通过模板ID获取物流运费模板
     * @author: zhangyz
     * @date: 2020/4/17
     */
    public function getExpressTemplateById()
    {
        $id = I('post.id', 0, 'intval');

        $express = $this->_businessLib->getExpressTemplateById($id);

        if (!empty($express) && $express['applyDid'] != $this->_sid) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无查看权限');
        }

        $this->apiReturn(self::CODE_SUCCESS, $express, 'success');
    }

    /**
     * 查询当前登录用户的运费模板
     * @author: zhangyz
     * @date: 2020/4/17
     */
    public function getExpressTemplates()
    {
        $sid = I('post.sid', $this->_sid, 'intval');

        $express = $this->_businessLib->getExpressTemplateByApplyDid($sid);

        $this->apiReturn(self::CODE_SUCCESS, $express, 'success');
    }

    /**
     * 删除运费模板
     * @author: zhangyz
     * @date: 2020/4/17
     */
    public function deleteExpressTemplate()
    {
        $id = I('post.id', 0, 'intval');

        $express = $this->_businessLib->getExpressTemplateById($id);

        if (empty($express)) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '模板不存在');
        }

        if ($express['applyDid'] != $this->_sid) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无权操作');
        }

        $res = $this->_businessLib->deleteExpressTemplate($id);

        if ($res) {
            $this->apiReturn(self::CODE_SUCCESS, [], '删除成功');
        } else {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '删除失败');
        }
    }

    /**
     * 预定页票类鼠标悬浮气泡信息
     * @author: zhangyz
     * @date: 2020/5/13
     */
    public function queryTopic()
    {
        $tid = I('post.tid', 0, 'intval');

        if (!$tid) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $res = $this->_businessLib->querySpecialGoodsTopic($tid);

        $this->apiReturn(self::CODE_SUCCESS, $res, 'success');
    }

    /**
     * 删除特产门票
     * @author: zhangyz
     * @date: 2020/5/21
     */
    public function deleteSpecialTicket()
    {
        $tid = I('post.tid', 0, 'intval');

        if (!$tid) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        $res = $this->_businessLib->deleteSpecialTicket($tid, $subSid);

        if ($res) {
            $this->apiReturn(self::CODE_SUCCESS, [], '删除成功');
        } else {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '删除失败');
        }
    }

    /**
     * 商品下架
     * @author: zhangyz
     * @date: 2020/5/23
     */
    public function delistingGoods()
    {
        $gid = I('post.gid', 0, 'intval');
        $lid = I('post.lid', 0, 'intval');

        if (!$gid && !$lid) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '商品ID或景区ID不能为空');
        }

        $subSid = 0;
        $sid = $this->_sid;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $goods = $this->_businessLib->getGoodsByGidOrLid($gid, $lid);

        if ($sid != $goods[0]['applyDid']) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无权操作');
        }

        $res = $this->_businessLib->delistingGoods($gid, $lid, $subSid);

        if ($res) {
            $this->apiReturn(self::CODE_SUCCESS, [], '操作成功');
        } else {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '操作失败');
        }
    }

    /**
     * 商品上架
     * @author: zhangyz
     * @date: 2020/5/23
     */
    public function listingGoods()
    {
        $gid = I('post.gid', 0, 'intval');
        $lid = I('post.lid', 0, 'intval');

        if (!$lid && !$gid) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '商品ID或景区ID不能为空');
        }
        $sid    = $this->_sid;
        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $sid    = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $goods = $this->_businessLib->getGoodsByGidOrLid($gid, $lid);

        if ($sid != $goods[0]['applyDid']) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '无权操作');
        }

        $res = $this->_businessLib->listingGoods($gid, $lid, $subSid);

        if ($res) {
            $this->apiReturn(self::CODE_SUCCESS, [], '操作成功');
        } else {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '操作失败');
        }
    }
}