<?php

namespace Controller\product;

use Business\Authority\AuthLogic as AuthLogicBiz;
use Business\CardSolution\Recharge;
use Business\JavaApi\Order\Query\UusOrder;
use Business\JavaApi\Product\Land;
use Business\Member\Member;
use Library\Constants\CardType;
use Library\Constants\RechargeSchemeChannel;
use Library\SimpleExcel;
use Library\Tools;
use Model\CardSolution\BlankCard;
use Model\CardSolution\RechargeScheme;
use Model\CardSolution\Vuser;
use Model\Member\Member as memberModel;
use Library\Controller;
use Library\Tools\Vcode;
use Model\Member\MemberLog;
use Model\Product\ParkCard as parkModel;
use Model\Product\BaseCard;
use Model\Terminal\SiteManage;

/**
 * 游乐园一卡通平台接口
 *
 * <AUTHOR>
 * @date    2017/3/14
 */
class ParkCard extends Controller
{

    //登录信息
    private $_loginInfo;
    //当前登陆用户主账号id
    private $_memberId;
    //当前登录的用户id
    private $_mid;
    //游乐园卡状态
    private $_cardStatus = [0 => '未激活', 1 => '使用中', 2 => '已退卡', '3' => '挂失'];

    //查询纬度和纬度对应的名称
    private $_needItem   = [
        'date'           => '日期',
        'card_no'        => '实体卡号',
        'ordernum'       => '订单号',
        'mobile'         => '手机号',
        'operation_type' => '操作类型',
        'operate_id'     => '操作员',
        'pay_type'       => '充值支付方式',
        'fid'            => '分销商',
        'site_id'        => '站点',
        'channel'        => '渠道',
    ];
    private $_targetItem = [
        'physical_principal' => '实体卡本金消费',
        'physical_bonus'     => '实体卡奖励金消费',
        'virtual_principal'  => '虚拟卡本金消费',
        'virtual_bonus'      => '虚拟卡奖励金消费',
        'recharge_amount'    => '充值金额',
        'refund_amount'      => '退卡金额',
        'repair_amount'      => '补卡金额',
        'land_name'          => '景区',
        'ticket_name'        => '门票',
        'change_money'       => '修改/取消订单金额(当前页)',
    ];
    
    private $memberData  = [];

    private $summaryParkInfo = [6970, 3385, 7855157];

    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo();
        $this->_memberId  = $this->_loginInfo['sid'];
        $this->_mid       = $this->_loginInfo['memberID'];
    }

    /**
     * 获取 一卡通游客列表 操作员信息
     *
     * <AUTHOR>
     *
     * @return array
     */
    public function getOpid()
    {
        //加载园区卡模型
        // $ParkModel = new \Model\Product\ParkCard();
        // $opArr = $ParkModel->getParkCardOpdInfo($this->_memberId);

        //加载会员模型
        $MemberModel = new \Model\Member\Member();

        $opArr = $MemberModel->getStaffByParentId($this->_memberId);

        if (!$opArr) {
            $this->apiReturn(201, [], '无数据');
        }

        $opData = $MemberModel->getMemberInfoByMulti(array_column($opArr, 'son_id'), 'id', 'id,dname');

        if ($opData) {
            $this->apiReturn(200, $opData, '获取成功');
        } else {
            $this->apiReturn(201, [], '无数据');
        }
    }

    /**
     *  获取一卡通游客列表
     *
     * <AUTHOR>
     *
     * @return
     */
    public function getTouristList()
    {
        //操作员id
        $opId = I('opid', 0, 'intval');
        //开始时间
        $begin = I('begin', 0, 'intval');
        //结束时间
        $end = I('end', 0, 'intval');
        //状态
        $status = I('status', 0, 'intval');
        //每页条数
        $pageSize = I('pagesize', 15, 'intval');
        //当前页
        $page = I('page', 1, 'intval');
        //物理卡号
        $physicsNo = I('physicsno', 0, 'strval');
        //手机号
        $mobile = I('mobile', 0, 'strval');
        //实体卡号
        $cardNo = I('card_no', '', 'strval');

        if ($pageSize > 100) {
            $this->apiReturn(201, [], '查询条数超出范围');
        }

        //默认取当天的数据
        if (!$begin || !$end) {
            $begin = strtotime(date('Y-m-d')) - 3600 * 24;
            $end   = $begin + 3600 * 24;
        }

        $result = $this->_getList($opId, $begin, $end, $status, $page, $pageSize, $physicsNo, $mobile, $cardNo);

        $total = $result['total'];
        $list  = $result['list'];
        if ($total) {
            $list = $this->_moreData($list, 'list', $this->getLoginInfo()['sid']);
        }
        if (in_array($this->_memberId, $this->summaryParkInfo)) {
            //获取余额
            $parkCardScenic = new \Business\JsonRpcApi\ScenicLocalService\ParkCard();
            $balanceResult  = $parkCardScenic->getAccountBooKAndAwardBalance($this->_memberId);
        }

        $return = [
            'show_summary'    => in_array($this->_memberId, $this->summaryParkInfo) ? 1: 0,
            'page'            => $page,
            'list'            => $list,
            'total_page'      => ceil($total / $pageSize),
            'in_use_card_num' => $balanceResult['data']['inUseCardNum'] ?? 0,
            'balance'         => $balanceResult['data']['bookBalanceRes'] ?? 0,
            'award'           => $balanceResult['data']['awardBalanceRes'] ?? 0,
        ];

        $this->apiReturn(200, $return);

    }

    /**
     * 卡交易记录
     * <AUTHOR>
     * @date   2019-02-13
     */
    public function transactionDetail()
    {
        //当前页码
        $page = I('post.page', 0, 'intval');
        //每页条数
        $size = I('post.size', 0, 'intval');
        //卡标识
        $identify = I('post.identify');
        //卡标识类型
        $type = I('post.type');
        //查询开始日期
        $dateBegin = I('post.date_begin');
        //查询结束日期
        $dateEnd = I('post.date_end');
        //卡类型
        $cardType = I('post.card_type', 5, 'intval');


        if (!$identify || !$type || !$dateBegin || !$dateEnd) {
            $this->clientReturn(203, [], '参数错误');
        }
        $parkJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\ParkCard();
        $result      = $parkJsonRpc->transactionDetail($this->_memberId, $identify, $type, $dateBegin, $dateEnd, $cardType, $page, $size);

        $this->clientReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 导出园区卡信息
     *
     * <AUTHOR>
     * @date   2017-03-20
     *
     * @return [type]     [description]
     */
    public function downLoad()
    {
        //操作员id
        $opId = I('opid', 0, 'intval');
        //开始时间
        $begin = I('begin', 0, 'intval');
        //结束时间
        $end = I('end', 0, 'intval');
        //状态
        $status = I('status', 0, 'intval');
        //物理卡号
        $physicsNo = I('physicsno', 0, 'strval');
        $mobile    = I('mobile', 0, 'strval');

        //默认取当天的数据
        if (!$begin || !$end) {
            $begin = strtotime(date('Y-m-d'));
            $end   = $begin + 3600 * 24;
        }
        $parkCardModel = new \Model\Product\ParkCard();
        $list          = $parkCardModel->getActiveList($this->_memberId, $opId, $begin, $end, $status, $physicsNo, $mobile);
        if (!empty($list)) {
            $list = $this->_moreData($list, 'down', $this->getLoginInfo()['sid']);
        }
        //栏目标题
        $downData[] = ['账号id', '姓名', '实体卡号','物理卡号','手机号', '身份证', '开卡时间', '操作员', '充值余额', '赠送余额', '状态'];
        foreach ($list as $item) {
            $downData[] = [
                $item['account'],
                $item['card_name'],
                strval($item['card_no']),
                strval($item['physics_no']),
                $item['mobile'],
                $item['id_card_no'],
                date('Y-m-d H:i:s', $item['active']),
                $item['op'],
                //$tmpRecharge,
                //$item['recharge']['op'],
                $item['remain'] / 100,
                isset($item['donationAmount']) ? $item['donationAmount'] / 100 : 0,
                $item['status'],
            ];
        }
        $this->excelReturn('园区卡列表.xls', 'ceshi', $downData,false);

    }

    /**
     * 获取一卡通列表
     *
     * <AUTHOR>
     * @date   2017-03-20
     *
     * @param  int  $opId  操作员id
     * @param  int  $begin  开始时间
     * @param  int  $end  结束时间
     * @param  int  $status  IC卡状态
     * @param  int  $page  当前页数
     * @param  int  $pageSize  每页条数
     * @param  string  $physicsNo  物理卡号
     * @param  string  $mobile  手机号
     *
     * @return
     */
    private function _getList($opId, $begin, $end, $status, $page, $pageSize, $physicsNo, $mobile, $cardNo = '')
    {

        //加载园区卡模型
        $ParkModel = new \Model\Product\ParkCard();

        $field = 'park.id,park.mobile,park.card_no,park.status,park.member_id,park.active_time,park.op_id,base.physics_no,
        park.card_name,park.mobile,park.identify_card,base.type';

        if ($opId) {
            $where['op_id'] = $opId;
        } else {
            $where['park.sid'] = $this->_memberId;
        }

        if ($mobile) {
            $where['park.mobile'] = $mobile;
        }

        if ($cardNo) {
            $where['park.card_no'] = $cardNo;
        }

        //获取单张卡绑定过的历史列表
        if ($physicsNo) {
            $where['base.physics_no'] = $physicsNo;
        } else {
            $where['park.active_time'] = ['between', [$begin, $end]];
        }
        $where['base.type'] = ['in', [CardType::PARK_CARD, CardType::MALL_VCARD, CardType::ID_PARK_CARD]];
        if ($status != -1) {
            $where['park.status'] = $status;
        }
        //获取卡列表
        $result = $ParkModel->getCardList($where, $field, $page, $pageSize);

        return $result;

    }

    /**
     * 填充园区卡列表的数据
     *
     * <AUTHOR>
     * @date   2017-03-20
     *
     * @param  array  $list  园区卡列表
     * @param  string  $type  list:列表数据 | down:导出数据
     *
     * @return array
     */
    private function _moreData($list, $type = 'list', $sid = 0)
    {
        $memArr = array_column($list, 'member_id');
        $opArr  = array_column($list, 'op_id');
        $midArr = array_merge($memArr, $opArr);

        //加载用户模型
        $mModel = new \Model\Member\Member();
        //挤在园区卡模型
        $ParkModel  = new \Model\Product\ParkCard();
        $parkBiz    = new \Business\Product\ParkCard();
        $creditMid  = [];  //之前的一卡通
        $oneCartoon = [];  //身份证一卡通用户
        foreach ($list as $key => $value) {
            if (Tools::personID_format_err($value['physics_no'])) {
                $oneCartoon[] = $value['member_id'];
            } else {
                $creditMid[] = $value['member_id'];
            }
        }
        //批量获取余额
        //$remainMap   = $mModel->getMoneyBatch($memArr);
        $remainMap = $mModel->batchBalanceAndCredit($memArr, $this->_memberId, false);
        //一卡通余额
        $oneCardMap = $parkBiz->batchMoneyWithOneCard($oneCartoon, $sid);
        //批量获取用户信息
        $memMap = $mModel->getMemberInfoByMulti($midArr, 'id', 'id,account,dname, mobile', true);
        //用户二期 - 信息获取修改
        $CustomerBus = new \Business\Member\Customer();
        $memMapExt   = $CustomerBus->getCustomerListByMemberIdArr($midArr, 'id_card_no,sex');
        //批量获取用户的赠送金额
        $parkCardMode           = new \Business\Product\ParkCard();
        $donationAmountBook     = $parkCardMode->getDonationAmountBook($memArr, $this->_memberId);
        $donationAmountBookInfo = $donationAmountBook['data'];
        if ($type == 'list') {
            //批量获取用户的所有的充值记录
            $rechargeMap = $ParkModel->getRechargeListMulti($memArr);
            foreach ($rechargeMap as &$item) {
                foreach ($item as &$val) {
                    $val['op'] = $memMap[$val['op_id']]['dname'];
                }
            }
        } else {
            //批量获取用户最近的充值记录
            $rechargeMap = [];
            //$rechargeMap = $ParkModel->getRechargeListDesc($memArr);
            foreach ($rechargeMap as &$item) {
                $item['op'] = $memMap[$item['op_id']]['dname'];
            }
        }
        foreach ($list as &$item) {
            //激活时间
            $item['active'] = $item['active_time'];
            //状态
            $item['status'] = $this->_cardStatus[$item['status']];
            //账号
            $item['account'] = $memMap[$item['member_id']]['account'];
            //操作人员
            $item['op']         = $memMap[$item['op_id']]['dname'];
            $item['danme']      = $item['card_name'];
            $item['id_card_no'] = $item['identify_card'];
            $item['sex']        = $memMapExt[$item['member_id']]['sex'];
            //余额
            if (Tools::personID_format_err($item['physics_no'])) {
                $item['remain'] = $oneCardMap[$item['member_id']]['balanceMoney'] ?: 0;
            } else {
                $item['remain'] = $remainMap[$item['member_id']]['credit'] ?: 0;
            }
            //充值记录
            $item['recharge'] = $rechargeMap[$item['member_id']] ?? [];
            $item['donationAmount'] = isset($donationAmountBookInfo[$item['member_id']]) ? $donationAmountBookInfo[$item['member_id']]['balanceMoney'] : 0;
        }

        return $list;
    }

    /**
     * 编辑操作卡
     *
     * @date   2018-04-08
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    public function editorCard()
    {
        $memberId = I('post.mid', '', 'intval');
        $mobile   = I('post.mobile', '', 'strval');     // 手机号
        $name     = I('post.name', '', 'strval');
        $idCard   = I('post.id_card', '', 'strval');    // 身份证
        $sex      = I('post.sex', '', 'strval');

        $cardId = I('post.id', '', 'intval');
        $cardNo = I('post.card_no', '', 'strval');    // 实体卡号

        if (!($memberId && $name && $sex)) {
            $this->apiReturn(201, [], '参数有误');
        }

        if (!$this->_checkAuth($memberId)) {
            $this->apiReturn(202, [], '无权操作');
        }
	    $data =[];
        
        if(!empty($mobile)){
        	if(!ismobile($mobile)){
		        $this->apiReturn(202, [], '手机号格式有误');
	        }
        }
	    $data['mobile'] = $mobile;
        if(!empty($idCard) && !Tools\Validate::isIDCard($idCard)) {
	        $this->apiReturn(202, [], '身份证号码格式错误');
        }
	    $data['identify_card'] = $idCard;
	    $data['card_no']   = $cardNo;
        $data['card_name'] = $name;
        if ($cardId) {
            $cardModel = new parkModel();
            $checkRes  = $cardModel->getActiveCardById($cardId);

            if (!$checkRes) {
                $this->apiReturn(205, [], '卡信息有误');
            }

            $where     = ['id' => $cardId];
            $memberLog = new MemberLog();
            $memberLog->startTrans();

            $res = $memberLog->saveCardModifyLog('YKT_' . $cardId, $memberId, $this->_memberId, $this->_mid,
                'pft_park_card_active', $data);

            if (!$res) {
                $this->apiReturn(400, [], '操作失败了');
            }
            
            $res2 = $cardModel->updateCardInfo($where, $data);
            
            if (!$res2) {
                $memberLog->rollback();
                $this->apiReturn(205, [], '实体卡号编辑失败');
            }

            $memberLog->commit();
        }
        if ($res2) {
            $this->apiReturn(200, [], '操作成功');
        }
        $this->apiReturn(400, [], '操作失败');
    }

    /**
     * 挂失发送验证码
     *
     * @date   2018-04-08
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    public function sendCardCode()
    {
        $mobile = I('mobile');
        if (!ismobile($mobile)) {
            $this->apiReturn(205, [], '请输入正确的手机号');
        }

        $data = [
            '{1}'  => '园区卡挂失',
            'code' => '{2}',
        ];

        $sendRes = Vcode::sendVcode($mobile, 'park_loss', 'park_loss', $data, 6, false, 300, 60);
        if ($sendRes['code'] == 200) {
            $this->apiReturn(200, [], '验证码发送成功');
        } else {
            $this->apiReturn(207, [], '验证码发送失败');
        }
    }

    /**
     * 挂失园区卡
     *
     * @date   2018-04-08
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    public function setLossCard()
    {
        $mobile  = I('post.mobile', '', 'strval');
        $code    = I('post.code', '', 'strval');
        $cardId  = I('post.card_id', '', 'strval');
        $mid     = I('post.mid', '', 'int');
        $id      = I('post.id', '', 'int');
        $id_card = I('post.id_card', '', 'strval');

        if ($id_card) {
            //身份证挂失
            if (!$this->_checkIDCard($cardId, $id_card)) {
                $this->apiReturn(202, [], '未查找到该身份证信息');
            }
        } else {
            //手机号挂失
            if (!$mobile || !$code) {
                $this->apiReturn(201, [], '参数有误');
            }

            $res  = Vcode::verifyVcode($mobile, $code, 'park_loss');
            $code = $res['code'];
            $msg  = $res['msg'];

            //登录验证码不正确
            if ($code != 200) {
                $this->apiReturn($code, [], $msg);
            }
        }

        if (!$this->_checkAuth($mid)) {
            $this->apiReturn(202, [], '无权操作');
        }

        $parkModel = new parkModel();

        $res = $parkModel->getCardInfo($cardId, false);

        if (!$res) {
            $this->apiReturn(202, [], '卡号有误');
        }
        $parkModel->startTrans();
        $res1    = $parkModel->updateStatus($id, $column = 'id', $status = 3);
        $parkBiz = new \Business\Product\ParkCard();
        $tradeNo = $parkBiz->getYktTradeNo($res['sid'], 7);
        $resLog  = $parkBiz->addOneCardGatherLog($res['member_id'], $res['sid'], $this->getLoginInfo()['memberID'],
            $res['card_no'], 7, 0, 0, 1, 0, $tradeNo, $res['mobile']);
        if (!$resLog) {
            $parkModel->rollback();
            $this->apiReturn(400, [], '挂失失败');
        }
        if ($res1) {
            $parkModel->commit();
            $this->apiReturn(200, [], '挂失成功');
        }
        $this->apiReturn(400, [], '挂失失败');
    }

    /**
     * 补卡操作
     *
     * @date   2018-04-08
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    public function changeCard()
    {
        $newCard = I('post.new_card', '', 'strval');
        $oldCard = I('post.old_card', '', 'strval');
        $id      = I('post.id', '', 'intval');

        if (!($newCard && $oldCard && $id)) {
            $this->apiReturn(201, [], '参数有误');
        }

        $baseModel   = new BaseCard();
        $oldCardInfo = $baseModel->getCardInfo($oldCard, 5);

        if (!$oldCardInfo) {
            $this->apiReturn(201, [], '旧卡卡号信息不存在');
        }

        $newCardInfo = $baseModel->getCardInfo($newCard, 5);

        //如果是补新卡，直接替换，不创建了
        if (!$newCardInfo) {
            $res = $baseModel->changeCard($oldCardInfo['id'], $newCard);

            if ($res) {
                $parkModel = new parkModel();

                $where = ['id' => $id];
                $data  = ['status' => 1];

                $res = $parkModel->updateCardInfo($where, $data);
            }
        } else {
            $parkModel = new parkModel();
            //IC卡是否已经绑定
            $exist = $parkModel->getCardInfo($newCard, false);
            if ($exist) {
                $this->clientReturn(204, [], 'IC卡已被绑定');
            }

            $oldCardInfo = $parkModel->getCardInfo($oldCard, false, 3);
            if (!$oldCardInfo) {
                $this->clientReturn(204, [], '旧卡状态不是挂失状态');
            }

            $cardId = $newCardInfo['id'];

            $where = ['id' => $id];
            $data  = ['card_id' => $cardId, 'status' => 1];

            $res = $parkModel->updateCardInfo($where, $data);
        }

        if ($res) {
            $this->apiReturn(200, [], '补卡成功');
        }
        $this->apiReturn(201, [], '补卡失败，请重新操作');
    }

    /**
     * 权限验证
     *
     * @date   2018-04-08
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    private function _checkAuth($mid)
    {
        $parkModel = new parkModel();
        $cardInfo  = $parkModel->getInfoByMid($mid, 'sid', $onlyExt = true, 0);

        if ($cardInfo['sid'] != $this->_memberId) {
            return false;
        }

        return true;
    }

    /**
     * 物理卡号和身份证校验
     *
     * @date   2018-08-28
     * <AUTHOR> Li
     *
     * @param  string  $cardId  物理卡号
     * @param  string  $idCard  身份证号
     *
     * @return bool
     */
    private function _checkIDCard($cardId, $idCard)
    {
        //通过物理卡号查出用户id
        $parkModel = new parkModel();
        $cardInfo  = $parkModel->getCardInfo($cardId, false);
        $memberId  = $cardInfo['member_id'];
        if (!$memberId) {
            return false;
        }

        //用户二期 - 信息获取修改
        $CustomerBus  = new \Business\Member\Customer();
        $customerInfo = $CustomerBus->getCustomerInfoByMemberId($memberId);
        $memberIdCard = $customerInfo['id_card_no'];

        //判断传入身份证号和库里的身份证号是否一致
        if ($memberIdCard != $idCard) {
            return false;
        }

        return true;
    }

    /**
     * 一卡通汇总详情和汇总总和
     *
     * @date   2018-08-28
     * <AUTHOR>
     *
     * @param  string  begin  结束时间
     * @param  string  end  开始时间
     * @param  string  opId  操作员
     *
     */
    public function getOneCardStatisticsReport()
    {
        $begin    = I('get.begin_date', '');
        $end      = I('get.end_date', '');
        $opId     = I('get.operate_id', 0);
        $page     = I('get.page', 1);
        $size     = I('get.size', 1);
        $isGather = I('get.gather', 0);
        $showOper = I('get.show_operate', 0);  //是否展示操作员  0不展示 1展示
        //是否导出
        $excel = I('export_excel', 0, 'intval');
        $fid = I('fid', 0, 'intval'); //分销商id

        $userInfo = $this->getLoginInfo();
        $mid      = $userInfo['memberID'];
        if (date('Y-m-d', strtotime($begin)) == $begin || date('Y-m-d', strtotime($end)) == $end) {
            $begin = date('Ymd', strtotime($begin));
            $end   = date('Ymd', strtotime($end));
        } else {
            $this->apiReturn(204, [], '日期格式错误');
        }
        $finance = $this->_checkFinance($mid);
        $sid     = $userInfo['memberID'] == $this->_memberId ? 1 : 0;

        $parkBiz = new \Business\Product\ParkCard();
        //员工的数据权限
        $dateScope = (new AuthLogicBiz())->memberDataResource($this->_memberId, $this->_mid, 6, 'one_card_statis');
        if ($finance || $sid || $dateScope) {
            $isSuper = 1;
        }

        if ($isGather) {
            $result = $parkBiz->getOnePassCardGather($begin, $end, $this->_memberId);
        } else {
            $result = $parkBiz->getOneCardStatisticsReport($begin, $end, $mid, $this->_memberId, $opId, $isSuper, $page,
                $size, $showOper, $dateScope, $fid);
        }
        if (isset($result['code'])) {
            if ($result['code'] == 200 && $excel) {
                $this->_exportOneCardStatisticsReport($result['data']);
            }
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取
     *
     * @date   2018-08-28
     * <AUTHOR>
     *
     * @param  string  begin  结束时间
     * @param  string  end  开始时间
     * @param  string  opId  操作员
     *
     */
    public function getOneCardOperator()
    {
        $keyWord = I('get.key_word', '');
        $parkBiz = new \Business\Product\ParkCard();
        //员工的数据权限
        $dateScope = (new AuthLogicBiz())->memberDataResource($this->_memberId, $this->_mid, $this->_loginInfo['dtype'], 'one_card_statis');
        if ($dateScope) {
            $result  = $parkBiz->getOneCardOperators($this->_memberId, $keyWord);
        } else {
            $result = [
                'code' => 200,
                'data' => [],
                'msg'  => '数据权限限制，不可查询其他账户报表数据'
            ];
        }

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], '');
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 判断是不是财务
     *
     * @date   2018-08-28
     * <AUTHOR>
     *
     * @param  string  opId  操作员
     *
     * @return bool
     */
    public function _checkFinance($mid)
    {
        $MemberBus  = new Member();
        $memberInfo = $MemberBus->getInfo($mid, true);
        if ($memberInfo["position"] === '2') {
            return true;
        } else {
            return false;
        }
    }

    /***********平台开卡管理*************/

    /**
     * 开卡管理列表
     * @Author: zhujb
     *
     * @param  string physics_no 物理卡号
     * @param  string begin_time 开始时间
     * @param  string end_time 结束时间
     * @param  string entity_no 实体卡号
     * @param  int op_id 操作员id
     * @param  int page 当前页码
     * @param  int page_size 每页条数
     * @param  int is_export 是否导出
     * 2019/6/28
     */
    public function getBlankCardList()
    {
        $physicsNo = I('post.physics_no', '', 'strval,trim');
        $beginTime = I('post.begin_time', '', 'strval,trim');
        $endTime   = I('post.end_time', '', 'strval,trim');
        $entityNo  = I('post.entity_no', '', 'strval,trim');
        $opId      = I('post.op_id', 0, 'intval');
        $page      = I('post.page', 1, 'intval');
        $pageSize  = I('post.page_size', 20, 'intval');
        $isExport  = I('post.is_export', 0, 'intval');
        $memberId  = $this->_memberId;

        if (empty($beginTime) || empty($endTime)) {
            $this->apiReturn(204, [], '请选择时间');
        }

        $blankCardModel = new BlankCard();
        $memberModel    = new \Model\Member\Member();

        $list  = $blankCardModel->getBlankCardList($beginTime, $endTime, $memberId, $opId, $physicsNo, $entityNo, $page,
            $pageSize, 'id,physics_no,create_time,op_id,entity_no');
        $total = $blankCardModel->getBlankCardTotalNum($beginTime, $endTime, $memberId, $opId, $physicsNo, $entityNo);
        //根据物理卡号获取卡激活状态
        $physicsNoArr  = empty($list) ? [] : array_column($list, 'physics_no');
        $baseCardModel = new BaseCard();
        $baseCardInfo  = $baseCardModel->getPhysicsCardByPhysicsNo($physicsNoArr, 'id, physics_no');
        $cardIdArr     = empty($baseCardInfo) ? [] : array_column($baseCardInfo, 'id');
        $baseCardInfo  = empty($baseCardInfo) ? [] : array_key($baseCardInfo, 'physics_no');
        $vUserModel    = new Vuser();
        $bindInfo      = $vUserModel->getBindInfoByCardIdArr($cardIdArr, 'card_id');
        $bindInfo      = empty($bindInfo) ? [] : array_column($bindInfo, 'card_id');
        $opIdArr       = array_column($list, 'op_id');
        $memberData    = $memberModel->getMemberAccountByIdArr($opIdArr);
        foreach ($list as &$item) {
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['op_name']     = $memberData[$item['op_id']] ?: '';
            $item['status']      = in_array($baseCardInfo[$item['physics_no']]['id'], $bindInfo) ? 1 : 0; //卡的使用情况 1：已激活 0：未激活
        }

        $res = [
            'list'      => $list,
            'total'     => $total,
            'page_size' => $pageSize,
        ];

        if ($isExport) {
            $xls  = new SimpleExcel('UTF-8', true, 'orderList');
            $file = '开卡管理-' . date('Y-m-d', time());

            $exportData[] = ['开卡时间', '物理卡号', '实体卡号', '操作员'];
            foreach ($list as $value) {
                $exportData[] = [
                    $value['create_time'],
                    $value['physics_no'],
                    (string)$value['entity_no'],
                    $value['op_name'],
                ];
            }
            $xls->addArray($exportData);
            $xls->generateXML($file);
            die;
        }

        $this->apiReturn(200, $res, '获取成功');
    }

    /**
     * 添加开卡信息
     * @Author: zhujb
     *
     * @param  array card_arr  一卡通信息列表 [
     *                      'physics_no' => xxx,
     *                      'entity_no'  => xxx,
     *                  ]
     * 2019/6/28
     */
    public function addCardInfo()
    {
        $cardArr   = I('post.card_arr', []);
        $loginInfo = $this->getLoginInfo();
        $opId      = $loginInfo['memberID'];

        if (empty($cardArr)) {
            $this->apiReturn(204, [], '请填写一卡通信息');
        }

        $data = [];
        foreach ($cardArr as $item) {
            $item['create_time'] = time();
            $item['op_id']       = $opId;
            $item['sid']         = $this->_memberId;
            $data[]              = $item;
        }

        $physicsNoArr = array_column($cardArr, 'physics_no');
        $cardModel    = new BlankCard();
        foreach ($physicsNoArr as $physicsNo) {
            $isExist = $cardModel->getBlankCardInfoByPhysicsNo($physicsNo);
            if ($isExist) {
                $this->apiReturn(204, [], '物理卡号已存在');
            }
        }
        $res = $cardModel->addAllBlankCardInfo($data);
        if ($res === false) {
            $this->apiReturn(204, [], '添加失败');
        }
        $this->apiReturn(200, [], '添加成功');
    }

    /**
     * 获取一卡通信息
     *
     * @param  string physics_no 物理卡号
     *
     * @Author: zhujb
     * 2019/6/28
     */
    public function getBlankCardInfo()
    {
        $physicsNo = I('post.physics_no', '', 'strval,trim');

        if (empty($physicsNo)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $blankCardModel = new BlankCard();
        $cardInfo       = $blankCardModel->getBlankCardInfoByPhysicsNo($physicsNo, 'physics_no,entity_no');
        $this->apiReturn(200, $cardInfo, '获取成功');
    }

    /**
     * 删除计时卡信息
     *
     * @param  string physics_no 物理卡号
     *
     * @Author: yangjianhui
     * 2021/07/21
     */
    public function deleteTimeCardInfo()
    {
        $physicsNo = I('post.physics_no', '', 'strval,trim');

        if (empty($physicsNo)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $parkCardBz = new \Business\Product\ParkCard();
        $result     = $parkCardBz->deleteTimeCardInfo($physicsNo);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据物理卡号修改一卡通信息
     * @Author: zhujb
     *
     * @param  string physics_no 物理卡号
     * @param  string entity_no 实体卡号
     * 2019/6/28
     */
    public function editCardInfoByphysicsNo()
    {
        $physicsNo    = I('post.physics_no', '', 'strval,trim');
        $newPhysicsNo = I('post.new_physics_no', '', 'strval,trim');
        $entityNo     = I('post.entity_no', '', 'strval,trim');

        if (empty($physicsNo)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $data = [
            'entity_no'   => $entityNo,
            'update_time' => time(),
        ];
        if ($newPhysicsNo) {
            $data['physics_no'] = $newPhysicsNo;
        }
        $blankCardModel = new BlankCard();
        $blankCardModel->startTrans();
        try {
            //修改了物理卡号
            if ($newPhysicsNo && $newPhysicsNo != $physicsNo) {
                //看下新的物理卡号是不是未激活状态，激活的不让用
                $baseCardModel = new BaseCard();
                $baseCardInfo  = $baseCardModel->getCardInfoByPhysicsNo($newPhysicsNo, 'id, physics_no');
                $vUserModel    = new Vuser();
                $bindInfo      = $vUserModel->getBindInfoByCardId($baseCardInfo['id'], 2, 'id');
                if ($bindInfo) {
                    throw new \Exception('修改的物理卡正在使用中，无法替换成该物理卡号', 400);
                }
                //记录下日志，免得后面一直问谁改的
                pft_log('time_card/physics_change', "用户{$this->_mid}修改了物理卡号" . $physicsNo . "修改成了" . $newPhysicsNo, 3);
                //更改下pft_card的物理卡号
                $baseCardModel = new BaseCard();
                $result        = $baseCardModel->updatePhysicsNo($physicsNo, $newPhysicsNo);
                if ($result === false) {
                    pft_log('time_card/physics_change', "用户{$this->_mid}修改了物理卡号" . $physicsNo . "修改成了" . $newPhysicsNo .
                                                        "pft_card表信息更新失败" . $baseCardModel->getDbError(), 3);
                    throw new \Exception('卡基础信息更新失败', 400);
                }
            }
            $res = $blankCardModel->editBlankCardByPhysicsNo($physicsNo, $data);
            if ($res === false) {
                throw new \Exception('物理卡号更新失败', 400);
            }
            $blankCardModel->commit();
        } catch (\Exception $exception) {
            $blankCardModel->rollback();
            $msg  = $exception->getMessage();
            $code = $exception->getCode();
        }
        $msg  = $msg ?? "修改成功";
        $code = $code ?? 200;

        $this->apiReturn($code, [], $msg);
    }


    /*******平台充值方案**********/

    /**
     * 充值方案列表
     * @Author: zhujb
     * 2019/6/28
     */
    public function getRechargeSchemeList()
    {
        $aid             = $this->_memberId;
        $createBeginTime = I('post.create_begin_time', '', 'strval,trim');
        $createEndTime   = I('post.create_end_time', '', 'strval,trim');
        $activeBeginTime = I('post.active_begin_time', '', 'strval,trim');
        $activeEndTime   = I('post.active_end_time', '', 'strval,trim');
        $status          = I('post.status', 0, 'intval');
        $schemeTitle     = I('post.scheme_title', '', 'strval,trim');
        $cardType        = I('post.card_type');
        $page            = I('post.page', 1, 'intval');
        $pageSize        = I('post.page_size', 10, 'intval');
		$channel         = I('post.channel', 0, 'intval');

        $rechargeSchemeModel = new RechargeScheme();
        $cardType            = empty($cardType) ? [] : explode(',', $cardType);
        $field               = 'id,scheme_title,card_type,active_begin_time,active_end_time,is_forever,deposit,cost_fee,status,
        discount_rule,create_time,lower_recharge_money,replace_fee,first_recharge_gift as first_recharge_give_away,channel,is_custom_recharge';

        $list  = $rechargeSchemeModel->getRechargeSchemeList($aid, $field, $page, $pageSize,
            $createBeginTime, $createEndTime, $activeBeginTime, $activeEndTime, $status, $schemeTitle, $cardType, $channel);
        $total = $rechargeSchemeModel->getRechargeSchemeTotal($aid, $createBeginTime, $createEndTime,
            $activeBeginTime, $activeEndTime, $status, $schemeTitle, $cardType, $channel);

        $closeIdArr = [];
        foreach ($list as &$item) {
            if (time() > $item['active_begin_time'] && time() < $item['active_end_time'] && $item['status'] != 3) {
                $item['status'] = 2;
            } else if (time() > $item['active_end_time'] && $item['is_forever'] != 1) {
                $item['status']          = 3;
                $closeIdArr[$item['id']] = $item['id'];
            }
            $item['create_time']       = date('Y-m-d H:i:s', $item['create_time']);
            $item['discount_rule']     = (array)json_decode($item['discount_rule'], true);
            $item['active_begin_time'] = date('Y-m-d H:i:s', $item['active_begin_time']);
            $item['active_end_time']   = date('Y-m-d H:i:s', $item['active_end_time']);
            if ($item['is_forever'] == 1 && $item['status'] != 3) {
                $item['status'] = 2;
                if (isset($closeIdArr[$item['id']])) {
                    unset($closeIdArr[$item['id']]);
                }
            }
        }
        //查询的时候把需要停止的配置停了  bugist:如果不查询，那就永远不停止。
        if ($closeIdArr) {
            $rechargeSchemeModel->changeStatus($closeIdArr, 3);
        }

        $res = [
            'list'      => $list,
            'total'     => $total,
            'page_size' => $pageSize,
        ];

        $this->apiReturn(200, $res, '获取成功');
    }

    /**
     * 添加充值方案
     * @Author: zhujb
     * 2019/6/28
     */
    public function addRechargeScheme()
    {
        $aid                = $this->_memberId;
        $schemeTitle        = I('post.scheme_title', '', 'strval,trim');
        $cardType           = I('post.card_type', 0, 'intval');
        $activeBeginTime    = I('post.active_begin_time', '', 'strval,trim');
        $activeEndTime      = I('post.active_end_time', '', 'strval,trim');
        $isForever          = I('post.is_forever', 0, 'intval');
        $deposit            = I('post.deposit', 0, 'intval');
        $costFee            = I('post.cost_fee', 0, 'intval');
        $discountRule       = I('post.discount_rule', []);
        $replaceFee         = I('post.replace_fee', 0, 'intval'); //补卡金额
        $lowerRechargeMoney = I('post.lower_recharge_money', 0, 'intval'); //最低充值金额
        $firstRechargeGift  = I('post.first_recharge_give_away',0); //首次充值赠送 0-不开启  1-首次才赠送，并且只能充值一次，（虚拟卡使用）
	    $isCustomRecharge = I('post.is_custom_recharge', 1, 'intval'); //是否允许自定义充值金额
	    $channel = I('post.channel', RechargeSchemeChannel::CLOUD, 'intval');  // 旧的园区一卡传 1， 新一卡通 1=云票务 2=微信or小程序
	
	
	    if (empty($schemeTitle) || empty($cardType)) {
            $this->apiReturn(204, [], '方案名称或一卡通类型为必填');
        }

        foreach ($discountRule as &$item) {
            $item['tag'] = trim($item['tag']);
        }
		
		// 如果卡类型不为新一卡通，强制将充值配置渠道改为云票务
		if($cardType != CardType::NEW_PARK_CARD) {
			$channel = RechargeSchemeChannel::CLOUD;
		}

        $data = [
            'aid'                  => $aid,
            'scheme_title'         => $schemeTitle,
            'card_type'            => $cardType,
            'active_begin_time'    => strtotime($activeBeginTime),
            'active_end_time'      => strtotime($activeEndTime),
            'is_forever'           => $isForever,
            'deposit'              => $deposit,
            'cost_fee'             => $costFee,
            'discount_rule'        => json_encode($discountRule),
            'create_time'          => time(),
            'replace_fee'          => $replaceFee,
            'lower_recharge_money' => $lowerRechargeMoney,
            'first_recharge_gift'  => (int)$firstRechargeGift,
            'is_custom_recharge'  => $isCustomRecharge, // new
            'channel' => $channel,
        ];

        $rechargeSchemeModel = new RechargeScheme();
        $rechargeBiz         = new Recharge();

        // 根据参数获取方案的状态
        $data['status'] = $rechargeBiz->getRechargeSchemeStatus($isForever, $data['active_begin_time'],
            $data['active_end_time']);
        if ($data['status'] == 2 && !in_array($cardType, [CardType::NEW_PARK_CARD, CardType::PARK_CARD])) {
            //欢乐太行谷计时卡特殊处理
            if ($cardType == CardType::TIME_CARD && in_array($aid, [8508087, 3385, 13995818])) {

            } else {
                // 检测是否有其他方案正在使用
                $checkRes = $rechargeBiz->checkRechargeSchemeStatus($aid, $cardType);
                if ($checkRes == false) {
                    $this->apiReturn(204, [], '该卡类型有正在使用中的方案，请先手工停止');
                }
            }
        }

        $res = $rechargeSchemeModel->addRechargeScheme($data);
        if ($res === false) {
            $this->apiReturn(204, [], '保存失败');
        }
        $this->apiReturn(200, [], '保存成功');
    }

    /**
     * 修改充值方案
     * @Author: zhujb
     * 2019/6/28
     */
    public function editRechargeScheme()
    {
        $aid                = $this->_memberId;
        $schemeId           = I('post.scheme_id', 0, 'intval');
        $schemeTitle        = I('post.scheme_title', '', 'strval,trim');
        $cardType           = I('post.card_type', 0, 'intval');
        $activeBeginTime    = I('post.active_begin_time', '', 'strval,trim');
        $activeEndTime      = I('post.active_end_time', '', 'strval,trim');
        $isForever          = I('post.is_forever', 0, 'intval');
        $deposit            = I('post.deposit', 0, 'intval');
        $costFee            = I('post.cost_fee', 0, 'intval');
        $discountRule       = I('post.discount_rule', []);
        $replaceFee         = I('post.replace_fee', 0, 'intval'); //补卡金额
        $lowerRechargeMoney = I('post.lower_recharge_money', 0, 'intval'); //最低充值金额
        $firstRechargeGift  = I('post.first_recharge_give_away',0); //首次充值赠送 0-不开启  1-首次才赠送，并且只能充值一次，（虚拟卡使用）
	    $isCustomRecharge = I('post.is_custom_recharge', 1, 'intval'); //是否允许自定义充值金额
	    $channel = I('post.channel', RechargeSchemeChannel::CLOUD, 'intval');  // 旧的园区一卡通传 1， 新一卡通 1=云票务 2=微信or小程序

        if (empty($schemeId) || empty($schemeTitle) || empty($cardType)) {
            $this->apiReturn(204, [], '参数错误');
        }

        foreach ($discountRule as &$item) {
            $item['tag'] = trim($item['tag']);
        }
	
	    if($cardType != CardType::NEW_PARK_CARD) {
		    $channel = RechargeSchemeChannel::CLOUD;
	    }

        $data = [
            'scheme_title'         => $schemeTitle,
            'card_type'            => $cardType,
            'active_begin_time'    => strtotime($activeBeginTime),
            'active_end_time'      => strtotime($activeEndTime),
            'is_forever'           => $isForever,
            'deposit'              => $deposit,
            'cost_fee'             => $costFee,
            'discount_rule'        => json_encode($discountRule),
            'replace_fee'          => $replaceFee,
            'lower_recharge_money' => $lowerRechargeMoney,
            'first_recharge_gift'  => (int)$firstRechargeGift,
            'is_custom_recharge'  => $isCustomRecharge, // add this feature as all_in_one_card_v3 REQ
            'channel' => $channel,
        ];

        $rechargeSchemeModel = new RechargeScheme();
        $rechargeBiz         = new Recharge();

        // 根据参数获取方案的状态
        $data['status'] = $rechargeBiz->getRechargeSchemeStatus($isForever, $data['active_begin_time'],
            $data['active_end_time']);
        if ($data['status'] == 2 && !in_array($cardType, [CardType::NEW_PARK_CARD, CardType::PARK_CARD])) {
            //欢乐太行谷计时卡特殊处理
            if ($cardType == CardType::TIME_CARD && in_array($aid, [8508087, 3385, 13995818])) {

            } else {
                // 检测是否有其他方案正在使用
                $checkRes = $rechargeBiz->checkRechargeSchemeStatus($aid, $cardType);
                if ($checkRes == false) {
                    $this->apiReturn(204, [], '该卡类型有正在使用中的方案，请先手工停止');
                }
            }
        }

        $res = $rechargeSchemeModel->editRechargeScheme($schemeId, $data);
        if ($res === false) {
            $this->apiReturn(204, [], '修改失败');
        }
        $this->apiReturn(200, [], '修改成功');
    }

    /**
     * 删除充值方案
     * @Author: zhujb
     * 2019/6/28
     */
    public function delRechargeScheme()
    {
        $aid      = $this->_memberId;
        $schemeId = I('post.scheme_id', 0, 'intval');

        if (empty($schemeId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $rechargeSchemeModel = new RechargeScheme();
        $res                 = $rechargeSchemeModel->delRechargeScheme($aid, $schemeId);
        if ($res === false) {
            $this->apiReturn(204, [], '删除失败');
        }
        $this->apiReturn(200, [], '删除成功');
    }

    /**
     * 结束掉这个方案
     * @Author: zhujb
     * 2019/6/28
     */
    public function stopRechargeScheme()
    {
        $schemeId = I('post.scheme_id', 0, 'intval');
        if (empty($schemeId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $rechargeBiz = new Recharge();
        $res         = $rechargeBiz->stopRechargeScheme($schemeId);
        if ($res === false) {
            $this->apiReturn(204, [], '操作成功');
        }
        $this->apiReturn(200, [], '操作成功');
    }

    /**
     * 获取一卡通消费流水列表 （接口暂不开放）
     * Create by zhangyangzhen
     * Date: 2019/11/1
     * Time: 11:38
     *
     * @param  string begin_time 开始时间
     * @param  string end_time 结束时间
     * @param  string keyword 关键字查询（可能是手机号或实体卡号）
     * @param  string ordernum 订单号
     * @param  string pay_type 支付方式,cash现金，alipay支付宝，wxpay微信支付，physical实体卡，virtual虚拟卡
     */
    public function getCardConsumeList()
    {
        $beginTime = I('post.begin_time', '', 'strval,trim');
        $endTime   = I('post.end_time', '', 'strval,trim');
        $ordernum  = I('post.ordernum', '', 'strval,trim');
        $payType   = I('post.pay_type', 0, 'intval,trim');
        $cardNo    = I('post.card_no', '', 'strval,trim');
        $mobile    = I('post.mobile', '', 'strval,trim');
        $page      = I('post.page', 1, 'intval');
        $size      = I('post.size', 10, 'intval');

        if (!strtotime($beginTime) || !strtotime($endTime)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $parkCard = new \Model\Product\ParkCard();

        $res = $parkCard->getCardConsumeList($beginTime, $endTime, $this->_memberId, $ordernum, $cardNo, $mobile,
            $payType, $page, $size);

        $this->apiReturn(self::CODE_SUCCESS, $res, 'success');
    }

    /**
     * 园区一卡通消费日汇总一览
     * <AUTHOR> Li
     * @date 2019-12-30
     *
     * @return array
     */
    public function getCardConsumeDaySummary()
    {
        //开始时间
        $beginTime = I('begin_time', '', 'strval,trim');
        //结束时间
        $endTime = I('end_time', '', 'strval,trim');
        //页数
        $page = I('page', 1, 'intval');
        //每页数量
        $size = I('size', 10, 'intval');
        //模板id
        $configId = I('search_config_id', 0, 'intval');
        //是否导出
        $excel = I('export_excel', 0, 'intval');
        //1 按日  2 按月
        $dataType = I('date_type', 1, 'intval');
        //操作员id
        $operateId = I('operate_id', 0, 'intval,trim');
        //卡号
        $cardNo = I('card_no', '', 'strval,trim');
        //手机号
        $mobile = I('mobile', '', 'strval,trim');
        //支付方式
        $payType = I('pay_type', 0, 'intval');
        //交易类型
        $operationType = I('operation_type', 0, 'intval');
        //订单号
        $ordernum = I('ordernum', '', 'strval');
        $fid      = I('fid', 0, 'intval');
        $siteId   = I('site_id', 0, 'intval');
        $channel  = I('channel', 0, 'intval');

        $code = 200;
        $data = [];
        $msg  = '';

        try {
            if (!$beginTime || !$endTime || !$configId) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '缺少必要参数');
            }

            $templateModel = new \Model\Report\Template();
            $template      = $templateModel->getTemplateById('', $configId);

            if (empty($template)) {
                throw new \Exception("模板配置未知");
            }

            //统计纬度
            $item = json_decode($template['item'], true);
            if (empty($item) || !is_array($item)) {
                throw new \Exception("配置有误, 统计纬度不能为空");
            }

            //可以被统计的纬度 清除掉没用的 整理纬度对应的汉字描述
            foreach ($item as $key => $value) {
                if (!array_key_exists($value, $this->_needItem)) {
                    unset($item[$key]);
                }

                $titleArr[] = $this->_needItem[$value];
            }

            $item = array_values($item);
            //$item = array_keys($this->_needItem);
            //报表名称
            $name = "{$beginTime}~{$endTime} {$template['name']}";
            //统计指标
            $target = json_decode($template['target'], true);
            if (empty($target) || !is_array($target)) {
                $target = [];
            }
            $finance = $this->_checkFinance($this->_mid);
            $sid     = $this->_mid == $this->_memberId ? 1 : 0;
            //员工的数据权限
            $dateScope = (new AuthLogicBiz())->memberDataResource($this->_memberId, $this->_mid, 6, 'one_card_order_statis');
            $isSuper   = 0;
            if ($finance || $sid || $dateScope) {
                $isSuper = 1;
            }
            $parkCard = new \Model\Product\ParkCard();
            $orderDetailArr = $parkCard->getCardConsumeDaySummary($beginTime, $endTime, $this->_memberId, $operateId,
                $cardNo, $mobile, $dataType, $payType, $operationType, $ordernum, false, $excel, true, $page,
                $size, $fid, $siteId, $channel, $isSuper, $this->_mid);
            $total          = $parkCard->getCardConsumeDaySummary($beginTime, $endTime, $this->_memberId, $operateId,
                $cardNo, $mobile, $dataType, $payType, $operationType, $ordernum, true, $excel, true, 1,10,
                $fid, $siteId, $channel, $isSuper, $this->_mid);
            //总合计不管分页
            $sumOrderList   = $parkCard->getCardConsumeDaySummary($beginTime, $endTime, $this->_memberId, $operateId,
                $cardNo, $mobile, $dataType, $payType, $operationType, $ordernum, false, $excel, false, $page,
                $size, $fid, $siteId, $channel, $isSuper, $this->_mid);

            $opIdArr          = array_unique(array_column($orderDetailArr, 'operate_id'));
            $opIdArr          = array_merge($opIdArr, array_unique(array_column($orderDetailArr, 'fid')));
            $memberModel      = new Member('slave');
            $this->memberData = $memberModel->getMemberInfoByMulti($opIdArr, 'id', true);

            $siteIdArr   = array_unique(array_column($orderDetailArr, 'site_id')); //站点id
            $site        = new SiteManage();
            $siteNameMap = $site->getDataBySiteIdArrAndSid(array_values($siteIdArr), $this->_memberId, 'id,sname');
            $siteNameMap = empty($siteNameMap) ? [] : array_column($siteNameMap, null, 'id');
            // 如果是消费类订单记录，需要统一提取订单号，然后通过订单号查找到对应的景区名和门票名
	        $ordernumArr = [];
	        foreach($orderDetailArr as $_order){
		        if(\Business\Product\ParkCard::TRADE_TYPE_CARD_CONSUME ==  $_order['operation_type']){
			        $ordernumArr[] = $_order['ordernum'];
		        }
	        }
	        $ordernumArr = array_unique($ordernumArr);
	        $orderList = $this->_getOrderInfoListByOrderNums($ordernumArr);
            $parkBiz = new \Business\Product\ParkCard();

	        $res = $parkBiz->handleData($orderDetailArr, $item, $dataType, $this->memberData, $orderList, $siteNameMap);

            //获取合计总和
	        $sumRes  = $parkBiz->handleData($sumOrderList, $item, $dataType, $this->memberData, $orderList);

            $list = $res['list'];
            $sum  = $sumRes['sum'];

            //列表
            $data['list'] = $list;
            //总和
            $data['sum'] = $sum;
            //最细纬度下的总条数
            $data['total'] = $total;
            //纬度名称
            $data['title'] = $titleArr;
            //报表名称
            $data['name'] = $name;
            //统计指标
            $data['target'] = $target;
            //统计方式 按月/按日
            $data['date_type'] = $dataType;

        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        if ($excel && $code == 200) {
            $this->_cardConsumeDayExportData($data);
        } else {

            $this->apiReturn($code, $data, $msg);
        }
    }
    
    private function _getOrderInfoListByOrderNums($ordernumArr = []){
	    $uusOrderService =  new UusOrder();
	    $orderListResult = $uusOrderService->queryOrderInfoByOrdernumList($ordernumArr, true);
	    $orderList = [];
	    if(200 == $orderListResult['code']){
		    $orderNumTicketMap = [];
		    $ticketIds = [];
		    foreach($orderListResult['data'] as $_){
			    $_ordernum = $_['ordernum'];
			    $_ticketId = $_['tid'];
			    $ticketIds[] = $_ticketId;
			    $orderNumTicketMap[$_ordernum] = $_ticketId;
		    }
		
		    $ticketIds = array_unique($ticketIds);
		
		    $ticketJavaApiService = new \Business\CommodityCenter\Ticket();
		    $ticketResult = $ticketJavaApiService->queryTicketInfoByIds($ticketIds, 'id,title', 'id', 'id,title,p_type','id');
		    $ticketList = [];
		    if(is_array($ticketResult)){
			    foreach($ticketResult as $_){
				    $tid =  $_['ticket']['id'];
				    $tname = $_['ticket']['title'];
				    $lid = $_['land']['id'];
				    $lname = $_['land']['title'];
				    $row = [];
				    $row['tid'] = $tid;
				    $row['tname'] = $tname;
				    $row['lid'] = $lid;
				    $row['lname'] = $lname;
				    $ticketList[$tid] = $row;
			    }
		    }
		
		    foreach($orderNumTicketMap as $ordernum=>$tid){
			    if(array_key_exists($tid, $ticketList)){
				    $orderList[$ordernum] = $ticketList[$tid];
			    }
		    }
	    }
		return $orderList;
    }
    
    /**
     * 计时订单报表导出
     *
     * <AUTHOR>
     * @date   2019-12-10
     *
     * @params  array       $data       数据
     *
     * @return
     */
    private function _cardConsumeDayExportData($data)
    {
        $filename = date('Ymd') . '一卡通订单报表';

        //数据列表
        $list = $data['list'];
        //报表名称
        $name = $data['name'];
        //纬度
        $title = $data['title'];
        //总和
        $sum = $data['sum'];
        //统计指标
        $target = $data['target'];

        if (empty($target)) {
            $target = [];
        }

        $excel[0] = [
            $name,
        ];
	
	    // $excel[1] 包含Excel 表头列['日期','实体卡号',,,,,]
        //配置的纬度
        foreach ((array)$title as $value) {
            $excel[1][] = $value;
        }

        //配置的统计指标
        if (!empty($target)) {
            foreach ((array)$target as $key => $value) {
                if (isset($this->_targetItem[$value])) {
                    $excel[1][] = $this->_targetItem[$value];
                }

            }
        }

        //从第二行开始
        $i = 2;
        foreach ((array)$list as $valueOne) {
        	
            switch (count($title)) {
            	
                case 1:
                    $excel[$i][] = $valueOne['title'];

                    foreach ($target as $item) {
                        $excel[$i][] = $valueOne['list'][$item];
                    }

                    $i++;
                    break;

                case 2:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        $excel[$i][] = $valueOne['title'];
                        $excel[$i][] = $valueTwo['title'];

                        foreach ($target as $item) {
                            $excel[$i][] = $valueTwo['list'][$item];
                        }

                        $i++;
                    }

                    break;
                case 3:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            $excel[$i][] = $valueOne['title'];
                            $excel[$i][] = $valueTwo['title'];
                            $excel[$i][] = $valueThree['title'];

                            foreach ($target as $item) {
                                $excel[$i][] = $valueThree['list'][$item];
                            }

                            $i++;
                        }
                    }
                    break;
                case 4:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                $excel[$i][] = $valueOne['title'];
                                $excel[$i][] = $valueTwo['title'];
                                $excel[$i][] = $valueThree['title'];
                                $excel[$i][] = $valueFour['title'];

                                foreach ($target as $item) {
                                    $excel[$i][] = $valueFour['list'][$item];
                                }

                                $i++;
                            }
                        }
                    }
                    break;
                case 5:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    $excel[$i][] = $valueOne['title'];
                                    $excel[$i][] = $valueTwo['title'];
                                    $excel[$i][] = $valueThree['title'];
                                    $excel[$i][] = $valueFour['title'];
                                    $excel[$i][] = $valueFive['title'];

                                    foreach ($target as $item) {
                                        $excel[$i][] = $valueFive['list'][$item];
                                    }

                                    $i++;
                                }
                            }
                        }
                    }
                    break;
                case 6:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        $excel[$i][] = $valueOne['title'];
                                        $excel[$i][] = $valueTwo['title'];
                                        $excel[$i][] = $valueThree['title'];
                                        $excel[$i][] = $valueFour['title'];
                                        $excel[$i][] = $valueFive['title'];
                                        $excel[$i][] = $valueSix['title'];

                                        foreach ($target as $item) {
                                            $excel[$i][] = $valueSix['list'][$item];
                                        }

                                        $i++;
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 7:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            $excel[$i][] = $valueOne['title'];
                                            $excel[$i][] = $valueTwo['title'];
                                            $excel[$i][] = $valueThree['title'];
                                            $excel[$i][] = $valueFour['title'];
                                            $excel[$i][] = $valueFive['title'];
                                            $excel[$i][] = $valueSix['title'];
                                            $excel[$i][] = $valueSeven['title'];

                                            foreach ($target as $item) {
                                                $excel[$i][] = $valueSeven['list'][$item];
                                            }

                                            $i++;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 8:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            foreach ($valueSeven['list'] as $keyEight => $valueEight) {
                                                $excel[$i][] = $valueOne['title'];
                                                $excel[$i][] = $valueTwo['title'];
                                                $excel[$i][] = $valueThree['title'];
                                                $excel[$i][] = $valueFour['title'];
                                                $excel[$i][] = $valueFive['title'];
                                                $excel[$i][] = $valueSix['title'];
                                                $excel[$i][] = $valueSeven['title'];
                                                $excel[$i][] = $valueEight['title'];

                                                foreach ($target as $item) {
                                                    $excel[$i][] = $valueEight['list'][$item];
                                                }

                                                $i++;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 9:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            foreach ($valueSeven['list'] as $keyEight => $valueEight) {
                                                foreach ($valueEight['list'] as $keyNine => $valueNine) {
                                                    $excel[$i][] = $valueOne['title'];
                                                    $excel[$i][] = $valueTwo['title'];
                                                    $excel[$i][] = $valueThree['title'];
                                                    $excel[$i][] = $valueFour['title'];
                                                    $excel[$i][] = $valueFive['title'];
                                                    $excel[$i][] = $valueSix['title'];
                                                    $excel[$i][] = $valueSeven['title'];
                                                    $excel[$i][] = $valueEight['title'];
                                                    $excel[$i][] = $valueNine['title'];

                                                    foreach ($target as $item) {
                                                        $excel[$i][] = $valueNine['list'][$item];
                                                    }
                                                    $i++;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
                case 10:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        foreach ($valueSix['list'] as $keySeven => $valueSeven) {
                                            foreach ($valueSeven['list'] as $keyEight => $valueEight) {
                                                foreach ($valueEight['list'] as $keyNine => $valueNine) {
                                                    foreach ($valueNine['list'] as $keyTen => $valueTen) {
                                                        $excel[$i][] = $valueOne['title'];
                                                        $excel[$i][] = $valueTwo['title'];
                                                        $excel[$i][] = $valueThree['title'];
                                                        $excel[$i][] = $valueFour['title'];
                                                        $excel[$i][] = $valueFive['title'];
                                                        $excel[$i][] = $valueSix['title'];
                                                        $excel[$i][] = $valueSeven['title'];
                                                        $excel[$i][] = $valueEight['title'];
                                                        $excel[$i][] = $valueNine['title'];
                                                        $excel[$i][] = $valueTen['title'];

                                                        foreach ($target as $item) {
                                                            $excel[$i][] = $valueTen['list'][$item];
                                                        }
                                                        $i++;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;
            }
        }

        $total[] = '总计';
        for ($i = 0; $i < count($title) - 1; $i++) {
            $total[] = '';
        }
        foreach ($target as $temp) {
            array_push($total, $sum[$temp]);
        }

        $excel[] = $total;
        $xls     = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }

    private function _exportOneCardStatisticsReport($data)
    {
        $filename = date('Ymd') . '一卡通交易报表';

        $sum = [
            //'total_operators' => $data['total_operators'],
            'total'        => $data['total'] / 100,
            'total_cash'   => $data['total_cash'] / 100,
            'total_wx'     => $data['total_wx'] / 100,
            'total_alipay' => $data['total_alipay'] / 100,
            'total_reward' => $data['total_reward'] / 100,
            'total_pos'    => $data['total_pos'] / 100,
            'total_credit' => $data['total_credit'] / 100,
        ];
        if (isset($data['operators']['details'])) {
            //不展示营业员
            $title = [
                //'total_operators',
                'total',
                'total_cash',
                'total_wx',
                'total_alipay',
                'total_reward',
                'total_pos',
                'total_credit',
            ];
            $list  = $data['operators']['details'];
        } else {
            //展示营业员
            $title = [
                'total_operators',
                'total',
                'total_cash',
                'total_wx',
                'total_alipay',
                'total_reward',
                'total_pos',
                'total_credit',
            ];
            $list  = $data['operators'];
        }

        $excel[0] = [
            $filename,
        ];

        //从第二行开始
        $i = 2;
        foreach ((array)$list as $key => $item) {
            if (isset($item['user_name'])) {
                $excel[$i][] = '合计';
                $excel[$i][] = '';
                $excel[$i][] = '';
                $excel[$i][] = $item['total'] / 100;
                $excel[$i][] = $item['total_cash'] / 100;
                $excel[$i][] = $item['total_wx'] / 100;
                $excel[$i][] = $item['total_alipay'] / 100;
                $excel[$i][] = $item['total_reward'] / 100;
                $excel[$i][] = $item['total_pos'] / 100;
                $excel[$i][] = $item['total_credit'] / 100;
                $i++;
                $excel[$i] = ['分销商', '营业员', '类型', '笔数', '现金', '微信', '支付宝', '奖励金', 'POS支付', '授信支付'];
                $i++;
                foreach ($item['operate'] as $tmpOperate) {
                    foreach ($tmpOperate['details'] as $type => $value) {
                        $excel[$i][] = $item['user_name'];
                        $excel[$i][] = $tmpOperate['user_name'];
                        $excel[$i][] = $value['name'];
                        $excel[$i][] = $value['num'];
                        $excel[$i][] = $value['cash'] / 100;
                        $excel[$i][] = $value['wx'] / 100;
                        $excel[$i][] = $value['alipay'] / 100;
                        $excel[$i][] = $value['reward'] / 100;
                        $excel[$i][] = $value['pos'] / 100;
                        $excel[$i][] = $value['credit'] / 100;
                        $i++;
                    }
                }
            } else {
                $excel[2] = ['类型', '笔数', '现金', '微信', '支付宝', '奖励金', 'POS支付', '授信支付'];
                $i++;
                $excel[$i][] = $item['name'];
                $excel[$i][] = $item['num'];
                $excel[$i][] = $item['cash'] / 100;
                $excel[$i][] = $item['wx'] / 100;
                $excel[$i][] = $item['alipay'] / 100;
                $excel[$i][] = $item['reward'] / 100;
                $excel[$i][] = $item['total_pos'] / 100;
                $excel[$i][] = $item['total_credit'] / 100;
                $i++;

            }
        }

        $total[] = '总计';
        foreach ($title as $temp) {
            if ($temp == 'total_operators') {
                $total[] = '';
                $total[] = '';
            }
            if (isset($sum[$temp])) {
                array_push($total, $sum[$temp]);
            }
        }

        $excel[] = $total;
        $xls     = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }

}
