<?php
/**
 * Created by PhpStorm.
 * User: lanlc
 * Date: 17-2-17
 * Time: 上午10:50
 */

namespace Controller\Product;

use Business\CommodityCenter\TicketAttribute;
use Library\Controller;
use Library\Resque\Queue;
use Model\Product\Marketing;
use Model\Order\Coupon as couponModel;
use Model\Product\Ticket;
use Model\Product\Area;
use Model\Product\MemberCard;
use Library\Business\ReadExcel;
use Model\Wechat\WxMember;
use Business\JavaApi\Ticket\Listing;
use Business\Product\ProductList;
use Business\Wechat\Share;
use Model\SystemLog\Statistics;
use Model\Member\Member;

class Coupon extends Controller
{
    private $_memberId;

    public function __construct()
    {

    }

    /**
     * 获取营销列表
     * @date   2017-02-17
     * <AUTHOR>
     *
     * @param  int $spId 营销活动分享页面表ID
     *
     * @return
     */
    public function marketingListSp()
    {
        $this->_memberId = $this->isLogin();
        $spId            = I('post.spid', '', 'intval');
        if (!$spId) {
            $this->apiReturn(201, [], '缺少参数');
        }
        $marketingModel = new Marketing();

        $field = 'sp.title, sp.share_type, sp.coupon_id, sp.relation_pid, sp.coupon_num, sp.content, sp.image_path, sp.red_pack_money,sp.is_send,
        m.id as mkid,m.activity_bt,m.activity_et,m.only_member';

        $res = $marketingModel->getMarketingList($spId, $field);
        if ($res) {
            if ($res[0]['red_pack_money']) {
                $res[0]['red_pack_money'] /= 100;
            }
            $res[0]['content'] = html_entity_decode($res[0]['content']);
            $this->apiReturn(200, ['data' => $res], '');
        } else {
            $this->apiReturn(201, [], '没有相关数据');
        }
    }

    /**
     * 获取优惠券信息
     * @date   2017-02-17
     * <AUTHOR>
     *
     * @return
     */
    public function getCoupon()
    {
        $this->_memberId = $this->isLogin();
        $couponModel     = new couponModel('remote_1');
        $res             = $couponModel->getCoupon($this->_memberId);
        if ($res) {
            $data = ['data' => $res];
            $this->apiReturn(200, $data, '');
        } else {
            $this->apiReturn(202, [], '没有相关数据');
        }
    }

    /**
     * 获取可销售产品名称
     * author  leafzl
     * Date:2018-4-29
     */

    public function getProName()
    {
        $pid                 = I('pid');
        $commodityProductBiz = new \Business\CommodityCenter\Product();
        $data                = $commodityProductBiz->getProductInfoById($pid);
        if ($data) {
            $this->apiReturn(200, $data['p_name'], '获取成功');
        } else {
            $this->apiReturn(204, [], '没有数据');
        }

    }

    /**
     * 获取可销售产品列表
     * author  leafzl
     * Date:2018-3-29
     */

    public function getProTicket()
    {
        $this->_memberId = $this->isLogin();
        $name            = I('name');
        $type            = I('type', "A,B,G");
        $type            = explode(',', $type);
        $pro             = new Listing();
        $res             = $pro->selectListing($this->_memberId, $name, $type, 30);
        if ($res['code'] == 200) {

            $this->apiReturn(200, $res['data'], '获取成功');
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

    }

    /**
     * 获取团购导码可销售产品列表(允许产品类型 A景区，B线路、G餐饮)
     * author  weijie
     * Date:2018-7-12
     *
     * @param  string $name 根据产品名获取
     *
     * @return array   产品列表对应票类
     */

    public function getProTicketType()
    {
        $this->_memberId = $this->isLogin();
        $name            = I('name');
        $type            = "A,B,G";
        $pro             = new ProductList();

        $res = $pro->getListSupplierId($this->_memberId, 30, $name, $type, true);
        if ($res['code'] == 200) {

            $this->apiReturn(200, $res['data'], '获取成功');
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
    }

    /**
     * 获取可销售产品列表
     * @date   2017-02-17
     * <AUTHOR>
     *
     * @return
     */
    public function getProducts()
    {
        $this->_memberId = $this->isLogin();
        $page            = I('post.page', 1, 'intval');
        $size            = I('post.size', 10, 'intval');
//        $ticketModel = new Ticket();
//        $selfPro = $ticketModel->getSaleProducts($this->_memberId);
//        //$sellPro = $ticketModel->getSaleDisProducts($this->_memberId);
//        $sellPro = $ticketModel->getSaleDisProductsByFidSourceIdStatus($this->_memberId);
        $ticketAttrBz = new TicketAttribute();
        $result       = $ticketAttrBz->getTicketAttribute($this->_memberId, 0, -1, '', -1, '', 0, 0, '', [], 0, 0, [],
            0, '', 1, $page, $size);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }
        $list = $result['data']['list'];
        //获取景区跟票信息
        $tidArr       = array_column($list, 'tid');
        $ticketBz     = new \Business\CommodityCenter\Ticket();
        $ticketResult = $ticketBz->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable('id,title', '', 'title', '',
            false, $tidArr, null, null, null, ['landFlag' => 1]);
        foreach ($ticketResult as $value) {
            $ticketInfo[$value['ticket']['id']]['ttitle'] = $value['ticket']['title'];
            $ticketInfo[$value['ticket']['id']]['title']  = $value['land']['title'];
        }
        $response = [];
        if ($list) {
            foreach ($list as $row) {
                $response[] = array(
                    'aid'    => $row['sapply_sid'],
                    'pid'    => $row['pid'],
                    'fid'    => $this->_memberId,
                    'ttitle' => $ticketInfo[$row['tid']]['ttitle'],
                    'ltitle' => $ticketInfo[$row['tid']]['title'],
                );
            }
        }

        if ($response) {
            $this->apiReturn(200, $response, '');
        } else {
            $this->apiReturn(201, [], '没有相关数据');
        }
    }

    /**
     * 创建/编辑分享优惠券活动数据
     * @date   2017-02-17
     * <AUTHOR>
     *
     * @param  int $shareType 分享赠送类型：1送优惠券2送红包3买即送
     * @param  int $couponId 优惠券ID
     * @param  int $couponNum 优惠券赠送张数 -1随订单
     * @param  int $relationPid 关联的产品ID 0
     * @param  int $redPackMoney 红包金额
     * @param  int $fid 会员ID
     * @param  int $content 图文介绍,富文本
     * @param  int $imagePath 图片地址
     * @param  int $title 页面标题
     * @param  int $spId 自增ID
     *
     * @param  int $Id 营销活动表ID
     * @param  int $onlyMember 是否必须为会员才能参与活动
     * @param  int $activityBt 活动开始时间,存时间戳
     * @param  int $activityEt 活动结束时间,存时间戳
     *
     * @return
     */
    public function CreateShare()
    {
        $this->_memberId = $this->isLogin();
        $shareType       = I('post.share_type', '', 'intval');
        $couponId        = I('post.coupon_id', '', 'intval');
        $couponNum       = I('post.coupon_num', '', 'intval');
        $relationPid     = I('post.relation_pid', '', 'intval');
        $redPackMoney    = I('post.red_pack_money', 0, 'float');

        $content   = htmlentities((I('post.content', '', 'strval')));
        $imagePath = safe_str(I('post.thumb', '', 'strval'));
        $title     = safe_str(I('post.activity_name', '', 'strval'));
        $spid      = I('post.spid', '', 'intval');

        $Id         = I('post.id', '', 'intval');
        $onlyMember = I('post.only_member', '', 'intval');
        $activityBt = I('post.bt', '', 'intval');
        $activityEt = I('post.et', '', 'intval');
        $isSend     = I('post.is_send', '', 'intval');

        if (!$title) {
            $this->apiReturn(202, [], '活动名称不能为空');
        }
        if (!in_array($onlyMember, [0, 1, 2])) {
            $this->apiReturn(202, [], '仅限会员设置错误！只能为0或1或者2');
        }

        if (!$activityBt || !$activityEt) {
            $this->apiReturn(202, [], '开始时间或结束时间不能为空');
        }

        $marketingModel = new Marketing();
        $redPackMoney   *= 100;
        $shareId        = $marketingModel->setMarketingSp($shareType, $redPackMoney, $couponId, $couponNum,
            $relationPid, $this->_memberId, $content, $imagePath, $title, $spid, $isSend);
        if ($shareId) {
            $res = $marketingModel->setMarketing($Id, $this->_memberId, $title, $onlyMember, $activityBt, $activityEt,
                $shareId);
            if ($res) {
                $this->apiReturn(200, [], '保存成功');
            } else {
                $this->apiReturn(201, [], '保存失败，请重试');
            }
        } else {
            $this->apiReturn(201, [], '保存失败，请重试');
        }
    }

    /**
     * 发送优惠券-钩子调用的函数
     * @date   2017-04-20
     * <AUTHOR> Lan
     *
     * @param  Mixed $data
     *
     * @return bool
     */
    public function sendCoupon($data)
    {

        pft_log("coupon/sendCoupon", json_encode($data, JSON_UNESCAPED_UNICODE));
        if (!$data) {
            return '非法访问!';
        }

        //订单号
        $orderId = $data['mainOrder']['ordernum'];
        //下单来源
        $source = $data['mainOrder']['ordermode'];

        if (!in_array($source, [11, 12, 13, 19])) {
            return false;
        }
        $orderArr        = [];
        $pidArr          = [];
        $orderToolsModel = new \Model\Order\OrderTools();

        $orderIds = $orderToolsModel->getLinkSubOrder($orderId);
        if ($orderIds) {
            $orderArr[] = $orderIds;
        } else {
            $orderArr[] = $orderId;
        }

        $field     = 'pid, tnum, member, ordernum';
        $orderInfo = $orderToolsModel->getOrderInfo($orderArr, $field);
        if (!$orderInfo) {
            return false;
        }
        foreach ($orderInfo as $val) {
            $pidArr[$val['pid']] = [$val['tnum'], $val['ordernum']];
        }
        //对应产品是否有做下单送优惠券活动
        $marketingModel = new Marketing();
        $resMarketing   = $marketingModel->getMarketing(array_keys($pidArr));
        if (!$resMarketing) {
            return false;
        }
        $markingData = [];
        foreach ($resMarketing as $key => $row) {
            if ($row['only_member'] == 1) {
                continue;
            }
            $markingData[$key]            = $row;
            $markingData[$key]['orderid'] = $pidArr[$row['relation_pid']][1];
            $markingData[$key]['tnum']    = $pidArr[$row['relation_pid']][0];
        }
        if ($markingData) {
            Queue::push('marketing_system', 'CouponSend_Job',
                array(
                    'couponData' => $markingData,
                    'memberID'   => $orderInfo[0]['member'],
                )
            );

            return true;
        }

        return false;
    }

    /**
     * 批量发送优惠券-导入文件的设置优先
     * @date   2017-05-08
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function sendBYBatch()
    {
        $memberId = $this->isLogin();
        if (!empty ($_FILES['file_coupon']['name'])) {
            $redis = \Library\Cache\Cache::getInstance('redis');
            $lock  = $redis->get('lock:sendBYBatch');
            if ($lock) {
                self::uploadReturn(207, [], '请勿频繁操作');
            }
            $redis->lock('lock:sendBYBatch', 1, 60);
            $tempFile  = $_FILES['file_coupon']['tmp_name'];
            $fileTypes = explode(".", $_FILES['file_coupon']['name']);
            $fileType  = $fileTypes[count($fileTypes) - 1];
            //判别是不是.xls文件，判别是不是excel文件
            if (!in_array(strtolower($fileType), ['xls', 'xlsx'])) {
                $redis->rm('lock:sendBYBatch');
                self::uploadReturn(201, [], '不是Excel文件，重新上传');
            }
        } else {
            self::uploadReturn(204, [], '请选择文件');
        }

        $couponId  = I('post.coupon_id', '', 'intval');
        $couponNum = I('post.coupon_num', '', 'intval');

        $readExcel = new ReadExcel();
        $data      = $readExcel->read($tempFile);

        $couponIdArr = [];
        if ($data) {
            $couponIdArr = array_unique(array_column($data, 2));
            $couponModel = new couponModel();
            $map         = [
                'apply_did' => $memberId,
                'status'    => 0,
            ];
            $couponInfo  = $couponModel->getCoupons('id', $couponIdArr,
                'id, coupon_name, apply_did, use_startdate, use_enddate', $map);
            if (count($couponIdArr) != count($couponInfo)) {
                $redis->rm('lock:sendBYBatch');
                self::uploadReturn(205, [], '部分优惠券Id不存在');
            }
            $wxModel   = new \Model\Wechat\WxMember();
            $modelCard = new \Model\Product\MemberCard();
            foreach ($data as $key => $val) {
                if (!$val[0]) {
                    pft_log('coupon/Excel', print_r($val, true) . '该条记录没有会员ID');
                    continue;
                }
                if (isset($val[3]) && $val[3] > 0) {
                    $Num = $val[3];  //赠送数量
                } else {
                    $Num = $couponNum;

                }
                if (isset($val[2]) && $val[2] > 0) {
                    $coupon_id = $val[2];  //优惠券id
                } else {
                    $coupon_id = $couponId;
                }

                $memberID                     = $val[0];
                $markingData                  = []; //初始化，由于当初的memberID分开了，导致得多条入队列
                $markingData[0]['fid']        = $memberId;   //供应商
                $markingData[0]['coupon_id']  = $coupon_id;
                $markingData[0]['orderid']    = 0;
                $markingData[0]['tnum']       = 0;
                $markingData[0]['coupon_num'] = $Num;

                $queueRes = Queue::push('marketing_system', 'CouponSend_Job',
                    array(
                        'couponData' => $markingData,
                        'memberID'   => $memberID,
                    )
                );
            }
            $redis->rm('lock:sendBYBatch');
            unlink($tempFile);
            self::uploadReturn(200, [], '导入成功');
        }
        $redis->rm('lock:sendBYBatch');
        unlink($tempFile);
        self::uploadReturn(400, [], '数据格式错误');
    }

    /**
     * 上传文件返回
     * @date   2017-05-05
     * <AUTHOR> Lan
     *
     * @param  int $code 返回码
     * @param  array $data 返回数据
     * @param  $msg   string  具体说明
     *
     * @return string
     */
    static private function uploadReturn($code, $data, $msg)
    {
        if ($data == []) {
            $data = new \stdClass;
        }

        $data   = array(
            'code' => $code,
            'data' => $data,
            'msg'  => $msg,
        );
        $list   = json_encode($data, JSON_UNESCAPED_UNICODE);
        $string = "
                <script type=\"text/javascript\">
                    var FileuploadCallbacks=window.parent.FileuploadCallbacks[3];
                    for(var i in FileuploadCallbacks) FileuploadCallbacks[i]($list);
                </script>
                ";
        echo $string;
        exit();
    }

    /**
     * 分享活动页面
     *
     * @date   2017-10-05
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function shareData()
    {
        header('Access-Control-Allow-Origin:*');
        $appIdList = array(
            'wx.12301.cc'     => 'wxd72be21f7455640d',//票付通
            '100005.12301.cc' => 'wx678d02326b6ba9d9',//三亚先行
        );

        $weChatAppId = $appIdList[$_SERVER['HTTP_HOST']];

        $code  = I('code', '', 'strval');
        $appId = I('appId', '', 'strval');

        $pageId = I('page_id', '', 'intval');
        $url    = I('url', '', 'strval');

        if ($pageId === '') {
            $this->apiReturn(201, [], '活动ID参数不能为空');
        }

        if ($url == '') {
            $this->apiReturn(201, [], 'url参数不能为空');
        }

        $marketingModel = new Marketing();

        if (!$weChatAppId && !$code) {
            $res = $marketingModel->getMarketingSpByPage($pageId);

            if (!$res) {
                $this->apiReturn(201, [], '商家数据异常');
            }

            $fid = $res['fid'];

            $wxModel = new WxMember();
            $res     = $wxModel->getDataByParams($fid, 'appid', ['isconnect' => 0]);

            if (!$res) {
                $this->apiReturn(201, [], '商家数据异常');
            }

            $weChatAppId = $res['appid'];
        }

        //判断是否是字母数字组合
        $realAppId = ctype_alnum($appId) ? strip_tags($appId) : '';

        if (!$realAppId) {
            $this->apiReturn(201, [], 'appId参数有误');
        }

        $url = 'http://' . $url;

        $weChatData = Share::getAuthorData($appId, $code, $url);

        $weChatData['wechat_appid'] = $weChatAppId;
        $weChatData['real_appid']   = $realAppId;
        $weChatData['key']          = Share::generateKey($weChatData['openid'] . $realAppId);

        $field = "sp.id AS page_id,sp.title,sp.content,sp.image_path,sp.coupon_id,sp.coupon_num,
                    sp.red_pack_money,sp.share_type,m.fid as aid,m.only_member,
                    FROM_UNIXTIME(m.activity_bt,'%Y%m%d%H%i%s') AS begin_time,
                    FROM_UNIXTIME(m.activity_et,'%Y%m%d%H%i%s') AS end_time";

        $res = $marketingModel->getMarketingList($pageId, $field, $map = ['m.status' => ['IN', [0, 2]]]);

        if (!$res) {
            $this->apiReturn('202', [], '没有相关营销活动');
        }

        $marketData = $res[0];
        $today      = date('YmdHis');
        if ($today < $marketData['begin_time']) {
            $this->apiReturn('204', [], '活动还没有开始');
        }

        if ($today > $marketData['end_time']) {
            $this->apiReturn('204', [], '活动已过期');
        }
        $marketData['content'] = htmlspecialchars_decode($marketData['content']);
        $this->apiReturn('200', ['marketingData' => $marketData, 'weChatData' => $weChatData], 'success');
    }

    /**
     * 微信分享的后续操作
     *
     * @date   2017-10-06
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function afterShareAction()
    {
        header('Access-Control-Allow-Origin:*');
        $pageId = I('post.page_id', '', 'intval');
        $openId = I('post.openid', '', 'intval');
        $appId  = I('post.appid', '', 'intval');
        $key    = I('post.key', '', 'strval');

//        $pageId = 515;
//        $openId = 'oNbmEuEMJw91MNfMl6ByZVVk2233';
//        $appId  = 'wx678d02326b6ba9d9';
//        $key = 123;

        if (!$pageId || !$openId || !$appId || !$key) {
            $this->apiReturn(201, [], '参数有误');
        }

        if (!Share::verifyKey($openId . $appId, $key)) {
            $this->apiReturn(201, [], '非法提交');
        }

        //由于是连续操作，无需验证活动是否开始

        $wxModel = new WxMember();
        $res     = $wxModel->getDataByOpenIdAndAppId($openId, $appId);

        if (empty($res)) {
            $this->apiReturn(201, [], '亲，您还没有绑定会员账号');
        }

        $fid = $res['fid'];

        $marketingModel = new Marketing();

        //这里取相同的语句是为了命中cache
        $field = "sp.id AS page_id,sp.title,sp.content,sp.image_path,sp.coupon_id,sp.coupon_num,
                    sp.red_pack_money,sp.share_type,m.fid as aid,m.only_member,
                    FROM_UNIXTIME(m.activity_bt,'%Y%m%d%H%i%s') AS begin_time,
                    FROM_UNIXTIME(m.activity_et,'%Y%m%d%H%i%s') AS end_time";

        $res = $marketingModel->getMarketingList($pageId, $field, $map = ['m.status' => ['IN', [0, 2]]]);

        if (!$res) {
            $this->apiReturn('202', [], '没有相关营销活动');
        }

        $marketData = $res[0];
        $today      = date('YmdHis');
        if ($today < $marketData['begin_time']) {
            $this->apiReturn('204', [], '活动还没有开始');
        }

        if ($today > $marketData['end_time']) {
            $this->apiReturn('205', [], '活动已过期');
        }

        if ($marketData['only_member'] == 1) {
            $memberCardModel = new \Model\Product\MemberCard();
            $res             = $memberCardModel->getDataByMemberId($fid, 'id');
            if (empty($res)) {
                $this->apiReturn('206', [], '抱歉，您还没有开通会员卡');
            }
        }

        $res = $marketingModel->getShareLog($openId, $pageId, 'id');
        if (!empty($res)) {
            $this->apiReturn('207', [], '您已经分享过了');
        }

        $time = date('Y-m-d H:i:s');
        //由于是队列运行，这里直接简化为已到账
        $res = $marketingModel->saveLog($fid, $pageId, $openId, $appId, $time, $flag = 1);

        if (empty($res)) {
            $this->apiReturn('207', [], '系统异常，稍后重试');
        }
        $statistics = new Statistics();
        $date       = strtotime(date('Y-m-d'));
        $statistics->saveStatistice($pageId, $date, 1, 0, 0);

        if ($marketData['share_type'] == 1) {
            $queueData                  = [];
            $queueData[0]['fid']        = $marketData['aid'];   //供应商
            $queueData[0]['coupon_id']  = $marketData['coupon_id'];
            $queueData[0]['orderid']    = 0;
            $queueData[0]['tnum']       = 0;
            $queueData[0]['coupon_num'] = $marketData['coupon_num'];

            Queue::push('marketing_system', 'CouponSend_Job',
                array(
                    'couponData' => $queueData,
                    'memberID'   => $fid,
                )
            );
            $this->apiReturn('200', [], '优惠券发放中，请稍后查看账号');
        } else {
            $this->apiReturn('207', [], '暂时不支持送红包功能');
        }

    }

    /**
     * 优惠券管理之优惠券汇总
     *
     * @date   2017-12-01
     * <AUTHOR> Lan
     *
     * @param
     * [
     *    'coupon_id' => '优惠券ID'
     *    'rstime'    => '发放时间-起始'
     *    'retime'    => '发放时间-结束'
     *    'ustime'    => '使用时间-起始'
     *    'uetime'    => '使用时间-结束'
     *    'dname'     => '领券用户'
     * ]
     *
     * @return string
     */
    public function summaryCoupon()
    {
        $couponId       = I('get.coupon_id', '', 'intval');
        $releaseTime    = I('get.rstime', '', 'strval');
        $releaseEndTime = I('get.retime', '', 'strval');
        $useTime        = I('get.ustime', '', 'strval');
        $useEndTime     = I('get.uetime', '', 'strval');
        $userName       = I('get.dname', '', 'strval');

        //$couponId = 173;

        if (!$couponId) {
            $this->apiReturn(201, [], '优惠券ID必传');
        }

        $this->_memberId = $this->isLogin();
        //$this->_memberId = 55;
        $where = [
            'coupon_id' => $couponId,
            'aid'       => $this->_memberId,
        ];

        if ($releaseTime) {
            $where['create_time'] = ['ELT', $releaseTime];
        }

        if ($releaseEndTime) {
            $where['create_time'] = ['EGT', $releaseEndTime];
        }

        if ($releaseEndTime && $releaseTime) {
            $where['create_time'] = ['BETWEEN', [$releaseTime, $releaseEndTime]];
        }

        if ($useTime) {
            $where['use_time'] = ['ELT', $useTime];
        }

        if ($useEndTime) {
            $where['use_time'] = ['EGT', $useEndTime];
        }

        if ($useEndTime && $useTime) {
            $where['use_time'] = ['BETWEEN', [$useTime, $useEndTime]];
        }

        $memberModel = new Member('slave');

        if ($userName) {
            $this->apiReturn(202, [], '无法通过用户名搜索');
            // $memberId = $memberModel->getMemberInfo($userName, 'dname', 'id');
            // if (!$memberId) {
            //     $this->apiReturn(202, [], '用户信息不存在');
            // }

            // $memberId = $memberId['id'];

            // $where['fid'] = $memberId;
        }

        $couponModel = new couponModel('remote_1');
        $field       = 'fid, aid, coupon_id ,dstatus, use_time, create_time, active_id';
        $data        = $couponModel->getCouponsByMixed($field, $where);

        if (empty($data)) {
            self::uploadReturn(201, [], '没有相关数据');
        }

        $couponValue = $couponModel->getCouponById($couponId, 'coupon_value');
        $couponValue = $couponValue['coupon_value'];

        $csvArr    = [];
        $activeArr = [];

        foreach ($data as $key => $value) {
            $activeId                                        = $value['active_id'];
            $csvArr[$value['fid']][$activeId]['useCnt']      = 0;
            $csvArr[$value['fid']][$activeId]['money']       = 0;
            $csvArr[$value['fid']][$activeId]['freeCnt']     = 0;
            $csvArr[$value['fid']][$activeId]['addCnt']      = 0;
            $csvArr[$value['fid']][$activeId]['create_time'] = 0;

            $csvArr[$value['fid']][$activeId]['card_no'] = 0;
            $csvArr[$value['fid']][$activeId]['usetime'] = 0;
            $csvArr[$value['fid']][$activeId]['title']   = 0;
            $csvArr[$value['fid']][$activeId]['dname']   = 0;
        }

        foreach ($data as $key => $value) {
            $activeId = $value['active_id'];
            //已使用
            if (in_array($value['dstatus'], [2])) {
                $csvArr[$value['fid']][$activeId]['useCnt']++;
                $csvArr[$value['fid']][$activeId]['money'] += $couponValue;
            }
            //结余可使用
            if (in_array($value['dstatus'], [0, 4])) {
                $csvArr[$value['fid']][$activeId]['freeCnt']++;
            }
            //新增
            $csvArr[$value['fid']][$activeId]['addCnt']++;

            if ($value['create_time'] > $csvArr[$value['fid']][$activeId]['create_time']) {
                $csvArr[$value['fid']][$activeId]['create_time'] = $value['create_time'];
            }

            if ($value['use_time'] > $csvArr[$value['fid']][$activeId]['use_time']) {
                $csvArr[$value['fid']][$activeId]['use_time'] = $value['use_time'];
            }

            $csvArr[$value['fid']][$activeId]['active_id'] = $value['active_id'];

            $activeArr[] = $value['active_id'];
        }

        $fidArr = array_keys($csvArr);

        $cardModel = new \Model\Product\MemberCard();
        $field     = 'card_no, memberID, usetime';
        $cardData  = $cardModel->getDataByMemberId($fidArr, $field, $map = ['apply_did' => $this->_memberId]);

        $cardArr = [];
        if ($cardData) {
            foreach ($cardData as $key => $val) {
                $cardArr[$val['memberID']] = $val;
            }
        }

        $memberData = $memberModel->getMemberInfoByMulti($fidArr, 'id', 'id, dname', true);

        $marketModel = new Marketing();
        $marketModel = $marketModel->getMarketingSpByPage(array_unique($activeArr), 'id, title');

        $marketName = [];
        if ($marketModel) {
            foreach ($marketModel as $value) {
                $marketName[$value['id']] = $value;
            }
        }

        foreach ($csvArr as $key => &$activeVal) {
            foreach ($activeVal as $k => &$value) {
                if (!empty($cardArr)) {
                    $value = array_merge($value, $cardArr[$key]);
                } else {
                    $value['card_no'] = '';
                }
                if (!empty($memberData)) {
                    $value = array_merge($value, $memberData[$key]);
                }
                if (!empty($marketName)) {
                    //内层数组比较小，可以这么干
                    foreach ($marketName as $val) {
                        if (isset($val['id']) && $value['active_id'] == $val['id']) {
                            $value['active_name'] = $val['title'];
                        }
                    }
                }

                $memberId = $key;
                $activeId = $k;
                $giveCouponWhere = [
                    'coupon_id' => $couponId,
                    'aid'       => $this->_memberId,
                    'to_fid'    => $memberId
                ];
                $memberCouponWhere = $where + [
                    'active_id' => $activeId,
                    'fid'       => $memberId,
                ];
                $useMemberCouponWhere = $memberCouponWhere + [
                    'dstatus' => 2
                ];

                $value += [
                    'give_member_coupon_count' => (int) $couponModel->getCountGiveCouponJoinMemberCoupon($giveCouponWhere, $memberCouponWhere),
                    'give_member_coupon_use_count' => (int) $couponModel->getCountGiveCouponJoinMemberCoupon($giveCouponWhere, $useMemberCouponWhere),
                ];
            }
        }

        $str = '会员名称, 实体卡号, 活动名称, 最近一次购买时间, 最近一次使用时间, 本期新增, 本期使用数, 本期使用金额, 本期结余可用数, 收到转赠数, 收到转赠使用数' . "\n";

        unset($value);
        unset($activeVal);

        $totalAdd   = 0;
        $totalUse   = 0;
        $totalmoney = 0;
        $totalLast  = 0;
        $totalGiveMemberCouponCount = 0;
        $totalGiveMemberCouponUseCount = 0;

        foreach (array_values($csvArr) as $key => $activeVal) {
            $cnt         = count($activeVal);
            $tempStr     = '';
            $tempAddCnt  = 0;
            $tempuseCnt  = 0;
            $tempMoney   = 0;
            $tempfreeCnt = 0;
            $tempUseTime = 0;
            $createTime  = 0;
            $giveMemberCouponCount = 0;
            $giveMemberCouponUseCount = 0;
            foreach ($activeVal as $k => $value) {
                $tempName = $value['dname'];
                $tempCard = $value['card_no'];

                $tempAddCnt  += $value['addCnt'];
                $tempuseCnt  += $value['useCnt'];
                $tempMoney   += $value['money'] / 100;
                $tempfreeCnt += $value['freeCnt'];

                $giveMemberCouponCount += $value['give_member_coupon_count'];
                $giveMemberCouponUseCount += $value['give_member_coupon_use_count'];

                $useTime = $value['use_time'] != '0000-00-00' ? $value['use_time'] : 0;

                if ($useTime > $tempUseTime) {
                    $tempUseTime = $useTime;
                }

                if ($value['create_time'] > $createTime) {
                    $createTime = $value['create_time'];
                }
                $tempStr .= $value['dname'] . "," . $value['card_no'] . "," . $value['active_name'] . "," . $value['create_time'] . "\t," .
                            $useTime . "\t," . $value['addCnt'] . "," . $value['useCnt'] . "," . $value['money'] / 100 . "," . $value['freeCnt'] . "," . $value['give_member_coupon_count'] . "," . $value['give_member_coupon_use_count'] . "\n";
            }

            $totalAdd   += $tempAddCnt;
            $totalUse   += $tempuseCnt;
            $totalmoney += $tempMoney;
            $totalLast  += $tempfreeCnt;
            $totalGiveMemberCouponCount += $giveMemberCouponCount;
            $totalGiveMemberCouponUseCount += $giveMemberCouponUseCount;

            $str .= $tempName . "," . $tempCard . ",合计:" . $cnt . "," . $createTime . "\t," . $tempUseTime
                    . "\t," . $tempAddCnt . "," . $tempuseCnt . "," . $tempMoney . "," . $tempfreeCnt . "," . $giveMemberCouponCount . "," . $giveMemberCouponUseCount . "\n";
            $str .= $tempStr;

        }
        $str      .= '合计,用户数:' . count($csvArr) . ', , , ,' . $totalAdd . ',' . $totalUse . ',' . $totalmoney . ',' . $totalLast . ',' . $totalGiveMemberCouponCount . ',' . $totalGiveMemberCouponUseCount;

        $fileName = date('Y-m-d H：i：s') . 'summary.csv';
        self::_exportCsv($fileName, $str);
    }

    public function exportGiveCoupon()
    {
        try {
            $couponId = I('get.coupon_id', '', 'intval');
            $aid = $this->isLogin();

            if (!$couponId) {
                throw new \Exception('优惠券ID必传');
            }
            if (!$aid) {
                throw new \Exception('未登录', 102);
            }

            $couponModel = new \Model\Order\Coupon();

            $couponDetail = $couponModel->getCouponById($couponId);
            if (!$couponDetail) {
                throw new \Exception('优惠券不存在');
            }
            if ($couponDetail['apply_did'] != $aid) {
                throw new \Exception('这不是你发布的优惠券');
            }

            $gitCouponCount = $couponModel->getCountGive($couponId, $aid);

            $size = 100;
            $tooBig = 10000;

            $outputList = [];

            if (0 < $gitCouponCount && $gitCouponCount <= $tooBig) {
                $apiMemberQuery = new \Business\JavaApi\Member\MemberQuery();

                $dstatusMap = [
                    0 => '正常',
                    1 => '已过期',
                    2 => '已使用',
                    3 => '人工销毁',
                    4 => '退票回收',
                ];

                $totalPage = ceil($gitCouponCount/$size);
                for ($page = 1; $page<=$totalPage; $page++) {
                    $list = $couponModel->getListGiveCouponByAidCid($aid, $couponId, $page, $size);
                    if ($list) {
                        $from_fids = array_values(array_column($list, 'from_fid'));
                        $to_fids = array_values(array_column($list, 'to_fid'));
                        $fids = array_values(array_unique(array_merge($from_fids, $to_fids)));
                        $memberListRes = $apiMemberQuery->queryMemberByMemberQueryInfo(['idList'=>$fids]);
                        if ($memberListRes['code'] != 200 || !$memberListRes['data']) {
                            throw new \Exception('用户数据异常');
                        }
                        $memberListMap = array_key(array_column($memberListRes['data'], 'memberInfo'), 'id');

                        $memberCouponIds = array_values(array_column($list, 'mc_id'));
                        $memberCouponRes = $couponModel->getCouponsByMixed('id,dstatus', ['id'=>['in', $memberCouponIds]]);
                        if (!$memberCouponRes) {
                            throw new \Exception('用户优惠券异常');
                        }
                        $memberCouponsMap = array_column($memberCouponRes, null, 'id');

                        foreach ($list as $item) {
                            $outputList[$item['id']] = [
                                'give_time_text' => date('Y-m-d H:i:s', $item['give_time']),
                                'from_member_account' => $memberListMap[$item['from_fid']]['account'],
                                'from_member_dname' => $memberListMap[$item['from_fid']]['dname'],
                                'to_member_account' => $memberListMap[$item['to_fid']]['account'],
                                'to_member_dname' => $memberListMap[$item['to_fid']]['dname'],
                                'to_member_mobile' => $item['tel'],
                                'member_coupon_id' => $item['mc_id'],
                                'member_coupon_status' => $dstatusMap[$memberCouponsMap[$item['mc_id']]['dstatus']],
                                'member_coupon_is_use' => (2 == $memberCouponsMap[$item['mc_id']]['dstatus']) ? '是' : '否',
                            ];
                        }
                    }
                }
            } elseif ($gitCouponCount > $tooBig) {
                throw new \Exception('转赠数量太大，不支持导出，请联系官方');
            }

            $headers = [
                '赠与时间', '赠与人账号', '赠与人名称', '被赠与人账号',
                '被赠与人名称', '被赠与人手机号码', '优惠券记录ID', '优惠券状态',
                '优惠券是否使用', '优惠券ID', '优惠券名称'
            ];
            $csvStrArr = [implode(',', $headers)];
            if ($outputList) {
                foreach ($outputList as $item) {
                    $csvStrArr[] = sprintf("%s\t,%s\t,%s\t,%s\t,%s\t,%s\t,%s\t,%s\t,%s\t,%s\t,%s\t",
                        $item['give_time_text'], $item['from_member_account'], $item['from_member_dname'], $item['to_member_account'],
                        $item['to_member_dname'], $item['to_member_mobile'], $item['member_coupon_id'], $item['member_coupon_status'],
                        $item['member_coupon_is_use'], $couponDetail['id'], $couponDetail['coupon_name']
                    );
                }
            }

            $csvStr = implode("\n", $csvStrArr);

            $csvFileName = date('Y-m-d_His_') . '优惠券转增明细.csv';
            self::_exportCsv($csvFileName, $csvStr);
        } catch (\Throwable $e) {
            $msg = $e->getMessage();
            $code = $e->getCode() ?: 201;
            exit(sprintf("错误码：%s <br> 错误原因：%s", $code, $msg));
//            $this->apiReturn($code, [], $msg);
        }
    }

    /**
     * 往浏览器输出文件
     *
     * @date   2017-06-15
     * <AUTHOR> Lan
     *
     * @param  string $fileName 文件名称
     * @param  string $data 数据内容
     *
     */
    private static function _exportCsv($fileName, $data)
    {
        header("Content-type:text/csv");
        header("Content-Disposition:attachment;filename=" . $fileName);
        header('Cache-Control:must-revalidate,post-check=0,pre-check=0');
        header('Expires:0');
        header('Pragma:public');
        echo chr(0xEF) . chr(0xBB) . chr(0xBF);
        echo $data;
        die();
    }

    /**
     * 预订产品整合数据
     *
     * @date   2018-08-09
     * <AUTHOR>
     *
     * @param  array $data 数据内容
     *
     */
    private function _reservationData($data)
    {
        if (empty($data)) {
            return [];
        }
        $landArr = [];
        foreach ($data as $key => $land) {
            $ticket        = [];
            $kid           = $land['lid'] . '_' . $land['aid'];
            $landArr[$kid] = array(
                'id'            => $land['lid'],
                'name'          => $land['title'],
                'salerid'       => $land['salerid'],
                'supplier_id'   => $land['aid'],
                'supplier_name' => $land['dname'],
                'terminal'      => $land['terminal'],
            );
            foreach ($land['ticket'] as $k => $value) {
                $ticket[] = array(
                    "id"          => $value['tid'],
                    "lid"         => $value['lid'],
                    "name"        => $value['ttitle'],
                    "product_id"  => $value['pid'],
                    "superior_id" => $value['aid'],
                );
            }
            $landArr[$kid]['tickets'] = $ticket;
        }

        return $landArr;
    }

    /**
     * 获取产品  -- 添加优惠券选择产品的时候使用
     * author  cyw
     * Date:2018-11-5
     *
     */
    public function getProduct()
    {
        $keyword = I('keyword');
        $seaType = I('type', true, 'boolval');
        $size    = I('size', 10);

        $loginInfoArr = $this->getLoginInfo('ajax');

        //获取产品列表
        $listingApi = new \Business\JavaApi\Ticket\Listing();
        $listRes    = $listingApi->selectListing($loginInfoArr['sid'], $keyword, ['A', 'B', 'C', 'H', 'F', 'L', 'G', 'J', 'I'], $size, $seaType, 1);
        if (empty($listRes) || $listRes['code'] != 200) {
            $this->apiReturn(200, [], '无数据');
        }

        $landIdArr = array_column($listRes['data'], 'id');
        //$landModel   = new \Model\Product\Land();
        //$landInfoArr = $landModel->getLandInfoByMuli($landIdArr, 'id,title,p_type', true);

        $javaAPi     = new \Business\CommodityCenter\Land();
        $landInfo    = $javaAPi->queryLandMultiQueryById($landIdArr);
        $landInfoArr = array_column($landInfo, null, 'id');

        $data = [];
        foreach ($listRes['data'] as $key => $value) {
            $data[$key]          = $value;
            $data[$key]['ptype'] = $landInfoArr[$value['id']]['p_type'];
        }

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * @date 2018-11-02
     * <AUTHOR>
     *
     * 生成优惠券 (修改优惠券)
     *
     * @param  int coupon_id 优惠券id
     * @param  string coupon_name 优惠券名字
     * @param  int coupon_value 优惠券面值:金额或折扣
     * @param  int coupon_num 优惠券数量
     * @param  int coupon_mode 优惠券种类  : 1:满减 2:立减 3:折扣
     * @param  int remark 优惠券明细
     * @param  int effective_type 有效时间类型:0= 领取日当天有效 ,1= 设定的时间段,2=领取日当天有效且自动发放,
     *                                      3=设定时间段有效且自动发放,4=领券多少天内有效，5领券多少天内有效，且自动发放
     * @param  int collect_days 获得优惠券多少天后有效
     * @param  string valid_start_time 有效起始时间
     * @param  string valid_end_time   有效结束时间
     * @param  int can_use_type 使用产品的  1:全店通用 2:指定类型 3:指定产品
     * @param  string can_use_product_type  可以使用的产品类型 景区类型：A景区,B线路,C酒店,F套票,G餐饮-限云顶,H演出,I年卡,J特产,K计时产品
     * @param  string can_use_product_ids 可以使用的产品id
     * @param  int arraive_money 满减的金额(满多少元可以同)
     * @param  int use_limit_type 限制使用张数：0=不限，1=一张(每单限用一张)，2=订票数(每张票限用一张), 3=每种票限用一张
     * @param  string use_limit_channel 限制使用渠道：1微信，2=二级店铺，3=自助机，4=会员卡
     * @param  int coupon_status  优惠券状态:0= 正常, 1=下架, 2=过期
     * @param  int limit_coupon_num_day 每天限制优惠券使用张数
     *
     *
     */
    public function createCoupon()
    {
        // 基础信息
        $couponId    = I('request.coupon_id', '', 'intval');
        $couponName  = I('request.coupon_name', '', 'strval');
        $couponValue = I('request.coupon_value', 0, 'intval');
        $couponNum   = I('request.coupon_num', 0, 'intval');
        $remark      = I('request.remark', '');

        // 使用规则
        $effectiveType      = I('request.effective_type', '');
        $collectDays        = I('request.collect_days', '');
        $validStime         = I('request.valid_start_time', '');
        $validEtime         = I('request.valid_end_time', '');
        $canUseType         = I('request.can_use_type', 1);
        $canUseProductType  = I('request.can_use_product_type', '');
        $canUseProductIds   = I('request.can_use_product_ids', '');
        $canUseCondition    = I('request.arraive_money', 0, 'intval');
        $useLimitType       = I('request.use_limit_type', '');
        $canuseLimitChannel = I('request.use_limit_channel', '');
        $couponStatus       = I('coupon_status', 0);
        $couponNumDay       = I('request.limit_coupon_num_day', 0);

        $loginInfoArr = $this->getLoginInfo('ajax');
        if (empty($couponName)) {
            $this->apiReturn(203, [], '优惠券名称不能为空');
        }
        if ($couponNum < 1) {
            $this->apiReturn(203, [], '优惠券数量设置有误');
        }
        if ($couponValue < 1) {
            $this->apiReturn(203, [], '优惠券值设置有误');
        }
        switch ($effectiveType) {
            case 0: // 领取日当天有效
                break;
            case 2: // 领取日当天有效且自动发放
                break;
            case 1: // 设定时间段
            case 3: // 设定时间段有效且自动发放
                if (empty($validStime) || $validEtime < $validStime) {
                    $this->apiReturn(203, [], '优惠券有效时间段设置有误');
                }
                //如果时间段显示已过期，则修改票券状态为已过期
                if (strtotime(date('Y-m-d', strtotime($validEtime))) < strtotime(date('Y-m-d', time()))) {
                    $couponStatus = 2;
                }
                break;
            case 4: // 领券多少天内有效
            case 5: // 领券多少天内有效,且自动发放
                if (empty($collectDays)) {
                    $this->apiReturn(203, [], '优惠券有效时间设置有误');
                }
                break;
            default:
                $this->apiReturn(203, [], '优惠券有效类型设置有误');
        }
        // 参数验证
        switch ($canUseType) {
            case 1: // 全店通用
                break;
            case 2: // 限制产品类型
                if (empty($canUseProductType)) {
                    $this->apiReturn(203, [], '请选择被限制的产品类型');
                }
                break;
            case 3: // 限制固定产品
                if (empty($canUseProductIds)) {
                    $this->apiReturn(203, [], '请选择特定的产品');
                }
                break;
        }
        if (!in_array($couponStatus, [0, 1, 2])) {
            $this->apiReturn(203, [], '优惠券状态有误');
        }
        if ($canUseCondition) {
            $couponMode = 1; // 满减
        } else {
            $couponMode = 2; // 立减
        }
        // 判断是否管理员发券  区分是否商家券还是平台券  --- 目前数据库中的0 可以默认是三亚的优惠券
        if ($this->isSuper()) {
            $couponType = 2; // 平台券
        } else {
            $couponType = 1; // 商家券
        }

        // 更新优惠券数据
        $data = [
            'coupon_name'    => $couponName,
            'coupon_mode'    => $couponMode,
            'coupon_value'   => $couponValue,
            'nums'           => $couponNum,
            'remarks'        => $remark,
            'effective_type' => $effectiveType,
            'collect_days'   => $collectDays,
            'use_startdate'  => $validStime,
            'use_enddate'    => $validEtime,
            'limit_ptype'    => $canUseProductType,
            'limit_use'      => $useLimitType,
            'pid'            => $canUseProductIds,
            'channel'        => $canuseLimitChannel,
            'condition'      => $canUseCondition,
            'status'         => $couponStatus,
            'apply_did'      => $loginInfoArr['sid'],
            'coupon_type'    => $couponType,
        ];

        $couponModel = new \Model\Product\Coupon('remote_1');
        if ($couponId) {
            $data['modify_time'] = date('Y-m-d H:i:s', time());
            $saveRes             = $couponModel->saveCoupon($couponId, $data);
            if (!$saveRes) {
                $this->apiReturn(205, [], '无更新');
            }
        } else {
            $data['create_time'] = date('Y-m-d H:i:s', time());
            $couponId            = $couponModel->createCoupon($data);
            $couponModel->createCouponExtend(['coupon_id' => $couponId]);
        }

        $this->apiReturn(200, ['coupon_id' => $couponId], 'success');
    }

    /**
     * @date 2018-11-02
     * <AUTHOR>
     *
     * 获取供应商优惠券列表
     *
     * @param  int page            页码
     * @param  int page_size       每页条数
     * @param  string coupon_name  优惠券名字
     * @param  int status          优惠券状态 0= 正常, 1=下架, 2=过期, 3=删除
     * @param  int show_type       请求页面类型 0:用户优惠券列表  1:用户获取添加券列表
     *
     * @return string | json
     *
     */
    public function applyDidCouponList()
    {
        $loginInfoArr = $this->getLoginInfo('ajax');
        $page         = I('request.page', 1);
        $pageSize     = I('request.page_size', 15);
        $couponName   = I('request.coupon_name', '');
        $status       = I('request.status', '');
        $showType     = I('request.show_type', 0);

        if (empty($loginInfoArr)) {
            $this->apiReturn(203, [], '未登陆');
        }

        // 删除的数据不展示在前台 , -1的时候也默认显示全部
        if ($status == 3) {
            $this->apiReturn(200, ['list' => [], 'total' => 0], '无数据');
        } elseif ($status == -1) {
            $status = [0, 1, 2];
        } elseif ($status == 1) {
            $status = array(1, 2);
        }

        if (is_numeric($couponName)) {
            $couponId   = $couponName;
            $couponName = '';
        }

        $couponModel = new \Model\Product\Coupon('remote_1');
        // 获取优惠券列表
        $field      = 'id, coupon_name, coupon_value, effective_type, limit_ptype, pid, nums, status, condition, use_startdate, use_enddate, collect_days';
        $couponList = $couponModel->couponListByApplyDid($loginInfoArr['sid'], $couponName, $couponId, $status, $page,
            $pageSize, $field, true);
        if (empty($couponList)) {
            $this->apiReturn(200, ['list' => [], 'total' => 0], '无数据');
        }

        // 获取优惠券扩展信息
        $couponIdArr         = array_column($couponList['list'], 'id');
        $extendInfoArr       = $couponModel->getCouponExtendInfos($couponIdArr, 'coupon_id, get_num');
        $couponExtendInfoArr = [];
        if (!empty($extendInfoArr)) {
            foreach ($extendInfoArr as $item) {
                $couponExtendInfoArr[$item['coupon_id']] = $item;
            }
        }

        $bindNumInfoArr = [];
        if ($showType == 1) {
            // 获取优惠券已经绑定的活动数量
            $marketModel = new \Model\Market\Market();
            $bindInfoArr = $marketModel->getMarketByCouponId($couponIdArr, 1);
            if (empty($bindInfoArr)) {
                foreach ($bindInfoArr as $item) {
                    if (!isset($bindNumInfoArr[$item['coupon_id']])) {
                        $bindNumInfoArr[$item['coupon_id']] = 0;
                    }
                    $bindNumInfoArr[$item['coupon_id']] += 1;
                }
            }
        }

        $data = ['total' => $couponList['total'], 'list' => []];
        foreach ($couponList['list'] as $key => $couponValue) {
            $data['list'][$key] = $couponValue;
            if ($couponValue['limit_ptype']) { // 限制产品类型
                $data['list'][$key]['can_use_type'] = 2;
            } elseif ($couponValue['pid']) { // 限制产品id
                $data['list'][$key]['can_use_type'] = 3;
            } else {
                $data['list'][$key]['can_use_type'] = 1;
            }
            if ($couponValue['status'] == 0) {
                $data['list'][$key]['status_desc'] = '正常';
            } elseif ($couponValue['status'] == 1) {
                $data['list'][$key]['status_desc'] = '下架';
            } elseif ($couponValue['status'] == 2) {
                $data['list'][$key]['status']      = 1;
                $data['list'][$key]['status_desc'] = '下架';
            }
            $data['list'][$key]['arraive_money']    = $couponValue['condition'];
            $data['list'][$key]['valid_start_time'] = $couponValue['use_startdate'];
            $data['list'][$key]['use_enddate']      = $couponValue['use_enddate'];
            $data['list'][$key]['get_num']          = isset($couponExtendInfoArr[$couponValue['id']]['get_num']) ? $couponExtendInfoArr[$couponValue['id']]['get_num'] : 0;
            $data['list'][$key]['binded_num']       = isset($bindNumInfoArr[$couponValue['id']]) ? $bindNumInfoArr[$couponValue['id']] : 0;
        }

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * @date 2018-11-02
     * <AUTHOR>
     *
     * 获取优惠券基本信息
     *
     * @param  string coupon_id 优惠券id
     *
     * @return string | json
     *
     */
    public function couponInfo()
    {
        $couponId     = I('request.coupon_id', '1021');
        $loginInfoArr = $this->getLoginInfo('ajax');
        if (empty($couponId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $couponBiz     = new \Business\Market\CouponList();
        $couponInfoArr = $couponBiz->couponInfo($couponId, $loginInfoArr['sid']);
        if ($couponInfoArr['code'] != 200) {
            $this->apiReturn($couponInfoArr['code'], [], $couponInfoArr['msg']);
        }
        $data = $couponInfoArr['data'];
        $this->apiReturn(200, $data, 'success');
    }

    /**
     * @date 2018-11-02
     * <AUTHOR>
     *
     * 修改优惠券的状态
     *
     * @param  string coupon_id 优惠券id
     * @param  int status 0= 正常, 1=下架, 2=过期, 3=删除
     *
     * @return string | json
     *
     */
    public function changeCouponStatus()
    {
        $loginInfoArr = $this->getLoginInfo('ajax');
        $couponId     = I('request.coupon_id', '');
        $status       = I('request.status');

        // 验证数据
        if (empty($couponId) || !in_array($status, [0, 1, 2, 3])) {
            $this->apiReturn(203, [], '参数错误');
        }
        $couponModel = new \Model\Product\Coupon('remote_1');
        // 获取优惠券的数据
        $couponInfoArr = $couponModel->getCouponById($couponId);
        if (empty($couponInfoArr)) {
            $this->apiReturn(203, [], '优惠券数据有误');
        }
        if ($couponInfoArr['apply_did'] != $loginInfoArr['sid']) {
            $this->apiReturn(203, [], '无权限操作该优惠券');
        }

        // 更新优惠券状态数据
        $upRes = $couponModel->setCouponStatus($couponId, $status, $loginInfoArr['sid']);
        if (!$upRes) {
            $this->apiReturn(205, [], '修改状态失败');
        }
        // 下架优惠券的时候要把 活动关联券的一起解绑
        if ($status == 1) {
            // 自动解绑
            $marketModel = new \Model\Market\Market('remote_1');
            $marketModel->setMarketCouponStatusByCouponIds([$couponId], 0);
        }
        $this->apiReturn(200, [], 'success');
    }

    /**
     * @date 2018-11-02
     * <AUTHOR>
     *
     * 获取优惠券适用的产品信息
     *
     * @param  string coupon_id 优惠券id
     *
     * @return string | json
     *
     */
    public function couponCanUseProduct()
    {
        $loginInfoArr = $this->getLoginInfo('ajax');
        $couponId     = I('request.coupon_id', '');
        if (empty($loginInfoArr) || empty($couponId)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $couponModel   = new \Model\Product\Coupon();
        $couponInfoArr = $couponModel->getCouponById($couponId);
        if (empty($couponInfoArr)) {
            $this->apiReturn(205, [], '无数据');
        }
        if (!$couponInfoArr['pid']) {
            $this->apiReturn(205, [], '无数据');
        }

        $pidArr = explode(',', $couponInfoArr['pid']);

        $ticketModel   = new \Model\Product\Ticket();
        $ticketInfoArr = $ticketModel->getTicketsByPid($pidArr, 'id, pid, landid, title');
        if (empty($ticketInfoArr)) {
            $this->apiReturn(205, [], '无该产品数据');
        }

        $lidArr      = array_column($ticketInfoArr, 'landid');
        //$landModel   = new \Model\Product\Land();
        //$landInfoArr = $landModel->getLandInfoByMuli($lidArr, 'id, title', true);

        $javaAPi     = new \Business\CommodityCenter\Land();
        $landInfo    = $javaAPi->queryLandMultiQueryById($lidArr);
        $landInfoArr = array_column($landInfo, 'title', 'id');

        $data = [];
        foreach ($ticketInfoArr as $ticketVal) {
            $data[] = $landInfoArr[$ticketVal['landid']] . '-' . $ticketVal['title'];
        }
        // 获取优惠券可以使用的产品信息
        $this->apiReturn(200, $data, 'success');
    }

    /**
     * @date 2018-11
     * <AUTHOR>
     *
     * 删除优惠券
     *
     * @param  int coupon_id 优惠券id
     *
     * @return string | json
     *
     */
    public function delCoupon()
    {
        $loginInfoArr = $this->getLoginInfo('ajax');
        $couponId     = I('request.coupon_id', '');
        if (empty($loginInfoArr) || empty($couponId)) {
            $this->apiReturn(203, [], '参数有误');
        }

        $couponModel   = new \Model\Product\Coupon('remote_1');
        $couponInfoArr = $couponModel->getCouponById($couponId);
        if (empty($couponInfoArr)) {
            $this->apiReturn(205, [], '无数据');
        }
        if ($couponInfoArr['apply_did'] != $loginInfoArr['sid']) {
            $this->apiReturn(203, [], '无权限操作该优惠券');
        }

        $marketModel = new \Model\Market\Market('remote_1');
        // 验证优惠券是否还有关联的活动正在进行中
        $marketCouponInfoArr = $marketModel->getMarketByCouponId([$couponId], 1, 'id, coupon_id, activity_id');

        if (!empty($marketCouponInfoArr)) {
            $activityIdArr      = array_column($marketCouponInfoArr, 'activity_id');
            $isExistActivitying = $marketModel->getMarketByIds($activityIdArr, 3, 1, 1, $field = 'id');
            if (!empty($isExistActivitying)) {
                $this->apiReturn(205, [], '正在进行中的活动使用了该优惠券,删除失败');
            }
        }

        // 更新优惠券状态数据
        $upRes = $couponModel->setCouponStatus($couponId, 3, $loginInfoArr['sid']);
        if (!$upRes) {
            $this->apiReturn(205, [], '删除优惠券失败');
        }

        // 移除所有关联了该优惠券的活动
        $marketModel->setMarketCouponStatusByCouponIds([$couponId], '0');
        $this->apiReturn(200, [], 'success');
    }

}
