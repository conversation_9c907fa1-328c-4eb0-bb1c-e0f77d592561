<?php

namespace Controller\product;

use Business\Product\ExcelCrossBusiness;
use Library\Controller;
use Process\Product\ExcelCrossProcess;

/**
 * @Author: CYQ19931115
 * @Date:   2017-10-11 13:38:53
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2017-10-23 16:01:21
 */

/**
 * excel比对工具接口
 */
class ExcelCrossTool extends Controller
{

    /** @var Business\Product\ExcelCrossBusiness  */
    private $business;

    public function __construct()
    {
        $this->business = new ExcelCrossBusiness();
    }

    /**
     * 获取三亚两表比对的导出结果
     * <AUTHOR>
     * @DateTime 2017-10-11T15:03:23+0800
     * @return
     */
    public function sanyaDoubleTableCross()
    {
        try {
            $file_upload_path = I("export_name", "");
            $files            = ExcelCrossProcess::getTempFile($_FILES);
            $main             = $files['main'];
            $minor            = $files['minor'];
            $file_name        = $files['upload_name'];
            $excel            = $this->business->cross($main, $minor);
            if (!empty($file_upload_path)) {//没有上传的路径的时候
                $filename = $file_upload_path;
            } else {
                $file_info = pathinfo($file_name);
                $filename = $file_info["filename"];
            }
            $filename=$filename. "-匹配结果";
            $excel->generateXML($filename);
        } catch (\Exception $e) {
            parent::apiReturn(500, [], $e->getMessage());
        }
    }
}
