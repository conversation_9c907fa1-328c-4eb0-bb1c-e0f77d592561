<?php

namespace Controller\Product;

use Business\AnnualCard\AnnualCardManage;
use Business\Member\MemberRelation;
use Business\Product\AnnualCardConst;
use Business\Product\AnnualCardTagBiz;
use Business\Product\AnnualCardVerifyBiz;
use Controller\AnnualCard\AnnualArchiveTrait;
use Library\Cache\Cache;
use Library\Constants\AllInOneCardConst;
use Library\Controller;
use Model\Product\AnnualCard as CardModel;
use Model\Product\Ticket;
use Model\Product\Land;
use Model\Member\Member;
use Model\Order\OrderTools;
use Library\Tools\Vcode;
use Business\Product\AnnualCard as AnnualBiz;
use Business\JavaApi\TicketApi;
use Business\JavaApi\Ticket\Price;
use Business\Member\MemberMoney;
use Model\Product\BaseCard;
use Library\SimpleExcel;

class AnnualCard extends Controller
{
    use AnnualArchiveTrait;

    private $_CardModel    = null;
    private $_TicketModel  = null;
    private $_sid          = null;
    private $_memberId     = null;
    private $_privilegeBiz = null;

    public function __construct()
    {
        $this->isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);

        $this->_sid          = $loginInfo['sid'];
        $this->_memberId     = $loginInfo['memberID'];
        $this->_CardModel    = new CardModel();
        $this->_TicketModel  = new Ticket();
        $this->_privilegeBiz = new \Business\Product\AnnualCardPrivilege();
    }

    /**
     * 获取所属供应商的年卡产品列表
     * @return [type] [description]
     */
    public function getAnnualCardProducts()
    {

        $page = I('page', 1, 'intval');
        $size = I('page_size', 10, 'intval');

        if (!$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }

        $commodityProductBiz = new \Business\CommodityCenter\Product();
        $result              = $commodityProductBiz->getAnnualProductList($this->_sid, $page, $size);

        $list  = $result['list'];
        $total = $result['total'];

        $return = [
            'list'       => $list,
            'page'       => $page,
            'page_size'  => $size,
            'total_page' => ceil($total / $size),
            'total'      => $total,
        ];

        $this->apiReturn('200', $return);
    }

    /**
     * 获取指定产品的关联年卡
     * @return [type] [description]
     */
    public function getAnnualCards()
    {
        $pid = I('pid', '', 'intval');
        $sid = $this->_sid;

        if (intval($pid) < 1) {
            $this->apiReturn(400, [], '参数错误');
        }

        $options = [
            'status'    => 3,
            'page'      => I('page', '1', 'intval'),
            'page_size' => I('page_size', '10', 'intval'),

        ];

        $list = $this->_CardModel->getAnnualCards($sid, $pid, $options);

        $total = $total_page = 0;
        if ($list) {
            $total      = $this->_CardModel->getAnnualCards($sid, $pid, $options, 'count');
            $total_page = ceil($total / $options['page_size']);
        }

        $virtual_stg = $this->_CardModel->getAnnualCardStorage($sid, $pid);
        $physics_stg = $this->_CardModel->getAnnualCardStorage($sid, $pid, 'physics');

        $return = [
            'list'       => $list ?: [],
            'page'       => $options['page'],
            'page_size'  => $options['page_size'],
            'total_page' => $total_page,
            'total'      => $total,
            'virtual'    => $virtual_stg,
            'physics'    => $physics_stg,
        ];

        $this->apiReturn('200', $return);
    }

    /**
     * 创建\录入年卡,此时都还处于未绑定物理卡状态
     * @return [type] [description]
     */
    public function createAnnualCard()
    {
        //关联的年卡产品
        $pid  = I('pid', '', 'intval');
        $list = I('list');
        //是否同时激活(一键激活)
        $active = I('active', 0, 'intval');

        if (intval($pid) < 1 || !is_array($list)) {
            $this->apiReturn(400, [], '参数错误');
        }
        if (count($list) == 0) {
            $this->apiReturn(200, [], '录入成功');
        }
        if (!$this->_isProductAvalid($pid, $this->_sid)) {
            $this->apiReturn(400, [], '请选择正确的产品');
        }
        if (count($list) > 1000) {
            $this->apiReturn(204, [], '一次性最多录入1000张年卡');
        }

        if ($active) {
            $physics_num = count(array_filter(array_column($list, 'physics_no')));
            $virtual_num = count(array_filter(array_column($list, 'virtual_no')));
            if ($physics_num != $virtual_num) {
                $this->apiReturn(204, [], '虚拟卡无法一键激活');
            }
        }

        $annualBiz = new \Business\AnnualCard\AnnualBizService();
        $result    = $annualBiz->createAnnualCard($this->_sid, $pid, $list, $active);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 选择的年卡产品是否合法
     *
     * @param  [type]  $pid [description]
     * @param  [type]  $sid [description]
     *
     * @return boolean      [description]
     */
    private function _isProductAvalid($pid, $sid): bool
    {
        /*$options['where'] = [
            'id'        => $pid,
            'apply_did' => $sid,
            'p_status'  => 0,
            // 'p_type'    => 'I'
        ];*/

        $Ticket              = new Ticket();
        $find                = [];
        $commodityProductBiz = new \Business\CommodityCenter\Product();
        $res                 = $commodityProductBiz->getProductInfoById($pid);
        if ($res) {
            if ($res['apply_did'] == $sid && $res['p_status'] == 0) {
                $find = $res;
            }
        }

        $type = $Ticket->getProductType($pid);

        return $find && $type == 'I';
    }

    /**
     * 删除年卡
     * @return [type] [description]
     */
    public function deleteAnnualCard()
    {

        //虚拟号
        $virtualNo = I('virtual_no', '');
        if (!$virtualNo) {
            $this->apiReturn(204, [], '参数错误');
        }

        $annualBiz = new \Business\AnnualCard\AnnualBizService();
        $result    = $annualBiz->delAnnualCardByVirtualNo($virtualNo, $this->_sid);

        if (isset($result)) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }

    }

    /**
     * 绑定物理卡（完善年卡信息）
     * @return [type] [description]
     */
    public function bindAnnualCard()
    {
        $card_no    = I('card_no');
        $physics_no = I('physics_no');
        $virtual_no = I('virtual_no');

        if (!$card_no || !$physics_no || !$virtual_no) {
            $this->apiReturn(400, [], '参数错误');
        }

        $result = $this->_CardModel->bindAnnualCard($this->_sid, $virtual_no, $card_no, $physics_no);

        if ($result) {
            $this->apiReturn(200, [], '绑定成功');
        } else {
            $this->apiReturn(204, [], '绑定失败');
        }
    }

    /**
     * pc端激活前的检测
     * @return [type] [description]
     */
    public function activeCheck()
    {

        $identify = I('identify');
        $type     = I('type');

        if (!$identify || !$type) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (!in_array($type, ['physics', 'virtual'])) {
            $this->apiReturn(204, [], "类型错误:{$type}");
        }

        if ($type == 'physics') {
            $physicsNo = is_numeric($identify) ? dechex($identify) : $identify;
            //处理高位为0的情况
           /* $whereIn = [$physicsNo, substr($physicsNo, 1)];
            if ($physicsNo{1} == 0) {
                $whereIn[] = substr($physicsNo, 2);
            }*/
            $option['where'] = [
                'sid'        => $this->_sid,
                //'physics_no' => ['in', $whereIn],
                'physics_no' => strval($physicsNo),
            ];
        } else {
            $option['where'] = [
                'sid'        => $this->_sid,
                'virtual_no' => $identify,
            ];
        }

        $card = $this->_CardModel->getAnnualCard('', '', $option, 'find');

        if (!$card) {
            $this->apiReturn(204, [], '未找到年卡');
        }

        $annualBase = new \Business\Product\AnnualCardConst();
        $checkRes = $annualBase->annualStatusHandle($card);
        if (!in_array($checkRes['annual_stratus'], [AnnualCardConst::STATUS_NOT_ACTIVE, AnnualCardConst::STATUS_AUDIT])) {
            $this->apiReturn(204, [], "年卡{$checkRes['status_name']}");
        }

        $javaApi    = new \Business\CommodityCenter\Ticket();
        $ticket     = $javaApi->queryTicketInfoByProductIds([$card['pid']]);
        $ticketInfo = $ticket[0]['ticket'];
		$annualOrderType = $ticket[0]['ext']['annual_order_type'] ?? AnnualCardConst::ANNUAL_ORDER_TYPE_NEED_ORDERNAME;

        //获取实名制及人脸录入信息
        $checkRes = (new \Business\Product\AnnualCardVerifyBiz())->checkAnnualRealNameAndPhoto($ticketInfo['id'], 0, 2);
        if ($checkRes['code'] != 200) {
            $this->apiReturn($checkRes['code'], $checkRes['data'], $checkRes['msg']);
        }
        $checkMap      = $checkRes['data'];
        $annualExtInfo = json_decode($card['ext_info'], true);

        $data = [
            'need_ID'              => $checkMap['annual_identity_info'],
            'virtual_no'           => $card['virtual_no'],
            'physics_no'           => $card['physics_no'],
            'card_no'              => $card['card_no'],
            'is_need_wirte'        => $checkMap['is_need_wirte'],
            'annual_identity_info' => $checkMap['annual_identity_info'],
            'book_active'          => $checkMap['book_active'],
            'book_active_channels' => $checkMap['book_active_channels'],
            'family_card_num'      => $checkMap['family_card_num'],
            'voucher_type'         => empty($annualExtInfo['voucher_type']) ? 1 : intval($annualExtInfo['voucher_type']),
        ];

        $dname     = '';
        $mobile    = '';
        $idcard    = '';
        $photoInfo = [];
		if($annualOrderType == AnnualCardConst::ANNUAL_ORDER_TYPE_NEED_CARD_HANDLER_NAME) {
            $dname     = $card['dname'];
            $mobile    = $card['mobile'];
            $idcard    = $card['id_card_no'];
            $photoInfo = $this->_CardModel->getGroupPhotos($card['virtual_no'], 'photos');
            $photoInfo = empty($photoInfo['photos']) ? [] : json_decode($photoInfo['photos'], true);
		}
        $data['dname']      = $dname;
        $data['mobile']     = $mobile;
        $data['id_card_no'] = $idcard;
        $data['avatar']     = $photoInfo;
        //获取附属持卡人信息
        $annualCardManageBiz       = new AnnualCardManage();
        $affiliatesPerson          = $annualCardManageBiz->getAffiliatesPerson($card['id']);
        $data['affiliates_person'] = $affiliatesPerson['data'];

        $this->apiReturn(200, $data);
    }

    /**
     * PC端供应商手动激活年卡
     */
    public function activateForPc()
    {

        //激活参数
        $paramsObject = (object)I('');
        //增加激活操作人 用于激活验证时候触发解冻，需要的操作人参数
        $paramsObject->opIdForThaw = $this->_memberId;
        //pc端渠道
        $paramsObject->source = 0;
        $annualBiz = new AnnualBiz();
        $result    = $annualBiz->activePhysicsCard($this->_sid, $paramsObject);

        if (isset($result['code'])) {
            //旧接口做下兼容
            if ($result['code'] == 209) {
                $result['code'] = 200;
            }
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }

    }

    /**
     * 年卡会员头像保存（终端）
     *
     * @param  [type] $base64_img [description]
     * @param  [type] $memberid   [description]
     *
     * @return [type]             [description]
     */
    private function _saveImgForBase64($base64_img, $mobile)
    {

        $file = "shops/{$mobile}__touxiang.jpg";

        $imgPath = IMAGE_UPLOAD_DIR . $file;

        file_put_contents($imgPath, base64_decode($base64_img));

        return IMAGE_URL . $file;

    }

    /**
     * 获取指定年卡的剩余的特权支付次数
     *
     * @param  [type] $memberid [description]
     * @param  [type] $sid      [description]
     * @param  [type] $pid      [description]
     *
     * @return [type]           [description]
     */
    public function getPrivilegessLeft($memberid, $sid, $pid, $virtual_no)
    {
        $annualBiz = new \Business\Product\AnnualCard();

        return $annualBiz->getPrivilegessLeft($memberid, $sid, $pid, $virtual_no);

    }

    /**
     * 根据手机号查询会员信息是否已经存在
     *
     * @param  [type]  $mobile [description]
     *
     * @return boolean         [description]
     */
    public function isMemberExists()
    {

        if (!I('mobile') || strlen(I('mobile')) != 11) {
            $this->apiReturn(400, '请填写正确的手机号');
        }

        $memberModel = new Member('slave');
        //TODO:需要返回的字段筛选
        $member = $memberModel->getMemberInfo(I('mobile'), 'mobile');

        $this->apiReturn(200, $member);

    }

    /**
     * 获取年卡会员列表
     * @return [type] [description]
     */
    public function getMemberList()
    {
        $from = I('if_fid', 0, 'intval');
        if ($from && !I('identify', '')) {
            $this->apiReturn(201, [], '请输入搜索参数');
        }
        $options = [
            'page_size'      => I('page_size', 10, 'intval'),    //每页数量
            'page'           => I('page', 1, 'intval'),          //页数
            'status'         => I('status', 5, 'intval'),        //年卡状态 0未激活 1正常 2禁用 3仓库中 4挂失 5除了仓库中的所有 6已过期
            'identify'       => I('identify', ''),               //搜索参数
            'sale_start'     => I('sale_start', '', 'strval'),   //售出开始时间
            'sale_end'       => I('sale_end', '', 'strval'),     //售出结束时间
            'active_start'   => I('active_start', '', 'strval'), //激活开始时间
            'active_end'     => I('active_end', '', 'strval'),   //激活结束时间
            'distributor_id' => I('distributor_id', 0, 'intval'),//分销商id
            'lid'            => I('lid', 0, 'intval'),           //景区id
            'tid'            => I('tid', 0, 'intval'),           //年卡产品门票id
            'search_type'    => I('search_type', -1, 'intval'),   //搜索类型 0物理卡 1 实体卡 2虚拟卡 3手机号 4身份证 5姓名
            'annual_status'  => I('annual_status', -1, 'intval'),   //年卡叠加状态搜索 -1所有 0未激活 1已激活 2禁用 4挂失 16 已使用 32 已过期
        ];
        if ($from) {
            $sid = '';
        } else {
            $sid = $this->_sid;
        }

        if (!empty($options['lid'])) {
            //通过传入景区id 获取到pid
            $javaApi = new \Business\CommodityCenter\LandF();
            $pidArr  = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([], [$options['lid']], [], 'pid', true);

            $options['pid'] = $pidArr;
        }

        if (!empty($options['tid'])) {
            //通过传入门票id 获取到pid
            $ticketModel    = new \Model\Product\Ticket();
            $pid            = $ticketModel->getTicketInfoById($options['tid'], 'pid');
            $options['pid'] = [$pid['pid']];
        }

        $result = $this->_CardModel->getMemberList($sid, $options, 'select');

        $total = $total_page = 0;

        if ($result) {
            $total      = $this->_CardModel->getMemberList($sid, $options, 'count');
            $total_page = ceil($total / $options['page_size']);
        } else {
            $return = [
                'list'       => [],
                'page'       => $options['page'],
                'page_size'  => $options['page_size'],
                'total_page' => $total_page,
                'total'      => $total,
            ];

            $this->apiReturn(200, $return ?: []);
        }

        $getMemberid = [];
        $getPid      = [];

        //当前页虚拟卡号
        $virtualNoArr = array_column($result, 'virtual_no');
        foreach ($result as $item) {
            if ($item['memberid']) {
                $getMemberid[] = $item['memberid'];
            }
            $getMemberid[] = $item['sid'];
            $getMemberid[] = $item['distributor_id'];
            $getPid[]      = $item['pid'];
        }

        $annualBase = new \Business\Product\AnnualCardConst();
        //虚拟卡号不为空时  获取虚拟卡号对应的人脸数量
        $virtualUseCountMap = [];
        if ($virtualNoArr) {
            $virtualUseCountMap = $this->_CardModel->batchGetVirtualOrderCount($virtualNoArr);
        }

        $members = $result ? $this->_getMemberInfoByMulti($getMemberid) : [];
        if ($members) {
            $members = $this->_replaceKey($members, 'id');
        }

        $commodityProductBiz = new \Business\CommodityCenter\Ticket();
        $ticketArr          = $commodityProductBiz->queryTicketInfoByProductIds($getPid);
        if (!$ticketArr) {
            $this->apiReturn(204, [], '年卡产品获取异常');
        }

        //获取门票产品信息
        $nameMap    = [];
        foreach ($ticketArr as $ticketInfo) {
            $nameMap[$ticketInfo['product']['id']] = [
                'id'      => $ticketInfo['product']['id'],
                'ttitle'  => $ticketInfo['ticket']['title'],
                'tid'     => $ticketInfo['ticket']['id'],
                'pid'     => $ticketInfo['ticket']['pid'],
                'title'   => $ticketInfo['land']['title'],
                'lid'     => $ticketInfo['land']['id'],
            ];
        }

        foreach ($result as $key => $item) {
            if (isset($members[$item['memberid']])) {
                $result[$key]['account'] = $item['dname'] ?: $members[$item['memberid']]['dname'];
                $result[$key]['mobile']  = $item['mobile'] ?: $members[$item['memberid']]['mobile'];
            }
            if (isset($members[$item['sid']])) {
                $result[$key]['supply'] = $members[$item['sid']]['dname'];
            }
            $result[$key]['sale_time']   = $item['sale_time'] ? date('Y-m-d H:i:s', $item['sale_time']) : '';
            $result[$key]['active_time'] = $item['active_time'] ? date('Y-m-d H:i:s', $item['active_time']) : '';

            $result[$key]['avalid_time'] = $item['avalid_begin'] && $item['avalid_end'] ? date('Y-m-d', $item['avalid_begin']) . ' ~ ' . date('Y-m-d', $item['avalid_end']): '';
            if ($item['sid'] == $this->_sid) {
                $result[$key]['self'] = 1;
            } else {
                $result[$key]['self'] = 0;
            }

            $result[$key]['title']  = $nameMap[$item['pid']]['title'];
            $result[$key]['ttitle'] = $nameMap[$item['pid']]['ttitle'];
            //if (!empty($item['distributor_id']) && $item['distributor_id'] != $this->_sid && $item['distributor_id'] != 112) {
            //    $result[$key]['s_name'] = $members[$item['distributor_id']]['dname'];
            //} else {
                $result[$key]['s_name'] = '';
            //}

            //获取一次年卡状态
            $annualHand                  = $annualBase->annualStatusHandle($item);
            $result[$key]['status_name'] = $annualHand['status_name'];
            $result[$key]['button']      = $annualHand['button'];
            $annualStratus               = $annualHand['annual_stratus'];
            //增加过期状态判断
            //禁用和挂失状态优先级更高， 这两种状态无需处理过期状态
            if (!in_array($annualStratus, [AnnualCardConst::ANNUAL_STATUS_BAN, AnnualCardConst::ANNUAL_STATUS_LOSS]) && $item['avalid_begin'] && $item['avalid_end'] && $item['avalid_end'] < time()) {
                $result[$key]['status']      = 5;
                $result[$key]['status_name'] = '已过期';
            }

            $result[$key]['contact_id'] = $nameMap[$item['pid']]['lid'];
            $result[$key]['physics_no'] = $item['physics_no'] ? hexdec($item['physics_no']) : 0;
            $result[$key]['use_cnt']    = $virtualUseCountMap[$item['virtual_no']] ?? 0;
        }

        $return = [
            'list'       => $result,
            'page'       => $options['page'],
            'page_size'  => $options['page_size'],
            'total_page' => $total_page,
            'total'      => $total,
        ];

        $this->apiReturn(200, $return ?: []);
    }

    /**
     * 年卡操作    16/10/8
     * @param  [type] $type [0 挂失 1 禁用 2 补卡 3 恢复]
     */
    public function operationAnnual()
    {
        //这里的memberid可能会在其他页面修改掉（年卡转让）
        $options = [
            //'memberid'   => I('memberid'),
            'id'         => I('id'),
            'sid'        => I('sid'),
            'status'     => I('status'),
            'type'       => I('type'),
            'mobile'     => I('mobile'),
            'vcode'      => I('vcode'),
            'card_no'    => I('card_no'),
            'physics_no' => I('physics_no'),
            'confirm'    => I('confirm', 0, 'intval'),
            'opt_member' => $this->_memberId,
            'opt_sid' => $this->_sid,
        ];
        $avalidEnd = I('avalid_end', '', 'strval');
        $table = $this->getArchiveAnnualCardTable($avalidEnd);
        $annualModel = new CardModel(0, 0, $table);
        if ($options['type'] == 1) {
            $result = $annualModel->operationAnnual($this->_sid, $options);
            $msg = false === $result ? '操作失败' : '操作成功';
            $code = false === $result ? 203 : 200;
            $this->apiReturn($code, [], $msg);
        } else if ($options['type'] == 2) {

            if (empty($options['card_no'])) {
                $this->apiReturn(203, [], '实体卡号不能为空');
            }
            //校验供应商下实体卡号唯一性
            $annualBiz = new AnnualBiz();
            $checkRes  = $annualBiz->checkAnnualCardNo($this->_sid, $options['card_no']);
            if ($checkRes['code'] != 200) {
                $this->apiReturn($checkRes['code'], $checkRes['data'], $checkRes['msg']);
            }

            $checkcard = $this->_CardModel->_checkCard($this->_sid, $options);
            if ($checkcard == 309) {  //激活原来的卡
                $this->_CardModel->jihuoCard($this->_sid, $options);
                $this->apiReturn(200, [], '操作成功');
            } else if ($checkcard == 308) {//此卡已无法使用
                $this->apiReturn(300, [], '此卡已无法使用');
            } else if ($checkcard == 200) {//可以使用
                $result    = $this->_CardModel->operationAnnual($this->_sid, $options);
                $annualBiz = new AnnualBiz();
                $annualBiz->addICRecord([$options['physics_no']]);
                $msg = false === $result ? '操作失败' : '操作成功';
                $code = false === $result ? 203 : 200;
                $this->apiReturn($code, [], $msg);
            }
        } else if ($options['type'] == 0) {
            $this->_checkcardVcode($options['mobile'], $options['vcode']);
            $result = $this->_CardModel->operationAnnual($this->_sid, $options);
            $msg = false === $result ? '操作失败' : '操作成功';
            $code = false === $result ? 203 : 200;
            $this->apiReturn($code, [], $msg);
        } else if ($options['type'] == 3) {
            if ($options['confirm'] == 0) {
                $result = $annualModel->operationAnnual($this->_sid, $options);
                if ($result['code'] == 501) {
                    $this->apiReturn(501, [], $result['card_no']);
                } else {
                    $this->apiReturn(200, [], '操作成功');
                }
            } else {
                $result = $annualModel->operationAnnual($this->_sid, $options);
                if ($result['code'] == 501) {
                    $yid    = $result['id'];
                    //下面这个方法应该进不去? 无效方法
                    $result = $annualModel->operationConfirmannual($this->_sid, $options, $yid);
                    $this->apiReturn(200, [], '操作成功');

                }
            }
        } else {
            $this->apiReturn(204, [], '参数错误');
        }
    }

    /**
     * 发送补卡验证码
     *
     * @param  int  $mobile  手机号码
     */
    public function sendcardVcode()
    {
        $mobile = I('mobile');
        if (!ismobile($mobile)) {
            $this->apiReturn(205, [], '请输入正确的手机号');
        }
        $virtualNo = I('virtual_no', '');
        //其他页面可能会改掉手机号 这里从年卡信息里获取下最新的手机号
        if (!empty($virtualNo)) {
            $annualModel = new \Model\Product\AnnualCard();
            $card = $annualModel->getAnnualCard($virtualNo, 'virtual_no');
            $mobile = empty($card['mobile']) ? $mobile : $card['mobile'];
        }
        $data = [
            '{1}'  => '年卡挂失',
            'code' => '{2}',
        ];

        $sendRes = Vcode::sendVcode($mobile, 'annual_loss', 'annual_loss', $data, 6, false, 300, 60);
        if ($sendRes['code'] == 200) {
            $this->apiReturn(200, [], '验证码发送成功');
        } else {
            $this->apiReturn(207, [], '验证码发送失败');
        }
    }

    /**
     * 导出年卡信息    16/9/23
     *
     * @param  [type] $result       [description]
     *
     * @return [type]               [description]
     */
    public function getUploadMember()
    {

        $status   = I('status', 1, 'intval');
        $identify = I('identify', '');

        $options = ['status' => $status, 'identify' => $identify];

        //获取年卡列表
        $list = $this->_CardModel->getMemberList($this->_sid, $options, 'select');
        $list = $list ?: [];

        $pNameArr = [];
        if ($list) {

            $memArr   = array_filter(array_column($list, 'memberid'));
            $pidArr   = array_column($list, 'pid');
            $pNameArr = $this->_TicketModel->getCardName($pidArr);
            //获取会员名称映射
            $memModel  = new Member('slave');
            $memberBiz = new \Business\Member\Member();
            $nameMap   = $memModel->getMemberInfoByMulti($memArr, 'id', 'id,dname,mobile', 1);
            //获取身份证和所在区域
            $field  = 'fid,province,city';
            $extMap = $memberBiz->getMemberExtListGetFieldToJava($memArr, $field);
            //用户二期 - 信息获取修改 - - modification-a
            $CustomerBus = new \Business\Member\Customer();
            $customerMap = $CustomerBus->getCustomerListByMemberIdArr($memArr, 'id_card_no');
//            $customerExt  = $CustomerBus->getCustomerExtListByMemberIdArr($memArr, 'com_province as province,com_city as city');

            $statusMap = [
                0 => '未激活',
                1 => '激活',
                2 => '禁用',
                3 => '仓库',
                4 => '挂失',
            ];

            $Area    = new \Model\Product\Area();
            $areaMap = $Area->getAreaList();

            foreach ($list as &$item) {
                $tmpMid = $item['memberid'];

                //实体卡号
                $item['card_no'] = $item['card_no'] ?: '';
                //虚拟卡号
                $item['virtual_no'] = $item['virtual_no'] ?: '';
                //状态
                $item['state'] = $statusMap[$item['status']];
                //账号名称
                $item['account'] = $nameMap[$tmpMid]['dname'] ?: '';
                //身份证
                $item['id_card'] = $customerMap[$tmpMid]['id_card_no'] ?: '';
                //城市
                $item['city'] = $areaMap[$extMap[$tmpMid]['city']] ?: '';
                //省份
                $item['province'] = $areaMap[$extMap[$tmpMid]['province']] ?: '';
                //手机号
                $item['mobile'] = $nameMap[$tmpMid]['mobile'] ?: '';
                //售出时间
                $item['sale'] = $item['sale_time'] ? date('Y-m-d H:i:s', $item['sale_time']) : '';
                //激活时间
                $item['active'] = $item['active_time'] ? date('Y-m-d H:i:s', $item['active_time']) : '';

                if ($item['status'] == 1) {
                    //有效期
                    $valid = date('Y-m-d H:i:s', $item['avalid_begin']) . '~' . date('Y-m-d H:i:s',
                            $item['avalid_end']);
                } else {
                    $valid = '';
                }

                $item['valid'] = $valid;
            }
        }

        $Excel      = array();
        $Excel[0][] = '会员名称';
        $Excel[0][] = '手机号码';
        $Excel[0][] = '身份证号码';
        $Excel[0][] = '所在区域';
        $Excel[0][] = '卡套餐';
        $Excel[0][] = '售出时间';
        $Excel[0][] = '激活时间';
        $Excel[0][] = '实体卡号';
        $Excel[0][] = '虚拟卡号';
        $Excel[0][] = '状态';
        $Excel[0][] = '有效期';
        $i          = 1;
        foreach ($list as $row) {
            $Excel[$i][] = $row['account'];
            $Excel[$i][] = $row['mobile'];
            $Excel[$i][] = $row['id_card'] . ' ';
            $Excel[$i][] = $row['province'] . $row['city'];
            $Excel[$i][] = isset($pNameArr[$row['pid']]['p_name']) ? $pNameArr[$row['pid']]['p_name'] : '';
            $Excel[$i][] = $row['sale'];
            $Excel[$i][] = $row['active'];
            $Excel[$i][] = $row['card_no'];
            $Excel[$i][] = $row['virtual_no'];
            $Excel[$i][] = $row['state'];
            $Excel[$i][] = $row['valid'];
            $i++;
        }

        $xls = new SimpleExcel('UTF-8', true, '年卡列表');
        $xls->addArray($Excel);
        $xls->generateXML('年卡列表');
    }

    /**
     * 获取多用户信息
     *
     * @param  [type] $getMemberid [description]
     *
     * @return [type]               [description]
     */
    private function _getMemberInfoByMulti($getMemberid)
    {

        return (new Member())->getMemberInfoByMulti($getMemberid, 'id', 'id,dname,account,mobile');

    }

    /**
     *检查年卡手机号码是否挂失
     *
     * @param [type] int $mobile   手机号
     *
     * @return array $findMobile   返回是否挂失 手机号
     */
    public function getCheckMobile($mobile)
    {

        $options = [
            'status'   => I('status', 5, 'intval'),
            'identify' => '',
        ];
        $result  = $this->_CardModel->getCheckMemberList($this->_sid, $options, 'select');

        $result = $result ?: [];

        $getMemberid = $getPid = [];
        foreach ($result as $item) {
            if ($item['memberid']) {
                $getMemberid[] = $item['memberid'];
            }

            $getMemberid[] = $item['sid'];

            $getPid[] = $item['pid'];
        }

        $members = $result ? $this->_getMemberInfoByMulti($getMemberid) : [];

        if ($members) {
            $members = $this->_replaceKey($members, 'id');
        }

        foreach ($result as $key => $item) {
            if (isset($members[$item['memberid']])) {
                $result[$key]['account'] = $members[$item['memberid']]['dname'];
                $result[$key]['mobile']  = $members[$item['memberid']]['mobile'];
            }
        }

        $findMobile = [];
        foreach ($result as $key => $mo) {
            if ($result[$key]['mobile'] == $mobile && $result[$key]['status'] == '4') {
                $findMobile = $result[$key]['mobile'];
            }
        }

        return $findMobile;
    }

    /**
     * 获取会员详细信息
     *
     * @param  int  $memberid  会员id
     *
     * @return [type] [description]
     */
    public function getMemberDetail()
    {
        $from       = I('if_fid', 0, 'intval');
        $virtual_no = I('virtual_no', 0);
        $memberid   = I('memberid');
        if (!$virtual_no || !$memberid) {
            $this->apiReturn(204, [], '参数错误');
        }

        if ($from) {
            $sid = 1;
        } else {
            $sid = $this->_sid;
        }

        $list = $this->_CardModel->getMemberDetail($sid, $memberid, false, $virtual_no);
        $list = $list ?: [];

        $member_info      = (new Member())->getMemberInfo($memberid);
        $return['member'] = [
            'account' => $member_info['dname'] ?: $member_info['account'],
            'mobile'  => $member_info['mobile'],
        ];

        if ($virtual_no) {
            $return['member'] = [
                'account' => $list[0]['dname'] ?: $member_info['dname'] ?: $member_info['account'],
                'mobile'  => $list[0]['mobile'] ?: $member_info['mobile'],
            ];
        }

        $getPid = [];
        //$annualBiz     = new AnnualBiz();

        foreach ($list as $key => $item) {

            $getPid[] = $item['pid'];

            $valid_time = date('Y-m-d H:i:s', $item['avalid_begin']) . '~' . date('Y-m-d H:i:s', $item['avalid_end']);

            $list[$key]['group_priv'] = $this->_privilegeBiz->getPrivilegeAndUsedNum($item['pid'], $item['virtual_no']);

            $list[$key]['valid_time'] = $valid_time;
            //$privs                    = $annualBiz->getPrivileges($item['pid'], $item['virtual_no']);
            //$list[$key]['priv']       = $annualBiz->formatPrivilege($privs, $item['virtual_no']);
            //foreach ($privs as $priv) {
            //    $use                  = $annualPrivBiz->getUsedTimes([$priv['tid']], $item['virtual_no'], true);
            //    $all                  = $priv['use_limit'] === '-1' ? '不限' : (explode(',',
            //        $priv['use_limit'])[2] !== '-1' ? explode(',', $priv['use_limit'])[2] : '不限');
            //    $list[$key]['priv'][] = [
            //        'title' => $priv['ltitle'] . '-' . $priv['title'],
            //        'use'   => $use . '/' . $all,
            //    ];
            //}

            //获取组合特权信息
            //
            //$ticket   = $ticModel->getTicketInfoByPid($item['pid'], 'id');
            //$privGroupRes  = $annualPrivBiz->getPrivilegeAndUsedNum([$ticket['id']], $item['virtual_no']);
            //$list[$key]['priv'] = [];
            //if ($privGroupRes['code'] == 200 && !empty($privGroupRes['data'])) {
            //
            //}

            $sid_arr[]   = $item['sid'];
            $virtualNo[] = $item['virtual_no'];
        }

        $pname_map = $this->_TicketModel->getCardName($getPid);

        $supplys = $list ? $this->_getMemberInfoByMulti($sid_arr) : [];
        $supplys = $this->_replaceKey($supplys, 'id');

        //获取购买年卡的备注
        $memo = $this->_CardModel->getAnnualMemo($virtualNo);

        foreach ($list as $key => $item) {
            $list[$key]['memo']   = $memo[$item['virtual_no']];
            $list[$key]['supply'] = $supplys[$item['sid']]['dname'];
            $list[$key]['title']  = $pname_map[$item['pid']];

            //增加过期状态判断
            if ($item['status'] == 1 && $item['avalid_end'] < time()) {
                $list[$key]['status'] = 5;
            }
        }

        $return['list'] = $list;

        $this->apiReturn(200, $return, []);
    }

    /**
     * 获取用户的年卡消费订单
     *
     * @param  [type] $memberid [description]
     *
     * @return [type]           [description]
     */
    public function getHistoryOrder()
    {
        $virtualNo = I('virtual_no', "");
        $memberid  = 0;
        if (!$virtualNo) {
            if (!$memberid = I('memberid', '', 'intval')) {
                $this->apiReturn(204, [], '参数错误');
            }
        }

        $page = I('page', 1, 'intval');
        $size = I('size', 10, 'intval');

        $sid = $this->_sid;

        $orders = $this->_privilegeBiz->getHistoryOrder($sid, $memberid, $page, $size, 'select', $virtualNo);

        $total = $totalPage = 0;
        if ($orders) {
            $total = $this->_privilegeBiz->getHistoryOrder($sid, $memberid, 1, 1, 'count', $virtualNo);

            $totalPage = ceil($total / $size);
        }

        $return = [
            'list'       => $orders ?: [],
            'total'      => $total,
            'page'       => $page,
            'total_page' => $totalPage,
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 年卡库存详细信息
     * @return [type] [description]
     */
    public function getAnnualCardStorage()
    {
        $card      = I('card', '', 'strval');      //卡号
        $beginTime = I('btime', '', 'strval');    //录入时间
        $endTime   = I('etime', '', 'strval');    //录入时间
        $show      = I('show', 0, 'intval');     //显示方式
        $page_size = I('page_size', '10', 'intval');
        $page      = I('page', 1, 'intval');

        if (($pid = I('pid')) < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $startCreateTime = 0;
        $endCreateTime   = 0;
        if ($beginTime && $endTime) {
            $startCreateTime = strtotime($beginTime);
            $endCreateTime   = strtotime($endTime . ' 23:59:59');
        }

        $commodityProductBiz = new \Business\CommodityCenter\Product();
        $product             = $commodityProductBiz->getProductInfoById($pid);

        //show 0全部 1实体卡 2虚拟卡 需要转换一次
        $cardType = !$show ? 0 : (($show == 2) ? 1 : 2);

        $annualBiz = new \Business\AnnualCard\AnnualBizService();
        $annualStorageTotal = $annualBiz->getAnnualCardStorageTotal($this->_sid, [$pid], $startCreateTime, $endCreateTime, $card, $cardType);
        if ($annualStorageTotal['code'] != 200) {
            $this->apiReturn(204, [], '库存接口异常，请稍后重试');
        }
        $virStorage = $annualStorageTotal['data']['virtual'];
        $phyStorage = $annualStorageTotal['data']['physics'];

        $annualStorageList = $annualBiz->getAnnualCardStorageList($this->_sid, [$pid], $startCreateTime, $endCreateTime, $card, $page, $page_size, $cardType);
        if ($annualStorageList['code'] != 200) {
            $this->apiReturn(204, [], '库存接口异常，请稍后重试');
        }
        $cards = [];
        foreach ($annualStorageList['data']['list'] as &$item) {
            $item['create_time'] = strtotime($item['create_time']);
            $item['physics_no']  = hexdec($item['physics_no']);
            $cards[]             = $item;
        }
        $total = $annualStorageList['data']['total'];

        $return = [
            'title'      => $product['p_name'],
            'cards'      => $cards,
            'virtual'    => $virStorage,
            'physics'    => $phyStorage,
            'page'       => $page,
            'total_page' => ceil($total / $page_size),
        ];

        $this->apiReturn(200, $return);

    }

    /**
     * 获取虚拟卡库存
     * @return [type] [description]
     */
    public function getVirtualStorage()
    {
        if (($pid = I('pid')) < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $sid = I('sid', '', 'intval');

        $vir_storage = $this->_CardModel->getAnnualCardStorage($sid, $pid, 'virtual');

        $return = ['storage' => $vir_storage];

        $this->apiReturn(200, $return);
    }

    /**
     * [可添加到年卡特权的门票(自供应 + 转分销一级)
     * @return [type] [description]
     */
    public function getTickets()
    {
        //景区id
        $lid = I('lid', 0, 'intval');
        //上级供应商id
        $aid = I('aid', 0, 'intval');
        if (!$lid || !$aid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $ticModel = new Ticket('slave');

        //产品条件
        $option['where'] = ['l.id' => $lid, 't.pay' => 1];
        $list            = $ticModel->getAllSaleProductsByFidSid($this->_sid, $aid, $option);

        $return = [];
        foreach ($list as $item) {
            $return[] = [
                'id'        => $item['tid'],
                'pid'       => $item['pid'],
                'title'     => $item['ttitle'],
                'apply_did' => $item['sapply_sid'],
            ];
        }

        $this->apiReturn(200, $return);
    }

    public function orderSuccess()
    {

        $ordernum = I('ordernum');
        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }
        $ordernumArr = explode(',', $ordernum);
        $toolModel   = new OrderTools('slave');
        $javaApi     = new \Business\CommodityCenter\Ticket();
        $return      = [];

        foreach ($ordernumArr as $ordernum) {
            $orderInfo   = $this->_CardModel->orderSuccess($ordernum);
            $orderDetail = $toolModel->getOrderInfo($ordernum);
            $pname_map   = $this->_TicketModel->getCardName([$orderInfo[0]['pid']]);

            $tidAttr    = $javaApi->queryTicketAttrsById($orderInfo['tid']);
            $ticketAttr = [];
            foreach ($tidAttr as $item) {
                $ticketAttr[$item['ticket_id']][$item['key']] = $item['val'];
            }

            $return[] = [
                'ordernum'    => $ordernum,
                'book_active' => isset($ticketAttr[$orderInfo['tid']]['book_active']) ? (int)$ticketAttr[$orderInfo['tid']]['book_active'] : 1,
                'type'        => $orderInfo[0]['physics_no'] ? 'physics' : 'virtual',
                'list'        => $orderInfo,
                'price'       => $orderDetail['totalmoney'] / 100,
                'date'        => date('Y-m-d H:i:s', time()),
                'ordername'   => $orderDetail['ordername'],
                'ordertel'    => $orderDetail['ordertel'],
                'title'       => $pname_map[$orderDetail['pid']],
            ];
        }

        // if ($return['type'] == 'virtual') {
        //     //订单直接验证
        //     $this->_CardModel->verifyAnnualOrder($ordernum);
        // }

        $this->apiReturn(200, $return);
    }

    /**
     * 获取物理卡或者虚拟卡
     *
     * <AUTHOR>
     * @date   2017-04-01
     *
     */
    public function getCardsForOrder()
    {
        //产品id
        $pid = I('pid', 0, 'intval');
        //物理卡号列表,不传则为虚拟卡
        $physics = I('physics', '');

        if ($pid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        //购买虚拟卡
        if ($physics == '') {
            $this->apiReturn(200, [], '虚拟卡购买');
        } else {
            $physicsArr = explode(',', $physics);

            if (count($physicsArr) > 10) {
                $this->apiReturn(204, [], '一次最多只能购买10张物理卡');
            }
            //进制转换
            foreach ($physicsArr as &$item) {
                $item = dechex($item);
            }

            $options = [
                'where' => ['pid' => $pid, 'physics_no' => ['in', $physicsArr], 'status' => 3],
                'field' => 'virtual_no,sid,physics_no,card_no',
            ];

            $return = $this->_CardModel->getAnnualCard(1, 1, $options, 'select');
        }

        $this->apiReturn(200, $return ?: []);
    }

    /**
     * 获取下单信息
     * @return [type] [description]
     */
    public function getOrderInfo()
    {

        $aid  = I('aid', '', 'intval');
        $pid  = I('pid', '', 'intval');
        $type = I('type');

        if ($aid < 1 || $pid < 1 || !in_array($type, ['virtual', 'physics'])) {
            $this->apiReturn(204, [], '参数错误');
        }

        //$ticModel  = new Ticket('slave');
        $landModel = new Land('slave');
        $annualCardVerifyBiz = new AnnualCardVerifyBiz();

        $extApi      = new \Business\CommodityCenter\Ticket();
        $productInfo = $extApi->queryTicketInfoByProductIds([$pid]);

        //获取门票信息
        $product = $productInfo[0]['ticket'];
        //$product = $ticModel->getTicketInfoByPid($pid);
        if (!$product) {
            $this->apiReturn(403, [], '门票信息获取失败');
        }

        //$javaApi  = new \Business\CommodityCenter\LandF();
        //$landFArr = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([], [], [$pid],
        //    'tid,family_card_num');

        $landFArr = $productInfo[0]['land_f'];
        $ext      = $landFArr ?? [];

        //获取扩展属性
        $extInfoArr = $productInfo[0]['ext'];

        //获取景区信息
        $land = $landModel->getLandInfo($product['landid'], false, 'title,jqts');
        //获取价格
        $priceApi = new Price();
        $priceRes = $priceApi->getActualtimePrice($this->_sid, $aid, $product['id'], date('Y-m-d'));
        if ($priceRes['code'] != 200 || $priceRes['data']['settlement_price'] == -1) {
            $this->apiReturn(403, [], '当前产品不可购买');
        }

        //虚拟库存
        $storage = -1;
        if ($type == 'virtual') {
            $storage = $this->_CardModel->getAnnualCardStorage($product['apply_did'], $pid, $type);
        }

		$annual_delay_enabled_time = isset($extInfoArr['annual_delay_enabled_time']) ? (int)$extInfoArr['annual_delay_enabled_time']: 0;
		$annual_valid_type = $extInfoArr['annual_valid_type'];
        $isOrderActive = $annualCardVerifyBiz->isOrderActiveByTicket($extInfoArr['book_active'], 0, $extInfoArr['book_active_channels']);
        $tab = [
            (int)$ext['family_card_num'] > 0 ? '多人卡' : '单人卡',
            '售出后' . $product['delaydays'] . '天内可激活',
            $isOrderActive ? '售出后立即激活' : '售出后不激活',
            '条件退',
        ];
		if($annual_valid_type == 1){    // TODO 常量定义
			if($annual_delay_enabled_time > 0){
				$tab[] = "激活后，延迟{$annual_delay_enabled_time}小时生效";
			}
		}

        //$annualIdentityInfo = isset($extInfoArr['annual_identity_info']) ? (!empty((int)$extInfoArr['annual_identity_info'] & (1 | 2)) ? 1 : 0) : 0;
        //年卡身份证信息  0 非必填 1激活必填 2下单必填
        $annualIdentityInfo = $annualCardVerifyBiz->isOrderIdentityByTicket($extInfoArr['annual_identity_info'] ?? 0, $isOrderActive);

        $data            = [];
        $data['product'] = [
            'sid'                  => $product['apply_did'],
            'tid'                  => $product['id'],
            'ltitle'               => $land['title'],
            'title'                => $product['title'],
            'storage'              => $storage,
            'price'                => $priceRes['data']['settlement_price'] / 100,
            'family_card_num'      => (int)$ext['family_card_num'],
            'tab'                  => $tab,
            'is_need_wirte'        => isset($extInfoArr['is_need_wirte']) ? (int)$extInfoArr['is_need_wirte'] : 0,
            'annual_identity_info' => $annualIdentityInfo,
            'order_active' => $isOrderActive,
            //'book_active'          => isset($extInfoArr['book_active']) ? (int)$extInfoArr['book_active'] : 1,
            //'book_active_channels' => isset($extInfoArr['book_active_channels']) ? $extInfoArr['book_active_channels'] : '2,3',
        ];
        $data['tags'] = (new AnnualCardTagBiz())->getPlatformOrderTag($product, $ext, $extInfoArr);

        $data['need_ID'] = $annualIdentityInfo;

        $annualBiz     = new AnnualBiz();
        $annualPrivBiz = new \Business\Product\AnnualCardPrivilege();
        //年卡囊括的特权信息
        $data['privileges'] = $annualBiz->getPrivileges($pid);
        //新版年卡组合特权
        $data['group_priv'] = $annualPrivBiz->getPrivilegeInfo($pid);

        $data['pay']['is_self'] = intval($aid == $this->_sid);

        if (!$data['pay']['is_self']) {
            $memModel = new Member('slave');
            $remain   = $memModel->balanceAndCredit($this->_sid, $aid);
            //结算方式
            $memberRelationBus          = new MemberRelation();
            $res                        = $memberRelationBus->getDisClearingModels($aid, [$this->_sid]);
            $clearingway                = isset($res[$this->_sid]) ? $res[$this->_sid] : 0;
            $data['pay']['remain']      = $remain['balance'] / 100;
            $data['pay']['credit']      = $remain['credit'] / 100;
            $data['pay']['clearingway'] = $clearingway;
        }

        //供应商信息
        $supplier = (new Member())->getMemberInfo($product['apply_did']);
        $CustomerBus  = new \Business\Member\Customer();
        $customerInfo = $CustomerBus->getCustomerInfoByMemberId($product['apply_did']);

        $data['supplier'] = [
            'name'    => $supplier['dname'],
            'cname'    => $supplier['cname'],
            'qq' => $customerInfo['qq'],
            //'linkman' => $supplier['mobile'],
            'intro'   => $land['jqts'],
        ];

        $this->apiReturn(200, $data);

    }

    /**
     * 虚拟卡下单，判断是否需要替换
     */
    public function isNeedToReplace()
    {
        //产品要求改成一个人可以再同个供应商下办理多张年卡，所以无需判断是否替换
        $this->apiReturn(200, ['exist' => 0]);
    }

    /**
     * 发送验证码
     * @return [type] [description]
     */
    public function sendVcode()
    {
        $this->apiReturn(405, [], '接口废弃');

        //$mobile = I('mobile');
        //
        //if (!ismobile($mobile)) {
        //    $this->apiReturn(204, [], '请输入正确的手机号');
        //}
        //
        //$code_info = $this->_getCodeInfo($mobile);
        //if ($code_info && (time() - $code_info['time'] < 60)) {
        //    $this->apiReturn(204, [], '操作太频繁');
        //}
        //
        //$code = substr(str_shuffle('123456789'), 0, 6);
        //
        //$content = str_replace('{vcode}', $code, $this->_getVcodeTpl());
        //
        //$soap = $this->getSoap();
        //
        //$result = $soap->Send_SMS_V($mobile, $content);
        //
        //if ($result == 100) {
        //
        //    $data = [
        //        'code' => $code,
        //        'time' => time(),
        //    ];
        //
        //    Cache::getInstance('redis')->set("vcode:annual_active:$mobile", json_encode($data), '', 1800);
        //
        //    $this->apiReturn(200, [], '验证码发送成功');
        //
        //} else {
        //    $this->apiReturn(204, [], '验证码发送失败');
        //}
    }

    /**
     * 验证码检测
     *
     * @param  [type] $mobile [description]
     * @param  [type] $code   [description]
     *
     * @return [type]         [description]
     */
    private function _checkVcode($mobile, $code)
    {
        $cache_info = $this->_getCodeInfo($mobile);

        if (!$cache_info) {
            $this->apiReturn(204, [], '验证码已过期');
        }

        if ($cache_info['code'] != $code) {
            $this->apiReturn(204, [], '验证码错误');
        }
    }

    private function _checkcardVcode($mobile, $code)
    {

        $res  = Vcode::verifyVcode($mobile, $code, 'annual_loss');
        $code = $res['code'];
        $msg  = $res['msg'];

        //登录验证码不正确
        if ($code != 200) {
            $this->apiReturn($code, [], $msg);
        }
    }

    private function _getCodeInfo($mobile)
    {

        $cache = Cache::getInstance('redis')->get("vcode:annual_active:$mobile");

        return json_decode($cache, true);

    }

    private function _getcardCodeInfo($mobile)
    {

        $cache = Cache::getInstance('redis')->get("vcode:annual_active_card:$mobile");

        return json_decode($cache, true);

    }

    private function _getVcodeTpl()
    {

        return '您正在使用年卡激活服务，验证码：{vcode}';

    }

    private function _getcardVcodeTpl()
    {

        return '您正在使用年卡补卡服务，验证码：{vcode}';
        // return '尊敬的{account}用户，您持有的[{card_no}]年卡申请挂失，验证码：{vcode},请妥善保管。';

    }

    /**
     * 实例化年卡model
     * @return [type] [description]
     */
    private function _initializeCardModel()
    {
        static $CardModel = null;

        if (is_object($CardModel)) {
            return $CardModel;
        }

        $CardModel = new CardModel();

        return $CardModel;
    }

    private function _replaceKey($arr, $key)
    {
        $new_arr = [];

        foreach ($arr as $item) {
            $new_arr[$item[$key]] = $item;
        }

        return $new_arr;
    }

    /**
     * 补实体卡号操作
     * @date   2017-03-27
     * <AUTHOR>
     *
     * @param  string  $id  表ID
     * @param  string  $physicsCard  物理卡号
     * @param  string  $cardNo  卡号
     *
     * @return json
     */
    public function addPhysicsCard()
    {
        $id          = I('post.id', '', 'intval');
        $physicsCard = I('post.physics_no', '', 'strval');
        $cardNo      = I('post.card_no', '', 'strval');
        $avalidEnd  = I('post.avalid_end', '', 'strval');
        if (!$id || !$physicsCard || !$cardNo) {
            $this->apiReturn(400, [], '缺少参数');
        }
        $table = $this->getArchiveAnnualCardTable($avalidEnd);
        $oriPhysicsNo = $physicsCard;
        $physicsCard  = dechex($physicsCard);
        $annualModel = new CardModel(0, 0, $table);
        if ($annualModel->checkCard('physics_no', $physicsCard)) {
            $this->apiReturn(201, [], '物理ID已被使用');
        }

        $baseCard = new BaseCard();
        $exist    = $baseCard->getCardInfo($oriPhysicsNo);
        if ($exist) {
            $this->apiReturn(201, [], '物理ID已被其他类型卡占用');
        }

        if ($annualModel->checkCard('card_no', $cardNo, ['sid' => intval($this->_sid)])) {
            $this->apiReturn(202, [], '实体卡号已存在');

        }
        //激活的时候判断下是不是新版一卡通里面下单的
        $allInOneCardJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\AllInOneCard();
        $allInOneCardInfo    = $allInOneCardJsonRpc->getRelationInfoByAppId($id, AllInOneCardConst::RELATION_ANNUAL_CARD);
        if ($allInOneCardInfo['code'] != 200) {
            $this->apiReturn(204, [], '年卡与一卡通信息获取失败：' . $allInOneCardInfo['msg']);
        }
        if (!empty($allInOneCardInfo['data']['member_id'])) {
            $this->apiReturn(204, [], '年卡与新一卡通购关联，无法补实体卡');
        }

        $res = $annualModel->getCardInfoById($id, 'sid,memberid,pid,virtual_no,physics_no,card_no');
        if ($res && $res['sid'] != $this->_sid) {
            $this->apiReturn(203, [], '无权操作');
        }
        //记录年卡产品操作记录流水
        $memberId = $res['memberid'];
        $pid      = $res['pid'];
        $sid      = $res['sid'];
        $ptype    = 9;
        $remark   = '平台补卡:' . '不收手续费 金额为0';
        $orderId  = date('ymd') . substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 4);
        $success = false;
        $annualModel->startTrans();
        try {
            $ret = $annualModel->updateCardById($id, $physicsCard, $cardNo);
            if (!$ret) {
                $this->apiReturn(400, [], '补卡失败');
            }
            //更新到基础卡
            $annualBiz = new AnnualBiz();
            $annualBiz->addICRecord([$oriPhysicsNo]);
            if ($ptype != 1) {
                //操作成功之后记录年卡流水表
                //通过card_id获取到实体卡号 物理卡号 虚拟卡号
                $cardInfo = $annualModel->getCardInfoById($id,
                    'virtual_no,card_no,physics_no,dname,mobile,id_card_no');
                $option   = [
                    'user_name'    => $cardInfo['dname'],                      //用户姓名
                    'user_mobile'  => $cardInfo['mobile'],                     //用户手机号
                    'user_id_card' => $cardInfo['id_card_no'],                 //用户身份证号
                    'virtual_no'   => $cardInfo['virtual_no'],                 //虚拟卡号
                    'card_no'      => $cardInfo['card_no'],                    //实体卡号
                    'physics_no'   => $cardInfo['physics_no'],                 //物理卡号
                ];
                $annualBiz->addAnnualCardTrade($memberId, $pid, $sid, 0, $orderId, 2, $sid, $ptype, $remark, 0, $id, 1,
                    $option);
            }
            $annualModel->commit();
            $success = true;
        } catch (\Exception $e) {
            $annualModel->rollback();
        }
        //添加年卡管理操作记录
        $cardManageService = new \Business\AnnualCard\AnnualCardManage();
        $cardManageService->addCardManageRecordWithCardInfo(AnnualCardConst::ANNUAL_OPT_FIX_PHYSIC, [
            'opt_sid' => $this->_sid,
            'opt_member' => $this->_memberId,
            'physics' => $oriPhysicsNo,
            'card_no' => $cardNo,
            'fail_success' => $success,
        ], $res);
        $code = $success ? 200 : 400;
        $msg = $success ? '补卡成功' : '补卡失败';
        $this->apiReturn($code, [], $msg);
    }

    /**
     * 编辑年卡信息
     * @date   2017-03-27
     * <AUTHOR>
     *
     * @param  string  $paramsObject->mobile  手机号
     * @param  string  $paramsObject->name  会员名称
     * @param  string  $paramsObject->id_card  身份证
     * @param  int  $paramsObject->province  省
     * @param  int  $paramsObject->city  市
     * @param  string  $paramsObject->address  详细地址
     * @param  string  $paramsObject->remark  备注
     * @param  array[string]  $paramsObject->avatar 头像数组
     * @param  int  $paramsObject->card_id  年卡id
     * @param  int  $paramsObject->member_id 年卡用户id
     * @param  string  $paramsObject->avalid_end 年卡过期日期 2024-11-06
     *
     * @return json
     */
    public function editCard()
    {
        $paramsObject       = (object)I('');
        $paramsObject->opid = $this->_memberId;
        $annualBiz = new AnnualBiz();
        $result    = $annualBiz->editCardInfo($this->_sid, $paramsObject);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], '接口出错');
    }

    /**
     * 获取单个年卡信息
     * @date   2017-03-28
     * <AUTHOR>
     *
     * @param  string  $cardId  物理ID
     * @param  int  $memberId  会员ID
     *
     * @return json
     */
    public function getCardInfo()
    {
        $cardId   = I('post.cid', '', 'intval');
        $memberId = I('post.memberid', '', 'intval');
        $avalidEnd  = I('post.avalid_end', '', 'strval');
        $table = $this->getArchiveAnnualCardTable($avalidEnd);
        $annualModel = new CardModel(0, 0, $table);
        $res = $card = $annualModel->getCardInfoById($cardId,
            'sid, physics_no, virtual_no, pid, memberid,dname,mobile,id_card_no,province,city,address,remarks,ext_info');
        if (!$res) {
            $this->apiReturn(400, [], '没有相关数据');
        }
        if ($res['sid'] != $this->_sid) {
            $this->apiReturn(203, [], '无权操作');
        }
        $annualExtInfo = json_decode($res['ext_info'], true);
        $physicsNo     = $res['physics_no'];
        //人脸限制
        $javaApi   = new \Business\CommodityCenter\LandF();
        $ticketArr = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([], [], [$res['pid']], 'family_card_num,tid');
        $extInfo   = $ticketArr[0];
        $res = [
            'mobile'       => $res['mobile'] ?: '',
            'dname'        => $res['dname'] ?: '',
            'headphoto'    => '',
            'other_les'    => '',
            'address'      => $res['address'] ?: '',
            'province'     => $res['province'] ?: 0,
            'city'         => $res['city'] ?: 0,
            'id_card_no'   => $res['id_card_no'] ?: '',
            'virtual_no'   => $res['virtual_no'],
            'voucher_type' => $annualExtInfo['voucher_type'],
            'remarks'      => $res['remarks'],
        ];

        $faceBiz                  = new \Business\Face\FaceBase();
        $res['family_card_num']   = (int)$extInfo['family_card_num'];
        $res['physics_no']        = $physicsNo;
        $annualCardManageBiz      = new AnnualCardManage();
        $affiliatesPerson         = $annualCardManageBiz->getAffiliatesPerson($cardId);
        $res['affiliates_person'] = $affiliatesPerson['data'];
        $res['avatar']            = [];
        //获取人脸照片组
        $group = $annualModel->getGroupPhotos($card['virtual_no']);
        if ($group) {
            $photos   = array_filter(json_decode($group['photos'], true));
            $photoArr = [];
            foreach ($photos as $p) {
                $photoArr[] = $faceBiz->faceUrlConversion($p);
            }

            $res['avatar'] = $photoArr;
            if ($res['avatar'] == null) {
                $res['avatar'] = [];
            }
        }
        $checkResult          = (new AnnualCardVerifyBiz())->checkAnnualRealNameAndPhoto($extInfo['tid'], 1, 2);
        $res['need_ID']       = $checkResult['data']['annual_identity_info'];
        $res['is_need_wirte'] = $checkResult['data']['is_need_wirte'];

        unset($res['headphoto']);
        $this->apiReturn(200, ['list' => $res], '');
    }

    /**
     * 根据订单号获取年卡下单信息
     * @date   2017-03-28
     * <AUTHOR>
     *
     * @param  string  $orderId  平台订单号
     *
     * @return json
     */
    public function annualOrderDetail()
    {
        $orderId = I('post.orderid', '', 'strval');
        //$orderId = 4004804;
        if (!$orderId) {
            $this->apiReturn(201, [], '缺少参数');
        }

        $virtual = $this->_CardModel->getVirtualNoByOrdernum($orderId);

        if ($virtual) {
            $data       = [];
            $virtualArr = explode(',', $virtual);
            //$virtualArr[] = 'UURA9256';
            $data['virtual_no'] = $virtualArr;
            $data['card_no']    = [];
            $res                = $this->_CardModel->getCardInfoByVirtual($virtualArr, 'card_no');
            if ($res) {
                foreach ($res as $val) {
                    $data['card_no'][] = $val['card_no'];
                }
            }
            $this->apiReturn(200, $data, '');
        } else {
            $this->apiReturn(202, [], '数据异常');
        }
    }

    /**
     * 录入卡号的时候判断是否重复
     *
     * @date   2017-09-22
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function checkCardWhenRecord()
    {
        $cardNo    = I('post.card', '', 'strval');
        $physicsNo = I('post.physics_no', '', 'strval');
        if (!$cardNo) {
            $this->apiReturn(201, [], '卡号参数不能为空');
        }

        if (!$physicsNo) {
            $this->apiReturn(201, [], '物理卡号参数不能为空');
        }

        if ($this->_CardModel->checkCard('sid', intval($this->_sid), ['card_no' => $cardNo])) {
            $this->apiReturn(201, [], '实体卡号已存在');
        }

        $physicsCard  = dechex($physicsNo);
        if ($this->_CardModel->checkCard('physics_no', $physicsCard)) {
            $this->apiReturn(201, [], '物理ID已被使用');
        }

        $baseCard = new BaseCard();
        $exist    = $baseCard->getCardInfo($physicsNo);
        if ($exist) {
            $this->apiReturn(201, [], '物理ID已被其他类型卡占用');
        }

        $this->apiReturn(200, [], 'success');
    }

    /**
     * 获取年卡有效期
     * <AUTHOR>
     * @date   2017-11-27
     */
    public function getValidDate()
    {
        $this->apiReturn(204, [], '接口调整中');
    }

    /**
     * 修改年卡有效期
     * <AUTHOR>
     * @date   2017-11-28
     */
    public function changeValidDate()
    {
        $this->apiReturn(204, [], '接口调整中');
    }

    /**
     * 获取续费显示信息
     *
     * @date   2018-03-06
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    public function getRenewData()
    {
        $applyId = I('apply_id', '', 'intval');
        $cardFid = I('memberid', '', 'intval');
        $landId  = I('land_id', '', 'intval');

        if ($applyId == '' || $landId == '' || !$cardFid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $ticketModel         = new Ticket();
        $commodityProductBiz = new \Business\CommodityCenter\Product();
        $pidArr              = $commodityProductBiz->getProductInfoByContactId([$landId]);

        if (!$pidArr) {
            $this->apiReturn(202, [], '年卡产品数据不存在');
        }

        $memberId = $this->_sid;

        $list = [];

        $currentData = $this->_CardModel->getMemberDetail($applyId, $cardFid, true);

        if (!$currentData) {
            $this->apiReturn(202, [], '年卡数据不存在');
        }

        $currentCard = $currentData[0];

        $javaApi   = new TicketApi();
        $ticketBiz = new \Business\CommodityCenter\Ticket();
        $annualBiz = new \Business\Product\AnnualCardVerifyBiz();

        foreach ($pidArr as $key => $item) {
            $ticket = $ticketModel->getTicketInfoByPid($item['id'], 'id, status');

            if ($ticket['status'] != 1) {
                unset($list[$key]);
                continue;
            }
            $validTime = date('Y-m-d H:i:s', $currentCard['avalid_begin']) . '~' . date('Y-m-d H:i:s',
                    $currentCard['avalid_end']);

            $ticketInfo = $ticketBiz->fieldConversion($ticketBiz->queryTicketInfoById($ticket['id']));
            //$ticketInfo = $javaApi->getTickets($ticket['id']);

            if (!$ticketInfo) {
                unset($list[$key]);
                continue;
            }

            $delayType = $ticketInfo['valid_period_type'];

            if ($delayType == 2) {
                unset($list[$key]);
                continue;
            }

            $list[$key]['available_time_period'] = $annualBiz->annualAvailableTimePeriodHandle($ticketInfo['ext']['available_time_period'] ?? '');

            $list[$key]['valid_time'] = $validTime;
            $list[$key]['delay_type'] = $delayType;
            $list[$key]['delay_day']  = $ticketInfo['valid_period_days'];

            //$annualBiz = new AnnualBiz();
            $annualPrivBiz = new \Business\Product\AnnualCardPrivilege();
            //套餐数据
            //$privs              = $annualBiz->getPrivileges($item['id']);
            //$list[$key]['priv'] = $annualBiz->formatPrivilege($privs, $currentCard['virtual_no']);

            $list[$key]['group_priv'] = $annualPrivBiz->getPrivilegeInfo($item['id']);

            $priceData = TicketApi::refreshPrice($memberId, $applyId, $ticket['id'],
                $playDate = date('Y-m-d'));

            if ($priceData && $priceData['retail_price'] == -1) {
                unset($list[$key]);
                continue;
            }
            if ($currentCard['sid'] == $this->_sid) {
                $priceData['settlement_price'] = $priceData['retail_price'];
            }

            $list[$key]['price'][] = $priceData;

            $list[$key]['title'] = $item['p_name'];
            $list[$key]['pid']   = $item['id'];
            $list[$key]['tid']   = $ticket['id'];
        }

        if ($memberId != $applyId) {
            $memberMoney = MemberMoney::getAllMoney($memberId, $applyId);
            $memberMoney = $memberMoney['credit3'];
        } else {
            $memberMoney = 0;
        }

        //用户二期 - 信息获取修改 - 2
        $idCard = $currentCard['id_card_no'];
        $name   = $currentCard['dname'];

        //续费身份证号判断
        if (empty($currentCard['id_card_no'])) {
            $memberModel  = new Member();
            $CustomerBus  = new \Business\Member\Customer();
            $customerInfo = $CustomerBus->getCustomerInfoByMemberId($cardFid);
            //$name         = $memberModel->getMemberCacheById($cardFid, 'dname');
            $queryParams = [[$cardFid]];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                $queryParams);

            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $name = array_column($queryRes['data'], null, 'id')[$cardFid]['dname'];
            }

            if ($customerInfo['id_card_no']) {
                $idCard = $customerInfo['id_card_no'];
            } else {
                $idCard = '';
            }
        }

        $data = [
            'money'   => (int)$memberMoney / 100,
            'id_card' => $idCard,
            'name'    => $name,
            'list'    => array_values($list),
        ];

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 年卡下单前置校验
     */
    public function annualOrderPreCheck()
    {
        $paramsObject              = I('');
        $paramsObject['ordermode'] = 0;

        $annualBiz = new AnnualBiz();
        $checkRes  = $annualBiz->annualOrderPreCheck($paramsObject);

        $this->apiReturn($checkRes['code'], $checkRes['data'], $checkRes['msg']);
    }

    /**
     * 购买年卡下单接口
     * <AUTHOR>
     * @date   2018-06-27
     */
    public function orderForCard()
    {

        //下单参数对象
        $paramsObject            = (object)I('');
        $paramsObject->ordermode = 0;
        $paramsObject->opt_member = $this->_memberId;
        $paramsObject->operid = $this->_memberId;

        //获取到下单实体卡数量
        $virtualNuArr = explode(',', $paramsObject->virtual_no);
        $physicsNoArr = [];
        $countNum     = count($virtualNuArr);

        if (empty($virtualNuArr)) {
            $this->apiReturn(203, [], '年卡数据不存在');
        }

        if ($countNum > 500) {
            $this->apiReturn(203, [], '超过最大下单数量:500');
        }

        $annualBiz = new AnnualBiz();
        $cardInfo  = $this->_CardModel->getCardsByVirtualNoArr($virtualNuArr, 'virtual_no, physics_no, card_no', true);
        foreach ($cardInfo as $item) {
            $physicsNoArr[] = [
                hexdec($item['physics_no']),
                $item['card_no'],
                $paramsObject->pid,
            ];
        }

        if ($paramsObject->order_type == 1 && $countNum > 1) {
            $paramsObject->physics_no = $physicsNoArr;
            $result                   = $annualBiz->orderCardForDidV2($this->_sid, (array)$paramsObject, false);
        } else if ($paramsObject->order_type == 0) {
            // 虚拟卡批量购买
            $result = $annualBiz->batchOrderVirtualCard($this->_sid, (array)$paramsObject);
        } else {
            $result = $annualBiz->orderForCard($this->_sid, $paramsObject);
            //为了做平台购卡兼容 特殊处理下
            if ($result['code'] == 200) {
                $tmpArr = $result['data'];
                unset($result['data']);
                $result['data']['list'][] = $tmpArr;
            }
        }

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }

    }

    /**
     * 释放实体卡
     * <AUTHOR> Li
     * @date   2019-02-01
     */
    public function releaseEntityCard()
    {
        $cardFid = I('member_id', 0, 'intval');
        $cardNo  = I('card_no', '', 'strval');
        $cardId  = I('id', '', 'intval');
        $avalidEnd  = I('avalid_end', '', 'strval');

        if (!$cardFid || !$cardNo || !$cardId) {
            $this->apiReturn(203, [], '缺少必要信息');
        }
        $table = $this->getArchiveAnnualCardTable($avalidEnd);
        $annualBiz = new AnnualBiz();
        $result    = $annualBiz->releaseEntityCard($this->_sid, $cardFid, $cardNo, $cardId, $this->_memberId, false, $table);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], '接口异常');
    }

    /**
     * 补实体卡号
     * <AUTHOR> Li
     * @date   2019-02-02
     *
     */
    public function operationAnnualCardNo()
    {
        $cardNo = I('card_no', '', 'strval');
        $cardId = I('id', '', 'strval');

        if (!$cardNo || !$cardId) {
            $this->apiReturn(203, [], '缺少必要信息');
        }

        $annualBiz = new AnnualBiz();

        $result = $annualBiz->operationAnnualCardNo($this->_sid, $cardId, $cardNo);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }

        $this->apiReturn(200, [], '补实体卡号成功');
    }

    /**
     * 年卡过期微信推送配置
     * <AUTHOR>
     * @date   2019-03-29
     *
     * @param  int     open 0不开启 1 开启是否开启推送
     * @param  int     validity 过期时间
     * @param  int     day 发送间隔天
     * @param  int     notice_num 通知次数
     *
     * @return int
     */
    public function addAutoWxChatNoticeConfig()
    {
        $openConfig   = I('post.open', 0);
        $validityTime = I('post.validity', 0, 'intval');
        $everyData    = I('post.day', 0, 'intval');
        $noticeNum    = I('post.notice_num', 0, 'intval');
        if (!in_array($openConfig, [0, 1])) {
            $this->apiReturn(204, [], '参数错误');
        }
        if ($openConfig && ($validityTime < 1 || $everyData < 1 || $noticeNum < 1)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $annualBiz = new AnnualBiz();
        $result    = $annualBiz->addWxNoticeConfig($this->_sid, $openConfig, $validityTime, $everyData, $noticeNum);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取年卡过期微信推送配置
     * <AUTHOR>
     * @date   2019-03-29
     */
    public function getAutoWxChatNoticeConfig()
    {
        $annualBiz = new AnnualBiz();
        $result    = $annualBiz->getAnnualWxConfig($this->_sid);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 手动发送过期年卡通知到微信
     * <AUTHOR>
     * @date   2019-03-29
     */
    public function sendWxAnnualTemplateToUser()
    {
        $validity = I('post.validity', 1);
        if ($validity < 1 || !is_numeric($validity)) {
            $this->apiReturn(204, [], '天数有误');
        }
        $annualBiz = new AnnualBiz();
        $result    = $annualBiz->sendAnnualExpireTemplate($this->_sid, $validity);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    public function createVirtualNo()
    {
        $length = I('get.length', 0, 'intval');

        if (!$length || $length > 1000) {
            $this->apiReturn(203, [], '生成数量有误');
        }

        $annualBiz = new \Business\AnnualCard\AnnualBizService();
        $result    = $annualBiz->createVirtualNo($length);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 激活虚拟卡
     * <AUTHOR>
     */
    public function activeVirtualCard()
    {
        $virtualNo        = I('post.virtual_no', '', 'strval'); // 虚拟卡
        $mobile           = I('post.mobile', '', 'strval'); // 手机号
        $idCard           = I('post.id_card', '', 'strval'); // 身份证
        $name             = I('post.name', '', 'strval'); // 会员名称
        $avatar           = I('post.avatar', []); // 头像，有数据则是个数
        $province         = I('post.province', 0, 'intval'); // 省
        $city             = I('post.city', 0, 'intval'); // 城市
        $address          = I('post.address', '', 'strval'); // 详细地址
        $remarks          = I('post.remark', '', 'strval'); // 备注
        $voucherType      = I('post.voucher_type', 1, 'intval'); //证件类型
        $affiliatesPerson = I('post.affiliates_person', '', 'strval'); //证件类型

        $annualBiz = new AnnualBiz();
        $res       = $annualBiz->activeVirtualCard($this->_sid, $virtualNo, $mobile, $idCard, $name, $province,
            $city, $address, $avatar, 0, $this->_memberId,'', '', $remarks, $voucherType,
            $affiliatesPerson);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 判断虚拟卡是否可激活
     * <AUTHOR>
     */
    public function checkVirtualCardCanBeActive()
    {
        $virtualNo = I('get.virtual_no', '', 'strval'); // 虚拟卡
        if (empty($virtualNo)) {
            $this->apiReturn(400, [], '参数缺失');
        }

        $model = new \Model\Product\AnnualCard();
        $card  = $model->getAnnualCard($virtualNo, 'virtual_no');
        $data  = (!empty($card) && $card['status'] == 0) ? 1 : 0;

        $this->apiReturn(200, $data, '');
    }

    /**
     * 分页获取异步任务列表
     * <AUTHOR> Li
     * @date  2020-07-07
     */
    public function getTaskList()
    {
        $startDate  = I('post.start_date', '0', 'strval');  // 下单开始时间
        $endDate    = I('post.end_date', '0', 'strval');    // 下单结束时间
        $identify   = I('post.identify', '', 'strval');     // 姓名或手机号
        $lid        = I('post.lid', 0, 'intval');         // 景区id
        $tid        = I('post.tid', 0, 'intval');         // 门票id
        $ordermode  = I('post.ordermode', '');   // 销售渠道
        $paymode    = I('post.paymode', '');     // 支付方式
        $status     = I('post.status', -1, 'intval');      // 任务状态  -1 全部  0 下单中 1已完成
        //$taskType   = I('post.task_type', -1, 'intval');      // 任务类型  -1 全部  0下单 1导入 2延期
        $operatorId = I('post.operator_id', 0, 'intval'); // 操作员工
        $page       = I('post.page', 1, 'intval'); // 当前页数
        $size       = I('post.size', 10, 'intval'); // 每页条数
        $taskType   = 0;//默认该接口的任务类型为下单

        $startTime = 0;
        $endTime   = 0;
        if (strtotime($startDate) && strtotime($endDate)) {
            $startTime = strtotime($startDate);
            $endTime   = strtotime($endDate);
        }

        $taskBiz = new \Business\Product\AnnualCardTask();
        $list    = $taskBiz->getTaskList($this->_sid, $startTime, $endTime, $identify, $lid, $tid, $paymode, $ordermode,
            $status, $taskType, $operatorId, $page, $size);

        $this->apiReturn(200, $list['data'], 'success');
    }

    /**
     * 获取异步任务详情列表
     * <AUTHOR> Li
     * @date  2020-07-08
     */
    public function getTaskDetailList()
    {
        $taskIds = I('post.task_id', '', 'strval');  // 任务id
        $page    = I('post.page', 1, 'intval'); // 当前页数
        $size    = I('post.size', 10, 'intval'); // 每页条数

        if (!$taskIds) {
            $this->apiReturn(203, [], '请选择需要导出的任务');
        }

        $taskIdArr = explode(',', $taskIds);
        $taskBiz   = new \Business\Product\AnnualCardTask();
        $list      = $taskBiz->getTaskDetailList($this->_sid, $taskIdArr, $page, $size);

        $this->apiReturn(200, $list['data'], 'success');
    }

    /**
     * 导出异步任务详情列表
     * <AUTHOR> Li
     * @date  2020-07-08
     */
    public function exportTaskDetailList()
    {
        $taskIds = I('post.task_ids', '1', 'strval');  // 任务id 多个以逗号隔开

        if (!$taskIds) {
            $this->apiReturn(203, [], '请选择需要导出的任务');
        }

        $taskIdArr = explode(',', $taskIds);
        $taskBiz   = new \Business\Product\AnnualCardTask();
        $list      = $taskBiz->exportTaskDetailList($this->_sid, $taskIdArr);

        $this->apiReturn(200, $list['data'], 'success');
    }

    /**
     * 分页获取批量操作任务列表
     * <AUTHOR> Li
     * @date  2022-06-22
     */
    public function getBatchTaskList()
    {
        $startDate  = I('post.start_date', '0', 'strval');  // 下单开始时间
        $endDate    = I('post.end_date', '0', 'strval');    // 下单结束时间
        $status     = I('post.status', -1, 'intval');      // 任务状态  -1 全部  0 下单中 1已完成
        $taskType   = I('post.task_type', -1, 'intval');      // 任务类型  -1全部 1导入 2延期 3年卡人脸短信 4禁用 5恢复
        $operatorId = I('post.operator_id', 0, 'intval'); // 操作员工
        $page       = I('post.page', 1, 'intval'); // 当前页数
        $size       = I('post.size', 10, 'intval'); // 每页条数

        $startTime = 0;
        $endTime   = 0;
        if (strtotime($startDate) && strtotime($endDate)) {
            $startTime = strtotime($startDate);
            $endTime   = strtotime($endDate);
        }

        $taskBiz = new \Business\Product\AnnualCardTask();
        $list    = $taskBiz->getBatchTaskList($this->_sid, $startTime, $endTime, $status, $taskType, $operatorId, $page, $size);

        $this->apiReturn(200, $list['data'], 'success');
    }

    /**
     * 获取批量操作详情列表
     * <AUTHOR> Li
     * @date  2022-06-22
     */
    public function getBatchTaskDetailList()
    {
        $taskIds   = I('post.task_id', '', 'strval');  // 任务id
        $dname     = I('post.dname', '', 'strval');  // 会员名称
        $mobile    = I('post.mobile', '', 'strval');  // 会员手机号
        $virtualNo = I('post.virtual_no', '', 'strval');  // 虚拟卡号
        $status    = I('post.status', -1, 'intval');      // 任务状态  -1全部 1成功 2失败
        $page      = I('post.page', 1, 'intval'); // 当前页数
        $size      = I('post.size', 10, 'intval'); // 每页条数
        $physicsNo = I('post.physics_no', '', 'strval'); //物理卡号 十进制
        $cardNo    = I('post.card_no', '', 'strval'); //实体卡号
        if (!$taskIds) {
            $this->apiReturn(203, [], '请选择需要导出的任务');
        }
        $taskIdArr = explode(',', $taskIds);
        $taskBiz   = new \Business\Product\AnnualCardTask();
        $extParams = [
            'physicsNo' => $physicsNo,
            'cardNo' => $cardNo,
        ];
        $list      = $taskBiz->getBatchTaskDetailList($this->_sid, $taskIdArr, $page, $size, $dname, $mobile, $virtualNo, $status, $extParams);
        $this->apiReturn(200, $list['data'], 'success');
    }

    /**
     * 获取年卡节假日常量
     *
     * @date 2024/04/29
     * @auther yangjianhui
     * @return array
     */
    public function getAnnualHolidayLimitConst()
    {
        $data = AnnualCardConst::HOLIDAY_MAP;

        $this->apiReturn(200, $data, 'success');
    }
}
