<?php

namespace Controller\Product;

use Business\Authority\DataAuthLogic;
use Business\CommodityCenter\Land;
use Business\Hotel\Intranet\RatePlan;
use Business\Hotel\Intranet\RoomType;
use Business\JavaApi\Ticket\TicketPrice;
use Business\Member\Member;
use Business\MemberLogin\MemberLoginHelper;
use Business\NewJavaApi\DistributionCenter\ProductIndexQueryClient;
use Business\Product\SubProduct as SubProductBiz;
use Business\Product\Ticket;
use Exception;
use Library\Controller;
use Business\Product\NewProductList as BusinessNewProductList;

class NewProductList extends Controller
{
    private $_memberId;
    private $_sid;
    private $_type;
    private $_loginMember;

    public function __construct()
    {
        parent::__construct();

        $loginInfo = $this->getLoginInfo('ajax');
        $this->_memberId = $loginInfo['memberID'];
        $this->_sid = $loginInfo['sid'];
        $this->_type = $loginInfo['dtype'];
        $this->_loginMember = $loginInfo;
    }

    /**
     * 获取预定列表
     */
    public function getList()
    {
        $page = I('currentPage', 1, 'intval') ?: 1;
        $pageSize = I('pageSize', 10, 'intval') ?: 10;
        $productName = I('product_name', null, 'strval,trim') ?: null;
        $productType = I('product_type', null, 'strval,trim') ?: null;
        $productTopic = I('product_topic', null, 'strval,trim') ?: null;
        $province = I('province', null, 'intval') ?: null;
        $city = I('city', null, 'intval') ?: null;
        $oversea = I('oversea', null);
        $oversea = $oversea === '' ? null : intval($oversea);
        $applyDid = I('apply_did', null, 'intval') ?: null; // 计调下单时传，代下单商户ID（当前登录商户）
        $accountId = I('account_id', null, 'intval') ?: null; // 下单商户ID
        $supplierName = I('supplier_name', null, 'strval,trim') ?: null; //供应商名称

        [$saleChannel, $sid, $fid] = $this->getDistributionParams($applyDid, $accountId);

        // 子商户
        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        try {
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        } catch (Exception $e) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '查询数据权限出错');
            return;
        }
        $condition = $dataAuthLimit->transInOrNotCondition();
        // 全禁的情况下不需要调用接口
        if ($condition === false) {
            $this->apiReturn(self::CODE_SUCCESS, ['list' => [], 'total' => 0, 'pages' => 0]);
        }

        // 产品类型拆分为产品类型和子产品类型处理
        [$productType, $subType] = SubProductBiz::decodeTypeAndSubType($productType);
        if ($subType) {
            $condition['subType'] = $subType;
        } elseif ($productType) { // 产品类型不为空，子产品类型为空，设置子产品类型为0
            $condition['subType'] = 0;
        }

        $options = [
            'fid'                    => $fid,
            'queryTotal'             => ProductIndexQueryClient::QUERY_TOTAL_ALL_PAGE,
            'pageNum'                => $page,
            'pageSize'               => $pageSize,
            'channel'                => $saleChannel,
            'city'                   => $city,
            'oversea'                => $oversea,
            'productName'            => $productName,
            'productType'            => $productType,
            'province'               => $province,
            'queryBasePrice'         => ProductIndexQueryClient::QUERY_BASE_PRICE_LAST,
            'queryDistributionPrice' => ProductIndexQueryClient::QUERY_DISTRIBUTION_PRICE_YES,
            'sids'                   => $sid ? [$sid] : null,
            'supplierName'           => $supplierName,
            'topic'                  => $productTopic,
            'subSid'                 => $subSid,
            'poiIdList'              => $condition['lidList'],
            'notPoiIdList'           => $condition['notLidList'],
            'subType'                => $condition['subType'] ?? null,
        ];
        try {
            $result = ProductIndexQueryClient::querySaleProductPageList($options);
        } catch (Exception $e) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '查询产品列表出错');
            return;
        }
        if (empty($result['rows'])) {
            $this->apiReturn(self::CODE_SUCCESS, ['list' => [], 'total' => 0, 'pages' => 0]);
            return;
        }
        $list = $result['rows'];

        $poiProductTypes = ['N'];
        $hotelList = BusinessNewProductList::handHotelProducts(array_filter($list, function ($item) use ($poiProductTypes) {
            return in_array($item['productType'], $poiProductTypes);
        }), $this->_memberId);
        $mapHotelList = array_column($hotelList, null, 'poi_id_sid');

        $landList = BusinessNewProductList::handleSecondLevelProducts(array_filter($list, function ($item) use ($poiProductTypes) {
            return !in_array($item['productType'], $poiProductTypes);
        }));
        $mapLandList = array_column($landList, null, 'lid_aid');

        foreach ($list as &$item) {
            if (in_array($item['productType'], $poiProductTypes)) {
                $item = $mapHotelList[$item['itemId'] . '_' . $item['sid']] ?? [];
                unset($item['poi_id_sid']);
            } else {
                $item = $mapLandList[$item['itemId'] . '_' . $item['sid']] ?? [];
                unset($item['lid_aid']);
            }
        }

        $this->apiReturn(self::CODE_SUCCESS, [
            'list'  => $list,
            'total' => $result['total'],
            'pages' => ceil($result['total'] / $pageSize)
        ]);
    }

    public function loadRoomTypes(): void
    {
        $poiId = I('poi_id', null, 'intval') ?: null;
        $applyDid = I('apply_did', null, 'intval') ?: null; // 计调下单时传，代下单商户ID（当前登录商户）
        $accountId = I('account_id', null, 'intval') ?: null; // 下单商户ID
        $ratePlanCode = I('rate_plan_code', null, 'strval,trim') ?: null;
        $ratePlanName = I('rate_plan_name', null, 'strval,trim') ?: null;
        $sid = I('sid', null, 'intval') ?: null; // 指定供应商，非计调下单时可用

        [$saleChannel, $sid, $fid] = $this->getDistributionParams($applyDid, $accountId, $sid);

        try {
            $result = ProductIndexQueryClient::querySaleHotelRoomList([
                'poiId'         => $poiId,
                'sid'           => $sid ?: null,
                'fid'           => $fid,
                'productIdCode' => $ratePlanCode,
                'skuName'       => $ratePlanName,
                'channel'       => $saleChannel,
            ]);
        } catch (Exception $e) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '查询出错: ' . $e->getMessage());
            return;
        }
        if (empty($result['rows'])) {
            $this->apiReturn(self::CODE_SUCCESS);
            return;
        }
        $rooms = $result['rows'];

        $roomTypeIds = array_column($rooms, 'roomTypeId');
        $sidList = $skuIds = [];
        foreach ($rooms as $roomType) {
            foreach ($roomType['skus'] as $sku) {
                $sidList[] = $sku['sid'];
                $skuIds[] = $sku['skuId'];
            }
        }
        $roomTypes = (new RoomType())->listByIds($roomTypeIds);
        $mapRoomTypes = array_key($roomTypes, 'id');
        $ratePlans = (new RatePlan())->listByIds($skuIds);
        $mapRatePlans = array_key($ratePlans, 'centre_sku_id');
        $mapMemberList = (new Member())->getList($sidList);

        $resRoomTypes = [];
        foreach ($rooms as $room) {
            $roomTypeInfo = $mapRoomTypes[$room['roomTypeId']] ?? [];
            $resRoomTypes[] = BusinessNewProductList::formatRoomType($room, $roomTypeInfo, $mapRatePlans, $mapMemberList);
        }

        $this->apiReturn(self::CODE_SUCCESS, $resRoomTypes);
    }

    public function loadRatePlans(): void
    {
        $applyDid = I('apply_did', null, 'intval') ?: null; // 计调下单时传，代下单商户ID（当前登录商户）
        $accountId = I('account_id', null, 'intval') ?: null; // 下单商户ID
        $roomTypeId = I('room_type_id', null, 'intval') ?: null;
        $sid = I('sid', null, 'intval') ?: null; // 指定供应商，非计调下单时可用
        $ratePlanCode = I('rate_plan_code', null, 'strval,trim') ?: null;
        $ratePlanName = I('rate_plan_name', null, 'strval,trim') ?: null;

        [$saleChannel, $sid, $fid] = $this->getDistributionParams($applyDid, $accountId, $sid);

        $spuId = $skuId = null;
        if ($ratePlanCode) {
            $arr = explode('|', $ratePlanCode);
            if (count($arr)) {
                $this->apiReturn(self::CODE_SUCCESS);
            }
            [$ratePlanCodeSid, $spuId, $skuId] = $arr;
            if ($sid && $ratePlanCodeSid != $sid) {
                $this->apiReturn(self::CODE_SUCCESS);
            }
            $sid = $ratePlanCodeSid;
        }

        try {
            $result = ProductIndexQueryClient::querySaleSkuList([
                'fid'                    => $fid,
                'sid'                    => $sid,
                'roomTypeId'             => $roomTypeId,
                'queryBasePrice'         => ProductIndexQueryClient::QUERY_BASE_PRICE_LAST,
                'queryDistributionPrice' => ProductIndexQueryClient::QUERY_DISTRIBUTION_PRICE_YES,
                'spuId'                  => $spuId,
                'skuId'                  => $skuId,
                'skuName'                => $ratePlanName,
                'channel'                => $saleChannel,
            ]);
        } catch (Exception $e) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '查询出错: ' . $e->getMessage());
            return;
        }
        if (empty($result['rows'])) {
            $this->apiReturn(self::CODE_SUCCESS);
            return;
        }
        $skus = $result['rows'];

        $sidList = array_column($skus, 'sid');
        $ratePlanIds = array_column($skus, 'skuId');

        $mapMemberList = (new Member())->getList($sidList);
        $ratePlans = (new RatePlan())->listByIds($ratePlanIds);
        $mapRatePlans = array_key($ratePlans, 'centre_sku_id');

        $ratePlans = [];
        foreach ($skus as $sku) {
            $ratePlanInfo = $mapRatePlans[$sku['skuId']] ?? [];
            $ratePlans[] = BusinessNewProductList::formatRatePlan($sku, $ratePlanInfo, $mapMemberList);
        }

        $this->apiReturn(self::CODE_SUCCESS, $ratePlans);
    }

    public function getTickets()
    {
        $applyDid = I('apply_did', null, 'intval') ?: null; // 计调下单时传，代下单商户ID（当前登录商户）
        $accountId = I('account_id', null, 'intval') ?: null; // 下单商户ID
        $ticketIds = I('ticket_ids', null, 'strval,trim') ?: null;

        [$saleChannel, $sid, $fid] = $this->getDistributionParams($applyDid, $accountId);

        $ticketIds = explode(',', $ticketIds);
        if (empty($ticketIds) || !is_array($ticketIds)) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], 'ticket_ids 参数错误');
            return;
        }

        try {
            $result = ProductIndexQueryClient::querySaleSkuList([
                'fid'                    => $fid,
                'sid'                    => $sid,
                'queryBasePrice'         => ProductIndexQueryClient::QUERY_BASE_PRICE_LAST,
                'queryDistributionPrice' => ProductIndexQueryClient::QUERY_DISTRIBUTION_PRICE_YES,
                'channel'                => $saleChannel,
                'skuIds'                 => $ticketIds,
            ]);
        } catch (Exception $e) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '查询出错: ' . $e->getMessage());
            return;
        }
        if (empty($result['rows'])) {
            $this->apiReturn(self::CODE_SUCCESS);
            return;
        }
        $skus = $result['rows'];

        $ticketInfoList = (new \Business\CommodityCenter\Ticket())->queryTicketInfoByIds($ticketIds);
        $mapTicketInfoList = [];
        foreach ($ticketInfoList as $ticketInfo) {
            $mapTicketInfoList[$ticketInfo['ticket']['id']] = $ticketInfo;
        }

        // 查询标签
        $mapTags = (new Ticket())->getTicketTagByIds($ticketIds, 5);

        // 根据ticketIds 查询返回当前时间有效期,有配置多个价格的票列表
        $calendar = (new TicketPrice())->queryReturnConfigMuchPriceTicketId($ticketIds);
        $dayPriceList = $calendar['data'] ?? [];

        $data = [];
        foreach ($skus as $sku) {
            $ticketInfo = $mapTicketInfoList[$sku['skuId']] ?? [];
            $data[] = BusinessNewProductList::formatTicket($ticketInfo, $sku, $mapTags, $dayPriceList);
        }

        $this->apiReturn(self::CODE_SUCCESS, $data);
    }

    private function getDistributionParams(?int $applyDid, ?int $accountId, int $sid = null): array
    {
        $isTravelCoordinatorOrder = in_array($applyDid, [$this->_sid, $this->_memberId]) &&
            !in_array($accountId, [$this->_sid, $this->_memberId]) && // 防止员工账户被误判计调下单
            $applyDid != $accountId;
        if ($isTravelCoordinatorOrder) {
            $saleChannel = 12;
            $sid = $this->_sid;
            $fid = $accountId;
        } else {
            $saleChannel = 5;
            $fid = $this->_sid;
            if (is_null($sid) && !is_null($applyDid)) {
                $sid = $applyDid;
            }
        }
        return [$saleChannel, $sid, $fid];
    }
}