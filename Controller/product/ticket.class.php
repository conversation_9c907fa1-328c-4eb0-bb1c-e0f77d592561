<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 5/5-005
 * Time: 16:16
 */

namespace Controller\product;

use Business\Authority\DataAuthLogic;
use Business\CommodityCenter\Ticket as ticketBiz;
use Business\JavaApi\Context\Product\TicketReserveStorageContext;
use Business\JavaApi\Product\ReserveStorage;
use Business\JavaApi\TicketApi;
use Business\MemberLogin\MemberLoginHelper;
use Business\Product\HandleTicket as businessLib;
use Business\Product\SubProduct as SubProductBiz;
use Library\Controller;
use Business\JavaApi\StorageApi;

class ticket extends Controller
{
    private $memberID;
    private $staffId;
    private $_operatorId;
    private $_businessLib;

    /**
     * @var \Model\Product\Ticket $ticketObj
     */
    private $ticketObj;

    public function __construct()
    {
        $loginInfo = $this->getLoginInfo();

        $this->memberID     = $loginInfo['sid'];
        $this->staffId      = $loginInfo['memberID'];
        $this->ticketObj    = new \Model\Product\Ticket();
        $this->_operatorId  = $loginInfo['memberID'];
        $this->_businessLib = new businessLib($this->memberID, $this->_operatorId);
    }

    /**
     * 获取子票信息-填写订单页面
     */
    public function packTicketInfo()
    {
        $token       = I('get.token');
        $appid       = I('get.appid');
        $tid         = I('get.tid', 0, 'intval');
        $pid         = I('get.pid', 0, 'intval');
        $sign        = I('get.signature');
        $aid         = I('get.aid', 0, 'intval');
        $tnum        = I('get.tnum', 0, 'intval');
        $beginTime   = I('get.beginTime', '', 'trim');
        $session_sid = I('get.memberSID', 0, 'intval');

        if ($sign != md5($token . $appid . $pid)) {
            exit('{"status":0,"msg":"Access Denied2!"}');
        }

        $packBiz  = new \Business\Product\PackTicket();
        $packInfo = $packBiz->getChildTickets($tid);
        $tipMsg   = '';

        foreach ($packInfo as $key => $prod) {
            $c_msg = '';
            $c_m   = '';
            if (isset($child_invalid_msg[$prod['pid']])) {
                $c_msg  = '<p class="pt-invalid-msg">' . $child_invalid_msg[$prod['pid']] . '</p>';
                $c_m    = $child_invalid_msg[$prod['pid']];
                $tipMsg .= $c_m;
            }
            $tickets[] = [
                'pid'     => $prod['pid'],
                'l_title' => $prod['ltitle'],
                't_title' => $prod['ttitle'],
                'l_img'   => $prod['imgpath'],
                'num'     => $prod['num'],
                'msg'     => $c_msg,
                'm'       => $c_m,
            ];
        }
        echo json_encode(['code' => 200, 'list' => $tickets, 'msg' => $tipMsg]);
    }

    /**
     * 设置总库存
     *
     * <AUTHOR> Chen
     * @date   2016-10-11
     *
     * post.storage_open int 总库存启用时间
     * post.total_storage int 总库存
     * post.tid int 门票id
     * post.pid int 门票pid
     */
    public function SetTotalStorage()
    {
        $total_storage = I('post.total_storage');
        $storage_open  = I('post.storage_open');
        $tid           = I('post.tid') + 0;
        if ($total_storage < -1 || !is_numeric($_POST['total_storage'])) {
            parent::apiReturn(1077, [], '总库存必须为大于等于');
        }
        if (!checkData($storage_open)) {
            parent::apiReturn(1077, [], '库存启用日期格式错误');
        }
        if (!$tid) {
            parent::apiReturn(1077, [], '产品ID格式错误');
        }
        $sid = $this->memberID;
        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        if ($total_storage == -1) {
            $res = StorageApi::closeTotalStorage($tid, $sid, $this->staffId, $subSid);
        } else {
            $storageInfo = StorageApi::getTotalStorage($tid);
            if (empty($storageInfo) || $storageInfo['storage'] == -1 && $storageInfo['storage_open'] == null) {
                $res = StorageApi::addTotalStorage($tid, $total_storage, $storage_open, $sid,
                    $this->staffId, $subSid);

            } else {
                $res = StorageApi::openTotalStorage($tid, $total_storage, $storage_open, $sid,
                    $this->staffId, $subSid);

            }
        }

        if (!empty($res)) {
            parent::apiReturn(200, $res, '总库存设置成功');
        } else {
            parent::apiReturn(201, [], '总库存设置失败');
        }
    }

    /**
     * 设置日库存
     * <AUTHOR> Chen
     * @date   2016-10-11
     *
     * post.pid int uu_product.pid
     * post.price_section json list, eg:[{"id":"0","sdate":"2016-10-11","edate":"2016-10-11","js":"1","ls":"2","ptype":1,"storage":"3"},……]
     */
    public function SetDailyPrice()
    {
        $pid           = I('post.pid', 0, 'intval');
        $price_section = I('post.price_section', '', '');
        $price_section = json_decode($price_section, true);
        $jqModel       = new \Model\Product\Ticket();
        $tid           = $jqModel->getTid($pid);
        $ticketApi     = new TicketApi();

        $sid = $this->memberID;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        //设置操作员信息
        $ticketApi->setOperator($sid, $this->staffId);

        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        $res    = $ticketApi->setDailyPrice($pid, $tid, $price_section, $subSid);
        $result = $this->_businessLib->getTickets($tid, 0);

        $returnRes['ext']                    = [];
        $returnRes['ext']['price']           = [];
        $returnRes['ext']['reserve_storage'] = [];

        //结算价高于零售价
        if ($res['code'] == 50051) {
            $returnRes['ext']['price'] = $res['data'];
        }

        // 不需要游玩日期才存在预约库存
        if ($result[1]['ticket']['pre_sale'] == 1) {
            $ticketReserveStorageContextArray = [];
            $reserveStorageService            = new ReserveStorage();
            foreach ($price_section as $priceSectionKey => $priceSectionKeyValue) {
                $ticketReserveStorageContext = new TicketReserveStorageContext([
                    'tid'     => $tid,
                    'start'   => $priceSectionKeyValue['sdate'],
                    'end'     => $priceSectionKeyValue['edate'],
                    'channel' => 0,
                ]);
                $reserveStorageResult        = $reserveStorageService->queryCalendarReserveStorage($ticketReserveStorageContext);

                if ($reserveStorageResult['code'] == 200) {
                    // pType 0: 时间段 1: 日历
                    $sectionFlag = $reserveStorageResult['data'][0]['sectionFlag'] ?? 0;
                    $pType       = $reserveStorageResult['data'][0]['pType'] ?? 0;

                    if ($sectionFlag == 1) {
                        $returnRes['ext']['reserve_storage'][] = [
                            'code' => '204',
                            'data' => [
                                'tid' => $tid,
                                'id'  => $priceSectionKeyValue['id'],
                            ],
                            'msg'  => '已开启预约分时',
                        ];
                        continue;
                    }
                    if ($pType == 0 || count($reserveStorageResult['data']) == 0) {
                        $ticketReserveStorageContextArray[] = new TicketReserveStorageContext([
                            'pType'      => 1,
                            'tid'        => $tid,
                            'startDate'  => $priceSectionKeyValue['sdate'],
                            'endDate'    => $priceSectionKeyValue['edate'],
                            'storage'    => $priceSectionKeyValue['reserve_storage'],
                            'operaterId' => $this->memberID,
                        ]);
                    } else {
                        $ticketReserveStorageContextArray[] = new TicketReserveStorageContext([
                            'id'         => $reserveStorageResult['data'][0]['id'],
                            'startDate'  => $priceSectionKeyValue['sdate'],
                            'endDate'    => $priceSectionKeyValue['edate'],
                            'storage'    => $priceSectionKeyValue['reserve_storage'],
                            'operaterId' => $this->memberID,
                        ]);
                    }
                } else {
                    parent::apiReturn($reserveStorageResult['code'], ['request' => $ticketReserveStorageContext],
                        $reserveStorageResult['msg']);
                }
            }
            if (!empty($ticketReserveStorageContextArray)) {
                $result = $reserveStorageService->saveOrUpdate($ticketReserveStorageContextArray, $subSid);
                if ($result['code'] != 200) {
                    $returnRes['ext']['reserve_storage'][] = [
                        'code' => $result['code'],
                        'data' => ['request' => $ticketReserveStorageContextArray],
                        'msg'  => $result['msg'] . '或数据为空',
                    ];
                }
            }
        }

        parent::apiReturn($res['code'], $returnRes, $res['msg']);
    }

    public function remove_price()
    {
        $pid  = I('pid', 0, 'intval');
        $id   = I('id', 0, 'intval');
        $list = I('price_list', '', 'strval');
        $sid  = $this->memberID;

        if (!$sid || !$pid || (!$id && !$list)) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $ticketBiz = new TicketApi();
        //设置操作员信息
        $ticketBiz->setOperator($this->memberID, $this->staffId);
        $returnRes['ext']                    = [];
        $returnRes['ext']['reserve_storage'] = [];
        $returnRes['ext']['price']           = [];
        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }
        if ($id) {
            //删除区间段价格
            $priceResult = $ticketBiz->delRangePrice([$id], $subSid);
        } else {
            $list        = json_decode($list, true);
            $idArr       = array_column($list, 'rid');
            $priceResult = $ticketBiz->delDailyPrice($idArr, $subSid);
        }
        if (isset($priceResult['code'])) {
            if ($priceResult['code'] == 200) {
                $returnRes['ext']['price'] = $this->responseFormat(200, '设置日历价格操作成功', []);
            } else {
                $returnRes['ext']['price'] = $this->responseFormat($priceResult['code'], '未设置日历价格，无须还原',
                    [$priceResult['data']]);
            }
        } else {
            $this->apiReturn(204, [], '设置日历价格的接口异常');
        }

        $jqModel = new \Model\Product\Ticket();
        $tid     = $jqModel->getTid($pid);
        $result  = $this->_businessLib->getTickets($tid, 0);

        // 不需要游玩日期才存在预约库存
        if ($result[1]['ticket']['pre_sale'] == 1) {
            $ticketReserveStorageContextArray = [];
            $reserveStorageService            = new ReserveStorage();
            foreach ($list as $priceSectionKey => $priceSectionKeyValue) {
                $ticketReserveStorageContext = new TicketReserveStorageContext([
                    'tid'     => $tid,
                    'start'   => $priceSectionKeyValue['sdate'],
                    'end'     => $priceSectionKeyValue['edate'],
                    'channel' => 0,
                ]);
                $reserveStorageResult        = $reserveStorageService->queryCalendarReserveStorage($ticketReserveStorageContext);
                if ($reserveStorageResult['code'] == 200) {
                    $pType = $reserveStorageResult['data'][0]['pType'] ?? 0;
                    // pType 0: 时间段 1: 日历
                    if ($pType == 1 && count($reserveStorageResult['data']) > 0) {
                        $ticketReserveStorageContextArray[] = new TicketReserveStorageContext([
                            'id'            => $reserveStorageResult['data'][0]['id'],
                            'startDate'     => $priceSectionKeyValue['sdate'],
                            'endDate'       => $priceSectionKeyValue['edate'],
                            'storageStatus' => 1,
                            'operaterId'    => $this->memberID,
                        ]);
                    }
                } else {
                    parent::apiReturn($reserveStorageResult['code'], ['request' => $ticketReserveStorageContext],
                        $reserveStorageResult['msg']);
                }
            }
            if (!empty($ticketReserveStorageContextArray)) {
                $result = $reserveStorageService->saveOrUpdate($ticketReserveStorageContextArray, $subSid);
                if ($result['code'] != 200) {
                    $returnRes['ext']['price'][] = $this->responseFormat($result['code'], '请求预约库存接口失败',
                        ['request' => $ticketReserveStorageContextArray, $result['msg']]);

                }
            }
        }
        parent::apiReturn(200, $returnRes, '操作成功');
    }

    /**
     * 总库存 ajax 返回
     * <AUTHOR>
     * @date    17-08-22
     *
     * @param  int  $tid
     *
     * @return ajax
     *
     */
    public function storageListAjax()
    {
        $tid = I('post.tid', 0, 'intval');

        if (!$tid) {
            $this->apiReturn('204', '', '参数有误');
        }

        // 获取库存数据
        $storageInfo = StorageApi::getTotalStorage($tid);

        if (empty($storageInfo)) {
            $this->apiReturn('204', '', '未配置总库存');
        } else {
            if (isset($storageInfo['openDate']) && $storageInfo['openDate']) {
                $timeStamp               = substr($storageInfo['openDate'], 0, 10);
                $storageInfo['openDate'] = date('Y-m-d', $timeStamp);
            } else {
                $storageInfo['openDate'] = '';
            }
            if (isset($storageInfo['storage'])) {
                if ($storageInfo['storage'] == -1) {
                    $storageInfo['storage'] = "";
                }
            }
            $this->apiReturn('200', $storageInfo, '数据取得成功');
        }
    }

    /**
     * 获取景区的门票分组
     * <AUTHOR> Chen
     * @date   2017-09-30
     */
    public function getTicketGroupConfig()
    {
        $landId     = I('get.lid', 0, 'intval');
        $configList = $this->ticketObj->getTicketGroupSetByLid([$landId]);
        $code       = parent::CODE_NO_CONTENT;
        if (!empty($configList)) {
            $code = 200;
        }

        parent::apiReturn($code, $configList[$landId], '');
    }

    /**
     * 保存景区的门票分组
     * <AUTHOR> Chen
     * @date   2017-09-29
     */
    public function saveTicketGroupConfig()
    {
        $landId    = I('post.lid', 0, 'intval');
        $groupName = I('post.group_name', 'safe_str');
        $groupId   = I('post.group_id', 0, 'intval');
        $delete    = I('post.delete', 0, 'intval');
        if ($delete) {
            $deleteResult = $this->ticketObj->deleteTicketGroup($landId, $groupId);
            if ($deleteResult == 1) {
                parent::apiReturn(200, [], '删除成功');
            } elseif ($deleteResult == -1) {
                parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '删除失败 本分组存在票 无法删除');
            }
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '删除失败请检查');
        }
        if (!$landId) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '景区ID参数错误');
        }
        if (empty($groupName)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '分组名称不能为空');
        }
        $res = $this->ticketObj->saveTicketGroupConfig($landId, $groupName, $groupId);
        if ($res) {
            parent::apiReturn(200, [], '保存成功');
        } elseif ($res === false) {
            parent::apiReturn(parent::CODE_CREATED, [], '分组名称已经存在');
        }
        parent::apiReturn(parent::CODE_CREATED, [], '保存失败');
    }

    /**
     * 获取票类分组
     * <AUTHOR>
     * @DateTime 2017-10-10T11:55:53+0800
     * @return   [type] [description]
     */
    public function getTicketGroup()
    {
        $landId = I('get.lid', 0, 'intval');
        $groups = $this->ticketObj->getTicketGroupByLid($landId);
        $code   = parent::CODE_NO_CONTENT;
        if (!empty($groups)) {
            $code = 200;
        }

        parent::apiReturn($code, $groups, '');
    }

    /**
     * 保存门票分组配置
     * <AUTHOR> CHEN
     * @date   2017-09-29
     */
    public function saveTicketGroup()
    {
        $data = I("post.ticketGroupData");
        if (!is_array($data)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], 'ticketGroup不是复合参数');
        }
        try {
            $data = $this->validateAge($data);
        } catch (\Exception $e) {
            parent::apiReturn(500, [], $e->getMessage());
        }
        if ($data === false) {
            parent::apiReturn(500, [], '年龄段不能重复');
        }
        $invalidArr = [];
        foreach ($data as $value) {
            list($landId, $ticketId, $minAge, $maxAge, $groupId) = $value;
            $res = $this->ticketObj->saveTicketGroup($landId, $ticketId, $groupId, $minAge, $maxAge);
            if ($res === false) {
                $item['msg']  = "数据库写入失败";
                $invalidArr[] = $item;
            }
        }
        if ($invalidArr) {
            if (count($invalidArr) == count($data)) {
                $msg = "全部保存失败";
            } else {
                $msg = "部分保存失败";
            }
            parent::apiReturn(parent::CODE_CREATED, $invalidArr, $msg);
        }
        parent::apiReturn(200, [], '保存成功');
    }

    /**
     * 根据景区ID获取门票ID
     */
    public function getTicketIdByLandId()
    {
        $landId      = I('post.land_id');
        $ticketModel = new \Model\Product\Ticket();
        //$res         = $ticketModel->getTicketInfoByLandId($landId, 'id, title, status');
        $javaApi = new ticketBiz();
        $res     = $javaApi->queryTicketBylandIdAndPay([$landId], 'id, title, status');

        $statusConfig = [
            1 => '上架',
            2 => '下架',
        ];

        $data = [];
        foreach ($res as $item) {
            if (!isset($statusConfig[$item['status']])) {
                continue;
            }
            $status = $statusConfig[$item['status']];
            $data[] = [
                'id'     => $item['id'],
                'title'  => $item['title'],
                'status' => $status,
            ];
        }

        $this->apiReturn(200, $data, []);
    }

    public function validateAge($post_data)
    {
        $data = [];
        foreach ($post_data as $value) {
            $item = explode(",", $value);
            if (count($item) != 5) {
                $item['msg']  = "数据不完整";
                $invalidArr[] = $item;
                continue;
            }
            $data[] = $item;
        }

        $dataAge      = [];
        $empty_number = 0;
        foreach ($data as $key => $value) {
            $minAge = explode("|", $value[2]);
            $maxAge = explode("|", $value[3]);
            for ($i = 0; $i < count($minAge); $i++) {
                // 有一个为空的就提示不能
                if (empty($minAge[$i]) && empty($maxAge[$i])) {
                    if ($value[4] == "-1") {
                        $dataAge[$value[4]]['count'] = 0;
                    }
                    $dataAge[$value[4]]['count']++;
                }
                $dataAge[$value[4]][] = [$minAge[$i], $maxAge[$i]];
            }
        }
        foreach ($dataAge as $key => $value) {
            if ($key == -1) {
                continue;
            }
            if ($value['count'] > 1 && !empty($value['count'])) {
                throw new \Exception("最多只能有一个年龄为空 请补充年龄段");
            }
            $this->checkCrossNumber($value);
        }

        return $data;
    }

    /**
     * 判断数值之间是否有交集
     * <AUTHOR>
     * @dateTime 2017-11-08T09:57:18+0800
     * @throws   Exception                              可能抛出异常
     */
    private function checkCrossNumber($dataAge)
    {
        //判断是否是区域有交集
        foreach ($dataAge as $value) {
            foreach ($dataAge as $value2) {
                $radius1 = ($value[1] - $value[0]) / 2;
                $radius2 = ($value2[1] - $value2[0]) / 2;
                $center1 = $value[1] - $radius1;
                $center2 = $value2[1] - $radius2;
                if ($center1 == $center2 && $radius1 == $radius2) {
                    continue;
                }
                if (abs($center1 - $center2) < ($radius1 + $radius2)) {
                    throw new \Exception("年龄段有交集，请检查年龄段！");
                }
            }
        }
    }

    /**
     * 保存套票时候的提交的检验和保存在缓存中
     *
     * @param  string json package_data
     *
     * @return string json
     *
     */
    public function savePackTicketCache()
    {
        $packageData = I('POST.package_data');
        $delTickets  = I('POST.del_tickets');
        $key         = I('POST.flag', '');

        $data                 = [];
        $data['package_data'] = $packageData;
        if (!empty($delTickets)) {
            foreach ($delTickets as $value) {
                $delData[] = [
                    'lid'        => 0,
                    'tid'        => intval($value),
                    'pid'        => 0,
                    'aid'        => 0,
                    'num'        => -1,
                    'cost_price' => 0,
                ];
            }
            $data['del_ticket'] = $delData;
        }
        if ($key) {
            //过滤一下删除操作
            if ($data['del_ticket']) {
                $data['del_ticket'] = $this->_delPackTicketUnique($key, $data['del_ticket']);
            }
        } else {
            $key = MD5(uniqid("", true));
        }

        $packageDataJson = json_encode($data);
        $packBiz         = new \Business\Product\PackTicket();
        $ret             = $packBiz->checkPackDataV2($packageDataJson);

        if ($ret === true) {
            $packBiz->rmCache($key);
            // $res = $packBiz->setCache($packageDataJson, $this->staffId);
            $res = $packBiz->setCache($packageDataJson, $key);

            //是演出子票验证,打包子票只允许未捆绑有其他门票的演出票
            // $isValid     = $packBiz->checkShowBindForPackSonTicket($this->staffId);
            $isValid = $packBiz->checkShowBindForPackSonTicket($key);
            if ($isValid['code'] != 200) {
                // $packBiz->rmCache($this->staffId);//验证失败，报错移除缓存
                $packBiz->rmCache($this->staffId);//验证失败，报错移除缓存
                $this->apiReturn($isValid['code'], [], $isValid['msg']);
            }

            $this->apiReturn(200, ['flag' => $key], 'success');

        } else {
            $this->apiReturn(400, [], 'fail');
        }
    }

    /**
     * 保存套票时候的提交的检验和保存在缓存中 过滤已存在缓存中的删除子票操作
     * @date   2021-06-07
     * <AUTHOR>
     *
     * @param  string  $key
     * @param  array  $newData
     *
     * @return array
     *
     */
    private function _delPackTicketUnique($key, array $newData)
    {
        if (!$key) {
            return [];
        }
        $packBiz       = new \Business\Product\PackTicket();
        $res           = $packBiz->getCache($key);
        $packData      = json_decode($res, true);
        $delTicketData = $packData['del_ticket'] ?? [];
        if (!$newData) {
            return $delTicketData;
        }

        if (!$delTicketData) {
            return $newData;
        }
        $addData = [];
        foreach ($newData as $item) {
            $flag = false;
            foreach ($delTicketData as $old) {
                if ($old['tid'] == $item['tid']) {
                    $flag = true;
                    break;
                }
            }
            if (!$flag) {
                $addData[] = $item;
            }
        }
        $addData = array_merge($addData, $delTicketData);

        return $addData;
    }

    /**
     * 关闭预约
     *
     * @param  string json package_data
     *
     * @return array
     *
     */
    public function stopReserve()
    {
        $tid                              = I('post.ticket_id');
        $time                             = I('post.time');
        $ticketReserveStorageContextArray = [];
        $reserveStorageService            = new ReserveStorage();
        $result                           = $this->_businessLib->getTickets($tid, 0);
        // 不需要游玩日期才存在预约库存
        if ($result[1]['ticket']['pre_sale'] != 1) {
            $this->apiReturn(200, [], "需要游玩日期不存在预约库存");
        }
        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }
        foreach ($time as $timeKey => $timeValue) {

            $ticketReserveStorageContext = new TicketReserveStorageContext([
                'tid'     => $tid,
                'start'   => $timeValue,
                'end'     => $timeValue,
                'channel' => 0,
            ]);
            $reserveStorageResult        = $reserveStorageService->queryCalendarReserveStorage($ticketReserveStorageContext);
            if ($reserveStorageResult['code'] == 200) {
                // pType 0: 时间段 1: 日历
                $sectionFlag = $reserveStorageResult['data'][0]['sectionFlag'] ?? 0;
                $pType       = $reserveStorageResult['data'][0]['pType'] ?? 0;

                if ($sectionFlag == 1) {
                    $returnRes['ext']['reserve_storage'][] = [
                        'code' => '204',
                        'data' => ['tid' => $tid],
                        'msg'  => '已开启预约分时',
                    ];
                    continue;
                }
                if ($pType == 0 || count($reserveStorageResult['data']) == 0) {
                    $ticketReserveStorageContextArray[] = new TicketReserveStorageContext([
                        'pType'      => 1,
                        'tid'        => $tid,
                        'startDate'  => $timeValue,
                        'endDate'    => $timeValue,
                        'storage'    => -2,
                        'operaterId' => $this->memberID,
                    ]);
                } else {
                    $ticketReserveStorageContextArray[] = new TicketReserveStorageContext([
                        'id'         => $reserveStorageResult['data'][0]['id'],
                        'startDate'  => $timeValue,
                        'endDate'    => $timeValue,
                        'storage'    => -2,
                        'operaterId' => $this->memberID,
                    ]);
                }
            } else {
                $this->apiReturn($reserveStorageResult['code'], ['request' => $ticketReserveStorageContext],
                    $reserveStorageResult['msg']);
            }
        }
        if (!empty($ticketReserveStorageContextArray)) {
            $result = $reserveStorageService->saveOrUpdate($ticketReserveStorageContextArray, $subSid);
            if ($result['code'] != 200) {
                parent::apiReturn($result['code'], ['request' => $ticketReserveStorageContextArray], $result['msg']);
            }
        }
        $this->apiReturn(200, [], "停止预约保存成功");
    }

    /**
     * @param $code
     * @param  string  $msg
     * @param  array  $data
     *
     * @return array
     */
    private function responseFormat($code = 200, string $msg = 'success', array $data = [])
    {
        return [
            'code' => $code,
            'msg'  => $msg,
            'data' => $data,
        ];
    }

    /**
     * 获取延期票分页
     *
     * @param  string json package_data
     *
     * @return string json
     *
     */
    public function getPostponeTicketPage()
    {
        $keyword           = I('post.keyword', '', 'strval,trim');
        $pType             = I('post.ptype', '', 'strval,trim');
        $startTime         = I('post.start_time', '', 'strval,trim');
        $endTime           = I('post.end_time', '', 'strval,trim');
        $expireStatuses    = I('post.expire_statuses', '', 'strval,trim');
        $expireDateOrderBy = I('post.expire_date_orderby', 0, 'intval');
        $searchType        = I('post.search_type', 1, 'intval');
        $searchTag         = I('post.search_tag', -1, 'intval');
        $page              = I('post.page', 1, 'intval');
        $size              = I('post.size', 10, 'intval');
        $subType           = I('post.sub_type', 0, 'intval');
        $skuSearchTag      = I('post.sku_search_tag', -1, 'intval'); // 票搜索标记 0票名称 1票id
        $skuKeyword        = I('post.sku_keyword', '', 'strval,trim'); // 票搜索值

        //产品类型拆分为产品类型和子产品类型处理
        list($pType, $subType) = SubProductBiz::decodeTypeAndSubType($pType, $subType);

        $sid = $this->memberID;
        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition();
        // 全禁的情况下不需要调用接口
        if ($condition === false) {
            $this->apiReturn(self::CODE_SUCCESS);
        }
        if ($skuSearchTag > -1 && $skuKeyword) {
            // 0票名称 1票id
            switch ($skuSearchTag) {
                case 0:
                    $condition['ticketName'] = $skuKeyword;
                    break;
                case 1:
                    $condition['ticketIds'] = [intval($skuKeyword)];
                    break;
            }
        }
        $packBiz = new \Business\Product\Ticket();
        $result  = $packBiz->queryPostponeTicketPage($sid, $keyword, $pType, $startTime, $endTime,
            $expireStatuses, $expireDateOrderBy, $searchType, $searchTag, $page, $size, (int)$subType, $subSid, $condition);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);

    }

    /**
     * 批量延长票的到期时间
     *
     * @param  string json package_data
     *
     * @return string json
     *
     */
    public function postponeTicketDate()
    {
        $postponeDate = I('post.postponeDate', '', 'strval,trim');
        $ticketIds    = I('post.ticket_ids', []);

        if (!$postponeDate || !$ticketIds) {
            $this->apiReturn(204, [], "参数错误");
        }
        if (strtotime($postponeDate) < strtotime(date('Y-m-d'))) {
            $this->apiReturn(204, [], "延期时间必须大等于今天");
        }

        if (count($ticketIds) > 500) {
            $this->apiReturn(204, [], "每次批量延期不得大于500张");
        }

        // 历史原因特殊，将错就错。
        $sid = $this->memberID;
        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        $packBiz = new businessLib($sid, $this->staffId);
        $result  = $packBiz->postponeTicketDate($postponeDate, $ticketIds, $sid, $this->staffId, $subSid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);

    }

    /**
     * 票搜索下来查询自供应
     * <AUTHOR>
     * @date 2021/08/1
     *
     * @return array
     */
    public function getMyTicketBySidAndLidAndTitle()
    {
        $title    = I('title', '');
        $lids     = I('land_id', '');
        $page     = I('page', 1);
        $size     = I('size', 20);
        $getTotal = I('get_more', 0);

        if ($lids) {
            $lids = explode(',', $lids);
        } else {
            $lids = null;
        }
        !$title && $title = null;

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByApplyDidAndLandIdAndPTypeAndApplyLimitAndPStatusAndStatus([$this->memberID],
            $lids, 'id,title', '', '', '', null, null, null, null, null, $title, $page, $size);

        if (empty($ticketArr['data']['list'])) {
            $this->apiReturn(self::CODE_SUCCESS, [], '');
        }

        $evoluteInfo = array_column($ticketArr['data']['list'], 'ticket');

        $data = [];
        if (!empty($evoluteInfo)) {
            foreach ($evoluteInfo as $item) {
                $data[] = [
                    'id'    => $item['id'],
                    'title' => $item['title'],
                ];
            }
        }
        if ($getTotal) {
            $result['list']  = $data;
            $result['total'] = $ticketArr['data']['total'];
            $this->apiReturn(self::CODE_SUCCESS, $result, '');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, $data, '');
        }
    }

    /**
     * 通过场馆id获取景区下门票自动校验时间
     *
     * <AUTHOR> Li
     * @date 2021-09-01
     */
    public function showAutoCheckByZoneId()
    {
        $venueId = I('post.venue_id', 0, 'intval');
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 10, 'intval');

        if (!$venueId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '场馆id缺失');
        }

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->showAutoCheckByZoneId($this->memberID, $venueId, $page, $size);

        if (empty($ticketArr['list'])) {
            $this->apiReturn(self::CODE_SUCCESS, [], '数据获取失败');
        }

        $this->apiReturn(self::CODE_SUCCESS, $ticketArr, '数据获取成功');
    }

    /**
     * 查询自供应的票
     * <AUTHOR>
     * @date   2024/07/13
     *
     */
    public function getSelfSupplyTicket()
    {
        $keyWord  = I('post.key_word', '', 'trim,strval');//门票名称搜索
        $page     = I('post.page', 1, 'intval');//页码
        $size     = I('post.size', 50, 'intval');//页数
        $getTotal = I('post.get_more', 1, 'intval');//是否获取总数
        $status   = I('post.status', 0, 'intval'); //1上架 2下架 6删除

        !$keyWord && $keyWord = null;

        !$status && $status = null;

        //分页限制
        empty($size) && $size = 50;
        $size > 200 && $size = 200;
        empty($page) && $page = 1;

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByApplyDidAndLandIdAndPTypeAndApplyLimitAndPStatusAndStatus([$this->memberID],
            null, 'id,title', '', '', '', null, null, null, $status, null, $keyWord, $page, $size);

        if (!empty($ticketArr['data']['list'])) {
            $evoluteInfo = array_column($ticketArr['data']['list'], 'ticket');

            $data = [];
            if (!empty($evoluteInfo)) {
                foreach ($evoluteInfo as $item) {
                    $data[] = [
                        'id'    => $item['id'],
                        'title' => $item['title'],
                    ];
                }
            }
        }

        $result = [
            'list' => $data ?? [],
        ];
        $getTotal && $result['total'] = $ticketArr['data']['total'] ?? 0;

        $this->apiReturn(self::CODE_SUCCESS, $result);
    }
}
