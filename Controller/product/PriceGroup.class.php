<?php

/**
 *  价格配置页面接口
 *
 * <AUTHOR>
 */

namespace Controller\Product;

use Business\JavaApi\EvoluteApi;
use Business\JavaApi\Product\ResourceCenterApi;
use Business\JavaApi\TicketApi;
use Business\Product\MultiLevelPrice;
use Business\Product\Price as PriceBiz;
use Business\Product\ProListPrice;
use Library\Cache\Cache;
use Library\Controller;
use Library\Tools\Helpers;
use Model\AdminConfig\AdminConfig;
use Model\Member\Member;
use Model\Member\MemberRelationship;
use Model\Product\Evolute;
use Model\Product\Land;
use Model\Product\Price;
use Model\Product\PriceGroup as GroupModel;
use Model\Product\Ticket;

class PriceGroup extends Controller
{

    private $javaFailMsgCode = [
        50033,
    ];

    const MAX_PRICE = 100000; //最大配置价格

    //供应商id
    private $_sid;

    //供应商id
    private $_saccount;

    private $_redisObj;

    private $_lockKey;

    private $_multiBiz;

    private $_multiAuth;

    private $_rcBizApi;

    private $_loginInfo;

    public function __construct()
    {
        $isLogin = $this->isLogin('auto', false);

        if (!$isLogin) {
            $this->_ajaxReturn('noSaleRight');
        }

        //配置人员的主账号id
        $this->_loginInfo = $this->getLoginInfo('auto');
        $this->_sid       = $this->_loginInfo['sid'];
        $this->_saccount  = $this->_loginInfo['saccount'];

        if (in_array(I('act'), ['savePriceForSelf', 'savePriceForDis', 'savePriceForProduct', 'disabled_product'])) {

            @pft_log('price/request', "memberID:{$this->_loginInfo['memberID']}|" . json_encode($_REQUEST));

            $act     = I('act');
            $setFlag = md5(json_encode(I('')));

            $redisObj = Cache::getInstance('redis');
            $lockKey  = "{$this->_sid}:{$act}:{$setFlag}";

            $lockRet = $redisObj->lock($lockKey, 1, 1);

            if (!$lockRet) {
                $this->_ajaxReturn('repeat');
            } else {
                $this->_redisObj = $redisObj;
                $this->_lockKey  = $lockKey;
            }
        }
    }

    /**
     * 获取门票列表
     *
     * @return [type] [description]
     */
    public function getTickets()
    {
        $pid    = I('pid', 0, 'intval');
        $landid = I('landid', 0, 'intval');
        $aid    = I('sid', 0, 'intval');

        $Ticket = $this->_getModelBySlave('Ticket');
        $Land   = $this->_getModelBySlave('Land');

        //如果传入的是pid,则先获取landid,再进一步获取下属的所有pid
        if ($pid) {
            $tInfo  = $Ticket->getTicketInfoByPid($pid, 'landid');
            $landid = $tInfo['landid'];
        } else {
            if (!$landid) {
                $this->_ajaxReturn('paramError');
            }
        }
        $commodityProductBiz = new \Business\CommodityCenter\Product();
        $productInfo         = $commodityProductBiz->getProductInfoByContactId([$landid], [0], 1);
        $pidArr              = array_column($productInfo, 'id');

        if (!$pidArr || !in_array($pid, $pidArr)) {
            $this->_ajaxReturn('noSaleRight');
        }

        //转分销产品,权限过滤
        if ($aid) {
            $getEvoluteBiz = new \Business\Product\Get\Evolute();
            $evoluteArr    = $getEvoluteBiz->getEvoluteByPidSidFidActiveStatus($pidArr, [$this->_sid], [$aid], 0, 1);
            if (!$evoluteArr) {
                $this->_ajaxReturn('noSaleRight');
            }

            $pidArr = array_column($evoluteArr, 'pid');
            if (!in_array($pid, $pidArr)) {
                $this->_ajaxReturn('noSaleRight');
            }
        }

        //票类名称映射
        $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
        $titleMap           = $commodityTicketBiz->getTicketAndLandInfoByArrPidHandle($pidArr);

        //原始成本价
        $sPriceMapping = $Ticket->getMuchSupplyPrice($pidArr);
        //获取门票id
        $tids = $Ticket->getTidArrByPid($pidArr);

        $tickets = [];
        foreach ($pidArr as $key => $tmpPid) {
            if (!isset($sPriceMapping[$tmpPid])) {
                unset($pidArr[$key]);
            }
            $currentTime = strtotime(date('Y-m-d 00:00:00'));
            $tickets[] = [
                'id'     => strval($tmpPid),
                'p_name' => $titleMap[$tmpPid]['title'],
                'title'  => $titleMap[$tmpPid]['ttitle'],
                'status' => $titleMap[$tmpPid]['status'],
                'maxExpirationDate' => $titleMap[$tmpPid]['max_expiration_date'],
                'isExpireTicket' => ($currentTime > strtotime($titleMap[$tmpPid]['max_expiration_date'])),
                'tid'    => $tids[$tmpPid],
            ];
        }
        //排序
        usort($tickets, function ($val1) use ($pid) {
            if ($val1['id'] == $pid) {
                return -1;
            } else {
                return 1;
            }
        });

        $landInfo = [
            'landid' => $landid,
            'title'  => $titleMap[$tmpPid]['title'],
        ];

        $return = ['tickets' => $tickets, 'land_info' => $landInfo];
        $this->_ajaxReturn('ticketsList', $return);
    }

    /**
     * 分销链3期切换 获取 [分销价格配置]页面
     * http://my.12301.local/new/multidistribution_groupTest.html?pid=343721&sid=6970&lvl=1&aids=6970
     *
     *
     */
    public function getDefaultSettlePriceNew()
    {
        $aid                = I('sid', 0, 'intval'); //供应商ID
        $pid                = I('pid', 0, 'intval'); //票类id
        $page               = I('page', 1, 'intval');
        $pageSize           = I('page_size', 15, 'intval');
        $fid                = I('fid', 0, 'intval'); //分销商id
        $distributionActive = I('distributionActive', 2, 'intval');

        //是否开启分销状态   0：全部    1：已分销   2：未分销
        if ($distributionActive == 2) {
            $distributionActive = 0;
        } elseif ($distributionActive == 0) {
            $distributionActive = 2;
        }

        // 定义需要返回的成功的数据格式
        $returnListArr = [
            'group'           => [],
            'current_page'    => $page,
            'total_page'      => 100,
            'is_top_confirm'  => false,
            'use_multi_right' => false,
            'multi_auth'      => false,
            'resource_center' => [],
            'self'            => [],
        ];

        $ticModel = $this->_getModelBySlave('ticket');
        $ticket   = $ticModel->getTicketInfoByPid($pid);
        if (!$ticket) {
            $this->_ajaxReturn('javaFailMsg', '门票信息异常');
        }

        $tmpAid = !empty($aid) ? $aid : $this->_sid;
        //获取价格
        $priceApi = new \Business\JavaApi\Ticket\Price();
        $priceRes = $priceApi->getActualtimePrice($this->_sid, $tmpAid, $ticket['id'], false);

        if ($priceRes['code'] != 200 || empty($priceRes['data'])) {
            $this->_ajaxReturn('outOfDate');
        }

        $lPrice      = $priceRes['data']['retail_price'] / 100;
        $supplyPrice = $priceRes['data']['settlement_price'] / 100;


        ////原始供货价
        //$sPrice = $ticModel->getSupplyPrice($pid);
        ////零售价
        //$lPrice = $ticModel->getRetailPrice($pid);
        ////产品是否过期
        //if ($sPrice === false) {
        //    $this->_ajaxReturn('outOfDate');
        //}

        //当前用户结算价
        //$supplyPrice = $this->_getSupplyPrice($pid, $aid, $this->_sid, $sPrice);
        //if ($supplyPrice === false) {
        //    $this->_ajaxReturn('noSaleRight');
        //}

        $commodityProductBiz = new \Business\CommodityCenter\Product();
        //是否自供应
        $productInfo = $commodityProductBiz->getProductInfoById($pid);
        if (!$productInfo) {
            $self = false;
        } else {
            if ($productInfo['apply_did'] == $this->_sid) {
                $self = true;
            } else {
                $self = false;
            }
        }

        $aid = $self ? $this->_sid : $aid;

        // 获取group 的数据信息
        $evoluteQueryHandleBiz = new \Business\Product\Get\EvoluteQueryHandle();
        $queryResArr           = $evoluteQueryHandleBiz->getEvoluteGroupConfigList($this->_sid, $pid,
            $distributionActive, $fid, 0, $page, $pageSize);
        if ($queryResArr['code'] != 200) {
            $this->_ajaxReturn('javaFailMsg', $queryResArr['msg']);
        }

        //假如有查询指定分销商的时候
        if ((!empty($fid) && empty($groups)) || empty($fid)) {
            $evoluteBz = new \Business\Product\Get\Evolute();
            $res       = $evoluteBz->getEvoluteListBySid($aid, $this->_sid, $pid, $distributionActive, $fid, $page,
                $pageSize);
        }

        $unGroupMember = $res['resultList'] ?? [];
        if (isset($unGroupMember) && !empty($unGroupMember)) {
            //处理数据
            foreach ($unGroupMember as &$value) {
                //防止出现null的情况
                $dprice         = $value['dprice'] ?: 0;
                $value['price'] = ($value['settlementPrice'] + $dprice) / 100;
                $value['id']    = $value['fid'];
                //status字段 0：已分销  1：未分销
                if ($value['status'] == 1) {
                    $value['allow'] = false;
                } else {
                    $value['allow'] = true;
                }
            }
        }

        //  计算总页数
        $returnListArr['total_page'] = ceil($queryResArr['data']['total'] / $pageSize);

        // 获取分组数据
        $groupListArr = [];
        if (!empty($queryResArr['data']['list'])) {
            foreach ($queryResArr['data']['list'] as $item) {
                $groupListArr[] = [
                    'group_id'     => $item['groupId'],
                    'group_name'   => $item['groupName'],
                    'member'       => $unGroupMember,
                    'allow'        => $item['distributionState'],
                    'count'        => $item['groupFidNum'],
                    'supply_price' => $item['ticketActualTimePriceDTO']['costPrice'] / 100,
                    'price'        => $item['ticketActualTimePriceDTO']['settlementPrice'] / 100,
                ];
            }
        }

        $returnListArr['group']          = $groupListArr;
        $returnListArr['is_top_confirm'] = false;

        //$tInfo = $ticModel->getTicketInfo('pid', $pid, 'id,landid', [], true);
        //$lid   = $tInfo['landid'];

        //资源中心相关信息
        // $rcInfo                           = $this->_getResourceCenterInfo($self, $lid, $pid);
        $returnListArr['resource_center'] = [];

        $returnListArr['self'] = [
            'recom_price'  => $lPrice,
            'supply_price' => $supplyPrice,
        ];

        $this->_ajaxReturn('defaultPriceSet', $returnListArr);
    }

    /**
     * 出售中产品默认价
     *
     * @return [type] [description]
     */
    public function getDefaultSettlePrice()
    {
        $aid      = I('sid', 0, 'intval'); //供应商ID
        $pid      = I('pid', 0, 'intval'); //票类id
        $page     = I('page', 0, 'intval');
        $pageSize = I('page_size', 15, 'intval');
        $keyword  = I('keyword', '', 'strval'); //搜素关键字
        $fid      = I('fid', 0, 'intval'); //分销商id
        //是否开启分销状态   2：全部    1：已分销   0：未分销
        $distributionActive = I('distributionActive', 2, 'intval');

        $ticModel = $this->_getModelBySlave('ticket');
        $ticket = $ticModel->getTicketInfoByPid($pid);
        if (!$ticket) {
            $this->_ajaxReturn('javaFailMsg', '门票信息异常');
        }

        $tmpAid = !empty($aid) ? $aid : $this->_sid;
        //获取价格
        $priceApi = new \Business\JavaApi\Ticket\Price();
        $priceRes = $priceApi->getActualtimePrice($this->_sid, $tmpAid, $ticket['id'], false);

        if ($priceRes['code'] != 200 || empty($priceRes['data'])) {
            $this->_ajaxReturn('outOfDate');
        }

        $lPrice      = $priceRes['data']['retail_price'] / 100;
        $supplyPrice = $priceRes['data']['settlement_price'] / 100;

        ////原始供货价
        //$sPrice = $ticModel->getSupplyPrice($pid);
        ////零售价
        //$lPrice = $ticModel->getRetailPrice($pid);
        ////产品是否过期
        //if ($sPrice === false) {
        //    $this->_ajaxReturn('outOfDate');
        //}
        //
        ////当前用户结算价
        //$supplyPrice = $this->_getSupplyPrice($pid, $aid, $this->_sid, $sPrice);
        //if ($supplyPrice === false) {
        //    $this->_ajaxReturn('noSaleRight');
        //}

        $commodityProductBiz = new \Business\CommodityCenter\Product();
        //是否自供应
        $productInfo = $commodityProductBiz->getProductInfoById($pid);
        if (!$productInfo) {
            $self = false;
        } else {
            if ($productInfo['apply_did'] == $this->_sid) {
                $self = true;
            } else {
                $self = false;
            }
        }

        //第一页才显示分组
        if ($page == 1) {
            //获取分组的结算信息
            $groups          = $this->_getSettlePriceForGrp($pid, $this->_sid, $supplyPrice, $keyword, $aid, $fid);
            $return['group'] = $groups;
        }
        if ((!empty($fid) && empty($groups)) || empty($fid)) {
            $evoluteBz = new \Business\Product\Get\Evolute();
            $res       = $evoluteBz->getEvoluteListBySid($aid, $this->_sid, $pid, $distributionActive, $fid, $page,
                $pageSize);
        }

        $unGroupMember = $res['resultList'] ?: [];
        if (!empty($unGroupMember)) {
            //处理数据
            foreach ($unGroupMember as &$value) {
                //防止出现null的情况
                $dprice         = $value['dprice'] ?: 0;
                $value['price'] = ($value['settlementPrice'] + $dprice) / 100;
                $value['id']    = $value['fid'];
                //status字段 0：已分销  1：未分销
                if ($value['status'] == 1) {
                    $value['allow'] = false;
                } else {
                    $value['allow'] = true;
                }
            }
        }
        //重组数据
        $unJoin = [
            'total' => $res['recordtotal'] ? ceil($res['recordtotal'] / $pageSize) : 0,
            'list'  => [
                'group_id' => 0,
                'member'   => $unGroupMember,
            ],
        ];
        if ($unJoin['list']) {
            $return['group'][] = $unJoin['list'];
        }

        $return['current_page']   = $page;
        $return['total_page']     = $unJoin['total'];
        $return['is_top_confirm'] = false;

        //$javaApi = new \Business\CommodityCenter\Ticket();
        //$tInfo   = $javaApi->queryTicketInfoByProductIds([$pid], 'id,landid');
        //$tInfo   = $ticModel->getTicketInfo('pid', $pid, 'id,landid', [], true);
        //$lid     = $tInfo[0]['ticket']['landid'];

        $return['use_multi_right'] = false;

        $return['multi_auth'] = false;

        //资源中心相关信息
        // $rcInfo                    = $this->_getResourceCenterInfo($self, $lid, $pid);
        $return['resource_center'] = [];

        $return['self'] = [
            'recom_price'  => $lPrice,
            'supply_price' => $supplyPrice,
        ];

        $this->_ajaxReturn('defaultPriceSet', $return);
    }

    /**
     * 配置自供应产品的价格(合作分销商)
     *
     * @return [type] [description]
     */
    public function savePriceForSelf()
    {
        $price = I('price');
        $gid   = I('gid', 0, 'intval');
        $did   = I('did', 0, 'intval');

        // 分销链3 新加参数
        $supplyPrice   = I('supply_price', 0, 'strval');
        $aids          = I('aids', '', 'strval');
        $groupIsNew    = I('is_new', 1);
        $parentGroupId = I('parent_groupid', -1);
        $groupIds      = I('group_ids', '');
        $skip          = I('skip', 0, 'intval');      //是否跳过价格判断 0否 1是

        pft_log('distribute', "配置自供应产品的价格本次操作人" . $this->_loginInfo['memberID'] . "所操作的分组ID为" . $gid . "或者用户ID为" .
                              $did . "配置信息为：" . json_encode($price, JSON_UNESCAPED_UNICODE));

        //没有自供应的产品
        if (!$price || count($price) != 1) {
            $this->_ajaxReturn('success');
        }

        if (!$gid && !$did) {
            $this->_ajaxReturn('paramError');
        }
        //参数转换
        foreach ($price as $key => $value) {
            if (!is_numeric($value)) {
                $this->_ajaxReturn('paramError');
            }
        }

        $pid = array_keys($price)[0];
        $aid = $this->_sid;

        $ticModel = $this->_getModelBySlave('ticket');
        //零售价
        $lPrice = $ticModel->getRetailPrice($pid);

        Helpers::loadPrevClass('PriceGroup');

        bcscale(3);
        // 计算差价
        //$dprice = float2int(($price[$pid] * 100) - ($supplyPrice * 100), 0, 1);
        $dprice = float2int(bcsub(bcmul($price[$pid], 100), bcmul($supplyPrice, 100)), 0, 1);

        //$costPrice = $supplyPrice + ($dprice / 100);
        $costPrice = bcadd($supplyPrice, bcdiv($dprice, 100));


        if ($costPrice < $supplyPrice) {
            $this->_ajaxReturn('fail');
        }

        if (!$skip && ($costPrice > $lPrice)) {
            $this->_ajaxReturn('customTextError', '结算价不能大于零售价');
        }

        // 调用新的接口来设置产品价格
        $upEvoluteBiz = new \Business\Product\Update\EvoluteProduct();
        $configRes    = $upEvoluteBiz->configEvoluteGroupProduct($this->_sid, $aid, $pid, $gid, $did, $aids,
            $dprice, 1, $this->_sid, $groupIsNew, $parentGroupId, $groupIds);

        $result       = false;
        if ($configRes['code'] == 200) {
            $result = true;
        }

        $msgCode = $result ? 'success' : 'fail';

        $info = null;
        if (in_array($configRes['code'], $this->javaFailMsgCode)) {
            $msgCode = 'javaFailMsg';
            $info    = $configRes['msg'];
        }

        $this->_ajaxReturn($msgCode, $info);
    }

    /**
     * 合作分销商给分组里面分销商单独配置价格（特）
     * <AUTHOR>  Li
     * @date  2021-10-20
     */
    public function saveSpecialPrice()
    {
        $price = I('price');
        $gid   = I('gid', 0, 'intval');
        $did   = I('did', 0, 'intval');

        // 分销链3 新加参数
        $supplyPrice   = I('supply_price', 0, 'strval');
        $groupIsNew    = I('is_new', 1);
        $parentGroupId = I('parent_groupid', -1);
        $groupIds      = I('group_ids', '');
        $skip          = I('skip', 0, 'intval');      //是否跳过价格判断 0否 1是

        $set = I('set');  // set[175335_3385]	"1.01"

        //需要判断下当前分销商所在分组里的人数  人数为1的不能操作  给出提示
        $evoluteGroupApi  = new \Business\JavaApi\Product\EvoluteGroupUser();
        $userGroupInfoArr = $evoluteGroupApi->queryDistributor($this->_sid, $did);
        if ($userGroupInfoArr['code'] != 200 || empty($userGroupInfoArr['data'])) {
            $this->_ajaxReturn('javaFailMsg', $userGroupInfoArr['msg']);
        }

        //需要判断下是否是特分组的
        $groupId     = $userGroupInfoArr['data']['groupId'];
        $parentGroup = $userGroupInfoArr['data']['parentGroupId'];
        if ($groupId == $parentGroup || $parentGroup == 0) {
            //获取分组下人的数量
            $evoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
            $groupRes          = $evoluteGroupBiz->queryGroupFidCountByGroupIds([$groupId]);
            if ($groupRes['code'] != 200 || empty($groupRes['data'])) {
                $this->_ajaxReturn('javaFailMsg', $groupRes['msg']);
            }
            if ($groupRes['data'][$groupId] <= 1) {
                $this->_ajaxReturn('javaFailMsg', '分组的分销价格不可全部都是特殊配置');
            }
        }

        pft_log('distribute', "配置转分销产品的价格本次操作人" . $this->_loginInfo['memberID'] . "所操作的分组ID为" . $gid . "或者用户ID为" .
                              $did . "配置信息为：" . json_encode($set, JSON_UNESCAPED_UNICODE));

        //转分销的情况
        if ($set) {
            //每次只能配置一个产品
            if (count($set) != 1) {
                $this->_ajaxReturn('paramError');
            }

            $pid = $aid = 0;
            foreach ($set as $key => $price) {
                if (!is_numeric($price)) {
                    $this->_ajaxReturn('paramError');
                }
                $evoInfo = explode('_', $key);
                $pid     = $evoInfo[0];
                $aid     = $evoInfo[1];
            }

            $price = $set;
            //没有转分销产品
            if (!$price || !$pid || !$aid) {
                $this->_ajaxReturn('success');
            }

            $pidKey = $pid . '_' . $aid;
        } else {
            //没有自供应的产品
            if (!$price || count($price) != 1) {
                $this->_ajaxReturn('success');
            }

            //参数转换
            foreach ($price as $key => $value) {
                if (!is_numeric($value)) {
                    $this->_ajaxReturn('paramError');
                }
            }

            $pid    = array_keys($price)[0];
            $aid    = $this->_sid;
            $pidKey = $pid;
        }

        if (!$gid && !$did) {
            $this->_ajaxReturn('paramError');
        }

        $ticModel = $this->_getModelBySlave('ticket');
        //零售价
        $lPrice = $ticModel->getRetailPrice($pid);

        Helpers::loadPrevClass('PriceGroup');

        // 计算差价
        bcscale(3);
        //$dprice = float2int(($price[$pidKey] * 100) - ($supplyPrice * 100), 0, 1);
        $dprice = float2int(bcsub(bcmul($price[$pidKey], 100), bcmul($supplyPrice, 100)), 0, 1);

        //$costPrice = $supplyPrice + ($dprice / 100);
        $costPrice = bcadd($supplyPrice, bcdiv($dprice, 100));

        if ($costPrice < $supplyPrice) {
            $this->_ajaxReturn('fail');
        }

        if (!$skip && ($costPrice > $lPrice)) {
            $this->_ajaxReturn('customTextError', '结算价不能大于零售价');
        }

        // 调用新的接口来设置产品价格
        $upEvoluteBiz = new \Business\Product\Update\EvoluteProduct();
        $configRes    = $upEvoluteBiz->configSpecialEvoluteGroupProduct($this->_sid, $aid, $pid, $gid, $did,
            $dprice, 1, $this->_sid, $groupIsNew, $parentGroupId, $groupIds);

        $result       = false;
        if ($configRes['code'] == 200) {
            $result = true;
        }  else {
            $this->_ajaxReturn('javaFailMsg', $configRes['msg']);
        }

        $this->_ajaxReturn($result ? 'success' : 'fail');
    }

    /**
     * 断开某个产品的分销链（特）
     * <AUTHOR> Li
     * @date  2021-11-25
     *
     * @return [type] [description]
     */
    public function disabledSpecialProduct()
    {
        $pid           = I('pid', 0, 'intval');
        $gid           = I('gid', 0, 'intval');
        $did           = I('did', 0, 'intval');
        $parentGroupId = I('parent_groupid', -1);
        $groupIds      = I('group_ids', '');

        pft_log('distribute', "断开产品的分销链本次操作人" . $this->_loginInfo['memberID'] . "所操作的分组ID为" . $gid . "或者用户ID为" .
                              $did . "断开的产品为" . $pid);
        if (!$pid || (!$gid && !$did)) {
            $this->_ajaxReturn('paramError');
        }

        // 分销链3添加参数aids 上级分销链
        $aids       = I('aids', '', 'strval');
        $groupIsNew = I('is_new', 0);

        //是否自供应
        $self                = false;
        $commodityProductBiz = new \Business\CommodityCenter\Product();
        $proInfo             = $commodityProductBiz->getProductInfoById($pid);

        if ($proInfo) {
            $applyDid = $proInfo['apply_did'];
            $self     = $applyDid == $this->_sid;
        }

        if (!$self) {
            $evolute = [];
        }

        Helpers::loadPrevClass('PriceGroup');

        // 判断是否自供应或者分销来的
        $applyDid = $evolute ? $evolute['sid'] : $this->_sid;

        // 调用新的接口来设置产品价格
        $upEvoluteBiz = new \Business\Product\Update\EvoluteProduct();
        $configRes    = $upEvoluteBiz->configSpecialEvoluteGroupProduct($this->_sid, $applyDid, $pid, $gid, $did,
            0, 0, $this->_sid, $groupIsNew, $parentGroupId, $groupIds);

        $result       = false;
        if ($configRes['code'] == 200) {
            $result = true;
        } else {
            $this->_ajaxReturn('javaFailMsg', $configRes['msg']);
        }

        if ($self && $result) {
            $this->_afterCleanRC($this->_sid, $pid, $gid, $did, true, $this->_loginInfo['memberID']);
        }

        $this->_ajaxReturn($result ? 'success' : 'fail');

    }

    /**
     * 配置转分销产品的价格(合作分销商)
     *
     * @return [type] [description]
     */
    public function savePriceForDis()
    {
        $set = I('set');  // set[175335_3385]	"1.01"
        $gid = I('gid', 0, 'intval');
        $did = I('did', 0, 'intval');

        // 分销链3新加参数
        $supplyPrice   = I('supply_price', 0, 'strval');
        $aids          = I('aids', '', 'strval');
        $groupIsNew    = I('is_new', 1);
        $parentGroupId = I('parent_groupid', -1);
        $groupIds      = I('group_ids', '');
        $skip          = I('skip', 0, 'intval');      //是否跳过价格判断 0否 1是

        pft_log('distribute', "配置转分销产品的价格本次操作人" . $this->_loginInfo['memberID'] . "所操作的分组ID为" . $gid . "或者用户ID为" .
                              $did . "配置信息为：" . json_encode($set, JSON_UNESCAPED_UNICODE));

        //每次只能配置一个产品
        if (count($set) != 1) {
            $this->_ajaxReturn('paramError');
        }

        $pid = $aid = 0;
        foreach ($set as $key => $price) {
            if (!is_numeric($price)) {
                $this->_ajaxReturn('paramError');
            }
            $evoInfo = explode('_', $key);
            $pid     = $evoInfo[0];
            $aid     = $evoInfo[1];
        }

        //没有转分销产品
        if (!$set || !$pid || !$aid) {
            $this->_ajaxReturn('success');
        }

        if (!$gid && !$did) {
            $this->_ajaxReturn('paramError');
        }

        $ticModel = $this->_getModelBySlave('ticket');
        //零售价
        $lPrice = $ticModel->getRetailPrice($pid);

        Helpers::loadPrevClass('PriceGroup');

        // 计算差价
        $key    = $pid . '_' . $aid;
        bcscale(3);
        //$dprice = float2int(($set[$key] * 100) - ($supplyPrice * 100), 0, 1);
        $dprice = float2int(bcsub(bcmul($set[$key], 100), bcmul($supplyPrice, 100)), 0, 1);

        //$costPrice = $supplyPrice + ($dprice / 100);
        $costPrice = bcadd($supplyPrice, bcdiv($dprice, 100));

        if ($costPrice < $supplyPrice) {
            $this->_ajaxReturn('fail');
        }

        if (!$skip && $costPrice > $lPrice) {
            $this->_ajaxReturn('customTextError', '结算价不能大于零售价');
        }

        // 调用新的接口来设置产品价格
        $upEvoluteBiz = new \Business\Product\Update\EvoluteProduct();
        $configRes    = $upEvoluteBiz->configEvoluteGroupProduct($this->_sid, $aid, $pid, $gid, $did, $aids,
            $dprice, 1, $this->_sid, $groupIsNew, $parentGroupId, $groupIds);
        $result       = false;

        if ($configRes['code'] == 200) {
            $result = true;
        } else {
            $this->_ajaxReturn('javaFailMsg', $configRes['msg']);
        }

        $msgCode = $result ? 'success' : 'fail';

        $this->_ajaxReturn($msgCode);
    }

    /**
     * 转分销页面批量转分销产品
     * <AUTHOR>
     * @date 2019/8/29
     */
    public function setBatchSavePriceForDisTask()
    {
        $params = I('post.'); //接受前端的所有数据

        if (empty($params) || !is_array($params)) {
            $this->apiReturn(203, [], "参数错误");
        }

        //生成本次任务的唯一标识
        $sign       = $this->_createUnique();
        $memberInfo = $this->getLoginInfo();

        $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('batch_dis', $this->_loginInfo['saccount']);
        if ($vacationMode === false) {
            $this->apiReturn(403, [], '当前处于假日模式，该功能被限制使用');
        }

        //暂时对这个用户做下配置
        if ($memberInfo['sid'] == 2548328) {
            pft_log('bad_seller', json_encode([$memberInfo['memberID'], $params]));
            $this->apiReturn(203, [], "系统异常，请重试");
        }

        $dataArray = [];
        foreach ($params['list'] as $key => $value) {
            $gidArr = explode(',', $value['gid']);
            foreach ($gidArr as $gid) {
                $dataArray[] = [
                    'aid'      => $value['aid'],
                    'sid'      => $this->_sid,
                    'gid'      => $gid,
                    'pid'      => $value['pid'],
                    'tid'      => $value['tid'],
                    'price'    => $value['price'] * 100, //入库时已分为单位
                    'opid'     => $memberInfo['memberID'],
                    'status'   => 0,
                    'add_time' => time(),
                    'recmonth' => date('m', time()),
                    'sign'     => $sign,
                    'dprice'   => $value['dprice'] * 100 ?? 0, //分销差价
                ];
            }
        }
        $gidArr    = array_column($dataArray, 'gid');
        $pidArr    = array_column($dataArray, 'pid');
        $priceMode = new Price();
        $field     = 'gid';
        //检查数据库中是否已存在相同的未执行的任务
        $result = $priceMode->checkBatchSavePriceForDisTask($gidArr, $pidArr, $field);
        if (!empty($result)) {
            $dataArray = array_key($dataArray, 'gid');

            foreach ($result as $value) {
                unset($dataArray[$value['gid']]);
            }

        }
        if (empty($dataArray)) {
            $this->apiReturn(400, $result, "您已创建该分销任务");
        }
        //将数据批量插入数据库
        $res = $priceMode->setBatchSavePriceForDisTask($dataArray);
        if ($res === false) {
            $this->apiReturn(400, [], "任务创建失败");
        }
        $this->apiReturn(200, $result, "任务创建成功");
    }

    /**
     * 生成任务的唯一表示符
     */
    private function _createUnique()
    {
        $data = $_SERVER['HTTP_USER_AGENT'] . $_SERVER['REMOTE_ADDR']
                . time() . rand();

        return sha1($data);
    }

    /**
     * 获取转分销页面批量转分销产品结果
     * <AUTHOR>
     * @date 2019/8/29
     */
    public function getBatchSavePriceForDisTask()
    {
        $pageSize = I('post.pageSize', 10, 'intval');
        $pageNum  = I('post.pageNum', 1, 'intval');
        $sign     = I('post.sign', '', 'strval');     //任务唯一标识
        $status   = I('post.status', '', 'int');     //任务唯一标识
        if (empty($sign)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $sid       = $this->_sid;
        $priceMode = new Price();
        $res       = $priceMode->getBatchSavePriceForDisTask($status, 0, $pageSize, $pageNum, true, $sid,
            'status, reason, update_time, tid, gid, price, opid, aid, pid', $sign);
        if (!empty($res)) {
            //获取票类信息
            $tidArr = array_column($res['list'], 'tid');
            //$tidStr     = implode(',', $tidArr);
            $javaApi      = new \Business\CommodityCenter\Ticket();
            $queryInfoArr = [];
            foreach ($tidArr as $key => $value) {
                $queryInfoArr[$key]['ticketId'] = $value;
            }
            $ticketInfo    = [];
            $ticketInfoArr = $javaApi->queryTicketInfoByQueryInfos($queryInfoArr);
            if ($ticketInfoArr) {
                foreach ($ticketInfoArr as $item) {
                    $ticketInfo[] = $javaApi->fieldConversion($item);
                }
            }
            //$ticketMode = new TicketApi();
            //$ticketInfo = $ticketMode->getTicketArr($tidStr);
            $ticketInfo = array_key($ticketInfo, 'id');
            //获取上级供应商信息
            $aidArr     = array_column($res['list'], 'aid');
            $memberMode = new Member();
            $memberInfo = $memberMode->getMemberInfoByMulti($aidArr, 'id', 'id,dname');
            $memberInfo = array_key($memberInfo, 'id');
            //获取分组信息
            $gidArr   = array_column($res['list'], 'gid');
            $groupBiz = new \Business\Product\Update\EvoluteGroup();
            $originGroupInfo = $groupBiz->queryByGroupIds($gidArr);
            if ($originGroupInfo['code'] == 200 && $originGroupInfo['data']) {
                foreach ($originGroupInfo['data'] as $group) {
                    $groupInfo[$group['id']] = [
                        'id'   => $group['group_id'],
                        'name' => $group['groupName']
                    ];
                }
            }

            foreach ($res['list'] as &$value) {
                if (isset($ticketInfo[$value['tid']])) {
                    $value['name']     = $ticketInfo[$value['tid']]['name'];
                    $value['landName'] = $ticketInfo[$value['tid']]['land_name'];
                }
                if (isset($memberInfo[$value['aid']])) {
                    $value['applyName'] = $memberInfo[$value['aid']]['dname'];
                }
                if (isset($groupInfo[$value['gid']])) {
                    $value['groupNmae'] = $groupInfo[$value['gid']]['name'];
                }
            }
        }
        $this->apiReturn(200, $res, "获取成功");
    }

    /**
     * 获取转分销页面批量转分销产品结果
     * <AUTHOR>
     * @date 2019/8/29
     */
    public function getBatchPriceForDisTask()
    {
        $pageSize       = I('get.pageSize', 1, 'intval');
        $pageNum        = I('get.pageNum', 10, 'intval');
        $sid            = $this->_sid;
        $priceMode      = new Price();
        $list           = $priceMode->getBatchPriceForDisTask($sid, false, $pageSize, $pageNum,
            'count(id) as taskNum,sign, add_time');
        $oneRunningTask = $priceMode->findPriceForDisTask($sid, 0);

        //如果没有在执行中的任务，则不需要在请求接口
        $isNeedRepeatRequest = empty($oneRunningTask) ? 0 : 1;
        if (!empty($list)) {
            $singArr = array_column($list, 'sign');
            //获取失败的任务数量
            $failNum = $priceMode->getBatchPriceForDisTask($sid, 2, $pageSize, $pageNum, 'count(id) as failNum, sign',
                $singArr);
            //获取成功的任务数量
            $success = $priceMode->getBatchPriceForDisTask($sid, 1, $pageSize, $pageNum,
                'count(id) as successNum, sign', $singArr);

            $failNum = array_key($failNum, 'sign');
            $success = array_key($success, 'sign');

            foreach ($list as &$value) {
                //初始化数据
                $value['failNum']    = 0;
                $value['successNum'] = 0;
                $value['add_time']   = date('m-d H:i:s', $value['add_time']);

                if (isset($failNum[$value['sign']])) {
                    $value['failNum'] = $failNum[$value['sign']]['failNum'];
                }
                if (isset($success[$value['sign']])) {
                    $value['successNum'] = $success[$value['sign']]['successNum'];
                }
            }
        }
        $data = [
            'list'                   => $list,
            'is_need_repeat_request' => $isNeedRepeatRequest,
        ];
        $this->apiReturn(200, $data, "获取成功");
    }

    /**
     * 配置一个产品的价格(出售中产品)
     *
     * @return [type] [description]
     */
    public function savePriceForProduct()
    {
        $pid         = I('pid', 0, 'intval');
        $group       = I('group');
        $sid         = I('sid', 0, 'intval');
        $distributor = I('distributor');

        // 分销链3期 新加参数
        $supplyPrice = I('supply_price', 0, 'strval');
        $aids        = I('aids', '', 'strval');

        $groupIsNew = I('is_new', 0);
        $skip       = I('skip', 0, 'intval');      //是否跳过价格判断 0否 1是

        if (!$pid || (!$group && !$distributor)) {
            $this->_ajaxReturn('paramError');
        }

        if (empty($group) && !empty($distributor[0])) {
            $this->_ajaxReturn('paramError');
        }

        $ticModel = $this->_getModelBySlave('ticket');

        //零售价
        $lPrice = $ticModel->getRetailPrice($pid);

        //是否自供应
        $commodityProductBiz = new \Business\CommodityCenter\Product();
        $productInfo         = $commodityProductBiz->getProductInfoById($pid);
        $self                = false;
        if ($productInfo && $productInfo['apply_did'] == $this->_sid) {
            $self = true;
        }

        $memberId = $this->_sid;
        $supply   = $self ? $memberId : $sid;

        $gid = $did = 0;
        if (!empty($group)) {
            $gid = array_keys($group)[0];
        } else {
            $did = array_keys($distributor)[0];
        }

        Helpers::loadPrevClass('PriceGroup');

        // 操作新版分销连的
        // 计算差价
        bcscale(3);
        $priceArr = is_array($group) ? $group : $distributor;
        //$dprice   = float2int((array_shift($priceArr) * 100) - ($supplyPrice * 100), 0, 1);
        $dprice   = float2int(bcsub(bcmul(array_shift($priceArr), 100), bcmul($supplyPrice, 100)), 0, 1);

        //$costPrice = $supplyPrice + ($dprice / 100);
        $costPrice = bcadd($supplyPrice, bcdiv($dprice, 100));

        if ($costPrice < $supplyPrice) {
            $this->_ajaxReturn('fail');
        }

        if (!$skip && $costPrice > $lPrice) {
            $this->_ajaxReturn('customTextError', '结算价不能大于零售价');
        }

        // 调用新的接口来设置产品价格
        $upEvoluteBiz = new \Business\Product\Update\EvoluteProduct();
        $configRes    = $upEvoluteBiz->configEvoluteGroupProduct($this->_sid, $supply, $pid, $gid, $did, $aids,
            $dprice, 1, $this->_sid, $groupIsNew);
        pft_log('debug/yangwxxx', 'JJJJJ:' . json_encode($configRes));
        $result = false;
        if ($configRes['code'] == 200) {
            $result = true;
        }

        $msgCode = $result ? 'success' : 'fail';

        $info = null;
        if (in_array($configRes['code'], $this->javaFailMsgCode)) {
            $msgCode = 'javaFailMsg';
            $info    = $configRes['msg'];
        }
        pft_log('debug/yangwxxx', 'MMMM:' . json_encode([$msgCode, $info]));
        $this->_ajaxReturn($msgCode, $info);
    }

    /**
     * 断开某个产品的分销链
     *
     * @return [type] [description]
     */
    public function disabledProduct()
    {
        $pid           = I('pid', 0, 'intval');
        $gid           = I('gid', 0, 'intval');
        $did           = I('did', 0, 'intval');
        $parentGroupId = I('parent_groupid', -1);
        $groupIds      = I('group_ids', '');

        pft_log('distribute', "断开产品的分销链本次操作人" . $this->_loginInfo['memberID'] . "所操作的分组ID为" . $gid . "或者用户ID为" .
                              $did . "断开的产品为" . $pid);
        if (!$pid || (!$gid && !$did)) {
            $this->_ajaxReturn('paramError');
        }

        // 分销链3添加参数aids 上级分销链
        $aids       = I('aids', '', 'strval');
        $groupIsNew = I('is_new', 0);

        //是否自供应
        $self                = false;
        $commodityProductBiz = new \Business\CommodityCenter\Product();
        $proInfo             = $commodityProductBiz->getProductInfoById($pid);

        if ($proInfo) {
            $applyDid = $proInfo['apply_did'];
            $self     = $applyDid == $this->_sid;
        }

        if (!$self) {
            $evolute = [];
            //$getEvoluteBiz = new \Business\Product\Get\Evolute();
            //$evolutes      = $getEvoluteBiz->getEvoluteByPidFidActiveStatus([$pid], [$this->_sid], 0, 1);
            //
            //if (!$evolutes) {
            //    $this->_ajaxReturn('noSaleRight');
            //}
            //$evolute = $evolutes[0];

        }

        // 判断是否自供应或者分销来的
        $applyDid = $evolute ? $evolute['sid'] : $this->_sid;

        // 调用新的接口来设置产品价格
        $upEvoluteBiz = new \Business\Product\Update\EvoluteProduct();
        $configRes    = $upEvoluteBiz->configEvoluteGroupProduct($this->_sid, $applyDid, $pid, $gid, $did, $aids, 0,
            0, $this->_sid, $groupIsNew, $parentGroupId, $groupIds);
        $result       = false;
        if ($configRes['code'] == 200) {
            $result = true;
        }

        if ($self && $result) {
            $this->_afterCleanRC($this->_sid, $pid, $gid, $did, true, $this->_loginInfo['memberID']);
        }

        $this->_ajaxReturn($result ? 'success' : 'fail');

    }

    /**
     * 获取分销商价格分组列表
     *
     * @param  int  $sid  当前用户id
     *
     * @return array
     */
    public function getGrpList()
    {
        $loginInfoArr = $this->getLoginInfo();
        $sid          = $loginInfoArr['sid'];

        $pageSize   = I('post.page_size', 500, 'intval');        //分页长度
        $pageNumber = I('post.page', 1, 'intval');              //页码
        $groupName  = I('post.group_name', '', 'strval');     //分组名称

        $result = (new \Business\Member\MemberEvoluteRelation())->queryGroupNameListBySidAndGroupName($sid, $groupName, $pageSize,
            $pageNumber);
        $groups = [];
        if ($result['code'] == 200 && !empty($result['data']['list'])) {
            foreach ($result['data']['list'] as $item) {
                $groups[] = [
                    'id'    => $item['group_id'],
                    'name' => $item['group_name'],
                ];
            }
        }

        $this->apiReturn(200, $groups, 'success');
    }

    /***
     * 分销价格配置列表联想搜索
     * <AUTHOR> Yiqiang
     * @date   2018-09-03
     *
     */
    public function getProductSearchList()
    {
        $type = I('post.type', '', 'strval');
        //搜索关键字
        $keyword = I('post.keyword', '', 'strval');
        //产品类型
        $productType = I('post.productType', '');
        //产品id
        $productId = I('post.productId', 0, 'intval');
        //是否返回退票属性
        $isRefund = I('post.is_refund', 0, 'intval'); //0 不返回 1 返回
        if (!$type) {
            $this->apiReturn(204, [], '缺少参数');
        }
        $return = [];
        switch ($type) {
            //获取自供应产品列表
            case 'self_product':
                $Evolute = new EvoluteApi();
                $result  = $Evolute->getSelfProductForMember(
                    $this->_sid,
                    0,
                    0,
                    $productType,
                    $keyword,
                    true
                );
                //对返回回来的数据进行处理
                if (isset($result['list']) && count($result['list']) > 0) {
                    foreach ($result['list'] as $item) {
                        $return['list'][$item['landId']] = [
                            'id'   => $item['landId'],
                            'name' => $item['title'],
                            'lid'  => $item['landId'],
                            'type' => 'get_products',
                        ];
                    }
                    $return['list'] = array_values($return['list']);
                }
                break;
            //获取自供应票类列表
            case 'self_ticket':
                $Evolute = new EvoluteApi();
                $result  = $Evolute->getSelfProForMember(
                    $this->_sid,
                    0,
                    0,
                    $productType,
                    $keyword,
                    true,
                    $productId
                );
                //再获取一次是否可退属性
                $refundMap = [];
                if ($isRefund) {
                    $tidArr      = array_column($result['list'], 'ticketId');
                    $ticketModel = new Ticket();
                    $refundMap   = $ticketModel->getTicketList($tidArr, 'id,refund_rule');
                }

                if (isset($result['list']) && count($result['list']) > 0) {
                    foreach ($result['list'] as $item) {
                        $return['list'][$item['ticketId']] = [
                            'id'        => $item['ticketId'],
                            'name'      => $item['ticketName'],
                            'lid'       => $item['lid'],
                            'type'      => 'get_products',
                            'pid'       => $item['pid'],
                            'is_refund' => isset($refundMap[$item['ticketId']]) ? $refundMap[$item['ticketId']]['refund_rule'] : 0,
                        ];
                    }
                    $return['list'] = array_values($return['list']);
                }
                break;
            //获取转分销产品列表
            case 'dis_product':
                $Evolute = new EvoluteApi();
                $result  = $Evolute->getEvoluteProductForMember(
                    $this->_sid,
                    0,
                    0,
                    $productType,
                    $keyword,
                    true
                );
                //对返回回来的数据进行处理
                if (isset($result['list']) && count($result['list']) > 0) {
                    foreach ($result['list'] as $item) {
                        $return['list'][$item['landId']] = [
                            'id'   => $item['landId'],
                            'name' => $item['title'],
                            'lid'  => $item['landId'],
                            'type' => 'get_products',
                            'sid'  => $item['sid'],
                        ];
                    }
                    $return['list'] = array_values($return['list']);
                }
                break;
            //获取转分销票类列表
            case 'dis_ticket':
                $Evolute = new EvoluteApi();
                $result  = $Evolute->getEvoluteProForMember(
                    $this->_sid,
                    0,
                    0,
                    $productType,
                    $keyword,
                    true,
                    0,
                    $productId
                );
                if (isset($result['list']) && count($result['list']) > 0) {
                    foreach ($result['list'] as $item) {
                        $return['list'][$item['ticketId']] = [
                            'id'   => $item['ticketId'],
                            'name' => $item['ticketName'],
                            'lid'  => $item['lid'],
                            'type' => 'get_products',
                            'pid'  => $item['pid'],
                        ];
                    }
                    $return['list'] = array_values($return['list']);
                }
                break;

            case 'supply':
                $info   = $this->getLoginInfo();
                $member = $this->_getModelBySlave('member');
                $result = $member->getMySupplierListByKeyword($info['sid'], $keyword);
                if ($result) {
                    foreach ($result as $item) {
                        $return['list'][] = [
                            'id'   => $item['id'],
                            'name' => $item['dname'],
                            'type' => 'get_dis_products',
                        ];
                    }
                }
                break;

            case 'distributor':
                $pid = I('pid', 0, 'intval');
                if (!$pid) {
                    $this->apiReturn(204, [], '参数错误');
                }

                //加载会员模型
                $memModel = $this->_getModelBySlave('member');

                //获取分组成员
                $memArr             = [];
                $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
                $resellerGroup      = $getEvoluteGroupBiz->getFidCountBySid($this->_sid);
                if ($resellerGroup['code'] == 200 && !empty($resellerGroup['data'])) {
                    foreach ($resellerGroup['data'] as $group) {
                        $memArr = array_merge($memArr, $group['fidArr']);
                    }
                }

                $unJoin = ['total' => 0, 'list'  => []];

                $list = [];
                if ($memArr || $unJoin['total']) {
                    $idsG = $memArr ?: [];
                    $idsU = $unJoin['list'] ?: [];
                    $ids  = array_merge($idsG, $idsU);

                    $memsMapping = $memModel->getMemberInfoByMulti(
                        $ids,
                        'id',
                        'id,dname,cname,account',
                        true
                    );
                    //没有关键字则默认返回十条
                    $key = 0;
                    foreach ($memsMapping as $id => $member) {
                        if ($keyword) {
                            if (stripos($member['dname'], $keyword) === false) {
                                continue;
                            }
                            $member['type'] = 'get_default_settle_price';
                            $list[]         = $member;
                        } else {
                            $member['type'] = 'get_default_settle_price';
                            $list[]         = $member;
                            $key++;
                            if ($key == 9) {
                                break;
                            }
                        }
                    }
                }
                $return['list'] = $list;
                break;
            default:
                break;
        }
        $this->apiReturn(200, $return, 'success');
    }

    /**
     * 设置默认分组
     *
     * @param  int group_id 分组id
     *
     * @return string/json
     *
     */
    public function setDefaultGrp()
    {
        $groupId = I('request.group_id', '', 'intval');

        $loginInfoArr = $this->getLoginInfo();
        $sid          = $loginInfoArr['sid'];
        $memberId     = $loginInfoArr['memberID'];

        // 旧版设置默认分组成功后   调用新版接口
        $upEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroup();
        $res               = $upEvoluteGroupBiz->setDefaultGroup($sid, $memberId, $groupId);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], 'success');
        } else {
            $this->apiReturn(203, [], 'error');
        }
    }

    /**
     * 异步移动分组任务记录
     * <AUTHOR>
     * @date   2018-06-08
     */
    public function groupMoveRecords()
    {

        $page = I('page', 1, 'intval');
        $size = I('size', 10, 'intval');

        if (!$page || !$size) {
            $this->apiReturn(204, [], '参数错误');
        }

        $priceBiz = new PriceBiz();
        $result   = $priceBiz->groupMoveRecords($this->_sid, $page, $size);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /***
     * 获取一个分组的成员信息
     *
     * <AUTHOR> Yiqiang
     * @date   2018-09-02
     */
    public function getGroupMembers()
    {
        $groupId = I('group_id', 0, 'intval');

        if (!$groupId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $groupBiz        = new \Business\Product\Update\EvoluteGroup();
        $originGroupInfo = $groupBiz->queryByGroupId($groupId);
        if ($originGroupInfo['code'] != 200 || empty($originGroupInfo['data'])) {
            exit(json_encode(['status' => 0, 'msg' => '分组信息获取失败']));
        }

        $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
        $getResArr          = $getEvoluteGroupBiz->getFidArrByGroupId($this->_sid, $groupId);
        $groupInfoFidArr    = [];

        if ($getResArr['code'] == 200 && !empty($getResArr['data'])) {
            $groupInfoFidArr = $getResArr['data'];
        }

        $list = [];
        if ($groupInfoFidArr) {
            $memberModel = new \Business\Member\Member();
            $list        = $memberModel->getMemberInfoByMulti($groupInfoFidArr, 'id', 'id,dname,account');
        }
        $return['list'] = $list;

        $this->apiReturn(200, $return, 'success');
    }

    /**
     * 设置结算方式
     * author  leafzl
     * Date: 2018-12-24
     */
    public function setClearingWay()
    {
        $groupId = I('groupid', []);
        $fid     = I('fidArr', []);
        $way     = I('way', 0, 'intval');
        $type    = I('isBatch', 1, 'intval');
        $opid    = $this->_loginInfo['memberID'];
        $groupDids = [];
        if (!empty($groupId)) {
            $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
            $groupFidRes        = $getEvoluteGroupBiz->queryGroupFidsByGroupIds([$groupId]);
            if ($groupFidRes['code'] != 200) {
                $this->apiReturn(204, [], '分销商信息获取失败');
            }
            $groupDids = !empty($groupFidRes['data']) ? array_column($groupFidRes['data'], 'fid') : [];
        }

        $fidArr = array_merge($fid, $groupDids);

        $distributor = new \Business\Product\Price();
        $result      = $distributor->setClearingWay($this->_sid, $fidArr, $way, $type, $opid);

        //记录日志
        $logData = json_encode([
            'key'   => '配置下级的支付方式',
            '供应商ID' => $this->_sid,
            '分销商ID' => $fid,
            '支付方式'  => $way,
            '结果'    => $result,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('distribute/set_clearing_way', $logData);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }

    }

    /**
     *批量设置结算方式
     * author  leafzl
     * Date: 2018-12-24
     */
    public function batchSetClearingWay()
    {
        $group_id = I('group_id', '', 'intval');
        $way      = I('way', 0, 'intval');
        $isNew    = I('is_new', 0, 'intval');
        $opid     = $this->_loginInfo['memberID'];

        $distributor = new \Business\Product\Price();
        $result      = $distributor->batchSetClearingWay($this->_sid, $group_id, $way, $isNew, $opid);

        //记录日志
        $logData = json_encode([
            'key'   => '配置下级的支付方式',
            '供应商ID' => $this->_sid,
            '分组ID'  => $group_id,
            '支付方式'  => $way,
            '结果'    => $result,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('distribute/set_clearing_way', $logData);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取当前用户的结算价
     *
     * @param  int  $pid  产品id
     * @param  int  $aid  上级供应商id
     * @param  int  $sid  当前用户id
     * @param  int  $sPrice  原始供货价
     *
     * @return 当前用户结算价
     */
    private function _getSupplyPrice($pid, $aid, $sid, $sPrice)
    {
        $evoModel = $this->_getModelBySlave('evolute');
        $priModel = $this->_getModelBySlave('price');

        if ($aid != $sid && $aid != 0) {
            $getEvoluteBiz = new \Business\Product\Get\Evolute();
            $hasRihgt      = $getEvoluteBiz->getEvoluteByPidSidFidActiveStatus([$pid], [$sid], [$aid], 0, 1);

            if (!$hasRihgt) {
                return false;
            }

            //我的分销链数据
            $this->_mySaleList = $hasRihgt[0];

            $diffPri     = $priModel->getLastDiffPri($pid, $hasRihgt[0]['aids']);
            $supplyPrice = ($hasRihgt[0]['dprice'] + $diffPri) / 100 + $sPrice;
        } else {
            $supplyPrice = $sPrice;
        }

        return $supplyPrice;
    }


    /**
     * 获取分组的结算信息
     *
     * @param  int  $pid  产品id
     * @param  int  $sid  当前用户主账号id
     * @param  int  $supplyPrice  当前用户的结算价
     * @param  string  $keyword  分销商名称
     * @param  int  $aid  上级供应商id
     *
     * @return array
     */
    private function _getSettlePriceForGrp($pid, $sid, $supplyPrice, $keyword = '', $aid = 0, $fid = 0)
    {
        //加载会员模型
        $memModel = $this->_getModelBySlave('member');

        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $resellerGroup      = $getEvoluteGroupBiz->getFidCountBySid($sid);
        $groups = [];
        if ($resellerGroup['code'] == 200 && !empty($resellerGroup['data'])) {
            $groups         = array_values($resellerGroup['data']);
        }

        //分销商精确搜索结果标识
        $searchRe = false;
        //精确搜索
        if ($fid) {
            $memArr[] = $fid;
        } else {
            //获取分组成员
            $memArr             = [];
            if ($resellerGroup['code'] == 200 && !empty($resellerGroup['data'])) {
                foreach ($resellerGroup['data'] as $group) {
                    $memArr = array_merge($memArr, $group['fidArr']);
                }
            }
        }

        if ($memArr) {
            $memsMapping = $memModel->getMemberInfoByMulti(
                $memArr,
                'id',
                'id,dname,cname,account',
                true
            );
        }

        $return = [];
        foreach ($groups as $group) {
            $tmp = [
                'group_id'   => $group['id'],
                'group_name' => $group['name'],
                'member'     => [],
            ];

            $standarDid = 0;
            $didArr     = array_filter(explode(',', trim($group['dids'], ',')));
            $num        = count($didArr);
            if ($didArr) {
                foreach ($didArr as $tmpDid) {
                    //精确搜索分销商
                    if ($fid && $fid != $tmpDid) {
                        continue;
                    } else if ($fid && $fid == $tmpDid) {
                        $searchRe = true;
                    }

                    //分销商搜索过滤
                    if ($keyword) {
                        if (stripos($memsMapping[$tmpDid]['dname'], $keyword) === false) {
                            continue;
                        }
                    }

                    if ($aid && $tmpDid == $aid) {
                        continue;
                    }

                    $tmp['allow']    = false;
                    $tmp['count']    = $num;
                    $tmp['member'][] = [
                        'id'      => $tmpDid,
                        'dname'   => $memsMapping[$tmpDid]['dname'],
                        'cname'   => $memsMapping[$tmpDid]['cname'],
                        'account' => $memsMapping[$tmpDid]['account'],
                    ];
                    //分组成员只显示3个
                    if (count($tmp['member']) >= 3) {
                        break;
                    }

                }
                if ($this->_mySaleList) {
                    $blackList = array_merge([$aid, $sid], explode(',', $this->_mySaleList['aids']));
                } else {
                    $blackList = [$aid, $sid];
                }
                $standarDid = $this->_getFilterDid($group['dids'], $blackList);
            }

            $tmp['supply_price'] = $supplyPrice;
            //是否配置过
            if ($standarDid) {
                $already = $this->_getConfdMapping($standarDid, [$pid]);

                if (isset($already[$pid])) {
                    $tmp['allow'] = true;
                    $tmp['price'] = $supplyPrice + $already[$pid];
                } else {
                    $tmp['price'] = '';
                }
            }

            //分销商精确搜索
            if ($fid) {
                if ($searchRe) {
                    $return[] = $tmp;
                    break;
                }
            } else {
                $return[] = $tmp;
            }
        }

        return $return;
    }

    /**
     * 获取分销商id(以此分销商的结算价为基准)
     *
     * @param  [type] $dids [description]
     *
     * @return [type]       [description]
     */
    private function _getFilterDid($dids, $neq = [])
    {
        $dids = trim($dids, ',');

        if (!$dids) {
            return false;
        }

        $didArr = explode(',', $dids);

        $member     = $this->_getModelBySlave('member');
        $memberShip = new MemberRelationship();

        $didArr = $memberShip->filterSonArr($this->_sid, $didArr);
        $list   = [];
        if ($didArr) {
            $list = $member->getMemberListByIds($didArr);
        }

        if ($list) {
            $didArr = array_column($list, 'id');
            foreach ($didArr as $did) {
                if (!in_array($did, $neq)) {
                    return $did;
                }

            }
        }

        return false;
    }

    /**
     * 获取已配置的产品信息
     *
     * @param  int  $did  分销商id
     * @param  array  $pidArr  产品数组
     *
     * @return [type] [description]
     */
    private function _getConfdMapping($did, $pidArr)
    {

        if (!$pidArr) {
            return [];
        }

        $getEvoluteBiz = new \Business\Product\Get\Evolute();
        $already       = $getEvoluteBiz->getEvoluteBySidFidPidStatus([$this->_sid], [$did], $pidArr, 0);

        $already = $already ?: [];

        $alreadyMapping = [];
        foreach ($already as $item) {
            $alreadyMapping[$item['pid']] = $item['dprice'] / 100;
        }

        return $alreadyMapping;
    }

    /***
     * 获取产品资源中心相关信息
     * @author: Cai Yiqiang
     * @date: 2019/1/15
     *
     * @param $self
     * @param $lid
     * @param $pid
     *
     * @return array
     */
    // private function _getResourceCenterInfo($self, $lid, $pid)
    // {
    //     //todo::如果加入资源中心 判断是否可以供应到资源中心 -- 权限加上后做好判断 -- 拆出一个私有方法
    //     //查询资源中心设置的结算价
    //     $rcBizApi = $this->_getRCBizApi();
    //
    //     if ($self) {
    //         $rcInfo = $rcBizApi->getSelfProducts($this->_sid, 1, 1, '', $lid);
    //     } else {
    //         $rcInfo = $rcBizApi->getDisProducts($this->_sid, 1, 1, '', $lid);
    //     }
    //
    //     $resourceInfo = [
    //         'settle_price' => -1,
    //         'is_support'   => 4, //4 发生查询错误 不要上架到资源中心
    //         'status'       => 0,
    //     ];
    //
    //     if ($rcInfo) {
    //         foreach ($rcInfo['ticketList'] as $rc) {
    //             if ($pid == $rc['productId']) {
    //                 $resourceInfo = [
    //                     'settle_price' => $rc['settlement'] / 100,
    //                     'is_support'   => $rc['supportDistribution'],
    //                     'status'       => $rc['status'],
    //                 ];
    //                 break;
    //             }
    //         }
    //     }
    //
    //     return $resourceInfo;
    // }

    /***
     * 资源中心java api
     * @author: Cai Yiqiang
     * @date: 2019/1/8
     * @return ResourceCenterApi
     */
    private function _getRCBizApi()
    {
        if (!$this->_rcBizApi) {
            $this->_rcBizApi = new ResourceCenterApi();
        }

        return $this->_rcBizApi;
    }

    /**
     * 获取数据库的从库实例
     * <AUTHOR>
     * @date   2017-08-28
     *
     * @param  [type]     $modelName model名称
     *
     * @return object
     */
    private function _getModelBySlave($modelName)
    {

        static $staticObj = [];

        if (!isset($staticObj[$modelName])) {
            switch (strtolower($modelName)) {

                case 'ticket':
                    $object = new Ticket('slave');
                    break;

                case 'land':
                    $object = new Land('slave');
                    break;

                case 'group':
                    //先改下
                    $object = new GroupModel();
                    break;

                case 'price':
                    $object = new Price('slave');
                    break;

                case 'evolute':
                    $object = new Evolute('slave');
                    break;

                case 'member':
                    $object = new Member('slave');
                    break;

                default:
                    throw new \Exception("{$modelName} model dose not exists");
            }
            $staticObj[$modelName] = $object;
        }

        return $staticObj[$modelName];
    }

    /**
     * 兼容旧版的返回，等前端重构的时候切换成apiReturn
     *
     * @param  [type] $code [description]
     * @param  [type] $info [description]
     *
     * @return [type]       [description]
     */
    private function _ajaxReturn($code, $info = null)
    {
        switch ($code) {
            case 'paramError':
                $status = 0;
                $info   = '参数错误';
                break;
            case 'accountNotExists':
                $status = 0;
                $info   = '账号不存在';
                break;
            case 'noSaleProduct':
                $status = 0;
                $info   = '没有可售卖产品';
                break;
            case 'noProduct':
                $status = 0;
                $info   = '没有发布产品';
                break;
            case 'outOfDate':
                $status = 0;
                $info   = '产品已过期';
                break;
            case 'noResult':
                $status = 0;
                $info   = '没有搜索到结果';
                break;
            case 'success':
                $status = 1;
                $info   = '操作成功';
                break;
            case 'priceError':
                $status = 0;
                $info   = '结算价必须小于等于' . self::MAX_PRICE;
                break;
            case 'noSaleRight':
                $status = 0;
                $info   = '没有分销权限';
                break;
            case 'fail':
                $status = 0;
                $info   = '操作失败';
                break;
            case 'ticketsList':
                $status = 1;
                break;
            case 'productsList':
                $status = 1;
                break;
            case 'distributorsList':
                $status = 1;
                break;
            case 'repeat':
                $status = 0;
                $info   = '重复请求';
                break;
            case 'defaultPriceSet':
                $status = 1;
                break;
            case 'multi_success':
                $status = 1;
                $info   = '多级定价配置中';
                break;
            case 'multi_false':
                $status = 0;
                $info   = '存在配置中的多级定价';
                break;
            case 'no_authority':
                $status = 0;
                $info   = '无配置多级定价权限';
                break;
            case 'first_price_error':
                $status = 0;
                $info   = '配置一级分销价格出错';
                break;
            case 'close_configuration_fail':
                $status = 0;
                $info   = '关闭多级定价失败';
                break;
            case 'priceParamError':
                $status = 0;
                $info   = '价格参数错误';
                break;
            case 'multistagePriceError':
                $status = 0;
                $info   = '多级定价配置失败';
                break;
            case 'closeFirstConfigurationFail':
                $status = 0;
                $info   = '关闭一级分销商的多级定价时发生错误';
                break;
            case 'noneDis':
                $status = 0;
                $info   = "当前分组无成员，建议添加成员后再配置分销价";
                break;
            case 'customTextError':
                $status = 2;
                break;
            case 'javaFailMsg':
                $status = 0;
                break;
            default:
                exit();
        }

        //移除锁
        if ($this->_redisObj && $this->_lockKey) {
            $this->_redisObj->rm($this->_lockKey);
        }

        $return = ['status' => $status, 'code' => $code, 'info' => $info];

        if ($status == 1) {
            $return['max_price'] = self::MAX_PRICE;
        }

        exit(json_encode($return));
    }

    /***
     * 顶级供应商取消分销后，可能需要清理下级资源中心分销
     * @author: Cai Yiqiang
     * @date: 2019/3/8
     *
     * @param $sid
     * @param $pid
     * @param $gid
     * @param $did
     * @param  int  $opId  操作人员id
     *
     * @throws
     */
    private function _afterCleanRC($sid, $pid, $gid, $did, $soldOut = true, int $opId = 0)
    {
        if ($gid) {
            $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
            $getResArr          = $getEvoluteGroupBiz->getFidArrByGroupId($sid, $gid);
            $groupInfoFidArr    = [];

            if ($getResArr['code'] == 200 && !empty($getResArr['data'])) {
                $groupInfoFidArr = $getResArr['data'];
            }
            $dids     = $groupInfoFidArr ? implode(',', $groupInfoFidArr) : '';
        } else {
            $dids = (string)$did;
        }

        if ($dids) {
            $dids = explode(',', $dids);

            //分销商id不为空的情况，需要处理通知Java资源中心专享价验证下架处理
            if (!empty($dids)) {
                pft_log('distribute/notice', "供应商: $sid 操作人员: $opId 产品: $pid 开始处理自供应断开资源中心专享产品操作");
                $javaApi = new \Business\CommodityCenter\Ticket();
                $tInfo   = $javaApi->queryTicketInfoByProductIds([$pid], 'id,landid');
                $tids    = [$tInfo[0]['ticket']['id']];
                //$ticModel = $this->_getModelBySlave('ticket');
                //$tInfo    = $ticModel->getTicketInfo('pid', $pid, 'id', [], true);

                $resourceCenterBiz = new \Business\JavaApi\Product\ResourceCenterEnjoyAlone();
                $resourceCenterBiz->batchCloseResourceProductPriceByTidsAndFids($dids, $tids, $opId);
            }

            // $querys = ResourceCenterApi::queryDidsProductStatus($dids, $pid);
            $querys = (new \Business\JavaApi\Resource\ResourcerProductQueryService())->queryResourceProductSupportAccounts($dids, $pid);
            if ($querys) {
                $hasSoldDids = [];
                foreach ($dids as $subDid) {
                    if (in_array($subDid, $dids)) {
                        $hasSoldDids[] = $subDid;
                    }
                }

                if ($hasSoldDids && $soldOut) {
                    //批量下架
                    // $result = ResourceCenterApi::batchSoldOutProducts($hasSoldDids, [$pid], $this->_sid);
                    $result = (new \Business\JavaApi\ResourceCenter\ResourceProductConfig())->batchSoldOutProducts($hasSoldDids, [$pid], $this->_sid);
                    if (!$result) {
                        pft_log('distribute/disabled',
                            "$sid $pid $gid $dids 断开分销链批量下架资源中心失败:" . json_encode($hasSoldDids));
                    }
                }
            }
        }
    }

    /***
     * 获取一个新分组的成员信息
     *
     * <AUTHOR>  Li
     * @date   2021-02-07
     */
    public function getNewGroupMembers()
    {
        $groupId = I('group_id', 0, 'intval');

        if (!$groupId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
        $getResArr          = $getEvoluteGroupBiz->queryGroupFidsByGroupIds([$groupId]);
        if ($getResArr['code'] != 200) {
            $this->apiReturn(203, [], '分组分销商获取失败');
        }

        $dids = !empty($getResArr['data']) ? array_column($getResArr['data'], 'fid') : [];

        $list = array();
        if ($dids) {
            $memberBiz = new \Business\Member\Member();
            $list      = $memberBiz->getMemberInfoByMulti($dids);
        }
        $return['list'] = $list;

        $this->apiReturn(200, $return, 'success');
    }


    /**
     *批量设置还款方式
     * author  xwh
     * Date: 2021-08-11
     */
    public function batchSetRepayWay()
    {
        $group_id    = I('group_id', '', 'intval');
        $way         = I('way', 0, 'intval');
        $opid        = $this->_loginInfo['memberID'];

        $distributor = new \Business\Product\Price();
        $result      = $distributor->batchSetRepayWay($this->_sid, $group_id, $way, $opid);

        //记录日志
        $logData = json_encode([
            'key'   => '配置下级的还款方式',
            '供应商ID' => $this->_sid,
            '分组ID'  => $group_id,
            '支付方式'  => $way,
            '结果'    => $result,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('distribute/set_repay_way', $logData);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     *设置还款方式
     * author  xwh
     * Date: 2021-08-11
     */
    public function setRepayWay()
    {
        $fid    = I('fid', '', 'intval');
        $way    = I('way', 0, 'intval');
        $opid   = $this->_loginInfo['memberID'];
        $distributor = new \Business\Product\Price();
        $result      = $distributor->setRepayWay($this->_sid, $fid, $way, $opid);

        //记录日志
        $logData = json_encode([
            'key'   => '配置下级的还款方式',
            '供应商ID' => $this->_sid,
            '分销商ID'  => $fid,
            '支付方式'  => $way,
            '结果'    => $result,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('distribute/set_repay_way', $logData);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口出错');
        }
    }

    /**
     * 获取供应商下所有分组的名称及id（仅供部分特殊业务使用，非特殊走分页接口）
     * <AUTHOR>  Li
     * @date  2021-12-19
     *
     * @return array
     */
    public function getAllGrpList()
    {
        $loginInfoArr = $this->getLoginInfo();
        $sid          = $loginInfoArr['sid'];

        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $result             = $getEvoluteGroupBiz->getFidCountBySid($sid, false, false);

        if ($result['code'] != 200 || empty($result['data'])) {
            $this->apiReturn(204, [], '分组信息获取失败');
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

}
