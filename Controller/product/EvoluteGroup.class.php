<?php

namespace Controller\product;

use Library\Controller;

class EvoluteGroup extends Controller
{
    private $_sid      = null;
    private $_memberId = null;

    public function __construct()
    {
        $loginInfoArr    = $this->getLoginInfo();
        $this->_sid      = $loginInfoArr['sid'];
        $this->_memberId = $loginInfoArr['memberID'];
    }

    /**
     * 添加新的分销商分组    http://my.12301.local/r/product_EvoluteGroup/addEvoluteGroup
     * @param string group_name 分组名
     * @return string ｜ json
     *
     */
    public function addEvoluteGroup()
    {
        $groupName = I('post.group_name', '', 'strval');

        if (empty($groupName)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $addEvoluteGroupBiz = new \Business\Product\Add\EvoluteGroup();
        $addRes             = $addEvoluteGroupBiz->addGroup($this->_sid, $groupName, $this->_memberId);

        $this->apiReturn($addRes['code'], [], $addRes['msg']);
    }

    /**
     * 删除分销商分组      http://my.12301.local/r/product_EvoluteGroup/delEvoluteGroup
     * @param int group_id 分组id
     * @return string ｜ json
     *
     */
    public function delEvoluteGroup()
    {
        $groupId = I('post.group_id', '', 'intval');

        if (empty($groupId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        // 判断分组内是否存在用户
        $memberRelationBiz = new \Business\Member\MemberRelation();
        $listArr           = $memberRelationBiz->getInGroupDistributorList($this->_sid, $groupId);
        if ($listArr['code'] == 200 && !empty($listArr['data']) && !empty($listArr['data']['total'])) {
            $this->apiReturn(205, [], '请先移动组内成员');
        }

        $upEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroup();
        $delRes            = $upEvoluteGroupBiz->deleteGroup($this->_sid, $this->_memberId, $groupId);

        $this->apiReturn($delRes['code'], [], $delRes['msg']);
    }

    /**
     * 设置默认分销商分组     http://my.12301.local/r/product_EvoluteGroup/setDefaultGroup
     * @param int group_id 分组id
     * @return string | json
     *
     */
    public function setDefaultGroup()
    {
        $groupId = I('post.group_id', 0, 'intval');

        if (empty($groupId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $upEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroup();
        $setRes            = $upEvoluteGroupBiz->setDefaultGroupNew($this->_sid, $this->_memberId, $groupId);

        $this->apiReturn($setRes['code'], [], $setRes['msg']);
    }

    /**
     * 修改分组名     http://my.12301.local/r/product_EvoluteGroup/setNewGroupName
     * @param int group_id 分组id
     * @param string group_name 分组名
     * @return string | json
     *
     */
    public function setNewGroupName()
    {
        $groupId   = I('post.group_id', 0, 'intval');
        $groupName = I('post.group_name', '', 'strval');

        if (empty($groupId) || empty($groupName)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $upEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroup();
        $setRes            = $upEvoluteGroupBiz->setNewGroupNameNew($this->_sid, $this->_memberId, $groupName, $groupId);

        $this->apiReturn($setRes['code'], [], $setRes['msg']);
    }
}