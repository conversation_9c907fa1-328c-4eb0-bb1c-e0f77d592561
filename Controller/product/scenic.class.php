<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 5/23-023
 * Time: 17:22
 */

namespace Controller\product;

use Business\Product\ProductBusiness;
use function GuzzleHttp\Psr7\str;
use Library\Controller;
use Model\Product\Land;
use Business\Product\Product as bizProduct;

class scenic extends Controller
{
    private $memberID;

    public function __construct()
    {
        $memberId       = $this->isLogin('ajax');
        $this->memberID = $memberId;
    }

    /**
     * 景区编辑页面，获取景区信息接口(暂时只适配年卡产品)
     * @return [type] [description]
     */
    public function get()
    {
        $lid = I('lid', '', 'intval');

        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }
        //切换java接口
        $productBiz  = new bizProduct();
        $landInfoArr = $productBiz->getProductInfo($this->memberID, $lid, 0);

        if ($landInfoArr['code'] != 200) {
            $this->apiReturn(204, [], $landInfoArr['msg']);
        }
        $land = $landInfoArr['data'];

        $land['bhjq'] = htmlspecialchars_decode($land['bhjq']);
        $land['bhjq'] = \image_opt($land['bhjq'], 600);

        $return = [
            'product_name'   => $land['title'],
            'product_type'   => $land['p_type'],
            'province'       => explode('|', $land['area'])[0],
            'city'           => explode('|', $land['area'])[1],
            'address'        => $land['address'],
            'tel'            => $land['tel'],
            'img_path'       => $land['imgpath'],
            'jqts'           => $land['jqts'],
            'jtzn'           => $land['jtzn'],
            'bhjq'           => $land['bhjq'],
            'img_path_group' => $land['imgpathGrp'] ? $land['imgpathGrp'] : [],
            'ext'            => $land['ext'] ?? (object)[],
            'videoUrl'       => $land['videoUrl'],
            'lngLatPos'      => $land['lng_lat_pos'],
            'oversea'        => $land['oversea'],
        ];

        $this->apiReturn(200, $return);
    }

}