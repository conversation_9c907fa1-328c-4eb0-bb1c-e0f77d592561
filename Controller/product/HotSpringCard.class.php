<?php
/**
 * 温泉手牌录入控制器
 * User: wuhao
 * Date: 2016/11/24
 * Time: 14:48
 */

namespace Controller\product;

use Business\Product\Presuming;
use Library\Controller;
use Library\SimpleExcel;
use Model\Product\BaseCard;
use Model\Product\HotSpringCard as hotSpringModel;
use Model\Product\Land;
use Model\Product\Presuming as PresumingModel;
use function GuzzleHttp\Psr7\str;

class HotSpringCard extends Controller
{
    private $_hotSpringModel = null;
    private $_baseCardModel  = null;
    private $_landModel      = null;
    private $_sid            = null;
    private $_memberId       = null;
    private $_logHotSpring   = null;//对温泉卡操作日志
    private $_logBaseCard    = null;//对pft_card表操作日志
    private $_color          = [
        1 => '男士-蓝色',
        2 => '女士-粉红',
        3 => '儿童-黄色',
        4 => '女Vip-橙色',
        5 => '男Vip-紫色',
    ];

    private $_status = [
        1 => '正常',
        2 => '已使用',
        3 => '已挂失',
        4 => '已禁用',
        5 => '已冻结',
    ];
    //卡类型定义
    private $_cardType = [
        'spring' => 4,
    ];

    public function __construct()
    {
        $loginInfo           = $this->getLoginInfo();
        $this->_sid          = $loginInfo['sid'];
        $this->_memberId     = $loginInfo['memberId'];
        $this->_logHotSpring = 'product/hot_spring_card';
        $this->_logBaseCard  = 'product/base_card';

        $this->_baseCardModel  = new BaseCard();
        $this->_hotSpringModel = new hotSpringModel();
        $this->_landModel      = new Land();
    }

    /**
     * 创建\录入温泉手牌
     * <AUTHOR>
     * @date   2016-11-24
     *
     * @param  string  $physics_no  物理卡号
     * @param  string  $visible_no  手牌号
     * @param  int  $color  手牌颜色
     * @param  int  $salerid  产品的salerid
     *
     * @return
     */
    public function createHotSpringCard()
    {
        //物理卡号，手牌号，手牌颜色, 景区id
        $physicsNo = I('post.physics_no', '', 'strval');
        $visibleNo = I('post.visible_no', '', 'strval');
        $color     = I('post.color', 1, 'intval');
        $salerid   = I('post.salerid', 0, 'intval');

        //判断颜色是否正常
        $colorInfo = $this->_hotSpringModel->getColorInfoById($color, 'state');
        if (empty($colorInfo)) {
            $this->apiReturn(400, [], '颜色信息有误！');
        }
        if ($colorInfo['state'] == 2) {
            $this->apiReturn(400, [], '手牌颜色以已禁用，无法使用');
        }
        if ($colorInfo['state'] == 3) {
            $this->apiReturn(400, [], '手牌颜色以已删除，无法使用');
        }
        //先查找物理卡表是否有账号信息
        $card = $this->_baseCardModel->getCardInfo($physicsNo, $this->_cardType['spring']);
        if ($card) {
            //存在账号信息
            if ($card['status'] == 1) {
                //已使用状态
                $this->apiReturn(400, [], '物理卡号已存在！');
            } else {
                //卡未使用状态
                //判断手牌号是否唯一
                $this->checkVisible($visibleNo, $this->_sid);

                //新增手牌号
                $res = $this->_hotSpringModel->createHotSpringCard($card['id'], $visibleNo, $color, $salerid,
                    $this->_sid, 1, 0, time());
                //更新物理卡为使用状态
                $res1 = $this->_baseCardModel->setCardStatus($card['id'], 1);

                if ($res && $res1) {
                    $logHotSpring = [
                        'create' => [
                            'card_id'    => $card['id'],
                            'visible_no' => $visibleNo,
                            'color'      => $color,
                            'salerid'    => $salerid,
                            'apply_did'  => $this->_sid,
                            'status'     => 1,
                            'mark'       => 0,
                            'time'       => time(),
                        ],
                    ];
                    pft_log($this->_logHotSpring, json_encode($logHotSpring));
                    $this->apiReturn(200, [], '账号信息录入成功！');
                } else {
                    $this->apiReturn(500, [], '系统错误！');
                }
            }
        } else {
            //不存在账号信息，新建物理卡
            //判断物理卡号是否唯一
            $this->checkCreatePhysics($physicsNo);
            //判断手牌号是否唯一
            $this->checkVisible($visibleNo, $this->_sid);
            //var_dump($physicsNo, $visibleNo, $salerid);die;

            //添加物理卡和温泉手牌号
            $res1     = $this->_baseCardModel->createBaseCard($physicsNo, 4, time());
            $cardInfo = $this->_baseCardModel->getCardInfo($physicsNo);
            $res2     = $this->_hotSpringModel->createHotSpringCard($cardInfo['id'], $visibleNo, $color, $salerid,
                $this->_sid, 1, 0, time());
            if ($res1 && $res2) {
                $logBase      = [
                    'create' => [
                        'physics_no' => $physicsNo,
                        'type'       => 4,
                        'time'       => time(),
                    ],
                ];
                $logHotSpring = [
                    'create' => [
                        'card_id'    => $cardInfo['id'],
                        'visible_no' => $visibleNo,
                        'color'      => $color,
                        'salerid'    => $salerid,
                        'apply_did'  => $this->_sid,
                        'status'     => 1,
                        'mark'       => 0,
                        'time'       => time(),
                    ],
                ];
                pft_log($this->_logBaseCard, json_encode($logBase));
                pft_log($this->_logHotSpring, json_encode($logHotSpring));
                $this->apiReturn(200, [], '账号信息录入成功！');
            } else {
                $this->apiReturn(500, [], '系统错误！');
            }
        }
    }

    /**
     * 判断物理卡号格式和唯一性（接口调用）
     * <AUTHOR>
     * @date   2016-11-25
     *
     * @param  string  $physics_no  物理卡号
     *
     * @return
     */
    public function checkPhysicsCard()
    {
        $physicsNo = I('post.physics_no', '', 'strval');
        if (!preg_match('/^[0-9a-zA-Z]{1,20}$/i', $physicsNo)) {
            $this->apiReturn(400, [], '物理卡号参数错误');
        }
        $res = $this->_baseCardModel->getCardInfo($physicsNo, false, 1);
        if ($res) {
            $this->apiReturn(400, [], '物理卡号已存在！');
        } else {
            $this->apiReturn(200, [], '账号合法');
        }
    }

    /**
     * 判断物理卡号格式和唯一性（内部调用）
     * <AUTHOR>
     * @date   2016-11-25
     *
     * @param  string  $physicsNo  物理卡号
     *
     * @return
     */
    public function checkPhysics($physicsNo)
    {
        if (!preg_match('/^[0-9a-zA-Z]{1,20}$/i', $physicsNo)) {
            $this->apiReturn(400, [], '物理卡号参数错误');
        }
        $res = $this->_baseCardModel->getCardInfo($physicsNo, $this->_cardType['spring']);
        if ($res) {
            //存在这个物理卡号
            if ($res['status'] == 1) {
                //且物理卡在使用中
                $this->apiReturn(400, [], '物理卡号已存在！');
            } elseif ($res['status'] == 0) {
                //未使用的状态
                return $res;
            }
        } else {
            //没有这个物理卡号
            return [];
        }
    }

    /**
     * 新增物理卡号判断唯一性
     * <AUTHOR>
     * @date   2016-11-25
     *
     * @param  string  $physicsNo  物理卡号
     *
     * @return
     */
    public function checkCreatePhysics($physicsNo)
    {
        if (!preg_match('/^[0-9a-zA-Z]{1,20}$/i', $physicsNo)) {
            $this->apiReturn(400, [], '物理卡号参数错误');
        }
        $res = $this->_baseCardModel->getCardInfo($physicsNo);
        if ($res) {
            $this->apiReturn(400, [], '物理卡号已存在！');
        }
    }

    /**
     * 判断手牌号的格式和唯一性(接口调用)
     * <AUTHOR>
     * @date   2016-11-25
     *
     * @param  int  $visible_no  手牌号
     * @param  int  $salerid  产品的salerid
     *
     * @return
     */
    public function checkVisibleNo()
    {
        $visibleNo = I('post.visible_no', '', 'strval');
        //$salerid   = I('post.salerid', 0, 'intval');
        if (!preg_match('/^[0-9a-zA-Z]{1,20}$/i', $visibleNo)) {
            $this->apiReturn(400, [], '手牌号参数错误');
        }
        //if ($salerid == 0) {
        //    $this->apiReturn(400, [], '景区salerid参数错误');
        //}
        $res = $this->_hotSpringModel->checkHotSpringCard($visibleNo, $this->_sid);
        if ($res) {
            $this->apiReturn(400, [], '手牌号已存在！');
        } else {
            $this->apiReturn(200, [], '账号合法');
        }
    }

    /**
     * 判断手牌号的格式和唯一性(内部调用)
     * <AUTHOR>
     * @date   2016-11-25
     *
     * @param  string  $visibleNo  手牌号
     * @param  int  $applyDid  供应商ID
     *
     * @return
     */
    public function checkVisible($visibleNo, $applyDid)
    {
        if (!preg_match('/^[0-9a-zA-Z]{1,20}$/i', $visibleNo)) {
            $this->apiReturn(400, [], '手牌号参数错误');
        }
        $res = $this->_hotSpringModel->checkHotSpringCard($visibleNo, $applyDid);
        if ($res) {
            $this->apiReturn(400, [], '手牌号已存在！');
        }
    }

    /**
     * 手牌挂失
     * <AUTHOR>
     * @date   2016-12-16
     *
     * @params int  $presuming_id  温泉卡表的id
     *
     * @return
     */
    public function lossCard()
    {
        $presumingId = I('post.presuming_id', 0, 'intval');
        if (!$presumingId) {
            $this->apiReturn(400, [], '参数错误');
        }

        //获取卡的信息
        $presumingInfo = $this->_hotSpringModel->getInfoById($presumingId);
        if (!$presumingInfo) {
            $this->apiReturn(401, [], '获取不到手牌信息');
        }
        $salerId = $presumingInfo['salerid'];
        if ($salerId) {
            //权限判断
            $isAuth = $this->_hotSpringModel->isAuth($this->_sid, $salerId);
            if (!$isAuth) {
                $this->apiReturn(403, [], '没有权限');
            }
        }

        //判断状态
        $status = $presumingInfo['status'];
        if (!in_array($status, [1, 2])) {
            $this->apiReturn(401, [], '手牌不能挂失');
        }

        //具体挂失
        $res = $this->_hotSpringModel->lossPresuming($presumingId);
        if (!$res) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            $log = [
                'update_status' => [
                    'id'     => $presumingId,
                    'status' => '3',
                    'info'   => 'lossCard',
                    'time'   => time(),
                ],
            ];
            pft_log($this->_logHotSpring, json_encode($log));
            $this->apiReturn(200, [], '挂失成功');
        }
    }

    /**
     * 删除温泉卡
     * <AUTHOR>
     * @date   2016-12-16
     *
     * @params int  $presuming_id  温泉卡表的id
     *
     * @return
     */
    public function deleteCard()
    {
        $presumingId = I('post.presuming_id', 0, 'intval');
        if (!$presumingId) {
            $this->apiReturn(400, [], '参数错误');
        }

        //获取卡的信息
        $presumingInfo = $this->_hotSpringModel->getInfoById($presumingId);
        if (!$presumingInfo) {
            $this->apiReturn(401, [], '获取不到手牌信息');
        }
        //$salerId = $presumingInfo['salerid'];
        //判断状态
        $status = $presumingInfo['status'];
        if (!in_array($status, [1, 3])) {
            //正常，已挂失状态才能删除
            $this->apiReturn(401, [], '手牌不能删除');
        }
        //权限判断
        if ($this->_sid != $presumingInfo['apply_did']) {
            $this->apiReturn(403, [], '没有权限');
        }

        //删除操作
        $res = $this->_hotSpringModel->deleteCard($presumingInfo['card_id'], $presumingInfo['id']);
        if ($res) {
            $logBase = [
                'delete' => [
                    'id'     => $presumingInfo['card_id'],
                    'status' => 0,
                    'time'   => time(),
                ],
            ];
            pft_log($this->_logBaseCard, json_encode($logBase));
            pft_log($this->_logHotSpring, "删除手牌##$presumingId");
            $this->apiReturn(200, [], '删除成功');
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }

    /**
     * 禁用温泉卡
     * <AUTHOR>
     * @date   2016-12-16
     *
     * @params int  $presuming_id  温泉卡表的id
     *
     * @return
     */

    public function forbidCard()
    {
        $presumingId = I('post.presuming_id', 0, 'intval');
        if (!$presumingId) {
            $this->apiReturn(400, [], '参数错误');
        }

        //获取卡的信息
        $presumingInfo = $this->_hotSpringModel->getInfoById($presumingId);
        if (!$presumingInfo) {
            $this->apiReturn(401, [], '获取不到手牌信息');
        }
        $salerId = $presumingInfo['salerid'];

        //判断状态
        $status = $presumingInfo['status'];
        if ($status != 1) {
            //正常状态才能禁用
            $this->apiReturn(401, [], '手牌不能禁用');
        }
        if ($salerId) {
            //权限判断
            $isAuth = $this->_hotSpringModel->isAuth($this->_sid, $salerId);
            if (!$isAuth) {
                $this->apiReturn(403, [], '没有权限');
            }
        }

        $res = $this->_hotSpringModel->forbidPresuming($presumingId);
        if (!$res) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            $log = [
                'update_status' => [
                    'id'     => $presumingId,
                    'status' => '4',
                    'info'   => 'forbidCard',
                    'time'   => time(),
                ],
            ];
            pft_log($this->_logHotSpring, json_encode($log));
            $this->apiReturn(200, [], '禁用成功');
        }
    }

    /**
     * 解除温泉卡禁用状态
     * <AUTHOR>
     * @date   2016-12-16
     *
     * @params int  $presuming_id  温泉卡表的id
     *
     * @return
     */
    public function recoverForbid()
    {
        $presumingId = I('post.presuming_id', 0, 'intval');
        if (!$presumingId) {
            $this->apiReturn(400, [], '参数错误');
        }

        //获取卡的信息
        $presumingInfo = $this->_hotSpringModel->getInfoById($presumingId);
        if (!$presumingInfo) {
            $this->apiReturn(401, [], '获取不到手牌信息');
        }
        $salerId = $presumingInfo['salerid'];

        //判断状态
        $status = $presumingInfo['status'];
        if ($status != 4) {
            //禁用状态才能恢复
            $this->apiReturn(401, [], '手牌不需要恢复禁用状态');
        }
        if ($salerId) {
            //权限判断
            $isAuth = $this->_hotSpringModel->isAuth($this->_sid, $salerId);
            if (!$isAuth) {
                $this->apiReturn(403, [], '没有权限');
            }
        }

        $res = $this->_hotSpringModel->recoverForbid($presumingId);
        if (!$res) {
            $this->apiReturn(500, [], '系统错误');
        } else {
            $log = [
                'update_status' => [
                    'id'     => $presumingId,
                    'status' => '1',
                    'info'   => 'recoverForbid',
                    'time'   => time(),
                ],
            ];
            pft_log($this->_logHotSpring, json_encode($log));
            $this->apiReturn(200, [], '解除禁用成功');
        }
    }

    /**
     * 恢复温泉卡挂失状态
     * <AUTHOR>
     * @date   2016-12-16
     *
     * @params int  $presuming_id  温泉卡表的id
     *
     * @return
     */
    public function recoverCard()
    {
        $presumingId = I('post.presuming_id', 0, 'intval');
        if (!$presumingId) {
            $this->apiReturn(400, [], '参数错误');
        }

        //获取卡的信息
        $presumingInfo = $this->_hotSpringModel->getInfoById($presumingId);
        if (!$presumingInfo) {
            $this->apiReturn(401, [], '获取不到手牌信息');
        }
        $salerId = $presumingInfo['salerid'];
        $status  = $presumingInfo['status'];

        //状态判断
        if ($status != 3) {
            $this->apiReturn(401, [], '手牌不需要恢复挂失');
        }
        if ($salerId) {
            //权限判断
            $isAuth = $this->_hotSpringModel->isAuth($this->_sid, $salerId);
            if (!$isAuth) {
                $this->apiReturn(403, [], '没有权限');
            }
        }


        $res = $this->_hotSpringModel->recoverLoss($presumingId);
        if (!$res) {
            $this->apiReturn(403, [], '恢复挂失失败');
        } else {
            $this->apiReturn(200, [], '恢复挂失成功');
        }
    }

    /**
     * 搜索手牌信息列表
     * <AUTHOR>
     * @date   2016-11-25
     *
     * @param  string  $physicsOrVisible  物理卡id或手牌号 模糊查询
     * @param  int  $status  卡状态(1=正常 2=已绑定 3=已挂失 4=已禁用)
     * @param  int  $color  手牌颜色
     * @param  int  $salerid  景区salerid
     * @param  int  $page  页码
     * @param  int  $size  条数
     * @param  int  $allPage  总页数
     *
     * @return array
     */
    public function searchHotSpringCard()
    {
        $physicsOrVisible = I('post.physicsOrVisible', '', 'strval');
        $status           = I('post.status', -1, 'intval');
        $color            = I('post.color', -1, 'intval');
        $salerid          = I('post.salerid', -1, 'intval');
        $page             = I('post.page', 1, 'intval');
        $size             = I('post.size', 20, 'intval');

        $visible   = $physicsOrVisible;
        $cardIdArr = [];
        if ($physicsOrVisible != '') {
            $cardIdArr = $this->_baseCardModel->getCardIdArrByPhysicsNo($physicsOrVisible);
        }

        $res = $this->_hotSpringModel->searchHotSpringCard($cardIdArr, $visible, $status, $color, $salerid, $page,
            $size, $this->_sid);

        if (!$res) {
            $this->apiReturn(204, [], '暂无查询结果');
        } else {
            //获取物理卡号physics_no
            $resCardIdArr = array_column($res['list'], 'card_id');
            $baseCardList = $this->_baseCardModel->getCardById($resCardIdArr);

            //预消费模型
            $presumingModel = new PresumingModel();

            foreach ($res['list'] as $k => $v) {
                //获取绑定的订单
                $presumingId = $v['id'];
                $mark        = $v['mark'];
                $status      = $v['status'];
                $cardId      = $v['card_id'];

                //添加物理卡号
                $physicsNo = '';
                if (isset($baseCardList[$cardId])) {
                    $physicsNo = $baseCardList[$cardId]['physics_no'];
                }

                if ($status == 2) {
                    //使用中的手牌才获取信息
                    $tmp      = $presumingModel->getDetails($presumingId, $mark);
                    $ordernum = $tmp && $tmp['orderid'] ? $tmp['orderid'] : '';

                    $res['list'][$k]['ordernum'] = $ordernum;
                } else {
                    $res['list'][$k]['ordernum'] = '';
                }

                //$res['list'][$k]['color_name']  = $this->_color[$res['list'][$k]['color']];
                $res['list'][$k]['status_name'] = $this->_status[$res['list'][$k]['status']];
                $res['list'][$k]['physics_no']  = $physicsNo;
            }
            $this->apiReturn(200, $res, '查询成功');
        }
    }

    /**
     * 根据物理卡号获取手牌信息
     * <AUTHOR>
     * @date   2016-11-25
     *
     * @param  string  $physics_no  物理卡号
     *
     * @return array
     */
    public function getCardInfo()
    {
        $physicsNo = I('post.physics_no', '', 'strval');
        if (!$physicsNo) {
            $this->apiReturn(400, [], '参数错误');
        }

        $res = $this->_hotSpringModel->getInfoByPhysicsNo($physicsNo);
        if ($res && $res['apply_did'] == $this->_sid) {//判断手牌 是否是已发布的产品
            if ($res['status'] == 2) {
                //预消费模型
                $presumingModel = new PresumingModel();
                //使用中的手牌才获取信息
                $tmp      = $presumingModel->getDetails($res['id'], $res['mark']);
                $ordernum = $tmp && $tmp['orderid'] ? $tmp['orderid'] : '';

                $res['ordernum'] = $ordernum;
            } else {
                $res['ordernum'] = '';
            }
            $res['physics_no']  = $physicsNo;
            $res['color_name']  = $this->_color[$res['color']];
            $res['status_name'] = $this->_status[$res['status']];

            //获取景区名称
            //$landModel         = new Land();
            //$salerName         = $landModel->getInfoBySalerId($res['salerid'], 'title');

            $landApi   = new \Business\CommodityCenter\Land();
            $salerNameRes = $landApi->queryLandMultiQueryByAdminAndPaging([], 1, 1, '', 'id desc', false, [], [], [], [],
                [$res['salerid']]);

            $salerName = '';
            if ($salerNameRes['list']) {
                $salerName = $salerNameRes['list'][0];
            }

            $res['saler_name'] = $salerName['title'];

            $this->apiReturn(200, $res, '查询成功');
        } else {
            $this->apiReturn(204, [], '暂无查询结果');
        }
    }

    /**
     * 编辑手牌
     * <AUTHOR>
     * @date   2016-11-25
     *
     * @param  string  $new_physics  新物理卡id
     * @param  string  $old_physics  旧物理卡id
     * @param  string  $new_visible  新手牌号
     * @param  string  $old_visible  旧手牌号
     * @param  int  $old_color  旧手牌颜色
     * @param  int  $new_color  新手牌颜色
     * @param  int  $salerid  产品的salerid
     * @params int     $presuming_id  温泉卡表的id
     *
     * @return
     */
    public function editCard()
    {
        $newPhysics  = I('post.new_physics', '');
        $oldPhysics  = I('post.old_physics', '');
        $newVisible  = I('post.new_visible', '');
        $oldVisible  = I('post.old_visible', '');
        $newColor    = I('post.new_color', 1, 'intval');
        $oldColor    = I('post.old_color', 1, 'intval');
        $salerid     = I('post.salerid', 0, 'intval');
        $presumingId = I('post.presuming_id', 0, 'intval');

        if (empty($newPhysics) || empty($oldPhysics) || empty($newVisible) || empty($oldVisible) || !$presumingId) {
            $this->apiReturn(400, [], '有必填项未填！');
        }

        if (!preg_match('/^[0-9a-zA-Z]{1,20}$/i', $newVisible) || !preg_match('/^[0-9a-zA-Z]{1,20}$/i', $oldVisible)) {
            $this->apiReturn(400, [], '手牌号参数错误');
        }

        //获取卡的信息
        $presumingInfo = $this->_hotSpringModel->getInfoById($presumingId);
        if (!$presumingInfo) {
            $this->apiReturn(401, [], '获取不到手牌信息');
        }
        $salerId = $presumingInfo['salerid'];

        //判断状态
        $status = $presumingInfo['status'];
        if ($status != 1) {
            //正常状态才能编辑
            $this->apiReturn(401, [], '手牌不能编辑');
        }
        if ($salerId) {
            //权限判断
            $isAuth = $this->_hotSpringModel->isAuth($this->_sid, $salerId);
            if (!$isAuth) {
                $this->apiReturn(403, [], '没有权限');
            }
        }


        if ($oldPhysics != $newPhysics) {
            //物理卡号更换

            if ($oldVisible != $newVisible) {
                //手牌号更换了

                //判断物理卡号和手牌号的唯一性

                $info = $this->checkPhysics($newPhysics);

                $this->checkVisible($newVisible, $this->_sid);

                if (is_array($info) && count($info) > 0) {
                    //该物理卡号存在且未使用
                    //更新新物理卡为使用状态
                    $this->_baseCardModel->setCardStatus($info['id'], 1);
                } else {
                    //新物理卡号
                    //新建一张温泉卡
                    $this->_baseCardModel->createBaseCard($newPhysics, 4, time());
                }

                //先删除原先的物理卡号
                $this->_baseCardModel->deleteById($presumingInfo['card_id']);

                //获取新建卡的信息
                $newCardInfo = $this->_baseCardModel->getCardInfo($newPhysics);

                //更新温泉卡表的信息
                $res = $this->_hotSpringModel->updateHotSpringInfo($presumingInfo['card_id'], $newCardInfo['id'],
                    $newColor, $newVisible, time());

                if ($res) {
                    $logDeleteBase      = [
                        'delete' => [
                            'id'     => $presumingInfo['card_id'],
                            'status' => 0,
                            'time'   => time(),
                        ],
                    ];
                    $logCreateBase      = [
                        'create' => [
                            'id'   => $newCardInfo['id'],
                            'type' => 4,
                            'time' => time(),
                        ],
                    ];
                    $logUpdateHotSpring = [
                        'update_info' => [
                            'old_card' => $presumingInfo['card_id'],
                            'new_card' => $newCardInfo['id'],
                            'color'    => $newColor,
                            'visible'  => $newVisible,
                            'time'     => time(),
                        ],
                    ];
                    pft_log($this->_logBaseCard, json_encode($logDeleteBase));
                    pft_log($this->_logBaseCard, json_encode($logCreateBase));
                    pft_log($this->_logHotSpring, json_encode($logUpdateHotSpring));
                    $this->apiReturn(200, [], '更新信息成功');
                } else {
                    $this->apiReturn(500, [], '更新失败');
                }
            } else {
                //手牌号未更换
                //判断物理卡号的唯一性
                $info = $this->checkPhysics($newPhysics);

                if (is_array($info) && count($info) > 0) {
                    //该物理卡号存在且未使用
                    //更新新物理卡为使用状态
                    $this->_baseCardModel->setCardStatus($info['id'], 1);
                } else {
                    //新物理卡号
                    //新建一张温泉卡
                    $this->_baseCardModel->createBaseCard($newPhysics, 4, time());
                }

                //先删除原先的物理卡号
                $this->_baseCardModel->deleteById($presumingInfo['card_id']);

                //获取新建卡的信息
                $newCardInfo = $this->_baseCardModel->getCardInfo($newPhysics);
                //更新温泉卡表的信息
                $res = $this->_hotSpringModel->updateHotSpringInfo($presumingInfo['card_id'], $newCardInfo['id'],
                    $newColor, $oldVisible, time());
                if ($res) {
                    $logDeleteBase      = [
                        'delete' => [
                            'id'     => $presumingInfo['card_id'],
                            'status' => 0,
                            'time'   => time(),
                        ],
                    ];
                    $logCreateBase      = [
                        'create' => [
                            'id'   => $newCardInfo['id'],
                            'type' => 4,
                            'time' => time(),
                        ],
                    ];
                    $logUpdateHotSpring = [
                        'update_info' => [
                            'old_card' => $presumingInfo['card_id'],
                            'new_card' => $newCardInfo['id'],
                            'color'    => $newColor,
                            'visible'  => $newVisible,
                            'time'     => time(),
                        ],
                    ];
                    pft_log($this->_logBaseCard, json_encode($logDeleteBase));
                    pft_log($this->_logBaseCard, json_encode($logCreateBase));
                    pft_log($this->_logHotSpring, json_encode($logUpdateHotSpring));
                    $this->apiReturn(200, [], '更新信息成功');
                } else {
                    $this->apiReturn(500, [], '更新失败');
                }
            }

        } else {
            //只更改手牌表的信息

            if ($newVisible == $oldVisible) {
                //如果手牌号没更改
                if ($oldColor == $newColor) {
                    $this->apiReturn(200, [], '信息未做更改');
                } else {
                    $res = $this->_hotSpringModel->updateHotSpringInfo($presumingInfo['card_id'],
                        $presumingInfo['card_id'], $newColor, $newVisible, time());
                    if ($res) {

                        $logUpdateHotSpring = [
                            'update_info' => [
                                'old_card' => $presumingInfo['card_id'],
                                'new_card' => $presumingInfo['card_id'],
                                'color'    => $newColor,
                                'visible'  => $newVisible,
                                'time'     => time(),
                            ],
                        ];
                        pft_log($this->_logHotSpring, json_encode($logUpdateHotSpring));

                        $this->apiReturn(200, [], '更新信息成功');
                    } else {
                        $this->apiReturn(500, [], '更新失败');
                    }
                }
            } else {
                //手牌号更改了
                //先判断手牌号唯一性
                $this->checkVisible($newVisible, $this->_sid);
                //更新数据
                $res = $this->_hotSpringModel->updateHotSpringInfo($presumingInfo['card_id'], $presumingInfo['card_id'],
                    $newColor, $newVisible, time());
                if ($res) {

                    $logUpdateHotSpring = [
                        'update_info' => [
                            'old_card' => $presumingInfo['card_id'],
                            'new_card' => $presumingInfo['card_id'],
                            'color'    => $newColor,
                            'visible'  => $newVisible,
                            'time'     => time(),
                        ],
                    ];
                    pft_log($this->_logHotSpring, json_encode($logUpdateHotSpring));
                    $this->apiReturn(200, [], '更新信息成功');
                } else {
                    $this->apiReturn(500, [], '更新失败');
                }
            }
        }
    }

    /**
     * 补卡
     * <AUTHOR>
     * @date   2016-12-21
     *
     * @param  string  $new_physics  新物理卡id
     * @param  string  $old_physics  旧物理卡id
     * @param  int  $new_color  新手牌颜色
     * @param  int  $salerid  产品的salerid
     * @params int     $presuming_id  温泉卡表的id
     *
     * @return
     */
    public function replacePhysics()
    {
        $newPhysics  = I('post.new_physics', '');
        $oldPhysics  = I('post.old_physics', '');
        $newColor    = I('post.new_color', 1, 'intval');
        $salerid     = I('post.salerid', 0, 'intval');
        $presumingId = I('post.presuming_id', 0, 'intval');

        if (empty($newPhysics) || empty($oldPhysics) || !$presumingId) {
            $this->apiReturn(400, [], '有必填项未填！');
        }

        if (!preg_match('/^[0-9a-zA-Z]{1,20}$/i', $newPhysics) || !preg_match('/^[0-9a-zA-Z]{1,20}$/i', $oldPhysics)) {
            $this->apiReturn(400, [], '物理卡号参数错误');
        }

        if ($newPhysics == $oldPhysics) {
            $this->apiReturn(400, [], '物理卡号必须更换');
        }

        //获取卡的信息
        $presumingInfo = $this->_hotSpringModel->getInfoById($presumingId);
        if (!$presumingInfo) {
            $this->apiReturn(401, [], '获取不到手牌信息');
        }
        $salerId = $presumingInfo['salerid'];

        //判断状态
        $status = $presumingInfo['status'];
        if ($status != 3) {
            //挂失状态才能补卡
            $this->apiReturn(401, [], '不能补卡');
        }
        if ($salerId) {
            //权限判断
            $isAuth = $this->_hotSpringModel->isAuth($this->_sid, $salerId);
            if (!$isAuth) {
                $this->apiReturn(403, [], '没有权限');
            }
        }


        if ($oldPhysics != $newPhysics) {
            //物理卡号更换

            //判断物理卡号的唯一性
            $info = $this->checkPhysics($newPhysics);

            if (is_array($info) && count($info) > 0) {
                //该物理卡号存在且未使用
                //更新新物理卡为使用状态
                $this->_baseCardModel->setCardStatus($info['id'], 1);
            } else {
                //新物理卡号
                //新建一张温泉卡
                $this->_baseCardModel->createBaseCard($newPhysics, 4, time());
            }

            //删除原先的物理卡号
            $this->_baseCardModel->deleteById($presumingInfo['card_id']);

            //获取新建卡的信息
            $newCardInfo = $this->_baseCardModel->getCardInfo($newPhysics);
            //更新温泉卡表的信息
            $res = $this->_hotSpringModel->updateHotSpringInfo($presumingInfo['card_id'], $newCardInfo['id'], $newColor,
                '', time());

            $this->_hotSpringModel->recoverLoss($presumingId);
            if ($res) {
                $logDeleteBase      = [
                    'delete' => [
                        'id'     => $presumingInfo['card_id'],
                        'status' => 0,
                        'time'   => time(),
                    ],
                ];
                $logCreateBase      = [
                    'create' => [
                        'id'   => $newCardInfo['id'],
                        'type' => 4,
                        'time' => time(),
                    ],
                ];
                $logUpdateHotSpring = [
                    'update_info' => [
                        'old_card' => $presumingInfo['card_id'],
                        'new_card' => $newCardInfo['id'],
                        'color'    => $newColor,
                        'visible'  => $presumingInfo['visible_no'],
                        'time'     => time(),
                    ],
                ];
                pft_log($this->_logBaseCard, json_encode($logDeleteBase));
                pft_log($this->_logBaseCard, json_encode($logCreateBase));
                pft_log($this->_logHotSpring, json_encode($logUpdateHotSpring));
                $this->apiReturn(200, [], '补卡成功');
            } else {
                $this->apiReturn(500, [], '补卡失败');
            }
        } else {
            $this->apiReturn(500, [], '物理卡号必须更换');
        }
    }

    /**
     * 换手牌（更换手牌号，将订单等信息转移过来）
     * <AUTHOR>
     * @date   2016-12-20
     *
     * @param  string  $old_visible  旧手牌号
     * @param  string  $new_visible  新手牌号
     * @param  int  $presuming_id  温泉卡表的id
     *
     * @return
     */
    public function replaceVisible()
    {
        $newVisible  = I('new_visible', '');
        $oldVisible  = I('old_visible', '');
        $presumingId = I('presuming_id', 0, 'intval');

        if (empty($newVisible) || empty($oldVisible) || !$presumingId) {
            $this->apiReturn(400, [], '有必填项未填！');
        }

        if (!preg_match('/^[0-9a-zA-Z]{1,20}$/i', $newVisible) || !preg_match('/^[0-9a-zA-Z]{1,20}$/i', $oldVisible)) {
            $this->apiReturn(400, [], '手牌号参数错误');
        }

        if ($newVisible == $oldVisible) {
            $this->apiReturn(400, [], '手牌号未更换');
        }

        //获取旧手牌信息
        $presumingInfo = $this->_hotSpringModel->getInfoById($presumingId);

        if (!$presumingInfo) {
            $this->apiReturn(401, [], '获取不到旧手牌信息');
        }
        if ($presumingInfo['visible_no'] != $oldVisible) {
            $this->apiReturn(401, [], '给定信息有误');
        }

        $salerId = $presumingInfo['salerid'];

        //判断状态
        $status = $presumingInfo['status'];
        if ($status != 2) {
            //使用状态才能换手牌
            $this->apiReturn(401, [], '手牌不能更换');
        }
        if ($salerId) {
            //权限判断
            $isAuth = $this->_hotSpringModel->isAuth($this->_sid, $salerId);
            if (!$isAuth) {
                $this->apiReturn(403, [], '没有权限');
            }
        }
        //获取新手牌信息
        $newCardInfo = $this->_hotSpringModel->getInfoByVisibleNo($newVisible, $this->_sid);
        if (empty($newCardInfo)) {
            $this->apiReturn(400, [], '新手牌不存在');
        }
        if ($newCardInfo['status'] != 1) {
            $this->apiReturn(400, [], '新手牌不处于正常状态');
        }
        if ($newCardInfo['color'] != $presumingInfo['color']) {
            $this->apiReturn(400, [], '手牌颜色不一致!');
        }
        //这个判断是为了兼容旧版
        if ($newCardInfo['salerid'] != 0 && $salerId == $newCardInfo['salerid']) {
            $this->apiReturn(400, [], '手牌关联的产品不一致!');
        }

        $presumingModel = new PresumingModel();

        $res = $presumingModel->changeCard($newCardInfo['id'], $presumingId, $presumingInfo['mark']);
        //这里如果没有绑定订单的话，手牌不能更换
        if ($res) {
            $logNew = [
                'replace_visible' => [
                    'old_id' => $presumingId,
                    'new_id' => $newCardInfo['id'],
                    'mark'   => $presumingInfo['mark'],
                ],
            ];
            pft_log($this->_logHotSpring, json_encode($logNew));
            $this->apiReturn(200, [], '更换手牌成功');
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }

    /**
     * 检查所换的手牌的合法性
     * <AUTHOR>
     * @date   2016-12-21
     *
     * @param  string  $old_visible  旧手牌号
     * @param  string  $new_visible  新手牌号
     * @params int     $presuming_id   温泉卡表的id
     *
     * @return
     */
    public function checkReplaceVisible()
    {
        $newVisible  = I('new_visible', '');
        $oldVisible  = I('old_visible', '');
        $presumingId = I('presuming_id', 0, 'intval');

        if (!preg_match('/^[0-9a-zA-Z]{1,20}$/i', $newVisible) || !preg_match('/^[0-9a-zA-Z]{1,20}$/i',
                $oldVisible) || !$presumingId) {
            $this->apiReturn(400, [], '参数错误');
        }
        $presumingInfo = $this->_hotSpringModel->getInfoById($presumingId);
        if (!$presumingInfo) {
            $this->apiReturn(400, [], '原手牌号有误');
        }

        $salerId = $presumingInfo['salerid'];
        //获取新手牌信息
        $newCardInfo = $this->_hotSpringModel->getInfoByVisibleNo($newVisible, $this->_sid);
        if (empty($newCardInfo)) {
            $this->apiReturn(400, [], '新手牌不存在');
        }
        if ($newCardInfo['status'] != 1) {
            $this->apiReturn(400, [], '新手牌不处于正常状态');
        }
        if ($newCardInfo['color'] != $presumingInfo['color']) {
            $this->apiReturn(400, [], '手牌颜色不一致!');
        }
        //这个判断是为了兼容旧版
        if ($newCardInfo['salerid'] != 0 && $salerId == $newCardInfo['salerid']) {
            $this->apiReturn(400, [], '手牌关联的产品不一致!');
        }

        $this->apiReturn(200, [], '手牌号合法');
    }

    /**
     * 冻结手牌
     *
     * @return array
     * <AUTHOR>
     * @date 2021/8/17
     *
     *
     */
    public function freezeCard()
    {
        $physicalNo = I('post.physics_no', '', "strval"); //温泉卡物理卡号
        if (empty($physicalNo)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $presumingBz = new Presuming();
        $result      = $presumingBz->freezeCard($physicalNo, $this->_sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 解冻手牌
     *
     * @return array
     * <AUTHOR>
     * @date 2021/8/17
     *
     */
    public function unFreezeCard()
    {
        $physicalNo = I('post.physics_no', '', "strval"); //温泉卡物理卡号
        if (empty($physicalNo)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $presumingBz = new Presuming();
        $result      = $presumingBz->unFreezeCard($physicalNo, $this->_sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取报表信息
     *
     * @date 2022/05/05
     * @auther yangjianhui
     * @return array
     */
    public function getReportInfo()
    {
        $payMode  = I('pay_way', '-1', 'strval');
        $lid      = I('lid', '', 'strval');
        $tid      = I('tid', '', 'strval');
        $saleOpId = I('operate_id', '', 'strval');
        $siteId   = I('site_id', '', 'strval');
        $begin    = I('begin_date', '', 'strval');
        $end      = I('end_date', '', 'strval');
        $type     = I('type', 1, 'intval');
        $configId = I('config_id', 1, 'intval');
        $page     = I('page', 1, 'intval');
        $size     = I('size', 10, 'intval');
        $excel    = I('excel', 0, 'intval'); //是否导出excel
        if (!strtotime($begin) || !strtotime($end)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $presumingJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\Presuming();
        $result      = $presumingJsonRpc->getReportInfo($this->_sid, $begin, $end, $type, $payMode, $lid, 
            $tid, $saleOpId, $siteId, $configId, $excel, $page, $size);
        if ($excel) {
            $this->_exportReport($result['data'], $configId);
        }


        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 添加手牌颜色
     *
     * @date 2022/05/06
     * @auther yangjianhui
     * @return array
     */
    public function addColor()
    {
        $color     = I('post.color', '', 'strval');
        $useMember = I('post.use_member', '', 'strval');
        if (empty($color)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $presumingJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\Presuming();
        $result      = $presumingJsonRpc->addColor($this->_sid, $color, $useMember);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取手牌颜色列表
     *
     * @date 2022/05/06
     * @auther yangjianhui
     * @return array
     */
    public function getColorList()
    {
        $page     = I('post.page', 1, 'intval');
        $size     = I('post.size', 10, 'intval');

        $presumingJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\Presuming();
        $result      = $presumingJsonRpc->getColorList($this->_sid, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 修改手牌颜色状态
     *
     * @date 2022/05/06
     * @auther yangjianhui
     * @return array
     */
    public function changeColorStatus()
    {
        $id               = I('post.id', 0, 'intval');
        $status           = I('post.status', 1, 'intval');
        if (empty($id)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $presumingJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\Presuming();
        $result           = $presumingJsonRpc->changeColorStatus($this->_sid, $id, $status);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 设置手牌颜色
     *
     * @date 2022/05/06
     * @auther yangjianhui
     * @return array
     */
    public function setPresumingColor()
    {
        $id      = I('post.id', 0, 'intval');
        $colorId = I('post.color_id', 0, 'intval');

        if (empty($id) || empty($colorId)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $presumingJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\Presuming();
        $result           = $presumingJsonRpc->setPresumingColor($this->_sid, $id, $colorId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取商家的全部手牌颜色
     *
     * @date 2022/05/06
     * @auther yangjianhui
     * @return array
     */
    public function getAllColorList()
    {
        $presumingJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\Presuming();
        $result      = $presumingJsonRpc->getAllColorList($this->_sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取卡的发放统计数量
     *
     * @date 2022/05/07
     * @auther yangjianhui
     * @return array
     */
    public function getIssueCardSummary()
    {
        $begin = I('begin_date', '', 'strval');
        $end   = I('end_date', '', 'strval');
        $type  = I('type', 1, 'intval'); //汇总类型 1：按日 2：按月
        $excel = I('excel', 0, 'intval'); //是否导出excel 1：是 0：否
        if (!strtotime($begin) || !strtotime($end)) {
            $this->apiReturn(203, [], "参数错误");
        }
        //按月汇总只支持12个月
        if ($type == 2 && strtotime($end) - strtotime($begin) >  31622400) {
            $this->apiReturn(203, [], "时间跨度过大，仅支持12个月查询");
        }
        //按日汇总只支持12个月
        if ($type == 1 && strtotime($end) - strtotime($begin) >  7948800) {
            $this->apiReturn(203, [], "时间跨度过大，仅支持3个月查询");
        }
        $presumingJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\Presuming();
        $result           = $presumingJsonRpc->getIssueCardSummary($this->_sid, $begin, $end, $type);
        $returnData       = [];
        foreach ($result['data'] as $value) {
            $returnData = array_merge($returnData, $value);
        }
        if ($excel) {
            $head = [
                '日期',
                '手牌颜色',
                '用户群体',
                '发放数'
            ];
            array_unshift($returnData, $head);
            $xls  = new SimpleExcel('UTF-8', true, 'orderList');
            $file = "手牌发放报表";
            $xls->addArray($returnData);

            $xls->generateXML($file);
            exit;
        }

        $this->apiReturn($result['code'], $returnData, $result['msg']);
    }

    private function _exportReport($data, $configId)
    {
        $templateModel = new \Model\Report\Template();
        $template      = $templateModel->getTemplateById(0, $configId);
        $item          = json_decode($template['item'], true);
        $itemNum       = count($item);
        $filename      = date('Ymd') . '手牌报表';
        //数据列表
        $list = $data['list'];
        //报表名称
        $name = $data['name'];
        //纬度
        $title = $data['title'];
        //总和
        $sum = $data['sum'];
        //统计指标
        $target = $data['target'];
        //汇总指标
        // $summaryTarget = $data['summary_target'];
        $summaryTarget = [];

        if (empty($target)) {
            $target = [];
        }
        $excel[0] = [
            $name,
        ];

        //配置的纬度
        foreach ($title as $value) {
            //纬度
            $excel[1][] = $value;
            //总计
            if ($value !== end($title)) {
                $excel[2][] = '';
            } else {
                $excel[2][] = '总计：';
            }
        }
        $needTarget = [
            'ticket_num'          => '总票数',
            'sale_money'          => '订单金额',
            'time_out_minutes'    => '超时时长',
            'time_out_money'      => '超时金额',
            'free_time_out_money' => '超时免缴金额',
            'sum_money'           => '总金额',
        ];
        //优化导出报表展示数字0
        foreach ($target as $key => $value) {
            if (isset($needTarget[$value])) {
                $excel[1][] = $needTarget[$value];
                $excel[2][] = isset($sum[$value]) ? $sum[$value] : '-';
            }
        }
        $i = 3;
        foreach ($list as $valueOne) {
            switch ($itemNum) {
                case 1:
                    $excel[$i][] = $valueOne['title'];
                    foreach ($target as $item) {
                        $excel[$i][] = $valueOne['list'][$item];
                    }
                    $i++;
                    break;
                case 2:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        $excel[$i][] = $valueOne['title'];
                        $excel[$i][] = $valueTwo['title'];
                        foreach ($target as $item) {
                            $excel[$i][] = $valueTwo['list'][$item];
                        }
                        $i++;
                    }

                    break;
                case 3:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            $excel[$i][] = $valueOne['title'];
                            $excel[$i][] = $valueTwo['title'];
                            $excel[$i][] = $valueThree['title'];
                            foreach ($target as $item) {
                                $excel[$i][] = $valueThree['list'][$item];
                            }
                            $i++;
                        }
                    }
                    break;
                case 4:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                $excel[$i][] = $valueOne['title'];
                                $excel[$i][] = $valueTwo['title'];
                                $excel[$i][] = $valueThree['title'];
                                $excel[$i][] = $valueFour['title'];
                                foreach ($target as $item) {
                                    $excel[$i][] = $valueFour['list'][$item];
                                }
                                $i++;
                            }
                        }
                    }
                    break;
                case 5:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    $excel[$i][] = $valueOne['title'];
                                    $excel[$i][] = $valueTwo['title'];
                                    $excel[$i][] = $valueThree['title'];
                                    $excel[$i][] = $valueFour['title'];
                                    $excel[$i][] = $valueFive['title'];
                                    foreach ($target as $item) {
                                        $excel[$i][] = $valueFive['list'][$item];
                                    }
                                    $i++;
                                }
                            }
                        }
                    }
                    break;
                case 6:
                    foreach ($valueOne['list'] as $keyTwo => $valueTwo) {
                        foreach ($valueTwo['list'] as $keyThree => $valueThree) {
                            foreach ($valueThree['list'] as $keyFour => $valueFour) {
                                foreach ($valueFour['list'] as $keyFive => $valueFive) {
                                    foreach ($valueFive['list'] as $keySix => $valueSix) {
                                        $excel[$i][] = $valueOne['title'];
                                        $excel[$i][] = $valueTwo['title'];
                                        $excel[$i][] = $valueThree['title'];
                                        $excel[$i][] = $valueFour['title'];
                                        $excel[$i][] = $valueFive['title'];
                                        $excel[$i][] = $valueSix['title'];
                                        foreach ($target as $item) {
                                            $excel[$i][] = $valueSix['list'][$item];
                                        }
                                        $i++;
                                    }
                                }
                            }
                        }
                    }
                    break;
            }
        }

        $xls = new SimpleExcel('UTF-8', true, 'orderList');
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }

    /**
     * 获取配置信息
     *
     * @date 2023/12/18
     * @auther yangjianhui
     * @return array
     */
    public function getConfig()
    {
        $presumingJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\Presuming();
        $result      = $presumingJsonRpc->getConfig($this->_sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 修改配置信息
     *
     * @date 2023/12/18
     * @auther yangjianhui
     * @return array
     */
    public function addOrEditConfig()
    {
        $config = I('post.config');
        if (empty($config)) {
            $this->apiReturn(203, [], "配置信息有误");
        }
        $config = html_entity_decode($config);
        $presumingJsonRpc = new \Business\JsonRpcApi\ScenicLocalService\Presuming();
        $result      = $presumingJsonRpc->addOrEditConfig($this->_sid, $config);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}