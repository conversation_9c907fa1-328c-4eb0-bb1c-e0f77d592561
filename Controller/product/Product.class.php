<?php
/**
 * Created by PhpStorm.
 * User: cgp
 * Date: 16/4/30
 * Time: 09:29
 */

namespace Controller\product;

use App\Tools\ArrayUtil;
use Business\AnnualCard\LandService;
use Business\AppCenter\Module;
use Business\Approval\ApprovalManage;
use Business\Authority\AuthLogic as AuthLogicBiz;
use Business\Authority\AuthContext;
use Business\Admin\ModuleConfig;
use Business\Authority\DataAuthLogic;
use Business\CommodityCenter\Ticket as ticketBiz;
use Business\JavaApi\ListService\LandListQueryService;
use Business\JavaApi\Member\MemberQuery;
use Business\JavaApi\Member\MemberRelationQuery;
use Business\JavaApi\Product\EvoluteGroupSpecialityService;
use Business\JavaApi\Product\EvoluteListQuery;
use Business\JavaApi\Product\PackageTicket;
use Business\JavaApi\ProductApi;
use Business\JavaApi\Ticket\Price;
use Business\JavaApi\Ticket\TicketExtendAttr;
use Business\JavaApi\TicketApi;
use Business\JsonRpcApi\TravelVoucherService\VoucherService;
use Business\Member\Authority;
use Business\MemberLogin\MemberLoginHelper;
use Business\NewJavaApi\Product\ProductLabels;
use Business\Notice\WxConfig;
use Business\Order\OrderAidsSplitQuery;
use Business\Order\OrderList;
use Business\Order\OrderSearch;
use Business\PackTicket\PackRelation;
use Business\Product\Hotel;
use Business\Product\PackTicket;
use Business\Product\Performance;
use Business\Product\ProductTag as ProductTagBiz;
use Business\Product\ProductZgyList;
use Business\Product\SubProduct as SubProductBiz;
use Business\Product\TimeShare as TimeShareLib;
use Exception;
use Library\Business\ReadExcel;
use Library\Cache\Cache;
use Library\Controller;
use Library\Resque\Queue;
use Library\SimpleExcel;
use Library\Tools;
use Model\AppCenter\ModuleList;
use Model\Member\Member;
use Model\Order\OrderTools;
use Model\Ota\ApiTbTicketCodeConfModel;
use Model\Ota\SysConfig;
use Model\Product\Land;
use Model\Product\LandResource;
use Model\Product\MemberCard;
use Model\Product\MemberCardConf;
use Model\Product\Show;
use Model\Product\Supplier;
use Model\Product\Terminal;
use Model\Report\SeparateConfig;
use Model\SystemLog\OptLog;
use Model\Taobao\Taobao;
use Process\CodeConst;
use Process\Product\LandProcess;
use Process\Product\ProductZgyList as businessLib;
use Business\Product\ExchangeCouponProduct as ExchangeCouponProductBiz;

class Product extends Controller
{
    private $config;
    private $_sid       = null;
    private $_loginInfo = null;

    //所有的产品类型
    private $_productType = ['A', 'B', 'C', 'F', 'G', 'H', 'I', 'K', 'L'];

    public function __construct()
    {
        $this->config     = include __DIR__ . '/../../Conf/product.conf.php';
        $this->_sid       = $this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
    }

    /**
     * 获取产品信息
     * <AUTHOR>
     * @date   2017-12-07
     */
    public function getProuctInfo()
    {
        $lid = I('lid', 0, 'intval');

        if (!$lid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $productBiz = new \Business\Product\Product();

        $result = $productBiz->getProductInfo($this->_sid, $lid);

        if ($result['code'] == 200) {
            $this->apiReturn(200, $result['data']);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }

    }

    public function upgradeTerminalType()
    {
        $userInfo = $this->getLoginInfo();

        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }

        $lid      = I('post.land_id');
        $model    = new Land();
        $productService = new \Business\Product\Product();
	    $res = $productService->changeTerminalType($lid, $userInfo['sid'], $userInfo['sdtype']);
        //$res      = $model->changeTerminalCodeTpe($lid, $userInfo['sid'], $userInfo['sdtype']);
        if ($res['code'] == 200) {
            // 操作日志
            $optLog = new OptLog();
            $optLog->StuffOptLog(
                $userInfo['memberID'],
                $userInfo['sid'],
                "修改凭证号类型,旧类型为:{$res['data']['oldtype']},新旧类型为:{$res['data']['newtype']}");
            $cache   = Cache::getInstance('redis');
            $landKey = "news_order_land:{$lid}";
            $cache->rm($landKey);
            // 重新生成池子里面的凭证
            $jobId = Queue::push('backend', 'Backend_Job', [
                'action' => 'regenerateOrderCode',
                'lid'    => $lid,
            ]);

            $this->apiReturn(200, [], '配置成功立即生效');
        }
        $this->apiReturn(204, [], $res['msg']);

    }

    /**
     * 新增/保存产品信息
     * <AUTHOR>
     * @date   2017-06-29
     */
    public function saveProduct()
    {
        //必要参数
        $necessary = $this->_parseNecessaryAttr();
        if ($necessary['status'] == 0) {
            $this->apiReturn(204, [], $necessary['msg']);
        }

        $necessary = $necessary['data'];
        $authBiz   = new AuthContext();
        $subSid    = 0;
        $sid       = $this->_sid;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $sid    = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            if (!in_array($necessary['ptype'], ['A', 'J'])){
                $this->apiReturn(403, [], '无发布该类型产品门票权限！');
            }
        }
        //发布产品权限校验  -xiexy 2020-01-03
        $proTypeAuth = $authBiz->memberProductTypeAuth($sid);
        if (!in_array($necessary['ptype'], $proTypeAuth)) {
            $this->apiReturn(403, [], '无发布该类型产品门票权限！');
        }
        //可选参数
        $optional = $this->_parseOptionalAttr($necessary['ptype']);

        //扩展属性 （针对java）
        $extend = $this->_parseOptionalExtend();

        if ($optional['status'] == 0) {
            $this->apiReturn(204, [], $optional['msg']);
        }
        $optional = $optional['data'];

        //产品类型,可能存在子产品类型，这边做下兼容
        if (isset($optional['p_type'])) {
            //重置下产品类型
            $necessary['ptype'] = $optional['p_type'];
            unset($optional['p_type']);
        }

        //操作人
        $op = $this->_loginInfo['memberID'];

        $productBiz = new \Business\Product\Product();
        $annualBiz  = new LandService();
        //数据权限
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();

        if ($necessary['pro_id']) {
            //数据权限
            if ($dataAuthLimit->hasLidBeenLimit($necessary['pro_id'])) {
                $this->apiReturn(403, [], '无相关数据权限，不可发布产品！');
            }
            //发布产品数量限制
            $checkNumLimit = $authBiz->releaseProductNumLimit($sid, true);
            if (!$checkNumLimit['res']) {
                $this->apiReturn(403, [], $checkNumLimit['msg']);
            }
            if ($necessary['ptype'] == 'I') {
                $result = $annualBiz->updateAnnualProduct($sid, $op, $necessary['pro_id'], $necessary['title'], $necessary['ptype'],
                    $necessary['level'], $necessary['address'], $necessary['province'], $necessary['city'], $necessary['notice'],
                    $necessary['details'], $necessary['thumb_img'], 1, $optional, $extend, $necessary['oversea']);
            } else {
                //更新
                $result = $productBiz->update(
                    $sid,
                    $op,
                    $necessary['pro_id'],
                    $necessary['title'],
                    $necessary['ptype'],
                    $necessary['level'],
                    $necessary['address'],
                    $necessary['province'],
                    $necessary['city'],
                    $necessary['notice'],
                    $necessary['details'],
                    $necessary['thumb_img'],
                    1,
                    $optional,
                    [],
                    $extend,
                    $necessary['oversea'],
                    $subSid,
                    $necessary['zoneId']
                );
            }

        } else {
            //数据权限
            if ($dataAuthLimit->hasLimit()) {
                $this->apiReturn(403, [], '无相关数据权限，不可发布产品！');
            }
            //发布产品数量限制
            $checkNumLimit = $authBiz->releaseProductNumLimit($sid);
            if (!$checkNumLimit['res']) {
                $this->apiReturn(403, [], $checkNumLimit['msg']);
            }

            if ($necessary['ptype'] == 'I') {
                $result = $annualBiz->createAnnualProduct($sid, $op, $necessary['title'], $necessary['ptype'], $necessary['level'],
                    $necessary['address'], $necessary['province'], $necessary['city'], $necessary['notice'], $necessary['details'],
                    $necessary['thumb_img'], 1, $optional, $extend, $necessary['oversea']);
            } else {
                //新增
                $result = $productBiz->publish(
                    $sid,
                    $op,
                    $necessary['title'],
                    $necessary['ptype'],
                    $necessary['level'],
                    $necessary['address'],
                    $necessary['province'],
                    $necessary['city'],
                    $necessary['notice'],
                    $necessary['details'],
                    $necessary['thumb_img'],
                    1,
                    $optional,
                    [],
                    $extend,
                    $necessary['oversea'],
                    $subSid,
                    $necessary['zoneId']
                );
            }
        }

        $data = $necessary['pro_id'] ? [] : ['pro_id' => $result['data']];

        if ($result['code'] == 200) {
            //演出产品保存成功之后 需要对场馆关联产品数量做数据维护
            if ($necessary['ptype'] == 'H' && isset($optional['venue_old_id']) && isset($optional['venus_id']) && $optional['venus_id'] && $optional['venue_old_id'] != $optional['venus_id']) {
                $jobData = [
                    'job_type' => 'show_product_relevance',
                    'job_data' => [
                        'sid'          => $sid,
                        'old_venue_id' => $optional['venue_old_id'] ?? 0,
                        'new_venue_id' => $optional['venus_id'],
                        'lid'          => $necessary['pro_id'] ?: $result['data'],
                        'op_id'        => $op,
                    ],
                ];
                \Library\Resque\Queue::push('show', 'Show_Job', $jobData);
            }
            $this->apiReturn(200, $data, '保存成功');
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }

    }

    /**
     * 上架产品
     * <AUTHOR>
     * @date   2017-07-05
     */
    public function putaway()
    {
        $proId = I('lid', 0, 'intval');

        if (!$proId) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        $productBiz = new \Business\Product\Product();

        $result = $productBiz->putaway($this->_sid, $this->_loginInfo['memberID'], $proId, $subSid);

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], $result['msg']);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 下架产品
     * <AUTHOR>
     * @date   2017-07-05
     */
    public function soldOut()
    {

        $proId = I('lid', 0, 'intval');

        if (!$proId) {
            $this->apiReturn(204, [], '参数缺失');
        }
        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()){
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        $productBiz = new \Business\Product\Product();

        $result = $productBiz->soldOut($this->_sid, $this->_loginInfo['memberID'], $proId, $subSid);

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], $result['msg']);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }

    }

    /**
     * 删除产品
     * <AUTHOR>
     * @date   2017-07-05
     */
    public function delete()
    {

        $proId = I('lid', 0, 'intval');

        if (!$proId) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $productBiz = new \Business\Product\Product();

        $result = $productBiz->delete($this->_sid, $this->_loginInfo['memberID'], $proId);

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], $result['msg']);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }

    }

    /**
     * 恢复产品
     * <AUTHOR>
     * @date   2017-07-05
     */
    public function restore()
    {
        $proId = I('lid', 0, 'intval');

        if (!$proId) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $sid = $this->_sid;
        $subSid = 0;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }

        $authBiz = new AuthContext();
        //发布产品数量限制
        $checkNumLimit = $authBiz->releaseProductNumLimit($sid);
        if (!$checkNumLimit['res']) {
            $this->apiReturn(403, [], $checkNumLimit['msg']);
        }

        $productBiz = new \Business\Product\Product();

        $result = $productBiz->restore($sid, $this->_loginInfo['memberID'], $proId, $subSid);

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], $result['msg']);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 解析必须填写的参数
     * <AUTHOR>
     * @date   2017-07-04
     */
    private function _parseNecessaryAttr()
    {
        //编辑时用到
        $proId = I('lastid', 0, 'intval');

        //产品类型
        $ptype = I('ptype', 'A', 'strval');
        if (!$ptype) {
            return ['status' => 0, 'msg' => '请选择产品类型'];
        }

        if ($ptype == 'I') {
            $proId = I('lid', 0, 'intval');
        }

        //省份代码
        $province = I('d_province', 0, 'intval');
        //城市代码
        $city = I('d_city', 0, 'intval');
        //区县代码
        $zoneId = I('zoneId', 0, 'intval');
        //境内外
        $oversea = I('d_oversea', 0, 'intval');
        if ((!$province || !$city) && $ptype != 'L') {
            return ['status' => 0, 'msg' => '请选择省份城市'];
        }

        //保险产品暂时兼容
        if ($ptype == 'L') {
            $province = 1;
            $city     = 35;
        }

        //产品名称
        $title = I('mainTitle', '', 'strval');
        if (!$title) {
            return ['status' => 0, 'msg' => '请填写产品名称'];
        }

        //预定须知
        $notice = I('buyTips', '', 'strval');
        //特产产品没有预定须知
        if (!$notice && $ptype != 'J' && $ptype != 'L') {
            return ['status' => 0, 'msg' => '请填写预定须知'];
        }

        if (mb_strlen($notice, 'utf-8') > 2000) {
            return ['status' => 0, 'msg' => '预定须知超出2000个字符'];
        }

        //预售券不填购买须知
        if (!$notice && ExchangeCouponProductBiz::isExchangeCouponProductByPType($ptype)) {
            return ['status' => 0, 'msg' => '请填写购买须知'];
        }

        //保险提示兼容
        $productInfo = '请填写产品详情';
        if ($ptype == 'L') {
            $productInfo = '请填写保险说明';
        }

        //景点详情, 预售券没有详情
        $details = I('detailInfo', '', 'strval');
        if (!$details && !ExchangeCouponProductBiz::isExchangeCouponProductByPType($ptype)) {
            return ['status' => 0, 'msg' => $productInfo];
        }

        //景点缩略图
        $thumbImg = I('thumb_img', '', 'strval');
        if (!$thumbImg) {
            return ['status' => 0, 'msg' => '请上传产品缩略图'];
        }

        //景点详细地址
        $address = I('mainAddress', '', 'strval');
        if (!$address && $ptype != 'B' && $ptype != 'L') {
            return ['status' => 0, 'msg' => '请填写产品详细地址'];
        }

        //景点级别
        $level = I('jtype', '非A级', 'strval');
        //首图
        $headImage = I('head_image', '', 'strval');

        $data = [
            'pro_id'    => $proId,
            'ptype'     => $ptype,
            'province'  => $province,
            'address'   => $address,
            'city'      => $city,
            'zoneId'    => $zoneId,
            'oversea'   => $oversea,
            'title'     => $title,
            'level'     => $level,
            'notice'    => $notice,
            'details'   => $details,
            'thumb_img' => $thumbImg,
            'head_img'  => $headImage,
        ];

        return ['status' => 1, 'data' => $data];
    }

    /**
     * 解析可选参数
     * <AUTHOR>
     * @date   2017-07-04
     */
    private function _parseOptionalAttr($ptype)
    {

        $data = [];

        //订单标识
        $data['order_flag'] = I('order_flag', 0, 'intval');

        // 报团属性
        if ($data['order_flag'] == 1) {
            $groupNumLimitArr    = I('group_number_limit', '');  // 是否勾选报团人数限制: 0=否，1=是
            $groupTicketLimitArr = I('group_ticket_limit', '');  // 是否勾选报团门票限制: 0=否，1=是

            $groupNumLimit    = isset($groupNumLimitArr['is_limit']) ? $groupNumLimitArr['is_limit'] : 0;
            $groupTicketLimit = isset($groupTicketLimitArr['is_limit']) ? $groupTicketLimitArr['is_limit'] : 0;

            $groupLimitArr = [];
            if ($groupNumLimit == 1 && isset($groupNumLimitArr['limit_number'])) {
                // 最少多少张的某个票
                $groupLimitArr['group_number_limit']['limit_min_num'] = $groupNumLimitArr['limit_number']; // 限制数量
            }

            if ($groupTicketLimit == 1 && isset($groupTicketLimitArr['tickets']) && is_array($groupTicketLimitArr['tickets'])) {
                foreach ($groupTicketLimitArr['tickets'] as $groupTicket) {
                    $groupLimitArr['group_ticket_limit'][] = [
                        'tid'   => $groupTicket['tid'],    // 门票id
                        'type'  => $groupTicket['type'],   // 不低于0 不高于1
                        'score' => $groupTicket['score'],  // 设置值
                    ];
                }
            }

            $data['group_number_limit'] = isset($groupLimitArr['group_number_limit']['limit_min_num']) ? $groupNumLimit : 0;
            $data['group_ticket_limit'] = isset($groupLimitArr['group_ticket_limit']) ? $groupTicketLimit : 0;

            $groupLimit = '';
            if (!empty($groupLimitArr)) {
                $groupLimit = json_encode($groupLimitArr);
            }
            if (empty($data['group_number_limit']) && empty($data['group_ticket_limit'])) {
                $groupLimit = '';
            }
            $data['group_limit'] = $groupLimit;
        }

        //旧的场馆id
        if (isset($_POST['venue_old'])) {
            $data['venus_id'] = I('venue_old', 0, 'intval');
        }

        //产品主题
        if (isset($_POST['topic'])) {
            $topics = I('topic');
            if (is_array($topics) && $topics) {
                $data['topics'] = implode(',', $topics);
            }
        } else {
            $data['topics'] = '';
        }

        //营业时间或者出发时间
        if (isset($_POST['runtime'])) {
            $data['runtime'] = I('runtime', '', 'strval');
        }

        //联系电话
        if (isset($_POST['mainPhoneNum'])) {
            $data['tel'] = I('mainPhoneNum', '', 'strval');
        }

        //交通指南(费用说明)
        if (isset($_POST['trafficInfo'])) {
            $data['traffic'] = I('trafficInfo', '', 'strval');
        }

        //经纬度信息
        if (isset($_POST['end_place_tude'])) {
            $data['lng_lat_pos'] = I('end_place_tude', '0,0', 'strval');
        }

        //轮播图
        if (isset($_POST['img_path_group'])) {
            $data['img_path_group'] = I('img_path_group');
        }
        //轮播图(兼容)
        if (isset($_POST['detail_img'])) {
            $data['img_path_group'] = I('detail_img');
        }

        if (isset($data['img_path_group'])) {
            $data['img_path_group'] = json_encode($data['img_path_group']);
        } else {
            $data['img_path_group'] = '';
        }

        // 新增景点的判断
        if (isset($_POST['create_resource'])) {
            $data['create_resource'] = I('create_resource', 0, 'intval');
        }

        $videoUrl = I('post.videoUrl', '', 'strval');
        $data['videoUrl'] = $videoUrl;

        switch ($ptype) {
            //线路产品出发地和目的地为必填
            case 'B':
                $startPlace = I('start_place', '');
                $endPlace   = I('end_place', '');

                if (!$startPlace || !$endPlace) {
                    return ['status' => 0, 'msg' => '请填写出发地和目的地'];
                }
                $data['start_place'] = $startPlace;
                $data['end_place']   = $endPlace;
                break;

            case 'H':
                $venueId = I('venue_id', 0, 'intval');

                if (!$venueId) {
                    return ['status' => 0, 'msg' => '请选择场馆'];
                }

                $resourceID           = I('resource_id', 0, 'intval');
                $data['venue_old_id'] = I('venue_old', 0, 'intval');
                $data['venus_id']     = $venueId;
                $data['resource_id']  = $resourceID;
                break;
            case 'A':
            case 'C':
                $resourceID          = I('resource_id', 0, 'intval');
                $data['resource_id'] = $resourceID;
                break;
            case 'K':
                $data['sub_type'] = I('sub_type', 0, 'intval');
                break;
            case 'A-1'://基于门票的子产品类型
                list($data['p_type'], $data['sub_type']) = SubProductBiz::decodeTypeAndSubType($ptype, I('sub_type', 0, 'intval'));
                break;
        }

        return ['status' => 1, 'data' => $data];
    }

    /**
     * 扩展属性接收
     *
     * @return array
     * <AUTHOR>
     * @date 2020/12/28
     *
     */
    public function _parseOptionalExtend()
    {
        $extendData = [];
        //产品首图
        if (isset($_POST['first_picture'])) {
            $extendData['first_picture'] = I('post.first_picture', '', 'strval');
        }

        return $extendData;
    }

    public function saveLand()
    {
        $this->saveProduct();
    }

    /**
     * 获取景点信息
     * <AUTHOR>
     * @date   2017-09-05
     * @return string json
     *
     */
    public function getLandInfo()
    {

        // 景区id
        $lid = I('request.lid', 0, 'intval');

        if (!$lid) {
            $this->apiReturn(204, [], '参数缺失');
        }

        $productBiz = new \Business\Product\Product();
        $result     = $productBiz->getLandInfo($lid);

        $code = 200;
        if ($result['code'] != 200) {
            $code = 204;
        }

        $this->apiReturn($code, $result['data'], $result['msg']);
    }

    /**
     * 模糊查询景区名称，获取当前用户发布对应的已上架的产品的salerid和名称
     * <AUTHOR>
     * @date   2016-12-08
     *
     * @param  string  $land_name  景区
     *
     * @return array
     */
    public function getLandInfoByApplyId()
    {
        $landName  = I('land_name', '', 'strval');

        $javaApi     = new \Business\CommodityCenter\Land();
        $landInfoArr = $javaApi->queryLandMultiQueryByAdminAndPaging([], 1, 30, $landName,
            $orderByClause = 'addtime desc', false, [$this->_sid], [1]);

        $res = [];
        if ($landInfoArr['list']) {
            $res = $landInfoArr['list'];
        }

        if ($res) {
            $this->apiReturn(200, $res, '查询成功');
        } else {
            $this->apiReturn(400, [], '暂无数据');
        }
    }

    /**
     * 检查用户刷新次数
     * <AUTHOR>
     *
     * @return
     */
    public function checkLandUpTime()
    {
        $landId = I('lid', '', 'intval'); //景区ID
        if (!$landId) {
            $this->apiReturn(201, [], '参数错误');
        }

        $memberId = $this->isLogin();
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        if (self::checkApplyId($landId, $memberId)) {
            $this->apiReturn(401, [], '无权操作');
        }

        $landModel   = new Land();
        $where['id'] = $landId;
        //$info        = $landModel->getInfoInLand('uptime', $where, 1);

        $javaAPi = new \Business\CommodityCenter\Land();
        $info    = $javaAPi->queryLandMultiQueryById([$landId]);

        $upTime = strtotime($info[0]['uptime']);
        //判断日期是不是在今天内
        if (date('Y-m-d') == date('Y-m-d', $upTime)) {
            $this->apiReturn(301, [], '您今日已执行刷新过了，请明日再试');
        } else {
            $res = $javaAPi->refreshLandUpdateTime($landId);
            if (!$res) {
                $this->apiReturn(204, [], '刷新失败');
            }
        }

        $this->apiReturn(200, [], '刷新成功');

    }

    /**
     * 给景区增加黑名单
     * @date   2017-04-17
     * <AUTHOR> Lan
     *
     * @return json
     */
    public function addBlackList()
    {
        $landId = I('post.lid', '', 'intval'); //景区ID
        $name   = I('post.name', '', 'strval'); //会员名称
        $idCard = I('post.id_card', '', 'strval'); //身份证

        if (!$landId || !$name || !$idCard) {
            $this->apiReturn(201, [], '参数错误');
        }

        $memberId = $this->isLogin();

        if (self::checkApplyId($landId, $memberId)) {
            $this->apiReturn(401, [], '无权操作');
        }

        $MemberCard = new MemberCard();
        $fid        = $MemberCard->getCardInfoByIdCardNo($idCard, 'memberID');
        if ($fid) {
            $mid = $fid['memberID'];
        } else {
            $mid = 0;
        }

        $otherModel = new MemberCardConf();
        $isExist    = $otherModel->checkBlacklist($landId, $idCard, $mid);
        if ($isExist) {
            $this->apiReturn(202, [], '该用户已经是黑名单');
        }

        $addRes = $otherModel->setBlacklist($landId, $mid, $name, $idCard);

        if ($addRes) {
            $this->apiReturn(200, [], '');
        } else {
            $this->apiReturn(500, [], '系统错误，请刷新重试');
        }
    }

    /**
     * 获取黑名单数据
     * @date   2017-04-17
     * <AUTHOR> Lan
     *
     * @return json
     */
    public function getBlacklist()
    {
        $landId   = I('post.lid', '', 'intval'); //景区ID
        $idCard   = I('post.id_card', '', 'strval');
        $page     = I('post.page', '1', 'intval');
        $pageSize = I('post.page_size', '15', 'intval');
        if (!$landId && !$idCard) {
            $this->apiReturn(201, [], '参数错误');
        }

        $memberId = $this->isLogin();

        if (self::checkApplyId($landId, $memberId)) {
            $this->apiReturn(401, [], '无权操作');
        }

        $field      = 'id, mid, id_card, name, lid';
        $landModel  = new Land();
        $otherModel = new MemberCardConf();
        $count      = $otherModel->getLandBlackListCnt($landId, $idCard);
        if ($count) {
            //$nameLand  = $landModel->getInfoInLand('title', ['id' => $landId], 1);
            $landApi   = new \Business\CommodityCenter\Land();
            $nameLand  = $landApi->queryLandMultiQueryById([$landId]);
            $nameLand  = $nameLand[0]['title'];
            $blackData = $otherModel->getLandBlackList($landId, $idCard, $field, $page, $pageSize);
            $data      = ['list' => $blackData, 'count' => $count, 'page' => $page, 'name_land' => $nameLand];
            $this->apiReturn(200, $data, '');
        }

        $this->apiReturn(400, [], '没有相关数据');
    }

    /**
     * 编辑黑名单数据
     * @date   2017-04-17
     * <AUTHOR> Lan
     *
     * @return json
     */
    public function setBlacklist()
    {
        $id     = I('post.id', '', 'intval'); //表ID
        $landId = I('post.lid', '', 'intval');
        $name   = I('post.name', '', 'strval');
        $idCard = I('post.id_card', '', 'strval');

        if (!$landId || !$name || !$idCard || !$id) {
            $this->apiReturn(201, [], '参数错误');
        }

        $memberId = $this->isLogin();

        if (self::checkApplyId($landId, $memberId)) {
            $this->apiReturn(401, [], '无权操作');
        }
        //会员卡的景区黑名单
        $MemberCard = new MemberCard();
        $fid        = $MemberCard->getCardInfoByIdCardNo($idCard, 'memberID');
        if ($fid) {
            $mid = $fid['memberID'];
        } else {
            $mid = 0;
        }

        $otherModel = new MemberCardConf();
        $isExist    = $otherModel->checkBlacklist($landId, $idCard, $mid);
        if ($isExist && $isExist['id'] != $id) {
            $this->apiReturn(202, [], '该用户已经是黑名单');
        }

        $addRes = $otherModel->setBlacklist($landId, $mid, $name, $idCard, $id);

        if ($addRes) {
            $this->apiReturn(200, [], '');
        } else {
            $this->apiReturn(500, [], '系统错误，请刷新重试');
        }
    }

    /**
     * 删除景区黑名单
     * @date   2017-05-05
     * <AUTHOR> Lan
     *
     * @return json
     */
    public function deleteBlacklist()
    {
        $id     = I('post.id', '', 'intval'); //表ID
        $landId = I('post.lid', '', 'intval'); //景区ID
        if (!$id) {
            $this->apiReturn(201, [], '参数错误');
        }

        $memberId = $this->isLogin();

        if (self::checkApplyId($landId, $memberId)) {
            $this->apiReturn(401, [], '无权操作');
        }

        $otherModel = new MemberCardConf();
        $deleteRes  = $otherModel->deleteBlacklist($id);

        if ($deleteRes) {
            $this->apiReturn(200, [], '');
        } else {
            $this->apiReturn(500, [], '系统错误，请刷新重试');
        }
    }

    /**
     * excel导入景区黑名单
     * @date   2017-04-05
     * <AUTHOR> Lan
     *
     */
    public function addBlacklistByExcel()
    {
        $memberId = $this->isLogin();
        if (!empty($_FILES['file_stu']['name'])) {
            $landId = I('lid', '', 'intval');
            if (!$landId) {
                self::uploadReturn(201, [], '缺少产品参数');
            }
            $tempFile = $_FILES['file_stu']['tmp_name'];

            $fileTypes = explode(".", $_FILES['file_stu']['name']);
            $fileType  = $fileTypes[count($fileTypes) - 1];
            /*判别是不是.xls文件，判别是不是excel文件*/
            if (!in_array(strtolower($fileType), ['xls', 'xlsx'])) {
                self::uploadReturn(201, [], '不是Excel文件，重新上传');
            }
        } else {
            self::uploadReturn(201, [], '请选择文件');
        }

        $readExcel = new ReadExcel();
        $data      = $readExcel->read($tempFile);
        if ($data) {
            $otherModel  = new MemberCardConf();
            $memberModel = new Member();
            $idCardTemp  = [];
            foreach ($data as $key => $val) {
                $idCard = (string)$val[0];

                if (Tools::idcard_checksum18($idCard) == false) {
                    pft_log('memberCard/Excel', $landId . '身份证号无效' . $idCard);
                    continue;
                }

                //会员卡的景区黑名单
                $MemberCard = new MemberCard();
                $mid        = $MemberCard->getCardInfoByIdCardNo($idCard, 'memberID');
                $mid        = $mid['memberID'];
                if (!$mid) {
                    pft_log('memberCard/Excel', (string)$val[0] . '会员信息不存在');
                    $mid = 0;
                }

                if (in_array($idCard . $landId . $mid, $idCardTemp)) {
                    continue;
                }

                $isExist = $otherModel->checkBlacklist($landId, $idCard, $mid);
                if ($isExist) {
                    pft_log('memberCard/Excel', '身份证:' . $idCard . '对应景区' . $landId . '该用户已经是黑名单');
                    continue;
                }

                if (self::checkApplyId($landId, $memberId)) {
                    pft_log('memberCard/Excel', $landId . '无权操作' . $idCard);
                    continue;
                }

                $tempArr['mid']     = $mid;
                $tempArr['lid']     = $landId;
                $tempArr['name']    = $val[1];
                $tempArr['id_card'] = $idCard;
                $tempArr['time']    = time();
                $insertData[]       = $tempArr;

                $idCardTemp[] = $idCard . $landId . $mid;
            }
            $otherModel->addBlacklistByExcel($insertData);
            unlink($tempFile);
            self::uploadReturn(200, [], '导入成功');
        }
        unlink($tempFile);
        self::uploadReturn(201, [], '导入失败，请重试');
    }

    /**
     * 添加产品购票记录
     * @date   2017-05-05
     * <AUTHOR> Lan
     *
     * @return json
     */
    public function addOrderRecord()
    {
        $landId = I('post.lid', '', 'intval'); //景区ID
        $idCard = I('post.id_card', '', 'strval'); //身份证
        $name   = I('post.name', '', 'strval'); //姓名
        $ticket = I('post.ticket', '', 'intval'); //购票数量
        if (!$landId || !$idCard || !$name || !$ticket) {
            $this->apiReturn(201, [], '错误参数');
        }

        if (Tools::idcard_checksum18($idCard) == false) {
            $this->apiReturn(202, [], '身份证号无效');
        }

        $memberId = $this->isLogin();

        if (self::checkApplyId($landId, $memberId)) {
            $this->apiReturn(401, [], '无权操作');
        }

        $landModel  = new Land();
        $otherModel = new MemberCardConf();

        $isExist = $otherModel->checkBlacklist($landId, $idCard);
        if ($isExist) {
            $this->apiReturn(203, [], '黑名单用户无法购票');
        }

        $field     = 'daily_buy_limit, buy_interval, buy_num_limit';
        $limitInfo = $otherModel->getLandLimit($landId, $field);

        if ($limitInfo) {
            $dailyBuyLimit = $limitInfo['daily_buy_limit']; // 每天购买总数
            $buyInterval   = $limitInfo['buy_interval']; // 两次购买间隔
            $buyNumLimit   = $limitInfo['buy_num_limit']; // 每次购买上限

            $beginTime = strtotime(date('Y-m-d'));
            $endTime   = strtotime(date('Y-m-d H:i:s'));

            if ($dailyBuyLimit != -1) {
                $dailyBuyCnt = $otherModel->getOrderRecordCnt($landId, $idCard, '', $beginTime, $endTime);
                if ($dailyBuyCnt >= $dailyBuyLimit) {
                    $this->apiReturn(206, [], '购买次数已达上限值' . $dailyBuyLimit);
                }
            }
            if ($buyInterval != -1) {
                // 购买间隔
                $field           = 'time';
                $lastSubmitOrder = $otherModel->getOrderRecord($field, $landId, $idCard, '', $beginTime, $endTime, 1,
                    1);
                $lastTime        = $lastSubmitOrder[0]['time'];
                $diffTime        = $buyInterval * 60 - (time() - $lastTime);
                $wait            = floor($diffTime / 3600) . '小时' . floor(($diffTime % 3600) / 60) . '分' . ($diffTime % 60) . '秒';
                if ($diffTime > 0) {
                    $this->apiReturn(205, [], '距离下次购买还剩' . $wait);
                }
            }

            if ($buyNumLimit != -1 && $ticket > $buyNumLimit) {
                $this->apiReturn(204, [], "票数大于单次购买限制$buyNumLimit");
            }
        }

        $res = $otherModel->addOrderRecord($landId, $idCard, $name, $ticket);

        if ($res) {
            $this->apiReturn(200, [], '购票成功');
        } else {
            $this->apiReturn(500, [], '系统错误，请稍后重试');
        }
    }

    /**
     * 获取购票记录
     * @date   2017-05
     * <AUTHOR> Lan
     *
     * @return json
     */
    public function getOrderRecord()
    {
        $landId    = I('post.lid', '', 'intval'); //景区ID
        $idCard    = I('post.id_card', '', 'strval'); //身份证
        $name      = I('post.name', '', 'strval'); //身份证
        $beginTime = I('post.btime', strtotime(date('Y-m-d')), 'intvar');
        $endTime   = I('post.etime', strtotime(date('Y-m-d 23:59:59')), 'intval');

        $page     = I('post.page', 1, 'intval');
        $pageSize = I('post.pageSize', 15, 'intval');

        if (!$landId && !$idCard) {
            $this->apiReturn(201, [], '错误参数');
        }
        $memberId = $this->isLogin();

        if (self::checkApplyId($landId, $memberId)) {
            $this->apiReturn(401, [], '无权操作');
        }

        $landModel = new MemberCardConf();

        $count = $landModel->getOrderRecordCnt($landId, $idCard, $name, $beginTime, $endTime);
        if ($count) {
            $field = 'r.time, r.lid, r.tnum, c.name, c.id_card';
            $res   = $landModel->getOrderRecord($field, $landId, $idCard, $name, $beginTime, $endTime, $page,
                $pageSize);
            unset($landModel);

            //通过景区id获取title
            $landIdArr = [$landId];
            $javaAPi   = new \Business\CommodityCenter\Land();
            $title     = $javaAPi->queryLandMultiQueryById($landIdArr)[0];
            if ($res && $title) {
                foreach ($res as &$item) {
                    $item['title'] = $title['title'];
                }
            }
            $data = ['list' => $res, 'count' => $count, 'page' => $page];
            $this->apiReturn(200, $data, '');
        }
        $this->apiReturn(400, [], '没有相关数据');
    }

    /**
     * 获取自供应产品
     * @date   2017-05-05
     * <AUTHOR> Lan
     *
     * @return json
     */
    public function getLand()
    {
        $mid   = $this->isLogin();
        $size  = I('get.size', 10, 'intval');
        $title = I('get.title', '', 'strval');
        $pType = I('get.p_type', '', 'strval');

        $checkStatus = I('get.check_status', 1, 'intval'); //是否校验状态1是2否
        $landStatus  = $checkStatus == 1 ? 1 : null;

        $landApi  = new \Business\CommodityCenter\Land();
        $landRes  = $landApi->getLandInfoListBySid($mid, $title, $page = 1, $size, $pType, $landStatus);
        $products = ['list' => [], 'total' => 0];
        if (!empty($landRes['list'])) {
            $list = [];
            foreach ($landRes['list'] as $item) {
                $list[] = [
                    'id'    => $item['id'],
                    'title' => $item['title'],
                ];
            }
            $products['list']  = $list;
            $products['total'] = $landRes['total'];
        }

        if ($products) {
            $this->apiReturn(200, $products, '获取成功');
        } else {
            $this->apiReturn(400, [], '没有相关数据');
        }
    }

    /**
     * 校验操作供应商
     * @date   2017-05-15
     * <AUTHOR> Lan
     *
     * @param  int  $lid  景区ID
     * @param  int  $mid  会员ID
     *
     * @return array|bool
     */
    private static function checkApplyId($lid, $mid)
    {
        $landApi  = new \Business\CommodityCenter\Land();
        $landInfoRes = $landApi->queryLandMultiQueryByAdminAndPaging([$lid], 1, 1, '', 'id desc', false, [$mid]);
        if (empty($landInfoRes['list'])) {
            return true;
        }

        return false;
    }

    /**
     * 上传文件返回
     * @date   2017-05-05
     * <AUTHOR> Lan
     *
     * @param  int  $code  返回码
     * @param  array  $data  返回数据
     * @param  $msg   string  具体说明
     *
     * @return string
     */
    private static function uploadReturn($code, $data, $msg)
    {
        $list   = [$code, $data, $msg];
        $list   = json_encode($list);
        $string = "
                <script type=\"text/javascript\">
                    var excelFileUpLoaded=window.parent.excelFileUpLoaded;
                    excelFileUpLoaded($list)
                </script>
        ";
        echo $string;
        die;
    }

    ///**
    // * 根据角色和关键字获取产品名称
    // * @date   2017-7-24
    // * <AUTHOR>
    // *
    // * @param  string $key 关键字
    // */
    //public function getLandInfoByKeyWord()
    //{
    //    $code = 200;
    //    $list = [];
    //    $msg  = '';
    //    try {
    //        $keyWord = I('post.keyword', '', 'strval');
    //
    //        if (empty($keyWord)) {
    //            throw new \Exception("关键字不能为空");
    //        }
    //
    //        $loginInfo = $this->getLoginInfo();
    //        $dtype     = $loginInfo['dtype'];
    //        $account   = $loginInfo['saccount'];
    //        $memberId  = $loginInfo['sid'];
    //
    //        //
    //        $business = new \Business\Product\Product();
    //        $list     = $business->getLandInfoByKeyWord($keyWord, $dtype, $memberId, $account);
    //
    //    } catch (\Exception $e) {
    //        $code = 400;
    //        $msg  = $e->getMessage();
    //    }
    //
    //    $this->apiReturn($code, $list, $msg);
    //}

    /**
     * 绑定子景点
     *
     * @date   2017-09-06
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function bindScenic()
    {
        $processLib = new LandProcess();
        $params     = $processLib->getBindParams();

        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }
        $javaParams = $params[1];
        $memberInfo = $this->getLoginInfo();

        $javaParams['account_id']  = $memberInfo['sid'];
        $javaParams['operator_id'] = $memberInfo['memberID'];

        //$result = ProductApi::bindChildLand($javaParams);

        //景区旧工程迁移
        $landLib = new \Business\CommodityCenter\Land();
        $result  = $landLib->bindSecnicList($javaParams['item_id'], $javaParams['account_id'],
            $javaParams['operator_id'], $javaParams['scenicid_list']);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 子景点CU操作
     *
     * @date   2017-09-06
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function setScenic()
    {
        $processLib = new LandProcess();
        $params     = $processLib->getScenicParams();

        if ($params[0] != 0) {
            $this->apiReturn($params[0], [], $params[1]);
        }

        if ($params[1]['account_id'] != $this->_sid) {
            $this->apiReturn(201, [], '无权操作');
        }

        $result = ProductApi::setScenic($params[1]);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 获取子景点详情
     *
     * @date   2017-09-07
     * <AUTHOR> Lan
     *
     * @return string
     */
    public function getScenic()
    {
        $scenicId  = I('id', '', 'intval');
        $accountId = I('account_id', '', 'intval');

        if (!$scenicId || !$accountId) {
            $this->apiReturn(201, [], '参数错误');
        }

        $javaParams['scenic_id']  = $scenicId;
        $javaParams['account_id'] = $accountId;

        $memberInfo = $this->getLoginInfo();

        $javaParams['operater_id'] = $memberInfo['memberID'];

        $result = ProductApi::getScenic($javaParams);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取套票打包产品
     *
     * @date   2018-05-21
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return string
     */
    public function getPacketProducts()
    {
        $productName = I('post.name', '', 'strval');
        $province    = I('post.province', '', 'intval');
        $city        = I('post.city', '', 'intval');
        $oversea     = I('post.oversea', '', 'intval');
        $aid         = I('post.aid', '', 'intval');
        $page        = I('post.page', 1, 'intval');
        $pageSize    = I('post.size', 20, 'intval');
        //0-全部 1-自供应 2-转分销
        $source = I('post.source', 0, 'intval');
        $isTeam = I('post.is_team', 0, 'intval');

        $type = I('post.type', 'A,C,G,H', 'strval');

        $landId      = I('post.landId', 0, 'intval');
        $ticketTitle = I('post.ticketTitle', '', 'strval');
        $ticketPage  = I('post.ticketPage', 0, 'intval');
        $ticketSize  = I('post.ticketSize', 10, 'intval');

        $ticketIds = I('post.ticketIds', '', 'strval');

        //演出支持套票， 演出票补充字段
        $isShowPack = I('post.is_show_pack', 0, 'intval'); //是否返回演出票是否是套票子票标识 1是 0否， 默认0
        $isShowBind = I('post.is_show_bind', 0, 'intval'); //是否返回演出票是否是绑定子票标识 1是 0否， 默认0
        //组合下参数
        $showParams = [
            'is_show_pack' => $isShowPack,
            'is_show_bind' => $isShowBind,
        ];

        $businessBiz = new \Business\Product\Product();

        $res = $businessBiz->getPacketProducts($this->_sid, $productName, $province, $city, $page, $pageSize, $isTeam,
            $type, $oversea, $aid, $source, $landId, $ticketTitle, $ticketPage, $ticketIds, $showParams, $ticketSize);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 验证子票和主票的票属性是否冲突
     * <AUTHOR>
     * @date 2020/11/20
     *
     * @return array
     */
    public function verifyPackageTicketAttrRuleConflict()
    {
        $subTicketIds = I('subTicketIds');

        if (empty($subTicketIds)) {
            $this->apiReturn(201, [], '参数错误');
        }

        $javaApi = new PackageTicket();

        $res = $javaApi->verifyPackageTicketAttrRuleConflict($subTicketIds);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 自供应产品列表
     * <AUTHOR>
     * @date 2018/06/07
     *
     * @return string
     */
    public function getList()
    {
        //需要用到的登录信息
        $loginInfo = $this->getLoginInfo();
        $memberId  = $loginInfo['memberID']; //会员id
        $isSuper   = $this->isSuper();        //判断登陆用户是不是管理员
        $dtype     = $loginInfo['dtype'];    //登录账号类型
        $qx        = $loginInfo['qx'];       //qx判断
        $sdtype    = $loginInfo['sdtype'];  //上级供应商账号类型
        $sid       = $loginInfo['sid'];     //上级id

        if (MemberLoginHelper::isSubMerchantLogin()) {
            $memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        //权限判断
        $Business = new ProductZgyList;
        $check    = $Business->judgeAuth($dtype, $qx, $sdtype);
        if ($check["code"] !== 0) {
            $this->apiReturn($check["code"], [], $check["msg"]);
        }
        if ($dtype == 6 && !$isSuper) {
            $memberId = $sid;                       //员工情况所属上级id
        }

        //参数接收
        $data = businessLib::getZgyParams();
        if ($data["code"] !== 0) {
            $this->apiReturn($data["code"], [], $data["msg"]);
        }

        $page     = $data["data"]["page"]; //页码
        $pageSize = $data["data"]["page_size"]; //条数

        //类型 1=出售中产品，2=仓库中产品，3=待发布产品
        $search_type = I('search_type', 1, 'intval');
        if (!in_array($search_type, array(1, 2, 3))) {
            $search_type = 1;
        }
        $isTeamOrder = 0;

        if (MemberLoginHelper::isSubMerchantLogin()){
            $subMerchantId = MemberLoginHelper::getLoginBusinessMember()->getSId();
            $data["data"]['subSid'] = $subMerchantId;
        }

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition();
        // 全禁的情况下不需要调用接口
        if ($condition === false) {
            $result = ['code' => self::CODE_SUCCESS, 'data' => ['products' => [], 'totalCount' => 0], 'msg' => ''];
        } else {
            $data['data'] = array_merge($data['data'], $condition);
            //获取java请求地址的接口
            $result = $Business->getJavaInterface($search_type, $isSuper, $memberId, $page, $pageSize, $data['data']);
        }
        if ($result['code'] == 200) {
            //需要校验一次当前用户的报团是否有开通
            $res = (new AuthLogicBiz())->getAuthTag($sid, $memberId);
            $ownAuth = $res['data'] ?? [];
            //校验下报团预订管理的权限是否有开启
            if (in_array('baotuan_ordergong', $ownAuth) || in_array('baotuan_manage_s', $ownAuth)) {
                $isTeamOrder = 1;
            }

            $isOpen   = false;
            //更新成功之后 且有需要更新分销库存的
            $checkRes = (new \Business\PftShow\DisStorage())->checkDisStorageAuth($sid);
            //有开通开放功能的 再为新创建分区的创建初始模板
            if ($checkRes['code'] == 200 && isset($checkRes['data']['is_open']) && $checkRes['data']['is_open']) {
                $isOpen = true;
            }

            //设置过期票数和即将过期票数默认值
            $result['data']['expireInfo'] = [
                'aboutExpireCount' => 0,
                'expireCount'      => 0,
                'team_order_flag'  => $isTeamOrder,
                'open_dis_storage' => $isOpen,
            ];

            if ($search_type == 1 && !$isSuper) {
                //获取用户过期或即将过期票数量
                $expireTicketInfo = TicketApi::getExpireCount($memberId, $subMerchantId);
                if ($expireTicketInfo['code'] == 200) {
                    $result['data']['expireInfo'] = [
                        'aboutExpireCount' => !empty($expireTicketInfo['data']['aboutExpireCount']) ? $expireTicketInfo['data']['aboutExpireCount'] : 0,
                        'expireCount'      => !empty($expireTicketInfo['data']['expireCount']) ? $expireTicketInfo['data']['expireCount'] : 0,
                        'team_order_flag'  => $isTeamOrder,
                        'open_dis_storage' => $isOpen,
                    ];
                }
            }

            //判断登录用户是否有开通分时预约
            $moduleModel  = new ModuleList();
            $moduleConfig = $moduleModel->getPackageLogInfoByUserIdArr($memberId, 61);
            if ($moduleConfig && !in_array($moduleConfig['status'], [1, 3, 7])) {
                $result['data']['expireInfo']['timeShare'] = 1;
            }

            $result['data']['is_old_list'] = true;
        }

        if (!empty($result['data']["products"])) {
            //用于处理过期类型的票价格 状态
            $result['data']["products"] = $Business->priceOverdue($result['data']["products"]);

            //处理捆绑，门票 演出类进行处理
            $result['data']["products"] = $Business->bundledTicket($result['data']["products"]);

            //获取资源信息 管理员才有
            if ($isSuper) {
                $result['data']["products"] = $Business->getResourceName($result['data']["products"]);
            }

            //非管理员才处理外部码发码
            if (!$isSuper) {
                //外部码判断一层
                $result['data']['products'] = $Business->externalCodeHandle($result['data']['products'], $sid);
            }

            //供应商有开启分时预约才通过景区id获取对应景区是否有开始分时预约
            if ($result['data']['expireInfo']['timeShare'] == 1) {
                $landIdArr     = array_column($result['data']["products"], 'landId');
                $javaApi       = new TimeShareLib();
                $landMap       = $javaApi->getConfByLidArr($landIdArr);
                $timeShareType = ['A'];
                //$ticketIds     = [];
                foreach ($result['data']["products"] as &$prouct) {

                    if (in_array($prouct['type'], $timeShareType)) {
                        $prouct['timeShare'] = isset($landMap[$prouct['landId']]) ? $landMap[$prouct['landId']] : 0;
                    }
                    //$ticketIdsTmp = array_column($prouct['tickets'], 'ticketId');
                    //$ticketIds    = array_merge($ticketIds, $ticketIdsTmp);
                }

                //票加分时标识
                //$ticketIds     = array_unique($ticketIds);
                //$landMap       = $javaApi->querySectionEnableByTids($ticketIds);
                //if ($landMap['code'] != 200) {
                //
                //}
                $timeShareType = ['A'];
                foreach ($result['data']["products"] as &$prouct) {
                    if (in_array($prouct['type'], $timeShareType)) {
                        $prouct['timeShare'] = isset($landMap[$prouct['landId']]) ? $landMap[$prouct['landId']] : 0;
                    }
                }
            }
            //关联产品标签
            $product = new \Business\Product\Product();
            $result['data']['products'] = $product->getProductTag($sid,$memberId,$result['data']['products']);
            //关联业态资源
            $result['data']['products'] = $product->reclandManagerRelation($result['data']['products'], $isSuper);
            //添加酒店房型参数
            $hotelLib                   = new Hotel();
            $result['data']['products'] = $hotelLib->handleRoomParamsSale($result['data']['products']);
            //分时标识添加
            $timeShareLib               = new TimeShareLib();
            $result['data']['products'] = $timeShareLib->handleShareParamsSale($result['data']['products']);
            //票扩展属性添加
            $result['data']['products'] = $this->ticketExtendAttrConf($result['data']['products']);
            //配置通知人员
            $wxConfigBiz                = new WxConfig();
            $result['data']['products'] = $wxConfigBiz->handleNoticeParamsSale($sid, $result['data']['products']);

            //SKU标签处理
            $productTagBiz              = new ProductTagBiz();
            $result['data']['products'] = $productTagBiz->handleSelfProductsSkuTag($sid, $result['data']['products']);

            //用于处理团单配置的状态
            if ($isTeamOrder) {
                $landIdArr      = array_column($result['data']["products"], 'landId');
                $ticketBiz      = new \Business\Product\Ticket();
                $ticketGroupRes = $ticketBiz->batchExistTicketGroupListFlags($landIdArr);
                $teamOrderMap   = [];
                if ($ticketGroupRes['code'] == 200 && !empty($ticketGroupRes['data'])) {
                    $teamOrderMap = array_column($ticketGroupRes['data'], 'exist', 'lid');
                }

                $teamOrderType = ['A', 'F', 'H'];
                foreach ($result['data']["products"] as &$prouct) {
                    if (in_array($prouct['type'], $teamOrderType)) {
                        $prouct['teamSwitch'] = isset($teamOrderMap[$prouct['landId']]) && !empty($teamOrderMap[$prouct['landId']]) ? $teamOrderMap[$prouct['landId']] : 0;
                    } else {
                        $prouct['teamSwitch'] = -1;
                    }
                }
            }

            //是演出子票处理
            $packBiz                    = new PackTicket();
            $result['data']["products"] = $packBiz->handleProductListSonShowParams($result['data']["products"]);
            $travelVoucherLidArray = []; //获取旅游券产品lid
            //资源中心 供应按钮展示 产品类型：门票、酒店、特产、演出、套票、线路
            $allowShowResourceCenterBtnType = ['A', 'B', 'C', 'J', 'H', 'F'];

            $showModel = new \Model\Product\Show();
            foreach ($result['data']["products"] as &$prouct) {
                //演出类型的需要返回场馆名称
                if ($prouct['type'] == 'H' && $prouct['venusId']) {
                    $prouct['venue_name'] = $showModel->getVenuesInfo($prouct['venusId'], 'venue_name')['venue_name'] ?? '';
                }

                if (in_array($prouct['type'], $allowShowResourceCenterBtnType)) {
                    $prouct['showResourceCenterBtn'] = true;
                } else {
                    $prouct['showResourceCenterBtn'] = false;
                }

                if ($prouct['type'] == 'Q'){
                    $travelVoucherLidArray[] = $prouct['landId'];
                }
            }
            //处理旅游券产品是否可编辑
            $result['data']['products'] = $product->handleTravelVoucherCanEdit($result['data']['products'], $travelVoucherLidArray);

            //先判断是否开通该应用
            $approvalManage = new ApprovalManage();
//            $isAuth = $approvalManage->checkModuleAuthForApprovalManage($this->_loginInfo);
            //处理是否对接审核流程
            $result['data']['products'] = $approvalManage->getApprovalTagForTickets($result['data']["products"]);

        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    public function getProductLabelList()
    {
        $level = I('post.level', 1, 'intval');
        $pType = I('post.pType', 'Q', 'strval');
        $tag   = I('post.tag', 'source', 'strval');

        $productLabels = new ProductLabels();
        $result = $productLabels->getProductLabelList($level, $pType, $tag);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 查询自供应产品列表
     * <AUTHOR>
     * @date 2021/2/8
     *
     */
    public function getSelfProductSearchList()
    {
        //搜索关键字
        $keyword = I('post.keyword', '', 'strval');
        $page    = I('post.page', '1', 'strval');
        $size    = 10;
        $ptype   = I('post.ptype', '', 'strval');//景区类型，多个类型使用逗号分隔
        //需要用到的登录信息
        $loginInfo = $this->getLoginInfo();
        if (empty($keyword)) {
            $this->apiReturn(200, ['total' => 0, 'list' => []], '');
        }

        $Business = new ProductZgyList;
        //获取java请求地址的接口
        $search_type = 1; //类型 1=出售中产品，2=仓库中产品，3=待发布产品
        $params      = [
            "type"            => empty($ptype) ? '' : $ptype,  //景区类型，多个类型使用逗号分隔
            "item_name"       => empty($keyword) ? '' : $keyword,     //产品名称
            "page"            => (int)$page,     //页码
            "page_size"       => (int)$size,     //条数
            "supplier_id"     => $loginInfo['sid'],   //供应商id
            'show_ticket_num' => 1,   //门票显示条数
            'is_management'   => 0,   //显示未关联景区的资源 0:否 1：是
        ];
        $result      = $Business->getJavaInterface($search_type, false, $loginInfo['sid'], $page, $size, $params);

        if (!isset($result['data']['products'])) {
            $this->apiReturn(200, ['total' => 0, 'list' => []], '');
        }
        $result   = isset($result['data']) ? $result['data'] : [];
        $total    = isset($result['totalCount']) ? $result['totalCount'] : 0;
        $products = isset($result['products']) ? $result['products'] : [];

        //数据处理
        $list = [];
        foreach ($products as $tmp) {
            $item   = [
                'id'   => $tmp['landId'],
                'name' => $tmp['name'],
                'type' => $tmp['type'],
            ];
            $list[] = $item;
        }

        $this->apiReturn(200, ['total' => $total, 'list' => $list], '');
    }

    /**
     * 判断员工账号是否存在发布产品查看转分销产品权限权限
     * <AUTHOR>
     * @date 2019/7/15
     */
    public function getStaffJurisdiction()
    {
        //需要用到的登录信息
        $loginInfo = $this->getLoginInfo();
        $isSuper   = $this->isSuper();        //判断登陆用户是不是管理员
        $dtype     = $loginInfo['dtype'];    //登录账号类型
        $qx        = $loginInfo['qx'];       //qx判断
        $sdtype    = $loginInfo['sdtype'];  //上级供应商账号类型
        //权限判断
        $Business = new ProductZgyList;
        $check    = $Business->judgeAuth($dtype, $qx, $sdtype);
        if ($check["code"] !== 0) {
            $this->apiReturn($check["code"], [], $check["msg"]);
        }
        $release  = true;
        $prodSale = true;
        if ($dtype == 6 && !$isSuper) {
            //判断出售中的按钮跟转分销产品按钮是否显示
            if (strpos($qx, 'release') === false) {
                $release = false;
            }
            if (strpos($qx, 'prod_sale') === false) {
                $prodSale = false;
            }
        }
        $returnData = [
            'release'   => $release,
            'prod_sale' => $prodSale,
        ];
        $this->apiReturn(200, $returnData, "权限判断成功");
    }

    /**
     * 获取不包括特殊票种的自供应景区和套票
     * Create by zhangyangzhen
     * Date: 2018/8/14
     * Time: 10:32
     */
    public function getUnsepcialList()
    {
        //需要用到的登录信息
        $loginInfo = $this->getLoginInfo();
        $memberId  = $loginInfo['memberID']; //会员id
        $isSuper   = $this->isSuper();        //判断登陆用户是不是管理员
        $dtype     = $loginInfo['dtype'];    //登录账号类型
        $qx        = $loginInfo['qx'];       //qx判断
        $sdtype    = $loginInfo['sdtype'];  //上级供应商账号类型
        $sid       = $loginInfo['sid'];     //上级id

        //权限判断
        $Business = new ProductZgyList;
        $check    = $Business->judgeAuth($dtype, $qx, $sdtype);
        if ($check["code"] !== 0) {
            $this->apiReturn($check["code"], [], $check["msg"]);
        }

        if ($dtype == 6 && !$isSuper) {
            $memberId = $sid;                       //员工情况所属上级id
        }

        //参数接收
        $data = businessLib::getZgyParams();
        if ($data["code"] !== 0) {
            $this->apiReturn($data["code"], [], $data["msg"]);
        }

        $page     = $data["data"]["page"]; //页码
        $pageSize = $data["data"]["page_size"]; //条数

        //获取java请求地址的接口
        $result = $Business->getJavaInterface(4, $isSuper, $memberId, $page, $pageSize, $data["data"]);

        if ($result['code'] == 200) {
            //设置过期票数和即将过期票数默认值
            $result['data']['expireInfo'] = [
                'aboutExpireCount' => 0,
                'expireCount'      => 0,
            ];

            if (!$isSuper) {
                //获取用户过期或即将过期票数量
                $expireTicketInfo = TicketApi::getExpireCount($memberId);
                if ($expireTicketInfo['code'] == 200) {
                    $result['data']['expireInfo'] = [
                        'aboutExpireCount' => !empty($expireTicketInfo['data']['aboutExpireCount']) ? $expireTicketInfo['data']['aboutExpireCount'] : 0,
                        'expireCount'      => !empty($expireTicketInfo['data']['expireCount']) ? $expireTicketInfo['data']['expireCount'] : 0,
                    ];
                }
            }
        }

        if (!empty($result['data']["products"])) {
            //用于处理过期类型的票价格 状态
            $result['data']["products"] = $Business->priceOverdue($result['data']["products"]);

            //处理捆绑，门票 演出类进行处理
            $result['data']["products"] = $Business->bundledTicket($result['data']["products"]);

            //获取资源信息 管理员才有
            if ($isSuper) {
                $result['data']["products"] = $Business->getResourceName($result['data']["products"]);
            }
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 导出获取供应商列表表格数据
     * <AUTHOR>
     * @date 2018/06/19
     *
     * @return string
     */
    public function exportList()
    {
        //需要用到的登录信息
        $loginInfo = $this->getLoginInfo();
        $memberId  = $loginInfo['memberID']; //会员id
        $isSuper   = $this->isSuper();        //判断登陆用户是不是管理员
        $dtype     = $loginInfo['dtype'];    //登录账号类型
        $qx        = $loginInfo['qx'];       //qx判断
        $sdtype    = $loginInfo['sdtype'];  //上级供应商账号类型
        $sid       = $loginInfo['sid'];     //上级id

        //权限判断
        $Business = new ProductZgyList;
        $check    = $Business->judgeAuth($dtype, $qx, $sdtype);
        if ($check["code"] !== 0) {
            $this->apiReturn($check["code"], [], $check["msg"]);
        }

        if ($dtype == 6 && !$isSuper) {
            $memberId = $sid;                       //员工情况所属上级id
        }

        //参数接收
        $data = businessLib::getZgyExportParams();
        if ($data["code"] !== 0) {
            $this->apiReturn($data["code"], [], $data["msg"]);
        }

        $page     = 1; //页码
        $pageSize = 999999999; //条数

        //类型 1=出售中产品，2=仓库中产品，3=待发布产品
        $search_type = I('search_type', 1, 'intval');
        if (!in_array($search_type, array(1, 2, 3))) {
            $search_type = 1;
        }

        //获取java请求地址的接口
        $result = $Business->getJavaInterface($search_type, $isSuper, $memberId, $page, $pageSize, $data["data"]);

        $filename      = date('YmdHis') . '分销商产品列表';
        $Excel[0]['a'] = '省';
        $Excel[0]['b'] = '市';
        $Excel[0]['c'] = '景区SID';
        $Excel[0]['d'] = '景区6位数ID';
        $Excel[0]['e'] = '景区名称';
        $Excel[0]['f'] = '供应商';
        $Excel[0]['g'] = '状态';
        if ($search_type != 3) {
            $Excel[0]['h'] = '景区级别';
            $Excel[0]['i'] = '景区主题';
            $Excel[0]['j'] = '创建时间';
            $Excel[0]['k'] = '供货价';
            $Excel[0]['l'] = '门市价';
            $Excel[0]['m'] = '建议零售价';
            $Excel[0]['n'] = '取票信息';
        }

        if (!empty($result['data']["products"])) {
            //用于处理过期类型的票价格 状态
            $result['data']["products"] = $Business->priceOverdue($result['data']["products"]);

            //这块地区排序是旧页面拿过来的
            foreach ($result['data']["products"] as $prod) {
                list($q, $sort[]) = explode('|', $prod['area']);
                //[$q, $sort[]] = explode('|', $prod['area']);
            }
            array_multisort($sort, $result['data']["products"]); //根据地区来排序

            $i               = 2;
            $is_only_product = I('is_only_product', 0, 'intval'); //仅列出产品:1是
            if ($is_only_product == 1) {
                foreach ($result['data']["products"] as $prod) {
                    $Excel[$i]['a'] = $prod["provice"];
                    $Excel[$i]['b'] = $prod["city"];
                    $Excel[$i]['c'] = $prod['landId'];
                    $Excel[$i]['d'] = $prod['salerid'];
                    $Excel[$i]['e'] = $prod['name'];
                    $Excel[$i]['f'] = $prod['supplierName'];
                    $Excel[$i]['g'] = $search_type == 1 ? '在售' : '仓库';
                    $Excel[$i]['h'] = $prod['level'];
                    $Excel[$i]['i'] = $prod['topic']; //景区主题
                    $Excel[$i]['j'] = $prod['addtime'] ? date("Y-m-d h:i:s", $prod['addtime'] / 1000) : "";
                    $i++;
                }
            } else {
                foreach ($result['data']["products"] as $prod) {
                    if (count($prod['tickets'])) {
                        foreach ($prod['tickets'] as $ticket) {
                            $Excel[$i]['a'] = $prod["provice"];
                            $Excel[$i]['b'] = $prod["city"];
                            $Excel[$i]['c'] = $prod['landId'];
                            $Excel[$i]['d'] = $prod['salerid'];
                            $Excel[$i]['e'] = $prod['name'] . $ticket['name'];
                            $Excel[$i]['f'] = $prod['supplierName'];
                            $Excel[$i]['g'] = $search_type == 1 ? '在售' : '仓库';
                            $Excel[$i]['h'] = $prod['level'];
                            $Excel[$i]['i'] = $prod['topic']; //景区主题
                            $Excel[$i]['j'] = $ticket['verifyTime'] ? date("Y-m-d h:i:s",
                                $ticket['verifyTime'] / 1000) : "";
//                            $Excel[$i]['k'] = $ticket['costPrice']/100;
//                            $Excel[$i]['l'] = $ticket['counterPrice']/100;
//                            $Excel[$i]['m'] = $ticket['retailPrice']/100;
                            // //兼容php7+版本
                            $Excel[$i]['k'] = (int)$ticket['costPrice'] / 100;
                            $Excel[$i]['l'] = (int)$ticket['counterPrice'] / 100;
                            $Excel[$i]['m'] = (int)$ticket['retailPrice'] / 100;
                            $Excel[$i]['n'] = $ticket['getaddr']; //取票信息
                            $i++;
                        }
                    }
                }
            }
        }

        $xls = new SimpleExcel('UTF-8', true, 'land');
        $xls->addArray($Excel);
        $xls->generateXML($filename);
    }

    /**
     * 获取供应商列表id
     * <AUTHOR>
     * @date 2018/06/19
     *
     * @return string
     */
    public function getSupplierID()
    {
        $isSuper = $this->isSuper(); //判断登陆用户是不是管理员
        if (!$isSuper) {
            $this->apiReturn(401, "", "没有权限");
        }

        $supplierName = I('supplier_name', "", 'strval'); //供应商名称

        $MemberModel = new MemberQuery();
        $res         = $MemberModel->queryMemberListByFidsOrDnameAndDTypes($supplierName, [0], [], 1, 100);
        if ($res) {
            $this->apiReturn(200, ["list" => $res['list']], "成功");
        } else {
            $this->apiReturn(200, ["list" => []], "无数据");
        }
    }

    /**
     * 下拉获取门票信息
     * <AUTHOR>
     * @date 2018/12/13
     * @return string
     */
    public function getTickets()
    {
        $ticketIds = I('post.ticket_ids', '', 'strval');
        if ($ticketIds === '') {
            $this->apiReturn(201, [], '门票Id为空');
        }
        $page      = I('post.page', 0);
        $size      = I('post.size', 5);
        $type      = I('post.type');
        $timeShare = I('post.timeShare', 0);
        if (!isset($type) || empty($type)) {
            $this->apiReturn(201, [], '类型不能为空');
        }
        $ticketArr    = $arrTicketIds = explode(',', $ticketIds);
        $start        = $page * $size;
        $cutTicketIds = array_splice($arrTicketIds, $start, $size);
        $strTicketIds = join(',', $cutTicketIds);
        //直接调取java获取票接口
        $result = TicketApi::getSupplyMoreTickets($strTicketIds);
        if (empty($result)) {
            $this->apiReturn(204, [], "获取失败");
        }

        //管理员不处理外部码发码的
        $Business = new ProductZgyList;
        $result   = $Business->externalCodeHandle($result, $this->_sid, 2);

        foreach ($result as &$t) {
            if ($t["costPrice"] == -1 && $t["retailPrice"] == -1) {
                $t["status"] = -1;
            }

            if ($t["costPrice"] == -1) {         //成本价
                $t["costPrice"] = "--";
            }
            if ($t["counterPrice"] == -1) {      //门市价
                $t["counterPrice"] = "--";
            }
            if ($t["retailPrice"] == -1) {
                $t["retailPrice"] = "--";
            }
            if ($t["windowPrice"] == -1) {
                $t["windowPrice"] = "--";
            }
            unset($t);
        }
        $PerformanceModel = new Performance();
        if ($type == "H") {
            foreach ($result as $idx => $tmpTicket) {
                $bindTickets = $PerformanceModel->getBindTickets($tmpTicket["ticketId"]);
                if ($bindTickets) {
                    $result[$idx]['child_tickets'] = $bindTickets;
                }
            }
        }

        //分时标识添加
        if ($type == 'A' && $timeShare == 1) {
            $timeShareLib = new TimeShareLib();
            $result       = $timeShareLib->handleShareParamsSaleMore($result);
        }

        if ($type == 'C') {
            //添加酒店房型参数
            $hotelLib = new Hotel();
            $result   = $hotelLib->handleRoomParamsSaleMore($result);
        }
        // 额外填写更多信息
        $ticketExtendAttr    = new TicketExtendAttr();
        $ticketSpecialValRes = $ticketExtendAttr->queryTicketSpecialVal($ticketArr,
            ['is_start_stop_sale_limit', 'auto_sale_rule']);
        $ticketSpecialValRes = $ticketSpecialValRes['data'] ?? [];
        $autoArr             = [];
        $ticketAttr          = [];
        foreach ($ticketSpecialValRes as $ticketConf) {
            if ($ticketConf['key'] == 'auto_sale_rule') {
                $autoArr[] = $ticketConf;
            }
            if ($ticketConf['key'] == 'is_start_stop_sale_limit') {
                $ticketAttr[] = $ticketConf;
            }
        }
        $autoArr    = array_key($autoArr, 'ticketId');
        $ticketAttr = array_key($ticketAttr, 'ticketId');
        foreach ($result as &$val) {
            $val['autoSaleRule']         = empty($autoArr[$val['ticketId']]['val']) ? [] : json_decode($autoArr[$val['ticketId']]['val'],
                true);
            $val['isStartStopSaleLimit'] = $ticketAttr[$val['ticketId']]['val'] ?? 0;
        }
        unset($val);

        //是演出子票处理
        $packBiz = new PackTicket();
        $result  = $packBiz->handleProductTicketsSonShowParams($result);

       //先判断是否开通该应用
        $approvalManage = new ApprovalManage();
//        $isAuth = $approvalManage->checkModuleAuthForApprovalManage($this->_loginInfo);
        //处理是否对接审核流程
        $result = $approvalManage->getApprovalTagForMoreTickets($result,$type);
        $product = new \Business\Product\Product();
        $result = $product->getProductTagMoreTickets($this->_sid,$this->_loginInfo['memberID'],$result);
        //SKU标签处理
        $result = (new ProductTagBiz())->handleSelfProductsSkuTagMoreTickets($this->_sid, $result);
        if ($type == 'Q'){
            $lid = $result[0]['landId'];
            $voucherService = new VoucherService();
            $voucherCanEditRes = $voucherService->getSpuList([$lid]);
            foreach ($result as &$value){
                $value['can_edit'] = $voucherCanEditRes['data'][0]['can_edit'];
            }
        }

        $this->apiReturn(200, ['list' => $result], "获取成功");
    }

    /**
     * 通过关键字和产品类型搜索自供应+转分销产品
     * Create by zhangyangzhen
     * Date: 2019/3/26
     * Time: 16:02
     */
    public function getAllProductsList()
    {
        $keyword    = I('post.keyword', '', 'strval,trim');
        $ptype      = I('post.ptype', '', 'strval,trim');
        $supplyType = I('post.supply_type', -1, 'intval');

        if (empty($keyword)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入关键字查询');
        }

        $result = \Business\Product\ProductList::getWPTSmallAppList($this->_sid, $keyword, $ptype, 0, '', $supplyType,
            1, 200);

        if (empty($result['lists'])) {
            $this->apiReturn(self::CODE_SUCCESS, []);
        }

        $list = [];
        foreach ($result['lists'] as $key => $val) {
            $list[] = [
                'land_name' => $val['landName'],
                'land_id'   => $val['lid'],
                'p_type'    => $val['ptype'],
                'aid'       => $val['supplierId'],
            ];
        }

        $this->apiReturn(self::CODE_SUCCESS, $list);
    }

    /**
     * 通过景区ID（包括自供应和转分销）查询该景区下的票类信息以及关联分账配置信息
     * Create by zhangyangzhen
     * Date: 2019/3/26
     * Time: 18:12
     */
    public function getSupplyEvoluteTicketByLid()
    {
        $lid = I('post.lid', 0, 'intval');
        $aid = I('post.aid', 0, 'intval');

        if ($lid < 1 || $aid < 1) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        // 走JAVA接口
        $productBiz = new \Business\Product\Product();
        $landInfo   = $productBiz->getWPTProductInfo($this->_sid, $aid, $lid);

        if ($landInfo['code'] != self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请求出错');
        }

        $ticket = $landInfo['data']['ticketList'];

        if (empty($ticket)) {
            $this->apiReturn(self::CODE_SUCCESS, []);
        }

        $ticketIdArr = array_column($ticket, 'ticketId');

        $model = new SeparateConfig();
        if ($landInfo['ptype'] == 'F') {
            $field = "p.pack_tid,p.channel";
            $res   = $model->getSeparateConfigByLidAndTid($this->_sid, $lid, $ticketIdArr, [], $field);
        } else {
            $field = "p.tid,p.channel";
            $res   = $model->getSeparateConfigByLidAndTid($this->_sid, $lid, [], $ticketIdArr, $field);
        }

        foreach ($ticket as $key => $val) {
            $ticket[$key]['ordermode'] = $res[$val['ticketId']] ?: '';
        }

        $this->apiReturn(self::CODE_SUCCESS, $ticket);
    }

    /**
     * 产品发布页
     * Create by zhangyangzhen
     * Date: 2019/11/18
     * Time: 10:25
     */
    public function productRelease()
    {
        $type = I('type', '', 'strval,trim');
        $lid  = I('lid', 0, 'intval');

        $loginInfo = $this->getLoginInfo();

        //一些基础权限的判断
        if ($loginInfo['sdtype'] != 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '您不是供应商身份，无法发布产品');
        }

        if (!$lid || $lid <= 0) {
            if (empty($type) || !in_array($type, $this->_productType)) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '产品类型错误');
            }
        } else {
            $landModel = new Land();
            $jdata     = $landModel->getLandInfo($lid);

            if (!$jdata || empty($jdata)) {
                $this->apiReturn(self::CODE_PARAM_ERROR, [], '产品不存在');
            }

            if ($jdata['apply_did'] != $loginInfo['sid']) {
                $this->apiReturn(self::CODE_AUTH_ERROR, [], '这不是你的产品');
            }

            // 数组键名转换
            //$jdata['title']      = $jdata['name'];
            //$jdata['jtype']      = $jdata['level'];
            //$jdata['p_type']     = $jdata['type'];
            //$jdata['resourceID'] = $jdata['resource_id'];
            //$jdata['apply_did']  = $jdata['account_id'];
            //$jdata['imgpath']    = $jdata['img_path'];
            //$jdata['imgpathGrp'] = $jdata['img_path_group'];
            //$jdata['bhjq']       = $jdata['details'];
            //$jdata['jqts']       = $jdata['notice'];
            //$jdata['jtzn']       = $jdata['traffic'];
            //$jdata['topic']      = $jdata['topics'];
            //$jdata['area']       = $jdata['province_name'] . "|" . $jdata['city_name'];

            $ticketModel = new \Model\Product\Ticket();
            $javaApi     = new ticketBiz();
            $ticketInfo  = $javaApi->queryTicketBylandIdAndPay([$lid]);
            //$ticketInfo  = $ticketModel->getTicketInfoByLandId($lid);

            $hasTicket = false;
            if ($ticketInfo && !empty($ticketInfo)) {
                $hasTicket = true;
            }

            if ($jdata['order_flag'] == 1) {
                // 返回团队订单预定的规则属性
                if (isset($jdata['group_limit']) && !empty($jdata['group_limit'])) {
                    $groupLimitArr = json_decode($jdata['group_limit'], true);
                }
                $groupNumLimit['is_limit']     = 0;
                $groupNumLimit['limit_number'] = 0;
                if (isset($jdata['group_number_limit']) && $jdata['group_number_limit'] == 1) {
                    $groupNumLimit['is_limit']     = 1;
                    $groupNumLimit['limit_number'] = isset($groupLimitArr['group_number_limit']['limit_min_num']) ? $groupLimitArr['group_number_limit']['limit_min_num'] : 0;
                }
                $groupTicketLimit['is_limit'] = 0;
                $groupTicketLimit['tickets']  = [];
                if (isset($jdata['group_ticket_limit']) && $jdata['group_ticket_limit'] == 1) {
                    $groupTicketLimit['is_limit'] = 1;
                    $groupTicketLimit['tickets']  = isset($groupLimitArr['group_ticket_limit']) ? $groupLimitArr['group_ticket_limit'] : [];
                }
                $jdata['group_ticket_limit'] = $groupTicketLimit;
                $jdata['group_number_limit'] = $groupNumLimit;
            }

            $jdata['resourceName'] = '';
            if (!empty($jdata['resourceID'])) {
                $resourceModel = new LandResource();
                $resourceRes   = $resourceModel->findResourceById($jdata['resourceID'], 'id,title');

                $jdata['resourceName'] = $resourceRes['title'];
            }
        }

        $imgUrl = !empty($jdata['imgpath']) ? $jdata['imgpath'] : 'images/defaultThum.jpg';

        //轮播展示图
        $imgArrInfo = [];

        if ($jdata['imgpathGrp']) {
            $imgUrls = @unserialize($jdata['imgpathGrp']);
            $imgUrls = is_array($imgUrls) ? $imgUrls : json_decode($jdata['imgpathGrp'], true);

            $count = count($imgUrls);
            for ($i = 0; $i < $count; $i++) {
                $imgArrInfo[$i] = (is_array($imgUrls) && $imgUrls[$i]) ? $imgUrls[$i] : 'images/defaultThum.jpg';
            }

            $jdata['imgpathGrp'] = $imgArrInfo;
        }

        $var['end_place_tude'] = $jdata['lng_lat_pos'] ? $jdata['lng_lat_pos'] : '0,0';

        //获取一些配置
        //展示字段配置
        $fieldList = load_config('fieldList', 'product');
        //旅游主题
        $productTopic = load_config('productTopic', 'product');

        $returnData = [
            'loginInfo'    => $loginInfo,
            'jdata'        => $jdata ?: [],
            'place'        => $var['end_place_tude'],
            'fieldList'    => $fieldList,
            'productTopic' => $productTopic,
            'hasTicket'    => $hasTicket,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $returnData, 'success');
    }

    /**
     * 获取供应商所有上架和下架的产品
     * Create by zhangyangzhen
     * Date: 2019/11/19
     * Time: 14:23
     * @editor zhujb
     * @date 2020/03/04
     */
    public function getAllLand()
    {
        $keyword  = I('post.keyword', '', 'strval');
        $action   = I('post.action', '', 'strval');
        $showMore = I('get.show_more', 0, 'intval');
        $status   = I('post.status', '', 'strval'); //查询多种景区状态 0所有 1上架 2下架 3删除  多个以逗号隔开
        $page     = I('post.page', -1, 'intval');
        $pageSize = I('post.page_size', -1, 'intval');
        $isNew    = I('post.is_new', 1, 'intval');  // 是否返回搜索状态

        $sdType   = $this->_loginInfo['sdtype'];
        $saccount = $this->_loginInfo['saccount'];

        $landStatus = 1;  // 上架
        if ($showMore) {
            $landStatus = 2; // 下架
        }

        if (empty($keyword) && ($page < 0 || $pageSize < 0)) {
            if (!in_array($sdType, [2, 3])) {
                $this->apiReturn(self::CODE_SUCCESS, [], 'success');
            }
        }

        $statusArr = !empty($status) ? explode(',', $status) : [];
        if (is_array($statusArr) && empty($statusArr)) {
            $statusArr = [1, 2, 3];
        }

        $landModel = new Land('slave');

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition();
        // 全禁的情况下不需要调用接口
        if ($condition === false) {
            $landInfo = [];
        } else if (in_array($sdType, [2, 3]) && $action == 'queryproduct') { //景区账号，资源方账号

            if ($sdType == 3) {
                //合并过的终端并且是主体
                $dataList    = $landModel->getShareTerminalByNowSalerId($saccount);
                $salerIdList = array_column($dataList, 'preSalerID');
            } else {
                $salerIdList = [$saccount];
            }

            $landApi  = new \Business\CommodityCenter\Land();
            $landInfoRes = $landApi->queryLandMultiQueryByAdminAndPaging([], $page, $pageSize, $keyword, 'id desc',
                false, [], $statusArr, [], [], $salerIdList, null, null, false, ['F','Q']);

            $landInfo = [];
            if ($landInfoRes['list']) {
                $landInfo = $landInfoRes['list'];
            }

            //$landInfo = $landModel->getLandInfoBySalerId($salerIdList, false,
            //    'id,imgpath,p_type,terminal,title,salerid,status', true, $landStatus, $keyword, $page, $pageSize,
            //    $statusArr);
        } else {
            $landInfo    = [];
            $sid = $this->_sid;
            $subMerchantId = 0;
            if (MemberLoginHelper::isSubMerchantLogin()){
                $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $subMerchantId = MemberLoginHelper::getLoginBusinessMember()->getSId();
            }
            $applyDidArr = [$sid];

            $lidArr = $condition['lidList'] ?? [];
            $javaApi     = new \Business\CommodityCenter\Land();
            $landInfoArr = $javaApi->queryLandMultiQueryByAdminAndPaging($lidArr, $page, $pageSize, $keyword,
                $orderByClause = '', false, $applyDidArr, $statusArr, [], [], [], null, null,
                false, ['F','Q'], $subMerchantId, $condition);
            if ($landInfoArr['list']) {
                $landInfo = $landInfoArr['list'];
            }
            //$landInfo = $landModel->getApplyAllLand($statusArr, $this->_sid, $keyword, $page, $pageSize);
        }

	    $terminalModel = new Terminal();
	    foreach($landInfo as $index=> $info){
		    $mainTerminal = $info['terminal'];
		    $memberId = $info['apply_did'];
		    $subTerminalList = $terminalModel->getSubList($mainTerminal, false, $memberId);
		    if(is_array($subTerminalList) && count($subTerminalList)>0){
			    $terminalList = [];
			    $terminalNoList = [];
			    foreach($subTerminalList as $_){
				    $row = [];
				    /* if($_['preTerminal'] == $mainTerminal){  //主终端也要显示
					    continue;
				    } */
				    $row['id'] = $_['id'];
				    $row['terminal'] = $_['preTerminal'];
				    $row['name'] = $_['name'];
				    $terminalList[] = $row;
				    $terminalNoList[] = $row['terminal'];
			    }

			    // 排序一下，主终端号肯定小于分终端。
			    array_multisort($terminalList, $terminalNoList);
			    $landInfo[$index]['sub_terminal'] = $terminalList;
		    } else {
			    $landInfo[$index]['sub_terminal'] = false;
		    }
	    }

        //新增接口处理下返回格式
        if ($isNew) {
            $landInfo = [
                'list'          => $landInfo,
                'search_status' => $statusArr,
            ];
        }

        $this->apiReturn(self::CODE_SUCCESS, $landInfo, 'success');
    }

    /**
     * 获取详情
     */
    public function getChangeLog()
    {
        $code = 200;
        $data = [];
        $msg  = '';

        try {
            $orderId       = I('get.ordernum', '', 'strval');
            $orderBusiness = new OrderList();

            if (empty($orderId)) {
                throw new Exception("订单号不能为空");
            }

            //获取
            $loginInfo  = $this->getLoginInfo();
            $sid        = $loginInfo['sid'];
            $memberType = $loginInfo['dtype'];
            $account    = $loginInfo['account'];
            $currentFid = $loginInfo['sid'];

            //判断身份 取当前符合要求的会员id集合
            $salerId = false;
            if ($this->isSuper()) {
                //管理员
                $currentFid = 1;
            }  elseif(MemberLoginHelper::isSubMerchantLogin()){
                $subMerchantId = MemberLoginHelper::getLoginBusinessMember()->getSId();
                $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $orderTools = new OrderTools();
                $orderInfo  = $orderTools->getOrderInfo($orderId, 'ordernum','ext_content');
                $extContent = json_decode($orderInfo['ext_content'] ?? null, true);
                // 判断这笔订单是否属于该子商户
                if(!(isset($extContent['subSid']) && (int)$extContent['subSid'] == $subMerchantId)){
                    throw new Exception('子商户无权查看此订单');
                }
                $currentFid = $sid;
            }
            elseif ($memberType == 7) {
                //集团账号
                $orderSearchBusiness = new OrderSearch();
                $currentFid          = $orderSearchBusiness->getRelationMember($sid);
            } elseif (in_array($memberType, [2, 3])) {
                //景区账号
                $salerId    = $account;
                $orderTools = new OrderTools();
                $orderInfo  = $orderTools->getOrderInfo($orderId, 'salerid');

                if ($salerId != $orderInfo['salerid']) {
                    throw new Exception('无权查看此订单');
                }
            } else {
                //对当前查询订单的用户做权限校验
                //$chainModel = new \Model\Order\SubOrderQuery\SubOrderSplit();
                //$orderChain = $chainModel->getListByOrderSingle($orderId, 'id,buyerid,sellerid');

                //订单查询迁移三期
                $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
                $orderChain             = $orderAidsSplitQueryLib->getListByOrderSingleNew(strval($orderId));

                if (empty($orderChain)) {
                    throw new Exception('订单分销链数据不存在');
                }

                $buyerid  = array_column($orderChain, 'buyerid');
                $sellerid = array_column($orderChain, 'sellerid');
                $members  = array_unique(array_merge($buyerid, $sellerid));

                if ($currentFid != 1 && !in_array($currentFid, $members)) {
                    throw new Exception('无权查看此订单');
                }
            }

            $data = $orderBusiness->getChangeLogByOrderIdArr($orderId, $currentFid, $memberType);
        } catch (Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 获取所有产品类型
     * <AUTHOR>
     * @date   2019-05-21
     *
     */
    public function getProductType()
    {
        $type        = I('type', 0, 'intval');
        $productType = [];
        if ($type) {
            // $type = 1 旅游主题  2 商品类型
            if ($type == 1) {
                $productType = array_keys(load_config('tourism_type', 'account'));
            } elseif ($type == 2) {
                $productType = array_keys(load_config('product_type', 'account'));
            }
        }
        $typeDesc   = (new \Business\Product\Product())->getProductType($productType);
        $returnData = ['product_type' => $typeDesc];
        $this->apiReturn(200, $returnData, 'success');
    }

    /**
     * 获取用户开通微信相关业务的模块
     *
     * @params int   $module_id   模块ID
     *
     * <AUTHOR>
     * @date   2020-02-26
     */
    public function getUserOpenWeChatProductUrlList()
    {
        $lid = I('lid', 0, 'intval');
        $aid = I('aid', 0, 'intval');

        if (!$lid) {
            $this->apiReturn(204, [], 'lid参数缺失');
        }

        if (empty($aid)) {
            $this->apiReturn(204, [], 'aid参数缺失');
        }

        $moduleBiz = new Module();
        $result    = $moduleBiz->getUserOpenWeChatModuleUrlListByLid($lid, $this->_loginInfo['sid'],
            $this->_loginInfo['saccount'], $aid);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], 'success');
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 获取用户可销售的产品根据lid
     *
     * @params int   $module_id   模块ID
     *
     * <AUTHOR>
     * @date   2020-02-26
     */
    public function getUserCanSaleProductAndPrice()
    {
        $lid = I('get.lid', 0);
        if (!$lid) {
            $this->apiReturn(204, [], '参数缺失');
        }
        $productBiz = new \Business\Product\Product();
        $result     = $productBiz->getUserCanSaleProductService($this->_loginInfo['sid'], $lid);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], 'success');
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * OTA飞猪配置页面产品列表
     * @Author: zhujb
     * 2020/2/21
     */
    public function getFlyingPigProductList()
    {
        $keyword = I('post.keyword', '', 'strval');
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 20, 'intval');

        if (empty($keyword)) {
            $this->apiReturn(200, [], 'success');
        }

        $sid = $this->_sid;
        $data['fid']        = $sid;
        $data['title']      = $keyword;
        $data['pageNumber'] = $page;
        $data['pageSize']   = $size;

        $landListQueryService = new LandListQueryService();
        $landInfoArr          = $landListQueryService->queryEvoluteLandTitleList($data);
        if ($landInfoArr['code'] != 200 || empty($landInfoArr['data']['list'])) {
            $this->apiReturn(204, [], '暂无数据');
        }

        $lidArr = array_unique(array_column($landInfoArr['data']['list'], 'landId'));
        //$productBiz = new \Business\Product\Product();
        //
        //$evoluteArr = $productBiz->getSaleProductByCondition($sid, 0, 0, 0, $keyword);
        //$lidArr     = array_column($evoluteArr['list'], 'lid');

        // 获取景区绑定的对接系统
        $sysConfigModel = new SysConfig();
        $sysResArr      = $sysConfigModel->getCsysByLids($lidArr, 'lid, notice_code', true);

        // 获取景区列表
        //$landModel = new Land('slave');
        //$landArr   = $landModel->getLandInfoByLids($lidArr, 'id,title');

        $javaAPi = new \Business\CommodityCenter\Land();
        $landArr = $javaAPi->queryLandMultiQueryById($lidArr);

        $res = [];
        if ($landArr) {
            foreach ($landArr as $key => $land) {
                // 去掉不发码的景区
                if (isset($sysResArr[$land['id']]['notice_code']) && $sysResArr[$land['id']]['notice_code'] == 3) {
                    continue;
                }

                //is_conf = 1显示配置页面，is_conf = 0不显示配置页面
                $tbMemberArr     = $this->_tbMember();
                $land['is_conf'] = 0;
                //判断上级是否在授权列表中
                if (in_array($sid, $tbMemberArr)) {
                    $land['is_conf'] = 1;
                }
                $res[] = $land;
            }
        }

        $this->apiReturn(200, $res, 'success');
    }

    /**
     * 需要有淘宝返码方式配置页面的用户列表\
     * @Author: liucm
     * 2021/6/11
     */
    private function _tbMember()
    {
        return [
            113,
            94,
            9712273,
        ];
    }

    /**
     * 获取淘宝返码方式配置
     * @Author: liucm
     * 2021/6/11
     */
    public function getTbTicketCodeConf()
    {
        $returnData = [
            'notice_type' => 1,
            'is_show'     => 1,
        ];
        $sid        = $this->_sid;
        $tid        = I('post.tid', 0, 'intval');
        if (empty($tid)) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model = new ApiTbTicketCodeConfModel();
        $res   = $model->getApiTbTicketCodeConf($sid, $tid);
        if (!empty($res)) {
            $returnData = [
                'notice_type' => $res['notice_type'],
                'is_show'     => $res['is_show'],
            ];
        }

        $this->apiReturn(200, $returnData, 'success');
    }

    /**
     * 增加或修改淘宝返码方式配置
     * @Author: liucm
     * 2021/6/11
     */
    public function addOrUpdateTbTicketCodeConf()
    {
        $tid        = I('post.tid', 0, 'intval');
        $noticeType = I('post.notice_type', -1, 'intval');
        $isShow     = I('post.is_show', -1, 'intval');
        $memberId   = $this->_sid;
        $opid       = $this->_loginInfo['memberID'];
        if (empty($tid) || $noticeType == -1 || $isShow == -1) {
            $this->apiReturn(204, [], '请求参数错误');
        }

        $model   = new ApiTbTicketCodeConfModel();
        $ConfRes = $model->getApiTbTicketCodeConf($memberId, $tid);
        if (!empty($ConfRes)) {
            $res     = $model->updateApiTbTicketCodeConf($memberId, $tid, $noticeType, $isShow, $opid);
            $logData = [
                'key'     => '修改淘宝返码配置',
                'oldData' => [
                    'member_id'   => $ConfRes['member_id'],
                    'tid'         => $ConfRes['tid'],
                    'notice_type' => $ConfRes['notice_type'],
                    'is_show'     => $ConfRes['is_show'],
                    'opid'        => $ConfRes['opid'],
                ],
                'newData' => [
                    'member_id'   => $memberId,
                    'tid'         => $tid,
                    'notice_type' => $noticeType,
                    'is_show'     => $isShow,
                    'opid'        => $opid,
                ],
                'res'     => $res,
            ];
            pft_log('open_platform/down_system/feizhu/config', json_encode($logData));
            if ($res !== false) {
                $this->apiReturn(200, [], '修改成功');
            }
            $this->apiReturn(204, [], '修改操作失败，请重试');
        }

        $res     = $model->addApiTbTicketCodeConf($memberId, $tid, $noticeType, $isShow, $opid);
        $logData = [
            'key'     => '新增淘宝返码配置',
            'newData' => [
                'member_id'   => $memberId,
                'tid'         => $tid,
                'notice_type' => $noticeType,
                'is_show'     => $isShow,
                'opid'        => $opid,
            ],
            'res'     => $res,
        ];
        pft_log('open_platform/down_system/feizhu/config', json_encode($logData));
        if ($res !== false) {
            $this->apiReturn(200, [], '新增成功');
        }

        $this->apiReturn(204, [], '新增操作失败，请重试');
    }

    /**
     * OTA飞猪配置页面产品票列表
     * @Author: zhujb
     * 2020/2/21
     */
    public function getFlyingPigTicketList()
    {
        $lid        = I('post.lid', 0, 'intval');
        $combinedId = I('post.combined_id', '', 'strval,trim');
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');
        $supplierId = I('post.sid', 0, 'intval');
        $pidtmp     = I('post.pid', 0, 'intval');

        if (empty($lid) && empty($pidtmp) && empty($supplierId)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $pid = 0;
        if ($combinedId) {
            if (false === strpos($combinedId, '|')) {
                $this->apiReturn(204, [], '参数错误');
            }

            $explodeId = explode('|', $combinedId);
            if (!isset($explodeId[0])) {
                $this->apiReturn(204, [], '参数错误');
            }

            $pid = intval($explodeId[0]);
        }

        if (!empty($pidtmp)) {
            $pid = $pidtmp;
        }

        // 获取指定景区的可售产品
        $productBiz = new \Business\Product\Product();
        $sid        = $this->_sid;

        if ($pid) {
            $evoluteArr = $productBiz->getSaleProductByCondition($supplierId, $sid, null, $pid, null, null, null, [], null,
                $page, $size);
        } else {
            $evoluteArr = $productBiz->getSaleProductByCondition($supplierId, $sid, $lid, null, null, null, null, null, null,
                $page, $size);
        }

        $total  = $evoluteArr['total'];
        $tidArr = array_column($evoluteArr['list'], 'ticket_id');
        $lidArr = array_column($evoluteArr['list'], 'lid');

        $memberBiz         = new \Business\Member\Member();
        $memberRelationBiz = new MemberRelationQuery();

        // 查出跟当前账户有分销关系的所有供应商
        $suppliers   = [];
        $javaData    = [
            'sonIds'     => [$sid],
            'sonIdTypes' => [0],
        ];
        $javaRes     = $memberRelationBiz->queryMemberRelation($javaData);
        $supplyIdArr = [];
        if ($javaRes['code'] == 200) {
            $supplyIdArr = array_column($javaRes['data'], 'parent_id');
        }
        if ($supplyIdArr) {
            $suppliers = $memberBiz->getList($supplyIdArr);
        }
        $suppliers[$sid] = ['dname' => $this->_loginInfo['dname']];

        //$ticketApi = new \Business\JavaApi\TicketApi();
        $priceApi = new Price;

        // 获取门票属性
        $ticketModel = new \Model\Product\Ticket('slave');
        $ticketData  = [];
        $ticketArr   = $ticketModel->getTicketInfoByIdArr($tidArr,
            'id,title,refund_audit,delaydays,delaytype,order_start,order_end');
        if ($ticketArr) {
            foreach ($ticketArr as $ticket) {
                $ticketData[$ticket['id']] = $ticket;
            }
        }

        // 获取景区属性
        //$landModel = new \Model\Product\Land('slave');
        $landData  = [];
        //$landArr   = $landModel->getLandInfoByLids($lidArr, 'id,title');

        $javaAPi = new \Business\CommodityCenter\Land();
        $landArr = $javaAPi->queryLandMultiQueryById($lidArr);
        if ($landArr) {
            foreach ($landArr as $land) {
                // 如果是旅游券产品。返回空
                if ($land['p_type'] == 'Q') {
                    $res = [
                        'list'  => [],
                        'total' => 0,
                    ];
                    $this->apiReturn(200, $res, 'success');
                }
                $landData[$land['id']] = $land;
            }
        }

        $list = [];
        foreach ((array)$evoluteArr['list'] as $item) {
            // 不是直接分销给当前用户的分销链  要过滤
            if (!isset($suppliers[$item['sid']])) {
                continue;
            }

            // 实时价格获取
            $priceRes = $priceApi->getActualtimePrice($this->_loginInfo['sid'], $item['sid'], $item['ticket_id'],
                false);
            // 返回错误或没有价格
            if ($priceRes['code'] != 200 || $priceRes['data']['settlement_price'] == -1) {
                continue;
            }

            // 如果有套票的某个子票绑定了第三方，则过滤掉
            if ($item['p_type'] == 'F') {
                //$javaApi       = new \Business\JavaApi\Product\PackageTicket();
                //$ticketInfoArr = $javaApi->queryPageTicketInfoListByParentId($item['ticket_id']);
                //$ticketInfoArr = $ticketApi->getSonTicketList($item['ticket_id']);
                $packApi       = new PackRelation();
                $ticketInfoArr = $packApi->queryPageTicketInfoListByParentId($item['ticket_id']);
                if (isset($ticketInfoArr['data']) && $ticketInfoArr['data']) {
                    $tidArr           = array_column($ticketInfoArr['data'], 'ticket_id');
                    $otaProductBiz    = new \Business\Ota\Product();
                    $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTidArr($tidArr);
                    if (!empty($thirdBindInfoArr)) {
                        $mdetailsArr = array_column($thirdBindInfoArr, 'Mdetails');
                        $maxVal      = max($mdetailsArr);
                        if ($maxVal > 0) {
                            continue;
                        }
                    }
                }
            }
            $isBindThird = 0;
            // 获取门票绑定三方信息
            $otaProductBiz      = new \Business\Ota\Product();
            $thirdTicketConfArr = $otaProductBiz->getTicketConfigByTid($item['ticket_id']);
            $uuid               = $thirdTicketConfArr['uuid'];
            //$Mdetails           = $thirdTicketConfArr['Mdetails'];
            // 判断票是否绑定
            if (!empty($uuid)) {
                $isBindThird = 1;
            }

            $list[] = array(
                'pid'           => $item['pid'],
                'sapply_did'    => $item['sid'],
                'dname'         => $suppliers[$item['sid']]['dname'],
                'ttitle'        => $ticketData[$item['ticket_id']]['title'],
                'ltitle'        => $landData[$item['lid']]['title'],
                'js'            => $priceRes['data']['settlement_price'] / 100,
                'ls'            => $priceRes['data']['retail_price'] / 100,
                'lid'           => $item['lid'],
                'tid'           => $item['ticket_id'],
                'refund_audit'  => $ticketData[$item['ticket_id']]['refund_audit'],
                'effective'     => $this->_htmlValid($item),
                'delaydays'     => $ticketData[$item['ticket_id']]['delaydays'],
                'delaytype'     => $ticketData[$item['ticket_id']]['delaytype'],
                'order_start'   => $ticketData[$item['ticket_id']]['order_start'],
                'order_end'     => $ticketData[$item['ticket_id']]['order_end'],
                'fid'           => $sid,
                'is_bind_third' => $isBindThird,
            );
        }

        //$total = count($list);

        $res = [
            'list'  => $list,
            'total' => (int)$total,
        ];
        $this->apiReturn(200, $res, 'success');
    }

    /**
     * OTA飞猪配置页面产品票所有的列表
     *
     * Author : liucm
     * Date : 2021/7/23
     * @throws Exception
     */
    public function getFeiZhuTicketList()
    {
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');
        $keyword    = I('post.keyword', null, 'strval');
        // 获取指定景区的可售产品
        $productBiz = new \Business\Product\Product();
        $sid        = $this->_sid;

        $evoluteGroupSpecialityService = new EvoluteGroupSpecialityService();
        $data = $evoluteGroupSpecialityService->otaTicketAllList($sid, $page, $size, [], $keyword, '', '');

        $evoluteArr = $data['data'];
        //$evoluteArr = $productBiz->getSaleProductByCondition(0, $sid, null, null, $keyword, null, null, null, null,
        //    $page, $size);
        //print_r($evoluteArr);exit();
        $total  = $evoluteArr['total'];
        $tidArr = array_column($evoluteArr['list'], 'ticketId');
        $lidArr = array_column($evoluteArr['list'], 'lid');

        $memberBiz         = new \Business\Member\Member();
        $memberRelationBiz = new MemberRelationQuery();

        // 查出跟当前账户有分销关系的所有供应商
        $suppliers   = [];
        $javaData    = [
            'sonIds'     => [$sid],
            'sonIdTypes' => [0],
        ];

        $javaRes     = $memberRelationBiz->queryMemberRelation($javaData);
        $supplyIdArr = [];
        if ($javaRes['code'] == 200) {
            $supplyIdArr = array_column($javaRes['data'], 'parent_id');
        }
        if ($supplyIdArr) {
            $suppliers = $memberBiz->getList($supplyIdArr);
        }
        //$suppliers[$sid] = ['dname' => $this->_loginInfo['dname']];

        //$ticketApi = new \Business\JavaApi\TicketApi();
        $priceApi = new Price;


        // 获取门票属性
        $ticketModel = new \Model\Product\Ticket('slave');
        $ticketData  = [];
        $ticketArr   = $ticketModel->getTicketInfoByIdArr($tidArr,
            'id,title,refund_audit,delaydays,delaytype,order_start,order_end');
        if ($ticketArr) {
            foreach ($ticketArr as $ticket) {
                $ticketData[$ticket['id']] = $ticket;
            }
        }

        // 获取景区属性
        $landData = [];
        $landApi  = new \Business\CommodityCenter\Land();
        $landArr  = $landApi->queryLandInfoByIds($lidArr);
        if ($landArr) {
            $landData = array_column($landArr, null, 'id');
        }

        $list = [];
        foreach ((array)$evoluteArr['list'] as $item) {
            // 不是直接分销给当前用户的分销链  要过滤
            //if (!isset($suppliers[$item['sid']])) {
            //    continue;
            //}
            // 实时价格获取
            $priceRes = $priceApi->getActualtimePrice($this->_loginInfo['sid'], $item['sid'], $item['ticketId'], false);
            // 返回错误或没有价格
            if ($priceRes['code'] != 200 || $priceRes['data']['settlement_price'] == -1) {
                continue;
            }

            // 如果有套票的某个子票绑定了第三方，则过滤掉
            if ($item['ptype'] == 'F') {
                //$javaApi       = new \Business\JavaApi\Product\PackageTicket();
                //$ticketInfoArr = $javaApi->queryPageTicketInfoListByParentId($item['ticketId']);
                //$ticketInfoArr = $ticketApi->getSonTicketList($item['ticketId']);
                $packApi       = new PackRelation();
                $ticketInfoArr = $packApi->queryPageTicketInfoListByParentId($item['ticketId']);
                if (isset($ticketInfoArr['data']) && $ticketInfoArr['data']) {
                    $tidArr           = array_column($ticketInfoArr['data'], 'ticket_id');
                    $otaProductBiz    = new \Business\Ota\Product();
                    $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTidArr($tidArr);
                    if (!empty($thirdBindInfoArr)) {
                        $mdetailsArr = array_column($thirdBindInfoArr, 'Mdetails');
                        $maxVal      = max($mdetailsArr);
                        if ($maxVal > 0) {
                            continue;
                        }
                    }
                }
            }
            $isBindThird = 0;
            // 获取门票绑定三方信息
            $otaProductBiz      = new \Business\Ota\Product();
            $thirdTicketConfArr = $otaProductBiz->getTicketConfigByTid($item['ticketId']);
            $uuid               = $thirdTicketConfArr['uuid'];
            //$Mdetails           = $thirdTicketConfArr['Mdetails'];
            // 判断票是否绑定
            if (!empty($uuid)) {
                $isBindThird = 1;
            }

            $account = $suppliers[$item['sid']]['account'] ?? '';
            $list[] = array(
                'pid'           => $item['pid'],
                'sapply_did'    => $item['sid'],
                'dname'         => $suppliers[$item['sid']]['dname'],
                'ttitle'        => $ticketData[$item['ticketId']]['title'],
                'ltitle'        => $landData[$item['lid']]['title'],
                'js'            => $priceRes['data']['settlement_price'] / 100,
                'ls'            => $priceRes['data']['retail_price'] / 100,
                'lid'           => $item['lid'],
                'tid'           => $item['ticketId'],
                'refund_audit'  => $ticketData[$item['ticketId']]['refund_audit'],
                'effective'     => $this->_htmlValid($item),
                'delaydays'     => $ticketData[$item['ticketId']]['delaydays'],
                'delaytype'     => $ticketData[$item['ticketId']]['delaytype'],
                'order_start'   => $ticketData[$item['ticketId']]['order_start'],
                'order_end'     => $ticketData[$item['ticketId']]['order_end'],
                'fid'           => $sid,
                'is_bind_third' => $isBindThird,
                'account'       => $account,
            );
        }

        //$total = count($list);

        $res = [
            'list'  => $list,
            'total' => $total,
        ];
        $this->apiReturn(200, $res, 'success');
    }

    protected function _htmlValid($p_info)
    {
        if ($p_info['delaydays'] > 0) {
            if ($p_info['delaytype'] == 0) {
                $valid = '游玩日期(含)' . $p_info['delaydays'] . '天内有效';
            }

            if ($p_info['delaytype'] == 1) {
                $valid = '下单日期(含)' . $p_info['delaydays'] . '天内有效';
            }

        }
        if ($p_info['order_start'] == '' && $p_info['delaydays'] == 0) {
            $valid = '订单当天有效';
        }

        if ($p_info['order_start'] != '' && $p_info['order_end'] != '') {
            $valid = '有效日期从' . $p_info['order_start'] . ' 至 ' . $p_info['order_end'];
        } elseif ($p_info['order_start'] == '' && $p_info['order_end'] != '') {
            $valid = '截止' . $p_info['order_end'];
        }

        return $valid;
    }

    /**
     * 开通医护免费票
     * <AUTHOR>
     */
    public function openFreeDoctorNurseTicket()
    {
        if (!$this->isSuper()) {
            $this->apiReturn(400, [], '无权限');
        }

        $applyId   = I('post.apply_id', 0, 'intval');
        $lids      = I('post.lids', '', 'strval');
        $landTitle = I('post.landTitle', '', 'strval');

        if (empty($applyId) || empty($lids)) {
            $this->apiReturn(400, [], '参数缺失');
        }

        $lids = explode(',', $lids);

        $biz    = new \Business\FreeTicket4DoctorNurse\Product();
        $result = $biz->createOrUpdateFreeTicket($this->_loginInfo['memberID'], $applyId, $lids, $landTitle);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 关闭供应商下所有免费门票
     * <AUTHOR>
     */
    public function takeOffFreeTicketByApplyId()
    {
        if (!$this->isSuper()) {
            $this->apiReturn(400, [], '无权限');
        }

        $applyId = I('post.apply_id', 0, 'intval');

        $biz    = new \Business\FreeTicket4DoctorNurse\Product();
        $result = $biz->takeOffFreeTicketByApplyId($this->_loginInfo['memberID'], $applyId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 关闭供应商下指定免费门票通过
     * <AUTHOR>
     */
    public function takeOffFreeTicketByApplyIdAndLandIds()
    {
        if (!$this->isSuper()) {
            $this->apiReturn(400, [], '无权限');
        }

        $applyId = I('post.apply_id', 0, 'intval');
        $lids    = I('post.lids', '', 'strval');

        $biz    = new \Business\FreeTicket4DoctorNurse\Product();
        $result = $biz->takeOffFreeTicketByLandIdArr($this->_loginInfo['memberID'], $applyId, $lids);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取供应商自供应产品上架中
     * <AUTHOR>
     */
    public function getOnShelvesProduct()
    {
        if (!$this->isSuper()) {
            $this->apiReturn(400, [], '无权限');
        }

        $applyId  = I('get.apply_id', 0, 'intval');
        $page     = I('get.page', 1, 'intval');
        $pageSize = I('get.page_size', 10, 'intval');

        $biz    = new \Business\FreeTicket4DoctorNurse\Product();
        $result = $biz->getSelfProductList($applyId, $page, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据景区id 批量下架预售门票
     *
     * @param  int  $lid
     *
     */
    public function batchDelPreSaleByLid()
    {
        $lid = I('lid', 0);
        if (empty($lid)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        $ticketBiz = new \Business\JavaApi\Product\Ticket();
        $res       = $ticketBiz->batchDelistingPreSaleTicket($lid, $this->_sid, $this->_loginInfo['memberID']);
        if ($res) {
            $this->apiReturn(200, [], 'success');
        }

        $this->apiReturn(205, [], '批量下架失败，请稍后重试');
    }

    /**
     * 获取演出场馆列表
     * <AUTHOR>
     * @date 2020/5/12
     *
     * @return array
     */
    public function getVenues()
    {
        $showModel  = new Show();
        $memberInfo = $this->getLoginInfo();
        $result     = $showModel->getVenueList($memberInfo['sid'], 0);
        if (!$result) {
            $this->apiReturn(200, ['list' => []], 'success');
        }
        $this->apiReturn(200, ['list' => $result], 'success');
    }

    /**
     * 获取产品编辑信息
     * <AUTHOR>
     * @date 2020/5/18
     *
     * @return array
     */
    public function getLandInfoByMod()
    {
        $landId = I('post.land_id', 0, 'intval');
        if (!$landId) {
            $this->apiReturn(203, [], '参数缺失');
        }
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        if ($dataAuthLimit->hasLidBeenLimit($landId)) {
            $this->apiReturn(403, [], '没有权限查看该产品');
        }
        //$memberInfo  = $this->getLoginInfo();
        $productBiz  = new \Business\Product\Product();
        $landInfoArr = $productBiz->getLandInfo($landId);
        if ($landInfoArr['code'] != 200 || !$landInfoArr['data']) {
            $this->apiReturn(203, [], '产品不存在');
        }

        $jdata = $landInfoArr['data'];

        // 数组键名转换
        $jdata['title']      = $jdata['name'];
        $jdata['jtype']      = $jdata['level'];
        $jdata['p_type']     = $jdata['type'];
        $jdata['resourceID'] = $jdata['resource_id'];
        $jdata['apply_did']  = $jdata['account_id'];
        $jdata['imgpath']    = $jdata['img_path'];
        $jdata['imgpathGrp'] = $jdata['img_path_group'];
        $jdata['bhjq']       = $jdata['details'];
        $jdata['jqts']       = $jdata['notice'];
        $jdata['jtzn']       = $jdata['traffic'];
        $jdata['topic']      = $jdata['topics'];
        $jdata['lngLatPos']  = $jdata['lng_lat_pos'];
        $jdata['ext']        = $jdata['ext'] ?? (object)[];
        //$jdata['area']       = $jdata['province_name'] . "|" . $jdata['city_name'];

        //if ($jdata['apply_did'] != $memberInfo['sid']) {
        //    $this->apiReturn(203, [], '这不是你的产品');
        //}

        if ($jdata['order_flag'] == 1) {
            // 返回团队订单预定的规则属性
            if (isset($jdata['group_limit']) && !empty($jdata['group_limit'])) {
                $groupLimitArr = json_decode($jdata['group_limit'], true);
            }
            $groupNumLimit['is_limit']     = 0;
            $groupNumLimit['limit_number'] = 0;
            if (isset($jdata['group_number_limit']) && $jdata['group_number_limit'] == 1) {
                $groupNumLimit['is_limit']     = 1;
                $groupNumLimit['limit_number'] = isset($groupLimitArr['group_number_limit']['limit_min_num']) ? $groupLimitArr['group_number_limit']['limit_min_num'] : 0;
            }
            $groupTicketLimit['is_limit'] = 0;
            $groupTicketLimit['tickets']  = [];
            if (isset($jdata['group_ticket_limit']) && $jdata['group_ticket_limit'] == 1) {
                $groupTicketLimit['is_limit'] = 1;
                $groupTicketLimit['tickets']  = isset($groupLimitArr['group_ticket_limit']) ? $groupLimitArr['group_ticket_limit'] : [];
            }
            $jdata['group_ticket_limit'] = $groupTicketLimit;
            $jdata['group_number_limit'] = $groupNumLimit;
        }

        $jdata['resourceName'] = '';
        if (!empty($jdata['resource_id'])) {
            $landResource          = new LandResource();
            $resourceRes           = $landResource->findResourceById($jdata['resource_id'], 'title');
            $jdata['resourceName'] = $resourceRes['title'] ?? '';
        }

        if (!$jdata) {
            $this->apiReturn(203, [], '产品不存在');
        }

        $imgUrl = $jdata['imgpath'] ?? 'images/defaultThum.jpg';

        //轮播展示图
        $imgArrInfo = [];
        if ($jdata['imgpathGrp']) {
            $imgUrls = @unserialize($jdata['imgpathGrp']);
            $imgUrls = is_array($imgUrls) ? $imgUrls : json_decode($jdata['imgpathGrp'], true);

            $count = count($imgUrls);
            for ($i = 0; $i < $count; $i++) {
                $imgArrInfo[$i] = (is_array($imgUrls) && $imgUrls[$i]) ? $imgUrls[$i] : 'images/defaultThum.jpg';
            }
            $imgArrString = implode(",", $imgArrInfo);
        }

        //套票子票景点营业时间
        $sonRunTime = [];
        if ($jdata['p_type'] == 'F') {
            $packBiz   = new PackTicket();
            $ticketBiz = new ticketBiz();
            $landBiz   = new \Business\JavaApi\Product\Land();
            //这边需要获取子票名称+子票营业时间
            //先获取出景区下所有门票的主票id
            $parentTicketList = $ticketBiz->queryTicketListByLandId($landId);
            if (empty($parentTicketList)) {
                $this->apiReturn(203, [], '查询不到套票景区信息');
            }

            $parentTicketArr = array_column($parentTicketList, 'title','id');
            $parentTidArr    = array_keys($parentTicketArr);

            $sonTicketIdList = $packBiz->getTickets($parentTidArr);
            if (!$sonTicketIdList) {
                $this->apiReturn(203, [], '查询不到套票子票信息');
            }


            $lidArr = array_values(array_unique(array_column($sonTicketIdList, 'lid')));

            $packArr = [];
            foreach ($sonTicketIdList as $item) {
                $packArr[$item['parent_tid']][$item['lid']] = $item['lid'];
            }

            $landInfoRes = $landBiz->queryLandByIds($lidArr);
            //通过景区id获取对应景区的营业时间
            if ($landInfoRes['code'] != 200 || empty($landInfoRes['data'])) {
                $this->apiReturn(203, [], '景区信息获取异常');
            }

            $landInfo = array_column($landInfoRes['data'], null, 'id');

            //数据组合拼接
            foreach($packArr as $parentId => $parentInfo) {
                foreach($parentInfo as $lid => $item) {
                    if (isset($landInfo[$lid]) && $landInfo[$lid] && !isset($sonRunTime[$lid])) {
                        $ltitle      = $landInfo[$lid]['title'] ?? '';
                        $runtime     = $landInfo[$lid]['runtime'] ?? '';
                        $sonRunTime[$lid] = [
                            'ltitle'  => $ltitle,
                            'runtime' => $runtime,
                        ];
                    }

                }
            }
        }
        $jdata['runtime_arr'] = $sonRunTime;

        if (!$imgArrString) {
            $imgArrString = '';
        }

        $jdata['end_place_tude'] = $jdata['lng_lat_pos'] ? $jdata['lng_lat_pos'] : '0,0';
        $jdata['imgUrl']         = $imgUrl;
        $jdata['imgArrString']   = $imgArrString;

        unset($jdata['group_limit'], $jdata['account_id'], $jdata['add_time'], $jdata['details'], $jdata['areacode'],
            $jdata['imgpath'], $jdata['imgpathGrp'], $jdata['verify_status'], $jdata['update_time'], $jdata['traffic'],
            $jdata['topics'], $jdata['terminal'], $jdata['status'], $jdata['sms_phone'], $jdata['ship_route_code'], $jdata['salerid'],
            $jdata['resource_id'], $jdata['province_name'], $jdata['operater_id'], $jdata['notice'], $jdata['name'], $jdata['lng_lat_pos'],
            $jdata['level'], $jdata['letters'], $jdata['introduce'], $jdata['img_path_group'], $jdata['img_path'], $jdata['city_name'],
            $jdata['attribute']);

        $this->apiReturn(200, $jdata, '');
    }

    /**
     * 处理营销属性需要的票扩展属性
     *
     * @param  array  $data  getList接口返回的  $result['data']['products'] 使用
     *
     * @param  array $data getList接口返回的  $result['data']['products'] 使用
     *
     * @return array
     * <AUTHOR>
     * @date 2021/2/22
     *
     * */
    public function ticketExtendAttrConf($data)
    {
        if (empty($data)) {
            return [];
        }
        $ticketData = array_column($data, 'tickets');
        if (empty($ticketData)) {
            return $data;
        }
        $tidArr = [];
        foreach ($ticketData as $item) {
            $tidArr = array_merge(array_column($item, 'ticketId'), $tidArr);
        }
        // 额外填写更多信息
        $ticketExtendAttr    = new TicketExtendAttr();
        $ticketSpecialValRes = $ticketExtendAttr->queryTicketSpecialVal($tidArr,
            ['is_start_stop_sale_limit', 'auto_sale_rule', 'is_online_reserve']);

        if ($ticketSpecialValRes['code'] != 200) {
            return $data;
        }
        $ticketSpecialValRes = $ticketSpecialValRes['data'];
        $autoArr             = [];
        $ticketAttr          = [];
        $ticketOnline        = [];
        foreach ($ticketSpecialValRes as $ticketConf) {
            if ($ticketConf['key'] == 'auto_sale_rule') {
                $autoArr[] = $ticketConf;
            }
            if ($ticketConf['key'] == 'is_start_stop_sale_limit') {
                $ticketAttr[] = $ticketConf;
            }
            if ($ticketConf['key'] == 'is_online_reserve') {
                $ticketOnline[] = $ticketConf;
            }
        }
        $autoArr      = array_key($autoArr, 'ticketId');
        $ticketAttr   = array_key($ticketAttr, 'ticketId');
        $ticketOnline = array_key($ticketOnline, 'ticketId');

        foreach ($data as $key => &$val) {
            foreach ($val['tickets'] as $k => &$v) {
                $v['autoSaleRule']         = json_decode($autoArr[$v['ticketId']]['val'], true) ?? [];
                $v['isStartStopSaleLimit'] = $ticketAttr[$v['ticketId']]['val'] ?? 0;
                $v['isOnlineReserve']      = $ticketOnline[$v['ticketId']]['val'] ?? 0;
            }
        }

        return $data;
    }

    /**
     * 检测用户是否开通了限时抢购
     * @return array
     * <AUTHOR>
     * @date 2021/2/22
     *
     *
     */
    public function checkUserOpenSeckill()
    {
        $sid         = $this->_sid;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }
        $moduleCheck = new ModuleList();
        $res         = $moduleCheck->getMemberUseModuleAuth([40], $sid);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /** 通过景区id获取景区下门票对应团单的配置
     * <AUTHOR>  Li
     * @data  2021-02-09
     *
     * @return array
     */
    public function getTeamOrderTicketGroupList()
    {
        $landId = I('land_id', 0, 'intval');
        if (!$landId) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $ticketBiz      = new \Business\Product\Ticket();
        $ticketGroupRes = $ticketBiz->queryTicketGroupListFlag($landId);
        if ($ticketGroupRes['code'] != 200 || empty($ticketGroupRes['data'])) {
            $this->apiReturn(204, [], '门票对应团单配置获取失败');
        }

        $this->apiReturn(200, $ticketGroupRes['data'], '门票对应团单配置获取成功');
    }

    /**
     * 编辑报团配置
     * <AUTHOR>  Li
     * @data  2021-02-09
     *
     * @return array
     */
    public function setTeamOrderTicket()
    {
        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }
        $landId        = I('land_id', 0, 'intval');         //景区id
        $enableTids    = I('enable_tid', '', 'strval');     //需要开启配置的票   多个以逗号隔开
        $disEnableTids = I('dis_enable_tid', '', 'strval'); //需要关闭配置的票   多个以逗号隔开
        $operatorId    = $this->_loginInfo['memberID'];     //操作员id

        if (!$landId) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (!$enableTids && !$disEnableTids) {
            $this->apiReturn(203, [], '配置有误，请确认！');
        }

        $enableTidArr    = explode(',', $enableTids);
        $disEnableTidArr = explode(',', $disEnableTids);

        $ticketBiz      = new \Business\Product\Ticket();
        $ticketGroupRes = $ticketBiz->updateTicketGroupListFlag($operatorId, $landId, $enableTidArr, $disEnableTidArr);

        if ($ticketGroupRes['code'] != 200) {
            $this->apiReturn(204, [], $ticketGroupRes['msg']);
        }

        $this->apiReturn(200, [], '门票团单配置成功');
    }

    /**
     * 关闭景区下所有门票团单配置
     * <AUTHOR>  Li
     * @data  2021-02-09
     *
     * @return array
     */
    public function closeTeamOrderConf()
    {
        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }
        $landId = I('land_id', 0, 'intval');

        if (!$landId) {
            $this->apiReturn(203, [], '参数缺失');
        }
        $ticketBiz      = new \Business\Product\Ticket();
        $ticketGroupRes = $ticketBiz->batchCLoseOrderFlag($landId);

        if ($ticketGroupRes['code'] != 200) {
            $this->apiReturn(204, [], $ticketGroupRes['msg']);
        }

        $this->apiReturn(200, [], '配置关闭成功');
    }
	
	/**
	 * 查询自供应产品列表
	 */
	public function searchProductListForbind()
	{
		//搜索关键字
		$keyword = I('post.keyword', '', 'strval');
		$ptype = I('post.ptype', '', 'strval');
		$page = I('post.page', 1, 'intval');
		$size = I('post.size', 10, 'intval');
		//需要用到的登录信息
		$loginInfo = $this->getLoginInfo();
		if (empty($keyword)) {
			$this->apiReturn(200, ['total' => 0, 'list' => []], '');
		}

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition();
        // 全禁的情况下不需要调用接口
        if ($condition !== false) {
            $Business = new \Business\Product\ProductZgyList;
            //获取java请求地址的接口
            $search_type = 1; //类型 1=出售中产品，2=仓库中产品，3=待发布产品
            $params      = [
                "type"            => empty($ptype) ? '' : $ptype,  //景区类型，多个类型使用逗号分隔
                "item_name"       => empty($keyword) ? '' : $keyword,     //产品名称
                "page"            => (int)$page,     //页码
                "page_size"       => (int)$size,     //条数
                "supplier_id"     => $loginInfo['sid'],   //供应商id
                'show_ticket_num' => 1,   //门票显示条数
                'is_management'   => 0,   //显示未关联景区的资源 0:否 1：是
            ];
            $params = array_merge($params, $condition);
            $result      = $Business->getJavaInterface($search_type, false, $loginInfo['sid'], $page, $size, $params);
        }

		if (!isset($result['data']['products'])) {
			$this->apiReturn(200, ['total' => 0, 'list' => []], '');
		}
		$result   = isset($result['data']) ? $result['data'] : [];
		$total    = isset($result['totalCount']) ? $result['totalCount'] : 0;
		$products = isset($result['products']) ? $result['products'] : [];

		//数据处理
		$list = [];
		foreach ($products as $tmp) {
			$item    = [
				'land_id'      => $tmp['landId'],
				'name'    => $tmp['name'],
			];
			$list[]  = $item;
		}

		$this->apiReturn(200, ['total' => $total, 'list' => $list], '');
	}

	/**
	 * 搜索产品，用途：关联联合发码配置
	 * <AUTHOR>
	 * @date 2021/3/16
	 *
	 * @return array
	 */
	public function searchProductListForbindXXXX()
	{
		//需要用到的登录信息
		$loginInfo = $this->getLoginInfo();
		$memberId  = $loginInfo['memberID']; //会员id
		$isSuper   = $this->isSuper();        //判断登陆用户是不是管理员
		$dtype     = $loginInfo['dtype'];    //登录账号类型
		$qx        = $loginInfo['qx'];       //qx判断
		$sdtype    = $loginInfo['sdtype'];  //上级供应商账号类型
		$sid       = $loginInfo['sid'];     //上级id
		//权限判断
		$Business = new ProductZgyList;
		$check    = $Business->judgeAuth($dtype, $qx, $sdtype);
		if ($check["code"] !== 0) {
			$this->apiReturn($check["code"], [], $check["msg"]);
		}
		if ($dtype == 6 && !$isSuper) {
			$memberId = $sid;                       //员工情况所属上级id
		}

		//参数接收
		// $data = businessLib::getZgyParams();
		$data = [];
		$page = $data['page'] = I('post.page', 1, 'intval');
		$pageSize = $data['page_size'] = I('post.size', 10, 'intval');
		$data['item_name'] = I('post.keyword', '', 'strval,trim');
		$data['type'] = I('post.ptype', '', 'strval');
		//类型 1=出售中产品，2=仓库中产品，3=待发布产品
		$search_type = 1;
		// var_dump($data);exit;
		//获取java请求地址的接口
		$result = $Business->getJavaInterface($search_type, $isSuper, $memberId, $page, $pageSize, $data);

		$total = !empty($result['data']['totalCount']) ? (int)$result['data']['totalCount'] : 0;
		$list = [];
		if (!empty($result['data']["products"])) {
			foreach($result['data']["products"] as $_){
				$row = [];

				$row['land_id'] = (int)$_['landId'];
				$row['name'] = $_['name'];

				$list[] = $row;
			}
		}

		$ret = [
			'total' => $total,
			'list'  => $list,
		];

		$this->apiReturn($result['code'], $ret, $result['msg']);
	}


    /**
     * 根据ticketId 获取商品ID
     * <AUTHOR>
     * @data  2021-02-09
     *
     * @return array
     */
    public function queryProductIdsByTicketIds()
    {
        $ticketid = I('post.ticket_id', 0, 'intval');
        if (!$ticketid) {
            $this->apiReturn(203, [], '参数错误');
        }

        $javaApi = new ticketBiz();
        $pidArr  = $javaApi->queryProductIdsByTicketIds([$ticketid]);
        $pidArr && $pidArr = $pidArr[0];
        $this->apiReturn(200, $pidArr, '成功');
    }

    /**
     * 获取产品详情
     * <AUTHOR>
     * @date 2021/1/21
     *
     * @return array
     */
    public function queryProductInfo()
    {
        $landId = I('post.land_id', 0, 'intval');
        if (!$landId) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $productBiz = new \Business\Product\Product();
        $productInfo = $productBiz->queryProductInfo($landId);

        $this->apiReturn($productInfo['code'], $productInfo['data'], $productInfo['msg']);
    }

    /**
     * 获取淘宝绑定关系列表
     * Author : liucm
     * Date : 2021/7/12
     */
    public function getTaoBaoBindList()
    {
        $page     = I('post.page', 0, 'intval');
        $size     = I('post.size', 0, 'intval');
        $memberID = $this->_sid;
        $tbModel  = new Taobao('slave');
        $list     = $tbModel->getBindListNew($memberID, $page, $size);
        $total    = $tbModel->getBindListCount($memberID);
        $response = [
            'total' => $total['count'],
            'list'  => $list,
        ];
        $this->apiReturn(200, $response, '成功');
    }
    /**
     * 根据用户ID和票名字模糊查询该用户所有自供应+转分销且状态in（有效+无效），即不过滤状态 的所有票列表-翻页返回
     * @author: zhangyz
     * @date: 2020/11/9
     *
     * @param  int  $fid  用户ID
     * @param  string  $ticketName  票名称模糊搜索
     * @param  int  $page  页码
     * @param  int  $size  每页数量
     *
     * @return array
     */
    public function getAllEvoluteProduct()
    {
        $state   = I('post.state', []);
        $keyWord = I('post.key_word', '', 'strval');
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 10, 'intval');
        $res     = (new EvoluteListQuery())->getAllEvoluteProduct($this->isLogin(), $state, $keyWord, $page, $size);
        foreach ($res['list'] as $k => $v) {
            $data[] = [
                'id'   => $v['tid'],
                'name' => $v['ticketName'],
            ];
        }
        $this->apiReturn(200, $data, '成功');
    }

    /**
     * 获取用户自定义配置规则
     */
    public function getConfigRuleByTicket()
    {
        $code = I('code', 'AGE_RULE', 'strval');
        $ticketId = I('tid', 0, 'intval');
        $productId = I('pid', 0, 'intval');

        if (!$ticketId && !$productId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $productBiz = new \Business\Product\Product();
        $res = $productBiz->getConfigRuleByTicket($this->_sid, $code, $ticketId, $productId);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 自供应景区产品查询
     * <AUTHOR>
     * @date   2024/07/13
     *
     */
    public function getSelfSupplyLand()
    {
        $keyWord  = I('post.key_word', '', 'trim,strval');//景区名称搜索
        $page     = I('post.page', 1, 'intval');//页码
        $size     = I('post.size', 50, 'intval');//页数
        $getTotal = I('post.get_more', 1, 'intval');//是否获取总数
        $status   = I('post.status', 0, 'intval'); //1上架 2下架 3删除

        !$status && $status = null;

        //分页限制
        empty($size) && $size = 50;
        $size > 200 && $size = 200;
        empty($page) && $page = 1;

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition     = $dataAuthLimit->transInOrNotCondition();
        // 全禁的情况下不需要调用接口
        if ($condition === false) {
            $this->apiReturn(self::CODE_SUCCESS, ['list' => [], 'total' => 0]);
        }
        $landIdArr    = empty($condition['lidList']) ? null : $condition['lidList'];
        $extCondition = empty($condition['notLidList']) ? [] : ['notLidList' => $condition['notLidList']];
        $res          = (new \Business\Product\Product())->getSaleLandListByStatusList($this->_sid, $keyWord,
            $landIdArr, $status, [1, 2, 3], '', $page, $size, $extCondition);
        if (!empty($res['list'])) {
            $data = [];
            foreach ($res['list'] as $item) {
                $data[] = [
                    'id'    => $item['id'],
                    'title' => $item['title'],
                ];
            }
        }

        $result = [
            'list' => $data ?? [],
        ];
        $getTotal && $result['total'] = $res['total'] ?? 0;

        $this->apiReturn(self::CODE_SUCCESS, $result);
    }
}
