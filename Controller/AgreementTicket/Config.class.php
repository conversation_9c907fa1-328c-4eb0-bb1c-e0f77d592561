<?php
/**
 * 协议票配置相关接口
 * <AUTHOR>
 * @date 2020/12/29
 */

namespace Controller\AgreementTicket;

use Business\JavaApi\CommodityCenter\Land;
use Business\JavaApi\EvoluteApi;
use Business\JavaApi\Product\Land as LandJavaBiz;
use Business\Member\Member;
use Business\NewJavaApi\Distribution\DistributionProduct;
use Model\Member\MemberRelationship;

class Config extends ATBase
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 分页列表查询
     * <AUTHOR>
     * @date 2020/12/31
     *
     * @return json
     */
    public function queryConfigPage()
    {
        $params = $this->_params;

        $applyDid = $this->_sid; //供应商ID
        $page     = $params->page ? intval($params->page) : 1; //页码
        $size     = $params->size ? intval($params->size) : 15; //每页大小
        $lid      = $params->lid ? intval($params->lid) : null; //景区ID
        $tid      = $params->tid ? intval($params->tid) : null; //票ID
        $title    = $params->title ? trim($params->title) : null; //名称

        $dname   = $params->dname ? trim($params->dname) : ''; //名称
        $account = $params->account ? trim($params->account) : ''; //账户
        $cname   = $params->cname ? trim($params->cname) : ''; //联系人名称
        $mobile  = $params->mobile ? trim($params->mobile) : ''; //手机号

        $configBiz = new \Business\AgreementTicket\Config();
        $listRes   = $configBiz->queryConfigPage($applyDid, $page, $size, $lid, $tid, $title, $dname, $account, $mobile, $cname);

        $this->apiReturn($listRes['code'], $listRes['data'], $listRes['msg']);
    }

    /**
     * 新增配置
     * <AUTHOR>
     * @date 2020/12/31
     *
     * @return json
     */
    public function addConfig()
    {
        $params = $this->_params;

        $this->_verifyParams($params, ['lid', 'tid', 'title', 'fid', 'useType', 'store', 'isDistribution']);

        if (!is_numeric($params->store)) {
            $this->apiReturn(400, [], '库存参数有误');
        }

        $applyDid  = $this->_sid; //供应商ID
        $title     = $params->title; //名称
        $fid       = intval($params->fid); //分销商ID
        $lid       = intval($params->lid); //景区ID
        $tid       = intval($params->tid); //票ID
        $useType   = intval($params->useType); //启用类型 1=启用时间内有效 2=永久有效
        $store     = intval($params->store); //协议库存
        $operater  = $this->_memberId; //操作用户ID
        $startTime = $params->startTime ? strtotime($params->startTime) : null; //启用时间的开始时间
        $endTime   = $params->endTime ? strtotime($params->endTime) : null; //启用时间的结束时间
        $isDistribution = intval($params->isDistribution); //是否向下分销 1=否 2=是

        $configBiz = new \Business\AgreementTicket\Config();
        $addRes    = $configBiz->addConfig($applyDid, $title, $fid, $lid, $tid, $useType, $store, $operater, $isDistribution,
            $startTime, $endTime);

        $this->apiReturn($addRes['code'], $addRes['data'], $addRes['msg']);
    }

    /**
     * 批量新增配置数据
     * @return void
     */
    public function batchAddConfig()
    {
        $params = $this->_params;
        $this->_verifyParams($params, ['fid', 'configList']);
        $applyDid  = $this->_sid; //供应商ID
        $fid       = intval($params->fid); //分销商ID
        $operater  = $this->_memberId; //操作用户ID
        $configBiz = new \Business\AgreementTicket\Config();
        $addRes = $configBiz->batchAddConfig($applyDid, $fid, $params->configList, $operater);

        $this->apiReturn($addRes['code'], $addRes['data'], $addRes['msg']);
    }


    /**
     * 编辑配置
     * <AUTHOR>
     * @date 2020/12/31
     *
     * @return json
     */
    public function modConfig()
    {
        $params = $this->_params;

        $this->_verifyParams($params, ['id', 'title', 'useType', 'store', 'isDistribution']);

        if (!is_numeric($params->store)) {
            $this->apiReturn(400, [], '库存参数有误');
        }

        $id        = intval($params->id); //主键ID
        $applyDid  = $this->_sid; //供应商ID
        $title     = $params->title; //名称
        $useType   = intval($params->useType); //启用类型 1=启用时间内有效 2=永久有效
        $store     = intval($params->store); //协议库存
        $operater  = $this->_memberId; //操作用户ID
        $startTime = $params->startTime ? strtotime($params->startTime) : null; //启用时间的开始时间
        $endTime   = $params->endTime ? strtotime($params->endTime) : null; //启用时间的结束时间
        $isDistribution = intval($params->isDistribution); //是否向下分销 1=否 2=是

        $configBiz = new \Business\AgreementTicket\Config();
        $addRes    = $configBiz->modConfig($id, $applyDid, $title, $useType, $store, $operater, $isDistribution,
            $startTime, $endTime);

        $this->apiReturn($addRes['code'], $addRes['data'], $addRes['msg']);
    }

    /**
     * 获取编辑信息
     * <AUTHOR>
     * @date 2021/1/2
     *
     * @return json
     */
    public function queryConfigStorageInfo()
    {
        $params = $this->_params;

        $this->_verifyParams($params, ['configId']);

        $configId = intval($params->configId); //配置ID
        $applyDid = $this->_sid; //供应商ID

        $configBiz = new \Business\AgreementTicket\Config();
        $result    = $configBiz->queryConfigStorageInfo($configId, $applyDid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取购票记录
     * <AUTHOR>
     * @date 2021/1/2
     *
     * @return json
     */
    public function queryOrderRecord()
    {
        $params = $this->_params;

        self::_verifyParams($params, ['configId', 'page', 'size']);

        $configId = intval($params->configId); //配置ID
        $page     = intval($params->page); //页码
        $size     = intval($params->size); //每页大小
        $orderNum = !empty($params->orderNum) ? $params->orderNum : null; //订单号

        $configBiz = new \Business\AgreementTicket\Storage();
        $result    = $configBiz->queryOrderRecord($configId, $page, $size, $orderNum);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据启用时间获取库存数据
     * <AUTHOR>
     * @date 2021/1/13
     *
     * @return json
     */
    public function queryStorageByUseTime()
    {
        $params = $this->_params;

        self::_verifyParams($params, ['configId', 'useType']);

        $configId  = intval($params->configId); //主键ID
        $useType   = intval($params->useType); //启用类型 1=启用时间内有效 2=永久有效
        $startTime = $params->startTime ? strtotime($params->startTime) : null; //启用时间的开始时间
        $endTime   = $params->endTime ? strtotime($params->endTime) : null; //启用时间的结束时间
        $applyDid  = $this->_sid; //供应商ID

        $configBiz = new \Business\AgreementTicket\Storage();
        $result    = $configBiz->queryStorageByUseTime($applyDid, $configId, $useType, $startTime, $endTime);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取分销商
     * <AUTHOR>
     * @date 2021/1/4
     *
     * @return json
     */
    public function queryDistributor()
    {
        $params = $this->_params;
        self::_verifyParams($params, ['keyword']);

        $mobile = $account = 0;
        if (mb_strlen($params->keyword) == 11 && is_numeric($params->keyword)) {
            $mobile = $params->keyword;
        } else {
            $account = $params->keyword;
        }

        $relationShipModel = new MemberRelationship($this->_sid);
        $searchResult      = $relationShipModel->partnerRelationSearch($this->_sid, $mobile, '', $account);

        if (empty($searchResult)) {
            $this->apiReturn(200, [], '');
        }

        $userIdArr = array_column($searchResult, 'son_id');

        $memberBuz = new Member();
        $userInfo  = $memberBuz->getList($userIdArr);

        $result = [];
        foreach ($searchResult as $value) {
            $result[] = [
                'fid'     => $value['son_id'],
                'account' => $userInfo[$value['son_id']]['account'] ?? '',
                'dname'   => $userInfo[$value['son_id']]['dname'] ?? '',
                'mobile'  => $userInfo[$value['son_id']]['mobile'] ?? '',
            ];
        }

        $this->apiReturn(200, $result, '');

    }

    /**
     * 查询景区
     * <AUTHOR>
     * @date 2021/1/4
     *
     * @return json
     */
    public function queryLand()
    {
        $params = $this->_params;
        self::_verifyParams($params, ['keyword', 'fid', 'page', 'size']);
        $productTypeList = ['A', 'F'];

        $keyword = $params->keyword;
        $fid     = intval($params->fid);
        $sid     = $this->_sid;

        $queryLand = new DistributionProduct();
        $result = $queryLand->queryDistributionLand($sid, $fid, $keyword, '', $productTypeList, 1, 500);
        $returnData = [];
        if (isset($result['data']['list'])){
            $result['data'] = $result['data']['list'];
        }
        foreach ($result['data'] as $value){
            $returnData[] = [
                'lid' => $value['lid'],
                'sid' => $value['sid'],
                'name' => $value['landTitle'],
            ];
        }

        $this->apiReturn(200, ['list' => $returnData], '');
    }

    /**
     * 查询票
     * <AUTHOR>
     * @date 2021/1/4
     *
     * @return json
     */
    public function queryTicket()
    {
        $params = $this->_params;
        self::_verifyParams($params, ['fid', 'lid']);

        $keyword = '';
        $fid     = intval($params->fid);
        $sid     = $this->_sid;
        $lid     = intval($params->lid);

        $queryTicket = new DistributionProduct();
        $result = $queryTicket->queryDistributionTicket($lid, $sid, $fid, $keyword, '', 1, 500);
        if (isset($result['data']['list'])){
            $result['data'] = $result['data']['list'];
        }
        $returnData = [];
        foreach ($result['data'] as $value){
            $returnData[] = [
                'name' => $value['ticketTitle'],
                'lid' => $value['lid'],
                'ticketId' => $value['tid'],
            ];
        }

        $this->apiReturn(200, ['list' => $returnData], '');
    }

    /**
     * 查询有分销的票列表（带有tid是否已配置）
     * @return void
     */
    public function queryTicketPaging()
    {
        $params = $this->_params;
        self::_verifyParams($params, ['fid']);
        $fid             = intval($params->fid);
        $sid             = $this->_sid;
        $landTitle       = $params->landTitle ?: '';
        $ticketTitle     = $params->ticketTitle ?: '';
        $pageNum         = $params->pageNum;
        $pageSize        = $params->pageSize;
        $productTypeList = ['A', 'F'];

        $params   = [
            'sid'             => $sid,
            'fid'             => $fid,
            'pageNum'         => $pageNum,
            'pageSize'        => $pageSize,
            'productTypeList' => $productTypeList,
        ];
        if ($landTitle){
            $params['landTitle'] = $landTitle;
        }
        if ($ticketTitle){
            $params['ticketTitle'] = $ticketTitle;
        }
        $configBz = new \Business\AgreementTicket\Config();
        $result   = $configBz->queryTicketPaging($params);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 查询批量操作列表
     * @return void
     */
    public function batchOperateList()
    {
        $params = $this->_params;
        self::_verifyParams($params, ['pageNum', 'pageSize']);
        $applyDid  = $this->_sid; //供应商ID
        $pageNum   = $params->pageNum;
        $pageSize  = $params->pageSize;
        $state     = $params->state;
        $beginTime = $params->beginTime;
        $endTime   = $params->endTime;

        $configBiz = new \Business\AgreementTicket\Config();
        $result    = $configBiz->batchOperateList($applyDid, $pageNum, $pageSize, $state, $beginTime, $endTime);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 查询批量操作详情列表
     * @return void
     */
    public function batchOperateDetail()
    {
        $params = $this->_params;
        self::_verifyParams($params, ['batchId', 'pageNum', 'pageSize']);
        $batchId   = $params->batchId;
        $pageNum   = $params->pageNum;
        $pageSize  = $params->pageSize;
        $state     = $params->state;
        $lid       = $params->lid;
        $tid       = $params->tid;
        $configBiz = new \Business\AgreementTicket\Config();
        $result    = $configBiz->batchOperateDetail($batchId, $pageNum, $pageSize, $state, $lid, $tid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除配置数据
     * <AUTHOR>
     * @date 2021/02/01
     *
     * @return json
     */
    public function delConfig()
    {
        $params = $this->_params;

        $this->_verifyParams($params, ['id']);

        $id       = intval($params->id); //主键ID
        $applyDid = $this->_sid; //供应商ID
        $operater = $this->_memberId; //操作用户ID

        $configBiz = new \Business\AgreementTicket\Config();
        $addRes    = $configBiz->delConfig($id, $applyDid, $operater);

        $this->apiReturn($addRes['code'], $addRes['data'], $addRes['msg']);
    }

}
