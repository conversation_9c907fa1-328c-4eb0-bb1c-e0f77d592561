<?php

namespace Controller\JavaInterface;

use Library\Controller;
use Library\Tools\Helpers;

class InterfaceRelay extends Controller
{
    public function relay()
    {
        $params = json_decode(file_get_contents('php://input'), true);
        $method = I('get.method');
        $method = explode('_', $method);

        $configKey = $method[0];
        $module    = $method[1];
        $url       = $method[2];

        $result = Helpers::DistributionCenterCall($url, $params, $module, false, $configKey);

        Helpers::DistributionCenterResponse($result);
    }
}