<?php
/**
 * 导出接口通用类
 * <AUTHOR>
 * @date   2022/12/14
 */

namespace Controller\MassData;

use Library\Controller;
use Model\Product\Land;
use Model\MassData\ExcelTask as ExcelTaskModel;

class ExportBase extends Controller
{
    /**
     * 错误日志地址
     * @var string
     */
    protected $_errPath = "/excel/task/error";

    /**
     * 登录的主账号用户id
     * @var false|mixed
     */
    protected $_fid;

    /**
     * 登录的账号用户id
     * @var false|mixed
     */
    protected $_mid;

    /**
     * 登录的信息
     * @var array
     */
    protected $_loginInfo;

    /**
     * 请求参数
     * @var
     */
    protected $_request;

    /**
     * 报表类型
     * @var
     */
    protected $_judgeType = 0;

    const TASK_NOT_CHECK_EXPORT_EXCEL_ID   = 70;
    const TASK_NOT_CHECK_EXPORT_EXCEL_NAME = '未验证报表导出Excel';

    const TASK_NOT_CHECK_EXPORT_DETAIL_ID   = 72;
    const TASK_NOT_CHECK_EXPORT_DETAIL_NAME = '未验证报表导出明细';

    //台账报表
    const TASK_STANDING_BOOK_EXPORT_SALE     = 77;
    const TASK_STANDING_BOOK_EXPORT_CHECK    = 78;
    const TASK_STANDING_BOOK_EXPORT_CANCEL   = 79;
    const TASK_STANDING_BOOK_EXPORT_REVOKE   = 80;
    const TASK_STANDING_BOOK_EXPORT_OVERALL  = 81;
    const TASK_STANDING_BOOK_EXPORT_NOTCHECK = 82;
    const TASK_SEPARATE_RULE_EXPORT_TEMPLATE = 86;
    const TASK_SEPARATE_REPORT_EXPORT = 87;

    //瘦西湖定制报表-五分钟销售报表
    const TASK_FIVE_MINUTE_ORDER_EXPORT = 88;//预订导出Excel
    const TASK_FIVE_MINUTE_ORDER_DETAIL = 89;//预订导出明细
    const TASK_FIVE_MINUTE_CHECK_EXPORT = 90;//验证导出Excel
    const TASK_FIVE_MINUTE_CHECK_DETAIL = 91;//验证导出明细

    //销售分账报表
    const CUSTOM_SAR_REPORT_EXPORT_ORDER = 93;
    const CUSTOM_SAR_REPORT_EXPORT_CHECK = 94;
    const CUSTOM_SAR_REPORT_DETAILS_EXPORT_ORDER = 95;
    const CUSTOM_SAR_REPORT_DETAILS_EXPORT_CHECK = 96;

    //预约报表
    const RESERVE_REPORT_DAILY_EXCEL_EXPORT_ORDER   = 103;//日导出excel
    const RESERVE_REPORT_DAILY_DETAILS_EXPORT_ORDER = 104;//日导出明细
    const RESERVE_REPORT_MONTH_EXCEL_EXPORT_ORDER   = 105;//月导出excel
    const RESERVE_REPORT_MONTH_DETAILS_EXPORT_ORDER = 106;//月导出明细
    const CUSTOM_SAR_SUB_PACK_REPORT_EXPORT_CHECK = 107;
    const CUSTOM_SAR_SUB_PACK_REPORT_DETAILS_EXPORT_CHECK = 108;

    //演出预约报表
    const SHOW_RESERVE_REPORT_DAILY_EXCEL_EXPORT_ORDER   = 113;//日导出excel
    const SHOW_RESERVE_REPORT_DAILY_DETAILS_EXPORT_ORDER = 114;//日导出明细
    const SHOW_RESERVE_REPORT_MONTH_EXCEL_EXPORT_ORDER   = 115;//月导出excel
    const SHOW_RESERVE_REPORT_MONTH_DETAILS_EXPORT_ORDER = 116;//月导出明细



    /**
     * 允许走单独接口的报表类型，为空则是全部
     * 注明：
     * 73:旅游券预订报表导出excel
     * 74:旅游券预订报表导出明细
     * 75:旅游券检券报表导出excel
     * 76:旅游券检券报表导出明细
     * @deprecated
     */
    const ALLOW_TYPE = [
        73,
        74,
        75,
        76,
        self::TASK_NOT_CHECK_EXPORT_EXCEL_ID,
        self::TASK_NOT_CHECK_EXPORT_DETAIL_ID,
        self::TASK_STANDING_BOOK_EXPORT_SALE,
        self::TASK_STANDING_BOOK_EXPORT_CHECK,
        self::TASK_STANDING_BOOK_EXPORT_CANCEL,
        self::TASK_STANDING_BOOK_EXPORT_REVOKE,
        self::TASK_STANDING_BOOK_EXPORT_OVERALL,
        self::TASK_STANDING_BOOK_EXPORT_NOTCHECK,
        self::TASK_SEPARATE_RULE_EXPORT_TEMPLATE,
        self::TASK_SEPARATE_REPORT_EXPORT,
        self::TASK_FIVE_MINUTE_ORDER_EXPORT,
        self::TASK_FIVE_MINUTE_ORDER_DETAIL,
        self::TASK_FIVE_MINUTE_CHECK_EXPORT,
        self::TASK_FIVE_MINUTE_CHECK_DETAIL,
        self::CUSTOM_SAR_REPORT_EXPORT_ORDER,
        self::CUSTOM_SAR_REPORT_EXPORT_CHECK,
        self::CUSTOM_SAR_REPORT_DETAILS_EXPORT_ORDER,
        self::CUSTOM_SAR_REPORT_DETAILS_EXPORT_CHECK,
        self::RESERVE_REPORT_DAILY_EXCEL_EXPORT_ORDER,
        self::RESERVE_REPORT_DAILY_DETAILS_EXPORT_ORDER,
        self::RESERVE_REPORT_MONTH_EXCEL_EXPORT_ORDER,
        self::RESERVE_REPORT_MONTH_DETAILS_EXPORT_ORDER,
        self::CUSTOM_SAR_SUB_PACK_REPORT_EXPORT_CHECK,
        self::CUSTOM_SAR_SUB_PACK_REPORT_DETAILS_EXPORT_CHECK,
        self::SHOW_RESERVE_REPORT_DAILY_EXCEL_EXPORT_ORDER,
        self::SHOW_RESERVE_REPORT_DAILY_DETAILS_EXPORT_ORDER,
        self::SHOW_RESERVE_REPORT_MONTH_EXCEL_EXPORT_ORDER,
        self::SHOW_RESERVE_REPORT_MONTH_DETAILS_EXPORT_ORDER,
    ];

    /**
     * 判断是否登陆 获取登录人ID
     */
    public function __construct()
    {
        parent::__construct();

        $loginInfo = $this->getLoginInfo('ajax');

        //获取登录账号和主账号 用户id
        $this->_fid = $loginInfo['sid'];
        $this->_mid = $loginInfo['memberID'];

        //登录信息
        $this->_loginInfo = $loginInfo;

        //请求参数
        $this->_request = I('param.');

        //假日模式检测
        $this->_checkVocationMode();

        //初始化验证
        //$this->_exportInit();
    }

    /**
     * 检测当前是否处理假日模式
     * <AUTHOR>
     * @date   2022/12/13
     *
     */
    private function _checkVocationMode()
    {
        $vocationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('Judge',
            $this->_loginInfo['saccount']);
        if ($vocationMode === false) {
            exit('{"code":401,"data":[],"msg":"当前处于假日模式，该功能被限制使用"}');
        }
    }

    /**
     * 导出初始化
     * <AUTHOR>
     * @date   2022/12/13
     *
     */
    protected function _exportInitNew()
    {
        //资源账号处理
        $this->_checkResourcesAccountNumber();

    }
    /**
     * 导出初始化
     * <AUTHOR>
     * @date   2022/12/13
     *
     */
    protected function _exportInit()
    {
        //资源账号处理
        $this->_checkResourcesAccountNumber();

        //报表类型请求参数
        $judgeType = $this->_judgeType ?: $this->_request['judgeType'];
        if (empty($judgeType)) {
            $this->apiReturn(203, [], '报表类型不能为空');
        }

        //接口报表类型限制
        // if (!empty(self::ALLOW_TYPE) && !in_array($judgeType, self::ALLOW_TYPE)) {
        //     $this->apiReturn(203, [], '该报表类型不支持新接口导出');
        // }

        $excelType = load_config('excelType', 'excel');

        //验证报表类型
        if (empty($excelType[$judgeType])) {
            $this->apiReturn(203, [], '未知报表类型');
        }

        //验证导出脚本是否存在
        $class  = $excelType[$judgeType]['class'];
        $action = $excelType[$judgeType]['preAction'];
        $limit  = $excelType[$judgeType]['limit'];

        if (!class_exists($class) || !method_exists($class, $action)) {
            $this->apiReturn(400, [], "系统错误, {$class}_{$action}");
        }

        //导出前置限制验证
        $run = new $class();
        $res = $run->$action($limit, $this->_loginInfo['saccount']);
        if ($res['code'] != 200) {
            $this->apiReturn(400, [], $res['msg'] ?? '前置校验失败');
        }

        $excelTask = new ExcelTaskModel();

        $request = $this->_request;

        //哈希值
        $hashVal = $this->_getHashVal($excelType[$judgeType] ?? []);

        //如果有重复的哈希值 不重新执行任务
        $res = $excelTask->getTaskByHash($hashVal);

        //hash查询后判断
        if (!empty($res) && !empty($excelType[$judgeType]['hashAfter'])) {
            $hashAfter       = $excelType[$judgeType]['hashAfter'];
            $hashAfter       = is_array($hashAfter) ? $hashAfter : [];
            $createTime      = date("Y-m-d", $res['create_time']);
            $checkPreDate    = $hashAfter['checkPreDate'] ?? '';
            $checkPreDateVal = empty($checkPreDate) ? '' : ($request[$checkPreDate] ?? '');
            if (!empty($checkPreDateVal)) {
                $checkPreDateVal = date("Y-m-d", strtotime($checkPreDateVal));
                //规则：查询结束时间和已有创建时间 是同一天的话，允许重新生成
                if ($checkPreDateVal == $createTime) {
                    unset($res);
                }
            }
        }

        //请求参数带上其他字段
        $this->_request['hash_res']  = $res ?? [];
        $this->_request['hash_val']  = $hashVal;
        $this->_request['member_id'] = $this->_mid;
    }

    /**
     * 验证资源账号
     * <AUTHOR>
     * @date   2022/12/13
     *
     */
    private function _checkResourcesAccountNumber()
    {
        $dtype   = $this->_loginInfo['dtype'];
        $account = $this->_loginInfo['account'];

        if ($dtype == 2 || $dtype == 3) {
            $landModel = new Land();
            $tmp       = $landModel->getListByResource($account, $dtype);
            //如果都没有景区的话，直接返回空
            if (empty($tmp[0]['id'])) {
                $this->apiReturn(200, [], '找不到景区');
            }

            $this->_request['land_id'] = $tmp[0]['id'];
            $this->_request['is_land'] = 1;
        }
    }

    /**
     * 获取hash值
     * <AUTHOR>
     * @date   2022/12/13
     *
     * @param  array  $config    导出excel配置信息
     *
     * @return string
     */
    private function _getHashVal(array $config): string
    {
        $request = $this->_request;

        $judgeType = $this->_judgeType ?: $this->_request['judgeType'];

        //哈希值
        if (!empty($config['dateDistin'])) {
            $time = time();
            $hash = md5($this->_fid . "_" . json_encode($request) . "_" . $time);

            if (!empty($config['noLimitToday']) && !empty($config['noLimitToday'])) {
                // 如果小于今天凌晨就不用一直重新生成
                $todayStamp = strtotime(date("Y-m-d"));
                if (strtotime($request[$config['noLimitToday']]) < $todayStamp) {
                    $hash = md5($this->_fid . "_" . json_encode($request));
                    if ($judgeType == 23) {
                        //交易记录导出数据，非当日需要限制到小时
                        $hash = md5($this->_fid . "_" . json_encode($request) . '_' . date("YmdH"));
                    }
                }
            }
        } else {
            $hash = md5($this->_fid . "_" . json_encode($request));
        }

        return $hash;
    }

    /**
     * 验证是否已存在导出任务
     * <AUTHOR>
     * @date   2022/12/13
     *
     */
    protected function _checkHashRes()
    {
        if (!empty($this->_request['hash_res'])) {
            $data = $this->_request['hash_res']['id'] ?? 0;
            $msg  = "你已经生成过该报表, 根据唯一标识符进入报表中心查看";

            $this->apiReturn(200, $data, $msg);
        }

        //移除参数
        unset($this->_request['hash_res']);
    }

    /**
     * 创建异步导出任务
     * <AUTHOR>
     * @date   2022/12/13
     *
     */
    protected function _createTask()
    {
        $code = 200;
        $msg  = '成功';
        $data = 0;
        try {
            $request   = $this->_request;
            $judgeType = $request['judgeType'] ?? 0;
            $hash      = $request['hash_val'];

            //移除参数
            unset($request['hash_val']);

            if (empty($request) || !is_array($request) || empty($this->_fid) || empty($judgeType) || !is_numeric($judgeType)) {
                throw new \Exception('异常情况, 请重试', 203);
            }

            $excelTaskModel = new ExcelTaskModel();
            $excelTaskModel->setTaskOperatorId($this->_mid);
            $data = $excelTaskModel->createTask($this->_fid, $request, $hash, $judgeType);
            if (!$data) {
                throw new \Exception('新增异步任务失败', 400);
            }
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }
}
