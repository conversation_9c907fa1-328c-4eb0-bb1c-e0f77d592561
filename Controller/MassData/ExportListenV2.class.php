<?php
/**
 * 走新版下载中心
 * 需要前端提交templateCode参数
 * 具体导出逻辑参考pft001.excel_auth_config表的配置
 */

namespace Controller\MassData;

use Business\Dc\DcExcelTaskBase;
use JsonRpc\Export\CooDistributionExport;
use JsonRpc\Export\LeaseExport;
use Library\Container;
use Library\Controller;
use Task\TeamOrderCheckReportExport;
use Task\TeamOrderSaleReportExport;

class ExportListenV2 extends Controller
{
    private $loginInfo;
    //[申请的模板templateCode]和[获取自定义的动态表头方法名]的映射
    private $templateCodeCustomStructureMap = [
        'TeamOrderSaleReport' => 'getSelfDefinedStructureForTeamSale',//导出团队销售报表
        'TeamOrderCheckReport' => 'getSelfDefinedStructureForTeamCheck',//导出团队验证报表
        'LeaseChangeLog' => '',//租赁订单变更记录导出
        'itemRentReport' => 'getSelfDefinedStructureForRent',//导出物品租赁报表
        'itemRentReportDetail' => '',//导出物品租赁报表明细
        'exchangeCodeBatchList' => '',//导出预发码批次列表
        'exchangeCodeBatchDetail' => '',//导出预发码明细
        'service_coo_distributor_list' => 'getSelfDefinedStructureForCooDistributorList', //导出合作分销商报表
    ];

    /**
     * 判断是否登陆 获取登录人ID
     */
    public function __construct()
    {
        $this->loginInfo = $this->getLoginInfo('ajax', false, false);
    }

    public function getSelfDefinedStructureForTeamSale($searchConfigId): array
    {
        return (new TeamOrderSaleReportExport())->getSelfDefinedStructureForTeamSale($searchConfigId);
    }

    public function getSelfDefinedStructureForTeamCheck($searchConfigId): array
    {
        return (new TeamOrderCheckReportExport())->getSelfDefinedStructureForTeamCheck($searchConfigId);
    }
    public function getSelfDefinedStructureForRent($searchConfigId): array
    {
        return (new LeaseExport())->getSelfDefinedStructureForRent($searchConfigId);
    }
    public function getSelfDefinedStructureForCooDistributorList($searchConfigId)
    {
        return Container::pull(CooDistributionExport::class)->getSelfDefinedStructure($this->loginInfo['sid']);
    }

    public function Judge()
    {
        // 假日模式，判断是否能够导出
        $vocationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('Judge', $this->loginInfo['saccount']);
        if ($vocationMode === false) {
            exit(json_encode(['code' => 401, 'data' => [], 'msg' => '当前处于假日模式，该功能被限制使用']));
        }
        //初始化返回信息
        $code = 3;
        $data = '';
        $msg = '不需要记录任务';
        try {
            $request = I('param.');
            if (empty($request['templateCode'])) {
                throw new \Exception('templateCode缺失');
            }
            // 请求新版下载接口
            //如果配置需要自定义表头$selfDefinedStructure 则通过配置的方法获取自定义表头
            $customStructure = [];
            //获取自定义表头方法名 如果比较多可以考虑重构获取对象
            $action = $this->templateCodeCustomStructureMap[$request['templateCode']] ?? '';
            //http://gitlab.12301.test/pft-doc/app-doc/-/blob/master/%E6%96%B0%E4%B8%8B%E8%BD%BD%E4%B8%AD%E5%BF%83/03%E5%8F%91%E8%B5%B7%E5%AF%BC%E5%87%BA%E4%BB%BB%E5%8A%A1.md
            if (!empty($action)) {
                $customStructure = $this->$action($request['search_config_id']);
            }
            $this->_createTaskNew($this->loginInfo['sid'], $this->loginInfo['memberID'], $request['templateCode'], $request, $customStructure);
        } catch (\Exception $e) {
            $code = 2;
            $msg = $e->getMessage();
        }
        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 创建任务-新下载中心
     */
    private function _createTaskNew($fid, $opId, $templateCode, $request, $customStructure = [])
    {
        $dcExcelTaskBase = new DcExcelTaskBase();
        $result = $dcExcelTaskBase->addTask($fid, $opId, $templateCode, $request, 0, '', '', $customStructure);
        if ($result['code'] == '10002') {
            return $this->apiReturn(2, null, '导出模板不存在，请检查是否配置');
        }
        $data = $result['data'] ?: [];
        $msg = $result['msg'];
        $code = $result['code'];
        if ($code == '200') {
            if ($msg == null) {
                return $this->apiReturn(1, $data['taskId'], '记录任务');
            }
            return $this->apiReturn(4, $data['taskId'], $msg);
        }
        return $this->apiReturn(2, $data['taskId'], $result['msg']);
    }
}