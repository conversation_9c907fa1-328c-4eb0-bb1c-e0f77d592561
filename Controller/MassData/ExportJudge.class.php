<?php
/**
 * 下载中心，异步导出接口
 * <AUTHOR>
 * @date   2022/12/13
 */

namespace Controller\MassData;

use \Business\Dc\DcExcelTaskBase;
use \Business\Statistics\StandingBook as StandingBookBiz;
use \Task\StandingBookExport;

class ExportJudge extends ExportBase
{
    /**
     * 判断是否登陆 获取登录人ID
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 未验证报表导出Excel
     * <AUTHOR>
     * @date   2023/1/6
     *
     */
    public function notCheckedReportExcelExport()
    {
        //当前处理的报表类型
        $this->_judgeType = self::TASK_NOT_CHECK_EXPORT_EXCEL_ID;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }
    /**
     * 旅游券预定报表导出excel
     * <AUTHOR>
     * @date   2022/12/13
     */
    public function payReportListExport()
    {
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 旅游券预定报表列表导出明细
     * <AUTHOR>
     * @date   2022/12/13
     */
    public function payOrderListExport()
    {
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 未验证报表导出明细
     * <AUTHOR>
     * @date   2023/1/6
     *
     */
    public function notCheckedReportDetailExport()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::TASK_NOT_CHECK_EXPORT_DETAIL_ID;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 旅游券检券报表列表导出excel
     * <AUTHOR>
     * @date   2022/12/13
     */
    public function verifyReportListExport()
    {
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();

        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 旅游券检券报表列表导出明细
     * <AUTHOR>
     * @date   2022/12/13
     */
    public function verifyOrderListExport()
    {
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();

        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 台账预售表导出明细
     * <AUTHOR>
     * @date   2023/3/8
     *
     */
    public function standingBookExportSale()
    {
        $this->_request['judgeType'] = self::TASK_STANDING_BOOK_EXPORT_SALE;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();

        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 台账预售表导出明细
     * <AUTHOR>
     * @date   2023/3/8
     *
     */
    public function standingBookExportCheck()
    {
        $this->_request['judgeType'] = self::TASK_STANDING_BOOK_EXPORT_CHECK;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();

        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 台账预售表导出明细
     * <AUTHOR>
     * @date   2023/3/8
     *
     */
    public function standingBookExportCancelOld()
    {
        $this->_request['judgeType'] = self::TASK_STANDING_BOOK_EXPORT_CANCEL;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();

        //创建异步下载任务
        $this->_createTask();
    }


    /**
     * 台账预售表导出明细-创建导出任务
     */
    public function standingBookExportCancel()
    {
        //初始化
        $this->_exportInitNew();

        //创建异步下载任务
        $startDt         = I("post.begin", '', 'strval'); //开始时间
        $endDt           = I("post.end", '', 'strval'); //结束时间
        $searchConfigId  = I("post.search_config_id", '0', 'intval'); //模板id
        $request         = [
            'begin'            => $startDt,
            'end'              => $endDt,
            'search_config_id' => $searchConfigId
        ];
        $dcExcelTaskBase = new DcExcelTaskBase();
        $templateCode    = $dcExcelTaskBase::STANDING_BOOK_CANCEL_DETAIL;
        $result          = $dcExcelTaskBase->addTask($this->_loginInfo['sid'], $this->_loginInfo['memberID'], $templateCode, $request);
        $data            = $result['data'] ?: [];
        return $this->apiReturn($result['code'], $data['taskId'], $result['msg']);
    }

    /**
     * 台账预售表导出明细
     * <AUTHOR>
     * @date   2023/3/8
     *
     */
    public function standingBookExportRevoke()
    {
        $this->_request['judgeType'] = self::TASK_STANDING_BOOK_EXPORT_REVOKE;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();

        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 台账汇总表导出明细
     * <AUTHOR>
     * @date   2023/1/6
     *
     */
    public function standingBookExportOverall()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::TASK_STANDING_BOOK_EXPORT_OVERALL;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 台账未验证报表导出明细
     * <AUTHOR>
     * @date   2023/1/6
     *
     */
    public function standingBookExportNotCheck()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::TASK_STANDING_BOOK_EXPORT_NOTCHECK;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 分账规则导出
     * @return void
     */
    public function separateRuleExportTemplate()
    {
        if (empty($this->_request['template_id'])) {
            $this->apiReturn(400, [], '参数错误');
        }
        //当前处理的报表类型
        $this->_request['judgeType'] = self::TASK_SEPARATE_RULE_EXPORT_TEMPLATE;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 分账报表导出
     * @return void
     */
    public function separateReportExport()
    {
        if (empty($this->_request['begin']) || empty($this->_request['end']) || empty($this->_request['tpl_id'] ||
                empty($this->_request['summary_date_type']))) {
            $this->apiReturn(400, [], '参数错误');
        }
        //当前处理的报表类型
        $this->_request['judgeType'] = self::TASK_SEPARATE_REPORT_EXPORT;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 预订报表（分钟）导出Excel
     * <AUTHOR>
     * @date   2023/10/16
     *
     */
    public function minuteOrderReportExportExcel()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::TASK_FIVE_MINUTE_ORDER_EXPORT;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 预订报表（分钟）导出明细
     * <AUTHOR>
     * @date   2023/10/16
     *
     */
    public function minuteOrderReportExportDetail()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::TASK_FIVE_MINUTE_ORDER_DETAIL;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 验证报表（分钟）导出明细
     * <AUTHOR>
     * @date   2023/10/16
     *
     */
    public function minuteCheckReportExportExcel()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::TASK_FIVE_MINUTE_CHECK_EXPORT;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 验证报表（分钟）导出明细
     * <AUTHOR>
     * @date   2023/10/16
     *
     */
    public function minuteCheckReportExportDetail()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::TASK_FIVE_MINUTE_CHECK_DETAIL;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 销售分账报表预订导出Excel
     * @return void
     */
    public function customSarReportExportOrder()
    {
        if (empty($this->_request['begin']) || empty($this->_request['end']) || empty($this->_request['tpl_id'])) {
            $this->apiReturn(400, [], '参数错误');
        }
        //当前处理的报表类型
        $this->_request['judgeType'] = self::CUSTOM_SAR_REPORT_EXPORT_ORDER;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 销售分账报表验证导出Excel
     * @return void
     */
    public function customSarReportExportCheck()
    {
        if (empty($this->_request['begin']) || empty($this->_request['end']) || empty($this->_request['tpl_id'])) {
            $this->apiReturn(400, [], '参数错误');
        }
        //当前处理的报表类型
        $this->_request['judgeType'] = self::CUSTOM_SAR_REPORT_EXPORT_CHECK;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 销售分账报表预订导出明细
     * @return void
     */
    public function customSarReportDetailsExportOrder()
    {
        if (empty($this->_request['begin']) || empty($this->_request['end']) || empty($this->_request['tpl_id'])) {
            $this->apiReturn(400, [], '参数错误');
        }
        //当前处理的报表类型
        $this->_request['judgeType'] = self::CUSTOM_SAR_REPORT_DETAILS_EXPORT_ORDER;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 销售分账报表验证导出明细
     * @return void
     */
    public function customSarReportDetailsExportCheck()
    {
        if (empty($this->_request['begin']) || empty($this->_request['end']) || empty($this->_request['tpl_id'])) {
            $this->apiReturn(400, [], '参数错误');
        }
        //当前处理的报表类型
        $this->_request['judgeType'] = self::CUSTOM_SAR_REPORT_DETAILS_EXPORT_CHECK;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 销售分账报表预订导出明细
     * @return void
     */
    public function customSarReportSubPackDetails()
    {
        if (empty($this->_request['begin']) || empty($this->_request['end']) || empty($this->_request['search_config_id']) ||
            empty($this->_request['date_type'])) {
            $this->apiReturn(400, [], '参数错误');
        }
        //当前处理的报表类型
        $this->_request['judgeType'] = self::CUSTOM_SAR_SUB_PACK_REPORT_DETAILS_EXPORT_CHECK;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 销售分账报表验证导出Excel
     * @return void
     */
    public function customSarReportSubPackCheck()
    {
        if (empty($this->_request['begin']) || empty($this->_request['end']) || empty($this->_request['search_config_id']) ||
            empty($this->_request['date_type'])) {
            $this->apiReturn(400, [], '参数错误');
        }
        //当前处理的报表类型
        $this->_request['judgeType'] = self::CUSTOM_SAR_SUB_PACK_REPORT_EXPORT_CHECK;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 预约日报表导出Excel
     * <AUTHOR>
     * @date   2023/11/1
     *
     */
    public function reserveReportDailyExportExcel()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::RESERVE_REPORT_DAILY_EXCEL_EXPORT_ORDER;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 预约日报表导出Excel
     * <AUTHOR>
     * @date   2023/11/1
     *
     */
    public function reserveReportMonthExportExcel()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::RESERVE_REPORT_MONTH_EXCEL_EXPORT_ORDER;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 预约日报表导出明细
     * <AUTHOR>
     * @date   2023/11/1
     *
     */
    public function reserveReportDailyExportDetail()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::RESERVE_REPORT_DAILY_DETAILS_EXPORT_ORDER;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 预约月报表导出明细
     * <AUTHOR>
     * @date   2023/11/1
     *
     */
    public function reserveReportMonthExportDetail()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::RESERVE_REPORT_MONTH_DETAILS_EXPORT_ORDER;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }


    /**
     * 演出预约日报表导出Excel
     *
     */
    public function showReserveReportDailyExportExcel()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::SHOW_RESERVE_REPORT_DAILY_EXCEL_EXPORT_ORDER;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 演出预约月报表导出Excel
     *
     */
    public function showReserveReportMonthExportExcel()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::SHOW_RESERVE_REPORT_MONTH_EXCEL_EXPORT_ORDER;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 演出预约日报表导出明细
     *
     */
    public function showReserveReportDailyExportDetail()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::SHOW_RESERVE_REPORT_DAILY_DETAILS_EXPORT_ORDER;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 演出预约月报表导出明细
     *
     */
    public function showReserveReportMonthExportDetail()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = self::SHOW_RESERVE_REPORT_MONTH_DETAILS_EXPORT_ORDER;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 导出可用金额
     * <AUTHOR>
     * @date   2024/1/23
     *
     */
    public function exportAvailableBalance()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = 110;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 导出冻结金额
     * <AUTHOR>
     * @date   2024/1/23
     *
     */
    public function exportFrozenBalance()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = 111;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }

    /**
     * 导出历史冻结明细
     * <AUTHOR>
     * @date   2024/1/23
     *
     */
    public function exportHistoryFrozenRecord()
    {
        //当前处理的报表类型
        $this->_request['judgeType'] = 112;
        //初始化
        $this->_exportInit();
        //验证hash
        $this->_checkHashRes();
        //创建异步下载任务
        $this->_createTask();
    }
}
