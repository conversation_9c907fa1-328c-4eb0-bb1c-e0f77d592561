<?php
/**
 * 导出明细自定义字段
 * <AUTHOR>
 * @date   2023/11/28
 */

namespace Controller\MassData;

use Library\Controller;
use Business\DownloadCenter\ExportDetail as ExportDetailBiz;

class ExportDetail extends Controller
{

    public function __construct()
    {
        $this->getLoginInfo('ajax');
    }

    /**
     * 获取导出字段
     * <AUTHOR>
     * @date   2023/11/28
     *
     */
    public function getField()
    {
        $tag = I('post.tag', '', 'strval');
        if (empty($tag)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $res = (new ExportDetailBiz)->getExportField($tag);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取导出字段配置
     * <AUTHOR>
     * @date   2023/11/28
     *
     */
    public function getConfig()
    {
        $tag = I('post.tag', '', 'strval');
        if (empty($tag)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $res = (new ExportDetailBiz)->getUserConfig($tag);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 导出字段配置
     * <AUTHOR>
     * @date   2023/11/28
     *
     */
    public function setConfig()
    {
        $data = I('post.data/a');
        $tag  = I('post.tag', '', 'strval');
        if (empty($data)) {
            $this->apiReturn(203, [], '参数错误');
        }
        if (empty($tag)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $res = (new ExportDetailBiz)->setUserConfig($tag, $data);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}