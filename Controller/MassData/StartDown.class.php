<?php
/**
 * excel文件下载页
 * <AUTHOR>
 * @date   2016-12-26
 */
namespace Controller\MassData;
use Library\Business\PFTcrypt;
use Library\Constants\MemberConst;
use Library\Controller;
use Model\MassData;

class StartDown extends Controller {

    private $_memberId;
    private $_loginInfo;
    private $_secretKey = 'pft12301';
    //暂时限制 只有 验证 取消 预定 撤销报表 完结 应收应付
    private $_limit = ['4','5','6','7','8','9'];

    public function __construct() {
        $this->isLogin('ajax');
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
        $this->_memberId = $this->_loginInfo['sid'];
    }

    /**
     * 下载任务
     * <AUTHOR>
     * @date   2016-12-27
     *
     * @params int  $id  任务ID
     */
    public function index($id) {
        $code = $msg = 200;

        try {
            $id            = urldecode($id);
            $xcrypt        = new PFTcrypt($this->_secretKey);
            $id            = $xcrypt->decrypt($id);

            $massDataModel = new MassData\ExcelTask();
            $data = $massDataModel->getTaskById($id);
            if (empty($data)) {
                throw new \Exception("未找到该任务");
            }

            if ($data['done'] != 3) {
                throw new \Exception("未生成, 请耐心等待");
            }

            if ($data['fid'] != $this->_memberId) {
                throw new \Exception("无权限");
            }

            //更新下载次数
            $massDataModel->updateDownload($id);

            $excelType = load_config('excelType', 'excel');
            $filepath  = $data['id'] . '.zip';
            if ($data['memo']  && in_array($data['excel_type'], $this->_limit)) {
                $filename  = $data['memo'] . '.zip';
            }else{
                $filename  = $excelType[$data['excel_type']]['desc'] . '.zip';
            }

            header("Content-Disposition: attachment; filename= {$filename}");
            header("Content-Type: application/octet-stream");
            header('X-Accel-Redirect: /Download/' . $filepath);
            header("X-Accel-Buffering: yes");
            //header("X-Accel-Limit-Rate :102400"); //速度限制 Byte/s  直接注释下  这个限制转NG处理
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, '', $msg);
    }

    /**
     * 获取下载列表页
     * <AUTHOR>
     * @date   2016-12-27
     *
     */
    public function getListOld() {

        $code = 200;
        $res  = [];
        $msg  = '';

        try {
            $page          = I('post.page', 1, 'intval');
            $pageSize      = I('post.pageSize', 15, 'intval');
            $excelType     = I('post.excel_type', 0, 'intval');
            $isFilter      = I('post.is_filter', 0, 'intval');
            $massDataModel = new MassData\ExcelTask();

            $data          = $massDataModel->getTaskListByUser($this->_memberId, $page, $pageSize, $excelType, $operatorId);

            $xcrypt        = new PFTcrypt($this->_secretKey);

            if (empty($data['list'])) {
                throw new \Exception("没有数据");
            }
            $excelType = load_config('excelType', 'excel');

            //获取商户信息
            $memberIdArr = array_column($data['list'] ?? [], 'member_id');
            $memberBiz   = new \Business\Member\Member();
            $memberMap   = $memberBiz->getMemberInfoByMulti($memberIdArr, 'id', true);

            foreach ($data['list'] as $key => $item) {
                $secretId                  = $xcrypt->encrypt($item['id']);
                $list[$key]['id']          = urlencode($secretId);
                $list[$key]['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
                $list[$key]['download']    = $item['download'];
                $list[$key]['done']        = $item['done'];
                if ($item['memo'] && in_array($item['excel_type'], $this->_limit)) {
                    $list[$key]['excel_type']  = $item['memo'];
                }else{
                    $list[$key]['excel_type']  = $excelType[$item['excel_type']]['desc'];
                }
                $list[$key]['hash']        = $item['id'];

                //操作人处理
                $opAccount = $memberMap[$item['member_id']]['account'] ?? '';
                $list[$key]['op_name'] = $memberMap[$item['member_id']]['dname'] ?? '';
                if (!empty($list[$key]['op_name']) && !empty($opAccount)) {
                    $list[$key]['op_name']  .= "({$opAccount})";
                }
            }
            $totalNum  = $data['total'];
            $totalPage = ceil($totalNum/$pageSize);
            $res       = ['list' => $list, 'totalPage' => $totalPage];
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $res, $msg);
    }

    /**
     * 获取下载列表页
     * <AUTHOR>
     * @date 2023/11/29
     */
    public function getList()
    {
        $isFilter      = I('post.is_filter', 0, 'intval');

        if ($this->_loginInfo['dtype'] == MemberConst::ROLE_STAFF) {
            // 员工只显示自己的
            $operatorId = $this->_loginInfo['memberID'];
        } else {
            if ($isFilter) {
                // 过滤的话，主账号只显示主账号自己的
                $operatorId = $this->_loginInfo['memberID'];
            } else {
                // 主账号默认不过滤
                $operatorId = 0;
            }
        }

        $dcBase4 = new \Business\Dc\DcExcelTaskBase();
        $params  = [
            'memberId'   => $this->_memberId,
            'page'       => I('post.page', 1, 'intval'),
            'pageSize'   => I('post.pageSize', 15, 'intval'),
            'isFilter'   => I('post.is_filter', 0, 'intval'),
            'operatorId' => $operatorId,
            'schema'     => is_ssl() ? 'https' : 'http',
            'host'       => $_SERVER['HTTP_HOST'],
            'url'        => getUrl()
        ];
        $externalCodeCall = $dcBase4->ExternalCodeCall('queryPage', $params);
        return $this->apiReturn($externalCodeCall['code'], $externalCodeCall['data'], $externalCodeCall['msg']);
    }

    /**
     * 更新异步下载任务状态
     * <AUTHOR>
     * @date 2022/2/11
     *
     * @throws
     */
    public function updateInfoOld()
    {
        $code = 200;
        $msg  = '';
        $data = [];

        try {
            $id = I('post.id', '', 'strval');
            if (empty($id)) {
                throw new \Exception("参数缺失");
            }
            $id     = urldecode($id);
            $xcrypt = new PFTcrypt($this->_secretKey);
            $taskId = $xcrypt->decrypt($id);

            $massDataModel = new MassData\ExcelTask();
            $taskInfo      = $massDataModel->getTaskById($taskId);
            if (empty($taskInfo)) {
                throw new \Exception("未找到该任务");
            }
            if ($taskInfo['done'] == 1) {
                throw new \Exception("当前状态待执行，无需操作");
            }
            if ($taskInfo['done'] != 4) {
                throw new \Exception("状态不符，重新生成失败");
            }
            $result = $massDataModel->updateReset($taskId, $this->_memberId);
            if (!$result) {
                throw new \Exception("重新生成失败，请重试");
            }
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 更新异步下载任务状态
     * <AUTHOR>
     * @date 2023/11/29
     */
    public function updateInfo()
    {
        $taskId = I('post.id', '', 'strval');

        $dcExcelTaskBase = new \Business\Dc\DcExcelTaskBase();

        $res = $dcExcelTaskBase->resetExcelTaskForFailure($taskId, $this->_memberId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}