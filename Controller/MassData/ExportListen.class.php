<?php
/**
 * 如果超过各自规定的阈值 则进入后台数据中心 下载该文档
 *
 * <AUTHOR>
 * @since  2016-11-24
 */
namespace Controller\MassData;


use Business\AppCenter\Module as ModuleBiz;
use Business\Dc\DcExcelTaskBase;
use Business\DownloadCenter\CustomField\Driver\ExportDetailTradeRecord;
use Business\DownloadCenter\CustomField\Driver\ExportTeamOrderSearch;
use Business\DownloadCenter\CustomField\Driver\ExportTeamOrderSettle;
use Business\DownloadCenter\CustomField\Factory;
use Business\DownloadCenter\CustomField\Factory as CustomFieldFactory;
use Business\MemberLogin\MemberLoginHelper;
use Business\SubMerchant\SubMerchant;
use JsonRpc\Export\GatePassLogExport;
use Library\Constants\BizCode;
use Library\Constants\Color;
use Library\Constants\PassLog\PassLogConst;
use Library\Controller;
use Library\Helper\ErrorHelper;
use Library\Util\DebugUtil;
use Library\Util\EnvUtil;
use Library\Util\PassLogUtil;
use Model\MassData;
use Business\Statistics\statistics;
use Model\Product\Land;
use Process\Product\ProductZgyList as businessLib;

class ExportListen extends Controller
{
    private $_errPath = "/excel/error";
    private $_fid;
    private $_loginInfo;

    private $_downloadCenter = [
        85  => ExportTeamOrderSearch::class,
        117 => ExportTeamOrderSettle::class,
    ];

    /**
     * 判断是否登陆 获取登录人ID
     */
    public function __construct()
    {
        $this->_fid       = $this->isLogin();
        $this->_loginInfo = $this->getLoginInfo('ajax', false, false);
        if(EnvUtil::isLocal()) {

        } else {
        }
    }

    /**
     * 转分销产品导出任务
     * <AUTHOR>
     * @vacation Judge
     * @date 2019-05-20
     */
    public function productEvoluteJudge()
    {
        //初始化返回信息
        $code = 3;
        $data = '';
        $msg  = '不需要记录任务';

        try {
            //需要用到的登录信息
            $loginInfo = $this->getLoginInfo();
            $memberId = $loginInfo['memberID'];
            $saccount = $loginInfo['saccount'];
            $sid = $loginInfo['sid'];

            $judgeType = I('post.judgeType');

            if (empty($judgeType)) {
                throw new \Exception("报表类型不能为空");
            }

            $excelType = load_config('excelType', 'excel');
            if (empty($excelType[$judgeType])) {
                throw new \Exception("未知报表类型");
            }

            $class  = $excelType[$judgeType]['class'];
            $action = $excelType[$judgeType]['preAction'];
            $limit  = $excelType[$judgeType]['limit'];

            if (!class_exists($class) || !method_exists($class, $action)) {
                throw new \Exception("系统错误, {$class}_{$action}");
            }

            $request = I('param.');
            $request = array_merge(['sid'=>$sid,'saccount'=>$saccount, 'member_id'=>$memberId], $request);

            $excelTask = new MassData\ExcelTask();
            // 产品列表 hash 目前按照时间戳进行hash 保证实时性
            $time = time();
            $hash = md5($memberId . json_encode($request) . $time);
            //如果有重复的哈希值 不重新执行任务
            $res      = $excelTask->getTaskByHash($hash);

            if (!empty($res)) {
                $code = 4;
                $data = $res['id'];
                $msg  = "你已经生成过该报表, 根据唯一标识符进入报表中心查看";
            } else {
                //开始导出
                $run = new $class();
                $res = $run->$action($limit);

                //释放
                unset($run);

                if ($res['code'] == 200) {
                    $excelTask->setTaskOperatorId($memberId);
                    $data = $excelTask->createTask($this->_fid, $request, $hash, $judgeType, 1, '', 1, 1, '', 0, '');
                    $code = 1;
                    $msg  = '记录任务';
                }
            }

        } catch(\Exception $e) {
            $code = 2;
            $msg  = $e->getMessage();
            $this->_logNote($msg, $judgeType);
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 自供应产品导出
     * <AUTHOR>
     * @vacation Judge
     * @date 2019-05-20
     */
    public function productJudge()
    {
        //初始化返回信息
        $code = 3;
        $data = '';
        $msg  = '不需要记录任务';

        try {
            //需要用到的登录信息
            $loginInfo = $this->getLoginInfo();
            $memberId  = $loginInfo['memberID']; //会员id
            $isSuper   = $this->isSuper();        //判断登陆用户是不是管理员
            $dtype     = $loginInfo['dtype'];    //登录账号类型
            $qx        = $loginInfo['qx'];       //qx判断
            $sdtype    = $loginInfo['sdtype'];  //上级供应商账号类型
            $sid       = $loginInfo['sid'];     //上级id

            if (MemberLoginHelper::isSubMerchantLogin()) {
                $memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $sid      = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            }

            //权限判断
            $Business = new \Business\Product\ProductZgyList;
            $check = $Business ->judgeAuth($dtype,$qx,$sdtype);
            if ($check["code"] !== 0) {
                $this->apiReturn($check["code"], [], $check["msg"]);
            }

            if ($dtype == 6 && !$isSuper) {
                $memberId = $sid;                       //员工情况所属上级id
            }

            //参数接收
            $data = businessLib::getZgyExportParams();
            if ($data["code"] !== 0) {
                $this->apiReturn($data["code"], [], $data["msg"]);
            }

            $judgeType = I('post.judgeType');

            if (empty($judgeType)) {
                throw new \Exception("报表类型不能为空");
            }

            $excelType = load_config('excelType', 'excel');
            if (empty($excelType[$judgeType])) {
                throw new \Exception("未知报表类型");
            }

            $class  = $excelType[$judgeType]['class'];
            $action = $excelType[$judgeType]['preAction'];
            $limit  = $excelType[$judgeType]['limit'];

            if (!class_exists($class) || !method_exists($class, $action)) {
                throw new \Exception("系统错误, {$class}_{$action}");
            }

            $request = I('param.');
            $request = array_merge(['member_id'=>$memberId, 'is_super'=>$isSuper], $request);
            if (MemberLoginHelper::isSubMerchantLogin()){
                $request['subSid'] = MemberLoginHelper::getLoginBusinessMember()->getSId();
            }

            $excelTask = new MassData\ExcelTask();
            // 产品列表 hash 目前按照时间戳进行hash 保证实时性
            $time = time();
            $hash = md5($memberId . json_encode($request) . $time);
            //如果有重复的哈希值 不重新执行任务
            $res      = $excelTask->getTaskByHash($hash);

            if (!empty($res)) {
                $code = 4;
                $data = $res['id'];
                $msg  = "你已经生成过该报表, 根据唯一标识符进入报表中心查看";
            } else {
                //开始导出
                $run = new $class();
                $res = $run->$action($limit);

                //释放
                unset($run);

                if ($res['code'] == 200) {
                    $excelTask->setTaskOperatorId($loginInfo['memberID']);
                    $data = $excelTask->createTask($this->_fid, $request, $hash, $judgeType, 1, '', 1, 1, '', 0, '');
                    $code = 1;
                    $msg  = '记录任务';
                }
            }
        } catch(\Exception $e) {
            $code = 2;
            $msg  = $e->getMessage();
            $this->_logNote($msg, $judgeType);
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 检测
     *
     * @date 2019-05-20
     * return array
     *        $res = [
     *            'code' => '',       // 1 记录任务进队列 成功;  2  记录任务进队列 失败 抛出异常;  3  不需要记录任务
     *            'msg'  => '',       // 详细信息
     *        ]
     *
     *
     */
    public function Judge()
    {
        // 假日模式管控
        $vocationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage('Judge', $this->_loginInfo['saccount']);
        if ($vocationMode === false) {
            exit(json_encode(['code' => 401, 'data' => [], 'msg' => '当前处于假日模式，该功能被限制使用']));
        }

        //初始化返回信息
        $code = 3;
        $data = '';
        $msg  = '不需要记录任务';

        try {
            $request = I('param.');
            $dtype   = $this->_loginInfo['dtype'];
            $account = $this->_loginInfo['account'];

            if($dtype == 2 || $dtype == 3)
            {
                $landModel = new Land();
                $tmp = $landModel->getListByResource($account, $dtype);
                $request['land_id'] = $tmp[0]['id'];
                //如果都没有景区的话，直接返回空
                if (!$request['land_id']) {
                    $this->apiReturn(200, [], '找不到景区');
                }

                $request['is_land'] = 1;
            }

            $judgeType = I('post.judgeType');

            if (empty($judgeType)) {
                throw new \Exception("报表类型不能为空");
            }

            $excelType = load_config('excelType', 'excel');
            if (empty($excelType[$judgeType])) {
                throw new \Exception("未知报表类型");
            }

            //验证报表异步导出，归档查询
            if (in_array($judgeType, [11, 12])) {
                //开始时间
                $begin = I('begin_date');
                //结束时间
                $end = I('end_date');
                $isAllow = (new \Controller\report\statistics())->checkDateAllowQuery($begin, $end);
                if (!$isAllow) {
                    throw new \Exception("不支持该时间段内的查询");
                }
            }

            $class                      = $excelType[$judgeType]['class'];
            $action                     = $excelType[$judgeType]['preAction'];
            $limit                      = $excelType[$judgeType]['limit'];
            $templateCode               = $excelType[$judgeType]['templateCode'] ?? '';
            $selfDefinedStructure       = $excelType[$judgeType]['selfDefinedStructure'] ?? false;
            $selfDefinedStructureAction = $excelType[$judgeType]['selfDefinedStructureAction'] ?? '';
            $downloadCenter             = $excelType[$judgeType]['downloadCenter'] ?? false;
            $driverName                 = $excelType[$judgeType]['driverName'] ?? false;
            // 有新版导出
            if (!empty($templateCode)) {
                // 请求新版下载接口
                //如果配置需要自定义表头$selfDefinedStructure 则通过配置的方法获取自定义表头
                $customStructure = [];
                //http://gitlab.12301.test/pft-doc/app-doc/-/blob/master/%E6%96%B0%E4%B8%8B%E8%BD%BD%E4%B8%AD%E5%BF%83/03%E5%8F%91%E8%B5%B7%E5%AF%BC%E5%87%BA%E4%BB%BB%E5%8A%A1.md
                if ($selfDefinedStructure === true && $downloadCenter === false) {
                    if (!class_exists($class) || !method_exists($class, $selfDefinedStructureAction)) {
                        throw new \Exception("系统错误, {$class}_{$selfDefinedStructureAction}");
                    }
                    $customStructure = (new $class())->$selfDefinedStructureAction($request['search_config_id']);
                } elseif ($downloadCenter === true) {
                    $customStructure = $this->_getDownloadCenterSelfDefineField($judgeType);
                }
                $this->_createTaskNew($this->_loginInfo['sid'], $this->_loginInfo['memberID'], $templateCode, $request, $customStructure);
                return;
            }

            if (!class_exists($class) || !method_exists($class, $action)) {
                throw new \Exception("系统错误, {$class}_{$action}");
            }

            $excelTask = new MassData\ExcelTask();
            //哈希值
            if (!empty($excelType[$judgeType]['dateDistin'])) {
                $time = time();
                $hash = md5($this->_fid . "_" . json_encode($request) . "_" . $time);

                if (!empty($excelType[$judgeType]['noLimitToday']) && !empty($request[$excelType[$judgeType]['noLimitToday']])) {
                    // 如果小于今天凌晨就不用一直重新生成
                    $todayStamp = strtotime(date("Y-m-d"));
                    if (strtotime($request[$excelType[$judgeType]['noLimitToday']]) < $todayStamp) {
                        $hash = md5($this->_fid . "_" . json_encode($request) . '_' . date("YmdH"));
                        if ($judgeType == 23) {
                            //获取自定义导出模板hash
                            $selectTag = Factory::getDriver(ExportDetailTradeRecord::class, $this->_loginInfo['sid'],
                                $this->_loginInfo['memberID'], $this->_loginInfo['sdtype'])->getUserSelectData();
                            if ($selectTag) {
                                $selectTagStr = implode(',', $selectTag);
                                //交易记录导出数据，非当日需要限制到小时
                                $hash = md5($this->_fid . "_" . json_encode($request) . '_' . $selectTagStr . '_' . date("YmdH"));
                            }
                        }
                    }
                }
            } else {
                $hash = md5($this->_fid . "_" . json_encode($request));
            }

            //如果有重复的哈希值 不重新执行任务
            $res      = $excelTask->getTaskByHash($hash);

            //hash查询后判断
            if (!empty($res) && !empty($excelType[$judgeType]['hashAfter'])) {
                $hashAfter = $excelType[$judgeType]['hashAfter'];
                $hashAfter = is_array($hashAfter) ? $hashAfter : [];
                $checkTaskInfo = $this->_handleGetTaskInfoCheck($res, $hashAfter, $request);
                if ($checkTaskInfo) { //true的时候需要重新生成
                    unset($res);
                }
            }

            //根据游玩日期和有效期查询结果，导出明细的时候，重新生成最新的明细表格到报表中心
            if(($judgeType == 14) && (!empty($request['play_time_start']) || !empty($request['play_time_end']) || !empty($request['begin_time_start']) || !empty($request['begin_time_end'])))
            {
                unset($res);
            }

            //judgeType = 24分终端汇总报表
            if($judgeType == 19 || $judgeType == 24){
                unset($res);
            }

            if (!empty($res)) {
                $code = 4;
                $data = $res['id'];
                $msg  = "你已经生成过该报表, 根据唯一标识符进入报表中心查看";
            } else {
                //开始导出
                $run = new $class();
                $res = $run->$action($limit, $this->_loginInfo['saccount']);

                //释放
                unset($run);

                if ($res['code'] == 200) {
                    $data = $this->_createTask($judgeType, $hash, $this->_loginInfo);
                    $code = 1;
                    $msg  = '记录任务';
                } else {
                    !empty($res['msg']) && $msg = $res['msg'];
                }
            }

        } catch (\Exception $e) {
            $code = 2;
            $msg  = $e->getMessage();
            $this->_logNote($msg, $judgeType);
        }

        $this->apiReturn($code, $data, $msg);
    }


    /**
     * 创建任务-新下载中心
     */
    private function _createTaskNewV2($fid, $opId, $templateCode, $request, $customStructure = [])
    {
        $dcExcelTaskBase = new DcExcelTaskBase();
        $result          = $dcExcelTaskBase->addTask($fid, $opId, $templateCode, $request,0, '', '', $customStructure);
        $data            = $result['data'] ?: [];
        $msg            = $result['msg'];
        $code            = $result['code'];
        return $result;
    }

    /**
     * 创建任务-新下载中心
     */
    private function _createTaskNew($fid, $opId, $templateCode, $request, $customStructure = [])
    {
        $dcExcelTaskBase = new DcExcelTaskBase();
        $result          = $dcExcelTaskBase->addTask($fid, $opId, $templateCode, $request,0, '', '', $customStructure);
        $data            = $result['data'] ?: [];
        $msg            = $result['msg'];
        $code            = $result['code'];
        if ($code == '200') {
            if ($msg == null) {
                return $this->apiReturn(1, $data['taskId'], '记录任务');
            } else {
                return $this->apiReturn(4, $data['taskId'], $msg);
            }
        }
        return $this->apiReturn(2, $data['taskId'], $result['msg']);
    }

    /**
     * 创建任务
     * <AUTHOR>
     * @date 2021/2/6
     *
     * @param int $type 报表类型   配置见 trade_record.conf.php
     * @param string $hash
     * @param array $loginInfo 登录信息
     *
     * @return array
     */
    private function _createTask($type, $hash, $loginInfo)
    {
        $request = I('param.');
        $dtype   = $loginInfo['dtype'];
        $account = $loginInfo['account'];
        $sid = $loginInfo['sid'];

        if($dtype == 2 || $dtype == 3)
        {
            $landModel = new Land();
            $tmp = $landModel->getListByResource($account, $dtype);

            $request['land_id'] = $tmp[0]['id'];
            $request['is_land'] = 1;
        }

        if (empty($request) || !is_array($request) || empty($this->_fid) || empty($type) || !is_numeric($type)) {
            throw new \Exception("异常情况, 请重试");
        }

        $excelTask = new MassData\ExcelTask();

        $statisticsBusiness = new statistics();
        //获取查询数据
        $begin_date  = $request['begin_date'];
        $end_date    = $request['end_date'];
        $land_id     = $request['land_id'];
        $reseller_id = $request['reseller_id'];
        $excelType   = load_config('excelType', 'excel');
        $excelName   = $excelType[$request['judgeType']]['desc'];
        //应收应付报表导出特别定制
        if ($request['judgeType'] == '8') {
            $count_way = '';
            if ($request['type'] == '1') {
                $typeName = '应收报表';
            }else if ($request['type'] == '2') {
                $typeName = '应付报表';
            }
            if ($request['searchTicket'] == 'true') {
                $ticketName = '按票统计';
            }else {
                $ticketName = '未按票统计';
            }

            $search_type = '';
        }else {
            $count_way   = $request['count_way'];
            $search_type = $request['search_type'];
        }
        //修复导出套票报表的月汇总是date缺失导致无数据，套票的预定月汇总是从日报里直接获取的，所以这边单独补充date字段  by jackchb
        // if (($request['judgeType'] == 13 || $request['judgeType'] == 15) && !isset($request['date']) && $request['date_type'] == 2 && !empty($end_date)){
        //     $request['date'] = date("Ym", strtotime($end_date));
        // }

        //添加参数
        //$loginInfo = $this->getLoginInfo();

        //资源中心首次入园导出报表补充字段
        //年卡管理操作记录 也需要传递sid
        if (in_array($request['judgeType'],[42, 61, 83, 92, 101, 102])){
            $request['dtype'] = $dtype;
            //$sid              = $loginInfo['sid'];
            $request['sid']   = $sid;
        }
        $memberId  = $loginInfo['memberID']; //会员id
        $request   = array_merge(['member_id'=>$memberId], $request);

        //导出订单搜索excel
        if ($request['judgeType'] == 14){
            $request['sdtype'] = $loginInfo['sdtype']; //三亚导出分销商账户需要判断
            if (MemberLoginHelper::isSubMerchantLogin()){
                $request['sub_supplier_id'] = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $subSid = MemberLoginHelper::getLoginBusinessMember()->getSId();
                $request = array_merge(
                    [
                        'sub_sid' => $subSid,
                        'needSubMerchantField' => false,//excel是否显示子商户列
                    ],
                    $request
                );
            }
            //如果登录的是供应商
            if ($dtype == 0){
                //获取子商户应用记录
                $userModuleUsedRes = (new ModuleBiz())->getUserModuleUsed([$loginInfo['sid']], [SubMerchant::SUB_MERCHANT_APP_CENTER_TAG], false);
            }
            //如果登录的是员工
            if ($dtype == 6 && $loginInfo['sdtype'] == 0){
                //获取子商户应用记录
                $userModuleUsedRes = (new ModuleBiz())->getUserModuleUsed([$loginInfo['sid']], [SubMerchant::SUB_MERCHANT_APP_CENTER_TAG], false);
            }
            //存在子商户应用记录，则excel会有商户列
            if (!empty($userModuleUsedRes[$loginInfo['sid']][SubMerchant::SUB_MERCHANT_APP_CENTER_TAG])){
                $request['needSubMerchantField'] = true;
            }
        }

        //子商户销售报表处理
        if (in_array($request['judgeType'], [11, 12])) {
            if (MemberLoginHelper::isSubMerchantLogin()) {
                $request['sub_supplier_id'] = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $request['sub_merchant_id'] = MemberLoginHelper::getLoginBusinessMember()->getSId();
            }
        }

        //根据条件 获取导出名 所需要的数据
        //$extInfo     = $statisticsBusiness->getUploadInfoExt($begin_date, $end_date, $land_id, $reseller_id, $count_way, $search_type, $request['type']);
        //拼接导出字符
        //        if ($request['judgeType'] == '8') {
        //            $memo    = $typeName .'_'. $extInfo['begTime'].'_'. $extInfo['endTime'].'_'.$extInfo['dis'].
        //                '_'.$extInfo['land']. '_'.$ticketName;
        //        }else {
        //            $memo    = $excelName .'_'. $extInfo['begTime'].'_'. $extInfo['endTime'].'_'.$extInfo['land'].
        //                '_'.$extInfo['dis']. '_'.$extInfo['countWay'];
        //        }
        $memo = '';
        $excelTask->setTaskOperatorId($memberId);
        $id        = $excelTask->createTask($this->_fid, $request, $hash, $type, 1, '', 1, 1, '', 0, $memo);

        return $id;
    }

    /**
     * 根据配置文件和已生成的数据
     * 判断是否需要重新导出
     * <AUTHOR>
     * @date 2021/7/22
     *
     * @param  array  $res 任务信息
     * @param  array  $hashAfter  配置信息
     * @param  array  $request  请求信息
     *
     * @return bool  true 重新导出  false继续往下判断
     */
    private function _handleGetTaskInfoCheck(array $res, array $hashAfter, array $request)
    {
        if (empty($res) || empty($hashAfter)) {
            return false;
        }

        $createTime = date("Y-m-d", $res['create_time']);

        $checkPreDate    = $hashAfter['checkPreDate'] ?? '';
        $checkPreDateVal = empty($checkPreDate) ? '' : ($request[$checkPreDate] ?? '');
        if (!empty($checkPreDateVal)) {
            $checkPreDateVal = date("Y-m-d", strtotime($checkPreDateVal));
            //规则：查询结束时间和已有创建时间 是同一天的话，允许重新生成
            if ($checkPreDateVal == $createTime) {
                return true;
            }
        }

        return false;
    }

    /**
     * 日志记录
     */
    private function _logNote($msg, $type) {
        //记录请求用户ID
        $errMsg    = "用户ID：{$this->_fid}\n";
        //获取IP
        $ip        = get_client_ip();
        $errMsg   .= "请求IP：{$ip}\n";
        //原始的请求参数
        $request   = I('param.');
        $request   = var_export($request, true);
        $errMsg   .= "请求参数：{$request}\n";
        //报表类型
        $excelList = load_config('excelType', 'trade_record');
        $excelType = empty($excelList[$type]['desc']) ? 'unknown' : $excelList[$type]['desc'];
        $errMsg   .= "报表类型：{$excelType}\n";
        //错误原因
        $errMsg   .= "错误原因：" . $msg;

        pft_log($this->_errPath, $errMsg);
    }

    public function exportForPasslog()
    {
        $params = I('param.');
        DebugUtil::debug([
            'title' => '导出参数',
            'params' => $params,
        ]);

        try {
            /**
             * identify : 关键字查询。
             * ordernum
             * code 凭证号
             * chk_code 门票码
             * name: 人员姓名
             * mobile: 联系电话
             * begin_time: 2024-06-01 00:00:00
             * end_time: 2024-06-07 23:59:59
             * type: 0
             * status: 0
             * sid:
             * page: 1
             * limit: 10
             */
            $type = I('type',0,'intval');

            $request = [];
            $request['identify'] = I('identify','');
            $request['ordernum'] = I('ordernum','');
            $request['code'] = I('code','');
            $request['chk_code'] = I('chk_code','');
            $request['name'] = I('name','');
            $request['mobile'] = I('mobile','');
            $request['begin_time'] = I('begin_time','');
            $request['end_time'] = I('end_time','');
            $request['end_time'] = I('end_time','');
            $request['status'] = I('status', 2, 'intval');
            $request['type'] = $type;
            $request['page'] = I('page',1,'intval');
            $request['limit'] = I('limit',10,'intval');
            $request['person_type'] = I('person_type',0, 'intval');

            if(!PassLogUtil::isRightType($type)) {
                ErrorHelper::bizError('type参数错误');
            }
            $sid = $this->_loginInfo['sid'];
            $memberId = $this->_loginInfo['memberID'];
            $templateCode = PassLogConst::DOWNLOAD_TEMP_CODE;
            $request['sid'] = $sid;
            $customStructure = PassLogUtil::getSSDByType($type);
            if(empty($customStructure)) {
                ErrorHelper::bizError('奇怪，为什么模板不存在');
            }
            DebugUtil::debug([
                'title' => Color::LIGHT.'提交给下载中心的请求参数',
                '$request' => $request,
            ]);
            $addRes = $this->_createTaskNewV2($sid,$memberId, $templateCode, $request, $customStructure);
            // 导出接口参考：\JsonRpc\Export\GatePassLogExport::exportData
            if(0){
                (new GatePassLogExport())->exportData([]);
            }
            if($addRes['code'] != '200') {
                $emsg = $addRes['msg'] ?? '未知错误';
                if(empty($addRes['msg'])) {
                    $emsg ='未知错误';
                } else {
                    $emsg = $addRes['msg'];
                }
                ErrorHelper::bizError($emsg);
            }
            $taskId = $addRes['data']['taskId'] ?? 0;
            if(empty($taskId)) {
                ErrorHelper::bizError('错误，任务ID为空');
            }

            $code = BizCode::CODE_SUCCESS;
            $data = $taskId;
            $msg  = "导出任务已经添加";
            $this->apiReturn($code, $data, $msg);
        } catch (\Throwable $e){
            $code = BizCode::CODE_PARAM_ERROR;
            $emsg = $e->getMessage();
            $this->apiReturn($code, null, $emsg);
        }
    }

    /**
     * 获取下载中心自定义字段
     *
     * @param  string $driverName 类名称
     *
     * @return string
     * @throws \Exception
     */
    private function _getDownloadCenterSelfDefineField($judgeType)
    {
        //获取导出自定义字段配置
        $customField = CustomFieldFactory::getDriver($this->_downloadCenter[$judgeType], $this->_loginInfo['sid'], $this->_loginInfo['memberID']);
        $selectField = $customField->getUserSelectData();
        //获取新的抬头
        $defaultField = $customField::getDefaultCustomFieldListMap();
        $head         = [];
        foreach ($selectField as $value) {
            $head[] = $defaultField[$value];
        }
        $returnData = [
            'head'  => $head,
            'field' => $selectField,
        ];


        return $returnData;
    }
}