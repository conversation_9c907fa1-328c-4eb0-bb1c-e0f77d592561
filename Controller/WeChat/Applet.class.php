<?php
/**
 * 小程序相关接口
 * <AUTHOR>
 * @date 2018-08-18
 */

namespace Controller\WeChat;

use Library\Business\WechatSmallApp;
use Library\Cache\Cache;
use Library\Controller;
use Model\Wechat\WxMember;
use Model\Member\Member;
use Model\Finance\PayMerchant;
use Business\Wechat\Applet as AppletBiz;
use Library\wechat\core\OpenPlatform;
use Process\Resource\Qiniu;
use Qiniu\Auth;
use Qiniu\Storage\UploadManager;

class Applet extends Controller
{

    private $memberId;
    private $loginInfo;
    private $wxMemberModel;
    private $memberModel;
    private $payMerchantModel;

    /**
     * Applet constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->memberId  = $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo('ajax', false, false);
    }

    /**
     * 获取member模型
     * <AUTHOR>
     * @date 2018-07-20
     */
    private function getMemberModel()
    {
        if (empty($this->memberModel)) {
            $this->memberModel = new Member();
        }

        return $this->memberModel;
    }

    /**
     * 获取wxMember模型
     * <AUTHOR>
     * @date 2018-07-13
     */
    private function getWxMemberModel()
    {
        if (empty($this->wxMemberModel)) {
            $this->wxMemberModel = new WxMember();
        }

        return $this->wxMemberModel;
    }

    /**
     * 获取payMerchantModel
     * <AUTHOR>
     * @date 2018-07-17
     */
    private function getPayMerchantModel()
    {
        if (empty($this->payMerchantModel)) {
            $this->payMerchantModel = new PayMerchant();
        }

        return $this->payMerchantModel;
    }

    /**
     * 返回小程序授权模板
     * <AUTHOR>
     * @date 2018-07-13
     */
    public function getAuthTemplate()
    {
        // 判断是否管理员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        // 获取accessToken, 该方法中已获取component_verify_ticket
        $accessToken = OpenPlatform::AccessToken();

        $res = OpenPlatform::getTempLateInfo($accessToken['component_access_token']);

        if ($res['errcode'] != 0) {
            $this->apiReturn(204, '', $res['errmsg']);
        }

        $this->apiReturn(200, $res['template_list'], '获取模板成功');
    }

    /**
     * 获取授权信息
     * <AUTHOR>
     * @date 2018-07-13
     */
    public function getAuthInfo()
    {
        // 判断是否管理员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        // 用户id
        $memberId = I('post.member_id');
        $appletId = I('post.applet_id');

        if (empty($memberId)) {
            $this->apiReturn(204, '', '用户id不能为空');
        }

        if (empty($appletId)) {
            $this->apiReturn(204, '', '小程序应用id不能为空');
        }

        // 获取授权相关信息
        $wxMemberModel = new \Model\Wechat\WxOpen();
        $wxOpenInfo    = $wxMemberModel->getAppId($memberId);

        // 获取支付相关配置
        $payMerchantModel = $this->getPayMerchantModel();
        $merchantConfig   = $payMerchantModel->getWePayMerChantConfigByMemberId($memberId, 'memberid');

        // 获取绑定应用信息
        $appletModel = new \Model\Wechat\AppletApp();
        $appletInfo  = $appletModel->getAppletById($wxOpenInfo['applet_id'], 'name');

        $res = [
            // 小程序名称
            'app_name'    => isset($wxOpenInfo['nick_name']) ? $wxOpenInfo['nick_name'] : '',
            // 是否绑定微信支付
            'pay_config'  => isset($merchantConfig) ? $merchantConfig : '',
            // 绑定应用名称
            'applet_name' => $appletInfo['name'],
        ];

        $this->apiReturn(200, $res, '获取信息成功');
    }

    /**
     * 获取第三方授权二维码
     * <AUTHOR>
     * @date 2018-07-09
     */
    public function getAuthPage()
    {
        // 判断是否管理员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        // 应用id
        $appletId = I('post.applet_id');
        // 用户id
        $memberId = I('post.member_id');
        // 用户账号
        $account = I('post.account');

        if (empty($appletId)) {
            $this->apiReturn(204, '', '应用id不能为空');
        }

        if (empty($memberId)) {
            $this->apiReturn(204, '', '用户id不能为空');
        }

        if (empty($account)) {
            $this->apiReturn(204, '', '用户账号不能为空');
        }

        // 获取accessToken, 该方法中已获取component_verify_ticket
        $accessToken = OpenPlatform::AccessToken();
        // 获取preAuthCode
        $preAuthCode = OpenPlatform::PreAuthCode($accessToken['component_access_token']);
        // 获取扫描路径, 并生成二维码
        $authPage = OpenPlatform::createAppletAuthPage($preAuthCode['pre_auth_code'], 2, $memberId, $account,
            $appletId);

        $data = [
            'auth_page' => $authPage,
        ];

        $this->apiReturn(200, $data, '');
    }

    /**
     * 获取用户信息通过手机
     * <AUTHOR>
     * @date 2018-07-19
     */
    public function getMemberInfoMobile()
    {
        // 判断是否管理员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        $mobile = I('post.mobile');

        if (!\ismobile($mobile)) {
            $this->apiReturn(204, '', '请输入合法手机号');
        }

        $memberModel = $this->getMemberModel();

        $res = $memberModel->getInfoByAccount($mobile, 'id, account, dname', false, true);
        // print_r($res);
        // 过滤非供应商的账号
        foreach ($res as $key=>$item) {
            if ($item['dtype'] == 0 ) {
                $flag = $key;
                break;
            }
        }
        if (!isset($flag)) {
            $this->apiReturn(204, '', '找不到对应的供应商数据');
        }
        $this->apiReturn(200, $res[$flag], '获取信息成功');
    }

    /**
     * 获取小程序应用列表
     * <AUTHOR>
     * @date 2018-08-18
     */
    public function getAppletAppList()
    {
        // 判断是否管理员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        $page = I('post.page', '1', 'intval');
        $size = I('post.size', '15', 'intval');
        // 应用名称关键字
        $keyWord = I('post.key_word');

        $appletModel = new \Model\Wechat\AppletApp();
        $res = $appletModel->getAppletAppList($keyWord, false, $page, $size);

        $this->apiReturn(200, $res, '获取应用列表成功');
    }

    /**
     * 添加小程序应用
     * <AUTHOR>
     * @date 2018-07-18
     */
    public function addAppletApp()
    {
        // 判断是否管理员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        // 应用名称
        $name = I('post.name');
        // 应用标识
        $iden = I('post.iden');
        // 应用简介
        $desc = I('post.desc');

        if (empty($name)) {
            $this->apiReturn(204, '', '应用名称不能为空');
        }

        if (empty($iden)) {
            $this->apiReturn(204, '', '应用标识不能为空');
        }

        if (empty($desc)) {
            $this->apiReturn(204, '', '应用简介不能为空');
        }

        $appletModel = new \Model\Wechat\AppletApp();
        $res         = $appletModel->addAppletApp($name, $iden, $desc);

        if (!$res) {
            $this->apiReturn(204, '', '添加失败');
        }

        $this->apiReturn(200, '', '添加成功');
    }

    /**
     * 更新小程序应用
     * <AUTHOR>
     * @date 2018-07-18
     */
    public function editAppletApp()
    {
        // 判断是否管理员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        // 小程序应用id
        $appId = I('post.app_id');
        // 应用名称
        $name = I('post.name');
        // 应用标识
        $iden = I('post.iden');
        // 应用简介
        $desc = I('post.desc');

        if (empty($appId)) {
            $this->apiReturn(204, '', '参数错误');
        }

        if (empty($name)) {
            $this->apiReturn(204, '', '应用名称不能为空');
        }

        if (empty($iden)) {
            $this->apiReturn(204, '', '应用标识不能为空');
        }

        if (empty($desc)) {
            $this->apiReturn(204, '', '应用简介不能为空');
        }

        $appletModel = new \Model\Wechat\AppletApp();
        $res         = $appletModel->updateAppletApp($appId, $name, $iden, $desc);

        if (!$res) {
            $this->apiReturn(204, '', '更新失败');
        }

        $this->apiReturn(200, '', '更新成功');
    }

    /**
     * 删除小程序应用， 软删除
     * <AUTHOR>
     * @date 2018-07-18
     */
    public function delAppletApp()
    {
        // 判断是否管理员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        // 小程序应用id
        $appId = I('post.app_id');

        if (empty($appId)) {
            $this->apiReturn(204, '', '参数错误');
        }

        $appletModel = new \Model\Wechat\AppletApp();
        $res         = $appletModel->deleteAppletApp($appId);

        if (!$res) {
            $this->apiReturn(204, '', '删除失败');
        }

        $this->apiReturn(200, '', '删除成功');
    }

    /**
     * 获取小程序用户授权列表
     * <AUTHOR>
     * @date 2018-07-20
     */
    public function getAppletAuthList()
    {
        // 判断是否管理员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        // 手机号
        $mobile = I('post.mobile');

        // 小程序应用id
        $appletId = I('post.applet_id');
        $page     = I('post.page', '1', 'intval');
        $size     = I('post.size', '15', 'intval');

        $appletBiz = new AppletBiz();
        $res       = $appletBiz->appletAuthList($mobile, $appletId, $page, $size);

        if ($res['code'] != 200) {
            $this->apiReturn(204, '', $res['msg']);
        }

        $this->apiReturn(200, $res['data'], '获取列表成功');
    }

    /**
     * 为用户选择选择模板
     * <AUTHOR>
     * @date 2018-07-23
     */
    public function commitAppletTemplate()
    {
        // 模板id
        $templateId = I('post.template_id');
        if (empty($templateId)) {
            $this->apiReturn(204, '', '请选择模板');
        }

        // appid
        $appId = I('post.app_id');
        if (empty($appId)) {
            $this->apiReturn(204, '', '请选择小程序');
        }

        // 版本号
        $version = I('post.version');
        if (empty($version)) {
            $this->apiReturn(204, '', '版本号不能为空');
        }

        // 代码描述
        $desc = I('post.desc');
        if (empty($desc)) {
            $this->apiReturn(204, '', '代码描述不能为空');
        }

        $appletBiz = new AppletBiz();
        $res       = $appletBiz->commitTemplate($templateId, $appId, $version, $desc);

        if ($res['code'] != 200) {
            $this->apiReturn(204, '', $res['msg']);
        }

        $this->apiReturn(200, '', '部署成功');
    }

    /**
     * 搜索相关账号的小程序码图片
     * Create by zhangyangzhen
     * Date: 2018/10/9
     * Time: 14:19
     */
    public function getSmallAppCode()
    {
        if (!$this->isSuper()) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '请登录管理员账号查看');
        }

        $memberid = I('post.id', 0);
        $account  = I('post.account', 0);
        $width    = I('post.width', 430);

        if (empty($memberid) && empty($account)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '用户ID或账号不能为空');
        }

        $memberModel = $this->getMemberModel();

        if (empty($memberid) && !empty($account)) {
            $memberInfo = $memberModel->getMemberInfo($account, 'account', 'id, dname');

            if (empty($memberInfo)) {
                $this->apiReturn(self::CODE_NO_CONTENT, [], '未找到该用户信息');
            }

            $memberid = $memberInfo['id'];
            $dname    = $memberInfo['dname'];
        }

        if (!empty($memberid) && empty($account)) {
            $memberInfo = $memberModel->getMemberInfo($memberid, 'id', 'account, dname');

            if (empty($memberInfo)) {
                $this->apiReturn(self::CODE_NO_CONTENT, [], '未找到该用户信息');
            }

            $account = $memberInfo['account'];
            $dname   = $memberInfo['dname'];
        }

        $smallLib = new WechatSmallApp();
        $scenCode = $smallLib->encodeShopCode($memberid);//小程序码

        $page = 'pages/index/index';

        $cacheRedis = Cache::getInstance('redis');
        $cacheKey   = "smappcode:$account:$memberid:$width:$scenCode";
        $url        = $cacheRedis->get($cacheKey);
        if ($url) {
            parent::apiReturn(self::CODE_SUCCESS, ['url' => $url, 'name' => $dname]);
        }

        $res = $smallLib->getWxCodeUnlimit('123624', $scenCode, $page, $width);

        $prefix = substr($res['data'], 0, 1);
        if ($prefix == '{') {
            $decodeRes = json_decode($res['data'], true);
            $this->apiReturn(401, $res, $decodeRes['errmsg']);
        }

        $mdscenCode = md5($scenCode . $page . $width);
        $fileName   = "wpfsmappcode_$mdscenCode.png";
        $config     = load_config('qiniu', 'Qiniu');
        $qiniu      = new Qiniu($config);
        $fileUrl    = $qiniu->hasFileExist($fileName);

        if ($fileUrl != false) {
            $ret['url']  = $fileUrl;
            $ret['name'] = $dname;
            $this->apiReturn(self::CODE_SUCCESS, $ret);
        }

//        composer自动加载
        \Library\Tools\Helpers::composerAutoload();

        $upManager = new UploadManager();
        $auth      = new Auth($config['accessKey'], $config['secretKey']);
        $token     = $auth->uploadToken($config['images']['bucket'], null, 3600);
        list($ret, $error) = $upManager->put($token, $fileName, $res['data']);

        $qiniuConfig = $qiniu->getConfig();
        $ret['url']  = $qiniuConfig['images']['domain'] . $fileName;
        $ret['name'] = $dname;

        $cacheRedis->set($cacheKey, $ret['url']);

        $this->apiReturn(self::CODE_SUCCESS, $ret);
    }
}