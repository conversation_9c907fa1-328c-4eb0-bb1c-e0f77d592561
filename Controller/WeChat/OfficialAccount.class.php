<?php

namespace Controller\WeChat;

use Library\Controller;
use Business\Wechat\OfficialAccount as OfficialAccountBz;

class OfficialAccount extends Controller
{
    /**
     * 解除公众号绑定
     *
     * @return array
     * <AUTHOR>
     * @date 2021/1/14
     *
     * @return array
     */
    public function deleteOfficialAccountBind()
    {
        $id = I('post.id', 0, 'intval');
        if (empty($id)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $memberInfo        = $this->getLoginInfo();
        $officialAccountBz = new OfficialAccountBz();
        $result            = $officialAccountBz->deleteOfficialAccountBind($memberInfo['sid'], $id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取权限列表
     * @return array
     * <AUTHOR>
     * @date 2021/5/11
     *
     *
     */
    public function getFuncInfoList()
    {
        $memberInfo        = $this->getLoginInfo();
        $officialAccountBz = new OfficialAccountBz();
        $result            = $officialAccountBz->getFuncInfoList($memberInfo['sid']);


        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}