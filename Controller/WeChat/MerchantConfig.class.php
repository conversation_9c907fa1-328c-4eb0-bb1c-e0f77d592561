<?php
/**
 * 微信独立收款相关接口
 * <AUTHOR>
 * @date 2018-07-16
 */

namespace Controller\WeChat;

use Library\Controller;
use Model\Finance\PayMerchant;
use Business\Wechat\Applet as AppletBiz;

class MerchantConfig extends Controller
{

    private $memberId;
    private $payMerchantModel;


    /**
     * WxMerchantConfig constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->memberId = $this->isLogin('ajax');
    }


    /**
     * 获取payMerchantModel
     * <AUTHOR>
     * @date 2018-07-17
     */
    private function getPayMerchantModel()
    {
        if (empty($this->payMerchantModel)) {
            $this->payMerchantModel = new PayMerchant();
        }

        return $this->payMerchantModel;
    }


    /**
     * 添加或更新pft_merchant_wepay数据
     * <AUTHOR>
     * @date 2018-07-16
     */
    public function editMerchantWePay()
    {
        // 判断是否管路员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        // 要配置的用户id
        $id = I('post.id');
        // 商家名称
        $name = I('post.name');
        // 子商家商户号
        $subMerchantId = I('post.sub_mch_id');
        // 子商户appid
        $subAppId   = I('post.sub_appid');
        // 是否是更新
        $isUpdate   = I('post.update' , 0);
        // 用户id
        $memberId   = I('post.member_id');
        $useEnv     = I('post.use_env');

        if (empty($name)) {
            $this->apiReturn(204, '', '商家名称不能为空');
        }

        if (empty($subMerchantId)) {
            $this->apiReturn(204, '', '子商家商户号不能为空');
        }

        if (empty($subAppId)) {
            $this->apiReturn(204, '', '子商家appid不能为空');
        }

        $payMerchantModel = $this->getPayMerchantModel();

        if (!$isUpdate) {
            // 新增数据
            // 获取公司服务商配置
            $serverConfig = load_config('server_config', 'wepay_server');

            // 判断 memberid 跟 subappid对应的记录只能一条
            if ($payMerchantModel->getWxPayMerchantConfig($memberId, $subAppId)) {
                $this->apiReturn(204, '', '一个用户的小程序支付配置只能对应一个支付');
            }

            $res = $payMerchantModel->saveWePayMerchantConfig($memberId, $name, $serverConfig['app_id'], $serverConfig['mch_id'], $serverConfig['app_key'], $subAppId, $subMerchantId, $serverConfig['cert_path'], $serverConfig['key_path'], $useEnv);
        } else {
            // 更新数据
            $res = $payMerchantModel->updateWePayMerchantConfig($id, $name, $subAppId, $subMerchantId, $useEnv);
            // 获取配置信息
            $config = $payMerchantModel->getWePayMerChantConfigByMemberId($id, 'sub_appid');
            $subAppid = substr($config['sub_appid'], 0, 6);
            // 清除缓存, 支付配置相关缓存
            $redisCache = \Library\Cache\CacheRedis::getInstance('redis');
            // 删除配置缓存,在mobilepay中getMerchantConfig中使用到
            $redisCache->del("wepay:{$memberId}t3a{$subAppid}");
            // 设置当前配置独立支付为关闭
            $redisCache->hset(AppletBiz::APPLET_PAY_KEY, '', [$memberId.$subAppid => 2]);
        }

        if (!$res) {
            $this->apiReturn(204, '', '配置失败');
        }

        $this->apiReturn(200, '', '配置成功');
    }

    /**
     * 获取微信商户配置列表
     * <AUTHOR>
     * @date 2018-07-16
     */
    public function getMerchantConfigList()
    {
        // 判断是否管理员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        $memberId       = I('post.member_id');
        $page           = I('post.page', '1', 'intval');
        $size           = I('post.size', '15', 'intval');
        $useEnv         = I('post.use_env', '3', 'intval');
        $merchantType   = I('post.merchant_type',2);
        $payMerchantModel = $this->getPayMerchantModel();
        if ($merchantType == \Business\Finance\PayMerchant::ALIPAY) {
            $res = $payMerchantModel->getAlipayPayMerchantConfigList($memberId,  'memberid, name, appid, user_id, created_at', $page, $size);
        } else {
            $res = $payMerchantModel->getWePayMerchantConfigList($memberId,  'id, memberid, name, sub_merchant_id, status, sub_appid, use_env', $page, $size, $useEnv);
        }
        $this->apiReturn(200, $res, '获取成功');
    }


    /**
     * 获取微信商户配置详细信息
     * <AUTHOR>
     * @date 2018-07-17
     */
    public function getMerChantConfigInfo()
    {
        // 判断是否管理员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        $id = I('post.id');

        if (empty($id)) {
            $this->apiReturn(204, '', '参数错误');
        }

        $payMerchantModel = $this->getPayMerchantModel();

        $res = $payMerchantModel->getWePayMerChantConfigByMemberId($id, 'id, memberid, sub_merchant_id, sub_appid');

        $this->apiReturn(200, $res, '获取成功');
    }


    /**
     * 开启或关闭独立收款, 前提需要配置, 直接记录在缓存中
     * <AUTHOR>
     * @date 2018-07-23
     *
     */
    public function setMerchantIndependent()
    {
        // 判断是否管理员操作
        if (!$this->isSuper()) {
            $this->apiReturn(204, '', '无权限');
        }

        // 配置id
        $id = I('post.id');
        if (empty($id)) {
            $this->apiReturn(204, '', '配置id不能为空');
        }

        // 用户id
        $memberId = I('post.member_id');
        if (empty($memberId)) {
            $this->apiReturn(204, '', '用户id不能为空');
        }

        // 开启或关闭，
        $status = I('status');
        if (!in_array($status, [0, 1])) {
            $this->apiReturn(204, '', '非法操作');
        }

        $appletBiz = new AppletBiz();

        $res = $appletBiz->setMerchantIndependent($id, $memberId, $status);

        if ($res['code'] != 200) {
            $this->apiReturn(204, '', $res['msg']);
        }

        $this->apiReturn(200, '', '设置成功');
    }




}
