<?php

namespace Controller\MsgNotify;

use Model\Order\SmsJournal;
use Library\Resque\Queue;

/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 7/21-021
 * Time: 10:50
 */
class SmsNotify
{
    public function zwx()
    {
        $data = $_POST['xml'];
        if (empty($data)) {
            exit('empty request');
        }
        pft_log('sms/zwx_notify', $data);
        $obj = @simplexml_load_string($data);
        if ($obj->receiptCnt > 1) {
            $obj = $obj->receiptResult[0];
        }
        if ($obj->msgId) {
            $msgId  = $obj->msgId . '';
            $status = $obj->statusInt + 0;
            $state  = $obj->state . '';
            $mobile = $obj->srcTermId . '';
            $msg    = "report_status:{$status}|state:{$state}";
            $model  = new SmsJournal();
            $model->UpdateJournal($mobile, $msgId, $status, $msg, strtotime('20' . $obj->receiveDate));
        }

    }

    public function qcloud()
    {
        /*[{
        "mobile":"18750193275",
        "report_status":"SUCCESS",
        "description":"用户短信接收成功",
        "errmsg":"DELIVRD",
        "user_receive_time":"2017-02-14 18:17:29",
        "sid":"8:r67PrBGUaBrGnFiNbGG20170214",
        "nationcode":"86"
        }]*/
        $data = file_get_contents('php://input');
        if (empty($data)) {
            exit('empty request');
        }
        pft_log('sms/qcloud_notify', $data);
        $reply_info = json_decode($data, true);
        if ($reply_info && isset($reply_info[0]['sid'])) {
            $model       = new SmsJournal();
            $report_code = $reply_info[0]['report_status'] == 'SUCCESS' ? 200 : 0;
            $msg         = "report_status:{$reply_info[0]['report_status']}|errmsg:{$reply_info[0]['errmsg']}|描述:{$reply_info[0]['description']}";
            $res         = $model->UpdateJournal($reply_info[0]['mobile'], $reply_info[0]['sid'], $report_code, $msg,
                strtotime($reply_info[0]['user_receive_time']));
            if ($res === false) {
                pft_log('sms/qcloud_notify', 'sid:' . $reply_info[0]['sid']);
            }
            echo '200';
            //客户接收失败了，使用即时通渠道重发一次
            /*if ($report_code!==200 && $reply_info[0]['errmsg']!='MK:0010') {
                $log = $model->getJournalList($reply_info[0]['mobile'],'','','', self::PLATFORMID, $reply_info[0]['sid']);
                $vcome  = new VComSms();
                $result = $vcome->doSendSMS($reply_info[0]['mobile'], $log[0]['smstxt'], '');
                pft_log('sms/qcloud_notify', "用户接收失败，重发短信|sid:{$reply_info[0]['sid']},重发结果:{$result['code']}");
            }*/
        }
        echo '0';
    }

    public function qh()
    {
        $data = file_get_contents('php://input');
        if (empty($data)) {
            exit('empty request');
        }
        pft_log('sms/qh_notify', $data);
        $reply_info = json_decode($data, true);
        if ($reply_info && isset($reply_info['invoke_id'])) {
            $model                       = new SmsJournal();
            $report_code                 = $reply_info['status'] == '1' ? 200 : 0;
            $msg                         = "report_status:{$reply_info['status']}|errmsg:{$reply_info['reason']}|分配的号码:{$reply_info['sms_from_number']}";
            $reply_info['sms_to_number'] = str_replace('+86', '', $reply_info['sms_to_number']);
            $res                         = $model->UpdateJournal($reply_info['sms_to_number'], $reply_info['invoke_id'],
                $report_code, $msg, strtotime($reply_info['time']));
            if ($res === false) {
                pft_log('sms/qcloud_notify', 'sid:' . $reply_info[0]['sid']);
            }
            echo '200';
            //客户接收失败了，使用即时通渠道重发一次
            /*if ($report_code!==200 && $reply_info[0]['errmsg']!='MK:0010') {
                $log = $model->getJournalList($reply_info[0]['mobile'],'','','', self::PLATFORMID, $reply_info[0]['sid']);
                $vcome  = new VComSms();
                $result = $vcome->doSendSMS($reply_info[0]['mobile'], $log[0]['smstxt'], '');
                pft_log('sms/qcloud_notify', "用户接收失败，重发短信|sid:{$reply_info[0]['sid']},重发结果:{$result['code']}");
            }*/
        }
        echo '0';
    }

    public function yx()
    {
        $data = file_get_contents('php://input');
        if (empty($data)) {
            exit('empty request');
        }
        pft_log('sms/yx_notify', $data);
        $reply_info = json_decode($data, true);
        $reply_info = $reply_info[0];
        if ($reply_info && isset($reply_info['requestId'])) {
            $model       = new SmsJournal();
            $report_code = $reply_info['sendSts'] == '1' ? 200 : 0;
            $msg         = "report_status:{$reply_info['sendSts']}|errmsg:云讯的没有错误备注|分配的号码:{$reply_info['mobileList']}";
            $res         = $model->UpdateJournal($reply_info['mobileList'], $reply_info['requestId'], $report_code,
                $msg, strtotime($reply_info['sendTime']));
            if ($res === false) {
                pft_log('sms/yx_error', 'sid:' . $reply_info['requestId']);
            }
            echo '200';
            exit;
            //客户接收失败了，使用即时通渠道重发一次
            /*if ($report_code!==200 && $reply_info[0]['errmsg']!='MK:0010') {
                $log = $model->getJournalList($reply_info[0]['mobile'],'','','', self::PLATFORMID, $reply_info[0]['sid']);
                $vcome  = new VComSms();
                $result = $vcome->doSendSMS($reply_info[0]['mobile'], $log[0]['smstxt'], '');
                pft_log('sms/qcloud_notify', "用户接收失败，重发短信|sid:{$reply_info[0]['sid']},重发结果:{$result['code']}");
            }*/
        }
        echo 0;
    }

    /**
     * 创蓝状态报告回调
     * <AUTHOR>
     * @date 2021/10/22
     *
     * @return array
     */
    public function chuangLand()
    {
        $replyInfo = $_GET;
        if (empty($replyInfo)) {
            echo '{"clcode":"111111"}';exit;
        }

        if (empty($replyInfo['msgid'])) {
            echo '{"clcode":"111111"}';exit;
        }

        if ($replyInfo['receiver'] != 'pft12301' || $replyInfo['pswd'] != 'kcdnsicnisenc') {
            echo '{"clcode":"111111"}';exit;
        }

        unset($replyInfo['receiver']);
        unset($replyInfo['pswd']);
        pft_log('SmsReportNotice/chuangLand/debug', json_encode($replyInfo, JSON_UNESCAPED_UNICODE));

        $data = [
            'action' => 'chuangLand',
            'data'   => $replyInfo,
        ];

        Queue::push('sync', 'SmsReportNotice_Job', $data);

        echo '{"clcode":"000000"}';exit;
    }
}