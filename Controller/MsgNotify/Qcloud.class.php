<?php
namespace Controller\MsgNotify;
use Library\MessageNotify\OrderNotify;
use Library\MessageNotify\VComSms;
use Model\Order\SmsJournal;

/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 02-14-2017
 * Time: 11:44
 *
 * 腾讯云短信回调通知
 */

class Qcloud
{
    const PLATFORMID = 1;
    /**
     * 获取短信状态报告
     */
    public function notify()
    {
        /*[{
        "mobile":"18750193275",
        "report_status":"SUCCESS",
        "description":"用户短信接收成功",
        "errmsg":"DELIVRD",
        "user_receive_time":"2017-02-14 18:17:29",
        "sid":"8:r67PrBGUaBrGnFiNbGG20170214",
        "nationcode":"86"
        }]*/
        $data = file_get_contents('php://input');
        if (empty($data)) exit('empty request');
        pft_log('sms/qcloud_notify', $data);
        $reply_info = json_decode($data, true);
        if ($reply_info && isset($reply_info[0]['sid'])) {
            $model = new SmsJournal();
            $report_code = $reply_info[0]['report_status']=='SUCCESS' ? 200 : 0;
            $msg = "report_status:{$reply_info[0]['report_status']}|errmsg:{$reply_info[0]['errmsg']}|描述:{$reply_info[0]['description']}";
            $res = $model->UpdateJournal($reply_info[0]['mobile'], $reply_info[0]['sid'], $report_code, $msg, strtotime($reply_info[0]['user_receive_time']));
            if ($res === false ) pft_log('sms/qcloud_notify', 'sid:' . $reply_info[0]['sid']);
            echo '200';
            //客户接收失败了，使用即时通渠道重发一次
            /*if ($report_code!==200 && $reply_info[0]['errmsg']!='MK:0010') {
                $log = $model->getJournalList($reply_info[0]['mobile'],'','','', self::PLATFORMID, $reply_info[0]['sid']);
                $vcome  = new VComSms();
                $result = $vcome->doSendSMS($reply_info[0]['mobile'], $log[0]['smstxt'], '');
                pft_log('sms/qcloud_notify', "用户接收失败，重发短信|sid:{$reply_info[0]['sid']},重发结果:{$result['code']}");
            }*/
        }
        echo '0';
    }

    /**
     * 上行回复
     */
    public function reply()
    {
        pft_log('sms/qcloud_reply', json_encode($_GET));
        echo '0';
    }
}