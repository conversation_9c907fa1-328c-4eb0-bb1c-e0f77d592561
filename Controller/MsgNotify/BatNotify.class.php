<?php
/**
 * 批量消息推送
 * <AUTHOR>
 * @time   2017-04-25
 */
namespace Controller\MsgNotify;

use Library\Controller as Controller;
use Library\Resque\Queue as Queue;
use Library\Cache\Cache as Cache;
use Library\Tools\Helpers;
use Process\Resource\Qiniu as Qiniu;
use Model\Product\MemberCard as MemberCard;

class BatNotify extends Controller
{
    private $_fid           = 0;                 // 发布者的用户ID
    private $_form          = [];                // 表单数据
    private $_maxNum        = 500;               // 表格导入消息数量上限
    private $_wxChannel     = 0;                 // 微信渠道 [0关闭，1开启]
    private $_smsChannel    = 0;                 // 短信渠道 [0关闭，1开启]
    private $_nowOperate    = '';                // 当前操作 ['add'新增|'edit'编辑]
    private $_allowMsgType  = [0,1,2];           // 可用消息类型
    private $_allowOperate  = ['add','edit'];    // 操作限制

    // 不同状态的查询条件
    private $_statusMap = [
        1 => ['status' => ['IN',[0,1]]],
        2 => ['send_sms' => ['IN',[0,2]], 'send_wx' => ['IN',[0,2]]],
        3 => ['_string' => 'send_sms=3 or send_wx=3']
    ];

    public function __construct()
    {
        $this->_getLoginInfo();
    }

    /**
     * 批量推送任务列表 [兼容搜索功能]
     * <AUTHOR>
     * @date   2017-05-11
     * 
     * @param  string $begin_time 任务发布开始时间 2017-01-01 10:00:00
     * @param  string $end_time   任务发布结束时间 2017-01-30 10:00:00
     * @param  int    $msg_type   消息类型[-1不限]
     * @param  int    $status     消息状态[-1不限]
     * @param  int    $now_id     当前页末条ID
     * @param  int    $size       每页条数
     * @param  int    $title      消息名称
     * @return
     */
    public function getPushTaskList()
    {
        // 参数过滤
        $beginTime = I('begin_time', 0, 'strtotime');   // 发布时间
        $endTime   = I('end_time', 0, 'strtotime');     // 发布时间
        $msgType   = I('msg_type', -1, 'intval');       // 消息类型[-1不限]
        $status    = I('status', -1, 'intval');         // 消息状态[-1不限]
        $nowId     = I('now_id', 0, 'intval');          // 当前页末条ID
        $pageSize  = I('size', 10, 'intval');           // 每页条数
        $title     = I('title', '','trim');             // 消息名称

        // 搜索条件
        $where = [
            'fid'   => $this->_fid,
            'type'  => ['IN', [3,4]]
        ];

        // 跳过已查询任务
        if ($nowId) {
            $where['id'] = ['lt', $nowId];
        }

        // 发布日期
        if ($beginTime && $endTime) {

            if ($beginTime == $endTime) {
                $endTime = $beginTime + 86400;
            } elseif ($beginTime > $endTime) {
                $this->apiReturn(201, [], '查询时间段不合法');
            } else {
                $endTime += 86400;
            }

            $where['create_time'] = ['between', [$beginTime, $endTime]];
        } elseif ($beginTime) {
            $where['create_time'] = ['gt', $beginTime];
        } elseif ($endTime) {
            $where['create_time'] = ['lt', $endTime];
        }

        // 消息状态
        if (in_array($status, [0,1,2])) {
            $where['status'] = $status;
        }

        // 任务列表
        $taskModel = new \Model\Product\Task();
        $field     = 'id,fid,type,data,status,create_time';
        $order     = 'id desc';
        $taskArr   = $taskModel->getUserTask($where, $field, '', $order);
        if (!$taskArr) {
            $this->apiReturn(200, ['list'=>[],'now_id'=>0], '没有更多发布信息');
        }

        // 进一步筛选并分页
        $i      = 0;
        $nowId  = 0;  // 当前任务ID
        $list   = []; // 任务列表
        foreach ($taskArr as $task) {

            // 异常记录判断
            if (!trim($task['data'])) {
                continue;
            }

            // 解析任务内容
           $data = $this->_parseTaskData($task['data']);

           // 消息类型过滤
           if (in_array($msgType, $this->_allowMsgType)) {
                if ($data['msgType'] != $msgType) {
                    continue;
                }
           }

           // 消息名称过滤
           if ($title) {
               if (!$data['title'] || false === stripos($data['title'], $title)) {
                    continue;
               }
           }

           // 记录数量
           if ($i >= $pageSize) {
                continue;
           }

           // 整理任务数据
           $list[] = [
               'id'         => $task['id'],         // 任务ID
               'type'       => $task['type'],       // 任务类型
               'status'     => $task['status'],     // 任务状态
               'msg_type'   => $data['msgType'],    // 消息类型
               'channel'    => $data['channel'],    // 推送渠道
               'send_type'  => $data['sendType'],   // 推送方式
               'title'      => $this->_parseContent($data['title'],10),   // 消息名称
               'content'    => $this->_parseContent($data['content']),    // 消息内容
               'date'       => date('Y-m-d H:i:s', $task['create_time']), // 创建时间
           ];

           $i++;
        }

        if ($list) {
            // 获取当前页面最早的任务ID
            $lastDate = end($list);
            $nowId    = $lastDate['id'];
        }

        // 返回数据
        $return = [
            'list'   => $list,
            'now_id' => $nowId
        ];

        $this->apiReturn(200, $return, '');
    }

    /**
     * 发布任务
     * <AUTHOR>
     * @time   2017-04-25
     */
    public function addSendTask()
    {
        set_time_limit(60*3);

        $this->_nowOperate = 'add';

        // 表单数据
        $this->_getForm();

        // 验证表单数据
        $this->_checkForm($this->_form);

        // 重复提交校验
        $this->_checkResubmit($this->_form);

        // 接收类型
        if ($this->_form['recType'] == 1) { // 指定会员，从EXCEL表导入 [通用/生日]

            $taskType    = 3; // 定时

            // EXCEL表格数据处理
            $file        = $this->_checkAndGetFile($this->_form['excelUrl']);
            $excelData   = $this->_parseExcelFile($file);
            $validData   = $this->_checkExcelFileData($excelData);

            // 平台用户
            $platMsg     = $this->_readyPlatUserMsg($validData['userIdArr'], $validData['excelMsgArr']);

            // 非平台用户
            $extMsg      = $this->_readyExcelSmsMsg($validData['excelMsgArr']['sms']);

            // 有效消息数组
            $msgDataArr  = array_merge($platMsg, $extMsg);

            if (!$msgDataArr) {
                $this->apiReturn(201, [], '表格中没有筛选出有效数据');
            }

        } else {  // 系统自动筛选会员 [生日/礼券]
            $taskType = 4; // 定日期
        }

        // 添加任务
        $this->_addBatchSendTask(
                $this->_fid,
                $this->_form,
                $taskType,
                $this->_form['saveType'],
                $this->_form['recType'],
                $msgDataArr
            );
    }

    /**
     * 任务开启或关闭
     * <AUTHOR>
     * @date   2017-05-17
     * 
     * @param  string $do 任务开关 ['on'开启|'off'关闭]
     * @param  int    $id 任务ID
     */
    public function editTaskStatus()
    {
        $switch = I('do', '', 'trim');
        $taskId = I('id', -1, 'intval');

        if ($taskId <= 0) {
            $this->apiReturn(201, [], '请选择指定任务');
        }

        if (!in_array($switch, ['on', 'off'])) {
            $this->apiReturn(201, [], '请指定操作');
        }

        $taskModel    = $this->_getNoticeTaskModel();
        $changeResult = $taskModel->updateBatSendTaskStatus($taskId, $switch);  

        $msg = $switch == 'on' ? '任务开启' : '任务关闭';

        if (!$changeResult) {
            $this->apiReturn(201, [], $msg . '失败');
        }

        $this->apiReturn(200, [], $msg . '成功');
    }

    /**
     * 任务详情
     * <AUTHOR>
     * @date   2017-05-17
     */
    public function getTaskInfo()
    {
        $taskId = I('id', -1, 'intval'); // 任务ID

        if ($taskId <= 0) {
            $this->apiReturn(201, [], '请选择要编辑的消息');
        }

        // 任务记录返回前端
        $taskModel = new \Model\Product\Task();
        $taskData  = $taskModel->getUserTask(['id'=>$taskId], 'data,status,id');
        if (!trim($taskData[0]['data'])) {
            $this->apiReturn(201, [], '未找到内容');
        }

        // 解析内容
        $info = json_decode($taskData[0]['data'], true);
        if (!isset($info['title'])) {
            $this->apiReturn(201, [], '内容解析有误, 可能您选择的不是消息推送任务');
        }

        // 增加任务状态
        $info['status'] = $taskData[0]['status'];
        $info['id']     = $taskData[0]['id'];

        // 返回数据
        $return = [
            'info' => $info,
        ];

        $this->apiReturn(200, $return, '');
    }

    /**
     * 编辑任务
     * <AUTHOR>
     * @date   2017-05-17
     * 
     * @param int id 任务ID
     */
    public function editTask()
    {
        $this->_nowOperate = 'edit';

        $this->_getForm();
        $this->_checkForm($this->_form);

        $taskModel  = new \Model\Product\Task();
        $taskDModel = $this->_getNoticeTaskModel();

        if ($this->_form['sendType'] == 2) { // 动态推送

            // 判断是否可以编辑 - 任务表
            $where = [
                'id' => $this->_form['taskId']
            ];
            $res = $taskModel->getUserTask($where, 'last_run_time, data');
            if ($res[0]['last_run_time']) {
                $this->apiReturn(201, [], '任务已开始执行，不允许编辑');
            }

            // 解析原任务内容
            $data = $this->_parseTaskData($res[0]['data']);
            if (!$data) {
                $this->apiReturn(201, [], '原任务内容不存在！');
            }

            unset($this->_form['taskId']);
            $newForm = array_merge($data, $this->_form);

            // 编辑
            $task = [
                'data' => json_encode($newForm, JSON_UNESCAPED_UNICODE)
            ];
            $res = $taskModel->updateUserTask($where, $task);

        } else {  // 定时推送

            $taskId = $this->_form['taskId'];

            // 判断是否可以编辑 - 消息表
            $where = [
                'task_id' => $taskId,
                'status'  => ['IN', [2,3]]  // 运行中3|已完成2
            ];
            $res = $taskDModel->getBatSendTaskDetail($where, 'id', 1);
            if ($res) {
                $this->apiReturn(201, [], '任务已开始执行，不允许编辑');
            }

            // 解析原任务表的任务内容
            $where = [
                'id' => $taskId
            ];
            $res  = $taskModel->getUserTask($where, 'data');
            $data = $this->_parseTaskData($res[0]['data']);
            if (!$data) {
                $this->apiReturn(201, [], '原任务内容不存在！');
            }

            unset($this->_form['taskId']);
            $newForm = array_merge($data, $this->_form);

            // 编辑
            $task = [
                'data' => json_encode($newForm, JSON_UNESCAPED_UNICODE)
            ];
            $taskDetail = [
                'send_time' => $this->_getSendTime(),
                'level'     => $this->_form['sendType'],
            ];
            $res = $taskDModel->updateBatchSendTaskData($taskId, $task, $taskDetail);
        }

        if ($res) {
            $this->apiReturn(200, [], '编辑成功');
        }
        $this->apiReturn(201, [], '编辑失败');
    }

    /**
     * 获得指定任务的消息推送详情 [兼容搜索和导出功能]
     * <AUTHOR>
     * @date   2017-05-20
     * 
     * @param  int    $id      任务ID
     * @param  int    $size    每页条数
     * @param  int    $now_id  当前页末条ID
     * @param  int    $status  消息状态[推送成功2推送失败3未推送1]
     * @param  int    $export  导出 [1导出|0列表]
     * @param  string $date    推送日期 [2017-01-01]
     * @param  string $name    会员名称或会员ID
     * @return
     */
    public function getPushMsgList()
    {
        $this->_getSearchForm();
        $this->_checkSearchForm();
        $map = $this->_getSearchCondition();

        $mModel = $this->_getMemberModel();
        $tModel = $this->_getNoticeTaskModel();

        // 任务详情
        $taskData = $this->_getSearchTaskData($this->_form['taskId']);
        $title    = $taskData['title'] ?: '';

        // 消息列表
        $list = $this->_getSearchMsgList(
                array_merge($map, $this->_statusMap[$this->_form['status']]), 
                $this->_form['pageSize'],
                $this->_form['status']
            );

        if ($this->_form['export']) {
            // 导出EXCEL
            $fileName = $this->_getExcelFileName($title);
            array_unshift($list, ['id', '会员名称', '短信', '微信']);
            $this->excelReturn($fileName, 0, $list);
            die;

        } else {
            // 数据统计
            if ($this->_form['nowId'] == 0) {
                unset($map['id']);
                if (isset($map['fid']) && $map['fid'] == 'nofind') {
                    $successCount = $failCount = $waitCount = 0;
                } else {
                    // 推送成功
                    $successCount = $tModel->countBatSendTaskDetail(
                            array_merge($map, $this->_statusMap[2])
                        );
                    // 推送失败
                    $failCount = $tModel->countBatSendTaskDetail(
                            array_merge($map, $this->_statusMap[3])
                        );
                    // 未推送
                    $waitCount = $tModel->countBatSendTaskDetail(
                            array_merge($map, $this->_statusMap[1])
                        );
                }
            }

            if ($list) {
                $nowId = end($list)['id'] ?: 0;
            }

            $return = [
                'list'    => $list,
                'success' => $successCount ?: 0,
                'fail'    => $failCount ?: 0,
                'wait'    => $waitCount ?: 0,
                'title'   => $title ?: '',
                'now_id'  => $nowId ?: 0
            ];

            $this->apiReturn(200, $return, '');
        }
    }

    /**
     * 获取表单数据
     * <AUTHOR>
     * @time   2017-04-25
     *
     * @param string $type 表单类型 ['add'新增|'edit'编辑]
     */
    private function _getForm()
    {
        $this->_checkOperate();

        if ($this->_nowOperate == 'edit') {
            $form['taskId'] = I('id', -1, 'intval');  // 任务ID
        } else {
            $form['recType']  = I('rec_type', -1, 'intval');   // 接收类型[0系统自动筛选1指定会员]
            $form['excelUrl'] = I('excel', '', 'trim');        // xls的URL地址
        }

        $form['title']      = I('title', '', 'trim');        // 消息名称
        $form['content']    = I('content', '', 'trim');      // 消息内容[不包括模板]
        $form['channel']    = I('channel', '');              // 推送渠道[0微信1短信(默认0)可多选]
        $form['msgType']    = I('msg_type', -1, 'intval');   // 消息类型[0通用1生日2礼券]
        $form['sendType']   = I('send_type', -1, 'intval');  // 推送类型[0立即推送1定时推送2动态推送]
        $form['sendTime']   = I('send_time', -1);            // 发送时间[立即推送0，定时推送日期精确到分，动态推送单位天数]
        $form['saveType']   = I('save_type', -1, 'intval');  // 保存类型[0保存1保存并执行]

        $this->_form = $form;
        return true;
    }

    /**
     * 操作权限校验
     * <AUTHOR>
     * @time   2017-05-18
     */
    private function _checkOperate()
    {
        if (!in_array($this->_nowOperate, $this->_allowOperate)) {
            $this->apiReturn(201, [], '操作有误');
        }
        return true;
    }

    /**
     * 检测相同表单重复提交
     * <AUTHOR>
     * @time   2017-04-25
     */
    private function _checkResubmit($form = [])
    {
        if (!$form || !is_array($form)) {
            $this->apiReturn(201, [], '清提交任务内容');
        }

        // 留下同属性的表单
        unset($form['saveType']);

        $redis  = $this->getRedisObject();
        $key    = md5(json_encode($form));
        $lock   = $redis->lock($key, 1, 60);

        if (!$lock) {
            $this->apiReturn(201, [], '请勿重复提交相同任务');
        }
    }

    /**
     * 表单数据验证
     * <AUTHOR>
     * @time   2017-04-25
     */
    private function _checkForm($form = [])
    {
        $this->_checkOperate();

        // 消息名称
        if (empty($form['title'])) {
            $this->apiReturn(201, [], '请填写消息名称');
        }

        // 消息类型[0通用1生日2礼券]
        if (!in_array($form['msgType'], $this->_allowMsgType)) {
            $this->apiReturn(201, [], '消息类型非法');
        }

        // 推送渠道 [0微信1短信(默认0)可多选]
        if (empty($form['channel']) || !is_array($form['channel'])) {
            $this->apiReturn(201, [], '推送渠道非法');
        }

        if (in_array(0, $form['channel'])) {
            $this->_wxChannel = 1;
        }

        if (in_array(1, $form['channel'])) {
            $this->_smsChannel = 1;
        }

        // 推送类型[0立即推送1定时推送2动态推送]
        if (!in_array($form['sendType'], [0,1,2])) {
            $this->apiReturn(201, [], '推送类型非法');
        }

        // 定时推送
        if ($form['sendType'] == 1) {
            $delayTime = 60 * 30; // 30分钟后才能发送
            if (strtotime($form['sendTime']) <= time() + $delayTime) {
                $this->apiReturn(201, [], '定时推送时间请设置30分钟后');
            }
        }

        // 动态推送
        if ($form['sendType'] == 2) {
            if (!is_numeric($form['sendTime']) || $form['sendTime'] < 1) {
                $this->apiReturn(201, [], '动态推送天数请设置1以上的整数');
            }
        }

        // 保存类型[0保存1保存并执行]
        if (!in_array($form['saveType'], [0,1])) {
            $this->apiReturn(201, [], '保存类型非法');
        }

        // 非礼券类型，则必须填写内容
        if ($form['msgType'] != 2 && !$form['content']) {
            $this->apiReturn(201, [], '请填写消息内容');
        }

        // 内容字数限制
        if ($form['content']) {
            $this->_limitContentLength($form['content']);
        }

        if ($this->_nowOperate == 'add') {
            // 接收类型[0系统自动筛选1指定会员]
            if (!in_array($form['recType'], [0,1])) {
                $this->apiReturn(201, [], '接收类型非法');
            }

            // 指定会员
            if ($form['recType'] == 1) {
                if (empty($this->_form['excelUrl'])) {
                    $this->apiReturn(201, [], '请上传指定会员的xls表格');
                }
            }

        } elseif ($this->_nowOperate == 'edit') {
            if ($form['taskId'] <= 0) {
                $this->apiReturn(201, [], '请选择要编辑的消息');
            }
        }

        return true;
    }

    /**
     * 文件类型校验和获取, 返回本地文件暂存路径
     * <AUTHOR>
     * @time   2017-04-25
     */
    private function _checkAndGetFile($fileUrl = '', $suffix = ['xls'])
    {
        if (!$fileUrl || !is_array($suffix) || !$suffix) {
            $this->apiReturn(201, [], '文件检验参数有误');
        }

        $fileInfo = pathinfo($fileUrl);

        // 文件类型校验
        if (!in_array($fileInfo['extension'], $suffix)) {
            $this->apiReturn(201, [], '请上传后缀是 '.implode('或', $suffix).' 的文件');
        }

        $tmpFile    = '/tmp/' . $fileInfo['basename'] ?: time();
        $fileStream = file_get_contents($fileUrl);
        if (!$fileStream) {
            $this->apiReturn(201, [], '文件获取失败');
        }

        $res = file_put_contents($tmpFile, $fileStream);
        if ($res === false) {
            $this->apiReturn(201, [], '文件转换失败');
        }

        return $tmpFile ?: '';
    }

    /**
     * 解析Excel文件
     * <AUTHOR>
     * @time   2017-04-25
     */
    private function _parseExcelFile($file = '')
    {
        if (!$file){
            $this->apiReturn(201, [], '文件参数有误');
        }
        Helpers::composerAutoload();
        // 文件校验
        //$objPHPExcel = load_excel();
        $objWriter = new \PHPExcel_Reader_Excel5();
        if (!$objWriter->canRead($file)) {
            $this->apiReturn(201, [], '文件读取失败');
        }

        // 辅助工具
        Helpers::composerAutoload();
        //$class = HTML_DIR . '/Service/Library/Business/PHPExcel/Cell.php';
        //if (!file_exists($class)) {
        //    $this->apiReturn(201, [], '辅助文件不存在');
        //} else {
        //    include_once $class;
        //}

        // 获取数据
        $PHPExcel      = $objWriter->load($file); 
        $sheet         = $PHPExcel->getSheet(0);
        $maxRowNum     = $sheet->getHighestRow();    // 总行数
        $maxColummNum  = $sheet->getHighestColumn(); // 总列数
        $maxColummNum  = \PHPExcel_Cell::columnIndexFromString($maxColummNum); 

        $dataArr = [];
        for ($column = 0; $column < $maxColummNum; $column++) {
            for ($row = 1; $row <= $maxRowNum; $row++){
                $dataArr[$row][] = $sheet->getCellByColumnAndRow($column, $row)->getValue();
            }
        }

        // 删除临时文件
        unlink($file);

        return $dataArr ?: [];
    }

    /**
     * 校验Excel文件内容
     * [用户ID, 会员卡号,内容,手机号, 昵称,...] 
     * <AUTHOR>
     * @time   2017-04-25
     *
     * @return 返回有效的用户ID数组 和 每条消息的单独数据
     */
    private function _checkExcelFileData($dataArr = [])
    {
        if (!$dataArr) {
            $this->apiReturn(201, [], '上传文件有效内容为空');
        }

        // 数据分类
        $titleArr  = array_shift($dataArr); // 标题数组
        $msgArr    = $dataArr;              // 内容数组

        // 空表检测
        if (empty($titleArr[0]) || empty($msgArr)) {
            $this->apiReturn(201, [], '上传文件内容为空，请确认上传文件内容是否正确！');
        }

        // 发送数量限制
        if (count($msgArr) > $this->_maxNum + 20) {
            $this->apiReturn(201, [], '发送消息 单个excel不能多于' . $this->_maxNum . '条数据');
        }

        $memberCardModel = new \Model\Product\MemberCard();

        // 过滤手机格式
        $length = count($msgArr);
        for ($i=0; $i < $length; $i++) { 
            $tel = $msgArr[$i][2];
            if ($tel && mb_strlen($tel) != 11) {
                $msgArr[$i][2] = $this->_filterMobileFormat($tel);
            }
        }

        // 根据会员卡号批量获取会员卡用户信息
        $cardNoArr   = array_column($msgArr, 1);
        if (count($cardNoArr)) {
            $cardNoArr   = array_unique($cardNoArr);

            // 空值过滤 和 类型校验
            foreach ($cardNoArr as $k1 => $v1) {
                if (!is_numeric($v1) || $v1 <= 0 || !$v1) {
                    unset($cardNoArr[$k1]);
                }
            }

            $cardNoArr = array_values($cardNoArr);
            if ($cardNoArr) {
                // 会员ID查询
                $cardInfoMap = $memberCardModel->getBatchMemberCardInfo(
                    $this->_fid, ['card_no' => ['in', $cardNoArr]], true, 'card_no'
                );

                if ($cardInfoMap && is_array($cardInfoMap)) {
                    foreach ($cardInfoMap as $cardNo => $cardInfo) {
                        if ($cardInfo['memberID']) {
                            $cardnoUseridMap[$cardNo] = $cardInfo['memberID'];
                        }
                    }
                }
            }
        }

        // 根据手机号批量获取会员卡用户信息
        $mobileArr  = array_column($msgArr, 2);
        if (count($mobileArr)) {
            $mobileArr = array_unique($mobileArr);

            // 过滤无效手机号
            foreach ($mobileArr as $k3 => $v3) {
                if (!$v3 || !ismobile($v3)) {
                    unset($mobileArr[$k3]);
                }
            }

            $mobileArr = array_values($mobileArr);
            if ($mobileArr) {
                // 会员ID查询
                $cardInfoMap = $memberCardModel->getBatCardInfoByMobile(
                    $this->_fid, $mobileArr, true
                );

                if ($cardInfoMap && is_array($cardInfoMap)) {
                    foreach ($cardInfoMap as $cardMobile => $cardMemberId) {
                        if ($cardMemberId) {
                            $cardnoUseridMap[$cardMobile] = $cardMemberId;
                        }
                    }
                }
            }
        }


        // 会员手机号 > 会员卡 > 用户ID
        $userIdArr   = []; // 平台用户ID
        $excelMsgArr = []; // 非平台用户信息
        foreach ($msgArr as $key => $msg) {

            $userId  = intval($msg[0]) ?: 0;                // 用户ID 
            $cardNo  = trim($msg[1]) ?: '';                 // 会员卡号   [优先于用户ID]
            $mobile  = trim($msg[2]) ?: '';                 // 会员手机号 [优先于会员卡号]
            $content = trim($msg[3]) ?: '';                 // 内容
            $landName= trim($msg[4]) ?: '';
            $useTime = trim($msg[5]) ?: '';
            // 存在会员卡 进行ID替换
            if ($cardNo && $cardnoUseridMap[$cardNo]){
                $userId = $cardnoUseridMap[$cardNo];
            }

            // 会员手机号优先 进行ID替换
            if ($mobile && ismobile($mobile) && $cardnoUseridMap[$mobile]) {
                $userId = $cardnoUseridMap[$mobile];
            }

            // 用户ID校验 如果excel中有多行是发给同一个人，则只发送最后的那条！
            if (is_numeric($userId) && $userId>0) {

                // 记录平台用户信息
                $excelMsgArr[$userId] = [
                    'fid'     => $userId,
                    'cardNo'  => $cardNo,
                    'mobile'  => $mobile,
                    'content' => $content,
                    'landName'=> $landName,
                    'useTime' => $useTime
                ];

                // 保存有效用户ID
                if (!in_array($userId, $userIdArr)) {
                    $userIdArr[] = $userId;
                }
                continue;
            }

            // 非平台用户, 设置短信渠道 并 指定手机号 
            if ($this->_smsChannel && $mobile && ismobile($mobile)){
                // 记录非平台用户信息
                $excelMsgArr['sms'][] = [
                    'mobile'  => $mobile,
                    'content' => $content // excel内容优先于模板内容
                ];
            }

        }

        if (!$userIdArr && !$excelMsgArr) {
            $this->apiReturn(201, [], '表格中未发现有效数据');
        }

        $return = [
            'userIdArr'     => $userIdArr,
            'excelMsgArr'   => $excelMsgArr
        ];

        return $return;
    }

    /**
     * 过滤表格中的手机号格式
     * <AUTHOR>
     * @time   2017-08-02
     */
    private function _filterMobileFormat($str = '')
    {
        $mobile = '';
        if ($str && is_string($str)) {
            preg_match('(1\d{10})', $str, $tmp);
            if ($tmp) {
                $mobile = $tmp[0];
            }
        }
        return $mobile;
    }

    /**
     * 限制模板内容长度
     * <AUTHOR>
     * @time   2017-05-31
     */
    private function _limitContentLength($content = '')
    {
        $smsChars  = ['[',']' ,'【', '】']; // 非法短信内容
        $smsMaxLen = 98;  // 短信内容字段不能超过98字
        $wxMaxLen  = 120; // 微信内容字段不能超过120字

        $length = mb_strlen($content);

        if ($this->_smsChannel) {
            if ($length > $smsMaxLen) {
                $this->apiReturn(201, [], '短信消息内容请不要超过98个字(标点符号算一个字)');
            }

            // 短信非法字符检测
            foreach ($smsChars as $char) {
                if (false === stripos($content, $char)) {
                    continue;
                }
                $this->apiReturn(201, [], '短信内容不能包含以下符号:'.implode(',', $smsChars));
            }
        }

        if ($this->_wxChannel && $length > $wxMaxLen) {
            $this->apiReturn(201, [], '微信消息内容请不要超过120个字(标点符号算一个字)');
        }

        return true;
    }

    /**
     * 微信绑定给平台的用户
     * <AUTHOR>
     * @time   2017-04-25
     */
    private function _getWxBindMemberArr($userIdArr = [], $aid = 0)
    {
        if (!is_array($userIdArr) || !$userIdArr || !$aid) {
            $this->apiReturn(201, [], '微信绑定平台的用户参数有误');
        }

        // 微信appid
        $wxOpenModel = new \Model\Wechat\WxOpen();
        $appInfo     = $wxOpenModel->getAppId($aid, 'appid');
        if (!$appInfo) {
            $this->apiReturn(201, [], '您当前登录的平台账户未绑定公众号');
        }

        // 批量获取微信绑定的用户 - 绑定公众号 + 允许接收消息
        $appid = $appInfo['appid'];

        $wxModel   = new \Model\Wechat\WxMember();
        $wxInfoArr = $wxModel->getBatchWxInfo($appid, $userIdArr);
        if (!$wxInfoArr) {
            $this->apiReturn(201, [], '没有绑定的用户信息');
        }

        // 过滤表中各种原因导致的无效绑定信息
        $temp = [];
        foreach ($wxInfoArr as $item) {
            if ($item['fromusername']) {
                $temp[$item['fid']][] = $item['fromusername'];
            }
        }

        // 微信绑定给平台的用户
        $userIdArr = array_keys($temp);

        return $userIdArr ?: [];
    }

    /**
     * 获取有手机号的平台用户
     * <AUTHOR>
     * @time   2017-04-25
     */
    private function _getHasMobilMemberArr($userIdArr = [])
    {
        if (!is_array($userIdArr) || !$userIdArr) {
            $this->apiReturn(201, [], '手机号用户筛选的参数有误');
        }

        // 批量过滤手机号存在的用户
        $memberBiz = new \Business\Member\Member();
        $list = $memberBiz->getMemberInfoByMulti($userIdArr, 'id');
        $res = [];
        foreach ($list as $item) {
            if  ($item['mobile']) {
                $res[] = $item['id'];
            }
        }

        return $res;
    }

    /**
     * 得到发送时间
     * <AUTHOR>
     * @time   2017-04-25
     */
    private function _getSendTime()
    {
        switch ($this->_form['sendType']) {

            case 0: // 立即推送 / 1分钟后开始运行
                $time = time() + 60 * 1;
                break;

            case 1: // 定时推送
                $time = strtotime($this->_form['sendTime']);
                break;
            
            case 2: // 动态推送 / 天
                $time = intval($this->_form['sendTime']);
                if (!$time) {
                    $this->apiReturn(201, [], '动态推送，输入天数有误！');
                }
                break;

            default:
                $this->apiReturn(201, [], '定时推送，时间有误！');
                break;
        }

        return $time ?: 0;
    }

    /**
     * 筛选短信渠道要发送的非平台用户消息
     * <AUTHOR>
     * @time   2017-04-25
     *
     * @param  array $excelMsgArr Excel导入的消息数组
     * @return array 整理好的平台用户消息数组
     */
    private function _readyExcelSmsMsg($excelMsgArr = [])
    {
        if (!$excelMsgArr || !is_array($excelMsgArr)) {
            // 没有额外的短信用户
            return []; 
        }

        if (!$this->_smsChannel) {
            // 没设置短信渠道
            return [];
        }

        $msgDataArr = []; // 返回的消息数组

        foreach ($excelMsgArr as $eMsg) {

            $msgData = [
                'fid'       => 0, // 非平台用户
                'aid'       => $this->_fid,
                'level'     => $this->_form['sendType'],
                'status'    => $this->_form['saveType'],
                'msg_type'  => $this->_form['msgType'],
                'send_time' => $this->_getSendTime(),
                'data'      => ismobile($eMsg['mobile']) ? json_encode($eMsg,JSON_UNESCAPED_UNICODE) : '',
                'send_wx'   => 0,
                'send_sms'  => 1,
            ];

            $msgDataArr[] = $msgData;
        }

        return $msgDataArr ?: [];
    }
    
    /**
     * 筛选各渠道要发送的平台用户消息
     * <AUTHOR>
     * @time   2017-04-25
     *
     * @param  array $userIdArr   用户ID数组
     * @param  array $excelMsgArr Excel导入的消息数组
     * @return array 整理好的平台用户消息数组
     */
    private function _readyPlatUserMsg($userIdArr = [], $excelMsgArr = [])
    {
        if (!$userIdArr || !is_array($userIdArr)) {
            return [];
        }

        // 微信 - 微信发送给平台的用户
        if ($this->_wxChannel) {
           $wxValidIdArr = $this->_getWxBindMemberArr($userIdArr, $this->_fid);
        }

        // 短信 - 不限用户，只要有手机号就可以发送
        if ($this->_smsChannel) {
           $smsValidIdArr = $this->_getHasMobilMemberArr($userIdArr);
        }

        if (!$wxValidIdArr && !$smsValidIdArr) {
            $this->apiReturn(201, [], '没有找到符合发送条件的用户');
        }

        $msgDataArr = []; // 返回的消息数组

        foreach ($userIdArr as $fid) {

            $msgData = [
                'fid'       => $fid,
                'aid'       => $this->_fid,
                'level'     => $this->_form['sendType'],
                'status'    => $this->_form['saveType'],
                'msg_type'  => $this->_form['msgType'],
                'send_time' => $this->_getSendTime(),
                'data'      => $excelMsgArr[$fid] ? json_encode($excelMsgArr[$fid],JSON_UNESCAPED_UNICODE) : '',
                'send_wx'   => 0,
                'send_sms'  => 0,
            ];

            // 发微信
            if ($wxValidIdArr && in_array($fid, $wxValidIdArr)) {
                $msgData['send_wx']  = 1;
            }

            // 发短信
            if ($smsValidIdArr && in_array($fid, $smsValidIdArr)) {
                $msgData['send_sms'] = 1;
            }

            // 跳过不合法的渠道数据
            if (!$msgData['send_wx'] && !$msgData['send_sms']) {
                continue;
            }

            $msgDataArr[] = $msgData;
        }

        return $msgDataArr ?: [];
    }

    /**
     * 增加批量推送任务
     * <AUTHOR>
     * @date   2017-04-26
     * 
     * @param  int    $fid          任务发布者ID
     * @param  array  $form         任务表单内容 
     * @param  int    $taskType     任务类型 [批量推送定时任务3|批量推送任务提前几日4]
     * @param  int    $taskStatus   任务状态 [0关闭 1启动]
     * @param  int    $recType      接收类型 [0系统自动筛选 1指定会员]
     * @param  array  $msgDataArr   已整理好的任务消息集合 
     * @return mixed  [201添加失败|200添加成功]
     */
    private function _addBatchSendTask($fid = 0, $form = [], $taskType = 3, $taskStatus = 0, $recType = 0, $msgDataArr = [])
    {
        if (!$fid || 
            !$form || 
            !in_array($taskType, [3,4]) || 
            !in_array($taskStatus, [0,1]) || 
            !in_array($recType, [0,1])) 
        {
            $this->apiReturn(201, [], '参数有误');
        }

        // 任务
        $task = [
            'fid'          =>   $fid,
            'create_time'  =>   time(),
            'type'         =>   $taskType,
            'status'       =>   $taskStatus,
            'data'         =>   json_encode($form, JSON_UNESCAPED_UNICODE)
        ];

        if ($recType == 1) {
            // 指定会员
            $taskModel = $this->_getNoticeTaskModel();
            $res = $taskModel->addBatSendTask($task, $msgDataArr);

        } else {
            // 自动筛选
            $taskModel = new \Model\Product\Task();
            $res = $taskModel->addUserTask($task);
        }

        if ($res === -1) {
            $this->apiReturn(201, [], '没有有效的任务信息，请检查数据');

        } elseif ($res) {
            $this->apiReturn(200, [], "批量推送任务添加成功");

        } elseif ($res === false) {
            $this->apiReturn(201, [], '批量推送任务添加失败');

        } else {
            $this->apiReturn(201, [], '未知错误');
        }
    }

    /**
     * 解析任务内容
     * <AUTHOR>
     * @date   2017-04-26
     * 
     * @param  array $taskDataField pft_bat_task表的data字段
     * @return array
     */
    private function _parseTaskData($taskDataField = '')
    {
        if (!$taskDataField || !is_string($taskDataField)) {
            return '';
        }
        $data = json_decode($taskDataField, true);
        return $data ?: '';
    }

    /**
     * 截取指定长度的任务内容
     * <AUTHOR>
     * @date   2017-05-11
     * 
     * @param  string   $content   任务内容
     * @param  int      $maxLength 限制长度
     * @param  string   $suffix    后缀
     * @return 缩写后的任务内容
     */
    private function _parseContent($content = '', $maxLength = 20, $suffix = '......')
    {
        if (!$content || !is_string($content)) {
            return '';
        }

        if (mb_strlen($content, 'utf8') > $maxLength) {
            $content = mb_substr($content, 0, $maxLength) . $suffix;
        }

        return $content;
    }

    /**
     * 根据搜索条件得到导出excel文件名
     * <AUTHOR>
     * @date   2017-05-20
     * 
     * @param  string $title 任务名称
     * @return string 文件名
     */
    private function _getExcelFileName($title = '')
    {
        $fileName = $title;

        if ($this->_form['beginTime']) {
            $fileName .= '_' . date('Y-m-d', $this->_form['beginTime']);
        }

        if ($this->_form['member']) {
            $fileName .= '_' . $this->_form['member'];
        }

        switch ($this->_form['status']) {
            case '2': $fileName .= '_推送成功'; break;
            case '3': $fileName .= '_推送失败'; break;
            case '1': $fileName .= '_未推送'; break;
            default: break;
        }

        return $fileName ?: time();
    }

    /**
     * 得到搜索表单
     * <AUTHOR>
     * @date   2017-05-20
     * 
     * @param  int    $id      任务ID
     * @param  int    $now_id  当前页末条ID
     * @param  int    $status  消息状态[推送成功2推送失败3未推送1]
     * @param  int    $size    每页条数
     * @param  int    $export  导出 [1导出|0列表]
     * @param  string $date    推送日期 [2017-01-01]
     * @param  string $name    会员名称或会员ID
     */
    private function _getSearchForm()
    {
        $form['taskId']    = I('id', 0, 'intval');
        $form['nowId']     = I('now_id', 0, 'intval');
        $form['beginTime'] = I('date', 0, 'strtotime');
        $form['status']    = I('status', 2, 'intval');
        $form['pageSize']  = I('size', 10, 'intval');
        $form['member']    = I('name', '');
        $form['export']    = I('export', 0, 'intval');

        $this->_form = $form;
        return true;
    }

    /**
     * 验证搜索表单
     * <AUTHOR>
     * @date   2017-05-20
     */
    private function _checkSearchForm()
    {
        if (!$this->_form['taskId']) {
            $this->apiReturn(201, [], '请指明任务!');
        }

        if (!in_array($this->_form['status'], [1,2,3])) {
            $this->apiReturn(201, [], '请选择状态!');
        }

        return true;
    }

    /**
     * 组合搜索条件
     * <AUTHOR>
     * @date   2017-05-20
     */
    private function _getSearchCondition()
    {
        // 搜索条件
        $map = [
            'aid'     => $this->_fid,
            'task_id' => $this->_form['taskId']
        ];

        // 指定会员
        $member = $this->_form['member'];
        if ($member) {
            $mModel = $this->_getMemberModel();
            if (is_numeric($member)) {
                $res = $mModel->getMemberInfo($member, 'id', 'id');

            } elseif (is_string($member)) {
                throw new \Exception("查询出现问题");
                // $res = $mModel->getMemberInfo($member, 'dname', 'id');
            }

            $map['fid'] = $res['id'] ?: 'nofind';
        }

        // 发送日期
        $beginTime = $this->_form['beginTime'];
        if ($beginTime) {
            $map['send_time'] = ['between', [$beginTime, $beginTime + 86400]];
        }

        // 导出不分页
        if ($this->_form['export']) {
            $this->_form['pageSize'] = 0;

        } else {
            // 跳过已查询任务
            if ($this->_form['nowId']) {
                $map['id'] = ['lt', $this->_form['nowId']];
            }
        }

        return $map;
    }

    /**
     * 按指定搜索条件得到消息列表
     * <AUTHOR>
     * @date   2017-05-20
     *
     * @param  array  $where    搜索条件
     * @param  int    $pageSize 单页消息数量
     * @param  int    $status   消息状态[推送成功2推送失败3未推送1]
     * @return array
     */
    private function _getSearchMsgList($where = [], $pageSize = 0, $status = 2)
    {
        if (isset($where['fid']) && $where['fid'] == 'nofind') {
            return [];
        }

        // 批量消息
        $tModel = $this->_getNoticeTaskModel();
        $field  = 'id,fid,send_time,send_sms,send_sms_time,send_wx,send_wx_time,comment,data';
        $msgArr = $tModel->getBatSendTaskDetail($where, $field, $pageSize, 'send_time desc');
        if ($msgArr == -1) {
            $this->apiReturn(201, [], '参数错误');

        } elseif (!$msgArr) {
            return [];
        }

        // 批量用户信息
        $allUserId = array_unique(array_column($msgArr, 'fid'));
        if ($allUserId != [0]) {
            $mModel = $this->_getMemberModel();
            $allUserInfo  = $mModel->getMemberInfoByMulti($allUserId, 'id', 'dname,id', true);
        } else {
            $allUserInfo[0] = '非平台会员';
        }

        // 不同状态的数据整理
        $list = $this->_getListByStatus($allUserInfo, $msgArr, $status);

        return $list;
    }

    /**
     * 得到指定任务的表单内容
     * <AUTHOR>
     * @date   2017-05-20
     *
     * @param  int    $taskId   任务ID
     * @return array  ['title'=>xx, ...]
     */
    private function _getSearchTaskData($taskId = 0)
    {
        $taskInfo = (new \Model\Product\Task())->getUserTask(['id' => $taskId], 'data', 1);
        if (!$taskInfo) {
            $this->apiReturn(201, [], '任务不存在');
        }
        $taskData = $this->_parseTaskData($taskInfo[0]['data']);
        if (!$taskData) {
            $this->apiReturn(201, [], '任务内容不存在');
        }
        return $taskData;
    }

    /**
     * 不同状态的列表数据
     * <AUTHOR>
     * @date   2017-05-20
     *
     * @param  array  $allUserInfo 用户信息 ['3385'=>['dname'=>'香菇'], ...]
     * @param  array  $msgArr      消息数组
     * @param  int    $status      消息状态 [推送成功2推送失败3未推送1]
     * @return array
     */
    private function _getListByStatus($allUserInfo = [], $msgArr = [], $status = 2)
    {
        if (!$allUserInfo || !$msgArr || !in_array($status, [1,2,3])) {
            return [];
        }

        $list = [];
        foreach ($msgArr as $msg) {

            $temp = [
                'id'   => $msg['id'],
                'name' => '',
                'sms'  => '',
                'wx'   => ''
            ];

            if ($msg['fid']) {
                $temp['name'] = $allUserInfo[$msg['fid']]['dname'] . '(ID:' . $msg['fid'] . ')';
            } else {
                $taskData     = $this->_parseTaskData($msg['data']);
                $temp['name'] = '非会员:' . $taskData['mobile'];
            }

            if ($status == 1) {
                // 未推送
                $sendTime    = '预定：' . date('Y-m-d H:i:s', $msg['send_time']);
                $temp['sms'] = $msg['send_sms'] ? $sendTime : '';
                $temp['wx']  = $msg['send_wx']  ? $sendTime : '';

            } else {

                // 微信
                if ($msg['send_wx'] == 2)  {
                    $temp['wx'] = date('Y-m-d H:i:s', $msg['send_wx_time']);
                } elseif ($msg['send_wx'] == 3) {
                    $temp['wx']  = '发送失败！';
                }

                // 短信
                if ($msg['send_sms'] == 2)  {
                    $temp['sms'] = date('Y-m-d H:i:s', $msg['send_sms_time']);
                } elseif ($msg['send_sms'] == 3) {
                    $temp['sms']  = '发送失败！' . $msg['comment'];
                }
            }

            $list[] = $temp;
        }

        return $list;
    }

    /**
     * 当前登录信息
     * <AUTHOR>
     * @time   2017-02-20
     *
     * @return 登录用户ID|102 未登录
     */
    private function _getLoginInfo()
    {
        // 登录检测
        $this->_fid = $this->isLogin('ajax');
    }

    /**
     * 获得redis实例
     * <AUTHOR>
     * @time   2017-02-20
     */
    private function getRedisObject()
    {
        static $redis;
        if (!$redis) {
            $redis = Cache::getInstance('redis');
        }
        return $redis;
    }

    /**
     * 获得消息通知模型实例
     * <AUTHOR>
     * @time   2017-05-19
     */
    private function _getNoticeTaskModel()
    {
        static $noticeTask;
        if (!$noticeTask) {
            $noticeTask = new \Model\Notice\Task();
        }
        return $noticeTask;
    }

    /**
     * 获得会员模型实例
     * <AUTHOR>
     * @time   2017-05-19
     */
    private function _getMemberModel()
    {
        static $memberModel;
        if (!$memberModel) {
            $memberModel = new \Model\Member\Member();
        }
        return $memberModel;
    }
}
