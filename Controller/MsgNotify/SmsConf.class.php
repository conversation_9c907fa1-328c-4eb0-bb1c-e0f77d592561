<?php
/**
 * Created by PhpStorm.
 * User: cgp
 * Date: 2017/2/14
 * Time: 22:39
 *
 * 短信系统配置监控等等
 */

namespace Controller\MsgNotify;


use Library\Controller;
use Library\MessageNotify\SmsSystem;
use Model\Order\SmsJournal;

class SmsConf extends Controller
{
    private $smsSystem;
    private $log_path     = '/pft_api_access/sms_conf';
    public function __construct()
    {
        parent::isLogin('ajax');
        $loginInfo = $this->getLoginInfo('ajax', false, false);
        $this->smsSystem = new SmsSystem();
        $action = I('get.a');
        $log_content = "用户ID[{$loginInfo['memberID']}]访问了:$action,ip:" . get_client_ip() . ',请求参数:' . json_encode($_POST, JSON_UNESCAPED_UNICODE);
        pft_log($this->log_path, $log_content,'month');
    }

    /**
     * 获取基础配置信息
     */
    public function getCurConf()
    {
        $sms_ac_conf = load_config('platform_list', 'sms_ac');
        $conf_list = ['platform_list'=>$sms_ac_conf];
        $conf_list['master_platform'] = $this->smsSystem->getPlatformId(0);
        $conf_list['slave_platform']  = $this->smsSystem->getPlatformId(1);
        foreach ($sms_ac_conf as $id=>$name) {
            $conf_list['error_times'][$id]     = $this->smsSystem->getErrorTimes($id);
        }
        return parent::apiReturn(parent::CODE_SUCCESS, $conf_list);
    }

    public function clearErrorTimes()
    {
        $platformid = I('post.platformid');
        if (!$platformid || !is_numeric($platformid)) {
            parent::apiReturn(parent::CODE_INVALID_REQUEST, [], '参数错误');
        }
        $res = $this->smsSystem->clearErrorTimes($platformid);
        if ($res !==false) {
            parent::apiReturn(200);
        }
    }

    public function setPlatformId()
    {
        parent::apiReturn(401);
    }

    public function getSmsJournal()
    {
        $mobile     = I('post.mobile');
        $ordernum   = I('post.ordernum');
        $platformid = I('post.platformid', 1, 'intval');
        $offset     = I('post.offset', 0, 'intval');
        $limit      = I('post.limit', 100, 'intval');
        $sid        = I('post.sid');
        $send_time_begin      = I('post.send_time_begin',0, 'strtotime');
        $send_time_end        = strtotime(I('post.send_time_end') . ' 23:59:59');
        $model      = new SmsJournal();
        $data       = $model->getJournalList($mobile, $ordernum, $send_time_begin, $send_time_end, $platformid, $sid, $offset, $limit);
        parent::apiReturn(parent::CODE_SUCCESS, $data);
    }
}