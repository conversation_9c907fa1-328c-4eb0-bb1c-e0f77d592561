<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: guanpeng
 * Date: 2018/10/16
 * Time: 11:14
 *
 * 对接第三方系统需要回调的通知统一在这个控制器里面实现。
 */

namespace Controller\MsgNotify;

use Library\Tools\YarClient;
use Library\Business\WePay\WxPayNotifyResponse;
use Library\Constants\DingTalkRobots;
use Library\Tools\Helpers;
use Yurun\OAuthLogin\Weixin\OAuth2;

class CallBack
{
    private $_specialDeviceKey = [
        '84E0F4210BEF02F8', //兰州
        '84E0F4210B6E02F8',//长沙
        '84E0F4211C4B02F8', //长沙
    ];
    public function uface()
    {
        pft_log('callback/uface', json_encode($_POST, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));
        $rawData    = str_replace("\\\"","\"",$_POST['data']);//data.replace("\\\"","\"")
        $arrData    = json_decode($rawData, true);
        $faceIdPrefixs = ['VIP','STF'];
        $idNoPrefix = substr($arrData['idNo'],0, 3);

        $deviceKey  = I('post.deviceKey');
        $guid       = I('post.personGuid');
        $faceUrl    = I('post.photoUrl');

        if (!$deviceKey || !$faceUrl || !$arrData['idNo'] || !$arrData['name']) {
            echo '{"code":"400","msg":"error","result":"0"}';die();
        }

        $yarClient = new YarClient('face');
        //指定某些设备进行统计判断
        if (in_array($deviceKey, $this->_specialDeviceKey)) {
            $yarClientFace  = new YarClient('face_meeting');

            //if (defined('ENV') && in_array(ENV, ['DEVELOP', 'LOCAL', 'TEST'])) {
            //    $yarClientFace  = $yarClient;
            //} else {
            //    $yarClientFace  = new YarClient('face_meeting');
            //}

            //用于控制是否插入抽奖表
            $saveStatus     = true;
            if (in_array($idNoPrefix, $faceIdPrefixs)) {
                //是员工或者VIP 则不往抽奖表里面插入数据
                $saveStatus = false;
            }

            //if (strpos($arrData['idNo'], 'STF')!==false) {
            //    //是员工则不往抽奖表里面插入数据
            //    $saveStatus = false;
            //}

            if ($saveStatus) {
                $checkRes1      = $yarClientFace->call('/Face/Raffle/findInfoByFaceId',[$arrData['idNo']]);
                pft_log('callback/raffle', json_encode(['findInfoByFaceId: ', json_encode($checkRes1)]));
                if ($checkRes1['res']['code'] == 200) {
                    //不存在 将该条人脸信息推送到抽奖表中
                    $addRes = $yarClientFace->call('/Face/Raffle/addFaceRaffle', [$arrData['idNo'], $arrData['name'], $faceUrl]);
                    if ($addRes['res']['code'] != 200) {
                        //添加失败后，记录日志 方便后期查询
                        pft_log('callback/raffle', json_encode($addRes));
                    }
                }
            }
        }

        if (in_array($idNoPrefix, $faceIdPrefixs)) {
            $result     = $yarClient->call('Face/Vip/addPassLog',[$arrData['idNo'],$arrData['name'], $guid, $faceUrl, $deviceKey, '', $idNoPrefix]);
        }
        echo '{"code":"200","msg":"success","result":"1"}';
    }

    public function wepayMicropay()
    {
        pft_log('callback/wepayMicropay', json_encode($_POST, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));
        echo '{"code":"200","msg":"success","result":"1"}';
    }

    /**
     * @author: guanpeng
     * @date: 2019/6/7
     */
    public function wechatLogin()
    {
        ini_set('display_errors', 'On');
        Helpers::composerAutoload();
        $wxOAuth = new OAuth2('wx9634cb10cbcfec9c', '318f2b517b7b33784faeda48c9fab89a');
        // 获取accessToken，把之前存储的state传入，会自动判断。获取失败会抛出异常！

        // 调用过getAccessToken方法后也可这么获取accessToken
        //$accessToken = $wxOAuth->accessToken;
        // 这是getAccessToken的api请求返回结果，一般不需要用到
        //$result = $wxOAuth->result;
        // openid，用户在第三方平台的唯一标识
        $openid = $wxOAuth->openid;

        $accessToken = $wxOAuth->getAccessToken($_GET['state'], $_GET['code']);
        // 获取用户资料，第一个参数不传则默认使用getAccessToken方法获取到的结果
        $userInfo = $wxOAuth->getUserInfo($accessToken);
        $token    = md5(substr($userInfo['openid'],0, 8) . substr($userInfo['unionid'],0, 2). date('YmdH'));
        $url = "http://{$_GET['state']}?openid={$userInfo['openid']}&token={$token}&nickname={$userInfo['nickname']}&unionid={$userInfo['unionid']}";
        header('location:' . $url);
        exit;
    }

    /**
     * 微信支付退款回调通知
     *
     * @author: guanpeng
     * @date: 2019/1/29
     * @see https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_16
     */
    public function wepayRefund()
    {
        //pft_log('callback/wepay_refund', file_get_contents("php://input"));
        $notify  = new WxPayNotifyResponse();
        $notify->saveData('callback/wepay_refund');
        $data    = $notify->data['req_info'];
        $appid   = $notify->data['appid'];
        $config  = load_config($appid, 'WePay');

        $key     = $config['key'];
        $decrypt = base64_decode($data, true);
        $decodeData    = openssl_decrypt($decrypt , 'aes-256-ecb', md5($key), OPENSSL_RAW_DATA);
        $refundData    = $notify->xmlToArray($decodeData);
        $msg = "APPID:{$appid} \n";
        $msg .= "商户号:{$notify->data['mch_id']} \n";
        $msg .= "票付通订单号:{$refundData['out_trade_no']} \n";
        $msg .= "微信交易号:{$refundData['transaction_id']} \n";
        $msg .= "总金额:{$refundData['total_fee']} \n";
        $msg .= "退款金额:{$refundData['refund_fee']} 分\n";
        $msg .= "用户收款方式:{$refundData['refund_recv_accout']} \n";
        $msg .= "退款渠道:{$refundData['refund_request_source']} \n";
        $msg .= "退款时间:{$refundData['success_time']} \n";
        Helpers::sendDingTalkGroupRobotMessageRaw($msg, DingTalkRobots::ONLINE_REFUND);
        pft_log('callback/wepay_refunddecrypt', json_encode($refundData, JSON_UNESCAPED_UNICODE));
        echo <<<XML
<xml> 
  <return_code><![CDATA[SUCCESS]]></return_code>
  <return_msg><![CDATA[OK]]></return_msg>
</xml>
XML;

    }

}