<?php
/**
 * User: Fang
 * Time: 11:15 2016/5/25
 */

namespace Controller\MsgNotify;

use Business\Notice\System;
use Library\Controller;
use Library\Exception;
use Business\Member\Member;
use Model\Notice\Announce;

class notice extends Controller
{
    private $_loginInfo = null;
    private $_memberId  = null;
    private $_sid       = null;

    private $_noticeModel = null;

    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo();
        $this->_memberId  = $this->_loginInfo['memberID'];
        $this->_sid       = $this->_loginInfo['sid'];
    }

    //获取重要公告
    public function get_nts()
    {
        $memberId = $this->_memberId;

        try {
            $account  = $this->_loginInfo['account'];
            $ntcModel = new Announce();

            // 指定人重要公告
            $assignNtc = $ntcModel->get_rcnt_nts($account);

            $anIds = [];
            if ($assignNtc) {
                $anIds = array_merge($anIds,array_column($assignNtc, 'an_id'));
            }

            $isReadArr = $ntcModel->is_read($memberId, $anIds);
            $isReadArr = $isReadArr ? array_column($isReadArr,'an_id') : [];
            //指定优先弹框
            foreach ($assignNtc as $item){
                if (!in_array($item['an_id'],$isReadArr) && $item['account'] == $account) {
                    $ntcModel->add_read($memberId, $item['an_id']);
                    $data = [
                        'an_id'   => $item['an_id'],
                        'title'   => $item['title'],
                        'details' => htmlspecialchars_decode($item['details']),
                    ];
                    $this->apiReturn('200', $data, '有重要公告');
                }
            }
            unset($item);

            //重要公告
            $ntc      = $ntcModel->get_rcnt_nts();
            if (!$ntc) {
                $this->apiReturn('202', [], '无未读公告');
            }

            if ($ntc && !$ntcModel->is_read($memberId, $ntc['an_id'])) {
                $ntcModel->add_read($memberId, $ntc['an_id']);
                $data = [
                    'an_id'   => $ntc['an_id'],
                    'title'   => $ntc['title'],
                    'details' => htmlspecialchars_decode($ntc['details']),
                ];
                $this->apiReturn('200', $data, '有重要公告');
            } else {
                $this->apiReturn('202', [], '无未读公告');
            }
         
        } catch (Exception $e) {
            $this->apiReturn($e->getCode(), [], $e->getMessage());
            \pft_log('announce/err', $e->getCode() . "|" . $e->getMessage());
        }

    }

    /**
     * 发布通知
     * Create by zhangyangzhen
     * Date: 2019/11/14
     * Time: 11:55
     */
    public function releaseNotice()
    {
        $title   = I('post.title', '', 'strval,trim');
        $content = I('post.content', '', 'strval,trim');
        $endTime = I('post.endTime', '', 'strval,trim');
        $product = I('post.product');

        if (empty($title) || !$title) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '通知标题不能为空');
        }

        if (empty($content) || mb_strlen($content,'utf-8') > 20000) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '通知内容不能为空且不得超过20000个字符');
        }

        if (!$endTime || !strtotime($endTime)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '到期时间不能为空，且不得小于今天');
        }

        if ($endTime < date('Y-m-d')) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '到期日期不能小于今天');
        }

        if (!empty($product) && $product) {
            $product = implode(',', $product);
        } else {
            $product = '';
        }

        $noticeModel = new \Model\Notice\Notice();
        $res         = $noticeModel->releaseNotice($title, $content, $this->_sid, $endTime, $product, $this->_memberId);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 获取系统通知
     * Create by zhangyangzhen
     * Date: 2019/11/13
     * Time: 14:27
     *
     * @param $type 通知的状态  0=未读，1=已读
     */
    public function getNotice()
    {
        $type = I('post.type', -1, 'intval');
        $page = I('post.page', 1, 'intval');
        $size = I('post.size', 10, 'intval');

        $noticeModel = $this->_getNoticeModel();

        $noticeCount = $noticeModel->getNotice($this->_memberId, $type, $page, $size, true);

        $noticeList = [];
        if ($noticeCount > 0) {
            $noticeList = $noticeModel->getNotice($this->_memberId, $type, $page, $size);
            $noticeList = $this->_handleNoticeData($noticeList);
        }

        $data = [
            'list'  => $noticeList,
            'count' => $noticeCount,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $data, 'success');
    }

    /**
     * 更新通知状态（标记为已读 and 删除）
     * Create by zhangyangzhen
     * Date: 2019/11/13
     * Time: 17:06
     *
     * @param int state  1=已读,2=删除
     * @param array nid 通知ID数组
     */
    public function updateNotice()
    {
        $state = I('post.state', 0, 'intval');
        $nid   = I('post.nid');

        if (!$nid || !is_array($nid) || !$state) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $noticeModel = $this->_getNoticeModel();
        $result      = $noticeModel->updateNoticeState($nid, $this->_memberId, $state);

        if ($result) {
            if ($state == 1) {
                //标记已读操作后，对未读数做相应的修改
                $count = count($nid);
                (new System())->cutCountUnreadMsg($this->_memberId, $count);
            }

            $this->apiReturn(self::CODE_SUCCESS, [], '操作成功');
        } else {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '操作失败');
        }
    }

    /**
     * 获取当前登录用户发布的通知
     * Create by zhangyangzhen
     * Date: 2019/11/13
     * Time: 17:23
     */
    public function getReleaseNotice()
    {
        $page = I('post.page', 1, 'intval');
        $size = I('post.size', 10, 'intval');

        $model = $this->_getNoticeModel();

        $field = 'id,title,content,btime,status,adminid,ntype,px,sms_notify';
        $count = $model->getReleaseNotice($this->_memberId, $field, $page, $size, true);

        if ($count > 0) {
            $list = $model->getReleaseNotice($this->_memberId, $field, $page, $size);
            $list = $this->_handleNoticeData($list);
        }

        $data = [
            'list'  => $list,
            'count' => $count,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $data, 'success');
    }

    /**
     * 获取单条通知详情
     * Create by zhangyangzhen
     * Date: 2019/11/20
     * Time: 11:02
     */
    public function getNoticeDetail()
    {
        $nid = I('post.nid', 0, 'intval');

        if (!$nid || empty($nid)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $noticeModel = $this->_getNoticeModel();
        $notice      = $noticeModel->getNoticeDetail($nid);

        $adminID = $notice['adminid'];

        if ($adminID == 1) {
            $notice['dname'] = '超级管理员';
        } else {
            $memberModel = new \Model\Member\Member();
            $memberInfo  = $memberModel->getMemberInfo($adminID, 'id', 'dname');

            $notice['dname'] = $memberInfo['dname'];
        }

        ////分销商查看通知，修改状态为未读
        if ($adminID != $this->_sid && !$notice['read_status']) {
            $noticeModel->updateNoticeState([$nid], $this->_sid, 1);
            (new System())->cutCountUnreadMsg($this->_sid, 1);
        }

        $this->apiReturn(self::CODE_SUCCESS, $notice, 'success');
    }

    /**
     *
     * Create by zhangyangzhen
     * Date: 2019/11/13
     * Time: 17:32
     *
     * @param $data
     *
     * @return mixed
     */
    private function _handleNoticeData($data)
    {
        $memberBiz      = new \Business\Member\Member();
        $adminIdArr     = array_unique(array_column($data, 'adminid'));
        $memberNameList = $memberBiz->getList($adminIdArr);

        foreach ($data as $key => $item) {
            $data[$key]['dname'] = $memberNameList[$item['adminid']]['dname'] ?: '';
        }

        return $data;
    }

    private function _getNoticeModel()
    {
        if (is_null($this->_noticeModel)) {
            $this->_noticeModel = new \Model\Notice\Notice();
        }

        return $this->_noticeModel;
    }
}