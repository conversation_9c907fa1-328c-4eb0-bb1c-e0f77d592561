<?php
/**
 * 配置人员通知
 * <AUTHOR>
 * @date 2021/2/23
 */

namespace Controller\MsgNotify;

use Business\Authority\DataAuthLogic;
use Business\MemberLogin\MemberLoginHelper;
use Business\NewJavaApi\NewPms\Spu;
use Business\Product\SubProduct as SubProductBiz;
use Library\Controller;

class WxConfig extends Controller
{
    protected $_params;
    protected $_sid;
    protected $_dtype;
    protected $_sdtype;
    protected $_memberId;

    public function __construct()
    {
        parent::__construct();
        $this->_params = I('param.');
        $this->_params = (object)$this->_params;

        $loginInfo = $this->getLoginInfo();
        $this->_sid = $loginInfo['sid'];//上级用户ID
        $this->_dtype = $loginInfo['dtype'];//当前账号类型
        $this->_sdtype = $loginInfo['sdtype'];//上级账号类型
        $this->_memberId = $loginInfo['memberID'];//用户ID
    }

    /**
     * 产品配置通知人员列表
     * @return json
     * <AUTHOR>
     * @date 2021/2/23
     *
     */
    public function productConfigNotice()
    {
        $params = $this->_params;

        $applyDid = $this->_sid; //供应商ID
        $lid = $params->landId; //景区ID

        if (empty($lid)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $configBiz = new \Business\Notice\WxConfig();
        $listRes = $configBiz->productConfigNotice($applyDid, $lid);

        $this->apiReturn($listRes['code'], $listRes['data'], $listRes['msg']);
    }

    /**
     * 微信绑定人员列表分页查询
     * @return json
     * <AUTHOR>
     * @date 2021/2/23
     *
     */
    public function queryWxBindPage()
    {
        $params = $this->_params;

        $applyDid = $this->_sid; //供应商ID
        $page = $params->page ? $params->page : 1; //页码
        $size = $params->size ? $params->size : 15; //每页大小

        $configBiz = new \Business\Notice\WxConfig();
        $listRes = $configBiz->queryWxBindPage($applyDid, $page, $size);

        $this->apiReturn($listRes['code'], $listRes['data'], $listRes['msg']);
    }

    /**
     * 批量配置页面产品列表
     */
    public function configProductList()
    {
        $page = I('page', 1, 'intval') ?: 1; // 页码
        $size = I('size', 10, 'intval') ?: 10; // 每页大小
        $pType = I('pType', null, 'strval') ?: null; // 产品类型
        $pName = I('pName', null, 'strval,trim') ?: null; // 产品名称
        $landId = I('landId', null, 'intval'); // 景区ID（产品ID）
        $state = I('state', []); // 状态 1=上架 2=下架 3=删除
        $state = array_filter($state, function ($v) {
            return in_array($v, ['1', '2', '3']);
        }); // 过滤非法状态
        $startTime = I('startTime', null, 'strval') ?: null; // 发布开始时间 2013-10-30 00:00:00
        $endTime = I('endTime', null, 'strval') ?: null; // 发布结束时间 2013-10-30 23:59:59

        $spuIds = $landId ? [$landId] : [];

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition(['lidList' => $spuIds]);
        // 全禁的情况下不需要调用接口
        if ($condition === false) {
            $this->apiReturn(self::CODE_SUCCESS);
        }
        $spuIds = array_unique(array_merge($spuIds, $condition['lidList'] ?? []));

        //产品类型拆分为产品类型和子产品类型处理
        list($pType, $subType) = SubProductBiz::decodeTypeAndSubType($pType);
        if ($subType) {
            $condition['subType'] = $subType;
        }

        $spuStatusList = null;
        $showDelete = null;
        if (!empty($state)) {
            $spuStatusList = [];
            if (in_array('1', $state)) {
                $spuStatusList[] = 1;
            }
            if (in_array('2', $state)) {
                $spuStatusList[] = 2;
            }
            $showDelete = in_array('3', $state);
        }

        $result = (new Spu())->List([
            'keyword'          => null,
            'page'             => $page,
            'productType'      => $pType,
            'publishBeginDate' => $startTime ? $startTime . ' 00:00:00' : null,
            'publishEndDate'   => $endTime ? $endTime . ' 23:59:59' : null,
            'sid'              => $this->_sid,
            'size'             => $size,
            'sort'             => null,
            'spuIds'           => $spuIds,
            'spuName'          => $pName,
            'notInSpuIds'      => $condition['notLidList'] ?? null,
            'subType'          => $condition['subType'] ?? null,
            'notInProductType' => ['Q'],
            'spuStatusList'    => $spuStatusList,
            'showDelete'       => $showDelete,
        ]);

        $list = $result['data']['records'] ?? [];
        $total = $result['data']['total'] ?? 0;

        foreach ($list as &$spu) {
            $spu = [
                'landId' => $spu['id'],
                'state'  => $spu['isLogicDel'] === 1 ? 3 : $spu['status'],
                'title'  => $spu['spuName'],
                'pType'  => $spu['ptype'],
            ];
        }

        $configBiz = new \Business\Notice\WxConfig();
        try {
            $list = $configBiz->payloadWxConfigsForProducts($this->_sid, $list);
        } catch (\Exception $e) {
            $this->apiReturn(self::CODE_SUCCESS, ['list' => [], 'total' => 0], $e->getMessage());
            return;
        }

        $this->apiReturn(self::CODE_SUCCESS, ['list' => $list, 'total' => $total]);
    }

    /**
     * 通知产品列表
     */
    public function noticeProductList()
    {
        $params = $this->_params;

        $applyDid = $this->_sid; //供应商ID
        $bandId = $params->bandId; //绑定ID
        $page = $params->page ? $params->page : 1; //页码
        $size = $params->size ? $params->size : 15; //每页大小
        $landId = $params->landId ? $params->landId : null; //景区ID

        if (empty($bandId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (!is_null($landId) && !is_numeric($landId)) {
            $this->apiReturn(203, [], '参数错误,请传ID');
        }

        $configBiz = new \Business\Notice\WxConfig();
        $listRes = $configBiz->noticeProductList($applyDid, $bandId, $page, $size, $landId);

        $this->apiReturn($listRes['code'], $listRes['data'], $listRes['msg']);
    }

    /**
     * 通知人员配置
     * @return json
     * <AUTHOR>
     * @date 2021/2/23
     *
     */
    public function productConfig()
    {
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $this->apiReturn(401, [], '无使用权限');
        }
        $params = $this->_params;

        $applyDid = $this->_sid; //供应商ID
        $landId = $params->landId; //景区ID
        $bandId = $params->bandId ? $params->bandId : []; //配置的通知人员ID集合

        if (empty($landId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $configBiz = new \Business\Notice\WxConfig();
        $listRes = $configBiz->productConfig($applyDid, $landId, $bandId);

        $this->apiReturn($listRes['code'], $listRes['data'], $listRes['msg']);
    }

    /**
     * 通知人员配置按人员配置
     * @return json
     * <AUTHOR>
     * @date 2021/2/23
     *
     */
    public function productConfigByUser()
    {
        $params = $this->_params;

        $applyDid = $this->_sid; //供应商ID
        $bandId = $params->bandId;//配置的通知人员ID
        $type = $params->type;//类型 1=增加当前方式 2=直接替换
        $landId = $params->landId;//景区ID

        if (empty($bandId) || empty($type) || empty($landId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $configBiz = new \Business\Notice\WxConfig();
        $listRes = $configBiz->productConfigByUser($applyDid, $bandId, $type, $landId);

        $this->apiReturn($listRes['code'], $listRes['data'], $listRes['msg']);
    }

    /**
     * 取消通知该产品
     * @return json
     * <AUTHOR>
     * @date 2021/3/4
     *
     */
    public function cancelNoticeProduct()
    {
        $params = $this->_params;

        $applyDid = $this->_sid; //供应商ID
        $landId = $params->landId; //景区ID
        $bandId = $params->bandId; //配置的通知人员ID

        if (empty($landId) || empty($bandId)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $configBiz = new \Business\Notice\WxConfig();
        $listRes = $configBiz->cancelNoticeProduct($applyDid, $landId, $bandId);

        $this->apiReturn($listRes['code'], $listRes['data'], $listRes['msg']);
    }

    /**
     * 获取微信公众号APPID
     * @return array
     * <AUTHOR>
     * @date 2021/4/21
     *
     */
    public function queryWatchAppid()
    {
        $this->apiReturn(200, ['appid' => PFT_WECHAT_APPID], '');
    }
}