<?php
/**
 * crm系统与12301平台 发票数据交互接口
 *
 * <AUTHOR>
 * @date   2022-04-18
 */


namespace Controller\Crm;

use Model\Member\Invoice as model;
use Model\Crm\Contrac as ContracModel;

class Invoice extends CrmBase
{

    public function __construct()
    {
        parent::__construct();
    }


    /**
     * 平台开票数据
     * <AUTHOR>
     * @date   2022-04-18
     *
     * @return
     */
    public function record()
    {
        $startTime = I('get.start_time/i', 0);
        $endTime   = I('get.end_time/i', 0);

        $page = I('get.page/i', 1);
        $size = I('get.size/i', 200);

        //发票类型：1普票，2专票
        $type  = I('get.type/i', 0);
        //状态：1 审核通过，3 已邮寄，5 已开票
        $state = I('get.state/i', '');
        //时间类型：1.申请时间(默认) 2.审核时间
        $timeType = I('get.time_type', 1, 'intval');

        if (empty($startTime) ||empty($endTime) || !in_array($timeType, [1, 2])) {
            $this->apiReturn(201, [], '参数错误');
        }

        if ($endTime - $startTime > (3600 * 24 * 7)) {
           // $this->apiReturn(201, [], '最多查询一周的数据');
        }

        //这边查询不限制了，erp那边自己过滤  20220629
        $tradeType = [];
        $stateArr  = $state === '' ? [1, 3, 5] : [$state];

        $record = (new model())->getList($startTime, $endTime, $type, $stateArr, $tradeType, $timeType, $page, $size);
        $formatData = [];

        foreach ($record['list'] as &$tmp) {
            if (isset($tmp['invoice_type']) && $tmp['invoice_type'] == 1) { //开的是普票，审核时间==申请时间
                $tmp['examine_time'] = $tmp['billing_time'];
            }

            $isMerge = false;
            foreach ($formatData as &$tmpFormat) {
                if ($tmp['id'] == $tmpFormat['id']) {
                    $tmpFormat['money'] += $tmp['money'];
                    !empty($tmp['order_id']) && $tmpFormat['order_id'][] = $tmp['order_id'];
                    $isMerge = true;
                }
            }

            if (!$isMerge) {
                $tmpData = $tmp;
                $tmpData['order_id'] = empty($tmp['order_id']) ? [] : [$tmp['order_id']];
                $formatData[] = $tmpData;
            }
        }

        $returnData = [
            'total' => $record['total'],
            'list'  => $formatData,
        ];

        $this->apiReturn(200, $returnData);
    }


    /**
     * 更新合同信息
     * <AUTHOR>
     * @date   2022-04-18
     *
     * @return
     */
    public function queryInfoByTime()
    {
        $startTime = I('get.start_time/s', '');
        $endTime   = I('get.end_time/s', '');

        //$page = I('get.page/i', 1);
        //$size = I('get.size/i', 200);

        if (empty($startTime) || empty($endTime)) {
            $this->apiReturn(201, [], '参数错误');
        }

        if ($endTime - $startTime > (3600 * 24 * 7)) {
            //  $this->apiReturn(201, [], '最多查询一周的数据');
        }

        $returnData = [];
        $getList    = (new ContracModel())->getListByTime();

        if (empty($getList['list'])) {
            $this->apiReturn(200, $returnData);
        }

        //service_order_id 为json串，这边在处理
        $orderIdRes = array_column($getList['list'], 'service_order_id');
        $orderId    = [];
        foreach ($orderIdRes as $tmp) {
            $tmp = json_decode($tmp, true);
            is_array($tmp) && $orderId = array_merge($orderId, $tmp);
        }
        $record     = (new \Business\AppCenter\Service())->getInfoByOrderId($orderId);
        if ($record['code'] != 200) {
            $this->apiReturn($record['code'], $returnData, $record['msg']);
        }

        // $fidList      = array_unique(array_column($record['data'], 'fid'));
        // $memberExt    = (new \Business\Member\Member())->getInfoMemberExtListFromJava($fidList, 'fid, contract_model');
        // $memberExtMap = array_column($memberExt, 'contract_model', 'fid');

        $openData = [];

        foreach ($record['data'] as $tmp) {

            $beginTimeTmp = strtotime($tmp['begin_time']);
            $endTimeTmp   = strtotime($tmp['end_time']);

            //时间取交集，就返回
            if ($beginTimeTmp - $startTime > 0) {
                $checkTime = ($beginTimeTmp - $endTime) >= 0 ? false : true;
            } else {
                $checkTime = ($endTimeTmp - $startTime) >= 0 ? true : false;
            }

            if ($checkTime) {
                // $tmpFid                = $tmp['fid'];
                // $tmp['contract_model'] = $memberExtMap[$tmpFid];
                $openData[]            = $tmp;
            }
        }

        $returnData = [
            'list' => $openData,
            // 'more' => $getList['total'],
        ];

        $this->apiReturn(200, $returnData);
    }


    /**
     * 更新合同信息
     * <AUTHOR>
     * @date   2022-04-18
     *
     * @return
     */
    public function queryInfoByOrderId()
    {
        $postJson = $this->json();
        $orderId = $postJson['order_id'] ?? [];
        $record  = (new \Business\AppCenter\Service())->getInfoByOrderId($orderId);
        $this->apiReturn($record['code'], $record['data'], $record['msg']);
    }


    /**
     * 更新合同信息
     * <AUTHOR>
     * @date   2022-04-18
     *
     * @return
     */
    public function syncContracNum()
    {
        $postJson = $this->json();
        $contracNum = $postJson['contrac_num'] ?? '';
        
        if (empty($contracNum)) {
            $this->apiReturn(400, [], '参数错误');
        }

        $returnData = ['contrac_num' => $contracNum];
        $model      = new \Model\Crm\Contrac();

        if ($model->checkContracNum($contracNum)) {
            $this->apiReturn(400, $returnData, '合同号已存在:' . $contracNum);
        }

        if ($model->insertContracNum($contracNum)) {
            $this->apiReturn(200, $returnData, '更新成功');
        }

        $this->apiReturn(500, $returnData, '更新合同号失败:' . $contracNum);
    }


}