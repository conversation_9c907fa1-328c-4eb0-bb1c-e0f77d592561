<?php
namespace Controller\Crm;

use Library\Controller;

class CrmBase extends Controller
{
    private $_key    = 'ca4238032d59a6f75849';
    private $_secret = '2c8ed3d61a0451d3b8f63';


    public function __construct($apiVerify = true)
    {
        if ($apiVerify) {
            list($code, $msg) = $this->_verifySign(I('get.'));
            if ($code != 200) {
                $this->apiReturn($code, [], $msg);
            }
        }
    }

    /**
     * 访问频率限制
     * <AUTHOR>
     * @date 2022/02/21
     *
     * @return array
     */
    private function _accessFrequencyLimit()
    {
        //$actionKey = '';
        //$memberID  = '';
        //$nowSecond = date('mdHi');
        //
        //$accessLimitKey   = "access_limit:frequency:{$actionKey}:{$memberID}";
        //$secondRequestKey = "access_limit:minute_total:{$actionKey}:{$memberID}:{$nowSecond}";
        //$frozeKey         = "access_limit:froze_seconds:{$actionKey}:{$memberID}";
        //
        //$limitLogKey = "pft_access_limit";
        //$logData     = json_encode([
        //    'member_id' => $memberID,
        //    'url'       => $actionKey,
        //]);
    }


    /**
     * 签名校验
     * <AUTHOR>
     * @date 2022/02/21
     *
     * @return array
     */
    private function _verifySign(array $data)
    {
        if (empty($data['sign'])) {
            return [400, '数据签名参数错误'];
        }

        if (empty($data['timestamp'])) {
            return [400, '时间戳参数错误'];
        }

        if (time() - $data['timestamp'] > 300) {
            return [400, '请重新发送请求'];
        }

        $sign = $data['sign'];
        unset($data['sign']);
        unset($data['a']);
        unset($data['c']);
        
        ksort($data);

        if ($sign != md5(http_build_query($data) .  '&appsecret=' . $this->_secret)) {
            return [400, '校验失败'];
        }

        return [200, ''];
    }

    protected function json()
    {
        $input  = file_get_contents('php://input');
        return json_decode($input, true);
    }

}