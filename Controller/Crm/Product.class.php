<?php

namespace Controller\Crm;

use Controller\Crm\CrmBase;

class Product extends CrmBase
{

    private $_appCenterBiz;

    public function __construct()
    {
        parent::__construct();
        $this->_appCenterBiz = new \Business\AppCenter\Service();
    }


    public function productList()
    {
        $res = $this->_appCenterBiz->crmGetProdctList();
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

}