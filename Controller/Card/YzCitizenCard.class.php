<?php
/**
 * 扬州市民卡中心年卡
 *平台市民卡管理、扬州市民卡年卡过闸记录管理
 * 市民卡管理的查看权限归所有使用扬州市民卡景区所有
 */
namespace Controller\Card;

use Business\Face\YzCitizenCardBiz;
use Library\Controller;

class YzCitizenCard extends Controller
{
    private $sid;
    private $memberId;
    private $biz;

    public function __construct()
    {
        if (ENV == 'LOCAL') {
            $loginInfo = [
                'sid' => 3385,
                'memberID' => 3385,
            ];
        } else {
            $loginInfo = $this->getLoginInfo();
        }
        $this->sid = $loginInfo['sid'];
        $this->memberId = $loginInfo['memberID'];
        $this->biz = YzCitizenCardBiz::getInstance();
    }
    /**
     * 扬州年卡过闸记录
     * <AUTHOR>
     * @Date 2023/10/30 18:56
     * @return void
     */
    public function getCardPassLog()
    {
        //卡号
        $cardNo = I('post.card_no', '', 'strval,trim');
        //身份证
        $idCardNo = I('post.id_card_no', '', 'strval,trim');
        //时间区间
        $st = I('post.start_time', '', 'strval,trim');
        $et = I('post.end_time', '', 'strval,trim');
        //过闸结果：成功=1 失败=0
        $checkStatus = I('post.check_status');
        $page = I('post.page', 1, 'intval');
        $pageSize = I('post.page_size', 10, 'intval');
        $page = $page < 1 ? 1 : $page;
        $pageSize = $pageSize < 1 ? 10 : $pageSize;
        $sid = $this->sid;
        $params = compact('cardNo', 'idCardNo', 'page', 'pageSize', 'st', 'et', 'checkStatus', 'sid');
        $res = $this->biz->getPassLog($params);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}