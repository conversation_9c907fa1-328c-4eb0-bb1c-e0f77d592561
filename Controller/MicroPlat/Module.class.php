<?php
/**
 * 微平台 应用相关
 * <AUTHOR>
 * @date 2021-09-08
 */

namespace Controller\MicroPlat;

use Controller\MicroPlat\Common as Common;
use Business\AppCenter\Module as ModuleBiz;
use Business\AppCenter\LimitBuy as LimitBuyBiz;

class Module extends Common
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取应用开通信息
     * <AUTHOR>
     * @date 2021/9/8
     *
     */
    public function getOpenInfo()
    {
        $this->isLogin('ajax');

        //模块id
        $moduleId = I('post.module_id', 0, 'intval');

        if (!$moduleId) {
            $this->apiReturn(400, [], '模块id参数错误');
        }

        $fid   = $this->_loginMember['sid'];
        $dtype = $this->_loginMember['dtype'];

        if (!in_array($dtype, [0, 1])) {
            $this->apiReturn(400, [], '用户无权操作');

        }
        $rcSpreadChannel = I('post.rc_spread_channel', 0, 'intval');
        $authParams = [];
        if ($moduleId == 57) {
            $authParams = ['from' => 'resource_center_pay', 'h' => 'm', 'id' => 57, 'rc_spread_channel' => $rcSpreadChannel];
        }
        if (!empty($authParams)) {
            $this->_wxAuth($authParams);
        }

        $moduleRes = (new ModuleBiz)->getOpenInfo($moduleId, $fid, $dtype);
        if ($moduleRes['code'] == 200) {
            $data = $moduleRes['data'];
            $this->apiReturn(200, $data);

        } else {
            $this->apiReturn($moduleRes['code'], [], $moduleRes['msg']);
        }
    }

    /**
     * 购买记录详情
     * <AUTHOR>
     * @date 2020/11/13
     *
     */
    public function getBuyInfo()
    {
        $this->isLogin('ajax');

        $tradeNo = I('post.trade_no', 0, 'strval');
        if (!$tradeNo) {
            $this->apiReturn(400, [], '订单号异常');
        }

        $fid = $this->_loginMember['sid'];

        $moduleRes = (new ModuleBiz)->getBuyRecordInfo($fid, $tradeNo);
        if ($moduleRes['code'] == 200) {
            $data = $moduleRes['data'];
            $this->apiReturn(200, $data);

        } else {
            $this->apiReturn($moduleRes['code'], [], $moduleRes['msg']);
        }
    }

    /**
     * 微信授权验证
     * <AUTHOR>
     * @date 2021/9/13
     *
     * @return bool
     */
    public function _wxAuth(array $params)
    {
        if (!$this->inWechatApp()) {
            $this->apiReturn(400, [], '请在微信客户端打开');
        }

        if (strstr(http_build_query($params), 'resource_center_pay')) {
            //资源中心开通相关授权，记录下日志
            pft_log('micro_plat_auth/resource_center_pay/debug', json_encode($params, JSON_UNESCAPED_UNICODE));
        }

        // 判断是否在未获授权的微信中 - 微信支付前需要获得授权
        if (!$this->_loginMember['pft_openid']) {
            if (in_array(ENV, ['PRODUCTION', 'TEST'])) {
                $url = $this->getMicroPlatHost() . 'r/MicroPlat_Member/pftWxAuth?' . http_build_query($params);
                $this->apiReturn(206, ['url' => $url]);
            } else {
                $this->apiReturn(400, [], '测试环境无法微信获得授权');
            }
        }

        return true;
    }

    /**
     * 获取用户限购配置详情
     * <AUTHOR>
     * @date 2021/9/6
     *
     */
    public function getUserLimitBuyInfo()
    {
        $this->isLogin('ajax');

        $targetId = I('post.target_id', 0, 'intval');
        $rcSpreadChannel = I('post.rc_spread_channel', 0, 'intval');
        $authParams = [];
        if ($targetId == 57) {
            $authParams = ['from' => 'resource_center_pay', 'h' => 'm', 'id' => 57, 'rc_spread_channel' => $rcSpreadChannel];
        }
        if (!empty($authParams)) {
            $this->_wxAuth($authParams);
        }

        $fid = $this->_loginMember['sid'];

        $moduleRes = (new LimitBuyBiz)->getUserLimitBuyInfo($fid, $targetId, 1);
        if ($moduleRes['code'] == 200) {
            $data = $moduleRes['data'];
            $this->apiReturn(200, $data);

        } else {
            $this->apiReturn($moduleRes['code'], [], $moduleRes['msg']);
        }
    }
}