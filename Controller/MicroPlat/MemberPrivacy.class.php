<?php
/**
 * 用户隐私相关
 */

namespace Controller\MicroPlat;

use Controller\MicroPlat\Common as Common;
use Business\Member\MemberPrivacy as MemberPrivacyBiz;
use Library\Cache\Cache;

class MemberPrivacy extends Common
{

    public function __construct()
    {
        parent::__construct();
        $memberId = $this->getMemberId();
        if (empty($memberId)) {
            $this->returnAutoLogin('请先登录');
        }

    }

    /**
     * 平台获取用户隐私验证码
     * <AUTHOR>
     * @date 2021/6/7
     *
     */
    public function getVcodeImg()
    {
        $memberId = $this->_loginMember['memberID'];
        $res = (new MemberPrivacyBiz())->getVcode($memberId);
        if (empty($res)) {
            $this->apiReturn(203, [], '验证码生成失败');
        }
        $this->apiReturn(200, $res, '验证码');
    }

    /**
     * 查新账号是否设置了防骚扰
     * <AUTHOR>
     * @date 2021/6/16
     *
     */
    public function getVerifCode()
    {
        $memberId = $this->_loginMember['memberID'];
        $sid = $this->_loginMember['sid'];

        $limitKey = "platform:get_distributor:{$sid}:{$memberId}";

        $cacheObj = Cache::getInstance('redis');
        $lockTime = 1;
        $lockInfo = $cacheObj->lock($limitKey, 1, $lockTime);
        if (!$lockInfo) {
            $this->apiReturn(500, [], '请求过于频繁');
        }

        $searchInfo = I('get.key_word', '');
        $searchType = I('get.search_type', -1, 'intval');    //搜索类型 -1默认 1账号 2手机号 3企业/个人实名
        $account    = I('get.account', '', 'strval');//多账户校验通过后 将对应的账号带入
        if (empty($searchInfo)) {
            $this->apiReturn(204, [], '请输入关键字');
        }

        //手机号无需校验
        if (ismobile($searchInfo)) {
            $this->apiReturn(200, []);
        }

        $result = (new MemberPrivacyBiz())->getVerifCodeByAccount($sid, $memberId, $searchInfo, $searchType, $account);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取验证详情
     * <AUTHOR>
     * @date 2021/9/29
     *
     */
    public function getVerifCodeInfo()
    {
        $sid = $this->_loginMember['sid'];

        $result = (new MemberPrivacyBiz())->getVerifCodeByFid($sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 防骚扰设置
     * <AUTHOR>
     * @date 2021/6/8
     *
     */
    public function setNotHarassConfig()
    {
        // 获取用户id
        $memberId = $this->_loginMember['memberID'];
        // 获取账户类型
        $dtype = $this->_loginMember['dtype'];
        //允许供应商和分销商
        if (!in_array($dtype, [0, 1])) {
            $this->apiReturn(200, [], '无权限操作');
        }

        $privacyBiz = new MemberPrivacyBiz();

        $status = I('post.status', 0, 'intval'); //状态 1开 0 关
        $type   = I('post.type', 0, 'intval'); //1默认手机号和姓名  2口令
        $passwd = I('post.passwd', '', 'strval'); //非必填  type2的时候必填
        $id     = I('post.id', 0, 'intval'); //配置id

        if (!in_array($status, $privacyBiz::NOT_HARASS_STATUS)) {
            $this->apiReturn(203, [], '设置状态错误');
        }

        if ($status == $privacyBiz::NOT_HARASS_STATUS_OPEN && !in_array($type, $privacyBiz::NOT_HARASS_TYPE)) {
            $this->apiReturn(203, [], '设置验证类型错误');
        }

        if ($type == $privacyBiz::NOT_HARASS_TYPE_CODE && $passwd == '') {
            $this->apiReturn(203, [], '口令不能为空');
        }

        $result = $privacyBiz->setNotHarassConfigByMemberId($memberId, $status, $type, $passwd, $id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

}