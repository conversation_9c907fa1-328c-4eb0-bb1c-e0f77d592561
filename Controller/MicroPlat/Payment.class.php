<?php
/**
 * 微平台支付相关
 * <AUTHOR>
 * @date 2021-09-09
 */

namespace Controller\MicroPlat;

use Controller\MicroPlat\Common as Common;
use Business\AppCenter\Payment as PaymentBiz;
use Library\Business\WePay\WxPayLib;
use Model\TradeRecord\OnlineTrade;
use Business\AppCenter\Module as ModuleBiz;

class Payment extends Common
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 应用购买
     * <AUTHOR>
     * @date 2021/9/13
     *
     */
    public function buyModuleOnLine()
    {
        $this->isLogin('ajax');
        //开通数据
        $moduleData = I('post.module_data', []);
        $moduleIds = array_column($moduleData, 'appid');
        $rcSpreadChannel = I('post.rc_spread_channel', 0, 'intval');
        $authParams = [];
        if (in_array(57, $moduleIds)) {
            $authParams = ['from' => 'resource_center_pay', 'h' => 'm', 'id' => 57, 'rc_spread_channel' => $rcSpreadChannel];
        }
        if (!empty($authParams)) {
            (new \Controller\MicroPlat\Module())->_wxAuth($authParams);
        }

        $_POST['pft_openid'] = $this->_loginMember['pft_openid'];
        $_POST['source']     = 'microplat';

        (new \Controller\AppCenter\AppCenterPayment())->buyOnLine();
    }

    /**
     * 应用支付查询
     * <AUTHOR>
     * @date 2021/9/9
     *
     */
    public function checkPayStatus()
    {
        $this->isLogin('ajax');
        try {
            $orderNo = I('post.order_no');

            if (empty($orderNo)) {
                throw new \Exception("订单号不能为空");
            }

            //在线交易记录表
            $onLineTradeModel = new OnlineTrade();
            $tradeRes = $onLineTradeModel->getLogByOrderId($orderNo);
            if (!empty($tradeRes) && isset($tradeRes['status']) && $tradeRes['status'] == 1) {
                $code = 200;
                $msg = 200;
            } else {
                $code = 202;
                $msg = '订单待支付';
            }
        } catch (\Exception $e) {
            $code = 400;
            $msg = $e->getMessage();
        }

        $this->apiReturn($code, '', $msg);
    }
}