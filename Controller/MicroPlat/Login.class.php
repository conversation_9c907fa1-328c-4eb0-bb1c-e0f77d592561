<?php
/**
 * 微平台相关
 *
 * <AUTHOR>
 * @time   2020-8-6
 *
 * 200成功 207前端跳转登录页 其余弹框
 */
namespace Controller\MicroPlat;

use Library\Controller as Controller;
use Library\Cache\Cache;


class Login extends Controller
{

    /**
    * 微商城分销专员微平台根据uuid登入设置session
    * <AUTHOR>
    * @date   2020-08-04
    *
    * @return array
    */
    public function loginByUuid()
    {
        $uuid       = I('uuid', '', 'strval,trim');
        $type       = I('type', 0, 'intval');
        if (!$uuid && !$type) {
            $this->apiReturn(203, [], '参数错误');
        }

        $multiMemberBuz = new \Business\MultiDist\Member();
        if ($uuid) {
            $teaminfo       = $multiMemberBuz->getChainByUuid($uuid);
            if (!$teaminfo) {
                $this->apiReturn(204, [], '获取分销专员数据错误');
            }
            $memberBuz   = new \Business\Member\Member();
            $userInfoRes = $memberBuz->getInfo($teaminfo['uid']);
            if (empty($userInfoRes)) {
                $this->apiReturn(204, [], '获取用户信息错误');
            }
            if (!in_array($userInfoRes['dtype'], [0, 1, 10])) {
                $this->apiReturn(204, [], '登录角色错误');
            }
            //身份验证 目前微商城与微平台session是公用的
            if ($userInfoRes['customer_id'] != $_SESSION['customerId']) {
                $this->apiReturn(400, [], '身份验证错误');
            }
            $uid   = $teaminfo['uid'];
            $dtype = $userInfoRes['dtype'];
        }
        if ($type) {
            $customerId = $_SESSION['customerId'];
            switch ($type){
                //微平台-》微商城-》微平台
                case 1:
                    $accountKey = "micro_login:mark:{$customerId}";
                    $redis      = Cache::getInstance('redis');
                    $loginMark  = $redis->get($accountKey);
                    if (!$loginMark) {
                        $this->apiReturn(204, [], '微平台登录信息已过期，请重新登录微平台');
                    }
                    $info  = json_decode($loginMark,true);
                    $uid   = $info['uid'];
                    $dtype = $info['dtype'];
                    break;
                case 2:
                    //微商城-》微平台
                    $memberMode = new \Model\Member\Member();
                    //客户id
                    $memberInfo = $memberMode->getMultiRoleInfo($customerId, [0, 1]);
                    if (empty($memberInfo)) {
                        $this->apiReturn(204, [], '散客不能登录微平台');
                    }
                    $memberInfo = $memberInfo[0];
                    $uid        = $memberInfo['id'];
                    $dtype      = $memberInfo['dtype'];
            }

        }

        $sessionBuz = new \Business\Member\Session();
        $result     = $sessionBuz->loginMemberId($uid, 'wx', $dtype);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }
        if ($uuid) {
            //session添加分销专员信息
            $multiMemberBuz->setMultiDistSession($uuid,
                $teaminfo['level'] == 0 ? $multiMemberBuz::TOP_ROLE : $multiMemberBuz::NOMAL_ROLE, $teaminfo['level'], $uid);
        }

        $this->apiReturn(200, [], '成功');
    }

    /**
     * 微商城跳回微平台获取登录标识
     * <AUTHOR>
     * @date   2020-10-23
     *
     * @return array
     */
    public function getMicroPlatMark()
    {
        $loginInfo = $this->getLoginInfo();
        $accountKey = "micro_login:mark:{$loginInfo['customerId']}";
        $redis      = Cache::getInstance('redis');
        $loginMark  = $redis->get($accountKey);
        $loginMark = $loginMark ? 0:1;
        $this->apiReturn(200,['from_mall'=>$loginMark] , '成功');

    }
}
