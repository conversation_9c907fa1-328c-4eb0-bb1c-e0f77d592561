<?php

/**
 * 产品预订 - 订单相关接口
 *
 * <AUTHOR>
 * @time   2017-01-15
 */

namespace Controller\MicroPlat;

use Business\Authority\DataAuthLogic;
use Business\Captcha\OrderCaptcha;
use Business\JavaApi\Order\OrderTouristInfo;
use Business\JavaApi\Product\Land as LandJavaBiz;
use Business\JavaApi\TicketApi;
use Business\Member\MemberRelation;
use Business\MultiDist\Trade;
use Business\Order\Modify;
use Business\Order\Query;
use Business\Order\ShowModify;
use Business\Product\Specialty;
use Controller\MicroPlat;
use Controller\MicroPlat\Common as Common;
use Library\Constants\OrderConst;
use Library\Kafka\KafkaProducer;
use Library\Tools\Helpers as Helpers;
use Model\Land;
use Model\Order\OrderQuery;
use Business\Order\MergeOrder;
use Model\Order\OrderTools;
use Model\Order\OrderTrack;
use Model\Subdomain\SubdomainInfo;
use Model\Product\Ticket;
use Business\Product\Show as ShowBus;
use phpDocumentor\Reflection\Types\Self_;
use Process\Order\OrderParams;
use Business\Order\PlatformSubmit;
use Business\Order\BaseCheck;
use Business\Order\OrderList;
use Library\Constants\Order\OrderStatus\SpecialOrderStatus;
use Library\Resque\Queue;

class Order extends Common
{

    private $_options     = []; //下单参数
    private $_tempOptions = []; //临时下单参数

    private $_soap;

    private $_orderModeConf;//渠道配置信息
    private $_orderModeKey;//渠道KEY

    const __DEFAULT_SY_ACCOUNT     = 100005;    // 三亚账号
    const __DEFAULT_SY_MEMBERID    = 55;        // 三亚ID

    //订单精确搜索类型
    private $_exactType = [
        //订单号查询
        'ordernum',
        //取票人手机
        'mobile',
        //产品名称
        'pro_name',
        //远程订单号
        'remote_order',
        //第三方订单号
        'third_order',
        //联系人姓名
        'linkman',
        //身份证
        'id_card',
    ];

    //订单的合法状态
    private $_orderStatus = [-1, 0, 1, 2, 3, 4, 5, 6, 7, 9];

    //0=分销商 1=供应商 2=供/分销商
    private $_memberType = [0, 1, 2];

    //0=下单时间, 2=验证时间
    private $_dateType = [0, 2];

    public function __construct()
    {
        parent::__construct();

        //渠道配置信息
        $tmpOrderModeConf = load_config('order_mode_two', 'orderSearch');

        foreach ($tmpOrderModeConf as $key => $item) {
            if (is_array($item['key'])) {
                foreach ($item['key'] as $v) {
                    $this->_orderModeConf[$v] = $item['name'];
                }
            } else {
                $this->_orderModeConf[$item['key']] = $item['name'];
            }
            $this->_orderModeKey[$key] = $item['key'];
        }

        $this->_soap = $this->getSoap();
    }

    /**
     * 微平台合并付款提交订单
     * date   2018-09-28
     * author hanwenlin
     *
     * return
     */
    public function order()
    {
        $this->isLogin('ajax');

        @pft_log('order/wechat/microplat', json_encode($_REQUEST, JSON_UNESCAPED_UNICODE), $pathMode = 'day');

        //并发，重复提交判断
        $this->_concurrencyHCheck();

        // 处理滑动验证码
        //$this->handleCaptcha();

        //是否是下单权限
        if ($this->_loginMember['dtype'] == 6) {
            $baseCheck = new BaseCheck();
            if (!$baseCheck->isHaveOrderAuth($this->_loginMember['memberID'])) {
                $this->apiReturn(204, [], '该员工账号无权限下单');
            }
        }

        //获取参数
        $paramsRes = OrderParams::getMicroPlatParams();
        if ($paramsRes[0] != 200) {
            $this->apiReturn(204, [], $paramsRes[1]);
        }

        $orderParam = $paramsRes[2];

        $orderParam['member_id'] = $this->_loginMember['sid'];
        $orderParam['op_id']     = $this->_loginMember['memberID'];
        $orderParam['is_sale']   = false;
        $orderParam['channel']   = self::__ORDER_MODE__;
        $multiDist = (new \Business\MultiDist\Member())->getMultiDistCache($this->_loginMember['sid']);
        //分销专员下单需要处理下单参数
        if (!empty($orderParam['dis_id'])) {
            if (!empty($multiDist['multi_dist']) && $multiDist['multi_dist']['uuid'] == $orderParam['dis_id']) {
                $multiDistTradeBus = new \Business\MultiDist\Order();
                $orderParam        = $multiDistTradeBus->orderParamsByMd($orderParam,
                    intval($this->_loginMember['sid']), $orderParam['dis_id']);
            } else {
                unset($orderParam['dis_id']);
            }
        }

        $platformOrderBiz = new PlatformSubmit();
        $orderRes         = $platformOrderBiz->combineSubmit($orderParam);

        //$orderRes = [
        //    '0' => 200,
        //    '2' => [
        //        'list' => [
        //            'ordernum' => 123,
        //        ]
        //    ]
        //];

        if ($orderRes[0] == 200) {
            //下单成功,延长两秒的锁时间,避免微信10秒重试机制导致重复下单
            $this->_continueLockLife(2);
            //// 保存联系人
            $keepRes = $this->_saveLinkMan();

            $orderData = $orderRes[2];
            $return    = [
                'tradeId' => $orderData['tradeId'],
                'paymode' => $orderData['list'][0]['paymode'],
            ];
            foreach ($orderData['list'] as $item) {
                $return['list'][] = [
                    'ordernum' => $item['ordernum'],
                    'paymode'  => $item['paymode'],
                ];
                $this->_saveShippingAddr($item['ordernum']);
            }
            $this->apiReturn(200, $return);
        } else {
            //下单出错移除锁
            $this->_removeLock();
            $this->apiReturn(204, [], $orderRes[1]);
        }
    }

    /**
     * 校验是否启用验证码，用于处理前端时候显示滑动验证码组件
     */
    public function checkCaptchaEnable()
    {
        $isEnable = OrderCaptcha::checkOrderSubmitCaptchaEnable($this->_loginMember['sid']);
        $result = ['is_enable' => $isEnable];
        $this->apiReturn(200, $result, 'success');
    }
    /**
     * 下单是否，校验验证码是否处理
     */
    protected function handleCaptcha()
    {
        $paymode = I('post.paymode', 0, 'intval');
        $captchaCode = I('post.captcha_code', '', 'string');
        $loginSid = $this->_loginMember['sid'];

        $res = OrderCaptcha::handleOrderSubmitCaptcha($captchaCode, $loginSid, $paymode);
        if (!is_array($res)) {
            return;
        }
        list($code, $msg, $data) = $res;
        if (200 != $code) {
            // 验证码错误
            $this->apiReturn($code, $data, $msg);
        }
    }

    /**
     * 重复下单（并发判断）
     * @return [type] [description]
     */
    private function _concurrencyHCheck()
    {
        //避免重复下单
        $Redis = $this->getRedisObject();
        unset($_REQUEST['token']);
        $key  = md5(json_encode($_REQUEST));
        $lock = $Redis->lock($key, 1, 60);

        if (!$lock) {
            @pft_log('order/wechat/microplat', '重复提交', $pathMode = 'day');
            $this->apiReturn(204, [], '您有一笔订单正在处理中,请稍后再试');
        }
    }

    /**
     * 下单成功后锁继续存在两秒钟
     * <AUTHOR>
     * @date   2017-09-27
     *
     * @param  integer $second 存活秒数
     */
    private function _continueLockLife($second = 2)
    {
        $Redis = $this->getRedisObject();
        $key   = md5(json_encode($_REQUEST));
        $Redis->lock_set($key, 1, $second);
    }

    /**
     * 移除锁
     * @return [type] [description]
     */
    private function _removeLock()
    {
        $Redis = $this->getRedisObject();
        $key   = md5(json_encode($_REQUEST));
        $Redis->rm($key);
    }

    /**
     * 微平台合并付款支付列表
     * author hanwenlin
     * date 2018-09-25
     */
    public function pay()
    {
        $host    = I('host', '', 'intval');
        $tradeId = I("tradeid");

        if ($host == 'm') {
            $host = self::__DEFAULT_ACCOUNT;
        }

        if (!$tradeId || !$host) {
            $this->apiReturn(204, [], '参数错误');
        }

        // 判断是否在未获授权的微信中 - 微信支付前需要获得授权
        if ($this->inWechatApp() && !$this->_loginMember['pft_openid']) {
            if (ENV == 'PRODUCTION') {
                $url = $this->getMicroPlatHost() . 'r/MicroPlat_Member/wxOrderAuth';
                $this->apiReturn(206, ['url' => $url]);
            }
        }

        // 订单查询
        $Member = $this->getMemberModel();
        // $Ticket     = $this->getTicketModel();
        $TicketApi = new \Business\JavaApi\Product\Ticket();
        $Order     = new \Model\Order\OrderTools();
        $landModel = new \Model\Product\Land('slave');

        // 账号合法性
        $supply = $Member->getMemberInfo($host, 'account', 'id');
        if (!$supply) {
            $this->apiReturn(204, [], '非法请求');
        }

        $payment = new \Business\Order\MergeOrder();
        if ($payment->isCombineOrder($tradeId)) {
            $mergeOrder = new MergeOrder();
            $ordernums  = $mergeOrder->getMainOrderNumByTradeId($tradeId);
            if (!is_array($ordernums) || empty($ordernums)) {
                $this->apiReturn(204, [], '合并付款子订单不存在');
            }
            $orderInfos = $Order->getOrderInfo($ordernums,
                'status,ordernum,ss.pay_status,tid,ordertime,paymode,tnum,lid,aid,ordername,ordertel,code,pid,totalmoney,begintime,endtime',
                'ext_content,playtime');
            if (!is_array($orderInfos) || empty($orderInfos)) {
                $this->apiReturn(204, [], '订单信息错误');
            }
            $orderData = [];
            foreach ($orderInfos as $item) {
                $orderData[$item['ordernum']] = $item;
            }

            $land = $landModel->getLandInfo($orderInfos[0]['lid'], false, 'p_type,title');

            foreach ($ordernums as $value) {
                $orderInfo = $orderData[$value];
                if (!$orderInfo) {
                    $this->apiReturn(204, [], '订单不存在');
                }

                if (!in_array($orderInfo['status'], [0, 4, 80, 10, 11])) {
                    $this->apiReturn(204, [], '订单状态错误');
                }

                // 订单已支付，跳转支付成功页面
                if ($orderInfo['pay_status'] != 2 && $orderInfo['totalmoney'] != 0) {
                    $this->apiReturn(205, [], '订单已支付');
                }

                // 获取票类信息
                $ticketApi      = new \Business\JavaApi\TicketApi();
                $memberIdAndAid = $ticketApi::getOrderAidByMemberIdAndAid($value, $orderInfo['member'],
                    $orderInfo['aid'], $orderInfo['visitors']);
                $ticket         = $TicketApi->queryTicketById($orderInfo['tid'], $memberIdAndAid['memberId'],
                    $memberIdAndAid['aid'], $this->saleChannel);
                if ($ticket['code'] != 200) {
                    $this->apiReturn(205, [], "获取票属性异常");
                }
                $ticket = $ticket['data'];
                $ticket = $TicketApi->mapAttribute($ticket);

                if (time() - strtotime($orderInfo['ordertime']) > $ticket['cancel_auto_onMin'] * 60) {
                    $this->apiReturn(204, [], '已超过支付时间');
                }
                // 获取支付参数
                $payParams = $this->_getPayParams(
                    $value,
                    $supply,
                    $host,
                    $ticket['title'],
                    $ticket['cancel_auto_onMin'],
                    $orderInfo,
                    $land
                );

                // 更多参数
                $payParams['ptype']             = $payParams['detail']['ptype'];
                $payParams['detail']['paymode'] = $orderInfo['paymode'];
                if ($this->inWechatApp() && $this->_loginMember['pft_openid']) {
                    $payParams['payParams']['appid']  = PFT_WECHAT_APPID;
                    $payParams['payParams']['openid'] = $this->_loginMember['pft_openid'];
                }
                $combineData[] = $payParams;
            }
            $money      = $mergeOrder->getTradeIdTotleMoney($tradeId);
            $resultData = [
                'list'       => $combineData,
                'totalMoney' => $money / 100,
            ];
            $this->apiReturn(200, $resultData);
        } else {
            // 订单合法性
            $ordernum  = $tradeId;
            $orderInfo = $Order->getOrderInfo($ordernum,
                'status,ordernum,ss.pay_status,tid,ordertime,paymode,tnum,lid,aid,ordername,ordertel,code,pid,totalmoney,begintime,endtime',
                'ext_content');
            if (!$orderInfo) {
                $this->apiReturn(204, [], '订单不存在');
            }

            $orderInfo['totalPrice'] = $orderInfo['totalmoney'];

            if (!in_array($orderInfo['status'], [0, 4, 80, 10, 11])) {
                $this->apiReturn(204, [], '订单状态错误');
            }

            // 订单已支付，跳转支付成功页面
            if ($orderInfo['pay_status'] != 2) {
                $this->apiReturn(205, [], '订单已支付');
            }

            // 获取票类信息
            // $ticket = $Ticket->getTicketInfoById($orderInfo['tid'], 'cancel_auto_onMin,title');
            $ticketApi      = new \Business\JavaApi\TicketApi();
            $memberIdAndAid = $ticketApi::getOrderAidByMemberIdAndAid($ordernum, $orderInfo['member'],
                $orderInfo['aid'], $orderInfo['visitors']);
            $ticket         = $TicketApi->queryTicketById($orderInfo['tid'], $memberIdAndAid['memberId'],
                $memberIdAndAid['aid'], $this->saleChannel);
            if ($ticket['code'] != 200) {
                $this->apiReturn(205, [], "获取票属性异常");
            }
            $ticket = $ticket['data'];
            $ticket = $TicketApi->mapAttribute($ticket);

            if (time() - strtotime($orderInfo['ordertime']) > $ticket['cancel_auto_onMin'] * 60) {
                $this->apiReturn(204, [], '已超过支付时间');
            }

            $land = $landModel->getLandInfo($orderInfo['lid'], false, 'p_type,title');

            // 获取支付参数
            $payParams = $this->_getPayParams(
                $ordernum,
                $supply,
                $host,
                $ticket['title'],
                $ticket['cancel_auto_onMin'],
                $orderInfo,
                $land
            );

            // 更多参数
            $payParams['ptype']             = $payParams['detail']['ptype'];
            $payParams['detail']['paymode'] = $orderInfo['paymode'];
            if ($this->inWechatApp() && $this->_loginMember['pft_openid']) {
                $payParams['payParams']['appid']  = PFT_WECHAT_APPID;
                $payParams['payParams']['openid'] = $this->_loginMember['pft_openid'];
            }

            $orderQuery = new \Model\Order\OrderQuery('localhost');
            $totalMoney = $orderQuery->get_order_total_fee((string)$ordernum);

            $combineData[] = $payParams;
            $resultData    = [
                'list'       => $combineData,
                'totalMoney' => $totalMoney / 100,
            ];
            $this->apiReturn(200, $resultData);
        }

    }

    /**
     * 支付是否完成
     *
     * @param  int ordernum 订单号
     *
     * @return
     */
    public function isPayComplete($ordernum = '')
    {
        $ordernum = I('ordernum', '', 'intval');

        if (!$ordernum) {
            $this->apiReturn(204, [], '订单号不存在');
        }

        $return = $this->getOrderPayStatus($ordernum);

        $this->apiReturn(200, $return);
    }

    /**
     * 支付成功页面 - 简要信息
     */
    public function paySuccess()
    {
        $tradeId    = I('post.tradeId');
        $mergeOrder = new MergeOrder();
        $payment    = new \Business\Order\MergeOrder();
        if ($payment->isCombineOrder($tradeId)) {
            $orderNums = $mergeOrder->getMainOrderNumByTradeId($tradeId);
        } else {
            $orderNums[] = $tradeId;
        }

        if (!$orderNums) {
            $this->apiReturn(204, [], '参数错误');
        }

        $Ticket    = $this->getTicketModel();
        $landModel = new \Model\Product\Land('slave');

        $Order      = new \Model\Order\OrderTools();
        $orderInfos = $Order->getOrderInfo($orderNums,
            'status,ordernum,ss.pay_status,tid,ordertime,paymode,tnum,lid,aid,ordername,ordertel,code,pid,totalmoney,begintime,endtime',
            'ext_content,product_ext,playtime');
        if (empty($orderInfos)) {
            $this->apiReturn(204, [], '订单信息不存在');
        }
        $orderData = [];
        foreach ($orderInfos as $item) {
            $orderData[$item['ordernum']] = $item;
        }

        $tids      = array_column($orderInfos, 'tid');
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($tids, 'id,title,pre_sale,apply_did');
        if (!$ticketArr) {
            $this->apiReturn(204, [], '门票信息获取失败');
        }
        $ticketData = [];
        foreach ($ticketArr as $ticketInfos) {
            $ticketData[$ticketInfos['ticket']['id']] = [
                'id'       => $ticketInfos['ticket']['id'],
                'title'    => $ticketInfos['ticket']['title'],
                'pre_sale' => $ticketInfos['ticket']['pre_sale'],
                'apply_did'=> $ticketInfos['ticket']['apply_did'],
            ];
        }

        //$land = $landModel->getLandInfo($orderInfos[0]['lid'], false, 'p_type,title');
        $lids = array_column($orderInfos, 'lid');
        $lids = array_unique($lids);
        $lids = array_values($lids);
        $landBiz      = new LandJavaBiz();
        $landsInfoRes = $landBiz->queryLandByIds($lids);
        $landMap = [];
        if ($landsInfoRes['code'] != 200 || empty($landsInfoRes['data'])) {
            $this->apiReturn(204, [], '景区信息获取失败');
        }

        foreach ($landsInfoRes['data'] as $item) {
            $landMap[$item['id']] = [
                'title' => $item['title'],
                'p_type' => $item['pType'],
            ];
        }

        $totalMoney = 0;
        foreach ($orderNums as $key => $ordernum) {
            $orderInfo = $orderData[$ordernum];
            $land = $landMap[$orderInfo['lid']] ?? [];
            $return    = $this->_getOrderDetail($ordernum, $orderInfo, $land, $ticketData[$orderInfo['tid']]['title'], $ticketData[$orderInfo['tid']]['apply_did'], $ticketData[$orderInfo['tid']]['pre_sale']);
            //赋值期票属性
            $return['pre_sale'] = $ticketData[$orderInfo['tid']]['pre_sale'];
            // 关注公众号
            $wxOpenModel = new \Model\Wechat\WxOpen();
            $account     = explode('.', $_SERVER['HTTP_HOST'])[0];
            $info        = $wxOpenModel->getWechatOffiAccInfo((int)$account, 'account');
            if ($info) {
                $return['alert']      = 1;
                $return['qrcode_url'] = $info['qrcode_url'];
                $return['nick_name']  = $info['nick_name'];
            } else {
                $return['alert'] = 0;
            }
            $returns['list'][] = $return;

            $totalMoney += $orderInfo['totalmoney'];
        }
        $returns['paymode'] = $returns['list'][0]['paymode'];

        //用来确认是不是第三方产品，调用Java接口, sourcet= [2,3]就是第三方
        $ticketIds = array_column($orderInfos,'tid'); //票ids
        $ticketIds = implode(',', $ticketIds);
        $isThird = (new \Business\JavaApi\Ticket\ThirdAttr())->isThirdTickets($ticketIds);
        $returns['is_third']  = $isThird;

        $this->apiReturn(200, $returns);
    }

    /**
     * 订单确认
     * <AUTHOR>
     * @date 2020/9/28
     *
     */
    public function orderConfirm()
    {
        $orderNumStr = I('ordernum', '', 'strval');
        $stuffId     = I('stuff_id', 0, 'intval');
        $siteId      = I('site_id', 0, 'intval');

        $sid     = $this->_loginMember['sid'];
        $mid     = $this->_loginMember['memberID'];
        $account = $this->_loginMember['account'];

        if (empty($orderNumStr)) {
            $this->apiReturn(203, [], '订单号不存在');
        }

        $hotelBiz = new \Business\Order\Modify();
        $result = $hotelBiz->handleOrderConfirm($orderNumStr, $sid, $mid, $account, $stuffId, $siteId);
        if (!isset($result['code'])) {
            $this->apiReturn(500, [], '异常出错');
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 订单拒绝
     * <AUTHOR>
     * @date 2020/9/28
     *
     */
    public function orderRefundConfirm()
    {
        $orderNumStr     = I('ordernum', '', 'strval');
        $reqSerialNumber = I('req_serial_number', '', 'strval');

        $sid     = $this->_loginMember['sid'];
        $mid     = $this->_loginMember['memberID'];
        $account = $this->_loginMember['account'];

        if (empty($orderNumStr)) {
            $this->apiReturn(203, [], '订单号不存在');
        }

        $hotelBiz = new \Business\Order\Modify();
        $result = $hotelBiz->handleOrderRefundConfirm($orderNumStr, $sid, $mid, $account, $reqSerialNumber);
        if (!isset($result['code'])) {
            $this->apiReturn(500, [], '异常出错');
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取订单待确认信息
     * <AUTHOR>
     * @date 2020/9/28
     *
     */
    public function getOrderConfirmInfo()
    {
        $ordernum = I('post.ordernum', '', 'strval');
        if (empty($ordernum)) {
            $this->apiReturn(203, [], '订单号不存在');
        }

        $hotelBiz = new \Business\Order\Modify();
        $result   = $hotelBiz->getOrderConfirmInfoByOrdernum($ordernum);
        if (!isset($result['code'])) {
            $this->apiReturn(500, [], '异常出错');
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取订单状态
     *
     * @param  [type] $mobile 手机号
     *
     * @return [type]         [description]
     */
    private function getOrderPayStatus($ordernum = '')
    {
//        $payMapping = (new \Model\Order\OrderTools())->getOrderPayStatus([$ordernum]);
        $queryParams = [[$ordernum],true,'pay_status'];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder','queryOrderInfoByOrdernumList', $queryParams);
        $payMapping = [];
        if ($queryRes['code'] == 200) {
            $payMapping = $queryRes['data'];
        }
        if (in_array($payMapping[$ordernum], [0, 1])) {
            $return['payStatus'] = 1;
        } else {
            $return['payStatus'] = 0;
        }

        return $return;
    }

    /**
     * 手机号检测
     *
     * @param  [type] $mobile 手机号
     *
     * @return [type]         [description]
     */
    private function _checkMobile($mobile, $ordername)
    {
        if (!\ismobile($mobile)) {
            $this->apiReturn(204, [], '请填写正确的手机号');
        }

        return true;
    }

    /**
     * 账号权限检测
     * <AUTHOR>
     * @time   2017-03-10
     */
    private function _checkAuth($memberId)
    {
        if (!$memberId || !is_numeric($memberId)) {
            $this->returnAutoLogin('请先登录');
        }

        $member = $this->getMemberModel('master')->getMemberInfo($memberId);
        $dtype  = $member['dtype'];

        // 验证账号下单权限
        if ($dtype == 6) {
            // 员工账号
            $this->checkSonAuth('bool');
        }

        return true;
    }

    /**
     * 组合下单接口需要的参数
     * author hanwenlin
     * time   2018-09-28
     *
     * @return [type] [description]
     */
    private function _combineOptions()
    {
        $todayTime = strtotime(date('Y-m-d'));
        $tmpBegin  = strtotime(I('post.begintime'));
        $beginTime = $todayTime;
        if ($tmpBegin && $tmpBegin >= $todayTime) {
            $beginTime = $tmpBegin;
        }

        $tModel = $this->getTicketModel();

        // 员工账号判断
        if ($this->isSonLogin()) {
            $mModel     = $this->getMemberModel();
            $memberInfo = $mModel->getMemberInfo($this->_loginMember['sid'], 'id', 'account');
            $account    = $memberInfo['account'];
            $operateId  = $this->_loginMember['memberID'];
        }
        $payMode = I('post.paymode', 1);
        // 支付方式判断
        if (!is_numeric($payMode) && !in_array($payMode, [0, 1, 2, 3, 4, 5, 7, 9])) {
            $this->apiReturn(204, [], '支付方式错误');
        }
        $proidList = I('post.proid');
        if (!is_array($proidList)) {
            $this->apiReturn(204, [], '产品列表错误');
        }
        $orderName  = I('post.ordername');
        $orderTel   = I('post.contacttel');
        $memo       = I('post.memo', '');
        $idCardInfo = I('post.idCardInfo');

        $beginDate = date('Y-m-d', $beginTime);
        foreach ($proidList as $key => $value) {
            if (empty($key)) {
                $this->apiReturn(204, [], '产品id错误');
            }
            $this->_tempOptions = [
                'pid'        => $key,
                'tnum'       => intval($value['tnum']),
                'begintime'  => $beginDate,
                'ordername'  => $orderName,
                'contacttel' => $orderTel,
                'ordertel'   => $orderTel,
                'memo'       => $memo,
                'aid'        => intval($value['aid']),
                'ordermode'  => self::__ORDER_MODE__,
                'paymode'    => $payMode,
            ];

            if (isset($account)) {
                $this->_tempOptions['account'] = $account;
            }

            if (isset($operateId)) {
                $this->_tempOptions['operateId'] = $operateId;
            }
            // 表单数据校验
            $this->_checkFormData();
            //新版接口，[tid=>[['name'=>'xxx','idcard'=>'xxx'],['name'=>'xxx','idcard'=>'xxx']]]
            if ($idCardInfo) {
                $this->_tempOptions['sfz'] = $idCardInfo;
            }
            //不同的产品类型可能需要一些额外的参数
            $pType = $tModel->getProductType($this->_tempOptions['pid']);
            $this->_checkforDifferentType($pType, $value);
            $this->_options[] = $this->_tempOptions;
        }
    }

    /**
     * 校验下单数据
     * @return [type] [description]
     */
    private function _checkFormData()
    {
        if (!$this->_tempOptions['pid']) {
            $this->apiReturn(204, [], '产品错误');
        }

        if (!$this->_tempOptions['tnum']) {
            $this->apiReturn(204, [], '主票购买数量不能为空');
        }

        if (!$this->_tempOptions['begintime']) {
            $this->apiReturn(204, [], '开始时间不能为空');
        }

        if (!$this->_tempOptions['ordername']) {
            $this->apiReturn(204, [], '联系人不能为空');
        }

        if (!$this->_tempOptions['contacttel']) {
            $this->apiReturn(204, [], '联系电话不能为空');
        }
    }

    /**
     * 身份证和游客姓名检测
     *
     * @param  [type] $ids   身份证
     * @param  [type] $names 游客姓名
     *
     * @return [type]        [description]
     */
    private function _checkIdAndNames($ids, $names)
    {
        $ids_arr   = $ids;
        $names_arr = $names;

        if (count($ids_arr) != count($names_arr)) {
            $this->apiReturn(204, [], '游客身份证信息和姓名不匹配');
        }

        $exists = [];
        foreach ($ids_arr as $key => $id) {
            if (!\idcard_checksum18($id)) {
                $this->apiReturn(204, [], '请填写正确的身份证信息:' . $id);
            }

            if (in_array($id, $exists)) {
                $this->apiReturn(204, [], '身份证重复:' . $id);
            }

            $ids_arr[$key] = \safetxt($id);
        }

        foreach ($names_arr as $key => $name) {
            $names_arr[$key] = \safetxt($name);
        }

        $this->_tempOptions['idcards']  = $ids_arr;
        $this->_tempOptions['tourists'] = $names_arr;
    }

    /**
     * 联票检测
     *
     * @param  array ['pid' => num,]
     *
     * @return [type] [description]
     */
    private function _checkLianTicket($link)
    {
        //Todo : 是否能下联票

        foreach ($link as $pid => $num) {
            if ((int)$num < 1) {
                unset($link[$pid]);
            }
        }

        $this->_options['c_pids'] = $link;

    }

    /**
     * 不同类型的产品检测
     *
     * @param  [type] $pType 产品类型
     *
     * @return [type]        [description]
     */
    private function _checkforDifferentType($pType, $value = false)
    {
        switch ($pType) {
//            case 'A':
//                $this->_checkforAType(); //固定有效期相关暂时注释
//                break;
            case 'C':
                $this->_checkforCType();
                break;

            case 'H':
                $this->_checkforHType($value);
                break;

            default:
                break;
        }
    }

    /***
     * 景区类型
     * <AUTHOR> Yiqiang
     * @date 2018-10-09
     * 固定有效期相关 暂时弃用
     *
     * @param $key
     */
    private function _checkforAType()
    {
        //todo::针对景区固定有效期的产品修改游玩时间为下单时间(即忽略游玩时间)
        $ticModel = new Ticket('slave');
        $tInfo    = $ticModel->getTicketInfoByPid($this->_tempOptions['pid']);

        if (!$tInfo) {
            $this->apiReturn(204, [], '门票信息获取失败');
        }

        if (strtotime($tInfo['order_start']) > 0) {
            $this->_tempOptions['playtime'] = time() > strtotime($tInfo['order_start']) ? date('Y-m-d',
                time()) : $tInfo['order_start'];
        }
    }

    /**
     * 酒店
     * @return [type] [description]
     */
    private function _checkforCType()
    {
        if (I('begintime') >= I('endtime')) {
            $this->apiReturn(204, [], '请选择正确的离店时间');
        }

        $this->_tempOptions['leavetime'] = I('endtime');
    }

    /**
     * 演出类型
     * @return [type] [description]
     */
    private function _checkforHType($value = false)
    {
        $roundId = $value['roundid'];
        $venusId = $value['venusid'];
        $zoneId  = $value['zoneid'];
        $seatIds = $value['seat_ids'];

        if (!($roundId && $venusId)) {
            $this->apiReturn(204, [], '场次信息缺失');
        }

        $Pro     = $this->_getProObject($this->_tempOptions['pid'], $this->_tempOptions['aid']);
        $product = $Pro->pInfo();
        if (!$product) {
            $this->apiReturn(204, [], '产品信息错误');
        }

        if ($venusId != $product['venus_id'] || $zoneId != $product['zone_id']) {
            $this->apiReturn(204, [], '场次信息错误');
        }

        $this->_tempOptions['series'] = json_encode([$venusId, $roundId, $zoneId, $seatIds]);
    }

    /**
     * 根据手机号解析用户id
     *
     * @param  int $mobile 手机号
     *
     * @return int 用户id
     */
    private function _parseMemberIdByMobile($mobile)
    {
        $memberInfo = $this->getMemberModel()->getMemberInfo(
            $this->_options['contacttel'],
            'mobile'
        );

        return $memberInfo['id'];
    }

    /**
     * 存放近一个小时的下单信息
     *
     * @param  int $ordernum 订单号
     * @param  int $supply 供应商id
     *
     * @return [type]
     */
    private function _storePayInfo($ordernum, $supply)
    {
        $buyInfo = [];
        if (isset($_COOKIE['weather1'])) {
            $buyInfo = json_decode(base64_decode($_COOKIE['weather1']), true);
            $buyInfo = $buyInfo ?: [];
        }

        $buyInfo[] = $supply . '-' . $ordernum;
        $buyInfo   = base64_encode(json_encode($buyInfo));

        setcookie('weather1', $buyInfo, time() + 3600, '/');
    }

    /**
     * 获取支付参数
     *
     * @param  string $ordernum 订单号
     * @param  array $supply 供应商账号信息
     * @param  string $host 供应商账号
     * @param  string $ttitle 门票名称
     * @param  int $autoCancel 自动取消时间
     *
     * @return array
     */
    private function _getPayParams($ordernum, $supply, $host, $ttitle, $autoCancel, $orderData = false, $land = false)
    {
        $return = [
            'payWay' => [
                'ali' => 1,
                'wx'  => 1,
                'uni' => 1,
            ],
        ];

        if (!$this->inWechatApp()) {
            // 不在微信app内 禁止使用
            $return['payWay']['wx'] = 0;
        }

        $detail = $this->_getOrderDetail($ordernum, $orderData, $land, $ttitle);

        unset($detail['qrcode']);
        $return['detail'] = $detail;

        //三亚的需要特殊处理下
        if ($host == self::__DEFAULT_SY_ACCOUNT) {
            // 三亚需要做特殊处理
            $jumpDomain = str_replace('wx', self::__DEFAULT_SY_ACCOUNT, MOBILE_DOMAIN);
        } else {
            // 统一都跳转到微平台 m.12301.cc 去
            $jumpDomain = str_replace('wx', 'm', MOBILE_DOMAIN);
        }

        $payParams = [
            'subject'    => $detail['landTitle'] . $ttitle,
            'expireTime' => $autoCancel,
            'outTradeNo' => $ordernum,
            'buyId'      => $supply['id'],
            'domain'     => $jumpDomain,
        ];

        if ($return['payWay']['wx']) {
            // 获取 appid 和 openid
            $payParams['appid']  = $this->_loginMember['wechat_appid'];
            $payParams['openid'] = $this->_loginMember['openid'];
        }

        $return['payParams'] = $payParams;

        return $return;
    }

    /**
     * 获取订单详细信息
     *
     * @param  int $ordernum 订单号
     *
     * @return array
     */
    private function _getOrderDetail($ordernum = false, $orderData = false, $land = false, $ttitle = false, $aid = false, $preSale = false)
    {
        $orderInfo       = $orderData;
        $orderExtra      = [];
        $externalCodeMap = [];
        //景区类型
        $pType = $land['p_type'];
        if ($pType == 'H') {
            //演出类型需要获取场次信息
            $Order                  = new \Model\Order\OrderTools();
            $orderExtra             = $Order->getOrderDetailInfo($ordernum);
            $orderExtra['pre_sale'] = $preSale;
        } elseif ($pType == 'J') {
            //演出类型需要获取场次信息
            $Order      = new \Model\Order\OrderTools();
            $orderExtra = $Order->getOrderDetailInfo($ordernum);

            //特产类型，需要展示收货/取货信息
            $productExt = json_decode($orderExtra['product_ext'], true);

            //快递还是自提,0快递自提
            $deliveryType = $productExt['deliveryType'] ?? 1;
            $expInfo      = $selfPickup = [];
            if ($deliveryType == 0) {
                //获取收货人信息
//                $orderUserModel = new \Model\Order\OrderUser();
//                $orderUserInfo  = $orderUserModel->getOneOrderUserByOrdernum($ordernum,
//                    'province_code,city_code,town_code,address');
                $queryParams = [[$ordernum]];
                $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderUserInfo','getOrderUserInfoByOrderNum', $queryParams);
                $orderUserInfo = [];
                if ($queryRes['code'] == 200) {
                    $orderUserInfo = $queryRes['data'][0];
                }

                $areaCodeArr    = [
                    $orderUserInfo['province_code'],
                    $orderUserInfo['city_code'],
                    $orderUserInfo['town_code'],
                ];
                $areaCodeArr    = array_filter($areaCodeArr);
                if ($areaCodeArr) {
                    $areaModel = new \Model\Product\Area();
                    $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
                } else {
                    $codeMap = [];
                }

                //快递物流信息
                $expInfo = [
                    'carriage'      => $productExt['carriage'] ?? 0,
                    'province_code' => $codeMap[$orderUserInfo['province_code']] ?? '',
                    'city_code'     => $codeMap[$orderUserInfo['city_code']] ?? '',
                    'town_code'     => $codeMap[$orderUserInfo['town_code']] ?? '',
                    'address'       => $orderUserInfo['address'] ?? '',
                ];
            } else {
                $specialApi = new \Business\JavaApi\Ticket\SpecialtyTicket();
                $res        = $specialApi->querySpecialGoodsDeliveryInfo($orderInfo['tid']);
                if ($res['code'] == 200) {
                    $pickPointData = $res['data']['pickPoint'];
                    $areaCodeArr   = explode('|', $pickPointData['areaCode']);
                    $areaCodeArr   = array_filter($areaCodeArr);
                    if ($areaCodeArr) {
                        $areaModel = new \Model\Product\Area();
                        $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
                    } else {
                        $codeMap = [];
                    }
                    //自提点信息
                    $selfPickup = [
                        'area'           => implode('', array_values($codeMap)),
                        'address'        => $pickPointData['address'],
                        'linkman'        => $pickPointData['linkman'] . '/' . $pickPointData['phone'],
                        'reception_time' => $pickPointData['acceptTime'],
                        'no_pick_cancel' => $res['data']['noPickAutoCancel'],
                    ];
                } else {
                    $selfPickup = [];
                }
            }

            $specialInfo = ['delivery_way' => $deliveryType, 'exp_info' => $expInfo, 'pick_info' => $selfPickup];
        }

        //获取包含的门票信息
        $tickets[] = [
            'title' => $ttitle,
            'num'   => $orderInfo['tnum'],
        ];

        $timeShareBiz = new \Business\Product\TimeShare();

        //获取分时预约数据
        $extContent           = json_decode($orderInfo['ext_content'], true);
        $packageTimeShareInfo = $extContent['packageTimeShareInfo'];
        //演出支持套票订单
        $packageShowInfo      = isset($extContent['packageShowInfo']) ? $extContent['packageShowInfo'] : [];
        $childListInfo        = [];
        //套票分时和演出子票时 需要查询子票信息
        if (($packageTimeShareInfo || $packageShowInfo) && $pType == 'F') {
            //$javaApi = new \Business\JavaApi\Product\PackageTicket();
            //$childListInfo  = $javaApi->queryPageTicketInfoListByParentId($orderInfo['tid']);
            $packApi       = new \Business\PackTicket\PackRelation();
            $childListInfo = $packApi->queryPageTicketInfoListByParentId($orderInfo['tid']);
        }
        //分时处理
        if ($packageTimeShareInfo && $pType == 'F') {
            //$ticketApi = new TicketApi();
            //$result    = $ticketApi->getSonTicketList($orderInfo['tid']);
            if (!empty($childListInfo['data'])) {
                $orderModel = new \Model\Order\OrderTools('slave');
                $childOrder = $orderModel->getPackChildOrders($ordernum, 'orderid');

                if ($childOrder) {
                    $childOrderArr  = array_column($childOrder, 'orderid');
                    $childOrderInfo = $orderModel->getOrderInfo($childOrderArr, 'lid, status');
                    $childOrderInfo = array_column($childOrderInfo, null, 'tid');
                }

                $childList = array_key($childListInfo['data'], 'ticket_id');
                foreach ($packageTimeShareInfo as $key => $value) {
                    if (isset($childList[$key])) {
                        $packageTimeShareInfo[$key]['ticket_name'] = $childList[$key]['ticket_name'];
                        $packageTimeShareInfo[$key]['item_name']   = $childList[$key]['item_name'];
                    }
                    if (isset($childOrderInfo[$key])) {
                        $childExtContent                        = json_decode($childOrderInfo[$key]['ext_content'], true);
                        $packageTimeShareInfo[$key]['time_str'] = isset($childExtContent['sectionTimeStr']) ? $childExtContent['sectionTimeStr'] .= $timeShareBiz->getTimeShareDelayTime($childExtContent['sectionTimeStr'], $childExtContent['sectionDelayCheckInTime'] ?? 0, $childExtContent['sectionAheadCheckInTime'] ?? 0) : '';
                    }
                }
            }
        }
        //套票含演出子票处理
        if ($packageShowInfo && $pType == 'F') {
            if (!empty($childListInfo['data'])) {
                $childList       = array_key($childListInfo['data'], 'ticket_id');
                $packBiz         = new \Business\Product\PackTicket();
                $packageShowInfo = $packBiz->handlePaySuccessParams($packageShowInfo, $childList, $orderInfo);
            }
        }

        $productExt = json_decode($orderInfo['product_ext'], true);
        //外部码发码判断，h5微平台用到
        if (isset($productExt['externalSendCode']) && $productExt['externalSendCode'] == 1) {  //代表有发码
            $externalBiz = new \Business\ExternalCode\CodeManage();
            $externalRes = $externalBiz->getExternalCodeInfoByOrdernum($aid, $ordernum);
            if (isset($externalRes['code']) && $externalRes['code'] == 200) {
                //查询是否设置游玩日期
                $javaApi   = new \Business\CommodityCenter\Ticket();
                $ticketArr = $javaApi->batchQueryTicketByTicketIds([$orderInfo['tid']], 'pre_sale');
                $preSale    = $ticketArr[0]['pre_sale']; //是否选择游玩日期 1是2否

                //有效期开始和结束时间的处理
                $expireType = $externalRes['data']['expire_type']; //发送给游客的有效期：1导码设置的有效期；2下单选择的游玩日期；3固定有效期
                $orderTime  = $orderInfo['ordertime']; //下单时间
                $playTime   = $orderInfo['playtime']; //游玩时间
                $expireTime = $externalRes['data']['expire_time'];  //外部码过期时间， 0为长期有效
                $startDt    = $externalRes['data']['start_dt']; //固定日期的开始时间
                $endDt      = $externalRes['data']['end_dt']; //固定日期的结束时间
                //有效期处理
                $handleRes = $externalBiz->handleExternalCodeOrderTime($expireType, $preSale, $orderTime, $playTime, $expireTime, $startDt, $endDt);
                if (isset($handleRes['code']) && $handleRes['code'] == 200) {
                    $orderInfo['begintime']  = $handleRes['data']['begin_time'];
                    $orderInfo['endtime']    = $handleRes['data']['end_time'] != 0 ? $handleRes['data']['end_time'] : '长期有效';
                }
            }
        }


        //根据景区类型不同，获取一些额外的展示信息
        $extra = $this->_getExtraInfo(
            $pType,
            $orderInfo,
            $orderExtra
        );

        return [
            'lid'                     => $orderInfo['lid'],
            'tid'                     => $orderInfo['tid'],
            'aid'                     => $orderInfo['aid'],
            'ordernum'                => $ordernum,
            'paymode'                 => $orderInfo['paymode'],
            'pid'                     => $orderInfo['pid'],
            'ptype'                   => $land['p_type'],
            'landTitle'               => $land['title'],
            'ordername'               => $orderInfo['ordername'],
            'ordertel'                => $orderInfo['ordertel'],
            'qrcode'                  => $orderInfo['code'],
            'tickets'                 => $tickets,
            'extra'                   => $extra,
            'time_share_order'        => isset($extContent['sectionTimeStr']) ? $extContent['sectionTimeStr'] .= $timeShareBiz->getTimeShareDelayTime($extContent['sectionTimeStr'], $extContent['sectionDelayCheckInTime'] ?? 0, $extContent['sectionAheadCheckInTime'] ?? 0) : '',
            'package_time_share_info' => $packageTimeShareInfo ?: "",
            'package_show_info'       => $packageShowInfo,
            'specialInfo'             => $specialInfo,
            'status'                  => intval($orderInfo['status']),
        ];
    }

    /**
     * 获取订单的门票信息
     *
     * @param  int $ordernum 主票订单号
     * @param  int $tid 主票tid
     * @param  int $tnum 主票票数
     * @param  boolean $link 是否是联票
     *
     * @return [type]            [description]
     */
    private function _getOrderTickets($ordernum, $tid, $tnum, $link = false)
    {
        if ($link) {
            $field   = 'ss.tid,ss.tnum';
            $tickets = (new \Model\Order\OrderTools())->getLinkOrdersInfo($ordernum, $field);
        } else {
            $tickets[] = [
                'tid'  => $tid,
                'tnum' => $tnum,
            ];
        }

        $Model = $this->getTicketModel('master');

        $return = [];
        foreach ($tickets as $item) {
            $title = $Model->getTicketInfoById($item['tid'], 'title');

            $return[] = [
                'title' => $title['title'],
                'num'   => $item['tnum'],
            ];
        }

        return $return;
    }

    /**
     * 根据票种获取不同的订单信息
     *
     * @param  string $type 类型
     * @param  array $orderInfo 订单信息
     * @param  array $orderExtra 订单额信息
     *
     * @return [type]             [description]
     */
    private function _getExtraInfo($type, $orderInfo, $orderExtra)
    {
        switch ($type) {
            case 'A': // 景区
            case 'F': // 套票
                return $this->_getExtraInfoForLand($orderInfo);

            case 'B': // 线路
                return $this->_getExtraInfoForRoad($orderInfo);

            case 'C': // 酒店
                return $this->_getExtraInfoForHotel($orderInfo);

            case 'H': // 演出
                return $this->_getExtraInfoForShow($orderExtra, $orderInfo);

            case 'G': // 餐饮
                return $this->_getExtraInfoForCatering($orderExtra);
            case 'J': // 特产
                return $this->_getExtraInfoForSpecial($orderExtra, $orderInfo);

            default:
                break;
        }
    }

    /**
     * 获取景区订单的信息
     *
     * @param  array $orderInfo 订单信息
     *
     * @return [type]            [description]
     */
    private function _getExtraInfoForLand($orderInfo)
    {
        //有效时间
        $date = $orderInfo['begintime'] . '~' . $orderInfo['endtime'];

        return [
            'date' => $date,
        ];
    }

    /**
     * 获取线路订单的信息
     *
     * @param  array $orderInfo 订单信息
     *
     * @return [type]            [description]
     */
    private function _getExtraInfoForRoad($orderInfo)
    {
        //集合时间
        $date = $orderInfo['begintime'];

        $Ticket = $this->getTicketModel('master');
        $ticket = $Ticket->getTicketExtInfoByTid($orderInfo['tid'], 'ass_station');

        //集合地点
        $station = json_decode($ticket['ass_station'], true);

        return [
            'date'    => $date,
            'station' => array_values($station),
        ];
    }

    /**
     * 获取酒店订单的信息
     *
     * @param  array $orderInfo 订单信息
     *
     * @return [type]            [description]
     */
    private function _getExtraInfoForHotel($orderInfo)
    {
        $link = (new \Model\Order\OrderTools())->getLinkOrdersInfo($orderInfo['ordernum'], 'playtime');

        $last = array_pop($link);

        if (!$last) {
            $last['playtime'] = $orderInfo['begintime'];
        }

        $begintime = strtotime($orderInfo['begintime']);
        $endtime   = strtotime($last['playtime']) + 3600 * 24;

        //住店时间
        $date = $orderInfo['begintime'] . '~' . date('Y-m-d', $endtime);

        //住店天数
        $days = ($endtime - $begintime) / 3600 / 24;

        return [
            'date' => $date,
            'days' => $days,
        ];
    }

    /**
     * 获取演出订单的信息
     *
     * @param  [type] $orderExtra 订单额外信息
     *
     * @return [type]             [description]
     */
    private function _getExtraInfoForShow($orderExtra, $orderInfo = '')
    {
        $date = '';
        $seat = '';
        if (isset($orderExtra['pre_sale']) && $orderExtra['pre_sale'] !== false && $orderExtra['pre_sale'] == 0) {
            $series = unserialize($orderExtra['series']);

            //演出日期
            $date = $series[11] ?? $series[4];

            //座位
            $seat = explode(',', $series[6])[2];

            $seatNew = $series[5];

            if (!empty($orderInfo)) {
                $tid           = $orderInfo['tid'];
                $ticketExtInfo = [];
                $ticketBiz     = new \Business\JavaApi\CommodityCenter\Ticket();
                $getTicketInfo = $ticketBiz->queryTicketAttrsById($tid);
                if ($getTicketInfo['code'] == 200) {
                    foreach ($getTicketInfo['data'] as $attr) {
                        if ($attr['key'] == 'is_show_seat') {
                            $ticketExtInfo[$attr['ticket_id']] = $attr;
                        }
                    }
                }
                //$ticketModel   = new \Model\Product\Ticket();
                //$ticketExtInfo = $ticketModel->getTicketExtConfig([$orderInfo['tid']], 'ticket_id,key,val', ['is_show_seat']);
                if (!empty($ticketExtInfo[$tid]['val']) && $ticketExtInfo[$tid]['val'] == 2) {
                    $seatNew = '';
                }
            }

            //获取座位分区
            $seatPlaceNew = '';
            $seatPlaceArray = explode(',', $series[6]);
            if (!empty($seatPlaceArray[1])) {
                $seatPlaceNewArray = explode(':', $seatPlaceArray[1]);
                if (!empty($seatPlaceNewArray[1])) {
                    $seatPlaceNew = "({$seatPlaceNewArray[1]})";
                }
            }

            $showBiz = new \Business\Product\Show();
            $seat    = $showBiz->handleSeatParamsFenQu($seatNew, $seatPlaceNew);

            $return = [
                'date' => $date,
                'seat' => $seat,
            ];
        } else {
            $reserveTime = '';
            if (!empty($orderInfo)) {
                $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
                $ticketRes          = $commodityTicketBiz->queryTicketInfoById($orderInfo['tid']);
                if ($ticketRes) {
                    $ticketInfo = array_merge($ticketRes['land'], $ticketRes['ext'], $ticketRes['land_f'], $ticketRes['ticket']);
                    $reserveTime = (new \Business\Product\Ticket())->showReserveTime($ticketInfo);
                }
            }

            $return = [
                'date'        => $date,
                'seat'        => $seat,
                'reserve_time' => $reserveTime,
            ];
        }
        return $return;
    }

    /**
     * 获取餐饮订单的信息
     *
     * @param  [type] $orderExtra 订单额外信息
     *
     * @return [type]             [description]
     */
    private function _getExtraInfoForCatering($orderExtra)
    {
        return [
            'date' => '',
        ];
    }

    /**
     *  获取特产订单的信息
     * <AUTHOR>
     * @date 2020/2/22
     *
     * @param $orderExtra
     * @param $orderInfo
     *
     * @return array
     */
    private function _getExtraInfoForSpecial($orderExtra, $orderInfo)
    {
        $series = $orderExtra['series'];
        $shipId = $tempShipId = 0;
        if ($series) {
            [$shipId, $expressWay] = explode('|', $series);
            if ($expressWay == 0) {
                $tempShipId = 0;
            } else {
                $tempShipId = $shipId;
            }
        }
        $return['shippingId'] = $shipId;
        $specialBiz           = new \Business\Product\Specialty();
        $special              = $specialBiz->parsePackInfo($orderInfo['tid'], $tempShipId, $orderInfo['ordernum']);
        if ($special['code'] == 200) {
            return [
                'special'         => $special['data'],
                'express_pay_way' => $expressWay,
            ];
        }

        return [
            'date' => '',
        ];
    }

    /**
     * 订单查询(不进行渠道过滤) - 新版订单查询
     *
     * <AUTHOR>
     * @date  2020-12-19
     *
     */
    public function orderQuery()
    {
        $loginInfo = $this->_loginMember;
        $sdtype    = $loginInfo['sdtype'];

        if ($sdtype == 2) {
            //资源方账号查询
            $orderRes = $this->_scenicOrderQuery();
        } else {
            //供应商/分销商账号查询
            $orderRes = $this->_businessOrderQuery();
        }

        $this->apiReturn($orderRes['code'], $orderRes['data'], $orderRes['msg']);
    }

    /**
     * 资源方账号查询
     * <AUTHOR>
     * @date 2020/12/18
     *
     * @return array
     */
    private function _scenicOrderQuery()
    {
        $loginInfo = $this->_loginMember;
        $memberId  = $loginInfo['memberID'];
        $sid       = $loginInfo['sid'];
        $saccount  = $loginInfo['saccount'];

        $baseQueryParam = I('post.');
        $queryParamRes  = $this->_handleParams($baseQueryParam);

        if ($queryParamRes[0] != 200) {
            return $this->returnData(204, $queryParamRes[1]);
        }

        //处理过的请求参数
        $queryParam = $queryParamRes[2];

        if ($queryParam['size'] > 100) {
            return $this->returnData(204, '获取条数超出范围');
        }

        $queryBiz = new Query();
        $queryRes = $queryBiz->searchScenicList($memberId, $sid, $saccount, false, false,
            $queryParam['orderStart'], $queryParam['orderEnd'], $queryParam['dtimeStart'], $queryParam['dtimeEnd'],
            $queryParam['page'], $queryParam['size'], $queryParam['ordernum'], $queryParam['remoteOrder'],
            $queryParam['thirdOrder'], $queryParam['status'], $queryParam['mobile'], $queryParam['idcard'],
            $queryParam['ordername'], $queryParam['lid'], $queryParam['memberRelationship'],true, false);

        if ($queryRes['code'] != 200) {
            return $this->returnData(204, $queryRes['msg']);
        }

        //剥离旅游券的订单
        $orderListOldWith = [];
        $orderInfo        = [];
        foreach ($queryRes['data'] as $orderListValueNew) {
            if ($orderListValueNew['p_type'] == 'Q') {
                continue;
            }
            $orderListOldWith[] = $orderListValueNew['ordernum'];
            $orderInfo[]        = $orderListValueNew;
        }

        $listWithMap = [];
        if (!empty($orderListOldWith) && !empty($orderInfo)) {
            //处理渠道
            $list = $this->_dealData($sid, $orderInfo);

            //取票状态处理
            $data = array_values($list);

            //资源账号
            $accountKey = "land_account:applydid:ziyuanhao:{$saccount}";
            $redis      = $this->getRedisObject();
            $applyDid   = $redis->get($accountKey);

            if (!$applyDid) {
                $landApi  = new \Business\CommodityCenter\Land();
                $landInfoRes = $landApi->queryLandMultiQueryByAdminAndPaging([], 1, 1, '',
                    'id desc', false, [], [], [], [], [$saccount]);
                if (!$landInfoRes['list']) {
                    return $this->returnData(404, '无该账号信息');
                }
                $landInfo = $landInfoRes['list'][0];

                //供应商id
                $applyDid = $landInfo['apply_did'] ?? 0;
                if (!$applyDid) {
                    return $this->returnData(404, '无供应商信息');
                }
                //设置缓存
                $redis->set($accountKey, $applyDid, '', 1800);
            }

            //获取appply名称
            $dnameKey = "land_account:member_dname:ziyuanhao:{$applyDid}";
            $dname    = $redis->get($dnameKey);
            if (!$dname) {
                $info  = (new \Business\Member\Member())->getInfo($applyDid);
                $dname = empty($info['dname']) ? '' : $info['dname'];
                if (empty($dname)) {
                    return $this->returnData(404, '无供应商信息');
                }

                //设置缓存
                $redis->set($dnameKey, $dname, '', 1800);
            }

            //资源号展示供销关系强制处理
            foreach ($data as &$item) {
                $item['buyer']    = '散客';
                $item['buyerid']  = 0;
                $item['seller']   = $dname;
                $item['sellerid'] = 0;
            }

            $data = (new OrderList())->_handleOrderListPrintstate($data);

            $listWithMap = array_key($data, 'ordernum');
        }

        //其他类型的订单和旅游券订单聚合显示
        $listNewWith = [];
        foreach ($queryRes['data'] as $orderListTmpWith) {
            if ($orderListTmpWith['p_type'] == 'Q') {
                $listNewWith[] = $this->handleLvOrderList($orderListTmpWith['ordernum'], $orderListTmpWith['p_type']);
            } else {
                $listNewWith[] = $listWithMap[$orderListTmpWith['ordernum']];
            }
        }

        $this->apiReturn(200, $listNewWith);
    }

    /**
     * 供应商/分销商账号查询
     * <AUTHOR>
     * @date 2020/12/18
     *
     * @return array
     *
     */
    private function _businessOrderQuery()
    {
        $loginInfo = $this->_loginMember;
        $memberId  = $loginInfo['memberID'];
        $sid       = $loginInfo['sid'];

        $baseQueryParam = I('post.');
        $queryParamRes  = $this->_handleParams($baseQueryParam);

        if ($queryParamRes[0] != 200) {
            return $this->returnData(204, $queryParamRes[1]);
        }

        //处理过的请求参数
        $queryParam = $queryParamRes[2];

        if ($queryParam['size'] > 100) {
            return $this->returnData(204, '获取条数超出范围');
        }

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition();
        if ($condition === false) {
            $queryRes = self::returnData(self::CODE_SUCCESS);
        } else {
            $queryBiz = new Query();
            $queryRes = $queryBiz->searchBusinessList($memberId, $sid, $queryParam['sellerid'], $queryParam['buyerid'],
                $queryParam['orderStart'], $queryParam['orderEnd'], $queryParam['dtimeStart'], $queryParam['dtimeEnd'],
                $queryParam['page'], $queryParam['size'], $queryParam['ordernum'], $queryParam['remoteOrder'],
                $queryParam['thirdOrder'], $queryParam['status'], $queryParam['mobile'], $queryParam['idcard'],
                $queryParam['ordername'], $queryParam['lid'], $queryParam['memberRelationship'], true,
                false, $condition
            );
        }

        if ($queryRes['code'] == 200) {
            //剥离旅游券的订单
            $orderListOldWith = [];
            $orderInfo        = [];
            foreach ($queryRes['data'] as $orderListValueNew) {
                if ($orderListValueNew['p_type'] == 'Q') {
                    continue;
                }
                $orderListOldWith[] = $orderListValueNew['ordernum'];
                $orderInfo[]        = $orderListValueNew;
            }

            $listWithMap = [];
            if (!empty($orderListOldWith) && !empty($orderInfo)) {
                //处理渠道
                $list = $this->_dealData($sid, $orderInfo);

                //取票状态处理
                $data = array_values($list);
                $data = (new OrderList())->_handleOrderListPrintstate($data);
                $listWithMap = array_key($data, 'ordernum');
            }

            //其他类型的订单和旅游券订单聚合显示
            $listNewWith = [];
            foreach ($queryRes['data'] as $orderListTmpWith) {
                if ($orderListTmpWith['p_type'] == 'Q') {
                    $listNewWith[] = $this->handleLvOrderList($orderListTmpWith['ordernum'], $orderListTmpWith['p_type']);
                } else {
                    $listNewWith[] = $listWithMap[$orderListTmpWith['ordernum']];
                }
            }

            $this->apiReturn(200, $listNewWith);
        } else {
            return $this->returnData(204, $queryRes['msg']);
        }
    }

    private function _dealData($memberId, $list)
    {
        //获取详细
        $ordernumArr = array_column($list, 'ordernum');
        $orderTools  = new \Model\Order\OrderTools('slave');
        $detailMap   = $orderTools->getOrderDetailInfo($ordernumArr, 'orderid,product_ext');
        $detailMap   = array_key($detailMap, 'orderid');

        foreach ($list as $key => $value) {

            $list[$key]['ordermode'] = $this->_orderModeConf[$value['ordermode']];
            //资源账号显示正常销售方法
            if ($value['buyerid'] == $value['sellerid'] && !in_array($this->_loginMember['dtype'], [2,3])) {
                $list[$key]['ordermode'] = '自供自销';
            }
            if ($value['ordermode'] == 23) {
                $list[$key]['ordermode'] = $this->_orderModeConf[$value['ordermode']];
            }
            if ($value['p_type'] == 'J') {
                //验证字段'delivery'是否存在，不存在则设置，用来展示微平台发货按钮
                if (!isset($value['delivery'])) {
                    //仅支持供应商有发货按钮，并且订单已支付
                    if (($value['buyerid'] == $value['sellerid']) && ($value['pay_status'] == 1)) {
                        $list[$key]['delivery'] = true;
                    } else {
                        $list[$key]['delivery'] = false;
                    }
                } else {
                    // 已存在的，说明数据已经处理，验证下订单是否支付
                    if ($value['pay_status'] == 1) {
                        $list[$key]['delivery'] = true;
                    } else {
                        $list[$key]['delivery'] = false;
                    }
                }
                $detailExt   = json_decode($detailMap[$value['ordernum']]['product_ext'], true);
                $deliveryWay = $detailExt['deliveryType'] ?? 1;

                //快递已发货，可以验证
                $checkButton  = false;
                $cancelButton = false;
                if ($value['status'] == SpecialOrderStatus::SEND_GOODS_CODE || ($deliveryWay == 1 && $value['status'] == SpecialOrderStatus::TO_SEND_GOODS_CODE)) {
                    if (in_array($memberId,
                            [$value['member'], $value['ticket_list'][0]['apply_id']]) && $value['pay_status'] == 1) {
                        $checkButton = true;
                    }
                }

                if ($value['status'] == SpecialOrderStatus::TO_SEND_GOODS_CODE) {
                    if (in_array($memberId, [$value['member'], $value['ticket_list'][0]['apply_id']])) {
                        $cancelButton = true;
                    }
                }

                $list[$key]['check_button'] = $checkButton;
                $list[$key]['cancel']       = $cancelButton;
            } else {
                $list[$key]['check_button'] = false;
                $list[$key]['delivery']     = false;
            }
        }

        return $list;
    }

    /**
     *  特产订单确认收货
     * <AUTHOR>
     * @date 2020/2/25
     * @return array
     */
    public function confirmReceipt()
    {
        $mid = $this->isLogin('ajax');

        $ordernum = I('ordernum', '', 'strval,trim');

        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }

        $specialBiz = new Specialty();

        $result = $specialBiz->confirmReceipt($mid, $ordernum);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }

    /**
     * 获取订单详细信息
     *
     * <AUTHOR>
     * @date   2017-04-18
     *
     */
    public function orderDetail()
    {
        $memberId = $this->isLogin('ajax');
        //主订单号
        $ordernum = I('ordernum', '');

        if (!$ordernum) {
            $this->apiReturn(204, [], '订单号缺失');
        }

        $queryBiz = new Query();

        $result = $queryBiz->getOrderDetail($memberId, $ordernum, $this->_loginMember['memberID'], true);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }
//        $visitor_info = [];
//        //$visitor_info = (new OrderQuery)->getTouristsInfoNoMobile($ordernum);
//        if ($result['data']['visitor_info'] || $result['data']['more_credentials']) {
//            $visitor_info = $result['data']['visitor_info'];
//            //游客取票人更多信息处理
//            $moreCredential = $this->_handleMoreCredential($result['data']['more_credentials'], $visitor_info,
//                $ordernum);
//            if ($moreCredential['code'] != 200) {
//                $this->apiReturn(204, [], $moreCredential['msg']);
//            }
//            $visitor_info                       = $moreCredential['data']['visitor_info'];
//            $result['data']['more_credentials'] = $moreCredential['data']['more_credentials'];
//        }

        //处理一票一证游客信息
        $visitor_info = $this->dealVisitorInfo($result['data']['visitor_info']);
        // if (count($visitor_info) > 1) {
        $result['data']['visitor_info'] = $visitor_info;

        // }
//        $result['data']['is_middlemen'] && $result['data'] = (new OrderList())->hideCustomerInformation($result['data']);

        if ($result && $result['code'] == 200) {
            $this->apiReturn(200, $result['data']);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }

    }

    /**
     * 微平台特产发货操作
     * <AUTHOR>
     * @date 2020/2/18
     * @return array
     */
    public function deliverGoods()
    {
        $ordernum    = I('ordernum', '', 'strval');
        $expCompany  = I('exp_company', '');
        $expNo       = I('exp_no', '');
        if (!$ordernum) {
            $this->apiReturn(204, [], '订单号缺失');
        }

        $sid = $this->_loginMember['sid'];
        $sdtype = $this->_loginMember['sdtype'];
        $operId = $this->_loginMember['memberID'];
        $saccount = $this->_loginMember['saccount'];

        $result = (new \Business\Order\OrderDeliver())->deliver($sid, $operId, $ordernum, $expCompany, $expNo, 16, $saccount, $sdtype);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 修改订单信息
     * <AUTHOR>
     * @date 2020/9/28
     *
     */
    public function modifyLogistics()
    {
        $ordernum    = I('ordernum', '', 'strval');
        $expCompany  = I('exp_company', '');
        $expNo       = I('exp_no', '');
        if (!$ordernum) {
            $this->apiReturn(204, '订单号缺失');
        }

        $sid = $this->_loginMember['sid'];
        $sdtype = $this->_loginMember['sdtype'];
        $operId = $this->_loginMember['memberID'];
        $saccount = $this->_loginMember['saccount'];

        $result = (new \Business\Order\OrderDeliver())->modifyLogistics($sid, $operId, $ordernum, $expCompany, $expNo, 16, $saccount, $sdtype);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 短信查看供应商收到订单的详情页面
     *
     * <AUTHOR>
     * @date   2019-12-10
     *
     */
    public function orderDetailToSms()
    {
        $ordernum = I('ordernum', '', 'strval');
        if (!$ordernum) {
            $this->apiReturn(204, [], '参数错误');
        }
        //订单号解码
        $ordernum   = $this->_ordernumDecode($ordernum);
        $ordernum   = strval($ordernum);
        $orderModel = new OrderTools('slave');
        //基本信息
        $mainInfo = $orderModel->getOrderInfo($ordernum, 'ordernum,lid', false, false, 'apply.apply_id');
        if (!$mainInfo) {
            $this->apiReturn(204, [], '订单不存在');
        }

        $landModel = new \Model\Product\Land();
        $land      = $landModel->getLandInfo($mainInfo['lid'], false, 'p_type,title');
        if ($land['p_type'] != 'C') {
            $this->apiReturn(204, [], '暂不支持此类型查询');
        }
        //这边因为发短信都是发给顶级供应商的，然后这边直接取顶级供应商了 lc
        $sid = $mainInfo['apply_id'];

        $queryBiz = new Query();

        $result       = $queryBiz->getOrderDetail($sid, $ordernum);
        $queryParams = [[$ordernum]];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo','queryOrderTouristInfoByOrderId', $queryParams);
        $visitor_info = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $visitor_info = $queryRes['data'];
        }
        //$visitor_info = (new OrderQuery)->getTouristsInfoNoMobile($ordernum);
        //处理一票一证游客信息
        $visitor_info = $this->dealVisitorInfo($visitor_info);

        // if (count($visitor_info) > 1) {
        $result['data']['visitor_info'] = $visitor_info;
        // }

        if ($result && $result['code'] == 200) {
            $this->apiReturn(200, $result['data']);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }

    }

    /**
     * author  leafzl
     * Date:  2018/3/14
     *
     * @param $list  array 游客信息
     * @param  is_display  int 是否显示
     *
     * @return mixed
     */

    public function dealVisitorInfo($list)
    {
        foreach ($list as &$value) {
            if ($value['idx'] > 0 && $value['idcard']) {
                $value['is_display'] = 1;
            } else {
                $value['is_display'] = 0;

            }
            unset($value);
        }

        return $list;

    }

    /**
     * 获取订单操作记录
     *
     * <AUTHOR>
     * @date   2017-04-26
     */
    public function operatingRecord()
    {
        $memberId = $this->isLogin('ajax');

        //主订单号
        $ordernum = I('ordernum', '');

        if (!$ordernum) {
            $this->apiReturn(204, [], '订单号缺失');
        }

        $queryBiz = new Query();

        $result = $queryBiz->operatingRecord($memberId, $ordernum);

        if ($result && $result['code'] == 200) {
            $this->apiReturn(200, $result['data']);
        } else {
            $this->apiReturn(204, [], $result['msg']);
        }
    }

    /**
     * 取消订单
     *
     * <AUTHOR>
     * @date   2017-05-04
     */
    public function cancel()
    {
        $memberId = $this->isLogin('ajax');
        $loginInfo = $this->_loginMember;

        //主订单号
        $ordernum = I('ordernum', '');
        if (!$ordernum) {
            $this->apiReturn(204, [], '订单号缺失');
        }

        //取消权限检测
        $queryBiz = new Query();
        $checkRes = $queryBiz->cancelCheck($ordernum, $memberId);

        if (!$checkRes['status']) {
            $this->apiReturn(204, [], $checkRes['msg']);
        }

        $modifyBiz       = new \Business\Order\Modify();
        $cancelChannel   = OrderConst::WECHAT_PLATFORM_CANCEL;
        $reqSerialNumber = $modifyBiz->getAuditInfoByOrder($ordernum);
        $result          = $modifyBiz->cancelBaseFormat($ordernum, $loginInfo['memberID'], $memberId, $cancelChannel, '',
            $reqSerialNumber);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], [], $result['msg']);
        } else {
            $this->apiReturn(204, [], '未知错误');
        }
    }

    /**
     * 重发短信
     *
     * <AUTHOR>
     * @date   2017-05-20
     */
    public function resendMsg()
    {

        $memberId = $this->isLogin('ajax');

        $ordernum = I('ordernum', '');
        if (!$ordernum) {
            $this->apiReturn(204, [], '订单号缺失');
        }

        $result = (new \Business\Order\Modify())->resendMsg($ordernum, $memberId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], [], $result['msg']);
        } else {
            $this->apiReturn(204, [], '未知错误');
        }
    }

    /**
     * 微平台订单查询的一些配置项
     * <AUTHOR>
     * @date   2019-08-12
     */
    public function getQueryConfig()
    {
        $memberId = $this->isLogin('ajax');

        $queryBiz = new Query();
        $result   = $queryBiz->getQueryConfig();

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(204, [], '未知错误');
        }
    }

    ///**
    // * 根据时间范围查询订单
    // *
    // * <AUTHOR>
    // * @date   2017-04-13
    // *
    // * @param  int $memberId 会员id
    // * @param  string $beginDate 开始时间
    // * @param  string $endDate 截至时间
    // * @param  string $dateType 0 : 下单时间, 1 : 游玩时间 , 2 : 验证时间
    // * @param  int $status 订单状态
    // * @param  int $memId 分销商或者供应商id
    // * @param  int $memType 0:分销商|1供应商
    // * @param  int $page 当前页数
    // * @param  int $size 每页条数
    // *
    // * @return array
    // */
    //private function _searchOnlyByDate($memberId, $beginDate, $endDate, $dateType, $status, $memId, $memType, $page, $size)
    //{
    //
    //    $this->_searchDateLimit($beginDate, $endDate);
    //
    //    $queryBiz = new \Business\Order\Query();
    //    $return   = $queryBiz->searchOnlyByDate(
    //        $memberId,
    //        $beginDate,
    //        $endDate,
    //        $dateType,
    //        $status,
    //        $memId,
    //        $memType,
    //        $page,
    //        $size
    //    );
    //
    //    return $return;
    //}

    ///**
    // * 订单精确搜索
    // *
    // * <AUTHOR>
    // * @date   2017-06-19
    // *
    // * @param  int $memberId 当前登陆用户id
    // * @param  int $exact 搜索值
    // * @param  int $exactType 搜索类型
    // * @param  string $beginDate 开始日期
    // * @param  string $endDate 结束日期
    // * @param  string $dateType 日期类型
    // * @param  int $status 订单状态
    // * @param  int $memId 分销商|供应商id
    // * @param  int $memType 0:分销商|1:供应商
    // * @param  int $page 当前页数
    // * @param  int $size 每页条数
    // *
    // * @return array
    // */
    //private function _searchByExact(
    //    $memberId,
    //    $exact,
    //    $exactType,
    //    $beginDate,
    //    $endDate,
    //    $dateType,
    //    $status,
    //    $memId,
    //    $memType,
    //    $page,
    //    $size)
    //{
    //
    //    if (!in_array($exactType, $this->_exactType) && !$memId) {
    //        $this->apiReturn(204, [], '精确查询类型错误');
    //    }
    //
    //    $queryBiz = new \Business\Order\Query();
    //
    //    switch ($exactType) {
    //        //订单号搜索
    //        case 'ordernum':
    //            $result = $queryBiz->searchByOrdernum($memberId, $exact);
    //            break;
    //
    //        //远程订单号搜索
    //        case 'remote_order':
    //            $result = $queryBiz->searchByRemoteOrder($memberId, $exact);
    //            break;
    //
    //        //第三方订单号搜索
    //        case 'third_order':
    //            $result = $queryBiz->searchByThirdOrder($memberId, $exact);
    //            break;
    //
    //        //联系人搜索
    //        case 'mobile':
    //            $this->_searchDateLimit($beginDate, $endDate);
    //            $result = $queryBiz->searchByMobile(
    //                $memberId,
    //                $exact,
    //                $beginDate,
    //                $endDate,
    //                $dateType,
    //                $status,
    //                $memId,
    //                $memType,
    //                $page,
    //                $size
    //            );
    //            break;
    //
    //        //景区搜索
    //        case 'pro_name':
    //            $this->_searchDateLimit($beginDate, $endDate);
    //            $result = $queryBiz->searchByProduct(
    //                $memberId,
    //                $exact,
    //                $beginDate,
    //                $endDate,
    //                $dateType,
    //                $status,
    //                $memId,
    //                $memType,
    //                $page,
    //                $size
    //            );
    //            break;
    //
    //        //身份证搜索
    //        case 'id_card':
    //            $this->_searchDateLimit($beginDate, $endDate);
    //            $result = $queryBiz->searchByIdCard(
    //                $memberId,
    //                $exact,
    //                $beginDate,
    //                $endDate,
    //                $dateType,
    //                $status,
    //                $memId,
    //                $memType,
    //                $page,
    //                $size
    //            );
    //            break;
    //
    //        //联系人姓名
    //        case 'linkman':
    //            $this->_searchDateLimit($beginDate, $endDate);
    //            $result = $queryBiz->searchByLinkman(
    //                $memberId,
    //                $exact,
    //                $beginDate,
    //                $endDate,
    //                $dateType,
    //                $status,
    //                $memId,
    //                $memType,
    //                $page,
    //                $size
    //            );
    //            break;
    //    }
    //
    //    return $result;
    //}

    /**
     * 搜索时间范围限制
     *
     * <AUTHOR>
     * @date   2017-06-19
     *
     * @param  string $beginDate 开始日期
     * @param  string $endDate 结束日期
     */
    private function _searchDateLimit($beginDate, $endDate)
    {

        if (!$beginDate || !$endDate) {
            $this->apiReturn(204, [], '请输入正确的起止日期');
        }

        $maxTimeDiff = 62 * 24 * 3600;
        if (strtotime($endDate) - strtotime($beginDate) > $maxTimeDiff) {
            $this->apiReturn(204, [], '最大只能查询62天的数据');
        }

    }

    /**
     * 是否联票订单
     * <AUTHOR>
     * @time   2017-07-27
     */
    private function _isLinkOrder()
    {
        $isLink = false; // 默认非联票

        if ($links = I('link', '')) {
            // 普通联票
            if ($links && is_array($links)) {
                foreach ($links as $pid => $num) {
                    if ((int)$num < 1) {
                        unset($links[$pid]);
                    }
                }
                $isLink = empty($links) ? false : true;
            }

        } elseif (I('endtime', 0)) {
            // 酒店联票
            $endTime   = I('endtime', 0, 'strtotime');
            $begintime = I('begintime', 0, 'strtotime');
            if ($endTime - $begintime > 24 * 3600) {
                $isLink = true;
            }
        }

        return $isLink;
    }

    /**
     * 保存联系人
     * @author: zhangyz
     * @date: 2020/6/1
     * @return array|bool
     */
    private function _saveLinkMan()
    {
        $linkman = I('post.linkman', 0, 'intval');

        if ($linkman) {
            $name         = I('post.ordername', '');
            $mobile       = I('post.contacttel', '');
            $idcard       = I('post.idcard', '');
            $delivery     = I('post.delivery', []);
            $province     = $delivery['province'] ?? '';
            $provinceCode = $delivery['province_code'] ?? 0;
            $city         = $delivery['city'] ?? '';
            $cityCode     = $delivery['city_code'] ?? 0;
            $town         = $delivery['town'] ?? '';
            $townCode     = $delivery['town_code'] ?? 0;
            $detailAddr   = $delivery['detail_addr'] ?? '';

            $contact               = new \Entity\Member\FrequentContacts($this->_loginMember['sid'], $name, $mobile);
            $contact->personId     = $idcard;
            $contact->province     = $province;
            $contact->provinceCode = $provinceCode;
            $contact->city         = $city;
            $contact->cityCode     = $cityCode;
            $contact->town         = $town;
            $contact->townCode     = $townCode;
            $contact->address      = $detailAddr;
            $contact->operId       = $this->_loginMember['memberID'];

            $contactsBiz = new \Business\Member\FrequentContacts();
            $result      = $contactsBiz->add($this->_loginMember['sid'], $contact);

            return [$result['code'], $result['data'], $result['msg']];
        }

        return true;
    }

    /**
     *  收获地址
     * <AUTHOR>
     * @date 2020/2/18
     * @return array
     */
    private function _saveShippingAddr($orderNum = '')
    {
        $saveLinkMan = I('post.linkman', 0, 'intval'); // 1保存 0不保存

        if ($saveLinkMan == 1) {
            $name          = trim(I('post.ordername'));
            $mobile        = trim(I('post.contacttel'));
            $delivery      = I('post.delivery', '', 'strval');
            $expressType   = $delivery['express_type'] ?? 0;
            $expressPayWay = $delivery['express_pay_way'] ?? 0;
            $province      = $city = $town = '';
            if (!$expressType) {
                $province = $delivery['province'] ?? 0;
                if (empty($province) && !$expressType) {
                    return [400, '地址省份不能为空'];
                }
                $city = $delivery['city'];
                if (empty($city) && !$expressType) {
                    return [400, '地址市区不能为空'];
                }
                $town        = $delivery['town'];
                $detail_addr = $delivery['detail_addr'];
            }

            //if (empty($detail_addr) && !$expressType) {
            //    return [400, '详细地址不能为空'];
            //}

            if (!$name || !$mobile) {
                return false;
            }
            $memberId = $this->getMemberId();

            $paramsObj = new \stdClass;
            //参数对象
            $paramsObj->name        = $name;
            $paramsObj->mobile      = $mobile;
            $paramsObj->province    = $province;
            $paramsObj->city        = $city;
            $paramsObj->town        = $town;
            $paramsObj->postcode    = '';
            $paramsObj->detail_addr = $detail_addr;
            $paramsObj->add_type    = $expressType;

            $specialBiz = new Specialty();

            $series = $specialBiz->createShippingAddr($memberId, $paramsObj);

            if ($series['code'] == 200) {
                $series = $series['data'];
                $series = $series . '|' . $expressPayWay;
                $api    = new \Business\JavaApi\Order\OrderDetailUpdate();
                $api->orderDetailInfoUpdate($orderNum, ['series' => $series]);
            }
        }

        return true;
    }

    /**
     * 订单号解码
     *
     * @param  [type] $ordernum 订单号
     *
     * @return [type]           [description]
     */
    private function _ordernumDecode($ordernum)
    {
        $ordernum = \Library\MessageNotify\OrderNotify::url_sms_decode($ordernum);

        return $ordernum[0];
    }

    /**
     * 处理取票人游客更多信息 参数转换
     * <AUTHOR>
     * @time   2020-7-10
     * @param  array $moreCredentials
     * @param  array $visitorInfo
     * @param  string $ordernum
     *
     */
    private function _handleMoreCredential($moreCredentials, $visitorInfo,$ordernum)
    {
        $data = [
            'visitor_info'     => $visitorInfo,
            'more_credentials' => $moreCredentials,
        ];
        if (empty($ordernum) || (empty($moreCredentials) && empty($visitorInfo))) {
            return [
                'code' => 203,
                'data' => $data,
                'msg'  => '参数错误',
            ];
        }
        $data = [
            0 => '其他',
            1 => '护照',
            2 => '回乡证',
            3 => '台胞证',
            4 => '军官证',
        ];
        //游客
        $touristJavaApi = new OrderTouristInfo();
        $touristExInfo  = $touristJavaApi->getOrderTouristInfoExt($ordernum);
        if ($touristExInfo['code'] != 200) {
            return [
                'code' => $touristExInfo['code'],
                'data' => $data,
                'msg'  => $touristExInfo['msg'],
            ];
        }
        //游客填写的更多信息 - 证件   $touristInfo添加more_credentials
        //{
        //             "target":"1: 取票人填写 2: 所有游客填写"
        //             ,"name":"1:护照 2:回乡证 3:台胞证 4:军官证 5:车牌号 0:其他'"
        //             ,"text":"其他证件名称"
        //             ,"realName":"游客姓名"
        //             , "documentValue":"证件号"
        //          }

        foreach ($moreCredentials as $key => &$moreData) {
            $papers           = $data[$moreData['name']];
            $moreData['name'] = $papers != '其他' ? $papers : $moreData['text'];
        }
        unset($moreData);
        foreach ($visitorInfo as &$touristData) {
            if (!isset($touristData['more_credentials'])) {
                $touristData['more_credentials'] = [];
                foreach ($touristExInfo['data'] as $key => $moreData) {
                    if ($touristData['id'] == $moreData['touristId']) {
                        $touristExInfos = [
                            'name' => $data[$moreData['certType']] ?? '',
                            'text' => $moreData['remark'],
                            'realName' => $touristData['tourist'],
                            'documentValue' => $moreData['certInfo'],
                        ];
                        $touristData['more_credentials'][] = $touristExInfos;
                    }
                }
            }
        }
        return [
            'code' => 200,
            'data' => [
                'visitor_info'     => $visitorInfo ?? [],
                'more_credentials' => $moreCredentials ?? [],
            ],
            'msg'  => '',
        ];
    }

    /**
     * 参数初步整理
     *
     * @param $param
     */
    private function _handleParams($param)
    {
        $status             = $param['status'] ?? -1;        //订单状态
        $exact              = $param['exact'] ?? '';        //精确搜索关键字
        $exactType          = $param['exact_type'] ?? '';        //精确搜索类型
        $page               = $param['page'] ?? 1;        //当前页数
        $size               = $param['size'] ?? 20;        //获取条数
        $memberRelationship = $param['member_relation_ship'] ?? -1;        //订单会员关系 -1 全部 0:平台关系,1:资源中心关系

        if (!in_array($status, $this->_orderStatus)) {
            return [0, '订单状态参数错误'];
        }

        if ($exact) {
            if (!in_array($exactType, $this->_exactType)) {
                return [0, '精确查询类型错误'];
            }
        }

        $resQuery = [
            'sellerid' => $param['sellerid'],
            'buyerid'  => $param['buyerid'],
        ];
        if ($exact) {
            switch ($exactType) {
                //订单号搜索
                case 'ordernum':
                    $resQuery['ordernum'] = strval($exact);
                    break;
                //远程订单号搜索
                case 'remote_order':
                    $resQuery['remoteOrder'] = strval($exact);
                    break;
                //第三方订单号搜索
                case 'third_order':
                    $resQuery['thirdOrder'] = strval($exact);
                    break;
                //联系人搜索
                case 'mobile':
                    $resQuery['mobile'] = strval($exact);
                    break;
                //景区搜索
                case 'pro_name':
                    $resQuery['lid'] = intval($exact);
                    break;
                //身份证搜索
                case 'id_card':
                    $resQuery['idcard'] = strval($exact);
                    break;
                //联系人姓名
                case 'linkman':
                    $resQuery['ordername'] = strval($exact);
                    break;
            }
        }

        if ($param['order_start']) {
            $resQuery['orderStart'] = date('Y-m-d H:i:s', strtotime($param['order_start']));
        }
        if ($param['order_end']) {
            $resQuery['orderEnd']   = date('Y-m-d H:i:s', strtotime($param['order_end']));
        }
        if ($param['d_time_start']) {
            $resQuery['dtimeStart'] = date('Y-m-d H:i:s', strtotime($param['d_time_start']));
        }
        if ($param['d_time_end']) {
            $resQuery['dtimeEnd']   = date('Y-m-d H:i:s', strtotime($param['d_time_end']));
        }

        //全部状态传-1
        $tmpStatus = intval($status);
        if ($tmpStatus != -1) {
            $resQuery['status'] = $tmpStatus;
        } else {
            $resQuery['status'] = false;
        }
        //订单会员关系 -1 全部 0:平台关系,1:资源中心关系
        $memberRelationship = intval($memberRelationship);
        if ($memberRelationship != -1) {
            $resQuery['memberRelationship'] = $memberRelationship;
        } else {
            $resQuery['memberRelationship'] = false;
        }

        $resQuery['page'] = intval($page);
        $resQuery['size'] = intval($size);

        return [200, '', $resQuery];
    }

    /**
     * 订单改签
     * <AUTHOR>  Li
     * @date  2021-08-26
     */
    public function ticketChanging()
    {
        $orderNum       = I('order_num', '', 'strval');         //订单号
        $sectionTimeStr = I('section_time_str', '', 'strval');  //分时预约时间段
        $playTime       = I('play_time', '', 'strval');         //预约游玩时间
        $sectionTimeId  = I('section_time_id', 0, 'intval');   //分时预约时间段Id
        if (!$orderNum) {
            $this->apiReturn(204, [], '订单号不能为空');
        }
        if (!strtotime($playTime)) {
            $this->apiReturn(204, [], '预约时间错误');
        }
        $modifyBz = new Modify();
        $result   = $modifyBz->ticketChanging($orderNum, $playTime, $this->_loginMember['sid'], $this->_loginMember['memberID'], 22, $sectionTimeId,
            $sectionTimeStr);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 演出订单改签
     * <AUTHOR>  Li
     * @date   2021-08-18
     */
    public function showTicketChanging()
    {
        $ordernum   = I('post.ordernum', '', 'strval');     //订单号
        $changeTime = I('post.play_date', '', 'strval');    //改签日期
        $venueId    = I('post.venue_id', 0, 'intval');      //演出场馆id
        $aid        = I('post.aid', 0, 'intval');           //演出门票上级供应商id
        $roundId    = I('post.round_id', 0, 'intval');       //演出场次id
        $seats      = I('post.seat_ids', '', 'strval');       //演出座位id 多个以逗号隔开

        if (!$ordernum || !$changeTime || !$aid || !$venueId || !$roundId) {
            $this->apiReturn(204, [], '缺少参数');
        }
        $seatIdList = explode(',', $seats);

        $source   = 16;
        $orderBiz = new ShowModify();
        $res      = $orderBiz->showTicketChanging($ordernum, $changeTime, $this->_loginMember['sid'], $aid, $venueId, $roundId, $this->_loginMember['memberID'],
            $source, $seatIdList);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 获取演出改签库存信息
     * <AUTHOR>  Li
     * @date   2021-08-26
     */
    public function getShowInfoList()
    {
        $venues    = I('post.venues', '', 'strval');
        $date      = I('post.date', '', 'strval');
        $isReserve = I('post.is_reserve', 0, 'strval');    //是否是预约获取库存

        if (empty($venues) || empty($date)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $orderBiz = new ShowModify();
        $result  = $orderBiz->getShowInfoList($this->_loginMember['sid'], $venues, $date, $isReserve);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 演出预约
     * <AUTHOR>  Li
     * @date   2022-04-13
     */
    public function showReserve()
    {
        $ordernum   = I('post.ordernum', '', 'strval');     //订单号
        $changeTime = I('post.play_date', '', 'strval');    //预约日期
        $venueId    = I('post.venue_id', 0, 'intval');      //演出场馆id
        $aid        = I('post.aid', 0, 'intval');           //演出门票上级供应商id
        $roundId    = I('post.round_id', 0, 'intval');      //演出场次id
        $seats      = I('post.seat_ids', '', 'strval');     //演出座位id 多个以逗号隔开
        $isSms      = I('post.is_sms', 0, 'intval');        //是否短信页预约

        if (!$ordernum || !$changeTime || !$aid || !$venueId || !$roundId) {
            $this->apiReturn(204, [], '缺少参数');
        }
        $seatIdList = explode(',', $seats);

        $opId     = $this->_loginMember['memberID'];
        $source   = 16;
        $orderBiz = new ShowModify();
        $res      = $orderBiz->showReserve($ordernum, $changeTime, $this->_loginMember['sid'], $aid, $venueId, $roundId, $opId,
            $source, $seatIdList, $isSms);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 获取演出库存
     *
     */
    public function getOrderReservationInventory()
    {
        $orderNum  = I('order_num', '', 'strval');
        $startData = I('start_date', '', 'strval');
        $endData   = I('end_date', '', 'strval');
        if (empty($orderNum)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        if (empty($startData) || empty($endData)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '预定日期未选择');
        }
        $mallOrderBz = new \Business\Product\ProductStorage();
        $res         = $mallOrderBz->getShowOrderStorageByReservationService($orderNum, $startData, $endData);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    public function handleLvOrderList($ordernum, $pType)
    {
        $temp = [
            'ordernum'      => $ordernum,
            'p_type'        => $pType,
            //'apicode'        => '',
            //'apiorder'        => '',
            //'appointmentButton'        => '',
            //'buy_money'        => '',
            //'buyer'        => '',
            //'buyerid'        => '',
            //'can_appointment'        => '',
            //'cancel'        => '',
            //'changeButton'        => '',
            //'check_button'        => '',
            //'code'        => '',
            //'confirmButton'        => '',
            //'delivery'        => '',
            //'dtime'        => '',
            //'ext_content'        => '',
            //'if_print'        => '',
            //'imgpath'        => '',
            //'is_print_state'        => '',
            //'is_self'        => '',
            //'lid'        => '',
            //'member'        => '',
            //'memo'        => '',
            //'more_credentials'        => '',
            //'ordermode'        => '',
            //'ordername'        => '',
            //'ordertel'        => '',
            //'ordertime'        => '',
            //'pay'        => '',
            //'pay_status'        => '',
            //'personid'        => '',
            //'playtime'        => '',
            //'product_ext'        => '',
            //'remotenum'        => '',
            //'resend'        => '',
            //'sale_money'        => '',
            //'seller'        => '',
            //'sellerid'        => '',
            //'show_arr'        => '',
            //'status'        => '',
            //'ticket_changing_range'        => '',
            //'ticket_changing_weekend'        => '',
            //'ticket_list'        => [],
            //'visitor_info'        => [],

        ];

        return $temp;
    }

    public function identity(){
        $queryBiz = new Query();
        $res = $queryBiz->getIdentity();
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}
