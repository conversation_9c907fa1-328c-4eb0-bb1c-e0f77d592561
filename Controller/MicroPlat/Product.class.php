<?php
/**
 * 产品相关操作
 *
 * <AUTHOR>
 * @time   2017-01-10
 */

namespace Controller\MicroPlat;

use Business\AgreementTicket\Storage;
use Business\Authority\DataAuthLogic;
use Business\JavaApi\Order\OrderParamConvert;
use Business\JavaApi\ProductApi;
use Business\JavaApi\Ticket\Listing;
use Business\JavaApi\TicketApi;
use Business\NewJavaApi\NewPms\StockQuery;
use Business\Order\OrderBook;
use Business\Order\TimeShareOrder;
use Business\Product\Product as bizProduct;
use Business\Product\SpecialtyV2;
use Business\Product\Ticket as bizTicket;

use Library\Constants\ThemeConst;
use Library\Tools\Helpers as Helpers;
use Model\AdminConfig\AdminConfig;
use Model\Product\Area as Area;
use Model\Product\Evolute;
use Model\Product\Land as Land;
//use Model\Product\PriceRead as PriceRead;
use Model\Product\Ticket;
use Model\Subdomain\SubdomainInfo;
use Business\Product\Show as ShowBiz;
use Model\Ota\OtaQueryModel;

class Product extends Common
{
    const __SELECT_SEAT__ = 44;

    private $_allowType     = ['A', 'B', 'C', 'F', 'H', 'I', 'G'];
    private $_lastTicketPos = 0;   // 最后一次取门票的位置
    private $_lastTid       = 0;   // 最后一次取的门票ID

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取指定景区的门票列表
     * <AUTHOR>
     * @time   2017-01-10
     *
     * @param  int   lid            景区ID
     * @param  int   pageSize       分页页面数据条数
     * @param  int   lastTid        最后一次取的门票ID
     * @param  int   lastTicketPos  最后一次取门票的位置
     * @param  int   useLid         景区ID   [作为方法调用时传递]
     * @param  int   useAid         供应商ID [作为方法调用时传递]
     * @param  int   size           票数量   [作为方法调用时传递]
     *
     * @return 分页后的票列表
     * ['list' => [['tid'=>1001, ...],[...],...], 'lastTid'=>0, 'lastTicketPos'=>0]
     */
    public function getTicketList($useLid = 0, $useAid = 0, $size = 4)
    {
        $memberId = $this->getMemberId();
        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        $lid                  = I('lid', 0, 'intval');
        $aid                  = I('aid', 0, 'intval');
        $pageSize             = I('pageSize', 3, 'intval');
        $this->_lastTid       = I('lastTid', 0, 'intval');
        $this->_lastTicketPos = I('lastTicketPos', 0, 'intval');
        $isMultiDist          = I('is_multi_dist', 0, 'intval');//是否来自分销专员 0=不是 1=是

        //页面上面选定的日期
        $date    = I('date', '', 'strval');
        $date    = strtotime($date) ? date('Y-m-d', strtotime($date)) : null;
        $channel = I('channel', '', 'intval');

        //分销专员获取数据记为运营商
        $multiDist = (new \Business\MultiDist\Member())->getMultiDistCache($this->_loginMember['sid']);
        if ($isMultiDist === 1 && !empty($multiDist['multi_dist'])) {
            $memberId = $this->updateOrderMemberNew($multiDist['multi_dist']['uuid'], true);
        }

        if ($this->_supplyId == 55 && empty($channel)) {
            $channel = 4;
        } else {
            $channel = 10;
        }

        // 内部调用, 重置变量值
        $useApiReturn = true; // 结果返回方式：true为ajax返回，false为数组返回
        if ($useLid && is_numeric($useLid)) {
            $lid          = $useLid;
            $aid          = $useAid;
            $pageSize     = $size;
            $useApiReturn = false;
        }

        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }
        //重新调整之后的接口
        if ($this->_supplyId == 55) {
            //三亚的 获取产品以三个主账号为供应商
            $products = TicketApi::getTicketListByLandIdSanya($aid, $memberId, $lid, $channel);
        } else {
            //过滤公众号
            if ($this->_supplyAccount == 124078 || $this->_supplyAccount == 589638 || $isMultiDist === 1 || $this->_supplyAccount == 'm') {
                $products = TicketApi::getTicketListByLandId($memberId, $aid, $lid, $channel);
            } else {
                $domainId = $this->getSuperiorIdByAcount();
                $products = TicketApi::getTicketListByLandId($memberId, $aid, $lid, $channel, $domainId);
            }
        }

        if ($products['code'] != 200 && !$useApiReturn) {
            return [];
        } elseif ($products['code'] != 200 || empty($products['data'])) {
            $this->apiReturn(201, [], '没有可售门票');
        }

        // 门票按景区分组
        $tickets    = $this->_getTicketSet($products['data'], $aid, $memberId);
        $ticketList = $tickets[$lid][$aid];

        if (!$ticketList && !$useApiReturn) {
            return [];
        } elseif (!$ticketList) {
            $this->apiReturn(204, [], '未找到更多门票');
        }

        // 门票属性
        $tags = $this->_parseTicketsTags($ticketList);

        //门票ID 门票零售价和门市价
        $tidArr          = array_column($ticketList, 'tid');
        $tidStr          = implode(',', $tidArr);
        $ticketPriceList = TicketApi::getSinglePrices($tidStr, $date);

        //获取门票的分销价
        $tmpDate = $date ? $date : date('Y-m-d');
        //$settlementPriceList = TicketApi::getPriceAndStorageByPlayDate($tidStr, $memberId, $aid, $tmpDate);

        $tPriceBiz          = new \Business\Product\Price();
        $priceAndStorageRes = $tPriceBiz->buyBatchGet($memberId, $aid, $tmpDate, $tidStr);

        if ($priceAndStorageRes['code'] == 200 && !empty($priceAndStorageRes['data'])) {
            $settlementPriceList = $priceAndStorageRes['data'];
        }

        //获取票标签
        $ticketBz = new \Business\Product\Ticket();
        $tagArr   = $ticketBz->getTicketTagByIds($tidArr);
        // 分页后的门票数据整合
        foreach ($ticketList as $key => $ticket) {

            $tid = $ticket['tid'];
            //结算价
            $tmpPrice = isset($settlementPriceList[$tid]) ? $settlementPriceList[$tid]['settlement'] : 0;

            // 最近可售门票分销价
            if (isset($settlementPriceList[$tid]) && $tmpPrice >= 0) {
                $ticketList[$key]['tprice'] = $tmpPrice / 100;
            } else {
                // 不显示过期的票
                unset($ticketList[$key]);

                continue;
            }

            // 未登录 按零售价或者结算价显示
            if (!$this->_loginMember['memberID']) {
                $ticketList[$key]['tprice'] = isset($ticketPriceList[$tid]) ? $ticketPriceList[$tid]['retail_price'] / 100 : $ticket['tprice'];
            }

            //通过接口获取零售价和门市价
            $ticketPriceList                   = TicketApi::refreshPrice($memberId, $ticket['aid'], $ticket['tid'],
                false);
            $ticketList[$key]['lprice']        = $ticketPriceList['retail_price'] / 100;
            $ticketList[$key]['counter_price'] = $ticketPriceList['counter_price'] / 100;

            //之前前端都是使用的sprice为门市价，所以在后端这边暂时做下兼容
            $ticketList[$key]['sprice'] = $ticketList[$key]['counter_price'];

            // 加入门票属性
            $ticketList[$key]['tags'] = $tags[$tid] ?: '';

            //处理套票子票
            if (in_array($ticket['p_type'], ['F', 'H'])) {
                $packBiz   = new \Business\Product\PackTicket();
                $childData = $packBiz->getChildTickets($tid);

                if (!empty($childData)) {
                    $ticketList[$key]['childTickets'] = $childData;
                }
            }

            if ($ticket['p_type'] == 'J') {
                $specialInfo = (new SpecialtyV2())->getGoodsDeliveryInfo($tid);

                $ticketList[$key]['special_info'] = $specialInfo;
            }
            //赋值票类属性标签
            $ticketList[$key]['tag_list'] = $tagArr[$ticket['p_type']][$ticket['tid']] ?? [];

            $ticketList[$key]['availableDayStorage'] = $settlementPriceList[$tid]['availableStorage'] ?? 0;
        }

        // 供应商名称
        if (isset($ticketList[0]['apply_sid'])) {
            $memberInfo = $this->getMemberModel()->getMemberInfo(
                $ticketList[0]['apply_sid'], 'id', 'dname'
            );
        }
        $landName = isset($memberInfo['dname']) ? $memberInfo['dname'] : '';

        //分销专员预订页面相关参数处理
        if ($isMultiDist === 1) {
            $ticketList = $this->addMultiDistParams($ticketList, $aid, $lid, $memberId);
        }

        $return = [
            'land'          => $landName,
            'list'          => $ticketList,
            'ticket_ids'    => $products['data']['ticket_ids'],
            'lastTid'       => $this->_lastTid,
            'lastTicketPos' => $this->_lastTicketPos,
        ];

        if (!$useApiReturn) {
            return $return;
        }
        $this->apiReturn(200, $return, '获取门票列表成功');
    }

    /**
     * 分销专员详情页面参数处理
     * <AUTHOR>
     * @date 2020/6/19
     *
     * @param  array $params 要处理的票列表
     * @param  int $aid 供应商ID
     * @param  int $lid 景区ID
     * @param  int $memberId 运营商ID
     *
     * @return array
     */
    private function addMultiDistParams(array $params, int $aid, int $lid, int $memberId)
    {
        if (empty($params)) {
            return $params;
        }
        $multiDist = (new \Business\MultiDist\Member())->getMultiDistCache($memberId);
        if (empty($multiDist['multi_dist'])) {
            return $params;
        }

        $productBiz = new \Business\Product\Product();
        $landInfo   = $productBiz->getWPTProductInfo($memberId, $aid, $lid, $this->_channel);
        if ($landInfo['code'] != 200 || empty($landInfo['data']) || empty($landInfo['data']['ticketList'])) {
            return $params;
        }
        $ticketListNew = $landInfo['data']['ticketList'];
        $ticketMap     = array_key($ticketListNew, 'ticketId');

        //佣金比例数据获取
        $distMemberModel = new \Model\MultiDist\Member();
        $distProductBus  = new \Business\MultiDist\Product();
        $tids            = array_column($params, 'tid');
        $chainRes        = $distProductBus->getUuidInfo($multiDist['multi_dist']['uuid']);
        if (empty($chainRes)) {
            return $params;
        }
        $topUuid      = $distProductBus->getTopUuidByUid($chainRes['top_uid']);
        $profitConfig = $distProductBus->handleProfitFromLid($topUuid, $tids, $aid);
        $params       = $distProductBus->filterDataForDist($params, $multiDist['multi_dist']['uuid'], 'tid', 'aid');

        //获取一次门票扩展属性
        $ticketArr = (new \Business\CommodityCenter\Ticket())->queryTicketInfoByIds($tids);
        $ticketExt = [];
        foreach ($ticketArr as $ticket) {
            $ticketExt[$ticket['ticket']['id']] = $ticket['ext'];
        }

        foreach ($params as &$listTmp) {
            $listTmp['refund_rule_new'] = '';
            $listTmp['usetime']         = '';
            $listTmp['profit']          = 0;

            //佣金参数
            if (!empty($profitConfig)) {
                //浮点数转换下在处理防止精度缺失
                $costPrice         = intval($listTmp['tprice'] * 100);
                $counterPrice      = intval($listTmp['lprice'] * 100);
                $rate              = json_decode($profitConfig[$listTmp['tid']], true);
                $rate              = $rate[$multiDist['multi_dist']['level']] ?? 0;
                $profit            = round(($counterPrice - $costPrice) * ($rate / 100));
                $listTmp['profit'] = $profit;
            }

            if (!isset($ticketMap[$listTmp['tid']])) {
                continue;
            }

            $listMap = $ticketMap[$listTmp['tid']];

            if (isset($listMap['refundAudit']) && isset($listMap['refundRule']) && isset($listMap['refundEarlyTime'])) {
                $message = OrderBook::handleRefundRule($listMap['refundAudit'], $listMap['refundRule'],
                    $listMap['refundEarlyTime'], $ticketExt[$listTmp['tid']]['refund_after_time'] ?? 0);
                //退票说明
                $listTmp['refund_rule_new'] = $message;
            }

            $ptype = $listTmp['p_type'];
            if ($ptype == 'F') {
                //套票特殊处理
                $packBiz   = new \Business\Product\PackTicket();
                $childData = $packBiz->getChildTickets($listMap['ticketId']);

                if (!empty($childData)) {
                    $sonPidArr = array_column($childData, 'pid');
                    //有效期说明
                    $listTmp['usetime'] = OrderBook::parsePackValidTag($sonPidArr);
                }
            } else {
                $validData = [
                    'type'                      => $ptype,
                    'valid_period_start'        => $listMap['validStartDateStr'],
                    'valid_period_end'          => $listMap['validEndDateStr'],
                    'valid_period_type'         => $listMap['validType'],
                    'use_early_days'            => $listMap['useEarlyDays'],
                    'valid_period_days'         => $listMap['validDays'],
                    'valid_period_timecancheck' => $listMap['ifVerify'],
                    'delaytype'                 => $listMap['delayType'],
                ];
                //有效期说明
                $listTmp['usetime'] = OrderBook::getValidityDate($validData);
            }
        }

        return $params;
    }

    /**
     * 城市列表
     * <AUTHOR>
     * @time   2017-01-10
     *
     * @param  string  keyword  搜索城市名的拼音首字母组合 'fz'
     *
     * @return array 返回符合的城市名数组
     * [['10'=>['name'=>'福州市','code'=>407, 'pin'=>'H'],...]]
     */
    public function getAreaList($result = false)
    {
        $keyword = I('keyword', '', 'trim');
        $citys   = $this->_getAreaList();
        if (count($citys) > 1) {
            unset($citys[0]);
        }

        if (!empty($keyword)) {
            // 拼音首字母缩写搜索城市
            $this->_searchAreaByPinYin($citys, $keyword);
            $citys = [array_values($citys)];

        } else {
            // 按城市首字母分类
            $tempCity = [];
            foreach ($citys as $city) {
                if (empty($city['pin'])) {

                } else {
                    $tempCity[strtoupper($city['pin'])][] = $city;
                }
            }
            ksort($tempCity);
            $citys = $tempCity;
        }
        if ($result) {
            return array_values($citys);
        }

        $this->apiReturn(200, array_values($citys));
    }

    /**
     * 详情页 - 获取景区信息
     * <AUTHOR>
     * @time   2017-01-10
     */
    public function getLandInfo()
    {
        $lid = I('lid', '', 'intval');
        $aid = I('aid', '', 'intval');
        if ($lid < 1) {
            $this->apiReturn(204, [], '参数错误');
        }

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        if ($dataAuthLimit === false || $dataAuthLimit->hasLidBeenLimit($lid)) {
            $this->apiReturn(403, [], '没有权限');
        }

        // 修改接口为java  2018-03-21
        $productBiz  = new bizProduct();
        $landInfoArr = $productBiz->getProductInfo($aid, $lid, 0);

        if ($landInfoArr['code'] != 200) {
            $this->apiReturn(204, [], '请求出错');
        }

        $landInfo               = [];
        $landInfo['p_type']     = $landInfoArr['data']['jtype'];
        $landInfo['title']      = $landInfoArr['data']['title'];
        $landInfo['area']       = $landInfoArr['data']['area'];
        $landInfo['jtzn']       = nl2br($landInfoArr['data']['jtzn']);
        $landInfo['jqts']       = nl2br($landInfoArr['data']['jqts']);
        $landInfo['bhjq']       = htmlspecialchars_decode($landInfoArr['data']['bhjq']);
        $landInfo['imgpath']    = $this->_completeImgPath($landInfoArr['data']['imgpath']);
        $landInfo['imgpathGrp'] = $landInfoArr['data']['imgpathGrp'];
        $landInfo['venue_id']   = $landInfoArr['data']['venus_id'];

        //返回省市名称
        $landInfo['province_name'] = $landInfoArr['data']['province_name'];
        $landInfo['city_name']     = $landInfoArr['data']['city_name'];

        //返回视频相关数据
        $landInfo['videoUrl']     = $landInfoArr['data']['videoUrl'] ?? '';

        //顶级供应商ID
        $landInfo['apply_did']   = $landInfoArr['data']['apply_did'];

        //返回扩展数据
        $landInfo['ext']     = $landInfoArr['data']['ext'];

        //添加微商城链接
        $productBuz      = new \Business\Product\Product();
        $landInfo['wxMallUrl'] = $productBuz->createWxMallUrlForProduct($this->_loginMember['saccount'], $landInfo['p_type'], $lid, $aid);

        // 供应商名称
        if ($aid) {
            $memberInfo = $this->getMemberModel()->getMemberInfo($aid, 'id', 'dname');
        }
        $landInfo['apply_name'] = $memberInfo['dname'] ?: '';

        //如果是场次类产品，添加额外的场次数据
        if ($landInfo['p_type'] == 'H') {
            $configModel = new AdminConfig();
            $result      = $configModel->havePermission($this->_loginMember['memberID'], self::__SELECT_SEAT__);

            $landInfo['select_seat'] = $result ? 1 : 0;

            $showBiz = new ShowBiz();
            $tmpInfo = $showBiz->getLastedRoundList($landInfo['venue_id']);
            $code    = $tmpInfo['code'];

            if ($code == 1) {
                //有获取到数据
                $data = $tmpInfo['data'];

                $landInfo['venue_img']       = $data['venue_img'];
                $landInfo['show_start_date'] = $data['lasted_date'];
                $landInfo['show_round_list'] = $data['round_list'];

            } else {
                //获取场次数据错误
                pft_log('order_show/error', json_encode(['getLandInfo', $landInfo['venue_id'], $tmpInfo]));

                $landInfo['venue_img']       = '';
                $landInfo['show_start_date'] = '';
                $landInfo['show_round_list'] = [];
            }
        }


        if ($landInfo['p_type'] == 'J') {
            $model     = new SpecialtyV2();
            $specialty = $model->getGoodsByGidOrLid(0, $lid);
            $landInfo['goodsDetails']  = $specialty[0]['goodsDetail'];
            $landInfo['selling_point'] = $specialty[0]['attribute']['selling_point'];
            if (!empty($specialty[0]['medias'])) {
                foreach ($specialty[0]['medias'] as $value) {
                    $landInfo['imgpathGrp'][] = $value['url'];
                }
            }
        }

        $this->apiReturn(200, $landInfo);
    }

    /**
     * 预定页面 - 获取价格和库存
     * <AUTHOR>
     * @time   2017-01-10
     *
     * @param  string   pids   产品ID集合 用-分隔 例如 11138-11139
     * @param  int      aid    上级分销商ID
     * @param  date     date   日期 '2017-01-20'
     *
     * @return  ['11138'=>['price'=>1.58,'store'=>100],'11139'=>[..]]
     */
    public function getPriceAndStorage()
    {
        $memberId = $this->getMemberId();
        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        $aid  = I('aid', '', 'intval');
        $date = I('date', date('Y-m-d'));
        $tids = I('tids');
        //分销专员推广id
        $disId = I('post.dis_id', '', 'strval');
        $supportCustomTime = I('post.support_custom_time', false, 'boolval'); //分时预约支持可售时间
        $ordernum = I('post.ordernum', '', 'strval');

        $tidArr = explode('-', $tids);
        if (empty($tidArr[0])) {
            $this->apiReturn(201, [], '未指定产品');
        }

        if (!$tidArr || !$date) {
            $this->apiReturn(201, [], '参数错误');
        }

        $date = date('Y-m-d', strtotime($date));
        if ($date < date('Y-m-d')) {
            $this->apiReturn(202, '请选择正确的日期');
        }

        $channel = 10;
        if ($ordernum) {
            $orderParamConvertApi = new OrderParamConvert();
            $res = $orderParamConvertApi->getShopByOrdernum($ordernum);
            if ($res['code'] == 200) {
                $channel = $res['data'];
            }
        }

        //分销专员下单的下单人记为运营商
        $distMemberModel = new \Model\MultiDist\Member();
        $distProductBus  = new \Business\MultiDist\Product();
        $profitConfig    = [];
        $multiDist = (new \Business\MultiDist\Member())->getMultiDistCache($memberId);
        if (!empty($disId) && !empty($multiDist['multi_dist'])) {
            $memberId = $this->updateOrderMemberNew($disId, true);
            $chainRes = $distProductBus->getUuidInfo($multiDist['multi_dist']['uuid']);
            if (!empty($chainRes)) {
                $topUuid      = $distProductBus->getTopUuidByUid($chainRes['top_uid']);
                $profitConfig = $distProductBus->handleProfitFromLid($topUuid, $tidArr, $aid);
            }
        }

        $ticketStr = implode(',', $tidArr);

        $priceKey = 'settlement';
        //$priceAndStorage = TicketApi::getPriceAndStorageByPlayDate($ticketStr, $memberId, $aid, $date);
        //
        //if (empty($priceAndStorage)) {
        //    $this->apiReturn(208, [], '获取价格出错，请稍后重试');
        //}

        $tPriceBiz          = new \Business\Product\Price();
        $priceAndStorageRes = $tPriceBiz->buyBatchGet($memberId, $aid, $date, $ticketStr, true);

        if ($priceAndStorageRes['code'] != 200 || empty($priceAndStorageRes['data'])) {
            $this->apiReturn(208, [], '获取价格出错，请稍后重试');
        }
        $priceAndStorage = $priceAndStorageRes['data'];

        // 先利用tidArr获取pidArr
        $ticketModel = new Ticket();
        $tidPidArr   = $ticketModel->getMuchTicketInfo($tidArr, 'id, pid, apply_did, is_agreement_ticket');
        //获取分时预约
        $timeShareOrderBz = new TimeShareOrder();
        $ticketShareRes   = $timeShareOrderBz->getTimeSlicesWithTidArr($tidArr, $date, $channel, $supportCustomTime);   //微平台销售渠道为10
        if ($ticketShareRes['code'] != 200) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分时数据获取错误！');
        }
        $ticketShareInfo = $ticketShareRes['data'];
        $return          = [];
        foreach ($priceAndStorage as $key => $val) {

            $pid = $tidPidArr[$key]['pid'];

            $return[$pid]                    = [
                'price'  => $val[$priceKey] != -1 ? $val[$priceKey] / 100 : 0,  //-1表示无价格
                'store'  => $val['availableStorage'] >= -1 ? $val['availableStorage'] : 0,
                'retail' => $val['retail'] != -1 ? $val['retail'] / 100 : 0,  //-1表示无价格
                'ticketId'    => $key
            ];
            $return[$pid]['time_share_info'] = $ticketShareInfo[$key] ?? [];

            //分销专员收益
            if (!empty($profitConfig)) {
                $costPrice                          = intval($val['settlement']);
                $counterPrice                       = intval($val['retail']);
                $rate                               = json_decode($profitConfig[$val['ticketId']], true);
                $rate                               = $rate[$multiDist['multi_dist']['level']] ?? 0;
                $profit                             = round(($counterPrice - $costPrice) * ($rate / 100));
                $return[$pid]['profit'] = $profit;
            }
        }

        //协议票库存处理
        $storageLib = new Storage();
        foreach ($return as &$value) {
            $fid = $memberId;
            $isAgreement = $tidPidArr[$value['ticketId']]['is_agreement_ticket'] ?? 0;
            $applyDid = $tidPidArr[$value['ticketId']]['apply_did'] ?? 0;

            if (!$isAgreement || $fid == $applyDid) {
                continue;
            }

            $sid = $aid;
            $pid = $tidPidArr[$value['ticketId']]['pid'] ?? 0;
            $time = strtotime($date);
            $storageRes = $storageLib->queryStorageByOrderPage($fid, $sid, $pid, $time);
            if ($storageRes['code'] != 200 || empty($storageRes['data'])) {
                continue;
            }

            if ($storageRes['data']['storage'] == -1) {
                continue;
            }

            $value['store'] = $storageRes['data']['storage'];
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 预定页面 - 获取单个酒店产品的价格和库存（针对酒店）
     * <AUTHOR>
     * @time   2017-02-15
     *
     * @param  date $beginDate 入驻日期
     * @param  date $endDate 离开日期
     * @param  int $aid 上级分销商ID
     * @param  int $pid 酒店产品ID
     *
     * @return ajax返回
     */
    public function getHotelPriceAndStorage()
    {
        $memberId = $this->getMemberId();
        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        $beginDate = I('beginDate', 0, 'strtotime');
        $endDate   = I('endDate', 0, 'strtotime');
        $pid       = I('pid', 0, 'intval');
        $aid       = I('aid');
        $tid       = I('tids');
        //分销专员推广id
        $disId = I('post.dis_id', '', 'strval');

        if (!$pid || !$aid) {
            $this->apiReturn(201, [], '参数错误');
        }

        if ($beginDate < strtotime(date('Y-m-d')) || $endDate < $beginDate) {
            $this->apiReturn(201, [], '请选择正确的时间');
        }

        $beginDate = date('Y-m-d', $beginDate);
        $endDate   = date('Y-m-d', $endDate - 86400);
        $memberId  = $this->getMemberId();

        //分销专员下单的下单人记为运营商
        $disId && $memberId = $this->updateOrderMemberNew($disId, true);

        $retailResArr = TicketApi::getOrderCalendar($memberId, $aid, $tid, $beginDate, $endDate);
        if (!$retailResArr) {
            $this->apiReturn(201, [], '获取价格库存失败');
        }

        // 预订天数计算
        $orderDays = floor((strtotime($endDate) - strtotime($beginDate)) / 86400);
        if ((count($retailResArr) - 1) != $orderDays) {
            $this->apiReturn(204, [], '发现有的日期无库存');
        }

        // 返回数据处理
        $totalPirce = 0;   // 总价
        $minStorage = 0;   // 最小日库存
        $i          = 1;   // 第一遍循环

        foreach ($retailResArr as $priceStorgeVal) {
            $totalPirce += $priceStorgeVal['settlement'];
            if ($i == 1) {
                $minStorage = $priceStorgeVal['availableStorage'];
            }
            if ($priceStorgeVal['availableStorage'] < $minStorage && $priceStorgeVal['availableStorage'] != -1) {
                $minStorage = $priceStorgeVal['availableStorage'];
            }
        }

        $return = [
            'pid'      => $pid,
            'jsprice'  => $totalPirce / 100,
            'minStore' => $minStorage,
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 获取单个产品的实时分销价
     *
     * @param  array $pid pid
     * @param  int $aid 上级供应商id
     * @param  string $date 日期 2016-08-10
     *
     * @return  [1026 => 1.85]
     */
    public function getPidDisPrice()
    {
        $memberId = $this->getMemberId();

        $pid  = I('pid', 0, 'intval');
        $aid  = I('aid', 0, 'intval');
        $date = I('date', '');

        if (!$pid || !$aid || !$date) {
            $this->apiReturn('201', [], '参数有误');
        }

        $date = date('Y-m-d', strtotime($date));
        $res  = $this->_Dynamic_Price_And_Storage(
            $memberId, $pid, $date, 1, 0, 0, $aid, $date, $date, 'id'
        );

        $priceSet = [];
        if ($res['code'] == 200) {
            if ($res['price'] != -1) {
                $priceSet[$pid] = $res['price'] / 100;
            }
        }

        $this->apiReturn('200', $priceSet);
    }

    /**
     * 预定页面 - 日历零售价
     * <AUTHOR>
     * @time   2017-01-10
     *
     * @param  string   pid    产品ID
     * @param  int      aid    上级分销商ID
     * @param  date     date   开始日期 '2017-01-20' 默认今天开始
     *
     * @return  ["2017-01-01"=>0.02,"2017-01-02"=>0.01,...]
     */
    public function getCalendarPrice()
    {
        if (!$this->getMemberId()) {
            $this->returnAutoLogin('请先登录');
        }

        $pid  = I('pid', '', 'intval');
        $aid  = I('aid', '', 'intval');
        $date = I('date', date('Y-m-d'));
        $date = date('Y-m-d', strtotime($date));

        if (!$pid || !$date || !$aid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $tModel    = $this->getTicketModel();
        $ticketBiz = new \Business\Product\Ticket();

        // 最近的有价格日期
        $minDate = $ticketBiz->getHasRetailPriceDate($pid);
        if ($date < $minDate) {
            $date = $minDate;
        }

        // 设置时间段
        $beginDate = date('Y-m-d', strtotime($date));
        $tmp       = date('Y-m-01', strtotime($date));
        $endDate   = date('Y-m-d', strtotime("$tmp +1 month -1 day"));
        $tidPidArr = $tModel->getTicketInfoByPid($pid, 'id, pid');

        $priceSet     = [];
        $priceListArr = TicketApi::getOrderCalendar($this->getMemberId(), $aid, $tidPidArr['id'], $beginDate, $endDate);

        if (!$priceListArr) {
            $this->apiReturn(204, [], '无此价格');
        }

        // 这里分销价可能有坑--标记
        foreach ($priceListArr as $k => $priceStorgeVal) {
            $priceSet[$priceStorgeVal['time']] = $priceStorgeVal['settlement'] / 100;   // 成本价
        }

        $this->apiReturn(200, $priceSet);
    }

    /**
     * 获取日历价格接口
     * Create by zhangyangzhen
     * Date: 2019/6/20
     * Time: 17:35
     */
    public function getCalendarData()
    {
        $memberId = $this->getMemberId();

        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        $ticketId = I('post.ticket_id', '', 'intval');
        $month    = I('post.month', '', 'strval');
        $aid      = I('post.aid', '', 'intval');
        $fsid     = I('post.fsid', '', 'intval');
        //分销专员推广id
        $disId = I('post.dis_id', '', 'strval');

        if (!$ticketId || !$month || !$aid) {
            $this->apiReturn(201, [], '参数有误');
        }

        $startDate = $month . '-01';
        $endDate   = $month . date('-t', strtotime($month));

        //日历模式的请求就是自己的
        if ($aid == -1) {
            $aid = $memberId;
        }

        if ($fsid) {
            $memberId = $fsid;
        }

        //分销专员下单的下单人记为运营商
        $disId && $memberId = $this->updateOrderMemberNew($disId, true);

        $tPriceBiz = new \Business\Product\Price();
        $calendar  = $tPriceBiz->buyGetCalendar($memberId, $aid, $startDate, $endDate, $ticketId, true);
        //$calendar = TicketApi::getOrderCalendarV2($memberId, $aid, $ticketId, $startDate, $endDate, $this->_channel);

        if ($calendar['code'] == self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_SUCCESS, $calendar['data'], 'success');
        } else {
            $this->apiReturn(self::CODE_NO_CONTENT, [], '暂无日历信息');
        }
    }

    /**
     * 主题列表
     * <AUTHOR>
     * @time   2017-01-10
     */
    public function getThemes()
    {
        $memberId = $this->getMemberId();

        $shopConfigModel = new \Model\Subdomain\ShopConfig();
        $config          = $shopConfigModel->getMallConfig($memberId);
        $others          = json_decode($config['others'], true);

        $themes = [

            // 景区
            'A' => $others['themes'] ?? [],
            // 线路
            'B' => $others['themes'] ?? [],

            // 酒店
            'C' => [
                '五星级',
                '四星级',
                '三星级',
                '二星级下',
            ],

            // 套票
            'F' => [],

            // 演出
            'H' => [],
        ];

        $type = I('type', '');

        if ($type && in_array($type, $this->_allowType)) {
            $return = $themes[$type];
        } else {
            $return = $themes;
        }
        $this->apiReturn(200, $return, $type . '主题列表');
    }

    /**
     * 所有类型
     * <AUTHOR>
     * @time   2017-01-10
     *
     * @param  bool $return 返回方式[true返回数据|false=ajax返回]
     */
    public function getAllType($return = true)
    {
        $allType = [
            'A' => '景区',
            'B' => '线路',
            'C' => '酒店',
            'F' => '套票',
            'H' => '演出',
            // 'I' => '年卡', // 暂不允许销售
            'G' => '餐饮',
        ];

        if ($return) {
            return $allType;
        } else {
            $this->apiReturn(200, $allType);
        }
    }

    /**
     * 订单查询-获取可分销产品名称
     * <AUTHOR>
     * @time   2017-06-19
     *
     * @param  int $num 数量
     * @param  string $key 关键字
     */
    public function getProductNameList()
    {
        $sid = $this->_loginMember['sid'];
        $num = I('num', 10, 'intval');
        $key = I('key', '', 'trim');
        if (!$key) {
            $this->apiReturn(201, [], '请输入关键字');
        }

        if (!$num) {
            $this->apiReturn(201, [], '非法数量');
        }

        // 关键字筛选产品
        $allPro = $this->_getMemberAllProduct($sid, $key, $num);

        $this->apiReturn(200, $allPro);
    }

    /**
     * 门票分页
     * <AUTHOR>
     * @time   2017-01-10
     *
     * @param  array tickets  票列表
     * @param  int   pageSize 分页页面数据条数
     *
     * @return 分页后的票列表
     */
    private function _ticketPageDeal($tickets = [], $pageSize = 0)
    {
        if (!$tickets) {
            return [];
        }

        $pageTickets = []; // 分页数据

        if ($this->_lastTid == 0 || $this->_lastTicketPos == 0) {
            // 首次取门票
            $pageTickets = array_slice($tickets, 0, $pageSize);

        } else {
            // 再次取门票 - [缓存过期后/排序有变/门票有新增, 导致原门票列表顺序变动]
            $tickets = array_slice($tickets, $this->_lastTicketPos - 1); // 去掉已取门票
            if (count($tickets) <= 1) {
                // 超过可取门票范围
                return [];
            }

            // 门票列表顺序是否已变动 1-旧顺序 2-新顺序
            $isNewSort = $this->_lastTid == $tickets[0]['tid'] ? 1 : 2;

            if ($isNewSort == 1) {
                // 旧顺序
                $pageTickets = array_slice($tickets, 1, $pageSize);

            } elseif ($isNewSort == 2) {
                // 新顺序 - 暂时同旧顺序一样处理
                $pageTickets = array_slice($tickets, 1, $pageSize);
            }
        }

        // 实际获取产品数量
        $ticketSize = count($pageTickets);
        if ($ticketSize != $pageSize) {
            $pageSize = $ticketSize;
        }

        // 更新门票列表偏移量
        $this->_lastTicketPos += $pageSize;

        // 更新最近取的门票Tid
        $lastTicket     = end($pageTickets);
        $this->_lastTid = $lastTicket['tid'];

        return $pageTickets ?: [];
    }

    /**
     * 门票按景区分组
     * <AUTHOR>
     * @time   2017-01-15
     *
     * @param  array $products 产品列表数组   [['pid'=>120001,'lid'=>1001,...],....]
     *
     * @return array $ticketList 各景区门票集合 ['1001'=>['lid'=>1001,'pid'=>120001,...],...]
     */
    private function _getTicketSet($products = [], $aid = 0, $memberId = 0)
    {
        $area     = new Area();
        $otaQuery = new OtaQueryModel();
        $packBiz  = new \Business\Product\PackTicket();
        $areaList = $area->getAreaList();
        if (!is_array($products) || !$products) {
            return [];
        }

        // 套票处理
        $ptypeArr = array_column($products, 'type');
        $ptype    = $ptypeArr[0];

        if ($ptype == 'F') {
            // 返回子票信息
            $products = $this->_parseSonTickets($products);
        }

        if (!is_array($products) || !$products) {
            return [];
        }

        $ticketList = [];

        if (!isset($products['ticket'])) {
            return [];
        }

        $tidArr = array_column($products['ticket'], 'tid');

        $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
        $ticketRes          = $commodityTicketBiz->queryTicketInfoByIds($tidArr);
        if (!$ticketRes) {
            return [];
        }

        $ticketInfoList = [];
        $ticktExtList   = [];
        $venusId        = 0;
        foreach ($ticketRes as $key => $value) {
            $ticketInfo = isset($value['ticket']) ? $value['ticket'] : [];
            $landInfo   = isset($value['land']) ? $value['land'] : [];
            $extInfo    = isset($value['ext']) ? $value['ext'] : [];
            $landfInfo  = isset($value['land_f']) ? $value['land_f'] : [];
            //故意把$ticketInfo放在最后覆盖其他的 id
            $ticketInfoList[$value['ticket']['id']] = array_merge($landInfo, $extInfo, $landfInfo, $ticketInfo);

            $ticktExtList[$value['ticket']['id']] = $extInfo;
            if (!$venusId) {
                $venusId = $value['land']['venus_id'];
            }
        }

        //获取分区数据
        $showBiz     = new ShowBiz();
        $zoneNameArr = $showBiz->getZones($venusId);

        $ticketBiz = new \Business\Product\Ticket();
        foreach ($products['ticket'] as $pro) {
            if (isset($pro['area'])) {
                $splitArea   = explode("|", $pro['area']);
                $pro['area'] = "";
                array_walk($splitArea, function ($value, $key) use ($areaList, &$pro) {
                    if (isset($areaList[$value])) {
                        $pro['area'] = $areaList[$value];
                    }
                });
            } else {
                $pro['area'] = "";
            }

            $usetimeNew = $entranceTimeLimit = $refundRuleNew = '';
            switch ($products['ptype']) {
                case 'F':
                    //退票标签
                    $refundRuleNew = OrderBook::handleRefundRule(0, $pro['refundRule'], $pro['refundEarlyTime']);

                    //套票特殊处理
                    $childData = $packBiz->getChildTickets($pro['tid']);
                    if (!empty($childData)) {
                        $sonPidArr            = array_column($childData, 'pid');
                        //有效期说明
                        $usetimeNew   = OrderBook::parsePackValidTag($sonPidArr);

                        //处理套票退款规则标签，主票不可退即不可退，就不考虑子票的情况了
                        if ($pro['refundRule'] != 2) {
                            $sonTidArr              = array_column($childData, 'tid');
                            $refundRuleNew = OrderBook::parsePackRefundTag($sonTidArr, $memberId, 10, $aid);
                        } else {
                            $refundRuleNew = '不可退';
                        }
                    }

                    break;
                case 'C':
                    //酒店
                    $validData                = [
                        'type'                      => $products['ptype'],
                        'valid_period_start'        => $pro['validStartDateStr'],
                        'valid_period_end'          => $pro['validEndDateStr'],
                        'valid_period_type'         => $pro['validType'],
                        'use_early_days'            => $pro['useEarlyDays'],
                        'valid_period_days'         => $pro['validDays'],
                        'valid_period_timecancheck' => $pro['ifVerify'],
                        'delaytype'                 => $pro['delayType'],
                    ];
                    //有效期说明
                    $usetimeNew = OrderBook::getHotelValidityDate($validData);
                    //退票标签
                    $refundRuleNew = OrderBook::handleRefundRule(0, $pro['refundRule'], $pro['refundEarlyTime']);
                    break;
                case 'J':
                    //特产
                    break;
                case 'H':
                    //演出
                    if (empty($pro['entranceTimeLimit'])) {
                        $entranceTimeLimit = '开演前60分钟可入场';
                    } else {
                        $pro['entranceTimeLimit'] = json_decode($pro['entranceTimeLimit'], true);
                        $entranceTimeLimit = '开演前' . $pro['entranceTimeLimit']['s'] . '分钟可入场';
                    }

                    $pro['advance_booking_str'] = $ticketBiz->advanceBookingTag($ticketInfoList[$pro['tid']]);
                    $pro['refund_after_time']   = $ticktExtList[$pro['tid']]['refund_after_time'] ?? 0;
                    $pro['showLimitTime']       = $ticketBiz->getTimeLimitedSaleTag($ticketInfoList[$pro['tid']]);
                    //退票标签
                    $refundRuleNew = OrderBook::handleRefundRule(0, $pro['refundRule'], $pro['refundEarlyTime'], $ticktExtList[$pro['tid']]['refund_after_time'] ?? 0);
                    break;
                case 'G':
                    //餐饮
                    $validData                = [
                        'type'                      => $products['ptype'],
                        'valid_period_start'        => $pro['validStartDateStr'],
                        'valid_period_end'          => $pro['validEndDateStr'],
                        'valid_period_type'         => $pro['validType'],
                        'use_early_days'            => $pro['useEarlyDays'],
                        'valid_period_days'         => $pro['validDays'],
                        'valid_period_timecancheck' => $pro['ifVerify'],
                        'delaytype'                 => $pro['delayType'],
                    ];
                    //有效期说明
                    $usetimeNew = OrderBook::getFoodValidityDate($validData);
                    //退票标签
                    $refundRuleNew = OrderBook::handleRefundRule(0, $pro['refundRule'], $pro['refundEarlyTime']);
                    break;
                default:
                    $tmpExtInfo = $ticktExtList[$pro['tid']] ?? [];

                    $validData = [
                        'type'                      => $products['ptype'],
                        'valid_period_start'        => $pro['validStartDateStr'],
                        'valid_period_end'          => $pro['validEndDateStr'],
                        'valid_period_type'         => $pro['validType'],
                        'use_early_days'            => $pro['useEarlyDays'],
                        'valid_period_days'         => $pro['validDays'],
                        'valid_period_timecancheck' => $pro['ifVerify'],
                        'delaytype'                 => $pro['delayType'],
                        'pre_sale'                  => $pro['preSale'],
                        'ext'                       => $tmpExtInfo,
                    ];
                    //有效期说明
                    $usetimeNew = OrderBook::getValidityDate($validData);
                    //退票标签
                    $refundRuleNew = OrderBook::handleRefundRule(0, $pro['refundRule'], $pro['refundEarlyTime']);
            }

            $ticket = [
                'lid'           => $pro['lid'],
                'aid'           => $pro['aid'],
                'tid'           => $pro['tid'],
                'fid'           => $pro['aid'],
                'area'          => $products['area'],
                'title'         => $pro['ttitle'],
                'sprice'        => $pro['counter'] / 100,
                'counter_price' => $pro['counter'] / 100,
                'tprice'        => $pro['settlement'] / 100,
                'lprice'        => $pro['retail'] / 100,
                'refund_rule'   => $pro['refundRule'],
                'p_type'        => $products['ptype'],
                'notes'         => nl2br($pro['explain']),
                'ttitle'        => $pro['ttitle'],
                'pay'           => $pro['pay'],
                'ddays'         => $pro['ddays'],
                'buy_limit_up'  => $pro['buyLimitUp'],
                'buy_limit_low' => $pro['buyLimitLow'],
                'pid'           => $pro['pid'],
                'pre_sale'      => $pro['preSale'],

                'validStartDateStr'     => $pro['validStartDateStr'],
                'validEndDateStr'       => $pro['validEndDateStr'],
                'validType'             => $pro['validType'],
                'useEarlyDays'          => $pro['useEarlyDays'],
                'validDays'             => $pro['validDays'],
                'ifVerify'              => $pro['ifVerify'],
                'delayType'             => $pro['delayType'],
                'refundAudit'           => $pro['refundAudit'],
                'refundRule'            => $pro['refundRule'],
                'refundEarlyTime'       => $pro['refundEarlyTime'],
                'usetimeNew'            => $usetimeNew,
                'refundRuleNew'         => $refundRuleNew,
                'entrance_time_limit'   => $entranceTimeLimit,
                'usetime'               => $pro['usetime'],
                'refundType'            => $pro['refundType'],
                'refundValue'           => $pro['refundValue'],
                'advance_booking_str'   => $pro['advance_booking_str'] ?? '',
                'refund_after_time'     => $pro['refund_after_time'] ?? 0,
                'showLimitTime'         => $pro['showLimitTime'] ?? '',
            ];

            if ($ptype == 'F') {
                $ticket['sonTickets'] = $pro['sonTickets'];
            }

            //给门票添加分区信息
            $zone_id             = $otaQuery->getLandExtInfo('', $pro['pid'], 'zone_id');
            $zoneId              = intval($zone_id['zone_id']);
            $ticket['zone_id']   = $zoneId;
            $ticket['zone_name'] = isset($zoneNameArr[$zoneId]) ? $zoneNameArr[$zoneId]['zone_name'] : '';

            $lid = $pro['lid'];

            $ticketList[$lid][$pro['aid']][] = $ticket;

        }

        return $ticketList ?: [];
    }

    /**
     * 解析子票信息
     * <AUTHOR>
     * @time   2017-01-15
     *
     * @param  array $tickets 门票列表
     *
     * @return array              [description]
     */
    private function _parseSonTickets($tickets = [])
    {
        if (!is_array($tickets) || !$tickets) {
            return [];
        }

        //$ticketApi = new TicketApi();
        //$javaApi = new \Business\JavaApi\Product\PackageTicket();
        $packApi = new \Business\PackTicket\PackRelation();
        foreach ($tickets as $key => $item) {
            //$sonTickets = $javaApi->queryPageTicketInfoListByParentId($item['tid']);
            //$sonTickets = $ticketApi->getSonTicketList($item['id']);
            $sonTickets = $packApi->queryPageTicketInfoListByParentId($item['tid']);
            // 兼容写法 ，有改版记得修改简单点
            if ($sonTickets['code'] == 200) {
                foreach ($sonTickets['data'] as $sonKey => $sonVal) {
                    $tickets[$key]['sonTickets'][$sonKey]['num']   = $sonVal['num'];
                    $tickets[$key]['sonTickets'][$sonKey]['title'] = $sonVal['ticket_name'];
                    $tickets[$key]['sonTickets'][$sonKey]['lid']   = $sonVal['item_id'];
                }
            }
        }

        return $tickets ?: [];
    }

    /**
     * 解析票类的一些标签属性
     * <AUTHOR>
     * @time   2017-01-16
     *
     * @param  array $tickets 门票列表
     *
     * @return 整理好的属性文本
     */
    private function _parseTicketsTags($tickets = [])
    {
        $tags = [];

        if ($tickets && is_array($tickets)) {
            foreach ($tickets as $item) {
                if ($item['ddays'] > 0) {
                    $tags[$item['tid']][] = "提前{$item['ddays']}天";
                }

                if ($item['pay'] == 0) {
                    $tags[$item['tid']][] = '现场支付';
                }

                //退票规则：添加了这个值 -1=不可退且是可提现
                if ($item['refund_rule'] == 2 || $item['refund_rule'] == -1) {
                    $tags[$item['tid']][] = '不可退';
                }
            }
        }

        return $tags;
    }

    /**
     * 获取景区所在城市列表code
     * <AUTHOR>
     * @time   2017-01-10
     *
     * @return [['10'=>['name'=>'福州市','code'=>407, 'pin'=>'H'],...]]
     */
    private function _getAreaList()
    {
        $memberId      = $this->getMemberId();
        $getEvoluteBiz = new \Business\Product\Get\Evolute();
        $result        = $getEvoluteBiz->getEvoluteByFidShopStatus([$memberId], 10, 0);
        if (empty($result)) {
            return [];
        }

        $lidArr = array_column($result, 'lid');

        if (!$lidArr) {
            return [];
        }

        $lidArr = array_unique($lidArr);

        $LModel = new Land('slave');

        $citys = [];   //城市code

        $javaAPi  = new \Business\CommodityCenter\Land();
        $landInfo = $javaAPi->queryLandMultiQueryById($lidArr, '', '', false, '', [], [1]);
        $areaArr  = array_column($landInfo, 'area');
        if ($areaArr) {
            $model = new Area('slave');
            $areas = $model->getAreaList();
            foreach ($areaArr as $item) {
                $cityCode  = explode('|', $item)[1];
                $cityName  = $areas[$cityCode];
                $fisrtChar = $this->_getFirstCharter($cityName);
                $citys[]   = [
                    'name' => $cityName,
                    'code' => $cityCode,
                    'pin'  => $fisrtChar,
                ];
            }
        }

        return $citys;
    }

    /**
     * 使用城市名的拼音首字母组合 搜索城市
     * <AUTHOR>
     * @time   2017-01-10
     *
     * @param  array  citys         城市数组 [['name'=>'福州',...], [...], ...]
     * @param  string searchCity    搜索城市名的拼音首字母组合 'fz'
     *
     * @return null 返回符合的城市名数组
     */
    private function _searchAreaByPinYin(&$citys, $searchCity)
    {
        if (!$citys || !is_array($citys) || !trim($searchCity)) {
            return;
        }

        $searchLength = mb_strlen($searchCity, 'UTF-8');
        foreach ($citys as $key => $city) {
            for ($i = 0; $i < $searchLength; $i++) {
                $fisrtChar  = $this->_getFirstCharter(mb_substr($city['name'], $i, 1, 'UTF-8'));
                $searchChar = strtoupper(mb_substr($searchCity, $i, 1, 'UTF-8'));
                if ($fisrtChar != $searchChar) {
                    unset($citys[$key]);
                    break;
                }
            }
        }

        return;
    }

    /**
     * 修正数据表中无效的图片路径
     * <AUTHOR>
     * @time   2017-01-10
     *
     * @return string 完整的图片url地址
     */
    private function _completeImgPath($imgpath = '')
    {
        $imgHost = str_replace('wx', 'images', MOBILE_DOMAIN);

        if ($imgpath && !(0 === stripos($imgpath, 'http'))) {
            $imgpath = $imgHost . $imgpath;
        }

        return $imgpath ?: ($imgHost . '/images/defaultThum.jpg');
    }

    /**
     * php获取中文字符拼音首字母
     *
     * @param  string $str 中文字符
     */
    private function _getFirstCharter($str)
    {
        if (empty($str)) {
            return '';
        }

        $fchar = ord($str{0});

        if ($fchar >= ord('A') && $fchar <= ord('z')) {
            return strtoupper($str{0});
        }

        $s1 = iconv('UTF-8', 'gb2312', $str);
        $s2 = iconv('gb2312', 'UTF-8', $s1);
        $s  = $s2 == $str ? $s1 : $str;

        $asc = ord($s{0}) * 256 + ord($s{1}) - 65536;
        if ($asc >= -20319 && $asc <= -20284) {
            return 'A';
        }
        if ($asc >= -20283 && $asc <= -19776) {
            return 'B';
        }
        if ($asc >= -19775 && $asc <= -19219) {
            return 'C';
        }
        if ($asc >= -19218 && $asc <= -18711) {
            return 'D';
        }
        if ($asc >= -18710 && $asc <= -18527) {
            return 'E';
        }
        if ($asc >= -18526 && $asc <= -18240) {
            return 'F';
        }
        if ($asc >= -18239 && $asc <= -17923) {
            return 'G';
        }
        if ($asc >= -17922 && $asc <= -17418) {
            return 'H';
        }
        if ($asc >= -17417 && $asc <= -16475) {
            return 'J';
        }
        if ($asc >= -16474 && $asc <= -16213) {
            return 'K';
        }
        if ($asc >= -16212 && $asc <= -15641) {
            return 'L';
        }
        if ($asc >= -15640 && $asc <= -15166) {
            return 'M';
        }
        if ($asc >= -15165 && $asc <= -14923) {
            return 'N';
        }
        if ($asc >= -14922 && $asc <= -14915) {
            return 'O';
        }
        if ($asc >= -14914 && $asc <= -14631) {
            return 'P';
        }
        if ($asc >= -14630 && $asc <= -14150) {
            return 'Q';
        }
        if ($asc >= -14149 && $asc <= -14091) {
            return 'R';
        }
        if ($asc >= -14090 && $asc <= -13319) {
            return 'S';
        }
        if ($asc >= -13318 && $asc <= -12839) {
            return 'T';
        }
        if ($asc >= -12838 && $asc <= -12557) {
            return 'W';
        }
        if ($asc >= -12556 && $asc <= -11848) {
            return 'X';
        }
        if ($asc >= -11847 && $asc <= -11056) {
            return 'Y';
        }
        if ($asc >= -11055 && $asc <= -10247) {
            return 'Z';
        }

        return null;
    }

    /**
     * 当前登录分销商或供应商身份的所有可销售产品
     * TODO:暂时恢复回来，后期需要统一对接到java去
     * 会请求的页面：http://124078.12301.cc/wx/b/order_query.html -> 通过产品名称去查询
     * 之前是在这个提交“fefb6d48fdd8add145ab0c22514d88ae6d5f8a8e ”被删除
     *
     * <AUTHOR>
     * @time   2017-01-10
     * @modify 20180-9-11
     *
     * @param  int     memberId     用户ID
     * @param  array   option       筛选条件
     */
    private function _getMemberAllProduct($memberId, $option, $num)
    {
        if (!$memberId) {
            return [];
        }
        $listingModel = new Listing();
        $productList  = $listingModel->getGroundingListing($memberId, $option, '', $num);
        if ($productList['code'] != 200) {
            return [];
        }

        return $productList['data'];
    }

    /**
     * 得到指定产品当前可用的优惠券列表
     * <AUTHOR>
     * @date   2017-07-18
     *
     * @param  int $fid 获取优惠券的用户ID
     * @param  int $aid 优惠券的供应商ID
     * @param  int $pid 优惠券指定使用的产品ID
     * @param  date $useDate 优惠券使用日期
     *
     * @return ['valid'=>[], 'other'=>[]]
     */
    public function getCouponList()
    {

        $this->apiReturn(204, [], '');

        $fid = $this->_loginMember['sid'];
        $aid = $this->_supplyId;

        $pid     = I('pid', 0, 'intval');
        $useDate = I('date', time(), 'strtotime');
        $useDate = date('Y-m-d', $useDate);
        $today   = date('Y-m-d');
        if ($useDate < $today) {
            $useDate = $today;
        }

        $bus = new \Business\Order\Coupon();
        $res = $bus->getCouponList($fid, $aid, $pid, $useDate);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $return = $res['data'];

        // 其他优惠券 - 适用产品信息
        if (!empty($return['other'])) {

            $other = $bus->getCouponProductName($return['other']);
            if ($other['code'] != 200) {
                $this->apiReturn($other['code'], [], $other['msg']);
            }

            $return['other'] = $other['data'];
        }

        $this->apiReturn(200, $return);
    }

    /**
     * 产品详情页面接口
     * <AUTHOR>
     * @date 2020/6/16
     *
     * @return array
     */
    public function info()
    {
        $lid = I('post.lid', 0, 'intval');//景区ID
        $aid = I('post.aid', 0, 'intval');//上级供应商ID

        if (empty($lid) || empty($aid)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数缺失');
        }

        $memberId = $this->getMemberId();
        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        //查询产品详情
        $productBiz = new \Business\Product\Product();
        $landInfo   = $productBiz->getWPTProductInfo($memberId, $aid, $lid, $this->_channel);

        if ($landInfo['code'] != self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请求出错');
        }

        $result = $productBiz->handleLandInfo($landInfo['data']);

        $this->apiReturn(self::CODE_SUCCESS, $result);
    }

    /**
     * 验证是否开通用户旅游小店
     * <AUTHOR>
     * @date   2020-12-15
     *
     * @return array
     */
    private function _authorityOpenTourismShop()
    {
        $memberId = $this->isLogin();
        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }
        $sdype = $this->_loginMember['sdtype'];

        //目前不给员工权限
        if ($sdype == 6) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '非法访问');
        }

        $isopen = (new \Business\AppCenter\Module())->checkUserIsCanUseApp($memberId, 'tourist_shop');

        if (empty($isopen)) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '非法访问');
        }
    }

    /**
     * 获取用户旅游小店专题列表
     * <AUTHOR>
     * @date   2020-12-15
     *
     * @return array
     */
    public function getTouristShopSubjectList()
    {
        $this->_authorityOpenTourismShop();
        $evoluteState = I('post.state', -1, 'intval');
        $pageNum      = I('post.page', 1, 'intval');
        $pageSize     = I('post.size', 10, 'intval');
        $memberId     = $this->isLogin();

        //分销状态:-1:全部, 0=停止分销,1=分销中
        if (!in_array($evoluteState, [-1, 0, 1])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分销状态参数错误');
        }
        $channel    = 10;
        $productBuz = new \Business\ResourceCenter\Product();
        $result     = $productBuz->queryResourceRecommendUserConfigListByMemberId($memberId, $channel, $evoluteState,
            $pageNum,
            $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取资源中心推荐景区列表 可返回门票列表
     * <AUTHOR>
     * @date   2020-12-15
     *
     * @return array
     */
    public function getResourceRecommendLandList()
    {
        //验证
        $this->_authorityOpenTourismShop();
        //推荐id
        $recommendId = I('post.recommend_id', 0, 'intval');
        //门票列表返回状态: 0:默认不返回,1:返回门票3条
        $ticketListState = I('post.ticket_list_state', 1, 'intval');
        //销售状态: 1=出售中,0=停售
        $salesStatus = I('post.sales_status', 1, 'intval');
        //供应商名称
        $supplierName = I('post.supplier_name', '', 'strval,trim');
        $landName     = I('post.land_name', '', 'strval,trim');
        $provinceCode = I('post.province_code', 0, 'intval');
        $cityCode     = I('post.city_code', 0, 'intval');
        //景区类型
        $landType = I('post.land_type', '', 'strval,trim');
        //景区主题
        $topic    = I('post.topic', '', 'strval,trim');
        $pageNum  = I('post.page', 1, 'intval');
        $pageSize = I('post.size', 10, 'intval');
        $memberId = $this->isLogin();

        if (!$recommendId) {
            $this->apiReturn(203, [], '参数错误');
        }
        //门票列表返回状态: 0:默认不返回,1:返回门票3条
        if (!in_array($ticketListState, [0, 1])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分销状态参数错误');
        }
        $channel    = 10;
        $productBuz = new \Business\ResourceCenter\Product();
        $result     = $productBuz->queryResourceRecommendLandList($memberId, $recommendId, $channel, $salesStatus,
            $ticketListState, $supplierName, $landName, $provinceCode, $cityCode, $landType, $topic, $pageNum,
            $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取资源中心推荐专题下景区内门票列表
     * <AUTHOR>
     * @date   2020-12-15
     *
     * @return array
     */
    public function getResourceRecommendTicketList()
    {
        //验证
        $this->_authorityOpenTourismShop();
        //推荐id
        $recommendId = I('post.recommend_id', 0, 'intval');
        //过滤门票ids (1,2,3)
        $filterTids  = I('post.filter_tids', '', 'strval,trim');
        $lid         = I('post.lid', 0, 'intval');
        $sid         = I('post.sid', 0, 'intval');
        $pageNum     = I('post.page', 1, 'intval');
        $salesStatus = I('post.sales_status', 0, 'intval');
        $pageSize    = I('post.size', 10, 'intval');
        $memberId    = $this->isLogin();

        if (!$memberId || !$recommendId || !$lid || !$sid) {
            $this->apiReturn(203, [], '参数错误');
        }
        $channel    = 10;
        $productBuz = new \Business\ResourceCenter\Product();
        $result     = $productBuz->queryResourceRecommendTicketList($memberId, $recommendId, $sid, $lid, $channel,
            $salesStatus, $filterTids,
            $pageNum, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 修改用户旅游小店专题状态
     * <AUTHOR>
     * @date   2020-12-15
     *
     * @return array
     */
    public function updateResourceRecommendUserConfigEvoluteSatet()
    {
        //验证
        $this->_authorityOpenTourismShop();
        $recommendId = I('post.recommend_id', 0, 'intval');
        //分销状态: 0=停止分销,1=分销中
        $evoluteState = I('post.state', -1, 'intval');
        $memberId     = $this->isLogin();

        if (!$memberId || !$recommendId) {
            $this->apiReturn(203, [], '参数错误');
        }
        if (!in_array($evoluteState, [0, 1])) {
            $this->apiReturn(203, [], '分销状态参数错误');
        }

        $productBuz = new \Business\ResourceCenter\Product();
        $result     = $productBuz->updateResourceRecommendUserConfigEvoluteSatet($memberId, $evoluteState, $recommendId,
            $this->_loginMember['memberID']);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 主题列表
     * <AUTHOR>
     * @date 2021/6/18
     *
     * @return array
     */
    public function getThemesNew()
    {
        $themeConst = ThemeConst::THEMES;

        $data = [];
        foreach ($themeConst as $value) {
            $data['A'][$value] = [
                'name'      => $value,
                'isChecked' => 'true'
            ];
        }

        $this->apiReturn(200, $data, '主题列表');
    }
}