<?php
/**
 * 特殊团队订单预约
 * Author: xwh
 * Date: 2021-03-25
 */

namespace Controller\MicroPlat;

use Business\Authority\AuthContext;
use Business\Authority\AuthLogic as AuthLogicBiz;
use Business\JavaApi\Product\EvoluteListQuery;
use Business\JavaApi\TicketApi;
use Business\Order\OrderBook;
use Controller\MicroPlat\Common as Common;

use Model\Order\OrderHandler;
use Model\Order\OrderTools;
use Model\Product\Land;

class TeamOrder extends Common
{
    const __SPECIAL_ORDER__      = 25; //特殊订单管理目录权限
    const __FORCE_CHECK_SOURCE__ = 46; //强制核销track_source
    const __LOG_PATH__           = 'force_check'; //日志记录目录
    //未使用，已过期，撤改，部分使用的订单可以被强制核销
    private $_checkStatus = [0, 2, 5, 7, 10];
    public function __construct()
    {
        parent::__construct();
        $this->isLogin('auto');
    }

    /**
     * 特殊团队预约订单提交
     * date   2021-03-24
     * author xwh
     *
     * return
     */
    public function reservationSpecialTeamOrder()
    {
        $this->isLogin('auto');

        @pft_log('order/reservation/specialTeamOrder', json_encode($_REQUEST, JSON_UNESCAPED_UNICODE), $pathMode = 'day');

        //特殊团体类型
        $specialType = I('post.special_type', 0, 'intval');

        //权限校验  供应商与员工
        if (in_array($this->_loginMember['dtype'],[0,6])) {
            $check = $this->_checkAuthoritySpecialTeamOrder($this->_loginMember['memberID'],'reservation_special_team_order',$this->_loginMember['dtype'], $specialType);
            if (!$check) {
                $this->apiReturn(204, [], '无权限');
            }
        }else{
            $this->apiReturn(204, [], '无权限');
        }

        //产品id
        $pid = I('post.pid', 0, 'intval');
        //供应商ID
        $aid = I('post.aid', 0, 'intval');
        //游玩时间
        $begintime = I('post.begintime', '', 'strval');
        //取票人姓名
        $ordername = I('post.ordername', 0, 'strval');
        //取票人联系方式
        $ordertel = I('post.contacttel', 0, 'strval');
        //合并付款门票参数
        $proidList = I('post.proid', []);

        $memberId     = $this->_loginMember['sid'];
        $opid         = $this->_loginMember['memberID'];
        $unitName     = I('post.unit_name', '', 'strval,trim');
        $specialLevel = I('post.special_level', 0, 'intval');

        $platformOrderBiz = new \Business\ReservationSpecialTeam\TeamOrder();

        //库存为0时可预约权限效验
        $authRes = $platformOrderBiz->checkBtnAuthMenuStorage($specialType, $opid, $proidList, $memberId, $this->_loginMember['dtype']);
        if (!$authRes) {
            $this->apiReturn(400, [], '无权限！');
        }

        $orderRes         = $platformOrderBiz->reservationSpecialTeamOrder($memberId, $opid, $pid, $aid, $begintime, $ordername, $unitName, $specialLevel, $specialType, $ordertel, $proidList);

        $this->apiReturn($orderRes['code'], $orderRes['data'], $orderRes['msg']);
    }



    /**
     * 根据条件获取特殊团队接待订单信息
     * @author: xwh
     * @date: 2021/3/23
     */
    public function querySpecialTeamOrderList()
    {
        $sid       = $this->_loginMember['sid'];
        //特殊团队类型
        $type      = I('post.type', -1, 'intval');
        $timeType  = I('post.time_type', -1, 'intval');
        $startTime = I('post.start_time', '', 'strval,trim');
        $endTime   = I('post.end_time', '', 'strval,trim');
        $status    = I('post.status', -1, 'intval');
        $lid       = I('post.lid', 0, 'intval');
        $tid       = I('post.tid', 0, 'intval');
        $page      = I('post.page', 1, 'intval');
        $size      = I('post.size', 10, 'intval');
        $keyword   = I('post.key', '', 'strval,trim');
        $word      = I('post.word', '', 'strval,trim');
        $sortName  = I('post.sort_name', '', 'strval,trim');
        $sortType  = I('post.sort_type', -1, 'intval');
        if (!in_array($keyword,['phone','unitName','name','ordernum'])) {
            $this->apiReturn(203, [], '参数错误');
        }
        $key = ['phone' => '','ordernum' => '','name' => '','unitName' => ''];
        $key[$keyword] = $word;

        $result = (new \Business\ReservationSpecialTeam\TeamOrder())->querySpecialTeamOrderList($sid, $type, $timeType, $startTime, $endTime, $status, $key['ordernum'], $lid, $tid,  $key['phone'], $key['unitName'], $key['name'], $page, $size, $sortName, $sortType);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }
    }
    /**
     * 检验是否有特殊团队下单权限
     * <AUTHOR>
     * @date 2021-03-24
     *
     * @param  int $mid 当前用户id
     * @param  string $menu 菜单标签
     * @param  int $dtype 用户类型
     * @param  int $specialType 特殊团体类型
     *
     * @return boolean
     */
    private function _checkAuthoritySpecialTeamOrder($mid, $menu, $dtype, $specialType)
    {
        if (!$mid || !$menu || !in_array($dtype,[0,6]) || !in_array($specialType,[0,1])) {
            return false;
        }
        if (!$dtype) {
            $isOpen = (new \Business\AppCenter\Module())->checkUserIsCanUseApp($mid, $menu,false);
            if (!$isOpen) {
                $this->apiReturn(204, [], '无权限');
            }

        }
        if ($dtype == 6){
            $specialTypes = [
                0 => "governmentReception",    //政务接待
                1 => "cadreAcademy",        //干部学院
            ];

            $res = (new AuthLogicBiz())->resource($this->_loginMember['sid'], $mid, $menu, $this->_loginMember['dtype'], 1);

            if (!in_array($specialTypes[$specialType],$res)) {
                return false;
            }
        }
        return true;
    }
    /**
     * 获取票填写更多信息
     * @author: xwh
     * @date: 2021/3/23
     */
    public function getTicketMoveInfo()
    {
        $ticketId   = I('post.ticket_id', '', 'intval');
        $landId     = I('post.land_id', '', 'intval');

        if (!$ticketId || !$landId) {
            $this->apiReturn(201, '', '参数错误');
        }
        $memberId       = $this->_loginMember['sid'];
        //获取门票数据
        $data = TicketApi::getBookTickets($memberId, $memberId, $landId, $ticketId, 10);
        if ($data['code'] != 200) {
            $this->apiReturn($data['code'], '', $data['sub_msg']);
        }

        $ticketData = $data['data'];
        if (empty($ticketData)) {
            $this->apiReturn(400, [], '产品未通过审核或无权限购买');
        }

        $data = [];
        $result = (new OrderBook())->handleMoveInfo($ticketData,$ticketId);
        if ($result) {
            $data['type']                    = $result['type'];
            $data['more_credential_content'] = $result['more_credential_content'] ?? [];
            $data['son_ticket']              = $result['son_ticket'] ?? [];
        }
        $this->apiReturn(200, $data, '成功');
    }

    /**
     * 获取在售产品景区列表
     * <AUTHOR>
     * @date   2021-03-24
     */
    public function queryLandDetailList()
    {
        $landType = I('post.land_type', "A", 'strval,trim');
        $type     = I('post.type', 1, 'intval');//0 全部 1自供应 2转分销
        $page     = I('post.page', 1, 'intval');
        $size     = I('post.size', 10, 'intval');
        $landName = I('post.land_name', "", 'strval,trim');
        $ProListBus = new EvoluteListQuery();
        if (!in_array($type,[0,1,2])) {
            $this->apiReturn(203, [], '参数错误');
        }
        $landType = explode(',',$landType);
        $result     = $ProListBus->queryLandDetailList($this->_loginMember['sid'], $landName, $type, $landType,$page,$size);
        $this->apiReturn(200, $result, '成功');
    }

    /**
     * 强制核销 -- 走快速验证接口
     * Create by zhangyangzhen
     * Date: 2019/6/5
     * Time: 14:23
     *
     * @param string ordernum 订单号
     */
    public function check()
    {
        $ordernum = I('post.ordernum', '', 'strval,trim');
        $date     = date('Y-m-d H:i:s');

        $orderTools = new OrderTools();
        $orderInfo  = $orderTools->getOrderList([$ordernum], 'ordernum, aid, pay_status, status, lid, ordermode');
        $orderInfo  = $orderInfo[0];

        //判断订单支付状态
        if ($orderInfo['pay_status'] == 2) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '未支付订单不能核销');
        }

        //判断订单状态
        if (!in_array($orderInfo['status'], $this->_checkStatus)) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '该订单状态不能核销');
        }

        $landId    = $orderInfo['lid'];
        $landModel = new Land();
        $landInfo  = $landModel->getLandInfo($landId, false, 'apply_did, salerid, p_type');
        $ptype     = $landInfo['p_type'];

        //用户权限判断
        //在特殊团队预定订单列表员工可以直接验证
        if ($this->_loginMember['dtype'] == 6 && $orderInfo['ordermode'] != \Business\ReservationSpecialTeam\TeamOrder::RESERVATION_SPECIAL_CHANNEL) {
            if (empty($this->_loginMember['qx'])) {
                $this->apiReturn(self::CODE_AUTH_ERROR, [], '员工账号未开通权限');
            } else {
                $staffAuthArr = explode(',', $this->_loginMember['qx']);
                if (!in_array('special_order', $staffAuthArr)) {
                    $this->apiReturn(self::CODE_AUTH_ERROR, [], '员工账号未开通权限');
                }
            }
        } else {
            if ($landInfo['apply_did'] != $this->_loginMember['sid'] && !$this->isSuper()) {
                $this->apiReturn(self::CODE_AUTH_ERROR, [], '非产品供应商无权核销');
            }
        }

        $orderModel = new OrderHandler();

        //演出捆绑的不同步验证子票或主票
        if ($ptype == 'H') {
            $result = $orderModel->CheckOrderSimply($ordernum, $this->_loginMember['memberID'], null, '强制核销',
                self::__FORCE_CHECK_SOURCE__, $date, 0, 0, false, '', false);
        } else {
            $result = $orderModel->CheckOrderSimply($ordernum, $this->_loginMember['memberID'], null, '强制核销',
                self::__FORCE_CHECK_SOURCE__, $date, 0, 0, false, '', true);
        }

        $content = "订单号：$ordernum|操作员ID：$this->_loginMember['memberID']|订单信息：" . json_encode($orderInfo) . '|核销结果：' . json_encode($result);
        pft_log(self::__LOG_PATH__, $content);

        if ($result === true) {
            $this->apiReturn(self::CODE_SUCCESS, [], '核销成功');
        } else {
            $this->apiReturn($result['code'], [], $result['msg']);
        }
    }


    /**
     * 获取当前用户特殊团队预约常用联系人
     * <AUTHOR>
     * @date 2021/8/12
     */
    public function getContactsList()
    {
        $sid  = $this->_loginMember['sid'];
        $page = I('post.page', 1, 'intval');
        $size = I('post.size', 30, 'intval');

        $result = (new \Business\ReservationSpecialTeam\Member())->getContactsList($sid, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据id删除特殊团队预约常用联系人
     * <AUTHOR>
     * @date 2021/8/12
     */
    public function delContacts()
    {
        $sid = $this->_loginMember['sid'];
        $id  = I('post.id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(203, '', '参数错误');
        }
        $result = (new \Business\ReservationSpecialTeam\Member())->delContacts($sid, $id);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 创建特殊团队预约常用联系人
     * <AUTHOR>
     * @date  2021-03-22
     * @throws
     */
    public function addContacts()
    {
        $sid      = $this->_loginMember['sid'];
        $opid     = $this->_loginMember['memberID'];
        $name     = I('post.name', '', 'strval,trim');
        $tel      = I('post.tel', '', 'strval,trim');
        $unitname = I('post.unitname', '', 'strval,trim');
        $level    = I('post.level', 0, 'intval');
        if (!$name || !$tel || !$unitname) {
            $this->apiReturn(203, '', '参数错误');
        }
        $result = (new \Business\ReservationSpecialTeam\Member())->addContacts($sid, $opid, $name, $tel, $unitname,
            $level);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}