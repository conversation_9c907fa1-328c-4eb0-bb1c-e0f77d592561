<?php
/**
 * 数据统计
 * <AUTHOR>
 * @time   2017-07-20
 */

namespace Controller\MicroPlat;

use Business\Authority\AuthContext;
use Business\Authority\AuthLogic as AuthLogicBiz;
use Business\Authority\DataAuthLogic;
use Business\Chart\WxChart;
use Business\Report\ReportBase as ReportBaseBiz;
use Business\Report\ReportSummary as ReportSummaryBiz;
use Controller\MicroPlat\Common as Common;
use Model\Member\MemberRelationship;
use Model\Product\Land;
use Model\Product\Ticket;
use Library\Cache\Cache;

class Statistics extends Common
{
    public function __construct()
    {
        parent::__construct();

    }
    private function _authWxChart()
    {
        $moduleBuz = new \Business\AppCenter\Module();
        $isOpen = $moduleBuz->checkUserIsCanUseApp($this->_loginMember['sid'], 'wx_chart_analyze', false);
        if (!$isOpen) {
            $this->apiReturn(400, [], '无权限');
        }
        if ($this->_loginMember['dtype'] == 6) {
            $isOpen = (new AuthLogicBiz())->hasAuth($this->_loginMember['sid'], $this->_loginMember['memberID'], 'wx_chart_analyze');
            if (!$isOpen) {
                $this->apiReturn(400, [], '无权限');
            }
        }
    }

    /**
     * 移动端销售报表应用开通验证
     * <AUTHOR>
     * @date   2024/06/24
     *
     */
    private function _authMobileSalesReport()
    {
        $moduleBuz = new \Business\AppCenter\Module();
        $isOpen = $moduleBuz->checkUserIsCanUseApp($this->_loginMember['sid'], 'mobile_sales_report', false);
        if (!$isOpen) {
            $this->apiReturn(400, [], '无权限');
        }
    }

    /**
     * 产品排行
     * <AUTHOR>
     * @date   2017-01-13
     *
     * @params date   $begin_time       开始时间  Y-m-d
     * @params date   $end_time         结束时间  Y-m-d
     */
    public function productRanking()
    {
        $this->_authWxChart();
        $sid = $this->_loginMember['sid'];
        //集团账号数据判断
        $sdtype = $this->_loginMember['sdtype'];
        $beginTime = I('post.begin_time', '', 'strval,trim');
        $endTime   = I('post.end_time', '', 'strval,trim');

        $useRank = (new WxChart())->productRanking($sid, $sdtype, $beginTime, $endTime);

        $this->apiReturn($useRank['code'], $useRank['data'], $useRank['msg']);
    }
    /***
     * 渠道排行
     * @author: xwh
     * @date: 2021/06/21
     * @param $sid
     * @param $dtype
     * @param string $beginTime
     * @param string $endTime
     * @return bool | array
     */
    public function channelsRanking()
    {
        $this->_authWxChart();
        $sid = $this->_loginMember['sid'];
        //集团账号数据判断
        $sdtype = $this->_loginMember['sdtype'];
        $beginTime = I('post.begin_time', '', 'strval,trim');
        $endTime   = I('post.end_time', '', 'strval,trim');

        $useRank = (new WxChart())->channelsRanking($sid, $sdtype, $beginTime, $endTime);

        $this->apiReturn($useRank['code'], $useRank['data'],$useRank['msg']);
    }


    /***
     * 按日期获取预约数据
     * @author: xwh
     * @date: 2021/06/21
     * @param string $beginTime
     * @param string $endTime
     * @return bool | array
     */
    public function getReportChartdReserveData()
    {
        $this->_authWxChart();
        $sid = $this->_loginMember['sid'];
        //集团账号数据判断
        $beginTime = I('post.begin_time', '', 'strval,trim');
        $endTime   = I('post.end_time', '', 'strval,trim');
        $useRank = (new WxChart())->getReportChartdReserveData($sid, $beginTime, $endTime);

        $this->apiReturn($useRank['code'], $useRank['data'],$useRank['msg']);
    }

    /***
     * 按日期获取销量和金额 （运营数据 （实售+验证+取消汇总））
     * @author: xwh
     * @date: 2021/06/21
     * @param string $beginTime
     * @param string $endTime
     * @return bool | array
     */
    public function getReportChartdData()
    {
        $this->_authWxChart();
        $sid = $this->_loginMember['sid'];
        //集团账号数据判断
        $beginTime = I('post.begin_time', '', 'strval,trim');
        $endTime   = I('post.end_time', '', 'strval,trim');

        $useRank = (new WxChart())->getReportChartdData($sid, $beginTime, $endTime);

        $this->apiReturn($useRank['code'], $useRank['data'],$useRank['msg']);
    }


    /***
     * 查询某个时间段内的票销量数据
     * @author: xwh
     * @date: 2021/06/21
     * @return array
     */
    public function getReportSalesVolume()
    {
        $sid       = $this->_loginMember['sid'];
        $beginTime = I('post.begin_time', '', 'strval,trim');
        $endTime   = I('post.end_time', '', 'strval,trim');
        $page      = I('post.page', 1, 'intval');
        $size      = I('post.size', 10, 'intval');
        //预订还是验证 可选order_two、checked_two
        $type   = I('post.type', 'order_two', 'strval,trim');
        //可选tid、lid
        $group  = I('post.group', 'tid', 'strval,trim');

        $result = (new \Business\Statistics\StatisticsSurvey())->getReportSalesVolume($beginTime, $endTime, $sid, $page,
            $size, $group, false, $type);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取指定周期内，总金额，总票数数据
     * <AUTHOR>
     * @date 2021/12/7
     *
     */
    public function getReportSalesVolumeSummary()
    {
        $sid       = $this->_loginMember['sid'];
        $beginTime = I('post.begin_time', '', 'strval,trim');
        $endTime   = I('post.end_time', '', 'strval,trim');
        //预订还是验证 可选order_two、checked_two
        $type   = I('post.type', 'order_two', 'strval,trim');

        $result = (new \Business\Statistics\StatisticsSurvey())->getReportSalesVolumeSummary($beginTime, $endTime, $sid, false, $type, [], 0, 0, 0);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取获取报表查询需要lid
     * <AUTHOR>
     * @date   2024/06/24
     *
     */
    public function getTemplateByNeedLid()
    {
        $page = I('post.page', 1, 'intval');
        //默认展示前100条
        $size    = I('post.size', 100, 'intval');
        $keyWord = I('post.key_word', '', 'strval');

        $fid = $this->_loginMember['sid'];

        $data = [];

        try {
            //数据权限过滤
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();

            $condition = $dataAuthLimit->transInOrNotCondition();
            // 全禁的情况下不需要调用接口
            if ($condition === false) {
                $this->apiReturn(self::CODE_SUCCESS);
            }
            $javaApi = new \Business\JavaApi\Product\EvoluteListQuery();
            $result  = $javaApi->queryEvoluteLandByFid($fid, $keyWord, $page, $size, ['Q'], [], $condition);

            if (isset($result['list'])) {
                foreach ($result['list'] as $item) {
                    $data[] = [
                        'id'   => $item['id'],
                        'name' => $item['title'],
                    ];
                }
            }
        } catch (\Exception $e) {
            pft_log('/micro_plat/Statistics/error', json_encode([
                'params' => I('post.'),
                'file'   => $e->getFile(),
                'line'   => $e->getLine(),
                'error'  => $e->getMessage(),
            ], JSON_UNESCAPED_UNICODE));
        }

        $this->apiReturn(200, $data);
    }

    /**
     * 微平台销售报表
     * <AUTHOR>
     * @date   2024/06/25
     *
     */
    public function salesReportList()
    {
        $this->_authMobileSalesReport();
        $code     = 200;
        $response = [];
        $msg      = '';
        try {
            //开始时间 2024-06-24
            $beginDate = I('post.begin_date', '', 'strval,trim');
            //结束时间 2024-06-24
            $endDate = I('post.end_date', '', 'strval,trim');
            //景区ID
            $lid = I('post.lid', 0, 'intval');
            //页码
            $pageNum = I('post.page_num', 1, 'intval');
            //页数
            $pageSize = I('post.page_size', 500, 'intval');
            //是否单屏，分页时，需要传0，不分页时，需要传1
            $isSingleScreen = I('post.is_single_screen', 1, 'intval');
            //商户id
            $fid = $this->_loginMember['sid'];

            //日期格式校验
            if (!chk_date($beginDate) || !chk_date($endDate)) {
                throw new \Exception("日期格式错误");
            }

            //商户ID校验
            if (!$fid) {
                throw new \Exception('商户ID不能为空');
            }

            // 查询条件
            $filter = [];

            $extFilter = [
                'is_single_screen' => $isSingleScreen,
            ];

            $reportBaseBiz = new ReportBaseBiz();

            //数据权限的校验, 只有个人数据权限的只能看自己的数据, 透传操作人ID
            $operateId = $reportBaseBiz->handleSearchCondition([], $this->_loginMember, [], [])[5] ?? '';
            $filter['operate_id'] = $operateId;

            $lid = empty($lid) ? [] : [$lid];

            //数据权限过滤
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
            $dataAuthCondition = $dataAuthLimit->transInOrNotCondition(['lidList' => $lid]);
            if ($dataAuthCondition !== false) {
                if (isset($dataAuthCondition['lidList'])) {
                    $filter['lid'] = $dataAuthCondition['lidList'];
                }

                // 排除lid
                $filter['not_in_arr'] = [];
                if (isset($dataAuthCondition['notLidList'])) {
                    $filter['not_in_arr']['not_lid'] = $dataAuthCondition['notLidList'];
                }

                $reportSummaryBiz = new ReportSummaryBiz();

                $res = $reportSummaryBiz->microPlatOrderDailyReport($beginDate, $endDate, $fid, $filter, $extFilter,
                    $pageNum, $pageSize);

                if ($res['code'] != 200) {
                    throw new \Exception($res['msg'] ?? '查询失败', $res['code'] ?? 400);
                }

                // 返回数据
                $response = $res['data'];
            }

        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
            pft_log('/micro_plat/Statistics/error', json_encode([
                'params' => I('post.'),
                'file'   => $e->getFile(),
                'line'   => $e->getLine(),
                'error'  => $e->getMessage(),
            ], JSON_UNESCAPED_UNICODE));
        }

        $this->apiReturn($code, $response, $msg);
    }
}