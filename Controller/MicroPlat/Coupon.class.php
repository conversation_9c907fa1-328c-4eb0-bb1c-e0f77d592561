<?php
/**
 * 优惠券相关
 * Created by PhpStorm.
 * User: leaf
 * Date: 2018/5/9
 * Time: 16:03
 */
namespace Controller\MicroPlat;

use Business\Product\Coupon as CouponBiz;

class Coupon extends Common
{

    /**
     * 转赠功能
     * author  leafzl
     * Date: 2018-5-10
     */

    public function giveCoupon()
    {
        $mobile = I('mobile');
        $aid = I('aid');
        $fromFid = I('fid');
        $coupon_id = I('coupon_id');
        $num = I('num');
        $couponBiz = new CouponBiz();
        $res = $couponBiz->giveCoupon($mobile, $aid, $fromFid, $coupon_id,$num);

        if ($res['code'] == 200) {
            $this->apiReturn(200, [], $res['msg']);
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);

        }
    }
}