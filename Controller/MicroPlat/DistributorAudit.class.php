<?php
/**
 * 合作关系审核
 *
 * <AUTHOR>  Li
 * @time   2021-04-27
 */

namespace Controller\MicroPlat;

class DistributorAudit extends Common
{
    private $_sid      = null;
    private $_memberId = null;

    public function __construct()
    {
        parent::__construct();

        $this->_sid      = $this->_loginMember['sid'];
        $this->_memberId = $this->_loginMember['memberID'];
        if (!$this->_sid) {
            $this->returnAutoLogin('请先登录');
        }
        //如果是员工判断员工账号当前状态
        $this->checkStaffStatus();
    }

    /**
     * 获取合作关系审核列表
     * <AUTHOR> Li
     * @date  2021-04-27
     */
    public function findMemberRelationshipApply()
    {
        $type       = I('post.type', 0, 'intval'); //搜索类型  1 企业名称 2 账号 3 手机号
        $identify   = I('post.identify', '', 'strval');
        $inviteType = I('post.invite_type', 0, 'intval'); //邀请类型;1 发送,2 接收
        $applyType  = I('post.apply_type', 0, 'intval'); //申请类型;1 查询添加,2 邀请添加,3 创建添加
        $client     = I('post.client', 0, 'intval'); //操作端: 1 微平台小程序,2 PC,3 微平台H5
        $stat       = I('post.status', 0, 'intval'); //审核状态: 1 待审核,2 已同意,3 已拒绝,-1 已失效
        $sid        = I('post.sid', 0, 'intval'); //搜索供应商id
        $fid        = I('post.fid', 0, 'intval'); //搜索分销商id
        $page       = I('post.page', 1, 'intval'); //当前页数
        $size       = I('post.size', 10, 'intval'); //每页条数

        $comName        = '';
        $cname          = '';
        $ctel           = '';
        $searchMemberId = 0;

        if ($identify) {
            $memberBiz = new \Business\Member\Member();
            switch ($type) {
                case 1:
                    //通过企业名称查询用户id
                    $comName = $identify;
                    break;
                case 2:
                    //通过企业账号查询用户id
                    $memberInfo = $memberBiz->getMemberInfoByMulti([$identify], 'account');
                    if (!$memberInfo) {
                        $this->apiReturn(204, [], '暂无数据');
                    }
                    $searchMemberId = $memberInfo[0]['id'];
                    break;
                case 3:
                    //通过手机号查询用户id
                    $memberInfo = $memberBiz->getMemberInfoByMulti([$identify], 'mobile');
                    if (!$memberInfo) {
                        $this->apiReturn(204, [], '暂无数据');
                    }
                    $searchMemberId = $memberInfo[0]['id'];
                    break;
            }
        }

        if ($client) {
            switch ($client) {
                case 1:
                    $client = '微平台小程序';
                    break;
                case 2:
                    $client = 'PC';
                    break;
                case 3:
                    $client = '微平台H5';
                    break;
                case 4:
                    $client = 'APP';
                    break;
            }
        }

        $minCreateTime = date('Y-m-d 00:00:00', strtotime('-30 day'));
        $maxCreateTime = date('Y-m-d 23:59:59');

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->findMemberRelationshipApply($sid, $fid, $this->_sid, $comName, $cname, $ctel,
            $inviteType, $applyType, $client, $stat, 'id', $page, $size, true, $searchMemberId, $minCreateTime, $maxCreateTime);

        if (isset($result['code']) && $result['code'] == 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(204, [], $result['msg']);
    }

    /**
     * 同意或拒绝 合作关系
     * <AUTHOR> Li
     * @date  2021-04-27
     */
    public function checkCreateMemberRelation()
    {
        $auditId = I('post.audit_id'); // 审核i
        $status  = I('post.status', 0, 'intval');   // 审核状态 1 拒绝 2通过

        if (empty($auditId) || empty($status)) {
            $this->apiReturn(400, [], '缺少审核信息');
        }

        if ($status == 2) {
            $pass = true;
        } else {
            $pass = false;
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->checkCreateMemberRelationBatch([$auditId], $this->_sid, $pass);

        if (isset($result['code']) && $result['code'] == 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], $result['msg']);
    }

    /**
     * 再次发送邀请
     * <AUTHOR> Li
     * @date   2021-04-27
     */
    public function reSendInviteSms()
    {
        $id = I('post.id');
        if (!$id) {
            $this->apiReturn(204, '', '参数错误');
        }

        $sDname = $this->_loginMember['sdname'] ?? (new \Business\Member\Member())->getInfo($this->_sid)['dname'];

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->reSendInviteSms($id, $this->_sid, $sDname, $this->_memberId, '微平台H5');
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 通过审核id 获取审核详情
     * <AUTHOR> Li
     * @date   2021-04-27
     *
     * @return array
     */
    public function getDistributionCheck()
    {
        $id = I('post.id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数有误');
        }

        $memberRelationBiz = new \Business\Member\DistributorAudit();
        $result            = $memberRelationBiz->getDistributionCheck($id, $this->_sid);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 失效/激活建立组织的供销关系申请
     * <AUTHOR> Li
     * @date  2021-04-27
     */
    public function invalidOrActiveApply()
    {
        $auditId = I('post.audit_id'); // 审核id
        $state   = I('post.status', 0, 'intval');   // 审核状态 1 失效 2 待审核

        if (!$auditId || !$state) {
            $this->apiReturn(400, '', '参数不能为空');
        }

        $status = 0;
        if (in_array($state, [1, 2])) {
            if ($state == 1) {
                $status = -1;
            } else {
                $status = 1;
            }
        } else {
            $this->apiReturn(400, [], '状态码错误');
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->invalidOrActiveApply($auditId, $status, $this->_memberId);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], $result['msg']);
    }

    /**
     * 设置被添加成为分销商审核状态
     * <AUTHOR>  Li
     * @date   2021-05-07
     *
     * @param  $status int 审核状态值：0=不要，1=要
     */
    public function setAuditStatus()
    {
        $dtype    = $this->_loginMember['dtype'];      //登录账号类型
        $memberID = $this->_loginMember['memberID'];  //会员id
        if (!in_array($dtype, [0, 1])) {          //只能供应商 和分销商
            $this->apiReturn(401, '', '没有权限');
        }

        $status = I('status'); //否需要审核：0=不要，1=要
        if ($status === "") {
            $this->apiReturn(400, '', '参数不能为空');
        }

        if (!in_array($status, [0, 1])) {
            $this->apiReturn(400, '', '参数不合法');
        }

        $data   = [
            'distribution_check' => $status,
        ];

        $memberBiz = new \Business\Member\Member();
        $res       = $memberBiz->updateMemberExtInfo($memberID, $data);
        if ($res) {
            $this->apiReturn(200, ['status' => $status], '保存成功');
        } else {
            $this->apiReturn(500, ['status' => $status], '保存失败');
        }
    }
}
