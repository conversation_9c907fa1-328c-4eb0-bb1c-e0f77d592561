<?php
/**
 * 微平台交易记录
 * Created by PhpStorm.
 * User: leafzl
 * Date: 2018/3/10
 * Time: 10:56
 */

namespace Controller\MicroPlat;

use Library\Controller;
use Business\MicroPlat\TradeRecord as bTradeRecord;

class TradeRecord extends Controller
{
    private $memberId;
    private $trade;

    public function __construct()
    {
        C(include HTML_DIR . '/Service/Conf/trade_record.conf.php');
        $this->memberId = $this->isLogin();
        $this->trade = new bTradeRecord();

    }
}