<?php
/**
 * 托管开票-相关接口
 */

namespace Controller\MicroPlat;

use Business\Authority\AuthContext;
use Business\ElectronicInvoices\AgentInvoiceApi;
use Controller\MicroPlat\Common as Common;
use Business\ElectronicInvoices\InvoiceApi;
use Library\Cache\Cache as Cache;
use Library\Container;

class AgentInvoice extends Common
{
    protected $sid;

    public function __construct()
    {
        parent::__construct();
        if ($this->isSanKeLogin()) {
            $this->apiReturn(400, '功能不可用');
        }
        $this->sid = $this->getMemberId();
        if (empty($this->sid)) {
            $this->returnAutoLogin('请先登录');
        }
    }

    /**
     * 获取可开科目列表
     *
     */
    public function subjectQuery()
    {
        $sid = I('post.sid', 0);
        if (!$sid) {
            $this->apiReturn(204, [], 'sid不能为空');
        }
        $pageNum  = I('post.page_num', 1);
        $pageSize = I('post.page_size', 15);

        $res = (new AgentInvoiceApi())->getSubjectList($sid, $pageNum, $pageSize, '','');

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 查询开票额度
     */
    public function getInvoiceLimit()
    {
        $sid  = I('sid', 0, 'intval');  //供应商

        $res = (new AgentInvoiceApi())->getInvoiceLimit($sid, $this->sid);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取销售方翻页列表（存在交易记录的供应商）
     * @return void
     */
    public function getSupplierOpenRecordsPage() {
        $page = I('page', 1, 'intval');
        $pageSize = I('page_size', 500, 'intval');
        $options = [];
        $options['sid'] = I('sid', 0, 'intval');
        $options['tariffNumber'] = I('tariff_number', '', 'strval');
        //判断是否开启了托管开票
        $agentInvoiceBz = Container::pull(\Business\ElectronicInvoices\AgentInvoice::class);
        $pageData = $agentInvoiceBz->getMySupplierOpenRecordsPageWithMemberInfo($this->sid, $options, $page, $pageSize);
        $this->apiReturn(self::CODE_SUCCESS, $pageData);
    }

    /**
     * 获取销售方列表（存在交易记录的供应商）
     * @return void
     */
    public function getSupplierOpenRecordsList() {
        //判断是否开启了托管开票
        $agentInvoiceBz = Container::pull(\Business\ElectronicInvoices\AgentInvoice::class);
        $pageData = $agentInvoiceBz->getMySupplierOpenRecordsList($this->sid);
        $this->apiReturn(self::CODE_SUCCESS, $pageData);
    }

    /**
     * 获取供应商列表（存在交易记录的供应商）
     * @return void
     */
    public function getSupplierList() {
        //判断是否开启了托管开票
        $agentInvoiceBz = Container::pull(\Business\ElectronicInvoices\AgentInvoice::class);
        $pageData = $agentInvoiceBz->getMySupplierList($this->sid);
        $this->apiReturn(self::CODE_SUCCESS, $pageData);
    }
    
    /**
     * 获取开票地址
     * @return void
     */
    public function getOpenInvoiceUrl() {
        $sid          = I('sid', 0, 'intval'); //开票供应商ID
        $billingPartyId = I('billing_party_id', 0, 'intval'); //销售方记录id
        $isNeedCerifi = I('is_need_cerifi', 0, 'intval'); //是否需要校验开票操作人资质认证
        if (empty($sid) || !$billingPartyId) {
            $this->apiReturn(201, [], '缺少必要参数');
        }
        $ordernum = strtoupper(uniqid('') . mt_rand(1000, 9000));
        $invoiceApi = Container::pull(InvoiceApi::class);
        $openInvoiceUrl = $invoiceApi->getOpenInvoiceUrl($sid, 1, $ordernum, false, 0, 'microplat', $this->sid, $isNeedCerifi, 4, ['billingPartyId' => $billingPartyId]);
        if ($openInvoiceUrl['code'] != 200) {
            $this->apiReturn($openInvoiceUrl['code'], [], $openInvoiceUrl['msg']);
        }
        $this->apiReturn(200, ['openInvoiceUrl' => $openInvoiceUrl['data']], '获取成功');
    }

    /**
     * 获取开票配置
     * @return void
     */
    public function getInvoiceConfig() {
        $ordernum  = I('post.ordernum', '');
        $sid       = I('post.sid', '');
        $fid       = I('post.fid', '');
        $billingPartyId = I('bpi', '');
        $isN       = I('post.isN', 0);
        $source = I('post.source', '');

        $sid       = \Library\MessageNotify\OrderNotify::url_sms_decode($sid)[0];
        $fid       = \Library\MessageNotify\OrderNotify::url_sms_decode($fid)[0] ?: 0;
        $billingPartyId = \Library\MessageNotify\OrderNotify::url_sms_decode($billingPartyId)[0] ?: 0;
        $source = $source ? base64_decode($source) : '';
        
        if (!$sid || !$fid || !$ordernum) {
            $this->apiReturn(self::CODE_PARAM_ERROR, '参数错误');
        }
        $invoiceApi = Container::pull(InvoiceApi::class);
        //判断订单是否已有开票记录
        $checkRes = $invoiceApi->getInvoiceRecordByOrderNum($ordernum, $sid, $fid);
        if ($checkRes['code'] == 200 && $checkRes['data']) {
            $this->apiReturn(self::CODE_PARAM_ERROR, '该订单已存在开票记录，请返回供应商页面后重新发起开票');
        }
        //判断是否开启了托管开票
        $agentInvoiceApi = Container::pull(AgentInvoiceApi::class);
        $config = $agentInvoiceApi->getAgentRuleConfig($sid);
        if ($config['code'] != 200) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], '获取供应商托管开票配置失败');
        }
        $config = $config['data'];
        if ($config['status'] != 1) {
            $this->apiReturn(self::CODE_SUCCESS, [], '供应商未开启托管开票');
        }
        //获取开票配置信息 orderType: 4=托管开票
        $invoiceDetail = $invoiceApi->getBasicInvoiceDetailByMixed($sid, $this->sid, $billingPartyId, $isN, 4, $source);
        if (!$invoiceDetail) {
            $this->apiReturn(self::CODE_SUCCESS, [], '获取开票信息失败');
        }
        //获取供应商开票基础配置
        $rules = $config['rules'] ?? [];
        //判断是否开通托管开票开放功能
        $result = Container::pull(AuthContext::class)->checkAccessByKey($sid, ['agent_invoice_tax_offset']);
        $isOpen = $result['data']['agent_invoice_tax_offset'] ?? false;
        $taxOffset = !$isOpen ? 0 : ($rules['taxOffset'] ?? 0);
        //获取可开项目
        $subjectList = $agentInvoiceApi->getSubjectList($sid, 1, 500);
        $subjectList = $subjectList['code'] == 200 ? $subjectList['data'] : [];
        $subjectList = $subjectList['list'] ?? [];
        $result = array_merge($invoiceDetail, [
            'subjectList' => array_map(function ($item) {
                return [
                    'id' => $item['id'],
                    'name' => $item['name'],
                    'spbm' => $item['spbm'],
                    'tax_rate' => $item['tax_rate']
                ];
            }, $subjectList),
            'taxOffset' => $taxOffset,
        ]);
        $this->apiReturn(self::CODE_SUCCESS, $result);
    }

    /**
     * 获取开票记录
     * @throws
     */
    public function getInvoiceRecord()
    {
        $beginTime = I('begin_time', 0, 'intval');
        $endTime = I('end_time', 0, 'intval');
        $sid = I('sid', 0, 'intval');
        $tariffNumber = I('tariff_number', '', 'strval');
        $status = I('status', 0, 'intval');
        $page = I('page', 1, 'intval');
        $pageSize = I('page_size', 10, 'intval');

        $vacationMode = (new \Business\PftSystem\VacationModeBiz())->judgeForPage(
            \Model\Member\VacationMode::__GLOBAL_IDENTIFIER__,
            $this->_loginMember['saccount']);
        if ($vacationMode === false) {
            $this->apiReturn(403, [], '当前处于假日模式，该功能被限制使用');
        }
        $agentInvoiceBz = Container::pull(\Business\ElectronicInvoices\AgentInvoice::class);
        $result = $agentInvoiceBz->getEnterpriseInvoiceRecords($sid, $beginTime, $endTime, $this->sid, $tariffNumber, '', 
            -1, '', $status, $page, $pageSize);
        $this->apiReturn(self::CODE_SUCCESS, $result);
    }

    /**
     * 航信开发票
     */
    public function writeReceipt()
    {
        $ordernum     = I('post.ordernum', 'strval');
        $tariffNumber = I('post.tariff_number', '', 'strval');
        $postData     = I('post.post_data_arr', []);
        $sid          = I('post.sid');
        $fid          = I('post.fid');
        $source       = I('post.source', '', 'strval');

        if (!$ordernum || !$tariffNumber || !$postData) {
            $this->apiReturn(203, [], '缺少必要参数');
        }
        $invoiceRes = $this->_writeReceipt($ordernum, $sid, $fid, $tariffNumber, $postData, $source);
        if ($invoiceRes['code'] != 200) {
            $this->apiReturn($invoiceRes['code'], $invoiceRes['data'], $invoiceRes['msg']);
        }
        $this->apiReturn(200, $invoiceRes['data'], '开票提交成功');
    }
    
    protected function _writeReceipt($ordernum, $sid, $fid, $tariffNumber, $postData, $source)
    {
        $cache  = Cache::getInstance('redis');
        try {
            //防抖处理
            $locKey = 'electronic_invoice:writeReceipt:lock:'.$ordernum .':'.$sid;
            $lockRet = $cache->lock($locKey, 1, 30);
            if (!$lockRet) {
                $orderNum = \Library\MessageNotify\OrderNotify::url_sms_decode($ordernum)[0] ?: '';
                return $this->returnData(203, "订单[{$orderNum}]已提交，请勿重复操作");
            }
            $invoiceApi = Container::pull(InvoiceApi::class);
            $isMerge = \Library\MessageNotify\OrderNotify::url_sms(0);
            $orderType = \Library\MessageNotify\OrderNotify::url_sms(4);
            return $invoiceApi->writeReceipt($tariffNumber, $ordernum, $postData, $sid, $fid, '', $isMerge, $orderType, $source);
        } finally {
            //释放锁
            $locKey && $cache->lock_rm($locKey);
        }
    }
}
