<?php

/**
 * 微平台公司项目功能
 * <AUTHOR>
 * @date   2019/10/24
 */

namespace Controller\MicroPlat;

use Model\Authority\RoleMember;
use Model\Product\Area;
use Process\Resource\AreaProcess;

class CompanyProject extends Common
{

    private $area;
    private $overSea;

    public function __construct()
    {
        $isOverSea     = 0;
        $this->overSea = $isOverSea;
        $this->area    = new Area($this->overSea);
        parent::__construct();
    }

    /**
     * 获取合作供应商列表
     * <AUTHOR>
     * @date   2019/10/24
     *
     * @param $orderBy cooperation_time_desc     合作时间由近到远
     *                 cooperation_time_asc      合作时间由远到近
     *                 thirty_capital_flow_desc  近30日流水交易排序高到低
     *                 thirty_capital_flow_asc   近30日流水交易排序低到高
     *                 total_capital_flow_desc   总流水交易排序低到高
     *                 total_capital_flow_asc    总流水交易排序低到高
     *                 on_sale_ticket_desc          在售门票数量有多到少
     *                 on_sale_ticket_asc           在售门票数量有少到多
     *                 order_number_desc         订单数量多到少
     *                 order_number_asc          订单数量少到多
     *
     * @param $companyType   企业类型      0：酒店、1：景区、2：旅行社、3：加盟门店、4：电商、5：淘宝/天猫、6、团购网、7、个人，8、其他
     * @param $cooperationMode  合作模式   0=无,1=套餐,2=票务,3=订单
     */
    public function getPartnerList()
    {
        $this->isLogin();
        $orderBy         = I('post.orderBy', 'cooperation_time_desc', 'strval');         //排序规则
        $provinceIds     = I('post.provinceId', '', 'strval');                            //省份
        $cityIds         = I('post.cityId', '', 'strval');                                //城市
        $companyType     = I('post.companyType', '', 'strval');                           //企业类型
        $cooperationMode = I('post.cooperationMode', '', 'strval');                       //合作模式
        $keyWord         = I('post.keyWord', '', 'strval');                              //搜索关键字
        $pageNum         = I('post.pageNum', 1, 'intval');                              //页数
        $pageSize        = I('post.pageSize', 10, 'intval');                              //每页条数
        $memberInfo      = $this->_loginMember;
        $memberId        = $memberInfo['memberID'];
        //存在搜索关键字时，默认按照合作时间由近到远
        switch ($orderBy) {
            case 'cooperation_time_desc':
                $order = 'user_first_package_begindate desc';
                break;
            case 'cooperation_time_asc':
                $order = 'user_first_package_begindate asc';
                break;
            case 'thirty_capital_flow_desc':
                $order = 'pay_amount_30d desc';
                break;
            case 'thirty_capital_flow_asc':
                $order = 'pay_amount_30d asc';
                break;
            case 'total_capital_flow_desc':
                $order = 'pay_amount_1y desc';
                break;
            case 'total_capital_flow_asc':
                $order = 'pay_amount_1y asc';
                break;
            case 'on_sale_ticket_desc':
                $order = 'ticket_num_all desc';
                break;
            case 'on_sale_ticket_asc':
                $order = 'ticket_num_all asc';
                break;
            case 'order_number_desc':
                $order = 'order_num_all desc';
                break;
            case 'order_number_asc':
                $order = 'order_num_all asc';
                break;
            default:
                $order = 'user_first_package_begindate desc';
                break;
        }
        $roleMember = new RoleMember();
        //获取当前登录用户的权限信息
        $result = $roleMember->getMemberCompanyJurisdiction($memberId);
        //当没有省份id时，默认为当前用户的管辖全部省份
        if (empty($provinceIds)) {
            $provinceIds = isset($result['province_ids']) ? $result['province_ids'] : '';
        }
        $provinceIdArr      = !empty($provinceIds) ? explode(',', $provinceIds) : '';
        $cityIdArr          = !empty($cityIds) ? explode(',', $cityIds) : '';
        $companyType        = $companyType !== '' ? explode(',', $companyType) : '';
        $cooperationMode    = $cooperationMode !== '' ? explode(',', $cooperationMode) : '';
        $companyProjectMode = new \Model\Mall\CompanyProject();
        $res                = $companyProjectMode->getPartnerList($provinceIdArr, $cityIdArr, $companyType,
            $cooperationMode, $keyWord, $result['is_super'],
            'user_id, user_company_name, customer_province, customer_city, customer_type, user_contract_model', $order,
            $pageNum, $pageSize);

        $this->apiReturn(200, $res, '获取成功');
    }

    /**
     * 获取商户详情
     * <AUTHOR>
     * @date   2019/10/24
     */
    public function getCompanyInfoDetail()
    {
        $this->isLogin();
        $userId = I('post.user_id', 0, 'intval');
        if (empty($userId)) {
            $this->apiReturn(203, [], "参数错误");
        }
        $companyProjectMode = new \Model\Mall\CompanyProject();
        $field              = 'user_id, user_name, user_company_name, user_type_code, user_type, land_type, land_level, customer_mobile, customer_type_code, customer_type, customer_province_code, customer_province, customer_city_code, customer_city, signer_id, signer, servicer_id, servicer, user_contract_model_code, user_contract_model, gathering_self_way, cooperation_projects, onsale_ticket_num_self, onsale_land_num_self, onsale_resource_num_self, user_group, user_first_package_begindate, user_curr_package_name, user_curr_package_begindate, user_curr_package_enddate, user_last_package_name, user_last_package_begindate, user_last_package_enddate, order_num_7d, ticket_num_7d, pay_amount_7d, order_num_30d, ticket_num_30d, pay_amount_30d, order_num_90d, ticket_num_90d, pay_amount_90d, order_num_1y, tiket_num_1y, pay_amount_1y, order_num_all, ticket_num_all, pay_amount_all';
        $res                = $companyProjectMode->getCompanyInfoDetail($userId, $field);

        $this->apiReturn(200, $res, '获取成功');
    }

    /**
     * 获取当前用户的权限省份
     * <AUTHOR>
     * @date   2019/10/24
     */
    public function getProvince()
    {
        $this->isLogin();
        $memberInfo = $this->_loginMember;
        $memberId   = $memberInfo['memberID'];
        $result     = AreaProcess::childerCity($this->area);
        $roleMember = new RoleMember();
        //获取当前登录用户的权限信息
        $res = $roleMember->getMemberCompanyJurisdiction($memberId);
        if ($res['is_super']) {
            $this->apiReturn(200, $result, "获取成功");
        }
        $result        = array_key($result, 'area_id');
        $provinceIdArr = explode(',', $res['province_ids']);
        $provinceInfo  = [];
        foreach ($provinceIdArr as $value) {
            $provinceInfo[] = $result[$value];
        }
        $this->apiReturn(200, $provinceInfo, "获取成功");
    }
}