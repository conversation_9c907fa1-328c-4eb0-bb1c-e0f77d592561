<?php
/**
 * 用户相关操作
 *
 * <AUTHOR>
 * @time   2017-01-10
 */

namespace Controller\MicroPlat;

use Business\AppCenter\Module;
use Business\Authority\AuthContext;
use Business\Authority\AuthLogic as AuthLogicBiz;
use Business\Authority\Base\AuthChannel;
use Business\Finance\AccountMoney;

use Business\JavaApi\Product\EvoluteLimiterService;
use Business\Mall\WechatShop;
use Business\Member\MemberPrivacy as MemberPrivacyBiz;
use Business\Member\MemberRelation as MemberRelationBiz;
use Business\JavaApi\Member\MemberRelationshipRemark;
use Business\Member\PartnerSuppliers as PartnerSuppliersBiz;
use Business\Member\SmallApp;
use Business\MultiDist\Base;
use Business\MultiDist\Member as MultiDistMember;
use Controller\MicroPlat\Common as Common;
use Business\Wechat\Authorization as Authorization;
use Controller\Login as PcLogin;
use Controller\Product\ResourceCenter;
use Controller\Tpl\bargain;
use Library\Cache\Cache as Cache;
use Library\Tools\BusinessCache;
use Library\Tools\Vcode as Vcode;
use Model\AppCenter\ModuleDetail;
use Model\Authority\RoleMember;
use Model\Subdomain\ShopConfig;
use Model\Wechat\WxMember as WxMember;
use Endroid\QrCode\QrCode;
use Business\Finance\AmountLedgerFreeze as AmountLedgerFreezeBiz;
use Business\Authority\AuthMicroPlatLogic as AuthMicroPlatLogicBiz;

class Member extends Common
{
    //短信服务标识
    const __VCODE_IDENTIFY__ = 'micro_plat';

    const __DEFAULT_SY_ACCOUNT  = 100005;    // 三亚账号
    const __DEFAULT_SY_MEMBERID = 55;        // 三亚ID

    // 授权目标页面的标识和页面地址映射
    private $_fromMap = [
        'order'               => 'html/order_pay_b.html',        // 微平台订单支付页面
        'credit'              => 'html/partners-supplier.html',  // 微平台供应商授信预存页面
        'bond'                => 'html/resource_center_pay.html',  // 微平台资源中心保证金充值页
        'resource_center_pay' => 'html/resource_center_pay.html',  // 微平台应用中心购买页
    ];

    public function __construct()
    {
        parent::__construct();
        //如果是员工判断员工账号当前状态   退出登录不判断
        if (!strpos($_SERVER['REQUEST_URI'], 'unBindLogout')) {
            $this->checkStaffStatus();
        }
    }

    /**
     * 票付通公众号的账号类型验证
     * <AUTHOR>
     * @time   2017-01-10
     */
    public function checkMemberType()
    {
        // 获取账户类型
        $dtype = $this->_loginMember['dtype'];
        if (!is_numeric($dtype)) {
            $this->returnAutoLogin('请先登录');
        }

        // 账户类型判断
        if ($this->isSanKeLogin()) {
            // 散客账号
            $this->apiReturn(202, [], '散客');

        } elseif (in_array($dtype, [0, 1])) {
            // 分销商/供应商 - 进入预订列表页
            $this->apiReturn(200, []);

        } elseif ($dtype == 6) {
            // 员工账号
            $this->checkSonAuth();

        } else {
            $this->returnAutoLogin('请先登录');
        }
    }

    /**
     * 登录之前 异步判断是否已登录 或 是否在微信中授权登录
     *
     * <AUTHOR>
     * @time   2017-01-21
     */
    public function beforeLogin()
    {
        // 已登录
        if ($this->_loginMember['memberID']) {
            $this->clearLoginInfo();
        }

        // 微信中 获取授权
        if ($this->inWechatApp()) {
            $url = $this->getMicroPlatHost() . 'r/MicroPlat_Member/wxAutoLogin';
            // 参数传递
            if (isset($_SERVER['HTTP_REFERER'])) {
                $query = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_QUERY);
                if ($query) {
                    $url .= '?' . $query;
                }
            }
            // 跳转指定页面
            $this->apiReturn(200, ['url' => $url]);
        }

        $this->apiReturn(201, [], '请先登录或绑定账号');
    }

    /**
     * 自动授权登陆
     * <AUTHOR>
     * @time   2017-03-21
     */
    public function wxAutoLogin()
    {
        $param = $this->_transParams();
        if (I('code')) {
            $params = json_decode(base64_decode(I('state')), true);
            $appid  = $params['appid'];

            $param['redit']  = $params['redit'];
            $param['ctx']    = isset($params['ctx']) ? $params['ctx'] : 0;
            $param['ctype']  = isset($params['ctype']) ? $params['ctype'] : 0;
            $param['ptype']  = isset($params['ptype']) ? $params['ptype'] : 0;
            $param['ptypeS'] = isset($params['ptypeS']) ? $params['ptypeS'] : 0;

            $busSession = new \Business\Member\Session();
            $authBiz    = new Authorization($appid);

            $auth = $authBiz->parseAuthInfo(I('code'));

            if ($auth['code'] != 200) {
                $this->redirectHandLogin($params['supplyAccount'], $param);
            }
            $res = $busSession->loginWx($appid, $auth['data']['openid']);

            if ($res['code'] == 200) {
                // 登录成功，写入session额外信息
                // 验证账号权限
                $this->getLoginInfo();
                $this->_checkMemberType(
                    $this->_loginMember['dtype'],
                    $params['supplyAccount'],
                    $param
                );
            } else {
                // 存储 appid 和 openid
                $openid = $auth['data']['openid'];
                $this->setLoginInfo('openid', $openid);
                $this->setLoginInfo('wechat_appid', $appid);
            }

        } else {
            // 微信中
            if ($this->inWechatApp()) {
                $params['supplyAccount'] = $this->_supplyAccount;
                $params['supplyId']      = $this->_supplyId;
                $params['appid']         = $this->_supplyAppId;

                $param['ptypeS'] = I('product_type', '', 'strval');
                $param['redit']  = I('redit', '', 'strval');

                if ($param) {
                    $params = array_merge($params, $param);
                }

                $callback = MOBILE_DOMAIN . 'api/index.php?' . $_SERVER['QUERY_STRING'];
                try {
                    $busWechat = new Authorization($params['appid']);
                    $busWechat->requestForAuth($callback, $params);
                } catch (\exception $e) {
                    //pass
                }

            }
        }

        $supplyAccount = $params['supplyAccount'] ?: $this->_supplyAccount;

        // 不在微信中 或 自动登录失败 ；跳到手动登录方式
        $this->redirectHandLogin($supplyAccount, $param);
    }

    /**
     * 手动登录
     * <AUTHOR>
     * @time   2017-01-21
     *
     * @param  string  $mobile  用户 [账号|手机号]
     * @param  string  $password  密码
     */
    public function login()
    {
        $mobile   = I('mobile');
        $password = I('password');
        $authCode = I('authCode', '', 'trim');

        if (!$password) {
            $this->apiReturn(204, [], '密码不能为空');
        }

        $memberModel = $this->getMemberModel();
        //用户二期 - 信息获取修改
        $MemberBus = new \Business\Member\Member();

        if (is_numeric($mobile) && mb_strlen($mobile) == 11) {
            // 手机号登录
            if (!\ismobile($mobile)) {
                $this->apiReturn(204, [], '请输入正确的手机号');
            }
            $memberInfo = $MemberBus->getInfoByMobile($mobile);
        } else {
            // 账号登录
            if (!$mobile) {
                $this->apiReturn(204, [], '账号不能为空');
            }
            $memberInfo = $MemberBus->getInfoByAccount($mobile);

        }

        if (!$memberInfo || !$memberInfo['customer_id']) {
            $this->apiReturn(204, [], '账号不存在');
        }

        $customerBiz = new \Business\Member\Customer();
        $errRes      = $customerBiz->queryCustomerErrorByCustomerId($memberInfo['customer_id']);

        // 错误次数检测
        if ($errRes['code'] == 200 && $errRes['data']['derror'] >= 6 && !$this->checkImgCode($authCode)) {
            $this->apiReturn(203, [], '请输入校验码');
        }

        $busSession = new \Business\Member\Session();
        $res        = $busSession->loginCommon($mobile, $password, 'mobile');
        if ($res['code'] != 200) {
            $this->apiReturn(204, [], $res['msg']);
        }

        // 登陆成功后，授权绑定微信号
        $url = ''; // 默认返回''，前端无须跳转

        $this->_bindWechat();

        $this->apiReturn(200, ['url' => $url]);
    }

    /**
     * 微信支付页面的授权
     * <AUTHOR>
     * @time   2017-02-15
     */
    public function wxOrderAuth()
    {
        if (I('code')) {
            $params = json_decode(base64_decode(I('state')), true);
            $appid  = $params['appid'];

            try {
                $authInfo = $this->_getAccessTokenAndOpenId($appid, I('code'));

                if ($authInfo === false) {
                    exit('授权过程发生错误');
                }

                $data['openid']   = $authInfo['openid'];
                $data['h']        = $params['h'];
                $data['ordernum'] = $params['ordernum'];

                $this->setLoginInfo('pft_openid', $authInfo['openid']);

                $url = MOBILE_DOMAIN . 'html/order_pay_b.html?' . http_build_query($data);

                header("Location:$url");
                exit();
            } catch (\Exception $e) {
                exit('授权过程发生错误：' . $e->getMessage());
            }
        } else {

            // 调用微信授权
            $params['supplyAccount'] = $this->_supplyAccount;
            $params['appid']         = PFT_WECHAT_APPID;
            $params['h']             = I('h');
            $params['ordernum']      = I('ordernum');

            if (!$this->inWechatApp()) {
                exit('不在微信中');
            }

            $callback  = MOBILE_DOMAIN . 'api/index.php?' . $_SERVER['QUERY_STRING'];
            $busWechat = new \Business\Wechat\Authorization();
            $busWechat->requestForAuth($callback, $params);
        }
    }

    /**
     * 票付通公众号的微信授权
     * <AUTHOR>
     * @time   2017-08-09
     */
    public function pftWxAuth()
    {
        if (I('code')) {

            $params   = json_decode(base64_decode(I('state')), true);
            $appid    = $params['appid'];
            $authInfo = $this->_getAccessTokenAndOpenId($appid, I('code'));
            if ($authInfo === false) {
                exit('授权过程发生错误');
            }

            // 不同目的页面的参数获取
            $fromId = $params['f'];

            switch ($fromId) {
                case 'order':
                    $data['openid']   = $authInfo['openid'];
                    $data['h']        = $params['h'];
                    $data['ordernum'] = $params['ordernum'];
                    break;

                case 'credit':
                    $data['sid'] = $params['sid'];
                    $data['h']   = $params['h'];
                    break;
                case 'bond':
                    $data['h']  = $params['h'];
                    $data['id'] = $params['id'];
                    $point      = "#/recharge";
                    break;
                case 'resource_center_pay':
                    $data['h']  = $params['h'];
                    $data['id'] = $params['id'];
                    $data['channel'] = $params['rc_spread_channel'] ?? 0; //资源中心开通推广来源标识
                    break;

                default:
                    break;
            }

            // 目标页面解析
            $html = $this->_fromMap[$fromId];
            if (!$html) {
                exit('授权成功，但来源页面标识不存在');
            }

            $this->setLoginInfo('pft_openid', $authInfo['openid']);

            $url = MOBILE_DOMAIN . $html;
            if ($data) {
                $url .= '?' . http_build_query($data);
            }
            if ($point) {
                $url .= $point;
            }

            header("Location:{$url}");
            exit();

        } else {

            // 调用微信授权
            $params['supplyAccount'] = $this->_supplyAccount;
            $params['appid']         = PFT_WECHAT_APPID;

            // 授权后跳转的页面简短标识
            $fromId = I('from', 'order', 'trim');

            // 传递标识
            $params['f'] = $fromId;

            // 各个不同标识需要传递的参数
            switch ($fromId) {
                case 'order':
                    $params['h']        = I('h');
                    $params['ordernum'] = I('ordernum');
                    break;

                case 'credit':
                    $params['sid'] = I('sid');
                    $params['h']   = I('h');
                    break;
                case 'bond':
                    $params['h']  = I('h');
                    $params['id'] = I('id');
                    break;
                case 'resource_center_pay':
                    $params['h']  = I('h');
                    $params['id'] = I('id'); //应用id
                    $params['rc_spread_channel'] = I('rc_spread_channel', 0, 'intval'); //资源中心开通推广来源标识
                    break;

                default:
                    break;
            }

            if (!$this->inWechatApp()) {
                exit('不在微信中');
            }

            // 获取跳转地址
            parse_str($_SERVER['QUERY_STRING'], $tmp);
            $info['c'] = $tmp['c'];
            $info['a'] = $tmp['a'];
            $pathInfo  = http_build_query($info);
            $callback  = MOBILE_DOMAIN . 'api/index.php?' . $pathInfo;
            $busWechat = new \Business\Wechat\Authorization();
            $busWechat->requestForAuth($callback, $params);
        }
    }

    /**
     * 散客解绑后退出登录
     * <AUTHOR>
     * @time   2017-01-10
     */
    public function unBindLogout($return = false)
    {
        //if (!$this->_loginMember) {
        //
        //} elseif ($this->getMemberId() && $this->_loginMember['openid']) {
        //    //解绑
        //    $res = (new \Model\Wechat\WxMember())->unBindWechat(
        //        $this->_loginMember['memberID'],
        //        $this->_supplyId,
        //        $this->_loginMember['openid'] ?: '',
        //        $this->_supplyAppId ?: '',
        //        'microPlat'
        //    );
        //}

        // 退出登录
        $this->clearLoginInfo();

        if ($return) {
            return true;
        }

        //微平台登录切到单点登录
        $backUrl = SSO_DOMAIN . '/mobile/#/logout?redirect=';

        //登入之前地址
        $domain      = str_replace('wx', 'm', MOBILE_DOMAIN);
        $callBackUrl = \Business\Member\MemberWx::__LOGIN_MICRO_PLAT_SSO_CALLBACK_URL__;
        $backUrl     = $backUrl . urlencode($domain . $callBackUrl);

        $this->apiReturn(200, ['link' => $backUrl], '退出成功');
    }

    /**
     * 散客解绑后退出登录
     * <AUTHOR>
     * @time   2017-01-10
     */
    public function unBindLogoutForAllDis($return = false)
    {
        if ($this->_loginMember['openid']) {
            //解绑
            $res = (new \Model\Wechat\WxMember())->unBindWechat(
                $this->_loginMember['memberID'],
                $this->_supplyId,
                $this->_loginMember['openid'] ?: '',
                $this->_supplyAppId ?: '',
                'microPlat'
            );
        }

        // 退出登录
        $this->clearLoginInfo();

        if ($return) {
            return true;
        }

        $this->apiReturn(200, [], '解绑退出成功');
    }

    /**
     * 退出登录
     * <AUTHOR>
     * @time   2017-01-10
     */
    public function logout()
    {
        $this->clearLoginInfo();
        $this->apiReturn(200, [], '退出成功');
    }

    /**
     * 分销商解绑后，以散客身份登录微商城
     * <AUTHOR>
     * @date   2017-08-01
     */
    public function resellerUseSankeAccountLogin()
    {
        if ($this->_loginMember['sid']) {
            $this->unBindLogout(true);  //解绑
        }

        if ($this->inWechatApp()) {
            // 微信端
            $url = str_replace('wx', $this->_supplyAccount, MOBILE_DOMAIN) .
                   'r/Mall_Member/autoLoginByOpenId';

            $this->apiReturn(200, ['url' => $url], '跳转微信授权');

        } else {
            // H5端 - 跳转微商城首页
            $this->apiReturn(201, [], '请换号码登录');
        }
    }

    /**
     * 商户微信公众号入口
     * <AUTHOR>
     * @time   2017-02-13
     *
     * @return 根据不同情况，返回相应URL
     */
    public function friendWx()
    {
        // 兼容旧版公众号产品预订的各种奇葩URL参数过滤
        $params          = I('');
        $params['ptype'] = $params['p_type'];
        unset(
            $params['token'],
            $params['c'],
            $params['a'],
            $params['m'],
            $params['p_type']
        );

        $host = $this->getMicroPlatHost();

        //  需要登录 或 不需要登录[散客] 的情况
        if ($this->_mustLogin && $this->inWechatApp()) {
            // 授权登录
            $url = $host . 'r/MicroPlat_Member/wxAutoLogin';

        } elseif ($this->_mustLogin) {
            // 手动登录
            $url = $host . 'wx/b/login.html';

        } else {
            // [散客]直达产品列表页
            $url = $host . 'wx/b/plist.html';
        }

        // 参数传递
        if ($params) {
            $url .= '?' . http_build_query($params);
        }

        $this->apiReturn(200, ['url' => $url]);
    }

    /**
     * 用户账户余额
     * <AUTHOR>
     * @time   2017-01-13
     */
    public function getMemberCreditMoney()
    {
        $memberId = $this->getMemberId();
        if (!$memberId) {
            $this->apiReturn(201, [], '登录过期');
        }

        $kmoney = $this->getMemberModel('master')->getMoney($memberId, 0);

        if (!is_numeric($kmoney)) {
            $this->apiReturn(201, [], '查询失败');
        }
        $kmoney = floatval($kmoney / 100);

        $this->apiReturn(200, ['kMoney' => $kmoney]);
    }

    /**
     * 用户授信余额
     * <AUTHOR>
     * @time   2017-01-13
     */
    public function getMemberAccountMoney()
    {
        $memberId = $this->getMemberId();
        if (!$memberId) {
            $this->apiReturn(201, [], '登录过期');
        }

        $creditMoney = $this->getMemberModel('master')
                            ->getMoney($memberId, 2, $this->_supplyId);

        if (!is_numeric($creditMoney)) {
            $this->apiReturn(201, [], '查询失败');
        }
        $creditMoney = floatval($creditMoney / 100);

        $this->apiReturn(200, ['cMoney' => $creditMoney]);
    }

    /**
     * 登录中转页数据获取
     * <AUTHOR>
     * @time   2017-01-23
     */
    public function loginChoose()
    {
        $memberId = $this->getMemberId();
        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        $host = $this->_supplyAccount ?: 124078;

        // 账号信息
        $return = [
            'account' => $this->_loginMember['account'],
            'url'     => str_replace('wx', $host, MOBILE_DOMAIN) . 'wx/c/index.html',
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 获取图形验证码
     * <AUTHOR>
     * @time   2017-02-15
     */
    public function getImgCode()
    {
        (new PcLogin)->getCode();
    }

    /**
     * 常用联系人信息操作：保存/删除
     * <AUTHOR>
     * @time   2017-02-15
     *
     * @param  int  $memberId  用户ID
     * @param  string  $ordername  联系人名字
     * @param  string  $ordertel  电话号码
     * @param  string  $idcard  身份证号
     * @param  string  $type  操作类型 删除del,新增add
     *
     * @return ajax返回
     */
    public function updateLinkman()
    {
        $memberId = $this->getMemberId();
        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        $name   = I('ordername', '');
        $tel    = I('ordertel', '');
        $idcard = I('idcard', '');
        $type   = I('type', '');

        // 操作校验
        if (!in_array($type, ['add', 'del'])) {
            $this->apiReturn(201, [], '非法操作');
        }

        // 数据校验
        if (!trim($name)) {
            $this->apiReturn(201, [], '联系人姓名不能为空');
        }

        if (!trim($tel)) {
            $this->apiReturn(201, [], '手机号不能为空');
        }

        // 新联系人信息
        $linkman = [
            'name'   => trim($name),
            'tel'    => trim($tel),
            'idcard' => trim($idcard),
        ];

        $memberBiz  = new \Business\Member\Member();
        $cinfos     = $memberBiz->getMemberExtInfoGetFieldToJava($memberId, 'cinfos');
        $linkmanArr = $this->parseLinkman($cinfos);

        if ($type == 'add') {

            $type = '保存';

            // 是否已存在
            if ($linkmanArr && in_array($linkman, $linkmanArr)) {
                $this->apiReturn(201, [], '此联系人信息已存在');
            }

            // 限制常用联系人只能5个
            if (count($linkmanArr) >= 12) {
                $this->apiReturn(201, [], '常用联系人最多5个');
            }

            if (!$cinfos) {
                $data['cinfos'] = implode(':', $linkman);
            } else {
                $data['cinfos'] = $cinfos . '|' . implode(':', $linkman);
            }

            $res = $memberBiz->updateMemberExtInfo($memberId, $data);

        } else {

            $type = '删除';

            // 是否不存在
            if (!$linkmanArr || !in_array($linkman, $linkmanArr)) {
                $this->apiReturn(201, [], '此联系人信息不存在');
            }

            // 删除
            foreach ($linkmanArr as $key => $val) {
                if ($val['name'] == $name &&
                    $val['tel'] == $tel &&
                    $val['idcard'] == $idcard
                ) {
                    unset($linkmanArr[$key]);
                    break;
                }
            }

            foreach ($linkmanArr as $key => $info) {
                $linkmanArr[$key] = implode(':', $info);
            }

            $res = $memberBiz->updateMemberExtInfo($memberId, $data);
        }

        if (!$res) {
            $this->apiReturn(201, [], '新联系人' . $type . '失败');
        }

        $this->apiReturn(200, [], '新联系人' . $type . '成功');
    }

    /**
     * 获取常用联系人信息
     * <AUTHOR>
     * @time   2017-02-15
     *
     * @param  int  $memberId  用户ID
     *
     * @return ajax返回
     */
    public function getLinkman()
    {
        $memberId = $this->getMemberId();
        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        $info = $this->_getContacts($memberId);

        $return = [
            'info' => $info,
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 获取用户保存的联系人信息
     * @author: zhangyz
     * @date: 2020/6/1
     *
     * @param $memberId
     *
     * @return array
     */
    private function _getContacts($memberId)
    {
        $contactsBiz = new \Business\Member\FrequentContacts();
        $contactsRes = $contactsBiz->getAllContacts($memberId);

        $list = [];
        //兼容旧数据格式
        if ($contactsRes['code'] == 200 && $contactsRes['data']) {
            foreach ($contactsRes['data'] as $item) {
                $list[] = [
                    'id'             => $item['id'],
                    'name'           => $item['name'],
                    'tel'            => $item['mobile'],
                    'idCard'         => $item['person_id'],
                    'person_id_type' => $item['person_id_type'],
                    'province'       => $item['province'],
                    'province_code'  => $item['province_code'],
                    'city'           => $item['city'],
                    'city_code'      => $item['city_code'],
                    'town'           => $item['town'],
                    'town_code'      => $item['town_code'],
                    'detail_addr'    => $item['address'],
                ];
            }
        }

        return $list;
    }

    /**
     * 合作伙伴 - 获取供应商分页列表
     * <AUTHOR>
     * @date   2017-06-13
     *
     * @param  int  $page  页码       从1开始
     * @param  int  $size  页数据大小 默认8
     */
    public function getSupplierList()
    {
        $page    = I('page', 1, 'intval');
        $size    = I('size', 8, 'intval');
        $keyword = I('keyword', '', 'strval');

        if (!$page || !$size) {
            $this->apiReturn(201, [], '参数错误');
        }

        // 权限判断
        $auth = $this->checkAuth(['supplyPartner']);
        if (!$auth) {
            $this->apiReturn(201, [], '您没有查看供应商的权限');
        }

        $sid = $this->_loginMember['sid'];

        $biz = new \Business\Member\PartnerSuppliers();
        $res = $biz->getSuppliersList($sid, $keyword, $page, $size);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        foreach ($res['data']['list'] as &$item) {
            $item['id'] = $item['fid'];
        }

        $this->apiReturn(200, $res['data']);
    }

    /**
     * 合作伙伴 - 获取供应商总数量 和 分销商总数量 和各分组的分销商数量
     * <AUTHOR>
     * @date   2017-06-14
     */
    public function getPartnerCount()
    {
        // 权限判断
        $auth = $this->checkAuth(['supplyPartner', 'fxPartner']);
        if (!$auth) {
            $this->apiReturn(201, [], '您没有查看的权限');
        }

        // 设置权限标识
        if (count($auth) == 2) {
            $type = 2;

        } elseif (isset($auth['fxPartner'])) {
            $type = 1;

        } elseif (isset($auth['supplyPartner'])) {
            $type = 0;
        }

        $bus = new \Business\Member\MemberRelation();
        $sid = $this->_loginMember['sid'];
        $res = $bus->getPartnerCount($sid, $type);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(200, $res['data']);
    }

    /**
     * 合作伙伴 - 获取供应商总数量
     * <AUTHOR>
     * @date   2017-06-13
     */
    public function getSupplierCount()
    {
        // 权限判断
        $auth = $this->checkAuth(['supplyPartner']);
        if (!$auth) {
            $this->apiReturn(201, [], '您没有查看供应商的权限');
        }

        $bus = new \Business\Member\MemberRelation();
        $sid = $this->_loginMember['sid'];
        $res = $bus->getSupplierCount($sid);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(200, $res['data']);
    }

    /**
     * 合作伙伴 - 获取供应商各个分组和各组的分销商数量
     * <AUTHOR>
     * @date   2017-06-13
     */
    public function getGroupReseller()
    {
        // 权限判断
        $auth = $this->checkAuth(['supplyPartner']);
        if (!$auth) {
            $this->apiReturn(201, [], '您没有查看供应商的权限');
        }

        $bus = new \Business\Member\MemberRelation();
        $sid = $this->_loginMember['sid'];
        $res = $bus->getGroupReseller($sid);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(200, $res['data']);
    }

    /**
     * 合作伙伴 - 模糊查找供应商或分销商
     * <AUTHOR>
     * @date   2017-06-14
     *
     * @param  string  $search  关键字查询 [账户名称/姓名/手机号]
     */
    public function searchPartner()
    {
        // 权限判断
        $auth = $this->checkAuth(['supplyPartner', 'fxPartner']);
        if (!$auth) {
            $this->apiReturn(201, [], '您没有查看的权限');
        }

        $keyword = I('search', '', 'trim');

        $bus = new \Business\Member\MemberRelation();
        $sid = $this->_loginMember['sid'];
        $res = $bus->searchPartner($sid, $keyword);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $data = $res['data'];

        // 供应商信息不可见
        if (!isset($auth['supplyPartner'])) {
            unset($data['supplier']);
        }

        // 分销商信息不可见
        if (!isset($auth['fxPartner'])) {
            unset($data['reseller']);
        }

        $this->apiReturn(200, $data);
    }


    /**
     * 根据分组名称模糊查询
     * <AUTHOR>  Li
     * @date  2020-10-30
     */
    public function queryGroupNameListBySidAndGroupName()
    {
        $sid        = $this->isLogin();
        $pageSize   = I('post.page_size', 10, 'intval');        //分页长度
        $pageNumber = I('post.page', 1, 'intval');              //页码
        $groupName  = I('post.group_name', '', 'strval');     //分组名称

        $relationBiz = new \Business\Member\MemberEvoluteRelation();
        $result      = $relationBiz->queryGroupNameListBySidAndGroupName($sid, $groupName, $pageSize,
            $pageNumber);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $list    = [];
        $javaApi = new \Business\JavaApi\Product\EvoluteProduct();

        foreach ($result['data']['list'] as $item) {
            $hasPrice = 0;
            $checkRes   = $javaApi->queryProductCount($item['group_id'], $sid, $sid);
            if ($checkRes['code'] == 200 && !empty($checkRes['data'])) {
                $hasPrice = 1;
            }
            $list[] = [
                'id'       => $item['group_id'],
                'name'     => $item['group_name'],
                'hasPrice' => $hasPrice,
            ];
        }

        $this->apiReturn(200, $list, '分组获取成功');
    }

    /**
     * 获取分销商的分组情况
     * <AUTHOR>
     * @date   2017-08-02
     */
    public function getResellerGroups()
    {
        $sid         = $this->isLogin();
        $keyword     = I('post.keyword', '', 'trim');
        $page        = I('post.page', 1, 'intval');
        $pageSize    = I('post.size', 300, 'intval');
        $relationBiz = new \Business\Member\MemberEvoluteRelation();
        $result      = $relationBiz->queryGroupNameListBySidAndGroupName($sid, $keyword, $pageSize, $page);

        if ($result['code'] != 200 || empty($result['data']['list'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $javaApi   = new \Business\JavaApi\Product\EvoluteProduct();
        $groupInfo = [];
        foreach ($result['data']['list'] as $group) {
            $hasPrice = 0;
            $checkRes = $javaApi->queryProductCount($group['group_id'], $sid, $sid);
            if ($checkRes['code'] == 200 && !empty($checkRes['data'])) {
                $hasPrice = 1;
            }
            $groupInfo[] = [
                'id'       => $group['group_id'],
                'name'     => $group['group_name'],
                'hasPrice' => $hasPrice,
            ];
        }

        //$model     = new \Model\Product\PriceGroup();
        //$groupInfo = $model->getGroupBySidAndKeyword($sid, '', 'id,name,default_inc,default');
        //foreach ($groupInfo as $key => $group) {
        //
        //    // 默认0没有配置产品价格
        //    $groupInfo[$key]['hasPrice'] = 0;
        //    $checkRes   = $javaApi->queryProductCount($group['id'], $sid, $sid);
        //    if ($checkRes['code'] == 200 && !empty($checkRes['data'])) {
        //        $groupInfo[$key]['hasPrice'] = 1;
        //    }
        //
        //}

        //array_push($groupInfo, [
        //    'id'        => 0,
        //    'name'      => '未分组',
        //    'hasPrice'  => 0,
        //]);

        $this->apiReturn(200, $groupInfo);
    }

    /**
     * 合作供应商授信信息
     * <AUTHOR>
     * @date   2017-08-02
     */
    public function getSupplierInfo()
    {
        $aid        = I('aid', 0, 'intval');
        $h          = I('h', 0, 'intval');
        $resellerId = $this->isLogin();
        $mModel     = $this->getMemberModel();
        $pft_openid = $this->_loginMember['pft_openid'];

        if ($h == 'm') {
            $h = self::__DEFAULT_ACCOUNT;
        }

        if (!$aid || !$h || !is_numeric($h) || !is_numeric($aid)) {
            $this->apiReturn(201, [], '参数错误');
        }

        // 判断是否在未获授权的微信中
        if ($this->inWechatApp() && !$pft_openid) {
            if (ENV == 'PRODUCTION') {
                $url = $this->getMicroPlatHost() . 'r/MicroPlat_Member/pftWxAuth?from=credit&sid=' . $aid . '&h=' . $h;
                $this->apiReturn(206, ['url' => $url]);
            }
        }

        // 是否是供应商
        $sid = $this->isLogin();
        $bus = new \Business\Member\MemberRelation();
        $res = $bus->getSupplierCount($sid);
        if ($res['code'] != 200) {
            $this->apiReturn(201, [], $res['msg']);
        }

        $supplierList = $res['data']['supplier_list'];
        if ($supplierList) {
            $supplierList = array_column($supplierList, 'id');
        }
        if (!in_array($aid, $supplierList)) {
            $this->apiReturn(201, [], '分销关系不存在');
        }

        $amountLedgerFreezeBiz = new AmountLedgerFreezeBiz();
        if ($amountLedgerFreezeBiz->isFrozenEnabled($resellerId)) {
            $cmoney = $amountLedgerFreezeBiz->getTotalBalance($resellerId);
            $freMoney = $amountLedgerFreezeBiz->getFrozenBalance($resellerId);
            $remain = floatval($cmoney / 100);
        } else {
            // 分销商账户余额
            $remain = 0;
            $cmoney = $mModel->getMoney($resellerId, 0);

            if ($cmoney === false) {
                $this->apiReturn(201, [], '系统异常，请重试【err_code=2601】');
            }

            if (is_numeric($cmoney)) {
                $remain = floatval($cmoney / 100);
            }

            //需要将冻结的金额扣除，才是可以进行还款的部分
            $accountMoneyBiz = new AccountMoney();
            $res             = $accountMoneyBiz->getFreezeMoney($resellerId, true, 'supply_repayment');
            if ($res['code'] != 200) {
                $this->apiReturn(201, [], '系统异常，请重试【err_code=9601】');
            }
            $freMoney       = intval($res['data']['money']);
        }

        $freMoneyFormat = floatval($freMoney / 100);

        $mid = $this->isLogin('ajax');
        // 供应商信息
        $mInfo      = $mModel->getMemberInfo($aid, 'id', 'dname,cname,mobile,account,headphoto');
        $memberInfo = $mModel->getMemberInfo($mid, 'id', 'mobile');
        if (!$mInfo) {
            $this->apiReturn(201, [], '供应商账号不存在');
        }

        // 供应商授信
        $credit = 0;
        //已用余额
        $kmoney = 0;
        //可用余额
        $useMoney   = 0;
        $creditInfo = $this->getCreditInfo($resellerId, $aid);
        if ($creditInfo) {
            if ($creditInfo['mode'] == 1) {
                $credit = -1; // 不限
            } elseif ($creditInfo['mode'] == 0 && isset($creditInfo['credit'])) {
                $credit = floatval($creditInfo['basecredit'] / 100);
            }
            $kmoney   = floatval($creditInfo['kmoney'] / 100);
            $useMoney = floatval($creditInfo['credit']);
        }

        //还款  供应商是否设置了还款方式
        $repayInfo = (new \Business\JavaApi\Member\MemberClearingWay())->getMemeberClearingWay($aid,
            $resellerId);
        if ($repayInfo['code'] != 200) {
            $this->apiReturn(204, [], '还款方式获取失败');
        }
        $repayMode = $repayInfo['data']['repayMode'] ?? 0;

        $return = [
            'credit'           => $credit, // 信用额度 -元 -1为不限
            'kmoney'           => $kmoney, // 信用余额 -元
            'remain'           => $remain, // 账户余额 -元
            'freeze_balance'   => $freMoneyFormat, // 账户余额 -元
            'use_money'        => $useMoney, // 剩余可用余额
            'dname'            => $mInfo['dname'],
            'cname'            => $mInfo['cname'],
            'headphoto'        => $mInfo['headphoto'],
            'mobile'           => $mInfo['mobile'],
            'activeUserMobile' => $memberInfo['mobile'],
            'account'          => $mInfo['account'],
            'openid'           => $pft_openid ?: '',
            'did'              => $resellerId,
            'repayMode'        => $repayMode,
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 合作分销商授信信息
     * <AUTHOR>
     * @date   2017-08-02
     */
    public function getResellerInfo()
    {
        $resellerId = I('did', 0, 'intval'); // 分销商ID
        $aid        = $this->isLogin();
        $mModel     = $this->getMemberModel();

        // 分销商当前所属的分组信息
        $groupInfo = [];
        $bus       = new \Business\Member\MemberRelation();
        $res       = $bus->getResellerGroupInfo($aid, $resellerId);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
        $groupInfo = [
            'group_id'   => $res['data']['group_id'],
            'group_name' => $res['data']['group_name'],
        ];

        // 分销商信息
        $mInfo = $mModel->getMemberInfo($resellerId, 'id', 'dname,cname,mobile,account,headphoto');

        // 供应商授信
        $credit     = $kmoney = 0;
        $creditInfo = $this->getCreditInfo($resellerId, $aid);
        if ($creditInfo) {
            if ($creditInfo['mode'] == 1) {
                $credit = -1; // 不限
            } elseif ($creditInfo['mode'] == 0 && isset($creditInfo['credit'])) {
                $credit = floatval($creditInfo['basecredit'] / 100);
                $kmoney = floatval($creditInfo['kmoney'] / 100);
            }
        }
        $remark = '';
        if (isset($mInfo['id'])) {
            $memberbus  = new MemberRelationshipRemark();
            $remarkInfo = $memberbus->queryDistributorRemarkName($resellerId, $aid);
            if ($remarkInfo['code'] == 200 && !empty($remarkInfo['data']['remark'])) {
                $remark = $remarkInfo['data']['remark'];
            }
        }

        $return = [
            'credit'    => $credit, // 信用额度 -元 -1为不限
            'kmoney'    => $kmoney, // 信用余额 -元
            'dname'     => $mInfo['dname'],
            'cname'     => $mInfo['cname'],
            'mobile'    => $mInfo['mobile'],
            'account'   => $mInfo['account'],
            'headphoto' => $mInfo['headphoto'],
            'remark'    => $remark,
            'group'     => $groupInfo,
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 修改指定分销商所属的分组
     * <AUTHOR>
     * @date   2017-08-02
     */
    public function changeResellerGroup()
    {
        $resellerId = I('did', 0, 'intval');
        $newGroupId = I('gid', -1, 'intval');
        if ($newGroupId <= 0) {
            $this->apiReturn(201, [], '请选择新分组');
        }

        $sid = $this->isLogin();

        //$vacationBiz = new \Business\PftSystem\VacationModeBiz();
        //$identifier = 'priceSet';
        //$isOpen     = $vacationBiz->judgeForPage($identifier, I('session.saccount'));
        //if ($isOpen ===  false) {
        //    $this->apiReturn(201, [], '当前处于假日模式，该功能暂停使用');
        //}

        // 禁止频繁操作
        $cache    = $this->getRedisObject();
        $lockey   = "{sid}:{$resellerId}:move_group";
        $lockInfo = $cache->lock($lockey, 1, $lockTime = 120);
        if (!$lockInfo) {
            $this->apiReturn(201, [], '分销商正在分组.... 请稍后再试');
        }

        $groupBiz        = new \Business\Product\Update\EvoluteGroup();
        $originGroupInfo = $groupBiz->queryByGroupId($newGroupId);
        if ($originGroupInfo['code'] != 200 || empty($originGroupInfo['data'])) {
            $this->apiReturn(403, [], '无默认分组');
        }

        $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
        $getResArr          = $getEvoluteGroupBiz->getFidArrByGroupId($sid, $newGroupId);
        $groupInfoFidArr    = [];

        if ($getResArr['code'] == 200 && !empty($getResArr['data'])) {
            $groupInfoFidArr = $getResArr['data'];
        }

        $limitNum = \Business\Member\DistributorGroup::GROUP_LIMIT_MUM;
        //判断下默认分组的分销商数量是否超过限制， 超过则拒绝
        $currentDidCount = count($groupInfoFidArr);
        if ($currentDidCount >= $limitNum) {
            $this->apiReturn(403, [], "分组中的分销商数到达上限{$limitNum}人，请更换分组后添加");
        }

        //判断分销商是否能添加到指定分组
        $evoluteLimiterService = new EvoluteLimiterService();
        $groupIdAssert         = $newGroupId;
        $assertEvoluteRes      = $evoluteLimiterService->assertEvolutePermitsByGroupId($sid, $resellerId, $groupIdAssert);
        if ($assertEvoluteRes['code'] != 200) {
            return $this->apiReturn($assertEvoluteRes['code'], [],$assertEvoluteRes['msg']);
        }

        // 操作日志
        $log = 'move_group:memberID=' . $this->_loginMember['memberID'] . ':resellerId=' . $resellerId . ':newGroupId=' . $newGroupId . ':' . json_encode(I(''));
        @pft_log('price/request', $log, 'day');

        // 分组切换
        $bus = new \Business\Member\MemberRelation();

        $bus->setOperator($this->_loginMember['sid'], $this->_loginMember['saccount']);

        $res = $bus->changeResellerGroup($sid, $resellerId, $newGroupId);

        $cache->rm($lockey);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(200, [], '分组成功');
    }

    /**
     * 合作伙伴 - 添加分销商之前通过手机号查询用户信息
     * <AUTHOR>
     * @date   2017-08-03
     *
     * @param  string  $mobile  手机号
     */
    public function getAccountInfo()
    {
        $sid = $this->isLogin('ajax');

        //$vacationBiz = new \Business\PftSystem\VacationModeBiz();
        //$identifier = 'priceSet';
        //$isOpen     = $vacationBiz->judgeForPage($identifier, I('session.saccount'));
        //if ($isOpen ===  false) {
        //    $this->apiReturn(500, [], '当前处于假日模式，该功能暂停使用');
        //}

        $mobile = I('mobile', '', 'trim');
        if (!ismobile($mobile)) {
            $this->apiReturn(201, [], '手机号码格式有误');
        }

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->mobileSearchForDistributor($sid, $mobile);

        if (!$result['data']) {
            $this->apiReturn(202, [], '该手机号无匹配会员');
        }

        $data = $result['data'];

        //隐私脱敏处理
        $privacyBiz = new \Business\Member\MemberPrivacy();
        $data['mobile'] = $privacyBiz->filterByMobile($data['mobile']);
        $data['cname']  = $privacyBiz->filterByName($data['cname']);
        $data['com_name']  = $privacyBiz->filterByName($data['com_name']);

        if ($data['created']) {
            $data['valid'] = 2;
        } else {
            $data['valid'] = 1;
        }

        $this->apiReturn(200, $data, '查询成功');
    }

    /**
     * 合作伙伴 - 添加分销商之前模糊查找分销商 [暂时没用]
     * <AUTHOR>
     * @date   2017-07-18
     *
     * @param  string  $search  关键字查询 [账户名称/姓名/手机号]
     * @param  string  $type  搜索类型 ['all'=全选|'supplier'=供应商|'reseller'=分销商]
     */
    public function searchReseller()
    {
        // 权限判断 - 员工需要有添加分销商的权限
        $auth = $this->checkAuth(['addlow']);
        if (!$auth) {
            $this->apiReturn(201, [], '您没有添加分销商的权限');
        }

        $searchType = I('type', 'reseller', 'trim');
        $keyword    = I('search', '', 'trim');

        $sid = $this->_loginMember['sid'];
        $bus = new \Business\Member\MemberRelation();
        $res = $bus->searchPartner($sid, $keyword, $searchType);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        // 分销商列表
        $data = $res['data']['reseller'];

        // 分销商账户类型判断
        foreach ($data as $key => $item) {
            if ($this->isSanKeAccount($item['dtype'], $item['mobile'], $item['account'])) {
                $data[$key]['dtype'] = 5;
            }
        }

        $this->apiReturn(200, $data);
    }

    /**
     * 合作伙伴 - 添加平台用户为分销商
     * <AUTHOR>
     * @date   2017-08-07
     *
     * @param  int  $did  分销商ID
     */
    public function addReseller()
    {
        // 权限判断 - 员工需要有添加分销商的权限
        $auth = $this->checkAuth(['addlow']);
        if (!$auth) {
            $this->apiReturn(201, [], '您没有添加分销商的权限');
        }

        $resellerId = I('did', 0, 'intval'); // 分销商ID

        $sid           = $this->isLogin();
        $loginMemberId = $this->_loginMember['memberID'];
        $bus           = new \Business\Member\MemberRelation();

        // 禁止频繁操作
        $cache    = $this->getRedisObject();
        $lockey   = "lock:add_reseller:{$sid}_{$resellerId}";
        $lockInfo = $cache->lock($lockey, 1, $lockTime = 120);
        if (!$lockInfo) {
            $this->apiReturn(201, [], '该分销商正在添加中，请稍后再试');
        }

        // 平台用户是否存在
        $mInfo = $this->getMemberModel()->getMemberInfo($resellerId, 'id', 'mobile');
        if (!$mInfo) {
            $this->apiReturn(201, [], '平台用户不存在');
        }

        // 分销关系是否已存在
        $res = $this->_isReseller($sid, $mInfo['mobile'], 'reseller');
        if ($res['code'] == 200) {
            $this->apiReturn(201, [], '分销关系已存在，无需再次添加');
        }

        $limitRes = (new \Business\Member\DistributorGroup())->defaultGroupOverLimit($sid);
        if ($limitRes['over_limit']) {
            $this->apiReturn(403, [], '分组中的分销商数到达上限, 请更换默认分组后添加');
        }

        // <AUTHOR> | @date 2018/08/27
        //检查是否需要添加审核
        $memberBiz = new \Business\Member\Member();
        $status    = $memberBiz->getMemberExtInfoGetFieldToJava($resellerId, 'distribution_check');
        if ($status == 1) { //需要审核
            $Audit  = new \Business\Member\DistributorAudit();
            $return = $Audit->addDistributionTable($resellerId, $sid);
            $cache->rm($lockey);
            $this->apiReturn($return['code'], [], $return['msg']);
        } else { //不需要审核，直接添加
            try {
                // 添加分销商
                $addRes = $bus->addReseller($sid, $resellerId, true, $loginMemberId, '微平台H5');
                if ($addRes['code'] != 200) {
                    throw new \Exception($addRes['code'] . '-' . $addRes['msg']);
                }

            } catch (\Exception $e) {
                $cache->rm($lockey);
                $this->apiReturn(201, [], $e->getMessage());
            }

            $cache->rm($lockey);

            $this->apiReturn(200, [], '分销商添加成功');
        }

    }

    /**
     * 合作伙伴 - 手机号注册并添加为分销商
     * <AUTHOR>
     * @date   2017-08-07
     *
     * @param  string  $mobile  手机号
     * @param  string  $dname  账户名称
     * @param  string  $cname  联系人
     * @param  string  $type  企业类型
     */
    public function registerReseller()
    {
        // 权限判断 - 员工需要有添加分销商的权限
        $auth = $this->checkAuth(['addlow']);
        if (!$auth) {
            $this->apiReturn(201, [], '您没有添加分销商的权限');
        }

        $mobile = I('mobile', '', 'trim');    // 手机号
        $dname  = I('dname', '', 'trim');     // 账户名称
        $cname  = I('com_name', '', 'trim');  // 联系人
        $type   = I('com_type', '', 'trim');  // 企业类型
        $vcode  = I('vcode', '', 'trim');     //手机验证码

        if (!$vcode) {
            $this->apiReturn(201, [], '请输入短信验证码');
        }

        $vcodeRes = Vcode::verifyVcode($mobile, $vcode, 'add_distributor');
        if ($vcodeRes['code'] != 200) {
            $this->apiReturn(201, [], $vcodeRes['msg']);
        }

        $sid           = $this->isLogin();
        $loginMemberId = $this->_loginMember['memberID'];
        $bus           = new \Business\Member\MemberRelation();

        // 禁止频繁操作
        $cache    = $this->getRedisObject();
        $lockey   = "lock:register_add_reseller:{$sid}_{$mobile}";
        $lockInfo = $cache->lock($lockey, 1, $lockTime = 120);
        if (!$lockInfo) {
            $this->apiReturn(201, [], '正在为您注册分销商，请稍后再试');
        }

        try {
            // 注册账户
            $regRes = $bus->registerReseller($sid, $mobile, $dname, $cname, $type);
            if ($regRes['code'] != 200) {
                throw new \Exception('账号注册出错:' . $regRes['code'] . '-' . $regRes['msg']);
            }

            $resellerId = $regRes['data']['memberId'];

            // 添加分销商
            $addRes = $bus->addReseller($sid, $resellerId, false, $loginMemberId, '微平台H5', 3);
            if ($addRes['code'] != 200) {
                throw new \Exception('账号注册成功，建立分销关系出错:' . $addRes['code'] . '-' . $addRes['msg']);
            }

        } catch (\Exception $e) {
            $cache->rm($lockey);
            $this->apiReturn(201, [], $e->getMessage());
        }

        $cache->rm($lockey);

        // 分销商账号信息
        $mInof = $this->getMemberModel()->getMemberInfo($resellerId, 'id', 'account');
        $msg   = '分销商添加成功，您可以使用账号' . $mInof['account'] .
                 '或手机号登录平台，密码是您手机号末6位';

        // 前端需要的数据
        $return = [
            'id'      => $resellerId,
            'account' => $mInof['account'],
        ];

        $this->apiReturn(200, $return, $msg);
    }

    /**
     * 合作伙伴 - 分组内部查询分页分销商
     * <AUTHOR>
     * @date   2017-06-15
     *
     * @param  int  $gid  分组ID
     * @param  int  $size  页数量  0则获取全部
     * @param  int  $page  页码    从1开始 0则获取全部
     */
    public function getGroupResellerList()
    {
        // 权限判断
        $auth = $this->checkAuth(['fxPartner']);
        if (!$auth) {
            $this->apiReturn(201, [], '无查看分销商的权限');
        }

        $sid     = $this->_loginMember['sid'];
        $groupId = I('gid', -1, 'intval');
        $page    = I('page', 1, 'intval');
        $size    = I('size', 5, 'intval');

        $bus = new \Business\Member\MemberRelation();
        $res = $bus->getGroupResellerList($sid, $groupId, $page, $size);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(200, $res['data']);
    }

    /**
     * 订单查询 - 所有供应商
     * <AUTHOR>
     * @date   2017-06-19
     */
    public function getOrderAllSuppier()
    {
        // 权限判断
        // $auth = $this->checkAuth(['supplyPartner']);
        // if (!$auth) {
        //     $this->apiReturn(201, [], '无查看供销商的权限');
        // }

        $bus = new \Business\Member\MemberRelation();
        $sid = $this->_loginMember['sid'];
        $res = $bus->getOrderAllSuppier($sid);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
        $this->apiReturn(200, $res['data']);
    }

    /**
     * 订单查询 - 所有分销商
     * <AUTHOR>
     * @date   2017-06-19
     */
    public function getOrderAllReseller()
    {
        // 权限判断
        // $auth = $this->checkAuth(['fxPartner']);
        // if (!$auth) {
        //     $this->apiReturn(201, [], '无查看分销商的权限');
        // }

        $bus = new \Business\Member\MemberRelation();
        $sid = $this->_loginMember['sid'];
        $res = $bus->getOrderAllReseller($sid);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }
        // 加入散客
        $list = $res['data'];
        $list = $this->_addSanke($res['data']);
        $this->apiReturn(200, $list);
    }

    /**
     * 订单查询 - 所有分销商和供应商
     * <AUTHOR>
     * @date   2017-06-19
     */
    public function getAllResellerAndSuppier()
    {
        // 权限判断
        // $auth = $this->checkAuth(['supplyPartner','fxPartner']);
        // if (!$auth) {
        //     $this->apiReturn(201, [], '您没有查看的权限');
        // }

        // 设置权限标识
        // if (count($auth) == 2) {
        //     $type = 2;

        // } elseif (isset($auth['fxPartner'])) {
        //     $type = 1;

        // } elseif (isset($auth['supplyPartner'])) {
        //     $type = 0;
        // }

        $bus = new \Business\Member\MemberRelation();
        $sid = $this->_loginMember['sid'];
        $res = $bus->getAllResellerAndSuppier($sid, 2);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        // 加入散客
        $list = $res['data'];
        $list = $this->_addSanke($res['data']);

        $this->apiReturn(200, $list);
    }

    /**
     * 是否已经是分销商/供应商
     * <AUTHOR>
     * @date   2017-08-04
     *
     * @param  int  $sid  当前登录用户ID
     * @param  string  $keyword  关键字查询 [账户名称/姓名/手机号]
     * @param  string  $searchType  搜索类型 ['all'=全选|'supplier'=供应商|'reseller'=分销商]
     */
    private function _isReseller($sid = 0, $keyword = '', $searchType = '')
    {
        if (!$keyword || !$searchType) {
            return ['code' => 204, 'msg' => '参数错误'];
        }

        $bus = new \Business\Member\MemberRelation();
        $res = $bus->searchPartner($sid, $keyword, $searchType);
        if ($res['code'] != 200) {
            return ['code' => $res['code'], 'msg' => $res['msg']];
        }

        // 分销商列表
        $data = $res['data']['reseller'];
        if (!$data) {
            return ['code' => 201, 'msg' => '分销商不存在'];
        }

        return ['code' => 200, 'data' => $data];
    }

    /**
     * 为订单查询的分销商列表添加散客 [与平台一致]
     * <AUTHOR>
     * @date   2017-06-19
     *
     * @param  array  $list  分销商供应商按字母分类后的数组 ['A'=>[[],[]]]
     */
    private function _addSanke($list = [])
    {
        $res = $this->getMemberModel()->getMemberInfo(112, 'id', 'dname,id');
        if ($res) {
            $list['A'][] = [
                'id'    => $res['id'],
                'py'    => 'A',
                'dname' => $res['dname'],
            ];
        }

        return $list;
    }

    /**
     * 绑定微信
     *
     * <AUTHOR>
     * @date   2017-04-20
     *
     * @return bool
     */
    private function _bindWechat()
    {

        $this->getLoginInfo();

        if ($this->inWechatApp() && $this->_loginMember['openid']) {

            // 登陆成功, 绑定微信账号
            $res = (new \Model\Wechat\WxMember())->bindWechat(
                $this->_loginMember['memberID'],
                $this->_supplyId,
                $this->_loginMember['openid'],
                $this->_loginMember['wechat_appid']
            );

            return $res;
        }

        return true;
    }

    /**
     * 图形验证码校验
     * <AUTHOR>
     * @time   2017-02-15
     *
     * @param  string  $authCode  图形验证码
     *
     * @return bool   [校验成功true|校验失败false]
     */
    private function checkImgCode($authCode = '')
    {
        $clientCode = strtolower($authCode);
        $serverCode = I('session.auth_code', '', 'strtolower');
        if ($authCode && $clientCode == $serverCode) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 请求的参数传递
     * <AUTHOR>
     * @time   2017-03-21
     *
     * @return array 整理后的要传递的参数数组
     */
    private function _transParams()
    {
        // 产品排序来源的用户ID
        if (I('ctx', 0, 'intval')) {
            $param['ctx'] = I('ctx', 0, 'intval');
        }

        // 产品渠道
        if (I('ctype', 0, 'intval')) {
            $param['ctype'] = I('ctype', 0, 'intval');
        }

        // 产品类型
        if (I('ptype')) {
            $param['ptype'] = I('ptype');
        }
        if (I('p_type')) {
            $param['ptype'] = I('p_type');
        }

        return $param ?: [];
    }

    /**
     * 个人中心信息
     * <AUTHOR>
     * @date   2017-06-28
     */
    public function userCenterInfo()
    {
        $sid       = $this->isLogin('ajax');
        $fid       = $this->_loginMember['memberID'];
        $dtype     = $this->_loginMember['dtype'];
        $mModel    = $this->getMemberModel();
        $memberBiz = new \Business\Member\Member();

        // 散客跳转微商城个人中心
        if ($this->isSanKeLogin()) {
            $host = $this->_supplyAccount ?: 124078;

            $roleList = (new \Library\Tools\BusinessCache())->getBusinessCache($fid);
            //暂时记录判断日志
            pft_log('userCenterInfo', json_encode([$fid, $host, $dtype, $roleList]));

            if ($dtype == 5) {
                if (!empty($roleList)) {
                    foreach ($roleList as $key => $value) {
                        if (in_array($value['dtype'], [0, 1])) {
                            $fid   = $value['id'];
                            $dtype = $value['dtype'];
                        }
                    }
                }
                if ($dtype == 5) {
                    $this->apiReturn(209, [
                        'url' => str_replace('wx', 'm', MOBILE_DOMAIN) . 'wx/b/transit.html',
                    ], '散客');
                }
            } else {
                $this->apiReturn(209, [
                    'url' => str_replace('wx', 'm', MOBILE_DOMAIN) . 'wx/b/transit.html',
                ], '散客');
            }
        }

        // 用户头像
        $memberInfo    = $mModel->getMemberInfo($fid, 'id', 'headphoto, account');
        $memberExtInfo = $memberBiz->getInfoInMemberExtFromJava($fid, 'com_name');
        $headphoto     = $memberInfo['headphoto'] ?: "images/touxiang.png";
        //获取商家微商城店铺名称
        $shopConfigModel = new ShopConfig();
        $shopConfigInfo  = $shopConfigModel->getWxShopConfig($this->_loginMember['sid'], 'name');
        $userInfo        = [
            'headphoto'  => $headphoto,
            'name'       => $this->_loginMember['dname'],
            'com_name'   => isset($memberExtInfo['com_name']) ? $memberExtInfo['com_name'] : '',
            'dtype'      => $this->_loginMember['sdtype'],
            'account'    => $this->_loginMember['saccount'],
            'store_name' => empty($shopConfigInfo) ? "" : $shopConfigInfo['name'],
        ];

        // 员工和资源方账号 不显示账户余额
        if (!in_array($dtype, [2, 3, 6])) {
            $amountLedgerFreezeBiz = new AmountLedgerFreezeBiz();
            if ($amountLedgerFreezeBiz->isFrozenEnabled($sid)) {
                $userInfo['money'] = $amountLedgerFreezeBiz->getAccountBalance($sid);
            } else {
                $money = $mModel->getPlatformBalanceByMemberId($sid);
                //$money             = $mModel->getMoney($sid, 0);
                $userInfo['money'] = $money;
            }
        }

        // 未读公告数量
        $aModel      = new \Model\Notice\Announce();
        $month       = date('Y-m-d H:i:s', strtotime("-1 month"));
        $map         = [
            'update_time' => ['egt', $month],
        ];
        $allAnnounce = $aModel->getSysNotice(0, 0, 0, 0, 'id', 'id desc', $map);
        if ($allAnnounce) {
            $aidArr      = array_column($allAnnounce, 'id');
            $hasReadAids = $aModel->hasReadAids($fid, $aidArr);
            $unReadAids  = array_diff($aidArr, $hasReadAids);
            if ($unReadAids) {
                $userInfo['unread'] = count($unReadAids);
            }
        }

        $menu = $this->_getMemberMenus();

        $authMenu = (new AuthMicroPlatLogicBiz())->getMicroPlatH5UserMenu($sid, $fid, $dtype, $this->_loginMember['sdtype']);

        $return = ['user_info' => $userInfo, 'menu' => $menu, 'auth_menu' => $authMenu['data'] ?? []];

        $this->apiReturn(200, $return);
    }

    /**
     * 获取用户菜单
     * <AUTHOR>
     * @time   2017-06-23
     */
    private function _getMemberMenus()
    {
        $sid    = $this->_loginMember['sid'];
        $fid    = $this->_loginMember['memberID'];
        $dtype  = $this->_loginMember['dtype'];
        $sdtype = $this->_loginMember['sdtype'];
        $model  = $this->getMemberModel();

        // 菜单项
        $menu = [
            // '权限配置值' => ['type' => '给前端的菜单标识', 'name' => '菜单名称'],
            'pro'        => ['type' => 'book', 'name' => '产品预订'],
            'orderquery' => ['type' => 'orderquery', 'name' => '订单查询'],
            'partner'    => ['type' => 'partner', 'name' => '合作伙伴'],
            // 'chart'      => ['type' => 'chart', 'name' => '图表分析'],
            'poster'     => ['type' => 'poster', 'name' => '海报推广'],
            'verify'     => ['type' => 'verify', 'name' => '订单验证'],
            'addlow'     => ['type' => 'addlow', 'name' => '添加分销商'],
            //'qianggou'   => ['type' => 'qianggou', 'name' => '限时抢购'],
            'trecord'    => ['type' => 'trecord', 'name' => '交易记录'],
        ];
        //公司项目菜单获取
        $companyMenu    = [];
        $roleMemberMode = new RoleMember();
        $partnerInfo    = $roleMemberMode->getAllPartners('member_id');
        $memberIdArr    = array_column($partnerInfo, 'member_id');
        if (in_array($fid, $memberIdArr)) {
            $companyMenu = [
                'firmproject' => "公司项目",
            ];
        }

        //$menu['cutprice'] = ['type' => 'cutprice', 'name' => '砍价'];
        //$menu['pintuan']  = ['type' => 'pintuan', 'name' => '拼团'];
        //unset($menu['qianggou']);
        //unset($menu['pintuan']);

        if ($dtype == 0) {
            //供应商如果在newzd配置了人脸，返回人脸vip接待按钮
            $faceModel = new \Model\Terminal\FaceCompare();
            $isFace    = $faceModel->getPlatformByAid($sid);
            if (count($isFace)) {
                $menu['faceapp']    = ['type' => 'faceapp', 'name' => '人脸小程序生成'];
                $menu['facemanage'] = ['type' => 'facemanage', 'name' => '人脸管理'];
            }
        }

        // 所有登录用户均可见的菜单
        $commonMenu = [
            'studycenter' => '学习中心',
            'announce'    => '升级公告',
        ];

        // 提现按钮菜单
        if (!$this->isSanKeLogin() && in_array($dtype, [0, 1])) {
            $menu['accourtmanage'] = ['type' => 'withdraw', 'name' => '提现'];
        }

        // 所有要判断权限的应用 [应用ID => 变量menu的key值]
        $appIdNameMap = [2 => 'poster'];

        // 特殊账户
        if (in_array($dtype, [2, 3])) {

            // 资源方菜单：票券验证，订单查询
            return array_merge([
                $menu['verify']['type']     => $menu['verify']['name'],
                $menu['orderquery']['type'] => $menu['orderquery']['name'],
            ], $commonMenu, $companyMenu);

        } elseif ($this->isSuper()) {
            // admin菜单：订单查询，图表分析
            return array_merge([
                'datacenter'                => '图表分析',
                $menu['orderquery']['type'] => $menu['orderquery']['name'],
                $menu['trecord']['type']    => $menu['trecord']['name'],
            ], $commonMenu, $companyMenu);
        }

        // 是否有应用中心
        $isAppModel = false;

        $api = new \Business\JavaApi\Member\MemberConfig();
        $res = $api->getConfigWithMemberId($sid);
        if ($res['code'] == 200 && $res['data']) {
            if (!($res['data']['fee_code'] || $res['data']['fee_platform'])) {
                $isAppModel = true;
            }
        }

        if (in_array($dtype, [0, 1, 6])) {
            $menu['verify'] = ['type' => 'verify', 'name' => '订单验证'];
        }

        // 员工
        if ($dtype == 6) {
            // 员工所有权限
            $allAuth = $model->getMemberInfo($fid, 'id', 'member_auth');
            if ($allAuth) {
                $allAuth = explode(',', $allAuth['member_auth']);
            }

            // 合作伙伴权限 supplyPartner=合作供应商 fxPartner=合作分销商
            $partnerAuth = array_intersect(['supplyPartner', 'fxPartner'], $allAuth);
            if (!$partnerAuth) {
                unset($menu['partner']);
            }

            // 不需要过滤的菜单项 [非权限标识partner, 应用菜单的标识poster]
            if ($isAppModel) {
                // 有应用中心，则在应用中心中过滤应用，而不判断权限
                $noFilterMenus = ['poster', 'partner'];
            } else {
                // 没有应用中心，则直接判断权限
                $noFilterMenus = ['partner'];
            }

            // 其他权限判断
            foreach ($menu as $key => $item) {
                if (!in_array($key, $noFilterMenus) && !in_array($key, $allAuth)) {
                    unset($menu[$key]);
                }
            }
            $memberId = $fid;
        } else {
            $memberId = $sid;
        }

        // 订单验证 仅供应商可见
        if (array_key_exists('verify', $menu)) {

            // 主账号类型
            if (!isset($this->_loginMember['sdtype'])) {
                $member = $model->getMemberInfo($sid, 'id', 'dtype');
                $sdtype = $member['dtype'];
            } else {
                $sdtype = $this->_loginMember['sdtype'];
            }

            if ($sdtype != 0) {
                unset($menu['verify']);
            }
        }

        // 应用权限过滤
        if (false) {
            //$appList = (new \Model\AppCenter\ModuleList())->getInfoByUserModule($memberId, array_keys($appIdNameMap));

            $moduleCommonBiz = new \Business\AppCenter\ModuleCommon();
            $appList         = $moduleCommonBiz->getModuleUsedByUid($memberId, '', array_keys($appIdNameMap));

            if ($appList) {
                // 循环校验应用权限
                foreach ($appList as $app) {
                    // 有效性校验
                    $nowTime    = time();
                    $beginTime  = $app['begin_time'];
                    $expireTime = $app['expire_time'];
                    $status     = $app['status'];
                    if (($status != 1) || ($nowTime < $beginTime) || ($nowTime > $expireTime)) {
                        // 删除无效应用
                        unset($menu[$appIdNameMap[$app['module_id']]]);
                    }
                }
            } else {
                // 无应用权限 或 表数据错误 则不显示所有应用
                foreach ($appIdNameMap as $name) {
                    unset($menu[$name]);
                }
            }
        }

        // 整理数据给前端
        $return = [];
        if ($menu) {
            foreach ($menu as $k1 => $v1) {
                $return[$v1['type']] = $v1['name'];
            }
        }

        //判断是否展示全名营销功能模块菜单
        //$allDisBz   = new \Business\Mall\AllDis();
        //$isAllDis   = $allDisBz->checkHasAllDis($this->_loginMember);
        $allDisMenu = [];
        //if ($isAllDis) {
        //    $allDisMenu = ['allDis' => '全民营销'];
        //}

        //分销专员菜单权限
//        $multiDistMem = new MultiDistMember();
//        $moduleRes    = $multiDistMem->checkAuthMultiDistByMicro((int)$sid);
//        if ($moduleRes['code'] == 200 && $moduleRes['data']['isMultiDistAuth']) {
//            $return['multi_dist'] = '分销专员';
//        }
        $moduleBuz = new \Business\AppCenter\Module();
        //旅游小店菜单权限
        $isOpenTourismShop = $moduleBuz->checkUserIsCanUseApp($memberId, 'tourist_shop', false);
        if ($isOpenTourismShop) {
            $return['tourism_shop'] = '旅游小店';
        }
        //如果是员工
        $qx = $allAuth ?? [];

        $isOpenResourceManage = (new \Business\ResourceCenter\ResourceCenter())->hasOpenedResourceCenter($sid, $dtype,
            implode($qx, ','));
        if ($isOpenResourceManage) {
            $return['resource_manage'] = '资源中心';
        }
 
        //特殊团队预约菜单权限
        $isOpenTourismShop = $moduleBuz->checkUserIsCanUseApp($memberId, 'reservation_special_team_order', false);
        if ($isOpenTourismShop) {
            $return['reservation_special_team_order'] = '特殊团队预约';
        }
        //图表分析
        $isOpenTourismShop = $moduleBuz->checkUserIsCanUseApp($sid, 'wx_chart_analyze', false);
        if ($isOpenTourismShop) {
            $return['chart'] = '图表分析';
        }

        //移动端销售报表
        $isOpenMobileSalesReport = $moduleBuz->checkUserIsCanUseApp($sid, 'mobile_sales_report', false);
        if ($isOpenMobileSalesReport) {
            $return['mobile_sales_report'] = '数据报表';
        }

        // 景数通。配置在开放功能。员工不支持查看，仅主账号
        // ！！！！已经移到权限中心，员工可以通过岗位权限控制。
        // ！！！！微平台工作台权限：\Business\Authority\AuthMicroPlatLogic::getMicroPlatH5UserMenu
        // if (6 != $dtype) {
        //     $openAppRes = (new AuthContext())->checkAccessByKey($sid, ['jingshutong_h5']);
        //     if ((200 == $openAppRes['code']) && ($openAppRes['data']['jingshutong_h5'])) {
        //         $return['jingshutong_h5'] = '景数通';
        //     }
        // }

        if ($qx) {
            $isOpenTourismShop = (new AuthLogicBiz())->hasAuth($sid, $memberId, 'wx_chart_analyze');
            if (!$isOpenTourismShop) {
                if (isset($return['chart'])) {
                    unset($return['chart']);
                }
            }
        }

        // 加入公用菜单
        $return = array_merge($return, $commonMenu, $allDisMenu, $companyMenu);

        return $return;
    }

    /***
     * 完善/编辑用户资料
     *
     * <AUTHOR>
     * @date   2018-01-03
     */
    public function compeleteUserInfo()
    {
        //验证登录
        $this->isLogin();

        $memberId = $this->_loginMember['memberID'];
        $dname    = I('dname');

        $comtype      = I('com_type');
        $address      = I('post.address', '', 'trim');
        $province     = I('province');
        $city         = I('city');
        $business     = I('business');
        $businessType = I('business_type');
        $comLevel     = I('com_level');

        $data = [
            'dname'         => $dname,
            'com_type'      => $comtype,
            'province'      => $province,
            'city'          => $city,
            'address'       => $address,
            'business'      => $business,
            'business_type' => $businessType,
            'com_level'     => $comLevel,
        ];

        $busModel = new \Business\Member\AccountInfo();
        $re       = $busModel->microModifyUserInfo($memberId, $data);
        $this->apiReturn($re['code'], [], $re['msg']);
    }

    /***
     * 微平台返回用户相关信息
     *
     * <AUTHOR>
     * @date   2018-01-04
     */
    public function getMemberInfo()
    {
        //验证登录
        $this->isLogin();
        $memberId = $this->_loginMember['memberID'];
        $dtype    = $this->_loginMember['dtype'];

        //获取隐私配置,分销商和供应商才有隐私配置
        $isNotHarass = '';
        if (in_array($dtype, [0, 1])) {
            $privacyBiz = new \Business\Member\MemberPrivacy();
            $checkResult = $privacyBiz->getVerifCodeByFid($memberId);
            //是否开启防骚扰
            $isNotHarass = 0;
            if ($checkResult['code'] != 200) {
                $this->apiReturn($checkResult['code'], [], $checkResult['msg']);
            }
            if (!empty($checkResult['data'])) {
                $isNotHarass = 1;
            }
        }

        //用户二期 - 信息获取修改
        $MemberModel = new \Model\Member\Member();
        $MemberBus   = new \Business\Member\Member();
        $CustomerBus = new \Business\Member\Customer();
        $memberInfo  = $MemberBus->getInfo($memberId, true);
        if (!$memberInfo) {
            $this->apiReturn(201, [], '该用户不存在');
        }
        //$memberInfoMap = $MemberModel->getMemberInfo($memberId, 'id', 'member_auth');
        $memberIdArr   = array_unique([$memberId, $this->_loginMember['sid']]);
        $memberInfoMap = $MemberModel->getMemberInfoByMulti($memberIdArr, 'id', 'member_auth', true);
        $customerInfo  = $CustomerBus->getCustomerInfo($memberInfo['customer_id']);

        $auth = explode(',', $memberInfoMap[$memberId]['member_auth']);
        if (in_array('poster_notify', $auth)) {
            //type 订单海报开启1 关闭 0
            $type = 1;
        } else {
            $type = 0;
        }

        $changeName = true;
        //这边判断 如果com_name是空的可以修改一次，不是空的情况可以修改一次空的话可以修改2次
        $checkComName = $MemberBus->checkUserChangeComName($memberId, '');
        if ($checkComName['code'] != 200) {
            $changeName = false;
        }
        //获取商家微商城店铺名称
        $shopConfigModel = new ShopConfig();
        $shopConfigInfo  = $shopConfigModel->getWxShopConfig($this->_loginMember['sid'], 'name');
        $companyType     = (new \Business\JavaApi\Server\CommonDict())->getComDict();

        $comLevelName     = '未知';
        $businessTypeName = '未知';
        if (isset($companyType[$memberInfo['corp_kind']]['comLevels'])) {
            foreach ($companyType[$memberInfo['corp_kind']]['comLevels'] as $level) {
                if ($level['value'] == $memberInfo['types']) {
                    $comLevelName = $level['label'];
                    break;
                }
            }
        }
        if (isset($companyType[$memberInfo['corp_kind']]['businessTypes'])) {
            foreach ($companyType[$memberInfo['corp_kind']]['businessTypes'] as $level) {
                if ($level['value'] == $memberInfo['format_type']) {
                    $businessTypeName = $level['label'];
                    break;
                }
            }
        }

        //资质认证
        $certification     = [];
        $certificationInfo = (new \Business\Member\CertificationInfo())->getCertificationStatus($memberId);
        if ($certificationInfo['code'] == 200 && !empty($certificationInfo['data'])) {
            $certification = $certificationInfo['data'];
        }

        $data = [
            0 => [
                'dname'              => $memberInfo['dname'],
                'dtype'              => $memberInfo['dtype'],
                'account'            => $memberInfo['account'],
                'saccount'           => $this->_loginMember['saccount'],
                'address'            => $customerInfo["address"],
                'bindEmail'          => $customerInfo["bind_email"] ?? '',
                'mobile'             => $memberInfo['mobile'],
                'smobile'            => $memberInfoMap[$this->_loginMember['sid']]['mobile'],
                'sdname'             => $memberInfoMap[$this->_loginMember['sid']]['dname'],
                'com_name'           => $memberInfo['com_name'],
                'business'           => $memberInfo['business'],
                'com_type'           => $MemberBus::__CORP_KIND_ARR__[$memberInfo['corp_kind']],
                'headphoto'          => $memberInfo['headphoto'],
                'province'           => $memberInfo['province'],
                'city'               => $memberInfo['city'],
                'member_auth'        => $memberInfoMap[$memberId]['member_auth'],
                'type'               => $type,
                'is_change_name'     => $changeName,
                'store_name'         => empty($shopConfigInfo) ? "" : $shopConfigInfo['name'],
                'distribution_check' => $memberInfo['distribution_check'],
                'business_type'      => (int)$memberInfo['format_type'],
                'com_level'          => (int)$memberInfo['types'],
                'com_code'           => $memberInfo['corp_kind'],
                'com_level_name'     => $comLevelName,
                'business_type_name' => $businessTypeName,
                'is_not_harass'      => $isNotHarass,
                'certification'      => $certification,
            ],
        ];

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 输出二维码图片
     *
     * <AUTHOR>
     * @date   2018-01-03
     */
    public function createAddResellerQrcode()
    {

        $memberId = $this->isLogin();

        $appid = $this->parseSupplyAppId(true);
        //供应商id
        $supplyId = $this->parseSupplyMemberId(true);
        //邀请人
        $dname = $this->_loginMember['dname'];

        $business = new \Business\Wechat\QrAddReseller();

        //获取二维码url与scene_id
        $qrCodeInfo = $business->getQrCodeInfo($memberId, $supplyId, $appid);

        if ($qrCodeInfo['code'] != 200) {
            $this->apiReturn($qrCodeInfo['code'], [], $qrCodeInfo['msg']);
        }

        $qrCodeInfo['data']['dname'] = $dname;
        $data                        = $qrCodeInfo['data'];

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 获取推荐分销商
     * <AUTHOR>
     * @date 2021/6/15
     *
     */
    public function getPriorityDistributor()
    {
        $key           = I('keyword', '', 'strval');
        $page          = I('page', 0, 'int');
        $pageSize      = I('pagesize', 0, 'int');


        $memberRelationBus = new \Business\Member\MemberRelation();
        $result            = $memberRelationBus->getRecommendDistributor($key, $page, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    public function qrcodeTest()
    {

        $senceId = I('senceId', 0, 'intval');

        if (!$senceId) {
            $this->apiReturn(204, [], '参数错误');
        }

        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();

        $url = MOBILE_DOMAIN . 'api/index.php?c=MicroPlat_Member&a=addResellerTest&senceId=' . $senceId;

        $qrCode = new QrCode($url);
        $qrCode->setText($url)
               ->setSize(150)
               ->setPadding(10)
               ->setErrorCorrection('high')
               ->setForegroundColor(['r' => 0, 'g' => 0, 'b' => 0, 'a' => 0])
               ->setBackgroundColor(['r' => 255, 'g' => 255, 'b' => 255, 'a' => 0])
               ->setLabelFontSize(16)
               ->setImageType('png');

        header('Content-Type: ' . $qrCode->getContentType());
        $qrCode->render();
    }

    public function addResellerTest()
    {

        $senceId = I('senceId', 0, 'intval');

        if (!$senceId) {
            $this->apiReturn(204, [], '参数错误');
        }

        $request = [
            'eventkey'     => 'qrsence_' . $senceId,
            'fromusername' => str_shuffle('olldBuFVBwDpfCs4UP6UZetB3yi4'),
        ];

        $biz   = new \Business\Member\MemberRelation();
        $appid = $this->parseSupplyAppId();

        $a = $biz->addResellerFromQr($request, $appid);
        var_dump($a);
    }

    /***
     * 扫码添加分销商-发送二维码图片到公众号
     *
     * <AUTHOR>
     * @date   2018-01-04
     *
     */
    public function sendQrCodeToWechat()
    {
        $memberId = $this->isLogin();

        $sceneId = I('scene_id');
        $openid  = I('session.openid', '');
        $appid   = I('session.wechat_appid');
        $dname   = $this->_loginMember['dname'];

        if (!$openid || !$appid) {
            return $this->apiReturn(204, [], 'openid缺失，请退出重试');
        }

        $business = new \Business\Wechat\QrAddReseller();

        $text = <<<TEXT
扫一扫或识别二维码，
立即注册成为$dname 的分销商
TEXT;
        //发送二维码图片以及文字消息至公众号
        $re = $business->sendQrWithMessage($openid, $memberId, $appid, $sceneId, $text);

        $re['code'] == 200 ? $this->apiReturn(200, [], '发送成功') : $this->apiReturn(203, [], '发送失败');
    }

    /**
     * 微平台-用户编辑头像
     *
     * <AUTHOR>
     * @date   2018-01-103
     */
    public function editUserHeadphoto()
    {
        $this->isLogin();
        $mid = $this->_loginMember['memberID'];

        $avatar = I('avatar', '');

        if (!$avatar) {
            $this->apiReturn(204, [], '参数错误');
        }

        $data = [
            'headphoto' => $avatar,
        ];

        $accountBiz = new \Business\Member\AccountInfo();

        $result = $accountBiz->editUserInfo($mid, $data);//保存头像

        if (isset($result['code'])) {
            $cache         = new \Library\Tools\BusinessCache();
            $businessCache = $cache->getBusinessCache($mid);
            $cache->setBusinessCache(['headImg' => $avatar ?: $businessCache['headImg']], $mid);

            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务器错误');
        }

    }

    /***
     * 校验修改微平台信息短信
     *
     * <AUTHOR>
     * @date   2019-12-04
     *
     */
    public function checkPhoneCode()
    {
        $sid      = $this->isLogin();
        $memberId = $this->_loginMember['memberID'];
        $type     = I('post.type', 'intval', 0);
        $code     = I('post.sms_code');
        $mobile   = I('post.mobile', '');
        if (!$type || !$code || !$mobile) {
            $this->apiReturn(406, [], '参数错误');
        }
        $memberBiz = new \Business\Member\Member();
        $res       = $memberBiz->checkPhoneCode($type, $code, $mobile, $memberId);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务错误');
        }
    }

    /***
     * 发送短信码
     *
     * <AUTHOR>
     * @date   2019-12-04
     *
     */
    public function sendPhoneCode()
    {
        $sid       = $this->isLogin();
        $memberId  = $this->_loginMember['memberID'];
        $type      = I('post.type', 0,'intval');
        $mobile    = I('post.mobile', '');
        $isCF      = I('post.is_c_f', 0, 'intval');
        $version   = I('post.version', 'v1', 'strval');
        $mid       = $memberId;

        //是否是资质认证操作的， 如果是需要将主账号当成用户id传下去
        if ($isCF) {
            $mid = $sid;
        }

        $memberBiz = new \Business\Member\Member();
        $res       = $memberBiz->sendPhoneCode($type, $mobile, $mid, $version);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务错误');
        }
    }

    /***
     * 短信修改企业名称
     *
     * <AUTHOR>
     * @date   2019-12-04
     *
     */
    public function microChangeComName()
    {
        $sid      = $this->isLogin();
        $memberId = $this->_loginMember['memberID'];
        $comName  = I('post.com_name', '');
        if (!$comName) {
            $this->apiReturn(204, [], '参数错误');
        }

        $memberBiz = new \Business\Member\Member();
        $res       = $memberBiz->SmsChangeComName($memberId, $comName);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务错误');
        }
    }

    /***
     * 短信修改电话
     *
     * <AUTHOR>
     * @date   2019-12-04
     *
     */
    public function microChangeMobile()
    {
        $sid      = $this->isLogin();
        $memberId = $this->_loginMember['memberID'];
        $mobile   = I('post.mobile', '');
        $type     = I('post.type', 0);
        $code     = I('post.sms_code', 0);
        $version  = I('post.version', 'v1', 'strval');
        if (!$mobile || !$type || !$code) {
            $this->apiReturn(204, [], '参数错误');
        }
        $memberBiz = new \Business\Member\Member();
        $res       = $memberBiz->SmsChangeMobile($memberId, $code, $type, $mobile, $version);
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务错误');
        }
    }

    /**
     * 微平台合作伙伴，修改分销商备注
     * <AUTHOR>
     * @date 2020/8/11
     *
     */
    public function updateRemark()
    {
        $sid       = $this->isLogin(); //上级id
        $memberSid = $this->_loginMember['memberID']; //当前用户id
        $memberId  = I('post.member_id', 0); //需要更改备注的分销商id
        $remark    = I('post.remark', ' ', 'strval,trim'); //备注

        //长度不超100
        if (mb_strlen($remark) > 100) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '备注字数超出限制');
        }

        $memberbus = new MemberRelationshipRemark();
        $res       = $memberbus->addDistributorRemarkName($memberId, $sid, $memberSid, $remark);
        if ($res['code'] != 200) {
            $this->apiReturn(self::CODE_INVALID_REQUEST, [], $res['msg']);
        } else {
            $this->apiReturn(self::CODE_SUCCESS, [], $res['msg']);
        }
    }

    //===================新版添加分销商================

    /**
     * 添加分销商
     * <AUTHOR>  Li
     * @date  2020-08-07
     *
     * @return array
     */
    public function addDistributorMemberRelation()
    {
        $memberInfo = $this->_loginMember;
        $sonId      = I('post.son_id', 0);
        $groupId    = I('post.group_id', 0);
        $notice     = I('post.notice', 1);   // 1短信 2微信
        $remark     = I('post.remark', '');   // 备注信息

        if (!is_numeric($sonId) || $sonId <= 0) {
            $this->apiReturn(204, [], '参数有误');
        }

        //隐私验证
        $vcode    = I('post.vcode', '', 'strval');
        $token    = I('post.token', '', 'strval');//验证码token

        $memberPrivacyBiz = new MemberPrivacyBiz();
        //格式化参数
        $verifData = $memberPrivacyBiz->verifCodeParamsFormat($memberInfo['sid'], $memberInfo['memberID'], $vcode, $token);
        //验证信息
        $verifResult = $memberPrivacyBiz->addDistributorPrivacyAuth($verifData);
        if ($verifResult['code'] != 200) {
            $this->apiReturn($verifResult['code'], $verifResult['data'], $verifResult['msg']);
        }

        $memberRelationBiz = new MemberRelationBiz();
        $result            = $memberRelationBiz->addDistributor($memberInfo['sid'], $sonId, $memberInfo['memberID'],
            $groupId, true, $notice, $remark, '微平台H5');
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 邀请码添加分销商
     * <AUTHOR> Li
     * @date   2020-08-11
     *
     * @param  int  $mobile  手机号
     * @param  string  $vcode  短信验证码
     */
    public function applyCreateMemberRelationByInviteCode()
    {
        $sid        = I('post.sid', 0, 'intval');        //供应商id
        $fid        = I('post.son_id', 0, 'intval');     //分销商id
        $inviteCode = I('post.invite_code', '', 'trim'); //邀请码
        $remark     = I('post.remark', '', 'trim');
        if (mb_strlen($remark) > 50) {
            $this->apiReturn(203, [], '备注字符过长，不得超过50字符');
        }
        //需要审核发起邀请码审核 否则直接建立分销关系
        $memberRelationBiz = new \Business\Member\MemberRelation();
        $addRes            = $memberRelationBiz->addDistributorByInviteCode($sid, $fid, $fid, $inviteCode, 0, $remark);

        if ($addRes['code'] == 200) {
            $this->apiReturn(200, $addRes['data'], $addRes['msg']);
        } else {
            $this->apiReturn($addRes['code'], $addRes['data'], $addRes['msg']);
        }
    }

    /**
     * 获取新版微商城地址
     * <AUTHOR>
     * @date   2020-11-04
     *
     */
    public function getMiniStoreUrl()
    {
        $memberInfo = $this->_loginMember;
        if (in_array($memberInfo['dtype'], [2, 3, 5, 7, 9])) {
            $data = [
                'mall_link'  => '',
                'headphoto'  => '',
                'account'    => $memberInfo['account'],
                'store_name' => '',
                'name'       => $memberInfo['dname'],
            ];
            $this->apiReturn(203, $data, '无新版微商城地址');
        }
        //新版微商城地址
        $url = $this->_getMallLink(0, $memberInfo['saccount']);

        $wechatShopBiz = new WechatShop();
        $res           = $wechatShopBiz->getWxShopConfig($memberInfo['sid']);
        if ($res['code'] == 200 && $res['data']) {
            $wechatModule = load_config('wx_module', 'authority');
            $isShowSmall  = $res['data']['home_qrcode_type'];
            $isDefault    = false;
            $moduleRes    = $this->_getUserOpenWechatModule($memberInfo['sid'], array_keys($wechatModule));

            foreach ($wechatModule as $k => $v) {
                if (!isset($moduleRes[$k]) && $v['is_show'] == 1) {
                    $moduleRes = array_merge($moduleRes, [$k => ['module_id' => $k]]);   //所有人都有
                    break;
                }
            }
            foreach ($wechatModule as $key => $value) {
                if (isset($moduleRes[$key]) && $isShowSmall == $value['type']) {
                    $isDefault = false;
                    $url       = $this->_getMallLink($isShowSmall, $memberInfo['saccount']);
                    break;
                } else {
                    $isDefault = true;
                }
            }

            if ($isDefault) {
                $isShowSmall = 0;
                $url         = $this->_getMallLink($isShowSmall, $memberInfo['saccount']);
            }
        }
        //获取商家微商城店铺名称
        $shopConfigModel = new ShopConfig();
        $shopConfigInfo  = $shopConfigModel->getWxShopConfig($memberInfo['sid'], 'name');

        $data = [
            'mall_link'  => $url,
            'headphoto'  => $memberInfo['headImg'] ?? '',
            'account'    => $memberInfo['account'],
            'store_name' => empty($shopConfigInfo) ? "" : $shopConfigInfo['name'],
            'name'       => $memberInfo['dname'],
        ];
        $this->apiReturn(200, $data, '成功');
    }

    /**
     * 获取新版微商城的地址
     *
     * <AUTHOR>
     * @date   2020-11-18
     *
     * @param  int  $type  类型 0新版微商城 1旧版 2小程序
     * @param  int  $saccount
     *
     * @return string
     */
    private function _getMallLink($type = 0, $saccount)
    {
        if (!$type) {
            //新版微商城地址
            $url = MOBILE_DOMAIN . 'h5';
            $url = str_replace('wx.', $saccount . '.', $url);
        } else {
            $url = '';
        }

        return $url;
    }

    private function _getUserOpenWechatModule($sid, $arrModule)
    {
        $cacheRedis = Cache::getInstance('redis');
        $redisKey   = 'wechat:module' . $sid;
        $expireTime = 3600;
        if (ENV != 'PRODUCTION') {
            $expireTime = 10;
        }
        $res = $cacheRedis->get($redisKey, '', true);
        if ($res === false) {
            $moduleBiz = new Module();
            $res       = $moduleBiz->getUserOpenModuleByModuleId($sid, $arrModule);
            $cacheRedis->set($redisKey, $res, '', $expireTime, true);
        }

        return $res;
    }

    ///**
    // * 新版合作分销商 获取分组列表
    // * <AUTHOR>  Li
    // * @date  2021-03-12
    // */
    //public function queryCooperationEvoluteGroupList()
    //{
    //    $queryCondition = I('post.query_condition', '', 'strval');  //查询信息(名称、账号、手机号)
    //    $pageSize       = I('post.page_size', 10, 'intval');        //分页长度
    //    $pageNumber     = I('post.page', 1, 'intval');              //页码
    //    $groupSize      = I('post.group_size', 10, 'intval');       //分销商分组页数
    //
    //    //获取分销商列表
    //    $evoluteBiz = new \Business\Member\MemberEvoluteRelation();
    //    $result     = $evoluteBiz->queryEvoluteGroupSimpleList($this->_loginMember['sid'], $queryCondition, $pageSize,
    //        $pageNumber, $groupSize);
    //
    //    if ($result['code'] != 200) {
    //        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    //    }
    //
    //    //获取分销商总数 + 供应商总数
    //    $relationModel = new \Model\Member\MemberRelationship;
    //    $resellerRes   = $relationModel->getValidResellers($this->_loginMember['sid'], '', [], 1, 1, true);
    //    $supplierRes   = $relationModel->getValidSuppliers($this->_loginMember['sid'], '', [], 1, 1, true);
    //
    //    $return = [
    //        'list'           => $result['data']['list'],
    //        'pageNum'        => $result['data']['pageNum'],
    //        'pageSize'       => $result['data']['pageSize'],
    //        'total'          => $result['data']['total'],
    //        'reseller_total' => $resellerRes['total'] ?? 0,
    //        'supplier_total' => $supplierRes['total'] ?? 0,
    //    ];
    //
    //    $this->apiReturn($result['code'], $return, $result['msg']);
    //}

    /**
     * 合作分销商-分组列表-分销商列表分页
     * <AUTHOR>  Li
     * @date  2021-03-12
     */
    public function queryEvoluteGroupMemberSimpleListByGroupId()
    {
        $groupId    = I('post.group_id', 0, 'intval');      //分组id
        $pageSize   = I('post.page_size', 10, 'intval');    //分页长度
        $pageNumber = I('post.page', 1, 'intval');          //页码
        $uuid       = I('post.uuid', '', 'strval');          //页码

        if (empty($groupId) || empty($uuid)) {
            $this->apiReturn(203, [], '分组id缺失');
        }

        $evoluteBiz = new \Business\Member\MemberEvoluteRelation();
        $result     = $evoluteBiz->queryEvoluteGroupMemberSimpleListByGroupId($this->_loginMember['sid'], $groupId,
            $uuid,
            $pageSize, $pageNumber);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 合作分销商-分组列表-分销商列表分页
     * <AUTHOR>  Li
     * @date  2021-03-12
     */
    public function queryEvoluteGroupMemberSimpleListByGroupIdAndCondition()
    {
        $queryCondition = I('query_condition', '', 'strval');  //查询信息(名称、账号、手机号)
        $groupId        = I('group_id', 0, 'intval');      //分组id
        $pageSize       = I('page_size', 10, 'intval');    //分页长度
        $pageNumber     = I('page', 1, 'intval');          //页码

        if (empty($groupId)) {
            $this->apiReturn(203, [], '分组id缺失');
        }

        $evoluteBiz = new \Business\Member\MemberEvoluteRelation();
        $result     = $evoluteBiz->queryEvoluteGroupMemberSimpleListByGroupIdAndCondition($groupId,
            $this->_loginMember['sid'], $queryCondition, $pageSize, $pageNumber);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取合作关系审核列表
     * <AUTHOR> Li
     * @date  2021-03-15
     */
    public function findMemberRelationshipApply()
    {
        $type       = I('post.type', 0, 'intval'); //搜索类型  1 企业名称 2 账号 3 手机号
        $identify   = I('post.identify', '', 'strval');
        $inviteType = I('post.invite_type', 0, 'intval'); //邀请类型;1 发送,2 接收
        $applyType  = I('post.apply_type', 0, 'intval'); //申请类型;1 查询添加,2 邀请添加,3 创建添加
        $client     = I('post.client', 0, 'intval'); //操作端: 1 微平台小程序,2 PC,3 微平台H5
        $stat       = I('post.status', 0, 'intval'); //审核状态: 1 待审核,2 已同意,3 已拒绝,-1 已失效
        $sid        = I('post.sid', 0, 'intval'); //搜索供应商id
        $fid        = I('post.fid', 0, 'intval'); //搜索分销商id
        $page       = I('post.page', 1, 'intval'); //当前页数
        $size       = I('post.size', 10, 'intval'); //每页条数

        $comName        = '';
        $cname          = '';
        $ctel           = '';
        $searchMemberId = 0;

        if ($identify) {
            $memberBiz = new \Business\Member\Member();
            switch ($type) {
                case 1:
                    //通过企业名称查询用户id
                    $comName = $identify;
                    break;
                case 2:
                    //通过企业账号查询用户id
                    $memberInfo = $memberBiz->getMemberInfoByMulti([$identify], 'account');
                    if (!$memberInfo) {
                        $this->apiReturn(204, [], '暂无数据');
                    }
                    $searchMemberId = $memberInfo[0]['id'];
                    break;
                case 3:
                    //通过手机号查询用户id
                    $memberInfo = $memberBiz->getMemberInfoByMulti([$identify], 'mobile');
                    if (!$memberInfo) {
                        $this->apiReturn(204, [], '暂无数据');
                    }
                    $searchMemberId = $memberInfo[0]['id'];
                    break;
            }
        }

        if ($client) {
            switch ($client) {
                case 1:
                    $client = '微平台小程序';
                    break;
                case 2:
                    $client = 'PC';
                    break;
                case 3:
                    $client = '微平台H5';
                    break;
            }
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->findMemberRelationshipApply($sid, $fid, $this->_loginMember['sid'], $comName,
            $cname,
            $ctel, $inviteType, $applyType, $client, $stat, 'id', $page, $size, true, $searchMemberId);

        if (isset($result['code']) && $result['code'] == 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(204, [], $result['msg']);
    }

    /**
     * 批量同意或拒绝 合作关系
     * <AUTHOR> Li
     * @date  2021-03-15
     */
    public function checkCreateMemberRelation()
    {
        $auditId = I('post.audit_id'); // 审核i
        $status  = I('post.status', 0, 'intval');   // 审核状态 1 拒绝 2通过

        if (empty($auditId) || empty($status)) {
            $this->apiReturn(400, [], '缺少审核信息');
        }

        if ($status == 2) {
            $pass = true;
        } else {
            $pass = false;
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->checkCreateMemberRelationBatch([$auditId], $this->_loginMember['sid'],
            $pass);

        if (isset($result['code']) && $result['code'] == 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], $result['msg']);
    }

    /**
     * 再次发送邀请
     * <AUTHOR> Li
     * @date   2021-03-15
     */
    public function reSendInviteSms()
    {
        $id = I('post.id');
        if (!$id) {
            $this->apiReturn(204, '', '参数错误');
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->reSendInviteSms($id, $this->_loginMember['sid'],
            $this->_loginMember['sdname'], $this->_loginMember['memberID']);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 通过id mobile 获取审核详情
     * <AUTHOR> Li
     * @date   2021-03-15
     *
     * @return array
     */
    public function getDistributionCheck()
    {
        $id = I('post.id', 0, 'intval');
        if (!$id) {
            $this->apiReturn(204, [], '参数有误');
        }

        $memberRelationBiz = new \Business\Member\DistributorAudit();
        $result            = $memberRelationBiz->getDistributionCheck($id, $this->_loginMember['sid']);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 失效/激活建立组织的供销关系申请
     * <AUTHOR> Li
     * @date  2021-03-15
     */
    public function invalidOrActiveApply()
    {
        $auditId = I('post.audit_id'); // 审核id
        $state   = I('post.status', 0, 'intval');   // 审核状态 1 失效 2 待审核

        if (!$auditId || !$state) {
            $this->apiReturn(400, '', '参数不能为空');
        }

        if (in_array($state, [1, 2])) {
            if ($state == 1) {
                $status = -1;
            } else {
                $status = 1;
            }
        } else {
            $this->apiReturn(400, [], '状态码错误');
        }

        $distributorBiz = new \Business\Member\DistributorAudit();
        $result         = $distributorBiz->invalidOrActiveApply($auditId, $status, $this->_loginMember['sid']);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], $result['msg']);
    }

    /**
     * 新版合作分销商 获取分组列表
     * <AUTHOR>  Li
     * @date  2020-10-30
     */
    public function queryCooperationEvoluteGroupList()
    {
        $queryType  = I('post.query_type', -1, 'intval');       //查询类型 0：分销商名称，1：账号，2：联系人名称，3：手机号，4：分组名称，5：备注名， 6：联系人电话
        $queryValue = I('post.query_value', '', 'strval');      //查询的值
        $province   = I('post.province', -1, 'intval');         //省份id
        $city       = I('post.city', -1, 'intval');             //市id
        $corpKind   = I('post.corp_kind', '',
            'strval');        //公司类型 0:酒店 1:景区 2:旅行社 8:其他 4:集团 5:OTA 6:政府 多个以逗号隔开
        $pageSize   = I('post.page_size', 10, 'intval');        //分页长度
        $pageNumber = I('post.page', 1, 'intval');              //页码
        $groupSize  = I('post.group_size', 1, 'intval');       //分销商分组页数

        $corpKindArr = [];
        if ($corpKind != '' && in_array($corpKind, $this->corpKindMap)) {
            $corpKindArr = explode(',', $corpKind);
        }

        $evoluteBiz = new \Business\Member\MemberEvoluteRelation();
        $result     = $evoluteBiz->queryCooperationEvoluteGroupList($this->_loginMember['sid'], $queryType, $queryValue,
            $province, $city, $corpKindArr, $pageSize, $pageNumber, $groupSize);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 新版合作分销商 分销商列表分页
     * <AUTHOR>  Li
     * @date  2020-10-30
     */
    public function queryEvoluteGroupMemberListByGroupId()
    {
        $groupId    = I('post.group_id', 0, 'intval');      //分组id
        $uuid       = I('post.uuid', '', 'strval');          //页码
        $pageSize   = I('post.page_size', 10, 'intval');    //分页长度
        $pageNumber = I('post.page', 1, 'intval');          //页码

        if (empty($groupId) || empty($uuid)) {
            $this->apiReturn(203, [], '缺少必要参数');
        }
        $evoluteBiz = new \Business\Member\MemberEvoluteRelation();
        $result     = $evoluteBiz->queryEvoluteGroupMemberListByGroupId($this->_loginMember['sid'], $groupId, $uuid,
            $pageSize, $pageNumber);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取当前供应商 对应的分销商总数和供应商总数
     * <AUTHOR>  Li
     * @date  2021-09-23
     */
    public function getTotalInfo()
    {
        //获取分销商总数
        //$relationModel = new \Model\Member\MemberRelationship;
        //$resellerRes   = $relationModel->getValidResellers($this->_loginMember['sid'], '', [], 1, 1, true);
        $relationBiz = new \Business\Member\MemberRelation();
        $resellerRes = $relationBiz->countEvoluteGroupFidList($this->_loginMember['sid']);

        //获取供应商总数
        //$supplierRes   = $relationModel->getValidSuppliers($this->_loginMember['sid'], '', [], 1, 1, true);
        $biz         = new \Business\Member\PartnerSuppliers();
        $supplierRes = $biz->getSuppliersList($this->_loginMember['sid'], '', 1, 1);

        $return = [
            'reseller_total' => $resellerRes['code'] == 200 && !empty($resellerRes['data']) ? $resellerRes['data'] : 0,
            'supplier_total' => $supplierRes['code'] == 200 && !empty($supplierRes['data']['list']) ? $supplierRes['data']['total'] : 0,
        ];

        $this->apiReturn(200, $return, '获取成功');
    }

    /**
     * 合作伙伴 - 模糊查找供应商
     * <AUTHOR>  Li
     * @date  2021-09-24
     *
     * @param  string  $search  关键字查询 [账户名称/姓名/手机号]
     */
    public function searchPartnerInfo()
    {
        // 权限判断
        $auth = $this->checkAuth(['supplyPartner']);
        if (!$auth) {
            $this->apiReturn(201, [], '您没有查看的权限');
        }

        $keyword = I('search', '', 'trim');
        $page    = I('page', 1, 'intval');
        $size    = I('size', 10, 'intval');

        $bus = new \Business\Member\MemberRelation();
        $sid = $this->_loginMember['sid'];
        $res = $bus->searchPartner($sid, $keyword, 'supplier', $page, $size);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $list  = $res['data']['supplier'];
        $total = $res['data']['supplierTotal'];

        $return = [
            'list'  => $list,
            'total' => $total,
        ];

        $this->apiReturn(200, $return);
    }

    /**
     * 获取分销商授信，余额等信息
     * <AUTHOR>  Li
     * @date  2021-09-26
     */
    public function getResellerCredit()
    {
        $did = I('post.did', 0, 'intval');

        if (!$did) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $memberId    = $did;
        $memberModel = new \Business\Member\Member();
        $memberInfo  = $memberModel->getInfo($memberId);
        if (!$memberInfo) {
            $this->apiReturn(204, [], '要查看的用户用户不存在');
        }

        //是否有分销关系
        $relationApi = new \Business\JavaApi\Member\MemberRelationQuery();
        if (!$relationApi->existsDisRelation($this->_loginMember['sid'], $did)) {
            $this->apiReturn(204, [], '无权查看');
        }

        $accountBookApi = new \Business\JavaApi\Account\AccountBook();
        $creditRes      = $accountBookApi->queryCreditBookMany2One($this->_loginMember['sid'], [$did]);

        if ($creditRes['code'] != 200) {
            $this->apiReturn(204, [], $creditRes['msg']);
        }

        $creditInfo = $creditRes['data'][0];

        //获取账户余额
        $balanceRes = $accountBookApi->queryOnePlatformBook($this->_loginMember['sid']);
        if ($balanceRes['code'] == 200) {
            $platformBalance = $balanceRes['data']['money'];
        } else {
            $platformBalance = 0;
        }

        //获取一次分销商备注信息
        $remark    = '';
        $groupInfo = [];

        if ($did) {
            $memberbus  = new MemberRelationshipRemark();
            $remarkInfo = $memberbus->queryDistributorRemarkName($memberId, $this->_loginMember['sid']);
            if ($remarkInfo['code'] == 200 && !empty($remarkInfo['data']['remark'])) {
                $remark = $remarkInfo['data']['remark'];
            }

            // 分销商当前所属的分组信息
            $priceGroupBuz = new \Business\JavaApi\Product\PriceGroup();
            $evoGroupBuz   = new \Business\JavaApi\Product\EvoluteGroup();
            $newGroupRes   = $priceGroupBuz->queryNewGroupIdByFidAndSid($memberId, $this->_loginMember['sid']);
            if ($newGroupRes['code'] == 200 && !empty($newGroupRes['data'])) {
                $groupInfoRes = $evoGroupBuz->queryByGroupId($newGroupRes['data']);
                if ($groupInfoRes['code'] == 200 && !empty($groupInfoRes['data'])) {
                    $groupInfo = [
                        'group_id'   => $groupInfoRes['data']['id'],
                        'group_name' => $groupInfoRes['data']['groupName'],
                    ];
                }
            }
        }

        $memberInfo = [
            'dname'     => $memberInfo['dname'],
            'cname'     => $memberInfo['cname'],
            'mobile'    => $memberInfo['mobile'],
            'headphoto' => $memberInfo['headphoto'],
            'id'        => $memberInfo['id'],
            'account'   => $memberInfo['account'],
            'balance'   => $platformBalance,
            'remark'    => $remark,
        ];

        //已用额度
        $usedLimit = $creditInfo['surplus_money'] < 0 ? -$creditInfo['surplus_money'] : 0;

        $returnData = [
            'loginInfo' => [
                'dname'   => $this->_loginMember['dname'],
                'account' => $this->_loginMember['account'],
            ],
            'member'    => $memberInfo,
            'credit'    => [
                'remain_limit'    => $creditInfo['credit_line'] - $usedLimit,
                'used_limit'      => $usedLimit,
                'total_limit'     => $creditInfo['credit_line'] ?? 0,
                'remain_money'    => $creditInfo['surplus_money'] + $creditInfo['credit_line'],
                'repayment_money' => $creditInfo['surplus_money'] > 0 ? $creditInfo['surplus_money'] : 0,
                'cmode'           => intval((int)$creditInfo['credit_line'] == ***********),
            ],
            'group'     => $groupInfo,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $returnData, 'success');
    }

    /**
     * 判断是否是领导账号
     * <AUTHOR>
     * @date 2021/12/6
     */
    public function checkViewChartAuth()
    {
        $this->isLogin('ajax');
        $auth = false;
        //微平台领导看数据初拉力
        $microPlatReportAccount = load_config('micro_plat_report_account_test', 'account'); // 测试环境账号
        if (ENV == 'PRODUCTION') {
            $microPlatReportAccount = load_config('micro_plat_report_account', 'account');
        }
        //当前用户账号或者主账号只要有一个在，则返回true
        $memberId = $this->_loginMember['memberID'];
        if (in_array($memberId, $microPlatReportAccount)) {
            $auth = true;
        }

        $this->apiReturn(200, ['chart_view_account' => $auth]);
    }

}
