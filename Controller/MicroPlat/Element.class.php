<?php
/**
 * 权限页面元素
 * <AUTHOR>
 * @time   2017-07-20
 */
namespace Controller\MicroPlat;

use Business\Authority\AuthContext;
use Business\Authority\AuthLogic as AuthLogicBiz;
use Controller\MicroPlat\Common as Common;

class Element extends Common
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取三级元素，  三级元素可能是按钮也可能是tab
     * <AUTHOR>
     * @date 2019-06-11
     */
    public function resource()
    {
        // 渠道ID
        $channel    = I('post.channel_id/d', 1);
        // menu tag
        $menuTag    = I('post.menu_tag/s');
        // 用户ID
        $memberId = $this->_loginMember['memberID'];

        $menuType = I('post.menu_type/d',0);

        //如果是新版权限体验用户，可以直接使用新版权限中心鉴权
        $res = (new AuthLogicBiz())->resource($this->_loginMember['sid'], $memberId, $menuTag, $this->_loginMember['dtype'], $menuType);

        $this->apiReturn(200, $res, '');
    }

    /**
     * 获取四级元素， 也就是tab页对应的按钮
     * <AUTHOR>
     * @date 2021-07-15
     */
    public function elementResource()
    {
        // 渠道ID
        $channel    = I('post.channel_id/d', 1);
        // element tag
        $elementTag = I('post.element_tag/s');
        // 用户ID
        $memberId = $this->_loginMember['memberID'];

        //如果是新版权限体验用户，可以直接使用新版权限中心鉴权
        $res = (new AuthLogicBiz())->elementResource($this->_loginMember['sid'], $memberId, $elementTag, $this->_loginMember['dtype']);

        $this->apiReturn(200, $res, '');
    }

}