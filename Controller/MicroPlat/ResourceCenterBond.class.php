<?php
/**
 * 微平台资源中心保证金
 *
 * <AUTHOR>
 * @time   2020-8-6
 *
 */
namespace Controller\MicroPlat;

use Business\ResourceCenter\Bond;
use Controller\MicroPlat\Common as Common;


class ResourceCenterBond extends Common
{

    /**
    * 微商城资源中心保证金订单充值
    * <AUTHOR>
    * @date   2020-08-04
    *
    * @return array
    */
    public function resourceBondRecharge()
    {
        $returnUrl = I('success_url', '' , 'strval,trim');
        if (!$returnUrl) {
            $this->apiReturn(203, [], '参数错误');
        }
        if (!$this->inWechatApp()) {
            $this->apiReturn(400, [], '请在微信客户端打开');
        }
        // 判断是否在未获授权的微信中 - 微信支付前需要获得授权
        if (!$this->_loginMember['pft_openid']) {
            if (ENV == 'PRODUCTION') {
                $url = $this->getMicroPlatHost() . 'r/MicroPlat_Member/pftWxAuth?from=bond&h=recharge_bond';
                $this->apiReturn(206, ['url' => $url]);
            }else{
                $this->apiReturn(400, [], '测试环境无法微信获得授权');
            }
        }

        $result = (new Bond())->resourceBondRecharge($this->_loginMember['memberID'], $this->_loginMember['pft_openid'], $returnUrl);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

}
