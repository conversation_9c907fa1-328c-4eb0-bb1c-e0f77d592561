<?php
/**
 * 公用类
 *
 * <AUTHOR>
 * @time   2017-01-10
 */
namespace Controller\MicroPlat;

use Library\Controller as Controller;
use Model\Product\Ticket as Ticket;
use Model\Member\Member as Member;
use Model\Member\MemberRelationship as MemberRelationship;
use Model\Subdomain\SubdomainInfo as SubdomainInfo;
use \LaneWeChat\Core\OpenWeChatOAuth as OpenWeChatOAuth;
use Library\Tools\Helpers as Helpers;
use Library\Cache\Cache as Cache;

class Common  extends Controller
{
    const __ORDER_MODE__        = 19;           // 订单标识
    const __DEFAULT_ACCOUNT     = 124078;       // 票付通账号
    const __DEFAULT_MEMBERID    = 22647;        // 票付通ID
    const __PLAT_IDENTIFY__     = 'micro_plat'; // 平台标识

    const __DEFAULT_SY_ACCOUNT     = 100005;    // 三亚账号
    const __DEFAULT_SY_MEMBERID    = 55;        // 三亚ID

    protected $_supplyId        = 0;       // 供应商id
    protected $_supplyAccount   = 0;       // 供应商账号
    protected $_supplyAppId     = false;   // 供应商AppId
    protected $_loginMember     = [];      // 登录用户的信息

    protected $_channel         = 10;       // 产品渠道设定 默认微信渠道1  修改为 默认后台产品预订5
    protected $_sortByMemberID  = 0;       // 指定产品排序来源的用户ID，0当前登录用户排序
    protected $_queryString     = [];      // 微信公众号跳转参数列表
    protected $_mustLogin       = 1;       // 是否需要登录 1必须登录 0无须登录
    protected $_inPFTApp        = true;    // 是否在票付通公众号中
    protected $_clearCache      = false;   // 是否清空缓存 true清除缓存
    protected $saleChannel      = null;        //售检渠道

    public function __construct()
    {

        if (!$this->_auth()) {
            $this->apiReturn(401, [], '页面登录过期，请刷新页面');
        }

        $this->_supplyAccount = $this->parseSupplyAccount();
        if (!$this->_supplyAccount) {
            exit('链接有误, 无法获取供应商!');
        }

        $this->_inPFTApp     = $this->inPFTApp($this->_supplyAccount);
        $this->_supplyId     = $this->parseSupplyMemberId();
        if ($this->inWechatApp()) {
            $this->_supplyAppId  = $this->parseSupplyAppId();
        }
        $this->saleChannel = I('saleChannel', 10, 'intval');

        $this->getLoginInfo();

        //访问限制
        if (strtolower(I('get.a')) != 'unbindlogout' && $this->_loginMember['sid'] && !in_array($this->_loginMember['sdtype'], [1, 10])) {
            [$limitCode, $limitMsg] = $this->_isAllowAccess($this->_loginMember['saccount'], $this->_loginMember['sdtype'], $this->_loginMember['sid']);
            if ($limitCode != 200) {
                $this->apiReturn($limitCode, [], $limitMsg);
            }
        }

        $this->getPlatParmas();
        $this->clearCache();
    }

    /**
     * 统一的数据返回
     * <AUTHOR>
     * @date   2017-02-14
     *
     * @param  string  $code  业务码
     * @param  string  $msg  相关信息
     * @param  array  $data  数组数据
     *
     * @return
     */
    public function returnData($code, $msg = '', $data = [])
    {
        return ['code' => $code, 'msg' => $msg, 'data' => $data];
    }

    /**
     * 微平台访问限制
     *
     * <AUTHOR>
     * @time   2020-08-14
     *
     * @param  string   $account    登录账户
     * @param  integer  $sdType     主账号类型
     * @param  integer  $sid        登录主账号id,
     *
     * @return array
     */
    private function _isAllowAccess($sAccount, $sdType, $sid)
    {
        if ($sdType == 2) {//资源账号访问

            $accountKey = "land_account:applydid:{$sAccount}";
            $redis      = $this->getRedisObject();
            $applyDid   = $redis->get($accountKey);

            if (!$applyDid) {
                //资源账号
                //$landInfo = (new \Model\Product\Land())->getInfoBySalerId($sAccount, 'apply_did');

                $landApi  = new \Business\CommodityCenter\Land();
                $landInfoRes = $landApi->queryLandMultiQueryByAdminAndPaging([], 1, 1, '', 'id desc', false, [], [], [], [],
                    [$sAccount]);
                if (!$landInfoRes['list']) {
                    return [404, '无该账号信息'];
                }
                $landInfo = $landInfoRes['list'][0];

                //供应商id
                $applyDid = isset($landInfo['apply_did']) ? $landInfo['apply_did'] : 0;
                if (!$applyDid) {
                    return [404, '无供应商信息'];
                }

                //设置缓存
                $redis->set($accountKey, $applyDid, 1800);
            }

            $hasUsePackage = (new \Business\AppCenter\Package())->getUsePackage($applyDid);
        } else {
            //分销商供应商账号
            $hasUsePackage = (new \Business\AppCenter\Package())->getUsePackage($sid);
        }

        if (empty($hasUsePackage)) {
            return [403, '当前账号无有效套餐，请联系客服'];
        }

        return [200, ''];
    }



    /**
     * 各种来源的登录信息集合
     * <AUTHOR>
     * @time   2017-01-22
     */
    public function getLoginInfo($type = 'auto', $isGetIcon = false, $ischeckLogin = true)
    {
        $loginMember = is_array($this->_loginMember) ? $this->_loginMember : [];
        $session     = is_array($_SESSION) ? $_SESSION : [];
        if (!empty($session['memberID'])) {
            $loginInfoCacheKey = sprintf(\Library\Controller::LOGIN_INFO_CACHE_KEY, $session['memberID']);
            $cache = \Library\Cache\Cache::getInstance('redis');
            $loginInfo = $cache->hGetAll($loginInfoCacheKey);

            $this->_loginMember = array_merge($loginInfo, $loginMember,$session);
        }else{
            $this->_loginMember = [];
        }

        if ($type == 'html') {
            return $this->_loginMember;
        }
        return true;
    }

    /**
     * 获取微平台Host
     * <AUTHOR>
     * @time   2017-01-22
     */
    protected function getMicroPlatHost()
    {
        return str_replace('wx', $this->_supplyAccount, MOBILE_DOMAIN);
    }

    /**
     * 获取要搜索产品的用户ID
     * 特殊：某些公众号的URL指定了销售渠道的参数ctype(表示允许不登录)，则查找当前公众号账户ID的产品
     * <AUTHOR>
     * @time   2017-02-22
     *
     * @return int|null
     */
    public function getMemberId()
    {
        if (!$this->_mustLogin && !$this->_loginMember['memberID']) {
            // 允许不登录
            $memberId = $this->_supplyId;

        } elseif ($this->isSanKeLogin()) {
            // 散客账号
            $memberId = $this->_loginMember['memberID'];

        }  elseif ($this->isSonLogin()) {
            // 员工账号
            $memberId = $this->_loginMember['sid'];

        } else {
            // 分销商或者供应商账号
            $memberId = $this->_loginMember['memberID'];
        }

        return $memberId ? : null;
    }

    /**
     * 是否登录
     * <AUTHOR>
     * @time   2017-03-21
     * 
     * return true=散客|false=非散客或未登录
     */
    protected function isSanKeLogin()
    {
        return $this->isSanKeAccount(
                $this->_loginMember['dtype'],
                $this->_loginMember['mobile'],
                $this->_loginMember['account']
            );
    }

    /**
     * 是否登录状态
     * <AUTHOR>
     * @time   2017-08-02
     * 
     * return 已登陆返回sid
     */
    public function isLogin($type = 'auto', $isAutoHandle = true)
    {
        if (!$this->_loginMember['sid']) {
            // return false;
            $this->apiReturn(207, [], '身份未知, 请先登录');
        }
        return $this->_loginMember['sid'];
    }

    /**
     * 是否散客账号
     * <AUTHOR>
     * @time   2017-07-19
     * 
     * return true=散客|false=非散客或未登录
     */
    protected function isSanKeAccount($dtype = 0, $mobile = 0 , $account = 0)
    {
        if ($dtype == 5 || ($dtype == 1 && $mobile == $account)){
            return true;
        }
        return false;
    }

    /**
     * 是否员工账号登录
     * <AUTHOR>
     * @time   2017-01-22
     */
    protected function isSonLogin()
    {
        if (!$this->_loginMember['memberID']) {
            // 未登录
            // $this->apiReturn(207, [], '身份未知, 请先登录');
            $this->returnAutoLogin('请先登录');
        }
        return $this->_loginMember['memberID'] != $this->_loginMember['sid'];
    }

    /**
     * 返回自动登录的链接给前端
     * <AUTHOR>
     * @time   2017-07-22
     */
    protected function returnAutoLogin($msg = '')
    {
        // 新登录
        $url = str_replace('wx', $this->_supplyAccount, MOBILE_DOMAIN).'r/Member_Login/autoLogin?plat=b';
        $this->apiReturn(207, ['url'=>$url], $msg);

        // 旧登录
        // $this->apiReturn(207, [], $msg);
    }

    /**
     * 员工权限
     * <AUTHOR>
     * @time   2017-01-22
     *
     * @param  string $return 验证权限通过后的返回方式 默认ajax返回 ['ajax'|'bool']
     */
    protected function checkSonAuth($return = 'ajax')
    {
        if (!in_array($return, ['ajax', 'bool'])) {
            $this->apiReturn(205, [], '权限判断参数有误');
        }

        $mModel = $this->getMemberModel('master');

        // 员工账号有效性检测
        $res = $mModel->getStaffBySonId($this->_loginMember['memberID']);
        if (!$res) {
            $this->apiReturn(205, [], '员工账号已失效或不存在!');
        } else {
            $this->setLoginInfo('sid', $res['parent_id']);
        }

        // 员工账号权限判断
        $son = $mModel->getMemberInfo($this->_loginMember['memberID']);
        if (!$son) {
            // 员工账号不存在
            $this->returnAutoLogin('请先登录');
        }
        if ($son['status'] == 1) {
            // 员工账号已禁用
            $this->apiReturn(205, [], '员工账号已禁用');
        }

        if (!in_array('pro', explode(',', $son['member_auth']))) {
            // 当前员工账号 没有产品预订权限
            $this->apiReturn(205, [], '该账号无购买权限');
        }

        if ($return == 'ajax') {
            $this->apiReturn(200, []);
        } else {
            return true;
        }
    }

    /**
     * 所有渠道的登录参数增加
     * <AUTHOR>
     * @time   2017-01-22
     * 
     * @param string $field 参数名
     * @param string $value 参数值
     */
    protected function setLoginInfo($field = '', $value = '')
    {
        $_SESSION[$field] = $value;
        $this->_loginMember[$field] = $value;
        $this->getLoginInfo();
    }

    /**
     * 清除所有渠道的登录信息
     * <AUTHOR>
     * @time   2017-01-22
     */
    protected function clearLoginInfo()
    {
        $tmp = $_SESSION;
        session_destroy();
        unset($_SESSION);

        $_SESSION['wechat_appid'] = $tmp['wechat_appid'];
        $_SESSION['openid']       = $tmp['openid'];

        $this->_loginMember = [];
        return true;
    }

    /**
     * 是否在微信app内
     * <AUTHOR>
     * @time   2017-01-22
     */
    protected function inWechatApp()
    {
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        $pos = strpos($user_agent, 'MicroMessenger');

        // if (ENV == 'PRODUCTION') {
            return $pos === false ? false : true;
        // } else {
        //     return true;
        // }       
    }

    /**
     * 获取不同平台参数
     * <AUTHOR>
     * @time   2017-01-22
     */
    protected function getPlatParmas()
    {
        // 其他公众号 - 用于兼容第三方公众号的旧版产品预订奇葩参数
        if (!$this->_inPFTApp) {

            // URL解析
            $temp = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_QUERY);
            $temp = explode('&', $temp);
            foreach ($temp as $value) {
                [$key, $val] = explode('=', $value);
                $this->_queryString[$key] = $val;
            }

            // 参数暂存
            $this->_queryString = array_merge($this->_queryString, I(''));
            unset($this->_queryString['token']); 
            unset($this->_queryString['c']); 
            unset($this->_queryString['a']);
            unset($this->_queryString['m']);
            if ($this->_supplyAccount == 100005) {
                $this->_queryString['ctx'] = 55;
            }
            // 参数筛选
            $ctype = $this->_queryString['ctype']; // 指定查询渠道 [1微信, 4会员卡]
            $ctx   = $this->_queryString['ctx'];   // 指定排序来源 [0当前登录用户ID, 非0为指定的用户Id]

            // 指定销售渠道
            if ($ctype && in_array($ctype, [1,4])) {
                $this->_channel   = $ctype;
                // $this->_mustLogin = 0; // 查询指定渠道, 允许无须登录
            }

            // 指定排序
            if ($ctx) {
                $this->_sortByMemberID = $ctx;
            }
        }
    }

    /**
     * 是否在票付通公众号内
     * <AUTHOR>
     * @time   2017-01-22
     */
    protected function inPFTApp($value, $type = 'supperAccount')
    {
        switch ($type) {
            case 'supperAccount': 
                return self::__DEFAULT_ACCOUNT == $value;

            case 'supperAppId': 
                return PFT_WECHAT_APPID == $value;

            case 'supperId': 
                return self::__DEFAULT_MEMBERID == $value;

            default:
                exit('代码有误！'); // 正常使用不会报错
        }
    }

    /**
     * 页面跳转
     * <AUTHOR>
     * @time   2017-01-23
     *
     * @param  int  $supplyAccount  供应商账号
     * @param  string  $redirect  预设地址
     * [
     *     mall ：微商城首页 [默认]
     *     microLogin  ：微平台登录页
     *     microPList  ：微平台产品预订页
     *     microChoose ：中转页
     *     microNoAuth ：微平台无预订权限的警告页
     * ]
     * @param  string  $url  指定优先的urL [必须是完整的URL，如http://www..]
     * @param  array  $param  get参数数组   [例如:['ctx'=>0,'ctype'=>1,....]]
     *
     * @return 跳转页面
     */
    protected function _redirect($supplyAccount = 'wx', $redirect = 'mall', $url = null, $params = [])
    {
        if (!is_null($url)) {
            header("Location:$url");
            exit();
        }

        $microDomain = str_replace('wx', 'm', MOBILE_DOMAIN);
        $domain      = str_replace('wx', $supplyAccount, MOBILE_DOMAIN);

        switch ($redirect) {

            case 'microLogin':
                $prefixUrl = $microDomain . 'wx/b/login.html'; // 微平台登录页
                break;
            case 'microPList':
                $prefixUrl = $microDomain . 'wx/b/plist.html'; // 微平台产品预订页
                break;
            case 'microChoose':
                $prefixUrl = $microDomain . 'wx/b/transit.html'; // 微平台登录中转页
                break;
            case 'microNoAuth':
                $prefixUrl = $microDomain . 'wx/b/noauth.html'; // 微平台无预订权限的警告页
                break;
            case 'microUsercenter':
                $prefixUrl = $microDomain . 'wx/b/usercenter.html'; // 微平台个人中心
                break;
            case 'microUserInfo':
                $prefixUrl = $microDomain . 'wx/b/setting.html'; // 微平台用户信息
                break;
            case 'mall': // no break
            default:
                $prefixUrl = $domain . 'wx/c/index.html'; // 微商城首页
                break;
        }

        $params = array_filter($params);

        $param = '';
        if ($params && is_array($params)) {
            $param = '?' . http_build_query($params);
        }

        header('Location:' . $prefixUrl . $param);
        exit();
    }

    /**
     * 跳转手动登录页面
     * <AUTHOR>
     * @time   2017-03-22
     */
    protected function redirectHandLogin($supplyAccount, $param = [])
    {
        // 带有noAuto标志的登录URL，采用手动登录方式
        $param['noAuto'] = 1; // 手动登录标志

        if (isset($param['ptypeS']) && $param['ptypeS']) {
            $url = 'http://100005.12301.cc/wx/b/plist.html?type=' . $param['ptypeS'];
        } else if ($param['redit']){
            $domain = str_replace('wx.', $supplyAccount.'.', MOBILE_DOMAIN);
            $url = $domain.$param['redit'];
        } else {
            $url = null;
        }

        $this->_redirect($supplyAccount, 'microLogin', $url, $param);
    }

    /**
     * 解析当前链接所属供应商的Appid
     * <AUTHOR>
     * @time   2017-01-22
     */
    protected function parseSupplyAppId(bool $isQrCode = false)
    {
        $account = $this->parseSupplyAccount();
        if ($isQrCode) {
            $host_info = explode('.', $_SERVER['HTTP_HOST']);
            if ($host_info[0] == 'm') {
                $account = $this->_loginMember['saccount'];
            }
        }

        if ($account == SELF::__DEFAULT_ACCOUNT) {
            return PFT_WECHAT_APPID;
        }

        if (!$this->_loginMember['wechat_appid']) {
            $wxOpenModel = new \Model\Wechat\WxOpen();
            $wxInfo      = $wxOpenModel->getWechatOffiAccInfo($account, 'account');
            $appid       = $wxInfo['appid'];
        } else {
            $appid = $this->_loginMember['wechat_appid'];
        }

        return $appid ?: false;
    }

    /**
     * 解析当前链接所属供应商的ID
     */
    protected function parseSupplyMemberId(bool $isQrCode = false) {

        $account = $this->parseSupplyAccount();
        if ($isQrCode) {
            $host_info = explode('.', $_SERVER['HTTP_HOST']);
            if ($host_info[0] == 'm') {
                $account = $this->_loginMember['saccount'];
            }
        }

        if ($account == SELF::__DEFAULT_ACCOUNT) {
            return SELF::__DEFAULT_MEMBERID;
        }

        //三亚走特殊处理
        if ($account == SELF::__DEFAULT_SY_ACCOUNT) {
            return SELF::__DEFAULT_SY_MEMBERID;
        }

        $member = $this->getMemberModel()->getMemberInfo($account, 'account');

        return $member['id'] ? : 0;
    }

    /**
     * 解析当前链接所属供应商的账号
     */
    protected function  parseSupplyAccount()
    {
        // 商户微信入口
        if (I('account', 0, 'intval')) {
            return \safetxt(I('account'));
        }

        // 授权账号
        if (I('code')) {
            $params = json_decode(base64_decode(I('state')), true);
            return $params['supplyAccount'];
        }

        $host_info = explode('.', $_SERVER['HTTP_HOST']);

        if (in_array($host_info[0], ['wx', SELF::__DEFAULT_ACCOUNT, 'm'])) {
            return SELF::__DEFAULT_ACCOUNT;
        }

        return \safetxt($host_info[0]);
    }

    /**
     * 账户类型校验, 跳转相应页面
     * 类型 商户账号 用户权限 更多参数
     */
    protected function _checkMemberType($dtype = null, $supplyAccount = null, $params = [])
    {
        // 类型合法性校验
        if (!(is_numeric($dtype) && $dtype >= 0)) {
            // 非法类型 - 手动登录
            $this->redirectHandLogin($supplyAccount);
        }

        // 判断登陆账号的类型
        if ($this->isSanKeLogin()) {
            // 散客
            $this->_redirect('m', 'microChoose', null, $params);

        }elseif ( $params['redit'] == 'wx/b/setting.html'){
            $this->_redirect($supplyAccount, 'microUserInfo');
        }elseif (in_array($dtype, [0,1])) {
            // 分销商/供应商
            // $this->_redirect($supplyAccount, 'microPList', null, $params);
            $this->_redirect($supplyAccount, 'microUsercenter', null, $params);

        } elseif ($dtype == 6) {

            // 员工账号有效性检测
            $res = $this->getMemberModel()
                        ->getStaffBySonId($this->_loginMember['memberID']);
            if (!$res) {  
                $this->_redirect($supplyAccount, 'microNoAuth', null, $params);
            } else {
                $this->setLoginInfo('sid', $res['parent_id']);
            }

            // 员工账号权限判断
            $son = $this->getMemberModel()
                           ->getMemberInfo($this->_loginMember['memberID']);
            if (!$son) {
                // 员工账号不存在
                $this->_redirect($supplyAccount, 'microNoAuth', null, $params);
            }

            if (!in_array('pro', explode(',', $son['member_auth']))) {
                // 当前员工账号 没有产品预订权限
                $this->_redirect($supplyAccount, 'microNoAuth', null, $params);
            }

            // $this->_redirect($supplyAccount, 'microPList', null, $params);
            $this->_redirect($supplyAccount, 'microUsercenter', null, $params);

        } else {
            // 手动登录页
            $this->redirectHandLogin($supplyAccount);
        }
    }

    /**
     * 指定分销商和供应商的授信信息
     * <AUTHOR>
     * @time   2017-08-01
     *
     * @param   int  sid  分销商ID
     * @param   int  aid  供应商ID
     */
    protected function getCreditInfo($sid = 0, $aid = 0)
    {
        if (!$sid || !$aid) {
            return false;
        }

        $return = [];

        $mModel = $this->getMemberModel();
        $cmoney = $mModel->getMoney($sid, 4, $aid);
        if ($cmoney && is_array($cmoney)) {
            $cmoney = array_pop($cmoney);
            if ($cmoney['mode'] == 1) {
                $return = [
                    'mode'       => 1,                      // 模式: 不限额度
                    'kmoney'     => $cmoney['kmoney']       // 信用余额 -分
                ];
            } else {
                $credit = floatval(($cmoney['kmoney'] + $cmoney['basecredit']) / 100);
                $return = [
                    'mode'       => 0,                      // 模式: 固定额度
                    'kmoney'     => $cmoney['kmoney'],      // 信用余额 -分
                    'basecredit' => $cmoney['basecredit'],  // 信用额度 -分
                    'credit'     => $credit                 // 可用额度 -元
                ];
            }
        }

        return $return;
    }

    /**
     * 获得 Ticket 模型实例
     * <AUTHOR>
     * @time   2017-02-20
     *
     * @param string $db 数据库标志 [主库localhost|从库slave]
     * @return Ticket
     */
    protected function getTicketModel($db = 'slave')
    {
        $modelName = $db . 'TicketModel';

        if (!$this->$modelName) {
            if (in_array($db, ['slave'])){
                $this->$modelName = new Ticket($db);
            } else {
                $this->$modelName = new \Model\Product\Ticket();
            }
        }
        return $this->$modelName;
    }

    /**
     * 获得 Member 模型实例
     * <AUTHOR>
     * @time   2017-02-20
     *
     * @param string $db 数据库标志 [主库localhost|从库slave]
     * @return Member
     */
    protected function getMemberModel($db = 'slave')
    {
        $modelName = $db . 'MemberModel';

        if (!$this->$modelName) {
            if (in_array($db, ['slave'])){
                $this->$modelName = new Member($db);
            } else {
                $this->$modelName = new \Model\Member\Member();
            }
        }

        return $this->$modelName;
    }

    /**
     * 获得 MemberRelationship 模型实例
     *
     * <AUTHOR>
     * @time   2017-02-20
     */
    protected function getMemberRelationShipModel()
    {
        static $mShipModel;
        if (is_null($mShipModel)) {
            $mShipModel = new \Model\Member\MemberRelationship();
        }
        return $mShipModel;
    }

    /**
     * 获得redis实例
     *
     * <AUTHOR>
     * @time   2017-02-20
     */
    protected function getRedisObject()
    {
        static $redis;
        if (!$redis) {
            $redis = Cache::getInstance('redis');
        }
        return $redis;
    }

    /**
     * 是否清空缓存
     * true清除缓存
     *
     * <AUTHOR>
     * @time   2017-03-20
     */
    protected function clearCache()
    {
        $query = $_SERVER['HTTP_REFERER'];
        if (false !== stripos($query, 'clearCache')) {
            $this->_clearCache = true;
        }
        return true;
    }

    /**
     * 微信授权回调后，获取 accesstoken 和 openid 信息
     * @param  [type] $appid 公众号appid
     * @param  [type] $code  微信回调所带的参数
     * @return [type]        [description]
     */
    protected function _getAccessTokenAndOpenId($appid, $code)
    {
        include BASE_WX_DIR . '/wechat/open_wechat.php';

        $result = OpenWeChatOAuth::getAccessTokenAndOpenId($appid, $code);

        if (isset($result['errcode'])) {
            return false;
        }

        return [
            'accessToken'   => $result['access_token'],
            'openid'        => $result['openid']
        ];
    }

    /**
     * 员工权限校验
     * <AUTHOR>
     * @time   2017-06-23
     *
     * @param  array $authArr 员工权限标志数组 ['supplyPartner','fxPartner']
     * @return array 员工账号返回$authArr中有权限的['fxPartner'=>1, ...]
     */
    protected function checkAuth($authArr = [])
    {
        $fid   = $this->_loginMember['memberID'];
        $dtype = $this->_loginMember['dtype'];
        $return = [];

        if (!$authArr || !is_array($authArr)) {
            return $return;
        }

        if ($dtype == 6) {
            $allowAuth = $this->getMemberModel()->getMemberInfo($fid, 'id', 'member_auth');
            if ($allowAuth) {
                $allowAuth = explode(',', $allowAuth['member_auth']);
                foreach ($authArr as $auth) {
                    if (in_array($auth, $allowAuth)) {
                        $return[$auth] = 1; // 1=有权限
                    }
                }
            }
        } else {
            foreach ($authArr as $auth) {
                $return[$auth] = 1;
            }
        }

        return $return;
    }

    /**
     * 账号类型判断
     * <AUTHOR>
     * @time   2017-08-03
     * 
     * @param  int      $dtype    账户类型
     * @param  int      $mobile   手机号
     * @param  string   $account  账号
     * @return -1类型错误|5散客|账号类型
     */
    protected function getMemberType($dtype = 0, $mobile = 0, $account = 0)
    {
        if (!ismobile($mobile) || !$account) {
            return -1;
        }

        if (!is_numeric($dtype) || $dtype < 0) {
            return -1;
        } 

        // 散客
        $isSanke = $this->isSanKeAccount($dtype, $mobile, $account);
        if ($isSanke) {
            return 5;
        }

        // 其他类型
        return $dtype;
    }

    /**
     * 解析常用联系人字段
     * <AUTHOR>
     * @time   2017-02-15
     *
     * @param string $linkmanField 数据库中联系人字段
     * 例如 ；'赵大:***********:|钱二:***********:35010519770402473X'
     * @return array 解析后的数组 
     * 例如 ；[['name'=>'赵大','tel'=>***********,'idcard'=>''],....]
     */
    protected function parseLinkman($linkmanField = '')
    {
        if (!$linkmanField || !is_string($linkmanField)) {
            return [];
        }

        $linkmanArr = explode('|', $linkmanField);
        foreach ($linkmanArr as $key => $linkman) {
            [$name, $tel, $idcard, $province, $city, $town, $detailAddr] = explode(':', $linkman);
            $info[$key]['name']        = $name;
            $info[$key]['tel']         = $tel;
            $info[$key]['idcard']      = $idcard;
            $info[$key]['province']    = $province;
            $info[$key]['city']        = $city;
            $info[$key]['town']        = $town;
            $info[$key]['detail_addr'] = $detailAddr;
        }

        return $info ?: [];
    }

    /**
     * 无需验证的接口
     *
     * <AUTHOR>
     * @time   2017-01-10
     */
    private function _whiteInterface()
    {
        return [
            'c=MicroPlat_Member&a=checkMemberType',
            'c=MicroPlat_Member&a=friendWx',
            'c=MicroPlat_Member&a=wxAutoLogin',
            'c=MicroPlat_Member&a=unBindLogout',
            'c=MicroPlat_Member&a=logout',
            'c=MicroPlat_Member&a=wxOrderAuth',
            'c=MicroPlat_Member&a=getImgCode',
            'c=MicroPlat_Member&a=autoLoginEdit'
        ];
    }

    /**
     * 页面令牌验证
     *
     * <AUTHOR>
     * @time   2017-01-10
     */
    private function _auth()
    {
        return true;
    }

    /**
     * 分销专员用户ID记为运营商
     * <AUTHOR>
     * @date 2020/6/24
     *
     * @param string $disId 分销专员唯一标识
     * @param bool $isCache 是否需要走缓存 true=是 false=否
     *
     * @return array
     */
    public function updateOrderMemberNew(string $disId, bool $isCache = false)
    {
        $this->getLoginInfo();
        $uuid = isset($this->_loginMember['multi_dist']['uuid']) ?? '';
        $multiDistBiz = new \Business\MultiDist\Member($this->_loginMember['sid'], $uuid);
        if ($isCache) {
            $topUid = $multiDistBiz->getTopUidCache($disId);
        } else {
            $topUid = $multiDistBiz->getTopUid($disId);
        }

        return $topUid;
    }
 }