<?php
/**
 * 微平台分销专员
 * <AUTHOR>
 * @date 2020/6/15
 */

namespace Controller\MicroPlat;

use Business\MultiDist\Member as MultiDistMember;
use Business\MultiDist\Statis;
use Business\Site\HelpCenter;
use Library\Tools;
use Model\MultiDist\Invite;
use Model\MultiDist\Member as MultiMemberModel;
use Business\MultiDist\Invite as InviteBus;
use \Business\MultiDist\Product as DistProductBus;
use Process\Business\Business;

class MultiDist extends Common
{
    private $uuid        = null;//唯一标识
    private $currentRole = null;//角色 1=运营商 2=分销专员
    private $level       = null;//层级

    public function __construct()
    {
        parent::__construct();
        $memberId = $this->getMemberId();
        if (empty($memberId)) {
            $this->returnAutoLogin('请先登录');
        }
        $multiDist = (new MultiDistMember())->getMultiDistCache($memberId);
        if (empty($multiDist['multi_dist'])) {
            $this->apiReturn(400, [], '无分销专员相关权限!');
        }

        $seesionMultiDist = $multiDist['multi_dist'];

        $this->uuid        = $seesionMultiDist['uuid'];
        $this->currentRole = $seesionMultiDist['role'];
        $this->level       = $seesionMultiDist['level'];
    }

    /**
     * 获取加入的分销专员团队
     * <AUTHOR>
     * @date    2020/6/18
     *
     * @return array
     */
    public function roleList()
    {
        $uid            = $this->_loginMember['sid'];
        $uuid           = $this->uuid;
        $multiMemberBuz = new \Business\MultiDist\Member();
        $roleRes        = $multiMemberBuz->roleList($uid, $uuid, $this->_loginMember['dname']);
        if ($roleRes['code'] != 200) {
            $this->apiReturn($roleRes['code'], [], $roleRes['msg']);
        }

        $this->apiReturn(200, $roleRes['data'], 'msg');
    }

    /**
     * 所属角色切换，session更新
     * <AUTHOR>
     * @date    2020/04/02
     *
     * @return array
     */
    public function roleChange()
    {
        $uuid = I('post.uuid', '', 'string');
        $role = I('post.role', 0, 'int');

        $uid   = $this->_loginMember['sid'];
        $level = -1;

        if (empty($uuid) || empty($role)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        if (!in_array($role, [\Business\MultiDist\Base::TOP_ROLE, \Business\MultiDist\Base::NOMAL_ROLE])) {
            $this->apiReturn(203, [], '非法参数');
        }

        //判断uuid有效性
        $roleList = (new MultiMemberModel())->getAllHigherLevelList($uid);
        if (!$roleList) {
            $this->apiReturn(404, [], '无效的uuid');
        }

        foreach ($roleList as $tmp) {
            if ($tmp['uuid'] == $uuid) {
                $level = $tmp['level'];
            }
        }

        if ($level == -1) {
            $this->apiReturn(404, [], '无效的uuid');
        }

        (new \Business\MultiDist\Member())->setMultiDistSession($uuid, $role, $level, $uid);
        $this->apiReturn(200, [], 'success');
    }

    /**
     * 微平台分销专员获取团队信息
     * date   2020-06-17
     * author xwh
     *
     * return array
     */
    public function teamInfo()
    {
        $multiMemberBuz = new \Business\MultiDist\Member();
        $memberModel    = new \Model\MultiDist\Member();
        $chainRes       = $multiMemberBuz->getUuidInfo($this->uuid);
        if (empty($chainRes)) {
            $this->apiReturn(404, [], '无效的uuid');
        }
        $data = $multiMemberBuz->getTeamInfoByUuidFormCache($this->uuid, $chainRes['higher_uid'],
            $this->_loginMember['sid'], $chainRes['top_uid'], $this->currentRole, $chainRes['create_time']);
        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 微平台分销专员编辑团队名称
     * date   2020-06-18
     * author xwh
     *
     * return array
     */
    public function editTeamName()
    {
        $teamName = I('post.team_name', '', 'strval');//团队名称
        if (empty($teamName)) {
            $this->apiReturn(203, [], '参数缺失');
        }
        $teamName = trim($teamName);
        // 验证参数长度
        if (mb_strlen($teamName, 'utf8') > 32) {
            $this->apiReturn(203, [], '名称参数格式不符合要求');
        }
        if ($this->level != 0) {
            $this->apiReturn(401, [], '非运营商无权限修改');
        }
        $multiDistInviteBus = new InviteBus();
        $modRes             = $multiDistInviteBus->setTeamName($this->uuid, $teamName);
        if (!$modRes || $modRes['code'] != $multiDistInviteBus::CODE_SUCCESS) {
            $this->apiReturn($modRes['code'], $modRes['data'], $modRes['msg']);
        }

        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 微平台分销专员获取团队首页初始数据
     * date   2020-06-18
     * author xwh
     *
     * return array
     */
    public function homeData()
    {
        $statisBuz   = new Statis();
        $memberModel = new \Model\MultiDist\Member();
        $memberLib   = new \Business\MultiDist\Member();
        $chainRes    = $memberLib->getUuidInfo($this->uuid);
        if (empty($chainRes)) {
            $this->apiReturn(404, [], '无效的uuid');
        }
        $result = $statisBuz->homeData($this->uuid, $this->currentRole, $this->level,
            date('Y-m-d H:i:s', $chainRes['create_time']));

        $this->apiReturn(200, $result, 'success');
    }

    /**
     * 微平台分销专员获取推广订单总汇
     * date   2020-06-18
     * author xwh
     *
     * return array
     */
    public function orderSummary()
    {

        $starTime = I('post.start_date', '', 'string');
        $endTime  = I('post.end_date', '', 'string');
        if (!strtotime($starTime) || !strtotime($endTime)) {
            $this->apiReturn(203, [], '参数缺失');
        }

        $statisBuz     = new Statis();
        $queryUuidList = [$this->uuid];
        $timeStart     = $starTime . ' 00:00:00';
        $timeEnd       = $endTime . ' 23:59:59';

        $orderList = $statisBuz->orderSummaryInfo(false, $queryUuidList, [], $timeStart, $timeEnd,
            1,
            -1);
        $data      = [
            'ordernum'         => $orderList['total'] ?? 0,//付款订单数
            'amount'           => $orderList['statis']['order_total_amount'] ?? 0,//付款订单金额
            'estimated_profit' => $orderList['statis']['estimated_profit'] ?? 0,//预估收益
            'start_date'       => $starTime,//开始时间
            'end_date'         => $endTime,//结束时间
        ];

        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 微平台分销专员获取我的收益数据
     * date   2020-06-18
     * author xwh
     *
     * return array
     */
    public function myEarnings()
    {
        $code        = 200;
        $msg         = '';
        $monthStatis = [];

        try {
            $starTime = I('post.start_date', '', 'string');
            $endTime  = I('post.end_date', '', 'string');
            $uuid     = I('post.uuid', '', 'string');

            if (empty($uuid)) {
                $uuid = $this->uuid;
            }

            if (!$starTime || !$endTime) {
                throw new \Exception('时间参数缺失！');
            }

            //收益明细
            $monthStatis = (new \Business\MultiDist\Statis())->sumStatis($uuid, $starTime, $endTime);
            $monthStatis = $monthStatis['month_data'];

        } catch (\Exception $e) {
            $msg  = $e->getMessage();
            $code = 500;
        }

        $this->apiReturn($code, $monthStatis, $msg);
    }

    /**
     * 获取我的收益列表
     * date   2020-06-19
     * author xwh
     *
     * return array
     */
    public function myIncomeList()
    {
        $data = [];
        try {
            $chainUuid = I('post.chain_uuid', '', 'string');
            $year      = I('year', '', 'strval');
            $month     = I('month', '', 'strval');
            $page      = I('post.page', 1, 'intval');
            $pageSize  = I('post.page_size', 10, 'intval');

//            if (!$year || !$month) {
//                throw new \Exception('时间参数格式错误');
//            }
            $statisBuz = new \Business\MultiDist\Statis();
            $result    = $statisBuz->myIncomeList($this->uuid, $this->_loginMember['sid'], $chainUuid,
                $this->currentRole, $year, $month, $page, $pageSize);
            $code      = $result['code'];
            $msg       = $result['msg'];
            $data      = $result['data'];

        } catch (\Exception $e) {
            $msg  = $e->getMessage();
            $code = 500;
        }
        $this->apiReturn($code, $data, $msg);

    }

    /**
     * 获取可推广产品列表
     * date   2020-06-19
     * author xwh
     *
     * return array
     */
    public function myProductList()
    {
        $this->apiReturn(400, '', '暂停使用');
//        $pType          = I('post.p_type', '', 'strval');//产品类型
//        $pid            = I('post.pid', '', 'strval');//搜索下拉框选择的产品ID
//        $sortCommission = I('post.commission_sort', 1, 'intval');//佣金排序 默认1降序,2升序
//        $keyword        = I('post.key_word', '', 'strval');//产品名称
//        $page           = I('post.page', 1, 'intval');//当前页
//        $pageSize       = I('post.page_size', 15, 'intval');//每页条数
//
//        $productBuz = new \Business\MultiDist\Product();
//
//        $paramArr = [
//            'p_type' => $pType,
//            'pid'    => $pid,
//            'p_name' => $keyword,
//        ];
//
//        $result = $productBuz->myProductList($this->uuid, $paramArr, $page, $pageSize);
//
//        $this->apiReturn($result['code'], $result['data'], $result['msg']);

    }

    /**
     * 获取发展下级列表 --微平台
     * date   2020-06-19
     * author xwh
     *
     * return array
     */
    public function lowList()
    {
        $wordKey    = I('post.word_key', '', 'strval');//搜索条件
        $inDateSort = I('post.in_date_sort', '', 'strval');//加入时间排序 1降序,2升序
        $incomeSort = I('post.income_sort', '', 'strval');//累计收益排序 1降序,2升序
        $page       = I('post.page', 1, 'intval');//当前页
        $pageSize   = I('post.page_size', 15, 'intval');//每页条数

        $sortField   = '';
        $sortType    = 0;
        $uidArr      = [];
        $paramArr    = [];
        $topUid      = [$this->_loginMember['sid']];
        $memberBus   = new MultiDistMember('', $this->uuid);
        $memberModel = new \Model\MultiDist\Member();

        if (!empty($wordKey)) {
            //是否为手机号
            if (ismobile($wordKey)) {
                $re = $memberModel->getMemberByMobiles($wordKey);
                if (isset($re['uid'])) {
                    $uidArr[] = $re['uid'];
                }
            } else {
                //用户名搜索
                if ($this->currentRole != 1) {
                    //获取本团队top UUid
                    $disChainRes = $memberBus->getChainByUuid($this->uuid);
                    if (!$disChainRes) {
                        $this->apiReturn(200, ['list' => []], '成功');
                    }
                    $topUid = [$disChainRes['top_uid']];
                }
                //为运营商将自身得uuid作为顶级uuid传入
                $re     = $memberModel->getMemberInfoByDname($wordKey, $topUid);
                if (empty($re)) {
                    $this->apiReturn(200, ['list' => []], '成功');
                }
                $uidArr = array_merge(array_column($re, 'uid'), $uidArr);
            }
            $paramArr['uids'] = array_unique($uidArr);
        }

        if ($inDateSort) {
            $sortField = 'create_time';
            $sortType  = $inDateSort == 1 ? 2 : 1;
        }
        if ($incomeSort) {
            $sortField = 'provide_profit';
            $sortType  = $incomeSort == 1 ? 2 : 1;
        }

        $result                 = $memberBus->getList($page, $pageSize, $paramArr, $sortField, $sortType);
        $handlowResult = $memberBus->handlowList($result['data']['list']);
        if ($handlowResult['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $result['data']['list'] = $handlowResult['data'];

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 查询审核列表
     * date   2020-06-19
     * author xwh
     *
     * return array
     */
    public function examineList()
    {
        $state    = I('post.state', 0, 'intval');//状态 0：待审核 1：已审核
        $page     = I('post.page', 1, 'intval');//当前页
        $pageSize = I('post.page_size', 15, 'intval');//每页条数

        if (!in_array($state, [0, 1])) {
            $this->apiReturn(203, [], '参数错误');
        }
        $inviteBuz = new \Business\MultiDist\Invite();
        $result    = $inviteBuz->examineList($this->uuid, $state, $page, $pageSize);

        $this->apiReturn(200, ['list' => $result['data']], '成功');

    }

    /**
     * 审核详情 - 获取申请详情
     * date   2020-06-19
     * author xwh
     *
     * return array
     */
    public function examineInfo()
    {
        $id = I('post.id', 0, 'intval');//审核ID
        if (!$id) {
            $this->apiReturn(203, [], '参数错误');
        }
        $inviteBuz                 = new InviteBus();
        $inviteModel               = new Invite();
        $result                    = $inviteModel->getInviteByMobileAndState($this->uuid, [], -1, '', 1, 15, [], $id);
        if (!$result) {
            $this->apiReturn(self::CODE_SUCCESS, [], 'success');
        }
        $result                    = $inviteBuz->examineInfo($result,$this->uuid);
        if ($result['code'] != 200){
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $result = $result['data'];
        //审核状态 自动审核 = 3
        $result[0]['check_result'] = $result[0]['is_auto'] == 1 ? 3 : $result[0]['check_result'];

        $this->apiReturn(200, $result[0], '成功');
    }

    /**
     * 同意/拒绝审核
     * <AUTHOR>
     * @date 2020/6/23
     *
     * @return array
     */
    public function setExamine()
    {
        $id          = I('post.id', '', 'intval');//审核ID
        $checkResult = I('post.check_result', '', 'intval');//类型 1=同意 2=拒绝
        $remark      = I('post.remark', '', 'strval');//拒绝原因

        if (!$id) {
            $this->apiReturn(203, [], '审核ID参数缺失');
        }

        if ($checkResult != 2 && $checkResult != 1) {
            $this->apiReturn(203, [], '类型参数缺失');
        }

        if ($checkResult == 2 && empty($remark)) {
            $this->apiReturn(203, [], '拒绝原因缺失');
        }

        // 验证参数长度
        if (mb_strlen($remark, 'utf8') > 30) {
            $this->apiReturn(203, [], '拒绝原因格式不符合要求');
        }
        $remark        = trim($remark);
        $distInviteBus = new InviteBus();
        $judgeRes      = $distInviteBus->judgeUserModuleDist($this->uuid);
        if (!$judgeRes) {
            $this->apiReturn(400, [], '请联系运营商续费才可以使用');
        }

        $modRes = $distInviteBus->setExamine($id, $checkResult, $this->uuid, $this->_loginMember['sid'],
            $this->_loginMember['memberID'], $remark);
        if (!$modRes || $modRes['code'] != $distInviteBus::CODE_SUCCESS) {
            $this->apiReturn($modRes['code'], $modRes['data'], $modRes['msg']);
        }

        $this->apiReturn(200, [], '审核成功');
    }

    /**
     * 获取产品海报
     * <AUTHOR>
     * @date 2020/6/23
     *
     * @return array
     */
    public function productPoster()
    {
        $lid = I('get.lid', '', 'intval');//景区id
        $sid = I('get.sid', '', 'intval');//顶级供应商ID
        $aid = I('get.aid', '', 'intval');//上级供应商ID

        if (!$lid || !$sid || !$aid) {
            $this->apiReturn(203, [], '参数缺失!');
        }

        $distMemberModel = new \Model\MultiDist\Member();
        $topMember       = $distMemberModel->getChainByUuid($this->uuid);
        $topAccount      = $distMemberModel->getMemberByUid($topMember['top_uid']);

        $productBus             = new DistProductBus();
        $res                    = $productBus->getProductPoster($this->_loginMember['sid'], $lid, $aid, $sid);
        $res['data']['account'] = $topAccount['account'] ?? '';
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     *
     * 获取分销专员基本信息
     * <AUTHOR>
     *
     * @return array
     */
    public function memberInfo()
    {
        $uuid = I('post.uuid', '', 'strval');//分销专员uuid
        if (!$uuid) {
            $this->apiReturn(203, [], '参数缺失!');
        }
        $MemberBuz   = new \Business\MultiDist\Member();
        $MemberModel = new \Model\MultiDist\Member();

        //普通身份只能查询自己及下级的
        if ($this->currentRole == 2) {
            $isLowerLevel = $MemberBuz->isLowerLevel($uuid, $this->uuid);
            if (!$isLowerLevel) {
                $this->apiReturn(400, [], '非下级，不允许查看');
            }
        }
        $chain = $MemberBuz->getUuidInfo($uuid);
        if (empty($chain) || !isset($chain['level'])) {
            $this->apiReturn(400, [], '初始信息错误');
        }
        $statisBuz   = new Statis();

        $result = $statisBuz->homeData($uuid,
            $chain['level'] > 0 ? \Business\MultiDist\Base::NOMAL_ROLE : \Business\MultiDist\Base::TOP_ROLE,
            $chain['level'],
            date('Y-m-d H:i:s', $chain['create_time']));

        $this->apiReturn(200, $result, '成功');
    }

    /**
     * 推广订单列表
     * <AUTHOR>
     * @date 2020/6/23
     *
     * @return array
     */
    public function orderList()
    {
        $code      = 200;
        $msg       = '';
        $orderList = [];

        try {
            $chainUuid = I('post.chain_uuid', '', 'strval');
            $year      = I('year', 0, 'intval');
            $month     = I('month', 0, 'intval');
            $page      = I('post.page', 1, 'intval');
            $pageSize  = I('post.page_size', 100, 'intval');
            if (!$this->currentRole) {
                throw new \Exception('非法身份！');
            }
//            if (!$year || !$month) {
//                throw new \Exception('时间参数格式错误');
//            }
            $statisBuz = new \Business\MultiDist\Statis();
            $checkData = $statisBuz->checkChainUuidAnduuid($this->uuid, $this->_loginMember['sid'], $this->currentRole,
                $chainUuid);
            if ($checkData['code'] != self::CODE_SUCCESS) {
                throw new \Exception($checkData['msg']);
            }
            $uuid      = $checkData['data']['uuid'];
            $role      = $checkData['data']['role'];

            $timeStart = $timeEnd = '';
            if ($year && $month) {
                $timeStart = $year . '-' . $month . '-01' . ' 00:00:00';
                $timeEnd   = date('Y-m-d', strtotime("$timeStart +1 month -1 Day")). ' 23:59:59';
            }

            $orderList = $statisBuz->orderList(false, $role, [$uuid], [], $timeStart, $timeEnd, 1, -1, '', $page,
                $pageSize);
        } catch (\Exception $e) {
            $msg  = $e->getMessage();
            $code = 500;
        }
        $this->apiReturn($code, $orderList, $msg);
    }

    /**
     * 推广订单详情
     * <AUTHOR>
     * @date 2020/6/23
     *
     * @return array
     */
    public function promoteOrderInfo()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $chainUuid = I('post.chain_uuid', '', 'string');
            $orderNum  = I('post.ordernum', '', 'strval');
            $orderBuz  = new \Business\MultiDist\Order();
            $result    = $orderBuz->promoteOrderInfo($this->uuid, $this->_loginMember['sid'], $chainUuid,
                $this->currentRole, $orderNum);
            $data      = $result['data'];
            $code      = $result['code'];
            $msg       = $result['msg'];
        } catch (\Exception $e) {
            $msg  = $e->getMessage();
            $code = 500;
        }
        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 获取短链接
     * <AUTHOR>
     * @date 2020/6/23
     *
     * @return array
     */
    public function shortUrl()
    {
        $url = I('post.url', '', 'strval');//链接

        if (empty($url)) {
            $this->apiReturn(203, [], '参数缺失!');
        }

        $shortUrl = Tools::transformShortUrl($url);
        if ($shortUrl === false) {
            $this->apiReturn(400, [], '短链接生成失败!');
        }
        $this->apiReturn(200, ['url' => $shortUrl], '');
    }

    /**
     * 店铺海报
     * <AUTHOR>
     * @date 2020/6/24
     *
     * @return array
     */
    public function storePoster()
    {
        $inviteBus   = new \Business\MultiDist\Invite();
        $memberModel = new \Model\MultiDist\Member();
        $memberLib   = new \Business\MultiDist\Member();
        $chainRes    = $memberLib->getUuidInfo($this->uuid);
        if (empty($chainRes)) {
            $this->apiReturn(404, [], '无效的uuid');
        }
        $result = $inviteBus->getStorePoster($chainRes['top_uid'], ENV, $this->uuid);
        if ($result['code'] != 200) {
            $this->apiReturn(203, [], $result['msg']);
        }
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 规则说明
     * <AUTHOR>
     * @date 2020/6/28
     *
     * @return array
     */
    public function getProblemListForFront()
    {
        $page          = I('post.page', 1, 'intval');
        $pageSize      = I('post.page_size', 10, 'intval');

        $param         = [
            'page'        => $page,
            'pageSize'    => $pageSize,
            'directoryId' => \Business\MultiDist\Base::$multiHelpId[ENV],
        ];

        $sdtype = $this->_loginMember['sdtype'];

        $helpCenterBuz = new HelpCenter();
        $result        = $helpCenterBuz->getProblemListForFront($param, $sdtype);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        foreach ($result['data']['list'] as &$item) {
            $item['text'] = htmlspecialchars_decode($item['text']);
        }

        $this->apiReturn($result['code'], $result['data'], "获取前台问题列表成功");
    }

    /**
     * 邀请海报
     * <AUTHOR>
     * @date 2020/6/28
     *
     * @return array
     */
    public function invitePoster()
    {
        $multiDistInviteBus = new InviteBus();
        $judgeRes           = $multiDistInviteBus->judgeUserModuleDist($this->uuid);
        if (!$judgeRes) {
            $this->apiReturn(400, [], '请联系运营商续费才可以使用');
        }

        $inviteRes = $multiDistInviteBus->getPosterInfo($this->uuid);

        if (!$inviteRes || $inviteRes['code'] != $multiDistInviteBus::CODE_SUCCESS) {
            $this->apiReturn($inviteRes['code'], $inviteRes['data'], $inviteRes['msg']);
        }

        $this->apiReturn(200, $inviteRes['data'], '成功');
    }

}