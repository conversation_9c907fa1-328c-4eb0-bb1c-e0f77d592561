<?php
/**
 * 用户账户相关操作
 *
 * <AUTHOR>
 * @time   2017-06-16
 */
namespace Controller\MicroPlat;

use Business\Finance\AmountLedgerFreeze as AmountLedgerFreezeBiz;
use Business\Finance\Credit as Credit;
use Business\Member\AccountInfo;
use Business\RiskManagement\ShumeiRiskCheck;
use Business\Wechat\Authorization;
use Controller\Member\Login;
use Controller\MicroPlat\Common as Common;
use Library\Cache\Cache as Cache;
use Library\Kafka\KafkaProducer;
use Library\Tools\Helpers;
use Library\Tools\Vcode;
use Model\Member\Member;
use Model\Ota\OtaQueryModel;

class Account extends Common
{
    private $_wxSid    = 0; // 提现用户ID
    private $_minMoney = 1000; // 提现最少金额(分)
    //验证短信业务的标识
    private $_verifySmsIdentify = 'verify_sms';

    //验证短信的发送频率 - 60秒
    private $_verifySmsInterval = 60;

    private $_verifySmsExpire = 1800;

    //短信模板
    private $_verifySmsTpl = 'bankcard_edit';

    public function __construct()
    {
        parent::__construct();

        $this->_wxSid = $this->_loginMember['sid'];
        if (!$this->_wxSid) {
            $this->returnAutoLogin('请先登录');
        }
        //如果是员工判断员工账号当前状态
        $this->checkStaffStatus();
    }

    /**
     * 账户余额
     * <AUTHOR>
     * @date   2017-06-15
     */
    public function getAccountMoney()
    {
        $moneyBus = new \Business\Finance\AccountMoney();
        $res      = $moneyBus->getAccountMoney($this->_wxSid);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(200, ['money' => $res['data']['money'] / 100]);
    }

    /**
     * 账户冻结金额
     * <AUTHOR>
     * @date   2017-06-15
     */
    public function getFreezeMoney()
    {
        $amountLedgerFreezeBiz = new AmountLedgerFreezeBiz();
        if ($amountLedgerFreezeBiz->isFrozenEnabled($this->_wxSid)) {
            $freMoney = $amountLedgerFreezeBiz->getFrozenBalance($this->_wxSid);
            $this->apiReturn(200, ['money' => $freMoney / 100]);
        } else {
            $bus = new \Business\Finance\AccountMoney();
            $res = $bus->getFreezeMoney($this->_wxSid);
            if ($res['code'] != 200) {
                $this->apiReturn($res['code'], [], $res['msg']);
            }
            $this->apiReturn(200, ['money' => $res['data']['money'] / 100]);
        }
    }

    /**
     * 账户可提现金额
     * <AUTHOR>
     * @date   2017-06-15
     */
    public function getValidWithdarwMoney()
    {
        $orderStatus  = [0, 2, 7];
        $hasSaleMoney = true;

        $bus = new \Business\Finance\AccountMoney();
        $res = $bus->getValidWithdarwMoney($this->_wxSid, $orderStatus, $hasSaleMoney);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $withdrawMoney = $res['data']['withdraw_money'];
        $showMoney     = money_fmt($withdrawMoney);

        //获取用户禁止提现配置
        $limitApi      = new \Business\JavaApi\Member\MemberLimit();
        $withdrawRes = $limitApi->queryFunctionLimitByFidAndLimitType($this->_wxSid, $limitType = 2);
        $withdrawLimit  = 0;
        if ($withdrawRes['code'] == 200 && !empty($withdrawRes['data'])) {
            $withdrawLimit = 1;
        }

        $this->apiReturn(200, ['money' => $showMoney, 'withdraw_limit' => $withdrawLimit]);
    }

    /**
     * 提现申请
     * 微平台这边统一提现手续费从提现金额扣除
     * 
     * <AUTHOR>
     * @date   2017-06-15
     * @modify dwer.cn 2018-04-14
     *
     * @param  int $cash 申请提现金额 (分)
     * @param  string $type_name 银行卡类型名 'alipay','bank1','bank2'
     */
    public function withdrawCash()
    {
        // 提现权限判断
        $this->_checkWithdrawAuth();

        $wdMoney  = I('post.cash', 0, 'intval');
        $typeName = I('post.type_name', '');
        $remark = I('post.remark', '', 'strval'); // 备注
        $memberId     = $this->_wxSid;
        $operatorId   = $this->_loginMember['memberID'];
        $operatorName = $this->_loginMember['dname'];

        //获取用户禁止提现配置
        $limitApi      = new \Business\JavaApi\Member\MemberLimit();
        $withdrawLimit = $limitApi->queryFunctionLimitByFidAndLimitType($memberId, $limitType = 2);
        if ($withdrawLimit['code'] == 200 && !empty($withdrawLimit['data'])) {
            $this->apiReturn(204, [], '该账户处于提现禁用状态，不可操作');
        }

        $riskCheck = new ShumeiRiskCheck();
        $eventData = [
            'withdrawAmount'      => floatval($wdMoney),
            'withdrawAccountId'   => (string)$this->_wxSid,
            'withdrawAccountType' => 'bank',
        ];
        $checkRes =$riskCheck->shumeiCheck('withdraw', 4, $this->_loginMember['account'], $eventData);
        if ($checkRes['code'] == 200 && $checkRes['data']['riskLevel'] == 'REJECT'){
            $this->apiReturn(210, [], '该设备/账号提现金额或次数已达到上限，请稍后再进行操作。若有疑问，请联系：400-99-22301');
        }

        // 提现操作
        $source = 5;
        if (!$typeName || $wdMoney <= 0){
            $this->apiReturn(204, [], '银行卡类型或金额有误');
        }
        $bus = new \Business\Finance\Withdraw();
        $res = $bus->apply($memberId, $wdMoney, $typeName, $cutFeeWay = 0, $operatorId, $operatorName,$source, [0,2,7], $remark);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 获取已验证过的银行账户信息
     * <AUTHOR>
     * @date   2017-06-16
     */
    public function getBankAccount()
    {
        $bus = new \Business\Finance\AccountMoney();
        $res = $bus->getBankAccount($this->_wxSid);
        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

        $this->apiReturn(200, $res['data']);
    }

    /**
     * 获得绑定手机
     * author  leafzl
     * Date:2018-3-23
     */

    public function getMobile()
    {
        $member = new \Business\Member\Member();
        $memberInfo = $member->getInfo($this->_wxSid);
        $this->apiReturn(200, $memberInfo['mobile']);
    }

    /**
     * 发送手机验证码
     * <AUTHOR>
     * @date   2018-3-23
     *
     * @param  int  $mobile 手机号
     */
    public function sendVcode()
    {
        $login  = new Login();
        $mobile = I('mobile', 0, 'intval');
        if (!Helpers::isMobile($mobile)) {
            $this->apiReturn(204, [], "手机号错误");
        }
        $graph = I('graph', '');
        if (!$graph) {
            $this->apiReturn(204, [], "请输入图形验证码");
        }

        $graphRes = $login->checkImgCode($graph);

        if (!$graphRes) {
            $this->apiReturn(206, [], "图形验证码输入错误");
        }

        $redis = Cache::getInstance('redis');
        //按目前的量，24小时只允许发送1000个
        $limitKey = 'limit:mobile:cnt';
        $sendNum  = $redis->get($limitKey);

        if ($sendNum > 5000) {
            $this->apiReturn(206, [], "疑似遭到攻击,接口暂时关闭");
        }

        $mobileKey = "lock:{$mobile}:cnt";
        $times     = $redis->get($mobileKey);
        if ($times > 5) {
            $this->apiReturn(204, [], "发送失败:发送频率频繁");
        }
        $data = [
            '{1}'  => '银行卡修改',
            'code' => '{2}',
        ];
        $result = Vcode::sendVcode($mobile, $this->_verifySmsTpl, $this->_verifySmsIdentify, $data, 6, false, $this->_verifySmsExpire, $this->_verifySmsInterval);
        if ($result['code'] == 200) {

            if (!$sendNum) {
                $redis->set($limitKey, 1, '', 3600 * 24);
            } else {
                $redis->incrBy($limitKey, 1);
            }

            if (!$times) {
                $redis->set($mobileKey, 1, '', 3600);
            } else {
                $redis->incrBy($mobileKey, 1);
            }
            $this->apiReturn(200, [], '发送成功');
        } else {
            $this->apiReturn(204, [], "发送失败:{$result['msg']}");
        }
    }

    /**
     * 手机短信验证码验证修改银行卡
     * <AUTHOR>
     * @date   2018-3-23
     *
     * @param  int     $mobile 手机号
     * @param  string  $vcode  短信验证码
     */
    public function verifyByVcode()
    {
        $mobile = I('mobile', 0, 'intval');
        $vcode  = I('vcode', '', 'trim');
        if (!$mobile || !ismobile($mobile) || !$vcode) {
            $this->apiReturn(400, '参数错误');
        }

        $result = Vcode::verifyVcode($mobile, $vcode, $this->_verifySmsIdentify);

        if ($result['code'] == 200) {
            $this->apiReturn(200, [], '验证成功');
        } else {
            $this->apiReturn($result['code'], [], $result['msg']);
        }
    }
    /**
     * 获取银行列表
     * <AUTHOR>
     * @date   2018-03-16
     */
    public function getBankList()
    {
        $sid = $this->isLogin();// 供应商ID

        $memberLib = new \Business\Member\Member();
        $resMember = $memberLib->getInfo($sid);
        if (empty($resMember)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $accountService = new \Business\JavaApi\Account\Account();
        $accRes = $accountService->getAccountInfoWithAccountId($resMember['account_id']);
        if ($accRes['code'] != 200 || !$accRes['data']) {
            $this->apiReturn('500', [], '账户信息获取失败');
        }

        $bankOne = $bankTwo = [];

        if (!empty($accRes['data']['bank_account_1'])) {
            $bankOneTmp = explode('|', $accRes['data']['bank_account_1']);
            $bankOne = [
                'type'        => intval($bankOneTmp[4]),
                'bankName'    => $bankOneTmp[0],
                'bankAccuont' => substr($bankOneTmp[2], -4),
                'accuont'     => $bankOneTmp[2],
            ];
        }

        if (!empty($accRes['data']['bank_account_2'])) {
            $bankTwoTmp = explode('|', $accRes['data']['bank_account_2']);
            $bankTwo = [
                'type'        => intval($bankTwoTmp[4]),
                'bankName'    => $bankTwoTmp[0],
                'bankAccuont' => substr($bankTwoTmp[2], -4),
                'accuont'     => $bankTwoTmp[2],
            ];
        }

        $resData = [];
        if (!empty($bankOne)) {
            $resData[1] = $bankOne;
        }
        if (!empty($bankTwo)) {
            $resData[2] = $bankTwo;
        }

        $this->apiReturn(200, $resData, '成功');
    }

    /**
     * 删除银行卡
     * author  leafzl
     * Date:2018=3-23
     */
    public function deleteBankCard()
    {
        $type = intval(I('type'));
        if (!in_array($type, array(1, 2))) {
            $this->apiReturn(204, [], '参数错误');
        }

        $sid = $this->isLogin();//供应商ID

        //账号信息
        $bankSerial = 'bank' . $type;
        $memberBiz  = new \Business\Member\Member();

        //删除银行卡
        $bankRes = $memberBiz->delBankAccount($sid, $bankSerial);

        $this->apiReturn($bankRes['code'], $bankRes['data'], $bankRes['msg']);
    }

    public function addBankCard()
    {
        $type = intval(I('type'));
        $bus  = new \Business\Finance\AccountMoney();

        if (!in_array($type, array(1, 2))) {
            $this->apiReturn(204, [], '参数错误');
        }

        $bankName    = I('bankName'); //支行名称
        $bankCode    = I('bankCode'); //支行行号
        $bankAccount = I('bankAccount'); //账号
        $accountName = I('accountName'); //账户名
        $accType     = I('accType');
        if (empty($bankName)) {
            $this->apiReturn(204, [], '银行名称不能为空');
        }

        if (empty($bankCode)) {
            $this->apiReturn(204, [], '支行名称不能为空');
        }

        if (empty($bankAccount) || !is_numeric($bankAccount)) {
            $this->apiReturn(204, [], '银行账号格式错误');
        }

        $bankSerial = $type == 1 ? 'bank1' : 'bank2';

        $res = $bus->addBankCard($this->_loginMember['memberID'], $bankSerial, $bankName, $bankCode, $bankAccount, $accountName, $accType);
        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '成功');
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);
        }

    }

    /**
     * 获取编辑银行卡信息
     * author  leafzl
     * Date:2018-3-28
     */
    public function getCardInfo()
    {
        $type = intval(I('type'));

        if (!in_array($type, array(1, 2))) {
            $this->apiReturn(204, [], '参数错误');
        }

        $sid = $this->isLogin();//供应商ID

        $memberLib = new \Business\Member\Member();
        $resMember = $memberLib->getInfo($sid);
        if (empty($resMember)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $accountService = new \Business\JavaApi\Account\Account();
        $accRes = $accountService->getAccountInfoWithAccountId($resMember['account_id']);
        if ($accRes['code'] != 200 || !$accRes['data']) {
            $this->apiReturn('500', [], '账户信息获取失败');
        }

        if ($type == 1) {
            $WithdrawB = new \Business\Finance\Withdraw();
            $bankCardaCcountInfo = $WithdrawB ->getBankCardInfo($accRes['data']['bank_account_1'], $sid,1);
        }

        if ($type == 2) {
            $WithdrawB = new \Business\Finance\Withdraw();
            $bankCardaCcountInfo = $WithdrawB ->getBankCardInfo($accRes['data']['bank_account_2'], $sid,2);
        }

        //参数兼容
        $resultData = [
            'province'      => $bankCardaCcountInfo['info']['province'],
            'city'          => $bankCardaCcountInfo['info']['city'],
            'bank_id'       => $bankCardaCcountInfo['info']['bank_id'],
            'branch_id'     => $bankCardaCcountInfo['info'][1],
            'bankAccount'   => $bankCardaCcountInfo['info'][2],
            'accountName'   => $bankCardaCcountInfo['info'][3],
            'bankType'      => $bankCardaCcountInfo['info'][4],
        ];

        $this->apiReturn(200, $resultData, '成功');
    }

    /**
     * 供应商给下级分销商修改授信额度
     * <AUTHOR>
     * @date   2017-07-25
     */
    public function changeCredit()
    {
        $mode       = I('post.mode', 0, 'intval'); // 授信模式[0固定额度,1不限额度]
        $cmoney     = I('post.cmoney', 0, 'floatval'); // 授信额度 单位：元（mode=0时生效）
        $memo       = I('post.meno', '', 'strval'); // 备注
        $resellerId = I('post.did', 0, 'intval'); // 分销商ID

        $sid     = $this->isLogin(); // 供应商ID
        $fid     = $this->_loginMember['memberID']; // 操作员ID
        $account = $this->_loginMember['account'];
        $dname   = $this->_loginMember['dname'];

        // 参数校验
        if (!$resellerId) {
            $this->apiReturn(201, [], '请选择分销商');
        }

        //将金额转换给分
        $cmoney = $cmoney * 100;

        // 禁止频繁操作
        if ($mode == 1) {
            // 不限额度
            $lockey = "lock:set_fund:set_unlimit:{$fid}_{$resellerId}";
            $memo .= '微平台设置不限额度';
            $cmoney = load_config('credit_unlimit', 'trade_record');
        } else {
            // 固定额度
            $lockey = "lock:set_fund:set_limit:{$fid}_{$resellerId}_{$cmoney}";
            $memo .= '微平台设置固定额度';
        }

        $cache    = $this->getRedisObject();
        $lockInfo = $cache->lock($lockey, 1, 60);

        if (!$lockInfo) {
            $this->apiReturn(201, [], '正在为您修改授信额度，请稍后');
        }

        // 将操作员写入备注中
        if ($fid != $sid) {
            $memo .= "【员工：{$dname}({$account})】";
        }

        // 模式判断
        $creditBiz = new Credit();
        $res       = $creditBiz->setLimit($sid, $resellerId, $cmoney, $fid, $memo, $mode);

        //清除锁
        $cache->rm($lockey);

        if ($res['code'] != 200) {
            $this->apiReturn(201, [], $res['msg']);
        }

        $this->apiReturn(200, [], '授信成功');
    }

    /**
     * 供应商给下级分销商设置的线下还款
     * <AUTHOR>
     * @date   2017-07-25
     */
    public function offlinePay()
    {
        $resellerId = I('post.did', 0, 'intval'); // 分销商ID
        $payMoney   = I('post.tmoney', 0, 'floatval'); // 线下还款金额-元
        $memo       = I('post.memo', '', 'trim'); // 备注

        $sid       = $this->isLogin(); // 供应商ID
        $fid       = $this->_loginMember['memberID']; // 操作员ID
        $opAccount = $this->_loginMember['account']; // 操作员账户
        $opName    = $this->_loginMember['dname']; // 操作员名称

        // 将操作员写入备注中
        if ($fid != $sid) {
            $memo .= "【员工：{$opName}({$opAccount})】";
        }

        // 禁止频繁操作
        $cache    = $this->getRedisObject();
        $lockey   = "lock:set_fund:offline:{$fid}_{$resellerId}_{$payMoney}";
        $lockInfo = $cache->lock($lockey, 1, $lockTime = 120);
        if (!$lockInfo) {
            $this->apiReturn(201, [], '业务正在处理中，请稍后再试');
        }

        $bus = new Credit();
        $res = $bus->offlinePay($sid, $resellerId, $payMoney, $fid, $memo);

        //清除锁
        $cache->rm($lockey);

        if ($res['code'] != 200) {
            $this->apiReturn($res['code'], [], $res['msg']);
        } else {
            $this->apiReturn(200, [], '还款成功');
        }
    }

    /**
     * 下级分销商通过预存/还款给供应商 - 账户余额
     * <AUTHOR>
     * @date   2017-07-25
     */
    public function balancePay()
    {
        $applyId = I('post.did', 0, 'intval'); // 供应商ID
        $money   = I('post.money', 0, 'floatval'); // 线下还款金额 - 元
        $memo    = I('post.memo', '', 'strval'); // 备注

        $resellerId = $this->isLogin(); // 分销商ID
        $opId       = $this->_loginMember['memberID']; // 操作员ID
        $opName     = $this->_loginMember['dname']; // 操作员名称
        $opAccount  = $this->_loginMember['account']; // 操作员账户

        if (!$applyId) {
            $this->apiReturn(201, [], '供应商参数错误，请重新提交');
        }

        if ($money <= 0) {
            $this->apiReturn(201, [], '还款金额不正确');
        }

        // 禁止频繁操作
        $cache    = $this->getRedisObject();
        $lockey   = "lock:set_fund:balance_pay:{$opId}_{$applyId}_{$money}";
        $lockInfo = $cache->lock($lockey, 1, $lockTime = 120);
        if (!$lockInfo) {
            $this->apiReturn(201, [], '业务正在处理中，请稍后再试');
        }

        $repayInfo = (new \Business\JavaApi\Member\MemberClearingWay())->getMemeberClearingWay($applyId, $resellerId);
        if ($repayInfo['code'] != 200) {
            $this->apiReturn(201, [], '获取当前分销商还款模式失败，请稍后再试');
        }
        $repayMode = $repayInfo['data']['repayMode'] ?? 0;
        if ($repayMode) {
            $this->apiReturn(201, [], '该供应商设置仅限线下还款模式');
        }

        //将操作员写入备注中
        if ($opId != $resellerId) {
            $memo .= "【员工：{$opName}({$opAccount})】";
        }

        $bus = new Credit();
        $res = $bus->balancePay($resellerId, $applyId, $money, $opId, $opName, $memo . '[微平台]');

        //清除锁
        $cache->rm($lockey);

        $code = $res['code'];
        $msg  = $res['msg'];
        if ($code != 200) {
            $this->apiReturn($code, [], $msg);
        } else {
            $this->apiReturn(200, [], '预存/还款成功');
        }
    }

    /**
     * 订单通知
     * author  leafzl
     * Date: 2018-3-27
     */
    public function posterNotify()
    {
        $memberIfon = new AccountInfo();
        $type       = I('type');
        if (!in_array($type, [0, 1])) {
            $this->apiReturn('204', [], '参数错误');
        }
        $res = $memberIfon->posterNotify($type, $this->_wxSid);

        if ($res['code'] == 200) {
            $this->apiReturn(200, [], '成功');
        } else {
            $this->apiReturn($res['code'], [], $res['msg']);

        }

    }

    /**
     * 员工提现权限判断
     * <AUTHOR>
     * @date   2017-06-16
     */
    private function _checkWithdrawAuth()
    {
        $sid   = $this->isLogin();
        $fid   = $this->_loginMember['memberID'];
        $dtype = $this->_loginMember['dtype'];

        if (in_array($dtype, [0, 1, 10])) {
            $this->_wxSid = $sid;

        } elseif ($dtype == 6) {

            $mModel = $this->getMemberModel('master');

            // 员工账号有效性检测
            $res = $mModel->getStaffBySonId($fid);
            if (!$res || $res['parent_id'] != $sid) {
                $this->apiReturn(201, [], '您没有提现权限');
            }

            // 员工账号权限判断
            $son = $mModel->getMemberInfo($fid);
            if (!$son) {
                $this->apiReturn(201, [], '员工账号不存在');
            }

            if (!in_array('accourtmanage', explode(',', $son['member_auth']))) {
                $this->apiReturn(201, [], '您没有提现权限');
            }

            $this->_wxSid = $sid;

        } else {
            $this->apiReturn(201, [], '您没有提现权限');
        }
    }

    /**
     * 充值页面自动登录
     * <AUTHOR>
     * @dateTime 2017-11-14T15:24:54+0800
     * @throws   Exception                              可能抛出异常
     */
    public function autoLoginRecharge()
    {
        if (!$this->isLogin() || !in_array($this->_loginMember['dtype'], [0,1])) {
            session_destroy();
            $this->_redirect($this->_supplyAccount, 'microLogin'); // 登录页
        }
        if (I("code")) {
            $params  = json_decode(base64_decode(I('state')), true);
            $authBiz = new Authorization();
            $auth    = $authBiz->parseAuthInfo(I('code'));
            if ($auth['code'] != 200) {
                $this->_redirect($this->_supplyAccount, 'login'); // 登录页
            }
            $openid             = $auth['data']['openid'];
            $_SESSION['openid'] = $openid;
            $domain             = str_replace('wx', $params["supplyAccount"], MOBILE_DOMAIN);
            $url                = MOBILE_DOMAIN . "html/recharge.html";
            $this->_redirect(null, '', $url, null);
        } else {
            //判断是否在微信浏览器中
            if ($this->inWechatApp()) {
                $params['supplyAccount'] = $this->_supplyAccount;
                $params['supplyId']      = $this->_supplyId;
                $callback                = MOBILE_DOMAIN . 'api/index.php?' . $_SERVER['QUERY_STRING'];
                try {
                    $busWechat = new Authorization();
                    $busWechat->requestForAuth($callback, $params);
                } catch (\Exception $e) {
                }
            }
        }
    }

    /**
     * 修改密码
     * <AUTHOR>
     * @dateTime 2017-11-06T15:17:25+0800
     * @throws   Exception                              可能抛出异常
     * @return   [type]                   [description]
     */
    public function resetPassword()
    {
        //$limit_channel = ['ResetOperPassword'];
        //$model         = new Member();

        $this->isLogin('ajax');
        $member_id = $this->_loginMember['memberID'];
        if (!$member_id) {
            parent::apiReturn(500, [], '身份校验失败');
        }

        $new_password1 = I('post.new_pwd', 'trim');
        $new_password2 = I('post.confirm_pwd', 'trim');
        if (isset($_POST['old'])) {
            $old_password = md5(md5(I('post.old', 'trim')));
            //用户二期 - 信息获取修改
            $CustomerBus = new \Business\Member\Customer();
            $res         = $CustomerBus->checkOldPassword($member_id,$old_password);
            if (!$res) {
                parent::apiReturn(500, [], '旧密码不正确');
            }
        }

        $res = self::chkPassword($new_password1, $new_password2);
        if ($res !== true) {
            parent::apiReturn(500, [], $res);
        }
        //<AUTHOR> | @date 2018/10/22 | 会员表更新模块统一
        $Member = new \Business\Member\Member();
        $res    = $Member->updateMemberPassword($member_id, $new_password1);
        if ($res) {
            parent::apiReturn(200, [], '修改成功');
        }else{
            parent::apiReturn(500, [], '修改失败');
        }
    }

    public static function chkPassword($p1, $p2)
    {
        $p1                 = strval($p1);
        $commonWeakPassword = array(
            '123456', 'a123456', 'a123456789', 'woaini1314', 'qq123456', 'abc123456',
            '123456a', '123456789a', 'abc123', 'qq123456789', '123456789.',
            'woaini', 'q123456', '123456abc', '123456.', '0123456789',
            'asd123456', 'aa123456', 'q123456789', 'abcd123456', 'woaini520',
            'woaini123', 'w123456', 'aini1314', 'abc123456789', 'woaini521',
            'qwertyuiop', 'qwe123456', 'asd123', '123456789abc', 'z123456',
            'aaa123456', 'abcd1234', 'www123456', '123456789q', '123abc',
            'qwe123', 'w123456789', '123456qq', 'zxc123456', 'qazwsxedc',
            '123456..', 'zxc123', 'asdfghjkl', '123456q', '123456aa',
            '9876543210', 'qaz123456', 'qq5201314', 'as123456',
            'z123456789', 'a123123', 'a5201314', 'wang123456', 'abcd123',
            '123456789..', 'woaini1314520', '123456asd', 'aa123456789',
            '741852963', 'a12345678',
        );

        $len = mb_strlen($p1, 'utf-8');
        if ($p1 != $p2) {
            return "两次密码输入不一致";
        } elseif ($len < 6 || $len > 20) {
            return "密码长度必须大于6小于20" . $len;
        } elseif (ctype_digit($p1) || ctype_alpha($p1)) {
            //纯数字&纯字母的提示
            return '您设置的密码过于简单，请输入6-20位数字、字母或常用符号，字母区分大小写';
        } elseif (in_array($p1, $commonWeakPassword)) {
            return '您输入的密码太常见，很容易被人猜出，请重新选择无规则的数字字母组合。';
        } elseif (preg_match('/\s/', $p1)) {
            return "密码仅支持英文、数字和字符，不支持空格";
        }
        return true;
    }
}
