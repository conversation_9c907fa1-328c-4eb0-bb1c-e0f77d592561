<?php
/**
 * Created by PhpStorm.
 * User: zhangyangzhen
 * Date: 2018/4/16
 * Time: 14:20
 */

namespace Controller\MicroPlat;

use Library\Exception;
use Model\Member\MemberRelationship;
use Model\Order\OrderTools;
use Process\Finance\TradeRecord as TradeProcess;

class TransactionRecord extends Common
{
    private $tradeModel;
    private $memberId;
    private $_isGroup = 0;   //是否是集团账号
    private $income   = 0;    //收入
    private $expenses = 0;  //支出

    public function __construct()
    {
        parent::__construct();
        $this->memberId = $this->_loginMember['sid'];

        if ($this->_loginMember['dtype'] == 7) {
            $this->_isGroup = 1;
        }
    }

    /**
     * 获取交易记录
     * Create by zhangyangzhen
     * Date: 2018/4/17
     * Time: 13:41
     * @throws \Process\Finance\Exception
     */
    public function getTradeRecord()
    {
        try {
            $map = [];

            //是不是超级用户
            $isSuper = $this->isSuper();

            // $fid = ($isSuper && !empty($_REQUEST['fid'])) ? intval(I('fid')) : $this->memberId;
            if (($isSuper || $this->_isGroup) && !empty($_REQUEST['fid'])) {
                $fid = intval(I('fid'));
            } elseif ($isSuper && empty($_REQUEST['fid'])) {
                if (isset($_REQUEST['fid'])) {
                    //会员交易记录 会传个空的fid
                    $fid = '';
                } else {
                    //交易记录 不会传fid
                    $fid = 1;
                }
            } elseif ($this->_isGroup) {
                $sons = (new \Model\Member\MemberRelationship())->getMemIdByGroupId($this->memberId);
                $fid  = array_column($sons, 'son_id');
            } else {
                $fid = $this->memberId;
            }

            $partner_id = intval(I('partner_id')) ?: 0;

            if ($isSuper && !$fid && $partner_id) {
                throw new Exception('请先选择企业名称', 220);
            }

            //权限判断
            if (!$isSuper && isset($_REQUEST['fid']) && !$this->_isGroup) {
                throw new Exception('无权限查看', 201);
            }

            //时段
            $interval       = TradeProcess::parseTime();
            $map['rectime'] = array('between', $interval);

            //这两个时间用来确定的数据是在哪个表中
            $startTime = $interval[0];
            $endTime   = $interval[1];

            //交易大类
            $items = \safe_str(I('items', ''));
            //获取这些大类对应的全部小类
            $subtype = [];
            if ('' != $items) {
                // -1代表全部大类
                if ($items == '-1') {
                    //取出配置文件中交易大类的所有键值，并转换为字符串，用 | 隔开
                    $items = implode('|', array_keys(load_config('trade_item', 'trade_record')));
                }

                $items        = explode('|', $items);
                $itemCategory = load_config('item_code_category', 'trade_record');
                $item_cat = [];
                foreach ($itemCategory as $tmpKey => $tmpItem) {
                    $item_cat[$tmpKey] = $tmpItem[0];
                }

                foreach ($items as $item) {
                    $subtype = array_merge($subtype, $itemCategory[$item] ?? []);
                }
                //平台套餐续费 特殊处理
                if (in_array(1, $items)) {
                    if (!in_array(1026, $subtype)) {
                        $subtype = array_merge($subtype, [1026]);
                    }
                }
                //去除授信余额调整
                // $key = array_search(1011, $subtype);
                // if ($key !== false) {
                //     array_splice($subtype, $key, 1);
                // }
                //去除转账
                $key1 = array_search(1018, $subtype);
                if ($key1 !== false) {
                    array_splice($subtype, $key1, 1);
                }
            }

            //是否选择交易小类，没选默认大类对应的全部小类
            $chooseDtype = I('choose_type', 0, 'intval');
            if ($chooseDtype == 0) {
                //没选,默认小类就是大类对应的全部的
                if ($subtype) {
                    $map['item_code'] = $subtype;
                }
            } else {
                //有选，判断选择的小类是否合法
                $dtypes = \safe_str(I('dtypes', '', 'strval'));
                if (!$dtypes) {
                    throw new Exception('请选择对应的小类！', 400);
                }

                $dtypes = explode('|', $dtypes);
                foreach ($dtypes as $key => $dtype) {
                    if (!in_array($dtype, $subtype)) {
                        throw new Exception('小类信息有误！', 400);
                    }
                    //合并凭证费至电子凭证费
                    if ($dtype == 1007) {
                        array_push($dtypes, 1010);
                    }
                    //合并平台费至平台套餐费
                    if ($dtype == 1026) {
                        array_push($dtypes, 1022);
                    }
                }
                $map['item_code'] = $dtypes;
            }

            //订单号
            $orderid = \safe_str(I('orderid'));
            if ($orderid) {
                $map['orderid'] = $orderid;
                if (!$_REQUEST['btime'] && !$_REQUEST['etime']) {
                    unset($map['rectime']);
                    $interval = ['', ''];
                }

                //如果是通过订单号查询，如果查询得到订单信息，就使用订单的下单当天作为查询时间条件，否则使用今天作为查询条件
                $orderModel = new OrderTools('slave');
                $orderInfo  = $orderModel->getOrderInfo($orderid, 'ordertime');
                if ($orderInfo) {
                    $orderTimestamp = strtotime($orderInfo['ordertime']);
                    if ($orderTimestamp) {
                        $startTime = date('Y-m-d 00:00:00', $orderTimestamp);
                        $endTime   = date('Y-m-d 23:59:59', $orderTimestamp);
                    }
                }
            }

            //判断起始时间是不是在可查询的段内
            $preTmp = TradeProcess::getDataPosition($startTime, $endTime);
            $code   = $preTmp['code'];
            if ($code == 0) {
                throw new Exception($preTmp['msg'], 202);
            }

            //支付方式
            $tmpMap = TradeProcess::parseSubjectCode();
            if ($tmpMap) {
                $map = array_merge($map, $tmpMap);
            }

            $map['fid'] = $fid;
            if (I('partner_id')) {
                $map['aid'] = $partner_id;
            }

            //交易金额为0的交易记录不显示
            $map['dmoney'] = ['gt', 0];

            //分页
            $page  = I('page', 1, 'intval');
            $limit = I('limit', 15, 'intval');

            $recordModel = $this->_getTradeModel('slave', $startTime, $endTime);

            $this->_output($recordModel, $map, $page, $limit, $interval, $fid, $partner_id);

        } catch (Exception $e) {
            \pft_log('trade_record/err', 'get_list|' . $e->getCode() . "|" . $e->getMessage(), 'month');
            $this->apiReturn($e->getCode(), [], $e->getMessage());
        }
    }

    /**
     * 获取某条交易记录详细
     * Create by zhangyangzhen
     * Date: 2018/4/17
     * Time: 14:06
     * @throws Exception
     */
    public function getTradeDetail()
    {
        $trade_id = \safe_str(I('trade_id'));

        if (!$trade_id) {
            $this->apiReturn(201, [], '传入参数不合法');
        }
        $fid = (($this->memberId == 1 || $this->_isGroup) && isset($_REQUEST['fid'])) ? intval(I('fid')) : $this->memberId;

        $partner_id = I('partner_id', 0, 'intval');

        if ($this->memberId == 1 && !$fid && $partner_id) {
            $this->apiReturn(220, [], '请先选择企业名称');
        }

        //根据传进来的时间和订单号确定数据所在的表
        $queryTimeArr = TradeProcess::parseTime();

        //这两个时间用来确定的数据是在哪个表中
        $startTime = $queryTimeArr[0];
        $endTime   = $queryTimeArr[1];

        //订单号
        $orderId = \safe_str(I('orderid'));
        if ($orderId) {
            //如果是通过订单号查询，如果查询得到订单信息，就使用订单的下单当天作为查询时间条件，否则使用今天作为查询条件
            $orderModel = new OrderTools('slave');
            $orderInfo  = $orderModel->getOrderInfo($orderId, 'ordertime');
            if ($orderInfo) {
                $orderTimestamp = strtotime($orderInfo['ordertime']);
                if ($orderTimestamp) {
                    $startTime = date('Y-m-d 00:00:00', $orderTimestamp);
                    $endTime   = date('Y-m-d 23:59:59', $orderTimestamp);
                }
            }
        }

        //判断起始时间是不是在可查询的段内
        $preTmp = TradeProcess::getDataPosition($startTime, $endTime);
        $code   = $preTmp['code'];
        if ($code == 0) {
            $this->apiReturn(202, [], $preTmp['msg']);
        }

        $record = $this->_getTradeModel('slave', $startTime, $endTime)->getDetails($trade_id, $fid, $partner_id);

        //数据处理
        TradeProcess::handleDetailData($record);

        //集团账号查看
        if ($this->_isGroup) {
            $queryParams = [$this->memberId];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberRelationQuery',
                'queryPageGroupSonIds', $queryParams);
            $sonArr      = [$this->memberId];
            if ($queryRes['code'] == 200 && !empty($queryRes['data']['list'])) {
                $sonArr = array_merge($sonArr, $queryRes['data']['list']);
            }
            //$Relation = new \Model\Member\MemberRelationship();
            //$list     = $Relation->getResellers($this->memberId, 7, true, true, 1, 10);
            //$sonArr   = array_column($list, 'id');
        }

        //无权查看时返回数据为空
        if (isset($record['fid'], $record['aid']) && (
                in_array($this->memberId, [1, $record['fid'], $record['aid']]) ||
                in_array($record['fid'], $sonArr) || in_array($record['aid'], $sonArr)
            )) {
            unset($record['fid'], $record['aid']);
        } else {
            $record = [];
        }

        $this->apiReturn(200, $record, '操作成功');
    }

    /**
     * 根据传入的form值输出结果
     * Create by zhangyangzhen
     * Date: 2018/4/16
     * Time: 16:09
     *
     * @param $recordModel      交易记录模型
     * @param $map              查询条件
     * @param $page             当前页数
     * @param $limit            每页行数
     * @param $interval         起止时间段   [开始时间, 结束时间]
     * @param $fid
     * @param $partner_id
     *
     * @throws Exception
     */
    private function _output($recordModel, $map, $page, $limit, $interval, $fid, $partner_id)
    {
        $data = $recordModel->getList($map, $page, $limit, $fid, $partner_id);

        //获取未结票款数据并统计收入和支出总额
        unset($map['_complex']);
        $map['account_type'] = 0;
        $map['fid']          = $fid;
        if (I('partner_id')) {
            $map['aid'] = $partner_id;
        }
        $map['dmoney'] = ['gt', 0];
        $summaryData   = $recordModel->getListSummary($map);
        if (!empty($summaryData)) {
            $this->_dealUnpayTradeMoney($summaryData);
        }

        //列表数据处理
        if (!empty($data['list'])) {
            $list = $data['list'];
            foreach ($list as &$item) {
                TradeProcess::handleData($item);
                TradeProcess::getTradeNameAndAccount($item);
                $item['counter'] = substr($item['counter'], 0, strpos($item['counter'], '<br />'));
                $item['rectime'] = date('Y-m-d H:i', strtotime($item['rectime']));
            }
        } else {
            $list = [];
        }

        $res = [
            'total'      => $data['total'],
            'page'       => $page,
            'total_page' => ceil($data['total'] / $limit),
            'limit'      => $limit,
            'list'       => $list,
            'btime'      => $interval[0],
            'etime'      => $interval[1],
            'income'     => $this->income,
            'expenses'   => $this->expenses,
        ];
        $this->apiReturn(200, $res);
    }

    /**
     * 处理未结票款总额数据，统计收入和支出
     * Create by zhangyangzhen
     * Date: 2018/4/25
     * Time: 11:50
     *
     * @param $dmoney
     * @param $isAccReverse
     * @param $ptype
     * @param $daction
     */
    private function _dealUnpayTradeMoney($summaryData)
    {
        $this->income = strval(sprintf(($summaryData['income'] ?? 0) / 100, 2));
        $this->expenses = strval(sprintf(($summaryData['expense'] ?? 0) / 100, 2));
        // if ($dmoney !== '') {
        //     $dmoney = strval(sprintf($summaryData['income'] / 100, 2));
        // }
        //
        // if ($isAccReverse && !in_array($ptype, [2, 3])) {
        //     if ($daction == 0) {
        //         $this->expenses -= $dmoney;
        //     } else {
        //         $this->income += $dmoney;
        //     }
        // } else {
        //     if ($daction == 0) {
        //         $this->income += $dmoney;
        //     } else {
        //         $this->expenses -= $dmoney;
        //     }
        // }
    }

    /**
     * 获取交易记录模型
     * Create by zhangyangzhen
     * Date: 2018/4/16
     * Time: 16:01
     *
     * @param  string  $defaultDb
     * @param  bool  $startTime
     * @param  bool  $endTime
     *
     * @return \Model\Finance\TradeRecord
     */
    private function _getTradeModel($defaultDb = 'slave', $startTime = false, $endTime = false)
    {
        if (null === $this->tradeModel) {
            $this->tradeModel = new \Model\Finance\TradeRecord($defaultDb, $startTime, $endTime);
            //设置集团账号标识
            if ($this->_isGroup) {
                $this->tradeModel->setGroupIdentify($this->memberId);
            }
        }

        return $this->tradeModel;
    }

    /**
     * 获取交易大类和账户类型
     * Create by zhangyangzhen
     * Date: 2018/4/16
     * Time: 14:45
     */
    public function getItems()
    {
        $items           = load_config('trade_item', 'trade_record');
        $items['-1']     = '全部类型';
        $accoutType      = load_config('micro_account_type_new', 'trade_record');
        $accoutType[100] = '全部账户';
        $data            = ['items' => $items, 'account' => $accoutType];
        $this->apiReturn(200, $data, 'success');
    }

    /**
     * 模糊匹配对方商户
     * Create by zhangyangzhen
     * Date: 2018/5/7
     * Time: 15:35
     */
    public function getPartner()
    {
        $srch = \safe_str(I('srch'));

        $limit       = intval(I('limit')) ?: 20;
        $memberModel = new MemberRelationship($this->memberId);
        $field       = ['distinct m.id as fid', 'm.account', 'm.dname'];
        $data        = $memberModel->getRelevantMerchants($srch, $field, $limit);
        $data        = $data ?: [];
        $this->apiReturn(200, $data, '操作成功');
    }
}