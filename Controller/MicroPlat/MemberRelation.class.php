<?php
/**
 * 合作关系处理
 *
 * <AUTHOR>  Li
 * @time   2021-04-27
 */

namespace Controller\MicroPlat;

use Business\Member\MemberPrivacy as MemberPrivacyBiz;
use Business\Member\MemberRelation as MemberRelationBiz;
use Library\Cache\Cache as Cache;

class MemberRelation extends Common
{
    private $_sid      = null;
    private $_memberId = null;

    public function __construct()
    {
        parent::__construct();

        $this->_sid      = $this->_loginMember['sid'];
        $this->_memberId = $this->_loginMember['memberID'];
        if (!$this->_sid) {
            $this->returnAutoLogin('请先登录');
        }
        //如果是员工判断员工账号当前状态
        $this->checkStaffStatus();
    }

    /**
     * 获取分销商
     * <AUTHOR>  Li
     * @date  2021-04-27
     *
     * @return array
     */
    public function getDistributorInfoByPhoneOrAccount()
    {
        $memberId = $this->_loginMember['sid'];

        //暂时做下请求频率的限制，一个用户1秒只能请求一次
        //这个家伙通过机器找了一批的手机号来刷这个接口，然会把没有添加为供应商的手机号之类的给记录到csv里面去，然会通过批量导入功能把给这些人发要求短信
        $limitKey = "platform:get_distributor:{$memberId}";

        $cacheObj = Cache::getInstance('redis');
        $lockTime = 1;
        $lockInfo = $cacheObj->lock($limitKey, 1, $lockTime);
        if (!$lockInfo) {
            $this->apiReturn(500, [], '请求过于频繁');
        }

        $vcode    = I('get.vcode', '', 'strval');
        $token    = I('get.token', '', 'strval');//验证码token
        $type     = I('get.type', 0, 'intval');//1是联系人；2手机号
        $value    = I('get.value', '', 'strval');//验证值
        $account  = I('get.account', '', 'strval');//多账户校验通过后 将对应的账号带入
        $version  = I('get.version', 'v1', 'strval');//版本号

        $verifData = (new MemberPrivacyBiz())->verifCodeParamsFormat($this->_loginMember['sid'], $this->_loginMember['memberID'], $vcode, $token, $type, $value);

        $searchInfo = I('get.key_word', '');
        $searchType = I('get.search_type', -1, 'intval');   //搜索类型 -1默认 1账号 2手机号 3企业/个人实名
        if (empty($searchInfo)) {
            $this->apiReturn(204, [], '请输入关键字');
        }

        $memberRelationBiz = new MemberRelationBiz();
        $result            = $memberRelationBiz->getDistributorInfoByPhoneOrAccount($memberId, $searchInfo, $verifData, $searchType, $account, $version);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 添加分销商
     * <AUTHOR>  Li
     * @date  2021-04-27
     *
     * @return array
     */
    public function addDistributorMemberRelation()
    {
        $memberInfo = $this->_loginMember;
        $sonId      = I('post.son_id', 0);
        $groupId    = I('post.group_id', 0);
        $notice     = I('post.notice', 1);   // 1短信 2微信
        $remark     = I('post.remark', '');   // 备注信息

        if (!is_numeric($sonId) || $sonId <= 0) {
            $this->apiReturn(204, [], '参数有误');
        }

        //隐私验证
        $vcode    = I('post.vcode', '', 'strval');
        $token    = I('post.token', '', 'strval');//验证码token

        $memberPrivacyBiz = new MemberPrivacyBiz();
        //格式化参数
        $verifData = $memberPrivacyBiz->verifCodeParamsFormat($memberInfo['sid'], $memberInfo['memberID'], $vcode, $token);
        //验证信息
        $verifResult = $memberPrivacyBiz->addDistributorPrivacyAuth($verifData);
        if ($verifResult['code'] != 200) {
            $this->apiReturn($verifResult['code'], $verifResult['data'], $verifResult['msg']);
        }

        $memberRelationBiz = new MemberRelationBiz();
        $result            = $memberRelationBiz->addDistributor($memberInfo['sid'], $sonId, $memberInfo['memberID'],
            $groupId, true, $notice, $remark, '微平台H5');
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }

    /**
     * 根据供应商id获取邀请码的状态,不做任何修改
     * <AUTHOR>  Li
     * @date  2021-04-27
     */
    public function getInviteCodeStateAndNotModify()
    {

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->getInviteCodeStateAndNotModify($this->_sid);

        if (!isset($result['code']) || $result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(200, $result['data'], '获取成功');
    }

    /**
     * 根据供应商id设置邀请码审核状态
     * <AUTHOR>  Li
     * @date  2020-07-20
     */
    public function setWithCheck()
    {
        //是否需要审核  0不需要 1需要
        $withCheck = I('post.with_check', 1, 'intval');
        $withCheck = $withCheck === 1 ? true : false;

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->setWithCheck($this->_sid, $withCheck);

        if (!isset($result['code']) || $result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(200, [], '设置成功');
    }

    /**
     * 设置被添加成为分销商审核状态
     * \Controller\Member\DistributorAudit::setAuditStatus
     * 直接拿，未修改
     * <AUTHOR>
     * @date 2021/6/15
     *
     */
    public function setAuditStatus()
    {
        //验证登录
        $this->isLogin();
        $memberId = $this->_loginMember['memberID'];
        $dtype    = $this->_loginMember['dtype'];
        if (!in_array($dtype, [0, 1])) {          //只能供应商 和分销商
            $this->apiReturn(401, '', '没有权限');
        }
        $status = I('status'); //否需要审核：0=不要，1=要
        if ($status === "") {
            $this->apiReturn(400, '', '参数不能为空');
        }

        if (!in_array($status, [0, 1])) {
            $this->apiReturn(400, '', '参数不合法');
        }

        $data = [
            'distribution_check' => $status,
        ];

        $memberBiz = new \Business\Member\Member();
        $res       = $memberBiz->updateMemberExtInfo($memberId, $data);
        if ($res) {
            $this->apiReturn(200, ['status' => $status], '保存成功');
        } else {
            $this->apiReturn(500, ['status' => $status], '保存失败');
        }
    }

    /**
     * 重新生成供应商账号对应的邀请码
     * <AUTHOR>  Li
     * @date  2021-04-27
     */
    public function regenerateInviteCode()
    {
        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->regenerateInviteCode($this->_sid);

        if (!isset($result['code']) || $result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(200, $result['data'], '获取成功');
    }

    /**
     * 创建账号添加分销商
     * <AUTHOR> Li
     * @date  2021-04-27
     */
    public function addAccountRegisterDistributor()
    {
        $dname   = I('post.dname', '', 'strval');
        $mobile  = I('post.mobile', '', 'strval');
        $groupId = I('post.group_id', 0, 'intval');

        if (empty($dname)) {
            $this->apiReturn(203, [], '请填写实名');
        }

        $relationBiz = new \Business\Member\MemberRelation();
        $result      = $relationBiz->addAccountRegisterDistributor($this->_sid, $dname, $mobile, $groupId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 邀请码添加分销商
     * <AUTHOR> Li
     * @date   2021-04-27
     *
     * @param  int  $mobile  手机号
     * @param  string  $vcode  短信验证码
     */
    public function applyCreateMemberRelationByInviteCode()
    {
        $sid        = I('post.sid', 0, 'intval');        //供应商id
        $fid        = I('post.son_id', 0, 'intval');     //分销商id
        $inviteCode = I('post.invite_code', '', 'trim'); //邀请码

        //需要审核发起邀请码审核 否则直接建立分销关系
        $memberRelationBiz = new \Business\Member\MemberRelation();
        $addRes            = $memberRelationBiz->addDistributorByInviteCode($sid, $fid, $fid, $inviteCode);

        if ($addRes['code'] == 200) {
            $this->apiReturn(200, $addRes['data'], $addRes['msg']);
        } else {
            $this->apiReturn($addRes['code'], $addRes['data'], $addRes['msg']);
        }
    }

    /**
     * 添加平台未注册用户的分销商
     * <AUTHOR> Li
     * @date  2021-04-27
     *
     * @return array
     */
    public function addUnRegisterDistributor()
    {
        $mobile  = I('post.mobile', '', 'string');
        $groupId = I('post.group_id', 0, 'intval');
        if (!$mobile || !ismobile($mobile)) {
            $this->apiReturn(204, [], '电话号码错误');
        }
        if ($groupId < 0) {
            $this->apiReturn(204, [], '组id错误');
        }

        $sDname = $this->_loginMember['sdname'] ?? (new \Business\Member\Member())->getInfo($this->_sid)['dname'];

        $memberRelationBiz = new MemberRelationBiz();
        $res               = $memberRelationBiz->addUnRegisterDistributor($mobile, $this->_sid, $groupId, $sDname,
            $this->_memberId, true, '', '微平台H5');
        if (isset($res['code'])) {
            $this->apiReturn($res['code'], $res['data'], $res['msg']);
        } else {
            $this->apiReturn(500, [], '服务异常');
        }
    }
    /**
     * 获取ota对应是否添加成为分销商
     * <AUTHOR> Li
     * @date   2021-05-07
     */
    public function getOtaDisStatus()
    {
        $memberBiz = new \Business\Member\MemberRelation();
        $result    = $memberBiz->getOtaStatus($this->_sid);

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        $this->apiReturn(500, [], '接口异常！');
    }

    /**
     * 获取分销商价格分组列表
     *
     * @param  int  $sid  当前用户id
     *
     * @return array
     */
    public function getGrpList()
    {
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 300, 'intval');
        $relationBiz = new \Business\Member\MemberEvoluteRelation();
        $result      = $relationBiz->queryGroupNameListBySidAndGroupName($this->_sid, '', $size, $page);

        $groupInfo  = [];
        if ($result['code'] == 200 && !empty($result['data']['list'])) {
            foreach ($result['data']['list'] as $group) {
                $groupInfo[] = [
                    'id'          => $group['group_id'],
                    'name'        => $group['group_name'],
                    'landCount'   => 0,
                    'ticketCount' => 0,
                ];
            }
        }

        $this->apiReturn(200, $groupInfo, 'success');
    }

    /**
     * 获取邀请海报相关信息
     * <AUTHOR> Li
     * @date 2021-05-12
     *
     * @return array
     */
    public function posterInfo()
    {
        $type      = I('type', 1, 'intval');
        $posterBis = new \Business\Member\MemberPoster();
        $posterRes = $posterBis->getPosterInfo($this->_sid, $type);

        if (!$posterRes || $posterRes['code'] != 200) {
            $this->apiReturn($posterRes['code'], $posterRes['data'], $posterRes['msg']);
        }


        $posterRes['data']['dname'] = (new \Business\Member\Member())->getInfo($this->_sid)['dname'];

        $this->apiReturn(200, $posterRes['data'], '成功');
    }
}
