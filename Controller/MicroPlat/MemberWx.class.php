<?php
/**
 * 微平台微信相关
 * <AUTHOR>
 * @date   2022/6/23
 */

namespace Controller\MicroPlat;

use Controller\MicroPlat\Common as Common;
use Business\Member\MemberWx as MemberWxBiz;

class MemberWx extends Common
{
    public function __construct()
    {
        parent::__construct();
        $memberId = $this->getMemberId();
        if (empty($memberId)) {
            $this->returnAutoLogin('请先登录');
        }

    }

    /**
     * 获取微信绑定信息
     * <AUTHOR>
     * @date   2022/6/23
     *
     */
    public function getWxBindInfo()
    {
        $memberId = $this->_loginMember['memberID'] ?? 0; //当前账号id
        $openId   = $this->_loginMember['openid'] ?? ''; //微信openid

        if (!$memberId || empty($openId)) {
            $this->apiReturn(204, [], '绑定信息不存在');
        }

        $data = [
            'is_bind'    => false, //是否绑定
            'nickname'   => '', //昵称
            'headimgurl' => '', //头像
            'alias'      => '', //备注
        ];

        $res  = (new MemberWxBiz())->getCurrentBind($memberId, $openId);
        if (!empty($res['data'])) {//不为空，说明已绑定
            $data['is_bind']    = true;
            $data['nickname']   = $res['nickname'] ?? '';
            $data['headimgurl'] = $res['headimgurl'] ?? '';
            $data['alias']      = $res['alias'] ?? '';
        }

        $this->apiReturn($res['code'], $data, $res['msg']);
    }
}