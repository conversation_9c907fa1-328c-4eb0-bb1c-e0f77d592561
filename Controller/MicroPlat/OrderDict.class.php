<?php

namespace Controller\MicroPlat;

use Library\Controller;

class OrderDict extends Controller
{
    /**
     * 订单状态释义
     * <AUTHOR>
     * @date   2020-04-17
     */
    public function orderStatus()
    {
        $orderStatusBiz = new \Business\Order\OrderStatus();
        $data           = $orderStatusBiz->getAllTypeOrderStatusMap();

        $this->apiReturn(self::CODE_SUCCESS, $data);
    }
}