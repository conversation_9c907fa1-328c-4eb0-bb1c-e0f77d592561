<?php
/**
 * 微平台产预定列表
 * Created by PhpStorm.
 * User: luo<PERSON><PERSON>
 * Date: 2017/9/27 0027
 * Time: 11:48
 */

namespace Controller\MicroPlat;

use Business\Authority\DataAuthLogic;
use Business\CommodityCenter\Ticket;
use Business\JavaApi\ListService\LandListQueryService;
use Business\Member\MemberRelation;
use Business\Order\OrderBook;
use Business\JavaApi\Ticket\TicketPrice;
use Business\Product\ProductList as businessLib;
use Library\Tools;
use Library\Tools\Helpers as Helpers;
use Model\Member\Member;
use Model\Product\Land;
use Business\JavaApi\TicketApi;
use \Business\MultiDist\Product as DistProductBus;

class ProductList extends Common
{
    private static $_allowType = ['A', 'B', 'C', 'F', 'H', 'I', 'G', 'K', 'J'];

    public function __construct()
    {
        parent::__construct();
        //$this->_supplyId = 3385;
    }

    /**
     * 获取产品预定列表
     *
     * @date   2017-09-27
     * <AUTHOR>
     *
     * @return string
     */
    public function getList()
    {
        $memberId = $this->getMemberId();
        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        //判断是否走独立域名  lipeijun  2018-7-2
        $isDomain = false;
        $domainId = '';

        $pageNo   = I('page_no', 1, 'intval');
        $pageSize = I('page_size', 3, 'intval');
        $city     = I('product_city', '', 'strval');
        $topic    = I('product_topic', '', 'strval');
        $type     = I('product_type', '', 'strval');
        $name     = I('product_name', '', 'strval');
        $channel  = I('channel', '', 'intval');
        //供应商名称
        $searchSupplier = I('supplier_name', '', 'strval');

        $orderBySales = I('orderBySales', null);
        $isAddTimeAsc = I('isAddTimeAsc', null);
        $sids         = I('sids', null);//供应商ID筛选数组

        $province     = I('province', '', 'strval');

        if ($type !== '' && !in_array($type, self::$_allowType)) {
            $this->apiReturn(203, [], '类型参数有误');
        }

        if (!is_null($orderBySales)) {
            $orderBySales = boolval($orderBySales);
        }

        //if (!is_null($isAddTimeAsc)) {
        //    $isAddTimeAsc = $isAddTimeAsc;
        //}

        /*$applyId = $this->_supplyId;
        if ($this->_loginMember['dtype'] == 6) {
            $this->_supplyId = $memberId;
        }*/

        //如果登录的是资方  仅展示资方对应景点的产品
        $landId = '';
        if ($this->_loginMember['dtype'] == 2) {
            //$land        = new Land();
            //$land_id_arr = $land->getLandInfoBySalerId($this->_loginMember['account'], false, 'id');

            $landApi     = new \Business\CommodityCenter\Land();
            $land_id_arr = $landApi->queryLandMultiQueryBySalerid([$this->_loginMember['account']])[0];
            $landId      = $land_id_arr['id'];
        }

        // 商户公众号 不需登录的情况 例如来自三亚会员卡
        // 获取当前商户公众号所能销售的所有产品
        // 当前登录分销商或供应商身份的所有产品

        if ($this->_supplyAccount == 124078 || $this->_supplyAccount == 589638) {
            //if ($memberId == $this->_supplyId) {
            //$applyId = '';

        } else {
            //$applyId = $this->_supplyId;
            /*微平台独立域名不走该验证  该验证移动到三亚中判断
             $checkRes = businessLib::auth($this->_supplyId, $memberId);

            if ($checkRes[0] != 0) {
                $this->apiReturn($checkRes[0], [], $checkRes[1]);
            }*/

            $isDomain = true;
        }

        //三亚中 默认真实渠道是4
        $realChannel = 4;
        //三亚的仅显示会员卡渠道的产品
        if ($this->_supplyId == 55) {

            //三亚判断供应商和登录这之间的分销关系
            $checkRes = businessLib::auth($this->_supplyId, $memberId);

            if ($checkRes[0] != 0) {
                $this->apiReturn($checkRes[0], [], $checkRes[1]);
            }

            //三亚的需要判断渠道  如果有传渠道进来   可以在微平台上下单
            if ($channel != '') {
                $realChannel = $channel;
            }
            //只要是三亚的 都走会员卡接口
            $this->_channel = 4;

            $noBook = true;
            //针对三亚的-判断是否有绑定，没有绑定当做没登录处理
            $wxMemberModel = new \Model\Wechat\WxMember();
            $bindInfo      = $wxMemberModel->hasBindedWechat(intval($memberId), $this->_supplyAppId);
            if (!$bindInfo) {
                $this->returnAutoLogin('请先登录');
            }
        } else {
            $noBook = false;
        }

        if ($type == 'I' && $this->_channel == 10) {
            $type = 'Z';
        } elseif ($type == '' && $this->_channel == 10) {
            $type = 'A,B,C,F,G,H,J,K';
        }

        //这里不用考虑是否登陆，memberID都是唯一的
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        $condition = $dataAuthLimit->transInOrNotCondition();
        if ($condition === false) {
            $this->apiReturn(201, [], '暂无可售商品');
        }

        // 商户公众号 不需登录的情况 例如来自三亚会员卡
        // 获取当前商户公众号所能销售的所有产品
        // 当前登录分销商或供应商身份的所有产品
        //走原有获取产品列表接口
        if (!$isDomain) {
            //非独立域名时 判断渠道
            if ($this->_supplyId == 55) {
                $this->_channel = $realChannel;
            }
            $result = businessLib::getListV2(
                $memberId,
                $pageNo,
                $pageSize,
                $saleChannel = $this->_channel,
                $name,
                $type,
                $productStar = '',
                $topic,
                $province,
                $city,
                '',
                $searchSupplier,
                0,
                null,
                $orderBySales,
                $sids,
                $isAddTimeAsc,
                0,
                0,
                $condition
            );
        } else {
            switch ($this->_channel) {
                //会员卡
                case 4:
                    //走独立域名会员卡渠道接口
                    $result = businessLib::getMemberCardList(
                        $this->_supplyId,
                        $memberId,
                        $province,
                        $city,
                        $name,
                        $type,
                        $topic,
                        $productStar = '',
                        $pageSize = 5,
                        $pageNo,
                        $landId,
                        $realChannel,
                        $searchSupplier,
                        $condition
                    );
                    break;
                default:
                    //走独立域名接口
                    $result = businessLib::getDomainList(
                        $this->_supplyId,
                        $memberId,
                        $pageNo,
                        $pageSize,
                        $saleChannel = $this->_channel,
                        $name,
                        $type,
                        $productStar = '',
                        $topic,
                        $province,
                        $city,
                        $landId,
                        '',
                        $searchSupplier,
                        0,
                        $orderBySales,
                        $sids,
                        $isAddTimeAsc,
                        0,
                        $condition
                    );
            }

        }

        if (empty($result['lands'])) {
            $this->apiReturn(201, $result, '暂无可售商品');
        }

        //如果这里都把门票过滤了呢？
        foreach ($result['lands'] as $key => &$val) {
            foreach ($val['ticket'] as $ticketKey => $ticketVal) {
                // 泰山指定产品, 不允许在微平台显示
                if (Helpers::ticketIdCheck($ticketVal['tid'], 'web') === false) {
                    unset($val['ticket'][$ticketKey]);
                }
                $val['ticket'][$ticketKey]['pre_sale'] = $ticketVal['preSale'];
            }

            if (empty($val['ticket'])) {
                unset($result['lands'][$key]);
            } else {
                $val['ticket'] = array_values($val['ticket']);
            }
        }

        //pc平台预定页更多 添加 是否设置了不同价格 票说明，提前预订，延迟验证
        $productBuz      = new \Business\Product\Product();
        $result['lands'] = $productBuz->handleReturnConfigMuchPriceList($result['lands']);

        $result['currentPage'] = $pageNo;
        $result['ifbook']      = $noBook;

        //添加微商城链接
        $result['lands'] = $productBuz->addhandleWxMallUrl($this->_loginMember['saccount'], $result['lands']);

        $this->apiReturn(200, $result, 'success');
    }

    /**
     * 下拉获取门票信息  (该接口已经弃用)
     *
     * @date   2017-09-20
     * <AUTHOR> Lan
     *
     * @return array
     */
    public function getTickets()
    {
        $memberId = $this->getMemberId();

        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        $productIds = I('post.product_ids', '', 'strval');
        $aid        = I('post.aid', '', 'intval');

        if (!$aid || !$productIds) {
            $this->apiReturn(201, [], '参数有误');
        }

        $checkRes = businessLib::auth($aid, $memberId);
        if ($checkRes[0] !== 0) {
            $this->apiReturn($checkRes[0], [], $checkRes[1]);
        }

        //$params['operator_id'] = $this->_memberId;

        $result = businessLib::getTickets($memberId, $aid, $productIds);

        if ($result === false) {
            $this->apiReturn(201, $result, '没有相关数据');
        }
        $this->apiReturn(200, ['list' => $result], 'success');
    }

    /**
     * 下拉获取更多门票信息
     *
     * @date   2018-07-10
     * <AUTHOR>
     *
     * @return array
     */
    public function getMoreTicket()
    {
        $memberId = $this->getMemberId();

        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        $productIds  = I('post.ticket_ids', '', 'strval');
        $aid         = I('post.superier_id', '', 'intval');
        $isMultiDist = I('post.is_multi_dist', 0, 'intval');//是否来自分销专员 0=不是 1=是

        if (!$aid || !$productIds) {
            $this->apiReturn(201, [], '参数有误');
        }

        //分销专员获取数据记为运营商
        $multiDist = (new \Business\MultiDist\Member())->getMultiDistCache($this->_loginMember['sid']);
        if ($isMultiDist === 1 && !empty($multiDist['multi_dist'])) {
            $memberId = $this->updateOrderMemberNew($multiDist['multi_dist']['uuid'], true);
        }

        $checkRes = businessLib::auth($aid, $memberId);
        if ($checkRes[0] !== 0) {
            $this->apiReturn($checkRes[0], [], $checkRes[1]);
        }
        //针对三亚的更多票接口
        if ($this->_supplyId == 55) {
            $result = businessLib::getSanyaTickets($memberId, $aid, $productIds);
        } else {
            $result = businessLib::getMoreTicket($memberId, $aid, $productIds);
        }

        if ($result === false) {
            $this->apiReturn(201, $result, '没有相关数据');
        }

        if ($result) {
            //pc平台预定页更多 添加 是否设置了不同价格 票说明，提前预订，延迟验证
            $productBuz = new \Business\Product\Product();
            $result     = $productBuz->handleReturnConfigMuchPriceListMore($result);
        }

        //分销专员预订页面相关参数处理
        if ($isMultiDist === 1) {
            $result = $this->addMultiDistParams($result, $aid);
        }

        //标签处理
        $result = $this->handleMoreNewTag($result, (int)$memberId, (int)$aid);

        $this->apiReturn(200, ['list' => $result], 'success');

    }

    /**
     * 更多的票标签添加处理
     * <AUTHOR>
     * @date 2021/1/27
     *
     * @param  array  $result 要处理的数据
     * @param  int  $memberId 用户ID
     * @param  int  $aid 上级用户ID
     *
     * @return array
     */
    public function handleMoreNewTag(array $result, int $memberId, int $aid)
    {
        if (empty($result)) {
            return $result;
        }

        $tidArr = array_column($result, 'tid');

        $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
        $ticketRes          = $commodityTicketBiz->queryTicketInfoByIds($tidArr);
        if (!$ticketRes) {
            return [];
        }

        $ticketList = [];
        $ticktExtList=  [];

        foreach ($ticketRes as $key => $value) {
            $ticketInfo = isset($value['ticket']) ? $value['ticket'] : [];
            $landInfo   = isset($value['land']) ? $value['land'] : [];
            $extInfo    = isset($value['ext']) ? $value['ext'] : [];
            $landfInfo  = isset($value['land_f']) ? $value['land_f'] : [];
            //故意把$ticketInfo放在最后覆盖其他的 id
            $ticketList[$value['ticket']['id']] = array_merge($landInfo, $extInfo, $landfInfo, $ticketInfo);

            $ticktExtList[$value['ticket']['id']] = $extInfo;
        }

        $ticketBiz = new \Business\Product\Ticket();

        $packBiz  = new \Business\Product\PackTicket();
        foreach ($result as $listKey => &$listTmp) {
            $usetimeNew = $entranceTimeLimit = $refundRuleNew = '';
            switch ($listTmp['p_type']) {
                case 'F':

                    //退票标签
                    $refundRuleNew = OrderBook::handleRefundRule(0, $listTmp['refundRule'], $listTmp['refundEarlyTime']);

                    //套票特殊处理
                    $childData = $packBiz->getChildTickets($listTmp['tid']);
                    if (!empty($childData)) {

                        $sonPidArr            = array_column($childData, 'pid');
                        //有效期说明
                        $usetimeNew   = OrderBook::parsePackValidTag($sonPidArr);

                        //处理套票退款规则标签，主票不可退即不可退，就不考虑子票的情况了
                        if ($listTmp['refundRule'] != 2) {
                            $sonTidArr              = array_column($childData, 'tid');
                            $refundRuleNew = OrderBook::parsePackRefundTag($sonTidArr, $memberId, 10, $aid);
                        } else {
                            $refundRuleNew = '不可退';
                        }

                    }
                    break;
                case 'C':
                    //酒店
                    $validData                = [
                        'type'                      => $listTmp['p_type'],
                        'valid_period_start'        => $listTmp['validStartDateStr'],
                        'valid_period_end'          => $listTmp['validEndDateStr'],
                        'valid_period_type'         => $listTmp['validType'],
                        'use_early_days'            => $listTmp['useEarlyDays'],
                        'valid_period_days'         => $listTmp['validDays'],
                        'valid_period_timecancheck' => $listTmp['ifVerify'],
                        'delaytype'                 => $listTmp['delayType'],
                    ];
                    //有效期说明
                    $usetimeNew = OrderBook::getHotelValidityDate($validData);
                    //退票标签
                    $refundRuleNew = OrderBook::handleRefundRule(0, $listTmp['refundRule'], $listTmp['refundEarlyTime']);
                    break;
                case 'H':
                    //演出
                    if (empty($listTmp['entranceTimeLimit'])) {
                        $entranceTimeLimit = '开演前60分钟可入场';
                    } else {
                        $listTmp['entranceTimeLimit'] = json_decode($listTmp['entranceTimeLimit'], true);
                        $entranceTimeLimit = '开演前' . $listTmp['entranceTimeLimit']['s'] . '分钟可入场';
                    }

                    $listTmp['advance_booking_str'] = $ticketBiz->advanceBookingTag($ticketList[$listTmp['tid']]);
                    break;
                case 'G':
                    //餐饮
                    $validData                = [
                        'type'                      => $listTmp['p_type'],
                        'valid_period_start'        => $listTmp['validStartDateStr'],
                        'valid_period_end'          => $listTmp['validEndDateStr'],
                        'valid_period_type'         => $listTmp['validType'],
                        'use_early_days'            => $listTmp['useEarlyDays'],
                        'valid_period_days'         => $listTmp['validDays'],
                        'valid_period_timecancheck' => $listTmp['ifVerify'],
                        'delaytype'                 => $listTmp['delayType'],
                    ];
                    //有效期说明
                    $usetimeNew = OrderBook::getFoodValidityDate($validData);
                    //退票标签
                    $refundRuleNew = OrderBook::handleRefundRule(0, $listTmp['refundRule'], $listTmp['refundEarlyTime']);
                    break;
                default:
                    $tmpExtInfo = $ticktExtList[$listTmp['tid']] ?? [];

                    $validData = [
                        'type'                      => $listTmp['p_type'],
                        'valid_period_start'        => $listTmp['validStartDateStr'],
                        'valid_period_end'          => $listTmp['validEndDateStr'],
                        'valid_period_type'         => $listTmp['validType'],
                        'use_early_days'            => $listTmp['useEarlyDays'],
                        'valid_period_days'         => $listTmp['validDays'],
                        'valid_period_timecancheck' => $listTmp['ifVerify'],
                        'delaytype'                 => $listTmp['delayType'],
                        'pre_sale'                  => $listTmp['pre_sale'],
                        'ext'                       => $tmpExtInfo,
                    ];

                    //有效期说明
                    $usetimeNew = OrderBook::getValidityDate($validData);
                    //退票标签
                    $refundRuleNew = OrderBook::handleRefundRule(0, $listTmp['refundRule'], $listTmp['refundEarlyTime']);
            }

            $listTmp['usetimeNew']          = $usetimeNew;
            $listTmp['refundRuleNew']       = $refundRuleNew;
            $listTmp['entrance_time_limit'] = $entranceTimeLimit;
        }

        return $result;
    }

    /**
     * 分销专员更多票参数处理
     * <AUTHOR>
     * @date 2020/6/19
     *
     * @param  array $params 票列表
     * @param  int $aid 上级供应商ID
     *
     * @return array
     */
    public function addMultiDistParams(array $params, int $aid)
    {
        if (empty($params)) {
            return $params;
        }
        $multiDist = (new \Business\MultiDist\Member())->getMultiDistCache($this->_loginMember['sid']);
        if (empty($multiDist['multi_dist'])) {
            return $params;
        }

        $paramsTids = array_column($params, 'tid');
        $ticketBus  = new Ticket();
        $ticketRes  = $ticketBus->batchQueryTicketByTicketIds($paramsTids);
        if (empty($ticketRes)) {
            return $params;
        }

        //佣金比例数据获取
        $distMemberModel    = new \Model\MultiDist\Member();
        $distProductBus     = new \Business\MultiDist\Product();
        $tids               = array_column($params, 'tid');
        $chainRes           = $distProductBus->getUuidInfo($multiDist['multi_dist']['uuid']);
        if (empty($chainRes)) {
            return $params;
        }
        $topUuid        = $distProductBus->getTopUuidByUid($chainRes['top_uid']);
        $profitConfig   = $distProductBus->handleProfitFromLid($topUuid, $tids, $aid);
        $params         = $distProductBus->filterDataForDist($params, $multiDist['multi_dist']['uuid'], 'tid', 'aid');

        //获取一次门票扩展属性
        $ticketArr = (new \Business\CommodityCenter\Ticket())->queryTicketInfoByIds($tids);
        $ticketExt = [];
        foreach ($ticketArr as $ticket) {
            $ticketExt[$ticket['ticket']['id']] = $ticket['ext'];
        }

        $ticketMap = array_key($ticketRes, 'id');
        foreach ($params as $listKey => &$listTmp) {
            $listTmp['refund_rule_new'] = '';
            $listTmp['profit']          = 0;

            //佣金参数
            if (!empty($profitConfig)) {
                //浮点数转换下在处理防止精度缺失
                $costPrice          = intval($listTmp['settlement']);
                $counterPrice       = intval($listTmp['retail']);
                $rate               = json_decode($profitConfig[$listTmp['tid']], true);
                $rate               = $rate[$multiDist['multi_dist']['level']] ?? 0;
                $profit             = round(($counterPrice - $costPrice) * ($rate / 100));
                $listTmp['profit']  = $profit;
            }

            if (!isset($ticketMap[$listTmp['tid']])) {
                continue;
            }
            $listMap = $ticketMap[$listTmp['tid']];

            if (isset($listMap['refund_audit']) && isset($listMap['refund_rule']) && (isset($listMap['refund_early_time']) || isset($ticketExt[$listTmp['tid']]['refund_after_time']))) {
                $message = OrderBook::handleRefundRule($listMap['refund_audit'], $listMap['refund_rule'],
                    $listMap['refund_early_time'] ?? 0, $ticketExt[$listTmp['tid']]['refund_after_time'] ?? 0);
                //退票说明
                $listTmp['refund_rule_new'] = $message;
            }
        }

        return $params;
    }

    /**
     * 获取产品海报
     * <AUTHOR>
     * @date 2020/4/8
     *
     * @return json
     */
    public function poster()
    {
        $lid = I('post.lid', '', 'intval');//景区id
        $aid = I('post.aid', '', 'intval');//顶级供应商ID
        $sid = I('post.sid', '', 'intval');//上级供应商ID

        if (!$lid || !$sid || !$aid) {
            $this->apiReturn(203, [], '参数缺失!');
        }

        $memberId = $this->getMemberId();

        $productBus             = new DistProductBus();
        $res                    = $productBus->getProductPoster($memberId, $lid, $sid, $aid);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取供应商列表
     * <AUTHOR>
     * @date 2021/3/11
     *
     * @return array
     */
    public function querySidByFid()
    {
        $pageNum  = I('post.pageNum', 0, 'intval');//页码
        $pageSize = I('post.pageSize', 0, 'intval');//当前页

        if (empty($pageNum) || empty($pageSize)) {
            $this->apiReturn(203, [], '参数缺失!');
        }

        $memberId = $this->getMemberId();
        $memberRelationBiz = new MemberRelation();
        $result = $memberRelationBiz->querySidByFid($memberId, $pageNum, $pageSize);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 查询分销店铺产品
     * <AUTHOR>
     * @date 2021/3/18
     *
     * @return array
     */
    public function queryEvoluteLandList()
    {
        $data = I('post.');
        if (empty($data)) {
            $this->apiReturn(203, [], '参数缺失!');
        }

        $memberId          = $this->getMemberId();
        $data['fid']       = $memberId;
        $data['pTypeList'] = ['A', 'B', 'C', 'F', 'G', 'H', 'J', 'K'];
        $data['shop']      = 10;

        // 自供应门店商品列表受权限控制
        if ($memberId == $data['sid']) {
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
            $condition = $dataAuthLimit->transInOrNotCondition();
            if ($condition === false) {
                $this->apiReturn(200, ['list' => []]);
            }
            $data = array_merge($data, $condition);
        }

        $landListQueryService = new LandListQueryService();
        $result               = $landListQueryService->queryEvoluteLandList($data);
        if ($result['code'] != 200 || empty($result['data']['list'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }
        //是否设置了不同价格
        $result['data']['list'] = $this->handleReturnConfigMuchPriceList($result['data']['list']);

        //添加微商城链接
        $productBuz             = new \Business\Product\Product();
        $result['data']['list'] = $productBuz->addhandleEvoluteWxMallUrl($data['sid'], $this->_loginMember['saccount'],
            $result['data']['list']);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 是否设置了不同价格
     * @Author: xwh
     * @date: 2020-07-15
     *
     * @param  array $param
     *
     * @return array
     */
    public function handleReturnConfigMuchPriceList(array $param)
    {
        if (empty($param)) {
            return $param;
        }
        $ticketApi  = new TicketPrice();
        $ticketTids = [];
        foreach ($param as $key => $item) {
            if (!empty($item['tickets'])) {
                foreach ($item['tickets'] as $k => $v) {
                    $ticketTids[] = $v['tid'];
                }
            }
        }

        // 根据ticketIds 查询返回当前时间有效期,有配置多个价格的票列表
        $calendar = $ticketApi->queryReturnConfigMuchPriceTicketId($ticketTids);
        if ($calendar['code'] != 200) {
            return $param;
        }

        foreach ($param as $key => &$item) {
            if (!empty($item['tickets'])) {
                foreach ($item['tickets'] as $k => &$v) {
                    //是否设置了不同价格
                    $dayPrice = 0;
                    if ($calendar['data']) {
                        $dayPrice = in_array($v['tid'], $calendar['data']) ? 1 : 0;
                    }
                    $v['day_price'] = $dayPrice;
                }
            }
        }

        return $param;
    }

    /**
     * 模糊查询产品
     * <AUTHOR>
     * @date 2021/3/18
     *
     * @return array
     */
    public function queryEvoluteLandTitleList()
    {
        $data  = I('post.');
        if (empty($data)) {
            $this->apiReturn(203, [], '参数缺失!');
        }

        $memberId          = $this->getMemberId();
        $data['fid']       = $memberId;
        $data['pTypeList'] = ['A', 'B', 'C', 'F', 'G', 'H', 'J', 'K'];
        $data['shop']      = 10;

        $landListQueryService = new LandListQueryService();
        $result = $landListQueryService->queryEvoluteLandTitleList($data);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取短链接
     * <AUTHOR>
     * @date 2020/6/23
     *
     * @return array
     */
    public function shortUrl()
    {
        $url = I('post.url', '', 'strval');//链接

        if (empty($url)) {
            $this->apiReturn(203, [], '参数缺失!');
        }

        $shortUrl = Tools::transformShortUrl($url);
        if ($shortUrl === false) {
            $this->apiReturn(400, [], '短链接生成失败!');
        }
        $this->apiReturn(200, ['url' => $shortUrl], '');
    }

    /**
     * 查询分销店铺产品查询更多票
     * <AUTHOR>
     * @date 2021/11/02
     *
     */
    public function queryEvoluteMoreTicket()
    {
        $memberId = $this->getMemberId();
        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        $sid                  = I('post.sid', 0, 'intval');
        $tids                 = I('post.tids', []);
        $landListQueryService = new LandListQueryService();
        $result               = $landListQueryService->queryEvoluteMoreTicket($memberId, $sid, $tids);
        $ticketApi            = new TicketPrice();
        $ticketTids           = array_column($result['data'], 'tid');

        // 根据ticketIds 查询返回当前时间有效期,有配置多个价格的票列表
        $calendar = $ticketApi->queryReturnConfigMuchPriceTicketId($ticketTids);
        if ($calendar['code'] == 200) {
            foreach ($result['data'] as $key => &$item) {
                if (!empty($item['tid'])) {
                    //是否设置了不同价格
                    $dayPrice = 0;
                    if ($calendar['data']) {
                        $dayPrice = in_array($item['tid'], $calendar['data']) ? 1 : 0;
                    }
                    $item['day_price'] = $dayPrice;
                }
            }
        }

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}