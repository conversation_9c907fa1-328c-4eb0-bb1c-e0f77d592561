<?php
/**
 * 微平台订单提交页数据获取
 * Created by PhpStorm.
 * User: luoCheng Lan
 * Date: 2017/10/31 0031
 * Time: 10:10
 */

namespace Controller\MicroPlat;

use Business\Finance\AmountLedgerFreeze as AmountLedgerFreezeBiz;
use Business\JavaApi\Product\PftTicketPriceStorage;
use Business\Member\MemberRelation;
use Business\Order\Booking;
use Business\Order\Modify;
use Business\Order\OrderBook as businessLib;
use Business\Member\ContactPerson;
use Business\Member\MemberMoney;
use Business\JavaApi\TicketApi;
use Business\Order\ReservationOrder;
use Business\Product\ProductStorage;
use Business\Product\TicketDeputyBiz;
use Business\Product\Show as ShowBiz;
use Library\Constants\Order\OrderChannel;
use Library\Container;

class OrderBook extends Common
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 预定数据的获取
     *
     * @date   2017-10-31
     * <AUTHOR>
     *
     * @return string
     */
    public function getBookData()
    {
        if ($this->_mustLogin && !$this->_loginMember['memberID']) {
            $this->returnAutoLogin('请先登录');
        }

        $ticketId   = I('post.ticket_id', '', 'intval');
        $applyId    = I('post.apply_id', '', 'intval');
        $channel    = I('post.channel', '', 'intval');
        $landId     = I('post.land_id', '', 'intval');
        //分销专员推广id
        $disId     = I('post.dis_id', '', 'strval');

        if ($ticketId == '' || $applyId == '' || $landId == '') {
            $this->apiReturn(201, '', '景区ID与门票ID和供应商ID必传');
        }

        if ($this->_supplyId == 55) {
            $this->_channel = 4;
        }

        if ($channel) {
            $this->_channel = $channel;
        }

        if ($this->_supplyId == 55 && $this->_channel == 4) {
            $this->apiReturn(201, '', '请用会员卡购买');
        }

        $memberId = $this->getMemberId();

        //分销专员下单的下单人记为运营商
        $disId && $memberId = $this->updateOrderMemberNew($disId, true);

        $requestTicket = 0;
        //后续这里会调用上个版本的验证分销链功能

        //获取门票数据
        if ($this->_supplyId == 55) {
            $data = TicketApi::getBookTicketsByAid($memberId, $applyId, $landId, $this->_channel, $requestTicket);
        } else {
            $data = TicketApi::getBookTickets($memberId, $applyId, $landId, $requestTicket, $this->_channel);
        }

        if ($data['code'] != 200) {
            $this->apiReturn($data['code'], '', $data['sub_msg']);
        }


        $ticketData = $data['data'];
        if (empty($ticketData)) {
            $this->apiReturn(400, [], '产品未通过审核或无权限购买');
        }
        $ticketData = businessLib::handleBookTicket($ticketData, $ticketId, $memberId, $applyId, $this->_channel, ['unifiedStorage' => true]);

        if ($ticketData[0] != 0) {
            $this->apiReturn($ticketData[0], [], $ticketData[1]);
        }

        $ticketData = $ticketData[1];

        $earlyDate = TicketApi::getEarliestTwoDayPriceDate($ticketId, $memberId, $this->_channel, $applyId);

        if (!$earlyDate || empty($earlyDate)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '该产品暂不可售');
        }

        $firstDate  = $earlyDate[0]['date'];
        $secondDate = $earlyDate[1]['date'];

        $contacts = ContactPerson::getContacts($memberId);

        $memberMoney = MemberMoney::getAllMoney($memberId, $applyId, true);

        //可用余额覆盖
        $amountLedgerFreezeBiz = new AmountLedgerFreezeBiz();
        if ($amountLedgerFreezeBiz->isFrozenEnabled($memberId)) {
            $memberMoney['balance'] = money_fmt($amountLedgerFreezeBiz->getAvailableBalance($memberId));
            $frozenEnabled          = true;
        }

        //结算方式
        $memberRelationBus = new MemberRelation();
        $res               = $memberRelationBus->isExsitRecord($applyId, $memberId);
        $clearingWay       = isset($res['clearing_mode']) ? $res['clearing_mode'] : 0;
        //为票附上期票属性
        foreach ($ticketData['tickets'] as &$value) {
            $earlyDateTmp = TicketApi::getEarliestTwoDayPriceDate($value['tid'], $memberId, $this->_channel, $applyId);
            $value['first_date']    = $earlyDateTmp[0]['date'];
            $value['second_date']   = $earlyDateTmp[1]['date'];
        }
        $multiDist = (new \Business\MultiDist\Member())->getMultiDistCache($memberId);
        //分销专员预订页面相关参数处理
        if (!empty($disId) && !empty($multiDist['multi_dist'])) {
            $aidKey = 'aid';
            foreach ($ticketData['tickets'] as &$tickTmp) {
                $tickTmp[$aidKey] = $applyId;
            }
            $distProductBus = new \Business\MultiDist\Product();
            $ticketData['tickets'] = $distProductBus->filterDataForDist($ticketData['tickets'], $multiDist['multi_dist']['uuid'], 'tid', $aidKey);
            if ($ticketData['p_type'] == 'C' || $ticketData['p_type'] == 'H' || $ticketData['p_type'] == 'J') {
                $distOrderBus            = new \Business\MultiDist\Order($memberId);
                $ticketData['tickets']   = $distOrderBus->addMultiDistProfit($ticketData['tickets'], $applyId, 'js', 'ls');
            }
        }
        //自定义标签根据渠道过滤
        $ticketData['tickets'] = (new \Business\Order\OrderBook())->handleCustomTag($ticketData['tickets'],$this->_channel);

        //下单风险提示
        $ticketData['tickets'] = (new \Business\Order\OrderBook())->handleRiskWarning($memberId, $landId, $ticketData['tickets']);

        $responseData = [
            'clearingWay'   => $clearingWay,
            'status'        => 'success',
            'capital'       => $memberMoney,
            'contacts'      => $contacts,
            'frozenEnabled' => $frozenEnabled ?? false,
            'account_pay'   => 0,
            'land'          => array(
                'lid'        => $ticketData['lid'],
                'pay'        => $ticketData['pay'],
                'ltitle'     => $ticketData['land_name'],
                'p_type'     => $ticketData['p_type'],
                'memberSID'  => $memberId,
                'begintime'  => $ticketData['begin_time'],
                'tickets'    => $ticketData['tickets'],
                'firstDate'  => $firstDate,
                'secondDate' => $secondDate,
            ),
        ];

        $this->apiReturn(200, $responseData, 'success');
    }

    /**
     * 设置联系人
     *
     * @date   2017-10-18
     * <AUTHOR> Lan
     *
     *
     * @return string
     */
    public function setContact()
    {
        // 需要登录 却没登录
        if ($this->_mustLogin && !$this->_loginMember['memberID']) {
            $this->returnAutoLogin('请先登录');
        }

        $name       = I('post.name', '', 'strval,trim');
        $phone      = I('post.phone', '', 'strval,trim');
        $action     = I('post.action', '', 'strval,trim');
        $province   = I('post.province', '', 'strval,trim');
        $city       = I('post.city', '', 'strval,trim');
        $town       = I('post.town', '', 'strval,trim');
        $detailAddr = I('post.detail_addr', '', 'strval,trim');

        if ($name == '' || $phone == '') {
            $this->apiReturn(201, [], '参数有误');
        }

        if (!in_array($action, ['delete', 'add'])) {
            $this->apiReturn(201, [], '动作参数有误');
        }

        $memberId = $this->getMemberId();

        $res = ContactPerson::setContact($memberId, $phone, $name, $action, $province, $city, $town, $detailAddr);

        $this->apiReturn($res[0], [], $res[1]);
    }

    /**
     * 删除联系人
     * @author: zhangyz
     * @date: 2020/6/2
     */
    public function deleteContact()
    {
        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(204, [], '参数有误');
        }

        $memberId = $this->getMemberId();

        $contactsBiz = new \Business\Member\FrequentContacts();
        $result      = $contactsBiz->delete($memberId, $id);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 计算运费
     * @author: zhangyz copy from wengbin
     * @date: 2020/5/27
     *
     * @see \Controller\Order\OrderBook::calculateCarriage
     */
    public function calculateCarriage()
    {
        //['tid' => 'num', 'tid2' => 'num']
        $tidNumMap    = I('tid_num_map', []);
        $provinceCode = I('province_code', 0, 'intval');
        $cityCode     = I('city_code', 0, 'intval');
        $aid          = I('aid', 0, 'intval');

        if (!is_array($tidNumMap) || empty($tidNumMap)) {
            $this->apiReturn(204, [], '参数错误');
        }

        if (!$provinceCode || !$cityCode || !$aid) {
            $this->apiReturn(204, [], '参数错误');
        }

        $aidArr = [];
        $fidArr = [];
        foreach ($tidNumMap as $key => $value) {
            $aidArr[$key] = $aid;
            $fidArr[$key] = $this->_loginMember['sid'];
        }

        $api    = new \Business\JavaApi\LogisticsCenter\Carriage();
        $result = $api->countBatchTicket($tidNumMap, $provinceCode, $cityCode, $fidArr, $aidArr); //微平台计算运费

        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取期票可最早预约日期
     *
     * @return array
     * @throws \Exception
     * <AUTHOR>
     * @date 2020/6/17
     *
     */
    public function getRecentOrderDate()
    {
        $orderNum  = I('ordernum', '', 'strval');
        if (empty($orderNum)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        $mallOrderBz = new ReservationOrder();
        $res         = $mallOrderBz->getEarlyCanReservationDateService($orderNum);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取期票预约库存数据
     *
     * @return array
     * <AUTHOR>
     * @date 2020/6/11
     *
     *
     */
    public function getOrderReservationInventory()
    {
        $orderNum = I('order_num', '', 'strval');
        $date     = I('start_date', '', 'strval');
        $end      = I('end_date', '', 'strval');
        $supportCustomTime = I('support_custom_time', false, 'boolval');
        if (empty($orderNum)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        if (empty($date)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '预定日期未选择');
        }
        $mallOrderBz = new ProductStorage();
        $res         = $mallOrderBz->getOrderStorageByReservationOrNormalService($orderNum, $date, $end, [
            'support_custom_time' => $supportCustomTime
        ]);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 微平台期票订单预约
     * <AUTHOR>
     * @date   2020/06/18
     */
    public function orderReservation()
    {
        $orderNum       = I('order_num', '', 'strval');         //订单号
        $sectionTimeStr = I('section_time_str', '', 'strval');  //分时预约时间段
        $playTime       = I('reservation_time', '', 'strval');         //预约游玩时间
        $sectionTimeId  = I('section_time_id', 0, 'intval');   //分时预约时间段Id
        if (!$orderNum) {
            $this->apiReturn(204, [], '订单号不能为空');
        }
        if (!strtotime($playTime)) {
            $this->apiReturn(204, [], '预约时间错误');
        }
        $memberId  = $this->getMemberId();
        $modifyBiz = new ReservationOrder();
        //source是business.conf中的order_track
        $result    = $modifyBiz->orderReservationService($orderNum, $memberId, $memberId, $playTime, 22,
             $sectionTimeId, $sectionTimeStr);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '系统错误');
        }
    }

    /**
     * 期票订单改签
     *
     * @return array
     * <AUTHOR>
     * @date 2020/6/22
     *
     */
    public function OrderBookChange()
    {
        $orderNum       = I('order_num', '', 'strval');         //订单号
        $sectionTimeStr = I('section_time_str', '', 'strval');  //分时预约时间段
        $playTime       = I('reservation_time', '', 'strval');         //预约游玩时间
        $sectionTimeId  = I('section_time_id', 0, 'intval');   //分时预约时间段Id
        if (!$orderNum) {
            $this->apiReturn(204, [], '订单号不能为空');
        }
        if (!strtotime($playTime)) {
            $this->apiReturn(204, [], '预约时间错误');
        }
        $memberId  = $this->getMemberId();
        $modifyBz  = new Modify();
        //source是business.conf中的order_track
        $result    = $modifyBz->ticketChanging($orderNum, $playTime, $memberId, $memberId, 22, $sectionTimeId, $sectionTimeStr);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 下单获取演出信息
     * <AUTHOR>
     * @date 2021/4/12
     *
     */
    public function getShowInfoList()
    {
        $memberId = $this->getMemberId();
        if (!$memberId) {
            $this->returnAutoLogin('请先登录');
        }

        $venues = I('post.venues', '', 'strval');
        $date   = I('post.date', '', 'strval');

        if (empty($venues) || empty($date)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $showBiz = new ShowBiz();
        $result  = $showBiz->getShowInfoList($memberId, $venues, $date);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 批量计算订单的门票金额
     * <AUTHOR>
     * @date 2021/8/14
     *
     * @return array
     */
    public function batchComputeOrderTicketTotalPrice()
    {
        $priceRequestList = I('post.priceRequestList', []);
        $memberId = $this->getMemberId();

        if (empty($priceRequestList)) {
            $this->apiReturn(203, [], '参数错误');
        }

        foreach ($priceRequestList as &$value) {
            if (empty($value['fid'])) {
                $value['fid'] = $memberId;
            }

            $value['sid'] = (int)$value['sid'];
            $value['ticketId'] = (int)$value['ticketId'];
            $value['num'] = (int)$value['num'];
        }

        $pftTTicketPriceStorageService = new PftTicketPriceStorage();
        $result = $pftTTicketPriceStorageService->batchComputeOrderTicketTotalPrice($priceRequestList);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据时间和主票id查询有库存的附属票
     * <AUTHOR>
     * @date 2021/9/14
     *
     * @return array
     */
    public function queryTicketDeputyByTidAndDate()
    {

        $tid     = I('tid', 0, 'intval');
        $applyId = I('apply_id', 0, 'intval');
        $date    = I('date', '', 'strval');

        $memberId = $this->getMemberId();

        if (empty($tid) || empty($applyId) || empty($date)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数缺失');
        }

        $ticketDeputyBiz = new TicketDeputyBiz();

        $result = $ticketDeputyBiz->queryTicketDeputyByTidAndDate($memberId, $applyId, $date, $tid, 10);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 演出下单前置校验
     */
    public function showPreCheck()
    {
        $tids     = I('tids', '', 'strval');    //门票id 多个以逗号隔开
        $playDate = I('play_date', '', 'strval');   //游玩日期
        if (!$tids || !$playDate || !strtotime($playDate)) {
            $this->apiReturn(204, [], '参数错误');
        }
        $tidArr = explode(',', $tids);

        $result = (new \Business\PftShow\Order())->ticketOrderLimitCheck($tidArr, $playDate);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 计算票价以及优惠信息
     */
    public function batchComputeOrderTicketPrice()
    {
        $priceRequestList = I('post.priceRequestList', []);
        $orderMode = I('post.orderMode', OrderChannel::MICROPLAT_CHANNEL, 'intval');
        if (empty($priceRequestList)) {
            $this->apiReturn(203, [], '参数错误');
        }
        $dtype = $this->_loginMember['dtype'];
        $formatRequestList = [];
        $sidSet = [];
        $fidSet = [];
        foreach ($priceRequestList as $value) {
            $tmp = [];
            $tmp['fid'] = $value['fid'] ?: $this->getMemberId();
            $tmp['sid'] = (int)$value['sid'];
            $tmp['ticketId'] = (int)$value['ticketId'];
            $tmp['num'] = (int)$value['num'];
            $tmp['playDate'] = $value['playDate'];
            $formatRequestList[] = $tmp;
            if (!isset($sidSet[$tmp['sid']])) $sidSet[$tmp['sid']] = 1;
            if (!isset($fidSet[$tmp['fid']])) $fidSet[$tmp['fid']] = 1;
        }
        $sidSet = array_keys($sidSet);
        if (count($sidSet) > 1) {
            $this->apiReturn(203, [], '只支持相同供应商产品下单');
        }
        $fidSet = array_keys($fidSet);
        if (count($fidSet) > 1) {
            $this->apiReturn(203, [], '只支持相同分销商产品下单');
        }
        $bookingBz = Container::pull(Booking::class);
        $result = $bookingBz->batchComputeOrderTicketPrice($sidSet[0], $fidSet[0], $formatRequestList, $orderMode, $dtype);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}