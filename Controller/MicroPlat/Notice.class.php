<?php
/**
 * 通知公告等相关操作
 * <AUTHOR>
 * @time   2017-07-20
 */
namespace Controller\MicroPlat;

use Controller\MicroPlat\Common as Common;

class Notice extends Common
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 系统公告列表
     * <AUTHOR>
     * @time   2017-07-20
     *
     * @param  int      $type       通知类型
     * @param  int      $page       页码
     * @param  int      $size       条数
     * @param  int      $status     资讯类型（0系统公告，1功能需求）
     * @param  string   $keyword    关键字
     */
    public function getSysAnnounce()
    {
        $mid     = $this->_loginMember['memberID'];
        $aModel  = new \Model\Notice\Announce();

        $type    = I('type', 0, 'intval');
        $page    = I('page', 2, 'intval');
        $size    = I('size', 10, 'intval');
        $status  = I('status', 0, 'intval');
        $keyword = I('keyword', '', 'trim');

        $field  = 'id, title, details, create_time, update_time';
        $order  = 'update_time desc';

        if ($keyword) {
            // 公告标题模糊搜索
            $map   = [
                'title' => ['like', "%{$keyword}%"]
            ];

        } else {
            $month = date('Y-m-d H:i:s', strtotime("-1 month"));
            $map   = [
                'update_time' => ['egt', $month]
            ];
        }

        // 公告列表
        $list = $aModel->getSysNotice(
                    $type, $page, $size, $status, $field, $order, $map
                );

        if ($list) {
            // 获取已读公告
            $aidArr      = array_column($list, 'id');
            $hasReadAids = $aModel->hasReadAids($mid, $aidArr);

            // 公告内容优化
            $maxLength = 50;
            foreach ($list as $key => $announce) {
                $detail = strip_tags(htmlspecialchars_decode($announce['details']));
                $length = mb_strlen($detail);
                if ($length > $maxLength) {
                    $list[$key]['details'] = mb_substr($detail, 0, $maxLength).'...';
                } else {
                    $list[$key]['details'] = $detail;
                }

                // 已读标识 1已读 0未读
                if (in_array($announce['id'], $hasReadAids)) {
                    $list[$key]['read'] = 1;
                } else {
                    $list[$key]['read'] = 0;
                }
            }
        }

        $this->apiReturn(200, $list);
    }

    /**
     * 获取系统公告内容
     * <AUTHOR>
     * @time   2017-07-20
     *
     * @param  int  $id 公告ID
     */
    public function getAnnounceContent()
    {
        $memberId = $this->_loginMember['memberID'];
        $aModel   = new \Model\Notice\Announce();

        $id = I('id', 0, 'intval');

        if (!$id) {
            $this->apiReturn(201, [], '请选择公告');
        }

        $res = $aModel->getSysNoticeContent($id);

        // 设置公告为已读
        if ($res && !$aModel->is_read($memberId, $res['id'])) {
            $aModel->add_read($memberId, $res['id']);
        }

        $this->apiReturn(200, $res);
    }

}