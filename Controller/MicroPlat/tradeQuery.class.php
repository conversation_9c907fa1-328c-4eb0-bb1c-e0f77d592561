<?php

namespace Controller\MicroPlat;

use Controller\Mall\SmallApp;
use Library\SimpleExcel;
use Business\Finance\TradeQuery as TradeQueryBiz;
use Model\Member\MemberRelationship;

class tradeQuery extends SmallApp {

    private $_tardeQueryBiz;

    private $_memberId;
    private $_loginInfo;

    public function __construct() {
        
        $this->_loginInfo = $this->_auth();
        $this->_memberId  = $this->_loginInfo['sid'];

        $this->_tardeQueryBiz = new TradeQueryBiz();
    }

    /**
     * 交易记录查询配置
     * <AUTHOR>
     * @date   2019-03-27
     */
    public function queryConfig() {
        $result = $this->_tardeQueryBiz->queryConfig($this->_memberId, $this->_loginInfo['saccount']);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }

    /**
     * 获取交易记录列表
     * <AUTHOR>
     * @date   2019-03-27
     */
    public function getTradeList() {
        //账本科目类型
        $subjectCode = I('subject_code', 0, 'intval');
        //费用项类型
        $itemType    = I('item_type', 0, 'intval');
        //开始时间
        $beginTime   = I('begin_time');
        //结束时间
        $endTime     = I('end_time');
        //当前页码
        $page        = I('page', 1, 'intval');
        //每页条数
        $size        = I('size', 10, 'intval');
        //内部订单号
        $orderId     = I('order_id', '', 'strval,trim');
        $aid         = I('aid', 0, 'intval');

        if (!$beginTime || !$endTime) {
            $this->apiReturn(204, '参数错误');
        }

        $option = [];
        if ($aid) $option['aid'] = $aid;
        if ($orderId) $option['order_id'] = $orderId;

        $result = $this->_tardeQueryBiz->getTradeListForMicroPlatform($this->_memberId, $beginTime, $endTime, $subjectCode, $itemType, $page, $size, $option);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 获取交易记录详情
     * <AUTHOR>
     * @date   2019-03-27
     */
    public function getTradeDetail() {

        $tradeId = I('trade_id', 0, 'intval');
        //开始时间
        $beginTime = I('begin_time');
        //结束时间
        $endTime   = I('end_time');

        if (!$tradeId || !$beginTime || !$endTime) {
            $this->apiReturn(204, '参数错误');
        }

        $result = $this->_tardeQueryBiz->getTradeDetail($this->_memberId, $tradeId, $beginTime, $endTime);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }

    }

    /**
     * 交易记录统计
     * <AUTHOR>
     * @date   2019-03-27
     */
    public function summaryForList() {

        //账本科目类型
        $subjectCode = I('subject_code', 0, 'intval');
        //费用项类型
        $itemType    = I('item_type', 0, 'intval');
        //开始时间
        $beginTime   = I('begin_time');
        //结束时间
        $endTime     = I('end_time');

        if (!$beginTime || !$endTime) {
            $this->apiReturn(204, '参数错误');
        }

        $result = $this->_tardeQueryBiz->summaryForMicroPlatform($this->_memberId, $beginTime, $endTime, $subjectCode, $itemType);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }
    }


    /**
     * 统计（详细版）
     * <AUTHOR>
     * @date   2019-03-27
     * @return [type]     [description]
     */
    public function summaryInDetail() {

        //开始时间
        $beginTime = I('begin_time');
        //结束时间
        $endTime   = I('end_time');
        //1收入或2支出
        $type      = I('type', 1, 'intval');

        if (!$beginTime || !$endTime || !$type) {
            $this->apiReturn(204, '参数错误');
        }

        $result = $this->_tardeQueryBiz->summaryInDetail($this->_memberId, $beginTime, $endTime, $type);
        if (isset($result['code'])) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        } else {
            $this->apiReturn(500, [], '接口异常');
        }

    }
}