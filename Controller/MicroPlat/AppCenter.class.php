<?php
/**
 * 应用中心微平台接口
 *
 * <AUTHOR>
 * @time   2021-12-08
 */
namespace Controller\MicroPlat;
use Business\Authority\AuthContext;


class AppCenter extends Common
{


    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 套餐是否到期
     * <AUTHOR>
     * @date   2021-12-08
     *
     *
     * @return array
     */
    public function packageExpire()
    {
        if ($this->_loginMember['sdtype'] != 1) {
            $this->apiReturn(200);
        }

        $hasUsePackage = (new \Business\AppCenter\Package())->getUsePackage($this->_loginMember['sid']);
        if (empty($hasUsePackage)) {
            $this->apiReturn(403, [],'当前账号无有效套餐，请联系客服');
        }

        $this->apiReturn(200);
    }


    /**
     * 开通分销商免费版套餐 套餐 id = 36
     * <AUTHOR>
     * @date   2021-12-08
     *
     *
     * @return array
     */
    public function openPackage()
    {
        $fid        = $this->_loginMember['sid'];

        $packageId  = 63;
        $priceMode  = 1;

        $code = 200;
        $msg  = '';

        try {

            if ($this->_loginMember['sdtype'] != 1) {
                throw new \Exception('只支持分销商账号开通');
            }

            $packageBiz    = new \Business\AppCenter\Package();
            $hasUsePackage = $packageBiz->getUsePackage($this->_loginMember['sid']);
            if ($hasUsePackage) {
                $this->apiReturn(200, [],'当前账号已开通套餐');
            }

            $checkRes   = $packageBiz->openPreCheck($fid, 1, $packageId, $priceMode, [], 1, false);
            if ($checkRes['code'] != 200) {
                throw new \Exception($checkRes['msg']);
            }

            $openRes = $packageBiz->openPackage($fid, $packageId, $priceMode, [], $checkRes['data']['trade_no'], [], false);
            if ($openRes['code'] != 200) {
                throw new \Exception($openRes['msg']);
            }

            $packageBiz->userPackageInfoCacheReset($fid);
            (new \Library\Tools\BusinessCache())->delBusinessCache('renewJudge', $fid);

        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, [], $msg);
    }

}