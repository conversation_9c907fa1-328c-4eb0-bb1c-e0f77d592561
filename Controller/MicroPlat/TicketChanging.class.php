<?php

/**
 * 订单改签相关接口
 *
 * <AUTHOR>  Li
 * @date  2021-08-26
 */

namespace Controller\MicroPlat;

use Business\Order\Modify;
use Business\Order\ShowModify;


class TicketChanging extends Common
{
    /**
     * 订单改签
     * <AUTHOR>  Li
     * @date  2021-08-26
     */
    public function ticketChanging()
    {
        $orderNum       = I('post.ordernum', '', 'strval');         //订单号
        $sectionTimeStr = I('post.section_time_str', '', 'strval');  //分时预约时间段
        $playTime       = I('post.play_date', '', 'strval');         //预约游玩时间
        $sectionTimeId  = I('post.section_time_id', 0, 'intval');    //分时预约时间段Id
        if (!$orderNum) {
            $this->apiReturn(204, [], '订单号不能为空');
        }
        if (!strtotime($playTime)) {
            $this->apiReturn(204, [], '预约时间错误');
        }
        $modifyBz = new Modify();
        $result   = $modifyBz->ticketChanging($orderNum, $playTime, $this->_loginMember['sid'], $this->_loginMember['memberID'], 22, $sectionTimeId,
            $sectionTimeStr);

        $this->apiReturn($result['code'], [], $result['msg']);
    }

    /**
     * 演出订单改签
     * <AUTHOR>  Li
     * @date   2021-08-18
     */
    public function showTicketChanging()
    {
        $ordernum   = I('post.ordernum', '', 'strval');     //订单号
        $changeTime = I('post.play_date', '', 'strval');    //改签日期
        $venueId    = I('post.venue_id', 0, 'intval');      //演出场馆id
        $aid        = I('post.aid', 0, 'intval');           //演出门票上级供应商id
        $roundId    = I('post.round_id', 0, 'intval');       //演出场次id
        $seats      = I('post.seat_ids', '', 'strval');       //演出座位id 多个以逗号隔开

        if (!$ordernum || !$changeTime || !$aid || !$venueId || !$roundId) {
            $this->apiReturn(204, [], '缺少参数');
        }
        $seatIdList = explode(',', $seats);

        $source   = 16;
        $orderBiz = new ShowModify();
        $res      = $orderBiz->showTicketChanging($ordernum, $changeTime, $this->_loginMember['sid'], $aid, $venueId, $roundId, $this->_loginMember['memberID'],
            $source, $seatIdList);

        $this->apiReturn($res['code'], [], $res['msg']);
    }

    /**
     * 获取演出改签库存信息
     * <AUTHOR>  Li
     * @date   2021-08-26
     */
    public function getShowInfoList()
    {
        $venues    = I('post.venues', '', 'strval');
        $date      = I('post.date', '', 'strval');
        $isReserve = I('post.is_reserve', 0, 'strval');    //是否是预约获取库存

        if (empty($venues) || empty($date)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $orderBiz = new ShowModify();
        $result  = $orderBiz->getShowInfoList($this->_loginMember['sid'], $venues, $date, $isReserve);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}
