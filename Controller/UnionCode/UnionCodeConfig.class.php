<?php
/**
 * 联合发码配置管理
 */
namespace Controller\UnionCode;

use Business\Authority\DataAuthLogic;
use Business\Authority\StaffCheckAuthBiz;
use Business\MemberLogin\MemberLoginHelper;
use Library\Constants\MemberConst;
use Library\Controller;
use Library\JsonRpc\PftRpcClient;
use Library\Tools\YarClient;
use Library\UnionCode\UnionCodeUtil;
use Library\YarWrapperController;
use Model\Product\Land;


class UnionCodeConfig extends YarWrapperController {

	/**
	 * @var YarClient
	 */
	private $yarClient;

	/**
	 * @var PftRpcClient
	 */
	private $jsonrpcClient;

	public function __construct() {
		parent::__construct();

		$loginInfo = $this->getLoginInfo();
		if(!is_array($loginInfo) || empty($loginInfo['sid'])){
			$this->apiReturn(self::CODE_PARAM_ERROR, [], '无法获取登录信息');
		}

		$jsonRpcService = 'pft_scenic_local_service';
		$this->jsonrpcClient = new PftRpcClient($jsonRpcService);
	}

	/**
	 * 请求配置列表
	 */
	public function getlist(){
		$loginInfo = $this->getLoginInfo();
		$postData = I('post.');
		$parentId = intval($loginInfo['sid']);
		$postData['__parent_id'] = $parentId;
		$postData['__member_id'] = $parentId;

		$method = 'Config/UnionCodeConfig/configList';
		$params = [];
		$params[] = $postData;
		$module = uniqid();
		try {
			$res = $this->jsonrpcClient->call($method, $params, $module);
			$data = $this->parseJsonRPCResult($res);
		} catch(\Throwable $e){
			$this->apiReturn(self::CODE_INVALID_REQUEST,[],$e->getMessage());
		}
		
		$this->apiReturn(self::CODE_SUCCESS,$data,"请求列表成功");
	}

	/**
	 * 添加配置
	 */
	public function addConfig(){
        $loginInfo = $this->getLoginInfo();
        if (MemberLoginHelper::isSubMerchantLogin()){
            $this->apiReturn(401, [], '无使用权限');
        }
        $postData = I('post.');
		$parentId = intval($loginInfo['sid']);
		//$memberId = intval($loginInfo['memberID']);
		$postData['__parent_id'] = $parentId;
		$postData['__member_id'] = $parentId;
		$method = 'Config/UnionCodeConfig/addConfig';
		$module = uniqid();
		$params = [];
		$params[] = $postData;
		try {
			$res = $this->jsonrpcClient->call($method, $params, $module);
			$data = $this->parseJsonRPCResult($res);
		} catch(\Throwable $e){
			$this->apiReturn(self::CODE_INVALID_REQUEST,[],$e->getMessage());
		}
		$this->apiReturn(self::CODE_SUCCESS,$data,"配置添加成功");
	}

	/**
	 * 编辑配置
	 */
	public function editConfig(){
		$loginInfo = $this->getLoginInfo();
		$postData = I('post.');
		$parentId = intval($loginInfo['sid']);
		$postData['__parent_id'] = $parentId;
		$postData['__member_id'] = $parentId;
		$method = 'Config/UnionCodeConfig/editConfig';
		$module = uniqid();
		$params = [];
		$params[] = $postData;
		try {
			$res = $this->jsonrpcClient->call($method, $params, $module);
			$data = $this->parseJsonRPCResult($res);
		} catch(\Throwable $e){
			$this->apiReturn(self::CODE_INVALID_REQUEST,[],$e->getMessage());
		}
		$this->apiReturn(self::CODE_SUCCESS,$data,"配置修改成功");
	}

	/**
	 * 删除配置
	 */
	public function deleteConfig(){
		$loginInfo = $this->getLoginInfo();
		$postData = I('post.');
		$parentId = intval($loginInfo['sid']);
		$postData['__parent_id'] = $parentId;
		$method = 'Config/UnionCodeConfig/delConfig';
		$module = uniqid();
		$params = [];
		$params[] = $postData;
		try {
			$res = $this->jsonrpcClient->call($method, $params, $module);
			$data = $this->parseJsonRPCResult($res);
		} catch(\Throwable $e){
			$this->apiReturn(self::CODE_INVALID_REQUEST,[],$e->getMessage());
		}
		$this->apiReturn(self::CODE_SUCCESS,$data,"配置删除成功");
	}

	/**
	 * 请求配置信息
	 */
	public function configInfo(){
		$loginInfo = $this->getLoginInfo();
		$postData = I('post.');
		$parentId = intval($loginInfo['sid']);
		$postData['__parent_id'] = $parentId;
		$method = 'Config/UnionCodeConfig/configInfo';
		$module = uniqid();
		$params = [];
		$params[] = $postData;
		try {
			$res = $this->jsonrpcClient->call($method, $params, $module);
			$data = $this->parseJsonRPCResult($res);
		} catch(\Throwable $e){
			$this->apiReturn(self::CODE_INVALID_REQUEST,[],$e->getMessage());
		}
		$this->apiReturn(self::CODE_SUCCESS,$data,"请求配置信息成功");
	}


	/**
	 * 通过配置ID，返回配置的绑定列表
	 */
	public function bindlist(){
		$loginInfo = $this->getLoginInfo();
		$postData = I('post.');
		$parentId = intval($loginInfo['sid']);
		$postData['__parent_id'] = $parentId;
		$postData['__member_id'] = $parentId;

        $memberId = $loginInfo['memberID'];
        $dtype = $loginInfo['dtype'];
        if($dtype == MemberConst::ROLE_STAFF) {
            $permissionData = StaffCheckAuthBiz::getInstance()->getPermissionData($memberId);
            $idList = $permissionData['idList'] ?? [];
            $notIdList = $permissionData['notIdList'] ?? [];

            if(empty($idList)  && empty($notIdList)) {
                ; // 不限制
            }elseif(!empty($idList) && in_array(0, $idList)) {
                // 全部限制
                $postData['lids'] = [PHP_INT_MAX];
            } else {
                $postData['lids'] = $idList; // 包含
                $postData['not_lids'] = $notIdList; // 排除
            }
        }


		$method = 'Config/UnionCodeConfig/bindList';
		$module = uniqid();
		$params = [];
		$params[] = $postData;
		try {
			$res = $this->jsonrpcClient->call($method, $params, $module);
			$data = $this->parseJsonRPCResult($res);
		} catch(\Throwable $e){
			$this->apiReturn(self::CODE_INVALID_REQUEST,[],$e->getMessage());
		}
		$this->apiReturn(self::CODE_SUCCESS,$data);
	}

	/**
	 * 关联配置与产品
	 */
	public function bind(){
		$loginInfo = $this->getLoginInfo();
		$postData = I('post.');
		$parentId = intval($loginInfo['sid']);
		$postData['__parent_id'] = $parentId;
		$postData['__member_id'] = $parentId;
		$method = 'Config/UnionCodeConfig/bind';
		$module = uniqid();
		$params = [];
		$params[] = $postData;
		try {
			$res = $this->jsonrpcClient->call($method, $params, $module);
			$data = $this->parseJsonRPCResult($res);
		} catch(\Throwable $e){
			$this->apiReturn(self::CODE_INVALID_REQUEST,[],$e->getMessage());
		}
		$this->apiReturn(self::CODE_SUCCESS,$data,"绑定成功");
	}

	/**
	 * 取消绑定
	 */
	public function unbind(){
		$loginInfo = $this->getLoginInfo();
		$postData = I('post.');
		$parentId = intval($loginInfo['sid']);
		$postData['__parent_id'] = $parentId;
		$method = 'Config/UnionCodeConfig/unbind';
		$module = uniqid();
		$params = [];
		$params[] = $postData;
		try {
			$res = $this->jsonrpcClient->call($method, $params, $module);
			$data = $this->parseJsonRPCResult($res);
		} catch(\Throwable $e){
			$this->apiReturn(self::CODE_INVALID_REQUEST,[],$e->getMessage());
		}
		$this->apiReturn(self::CODE_SUCCESS,$data,'解绑成功');
	}

	// 测试内网RPC调用网络耗时
	/* public function test(){
		$loginInfo = $this->getLoginInfo();
		$postData = I('post.');
		$parentId = intval($loginInfo['sid']);
		$postData['__parent_id'] = $parentId;
		$method = 'Config/UnionCodeConfig/test';
		$module = uniqid();
		$params = [];
		$params[] = $postData;
		$__s = microtime(true);
		$res = $this->jsonrpcClient->call($method, $params, $module);
		$el = (microtime(true) - $__s) * 1000;
		$data = $this->parseJsonRPCResult($res);
		var_dump($el.' ms');
		var_dump($data);
	} */

	/**
	 * 测试通过yar方式获取凭证码耗时情况
	 */
	/* public function getcode(){

		$lid = 205157;   // 75747;
		$ptype = 'A';
		$terminalType = 0;
		$resourceId = 196921;

		$method = 'Config/UnionCode/getcode';
		$module = uniqid();
		$postData = [
			'land_id' => $lid,
			'ptype' => $ptype,
			'termianl_type' => $terminalType,
			'resource_id' => $resourceId,
		];
		$params = [$postData];
		$__s = microtime(true);
		$res = $this->jsonrpcClient->call($method, $params, $module);
		$result = json_decode($res, true);
		$el = (microtime(true) - $__s) * 1000;
		if(is_array($result)){
			if($result['code'] == self::CODE_SUCCESS){
				$data = [];
				if(isset($result['data'])){
					$data = $result['data'];
				}
				if(!empty($data['ticket_code'])){
					var_dump($data['ticket_code']);
				}
			}
		}
		var_dump($el . ' ms');
		//var_dump($data);
	} */

	/*public function supplement(){
		$lid = 205157;
		$ptype = 'A';
		$terminalType = 0;
		$resourceId = 196921;
		$codeType = 1;
		$configId = 11035985;


		$method = 'Config/UnionCode/getcode';
		$module = uniqid();
		$postData = [
			'land_id' => $lid,
			'code_type' => $ptype,
			'config_id' => $codeType,
			'resource_id' => $resourceId,
		];
		$params = [$postData];
		$method = 'Config/UnionCode/supplement';
		$__s = microtime(true);
		$res = $this->jsonrpcClient->call($method, $params, $module);
		var_dump($res);exit;
		$result = json_decode($res, true);
		$el = (microtime(true) - $__s) * 1000;
		if(is_array($result)){
			var_dump($result['code']);
			if($result['code'] == self::CODE_SUCCESS){
				$data = [];
				if(isset($result['data'])){
					$data = $result['data'];
				}
				var_dump($data);
			}
		}
		var_dump($el . ' ms');
		//var_dump($data);
	}*/


    /**
     * 根据lid获取联合发码配置
     *
     * <AUTHOR>
     * @date 2021/04/23
     *
     * @return array
     */
	public function landConfig()
    {
        //模块id
        $landIdStr = I('get.land_id_list', '', 'strval');
        if (empty($landIdStr)) {
            $this->apiReturn(400, [], '参数错误');
        }

        $landIdList = explode(',', $landIdStr);
        if (empty($landIdList)) {
            $this->apiReturn(400,  [], '参数错误');
        }

        $loginInfo = $this->getLoginInfo();
        $sid = $loginInfo['sid'];
        $memberId = $loginInfo['memberID'];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $memberId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        $params = [
            'land_id_list' => $landIdList,
            '__parent_id'  => $sid,
            '__member_id'  => $memberId,
        ];
	
	    try {
		    $res  = $this->jsonrpcClient->call('Config/UnionCodeConfig/landFullConfig', $params, 'config');
		    $data = $this->parseJsonRPCResult($res);
		    foreach ($data as &$tmp) {
                $tmp['source_type'] = $tmp['operator_type'];
            }
	    } catch(\Throwable $e){
		    $this->apiReturn(self::CODE_INVALID_REQUEST,[],$e->getMessage());
	    }
        
        $this->apiReturn(self::CODE_SUCCESS, $data);
    }
    
    public function getcode(){
	    $lid = I('land_id');
	    $landModel = new Land();
	    $field = 'id,p_type,resourceID,terminal_type';
	    $landInfo = $landModel->getLandInfoByLandId($lid, $field);
	    if(!$landInfo){
	    	$this->apiReturn(self::CODE_NO_CONTENT,'景区不存在');
	    }
	    $ptype = $landInfo['p_type'];
	    $force=false;
	    $resourceId = (int)$landInfo['resourceID'];
	    $terminalType = $landInfo['terminal_type'];
	    $_start = microtime(true);
	    $data = UnionCodeUtil::getCode($lid, $ptype, $force, $resourceId, $terminalType);
	    if($data != null){
	    	$el = ((microtime(true) - $_start) * 1000).' ms';
		    $this->apiReturn(self::CODE_SUCCESS, $data, $el);
	    } else {
		    $this->apiReturn(self::CODE_NO_CONTENT,'获取凭证码失败');
	    }
    }
}
