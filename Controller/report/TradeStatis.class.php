<?php
/**
 * 交易报表
 *
 * <AUTHOR>
 * @date 2019-07-22
 *
 */

namespace Controller\report;

use Business\JavaApi\Member\MemberQuery;
use Business\JavaApi\Trade\Query;
use Business\Member\Member;
use Library\Controller;
use Library\Traits\CacheTrait;
use Model\Member\MemberRelationship;
use Model\Member\TradeJournal;
use Model\Report\TradeReport as TradeReportModel;
use Library\SimpleExcel;
use function GuzzleHttp\Psr7\str;

class TradeStatis extends Controller
{
    use CacheTrait;

    //登录信息
    private $_loginInfo;
    //交易报表数据模型
    private $_tradeReportModel;

    //导出报表表头
    private $_exportHead = ['交易日期', '账本科目', '期初余额（单位元）', '收入（单位元）', '支出（单位元）', '期末余额（单位元）'];

    private $_exportBalanceHead = [
        '交易日期',
        '对方账户',
        '账本科目',
        '可用金额（单位：元）',
        '总额度（单位：元）',
        '已用额度（单位：元）',
        '剩余额度（单位：元）',
        '还款金额（单位：元）',
        '收入（单位：元）',
        '支出（单位：元）',
    ];
    //导出收入详情表头
    private $_exportInComeDetailHeand = ['交易时间', '账本科目', '费用类型', '收入（单位元）'];
    //导出支出详情表头
    private $_exportOutComeDetailHeand = ['交易时间', '账本科目', '费用类型', '支出（单位元）'];

    private $_limitTime          = 0;
    private $_limitRedisKey      = 'realtime:trade:create:%u';
    private $_maxTradeLimitMoney = 10000000000; //授信额度等于这个表示不限

    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo();
    }

    /**
     * 交易记录列表
     * <AUTHOR>
     * @date    2019-07-22
     *
     * @param  integer  $startDate  开始日期
     * @param  integer  $endDate  结束日期
     * @param  integer  partner_id 交易对方商id（供应商或分销商）
     * @param  integer  $subjectCode  账本类型
     * @param  string  $dateType  报表类型 date=日报表 month=月报表
     *
     * @return
     */
    public function tradeList()
    {
        $code   = 200;
        $msg    = '';
        $export = 0;

        $formatData = [];
        $returnData = [];

        try {
            $dateType  = I('get.date_type/s', 'date');
            $startDate = I('get.start_date/s', '');
            $endDate   = I('get.end_date/s', '');
            $partnerId = I('get.partner_id', 0, 'intval');

            $subjectCode = I('get.subject_code/s', '');
            $export      = I('get.export/d', 0);
            $page        = I('get.page/d', 1);
            $size        = I('get.size', 50, 'intval');

            if (!in_array($dateType, [TradeReportModel::TYPE_DATE, TradeReportModel::TYPE_MONTH])) {
                $this->apiReturn(400, [], '日期类型参数错误');
            }

            if (!$startDate || !$endDate || ($startDate > $endDate)) {
                $this->apiReturn(400, [], '日期参数错误');
            }

            //所属供应商
            $sid = $this->_loginInfo['sid'];

            //如果有请求当天实时数据，需要先请求一个java接口，让Java去跑个脚本生成实时的数据
            //因为java是没有提供实时数据的，所以要临时生成，再让客户延迟去查询。这个数据做5分钟的缓存
            if ($dateType == TradeReportModel::TYPE_DATE && $endDate == date('Ymd')) {
                $redisInstance = $this->getRedisInstance();
                $redisKey      = $this->getCacheKey($this->_limitRedisKey, [$sid]);
                $hasRealData   = $redisInstance->get($redisKey);
                if (!$hasRealData) {
                    $queryBiz  = new Query();
                    $queryData = $queryBiz->createTradeReport($sid);
                    $redisInstance->set($redisKey, 1, '', 300);
                    //$this->apiReturn(self::CODE_PARAM_ERROR, [], '实时数据正在生成中，请稍后再试');
                }
            }

            //通过用户ID获取账户ID
            $memberBiz = new Member();
            if ($partnerId != 0) {
                $memberInfos = $memberBiz->getMemberInfoByMulti([$sid, $partnerId], 'id');
            } else {
                $memberInfos = $memberBiz->getMemberInfoByMulti([$sid], 'id');
            }

            $memberAccount = [];
            foreach ($memberInfos as $member) {
                $memberAccount[$member['id']] = $member['account_id'];
            }

            //账本 日/月 统计数据
            $balanceData = $this->_getBalance($startDate, $endDate, $memberAccount[$sid],
                $memberAccount[$partnerId] ?: 0, $subjectCode, $dateType, $page, $size);

            $balanceList = $balanceData['list'];
            $count       = $balanceData['total'];

            if (empty($balanceList) || $balanceData['total'] == 0) {
                $this->apiReturn($code, ['total' => 0, 'list' => []], '暂无数据');
            }

            switch ($subjectCode) {
                //授信账户（2101） & 供应商授信账户（2103）
                case 2101:
                case 2103:
                    $memberArr = [];
                    $acIdArr   = array_values($balanceData['fid']);

                    //通过账户ID查询用户相关信息
                    if (!empty($acIdArr)) {
                        $memberQuery = new MemberQuery();
                        $accountInfo = $memberQuery->queryMemberByMemberQueryInfo(['accountIdList' => $acIdArr]);
                        $accountInfo = array_column($accountInfo['data'], 'memberInfo');

                        $fidArr = [];
                        foreach ($accountInfo as $key => $value) {
                            array_push($fidArr, $value['id']);

                            $memberArr[$value['account_id']] = [
                                'id'      => $value['id'],
                                'dname'   => $value['dname'],
                                'account' => $value['account'],
                            ];
                        }
                        unset($accountInfo);
                    }

                    //组合数据
                    $formatData = $this->_formatBalanceData($balanceList, $memberArr, $subjectCode, $dateType);
                    break;
                //包括现金，支付，平台账户在内
                default:
                    $formatData = $this->_formatData($balanceList, $dateType);
                    break;
            }
            $returnData = ['total' => intval($count), 'list' => $formatData];
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        if ($export && $code == 200) {
            $this->_export($formatData, $startDate, $endDate, $subjectCode);
        } else {
            $this->apiReturn($code, $returnData, $msg);
        }
    }

    /**
     * 获取合作的供应商/分销商
     * @author: zhangyz
     * @date: 2020/2/24
     *
     * @param  int  $type  类型，0=供应商  1=分销商
     * @param  string  $keyword  关键字
     *
     * @return array
     */
    public function getPartners()
    {
        $type    = I('post.type', 0, 'intval');
        $keyword = I('post.keyword', '', 'strval');

        if (!in_array($type, [0, 1])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '查询类型错误');
        }

        //没有查询关键字直接返回空
        if (empty($keyword)) {
            $this->apiReturn(self::CODE_SUCCESS, [], 'success');
        }

        $sid  = $this->_loginInfo['sid'];
        $data = [];

        $shipModel = new MemberRelationship();
        if ($type == 0) {
            $result = $shipModel->getValidSuppliers($sid, $keyword, [], 1, 200);

            foreach ($result['list'] as $value) {
                $data[] = [
                    'id'    => $value['id'],
                    'dname' => $value['dname'],
                ];
            }
        } else {
            $fidData = $shipModel->getValidResellers($sid, $keyword, [], 1, 200);

            foreach ($fidData['list'] as $key => $value) {
                if (strstr($value['dname'], $keyword)) {
                    $data[] = [
                        'id'    => $value['id'],
                        'dname' => $value['dname'],
                    ];
                }
            }
        }

        $this->apiReturn(self::CODE_SUCCESS, $data, 'success');
    }

    /**
     * 获取用户交易记录日汇总数据
     * @author: zhangyz
     * @date: 2020/2/21
     *
     * @param  string  $startDate  开始时间
     * @param  string  $endDate  结束时间
     * @param  int  $accountId  账户ID
     * @param  int  $tradeAccountId  交易对方账户ID
     * @param  array  $subjectCode  账本类型
     * @param  string  $dateType  报表类型，date=日报表  month=月报表
     * @param  int  $page  页码
     * @param  int  $size  每页数量
     *
     * @return array
     */
    private function _getBalance($startDate, $endDate, $accountId, $tradeAccountId, $subjectCode, $dateType, $page = 1, $size = 50)
    {
        $returnData        = ['list' => [], 'total' => 0, 'fid' => []];
        $transReoportModel = new \Model\TradeRecord\PftTransReport();

        $balanceData = $transReoportModel->getSubjectBalance($startDate, $endDate, $accountId, $tradeAccountId,
            $subjectCode, $dateType, $page, $size);

        $returnData['total'] = $balanceData['total'];
        if (!empty($balanceData['list'])) {
            $data = $this->_handleDateBalance($balanceData['list'], $subjectCode);

            $returnData['list'] = $data['list'];
            $returnData['fid']  = $data['fid'];
        }

        return $returnData;
    }

    /**
     * 处理日账单数据
     * @author: zhangyz
     * @date: 2020/2/28
     *
     * @param $list
     * @param $subjectCode
     *
     * @return array
     */
    private function _handleDateBalance($list, $subjectCode)
    {
        $data   = [];
        $fidArr = [];
        if (in_array($subjectCode, [2101, 2103])) {
            foreach ($list as $key => $item) {
                $fid  = $subjectCode == 2103 ? $item['account_id'] : $item['trade_account_id'];
                $date = '20' . $item['trans_time'];

                array_push($fidArr, $fid);
                //每日的统计是按时间和交易对方商户统计的，所以不会有重复数据，直接生成data数据即可
                $data[$date][$fid] = $item;
            }
        } else {
            foreach ($list as $key => $item) {
                $data['20' . $item['trans_time']] = $item;
            }
        }

        return ['list' => $data, 'fid' => array_unique($fidArr)];
    }

    /**
     * 交易记录详情
     * <AUTHOR>
     * @date    2019-07-22
     *
     * @param  integer  $startDate  开始日期
     * @param  integer  $endDate  结束日期
     * @param  integer  $subjectCode  账本类型
     * @param  string  $dateType  报表类型 date=日报表 month=月报表
     *
     * @return
     */
    public function detail()
    {
        $code       = 200;
        $msg        = '';
        $export     = 0;
        $detailList = [];

        try {
            //详情类型
            $detailType = I('get.detail_type/d', '');
            //账本类型
            $subjectCode = I('get.subject_code/d', '');
            //查询日期类型
            $dateType = I('get.date_type/s', 'date');
            //交易对方ID
            $tradeId = I('get.partner_id', 0, 'intval');
            //查询日期
            $date = I('get.date/s', '');
            //导出
            $export = I('get.export/d', 0);

            //所属供应商
            $sid = $this->_loginInfo['sid'];

            if (!in_array($detailType, [TradeReportModel::ACTION_INCOME, TradeReportModel::ACTION_OUTCOME], true)) {
                $this->apiReturn(400, [], '类型错误');
            }

            if (!$sid) {
                $this->apiReturn(400, [], '未登录');
            }

            $subjectCodeArr = $subjectCode ? [$subjectCode] : TradeReportModel::SHOW_SUBJECT;
            if (!in_array($subjectCode, [2101, 2103])) {
                $detailList = $this->_getTradeReportModel()->getTradeList($sid, $date, $date, $detailType,
                    $subjectCodeArr, $dateType);
            } else {
                $memberBiz   = new Member();
                $memberInfos = $memberBiz->getMemberInfoByMulti([$sid, $tradeId], 'id');

                $memberArr = [];
                foreach ($memberInfos as $member) {
                    $memberArr[$member['id']] = $member['dname'];
                }
                unset($memberInfos);

                $journalModel = new TradeJournal();
                $list         = $journalModel->getSumJournalByFidAid($sid, $tradeId, $date, $subjectCode, $detailType, $dateType);

                $dtypeMap   = $this->getDtypeCodeNameMap();
                $subjectMap = $this->getSubjectCodeNameMap();

                $detailList = [];
                foreach ($list as $key => $value) {
                    $detailList[] = [
                        'money'        => $value['dmoney'],
                        'subject_code' => $value['subject_code'],
                        'subject_name' => $subjectMap[$value['subject_code']],
                        'action'       => $detailType,
                        'dtype_code'   => $value['template_item_code'],
                        'dtype_name'   => $dtypeMap[$value['template_item_code']],
                        'partner_id'   => $tradeId,
                        'partner_name' => $memberArr[$tradeId],
                    ];
                }
            }
        } catch (\Exception $e) {

            $code = 400;
            $msg  = $e->getMessage();
        }

        if ($export && $code == 200) {
            $this->_exportDetail($detailList, $detailType, $date, $date);
        } else {
            $this->apiReturn($code, $detailList, $msg);
        }
    }

    /**
     * 费用编码名称对应关系
     * <AUTHOR>
     * @date    2019-07-18
     *
     * @return  array
     */
    public function getDtypeCodeNameMap()
    {
        $dtype      = [];
        $dtypeItems = load_config('template_item', 'account_book');

        foreach ($dtypeItems as $item) {
            $dtype[$item['code']] = $item['name'];
        }

        return $dtype;
    }

    /**
     * 账本编码名称对应关系
     * <AUTHOR>
     * @date    2019-07-18
     *
     * @return  array
     */
    public function getSubjectCodeNameMap()
    {
        $subjectBooks = load_config('book_subject', 'account_book');
        $subjectArr   = [];

        foreach ($subjectBooks as $subject) {
            $subjectArr[$subject['code']] = $subject['name'];
        }

        return $subjectArr;
    }

    /**
     * 账本类型列表
     * <AUTHOR>
     * @date    2019-07-22
     * @return
     */
    public function subjectList()
    {
        $showSubject   = TradeReportModel::SHOW_SUBJECT;
        $subjectBooks  = load_config('book_subject', 'account_book');
        $returnSubject = [];

        foreach ($subjectBooks as $subject) {
            if (in_array($subject['code'], $showSubject)) {
                $returnSubject[] = $subject;
            }
        }

        //$all = ['code' => 0, 'name' => '全部'];
        //array_unshift($returnSubject, $all);
        $this->apiReturn(200, ['subject_list' => $returnSubject]);
    }

    /**
     * 导出
     * <AUTHOR>
     * @date    2019-07-22
     *
     * @return
     */
    private function _export($tradeData, $startDate, $endDate, $subjectCode)
    {
        $filename = date('Ymd') . '交易报表';
        $title    = $startDate . ' - ' . $endDate . '交易报表';
        $excel[0] = [$title];

        if (in_array($subjectCode, [2101, 2103])) {
            //配置的纬度
            foreach ($this->_exportBalanceHead as $key => $value) {
                if ($key == 1) {
                    $excel[1][] = $subjectCode == 2101 ? '供应商' : '分销商';
                } else {
                    $excel[1][] = $value;
                }
            }

            $i = 2;
            foreach ($tradeData as $trade) {
                $excel[$i][] = $trade['date'];
                $excel[$i][] = $trade['dname'] . '/' . $trade['account'];
                $excel[$i][] = $trade['subject_text'];
                $excel[$i][] = $trade['use_money'] == '不限' ? $trade['use_money'] : round($trade['use_money'] / 100, 2);
                $excel[$i][] = $trade['total'] == '不限' ? $trade['total'] : round($trade['total'] / 100, 2);
                $excel[$i][] = round($trade['total_used'] / 100, 2);
                $excel[$i][] = $trade['total_left'] == '不限' ? $trade['total_left'] : round($trade['total_left'] / 100, 2);
                $excel[$i][] = round($trade['repayment'] / 100, 2);
                $excel[$i][] = round($trade['income'] / 100, 2);
                $excel[$i][] = round($trade['expense'] / 100, 2);
                $i++;
            }
        } else {
            //配置的纬度
            foreach ($this->_exportHead as $value) {
                $excel[1][] = $value;
            }

            $i = 2;

            foreach ($tradeData as $tradeInfo) {

                $excel[$i][] = $tradeInfo['date'];
                $excel[$i][] = $tradeInfo['subject_name'];
                $excel[$i][] = round($tradeInfo['start_balance'] / 100, 2);
                $excel[$i][] = round($tradeInfo['income'] / 100, 2);
                $excel[$i][] = round($tradeInfo['outcome'] / 100, 2);
                $excel[$i][] = round($tradeInfo['end_balance'] / 100, 2);
                $i++;
            }
        }

        $xls = new SimpleExcel('UTF-8', true);
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }

    /**
     * 导出
     * <AUTHOR>
     * @date    2019-07-22
     *
     * @return
     */
    private function _exportDetail($detailData, $action, $startDate, $endDate)
    {
        if ($action == TradeReportModel::ACTION_INCOME) {
            $actionDesc = '收入详情报表';
            $exportHead = $this->_exportInComeDetailHeand;
        } else {
            $actionDesc = '支出详情报表';
            $exportHead = $this->_exportOutComeDetailHeand;
        }

        $filename = date('Ymd') . $actionDesc;
        $title    = $startDate . ' - ' . $endDate . $actionDesc;
        $excel[0] = [$title];

        //配置的纬度
        foreach ($exportHead as $value) {
            $excel[1][] = $value;
        }

        $i = 2;
        foreach ($detailData as $detailInfo) {
            $excel[$i][] = $startDate . ' - ' . $endDate;
            $excel[$i][] = $detailInfo['subject_name'];
            $excel[$i][] = $detailInfo['dtype_name'];
            $excel[$i][] = round($detailInfo['money'] / 100, 2);
            $i++;
        }

        $xls = new SimpleExcel('UTF-8', true);
        $xls->addArray($excel);
        $xls->generateXML($filename);
        exit;
    }

    /**
     * 处理交易报表授信账本的数据
     * @author: zhangyz
     * @date: 2020/2/21
     *
     * @param  array  $balanceData  交易记录数据
     * @param  array  $fidArr  用户信息
     * @param  int  $subjectCode  账本类型
     * @param  string $dateType 报表类型
     *
     * @return array
     */
    private function _formatBalanceData($balanceData, $fidArr, $subjectCode, $dateType = 'date')
    {
        if (empty($balanceData)) {
            return [];
        }

        //账本科目文本
        $subjectArr = $this->_getTradeReportModel()->getSubjectCodeNameMap();

        $formatData = [];
        foreach ($balanceData as $date => $dateData) {
            foreach ($dateData as $fid => $balance) {
                if ($balance['end_money'] > 0) {
                    $totalUsed = 0;
                    $repayment = (int)abs($balance['end_money']);
                } else {
                    $totalUsed = (int)abs($balance['end_money']);
                    $repayment = 0;
                }

                $limit = (int)$balance['trade_limit_money'];

                $income  = $dateType == 'date' ? (int)$balance['income'] : (int)$balance['month_income'];
                $expense = $dateType == 'date' ? (int)$balance['expense'] : (int)$balance['month_expense'];

                $formatData[] = [
                    //总额度:供应商给的总授信额度
                    'total'        => $limit == $this->_maxTradeLimitMoney ? '不限' : $limit,
                    //已用额度：分销商下单购买商品花费的授信额度
                    'total_used'   => $totalUsed,
                    //剩余额度：总授信额度 - 已用额度
                    'total_left'   => $limit == $this->_maxTradeLimitMoney ? '不限' : $limit - $totalUsed,
                    //还款余额：还款超过总授信额度的金额（还款优先还至已有额度中）
                    'repayment'    => $repayment,
                    //可用余额：剩余额度 + 还款余额
                    'use_money'    => $limit == $this->_maxTradeLimitMoney ? '不限' : $limit - $totalUsed + $repayment,
                    //在上层查询数据的时候都是查询的2101账本的数据，所以2103的收入支出和2101是相反的
                    'income'       => $subjectCode == 2103 ? $expense : $income,
                    'expense'      => $subjectCode == 2103 ? $income : $expense,
                    'date'         => (string)$date,
                    'subject_code' => $subjectCode,
                    'subject_text' => $subjectArr[$subjectCode],
                    'partner_id'   => $fidArr[$fid]['id'],
                    'dname'        => $fidArr[$fid]['dname'],
                    'account'      => $fidArr[$fid]['account'],
                ];
            }
        }

        return $formatData;
    }

    /**
     * 计算使用的信用额度
     * @author: zhangyz
     * @date: 2020/2/27
     *
     * @param  int  $dmoney  本次操作的金额
     * @param  int  $lmoney  信用账户余额
     *
     * @return float|int
     */
    private function _handleQuota($dmoney, $lmoney)
    {
        if ($lmoney >= 0) {
            //如果信用账户余额大于等于0，说明全部用的是预存的金额，没用到信用额度
            $quota = 0;
        } else {
            //如果信用账户余额的绝对值比本次消费的金额小，说明是一部分用的预存金额，一部分用的信用额度
            if (abs($lmoney) < abs($dmoney)) {
                $quota = abs($lmoney);
            } else {
                $quota = abs($dmoney);
            }
        }

        return $quota;
    }

    /**
     * 格式交易报表数据与账本余额数据
     * <AUTHOR>
     * @date    2019-07-22
     *
     * @param  array  $balanceData
     *
     * @return  array
     */
    private function _formatData($balanceData, $dateType = 'date')
    {
        $formatData = [];
        //账本科目文本
        $subjectArr = $this->_getTradeReportModel()->getSubjectCodeNameMap();

        //处理账本数据给前端
        foreach ($balanceData as $date => $balance) {
            $subjectCode = $balance['subject_code'];

            $formatData[] = [
                'date'          => (string)$date,
                'subject_code'  => $subjectCode,
                'subject_name'  => $subjectArr[$subjectCode],
                'income'        => $dateType == 'date' ? $balance['income'] : $balance['month_income'],
                'outcome'       => $dateType == 'date' ? $balance['expense'] : $balance['month_expense'],
                'start_balance' => $subjectCode < 2000 ? '-' : $balance['begin_money'], //小于2000的是现金支付宝等账本类型
                'end_balance'   => $subjectCode < 2000 ? '-' : $balance['end_money'],
            ];
        }

        return $formatData;
    }

    /**
     * 交易报表模型
     * <AUTHOR>
     * @date    2019-07-22
     * @return object
     */
    private function _getTradeReportModel()
    {
        if (!$this->_tradeReportModel) {
            $this->_tradeReportModel = new TradeReportModel();
        }

        return $this->_tradeReportModel;
    }
}