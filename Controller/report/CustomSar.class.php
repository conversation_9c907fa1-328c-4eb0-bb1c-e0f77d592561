<?php

namespace Controller\report;

use Library\Controller;

class CustomSar extends Controller
{
    //用户上级ID
    private $_sid;
    //用户ID
    private $_memberId;
    private $_loginInfo;
    /** @var \Business\Report\CustomSar */
    private $customSarBiz;

    public function __construct()
    {
        parent::__construct();
        $this->_sid         = $this->isLogin('ajax');
        $this->_loginInfo = $loginInfo = $this->getLoginInfo('ajax', false, false);
        $this->_memberId    = $loginInfo['memberID'];
        $this->customSarBiz = $this->business('Report/CustomSar');
    }

    /**
     * 获取分账对象
     * @return void
     */
    public function getObjectList()
    {
        $res = $this->customSarBiz->getObjectList($this->_sid);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取分账对象详情
     * @return void
     */
    public function getObject()
    {
        $objectId = I('object_id', 0, 'intval');
        $res = $this->customSarBiz->getObject($this->_sid, $objectId);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 添加分账对象
     * @return void
     */
    public function addObject()
    {
        $submitData = I('post.');

        $submitData = htmlspecialchars_decode($submitData['data']);
        $submitData = json_decode($submitData, true);
        $name            = $submitData['name'] ?? '';
        $assignTicketIds = $submitData['assign_ticket_ids'] ?? [];
        $objectId        = $submitData['id'] ?? 0;

        $res = $this->customSarBiz->addObject($this->_sid, $this->_memberId, $name, $assignTicketIds, $objectId);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 删除分账对象
     * @return void
     */
    public function delObject()
    {
        $objectId = I('post.id', 0, 'intval');
        $res = $this->customSarBiz->deleteObject($objectId, $this->_sid);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取已关联票种
     * @return void
     */
    public function getObjectAssigned()
    {
        $res = $this->customSarBiz->getObjectAssigned($this->_sid);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取对象关联票种列表
     * @return void
     */
    public function getObjectTicketByNeed()
    {
        $pageNum = I('pageNum', 1, 'intval');
        $pageSize = I('page_size', 15, 'intval');

        $keyword   = I('key_word', '', 'strval');
        //门票范围 1=在售的门票 2=下架的门票 6=已删除的门票 99已过期 -1=不限
        $ticketScope = I('ticket_scope', '-1', 'strval');
        //商品来源 1-转分销 2-自供应
        $supplyType = I('supply_type', 2, 'intval');

        $res = $this->customSarBiz->getObjectTicketByNeed($this->_sid, $keyword, $ticketScope,
            $supplyType, $pageNum, $pageSize);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 获取所有报表模板配置
     */
    public function getTemplateList()
    {
        //报表类型
        $reportType = I('report_type', 1, 'intval');
        $res = $this->customSarBiz->getTemplateList($this->_sid, $this->_memberId, $reportType);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 配置报表模板
     * @return void
     */
    public function setTemplate()
    {
        //报表类型 5-预定报表 6-验证报表
        $reportType = I('post.report_type', 1, 'intval');
        //搜索纬度
        $item = I('post.item', []);
        //报表名称
        $name = I('post.name', '', 'strval,trim');
        //模板ID 如果为空则是新增模板
        $templateId = I('post.template_id', 0, 'intval');
        //数据范围
        $itemValue    = I('post.item_value', '', 'strval');
        //数据范围
        $itemNotValue    = I('post.item_not_value', '', 'strval');

        $itemValue = json_decode($itemValue, true) ?? [];
        $itemNotValue = json_decode($itemNotValue, true) ?? [];

        $res = $this->customSarBiz->setTemplate($this->_sid, $this->_memberId, $reportType, $item, $name,
            $itemValue, $itemNotValue, $templateId);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 删除模板
     * @return void
     */
    public function deleteTemplate()
    {
        //报表类型 5-预定报表 6-验证报表
        $reportType = I('post.report_type', 1, 'intval');
        //模板ID 如果为空则是新增模板
        $templateId = I('post.template_id', 0, 'intval');

        $res = $this->customSarBiz->deleteTemplate($this->_sid, $templateId, $reportType);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 统计模板配置里需要的信息
     * @return void
     */
    public function getTemplateNeed()
    {
        $type = I('type', '', 'strval');
        $pageNum = I('page_num', 1, 'intval');
        $pageSize = I('page_size', 2000, 'intval');

        $keyword   = I('key_word', '', 'strval');
        //门票范围 1=在售的门票 2=下架的门票 6=已删除的门票 99已过期 -1=不限
        $ticketScope = I('ticket_scope', '-1', 'strval');
        //商品来源 1-自供应 2-转分销
        $supplyType = I('supply_type', 1, 'intval');
        //员工状态范围    0=正常 1=禁用 2=已删除 -1=不限
        $operatorScope = I('operator_scope', '-1', 'strval');
        $keyWord   = I('post.key_word', '', 'strval');
        $res = $this->customSarBiz->getTemplateNeed($this->_sid, $type, $keyword, $ticketScope,
            $supplyType,$this->_loginInfo,$operatorScope,$keyWord, $pageNum, $pageSize);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 分账预订报表查询
     * <AUTHOR>
     * @date   2023/10/18
     *
     */
    public function orderTwoList()
    {
        $begin = I('post.begin', '', 'strval');
        $end   = I('post.end', '', 'strval');
        $tplId = I('post.tpl_id', 0, 'intval');
        $page  = I('post.page', 1, 'intval');
        $size  = I('post.page_size', 10, 'intval');

        if (empty($begin) || empty($end) || !strtotime($begin) || !strtotime($end)) {
            $this->apiReturn(203, [], '日期参数错误');
        }

        if (!$tplId) {
            $this->apiReturn(203, [], '模板参数错误');
        }

        $res= $this->customSarBiz->orderTwoList($this->_sid, $begin, $end, $tplId, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 分账验证报表查询
     * <AUTHOR>
     * @date   2023/10/18
     *
     */
    public function checkTwoList()
    {
        $begin = I('post.begin', '', 'strval');
        $end   = I('post.end', '', 'strval');
        $tplId = I('post.tpl_id', 0, 'intval');
        $page  = I('post.page', 1, 'intval');
        $size  = I('post.page_size', 10, 'intval');

        if (empty($begin) || empty($end) || !strtotime($begin) || !strtotime($end)) {
            $this->apiReturn(203, [], '日期参数错误');
        }

        if (!$tplId) {
            $this->apiReturn(203, [], '模板参数错误');
        }

        $res= $this->customSarBiz->checkTwoList($this->_sid, $begin, $end, $tplId, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}