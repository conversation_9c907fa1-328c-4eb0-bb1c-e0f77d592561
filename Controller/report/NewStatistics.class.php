<?php

namespace Controller\report;

use Business\Authority\DataAuthLimit;
use Business\Authority\DataAuthLogic;
use Business\Common\DictPayModeService;
use Business\JavaApi\Product\EvoluteListQuery;
use Library\Controller;

class NewStatistics extends Controller
{
    protected $memberId = null;
    protected $loginInfo = null;

    public function __construct()
    {
        parent::__construct();
        $this->memberId = $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo('ajax', false, false);
    }

    public function getListByLandAndTicketWithPagination()
    {
        $type = I('post.type', '', 'strval');
        $keyword = I('post.keyword', '', 'strval');
        $page = I('post.page', 1, 'intval');
        $pageSize = I('post.page_size', 20, 'intval');

        $fid = $this->loginInfo['sid'];

        switch ($type) {
            case 'lid':
                $this->getLandPagination($fid, $keyword, $page, $pageSize);
                break;
            case 'tid':
                $this->getTicketPagination($fid, $keyword, $page, $pageSize);
                break;
            case 'pay_way':
                $this->getPayModePagination($keyword, $page, $pageSize);
                break;
            case 'channel':
                $this->getOrderModePagination($keyword, $page, $pageSize);
                break;
            default:
                $this->apiReturn(400, [], "不存在的type类型");
        }
    }

    function getLandPagination($fid, $keyword, $page, $size)
    {
        $condition = $this->getConditionByDataPermission();
        // 全禁的情况下不需要调用接口
        if ($condition === false) {
            $this->apiReturn(self::CODE_SUCCESS);
        }

        $javaApi = new \Business\JavaApi\Product\EvoluteListQuery();
        $result = $javaApi->queryEvoluteLandByFid($fid, $keyword, $page, $size, ['Q'], [], $condition);
        $items = [];
        if (isset($result['list'])) {
            foreach ($result['list'] as $item) {
                $items[] = [
                    'id' => $item['id'],
                    'name' => $item['title'],
                ];
            }
        }
        $total = $result['total'] ?? 0;
        $this->apiReturn(200, [
            'items' => $items,
            'total' => $total,
        ], '');
    }

    function getTicketPagination($fid, $keyword, $page, $pageSize)
    {
        $condition = $this->getConditionByDataPermission();
        // 全禁的情况下不需要调用接口
        if ($condition === false) {
            $this->apiReturn(self::CODE_SUCCESS);
        }

        //处理下查询的产品参数  门票范围 1=在售的门票 2=下架的门票 6=已删除的门票 -1=不限
        $state = [];
        $result = (new EvoluteListQuery())->getAllEvoluteProduct($fid, $state, $keyword, $page, $pageSize, ['Q'], $condition);
        $items = [];
        if (isset($result['list'])) {
            foreach ($result['list'] as $item) {
                $items[] = [
                    'id' => $item['tid'],
                    'name' => $item['ticketName'],
                ];
            }
        }
        $total = $result['total'] ?? 0;
        $this->apiReturn(200, [
            'items' => $items,
            'total' => $total,
        ], '');
    }

    function getConditionByDataPermission()
    {
        if (DataAuthLogic::getInstance()->isRefererException($_SERVER['HTTP_REFERER'], [
            '/new/report_bookteamnew.html',
            '/new/newReport_resourcecenter.html',
            '/new/subterminal.html',
            '/new/report_showb.html',
            '/new/report_showb.html',
            '/new/cardreport_book.html',
            '/new/cardreport_check.html',
            '/new/cardreport_active.html',
        ])) {
            $dataAuthLimit = new DataAuthLimit();
        } else {
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();
        }
        return $dataAuthLimit->transInOrNotCondition();
    }

    function getPayModePagination($keyword, $page, $pageSize)
    {
        $payModes = DictPayModeService::getInstance()->businessPayWayListConf($this->memberId, true);
        $payModes[10000] = '成本';
        $payModes[10001] = '自采';
        $items = $this->mapConvertItems($payModes, $keyword);
        $this->apiReturn(200, [
            'items' => array_slice($items, ($page - 1) * $pageSize, $pageSize),
            'total' => count($items),
        ], '');
    }

    function getOrderModePagination($keyword, $page, $pageSize)
    {
        $orderModes = load_config('order_mode');
        $orderModes[0] = '正常分销商下单';
        $orderModes[1] = '普通用户支付';
        $orderModes[2] = '用户手机支付';
        $items = $this->mapConvertItems($orderModes, $keyword);
        $this->apiReturn(200, [
            'items' => array_slice($items, ($page - 1) * $pageSize, $pageSize),
            'total' => count($items),
        ], '');
    }

    function mapConvertItems(array $map, string $keyword): array
    {
        $items = [];
        foreach ($map as $key => $value) {
            if (!empty($keyword) && strpos($value, $keyword) === false) {
                continue;
            }
            $items[] = [
                'id' => $key,
                'name' => $value,
            ];
        }
        return $items;
    }
}