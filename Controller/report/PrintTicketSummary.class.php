<?php
/**
 * 票纸使用报表控制器层
 *
 * <AUTHOR>
 * @date 2021-9-1
 *
 */
namespace Controller\report;

use Library\Controller;
use Business\Report\PrintTicketSummary as PrintTicketSummaryBusiness;

class PrintTicketSummary extends Controller
{

    private $_loginInfo = null;

    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo();
    }

    public function getList()
    {

        //开始时间  yyyy-mm-dd
        $begin         = I('begin','','strval,trim');
        //结束时间  yyyy-mm-dd
        $end           = I('end','','strval,trim');
        //模板id
        $configId      = I('search_config_id',0,'intval');
        //页数
        $page          = I('page', 1, 'intval');
        //每页数量
        $pageSize      = I('page_size', 10, 'intval');
        //1 按日汇总  2按月汇总
        $dateType      = I('date_type', 1, 'intval');
        //是否导出
        $excel         = I('export_excel', false, 'boolval');

       if (empty($begin) || empty($end) || $configId < 1 || !in_array($dateType, [1,2])) {
           $this->apiReturn(204, [], '参数错误');
       }
        $btm = strtotime($begin);
        $etm = strtotime($end);
        if (date('Y', $btm) != date('Y', $etm)) {
            $this->apiReturn(204, [], '不支持跨年查询');
        }

        $printTicketSummaryBusiness = new PrintTicketSummaryBusiness();

        $rs = $printTicketSummaryBusiness->getList(
            $this->_loginInfo['sid'],
            $this->_loginInfo['memberID'],
            date('Ymd', $btm),
            date('Ymd', $etm),
            $configId,
            $page,
            $pageSize,
            $dateType,
            $excel
        );
        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }


}