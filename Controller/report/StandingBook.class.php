<?php
/**
 * 台账报表入口
 * <AUTHOR>
 * @date   2023/3/2
 */

namespace Controller\report;

use Library\Controller;
use Business\Statistics\StandingBook as StandingBookBiz;

class StandingBook extends Controller
{
    private $sid; //主账号id
    private $memberId; //当前账户id
    private $operateId; //当前账户id
    private $dtype; //当前账号类型
    private $loginInfo; //登陆信息

    private $summaryBiz;

    public function __construct()
    {
        $this->loginInfo = $this->getLoginInfo('ajax', false);
        $this->dtype     = $this->loginInfo['dtype']; //当前账号类型
        $this->operateId = $this->loginInfo['memberID']; //当前账户id
        $this->memberId  = $this->loginInfo['memberID'];
        $this->sid       = $this->loginInfo['sid']; //主账号id

        $this->summaryBiz = new StandingBookBiz();

        //设置登录参数
        $this->_setLoginInfo();
    }

    /**
     * 根据类型查询模板列表
     * <AUTHOR>
     * @date   2023/3/3
     *
     */
    public function getTemplate()
    {
        $type = I("post.report_type", '', 'strval'); //报表类型

        $result = $this->summaryBiz->getTemplateList($type);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 新增模板
     * <AUTHOR>
     * @date   2023/3/3
     *
     */
    public function addTemplate()
    {
        $title       = I("post.title", '', 'strval'); //报表名称
        $type        = I("post.report_type", '', 'strval'); //报表类型
        $showPrice   = I("post.show_price", '0', 'intval'); //是否展示票价
        $channel     = I("post.channel/a"); //是否展示渠道
        $payWay      = I("post.pay_way/a"); //是否展示支付方式
        $groupTicket = I("post.group_ticket/a"); //组合票类

        $showPrice = $showPrice == 0 ? 0 : 1;

        $result = $this->summaryBiz->addTemplate($title, $type, $showPrice, $channel, $payWay, $groupTicket);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 编辑模板
     * <AUTHOR>
     * @date   2023/3/3
     *
     */
    public function editTemplate()
    {
        $templateId  = I("post.id", '0', 'intval'); //是否展示票价
        $title       = I("post.title", '', 'strval'); //报表名称
        $showPrice   = I("post.show_price", '0', 'intval'); //是否展示票价
        $channel     = I("post.channel/a"); //是否展示渠道
        $payWay      = I("post.pay_way/a"); //是否展示支付方式
        $groupTicket = I("post.group_ticket/a"); //组合票类

        $showPrice = $showPrice == 0 ? 0 : 1;

        $result = $this->summaryBiz->editTemplate($templateId, $title, $showPrice, $channel, $payWay, $groupTicket);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 删除模板
     * <AUTHOR>
     * @date   2023/3/3
     *
     */
    public function delTemplate()
    {
        $templateId = I("post.id", '0', 'intval'); //模板id

        $result = $this->summaryBiz->delTemplate($templateId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取汇总列表
     * <AUTHOR>
     * @date   2023/3/7
     *
     */
    public function getOverall()
    {
        $startDt    = I("post.begin", '', 'strval'); //开始时间
        $endDt      = I("post.end", '', 'strval'); //结束时间
        $templateId = I("post.search_config_id", '0', 'intval'); //模板id

        $result = $this->summaryBiz->getOverallList($startDt, $endDt, $templateId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取预售报表
     * <AUTHOR>
     * @date   2023/3/7
     *
     */
    public function getSale()
    {
        $startDt    = I("post.begin", '', 'strval'); //开始时间
        $endDt      = I("post.end", '', 'strval'); //结束时间
        $templateId = I("post.search_config_id", '0', 'intval'); //模板id

        $result = $this->summaryBiz->getSaleAndCancelList($startDt, $endDt, $templateId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);

    }

    /**
     * 获取验证报表
     * <AUTHOR>
     * @date   2023/3/7
     *
     */
    public function getCheck()
    {
        $startDt    = I("post.begin", '', 'strval'); //开始时间
        $endDt      = I("post.end", '', 'strval'); //结束时间
        $templateId = I("post.search_config_id", '0', 'intval'); //模板id

        $result = $this->summaryBiz->getCheckAndRevokeList($startDt, $endDt, $templateId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取取消报表
     * <AUTHOR>
     * @date   2023/3/7
     *
     */
    public function getCancel()
    {
        $startDt    = I("post.begin", '', 'strval'); //开始时间
        $endDt      = I("post.end", '', 'strval'); //结束时间
        $templateId = I("post.search_config_id", '0', 'intval'); //模板id

        $result = $this->summaryBiz->getSaleAndCancelList($startDt, $endDt, $templateId,
            StandingBookBiz::REPORT_TYPE_CANCEL);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取撤销撤改
     * <AUTHOR>
     * @date   2023/3/7
     *
     */
    public function getRevoke()
    {
        $startDt    = I("post.begin", '', 'strval'); //开始时间
        $endDt      = I("post.end", '', 'strval'); //结束时间
        $templateId = I("post.search_config_id", '0', 'intval'); //模板id

        $result = $this->summaryBiz->getCheckAndRevokeList($startDt, $endDt, $templateId,
            StandingBookBiz::REPORT_TYPE_REVOKE);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 获取未验证报表
     * <AUTHOR>
     * @date   2023/3/7
     *
     */
    public function getNotCheck()
    {
        $startDt    = I("post.date", '', 'strval'); //开始时间
        $templateId = I("post.search_config_id", '0', 'intval'); //模板id

        $result = $this->summaryBiz->getNotCheckList($startDt, $templateId);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 根据景区获取上下架门票 自供应+转分销
     * <AUTHOR>
     * @date   2023/3/14
     *
     */
    public function getTemplateGroupTicket()
    {
        $detailId = I('post.detail_id', 0, 'intval');
        $keyWord  = I('post.key_word', '', 'strval,trim');
        $page     = I('post.page', 1, 'intval');
        //默认展示前100条
        $size   = I('post.size', 200, 'intval');
        $result = $this->summaryBiz->getTemplateGroupTicket($detailId, $keyWord, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 取登录用户的直销和转分销景区(只获取上下架的)
     * <AUTHOR>
     * @date   2023/3/16
     *
     */
    public function getTemplateGroupLand()
    {
        $page = I('post.page', 1, 'intval');
        //默认展示前100条
        $size    = I('post.size', 200, 'intval');
        $keyWord = I('post.key_word', '', 'strval,trim');

        $result = $this->summaryBiz->getTemplateGroupLand($keyWord, $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 设置用户信息
     * <AUTHOR>
     * @date   2021/7/17
     *
     * @return bool
     */
    private function _setLoginInfo()
    {
        $this->summaryBiz->dtype     = $this->dtype;
        $this->summaryBiz->sid       = $this->sid;
        $this->summaryBiz->memberId  = $this->memberId;
        $this->summaryBiz->operateId = $this->operateId;

        return true;
    }
}