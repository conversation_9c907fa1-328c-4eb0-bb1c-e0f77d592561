<?php
/**
 * 预订验证分钟报表
 * <AUTHOR>
 * @date   2023/10/16
 */

namespace Controller\report;

use Business\Authority\DataAuthLogic;
use Library\Controller;
use Business\Report\MinuteReport as MinuteReportBiz;

class MinuteReport extends Controller
{
    private $loginInfo = null;

    public function __construct()
    {
        $this->memberId  = $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo('ajax', false, false);
    }

    /**
     * 预订报表（分钟）
     * <AUTHOR>
     * @date   2023/10/16
     *
     */
    public function orderTwoList()
    {
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();

        $result = (new MinuteReportBiz())->minuteOrderTwoList(I('post.'), $this->loginInfo, $dataAuthLimit);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 验证报表（分钟）
     * <AUTHOR>
     * @date   2023/10/16
     *
     */
    public function checkTwoList()
    {
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimitFromSession();

        $result = (new MinuteReportBiz())->minuteCheckTwoList(I('post.'), $this->loginInfo, $dataAuthLimit);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }


}