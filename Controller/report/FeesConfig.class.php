<?php
/**
 * 费用配置 展示页在/html/new/d/fees.html 未重写 后期要做 可直接写在本文件内
 * 此处为导出功能
 *
 * <AUTHOR>
 * @date   2017-2-15
 */
namespace Controller\report;

use Library\Controller;
use Library\SimpleExcel;

class FeesConfig extends Controller {

    private $_modeArr = ['1' => '日结', '2' => '周结', '3' => '月结'];
    /**
     * 提供给应用中心的接口 返回总数
     */
    public function getTotalOutSide() {
        $params = $this->_handleParams();

        $total  = $this->getFeesTotal($params);

        return $total;
    }

    /**
     * 费用配置导出
     */
    public function exportExcel() {
        $code = 200;
        $msg  = '';
        try {
            $params = $this->_handleParams();

            $data   = $this->getFeesInfo($params, true);
            if (!is_array($data) || empty($data)) {
                throw new \Exception("数据为空");
            }
            $this->_startExport($data);
        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        if ($code == 400) {
            $this->apiReturn($code, '', $msg);
        }
    }

    /**
     * 参数处理
     */
    private function _handleParams() {

        //当前页
        //$currentPage   = $_REQUEST["currentPage"]?$_REQUEST["currentPage"]:1;
        //$pageSize      = $_REQUEST['pageSize']?$_REQUEST["pageSize"]:15;
        //$txt           = $_REQUEST["txt"];
        //$dtype         = $_REQUEST["dtype"];
        //$signPlatform  = $_REQUEST["sign_platform"];//平台使用符号
        //$feePlatform   = $_REQUEST["fee_platform"] * 100;//平台使用费
        //$signBank      = $_REQUEST["sign_bank"];//支付手续费符号
        //$feeBank       = $_REQUEST["fee_bank"];//支付手续费费用
        //$signSms       = $_REQUEST["sign_sms"];//短信费符号
        //$feeSms        = $_REQUEST["fee_sms"] * 100;//短信费费用
        //$signCode      = $_REQUEST["sign_code"];//凭证费符号
        //$feeCode       = $_REQUEST["fee_code"] * 100;//凭证费费用
        //$mode          = $_REQUEST['mode'];//是否筛选自动清分
        //$exclusive     = $_REQUEST['exclusive'];//是否筛选凭证费专项预存

        $page                     = $_REQUEST["page"] ? $_REQUEST["page"] : 1;      //当前页数
        $size                     = $_REQUEST['size'] ? $_REQUEST["size"] : 15;     //每页条数
        $identify                 = $_REQUEST["identify"];                          //搜索的内容
        $dtype                    = $_REQUEST["dtype"];                             //用户类型  0供应商 1分销商
        $searchType               = $_REQUEST["search_type"];                       //搜索类型 1名称 2账号 3联系人 4手机号
        $mode                     = $_REQUEST['mode'];                              //是否筛选自动清分
        $standaloneAccountEnabled = $_REQUEST['standalone_account_enabled'];        //独立收款类型 0.未开通 1.已开通 2.全部
        $feeType                  = $_REQUEST['fee_type'];                          //费用查询类型 1.平台使用费 2.提现手续费 3.短信费 4.凭证费 5.独立收款费率
        $feeSign                  = $_REQUEST['fee_sign'];                          //费用查询比较符号 1:小于 2:等于 3:大于
        $fee                      = $_REQUEST['fee'];                               //费用
        $exclusive                = $_REQUEST['exclusive'];                         //是否筛选凭证费专项预存

        $data = [
            'page'                       => $page,
            'size'                       => $size,
            'identify'                   => $identify,
            'dtype'                      => $dtype,
            'search_type'                => $searchType,
            'mode'                       => $mode,
            'standalone_account_enabled' => $standaloneAccountEnabled,
            'fee_type'                   => $feeType,
            'fee_sign'                   => $feeSign,
            'fee'                        => $fee,
            'exclusive'                  => $exclusive,
        ];

        return $data;
    }

    /**
     * 获取数据
     */
    public function getFeesInfo($params, $export = false) {
        if($export){
            $params['page'] = 0;
            $params['size'] = 999999;
        }

        if($params['mode'] == 1){
            $SettleBlanceModel = new \Model\Finance\SettleBlance();
            $fidArr = $SettleBlanceModel->getWithdrawSetFidList();//获取清分表所有用户
            if(!$fidArr){
                return 0;
            }
        }else{
            $fidArr = [];
        }

        $dname       = '';
        $account     = '';
        $contact     = '';
        $mobilePhone = '';
        if ($params['identify']) {
            switch ($params['search_type']) {
                case 2:
                    $account = $params['identify'];
                    break;
                case 3:
                    $contact = $params['identify'];
                    break;
                case 4:
                    $mobilePhone = $params['identify'];
                    break;
                default:
                    $dname = $params['identify'];
                    break;
            }
        }


        $MemberModel = new \Model\Member\Member();
        $data = $MemberModel->queryFeesMemberList(
            $params['dtype'],
            $dname,
            $account,
            $contact,
            $mobilePhone,
            $params['standalone_account_enabled'],
            $params['fee_type'],
            $params['fee_sign'],
            $params['fee'],
            $fidArr,
            false,
            $params['page'],
            $params['size'],
            $params['exclusive']
        );
        //$data = $MemberModel->getFeesMemberList(
        //    $params['txt'],
        //    $params['dtype'],
        //    $params['signPlatform'],
        //    $params['feePlatform'],
        //    $params['signBank'],
        //    $params['feeBank'],
        //    $params['signSms'],
        //    $params['feeSms'],
        //    $params['signCode'],
        //    $params['feeCode'],
        //    $fidArr,
        //    false,
        //    $params['page'],
        //    $params['pageSize']
        //);

        return $data['data'];
    }

    /**
     * 获取总数
     */
    public function getFeesTotal($params) {
        if($params['mode'] == 1){
            $SettleBlanceModel = new \Model\Finance\SettleBlance();
            $fidArr = $SettleBlanceModel->getWithdrawSetFidList();//获取清分表所有用户
            if(!$fidArr){
                return 0;
            }
        }else{
            $fidArr = [];
        }

        $dname       = '';
        $account     = '';
        $contact     = '';
        $mobilePhone = '';
        if ($params['identify']) {
            switch ($params['search_type']) {
                case 2:
                    $account = $params['identify'];
                    break;
                case 3:
                    $contact = $params['identify'];
                    break;
                case 4:
                    $mobilePhone = $params['identify'];
                    break;
                default:
                    $dname = $params['identify'];
                    break;
            }
        }


        $MemberModel = new \Model\Member\Member();
        $data = $MemberModel->queryFeesMemberList(
            $params['dtype'],
            $dname,
            $account,
            $contact,
            $mobilePhone,
            $params['standalone_account_enabled'],
            $params['fee_type'],
            $params['fee_sign'],
            $params['fee'],
            $fidArr,
            true,
            1,
            1,
            $params['exclusive']
        );

        return $data;
    }

    /**
     * 开始导出
     */
    private function _startExport($data) {
        if (empty($data) || !is_array($data)) {
            throw new \Exception("数据为空");
        }

        $excel[0] = [
            '供应商账号',
            '供应商名称',
            '联系人',
            '手机号',
            '平台使用费',
            '支付手续费用',
            '短信收费(元/条)',
            '电子凭证费(元/单)',
            '短信及凭证费收取',
            '自动清分方式',
            '清分手续费',
            '清分日期'
        ];
        $i = 1;
        foreach ($data as $key => $item) {
            if ($item['fee_pay_way'] == 1) {
                $value = '分销商';
            } elseif ($item['fee_pay_way'] == 0) {
                $value = '供应商';
            }

            $modeName = isset($this->_modeArr[$item['mode']]) ? $this->_modeArr[$item['mode']] : '无';
            //判断结算时间
            if ($item['mode'] == 1) {
                $closeTime = $item['close_time'] . ":00";
            } elseif ($item['mode'] == 2) {
                $closeTime = "星期" . $item['close_date'] . ' ' . $item['close_time'] . ":00";
            } elseif ($item['mode'] == 3) {
                $closeTime = $item['close_date'] . '号' . $item['close_time'] . ":00";
            } else {
                $closeTime = "无";
            }
            $excel[$i] = [
                $item['account'],
                $item['dname'],
                $item['cname'],
                $item['mobile'],
                $item['fee_platform'] / 100,
                $item['fee_bank'],
                $item['fee_sms'] / 100,
                $item['fee_code'] / 100,
                $value,
                $modeName,
                $item['service_fee'] . '‰',
                $closeTime
            ];
            $i++;
        }

        $xls = new SimpleExcel('UTF-8', true, '费用配置');
        $xls->addArray($excel);
        $xls->generateXML('费用配置');
    }
}