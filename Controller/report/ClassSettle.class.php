<?php

namespace Controller\report;

use Business\Statistics\ShiftSummary;
use Library\Controller;

class ClassSettle extends Controller
{

    //供应商id
    private $sid;
    private $loginInfo;

    public function __construct()
    {
        $this->loginInfo = $this->getLoginInfo();
        $this->sid = $this->loginInfo['sid'];
    }

    public function checkClassReportVersion()
    {
        $params = [
            'beginTime' => I('request.beginTime', ''),
            'endTime' => I('request.endTime', ''),
            'channel' => I('request.channel', ''),
            'sid' => $this->loginInfo['sid'],
            'memberId' => $this->loginInfo['memberID'],
        ];
        $biz = new ShiftSummary();
        $result = $biz->checkClassReportVersion($params);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    public function exportSettleReportForOld()
    {
        $params = [
            'csrIdArr' => I('post.csrIdArr', []),
            'sid' => $this->loginInfo['sid'],
            'memberId' => $this->loginInfo['memberID'],
        ];
        $biz = new ShiftSummary();
        $result = $biz->exportSettleReportForOld($params);
        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}