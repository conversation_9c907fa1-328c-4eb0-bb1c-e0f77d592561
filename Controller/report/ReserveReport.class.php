<?php
/**
 * 预约报表
 * <AUTHOR>
 * @date   2023/10/31
 */

namespace Controller\report;

use Library\Controller;
use Business\Report\ReserveReport as ReserveReportBiz;

class ReserveReport extends Controller
{
    private $loginInfo = null;

    /**
     * @var false|mixed
     */
    private $sid;

    public function __construct()
    {
        $this->sid       = $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo('ajax', false, false);
    }

    /**
     * 预约报表
     * <AUTHOR>
     * @date   2023/11/3
     *
     */
    public function getDailyList()
    {
        //报表模板id
        $configId = I('post.search_config_id', 0, 'intval');
        //开始日期
        $begin = I('post.begin', '', 'strval');
        //结束日期
        $end = I('post.end', '', 'strval');
        //景区id
        $lid = I('post.lid', '', 'strval');
        //门票id
        $tid = I('post.tid', '', 'strval');
        //支付方式
        $payWay = I('post.pay_way', '', 'strval');
        //渠道
        $channel = I('post.channel', '', 'strval');
        //分销商分组
        $resellerGroup = I('post.reseller_group', '', 'strval');
        //分销商
        $resellerId = I('post.reseller_id', '', 'strval');
        //票类范围
        $ticketScope = I('post.ticket_scope', '-1', 'strval');
        //页码
        $page = I('post.page', 1, 'intval');
        //页数
        $pageSize = I('post.page_size', 10, 'intval');

        //是否展示打包方的子票订单 0不展示 1展示
        $showSelfSonTicket = I('post.show_self_son_ticket', 0, 'intval');

        //是否展示演出捆绑票子票
        $showBindSonTicket = I('post.show_bind_son_ticket', 0, 'intval');

        if (!strtotime($begin) || !strtotime($end)) {
            $this->apiReturn(203, [], '时间参数错误');
        }

        if (!$configId) {
            $this->apiReturn(203, [], '请选择报表模板');
        }

        //分页限制
        $page < 1 && $page = 1;
        ($pageSize > 100 || $pageSize < 1) && $pageSize = 10;

        //整理参数
        $params = [
            'lid'                  => $lid,
            'tid'                  => $tid,
            'pay_way'              => $payWay,
            'channel'              => $channel,
            'reseller_id'          => $resellerId,
            'reseller_group'       => $resellerGroup,
            'ticket_scope'         => $ticketScope,
            'show_self_son_ticket' => $showSelfSonTicket,
            'show_bind_son_ticket' => $showBindSonTicket,
        ];

        $reserveReportBiz = new ReserveReportBiz();

        $reportType = 1;

        $result = $reserveReportBiz->getOrderList($this->sid, $configId, $begin, $end, $reportType, $params, $this->loginInfo, $page, $pageSize);

        $this->apiReturn($result['code'], $result['data'],  $result['msg']);
    }

    public function getMonthList()
    {
        //报表模板id
        $configId = I('post.search_config_id', 0, 'intval');
        //开始日期
        $begin = I('post.begin', '', 'strval');
        //结束日期
        $end = I('post.end', '', 'strval');
        //景区id
        $lid = I('post.lid', '', 'strval');
        //门票id
        $tid = I('post.tid', '', 'strval');
        //支付方式
        $payWay = I('post.pay_way', '', 'strval');
        //渠道
        $channel = I('post.channel', '', 'strval');
        //分销商分组
        $resellerGroup = I('post.reseller_group', '', 'strval');
        //分销商
        $resellerId = I('post.reseller_id', '', 'strval');
        //票类范围
        $ticketScope = I('post.ticket_scope', '-1', 'strval');
        //页码
        $page = I('post.page', 1, 'intval');
        //页数
        $pageSize = I('post.page_size', 10, 'intval');

        //是否展示打包方的子票订单 0不展示 1展示
        $showSelfSonTicket = I('post.show_self_son_ticket', 0, 'intval');

        //是否展示演出捆绑票子票
        $showBindSonTicket = I('show_bind_son_ticket', 0, 'intval');

        if (!strtotime($begin) || !strtotime($end)) {
            $this->apiReturn(203, [], '时间参数错误');
        }

        if (!$configId) {
            $this->apiReturn(203, [], '请选择报表模板');
        }

        //分页限制
        $page < 1 && $page = 1;
        ($pageSize > 100 || $pageSize < 1) && $pageSize = 10;

        //整理参数
        $params = [
            'lid'                  => $lid,
            'tid'                  => $tid,
            'pay_way'              => $payWay,
            'channel'              => $channel,
            'reseller_id'          => $resellerId,
            'reseller_group'       => $resellerGroup,
            'ticket_scope'         => $ticketScope,
            'show_self_son_ticket' => $showSelfSonTicket,
            'show_bind_son_ticket' => $showBindSonTicket,
        ];

        $reserveReportBiz = new ReserveReportBiz();

        $reportType = 2;

        $result = $reserveReportBiz->getOrderList($this->sid, $configId, $begin, $end, $reportType, $params, $this->loginInfo, $page, $pageSize);

        $this->apiReturn($result['code'], $result['data'],  $result['msg']);
    }
}