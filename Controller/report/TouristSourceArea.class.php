<?php
/**
 * 客源地统计
 * <AUTHOR>
 * @date   2024/07/04
 */

namespace Controller\report;

use Business\Authority\AuthLogic as AuthLogicBiz;
use Business\PftSystem\VacationModeBiz;
use Business\Statistics\TouristSourceArea\CommonBase as CommonBaseBiz;
use Library\Controller;
use Business\Statistics\TouristSourceArea\MemberConfig as MemberConfigBiz;
use Business\Statistics\TouristSourceArea\ReportManage as ReportManageBiz;
use Business\Statistics\TouristSourceArea\ReportExport as ReportExportBiz;

class TouristSourceArea extends Controller
{
    private $loginInfo;

    /**
     * @var false|mixed
     */
    private $sid;

    public function __construct()
    {
        $this->sid       = $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo('ajax', false, false);
    }

    /**
     * 用户客源地配置信息查询
     * <AUTHOR>
     * @date   2024/07/04
     *
     */
    public function getInfo()
    {
        $memberConfigBiz = new MemberConfigBiz();

        $res = $memberConfigBiz->getMemberInfo($this->sid);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 数据准备进度查询
     * <AUTHOR>
     * @date   2024/07/04
     *
     */
    public function getProgress()
    {
        $memberConfigBiz = new MemberConfigBiz();

        $res = $memberConfigBiz->getBrushingDataProgressInfo($this->sid);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 删除报表模板
     * <AUTHOR>
     * @date   2024/07/05
     *
     */
    public function templateDelete()
    {
        $templateId = I('post.template_id', 0, 'intval');
        if (empty($templateId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $reportManageBiz = new ReportManageBiz();

        $res = $reportManageBiz->templateDelete($this->sid, $this->loginInfo['memberID'], $templateId);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 客源地报表模板列表
     * <AUTHOR>
     * @date   2024/07/05
     *
     */
    public function templateList()
    {
        $reportManageBiz = new ReportManageBiz();

        $res = $reportManageBiz->templateList($this->sid, $this->loginInfo['memberID'], $this->loginInfo['dtype']);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 客源地报表模板提交
     * <AUTHOR>
     * @date   2024/07/05
     *
     */
    public function templateSubmit()
    {
        $templateId    = I('post.template_id', 0, 'intval');//模板ID
        $templateName  = I('post.template_name', '', 'strval,trim');//模板名称
        $itemValue     = I('post.item_value', '{}');//定制维度 lid,tid
        $customProduct = I('post.custom_product/a');//定制产品统计维度
        $sourceArea    = I('post.source_area/a');//客源地统计维度
        $action        = I('post.action/a');//操作类型

        //定制维度处理
        if (is_string($itemValue)) {
            $itemValue = htmlspecialchars_decode($itemValue);
        }
        $itemValue = json_decode($itemValue, true);
        if (!is_array($itemValue)) {
            $this->apiReturn(203, [], '定制维度错误');
        }

        empty($customProduct) && $customProduct = [];
        empty($sourceArea) && $sourceArea = [];
        empty($action) && $action = [];

        $sid      = $this->sid;
        $memberId = $this->loginInfo['memberID'];

        //如果是员工账号，需要判断权限
        if ($this->loginInfo['dtype'] == 6) {
            $hasAuth = (new AuthLogicBiz())->hasAuth($sid, $memberId,
                CommonBaseBiz::TOURIST_SOURCE_AREA_STATISTICS_TEMPLATE);
            if (!$hasAuth) {
                $this->apiReturn(400, '', '该账号无权限');
            }
        }

        $reportManageBiz = new ReportManageBiz();

        $res = $reportManageBiz->templateSubmit($sid, $memberId, $templateId, $templateName, $sourceArea, $action,
            $itemValue, $customProduct);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 报表模板配置字段枚举
     * <AUTHOR>
     * @date   2024/07/05
     *
     */
    public function getTemplateFieldEnum()
    {
        $reportManageBiz = new ReportManageBiz();

        $data = $reportManageBiz->templateFieldEnum();

        $this->apiReturn(200, $data);
    }

    /**
     * 区域下拉查询列表
     * <AUTHOR>
     * @date   2024/07/06
     *
     */
    public function getRegionList()
    {
        $areaName = I('post.key_word', '', 'strval,trim');//关键词
        $pageNum  = I('post.page_num', 1, 'intval');//页码
        $pageSize = I('post.page_size', 500, 'intval');//页数
        $level    = I('post.level', 1, 'intval'); //1省 直辖市 2 市区 3 区县 默认1

        $reportManageBiz = new ReportManageBiz();

        $res = $reportManageBiz->getRegionList($areaName, $level, $pageNum, $pageSize);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 客源地报表列表查询
     * <AUTHOR>
     * @date   2024/07/06
     *
     */
    public function getReportList()
    {
        $pageNum    = I('post.page_num', 1, 'intval');//页码
        $pageSize   = I('post.page_size', 10, 'intval');//页数
        $beginDate  = I('post.begin_date', '', 'strval,trim');//开始时间
        $endDate    = I('post.end_date', '', 'strval,trim');//结束时间
        $templateId = I('post.template_id', 0, 'intval');//模板ID
        $level      = I('post.level', 0, 'intval'); //0地域 1省 直辖市 2 市区 3 区县 默认0
        $dateType   = I('post.date_type', 0, 'intval'); //1=小时 2=日 3=月 4=年  默认1
        $levelCode  = I('post.level_code/a'); //区域编码 [1]=>-1; [2]=>[23,35]

        $sid      = $this->sid;
        $memberId = $this->loginInfo['memberID'];

        if (empty($beginDate) || empty($endDate) || empty($templateId) || (!empty($levelCode) && !is_array($levelCode))) {
            $this->apiReturn(203, [], '参数错误');
        }

        empty($pageSize) && $pageSize = 10;

        empty($levelCode) && $levelCode = [];

        $reportManageBiz = new ReportManageBiz();

        $res = $reportManageBiz->getReportList($sid, $memberId, $beginDate, $endDate, $templateId, $dateType, $level,
            $levelCode, [], [], $pageNum, $pageSize);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 客源地报表列表总人数查询
     * <AUTHOR>
     * @date   2024/07/08
     *
     */
    public function getTotalNum()
    {
        $beginDate  = I('post.begin_date', '', 'strval,trim');//开始时间
        $endDate    = I('post.end_date', '', 'strval,trim');//结束时间
        $templateId = I('post.template_id', 0, 'intval');//模板ID
        $dateType   = I('post.date_type', 0, 'intval'); //1=小时 2=日 3=月 4=年  默认1

        $sid      = $this->sid;
        $memberId = $this->loginInfo['memberID'];

        if (empty($beginDate) || empty($endDate) || empty($templateId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $reportManageBiz = new ReportManageBiz();

        $res = $reportManageBiz->getTotalNum($sid, $memberId, $beginDate, $endDate, $templateId, $dateType);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 客源地报表异步导出统计
     * <AUTHOR>
     * @date   2024/07/12
     *
     */
    public function exportExcel()
    {
        $saccount     = $this->loginInfo['saccount'];
        $vocationMode = (new VacationModeBiz())->judgeForPage('Judge', $saccount);
        if ($vocationMode === false) {
            $this->apiReturn(400, '当前处于假日模式，该功能被限制使用');
        }

        $beginDate  = I('post.begin_date', '', 'strval,trim');//开始时间
        $endDate    = I('post.end_date', '', 'strval,trim');//结束时间
        $templateId = I('post.template_id', 0, 'intval');//模板ID
        $dateType   = I('post.date_type', 0, 'intval'); //1=小时 2=日 3=月 4=年  默认1

        if (empty($beginDate) || empty($endDate) || empty($templateId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $sid      = $this->sid;
        $memberId = $this->loginInfo['memberID'];

        //如果是员工账号，需要判断权限
        if ($this->loginInfo['dtype'] == 6) {
            $hasAuth = (new AuthLogicBiz())->hasAuth($sid, $memberId,
                CommonBaseBiz::TOURIST_SOURCE_AREA_STATISTICS_EXCEL);
            if (!$hasAuth) {
                $this->apiReturn(400, '', '该账号无权限');
            }
        }

        $reportManageBiz = new ReportExportBiz();
        $res             = $reportManageBiz->createExportExcelTask($sid, $memberId, $beginDate, $endDate, $templateId,
            $dateType);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 客源地报表异步导出明细
     * <AUTHOR>
     * @date   2024/07/12
     *
     */
    public function exportDetail()
    {
        $saccount     = $this->loginInfo['saccount'];
        $vocationMode = (new VacationModeBiz())->judgeForPage('Judge', $saccount);
        if ($vocationMode === false) {
            $this->apiReturn(400, '当前处于假日模式，该功能被限制使用');
        }

        $beginDate  = I('post.begin_date', '', 'strval,trim');//开始时间
        $endDate    = I('post.end_date', '', 'strval,trim');//结束时间
        $templateId = I('post.template_id', 0, 'intval');//模板ID
        $dateType   = I('post.date_type', 0, 'intval'); //1=小时 2=日 3=月 4=年  默认1

        if (empty($beginDate) || empty($endDate) || empty($templateId)) {
            $this->apiReturn(203, [], '参数错误');
        }

        $sid      = $this->sid;
        $memberId = $this->loginInfo['memberID'];

        //单行导出处理参数
        $level     = I('post.level', -1, 'intval'); //0地域 1省 直辖市 2 市区 3 区县 默认0
        $areaCode  = I('post.code', 0, 'intval'); //区域编码
        $levelCode = [];
        if ($level != -1) {
            $levelCode = [$level => $areaCode];
        }

        //如果是员工账号，需要判断权限
        if ($this->loginInfo['dtype'] == 6) {
            $hasAuth = (new AuthLogicBiz())->hasAuth($sid, $memberId,
                CommonBaseBiz::TOURIST_SOURCE_AREA_STATISTICS_DETAIL);
            if (!$hasAuth) {
                $this->apiReturn(400, '', '该账号无权限');
            }
        }

        $reportManageBiz = new ReportExportBiz();
        $res             = $reportManageBiz->createExportDetailTask($sid, $memberId, $beginDate, $endDate, $templateId,
            $dateType, -1, $levelCode);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}