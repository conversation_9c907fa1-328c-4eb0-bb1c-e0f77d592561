<?php
namespace Controller\report;

use Business\Report\ChangeShiftsBusiness;
use Library\Controller;
use Library\SimpleExcel;

/**
 * @Author: CYQ19931115
 * @Date:   2017-10-18 13:42:54
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2017-12-18 18:21:28
 */

/**
 * 班结信息处理控制器
 */
class ChangeShifts extends Controller
{
    private $business;
    private $memberId;

    public function __construct()
    {
        $this->memberId = $this->isLogin();
        $this->business = new ChangeShiftsBusiness();
    }

    /**
     * 设置导出报表的员工到相应渠道
     * <AUTHOR>
     * @DateTime 2017-10-18T13:44:58+0800
     */
    public function setChannelUser()
    {
        /** @var int 企业的id */
        $pid = I("post.pid", $this->memberId);
        /** @var json 需要绑定的用户列表 需要绑定的用户id */
        $data = I("post.data_ids", '[]');
        /** @var 渠道信息 */
        $channel = I("post.channel");
        /** @var 需要统计类型的数据 */
        $countype = I("post.count_type", '[]');
        /** @var string 时：分：秒 */
        $time = I("post.time", "00:00:00", 'strval');
        //[$pid, $data, $channel, $time, $countype]
        try {
            $this->business->setChangeShiftReportInfo(compact('pid', 'data', 'channel', 'time', 'countype'));
            $this->apiReturn(200, [], "保存成功");
        } catch (\Exception $e) {
            $this->apiReturn(500, [], $e->getMessage());
        }
    }

    /**
     * 获取本企业的设置的渠道定时班结的信息
     * <AUTHOR>
     * @DateTime 2017-10-19T15:56:42+0800
     * @return
     */
    public function getChannelUser()
    {
        /** @var int 企业的id */
        $pid = I("get.pid", $this->memberId);
        try {
            $data = $this->business->getChangeShiftReportInfo($pid);
            $this->apiReturn(200, $data, "获取成功");
        } catch (\Exception $e) {
            $this->apiReturn(500, [], $e->getMessage());
        }
    }

    /**
     * 获取企业的员工id
     * <AUTHOR>
     * @DateTime 2017-10-23T09:46:25+0800
     */
    public function getStaffIds()
    {
        /** @var int 企业的id */
        $pid = I("get.pid", $this->memberId);
        try {
            $data = $this->business->getStaffs($pid);
            $this->apiReturn(200, $data, "获取成功");
        } catch (\Exception $e) {
            $this->apiReturn(500, [], "获取失败");
        }
    }

    /**
     * 获取网络销售报表数据
     * <AUTHOR>
     * @DateTime 2017-10-25T15:19:06+0800
     * @throws   Exception                可能抛出异常
     * @return   [type]                   [description]
     */
    public function getInternetExport()
    {

        $aid              = I("get.aid", $this->memberId); //分销商id
        $type             = I("get.type"); //导出类型
        $check_start_time = I("get.check_start_time"); //验证开始时间
        $check_end_time   = I("get.check_end_time"); //验证结束时间
        $order_start_time = I("get.order_start_time"); //下单开始时间
        $order_end_time   = I("get.order_end_time"); //下单开始时间

        try {
            $data = $this->business->getInternetSaleReport([
                $aid,
                $type,
                $check_start_time,
                $check_end_time,
                $order_start_time,
                $order_end_time,
            ]);
            $this->apiReturn(200, $data, "获取成功");
        } catch (\Exception $e) {
            $this->apiReturn(500, [], "获取失败:" . $e->getMessage());
        }
    }

    /**
     * 导出网络销售报表
     * <AUTHOR>
     * @DateTime 2017-10-26T15:13:38+0800
     * @throws   Exception                可能抛出异常
     * @return   [type]                   [description]
     */
    public function exportInternet()
    {
        $aid              = I("get.aid", $this->memberId); //分销商id
        $type             = I("get.type"); //导出类型
        $check_start_time = I("get.check_start_time"); //验证开始时间
        $check_end_time   = I("get.check_end_time"); //验证结束时间
        $order_start_time = I("get.order_start_time"); //下单开始时间
        $order_end_time   = I("get.order_end_time"); //下单开始时间
        try {
            $data = $this->business->getInternetSaleExport([
                $aid,
                $type,
                $check_start_time,
                $check_end_time,
                $order_start_time,
                $order_end_time,
            ]);
            if (empty($data[1])) {
                $this->apiReturn(500, [], "数据为空");
            }
            $SimpleExcel = new SimpleExcel();
            $SimpleExcel->addArray($data);
            $SimpleExcel->generateXML("网络销售报表-" . date("Y-m-d"));
        } catch (\Exception $e) {
            $this->apiReturn(500, [], "获取失败:" . $e->getMessage());
        }
    }
}
