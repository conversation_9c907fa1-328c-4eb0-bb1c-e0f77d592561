<?php
/*
 * 交易记录统计接口
 *
 * <AUTHOR>
 * @since  2016-09-12
 */
namespace Controller\report;
use Library\Controller;
use Model\TradeRecord;

class recordCount extends Controller {

    /*
     * 搜索
     * @param int $bTime    eq:20160101
     * @param int $eTime    eq:20160101
     * @param int $resellerId  分销商ID
     * @param int $ignoreType  =0  不过滤    =1  过滤测试账号    =2  过滤正式账号
     * @param int $page     第几页
     *
     * <AUTHOR>
     * @since  2016-09-12
     */
    public function index() {
        $bTime      = I('bTime');
        $eTime      = I('eTime');
        $resellerId = I('resellerId', 0, 'intval');
        $ignoreType = I('ignoreType', 0, 'intval');
        $page       = I('page', 1, 'intval');
        $pageSize   = 15;

        $bTime = strtotime($bTime) ? date('Ymd', strtotime($bTime)) : date('Ymd', time());
        $eTime = strtotime($eTime) ? date('Ymd', strtotime($eTime)) : date('Ymd', time());

        $pftTransReport = new Finance\PftTransReport();

        //搜索要求时间段内的所有fid
        $fidArr = $pftTransReport->getFidByTime($bTime, $eTime);

        $data = $pftTransReport->getRecordCountInfo($bTime, $eTime, $resellerId, $ignoreType, $fidArr);

        $itemCategory = load_config('item_category', 'trade_record');

        if (empty($data)) {
            return ['code' => false, 'msg' => '未查询到数据'];
        }

        foreach ($data as $key => $val) {
            $res[$val['fid']][$val['dtype']]['income'] = isset($res[$val['fid']][$val['dtype']]['income']) ?
                                                                    $res[$val['fid'][$val['dtype']]['income']] : 0;
            $res[$val['fid']][$val['dtype']]['expense'] = isset($res[$val['fid']][$val['dtype']]['expense']) ?
                                                                    $res[$val['fid']][$val['dtype']]['expense'] : 0;
            $res[$val['fid']][$val['dtype']]['income'] += $val['income'];
            $res[$val['fid']][$val['dtype']]['expense'] += $val['expense'];
        }
        unset($data);
        $i = 0;
        foreach ($res as $k => $v) {
            $list[$i]['id']                = $k;
            $list[$i]['name']              = 'TODO';
//            $list[$i]['lmoney'] = $v['lmoney'];
            $list[$i]['buy']               = $v[1]['income'];     //购买产品
            $list[$i]['orderIn']           = $v[2]['income'];     //修改取消订单  收入
            $list[$i]['orderOut']          = $v[2]['expense'];    //修改取消订单  支出
            $list[$i]['rechargeIn']        = $v[3]['income'];     //充值/扣款    收入
            $list[$i]['rechargeOut']       = $v[3]['expense'];    //充值/扣款    支出
            $list[$i]['credit']            = $v[4]['income'];
        }


    }
}
