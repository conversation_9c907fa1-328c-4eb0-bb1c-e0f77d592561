<?php
/**
 * 报表配置
 * <AUTHOR>
 * @date   2024/03/11
 */

namespace Controller\report;

use Library\Controller;
use Business\Report\StatisticsConfig as StatisticsConfigBiz;

class StatisticsConfig extends Controller
{

    private $sid; //主账号id
    private $memberId; //当前账户id
    private $loginInfo; //登陆信息

    public function __construct()
    {
        $this->loginInfo = $this->getLoginInfo('ajax');
        $this->memberId  = $this->loginInfo['memberID'];
        $this->sid       = $this->loginInfo['sid']; //主账号id
    }

    /**
     * 数据统计范围配置详情
     * <AUTHOR>
     * @date   2024/03/27
     *
     */
    public function getScopeConfig()
    {
        $res = (new StatisticsConfigBiz())->getScopeConfig($this->sid);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 数据统计范围设置
     * <AUTHOR>
     * @date   2024/03/27
     *
     */
    public function setScopeConfig()
    {
        $tagsKey   = I('post.tags_key/a');

        //$tagsKey验证是不是数组，非数组默认为空数组
        if (!is_array($tagsKey)) {
            $tagsKey = [];
        }

        $res = (new StatisticsConfigBiz())->setScopeConfig($this->sid, $this->memberId, $tagsKey);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

}