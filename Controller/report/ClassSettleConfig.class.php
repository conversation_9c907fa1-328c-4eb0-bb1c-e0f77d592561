<?php
/**
 * 班结配置管理
 * User: lanwanhui
 * Date: 2021/2/26
 * Time: 15:55
 */

namespace Controller\report;

use Library\Controller;
use Business\Report\ClassSettleConfig as ClassSettleConfigBusiness;

class ClassSettleConfig extends Controller
{

    //供应商id
    private $sid ;

    public function __construct()
    {

        $loginInfo         = $this->getLoginInfo();

        $this->sid   = $loginInfo['sid'];

    }

    public function getClassSettleConfig()
    {
        $classSettleConfigBiz = new ClassSettleConfigBusiness();

        $rs = $classSettleConfigBiz->getClassSettleConfig($this->sid);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }

    public function setClassSettleConfig()
    {
        $verifyState     = I('verify_state', '', 'strval');
        if (!in_array($verifyState, ['0','1'], true)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $classSettleConfigBiz = new ClassSettleConfigBusiness();

        $rs = $classSettleConfigBiz->setClassSettleConfig($this->sid, $verifyState);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);


    }


    /**
     * 获取有审核权限的员工
     * User: lanwanhui
     * Date: 2021/3/23
     *
     *
     */
    public function getLimitStaff(){

        $classSettleConfigBiz = new ClassSettleConfigBusiness();

        $rs = $classSettleConfigBiz->getClassSettleStaff($this->sid);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);
    }


    /**
     * 修改可审核员工id
     *
     * User: lanwanhui
     * Date: 2021/3/23
     *
     */
    public function saveLimitStaff(){

        $staffIds = I('staff_ids','', 'strval,trim');

        $staffIdArray  = explode(',',$staffIds);

        foreach ($staffIdArray as $k => $id) {
            if (!preg_match("/^[1-9]{1}[0-9]{0,9}$/", $id)) {
                unset($staffIdArray[$k]);
            }
        }

        $staffIdArray = array_values(array_unique($staffIdArray));

        if (empty($staffIdArray)) {
            $staffIds = '';
        } else {
            $staffIds  = implode(',', $staffIdArray);
            if (strlen($staffIds) > 1000) {
                $this->apiReturn(204, [], '添加分员工过多');
            }
        }

        $classSettleConfigBiz = new ClassSettleConfigBusiness();

        $rs = $classSettleConfigBiz->saveClassSettleStaff($this->sid, $staffIds);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }
}