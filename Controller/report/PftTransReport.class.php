<?php

namespace Controller\report;

use Library\Controller;
use Model\TradeRecord;

class PftTransReport extends Controller {

    public function runTask($time = '')
    {

        if (empty($time)) {
            $time = I('time');
        }

        if (!strtotime($time)) {
            exit(json_encode(['code' => 'fail', msg => '时间格式错误']));
        }

        $this->pftMemberJournal = new TradeRecord\PftMemberJournal();
        $this->pftTransReport = new TradeRecord\PftTransReport();

        $execTime = date('Y-m-d', strtotime($time));

        //跑数据前删除指定日期的已有数据
        $del = $this->pftTransReport->deleteData($execTime);
        if ($del === false) {
            exit(json_encode(['code' => 'fail', 'msg' => '删除旧数据失败']));
        }
        //获取脚本执行当日的前一天所有有交易记录的fid和信用账户里的aid
        $res = $this->pftMemberJournal->getTransFids($execTime);

        if ($res === false) {
            exit(json_encode(['code' => 'fail', 'msg' => '获取fid信息有误']));
        }

        try {
            //通过每个fid和账户类型（$b）搜索昨天的交易记录 信用账户比较特殊 还要搜索aid
            $insertTotalArr = [];
            $insertDetailArr = [];
            foreach ($res as $key => $val) {

                $data = $this->pftMemberJournal->getListInfoByFidAndActype((int)$val, $execTime);

                if (isset($data['code']) && $data['code'] == 'fail') {
                    throw new \Exception($data['msg']);
                }

                if ($data['totalArr']) {
                    $resultTotal[] = $data['totalArr'];
                }
                if ($data['detailArr']) {
                    $resultDetail[] = $data['detailArr'];
                }
                unset($data);


                if (!empty($resultTotal)) {
                    $totalPart = true;     //是否有最后一次未插入的数据
                    $insertTotalArr = array_merge($insertTotalArr, $resultTotal);
                    if (count($insertTotalArr) >= 500) {
                        $insertTotalRes = $this->pftTransReport->insertTotalDataIntoTable($insertTotalArr, $execTime);
                        $insertTotalArr = [];
                    }
                    unset($resultTotal);
                }

                if (!empty($resultDetail)) {
                    $detailPart = true;
                    $insertDetailArr = array_merge($insertDetailArr, $resultDetail);
                    if (count($insertDetailArr) >= 500) {
                        $detailPart = false;
                        $insertDetailRes = $this->pftTransReport->insertDetailDataIntoTable($insertDetailArr, $execTime);
                        $insertDetailArr = [];
                    }
                    unset($resultDetail);
                }

                if ($insertTotalRes === false || $insertDetailRes === false) {
                    throw new \Exception("数据写入失败");
                }
            }

            //最后一部分的数据处理
            if ($totalPart) {
                $insertTotalRes = $this->pftTransReport->insertTotalDataIntoTable($insertTotalArr, $execTime);
            }

            if ($detailPart) {
                $insertDetailRes = $this->pftTransReport->insertDetailDataIntoTable($insertDetailArr, $execTime);
            }

            if ($insertTotalRes === false || $insertDetailRes === false) {
                throw new \Exception("数据写入失败");
            }

            $this->pftTransReport->recordTaskInfo($execTime, 1);
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            exit($msg);
        }
    }
}