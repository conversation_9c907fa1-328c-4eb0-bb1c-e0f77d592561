<?php
/**
 * 分账配置相关接口
 * Created by PhpStorm.
 * User: zhangyangzhen
 * Date: 2019/3/20
 * Time: 13:54
 */

namespace Controller\report;

use Business\JavaApi\TicketApi;
use Business\MemberLogin\MemberLoginHelper;
use Business\Product\HandleTicket;
use Business\Product\ProductList;
use Business\Product\Ticket;
use Library\Controller;
use Model\Report\SeparateConfig;
use mysql_xdevapi\Exception;

class SubAccount extends Controller
{
    private $_sid      = null;//用户上级ID
    private $_memberId = null;//用户ID

    private $_separateModel = null;

    //分账类型
    private $_separateType = [
        1 => '比例',
        2 => '金额',
        3 => '固定比例',
        4 => '固定金额',
        5 => '门限',
    ];

    //分账配置产品类型
    private $_configType = [
        1 => '捆绑产品',
        2 => '基础产品',
    ];

    public function __construct()
    {
        $this->_sid         = $this->isLogin('ajax');
        $loginInfo          = $this->getLoginInfo('ajax', false, false);
        $this->_memberId    = $loginInfo['memberID'];

        if (empty($this->_separateModel)) {
            $this->_separateModel = new SeparateConfig();
        }
    }

    /**
     * 获取分账对象
     * Create by zhangyangzhen
     * Date: 2019/3/21
     * Time: 20:19
     *
     * @return array
     */
    public function getObject()
    {
        //获取分账对象
        $object = $this->_separateModel->getObject($this->_sid, 1, false, 'id desc', 'id,name');

        //返回门票渠道
        $orderMode = load_config('order_mode_separate', 'orderSearch');

        $channel = [];
        foreach ($orderMode as $key => $val) {
            $channel[] = [
                'id'   => (string)$val['key'],
                'name' => $val['name'],
            ];
        }

        $data = [
            'object'  => $object,
            'channel' => $channel,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $data, '');
    }

    /**
     * 新增分账对象
     * Create by zhangyangzhen
     * Date: 2019/3/20
     * Time: 14:39
     *
     * @param  string  $name  分账对象名称
     *
     * @return array
     */
    public function addObject()
    {
        $name = I('post.name', '', 'strval,trim');

        if (empty($name) || !$name) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分账对象名称不能为空');
        }

        $res = $this->_separateModel->addSeparateObj($this->_sid, $name);

        if (!$res) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '删除失败');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, $res, 'success');
        }
    }

    /**
     * 删除分账对象（逻辑删除）
     * Create by zhangyangzhen
     * Date: 2019/3/20
     * Time: 14:53
     *
     * @param  int  $id  分账对象ID
     *
     * @return array
     */
    public function delObject()
    {
        $id = I('post.id', 0, 'intval');

        if (!$id || $id <= 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $bool = $this->_separateModel->validObject($this->_sid, $id);

        if ($bool) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '该分账对象正在使用中，不允许删除');
        }

        $res = $this->_separateModel->delSeparateObj($this->_sid, $id);

        if (!$res) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '删除失败');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, [], 'success');
        }
    }

    /**
     * 保存(新增and编辑)分账配置
     * Create by zhangyangzhen
     * Date: 2019/3/24
     * Time: 18:03
     */
    public function setSeparateConfig()
    {
        $params = I('post.');

        //数据判断处理
        $this->_handleParams($params);

        $date = date('Ymd');

        $this->_separateModel->startTrans();

        $code = 200;
        $msg  = 'success';
        try {
            //先判断是否存在有效配置，存在的话，先删除（修改状态），再新增一条数据
            if (!empty($params['ticket'])) {
                foreach ($params['ticket'] as $key => $val) {
                    foreach ($val as $k => $v) {
                        //获取有效的配置
                        $res = $this->_separateModel->getMemberSeparateConfig($this->_sid, $key, $k,
                            $params['config_type'], 0, false);

                        if (!empty($res)) {
                            $rst = $this->_separateModel->deleteSeparateConfig($res['config_id']);

                            if (!$rst) {
                                throw new \Exception('配置修改失败');
                            }

                            $date = date('Ymd', strtotime("+1 day"));
                        }
                    }
                }
            }

            $list = $params['single'];
            if (!empty($list)) {
                foreach ($list as $key => $val) {
                    $res = $this->_separateModel->getMemberSeparateConfig($this->_sid, [], $key, $params['config_type'],
                        1, false);

                    if (!empty($res)) {
                        $rst = $this->_separateModel->deleteSeparateConfig($res['config_id']);

                        if (!$rst) {
                            throw new \Exception('配置修改失败');
                        }

                        $date = date('Ymd', strtotime("+1 day"));
                    }
                }
            }

            $res = $this->_separateModel->createSeparateConfig($params, $this->_sid, $this->_memberId, $date);

            if ($res[0] != 200) {
                throw new \Exception($res[1]);
            }
        } catch (\Exception $e) {
            $this->_separateModel->rollback();
            $code = self::CODE_PARAM_ERROR;
            $msg  = $e->getMessage();
        }

        $this->_separateModel->commit();

        $this->apiReturn($code, [], $msg);
    }

    /**
     * 分账配置参数处理
     * Create by zhangyangzhen
     * Date: 2019/3/24
     * Time: 14:18
     *
     * @param  array  $ticketData  分账配置套票子票数据
     * @param  int  $separateType  分账类型
     * @param  int  $configType  分账配置产品类型
     * @param  int  $lid  景区ID
     * @param  string  $channel  销售渠道
     *
     * @return mixed
     */
    private function _handleParams($params)
    {
        if ((empty($params['ticket']) && empty($params['single']))
            || (!empty($params['ticket']) && !empty($params['single']))
            || empty($params['separate_type']) || empty($params['config_type'])
            || !isset($params['sale_channel']) || !isset($params['settlement_price'])) {

            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $ticketData   = $params['ticket'];
        $singleData   = $params['single'];
        $separateType = $params['separate_type'];
        $configType   = $params['config_type'];
        $settlePrice  = $params['settlement_price'];

        if (!$separateType || !$this->_separateType[$separateType]) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分账类型错误');
        }

        if (!$configType || !$this->_configType[$configType]) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分账配置产品类型错误');
        }

        //非单独售卖产品配置参数检查
        if (!empty($ticketData)) {
            foreach ($ticketData as $packId => $son) {
                if (empty($son) || !$son) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
                }

                foreach ($son as $key => $val) {
                    if (!isset($val['value']) || (empty($val['value']) && intval($val['value']) !== 0)) {
                        $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入分配比例/金额');
                    }

                    if (empty($val['pid']) || empty($val['lid']) || empty($val['ttitle']) || empty($val['aid'])) {
                        $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
                    }

                    if (in_array($separateType,
                        [SeparateConfig::__SEPARATE_TYPE_MONEY__, SeparateConfig::__SEPARATE_TYPE_FIXED_MONEY__])) {
                        if (!is_numeric($val['value']) || $val['value'] < 0) {
                            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分账金额设置有误');
                        }
                    } elseif (in_array($separateType,
                        [SeparateConfig::__SEPARATE_TYPE_PROP__, SeparateConfig::__SEPARATE_TYPE_FIXED_PROP__])) {
                        if (!is_numeric($val['value']) || $val['value'] < 0 || $val['value'] > 100) {
                            $this->apiReturn(self::CODE_PARAM_ERROR, [], '分账比例设置有误');
                        }
                    }
                }
            }
        }

        //单独售卖产品配置参数检查
        if (!empty($singleData)) {
            $total  = 0;
            $object = [];
            foreach ($singleData as $ticketId => $config) {
                foreach ($config as $k => $item) {
                    if (!isset($item['value']) || (empty($item['value']) && intval($item['value']) !== 0)) {
                        $this->apiReturn(self::CODE_PARAM_ERROR, [], '请输入分账比例或金额');
                    }

                    if (empty($item['pid']) || empty($item['lid']) || empty($item['ttitle']) || empty($item['aid'])) {
                        $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
                    }

                    if (in_array($item['object'], $object)) {
                        $this->apiReturn(self::CODE_PARAM_ERROR, [], '分账对象不能重复使用');
                    }

                    array_push($object, $item['object']);

                    $total += $item['value'];
                }
            }

            if (in_array($separateType,
                [SeparateConfig::__SEPARATE_TYPE_PROP__, SeparateConfig::__SEPARATE_TYPE_FIXED_PROP__])) {
                if ($total != 100) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '分账比例总和必须等于100%');
                }
            } else {
                if ($total != $settlePrice) {
                    $this->apiReturn(self::CODE_PARAM_ERROR, [], '分账金额总和必须等于成本价');
                }
            }
        }
    }

    /**
     * 用户配置查询列表
     * Create by zhangyangzhen
     * Date: 2019/3/26
     * Time: 20:43
     *
     * @param  int lid   景区ID
     * @param  int aid   供应商ID
     * @param  int page  页码
     * @param  int size  每页数量
     *
     * @return array
     */
    public function getList()
    {
        $lid   = I('post.lid', 0, 'intval');
        $aid   = I('post.aid', 0, 'intval');
        $page  = I('post.page', 1, 'intval');
        $size  = I('post.size', 10, 'intval');
        $ptype = I('post.ptype', '', 'strval,trim');

        if ($lid < 1 || $aid < 1 || !$ptype) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        // 走JAVA接口
        $productBiz = new \Business\Product\Product();
        $landInfo   = $productBiz->getWPTProductInfo($this->_sid, $aid, $lid);

        if ($landInfo['code'] != self::CODE_SUCCESS) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '请求出错');
        }

        //该套票产品下的所有套票主票信息
        $ticket = $landInfo['data']['ticketList'];

        if (empty($ticket)) {
            $this->apiReturn(self::CODE_SUCCESS, ['count' => 0, 'list' => []]);
        }

        if ($ptype == 'F') {
            $data = $this->_handlePackTicketList($ticket, SeparateConfig::__CONFIG_TYPE_PACK__, $page, $size);
        } else {
            $data = $this->_handleSonTicketList($ticket, SeparateConfig::__CONFIG_TYPE_BASE__, $page, $size);
        }

        $this->apiReturn(self::CODE_SUCCESS, $data);
    }

    /**
     * 处理基础产品分账配置搜索列表
     * Create by zhangyangzhen
     * Date: 2019/3/29
     * Time: 10:53
     *
     * @param  array  $ticket  该产品下的所有票信息
     * @param  int  $page
     * @param  int  $size
     *
     * @return array
     */
    private function _handleSonTicketList($ticket, $configType, $page, $size)
    {
        $tids = array_column($ticket, 'ticketId');

        //对该产品下的票类进行group查询，然后才能进行分页处理
        $groupData = $this->_separateModel->getGroupData($this->_sid, [], $tids, $configType, 'p.config_id', $page,
            $size);

        if (empty($groupData['list'])) {
            $this->apiReturn(self::CODE_SUCCESS, ['count' => 0, 'list' => []]);
        }

        $tids  = array_column($groupData['list'], 'tid');
        $count = $groupData['count'];

        $ticketList = [];
        foreach ($ticket as $key => $val) {
            if (in_array($val['ticketId'], $tids)) {
                $ticketList[$val['ticketId']] = $val;
            }
        }

        $list = $this->_separateModel->getMemberSeparateConfig($this->_sid, [], $tids, $configType, 0);

        if (empty($list)) {
            $this->apiReturn(self::CODE_SUCCESS, ['count' => 0, 'list' => []]);
        }

        $data = [];
        // $tag  = [];
        // foreach ($tids as $k => $v) {
        //     $tag[$v] = 0;
        // }

        foreach ($list as $key => $value) {
            if (isset($ticketList[$value['tid']])) {
                //单独售卖产品处理
//                 $singleConf = $this->_separateModel->getMemberSeparateConfig($this->_sid, [], $value['tid'], $configType, 1, false);
//                 if ($singleConf && $tag[$value['tid']] == 0) {
//                     $single           = $singleConf;
//                     $single['ttitle'] = $ticketList[$value['tid']]['ticketName'];
//                     $single['tnum']   = 1;
//
// //                    if (in_array($singleConf['separate_type'], [SeparateConfig::__SEPARATE_TYPE_PROP__, SeparateConfig::__SEPARATE_TYPE_FIXED_PROP__])) {
// //                        $single['separate_value'] = $singleConf['separate_value'] . "%";
// //                    } else {
// //                        $single['separate_value'] = $singleConf['separate_value'] . "元";
// //                    }
//                     $single['separate_value'] = "-";//单独售卖的产品不显示配置金额or比例
//
//                     if ($data[$single['config_id']]) {
//                         $data[$single['config_id']]['ticketArr'][]  = $single;
//                     } else {
//                         $ticketList[$single['tid']]['configId']     = $single['config_id'];
//
//                         unset($ticketList[$single['tid']]['ticketArr']);
//
//                         $ticketList[$single['tid']]['ticketArr'][]  = $single;
//                         $data[$single['config_id']]                 = $ticketList[$single['tid']];
//                     }
//
//                     $tag[$single['tid']] = 1;
//                 }

                if ($value['is_single_sale'] != 1) {
                    //非单独售卖产品处理
                    $packList = HandleTicket::getLinkPackTicket($this->_sid, $value['tid'], true);

                    if ($packList[$value['pack_tid']]) {
                        $value['ltitle'] = $packList[$value['pack_tid']]['ltitle'];
                        $value['ttitle'] = $packList[$value['pack_tid']]['ttitle'];
                        $value['tnum']   = $packList[$value['pack_tid']]['num'];

                        if (in_array($value['separate_type'],
                            [SeparateConfig::__SEPARATE_TYPE_PROP__, SeparateConfig::__SEPARATE_TYPE_FIXED_PROP__])) {
                            $value['separate_value'] = $value['separate_value'] . "%";
                        } else {
                            $value['separate_value'] = $value['separate_value'] . "元";
                        }
                    }

                    if ($data[$value['config_id']]) {
                        $data[$value['config_id']]['ticketArr'][] = $value;
                    } else {
                        $ticketList[$value['tid']]['configId'] = $value['config_id'];

                        unset($ticketList[$value['tid']]['ticketArr']);

                        $ticketList[$value['tid']]['ticketArr'][] = $value;
                        $data[$value['config_id']]                = $ticketList[$value['tid']];
                    }
                }
            }
        }

        $data = [
            'list'  => $data,
            'count' => $count,
        ];

        return $data;
    }

    /**
     * 处理捆绑产品分账配置搜索列表
     * Create by zhangyangzhen
     * Date: 2019/3/29
     * Time: 10:54
     *
     * @param  array  $ticket  该产品下的所有套票主票信息
     * @param  int  $page
     * @param  int  $size
     *
     * @return array
     */
    private function _handlePackTicketList($ticket, $configType, $page, $size)
    {
        $tids = array_column($ticket, 'ticketId');

        //对该产品下的套票主票进行group查询，然后才能进行分页处理
        $groupData = $this->_separateModel->getGroupData($this->_sid, $tids, [], $configType, 'p.pack_tid', $page,
            $size);

        if (empty($groupData['list'])) {
            $this->apiReturn(self::CODE_SUCCESS, ['count' => 0, 'list' => []]);
        }

        //有配置的套票ID集合
        $packIds = array_column($groupData['list'], 'pack_tid');
        $count   = $groupData['count'];

        //处理套票主票数据
        $ticketList = [];
        foreach ($ticket as $key => $val) {
            if (in_array($val['ticketId'], $packIds)) {
                $ticketList[$val['ticketId']] = $val;
            }
        }

        //获取该用户的该套票产品（所有套票）的分账配置
        $list = $this->_separateModel->getMemberSeparateConfig($this->_sid, $packIds, [], $configType, 0);

        if (empty($list)) {
            $this->apiReturn(self::CODE_SUCCESS, ['count' => 0, 'list' => []]);
        }

        foreach ($list as $key => $val) {
            $sonTicketInfo = HandleTicket::getSonTicket($val['pack_tid']);

            if (isset($sonTicketInfo[$val['tid']])) {
                $val['ltitle'] = $sonTicketInfo[$val['tid']]['ltitle'];
                $val['ttitle'] = $sonTicketInfo[$val['tid']]['ttitle'];
                $val['tnum']   = $sonTicketInfo[$val['tid']]['num'];

                if (in_array($val['separate_type'],
                    [SeparateConfig::__SEPARATE_TYPE_PROP__, SeparateConfig::__SEPARATE_TYPE_FIXED_PROP__])) {
                    $val['separate_value'] = $val['separate_value'] . "%";
                } else {
                    $val['separate_value'] = $val['separate_value'] . "元";
                }
            }

            if (isset($ticketList[$val['pack_tid']])) {
                $ticketList[$val['pack_tid']]['configId']    = $val['config_id'];
                $ticketList[$val['pack_tid']]['ticketArr'][] = $val;
            }
        }

        $data = [
            'list'  => $ticketList,
            'count' => $count,
        ];

        return $data;
    }

    /**
     * 编辑页面获取配置信息
     * Create by zhangyangzhen
     * Date: 2019/3/28
     * Time: 14:14
     *
     * @param  int ticket_id 套票ID
     * @param  int config_id 配置ID
     *
     * @return array
     */
    public function editConfig()
    {
        $ticketId = I('post.ticket_id', 0, 'intval');
        $configId = I('post.config_id', 0, 'intval');

        if ($ticketId <= 0 || $configId <= 0) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $ticketModel = new TicketApi();

        //获取套票主票信息
        $ticketBiz = new \Business\CommodityCenter\Ticket();
        $ticket    = $ticketBiz->fieldConversion($ticketBiz->queryTicketInfoById($ticketId));
        //$ticket = $ticketModel->getTickets($ticketId);
        $price = $ticketModel->refreshPrice($this->_sid, $ticket['account_id'], $ticket['id']);

        if ($ticket['type'] == 'F') {
            //获取套票子票信息以及配置信息
            $ticketArr = $this->_handleSonTicketConfig($ticketId);

            foreach ($ticketArr as $key => $value) {
                if ($value['config_id'] != $configId) {
                    $ticketArr[$key]['disabled'] = 1;
                }
            }
        } else {
            $allConf   = $this->_getPackTicketConfigV2($ticketId, $configId);
            $ticketArr = $allConf['list'];
            $singleArr = $allConf['single'];
        }

        if ($singleArr && !empty($singleArr)) {
            $singleConfigId = array_column($singleArr, 'config_id');

            if (in_array($configId, $singleConfigId)) {
                $isSingle = 1;
            } else {
                $isSingle = 0;
            }
        } else {
            $isSingle = 0;
        }

        $configIdValue  = array_column($ticketArr, 'separate_value', 'config_id');
        $configIdObject = array_column($ticketArr, 'object_id', 'config_id');

        //获取配置信息
        $config = $this->_separateModel->getSeparateConfig($configId);

        $data = [
            'land_id'        => $ticket['item_id'],
            'product_id'     => $ticket['product_id'],
            'land_name'      => $ticket['land_name'],
            'price'          => $price['cost_price'],
            'aid'            => $ticket['account_id'],
            'ticket_id'      => $ticket['id'],
            'ticket_name'    => $ticket['name'],
            'config_type'    => $config['config_type'],
            'separate_type'  => $config['separate_type'],
            'separate_value' => $configIdValue[$configId],
            'object_id'      => $configIdObject[$configId],
            'config_shop'    => $ticketArr ? implode(',',
                array_unique(array_filter(array_column($ticketArr, 'channel')))) : '',
            'single_shop'    => $singleArr ? implode(',',
                array_unique(array_filter(array_column($singleArr, 'channel')))) : '',
            'is_single'      => $isSingle,
            'ticketArr'      => $ticketArr,
            'singleArr'      => $singleArr,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $data);
    }

    /**
     * 根据配置ID删除用户配置
     * Create by zhangyangzhen
     * Date: 2019/3/26
     * Time: 11:25
     *
     * @param  int config_id 配置ID
     *
     * @return array
     */
    public function deleteSeparateConfig()
    {
        $configId = I('post.config_id', 0, 'intval');
        $config   = $this->_separateModel->getSeparateConfig($configId);

        $sid = $this->_sid;
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        //判断主账号ID是否一样
        if ($sid != $config['fid']) {
            $this->apiReturn(self::CODE_AUTH_ERROR, [], '没有权限！');
        }

        $res = $this->_separateModel->deleteSeparateConfig($configId, 1);

        if (!$res) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '删除失败');
        } else {
            $this->apiReturn(self::CODE_SUCCESS, [], '删除成功');
        }
    }

    /**
     * 通过套票ID获取子票，以及子票的配置信息
     * Create by zhangyangzhen
     * Date: 2019/3/21
     * Time: 21:28
     *
     * @param  int  $ticketId  套票ID
     *
     * @return array
     */
    public function getSonTicketByPackId()
    {
        $ticketId = I('post.ticket_id', 0, 'intval');

        if (!$ticketId || empty($ticketId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $sonTicket = $this->_handleSonTicketConfig($ticketId);

        foreach ($sonTicket as $key => $val) {
            $sonTicket[$key]['disabled'] = isset($val['separate_value']) ? 1 : 0;
        }

        $this->apiReturn(self::CODE_SUCCESS, $sonTicket);
    }

    /**
     * 通过套票ID获取到的子票信息以及配置
     * <AUTHOR>
     * @date   2019-03-24 22:59:51
     *
     * @param  int  $ticketId  套票ID
     *
     * @return mixed
     */
    private function _handleSonTicketConfig($ticketId, $configType = 0)
    {
        $sonTicket = \Business\Product\HandleTicket::getSonTicket($ticketId);

        if (empty($sonTicket)) {
            return [];
        }

        $sonTidArr = array_column($sonTicket, 'tid');

        //通过套票和子票ID获取有效的分账配置
        $separateConf = $this->_separateModel->getMemberSeparateConfig($this->_sid, $ticketId, $sonTidArr, $configType,
            0);

        if (empty($separateConf)) {
            return $sonTicket;
        }

        $separate = [];
        foreach ($separateConf as $key => $val) {
            $separate[$val['tid']] = $val;
        }

        foreach ($sonTicket as $key => $val) {
            $sonTicket[$key]['id'] = $val['tid'];
            $tid                   = $val['tid'];
            if ($separate[$tid]) {
                $sonTicket[$key]['config_id']      = $separate[$tid]['config_id'];
                $sonTicket[$key]['separate_type']  = $separate[$tid]['separate_type'];
                $sonTicket[$key]['separate_value'] = $separate[$tid]['separate_value'];
                $sonTicket[$key]['object_name']    = $separate[$tid]['name'];
                $sonTicket[$key]['object_id']      = $separate[$tid]['object_id'];
                $sonTicket[$key]['channel']        = $separate[$tid]['channel'];

                if (in_array($separate[$tid]['separate_type'],
                    [SeparateConfig::__SEPARATE_TYPE_PROP__, SeparateConfig::__SEPARATE_TYPE_FIXED_PROP__])) {
                    $text = $separate[$tid]['separate_value'] . '%';
                } else {
                    $text = $separate[$tid]['separate_value'] . '元';
                }

                $sonTicket[$key]['separate_text'] = $text;
            }
        }

        return $sonTicket;
    }

    /**
     * 通过票ID查询包含该子票的套票信息（分账配置中使用）
     * Create by zhangyangzhen
     * Date: 2019/3/21
     * Time: 21:34
     *
     * @param  int ticket_id 票ID
     *
     * @return array
     */
    public function getPackTicketBySonId()
    {
        $ticketId = I('post.ticket_id', 0, 'intval');

        if (!$ticketId || empty($ticketId)) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '参数错误');
        }

        $result = $this->_getPackTicketConfigV2($ticketId);

        $this->apiReturn(self::CODE_SUCCESS, $result);
    }

    /**
     * 通过子票ID获取包含该子票的套票信息（不包含单独售卖）
     * Create by zhangyangzhen
     * Date: 2019/5/13
     * Time: 10:17
     *
     * @param  int  $ticketId  子票ID
     * @param  int  $configId  配置ID
     *
     * @return array
     */
    private function _getPackTicketConfigV2($ticketId, $configId = 0)
    {
        //包含该子票的套票
        $result = HandleTicket::getLinkPackTicket($this->_sid, $ticketId);

        if (!empty($result)) {
            $packIdArr = array_column($result, 'id');//套票ID集合

            //先通过套票ID和子票ID获取产品配置
            $separateConf = $this->_separateModel->getMemberSeparateConfig($this->_sid, $packIdArr, $ticketId, 0, 0);

            $separate = [];
            foreach ($separateConf as $key => $value) {
                $separate[$value['pack_tid']] = $value;
            }

            //最后取出已经配置过的套票ID
            foreach ($result as $key => $value) {
                $result[$key]['is_single_sale'] = 0;
                $result[$key]['separate_value'] = $separate[$value['id']]['separate_value'] ?: '';
                $result[$key]['config_id']      = $separate[$value['id']]['config_id'] ?: 0;
                $result[$key]['object_id']      = $separate[$value['id']]['object_id'] ?: 0;
                $result[$key]['object_name']    = $separate[$value['id']]['name'] ?: '';
                $result[$key]['channel']        = $separate[$value['id']]['channel'] ?: '';
                $result[$key]['aid']            = $value['apply_did'];
                unset($result[$key]['apply_did']);

                if ($configId) {
                    if ($separate[$value['id']] && $configId != $separate[$value['id']]['config_id']) {
                        $result[$key]['disabled'] = 1;
                        $result[$key]['choose']   = 0;
                    } elseif ($separate[$value['id']] && $configId == $separate[$value['id']]['config_id']) {
                        $result[$key]['disabled'] = 0;
                        $result[$key]['choose']   = 1;
                    } else {
                        $result[$key]['disabled'] = 0;
                        $result[$key]['choose']   = 0;
                    }
                } else {
                    if ($separate[$value['id']]) {
                        $result[$key]['disabled'] = 1;
                        $result[$key]['choose']   = 0;
                    } else {
                        $result[$key]['disabled'] = 0;
                        $result[$key]['choose']   = 0;
                    }
                }

                //在基础产品里面展开捆绑产品配置方式
                $sonTickets = HandleTicket::getSonTicket($value['id']);

                $sonIdArr    = array_column($sonTickets, 'tid');
                $sonSeparate = $this->_separateModel->getMemberSeparateConfig($this->_sid, $value['id'], $sonIdArr);

                $sonSeparates = [];
                foreach ($sonSeparate as $item) {
                    $sonSeparates[$item['tid']] = $item;
                }

                foreach ($sonTickets as $sonTicket) {
                    $tid = $sonTicket['tid'];

                    if ($sonSeparates[$tid]) {
                        $sonTicket['object_id']      = $sonSeparates[$tid]['object_id'];
                        $sonTicket['separate_type']  = $sonSeparates[$tid]['separate_type'];
                        $sonTicket['separate_value'] = $sonSeparates[$tid]['separate_value'];

                        if (in_array($sonSeparates[$tid]['separate_type'],
                            [SeparateConfig::__SEPARATE_TYPE_FIXED_PROP__, SeparateConfig::__SEPARATE_TYPE_PROP__])) {
                            $sonTicket['separate_text'] = $sonSeparates[$tid]['separate_value'] . '%';
                        } else {
                            $sonTicket['separate_text'] = $sonSeparates[$tid]['separate_value'] . '元';
                        }

                        $sonTicket['disabled'] = 1;

                        //在基础产品配置里面，展开捆绑产品配置，因为配置类型不同，所以需要置灰不让配置
                        if ($configId) {
                            if ($result[$key]['disabled'] == 1) {
                                $sonTicket['disabled'] = 0;
                            } else {
                                $sonTicket['disabled'] = 1;
                            }
                        }
                    } else {
                        $sonTicket['disabled'] = 0;
                    }

                    $result[$key]['sonTicketArr'][] = $sonTicket;
                }
            }
        }

        $singleConf = $this->_separateModel->getMemberSeparateConfig($this->_sid, [], $ticketId,
            SeparateConfig::__CONFIG_TYPE_BASE__, 1);
        if (!empty($singleConf)) {
            foreach ($singleConf as $key => $single) {
                if ($configId) {
                    $singleConf[$key]['disabled'] = 0;
                } else {
                    $singleConf[$key]['disabled'] = 1;
                }
            }
        }

        $returnArr = [
            'list'   => $result ?: [],
            'single' => $singleConf ?: [],
        ];

        return $returnArr;
    }

    /**
     * 通过子票ID获取包含该子票的套票信息（包含单独售卖）
     * Create by zhangyangzhen
     * Date: 2019/3/29
     * Time: 14:46
     *
     * @param  int  $ticketId  子票ID
     * @param  int  $configId  配置ID
     *
     * @return array
     */
    private function _getPackTicketConfig($ticketId, $configId = 0)
    {
        //包含该子票的套票
        $result = HandleTicket::getLinkPackTicket($this->_sid, $ticketId);

        if (!empty($result)) {
            $packIdArr = array_column($result, 'id');//套票ID集合

            //先通过套票ID和子票ID获取产品配置
            $separateConf = $this->_separateModel->getMemberSeparateConfig($this->_sid, $packIdArr, $ticketId, 0, 0);

            $separate = [];
            foreach ($separateConf as $key => $value) {
                $separate[$value['pack_tid']] = $value;
            }

            //最后取出已经配置过的套票ID
            foreach ($result as $key => $value) {
                $result[$key]['is_single_sale'] = 0;
                $result[$key]['separate_value'] = $separate[$value['id']]['separate_value'] ?: '';
                $result[$key]['config_id']      = $separate[$value['id']]['config_id'] ?: 0;
                $result[$key]['object_id']      = $separate[$value['id']]['object_id'] ?: 0;
                $result[$key]['object_name']    = $separate[$value['id']]['name'] ?: '';
                $result[$key]['channel']        = $separate[$value['id']]['channel'] ?: '';

                if ($configId) {
                    if ($separate[$value['id']] && $configId != $separate[$value['id']]['config_id']) {
                        $result[$key]['disabled'] = 1;
                        $result[$key]['choose']   = 0;
                    } elseif ($separate[$value['id']] && $configId == $separate[$value['id']]['config_id']) {
                        $result[$key]['disabled'] = 0;
                        $result[$key]['choose']   = 1;
                    } else {
                        $result[$key]['disabled'] = 0;
                        $result[$key]['choose']   = 0;
                    }
                } else {
                    if ($separate[$value['id']]) {
                        $result[$key]['disabled'] = 1;
                        $result[$key]['choose']   = 0;
                    } else {
                        $result[$key]['disabled'] = 0;
                        $result[$key]['choose']   = 0;
                    }
                }
            }
        }

        //单独售卖产品配置需要单独处理
        $singleSaleConf = $this->_separateModel->getMemberSeparateConfig($this->_sid, [], $ticketId, 0, 1, false);
        $ticketBiz      = new \Business\CommodityCenter\Ticket();
        $ticketInfo     = $ticketBiz->fieldConversion($ticketBiz->queryTicketInfoById($ticketId));
        //$ticketModel    = new TicketApi();
        //$ticketInfo     = $ticketModel->getTickets($ticketId);

        if ($configId) {
            if ($singleSaleConf && $configId != $singleSaleConf['config_id']) {
                $disabled = 1;
                $choose   = 0;
            } elseif ($singleSaleConf && $configId == $singleSaleConf['config_id']) {
                $disabled = 0;
                $choose   = 1;
            } else {
                $disabled = 0;
                $choose   = 0;
            }
        } else {
            if ($singleSaleConf) {
                $disabled = 1;
                $choose   = 0;
            } else {
                $disabled = 0;
                $choose   = 0;
            }
        }

        $result[] = [
            'separate_value' => $singleSaleConf['separate_value'] ?: '',
            'config_id'      => $singleSaleConf['config_id'] ?: 0,
            'object_id'      => $singleSaleConf['object_id'] ?: 0,
            'object_name'    => $singleSaleConf['name'] ?: '',
            'channel'        => $singleSaleConf['channel'] ?: '',
            'pid'            => $ticketInfo['product_id'] ?: 0,
            'lid'            => $ticketInfo['item_id'] ?: 0,
            'disabled'       => $disabled,
            'choose'         => $choose,
            'ltitle'         => $ticketInfo['land_name'],
            'ttitle'         => $ticketInfo['name'],
            'apply_did'      => $ticketInfo['account_id'],
            'id'             => -1,//单独售卖的传-1作为下一步保存配置的套票ID
            'num'            => 1,//单独售卖的数量就默认为1
            'is_single_sale' => 1,
        ];

        return $result;
    }

    /**
     * 获取基础产品和捆绑产品的配置
     * Create by zhangyangzhen
     * Date: 2019/5/10
     * Time: 14:37
     */
    public function getSeparateList()
    {
        $page    = I('post.page', 1, 'intval');
        $size    = I('post.size', 10, 'intval');
        $ptype   = I('post.ptype', 1, 'intval');
        $keyword = I('post.keyword', '', 'strval,trim');

        if (!in_array($ptype, [SeparateConfig::__CONFIG_TYPE_PACK__, SeparateConfig::__CONFIG_TYPE_BASE__])) {
            $this->apiReturn(self::CODE_PARAM_ERROR, [], '配置类型错误');
        }

        $lidArr = $this->_handleLandidByKeyword($keyword, $ptype);

        if ($ptype == 1) {
            $data = $this->_getPackSeparateList($page, $size, $lidArr);
        } else {
            $data = $this->_getBaseSeparateList($page, $size, $lidArr);
        }

        $this->apiReturn(self::CODE_SUCCESS, $data);
    }

    /**
     * 通过关键字获取所有的lid数组
     * Create by zhangyangzhen
     * Date: 2019/5/14
     * Time: 17:10
     *
     * @param $keyword
     * @param $type
     *
     * @return array
     */
    private function _handleLandidByKeyword($keyword, $type)
    {
        if (empty($keyword)) {
            return [];
        }

        if ($type == 1) {
            $ptype = 'F';
        } else {
            $ptype = 'A,C,G';
        }

        $productList = ProductList::getWPTSmallAppList($this->_sid, $keyword, $ptype, 0, '', -1, 1, 500);

        $lidArr = array_column($productList['lists'], 'lid');

        return $lidArr ?: [];
    }

    /**
     * 获取默认捆绑产品配置列表
     * Create by zhangyangzhen
     * Date: 2019/5/10
     * Time: 14:36
     *
     * @param $page
     * @param $size
     */
    private function _getPackSeparateList($page, $size, $lidArr = [])
    {
        $data   = $this->_separateModel->getGroupData($this->_sid, [], [], SeparateConfig::__CONFIG_TYPE_PACK__,
            'p.pack_tid', $page, $size, 1, 0, $lidArr);
        $tidArr = array_column($data['list'], 'pack_tid');
        $aidArr = array_column($data['list'], 'aid');

        $count = $data['count'];

        $ticketModel = new TicketApi();
        $tickets     = $ticketModel->getSupplyAndDistriProduct($this->_sid, $tidArr, $aidArr);

        $ticketList = [];
        foreach ($tickets as $key => $value) {
            $ticketList[$value['ticket_id']] = [
                'settlementPrice' => $value['settlementPrice'],
                'landName'        => $value['title'],
                'ticketName'      => $value['ticketTitle'],
                'ticketId'        => $value['ticket_id'],
                'ptype'           => $value['ptype'],
            ];
        }

        $list = $this->_separateModel->getMemberSeparateConfig($this->_sid, $tidArr, [],
            SeparateConfig::__CONFIG_TYPE_PACK__, false, true, 1, 0, 0, 0, $lidArr);

        foreach ($list as $key => $val) {
            $sonTicketInfo = HandleTicket::getSonTicket($val['pack_tid']);

            if (isset($sonTicketInfo[$val['tid']])) {
                $val['ltitle'] = $sonTicketInfo[$val['tid']]['ltitle'];
                $val['ttitle'] = $sonTicketInfo[$val['tid']]['ttitle'];
                $val['tnum']   = $sonTicketInfo[$val['tid']]['num'];

                if (in_array($val['separate_type'],
                    [SeparateConfig::__SEPARATE_TYPE_PROP__, SeparateConfig::__SEPARATE_TYPE_FIXED_PROP__])) {
                    $val['separate_value'] = $val['separate_value'] . "%";
                } else {
                    $val['separate_value'] = $val['separate_value'] . "元";
                }
            }

            if (isset($ticketList[$val['pack_tid']])) {
                $ticketList[$val['pack_tid']]['configId']    = $val['config_id'];
                $ticketList[$val['pack_tid']]['ticketArr'][] = $val;
            }
        }

        $returnData = [
            'list'  => $ticketList,
            'count' => $count,
        ];

        $this->apiReturn(self::CODE_SUCCESS, $returnData);
    }

    /**
     * 获取默认基础产品配置列表
     * Create by zhangyangzhen
     * Date: 2019/5/10
     * Time: 14:35
     *
     * @param  int  $page
     * @param  int  $size
     * @param  array  $lidArr  景区ID
     *
     * @return array
     */
    private function _getBaseSeparateList($page, $size, $lidArr = [])
    {
        $group  = $this->_separateModel->getGroupData($this->_sid, [], [], SeparateConfig::__CONFIG_TYPE_BASE__,
            'p.tid,p.is_single_sale', $page, $size, 1, 0, $lidArr);
        $tidArr = array_column($group['list'], 'tid');
        $aidArr = array_column($group['list'], 'aid');

        $count = $group['count'];

        $ticketModel = new TicketApi();
        $tickets     = $ticketModel->getSupplyAndDistriProduct($this->_sid, $tidArr, $aidArr);

        $ticketList = [];
        foreach ($tickets as $key => $value) {
            $ticketList[$value['ticket_id']] = [
                'settlementPrice' => $value['settlementPrice'],
                'landName'        => $value['title'],
                'ticketName'      => $value['ticketTitle'],
                'ticketId'        => $value['ticket_id'],
                'ptype'           => $value['ptype'],
            ];
        }

        $list = $this->_separateModel->getMemberSeparateConfig($this->_sid, [], $tidArr,
            SeparateConfig::__CONFIG_TYPE_BASE__, false, true, 1, 0, 0, 0, $lidArr);

        $data = [];
        if (!empty($list)) {
            foreach ($list as $key => $value) {
                if (isset($ticketList[$value['tid']])) {
                    if ($value['is_single_sale'] != 1) {
                        $packList = HandleTicket::getLinkPackTicket($this->_sid, $value['tid'], true);

                        if ($packList[$value['pack_tid']]) {
                            $value['ltitle'] = $packList[$value['pack_tid']]['ltitle'];
                            $value['ttitle'] = $packList[$value['pack_tid']]['ttitle'];
                            $value['tnum']   = $packList[$value['pack_tid']]['num'];
                        }
                    }

                    //前端分账配置展示处理
                    if (in_array($value['separate_type'],
                        [SeparateConfig::__SEPARATE_TYPE_PROP__, SeparateConfig::__SEPARATE_TYPE_FIXED_PROP__])) {
                        $value['separate_value'] = $value['separate_value'] . "%";
                    } else {
                        $value['separate_value'] = $value['separate_value'] . "元";
                    }

                    if ($data[$value['config_id']]) {
                        $data[$value['config_id']]['ticketArr'][] = $value;
                    } else {
                        $ticketList[$value['tid']]['configId'] = $value['config_id'];

                        unset($ticketList[$value['tid']]['ticketArr']);

                        $ticketList[$value['tid']]['ticketArr'][] = $value;
                        $data[$value['config_id']]                = $ticketList[$value['tid']];
                    }
                }
            }
        }

        $arr = [
            'list'  => $data,
            'count' => $count,
        ];

        return $arr;
    }
}