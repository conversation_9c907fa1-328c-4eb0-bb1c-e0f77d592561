<?php
/**
 * 报表概览
 * @Date:   2021-07
 */

namespace Controller\report;

use Library\Controller;
use Business\Statistics\StatisticsSurvey AS StatisticsSurveyBiz;
use Business\Common\CommonConfig as CommonConfigBiz;

class StatisticsSurvey extends Controller
{
    private $sid; //主账号id
    private $memberId; //当前账户id
    private $operateId; //当前账户id
    private $dtype; //当前账号类型
    private $loginInfo; //登陆信息

    private $summaryBiz;

    public function __construct()
    {
        $this->loginInfo = $this->getLoginInfo('ajax', false);
        $this->dtype     = $this->loginInfo['dtype']; //当前账号类型
        $this->operateId = $this->loginInfo['memberID']; //当前账户id
        $this->memberId  = $this->loginInfo['memberID'];
        $this->sid       = $this->loginInfo['sid']; //主账号id

        $this->summaryBiz = new StatisticsSurveyBiz();
    }

    /**
     * 报表概览汇总 按日/按月汇总
     * <AUTHOR>
     * @DateTime 2021-07-13
     */
    public function statisticsSurvey()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $type      = I("date_type", 0, 'intval'); //1 日汇总 2 月汇总
            $startDate = I("start_date", '', 'strval');// 2021-06-01 开始时间
            $endDate   = I("end_date", '', 'strval');// 2021-06-30 结束时间

            if (!$type || empty($startDate) || empty($endDate)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $data = $this->summaryBiz->summaryDayAndMonth($type, $startDate, $endDate);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表小时汇总 按小时汇总
     * <AUTHOR>
     * @DateTime 2021-07-13
     */
    public function hoursStatisticsSurvey()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date    = I("date", '', 'strval');// 2021-06-01  查询日期
            $channel = I("channel", null, 'intval'); //渠道id
            $tid     = I("tid", 0, 'intval'); //票id

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $data = $this->summaryBiz->summaryHour($date, $tid, $channel);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表小时详情 按票汇总(分页)
     * <AUTHOR>
     * @DateTime 2021-07-13
     */
    public function hoursProductStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date    = I("date", '', 'strval');// 2021-06-01 14 查询日期
            $channel = I("channel", null, 'intval'); //渠道id
            $page    = I("page", 1, 'intval');
            $size    = I("size", 10, 'intval');

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_PRODUCT_TYPE;

            $data = $this->summaryBiz->summaryHourDetails($searchType, $date, null, $channel, $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表小时详情 按渠道汇总(分页)
     * <AUTHOR>
     * @DateTime 2021-07-13
     */
    public function hoursChannelStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date = I("date", '', 'strval');// 2021-06-01 14 查询日期
            $tid  = I("tid", 0, 'intval'); //票id
            $page = I("page", 1, 'intval');
            $size = I("size", 10, 'intval');

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_CHANNEL_TYPE;

            $data = $this->summaryBiz->summaryHourDetails($searchType, $date, $tid, null, $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表产品详情 按产品汇总(分页)
     * <AUTHOR>
     * @DateTime 2021-07-13
     */
    public function productStatisticsSurvey()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date = I("date", '', 'strval');// 2021-06-01 查询日期
            $page = I("page", 1, 'intval');
            $size = I("size", 10, 'intval');

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_PRODUCT_TYPE;

            $data = $this->summaryBiz->summaryDayDetails($searchType, $date, $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表渠道详情 按渠道汇总(分页)
     * <AUTHOR>
     * @DateTime 2021-07-13
     */
    public function channelStatisticsSurvey()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date = I("date", '', 'strval');// 2021-06-01 查询日期
            $page = I("page", 1, 'intval');
            $size = I("size", 10, 'intval');

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_CHANNEL_TYPE;

            $data = $this->summaryBiz->summaryDayDetails($searchType, $date, $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表预订数据详情 按小时汇总
     * <AUTHOR>
     * @DateTime 2021-07-13
     */
    public function bookStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date = I("date", '', 'strval');// 2021-06-01 查询日期

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_BOOK_CANCEL_REVOKE_TYPE;

            $data = $this->summaryBiz->summaryHourByBookOrCancelOrRevokeOrChecked($searchType, $date, 'book');

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表取消数据详情 按小时汇总
     * <AUTHOR>
     * @date 2021/7/17
     *
     */
    public function cancelStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date = I("date", '', 'strval');// 2021-06-01 查询日期

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_BOOK_CANCEL_REVOKE_TYPE;

            $data = $this->summaryBiz->summaryHourByBookOrCancelOrRevokeOrChecked($searchType, $date, 'cancel');

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表撤销撤改数据详情 按小时汇总
     * <AUTHOR>
     * @date 2021/7/17
     *
     */
    public function revokeStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date = I("date", '', 'strval');// 2021-06-01 查询日期

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_BOOK_CANCEL_REVOKE_TYPE;

            $data = $this->summaryBiz->summaryHourByBookOrCancelOrRevokeOrChecked($searchType, $date, 'revoke');

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表验证数据详情 按小时汇总
     * <AUTHOR>
     * @date 2021/7/17
     *
     */
    public function checkedStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date = I("date", '', 'strval');// 2021-06-01 查询日期

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_CHECKED_TYPE;

            $data = $this->summaryBiz->summaryHourByBookOrCancelOrRevokeOrChecked($searchType, $date, 'checked');

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表小时详情（预定） 按票汇总(分页)
     * <AUTHOR>
     * @date 2021/7/17
     *
     */
    public function bookHoursProductStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date    = I("date", '', 'strval');// 2021-06-01 14 查询日期
            $channel = I("channel", null, 'intval'); //渠道id
            $page    = I("page", 1, 'intval');
            $size    = I("size", 10, 'intval');

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_PRODUCT_BOOK_CANCEL_REVOKE_TYPE;

            $data = $this->summaryBiz->summaryHourDetailsByBookOrCancelOrRevokeOrChecked($searchType, $date,
                $channel, null, 'book', $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表小时详情（取消） 按票汇总(分页)
     * <AUTHOR>
     * @date 2021/7/17
     *
     */
    public function cancelHoursProductStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date    = I("date", '', 'strval');// 2021-06-01 14 查询日期
            $channel = I("channel", null, 'intval'); //渠道id
            $page    = I("page", 1, 'intval');
            $size    = I("size", 10, 'intval');

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_PRODUCT_BOOK_CANCEL_REVOKE_TYPE;

            $data = $this->summaryBiz->summaryHourDetailsByBookOrCancelOrRevokeOrChecked($searchType, $date,
                $channel, null, 'cancel', $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表小时详情（撤销撤改） 按票汇总(分页)
     * <AUTHOR>
     * @date 2021/7/17
     *
     */
    public function revokeHoursProductStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date    = I("date", '', 'strval');// 2021-06-01 14 查询日期
            $channel = I("channel", null, 'intval'); //渠道id
            $page    = I("page", 1, 'intval');
            $size    = I("size", 10, 'intval');

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_PRODUCT_BOOK_CANCEL_REVOKE_TYPE;

            $data = $this->summaryBiz->summaryHourDetailsByBookOrCancelOrRevokeOrChecked($searchType, $date,
                $channel, null, 'revoke', $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表小时详情（验证） 按票汇总(分页)
     * <AUTHOR>
     * @date 2021/7/17
     *
     */
    public function checkedHoursProductStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date    = I("date", '', 'strval');// 2021-06-01 14 查询日期
            $channel = I("channel", null, 'intval'); //渠道id
            $page    = I("page", 1, 'intval');
            $size    = I("size", 10, 'intval');

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_PRODUCT_CHECKED_TYPE;

            $data = $this->summaryBiz->summaryHourDetailsByBookOrCancelOrRevokeOrChecked($searchType, $date,
                $channel, null, 'checked', $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表小时详情(预订) 按渠道汇总(分页)
     * <AUTHOR>
     * @date 2021/7/17
     *
     */
    public function bookHoursChannelStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date = I("date", '', 'strval');// 2021-06-01 14 查询日期
            $tid  = I("tid", 0, 'intval'); //票id
            $page = I("page", 1, 'intval');
            $size = I("size", 10, 'intval');

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_CHANNEL_BOOK_CANCEL_REVOKE_TYPE;

            $data = $this->summaryBiz->summaryHourDetailsByBookOrCancelOrRevokeOrChecked($searchType, $date,
                null, $tid, 'book', $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表小时详情（取消） 按渠道汇总(分页)
     * <AUTHOR>
     * @date 2021/7/17
     *
     */
    public function cancelHoursChannelStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date = I("date", '', 'strval');// 2021-06-01 14 查询日期
            $tid  = I("tid", 0, 'intval'); //票id
            $page = I("page", 1, 'intval');
            $size = I("size", 10, 'intval');

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_CHANNEL_BOOK_CANCEL_REVOKE_TYPE;

            $data = $this->summaryBiz->summaryHourDetailsByBookOrCancelOrRevokeOrChecked($searchType, $date,
                null, $tid, 'cancel', $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表小时详情（撤销撤改） 按渠道汇总(分页)
     * <AUTHOR>
     * @date 2021/7/17
     *
     */
    public function revokeHoursChannelStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date = I("date", '', 'strval');// 2021-06-01 14 查询日期
            $tid  = I("tid", 0, 'intval'); //票id
            $page = I("page", 1, 'intval');
            $size = I("size", 10, 'intval');

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_CHANNEL_BOOK_CANCEL_REVOKE_TYPE;

            $data = $this->summaryBiz->summaryHourDetailsByBookOrCancelOrRevokeOrChecked($searchType, $date,
                null, $tid, 'revoke', $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 报表小时详情（验证） 按渠道汇总(分页)
     * <AUTHOR>
     * @date 2021/7/17
     *
     */
    public function checkedHoursChannelStatisticsDetails()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $date = I("date", '', 'strval');// 2021-06-01 14 查询日期
            $tid  = I("tid", 0, 'intval'); //票id
            $page = I("page", 1, 'intval');
            $size = I("size", 10, 'intval');

            if (empty($date)) {
                throw  new \Exception('查询参数错误', 203);
            }

            //设置用户信息
            $this->_setLoginInfo();

            $searchType = StatisticsSurveyBiz::SUMMARY_HOUR_CHANNEL_CHECKED_TYPE;

            $data = $this->summaryBiz->summaryHourDetailsByBookOrCancelOrRevokeOrChecked($searchType, $date,
                null, $tid, 'checked', $page, $size);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 获取系统报表的配置（各个环境不一样）
     * <AUTHOR>
     * @date 2021/7/20
     *
     */
    public function getReportSysSearchConfigId()
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {

            //设置用户信息
            $this->_setLoginInfo();

            $data = $this->summaryBiz->getReportSysSearchConfigId();

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $this->apiReturn($code, $data, $msg);
    }

    /**
     * 获取报表概览配置开关
     * <AUTHOR>
     * @date   2023/3/31
     *
     */
    public function getSurveyConfig()
    {
        //设置用户信息
        $this->_setLoginInfo();

        $result = $this->summaryBiz->getSurveyConfig();

        $this->apiReturn(200, $result->getConfig());
    }

    /**
     * 设置报表概览配置开关
     * <AUTHOR>
     * @date   2023/3/31
     *
     */
    public function setSurveyConfig()
    {
        //设置用户信息
        $this->_setLoginInfo();

        $showSelfSonTicket = I('post.show_self_son_ticket', 0, 'intval');

        $result = $this->summaryBiz->setSurveyConfig($showSelfSonTicket);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 设置用户信息
     * <AUTHOR>
     * @date 2021/7/17
     *
     * @return bool
     */
    private function _setLoginInfo()
    {
        $this->summaryBiz->dtype     = $this->dtype;
        $this->summaryBiz->sid       = $this->sid;
        $this->summaryBiz->memberId  = $this->memberId;
        $this->summaryBiz->operateId = $this->operateId;

        return true;
    }
}
