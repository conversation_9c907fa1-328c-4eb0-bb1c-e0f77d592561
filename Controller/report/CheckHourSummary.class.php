<?php
/**
 * 检票时段报表
 *
 * <AUTHOR>
 * @date 2022-5-10
 *
 */
namespace Controller\report;

use Library\Controller;
use Business\Report\CheckHourSummary as CheckHourSummaryBiz;

class CheckHourSummary extends Controller
{

    private $_loginInfo = null;

    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo();
    }

    /**
     * 获取小时端检票数据
     * User: lanwanhui
     * Date: 2022/5/11
     */
    public function getList()
    {

        //开始时间  yyyy-mm-dd-hh
        $begin      = I('begin', '', 'strval,trim');
        //结束时间  yyyy-mm-dd-hh
        $end        = I('end', '', 'strval,trim');
        //页数
        $page       = I('page', 1, 'intval');
        //每页数量
        $pageSize   = I('page_size', 10, 'intval');
        //景区名称
        $lid        = I('lid', 0, 'intval');

        if (empty($begin) || empty($end) || !strtotime($begin) || !strtotime($begin)) {
            $this->apiReturn(204, [], '参数错误');
        }

        $checkHourSummaryBiz = new CheckHourSummaryBiz();

        $rs = $checkHourSummaryBiz->getList($page, $pageSize, $this->_loginInfo['sid'], $lid, $begin, $end);

        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);

    }
}