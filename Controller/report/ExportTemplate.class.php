<?php
/**
 * 导出业务Excel模板
 *
 * <AUTHOR>  Li
 * @date  2021-08-31
 *
 */

namespace Controller\report;

use Library\Controller;

class ExportTemplate extends Controller
{
    private $sid       = null;
    private $loginInfo = null;

    //年卡导入信息的表头
    private $_importHead = [
        [
            '实体卡号',
            '物理ID（物理卡号）',
            '*会员名称',
            '*手机号',
            '身份证号',
            '*有效期开始时间 格式请按照：(20210101)',
            '*有效期结束时间 格式请按照：(20220101)',
            '备注：实体卡号与物理ID如果有填写，则两项都为必填',
        ],
    ];

    //年卡延期信息的表头
    private $_postponeHead = [['虚拟卡号']];
    private $_addPrivilegeHead = [['虚拟卡号']];
    private $_deletePrivilegeHead = [['虚拟卡号']];

    /**
     * 初始化
     * <AUTHOR>  Li
     * @date  2021-08-31
     */
    public function __construct()
    {
        $this->sid       = $this->isLogin('ajax');
        $this->loginInfo = $this->getLoginInfo('ajax', false, false);
    }

    /**
     * 下载年卡Excel
     * <AUTHOR>  Li
     * @date  2021-08-31
     *
     */
    public function getAnnualExcel()
    {
        $type = I('post.type', 1, 'intval');  //获取excel的类型   0年卡导入  1年卡延期 2-年卡新增特权 3-年卡删除特权
        if (!in_array($type, [0, 1, 2, 3])) {
            $this->apiReturn(203, [], '导入类型有误');
        }
        switch ($type) {
            case 1:
                $fileName  = '年卡延期模板';
                $sheetName = '年卡延期';
                $head   = $this->_postponeHead;
                break;
            case 2:
                $fileName  = '年卡新增特权模板';
                $sheetName = '年卡新增特权';
                $head   = $this->_addPrivilegeHead;
                break;
            case 3:
                $fileName  = '年卡删除特权模板';
                $sheetName = '年卡删除特权';
                $head   = $this->_deletePrivilegeHead;
                break;
            default:
                $fileName  = '年卡导入模板';
                $sheetName = '年卡导入';
                $head   = $this->_importHead;
                break;
        }
        $this->excelReturn($fileName, $sheetName, $head);
    }
}