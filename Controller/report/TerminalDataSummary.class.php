<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 1/19-019
 * Time: 16:10
 */

namespace Controller\report;


use Business\Order\OrderQueryMove;
use Library\Controller;
use Library\Model;
use Model\Order\OrderHandler;
use Model\Order\SubOrderQuery;

//ini_set('display_errors', 'On');
class TerminalDataSummary extends Controller
{
    private $model = null;
    public function __construct()
    {
        $authOpenid = load_config('mobile_data_monitor');
        if (!isset($_SESSION['openid']) || !isset( $authOpenid[$_SESSION['openid']])) {
            parent::apiReturn(parent::CODE_AUTH_ERROR);
        }
        $this->model = new \Model\Report\TerminalDataSummary();
    }

    public function printSummary()
    {
        $bt = str_replace('-','',I('get.bt', date('Ymd', strtotime('-1 days'))));
        $et = str_replace('-','',I('get.et',date('Ymd')));
        $ub = strtotime($bt);
        $ue = strtotime($et);
        $diffDay = ceil(($ue-$ub) / 86400);
        if ($diffDay > 7) $this->apiReturn(parent::CODE_INVALID_REQUEST);
        $data = $this->model->printSummary($bt, $et);
        $output = [];
        $output_orders = [];
        $output_money = [];
        $nameList = [];
        //初始化数据
        for ($d=0; $d<= $diffDay; $d++) {
            $date = date('n月j号', strtotime("+ $d days", $ub));
            $nameList[] = $date;
            $output[$date] = ['name'=>$date,'type'=>'line', 'areaStyle'=>['normal'=>[]]];
            $output_orders[$date] = ['name'=>$date,'type'=>'line', 'areaStyle'=>['normal'=>[]]];
            $output_money[$date] = ['name'=>$date,'type'=>'bar', 'areaStyle'=>['normal'=>[]]];
            for ($i=7;$i<=21; $i++) {
                $output[$date]['data'][$i]         = 0;
                $output_orders[$date]['data'][$i]  = 0;
                $output_money[$date]['data'][$i]   = 0;
            }
        }
        //print_r($output);exit;
        $total_money = $total_cnt = $total_num = 0;
        if (is_array($data)) {
            foreach ($data as $item) {
                $_date = date('n月j号', strtotime($item['print_date']));
                $total_num   += $item['print_num'];
                $total_cnt   += $item['print_cnt'];
                $total_money += $item['print_money'];
                //$output[$item['print_date']]['data'][$item['print_hour']]          += $item['print_num'];
                //$output_orders[$item['print_date']]['data'][$item['print_hour']]   += $item['print_cnt'];
                //$output_money[$item['print_date']]['data'][$item['print_hour']]    += $item['print_money'];
                $output[$_date]['data'][$item['print_hour']]          += $item['print_num'];
                $output_orders[$_date]['data'][$item['print_hour']]   += $item['print_cnt'];
                $output_money[$_date]['data'][$item['print_hour']]    += $item['print_money'];
            }
        }
        foreach ($output as $key=>$item) {
            $output[$key]['data']           = array_values($item['data']);
            $output_orders[$key]['data']    = array_values($output_orders[$key]['data']);
            $output_money[$key]['data']     = array_values($output_money[$key]['data']);
        }
        //foreach ($output_money as $key=>$item) {
        //}
        parent::apiReturn(200, [
            array_values($output),
            array_values($output_orders),
            array_values($output_money),
            [number_format($total_cnt),number_format($total_num),number_format($total_money),],
            $nameList,
        ]);
    }
    public function createSummary()
    {
        $bt = str_replace('-','',I('get.bt', date('Ymd')));
        //$et = str_replace('-','',I('get.et'));
        $data = $this->model->createSummary($bt);
        $output = [
            //areaStyle: {normal: {}},
            0 =>['name'=>'票数','type'=>'line', 'areaStyle'=>['normal'=>[]],'label'=>['normal'=>['show'=>true, 'position'=>'top']]],
            1 =>['name'=>'订单数','type'=>'line', 'areaStyle'=>['normal'=>[]],'label'=>['normal'=>['show'=>true, 'position'=>'top']]],
            //2 =>['name'=>'金额','type'=>'bar'],
        ];
        $output2 = [
            //,'label'=>['normal'=>['show'=>true, 'position'=>'top']]
            0 =>['name'=>'金额','type'=>'bar','stack'=> '总量', 'areaStyle'=>'{normal: {}}'],
            //2 =>['name'=>'金额','type'=>'bar'],
        ];
        for ($i=0;$i<24; $i++) {
            $output[0]['data'][$i] = 0;
            $output[1]['data'][$i] = 0;
            $output2[0]['data'][$i] = 0;
        }
        $total_money = $total_cnt = $total_num = 0;
        if (is_array($data)) {
            foreach ($data as $item) {
                $total_num   += $item['create_num'];
                $total_cnt   += $item['create_cnt'];
                $total_money += $item['create_money'];
                $output[0]['data'][$item['create_hour']]   += $item['create_num'];
                $output[1]['data'][$item['create_hour']]   += $item['create_cnt'];
                $output2[0]['data'][$item['create_hour']]  += $item['create_money'];
            }
        }
        foreach ($output as $key=>$item) {
            $output[$key]['data'] = array_values($item['data']);
        }
        foreach ($output2 as $key=>$item) {
            $output2[$key]['data'] = array_values($item['data']);
        }
        parent::apiReturn(200, [$output,$output2,[number_format($total_cnt),number_format($total_num),number_format($total_money)]]);
    }
    public function monthSummary()
    {
        $bt = str_replace('-','',I('get.bt', date('Ymd', strtotime("-1 month"))));
        $et = str_replace('-','',I('get.et',date('Ymd')));
        $data = $this->model->monthSummary($bt, $et);
        $output = [
            0 =>['name'=>'订单','type'=>'line', 'areaStyle'=>'{normal: {}}'],
            1 =>['name'=>'门票','type'=>'line', 'areaStyle'=>'{normal: {}}'],
        ];
        $money = [0 =>['name'=>'金额','type'=>'bar','areaStyle'=>'{normal: {}}']];
        $date_list = [];
        $total_list = [];
        foreach ($data as $key=>$item) {
            $date_list[$key] = str_replace('-','/',substr($item['print_date'],5));
            $output[0]['data'][$key] = $item['print_cnt']+0;
            $output[1]['data'][$key] = $item['print_num']+0;
            $money[0]['data'][$key]  = $item['print_money']+0;

            $total_list['num']   += $item['print_num'];
            $total_list['cnt']   += $item['print_cnt'];
            $total_list['money'] += $item['print_money'];
        }
        $total_list['num']          = number_format($total_list['num']);
        $total_list['cnt']          = number_format($total_list['cnt']);
        $total_list['money']        = number_format($total_list['money']);
        parent::apiReturn(200, [
                'date'=>array_values($date_list),
                'orders'=>$output,
                'money'=>$money,
                'summary'=>$total_list,
            ]
        );
    }

    public function createMonthSummary()
    {
        $bt = str_replace('-','',I('get.bt', date('Ymd', strtotime("-1 month"))));
        $et = str_replace('-','',I('get.et',date('Ymd')));
        $data = $this->model->createMonthSummary($bt, $et);
        $output = [
            0 =>['name'=>'订单','type'=>'line', 'areaStyle'=>'{normal: {}}'],
            1 =>['name'=>'门票','type'=>'line', 'areaStyle'=>'{normal: {}}'],
        ];
        $money = [0 =>['name'=>'金额','type'=>'bar','areaStyle'=>'{normal: {}}']];
        $date_list = [];
        $total_list = [];
        foreach ($data as $key=>$item) {
            $date_list[$key] = str_replace('-','/',substr($item['create_date'],5));
            $output[0]['data'][$key] = $item['create_cnt'] / 1000 +0;
            $output[1]['data'][$key] = $item['create_num'] / 1000 +0;
            $money[0]['data'][$key]  = $item['create_money'] / 1000 +0;

            $total_list['num']   += $item['create_num'];
            $total_list['cnt']   += $item['create_cnt'];
            $total_list['money'] += $item['create_money'];
        }
        $total_list['num']          = number_format($total_list['num']);
        $total_list['cnt']          = number_format($total_list['cnt']);
        $total_list['money']        = number_format($total_list['money']);
        parent::apiReturn(200, [
                'date'=>array_values($date_list),
                'orders'=>$output,
                'money'=>$money,
                'summary'=>$total_list,
            ]
        );
    }

    public function printChannelSummary()
    {
        $bt = str_replace('-','',I('get.bt', date('Ymd')));
        $et = str_replace('-','',I('get.et'));
        $data = $this->model->printChannelSummary($bt, $et);
        $track_map = [
            0=>'黑色终端机',
            1=>'自助机',
            2=>'云票务',
            3=>'软终端',
            4=>'接口更新',
            5=>'mini终端',
            6=>'云闸机',
            20=>'未知渠道',
        ];
        $output = $output2 = $pie_chart =  [];
        foreach ($track_map as $key=>$item) {
            //'label'=>['normal'=>['show'=>true, 'position'=>'top']]
            $output[$key] = ['name'=>$item,'type'=>'line', 'areaStyle'=>['normal'=>[]],];
        }
        for ($i=7;$i<=21; $i++) {
            foreach ($output as $key=>$item) {
                $output[$key]['data'][$i] = 0;
            }
        }
        if (is_array($data)) {
            foreach ($data as $item) {
                $output[$item['print_channel']]['data'][$item['print_hour']]  += $item['print_cnt'];
                $output2[$item['print_channel']] += $item['print_cnt'];
            }
        }
        foreach ($output as $key=>$item) {
            $output[$key]['data'] = array_values($item['data']);
        }
        foreach ($output2 as $key=>$item) {
            $pie_chart[$key]['value'] = $item;
            $pie_chart[$key]['name']  = $track_map[$key];
        }
        //print_r($pie);
        parent::apiReturn(200, [array_values($output), array_values($pie_chart),'legend'=>array_values($track_map)]);
    }

    public function printTopTen()
    {
        $bt = str_replace('-','',I('get.bt', date('Ymd')));
        $hour = I('get.hour', 0,'intval');
        $data = $this->model->printTopTen($bt, $hour);

        $title_order = $title_ticket = $title_money = $order_cnt = $money_chart = $ticket_cnt = [];
        $total = $this->model->printTopTenSummary($bt);
        if (is_array($data)) {
            foreach ($data as $key=>$item) {
                $title_order[$key]           = $item['title'] . ( '(占比'.round(($item['print_cnt'] / $total['print_cnt']),4) * 100 . '%)');
                $title_ticket[$key]          = $item['title'] . ( '(占比'.round(($item['print_num'] / $total['print_num']),4) * 100 . '%)');
                $title_money[$key]           = $item['title'] . ( '(占比'.round(($item['print_money'] / $total['print_money']),4) * 100 . '%)');

                $order_cnt[$key]['name']    = $item['title'] . ( '(占比'.round(($item['print_cnt'] / $total['print_cnt']),4) * 100 . '%)');
                $money_chart[$key]['name']  = $item['title'] . ( '(占比'.round(($item['print_money'] / $total['print_money']),4) * 100 . '%)');
                $ticket_cnt[$key]['name']   = $item['title'] . ( '(占比'.round(($item['print_num'] / $total['print_num']),4) * 100 . '%)');

                $order_cnt[$key]['value']   = $item['print_cnt'];
                $money_chart[$key]['value'] = $item['print_money'];
                $ticket_cnt[$key]['value']  = $item['print_num'];
            }
        }
        parent::apiReturn(200, [
             $order_cnt,$ticket_cnt,$money_chart,
            'legend_order'  =>array_values($title_order),
            'legend_ticket' =>array_values($title_ticket),
            'legend_money'  =>array_values($title_money),
        ]);
    }

    public function forceCheck()
    {
        $ordernum = I('post.ordernum');
        $orderHandler = new OrderHandler();
        $memo = "微信端强制验证-验证人微信昵称:{$_SESSION['nickname']},openid:{$_SESSION['openid']}";
        $ret  = $orderHandler->CheckOrderSimply($ordernum, 1, null, $memo);
        if ($ret!==true) {
            parent::apiReturn(parent::CODE_NO_CONTENT,[], $ret['msg']);
        }
        parent::apiReturn(parent::CODE_SUCCESS,[], 'success');
    }

    /**
     * 修复在终端验证库不存在的未验证的订单
     * <AUTHOR> Chen
     * @date   2018-09-23
     */
    public function repairTerminalOrderData()
    {
        $ordernum = I('post.ordernum');
        //$token          = I("post.token");

        $modelMaster   = new SubOrderQuery();
        $modelTerminal = new Model('terminal');
        $tableList     = [
            'uu_ss_order'           => [
                'key'    => 'ordernum',
                'fields' => 'member, ordernum, certnum, remotenum, lid, tid, pid, aid, tnum, tprice, ordername, ordertel, contacttel, ordertime, playtime, begintime, endtime, ctime, dtime, status, pay_status, salerid, remsg, paymode, totalmoney, ordermode, onsale, personid, callback, code',
            ],
            'uu_order_addon'        => [
                'key'    => 'orderid',
                'fields' => 'orderid, ifpack, sysorder, pack_order, pack_process, ifprint, vcode, tordernum',
            ],
            'uu_order_apply_info'   => [
                'key'    => 'orderid',
                'fields' => 'orderid, apply_id, aprice, lprice, sale_price, refund_num, verified_num, origin_num, addtime',
            ],
            'uu_order_fx_details'   => [
                'key'    => 'orderid',
                'fields' => 'orderid, series, assembly, memo, origin, concat_id, pay_status, aids_money, aids, aids_price',
            ],
            'uu_order_tourist_info' => [
                'key'    => 'orderid',
                'fields' => 'orderid, tourist, idcard, voucher_type, check_state, idx, print_state, mobile, check_time, lid, chk_code, member_id, is_checked, order_time, update_time, order_month, apply_did, sync_state',
            ],
            'order_user_info'=>[
                'key'    => 'ordernum',
                'fields' => 'ordernum,ordername,ordertel,personid,voucher_type,create_time,apply_did,sync_state',
            ],
            'uu_order_tourist_info_extend' => [
                'key'    => 'orderid',
                'fields' => 'orderid,idx,end_time,valid_num,take_way,apply_did,ext_info,order_time,update_time,order_month,sync_state'
            ],
        ];
        $addTables     = [];
        // 先检查订单是否存在
        //$orderInfo = $modelMaster->getInfoByOrder($ordernum);

        //订单查询迁移1.1
        $orderMove = new OrderQueryMove();
        $orderInfo = $orderMove->getListByOrderNumNew([$ordernum]);
        $orderInfo = $orderInfo[0] ?? [];

        if (!$orderInfo) {
            parent::apiReturn(201, [], '订单不存在');
        }
        $msg = "";
        foreach ($tableList as $tbName => $items) {
            $map  = [$items['key'] => $ordernum . ''];
            $info = $modelTerminal->table($tbName)->where($map)->getField('id');
            if (!$info) {
                $data = $modelMaster->table($tbName)->where($map)->field($items['fields'])->select();
                if ($data) {
                    $lastId = $modelTerminal->table($tbName)->addAll($data);
                    if ($lastId) {
                        $msg .= "{$tbName} 添加成功<br/>";
                    }
                }

            } else {
                $msg .= "{$tbName} 存在数据<br/>";
            }
        }
        parent::apiReturn(200, $addTables, $msg);
    }
}