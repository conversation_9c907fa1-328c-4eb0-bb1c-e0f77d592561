<?php
/**
 * 时刻检验报表
 *
 * <AUTHOR>
 * @date 2022-6-13
 *
 */
namespace Controller\report;

use Library\Controller;
use Business\Report\CheckHourReport as CheckHourReportBusiness;

class CheckHourReport extends Controller
{

    private $_loginInfo = null;

    public function __construct()
    {
        $this->_loginInfo = $this->getLoginInfo();
    }

    public function getList()
    {

        //开始时间  yyyy-mm-dd
        $begin         = I('begin','','strval,trim');
        //结束时间  yyyy-mm-dd
        $end           = I('end','','strval,trim');
        //模板id
        $configId      = I('search_config_id',0,'intval');
        //页数
        $page          = I('page', 1, 'intval');
        //每页数量
        $pageSize      = I('page_size', 10, 'intval');
        //是否导出
        $excel         = I('export_excel', false, 'boolval');

       if (empty($begin) || empty($end) || $configId < 1) {
           $this->apiReturn(204, [], '参数错误');
       }

        $btm = strtotime($begin);
        $etm = strtotime($end);

        if (!$btm || !$etm) {
            $this->apiReturn(204, [], '时间格式错误');
        }

        if (date('Y', $btm) != date('Y', $etm)) {
            $this->apiReturn(204, [], '不支持跨年查询');
        }

        $checkHourReportBusiness = new CheckHourReportBusiness();

        $rs = $checkHourReportBusiness->getList(
            $this->_loginInfo['sid'],
            $this->_loginInfo['memberID'],
            $begin,
            $end,
            $configId,
            $page,
            $pageSize,
            $excel
        );
        $this->apiReturn($rs['code'], $rs['data'], $rs['msg']);
    }


}