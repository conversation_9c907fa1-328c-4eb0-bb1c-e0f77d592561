<?php

namespace Controller\report;

use Business\Member\Member;
use Business\MemberLogin\MemberLoginHelper;
use Library\Controller;
use Model\Report\Template;

class CustomSarSubPack extends Controller
{
    /** @var \Business\MemberLogin\BusinessMember */
    private $loginInfo;
    /** @var \Business\Report\CustomSarSubPack */
    private $customSarSubPackBiz;

    public function __construct()
    {
        parent::__construct();
        $this->isLogin('ajax');
        $this->loginInfo = MemberLoginHelper::getLoginBusinessMember();
        if (!MemberLoginHelper::isDistributorLogin() && !MemberLoginHelper::isSupplierLogin()) {
            $this->apiReturn(self::CODE_AUTH_ERROR, '无权限访问');
        }
        $this->customSarSubPackBiz = $this->business('Report/CustomSarSubPack');
    }

    /**
     * 获取所有报表模板配置
     */
    public function getTemplateList()
    {
        //报表类型  1预订 2验证
        $reportType = I('post.report_type', 2, 'intval');

        $res = $this->customSarSubPackBiz->getTemplateList($this->loginInfo, $reportType);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }


    /**
     * 查询共享模板的适用员工信息
     */
    public function getShareTemplateApplyStaff()
    {
        $templateId = I('template_id', 0, 'intval');
        $reportType = I('report_type', 2, 'intval');
        if (!$templateId) {
            $this->apiReturn(400, [], '参数错误');
        }
        $template = $this->customSarSubPackBiz->getTemplateById($this->loginInfo->getSId(), $templateId, $reportType);
        if (!$template) {
            $this->apiReturn(400, [], '未找到该条记录');
        }
        if ($template['is_share'] == \Model\Report\CustomSarSubPack::IS_SHARE && $this->loginInfo->isStaffLogin()) {
            $this->apiReturn(400, [], '该模板为共享模板，只允许主账号进行便捷操作');
        }
        $staffs = $this->customSarSubPackBiz->getShareTemplateStaff($template);
        $this->apiReturn(200, ['apply_to_staff' => $staffs]);
    }

    /**
     * 配置报表模板
     */
    public function setTemplate()
    {
        //报表类型 1预订 2验证
        $reportType = I('post.report_type', 2, 'intval');
        //搜索纬度
        $item = I('post.item/a', []);
        //统计指标
        $target = I('post.target/a', []);
        //报表名称
        $name = I('post.name', '', 'strval');
        //模板ID 如果为空则是新增模板
        $templateId = I('post.template_id', 0, 'intval');
        //模版某些模块的默认值
        $itemValue    = htmlspecialchars_decode(I('post.item_value', ''));
        //模版某些模块的排除
        $itemNotValue = htmlspecialchars_decode(I('post.item_not_value', ''));
        //是否是共享报表
        $isShare = I('post.is_share',2, 'intval');
        //共享模板的适用员工 用户id,多个逗号隔开 -1 适用全部
        $applyStaff = I('post.apply_to_staff','-1', 'strval');

        if (!$item) {
            $this->apiReturn(400, [], '至少选择一个统计维度');
        }

        if (!$target) {
            $this->apiReturn(400, [], '至少选择一个统计指标');
        }

        $itemValue   = json_decode($itemValue, true);
        $itemNotValue = json_decode($itemNotValue, true);

        if ($templateId) { //编辑
            $templateInfo = $this->customSarSubPackBiz->getTemplateById($this->loginInfo->getSId(), $templateId, $reportType);
            if (!$templateInfo || $templateInfo['member_id'] != $this->loginInfo->getMemberId()) {
                $this->apiReturn(400, [], '未找到该条记录');
            }
            if ($templateInfo['is_share'] == \Model\Report\CustomSarSubPack::IS_SHARE && $this->loginInfo->isStaffLogin()) {
                $this->apiReturn(400, [], '该模板为共享模板，只允许主账号进行便捷操作');
            }
            // 编辑的时候不允许修改是否共享模板
            $isShare = $templateInfo['is_share'];
        } else { //新增
            //共享模板非主账号不能操作
            if ($isShare == \Model\Report\CustomSarSubPack::IS_SHARE && $this->loginInfo->isStaffLogin()) {
                $this->apiReturn(400, [], '非主账号不能创建共享模板');
            }
        }
        $res = $this->customSarSubPackBiz->setTemplate($this->loginInfo, $templateId, $reportType, $name, $item, $target,
            $itemValue, $itemNotValue, $isShare, $applyStaff);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 删除报表模板
     */
    public function delTemplate()
    {
        //报表类型 1预订报表
        $reportType = I('post.report_type', 2, 'intval');
        //模板id
        $templateId = I('post.template_id', 0, 'intval');

        if (!$templateId) {
            $this->apiReturn(self::CODE_PARAM_ERROR, '参数错误');
        }
        $templateInfo = $this->customSarSubPackBiz->getTemplateById($this->loginInfo->getSId(), $templateId, $reportType);
        if (empty($templateInfo)) {
            $this->apiReturn(400, [], '未找到该条记录');
        }
        if ($templateInfo['is_share'] == \Model\Report\CustomSarSubPack::IS_SHARE && $this->loginInfo->isStaffLogin()) {
            $this->apiReturn(400, [], '该模板为共享模板，只允许主账号进行删除操作');
        }
        $res = $this->customSarSubPackBiz->deleteTemplate($this->loginInfo->getSId(), $templateId, $reportType);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 统计模板配置里需要的信息
     * @return void
     */
    public function getTemplateNeed()
    {
        $type = I('type', '', 'strval');
        $pageNum = I('pageNum', 1, 'intval');
        $pageSize = I('page_size', 2000, 'intval');
        $keyword   = I('key_word', '', 'strval');
        //门票范围 1=在售的门票 2=下架的门票 6=已删除的门票 99已过期 -1=不限
        $ticketScope = I('ticket_scope', '-1', 'strval');
        //商品来源 2-自供应 1-转分销
        $supplyType = I('supply_type', -1, 'intval');
        $res = $this->customSarSubPackBiz->getTemplateNeed($this->loginInfo, $type, $keyword, $ticketScope,
            $supplyType, $pageNum, $pageSize);
        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }

    /**
     * 分账验证报表查询
     * <AUTHOR>
     * @date   2023/10/18
     *
     */
    public function checkTwoList()
    {
        $dateType = I('post.date_type', 1, 'intval');
        $begin = I('post.begin', '', 'strval');
        $end   = I('post.end', '', 'strval');
        $tplId = I('post.search_config_id', 0, 'intval');
        $productScope = I('post.product_scope', '-1', 'strval');
        $page  = I('post.page', 1, 'intval');
        $size  = I('post.page_size', 10, 'intval');

        if (empty($begin) || empty($end) || empty($dateType) || !strtotime($begin) || !strtotime($end)) {
            $this->apiReturn(203, [], '日期参数错误');
        }

        if (!$tplId) {
            $this->apiReturn(203, [], '模板参数错误');
        }

        $res= $this->customSarSubPackBiz->checkTwoList($this->loginInfo->getSId(), $begin, $end, $tplId, $dateType, $productScope, $page, $size);

        $this->apiReturn($res['code'], $res['data'], $res['msg']);
    }
}