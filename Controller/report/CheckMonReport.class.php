<?php
/**
 * 检票月表
 *
 * <AUTHOR>
 * @since  2016-09-02
 */
namespace Controller\report;

use Library\Controller;
use Model\Report;
use Model\Member;
use Model\Product;
use Business\Member\Member as MemberBus;

class CheckMonReport extends Controller {

    private $_allType = [
        '0' => ['4', '6', '5', '1', '2', '0', '8'],//4：电商, 6、团购网, 5：淘宝/天猫, 1：景区, 2：旅行社, 0:酒店, 8、其他
        '1' => ['4'],//电商
        '2' => ['6'],//团购网
        '3' => ['5'],//淘宝/天猫
        '4' => ['1'],//景区
        '5' => ['2'],//旅行社
        '6' => ['0'],//酒店
        '7' => ['8'],//其他
    ];

    public function __construct() {
        $this->statisTics = new Report\Statistics();
        $this->member     = new Member\Member();
        $this->land       = new Product\Land();
    }

//    public function index() {
//
//        //企业类型
//        $comType = I('com_type');
//        if (empty($comType)) {
//            $comType = [0];
//        }
//
//        $type = [];
//        foreach ($comType as $item) {
//            switch ($item) {
//                case '1':
//                    $temType = $this->_allType[1];
//                    break;
//                case '2':
//                    $temType = $this->_allType[2];
//                    break;
//                case '3':
//                    $temType = $this->_allType[3];
//                    break;
//                case '4':
//                    $temType = $this->_allType[4];
//                    break;
//                case '5':
//                    $temType = $this->_allType[5];
//                    break;
//                case '6':
//                    $temType = $this->_allType[6];
//                    break;
//                case '7':
//                    $temType = $this->_allType[7];
//                    break;
//                default:
//                    $temType = $this->_allType[0];
//                    break;
//            }
//            $type = array_merge($temType, $type);
//        }
//        $type = array_unique($type);
//        //企业ID
//        $comId = I('select_id', 0);
//
//        //月份
//        $date = I('time');
//        if (strtotime($date.'01') == false) {
//            return ['code' => false, 'msg' => '时间格式错误'];
//        }
//
//        //获取查询日期内有在检票报表内的fid集合
//        $bTime = $date.'01';
//        $eTime = date('Ymd', strtotime("$bTime + 1 month - 1 day"));
//
//        if ($comId) {
//            $fidArr = [$comId];
//        } else {
//            $fidArr = $this->statisTics->getCheckedFidArr($bTime, $eTime);
//            $fidArr = array_column($fidArr, 'fid');
//        }
//        //筛选符合企业类型的公司fid
//        $comRes = $this->member->getMemberExtInfoGroup($fidArr, $type, 'fid, corp_kind');
//
//        if (empty($comRes)) {
//            echo json_encode(['code' => 0]);
//            die();
//        }
//
//        foreach ((array)$comRes as $val) {
//            $comTypeArr[$val['fid']] = MemberBus::__CORP_KIND_ARR__[$val['corp_kind']];
//        }
//
//        $fidArr = array_keys((array)$comTypeArr);
//
//        //搜索符合企业类型的公司的名字
//        $comNameRes = $this->member->getMemberInfoByMulti($fidArr, 'id', 'dname, id');
//
//        foreach ((array)$comNameRes as $val) {
//
//            $comData[$val['id']]['dname'] = $val['dname'];
//            $comData[$val['id']]['com_type'] = $comTypeArr[$val['id']];
//        }
//
//        //从检票表获取数据
//        $allData = $this->statisTics->getCheckedList($bTime, $eTime, 'fid', 1, 999999, (array)$fidArr);
//
//        $allDataList = isset($allData['list']) ? $allData['list'] : [];
//
//        if (!empty($allDataList)) {
//            $html = '';
//            $totalOrder = $totalNum = $totalMoney = 0;
//            $lidArr = array_column($allDataList, 'lid');
//            $titleRes = $this->land->getLandInfoByMuli($lidArr, 'id, title');
//            foreach ($titleRes as $item) {
//                $landNameRes[$item['id']] = $item['title'];
//            }
//            $i = 0;
//            foreach ($allDataList as $key => $value) {
//                $lid = $value['lid'];
//                $landName = $landNameRes[$lid];
//                $allData[$key]['pname'] = $landName;
//                $totalOrder += $value['order_num'];
//                $totalNum   += $value['ticket_num'];
//                $totalMoney += $value['sale_money'] / 100;
//                $fff = $i%2 == 0 ? '#FFFFFF' : '#F7F7F7';
//                $saleMoney = $value['sale_money'] / 100;
//                $html .= <<<HTML
//<tr style="background:{$fff}">
//    <td class="setAlign setTd">{$comData[$value['fid']]['com_type']}</td>
//    <td class="setAlign setTd">{$comData[$value['fid']]['dname']}</td>
//    <td class="setAlign setTd">{$landName}</td>
//    <td>{$value['order_num']}</td>
//    <td>{$value['ticket_num']}</td>
//    <td class="setColor_a">&yen{$saleMoney}</td>
//</tr>
//HTML;
//                $i++;
//            }
//            $return = [
//                       'code'  => 1,
//                       'total' => [
//                                   'totalOrder' => $totalOrder,
//                                   'totalMoney' => $totalMoney,
//                                   'totalNum'   => $totalNum
//                                  ],
//                       'html' => $html
//                      ];
//            echo json_encode($return);
//        }
//    }
}