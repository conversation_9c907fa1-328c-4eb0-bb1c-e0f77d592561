<?php
/**
 * User: Fang
 * Time: 18:31 2016/4/1
 * TestUrl: http://www.12301.local/route/index.php?c=OrderCallbackLog&a=getNoticeList
 * TestUrl: http://www.12301.test/route/index.php?c=OrderCallbackLog&a=getNoticeList
 * TestUrl: http://www.12301.local/route/index.php?c=OrderCallbackLog&a=getReceivers
 */

namespace Controller;

use Library\Controller;

class OrderCallbackLog extends Controller
{
    private $msgInfo = array(200 => '操作成功', 201 => '无权限查看', 202 => '查询记录为空', 203 => '未知错误',204=>'文件缺失');

    public function __construct()
    {
        $this->apiReturn(201);
    }
}