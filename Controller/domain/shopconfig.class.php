<?php
/**
 * Created by PhpStorm.
 * User: leafzl
 * Date: 2018/3/7
 * Time: 13:55
 */
namespace Controller\domain;

use Model\Subdomain\SubdomainInfo;
use Business\Domain\Shop;
use Library\Controller;


class shopconfig extends Controller
{
    private $_sid;  //用户id
    private $loginInfo;

    public function __construct()
    {
        //用户id
        $this->_sid = $this->isLogin('ajax');

        $this->loginInfo = $this->getLoginInfo('ajax', false, false);
    }

    /**
     * 获取二级店铺配置
     * @return array 商店配置信息
     */
    public function getConfig()
    {
        $shop = new Shop();
        $result = $shop->getShopConfig($this->_sid);
        if ($result['code'] == 200) {
            $this->apiReturn($result['code'], $result['data'], 'success');
        }

    }

    /**
     * 保存商店配置信息
     * author  leafzl
     * Date: 2018/3/7
     */

    public function saveConfig()
    {
        $this->checkAvalid();
        $config = [
            'fid' => $this->_sid,
            'M_domain' => I('custom_domain') ?: $this->loginInfo['account'],
            'M_name' => I('site_name'),
            'M_logo1' => I('site_logo'),
            'M_qq' => I('qq'),
            'M_tel' => I('telephone'),
            'M_addr' => I('address'),
            'M_copyright' => I('copyright'),
            'M_navi' => json_encode(I('navi'), JSON_UNESCAPED_UNICODE),
            'M_about_us' => I('about_us'),
            'M_tpl' => I('tpl') ?: 'default',
            'M_slider' => json_encode(I('slider')),
            'M_theme' => json_encode(I('theme')),
            'M_hotel_special' => I('hotel_special') ?: 0,
            'M_recommend' => I('recommend'),
            'M_show_pft' => I('show_pft'),
            'M_account_domain' => $this->loginInfo['account'],
            'M_background' => I('background') ?: ''
        ];

        $shop = new Shop();
        $result = $shop->saveShopConfig($this->_sid, $config);

        if ($result['code']==200) {
            $this->apiReturn(1, '', 'success');
        } else {
            $this->apiReturn(0, '', '保存失败，请重试');
        }
    }

    /**
     * 验证接受到的数据
     * author  leafzl
     * Date: 2018/3/8
     */

    private function checkAvalid()
    {
        if (!I('site_name') || !I('site_logo')) {
            $this->apiReturn(0, '', '网站名和网站logo不能为空');
        }

        if (!filter_var(I('site_logo'), FILTER_VALIDATE_URL)) {
            $this->apiReturn(0, '', 'logo上传不符合规则');
        }

    }
}