<?php
/** 独立域名登陆 配置
 *
 * <AUTHOR>
 * @date 2017-04-28
 *
 */

namespace Controller\domain;

use Library\Controller;
use Library\Tools;
use Model\domain\platform as PlatModel;

class platform extends Controller
{
    private $sid       = null;
    private $account   = null;
    private $loginInfo = null;
    private $hostInfo  = null;

    public function __construct()
    {
        $tmpAction = $_GET['a'] ?? '';
        $tmpAction = strtolower($tmpAction);

        if($tmpAction == 'domaininfo') {
            //这个方法是独立域名首页调用的，没有登录

        } else {
            $this->isLogin('ajax');
            $this->loginInfo = $this->getLoginInfo('ajax', false, false);

            $this->sid     = $this->loginInfo['sid'];
            $this->account = $this->loginInfo['account'];
        }

        $this->hostInfo = explode('.', $_SERVER['HTTP_HOST']);
    }

    /**
     * 获取B端配置页信息
     * <AUTHOR>
     *
     * @return
     */
    public function getDomainInfo()
    {
        if (!$this->sid) {
            $this->apiReturn(102, '', '登陆超时！');
        }

        $configure = include __DIR__ . '/../../Conf/domain.conf.php';
        $oldRecord = $this->_getShopConfig($this->sid);
        $record    = $this->_getNewdDomainInfo($this->sid);

        if (!$record) {
            $info = $oldRecord;
        } else {
            $info = $record;
        }

        $info = $info ?: [];

        if (in_array($this->account, $configure)) {
            $info['power'] = 1;
        } else {
            $info['power'] = 0;
        }

        $this->apiReturn(200, $info, '成功');
    }

    /**
     *  B端二级店铺 登陆 获取 配置信息
     * <AUTHOR>
     *
     * @return
     */
    public function domainInfo()
    {
        //登陆获取
        $platform       = new PlatModel();
        $subDomainModel = new \Model\Subdomain\SubdomainInfo();

        $hostInfo = $this->hostInfo;
        $hostPre  = $hostInfo[0];

        if ($hostInfo[1] != '12301') { //非12301的二级域名访问

            $domainSetting = (new \Model\AdminConfig\SystemSetting())->getAllValue();
            foreach ($domainSetting as $tmpSetting) {
                $tmpData = json_decode($tmpSetting['value'], true);

                if ($_SERVER['HTTP_HOST'] == trim($tmpData[0])) {
                    $urlArr  = parse_url($tmpData[1]);
                    $tmpHost = explode('.', $urlArr['host']);
                    $hostPre = $tmpHost[0];
                    continue;
                }
            }
        }

        $domainInfo    = $subDomainModel->getBindedSubdomainInfo($hostPre, $identify = 'account');
        $newDomainInfo = $platform->getDetails($hostPre, 'account');

        $oldRecord = $this->_getShopConfig($domainInfo['fid']);
        $record    = $this->_getNewdDomainInfo($newDomainInfo['fid']);
        if (!$record && $oldRecord['site_name'] === NULL) {
            $this->apiReturn(100, [], '您访问的地址无效');
        }
        if (!$record) {
            if ($domainInfo) {
                $info = $oldRecord;
            } else {
                exit('404');
            }
        } else {
            $info = $record;
        }

        $info = $info ?: [];

        $this->apiReturn(200, $info, '成功');
    }

    /**
     * 进入平台 获取相对应的 二级域名信息 (common/h_config)
     * <AUTHOR>
     *
     * @return
     */
    public function Hconfig()
    {
        $platform       = new PlatModel();
        $subDomainModel = new \Model\Subdomain\SubdomainInfo();

        $hostInfo = $this->hostInfo;
        //获取旧版二级域名配置信息
        $domainInfo = $subDomainModel->getBindedSubdomainInfo($hostInfo[0], $identify = 'account');
        //获取新版二级域名配置信息
        $newDomainInfo = $platform->getDetails($hostInfo[0], 'account');
        $record        = $this->_getNewdDomainInfo($newDomainInfo['fid']);

        if ($domainInfo && !$newDomainInfo) {
            $fid    = $domainInfo['fid'];
            $record = $this->_getNewdDomainInfo($fid);
            if ($record) {
                exit('404');
            }
        }
        if (!$domainInfo && !$newDomainInfo) {
            exit('404');
        }
        if (!$record) {
            if ($domainInfo) {
                $info = false;
            } else {
                exit('404');
            }

        } else {
            $info = $record;
        }

        return $info;
    }

    /**
     * 获取店铺信息
     * <AUTHOR>
     *
     * @param  int  $memberId  会员id
     *
     * @return
     */
    private function _getShopConfig($memberId)
    {
        $platform       = new PlatModel();
        $subDomainModel = new \Model\Subdomain\SubdomainInfo();

        //查询微信二维码
        $wxUrl = $platform->wxOpenInfo($memberId);
        //获取已绑定的二级域名信息
        $config = $subDomainModel->getBindedSubdomainInfo($memberId, 'id');

        if ($config['M_qq'] == 0) {
            $config['M_qq'] = "";
        }

        $bannerOne   = 'http://www.12301.cc/images/img/benner1.jpg';
        $bannerTwo   = 'http://www.12301.cc/images/img/benner2.jpg';
        $bannerThere = 'http://www.12301.cc/images/img/benner3.jpg';

        $banner = [
            ['imgpath' => $bannerOne, 'url' => '', 'Img' => $bannerOne, 'Inp' => '', 'num' => ''],
            ['imgpath' => $bannerTwo, 'url' => '', 'Img' => $bannerTwo, 'Inp' => '', 'num' => ''],
            ['imgpath' => $bannerThere, 'url' => '', 'Img' => $bannerThere, 'Inp' => '', 'num' => ''],
        ];

        return [
            'fid'        => $config['fid'],
            'site_name'  => $config['M_name'],
            'logo'       => $config['M_logo1'] ? $config['M_logo1'] : "http://images.12301.test/images/index_logo.png",
            'banner'     => $banner,
            'tel'        => $config['M_tel'],
            'address'    => $config['M_addr'],
            'copyright'  => $config['M_copyright'],
            'qq'         => $config['M_qq'],
            'host'       => $config['M_account_domain'] ? $config['M_account_domain'] : $this->account,
            'domain'     => $config['M_domain'],
            'groupInfo'  => json_decode($config['M_slider'], true),
            'setgroup'   => '1',
            'weixinLogo' => $wxUrl,
        ];
    }

    /**
     * 获取用户 新版 域名配置信息
     *
     * @param  int  $memberid  用户ID
     *
     * @return array | bool
     */
    private function _getNewdDomainInfo($memberId)
    {
        $platform = new PlatModel();
        $wxUrl    = $platform->wxOpenInfo($memberId);
        $config   = $platform->getDetails($memberId);
        $fid      = $config['fid'];

        if (!$fid) {
            return false;
        }

        if ($config['p_qq'] == 0) {
            $config['p_qq'] = "";
        }

        $groupInfo = json_decode($config['p_groupInfo'], true);
        foreach ($groupInfo as &$tmp) {
            $tmp['Img']     = isset($tmp['Img']) ? $tmp['Img'] : $tmp['imgpath'];
            $tmp['Inp']     = isset($tmp['Inp']) ? $tmp['Inp'] : '';
            $tmp['DesInp']  = isset($tmp['DesInp']) ? $tmp['DesInp'] : '';
        }

        $banner = json_decode($config['p_banner'], true);
        if (empty($banner)) {
            $banner = [
                ['imgpath' => 'http://www.12301.cc/images/img/benner1.jpg'],
                ['imgpath' => 'http://www.12301.cc/images/img/benner2.jpg'],
                ['imgpath' => 'http://www.12301.cc/images/img/benner3.jpg'],
            ];
        }

        return [
            'fid'        => $config['fid'],
            'site_name'  => $config['p_name'],
            'logo'       => $config['p_logo'] ? $config['p_logo'] : "http://images.12301.test/images/index_logo.png",
            'banner'     => $banner,
            'tel'        => $config['p_tel'],
            'address'    => $config['p_addr'],
            'copyright'  => $config['p_copyright'],
            'qq'         => $config['p_qq'],
            'host'       => $config['p_host'] ? $config['p_host'] : $this->account,
            'domain'     => $config['p_domain'],
            'groupInfo'  => $groupInfo,
            'setgroup'   => $config['p_setgroup'],
            'weixinLogo' => $wxUrl,
        ];
    }

    public function save()
    {
        if (!$this->sid) {
            $this->apiReturn(102, '', '登陆超时！');
        }

        $cleanRequest = Tools::deepRemoveXss($_REQUEST);
        if ($cleanRequest !== $_REQUEST) {
            $this->apiReturn(1001, '', '含有非法字符，请检查！');
        }

        $domain    = I('post.Cusdomain', '', 'strval');
        $siteName  = I('post.Sitename', '', 'strval');
        $qq        = I('post.Cusqq', '', 'intval');
        $tel       = I('post.Custel', '', 'strval');
        $Address   = I('post.Address', '', 'strval');
        $Copyright = I('post.Copyright', '', 'strval');
        $logo      = I('post.logo', '', 'strval');
        $banner    = I('post.banner', '', 'strval');
        $deDomain  = I('post.Dedomain', '', 'strval');
        $groupInfo = I('post.groupInfo', '', 'strval');

        if (!$siteName) {
            $this->apiReturn(104, '', '网站名称含有非法字符或不能为空！');
        }
        $config = [
            'fid'              => $this->sid,
            'p_domain'         => $domain ? $domain : $this->account,
            'p_name'           => $siteName,
            'p_logo'           => $logo,
            'p_qq'             => $qq,
            'p_banner'         => json_encode($banner),
            'p_tel'            => $tel,
            'p_host'           => $deDomain,
            'p_addr'           => $Address,
            'p_about'          => '关于我们',
            'p_setgroup'       => '2',
            'p_groupInfo'      => json_encode($groupInfo),
            'p_copyright'      => $Copyright,
            'p_account_domain' => $this->account,
            'createtime'       => date('Y-m-d H:i:s'),
        ];

        $platModel    = new PlatModel();
        $exist        = $platModel->getDetails($this->sid);
        $checkNewInfo = $platModel->checknewdAgain($domain);
        $fid          = $checkNewInfo['fid'];
        $result       = false;
        if ($exist) {
            $config['id'] = $exist['id'];
            if ($checkNewInfo) {
                if ($this->sid != $fid) {
                    $this->apiReturn(1001, '', '自定义域名重复，请更换！');
                } else {
                    $result = $platModel->UpdataSubDomain($config);
                }
            } else {
                $result = $platModel->UpdataSubDomain($config);
            }
        } else {
            if ($checkNewInfo) {
                $this->apiReturn(1001, '', '自定义域名重复，请更换！');
            } else {
                $result = $platModel->SaveSubDomain($config);
            }
        }
        if ($result !== false) {
            $this->apiReturn(200, '', '保存成功！');
        } else {
            $this->apiReturn(1005, '', '保存失败，请重试一下');
        }

    }
}
