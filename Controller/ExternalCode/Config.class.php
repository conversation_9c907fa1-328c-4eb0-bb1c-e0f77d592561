<?php
/**
 * 外部码
 */

namespace Controller\ExternalCode;

use Library\Controller;
use Business\ExternalCode\Config as ConfigApi;

class Config extends Controller
{
    private $loginInfo;
    private $_ConfigApi;

    public function __construct()
    {
        $this->loginInfo  = $this->getLoginInfo();
        $this->_ConfigApi = new ConfigApi();
    }

    /**
     * 外部码配置提交接口
     *
     * <AUTHOR>
     * @date 2020-07-20
     *
     */
    public function submit()
    {
        $sid = $this->loginInfo['sid']; //获取上一级供应商id
        $mid = $this->loginInfo['memberID']; //当前用户id/操作人员id
        //请求参数
        $title     = I('post.title', '', 'strval');  //外部码关联票配置名称
        $land_id   = I('post.land_id', '0', 'intval'); //景区id
        $ticket_id = I('post.ticket_id', '0', 'intval'); //门票id
        $state     = I('post.state', '0', 'intval'); //状态
        $send_type = I('post.send_type', '2', 'intval'); //发码方式
        $remark    = I('post.remark', '', 'strval'); //备注

        if (!$title || !$land_id || !$ticket_id) {
            $this->apiReturn(203, [], '参数出错');
        }
        if (!in_array($state, [1, 0])) {
            $this->apiReturn(203, [], '状态设置出错');
        }
        if (!in_array($send_type, [1, 2])) {
            $this->apiReturn(203, [], '发码方式设置出错');
        }
        if (mb_strlen($remark, 'UTF-8') > 100) {
            $this->apiReturn(203, [], '备注不能超过100个字');
        }

        $result = $this->_ConfigApi->addConfigBySubmit($sid, $mid, $title, $land_id, $ticket_id, $state, $send_type,
            $remark);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], $result['msg']);
    }

    /**
     * 外部码配置 禁用或者启用
     *
     * <AUTHOR>
     * @date 2020-07-20
     *
     */
    public function disable()
    {
        $sid = $this->loginInfo['sid']; //获取上一级供应商id
        $mid = $this->loginInfo['memberID']; //当前用户id/操作人员id
        //请求参数
        $ext_id = I('post.ext_id', '0', 'intval');  //外部码关联票配置id
        $state  = I('post.state', '0', 'intval'); //状态：1启用；0禁用

        if (!$ext_id) {
            $this->apiReturn(203, [], '参数出错');
        }
        if (!in_array($state, [1, 0])) {
            $this->apiReturn(203, [], '状态异常');
        }

        $result = $this->_ConfigApi->updateConfigByExtId($sid, $mid, $ext_id, $state);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], $result['msg']);
    }

    /**
     * 外部码配置 编辑提交接口
     *
     * <AUTHOR>
     * @date 2020-07-20
     *
     */
    public function compile()
    {
        $sid = $this->loginInfo['sid']; //获取上一级供应商id
        $mid = $this->loginInfo['memberID']; //当前用户id/操作人员id
        //请求参数
        $ext_id    = I('post.ext_id', '0', 'intval');  //外部码关联票配置id
        $title     = I('post.title', '', 'strval'); //名称
        $state     = I('post.state', '0', 'intval'); //状态：1启用；0禁用
        $send_type = I('post.send_type', '2', 'intval'); //发码方式：1一单一码；2一单多码
        $remark    = I('post.remark', '', 'strval'); //备注

        if (!$title) {
            $this->apiReturn(203, [], '参数出错');
        }
        if (!in_array($state, [1, 0])) {
            $this->apiReturn(203, [], '状态设置出错');
        }
        if (!in_array($send_type, [1, 2])) {
            $this->apiReturn(203, [], '发码方式设置出错');
        }
        if (mb_strlen($remark, 'UTF-8') > 100) {
            $this->apiReturn(203, [], '备注不能超过100个字');
        }

        $result = $this->_ConfigApi->editConfigByExtId($sid, $mid, $ext_id, $title, $state, $send_type, $remark);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], $result['msg']);
    }

}