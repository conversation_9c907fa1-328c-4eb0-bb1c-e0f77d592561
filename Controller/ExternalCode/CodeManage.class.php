<?php
/**
 * 外部码发码管理
 * <AUTHOR> Li
 * @date  2020-07-21
 */

namespace Controller\ExternalCode;

use Business\ExternalCode\CodeManage as manageBiz;
use Library\Controller;

class CodeManage extends Controller
{
    private $loginInfo;
    private $_namageBiz;

    public function __construct()
    {
        $this->loginInfo  = $this->getLoginInfo();
        $this->_namageBiz = new manageBiz();
    }

    //===============发码管理=========================

    /**
     * 获取发码管理列表
     *
     * <AUTHOR>
     * @date 2020-07-20
     *
     */
    public function getManagelist()
    {
        $sid       = $this->loginInfo['sid']; //获取上一级供应商id
        $ext_title = I('post.ext_title', '', 'strval'); //搜索关联配置名称
        $landId    = I('post.land_id', 0, 'intval'); //景区id
        $ticketId  = I('post.ticket_id', 0, 'intval'); //门票id
        $searchSId = I('post.search_sid', 0, 'intval'); //搜索的供应商id
        $sendType  = I('post.send_type', 2, 'intval'); //发码方式 1一单一码；2一单多码
        $state     = I('post.state'); //状态 0停用;1启用
        $page      = I('post.page', 1, 'int');
        $size      = I('post.size', 10, 'int');
        $recycle   = I('post.recycle', 0, 'intval'); //回收站标识 0否;1是
        if ($recycle) {
            //设置回收站查询
            $this->_namageBiz->recycle = true;
        }

        $status = 999; //随机取一个较大的数
        if (($state == 0 || $state == 1) && is_numeric($state)) {
            $status = $state;
        }

        $result = $this->_namageBiz->queryExtListByMemberId($sid, $searchSId, $sendType, $ext_title, $landId, $ticketId, (int)$status,
            $page, $size);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }

    /**
     * 外部码配置提交接口
     *
     * <AUTHOR>
     * @date 2020-07-20
     *
     */
    public function saveConfigInfo()
    {
        $sid = $this->loginInfo['sid']; //获取上一级供应商id
        $mid = $this->loginInfo['memberID']; //当前用户id/操作人员id
        //请求参数
        $title        = I('post.title', '', 'strval');  //外部码关联票配置名称
        $landId       = I('post.land_id', 0, 'intval'); //景区id
        $ticketId     = I('post.ticket_id', 0, 'intval'); //门票id
        $state        = I('post.state', 0, 'intval'); //状态
        $sendType     = I('post.send_type', 2, 'intval'); //发码方式
        $expireType   = I('post.expire_type', 1, 'intval'); //发送给游客的有效期：1导码设置的有效期；2下单选择的游玩日期；3固定有效期
        $startDt      = I('post.start_dt', '', 'strval'); //当expire_type=3时必传属性，例如2020/09/01
        $endDt        = I('post.end_dt', '', 'strval'); //当expire_type=3时必传属性，例如2020/09/31
        $showCode     = I('post.show_code', 0, 'intval'); //短信是否显示外部码: 1展示；2不展示
        $showDetails  = I('post.show_details', 0, 'intval'); //短信是否显示订单详情: 1展示；2不展示
        $remark       = I('post.remark', '', 'strval'); //备注

        if ($sid == 1) {
            $this->apiReturn(203, [], '管理员没有该权限，请联系供应商操作');
        }

        if (!$title || !$landId || !$sendType) {
            $this->apiReturn(203, [], '参数出错');
        }

        if (utf8Length($title) > 10) {
            $this->apiReturn(203, [], '名称不能超过10个字符');
        }

        if (!in_array($state, [1, 0])) {
            $this->apiReturn(203, [], '状态设置出错');
        }

        if (!in_array($sendType, [1, 2])) {
            $this->apiReturn(203, [], '发码方式设置出错');
        }

        //有效期验证
        if (!in_array($expireType, [1, 2, 3])) {
            $this->apiReturn(203, [], '有效期设置出错');
        }

        //有效期管理验证必填
        if ($expireType == 3 && (empty($startDt) || empty($endDt))) {
            $this->apiReturn(203, [], '设置固定有效期，日期选择出错');
        }

        //验证短信是否显示外部码
        if (!in_array($showCode, [1, 2])) {
            $this->apiReturn(203, [], '短信是否展示外部码设置出错');
        }

        //验证短信是否显示订单详情
        if (!in_array($showDetails, [1, 2])) {
            $this->apiReturn(203, [], '短信是否展示订单详情设置出错');
        }

        if (utf8Length($remark) > 100) {
            $this->apiReturn(203, [], '备注不能超过100个字');
        }

        $result = $this->_namageBiz->addConfigBySubmit($sid, $mid, $title, $landId, $ticketId, $state, $sendType,
            $expireType, $startDt, $endDt, $showCode, $showDetails, $remark);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 外部码配置 禁用或者启用
     *
     * <AUTHOR>
     * @date 2020-07-20
     *
     */
    public function editConfigStatus()
    {
        $sid = $this->loginInfo['sid']; //获取上一级供应商id
        $mid = $this->loginInfo['memberID']; //当前用户id/操作人员id
        //请求参数
        $extId = I('post.ext_id', '0', 'intval');  //外部码关联票配置id
        $state = I('post.state', '0', 'intval'); //状态：1启用；0禁用
        $tid   = I('post.ticket_id', 0, 'intval'); //门票id

        if ($sid == 1) {
            $this->apiReturn(203, [], '管理员没有该权限，请联系供应商操作');
        }

        if (!$extId) {
            $this->apiReturn(203, [], '参数出错！！');
        }
        if (!in_array($state, [1, 0])) {
            $this->apiReturn(203, [], '状态异常');
        }

        if (!$tid) {
            $this->apiReturn(203, [], '门票信息缺失');
        }

        $result = $this->_namageBiz->updateConfigByExtId($sid, $mid, $extId, $state, $tid);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 批量禁启用
     * <AUTHOR>
     * @date 2021/6/25
     *
     */
    public function batchEditConfigStatus()
    {
        $sid = $this->loginInfo['sid']; //获取上一级供应商id
        $mid = $this->loginInfo['memberID']; //当前用户id/操作人员id
        //请求参数
        $extId = I('post.ext_ids', '0', 'strval');  //外部码关联票配置id 多个逗号隔开
        $state = I('post.state', '0', 'strval'); //状态：1启用；0禁用
        $tid   = I('post.ticket_ids', 0, 'strval'); //门票id 多个逗号隔开

        $extIds = explode(',', $extId);
        $tids = explode(',', $tid);

        if (empty($extIds) || empty($tids) || (count($extIds) != count($tids))) {
            $this->apiReturn(203, [], '操作参数缺失');
        }

        $result = $this->_namageBiz->updateConfigByExtIds($sid, $mid, $extIds, $state, $tids);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 删除外部码配置
     * 这边只是挪到回收站
     * <AUTHOR>
     * @date 2021/6/26
     *
     */
    public function delConfig()
    {
        $sid = $this->loginInfo['sid']; //获取上一级供应商id
        $mid = $this->loginInfo['memberID']; //当前用户id/操作人员id
        //请求参数
        $extId = I('post.ext_ids', '0', 'strval');  //外部码关联票配置id 多个逗号隔开

        //转化数组
        $extIds = explode(',', $extId);

        if (empty($extIds)) {
            $this->apiReturn(203, [], '操作参数缺失');
        }

        $result = $this->_namageBiz->delConfigByExtIds($sid, $mid, $extIds);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 应用失效时，关闭最后状态，商户端产品列表不在提示
     * <AUTHOR>
     * @date 2021/7/9
     *
     */
    public function closeLast()
    {
        $sid = $this->loginInfo['sid']; //获取上一级供应商id
        $mid = $this->loginInfo['memberID']; //当前用户id/操作人员id
        //请求参数
        $tid = I('post.tid', '0', 'strval');  //票id

        if (empty($tid)) {
            $this->apiReturn(203, [], '操作参数缺失');
        }

        $result = $this->_namageBiz->closeLastStateByTid($sid, $mid, $tid);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 回收站-恢复
     * <AUTHOR>
     * @date 2021/6/26
     *
     */
    public function discardRecovery()
    {
        $sid = $this->loginInfo['sid']; //获取上一级供应商id
        $mid = $this->loginInfo['memberID']; //当前用户id/操作人员id
        //请求参数
        $extId = I('post.ext_ids', '0', 'strval');  //外部码关联票配置id 多个逗号隔开
        //转化数组
        $extIds = explode(',', $extId);

        if (empty($extIds)) {
            $this->apiReturn(203, [], '操作参数缺失');
        }

        $result = $this->_namageBiz->discardRecoveryConfigByExtIds($sid, $mid, $extIds);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 回收站-永久删除
     * <AUTHOR>
     * @date 2021/6/28
     *
     */
    public function delForever()
    {
        $sid = $this->loginInfo['sid']; //获取上一级供应商id
        $mid = $this->loginInfo['memberID']; //当前用户id/操作人员id
        //请求参数
        $extId = I('post.ext_ids', '0', 'strval');  //外部码关联票配置id 多个逗号隔开
        //转化数组
        $extIds = explode(',', $extId);

        if (empty($extIds)) {
            $this->apiReturn(203, [], '操作参数缺失');
        }

        $result = $this->_namageBiz->delForeverConfigByExtIds($sid, $mid, $extIds);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 外部码配置 编辑提交接口
     *
     * <AUTHOR>
     * @date 2020-07-20
     *
     */
    public function editConfigInfo()
    {
        $sid = $this->loginInfo['sid']; //获取上一级供应商id
        $mid = $this->loginInfo['memberID']; //当前用户id/操作人员id
        //请求参数
        $extId        = I('post.ext_id', 0, 'intval');  //外部码关联票配置id
        $title        = I('post.title', '', 'strval'); //名称
        $state        = I('post.state', 0, 'intval'); //状态：1启用；0禁用
        $sendType     = I('post.send_type', 2, 'intval'); //发码方式：1一单一码；2一单多码
        $remark       = I('post.remark', '', 'strval'); //备注
        $tid          = I('post.ticket_id', 0, 'intval'); //门票id
        $expireType   = I('post.expire_type', 1, 'intval'); //发送给游客的有效期：1导码设置的有效期；2下单选择的游玩日期；3固定有效期
        $startDt      = I('post.start_dt', '', 'strval'); //当expire_type=3时必传属性，例如2020/09/01
        $endDt        = I('post.end_dt', '', 'strval'); //当expire_type=3时必传属性，例如2020/09/31
        $showCode     = I('post.show_code', 0, 'intval'); //短信是否显示外部码: 1展示；2不展示
        $showDetails  = I('post.show_details', 0, 'intval'); //短信是否显示订单详情: 1展示；2不展示

        if ($sid == 1) {
            $this->apiReturn(203, [], '管理员没有该权限，请联系供应商操作');
        }

        if (!$extId) {
            $this->apiReturn(203, [], '缺少配置信息');
        }

        if (!$tid) {
            $this->apiReturn(203, [], '门票信息缺失');
        }

        if (!$title) {
            $this->apiReturn(203, [], '参数出错');
        }

        if (utf8Length($title) > 10) {
            $this->apiReturn(203, [], '名称不能超过10个字符');
        }

        if (!in_array($state, [1, 0])) {
            $this->apiReturn(203, [], '状态设置出错');
        }

        if (!in_array($sendType, [1, 2])) {
            $this->apiReturn(203, [], '发码方式设置出错');
        }

        //有效期验证
        if (!in_array($expireType, [1, 2, 3])) {
            $this->apiReturn(203, [], '有效期设置出错');
        }

        //有效期管理验证必填
        if ($expireType == 3 && (empty($startDt) || empty($endDt))) {
            $this->apiReturn(203, [], '设置固定有效期，日期选择出错');
        }

        //验证短信是否显示外部码
        if (!in_array($showCode, [1, 2])) {
            $this->apiReturn(203, [], '短信是否展示外部码设置出错');
        }

        //验证短信是否显示订单详情
        if (!in_array($showDetails, [1, 2])) {
            $this->apiReturn(203, [], '短信是否展示订单详情设置出错');
        }

        if (utf8Length($remark) > 100) {
            $this->apiReturn(203, [], '备注不能超过100个字');
        }

        $result = $this->_namageBiz->editConfigByExtId($sid, $mid, $extId, $title, $state, $sendType, $tid, $expireType, $startDt, $endDt, $showCode, $showDetails, $remark);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 获取外部码配置
     * <AUTHOR>
     * @date 2020-07-20
     */
    public function getConfigInfo()
    {
        $sid = $this->loginInfo['sid']; //获取上一级供应商id
        $mid = $this->loginInfo['memberID']; //当前用户id/操作人员id
        //请求参数
        $confId = I('post.conf_id', 0, 'intval');  //外部码关联票配置id

        if (!$confId) {
            $this->apiReturn(203, [], '缺少配置信息');
        }

        $result = $this->_namageBiz->getConfigInfoByExtId($sid, $mid, $confId);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], $result['msg']);
    }

    //===============批次管理=========================

    /**
     * 获取批次列表
     * <AUTHOR> Li
     * @date  2020-07-22
     */
    public function getCodeBatchList()
    {
        $sid        = $this->loginInfo['sid'];
        $mid        = $this->loginInfo['memberID'];
        $identify   = I('post.identify', '', 'strval');
        $searchType = I('post.search_type', 0, 'intval'); //搜索类型 1订单号 2三方码
        $confId     = I('post.conf_id');
        $page       = I('post.page', 1, 'intval');
        $size       = I('post.size', 10, 'intval');
        $recycle    = I('post.recycle', 0, 'intval'); //回收站标识 0否;1是
        if ($recycle) {
            //设置回收站查询
            $this->_namageBiz->recycle = true;
        }

        if (!$confId) {
            $this->apiReturn(203, [], '缺少配置信息');
        }

        $orderNum = '';
        $code     = '';
        if ($identify) {
            if ($searchType == 1) {
                $orderNum = $identify;
            } elseif ($searchType == 2) {
                $code = $identify;
            }
        }

        $result = $this->_namageBiz->getCodeBatchList($sid, $mid, $confId, $orderNum, $code, $page, $size);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], $result['msg']);
    }

    /**
     * 上移下移批次
     * <AUTHOR> Li
     * @date  2020-07-22
     */
    public function sortCodeBatch()
    {
        $sid     = $this->loginInfo['sid'];
        $mid     = $this->loginInfo['memberID'];
        $batchId = I('post.batch_id');
        $confId  = I('post.conf_id');
        $status  = I('post.status'); //1上移 2下移

        if ($sid == 1) {
            $this->apiReturn(203, [], '管理员没有该权限，请联系供应商操作');
        }

        if (!$batchId) {
            $this->apiReturn(203, [], '缺少批次信息');
        }

        if (!$confId) {
            $this->apiReturn(203, [], '缺少配置信息');
        }

        $result = $this->_namageBiz->sortCodeBatch($sid, $mid, $confId, $batchId, $status);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 置顶批次
     * <AUTHOR> Li
     * @date  2020-07-22
     */
    public function topCodeBatch()
    {
        $sid     = $this->loginInfo['sid'];
        $mid     = $this->loginInfo['memberID'];
        $batchId = I('post.batch_id');
        $confId  = I('post.conf_id');

        if ($sid == 1) {
            $this->apiReturn(203, [], '管理员没有该权限，请联系供应商操作');
        }

        if (!$batchId) {
            $this->apiReturn(203, [], '缺少批次信息');
        }

        if (!$confId) {
            $this->apiReturn(203, [], '缺少配置信息');
        }

        $result = $this->_namageBiz->topCodeBatch($sid, $mid, $batchId, $confId);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 作废或恢复批次
     * <AUTHOR> Li
     * @date  2020-07-22
     */
    public function invalidOrRestoreCodeBatch()
    {
        $sid     = $this->loginInfo['sid'];
        $mid     = $this->loginInfo['memberID'];
        $batchId = I('post.batch_id');
        $confId  = I('post.conf_id');
        $status  = I('post.status'); //1作废 0恢复

        if ($sid == 1) {
            $this->apiReturn(203, [], '管理员没有该权限，请联系供应商操作');
        }

        if (!$confId) {
            $this->apiReturn(203, [], '缺少配置信息');
        }
        if (!$batchId) {
            $this->apiReturn(203, [], '缺少批次信息');
        }

        $result = $this->_namageBiz->invalidOrRestoreCodeBatch($sid, $mid, $confId, $batchId, $status);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 备注批次
     * <AUTHOR> Li
     * @date  2020-07-22
     */
    public function remarkCodeBatch()
    {
        $sid     = $this->loginInfo['sid'];
        $mid     = $this->loginInfo['memberID'];
        $batchId = I('post.batch_id');
        $confId  = I('post.conf_id');
        $remark  = I('post.remark');

        if ($sid == 1) {
            $this->apiReturn(203, [], '管理员没有该权限，请联系供应商操作');
        }

        if (!$batchId) {
            $this->apiReturn(203, [], '缺少批次信息');
        }

        if (!$confId) {
            $this->apiReturn(203, [], '缺失配置信息');
        }

        $result = $this->_namageBiz->remarkCodeBatch($sid, $mid, $batchId, $confId, $remark);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 获取发码配置信息
     * <AUTHOR> Li
     * @date  2020-07-27
     */
    public function getConfInfo()
    {
        $sid    = $this->loginInfo['sid'];
        $mid    = $this->loginInfo['memberID'];
        $confId = I('post.conf_id');

        if (!$confId) {
            $this->apiReturn(203, [], '缺失配置信息');
        }

        $result = $this->_namageBiz->getConfInfo($sid, $mid, $confId);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], $result['msg']);
    }

    /**
     * 获取批次配置信息
     * <AUTHOR> Li
     * @date  2020-07-27
     */
    public function getBatchInfo()
    {
        $sid     = $this->loginInfo['sid'];
        $mid     = $this->loginInfo['memberID'];
        $confId  = I('post.conf_id');
        $batchId = I('post.batch_id');

        if (!$confId) {
            $this->apiReturn(203, [], '缺失配置信息');
        }

        if (!$batchId) {
            $this->apiReturn(203, [], '缺失批次信息');
        }

        $result = $this->_namageBiz->getBatchInfo($sid, $mid, $confId, $batchId);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], $result['msg']);
    }

    /**
     * 导入票码
     * <AUTHOR> Li
     * @date  2020-07-22
     */
    public function importCode()
    {
        $sid       = $this->loginInfo['sid'];
        $mid       = $this->loginInfo['memberID'];
        $confId    = I('post.conf_id', 0, 'intval'); //发码配置id
        $validDate = I('post.valid_date', '', 'strval'); //有效期
        $remark    = I('post.remark', '', 'strval'); //备注
        $file      = $_FILES['file'];

        if ($sid == 1) {
            $this->apiReturn(203, [], '管理员没有该权限，请联系供应商操作');
        }

        if (!$confId) {
            $this->apiReturn(203, [], '缺少配置信息');
        }

        if (!$file) {
            $this->apiReturn(203, [], '文件有误，请确认');
        }

        //不设置有效期 默认有效期为 0
        if (!$validDate) {
            $validTime = 0;
        } else {
            $validTime = strtotime($validDate) + (86400 - 1);
        }

        //解析文件 获取文件内容
        $checkRes = $this->_namageBiz->fileCheck($file);
        if (!isset($checkRes['code']) || $checkRes['code'] != 200) {
            $this->apiReturn($checkRes['code'], $checkRes['data'], $checkRes['msg']);
        }

        $codeArr = $checkRes['data'];

        $result = $this->_namageBiz->importCode($sid, $mid, $confId, $codeArr, $validTime, $remark);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], $result['data'], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 导入票码二次确认
     * <AUTHOR> Li
     * @date  2020-07-27
     */
    public function importCodeConfirm()
    {
        $sid       = $this->loginInfo['sid'];
        $mid       = $this->loginInfo['memberID'];
        $confId    = I('post.conf_id', 0, 'intval'); //发码配置id
        $importKey = I('post.import_key', '', 'strval'); //导入上次关键字
        $isImport  = I('post.is_import', '', 'strval'); //是否导入 1不导入 2导入并作废之前的码

        if ($sid == 1) {
            $this->apiReturn(203, [], '管理员没有该权限，请联系供应商操作');
        }

        if (!$confId) {
            $this->apiReturn(203, [], '缺少配置信息');
        }

        if (!$importKey) {
            $this->apiReturn(203, [], '缺少导入配置');
        }

        $result = $this->_namageBiz->importCodeConfirm($sid, $mid, $confId, $importKey, $isImport);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    //===================码管理=============================

    /**
     * 获取码列表
     * <AUTHOR> Li
     * @date  2020-07-22
     */
    public function getCodeInfoList()
    {
        $sid      = $this->loginInfo['sid'];
        $mid      = $this->loginInfo['memberID'];
        $confId   = I('post.conf_id', 0, 'intval');
        $batchId  = I('post.batch_id', 0, 'intval');
        $code     = I('post.code', '', 'strval');
        $ordernum = I('post.ordernum', '', 'strval');
        $status   = I('post.status', -1, 'intval');
        $remarg   = I('post.remark', '', 'strval');
        $page     = I('post.page', 1, 'intval');
        $size     = I('post.size', 10, 'intval');
        $recycle  = I('post.recycle', 0, 'intval'); //回收站标识 0否;1是
        if ($recycle) {
            //设置回收站查询
            $this->_namageBiz->recycle = true;
        }

        if (!$confId) {
            $this->apiReturn(203, [], '缺少配置信息');
        }

        if (!$batchId) {
            $this->apiReturn(203, [], '缺少批次信息');
        }

        $result = $this->_namageBiz->getCodeInfoList($sid, $mid, $confId, $batchId, $code, $ordernum, $status, $remarg,
            $page, $size);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], $result['msg']);
    }

    /**
     * 编辑码备注
     * <AUTHOR> Li
     * @date  2020-07-22
     */
    public function remarkCode()
    {
        $sid     = $this->loginInfo['sid'];
        $mid     = $this->loginInfo['memberID'];
        $codeId  = I('post.code_id');
        $confId  = I('post.conf_id');
        $batchId = I('post.batch_id');
        $remark  = I('post.remark');

        if ($sid == 1) {
            $this->apiReturn(203, [], '管理员没有该权限，请联系供应商操作');
        }

        if (!$codeId) {
            $this->apiReturn(203, [], '缺少码信息');
        }

        if (!$confId) {
            $this->apiReturn(203, [], '缺少配置信息');
        }

        if (!$batchId) {
            $this->apiReturn(203, [], '缺少批次信息');
        }

        $result = $this->_namageBiz->remarkCode($sid, $mid, $codeId, $confId, $batchId, $remark);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 作废或者恢复码
     * <AUTHOR> Li
     * @date  2020-07-22
     */
    public function invalidOrRestoreCode()
    {
        $sid = $this->loginInfo['sid'];
        $mid = $this->loginInfo['memberID'];

        $codeId  = I('post.code_id');
        $confId  = I('post.conf_id');
        $batchId = I('post.batch_id');
        $status  = I('post.status');  //状态码 1作废 0恢复

        if ($sid == 1) {
            $this->apiReturn(203, [], '管理员没有该权限，请联系供应商操作');
        }

        if (!$codeId) {
            $this->apiReturn(203, [], '缺少码信息');
        }

        if (!$confId) {
            $this->apiReturn(203, [], '缺少配置信息');
        }

        if (!$batchId) {
            $this->apiReturn(203, [], '缺少批次信息');
        }

        $result = $this->_namageBiz->invalidOrRestoreCode($sid, $mid, $codeId, $confId, $batchId, $status);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], '操作成功');
    }

    /**
     * 批量作废或者恢复码
     * <AUTHOR> Li
     * @date  2020-07-22
     */
    public function invalidOrRestoreCodeMore()
    {
        $sid     = $this->loginInfo['sid'];
        $mid     = $this->loginInfo['memberID'];
        $codeIds = I('post.code_ids');
        $confId  = I('post.conf_id');
        $batchId = I('post.batch_id');
        $status  = I('post.status');  //状态码 1作废 0恢复

        if ($sid == 1) {
            $this->apiReturn(203, [], '管理员没有该权限，请联系供应商操作');
        }

        if (!$codeIds) {
            $this->apiReturn(203, [], '缺少码信息');
        }

        $codeIdArr = explode(',', $codeIds);

        if (!$confId) {
            $this->apiReturn(203, [], '缺少配置信息');
        }

        if (!$batchId) {
            $this->apiReturn(203, [], '缺少批次信息');
        }

        $result = $this->_namageBiz->invalidOrRestoreCodeMore($sid, $mid, $codeIdArr, $confId, $batchId, $status);

        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], $result['msg']);
    }

    //===============其他查询=========================

    /**
     * 外部码景区查询票 移除绑定上游门票
     * 注：创建的时候需要调用下
     * <AUTHOR>
     * @date 2022/1/24
     *
     */
    public function getSelfTicket()
    {
        $sid    = $this->loginInfo['sid'];
        $landId = I('post.lid', 0, 'intval');

        $result = $this->_namageBiz->getSelfTicketByLandId($sid, $landId);
        if ($result['code'] != 200) {
            $this->apiReturn($result['code'], [], $result['msg']);
        }

        $this->apiReturn(200, $result['data'], $result['msg']);
    }
}