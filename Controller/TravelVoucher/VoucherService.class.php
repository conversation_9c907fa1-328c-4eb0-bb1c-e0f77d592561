<?php

namespace Controller\TravelVoucher;

use Library\Controller;

class VoucherService extends Controller
{
    private $_sid;
    private $_memberId;

    public function __construct()
    {
        $memberInfo = $this->getLoginInfo();
        if (empty($memberInfo)) {
            $this->apiReturn(0, [], "未登录");
        }
        $this->_sid      = $memberInfo['sid'];
        $this->_memberId = $memberInfo['memberID'];
    }

    public function spuSkuServiceSearch()
    {
        $type    = I('type', 0, 'intval');
        $pids    = I('pids', []);
        $page    = I('page', 0, 'intval');
        $perPage = I('per_page', 0, 'intval');
        $name    = I('name', '', 'strval');

        $voucherService = new \Business\JsonRpcApi\TravelVoucherService\VoucherService();
        $result         = $voucherService->spuSkuServiceSearch($this->_sid, $type, $pids, $page, $perPage, $name);

        $this->apiReturn($result['code'], $result['data'], $result['msg']);
    }
}