<?php
/**
 * 对接外部接口的api地址配置
 *
 * <AUTHOR>
 * @date   2016-12-22
 */

//防止没有配置
if (!defined('ENV')) {
    define('ENV', 'DEVELOP');
}

if (ENV == 'DEVELOP') {
    $developIp = gethostbyname(gethostname());
    switch ($developIp) {
        case '**************':
            $apiBaseUrl    = 'http://**************';
            $apiPort       = '8080';
            $newApiBaseUrl = 'gateway245b.12301.test:32265';
            $newApiPort    = '32265';
            break;
        case '**************':
            $apiBaseUrl    = 'http://**************';
            $apiPort       = '8080';
            $newApiBaseUrl = 'gateway216b.12301.test:32514';
            $newApiPort    = '32514';
            break;
        case '**************':
            $apiBaseUrl    = 'http://**************';
            $apiPort       = '8080';
            $newApiBaseUrl = 'gateway242b.12301.test:30532';
            $newApiPort    = '30532';
            break;
        case '**************':
            $apiBaseUrl    = 'http://**************';
            $apiPort       = '8080';
            $newApiBaseUrl = 'gateway242b.12301.test:30532';
            $newApiPort    = '30532';
            break;
        case '**************':
            $apiBaseUrl    = 'http://**************';
            $apiPort       = '8080';
            $newApiBaseUrl = 'gateway171b.12301.test:30598';
            $newApiPort    = '30598';
            break;
        case '**************':
            $apiBaseUrl    = 'http://**************';
            $apiPort       = '8080';
            $newApiBaseUrl = 'gateway242b.12301.test:30532';
            $newApiPort    = '30532';
            break;
        default:
            $apiBaseUrl    = 'http://**************';
            $apiPort       = '8080';
            $newApiBaseUrl = 'gateway171b.12301.test:30598';
            $newApiPort    = '30598';

            break;
    }
}

if (ENV == 'PRODUCTION') {
    if (defined('IS_PFT_GRAY')) {
        $newJavaApiUrl = 'gateway-gray.pft12301.com';
        $newPort       = 80;

        //数据中心灰度配置
        $reportServiceApiUrl = 'http://report-server-gray.pft12301.com';
        $marketServiceApiUrl = 'http://vip-gray.pft12301.com/marketing';
        //新酒店
        $newHotelIntranetApiUrl = 'http://hotel-intranet-gray.pft12301.com';
        $shoppingCarApiUrl = 'http://shopping-cart-api-gray.pft12301.com';
        $upstreamJavaApi = 'http://upstream-java-api-gray.pft12301.com';
        $openDistributionApi = 'http://open-distribution-internal-gray.pft12301.com';
    } else {
        $newJavaApiUrl = 'gateway.pft12301.com';
        $newPort       = 80;

        //数据中心生产配置
        $reportServiceApiUrl = 'http://report-server.pft12301.com';
        $marketServiceApiUrl = 'http://vip.pft12301.com/marketing';
        //新酒店
        $newHotelIntranetApiUrl = 'http://hotel-intranet.pft12301.com';
        $shoppingCarApiUrl = 'http://shopping-cart-api.pft12301.com';
        $upstreamJavaApi = 'http://upstream-java-api.pft12301.com';
        $openDistributionApi = 'http://open-distribution-internal.pft12301.com';
    }

    //数据中心生产配置
    $reportServiceApiPort = 80;
}

$apiArr = [
    //生产环境配置
    'PRODUCTION' => [
        //支付相关配置
        'pay'          => [
            'card_pay' => 'http://pay.12301.cc/card/api.php',
        ],
        //java接口相关配置
        'java_api'     => [
            'v1'         => [
                //接口地址
                'api_base_url' => 'http://java-api.12301.cc',
                //端口号
                'api_port'     => 8080,
                //超时时间
                'time_out'     => 60,

                //通用业务线密钥
                'app_id'       => 'business_common',
                'app_secret'   => '2e32b0e2b809d3d573445d3427806ef2cd65bfbb',

                //服务接口地址切换， switch_api_info 配置项为切换默认配置，当特定场景需要去切换接口地址时，
                //使用switch_api_info的不同服务的配置项覆盖默认的配置，如：v1.api_base_url=switch_api_info.upstream_api.api_base_url
                //例如场景：门票产品线订单，针对有采购上游和没有采购上游的服务进行拆分，switch_api_info.upstream_api配置覆盖默认的配置的api_base_url
                //注：这里只是配置项，不是设置完就可以了，需要使用的时候，根据特定场景去调用配置。
                //参考代码：\Business\JavaApi\Order\ScenicOrder::submitOrder
                'switch_api_info'=>[
                    //上游下单服务地址
                    'upstream_api' => [
                        //接口地址
                        'api_base_url' => $upstreamJavaApi,
                        //端口号
                        'api_port'     => 80,
                    ],
                ],
            ],
            'douyin_api' => [
                //接口地址
                'api_base_url' => 'http://wanju.12301.cc',
                //端口号
                'api_port'     => 80,
                //超时时间
                'time_out'     => 60,
            ],
        ],
        //新java接口相关配置
        'new_java_api' => [
            'v1' => [
                //接口地址
                'api_base_url' => $newJavaApiUrl,
                //端口号
                'api_port'     => $newPort,
                //超时时间
                'time_out'     => 60,
                'app_id'       => 'business_common',
                'app_secret'   => '9a8b8cy3lnx7bshyq0lrhygjcndc5jze8rjwvi2z'
            ],
        ],
        //数据中心接口相关配置
        'report_service_api' => [
            //地址
            'api_base_url' => $reportServiceApiUrl,
            //端口号
            'api_port'     => $reportServiceApiPort,
        ],
        //营销中心
        'market_center' => [
            'url'     => $marketServiceApiUrl,
            'app_key' => 'pukom3xnxr9b2sl6',
        ],
        // 酒店
        'hotel' => [
            'intranet_domain' => $newHotelIntranetApiUrl,
        ],
        // 购物车
        'shopping_car' => [
            'api_base_url' => $shoppingCarApiUrl,
        ],
        // 开放平台
        'open_dis_center' => [
            'api_base_url' => $openDistributionApi,
            //端口号
            'api_port'     => 80,
        ],
        // 内部基础服务
        'infrastructure_api' => [
            'api_base_url' => 'http://infrastructure.pft12301.com',
            //端口号
            'api_port'     => 80,
            //超时时间
            'time_out'     => 2,
        ],
    ],

    //预生产配置
    'TEST'       => [
        //支付相关配置
        'pay'          => [
            'card_pay' => 'http://pay.12301dev.com/card/api.php',
        ],
        //java接口相关配置
        'java_api'     => [
            'v1'         => [
                //接口地址
                'api_base_url' => 'http://java-api.12301dev.com',
                //端口号
                'api_port'     => 8080,
                //超时时间
                'time_out'     => 60,

                //通用业务线密钥
                'app_id'       => 'business_common',
                'app_secret'   => '2e32b0e2b809d3d573445d3427806ef2cd65bfbb',

                //服务接口地址切换， switch_api_info 配置项为切换默认配置，当特定场景需要去切换接口地址时，
                //使用switch_api_info的不同服务的配置项覆盖默认的配置，如：v1.api_base_url=switch_api_info.upstream_api.api_base_url
                //例如场景：门票产品线订单，针对有采购上游和没有采购上游的服务进行拆分，switch_api_info.upstream_api配置覆盖默认的配置的api_base_url
                //注：这里只是配置项，不是设置完就可以了，需要使用的时候，根据特定场景去调用配置。
                //参考代码：\Business\JavaApi\Order\ScenicOrder::submitOrder
                'switch_api_info'=>[
                    //上游下单服务地址
                    'upstream_api' => [
                        //接口地址
                        'api_base_url' => 'http://upstream-java-api.12301dev.com',
                        //端口号
                        'api_port'     => 9080,
                    ],
                ],
            ],
            'douyin_api' => [
                //接口地址
                'api_base_url' => 'http://wanju.12301dev.com',
                //端口号
                'api_port'     => 80,
                //超时时间
                'time_out'     => 60,
            ],
        ],
        //新java接口相关配置
        'new_java_api' => [
            'v1' => [
                //接口地址
                'api_base_url' => 'gatewayb.12301dev.com:8390',
                //端口号
                'api_port'     => 8390,
                //超时时间
                'time_out'     => 60,
                'app_id'       => 'business_common',
                'app_secret'   => '9a8b8cy3lnx7bshyq0lrhygjcndc5jze8rjwvi2z'
            ],
        ],
        //数据中心接口相关配置
        'report_service_api' => [
            //地址
            'api_base_url' => 'http://report-server.12301dev.com',
            //端口号
            'api_port'     => 9080,
        ],
        //营销中心
        'market_center' => [
            'url'     => 'http://vip.12301dev.com/marketing',
            'app_key' => 'pukom3xnxr9b2sl6',
        ],
        // 酒店
        'hotel' => [
            'intranet_domain' => 'http://hotel-intranet.12301dev.com:9080',
        ],
        // 购物车
        'shopping_car' => [
            'api_base_url' => 'http://shopping-cart-api.12301dev.com:9080'
        ],
        // 开放平台
        'open_dis_center' => [
            'api_base_url' => 'http://open-distribution-internal.12301dev.com:9080',
            //端口号
            'api_port'     => 9080,
        ],
        // 内部基础服务
        'infrastructure_api' => [
            'api_base_url' => 'http://infrastructure.12301dev.com',
            //端口号
            'api_port'     => 9080,
            //超时时间
            'time_out'     => 2,
        ],
    ],

    //内网测试配置
    'DEVELOP'    => [
        //java接口相关配置
        'java_api'     => [
            'v1' => [
                'api_base_url' => $apiBaseUrl,
                //端口号
                'api_port'     => $apiPort,
                //超时时间
                'time_out'     => 60,

                //通用业务线密钥
                'app_id'       => 'business_common',
                'app_secret'   => '2e32b0e2b809d3d573445d3427806ef2cd65bfbb',

                //服务接口地址切换， switch_api_info 配置项为切换默认配置，当特定场景需要去切换接口地址时，
                //使用switch_api_info的不同服务的配置项覆盖默认的配置，如：v1.api_base_url=switch_api_info.upstream_api.api_base_url
                //例如场景：门票产品线订单，针对有采购上游和没有采购上游的服务进行拆分，switch_api_info.upstream_api配置覆盖默认的配置的api_base_url
                //注：这里只是配置项，不是设置完就可以了，需要使用的时候，根据特定场景去调用配置。
                //参考代码：\Business\JavaApi\Order\ScenicOrder::submitOrder
                'switch_api_info'=>[
                    //上游下单服务地址
                    'upstream_api' => [
                        //接口地址
                        'api_base_url' => $apiBaseUrl,
                        //端口号
                        'api_port'     => $apiPort,
                    ],
                ],
            ],
        ],
        //新java接口相关配置
        'new_java_api' => [
            'v1' => [
                //接口地址
                'api_base_url' => $newApiBaseUrl,
                //端口号
                'api_port'     => $newApiPort,
                //超时时间
                'time_out'     => 60,
                'app_id'       => 'business_common',
                'app_secret'   => '9a8b8cy3lnx7bshyq0lrhygjcndc5jze8rjwvi2z'
            ],
        ],
        //数据中心接口相关配置
        'report_service_api' => [
            //地址
            'api_base_url' => 'http://report-server.12301.test',
            //端口号
            'api_port'     => 80,
        ],
        //营销中心
        'market_center' => [
            'url'     => 'http://vip.12301.test/marketing',
            'app_key' => 'pukom3xnxr9b2sl6',
        ],
        // 酒店
        'hotel' => [
            'intranet_domain' => 'http://hotel-intranet.12301.test',
        ],
        // 购物车
        'shopping_car' => [
            'api_base_url' => 'http://shopping-cart-api.12301.test'
        ],
        // 开放平台
        'open_dis_center' => [
            'api_base_url' => 'http://open-distribution-internal.12301.test',
            //端口号
            'api_port'     => 80,
        ],
        // 内部基础服务
        'infrastructure_api' => [
            'api_base_url' => 'http://infrastructure.12301.test',
            //端口号
            'api_port'     => 80,
            //超时时间
            'time_out'     => 2,
        ],
    ],

    //内网测试配置 - 本地的这个配置默认使用和测试环境的一致
    //如果本地开发的时候有需要和java的开发环境联调，自己修改这里面的配置，但是代码提交的时候记得恢复原样
    //因为其他很多同学在本地开发是不需要使用java的开发环境的。
    'LOCAL'      => [
        //支付相关配置
        'pay'          => [
            'card_pay' => 'http://pay.12301.local/card/api.php',
        ],

        //java接口相关配置
        'java_api'     => [
            'v1'         => [
                //20.73为java稳定的环境，只部署master分支，有需要本地和java联调的再进行改动
                'api_base_url' => 'http://**************',
                //端口号
                'api_port'     => 8080,
                //超时时间
                'time_out'     => 60,

                //通用业务线密钥
                'app_id'       => 'business_common',
                'app_secret'   => '2e32b0e2b809d3d573445d3427806ef2cd65bfbb',

                //服务接口地址切换， switch_api_info 配置项为切换默认配置，当特定场景需要去切换接口地址时，
                //使用switch_api_info的不同服务的配置项覆盖默认的配置，如：v1.api_base_url=switch_api_info.upstream_api.api_base_url
                //例如场景：门票产品线订单，针对有采购上游和没有采购上游的服务进行拆分，switch_api_info.upstream_api配置覆盖默认的配置的api_base_url
                //注：这里只是配置项，不是设置完就可以了，需要使用的时候，根据特定场景去调用配置。
                //参考代码：\Business\JavaApi\Order\ScenicOrder::submitOrder
                'switch_api_info'=>[
                    //上游下单服务地址
                    'upstream_api' => [
                        'api_base_url' => 'http://**************',
                        //端口号
                        'api_port'     => 8080,
                    ],
                ],
            ],
            'douyin_api' => [
                //接口地址
                'api_base_url' => 'http://wanju.12301dev.com',
                //端口号
                'api_port'     => 80,
                //超时时间
                'time_out'     => 60,
            ],
        ],
        //新java接口相关配置
        'new_java_api' => [
            'v1' => [
                //接口地址
                'api_base_url' => 'gateway171b.12301.test:30598',    //171
                //'api_base_url' => 'gateway245b.12301.test:32265',  //245
//                'api_base_url' => 'gateway242b.12301.test:30532',  //242
//                'api_base_url' => 'gateway216b.12301.test:32514',  //216
                //端口号
                'api_port'     => 30598,  //171
//                'api_port'     => 32265,  //245
//                'api_port'     => 30532,  //242
//                'api_port'     => 32514,  //216
                //超时时间
                'time_out'     => 60,
                'app_id'       => 'business_common',
                'app_secret'   => '9a8b8cy3lnx7bshyq0lrhygjcndc5jze8rjwvi2z'
            ],
        ],
        //数据中心接口相关配置
        'report_service_api' => [
            //地址
            'api_base_url' => 'http://report-server.12301.test',
            //端口号
            'api_port'     => 80,
        ],
        //营销中心
        'market_center' => [
            'url'     => 'http://vip.12301.test/marketing',
            'app_key' => 'pukom3xnxr9b2sl6',
        ],
        // 酒店
        'hotel' => [
            'intranet_domain' => 'http://hotel-intranet.12301.test',
        ],
        // 购物车
        'shopping_car' => [
            'api_base_url' => 'http://shopping-cart-api.12301.test'
        ],
        // 开放平台
        'open_dis_center' => [
            'api_base_url' => 'http://open-distribution-internal.12301.test',
            //端口号
            'api_port'     => 80,
        ],
        // 内部基础服务
        'infrastructure_api' => [
            'api_base_url' => 'http://infrastructure.12301.test',
            //端口号
            'api_port'     => 80,
            //超时时间
            'time_out'     => 2,
        ],
    ],
];

//返回对应环境下面的配置
if (!isset($apiArr[ENV])) {
    return [];
} else {
    return $apiArr[ENV];
}
