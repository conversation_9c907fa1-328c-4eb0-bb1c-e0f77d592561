<?php
/**
 * 对接新java接口地址配置列表
 * <AUTHOR>  Li
 * @date   2022-03-10
 *
 */
return [
    'risk-center' => [
        'shumeiRisk' => [
            'path' => 'risk-center/shumeiRisk',
            'method_list' => [
                'check' => 'check', //数美校验
                'leadsSubmit' => 'leadsSubmit',//数美黑产手机号校验
                'checkWhiteList' => 'checkWhiteList',//是否在白名单中
            ]
        ]
    ],

    //订单查询相关的接口
    'order_center' => [
        //订单基础信息查询
        'order' => [
            'path'        => 'order-center/feign-client/order',
            'method_list' => [
                'batchQueryDistribution' => 'batchQueryDistribution',    //批量查询订单分销商信息
                'complete'               => 'complete',    //批量查询订单详情
                'aggregationIds'         => 'aggregation/ids', // 批量查询新订单中心订单信息
            ],
        ],
        'biz' => [
            'path'    => 'order-center/feign-client/biz',
            'method_list' => [
                // 批量查询订单产品类型 http://yapi.12301.test/project/226/interface/api/33822
               'batchQueryItemType' => 'product-order/item-type/batch',
                // 修改订单快照信息 http://yapi.12301.test/project/226/interface/api/34543
                'editOrderSnapshot' => 'mobile-available-product-order/edit-order-snapshot',
                //获取支付中心单号 http://yapi.12301.test/project/226/interface/api/34235
                'tradeOrderQueryPayId' => 'trade-order/query-pay-id',
            ],
        ],
        'product_order' => [
            'path'    => 'order-center/biz/product-order',
            'method_list' => [
                // 根据条件分页查询ES+DB（优先ES） http://yapi.12301.test/project/226/interface/api/35151
                'pageInfoBest' => 'page-info-best',
                // 批量订单号查询详细信息  http://yapi.12301.test/project/226/interface/api/35179
                'batchDetailInfo' => 'batch-detail-info',
            ],
        ],
        'order_key' => [
            'path'    => 'order-center/feign-client/order-key',
            'method_list' => [
                // 全量订单关联表-查询 http://yapi.12301.test/project/226/interface/api/38984
                'get' => 'get',
                // 全量订单关联表-批量查询 http://yapi.12301.test/project/226/interface/api/38983
                'batchGet' => 'batch-get',
            ],
        ],
    ],

    //独立收款相关的接口
    'user_center' => [
        //独立收款账户
        'independent' => [
            'path'        => 'user-center/feign-client/standaloneAccount',
            'method_list' => [
                'status' => 'status',    //通过ID查询独立收款账户状态(批量)
            ],
        ],

        //子商户信息查询
        'member_sub_merchant' => [
            'path'        => 'user-center/feign-client/member-sub-merchant',
            'method_list' => [
                'memberId' => 'memberId',    //通过MemberId,获取子商户信息,不存在时，返回null
                'batch'    => 'batch',    //通过MemberId,获取子商户信息,不存在时，返回null
                'page'     => 'page',    //通过MemberId,获取子商户信息,不存在时，返回null
            ],
        ],
        //自贡银行国密sm2
        'sm2' => [
            'path' => 'user-center/feign-client/security/sm2',
            'method_list' => [
                //user-center-web/src/main/java/com/pft/cloud/user/center/controller/fclient/certification/SecurityController.java
                'encrypt' => 'encrypt',
                'decrypt' => 'decrypt',
                'sign' => 'sign',
                'verify' => 'verify',
            ],
        ],
        'individual_traveler_register' => [
            'path'        => 'user-center/user',
            'method_list' => [
                'registerUser' => 'registerUser',    //注册散客账户
            ],
        ],
        'customer' => [
            'path' => 'user-center/feign-center/customer',
            'method_list' => [
                'updateMobile' => 'updateMobile', //修改手机号
            ]
        ],
        'user_age_rule' => [
            'path'        => 'user-center/feign-client/user-age-rule',
            'method_list' => [
                'fetchSimple'      => 'fetch-simple', //根据用户id和code获取配置内容 年龄规则code=AGE_RULE 1开始计算 2结束计算
                'batchFetchSimple' => 'batch-fetch-simple',//批量根据用户id和code获取配置内容 年龄规则code=AGE_RULE 1开始计算 2结束计算
            ],
        ],
        //自定义线下支付方式
        'offline_payment' => [
            'path' => 'user-center/feign-client/offline-pay-channel',
            'method_list' => [
                'list' => 'list',//获取商家自定义的线下支付方式列表
            ]
        ],
        'account_book_subject' => [
            'path' => 'user-center/feign-client/account-book-subject',
            'method_list' => [
                'list' => 'list', //获取账本列表
            ]
        ]
    ],

    'distribution'        => [
        //批量操作分销商
        'batch_operate_distributor'   => [
            'path'        => 'distribution',
            'method_list' => [
                //批量删除分销商
                'batchDeleteDistributor'              => 'groupDistributorConfig/batchDeleteDistributor',
                //批量加入黑名单
                'batchConfigureDowngradeEvoluteLimit' => 'distributionBlacklist/batchConfigureDowngradeEvoluteLimit',
                //操作单个加入或移除黑名单
                'configureDowngradeEvoluteLimit'      => 'distributionBlacklist/configureDowngradeEvoluteLimit',
                //批量移动分销商分组
                'moveMixGroupDistributor'             => 'groupDistributorConfig/moveMixGroupDistributor',
            ],
        ],
        'distributionChannelHandle' => [
            'path'        => 'distribution/distributionChannelHandle',
            'method_list' => [
                'queryMemberDefaultChannel' => 'queryMemberDefaultChannel'
            ]
        ],
        'distribution_channel_handle' => [
            'path'        => 'distribution/distributionChannelHandle',
            'method_list' => [
                'queryChannelList'               => 'queryChannelList', //查询渠道列表
                'queryChannelListMoreData'       => 'queryChannelListMoreData', //门票列表
                'landSortConfig'                 => 'landSortConfig', //设置景区渠道配置推荐级别（景区排序）
                'ticketSortConfig'               => 'ticketSortConfig', //设置景区渠道配置推荐级别（门票排序）（上移、下移）
                'queryChannelLandListByPaging'   => 'queryChannelLandListByPaging', //比价页面景区列表
                'queryChannelTicketInfoByPaging' => 'queryChannelTicketInfoByPaging', //比价页面门票列表
                'batchConfigChannel'             => 'batchConfigChannel', //批量修改分销渠道接口
                'memberChannelDefaultModify'     => 'memberChannelDefaultModify', //新产品默认初始渠道配置接口
            ],
        ],
        'distribution_blacklist'      => [
            'path'        => 'distribution/distributionBlacklist',
            'method_list' => [
                'queryBlacklistFidByPaging'      => 'queryBlacklistFidByPaging', //黑名单列表接口
                'configureDowngradeEvoluteLimit' => 'configureDowngradeEvoluteLimit', // 添加/删除 黑名单接口
            ],
        ],
        'distributor_num_limit'       => [
            'path'        => 'distribution/distributorNumLimit',
            'method_list' => [
                'queryDistributorNumLimitInfo'   => 'queryDistributorNumLimitInfo', //分销商数量限制详情接口
                'updateDistributorNumUpperLimit' => 'updateDistributorNumUpperLimit', //分销商数量上限变更接口
            ],
        ],
        'group_query'                 => [
            'path'        => 'distribution/groupQuery',
            'method_list' => [
                'queryGroupPriceConfigPage' => 'queryGroupPriceConfigPage', //分销链价格配置接口迁移
            ],
        ],
        'group_distributor_query'     => [
            'path'        => 'distribution/groupDistributorQuery',
            'method_list' => [
                'queryGroupAllDistributorByGroupId' => 'queryGroupAllDistributorByGroupId', //分组成员列表接口
                'queryOneGroupAndDistributor'       => 'queryOneGroupAndDistributor', //查询指定的分组和分销商信息
            ],
        ],
        'distribution_price_query'    => [
            'path'        => 'distribution/distributionPriceQuery',
            'method_list' => [
                //当前产品成本价(本级结算价)和零售价查询接口
                'queryOneChainAndBasePrice'               => 'queryOneChainAndBasePrice',
                //合作分销商- 分销价格 - 分销产品列表接口
                'queryGroupSelfProductPriceByGroupId'     => 'queryGroupSelfProductPriceByGroupId',
                //转分销
                'queryGroupTransferProductPriceByGroupId' => 'queryGroupTransferProductPriceByGroupId',
            ],
        ],
        'distribution_product_query'  => [
            'path'        => 'distribution/distributionProductQuery',
            'method_list' => [
                'queryDistributionLandTitlePage'   => 'queryDistributionLandTitlePage',// 景区名称列表接口
                'queryDistributionTicketTitlePage' => 'queryDistributionTicketTitlePage', //门票名称列表接口
            ],
        ],
        'distribution_product'        => [
            'path'        => 'distribution/distributionProduct',
            'method_list' => [
                'queryDistributionLand'   => 'queryDistributionLand',//查询分销景区列表（协议票景区下拉框）
                'queryDistributionTicket' => 'queryDistributionTicket', //查询分销门票列表（协议票门票下拉框）
            ],
        ],

        'product' => [
            'path'        => 'distribution/distributionProductQuery',
            'method_list' => [
                'queryDistributionLandTitlePage'   => 'queryDistributionLandTitlePage', //查询分销景区列表
                'queryDistributionTicketTitlePage' => 'queryDistributionTicketTitlePage',//查询分销门票列表
            ],
        ],
        'groupDistributorQuery' => [
            'path' => 'distribution/groupDistributorQuery',
            'method_list' => [
                'querySimpleGroupPage' => 'querySimpleGroupPage',
            ]
        ],
        'OperateRecordQuery' => [
            'path' => 'distribution/OperateRecordQuery',
            'method_list' => [
                'queryPriceConfigOperateProgressRecordPage' => 'queryPriceConfigOperateProgressRecordPage',
                'queryPriceConfigOperateRecordPageFromMongo' => 'queryPriceConfigOperateRecordPageFromMongo',
            ]
        ]
    ],

    // 交易流水
    'memberTrade' => [
        'commonTradeService' => [
            'path' => 'member/commonTradeService',
            'method_list' => [
                'transferTrade' => 'transferTrade' // 转账交易接口
            ]
        ]
    ],

    'user-center' => [
        'feesConfig' => [
            'path' => 'user-center/feesConfig',
            'method_list' => [
                'getRecords'           => 'getRecords', //获取具体的转账记录（清分记录）
                'getFrozeOrders'       => 'getFrozeOrders',
                'getFrozeSummary'      => 'getFrozeSummary',
                'queryWithdrawLimit'   => 'queryWithdrawLimit',
                'queryFeesConfigList'  => 'queryFeesConfigList',
                'addSettingInfo'       => 'addSettingInfo',
                'getAccounts'          => 'getAccounts',
                'getSettingInfo'       => 'getSettingInfo',
                'listFeeStatus'        => 'listFeeStatus',
                'setStatus'            => 'setStatus',
                'updateMemberConfig'   => 'updateMemberConfig',
                'updateSettingInfo'    => 'updateSettingInfo',
                'getSettingDatePeriod' => 'getSettingDatePeriod', //获取指定用户的清分周期信息
            ],
        ],
        'relationConfig' => [
            'path'        => 'user-center/relationConfig',
            'method_list' => [
                'inviteSupplyShipApply'    => 'inviteSupplyShipApply',
                'queryToSupplyShipApply'   => 'queryToSupplyShipApply',
                'registerToSupplyShip'     => 'registerToSupplyShip',
                'BatchBreakSupplyShip'     => 'BatchBreakSupplyShip',
                'registerSanyaDistributor' => 'registerSanyaDistributor',  //三亚会员卡注册会员
            ],
        ],
        'member' => [
            'path'        => 'user-center/member',
            'method_list' => [
                'getCreateBy' => 'getCreateBy',
            ]
        ],
        'email' => [
            'path' => 'user-center/email',
            'method_list' => [
                'bindEmail' => 'bindEmail',
                'changeEmail' => 'changeEmail',
                'sendEmailCode' => 'sendEmailCode',
                'updatePasswordByEmail' => 'updatePasswordByEmail',
                'verifyEmailCode' => 'verifyEmailCode',
            ]
        ],
        'userConfigRule' => [
            'path' => 'user-center/feign-client/userConfigRule',
            'method_list' => [
                'getByMemberIdAndCode' => 'getByMemberIdAndCode', // 获取配置信息
            ]
        ],
        'platformRegister' => [
            'path' => 'user-center/platform-register',
            'method_list' => [
                'getMobileDetails' => 'getMobileDetails', // 通过手机号检验注册情况
            ]
        ],
        'memberDistributor' => [
            'path' => 'user-center/member-distributor',
            'method_list' => [
                'register' => 'register', // 注册分销商
            ]
        ],
        'userAgeRule' => [
            'path' => 'user-center/feign-client/user-age-rule',
            'method_list' => [
                'fetch-simple' => 'fetch-simple', //获取年龄配置
            ]
        ],
        'adminRelationQuery' => [
            'path'        => 'user-center/admin/relationQuery',
            'method_list' => [
                'queryCommonSubRelationByPaging' => 'queryCommonSubRelationByPaging', // 管理端通用下级关系查询(用于集团账号查询下属成员)
            ]
        ]
    ],

    //审核流程
    'process' =>[
        'auditProcess' => [
            'path'        => 'workflow-center/process',
            'method_list' => [
                'queryBusinessApproveStatus' => 'queryBusinessApproveStatus', //根据功能类型、业务ID查询业务审批状态
            ],
        ]
    ],
    //分销中心
    'distribution-center' => [
        'batchSetPriceDis' => [
            'path'        => 'distribution-center/batchSetPriceDis',
            'method_list' => [
                'getBatchPriceForDisTask'     => 'getBatchPriceForDisTask',
                'getBatchSavePriceForDisTask' => 'getBatchSavePriceForDisTask',
                'isNeedRepeatRequest'         => 'isNeedRepeatRequest',
                'setBatchSavePriceForDisTask' => 'setBatchSavePriceForDisTask',
            ],
        ],
        'distributionPriceQuery'  => [
            'path'        => 'distribution-center/distributionPriceQuery',
            'method_list' => [
                'queryTransferProductSpecialPriceByFid' => 'queryTransferProductSpecialPriceByFid',//转分销产品列表
                'querySelfProductSpecialPriceByFid'     => 'querySelfProductSpecialPriceByFid',//自供应产品列表
            ],
        ],
        'distributionPriceConfig' => [
            'path'        => 'distribution-center/distributionPriceConfig',
            'method_list' => [
                'specialPriceGroupProductConfig' => 'specialPriceGroupProductConfig',//特殊价格保存接口
                'groupProductConfig'             => 'groupProductConfig',//分销价格配置接口
                'separateGroupProductConfig'     => 'separateGroupProductConfig', //特殊分组分销价格配置接口
            ],
        ],
        'groupDistributorConfig'  => [
            'path'        => 'distribution-center/groupDistributorConfig',
            'method_list' => [
                'moveSeparateToCommonGroup'      => 'moveSeparateToCommonGroup',//新的特殊分组恢复通用接口
                'moveSpecialPriceToCommonGroup'  => 'moveSpecialPriceToCommonGroup',//新的特殊价格恢复通用接口
                'moveDistributorToSeparateGroup' => 'moveDistributorToSeparateGroup',//切换为独立分组接口
                'moveMixGroupDistributor'        => 'moveMixGroupDistributor', //分销商移动分组
            ],
        ],
        'groupDistributorQuery'   => [
            'path'        => 'distribution-center/groupDistributorQuery',
            'method_list' => [
                'queryCooperationGroupPage'                => 'queryCooperationGroupPage', //分组列表
                'queryCooperationDistributorPageByGroupId' => 'queryCooperationDistributorPageByGroupId',//组内成员列表
                'queryQuickFilterDistributorPage'          => 'queryQuickFilterDistributorPage',//快速筛选分销商
                'querySimpleGroupPage'                     => 'querySimpleGroupPage',
                'querySimpleGroupDistributorPage'          => 'querySimpleGroupDistributorPage',
                'queryGroupBySidAndFid'                    => 'queryGroupBySidAndFid', //根据供应商、分销商批量查询分组信息
            ],
        ],
        'groupQuery' => [
            'path'        => 'distribution-center/groupQuery',
            'method_list' => [
                'queryGroupByIdList'                   => 'queryGroupByIdList', //根据主键查询分组列表
                'queryGroupByPaging'                   => 'queryGroupByPaging', //分页查询分组信息 支持分组名称模糊查询
                'queryGroupSpecialDistributorByPaging' => 'queryGroupSpecialDistributorByPaging', //分页查询分组信息 支持分组名称模糊查询 返回的有分销商组的账户信息
                'queryGroupById'                       => 'queryGroupById', //根据id查询分组信息
            ],
        ],
        'OperateRecordQuery'      => [
            'path'        => 'distribution-center/OperateRecordQuery',
            'method_list' => [
                'queryOperateRecordPage'       => 'queryOperateRecordPage',//变更记录
                'queryOperateRecordDetailPage' => 'queryOperateRecordDetailPage', //变更记录详情列表
            ],
        ],
        'groupConfig'             => [
            'path'        => 'distribution-center/groupConfig',
            'method_list' => [
                'addCommonGroup'               => 'addCommonGroup', //添加分组
                'updateCommonGroupName'        => 'updateCommonGroupName',//修改分组名称
                'defaultGroupConfig'           => 'defaultGroupConfig',//修改默认分组
                'groupSortConfig'              => 'groupSortConfig', //分组排序
                'deleteGroup'                  => 'deleteGroup',//删除分组
            ],
        ],
        'chainTaskQuery' => [
            'path' => 'distribution-center/chainTaskQuery',
            'method_list' => [
                'countMixMoveGroupTask' => 'countMixMoveGroupTask',
            ]
        ],
        'distributionProductQuery' => [
            'path' => 'distribution-center/distributionProductQuery',
            'method_list' => [
                'queryDistributionAndInvalidLandTitlePage' => 'queryDistributionAndInvalidLandTitlePage', // 查询自供应所有+有效转分销产品
                'queryDistributionAndInvalidTicketTitlePage' => 'queryDistributionAndInvalidTicketTitlePage', // 查询自供应所有+有效转分销门票
            ]
        ],
        'distributionChainQuery' => [
            'path' => 'distribution-center/distributionChainQuery',
            'method_list' => [
                'queryOneSkuValidChain' => 'queryOneSkuValidChain', // 根据skuId查询有效分销链
                'querySkuValidChainList' => 'querySkuValidChainList', // 根据skuIds列表查询有效分销链
            ]
        ],
        //快捷购票规则相关
        'quickBuyRule' => [
            'path' => 'distribution-center/quickBuyRuleMatch',
            'method_list' => [
                'queryQuickBuyMatchSolutionByPaging' => 'queryQuickBuyMatchSolutionByPaging', //分页查询购票方案
                'queryQuickBuyMatchSolution' => 'queryQuickBuyMatchSolution', //根据下单信息匹配可用的门票
            ]
        ],
        'quickBuySolution' => [
            'path' => 'distribution-center/quickBuySolution',
            'method_list' => [
                'querySolutionById' => 'querySolutionById',//根据id查询购票方案
            ]
        ]
    ],

    //用户关系相关接口
    'relation_config' => [
        //关系操作
        'member' => [
            'path'        => 'user-center/relationConfig',
            'method_list' => [
                'breakSupplyShip'                   => 'breakSupplyShip',               //断开供需关系
                'createSupplyShip'                  => 'createSupplyShip',              //创建供需关系
                'createSupplyShipAndRecord'         => 'createSupplyShipAndRecord',     //创建供需关系,并直接新增一条同意记录
                'submitSupplyShipApply'             => 'submitSupplyShipApply',         //提交创建供需关系申请
                'checkSupplyShipApply'              => 'checkSupplyShipApply',          //批量审核供需关系
                'submitSupplyShipApplyByInviteCode' => 'submitSupplyShipApplyByInviteCode', //邀请码 仅提交申请，待对应用户审核通过后才会创建关系
            ],
        ],
    ],

    //资质认证的接口
    'certification' => [
        'certification' => [
            'path'        => 'user-center/feign-client/user-certification',
            'method_list' => [
                'detail'               => 'detail',                     //查询用户资质详细信息
                'status'               => 'status',                     //查询用户资质认证状态
                'change'               => 'change',                     //修改用户资质信息
                'approve'              => 'approve',                    //认证审核
                'cancel'               => 'cancel',                     //撤回用户资质认证
                'changeByAdmin'        => 'changeByAdmin',              //修改用户资质实名信息
                'sync'                 => 'sync',                       //信息同步到资质认证
                'unbindBankcardVerify' => 'unbindBankcardVerify',       //解绑银行卡校验
                'getSubjectName'       => 'subject-name',    //获取最新资质认证的主体名称
            ],
        ],
    ],
    'pms' => [
        'labelInfo' => [
            'path' => 'pms/labelInfo',
            'method_list' => [
                'list' => 'list',//获取标签列表
            ]
        ],
        'query' => [
            'path' => 'pms/label/query',
            'method_list' => [
                'productLabel' => 'productLabel',//获取商品标签
            ]
        ],
        'new' => [
            'path' => 'pms/new',
            'method_list' => [
                'queryPage' => 'query/queryPage', // 分页查询poi
                'spuDetail' => 'spuInfo/get', // spu详情
                'spuList' => 'spuInfo/pageSearch', // spu列表
            ]
        ],
        'stockQuery' => [
            'path' => 'pms/stockQuery',
            'method_list' => [
                'queryBatchSkuAvailableStock' => 'queryBatchSkuAvailableStock', // 批量查询sku指定日期的库存
                'queryRangeSkuAvailableStock' => 'queryRangeSkuAvailableStock', // 日期范围查询sku的库存
            ]
        ]
    ],
    'afterSale' => [
        'afterSaleOrderRecord' => [
            'path' => 'aftersales-center/afterSaleCode',
            'method_list' => [
                'queryListCodeStateByOrder' => 'queryListCodeStateByOrder',//获取标签列表
            ]
        ],
        'afterSaleExport' => [
            'path' => 'aftersales-center/afterSaleExport',
            'method_list' => [
                'queryAuditExpoertPage' => 'queryAuditExpoertPage',//售后审核导出列表
                'page' => 'page',//售后记录导出
            ]
        ],
        'afterSaleTransaction' => [
            'path' => 'order-center/feign-client/after-sales',
            'method_list' => [
                'detail' => 'detail',//获取标签列表
            ]
        ]
    ],
    'approvalFlowCenter' => [
        'process' => [
            'path' => 'workflow-center/process',
            'method_list' => [
                'startProcessCheck' => 'startProcessCheck',//流程校验
                'deleteProcInsById' => 'deleteProcInsById', //撤销流程
                'instancePage' => 'instancePage',//通用查询审批流程列表 通用
                'historicalFlow' => 'histoicFlow',//流转意见列表
                'runningInstanceList' => 'runningInstanceList', // 商家端-全部流程-未归档列表 / 售后管理-等待审核列表
                'finishInstanceList' => 'finishInstanceList',//商家端-全部流程-已归档列表 / 售后管理-同意拒绝列表
                'updateProcessExt' => 'updateProcessExt',//同步审核状态
            ]
        ],
        'task' => [
            'path' => 'workflow-center/task',
            'method_list' => [
                'startProcess' => 'startProcess',//发起流程
                'deleteProcInsById' => 'deleteProcInsById',//业务处理结果回调
                'completePassWait' => 'completePassWait',//passNotifyWait 结果同步
                'completeBusinessWait' => 'completeBusinessWait',//businessNotifyWait  结果同步
                'getBusinessNodesByProcId' => 'getBusinessNodesByProcId',//根据流程获取所有节点
                'cancelProcess' => 'cancelProcess',//取消节点
                'startProcessBatch' => 'startProcessBatch',//批量
                'forceCompleteTask' => 'forceCompleteTask',//跳过前审
            ]
        ],
        'processNameDictConfig' => [
            'path' => 'workflow-center/processNameDictConfig',
            'method_list' => [
                'addDict' => 'addDict',//添加字典配置
                'getProcessName' => 'getProcessName',//根据配置获取流程名称
                'queryProcessNameDictList'=>'queryProcessNameDictList' //获取流程名称字典列表
            ]
        ]
    ],
    'tagCenter'=>[
        'tag' => [
            'path' => 'tag-center/tag',
            'method_list' => [
                'page' => 'page',//分页查询标签
            ]
        ],
        'taggedData' => [
            'path' => 'tag-center/taggedData',
            'method_list' => [
                'tagSubject' => 'tag/subject',//分页  通过主体获取标签打标数据
                'subjectTag' => 'subject/tag',//分页  通过标签获取主体打标数据
            ]
        ],
        'tagGroup' => [
            'path' => 'tag-center/tagGroup',
            'method_list' => [
                'tagGroupPage' => 'page',//分页  分页查询标签组
                'getList' => 'get/list', // 查询多个标签组详情 https://apifox.com/apidoc/shared/e7f63d5f-157d-4de6-9c9f-acd9c0d560b5/api-288531959
            ]
        ],
    ],

    'base_server' => [
        'calendar' => [
            'path' => 'base-server/feign-client/calendar',
            'method_list' => [
                'date'  => 'date',//查询日期详情
                'month' => 'year/month',//查询月期详情
            ]
        ],
        'biz' => [
            'path' => 'base-server/biz',
            'method_list' => [
                'getPhoneAreaList' => 'global-phone-area/list', //查询手机号列表
            ]
        ],
    ],

    'trade-center' => [
        'divide_record' => [
            'path' => 'trade-center/feign-client/divide/record',
            'method_list' => [
                'queryDivideRecordByOrderId' => 'queryDivideRecordByOrderId' // 通过订单号查询分账记录状态
            ]
        ],
        'trade-traffic' => [
            'path' => 'trade-center/feign-client/trade-traffic',
            'method_list' => [
                'rollback' => 'rollback', //回滚码数退回
            ]
        ],
        'trade_journal_hot_data' => [
            'path'        => 'trade-center/feign-client/trade-journal-hot-data',
            'method_list' => [
                'compatiblePage'        => 'compatible/page', //分页查询交易记录（热数据查询）
                'compatibleExportQuery' => 'compatible/export/query', //导出分页查询交易记录（热数据查询）
                'compatibleSummary'     => 'compatible/summary', //汇总查询交易记录（热数据查询）
            ],
        ],
    ],

];
