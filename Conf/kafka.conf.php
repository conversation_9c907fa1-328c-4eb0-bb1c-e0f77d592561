<?php
/**
 * kafka配置文件
 * <AUTHOR>
 * @date 2021/3/17
 */
switch (ENV) {
    case 'LOCAL':
        $config = [
            'kafka_consumer' => [
                'platform' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'platform_special_team_order_local',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
//                        'myuu_uu_ss_order'           => 'Order/SpecialTeamOrderConsumer',
                        'myuu_uu_order_tourist_info' => 'Order/OrderTouristInfoConsumer',
                        'ticketAttOpTopic'           => 'Product/TicketAttOpConsumer',
                        'myuu_pft_evolute_group_fid' => 'MemberRelation/MemberRelation',
                        'platform_land_new'          => 'Product/LandCreateConsumer',
                        'ticket_order_cancel_topic'  => 'Order/TicketOrderCancelConsumer', //退票处理
                        'ticket_order_pay_topic'     => 'Order/TicketOrderPayConsumer', //支付后处理
//                        'open_upstream_ticketing_status' => 'Order/TicketFailIssueTicketsConsumer',//OTA出票失败处理 改为产品线通知
                        'show_order_ticketing_status' => 'Order/PackTicketFailIssueConsumerForShow',//演出子票票失败处理
                        'openOtaPlatform_ticketBind'  => 'openOtaPlatform_ticketBind_local', //开放平台ota票付通绑定
                        'ticket_order_open_ticket' => 'Order/PackTicketFailIssueConsumerForScenic',//景区出票失败处理
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_process' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'platform_special_team_order_local',
                    'expect_invoice_billable_offset' => '0:145437,1:137188,2:137898,3:136915,4:137969,5:139043,6:137632,7:139263,8:138907,9:138110',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'myuu_uu_ss_order'           => 'Order/SpecialTeamOrderConsumer',
                    ],
                    'process_num'       => 1,//ps:开启多进程
                ],
                'platform_member_wechat' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'platform_member_wechat',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'member_bind_wechat'           => 'MemberWechat/BindWechat',//绑定微信
                        'member_unbind_wechat'         => 'MemberWechat/UnbindWechat',//解绑微信
                        'member_wechat_config_change'  => 'MemberWechat/ConfigChange', //更新微信配置信息
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'down_ota' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'down_ota',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'ticket_topic'  => 'Product/QunarTicketConsumer',
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_app_center_module' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'platform_app_center_module',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'app_center_module_notice_topic' => 'AppCenter/ModuleChange',//应用变更
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'admin_member_wechat_unbinding' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'admin_member_wechat_unbinding',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'manage_member_unbind_wechat' => 'MemberWechat/AdminUnbindingWechat',//管理端解绑票付通员工微信
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'member_center' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'member_center_create_member',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'create_member_test' => 'Member/CreateMemberNotice',//创建用户后通知
                        'member_staff_delete_event_test' => 'Member/MemberStaffDelete', //中台删除员工
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_myuu_pft_order_track' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'platform_myuu_pft_order_track',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'myuu_pft_order_track' => 'Order/OrderTrackConsumer',//追踪记录
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_ticket_order_verify' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'platform_ticket_order_verify',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'ticket_order_verify_topic_test' => 'Order/OrderVerifyConsumer',//追踪记录
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_aliyun' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'productLineService_orderCancel_test',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'productLineService_orderCancelAsync_test' => 'Order/OrderCancelAsyncConsumer', //订单取消异步任务
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
            ],
            'kafka_producer' => [
                'platform' => [
                    'host'              => '**************:9092',
                    'topic' => [
                        'order_status_topic'                   => 'order_status_topic',
                        'refund_examine_topic'                 => 'refund_examine_topic',
                        'shuMeiEventPushInput'                 => 'shuMeiEventPushInput',
                        'order_refund_topic'                   => 'order_refund_topic',
                        'time_ticket_change_topic'             => 'time_ticket_change_topic',
                        'batch_refund_notice_after_sale_topic' => 'batch_refund_notice_after_sale_topic_test',
                        'equity_cancel_notify_test'            => 'equity_cancel_notify_test',
                        'transaction_verification_order'       => 'transaction_verification_order_test', //解冻处理
                        'open_upstream_ticketing_status'       => 'open_upstream_ticketing_status', //出票失败
                        'annual_card_operation_topic'          => 'annual_card_operation_topic', //年卡操作变更
                        'changed_examine_topic'                => 'changed_examine_topic', //下游改签审核通知
                        'cancelService_exchangeBaseTicketCancel'  => 'cancelService_exchangeBaseTicketCancel_test',      // 预售券退票通知
                    ],
                ],
                'offline_ticket' => [
                    'host'             => '**************:9092',
                    'topic' => [
                        'platform_site_topic' => 'platform_site_mod',
                    ],
                ],
                'bot'      => [
                    'host'  => '**************:9092',
                    'topic' => [
                        'feishu_bot_card' => 'feishu_bot_card',
                    ],
                ],
                'sub_merchant'=> [
                    'host'  => '**************:9092',
                    'topic' => [
                        'sub_merchant_log' => 'sub_merchant_log_test171',
                    ],
                ],
                'platform_aliyun'=> [
                    'host'  => '**************:9092',
                    'topic' => [
                        'order_cancel_async_topic' => 'productLineService_orderCancelAsync_test', //订单取消异步任务
                    ],
                ],
            ],
        ];
        break;
    case 'DEVELOP':
        $config = [
            'kafka_consumer' => [
                'platform' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'platform_special_team_order_test',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
//                        'myuu_uu_ss_order'           => 'Order/SpecialTeamOrderConsumer',
                        'myuu_uu_order_tourist_info' => 'Order/OrderTouristInfoConsumer',
                        'ticketAttOpTopic'           => 'Product/TicketAttOpConsumer',
                        'myuu_pft_evolute_group_fid' => 'MemberRelation/MemberRelation',
                        'platform_land_new'          => 'Product/LandCreateConsumer',
                        'ticket_order_cancel_topic'  => 'Order/TicketOrderCancelConsumer', //退票处理
                        'ticket_order_pay_topic'     => 'Order/TicketOrderPayConsumer', //支付后处理
//                        'open_upstream_ticketing_status' => 'Order/TicketFailIssueTicketsConsumer',//出票失败处理
                        'show_order_ticketing_status' => 'Order/PackTicketFailIssueConsumerForShow',//演出子票票失败处理
                        'ticket_order_open_ticket' => 'Order/PackTicketFailIssueConsumerForScenic',//景区出票失败处理
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_process' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'platform_special_team_order_test',
                    'expect_invoice_billable_offset' => '0:145438,1:137193,2:137903,3:136919,4:137977,5:139050,6:137638,7:139267,8:138912,9:138117',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'myuu_uu_ss_order'           => 'Order/SpecialTeamOrderConsumer',
                    ],
                    'process_num'       => 1,//ps:开启多进程
                ],

                'platform_member_wechat' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'platform_member_wechat',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'member_bind_wechat'           => 'MemberWechat/BindWechat',//绑定微信
                        'member_unbind_wechat'         => 'MemberWechat/UnbindWechat',//解绑微信
                        'member_wechat_config_change'  => 'MemberWechat/ConfigChange', //更新微信配置信息
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_app_center_module' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'platform_app_center_module',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'app_center_module_notice_topic' => 'AppCenter/ModuleChange',//应用变更
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'down_ota' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'down_ota',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'ticket_topic'  => 'Product/QunarTicketConsumer',
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'admin_member_wechat_unbinding' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'admin_member_wechat_unbinding',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'manage_member_unbind_wechat' => 'MemberWechat/AdminUnbindingWechat',//管理端解绑票付通员工微信
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'member_center' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'member_center_create_member',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'create_member_test' => 'Member/CreateMemberNotice',//创建用户后通知
                        'member_staff_delete_event_test' => 'Member/MemberStaffDelete', //中台删除员工
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_myuu_pft_order_track' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'platform_myuu_pft_order_track',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'myuu_pft_order_track' => 'Order/OrderTrackConsumer',//追踪记录
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_ticket_order_verify' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'platform_ticket_order_verify',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'ticket_order_verify_topic_test' => 'Order/OrderVerifyConsumer',//追踪记录
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_aliyun' => [
                    'host'              => '**************:9092',
                    'group_id'          => 'productLineService_orderCancel_test',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'productLineService_orderCancelAsync_test' => 'Order/OrderCancelAsyncConsumer', //订单取消异步任务
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
            ],
            'kafka_producer' => [
                'platform' => [
                    'host'              => '**************:9092',
                    'topic' => [
                        'order_status_topic'                   => 'order_status_topic',
                        'refund_examine_topic'                 => 'refund_examine_topic',
                        'shuMeiEventPushInput'                 => 'shuMeiEventPushInput',
                        'order_refund_topic'                   => 'order_refund_topic',
                        'time_ticket_change_topic'             => 'time_ticket_change_topic',
                        'batch_refund_notice_after_sale_topic' => 'batch_refund_notice_after_sale_topic_test',
                        'equity_cancel_notify_test'            => 'equity_cancel_notify_test',
                        'transaction_verification_order'       => 'transaction_verification_order_test', //解冻处理
                        'open_upstream_ticketing_status'       => 'open_upstream_ticketing_status', //出票失败
                        'annual_card_operation_topic'          => 'annual_card_operation_topic', //年卡操作变更
                        'changed_examine_topic'                => 'changed_examine_topic', //下游改签审核通知
                        'cancelService_exchangeBaseTicketCancel' => 'cancelService_exchangeBaseTicketCancel_test', // 预售券退票通知
                    ],
                ],
                'offline_ticket' => [
                    'host'             => '**************:9092',
                    'topic' => [
                        'platform_site_topic' => 'platform_site_mod',
                    ],
                ],
                'bot' => [
                    'host'  => '**************:9092',
                    'topic' => [
                        'feishu_bot_card' => 'feishu_bot_card',
                    ],
                ],
                'sub_merchant'=> [
                    'host'  => '**************:9092',
                    'topic' => [
                        'sub_merchant_log' => 'sub_merchant_log_test171',
                    ],
                ],
                'platform_aliyun' => [
                    'host'  => '**************:9092',
                    'topic' => [
                        'order_cancel_async_topic' => 'productLineService_orderCancelAsync_test', //订单取消异步任务
                    ],
                ],
            ],
        ];
        break;
    case 'TEST':
        $config = [
            'kafka_consumer' => [
                'platform' => [
                    'host'              => '**********:9092',
                    'group_id'          => 'platform_special_team_order_dev',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
//                        'myuu_uu_ss_order'           => 'Order/SpecialTeamOrderConsumer',
                        'myuu_uu_order_tourist_info' => 'Order/OrderTouristInfoConsumer',
                        'ticketAttOpTopic'           => 'Product/TicketAttOpConsumer',
                        'myuu_pft_evolute_group_fid' => 'MemberRelation/MemberRelation',
                        'platform_land_new'          => 'Product/LandCreateConsumer',
                        'ticket_order_cancel_topic'  => 'Order/TicketOrderCancelConsumer', //退票处理
                        'ticket_order_pay_topic'     => 'Order/TicketOrderPayConsumer', //支付后处理
//                        'open_upstream_ticketing_status' => 'Order/TicketFailIssueTicketsConsumer',//出票失败处理
                        'show_order_ticketing_status' => 'Order/PackTicketFailIssueConsumerForShow',//演出子票票失败处理
                        'ticket_order_open_ticket' => 'Order/PackTicketFailIssueConsumerForScenic',//景区出票失败处理
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_process' => [
                    'host'              => '**********:9092',
                    'group_id'          => 'platform_special_team_order_dev',
                    'expect_invoice_billable_offset' => '0:3532886,1:11997,2:11769,3:11836,4:11923',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'myuu_uu_ss_order'           => 'Order/SpecialTeamOrderConsumer',
                    ],
                    'process_num'       => 1,//ps:开启多进程
                ],
                'platform_member_wechat' => [
                    'host'              => '**********:9092',
                    'group_id'          => 'platform_member_wechat',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'member_bind_wechat'           => 'MemberWechat/BindWechat',//绑定微信
                        'member_unbind_wechat'         => 'MemberWechat/UnbindWechat',//解绑微信
                        'member_wechat_config_change'  => 'MemberWechat/ConfigChange', //更新微信配置信息
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_app_center_module' => [
                    'host'              => '**********:9092',
                    'group_id'          => 'platform_app_center_module',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'app_center_module_notice_topic' => 'AppCenter/ModuleChange',//应用变更
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'down_ota' => [
                    'host'              => '**********:9092',
                    'group_id'          => 'down_ota',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'ticket_topic'  => 'Product/QunarTicketConsumer',
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'admin_member_wechat_unbinding' => [
                    'host'              => '**********:9092',
                    'group_id'          => 'admin_member_wechat_unbinding',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'manage_member_unbind_wechat' => 'MemberWechat/AdminUnbindingWechat',//管理端解绑票付通员工微信
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'member_center' => [
                    'host'              => '**********:9092',
                    'group_id'          => 'member_center_create_member',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'create_member_dev' => 'Member/CreateMemberNotice',//创建用户后通知
                        'member_staff_delete_event_dev' => 'Member/MemberStaffDelete', //中台删除员工
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_myuu_pft_order_track' => [
                    'host'              => '**********:9092',
                    'group_id'          => 'platform_myuu_pft_order_track',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'myuu_pft_order_track' => 'Order/OrderTrackConsumer',//追踪记录
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_ticket_order_verify' => [
                    'host'              => '**********:9092',
                    'group_id'          => 'platform_ticket_order_verify',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'ticket_order_verify_topic_dev' => 'Order/OrderVerifyConsumer',//追踪记录
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_aliyun' => [
                    'host'              => '**********:9092',
                    'group_id'          => 'productLineService_orderCancel_dev',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'productLineService_orderCancelAsync_dev' => 'Order/OrderCancelAsyncConsumer', //订单取消异步任务
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
            ],
            'kafka_producer' => [
                'platform' => [
                    'host'              => '**********:9092',
                    'topic' => [
                        'order_status_topic'                   => 'order_status_topic',
                        'refund_examine_topic'                 => 'refund_examine_topic',
                        'shuMeiEventPushInput'                 => 'shuMeiEventPushInput',
                        'order_refund_topic'                   => 'order_refund_topic',
                        'time_ticket_change_topic'             => 'time_ticket_change_topic',
                        'batch_refund_notice_after_sale_topic' => 'batch_refund_notice_after_sale_topic_dev',
                        'openMonitoringCenter_log'             => 'openMonitoringCenter_log_dev', //开放监控中心日志
                        'equity_cancel_notify_dev'             => 'equity_cancel_notify_dev',
                        'transaction_verification_order'       => 'transaction_verification_order_dev',
                        'open_upstream_ticketing_status'       => 'open_upstream_ticketing_status', //出票失败
                        'openOtaPlatform_ticketBind'           => 'openOtaPlatform_ticketBind_dev', //开放平台ota票付通绑定
                        'annual_card_operation_topic'          => 'annual_card_operation_topic', //年卡操作变更
                        'changed_examine_topic'                => 'changed_examine_topic', //下游改签审核通知
                        'cancelService_exchangeBaseTicketCancel' => 'cancelService_exchangeBaseTicketCancel_dev', // 预售券退票通知
                    ],
                ],
                'offline_ticket' => [
                    'host'              => '**********:9092',
                    'topic' => [
                        'platform_site_topic' => 'platform_site_mod',
                    ],
                ],
                'bot' => [
                    'host'  => '**********:9092',
                    'topic' => [
                        'feishu_bot_card' => 'feishu_bot_card',
                    ],
                ],
                'sub_merchant' => [
                    'host'  => '**********:9092',
                    'topic' => [
                        'sub_merchant_log' => 'sub_merchant_log_dev',
                    ],
                ],
                'platform_aliyun' => [
                    'host'              => '**********:9092',
                    'topic' => [
                        'order_cancel_async_topic' => 'productLineService_orderCancelAsync_dev', //订单取消异步任务
                    ],
                ],
            ],
        ];
        break;
    case 'PRODUCTION':
        $batchRefundNoticeAfterSaleTopic = 'batch_refund_notice_after_sale_topic_prod';
        $equityCancelNotifyTopic = "equity_cancel_notify_prod";
        $ticketOrderVerifyTopic  = "ticket_order_verify_topic_prod";
        $transactionVerificationOrderTopic = 'transaction_verification_order_prod';
        $openMonitoringCenterLogTopic = "openMonitoringCenter_log_prod";
        $openOtaPlatformTicketBindTopic = 'openOtaPlatform_ticketBind_prod';
        $cancelServiceExchangeBaseTicketCancelTopic = 'cancelService_exchangeBaseTicketCancel_prod';
        $orderCancelAsyncTopic = 'productLineService_orderCancelAsync_prod';
        if (\Library\Tools::isGrayEnv()) {
            $batchRefundNoticeAfterSaleTopic = 'batch_refund_notice_after_sale_topic_gray';
            $equityCancelNotifyTopic = "equity_cancel_notify_gray";
            $ticketOrderVerifyTopic  = "ticket_order_verify_topic_gray";
            $transactionVerificationOrderTopic = 'transaction_verification_order_gray';
            $openMonitoringCenterLogTopic = "openMonitoringCenter_log_gray";
            $openOtaPlatformTicketBindTopic = 'openOtaPlatform_ticketBind_gray';
            $cancelServiceExchangeBaseTicketCancelTopic = 'cancelService_exchangeBaseTicketCancel_gray';
            $orderCancelAsyncTopic = 'productLineService_orderCancelAsync_gray';
        }

        $config = [
            'kafka_consumer' => [
                'platform' => [
                    'host'              => '*********:9092,*********:9092,*********:9092',
                    'group_id'          => 'production_platform_special_team_order',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
//                        'es_myuu_uu_ss_order'           => 'Order/SpecialTeamOrderConsumer',
                        'es_myuu_uu_order_tourist_info' => 'Order/OrderTouristInfoConsumer',
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_process' => [
                    'host'              => '*********:9092,*********:9092,*********:9092',
                    'group_id'          => 'production_platform_special_team_order',
                    'expect_invoice_billable_offset' => '0:92803168,1:48948536,2:48906641,3:48907061,4:48927329,5:48904640,6:48929736,7:48892910,8:48951302,9:48893527,10:48919044,11:48917959,12:48913180,13:48912691,14:48940580,15:48897906,16:48893139,17:48926235,18:48901696,19:48908708,20:48893818,21:48896700,22:48908640,23:48923176',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'es_myuu_uu_ss_order'           => 'Order/SpecialTeamOrderConsumer',
                    ],
                    'process_num'       => 1,
                ],
                'platform_default' => [
                    'host'              => '*********:9092,*********:9092,*********:9092',
                    'group_id'          => 'production_platform_default',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'ticketAttOpTopic'              => 'Product/TicketAttOpConsumer',
                        'myuu_pft_evolute_group_fid'    => 'MemberRelation/MemberRelation',
                        'platform_land_new'             => 'Product/LandCreateConsumer',
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_order' => [
                    'host'              => '*********:9092,*********:9092,*********:9092',
                    'group_id'          => 'production_platform_order',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'ticket_order_cancel_topic'     => 'Order/TicketOrderCancelConsumer', //退票处理
                        'ticket_order_pay_topic'        => 'Order/TicketOrderPayConsumer', //支付后处理
//                        'open_upstream_ticketing_status' => 'Order/TicketFailIssueTicketsConsumer',//出票失败处理
                        'show_order_ticketing_status' => 'Order/PackTicketFailIssueConsumerForShow',//演出子票票失败处理
                        'ticket_order_open_ticket' => 'Order/PackTicketFailIssueConsumerForScenic',//景区出票失败处理
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_member_wechat' => [
                    'host'              => '*********:9092,*********:9092,*********:9092',
                    'group_id'          => 'platform_member_wechat',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'member_bind_wechat'            => 'MemberWechat/BindWechat',//绑定微信
                        'member_unbind_wechat'          => 'MemberWechat/UnbindWechat',//解绑微信
                        'member_wechat_config_change'   => 'MemberWechat/ConfigChange', //更新微信配置信息
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_app_center_module' => [
                    'host'              => '*********:9092,*********:9092,*********:9092',
                    'group_id'          => 'platform_app_center_module',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'app_center_module_notice_topic' => 'AppCenter/ModuleChange',//应用变更
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'down_ota' => [
                    'host'              => '*********:9092,*********:9092,*********:9092',
                    'group_id'          => 'down_ota',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'ticket_topic'  => 'Product/QunarTicketConsumer',
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'admin_member_wechat_unbinding' => [
                    'host'              => '*********:9092,*********:9092,*********:9092',
                    'group_id'          => 'admin_member_wechat_unbinding',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'manage_member_unbind_wechat' => 'MemberWechat/AdminUnbindingWechat',//管理端解绑票付通员工微信
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'member_center' => [
                    'host'              => '*********:9092,*********:9092,*********:9092',
                    'group_id'          => 'member_center_create_member',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'create_member_prod' => 'Member/CreateMemberNotice',//创建用户后通知
                        'member_staff_delete_event_pro' => 'Member/MemberStaffDelete', //中台删除员工
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_myuu_pft_order_track' => [
                    'host'              => '*********:9092,*********:9092,*********:9092',
                    'group_id'          => 'platform_myuu_pft_order_track',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        'es_myuu_pft_order_track' => 'Order/OrderTrackConsumer',//追踪记录
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_ticket_order_verify' => [
                    'host'              => '*********:9092,*********:9092,*********:9092',
                    'group_id'          => 'platform_ticket_order_verify',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        $ticketOrderVerifyTopic => 'Order/OrderVerifyConsumer',//追踪记录
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
                'platform_aliyun' => [
                    'host'              => 'alikafka-serverless-cn-w5g4av95403-1000-vpc.alikafka.aliyuncs.com:9092,alikafka-serverless-cn-w5g4av95403-2000-vpc.alikafka.aliyuncs.com:9092,alikafka-serverless-cn-w5g4av95403-3000-vpc.alikafka.aliyuncs.com:9092',
                    'group_id'          => \Library\Tools::isGrayEnv() ? 'productLineService_orderCancel_gray' : 'productLineService_orderCancel_prod',
                    'auto_offset_reset' => 'earliest',
                    'consumer'          => [
                        $orderCancelAsyncTopic => 'Order/OrderCancelAsyncConsumer', //订单取消异步任务
                    ],
                    'process_num'       => 1,//ps:开启多进程的话会重复消费
                ],
            ],
            'kafka_producer' => [
                'platform' => [
                    'host'              => '*********:9092,*********:9092,*********:9092',
                    'topic' => [
                        'order_status_topic'                   => 'order_status_topic',
                        'refund_examine_topic'                 => 'refund_examine_topic',
                        'shuMeiEventPushInput'                 => 'shuMeiEventPushInput',
                        'order_refund_topic'                   => 'order_refund_topic',
                        'time_ticket_change_topic'             => 'time_ticket_change_topic',
                        'batch_refund_notice_after_sale_topic' => $batchRefundNoticeAfterSaleTopic,
                        $equityCancelNotifyTopic               => $equityCancelNotifyTopic,
                        'transaction_verification_order'       => $transactionVerificationOrderTopic, //解冻处理
                        'open_upstream_ticketing_status'       => 'open_upstream_ticketing_status', //出票失败
                        'openMonitoringCenter_log'             => $openMonitoringCenterLogTopic, //开放监控中心日志
                        'openOtaPlatform_ticketBind'           => $openOtaPlatformTicketBindTopic, //ota门票绑定
                        'annual_card_operation_topic'          => 'annual_card_operation_topic', //年卡操作变更
                        'changed_examine_topic'                => 'changed_examine_topic', //下游改签审核通知
                        'cancelService_exchangeBaseTicketCancel' => $cancelServiceExchangeBaseTicketCancelTopic, // 预售券退票通知
                    ],
                ],
                'offline_ticket' => [
                    'host'             => '*********:9092,*********:9092,*********:9092',
                    'topic' => [
                        'platform_site_topic' => 'platform_site_mod',
                    ],
                ],
                'bot' => [
                    'host'  => '*********:9092,*********:9092,*********:9092',
                    'topic' => [
                        'feishu_bot_card' => 'feishu_bot_card',
                    ],
                ],
                'sub_merchant' => [
                    'host'  => '*********:9092,*********:9092,*********:9092',
                    'topic' => [
                        'sub_merchant_log' => 'sub_merchant_log_pro',
                    ],
                ],
                'platform_aliyun' => [
                    'host'              => 'alikafka-serverless-cn-w5g4av95403-1000-vpc.alikafka.aliyuncs.com:9092,alikafka-serverless-cn-w5g4av95403-2000-vpc.alikafka.aliyuncs.com:9092,alikafka-serverless-cn-w5g4av95403-3000-vpc.alikafka.aliyuncs.com:9092',
                    'topic' => [
                        'order_cancel_async_topic' => $orderCancelAsyncTopic, //订单取消异步任务
                    ],
                ],
            ],
        ];
        break;

    default:
        # code...
        break;
}

return $config;
