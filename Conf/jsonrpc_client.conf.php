<?php
/**
 * JSON-RPC客户端配置
 * <AUTHOR>
 * @date 2020-07-15
 */

if (ENV == 'LOCAL') {
    $rpcClientArr = [
        //外部码接口接口
        'external_code'            => [
            'api_url'  => 'http://app-center-internal.pft12301.local/external_code/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '75f6a23291ee1b7a',
            'timeout'  => 30,
        ],

        //云票务
        'pft_scenic_local_service' => [
            'api_url'  => 'http://rpc-scenic-internal.12301.local/',
            'system'   => 'pft_scenic_local_service',
            'auth-key' => 'e5b79bbdb797144a',
            'timeout'  => 30,
        ],

        //应用市场
        'market'                   => [
            'api_url'  => 'http://app-center-internal.pft12301.local/market/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '95c9d4b9fac3d61a',
            'timeout'  => 30,
        ],

        //年卡服务接口
        'annual_card'              => [
            'api_url'  => 'http://app-center-internal.pft12301.local/annual_card/jsonrpc/',
            'system'   => 'pft_annual_card',
            'auth-key' => 'ymc7bw7pzk6mvafz',
            'timeout'  => 30,
        ],
        //资源中心hyperf
        'resource_product'         => [
            'api_url'  => 'http://app-center-internal.pft12301.local/resource_hyperf/jsonrpc/',
            'system'   => 'pft_resource_hyperf',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 60,
        ],
        //资源中心hyperf
        'resource_pull_product'         => [
            'api_url'  => '192.168.20.161:30859',
            'system'   => 'pft_resource_hyperf',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 60,
        ],
        //上游系统对接
        'ota_supplier'             => [
            'api_url'  => 'http://ota-supplier-internal.12301.local/jsonrpc/',
            'system'   => 'pft_ota_supplier',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 60,
        ],
        //ota下游接口
        'ota_open'                 => [
            'api_url'  => 'http://ota-internal.12301.local/jsonrpc/',
            'system'   => 'pft_ota',
            'auth-key' => 'a13c1580136bec32',
            'timeout'  => 60,
        ],
        //上游系统api对接
        'ota_supplier_api'         => [
            'api_url'  => 'http://ota-supplier-api-internal.12301dev.com/jsonrpc/',
            'system'   => 'pft_ota_supplier_api',
            'auth-key' => 'd828162bfbcda351',
            'timeout'  => 60,
        ],
        'pft_app'                  => [
            'api_url'  => 'http://app-internal.pft12301.local/jsonrpc',
            'system'   => 'pft_platform',
            'auth-key' => 'e5b79bbdb797144a',
            'timeout'  => 30,

        ],

        //协议票服务
        'agreement_ticket'         => [
            'api_url'  => 'http://app-center-internal.pft12301.local/agreement_ticket/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '23c9d4b9fac5d61v',
            'timeout'  => 30,
        ],

        //授权服务接口
        'authorized'               => [
            'api_url'  => 'http://app-center-internal.pft12301.local/authorized/jsonrpc/',
            'system'   => 'pft_authorized',
            'auth-key' => '5se0v90y1lu5w8m0',
            'timeout'  => 30,
        ],
        //报表
        'statistics'               => [
            'api_url'  => 'http://app-center-internal.pft12301.local/statistics/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '95c9d4b9fac3d61a',
            'timeout'  => 30,
        ],

        //特殊团队预约
        'special_reservation'      => [
            'api_url'  => 'http://app-center-internal.pft12301.local/special_reservation/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '75f6a23291ee1b7a',
            'timeout'  => 30,
        ],

        //业务线统一jsonrpc服务
        'common_service'           => [
            'api_url'  => 'http://rpcapi.12301.local/jsonRpc.php',
            'system'   => 'platform_user',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 30,
        ],
        'common_service_router_proxy'           => [
            'api_url'  => 'http://router-internal.12301.test/rpcapi/jsonRpc.php',
            'system'   => 'platform_user',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 30,
        ],

        //电子发票服务接口
        'electronic_invoice'       => [
            'api_url'  => 'http://app-center-internal.pft12301.local/electronic_invoice/jsonrpc/',
            'system'   => 'electronic_invoice',
            'auth-key' => 'b31zgk3xtxi4jgxy',
            'timeout'  => 30,
        ],
        //返利服务接口
        'rebate'               => [
            'api_url'  => 'http://app-center-internal.pft12301.local/rebate/jsonrpc/',
            'system'   => 'pft_rebate',
            'auth-key' => 'i4l8aig0pdypi1w1',
            'timeout'  => 30,
        ],

        // 通知中心的
        'open_push_service'       => [
            'api_url'  => 'http://open-push-internal.12301.local',
            'system'   => 'pft_verify_center',
            'auth-key' => '762294f734883346e9ea2255653d422c',
            'timeout'  => 30,
        ],

        //请求开放平台的接口
        'open_platform_api'         => [
            'api_url'  => 'http://open-platform-internal.12301.local/jsonrpc/',
            'system'   => 'open_platform_api',
            'auth-key' => 'd828162bfbcda351',
            'timeout'  => 30,
        ],

        //业务线提供给开放平台的接口
        'platform_to_open'         => [
            'api_url'  => 'http://rpcapi.12301.local/jsonRpc.php/',
            'system'   => 'open_platform',
            'auth-key' => '4d87be4b59a755se',
            'timeout'  => 30,
        ],

        //营销短信服务
        'marketing_sms' => [
            'api_url'  => 'http://app-center-internal.pft12301.local/marketing_sms/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '23c9d4b9fac5d61v',
            'timeout'  => 30,
        ],
        //团队预约
        'team_appointment'      => [
            'api_url'  => 'http://app-center-internal.pft12301.local/appointment/jsonrpc/',
            'system'   => 'pft_team_appointment',
            'auth-key' => 'e54987f9b870fd1a',
            'timeout'  => 30,
        ],
        //演出服务接口
        'pft_show'              => [
            'api_url'  => 'http://app-center-internal.pft12301.local/pft_show/jsonrpc/',
            'system'   => 'pft_show',
            'auth-key' => 'hz95or5pcujzmjs9',
            'timeout'  => 60,
        ],
        //团购导码
        'export_code'              => [
            'api_url'  => 'http://app-center-internal.pft12301.local/export_code/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '8b1118ef605f50ad',
            'timeout'  => 60,
        ],
        'platform_pay_service' => [
            'api_url'  => 'http://rpc-pay-center.12301.local/jsonRpc.php',
            'system'   => 'platform_pay_service',
            'auth-key' => 'gt2JDr6CumHrAz0R',
            'timeout'  => 60,
        ],

        //=============旅游券==============
        'travel_voucher_service' => [
            'api_url'  => 'http://192.168.20.161:32352',
            'appid'    => '12301_platform',
            'secret'   => 'f05e2f69c80cb19df9fcab6aa74424e2542ef925',
            'timeout'  => 60,
        ],
        'sar_biz'         => [
            'api_url'  => 'sar-internal.12301.local:9502',
            'system'   => 'pft_platform_service',
            'auth-key' => 'KCBzAn1gmxfMioT8',
            'timeout'  => 60,
        ],
        //============新下载中心===========
        'dc-excel-task'              => [
//            'api_url'  => 'http://my.12301.local:8080/download-center-rpc/jsonrpc/excel-task',
            'api_url'  => 'http://download-internal.12301.test/download-center-rpc/jsonrpc/excel-task',
            'system'   => 'pft_platform',
            'auth-key' => '75f6a23291ee1b7a',
            'timeout'  => 30,
        ],

        //============年卡独立服务===========
        'annual_biz'              => [
            'api_url'  => 'http://annual-biz-internal.12301.local/jsonrpc.php',
            'system'   => 'pft_platform_service',
            'auth-key' => 'Dlkh4JRGpSH9TTWY',
            'timeout'  => 30,
        ],

        // 通知中心 json-rpc
        'notice_order_status'         => [
            'api_url'  => 'http://noticeorder-jrpc.12301dev.com:9080/',
            'system'   => 'pft_verify_center',
            'auth-key' => '762294f734883346e9ea2255653d422c',
            'timeout'  => 60,
        ],
        //消息中心
        'message_center'              => [
            'api_url'  => 'http://192.168.20.161:31178/',
            'system'   => 'pft_platform',
            'auth-key' => 'hDYeDRRLV4bKDQ8f',
            'timeout'  => 60,
        ],
        //ota调用的消息中心
        'ota_message_center'              => [
            'api_url'  => 'http://192.168.20.161:31178/',
            'system'   => 'ota_platform',
            'auth-key' => '60a52fa241a4321d4c45b1ae33afc427',
            'timeout'  => 60,
        ],
        //============演出服务===========
        'show_manage' => [
            'api_url'  => 'http://show-internal.12301.local/jsonrpc.php',
            'system'   => 'platform_user',
            'auth-key' => 'lb4musr0ttmv1cg2',
            'timeout'  => 60,
        ],
        // 开放平台上游
        'open-distribution-manage' => [
            'api_url' => 'http://open-distribution-manage-internal.12301dev.com:9080/',
            'api_port' => 9080,
        ],
        //新消息中心
        'message_service'            => [
            'api_url'  => 'http://message.12301.local/jsonrpc',
            'system'   => 'pft_platform',
            'auth-key' => 'pft_platform',
            'timeout'  => 30,
        ],
        'message_service_annual'            => [
            'api_url'  => 'http://message.12301.local/jsonrpc',
            'system'   => 'pft_annual_card',
            'auth-key' => 'pft_annual_card',
            'timeout'  => 30,
        ],
    ];
}
elseif (ENV == 'DEVELOP') {
    $rpcClientArr = [
        //外部码接口接口
        'external_code'    => [
            'api_url'  => 'http://app-center-internal.pft12301.test/external_code/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '75f6a23291ee1b7a',
            'timeout'  => 30,
        ],

        //应用市场
        'market'           => [
            'api_url'  => 'http://app-center-internal.pft12301.test/market/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '95c9d4b9fac3d61a',
            'timeout'  => 30,
        ],

        //年卡服务接口
        'annual_card'      => [
            'api_url'  => 'http://app-center-internal.pft12301.test/annual_card/jsonrpc/',
            'system'   => 'pft_annual_card',
            'auth-key' => 'b864u5bpqknl189b',
            'timeout'  => 30,
        ],

        //资源中心hyperf
        'resource_product' => [
            'api_url'  => 'http://app-center-internal.pft12301.test/resource_hyperf/jsonrpc/',
            'system'   => 'pft_resource_hyperf',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 60,
        ],

        //资源中心hyperf
        'resource_pull_product'         => [
            'api_url'  => '192.168.20.161:30859',
            'system'   => 'pft_resource_hyperf',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 60,
        ],

        //上游系统对接
        'ota_supplier'     => [
            'api_url'  => 'http://ota-supplier-internal.12301.test/jsonrpc/',
            'system'   => 'pft_ota_supplier',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 60,
        ],

        //ota下游接口
        'ota_open'         => [
            'api_url'  => 'http://ota-internal.12301.test/jsonrpc/',
            'system'   => 'pft_ota',
            'auth-key' => 'a13c1580136bec32',
            'timeout'  => 60,
        ],

        //上游系统api对接
        'ota_supplier_api' => [
            'api_url'  => 'http://ota-supplier-api-internal.12301dev.com/jsonrpc/',
            'system'   => 'pft_ota_supplier_api',
            'auth-key' => 'd828162bfbcda351',
            'timeout'  => 60,
        ],

        'pft_app'                  => [
            'api_url'  => 'http://app-internal.pft12301.test/jsonrpc',
            'system'   => 'pft_platform',
            'auth-key' => 'e5b79bbdb797144a',
            'timeout'  => 30,
        ],

        //云票务
        'pft_scenic_local_service' => [
            'api_url'  => 'http://rpc-scenic-internal.12301.test/',
            'system'   => 'pft_scenic_local_service',
            'auth-key' => 'e5b79bbdb797144a',
            'timeout'  => 30,
        ],
        //协议票服务
        'agreement_ticket'         => [
            'api_url'  => 'http://app-center-internal.pft12301.test/agreement_ticket/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '21c4d4b2fac3d64f',
            'timeout'  => 30,
        ],

        //授权服务接口
        'authorized'               => [
            'api_url'  => 'http://app-center-internal.pft12301.test/authorized/jsonrpc/',
            'system'   => 'pft_authorized',
            'auth-key' => 'uuhfvobjas3n4qkf',
            'timeout'  => 30,
        ],

        //报表
        'statistics'               => [
            'api_url'  => 'http://app-center-internal.pft12301.test/statistics/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '95c9d4b9fac3d61a',
            'timeout'  => 30,
        ],
        //特殊团队预约
        'special_reservation'      => [
            'api_url'  => 'http://app-center-internal.pft12301.test/special_reservation/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '75f6a23291ee1b7a',
            'timeout'  => 30,
        ],

        //业务线统一jsonrpc服务
        'common_service'           => [
            'api_url'  => 'http://rpcapi.12301.test/jsonRpc.php/',
            'system'   => 'platform_user',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 30,
        ],
        'common_service_router_proxy'           => [
            'api_url'  => 'http://router-internal.12301.test/rpcapi/jsonRpc.php',
            'system'   => 'platform_user',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 30,
        ],

        //电子发票服务接口
        'electronic_invoice'       => [
            'api_url'  => 'http://app-center-internal.pft12301.test/electronic_invoice/jsonrpc/',
            'system'   => 'electronic_invoice',
            'auth-key' => '4o1qkni3pmowpkjz',
            'timeout'  => 30,
        ],
        //返利服务接口
        'rebate'               => [
            'api_url'  => 'http://app-center-internal.pft12301.test/rebate/jsonrpc/',
            'system'   => 'pft_rebate',
            'auth-key' => 'hv8ng67wk1p3oauw',
            'timeout'  => 30,
        ],

        //请求开放平台的接口
        'open_platform_api'         => [
            'api_url'  => 'http://open-platform-internal.12301.test/jsonrpc/',
            'system'   => 'open_platform_api',
            'auth-key' => 'd828162bfbcda351',
            'timeout'  => 30,
        ],

        //业务线提供给开放平台的接口
        'platform_to_open'         => [
            'api_url'  => 'http://rpcapi.12301.test/jsonRpc.php/',
            'system'   => 'open_platform',
            'auth-key' => '4d87be4b59a755se',
            'timeout'  => 30,
        ],

        //营销短信服务
        'marketing_sms' => [
            'api_url'  => 'http://app-center-internal.pft12301.test/marketing_sms/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '21c4d4b2fac3d64f',
            'timeout'  => 30,
        ],
        //团队预约
        'team_appointment'      => [
            'api_url'  => 'http://app-center-internal.pft12301.test/appointment/jsonrpc/',
            'system'   => 'pft_team_appointment',
            'auth-key' => 'e54987f9b870fd1a',
            'timeout'  => 30,
        ],

        //演出服务接口
        'pft_show'              => [
            'api_url'  => 'http://app-center-internal.pft12301.test/pft_show/jsonrpc/',
            'system'   => 'pft_show',
            'auth-key' => '9t5al16fqc04qamr',
            'timeout'  => 30,
        ],
        //团购导码
        'export_code'              => [
            'api_url'  => 'http://app-center-internal.pft12301.test/export_code/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => 'f9651fb168155beb',
            'timeout'  => 60,
        ],
        'platform_pay_service' => [
            'api_url'  => 'http://rpc-pay-center.12301.test/jsonRpc.php',
            'system'   => 'platform_pay_service',
            'auth-key' => 'gt2JDr6CumHrAz0R',
            'timeout'  => 60,
        ],

        //=============旅游券==============
        'travel_voucher_service' => [
            'api_url'  => 'http://192.168.20.161:32352',
            'appid'    => '12301_platform',
            'secret'   => 'f05e2f69c80cb19df9fcab6aa74424e2542ef925',
            'timeout'  => 60,
        ],
        'sar_biz'         => [
            'api_url'  => 'http://rpc-sar170-internal.12301.test',
            'system'   => 'pft_platform_service',
            'auth-key' => 'KCBzAn1gmxfMioT8',
            'timeout'  => 60,
        ],
        //============新下载中心===========
        'dc-excel-task'              => [
            'api_url'  => 'http://download-internal.12301.test/download-center-rpc/jsonrpc/excel-task',
            'system'   => 'pft_platform',
            'auth-key' => '75f6a23291ee1b7a',
            'timeout'  => 30,
        ],
        //============年卡独立服务===========
        'annual_biz'              => [
            'api_url'  => 'http://annual-biz-internal.12301.test/jsonrpc.php',
            'system'   => 'pft_platform_service',
            'auth-key' => 'Dlkh4JRGpSH9TTWY',
            'timeout'  => 30,
        ],

        // 通知中心 json-rpc
        'notice_order_status'         => [
            'api_url'  => 'http://noticeorder-jrpc.12301dev.com:9080/',
            'system'   => 'pft_verify_center',
            'auth-key' => '762294f734883346e9ea2255653d422c',
            'timeout'  => 60,
        ],

        'message_center'              => [
            'api_url'  => 'http://192.168.20.161:31178/',
            'system'   => 'pft_platform',
            'auth-key' => 'hDYeDRRLV4bKDQ8f',
            'timeout'  => 60,
        ],

        //ota调用的消息中心
        'ota_message_center'              => [
            'api_url'  => 'http://192.168.20.161:31178/',
            'system'   => 'ota_platform',
            'auth-key' => '60a52fa241a4321d4c45b1ae33afc427',
            'timeout'  => 60,
        ],
        //============演出服务===========
        'show_manage' => [
            'api_url'  => 'http://show-internal.12301.test/jsonrpc.php',
            'system'   => 'platform_user',
            'auth-key' => 'lb4musr0ttmv1cg2',
            'timeout'  => 60,
        ],
        // 开放平台上游
        'open-distribution-manage' => [
            'api_url' => 'http://open-distribution-manage-internal.12301dev.com:9080/',
            'api_port' => 9080,
        ],
        'message_service'            => [
            'api_url'  => 'http://message.12301.test/jsonrpc',
            'system'   => 'pft_platform',
            'auth-key' => 'pft_platform',
            'timeout'  => 30,
        ],
        'message_service_annual'            => [
            'api_url'  => 'http://message.12301.test/jsonrpc',
            'system'   => 'pft_annual_card',
            'auth-key' => 'pft_annual_card',
            'timeout'  => 30,
        ],
    ];
}
elseif (ENV == 'TEST') {
    $rpcClientArr = [
        //外部码接口接口
        'external_code'    => [
            'api_url'  => 'http://app-center-internal.12301dev.com/external_code/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '10e74539c05eca4e',
            'timeout'  => 30,
        ],

        //应用市场
        'market'           => [
            'api_url'  => 'http://app-center-internal.12301dev.com/market/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '9a69d9a26dbbe2d0',
            'timeout'  => 30,
        ],

        //年卡服务接口
        'annual_card'      => [
            'api_url'  => 'http://app-center-internal.12301dev.com/annual_card/jsonrpc/',
            'system'   => 'pft_annual_card',
            'auth-key' => 'rcbdnf581aahyyei',
            'timeout'  => 30,
        ],

        //资源中心hyperf
        'resource_product' => [
            'api_url'  => 'http://app-center-internal.12301dev.com/resource_hyperf/jsonrpc/',
            'system'   => 'pft_resource_hyperf',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 60,
        ],

        //资源中心hyperf
        'resource_pull_product'         => [
            'api_url'  => '10.53.0.14:9506',
            'system'   => 'pft_resource_hyperf',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 60,
        ],

        //上游系统对接
        'ota_supplier'     => [
            'api_url'  => 'http://ota-supplier-internal.12301dev.com/jsonrpc/',
            'system'   => 'pft_ota_supplier',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 60,
        ],

        //ota下游接口
        'ota_open'         => [
            'api_url'  => 'http://ota-internal.12301dev.com/jsonrpc/',
            'system'   => 'pft_ota',
            'auth-key' => 'a13c1580136bec32',
            'timeout'  => 60,
        ],

        //上游系统api对接
        'ota_supplier_api' => [
            'api_url'  => 'http://ota-supplier-api-internal.12301dev.com/jsonrpc/',
            'system'   => 'pft_ota_supplier_api',
            'auth-key' => 'd828162bfbcda351',
            'timeout'  => 60,
        ],

        'pft_app'                  => [
            'api_url'  => 'http://app-internal.12301dev.com/jsonrpc',
            'system'   => 'pft_platform',
            'auth-key' => 'e5b79bbdb797144a',
            'timeout'  => 30,
        ],

        //云票务
        'pft_scenic_local_service' => [
            'api_url'  => 'http://rpc-scenic-internal.12301dev.com/',
            'system'   => 'pft_scenic_local_service',
            'auth-key' => 'e5b79bbdb797144a',
            'timeout'  => 30,
        ],
        //协议票服务
        'agreement_ticket'         => [
            'api_url'  => 'http://app-center-internal.12301dev.com/agreement_ticket/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '3a21d9a26dbbe2k5',
            'timeout'  => 30,
        ],

        //授权服务接口
        'authorized'               => [
            'api_url'  => 'http://app-center-internal.12301dev.com/authorized/jsonrpc/',
            'system'   => 'pft_authorized',
            'auth-key' => 'hqufegsinr701faa',
            'timeout'  => 30,
        ],

        //报表
        'statistics'               => [
            'api_url'  => 'http://app-center-internal.12301dev.com/statistics/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '9a69d9a26dbbe2d0',
            'timeout'  => 30,
        ],
        //特殊团队预约
        'special_reservation'      => [
            'api_url'  => 'http://app-center-internal.12301dev.com/special_reservation/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '10e74539c05eca4e',
            'timeout'  => 30,
        ],

        //业务线统一jsonrpc服务
        'common_service'           => [
            'api_url'  => 'http://rpcapi.12301dev.com/jsonRpc.php',
            'system'   => 'platform_user',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 30,
        ],
        'common_service_router_proxy'           => [
            'api_url'  => 'http://router-internal.12301dev.com:9080/rpcapi/jsonRpc.php',
            'system'   => 'platform_user',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 30,
        ],

        //电子发票服务接口
        'electronic_invoice'       => [
            'api_url'  => 'http://app-center-internal.12301dev.com/electronic_invoice/jsonrpc/',
            'system'   => 'electronic_invoice',
            'auth-key' => '528l1eut7gjbelpb',
            'timeout'  => 30,
        ],
        //返利服务接口
        'rebate'               => [
            'api_url'  => 'http://app-center-internal.12301dev.com/rebate/jsonrpc/',
            'system'   => 'pft_rebate',
            'auth-key' => 'qr5wboohl656vzmg',
            'timeout'  => 30,
        ],

        // 通知中心的
        'open_push_service'       => [
            'api_url'  => 'http://open-push-internal.12301dev.com',
            'system'   => 'pft_verify_center',
            'auth-key' => '762294f734883346e9ea2255653d422c',
            'timeout'  => 30,
        ],

        //请求开放平台的接口
        'open_platform_api'         => [
            'api_url'  => 'http://open-platform-internal.12301dev.com/jsonrpc/',
            'system'   => 'open_platform_api',
            'auth-key' => 'd828162bfbcda351',
            'timeout'  => 30,
        ],

        //业务线提供给开放平台的接口
        'platform_to_open'         => [
            'api_url'  => 'http://rpcapi.12301dev.com/jsonRpc.php',
            'system'   => 'open_platform',
            'auth-key' => '4d87be4b59a755se',
            'timeout'  => 30,
        ],


        //营销短信服务
        'marketing_sms' => [
            'api_url'  => 'http://app-center-internal.12301dev.com/marketing_sms/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '3a21d9a26dbbe2k5',
            'timeout'  => 30,
        ],

        //团队预约
        'team_appointment'      => [
            'api_url'  => 'http://app-center-internal.12301dev.com/appointment/jsonrpc/',
            'system'   => 'pft_team_appointment',
            'auth-key' => 'e54987f9b870fd1a',
            'timeout'  => 30,
        ],

        //演出服务接口
        'pft_show'              => [
            'api_url'  => 'http://app-center-internal.12301dev.com/pft_show/jsonrpc/',
            'system'   => 'pft_show',
            'auth-key' => 'xrdw0hn7by1b79dr',
            'timeout'  => 30,
        ],
        //团购导码
        'export_code'              => [
            'api_url'  => 'http://app-center-internal.12301dev.com/export_code/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => 'cbee17bf8b8dee9f',
            'timeout'  => 60,
        ],
        'platform_pay_service' => [
            'api_url'  => 'http://rpc-pay-center.12301dev.com/jsonRpc.php',
            'system'   => 'platform_pay_service',
            'auth-key' => 'gt2JDr6CumHrAz0R',
            'timeout'  => 60,
        ],

        //=============旅游券==============
        'travel_voucher_service' => [
            'api_url'  => 'http://voucher-jrpc.12301dev.com:9080/',
            'appid'    => '12301_platform',
            'secret'   => 'f05e2f69c80cb19df9fcab6aa74424e2542ef925',
            'timeout'  => 60,
        ],
        'sar_biz'         => [
            'api_url'  => 'http://rpc-sar-internal.12301dev.com:9080',
            'system'   => 'pft_platform_service',
            'auth-key' => 'KCBzAn1gmxfMioT8',
            'timeout'  => 60,
        ],
        //============新下载中心===========
        'dc-excel-task'              => [
            'api_url'  => 'http://download-internal.12301dev.com:9080/download-center-rpc/jsonrpc/excel-task',
            'system'   => 'pft_platform',
            'auth-key' => '75f6a23291ee1b7a',
            'timeout'  => 30,
        ],
        //============年卡独立服务===========
        'annual_biz'              => [
            'api_url'  => 'http://annual-biz-internal.12301dev.com/jsonrpc.php',
            'system'   => 'pft_platform_service',
            'auth-key' => '2YD0bNcHLQqxiYMZ',
            'timeout'  => 30,
        ],

        // 通知中心 json-rpc
        'notice_order_status'         => [
            'api_url'  => 'http://noticeorder-jrpc.12301dev.com:9080/',
            'system'   => 'pft_verify_center',
            'auth-key' => '762294f734883346e9ea2255653d422c',
            'timeout'  => 60,
        ],

        //消息中心
        'message_center'              => [
            'api_url'  => 'http://47.96.73.176:19502/',
            'system'   => 'pft_platform',
            'auth-key' => 'hDYeDRRLV4bKDQ8f',
            'timeout'  => 60,
        ],

        //ota调用的消息中心
        'ota_message_center'          => [
            'api_url'  => 'http://47.96.73.176:19502/',
            'system'   => 'ota_platform',
            'auth-key' => '60a52fa241a4321d4c45b1ae33afc427',
            'timeout'  => 60,
        ],
        //============演出服务===========
        'show_manage' => [
            'api_url'  => 'http://show-internal.12301dev.com/jsonrpc.php',
            'system'   => 'platform_user',
            'auth-key' => '8av9i4bwyz3h8hwf',
            'timeout'  => 60,
        ],
        // 开放平台上游
        'open-distribution-manage' => [
            'api_url' => 'http://open-distribution-manage-internal.12301dev.com:9080/',
            'api_port' => 9080,
        ],
        'message_service'            => [
            'api_url'  => 'http://message.12301dev.com:9080/jsonrpc',
            'system'   => 'pft_platform',
            'auth-key' => 'pft_platform',
            'timeout'  => 30,
        ],
        'message_service_annual' => [
            'api_url'  => 'http://message.12301dev.com:9080/jsonrpc',
            'system'   => 'pft_annual_card',
            'auth-key' => 'pft_annual_card',
            'timeout'  => 30,
        ],
    ];
}
elseif (ENV == 'PRODUCTION') {
    //灰度判断处理
    if (defined('IS_PFT_GRAY')) {
        $apiUrl           = 'http://app-center-internal-gary.pft12301.com';
        $otaUrl           = 'http://ota-internal.gray.pft12301.com/jsonrpc/';
        $otaSupplierUrl   = 'http://ota-supplier-internal.gray.pft12301.com/jsonrpc/';
        $annualUrl        = 'http://app-center-internal-gary.pft12301.com/annual_card/jsonrpc/';
        $scenicUrl        = 'http://rpc-scenic-internal.gray.pft12301.com/';
        $authorUrl        = 'http://app-center-internal-gary.pft12301.com/authorized/jsonrpc/';
        $commonServiceUrl = 'http://rpcapi.gray.12301.cc/jsonRpc.php';
        $commonServiceRouterProxyUrl = 'http://router-internal-gray.pft12301.com/gray/rpcapi/jsonRpc.php';
        $invoiceUrl       = 'http://app-center-internal-gary.pft12301.com/electronic_invoice/jsonrpc/';
        $openPushUrl      = 'http://open-push-internal.gray.pft12301.com';
        $otaSupplierApiUrl = 'http://ota-supplier-api-internal.gray.pft12301.com/jsonrpc/';
        $rebateUrl        = 'http://app-center-internal-gary.pft12301.com/rebate/jsonrpc/';
        $teamAppointment  = 'http://app-center-internal-gary.pft12301.com/appointment/jsonrpc/';
        $pftShowApiUrl    = 'http://app-center-internal-gary.pft12301.com/pft_show/jsonrpc/';
        $platformToOpen   = 'http://open-platform-internal.gray.pft12301.com/jsonrpc/';
        $resourcePullProductUrl = '10.1.8.213:9506';
        $voucherUrl = 'https://voucher-gray-jrpc.12301.cc/';
        //$annualBizUrl = 'http://annual-biz-internal.12301gray.com/jsonrpc.php';
        $annualBizUrl = 'http://annual-biz-internal.gray.pft12301.com/jsonrpc.php';
        $noticeOrderStatus = 'http://noticeorder-jrpc.pft12301.com/';
        $messageCenter    = 'http://message-center.12301.cc/';
        $payCenter        = 'http://rpc-pay-center.gray.pft12301.com/jsonRpc.php';
        $downloadCenter   = 'http://download-internal-gray.pft12301.com/download-center-rpc/jsonrpc/excel-task';
        $showManageApiUrl = 'http://show-internal.gray.12301.cc/jsonrpc.php';
        $messageServiceApiUrl = 'http://message-gray.pft12301.com/jsonrpc';
        $openDistributionManageApiUrl = 'http://open-distribution-manage-internal-gray.pft12301.com/';
    } else {
        $apiUrl           = 'http://app-center-internal.pft12301.com';
        $otaUrl           = 'http://ota-internal.pft12301.com/jsonrpc/';
        $otaSupplierUrl   = 'http://ota-supplier-internal.pft12301.com/jsonrpc/';
        $annualUrl        = 'http://app-center-internal.pft12301.com/annual_card/jsonrpc/';
        $scenicUrl        = 'http://rpc-scenic-internal.pft12301.com/';
        $authorUrl        = 'http://app-center-internal.pft12301.com/authorized/jsonrpc/';
        $commonServiceUrl = 'http://rpcapi.pft12301.com/jsonRpc.php';
        $commonServiceRouterProxyUrl = 'http://router-internal.pft12301.com/rpcapi/jsonRpc.php';
        $invoiceUrl       = 'http://app-center-internal.pft12301.com/electronic_invoice/jsonrpc/';
        $openPushUrl      = 'http://open-push-internal.pft12301.com';
        $otaSupplierApiUrl = 'http://ota-supplier-api-internal.pft12301.com/jsonrpc/';
        $rebateUrl        = 'http://app-center-internal.pft12301.com/rebate/jsonrpc/';
        $teamAppointment  = 'http://app-center-internal.pft12301.com/appointment/jsonrpc/';
        $pftShowApiUrl    = 'http://app-center-internal.pft12301.com/pft_show/jsonrpc/';
        $platformToOpen   = 'http://open-platform-internal.pft12301.com/jsonrpc/';
        $resourcePullProductUrl = '10.1.8.213:9506';
        $voucherUrl = 'https://voucher-jrpc.12301.cc/';
        $annualBizUrl = 'http://annual-biz-internal.pft12301.com/jsonrpc.php';
        $noticeOrderStatus = 'http://noticeorder-jrpc.pft12301.com/';
        $messageCenter    = 'http://message-center.12301.cc/';
        $payCenter        = 'http://rpc-pay-center.pft12301.com/jsonRpc.php';
        $downloadCenter   = 'http://download-internal.pft12301.com/download-center-rpc/jsonrpc/excel-task';
        $showManageApiUrl = 'http://show-internal.pft12301.com/jsonrpc.php';
        $messageServiceApiUrl = 'http://message.pft12301.com/jsonrpc';
        $openDistributionManageApiUrl = 'http://open-distribution-manage-internal.pft12301.com/';
    }

    $rpcClientArr = [
        //外部码接口接口
        'external_code'    => [
            'api_url'  => $apiUrl . '/external_code/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 30,
        ],

        //应用市场
        'market'           => [
            'api_url'  => $apiUrl . '/market/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '59f5165e973c58f3',
            'timeout'  => 30,
        ],

        //年卡服务接口
        'annual_card'      => [
            'api_url'  => $annualUrl,
            'system'   => 'pft_annual_card',
            'auth-key' => 'xext0c3ysfysgbmg',
            'timeout'  => 30,
        ],

        //资源中心hyperf
        'resource_product' => [
            'api_url'  => 'http://app-center-internal.pft12301.com/resource_hyperf/jsonrpc/',
            'system'   => 'pft_resource_hyperf',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 60,
        ],

        //资源中心hyperf
        'resource_pull_product'         => [
            'api_url'  => $resourcePullProductUrl,
            'system'   => 'pft_resource_hyperf',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 60,
        ],

        //上游系统对接
        'ota_supplier'     => [
            // 'api_url'  => 'http://ota-supplier-internal.pft12301.com/jsonrpc/',
            'api_url'  => $otaSupplierUrl,
            'system'   => 'pft_ota_supplier',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 60,
        ],

        //ota下游接口
        'ota_open'         => [
            'api_url'  => $otaUrl,
            'system'   => 'pft_ota',
            'auth-key' => 'a13c1580136bec32',
            'timeout'  => 60,
        ],

        //上游系统api对接
        'ota_supplier_api' => [
            'api_url'  => $otaSupplierApiUrl,
            'system'   => 'pft_ota_supplier_api',
            'auth-key' => 'd828162bfbcda351',
            'timeout'  => 60,
        ],

        'pft_app'                  => [
            'api_url'  => 'http://app-internal.pft12301.cc/jsonrpc',
            'system'   => 'pft_platform',
            'auth-key' => 'e5b79bbdb797144a',
            'timeout'  => 30,
        ],

        //云票务
        'pft_scenic_local_service' => [
            'api_url'  => $scenicUrl,
            'system'   => 'pft_scenic_local_service',
            'auth-key' => 'e5b79bbdb797144a',
            'timeout'  => 30,
        ],
        //协议票服务
        'agreement_ticket'         => [
            'api_url'  => $apiUrl . '/agreement_ticket/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '67f5165e973d98f4',
            'timeout'  => 30,
        ],

        //授权服务接口
        'authorized'               => [
            'api_url'  => $authorUrl,
            'system'   => 'pft_authorized',
            'auth-key' => 'iva2ulohk9rkog1p',
            'timeout'  => 30,
        ],

        //报表
        'statistics'               => [
            'api_url'  => $apiUrl . '/statistics/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '59f5165e973c58f3',
            'timeout'  => 30,
        ],
        //特殊团队预约
        'special_reservation'      => [
            'api_url'  => $apiUrl . '/special_reservation/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 30,
        ],

        //业务线统一jsonrpc服务
        'common_service'           => [
            'api_url'  => $commonServiceUrl,
            'system'   => 'platform_user',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 30,
        ],
        'common_service_router_proxy'           => [
            'api_url'  => $commonServiceRouterProxyUrl,
            'system'   => 'platform_user',
            'auth-key' => 'd828162bfbcda359',
            'timeout'  => 30,
        ],

        //电子发票服务接口
        'electronic_invoice'       => [
            'api_url'  => $invoiceUrl,
            'system'   => 'electronic_invoice',
            'auth-key' => 'ep5tltd2ekk9bu07',
            'timeout'  => 30,
        ],
        //返利服务接口
        'rebate'               => [
            'api_url'  => $rebateUrl,
            'system'   => 'pft_rebate',
            'auth-key' => '8t8xpgvyd6u6mry5',
            'timeout'  => 30,
        ],

        //请求开放平台的接口
        'open_platform_api'         => [
            'api_url'  => $platformToOpen,
            'system'   => 'open_platform_api',
            'auth-key' => 'd828162bfbcda351',
            'timeout'  => 30,
        ],

        //业务线提供给开放平台的接口
        'platform_to_open'         => [
            'api_url'  => $commonServiceUrl,
            'system'   => 'open_platform',
            'auth-key' => '6ee08e5c2b50b61b',
            'timeout'  => 30,
        ],

        //营销短信服务
        'marketing_sms' => [
            'api_url'  => $apiUrl . '/marketing_sms/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '67f5165e973d98f4',
            'timeout'  => 30,
        ],

        // 通知中心的
        'open_push_service'       => [
            'api_url'  => $openPushUrl,
            'system'   => 'pft_verify_center',
            'auth-key' => '762294f734883346e9ea2255653d422c',
            'timeout'  => 30,
        ],

        'team_appointment'      => [
            'api_url'  => $teamAppointment,
            'system'   => 'pft_team_appointment',
            'auth-key' => 'e54987f9b870fd1a',
            'timeout'  => 30,
        ],

        //演出服务接口
        'pft_show'              => [
            'api_url'  => $pftShowApiUrl,
            'system'   => 'pft_show',
            'auth-key' => 'lv03q2dldncqtdh2',
            'timeout'  => 30,
        ],

        //团购导码
        'export_code'              => [
            'api_url'  => $apiUrl . '/export_code/jsonrpc/',
            'system'   => 'pft_platform',
            'auth-key' => '8f01d21fd11e255f',
            'timeout'  => 60,
        ],
        'platform_pay_service' => [
            'api_url'  => $payCenter,
            'system'   => 'platform_pay_service',
            'auth-key' => 'EkqdvXTlHIy2fnHq',
            'timeout'  => 180,
        ],

        //=============旅游券==============
        'travel_voucher_service' => [
            'api_url'  => $voucherUrl,
            'appid'    => '12301_platform',
            'secret'   => 'f05e2f69c80cb19df9fcab6aa74424e2542ef925',
            'timeout'  => 60,
        ],
        //分账应用
        'sar_biz'         => [
            'api_url'  => 'http://rpc-sar-internal.pft12301.com',
            'system'   => 'pft_platform_service',
            'auth-key' => 'KCBzAn1gmxfMioT8',
            'timeout'  => 60,
        ],
        //============年卡独立服务===========
        'annual_biz'              => [
            'api_url'  => $annualBizUrl,
            'system'   => 'pft_platform_service',
            'auth-key' => 'ytFG9PsYB7WdrU3J',
            'timeout'  => 30,
        ],

        // 通知中心 json-rpc
        'notice_order_status'         => [
            'api_url'  => $noticeOrderStatus,
            'system'   => 'pft_verify_center',
            'auth-key' => '762294f734883346e9ea2255653d422c',
            'timeout'  => 60,
        ],

        //消息中心
        'message_center'              => [
            'api_url'  => $messageCenter,
            'system'   => 'pft_platform',
            'auth-key' => '2Qaz6VwPpR8WMYjJ',
            'timeout'  => 60,
        ],

        //ota调用的消息中心
        'ota_message_center'          => [
            'api_url'  => $messageCenter,
            'system'   => 'ota_platform',
            'auth-key' => '60a52fa241a4321d4c45b1ae33afc427',
            'timeout'  => 60,
        ],
        //============新下载中心===========
        'dc-excel-task'              => [
            'api_url'  => $downloadCenter,
            'system'   => 'pft_platform',
            'auth-key' => '75f6a23291ee1b7a',
            'timeout'  => 30,
        ],
        //============演出服务===========
        'show_manage' => [
            'api_url'  => $showManageApiUrl,
            'system'   => 'platform_user',
            'auth-key' => 'mzdftjros5hs5xxi',
            'timeout'  => 60,
        ],
        // 开放平台上游
        'open-distribution-manage' => [
            'api_url' => $openDistributionManageApiUrl,
            'api_port' => 80,
        ],
        // 新消息中心
        'message_service'            => [
            'api_url'  => $messageServiceApiUrl,
            'system'   => 'pft_platform',
            'auth-key' => 'pft_platform',
            'timeout'  => 30,
        ],
        // 新消息中心(年卡)
        'message_service_annual'            => [
            'api_url'  => $messageServiceApiUrl,
            'system'   => 'pft_annual_card',
            'auth-key' => 'pft_annual_card',
            'timeout'  => 30,
        ],
    ];
}

return $rpcClientArr;
