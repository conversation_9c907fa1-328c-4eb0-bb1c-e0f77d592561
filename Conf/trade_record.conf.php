<?php
/**
 * User: Fang
 * Time: 16:49 2016/6/3
 */

use Library\Constants\Account\BookSubject;
use Library\Constants\Account\TemplateItem;

return array(
    //交易渠道
    'order_channel' => [
        0  => '平台-分销商下单',
        1  => 'PC支付',
        2  => '手机支付',
        9  => '会员卡购票',
        10 => '云票务',
        11 => '微信商城',
        12 => '自助机',
        13 => '二级店铺',
        14 => '闸机购票',
        15 => '智能终端',
        16 => '计调下单',
        33 => 'e福州'
    ],
    //支付类型 第一列: 对应account_type的键值; 第二列: 支付方式描述; 第三列：所属支付大类
    'pay_type'      => [
        0  => [0, '账户资金', 1],
        1  => [1, '支付宝', 0],
        2  => [2, '授信', 2],
        3  => [2, '信用额度', 2],
        4  => [3, '微信', 0],
        5  => [4, '银联', 0],
        6  => [5, '环迅', 0],
        9  => [7, '现金', 3],
        11 => [6, '商户拉卡拉', 0],
        12 => [6, '平台拉卡拉', 0],
        13 => [6, '微信', 0],//民生“乐收银”-微信
        14 => [6, '平安银行收款', 0],
        15 => [15, '优惠券', 0],
        16 => [6, '支付宝', 0],//民生“乐收银”-支付宝
        25 => [25, '招行一网通支付', 0],//招行一网通支付
        26 => [26,'银联商务支付',0], //银联商务支付
        27 => [27,'银联商务POS支付',0], //银联商务支付
        28 => [28,'威富通',0],//农商威富通
        29 => [29,'易宝云企付',0], //银联商务支付
        30 => [30,'云闪付支付',0], //银联云闪付
        31 => [31,'农行',0], //农行
        32 => [32,'建行',0], //建行
        33 => [33, '博思支付', 0], //博思支付
        34 => [34, '积分账户', 0], // 积分支付，所属支付大类，没用到，先为0
        35 => [35, '账户押金', 0], // 押金支付，所属支付大类，没用到，先为0
        36 => [36, 'pos支付', 2], // 押金支付，所属支付大类，没用到，先为0
        37 => [37, '预存余额', 0], //预存余额
        38 => [2, '供应商授信', 2],
        39 => [39, '供应商预存余额', 0],
        40 => [40, '供应商账户押金', 0],
        41 => [41, '供应商积分', 0],
        42 => [42, '供应商信用额度', 2],
        43 => [43,'充值卡',0],
        44 => [44,'供应商充值卡',0],
        45 => [45,'丰收互联',0],
        47 => [47,'江西农商行',0],
        48 => [48,'银联会员卡',0],
        49 => [49,'盛世通支付',0],
        50 => [50,'玩聚',0],
        51 => [51,'青海银行',0],
        52 => [52,'玩聚-抖音担保交易支付',0],
        53 => [53,'招行聚合支付',0],
        54 => [54,'百度聚合支付',0],
        55 => [55,'线下手工补单',0],
        56 => [56,'中国银行',0],
        57 => [57,'易宝独立收款',0],
        58 => [58,'工商银行',0],
        59 => [59,'预付卡',0],
        60 => [60,'农行(云BMP)',0],
        61 => [61,'预付卡',0],
        62 => [62,'安徽农商行',0],
        63 => [63,'易宝平台收款',0],
        64 => [64,'绿聚支付',0],
        67 => [67,'福建农信',0],
        68 => [68,'聚富通',0],
        69 => [69,'农行(创识)',0],
        70 => [70,'通联支付',0],
        71 => [71,'富友支付',0],
        72 => [72,'酷享支付',0],
        73 => [73,'自贡银行',0],
        74 => [74, '预售券权益支付', 0],
        75 => [75, '慧徕店支付', 0],
        76 => [76, '预存码权益支付', 0],
        77 => [77, '星沙农商', 0],
        78 => [78, '网联商务', 0],
        79 => [79, '金华银行', 0],
        80 => [80, '悦航支付', 0],
        81 => [81, '拉卡拉支付', 0],
        82 => [82, '茶卡自有支付', 0],
        83 => [83, 'TLinx', 0],
        84 => [84, '线下收款1', 0],
        85 => [85, '线下收款2', 0],
        86 => [86, '线下收款3', 0],
        87 => [87, '线下收款4', 0],
        88 => [88, '线下收款5', 0],
        89 => [89, '线下收款6', 0],
        90 => [90, '线下收款7', 0],
        91 => [91, '线下收款8', 0],
        92 => [92, '线下收款9', 0],
        93 => [93, '线下收款10', 0],
        94 => [94, '通联支付', 0],
        95 => [95, '小红书-担保支付', 0],
        96 => [96, '西安银行', 0],
        97 => [97, '富滇银行', 0],
        98 => [98, '浙江农信', 0],
    ],
    //账户类型  添加此处时请注意一起添加本页面下的$excel_account_trans数据  这是交易汇总排列顺序用的
    'account_type'  => [
        -1 => '交易账户未定义',
        0  => '余额支付',
        1  => '支付宝',
        2  => '信用账户',
        3  => '信用账户额度设置',
        4  => '财付通-微信',
        5  => '银联',
        6  => '环迅',
        9  => '现金',
        11 => '商户拉卡拉',
        12 => '平台拉卡拉',
        13 => '微信(招商聚合)',//民生微信
        14 => '平安银行收款',
        15 => '优惠券',
        16 => '支付宝(招商聚合)',//民生支付宝
        17 => '线下打款',
        19 => '短信专用账户',
        20 => '电子凭证费专用账户',
        25 => '招行一网通',
        26 => '银联商务支付',
        27 => '银联商务POS支付',
        28 => '威富通',
        29 => '易宝云企付',
        30 => '银联云闪付',
        31 => '农行',
        32 => '建行',
        33 => '博思支付',
        34 => '积分账户',
        35 => '账户押金',
        36 => 'POS支付',
        37 => '预存余额',
        38 => '供应商信用账户',
        43 => '充值卡',
        44 => '供应商充值卡',
        45 => '丰收互联',
        47 => '江西农商行',
        48 => '银联会员卡',
        49 => '盛世通支付',
        50 => '玩聚',
        51 => '青海银行',
        52 => '玩聚-抖音担保交易支付',
        53 => '招行聚合支付',
        54 => '百度聚合支付',
        55 => '线下手工补单',
        56 => '中国银行',
        57 => '易宝独立收款',
        58 => '工商银行',
        59 => '预付卡',
        60 => '农行(云BMP)',
        61 => '预付卡',
        62 => '安徽农商行',
        63 => '易宝平台收款',
        64 => '绿聚支付',
        65 => '专项电子凭证费购买',
        66 => '专项短信费购买',
        67 => '福建农信',
        68 => '聚富通',
        69 => '农行(创识)',
        70 => '通联支付',
        71 => '富友支付',
        72 => '酷享支付',
        73 => '自贡银行',
        74 => '预售券',
        75 => '慧徕店支付',
        76 => '预存码',
        77 => '星沙农商',
        78 => '网联商务',
        79 => '金华银行',
        80 => '悦航支付',
        81 => '拉卡拉支付',
        82 => '茶卡自有支付',
        83 => 'TLinx',
//        84 => '线下收款1',
//        85 => '线下收款2',
//        86 => '线下收款3',
//        87 => '线下收款4',
//        88 => '线下收款5',
//        89 => '线下收款6',
//        90 => '线下收款7',
//        91 => '线下收款8',
//        92 => '线下收款9',
//        93 => '线下收款10',
        94 => '通联支付',
        95 => '小红书-担保支付',
        96 => '西安银行',
        97 => '富滇银行',
        98 => '浙江农信',
    ],

    'micro_account_type' => [
        0   => '未结票款',
        1   => '支付宝',
        2   => '供应商信用账户',
        4   => '微信',
        6   => '环迅',
        9   => '现金',
        17  => '线下打款',
        99  => '分销商信用账户',
        98  => '在线支付',
    ],

    //交易类型分类 添加此处时请注意一起添加本页面下的$excel_category_trans数据  这是交易汇总排列顺序用的
    'item_category' => [
        0  => [2, '购买产品',],
        1  => [2, '修改/取消订单',],
        2  => [1, '未定义操作',],
        3  => [3, '充值/扣款',],
        4  => [3, '线下还款',],
        5  => [4, '产品利润',],
        6  => [3, '提现冻结',],
        7  => [1, '电子凭证费',],
        8  => [1, '短信息费',],
        9  => [1, '提现交易手续费',],
        97 => [1, '小红书交易佣金'],
        //10 => [1,'空'],
        11 => [3, '供应商信用额度变化',],
        12 => [3, '取消提现',],
        13 => [3, '拒绝提现',],
        14 => [2, '退款手续费',],
        15 => [2, '押金',],
        16 => [2, '充值返现',],
        17 => [2, '撤销/撤改订单',],
        18 => [3, '转账',],
        19 => [4, '佣金发放',],
        20 => [4, '佣金提现',],
        21 => [4, '获得佣金',],
        22 => [1, '平台费',],
        23 => [2, '出售产品',],
        24 => [3, '线下充值',],
        25 => [3, '银行卡验证费'],
        26 => [1, '平台套餐续费'],
        27 => [3, '还款'],
        28 => [3, '一卡通充值'],
        29 => [3, '一卡通提现'],
		30 => [3, '终端收款'],
        31 => [2, '增加票数'],
        32 => [1, '应用模块购买'],
        33 => [2, '停车费'],
        34 => [2, '返利'],
        35 => [3, '会员卡充值'],
        36 => [3, '会员卡提现'],
        37 => [3, '签到送积分'],
        38 => [3, '消费送积分'],
        39 => [3, '积分兑换商品'],
        40 => [3, '年卡补卡'],
        41 => [3, '押金充值'],
        42 => [3, '押金退款'],
        43 => [1, '预存短信费'],
        44 => [1, '预存凭证费'],
        45 => [2, 'POS支付'],
        46 => [3, '预存'],
        47 => [3, '工本费'],
        66 => [1, '专项短信费购买'],
        65 => [1, '专项电子凭证费购买'],
        77 => [3, '买单赠送'],
        78 => [3, '刷卡赠送'],
        99 => [3, '终端退款'],
        100 => [3, '商家取消赠送'],
        101 => [3, '买单赠送取消'],
        120  => [2, '购买码费',],
    ],
    //交易大类
    'trade_item'    => [
        1 => '平台运营',
        2 => '产品交易',
        3 => '账户操作',
        4 => '佣金利润',
    ],

    //excel表头
    'excel_head_new' => [
        'rectime'      => '交易时间',
        'orderid'      => '业务订单号',
        'orderstatus'  => '订单状态',
        'trade_no'     => '第三方流水号',
        'trade_id'     => '交易流水号',
        'cmb_id'       => '交易订单号',
        'pay_id'       => '支付流水号',
        'com_name'     => '企业名称',
        'member_id'    => '用户ID',
        'account_name' => '账户名称',
        'to_member_id' => '对方商户ID',
        'to_merchant'  => '对方商户',
        'to_account'   => '对方账户',
        'fee_type'     => '费用类型',
        'pro_name'     => '产品名称',
        'subject_book' => '账本类型',
        'money'        => '收支金额',
        'balance'      => '余额',
        'memo'         => '备注',
    ],

    //excel表头
    'excel_head'    => [
        'rectime'        => '交易时间',
        'member'         => '商户名称',
        'com_name'       => '企业名称',
        'orderid'        => '交易号',
        'status'         => '订单状态',
        'itemDetail'     => '交易内容',
        'ptype'          => '交易方式',
        'income'         => '收入(元)',
        'outcome'        => '支出(元)',
        'lmoney'         => '未结票款(元)',
        'cmoney'         => '授信余额(元)',
        'counter'        => '对方商户/授信供应商',
        'counter_acc'    => '对方商户ID/授信供应商ID', //'aid' => '对方商户ID/授信供应商ID',
        'body'           => '产品名称',
        'order_channel'  => '交易渠道',
        'payer_acc'      => '支付方账号',
        'payer_acc_type' => '支付方账号类型',
        'payee_acc'      => '收款方账号',
        'payee_acc_type' => '收款方账号类型',
        'trade_no'       => '支付流水号',
        'item'           => '交易分类',
        'memo'           => '备注',
    ],

    //订单的状态
    'order_status' => [
        '0'  => '未使用',
        '1'  => '已使用',
        '2'  => '已过期',
        '3'  => '被取消',
        '4'  => '待确认',
        '5'  => '撤改',
        '6'  => '撤销',
        '7'  => '部分使用',
        '8'  => '完结',
        '9'  => '被删除',
        '10' => '待预约',
        '11' => '待出票',
        '80' => '待发货',
        '81' => '已发货',
    ],

    //管理员显示名称
    'admin_title' => '票付通信息科技',

    //汇总数据导出时 excel的第一行 按交易账户导出 不在其他地方使用
    'excel_account_trans'  => [
        0  => 0,
        1  => 99,
        2  => 98,
        3  => 1,
        4  => 4,
        5  => 5,
        6  => 9,
        7  => 6,
        8  => 12,
        9  => 14,
        10 => 15,
    ],

    //汇总账户类型 显示顺序
    'excel_account_type' => [
        0 => '余额支付',
        1 => '供应商信用账户',
        2 => '分销商信用账户',
        3 => '支付宝',
        4 => '微信',
        5 => '银联',
        6 => '现金',
        7 => '环迅',
        8 => '平台拉卡拉',
        9 => '平安银行收款',
        10 => '优惠券'
    ],

    //汇总数据导出时 excel的第一行 按交易类型导出 不在其他地方使用
    'excel_item_category' => [
        0  => '购买产品',
        1  => '修改/取消订单',
        2  => '未定义操作',
        3  => '充值/扣款',
        4  => '线下预存还款',
        5  => '产品利润',
        6  => '提现冻结',
        7  => '电子凭证费',
        8  => '短信息费',
        9  => '提现交易手续费',
        97 => '小红书交易佣金',
        10 => '凭证费',
        11 => '取消提现',
        12 => '拒绝提现',
        13 => '退款手续费',
        14 => '押金',
        15 => '充值返现',
        16 => '撤销/撤改订单',
        17 => '转账',
        18 => '佣金发放',
        19 => '佣金提现',
        20 => '获得佣金',
        21 => '平台费',
        22 => '出售产品',
        23 => '线下充值',
        24 => '银行卡验证费',
        25 => '平台套餐续费',
        26 => '预存/还款',
        66 => '专项短信费购买',
        65 => '专项电子凭证费购买',
    ],

    //转换汇总数据导出时 excel的第一行对应的值    a=>b   a对应excel_item_category的值   b对应item_category的值
    'excel_category_trans' => [
         0 => 0,
         1 => 1,
         2 => 2,
         3 => 3,
         4 => 4,
         5 => 5,
         6 => 6,
         7 => 7,
         8 => 8,
         9 => 9,
        10 => 10,
        11 => 12,
        12 => 13,
        13 => 14,
        14 => 15,
        15 => 16,
        16 => 17,
        17 => 18,
        18 => 19,
        19 => 20,
        20 => 21,
        21 => 22,
        22 => 23,
        23 => 24,
        24 => 25,
        25 => 26,
        26 => 27,
        27 => 66,
        28 => 65,
    ],

    //微信端的为交易类型分类item_category的精简版，第一列: 对应dtype的键值; 第二列: 交易类型描述; 第三列：所属交易大类
    'wx_member_item_category' => [
        0  => [0,  '购买产品', 2],
        1  => [1,  '修改/取消订单', 2],
        2  => [3,  '充值/扣款', 3],
        3  => [4,  '线下预存/还款', 3],
        4  => [14, '退款手续费', 2],
        5  => [17, '撤销/撤改订单', 2],
        6  => [24, '线下充值', 3],
        7  => [27, '预存/还款', 3],
    ],

    'credit_unlimit' => 10000000000, //后结模式超大授信值：1亿*10(单位分)

    //将末级购买散客按购买渠道通过分销商进行区分 key对应的ss_order的ordermode字段  id对应的member表的id字段
    // {channel} = ['id' => {pft_member.id}, 'name' => 'pft_member.dname']
    'reseller_map' => [
        10 => ['id' => 11, 'name' => '云票务散客'],
        11 => ['id' => 12, 'name' => '微信商城散客'],
        12 => ['id' => 13, 'name' => '自助机散客'],
        13 => ['id' => 14, 'name' => '二级店铺散客'],
        17 => ['id' => 17, 'name' => '淘宝码商散客'],
        14 => ['id' => 15, 'name' => '闸机散客'],
        15 => ['id' => 16, 'name' => '智能终端散客'],
        18 => ['id' => 21, 'name' => '年卡散客'],
        19 => ['id' => 19, 'name' => '微平台产品预定（自销）散客'],
        22 => ['id' => 22, 'name' => '园区一卡通散客'],
        0  => ['id' => 20, 'name' => 'B端产品预定（自销）散客'],
        //增加微票房散客
        43 => ['id' => 10, 'name' => '微票房散客'],
        // 增加玩聚散客
        56 => ['id' => 66, 'name' => '玩聚散客'],
    ],

    //大数据面板 需要特殊统计的OTA分销商  key => value key和数据库相关联 不能随意改变原来的值
    'reseller_need' => [
        0 => [
            'name' => '携程',
            'need' => [1214]
        ],
        1 => [
            'name' => '美团',
            'need' => [28227],
        ],
        2 => [
            'name' => '去哪儿',
            'need' => [2175]
        ],
        3 => [
            'name' => '驴妈妈',
            'need' => [1472]
        ],
        4 => [
            'name' => '北京旅游网',
            'need' => [4778]
        ],
        5 => [
            'name' => '同程网',
            'need' => [1454]
        ],
        6 => [
            'name' => '广州酷旅旅行社有限公司产品中心',
            'need' => [51603]
        ],
        7 => [
            'name' => '四海一路同行',
            'need' => [61644]
        ],
        8 => [
            'name' => '乐游天下旅游网',
            'need' => [37809],
        ],
        9 => [
            'name' => '福建金桥国际旅行社有限公司',
            'need' => [22448],
        ],
        10 => [
            'name' => '日照锦程国际旅行社有限公司',
            'need' => [53085]
        ],
        11 => [
            'name' => '酷秀旅游',
            'need' => [2079]
        ],
        12 => [
            'name' => '百度糯米',
            'need' => [2706]
        ],
        13 => [
            'name' => '福建光大国际旅行社有限公司',
            'need' => [22474]
        ],
        14 => [
            'name' => '万里行（武汉）国际旅行社有限公司',
            'need' => [225360]
        ],
        15 => [
            'name' => '厦门旅游集散服务中心有限公司',
            'need' => [97762]
        ],
        16 => [
            'name' => '福建中青国际旅行社有限公司',
            'need' => [6630]
        ],
        17 => [
            'name' => '魅力中华',
            'need' => [126473]
        ],
        18 => [
            'name' => '一路同行',
            'need' => [320053]
        ],
        19 => [
            'name' => '新创自我游',
            'need' => [285824]
        ],
        20 => [
            'name' => '福建先行旅游',
            'need' => [4]
        ],
        21 => [
            'name' => '三亚智慧先行旅业发展有限公司',
            'need' => [55]
        ]
    ],
    'ptype_2_subject_code' => [
        0  => BookSubject::PLATFORM_SUBJECT_CODE,
        1  => BookSubject::ALIPAY_SUBJECT_CODE,
        2  => BookSubject::CREDIT_SUBJECT_CODE,
        3  => BookSubject::ADJUST_CREDIT_SUBJECT_CODE,
        4  => BookSubject::WECHAT_SUBJECT_CODE,
        5  => BookSubject::UNION_SUBJECT_CODE,
        6  => BookSubject::HUANXUN_SUBJECT_CODE,
        9  => BookSubject::CASH_SUBJECT_CODE,
        11 => BookSubject::M_LAKALA_SUBJECT_CODE,
        12 => BookSubject::P_LAKALA_SUBJECT_CODE,
        13 => BookSubject::CMBC_WECHAT_SUBJECT_CODE,
        14 => BookSubject::PINGAN_SUBJECT_CODE,
        15 => BookSubject::COUPON_SUBJECT_CODE,
        16 => BookSubject::CMBC_ALIPAY_SUBJECT_CODE,
        25 => BookSubject::CMBC_NET_SUBJECT_CODE,
        26 => BookSubject::UNION_BIZ_SUBJECT_CODE,
        27 => BookSubject::UNION_BIZ_POS_SUBJECT_CODE,
        28 => BookSubject::WFT_SUBJECT_CODE,
        29 => BookSubject::YBY_SUBJECT_CODE,
        30 => BookSubject::YSF_SUBJECT_CODE,
        31 => BookSubject::ABC_SUBJECT_CODE,// 农行的
        32 => BookSubject::BBC_SUBJECT_CODE,//建行的
        33 => BookSubject::BOOZ_SUBJECT_CODE,
        34 => BookSubject::POINT_SUBJECT_CODE,
        35 => BookSubject::ONECARD_PLEDGE_SUBJECT_CODE,
        36 => BookSubject::POS_SUBJECT_CODE,
        37 => 2105,
        38 => BookSubject::SUPPLY_CREDIT_SUBJECT_CODE,
        39 => 2106,
        40 => BookSubject::SUPPLY_ONECARD_PLEDGE_SUBJECT_CODE,
        41 => BookSubject::SUPPLY_POINT_SUBJECT_CODE,
        42 => BookSubject::SUPPLY_ADJUST_CREDIT_SUBJECT_CODE,
        43 => BookSubject::RECHARGECARD_SUBJECT_CODE,
        44 => BookSubject::SUPPLY_RECHARGECARD_SUBJECT_CODE,
        45 => BookSubject::HARVEST_SUBJECT_CODE,
        46 => BookSubject::RECHARGE_DONATION_AMOUNT_CODE,
        47 => BookSubject::JXNSH_SUBJECT_CODE,
        48 => BookSubject::UNIONPAYAPPLET_SUBJECT_CODE,
        49 => BookSubject::SHENGSHITONG_SUBJECT_CODE,
        50 => BookSubject::PLAYGATHER_SUBJECT_CODE,
        51 => BookSubject::QINGHAIPAY_SUBJECT_CODE,
        52 => BookSubject::PLAYGATHERDOUYIN_SUBJECT_CODE,
        53 => BookSubject::CMB_SUBJECT_CODE,
        54 => BookSubject::BAI_DU_SUBJECT_CODE,
        55 => BookSubject::OFFLINE_MANUAL_ORDER_CODE,
        56 => BookSubject::BOCBANK_SUBJECT_CODE,
        57 => BookSubject::YOPPAY_SUBJECT_CODE,
        58 => BookSubject::ICBCBANKAY_SUBJECT_CODE,
        59 => BookSubject::PREPAID_CARD_REPLACE_CODE,
        60 => BookSubject::ABCBMPKAY_SUBJECT_CODE,
        61 => BookSubject::PREPAID_CARD_CODE,
        62 => BookSubject::AHRCU_BANK_SUBJECT_CODE,
        63 => BookSubject::YI_BAO_SUBJECT_CODE,
        64 => BookSubject::GREEN_ROAD_SUBJECT_CODE,
        67 => BookSubject::FJNX_ROAD_SUBJECT_CODE,
        68 => BookSubject::JFT_ROAD_SUBJECT_CODE,
        69 => BookSubject::ABC_BANKCS_SUBJECT_CODE,
        70 => BookSubject::ALLINPAY_SUBJECT_CODE,
        71 => BookSubject::FUIOU_PAY_SUBJECT_CODE,
        72 => BookSubject::FJKX_PAY_SUBJECT_CODE,
        73 => BookSubject::ZGBANK_PAY_SUBJECT_CODE,
        75 => BookSubject::HLD_PAY_SUBJECT_CODE,
        77 => BookSubject::XSNS_PAY_SUBJECT_CODE,//星沙农商
        78 => 1251,//网联商务
        79 => 1252,//金华银行
        80 => 1253,//悦航支付
        81 => 1254,//拉卡拉支付
        82 => 1255,//茶卡自有支付
        83 => 1256,//TLinx
        94 => 1277,//通联支付
        95 => 1278,//小红书-担保支付
        96 => 1279,//西安银行
        97 => 1280,//富滇银行
        98 => 1281,//浙江农信
    ],
    'dtype_2_item_code' => [
        0 => TemplateItem::ORDER_DEDUCT_CODE,
        1 => TemplateItem::ORDER_REFUND_CODE,
        2 => TemplateItem::UNDEFINED_ACTION_CODE,
        3 => TemplateItem::RECHARGE_DEDUCT_CODE,
        4 => TemplateItem::OFFLINE_REFUND_CODE,
        5 => TemplateItem::PRODUCT_PROFIT_CODE,
        6 => TemplateItem::PLATFORM_BALANCE_WITHDRAW_CODE,
        7 => TemplateItem::ELE_CERT_DEDUCT_CODE,
        8 => TemplateItem::SMS_DEDUCT_CODE,
        9 => TemplateItem::WITHDRAW_SERVICE_FEE_CODE,
        11 => TemplateItem::ADJUST_CREDIT_LINE,
        12 => TemplateItem::CANCEL_PLATFORM_BALANCE_WITHDRAW_CODE,
        13 => TemplateItem::REFUSE_PLATFORM_BALANCE_WITHDRAW_CODE,
        14 => TemplateItem::REFUND_SERVICE_FEE_CODE,
        15 => TemplateItem::DEPOSIT_CODE,
        16 => TemplateItem::RECHARGE_FEEDBAC_CODE,
        17 => TemplateItem::ORDER_REVOKE_CODE,
        18 => TemplateItem::TRANSFER_CODE,
        19 => TemplateItem::COMMISSION_ISSUE_CODE,
        20 => TemplateItem::COMMISSION_WITHDRAW_CODE,
        21 => TemplateItem::COMMISSION_RECEIVE_CODE,
        22 => TemplateItem::PLATFORM_FEE_CODE,
        23 => TemplateItem::PRODUCT_SALE_CODE,
        24 => TemplateItem::OFFLINE_RECHARGE_CODE,
        25 => TemplateItem::BANK_VERIFY_FEE_CODE,
        26 => TemplateItem::PLATFORM_MEAL_RENEW_CODE,
        27 => TemplateItem::REFUND_CREDIT_RECHARGE_CODE,
        28 => TemplateItem::ONECARD_CREDIT_RECHARGE_CODE,
        29 => TemplateItem::ONECARD_WITHDRAW_CODE,
        30 => TemplateItem::TERMINAL_RECEIPT_CODE,
        31 => TemplateItem::INCREASE_TICKET_NUM_CODE,
        32 => TemplateItem::APP_BUY_CODE,
        33 => TemplateItem::PARKING_FEE_CODE,
        34 => TemplateItem::REBATE_CODE,
        35 => TemplateItem::MEMBERCARD_CREDIT_RECHARGE_CODE,
        36 => TemplateItem::MOFANG_WITHDRAW_CODE,
        37 => TemplateItem::SIGN_POINT_CODE,
        38 => TemplateItem::CONSUME_POINT_CODE,
        39 => TemplateItem::EXCHANGE_POINT_CODE,
        40 => TemplateItem::ANNUAL_REMAKE_CODE,
        41 => TemplateItem::DEPOSIT_RECHARGE_CODE,
        42 => TemplateItem::ONECARD_DEPOSIT_WITHDRAW_CODE,
        43 => TemplateItem::PRESTORE_SMS_FEE_CODE,
        44 => TemplateItem::PRESTORE_CERT_FEE_CODE,
        45 => TemplateItem::POS_PAY_CODE,
        46 => 1046,
        47 => TemplateItem::COST_OF_PRODUCTION_CODE,
        48 => TemplateItem::MEMBER_SYSTEM_BUY_CODE,
        49 => TemplateItem::MEMBER_SYSTEM_ORDER_CODE,
        50 => TemplateItem::MEMBER_SYSTEM_BIRTHDAY_ORDER_CODE,
        52 => TemplateItem::PACK_SON_REFUND_CODE,
        53 => TemplateItem::RECHARGE_DONATION_AMOUNT__CODE,
        55 => TemplateItem::ORDER_FREIGHT_CODE,
        56 => TemplateItem::CANCEL_FREIGHT_CODE,
        57 => TemplateItem::MULTI_DIST_PROVIDE_AMOUNT_CODE,
        58 => TemplateItem::MULTI_DIST_GAIN_AMOUNT_CODE,
        73 => TemplateItem::ORDER_DEPOSIT_RECHARGE_CODE,
        74 => TemplateItem::ORDER_DEPOSIT_REFUND_CODE,
        77 => TemplateItem::PAY_ORDER_PRESENT_AWARD,
        78 => TemplateItem::SWIPING_PRESENT_AWARD,
        99 => TemplateItem::TERMINAL_REFUND_CODE,
        100 => TemplateItem::SUPPLY_REFUND_AWARD,
        101 => TemplateItem::ORDER_REFUND_AWARD,
    ],

    //费用项归类
    'item_code_category' => [
        //平台运营
        1 => [
            1003,//未定义操作
            1008,//电子凭证费扣除
            1009,//短信息费扣除
            1010,//提现手续费
            1022,//平台费用
            1026,//平台套餐续费
            1032,//应用模块购买
            1043,//短信费预存
            1044,//凭证费预存
            1066,//专项短信费预存
            1065,//专项凭证费预存
            1064,//微商城店铺模板开通扣费
            1097,//小红书交易佣金
        ],
        //产品交易
        2 => [
            1001,//购买产品
            1002,//修改/取消订单
            1014,//退款手续费
            1015,//押金
            1016,//充值返现
            1017,//撤销/撤改订单
            1023,//出售产品
            1031,//增加票数
            1033,//停车费
            1034,//返利
            1045,//POS支付
            1052,//套票子票退款
            1055,//下单扣除运费
            1056,//退款退还运费
            1070,//临时商品
            1071,//计件商品
            1049,//会员下单购买
            1050,//会员生日下单购买
            1072,//积分商城
        ],
        //账户操作
        3 => [
            1004,//充值/扣款
            1005,//线下还款
            1007,//平台余额提现
            1011,//更改授信额度
            1012,//平台余额提现取消
            1013,//提现被拒绝
            1018,//转账
            1024,//线下充值
            1025,//银行卡验证费
            1027,//还款
            1028,//一卡通充值
            1029,//一卡通提现
            1030,//终端收款
            1035,//会员卡充值
            1036,//会员卡提现
            1037,//签到送积分
            1038,//消费送积分
            1039,//积分兑换商品
            1040,//年卡补卡
            1041,//押金充值
            1042,//押金退款
            1046,//预存
            1047,//工本费
            1048,//购买会员
            1053,//充值赠送
            1061,//保证金账本充值
            1062,//保证金账本扣款
            1063,//保证金账本退款
            1073,//订单押金充值
            1074,//订单押金退款
            1069,//营销短信费用退还
        ],
        //佣金利润
        4 => [
            1006,//产品利润
            1019,//佣金发放
            1020,//佣金提现
            1021,//获得佣金
            1057,//分销专员佣金发放
            1058,//分销专员获得佣金
        ],
    ],

    //微平台账本分类搜索
    'micro_account_type_new' => [
        0  => '平台账户',
        1  => '支付宝',
        2  => '供应商授信账户',
        3  => '微信',
        4  => '环迅',
        5  => '现金',
        6  => '线下打款',
        7  => '授信账户',
        8  => '供应商一卡通押金',
        9  => '供应商奖励金账本',
        10 => '专项电子凭证账本',
        11 => '专项短信账本',
        12 => '保证金账本',
        13 => '信用额度调整',
        14 => '供应商信用额度调整',
        15 => '在线支付',
        16 => '电子凭证专款预存',
        17 => '短信专款预存',
    ],

    //微平台账本分类细项
    'subject_code_category' => [
        0 => [
            2601,//平台账户
        ],
        1 => [
            1201,//支付宝
            1209,//支付宝(招商聚合)
        ],
        2 => [
            2103,//供应商授信账户
        ],
        3 => [
            1202,//微信
            1207,//微信(招商聚合)
        ],
        4 => [
            1204,//环迅
        ],
        5 => [
            1101,//现金
        ],
        6 => [
            1210,//线下打款
        ],
        7 => [
            2101,//授信账户
        ],
        8 => [
            2202,//供应商一卡通押金
        ],
        9 => [
            2108,//供应商奖励金账本
        ],
        10 => [
            2503,//专项电子凭证账本
        ],
        11 => [
            2504,//专项短信账本
        ],
        12 => [
            2505,//保证金账本
        ],
        13 => [
            2102,//信用额度调整
        ],
        14 => [
            2104,//供应商信用额度调整
        ],
        15 => [
            1213,//POS支付
            1201,//支付宝
            1202,//微信
            1203,//银联
            1204,//环迅
            1205,//商户拉卡拉
            1206,//平台拉卡拉
            1207,//微信(招商聚合)
            1208,//平安银行
            2701,//优惠券
            1209,//支付宝(招商聚合)
            1211,//招行一网通
            1214,//银联商务支付
            1215,//银联商务POS支付
            1216,//威富通
            1217,//易宝云企付
            1218,//银联云闪付
            1219,//农行
            1220,//建行
            1212,//博思支付
            2301,//积分
            2201,//一卡通押金
            2105,//预存账户
            2106,//供应商预存账户
            2202,//供应商一卡通押金
            2302,//供应商积分
            2401,//充值卡
            2402,//供应商充值卡
            1221,//丰收互联
            1222,//江西农商行
            1223,//银联会员卡
            1224,//盛世通支付
            1225,//玩聚
            1226,//青海银行
            1227,//玩聚-抖音担保交易支付
            1230,//招商银行
            1231,//百度聚合支付
            1232,//线下手工补单
        ],
        16 => [
            2501,//电子凭证专款预存
        ],
        17 => [
            2502,//短信专款预存
        ],
    ],

    //历史冻结明细导出字段
    'frozen_order_history_excel_head' => [
        'orderId'           => '订单号',
        'orderTime'         => '下单时间',
        'productType'       => '产品类型',
        'orderStatus'       => '订单状态',
        'total'             => '票总数',
        'unused'            => '未使用数量',
        'refund'            => '退票数量',
        'verification'      => '验证数量',
        'salePrice'         => '售价单价',
        'costPrice'         => '成本单价',
        'discountMoney'     => '优惠金额(单位:元)',
        'verificationMoney' => '预估释放金额(单位:元)',
        'frozenMoney'       => '冻结金额(单位:元)',
        'resolveDateTime'   => '计算时间',
        'remark'            => '备注',
    ],

    //excel表头
    'finance_balance_excel_head' => [
        'trade_id'     => '交易流水号',
        'rectime'      => '交易时间',
        'orderid'      => '业务订单号',
        'orderstatus'  => '订单状态',
        'trade_no'     => '第三方流水号',
        'cmb_id'       => '交易订单号',
        'pay_id'       => '支付流水号',
        'to_merchant'  => '对方商户',
        'to_account'   => '对方账户',
        'fee_type'     => '费用类型',
        'pro_name'     => '产品名称',
        'subject_book' => '账本类型',
        'money'        => '收支金额',
        'balance'      => '余额',
        'memo'         => '备注',
    ],
);