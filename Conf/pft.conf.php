<?php
include dirname(__FILE__) . '/env.php';
if (!defined('ENV')) {
    define('ENV', 'DEVELOP');
}

//https判断
$httpProto = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https' : 'http';

//定义全局的http头
define('REQUEST_SCHEME', $httpProto);
// 票付通支持H5支付的微信appid
define('PFT_WECHAT_H5PAY_APPID', 'wx042bc4a434a8d3c9');
if (ENV == 'PRODUCTION') {
    define('PFT_WECHAT_APPID', 'wxd72be21f7455640d');
    define("PFT_WECHAT_APPSECRET", 'fb330082b1f0d8a82049a8c2098276be');
    define("OPEN_WECHAT_APPID", 'wx4b3572c2461ef385'); //微信开放平台APPID
    define("OPEN_WECHAT_APPSECRET", '0c79e1fa963cd80cc0be99b20a18faeb'); //微信开放平台APPSECRET
    define('BASE_WWW_DIR', '/var/www/html/new/d');
    define('BASE_WX_DIR', '/var/www/html/wx');
    define('BASE_LOG_DIR', '/alidata/log/site');
    define('IMAGE_UPLOAD_DIR', '/data/images/');
    define('MAIN_DOMAIN', REQUEST_SCHEME . '://www.12301.cc/');

    //登录后平台域名
    if (isset($_SERVER['HTTP_HOST']) && (stripos($_SERVER['HTTP_HOST'], '.pft12301.cc') !== false)) {
        define('MY_DOMAIN', 'http://my.pft12301.cc/');
    } else {
        if (isset($_SERVER['HTTP_HOST']) && (stripos($_SERVER['HTTP_HOST'], 'mybak.12301.cc') !== false)) {
            define('MY_DOMAIN', REQUEST_SCHEME . '://mybak.12301.cc/');
            define('SSO_DOMAIN', REQUEST_SCHEME . '://signin.12301.cc');
        } else if (defined('IS_PFT_GRAY')) {
            define('MY_DOMAIN', REQUEST_SCHEME . '://my.gray.12301.cc/');
            define('SSO_DOMAIN', REQUEST_SCHEME . '://signin.gray.12301.cc');
        } else {
            define('MY_DOMAIN', REQUEST_SCHEME . '://my.12301.cc/');
            define('SSO_DOMAIN', REQUEST_SCHEME . '://signin.12301.cc');
        }
    }
    if (defined('IS_PFT_GRAY')) {
        define('PAY_DOMAIN', REQUEST_SCHEME . '://pay.gray.12301.cc/');
    } else {
        define('PAY_DOMAIN', REQUEST_SCHEME . '://pay.12301.cc/');
    }
    if (defined('IS_PFT_GRAY')) {
        define('MOBILE_DOMAIN', REQUEST_SCHEME . '://wx.gray.12301.cc/');
    } else {
        define('MOBILE_DOMAIN', REQUEST_SCHEME . '://wx.12301.cc/');
    }
    // define('MOBILE_DOMAIN', REQUEST_SCHEME . '://wx.12301.cc/');
    define('SECKILL_DOMAIN', REQUEST_SCHEME . '://seckill.12301.cc/');
    define('IMAGE_URL', REQUEST_SCHEME . '://images.12301.cc/');
    define('LOCAL_DIR', '');

    //huangzhiyang 20180709
    //定义www项目里静态资源外迁至static服务器时需要用到的统一路径前缀
    if (defined('IS_PFT_GRAY') && IS_PFT_GRAY) {
        define('ASSETS_URL_OLD', REQUEST_SCHEME . '://static.12301dev.com/assets/build/old/');
    } else {
        define('ASSETS_URL_OLD', REQUEST_SCHEME . '://static.12301.cc/assets/build/old/');
    }

    define('ASSETS_URL_OLD_POS_MANAGE_ASSETS', REQUEST_SCHEME . '://static.12301.cc/assets/pos-manage-build/old/');
    define('MY_URL', REQUEST_SCHEME . '://my.12301.cc/');
    define('OPEN_URL', 'http://open.12301.cc/');
    define('OTA_URL', 'http://ota.12301.cc/');
    define('API_URL', 'https://api.12301.cc/');
    define('APP_URL', 'https://app.pft12301.cc/');
    define('MOBILE_URL', REQUEST_SCHEME . '://12301.cc/');
    define('IP_INSIDE', '127.0.0.1'); //内网IP地址
    if (defined('IS_PFT_GRAY') && IS_PFT_GRAY) {
        define('IP_TERMINAL', '127.0.0.1'); //终端服务器IP
        define('IP_TERMINAL_INSIDE', '127.0.0.1'); //终端服务器IP-统一使用外网IP(暂时)
    } else {
        define('IP_TERMINAL', '************'); //终端服务器IP
        define('IP_TERMINAL_INSIDE', '************'); //终端服务器IP-统一使用外网IP(暂时)
    }
    //定义EXCEL根路径
    define('EXCEL_DIR', '/data/download_data/report_data/Excel/');
    define('DOWNLOAD_DIR', '/data/download_data/report_data/Download/');

    if (defined('IS_PFT_GRAY') && IS_PFT_GRAY) {
        //灰度环境
        define('STATIC_URL', REQUEST_SCHEME . '://static.12301dev.com/');
        define('ASSETS_URL', REQUEST_SCHEME . '://static.12301dev.com/assets/build/');
        define('ASSETS_URL_POS_MANAGE_ASSETS', REQUEST_SCHEME . '://static.12301dev.com/assets/pos-manage-build/');
    } else {
        define('STATIC_URL', REQUEST_SCHEME . '://static.12301.cc/');
        define('ASSETS_URL', REQUEST_SCHEME . '://static.12301.cc/assets/build/');
        define('ASSETS_URL_POS_MANAGE_ASSETS', REQUEST_SCHEME . '://static.12301.cc/assets/pos-manage-build/');
    }
    // 票付通默认充值收款账户
    define('PFT_RECHARGE_MERCHANT_ID', 38191680);
} elseif (ENV == 'TEST') {
    define('PFT_WECHAT_APPID', 'wxbbe5af4bd6ec3805'); //新的测试云智旅， 旧的：wx042bc4a434a8d3c9
    define("PFT_WECHAT_APPSECRET", "2e5b6c3fce0fca8350802a637fefcc3b");
    define("OPEN_WECHAT_APPID", 'wxb2248ebc66d302bc');
    define("OPEN_WECHAT_APPSECRET", '0c79e1fa963cd80cc0be99b20a18faeb'); //微信开放平台APPSECRET
    //===================
    define('BASE_WWW_DIR', '/var/www/html/new/d');
    define('BASE_WX_DIR', '/var/www/html/wx');
    define('BASE_LOG_DIR', '/alidata/log/site');
    define('IMAGE_UPLOAD_DIR', '/alidata/images/');
    define('MAIN_DOMAIN', REQUEST_SCHEME . '://www.12301dev.com/');
    define('SECKILL_DOMAIN', REQUEST_SCHEME . '://seckill.12301dev.com/');
    //登录后平台域名
    define('MY_DOMAIN', REQUEST_SCHEME . '://my.12301dev.com/');
    define('SSO_DOMAIN', REQUEST_SCHEME . '://signin.12301dev.com');
    define('PAY_DOMAIN', REQUEST_SCHEME . '://pay.12301dev.com/');
    define('API_URL', 'http://api.12301dev.com/');
    define('APP_URL', 'http://app.12301dev.com/');
    define('MOBILE_DOMAIN', REQUEST_SCHEME . '://wx.12301dev.com/');
    define('IMAGE_URL', REQUEST_SCHEME . '://images.12301dev.com/');
    define('STATIC_URL', REQUEST_SCHEME . '://static.12301dev.com/');
    define('LOCAL_DIR', '');
    define('ASSETS_URL', REQUEST_SCHEME . '://static.12301dev.com/assets/build/');
    define('ASSETS_URL_POS_MANAGE_ASSETS', REQUEST_SCHEME . '://static.12301dev.com/assets/pos-manage-build/');
    //huangzhiyang 20180709
    //定义www项目里静态资源外迁至static服务器时需要用到的统一路径前缀
    define('ASSETS_URL_OLD', REQUEST_SCHEME . '://static.12301dev.com/assets/build/old/');
    define('ASSETS_URL_OLD_POS_MANAGE_ASSETS', REQUEST_SCHEME . '://static.12301dev.com/assets/pos-manage-build/old/');
    define('OPEN_URL', 'http://open.12301dev.com/');
    define('MOBILE_URL', REQUEST_SCHEME . '://12301dev.com/');
    define('OTA_URL', 'http://ota.12301dev.com/');
    define('IP_INSIDE', '*********');
    define('IP_TERMINAL', '*********');
    define('IP_TERMINAL_INSIDE', '*********'); //终端服务器IP-内网
    //定义EXCEL根路径
    define('EXCEL_DIR', '/alidata/download_data/report_data/Excel/');
    define('DOWNLOAD_DIR', '/alidata/download_data/report_data/Download/');
    //微商城dev支付回调需要该参数
    define('WECHAT_APPID', 'wx042bc4a434a8d3c9');
    define("WECHAT_APPSECRET", "2e5b6c3fce0fca8350802a637fefcc3b");
    // 票付通默认充值收款账户
    define('PFT_RECHARGE_MERCHANT_ID', 3278922);
} elseif (ENV == 'DEVELOP' || ENV == 'LOCAL') {
    define('PFT_WECHAT_APPID', 'wxbbe5af4bd6ec3805'); //新的测试云智旅， 旧的：wx042bc4a434a8d3c9
    define("PFT_WECHAT_APPSECRET", "2e5b6c3fce0fca8350802a637fefcc3b");
    define("OPEN_WECHAT_APPID", 'wxb2248ebc66d302bc');
    define("OPEN_WECHAT_APPSECRET", '0c79e1fa963cd80cc0be99b20a18faeb'); //微信开放平台APPSECRET

    define('BASE_WWW_DIR', '/var/www/html/new/d');
    define('BASE_WX_DIR', '/var/www/html/wx');
    defined('BASE_LOG_DIR') || define('BASE_LOG_DIR', '/alidata/log/site');
    define('IMAGE_UPLOAD_DIR', '/var/www/images/');

    if (ENV == 'LOCAL') {
        define('OPEN_URL', 'http://open.12301.local/');
        define('PAY_DOMAIN', 'http://pay.12301.local/');
    } else {
        if (stripos($_SERVER['HTTP_HOST'], 'my2.') !== false || stripos($_SERVER['HTTP_HOST'], 'www2.') !== false) {
            define('OPEN_URL', 'http://open2.12301.test/');
            define('PAY_DOMAIN', 'http://pay2.12301.test/');
        } else {
            define('OPEN_URL', 'http://open.12301.test/');
            define('PAY_DOMAIN', 'http://pay.12301.test/');
        }
    }

    //定义EXCEL根路径
    define('EXCEL_DIR', '/alidata/download_data/report_data/Excel/');
    define('API_URL', 'http://api.12301.local/');
    define('DOWNLOAD_DIR', '/alidata/download_data/report_data/Download/');
    if (strpos($_SERVER['HTTP_HOST'], 'test')) {
        if (stripos($_SERVER['HTTP_HOST'], 'www2.') !== false) {
            define('MAIN_DOMAIN', 'http://www2.12301.test/');
            //登录后平台域名
            define('MY_DOMAIN', 'http://my2.12301.test/');
            define('SSO_DOMAIN', 'http://signin.12301.test');
        } else {
            define('MAIN_DOMAIN', 'http://www.12301.test/');
            //登录后平台域名
            define('MY_DOMAIN', 'http://my.12301.test/');
            define('SSO_DOMAIN', 'http://signin.12301.test');
        }

        define('MOBILE_DOMAIN', 'http://wx.12301.test/');
        define('SECKILL_DOMAIN', 'http://seckill.12301.test/');
        define('IMAGE_URL', 'http://images.12301.test/');
        define('STATIC_URL', 'http://static.12301.test/');
        define('APP_URL', 'http://app.pft12301.test/');
        define('LOCAL_DIR', '');
        define('MOBILE_URL', 'http://12301.test/');
        define('OTA_URL', 'http://ota.12301.test/');
        define('ASSETS_URL', 'http://static.12301.test/assets/build/');
        define('ASSETS_URL_POS_MANAGE_ASSETS', 'http://static.12301.test/assets/pos-manage-build/');
        //huangzhiyang 20180709
        //定义www项目里静态资源外迁至static服务器时需要用到的统一路径前缀
        define('ASSETS_URL_OLD', REQUEST_SCHEME . '://static.12301.test/assets/build/old/');
        define('ASSETS_URL_OLD_POS_MANAGE_ASSETS', REQUEST_SCHEME . '://static.12301.test/assets/pos-manage-build/old/');
    } elseif (strpos($_SERVER['HTTP_HOST'], '9000')) {
        define('MY_DOMAIN', 'http://my.9000.io/');
        define('MOBILE_DOMAIN', 'http://wx.12301.test/');
        define('SECKILL_DOMAIN', 'http://seckill.12301.test/');
        define('IMAGE_URL', 'http://images.12301.test/');
        define('STATIC_URL', 'http://static.12301.test/');
        define('LOCAL_DIR', '');
        define('MOBILE_URL', 'http://12301.test/');
        define('OTA_URL', 'http://ota.12301.test/');
        define('ASSETS_URL', 'http://static.12301.test/assets/build/');
        define('ASSETS_URL_POS_MANAGE_ASSETS', 'http://static.12301.test/assets/pos-manage-build/');
        //huangzhiyang 20180709
        //定义www项目里静态资源外迁至static服务器时需要用到的统一路径前缀
        define('ASSETS_URL_OLD', REQUEST_SCHEME . '://static.12301.test/assets/build/old/');
        define('ASSETS_URL_OLD_POS_MANAGE_ASSETS', REQUEST_SCHEME . '://static.12301.test/assets/pos-manage-build/old/');
    } else {
        define('MOBILE_DOMAIN', 'http://wx.12301.local/');
        define('SECKILL_DOMAIN', 'http://seckill.12301.local/');
        define('MAIN_DOMAIN', 'http://www.12301.local/');
        define('APP_URL', 'http://app.pft12301.local/');
        //登录后平台域名
        define('MY_DOMAIN', 'http://my.12301.local/');

        if (defined('ENV_LOCAL')) {
            //前端同学访问的地址
            define('STATIC_URL', 'http://static.12301.local/');
            define('LOCAL_DIR', 'local/');
            define('ASSETS_URL', 'http://static.12301.local/assets/build/local/');
            define('ASSETS_URL_POS_MANAGE_ASSETS', 'http://static.12301.local/pos-manage-assets/build/local/');
            //huangzhiyang 20180709
            //定义www项目里静态资源外迁至static服务器时需要用到的统一路径前缀
            define('ASSETS_URL_OLD', REQUEST_SCHEME . '://static.12301.local/assets/build/old/');
            define('ASSETS_URL_OLD_POS_MANAGE_ASSETS', REQUEST_SCHEME . '://static.12301.local/pos-manage-assets/build/old/');

            //单点登录地址
            define('SSO_DOMAIN', 'http://signin.12301.local');
        } else {
            //后端同学访问的地址
            define('STATIC_URL', 'http://static.12301.test/');
            define('LOCAL_DIR', '');
            define('ASSETS_URL', 'http://static.12301.test/assets/build/');
            define('ASSETS_URL_POS_MANAGE_ASSETS', 'http://static.12301.test/assets/pos-manage-build/');
            //huangzhiyang 20180709
            //定义www项目里静态资源外迁至static服务器时需要用到的统一路径前缀
            define('ASSETS_URL_OLD', REQUEST_SCHEME . '://static.12301.test/assets/build/old/');
            define('ASSETS_URL_OLD_POS_MANAGE_ASSETS', REQUEST_SCHEME . '://static.12301.test/assets/pos-manage-build/old/');

            //单点登录地址
            define('SSO_DOMAIN', 'http://signin.12301.test');
        }
        define('IMAGE_URL', 'http://images.12301.local/');
        define('MOBILE_URL', 'http://12301.local/');
        define('OTA_URL', 'http://ota.12301.local/');
    }
    // 这是本地和内网测试环境的。
    define('IP_INSIDE', '**************');
    define('IP_TERMINAL', '**************');
    define('IP_TERMINAL_INSIDE', '**************'); //终端服务器IP-内网
    // 票付通默认充值收款账户
    define('PFT_RECHARGE_MERCHANT_ID', 1);
}
//定义前端组件库的静态资源引用地址
define('PFT_UI_COMPONENT', REQUEST_SCHEME . '://static.12301.cc/assets/build/component/');

//定义散客ID
define('PFT_SANKE_ID', 112);
//定义支付宝收款账户ID
if (!defined('PFT_ALIPAY_PARTNER_ID')) {
    define('PFT_ALIPAY_PARTNER_ID', '2088021989927151');
}

//定义微信支付收款商户ID
if (!defined('PFT_WEPAY_MCHID')) {
    // 定义票付通自己收款的商户号，define定义数组常量在PHP7里面才支持
    define('PFT_WEPAY_MCHID', ['1287192601', '1510469351']);
}
//定义支付宝当面付appid
if (!defined('PFT_ALIPAY_F2FPAY_ID')) {
    define('PFT_ALIPAY_F2FPAY_ID', '2015102800574442');
}
const PFT_ALIPAY_ISV_APPID = '2021004159666991';

//定义支付宝app支付appid
if (!defined('PFT_ALIPAY_APP_PAY_ID')) {
    define('PFT_ALIPAY_APP_PAY_ID', '2021001194697489');
}


//定义html目录的路径，方便后面的文件查找
define('HTML_DIR', '/var/www/html/');
//定义配置文件路径
define('CONF_DIR', HTML_DIR . '/Service/Conf');
//定义新的模板路径

//设置主题 - 去除之前过渡期添加的default主题
define('TPL_THEME', 'new');

define('VIEWS', HTML_DIR . '/Views/' . TPL_THEME);

//定义前端新页面需要用到的域名 - 因为二级店铺是这样的
if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/new/d/') !== false) {
    define('PREFIX_DOMAIN', REQUEST_SCHEME . '://' . $_SERVER['HTTP_HOST'] . '/new/d/');

    //是否是二级店铺
    define('IS_SECOND_DOMAIN', true);
} else {
    define('PREFIX_DOMAIN', REQUEST_SCHEME . '://' . $_SERVER['HTTP_HOST'] . '/');

    define('IS_SECOND_DOMAIN', false);
}

//huangzhiyang 2017-12-04
//定义前端资源在正式环境的引用地址前缀
define('ASSETS_URL_PROD', REQUEST_SCHEME . '://static.12301.cc/assets/build/');
define('ASSETS_URL_PROD_POS_MANAGE_ASSETS', REQUEST_SCHEME . '://static.12301.cc/assets/pos-manage-build/');
define('ASSETS_URL_PROD_OLD', REQUEST_SCHEME . '://static.12301.cc/assets/build/old/');
define('ASSETS_URL_PROD_OLD_POS_MANAGE_ASSETS', REQUEST_SCHEME . '://static.12301.cc/assets/pos-manage-build/old/');
