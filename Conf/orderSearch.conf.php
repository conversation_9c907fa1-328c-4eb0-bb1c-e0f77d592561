<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON>n
 * Date: 2017/6/9
 * Time: 14:11
 */
return [
    //订单来源
    'source'                 => [
        '0'  => '正常分销商下单',
        '1'  => '普通用户支付',
        '2'  => '用户手机支付',
        '9'  => '会员卡购票',
        '10' => '云票务',
        '11' => '微商城',
        '12' => '自助机',
        '13' => '二级店铺',
        '14' => '闸机购票 ',
        '15' => '智能终端',
        '16' => '计调下单',
        '17' => '淘宝码商',
        '18' => '年卡',
        '19' => '微平台',
        '20' => '外部接口OTA下单',
        '23' => '套票子票下单',
        '24' => '团报订单',
        '33' => 'e福州',
        '34' => '拼团',
        '35' => '积分商城',
        '38' => '招行',
        '43' => '微票房',
        '46' => '断网离线订单',
    ],
    'status'                 => [
        '<em style="color: #f07845">未验证</em>',
        '<em style="color: #3dba31">已验证</em>',
        '<em style="color: #92a0ab">已过期</em>',
        '<em style="color: #92a0ab">已取消</em>',
        '<em style="color: #92a0ab">待确认</em>',
        '<em style="color: #92a0ab">已撤改</em>',
        '<em style="color: #92a0ab">已撤销</em>',
        '<em style="color: #3dba31">部分验证</em>',
        '<em style="color: #92a0ab">完结</em>',
    ],
    'pay_status'             => [
        2 => '未支付',
        1 => '已支付',
    ],
    
    //下单渠道
    'order_mode_two'         => [
        0  => ['name' => '分销商下单', 'key' => 0],
        1  => ['name' => '淘宝码商(飞猪)', 'key' =>17],
        2  => ['name' => 'OTA', 'key' =>  20],
        3  => ['name' => '阿里供销', 'key' =>  58],
        4  => ['name' => '微商城', 'key' => 11],
        5  => ['name' => '微平台', 'key' => 19],
        6  => ['name' => '云票务', 'key' => 10],
        7  => ['name' => '智能终端', 'key' => 15],
        8  => ['name' => '自助机', 'key' => 12],
        9  => ['name' => '二级店铺', 'key' => 13],
        10  => ['name' => '闸机购票', 'key' => 14],
        11  => ['name' => '计调下单', 'key' => 16],
        12 => ['name' => '套票子票下单', 'key' => 23],
        13 => ['name' => '年卡', 'key' => 18],
        14 => ['name' => '一卡通', 'key' => 22],
        15 => ['name' => '兑换码购票', 'key' => 21],
        16 => ['name' => '会员卡购票', 'key' => 9],
        17 => ['name' => 'e福州', 'key' => 33],
        18 => ['name' => '拼团', 'key' => 34],
        19 => ['name' => '积分商城', 'key' => 35],
        20 => ['name' => '招行', 'key' => 38],
        21 => ['name' => '微信-微票房', 'key' => 43],
        22 => ['name' => '团队订单', 'key' => 24],
        23 => ['name' => '断网离线订单', 'key' => 46],
        24 => ['name' => '云闪付', 'key' => 45],
        25 => ['name' => '团单报团计调', 'key' => 44], //帮建辉新增
        26 => ['name' => '桌面云票务', 'key' => 49],//桌面云票务
        27 => ['name' => 'APP', 'key' => 50],
        28 => ['name' => '特殊团队预约', 'key' => 55],
        29 => ['name' => '小程序', 'key' => 56],
        30 => ['name' => '抖音-微票房', 'key' => 57],
        31 => ['name' => '团购导码', 'key' => 59],
        32 => ['name' => '预售券兑换', 'key' => 64],
    ],
    //分账配置使用的销售渠道
    'order_mode_separate'    => [
        0  => ['name' => '分销商下单', 'key' => 0],
        1  => ['name' => '微商城', 'key' => 11],
        2  => ['name' => '微平台', 'key' => 19],
        3  => ['name' => '云票务', 'key' => 10],
        4  => ['name' => '智能终端', 'key' => 15],
        5  => ['name' => '自助机', 'key' => 12],
        6  => ['name' => '闸机购票', 'key' => 14],
        7  => ['name' => '计调下单', 'key' => 16],
        8  => ['name' => '会员卡购票', 'key' => 9],
        9  => ['name' => '积分商城', 'key' => 35],
        10 => ['name' => '微票房', 'key' => 43],
        11 => ['name' => '云闪付', 'key' => 45],
    ],
    'order_mode'             => [
        0  => '',
        1  => '普通用户支付',
        2  => '用户手机支付',
        9  => '会员卡购票',
        10 => '云票务',
        11 => '微商城',
        12 => '自助机',
        13 => '二级店铺',
        14 => '闸机购票',
        15 => '智能终端',
        16 => '计调下单',
        17 => '淘宝码商',
        18 => '年卡',
        19 => '微平台',
        20 => 'OTA',
        21 => '兑换码购票',
        22 => '一卡通',
        33 => '博思支付',
        34 => '拼团',
        35 => '积分商城',
        38 => '招行',
        43 => '微信-微票房',
        45 => '云闪付',
        46 => '断网离线订单',
        49 => '桌面云票务',
        50 => 'APP',
        55 => '特殊团队预约',
        56 => '小程序',
        57 => '抖音-微票房',
        59 => '团购导码',
        64 => '预售券兑换',
    ],
    //支付方式
    'pay_mode_two'           => [ //订单支付类型
        0  => ['name' => "余额支付", 'key' => 0],
        1  => ['name' => "授信支付", 'key' => 2],
        2  => ['name' => "支付宝", 'key' => [1, 16]],
        3  => ['name' => "微信", 'key' => [5, 14]],
        4  => ['name' => "现金支付", 'key' => 9],
        5  => ['name' => "产品自销", 'key' => 3],
        6  => ['name' => "现场支付", 'key' => 4],
        7  => ['name' => '会员卡支付', 'key' => 6],
        8  => ['name' => '银联支付', 'key' => 7],
        9  => ['name' => '环迅支付', 'key' => 8],
        10 => ['name' => '会员卡', 'key' => 10],
        11 => ['name' => '拉卡拉（商户）', 'key' => 11],
        12 => ['name' => '拉卡拉（平台）', 'key' => 12],
        13 => ['name' => '特权支付（年卡）', 'key' => 13],
        14 => ['name' => '微信（聚合）', 'key' => 14],
        15 => ['name' => '平安银行', 'key' => 15],
        16 => ['name' => '支付宝（聚合）', 'key' => 16],
        17 => ['name' => '会员卡支付', 'key' => 17],
        18 => ['name' => '计时卡支付', 'key' => 18],
        25 => ['name' => '一网通支付', 'key' => 25],
        26 => ['name' => '银联商务支付', 'key' => 26],
        27 => ['name' => '银联商务POS支付', 'key' => 27],
        28 => ['name' => '威富通支付', 'key' => 28],
        29 => ['name' => '易宝云企付', 'key' => 29],
        30 => ['name' => '银联云闪付', 'key' => 30],
        31 => ['name' => '农行', 'key' => 31],
        32 => ['name' => '建行', 'key' => 32],
        33 => ['name' => '博思支付', 'key' => 33],
        35 => ['name' => '积分支付', 'key' => 35],
        36 => ['name' => 'POS支付', 'key' => 36],
        37 => ['name' => '身份证一卡通', 'key' => 37],
        38 => ['name' => '预存余额支付', 'key' => 38],
        34 => ['name' => '丰收互联', 'key' => 34],
        39 => ['name' => '江西农商行', 'key' => 39],
        40 => ['name' => '银联会员卡', 'key' => 40],
        41 => ['name' => '盛世通支付', 'key' => 41],
        42 => ['name' => '玩聚', 'key' => 42],
        43 => ['name' => '青海银行', 'key' => 43],
        44 => ['name' => '玩聚-抖音担保交易支付', 'key' => 44],
        45 => ['name' => '招商银行聚合支付','key' => 45],
        46 => ['name' => '百度聚合支付','key' => 46],
        47 => ['name' => '线下手工补单','key' => 47],
        48 => ['name' => '中国银行','key' => 48],
        49 => ['name' => '易宝独立收款','key' => 49],
        50 => ['name' => '工商银行','key' => 50],
        51 => ['name' => '预付卡','key' => 51],
        52 => ['name' => '农行(云BMP)','key' => 52],
        53 => ['name' => '预付卡','key' => 53],
        54 => ['name' => '安徽农商行','key' => 54],
        55 => ['name' => '易宝平台收款','key' => 55],
        56 => ['name' => '绿聚支付','key' => 56],
        58 => ['name' => '福建农信','key' => 58],
        59 => ['name' => '聚富通','key' => 59],
        60 => ['name' => '农行(创识)','key' => 60],
        61 => ['name' => '通联支付','key' => 61],
        62 => ['name' => '富友支付','key' => 62],
        63 => ['name' => '酷享支付','key' => 63],
        64 => ['name' => '自贡银行','key' => 64],
        65 => ['name' => '预售券权益支付','key' => 65],
        66 => ['name' => '慧徕店支付','key' => 66],
        67 => ['name' => '预存码权益支付', 'key' => 67],
        68 => ['name' => '星沙农行', 'key' => 68],
        69 => ['name' => '网联商务', 'key' => 69],
        70 => ['name' => '金华银行', 'key' => 70],
        71 => ['name' => '悦航支付', 'key' => 71],
        72 => ['name' => '拉卡拉支付', 'key' => 72],
        73 => ['name' => '茶卡自有支付', 'key' => 73],
        74 => ['name' => '无需支付', 'key' => 74],
        75 => ['name' => 'TLinx', 'key' => 75],
        76 => ['name' => '线下收款1', 'key' => 76],
        77 => ['name' => '线下收款2', 'key' => 77],
        78 => ['name' => '线下收款3', 'key' => 78],
        79 => ['name' => '线下收款4', 'key' => 79],
        80 => ['name' => '线下收款5', 'key' => 80],
        81 => ['name' => '线下收款6', 'key' => 81],
        82 => ['name' => '线下收款7', 'key' => 82],
        83 => ['name' => '线下收款8', 'key' => 83],
        84 => ['name' => '线下收款9', 'key' => 84],
        85 => ['name' => '线下收款10', 'key' => 85],
        86 => ['name' => '通联支付', 'key' => 86],
        87 => ['name' => '小红书-担保支付', 'key' => 87],
        255 => ['name' => '未知', 'key' => 255],
        88 => ['name' => '西安银行', 'key' => 88],
        89 => ['name' => '富滇银行', 'key' => 89],
        90 => ['name' => '浙江农信', 'key' => 90],
    ],
    'pay_mode'  => [ //订单支付类型
        0  => "余额支付",
        1  => "支付宝",
        2  => "授信支付",
        3  => "产品自销",
        4  => "现场支付",
        5  => '微信支付',
        6  => '会员卡支付',
        7  => '银联支付',
        8  => '环迅支付',
        9  => '现金支付',
        10 => '会员卡',
        11 => '拉卡拉（商户）',
        12 => '拉卡拉（平台）',
        13 => '特权支付（年卡）',
        14 => '微信（聚合）',
        15 => '平安银行',
        16 => '支付宝（聚合）',
        17 => '会员卡支付',
        18 => '计时卡支付',
        25 => '招行一网通支付',
        26 => '银联商务支付',
        27 => '银联商务POS支付',
        28 => '威富通支付',
        29 => '易宝云企付',
        30 => '银联云闪付',
        31 => '农行',
        32 => '建行',
        33 => '博思支付',
        35 => '积分支付',
        36 => 'POS支付',
        37 => '身份证一卡通',
        38 => '预存余额支付',
        34 => '丰收互联',
        39 => '江西农商行',
        40 => '银联会员卡',
        41 => '盛世通支付',
        42 => '玩聚',
        43 => '青海银行',
        44 => '玩聚-抖音担保交易支付',
        45 => '招商银行聚合支付',
        46 => '百度聚合支付',
        47 => '线下手工补单',
        48 => '中国银行',
        49 => '易宝独立收款',
        50 => '工商银行',
        51 => '预付卡',
        52 => '农行(云BMP)',
        53 => '预付卡',
        54 => '安徽农商行',
        55 => '易宝平台收款',
        56 => '绿聚支付',
        58 => '福建农信',
        59 => '聚富通',
        60 => '农行(创识)',
        61 => '通联支付',
        62 => '富友支付',
        63 => '酷享支付',
        64 => '自贡银行',
        65 => '预售券权益支付',
        66 => '慧徕店支付',
        67 => '预存码权益支付',
        68 => '星沙农行',
        69 => '网联商务',
        70 => '金华银行',
        71 => '悦航支付',
        72 => '拉卡拉支付',
        73 => '茶卡自有支付',
        74 => '无需支付',
        75 => 'TLinx',
        76 => '线下收款1',//线下收款1
        77 => '线下收款2',//线下收款2
        78 => '线下收款3',//线下收款3
        79 => '线下收款4',//线下收款4
        80 => '线下收款5',//线下收款5
        81 => '线下收款6',//线下收款6
        82 => '线下收款7',//线下收款7
        83 => '线下收款8',//线下收款8
        84 => '线下收款9',//线下收款9
        85 => '线下收款10',//线下收款10
        86 => '通联支付',
        87 => '小红书-担保支付',
        255 => '未知',
        88 => '西安银行',
        89 => '富滇银行',
        90 => '浙江农信',
    ],
    //景区类型
    'land_type' => [
        'A'   => '景区',
        'A-1' => '预售券',
        'B'   => '线路',
        'C'   => '酒店',
        'F'   => '套票',
        'G'   => '餐饮',
        'H'   => '演出',
        'I'   => '年卡',
        'J'   => '特产',
        'K'   => '计时',
        'Q'   => '旅游券',
        'N'   => '酒店＋',
    ],
    'terminal_order_mode'    => [10, 12, 15, 16],

    //订单状态
    'order_status'           => [
        0  => '未使用',
        1  => '已使用',
        2  => '已过期',
        3  => '被取消',
        4  => '待确认',
        5  => '撤改',
        6  => '撤销',
        7  => '部分使用',
        8  => '订单完结',
        10 => '待预约',
        11 => '待出票',
        80 => '待发货',
        81 => '已发货',
    ],
    //游客表状态映射
    'tourist_order_status' => [
        0 => '未入园',
        1 => '已使用',
        2 => '已取消',
        3 => '审核中',
        4 => '已过期',
        7 => '已入园',
    ],
    //验证方式
    'validation_way'         => [
        0 => [
            'id'   => [5],
            'name' => '云闸机',
        ],
        1 => [
            'id'   => [0, 20],
            'name' => '终端机',
        ],
        2 => [
            'id'   => [1],
            'name' => '软终端',
        ],
        3 => [
            'id'   => [3],
            'name' => '外部接口通知更新',
        ],
        4 => [
            'id'   => [19],
            'name' => '自运行服务',
        ],
    ],

    //分库对应的下单时间区间
    'time_type'              => [
        1  => ['2015-01-01', '2015-06-30'],
        2  => ['2015-07-01', '2015-12-31'],
        3  => ['2016-01-01', '2016-06-30'],
        4  => ['2016-07-01', '2016-12-31'],
        5  => ['2017-01-01', '2017-06-30'],
        6  => ['2017-07-01', '2017-12-31'],
        7  => ['2018-01-01', '2018-06-30'],
        8  => ['2018-07-01', '2018-12-31'],
        9  => ['2019-01-01', '2019-06-30'],
        10 => ['2019-07-01', '2019-12-31'],
        11 => ['2020-01-01', '2020-06-30'],
        12 => ['2020-07-01', '2020-12-31'],
        13 => ['2021-01-01', '2021-06-30'],
        14 => ['2021-07-01', '2021-12-31'],
        15 => ['2022-01-01', '2022-06-30'],
        16 => ['2022-07-01', '2022-12-31'],
        17 => ['2023-01-01', '2023-06-30'],
        18 => ['2023-07-01', '2023-12-31'],
        19 => ['2024-01-01', '2024-06-30'],
        20 => ['2024-07-01', '2024-12-31'],
        0  => ['2025-01-01'],
    ],

    //追踪表的操作
    'action'                 => [
        0  => '下单',
        1  => '修改',
        2  => '取消',
        3  => '取票',
        4  => '支付',
        5  => '验证',
        6  => '撤销',
        7  => '撤改',
        8  => '重打印',
        9  => '离线订单下载',
        10 => '处理退票申请',
        11 => '提交退票申请',
        12 => '过期',
        13 => '同意退票申请',
        14 => '拒绝退票申请',
        15 => '核销',
        16 => '订单加票',
        17 => '完结',
        18 => '重发短信',
        19 => '激活码兑换',
        20 => '退票处理失败',
        21 => '修改基础信息',
        22 => '改签',
        23 => '提交改签申请',
        24 => '同意改签申请',
        25 => '拒绝改签申请',
        26 => '审核（人工）',
        27 => '发货',
        28 => '修改物流',
        29 => '提交退货申请',
        30 => '拒绝退货申请',
        31 => '同意退货申请',
        32 => '订单预约',
        33 => '入园',
        35 => '变更有效期',
        36 => '积分消费',
        37 => '积分退还',
        38 => '售后',
        39 => '兑换权益',
        40 => '预检',
    ],

    //票类状态
    'ticket_status'          => [
        1 => '上架',
        2 => '下架',
        6 => '删除',
    ],

    //验证状态 - order_tourist_info
    'check_state'            => [
        0 => '未验证',
        1 => '已验证',
        2 => '已取消',
    ],

    //退票审核状态
    'audit_status'           => [
        0 => '待审核',
        1 => '已同意',
        2 => '已拒绝',
        3 => '等待第三方平台审核通过',
    ]
    ,
    'url'                    => [
        'PRODUCTION' => "https://{account}.12301.cc/h5?disId={uuid}",
        'TEST'       => "http://{account}.12301dev.com/h5?disId={uuid}",
        'DEVELOP'    => "http://{account}.12301.test/h5?disId={uuid}",
        'LOCAL'      => "http://{account}.12301.local/h5?disId={uuid}",
    ],

    //取票状态
    'print_state'            => [
        0 => '未取票',
        1 => '已取票',
        2 => '部分取票',
    ],

    //允许展示取票状态的类型
    'print_state_allow_type' => [
        0 => 'A',
        1 => 'B',
        2 => 'F',
        3 => 'H',
        4 => 'G',
    ],

    //取票方式
    'print_way'              => [
        1 => '门票码取票',
        2 => '手机号取票',
        3 => '身份证取票',
        4 => '订单号取票',
        5 => '团单号取票',
    ],

    //取票方式
    'track_action'           => [
        1 => '入园',
        2 => '取消',
        3 => '过期',
        4 => '验证',
        5 => '撤销撤改'
    ],

    //订单颜色标记映射
    'order_color'            => [
        1 => 'RED',
        2 => 'GREEN',
        3 => 'BLUE',
    ],
];