<?php
/**
 * 业务配置数据
 *
 * <AUTHOR>
 * @date   2016-05-18
 */

return array(
    //定义当前移动支付使用的平台
    //'use_cmbc_platform'          => true,
    'use_cmbc_platform'          => false,
    //产品类型
    'ptype_list'                 => ['A', 'B', 'C', 'D', 'F', 'G', 'H'],
    //定义在线支付的交易方式  去除平安银行15 取消退款 退回账户
    'online_pay_mode'  => [
        1, 5, 7, 14, 15, 16, 23, 26, 27,
        28, 29, 30, 31, 32, 34, 39, 40,
        41, 42, 43, 44, 45, 46, 48, 49,
        50, 51, 52, 54, 55, 56, 58, 59,
        60, 61, 62, 63, 64, 66, 68, 69,
        70, 71, 72, 73, 75, 86, 87,
        88,//西安银行
        89,//富滇银行
        90,//浙江农信
    ],
    // 线下在线收款
    'underline_online_pay'       => [34],
    'email'                      => [
        //'host'     => 'smtp.mxhichina.com',
        'host'     => 'smtp.exmail.qq.com',
        'port'     => 465,
        'username' => '<EMAIL>',
        'password' => 'Pft88080123$',
        'secure'   => 'ssl',//ssl
        'from'     => '票付通12301',
    ],
    //短信模板配置
    'sms'                        => array(
        'account_upgrade'  => '您正在使用账号升级功能。验证码:{vcode}。【票付通】',
        'alipay_m'         => '您正在绑定或者修改支付宝账号，您的验证码为{vcode}。',
        'change_pwd'       => '您正在修改密码，验证码{vcode}',
        'forget'           => '您正在使用找回密码功能，您的验证码为{vcode}',
        'hotel_order'      => '预订通知：客人{dname}预订了{pname}{num}间，{begintime}入住，{endtime}离店，订单号：{ordernum}。联系电话：{tel}。客人备注信息：{note}。',
        'order_search'     => '您正在使用手机号查询订单功能。验证码:{vcode}。【票付通】',
        'phone'            => '您正在使用修改手机号功能，您的验证码为{vcode}【票付通】',
        'register'         => '验证码{vcode}，欢迎使用票付通平台。',
        'relation'         => '{dname}您好！【{aname}】添加您为平台分销商。帐号为您的手机号,密码{pwd},赶快登录www.12301.cc或关注"票付通"、"pft_12301"微信公众号，绑定账号分销{aname}的产品吧~帮助：t.cn/RZG1HLA',
        'wechat_bind'      => '您正在使用微信绑定功能，验证码：{vcode}',
        'annual_activate'  => '您正在使用年卡激活服务，验证码: {vcode}',
        'annual_loss'      => '您正在使用年卡挂失服务，验证码: {vcode}',
        'bankcard_edit'    => '您正在使用银行卡修改服务，验证码: {vcode}',
        'park_loss'        => '您正在使用园区卡挂失服务，验证码: {vcode}',
        'renew'            => '平台会员{id}：{dname}，已成功充值{meal}，{desc}，协议有效期截止时间为{date}。',
        'order_cancel'     => '您正在使用订单取消功能，验证码：{vcode}',
        'check_old_mobile' => '您本次操作（安全验证手机号）的短信验证码为{vcode}，如非本人操作请及时修改平台密码。',
        'check_new_mobile' => '您本次操作（绑定新手机号）的短信验证码为{vcode}。',
        'bat_task_finish'  => '您定制的任务[编号{taskID}-{taskName}]已完成，请您验收。',
        'old_mall_vcode'   => '您正在使用微信绑定功能，验证码：{vcode}',
    ),

    //黑名单
    'black_list'                 => array(
        'mobile' => array('***********', '***********'),
        'ip'     => array(),
),
    'order_pay_mode'  => [ //订单支付类型
        0  => "余额支付",
        1  => "支付宝",
        2  => "授信支付",
        3  => "产品自销",
        4  => "现场支付",
        5  => '微信支付',
        6  => '会员卡支付',
        7  => '银联支付',
        8  => '环迅支付',
        9  => '现金支付',
        10 => '会员卡',
        11 => '拉卡拉（商户）',
        12 => '拉卡拉（平台）',
        13 => '特权支付（年卡）',
        14 => '微信（聚合）',
        15 => '平安银行',
        16 => '支付宝（聚合）',
        17 => '会员卡支付',     // 虚拟会员卡支付 法利兰
        25 => '招行一网通支付',
        26 => '银联商务支付',
        27 => '银联商务POS通',
        28 => '威富通',
        29 => '易宝',
        30 => '云闪付',
        31 => '农行',
        32 => '建行',
        34 => '丰收互联支付',
        35 => '积分支付',
        36 => 'POS支付',
        37 => '一卡通(身份证)',
        38 => '预存余额支付',
        39 => '江西农商行',
        40 => '银联会员卡',
        41 => '盛世通支付',
        42 => '玩聚',
        43 => '青海银行',
        44 => '玩聚-抖音担保交易支付',
        45 => '招商银行聚合支付',
        46 => '百度聚合支付',
        47 => '线下手工补单',
        48 => '中国银行',
        49 => '易宝独立收款',
        50 => '工商银行',
        51 => '预付卡',
        52 => '农行(云BMP)',
        53 => '预付卡',
        54 => '安徽农商行',
        55 => '易宝平台收款',
        56 => '绿聚支付',
        58 => '福建农信',
        59 => '聚富通',
        60 => '农行(创识)',
        61 => '通联支付',
        62 => '富友支付',
        63 => '酷享支付',
        64 => '自贡银行',
        65 => '预售券权益支付',
        66 => '慧徕店支付',
        67 => '预存码权益支付',
        68 => '星沙农商',
        69 => '网联商务',
        70 => '金华银行',
        71 => '悦航支付',
        72 => '拉卡拉支付',
        73 => '茶卡自有支付',
        74 => '无需支付',
        75 => 'TLinx',
        //76~85这10个是自定义线下收款预留 如果商家有配置会通过java接口获取到后与这个合并
        76 => '线下收款1',//线下收款1
        77 => '线下收款2',//线下收款2
        78 => '线下收款3',//线下收款3
        79 => '线下收款4',//线下收款4
        80 => '线下收款5',//线下收款5
        81 => '线下收款6',//线下收款6
        82 => '线下收款7',//线下收款7
        83 => '线下收款8',//线下收款8
        84 => '线下收款9',//线下收款9
        85 => '线下收款10',//线下收款10
        86 => '通联支付',//非独立收款
        87 => '小红书-担保支付',
        255 => '未知',
        88 => '西安银行',
        89 => '富滇银行',
        90 => '浙江农信',
    ],
    'pay_type' => [ //交易记录支付类型
        0  => '帐号资金',
        1  => '支付宝',
        2  => '供应商处可用资金',
        3  => '供应商信用额度设置',
        4  => '财付通',
        5  => '银联',
        6  => '环迅',
        9  => '现金',
        10 => '会员卡',
        11 => '拉卡拉（商户）',
        12 => '拉卡拉（平台）',
        13 => '',
        14 => '',
        15 => '',
        16 => '',
        17 => '',//liubb
        25 => '招行一网通支付',
        26 => '银联商务',
        27 => '银联商务pos',
        28 => '威富通',
        29 => '易宝',
        30 => '云闪付',
        31 => '农行',
        32 => '建行',
        33 => '福州市民卡-博思支付',
        36 => 'POS支付',
        45 => '丰收互联支付',
        47 => '江西农商行',
        48 => '银联会员卡',
        49 => '盛世通支付',
        50 => '玩聚',
        51 => '青海银行',
        52 => '玩聚-抖音担保交易支付',
        53 => '招商银行',
        54 => '百度聚合支付',
        55 => '线下手工补单',
        56 => '中国银行',
        57 => '易宝独立收款',
        58 => '工商银行',
        59 => '预付卡',  //这个是预付卡代付
        60 => '农行(云BMP)',
        61 => '预付卡',
        62 => '安徽农商行',
        63 => '易宝平台收款',
        64 => '绿聚支付',
        67 => '福建农信',
        68 => '聚富通',
        69 => '农行(创识)',
        70 => '通联支付',
        71 => '富友支付',
        72 => '酷享支付',
        73 => '自贡银行',
        74 => '预售券权益支付',
        75 => '慧徕店支付',
        76 => '预存码权益支付',
        77 => '星沙农商',
        78 => '网联商务',
        79 => '金华银行',
        80 => '悦航支付',
        81 => '拉卡拉支付',
        82 => '茶卡自有支付',
        83 => 'TLinx',
        //84~93这10个是自定义线下收款预留
        84 => '线下收款1',//线下收款1
        85 => '线下收款2',//线下收款2
        86 => '线下收款3',//线下收款3
        87 => '线下收款4',//线下收款4
        88 => '线下收款5',//线下收款5
        89 => '线下收款6',//线下收款6
        90 => '线下收款7',//线下收款7
        91 => '线下收款8',//线下收款8
        92 => '线下收款9',//线下收款9
        93 => '线下收款10',//线下收款10
        94 => '通联支付',
        95 => '小红书-担保支付',
        96 => '西安银行',
        97 => '富滇银行',
        98 => '浙江农信',
    ],
    // pft_alpay_rec.sourceT字段与ss_order.pay_mode字段映射
    'sourceT_pay_mode_map' => [
        0 => 1,
        1 => 5,
        2 => 7,
        3 => 8,
        4 => 9,
        5 => 10,
        6 => 11,
        7 => 12,
        8 => 14,
        9 => 16,
        11 => 15,
        12 => 14,
        13 => 16,
        14 => 36, // POS支付
        15 => 17,// liubb
        16 => 25,// 招行一网通支付
        17 => 26,//银联商务
        18 => 27,//银联商务Pos
        19 => 28,//农行（威富通）
        20 => 29,//易宝
        21 => 30,//云闪付
        22 => 31,// 农行
        23 => 32,// 建行
        24 => 34,//丰收互联支付
        25 => 39,//江西农商行
        26 => 40,//银联会员卡
        27 => 41,//盛世通支付
        28 => 42,//玩聚
        29 => 43,//青海银行
        30 => 44,//玩聚-抖音担保交易支付
        31 => 45,//招行
        32 => 46,//百度支付
        33 => 33,// 福州市民卡
        34 => 47,//线下手工补单
        35 => 48,//中国银行
        36 => 49,//易宝独立收款
        37 => 50,//工商银行
        38 => 51,//预付卡
        39 => 52,//农行(云BMP)
        40 => 54,//安徽农商行
        41 => 55,//易宝新版接口，票付通收款
        42 => 56,//绿聚支付
        43 => 58,//福建农信
        44 => 59,//聚富通
        45 => 60,//农行(创识)
        46 => 61,//通联支付
        47 => 62,//富友支付
        48 => 63,//酷享支付
        49 => 64,//自贡银行
        50 => 66,//慧徕店支付
        51 => 68,//星沙农商
        52 => 69,//网联商务
        53 => 70,//金华银行
        54 => 71,//悦航支付
        55 => 72,//拉卡拉支付
        56 => 73,//茶卡自有支付
        57 => 75,//TLinx
        58 => 76,//线下收款1
        59 => 77,//线下收款2
        60 => 78,//线下收款3
        61 => 79,//线下收款4
        62 => 80,//线下收款5
        63 => 81,//线下收款6
        64 => 82,//线下收款7
        65 => 83,//线下收款8
        66 => 84,//线下收款9
        67 => 85,//线下收款10
        68 => 86,//通联支付
        69 => 87,//小红书-担保支付
        70 => 88,//西安银行
        71 => 89,//富滇银行
        72 => 90,//浙江农信
    ],
    // pft_alpay_rec.sourceT字段
    'sourceT_pay_type_map' =>[
        0  => 1,
        1  => 4,
        2  => 5,
        3  => 6,
        4  => 9,
        5  => 10,
        6  => 11,
        7  => 12,
        8  => 13,
        9  => 16,
        10 => 15,
        11 => 14,
        12 => 13,
        13 => 16,// 招商聚合支付13微信，16支付宝
        14 => 36, // POS支付
        15 => 17,// liubb
        16 => 25,// 招行一网通支付
        17 => 26,//银联商务
        18 => 27,//银联商务pos
        19 => 28,//农行（威富通）
        20 => 29,//易宝
        21 => 30,//云闪付
        22 => 31,//农行
        23 => 32,//建行
        24 => 45,//丰收互联支付
        25 => 47,//江西农商行
        26 => 48,//银联会员卡
        27 => 49,//盛世通支付
        28 => 50,//玩聚
        29 => 51,//青海银行
        30 => 52,//玩聚-抖音担保交易支付
        31 => 53,//招行
        32 => 54,//百度
        33 => 33,// 福州市民卡
        34 => 55,//线下手工补单
        35 => 56,//中国银行
        36 => 57,//新版易宝:独立收款
        37 => 58,//工商银行
        38 => 59,//预付卡
        39 => 60,//农行(云BMP)
        40 => 62,//安徽农商行
        41 => 63,//新版易宝:平台收款
        42 => 64,//绿聚支付
        43 => 67,//福建农信
        44 => 68,//聚富通
        45 => 69,//农行(创识)
        46 => 70,//通联支付
        47 => 71,//富友支付
        48 => 72,//酷享支付
        49 => 73,//自贡银行
        50 => 75,//慧徕店支付
        51 => 77,//星沙农商
        52 => 78,//网联商务
        53 => 79,//金华银行
        54 => 80,//悦航支付
        55 => 81,//拉卡拉支付
        56 => 82,//茶卡自有支付
        57 => 83,//TLinx
        58 => 84,//线下收款1
        59 => 85,//线下收款2
        60 => 86,//线下收款3
        61 => 87,//线下收款4
        62 => 88,//线下收款5
        63 => 89,//线下收款6
        64 => 90,//线下收款7
        65 => 91,//线下收款8
        66 => 92,//线下收款9
        67 => 93,//线下收款10
        68 => 94,//通联支付
        69 => 95,//小红书-担保支付
        70 => 96,//西安银行
        71 => 97,//富滇银行
        72 => 98,//浙江农信
    ],

    //
    'sourceT_track_source_map' =>[
        0  => 9,//支付宝
        1  => 10,//微信
        17 => 39,//银联商务
        18 => 40,//银联商务pos
        19 => 41,//农行（威富通）
        20 => 42,//易宝
        21 => 43,//云闪付
        22 => 47,//农行
        23 => 48,//建行
        24 => 49,//丰收互联支付
        25 => 55,//江西农商行
        26 => 56,//银联会员卡
        27 => 58,//盛世通支付
        28 => 59,//玩聚
        29 => 60,//青海银行
        30 => 63,//玩聚-抖音担保交易支付
        31 => 64,//招行
        32 => 65,//百度小程序
        35 => 67,//中国银行
        36 => 68,//易宝独立收款
        37 => 70,//工商银行
        39 => 72,//农行(云BMP)
        40 => 73,//安徽农商行
        41 => 74,//易宝平台收款
        42 => 75,//绿聚支付
        43 => 76,//福建农信
        44 => 77,//聚富通
        45 => 78,//农行创识
        46 => 79,//通联支付
        47 => 80,//富友支付
        48 => 82,//酷享支付 81已经被占用
        49 => 83,//自贡银行
        50 => 84,//慧徕店支付
        51 => 87,//星沙农商
        52 => 88,//网联商务
        53 => 89,//金华银行
        54 => 90,//悦航支付
        55 => 91,//拉卡拉支付
        56 => 92,//茶卡自有支付
        57 => 93,//TLinx
        58 => 94,//线下收款1
        59 => 95,//线下收款2
        60 => 96,//线下收款3
        61 => 97,//线下收款4
        62 => 98,//线下收款5
        63 => 99,//线下收款6
        64 => 100,//线下收款7
        65 => 101,//线下收款8
        66 => 102,//线下收款9
        67 => 103,//线下收款10
        68 => 104,//通联支付
        69 => 105,//小红书-担保支付
        70 => 106,//西安银行
        71 => 107,//富滇银行
        72 => 108,//浙江农信
    ],

    'cashier_desk_to_sourceT' => [//支付平台收银台模式对应的sourceT和Track_source
        5  => [
            'sourceT' => 36,
            'track_source' => 68,
        ],
        12  => [
            'sourceT' => 31,
            'track_source' => 64,
        ],
        13  => [
            'sourceT' => 32,
            'track_source' => 65,
        ],
        3   => [
            'sourceT' => [
                'micropay' => 18,
                'order'    => 17,
            ],
            'track_source' => [
                'micropay' => 40,
                'order'    => 39,
            ],
        ],
        14  => [
            'sourceT' => 35,
            'track_source' => 67,
        ],
        15  => [
            'sourceT' => 37,
            'track_source' => 70,
        ],
        16  => [
            'sourceT' => 39,
            'track_source' => 72,
        ],
    ],
    'order_mode' => [
        //下单方式
        //0=>'正常分销商下单',
        //1=>'普通用户支付',
        //2=>'用户手机支付',
        9  => '会员卡购票',
        10 => '云票务',
        11 => '微商城',
        12 => '自助机',
        13 => '二级店铺',
        14 => '闸机购票',
        15 => '智能终端',
        16 => '计调下单',
        17 => '淘宝码商(飞猪)',
        18 => '年卡',
        19 => '微平台',
        20 => 'OTA',
        21 => '兑换码购票',
        22 => '一卡通',
        23 => '套票子票下单',
        24 => '团队订单',
        33 => 'e福州',
        34 => '拼团',
        35 => '积分商城',
        36 => 'POS支付',
        38 => '招行',
        39 => '银联商务',
        40 => '银联pos支付',
        41 => '威富通',
        42 => '易宝',
        43 => '微信-微票房',
        44 => '报团计调下单',
        45 => '云闪付下单',
        46 => '断网离线订单',
        47 => '员工卡',
        48 => '手牌',
        49 => '桌面云票务',
        50 => 'APP',
        55 => '特殊团队预约',
        56 => '小程序',
        57 => '抖音-微票房',
        58 => '阿里供销',
        59 => '团购导码',
        60 => '京津冀年卡',
        61 => '水韵江苏年卡',
        62 => '武汉老年卡',
        63 => '宁镇扬游园卡',
        64 => '预售券兑换',
        65 => '景旅纵横动态码',
        66 => '丝路风情年卡',
    ],
    'order_mode_track_map' => [ //下单方式对应追踪记录表
        0  => 16, //'正常分销商下单',
        1  => 16, // '普通用户支付',
        2  => 22, //'用户手机支付',
        9  => 25, //'会员卡购票'
        10 => 4, //'云票务'
        11 => 22, //'微信商城',
        12 => 2, //'自助机',
        13 => 23, //pc店铺
        14 => 5, //'闸机购票'
        15 => 20, //'智能终端',
        16 => 16, //'计调下单',
        17 => 24, //'淘宝码商',
        18 => 26, //'年卡',
        19 => 22, //'微信端',
        20 => 24, //'OTA',
        22 => 30, //一卡通
        23 => 31, //套票子票
        24 => 16, //抱团
        33 => 33, //e福州下单
        34 => 34, //拼团下单
        35 => 37, // 积分商城
        38 => 38, //招行
        39 => 39, //银联商务,
        40 => 40, // 银联pos支付
        41 => 41,//威富通
        42 => 42, //易宝
        43 => 22, //微票房
        44 => 44, //报团计调下单
        45 => 43, // 云闪付
        46 => 45,  // 断网离线订单
        49 => 51, //桌面云票务
        50 => 50,  //APP
        55 => 57,  //特殊团队预约
        56 => 22,  //微信小程序
        57 => 22,  //抖音-微票房
        59 => 61,  //团购导码
        58 => 62,  //阿里供销
        64 => 85,  //预售券兑换
    ],
    'channel_track_cancel_map'  => [   //取消的渠道对应下面正式的取消渠道
        4  => 11,        //云票务渠道
        8  => 12,        //手持机渠道
        22 => 10,        //微商城渠道
        59 => 56,        //玩聚渠道
        81 => 57,        //售后取消-平台渠道
        20 => 12,        //安卓终端机渠道
    ],
    'cancel_modify_track_map' => [   //取消真实渠道对应追踪表
        0  => 16,  //PC端对应内部接口
        1  => 22,  //用户手机取消对应微商城
        2  => 16,  //团队订单取消
        3  => 16,  //审核取消
        4  => 16,  //下单失败取消
        5  => 16,  //支付后失败取消
        6  => 16,  //系统自运行取消过期的
        7  => 16,  //系统自运行取消秒杀的
        8  => 16,  //预消费自动取消
        9  => 19,  //有设置订单过期后自动取消的
        10 => 22,  //微商城渠道
        11 => 4,   //云票务渠道
        12 => 20,   //安卓终端机
        13 => 16,   //未知的渠道，全部内部接口
        14 => 22,   //微平台
        15 => 22,   //微票房
        16 => 16,   //ota的取消通用
        23 => 31,  //套票子票的
        20 => 22,  //新版微商城
        21 => 16,  //团单审核取消
        17 => 16,  //合并付款现金支付失败取消
        18 => 16,  //合并付款终端取消
        19 => 16,  //合并付款终端取消新
        22 => 16,  //终端未支付取消
        24 => 16,  //年卡失败取消
        25 => 16,  //导码购票取消
        26 => 16,  //p1的对外取消修改
        27 => 16,  //拼团取消
        28 => 16,  //手牌主动取消
        30 => 16,  //外部直接调用我们的取消接口
        29 => 16,  //套票子票强制取消
        31 => 16,  //撤销测改
        33 => 21,  //验证服务器那边的
        34 => 16,  //淘宝的取消
        32 => 16,  //团购的取消
        35 => 16,  //团购导码页面取消
        36 => 16,  //二级店铺
        37 => 16,  //线下在线收款支付后失败取消
        38 => 16,  //特产批量下单失败取消
        40 => 16,  //三方下单失败取消
        41 => 51,  //桌面云票务取消
        42 => 51,  //桌面云票务撤销
        39 => 16,  //本地系统请求取消
        43 => 16,  //团单平台购买在线支付生成合并付款失败
        44 => 16,  //云票务未支付取消
        45 => 16,  //平台指定idx退票
        46 => 16,  //云票务指定idx
        47 => 16,  //在线支付失败后未支付的取消
        48 => 4,   //新版温泉取消
        49 => 16,  //预售券兑换取消
        56 => 59,   //玩聚项目那边-微信小程序
        57 => 81,   //售后取消
    ],
    'order_status'               => [
        0  => '未验证',
        1  => '已验证',
        2  => '已过期',
        3  => '已取消',
        4  => '待确认',
        5  => '已撤改',
        6  => '已撤销',
        7  => '部分验证',
        8  => '订单完结',
        9  => '已删除',
        99 => '未支付',
    ],

    //订单追踪记录来源
    'track_source'               => [
        0  => '终端机',
        1  => '软终端',
        2  => '自助机',
        3  => '外部通知更新',
        4  => '云票务',
        5  => '云闸机',
        6  => 'PC-支付宝',
        7  => '手机支付宝',
        8  => '支付宝刷卡',
        9  => '支付宝扫码',
        10 => '微信支付',
        11 => '微信刷卡',
        12 => '微信扫码',
        13 => 'PC-银联',
        14 => '手机-银联',
        15 => 'PC-环迅',
        16 => '内部接口',
        17 => '外部接口',
        18 => 'undefined',
        19 => '自运行服务',
        20 => '安卓智能终端机',
        21 => '验证服务器',
        22 => '微信商城',
        23 => '二级店铺',
        24 => 'OTA',
        25 => '会员购票',
        26 => '年卡特权',
        27 => '平安银行',
        28 => '内部接口', //此28为数据修补，未避免用户疑问显示文字同16-内部接口
        29 => '微信',//'民生乐收银', 2019-04-01 21:12:18由民生乐收银改为微信
        30 => '一卡通',
        31 => '套票子票',
        32 => '子景点验证',
        33 => '市民卡',
        34 => '拼团',
        35 => '法利兰一卡通',
        36 => 'mini景区端',
        37 => '积分商城',
        38 => '招行',
        39 => '银联商务',
        40 => '银联pos支付',
        41 => '威富通',
        42 => '易宝',
        43 => '云闪付',
        44 => '报团计调下单',
        45 => '断网离线订单',
        46 => '强制核销',
        47 => '农行聚合',
        48 => '建行聚合',
        49 => '丰收互联',
        50 => 'app',
        51 => '桌面云票务',
        52 => '桌面云票务子景点验证',
        53 => '子景点过期自动验证',
        54 => '子景点快速验证',
        55 => '江西农商行',
        56 => '银联会员卡',
        57 => '特殊团队预约',
        58 => '盛世通码支付',
        59 => '玩聚',
        60 => '青海银行',
        61 => '团购导码',
        62 => 'OTA-阿里供销',
        63 => '玩聚-抖音担保交易支付',
        64 => '招商银行聚合',
        65 => '百度聚合支付',
        66 => '线下手工补单',
        67 => '中国银行',
        68 => '易宝独立收款',
        69 => '手持机(人脸)',
        70 => '工商银行',
        71 => '预付卡',
        72 => '农行(云BMP)',
        73 => '安徽农商行',
        74 => '易宝平台收款',
        75 => '绿聚支付',
        76 => '福建农信',
        77 => '聚富通',
        78 => '农行(创识)',
        79 => '通联支付',
        80 => '富友支付',
        81 => '平台',
        82 => '酷享支付',
        83 => '自贡银行',
        84 => '慧徕店支付',
        85 => '预售券兑换',
        86 => '预存码预约',
        87 => '星沙农商',
        88 => '网联商务',
        89 => '金华银行',
        90 => '悦航支付',
        91 => '拉卡拉支付',
        92 => '茶卡自有支付',
        93 => 'TLinx',
        //94~103为线下收款占用 需要的时候通过java接口转换改造
        94 => '线下收款1',
        95 => '线下收款2',
        96 => '线下收款3',
        97 => '线下收款4',
        98 => '线下收款5',
        99 => '线下收款6',
        100 => '线下收款7',
        101 => '线下收款8',
        102 => '线下收款9',
        103 => '线下收款10',
        104 => '通联支付',
        105 => '小红书-担保支付',
        106 => '西安银行',
        107 => '富滇银行',
        108 => '浙江农信',
    ],

    //订单追踪操作类型
    'track_action'  => [
        0  => '下单',
        1  => '修改',
        2  => '取消',
        3  => '出票',
        4  => '支付',
        5  => '验证',
        6  => '撤销',
        7  => '撤改',
        8  => '重打印',
        9  => '离线订单下载',
        10 => '处理退票申请',
        11 => '提交退票申请',
        12 => '过期',
        13 => '同意退票申请',
        14 => '拒绝退票申请',
        15 => '核销',
        16 => '订单加票',
        17 => '完结',
        18 => '重发短信',
        19 => '激活码兑换',
        20 => '退票处理失败',
        21 => '修改基础信息',
        22 => '改签',
        23 => '提交改签申请',
        24 => '同意改签申请',
        25 => '拒绝改签申请',
        26 => '审核（人工）',
        27 => '发货',
        28 => '修改物流',
        29 => '提交退货申请',
        30 => '拒绝退货申请',
        31 => '同意退货申请',
        32 => '订单预约',
        33 => '入园',
        35 => '变更有效期',   //记录下（track表里面似乎没有）
        36 => '积分消费',
        37 => '积分退还',
        38 => '售后取消',
        39 => '兑换权益', //预售券兑换权益
    ],

    //自动提现默认配置
    'withdraw_default'           => array(
        'fee_cut_way'          => [ //提现手续费从哪里扣除
            '0' => '提现金额扣除',
            '1' => '账户余额扣除',
        ],
        'bank_type'            => [ //银行卡类型名
            'alipay', //支付宝
            'bank1', //银行卡1
            'bank2', //银行卡2
        ],

        'min_withdraw_money'   => 10, //最小提现金额，单位元
        'max_withdraw_money'   => 3000000, //最大提现金额，单位元
        'limit_withdraw_money' => 200, //临界提现金额，单位元
        'limit_service_charge' => 2, //如果没有达到limit_withdraw_money元时的提现手续费，单位元

        //日提现配置
        'day'                  => array(
            'service_fee'       => 5, //默认千分之五
            'reserve_money'     => 200, //默认冻结多少钱
            'reserve_scale'     => 20, //默认冻结的比例
            'limit_money'       => 200, //最低需要达到多少才能体现 - 单位元
            'auth_money'        => 50000, //金额达到多少需要财务审核 - 单位元
            'low_service_money' => 2, //最低提现手续费 - 单位元
        ),
        //周提现配置
        'week'                 => array(
            'service_fee'       => 4, //默认千分之五
            'reserve_money'     => 200, //默认冻结多少钱
            'reserve_scale'     => 20, //默认冻结的比例
            'limit_money'       => 200, //最低需要达到多少才能体现 - 单位元
            'auth_money'        => 50000, //金额达到多少需要财务审核 - 单位元
            'low_service_money' => 2, //最低提现手续费 - 单位元
        ),
        //月提现配置
        'month'                => array(
            'service_fee'       => 0, //默认千分之五
            'reserve_money'     => 200, //默认冻结多少钱
            'reserve_scale'     => 20, //默认冻结的比例
            'limit_money'       => 200, //最低需要达到多少才能体现 - 单位元
            'auth_money'        => 50000, //金额达到多少需要财务审核 - 单位元
            'low_service_money' => 2, //最低提现手续费 - 单位元
        ),
    ),
    'warning_mail_list'          => [
        '<EMAIL>', //cgp
        '<EMAIL>', //pzp
        '<EMAIL>', //zdw
    ],
    'kefu_mail_list'             => [
    ],
    //微信报警通知
    'wechat_warning_openid'      => array(
        'oNbmEuDdAEWDS_a02HYFlzNYFUTg', //Guangpeng Chen
    ),
    //----报表执行失败时发送短信报警 BEGIN---
    //预订 验证 取消 撤销
    'statistics_V1'              => [
    ],
    //应收应付
    'statistics_V2'              => [
    ],
    //交易汇总
    'trans_report'               => [
    ],
    //----报表执行失败时发送短信报警 END---

    // 微信菜单动作,供用户选择
    'wechat_menu_action'         => [

        // 散客菜单
        'sankeMenu' => [
            array(
                'name' => '微商城',
                'type' => 'view',
                'code' => str_replace('wx', '%DOMAIN%', MOBILE_DOMAIN) . 'r/Mall_Member/autoLogin/',
            ),
            array(
                'name' => '限时抢购',
                'type' => 'view',
                'code' => str_replace('wx', '%DOMAIN%', MOBILE_DOMAIN) . 'wx/c/seckill_list.html',
            ),
            array(
                'name' => '拼团',
                'type' => 'view',
                'code' => str_replace('wx', '%DOMAIN%', MOBILE_DOMAIN) . 'wx/c/pintuan.html',
            ),
            array(
                'name' => '砍价',
                'type' => 'view',
                'code' => str_replace('wx', '%DOMAIN%', MOBILE_DOMAIN) . 'wx/c/bargain_activity.html',
            ),
            array(
                'name' => '全民营销',
                'type' => 'view',
                'code' => str_replace('wx', '%DOMAIN%', MOBILE_DOMAIN) . 'h5/alldis/index',
            ),
            array(
                'name' => '新版微商城',
                'type' => 'view',
                'code' => str_replace('wx', '%DOMAIN%', MOBILE_DOMAIN) . 'h5',
            ),
        ],

        // 分销商菜单
        'disMenu'   => [
            array(
                'name' => '微分销',
                'type' => 'view',
                'code' => str_replace('wx', 'm', MOBILE_DOMAIN) . 'wx/b/login.html',
            ),
            array(
                'name' => '微平台',
                'type' => 'view',
                'code' => str_replace('wx', 'm', MOBILE_DOMAIN) . 'wx/b/login.html',
            ),
            array(
                'name' => '手机号查单',
                'type' => 'view',
                'code' => MOBILE_DOMAIN . 'html/mobile_search_order.html',
            ),
            array(
                'name' => '终端验证',
                'type' => 'view',
                'code' => MOBILE_DOMAIN . 'index.php?appid=%WECHAT_APPID%&fid=%FID%&m=terminal&account=%DOMAIN%',
            ),
        ],

        // 会员卡菜单
        'cardMenu'  => [
            array(
                'name' => '营销活动',
                'type' => 'view',
                'code' => MOBILE_DOMAIN . 'index.php?appid=%WECHAT_APPID%&fid=%FID%&m=sale_youhui&account=%DOMAIN%',
            ),
        ],
    ],

    //自动清分大于5W，需要通知的微信用户
    'finance_lady_song'          => 'oNbmEuCJxEEnEbfrsdYhbDKxp9LY',

    //数据校验异常，需要通知的微信用户
    'check_warning_openid'       => array(
        'oNbmEuDdAEWDS_a02HYFlzNYFUTg', //Guangpeng Chen
        'oNbmEuPBbY8Mwk2R74q9tlaN0Zo4', //dwer
    ),

    //接口出现故障，需要通知的微信用户
    'api_warning_openid'         => array(
        'oNbmEuDdAEWDS_a02HYFlzNYFUTg', //Guangpeng Chen
        'oNbmEuPBbY8Mwk2R74q9tlaN0Zo4', //dwer
        'oNbmEuFXYw-3bD1E39LWmnINpurA', //pzp
    ),
    //监控数据查看权限
    'mobile_data_monitor'        => [
        'oNbmEuDdAEWDS_a02HYFlzNYFUTg' =>['name'=>'Guangpeng Chen','limit'=>false],
    ],
    //接口出现故障，需要通知的短信用户
    'api_warning_mobile'         => array(
        '***********', //pzp
        '***********', //dwer
        '***********', //Guangpeng Chen
    ),

    //第三方系统出现故障需要通知的用户
    'third_system_notify_mobile' => array(),

    //线上购买应用中心模块需要通知的用户
    'app_buy_online'             => array(
        'oNbmEuN_yCKV8zoNKbAG0_LrgKUo', //吴燕玫
        'oNbmEuAeoFt7TKoc8zZmxEbxazvM', //王佳鑫
        'oNbmEuAbDJ_CStUcru5zF3b23vUc', //陈婷
        'oNbmEuNDn2bZmqvBvXyYAWdXJfWE', //薛一票
    ),

    //商家信息
    'group_order_shop_typpe'     => array(
        0  => "去哪儿",
        3  => "环企",
        4  => "票联",
        5  => "天时同城",
        6  => "华侨城",
        7  => "自我游",
        8  => "酷秀",
        9  => "智游宝",
        10 => "九天达",
        11 => "青岛新锐",
        12 => "智慧龙虎山",
        13 => '百度直达号',
        20 => "美团直连V1",
        21 => "美团发码",
        22 => "百度糯米",
        23 => "美团直连V2",
        24 => "票付通转分销",
        25 => "同程",
        26 => "贝竹",
        27 => "畅游通",
        28 => "滑沙",
        29 => "鑫海科技",
        35 => "皇家海洋",
        40 => "联合集散",
        41 => "锦航云天",
        42 => "九天达V1.0",
        43 => "居游网",
        44 => "票管通",
        45 => "票管家",
        46 => "小径平台",
        47 => "谢谢网",
    ),
    // 广告页面配置
    'page_type'  => [
        3 => '微信页面',
        4 => '微信消息',
        5 => '短信链接', // 管理员才有权限配置
    ],
    //订单来源
    'order_source'               => [
        0  => '',
        1  => '普通用户支付',
        2  => '用户手机支付',
        9  => '会员卡购票',
        10 => '云票务',
        11 => '微信商城',
        12 => '自助机',
        13 => '二级店铺',
        14 => '闸机购票 ',
        15 => '智能终端',
        16 => '计调下单',
        17 => '淘宝码商',
        18 => '年卡',
        19 => '微信端',
        20 => '外部接口OTA下单',
        33 => 'e福州',
        43 => '微票房',
    ],

    //支付方式列表
    'payway_list'                => [
        '0'  => '余额支付',
        '1'  => '支付宝',
        '2'  => '授信支付',
        '3'  => '产品自销',
        '4'  => '现场支付',
        '5'  => '微信支付',
        '6'  => '会员卡支付',
        '7'  => '银联支付',
        '8'  => '环迅支付',
        '9'  => '现金支付',
        '10' => '会员卡',
        '11' => '拉卡拉',
        '12' => '拉卡拉商户',
        '13' => '年卡特权支付',
        '14' => '微信支付(民生)',
        '15' => '平安银行',
        '16' => '支付宝(民生)',
        '17' => '会员卡支付',
        '18' => '计时卡支付',
        '25' => '一网通支付',
        '26' => '银联商务支付',
        '27' => '银联商务POS通',
        '28' => '威富通支付',
        '29' => '易宝',
        '30' => '云闪付',
        '31' => '农行',
        '32' => '建行',
        '33' => '博思支付',
        '35' => '积分支付',
        '36' => 'POS支付',
        '37' => '(身份证)一卡通',
        '38' => '预存余额支付',
        '34' => '丰收互联',
        '39' => '江西农商行',
        '40' => '银联会员卡',
        '41' => '盛世通支付',
        '42' => '玩聚',
        '43' => '青海银行',
        '44' => '玩聚-抖音担保交易支付',
        '45' => '招商银行聚合支付',
        '46' => '百度聚合支付',
        '47' => '线下手工补单',
        '48' => '中国银行',
        '49' => '易宝独立收款',
        '50' => '工商银行',
        '51' => '预付卡',  //这个是预付卡代付
        '52' => '农行(云BMP)',
        '53' => '预付卡',
        '54' => '安徽农商行',
        '55' => '易宝平台收款',
        '56' => '绿聚支付',
        '58' => '福建农信',
        '59' => '聚富通',
        '60' => '农行(创识)',
        '61' => '通联支付',
        '62' => '富友支付',
        '63' => '酷享支付',
        '64' => '自贡银行',
        '65' => '预售券权益支付',
        '66' => '慧徕店支付',
        '67' => '预存码权益支付',
        '68' => '星沙农商',
        '69' => '网联商务',
        '70' => '金华银行',
        '71' => '悦航支付',
        '72' => '拉卡拉支付',
        '73' => '茶卡自有支付',
        '74' => '无需支付',
        '75' => 'TLinx',
        //76~85这10个是自定义线下收款预留 如果商家有配置会通过java接口获取到后与这个合并
        '76' => '线下收款1',//线下收款1
        '77' => '线下收款2',//线下收款2
        '78' => '线下收款3',//线下收款3
        '79' => '线下收款4',//线下收款4
        '80' => '线下收款5',//线下收款5
        '81' => '线下收款6',//线下收款6
        '82' => '线下收款7',//线下收款7
        '83' => '线下收款8',//线下收款8
        '84' => '线下收款9',//线下收款9
        '85' => '线下收款10',//线下收款10
        '86' => '通联支付',//非独立收款
        '87' => '小红书-担保支付',
        '255' => '未知',
        '88' => '西安银行',
        '89' => '富滇银行',
        '90' => '浙江农信',
    ],
    'terminal_code'              => [
        '2201' => [
            'defaults'     => '订单已验证或不存在', //默认
            'change_check' => '订单状态已修改,请重新查询订单', //改单验证
            'query'        => '订单未找到,可能原因:订单未支付或订单已使用', //订单查询
        ],
        '0702' => [
            'defaults' => '订单游玩时间未到',
            'revoke'   => '不允许二次申请撤销撤改', //撤销撤改
            'check'    => '游玩时间未到', //验证
        ],
        '0802' => [
            'defaults' => '有重名',
            'revoke'   => '订单撤销撤改申请成功', //撤销撤改
        ],
        '2001' => [
            'defaults' => '订单已过期,终端不可验', //默认
            'check'    => '订单已失效', //验证
        ],
        '0103' => [
            'defaults' => '套票不能单独改单,请使用【凭证号】快速验票',
            'revoke'   => '套票子票不允许撤改', //撤销撤改
        ],
        '0104' => [
            'defaults' => '不可退而且可提现的订单不允许撤改',
        ],
        '0107' => [
            'defaults' => '快递发货的特产产品，待发货状态下不支持验证',
        ],
        '0203' => [
            'defaults'    => '已过取消时间', //默认
            'batch_check' => '无解的错误', //分批验证
        ],
        '1901' => [
            'defaults' => '订单已验证',
        ],
        '2501' => [
            'defaults' => '订单已验证',
        ],
        '2502' => [
            'defaults' => '订单正在审核中',
        ],
        '0502' => [
            'defaults' => '订单未支付',
        ],
        '0602' => [
            'defaults' => '订单已撤销',
        ],
        '0902' => [
            'defaults' => '验证时间未到',
        ],
        '2B0A' => [
            'defaults' => '订单需要审核',
        ],
        '0003' => [
            'defaults' => '格式错误',
        ],
        '0105' => [
            'defaults' => '使用优惠券的订单不支持改单验证',
        ],
        '0106' => [
            'defaults' => '使用优惠券的订单不支持分批验证',
        ],
        '0001' => [
            'defaults' => '修改人数大于支付人数',
        ],
        '0901' => [
            'defaults' => '撤销撤改无需审核',
        ],
        '6500' => [
            'defaults' => '服务器连接失败',
        ],
        '550b' => [
            'defaults' => '验证票数有误',
        ],
        '550B' => [
            'defaults' => '验证票数有误',
        ],
        '560B' => [
            'defaults' => '余票不足',
        ],
        '560b' => [
            'defaults' => '余票不足',
        ],
        '540b' => [
            'defaults' => '分批验证未开启',
        ],
        '570b' => [
            'defaults' => '日验证上限',
        ],
        '6f0a' => [
            'defaults' => '分批验证不支持此重打印方式',
        ],
        '6F0A' => [
            'defaults' => '分批验证不支持此重打印方式',
        ],
        '580b' => [
            'defaults' => '现场支付不支持分批验证',
        ],
        '580B' => [
            'defaults' => '现场支付不支持分批验证',
        ],
        '4b0b' => [
            'defaults' => '退票审核不支持改单验证',
        ],
        '4B0B' => [
            'defaults' => '退票审核不支持改单验证',
        ],
        '2C0A' => [
            'defaults' => '离线验证检查失败',
        ],
        '6f0b' => [
            'defaults' => '重打印次数已超过限定次数',
        ],
        '6F0B' => [
            'defaults' => '重打印次数已超过限定次数',
        ],
        '5005' => [
            'defaults' => '业务正在处理中...',
        ],
        '0903' => [
            'defaults' => '订单未找到',
            'revoke'   => '没有找到已经验证的订单', //撤销撤改
        ],
        '0905' => [
            'defaults' => '联票不允许撤改',
        ],
        '0906' => [
            'defaults' => '分批验证订单不允许撤销撤改',
        ],
        '0907' => [
            'defaults' => '数据添加失败',
        ],
        '0908' => [
            'defaults' => '使用优惠券的订单只允许撤销',
        ],
        '0909' => [
            'defaults' => '订单验证超过7天，不可撤改撤销',
        ],
        '2002' => [
            'defaults' => '一票一码凭证码不存在',
        ],
        '2003' => [
            'defaults' => '游客已入园',
        ],
        '5006' => [
            'defaults' => '重复入园',
        ],
        '5007' => [
            'defaults' => '该景点无法验证此订单',
        ],
        '5008' => [
            'defaults' => '验证凭据需要一致, 请使用身份证验证',
        ],
        '5011' => [
            'defaults' => '验证凭据需要一致, 请使用订单号或凭证号验证',
        ],
        '5009' => [
            'defaults' => '该项目次数已用完',
        ],
        '5010' => [
            'defaults' => '该凭证已达到最高验证次数',
        ],
        '5012' => [
            'defaults' => '拼团成功后才可以验证',
        ],
        '5013' => [
            'defaults' => '订单状态被锁定,请稍后再试',
        ],
        '5014' => [
            'defaults' => '未在门票所预约时间段，请使用强制验证',
        ],
        '6000' => [
            'defaults' => '订单未预约不可进，请先预约',
        ],
        '6001' => [
            'defaults' => '当前不在预约游玩日期',
        ],
        '6002' => [
            'defaults' => '当前不可验证，',
        ],
        '6006' => [
            'defaults' => '门票验证时间已过',
        ]
    ],
    //一票一码都门票，配置云票务门票打印强制使用凭证号,['供应商ID'=>1]
    'cts_ticket_print'           => [
        595947 => 1, //凤凰古城文化旅游投资股份有限公司
        //595955 => 1,//湘西烟雨凤凰旅游演艺有限公司
        595971 => 1, //黄龙洞投资股份有限公司
    ],
    //销售渠道
    'sale_channel'               => [
        5  => '分销后台',
        10 => '微平台',
        13 => '套票',
        12 => '计调下单',
        7  => '散客窗口',
        9  => '团队窗口',
        6  => '云闸机',
        8  => 'POS终端',
        3  => '自助机',
        4  => '会员卡',
        11 => '微票房',
        2  => 'pc商城',
        1  => '微商城',
        14 => '积分商城',
        15 => '云闪付',
        17 => '桌面云票务',  //16被啥ota的给占用了
        18 => 'APP',
        19 => '特殊团队预约',
        20 => '小程序',
        30 => '云票务',
    ],
    //演出支持套票限制销售渠道
    'pack_show_sale_channel'               => [
        5  => '分销后台',
        10 => '微平台',
        12 => '计调下单',
        7  => '散客窗口',
        9  => '团队窗口',
        1  => '微商城',
        20 => '小程序',
    ],
    // 微信小程序，不支持套票演出子票，商家列表
    'pack_show_wxxcxpro_sids' => [
        3385,
        23938,
        60347,
        110036,
        190524,
        432404,
        1253574,
        1601659,
        1767026,
        6022338,
        6573145,
        7517853,
        8226762,
        8718140,
        8983717,
        9700756,
        9983925,
        10926017,
        11602071,
        11790099,
        12191034,
        12958716,
        13875670,
    ],

    'code_arr'                   => [
        'SQ', //
        'TQ', //
        'YY', //
        'YP', //白水洋 开始
        'TE', //白水洋
        'BE', //白水洋
        'YT', //白水洋
        'YQ', //白水洋
        'EZ', //白水洋
        'DK', //白水洋
        'DH', //白水洋
        'DL',
        'DC',
        'EA',
        'YE',
        'DE',
        'DS',
        'DA',
        'QD',
        'TD',
        'BT',
        'BK',
        'LY',
        'DT',
        'E0',
        'EO',
        'MC',
        'LP',
        'LY',
        'BT',
        'FG',
        'ED',
        'LT',
        'XL',
        'BB',
        'QM',
        'TX',
        'BK',
        'CC',
        'YP',
        'TE',
        'BE',
        'YT',
        'M0',
        'YQ',
        'EZ',
        'M1',
        'M',
        'D',
        'T', //白水洋  结束
        'MZAA', // 妈祖公园全价
        'MZBB', // 妈祖公园半价
    ],
    'clearway_2_paymode' => [
        0 => [38,2,0],
        1 => [2],
        2 => [0],
        3 => [38],
        4 => [38, 2],//多个，请按照支付优先级排序
    ],

    // 渠道售检规则客户类型
    'channel_inspect_customer_type' => [
        1 => '直销',
        2 => '分销商',
    ],
    'operate_log_source' => [
        1 => '平台',
        2 => '微平台',
        3 => 'mini云',
        4 => '管理员后台',
        5 => '旧版微平台',
    ],
    'refund_biz_type' => [
        'order_refund'           => [0, '订单类型退款'],
        'one_card_refund'        => [1, '一卡通业务退款'],
        'under_refund'           => [2, '楠溪江线下退款'],
        'special_carriage'       => [3, '特产运费退款'],
        'deposit_refund'         => [4, '保证金退款'],
        'wx_shop_module'         => [5, '微商城店铺模块退款'],
        'travel_landing'         => [6, '小店入驻订金退款'],
        'one_card_online_refund' => [7, '一卡通在线退款'],
        'wan_ju_refund'          => [8, '玩聚退款'],
    ],
    //本地系统开通的供应商
    'local_system_supplier_ids' => [
        3385, 6970, 977938, 8621779,9712273,954117,11402510,11720939,13769393,2749,10249872,14552062,13995818,15883117,7855157,15796622,8983717
    ],
    //员工卡类型
    'staff_card_type' => [
        0 => '员工卡',
        1 => '订单二次确认卡',
    ],
    'special_pack_check' => [
        6970,
        8225326,
        380461,
        11108923,
        13769393,
        8508087,
        23973002,   // 利山涧景区
        27407252,   // 荷包岛旅游
        20870563,   // 齐齐哈尔水师温泉
        29821585,   // 瘦西湖也
        29413835,   // 乐华城88度温泉乐园开通1:1:1
        671943,     // 沈阳清河半岛温泉度假酒店有限公司
        31324336,	// 百色大王岭漂流
        26264059,	// 龙谷湾旅游休闲度假区
        3262552,    //dev测试瘦西湖账号改名
        2380719,    // 张伟荣
        15771654,       // 川旅文投
        7307175,        // 漫花庄园
        37945842,       // 长沙海底世界
        37948500,   // 河北黄金寨旅游开发有限责任公司
        37950206,	// 西夏陵景区
        16577379, // 南宁极地海洋世界
        10407539, // 南京固城湾
        37953780, // 烟台钟离湖农业投资有限
        1331428, // 宁夏丝路风情网络科技股份有限公司
        7219588, // 陕西自然博物馆
        9990741, // 寻梦小镇
        9864867, // 司徒小镇
        14286178, // 广西桂林八角寨开通盘山套票
        8621779, // 大连圣亚
        37961607, //沈阳故宫
        32167861, //沈阳总督府博物馆
    ],
    'verify_used' => [
        6970,3385,23523,9712273,97762,10442542,7037486,8936615,6558763,8621779,1631028,11108923,85331
    ],
    'pay_biz_type'=>[ // 支付业务类型
        1 => '计时卡订单',
        3 => '租赁押金订单',
    ],
    //特定用户允许他撤销多次
    'more_time_revoke' => [
        6970,7920916,11402510,15884097
    ],
    'tourist_voucher_type' => [
        1 => '身份证',
        2 => '护照',
        3 => '军官证',
        4 => '回乡证',
        5 => '台胞证',
        99 => '其他'
    ],

    //团单人脸同步模式
    'team_order_face_sync' => [
        1 => "同一身份证同步",
        2 => "同一序号同步",
        3 => "不同步",
    ],

    //游客身份类型
    'tourist_voucher_type_map' => [
        1  => '身份证',
        2  => '护照',
        3  => '军官证',
        4  => '回乡证',
        5  => '台胞证',
        6  => '外国人永久居留证',
        7  => '港澳通行证',
        8  => '台湾通行证',
        9  => '港澳居民居住证',
        10 => '台湾居民居住证',
        99 => '其他',
    ],
);
