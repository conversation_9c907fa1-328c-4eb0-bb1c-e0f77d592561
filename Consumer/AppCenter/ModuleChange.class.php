<?php

namespace Consumer\AppCenter;

/**
 * 应用中心变更kafka记录
 */
class ModuleChange
{
    public $args = [];

    public function perform()
    {
        $args = $this->args;
        pft_log('kafka/app_center_module_change', json_encode($args, JSON_UNESCAPED_UNICODE));

        //如果是开通电子发票的情况  需要为用户添加开票系统
        if ($args['module_tag'] == 'invoice_center') {
            $addRes = (new \Business\ElectronicInvoices\InvoiceApi())->addUserSystem($args['member_id']);
            pft_log('kafka/add_invoice_system', json_encode(['sid' => $args['member_id'], 'res' => $addRes], JSON_UNESCAPED_UNICODE));
        }

        //客源地统计应用变更操作消息 tag=tourist_source_area_statistics
        // if ($args['module_tag'] == 'tourist_source_area_statistics') {
        //     $action   = $args['action'] ?? 0; //操作类型 1开启 2关闭
        //     $memberId = $args['member_id'] ?? 0; //商户id
        //     //参数校验
        //     if (!$action || !$memberId) {
        //         pft_log('kafka/tourist_source_area_statistics',
        //             json_encode([$args, '参数错误'], JSON_UNESCAPED_UNICODE));
        //
        //         return false;
        //     }
        //
        //     //操作时间
        //     $changeTime = $args['change_time'] ?? time() . "000";
        //     //毫秒解析为秒
        //     $changeTime = strtotime(date('Y-m-d H:i:s', $changeTime / 1000));
        //
        //     //操作应用业务处理，后续可以丢到统一的队列中，通过tag进行区分
        //     $touristSourceAreaBiz = new \Business\Statistics\TouristSourceArea\MemberConfig();
        //
        //     //应用变更处理
        //     $res = $touristSourceAreaBiz->moduleChange($memberId, $action, $changeTime);
        //
        //     //变更记录日志
        //     pft_log('kafka/tourist_source_area_statistics',
        //         json_encode(['params' => [$memberId, $action, $changeTime], 'res' => $res], JSON_UNESCAPED_UNICODE));
        // }
    }
}