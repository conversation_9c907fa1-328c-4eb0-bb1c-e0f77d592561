<?php
/**
 * 游客信息消费端
 */

namespace Consumer\Order;

class OrderTouristInfoConsumer
{
    public $args = [];

    //游客信息表
    private $_tableName = 'uu_order_tourist_info';

    private $_eventTypeUpdate = 'UPDATE';

    private $_logPath = 'consumer/order_tourist_info/debug';

    /**
     * 统一入口
     * <AUTHOR>
     * @date 2021/9/26
     *
     * @return bool
     */
    public function perform()
    {
        $result = true;
        //参数
        $param      = $this->args;
        $afterList  = $param['afterList'] ?? [];
        $beforeList = $param['beforeList'] ?? [];
        $ordernum   = $afterList['orderid'] ?? '';

        if (empty($afterList) || empty($ordernum)) {
            return $result;
        }

        if (!isset($param['tableName']) || $param['tableName'] != $this->_tableName) {
            return  $result;
        }

        //团购导码业务处理
        $this->_exportCode($param, $afterList, $beforeList, $ordernum);

        return $result;
    }

    /**
     * 团购导码业务处理
     * <AUTHOR>
     * @date 2021/9/26
     *
     * @param  array  $param  参数集
     * @param  array  $afterList 后面的数据
     * @param  array  $beforeList 之前的数据
     * @param  string  $ordernum 订单号
     *
     * @return bool
     */
    private function _exportCode(array $param, array $afterList, array $beforeList, string $ordernum)
    {
        $res = true;

        //操作类型判断 之前数据如果没有， 就不是更新
        if (empty($param['beforeList'])) {
            return $res;
        }

        $exportCode = new \Business\ExportCode\Order();

        //门票码不一致，触发更新状态
        if ($afterList['check_state'] != $beforeList['check_state']) {
            $ordernumCode = $exportCode->cacheOrderAndCodeFormart($ordernum, $afterList['chk_code']);
            //验证是否是团购导码
            if (!$exportCode->cacheSismember($ordernumCode)) {
                return $res;
            }

            //更新状态
            $result  = $exportCode->updateOrderStatus($ordernum, $afterList['check_state'], $afterList['chk_code']);
            $logData = json_encode([
                'key'     => 'export_code',
                'data'    => $afterList,
                'result ' => $result,
            ], JSON_UNESCAPED_UNICODE);

            pft_log($this->_logPath, $logData);
        }

        return $res;
    }

}