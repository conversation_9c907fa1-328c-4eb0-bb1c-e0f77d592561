<?php

namespace Consumer\Order;

use Business\Order\OrderAsync;

/**
 * 订单取消异步任务消费者
 */
class OrderCancelAsyncConsumer
{
    public $args = [];

    private $_logPath = 'consumer/order_cancel_async/debug';

    public function perform()
    {
        //参数
        $params = $this->args;
        
        if (empty($params) || !isset($params['job_type']) || $params['job_type'] !== 'cancel_async_task' || empty($params['job_data'])) {
            pft_log($this->_logPath, 'Invalid message format: ' . json_encode($params, JSON_UNESCAPED_UNICODE));
            return false;
        }
        
        $jobData = $params['job_data'];
        
        // 订单取消异步任务处理
        $ordernum        = $jobData['ordernum'];
        $isPayed         = $jobData['isPayed'];
        $orderInfo       = $jobData['orderInfo'];
        $ticketInfo      = $jobData['ticketInfo'];
        $refundData      = $jobData['refundData'];
        $refundParams    = $jobData['refundParams'];
        
        try {
            // 调用与 Order_Job.php 中相同的处理逻辑
            $orderAsyncBiz  = new OrderAsync();
            $asyncHandleRes = $orderAsyncBiz->asyncRefundTask(
                $ordernum, 
                $isPayed, 
                $orderInfo, 
                $ticketInfo,
                $refundData, 
                $refundParams
            );
            
            $logData = json_encode([
                'key'             => 'cancel_async_task',
                'ordernum'        => $ordernum,
                'isPayed'         => $isPayed,
                'refundData'      => $refundData,
                'pftSerialNumber' => $refundData['pft_serial_number'] ?? '',
                'res'             => $asyncHandleRes,
            ], JSON_UNESCAPED_UNICODE);
            
            pft_log($this->_logPath, $logData);
            
            return true;
        } catch (\Exception $e) {
            pft_log($this->_logPath, 'Exception: ' . $e->getMessage() . ', ordernum: ' . $ordernum);
            return false;
        }
    }
} 