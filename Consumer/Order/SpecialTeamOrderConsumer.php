<?php
/**
 * 特殊团队订单kafka消费端
 * User: xwh
 * Date: 2021/5/14
 * Time: 10:58
 */

namespace Consumer\Order;

use Library\Constants\Order\OrderStatus\CommonOrderStatus;
use Library\Constants\Team\TeamConst;

class SpecialTeamOrderConsumer
{
    public $args = [];
    //特殊团队下单渠道
    private $_orderMode = 55;//特殊团队接待订单
    //团购导码
    private $_exportOrderMode = 59;

    //游客信息表
    private $_tableName = 'uu_ss_order';
    //更新标识
    private $_eventTypeUpdate = 'UPDATE';
    //日志记录
    private $_logPath = 'consumer/order/debug';
    private $teamLogPath = 'consumer/team_order';

    /**
     * 统一的入口
     * <AUTHOR>
     * @date 2021/3/31
     *
     * @return bool
     */
    public function perform()
    {
        $result = true;
        //参数
        $param      = $this->args;
        $afterList  = $param['afterList'];
        $beforeList = $param['beforeList'];
        $ordernum   = $afterList['ordernum'];
        //是否已经记录了原始日志
        $isRecordLog = false;

        //更新动作
        if ($param['eventType'] == "UPDATE" && $param['tableName'] == "uu_ss_order" && $afterList['status'] != $beforeList['status']) {

            if ($afterList['ordermode'] == $this->_orderMode) {
                //原始日志丟到supervisor日志
                $isRecordLog = true;
                pft_log($this->_logPath, date('Y-m-d H:i:s') . ' ' . json_encode($param, JSON_UNESCAPED_UNICODE) . "\n");
                $data = [
                    'ordernum'    => $ordernum,
                    'afterStatus' => $afterList['status'],
                    'saveData'    => [
                        'order_status' => intval($afterList['status']),
                        'update_time'  => time(),
                    ],
                ];
                $specialJobData = [
                    'job_type' => 'special_team_order',
                    'job_data' => $data,
                ];
                $queueId = \Library\Resque\Queue::push('order', 'OrderStatus_Job', $specialJobData);
                //将数据写入日志
                $logData = json_encode([
                    'key'     => 'special_team_order',
                    'data'    => $data,
                    'queueId' => $queueId,
                ]);
                pft_log('yuhuatai_order_updata/debug', $logData);
            }
            //报团和报团计调下单 同步团单子单状态到uu_ss_order_team_link表
            if (in_array($afterList['ordermode'], TeamConst::$teamMode)) {
                //原始日志丟到supervisor日志
                $isRecordLog = true;
                pft_log($this->teamLogPath, date('Y-m-d H:i:s') . ' ' . json_encode($param, JSON_UNESCAPED_UNICODE) . "\n");
                $data = [
                    'ordernum'    => $ordernum,
                    'afterStatus' => $afterList['status'],
                    'saveData'    => [
                        'order_status' => intval($afterList['status']),
                        'playtime'     => $afterList['playtime'],
                        'update_time'  => time(),
                    ],
                ];
                $specialJobData = [
                    'job_type' => 'team_order',
                    'job_data' => $data,
                ];
                $queueId = \Library\Resque\Queue::push('order', 'OrderStatus_Job', $specialJobData);
                //将数据写入日志
                $logData = json_encode([
                    'key'     => 'team_order',
                    'data'    => $data,
                    'queueId' => $queueId,
                ]);
                pft_log($this->teamLogPath, $logData);
            }
        }

        // 团单改签
        if ( in_array($afterList['ordermode'], TeamConst::$teamMode)
            && $param['eventType'] == "UPDATE" && $param['tableName'] == "uu_ss_order"
            && $afterList['playtime'] != $beforeList['playtime']) {
            $data = [
                'ordernum'    => $ordernum,
                'afterStatus' => $afterList['status'],
                'saveData'    => [
                    'playtime'     => $afterList['playtime'],
                    'update_time'  => time(),
                ],
            ];
            $specialJobData = [
                'job_type' => 'team_order',
                'job_data' => $data,
            ];
            $queueId = \Library\Resque\Queue::push('order', 'OrderStatus_Job', $specialJobData);
            //将数据写入日志
            $logData = json_encode([
                'key'     => 'team_order_change_playtime',
                'data'    => $data,
                'queueId' => $queueId,
            ]);
            pft_log($this->teamLogPath, $logData);
        }
            //当产品类型为“景区、套票、年卡”时且订单状态等于已过期就写入电子发票开票表
        if ($param['eventType'] == "UPDATE" && $param['tableName'] == "uu_ss_order" && $afterList['status'] != $beforeList['status'] && in_array($afterList['product_type'],
                ['A','F','I','B','C','G','H','J']) && in_array($afterList['status'], [1,2,3,8,5,6,7])) {
            $consumerPartition = $param['partition'];
            $consumerOffset = $param['offset'];
            $expectOffset = $param['expect_invoice_billable_offset'];
            $isRecordLog = true;
            if (!isset($expectOffset[$consumerPartition]) || $expectOffset[$consumerPartition] > $consumerOffset) {
                pft_log($this->_logPath, date('Y-m-d H:i:s') . ' ' . json_encode($param, JSON_UNESCAPED_UNICODE) . "\n");
                $queueId = '';
                if (in_array($afterList['status'], [1, 2])) {
                    //这边是写入发票中心数据
                    $invoiceJobData = [
                        'job_type' => 'invoice_billable',
                        'job_data' => ['ordernum' => $ordernum],
                    ];
                    $queueId        = \Library\Resque\Queue::push('order', 'OrderStatus_Job', $invoiceJobData);
                }

                //这是更新发票中心订单状态
                $invoiceJobData = [
                    'job_type' => 'invoice_billable_new',
                    'job_data' => ['param' => $param],
                ];
                $updateQueueId = \Library\Resque\Queue::push('order', 'OrderStatus_Job', $invoiceJobData);
                //pft_log($this->_logPath, date('Y-m-d H:i:s') . ' ' . json_encode($queueIdNew, JSON_UNESCAPED_UNICODE) . "\n");


//            $b = $this->_invoiceBillable($param);
//            pft_log($this->_logPath, date('Y-m-d H:i:s') . ' ' . json_encode($b, JSON_UNESCAPED_UNICODE) . "\n");

                //将数据写入日志
                $logData = json_encode([
                    'key'           => 'asyncRollbackOrder',
                    'ordernum'      => $ordernum,
                    'queueId'       => $queueId,
                    'updateQueueId' => $updateQueueId,
                ]);
                pft_log('order_status/debug', $logData);
            } else {
                pft_log('consumer/order/discard', json_encode($param, JSON_UNESCAPED_UNICODE) . "\n");
            }
        }

        //当产品类型为“年卡”时且订单状态等于已过期 需要将年卡状态改成已禁用
        if ($param['eventType'] == "UPDATE" && $param['tableName'] == "uu_ss_order" && $afterList['status'] != $beforeList['status'] && in_array($afterList['product_type'],
                ['I']) && $afterList['status'] == 2) {
            pft_log($this->_logPath, date('Y-m-d H:i:s') . ' ' . json_encode($param, JSON_UNESCAPED_UNICODE) . "\n");

            $annualJobData = [
                'job_type' => 'annual_status',
                'job_data' => ['ordernum' => $ordernum],
            ];
            $queueId = \Library\Resque\Queue::push('order', 'OrderStatus_Job', $annualJobData);

            $isRecordLog = true;
            //pft_log($this->_logPath, date('Y-m-d H:i:s') . ' ' . json_encode($b, JSON_UNESCAPED_UNICODE) . "\n");

            //将数据写入日志
            $logData = json_encode([
                'key'      => 'asyncAnnualStatus',
                'ordernum' => $ordernum,
                'queueId'  => $queueId,
            ]);
            pft_log('order_status/debug', $logData);
        }

        if ($beforeList && $afterList && $ordernum && $param['tableName'] == "uu_ss_order" &&
            $afterList['ordermode'] == $this->_exportOrderMode && $afterList['status'] != $beforeList['status']) {
            pft_log($this->_logPath, date('Y-m-d H:i:s') . ' ' . json_encode($param, JSON_UNESCAPED_UNICODE) . "\n");

            $exportCodeData = [
                'job_type' => 'export_code',
                'job_data' => ['param' => $param]
            ];
            $queueId = \Library\Resque\Queue::push('order', 'OrderStatus_Job', $exportCodeData);

            $isRecordLog = true;

            //将数据写入日志
            $logData = json_encode([
                'key'           => 'asyncExportCode',
                'ordernum'      => $ordernum,
                'queueId'       => $queueId,
            ]);
            pft_log('order_status/debug', $logData);
        }

        //处理 订单核销之后还处于审核中的订单 进行拒绝操作
        if ($param['eventType'] == "UPDATE" && $param['tableName'] == "uu_ss_order" && $afterList['status'] != $beforeList['status']
            && in_array($afterList['status'],[CommonOrderStatus::USED_CODE,CommonOrderStatus::SOME_USED_CODE])
            && in_array($beforeList['status'],[CommonOrderStatus::UNUSED_CODE,CommonOrderStatus::EXPIRED_CODE,CommonOrderStatus::BE_CONFIRMED_CODE,
                CommonOrderStatus::WAIT_APPOINTMENT_CODE,CommonOrderStatus::WAIT_PRINTTICKET_CODE])){
            pft_log($this->_logPath, date('Y-m-d H:i:s') . ' ' . json_encode($param, JSON_UNESCAPED_UNICODE) . "\n");
            $auditJobData = [
                'job_type' => 'verify_link_audit',
                'job_data' => ['ordernum' => $ordernum,'update_time' =>$afterList['update_time']],
            ];
            $queueId = \Library\Resque\Queue::push('order', 'OrderStatus_Job', $auditJobData);

            $isRecordLog = true;
            //pft_log($this->_logPath, date('Y-m-d H:i:s') . ' ' . json_encode($b, JSON_UNESCAPED_UNICODE) . "\n");

            //将数据写入日志
            $logData = json_encode([
                'key'      => 'verifyLinkAudit',
                'ordernum' => $ordernum,
                'queueId'  => $queueId,
            ]);
            pft_log('order_status/debug', $logData);
        }
        //如果还没有记录日志的话，每2分钟记录一些日志，便于后续消费情况的观察
        if (!$isRecordLog) {
            $minute = date('i');
            $second = date('s');

            if ($minute % 2 == 0 && in_array($second, [0, 10])) {
                pft_log($this->_logPath, date('Y-m-d H:i:s') . ' ' . json_encode($param, JSON_UNESCAPED_UNICODE) . "\n");
            }
        }

        return $result;
    }

    /**
     * 电子发票业务处理
     * <AUTHOR>
     * @date 2021/9/26
     *
     * @param  array  $param 参数集
     *
     * @return bool
     */
    private function _invoiceBillable(array $param)
    {
        $res = 200;
        $afterList  = $param['afterList'] ?? [];
        $beforeList = $param['beforeList'] ?? [];
        $ordernum   = $afterList['ordernum'] ?? '';

        if (empty($afterList) || empty($ordernum)) {
            return 203;
        }

        //数据表判断
        if (!isset($param['tableName']) || $param['tableName'] != $this->_tableName) {
            return  203;
        }

        //操作类型判断
        if (!isset($param['eventType']) || $param['eventType'] != $this->_eventTypeUpdate) {
            return 203;
        }
        //获取需要更新状态的订单列表缓存
        //$res = (new \Business\ElectronicInvoices\InvoiceApi)->getUpdateListCache($ordernum);
        //if ($res['code'] != 200 || !$res['data']) {
        //    return $res['data'];
        //}
        $res = (new \Business\ElectronicInvoices\InvoiceApi)->findDistributorInvoiceByOrdernum($ordernum);
        if ($res['code'] != 200 || empty($res['data'])) {
            return 203;
        }
        //状态不一致，触发更新状态
        if ($afterList['status'] != $beforeList['status']) {
            $data = [
                'ordernum'    => $ordernum,
                'afterStatus' => $afterList['status'],
            ];
            //订单完结 完结时间为update_time
            if ($afterList['status'] == 8) {
                $data['dtime'] = $afterList['update_time'];
            }

            $jobData = [
                'job_type' => 'update_invoice_billable',
                'job_data' => $data,
            ];
            $queueId = \Library\Resque\Queue::push('order', 'OrderStatus_Job', $jobData);

            //将数据写入日志
            $logData = json_encode([
                'key'     => 'invoice_billable',
                'data'    => $data,
                'queueId' => $queueId,
            ]);
            pft_log($this->_logPath, '更新参数：' . $logData);
        }

        return 200;
    }
}