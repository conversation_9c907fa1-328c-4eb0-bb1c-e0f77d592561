<?php
/**
 * 门票核销
 * <AUTHOR>
 * @date   2024/02/29
 */

namespace Consumer\Order;

use Model\Order\OrderTools;

class OrderVerifyConsumer
{
    public $args = [];

    private $_logPath = 'consumer/order_verify/debug';

    public function perform()
    {
        //参数
        $params = $this->args;

        foreach ($params as $item) {

            //这边底层数据结构有问题，加下判断下
            if (!isset($item['orderNum'])) {
                continue;
            }

            try {
                $orderNum  = $item['orderNum'];
                $verifyNum = intval($item['verifyNum'] ?? 0);
                $chkDate   = $item['chkDate'] ?? '';

                if (!$verifyNum || empty($chkDate)) {
                    throw new \Exception("参数错误，{$orderNum}", 400);
                }

                //解冻金额
                \Library\Resque\Queue::push('verify_unfreeze_money', 'VerifyUnfreezeMoney_Job', [
                    'params' => $item,
                ]);

            } catch (\Exception $e) {
                $log = [
                    'params' => $params,
                    'msg'    => $e->getMessage(),
                    'line'   => $e->getLine(),
                ];
                pft_log($this->_logPath, json_encode($log, JSON_UNESCAPED_UNICODE));
            }
        }

        return true;
    }
}