<?php
/**
 * 支付后处理
 * <AUTHOR>
 * @date   2022/8/31
 */

namespace Consumer\Order;

use Library\Resque\Queue;
use Model\Order\OrderQuery;

class TicketOrderPayConsumer
{
    /**
     * 参数集
     * @var array
     */
    public $args = [];

    //日志地址
    private $_logPath      = 'consumer/ticket_order_pay/debug';
    private $_logErrorPath = 'consumer/ticket_order_pay/error';

    public function perform()
    {
        //获取参数
        $params = $this->args;

        //写入日志
        $logData = json_encode([
            'key'    => 'ticket_order_pay_topic',
            'params' => $params,
        ], JSON_UNESCAPED_UNICODE);
        pft_log($this->_logPath, $logData);

        try {
            if (!$this->_checkParams($params)) {
                throw new \Exception('参数错误', 203);
            }

            $ordernum = $params['orderNum'];//订单号
            $tradeNo  = $params['tradeNo'];//交易流水号
            $sourceT  = $params['sourceT'];//来源
            $payToPft = $params['payToPft'];//是否票付通收款
            $payMode  = $params['payMode'];//支付方式
            $verify   = $params['verify'];//是否买完就验证

            if (empty($ordernum)) {
                throw new \Exception('订单号不存在', 203);
            }

            //获取订单信息
            $orderModel = new OrderQuery('localhost');
            $orderData  = $orderModel->getSimpleOrderInfo($ordernum);
            if (!isset($orderData['mainOrder']['ordernum'])) {
                throw new \Exception("[$ordernum]getSimpleOrderInfo返回空." . json_encode($orderData,
                        JSON_UNESCAPED_UNICODE), 203);
            }

            //获取门票数据
            $mainTid         = $orderData['mainOrder']['tid'];
            $ticketCenter    = new \Business\CommodityCenter\Ticket();
            $mainProductInfo = $ticketCenter->queryTicketInfoById($mainTid, 'title', true, 'title,p_type', true, false);

            if (!$mainProductInfo) {
                throw new \Exception('商品记录不存在', 203);
            }

            $mainTicketInfo = $mainProductInfo['ticket'] ?: [];
            $mainLandInfo   = $mainProductInfo['land'] ?: [];

            $orderData['mainOrder']['p_type'] = $mainLandInfo['p_type'];
            $orderData['mainOrder']['ltitle'] = $mainLandInfo['title'];
            $orderData['mainOrder']['ttitle'] = $mainTicketInfo['title'];

            //支付方式
            $orderData['mainOrder']['real_paymode'] = $payMode;
            //第三方流水号
            $orderData['mainOrder']['third_pay_trade'] = $tradeNo;

            //支付成功后的一些列动作(异步)
            Queue::push('order', 'TicketOrderPayFinish_Job', $orderData);

            //通知微商城全民营销订单
            $allDisBiz = new \Business\Cooperator\AllDis\AllDisOrder();
            $allDisRes = $allDisBiz->orderNotify($ordernum, 0);
            if ($allDisRes['code'] != 200) {
                pft_log($this->_logErrorPath, json_encode(['mall/allDis/orderNotify', $allDisRes], JSON_UNESCAPED_UNICODE));
            }

        } catch (\Exception $e) {
            $log = json_encode([
                'params' => $params,
                'code'   => $e->getCode(),
                'msg'    => $e->getMessage(),
                'file'   => $e->getFile(),
                'line'   => $e->getLine(),
            ], JSON_UNESCAPED_UNICODE);

            pft_log($this->_logErrorPath, $log);
        }

        return true;
    }

    /**
     * 检测订阅参数
     * <AUTHOR>
     * @date   2022/8/31
     *
     * @param  array  $params
     *
     * @return bool
     */
    private function _checkParams(array $params)
    {
        $keyArr = [
            'orderNum',//订单号
            'tradeNo',//交易流水号
            'sourceT',//来源
            'payToPft',//是否票付通收款
            'payMode',//支付方式
            'verify',//是否买完就验证
        ];

        foreach ($keyArr as $key) {
            if (!isset($params[$key])) {
                return false;
            }
        }

        return true;
    }
}