<?php

namespace Consumer\Order;

use Business\PackTicket\OrderNotice\ShowTicketNotice;
use Library\Resque\Queue;
use Model\Order\OrderTools;

class PackTicketFailIssueConsumerForShow
{
    /**
     * 参数集
     * @var array
     */
    public $args = [];

    //日志地址
    private $_logPath      = 'consumer/ticket_fail_issue_ticket/debug';
    private $_logErrorPath = 'consumer/ticket_fail_issue_ticket/error';

    public function perform()
    {
        //获取参数
        $params = $this->args;
        try {
            if (!$this->_checkParams($params)) {
                throw new \Exception('参数错误', 203);
            }

            $currentOrderNum = $params['orderNum'] ?? ''; //订单号
            $status = $params['status'] ?? '';//1出票成功 0出票失败

            if (empty($currentOrderNum)) {
                throw new \Exception('订单号不存在', 203);
            }
            if (!is_numeric($status) || !in_array($status,[0,1])  ) {
                throw new \Exception('出票状态异常', 203);
            }
            $orderMode = $params['ordermode'] ;
            $desc = $params['desc'] ;
            $showTicketNotice = new ShowTicketNotice();
            $res = $showTicketNotice->noticeOfIssue($currentOrderNum,$orderMode,$desc,$status);
            //写入日志
            $logData = json_encode([
                'key' => 'PackTicketFailIssueConsumerForShow',
                'params' => $params,
                'res' => $res,
            ], JSON_UNESCAPED_UNICODE);
            pft_log($this->_logPath, $logData);

        } catch (\Exception $e) {
            $log = json_encode([
                'params' => $params,
                'code'   => $e->getCode(),
                'msg'    => $e->getMessage(),
                'file'   => $e->getFile(),
                'line'   => $e->getLine(),
            ], JSON_UNESCAPED_UNICODE);
            pft_log($this->_logErrorPath, $log);
        }

        return true;
    }

    private function _checkParams(array $params)
    {
        $keyArr = [
            'orderNum',//订单号
            'status',//1出票成功 0出票失败
            'desc',//出票失败原因
            'ordermode',//下单渠道
        ];

        foreach ($keyArr as $key) {
            if (!isset($params[$key])) {
                return false;
            }
        }

        return true;
    }

}