<?php
/**
 * 追踪记录变更
 * <AUTHOR>
 * @date   2023/11/14
 */

namespace Consumer\Order;

use Library\Resque\Queue;

class OrderTrackConsumer
{
    public $args = [];

    private $_logPath = 'consumer/order_track/debug';

    /**
     * 统一入口
     * <AUTHOR>
     * @date   2021/9/26
     *
     * @return bool
     */
    public function perform()
    {
        //参数
        $param     = $this->args;
        $afterList = $param['afterList'] ?? [];

        if (empty($afterList)) {
            return true;
        }

        $action = $afterList['action'];
        //只放行预约、改签
        if (in_array($action, [22, 32])) {
            Queue::push('ReportReal', 'ReportReal_Job', ['data' => $afterList, 'action' => 'reserve_change']);
        }

        $source = $afterList['source'];
        //预售券首次兑换才记录，非首次兑换不走这边的逻辑
        if ($action == 33 && $source == 85) {
            Queue::push('ReportReal', 'ReportReal_Job', ['data' => $afterList, 'action' => 'exchange_coupon_first']);
        }

        return true;
    }
}