<?php
/**
 * 退票处理
 * <AUTHOR>
 * @date   2022/8/31
 */

namespace Consumer\Order;

use Business\AgreementTicket\Storage;
use Business\Product\Ticket as TicketBiz;
use Library\Resque\Queue;
use Model\Order\OrderTools;

class TicketFailIssueTicketsConsumer
{
    /**
     * 参数集
     * @var array
     */
    public $args = [];

    //日志地址
    private $_logPath      = 'consumer/ticket_fail_issue_ticket/debug';
    private $_logErrorPath = 'consumer/ticket_fail_issue_ticket/error';

    public function perform()
    {
        //获取参数
        $params = $this->args;

        //写入日志
        $logData = json_encode([
            'key'    => 'open_upstream_ticketing_status',
            'params' => $params,
        ], JSON_UNESCAPED_UNICODE);

        try {
            if (!$this->_checkParams($params)) {
                throw new \Exception('参数错误', 203);
            }

            $currentOrderNum = $params['orderNum'] ?? ''; //订单号
            $status = $params['status'] ?? '';//1出票成功 0出票失败

            if (empty($currentOrderNum)) {
                throw new \Exception('订单号不存在', 203);
            }
            if (!is_numeric($status) || !in_array($status,[0,1])  ) {
                throw new \Exception('出票状态异常', 203);
            }

            //获取订单信息
            $OrderTools = new OrderTools();
            $orderInfo  =  $OrderTools ->getOrderInfo($currentOrderNum);
            if (!$orderInfo) {
                throw new \Exception('订单数据不存在', 203);
            }
            if($orderInfo['ifpack']==1){
                $mainOrderNum = $currentOrderNum;
            }
            elseif($orderInfo['ifpack']==2){
                $mainOrderNum = $orderInfo['pack_order'];
            }
            else{
                $mainOrderNum = '';
            }
            $visibleArr = '';
            $packageCancelJobId = '';
            $buyId = $orderInfo['apply_did'];
            if(!empty($mainOrderNum) && $status == 0){
                //手牌购买套票才去做解绑的动作
                $cacheKey = "pack_ticket_visible:{$mainOrderNum}";
                $cache = \Library\Cache\RedisCache::Connect('master');
                if($cache->exists($cacheKey)){
                    $visibleRes = $cache->hGet($cacheKey,'visible');
                     if($visibleRes){
                         $visibleArr = json_decode($visibleRes,true);
                     }
                }

                $jobInfo = [
                    'job_type' => 'package_cancel',
                    'job_data' => [
                        'ordernum'   => $mainOrderNum,
                        'visibleArr' => $visibleArr,
                        'error_info' => "订单:{$currentOrderNum}出票失败,套票整笔取消",
                        'apply_did'  => $buyId,
                    ],
                ];
                $packageCancelJobId = Queue::push('order', 'Order_Job', $jobInfo);
            }

            //写入日志
            $logData = json_encode([
                'key' => 'open_upstream_ticketing_status',
                'mainOrderNum' => $mainOrderNum,
                'currentOrderNum' => $currentOrderNum,
                'status' => $status,
                'visibleArr' => $visibleArr,
                'packageCancelJobId' => $packageCancelJobId,
                'buyId' => $buyId,
                'orderInfo' => json_encode([$orderInfo],JSON_UNESCAPED_UNICODE)
            ], JSON_UNESCAPED_UNICODE);
            pft_log($this->_logPath, $logData);

        } catch (\Exception $e) {
            $log = json_encode([
                'params' => $params,
                'code'   => $e->getCode(),
                'msg'    => $e->getMessage(),
                'file'   => $e->getFile(),
                'line'   => $e->getLine(),
            ], JSON_UNESCAPED_UNICODE);
            pft_log($this->_logErrorPath, $log);
        }

        return true;
    }

    private function _checkParams(array $params)
    {
        $keyArr = [
            'orderNum',//订单号
            'status',//1出票成功 0出票失败
        ];

        foreach ($keyArr as $key) {
            if (!isset($params[$key])) {
                return false;
            }
        }

        return true;
    }

}