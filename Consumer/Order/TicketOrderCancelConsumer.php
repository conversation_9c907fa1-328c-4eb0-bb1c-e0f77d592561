<?php
/**
 * 退票处理
 * <AUTHOR>
 * @date   2022/8/31
 */

namespace Consumer\Order;

use Business\AgreementTicket\Storage;
use Business\Product\Ticket as TicketBiz;
use Library\Resque\Queue;
use Model\Order\OrderTools;

class TicketOrderCancelConsumer
{
    /**
     * 参数集
     * @var array
     */
    public $args = [];

    //日志地址
    private $_logPath      = 'consumer/ticket_order_cancel/debug';
    private $_logErrorPath = 'consumer/ticket_order_cancel/error';

    /**
     * 入口
     * <AUTHOR>
     * @date   2022/9/5
     *
     * @return bool
     */
    public function perform()
    {
        //获取参数
        $params = $this->args;

        //写入日志
        $logData = json_encode([
            'key'    => 'ticket_order_cancel_topic',
            'params' => $params,
        ], JSON_UNESCAPED_UNICODE);
        pft_log($this->_logPath, $logData);


        try {
            if (!$this->_checkParams($params)) {
                throw new \Exception('参数错误', 203);
            }

            $ordernum           = $params['orderNum'] ?? ''; //订单号
            $refundSerialNumber = $params['refundSerialNumber'];//订单退票序号
            $isPay              = $params['isPay'];//是否支付 0否 1是
            $orderStatus        = $params['orderStatus'];//订单状态
            $ticketNum          = $params['ticketNum']; //当前门票总数量
            $cancelNum          = $params['cancelNum']; //取消的门票数量
            $leftNum            = $params['leftNum']; //剩余可用票数量
            $verifiedNum        = $params['verifiedNum']; //已验证门票数量
            $cancelChannel      = $params['cancelChannel']; //取消渠道
            $opId               = $params['opId']; //操作者id

            if (empty($ordernum)) {
                throw new \Exception('订单号不存在', 203);
            }

            //获取订单信息
            $orderModel   = new OrderTools('localhost');
            $tmpOrderInfo = $orderModel->getInfoForCancel($ordernum);
            if (!$tmpOrderInfo) {
                throw new \Exception('订单数据不存在', 203);
            }

            $orderInfo = $tmpOrderInfo['order_info'];

            //门票数据
            $ticketBiz = new TicketBiz();
            $tid       = $orderInfo['tid'];
            $ticketRes = $ticketBiz->getListForOrderNew($tid);
            if (!$ticketRes['ticket_info']) {
                throw new \Exception('门票数据不存在', 203);
            }

            $tmpTicketInfo = $ticketRes['ticket_info'];
            $ticketExtInfo = $ticketRes['ticket_ext_info'] ?: [];
            $landInfo      = $ticketRes['land_info'] ?: [];
            $ticketInfo    = array_merge($tmpTicketInfo, $ticketExtInfo);

            //景区的名称也加入到$ticketInfo中去
            $ticketInfo['land_title'] = $landInfo['title'];

            //协议票异步任务
            $agreementTicketJobId = Storage::pushCancelStorage($ordernum, (int)$cancelNum);

//            //分销专员订单取消佣金结算业务
//            $multiDistData  = [
//                'action' => 'cancel',
//                'data'   => ['ordernum' => $ordernum],
//            ];
//            $multiDistJobId = Queue::push('independent_system', 'MultiDist_Job', $multiDistData);

            //取消成功，卡券核销业务
            $weChatData           = [
                'order_num' => $ordernum,
                'ticket_id' => $orderInfo['tid'],
            ];
//            $weChatCardOrderJobId = Queue::push('marketing_system', 'WeChatCardOrder_Job', $weChatData);

            //未支付的取消 增加第三方发码的异步任务
            $externalCodeJobId = '';
            if (!$isPay) {
                $externalCodeData  = [
                    'action' => 'cancel_release_code',
                    'data'   => ['ordernum' => $ordernum],
                ];
                $externalCodeJobId = Queue::push('independent_system', 'ExternalCode_Job', $externalCodeData);
            }

            //营销类取消任务
            $refundData     = [
                'left_num'   => $leftNum,
                'valid_num'  => $verifiedNum + $leftNum,
                'cancel_num' => $cancelNum,
                'op_id'      => $opId,
            ];
            $refundParams   = [
                'cancelChannel' => $cancelChannel,
            ];
            $marketData     = [
                'action' => 'refund',
                'data'   => [
                    'ordernum'          => $ordernum,
                    'isPayed'           => (bool)$isPay,
                    'orderInfo'         => $orderInfo,
                    'ticketInfo'        => $ticketInfo,
                    'refundData'        => $refundData,
                    'refundParams'      => $refundParams,
                    'pft_serial_number' => $refundSerialNumber,
                ],
            ];
            $marketingJobId = Queue::push('independent_system', 'Marketing_Job', $marketData);

            //写入日志
            $logData = json_encode([
                'key'                  => 'ticket_order_cancel_topic',
                'ordernum'             => $ordernum,
//                'multiDistJobId'       => $multiDistJobId,
                'externalCodeJobId'    => $externalCodeJobId,
//                'weChatCardOrderJobId' => $weChatCardOrderJobId,
                'agreementTicketJobId' => $agreementTicketJobId,
                'marketingJobId'       => $marketingJobId,
            ], JSON_UNESCAPED_UNICODE);
            pft_log($this->_logPath, $logData);

        } catch (\Exception $e) {
            $log = json_encode([
                'params' => $params,
                'code'   => $e->getCode(),
                'msg'    => $e->getMessage(),
                'file'   => $e->getFile(),
                'line'   => $e->getLine(),
            ], JSON_UNESCAPED_UNICODE);

            pft_log($this->_logErrorPath, $log);
        }

        return true;
    }

    /**
     * 检测订阅参数
     * <AUTHOR>
     * @date   2022/8/31
     *
     * @param  array  $params
     *
     * @return bool
     */
    private function _checkParams(array $params)
    {
        $keyArr = [
            'orderNum',//订单号
            'refundSerialNumber',//订单退票序号(uu_order_refund表的主键)
            'isPay',//是否支付过 0否 1是
            'orderStatus',//订单状态
            'ticketNum',//当前门票数量
            'cancelNum',//取消的门票数量
            'leftNum',//剩余可用票数量
            'verifiedNum',//已验证门票数量
            'cancelChannel',//取消渠道
            'opId',//操作者id
        ];

        foreach ($keyArr as $key) {
            if (!isset($params[$key])) {
                return false;
            }
        }

        return true;
    }

}