<?php

namespace Consumer\MemberRelation;

use Business\JavaApi\DistributionCenter\GroupQuery;
use Business\TeamOrder\TeamRules;

class MemberRelation
{
    /**
     * 用户会员关系变化订阅入口
     *
     * <AUTHOR>
     * @date 2022/03/11
     *
     * @return bool
     */
    public function perform()
    {
        pft_log('member_relation/debug', '消费参数信息' . json_encode($this->args, JSON_UNESCAPED_UNICODE));
        $eventType        = $this->args['eventType']; //member_relation表变化的事件类型
        $afterList        = $this->args['afterList'];
        $beforeList       = $this->args['beforeList'];
        $teamRuleBz       = new TeamRules();
        $memberRelationBz = new \Business\Member\MemberRelation();
        $relationShipInfo = $memberRelationBz->getMemberDistributionInfoToJava($afterList['sid'], $afterList['fid'], 'ship_type');
        //不是合作分销商分销关系，不做处理
        if ($relationShipInfo['ship_type'] != 0) {
            return true;
        }
        switch ($eventType) {
            case "INSERT": //新增分销商
                //创建分销商添加的情况下没有ship_type字段，需要查询下
                if (!isset($afterList['ship_type'])) {
                    $groupQueryApi    = new \Business\NewJavaApi\DistributionCenter\GroupQuery();
                    $groupInfo = $groupQueryApi->queryGroupById($afterList['group_id']);
                    $groupInfo = $groupInfo['data'][0] ?? [];
                }
                //新增类型为分销关系的时候添加对应的团队类型分销    groupType 0=普通分组,1=无产品分组,2=未分组,3=资源中心分组,4=独立子分组,5=特殊价格分组,6=资源中心分组-专享
                if (($afterList['ship_type'] == 0 && $afterList['status'] == 0) || (!empty($groupInfo) && !in_array($groupInfo['groupType'], [3,6]))) {
                    $result = $teamRuleBz->addDistributorTeamType($afterList['sid'], $afterList['fid'], $afterList['group_id']);
                    if ($result['code'] != 200) {
                        pft_log('member_relation/team_type_dis/error', '新增失败，参数' .
                                                                       json_encode($afterList, JSON_UNESCAPED_UNICODE)
                                                                       . "原因" . json_encode($result, JSON_UNESCAPED_UNICODE));
                    }
                    $data = [
                        'sid'             => $afterList['sid'],
                        'fid'             => $afterList['fid'],
                        'distribute_type' => 4,
                        'group_id'        => $afterList['group_id'],
                    ];
                    \Library\Resque\Queue::push('order', 'FastBuyTicketsPreOrder_Job', $data);
                }
                break;
            case "DELETE":
                //关系断开分销关系
                $result = $teamRuleBz->closeDistributorTeamType($beforeList['sid'], $beforeList['fid']);
                if ($result['code'] != 200) {
                    pft_log('member_relation/team_type_dis/error', '断开失败，参数' .
                                                                   json_encode($afterList, JSON_UNESCAPED_UNICODE)
                                                                   . "原因" . json_encode($result,
                            JSON_UNESCAPED_UNICODE));
                }
                $data = [
                    'sid'             => $beforeList['sid'],
                    'fid'             => $beforeList['fid'],
                    'distribute_type' => 5,
                ];
                \Library\Resque\Queue::push('order', 'FastBuyTicketsPreOrder_Job', $data);
                break;
            case "UPDATE": //更新分销商信息
                $afterGroupId = $afterList['parent_group_id'];
                if ($afterList['parent_group_id'] == 0) {
                    $afterGroupId = $afterList['group_id'];
                }
                $beforeGroupId = $beforeList['parent_group_id'];
                if ($beforeList['parent_group_id'] == 0) {
                    $beforeGroupId = $beforeList['group_id'];
                }
                //分组没变更，不管
                if ($afterGroupId == $beforeGroupId) {
                    break;
                }
                //移动了分组，先把原来的关系全都删了
                $delResult = $teamRuleBz->updateCloseDistributorTeamType($afterList['sid'], $afterList['fid']);
                if ($delResult['code'] != 200) {
                    pft_log('member_relation/team_type_dis/error', '断开失败，参数' .
                                                                   json_encode($afterList, JSON_UNESCAPED_UNICODE)
                                                                   . "原因" . json_encode($delResult, JSON_UNESCAPED_UNICODE));
                }
                $result = $teamRuleBz->addDistributorTeamType($afterList['sid'], $afterList['fid'], $afterGroupId);
                if ($result['code'] != 200) {
                    pft_log('member_relation/team_type_dis/error', '新增失败，参数' .
                                                                   json_encode($afterList, JSON_UNESCAPED_UNICODE)
                                                                   . "原因" . json_encode($result, JSON_UNESCAPED_UNICODE));
                }
                $data = [
                    'sid'             => $beforeList['sid'],
                    'fid'             => $beforeList['fid'],
                    'old_group_id'    => $beforeGroupId,
                    'group_id'         => $afterGroupId,
                    'distribute_type' => 6,
                ];
                \Library\Resque\Queue::push('order', 'FastBuyTicketsPreOrder_Job', $data);

                break;
            default :
                break;
        }
        return true;
    }
}