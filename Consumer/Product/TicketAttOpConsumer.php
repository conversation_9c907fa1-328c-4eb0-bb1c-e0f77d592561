<?php
/**
 * 分销专员kafka消费端
 *
 * @date 2021-03-16
 * <AUTHOR>
 */

namespace Consumer\Product;

use Business\Product\Ticket as TicketBiz;
use Library\Business\TerminalCache;

class TicketAttOpConsumer
{

    public $args = [];

    private $_distProductBus;

    private $_distMemberModel;

    private $_distProductModel;

    /**
     * 统一的入口
     * <AUTHOR>
     * @date 2021/3/31
     *
     * @return array
     */
    public function perform()
    {
        $result  = true;
        //请求
        $opType = $this->args['opType'];
        //参数
        $param  = $this->args['param'];

        //sleep(5);
        pft_log('kafkaDebug/ticketAttOpConsumer', json_encode($this->args, JSON_UNESCAPED_UNICODE));

        //同步分销专员产品价格记录
        if ($opType == 'multiDistJob') {
            $result = $this->_multiDistTask($param);
        }

        //分销专员更新自供应产品
        if ($opType == 'updateSelf') {
            $result = $this->_multiDistTask($param);
        }

        //分销专员自供产品名称更新通知
        if ($opType == 'updateSelfName') {
            $result = $this->_multiDistTask($param);
        }

        //特产产品配置通知
        if ($opType == 'specialtyProduct') {
            $result = $this->_multiDistTask($param);
        }

        //清除下单设置的缓存
        if ($opType == 'delRedisLandInfo') {
            $result = $this->_delRedisLandInfoTask($param);
        }

        //票任务
        if ($opType == 'ticketJob') {
            $result = $this->_ticketJobTask($param);
        }

        //删除终端缓存
        if ($opType == 'delTerminalCache') {
            $result = $this->_delTerminalCacheTask($param);
        }

        //$result = true;
        return $result;
    }

    /**
     * 终端有缓存门票数据，门票更新，删除缓存
     * <AUTHOR>
     * @date 2021/3/31
     *
     * @param  array  $param
     *
     * @return array
     */
    public function _delTerminalCacheTask(array $param)
    {
        $tid = $param['tid'];

        TerminalCache::DeleteTicketCache($tid);
    }

    /**
     * 票类相关的一些异步任务处理
     * <AUTHOR>
     * @date 2021/3/31
     *
     * @param  array  $param
     *
     * @return array
     */
    public function _ticketJobTask(array $param)
    {
        $jobType = $param['job_type'];
        $jobData = $param['tid'];

        if ($jobType == 'withdraw_order') {
            //门票的退票属性修改位"不可退，而且可提现"，将已经生成的订单设置为可提现

            //门票ID
            $ticketId = $jobData;

            $queryModel  = new \Model\Order\OrderTools('slave');

            $startTime = '2017-01-01 00:00:00';
            $endTime   = date('Y-m-d H:i:s');
            $total     = $queryModel->getListByTid($ticketId, $startTime, $endTime, 0, 0, $getTotal = true);

            //要写入的日志参数
            $ticketLog = ["ticketId" => $ticketId];
            $totalLog  = ["total" => $total];

            if ($total <= 0) {
                pft_log('ticket_withdraw/debug', json_encode(['withdraw_order', $ticketLog, $totalLog]));
            } else {
                $num = 500; //每次取出500条
                for ($i = 1; $i <= ceil((int)$total / $num); $i++) {
                    //取出的数据 分页取 $i页数 $num 条数
                    $tmpArr      = $queryModel->getListByTid($ticketId, $startTime, $endTime, $i, $num, false);
                    $ordernumArr = array_column($tmpArr, 'ordernum');

                    if ($ordernumArr !== false) {
                        $api = new \Business\JavaApi\Order\OrderInfoUpdate();
                        $res = $api->batchBaseOrderInfoUpdate($ordernumArr, ['unioncheck' => 1], 0);
                    } else {
                        $res = false;
                    }
                    pft_log('ticket_withdraw/debug', json_encode(['ticket_Job', $ticketLog, $startTime, $endTime, $res]));
                }
            }
        } else {
            //其他票类异步任务

        }
    }

    /**
     * 删除景区缓存
     * <AUTHOR>
     * @date 2021/3/31
     *
     * @param  array  $param
     *
     * @return array
     */
    public function _delRedisLandInfoTask(array $param)
    {
        $tid = $param['tid'];
        $pid = $param['product_id'];

        TicketBiz::delRedisLandInfo($tid, $pid);
    }

    /**
     * 分销专员
     * <AUTHOR>
     * @date 2021/3/16
     *
     * @return array
     */
    public function _multiDistTask(array $params)
    {
        $result  = true;
        //请求
        $actions = $params['action'];

        //pft_log('debug/reques_multi/', json_encode([$actions, $params]));
        //用户id
        $memberId       = isset($params['mid']) ? $params['mid'] : '';
        //要处理的票ID
        $tid            = isset($params['tid']) ? $params['tid'] : '';
        //产品更新同步类型
        $productType    = isset($params['productType']) ? $params['productType'] : '';
        //要更新的产品数据
        $productData    = isset($params['productData']) ? $params['productData'] : '';
        //商品ID
        $gid    = isset($params['gid']) ? $params['gid'] : '';
        //状态
        $state    = isset($params['state']) ? $params['state'] : '';

        //分销专员自供应上/下架数据同步
        if ($actions == 'updata_self') {
            $result = $this->_updataSelf($memberId, $tid, $productType);
        }

        //分销专员自供应产品名称/票名称数据同步
        if ($actions == 'updata_self_data') {
            $result = $this->_updataSelfData($memberId, $tid, $productData,$productType);
        }

        //特产商品数据同步
        if ($actions == 'specialty_product') {
            $result = $this->_specialtyProduct($gid, $memberId, $state);
        }
//
//        //产品价格更新
//        if ($actions == 'update_product_price') {
//            $result = $this->_updateProductPrice($tid);
//        }

        if (!$result) {
            //处理失败情况记录失败重试表
            $this->_distMemberModel()->addQueueFail($actions, $params);
        }

        return $result;
    }


    /**
     * 自供应商上/下架数据同步
     * <AUTHOR>
     * @date 2020/4/11
     *
     * @param  int  $mid 用户ID
     * @param  int  $upId 要处理的ID
     * @param  string  $type p_on=产品上架 p_off=产品下架 t_on=票上架 t_off=票下架
     *
     * @return boolean
     */
    private function _updataSelf(int $mid, int $upId, string $type)
    {
        if (!$mid || !$upId || !$type) {
            return false;
        }

        $distProductBus   = $this->_distProductBus();
        $syncRes = false;
        $dinName = '自供应商上/下架数据同步失败';
        $logPath = 'multi_dist/error/updataSelf';
        $dinParam = json_encode([$mid, $upId, $type], JSON_UNESCAPED_UNICODE);
        $dinType = 'update_product';


        //产品上架
        if ($type == 'p_on') {
            $syncRes = $distProductBus->updataSelfByPon($mid, $upId);
        }

        //产品下架
        if ($type == 'p_off') {
            $syncRes = $distProductBus->updataSelfByPoff($mid, $upId);
        }

        //票上架
        if ($type == 't_on') {
            $syncRes = $distProductBus->updataSelfByTon($mid, $upId);
        }

        //票下架
        if ($type == 't_off') {
            $syncRes = $distProductBus->updataSelfByToff($mid, $upId);
        }

        $syncResJson = json_encode($syncRes, JSON_UNESCAPED_UNICODE);

        if ($syncRes['code'] != 200) {
            pft_log($logPath,
                '同步失败|mid:' . $mid . '|syncRes:' . $syncResJson);
            $distProductBus->dingWarn($dinName, [$dinParam, $dinParam], $dinType);
        }

        if ($syncRes['code'] != 200) {
            return false;
        }

        return true;

    }

    /**
     * 自供应产品名称/票名称数据同步
     * <AUTHOR>
     * @date 2020/4/11
     *
     * @param  int  $mid 用户ID
     * @param  int  $upId 要处理的ID
     * @param  string  $data 更新的数据
     * @param  string  $type p_name=产品名称 t_name=票名称
     *
     * @return boolean
     */
    private function _updataSelfData(int $mid, int $upId, string $data, string $type)
    {
        if (!$mid || !$upId || !$data || !$type) {
            return false;
        }

        $distProductBus   = $this->_distProductBus();
        $syncRes    = false;
        $dinName    = '自供应产品名称/票名称数据同步失败';
        $logPath    = 'multi_dist/error/updataSelfData';
        $dinParam   = json_encode([$mid, $upId, $data, $type], JSON_UNESCAPED_UNICODE);
        $dinType    = 'update_product';
        $content    = "|mid:{$mid}|upid:{$upId}|data:{$data}|type:{$type}";
        $errContent = "同步失败" . $content;

        //自供应产品名称同步
        if ($type == 'p_name') {
            $syncRes = $distProductBus->updataSelfDataByPname($mid, $upId, $data);
        }

        //自供应票名称同步
        if ($type == 't_name') {
            $syncRes = $distProductBus->updataSelfDataByTname($mid, $upId, $data);
        }

        $syncResJson = json_encode($syncRes, JSON_UNESCAPED_UNICODE);

        if ($syncRes['code'] != 200) {
            pft_log($logPath, $errContent . '|syncRes:' . $syncResJson);
            $distProductBus->dingWarn($dinName, [$dinParam, $dinParam], $dinType);
        }

        if ($syncRes['code'] != 200) {
            return false;
        }

        return true;
    }

    /**
     * 特产商品数据同步
     * <AUTHOR>
     * @date 2020/9/28
     *
     * @param  int  $gid 商品ID
     * @param  int  $mid 用户ID
     *
     * @return array
     */
    public function _specialtyProduct(int $gid, int $mid, int $state)
    {
        if (empty($mid) || empty($gid)) {
            return false;
        }

        $distProductBus   = $this->_distProductBus();

        $result = $distProductBus->specialtyProduct($gid, $mid, $state);

        if ($result['code'] != 200) {

            $dinName         = '特产商品数据更新同步失败';
            $dinParam        = json_encode([$mid, $gid, 'specialty'], JSON_UNESCAPED_UNICODE);
            $dinType         = 'update_product';
            $logPath         = 'multi_dist/error/specialtyProduct';
            $syncResJson     = json_encode($result, JSON_UNESCAPED_UNICODE);
            $productDataJson = json_encode($gid, JSON_UNESCAPED_UNICODE);

            pft_log($logPath, "同步失败|mid:{$mid}|productData:{$productDataJson}|syncRes:{$syncResJson}");
            $distProductBus->dingWarn($dinName, [$dinParam, $dinParam], $dinType);
            return false;
        }

        return true;
    }


    /**
     * 渠道配置通知处理任务
     * <AUTHOR>
     * @date 2020/9/24
     *
     * @param  int  $uid 用户ID
     * @param  string  $pids 多个产品ID
     * @param  string  $channel 渠道
     *
     * @return array
     */
    public function _channelConfig(int $uid, string $pids, string $channel)
    {
        if (empty($uid) || empty($pids)) {
            return false;
        }

        $distProductBus   = $this->_distProductBus();

        $result = $distProductBus->syncChannelConfig($uid, $pids, $channel);
        if ($result['code'] != 200) {
            return false;
        }

        return true;
    }


    /**
     * 实例化产品业务
     * <AUTHOR>
     * @date 2020/4/9
     *
     * @return \Business\MultiDist\Product
     */
    private function _distProductBus()
    {
        if (!$this->_distProductBus) {
            $this->_distProductBus = new \Business\MultiDist\Product();
        }

        return $this->_distProductBus;
    }

    /**
     * 实例化分销专员业务
     * <AUTHOR>
     * @date 2020/4/9
     *
     * @return \Model\MultiDist\Member
     */
    private function _distMemberModel()
    {
        if (!$this->_distMemberModel) {
            $this->_distMemberModel = new \Model\MultiDist\Member();
        }

        return $this->_distMemberModel;
    }

    /**
     * 实例化分销专员业务
     * <AUTHOR>
     * @date 2020/4/9
     *
     * @return \Model\MultiDist\Product
     */
    private function _distProductModel()
    {
        if (!$this->_distProductModel) {
            $this->_distProductModel = new \Model\MultiDist\Product();
        }

        return $this->_distProductModel;
    }


    /**
     * 产品价格同步
     * <AUTHOR>
     * @date 2020/8/19
     *
     * @param  int  $tid tID
     *
     * @return array
     */
    public function _updateProductPrice(int $tid)
    {
        if (empty($tid)) {
            return true;
        }
        $updateProductPriceInfo = $this->_updateProductPriceInfo($tid);


        if (!$updateProductPriceInfo) {
            //处理失败情况记录失败重试表
            $this->_distMemberModel()->addQueueFail('update_product_price', $this->args['data']);
        }

        return true;
    }
    /**
     * 分销专员产品价格同步
     * <AUTHOR>
     * @date 2020/8/19
     *
     * @param  int  $tid  Tid
     *
     * @return boolean
     */
    private function _updateProductPriceInfo(int $tid)
    {
        if (!$tid) {
            return false;
        }

        $distProductBus    = $this->_distProductBus();
        $logPath = 'multi_dist/debug/updateProductPriceInfo';
        $dinName = '分销商产品价格同步失败';
        $isRun   = true;//运行标识初始化（分批量处理数据，当数据处理完后赋值为false）
        $page    = 1;
        $size    = 20;//每次取20条数据处理


        pft_log($logPath, '开始同步|tid:' . $tid);

        $error = false;


        //分批量处理数据
        while ($isRun) {

            $syncRes = $distProductBus->syncCreateProductPriceInfo($tid, $page, $size);

            if ($syncRes['code'] != 200) {
                $error = true;
                $isRun = false;
            }

            //判断数据是否已经处理完，处理完后终止程序
            if (!$syncRes['data']['is_more']) {
                $isRun = false;
            }

            $page++;
            if ($page > 20) {
                $error = true;
                $isRun = false;
            }
        }
        $syncResJson = json_encode($syncRes, JSON_UNESCAPED_UNICODE);
        if ($error) {
            pft_log($logPath,
                '同步失败|tid:' . $tid . '|syncRes:' . $syncResJson);
            $distProductBus->dingWarn($dinName, [$tid, $tid], 'update_product_price_info');

        }

        pft_log($logPath, '同步结束|tid:' . $tid);

        if ($error) {
            return false;
        }
        return true;
    }
}