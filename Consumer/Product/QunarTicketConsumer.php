<?php

namespace Consumer\Product;
use Business\Ota\DownstreamConfig\Config as ConfigBusiness;

class QunarTicketConsumer
{
    public $args = [];

    public function perform()
    {
        $data = $this->args;
        try {
            \Library\Resque\Queue::push('cooperation_system', 'GroupOrderProduct_Job', $data);

        } catch (\Throwable $e) {}

    }

    // public function perform()
    // {
    //     $data = $this->args;

    //     try {
    //         $this->_ticketDownUnbindQuanr($data);
    //     } catch (\Throwable $e) {}

    // }

    // /**
    //  * {
    //  * "send_time": 1644964319 // 写入 kafka 的时间戳
    //  * "send_time_str": "2022-02-16 11:50:05" // 写入 kafka 的时间
    //  * "sid": "Integer // 顶级供应商id",
    //  * "landId": "Integer // 产品id",
    //  * "ticketId": "Integer // 门票id",
    //  * "opType": "Integer  // 操作类型 0添加 1更新 2删除 3上架 4下架"
    //  * "fids": [{
    //  *  "fid": 1, // 直接分销商 id
    //  *  "sid": 11, // 直接供应商 id
    //  *  },
    //  *  {
    //  *  "fid": 2, // 直接分销商 id
    //  *  "sid": 22, // 直接供应商 id
    //  *  }]
    //  * }
    //  *
    //  * @param array $payload
    //  */
    // private function _ticketDownUnbindQuanr($data)
    // {
    //     // 门票删除和下架
    //     if (!in_array($data['opType'], [2, 4])) {
    //         return ;
    //     }

    //     if (!empty($data['fids'])) {
    //         $unbindTicketList = [];
    //         $sysconfigModel   = new \Model\Ota\SysConfig();
    //         foreach ($data['fids'] as $item) {
    //             // 过滤不是去哪儿分销id
    //             if ($item['fid'] != 2175) {
    //                 continue;
    //             }
    //             $infoList = $sysconfigModel->getBindList($item['sid'], 0, $data['ticketId'], '', '');
    //             if (!empty($infoList)) {
    //                 foreach ($infoList as $val) {
    //                     $unbindTicketList[] = [
    //                         'id'        => $val['id'],
    //                         'member_id' => $item['sid'],
    //                         'op_id'     => 1,
    //                         'tid'       => $data['ticketId'] 
    //                     ];
    //                 }
    //             }
    //         }

    //         if (!empty($unbindTicketList)) {
    //             // 开始准备解绑门票
    //             $configBusiness = new ConfigBusiness();
    //             foreach ($unbindTicketList as $unbindIdVal) {
    //                 $res = $configBusiness->unBindTicketCode($unbindIdVal['id'], $unbindIdVal['member_id'], $unbindIdVal['op_id']);
    //                 pft_log('open_platform/down_system/ota/config', json_encode(['下架自动解绑去哪儿门票信息', $unbindIdVal, $res], JSON_UNESCAPED_UNICODE));
    //             }
    //         }
    //     }
    // }
}