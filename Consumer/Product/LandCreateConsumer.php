<?php

namespace Consumer\Product;

class LandCreateConsumer
{
    public $args = [];

    public function perform()
    {
        $data = $this->args['data'];

        try {
            $this->QptypeNoticeInit($data);
        } catch (\Throwable $e) {}
    }

    private function QptypeNoticeInit($data)
    {
        if (!in_array($data['p_type'], ['Q'])) {
            return ;
        }

        // 顶级供应商member_id
        $applyDid = $data['apply_did'];
        // 景区产品id
        $landId = $data['id'];

        $noticeData = [
            'action' => 'config_release_product',
            'data'   => ['uid' => $applyDid, 'lid' => $landId],
        ];
        $jobId = \Library\Resque\Queue::push('sync', 'NoticeProductConfig_Job', $noticeData);

        if (empty($jobId)) {
            pft_log('noticeProductConfig/pushJobForRelease',
                '产品发布通知存队列任务失败:' . json_encode([__METHOD__, $data, $noticeData]));
        }
    }
}