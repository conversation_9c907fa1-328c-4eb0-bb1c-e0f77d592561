<?php
/**
 * 绑定微信
 * <AUTHOR>
 * @date   2022/7/7
 */

namespace Consumer\MemberWechat;

use Business\Member\MemberWx as MemberWxBiz;
use Model\Member\Member as memberModel;
use Model\Wechat\WxMember;
use Business\Notice\WxConfig;

class BindWechat
{
    public $args = [];

    /**
     * 入口
     * <AUTHOR>
     * @date   2022/7/7
     *
     */
    public function perform()
    {
        try {
            $args = $this->args;
            //默认参数
            $params = [
                'memberId' => 0,
                'openId'   => '',
                'appId'    => '',
                'bindTime' => time() . "000", //毫秒
                'remark'   => '',
            ];

            //参数组合, 覆盖默认参数
            $params = array_merge($params, $args);

            pft_log('kafka/member_wechat/debug', '绑定微信参数接收：' . json_encode([$args, $params], JSON_UNESCAPED_UNICODE));

            $memberWx      = new MemberWxBiz();
            $wxMemberModel = new WxMember();

            if (empty($params['memberId']) || empty($params['openId']) || empty($params['appId'])) {
                throw new \Exception('参数缺失');
            }

            $bindId = 0;
            $fid    = $params['memberId'];
            $openid = $params['openId'];
            $appid  = $params['appId'];

            //获取uu_wx_member_pft信息
            $wxInfo = $memberWx->getWxInfo($fid, $openid);
            if (!empty($wxInfo)) {
                $bindId = $wxInfo['id'];
            }

            $res = false;
            if (!$bindId) { //新增
                $aid        = $memberWx->getWxAidByAppId($appid);
                $createtime = substr($params['bindTime'], 0, 10); //毫秒转为秒
                $alias      = $params['remark'];
                //获取用户账号
                $memberModel = new memberModel();
                $memberInfo  = $memberModel->getMemberInfo($fid, 'id', 'dname,account');
                $account     = $memberInfo['account'] ?? '';
                //新增数据
                $res = $wxMemberModel->saveWxMemberInfo($fid, $aid, $account, $openid, $appid, $alias, $createtime);
                if (!$res) {
                    throw new \Exception('新增同步数据失败，SQL:' . $wxMemberModel->_sql());
                }
            } else { //已存在更新, 则更新状态就行
                $update = [
                    'dstatus' => 0,
                ];
                $wxMemberModel->UpdateWxInfo(['id' => $bindId], $update);
            }
            //操作成功后处理
            if ($res) {
                //清除缓存
                $memberWx->handleWxnotifyListCache($appid, $fid, '', 'del');
                //异步同步微信绑定用户基础信息
                $lastid = !$bindId ? $res : $bindId;
                //同步用户信息 这个接口还是需要，由于出售中通知需要以这个为主
                WxConfig::pushJobForBandWx($fid, $lastid);
            }

        } catch (\Exception $e) {

            $log = [
                'msg'  => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ];

            pft_log('kafka/member_wechat/error', '绑定微信错误记录：' . json_encode($log, JSON_UNESCAPED_UNICODE));
        }

        return true;
    }
}