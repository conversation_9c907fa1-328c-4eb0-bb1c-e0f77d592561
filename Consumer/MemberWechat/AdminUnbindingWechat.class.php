<?php

namespace Consumer\MemberWechat;

class AdminUnbindingWechat
{
    public $args = [];

    public function perform()
    {
        $args = $this->args;

        $unbindingMemberId = $args['memberId'];//解绑的member_id
        $unbindingTime     = strtotime($args['unBindTime']);//解绑时间

        //给被解绑的账号增加一个解绑标识, 并且清空redis中的openId
        $loginInfoCacheKey = sprintf(\Library\Controller::LOGIN_INFO_CACHE_KEY, $unbindingMemberId);
        $cache             = \Library\Cache\Cache::getInstance('redis');
        $cache->hMset($loginInfoCacheKey, ['unbindingWechatTime' => $unbindingTime, 'openId' => '']);
        $redisLoginInfo = $cache->hGetAll($loginInfoCacheKey);

        pft_log('admin_unbinding_wechat',
            json_encode(['kafkaData' => $args, 'redisData' => $redisLoginInfo], JSON_UNESCAPED_UNICODE));
    }
}