<?php
/**
 * 绑定微信配置变更
 * <AUTHOR>
 * @date   2022/7/7
 */

namespace Consumer\MemberWechat;

use Business\Member\MemberWx as MemberWxBiz;
use Model\Wechat\WxMember;

class ConfigChange
{
    public $args = [];

    /**
     * 入口
     * <AUTHOR>
     * @date   2022/7/7
     *
     */
    public function perform()
    {
        try {
            $params = $this->args;

            pft_log('kafka/member_wechat/debug', '绑定微信配置变更参数接收：' . json_encode($params, JSON_UNESCAPED_UNICODE));

            $memberWx      = new MemberWxBiz();
            $wxMemberModel = new WxMember();

            $beforeList = $params['before'] ?? [];
            $afterList  = $params['after'] ?? [];

            if (empty($beforeList['memberId']) || empty($beforeList['openId']) || empty($beforeList['appId'])) {
                throw new \Exception('参数缺失');
            }

            $bindId = 0;
            $fid    = $beforeList['memberId'];
            $openid = $beforeList['openId'];

            //获取uu_wx_member_pft信息
            $wxInfo = $memberWx->getWxInfo($fid, $openid);
            if (!empty($wxInfo)) {
                $bindId = $wxInfo['id'];
            }

            if ($bindId) {
                //alias、auto_login
                $update = [];
                if (isset($beforeList['active']) && isset($afterList['active']) && ($beforeList['active'] != $afterList['active'])) {
                    $update['auto_login'] = ($afterList['active'] ?? false) == true ? 1 : 2;
                }
                if (isset($beforeList['remark']) && isset($afterList['remark']) && ($beforeList['remark'] != $afterList['remark'])) {
                    $update['alias'] = $afterList['remark'];
                }
                if (!empty($update)) {
                    $res = $wxMemberModel->UpdateWxInfo(['id' => $bindId], $update);
                    if (!$res) {
                        throw new \Exception('更新同步数据失败，SQL:' . $wxMemberModel->_sql());
                    }
                }
            }
        } catch (\Exception $e) {
            $log = [
                'msg'  => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ];

            pft_log('kafka/member_wechat/error', '绑定微信配置变更错误记录：' . json_encode($log, JSON_UNESCAPED_UNICODE));
        }

        return true;
    }
}