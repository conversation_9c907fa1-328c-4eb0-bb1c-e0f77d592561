<?php
/**
 * 解绑微信
 * <AUTHOR>
 * @date   2022/7/7
 */

namespace Consumer\MemberWechat;

use Business\Member\MemberWx as MemberWxBiz;
use Model\Wechat\WxMember;

class UnbindWechat
{
    public $args = [];

    /**
     * 入口
     * <AUTHOR>
     * @date   2022/7/7
     *
     */
    public function perform()
    {
        try {
            $params = $this->args;

            pft_log('kafka/member_wechat/debug', '解绑微信参数接收：' . json_encode($params, JSON_UNESCAPED_UNICODE));

            $memberWx      = new MemberWxBiz();
            $wxMemberModel = new WxMember();

            if (empty($params['memberId']) || empty($params['openId']) || empty($params['appId'])) {
                throw new \Exception('参数缺失');
            }

            $bindId = 0;
            $fid    = $params['memberId'];
            $openid = $params['openId'];
            $appid  = $params['appId'];

            //获取uu_wx_member_pft信息
            $wxInfo = $memberWx->getWxInfo($fid, $openid);
            if (!empty($wxInfo)) {
                $bindId = $wxInfo['id'];
            }

            if ($bindId) {
                $where = [
                    'id' => $bindId,
                ];

                $res = $wxMemberModel->deleteBind($where);
                if (!$res) {
                    throw new \Exception('删除数据失败，SQL:' . $wxMemberModel->_sql());
                }

                //清除缓存
                $memberWx->handleWxnotifyListCache($appid, $fid, '', 'del');
            }
        } catch (\Exception $e) {
            $log = [
                'msg'  => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ];
            pft_log('kafka/member_wechat/error', '解绑微信错误记录：' . json_encode($log, JSON_UNESCAPED_UNICODE));
        }

        return true;
    }
}