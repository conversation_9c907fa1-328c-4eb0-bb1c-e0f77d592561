<?php

namespace Consumer\Member;

class MemberStaffDelete
{
    /**
     * {"memberId":10220811, "sid":3385, "operatorId":"3385"}
     */
    public $args = [];
    public function perform()
    {
        $args = $this->args;

        pft_log('kafka_topic_notice', json_encode(['$args'=>$args]));

        $memberId = $args['memberId'];  // 被删员工id
        $sid = $args['sid'];            // 所属租户sid
        $opId = $args['operatorId'];    //

        //删除员工卡信息
        $this->_deleteStaffCard($args);
        // 解绑新旧权限
        $authLogicBiz = new \Business\Authority\AuthLogic();
        $res = $authLogicBiz->clearAllStaffRolePack($sid, $memberId, $opId);
        pft_log('kafka_topic_notice', json_encode(['$res'=>$res]));
    }

    /**
     * 删除
     *
     * <AUTHOR>
     *
     * @return bool
     */
    private function _deleteStaffCard($args)
    {
        $mid        = $args['memberId'];//解绑的member_id
        $sid        = $args['sid'];//解绑的member_id
        $StaffMange = new \Model\AdminConfig\StaffManage; //如果员工有绑定物理卡号 把相对于的卡号状态改为删除
        $staffBiz   = new \Business\Product\BaseCard();
        $cardBiz    = new \Model\Product\BaseCard();
        $code       = $staffBiz->delCardInfo($mid);
        if ($code == 200 || $code == 205) {
            //旧卡删除
            $oldInfo     = $StaffMange->getStaffInfo($mid);
            $oldCardInfo = $cardBiz->getMulitCardInfo($oldInfo['card_id'], 'id,physics_no', 1, true);
            if ($oldCardInfo) {
                $info['physics_no'] = $oldCardInfo[$oldInfo['card_id']];
                $info['id']         = $oldInfo['card_id'];
            }
            if ($oldInfo['physics_no']) {
                $physicsNoPushBiz = new \Business\Product\TerminalProduct();
                $physicsNoPushBiz->saveRecording($sid, $oldInfo['physics_no'], 1, 1);
            }
        }
        // 删除人脸
        $biz = new \Business\FaceIdentify\StaffFaceManage();
        $biz->clearFace($mid, $sid);

        return true;
    }
}