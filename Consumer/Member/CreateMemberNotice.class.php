<?php
/**
 * 创建Member后
 * <AUTHOR>
 * @date   2023/6/1
 */

namespace Consumer\Member;

use Business\Authority\AuthLogic as AuthLogicBiz;

class CreateMemberNotice
{
    public $args = [];

    /**
     * 入口
     * <AUTHOR>
     * @date   2023/6/1
     *
     * @return bool
     */
    public function perform()
    {
        $args     = $this->args;
        $dtype    = $args['member']['dtype'] ?? null;
        $memberId = $args['member']['id'] ?? null;

        if (is_null($dtype) || is_null($memberId)) {
            return true;
        }

        (new AuthLogicBiz())->merchantDtypeBindPackRole($memberId, $dtype);

        return true;
    }
}