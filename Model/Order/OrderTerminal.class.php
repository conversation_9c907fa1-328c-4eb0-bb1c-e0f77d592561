<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 11/14-2016
 * Time: 15:21
 *
 * 订单终端模型
 */

namespace Model\Order;

use app\library\Constant\Table\MyuuTableConst;
use Business\LegacyService\LegacyServiceTrafficLog;
use Business\Order\Query;
use Business\Order\RefundApprovalCenterService\AbstractApprovalCenterService;
use Business\Order\RefundApprovalCenterService\RefundApprovalService;
use Library\Constants\OrderConst;
use Library\Model;
use Library\Tools\Helpers;
use Model\Report\TerminalCollect;
use Library\Constants\Table\AuditCenter;
class OrderTerminal extends Model
{
    const STYPE_REVOKE_PART = 0;
    const STYPE_REVOKE  = 1;
    const STYPE_MODIFY = 2;
    const STYPE_CANCEL = 3;
    const STYPE_CHANGE  = 4;

    const AUDIT_STATUS_INIT = 0;// 未审核
    const AUDIT_STATUS_AGREE  = 1; //同意
    const AUDIT_STATUS_REJECT = 2;//拒绝
    const AUDIT_STATUS_THIRD_AUDIT = 3; //等待第三方平台审核通过

    const APPROVAL_STATUS_MAP = [
        0 => 0, //'未审核',前者是 审核票 后者为审批流
        1 => 1,//'同意',
        2 => -1,//'拒绝',
    ];
    const APPROVAL_STATUS_CONVERSION = [
        0 => 0, //'未审核',前者是 审核票 后者为审批流
        1 => 1,//'同意',
        -1 => 2,//'拒绝',
    ];
    private $_changeErrorCode = [
        '100'  => '操作成功',
        '118'  => '终端信息出错，请联系技术人员',
        '146'  => '终端信息出错，请联系技术人员',
        '1096' => '拒绝退款',
        '1412' => '该门票设置了退票限制，过期不可退',
        '1413' => '该门票设置了退票限制，不可退票',
        '1414' => '已超过退票有效期',
        '1420' => '积分商品无法进行退改',
    ];

    public function __construct($defaultDb = 'coredb_write')
    {
        //太多地方写了，直接限制只允许2种方式
        $defaultDb = ($defaultDb == 'coredb_slave') ? 'coredb_slave' : 'coredb_write';

        // 埋点日志采集
        (new LegacyServiceTrafficLog())->create();

        parent::__construct($defaultDb);
    }

    /**
     * 获取记录信息
     * <AUTHOR>
     * @date   2017-03-28
     *
     * @param  $terId
     * @return array
     */
    public function getInfo($terId)
    {
        if (!$terId) {
            return false;
        }

        $info = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where(['id' => $terId])->find();
        return $info ? $info : false;
    }

    /**
     * 获取撤销撤改记录  注：订单撤销撤改时调用 不支持订单分库
     * <AUTHOR> Chen
     *
     * @param string $ordernum 订单号
     * @param int $stype 类型1：撤销，0撤改
     * @return bool|array
     */
    public function GetTerminalChange($ordernum,$dstatus = [0,1])
    {
        $where = [
            'ordernum' => (string) $ordernum,
            'stype'    => ['in', [0, 1]],
            'dstatus'  => ['in', $dstatus],
        ];

        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
            ->field('id,dstatus,lid,tid,tnum,ordernum,dadmin')
            ->where($where)
            ->order('id desc')
            ->find();
        return $data;
    }

    /**
     * 添加审核记录
     * @param array $applyArr
     * [{'ordernum':'xxx', 'terminal':'xxx','dstatus':1,xxxx}]
     *
     * @return array
     * ['订单号'=>'主键',xxx]
     */
    public function addRevokeApply(array $applyArr)
    {
        if (!$applyArr || !is_array($applyArr)) {
            return false;
        }

        $res = [];
        foreach ($applyArr as $applyInfo) {
            $ordernum = $applyInfo['ordernum'];

            $terId = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->add($applyInfo);
            if ($terId !== false) {
                $res[$ordernum] = $terId;
            }
        }
        //增加退票审核链路表
        $orderTerminalAidSplit = new OrderTerminalAidSplit();
        $operatorID = $applyArr[0]['fxid'];
        $orderNumArr = array_column($applyArr,'ordernum');
        $operatorMap = array_column($applyArr,'fxid','ordernum');
        list($aidRes,$msg,$data) = $orderTerminalAidSplit->batchSaveAuditAids($orderNumArr,$operatorMap);
        if(!$aidRes){
            pft_log('refundAudit/aids',json_encode(['addRevokeApply',$aidRes,$msg,$data,$orderNumArr,$operatorID],JSON_UNESCAPED_UNICODE));
        }

        return $res ? $res : false;
    }

    /**
     * 同意撤销撤改
     * <AUTHOR>
     * @date   2017-03-28
     *
     * @param  int  $terId 记录ID
     * @param  int  $opId 操作人员ID
     * @param  string $reason 同意原因
     * @return
     */
    public function agreeApply($terId, $opId, $reason = '', $stuff_id = 0, $trackMsg = '')
    {
        $info = $this->getInfo($terId);
        if (!$info) {
            return [203, '参数错误'];
        }

        //这边只能处理撤销撤改的记录
        if(!in_array($info['stype'], [0,1])) {
            return [204, '记录类型错误'];
        }

        $auditData = $info['audit_data'];
        if (!empty($auditData)) {
            $auditData = json_decode($auditData,true);
        }
        if (isset($auditData['cloudRemark']) && !empty($auditData['cloudRemark'])) {
            $cloudRemark = $auditData['cloudRemark'];
            $trackMsg = $trackMsg . "($cloudRemark)";
        }
        $ticketCodeArr = [];
        if (isset($auditData['tourist_idx']) && !empty($auditData['tourist_idx'])){
            //这边装换成门票码传到取消里面去
            $orderTouristInfoApi = new \Business\JavaApi\Order\Query\OrderTouristInfo();
            $orderTouristList = $orderTouristInfoApi->queryOrderTouristInfoByOrderId([$info['ordernum']],false,$auditData['tourist_idx']);
            if ($orderTouristList['code'] != 200){
                return [203, $orderTouristList['msg']];
            }
            $ticketCodeArr = array_column($orderTouristList['data'],'chk_code');
        } else if(isset($auditData['code_list']) && !empty($auditData['code_list'])) {
            $ticketCodeArr = $auditData['code_list'];
        }

        $isAllowMoreRevoke = false;
        if (isset($auditData['is_allow_more_revoke'])){
            $isAllowMoreRevoke = $auditData['is_allow_more_revoke'];
        }
        
        $trackSource = $auditData['track_source'] ?? null;

        $ordernum = strval($info['ordernum']);
        $tnum     = $info['tnum'];
        $dadmin   = $info['dadmin'];
        $terminal = $info['terminal'];
        $tid      = $info['tid'];
        $lid      = $info['lid'];
        $modifyNum = intval($info['modify_tnum']);

        if ($info['dstatus'] > 0) {
            return [204, '记录已经处理', $ordernum];
        }

        if($modifyNum > 0) {
            //优化后撤改: 直接将撤改数量存起来
            $revokeNum = $modifyNum;
        } else {
            //旧撤改逻辑: 由于旧逻辑只支持已使用的订单进行撤改, 所以可以由验证数量-剩余数量=撤改数量
            //获取撤销的票数
            $orderModel = new OrderTools();
            $orderInfo  = $orderModel->getOrderInfo((string) $ordernum, 'tnum',false,false,'apply.verified_num');
            if (!$orderInfo) {
                return [204, '订单不存在', $ordernum];
            }
            $revokeNum = intval($orderInfo['verified_num'] - $tnum);
        }


        //操作用户ID
        $fxid = $info['fxid'];

        if ($revokeNum < 0) {
            return [204, '撤改数量有误', $ordernum];
        }

        $isAuth = $this->_auth($ordernum, $dadmin);
        if (!$isAuth) {
            return [204, '权限不够', $ordernum];
        }
        //额外的扩展退票信息
        $otherParams = [
            'is_refund_deposit'  => $auditData['is_refund_deposit'] ?? 0,  //是否退押金 0：不退押金 1:退押金
            'is_refund_over_pay' => $auditData['is_refund_over_pay'] ?? 0, //是否否退超时补费金额 0：不退 1:退
            'is_revoke_sms_notice' =>  $auditData['is_revoke_sms_notice'] ?? 0,
            'after_sale_num' =>  $auditData['after_sale_num'] ?? '',
            'tourist_idx' =>$auditData['tourist_idx'] ?? "",
            'batch_refund_more_idx' =>  $auditData['batch_refund_more_idx'] ?? '',
        ];
        $res       = $this->_revokeOrder($terId, $ordernum, $revokeNum, $dadmin, $reason, $fxid, $terminal, $tid, $lid,
            $fxid, $trackMsg, $trackSource, $ticketCodeArr, $isAllowMoreRevoke, $otherParams);
        $isSucc    = $res[0];
        $errorCode = $res[1];
        $errorMsg = $res[2];
        if ($isSucc) {
            return [200, '审核成成功', $ordernum];
        } else {
            $errorMsg = isset($this->_changeErrorCode[$errorCode]) ? $this->_changeErrorCode[$errorCode] : "处理失败(code=$errorCode,msg =$errorMsg)";
            return [205, $errorMsg, $ordernum];
        }
    }

    /**
     * 拒绝撤销撤改
     * <AUTHOR>
     * @date   2017-03-28
     *
     * @param  $terId 记录ID
     * @param  $reason 拒绝原因
     * @return
     */
    public function rejectApply($terId, $reason = '')
    {
        $info = $this->getInfo($terId);
        if (!$info) {
            return [203, '参数错误'];
        }

        $ordernum = strval($info['ordernum']);
        $tnum     = $info['tnum'];
        $dadmin   = $info['dadmin'];

        if ($info['dstatus'] > 0) {
            return [204, '记录已经处理', $ordernum];
        }

        //这边只能处理撤销撤改的记录
        if(!in_array($info['stype'], [0,1])) {
            return [204, '记录类型错误', $ordernum];
        }

        if ($tnum < 0) {
            return [204, '传入的参数有误', $ordernum];
        }

        $isAuth = $this->_auth($ordernum, $dadmin);
        if (!$isAuth) {
            return [204, '权限不够', $ordernum];
        }
        $where = [
            'id'      => $terId,
            'dstatus' => 0,
        ];

        $data = [
            'dstatus' => 2,
            'reason'  => $reason,
            'dadmin'  => $dadmin,
            'dtime'   => ['exp', 'now()'],
        ];

        $res = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($where)->save($data);
        $res = intval($res);

        //售后拒绝通知
        $subOrderName = $info['ifpack'] != 0 ? '子票' : '权益订单';
        $msg = "{$subOrderName}供应商审核撤改失败，原因：{$reason}";
        $auditData    = empty($info['audit_data']) ? [] : json_decode($info['audit_data'], true);
        if(isset($auditData['after_sale_num']) && !empty($auditData['after_sale_num'])){
            $linkTouristIdx = reset($auditData['tourist_idx']);
            $idx = is_array($linkTouristIdx) ? $linkTouristIdx['idx'] :$linkTouristIdx;
            $dataKafka = [
                'op_id' => $dadmin,
                'refund_status'  => 2,// 0审核中 1退票成功  2失败
                'after_sale_num' => $auditData['after_sale_num'],
                'req_serial_number' => strval($refundRecord['remote_sn'] ?? $info['system_sn']),
                'order_num' => $ordernum,
                'refund_idx' =>  empty($idx) ? ["1"]:[strval($idx)],
                'cancel_type' => 'revoke',
                'order_status' => '',
                'cancel_num' => $refundRecord['modify_tnum'] ?? 0,
                'left_num' =>  $refundRecord['tnum'] ?? 0,
                'is_retry' => $auditData['is_retry'] ?? false,
                'msg' => $msg
            ];
            pft_log('debug/refundKafka', json_encode(['售后-撤改审核', $dataKafka,$res,$ordernum],JSON_UNESCAPED_UNICODE));
            \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic', $dataKafka, strval($dataKafka['order_num']));
        }
        if ($res > 0) {
            return [200, '处理成功', $ordernum];
        } else {
            return [205, '处理失败', $ordernum];
        }
    }

    /**
     * 是否有权限
     * <AUTHOR>
     * @date   2017-03-28
     *
     * @param  $ordernum 订单号
     * @param  $dadmin 操作用户ID
     * @return
     */
    private function _auth($ordernum, $dadmin)
    {
        if (!$ordernum || !$dadmin) {
            return false;
        }

        $orderModel = new OrderTools();
        $orderInfo  = $orderModel->getOrderInfo((string) $ordernum, 'id, member');
        if (!$orderInfo) {
            return false;
        }

        // uu_order_terminal_change->dadmin 在撤销审核的时候写入的是 uu_ss_order->member, 在退票审核的时候写入的是 uu_ss_order->apply_did
        //所以这边直接屏蔽了，这个判断没有意义
        //if ($dadmin != $orderInfo['member']) {
        //    return false;
        //} else {
        //    return true;
        //}

        return true;
    }
	
	/**
	 * 统一撤销，具体去取消订单
	 * @param int    $terId    主键ID
	 * @param string $ordernum 订单号
	 * @param int    $revokeNum  撤销数量
	 * @param int    $dadmin   订单member
	 * @param int    $reason   理由
	 * @param int    $opId     操作人员
	 * @param int    $terminal 验证的终端
	 * @param int    $tid      票类ID
	 * @param int    $lid      景点ID
	 * @param int    $stuff_id 审核用户ID
	 * @param string $trackMsg 订单审核追踪数据
	 * @param null|int   $trackSource
	 * @param array   $ticketCodeList  门票码数组
	 * @param bool   $isAllowMoreRevoke  是否允许多次撤销
	 * @param array   $otherParams  额外的扩展退票信息
	 * @return array
	 * <AUTHOR>
	 *
	 */
    private function _revokeOrder($terId, $ordernum, $revokeNum, $dadmin, $reason, $opId, $terminal, $tid, $lid, $stuff_id, $trackMsg = '', $trackSource = null,
        $ticketCodeList = [],$isAllowMoreRevoke = false, $otherParams = [])
    {
        //首先修改审核的状态
        $where = [
            'id'      => $terId,
            'dstatus' => 0,
        ];

        $data = [
            'dstatus' => 1,
            'reason'  => $reason,
            'dtime'   => ['exp', 'now()'],
        ];

        $res = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($where)->save($data);
        $res = intval($res);
        if ($res <= 0) {
            //状态修改失败
            return [false, 0,'状态修改失败'];
        }

        if (!$trackMsg) {
            $trackMsg = '订单撤销/撤改审核';
        }

        try {
            //通过接口区修改订单
	
	        if(!$trackSource){
		        $cancelChannel   = OrderConst::REVOKE_CANCEL;
	        }elseif($trackSource == 4){ // 退票申请来自云票务
		        $cancelChannel = OrderConst::CLOUD_TICKET_CANCEL;
	        }
            // $cancelChannel   = OrderConst::REVOKE_CANCEL;
	        
            $cancelType      = 'revoke';
            $reqSerialNumber = '';
            $cancelRemarkArr = [
                'remark'   => $trackMsg
            ];
            $cancelSiteArr   = [
                'terminal' => $terminal
            ];
            $cancelPersonArr = [
                'ticket_code_list' => $ticketCodeList,
            ];
            $cancelSpecialArr= [
                'is_need_audit'        => false,
                'is_audit_agreed'      => true,
                'is_force_cancel'      => false,
                'is_cancel_sub'        => true,
                'is_allow_more_revoke' => $isAllowMoreRevoke,
                'is_refund_deposit'    => $otherParams['is_refund_deposit'] ?? 0,  //是否退押金 0：不退押金 1:退押金（计时特有）
                'is_refund_over_pay'   => $otherParams['is_refund_over_pay'] ?? 0, //是否否退超时补费金额 0：不退 1:退（计时特有）
                'is_revoke_sms_notice'   => $otherParams['is_revoke_sms_notice'] ?? 0,
                'after_sale_num'   => $otherParams['after_sale_num'] ?? '',
                'batch_refund_more_idx'=> $otherParams['batch_refund_more_idx'] ?? '',
            ];
            //演出捆绑票撤销撤改，不联动退子票
            $orderQuery = new Query();
            $isBindTicket = $orderQuery->isBindTicketForOrderNum($ordernum);
            if($isBindTicket){
                $cancelSpecialArr['is_cancel_sub'] = false;
            }
            //售后目前只支持idx
            if(!empty($cancelSpecialArr['after_sale_num'])){
                $cancelPersonArr =  [
                    'person_index' => reset($otherParams['tourist_idx']),
                ];
            }
            //先记录下下单人
            pft_log('cancel/five',json_encode(func_get_args()));
            $opId            = $opId ? $opId : 1;
            $res             = Helpers::platformRefund($ordernum,$revokeNum,$opId,$cancelChannel,$cancelType,$reqSerialNumber,$cancelRemarkArr,$cancelSiteArr
            ,$cancelPersonArr,$cancelSpecialArr);

            @pft_log('cancel/five',json_encode([$ordernum,$revokeNum,$opId,$cancelChannel,$cancelType,$reqSerialNumber,$cancelRemarkArr,$cancelSiteArr
                ,$cancelPersonArr,$cancelSpecialArr,$res]));
            if ($res['code'] == 200) {
                //这边做统一的终端数据汇总
                $collectModel = new TerminalCollect();
                $checkTime    = date('Y-m-d H:i:s');

                //暂时把撤销的渠道都设置为智能终端
                $revokeSource = 20;

                $collectModel->addData($terminal, $tid, $lid, $checkTime, $revokeSource, 0, 0, $revokeNum);
                return [true, 1];
            } else {
                //修改失败
                //https://www.tapd.cn/43247536/bugtrace/bugs/view?bug_id=1143247536001021857
                $errCode = isset($res['data']['err_code']) ?  $res['data']['err_code'] : $res['code'];
                $errMsg = isset($res['data']['err_msg']) ?  $res['data']['err_msg'] : $res['msg'];
                $data = [
                    'dstatus' => 2,
                    'reason'  => $errMsg,
                    'dtime'   => null,
                ];
                $where['dstatus'] = 1;
                $tmpRes           = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($where)->save($data);
                pft_log('order_terminal/fail', json_encode([$ordernum, $terId, $res, $tmpRes]));
                return [false, $errCode,$errMsg];
            }
        } catch (\Exception $e) {
            //修改失败
            $data = [
                'dstatus' => 0,
                'reason'  => '',
                'dtime'   => null,
            ];
            $where['dstatus'] = 1;
            $tmpRes           = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($where)->save($data);
            pft_log('order_terminal/fail', json_encode(['soap_error', $ordernum, $terId, $e->getMessage(), $tmpRes]));
            return [false, '订单取消出错，请重试。',$e->getMessage()];
        }
    }

    /**
     * 添加记录
     * WxService JsonRpc调用
     * <AUTHOR>
     * @date   2017-12-06
     * @param  array     $data 记录数据
     */
    public function addTerminalChangeLog($data)
    {
        if (!is_array($data) || !$data) {
            return false;
        }

        $add = [];
        foreach ($data as $one) {

            if (!isset($one['ordernum'], $one['salerid'], $one['lid'], $one['tid'], $one['tnum'])) {
                continue;
            }

            $add[] = [
                'ordernum' => $one['ordernum'],
                'salerid'  => $one['salerid'],
                'terminal' => $one['terminal'] ?: 0,
                'lid'      => $one['lid'],
                'tid'      => $one['tid'],
                'tnum'     => $one['tnum'],
                'reason'   => $one['reason'] ?: '',
                'dadmin'   => (int) $one['dadmin'],
                'fxid'     => (int) $one['fxid'],
                'dstatus'  => (int) $one['dstatus'] ?: 1,
                'stype'    => (int) $one['stype'] ?: 1,
                'stime'    => date('Y-m-d H:i:s'),
                'dtime'    => date('Y-m-d H:i:s'),
            ];
        }
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($one['ordernum']);
        if(!$isWhite){
            $result = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->addAll($add);
        }
        else{
            $result = true;
            foreach ($add as $info){
                $res = Helpers::submitRefundApproval($info['ordernum'],0,$info['terminal'],$info['salerid'],$info['lid']
                    ,$info['tid'],$info['stype'],$info['tnum'],$info['fxid'],$info['dstatus'],$info['dadmin'],$info['stime'],
                    $info['reason'],$info['dtime'],'','',false,[]);
                if($res['code'] != 200){
                    pft_log('order_terminal/fail', json_encode(['addTerminalChangeLog', $info,$res]));
                    return false;
                }
            }
        }
        //增加退票审核链路表
        $orderTerminalAidSplit = new OrderTerminalAidSplit();
        $operatorID = $add[0]['fxid'];
        $orderNumArr = array_column($add,'ordernum');
        $operatorMap = array_column($add,'fxid','ordernum');
        list($aidRes,$msg,$data) = $orderTerminalAidSplit->batchSaveAuditAids($orderNumArr,$operatorMap);
        if(!$aidRes){
            pft_log('refundAudit/aids',json_encode(['addTerminalChangeLog',$aidRes,$msg,$data,$orderNumArr,$operatorID],JSON_UNESCAPED_UNICODE));
        }
        return boolval($result);
    }

    /**
     * 获取撤销撤改审核
     * <AUTHOR>
     * @date   2018-06-04
     */
    public function getAlertAuditByOrderId($orderArr)
    {
        if (empty($orderArr) || !is_array($orderArr)) {
            return [];
        }

        $filter = [
            'ordernum' => ['in', $orderArr],
            'dstatus'  => 0,
            'stype'    => ['in', [0, 1]],
        ];

        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($filter)->field('ordernum')->select();
        if (empty($data)) {
            return [];
        }

        $res = [];
        foreach ($data as $item) {
            $res[] = $item['ordernum'];
        }
        return $res;
    }

    /**
     * 获取订单撤改记录 注:取消时用到 暂不支持分库
     *
     * @param  int  $ordernum  订单号
     * @param  array  $extraWhere  额外的查询条件
     *
     * @return []
     */
    public function getOrderChangeRecord($ordernum, $field = '*', $extraWhere = [])
    {
        $where = [
            'ordernum' => (string) $ordernum,
        ];

        if ($extraWhere) {
            $where = array_merge($where, $extraWhere);
        }

        $list = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
            ->where($where)
            ->field($field)
            ->select();

        return $list ?: [];
    }

    /**
     * 获取订单撤改记录列表 注:取消时用到 暂不支持分库
     *
     * @param  int  $ordernumArr  订单号
     * @param  array  $extraWhere  额外的查询条件
     *
     * @return array
     */
    public function getOrderChangeRecordList($ordernumArr, $field = '*', $extraWhere = [])
    {
        if (empty($ordernumArr)) {
            return [];
        }

        $where = [
            'ordernum' => ['in', $ordernumArr],
        ];

        if ($extraWhere) {
            $where = array_merge($where, $extraWhere);
        }
        if($field == '*'){
            $list = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
                ->where($where)->select();
        }
        else{
            $list = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
                ->where($where)
                ->getField($field, true);
        }
        return $list ?: [];
    }

    /**
     * 获取退票审核
     * <AUTHOR>
     * @date   2017-11-24
     */
    public function getRefundAuditByOrderId($orderArr)
    {
        if (empty($orderArr) || !is_array($orderArr)) {
            return [];
        }

        $filter = [
            'ordernum' => ['in', $orderArr],
            'dstatus'  => 0,
            'stype'    => ['in', [2, 3]],
        ];

        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($filter)->field('ordernum')->select();
        if (empty($data)) {
            return [];
        }

        $res = [];
        foreach ($data as $item) {
            $res[] = $item['ordernum'];
        }

        return $res;
    }

    /**
     * 获取退票审核流水号之类
     * 如果是已经退票审核的，需要将退票请求流水号带过去
     *
     * <AUTHOR>
     * @date   2019-08-07
     *
     * @param  array  $orderArr
     *
     * @return array
     */
    public function getRefundAuditList($orderArr, $statusArr = [0])
    {
        if (empty($orderArr) || !is_array($orderArr)) {
            return [];
        }

        $filter = [
            'ordernum' => ['in', $orderArr],
            'dstatus'  => ['in', $statusArr],
            'stype'    => ['in', [2, 3]],
        ];

        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($filter)->field('ordernum,stype,system_sn,remote_sn')->select();
        if (empty($data)) {
            return [];
        }

        $res = [];
        foreach ($data as $item) {
            $res[$item['ordernum']] = $item;
        }

        return $res;
    }

    /**
     * 获取退票审核的订单状态
     * Create by zhangyangzhen
     * Date: 2018/8/23
     * Time: 15:57
     *
     * @param $orderArr
     * @param  string  $field
     * @param  bool  $dstatus
     *
     * @return array|mixed
     */
    public function getRefundAuditByOrdernum($orderArr, $field = 'ordernum, dstatus', $dstatus = false)
    {
        if (empty($orderArr) || !is_array($orderArr)) {
            return [];
        }

        $filter = [
            'ordernum' => ['in', $orderArr],
        ];

        if ($dstatus != false) {
            if (is_array($dstatus)) {
                $filter['dstatus'] = ['in', $dstatus];
            } else {
                $filter['dstatus'] = $dstatus;
            }
        }

        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
            ->where($filter)
            ->field($field)
            ->order('stime desc')
            ->select();
        if (empty($data)) {
            return [];
        }

        return $data;
    }


    /**
     * 添加退票审核记录
     * <AUTHOR>
     * @date   2019-06-29
     *
     * @param  int  $orderNum  平台订单号
     * @param  int  $modifyNum  本次修改的票数
     * @param  int  $terminal  终端号
     * @param  int  $salerid  景区6位编号
     * @param  int  $lid  景区id
     * @param  int  $tid  门票id
     * @param  int  $modifyType  修改类型 0-撤改 1-撤销 2-修改 3-取消 4-改签
     * @param  int  $targetTnum  变更后票数
     * @param  int  $operatorID  退票发起人
     * @param  int  $auditStatus  退票审核状态 0-未处理 1-同意 2-拒绝 3-等待第三方自动审核
     * @param  int  $requestTime  申请时间
     * @param  string  $auditNote  审核备注
     * @param  int  $auditorID  审核人
     * @param  int  $auditTime  审核时间
     * @param  string  $remote_sn  第三方流水号
     * @param  string  $system_sn  平台流水号
     * @param  bool  $sysException  是否系统异常时候请求的退票
     * @param  array  $otherData  其他信息
     *
     * @return mixed
     */
    public function addRefundAudit($orderNum, $modifyNum, $terminal, $salerid, $lid, $tid, $modifyType, $targetTnum,
                                   $operatorID = 1, $auditStatus = 0, $auditorID = 0, $requestTime = 0, $auditNote = '', $auditTime = 0,
                                   $remote_sn = '', $system_sn = '', $sysException = false, $otherData = [])
    {
        if (empty($otherData)) {
            return 0;
        }
        $requestTime = ($requestTime) ? $requestTime : date('Y-m-d H:i:s');
        $data        = [
            'ordernum'    => $orderNum,
            'terminal'    => $terminal,
            'salerid'     => $salerid,
            'lid'         => $lid,
            'tid'         => $tid,
            'stype'       => $modifyType,
            'tnum'        => $targetTnum,
            'modify_tnum' => $modifyNum,
            'dstatus'     => $auditStatus, //状态0未操作1同意2拒绝
            'stime'       => $requestTime,
            'fxid'        => $operatorID, //申请发起人
            'dadmin'      => $auditorID,
            'remote_sn'   => $remote_sn,
            'system_sn'   => $system_sn,
            'ltitle'      => $otherData['land_name'],
            'ttitle'      => $otherData['ticket_name'],
            'aids'        => $otherData['aids'],
            'concat_id'   => $otherData['concat_id'],
            'apply_did'   => $otherData['apply_did'],
            'ifpack'      => $otherData['ifpack'],
            'dcodeURL'    => $otherData['dcodeURL'],
            'pack_order'  => $otherData['pack_order'],
            'p_type'      => $otherData['p_type'],
        ];
        if ($auditTime) {
            $data['dtime'] = $auditTime;
        }
        if ($auditNote) {
            $data['reason'] = $auditNote;
        }
        if ($sysException) {
            $data['system_auto_check'] = 1;
        }
        if (isset($otherData['audit_data'])){
            $data['audit_data'] = json_encode($otherData['audit_data']);
        }
        if (isset($otherData['sub_merchant_id']) && $otherData['sub_merchant_id'] > 0){
            $data['sub_merchant_id'] = $otherData['sub_merchant_id'];
        }
        $sqlId = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->data($data)->add();

        //增加退票审核链路表
        $orderTerminalAidSplit = new OrderTerminalAidSplit();
        $operatorMap = [$orderNum=>$operatorID];
        list($aidRes,$msg,$data) = $orderTerminalAidSplit->batchSaveAuditAids([$orderNum],$operatorMap);
        if(!$aidRes){
            pft_log('refundAudit/aids',json_encode(['addRefundAudit',$aidRes,$msg,$data,$orderNum,$operatorID],JSON_UNESCAPED_UNICODE));
        }

        return $sqlId;
    }

    /**
     * 获取系统异常审核订单列表
     *
     * @author: guanpeng
     * @date: 2019/5/22
     *
     * @param  string  $stimeStart  开始时间
     * @param  string  $stimeEnd  截止时间
     * @param  int  $limit  限制条数
     *
     * @return array
     */
    public function getSysExceptionList($stimeStart, $stimeEnd, $limit = 1000)
    {
        $field = "id,ordernum,stype,apply_did";
        $where = [
            'stime'             => ['between', [$stimeStart, $stimeEnd]],
            'dstatus'           => 0,
            'system_auto_check' => 1,
        ];
        $list  = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->field($field)
            ->where($where)->limit($limit)->select();

        return $list;
    }

    /**
     * 获取过期数据
     * @author: guanpeng
     * @date: 2019/5/22
     *
     * @param  int  $limit
     * @param $orderBy
     *
     * @return mixed
     */
    public function getExpireDataIdArray($limit, $orderBy)
    {
        $field      = 'id';
        $cancelTime = date('Y-m-d H:i:s', strtotime('-180 days'));
        $where      = [
            'stime'   => ['elt', $cancelTime],
            'dstatus' => 1,
        ];
        if ($orderBy == 'asc') {
            $order = 'id asc';
        } else {
            $order = 'id desc';
        }
        $idArr = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($where)->order($order)->limit($limit)->getField($field,
            true);

        return $idArr;
    }

    /**
     * 获取符合归档条件的数据
     * @param $idList
     * @return array|false|mixed|string
     */
    public function getExpireDataDetailList($idList)
    {
        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where(['id' => ['in', $idList]])->select();

        return $data;
    }


    /**
     * 将即将删除的数据转储的归档实例的数据库上
     * @param array $data
     * @return false|mixed|string
     */
    public function transerExpireData(array $data)
    {
        return $this->table('uu_order_terminal_change_history')->addAll($data);
    }
    /**
     * 删除过期数据
     *
     * @author: guanpeng
     * @date: 2019/5/22
     *
     * @param  array  $taskIdArr
     *
     * @return bool|mixed
     */
    public function delExpireTask($taskIdArr)
    {
        if (!$taskIdArr || !is_array($taskIdArr)) {
            return false;
        }
        $where = ['id' => ['in', $taskIdArr]];
        $res   = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($where)->delete();

        return $res;
    }

    /**
     * 获取审核记录的数据   注:订单审核处用到 不支持订单分库 慎调用
     * <AUTHOR>
     * @date   2016-10-18
     *
     * @param  string  $ordernum  订单ID
     * @param  int  $status  记录的状态
     * @param  int|bool  $tnum  本次修改的票数，如果是取消的话=0
     *
     * @return
     */
    public function getTerminalChangeInfo($ordernum, $status, $tnum = false)
    {
        $ordernum = strval($ordernum);
        $status   = intval($status);
        if (!$ordernum) {
            return false;
        }

        $where = [
            'ordernum' => $ordernum,
            'dstatus'  => $status,
        ];

        if ($tnum !== false) {
            $where['tnum'] = $tnum;
        }

        $res = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($where)->find();

        return $res;
    }

    /**
     * 查询订单是否处于退款审核状态
     *
     * @param  string  $orderNum  订单号
     * @param  string  $remoteSn  流水号
     * @param  int  $modifyType  变更类型：2-修改 3-取消
     * @param  int  $dstatus  审核状态，传null不根据此字段过滤
     *
     * @return array
     */
    public function isUnderAudit($orderNum, $remoteSn, $modifyType = null, $dstatus = 0)
    {
        $table = AuditCenter::TABLE_TERMINAL_CHANGE;
        $where = [
            'ordernum'  => (string) $orderNum,
            'remote_sn' => $remoteSn,
        ];
        if (!is_null($dstatus)) {
            $where['dstatus'] = $dstatus;
        }
        if ($modifyType !== null) {
            $where['stype'] = $modifyType;
        }

        $result = $this->table($table)->where($where)
            ->field("id,ordernum, terminal, salerid, lid, tid, stype, tnum, dstatus, reason, dadmin, stime, dtime, fxid, remote_sn, system_sn, modify_tnum, audit_data")
            ->find();

        return $result;
    }

    /**
     * 判断订单是不是处于退票审核状态中
     * <AUTHOR>
     * @date   2017-05-16
     *
     * @param  array  $orderIdArr  订单数组
     *
     * @return bool
     */
    public function isAllUnderAudit($orderIdArr)
    {
        if (!$orderIdArr || !is_array($orderIdArr)) {
            return false;
        }

        $table = AuditCenter::TABLE_TERMINAL_CHANGE;
        $field = ['id'];
        $where = [
            'ordernum' => ['in', $orderIdArr],
            'dstatus'  => 0,
            'stype'    => ['in', [2, 3]],
        ];
        $res   = $this->table($table)->where($where)->field($field)->find();

        return $res ? true : false;
    }

    /**
     * 检查套票主票是否有未审核通过的子票订单
     *
     * @param  array  $childOrders  子票订单号数组
     * @param  string  $remoteSn  第三方流水号
     *
     * @return array
     */
    public function hasUnAuditSubOrder($childOrders, $remoteSn = '')
    {
        $where = [
            "dstatus"  => 0,
            "ordernum" => ['in', $childOrders],
        ];
        if (!empty($remoteSn)) {
            $where['remote_sn'] = $remoteSn;
        }
        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->field('id')->where($where)->find();

        return $data;
    }

    /**
     *  更新退款审核结果
     *
     * @param  int  $orderNum  订单号
     * @param  int  $auditResult  审核结果 1-同意 2-决绝
     * @param  int  $auditNote  审核意见
     * @param  int  $operatorID  审核人
     * @param  int  $auditID  审核记录ID
     * @param  int  $auditTime  审核时间
     * @param  string|null  $sn  第三方流水号
     *
     * @return bool
     */
    public function updateAudit($orderNum, $auditResult, $auditNote, $operatorID = 1, $auditTime = 0, $auditID = 0, $sn = null, $status = 0, $statusArr = [], $auditData = [])
    {
        $table = AuditCenter::TABLE_TERMINAL_CHANGE;
        //审核记录ID和订单至少要传入一个才能更新 订单号优先
        if ($auditID) {
            $where['id'] = $auditID;
        } elseif ($orderNum) {
            $where = ['ordernum' => $orderNum . ''];
            if ($statusArr) {
                $where['dstatus'] = ['in', $statusArr];
            } else {
                if (empty($status)) {
                    $where['dstatus'] = 0;
                } else {
                    $where['dstatus'] = $status;
                }
            }

        } else {
            return false;
        }
        if (!empty($sn)) {
            $where['remote_sn'] = $sn . '';
        }
        $auditTime = ($auditTime) ? $auditTime : date('Y-m-d H:i:s');

        $data   = [
            'dstatus' => $auditResult,
            'reason'  => $auditNote,
            'dadmin'  => $operatorID,
            'dtime'   => $auditTime,
        ];

        if ($auditData) {
            $data['audit_data'] = json_encode($auditData);
        }

        $result = $this->table($table)->where($where)->data($data)->save();

        return $result;
    }

    /**
     * 获取拒绝票数
     *
     * @param  string  $orderNum  订单号
     * @param  int  $dstatus  审核状态
     *
     * @return mixed
     */
    public function getAuditTargetTnum($orderNum, $dstatus = 0)
    {
        $table = AuditCenter::TABLE_TERMINAL_CHANGE;
        $where = [
            'ordernum' => (string) $orderNum,
        ];
        if (!is_null($dstatus)) {
            $where['dstatus'] = $dstatus;
        }
        $filed = 'tnum as audit_tnum';
        $order = 'id desc';

        return $this->table($table)
            ->where($where)
            ->field($filed)
            ->order($order)
            ->find();
    }
    /**
     * 获取退票数量
     *
     * @param  string  $orderNum  订单号
     * @param  int  $dstatus  审核状态
     *
     * @return mixed
     */
    public function getAllAuditTargetTnum($orderNum, $dstatus = [])
    {
        $table = AuditCenter::TABLE_TERMINAL_CHANGE;
        $where = [
            'ordernum' => (string) $orderNum,
        ];
        if (!empty($dstatus)) {
            $where['dstatus'] = ['in',$dstatus];
        }
        $filed = 'modify_tnum,tnum as surplus_num,id';

        return $this->table($table)
            ->where($where)
            ->field($filed)
            ->select();
    }

    /**
     * 获取审核列表数据
     * @param $memberID
     * @param null $landTitle
     * @param null $noticeType
     * @param null $applyDate
     * @param null $auditStatus
     * @param null $auditDate
     * @param null $orderNum
     * @param false $getTotalPage
     * @param int $page
     * @param int $limit
     * @param false $auditType
     * @param array $extCondition ['lidList' => '允许产品列表', 'notLidList' => '排除的产品列表', 'use_offset' => '使用offset来查询数据']
     * @return array|false|mixed|string|string[]|null
     */
    public function getAuditListNew($memberID, $landTitle = null, $noticeType = null, $timeType = null,
                                    $beginTime = null, $endTime = null, $auditStatus = null, $orderNum = null,
                                    $getTotalPage = false, $page = 1, $limit = 20,$auditType = false, $landId = 0, $tid = 0,$subSid = 0, array $extCondition = [])
    {
        $table = AuditCenter::TABLE_TERMINAL_CHANGE . " AS a";
        $where = [];
        if (isset($extCondition['approval_code'])) {
            $where['a.approval_code'] = $extCondition['approval_code'];
        }
        //根据传入参数确定查询条件
        //2016-3-27 供应商能看到套票子票，分销商能看到套票主票
        //2016-3-28 修改撤销撤改记录的显示
        if ($memberID != 1) {
            $where['uotca.aid'] = $memberID;
//            $where['_complex'][] = [
//                [
//                    'a.apply_did' => $memberID,
//                    [
//                        [
//                            'a.ifpack' => ['in', [0, 2]],
//                            'a.stype'  => ['in', [2, 3]],
//                        ],
//                        [
//                            'a.stype' => ['in', [0, 1, 4]],
//                        ],
//                        '_logic' => 'or',
//                    ],
//                ],
//                [
//                    'a.fxid'   => $memberID,
//                    'a.ifpack' => ['in', [0, 1]],
//                ],
//                [
//                    'a.apply_did' => $memberID,
//                ],
//                [
//                    '_string'  => "find_in_set('{$memberID}',a.aids)",
//                    'a.ifpack' => ['in', [0, 1]],
//                ],
//                '_logic' => 'or',
//            ];
            if (isset($extCondition['lidList'])) {
                $where['_complex'][] = [
                    'a.lid' => ['in', $extCondition['lidList']]
                ];
            }
            if (isset($extCondition['notLidList'])) {
                $where['_complex'][] = [
                    'a.lid' => ['not in', $extCondition['notLidList']]
                ];
            }
        }
        if ($auditStatus !== null) {
            $where['a.dstatus'] = $auditStatus;
        }
        if ($auditType == 1){
            $where['p_type'] = ['eq','J'];
        }elseif ($auditType == 0){
            $where['p_type'] = ['neq','J'];
        }
        if ($landId){
            $where['a.lid'] = $landId;
        }
        if ($tid){
            $where['a.tid'] = $tid;
        }
        if ($orderNum) {
            $map['a.ordernum']  = $orderNum;
            $map['a.concat_id'] = $orderNum;
            $map['_logic']      = 'or';
            $where['_complex'][]  = $map;
        } else {
            //这个判断逻辑沿用了好几年不知道从哪个需求来的
            if ($landTitle) {
                $where['a.ltitle'] = ["like", "%{$landTitle}%"];
            }
            if ($subSid){
                $where['a.sub_merchant_id'] = $subSid;
            }
            if ($noticeType !== null) {
                $where['a.stype'] = $noticeType;
            }
            if($timeType>0){
                $beginTodayDefault = mktime(0, 0, 0, date('m'), date('d'), date('Y'));
                $endTodayDefault = mktime(0, 0, 0, date('m'), date('d')+1, date('Y'))-1;
                $bTime = date('Y-m-d H:i:s', (empty($beginTime) ? $beginTodayDefault :$beginTime));
                $eTime = date('Y-m-d H:i:s',  (empty($endTime) ? $endTodayDefault :$endTime));
                if($timeType == 2){
                    $where['a.dtime'] = ['between', "{$bTime},{$eTime}"];
                }
                else{
                    $where['a.stime'] = ['between', "{$bTime},{$eTime}"];
                }
            }
        }

        //获取记录总数
        if ($getTotalPage) {

//            if ($orderNum) {
//                return ["count(DISTINCT a.id)" => '1'];
//            }

            $field = ["count(DISTINCT a.id)"];

            return $this->table($table)
                ->join(AuditCenter::TABLE_TERMINAL_CHANGE_AIDS . ' uotca ON a.ordernum = uotca.order_num', 'LEFT')
                ->where($where)
                ->field($field)
                ->find();

        } else {
            //查询记录详情
            $field = [
                'DISTINCT a.id',
                'a.ordernum',
                'a.stype',
                'a.tnum',
                'a.dstatus',
                'a.reason',
                'a.stime',
                'a.dtime',
                'a.remote_sn',
                'a.system_sn',
                'a.ltitle AS ltitle',
                'a.apply_did',
                'a.concat_id',
                'a.dcodeURL',
                'a.ifpack',
                'a.pack_order',
                'a.ttitle as ticketTitle',
                'a.p_type',
                'a.modify_tnum',
                'a.dadmin',
                'a.audit_data',
                'a.fxid',
                'a.approval_code',
            ];
            $order = [
                'stime DESC',
                'dstatus ASC',
            ];
            $query = $this->table($table)
                ->join(AuditCenter::TABLE_TERMINAL_CHANGE_AIDS . ' uotca ON a.ordernum = uotca.order_num', 'LEFT')
                ->where($where);
            if (isset($extCondition['use_offset'])) {
                $query->limit($extCondition['use_offset'], $limit);
            } else {
                $query->page($page, $limit);
            }
            $query->order($order)->field($field);

            if ($orderNum) {
                $result = $query->select();
                if (!$result) {
                    return false;
                }
                if ($limit == 1) {
                    return current($result);
                }
                return $result;
            } else {
                if ($limit == 1) {
                    return $query->find();
                } else {
                    return $query->select();
                }
            }
        }
    }


    /**
     * 根据退款记录审核id获取详情
     *
     * @param  int  $id  ID
     *
     * @return mixed
     */
    public function getRefundAuditDetailById($id)
    {
        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
//            ->field("id, ordernum, terminal, salerid, lid, tid, stype, tnum, modify_tnum,dstatus, reason, dadmin, stime, dtime, fxid, remote_sn, system_sn,p_type,audit_data")
            ->where(['id' => $id])->find();

        return $data;
    }

    /**
     * 获取已经审核的或申请中的修改票数
     *
     * <AUTHOR> Chen
     * @date 2018-03-08
     *
     * @param  string  $ordernum  订单号
     *
     * @return int    总的修改票数
     */
    public function getSumModifyTnum($ordernum)
    {
        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
            ->field("sum(modify_tnum) as modify_tnum")
            ->where(['ordernum' => (string) $ordernum, 'dstatus' => ['in', [0, 1]]])->find();

        return isset($data['modify_tnum']) ? $data['modify_tnum'] : 0;
    }

    /***
     * 获取订单改签审核
     * <AUTHOR> Yiqiang
     * @date   2018-09-18
     *
     * @param  array  $orderIds  订单列表
     * @param  string  $field
     * @param  array  $dstatus  审核状态数组
     *
     * @return array
     */
    public function getTicketChangingOrders($orderIds, $field = 'ordernum', $dstatus = [], $orderColum = false)
    {
        if (empty($orderIds) || !is_array($orderIds) || !is_string($field) || !is_array($dstatus)) {
            return [];
        }

        $where = [
            'ordernum' => ['in', $orderIds],
            'stype'    => 4,
        ];

        if (!empty($dstatus)) {
            //根据审核状态筛选
            foreach ($dstatus as $status) {
                if (!is_numeric($status)) {
                    return [];
                }
                $stArr[] = $status;
            }
            $filter = ['dstatus' => ['in', $stArr]];
            $where  = array_merge($where, $filter);
        }

        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
            ->where($where)
            ->field($field)
            ->select();

        if (empty($data)) {
            return [];
        }

        if ($orderColum) {
            foreach ($data as $item) {
                $res[] = $item['ordernum'];
            }
        } else {
            $res = $data;
        }

        return $res;
    }

    /***
     * 获取订单退票审核
     * <AUTHOR>
     * @date   2021-09-15
     *
     * @param  array  $orderIds  订单列表
     * @param  string  $field
     * @param  array  $dstatus  审核状态数组
     *
     * @return array
     */
    public function getTicketRefundOrders($orderIds, $field = 'ordernum', $dstatus = [], $orderColum = false)
    {
        if (empty($orderIds) || !is_array($orderIds) || !is_string($field) || !is_array($dstatus)) {
            return [];
        }
        foreach ($orderIds as &$value){
            $value = strval($value);
        }
        $where = [
            'ordernum' => ['in', $orderIds],
            'stype'    => ['in', [0,1, 2, 3]],
        ];

        if (!empty($dstatus)) {
            //根据审核状态筛选
            foreach ($dstatus as $status) {
                if (!is_numeric($status)) {
                    return [];
                }
                $stArr[] = $status;
            }
            $filter = ['dstatus' => ['in', $stArr]];
            $where  = array_merge($where, $filter);
        }

        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
            ->where($where)
            ->field($field)
            ->select();

        if (empty($data)) {
            return [];
        }

        if ($orderColum) {
            foreach ($data as $item) {
                $res[] = $item['ordernum'];
            }
        } else {
            $res = $data;
        }

        return $res;
    }

    /**
     * 根据退款记录票付通流水号获取详情
     *
     * @param  string  $systemSn
     *
     * @return mixed
     */
    public function getRefundAuditDetailBySystemSn($systemSn)
    {
        if (empty($systemSn)) {
            return [];
        }
        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
            ->field("id, ordernum, terminal, salerid, lid, tid, stype, tnum, modify_tnum,dstatus, reason, dadmin, stime, dtime, fxid, remote_sn, system_sn")
            ->where(['system_sn' => $systemSn])->find();

        return $data ?: [];
    }

    /***
     * 获取订单改签审核
     * <AUTHOR>
     * @date   2019-05-31
     *
     * @param  array  $orderIds  订单列表
     * @param  array  $stype  变更类型:0=撤改,1=撤销,2=修改,3=取消,4=改签
     * @param  array  $dstatus  审核状态数组
     *
     * @return array
     */
    public function getInAuditOrder($orderIds, $dstatus = [], $stype = [], $field = 'ordernum', $orderColum = true)
    {
        if (!is_array($orderIds) || !is_array($dstatus)) {
            return [];
        }
        $where = [
            'ordernum' => ['in', $orderIds],
            'dstatus'  => ['in', $dstatus],
        ];
        if (!empty($stype) && is_array($stype)) {
            $where['stype'] = ['in', $stype];
        }
        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
            ->where($where)
            ->field($field)
            ->select();

        if (empty($data)) {
            return [];
        }

        if ($orderColum) {
            foreach ($data as $item) {
                $res[$item['ordernum']] = $item;
            }
        } else {
            $res = $data;
        }

        return $res;
    }

    /**
     * 获取审核中的流水号
     * <AUTHOR>
     * @date   2018-09-08
     *
     * @param  array  $orderIdArr  订单数组
     *
     * @return array
     */
    public function getUnderAuditInfo($orderNum, $dstatus = 0, $stype = [])
    {
        if (!$orderNum) {
            return [];
        }

        $table = AuditCenter::TABLE_TERMINAL_CHANGE;
        $field = ['id,ordernum,remote_sn'];
        $where = [
            'ordernum' => (string) $orderNum,
            'dstatus'  => $dstatus,
        ];
        if (!empty($stype)) {
            $where['stype'] = ['in', $stype];
        }
        $res = $this->table($table)->where($where)->field($field)->find();

        return $res ? $res : [];
    }
    
    /**
     * 更新取消审核记录
     * <AUTHOR>
     * @date 2022/3/11
     *
     * @param  int  $examineId 审核ID
     * @param  int  $dstatus 状态:0=未审核,1=同意,2=拒绝,3=等待第三方平台审核通过
     * @param  string  $dtime 处理时间
     * @param  int  $dadmin 操作人员用户ID
     * @param  string  $reason 审核备注
     *
     * @return array
     */
    public function updateExamineById(int $examineId, int $dstatus, string $dtime = '', int $dadmin = 0, string $reason = '')
    {

        if (empty($examineId)) {
            return 0;
        }

        $where = [
            'id' => $examineId
        ];

        $data   = [
            'dstatus'   => $dstatus,
        ];
        if (!empty($dtime)) {
            $data['dtime'] = $dtime;
        }
        if (!empty($dadmin)) {
            $data['dadmin'] = $dadmin;
        }
        if (!empty($reason)) {
            $data['reason'] = $reason;
        }

        $result = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($where)->data($data)->save();

        return $result;
    }

    /**
     * 获取等待子票供应商审核列表
     *
     * @param $memberID
     * @param  null  $landTitle
     * @param  null  $noticeType
     * @param  null  $applyDate
     * @param  null  $auditStatus
     * @param  null  $auditDate
     * @param  null  $orderNum
     * @param  false  $getTotalPage
     * @param  int  $page
     * @param  int  $limit
     *
     * @return array|mixed
     */
    public function getPackAuditList($memberID, $landTitle = null, $noticeType = null, $applyDate = null, $auditDate = null, $orderNum = null, $getTotalPage = false,
                                     $page = 1, $limit = 20)
    {
        $table = AuditCenter::TABLE_TERMINAL_CHANGE . " AS a";

        $auditStatusArr = [1, 2, 3, 5];

        //先查询出套票子票需要审核的主票订单号
        $packOrderRes = $this->table($table)
            ->where(['a.p_type' => ['in', ['A', 'C', 'G', 'H']], 'a.ifpack' => 2, 'a.dstatus' => ['in', [0, 3]], 'audit_data' => ['like', '%is_new_package%']])
            ->field('a.pack_order, count(*) t')
            ->page($page)
            ->limit($limit)
            ->order(['stime DESC', 'dstatus ASC'])
            ->group('pack_order')
            ->having('t > 0')->select();

        if (empty($packOrderRes)) {
            return [];
        }
        $packOrderArr = array_column($packOrderRes, 'pack_order');

        //根据传入参数确定查询条件
        //2016-3-27 供应商能看到套票子票，分销商能看到套票主票
        //2016-3-28 修改撤销撤改记录的显示
        $where['uotca.aid'] = $memberID;
//        $where['_complex'][] = [
//            [
//                'a.apply_did' => $memberID,
//                [
//                    [
//                        'a.ifpack' => ['in', [1, 2]],
//                        'a.stype'  => ['in', [2, 3]],
//                    ],
//                    [
//                        'a.stype' => ['in', [0, 1, 4]],
//                    ],
//                    '_logic' => 'or',
//                ],
//            ],
//            [
//                'a.fxid'   => $memberID,
//                'a.ifpack' => ['in', [1, 2]],
//            ],
//            [
//                'a.apply_did' => $memberID,
//            ],
//            [
//                '_string'  => "find_in_set('{$memberID}',a.aids)",
//                'a.ifpack' => ['in', [1, 2]],
//            ],
//            '_logic' => 'or',
//        ];

        if (!$orderNum) {
            if ($landTitle) {
                $where['a.ltitle'] = ["like", "%{$landTitle}%"];
            }
            if ($noticeType !== null) {
                $where['a.stype'] = $noticeType;
            }
            if ($applyDate) {
                $applyDate        = substr($applyDate, 0, 10);
                $bTime1           = $applyDate . " 00:00:00";
                $eTime1           = $applyDate . " 23:59:59";
                $where['a.stime'] = ['between', "{$bTime1},{$eTime1}"];
            }
            if ($auditStatusArr) {
                $where['a.dstatus'] = ['in', $auditStatusArr];
            }
            if ($auditDate) {
                $auditDate        = substr($auditDate, 0, 10);
                $bTime2           = $auditDate . " 00:00:00";
                $eTime2           = $auditDate . " 23:59:59";
                $where['a.dtime'] = ['between', "{$bTime2},{$eTime2}"];
            }

            $where['a.p_type']   = 'F';
            $where['a.ordernum'] = ['in', $packOrderArr];
        }

        //获取记录总数
        if ($getTotalPage) {

            if ($orderNum) {
                $where['a.ordernum']  = $orderNum;
            }

            return $this->table($table)
                ->join(AuditCenter::TABLE_TERMINAL_CHANGE_AIDS . ' uotca ON a.ordernum = uotca.order_num', 'LEFT')
                ->where($where)
                ->count('DISTINCT a.id');

        } else {
            //查询记录详情
            $field = 'DISTINCT a.id ,a.ordernum,a.stype,a.tnum,a.dstatus,a.reason,a.stime,a.dtime,a.remote_sn,a.system_sn,a.ltitle AS ltitle,a.apply_did,a.concat_id,a.dcodeURL,a.ifpack,a.pack_order,a.ttitle as ticketTitle,a.p_type,a.modify_tnum,a.dadmin,a.audit_data';
            $order = [
                'stime DESC',
                'dstatus ASC',
            ];

            if ($orderNum) {
                if ($auditStatusArr) {
                    $where['a.dstatus'] = ['in', $auditStatusArr];
                }

                $where['a.p_type'] = 'F';

                $where['a.ordernum'] = $orderNum;
                $result              = $this->table($table)
                    ->join(AuditCenter::TABLE_TERMINAL_CHANGE_AIDS . ' uotca ON a.ordernum = uotca.order_num', 'LEFT')
                    ->where($where)
                    ->page($page, $limit)
                    ->order($order)
                    ->field($field)
                    ->select();
                if (!$result) {
                    return [];
                }

                if ($limit == 1) {
                    return current($result);
                }

                return $result;
            }
            $map = $this->table($table)
                ->join(AuditCenter::TABLE_TERMINAL_CHANGE_AIDS . ' uotca ON a.ordernum = uotca.order_num', 'LEFT')
                ->where($where)
                ->field($field)
                ->page($page)
                ->limit($limit)
                ->order($order);

            if ($limit == 1) {
                $result = $map->find();
            } else {
                $result = $map->select();
            }
            return $result;
        }
    }

    public function getPackSonAuditList($packOrder)
    {
        if (!$packOrder) {
            return [];
        }
        $field   = 'id,ordernum,stype,tnum,dstatus,reason,stime,dtime,remote_sn,system_sn,ltitle AS ltitle,apply_did,concat_id,dcodeURL,ifpack,pack_order,ttitle as ticketTitle,p_type,modify_tnum,dadmin,audit_data';
        $sonList = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
            ->where(['pack_order' => $packOrder, 'dstatus' => ['in', [0, 1, 2, 3, 5]]])
            ->field($field)
            ->select();

        return $sonList ?: [];
    }

    /**
     * 获取审核记录的数据 （新）  注:订单审核处用到 不支持订单分库 慎调用
     *
     * @param  string  $ordernum  订单ID
     * @param  array  $statusArr  记录的状态
     * @param  int|bool  $tnum  本次修改的票数，如果是取消的话=0
     *
     * @return
     */
    public function getTerminalChangeInfoNew($ordernum, $statusArr, $tnum = false)
    {
        $ordernum = strval($ordernum);
        if (!$ordernum) {
            return false;
        }

        $where = [
            'ordernum' => (string) $ordernum,
            'dstatus'  => ['in', $statusArr],
        ];

        if ($tnum !== false) {
            $where['tnum'] = $tnum;
        }

        $res = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($where)->find();

        return $res;
    }

    /**
     * 检查新套票主票是否有未审核过的子票订单
     *
     * @param  array  $childOrders  子票订单号数组
     * @param  array  $dstatusArr  状态数组
     * @param  string  $remoteSn  第三方流水号
     *
     * @return array
     */
    public function getRefundRecordList($childOrders, $dstatusArr = [5], $remoteSn = '')
    {
        $where = [
            "dstatus"  => ['in', $dstatusArr],
            "ordernum" => ['in', $childOrders],
        ];

        if (!empty($remoteSn)) {
            $where['remote_sn'] = $remoteSn;
        }

        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($where)->select();

        return $data;
    }

    /**
     * 通过主票订单号 获取出主票对应批次下子票待审核、通过、拒绝、待三方审核的数量
     *
     * @param  string  $parentOrderNum  主票订单号
     *
     * @return array
     */
    public function getSonNeedAuditNumByParentOrderNum($parentOrderNum, $statusArr = [0, 2, 3, 5])
    {
        if (!$parentOrderNum) {
            return [];
        }

        $sonNumber = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)
            ->where(['pack_order' => $parentOrderNum, 'dstatus' => ['in', $statusArr]])
            ->select();

        return $sonNumber;
    }


    public function getRefundApprovalList($orderArr, $statusArr = [], $sTypeArr =[])
    {
        if (empty($orderArr) || !is_array($orderArr)) {
            return [];
        }

        $filter = [
            'ordernum' => ['in', $orderArr],
        ];
        if(!empty($statusArr)){
            $filter['dstatus']  = ['in', $statusArr];
        }

        if(!empty($sTypeArr)){
            $filter['stype']  = ['in', $sTypeArr];
        }

        $data = $this->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where($filter)->select();
        if (empty($data)) {
            return [];
        }
        return $data;
    }
}
