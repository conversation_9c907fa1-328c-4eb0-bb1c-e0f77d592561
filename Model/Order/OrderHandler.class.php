<?php
/**
 * 订单表操作
 * User: chenguangpeng
 * Date: 12/15-2016
 * Time: 16:59
 * 订单表操作，涉及到较高的写权限。
 */

namespace Model\Order;

use Business\Admin\ModuleConfig;
use Business\CommodityCenter\Land;
use Business\Order\OrderVerify;
use Business\VerifyRpcApi\Verify\VerifyModule;
use Library\ApplicationContext;
use Library\Cache\RedisCache;
use Library\Constants\DingTalkRobots;
use Library\Constants\FastCheckConst;
use Library\Constants\Table\MainTableConst;
use Library\Resque\Queue;
use Library\Tools\FastCheckUtil;
use Library\Tools\Helpers;
use Model\CardSolution\TimingOrderDeposit;
use Model\CardSolution\TimingProduct;
use Model\Product\Ticket;
use Library\Cache\Cache;
use Library\Lock\LockFactory;

class OrderHandler extends OrderTools
{
    private $terminalDb = null;

    //是否需要扣除可退数量
    public $isNeedUpdateCanRefundNum = true;

    public function __construct($db = 'localhost_wsdl')
    {
        parent::__construct($db);
        $this->terminalDb = new TerminalOrderHandler();
    }

    /**
     * 更新订单打印状态
     *
     * @param  string  $orderId  订单号
     * @param  int  $status  要更新的状态值
     * @param  bool  $byIdCard  是否根据身份证
     * @param  int  $idxOrIdCard  票码序号或身份证（一票一码的情况下）
     *
     * @return bool
     */
    public function updatePrintStatus($orderId, $status = 1, $byIdCard = false, $idxOrIdCard = 0)
    {
        if (empty($orderId)) {
            return false;
        }
        //多笔订单
        if (is_array($orderId)) {
            $orderArr = $orderId;
        } else {
            $orderArr = [$orderId];
        }

        if ($byIdCard && $idxOrIdCard) {
            $idCard = $idxOrIdCard;
        } else {
            $idCard = '';
        }

        if (!$byIdCard && $idxOrIdCard) {
            $idx = $idxOrIdCard;
        } else {
            $idx = 0;
        }

        $api = new \Business\JavaApi\Order\OrderHandle();
        $res = $api->updatePrintStatus($orderArr, $status, $byIdCard, $idx, $idCard, 0);

        return $res['code'] == 200;
    }

    /**
     * 更新订单支付状态-包括联票
     * @authro Guangpeng Chen
     * @date 2016-12-09
     *
     * @param  string  $ordernum  主票订单号
     * @param  int  $pay_status  支付状态，默认“1”，已支付
     * @param  int|null  $paymode  支付凡方式
     * @param  int  $buyerId  下单人ID
     * @param  bool  $isUpdateLink  是否一起联票所有订单
     *
     * @return bool
     */
    public function UpdatePayStatus($ordernum, $pay_status = 1, $paymode = null, $buyerId = 0, $isUpdateLink = true)
    {
        $api = new \Business\JavaApi\Order\OrderHandle();
        $res = $api->updatePayStatus($ordernum, $pay_status, is_null($paymode) ? -1 : $paymode, $buyerId,
            $isUpdateLink);

        return $res['code'] == 200;
    }

    /**
     * 更新订单价格-包括联票 (会员卡下单 先下单 选择完支付方式 修改价格)
     *
     * <AUTHOR>
     * @date   2018-04-23
     */
    public function UpdateOrderPrice($orderNum, $tPrice, $totalMoney, $buyerId)
    {
        $api = new \Business\JavaApi\Order\OrderHandle();
        $res = $api->updateOrderPrice($orderNum, $buyerId, $tPrice, $totalMoney);

        return $res['code'] == 200;
    }

    /**
     * 订单快速验证——请在理解业务场景的基础上调用此方法
     * 由于权限问题，在实例化该类时，数据库配置文件必须是‘localhost_wsdl’
     * 应用场景：
     *       1.第三方渠道订单过期核销
     *       2.平台管理员验证有问题的订单
     *       3.
     *
     * @param  string  $ordernum  订单号
     * @param  int  $memberId  调用这个方法的用户ID，外部接口核销传合作方的ID
     * @param  array  $orderInfo  订单信息数组，如果外部传入，必须存在member,lid,tid,tnum,callback,status,salerid,paymode,verified_num
     * @param  string  $memo  备注信息
     * @param  bool  $source  验证来源
     * @param  bool  $dtime  验证时间，套票验证的是时候会带时间过来
     * @param  int  $terminal  验证分终端
     * @param  int  $chkNum  验证票数
     * @param  bool  $strictMode  是否启用严格模式，启用严格模式后只有供应商自身可严重;true 根据供应商id校验；2根据salerid校验。
     * @param  string  $idcard  身份证验证
     * @param  bool  $isHandleChild  是否同步验证子票
     * @param  bool  $childBatchCheck  是否开启子票分批验证
     *
     * @return bool|array  成功返回true，失败返回数组信息
     * <AUTHOR> Chen
     * @date 2016年11月29日23:09:26
     *
     */
    public function CheckOrderSimply($ordernum, $memberId, $orderInfo = null, $memo = '', $source = false,
        $dtime = false, $terminal = 0, $chkNum = 0, $strictMode = false, $idcard = '', $isHandleChild = true, $childBatchCheck = false,
        $checkType=FastCheckConst::CHECK_TYPE_IMMEDIATELY, $merchantSubId=0)
    {
        $verifyRpcBiz = new VerifyModule();
        $rpcData      = [
            'memberId'                 => $memberId,            //调用这个方法的用户ID，外部接口核销传合作方的ID
            'memo'                     => $memo,           //备注信息
            'source'                   => $source,           //验证来源
            'checkTime'                => $dtime,       //验证时间，套票验证的是时候会带时间过来
            'terminalId'               => $terminal,         //验证分终端
            'checkNum'                 => $chkNum,         //验证票数
            'strictMode'               => $strictMode,     //是否启用严格模式，启用严格模式后只有供应商自身可严重;true 根据供应商id校验；2根据salerid校验。
            'idCard'                   => $idcard,        //身份证验证
            'isHandleChild'            => $isHandleChild,   //是否同步验证子票
            'childBatchCheck'          => $childBatchCheck,  //是否开启子票分批验证
            'isNeedUpdateCanRefundNum' => $this->isNeedUpdateCanRefundNum,//是否需要扣除可退数量
            'requestIp'                => isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] ? $_SERVER['REMOTE_ADDR'] : '',
            'deviceKey'                => ApplicationContext::get('device_key', ""),
        ];
        if($merchantSubId>0){
            $rpcData['subMerchId'] = $merchantSubId;
        }

        if ($checkType == FastCheckConst::CHECK_TYPE_DELAY_ASYNC) {  // 延迟验证
            $delaySecond = strtotime("+ 2 seconds");
            $retry       = 3;
            $rpcRes      = FastCheckUtil::delayCheck($ordernum, $rpcData, $delaySecond, $retry, $retry);
        } else {
            $rpcRes = $verifyRpcBiz->quickVerifyByRpc($ordernum, $rpcData);
        }

        if ($rpcRes['code'] == 200) {
            return true;
        } else {
            pft_log('quick_check/error', "订单号{$ordernum}" . json_encode($rpcRes));

            return $rpcRes;
        }
    }

    /**
     * 获取允许子票部分使用退的供应商fid
     * @param int $applyDid
     * @return bool
     */
    private function isAllowPartUsed(int $applyDid)
    {
        if(empty($applyDid)) {
            return false;
        }
        $moduleConfigBiz = new ModuleConfig();
        $resultRes = $moduleConfigBiz->getOpenModuleBySidAndAppKey([$applyDid], 'special_pack_check', 60);
        if($resultRes['code'] != 200) {
            return false;
        }
        return !empty($resultRes['data']);
    }

    public function _checkOrderSimply($ordernum, $memberId, $orderInfo = null, $memo = '', $source = false,
        $dtime = false, $terminal = 0, $chkNum = 0, $strictMode = false, $idcard = '', $isHandleChild = true, $childBatchCheck = false)
    {
        $isFinallyVerifyPack = false;
        $finallVerifyData    = []; //配合上面变量使用
        try{
        $ordernum = strval($ordernum);
        $orderInfo = $this->getOrderInfo($ordernum,
            'member,tid,lid,tnum,callback,ordermode,status,salerid,paymode,ss.ordermode,ss.pay_status,ss.remotenum, ss.dtime, ss.apply_did, ss.sync_state',
            false, false, 'verified_num');

        //操作人员处理
        $memberId = $memberId ?: 0;

        $OrderTools  = new \Model\Order\OrderTools(); //订单工具(接口)模型
        // 检测是不是套票主票
        $subOrders = $this->getPackSubOrder($ordernum);
        if ($isHandleChild == true) {
            //需要同步验证的订单号
            $orderArr = [];

            if ($subOrders) {
                $orderAll    = array_column($subOrders, 'orderid'); //订单号查ss_order查出票的tid; tid查apply_did
                
                $Ticket      = new \Model\Product\Ticket(); //门票信息模型
                $AppDid      = $Ticket->getMuchTicketInfo([$orderInfo['tid']], 'apply_did'); //主票的供应商apply_did
                $applyDidArr = $OrderTools->getOrderApplyDid($orderAll); //子票的供应商apply_did数组
                $AppDid      = $AppDid[0];

                if (!empty($applyDidArr)) {
                    //这边要处理一下过滤掉不是相同供应商
                    foreach ($applyDidArr as $k => $applyDid) {
                        if ($applyDid == $AppDid) {
                            $orderArr[] = strval($k);
                        }
                    }
                    if (!empty($orderArr)) {
                        //是否开启子票分批验证
                        if (!$childBatchCheck) {
                            $chkNum = 0;
                        }

                        foreach ($orderArr as $value) {
                            //之前验证就是调用自己
                            $this->CheckOrderSimply($value, $memberId, null, '套票主票验证，子票自动验证', $source = false,
                                $dtime = false, $terminal = 0, $chkNum, $strictMode = false, $idcard = '',
                                $isHandleChild = false);
                        }
                    }
                }
            } else {
                // 检测是不是子票
                $addonInfo = $this->getOrderAddonInfo($ordernum, 'pack_order');
                if ($addonInfo && $addonInfo['pack_order']) {
                    $isVerifyPackOrder = true;
                    //这边需要判断下是不是特殊用户验证子票不全验证主票
                    $packOrderInfo = $OrderTools->getOrderInfo($addonInfo['pack_order'],'ss.apply_did');
                    if ($this->isAllowPartUsed($packOrderInfo['apply_did'])) {  //验证数量=0的话就不要管了，单做全部验证
                        $isVerifyPackOrder = false;//先不验证主票放到最尾巴验证
                        $isFinallyVerifyPack = true;
                        $finallVerifyData = [
                            'ordernum' => $addonInfo['pack_order'],
                            'member_id' => $memberId,
                        ];
                    }
                    if ($isVerifyPackOrder) {
                        $this->CheckOrderSimply($addonInfo['pack_order'], $memberId, null, '套票子票验证，主票自动验证', false,
                            false, 0, 0, false, '', false);
                    }
                }
            }
        }

        if (!isset($orderInfo['status'])) {
            return ['code' => 101, 'msg' => '订单查找失败'];
        }

        if ($orderInfo['status'] == 1) {
            return true;
        }

        if ($orderInfo['status'] == 4) {
            return ['code' => 102, 'msg' => '订单待确认'];
        }

        if ($orderInfo['status'] == 3) {
            return ['code' => 102, 'msg' => '订单已取消'];
        }

        //完結的订单不能验证
        if ($orderInfo['status'] == 8) {
            return ['code' => 102, 'msg' => '订单已完結'];
        }

        //现场支付=4
        if ($orderInfo['paymode'] != 4 && $orderInfo['pay_status'] != 1) {
            pft_log('order/check_fail', json_encode($orderInfo));

            return ['code' => 0, 'msg' => '订单未支付无法验证'];
        }

        $extField = [];
        if ($dtime !== false) {
            $extField = ['dtime' => $dtime];
        } else {
            $extField = ['dtime' => date('Y-m-d H:i:s')];
        }

        $orderstatus = 1;
        $leftNum     = 0;
        /**分终端报表提前需求新增*/
        $isNoUsedBranch = false;  //是否有存在未使用的分终端快速验证
        $branchNoUsedNum = 0;     //分终端剩余数量
        $nowBranchUsedNum = 0;   //本次快速验证使用的分终端数量
        //可以验证的票数
        $verifiedNum   = isset($orderInfo['verified_num']) ? intval($orderInfo['verified_num']) : 0;
        $verifiedNum   = $verifiedNum > 0 ? $verifiedNum : 0;
        /**分终端报表提前需求新增*/
        //这边在获取下分终端实际验证数量
        $TerminalModel = new TerminalDbHandler();
        $branchUsedInfo    = $TerminalModel->getBranchUsedLog($ordernum,'finish_num,used_num');
        if ($branchUsedInfo['finish_num'] != $branchUsedInfo['used_num']){
            $isNoUsedBranch = true;
            $branchNoUsedNum = $branchUsedInfo['used_num'] - $branchUsedInfo['finish_num'] >= 0 ? $branchUsedInfo['used_num'] - $branchUsedInfo['finish_num'] : 0;
        }
        /**--------------------*/
        $canCheckedNum = $orderInfo['tnum'] - $verifiedNum;
        $canCheckedNum = $canCheckedNum > 0 ? $canCheckedNum : 0;

        $leftCanVerifiedNum = $canCheckedNum;

//        // 如果是自运行服务执行订单过期验证，且在分终端验证模式下可验证票数为0，那么设置验证票数为订单原始的票数(订单号:23027099)——cgp
//        // $source = 46是强制核销 -- zhangyz
//        if (($source == 19 || $source == 46) && $canCheckedNum == 0) {
//            $canCheckedNum = $orderInfo['tnum'];
//        }
        //根据票ID获取景区ID和终端号
        $commodityLandBiz = new Land();
        $landInfo         = $commodityLandBiz->getLandInfoByTidToJava($orderInfo['tid']);
        //如果是自动验证的或者强制验证的不判断押金了
        if ($landInfo['p_type'] == 'K' && !in_array($source,[OrderTrack::SOURCE_FORCE_VERIFY,OrderTrack::SOURCE_AUTO_SOAP])){
            $timeProductMdl = new TimingProduct();
            $timeProductInfo = $timeProductMdl->getTimingTicket($orderInfo['tid']);
            if ($timeProductInfo && $timeProductInfo['deposit'] > 0){
                $timeOrderDepositMdl = new TimingOrderDeposit();
                $timeOrderDepositInfo = $timeOrderDepositMdl->getTimeOrderDepositInfo($ordernum,'id',1);
                if (empty($timeOrderDepositInfo)){
                    return ['code' => 0, 'msg' => '计时卡订单需要押金，请先交押金在验证'];
                }
            }
        }


        //获取门票信息
        $modelTicket = new Ticket('slave');
        $tinfo       = $modelTicket->getTicketInfoById($orderInfo['tid'], 'title,apply_did,batch_check');

        // 切换门票获取第三方系统信息
        $otaProductBiz    = new \Business\Ota\Product();
        $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTid($orderInfo['tid']);
        if (empty($thirdBindInfoArr)) {
            return ['code' => 0, 'msg' => '获取门票绑定数据失败'];
        }
        $Mdetails = $thirdBindInfoArr['Mdetails'];
        $Mpath    = $thirdBindInfoArr['Mpath'];
        $sourceT  = $thirdBindInfoArr['sourceT'];
        $uuid     = $thirdBindInfoArr['uuid'];

        if ($strictMode === true && $tinfo['apply_did'] != $memberId) {
            return ['code' => 104, 'msg' => "权限校验失败，您不是该订单的供应商"];
        } elseif ($strictMode === 2 && $orderInfo['salerid'] != $memberId) {
            return ['code' => 104, 'msg' => "权限校验失败，您不是该订单的资源方"];
        }

        /**--------------未来删除代码开始-----------------**/
        //这边获取下是不是在提前记录报表前有redis记录的
        $redisService = RedisCache::Connect('terminal');
        $redisKey     = sprintf('bao_biao_write:%s',$ordernum);
        $needWriteOrder = $redisService->get($redisKey);
        $needWriteIdx = [];
        $needDelIdx   = [];
        $needWriteNum = 0;
        if ($needWriteOrder){
            $needWriteIdx  = json_decode($needWriteOrder,true);
        }
        /**--------------未来删除代码结束-----------------**/


        // 现在新的分终端过期自动验证走53的，这种情况直接给他能验证数量变成0
        if($source == OrderTrack::SOURCE_BRANCH_USED){//会存在分终端的过期时间不一致的情况要记得判断
            $orderstatus = 7;
            if ($chkNum == 0){
                $chkNum = $branchNoUsedNum;
            }
            /**--------------未来删除代码开始-----------------**/
            if ($needWriteIdx){
                $needDelIdx = $needWriteIdx;
                $needWriteNum = count($needDelIdx);
            }
            /**--------------未来删除代码结束-----------------**/

            //直接赋值了这个验证数量英文是不去修改他的验证数量和可退数量的，只是改下门票码状态和订单状态判断
            if ($canCheckedNum < 1 && $branchNoUsedNum - $chkNum  < 1){
                $orderstatus = 1;
            }
            $leftNum  = $canCheckedNum + ($branchNoUsedNum - $chkNum > 0 ? $branchNoUsedNum - $chkNum : 0);
            $nowBranchUsedNum = $branchNoUsedNum >= $chkNum ? $chkNum : $branchNoUsedNum;
            $canCheckedNum = 0;
        }elseif ($chkNum == 0 || $canCheckedNum == $chkNum) {
            //直接验证
            if ($isNoUsedBranch){  //说明分终端还有没验证完的
                if (in_array($source,[OrderTrack::SOURCE_FORCE_VERIFY,OrderTrack::SOURCE_AUTO_SOAP])){  //强制核销的
                    $nowBranchUsedNum = $branchNoUsedNum;
                    /**--------------未来删除代码开始-----------------**/
                    if ($needWriteIdx){
                        $needDelIdx = $needWriteIdx;
                        $needWriteNum = count($needDelIdx);
                    }
                    /**--------------未来删除代码结束-----------------**/
                }else{
                    if ($canCheckedNum == 0){
                        return ['code' => 106, 'msg' => "验证数量小于可核销数量（分终端）"];
                    }
                    $orderstatus = 7;
                    $isNoUsedBranch = false;  //分终端部分不做修改
                    $leftNum     = $leftNum + $branchNoUsedNum;
                }
            }
        } elseif ($chkNum > 0) {
            //app下没开启分批验证，并且验证数量小于可验证数据，提示不支持改单验证
            if ($tinfo['batch_check'] == 0 && $chkNum != $canCheckedNum && $source == 50) {
                return ['code' => 105, 'msg' => "app核销不支持改单验证"];
            }

            //$chk_num>0,表示分批验证
            //检测门票是否开启分批验证属性   ota过来的验证通知不校验分批验证属性
            if ($tinfo['batch_check'] == 0 && $source != 3) {
                return ['code' => 105, 'msg' => "门票{$tinfo['title']}不支持分批验证"];
            }
            $orderstatus = 7;

            //检测可被验证的票数
            if ($chkNum > $canCheckedNum) {
                return ['code' => 106, 'msg' => "验证数量小于可核销数量"];
            }
            if ($canCheckedNum == $chkNum) {
                $orderstatus = 1;
            }

            //设置验证数量为此次核销数量
            $leftNum       = $canCheckedNum - $chkNum + $branchNoUsedNum;
            $canCheckedNum = $chkNum;
        }
        /**分终端报表提前需求新增*/
        //实际操作的数量  普通的订单只有$canCheckedNum  分终端的情况$branchNoUsedNum  分终端过期操作的时候会对可验证数量做处理$canCheckedNum = 0 这个有逻辑判断
        $realOperateNum = $canCheckedNum + $nowBranchUsedNum;
        /**--------------------*/
        //来源处理
        if ($source === false) {
            $source = OrderTrack::SOURCE_INSIDE_SOAP;
        } else {
            $sourceList = load_config('track_source');
            $source     = array_key_exists($source, $sourceList) ? $source : OrderTrack::SOURCE_INSIDE_SOAP;
        }

        //如果订单状态更新失败，直接返回异常
        $orderUpdateBiz = new \Business\JavaApi\Order\OrderInfoUpdate();
        $baseCheckRes   = $orderUpdateBiz->fastVerify($ordernum, $canCheckedNum, $orderstatus, $extField['dtime'], intval($source),
            intval($orderInfo['status']));
        if ($baseCheckRes['code'] != 200) {
            return ['code' => 0, 'msg' => '系统异常，请重试'];
        }

        //统计订单的验证数据
        if($canCheckedNum > 0){
            (new OrderSubmit())->increCount($ordernum, 'verified', $canCheckedNum > $leftCanVerifiedNum ? 0 : $canCheckedNum,false,$this->isNeedUpdateCanRefundNum);
        }
//        if ($isChangeCanRefund){
//            (new OrderSubmit())->increCount($ordernum, 'canRefund',$isChangeCanRefund);
//        }
        /**分终端报表提前需求新增*/
        if ($isNoUsedBranch && $nowBranchUsedNum > 0){
            $TerminalModel->updateBranchUsedCase($ordernum,$nowBranchUsedNum);
        }
        /**--------------------*/
        //如果是闸机订单必须同步数据
        //$sourceT == 1 => 表示是九天的订单
        //$Mpath没有设置 => 早期通过轮询的订单，如果有设置的话，就直接通过通知地址通知给对方
        if ($sourceT == 1 && !$Mpath) {
            $machineSysMainModel = new \Model\Ota\MachineSys();
            $machineSysMainModel->addChangeOrder($ordernum, 3, $realOperateNum, 0, $orderInfo['salerid']);
        }

        $modelTerminal = new TerminalDbHandler();
        $modelTerminal->Save_Print_Log($ordernum, $orderInfo['salerid'], 0, $realOperateNum, 1, 4, $orderInfo['lid']);

        if ($landInfo) {
            $mainTerminal   = $landInfo['terminal'];
            $branchTerminal = 0;
            $lid            = $landInfo['id'];
        } else {
            $mainTerminal   = $terminal ? $terminal : 0;
            $branchTerminal = 0;
            $lid            = 0;
        }

        //判断是否处理计时验证后逻辑
        $isAfterCheck = false;
        //需要通知第三方的订单，ordermode=17是淘宝的
        if (isset($orderInfo['callback']) && $orderInfo['callback'] == 1 || $orderInfo['ordermode'] == 17) {
            $syncId = $modelTerminal->Save_Sync_Log($ordernum, $orderInfo['member']);

            $action      = ['orderSyncNotify'];
            $joyTimeLids = Cache::getInstance('redis', ['terminal'])->sMembers('joytime_lid_set');
            //淘宝或者九天
            if ($orderInfo['ordermode'] == 17 || in_array($orderInfo['lid'], $joyTimeLids)) {
                $action = ['orderSyncNotify', 'chargeCodeFee'];
            }
            pft_log('CheckOrderSimply/ota_debug', json_encode([$ordernum, $action]));

            // 推送第三方系统核销
            $queuePrams = [
                'actions'  => $action,
                'sourceId' => 4,
                'data'     => [
                    'syncid' => $syncId,
                    [
                        'ordernum'  => $ordernum,
                        'tnum'      => $realOperateNum,
                        'ordermode' => $orderInfo['ordermode'],
                        'remotenum' => $orderInfo['remotenum'],
                        'tnum_s'    => $canCheckedNum,
                        'member'    => $orderInfo['member'],
                        'endtime'   => $orderInfo['endtime'],
                        'tid'       => $orderInfo['tid'],
                        'lid'       => $orderInfo['lid'],
                    ],

                ],
            ];
            Queue::push('notify', 'afterOrderChecked_Job', $queuePrams, 'setting_terminal');
            $isAfterCheck = true;
        } elseif (!isset($orderInfo['callback'])) {
            pft_log('order/error', "$ordernum 不存在callback字段;" . pft_trace());
        }

        if (!$isAfterCheck && $landInfo['p_type'] == 'K') {
            //是计时卡的
            $queuePrams = [
                'actions'  => ['timingCard'],
                'sourceId' => 4,
                'data'     => [
                    'ordernum' => $ordernum,
                    'tid'      => $orderInfo['tid'],
                    'lid'      => $orderInfo['lid'],
                ],
            ];
            $requestId  = Queue::push('notify', 'afterOrderChecked_Job', $queuePrams, 'setting_terminal');
            pft_log('CheckOrderSimply',
                json_encode(['ac' => '快速验证', 'queuePrams' => $queuePrams, 'requestId' => $requestId],
                    JSON_UNESCAPED_UNICODE));
        }

        //如果有传入身份证，当作一票一证的订单去处理
        // if (!empty($idcard)) {
        //     $this->updateOrderTouristInfoState($ordernum, $idcard, 1);
        // }

        //追踪记录
        $track = new OrderTrack();

        //在cli模式下面不添加请求ip
        if (isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR']) {
            $tmpMemo = "订单快速验证，请求IP:{$_SERVER['REMOTE_ADDR']}";
        } else {
            $tmpMemo = "订单快速验证";
        }

        if ($memo) {
            $tmpMemo = $memo . ' - ' . $tmpMemo;
        }
        $trackId = 0;
        $branchTrackId = 0; //碰到分终端的数据
        //添加追踪记录
        if ($canCheckedNum > 0){
            $trackId = $track->addTrack($ordernum, OrderTrack::ORDER_VERIFIED, $orderInfo['tid'],
                $canCheckedNum, $leftNum, $source,
                $mainTerminal, $branchTerminal, '', $memberId,
                $orderInfo['salerid'], '', $tmpMemo);
            // 将追踪记录同步到终端数据库，避免手持机重打印出现的找不到订单的问题
            if ($trackId) {
                $this->terminalDb->execute($track->_sql());
            }
        }
        if ($isNoUsedBranch && $nowBranchUsedNum > 0){
            $branchTrackId = $track->addTrack($ordernum, OrderTrack::ORDER_VERIFIED, $orderInfo['tid'],
                $nowBranchUsedNum, $leftNum, $source == OrderTrack::SOURCE_BRANCH_USED ? $source : OrderTrack::SOURCE_BRANCH_VERIFY,
                $mainTerminal, $branchTerminal, '', $memberId,
                $orderInfo['salerid'], '', $tmpMemo);
            $this->terminalDb->execute($track->_sql());
        }

        /**--------------未来删除代码开始-----------------**/

        if ($needWriteNum > 0){
            $branchTrackId = $track->addTrack($ordernum, OrderTrack::ORDER_VERIFIED, $orderInfo['tid'],
                $needWriteNum, $leftNum, OrderTrack::SOURCE_INSIDE_SOAP,
                $mainTerminal, $branchTerminal, '', $memberId,
                $orderInfo['salerid'], '', '订单快速验证-规则升级后的历史订单处理');
            $this->terminalDb->execute($track->_sql());

            $surplusIdx = array_diff($needWriteIdx,$needDelIdx);
            if (empty($surplusIdx)){
                $redisService->del($redisKey);
            }else{
                $redisService->set($redisKey,json_encode($surplusIdx));
            }
        }
        /**--------------未来删除代码结束-----------------**/
        //添加门票追踪记录(后面这些都由java操作)
        //获取门票码信息
        $tourists = $OrderTools->getOrderTouristInfo($ordernum, 0, $idcard, [0,4]);
        $tourists = array_slice($tourists, 0, $canCheckedNum);
        if ($nowBranchUsedNum){
            $touristsBranch = $OrderTools->getOrderTouristInfo($ordernum, 0, '', [7]);
            $tourists  = array_merge($tourists,array_slice($touristsBranch, 0, $nowBranchUsedNum));
        }

        $touristUpdateCond = [
            'idxs' => array_column($tourists, 'idx')
        ];

        $javaApi = new  \Business\JavaApi\Order\OrderTouristUpdate();
        $javaApi->touristInfoUpdate($ordernum, ['checkState' => 1], $touristUpdateCond, $memberId);

        foreach ($tourists as $item) {
            //一票种一票
            if (in_array(substr($item['chk_code'], 0, 2), [80, 81])) {
                $inTnum    = $realOperateNum;
                $inLeftNum = $leftNum;
            } else {
                $inTnum    = 1;
                $inLeftNum = 0;
            }
            $temp = [
                'ordernum'        => $ordernum,
                'idx'             => $item['idx'],
                'track_action'    => 1,
                'tid'             => $orderInfo['tid'],
                'tnum'            => $inTnum,
                'left_num'        => $inLeftNum,
                'source'          => $source,
                'terminal'        => $mainTerminal,
                'branch_terminal' => $branchTerminal,
                'insert_time'     => time(),
                'oper_member'     => $memberId,
                'msg'             => $tmpMemo,
                'order_time'      => time(),
                'update_time'     => time(),
                'order_month'     => date('m'),
                'apply_did'       => $orderInfo['apply_did'],
                'sync_state'      => $orderInfo['sync_state']
            ];

            $touristTracks[] = $temp;
        }

        $addId = $track->table('pft_order_tourist_track')->addAll($touristTracks);
        if ($addId) {
            $this->terminalDb->execute($track->_sql());
        }

        //把可以进行异步处理的业务逻辑迁移走
        if ($trackId || $branchTrackId) {
            if (!$dtime) {
                $dtime = date('Y-m-d H:i:s');
            }

            $checkData = [
                'ordernum'       => $ordernum,
                'orderInfo'      => $orderInfo,
                'tid'            => $orderInfo['tid'],
                'lid'            => $lid,
                'canCheckedNum'  => $realOperateNum,
                'mainTerminal'   => $mainTerminal,
                'branchTerminal' => $branchTerminal,
                'memberId'       => $memberId,
                'source'         => $source,
                'dtime'          => $dtime,
            ];

            $queueData = [
                'track_id'   => $trackId,
                'track_data' => $checkData,
            ];

            $checkJobData = [
                'job_type' => 'check_async',
                'job_data' => $queueData,
            ];
            $queueId      = Queue::push('order', 'Order_Job', $checkJobData);

            //写入日志
            $checkJobData['job_data']['queue_id'] = $queueId;
            pft_log('order/check_async', json_encode($checkJobData['job_data'], JSON_UNESCAPED_UNICODE));
        }
        return true;
        }catch (\Exception $e){
        }finally{
            //这边方法不做return会覆盖
            if ($isFinallyVerifyPack){
                //这边计算下这次套票主票到底要验证多少张
                $orderVerifyBiz = new OrderVerify();
                $verifyCheckRes = $orderVerifyBiz->getSonVerifyPackOrderVerifyNum($finallVerifyData['ordernum'],$ordernum);
                if (in_array($verifyCheckRes['code'],[101,204])){  //黄金比例破坏了或者参数错误了
                    $this->CheckOrderSimply($finallVerifyData['ordernum'], $finallVerifyData['member_id'], null, '套票子票验证，主票自动验证', false,
                        false, 0, 0, false, '', false);
                }elseif ($verifyCheckRes['code'] == 200){

                    $packCanRefundNum = $verifyCheckRes['data']['need_can_refund'];
                    $packVerifyNum    = $verifyCheckRes['data']['need_verify_num'];
                    Helpers::sendDingTalkGroupRobotMessageRaw("订单号{$finallVerifyData['ordernum']}:{$ordernum}触发了,验证数量{$packVerifyNum},可退数量{$packCanRefundNum}",
                        DingTalkRobots::CANCEL_AND_VERIFY);
                    //这边分2部走算了
                    if ($packVerifyNum){
                        //这边验证主票的
                        $this->CheckOrderSimply($finallVerifyData['ordernum'], $finallVerifyData['member_id'], null, '套票子票验证，主票自动验证', false,
                            false, 0, $packVerifyNum, false, '', false,false);
                    }
//                    elseif ($packCanRefundNum){
//                        //这边说明不需要验证但是需要改下状态和可退的数量
//                        $this->packNoCheckOrderSimply($finallVerifyData['ordernum'],$finallVerifyData['member_id'],$packCanRefundNum,false,false,0);
//                    }
                }
            }
        }
    }
    public function packNoCheckOrderSimply($ordernum, $memberId, $canRefundNum, $dtime, $source, $terminal = 0)
    {
        try {
            $ordernum = strval($ordernum);
            // if (is_null($orderInfo) || !is_array($orderInfo) || !isset($orderInfo['lid'])) {
            $orderInfo    = $this->getOrderInfo($ordernum,
                'member,tid,lid,tnum,callback,ordermode,status,salerid,paymode,ss.ordermode,ss.pay_status,ss.remotenum, ss.dtime, ss.apply_did, ss.sync_state',
                false, false, 'verified_num,can_refund');
            $checkBaseRes = $this->_checkBaseSimply($orderInfo);
            if ($checkBaseRes['code'] == 100) {
                return true;
            } elseif ($checkBaseRes['code'] != 200) {
                return $checkBaseRes;
            }
            if ($dtime !== false) {
                $extField = ['dtime' => $dtime];
            } else {
                $extField = ['dtime' => date('Y-m-d H:i:s')];
            }

            $orderstatus = 7;

            //来源处理
            if ($source === false) {
                $source = OrderTrack::SOURCE_INSIDE_SOAP;
            } else {
                $sourceList = load_config('track_source');
                $source     = array_key_exists($source, $sourceList) ? $source : OrderTrack::SOURCE_INSIDE_SOAP;
            }

            //如果订单状态更新失败，直接返回异常
            $orderUpdateBiz = new \Business\JavaApi\Order\OrderInfoUpdate();
            $baseCheckRes   = $orderUpdateBiz->fastVerify($ordernum, $orderInfo['tnum'], $orderstatus,
                $extField['dtime'], intval($source),
                intval($orderInfo['status']));
            if ($baseCheckRes['code'] != 200) {
                return ['code' => 0, 'msg' => '系统异常，请重试'];
            }
            //追踪记录
            $track = new OrderTrack();
            //根据票ID获取景区ID和终端号
            $commodityLandBiz = new Land();
            $landInfo         = $commodityLandBiz->getLandInfoByTidToJava($orderInfo['tid']);

            if ($landInfo) {
                $mainTerminal = $landInfo['terminal'];
                if ($this->isSubTerminal) {
                    $branchTerminal = $terminal ? $terminal : 0;
                } else {
                    $branchTerminal = 0;
                }
                $lid = $landInfo['id'];
            } else {
                $mainTerminal   = $terminal ? $terminal : 0;
                $branchTerminal = $terminal ? $terminal : 0;
                $lid            = 0;
            }

            if ($mainTerminal == $branchTerminal) {
                $branchTerminal = 0;
            }

            //统计订单的验证数据
            (new OrderSubmit())->increCount($ordernum, 'canRefund', $canRefundNum);
            $OrderTools = new OrderTools();

            //在cli模式下面不添加请求ip
            if (isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR']) {
                $tmpMemo = "套票子票验证，主票自动入园，请求IP:{$_SERVER['REMOTE_ADDR']}";
            } else {
                $tmpMemo = "套票子票验证，主票自动入园";
            }


            //添加追踪记录
            $trackId = $track->addTrack($ordernum, OrderTrack::ORDER_ENTER_PARK, $orderInfo['tid'],
                $canRefundNum, $orderInfo['tnum'] - $orderInfo['verified_num'], $source,
                $mainTerminal, $branchTerminal, '', $memberId,
                $orderInfo['salerid'], '', $tmpMemo);
            // 将追踪记录同步到终端数据库，避免手持机重打印出现的找不到订单的问题
            if ($trackId) {
                $this->terminalDb->execute($track->_sql());
            }



            //随机获取
            $tourists = $OrderTools->getOrderTouristInfo($ordernum, 0, '', [0, 4]);
            if ($tourists) {
                $ticketCheckStatus = 7;
                //这边根据套票的门票码判断下门票码要怎么操作
                if (in_array(substr($tourists[0]['chk_code'], 0, 2), [80, 81])) {
                    if (substr($tourists[0]['chk_code'], 0, 2) == 81){
                        $ticketCheckStatus = 1;
                    }
                } else {
                    $tourists = array_slice($tourists, 0, $orderInfo['can_refund'] - $canRefundNum);
                }

                $touristUpdateCond = [
                    'idxs' => array_column($tourists, 'idx'),
                ];

                $javaApi = new  \Business\JavaApi\Order\OrderTouristUpdate();
                $javaApi->touristInfoUpdate($ordernum, ['checkState' => $ticketCheckStatus], $touristUpdateCond, $memberId);
            }
            $touristTracks = [];
            foreach ($tourists as $item) {
                //一票种一票
                if (in_array(substr($item['chk_code'], 0, 2), [80, 81])) {
                    $inTnum    = $orderInfo['tnum'] - $orderInfo['verified_num'];
                    $inLeftNum = $orderInfo['tnum'] - $orderInfo['verified_num'];
                } else {
                    $inTnum    = 1;
                    $inLeftNum = 1;
                }
                $temp = [
                    'ordernum'        => $ordernum,
                    'idx'             => $item['idx'],
                    'track_action'    => 1,
                    'tid'             => $orderInfo['tid'],
                    'tnum'            => $inTnum,
                    'left_num'        => $inLeftNum,
                    'source'          => $source,
                    'terminal'        => $mainTerminal,
                    'branch_terminal' => $branchTerminal,
                    'insert_time'     => time(),
                    'oper_member'     => $memberId,
                    'msg'             => $tmpMemo,
                    'order_time'      => time(),
                    'update_time'     => time(),
                    'order_month'     => date('m'),
                    'apply_did'       => $orderInfo['apply_did'],
                    'sync_state'      => $orderInfo['sync_state'],
                ];

                $touristTracks[] = $temp;
            }
            if ($touristTracks){
                $addId = $track->table('pft_order_tourist_track')->addAll($touristTracks);
                if ($addId) {
                    $this->terminalDb->execute($track->_sql());
                }
            }
            $modelTerminal = new TerminalDbHandler();
            if (isset($orderInfo['callback']) && $orderInfo['callback'] == 1 || $orderInfo['ordermode'] == 17) {
                $syncId = $modelTerminal->Save_Sync_Log($ordernum, $orderInfo['member'], 0, '', 0, 0, 0, array_column($touristTracks, 'idx'));

                $action      = ['orderSyncNotify'];
                $joyTimeLids = Cache::getInstance('redis', ['terminal'])->sMembers('joytime_lid_set');
                //淘宝或者九天
                if ($orderInfo['ordermode'] == 17 || in_array($orderInfo['lid'], $joyTimeLids)) {
                    $action = ['orderSyncNotify', 'chargeCodeFee'];
                }
                //本次操作数量（盘山套票主票分批要用到）
                $thisOperateNum = $canRefundNum;
                pft_log('CheckOrderSimply/ota_debug', json_encode([$ordernum, $action]));

                // 推送第三方系统核销
                $queuePrams = [
                    'actions'  => $action,
                    'sourceId' => 4,
                    'data'     => [
                        'tourist_idxs'  => array_column($touristTracks, 'idx'),
                        'syncid'        => $syncId,
                        [
                            'ordernum'  => $ordernum,
                            'tnum'      => $canRefundNum,
                            'ordermode' => $orderInfo['ordermode'],
                            'remotenum' => $orderInfo['remotenum'],
                            'tnum_s'    => $canRefundNum,
                            'member'    => $orderInfo['member'],
                            'endtime'   => $orderInfo['endtime'],
                            'tid'       => $orderInfo['tid'],
                            'lid'       => $orderInfo['lid'],
                            'thisOperateNum' => $thisOperateNum,
                        ],

                    ],
                ];
                Queue::push('notify', 'afterOrderChecked_Job', $queuePrams, 'setting_terminal');
                $isAfterCheck = true;
            } elseif (!isset($orderInfo['callback'])) {
                pft_log('order/error', "$ordernum 不存在callback字段;" . pft_trace());
            }

            return true;
        } catch (\Exception $e) {

        }

    }


    private function _checkBaseSimply($orderInfo){
        if (!isset($orderInfo['status'])) {
            return ['code' => 101, 'msg' => '订单查找失败'];
        }

        if ($orderInfo['status'] == 1) {
            return ['code' => 100, 'msg' => '直接输出ture'];
        }

        if ($orderInfo['status'] == 4) {
            return ['code' => 102, 'msg' => '订单待确认'];
        }

        if ($orderInfo['status'] == 3) {
            return ['code' => 102, 'msg' => '订单已取消'];
        }

        //完結的订单不能验证
        if ($orderInfo['status'] == 8) {
            return ['code' => 102, 'msg' => '订单已完結'];
        }

        //现场支付=4
        if ($orderInfo['paymode'] != 4 && $orderInfo['pay_status'] != 1) {
            pft_log('order/check_fail', json_encode($orderInfo));

            return ['code' => 0, 'msg' => '订单未支付无法验证'];
        }
        return ['code' => 200,'msg' => 'success'];
    }
    /**
     * 获取订单的序号ID，根据状态
     * <AUTHOR> Chen
     * @date 2018-05-22
     *
     * @param $orderId
     * @param  int  $checkState
     * @param  string  $printState  云票务print为1,
     *
     * @return mixed
     */
    public function getOrderToristIdx($orderId, $checkState = 0, $printState = -1)
    {
        $subOrderQueryModel = new SubOrderQuery();

        $where = [
            'check_state' => $checkState,
        ];

        if ($printState != -1) {
            $where['print_state'] = $printState;
        }

        $checkStateArr = [$checkState];
        $printStateArr = $printState != -1 ? [$printState] : [];
        $queryParams   = [[$orderId], false, [], [], $checkStateArr, $printStateArr];
        $queryRes      = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo','queryOrderTouristInfoByOrderId', $queryParams);
        $touristInfo   = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $touristInfo = $queryRes['data'];
        }

        //$touristInfo = $subOrderQueryModel->getInfoInTouristByOrder($orderId, 'idx', 1, $where, '', true);

        if (empty($touristInfo)) {
            return '';
        }

        $res = array_column($touristInfo, 'idx');

        return $res;
    }

    /**
     * 一票一码 or 一票一证订单更新验证、打印状态
     * <AUTHOR> Chen
     * @date 2017-05-17
     *
     * @param  string  $orderid  订单号
     * @param  string|array  $idCard  身份证
     * @param  bool|int  $check_state  验证状态：1验证2删除
     * @param  bool|int  $print_state  打印状态：1打印
     *
     * @return bool
     */
    public function updateOrderTouristInfoState($orderid, $idCard = '', $check_state = false, $print_state = false, $idx = 0)
    {
        $map = [];
        if (is_array($orderid)) {
            foreach ($orderid as &$item) {
                $item = strval($item);
            }
        } else {
            $orderid = [strval($orderid)];
        }

        if (is_array($idx)) {
            $map['idxs'] = $idx;
        } elseif (is_numeric($idx) && empty($idCard) && $idx > 0) {
            $map['idxs'] = [$idx];
        } else {
            if ($idCard != '') {
                //这个这样写的逻辑会有问题，如果$idCard = [1, 2, 3] 的时候，strpos 返回的是 null !== false
                //所以逻辑有问题
                if (is_array($idCard)) {
                    $map['idcards'] = $idCard;
                } else if (strpos($idCard, ',') !== false) {
                    $tmp            = explode(',', $idCard);
                    $map['idcards'] = $tmp;
                } else {
                    $map['idcards'] = [$idCard];
                }
            }
        }
        $data = [];
        if ($check_state > 0) {
            $data['checkState'] = $check_state;
        }

        if ($print_state > 0) {
            $data['printState'] = $print_state;
        }

        if (empty($data)) {
            return false;
        }

        $api = new \Business\JavaApi\Order\OrderTouristUpdate();
        $res = $api->BatchTouristInfoUpdate($orderid, $data, $map);

        return $res;
    }

    /**
     * 更新订单表的数据
     *
     * @param  string  $orderId  订单号
     * @param  int  $payMode  支付方式
     *
     * @return bool
     */
    public function updatePayModeByOrder($orderId, $payMode)
    {
        if (!$orderId) {
            return false;
        }
        $where    = ['ordernum' => strval($orderId)];
        $data     = ['paymode' => $payMode];
        $resOrder = $this->table(MainTableConst::TABLE_SS_ORDER)->where($where)->limit(1)->save($data);

        $where = ['orderid' => strval($orderId)];
        $data  = ['pmode' => $payMode];

        $resSplit = $this->table(MainTableConst::TABLE_ORDER_AIDS_SPLIT)->where($where)->limit(1)->save($data);

        return $resOrder && $resSplit;
    }

    /**
     * 根据订单号更新order_refer
     *
     */
    public function updateReferOrder($orderNum, $data)
    {
        $res = $this->table(MainTableConst::TABLE_ORDER_REFER)->where(['ordernum' => $orderNum])->save($data);

        return $res;
    }

}
