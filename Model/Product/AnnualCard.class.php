<?php

namespace Model\Product;

use Business\Face\AnnualAliFaceBiz;
use Business\JsonRpcApi\ScenicLocalService\EscapeTicket;
use Business\Order\OrderQueryMove;
use Business\Product\AnnualCardConst;
use Controller\Order\Traits\AnnualCardTrait;
use Library\Cache\Cache;
use Library\Model;
use Library\Tools;
use Library\Util\AnnualUtil;
use Model\Annual\AliFaceMapModel;
use Model\Member\Member;
use Model\Order\OrderTools;
use Model\Order\SubOrderQuery;

class AnnualCard extends Model
{
    use AnnualCardTrait;

    const ANNUAL_CARD_TABLE         = 'pft_annual_card'; //卡片信息表
    const ANNUAL_CARD_TABLE_HISTORY = 'pft_annual_card_history'; //卡片信息归档表
    const CARD_CONFIG_TABLE         = 'pft_annual_card_conf'; //年卡激活配置表
    const CARD_PRIVILEGE_TABLE      = 'pft_annual_card_privilege'; //年卡景区特权表
    const CARD_ORDER_TABLE          = 'pft_annual_card_order'; //年卡订单记录表
    const CARD_MAPPING_TABLE        = 'pft_annual_card_mapping';
    const CARD_RENEW_TABLE          = 'pft_annual_card_renew'; //年卡续费记录表
    const CARD_PHOTOS_TABLE         = 'pft_annual_card_photos'; //年卡照片记录表
    const CARD_TRADE_TABLE          = 'pft_annual_card_trade'; //年卡产品交易记录
    const CARD_ASYNC_ORDER_TABLE    = 'pft_annual_async_order'; //年卡异步下单重试
    const WX_ANNUAL_CONFIG_TABLE    = 'pft_annual_wx_config'; //年卡过期微信推送配置表

    const VIRTUAL_LEN = 8; //虚拟卡号长度

    //年卡交易类型
    const TRADE_SALE_CARD     = 0; //年卡出售
    const TRADE_RESUME_CARD   = 1; //年卡续费
    const TRADE_REAPPLY_CARD  = 2; //年卡补卡
    const TRADE_CANCEL_CARD   = 3; //年卡取消
    const TRADE_ACTIVITE_CARD = 4; //年卡激活
    const TRADE_CHECKED_CARD  = 5; //年卡验证（特权支付）
    const TRADE_UPGRADE_CARD  = 7; //年卡升级

    //交易来源
    const SOURCE_CHECKED = 18; //验证（特权支付）

    private $annualCardTable;

    public function __construct($parent_tid = 0, $sid = 0, $table = self::ANNUAL_CARD_TABLE)
    {
        parent::__construct('pft_business_case');
        $this->annualCardTable = $table;
        $this->parent_tid      = $parent_tid;
        $this->cacheKey        = "crd:{$sid}";
        $this->cache           = Cache::getInstance('redis');
    }

    /**
     * 批量根据虚拟卡号获取特权订单使用数量
     * author queyourong
     * date 2022/5/17
     *
     * @param  array  $virtualList
     *
     * @return array
     */
    public function batchGetVirtualOrderCount(array $virtualList)
    {
        if (empty($virtualList)) {
            return [];
        }
        $where = [
            'virtual_no' => ['in', $virtualList],
            'status'     => 1,
        ];

        return $this->table(self::CARD_ORDER_TABLE)->where($where)->group('virtual_no')->getField('virtual_no, count(id) as count',
            true);
    }

    /**
     * 根据字段获取年卡信息
     *
     * @param  [type] $identify 值
     * @param  string  $field  字段
     *
     * @return [type]           [description]
     */
    public function getAnnualCard($identify, $field = 'id', $options = [], $action = 'find')
    {

        if (!in_array($action, ['find', 'select'])) {
            return false;
        }

        if (isset($options['where'])) {
            $where = 1;
        } else {
            $where = [$field => $identify];
        }

        return $this->table($this->annualCardTable)->where($where)->$action($options);

    }

    /**
     * 批量获取年卡信息
     * Create by zhangyangzhen
     * Date: 2019/5/23
     * Time: 18:25
     *
     * @param $cardId
     * @param  string  $field
     * @param  int  $sid  供应商id
     *
     * @return array|mixed
     */
    public function getMultiAnnualCard($cardId, $field = 'id', $sid = 0)
    {
        if (!$cardId) {
            return [];
        }

        $where['id'] = ['in', $cardId];

        if ($sid) {
            $where['sid'] = $sid;
        }

        $res = $this->table($this->annualCardTable)->where($where)->getField($field);

        return $res ?: [];
    }

    /**
     * 是否需要填写身份证信息
     *
     * @param  [type]  $sid [description]
     * @param  [type]  $tid [description]
     *
     * @return boolean      [description]
     * @deprecated
     */
    public function isNeedID($sid, $tid)
    {

        $where = [
            'aid' => $sid,
            'tid' => $tid,
        ];

        return $this->table(self::CARD_CONFIG_TABLE)->where($where)->getField('cert_limit');

    }

    /**
     * 获取指定产品的关联年卡
     *
     * @return [type] [description]
     */
    public function getAnnualCards($sid, $pid, $options = [], $action = 'select')
    {

        $where = [
            'sid' => $sid,
            'pid' => $pid,
        ];

        if (isset($options['status'])) {
            $where['status'] = $options['status'];
        }

        if (isset($options['card_no'])) {
            $where['card_no'] = $options['card_no'];
        }

        if (isset($options['create_time'])) {
            $where['create_time'] = $options['create_time'];
        }

        if (isset($options['card'])) {
            $where['_string'] = 'physics_no="' . (string)dechex($options['card']) . '" OR card_no="' . (string)$options['card'] . '"';
        }

        if (isset($options['mobile'])) {
            $where['mobile'] = $options['mobile'];
        }

        if (isset($options['id_card_no'])) {
            $where['id_card_no'] = $options['id_card_no'];
        }
        if (isset($options['id'])) {
            $where['id'] = $options['id'];
        }

        $limit = ($options['page'] - 1) * $options['page_size'] . ',' . $options['page_size'];

        $field = 'id,virtual_no,card_no,physics_no,create_time';

        if ($action == 'select') {
            return $this->table(self::ANNUAL_CARD_TABLE)
                        ->where($where)
                        ->field($field)
                        ->limit($limit)
                        ->order('create_time DESC')
                        ->select();
        } else {
            return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->count();
        }
    }

    public function getAnnualCardsBySidAndIdCardNO($sid, $idCardNO, $status = [], $limit = 2, $field = '*')
    {
        $where = [
            'sid'        => $sid,
            'id_card_no' => $idCardNO,
        ];
        if (!empty($status)) {
            $where['status'] = ['in', $status];
        }

        return $this->table(self::ANNUAL_CARD_TABLE)
                    ->where($where)
                    ->field($field)
                    ->limit($limit)
                    ->select();
    }

    /**
     * 生成年卡
     *
     * @param  array  $list  年卡记录列表
     *
     * @return bool
     */
    public function createAnnualCard($list)
    {

        if (!$list) {
            return false;
        }

        $result = $this->table(self::ANNUAL_CARD_TABLE)->addAll($list);

        return boolval($result);
    }

    /**
     * 写入一条年卡信息并返回id
     *
     * @param  array  $saveData  年卡数据
     *
     * @return bool
     */
    public function createAnnualCardSingle($saveData)
    {

        if (!$saveData) {
            return false;
        }

        $result = $this->table(self::ANNUAL_CARD_TABLE)->add($saveData);

        return $result;
    }

    /**
     * 删除年卡
     *
     * @return [type] [description]
     */
    public function deleteAnnualCard($where)
    {
        return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->delete();

    }

    /**
     * 绑定物理卡
     *
     * @param  [type] $sid        [description]
     * @param  [type] $virtual_no [description]
     * @param  [type] $card_no    [description]
     * @param  [type] $physics_no [description]
     *
     * @return [type]             [description]
     */
    public function bindAnnualCard($sid, $virtual_no, $card_no, $physics_no)
    {
        $where = [
            'card_no'    => $card_no,
            'physics_no' => $physics_no,
            '_logic'     => 'OR',
        ];

        $find = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->find();

        if ($find) {
            return false;
        }

        $update = [
            'card_no'    => $card_no,
            'physics_no' => $physics_no,
        ];

        $where = [
            'sid'        => $sid,
            'virtual_no' => $virtual_no,
            'card_no'    => '',
        ];

        return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->save($update);
    }

    /**
     * 获取指定会员处于激活状态的年卡
     *
     * @param  [type] $sid      供应商id
     * @param  [type] $memberid 会员id
     *
     * @return string virtual_no 虚拟卡号
     */
    public function getActivedCard($sid, $memberid)
    {

        $where = [
            'sid'      => $sid,
            'memberid' => $memberid,
            'status'   => 1,
        ];

        return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->getField('virtual_no');
    }

    /**
     * 获取年卡库存
     *
     * @param  int  $sid  供应商id
     * @param  int  $pid  门票pid
     * @param  string  $type  虚拟卡/物理卡
     *
     * @return int
     */
    public function getAnnualCardStorage($sid, $pid, $type = 'virtual')
    {

        if (!$sid || !$pid) {
            return 0;
        }

        $where = [
            'sid'       => $sid,
            'status'    => AnnualCardConst::STATUS_IN_STOCK,
            'lock_type' => 0,
        ];

        if ($type == 'virtual') {
            $where['card_no'] = '';
        } else {
            $where['card_no'] = ['neq', ''];
        }

        if (is_array($pid)) {
            $where['pid'] = ['in', $pid];

            //->force('idx_pid')
            return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->group('pid')->getField('pid,count(id)', true);
        } else {
            $where['pid'] = $pid;

            //->force('idx_pid')
            return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->count();
        }

    }

    /**
     * 保存虚拟卡和订单的关系
     * author  leafzl
     * Date: 2018-8-13
     *
     * @param $ordernum
     * @param $virtual_no
     *
     * @return bool|mixed|string
     */
    public function orderMapping($ordernum, $virtual_no)
    {

        $data  = [
            'ordernum'   => $ordernum,
            'virtual_no' => $virtual_no,
        ];
        $check = $this->table(self::CARD_MAPPING_TABLE)->where($data)->find();
        if ($check) {
            return true;
        }

        return $this->table(self::CARD_MAPPING_TABLE)->add($data);
    }

    /**
     * 根据虚拟卡号获取订单号
     *
     * @param  string  $virtual_no  虚拟卡号
     *
     * @return [type]             [description]
     */
    public function getOrdernumByVirtualNo($virtual_no)
    {

        $where = [
            'virtual_no' => $virtual_no,
        ];

        return $this->table(self::CARD_MAPPING_TABLE)
                    ->where($where)
                    ->getField('ordernum');
    }

    /**
     * 根据订单号获取虚拟卡号
     *
     * @param  string  $orderum  订单号
     *
     * @return 虚拟卡号
     */
    public function getVirtualNoByOrdernum($ordernum)
    {

        $where = [
            'ordernum' => (string)$ordernum,
        ];

        return $this->table(self::CARD_MAPPING_TABLE)
                    ->where($where)
                    ->getField('virtual_no');
    }

    /**
     * 根据物理卡号获取虚拟考号
     * author  leafzl
     * Date: 2018-8-13
     *
     * @param $physics_no array 物理卡号
     *
     * @return mixed
     */
    public function getVirtualByPy($physics_no, $action = 'getField')
    {
        if (!$physics_no) {
            return [];
        }

        $where = [
            'status'     => ['not in', '2,4'],
            'physics_no' => ['in', $physics_no],
            'lock_type'  => 0,
        ];
        if ($action == 'getField') {
            $res = $this->table(self::ANNUAL_CARD_TABLE)
                        ->where($where)
                        ->getField('physics_no,virtual_no', true);
        } else if ($action == 'select') {
            $res = $this->table(self::ANNUAL_CARD_TABLE)
                        ->where($where)
                        ->field('physics_no,virtual_no,id')
                        ->select();
        } else {
            $res = $this->table(self::ANNUAL_CARD_TABLE)
                        ->where($where)
                        ->field('physics_no,virtual_no,id')
                        ->$action();
        }

        return $res ?: [];
    }

    /**
     * 获取年卡会员列表
     *
     * @param  int  $sid  供应商id
     * @param  array  $options  额外条件
     *
     * @return [type]          [description]
     */
    public function getMemberList($sid, $options = [], $action = 'select')
    {
        $status = (int)$options['status'];
        $time   = time();

        if ($sid) {
            $where['sid'] = $sid;
        }
        if ($status == 5) {
            $where['status'] = ['neq', 3];
        } else {
            if ($status != 6) {
                $where['status'] = $status;
                if (in_array($status, [1, 2]) && (!isset($options['isRenewal']) || empty($options['isRenewal']))) {
                    $where['avalid_end'] = ['EGT', $time];
                }
            } else {
                //搜索正常状态已过期
                $where['status']       = ['neq', 3];
                $where['avalid_begin'] = ['GT', 0];
                $where['avalid_end']   = ['ELT', $time];
            }
        }

        //卡号手机号等搜索操作
        if (isset($options['identify']) && $options['identify']) {
            $identify = $options['identify'];
            if (isset($options['search_type']) && $options['search_type'] != -1) {
                switch ($options['search_type']) {
                    //0物理卡 1 实体卡 2虚拟卡 3手机号 4身份证 5姓名
                    case 0:
                        $where['physics_no'] = dechex($identify);
                        break;
                    case 1:
                        $where['card_no'] = $identify;
                        break;
                    case 2:
                        $where['virtual_no'] = $identify;
                        break;
                    case 3:
                        $where['mobile'] = $identify;
                        break;
                    case 4:
                        $where['id_card_no'] = $identify;
                        break;
                    case 5:
                        $where['dname'] = $identify;
                        break;
                }
            } else {
                if (\ismobile($identify)) {
                    $where['mobile'] = $identify;
                } elseif (Tools::idcard_checksum18($identify)) {
                    $where['id_card_no'] = $identify;
                } else {
                    if (dechex($identify)) {
                        $where2['card_no']    = $identify;
                        $where2['virtual_no'] = $identify;
                        $where2['physics_no'] = dechex($identify);
                        $where2['id_card_no'] = $identify; //现在年卡支持多种证件类型购买，非身份证的时候得这边再加个条件判断下
                        $where2['_logic']     = 'or';
                        $where['_complex']    = $where2;
                    } else {
                        $where2['card_no']    = $identify;
                        $where2['virtual_no'] = $identify;
                        $where2['id_card_no'] = $identify;
                        $where2['_logic']     = 'or';
                        $where['_complex']    = $where2;
                    }
                }
            }
        }

        if (isset($options['page'], $options['page_size'])) {
            $limit = ($options['page'] - 1) * $options['page_size'] . ',' . $options['page_size'];
        } else {
            $limit = 10000;
        }

        if (isset($options['memberid'])) {
            $where['memberid'] = $options['memberid'];
            unset($where['status']);
        }

        if (!empty($options['sale_start']) && !empty($options['sale_end'])) {
            $where['sale_time'] = [
                'between',
                [strtotime($options['sale_start'] . '00:00:00'), strtotime($options['sale_end'] . '23:59:59')],
            ];
        }

        if (!empty($options['active_start']) && !empty($options['active_end'])) {
            $where['active_time'] = [
                'between',
                [strtotime($options['active_start'] . '00:00:00'), strtotime($options['active_end'] . '23:59:59')],
            ];
        }

        if (!empty($options['distributor_id'])) {
            $where['distributor_id'] = $options['distributor_id'];
        }

        if (!empty($options['pid'])) {
            $where['pid'] = ['in', $options['pid']];
        }

        if (!empty($options['id'])) {
            $where['id'] = ['=', $options['id']];
        }

        $field = 'id,sid,virtual_no,card_no,sale_time,memberid,activate_source,active_time,pid,status,avalid_begin,avalid_end,physics_no,distributor_id,dname,mobile,id_card_no,province,city,address,annual_status,ext_info';
        if ($action == 'select') {

            $list = $this->table($this->annualCardTable)
                         ->where($where)->field($field)
                         ->limit($limit)
                         ->order('sale_time desc')
                         ->select();

            //pft_log('debug' , 'sql' . $this->getLastSql());
            return $list ?: [];
        } else {
            $count = $this->table($this->annualCardTable)
                          ->where($where)
                          ->count();

            return (int)$count;
        }
    }

    /**
     * 年卡操作    16/10/8
     *
     * @param  [type] $type       [0 挂失 1 禁用 2 补卡 3 恢复]
     * @param  [type] $status     [0未激活，1激活,2禁用,3仓库,4挂失]
     *
     * @return [type]             [description]
     */
    public function operationAnnual($sid, $options = [])
    {
        //$condition['memberid'] = $options['memberid'];
        $condition['sid'] = $sid;
        $condition['id']  = $options['id'];

        //先查出原始的状态
        $annual = $this->table($this->annualCardTable)->where($condition)->find();
        if (!$annual) {
            return false;
        }
        //$oldStatus       = $annual['status'];
        $oldAnnualStatus = $annual['annual_status'];
        $type            = (int)$options['type'];
        // $data['active_time']     = time();
        if ($type == AnnualCardConst::ANNUAL_OPT_LOSS) {
            $data['status']        = AnnualCardConst::STATUS_LOSS;
            $data['annual_status'] = $oldAnnualStatus | AnnualCardConst::ANNUAL_STATUS_LOSS;
        } else if ($type == AnnualCardConst::ANNUAL_OPT_BAN) {
            //尝试删除人脸
            $callIsAliFacePlatform = AnnualAliFaceBiz::getInstance()->callIsAliFacePlatform($sid, $annual['pid']);
            if ($callIsAliFacePlatform) {
                $ticketModel = new \Model\Product\Ticket();
                $ticketInfo  = $ticketModel->getTicketInfoByPid($annual['pid']);
                $ordernum    = $this->getOrdernumByVirtualNo($annual['virtual_no']);
                //获取人脸id
                $annualAliFaceIdMapModel = new AliFaceMapModel();
                $faceIdList              = $annualAliFaceIdMapModel->getValidFaceIdListByVirtualNo($annual['virtual_no']);
                if (!empty($faceIdList)) {
                    foreach ($faceIdList as $faceId) {
                        AnnualAliFaceBiz::getInstance()->callDeleteFaceInfo([
                            'virtual_no' => $annual['virtual_no'],
                            'apply_did'  => (int)$sid,
                            'faceid'     => $faceId,
                            'order_id'   => $ordernum,
                            'lid'        => (int)$ticketInfo['lid'],
                            'id_card_no' => $annual['id_card_no'],
                        ]);
                    }
                }
            }
            //禁用年卡 尝试同步海康
            $scenicRpc = new EscapeTicket();
            $scenicRpc->syncHikFace(0, 'del', $annual['virtual_no']);
            $data['status']        = AnnualCardConst::STATUS_BAN;
            $data['annual_status'] = $oldAnnualStatus | AnnualCardConst::ANNUAL_STATUS_BAN;
        } else if ($type == AnnualCardConst::ANNUAL_OPT_FIX) {
            $data['card_no']       = $options['card_no'];
            $data['physics_no']    = dechex($options['physics_no']);
            $data['status']        = AnnualCardConst::STATUS_ACTIVE;
            $data['annual_status'] = $oldAnnualStatus & ~AnnualCardConst::ANNUAL_STATUS_LOSS;
        }
        if ($type == AnnualCardConst::ANNUAL_OPT_RECOVER) {
            //尝试注册人脸
            $callIsAliFacePlatform = AnnualAliFaceBiz::getInstance()->callIsAliFacePlatform($sid, $annual['pid']);
            if ($callIsAliFacePlatform) {
                $ticketModel = new \Model\Product\Ticket();
                $ticketInfo  = $ticketModel->getTicketInfoByPid($annual['pid']);
                $ordernum    = $this->getOrdernumByVirtualNo($annual['virtual_no']);
                AnnualAliFaceBiz::getInstance()->callRegisterFace([
                    'virtual_no'   => $annual['virtual_no'],
                    'apply_did'    => (int)$sid,
                    'name'         => $annual['dname'],
                    'id_card'      => $annual['id_card_no'],
                    'lid'          => (int)$ticketInfo['lid'],
                    'product_type' => 'I',
                    'order_id'     => $ordernum,
                    'begin_time'   => (int)date('Ymd', $annual['avalid_begin']),
                    'end_time'     => (int)date('Ymd', $annual['avalid_end']),
                ]);
            }
            $res = $this->_checkStatus($sid, $options);
        } else {
            $data['update_time'] = time();
            $res                 = $this->table($this->annualCardTable)->where($condition)->data($data)->save();
        }
        $cardManageService       = new \Business\AnnualCard\AnnualCardManage();
        $options['fail_success'] = (bool)$res;
        $cardManageService->addCardManageRecordWithCardInfo($type, $options, $annual);

        return $res;
    }

    /**
     * 年卡禁用操作
     * <AUTHOR> Li
     * @data 2018-08-14
     *
     * @param  int  $sid  供应商ID
     * @param  int  $memberid  用户ID
     * @param  string  $id  卡ID
     */
    public function forbiddenCard($sid, $memberid, $id)
    {
        //更新条件
        $condition['id']       = $id;
        $condition['sid']      = $sid;
        $condition['memberid'] = $memberid;

        //先查出原始的状态
        $annual = $this->table(self::ANNUAL_CARD_TABLE)->where($condition)->find();
        if (!$annual) {
            return false;
        }

        $oldAnnualStatus = $annual['annual_status'];

        //更新数据
        $data['status']        = AnnualCardConst::STATUS_BAN;
        $data['annual_status'] = $oldAnnualStatus | AnnualCardConst::ANNUAL_STATUS_BAN;
        $data['update_time']   = time();

        $result = $this->table(self::ANNUAL_CARD_TABLE)->where($condition)->data($data)->save();

        return $result !== false ? true : false;
    }

    /**
     * 年卡补卡操作
     * <AUTHOR> Li
     * @data 2018-08-14
     *
     * @param  int  $sid  供应商ID
     * @param  int  $memberid  用户ID
     * @param  string  $id  卡ID
     * @param  string  $card_no  实体卡号
     * @param  string  $physics_no  物理卡号
     */
    public function supplementCard($sid, $memberid, $id, $card_no, $physics_no)
    {
        //更新条件
        $condition['id']       = $id;
        $condition['sid']      = $sid;
        $condition['memberid'] = $memberid;

        //先查出原始的状态
        $annual = $this->table($this->annualCardTable)->where($condition)->find();
        if (!$annual) {
            return false;
        }

        $oldAnnualStatus = $annual['annual_status'];

        //更新数据
        $data['status']        = AnnualCardConst::STATUS_ACTIVE;
        $data['annual_status'] = $oldAnnualStatus & ~AnnualCardConst::ANNUAL_STATUS_LOSS;
        $data['update_time']   = time();
        $data['card_no']       = $card_no;
        $data['physics_no']    = $physics_no;

        $result = $this->table($this->annualCardTable)->where($condition)->data($data)->save();

        return $result !== false;
    }

    /**
     * 年卡恢复操作
     * <AUTHOR> Li
     * @data 2018-08-14
     *
     * @param  int  $sid  供应商ID
     * @param  int  $memberid  用户ID
     * @param  string  $id  卡ID
     */
    public function recoverCard($sid, $memberid, $id)
    {
        $options = [
            'memberid' => $memberid,
            'id'       => $id,
            'sid'      => $sid,
        ];

        return $this->_checkStatus($sid, $options);
    }

    public function jihuoCard($sid, $options = [])
    {
        $data['status']        = AnnualCardConst::STATUS_ACTIVE;
        $data['annual_status'] = AnnualCardConst::ANNUAL_STATUS_ACTIVE;
        $data['update_time']   = time();
        $condition['id']       = $options['id'];
        $this->table($this->annualCardTable)->where($condition)->data($data)->save();
    }

    /**
     * 获取年卡检查手机号码
     *
     * @param  int  $sid  供应商id
     * @param  array  $options  额外条件
     *
     * @return [type]          [description]
     */
    public function getCheckMemberList($sid, $options = [], $action = 'select')
    {
        $whereAll           = [
            'sid' => $sid,
        ];
        $whereAll['status'] = array('not in', '3');
        if ($options['identify']) {
            $identify = $options['identify'];
            if (ismobile($identify)) {
                $memberinfo           = (new Member)->getMemberInfo($identify, 'mobile');
                $tmp_mid              = $memberinfo['id'];
                $whereAll['memberid'] = $tmp_mid;
            } else {
                $whereAll['_string'] = "card_no='{$identify}' or virtual_no='{$identify}'";
            }
        }
        $fieldAll = 'id,memberid,virtual_no,status';
        if ($action == 'select') {
            return $this->table(self::ANNUAL_CARD_TABLE)
                        ->where($whereAll)->field($fieldAll)
                        ->order('sale_time desc')
                        ->select();
        }
    }

    public function operationConfirmannual($sid, $options = [], $yid)
    {
        //先查出对应年卡原始数据
        $annualMap = $this->table(self::ANNUAL_CARD_TABLE)->where([
            'id' => [
                'in',
                [$options['id'], $yid]
            ]
        ])->getField('id,annual_status');

        //原卡状态调整
        $condition['id']       = $yid;
        $data['status']        = AnnualCardConst::STATUS_BAN;
        $data['annual_status'] = $annualMap[$yid] | AnnualCardConst::ANNUAL_STATUS_BAN;
        $data['active_time']   = time();
        $this->table(self::ANNUAL_CARD_TABLE)->where($condition)->data($data)->save();

        //新卡状态调整
        $cdata['id']            = $options['id'];
        $vdata['status']        = AnnualCardConst::ANNUAL_STATUS_ACTIVE;
        $vdata['annual_status'] = $annualMap[$options['id']] | AnnualCardConst::ANNUAL_STATUS_BAN;
        $vdata['active_time']   = time();

        return $this->table(self::ANNUAL_CARD_TABLE)->where($cdata)->data($vdata)->save();
    }

    public function _checkStatus($sid, $options = [])
    {

        $conditions['status'] = AnnualCardConst::STATUS_BAN;
        $conditions['id']     = $options['id'];

        if (!$conditions['id']) {
            return false;
        }

        //先查出原始的状态
        $annual = $this->table($this->annualCardTable)->where($conditions)->find();
        if (!$annual) {
            return false;
        }

        $oldAnnualStatus = $annual['annual_status'];

        $data['status']        = (($oldAnnualStatus & (AnnualCardConst::ANNUAL_STATUS_ACTIVE | AnnualCardConst::ANNUAL_STATUS_LOSS | AnnualCardConst::ANNUAL_STATUS_USED)) > 0) ? 1 : 0;
        $data['annual_status'] = $oldAnnualStatus & ~AnnualCardConst::ANNUAL_STATUS_BAN;
        $data['update_time']   = time();

        return $this->table($this->annualCardTable)->where($conditions)->data($data)->save();
    }

    /**
     * 补卡检查卡是否存在
     *
     * @param  int  $sid  供应商id
     * @param  array  $options  id 虚拟卡号等信息
     *
     * @return [code]   200 允许使用 308此卡已使用不可再使用 309激活
     */
    public function _checkCard($sid, $options = [])
    {

        $condition['physics_no'] = dechex($options['physics_no']);
        $field                   = 'virtual_no,physics_no,id';
        $recovery                = $this->table(self::ANNUAL_CARD_TABLE)->where($condition)->field($field)->select();
        if (dechex($options['physics_no']) == $recovery[0]['physics_no']) {
            if ($options['id'] == $recovery[0]['id']) {
                return 309;
            } else {
                return 308;
            }
        } else {
            return 200;
        }
    }

    /**
     * 获取会员详细信息
     *
     * @param  int  $sid  供应商id
     * @param  int  $memberid  会员id
     * @param  bool  $onlyActive  之获取激活状态的年卡
     * @param  int  $virtualNo  虚拟卡号
     * @param  int  $cardId  年卡id
     * @param  int  $page  页码
     * @param  int  $pageSize  一页条数
     * @param  bool  $getTotal  是否获取总数
     *
     * @return array|int
     */
    public function getMemberDetail($sid, $memberid, $onlyActive = false, $virtualNo = 0, $cardId = 0, $page = 1, $pageSize = 10, $getTotal = false)
    {
        if ($sid != 1) {
            $where['sid'] = $sid;
        }

        if ($virtualNo) {
            $where['virtual_no'] = $virtualNo;
        } else {
            $where['memberid'] = $memberid;
        }

        if ($onlyActive) {
            $where['status'] = AnnualCardConst::STATUS_ACTIVE;
        } else {
            //默认过滤撤销的年卡
            $where['status'] = ['neq', AnnualCardConst::STATUS_REVOKE];
        }

        if ($cardId) {
            $where['id'] = $cardId;
        }
        if ($getTotal) {
            $total = $this->table($this->annualCardTable)->where($where)->field('id')->count();

            return intval($total);
        }

        return $this->getMemberPageCard($where, $page, $pageSize);
    }

    //获取c端用户年卡列表
    public function getMemberAnnualList($sid, $mid, $page = 1, $pageSize = 10, $annualValid = -1, $getTotal = false)
    {
        $where['sid']      = $sid;
        $where['memberid'] = $mid;
        if ($annualValid == 1) {
            $where['status']     = ['in', AnnualCardConst::ANNUAL_STATUS_VALID_MAP];
            $where['avalid_end'] = ['gt', time()];
        }
        if ($annualValid == 0) {
            //撤销的不展示
            $map['status']     = ['in', AnnualCardConst::ANNUAL_STATUS_INVALID_MAP];
            $map['avalid_end'] = ['elt', time()];
            $map['_logic']     = 'OR';
            $where['_complex'] = $map;
        }
        if ($getTotal) {
            $total = $this->table($this->annualCardTable)->where($where)->field('id')->count();

            return intval($total);
        }

        return $this->getMemberPageCard($where, $page, $pageSize);
    }

    private function getMemberPageCard($where, $page, $pageSize)
    {
        $field = 'id,sid,pid,memberid,card_no,virtual_no,physics_no,status,active_time,sale_time,create_time,avalid_begin,avalid_end,
        dname,mobile,id_card_no,annual_status,address,province,city,ext_info,remarks';

        $list = $this->table($this->annualCardTable)
                     ->where($where)
                     ->field($field)
                     ->order('avalid_end desc')
                     ->page($page, $pageSize)
                     ->select();

        return $list ?: [];
    }

    /**
     * 获取会员历史消费信息
     *
     * @param  int  $sid  供应商id
     * @param  int  $memberid  会员id
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     * @param  string  $action  查询还是统计
     *
     * @return mix
     * @deprecated
     * @see \Business\Product\AnnualCardPrivilege::getHistoryOrder();
     */
    public function getHistoryOrder($sid, $memberid, $page = 1, $size = 15, $action = 'select', $virtualNo = 0)
    {
        if ($sid != 1) {
            $where['aid'] = $sid;
        }

        if ($virtualNo) {
            $where['virtual_no'] = $virtualNo;
        } else {
            $where['memberid'] = $memberid;
        }
        $where['status'] = ['neq', 0];
        if ($action == 'select') {
            $limit = ($page - 1) * $size . ',' . $size;
            //获取订单号数组
            $orderArr = $this->table(self::CARD_ORDER_TABLE)
                             ->where($where)
                             ->limit($limit)
                             ->order('id desc')
                             ->getField('ordernum', true);

            $return = [];
            if ($orderArr) {

                //$field      = 'tnum,tprice,ordertime,totalmoney,ordernum,pid';
                //$orderModel = new SubOrderQuery('slave');
                //$orderList  = $orderModel->getInfoByOrder($orderArr, $field, 1, [], 'ordertime desc');

                //订单查询迁移1.1
                $orderMove = new OrderQueryMove();
                $orderList = $orderMove->getListByOrderNumNew($orderArr, 'ordertime', true);

                $pidArr = array_column($orderList, 'pid');

                if (($pidArr)) {
                    //批量获取景点-票名称
                    $javaApi   = new \Business\CommodityCenter\Ticket();
                    $ticketArr = $javaApi->queryTicketInfoByProductIds($pidArr, 'title,id,pid', 'id',
                        'title,imgpath,id');
                    $proMap    = [];
                    foreach ($ticketArr as $ticketInfo) {
                        $proMap[$ticketInfo['product']['id']] = [
                            'id'      => $ticketInfo['product']['id'],
                            'ttitle'  => $ticketInfo['ticket']['title'],
                            'tid'     => $ticketInfo['ticket']['id'],
                            'pid'     => $ticketInfo['ticket']['pid'],
                            'title'   => $ticketInfo['land']['title'],
                            'imgpath' => $ticketInfo['land']['imgpath'],
                            'landid'  => $ticketInfo['land']['id'],
                        ];
                    }

                    foreach ($orderList as &$item) {
                        $item['title']  = $proMap[$item['pid']]['ttitle'];
                        $item['ltitle'] = $proMap[$item['pid']]['title'];
                    }
                }

                $return = $orderList;
            }

            return $return;

        } else {
            return $this->table(self::CARD_ORDER_TABLE)->where($where)->count();
        }
    }

    /**
     * 年卡库存判断
     *
     * @param [type] $sid  [description]
     * @param [type] $pid  [description]
     * @param [type] $num  [description]
     * @param  string  $type  [description]
     */
    public function storageCheck($sid, $pid, $num, $type = 'virtual')
    {

        $left = $this->getAnnualCardStorage($sid, $pid, $type);

        return $left >= $num ? true : $left;
    }

    public function checkStatusForSale(array $virtual_arr)
    {
        if (count($virtual_arr) < 1) {
            return false;
        }

        $where = [
            'virtual_no' => ['in', implode(',', $virtual_arr)],
            'status'     => 3,
            'lock_type'  => 0,
        ];

        $count = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->count();

        return $count == count($virtual_arr);
    }

    /**
     * 获取[当日,当月,总数]已使用
     *
     * @param  int  $tid  特权门票tid
     * @param  string  $virtualNo  虚拟卡号
     * @param  bool  $onlyAll  是否只获取全部的
     *
     * @return array
     * @deprecated
     * @see \Business\AnnualCard\Privilege::getUsedTimes()
     */

    public function getUsedTimes($tid, $virtualNo, $onlyAll = false)
    {

        if (!$tid || !$virtualNo) {
            return [0, 0, 0];
        }

        $loop = [
            [
                //每日次数
                date('Y-m-d') . ' 00:00:00',
                date('Y-m-d') . ' 23:59:59',
            ],
            [
                //每月次数
                date('Y-m-01') . ' 00:00:00',
                date('Y-m-t') . ' 23:59:59',
            ],
            [], //总次数
        ];

        $all = $this->_countTimeRangeOrder($tid, $virtualNo, $loop[2]);
        if ($onlyAll) {
            return (int)$all;
        }

        $today = $this->_countTimeRangeOrder($tid, $virtualNo, $loop[0]);

        $month = $this->_countTimeRangeOrder($tid, $virtualNo, $loop[1]);

        return [(int)$today, (int)$month, (int)$all];

    }

    /**
     * 统计时间段内的订单总数
     *
     * @param  int  $tid  特权tid
     * @param  string  $virtualNo  虚拟卡号
     * @param  arrat  $timeRange  时间范围
     *
     * @return int
     */
    private function _countTimeRangeOrder($tid, $virtualNo, $timeRange)
    {

        $where = [
            'tid'        => $tid,
            'virtual_no' => $virtualNo,
            'status'     => 1,
        ];

        if ($timeRange) {
            $where['create_time'] = ['between', array_map('strtotime', $timeRange)];
        }

        $result = $this->table(self::CARD_ORDER_TABLE)->where($where)->sum('num');

        return intval($result);
    }

    public function getNewestConcumeTime($sid, $memberid)
    {
        $where = [
            'aid'      => $sid,
            'memberid' => $memberid,
        ];

        return $this->table(self::CARD_ORDER_TABLE)
                    ->where($where)
                    ->order('create_time desc')
                    ->field('create_time')
                    ->limit(2)
                    ->select();
    }

    /**
     * 删除年卡订单记录
     * author  leafzl
     * Date: 2018-12-11
     *
     * @param $id
     *
     * @return bool
     */
    public function updateAnnualOrderRecord($id, $ordernum)
    {
        if (!$id) {
            return false;
        }
        $where = [
            'id' => $id,
        ];

        $data = ['ordernum' => $ordernum];
        $res  = $this->table(self::CARD_ORDER_TABLE)->where($where)->save($data);

        return $res ?: false;
    }

    /**
     * 记录年卡订单
     *
     * @param  [type] $ordernum [description]
     * @param  [type] $tid      [description]
     * @param  [type] $memberid [description]
     *
     * @return [type]           [description]
     */
    public function annualOrderRecord($ordernum, $tid, $memberid, $aid, $num, $virtualNo, $packageId)
    {
        $data = [
            'ordernum'    => $ordernum,
            'tid'         => $tid,
            'memberid'    => $memberid,
            'aid'         => $aid,
            'virtual_no'  => $virtualNo,
            'num'         => $num,
            'create_time' => time(),
            'status'      => 1,
            'package_id'  => $packageId,
        ];

        return $this->table(self::CARD_ORDER_TABLE)->add($data);
    }

    /**
     * 取消年卡订单
     *
     * @param  string  $ordernum  订单号
     *
     * @return int | false
     */
    public function cancelOrder($ordernum)
    {
        $update = [
            'status' => 0,
        ];

        return $this->table(self::CARD_ORDER_TABLE)
                    ->where(['ordernum' => $ordernum])
                    ->limit(1)
                    ->save($update);
    }

    /**
     * 更改订单票数
     * <AUTHOR>
     * @date   2017-08-06
     *
     * @param  string  $ordernum  订单号
     * @param  int  $num  更改后的数量
     *
     * @return int  | false
     */
    public function changeOrder($ordernum, $num)
    {
        $update = [
            'num' => $num,
        ];

        return $this->table(self::CARD_ORDER_TABLE)
                    ->where(['ordernum' => $ordernum])
                    ->limit(1)
                    ->save($update);
    }

    /**
     * 售卡成功页面接口
     *
     * @param  [type] $ordernum 订单号
     *
     * @return [type]           [description]
     */
    public function orderSuccess($ordernum)
    {

        $virtual_no = $this->table(self::CARD_MAPPING_TABLE)->where(['ordernum' => $ordernum])->getField('virtual_no');

        if (!$virtual_no) {
            return [];
        }

        $where = [
            'virtual_no' => ['in', $virtual_no],
        ];

        $field = 'id,virtual_no,card_no as physics_no,pid,status';

        return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->field($field)->select();

    }

    /**
     * 判断是否是年卡订单
     *
     * @param  [type]  $ordernum [description]
     *
     * @return boolean           [description]
     */
    public function isAnnualOrder($ordernum)
    {
        return $this->table(self::CARD_MAPPING_TABLE)->where(['ordernum' => $ordernum])->count();
    }

    /**
     * 年卡产品验证
     *
     * @param  [type] $ordernum 订单号
     * @param  string  $type  ordernum virtual_no
     * @param  int  $opIdForThaw  触发解冻操作人
     *
     * @return bool
     */
    public function verifyAnnualOrder($ordernum, $type = 'ordernum', $opIdForThaw = 0)
    {

        if ($type != 'ordernum') {
            $where = [
                '_string' => "find_in_set('{$ordernum}', virtual_no)",
            ];
            //虚拟卡号获取订单号
            $ordernum = $this->table(self::CARD_MAPPING_TABLE)
                             ->where($where)
                             ->order('id DESC')
                             ->getField('ordernum');
        }

        $ordernum = (string)$ordernum;

        $orderTools = new OrderTools('slave');
        //获取订单详情 http://wiki.12301.io/display/jad/OrderQueryService
        $orderInfo = $orderTools->getOrderInfo($ordernum);
        if (!$orderInfo || $orderInfo['pay_status'] != 1) {
            return false;
        }
        //目前年卡一个订单只买一个卡, 这里只处理解冻末级分销链的情况
        //不考虑独立收款的解冻 因为独立收款的不会去冻结
        //自供自销的也不参与冻结
        //这里的totalmoney单位是分
        $orderOutPut = [
            //总的支付金额
            'totalMoney' => $orderInfo['totalmoney'],
            //门票数量
            'ticketNum'  => $orderInfo['tnum'],
            //供应商ID
            'orderAid'   => $orderInfo['aid'],
            //apply_did 顶级供应商id
            //支付方式
            'payMode'    => $orderInfo['paymode'],
            //操作人
            'opId'       => $opIdForThaw,
        ];
        $this->judgeAndProceedThawAnnualFundingWhenCheck($ordernum, $orderOutPut);
        //获取terminal配置
        $landModel = new Land('slave');
        $landInfo  = $landModel->getLandInfo($orderInfo['lid'], false, 'terminal');

        $terminal = $landInfo['terminal'];

        if (!$terminal) {
            return false;
        }

        $where = [
            'ordernum' => $ordernum,
        ];

        //获取虚拟卡号
        $virtualNo = $this->table(self::CARD_MAPPING_TABLE)
                          ->where($where)
                          ->getField('virtual_no');

        if (!$virtualNo) {
            return false;
        }

        $where = ['virtual_no' => ['in', $virtualNo]];

        $field = 'sid,activate_source';

        $res = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->field($field)->find();
        //0pc，1终端
        $source = 16;
        if ($res['activate_source']) {
            $source = 0;
        }
        $orderHandle = new \Model\Order\OrderHandler();
        $result      = $orderHandle->CheckOrderSimply($ordernum, $res['sid'], $orderInfo = null, $memo = '', $source,
            $dtime = false, $terminal);
        if ($result) {
            //订单验证成功之后写入发票中心
            $invoiceJobData = [
                'job_type' => 'invoice_billable',
                'job_data' => ['ordernum' => $ordernum],
            ];
            $queueId        = \Library\Resque\Queue::push('order', 'OrderStatus_Job', $invoiceJobData);

            //将数据写入日志
            $logData = json_encode([
                'key'      => 'asyncRollbackOrder',
                'ordernum' => $ordernum,
                'queueId'  => $queueId,
            ]);
            AnnualUtil::info([
                'tag'  => 'verifyAnnualOrder',
                'data' => $logData,
            ]);

            return true;
        } else {
            return false;
        }
    }

    /**
     * 保存年卡激活配置信息
     *
     * @param  array  $data
     *
     * @return mixed
     * @deprecated
     */
    public function saveCardConfig($parent_tid, $crdConf, $crdPriv)
    {
        return false;
        //$ret1 = $this->saveCrdConf($crdConf);
        //$ret2 = $this->setCardPrivilege($parent_tid, $crdPriv);
        //
        //if ($ret1 && $ret2) {
        //    return true;
        //} else {
        //    return false;
        //}
    }

    /**
     * 保存年卡激活配置信息
     *
     * @param  array  $data
     *
     * @return mixed
     * @deprecated
     */
    public function saveCrdConf($data)
    {
        $where = [
            'tid' => $data['tid'],
            'aid' => $data['aid'],
        ];

        $exist = $this->table(self::CARD_CONFIG_TABLE)->where($where)->getField('id');

        if ($exist) {
            $result = $this->table(self::CARD_CONFIG_TABLE)->where(['id' => $exist])->save($data);
        } else {
            $result = $this->table(self::CARD_CONFIG_TABLE)->add($data);
        }

        return $result !== false ? true : false;
    }

    /**
     * 获取年卡激活配置信息
     *
     * @param  [type] $tid [description]
     * @param  boolean  $isOrigin  是否返回原始数据库数据  update by zhangyangzhen 2018-12-04
     *
     * @return [type]      [description]
     *
     * @deprecated
     * @see \Business\AnnualCard\Privilege::getPrivilegeInfoByParentTid()
     */
    public function getCrdConf($tid)
    {

        $return = [];

        $config = $this->table(self::CARD_CONFIG_TABLE)->where(['tid' => $tid])->find();

        $return['auto_active_days'] = $config['auto_act_day'];
        $return['search_limit']     = $config['srch_limit'];
        $return['cert_limit']       = $config['cert_limit'];
        $return['act_notice']       = $config['act_notice'];

        switch ($config['act_notice']) {
            case 0:
                $return['nts_tour'] = $return['nts_sup'] = 0;
                break;

            case 1:
                $return['nts_tour'] = 1;
                $return['nts_sup']  = 0;
                break;

            case 2:
                $return['nts_tour'] = 0;
                $return['nts_sup']  = 1;
                break;

            case 3:
                $return['nts_tour'] = 1;
                $return['nts_sup']  = 1;
                break;

            default:
                $return['nts_tour'] = $return['nts_sup'] = 0;
                break;
        }

        $return['priv'] = $this->table(self::CARD_PRIVILEGE_TABLE)
                               ->where(['parent_tid' => $tid, 'status' => 1])
                               ->field('tid,aid,use_limit')
                               ->select();

        return $return;

    }

    public function getCache()
    {
        return $this->cache->get($this->cacheKey);
    }

    public function setCache($json)
    {
        return $this->cache->set($this->cacheKey, $json, '', 1800);
    }

    public function rmCache()
    {
        // return $this->cache->rm($this->cacheKey);
    }
    //

    /**
     * 配置年卡特权景区（增/删/改门票对应特权景区）
     *
     * @param $parentId
     * @param $data
     *
     * @return bool
     */
    public function setCardPrivilege($parentId, $data)
    {

        ////已记录在库的特权门票
        //$exists_tids = $this->getPrivilegeInfo(
        //    ['parent_tid' => $parentId, 'status' => 1],
        //    'tid,id'
        //);
        //$exists_tids = $exists_tids ?: [];
        //$exists_tids = array_keys($exists_tids);
        //
        ////本次提交的特权门票
        //$submit_tids = array_keys($data);
        //
        //$to_insert = array_diff($submit_tids, $exists_tids);
        //
        //$to_update = array_intersect($submit_tids, $exists_tids);
        //
        //$to_delete = array_diff($exists_tids, $submit_tids);
        //
        //$this->startTrans();
        //
        ////TODO:事务
        //if ($to_delete) {
        //    if (!$this->deleteCardPrivilege($parentId, $to_delete)) {
        //        echo $this->getDbError();
        //        $this->rollback();
        //
        //        return false;
        //    }
        //}
        //
        //if ($to_update) {
        //    if (!$this->updateCardPrivilege($parentId, $data, $to_update)) {
        //        $this->rollback();
        //
        //        return false;
        //    }
        //}
        //
        //if ($to_insert) {
        //    if (!$this->addCardPrivilege($parentId, $data, $to_insert)) {
        //        $this->rollback();
        //
        //        return false;
        //    }
        //}
        //
        //$this->commit();

        return true;
    }

    /**
     * 获取年卡产品特权信息
     *
     * @param $condition 特权条件
     * @param $field     获取的字段
     *
     * @return mixed
     * @deprecated
     * @see \Business\AnnualCard\Privilege::getPrivilegeInfoByParentTid()
     */
    public function getPrivilegeInfo($condition, $field)
    {
        return $this->table(self::CARD_PRIVILEGE_TABLE)->where($condition)->getField($field, true);
    }

    /**
     *  分页获取年卡产品特权信息
     * <AUTHOR>
     * @date 2020/6/1
     *
     * @param  array  $condition  特权条件
     * @param  string  $field  获取的字段
     * @param  int  $page  获取的字段
     * @param  int  $pageSize  获取的字段
     *
     * @return
     * @deprecated
     * @see \Business\AnnualCard\Privilege::getPrivilegeInfoByParentTidPage()
     */
    public function getPrivilegeInfoForPage(array $condition, string $field, int $page, int $pageSize)
    {
        return $this->table(self::CARD_PRIVILEGE_TABLE)->where($condition)->field($field)->page($page,
            $pageSize)->select();
    }

    /**
     * 保存年卡景区特权信息
     *
     * @param  array  $data
     *
     * @return bool|string
     */
    public function addCardPrivilege($parent_tid, $data, $to_insert)
    {
        $insert = [];
        foreach ($to_insert as $tid) {
            $insert[] = [
                'parent_tid' => $parent_tid,
                'aid'        => $data[$tid]['aid'],
                'tid'        => $tid,
                'use_limit'  => $data[$tid]['use_limit'],
                'status'     => 1,
            ];
        }

        return $this->table(self::CARD_PRIVILEGE_TABLE)->addAll($insert);
    }

    /**
     * 删除年卡特权景区
     *
     * @param $condition
     *
     * @return bool
     */
    public function deleteCardPrivilege($parent_tid, $tid_arr)
    {
        $where = [
            'parent_tid' => $parent_tid,
            'tid'        => ['in', implode(',', $tid_arr)],
            'status'     => 1,
        ];

        return $this->table(self::CARD_PRIVILEGE_TABLE)->where($where)->delete();
    }

    /**
     * 更新年卡特权景区信息
     *
     * @param $condition
     * @param $data
     *
     * @return bool
     */
    public function updateCardPrivilege($parent_tid, $data, $tid_arr)
    {
        foreach ($tid_arr as $tid) {

            $where = [
                'parent_tid' => $parent_tid,
                'tid'        => $tid,
            ];

            $update = [
                'use_limit' => $data[$tid]['use_limit'],
                'status'    => 1,
            ];

            $tmp_res = $this->table(self::CARD_PRIVILEGE_TABLE)->where($where)->save($update);

            if ($tmp_res === false) {
                return false;
            }
        }

        return true;
    }

    /**
     * 年卡票类编辑默认字段参数
     *
     * @return array
     */
    public function createDefaultParams()
    {
        $default = [
            'preorder_early_days' => 0,
            'verify_time'         => 0,
            'verify_disable_week' => '',
            'refund_audit'        => 0,
            'refund_rule'         => 3,
            'cancel_sms_supplier' => 0,
            'type'                => 'I',
            // 'order_sms_supplier'  => 0,
            //'product_id'          => 0,
            'refund_type'         => 1,
            'buy_min_amount'      => 1,
            'buy_max_amount'      => 0,
            'pay_way'             => 1,
            // 'order_sms_buyer'     => 1,
            'nopay_max_minu'      => 120,
            'buy_limit_max_age'   => '',
            'buy_limit_min_age'   => '',
        ];

        return $default;
    }

    /**
     * 根据虚拟卡号对应相应的订单号
     *
     * @param  array  $virtualArr  虚拟卡号数组
     *
     * @return array ['虚拟卡号' => '订单号']
     */
    public function getOrdersByVirtualNo($virtualArr)
    {

        if (!$virtualArr) {
            return [];
        }

        $where = [
            'virtual_no' => ['in', $virtualArr],
        ];

        $list = $this->table(self::CARD_MAPPING_TABLE)
                     ->where($where)
                     ->getField('virtual_no,ordernum');

        return $list ?: [];

    }

    /**
     * 获取购买年卡时的备注
     *
     * @param  array  $virtualArr  虚拟卡号数组
     *
     * @return ['虚拟卡号' => '备注']
     */
    public function getAnnualMemo($virtualArr)
    {

        if (!$virtualArr) {
            return [];
        }

        //获取对应的购买订单号
        $orderMap = $this->getOrdersByVirtualNo($virtualArr);
        $orderArr = array_values($orderMap);
        $return   = [];
        if ($orderArr) {
            $where = [
                'orderid' => ['in', $orderArr],
            ];

            $subOrderModel = new SubOrderQuery();
            $list          = $subOrderModel->getInfoInDetailByOrder($orderArr, 'orderid, memo', 2);

            $list = $list ?: [];

            foreach ($orderMap as $vir => $ordernum) {
                if (isset($list[$ordernum])) {
                    $return[$vir] = $list[$ordernum];
                } else {
                    $return[$vir] = '';
                }
            }

        }

        return $return;
    }

    /**
     * 取消年卡订单
     *
     * @param  string  $ordernum  订单号
     * @param  string  $virtualNo  虚拟卡号
     *
     * @return bool
     */
    public function cancelAnnualOrder($ordernum, $virtualNo = '')
    {
        if (!$virtualNo) {
            return false;
        }

        //在这边临时做下处理，如果一个虚拟卡对应多笔订单
        //说明有可能是是有笔订单没有支付，然后有 pft_annual_card_mapping 的数据，后面又有一笔支付的正常订单
        //这种情况暂时不去删除年卡数据
        $tmplist     = $this->table(self::CARD_MAPPING_TABLE)
                            ->where(['virtual_no' => $virtualNo])
                            ->field('ordernum')->select();
        $tmpOrderArr = array_column($tmplist, 'ordernum');
        $ordernumArr = array_unique($tmpOrderArr);

        if (count($ordernumArr) > 1) {
            return false;
        }

        $card = $this->getAnnualCard($virtualNo, 'virtual_no');

        //非待激活状态或者禁用（过期后禁用）
        if ($card['status'] != 0 || $card['status'] != 2) {
            return false;
        }

        $this->startTrans();

        //更新年卡状态
        //$data = [
        //    'status'      => 3,
        //    'update_time' => time(),
        //];
        //
        //$upRes = $this->table(self::ANNUAL_CARD_TABLE)
        //              ->where(['id' => $card['id']])
        //              ->save($data);

        //年卡直接删除
        $upRes = $this->table(self::ANNUAL_CARD_TABLE)
                      ->where(['id' => $card['id']])
                      ->delete();

        if (!$upRes) {
            $this->rollback();

            return false;
        }

        //清除年卡订单记录
        $delRes = $this->table(self::CARD_MAPPING_TABLE)
                       ->where(['ordernum' => $ordernum])
                       ->delete();

        if (!$delRes) {
            $this->rollback();

            return false;
        }

        $this->commit();

        return true;

    }

    /**
     * 检测年卡的信息是否已经存在
     * @date   2017-03-27
     * <AUTHOR>
     *
     * @param  string  $field  检测的字段名称
     * @param  string  $param  检测的字段参数
     * @param  array  $map  额外条件
     *
     * @return array|bool
     */
    public function checkCard($field, $param, $map = [], $fields = 'id')
    {
        if (!$field || !$param) {
            return false;
        }
        $where = [$field => $param];
        if ($map) {
            $where = array_merge($where, $map);
        }

        $res = $this->table($this->annualCardTable)->field($fields)->where($where)->find();

        return $res ?: false;
    }

    /**
     * 更新年卡的字段信息
     * @date   2017-03-27
     * <AUTHOR>
     *
     * @param  int  $id  表自增ID
     * @param  string  $physicsCard  物理卡号ID
     * @param  string  $cardNo  卡号
     *
     * @return id|false
     */
    public function updateCardById($id, $physicsCard, $cardNo)
    {
        $data = [];
        if ($physicsCard) {
            $data['physics_no'] = $physicsCard;
        }

        if ($cardNo) {
            $data['card_no'] = $cardNo;
        }

        if (empty($data)) {
            return false;
        }
        $data['update_time'] = time();
        $res                 = $this->table($this->annualCardTable)->where(['id' => $id])->save($data);

        return $res ?: false;
    }

    /**
     * 根据ID获取年卡信息
     * @date   2017-03-27
     * <AUTHOR>
     *
     * @param  int  $cardId  年卡表ID
     * @param  string  $field  查询字段
     *
     * @return array
     */
    public function getCardInfoById($cardId, $field)
    {
        if (!$cardId || !$field) {
            return [];
        }
        $res = $this->table($this->annualCardTable)
                    ->field($field)
                    ->where(['id' => $cardId])
                    ->find();

        return $res ?: [];
    }

    /**
     * 根据ID获取年卡信息
     * @date   2017-03-27
     * <AUTHOR>
     *
     * @param  array  $virtual  虚拟卡号数组
     *
     * @return array|false
     */
    public function getCardInfoByVirtual(array $virtual, $field = '*')
    {
        $where = ['virtual_no' => ['IN', $virtual]];
        $res   = $this->table(self::ANNUAL_CARD_TABLE)->field($field)->where($where)->select();

        return $res ? $res : false;
    }

    /**
     * 是否是年卡支付的订单
     * <AUTHOR>
     * @date   2017-08-06
     *
     * @param  string  $ordernum  订单号
     *
     * @return boolean
     */
    public function isFromAnnual($ordernum)
    {

        if (!$ordernum) {
            return false;
        }

        $where = [
            'ordernum' => (string)$ordernum,
        ];

        $id = $this->table(self::CARD_ORDER_TABLE)->where($where)->getField('id');

        return $id ? true : false;
    }

    /**
     * 添加续费记录
     * <AUTHOR>
     * @date   2017-11-24
     *
     * @param  int  $mid  分销商id
     * @param  int  $sid  供应商id
     * @param  int  $cardId  年卡id
     * @param  int  $money  续费金额
     * @param  int  $ordernum  订单号
     * @param  int  $status  续费状态
     * @param  bool
     */
    public function addRenewRecord($mid, $sid, $cardId, $money, $ordernum = '', $status = 1)
    {

        if (!($mid && $sid && $cardId && is_numeric($money))) {
            return false;
        }

        $data = [
            'sid'         => $sid,
            'card_id'     => $cardId,
            'money'       => $money,
            'memberid'    => $mid,
            'order_id'    => $ordernum,
            'status'      => $status,
            'create_time' => time(),
        ];

        $result = $this->table(self::CARD_RENEW_TABLE)->add($data);

        return boolval($result);
    }

    /**
     * 获取续费订单信息
     *
     * @date   2018-03-07
     * <AUTHOR> Lan
     *
     * @param  string  $orderId  订单号
     * @param  string  $field  查询字段
     *
     * @return  array
     */
    public function getRenewRecordByOrder($orderId, $field = 'id, status')
    {
        if (!$orderId) {
            return [];
        }
        $result = $this->table(self::CARD_RENEW_TABLE)->field($field)->where(['order_id' => $orderId])->find();

        return $result ?: [];
    }

    /**
     * 更新续费订单信息
     *
     * @date   2018-03-07
     * <AUTHOR> Lan
     *
     * @param  string  $orderId  订单号
     * @param  array  $data  查询字段
     *
     * @return  bool
     */
    public function setRenewRecordByOrder($orderId, $data)
    {
        if (!$orderId || !$data) {
            return false;
        }
        $result = $this->table(self::CARD_RENEW_TABLE)->where(['order_id' => $orderId])->save($data);

        return $result ?: false;
    }

    /**
     * 更新年卡出售/激活时间
     * <AUTHOR>
     * @date   2017-11-24
     *
     * @param  string  $virtualNo  虚拟卡号
     * @param  string  $field  时间字段
     * @param  int  $val  更新后的时间
     *
     * @return bool
     */
    public function updateTimeField($virtualNo, $field, $val)
    {

        if (!$virtualNo || !$field || !$val) {
            return false;
        }

        if (!in_array($field, ['active_time', 'sale_time'])) {
            return false;
        }

        $where = [
            'virtual_no' => $virtualNo,
        ];

        $data                = [
            $field => $val,
        ];
        $data['update_time'] = time();

        $result = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->limit(1)->save($data);

        return boolval($result);
    }

    /**
     * 将年卡更新为激活状态
     * <AUTHOR>
     * @date   2017-11-27
     *
     * @param  string  $virtualNo  虚拟卡号
     *
     * @return bool
     */
    public function activeStatus($virtualNo, $memberid = 0)
    {

        if (!$virtualNo) {
            return false;
        }

        $where = [
            'virtual_no' => $virtualNo,
        ];

        $data = [
            'status'        => AnnualCardConst::STATUS_ACTIVE,
            'annual_status' => AnnualCardConst::ANNUAL_STATUS_ACTIVE,
            'active_time'   => time(),
            'update_time'   => time(),
        ];
        //绑定用户
        if ($memberid) {
            $data['memberid'] = $memberid;
        }

        $res = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->limit(1)->save($data);

        return boolval($res);
    }

    /**
     * 更新年卡状态
     * <AUTHOR>
     *
     * @param  string  $virtualNo
     * @param  int  $status
     *
     * @return bool
     */
    public function updateStatusByVirtualNo(string $virtualNo, int $status): bool
    {
        if (empty($virtualNo) || !in_array($status, [0, 1, 2, 3, 4, 5])) {
            return false;
        }

        $where = [
            'virtual_no' => $virtualNo,
        ];

        $annualStatus = $status;
        if ($status == 3) {
            $annualStatus = AnnualCardConst::ANNUAL_STATUS_IN_STOCK;
        }

        //如果是5 设置成已使用的情况
        if ($status == 5) {
            $status       = AnnualCardConst::STATUS_ACTIVE;
            $annualStatus = AnnualCardConst::ANNUAL_STATUS_USED;
        }

        $data = [
            'status'        => $status,
            'annual_status' => $annualStatus,
        ];

        return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->save($data);
    }

    /**
     * 将年卡更新为已售未激活状态
     * <AUTHOR>
     * @date   2018-06-29
     *
     * @param  string  $virtualNo  虚拟卡号
     *
     * @return array
     */
    public function saleStatus($virtualNo)
    {

        if (!$virtualNo) {
            return false;
        }

        $where = [
            'virtual_no' => $virtualNo,
        ];

        $data = [
            'status'        => AnnualCardConst::STATUS_NOT_ACTIVE,
            'annual_status' => AnnualCardConst::ANNUAL_STATUS_NOT_ACTIVE,
            'update_time'   => time(),
        ];

        $res = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->save($data);

        return boolval($res);
    }

    /**
     * 年卡续费,将之前下的订单作废,特权次数重新统计
     * <AUTHOR>
     * @date   2017-11-28
     *
     * @return bool
     */
    public function orderReset($virtualNo)
    {

        if (!$virtualNo) {
            return false;
        }

        $where = [
            'virtual_no' => $virtualNo,
            'status'     => 1,
        ];

        $data = [
            'status' => 2,
        ];

        $result = $this->table(self::CARD_ORDER_TABLE)->where($where)->save($data);

        return $result !== false;
    }

    /**
     * 获取年卡订单信息
     *
     * @date   2018-03-07
     * <AUTHOR> Lan
     *
     * @param  string  $virtualNo  虚拟卡号
     * @param  string  $field  查询字段
     *
     * @return  array
     */
    public function getOrderData($virtualNo, $field = 'id')
    {
        if (!$virtualNo) {
            return [];
        }

        $where = [
            'virtual_no' => $virtualNo,
            'status'     => 1,
        ];

        $result = $this->table(self::CARD_ORDER_TABLE)->where($where)->field($field)->find();

        return $result ?: [];

    }

    /**
     * 获取指定会员的拿卡绑定信息
     * <AUTHOR>
     * @date   2017-12-20
     *
     * @param  int  $sid  供应商id
     * @param  array  $memArr  会员id数组
     *
     * @return array
     */
    public function getCardListForMember($sid, $memArr, $field = '*')
    {

        if (!$sid || !$memArr) {
            return [];
        }

        $where = [
            'sid'      => $sid,
            'memberid' => ['in', $memArr],
            'status'   => 1,
        ];

        $list = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->field($field)->select();

        return $list ?: [];
    }

    /**
     * 根据物理卡号获取年卡
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  array  $physicsArr  物理卡号数组(十六进制)
     * @param  string  $field  要获取的字段
     *
     * @return array
     */
    public function getCardsByPhysics($physicsArr, $field = 'id, sid, status, virtual_no, memberid, physics_no')
    {

        if (!$physicsArr) {
            return [];
        }

        $where = [
            'physics_no' => ['in', $physicsArr],
        ];

        $list = $this->table($this->annualCardTable)->where($where)->field($field)->select();

        return $list ?: [];
    }

    /**
     * 根据年卡ID更新年卡数据
     *
     * @date   2018-03-07
     * <AUTHOR> Lan
     *
     * @param  int  $annualId  年卡ID
     * @param  array  $data  更新数据
     *
     * @return bool
     */
    public function updateAnnualById($annualId, $data)
    {
        if (!$annualId || !$data) {
            return false;
        }

        $res = $this->table($this->annualCardTable)->where(['id' => $annualId])->save($data);

        return $res !== false;
    }

    /**
     * 根据年卡ID获取年卡数据
     *
     * @date   2018-03-07
     * <AUTHOR> Lan
     *
     * @param  int  $sid  供应商ID
     * @param  int  $fid  会员ID
     * @param  string  $field  查询字段
     *
     * @return array
     */
    public function getAnnualBySidFid($sid, $fid, $field = 'id, active_time, sale_time, virtual_no')
    {
        if (!$sid || !$fid) {
            return [];
        }

        $where = [
            'sid'      => $sid,
            'memberid' => $fid,
        ];

        $res = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->field($field)->find();

        return $res ?: [];
    }

    /**
     * 返回包含置顶特权的套餐
     * <AUTHOR>
     * @date   2018-05-18
     *
     * @param  array  $parenTidArr  年卡套餐tid数组
     * @param  integer  $tid  特权门票tid
     *
     * @return array
     * @deprecated
     * @see \Business\AnnualCard\Privilege::getPrivilegeInfoByParentTid()
     */
    public function getMealIncludeThePrivilege($parenTidArr, $tid)
    {

        if (!$parenTidArr || !$tid) {
            return [];
        }

        $where = [
            'parent_tid' => ['in', $parenTidArr],
            'tid'        => $tid,
        ];

        $parentArr = $this->table(self::CARD_PRIVILEGE_TABLE)->where($where)->getField('parent_tid', true);

        return $parentArr ?: [];
    }

    /**
     * 保存年卡照片
     * <AUTHOR>
     * @date   2018-07-02
     *
     * @param  string  $virtualNo  虚拟卡号
     * @param  array  $photosArr  照片集合
     *
     * @return mixed
     */
    public function saveGroupPhotos($virtualNo, array $photosArr)
    {
        if (!$virtualNo) {
            return false;
        }

        //是否存在
        $where = [
            'virtual_no' => $virtualNo,
        ];

        $id = $this->table(self::CARD_PHOTOS_TABLE)->where($where)->getField('id');

        if ($id) {
            $data = [
                'photos'      => json_encode($photosArr),
                'create_time' => time(),
            ];

            $where['id'] = $id;
            $res         = $this->table(self::CARD_PHOTOS_TABLE)->where($where)->save($data);
        } else {
            $data = [
                'virtual_no'  => $virtualNo,
                'photos'      => json_encode($photosArr),
                'create_time' => time(),
            ];
            $res  = $id = $this->table(self::CARD_PHOTOS_TABLE)->add($data);
        }

        return $res ? $id : false;
    }

    public function getGroupPhotosForMigration($virtualNos)
    {
        //是否存在
        $where  = [
            'virtual_no' => ['in', $virtualNos],
        ];
        $exists = $this->table(self::CARD_PHOTOS_TABLE)->where($where)->getField('virtual_no, 1', true);

        return $exists;
    }

    /**
     * 保存年卡照片
     * <AUTHOR>
     * @date   2018-07-02
     *
     * @param  string  $virtualNo  虚拟卡号
     * @param  array  $photosArr  照片集合
     *
     * @return mix
     */
    public function saveGroupPhotosForMigration($saveData)
    {
        $id = $this->table(self::CARD_PHOTOS_TABLE)->addAll($saveData);

        return $id;

    }

    /**
     * 获取一组照片信息
     * <AUTHOR>
     * @date   2018-07-03
     *
     * @param  string  $virtualNo  虚拟卡号
     *
     * @return array
     */
    public function getGroupPhotos($virtualNo, $fields = '*')
    {

        if (!$virtualNo) {
            return [];
        }

        $where['virtual_no'] = $virtualNo;

        $result = $this->table(self::CARD_PHOTOS_TABLE)->field($fields)->where($where)->find();

        return $result ?: [];
    }

    /**
     * 根据虚拟卡号获取头像、供应商id
     *
     * @author: guanpeng
     * @date: 2019/1/22
     *
     * @param  string|array  $virtualNo  虚拟卡号
     *
     * @return mixed
     */
    public function getGroupPhotosWithAid($virtualNo)
    {
        if (is_array($virtualNo)) {
            $where = ['a.virtual_no' => ['in', $virtualNo]];
        } else {
            $where = ['a.virtual_no' => $virtualNo . ''];
        }

        $data = $this->table(self::ANNUAL_CARD_TABLE . " as a")
                     ->join(self::CARD_PHOTOS_TABLE . " AS b ON a.virtual_no=b.virtual_no", 'LEFT')
                     ->field('a.virtual_no,a.sid,b.photos')
                     ->where($where)
                     ->select();

        return $data;
        //select concat(a.virtual_no,",") as virtual_no,concat(a.sid, ",") as sid,p.photos from pft_annual_card a left join pft_annual_card_photos p on a.virtual_no=p.virtual_no where a.virtual_no
    }

    /**
     * 根据虚拟卡号，批量获取年卡信息
     * <AUTHOR>
     * @date   2018-07-16
     *
     * @param  array  $virtualNoArr  虚拟卡号数组
     * @param  string  $field  字段
     * $param  bool      $isGetField   是否获取fetField格式的数据
     *
     * @return array
     */
    public function getCardsByVirtualNoArr($virtualNoArr, $field = 'virtual_no,card_no,physics_no,dname,mobile,id_card_no,pid,memberid', $isGetField = false)
    {

        if (!$virtualNoArr || !$field) {
            return [];
        }

        $where = [
            'virtual_no' => ['in', $virtualNoArr],
        ];

        if ($isGetField) {
            $list = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->field($field)->getField($field);
        } else {
            $list = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->field($field)->select();
        }

        return $list ?: [];
    }

    /**
     * 设置年卡有效期
     * <AUTHOR>
     * @date   2018-08-06
     *
     * @param  string  $virtualNo  虚拟卡号
     * @param  int  $begin  开始时间
     * @param  int  $end  结束时间
     * @param  string  $dname  办卡姓名
     * @param  string  $mobile  办卡手机号
     * @param  string  $idCardNo  办卡身份证号
     * @param  int  $province  省
     * @param  int  $city  市
     * @param  string  $address  地址
     *
     * @return bool
     */
    public function setAvalidDate($virtualNo, $begin, $end, $dname = '', $mobile = '', $idCardNo = '', $province = 0, $city = 0, $address = '', $sid = 0, $pid = 0)
    {
        if (!$virtualNo) {
            return false;
        }

        $where = [
            'virtual_no' => $virtualNo,
        ];

        $data = [
            'avalid_begin' => $begin,
            'avalid_end'   => $end,
            'update_time'  => time(),
        ];

        if ($dname) {
            $data['dname'] = $dname;
        }
        if ($mobile) {
            $data['mobile'] = $mobile;
        }
        if ($idCardNo) {
            $data['id_card_no'] = $idCardNo;
        }
        if ($province) {
            $data['province'] = $province;
        }
        if ($city) {
            $data['city'] = $city;
        }
        if ($address) {
            $data['address'] = $address;
        }
        if ($sid) {
            $data['sid'] = $sid;
        }
        if ($pid) {
            $data['pid'] = $pid;
        }

        $result = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->limit(1)->save($data);

        return boolval($result);
    }

    /**
     * 获取未固化有效期的年卡数量
     * <AUTHOR>
     * @date   2018-08-03
     * @return int
     */
    public function countNotFixedDate()
    {

        $where = [
            'avalid_begin' => 0,
            '_string'      => 'active_time <> 0 or sale_time <> 0',
        ];

        $count = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->count();

        return intval($count);
    }

    /**
     * 年卡有效期固化，不要调用
     * <AUTHOR>
     * @date   2018-08-06
     *
     * @param  [type]     $page  [description]
     * @param  [type]     $size  [description]
     * @param  string  $field  [description]
     *
     * @return [type]            [description]
     */
    public function getNotFixedDateList($page, $size, $field = 'id,pid,active_time,sale_time')
    {

        if (!$page || !$size) {
            return [];
        }

        $where = [
            'avalid_begin' => 0,
            '_string'      => 'active_time <> 0 or sale_time <> 0',
        ];

        $limit = ($page - 1) * $size . ',' . $size;

        $list = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->limit($limit)->field($field)->select();

        return $list ?: [];
    }

    /**
     *  年卡有效期固化，不要调用
     * <AUTHOR>
     * @date   2018-08-06
     *
     * @param  [type]     $data [description]
     *
     * @return [type]           [description]
     */
    public function fixedDateMulti($data)
    {

        if (!$data) {
            return false;
        }

        $sql = 'update pft_annual_card set avalid_begin = case id ';
        foreach ($data as $id => $item) {
            $sql .= " when {$id} then {$item['begin']} ";
        }

        $sql .= 'end,avalid_end = case id ';

        foreach ($data as $id => $item) {
            $sql .= " when {$id} then {$item['end']} ";
        }

        $idArr = array_keys($data);

        $sql .= ' end where id in (' . implode(',', $idArr) . ')';

        $result = $this->execute($sql);

        return boolval($result);
    }

    /**
     * 获取年卡产品的总数（招行）
     *
     * @param  int  $sid  供应商id
     * @param  mix  $pid  门票pid
     * @param  strring  $type  虚拟卡/物理卡
     *
     * @return aray
     */
    public function getAnnualCardTotal($sid, $pid, $type = 'virtual')
    {

        if (!$sid || !$pid) {
            return 0;
        }
        $where           = [];
        $where['sid']    = $sid;
        $where['status'] = ['in', [0, 1, 2, 3]];

        if ($type == 'virtual') {
            $where['card_no'] = '';
        } else {
            $where['card_no'] = ['neq', ''];
        }

        if (is_array($pid)) {
            $where['pid'] = ['in', $pid];

            return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->group('pid')->getField('pid,count(id)', true);
        } else {
            $where['pid'] = $pid;

            return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->count();
        }
    }

    /**
     * 添加年卡产品交易记录
     * <AUTHOR> Li
     * @date   2018-11-06
     *
     * @param  int  $fid  会员ID
     * @param  int  $tid  票ID
     * @param  int  $sid  供应商ID
     * @param  int  $money  交易金额
     * @param  string  $orderNo  订单号
     * @param  int  $type  交易类型 0购卡 1续费 2补卡 3订单被取消
     * @param  int  $operaterId  操作员ID
     * @param  int  $ptype  支付方式
     * @param  string  $remark  备注
     * @param  string  $tradeNo  流水号
     * @param  int  $sourceT  来源 0平台 10云票务
     * @param  int  $card_id  操作卡id
     * @param  int  $num  操作卡数量
     * @param  string  $userName  用户名称
     * @param  string  $userMobile  用户手机号
     * @param  string  $userIdCard  用户身份证号
     * @param  string  $virtualNo  虚拟卡号
     * @param  string  $cardNo  实体卡号
     * @param  string  $physicsNo  物理卡号
     * @param  string  $pName  卡套餐
     * @param  int  $wristId  新一卡通腕带id
     * @param  int  $pid  票id
     *
     * @return bool
     */
    public function addAnnualCardTrade(
        $fid = 0,
        $tid,
        $sid = 0,
        $money,
        $orderNo,
        $type,
        $operaterId,
        $ptype,
        $remark = '',
        $sourceT = 0,
        $card_id = 0,
        $num = 1,
        $userName = '',
        $userMobile = '',
        $userIdCard = '',
        $virtualNo = '',
        $cardNo = '',
        $physicsNo = '',
        $pName = '',
        $pid = 0,
        $wristId = 0
    )
    {
        if (!$tid) {
            return false;
        }

        $data = [
            'card_id'      => $card_id,
            'member_id'    => $fid,
            'sid'          => $sid,
            'type'         => $type,
            'payway'       => $ptype,
            'source'       => $sourceT,
            'pay_money'    => $money,
            'num'          => $num,
            'ordernum'     => $orderNo,
            'ticket_id'    => $tid,
            'add_time'     => time(),
            'operator_id'  => $operaterId,
            'remark'       => $remark,
            'user_name'    => $userName,
            'user_mobile'  => $userMobile,
            'user_id_card' => !empty($userIdCard) ? $userIdCard : '',
            'virtual_no'   => $virtualNo,
            'card_no'      => $cardNo,
            'physics_no'   => $physicsNo,
            'p_name'       => $pName,
            'pid'          => $pid,
            'update_time'  => time(),
            'wrist_id'     => $wristId,
        ];

        $result = $this->table(self::CARD_TRADE_TABLE)->add($data);

        return $result !== false ? true : false;

    }

    /**
     * 获取年卡产品交易记录及汇总
     * <AUTHOR> Li
     * @date   2018-11-06
     *
     * @param  data  $startTime  交易开始时间
     * @param  data  $endTime  交易结束时间
     * @param  mixed  $tids  票id -1全部
     * @param  mixed|int  $type  交易类型 -1全部 0购卡 1续费 2补卡 3订单被取消 4激活 5验证（特权支付）
     * @param  array  $userId  登陆者ID数组
     * @param  int  $sid  供应商ID
     * @param  int  $source  来源 0平台 10云票务
     * @param  int  $field  汇总字段
     *
     * @return array
     */
    public function getAnnualCardTrade(
        $startTime,
        $endTime,
        $tids,
        $type,
        $userIdArr,
        $sid,
        $source = 10,
        $field = 'type,payway,pay_money,ordernum,ticket_id,add_time,remark,num',
        $group = false,
        $page = '',
        $pageSize = '',
        $count = false,
        $explod = false
    )
    {

        if (empty($startTime) || empty($endTime)) {
            return false;
        }

        $where = [
            'add_time' => ['between', [$startTime, $endTime]],
        ];

        if ($source && $source >= 0) {
            $where['source'] = $source;
        }

        if ($source != 10) {
            $userIdArr && $where['sid'] = ['in', $userIdArr];
        } else {
            if ($userIdArr) {
                $where['operator_id'] = ['in', $userIdArr];
            }
            if (!empty($sid)) {
                $where['sid'] = $sid;
            }
        }

        if (is_array($tids)) {
            $where['ticket_id'] = ['in', $tids];
        } elseif ($tids != '-1') {
            $where['ticket_id'] = $tids;
        }

        if (is_array($type)) {
            $where['type'] = ['in', $type];
        } elseif ($type != '-1') {
            $where['type'] = $type;
        } else {
            //过滤验证数据
            $where['type'] = ['neq', 5];
        }

        if ($explod) {
            $where['type'] = ['neq', 3];
        }

        if ($count) {
            $res = $this->table(self::CARD_TRADE_TABLE)->where($where)->count();
        } else {
            //分页处理
            if (!empty($page) && !empty($pageSize)) {
                $pages = ($page - 1) * $pageSize . ',' . $pageSize;
                $res   = $this->table(self::CARD_TRADE_TABLE)->where($where)->group($group)->field($field)->limit($pages)->select();
            } else {
                $res = $this->table(self::CARD_TRADE_TABLE)->where($where)->group($group)->field($field)->select();
            }
        }

        return $res !== false ? $res : false;

    }

    /**
     * 将年卡产品交易记录类型修改为3
     * <AUTHOR> Li
     * @date   2018-11-06
     *
     * @param  int  $sid  供应商ID
     * @param  string  $orderNo  订单号
     *
     * @return array
     */
    public function changeAnnualCardTradeType($orderNo, $sid)
    {
        if (!$orderNo || !$sid) {
            return false;
        }

        $where = [
            'ordernum' => (string)$orderNo,
            'sid'      => $sid,
        ];

        $data = [
            'type'        => 3,
            'update_time' => time(),
        ];

        $check = $this->table(self::CARD_TRADE_TABLE)->where($where)->find();
        if ($check) {
            $result = $this->table(self::CARD_TRADE_TABLE)->where($where)->limit(1)->save($data);
        } else {
            sleep(1);
            $result = $this->table(self::CARD_TRADE_TABLE)->where($where)->limit(1)->save($data);
        }

        return $result !== false ? $result : false;

    }

    /**
     * 获取到年卡记录表的一条数据
     *
     * @param $virtualNo  string 虚拟卡号
     * @param $type   int  类型
     * @param $filed
     *
     * @return array
     * <AUTHOR>
     * @date   2019-2-26
     */
    public function getAnnualTradeRecode($virtualNo, $type, $filed)
    {
        if (!$virtualNo || $type === false) {
            return [];
        }
        $where = [
            'virtual_no' => $virtualNo,
            'type'       => $type,
        ];
        $res   = $this->table(self::CARD_TRADE_TABLE)->where($where)->field($filed)->find();

        return $res ?: [];

    }

    /**
     * 记录异步下单失败的记录
     * author  leafzl
     * Date: 2018-12-11
     * @return bool|mixed|string
     */
    public function addAsyncOrderErr($data)
    {
        $res = $this->table(self::CARD_ASYNC_ORDER_TABLE)->add($data);

        return $res ?: false;
    }

    /**
     * 获取在低于重试阈值的订单
     * author  leafzl
     * Date: 2018-12-11
     *
     * @param $limit int 阈值
     * @param $sid int 供应商ID
     * @param $cardOrderId int 卡的对应的id
     * @param $startTime int 时间戳
     *
     * @return array
     */
    public function getRetryOrder($limit, $sid = 0, $cardOrderId = 0, $startTime = 0)
    {
        if (!$limit) {
            return [];
        }
        $where = [
            'create_time' => ['egt', $startTime],
            'status'      => 0,
            'number'      => ['lt', $limit],
            'card_mid'    => ['GT', 0],
            'options'     => ['neq', ''],
        ];

        if ($sid) {
            $where['sid'] = $sid;
        }

        if ($cardOrderId) {
            $where['card_order_id'] = $cardOrderId;
        }
        $field = 'id,consume_map,card_mid,sid,terminal,operId,card_order_id,number,err_msg,options';

        $res = $this->table(self::CARD_ASYNC_ORDER_TABLE)->where($where)->field($field)->select();

        return $res ?: [];

    }

    /**
     * 保存错误信息
     * author  leafzl
     * Date:2018-12-11
     *
     * @param $id
     * @param $num
     * @param $errMsg
     *
     * @return bool
     */
    public function saveErrmsg($id, $num, $errMsg)
    {
        if (!$id || !$num) {
            return false;
        }
        $where = [
            'id' => $id,
        ];
        $data  = [
            'number'  => $num,
            'err_msg' => $errMsg,
        ];

        $res = $this->table(self::CARD_ASYNC_ORDER_TABLE)->where($where)->save($data);

        return $res ?: false;
    }

    public function saveStatus($id)
    {
        if (!$id) {
            return false;
        }
        $where = [
            'id' => $id,
        ];
        $data  = [
            'status' => 1,
        ];

        $res = $this->table(self::CARD_ASYNC_ORDER_TABLE)->where($where)->save($data);

        return $res ?: false;
    }

    /**
     * 获取用户的年卡（招行）
     *
     * @param  int  $sid  供应商id
     * @param  int  $memberId  用户id
     *
     * @return [type]           [description]
     */
    public function getCmbAnnualCard($sid, $memberId)
    {
        if (!$sid || !$memberId) {
            return false;
        }
        $where  = [
            'memberid' => $memberId,
            'sid'      => $sid,
            'status'   => ['in', [0, 1]],
        ];
        $result = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->find();

        return $result ? $result : [];

    }

    /**
     * 更新年卡有效期时间
     * <AUTHOR>
     * @date   2017-11-24
     *
     * @param  string  $virtualNo  虚拟卡号
     * @param  string  $field  时间字段
     * @param  int  $val  更新后的时间
     *
     * @return bool
     */
    public function updateBeginTimeToCmb($virtualNo, $field, $val)
    {

        if (!$virtualNo || !$field || !$val) {
            return false;
        }

        if (!in_array($field, ['avalid_begin', 'avalid_end'])) {
            return false;
        }

        $where = [
            'virtual_no' => $virtualNo,
        ];

        $data                = [
            $field => $val,
        ];
        $data['update_time'] = time();

        $result = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->limit(1)->save($data);

        return boolval($result);
    }

    /**
     * 数据迁移-年卡基础信息
     *
     * @return
     */
    public function migrationAnnaulCardInfo($virtualNoList)
    {
        $where = [
            'memberid'   => ['neq', 0],
            'virtual_no' => ['in', $virtualNoList],
        ];

        $result = $this->table(self::ANNUAL_CARD_TABLE)
                       ->where($where)
                       ->getField('virtual_no, sid');

        return $result ?: [];
    }

    /**
     * 数据迁移-年卡照片信息
     * <AUTHOR> Li
     * @date   2019-01-14
     *
     * @return array
     */
    public function migrationAnnaulCardPhotos($count = false, $startId = 4980, $lastId = 18544, $page = 1, $limit = 500)
    {
        if ($count) {
            return $this->table(self::CARD_PHOTOS_TABLE)->where(['id' => ['between', [$startId, $lastId]]])->count();
        }

        $result = $this->table(self::CARD_PHOTOS_TABLE)
                       ->page($page, $limit)
                       ->where(['id' => ['between', [$startId, $lastId]]])
                       ->getField('virtual_no, photos');

        return $result ?: [];
    }

    /**
     * 数据迁移-获取出非指定虚拟卡的年卡信息
     * <AUTHOR> Li
     * @date   2019-01-14
     *
     * @return array
     */
    public function getAnnaulCardInfoByVirtualNoArr($startId = 0, $lastId = 329793, $count = false, $page = 1, $limit = 500)
    {
        $where = [
            'id' => ['between', [$startId, $lastId]],

            'memberid' => ['neq', 0],
            //'virtual_no' => ['not in', $virtualNoArr]
        ];

        if ($count) {
            return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->count();
        }

        $result = $this->table(self::ANNUAL_CARD_TABLE)
                       ->where($where)
                       ->page($page, $limit)
                       ->getField('memberid, virtual_no, sid');

        return $result ?: [];
    }

    /**
     * 获取指定会员账号下所有可用的年卡
     *
     * @param  int  $sid  供应商id
     * @param  array  $memberid  会员id
     *
     * @return string $virtual_no 虚拟卡号
     *
     * @return array
     */
    public function getActivedCardList($sid, $memberid)
    {

        $where = [
            'sid'      => $sid,
            'memberid' => $memberid,
            'status'   => 1,
        ];

        return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->field('virtual_no,pid')->select();
    }

    /**
     * 修改年卡对应订单的状态
     * <AUTHOR> Li
     * @date   2019-01-19
     *
     * @param $ordernum
     *
     * @return bool
     */
    public function changeMappingStatusByOrdernum($ordernum)
    {
        if (!$ordernum) {
            return false;
        }

        $result = $this->table(self::CARD_MAPPING_TABLE)->where(['ordernum' => (string)$ordernum])->save(['status' => 0]);

        return boolval($result);

    }

    /**
     * 修改年卡对应订单的状态 , 同changeMappingStatusByOrdernum
     * <AUTHOR>
     * @date   2019-01-19
     *
     * @param $orderNumArr
     * @param  int  $status
     *
     * @return bool
     */
    public function changeMappingStatusByOrderNumArr($orderNumArr, $status)
    {
        if (empty($orderNumArr)) {
            return false;
        }

        $where = [
            'ordernum' => ['in', $orderNumArr],
        ];

        $data = [
            'status' => $status,
        ];

        $res = $this->table(self::CARD_MAPPING_TABLE)->where($where)->save($data);

        return $res;
    }

    /**
     * 根据订单号获取该笔订单是否需要取消
     *
     * @param  string  $orderum  订单号
     *
     * @return status
     */
    public function getStatusByOrdernum($ordernum)
    {
        if (!$ordernum) {
            return false;
        }

        $where = [
            'ordernum' => (string)$ordernum,
        ];

        return $this->table(self::CARD_MAPPING_TABLE)
                    ->where($where)
                    ->getField('ordernum,status,virtual_no');
    }

    /**
     * 更新年卡分销商id
     *
     * <AUTHOR> Li
     * @date   2019-01-30
     *
     * @param  int  $card_id  卡id
     * @param  string  $virtual_no  虚拟卡号
     * @param  int  $distributor_id  分销商id
     *
     * @return bool
     */
    public function saveDistributorIdByCardId($card_id, $virtual_no, $distributor_id)
    {
        if (!$card_id || !$virtual_no || !$distributor_id) {
            return false;
        }
        $res = $this->table(self::ANNUAL_CARD_TABLE)
                    ->where(['id' => $card_id, 'virtual_no' => $virtual_no])
                    ->save(['distributor_id' => $distributor_id]);

        return boolval($res);
    }

    /**
     * 释放实体卡
     * <AUTHOR> Li
     * @date   2019-02-01
     *
     *
     * @param  int  $sid  供应商id
     * @param  int  $cardFid  用户id
     * @param  string  $cardNo  需要清除的实体卡号
     * @param  string  $physicsNo  物理卡号
     *
     * @return bool
     */
    public function releaseEntityCard($sid, $cardFid, $cardNo, $cardId)
    {

        if (!$sid || !$cardNo || !$cardId) {
            return false;
        }

        $where = [
            'sid'     => $sid,
            'card_no' => $cardNo,
            'id'      => $cardId,
        ];
        if ($cardFid) {
            $where['memberid'] = $cardFid;
        }

        $data = [
            'physics_no' => 0,
            'card_no'    => '',
        ];

        $result = $this->table($this->annualCardTable)->where($where)->limit(1)->save($data);

        return boolval($result);
    }

//    /**
//     * 从仓库随机获取一张虚拟卡
//     * <AUTHOR>
//     * @date   2019-02-20
//     *
//     * @param  int  $pid  套餐pid
//     * @param  array  $notInVirtualArr  过滤的虚拟卡号
//     *
//     * @return array
//     */
//    public function getRandVirtualCardFromUnsale($pid, $field = '*', $notInVirtualArr = [])
//    {
//        if (!$pid) {
//            return [];
//        }
//
//        $table = self::ANNUAL_CARD_TABLE;
//        $where = "pid=$pid AND status=3 AND card_no='' AND lock_type=0";
//        if (!empty($notInVirtualArr)) {
//            $strVirtual = '';
//            foreach ($notInVirtualArr as $key => $value) {
//                $strVirtual .= "'" . $value . "',";
//            }
//            $strVirtual = rtrim($strVirtual, ',');
//            $where      .= " and virtual_no not in ({$strVirtual})";
//        }
//        //出现了并发的情况，这边随机还是保留吧
//        $sql    = <<<SQL
//SELECT $field
//FROM `$table` AS t1 JOIN (SELECT ROUND(RAND() * ((SELECT MAX(id) FROM `$table` where $where)-(SELECT MIN(id) FROM `$table` where $where))+(SELECT MIN(id) FROM `$table` where $where)) AS id) AS t2
//WHERE  $where And t1.id >= t2.id
//ORDER BY t1.id LIMIT 1;
//SQL;
//        $result = $this->query($sql);
//
//        return $result ? $result[0] : [];
//    }

    /**
     * 从仓库随机获取一张虚拟卡
     * <AUTHOR>
     * @date   2019-02-20
     *
     * @modifed  dwer.cn 2021-07-30
     * 原来的逻辑：最小的ID + 可用的ID段里面随机获取一个增量 （可用的ID段 通过数据库里面的 MAX 和 MIN 去查询获取）
     *
     * 调整后的逻辑：程序随机生成一个100以内的数字randNum，查询出randNum条只有ID的数据，获取最后一条数据的ID, 然后通这个ID获取数据
     *
     *
     * @param  int  $pid  套餐pid
     * @param  array  $notInVirtualArr  过滤的虚拟卡号
     *
     * @return array
     */
    public function getRandVirtualCardFromUnsale($pid, $field = '*', $notInVirtualArr = [])
    {
        if (!$pid) {
            return [];
        }

        //获取一个 2-100内的随机数
        $randMax = 100;
        $randNum = rand(1, $randMax);
        $table   = self::ANNUAL_CARD_TABLE;

        $whereArr = [
            'pid'       => $pid,
            'status'    => AnnualCardConst::STATUS_IN_STOCK,
            'card_no'   => '',
            'lock_type' => 0,
        ];

        if (!empty($notInVirtualArr)) {
            //转成字符串
            $queryParamArr = [];
            foreach ($notInVirtualArr as $item) {
                $queryParamArr[] = strval($item);
            }

            $whereArr['virtual_no'] = ['not in', $queryParamArr];
        }

        $queryRes = $this->table($table)->where($whereArr)->page("0,{$randNum}")->order('id asc')->field('id')->select();
        if (!$queryRes) {
            return [];
        }

        $queryCount = count($queryRes);
        if ($queryCount < $randNum) {
            //如果是剩余的数量很少，就再随机获取一条，要不然都是获取到最后一条数据
            $secondRandNum = rand(1, $queryCount);
            $lastNode      = $queryRes[$secondRandNum - 1];
            $getId         = $lastNode['id'];
        } else {
            //如果剩余的数量比较大，取最后一条数据
            $lastNode = end($queryRes);
            $getId    = $lastNode['id'];
        }

        $info = $this->table($table)->where(['id' => $getId])->field($field)->find();

        return $info ?: [];
    }

    /**
     * 添加年卡过期微信推送配置
     * <AUTHOR>
     * @date   2019-03-29
     *
     * @param  int  $sid  用户id
     * @param  int  $openConfig  0不开启 1 开启是否开启推送
     * @param  int  $validityTime  过期时间
     * @param  int  $spaceDay  发送间隔天
     * @param  int  $noticeNum  通知次数
     *
     * @return int
     */
    public function addWxAnnualConfig($sid, $openConfig, $validityTime, $spaceDay, $noticeNum)
    {
        if (!$sid) {
            return false;
        }
        if ($openConfig) {
            $data = [
                'is_open'    => 1,
                'member_id'  => $sid,
                'validity'   => $validityTime,
                'notice_num' => $noticeNum,
                'space_day'  => $spaceDay,
                'begin_time' => time(),
            ];
        } else {
            $data = [
                'is_open'    => 0,
                'begin_time' => time(),
                'member_id'  => $sid,
            ];
        }

        $result = $this->table(self::WX_ANNUAL_CONFIG_TABLE)->add($data);

        return $result;
    }

    /**
     * 获取年卡过期微信推送配置
     * <AUTHOR>
     * @date   2019-03-29
     *
     * @param  int  $mid  用户id
     *
     * @return int
     */
    public function getWxAnnualConfig($mid)
    {
        if (!$mid) {
            return false;
        }
        $where  = [
            'member_id' => $mid,
        ];
        $result = $this->table(self::WX_ANNUAL_CONFIG_TABLE)->where($where)->find();

        return $result;
    }

    /**
     * 更新年卡过期微信推送配置
     * <AUTHOR>
     * @date   2019-03-29
     *
     * @param  int  $id  就是id
     * @param  int  $openConfig  0不开启 1 开启是否开启推送
     * @param  int  $validityTime  过期时间
     * @param  int  $spaceDay  发送间隔天
     * @param  int  $noticeNum  通知次数
     *
     * @return int
     */
    public function updateWxAnnualConfig($id, $openConfig, $validityTime, $spaceDay, $noticeNum)
    {
        if (!$id) {
            return false;
        }
        $where = [
            'id' => $id,
        ];
        if ($openConfig) {
            $data = [
                'is_open'    => 1,
                'validity'   => $validityTime,
                'notice_num' => $noticeNum,
                'space_day'  => $spaceDay,
                'begin_time' => time(),
            ];
        } else {
            $data = [
                'is_open'    => 0,
                'begin_time' => time(),
            ];
        }
        $result = $this->table(self::WX_ANNUAL_CONFIG_TABLE)->where($where)->save($data);

        return $result;
    }

    /**
     * 根据供应商id获取年卡将要过期的人和发送次数小于指定的
     * <AUTHOR>
     * @date   2019-03-29
     *
     * @param  int  $sid  供应商id
     * @param  int  $avalidTime  过期时间
     * @param  int  $num  过期时间
     * @param  int  $page  页数
     * @param  int  $size  数量
     *
     * @return array
     */
    public function getWillExpireAnnualBySid($sid, $avalidTime, $num, $page = 1, $size = 500)
    {
        if (!$sid || !$avalidTime) {
            return [];
        }
        $where = [
            'sid'        => $sid,
            'status'     => 1,
            'avalid_end' => ['between', [time(), $avalidTime]],
            'send_num'   => ['lt', $num],
        ];

        $field  = 'memberid,id,virtual_no,avalid_end,pid';
        $result = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->page($page,
            $size)->field($field)->select();

        return $result;
    }

    /**
     * 获取年卡过期微信推送配置列表
     * <AUTHOR>
     * @date   2019-03-29
     * @return array
     */
    public function getWxAnnualConfigList()
    {
        $where  = [
            'is_open' => 1,
        ];
        $result = $this->table(self::WX_ANNUAL_CONFIG_TABLE)->where($where)->select();

        return $result ? $result : [];
    }

    /**
     * 获取年卡过期微信推送配置列表
     * <AUTHOR>
     *
     * @param  array  $arrIds  年卡id数组
     *
     * @date   2019-03-29
     * @return bool
     */
    public function updateAnnualSendNum($arrIds)
    {
        if (!is_array($arrIds)) {
            return false;
        }
        $where  = [
            'id' => ['in', $arrIds],
        ];
        $result = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->setInc('send_num', 1);

        return $result;
    }

    /**
     * 清空年卡的推送wx次数
     * <AUTHOR>
     * @date   2019-04-01
     *
     * @param  string  $virtualNo  虚拟卡号
     *
     * @return bool
     */
    public function setAnnualSendNum($virtualNo)
    {
        if (!$virtualNo) {
            return false;
        }

        $where = [
            'virtual_no' => $virtualNo,
        ];

        $data = [
            'send_num' => 0,
        ];

        $result = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->limit(1)->save($data);

        return boolval($result);
    }

    /**
     * 获取供应商下的多用户年卡去重
     * <AUTHOR>
     * @date   2019-04-11
     *
     * @param  array  $arrMid  用户数组
     * @param  string  $aid  供应商id
     *
     * @return array
     */
    public function getAnnualByAidGroupByMid($arrMid, $aid, $field = '*')
    {
        if (!is_array($arrMid) || !$aid) {
            return [];
        }
        $where  = [
            'memberid' => ['in', $arrMid],
            'sid'      => $aid,
            'status'   => 1,
        ];
        $result = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->order('id desc')->limit(5)->field($field)->select();

        return $result ? $result : [];
    }

    /**
     * 获取年卡交易记录
     * <AUTHOR>
     * @date   2019-04-30
     *
     * @param  $orderNum    array/string      订单号
     * @param  $field       string      查询字段
     * @param  $tradeType   integer      交易类型
     *
     * @return array
     */
    public function getTradeRecordByOrderNum($orderNum, $field = '', $tradeType = self::TRADE_CHECKED_CARD)
    {
        if (!$orderNum) {
            return [];
        }

        $queryField = $field ? $field : "id";

        $where = [
            'type' => (int)$tradeType,
        ];

        if (is_array($orderNum)) {
            $where['ordernum'] = ['in', $orderNum];
            $record            = $this->table(self::CARD_TRADE_TABLE)->field($queryField)->where($where)->select();
        } else {
            $where['ordernum'] = $orderNum;
            $record            = $this->table(self::CARD_TRADE_TABLE)->field($queryField)->where($where)->find();
        }

        return is_array($record) ? $record : [];
    }

    /**
     * 获取年卡交易记录
     * <AUTHOR>
     * @date   2019-04-30
     *
     * @param  string  $virtualNo  虚拟卡号
     * @param  array  $tradeTypeArr  交易类型
     * @param  string  $orderBy  倒叙字段
     * @param  string  $field  查询字段
     *
     * @return array
     */
    public function getTradeRecordByCardId($virtualNo, $tradeTypeArr = [], $orderBy = false, $field = 'ordernum')
    {
        if (!$virtualNo) {
            return [];
        }

        $where = [
            'virtual_no' => $virtualNo,
            'type'       => ['in', $tradeTypeArr],
        ];

        $record = $this->table(self::CARD_TRADE_TABLE)->field($field)->where($where)->order($orderBy)->find();

        return is_array($record) ? $record : [];
    }

    /**
     * 通过订单号获取到虚拟卡号
     * <AUTHOR> Li
     * @date   2019-05-20
     *
     * @param  $orderNumArr    array       订单号数组
     *
     * @return array
     */
    public function getVirtualNoByOrderNumArr($orderNumArr)
    {
        if (!$orderNumArr) {
            return [];
        }

        $result = $this->table(self::CARD_TRADE_TABLE)->where([
            'ordernum' => [
                'in',
                $orderNumArr,
            ],
        ])->getField('ordernum, card_id, p_name,virtual_no,card_id,num,pay_money,user_name,user_mobile,user_id_card,card_no,physics_no,operator_id,source,add_time,type,sid,member_id,payway');

        return $result ?: [];
    }

    /**
     * 通过订单号获取到虚拟卡号, 同上一个方法，但新增状态条件
     * <AUTHOR>
     * @date   2019-08-02
     *
     * @param  $orderNumArr    array       订单号数组
     * @param  int  $status
     * @param  string  $field  获取字段
     *
     * @return array
     */
    public function getVirtualNoByOrderNumFromMapping($orderNumArr, $status, $field = 'id')
    {
        if (empty($orderNumArr) && !in_array($status, [0, 1])) {
            return [];
        }

        $orderNumArr = array_map(function ($item) {
            return strval($item);
        }, $orderNumArr);

        $where = [];
        if ($orderNumArr) {
            $where['ordernum'] = ['in', $orderNumArr];
        }
        if ($status === 0 || $status === 1) {
            $where['status'] = $status;
        }

        $res = $this->table(self::CARD_MAPPING_TABLE)->where($where)->field($field)->select();

        return is_array($res) ? $res : [];
    }

    /**
     * 获取门票特权的上级供应商id(aid)
     * <AUTHOR>
     * @date   2019-04-29
     *
     * @param  int  $parentTid  年卡套餐tid
     * @param  int  $tid  特权门票tid
     *
     * @return array
     * @deprecated
     * @see \Business\AnnualCard\Privilege::getPrivilegeInfoByParentTidPage()
     */
    public function getPrivilegeAid(int $parentTid, int $tid): int
    {
        if (!$parentTid || !$tid) {
            return 0;
        }

        $where = [
            'parent_tid' => $parentTid,
            'tid'        => $tid,
        ];

        $aid = $this->table(self::CARD_PRIVILEGE_TABLE)->where($where)->getField('aid');

        return intval($aid);
    }

    /**
     * 编辑年卡绑定用户的基本信息(pft_annual_card)
     *
     * <AUTHOR> Li
     * @date   2019-05-13
     *
     * @param  int  $cardId  年卡ID
     * @param  object  $paramsObj  参数对象
     *
     * @return bool
     */
    public function editBindCardInfo($cardId, $options = [])
    {
        if (!$cardId || !$options) {
            return false;
        }

        $result = $this->table($this->annualCardTable)->where(['id' => $cardId])->data($options)->save();

        return $result === false ? false : true;
    }

    /**
     * 获取所有已绑定用户的年卡数量
     * <AUTHOR> Li
     * @date   2019-05-15
     * @return int|array
     */
    public function countAnnualCardNum($count = false, $page = 1, $size = 1000, $field = 'memberid, id')
    {
        $where = [
            'memberid' => ['GT', 0],
        ];

        if ($count) {
            $count = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->count();

            return intval($count);
        }

        $limit = ($page - 1) * $size . ',' . $size;

        $list = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->limit($limit)->getField($field);

        return $list ? $list : [];
    }

    /**
     * 批量更新年卡表中  用户姓名、手机号、身份证等信息
     *
     * <AUTHOR> Li
     * @date   2019-05-15
     * @return bool
     */
    public function updateAnnualCardInfo($data)
    {
        $ids = implode(',', array_keys($data));
        $sql = "UPDATE " . self::ANNUAL_CARD_TABLE . " SET dname = CASE memberid ";
        foreach ($data as $key => $value) {
            $sql .= sprintf("WHEN %d THEN '%s' ", $key, $value['dname']);
        }
        $sql .= " END,mobile = CASE memberid ";
        foreach ($data as $key => $value) {
            $sql .= sprintf("WHEN %d THEN '%s' ", $key, $value['mobile']);
        }
        $sql .= " END,id_card_no = CASE memberid ";
        foreach ($data as $key => $value) {
            $sql .= sprintf("WHEN %d THEN '%s' ", $key, $value['id_card_no']);
        }
        $sql .= " END,province = CASE memberid ";
        foreach ($data as $key => $value) {
            $sql .= sprintf("WHEN %d THEN '%s' ", $key, $value['province']);
        }
        $sql .= " END,city = CASE memberid ";
        foreach ($data as $key => $value) {
            $sql .= sprintf("WHEN %d THEN %d ", $key, $value['city']);
        }
        $sql .= " END,address = CASE memberid ";
        foreach ($data as $key => $value) {
            $sql .= sprintf("WHEN %d THEN %d ", $key, $value['address']);
        }
        $sql    .= "END WHERE memberid IN ($ids)";
        $result = $this->execute($sql);

        return $result;
    }

    public function batchGetInfoByCardNo(int $sid, array $cardNoArr, $field = 'card_no')
    {
        if (empty($cardNoArr)) {
            return [];
        }

        return $this->table(self::ANNUAL_CARD_TABLE)
                    ->where(['sid' => $sid, 'card_no' => ['in', $cardNoArr]])
                    ->field($field)
                    ->select();
    }

    /**
     * 根据续费次数为按次数的累加
     * <AUTHOR>
     * @date   2019-04-11
     *
     * @param  array  $arrMid  用户数组
     * @param  string  $aid  供应商id
     *
     * @return array
     */
    private function _formatUsedNum($data, $num)
    {
        foreach ($data as $key => $value) {
            if (in_array($value['use_limit'], ['-1', '-1,-1,-1'])) {
                continue;
            }
            $limit = explode(',', $value['use_limit']);
            if (isset($limit[2]) && $limit[2] && $limit[2] != '-1') {
                $limit[2] = $limit[2] * ($num + 1);
            }
            $data[$key]['use_limit'] = implode(',', $limit);
        }

        return $data;
    }

    /**
     * 根据物理卡号获取年卡
     * <AUTHOR> Li
     * @date   2019-07-09
     *
     * @param  array  $physicsArr  物理卡号数组(十六进制)
     * @param  string  $field  要获取的字段
     *
     * @return array
     */
    public function getCardsByPhysicsArr($physicsArr, $field = 'sid,physics_no,pid,virtual_no,card_no')
    {
        if (!$physicsArr || !is_array($physicsArr)) {
            return [];
        }

        $where = [
            'physics_no' => ['in', $physicsArr],
            'status'     => 1,
        ];

        $list = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->field($field)->select();

        return $list ?: [];
    }

    /**
     * 获取年卡检票会员列表（关联 pft_annual_card_privilege 表）
     *
     * @param  int  $sid  供应商id
     * @param  int  $tid  特权产品id
     * @param  string  $identify  搜索条件
     * @param  int  $page  当前页数
     * @param  int  $pageSize  每页条数
     * @param  string  $action  查询类型  select count
     *
     * @return [type]          [description]
     */
    public function getMemberCheckCardList($sid, $tid, $pidArr = [], $idArr = [], $identify = '', $page = 1, $pageSize = 1000)
    {
        $return = ['list' => [], 'total' => 0];
        if (!$sid || !$tid || (!$pidArr && !$idArr)) {
            return $return;
        }
        $where = [
            'status' => ['neq', 3],
        ];

        if ($pidArr) {
            $where['pid'] = ['in', $pidArr];
        } else {
            $where['id'] = ['in', $idArr];
        }

        if ($sid) {
            $where['sid'] = $sid;
        }

        //卡号手机号等搜索操作
        if ($identify) {
            if (\ismobile($identify)) {
                $where['mobile'] = $identify;
            } elseif (Tools::idcard_checksum18($identify)) {
                $where['id_card_no'] = $identify;
            } else {
                if (dechex($identify)) {
                    $where['_string'] = "card_no='{$identify}' or virtual_no='{$identify}' or physics_no='" . dechex($identify) . "'";
                } else {
                    $where['_string'] = "card_no='{$identify}' or virtual_no='{$identify}'";
                }
            }
        }

        $field = 'id,sid,virtual_no,card_no,sale_time,memberid,activate_source,active_time,pid,status,avalid_begin,avalid_end,physics_no,distributor_id,dname,mobile,id_card_no,province,city,address,ext_info';

        $count = $this->table(self::ANNUAL_CARD_TABLE)
                      ->where($where)
                      ->count();

        $list = [];
        if ((int)$count > 0) {
            $list = $this->table(self::ANNUAL_CARD_TABLE)
                         ->where($where)->field($field)
                         ->page($page, $pageSize)
                         ->order('sale_time desc')
                         ->select();
        }

        return ['list' => $list, 'total' => (int)$count];

    }

    /**
     * 获取年卡补卡重复订单号数据 (异常情况下调用)
     * <AUTHOR> Li
     * @date   2019-11-11
     *
     * @return array
     */
    public function getAnnualTradeExceptionData($type = 2, $filed = 'id,card_id,member_id,sid,ordernum,add_time,operator_id,payway,pay_money')
    {
        $where = [
            'type' => $type,
        ];

        $res           = [];
        $tempOrderInfo = $this->table(self::CARD_TRADE_TABLE)->where($where)->field($filed)->group('ordernum')->having('count(ordernum) > 1')->select();
        if (!empty($tempOrderInfo)) {
            $tempOrderNum      = array_column($tempOrderInfo, 'ordernum');
            $where['ordernum'] = ['in', $tempOrderNum];
            $res               = $this->table(self::CARD_TRADE_TABLE)->where($where)->field($filed)->select();
        }

        return $res ?: [];

    }

    /**
     * 修改年卡追踪表中的订单号 (异常情况下调用)
     * <AUTHOR> Li
     * @date   2019-11-12
     *
     * @param  int  $id  追踪表id
     * @param  string  $oldOrdernum  原始订单号
     * @param  string  $ordernum  修改后的订单号
     * @param  string  $memo  备注信息
     *
     * @return array
     */
    public function changeAnnualCardTradeOrdernumById($id, $oldOrdernum, $ordernum, $memo = '')
    {
        if (!$id || !$oldOrdernum || !$ordernum) {
            return false;
        }

        $where = [
            'ordernum' => (string)$oldOrdernum,
            'id'       => $id,
        ];

        $data = [
            'ordernum' => $ordernum,
            'remark'   => $memo,
        ];

        $result = $this->table(self::CARD_TRADE_TABLE)->where($where)->limit(1)->save($data);

        return $result !== false ? $result : false;

    }

    /**
     * 根据字段获取年卡信息
     * <AUTHOR>
     * @date   2020-04-23
     *
     * @param  string  $card_no  实体卡卡号
     * @param  string  $sid  供应商id
     * @param  int  $type  0实体卡1虚拟卡
     *
     * @return array
     */
    public function getAnnualCardBycardNoAndsid($card_no, $sid, $type = 0)
    {

        if (!$card_no || !$sid) {
            return [];
        }
        if ($type) {
            $where = [
                'virtual_no' => $card_no,
                'sid'        => $sid,
            ];
        } else {
            $where = [
                'card_no' => $card_no,
                'sid'     => $sid,
            ];
        }

        return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->find();

    }

    /**
     * 从仓库获取N张虚拟卡
     * <AUTHOR>  Li
     * @date   2020-07-03
     *
     * @param  int  $pid  套餐pid
     * @param  int  $num  获取数量
     * @param  bool  $isLock  是否锁定年卡
     * @param  string  $type  获取年卡类型   virtual虚拟卡
     *
     * @return array
     */
    public function getRandVirtualCardFromUnsaleByNum($pid, $num = 1, $isLock = false, $type = 'virtual')
    {
        if (!$pid) {
            return [];
        }

        $where = [
            'pid'       => $pid,
            'status'    => AnnualCardConst::STATUS_IN_STOCK,
            'lock_type' => 0,
        ];

        if ($type == 'virtual') {
            $where['card_no'] = ['EQ', ''];
        } else {
            $where['card_no'] = ['NEQ', ''];
        }

        $result = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->field('virtual_no')->page(0,
            $num)->getField('virtual_no', true);

        return $result;
    }

    /**
     * 将年卡锁定
     * <AUTHOR>  Li
     * @date   2020-07-03
     *
     * @param  array  $virtualNoArr  虚拟卡号数组
     * @param  int  $lockType  锁定状态  0解锁 1锁定
     *
     * @return bool
     */
    public function lockOrUnLockAnnualCard($virtualNoArr, $lockType = '')
    {
        if (!$virtualNoArr || $lockType === '') {
            return false;
        }
        $res = $this->table(self::ANNUAL_CARD_TABLE)
                    ->where(['virtual_no' => ['in', $virtualNoArr]])
                    ->save(['lock_type' => $lockType]);

        return boolval($res);
    }

    /**
     * 计算当前用户有效的年卡数量
     * <AUTHOR>
     *
     * @param  int  $memberId
     *
     * @return int
     */
    public function countEffectCardNumberByMemberId(int $memberId): int
    {
        if (empty($memberId)) {
            return 0;
        }

        $where = [
            'memberid' => $memberId,
            'status'   => AnnualCardConst::STATUS_ACTIVE,
        ];

        return $this->table(self::ANNUAL_CARD_TABLE)->where($where)->count();
    }

    /**
     * 根据虚拟卡号判断年卡是不是在仓库中
     * <AUTHOR>
     *
     * @param  array  $virtual_arr  虚拟卡号数组
     *
     * @return array
     */
    public function getStoreStatusForVirtual(array $virtual_arr)
    {
        if (count($virtual_arr) < 1) {
            return [];
        }

        $where = [
            'virtual_no' => ['in', $virtual_arr],
            'status'     => AnnualCardConst::STATUS_IN_STOCK,
            'lock_type'  => 0,
        ];

        $res = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->field('virtual_no')->select();

        return $res ?: [];
    }

    /**
     * 通过虚拟卡号更新扩展属性信息
     * <AUTHOR>  Li
     * @date  2022-09-09
     *
     * @param  string  $virtualNo  虚拟卡号
     * @param  array  $extInfoArr  扩展信息
     *
     * @return bool
     */
    public function updateExtInfoByVirtualNo($virtualNo, $extInfoArr = [])
    {
        if (!$virtualNo) {
            return false;
        }

        $where = [
            'virtual_no' => $virtualNo,
        ];

        $data = [
            'ext_info' => json_encode($extInfoArr),
        ];

        $result = $this->table(self::ANNUAL_CARD_TABLE)->where($where)->limit(1)->save($data);

        return $result !== false ? $result : false;
    }

    /**
     * 获取年卡分页列表
     * <AUTHOR>
     * @date   2022/10/8
     *
     * @param  int  $sid  供应商id
     * @param  array  $options  查询条件
     * @param  bool  $isTotal  是否返回总数
     * @param  string  $field  查询字段
     *
     * @return array|int
     */
    public function getCardPageList(int $sid, array $options = [], bool $isTotal = false, string $field = '')
    {
        $status = (int)$options['status'];
        //$time         = time();

        if ($sid) {
            $where['sid'] = $sid;
        }

        if ($status == 5) {
            $where['status'] = ['neq', 3];
        } else {
            if ($status != 6) {
                $where['status'] = $status;
                if ($status == 7) {  //没办法了，只能这里这么转换了，数据库定义了5位审核中状态，改上面的判断耗费时间太多了，需要改的接口很多
                    $where['status'] = 5;
                }
            } else {
                //年卡延期列表中获取未激活 + 已激活的数据
                $where['status'] = ['in', [0, 1]];
            }
        }
        //卡号手机号等搜索操作
        if (isset($options['identify']) && $options['identify']) {
            $identify = $options['identify'];
            if (isset($options['search_type']) && $options['search_type'] != -1) {
                switch ($options['search_type']) {
                    //0物理卡 1 实体卡 2虚拟卡 3手机号 4身份证 5姓名
                    case 0:
                        $where['physics_no'] = dechex($identify);
                        break;
                    case 1:
                        $where['card_no'] = $identify;
                        break;
                    case 2:
                        $where['virtual_no'] = $identify;
                        break;
                    case 3:
                        $where['mobile'] = $identify;
                        break;
                    case 4:
                        $where['id_card_no'] = $identify;
                        break;
                    case 5:
                        $where['dname'] = $identify;
                        break;
                }
            } else {
                if (\ismobile($identify)) {
                    $where['mobile'] = $identify;
                } elseif (Tools::idcard_checksum18($identify)) {
                    $where['id_card_no'] = $identify;
                } else {
                    //物理卡查询处理
                    if (dechex($identify)) {
                        $where2['physics_no'] = dechex($identify);
                    }
                    $where2['card_no']    = $identify;
                    $where2['virtual_no'] = $identify;
                    $where2['id_card_no'] = $identify; //支持多证件类型后，加在这边查询下
                    $where2['_logic']     = 'or';
                    $where['_complex']    = $where2;
                }
            }
        }

        //分页处理
        if (isset($options['page'], $options['page_size'])) {
            $page  = $options['page'];
            $size  = $options['page_size'];
            $limit = (($page - 1) * $size) . ',' . $size;
        } else {
            $limit = 10000;
        }

        if (isset($options['memberid'])) {
            $where['memberid'] = $options['memberid'];
            unset($where['status']);
        }

        //售出时间查询
        if (!empty($options['sale_start']) && !empty($options['sale_end'])) {
            $saleStart          = strtotime($options['sale_start'] . ' 00:00:00');
            $saleEnd            = strtotime($options['sale_end'] . ' 23:59:59');
            $where['sale_time'] = ['between', [$saleStart, $saleEnd]];
        }

        //激活时间查询
        if (!empty($options['active_start']) && !empty($options['active_end'])) {
            $activeStart          = strtotime($options['active_start'] . ' 00:00:00');
            $activeEnd            = strtotime($options['active_end'] . ' 23:59:59');
            $where['active_time'] = ['between', [$activeStart, $activeEnd]];
        }

        //有效期开始时间范围查询
        if (!empty($options['valid_start']) && !empty($options['valid_start']['start']) && !empty($options['valid_start']['end'])) {
            $validStartStart       = strtotime($options['valid_start']['start'] . ' 00:00:00');
            $validStartEnd         = strtotime($options['valid_start']['end'] . ' 23:59:59');
            $where['avalid_begin'] = ['between', [$validStartStart, $validStartEnd]];
        }

        //有效期结束时间范围查询
        if (!empty($options['valid_end']) && !empty($options['valid_end']['start']) && !empty($options['valid_end']['end'])) {
            $validEndStart       = strtotime($options['valid_end']['start'] . ' 00:00:00');
            $validEndEnd         = strtotime($options['valid_end']['end'] . ' 23:59:59');
            $where['avalid_end'] = ['between', [$validEndStart, $validEndEnd]];
        }

        //查询主套餐状态
        $packState = $options['pack_state'] ?? 0;
        if (in_array($packState, [1, 2, 3])) {
            if ($packState == 1) {//未生效
                $where['avalid_end']   = [['gt', strtotime(date("Y-m-d 23:59:59"))], ['neq', '']];
                $where['avalid_begin'] = [['gt', strtotime(date("Y-m-d 23:59:59"))], ['neq', '']];
            }
            if ($packState == 2) {//生效
                $where['avalid_end']   = [['egt', strtotime(date("Y-m-d 23:59:59"))], ['neq', '']];
                $where['avalid_begin'] = [['elt', strtotime(date("Y-m-d 00:00:00"))], ['neq', '']];
            }
            if ($packState == 3) {//已过期
                $where['avalid_end']   = [['lt', strtotime(date("Y-m-d 00:00:00"))], ['neq', '']];
                $where['avalid_begin'] = [['lt', strtotime(date("Y-m-d 00:00:00"))], ['neq', '']];
            }
        }

        //分销商id查询
        if (!empty($options['distributor_id'])) {
            $where['distributor_id'] = $options['distributor_id'];
        }

        //产品id查询
        if (!empty($options['pid'])) {
            $where['pid'] = ['in', $options['pid']];
        }

        //查询字段
        if (empty($field)) {
            $field = 'id,sid,virtual_no,card_no,sale_time,memberid,activate_source,' .
                     'active_time,pid,status,avalid_begin,avalid_end,physics_no,distributor_id,' .
                     'dname,mobile,id_card_no,province,city,address,annual_status,ext_info,remarks';
        }

        $query = $this->table($this->annualCardTable)->where($where);

        //总数查询
        if ($isTotal) {
            $total = $query->count();

            return (int)$total;
        }

        //列表查询
        $res = $query->field($field)
                     ->limit($limit)
                     ->order('sale_time desc')
                     ->select();

        return is_array($res) ? $res : [];
    }

    /**
     * 根据虚拟卡号获取特权订单使用数量
     * <AUTHOR>
     * @date   2022/10/12
     *
     * @param  string  $virtualNo  虚拟卡
     *
     * @return array
     */
    public function getVirtualOrderCount(string $virtualNo)
    {
        if (empty($virtualNo)) {
            return [];
        }
        $where = [
            'virtual_no' => $virtualNo,
            'status'     => 1,
        ];

        $field = 'virtual_no, count(id) as count';

        return $this->table(self::CARD_ORDER_TABLE)->where($where)->group('virtual_no')->getField($field, true);
    }

    /**
     * 通过虚拟卡查询首次购买订单号
     * <AUTHOR>
     * @date   2022/10/12
     *
     * @param  string  $virtualNo  虚拟卡号
     *
     * @return string
     */
    public function getCardPurchaserOrderNum(string $virtualNo)
    {
        if (empty($virtualNo)) {
            return '';
        }

        $where = [
            'virtual_no' => $virtualNo,
            'status'     => 1,
        ];

        $res = $this->table(self::CARD_MAPPING_TABLE)->where($where)->order('id asc')->find();

        return $res['ordernum'] ?? '';
    }

    public function getInactivedCardList($sid, $telephone, $memberId = 0, $page, $limit)
    {
        $wh = [
            'sid'    => $sid,
            'mobile' => $telephone,
            'status' => AnnualCardConst::STATUS_NOT_ACTIVE,
        ];
        if ($memberId != 0) {
            $wh['memberid'] = $memberId;
        }
        $query = $this->table(self::ANNUAL_CARD_TABLE)->where($wh);
        $total = (int)$query->count();

        $query  = $this->table(self::ANNUAL_CARD_TABLE)->where($wh);
        $query  = $query->order(['id' => 'desc']);
        $offset = ($page - 1) * $limit;
        $query  = $query->limit($offset, $limit);

        $records = $query->select();

        return [
            'total'   => $total,
            'records' => $records,
        ];
    }

    /**
     * 根据ID获取年卡信息
     *
     * @param  array  $cardIdArr  年卡表ID
     * @param  string  $field  查询字段
     *
     * @return array
     */
    public function getCardInfoByIdArr($cardIdArr, $status, $field = 'id')
    {
        if (!$cardIdArr) {
            return [];
        }
        $where = [
            'id'     => ['in', $cardIdArr],
            'status' => ['in', $status],
        ];
        $res   = $this->table($this->annualCardTable)->where($where)->field($field)->select();

        return is_array($res) ? $res : [];
    }
}
