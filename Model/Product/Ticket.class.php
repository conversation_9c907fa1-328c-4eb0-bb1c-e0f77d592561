<?php
/**
 * 门票信息模型
 */

namespace Model\Product;

use Business\CommodityCenter\LandF;
use Business\CommodityCenter\Product;
use Business\JavaApi\StorageApi;
use Business\JavaApi\TicketApi;
use Business\Product\Get\EvoluteQuery;
use Library\Constants\Table\MainTableConst;
use Library\Model;
use Model\Member\Member;
use Model\Order\SubOrderQuery\SubOrderAddon;
use Business\CommodityCenter\Ticket as ticketBiz;
use Business\CommodityCenter\Land;

class Ticket extends Model
{
    private $ticket_filed = [
        'id',
        'landid',
        'title',
        'tprice',
        'reb',
        'discount',
        'delaydays',
        'use_early_days',
        'status',
        'pay',
        'notes',
        'ddays',
        'getaddr',
        'smslimit',
        's_limit_up',
        's_limit_low',
        'buy_limit_up',
        'if_verify',
        'buy_limit_low',
        'open_time',
        'end_time',
        'apply_did',
        'pid',
        'cancel_cost',
        'reb_type',
        'order_start',
        'max_order_days',
        'cancel_auto_onMin',
        'delaytype',
        'delaytime',
        'order_end',
        'order_limit',
        'expire_action',
        'expire_action_days',
        'batch_check',
        'batch_day_check',
        'batch_diff_identities',
        'refund_audit',
        'refund_rule',
        'refund_early_time',
        'cancel_notify_supplier',
        're_integral',
        'revoke_audit',
        'delay_refund_time',
        'print_ticket_limit',
        'modify_limit_time',
    ];

    const TICKET_ATTR_MAP = [
        'title'                  => '门票名称',
        'tprice'                 => '市场价',
        'delaydays'              => '允许推迟游玩的天数',
        'use_early_days'         => '游玩日期前多少天有效',
        'mobre'                  => '购票年龄限制[满多少岁后可以购买]',
        'px'                     => '排序',
        'pay'                    => '支付方式',
        'notes'                  => '购票须知',
        'ifs'                    => '是否有特价票',
        'if_verify'              => '游玩日期前不可验证',
        'ddays'                  => '提前下单间隔时间',
        'getaddr'                => '取票地址',
        's_limit_up'             => '库存上限',
        's_limit_low'            => '库存下限',
        'buy_limit_up'           => '购买上限',
        'buy_limit_low'          => '购买下限',
        'apply_did'              => '供应商ID',
        'modify_op'              => '修改人ID',
        're_integral'            => '点评返现积分',
        'cancel_cost'            => '取消费用',
        'reb_type'               => '取消费用类型 0 百分比，1 实际指定具体值',
        'order_start'            => '订单有效期开始日期 00:00:00',
        'max_order_days'         => '提前下单最多间隔天数',
        'Mdetails'               => '是否开启景点类别属性',
        'Mpath'                  => '景点类别属性功能模块路径',
        'uuid'                   => '第三方系统ID',
        'sourceT'                => '第三方系统配置',
        'cancel_auto_onMin'      => '未支付多少分钟内自动取消',
        'delaytype'              => '允许推迟的天数使用0游玩时间/1下单时间/2期票模式',
        'delaytime'              => '延迟验证时间',
        'order_end'              => '截止订单有效期日期',
        'order_limit'            => '订单验证限制日期 默认全部',
        'expire_action'          => '订单过期后处理方式 - 1=不做处理，2=自动完结，3=自动验证，4=自动取消',
        'expire_action_days'     => '多少天后自动处理 - 结合expire_action使用',
        'batch_check'            => '分批验证',
        'batch_day_check'        => '分批验证 每天可验证张数',
        'batch_diff_identities'  => '限不同身份证验证',
        'refund_audit'           => '退票审核:0不审核, 1需审核',
        'refund_rule'            => '退票规则:0有效期内、过期可退,1有效期内可退, 2不可退',
        'refund_early_time'      => '退票提前多少分钟 ',
        'cancel_notify_supplier' => '取消订单时短信通知供应商',
        'delay_refund_time'      => '延迟退款时间',
    ];

    /**
     * 根据票类id获取票类信息
     * <AUTHOR>
     *
     * @param  int  $id  票类id
     *
     * @return array
     */
    public function getTicketInfoById($id, $filed = '', $map = [])
    {
        $filed = (empty($filed) || $filed == '*') ? implode($this->ticket_filed, ',') : $filed;
        $map   = array_merge(['id' => $id], $map);

        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoById($id, $filed);
        $data      = $ticketArr['ticket'];
        if (is_array($data) && is_array($ticketArr['ext'])) {
            $data = array_merge($ticketArr['ext'], $data);
        }

        return $data;
    }

    /**
     * 根据票类id获取票类信息和扩展信息
     * <AUTHOR>
     * @date 2020/8/18
     *
     * @param  int  $id  票类id
     * @param  string  $filed  展示字段
     * @param  bool  $ext  扩展配置
     *
     * @return array
     * @throws
     */
    public function getTicketInfoExtById($id, $filed = '', $isExt = true)
    {
        $filed = (empty($filed) || $filed == '*') ? implode($this->ticket_filed, ',') : $filed;

        $javaApi        = new ticketBiz();
        $ticketArr      = $javaApi->queryTicketInfoById($id, $filed);
        $data           = [];
        $data['ticket'] = $ticketArr['ticket'];
        if ($isExt) {
            $data['ext'] = $ticketArr['ext'];
        }

        return $data;
    }

    /**
     * 判断供应商是否可以发布现场支付的套票
     *
     * @param  int  $apply_did  供应商ID
     *
     * @return bool
     */
    public function allowOfflinePackage($apply_did)
    {
        $allow_list  = [4];
        $member_list = [94, 3385];

        $queryParams = [[$apply_did]];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
            $queryParams);
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $group_id = array_column($queryRes['data'], null, 'id')[$apply_did]['group_id'];

            //$member      = new Member();
            //$group_id    = $member->getMemberCacheById($apply_did, 'group_id');
            if (in_array($group_id, $allow_list) || in_array($apply_did, $member_list)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取门票扩展属性
     * <AUTHOR> Chen
     *
     * @param  int  $tid  门票ID
     * @param  string  $field
     *
     * @return mixed
     */
    public function getTicketExtInfoByTid($tid, $field = "*")
    {
        $commodityLandFBiz = new LandF();
        $ticketArr         = $commodityLandFBiz->queryLandFByLandIdAndTicketIdAndProductId([$tid], null, null, $field);
        $return            = !empty($ticketArr[0]) ? $ticketArr[0] : [];

        return $return;
    }

    /**
     * 根据productid获取票类信息
     * <AUTHOR>
     *
     * @param  int|array  $pid  product_id
     * @param  string  $field  查询字段
     *
     * @return array|int
     */
    public function getTicketInfoByPid($pid, $field = '*', $getFiled = false)
    {
        if (!$pid) {
            return [];
        }
        $javaApi = new ticketBiz();
        $idTypes = ['id', 'landid', 'apply_did'];
        if (strpos($field, ',') !== false || in_array($field, $idTypes)) {
            $filedArr = explode(',', $field);
            if (count(array_diff($filedArr, $idTypes)) == 0) {
                $rawField = $field;
                $field    = $idTypes;
                $cacheKey = "ticket:pid_map:$pid";
                $cache    = \Library\Cache\Cache::getInstance('redis');
                $data     = $cache->hgetAll($cacheKey);
                if ($data) {
                    return isset($data[$rawField]) ? [$rawField => $data[$rawField]] : $data;
                }
            }
        }

        $ticketRes = $javaApi->queryTicketInfoByProductIds([$pid], implode(',', $this->ticket_filed));
        if (empty($ticketRes)) {
            return [];
        }

        $landFArr  = $ticketRes[0]['land_f'] ?? [];
        $landArr   = $ticketRes[0]['land'] ?? [];
        $ticketArr = $ticketRes[0]['ticket'] ?? [];
        $extArr    = $ticketRes[0]['ext'] ?? [];

        $data = array_merge($extArr, $landFArr, $landArr, $ticketArr);

        if (isset($cacheKey) && isset($cache)) {
            $cache->hmset($cacheKey, $data);
            $cache->expire($cacheKey, 2678400);
        }

        return (isset($rawField) && isset($data[$rawField])) ?
            [$rawField => $data[$rawField]] : $data;
    }

    /**
     * 获取产品类型
     *
     * @param  int  $pid  productID
     *
     * @return array
     */
    public function getProductType($pid)
    {
        $commodityProductBiz = new Product();
        $productInfo         = $commodityProductBiz->getProductInfoById($pid);

        return $productInfo['p_type'] ? $productInfo['p_type'] : '';
    }

    /**
     * 获取自供应可出售产品
     *
     * @param  int  $memberId  会员id
     * @param  array  $options  获取产品信息时的额外参数 ['limit' => 10, field => 'l.title']
     *
     * @return array    产品数据列表
     */

    public function getSaleProducts($memberId, $options = [])
    {
        if (!is_numeric($memberId) || !is_array($options)) {
            return [];
        }

        $where = ['fid' => $memberId];

        // 获取分销链的切换到java
        $getEvoluteBiz  = new \Business\Product\Get\Evolute();
        $evoluteListArr = $getEvoluteBiz->getEvoluteBySidFidStatusLvl([$memberId], [$memberId], -1, 0);

        if (empty($evoluteListArr)) {
            return [];
        }

        return $this->_getProAndEvoInfo($where, $options, $evoluteListArr);
    }

    /**
     * getSaleDisProducts  没有 $evoWhere 的版本
     *
     * @param $memberId
     * @param  array  $options
     *
     * @return array
     */
    public function getSaleDisProductsByFidSourceIdStatus($memberId, $options = [])
    {
        $getEvoluteBiz  = new \Business\Product\Get\Evolute();
        $evoluteListArr = $getEvoluteBiz->getEvoluteByFidStatusLvlLvleq([$memberId], 0, 0, 6);

        if (empty($evoluteListArr)) {
            return [];
        }

        return $this->_getProAndEvoInfo([], $options, $evoluteListArr);
    }

    /**
     * getSaleDisProducts   $evoWhere 有active 的版本
     *
     * @param $memberId
     * @param  int  $active
     * @param  array  $options
     *
     * @return array
     */
    public function getSaleDisProductsByFidSourceIdStatusActive($memberId, $active = -1, $options = [])
    {
        $getEvoluteBiz  = new \Business\Product\Get\Evolute();
        $evoluteListArr = $getEvoluteBiz->getEvoluteByFidActiveStatusLvlLvleq([$memberId], 0, $active, 0, 6);

        if (empty($evoluteListArr)) {
            return [];
        }

        return $this->_getProAndEvoInfo([], $options, $evoluteListArr);
    }

    /**
     * @param $fid
     * @param $options
     *
     * @return array
     */
    public function getAllSaleProductsByFid($fid, $options)
    {
        if (empty($fid)) {
            return [];
        }

        $getEvoluteBiz = new \Business\Product\Get\Evolute();
        if ($options['union_channel'] && in_array($options['union_channel'], [1, 15])) {
            $evoluteList = $getEvoluteBiz->getEvoluteByFidShopStatus($fid, $options['union_channel'], 0);
            unset($options['union_channel']);
        } else {
            $evoluteList = $getEvoluteBiz->getEvoluteByFidStatus($fid, 0);
        }

        if (empty($evoluteList)) {
            return [];
        }

        return $this->_getProAndEvoInfo(['fid' => $fid], $options, $evoluteList);
    }

    /**
     * @param $fid
     * @param $sid
     * @param $options
     *
     * @return array
     */
    public function getAllSaleProductsByFidSid($fid, $sid, $options)
    {
        if (empty($fid) || empty($sid)) {
            return [];
        }

        $getEvoluteBiz = new \Business\Product\Get\Evolute();
        $evoluteList   = $getEvoluteBiz->getEvoluteBySidFidStatus($sid, $fid, -1);

        if (empty($evoluteList)) {
            return [];
        }

        return $this->_getProAndEvoInfo(['fid' => $fid], $options, $evoluteList);
    }

    /**
     * 获取产品以及分销链信息
     *
     * @param  array  $evoWhere  分销链的查询条件
     * @param  array  $options  获取产品信息时的额外参数 ['limit' => 10, field => 'l.title']
     *
     * @return array
     */
    private function _getProAndEvoInfo($evoWhere, $options, $evoluteList = [])
    {
        if (empty($evoluteList)) {
            return [];
        }

        //获取产品信息
        $condition = [
            'pStatuss'    => [0],
            'applyLimit'  => 1,
            'landFlag'    => 1,
            'productFlag' => 1,
            'landFFlag'   => 1,
            'landStatus'  => 1,
        ];

        $pidArr = array_column($evoluteList, 'pid');
        $pidArr = array_unique($pidArr);

        $tidArr = [];
        $lidArr = [];

        if (isset($options['where'])) {
            //景区名称搜索
            if (isset($options['where']['l.title'])) {
                $condition['landTitle'] = $options['where']['l.title'];
            }
            //产品类型搜索
            if (isset($options['where']['l.p_type'])) {
                $condition['pTypes'] = is_array($options['where']['l.p_type']) ? $options['where']['l.p_type'] : [$options['where']['l.p_type']];
            }
            //门票id搜索
            if (isset($options['where']['t.id'])) {
                $tidArr = is_array($options['where']['t.id']) ? $options['where']['t.id'] : [$options['where']['t.id']];
            }
            //支付方式搜索
            if (isset($options['where']['t.pay'])) {
                $condition['pay'] = [$options['where']['t.pay']];
            }
            //景区id搜索
            if (isset($options['where']['l.id'])) {
                $lidArr = is_array($options['where']['l.id']) ? $options['where']['l.id'] : [$options['where']['l.id']];
            }

            unset($options['where']);
        }

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable('pid,id,pay,delaytime,getaddr,title,tprice,notes,shop,buy_limit_low,ddays,delaydays,batch_check,batch_day_check,order_limit,overdue_refund,status,order_end,refund_audit,order_start,delaytype,cancel_cost',
            'id,apply_did,p_status',
            'id,p_type,jtype,areacode,terminal,title,salerid,area,topic,px,runtime,imgpath,letters,order_flag,address',
            'ass_station,tourist_info,mdays,mhour,rdays,age_limit_min,age_limit_max,dhour,sendVoucher,v_time_limit',
            '',
            $tidArr, $lidArr, null, $pidArr, $condition);

        $list = [];
        if (!empty($ticketArr)) {
            foreach ($ticketArr as $ticketInfo) {
                $list[$ticketInfo['product']['id']]['tid']             = $ticketInfo['ticket']['id'];
                $list[$ticketInfo['product']['id']]['pay']             = $ticketInfo['ticket']['pay'];
                $list[$ticketInfo['product']['id']]['delaytime']       = $ticketInfo['ticket']['delaytime'];
                $list[$ticketInfo['product']['id']]['getaddr']         = $ticketInfo['ticket']['getaddr'];
                $list[$ticketInfo['product']['id']]['ttitle']          = $ticketInfo['ticket']['title'];
                $list[$ticketInfo['product']['id']]['tprice']          = $ticketInfo['ticket']['tprice'];
                $list[$ticketInfo['product']['id']]['notes']           = $ticketInfo['ticket']['notes'];
                $list[$ticketInfo['product']['id']]['shop']            = $ticketInfo['ticket']['shop'];
                $list[$ticketInfo['product']['id']]['ticket_id']       = $ticketInfo['ticket']['id'];
                $list[$ticketInfo['product']['id']]['buy_limit_low']   = $ticketInfo['ticket']['buy_limit_low'];
                $list[$ticketInfo['product']['id']]['ddays']           = $ticketInfo['ticket']['ddays'];
                $list[$ticketInfo['product']['id']]['delaydays']       = $ticketInfo['ticket']['delaydays'];
                $list[$ticketInfo['product']['id']]['batch_check']     = $ticketInfo['ticket']['batch_check'];
                $list[$ticketInfo['product']['id']]['batch_day_check'] = $ticketInfo['ticket']['batch_day_check'];
                $list[$ticketInfo['product']['id']]['order_limit']     = $ticketInfo['ticket']['order_limit'];
                $list[$ticketInfo['product']['id']]['overdue_refund']  = $ticketInfo['ticket']['overdue_refund'];
                $list[$ticketInfo['product']['id']]['status']          = $ticketInfo['ticket']['status'];
                $list[$ticketInfo['product']['id']]['order_end']       = $ticketInfo['ticket']['order_end'];
                $list[$ticketInfo['product']['id']]['refund_audit']    = $ticketInfo['ticket']['refund_audit'];
                $list[$ticketInfo['product']['id']]['order_start']     = $ticketInfo['ticket']['order_start'];
                $list[$ticketInfo['product']['id']]['delaytype']       = $ticketInfo['ticket']['delaytype'];
                $list[$ticketInfo['product']['id']]['cancel_cost']     = $ticketInfo['ticket']['cancel_cost'];
                $list[$ticketInfo['product']['id']]['id']              = $ticketInfo['product']['id'];
                $list[$ticketInfo['product']['id']]['pid']             = $ticketInfo['product']['id'];
                $list[$ticketInfo['product']['id']]['apply_did']       = $ticketInfo['product']['apply_did'];
                $list[$ticketInfo['product']['id']]['p_status']        = $ticketInfo['product']['p_status'];
                $list[$ticketInfo['product']['id']]['lid']             = $ticketInfo['land']['id'];
                $list[$ticketInfo['product']['id']]['p_type']          = $ticketInfo['land']['p_type'];
                $list[$ticketInfo['product']['id']]['jtype']           = $ticketInfo['land']['jtype'];
                $list[$ticketInfo['product']['id']]['areacode']        = $ticketInfo['land']['areacode'];
                $list[$ticketInfo['product']['id']]['terminal']        = $ticketInfo['land']['terminal'];
                $list[$ticketInfo['product']['id']]['title']           = $ticketInfo['land']['title'];
                $list[$ticketInfo['product']['id']]['salerid']         = $ticketInfo['land']['salerid'];
                $list[$ticketInfo['product']['id']]['area']            = $ticketInfo['land']['area'];
                $list[$ticketInfo['product']['id']]['topic']           = $ticketInfo['land']['topic'];
                $list[$ticketInfo['product']['id']]['px']              = $ticketInfo['land']['px'];
                $list[$ticketInfo['product']['id']]['runtime']         = $ticketInfo['land']['runtime'];
                $list[$ticketInfo['product']['id']]['imgpath']         = $ticketInfo['land']['imgpath'];
                $list[$ticketInfo['product']['id']]['py']              = $ticketInfo['land']['letters'];
                $list[$ticketInfo['product']['id']]['order_flag']      = $ticketInfo['land']['order_flag'];
                $list[$ticketInfo['product']['id']]['address']         = $ticketInfo['land']['address'];
                $list[$ticketInfo['product']['id']]['ass_station']     = $ticketInfo['land_f']['ass_station'];
                $list[$ticketInfo['product']['id']]['tourist_info']    = $ticketInfo['land_f']['tourist_info'];
                $list[$ticketInfo['product']['id']]['mdays']           = $ticketInfo['land_f']['mdays'];
                $list[$ticketInfo['product']['id']]['mhour']           = $ticketInfo['land_f']['mhour'];
                $list[$ticketInfo['product']['id']]['rdays']           = $ticketInfo['land_f']['rdays'];
                $list[$ticketInfo['product']['id']]['age_limit_min']   = $ticketInfo['land_f']['age_limit_min'];
                $list[$ticketInfo['product']['id']]['age_limit_max']   = $ticketInfo['land_f']['age_limit_max'];
                $list[$ticketInfo['product']['id']]['dhour']           = $ticketInfo['land_f']['dhour'];
                $list[$ticketInfo['product']['id']]['sendVoucher']     = $ticketInfo['land_f']['sendVoucher'];
                $list[$ticketInfo['product']['id']]['v_time_limit']    = $ticketInfo['land_f']['v_time_limit'];
            }
        }

        //产品列表

        $list   = $list ?: [];
        $return = [];
        foreach ($evoluteList as $item) {
            if (isset($list[$item['pid']])) {
                $tmp               = $list[$item['pid']];
                $tmp['eid']        = $item['id'];
                $tmp['px']         = $item['px'];
                $tmp['tx']         = $item['tx'];
                $tmp['level']      = $item['lvl'];
                $tmp['channel']    = $item['channel'];
                $tmp['apply_sid']  = $item['sid'];
                $tmp['sapply_sid'] = $item['sourceid'];
                $tmp['aids']       = $item['aids'];
                $tmp['evo_active'] = $item['active'];

                if ($item['sid'] == $item['sourceid'] && !is_array($evoWhere['fid'])) {
                    $return[$item['pid']] = $tmp;
                } else {
                    $return[$item['pid'] . '_' . $item['aids']] = $tmp;
                }
            }
        }

        return $return;
    }

    /**
     * 获取转分销产品总数
     *
     * @param  int  $memberId  会员id
     *
     * @return int
     */
    public function saleDisCount($memberId)
    {
        if (!$memberId) {
            return 0;
        }

        $evoluteQueryLib = new EvoluteQuery();
        $result          = $evoluteQueryLib->queryEvoluteCount(intval($memberId));
        if ($result['code'] != 200 || empty($result['data'])) {
            $num = 0;
        } else {
            $num = intval($result['data']);
        }

        return $num;
    }

    /**
     * 产品状态过滤
     *
     * @param  array  $pidArr  产品id数组
     *
     * @return array
     */
    public function filterInvalidPid($pidArr)
    {
        if (!$pidArr) {
            return [];
        }
        $commodityProductBiz = new Product();
        $list                = $commodityProductBiz->getProductInfoByIds($pidArr);

        $list   = $list ?: [];
        $return = [];
        foreach ($list as $item) {
            // '0未审核1已审核2下架3被拒绝6删除',
            if ($item['apply_limit'] != 1) {
                continue;
            }

            // '0正常1禁止销售2仅零售3仅分销销售4仅直属下级销售5仅所有者销售6已废弃 票付通 0正常 其他禁用',
            if (!in_array($item['p_status'], [0, 3, 4, 5])) {
                continue;
            }

            $return[] = $item['id'];
        }

        return $return;
    }

    /**
     * 获取门票零售价
     * <AUTHOR>
     *
     * @param  int  $pid  productID
     * @param  string  $date  日期,2016-03-26
     *
     * @return mixed
     */
    public function getRetailPrice($pid, $date = '')
    {
        if (!$pid) {
            return false;
        }

        if (\inWechatSmallApp()) {
            //小程序获取窗口价
            return $this->getUprice($pid, $date);
        }

        $result = $this->getMuchRetailPrice([$pid], $date);

        if (isset($result[$pid])) {
            return $result[$pid];
        } else {
            return false;
        }
    }

    /**
     * 获取窗口价
     * <AUTHOR>  lanlc-2018-01-19
     * @date   2017-09-27
     *
     * @param  int  $pid  门票pid
     *
     * @return int | false
     */
    public function getUprice($pid, $date = '')
    {
        if (!$pid) {
            return false;
        }

        if (!$date) {
            $date = date('Y-m-d');
        }

        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketIdsByProductIds([$pid]);
        if (empty($ticketArr)) {
            return false;
        }
        $ticket['id'] = $ticketArr[0];

        $priceData = TicketApi::getSinglePrices($ticket['id'], $date);
        if (empty($priceData)) {
            return false;
        }

        if ($priceData[$ticket['id']]['window_price']) {
            return $priceData[$ticket['id']]['window_price'] / 100;
        } else {
            return $priceData[$ticket['id']]['counter_price'] / 100;
        }
    }

    /**
     * 获取门市价
     * Create by zhangyangzhen
     * Date: 2019/1/8
     * Time: 11:59
     *
     * @param  int pid 产品ID
     * @param  string date 日期
     */
    public function getCounterPrice($pid, $date = '')
    {
        if (!$pid) {
            return false;
        }

        if (!$date) {
            $date = date('Y-m-d');
        }

        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketIdsByProductIds([$pid]);
        if (empty($ticketArr)) {
            return false;
        }
        $ticket['id'] = $ticketArr[0];

        $priceData = TicketApi::getSinglePrices($ticket['id'], $date);
        if (empty($priceData)) {
            return false;
        }

        return $priceData[$ticket['id']]['counter_price'] / 100;
    }

    /**
     * 一次性获取多产品零售价
     * <AUTHOR>
     *
     * @param  array  $pidArr  array(1,2)
     * @param  string  $date  日期
     *
     * @return array  array(1 => 0.01, 2 => 0.02)
     */

    public function getMuchRetailPrice($pidArr, $date = '')
    {
        if (!$pidArr) {
            return [];
        }

        $mapping = $this->getTidArrByPid($pidArr);
        $mapping = array_flip($mapping);
        $tidArr  = array_keys($mapping);

        $priceApi = new \Business\JavaApi\Ticket\Price();
        $priceRes = $priceApi->batchBasePrice($tidArr, $date);
        if ($priceRes['code'] != 200) {
            return [];
        }

        $return = [];
        foreach ($priceRes['data'] as $key => $item) {
            $tid = $item['ticketId'];

            if ($item['lPrice'] != -1) {
                $return[$mapping[$tid]] = $item['lPrice'] / 100;
            }
        }

        return $return;
    }

    /**
     * 一次性获取多产品的实时库存
     * <AUTHOR>
     *
     * @param  array  $pidArr  array(1026,2067)
     * @param  string  $date  2016-07-03
     * @param  int  $memberid  会员id
     * @param  int  $sapply_did  供应商id
     *
     * @return array    array(1026 => 2, 2067 => 3)
     */
    public function getMuchStorage($pidArr, $date = '', $memberid = 0, $sapply_did = 0)
    {
        if (!$pidArr) {
            return [];
        }

        if (!is_array($pidArr)) {
            $pidArr = [$pidArr];
        }

        $playDate = $date ?: date('Y-m-d', time());

        foreach ($pidArr as $key => &$pid) {
            $pid = intval($pid);
        }

        $ticketArr = $this->getTidArrByPid($pidArr);

        $ticketStr  = implode(',', $ticketArr);
        $storageArr = StorageApi::getBatchStorageByPlayDate($ticketStr, $playDate);

        if (empty($storageArr)) {
            return [];
        }

        $pidArr = array_flip($ticketArr);

        $result = [];

        foreach ($storageArr as $key => $val) {
            $result[$pidArr[$key]] = $val;
        }

        return $result;
    }

    /**
     * 获取时间段价格
     *
     * @param  int  $pid  产品ID
     * @param  string  $start_date  开始日期
     * @param  string  $end_date  截止日期
     *
     * @return array
     */
    public function getPriceSection($pid, $start_date = '', $end_date = '', $ptypes = [0])
    {
        $today         = date('Y-m-d');
        $priceModel    = new PriceRead();
        $price         = $priceModel->get_Dynamic_Price_Merge($pid, '', 0, $start_date, $end_date, 0, 1);
        $price_section = [];
        foreach ($price as $val) {
            if (in_array($val['ptype'], $ptypes) && $val['end_date'] >= $today) {
                $price_section[$val['id']] = [
                    'js'       => $val['n_price'],
                    'ls'       => $val['l_price'],
                    'id'       => $val['id'],
                    'ptype'    => $val['ptype'],
                    's_price'  => $val['s_price'],
                    'sdate'    => $val['start_date'],
                    'edate'    => $val['end_date'],
                    'storage'  => $val['storage'],
                    'weekdays' => $val['weekdays'],
                ];
            }
        }

        return $price_section;
    }

    /**
     * 获取产品供货价
     *
     * @param  int  $pid  productID
     * @param  string  $date  日期,2016-03-26
     *
     * @return mix
     */
    public function getSupplyPrice($pid, $date = '')
    {
        if (!$pid) {
            return false;
        }

        $date = $date ?: date('Y-m-d', time());
        $date = date('Y-m-d', strtotime($date));

        $result = $this->getMuchSupplyPrice([$pid], $date);

        return isset($result[$pid]) ? $result[$pid] : false;
    }

    /**
     * 一次性获取多产品的供货价
     * <AUTHOR>
     *
     * @param  array  $pid_arr  array(1,2)
     * @param  string  $date  日期
     *
     * @return array          array(1 => 0.01, 2 => 0.02)
     */
    public function getMuchSupplyPrice($pidArr, $date = '')
    {
        if (!$pidArr) {
            return [];
        }

        if (!$pidArr) {
            return [];
        }

        $mapping = $this->getTidArrByPid($pidArr);
        $mapping = array_flip($mapping);
        $tidArr  = array_keys($mapping);

        $priceApi = new \Business\JavaApi\Ticket\Price();
        $priceRes = $priceApi->batchBasePrice($tidArr, $date);
        if ($priceRes['code'] != 200) {
            return [];
        }

        $return = [];
        foreach ($priceRes['data'] as $key => $item) {
            $tid = $item['ticketId'];

            if ($item['costPrice'] != -1) {
                $return[$mapping[$tid]] = $item['costPrice'] / 100;
            }
        }

        return $return;
    }

    /**
     * 获取结算价格
     *
     * @param  int  $tid  门票tid
     * @param  int  $memberId  会员id
     * @param  int  $aid  上级供应商id
     * @param  string  $date  日期
     *
     * @return int | false
     */
    public function getSettlePrice($tid, $memberId, $aid, $date = '')
    {
        $date = $date ?: date('Y-m-d');

        $res = TicketApi::refreshPrice($memberId, $aid, $tid, $date);

        if (isset($res['settlement_price']) && $res['settlement_price'] != -1) {
            return $res['settlement_price'] / 100;
        } else {
            return false;
        }
    }

    /**
     * 获取时间段窗口价
     * <AUTHOR>
     * @date   2017-09-27
     *
     * @param  string  $start  2017-09-01
     * @param  string  $end  2017-09-10
     * @param  pid  $pid  门票pid
     *
     * @return array
     */
    public function getRangeUprice($start, $end, $pid)
    {
        if (!$start || !$end || !$pid) {
            return [];
        }

        $uprice = $this->getUprice($pid);

        if (!$uprice) {
            return [];
        }

        $uprice *= 100;

        $return = [];
        while ($start <= $end) {
            $return[$start] = $uprice;
            $start          = date('Y-m-d', strtotime($start) + 3600 * 24);
        }

        return $return;
    }

    /**
     * 获取多张门票的信息
     *
     * @param  array  $tidArr  门票id组成的数组,[1,2]
     * @param  string  $field  需要获取的字段
     *
     * @return array    门票列表
     */
    public function getTicketInfoMulti($tidArr, $field = '*')
    {
        if (!$tidArr) {
            return [];
        }

        $tField    = $field == '*' ? '' : $field;
        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, $tField);

        if (!$ticketArr) {
            return [];
        }

        $result   = [];
        $fieldArr = explode(',', strtr($tField, [' ' => '']));
        foreach ($ticketArr as $ticket) {
            if (count($fieldArr) == 1) {
                $result[] = $ticket['ticket'][$fieldArr[0]];
            } else if (count($fieldArr) <= 2) {
                $result[$ticket['ticket'][$fieldArr[0]]] = $ticket['ticket'][$fieldArr[1]];
            } else {
                $result[$ticket['ticket'][$fieldArr[0]]]                     = $ticket['ticket'];
                $result[$ticket['ticket'][$fieldArr[0]]]['dhour']            = $ticket['land_f']['dhour'];
                $result[$ticket['ticket'][$fieldArr[0]]]['buy_limit']        = $ticket['land_f']['buy_limit'];
                $result[$ticket['ticket'][$fieldArr[0]]]['buy_limit_date']   = $ticket['land_f']['buy_limit_date'];
                $result[$ticket['ticket'][$fieldArr[0]]]['buy_limit_num']    = $ticket['land_f']['buy_limit_num'];
                $result[$ticket['ticket'][$fieldArr[0]]]['tourist_info']     = $ticket['land_f']['tourist_info'];
                $result[$ticket['ticket'][$fieldArr[0]]]['age_limit_max']    = $ticket['land_f']['age_limit_max'];
                $result[$ticket['ticket'][$fieldArr[0]]]['age_limit_min']    = $ticket['land_f']['age_limit_min'];
                $result[$ticket['ticket'][$fieldArr[0]]]['more_credentials'] = $ticket['ext']['more_credentials'];
                $result[$ticket['ticket'][$fieldArr[0]]]['refund_before_early_time'] = $ticket['ext']['refund_before_early_time'];
                $result[$ticket['ticket'][$fieldArr[0]]]['refund_after_early_time'] = $ticket['ext']['refund_after_early_time'];
                $result[$ticket['ticket'][$fieldArr[0]]]['refund_num'] = $ticket['ext']['refund_num'];
                $result[$ticket['ticket'][$fieldArr[0]]]['effective_limit'] = $ticket['ext']['effective_limit'] ?? '';
                $result[$ticket['ticket'][$fieldArr[0]]]['refund_least_num'] = $ticket['ext']['refund_least_num'] ?? 0;
                $result[$ticket['ticket'][$fieldArr[0]]]['refund_after_time'] = $ticket['ext']['refund_after_time'] ?? 0;
                $result[$ticket['ticket'][$fieldArr[0]]]['ifprint_refund_rule'] = $ticket['ext']['ifprint_refund_rule'] ?? 1;   //默认值为1
            }
        }

        return $result ?: [];
    }

    /*
     * 获取多个票类的信息
     * <AUTHOR>
     *
     * @param  array    $tidArr 产品id组成的数组,[1,2]
     * @param  string   $field  需要获取的字段
     * @return array    产品列表
     */
    public function getTicketList($tidArr, $field = '*')
    {
        if (!$tidArr) {
            return [];
        }

        $res       = [];
        $javaApi   = new ticketBiz();
        $tField    = $field == '*' ? '' : $field;
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, $tField);
        if (!empty($ticketArr)) {
            foreach ($ticketArr as $ticket) {
                $res[$ticket['ticket']['id']]             = $ticket['ticket'];
                $res[$ticket['ticket']['id']]['ltitle']   = $ticket['land']['title'];
                $res[$ticket['ticket']['id']]['p_type']   = $ticket['land']['p_type'];
                $res[$ticket['ticket']['id']]['venus_id'] = $ticket['land']['venus_id'];
                $res[$ticket['ticket']['id']]['zone_id']  = $ticket['land_f']['zone_id'];
                $res[$ticket['ticket']['id']]['tourist_info']  = $ticket['land_f']['tourist_info'];

                $res[$ticket['ticket']['id']]['refund_before_early_time'] = $ticket['ext']['refund_before_early_time'] ?? 0;
                $res[$ticket['ticket']['id']]['refund_after_early_time']  = $ticket['ext']['refund_after_early_time'] ?? 0;
                $res[$ticket['ticket']['id']]['reserve_refund_rule']      = $ticket['ext']['reserve_refund_rule'] ?? -1;
                $res[$ticket['ticket']['id']]['refund_after_time']        = $ticket['ext']['refund_after_time'] ?? 0;
                $res[$ticket['ticket']['id']]['show_order_limit_time']    = $ticket['ext']['show_order_limit_time'] ?? '';
                $res[$ticket['ticket']['id']]['ifprint_refund_rule']      = $ticket['ext']['ifprint_refund_rule'] ?? 1;
            }
        }

        return $res;
    }

    /*
     * 获取多个产品的信息(不要调用这个方法了，直接调java的接口)
     * <AUTHOR>
     *
     * @param  array    $pidArr 产品id组成的数组,[1,2]
     * @param  string   $field  需要获取的字段
     * @return array    产品列表
     */
    public function getProductList($pidArr, $field = '*')
    {
        if (!$pidArr || !is_array($pidArr)) {
            return [];
        }
        $commodityProductBiz = new Product();
        $result              = $commodityProductBiz->getProductInfoByIds($pidArr);

        $res = [];
        foreach ($result as $item) {
            $res[$item['id']] = $item;
        }

        return $res;
    }

    /**
     * 获取有销售产品的景点列表
     * <AUTHOR>
     * @date   2016-08-11
     *
     * @param  int  $memberId  查询用户ID
     *
     * @return array
     */
    public function getLandList($memberId)
    {
        if (is_array($memberId)) {
            $aidArr = $memberId;
        } else {
            $aidArr = [$memberId];
        }

        $getEvoluteBiz = new \Business\Product\Get\Evolute();
        $tmp           = $getEvoluteBiz->getAllowSaleProductByAidArr($aidArr);

        //去掉重复数据
        $res  = [];
        $mark = [];
        foreach ($tmp as $item) {
            $id = $item['lid'];

            if (!isset($mark[$id])) {
                $mark[]   = $id;
                $res[$id] = [
                    'id'    => $item['lid'],
                    'title' => $item['title'],
                ];
            }
        }

        return $res;
    }

    /**
     * 获取某个用户的所有景点列表（直销，转分销,以及下架和断开分销的）
     * <AUTHOR>
     * @date   2017-7-24
     *
     * @params $memberId 用户ID
     * @params $keyWord  关键字
     * @params $type     1 查汉字 2 查英文
     */
    public function getLandListByMember($memberId, $keyWord = '', $type = 1)
    {
        if (is_array($memberId)) {
            $fidArr = $memberId;
        } else {
            $fidArr = [$memberId];
        }
        $getEvoluteBiz = new \Business\Product\Get\Evolute();
        $list          = $getEvoluteBiz->getEvoluteByFidStatus($fidArr, 0);

        $list = $list ?: [];

        $pidArr = array_column($list, 'pid');

        $tmp = [];
        if ($pidArr) {
            $landTitle = '';
            $letters   = '';
            if (!empty($keyWord) && $type == 1) {
                $landTitle = $keyWord;
            } elseif (!empty($keyWord) && $type == 2) {
                $letters = $keyWord;
            }
            $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
            $tmp                = $commodityTicketBiz->getTicketInfoByArrPidAndLandTitleOrLetter($pidArr, $landTitle,
                $letters);
        }

        //去掉重复数据
        $res  = [];
        $mark = [];
        foreach ($tmp as $item) {
            $id = $item['id'];

            if (!isset($mark[$id])) {
                $mark[]   = $id;
                $res[$id] = $item;
            }
        }

        return $res;
    }

    /**
     * 一次性获取多个门票的信息
     *
     * @param  mixed  $tidArr  门票id
     * @param  string  $field  字段,写成"id,title",可获取[id => title]的映射
     *
     * @return [type]
     */
    public function getMuchTicketInfo($tidArr, $field = '*')
    {
        if (!$tidArr) {
            return [];
        }

        $tField    = $field == '*' ? '' : $field;
        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, $tField);

        if (!$ticketArr) {
            return [];
        }

        $return   = [];
        $fieldArr = explode(',', strtr($tField, [' ' => '']));
        foreach ($ticketArr as $ticket) {
            if (count($fieldArr) == 1) {
                $return[] = $ticket['ticket'][$fieldArr[0]];
            } else if (count($fieldArr) <= 2) {
                $return[$ticket['ticket'][$fieldArr[0]]] = $ticket['ticket'][$fieldArr[1]];
            } else {
                $return[$ticket['ticket'][$fieldArr[0]]] = $ticket['ticket'];
                $return[$ticket['ticket'][$fieldArr[0]]]['is_agreement_ticket'] = $ticket['ext']['is_agreement_ticket'] ?? 0;
            }

        }

        return $return;
    }

    /**
     * 获取票的分区名称
     *
     * @param  [type] $tidArr [description]
     *
     * @return [type]         [description]
     */
    public function getZoneName($tidArr)
    {
        if (!$tidArr) {
            return [];
        }

        $javaApi   = new \Business\CommodityCenter\LandF();
        $zoneIdArr = $javaApi->queryLandFByLandIdAndTicketIdAndProductId($tidArr, [], [], 'tid, zone_id', true);

        $showBiz = new \Business\Product\Show();
        $nameArr = $showBiz->getZones(0, $zoneIdArr);

        $return = [];
        foreach ($zoneIdArr as $tid => $zoneId) {
            $return[$tid] = $nameArr[$zoneId];
        }

        return $return;
    }

    /*
     * 根据pid获取对应lid
     *
     * @param array $pidArr pid数组
     * @return ['pid' => tid]
     */
    public function getTidArrByPid($pidArr)
    {
        if (!$pidArr) {
            return [];
        }

        $javaApi   = new ticketBiz();
        $result    = $javaApi->queryTicketInfoByProductIds($pidArr, 'pid,id');
        $ticketArr = array_column($result, 'ticket');
        $mapping   = [];
        foreach ($ticketArr as $item) {
            $mapping[$item['pid']] = $item['id'];
        }

        return $mapping ?: [];

    }

    /**
     * 根据pid获取tid
     *
     * @authro wuhao
     * @date 2016-11-22
     *
     * @param  integer  $pid  pid
     *
     * @return int
     */
    public function getTid($pid)
    {
        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketIdsByProductIds([$pid]);
        $res       = $ticketArr[0];

        return $res;
    }

    /**
     * 获取票类id-名称映射
     *
     * <AUTHOR>
     * @date   2017-03-10
     *
     * @param  [type]     $tidArr tid数组
     *
     * @return array
     */
    public function getTtitleMapping($tidArr)
    {
        if (!$tidArr) {
            return [];
        }

        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
        $result    = [];
        if (!empty($ticketArr)) {
            foreach ($ticketArr as $ticket) {
                $result[$ticket['ticket']['id']] = $ticket['ticket']['title'];
            }
        }

        return $result ?: [];
    }

    /**
     * 根据产品ID获取门票信息
     * @date   2017-04-11
     * <AUTHOR>
     *
     * @param  array  $pidArr  产品ID数组
     * @param  string  $filed  查询字段
     *
     * @return array
     */
    public function getTicketsByPid(array $pidArr, $filed)
    {
        if (!$pidArr) {
            return false;
        }
        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByProductIds($pidArr, $filed);

        $mapping = [];
        if (!empty($ticketArr)) {
            $mapping = array_column($ticketArr, 'ticket');
        }

        return $mapping ?: [];
    }

    /**
     * 获取订单套票扩展表信息 注：修改票数调用 不支持订单分库
     * <AUTHOR>
     *
     * @param  string  $orderId  订单号
     * @param  string  $field  查询字段
     *
     * @return bool|mixed
     *
     * @deprecated 接口已废弃
     * @see \Model\Order\SubOrderQuery\SubOrderAddon::getPackInfoByOrderSingle()
     */
    public function getOrderAddonByOrderId($orderId, $field = 'ifpack,pack_order')
    {
        if (!$orderId || !$field) {
            return false;
        }

        $subOrderAddon = new SubOrderAddon();
        $result        = $subOrderAddon->getPackInfoByOrderSingle($orderId, $field);
        if (!$result) {
            return false;
        }

        return $result;
    }

    /**
     * 获取门票数据
     * @date   2017-05-09
     * <AUTHOR> Lan
     *
     * @param  string  $column  查询条件字段
     * @param  mixed  $columnValue  查询条件字段值
     * @param  string  $field  查询字段
     * @param  array  $map  额外查询条件
     *
     * @deprecated 接口已废弃
     *
     * @return array|bool
     */
    public function getTicketInfo($column, $columnValue, $field, $map = [], $type = false)
    {
        if (!$column || !$columnValue) {
            return false;
        }
        if (is_array($columnValue)) {
            $where = [$column => ['IN', $columnValue]];
        } else {
            $where = [$column => $columnValue];
        }

        if ($map) {
            $where = array_merge($where, $map);
        }

        if ($type) {
            $res = $this->table(MainTableConst::TABLE_JQ_TICKET)->field($field)->where($where)->find();
        } else {
            $res = $this->table(MainTableConst::TABLE_JQ_TICKET)->where($where)->getField($field, true);
        }

        return $res ?: [];
    }

    /**
     * 获取期票的开始时间
     * <AUTHOR>
     * @date   2017-09-04
     *
     * @param  int  $pid  门票pid
     *
     * @return false | string
     */
    public function getQipiaoStart($pid)
    {
        $ticket = $this->getTicketInfoByPid($pid, 'order_start');

        if ($ticket['order_start'] > 0) {
            $startDate = date('Y-m-d', strtotime($ticket['order_start']));

            return $startDate;
        }

        return false;
    }

    /**
     * 根据景区ID获取门票分组列表
     * <AUTHOR> Chen
     * @date   2017-09-29
     *
     * @param  array|int  $lid  景区ID
     *
     * @return array [
     *              'lid1'=>[['group_id1'=>'','group_name1'=>''],
     *                       ['group_id2'=>'','group_name2'=>'']]
     *              ]
     */
    public function getTicketGroupSetByLid($lid)
    {
        $map  = is_array($lid) ? ['lid' => ['in', $lid]] : ['lid' => $lid];
        $data = $this->table('pft_ticket_group_config')
                     ->where($map)
                     ->field('id as grp_id,lid,grp_name')
                     ->select();
        if (!$data) {
            return [];
        }

        $output = [];
        foreach ($data as $item) {
            $output[$item['lid']][] = ['group_id' => $item['grp_id'], 'group_name' => $item['grp_name']];
        }

        return $output;
    }

    public function getTicketGroupByLid($lid)
    {
        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketBylandIdAndPay([$lid], 'id,title');

        if (!$ticketArr) {
            $ticketTitles = [];
        }

        $ticketTitles = $ticketArr;
        $data         = $this->table('pft_ticket_group g')
                             ->join('pft_ticket_group_config c ON c.id=g.grp_id')
                             ->where(['g.lid' => $lid])
                             ->field('c.grp_name,g.grp_id as group_id,g.lid,g.tid,g.min_age as group_min_age,g.max_age as group_max_age')
                             ->select();

        if (!$data) {
            $data = [];
        }
        $output      = [];
        $ticketGroup = [];

        $TicketApi       = new \Business\JavaApi\Product\Ticket();
        $validTicketInfo = $TicketApi->queryListingTicketIdAndNameByItemId($lid, '', 0);

        $valid_ticket = [];
        if ($validTicketInfo['code'] == 200 && $validTicketInfo['data']) {
            $valid_ticket = array_column($validTicketInfo['data'], "id");
        }
        //$limit_ticket_property = $TicketApi->getTicketsLimit($valid_ticket);

        $attributApi     = new \Business\JavaApi\Ticket\TicketOrderAttr();
        $limitTicketInfo = $attributApi->batchQueryOrderLimitAttribute($valid_ticket);
        if ($limitTicketInfo['code'] != 200 || empty($limitTicketInfo['data'])) {
            $limit_ticket_property = [];
        } else {
            $limit_ticket_property = $limitTicketInfo['data'];
        }

        foreach ($data as $value) {
            $data_map[$value['tid']] = $value;
        }

        foreach (\SplFixedArray::fromArray($limit_ticket_property) as $value) {
            $ticketGroup[$value['id']]['group_max_age'] = $value['buy_limit_max_age'];
            $ticketGroup[$value['id']]['group_min_age'] = $value['buy_limit_min_age'];
        }

        foreach (\SplFixedArray::fromArray($ticketTitles) as $key => $value) {
            if (!in_array($value['id'], $valid_ticket)) {
                continue;
            }
            $ticketGroup[$value['id']]['title']    = $value['title'];
            $ticketGroup[$value['id']]['lid']      = $lid;
            $ticketGroup[$value['id']]['tid']      = $value['id'];
            $ticketGroup[$value['id']]['group_id'] = $data_map[$value['id']]['group_id'];
            $ticketGroup[$value['id']]['grp_name'] = $data_map[$value['id']]['grp_name'];
            if (!$ticketGroup[$value['id']]['group_max_age']) {
                $ticketGroup[$value['id']]['group_max_age'] = "";
            }
            if (!$ticketGroup[$value['id']]['group_min_age']) {
                $ticketGroup[$value['id']]['group_min_age'] = "";
            }
            $output[] = $ticketGroup[$value['id']];
        }

        return $output;
    }

    /**
     * 根据门票ID列表获取分组配置
     * <AUTHOR> Chen
     * @date   2017-09-29
     *
     * @param  array  $ticketIdList  门票ID列表
     *
     * @return array 如果存在数据，返回[
     * 'tid1'=>['group_id'=>'分组ID', 'group_min_age'=>'最小年龄','group_max_age'=>'最大年龄']
     * 'tid2'=>['group_id'=>'分组ID', 'group_min_age'=>'最小年龄','group_max_age'=>'最大年龄']
     * ]
     */
    public function getTicketGroupByTicketIdList(array $ticketIdList)
    {
        $data = $this->table('pft_ticket_group g')
                     ->where(['tid' => ['in', $ticketIdList]])
                     ->field('g.grp_id as group_id,g.tid,g.min_age as group_min_age,g.max_age as group_max_age')
                     ->select();
        if (!$data) {
            return [];
        }

        $output = [];
        foreach ($data as $item) {
            $tid = $item['tid'];
            unset($item['tid']);
            $output[$tid] = $item;
        }

        return $output;
    }

    /**
     * 保存门票分组的配置
     * <AUTHOR> Chen
     * @date 2017-09-29
     *
     * @param  int  $landId  景点ID
     * @param  string  $groupName  分组名称
     * @param  int  $groupId  分组ID
     *
     * @return mixed 返回id 或者false：分组已经存在
     */
    public function saveTicketGroupConfig($landId, $groupName, $groupId = 0)
    {
        $data      = ['lid' => $landId, 'grp_name' => $groupName];
        $groupFind = $this->table('pft_ticket_group_config')->where([
            "grp_name" => $groupName,
            "lid"      => $landId,
        ])->find();
        if ($groupFind) {
            return false;
        }
        if (!$groupId) {
            $lastId = $this->table('pft_ticket_group_config')
                           ->data($data)->add();
        } else {
            $lastId = $this->table('pft_ticket_group_config')
                           ->where(['id' => $groupId])
                           ->data($data)->save();
        }

        return $lastId;
    }

    /**
     * 保存门票分组配置
     * <AUTHOR> Chen
     * @date   2017-09-29
     *
     * @param  int  $landId  景区ID
     * @param  int  $ticketId  门票ID
     * @param  int  $groupId  分组ID
     * @param  int  $minAge  最小年龄
     * @param  int  $maxAge  最大年龄
     *
     * @return bool|mixed
     */
    public function saveTicketGroup($landId, $ticketId, $groupId, $minAge, $maxAge)
    {
        $data = [
            'lid'     => $landId,
            'tid'     => $ticketId,
            'grp_id'  => $groupId,
            'min_age' => $minAge,
            'max_age' => $maxAge,
        ];

        $id = $this->table('pft_ticket_group')
                   ->where(['tid' => $ticketId])->limit(1)
                   ->getField('id');
        //用户不想分组的话进行删除操作
        if ($groupId == -1 && $id) {
            return $id = $this->table('pft_ticket_group')->where(['id' => $id])
                              ->delete();
        }
        if ($id > 0) {
            $id = $this->table('pft_ticket_group')->where(['id' => $id])
                       ->data($data)->save();
        } else {
            $id = $this->table('pft_ticket_group')->data($data)->add();
        }

        return $id;
    }

    /**
     * 删除分组信息
     * <AUTHOR>
     * @DateTime 2017-09-30T18:14:17+0800
     *
     * @param    [type] $lid 景区id
     * @param    [type] $groupId 分组id
     *
     * @return   1 删除成功  0 删除失败  -1 分组存在票不能进行删除
     */
    public function deleteTicketGroup($lid, $groupId)
    {
        $groupTable      = "pft_ticket_group_config";
        $groupTicketFind = $this->checkTicketGroupHaveTicket($lid, $groupId);
        if ($groupTicketFind) {
            return -1;
        }
        $groupDelete = $this->table($groupTable)->where([
            "id"  => $groupId,
            "lid" => $lid,
        ])->delete();
        if ($groupDelete) {
            return 1;
        }

        return 0;
    }

    /**
     * 检查票分组是否有包含票
     * <AUTHOR>
     * @DateTime 2017-10-09T10:33:13+0800
     *
     * @param  string  $value  [description]
     *
     * @return   [type] [description]
     */
    public function checkTicketGroupHaveTicket($lid, $groupId)
    {
        $groutTicketFind = $this->table("pft_ticket_group")->where([
            "lid"    => $lid,
            "grp_id" => $groupId,
        ])->find();
        if ($groutTicketFind) {
            return true;
        }

        return false;
    }

    /**
     * 获取tid对应的lid
     * <AUTHOR>
     * @date   2018-05-22
     *
     * @param  array  $tidArr  tid数组
     *
     * @return array
     */
    public function getlidArrByTidArr($tidArr)
    {
        if (!$tidArr) {
            return [];
        }

        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,landid');
        $lidArr    = [];
        foreach ($ticketArr as $ticket) {
            $lidArr[$ticket['ticket']['id']] = $ticket['ticket']['landid'];
        }

        return $lidArr ?: [];
    }

    /**
     * 根据pid获取门票信息
     * <AUTHOR>
     * @date   2017-11-21
     *
     * @param  array  $pidArr  门票pid数组
     * @param  string  $field  字段
     *
     * @return array
     */
    public function getTicketInfoByPidArr($pidArr, $field = '*')
    {
        if (!$pidArr) {
            return [];
        }

        $tField = $field == '*' ? '' : $field;

        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByProductIds($pidArr, $tField);
        if (empty($ticketArr)) {
            return [];
        }

        $list = array_column($ticketArr, 'ticket');

        return $list ?: [];
    }

    /**
     * 根据tid获取门票信息
     * <AUTHOR>
     * @date   2019-1-21
     *
     * @param  array  $tidArr  门票ptd数组
     * @param  string  $field  字段
     *
     * @return array
     */
    public function getTicketInfoByTidArr($tidArr, $field = '*')
    {
        if (!$tidArr) {
            return [];
        }

        $tField    = $field == '*' ? '' : $field;
        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, $tField, '', '', '', true);
        $list      = array_column($ticketArr, 'ticket');

        return $list ?: [];
    }

    /**
     * 通过pid批量获取票类信息
     * Create by zhangyangzhen
     * Date: 2019/1/8
     * Time: 18:51
     *
     * @param $pidArr
     * @param  string  $field
     *
     * @return array
     */
    public function getTicketByPidArr($pidArr, $field = '*')
    {
        if (!$pidArr) {
            return [];
        }

        $tField    = $field == '*' ? '' : $field;
        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByProductIds($pidArr, $tField);
        $list      = array_column($ticketArr, 'ticket');

        return $list ?: [];
    }

    /**
     * 根据id批量获取门票信息
     */
    public function getTicketInfoByIdArr($idArr, $field = 'id')
    {
        if (empty($idArr) || !is_array($idArr)) {
            return [];
        }

        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByIds($idArr, $field);
        if (empty($ticketArr)) {
            return [];
        }

        $data = array_column($ticketArr, 'ticket');

        return is_array($data) ? $data : [];
    }

    /**
     * 根据产品编号获取产品信息
     * <AUTHOR>
     * @date   2018-07-25
     *
     * @param  intval  $salerid  产品编号
     * @param  string  $field  需要获取的字段
     * @param  bool  $multiterm  是否获取多条
     *
     * @return array
     */
    public function getLandBySalerid($salerid, $field = 'id,terminal', $multiterm = false)
    {
        if (!$salerid) {
            return [];
        }

        $landBiz = new Land();
        if ($multiterm) {
            $list = $landBiz->queryLandMultiQueryBySalerid($salerid);
        } else {
            $list = $landBiz->queryLandMultiQueryBySalerid([$salerid]);
            $list = $list[0];
        }

        return $list;
    }

    /**
     * ota下单查询member的数据
     * author  leafzl
     * Date: 2018-10-8
     *
     * @param $tid int 门票
     *
     * @return array|mixed
     */

    public function getOtaOrderInfo($tid)
    {
        if (!$tid) {
            return [];
        }

        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoById($tid, 'pid,title,pay,tprice', '', 'apply_did,id,title,p_type');
        $res       = [];
        if ($ticketArr) {
            $res = [
                'pid'       => $ticketArr['ticket']['pid'],
                'apply_did' => $ticketArr['land']['apply_did'],
                'lid'       => $ticketArr['land']['id'],
                'ltitle'    => $ticketArr['land']['title'],
                'p_type'    => $ticketArr['land']['p_type'],
                'title'     => $ticketArr['ticket']['title'],
                'pay'       => $ticketArr['ticket']['pay'],
                'tprice'    => $ticketArr['ticket']['tprice'],
            ];
        }

        return $res;
    }

    /**
     * java转分销产品列表获取数据结构转换兼容
     *
     * @param $list
     *
     * @return
     * $listNew = [
     *     'tid' => '',//门票id
     *     'landid' => '',//景区id
     *     'pid' => '',//产品id
     *     'title' => '',//产品名称
     *     'apply_did' => '', //最初的供应商id
     *     'apply_name' => '', //最初的供应商名称
     *     'status' => '',//状态 1：上架 2：下架
     *     'ttitle' => '',  //门票名称
     *  ];
     * Author: yangwx
     * Date: 2019/9/4 0004
     */
    public function handleEvoluteList($list)
    {
        if (!$list) {
            return [];
        }

        $listNew = [];
        foreach ($list as $key => $value) {
            foreach ($value['ticketList'] as $ticKey => $ticVal) {
                $listNew[] = [
                    'tid'        => $ticVal['tid'],
                    'landid'     => $ticVal['lid'],
                    'pid'        => $ticVal['pid'],
                    'title'      => $value['title'],
                    'apply_did'  => $value['sourceId'],
                    'apply_name' => $value['sourceName'],
                    'status'     => 1,
                    'ttitle'     => $ticVal['title'],
                ];
            }
        }

        return $listNew;
    }

    /**
     * 获取多个年卡产品名称
     *
     * @param  [type] $pid_arr [3026,3027]
     *
     * @return [type]          [description]
     */
    public function getCardName($pid_arr)
    {
        if (!$pid_arr) {
            return [];
        }

        $commodityProductBiz = new Product();
        $list                = $commodityProductBiz->getProductInfoByIds($pid_arr);

        $return = [];
        foreach ($list as $item) {
            $return[$item['id']] = $item['p_name'];
        }

        return $return;
    }
}
