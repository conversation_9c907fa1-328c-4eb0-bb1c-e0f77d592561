<?php
/**
 * 演出产品相关数据模型
 *
 * <AUTHOR>
 * @date 2018-01-26
 *
 */

namespace Model\Product;

use Library\Model;

class Show extends Model
{
    private $_pft_roundzone     = 'pft_roundzone';
    private $_pft_roundseat     = 'pft_roundseat';
    private $_pft_round         = 'pft_round';
    private $_pft_venues        = 'pft_venues';

    //演出场馆库存显示配置表
    private $_pft_show_stock_config = 'pft_venues_show_stock_config';

    //新的场次座位详细表
    private $_pft_seat_jit = 'pft_roundseat_jit';

    //新的场次座位详细表 - 三个月前的归档
    private $_pft_seat_jit_history = 'pft_roundseat_jit_history';

    //分区座位汇总表
    private $_pft_zone_seat = 'pft_round_zoneseats';

    //预留记录表
    private $_reserve_record = 'pft_round_reserve_record';

    //预留座位表
    private $_reserve_seats = 'pft_round_reserve_seats';

    //库存显示规则 1-实时显示  2-模糊显示:只在库存小于配置的数值时显示相关提示
    const SHOW_STOCK_REAL  = 1;
    const SHOW_STOCK_VAGUE = 2;

    public function __construct()
    {
        parent::__construct('pft_business_case');
    }

    /**
     * 新增场馆
     *
     * @param  int  $applyDid  供应商ID
     * @param  string  $venueName  场馆名
     * @param  string  $venueThumb  缩略图
     * @param  array  $zoneNameArr  分区数组
     * @param  array  $showStockRule  库存显示规则
     */
    public function addVenue($applyDid, $venueName, $venueThumb, $zoneNameArr, $showStockRule = [])
    {
        if (!$applyDid || !$venueName || !$venueThumb || !$zoneNameArr) {
            return false;
        }

        $data = [
            'apply_did'   => $applyDid,
            'venue_name'  => $venueName,
            'venue_thumb' => $venueThumb,
            'status'      => 0,
        ];

        $this->startTrans();

        $venueId = $this->table($this->_pft_venues)->add($data);
        if (!$venueId) {
            $this->rollback();

            return false;
        }

        $zoneArr = [];
        foreach ($zoneNameArr as $zoneName) {
            $zoneArr[] = ['venue_id' => $venueId, 'zone_name' => $zoneName];
        }
        $res = $this->table($this->_pft_roundzone)->addAll($zoneArr);

        if (!$res) {
            $this->rollback();

            return false;
        }

        //添加演出场馆库存显示规则
        if (is_array($showStockRule) && !empty($showStockRule)) {

            $showRule   = isset($showStockRule['show_rule']) ? $showStockRule['show_rule'] : self::SHOW_STOCK_REAL;
            $vagueShowA = isset($showStockRule['vague_show_a']) ? $showStockRule['vague_show_a'] : 0;
            $vagueShowB = isset($showStockRule['vague_show_b']) ? $showStockRule['vague_show_b'] : 0;

            if ($this->updateShowStockConfig($venueId, $showRule, $vagueShowA, $vagueShowB)) {
                $this->commit();

                return $venueId;
            }
        }
        $this->rollback();

        return false;
    }

    /**
     * 更新场馆库存显示配置
     *
     * @date   2019-01-16
     * <AUTHOR>
     *
     * @param  integer  $venueId  场馆id
     * @param  integer  $showRule  库存显示规则 1=实时显示 2=模糊显示
     * @param  integer  $vagueShowA  模糊显示时小于该数值显示余票紧张
     * @param  integer  $vagueShowB  模糊显示时小于该数值显示仅剩当前库存数
     *
     * @return boolean
     */
    public function updateShowStockConfig($venueId, $showRule, $vagueShowA = 0, $vagueShowB = 0)
    {
        if (!$venueId || !in_array($showRule, [self::SHOW_STOCK_VAGUE, self::SHOW_STOCK_REAL])) {
            return false;
        }

        $showConfig = [
            'venues_id'    => $venueId,
            'show_rule'    => $showRule,
            'vague_show_a' => $vagueShowA,
            'vague_show_b' => $vagueShowB,
            'create_time'  => time(),
        ];

        $result = $this->table($this->_pft_show_stock_config)->add($showConfig, [], true);

        return $result;
    }

    /**
     * 获取场馆库存显示配置
     *
     * @date    2019-01-16
     * <AUTHOR>
     *
     * @param  integer  $venueId  场馆id
     *
     * @return  array
     */
    public function getShowStockConfig($venueId, $field = "show_rule, vague_show_a, vague_show_b")
    {
        $where  = ['venues_id' => (int)$venueId];
        $config = $this->table($this->_pft_show_stock_config)->where($where)->field($field)->find();

        if (!$config) {
            //默认配置
            $config = $this->defaultShowStockConfig();
        }

        return $config;
    }

    /**
     * 场馆库存默认显示配置:实时显示
     *
     * @date   2019-01-16
     * <AUTHOR>
     *
     * @return array
     */
    public function defaultShowStockConfig()
    {
        return [
            'show_rule'    => self::SHOW_STOCK_REAL,
            'vague_show_a' => 0,
            'vague_show_b' => 0,
        ];
    }

    /**
     * 更新场馆信息
     *
     * @param  int  $venueId
     * @param  string  $venueName
     * @param  string  $venueThumb
     * @param  array  $newZoneNameArr  新增分区
     * @param  array  $updateZoneArr  更新的分区
     * @param  array  $delZoneIdArr  需要删除的分区
     *
     * @return boolean
     */
    public function updateVenue($venueId, $venueName, $venueThumb, $newZoneNameArr = [], $updateZoneArr = [], $delZoneIdArr = [], $showStockRule = [])
    {
        $where = [
            'id' => $venueId,
        ];
        $data  = [
            'venue_name'  => $venueName,
            'venue_thumb' => $venueThumb,
        ];

        $this->startTrans();

        $res = $this->table($this->_pft_venues)->where($where)->save($data);
        if ($res === false) {
            $this->rollback();

            return false;
        }

        if ($delZoneIdArr) {
            $delWhere = ['id' => ['in', $delZoneIdArr]];
            $res      = $this->table($this->_pft_roundzone)->where($delWhere)->save(['state' => 1]);

            if ($res === false) {
                $this->rollback();

                return false;
            }
        }

        if ($updateZoneArr) {
            foreach ($updateZoneArr as $item) {
                if ($item['zone_id'] && $item['zone_name']) {
                    $res = $this->table($this->_pft_roundzone)->where(['id' => $item['zone_id']])->save(['zone_name' => $item['zone_name']]);

                    if ($res === false) {
                        $this->rollback();

                        return false;
                    }
                }
            }
        }

        if ($newZoneNameArr) {
            //将重复的去除
            $existZone = $this->table($this->_pft_roundzone)->where([
                'zone_name' => [
                    'in',
                    $newZoneNameArr,
                ],
                'venue_id' => $venueId,
                'state'    => 0,
            ])->getField('zone_name', true);
            $existZone = $existZone ? $existZone : [];

            $leftZoneName = array_diff($newZoneNameArr, $existZone);
            if ($leftZoneName) {
                $addArr = [];
                foreach ($leftZoneName as $addZoneName) {
                    $addArr[] = ['venue_id' => $venueId, 'zone_name' => $addZoneName];
                }

                $res = $this->table($this->_pft_roundzone)->addAll($addArr);
                if ($res === false) {
                    $this->rollback();

                    return false;
                }
            }
        }

        //更新演出场馆库存显示规则
        if (is_array($showStockRule) && !empty($showStockRule)) {

            $showRule   = isset($showStockRule['show_rule']) ? $showStockRule['show_rule'] : self::SHOW_STOCK_REAL;
            $vagueShowA = isset($showStockRule['vague_show_a']) ? $showStockRule['vague_show_a'] : 0;
            $vagueShowB = isset($showStockRule['vague_show_b']) ? $showStockRule['vague_show_b'] : 0;

            if (!$this->updateShowStockConfig($venueId, $showRule, $vagueShowA, $vagueShowB)) {
                $this->rollback();

                return false;
            }
        }

        $this->commit();

        return true;
    }

    /**
     * 设置场馆座位信息
     *
     * @param  int  $venueId  场馆ID
     * @param  int  $addSeatArr  需要批量新增的座位
     * @param  int  $updateSeatArr  需要更新的座位
     * @param  int  $delSeatIdArr  需要删除的座位ID数组
     * @param  int  $changeZoneColorArr  需要更新分区颜色的数组
     * @param  int  $rowNum  座位行数
     * @param  int  $colNum  座位列数
     * @param  int  $layout  座位排序方式： 0=倒序 1=奇数在左 2=奇数在右
     * @param  int  $rowHead  列编号 - 上到下
     * @param  int  $colHead  行编号 - 左到右
     */
    public function setVenueSeat($venueId, $addSeatArr, $updateSeatArr, $delSeatIdArr, $changeZoneColorArr = [], $rowNum = false, $colNum = false, $layout = false, $rowHead = false, $colHead = false)
    {
        //开启事务
        $this->startTrans();

        //第一次初始化场馆基础信息
        if ($rowNum && $colNum) {
            $where = ['id' => $venueId];
            $data  = [
                'row_num'  => $rowNum,
                'col_num'  => $colNum,
                'layout'   => $layout,
                'col_head' => $colHead,
                'col_side' => $rowHead,
            ];

            $res = $this->table($this->_pft_venues)->where($where)->save($data);
            if ($res === false) {
                $this->rollback();

                return false;
            }
        }

        if ($addSeatArr) {
            $res = $this->table($this->_pft_roundseat)->addAll($addSeatArr);
            if (!$res) {
                $this->rollback();

                return false;
            }
        }

        if ($delSeatIdArr) {
            $where = ['id' => ['in', $delSeatIdArr]];
            $data  = ['seat_status' => 1];

            $res = $this->table($this->_pft_roundseat)->where($where)->save($data);
            if ($res === false) {
                $this->rollback();

                return false;
            }
        }

        if ($updateSeatArr) {
            foreach ($updateSeatArr as $item) {
                $where = ['id' => $item['id']];
                $data  = $item['data'];

                $res = $this->table($this->_pft_roundseat)->where($where)->save($data);
                if ($res === false) {
                    $this->rollback();

                    return false;
                }
            }
        }

        //更新分区颜色
        if ($changeZoneColorArr) {
            foreach ($changeZoneColorArr as $zoneId => $zoneColor) {
                $res = $this->table($this->_pft_roundzone)->where(['id' => $zoneId])->save(['color' => $zoneColor]);
                if ($res === false) {
                    $this->rollback();

                    return false;
                }
            }
        }

        $this->commit();

        return true;
    }

    /**
     * 获取供应商的场馆列表
     * <AUTHOR>
     * @date   2018-01-12
     *
     * @param  int  $applyDid
     * @param  int  $status
     *
     * @return array
     */
    public function getVenueList($applyDid, $status = 0, $field = 'id as venue_id,venue_name,venue_thumb')
    {
        if (!$applyDid) {
            return [];
        }

        $where = [
            'apply_did' => $applyDid,
            'status'    => $status,
        ];

        $res = $this->table($this->_pft_venues)->where($where)->field($field)->select();

        return $res ? $res : [];
    }

    /**
     * 管理员获取具体场馆列表
     * <AUTHOR>
     * @date   2018-01-12
     *
     * @param  int  $applyDid
     * @param  int  $status
     *
     * @return array
     */
    public function getAdminVenueList($venueId, $status = 0)
    {
        if (!$venueId) {
            return [];
        }

        $field = 'id as venue_id,venue_name,venue_thumb';
        $where = [
            'id'     => $venueId,
            'status' => $status,
        ];

        $res = $this->table($this->_pft_venues)->where($where)->field($field)->select();

        return $res ? $res : [];
    }

    /**
     * 获取分区列表
     * <AUTHOR>
     * @date   2018-01-12
     *
     * @param  int  $venueId  场馆ID
     * @param  int  $zoneIds  场次列表
     *
     * @return array
     */
    public function getZoneList($venueId = 0, $zoneIds = [], $stateArr = [0])
    {
        if (!$venueId && !$zoneIds) {
            return [];
        }

        $field = 'id zone_id,zone_name, venue_id, hot_spot_info, color, floor';
        if ($zoneIds) {
            if (is_array($zoneIds)) {
                $where = ['id' => ['in', $zoneIds]];
            } else {
                $where = ['id' => $zoneIds];
            }
        } else {
            $where = ['venue_id' => $venueId];
        }
        $where['state'] = ['in', $stateArr];

        $res = $this->table($this->_pft_roundzone)->field($field)->where($where)->select();

        return $res ? $res : [];
    }

    /**
     * 设置场馆状态
     * <AUTHOR>
     * @date   2018-01-12
     *
     * @param  string  $status  操作 delete=关闭，open=开启
     */
    public function setVenue($venueId, $status = 'delete')
    {
        if (!$venueId) {
            return false;
        }

        $where = ['id' => $venueId];
        $data  = ['status' => 0];
        if ($status == 'delete') {
            $data['status'] = 1;
        }

        $res = $this->table($this->_pft_venues)->where($where)->save($data);

        return $res === false ? false : true;
    }

    /**
     * 场馆名称是否存在
     * <AUTHOR>
     * @date   2018-01-12
     *
     * @param  int  $applyDid
     * @param  string  $venueName
     *
     * @return bool
     */
    public function isVenueExist($applyDid, $venueName)
    {
        if (!$applyDid || !$venueName) {
            return false;
        }

        $where = [
            'apply_did'  => $applyDid,
            'venue_name' => $venueName,
            'status'     => 0,
        ];

        $res = $this->table($this->_pft_venues)->where($where)->find();

        return $res ? true : false;
    }

    /**
     * 判断指定日期是否已经存在场次
     *
     * <AUTHOR>
     * @date   2018-01-19
     *
     * @param  int  $venueId
     * @param  array  $dateArr
     *
     * @return bool
     */
    public function isDateExistRound($venueId, $dateArr)
    {
        if (!$venueId || !$dateArr || !is_array($dateArr)) {
            return false;
        }

        $where = [
            'venus_id' => $venueId,
            'use_date' => ['in', $dateArr],
            'status'   => 0,
        ];

        $res = $this->table($this->_pft_round)->where($where)->field('id, use_date')->find();
        if ($res) {
            return $res;
        } else {
            return false;
        }
    }

    /**
     * 判断场馆是否可以删除
     * 如果已经生成过场次就不能删除
     *
     * <AUTHOR>
     * @date   2018-02-23
     *
     * @param  int  $venueId
     *
     * @return bool
     */
    public function isVenueExistRound($venueId)
    {
        if (!$venueId) {
            return false;
        }

        $where = ['venus_id' => $venueId, 'status' => 0];
        $order = 'id desc';

        $roundInfo = $this->table($this->_pft_round)->where($where)->order($order)->find();

        return $roundInfo ? $roundInfo : [];
    }

    /**
     * 统一批量创建场次
     * <AUTHOR>
     * @date   2018-01-22
     *
     * @param  int  $venueId  场馆ID
     * @param  int  $opId  操作用户
     * @param  array  $dates  日期
     * @param  array  $roundData  添加的场次数据
     *
     * @return bool
     */
    public function createRound($venueId, $opId, $dates, $roundData)
    {
        if (!$venueId || !$opId || !$roundData || !$dates) {
            return false;
        }

        //数据批量按天插入
        $this->startTrans();

        foreach ($dates as $date) {
            $inserData = [];
            foreach ($roundData as $round) {
                $inserData[] = [
                    'venus_id'        => $venueId,
                    'status'          => 0,
                    'opid'            => $opId,
                    'use_date'        => $date,
                    'publish_time'    => date('Y-m-d H:i:s'),
                    'ext_handle_time' => 0,
                    'round_name'      => $round['round_name'],
                    'bt'              => $round['bt'],
                    'et'              => $round['et'],
                    'pre_sale'        => $round['pre_sale'],
                    'round_sort_id'   => $round['round_sort_id'],
                    'is_new'          => $round['is_new'],
                    'round_mode'      => $round['round_mode'] ?? 0,
                ];
            }

            if ($inserData) {
                $res = $this->table($this->_pft_round)->addAll($inserData);

                if (!$res) {
                    $this->rollback();

                    return false;
                }
            }
        }

        $this->commit();

        return true;
    }

    /**
     * 场次编辑
     * <AUTHOR>
     * @date   2018-01-22
     *
     * @param  int  $venueId
     * @param  int  $memberId
     * @param  string  $date
     * @param  array  $addData
     * @param  array  $updateData
     *
     * @return bool
     */
    public function updateRound($venueId, $memberId, $date, $addData, $updateData)
    {
        if (!$venueId || !$memberId || !$date || (!$addData && !$updateData)) {
            return false;
        }

        $this->startTrans();

        if ($addData) {
            $inserData = [];
            foreach ($addData as $round) {
                $inserData[] = [
                    'venus_id'        => $venueId,
                    'status'          => 0,
                    'opid'            => $memberId,
                    'publish_time'    => date('Y-m-d H:i:s'),
                    'ext_handle_time' => 0,
                    'round_name'      => $round['round_name'],
                    'bt'              => $round['bt'],
                    'et'              => $round['et'],
                    'use_date'        => $date,
                    'pre_sale'        => $round['pre_sale'],
                    'round_sort_id'   => $round['round_sort_id'],
                    'is_new'          => $round['is_new'],
                    'round_mode'      => $round['round_mode'] ?? 0,
                ];
            }

            $res = $this->table($this->_pft_round)->addAll($inserData);
            if (!$res) {
                $this->rollback();

                return false;
            }
        }

        if ($updateData) {
            foreach ($updateData as $item) {
                if (isset($item['id']) && isset($item['data'])) {
                    $where   = ['id' => $item['id']];
                    $tmpData = $item['data'];
                    $data    = [
                        'round_name'    => $tmpData['round_name'],
                        'bt'            => $tmpData['bt'],
                        'et'            => $tmpData['et'],
                        'pre_sale'      => $tmpData['pre_sale'],
                        'round_sort_id' => $tmpData['round_sort_id'],
                    ];

                    $res = $this->table($this->_pft_round)->where($where)->save($data);
                    if ($res === false) {
                        $this->rollback();

                        return false;
                    }
                }
            }
        }

        $this->commit();

        return true;
    }

    /**
     * 刪除场次
     * <AUTHOR>
     * @date   2018-01-22
     *
     * @param  int  $roundId
     *
     * @return bool
     */
    public function delRound($roundId)
    {
        if (!$roundId) {
            return false;
        }

        $where = ['id' => $roundId];
        //$res   = $this->table($this->_pft_round)->where($where)->delete();
        $res = $this->table($this->_pft_round)->where($where)->data(['status' => 1])->save();

        return $res === false ? false : true;
    }


    /**
     * 作废场次
     *
     * @param  int  $roundId  场次id
     *
     * @return bool
     */
    public function voidedRound($roundId)
    {
        if (!$roundId) {
            return false;
        }

        $where = ['id' => $roundId];
        $res = $this->table($this->_pft_round)->where($where)->data(['status' => 3])->save();

        return $res === false ? false : true;
    }

    /**
     * 场次名称是否已经同步
     * <AUTHOR>
     * @date   2018-02-24
     *
     * @param  array  $roundNameList
     * @param  array  $useDateList
     *
     * @return bool
     */
    public function isRoundSync($venueId, $roundNameArr, $useDateList)
    {
        $where = [
            'venus_id'   => $venueId,
            'round_name' => ['in', $roundNameArr],
            'use_date'   => ['in', $useDateList],
            'status'     => 0,
        ];
        $order = 'id desc';

        $roundInfo = $this->table($this->_pft_round)->where($where)->order($order)->field('use_date, round_name')->find();

        return $roundInfo ? $roundInfo : false;
    }

    /**
     * 场次同步
     * <AUTHOR>
     * @date   2018-01-22
     *
     * @param  int  $venueId  场馆ID
     * @param  int  $opId  操作用户
     * @param  date  $syncDate  用来同步的日期
     * @param  array  $targetDateArr  需要同步数据的日期数组
     *
     * @return bool
     */
    public function syncRound($venueId, $opId, $syncDate, $targetDateArr, $isOpen = false, $roundMode = 0)
    {
        if (!$venueId || !$opId || !$syncDate || !$targetDateArr) {
            return false;
        }

        //获取已经设置的场次数据
        $field     = 'round_name, bt, et, pre_sale, round_sort_id';
        $roundList = $this->getRoundList($venueId, $field, $status = 0, $syncDate, $syncDate);

        if (!$roundList) {
            return false;
        }

        $this->startTrans();

        foreach ($targetDateArr as $date) {
            $inserData = [];
            foreach ($roundList as $round) {
                $inserData[] = [
                    'venus_id'        => $venueId,
                    'status'          => 0,
                    'opid'            => $opId,
                    'use_date'        => $date,
                    'publish_time'    => date('Y-m-d H:i:s'),
                    'ext_handle_time' => 0,
                    'round_name'      => $round['round_name'],
                    'bt'              => $round['bt'],
                    'et'              => $round['et'],
                    'pre_sale'        => $round['pre_sale'],
                    'round_sort_id'   => $round['round_sort_id'],
                    'is_new'          => !$isOpen ? 0 : 1,
                    'round_mode'      => $round['round_mode'] ?? $roundMode,
                ];
            }

            $res = $this->table($this->_pft_round)->addAll($inserData);
            if (!$res) {
                $this->rollback();

                return false;
            }
        }

        $this->commit();

        return true;
    }

    /**
     * 添加座位预留记录
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  int  $venudId  场馆ID
     * @param  int  $roundId  场次ID
     * @param  int  $zoneId  分区ID
     * @param  array  $seatList  座位列表
     * @param  int  $type  类型：1=默认的预留记录 2=售票员添加的记录
     * @param  array  $contactInfo  预留联系人相关的信息
     *           {
     *              'name' : '联系人姓名',
     *              'mobile' : '联系人手机',
     *              'desc' : '预留备注',
     *              'op_id' : '预留操作人',
     *           }
     */
    public function addReserveRecord($venudId, $roundId, $zoneId, $seatList, $type = 1, $contactInfo = [])
    {
        if (!$venudId || !$roundId || !$zoneId || !$seatList || !is_array($seatList)) {
            return false;
        }

        //过滤重复的座位
        $seatList = array_unique($seatList);

        //获取场次的版本
        $roundInfo = $this->getRoundInfo($roundId, 'version, use_date, is_new');
        if (!$roundInfo) {
            return false;
        }
        $version = $roundInfo['version'];
        $useDate = $roundInfo['use_date'];

        $this->startTrans();

        //首先将相应的座位设置为预留
        $where = [
            'round_id' => $roundId,
            'seat_id'  => ['in', $seatList],
            'status'   => ['in', [0, 4]],
        ];
        $data  = [
            'status' => 1,
        ];

        $changeNum = $this->table($this->_pft_seat_jit)->where($where)->save($data);

        if ($changeNum != count($seatList)) {
            $this->rollback();

            return false;
        }

        //添加相应的预留记录
        $res = $this->_insertReserveDetail($venudId, $roundId, $zoneId, $seatList, $useDate, $type, $contactInfo);

        if (!$res) {
            $this->rollback();

            return false;
        }

        //重新计算剩余座位
        $res = $this->_updateRoundCollect($roundId, $version, $zoneId, count($seatList), 'reserve');
        if (!$res) {
            $this->rollback();

            return false;
        }

        $this->commit();

        return true;
    }

    /**
     * 释放预留的座位
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @return
     */
    public function releaseReserveRecord($recordId, $roundId, $zoneId, $seatList)
    {
        if (!$recordId || !$roundId || !$zoneId || !$seatList || !is_array($seatList)) {
            return false;
        }

        //过滤重复的座位
        $seatList = array_unique($seatList);

        //获取场次的版本
        $roundInfo = $this->getRoundInfo($roundId, 'version, is_new');
        if (!$roundInfo) {
            return false;
        }
        $version = $roundInfo['version'];

        $this->startTrans();

        //修改相应的座位
        $where = [
            'round_id' => $roundId,
            'seat_id'  => ['in', $seatList],
            'status'   => 1,
        ];
        $data  = [
            'status' => 4,
        ];

        $changeNum = $this->table($this->_pft_seat_jit)->where($where)->save($data);
        if ($changeNum != count($seatList)) {
            $this->rollback();

            return false;
        }

        //更新预留相关记录
        $res = $this->_updateReserveDetail($recordId, $roundId, $zoneId, $seatList, 'release');
        if (!$res) {
            $this->rollback();

            return false;
        }

        //还需要更新汇总数据
        $res = $this->_updateRoundCollect($roundId, $version, $zoneId, count($seatList), $type = 'release_reserve');
        if (!$res) {
            $this->rollback();

            return false;
        }

        $this->commit();

        return true;
    }

    /**
     * 判断座位是否是预留的
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  int  $roundId
     * @param  array  $seatIdList
     *
     * @return bool
     */
    public function isSeatReserve($roundId, $seatIdList, $status = false)
    {
        if (!$roundId || !$seatIdList) {
            return -1;
        }
        $where = [
            'round_id' => $roundId,
            'seat_id'  => ['in', $seatIdList],
        ];

        if ($status !== false) {
            if (is_array($status)) {
                $where['reserve_status'] = ['in', $status];
            } else {
                $where['reserve_status'] = $status;
            }
        }

        $res = $this->table($this->_reserve_seats)->where($where)->field('id')->find();

        if ($res === false) {
            return -1;
        } else if ($res) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 预留的座位是否都是可用的
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  int  $roundId
     * @param  array  $seatIdList
     *
     * @return bool
     */
    public function isZoneReserveEnough($roundId, $seatIdList)
    {
        if (!$roundId || !$seatIdList) {
            return false;
        }

        //只要判断这几个位置都是预留的就可以了
        $where = [
            'round_id'       => $roundId,
            'seat_id'        => ['in', $seatIdList],
            'reserve_status' => 1,
        ];

        $count = $this->table($this->_reserve_seats)->where($where)->count();
        if ($count >= count($seatIdList)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 通过场次和座位获取相应的预留记录
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  int  $roundId  场次ID
     * @param  int  $seatIdList  座位列表
     *
     * @return array
     */
    public function getReserveRecordInfoBySeats($roundId, $seatIdList)
    {
        if (!$roundId || !$seatIdList) {
            return [];
        }

        //获取记录
        $field = 'record_id, reserve_status';
        $where = [
            'round_id' => $roundId,
            'seat_id'  => ['in', $seatIdList],
        ];

        $info = $this->table($this->_reserve_seats)->where($where)->field($field)->find();

        return $info ? $info : [];
    }

    /**
     * 获取预留记录信息
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  int  $recordId
     *
     * @return array
     */
    public function getReserveRecordInfo($recordId)
    {
        if (!$recordId) {
            return [];
        }

        $info = $this->table($this->_reserve_record)->where(['id' => $recordId])->find();

        return $info ? $info : [];
    }

    /**
     * 获取默认的预留记录
     * <AUTHOR>
     * @date   2018-02-06
     *
     * @param  int  $roundId
     * @param  int  $zoneId
     *
     * @return array
     */
    public function getRecordInfoByRound($roundId, $zoneId)
    {
        if (!$roundId || !$zoneId) {
            return false;
        }

        $where = [
            'round_id' => $roundId,
            'zone_id'  => $zoneId,
            'type'     => 1,
        ];

        $info = $this->table($this->_reserve_record)->where($where)->find();

        return $info ? $info : [];
    }

    /**
     * 获取预留记录列表
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  int  $venueId  场馆ID
     * @param  string  $useDate  使用日期
     * @param  int  $page  第几页
     * @param  int  $size  条数
     * @param  int  $roundId  场次ID
     * @param  string  $contactName  联系人
     * @param  string  $contactMobile  联系人手机
     * @param  string  $ticketType  unuse=未出票，using=已经出票, used=已经完全出票，all=所有状态
     */
    public function getReserveRecordList($venueId, $useDate, $page = 1, $size = 20, $roundId = 0, $contactName = '', $contactMobile = '', $ticketType = false)
    {
        if (!$venueId || !$useDate) {
            return ['list' => [], 'total' => 0];
        }

        $order = "type asc, add_time desc"; //这个是为了把默认的预留记录显示在第一条
        $table = "{$this->_reserve_record} record";
        $join  = [
            "left join {$this->_pft_round} round on round.id=record.round_id",
            "left join {$this->_pft_roundzone} zone on zone.id = record.zone_id",
        ];
        $field = "record.venue_id, record.zone_id, record.status, record.id, record.round_id, record.contact_name, record.contact_mobile, record.desc, record.op_id, record.type, record.add_time, round.round_name, round.bt, round.et, zone.zone_name";
        $page  = "{$page},{$size}";

        $where = [
            'record.venue_id' => $venueId,
            'record.use_date' => $useDate,
        ];

        if ($roundId > 0) {
            $where['record.round_id'] = $roundId;
        }

        if ($contactName) {
            $where['record.contact_name'] = $contactName;
        }

        if ($contactMobile) {
            $where['record.contact_mobile'] = $contactMobile;
        }

        $ticketArr = [
            'unuse' => 0,
            'used'  => 2,
            'using' => ['in', [1, 0]], //未出票和部分出票
            'all'   => ['in', [1, 2, 0]], //所有状态
        ];

        if ($ticketType != false && in_array($ticketType, $ticketArr)) {
            $where['record.status'] = $ticketArr[$ticketType];
        }

        $list = $this->table($table)->where($where)->field($field)
                     ->join($join)->page($page)->order($order)->select();

        $list  = $list ? $list : [];
        $total = $this->table($table)->where($where)->field($field)->count();

        return ['list' => $list, 'total' => $total];
    }

    /**
     * 获取座位信息
     * 如果是 默认的预留记录的话，只获取还没有使用的位置
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  int  $recordId
     *
     * @return array
     */
    public function getReserveSeats($recordId, $type = 1)
    {
        if (!$recordId) {
            return [];
        }

        $where = [
            'record_id' => $recordId,
        ];

        if ($type == 1) {
            //只获取还可以使用的
            $where['reserve_status'] = 1;
        }

        $table = "{$this->_reserve_seats} reserve";
        $join  = "{$this->_pft_roundseat} seat on seat.id = reserve.seat_id";
        $field = "reserve.reserve_status, reserve.seat_id, seat.custom_pos, seat.custom_num, seat.col_num, seat.row_num";

        $list = $this->table($table)->where($where)->join($join, 'left')->field($field)->select();

        return $list ? $list : [];
    }

    /**
     * 获取场次信息
     *
     * @param  int  $round_id  场次ID
     *
     * @return array
     */
    public function getRoundInfo($roundId, $field = '*')
    {
        $data = $this->table('pft_round')->field($field)->find($roundId);
        if ($data != false) {
            return $data;
        } else {
            return false;
        }
    }

    /**
     * 获取场馆分区座位的数量
     * <AUTHOR>
     * @date   2018-02-04
     *
     * @return int
     */
    public function getZoneSeatsNums($venusId, $areaId, $status = 0)
    {
        //获取总的座位数
        $where    = array(
            'venue_id'    => $venusId,
            'zone_id'     => $areaId,
            'seat_status' => $status,
        );
        $allSeats = $this->table($this->_pft_roundseat)->where($where)->count();
        $allSeats = $allSeats === false ? 0 : intval($allSeats);

        return $allSeats;
    }

    /**
     * 获取剩余的座位列表
     *
     * <AUTHOR>
     * @date   2018-02-24
     *
     * @param  int  $roundId  场次ID
     * @param  int  $zoneId  分区ID
     *
     * @return array
     */
    public function sortZoneLeftSeats($roundId, $zoneId)
    {
        //获取剩余的可售座位
        $availableSeats = $this->getZoneLeftSeats($roundId, $zoneId);

        //初步整理座位数据
        $rowData      = [];
        $priorityMark = false;

        foreach ($availableSeats as $item) {
            $rowId = intval($item['row_id']);

            if (!isset($rowData[$rowId])) {
                $rowData[$rowId] = [];
            }

            //没有优先级的座位数据
            $rowData[$rowId][] = $item;

            if ($item['priority'] > 0) {
                $priorityMark = true;
            }
        }

        //没有优先级的座位数据进行排序
        foreach ($rowData as $rowId => $rowList) {
            usort($rowList, function ($a, $b) {
                if ($a['col_id'] == $b['col_id']) {
                    return 0;
                }

                return ($a['col_id'] > $b['col_id']) ? 1 : -1;
            });
            $rowData[$rowId] = $rowList;
        }

        //将行数据进行排序
        ksort($rowData);

        //返回排序后的座位信息
        $sortList = [];
        foreach ($rowData as $row) {
            $sortList = array_merge($sortList, $row);
        }

        //如果有优先级的话，将座位按优先级排序
        if ($priorityMark) {
            usort($sortList, function ($a, $b) {
                // 如果优先级一样的话那么根据排数座位号排序
                if (($a['priority'] == 0 && $b['priority'] == 0) || ($a['priority'] == $b['priority'])) {
                    if ($a['row_id'] == $b['row_id']) {
                        if ($a['col_id'] == $b['col_id']) {
                            return 0;
                        } else {
                            return ($a['col_id'] > $b['col_id']) ? 1 : -1;
                        }
                    } else {
                        return ($a['row_id'] > $b['row_id']) ? 1 : -1;
                    }
                } else {

                    if ($a['priority'] == 0) {
                        return 1;
                    } elseif ($b['priority'] == 0) {
                        return -1;
                    } else  {
                        return $a['priority'] > $b['priority'] ? 1 : -1;
                    }
                }

            });
        }

        //现在这边的逻辑是同一排里面有多个优先级，如果相同优先级有连坐就会使用连坐的位置，这样会出现低优先级的座位先出了
        //所以，这边需要按优先顺序，然后按排进行排序后再进行选座，这样就可以先进行高优先级的连坐判断，然后才进行低优先级连坐的判断

        //获取按优先级排序后的座位的优先座位
        $priorityList = []; //使用排号和优先级作为key，用来进行后续连坐的排序

        foreach ($sortList as $item) {
            $rowId = intval($item['row_id']);

            if ($item['priority'] > 0) {
                $priority  = intval($item['priority']);
                $tmpPriKey = $rowId . '_' . $priority;

                if (!isset($priorityList[$tmpPriKey])) {
                    $priorityList[$tmpPriKey] = ['key' => $tmpPriKey, 'list' => []];
                }

                $priorityList[$tmpPriKey]['list'][] = $item;
            }
        }

        //返回
        return ['row_seat' => $rowData, 'sort_seat' => $sortList, 'priority' => $priorityList];
    }

    /**
     * 获取分区剩余可用的座位
     * <AUTHOR>
     * @date   2018-01-28
     *
     * @param  int  $roundId  场次ID
     * @param  int  $zoneId  分区ID
     *
     * @return array
     */
    public function getZoneLeftSeats($roundId, $zoneId, $statusArr = [0, 4], $seatStatus = [])
    {
        if (!$roundId || !$zoneId) {
            return [];
        }

        $table = "{$this->_pft_seat_jit} jit";
        $field = 'jit.id, jit.priority, jit.status, row_id, col_id, custom_num, col_num, row_num, seat_id, seat.seat_status';
        $join  = "{$this->_pft_roundseat} seat on seat.id = jit.seat_id";
        $where = [
            'jit.round_id' => $roundId,
            'jit.zone_id'  => $zoneId,
            'jit.status'   => ['in', $statusArr],
        ];

        //座位可售状态控制
        if ($seatStatus) {
            $where['seat.seat_status'] = ['in', $seatStatus];
        }

        $res = $this->table($table)->where($where)->field($field)->join($join)->select();

        return $res ? $res : [];
    }

    /**
     * 具体占座
     * <AUTHOR>
     * @date   2018-01-28
     *
     * @param  int  $roundId  场次ID
     * @param  int  $zoneId  分区ID
     * @param  int  $seatNum  需要的座位数量
     * @param  int  $buyId  购买人ID
     * @param  int  $topResellerId  购买的一级分销商ID
     * @param  array  $seatIdList  选座的座位，如果没有的话就按配置优先级获取
     * @param  bool  $isReserveMode  是否是预留位置占座
     * @param  bool  $isAutoContinueSeat  是否开启自动连座排座
     *
     * @return array
     */
    public function holdingZoneSeats($roundId, $zoneId, $seatNum, $buyId, $topResellerId, $seatIdList = [], $isReserveMode = false, $isAutoContinueSeat = true)
    {
        $seatNum = intval($seatNum);
        if (!$roundId || !$zoneId || !$seatNum || !$buyId || !$topResellerId) {
            return [0, '参数错误'];
        }

        if ($isReserveMode) {
            //预留位置占座模式

            if (!$seatIdList || count($seatIdList) != $seatNum) {
                return [0, '预留占座的位置数量不对'];
            }

            //需要校验下对应座位是否有包含舞台
            $stageList = $this->getZoneSeats($roundId, \Business\PftShow\ShowManage::getStageConstants()['stage_id'], false, $seatIdList);
            if ($stageList) {
                return [0, '该区域不支持售卖'];
            }

            //获取预留记录ID
            $recordInfo = $this->getReserveRecordInfoBySeats($roundId, $seatIdList);
            if (!$recordInfo) {
                return [0, '获取不到预留记录'];
            }
            $recordId = $recordInfo['record_id'];

            //开启事务
            $updateTime = time();
            $this->startTrans();

            //获取场次版本号
            $roundInfo = $this->getRoundInfo($roundId, 'version, is_new, opid');

            //返回相应的分区信息
            $zoneInfo = $this->getZoneBaseInfo($zoneId);

            if (!$roundInfo || !$zoneInfo) {
                return [0, '场次或是分区的数据不存在'];
            }
            $version = $roundInfo['version'];

            //将相应的预留位置设置为已经占座
            $where = [
                'round_id' => $roundId,
                'zone_id'  => $zoneId,
                'seat_id'  => ['in', $seatIdList],
                'status'   => 1, //预留
            ];

            $data = [
                'status'          => 2,
                'opid'            => $buyId,
                'top_reseller_id' => $topResellerId,
                'update_time'     => $updateTime,
            ];

            $successNum = $this->table($this->_pft_seat_jit)->where($where)->save($data);
            if ($successNum === false) {
                $this->rollback();

                return [0, "占座失败[{$successNum}]"];
            }
            if($successNum != count($seatIdList)) {
                $this->rollback();

                return [3, "占座失败! 部分座位存在冲突{$successNum}"];
            }

            //更新预留记录
            $res = $this->_updateReserveDetail($recordId, $roundId, $zoneId, $seatIdList, $useType = 'use');
            if (!$res) {
                $this->rollback();

                return [0, "更新预留记录信息错误"];
            }

            //更新座位汇总数据
            $collectRes = $this->_updateRoundCollect($roundId, $version, $zoneId, $seatNum, $type = 'use_reserve');
            if (!$collectRes) {
                $this->rollback();

                return [2, '场次分区剩余座位保存失败'];
            }

            if ($roundInfo['is_new']) {
                //调用演出服务更新预留座位数
                $upRes = (new \Business\PftShow\Storage())->setUnSaleNum($roundInfo['opid'], $zoneId, $seatNum, 2, $roundId, false, 'reverse_num');
                if ($upRes['code'] != 200) {
                    $this->rollback();

                    return [2, $upRes['msg']];
                }

                //调用演出服务更新预留已售数
                $upRes = (new \Business\PftShow\Storage())->setUnSaleNum($roundInfo['opid'], $zoneId, $seatNum, 1, $roundId, false, 'reverse_sale_num');
                if ($upRes['code'] != 200) {
                    $this->rollback();

                    return [2, $upRes['msg']];
                }
            }

            //提交事务
            $this->commit();

            //为了兼容之前的程序，这边返回相应的jit表的ID
            $seatList = $this->_getSeatListBySeats($roundId, $seatIdList);

            $resData = ['seat_list' => $seatList];

            //返回数据
            return [1, '', $resData];

        } else {
            //正常占座
            if (!$seatIdList || !is_array($seatIdList)) {
                $seatIdList = $this->getAvailableZoneSeats($roundId, $zoneId, $seatNum, $isAutoContinueSeat);

                //获取座位失败
                if (!$seatIdList || count($seatIdList) != $seatNum) {
                    return [0, '系统占座失败', $seatIdList];
                }
            } else {
                //检测下指定座位状态是否可售
                $tmpWhere = [
                    'round_id' => $roundId,
                    'seat_id'  => ['in', $seatIdList],
                    'status'   => ['in', [0, 1, 4]], // 1=预留 2=锁定 3=已售出 5=不可售
                ];
                $tmpCount = $this->table($this->_pft_seat_jit)->where($tmpWhere)->count();
                if ($seatNum != $tmpCount) {
                    return [0, '系统占座失败，座位不可用', $seatIdList];
                }
            }

            //需要校验下对应座位是否有包含舞台
            $stageList = $this->getZoneSeats($roundId, \Business\PftShow\ShowManage::getStageConstants()['stage_id'], false, $seatIdList);
            if ($stageList) {
                return [0, '该区域不支持售卖'];
            }

            //开启事务
            $updateTime = time();
            $this->startTrans();

            //获取场次版本号
            $roundInfo = $this->getRoundInfo($roundId, 'version, is_new');

            //返回相应的分区信息
            $zoneInfo = $this->getZoneBaseInfo($zoneId);

            if (!$roundInfo || !$zoneInfo) {
                return [0, '场次或是分区的数据不存在'];
            }
            $version = $roundInfo['version'];

            //将相应的位置设置为已经占座
            $where = [
                'round_id' => $roundId,
                'zone_id'  => $zoneId,
                'seat_id'  => ['in', $seatIdList],
                'status'   => ['in', [0, 1, 4]], //可用，预留，已经退
            ];

            $data = [
                'status'          => 2,
                'opid'            => $buyId,
                'top_reseller_id' => $topResellerId,
                'update_time'     => $updateTime,
            ];

            $successNum = $this->table($this->_pft_seat_jit)->where($where)->save($data);
            if ($successNum === false) {
                $this->rollback();

                return [0, "占座失败[{$successNum}]"];
            }
            if($successNum != count($seatIdList)) {
                $this->rollback();

                return [3, "占座失败! 部分座位存在冲突{$successNum}"];
            }

            $collectRes = $this->_updateRoundCollect($roundId, $version, $zoneId, $seatNum, $type = 'direct');
            if (!$collectRes) {
                $this->rollback();

                return [2, '场次分区剩余座位保存失败'];
            }

            //提交事务
            $this->commit();

            //为了兼容之前的程序，这边返回相应的jit表的ID
            $seatList = $this->_getSeatListBySeats($roundId, $seatIdList);

            $resData = ['seat_list' => $seatList];

            //返回数据
            return [1, '', $resData];
        }
    }

    /**
     * 关联座位
     * <AUTHOR>
     * @date   2018-02-01
     *
     * @param  int  $roundId  场次ID
     * @param  array  $jitIdList  jitId
     * @param  int  $buyId  购买用户ID
     * @param  int  $ordernum  订单号
     * @param  int  $payStatus  支付状态
     *
     * @return mix
     */
    public function relationZoneSeats($roundId, $jitIdList, $buyId, $ordernum, $payStatus)
    {
        $where = [
            'id'       => ['in', $jitIdList],
            'round_id' => $roundId,
            // 'opid'     => $buyId,
            'status'   => 2,
        ];

        //未支付更新但状态是锁定
        $data = ['ordernum' => $ordernum];
        if ($payStatus == 2) {
            $data['status'] = 2;
        } else {
            $data['status'] = 3;
        }

        $res = $this->table($this->_pft_seat_jit)->where($where)->save($data);

        return $res === false ? false : $res;
    }

    /**
     * 订单支付后更新座位
     * TODO:这边正常应该是通过订单号找到相应的座位，然后通过场次和座位号来更新
     *
     * @param  string  $ordernum
     * @param  int  $buyId
     *
     * @return int
     */
    public function payZoneSeats($ordernum, $buyId)
    {
        $where = [
            'ordernum' => $ordernum,
            'status'   => 2,
            'opid'     => $buyId,
        ];
        $data  = ['status' => 3];

        $res = $this->table($this->_pft_seat_jit)->where($where)->save($data);

        return $res === false ? false : $res;
    }

    /**
     * 判断分区的库存
     *
     * @param  int  $roundId
     * @param  int  $zoneId
     * @param  int  $tnum
     *
     * @return bool
     */
    public function isZoneStorageEnough($roundId, $zoneId, $tnum)
    {
        if (!$roundId || !$zoneId) {
            return false;
        }

        $where = ['round_id' => $roundId, 'zone_id' => $zoneId];
        $res   = $this->table($this->_pft_zone_seat)->where($where)->find();
        if (!$res) {
            return false;
        }

        $seatLeft = intval($res['seat_left']);
        if ($seatLeft < $tnum) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 获取场次已经使用的座位数量
     * <AUTHOR>
     * @date   2018-02-01
     *
     * @param  int  $roundId  场次ID
     *
     * @return int
     */
    public function getRoundUsedSeatsNum($roundId)
    {
        if (!$roundId) {
            return false;
        }

        $where = [
            'round_id' => $roundId,
            'status'   => ['in', [2, 3]],
        ];

        $usedNum = $this->table($this->_pft_seat_jit)->where($where)->count();
        if ($usedNum === false) {
            return false;
        }

        return intval($usedNum);
    }

    /**
     * 获取需要生成场次座位的场次列表
     * <AUTHOR>
     * @date   2018-02-01
     *
     * @return array
     */
    public function getHandleRoundList($startTime, $page = 1, $size = 100, $order = 'desc')
    {
        $where = [
            'publish_time'    => ['EGT', $startTime],
            'ext_handle_time' => 0,
        ];

        if ($order == 'desc') {
            $orderStr = "publish_time desc";
        } else {
            $orderStr = "publish_time asc";
        }

        $pageStr = "{$page},{$size}";
        $field   = 'venus_id, id, opid, is_new';

        $list = $this->table($this->_pft_round)->where($where)->field($field)->page($pageStr)->order($orderStr)->select();

        return $list ? $list : [];
    }

    /**
     * 获取需要迁移的场次
     * <AUTHOR>
     * @date   2018-03-14
     *
     * @param  date  $startDate  开始日期
     * @param  date  $endDate  结束日期
     *
     * @return array
     */
    public function getArchiveRoundList($startDate, $endDate)
    {
        if (!$endDate) {
            return [];
        }

        $field = 'id';
        $order = 'id desc';
        $where = [
            'use_date' => ['between', [$startDate, $endDate]],
        ];
        $list  = $this->table($this->_pft_round)->where($where)->order($order)->field($field)->select();

        return $list ? $list : [];
    }

    /**
     * 迁移场次座位数据
     * <AUTHOR>
     * @date   2018-03-14
     *
     * @param  int  $roundId  场次ID
     *
     * @return bool
     */
    public function migrationsRoundSeats($roundId)
    {
        if (!$roundId) {
            return false;
        }

        $where = ['round_id' => $roundId];
        $list  = $this->table($this->_pft_seat_jit)->where($where)->select();

        if (!$list) {
            return false;
        }

        $delIdList = array_column($list, 'id');
        $addData   = [];
        foreach ($list as $item) {
            $newData = $item;
            unset($newData['id']);
            $addData[] = $newData;
        }

        $this->startTrans();

        //先迁移数据
        $res = $this->table($this->_pft_seat_jit_history)->addAll($addData);
        if ($res === false) {
            $this->rollback();

            return false;
        }

        //删除原来表的数据
        $where = ['id' => ['in', $delIdList]];
        $res   = $this->table($this->_pft_seat_jit)->where($where)->delete();
        if ($res === false) {
            $this->rollback();

            return false;
        }

        //返回成功
        $this->commit();

        return true;
    }

    /**
     * 通过座位ID获取座位数据
     * <AUTHOR>
     *
     * @param  int  $roundId
     * @param  array  $seatIdList
     *
     * @return array
     */
    private function _getSeatListBySeats($roundId, $seatIdList)
    {
        if (!$roundId || !$seatIdList) {
            return [];
        }

        $field = 'jit.id, seat.custom_num,seat.custom_pos,seat.row_num, seat.col_num, jit.seat_id';
        $table = "{$this->_pft_seat_jit} as jit";
        $join  = "{$this->_pft_roundseat} as seat ON jit.seat_id = seat.id";
        $where = ['round_id' => $roundId, 'seat_id' => ['in', $seatIdList]];

        $list = $this->table($table)->where($where)->join($join, 'left')->field($field)->select();

        return $list ? $list : [];
    }

    /**
     * 更新场次的剩余座位信息
     * 后期如果性能有问题，在下单的时候可以使用direct方式
     * <AUTHOR>
     *
     * @param  int  $roundId  场次ID
     * @param  int  $version  版本号
     * @param  int  $zoneId  分区ID
     * @param  int  $seatNum  座位数
     * @param  int  $type  类型 calc=从jit表中计算后更新，direct=直接扣减, reserve=座位预留, use_reserve=预留座位占座, release=释放已经占用的座位
     * @param  bool  $isFromHistoryJit  是否从归档表中汇总
     *
     * @return bool
     */
    private function _updateRoundCollect($roundId, $version, $zoneId = 0, $seatNum = 0, $type = 'calc', $isFromHistoryJit = false)
    {
        if ($type == 'calc') {
            //获取相应分区的汇总数据
            $where = [
                'round_id' => $roundId,
                'status'   => ['in', [0, 1, 2, 3, 4, 5]],
            ];
            $field = 'zone_id,status,count(id) cnt';
            $group = "zone_id,status";

            if ($isFromHistoryJit) {
                $jitTable = $this->_pft_seat_jit_history;
            } else {
                $jitTable = $this->_pft_seat_jit;
            }

            $res = $this->table($jitTable)->where($where)->field($field)->group($group)->select();
            if ($res === false) {
                return false;
            }

            //处理汇总的数据
            $roundData = [
                'seat_storage' => 0,
                'seat_reverse' => 0,
                'seat_left'    => 0,
            ];
            $areaData  = [];

            foreach ($res as $item) {
                $zoneId = $item['zone_id'];
                $status = $item['status'];
                $cnt    = $item['cnt'];

                if (!isset($areaData[$zoneId])) {
                    $areaData[$zoneId] = [
                        'seat_storage' => 0,
                        'seat_reverse' => 0,
                        'seat_left'    => 0,
                        'seat_total'   => 0,
                    ];
                }

                //所有的座位
                $areaData[$zoneId]['seat_total'] += $cnt;

                if ($status == 5) {
                    //座位禁用

                } else {
                    //可以使用座位
                    $areaData[$zoneId]['seat_storage'] += $cnt;
                    if ($zoneId > 0) {
                        $roundData['seat_storage'] += $cnt;
                    }

                    if (in_array($status, [0, 4])) {
                        //可售
                        if ($zoneId > 0) {
                            $roundData['seat_left'] += $cnt;
                        }
                        $areaData[$zoneId]['seat_left'] += $cnt;

                    } else if ($status == 1) {
                        //预留
                        if ($zoneId > 0) {
                            $roundData['seat_reverse'] += $cnt;
                        }
                        $areaData[$zoneId]['seat_reverse'] += $cnt;

                    } else {
                        //已经被使用了 = 3
                    }
                }
            }

            //更新分区座位汇总信息
            foreach ($areaData as $zoneId => $value) {
                $tmpWhere = [
                    'round_id' => $roundId,
                    'zone_id'  => $zoneId,
                ];

                //之前已经生成的分区，会存在这个汇总表分区数据不存在的情况
                $existZoneInfo = $this->table($this->_pft_zone_seat)->where($tmpWhere)->find();
                if ($existZoneInfo) {
                    //更新数据
                    $res = $this->table($this->_pft_zone_seat)->where($tmpWhere)->save($value);
                    if ($res === false) {
                        return false;
                    }
                } else {
                    //添加数据
                    $newZoneInfo = array_merge($tmpWhere, $value);
                    $res         = $this->table($this->_pft_zone_seat)->add($newZoneInfo);
                    if ($res === false) {
                        return false;
                    }
                }
            }

            //最后更新场次的座位信息
            $roundWhere           = ['id' => $roundId, 'version' => $version];
            $roundData['version'] = ['exp', "version+1"];

            $res = $this->table($this->_pft_round)->where($roundWhere)->save($roundData);
            if ($res === false) {
                return false;
            }

            //更新成功
            return true;
        } else if ($type == 'reserve') {
            //预留座位，将可以售卖的座位设置为预留
            $tmpWhere = [
                'round_id' => $roundId,
                'zone_id'  => $zoneId,
            ];
            $tmpData  = [
                'seat_left'    => ['exp', "seat_left-{$seatNum}"],
                'seat_reverse' => ['exp', "seat_reverse+{$seatNum}"],
            ];

            $res = $this->table($this->_pft_zone_seat)->where($tmpWhere)->save($tmpData);
            if (!$res) {
                return false;
            }

            //将场次的库存扣除
            $roundWhere = ['id' => $roundId, 'version' => $version];
            $roundData  = [
                'version'      => ['exp', "version+1"],
                'seat_left'    => ['exp', "seat_left-{$seatNum}"],
                'seat_reverse' => ['exp', "seat_reverse+{$seatNum}"],
            ];
            $res        = $this->table($this->_pft_round)->where($roundWhere)->save($roundData);
            if (!$res) {
                return false;
            }

            //更新成功
            return true;

        } else if ($type == 'use_reserve') {
            //使用预留座位，将预留的座位设置为占座
            $tmpWhere = [
                'round_id' => $roundId,
                'zone_id'  => $zoneId,
            ];
            $tmpData  = [
                'seat_reverse' => ['exp', "seat_reverse-{$seatNum}"],
            ];

            $res = $this->table($this->_pft_zone_seat)->where($tmpWhere)->save($tmpData);
            if (!$res) {
                return false;
            }

            //将场次的预留数据扣除
            $roundWhere = ['id' => $roundId, 'version' => $version];
            $roundData  = [
                'version'      => ['exp', "version+1"],
                'seat_reverse' => ['exp', "seat_reverse-{$seatNum}"],
            ];
            $res        = $this->table($this->_pft_round)->where($roundWhere)->save($roundData);
            if (!$res) {
                return false;
            }

            //更新成功
            return true;

        } else if ($type == 'release_reserve') {
            //取消预留座位
            $tmpWhere = [
                'round_id' => $roundId,
                'zone_id'  => $zoneId,
            ];
            $tmpData  = [
                'seat_left'    => ['exp', "seat_left+{$seatNum}"],
                'seat_reverse' => ['exp', "seat_reverse-{$seatNum}"],
            ];

            $res = $this->table($this->_pft_zone_seat)->where($tmpWhere)->save($tmpData);
            if (!$res) {
                return false;
            }

            //将场次的库存扣除
            $roundWhere = ['id' => $roundId, 'version' => $version];
            $roundData  = [
                'version'      => ['exp', "version+1"],
                'seat_left'    => ['exp', "seat_left+{$seatNum}"],
                'seat_reverse' => ['exp', "seat_reverse-{$seatNum}"],
            ];
            $res        = $this->table($this->_pft_round)->where($roundWhere)->save($roundData);
            if (!$res) {
                return false;
            }

            //更新成功
            return true;

        } else if ($type == 'release') {
            //释放座位
            $tmpWhere = [
                'round_id' => $roundId,
                'zone_id'  => $zoneId,
            ];
            $tmpData  = ['seat_left' => ['exp', "seat_left+{$seatNum}"]];

            $res = $this->table($this->_pft_zone_seat)->where($tmpWhere)->save($tmpData);
            if (!$res) {
                return false;
            }

            //将场次的库存扣除
            $roundWhere = ['id' => $roundId, 'version' => $version];
            $roundData  = [
                'version'   => ['exp', "version+1"],
                'seat_left' => ['exp', "seat_left+{$seatNum}"],
            ];
            $res        = $this->table($this->_pft_round)->where($roundWhere)->save($roundData);
            if (!$res) {
                return false;
            }

            //更新成功
            return true;
        } else {
            //将相应分区的库存扣除
            $tmpWhere = [
                'round_id' => $roundId,
                'zone_id'  => $zoneId,
            ];
            $tmpData  = ['seat_left' => ['exp', "seat_left-{$seatNum}"]];

            $res = $this->table($this->_pft_zone_seat)->where($tmpWhere)->save($tmpData);
            if (!$res) {
                return false;
            }

            //将场次的库存扣除
            $roundWhere = ['id' => $roundId, 'version' => $version];
            $roundData  = [
                'version'   => ['exp', "version+1"],
                'seat_left' => ['exp', "seat_left-{$seatNum}"],
            ];
            $res        = $this->table($this->_pft_round)->where($roundWhere)->save($roundData);
            if (!$res) {
                return false;
            }

            //更新成功
            return true;
        }
    }

    /**
     * 获取可用的分区座位
     *
     * 自动排座的逻辑 - 连座的逻辑第一优先，然后是后台配置的优先级
     *  1. 先判断同一优先级同一排有没有满足连续的座位
     *  2. 如果都没有的话，就去掉优先级判断，从第一排开始轮询判断是否有连续的座位
     *  3. 如果都没有连续的座位的话，就是按优先级一个个座位占座，如果优先级相同，则从上到下，左到右
     *
     * <AUTHOR>
     * @date   2018-01-28
     *
     * @param  int  $roundId  场次ID
     * @param  int  $zoneId  分区ID
     * @param  int  $seatNum  需要的座位数量
     * @param  bool  $isAutoContinueSeat  是否开启自动连座排座
     *
     * @return array
     */
    public function getAvailableZoneSeats($roundId, $zoneId, $seatNum, $isAutoContinueSeat = true)
    {
        $seatNum = intval($seatNum);
        if (!$roundId || !$zoneId || !$seatNum) {
            return [];
        }

        //获取排序后的可用座位
        $availableSeats = $this->sortZoneLeftSeats($roundId, $zoneId);

        $rowSeat      = $availableSeats['row_seat'];
        $sortSeat     = $availableSeats['sort_seat'];
        $priorityList = $availableSeats['priority'];

        //判断座位够不够
        if ($seatNum > count($sortSeat)) {
            return [];
        }

        //如果只是需要一个座位，直接返回
        if ($seatNum == 1) {
            $firstSeatId = $sortSeat[0]['seat_id'];

            return [$firstSeatId];
        }

        if (!$isAutoContinueSeat) {
            //如果不需要连座排序的话，直接从排序好的座位中截取座位就可以了
            $tmpSeats = array_slice($sortSeat, 0, $seatNum);
            $resSeat  = array_column($tmpSeats, 'seat_id');

            return $resSeat;
        } else {
            //需要进行连座排序
            //1. 判断是否有相同优先级连续的座位
            if ($priorityList) {
                $resSeats = $this->_getPriovityContinueSeats($priorityList, $seatNum);
                if ($resSeats) {
                    return $resSeats;
                }
            }

            //2. 去除优先级后，判断是否有连续的座位
            $resSeats = $this->_getCommonContinueSeats($rowSeat, $seatNum);
            if ($resSeats) {
                return $resSeats;
            }

            //3. 都没有连续的座位
            $tmpSeats = array_slice($sortSeat, 0, $seatNum);
            $resSeat  = array_column($tmpSeats, 'seat_id');

            return $resSeat;
        }
    }

    /**
     * 分销商库存判断
     * <AUTHOR>
     * @DateTime 2016-02-05T10:59:53+0800
     *
     * @param  int  $roundId  场次ID
     * @param  int  $areaId  分区ID
     * @param  int  $resellerId  分销商ID
     *
     * @return   bool 返回true，就是没有做分销数量判断
     */
    public function storageCheck($roundId, $areaId, $resellerId, $venueID, $tNum)
    {
        //需要做分销库存的判断
        $roundInfo = $this->getRoundInfo($roundId);

        //如果是新版的 需要获取新版可用库存数
        if ($roundInfo['is_new']) {
            $storage    = false;
            $storageRes = (new \Business\PftShow\DisStorage())->queryBatchAvailableStorage($roundInfo['opid'], $resellerId, $areaId, $roundId, $roundInfo['use_date']);
            if ($storageRes['code'] == 200 && $storageRes['data']) {
                $storage = $storageRes['data'][0]['available_num'];
            }
        } else {
            $storageModel = new YXStorage();
            $storage      = $storageModel->getResellerStorage($resellerId, $roundId, $areaId);
        }
        if ($storage === false) {
            return true;
        }

        if (intval($tNum) > $storage) {
            return false;
        }

        return true;
    }

    /**
     * 根据场次信息，匹配唯一场次
     *
     * <AUTHOR>
     * @date   2017-07-03
     *
     * @param $venueId 场馆id
     * @param $useDate 适用日期
     * @param $beginTime 开始时间
     * @param $field 字段
     * @param $status 状态
     *
     * @return bool|mixed
     */
    public function getRoundByDetail($venueId, $useDate, $beginTime, $field = 'id', $status = false)
    {
        if (!$venueId || !$useDate || !$beginTime || !$field) {
            return false;
        }

        $filter = [
            'venus_id' => $venueId,
            'use_date' => $useDate,
            'bt'       => $beginTime,
        ];

        if ($status !== false) {
            $filter['status'] = $status;
        }

        $res = $this->table($this->_pft_round)->field($field)->where($filter)->find();

        return $res;
    }

    /**
     * 根据场次信息，匹配唯一场次
     *
     * <AUTHOR>
     * @date   2017-07-03
     *
     * @param  int  $venueId  场馆id
     * @param  string  $useDate  适用日期
     * @param  string  $beginTime  开始时间
     * @param  string  $field  字段
     * @param  int  $status  状态
     *
     * @return bool|mixed
     */
    public function getRoundInfoBySortId($venusId, $useDate, $roundSortId, $field = 'id', $statusArr = [])
    {
        if (!$venusId || !$useDate || !$roundSortId || !$field) {
            return false;
        }

        $filter = [
            'venus_id'      => $venusId,
            'use_date'      => $useDate,
            'round_sort_id' => $roundSortId,
            'status'        => 0,
        ];

        if ($statusArr) {
            $filter['status'] = ['in', $statusArr];
        }

        $res = $this->table($this->_pft_round)->field($field)->where($filter)->find();

        return $res;
    }

    /**
     * 获取场馆演出列表
     *
     * <AUTHOR>
     * @date   2017-07-03
     *
     * @param  int  $venueId  场馆id
     * @param  string  $field  字段
     * @param  int  $status  状态
     * @param  string  $fromDate  开始适用日期 2017-01-01
     * @param  string  $toDate  结束适用日期 2017-01-02
     * @param  string  $orderBy  排序
     *
     * @return bool|mixed
     */
    public function getRoundList($venueId, $field = 'id', $status = false, $fromDate = false, $toDate = false, $orderBy = false)
    {
        if (!$venueId || !$field) {
            return false;
        }

        //过滤条件
        $filter = [
            'venus_id' => $venueId,
        ];

        if ($status !== false) {
            $filter['status'] = $status;
        }

        if ($fromDate && $toDate) {
            //确定查询开始结束区间
            if (strtotime($fromDate) > strtotime($toDate)) {
                return false;
            }
            $filter['use_date'] = [['egt', $fromDate], ['elt', $toDate]];
        } else if ($fromDate) {
            //开始日期
            $filter['use_date'] = ['egt', $fromDate];
        } else if ($toDate) {
            //结束日期
            $filter['use_date'] = ['elt', $toDate];
        }

        $res = $this->table($this->_pft_round)->field($field)->where($filter);
        //排序
        if ($orderBy) {
            $res = $res->order($orderBy);
        }

        $res = $res->select();

        return $res;
    }

    /**
     * 获取场馆信息
     * <AUTHOR>
     * @date   2017-07-05
     *
     * @param  int  $venueId  场馆id
     * @param  string  $field
     *
     * @return bool|mixed
     */
    public function getVenuesInfo($venueId, $field = 'id')
    {
        if (!$venueId || !$field) {
            return false;
        }

        $filter = [
            'id' => $venueId,
        ];

        $res = $this->table($this->_pft_venues)->where($filter)->find();

        return $res;
    }

    /**
     * 根据id获取分区基本信息
     * <AUTHOR>
     * @date   2017-07-05
     *
     * @param  int  $zoneId  分区号
     * @param $field
     *
     * @return
     */
    public function getZoneBaseInfo($zoneId, $field = 'id')
    {

        if (!$zoneId || !$field) {
            return false;
        }

        $filter = [
            'id' => $zoneId,
        ];

        $res = $this->table($this->_pft_roundzone)->where($filter)->find();

        return $res;
    }

    /**
     * 根据分区id获取座位
     * <AUTHOR>
     * @date   2017-07-03
     *
     * @param  int  $zoneId  分区id
     * @param  string  $field  字段
     * @param  bool  $seatStatus  状态
     * @param  int  $venueId  场馆id
     *
     * @return bool|mixed
     */
    public function getRoundSeatById($zoneId, $field = 'id', $seatStatus = false, $venueId = 0)
    {
        if (!$zoneId || !$field) {
            return false;
        }

        $filter = [
            'zone_id' => $zoneId,
        ];

        if ($venueId) {
            $filter['venue_id'] = intval($venueId);
        }

        if ($seatStatus !== false) {
            if (is_array($seatStatus)) {
                $filter['seat_status'] = ['in', $seatStatus];
            } else {
                $filter['seat_status'] = $seatStatus;
            }
        }

        $res = $this->table($this->_pft_roundseat)->field($field)->where($filter)->select();

        return $res;
    }

    /**
     * 根据场馆id获取座位
     * <AUTHOR>
     * @date   2018-01-15
     *
     * @param  int  $venueId  场馆id
     * @param  string  $field  字段
     * @param  bool  $seatStatus  状态
     *
     * @return bool|mixed
     */
    public function getRoundSeatByVenueId($venueId, $field = 'id', $seatStatus = false)
    {
        if (!$venueId || !$field) {
            return false;
        }

        $filter = [
            'venue_id' => $venueId,
        ];

        if ($seatStatus !== false) {
            if (is_array($seatStatus)) {
                $filter['seat_status'] = ['in', $seatStatus];
            } else {
                $filter['seat_status'] = $seatStatus;
            }
        }

        $res = $this->table($this->_pft_roundseat)->field($field)->where($filter)->select();

        return $res;
    }

    /**
     * 获取旧的座位数据
     * <AUTHOR>
     * @date   2018-02-08
     *
     * @param  int  $roundId
     *
     * @return
     */
    public function getOldSeatDyn($roundId)
    {
        if (!$roundId) {
            return [];
        }

        $list = $this->table('pft_roundseat_dyn')->where(['round_id' => $roundId])->select();

        return $list ? $list : [];
    }

    /**
     * 通过场馆获取可以售卖的场次数据
     * <AUTHOR>
     * @date   2018-01-25
     *
     * @param  int  $venusId
     * @param  date  $useDate
     * @param  int  $presale
     *
     * @return array
     */
    public function getRoundInfoByVenusId($venusId, $useDate, $presale = 0)
    {
        $map   = [
            'venus_id' => $venusId,
            'use_date' => $useDate,
            'pre_sale' => ['egt', $presale],
            'status'   => 0,
        ];
        $field = 'id, venus_id, round_name, bt, et, use_date, pre_sale, status, seat_storage, seat_reverse, seat_left, round_sort_id, is_new, opid, round_mode';

        $data = $this->table($this->_pft_round)->field($field)->where($map)->order('use_date, bt, et')->select();

        return $data;
    }

    /**
     * 通过round表的id获取该条数据
     *
     * @param $roundId
     * @param  string  $field
     *
     * @return mixed
     *
     */
    public function getRoundInfoById($roundId, $field = 'id,round_sort_id')
    {
        $map = [
            'id' => $roundId,
        ];

        $data = $this->table($this->_pft_round)->field($field)->where($map)->find();

        return $data;
    }

    /**
     * 根据日期获取演出所有场次
     *
     * @param  int  $venusId  场馆ID
     * @param  string  $date  使用日期
     * @param  int  $getStorage  是否一起获取库存
     * @param  int  $areaId  分区ID
     *
     * @return array|bool
     */
    public function getVenusRoundByDate($venusId, $date, $getStorage = 0, $areaId = 0)
    {
        // 获取场馆分区
        $areaList = $this->GetRoundZoneInfo($venusId, $areaId);
        if ($areaList['code'] != 200) {
            return false;
        }

        // 如果有用分区ID查询，那么返回的是一维数组，这里统一处理为二维数组
        if ($areaId > 0) {
            $areaList['data'] = [$areaList['data']];
        }

        // 处理预售
        $presale    = (strtotime($date) - strtotime(date('Y-m-d', time() + 96400))) / 86400;
        $roundsList = $this->getRoundInfoByVenusId($venusId, $date, $presale);

        $output = [];
        if ($getStorage && $roundsList) {
            foreach ($roundsList as $item) {
                $areaSeat     = array();
                $roundStorage = 0;
                foreach ($areaList['data'] as $areaItem) {
                    $seats             = $this->round_seat_list($item['id'], $item['venus_id'], $areaItem['id']);
                    $areaSeat[$areaId] = count($seats);
                    $roundStorage      += count($seats);
                }
                $item['storage']      = $roundStorage;
                $item['area_storage'] = $areaSeat;
                $output[]             = $item;
            }

            return $output;
        }

        return $roundsList;
    }

    /**
     * 获取场馆最近有安排场次的日期
     * <AUTHOR>
     * @date   2018-02-01
     *
     * @param  int  $venueId
     * @param  string  $startDate
     *
     * @return string
     */
    public function getLastedShowDate($venueId, $startDate)
    {
        if (!$venueId || !$startDate) {
            return false;
        }

        $where = [
            'venus_id' => $venueId,
            'use_date' => ['EGT', $startDate],
            'status'   => 0,
        ];

        $res = $this->table($this->_pft_round)->where($where)->field('use_date')->order('use_date asc')->find();
        if ($res) {
            return $res['use_date'];
        } else {
            return false;
        }
    }

    /**
     * 获取场馆在指定时间段内已经设置了场次的日期
     * <AUTHOR>
     * @date   2018-01-11
     *
     * @param  int  $venueId
     * @param  string  $startDate
     * @param  string  $endDate
     *
     * @return array
     */
    public function getSetRoundDate($venueId, $startDate = false, $endDate = false)
    {
        if (!$venueId) {
            return [];
        }

        $group = 'use_date';
        $where = [
            'venus_id' => $venueId,
            'status'   => 0,
        ];

        if ($startDate && $endDate) {
            $where['use_date'] = ['between', [$startDate, $endDate]];
        }
        $tmp = $this->table($this->_pft_round)->where($where)->group($group)->getField('use_date', true);
        $tmp = $tmp ? $tmp : [];

        $res = [];
        foreach ($tmp as $item) {
            if ($item && $item != '0000-00-00') {
                $res[] = $item;
            }
        }

        return $res;
    }

    /**
     * 插入场次的冗余数据
     * <AUTHOR>
     * @date   2018-01-23
     *
     * @param  int  $roundId
     * @param  array  $seatList
     * @param  array  $areaSummary
     *
     * @return bool
     */
    public function saveRoundExt($roundId, $seatList, $areaSummary)
    {
        if (!$roundId || !$seatList || !$areaSummary) {
            return false;
        }

        $this->startTrans();

        //插入场次座位数据
        $res = $this->table($this->_pft_seat_jit)->addAll($seatList);
        if (!$res) {
            $this->rollback();

            return false;
        }

        //插入分区汇总数据
        $areaList    = [];
        $seatStorage = 0;
        $seatLeft    = 0;
        foreach ($areaSummary as $zoneId => $tmpInfo) {
            $zoneSeatStorage = $tmpInfo['seat_storage'];
            $zoneSeatLeft    = $tmpInfo['seat_left'];
            $zoneSeatTotal   = $tmpInfo['seat_total'];

            $areaList[] = [
                'round_id'     => $roundId,
                'zone_id'      => $zoneId,
                'seat_storage' => $zoneSeatStorage,
                'seat_reverse' => 0,
                'seat_left'    => $zoneSeatLeft,
                'seat_total'   => $zoneSeatTotal,
            ];

            if ($zoneId > 0) {
                $seatStorage += $zoneSeatStorage;
                $seatLeft    += $zoneSeatLeft;
            }
        }

        //更新分区的汇总数据
        $res = $this->table($this->_pft_zone_seat)->addAll($areaList);
        if (!$res) {
            $this->rollback();

            return false;
        }

        //插入场次的汇总信息
        $where = ['id' => $roundId, 'ext_handle_time' => 0];
        $data  = ['seat_storage' => $seatStorage, 'seat_left' => $seatLeft, 'ext_handle_time' => time()];
        $res   = $this->table($this->_pft_round)->where($where)->save($data);
        if (!$res) {
            $this->rollback();

            return false;
        }

        //输入处理完成
        $this->commit();

        return true;
    }

    /**
     * 没有设置座位的场馆直接生成场次
     * <AUTHOR>
     * @date   2018-02-09
     *
     * @param  int  $roundId
     *
     * @return [type]
     */
    public function saveEmptyRound($roundId)
    {
        if (!$roundId) {
            return false;
        }

        //插入场次的汇总信息
        $where = ['id' => $roundId, 'ext_handle_time' => 0];
        $data  = ['seat_storage' => 0, 'seat_left' => 0, 'ext_handle_time' => time()];
        $res   = $this->table($this->_pft_round)->where($where)->save($data);
        if (!$res) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 将旧的场次数据插入到新表中
     * <AUTHOR>
     * @date   2018-01-23
     *
     * @param  int  $roundId
     * @param  array  $seatList
     * @param  array  $areaSummary
     *
     * @return bool
     */
    public function addOldRoundData($venueId, $roundId, $seatList, $reserveList = [])
    {
        if (!$roundId || !$seatList || !$venueId) {
            return false;
        }

        $this->startTrans();

        $roundInfo = $this->getRoundInfo($roundId, 'version, use_date, is_new');
        if (!$roundInfo) {
            $this->rollback();

            return false;
        }
        $version = $roundInfo['version'];
        $useDate = $roundInfo['use_date'];

        //插入场次座位数据
        $res = $this->table($this->_pft_seat_jit_history)->addAll($seatList);
        if (!$res) {
            $this->rollback();

            return false;
        }

        $res = $this->_updateRoundCollect($roundId, $version, $zoneId = 0, $seatNum = 0, $type = 'calc', true);
        if (!$res) {
            $this->rollback();

            return false;
        }

        if ($reserveList) {
            $contactInfo = [
                'desc' => '系统默认记录',
            ];

            foreach ($reserveList as $zoneId => $seatList) {
                //添加相应的预留记录
                $res = $this->_insertReserveDetail($venueId, $roundId, $zoneId, $seatList, $useDate, $type = 1,
                    $contactInfo);
                if (!$res) {
                    $this->rollback();

                    return false;
                }
            }
        }

        //插入场次的汇总信息
        $where = ['id' => $roundId, 'ext_handle_time' => 0];
        $data  = ['ext_handle_time' => time()];
        $res   = $this->table($this->_pft_round)->where($where)->save($data);
        if (!$res) {
            $this->rollback();

            return false;
        }

        //输入处理完成
        $this->commit();

        return true;
    }

    /**
     * 更新座位数据
     * <AUTHOR>
     * @date   2018-01-24
     *
     * @param  int  $roundId
     * @param  array  $zoneSeatStatus
     *
     * @return bool
     */
    public function updateRoundExt($roundId, $zoneSeatStatus, $isSync = false)
    {
        if (!$roundId || !$zoneSeatStatus) {
            return false;
        }

        //获取场次更新锁
        $roundInfo = $this->table($this->_pft_round)->where(['id' => $roundId])->find();
        if (!$roundInfo) {
            return false;
        }
        $version = $roundInfo['version'];

        $this->startTrans();

        //更新座位信息
        foreach ($zoneSeatStatus as $item) {
            $where = [
                'seat_id'  => $item['seat_id'],
                'round_id' => $roundId,
                'zone_id'  => $item['zone_id'],
            ];

            //如果有变更前的状态  需要将之前的状态当做条件 防止状态不一致的情况 误更新数据了
            if (isset($item['old_status'])) {
                $where['status'] = $item['old_status'];
            }

            $data  = [
                'status'      => $item['status'],
                'update_time' => time(),
            ];

            $res = $this->table($this->_pft_seat_jit)->where($where)->save($data);

            //如果是同步操作的情况 且更新数量为0或者更新失败 直接返回false
            if ($isSync && !$res) {
                $this->rollback();

                return false;
            }

            if ($res === false) {
                $this->rollback();

                return false;
            }
        }

        $collectRes = $this->_updateRoundCollect($roundId, $version);
        if (!$collectRes) {
            $this->rollback();

            return false;
        }

        $this->commit();

        return true;
    }

    /**
     * 根据订单号获取已经占用的座位
     * <AUTHOR>
     * @date   2018-02-02
     *
     * @param  bool  $isOrderByPriority  是否根据 priority  优先级字段排序
     *
     * @return array
     */
    public function getJitArrByOrdernum($roundId, $zoneId, $ordernum, $isGetBase = false, $isOrderByPriority = false)
    {
        if (!$roundId || !$zoneId || !$ordernum) {
            return [];
        }

        $table = "{$this->_pft_seat_jit} as jit";
        $where = [
            'jit.round_id' => $roundId,
            'jit.zone_id'  => $zoneId,
            'jit.ordernum' => $ordernum,
            'jit.status'   => ['in', [2, 3]],
        ];

        if ($isGetBase) {
            $field = 'jit.seat_id, jit.id, custom_num as seat, custom_pos';
            $join  = "{$this->_pft_roundseat} as seat ON jit.seat_id = seat.id";

            $list = $this->table($table)->where($where)->join($join, 'left')->field($field)->select();
        } else {
            if ($isOrderByPriority) {
                $order = 'jit.priority asc,jit.id desc';
            } else {
                $order = 'jit.id asc';
            }

            $field = 'jit.id, jit.seat_id';
            $list  = $this->table($this->_pft_seat_jit . " jit")->where($where)->field($field)->order($order)->select();
        }

        return $list ? $list : [];
    }

    /**
     * 通过jitId获取被暂时占用的座位信息
     * <AUTHOR>
     * @data   2019-03-26
     *
     * @param  array  $jitIdList
     *
     * @return []
     */
    public function getSeatsListByJit($roundId, $jitIdList)
    {
        if (!$roundId || !$jitIdList || !is_array($jitIdList)) {
            return [];
        }

        $table = "{$this->_pft_seat_jit} as jit";
        $where = [
            'jit.round_id' => $roundId,
            'jit.id'       => ['in', $jitIdList],
            'jit.status'   => 2,
        ];

        $field = 'jit.seat_id, jit.id, custom_num as seat, custom_pos';
        $join  = "{$this->_pft_roundseat} as seat ON jit.seat_id = seat.id";

        $list = $this->table($table)->where($where)->join($join, 'left')->field($field)->select();

        return $list ? $list : [];
    }

    /**
     * 通过查询到的ID释放座位
     * <AUTHOR>
     * @date   2018-02-07
     *
     * @param  int  $roundId
     * @param  int  $zoneId
     * @param  array  $jitArr  动态表ID
     * @param  array  $reserveSeatIdList  需要取消的预留座位ID
     *
     * @return int
     */
    public function releaseSeatByJitArr($roundId, $zoneId, $jitArr, $reserveSeatIdList = [])
    {
        if (!$roundId || !$zoneId || !$jitArr) {
            return false;
        }

        //开启事务
        $this->startTrans();

        $roundInfo = $this->getRoundInfo($roundId, 'version, is_new');
        if (!$roundInfo) {
            $this->rollback();

            return false;
        }

        $version = $roundInfo['version'];

        $where = [
            'id'       => ['in', $jitArr],
            'round_id' => $roundId,
            'zone_id'  => $zoneId,
            'status'   => ['in', [2, 3]],
        ];
        $data  = [
            'status'   => 4,
            'ordernum' => '',
        ];

        $res = $this->table($this->_pft_seat_jit)->where($where)->save($data);
        if ($res != count($jitArr)) {
            $this->rollback();

            return false;
        }

        //如果是预留的座位，就要把相应的预留座位删除
        if ($reserveSeatIdList) {
            $where = [
                'round_id'       => $roundId,
                'zone_id'        => $zoneId,
                'reserve_status' => 2,
                'seat_id'        => ['in', $reserveSeatIdList],
            ];
            $res   = $this->table($this->_reserve_seats)->where($where)->delete();
            if ($res === false) {
                $this->rollback();

                return false;
            }
        }

        //更新座位的汇总信息
        $res = $this->_updateRoundCollect($roundId, $version, $zoneId, count($jitArr), $type = 'release');
        if (!$res) {
            $this->rollback();

            return false;
        }

        $this->commit();

        return true;
    }

    /**
     * 查询动态座位数据
     * <AUTHOR>
     * @date   2018-01-24
     *
     * @param  int  $roundId  场次ID
     * @param  int  $zoneId  分区ID
     * @param  bool  $isGetBase  是否获取基础座位数据
     * @param  array  $seatIdList  指定具体的座位
     *
     * @return array
     */
    public function getZoneSeats($roundId, $zoneIds = false, $isGetBase = true, $seatIdList = [], $jitStateArr = [])
    {
        if (!$roundId) {
            return [];
        }

        $where = ['jit.round_id' => $roundId];
        if ($zoneIds !== false) {
            if (is_array($zoneIds)) {
                $where['jit.zone_id'] = ['in', $zoneIds];
            } else {
                $where['jit.zone_id'] = $zoneIds;
            }
        }

        if ($seatIdList) {
            $where['jit.seat_id'] = ['in', $seatIdList];
        }

        if ($jitStateArr) {
            $where['jit.status'] = ['in', $jitStateArr];
        }

        if ($isGetBase) {
            $field = 'jit.seat_id as id, jit.status, jit.priority, col_num, row_num, custom_num as seat, custom_pos, col_id, row_id';
            $table = "{$this->_pft_seat_jit} as jit";
            $join  = "{$this->_pft_roundseat} as seat ON jit.seat_id = seat.id";

            $list = $this->table($table)->where($where)->join($join, 'left')->field($field)->select();
        } else {
            $field = 'jit.seat_id as id, jit.status, jit.priority, jit.zone_id';
            $table = "{$this->_pft_seat_jit} as jit";

            $list = $this->table($table)->where($where)->field($field)->select();
        }

        $list = $list ? $list : [];

        return $list;
    }

    /**
     * 获取座位列表
     * <AUTHOR>
     * @date   2018-02-01
     *
     * @param  int  $venueId
     * @param  array  $seatIdList
     *
     * @return array
     */
    public function getSeatsList($venueId, $zoneId = 0, $seatIdList = [])
    {
        if (!$venueId) {
            return [];
        }

        $field = 'id, col_num, row_num, custom_num, custom_pos, priority';
        $where = ['venue_id' => $venueId, 'seat_status' => 0];
        if ($zoneId) {
            $where['zone_id'] = $zoneId;
        }
        if ($seatIdList && is_array($seatIdList)) {
            $where['id'] = ['in', $seatIdList];
        }

        $list = $this->table($this->_pft_roundseat)->where($where)->field($field)->select();

        return $list ? $list : [];
    }

    /**
     * 查询分区座位汇总数据
     * <AUTHOR>
     * @date   2018-01-24
     *
     * @param  int  $roundId  场次ID
     * @param  int  $zoneId  分区ID
     *
     * @return array
     */
    public function getZoneCollect($roundId, $zoneId)
    {
        $collect = [
            'total'   => 0, //可以售卖的座位数
            'left'    => 0,
            'reserve' => 0,
            'all'     => 0, //所有的座位数
        ];
        $where   = ['round_id' => $roundId, 'zone_id' => $zoneId];
        $res     = $this->table($this->_pft_zone_seat)->where($where)->find();

        if ($res) {
            $collect['total']   = $res['seat_storage'];
            $collect['left']    = $res['seat_left'];
            $collect['reserve'] = $res['seat_reverse'];
            $collect['all']     = $res['seat_total'];
        }

        return $collect;
    }

    /**
     * 获取分销商的销售量
     */
    public function getResellerNums($roundId, $zoneId, $resellerId = false)
    {
        if (!$roundId || !$zoneId) {
            return false;
        }

        $where = array(
            'zone_id'  => $zoneId,
            'round_id' => $roundId,
            'status'   => ['in', '2, 3'],
        );

        //获取的是一级分销商的数据
        if ($resellerId) {
            $where['top_reseller_id'] = $resellerId;
        }

        $sales = $this->table($this->_pft_seat_jit)->where($where)->count();
        $sales = $sales === false ? 0 : intval($sales);

        return $sales;
    }

    /**
     * 获取场次分区的座位数据
     * <AUTHOR>
     * @date   2018-01-25
     *
     * @return array
     */
    public function getRoundZoneCollect($roundId, $zoneId = false)
    {
        if (!$roundId) {
            return [];
        }

        $where = ['round_id' => $roundId];
        if ($zoneId) {
            $where['zone_id'] = $zoneId;
        }
        $field = 'round_id, zone_id, seat_storage, seat_reverse, seat_left';
        $res   = $this->table($this->_pft_zone_seat)->where($where)->field($field)->select();

        return $res ? $res : [];
    }

    /**
     * 根据座位号获取座位数据
     * <AUTHOR>
     * @date   2018-01-24
     *
     * @param  int  $roundId
     * @param  array  $seatIdArr
     *
     * @return array
     */
    public function getListBySeats($roundId, $seatIdArr)
    {
        if (!$roundId || !$seatIdArr) {
            return [];
        }

        $where = [
            'round_id' => $roundId,
            'seat_id'  => ['in', $seatIdArr],
        ];

        $field = 'seat_id as id, status, zone_id, priority';
        $list  = $this->table($this->_pft_seat_jit)->where($where)->field($field)->select();

        return $list ? $list : [];
    }

    /**
     * 根据订单号获取已经占用的座位
     * <AUTHOR>
     * @date   2018-01-25
     *
     * @return
     */
    public function getSeatByOrdernum($ordernum)
    {
        $ordernum = strval($ordernum);
        if (!$ordernum) {
            return [];
        }

        $where = ['ordernum' => $ordernum, 'status' => ['in', [2, 3]]];
        $res   = $this->table($this->_pft_seat_jit)->where($where)->field('id,seat_id,round_id,zone_id,status')->select();

        return $res ? $res : [];
    }

    /**
     * 批量根据座位动态id获取座位信息
     * author queyourong
     * date 2022/5/23
     *
     * @param $ordernum
     * @param $jitIdArr
     *
     * @return array|false|mixed|string
     */
    public function batchGetSeatInfoByJitId($ordernum, $jitIdArr)
    {
        if(empty($ordernum) || empty($jitIdArr) || !is_array($jitIdArr)) {
            return [];
        }
        $where = [
            //'ordernum' => $ordernum,
            'id' => ['in', $jitIdArr]
        ];
        $res   = $this->table($this->_pft_seat_jit)->where($where)->field('id,seat_id,round_id,zone_id,status')->select();

        return $res ?: [];
    }

    /**
     * 判断是否存在重复的座位
     * <AUTHOR>
     * @date   2018-03-01
     *
     * @param  array  $roundList
     *
     * @return array
     */
    public function isRoundRepeatSeat($roundList)
    {
        if (!$roundList) {
            return [];
        }

        $field  = 'count(id) as cnt,round_id,seat_id';
        $group  = 'round_id, seat_id';
        $where  = ['round_id' => ['in', $roundList], 'ordernum' => ['NEQ', '']];
        $having = "cnt >= 2";

        $res = $this->table($this->_pft_seat_jit)->where($where)->field($field)->group($group)->having($having)->select();

        return $res ? $res : [];
    }

    /**
     * 写入座位预留信息
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  int  $venueId  场馆ID
     * @param  int  $roundId  场次ID
     * @param  int  $zoneId  分区ID
     * @param  array  $seatList  座位列表
     * @param  date  $useDate  日期
     * @param  int  $type  类型：1=默认的预留记录 2=售票员添加的记录
     * @param  array  $contactInfo  预留联系人相关的信息
     *           {
     *              'name' : '联系人姓名',
     *              'mobile' : '联系人手机',
     *              'desc' : '预留备注',
     *              'op_id' : '预留操作人',
     *           }
     *
     * @return bool
     */
    private function _insertReserveDetail($venueId, $roundId, $zoneId, $seatList, $useDate, $type = 1, $contactInfo = [])
    {
        //如果默认的预留记录下，先判断之前是否已经存在记录了
        $recordId = 0;
        if ($type == 1) {
            $existWhere  = ['round_id' => $roundId, 'venue_id' => $venueId, 'zone_id' => $zoneId, 'type' => 1];
            $existRecord = $this->table($this->_reserve_record)->where($existWhere)->field('status, id')->find();
            if ($existRecord) {
                $recordId     = $existRecord['id'];
                $recordStatus = $existRecord['status'];
            }
        }

        if ($recordId) {
            //更新记录
            $where = ['id' => $recordId];
            if ($recordStatus == 1 || $recordStatus == 2) {
                $data = ['status' => 1];
            } else {
                $data = ['status' => 0];
            }

            $res = $this->table($this->_reserve_record)->where($where)->save($data);
            if ($res === false) {
                return false;
            }
        } else {
            //新增记录
            $recordData = [
                'round_id' => $roundId,
                'venue_id' => $venueId,
                'use_date' => $useDate,
                'zone_id'  => $zoneId,
                'add_time' => time(),
                'type'     => $type,
                'status'   => 0,
            ];

            if (isset($contactInfo['name'])) {
                $recordData['contact_name'] = $contactInfo['name'];
            }
            if (isset($contactInfo['mobile'])) {
                $recordData['contact_mobile'] = $contactInfo['mobile'];
            }
            if (isset($contactInfo['desc'])) {
                $recordData['desc'] = $contactInfo['desc'];
            }
            if (isset($contactInfo['op_id'])) {
                $recordData['op_id'] = $contactInfo['op_id'];
            }

            $recordId = $this->table($this->_reserve_record)->add($recordData);
            if (!$recordId) {
                return false;
            }
        }

        $reserveSeat = [];
        foreach ($seatList as $seatId) {
            $reserveSeat[] = [
                'record_id'      => $recordId,
                'round_id'       => $roundId,
                'zone_id'        => $zoneId,
                'seat_id'        => $seatId,
                'reserve_status' => 1,
            ];
        }

        $res = $this->table($this->_reserve_seats)->addAll($reserveSeat);
        if ($res) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 更改预留座位的状态
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  int  $recordId
     * @param  int  $roundId
     * @param  int  $zoneId
     * @param  array  $seatList
     * @param  string  $useType  release=释放座位 use=占住座位
     *
     * @return bool
     */
    private function _updateReserveDetail($recordId, $roundId, $zoneId, $seatList, $useType = 'release')
    {
        $where = [
            'record_id' => $recordId,
            'round_id'  => $roundId,
            'seat_id'   => ['in', $seatList],
        ];

        if ($useType == 'release') {
            //释放座位直接将座位删除
            $res = $this->table($this->_reserve_seats)->where($where)->delete();

            if (!$res) {
                return false;
            }
        } else {
            $data = ['reserve_status' => 2];
            $res  = $this->table($this->_reserve_seats)->where($where)->save($data);

            if (!$res) {
                return false;
            }
        }

        //根据相应的汇总信息获取预留记录的状态
        $res = $this->_updateReserveRecordStatus($recordId);

        return $res ? true : false;
    }

    /**
     * 查询和更新预留记录的出票状态
     * <AUTHOR>
     * @date   2018-01-30
     *
     * @param  int  $recordId  预留记录
     *
     * @return bool
     */
    private function _updateReserveRecordStatus($recordId, $remark = '')
    {
        $tmpList = $this->table($this->_reserve_seats)->where(['record_id' => $recordId])->field('reserve_status')->select();

        if ($tmpList === false) {
            //查询报错了
            return false;
        }

        if (!$tmpList) {
            //如果没有找到预留座位记录,认为都已经出票
            $recordStatus = 2;
        } else {
            //计算出票状态
            $statusList = array_column($tmpList, 'reserve_status');
            $statusList = array_unique($statusList);

            if (count($statusList) >= 2) {
                //部分出票
                $recordStatus = 1;
            } else if ($statusList[0] == 2) {
                //都已经出票
                $recordStatus = 2;
            } else {
                //都还没有出票
                $recordStatus = 0;
            }
        }

        //更新状态
        $where = ['id' => $recordId];
        $data  = ['status' => $recordStatus];
        if ($remark) {
            $data['desc'] = $remark;
        }

        $res = $this->table($this->_reserve_record)->where($where)->save($data);

        return $res === false ? false : true;
    }

    /**
     * 根据优先级座位，获取相同优先级的连续座位
     * <AUTHOR>
     * @date   2018-01-28
     *
     * @param  array  $priorityList  按行和优先级排序好的座位 - [['排号_优先级' => ['key' => '排号_优先级', 'list' => [$seat, $seat]]], []]
     * @param  int  $seatNum  需要的座位数
     *
     * @return array
     */
    private function _getPriovityContinueSeats($priorityList, $seatNum)
    {
        //先要按优先级和排数进行排序
        uasort($priorityList, function ($a, $b) {
            $aKeyTmp = explode('_', $a['key']);
            $bKeyTmp = explode('_', $b['key']);

            $aRowId    = $aKeyTmp[0];
            $aPriovity = $aKeyTmp[1];
            $bRowId    = $bKeyTmp[0];
            $bPriovity = $bKeyTmp[1];
            if (($aRowId == $bRowId) && ($aPriovity == $bPriovity)) {
                return 0;
            } else if ($aPriovity == $bPriovity) {
                return $aRowId > $bRowId ? 1 : -1;
            } else {
                return $aPriovity > $bPriovity ? 1 : -1;
            }
        });

        //对优先级的数据进行排序
        foreach ($priorityList as $tmpRowIdPriority => $tmpNode) {
            $tmpPriorityList = $tmpNode['list'];

            //如果只有一个座位，直接略过
            if (count($tmpPriorityList) == 1) {
                continue;
            }

            //将每个优先级的座位进行排序
            usort($tmpPriorityList, function ($a, $b) {
                if ($a['col_id'] == $b['col_id']) {
                    return 0;
                } else if ($a['col_id'] > $b['col_id']) {
                    return 1;
                } else {
                    return -1;
                }
            });

            //连续的标识
            $currentContinueSeats = [];
            $lastContinueSeats    = [];

            //找出每个优先级的连续的座位
            foreach ($tmpPriorityList as $tmpKey => $tmpItem) {
                if ($tmpKey > 0) {
                    if (($tmpPriorityList[$tmpKey]['col_id'] - 1) == $tmpPriorityList[$tmpKey - 1]['col_id']) {

                        $currentContinueSeats[] = $tmpItem['seat_id'];

                        //判断新的连续的座位数是不是比之前的多
                        if ($lastContinueSeats) {
                            if (count($currentContinueSeats) > count($lastContinueSeats)) {
                                $lastContinueSeats = $currentContinueSeats;
                            }
                        } else {
                            $lastContinueSeats = $currentContinueSeats;
                        }
                    } else {
                        $currentContinueSeats = [$tmpItem['seat_id']];
                    }

                    //如果当前的连座座位已经够了，直接返回
                    if (count($currentContinueSeats) >= $seatNum) {
                        return $currentContinueSeats;
                    }

                } else {
                    $currentContinueSeats = [$tmpItem['seat_id']];
                }
            }
        }

        //如果都没有连续的座位，直接返回空
        return [];
    }

    /**
     * 去除优先级后，获取连续座位
     * <AUTHOR>
     * @date   2018-01-28
     *
     * @param  array  $sortSeats  按行排序好的座位 - ['row_id' => [$seat, $seat]]
     * @param  int  $seatNum  需要的座位数
     *
     * @return array
     */
    private function _getCommonContinueSeats($rowSeats, $seatNum)
    {
        foreach ($rowSeats as $rowId => $colSeats) {
            //如果只有一个座位，直接略过
            if (count($colSeats) == 1) {
                continue;
            }

            //连续的标识
            $currentContinueSeats = [];
            $lastContinueSeats    = [];

            //找出每个优先级的连续的座位
            foreach ($colSeats as $tmpKey => $tmpItem) {
                if ($tmpKey > 0) {
                    if (($colSeats[$tmpKey]['col_id'] - 1) == $colSeats[$tmpKey - 1]['col_id']) {

                        $currentContinueSeats[] = $tmpItem['seat_id'];

                        //判断新的连续的座位数是不是比之前的多
                        if ($lastContinueSeats) {
                            if (count($currentContinueSeats) > count($lastContinueSeats)) {
                                $lastContinueSeats = $currentContinueSeats;
                            }
                        } else {
                            $lastContinueSeats = $currentContinueSeats;
                        }
                    } else {
                        $currentContinueSeats = [$tmpItem['seat_id']];
                    }

                    //如果当前的连座座位已经够了，直接返回
                    if (count($currentContinueSeats) >= $seatNum) {
                        return $currentContinueSeats;
                    }

                } else {
                    $currentContinueSeats = [$tmpItem['seat_id']];
                }
            }
        }

        //如果都没有连续的座位，直接返回空
        return [];
    }

    /**
     * 通过场次排序id获取场次信息
     * <AUTHOR>
     * @date   2018-04-19
     *
     * @param  string  $name  场次ID
     * @param  string  $field  查询字段
     * @param  int  $applyID  $loginInfo['sid']
     *
     *
     * @return array
     */
    public function getRoundInfoByName($name, $field = 'id', $applyID = false)
    {
        if (empty($name) || empty($applyID)) {
            return [];
        }

        $filter = [
            'round_sort_id' => $name,
            'status'        => 0,
        ];

        if ($applyID != 1) {
            $filter['opid'] = $applyID;
        }

        $res = $this->table($this->_pft_round)->field($field)->where($filter)->select();

        return $res ? $res : [];
    }

    /**
     * 获取用户场次订单号
     *
     * @date   2018-04-19
     * <AUTHOR>
     *
     * @param  string  $field  搜索条件字段
     * @param  array  $roundIds  场次id集合
     *
     * @return array
     */
    public function getUseRound($field, $roundIds)
    {
        if (!$field || !$roundIds || !is_array($roundIds)) {
            return [];
        }

        $where = [
            'round_id' => ['in', $roundIds],
        ];
        $res   = $this->table($this->_pft_seat_jit)->field($field)->where($where)->select();

        return $res ?: [];
    }

    /**
     * 批量更新座区数据
     *
     * @date   2018-06-14
     * <AUTHOR>
     *
     * @param  int  $roundId  场次ID
     * @param  array  $seatIds  场次座区ID条件
     * @param  int  $status  需要修改成的 场次状态
     *
     * @return array
     */
    public function updateRoundBatch($roundId, $seatIds, $status = 5)
    {
        if (!$roundId || !is_array($seatIds) || empty($seatIds)) {
            return false;
        }

        //条件
        $where = [
            'round_id' => $roundId,
            'seat_id'  => ['in', $seatIds],
            'zone_id'  => ['gt', 0],
            'status'   => ['not in', '1,2,3,5'], //排除 1=预留 2=锁定 3=已售出 5=不可售
        ];
        //需要修改的参数
        $data = [
            'status' => $status,
        ];

        //获取场次更新锁
        $roundInfo = $this->table($this->_pft_round)->where(['id' => $roundId])->find();
        if (!$roundInfo) {
            return false;
        }
        $version = $roundInfo['version'];

        $this->startTrans();
        //更新座区状态
        $res = $this->table($this->_pft_seat_jit)->where($where)->save($data);
        if (!$res) {
            $this->rollback();

            return false;
        }

        //同步座区剩余座位
        $collectRes = $this->_updateRoundCollect($roundId, $version);
        if (!$collectRes) {
            $this->rollback();

            return false;
        }

        $this->commit();

        return true;
    }

    /**
     * 通过场馆获取指定日期时间段可以售卖的场次数据
     * <AUTHOR>  Li
     * @date   2021-04-07
     *
     * @param  int  $venusId  场馆id
     * @param  string  $startUseDate  日期 2021-04-02提前预售开始日期
     * @param  string  $endUseDate  日期 2021-04-02提前预售结束日期
     * @param  int  $presale  提前预售日期
     * @param  array  $statusArr  场次状态 0启用 2禁用 3作废
     *
     * @return array
     */
    public function getRoundInfoByVenusIdAndDate($venusId, $startUseDate, $endUseDate, $presale = 0, $statusArr = [])
    {
        if (!$venusId || !$startUseDate || !$endUseDate) {
            return [];
        }

        $map = [
            'venus_id' => $venusId,
            'use_date' => ['between', [$startUseDate, $endUseDate]],
            'pre_sale' => ['egt', $presale],
            'status'   => 0,
        ];

        if ($statusArr) {
            $map['status'] = ['in', $statusArr];
        }

        $field = 'id, venus_id, round_name, bt, et, use_date, pre_sale, status, seat_storage, seat_reverse, seat_left, round_sort_id';

        $data = $this->table($this->_pft_round)->field($field)->where($map)->select();

        return $data;
    }

    /**
     * 通过分区名称获取场馆下的分区信息
     *
     * @param  int  $venueId  场馆id
     * @param  array  $zoneNameArr  分区名称数组
     *
     * @return array
     */
    public function getRoundZoneByName(int $venueId, array $zoneNameArr)
    {
        if (!$venueId || !$zoneNameArr) {
            return [];
        }

        $where = [
            'venue_id' => $venueId,
            'zone_name' => ['in', $zoneNameArr],
            'state'     => 0,
        ];

        $zoneMap = $this->table($this->_pft_roundzone)->where($where)->select();

        return $zoneMap?: [];
    }

    /**
     * 通过座位ID获取场次对应的分区id
     *
     * @param  int  $roundId  场次id
     * @param  array  $seatIdList  座位id
     *
     * @return array
     */
    public function getSeatListBySeats($roundId, $seatIdList)
    {
        if (!$roundId || !$seatIdList) {
            return [];
        }

        $field = 'jit.zone_id';
        $table = "{$this->_pft_seat_jit} as jit";
        $join  = "{$this->_pft_roundseat} as seat ON jit.seat_id = seat.id";
        $where = ['round_id' => $roundId, 'seat_id' => ['in', $seatIdList]];

        $list = $this->table($table)->where($where)->join($join, 'left')->field($field)->group('zone_id')->select();

        return $list ?: [];
    }

    /**
     * 获取新创建的场次列表
     *
     * @param  int  $venueId  场馆id
     * @param  string  $field  字段
     * @param  int  $status  状态
     * @param  array  $useDateArr  适用日期数组
     * @param  int  $isNew  新旧版 0旧版 1新版
     *
     * @return bool|mixed
     */
    public function getNewRoundList($venueId, $useDateArr, $roundNameArr, $status = false, $isNew = 1, $field = 'id, use_date')
    {
        if (!$venueId || !$useDateArr || !$roundNameArr || !$field) {
            return false;
        }

        //过滤条件
        $filter = [
            'venus_id'   => $venueId,
            'is_new'     => $isNew,
            'use_date'   => ['in', $useDateArr],
            'round_name' => ['in', $roundNameArr],
        ];

        if ($status !== false) {
            $filter['status'] = $status;
        }

        $res = $this->table($this->_pft_round)->field($field)->where($filter)->select();

        return $res;
    }

    /**
     * 获取场次对应座位的预留记录id对应的所有座位id
     *
     * @param  int  $roundId
     * @param  array  $seatIdList
     *
     * @return array
     */
    public function getSeatReserveList($roundId, $seatIdList, $status = 1)
    {
        if (!$roundId || !$seatIdList) {
            return [];
        }

        $where = [
            'round_id'       => $roundId,
            'seat_id'        => ['in', $seatIdList],
            'reserve_status' => is_array($status) ? ['in', $status] : $status,
        ];

        $recordIdList = $this->table($this->_reserve_seats)->where($where)->field('record_id')->select();
        //获取预留记录对应的座位号
        $recordList = [];
        if ($recordIdList) {
            $recordList = $this->table($this->_reserve_seats)->where(['record_id' => ['in', array_column($recordIdList, 'record_id')]])->select();
        }

        return $recordList;
    }

    /**
     * 释放预留座位记录
     *
     * @param  int  $recordId
     * @param  int  $roundId
     * @param  array  $seatList
     *
     * @return bool
     */
    public function releaseReserveDetail($recordId, $roundId, $seatList, $remark = '')
    {
        $where = [
            'record_id' => $recordId,
            'round_id'  => $roundId,
            'seat_id'   => ['in', $seatList],
        ];

        $this->startTrans();

        //释放座位直接将座位删除
        $res = $this->table($this->_reserve_seats)->where($where)->delete();

        if (!$res) {
            $this->rollback();
            return false;
        }

        //根据相应的汇总信息获取预留记录的状态
        $res = $this->_updateReserveRecordStatus($recordId, $remark);
        if (!$res) {
            $this->rollback();
            return false;
        }

        $this->commit();
        return true;
    }

    /**
     * 批量更新座位数据
     *
     * @param  int  $roundId  场次id
     * @param  array  $batchSaveData  分区座位状态更新数据
     * @param  bool  $isSync  是否场次同步操作
     *
     * @return bool
     */
    public function batchUpdateRoundExt($roundId, $batchSaveData, $isSync = false)
    {
        if (!$roundId || !$batchSaveData) {
            return false;
        }

        //获取场次更新锁
        $roundInfo = $this->table($this->_pft_round)->where(['id' => $roundId])->find();
        if (!$roundInfo) {
            return false;
        }
        $version = $roundInfo['version'];

        $this->startTrans();

        //更新座位信息
        foreach ($batchSaveData as $zoneId => $oldStatusInfo) {
            //需要将之前的状态当做条件 防止状态不一致的情况 误更新数据了
            foreach ($oldStatusInfo as $oldStatus => $seatInfo) {
                //按分区id及座位id去批量更新
                foreach ($seatInfo as $status => $seatIdList) {
                    $where = [
                        'round_id' => $roundId,
                        'zone_id'  => $zoneId,
                        'status'   => $oldStatus,
                        'seat_id'  => ['in', $seatIdList],
                    ];

                    $data = [
                        'status'      => $status,
                        'update_time' => time(),
                    ];

                    $res = $this->table($this->_pft_seat_jit)->where($where)->save($data);

                    //如果是同步操作的情况 且更新数量为0或者更新失败 直接返回false
                    if ($isSync && !$res) {
                        $this->rollback();

                        return false;
                    }

                    if ($res === false) {
                        $this->rollback();

                        return false;
                    }
                }
            }
        }

        $collectRes = $this->_updateRoundCollect($roundId, $version);
        if (!$collectRes) {
            $this->rollback();

            return false;
        }

        $this->commit();

        return true;
    }
}
