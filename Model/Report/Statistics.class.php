<?php
/**
 * 报表数据计算模型
 *
 * <AUTHOR>
 * @date   2016-07-26
 */

namespace Model\Report;

use Library\Model;
use Model\Member\Member;
use Model\Order\BuyChain;
use Model\Order\Coupon;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\Order\OrderTrack;
use Model\Order\SubOrderQuery\SubOrderSplit;
use Business\Statistics\StatisticsHandle as StatisticsHandleBiz;

class Statistics extends Model
{
    //检票表
    private $_checkTable = 'pft_report_checked';
    //取消报表
    private $_cancelTable = 'pft_report_cancel';
    //撤销报表
    private $_revokeTable = 'pft_report_revoke';
    //预定销售报表
    private $_preOrderTable = 'pft_report_order';
    //套票预订报表
    private $_orderPack = 'pft_report_pack_order';
    //第二版预订报表 预订 - 取消 - 撤销
    private $_orderTwo = 'pft_report_order_two';
    //第二版验证报表 验证 + 完结
    private $_checkTwo = 'pft_report_checked_two';
    //套票验证报表
    private $_checkedPack = 'pft_report_pack_checked';
    //云票务出票报表
    private $_ticketReport = 'pft_report_ticket';
    //云票务团队订单汇总
    private $_teamOrderPay = 'pft_report_team_pay';
    //团队订单验证汇总
    private $_teamCheckPay = 'pft_report_team_check';
    //第二版预订月报表 预订 - 取消 - 撤销
    private $_orderTwoMonth = 'pft_report_order_two_month';
    //第二版验证月报表 验证 + 完结
    private $_checkTwoMonth = 'pft_report_checked_two_month';
    //带验证方式的销售报表
    private $_payWayCheckTable = 'pft_report_checked_payway';
    //带验证方式的预定+取消报表
    private $_payWayOrderTable = 'pft_report_order_payway';
    //过期报表
    private $_expireTable = 'pft_report_expire';
    //未支付报表
    private $_unpaidTable = 'pft_report_unpaid';
    //过期完结报表
    private $_finishTable = 'pft_report_finish';
    //团队报表
    private $_teamOrderTable = 'pft_report_team_order';
    //团队验证报表
    private $_checkTeam = 'pft_report_team_check';
    //班结报表
    private $_classSettle = 'pft_report_class_settle';
    //班结取票报表
    private $_classTicketSettle = 'pft_report_class_ticket_settle';
    //演出预定报表
    private $_seriesOrderTable = 'pft_report_series_order';
    //演出预定月报表
    private $_seriesMonthOrderTable = 'pft_report_series_order_month';
    //演出验证报表
    private $_seriesCheckedTable = 'pft_report_series_checked';
    //演出验证月报表
    private $_seriesMonthCheckedTable = 'pft_report_series_checked_month';
    //服务器推送报表配置
    private $_reportPushConfig = 'pft_report_push_config';
    //服务器推送报表日志
    private $_reportPush = 'pft_report_push';
    //任务结果记录表
    private $_taskLogTable = 'pft_report_tasklog';
    //
    private $_separateOrder = 'pft_report_separate_order';

    private $_separateChedked = 'pft_report_separate_checked';
    //分终端汇总表
    private $_branchTerminalSummary = 'terminal_branch_summary';
    //分终端小时汇总表
    private $_branchTerminalHourSummary = 'terminal_branch_hour_summary';

    //待处理日期表
    private $_pendingDayTable = 'pft_report_pending_day';

    //数据归档日志表
    private $_archiverLogTable = 'pft_report_archiver_log';

    //年卡预订、激活报表表
    private $_annualCardOrderTable = 'pft_report_annual_card_order';

    //年卡验证报表
    private $_annualCardCheckedTable = 'pft_report_annual_card_checked';

    //分终端已经完成验证的订单记录
    private $_terminalCheckedOrderTable = 'terminal_checked_order';

    //终端过闸报表
    private $_terminalPassTable = 'pft_report_terminal_pass';
    //终端过闸小时报表
    private $_terminalPassHourTable = 'pft_report_terminal_pass_hour';

    //资源中心预定报表
    private $_resourceOrderTable      = 'pft_report_resource_order';
    private $_resourceOrderMonthTable = 'pft_report_resource_order_month';

    //按小时汇总
    private $_checkTwoHour = 'pft_report_checked_two_hour';
    private $_orderTwoHour = 'pft_report_order_two_hour';

    //预约汇总
    private $_orderReserve = 'pft_report_order_reserve';
    //特殊定制
    private $_customizeReport = 'pft_report_customize';
    //终端验证报表
    private $_terminalCheckedReport = 'pft_report_terminal_checked';

    //预订报表（分钟）
    private $_orderFiveMinuteReport = 'pft_report_order_five_minute';
    //验证报表（分钟）
    private $_checkFiveMinuteReport = 'pft_report_checked_five_minute';

    //预约日汇总
    private $_orderReserveDaily = 'pft_report_order_reserve_daily';

    //预约月报表
    private $_orderReserveMonth = 'pft_report_order_reserve_month';

    //演出预约日汇总
    private $_showOrderReserveDaily = 'pft_report_show_order_reserve_daily';

    //演出预约月报表
    private $_showOrderReserveMonth = 'pft_report_show_order_reserve_month';

    //常量 bi_summary_pf bi数据库配置标识
    const BI_SUMMARY_PF = 'bi_summary_pf';



    //错误日志目录
    private $_logPath = 'statistic_report';

    //开始有将末级购买多加一条记录到split表开始的时间戳 (现在先从2020-05-08 01:35:14开始)
    private $_newModelTime = 1588872914;

    //其他模型
    private $_couponModel    = null;
    private $_orderToolModel = null;
    private $_buyChainModel  = null;
    private $_trackModel     = null;
    private $Model           = null;

    //默认的散客
    private $_defaultReseller = 112;

    private $_resellerMap;

    //批量插入数据库的条数 - 达到这个数据的时候插入
    private $_insertLimit = 1000;

    //批量查询数据
    private $_selectSize = 2000;

    //每次批量处理的数据
    private $_pieceNum = 2000;

    //归档年份标识
    private $_yearMark = '';

    //数据归档操作结果 1-成功 0-失败
    const ARCHIVER_ACTION_SUCC = 1;
    const ARCHIVER_ACTION_FAIL = 0;

    //聚合方式数组
    private $_groupByArr = [
        'product',
        'ticket',
        'reseller',
        'channel',
        'fid',
        'date',
        'resellerAndTicket',
        'lidAndTicket',
        'lidAndTicketAndPayWay',
    ];
    //需要获取的ota分销商ID 4 先行 284 拉手 1214 携程 1454 同程 1472 驴妈妈 2175 去哪儿 2706 糯米 3034 途牛 28227 美团 51603 酷旅 148515 56人 225110 好票 236965 侠侣
    private $_needOtaReseller = [4, 284, 1214, 1454, 1472, 2175, 2706, 3034, 28227, 51603, 148515, 225110, 236965];

    //用户id列表
    public $fidArr = [];

    //初始化数据库
    public function __construct($db = 'summary', $yearMark = '')
    {
        $resellerMapConfig = load_config('reseller_map', 'trade_record');
        foreach ($resellerMapConfig as $key => $item) {
            $this->_resellerMap[$key] = $item['id'];
        }

        //统计数据放在统计库区
        parent::__construct($db);
        if ($yearMark) {
            $this->_yearMark = $yearMark;
        }
    }

    /**
     * 获取预定列表
     * <AUTHOR>
     * @date   2016-08-08
     *
     * @param  $beginDate 开始查询时间 - 2016-05-01
     * @param  $endDate 结束查询时间 - 2016-05-02
     * @param  $groupBy 数据聚合方式 - product:产品，ticket：门票，reseller：分销商，channel：渠道
     * @param  int  $page  页码 - 1
     * @param  int  $size  条数 - 200
     * @param  int/arr $fid 出售用户ID
     * @param  int/arr $level 所属层级
     * @param  int/arr $ticketId 门票ID
     * @param  int/arr $landId 景点ID
     * @param  int/arr $resellerId 分销商ID
     * @param  string  $excludeUids  需要排除掉的账号字符串 - 111,222,33,44
     * @param  int  $searchType  0分销商  1供应商
     *
     * @return
     */
    public function getOrderList($beginDate, $endDate, $groupBy = 'product', $page = 1, $size = 200, $fid = false, $level = false, $ticketId = false, $landId = false, $resellerId = false, $excludeUids = false, $searchType = 0)
    {
        //参数处理
        $params = $this->_formatParam('order_v2', $beginDate, $endDate, $groupBy, $page, $size, $fid, $level, $ticketId,
            $landId, $resellerId, $excludeUids, $searchType);
        //如果参数有问题的话
        if ($params === false) {
            return [];
        }

        $table = $this->_orderTwo;
        $total = $this->table($table)->where($params['where'])->count("DISTINCT {$params['ext_field']}");

        if (in_array($groupBy, ['resellerAndTicket', 'lidAndTicket'])) {
            //TP在对一个以上的字段使用distinct时 特殊处理下
            $total = key($total);
        }
        $list = $this->table($table)->where($params['where'])->group($params['group'])
                     ->order($params['order'])->page($params['page'])
                     ->field($params['field'])->select();

        $sum = $this->table($table)->where($params['where'])->field($params['field'])->find();

        //返回
        return ['total' => $total, 'list' => $list, 'sum' => $sum];
    }

    /**
     * @param $playDate
     * @param  int  $page
     * @param  int  $size
     */
    public function getTeamOrderList($playDate, $fid, $page = 1, $size = 15, $excel = false)
    {
        $filter = [
            'play_time' => date('Ymd', strtotime($playDate)),
            'fid'       => $fid,
        ];

        if ($excel) {
            $total = 0;
            $list  = $this->table($this->_teamOrderTable)->where($filter)->group('lid, tid, province, city, county, operate_id')
                          ->field('sum(order_num) as order_num, sum(order_ticket) as order_ticket, lid, tid, province, city, county, reseller_id')->select();
            $sum   = 0;
        } else {
            $total = $this->table($this->_teamOrderTable)->where($filter)->count('distinct(lid, tid, province, city, county, operate_id)');

            $list = $this->table($this->_teamOrderTable)->where($filter)->group('lid, tid, province, city, county, operate_id')
                         ->page($page, $size)
                         ->field('sum(order_num) as order_num, sum(order_ticket) as order_ticket, lid, tid, province, city, county, reseller_id')->select();

            $sum = $this->table($this->_teamOrderTable)->where($filter)->field('sum(order_num) as order_num, sum(order_ticket) as order_ticket')->find();
        }

        //返回
        return ['total' => $total, 'list' => $list, 'sum' => $sum];
    }

    public function getOrderV2List($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $notInArr = [], $isSonTicket = 0, $subSid = 0, $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_orderTwo . '_' . $this->_yearMark : $this->_orderTwo;

        $beginDate = date('Ymd', strtotime($beginDate));
        $endDate   = date('Ymd', strtotime($endDate));
        $filter    = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        //子商户
        $subSid = trim($subSid, ',');
        if (!empty($subSid)) {
            //子商户登录的时候需要过滤下
            if ($subSid == $fid) {
                $fid = 0;
                $filter['sub_merchant_id'] = $subSid;
            } else {
                $subSidArr = explode(',', $subSid);
                $filter['sub_merchant_id'] = ['in', $subSidArr];
            }
        }

        // 管理员查询权限
        if (!empty($fid)) {
            // 景区id $fid 有可能是数组， 这里需要判断
            if (is_array($fid)) {
                $filter['fid'] = ['in', $fid];
            } else {
                $fidArr = explode(',', $fid);
                // 判断是否模糊条件查询
                if (count($fidArr) > 1) {
                    $filter['fid'] = ['in', $fidArr];
                } else {
                    $filter['fid'] = $fid;
                }
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = ['in', $lid];
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($notInArr['not_lid'])) {
            $notLidArr = is_array($notInArr['not_lid']) ? $notInArr['not_lid'] : explode(',', $notInArr['not_lid']);
            !empty($notLidArr) && $filter['_complex'][] = ['lid' => ['not in', $notLidArr]];
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        if ($siteId !== false && $siteId != '') {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        $filterStr = "order_ticket <> 0 or revoke_ticket <> 0 or cancel_ticket <> 0 or after_sale_ticket_num <> 0";

        //新增取票数量统计print_num by jackchb 20200819
        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(cancel_num) as cancel_num,
        SUM(cancel_ticket) as cancel_ticket, SUM(revoke_num) as revoke_num, SUM(revoke_ticket) as revoke_ticket,
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money, SUM(cancel_cost_money) as cancel_cost_money,
        SUM(cancel_sale_money) as cancel_sale_money, SUM(revoke_cost_money) as revoke_cost_money,
        SUM(revoke_sale_money) as revoke_sale_money, SUM(service_money) as service_money, 
        SUM(after_sale_ticket_num) as after_sale_ticket_num, SUM(after_sale_refund_money) as after_sale_refund_money, 
        SUM(after_sale_income_money) as after_sale_income_money';

        //归档处理 ->归档移除 2020之前的移除取票统计
        if ($this->_judgePrintNum()) {
            $field     = $field . ', SUM(print_num) as print_num';
            $filterStr .= " or print_num <> 0";
        }

        !$isSonTicket && $filter['_string'] = $filterStr;

        //缺失逗号异常处理
        if (!empty($groupBy) && substr($groupBy, 0, 1) != ',') {
            $field = $field . ',' . $groupBy;
        }

        //子票维度
        if ($isSonTicket) {
            return $this->_getListByPackSonTicket($tableName, $filter, $field, $groupBy, $page, $size, $isExcel);
        }

        // if ($groupBy == 'reseller_id') {
        //     //主要针对分销商只有 散客、云票务散客这样的用户数据
        //     $baseSql = $this->table($tableName . ' FORCE INDEX(index_fid)')->where($filter)->group($groupBy);
        // } else {
            $baseSql = $this->table($tableName)->where($filter)->group($groupBy);
        // }

        if ($isExcel) {
            $list = $baseSql->field($field)->select();
        } else {
            $list = $baseSql->field($field)->page($page, $size)->select();
        }
        // var_Dump($filter, $this->getLastSql());exit;
        $list = is_array($list) ? $list : [];
        $sum  = $this->table($tableName)->where($filter)->field($field)->find();

        // if ($groupBy == 'reseller_id') {
        //     //主要针对分销商只有 散客、云票务散客这样的用户数据
        //     $subOrderSql = $this->table($tableName . ' FORCE INDEX(index_fid)')->where($filter)->field('id')->group($groupBy)->buildSql();
        // } else {
            $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
        // }

        $total = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    public function getOrderV2MonthList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $notInArr = [], $isSonTicket = 0, $subSid = 0, $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_orderTwoMonth . '_' . $this->_yearMark : $this->_orderTwoMonth;
        $beginDate = date('Ym', strtotime($beginDate));
        $endDate   = date('Ym', strtotime($endDate));
        $filter    = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        //子商户
        $subSid = trim($subSid, ',');
        if (!empty($subSid)) {
            //子商户登录的时候需要过滤下
            if ($subSid == $fid) {
                $fid = 0;
                $filter['sub_merchant_id'] = $subSid;
            } else {
                $subSidArr = explode(',', $subSid);
                $filter['sub_merchant_id'] = ['in', $subSidArr];
            }
        }

        // 管理员查询权限
        if (!empty($fid)) {
            // 景区id $fid 有可能是数组， 这里需要判断
            if (is_array($fid)) {
                $filter['fid'] = ['in', $fid];
            } else {
                $fidArr = explode(',', $fid);
                // 判断是否模糊条件查询
                if (count($fidArr) > 1) {
                    $filter['fid'] = ['in', $fidArr];
                } else {
                    $filter['fid'] = $fid;
                }
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = ['in', $lid];
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($notInArr['not_lid'])) {
            $notLidArr = is_array($notInArr['not_lid']) ? $notInArr['not_lid'] : explode(',', $notInArr['not_lid']);
            !empty($notLidArr) && $filter['_complex'][] = ['lid' => ['not in', $notLidArr]];
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        if ($siteId !== false && $siteId != '') {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        $filterStr = "order_ticket <> 0 or revoke_ticket <> 0 or cancel_ticket <> 0 or after_sale_ticket_num <> 0";

        //新增取票数量统计take_ticket_num by jackchb 20200819
        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(cancel_num) as cancel_num,
        SUM(cancel_ticket) as cancel_ticket, SUM(revoke_num) as revoke_num, SUM(revoke_ticket) as revoke_ticket,
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money, SUM(cancel_cost_money) as cancel_cost_money,
        SUM(cancel_sale_money) as cancel_sale_money, SUM(revoke_cost_money) as revoke_cost_money,
        SUM(revoke_sale_money) as revoke_sale_money, SUM(service_money) as service_money,
        SUM(after_sale_ticket_num) as after_sale_ticket_num, SUM(after_sale_refund_money) as after_sale_refund_money,
        SUM(after_sale_income_money) as after_sale_income_money';

        //归档处理 ->归档移除 2020之前的移除取票统计
        if ($this->_judgePrintNum()) {
            $field = $field . ', SUM(print_num) as print_num';
            $filterStr .= " or print_num <> 0";
        }
        !$isSonTicket && $filter['_string'] = $filterStr;

        //缺失逗号异常处理
        if (!empty($groupBy) && substr($groupBy, 0, 1) != ',') {
            $field = $field . ',' . $groupBy;
        }
        //子票维度处理
        if ($isSonTicket) {
            return $this->_getMonthListByPackSonTicket($tableName, $filter, $field, $groupBy, $page, $size, $isExcel);
        }

        if ($isExcel) {
            $list = $this->table($tableName)->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($tableName)->where($filter)->field($field)->page($page,
                $size)->group($groupBy)->select();
        }

        $list = is_array($list) ? $list : [];

        $sum         = $this->table($tableName)->where($filter)->field($field)->find();
        $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
        $total       = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    public function getOrderV2JoinCustomizeMonthList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $notInArr = [], $isSonTicket = 0, $subSid = 0, $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName      = $this->_yearMark ? $this->_orderTwoMonth . '_' . $this->_yearMark : $this->_orderTwoMonth;
        $customizeTable = 'pft_report_customize_month';

        $beginDate = date('Ym', strtotime($beginDate));
        $endDate   = date('Ym', strtotime($endDate));
        $filter    = [
            'o.date' => ['between', [$beginDate, $endDate]],
        ];

        $customizeFilter = ['cal_date' => ['between', [$beginDate, $endDate]],];

        //子商户
        $subSid = trim($subSid, ',');
        if (!empty($subSid)) {
            //子商户登录的时候需要过滤下
            if ($subSid == $fid) {
                $fid = 0;
                $filter['o.sub_merchant_id']        = $subSid;
                $customizeFilter['sub_merchant_id'] = $subSid;
            } else {
                $subSidArr = explode(',', $subSid);
                $filter['o.sub_merchant_id']        = ['in', $subSidArr];
                $customizeFilter['sub_merchant_id'] = ['in', $subSidArr];
            }
        }

        // 管理员查询权限
        if (!empty($fid)) {
            // 景区id $fid 有可能是数组， 这里需要判断
            if (is_array($fid)) {
                $filter['o.fid']        = ['in', $fid];
                $customizeFilter['fid'] = ['in', $fid];
            } else {
                $fidArr = explode(',', $fid);
                // 判断是否模糊条件查询
                if (count($fidArr) > 1) {
                    $filter['o.fid']        = ['in', $fidArr];
                    $customizeFilter['fid'] = ['in', $fidArr];
                } else {
                    $filter['o.fid']        = $fid;
                    $customizeFilter['fid'] = $fid;
                }
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['o.lid']        = ['in', $lid];
                $customizeFilter['lid'] = ['in', $lid];
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['o.lid']        = ['in', $lidArr];
                    $customizeFilter['lid'] = ['in', $lidArr];
                } else {
                    $filter['o.lid']        = $lid;
                    $customizeFilter['lid'] = $lid;
                }
            }
        }

        if (!empty($notInArr['not_lid'])) {
            $notLidArr = is_array($notInArr['not_lid']) ? $notInArr['not_lid'] : explode(',', $notInArr['not_lid']);
            !empty($notLidArr) && $filter['_complex'][] = ['o.lid' => ['not in', $notLidArr]];
        }

        if (!empty($pid)) {
            $pidArr                 = explode(',', $pid);
            $filter['o.pid']        = ['in', $pidArr];
            $customizeFilter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['o.pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr                 = explode(',', $tid);
            $filter['o.tid']        = ['in', $tidArr];
            $customizeFilter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['o.tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup                  = explode(',', $resellerGroup);
            $filter['o.reseller_id']        = ['in', $resellerGroup];
            $customizeFilter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr                  = explode(',', $resellerId);
            $filter['o.reseller_id']        = ['in', $resellerIdArr];
            $customizeFilter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr                 = explode(',', $channel);
            $filter['o.channel']        = ['in', $channelArr];
            $customizeFilter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr                  = explode(',', $operateId);
            $filter['o.operate_id']        = ['in', $operateIdArr];
            $customizeFilter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr                  = explode(',', $payWay);
            $filter['o.pay_way']        = ['in', $payWayArr];
            $customizeFilter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['o.level']        = $level;
            $customizeFilter['level'] = $level;
        }

        if ($siteId !== false && $siteId != '') {
            $siteId                     = explode(',', $siteId);
            $filter['o.site_id']        = ['in', $siteId];
            $customizeFilter['site_id'] = ['in', $siteId];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filterSonTicket = $this->filterSonTicket([], $extFilter);
        if (isset($filterSonTicket['main_tid'])) {
            $filter['o.main_tid']        = $filterSonTicket['main_tid'];
            $customizeFilter['main_tid'] = $filterSonTicket['main_tid'];
        }
        if (isset($filterSonTicket['main_type'])) {
            $filter['o.main_type']        = $filterSonTicket['main_type'];
            $customizeFilter['main_type'] = $filterSonTicket['main_type'];
        }

        $filterStr = "o.order_ticket <> 0 or o.revoke_ticket <> 0 or o.cancel_ticket <> 0 or o.after_sale_ticket_num <> 0";
        if ($this->_judgePrintNum()) {
            $filterStr .= " or o.print_num <> 0";
        }
        !$isSonTicket && $filter['_string'] = $filterStr;

        //子票维度
        if ($isSonTicket) {
            return $this->_getJoinCustomizeListByPackSonTicket($tableName, $customizeTable, $groupBy, $filter, $customizeFilter, $page, $size, $isExcel);
        }

        $firstSubField = 'o.date, o.fid, o.reseller_id, o.lid, o.tid, o.pid, o.level, o.operate_id, o.pay_way, o.channel, o.site_id, o.main_tid, o.sub_merchant_id, o.main_type,
                        SUM(o.order_num) as order_num, SUM(o.order_ticket) as order_ticket, SUM(o.cancel_num) as cancel_num,
                        SUM(o.cancel_ticket) as cancel_ticket, SUM(o.revoke_num) as revoke_num, SUM(o.revoke_ticket) as revoke_ticket,
                        SUM(o.cost_money) as cost_money, SUM(o.sale_money) as sale_money, SUM(o.cancel_cost_money) as cancel_cost_money,
                        SUM(o.cancel_sale_money) as cancel_sale_money, SUM(o.revoke_cost_money) as revoke_cost_money, 
                        SUM(o.revoke_sale_money) as revoke_sale_money, SUM(o.service_money) as service_money,
                        SUM(o.after_sale_ticket_num) as after_sale_ticket_num,SUM(o.after_sale_refund_money) as after_sale_refund_money, 
                        SUM(o.after_sale_income_money) as after_sale_income_money';
        if ($this->_judgePrintNum()) {
            $firstSubField .= ", SUM(o.print_num) as print_num";
        }
        $firstGroupBY = $groupBy;
        if (strstr($firstGroupBY, 'operate_id') === false) {
            $firstGroupBY .= ',operate_id';
        }

        $firstSubSql = $this->table($tableName . ' o')
                            ->field($firstSubField)
                            ->where($filter)
                            ->group($firstGroupBY)
                            ->buildSql();

        $secondSubField = 'cal_date, fid, reseller_id, lid, tid, pid, lvl, main_tid, sub_merchant_id, operate_id, pay_way, channel, site_id, SUM(window_real_money) window_real_money, 
                        SUM(cast(window_cancel_money as signed) - cast(window_cancel_service_money as signed)) window_cancel_money';

        $customizeGroup = str_replace('date', 'cal_date', $groupBy);
        $customizeGroup = str_replace('level', 'lvl', $customizeGroup);

        $secondSubSql = $this->table($customizeTable)
                             ->field($secondSubField)
                             ->where($customizeFilter)
                             ->group($customizeGroup)
                             ->buildSql();

        $subField = 'o.date, o.fid, o.reseller_id, o.lid, o.tid, o.pid, o.level, o.operate_id, o.pay_way, o.channel, o.site_id, o.main_tid, o.sub_merchant_id,
                    order_num, order_ticket, cancel_num, cancel_ticket, revoke_num, revoke_ticket, cost_money, sale_money,
                    cancel_cost_money, cancel_sale_money, revoke_cost_money, revoke_sale_money, service_money, 
                    window_real_money, window_cancel_money, after_sale_ticket_num, after_sale_refund_money, after_sale_income_money';
        if ($this->_judgePrintNum()) {
            $subField .= ", print_num";
        }

        $joinCondition = ' c.cal_date = o.date AND c.fid = o.fid AND c.reseller_id = o.reseller_id AND c.lid = o.lid 
                            AND c.tid = o.tid AND c.pid = o.pid AND c.lvl = o.level AND c.operate_id = o.operate_id AND 
                            c.pay_way = o.pay_way AND c.channel = o.channel AND c.site_id = o.site_id AND c.main_tid = o.main_tid AND c.sub_merchant_id = o.sub_merchant_id';

        $subSql = $this->table($firstSubSql . ' o')
                       ->join($secondSubSql . ' c ON ' . $joinCondition, 'LEFT')
                       ->field($subField)
                       ->where($filter)
                       ->buildSql();

        $field = 'SUM(a.order_num) as order_num, SUM(a.order_ticket) as order_ticket, SUM(a.cancel_num) as cancel_num,
            SUM(a.cancel_ticket) as cancel_ticket, SUM(a.revoke_num) as revoke_num, SUM(a.revoke_ticket) as revoke_ticket,
            SUM(a.cost_money) as cost_money, SUM(a.sale_money) as sale_money, SUM(a.cancel_cost_money) as cancel_cost_money,
            SUM(a.cancel_sale_money) as cancel_sale_money, SUM(a.revoke_cost_money) as revoke_cost_money, 
            SUM(a.revoke_sale_money) as revoke_sale_money, SUM(a.service_money) as service_money, 
            SUM(a.window_real_money) as window_real_money, SUM(a.window_cancel_money) as window_cancel_money,
            SUM(a.after_sale_ticket_num) as after_sale_ticket_num, SUM(a.after_sale_refund_money) as after_sale_refund_money,
            SUM(a.after_sale_income_money) as after_sale_income_money';

        //归档处理 ->归档移除 2020之前的移除取票统计
        if ($this->_judgePrintNum()) {
            $field = $field . ', SUM(a.print_num) as print_num';
        }

        //缺失逗号异常处理
        if (!empty($groupBy) && substr($groupBy, 0, 1) != ',') {
            $tmpGroup    = explode(',', $groupBy);
            $newTmpGroup = [];
            foreach ($tmpGroup as $tmp) {
                $newTmpGroup[] = 'a.' . $tmp;
            }

            if (!empty($newTmpGroup)) {
                $groupBy = implode(',', $newTmpGroup);
            }

            $field = $field . ',' . $groupBy;
        }

        if ($isExcel) {
            $queryRes = $this->table($subSql . ' a')->field($field)->group($groupBy)->select();
        } else {
            $queryRes = $this->table($subSql . ' a')->field($field)->group($groupBy)->page($page, $size)->select();
        }

        $list = is_array($queryRes) ? $queryRes : [];
        foreach ($list as $key => $tmp) {
            if (empty($tmp['window_real_money'])) {
                $list[$key]['window_real_money'] = 0;
            }
            if (empty($tmp['window_cancel_money'])) {
                $list[$key]['window_cancel_money'] = 0;
            }
        }

        //总计数据
        $sumRes = $this->table($subSql . ' a')->field($field)->find();
        $sum    = is_array($sumRes) ? $sumRes : [];

        // 总条数
        $subTotalSql = $this->table($subSql . ' a')->field('a.date')->group($groupBy)->buildSql();
        $total       = $this->table($subTotalSql . ' t')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 获取资源中心预定报表数据
     * @author: zhangyz
     * @date: 2020/4/26
     *
     * @param $beginDate
     * @param $endDate
     * @param  string  $fid
     * @param  string  $lid
     * @param  string  $pid
     * @param  string  $tid
     * @param  string  $resellerGroup
     * @param  string  $resellerId
     * @param  string  $channel
     * @param  string  $operateId
     * @param  string  $payWay
     * @param  string  $groupBy
     * @param  string  $level
     * @param  int  $page
     * @param  int  $size
     * @param  bool  $isExcel
     * @param  bool  $siteId
     *
     * @return array
     */
    public function getResourceOrderList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $province = 0, $city = 0, $notInArr)
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_resourceOrderTable . '_' . $this->_yearMark : $this->_resourceOrderTable;

        $beginDate = date('Ymd', strtotime($beginDate));
        $endDate   = date('Ymd', strtotime($endDate));
        $filter    = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        // 管理员查询权限
        if (!empty($fid)) {
            // 景区id $fid 有可能是数组， 这里需要判断
            if (is_array($fid)) {
                $filter['fid'] = $fid;
            } else {
                $fidArr = explode(',', $fid);
                // 判断是否模糊条件查询
                if (count($fidArr) > 1) {
                    $filter['fid'] = ['in', $fidArr];
                } else {
                    $filter['fid'] = $fid;
                }
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = $lid;
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        if ($siteId !== false && $siteId != '') {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        if ($province > 0) {
            $province           = explode(',', $province);
            $filter['province'] = ['in', $province];
        }

        if ($city > 0) {
            $city           = explode(',', $city);
            $filter['city'] = ['in', $city];
        }

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(cancel_num) as cancel_num,
        SUM(cancel_ticket) as cancel_ticket, SUM(revoke_num) as revoke_num, SUM(revoke_ticket) as revoke_ticket,
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money, SUM(cancel_cost_money) as cancel_cost_money,
        SUM(cancel_sale_money) as cancel_sale_money, SUM(revoke_cost_money) as revoke_cost_money,
        SUM(revoke_sale_money) as revoke_sale_money, SUM(service_money) as service_money';
        if (!empty($groupBy)) {
            $field = $field . ',' . $groupBy;
        }

        if ($isExcel) {
            $list = $this->table($tableName)->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($tableName)->where($filter)->field($field)->page($page,
                $size)->group($groupBy)->select();
        }

        $list = is_array($list) ? $list : [];

        $sum = $this->table($tableName)->where($filter)->field($field)->find();

        $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
        $total       = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 获取资源中心预定月报表数据
     * @author: zhangyz
     * @date: 2020/4/26
     *
     * @param $beginDate
     * @param $endDate
     * @param  string  $fid
     * @param  string  $lid
     * @param  string  $pid
     * @param  string  $tid
     * @param  string  $resellerGroup
     * @param  string  $resellerId
     * @param  string  $channel
     * @param  string  $operateId
     * @param  string  $payWay
     * @param  string  $groupBy
     * @param  string  $level
     * @param  int  $page
     * @param  int  $size
     * @param  bool  $isExcel
     * @param  bool  $siteId
     *
     * @return array
     */
    public function getResourceOrderMonthList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $province = 0, $city = 0, $notInArr)
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_resourceOrderMonthTable . '_' . $this->_yearMark : $this->_resourceOrderMonthTable;
        $beginDate = date('Ym', strtotime($beginDate));
        $endDate   = date('Ym', strtotime($endDate));
        $filter    = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        // 管理员查询权限
        if (!empty($fid)) {
            // 景区id $fid 有可能是数组， 这里需要判断
            if (is_array($fid)) {
                $filter['fid'] = $fid;
            } else {
                $fidArr = explode(',', $fid);
                // 判断是否模糊条件查询
                if (count($fidArr) > 1) {
                    $filter['fid'] = ['in', $fidArr];
                } else {
                    $filter['fid'] = $fid;
                }
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = $lid;
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        if ($siteId !== false && $siteId != '') {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        if ($province > 0) {
            $province           = explode(',', $province);
            $filter['province'] = ['in', $province];
        }

        if ($city > 0) {
            $city           = explode(',', $city);
            $filter['city'] = ['in', $city];
        }

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(cancel_num) as cancel_num,
        SUM(cancel_ticket) as cancel_ticket, SUM(revoke_num) as revoke_num, SUM(revoke_ticket) as revoke_ticket,
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money, SUM(cancel_cost_money) as cancel_cost_money,
        SUM(cancel_sale_money) as cancel_sale_money, SUM(revoke_cost_money) as revoke_cost_money,
        SUM(revoke_sale_money) as revoke_sale_money, SUM(service_money) as service_money';
        if (!empty($groupBy)) {
            $field = $field . ',' . $groupBy;
        }

        if ($isExcel) {
            $list = $this->table($tableName)->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($tableName)->where($filter)->field($field)
                         ->page($page, $size)->group($groupBy)->select();
        }

        $list = is_array($list) ? $list : [];

        $sum         = $this->table($tableName)->where($filter)->field($field)->find();
        $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
        $total       = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    public function getCheckV2List($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $notInArr=[], $isSonTicket = 0, $subSid = 0, $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_checkTwo . '_' . $this->_yearMark : $this->_checkTwo;

        $beginDate = date('Ymd', strtotime($beginDate));
        $endDate   = date('Ymd', strtotime($endDate));
        $filter    = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        //子商户
        $subSid = trim($subSid, ',');
        if (!empty($subSid)) {
            //子商户登录的时候需要过滤下
            if ($subSid == $fid) {
                $fid = 0;
                $filter['sub_merchant_id'] = $subSid;
            } else {
                $subSidArr = explode(',', $subSid);
                $filter['sub_merchant_id'] = ['in', $subSidArr];
            }
        }

        // 管理员查询权限
        if (!empty($fid)) {
            // 景区id $fid 有可能是数组， 这里需要判断
            if (is_array($fid)) {
                $filter['fid'] = ['in', $fid];
            } else {
                $fidArr = explode(',', $fid);
                // 判断是否模糊条件查询
                if (count($fidArr) > 1) {
                    $filter['fid'] = ['in', $fidArr];
                } else {
                    $filter['fid'] = $fid;
                }
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = ['in', $lid];
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($notInArr['not_lid'])) {
            $notLidArr = is_array($notInArr['not_lid']) ? $notInArr['not_lid'] : explode(',', $notInArr['not_lid']);
            !empty($notLidArr) && $filter['_complex'][] = ['lid' => ['not in', $notLidArr]];
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        //站点
        if ($siteId !== false && $siteId != '') {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        !$isSonTicket && $filter['_string']  = "order_ticket <> 0 or revoke_ticket <> 0 or finish_ticket <> 0 or after_sale_ticket_num <> 0";

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, 
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money,
        SUM(finish_num) as finish_num, SUM(finish_ticket) as finish_ticket,
        SUM(finish_cost_money) as finish_cost_money, SUM(finish_sale_money) as finish_sale_money,
        SUM(revoke_ticket) as revoke_ticket, SUM(revoke_num) as revoke_num,
        SUM(revoke_cost_money) as revoke_cost_money, SUM(revoke_sale_money) as revoke_sale_money,today_check,
        SUM(service_money) AS service_money, SUM(after_sale_ticket_num) as after_sale_ticket_num,
        SUM(after_sale_refund_money) as after_sale_refund_money, SUM(after_sale_income_money) as after_sale_income_money';

        if (!empty($groupBy)) {
            $field = $field . ',' . $groupBy;
        }

        //子票维度处理
        if ($isSonTicket) {
            return $this->_getListByPackSonTicket($tableName, $filter, $field, $groupBy, $page, $size, $isExcel);
        }

        // if ($groupBy == 'reseller_id' && in_array(ENV, ['TEST', 'IS_PFT_GRAY', 'PRODUCTION'])) {
        //     //主要针对分销商只有 散客、云票务散客这样的用户数据
        //     $baseSql = $this->table($tableName . ' FORCE INDEX(index_fid)')->where($filter)->group($groupBy);
        // } else {
            $baseSql = $this->table($tableName)->where($filter)->group($groupBy);
        // }

        if ($isExcel) {
            $list = $baseSql->field($field)->select();
        } else {
            $list = $baseSql->field($field)->page($page, $size)->select();
        }

        $list = is_array($list) ? $list : [];
        $sum  = $this->table($tableName)->where($filter)->field($field)->find();

        // if ($groupBy == 'reseller_id' && in_array(ENV, ['TEST', 'IS_PFT_GRAY', 'PRODUCTION'])) {
        //     //主要针对分销商只有 散客、云票务散客这样的用户数据
        //     $subOrderSql = $this->table($tableName . ' FORCE INDEX(index_fid)')->where($filter)->field('id')->group($groupBy)->buildSql();
        // } else {
            $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
        // }

        // $subOrderSql = $baseSql->field('id')->buildSql();
        $total = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    public function getCheckV2MonthList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $notInArr = [], $isSonTicket = 0, $subSid = 0, $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_checkTwoMonth . '_' . $this->_yearMark : $this->_checkTwoMonth;
        $beginDate = date('Ym', strtotime($beginDate));
        $endDate   = date('Ym', strtotime($endDate));

        $filter = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        //子商户
        $subSid = trim($subSid, ',');
        if (!empty($subSid)) {
            //子商户登录的时候需要过滤下
            if ($subSid == $fid) {
                $fid = 0;
                $filter['sub_merchant_id'] = $subSid;
            } else {
                $subSidArr = explode(',', $subSid);
                $filter['sub_merchant_id'] = ['in', $subSidArr];
            }
        }

        // 管理员查询权限
        if (!empty($fid)) {
            // 景区id $fid 有可能是数组， 这里需要判断
            if (is_array($fid)) {
                $filter['fid'] = ['in', $fid];
            } else {
                $fidArr = explode(',', $fid);
                // 判断是否模糊条件查询
                if (count($fidArr) > 1) {
                    $filter['fid'] = ['in', $fidArr];
                } else {
                    $filter['fid'] = $fid;
                }
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = ['in', $lid];
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($notInArr['not_lid'])) {
            $notLidArr = is_array($notInArr['not_lid']) ? $notInArr['not_lid'] : explode(',', $notInArr['not_lid']);
            !empty($notLidArr) && $filter['_complex'][] = ['lid' => ['not in', $notLidArr]];
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        //站点
        if ($siteId !== false && $siteId != '') {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        !$isSonTicket && $filter['_string']  = "order_ticket <> 0 or revoke_ticket <> 0 or finish_ticket <> 0 or after_sale_ticket_num <> 0";

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(finish_num) as finish_num,
        SUM(finish_ticket) as finish_ticket, SUM(cost_money) as cost_money, SUM(sale_money) as sale_money,
        SUM(finish_cost_money) as finish_cost_money, SUM(finish_sale_money) as finish_sale_money, 
        SUM(revoke_ticket) as revoke_ticket, SUM(revoke_num) as revoke_num,
        SUM(revoke_cost_money) as revoke_cost_money, SUM(revoke_sale_money) as revoke_sale_money,
        SUM(service_money) AS service_money, SUM(after_sale_ticket_num) as after_sale_ticket_num,
        SUM(after_sale_refund_money) as after_sale_refund_money, SUM(after_sale_income_money) as after_sale_income_money';

        if (!empty($groupBy)) {
            $field = $field . ',' . $groupBy;
        }

        //子票维度处理
        if ($isSonTicket) {
            return $this->_getMonthListByPackSonTicket($tableName, $filter, $field, $groupBy, $page, $size, $isExcel);
        }

        if ($isExcel) {
            $list = $this->table($tableName)->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($tableName)->where($filter)->field($field)->page($page,
                $size)->group($groupBy)->select();
        }

        $list = is_array($list) ? $list : [];

        $sum = $this->table($tableName)->where($filter)->field($field)->find();

        $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
        $total       = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 获取套票主票, 主票获取是无条件直接获取或 子->主 通过tidArr获取
     * <AUTHOR>
     * @date 2018-05-30
     */
    public function getPackMainList($beginDate, $endDate, $fid = '', $level = '', $groupBy = '', $page = 1, $size = 15, $isExcel = false, $type = 'order', $siteId = false)
    {

        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $beginDate = date('Ymd', strtotime($beginDate));
        $endDate   = date('Ymd', strtotime($endDate));
        $filter    = [
            'date'    => ['between', [$beginDate, $endDate]],
            'is_main' => 1,
        ];

        // 管理员增加查询权限
        if (!empty($fid)) {
            $filter['fid'] = $fid;
        }

        // 含转分销， 如果含转分销则只取转分销否则取level = 1
        if (!empty($level)) {
            $filter['level'] = $level;
        }

        //站点
        if ($siteId !== false) {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        $field = 'tid';

        switch ($type) {
            case 'order':
                $table = $this->_orderPack;
                break;
            case 'check':
                $table = $this->_checkedPack;
                break;
            default:
                $table = $this->_orderPack;
                break;
        }

        $list = $this->table($table)->where($filter)->field($field)->group($groupBy)->select();

        $subOrderSql = $this->table($table)->where($filter)->field('id')->group($groupBy)->buildSql();
        $total       = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total];
    }

    /**
     * 获取套票子票
     * <AUTHOR>
     * @date 2018-05-31
     */
    public function getPackKidList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '',
        $resellerId = '', $channel = '', $operateId = '', $payWay = '', $groupBy = '', $level = '', $page = 1, $size = 15,
        $isExcel = false, $type = 'order', $siteId = false, $notInArr = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $beginDate = date('Ymd', strtotime($beginDate));
        $endDate   = date('Ymd', strtotime($endDate));
        $filter    = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        // 管理员查询权限
        if (!empty($fid)) {
            // 景区id $fid 有可能是数组， 这里需要判断
            if (is_array($fid)) {
                $filter['fid'] = ['in', $fid];
            } else {
                $fidArr = explode(',', $fid);
                // 判断是否模糊条件查询
                if (count($fidArr) > 1) {
                    $filter['fid'] = ['in', $fidArr];
                } else {
                    $filter['fid'] = $fid;
                }
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = ['in', $lid];
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($notInArr['not_lid'])) {
            $notLidArr = is_array($notInArr['not_lid']) ? $notInArr['not_lid'] : explode(',', $notInArr['not_lid']);
            !empty($notLidArr) && $filter['_complex'][] = ['lid' => ['not in', $notLidArr]];
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        if ($siteId !== false && $siteId != '') {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        $filter['main_tid'] = ['neq', 0];

        //新增取票数量统计take_ticket_num by jackchb 20200819
        switch ($type) {
            case 'order':
                $table = $this->_orderPack;
                $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(cancel_num) as cancel_num,
        SUM(cancel_ticket) as cancel_ticket, SUM(revoke_num) as revoke_num, SUM(revoke_ticket) as revoke_ticket,
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money, SUM(cancel_cost_money) as cancel_cost_money,
        SUM(cancel_sale_money) as cancel_sale_money, SUM(revoke_cost_money) as revoke_cost_money,
        SUM(revoke_sale_money) as revoke_sale_money, SUM(service_money) as service_money,
        SUM(print_num) as print_num';
                break;
            case 'check':
                $table = $this->_checkedPack;
                $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(finish_num) as finish_num,
        SUM(finish_ticket) as finish_ticket, SUM(cost_money) as cost_money, SUM(sale_money) as sale_money, 
        SUM(finish_cost_money) as finish_cost_money, SUM(finish_sale_money) as finish_sale_money, SUM(revoke_ticket) as revoke_ticket, SUM(revoke_num) as revoke_num, 
        SUM(revoke_cost_money) as revoke_cost_money, SUM(revoke_sale_money) as revoke_sale_money, SUM(service_money) as service_money';
                break;
            default:
                $table = $this->_orderPack;
                $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(cancel_num) as cancel_num,
        SUM(cancel_ticket) as cancel_ticket, SUM(revoke_num) as revoke_num, SUM(revoke_ticket) as revoke_ticket,
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money, SUM(cancel_cost_money) as cancel_cost_money,
        SUM(cancel_sale_money) as cancel_sale_money, SUM(revoke_cost_money) as revoke_cost_money,
        SUM(revoke_sale_money) as revoke_sale_money, SUM(service_money) as service_money,
        SUM(print_num) as print_num';
                break;
        }
        //缺失逗号异常处理
        if (!empty($groupBy) && substr($groupBy, 0, 1) != ',') {
            $field = $field . ',' . $groupBy;
        }

        $table = $this->_yearMark ? $table . '_' . $this->_yearMark : $table;

        //根据条件查出主票tid
        $tidField    = 'main_tid';
        $mainTidInfo = $this->table($table)->where($filter)->field($tidField)->group($groupBy)->select();

        if (!empty($mainTidInfo) && is_array($mainTidInfo)) {
            $mainTid = array_column($mainTidInfo, 'main_tid');
        }

        if (empty($mainTid) || !is_array($mainTid)) {
            return ['list' => [], 'sum' => []];
        }

        $mainTid           = array_unique($mainTid);
        $mainTid           = implode(',', $mainTid);
        $filter['_string'] = "main_tid in ({$mainTid}) or tid in ({$mainTid})";
        if ($isExcel) {
            $list = $this->table($table)->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($table)->where($filter)->field($field)->page($page, $size)->group($groupBy)->select();
        }

        $subOrderSql = $this->table($table)->where($filter)->field($field)->group($groupBy)->buildSql();
        $total       = $this->table($subOrderSql . ' a')->count();

        $list = is_array($list) ? $list : [];

        $sum = $this->table($table)->where($filter)->field($field)->find();

        return ['list' => $list, 'sum' => $sum, 'total' => $total];
    }

    /**
     * 获取检票列表
     * <AUTHOR>
     * @date   2016-08-08
     *
     * @param  $beginDate 开始查询时间 - 2016-05-01
     * @param  $endDate 结束查询时间 - 2016-05-02
     * @param  $groupBy 数据聚合方式 - product:产品，ticket：门票，reseller：分销商，channel：渠道 fid: 供应商
     * @param  int  $page  页码 - 1
     * @param  int  $size  条数 - 200
     * @param  int/arr $fid 出售用户ID
     * @param  int/arr $level 所属层级
     * @param  int/arr $ticketId 门票ID
     * @param  int/arr $landId 景点ID
     * @param  int/arr $resellerId 分销商ID
     * @param  string  $excludeUids  需要排除掉的账号字符串 - 111,222,33,44
     * @param  string  $searchType  0分销商  1供应商
     *
     * @return
     */
    public function getCheckedList($beginDate, $endDate, $groupBy = 'product', $page = 1, $size = 200, $fid = false, $level = false, $ticketId = false, $landId = false, $resellerId = false, $excludeUids = false, $searchType = 0)
    {
        //参数处理
        $params = $this->_formatParam('checked_v2', $beginDate, $endDate, $groupBy, $page, $size, $fid, $level,
            $ticketId, $landId, $resellerId, $excludeUids, $searchType);
        //如果参数有问题的话
        if ($params === false) {
            return [];
        }

        $table = $this->_checkTwo;
        $total = $this->table($table)->where($params['where'])->count("DISTINCT {$params['ext_field']}");

        if (in_array($groupBy, ['resellerAndTicket', 'lidAndTicket'])) {
            //TP在对一个以上的字段使用distinct时 特殊处理下
            $total = key($total);
        }

        $list = $this->table($table)->where($params['where'])->group($params['group'])
                     ->order($params['order'])->page($params['page'])
                     ->field($params['field'])->select();
        $sum  = $this->table($table)->where($params['where'])->field($params['field'])->find();

        foreach ($list as $key => $value) {
            $profitMoney                = $this->_countProfitMoney($value['sale_money'], $value['finish_sale_money'],
                $value['revoke_sale_money'], $value['cost_money'], $value['finish_cost_money'],
                $value['revoke_cost_money']);
            $list[$key]['profit_money'] = $profitMoney;
        }
        if ($sum) {
            $profitMoney         = $this->_countProfitMoney($sum['sale_money'], $sum['finish_sale_money'],
                $sum['revoke_sale_money'], $sum['cost_money'], $sum['finish_cost_money'],
                $sum['revoke_cost_money']);
            $sum['profit_money'] = $profitMoney;
        }

        //返回
        return ['total' => $total, 'list' => $list, 'sum' => $sum];
    }

    /**
     * 获取演出预定报表
     * <AUTHOR>
     * @date 2019-5-16
     */
    public function getSeriesOrderList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $seriesTimeBegin = 0, $seriesTimeEnd = 0, $notInArr = [], $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_seriesOrderTable . '_' . $this->_yearMark : $this->_seriesOrderTable;

        $beginDate = date('Ymd', strtotime($beginDate));
        $endDate   = date('Ymd', strtotime($endDate));
        $filter    = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        // 管理员查询权限
        if (!empty($fid)) {
            $filter['fid'] = $fid;
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = $lid;
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        if ($siteId !== false) {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        if ($seriesTimeBegin && $seriesTimeEnd) {
            $filter['series_time_begin'] = ['EGT', $seriesTimeBegin];
            $filter['series_time_end']   = ['ELT', $seriesTimeEnd];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(cancel_num) as cancel_num,
        SUM(cancel_ticket) as cancel_ticket, SUM(revoke_num) as revoke_num, SUM(revoke_ticket) as revoke_ticket,
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money, SUM(cancel_cost_money) as cancel_cost_money,
        SUM(cancel_sale_money) as cancel_sale_money, SUM(revoke_cost_money) as revoke_cost_money,
        SUM(revoke_sale_money) as revoke_sale_money, SUM(service_money) as service_money';
        if (!empty($groupBy)) {
            $field = $field . ',' . $groupBy;
        }

        if ($isExcel) {
            $list = $this->table($tableName)->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($tableName)->where($filter)->field($field)->page($page,
                $size)->group($groupBy)->select();
        }

        $list = is_array($list) ? $list : [];

        $sum = $this->table($tableName)->where($filter)->field($field)->find();

        $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
        $total       = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 获取演出预定报表（月汇总）
     * <AUTHOR>  Li
     * @date  2021-11-15
     */
    public function getSeriesMonthOrderList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $seriesTimeBegin = 0, $seriesTimeEnd = 0, $notInArr = [], $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_seriesMonthOrderTable . '_' . $this->_yearMark : $this->_seriesMonthOrderTable;

        $beginDate = date('Ym', strtotime($beginDate));
        $endDate   = date('Ym', strtotime($endDate));
        $filter    = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        // 管理员查询权限
        if (!empty($fid)) {
            $filter['fid'] = $fid;
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = $lid;
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        if ($siteId !== false) {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        if ($seriesTimeBegin && $seriesTimeEnd) {
            $filter['series_time_begin'] = ['EGT', $seriesTimeBegin];
            $filter['series_time_end']   = ['ELT', $seriesTimeEnd];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(cancel_num) as cancel_num,
        SUM(cancel_ticket) as cancel_ticket, SUM(revoke_num) as revoke_num, SUM(revoke_ticket) as revoke_ticket,
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money, SUM(cancel_cost_money) as cancel_cost_money,
        SUM(cancel_sale_money) as cancel_sale_money, SUM(revoke_cost_money) as revoke_cost_money,
        SUM(revoke_sale_money) as revoke_sale_money, SUM(service_money) as service_money';
        if (!empty($groupBy)) {
            $field = $field . ',' . $groupBy;
        }

        if ($isExcel) {
            $list = $this->table($tableName)->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($tableName)->where($filter)->field($field)->page($page,
                $size)->group($groupBy)->select();
        }

        $list = is_array($list) ? $list : [];

        $sum = $this->table($tableName)->where($filter)->field($field)->find();

        $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
        $total       = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 获取演出预定报表（场次汇总）
     * <AUTHOR>  Li
     * @date  2021-11-16
     */
    public function getSeriesPerformanceOrderList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $seriesTimeBegin = 0, $seriesTimeEnd = 0, $notInArr = [], $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_seriesOrderTable . '_' . $this->_yearMark : $this->_seriesOrderTable;

        $beginDate = date('Ymd', strtotime($beginDate));
        $endDate   = date('Ymd', strtotime($endDate));
        $filter    = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        // 管理员查询权限
        if (!empty($fid)) {
            $filter['fid'] = $fid;
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = $lid;
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        if ($siteId !== false) {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        if ($seriesTimeBegin && $seriesTimeEnd) {
            $filter['series_time_begin'] = ['EGT', $seriesTimeBegin];
            $filter['series_time_end']   = ['ELT', $seriesTimeEnd];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        $field = 'series_time,series_time_begin,series_time_end,a.date,SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(cancel_num) as cancel_num,
        SUM(cancel_ticket) as cancel_ticket, SUM(revoke_num) as revoke_num, SUM(revoke_ticket) as revoke_ticket,
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money, SUM(cancel_cost_money) as cancel_cost_money,
        SUM(cancel_sale_money) as cancel_sale_money, SUM(revoke_cost_money) as revoke_cost_money,
        SUM(revoke_sale_money) as revoke_sale_money, SUM(service_money) as service_money';
        if (!empty($groupBy)) {
            $field = $field . ',' . $groupBy;
        }

        $subField    = 'id, date,series_time_begin,series_time_end,lid,pid,main_tid,main_type,tid,reseller_id,operate_id,fid,site_id,pay_way,channel,order_num,order_ticket,cancel_num, 
        cancel_ticket,revoke_num,revoke_ticket,cost_money,sale_money,cancel_cost_money,cancel_sale_money,revoke_cost_money,
        revoke_sale_money,service_money, GROUP_CONCAT(series_time_begin, "~",series_time_end) as series_time';
        $subOrderSql = $this->table($tableName)->where($filter)->field($subField)->group('id')->buildSql();

        if ($isExcel) {
            $list = $this->table($subOrderSql . ' a')->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($subOrderSql . ' a')->field($field)->page($page,
                $size)->group($groupBy)->select();
        }

        $list  = is_array($list) ? $list : [];
        $sum   = $this->table($subOrderSql . ' a')->where($filter)->field($field)->find();

        $subTotalSql = $this->table($subOrderSql . ' a')->field('a.series_time,a.date')->group($groupBy)->buildSql();
        $total       = $this->table($subTotalSql . ' b')->field($field)->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 获取演出验证报表
     * <AUTHOR>
     * @date 2019-05-17
     */
    public function getSeriesCheckedList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $seriesTimeBegin = 0, $seriesTimeEnd = 0, $notInArr = [], $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_seriesCheckedTable . '_' . $this->_yearMark : $this->_seriesCheckedTable;

        $beginDate = date('Ymd', strtotime($beginDate));
        $endDate   = date('Ymd', strtotime($endDate));
        $filter    = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        // 管理员查询权限
        if (!empty($fid)) {
            $filter['fid'] = $fid;
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = $lid;
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }


        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        //站点
        if ($siteId !== false) {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        if ($seriesTimeBegin && $seriesTimeEnd) {
            $filter['series_time_begin'] = ['EGT', $seriesTimeBegin];
            $filter['series_time_end']   = ['ELT', $seriesTimeEnd];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, 
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money,
        SUM(finish_num) as finish_num, SUM(finish_ticket) as finish_ticket,
        SUM(finish_cost_money) as finish_cost_money, SUM(finish_sale_money) as finish_sale_money,
        SUM(revoke_ticket) as revoke_ticket, SUM(revoke_num) as revoke_num,
        SUM(revoke_cost_money) as revoke_cost_money, SUM(revoke_sale_money) as revoke_sale_money,
        SUM(service_money) as service_money
        ';

        if (!empty($groupBy)) {
            $field = $field . ',' . $groupBy;
        }

        if ($isExcel) {
            $list = $this->table($tableName)->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($tableName)->where($filter)->field($field)->page($page,
                $size)->group($groupBy)->select();
        }

        $list = is_array($list) ? $list : [];

        $sum = $this->table($tableName)->where($filter)->field($field)->find();

        $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
        $total       = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 获取演出验证报表（月报表）
     * <AUTHOR>  Li
     * @date 2021-11-15
     */
    public function getSeriesMonthCheckedList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $seriesTimeBegin = 0, $seriesTimeEnd = 0, $notInArr = [], $extFilter =  [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_seriesMonthCheckedTable . '_' . $this->_yearMark : $this->_seriesMonthCheckedTable;

        $beginDate = date('Ym', strtotime($beginDate));
        $endDate   = date('Ym', strtotime($endDate));
        $filter    = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        // 管理员查询权限
        if (!empty($fid)) {
            $filter['fid'] = $fid;
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = $lid;
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }


        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        //站点
        if ($siteId !== false) {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        if ($seriesTimeBegin && $seriesTimeEnd) {
            $filter['series_time_begin'] = ['EGT', $seriesTimeBegin];
            $filter['series_time_end']   = ['ELT', $seriesTimeEnd];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, 
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money,
        SUM(finish_num) as finish_num, SUM(finish_ticket) as finish_ticket,
        SUM(finish_cost_money) as finish_cost_money, SUM(finish_sale_money) as finish_sale_money,
        SUM(revoke_ticket) as revoke_ticket, SUM(revoke_num) as revoke_num,
        SUM(revoke_cost_money) as revoke_cost_money, SUM(revoke_sale_money) as revoke_sale_money,
        SUM(service_money) as service_money';

        if (!empty($groupBy)) {
            $field = $field . ',' . $groupBy;
        }

        if ($isExcel) {
            $list = $this->table($tableName)->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($tableName)->where($filter)->field($field)->page($page,
                $size)->group($groupBy)->select();
        }

        $list = is_array($list) ? $list : [];

        $sum = $this->table($tableName)->where($filter)->field($field)->find();

        $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
        $total       = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 获取演出验证报表（场次汇总）
     * <AUTHOR>  Li
     * @date  2021-11-16
     */
    public function getSeriesPerformanceCheckList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $seriesTimeBegin = 0, $seriesTimeEnd = 0, $notInArr = [], $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_seriesCheckedTable . '_' . $this->_yearMark : $this->_seriesCheckedTable;

        $beginDate = date('Ymd', strtotime($beginDate));
        $endDate   = date('Ymd', strtotime($endDate));
        $filter    = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        // 管理员查询权限
        if (!empty($fid)) {
            $filter['fid'] = $fid;
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = $lid;
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }


        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        if ($siteId !== false) {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        if ($seriesTimeBegin && $seriesTimeEnd) {
            $filter['series_time_begin'] = ['EGT', $seriesTimeBegin];
            $filter['series_time_end']   = ['ELT', $seriesTimeEnd];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        $field = 'series_time,series_time_begin,series_time_end,a.date,
        SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, 
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money,
        SUM(finish_num) as finish_num, SUM(finish_ticket) as finish_ticket,
        SUM(finish_cost_money) as finish_cost_money, SUM(finish_sale_money) as finish_sale_money,
        SUM(revoke_ticket) as revoke_ticket, SUM(revoke_num) as revoke_num,
        SUM(revoke_cost_money) as revoke_cost_money, SUM(revoke_sale_money) as revoke_sale_money,
        SUM(service_money) as service_money';
        if (!empty($groupBy)) {
            $field = $field . ',' . $groupBy;
        }

        $subField    = 'id,date,series_time_begin,series_time_end,lid,pid,main_tid,main_type,tid,reseller_id,operate_id,fid,site_id,pay_way,channel,order_num,order_ticket,cost_money, 
        sale_money,finish_num,finish_ticket,finish_cost_money,finish_sale_money,revoke_ticket,revoke_num,revoke_cost_money,revoke_sale_money,today_check, service_money, GROUP_CONCAT(series_time_begin, "~",series_time_end) as series_time';
        $subOrderSql = $this->table($tableName)->where($filter)->field($subField)->group('id')->buildSql();

        if ($isExcel) {
            $list = $this->table($subOrderSql . ' a')->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($subOrderSql . ' a')->field($field)->page($page,
                $size)->group($groupBy)->select();
        }

        $list  = is_array($list) ? $list : [];
        $sum   = $this->table($subOrderSql . ' a')->where($filter)->field($field)->find();

        $subTotalSql = $this->table($subOrderSql . ' a')->field('a.series_time,a.date')->group($groupBy)->buildSql();
        $total       = $this->table($subTotalSql . ' b')->field($field)->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 获取取消列表
     * <AUTHOR>
     * @date   2016-08-08
     *
     * @param  $beginDate 开始查询时间 - 2016-05-01
     * @param  $endDate 结束查询时间 - 2016-05-02
     * @param  $groupBy 数据聚合方式 - product:产品，ticket：门票，reseller：分销商，channel：渠道
     * @param  int  $page  页码 - 1
     * @param  int  $size  条数 - 200
     * @param  int/arr $fid 出售用户ID
     * @param  int/arr $level 所属层级
     * @param  int/arr $ticketId 门票ID
     * @param  int/arr $landId 景点ID
     * @param  int/arr $resellerId 分销商ID
     * @param  $isRevoke 是不是撤销
     * @param  string  $excludeUids  需要排除掉的账号字符串 - 111,222,33,44
     * @param  string  $searchType  0分销商  1供应商
     *
     * @return
     */
    public function getCancelList($beginDate, $endDate, $groupBy = 'product', $page = 1, $size = 200, $fid = false, $level = false, $ticketId = false, $landId = false, $resellerId = false, $isRevoke = false, $excludeUids = false, $searchType = 0)
    {
        //参数处理
        $params = $this->_formatParam('cancel', $beginDate, $endDate, $groupBy, $page, $size, $fid, $level, $ticketId,
            $landId, $resellerId, $excludeUids, $searchType);
        //如果参数有问题的话
        if ($params === false) {
            return [];
        }

        if ($isRevoke) {
            $table = $this->_revokeTable;
        } else {
            $table = $this->_cancelTable;
        }

        $total = $this->table($table)->where($params['where'])->count("DISTINCT {$params['ext_field']}");

        if (in_array($groupBy, ['resellerAndTicket', 'lidAndTicket'])) {
            //TP在对一个以上的字段使用distinct时 特殊处理下
            $total = key($total);
        }

        $list = $this->table($table)->where($params['where'])->group($params['group'])
                     ->order($params['order'])->page($params['page'])
                     ->field($params['field'])->select();
        $sum  = $this->table($table)->where($params['where'])->field($params['field'])->find();

        //返回
        return ['total' => $total, 'list' => $list, 'sum' => $sum];
    }

    /**
     * 获取撤销列表
     * <AUTHOR>
     * @date   2016-08-08
     *
     * @param  $beginDate 开始查询时间 - 2016-05-01
     * @param  $endDate 结束查询时间 - 2016-05-02
     * @param  $groupBy 数据聚合方式 - product:产品，ticket：门票，reseller：分销商，channel：渠道
     * @param  int  $page  页码 - 1
     * @param  int  $size  条数 - 200
     * @param  int/arr $fid 出售用户ID
     * @param  int/arr $level 所属层级
     * @param  int/arr $ticketId 门票ID
     * @param  int/arr $landId 景点ID
     * @param  int/arr $resellerId 分销商ID
     * @param  string  $excludeUids  需要排除掉的账号字符串 - 111,222,33,44
     *
     * @return
     */
    public function getRevokeList($beginDate, $endDate, $groupBy = 'product', $page = 1, $size = 200, $fid = false, $level = false, $ticketId = false, $landId = false, $resellerId = false, $excludeUids = false, $searchType = 0)
    {

        //撤销的数据和取消的差不多
        return $this->getCancelList($beginDate, $endDate, $groupBy, $page, $size, $fid, $level, $ticketId, $landId,
            $resellerId, true, $excludeUids, $searchType);
    }

    /**
     * 获取未支付列表
     * <AUTHOR>
     * @date   2016-09-28
     *
     * @param  $beginDate 开始查询时间 - 2016-05-01
     * @param  $endDate 结束查询时间 - 2016-05-02
     * @param  $groupBy 数据聚合方式 - product:产品，ticket：门票，reseller：分销商，channel：渠道
     * @param  int  $page  页码 - 1
     * @param  int  $size  条数 - 200
     * @param  int/arr $fid 出售用户ID
     * @param  int/arr $level 所属层级
     * @param  int/arr $ticketId 门票ID
     * @param  int/arr $landId 景点ID
     * @param  int/arr $resellerId 分销商ID
     * @param  string  $excludeUids  需要排除掉的账号字符串 - 111,222,33,44
     *
     * @return
     */
    public function getUnpaidList($beginDate, $endDate, $groupBy = 'product', $page = 1, $size = 200, $fid = false, $level = false, $ticketId = false, $landId = false, $resellerId = false, $excludeUids = false)
    {
        $params = $this->_formatParam('unpaid', $beginDate, $endDate, $groupBy, $page, $size, $fid, $level, $ticketId,
            $landId, $resellerId, $excludeUids);
        //如果参数有问题的话
        if ($params === false) {
            return [];
        }

        $table = $this->_unpaidTable;

        $total = $this->table($table)->where($params['where'])->count("DISTINCT {$params['ext_field']}");
        $list  = $this->table($table)->where($params['where'])->group($params['group'])
                      ->order($params['order'])->page($params['page'])
                      ->field($params['field'])->select();
        $sum   = $this->table($table)->where($params['where'])->field($params['field'])->find();

        //返回
        return ['total' => $total, 'list' => $list, 'sum' => $sum];
    }

    /**
     * 获取过期列表
     * <AUTHOR>
     * @date   2016-09-28
     *
     * @param  string  $beginDate  开始查询时间 - 2016-05-01
     * @param  string  $endDate  结束查询时间 - 2016-05-02
     * @param  string  $groupBy  数据聚合方式 - product:产品，ticket：门票，reseller：分销商，channel：渠道
     * @param  int  $page  页码 - 1
     * @param  int  $size  条数 - 200
     * @param  bool|int|array  $fid  出售用户ID
     * @param  bool|int|array  $level  所属层级
     * @param  bool|int|array  $ticketId  门票ID
     * @param  bool|int|array  $landId  景点ID
     * @param  bool|int|array  $resellerId  分销商ID
     * @param  bool|string  $excludeUids  需要排除掉的账号字符串 - 111,222,33,44
     *
     * @return array
     */
    public function getExpireList($beginDate, $endDate, $groupBy = 'product', $page = 1, $size = 200, $fid = false, $level = false, $ticketId = false, $landId = false, $resellerId = false, $excludeUids = false)
    {
        $params = $this->_formatParam('expire', $beginDate, $endDate, $groupBy, $page, $size, $fid, $level, $ticketId,
            $landId, $resellerId, $excludeUids);
        //如果参数有问题的话
        if ($params === false) {
            return [];
        }

        $table = $this->_expireTable;

        $total = $this->table($table)->where($params['where'])->count("DISTINCT {$params['ext_field']}");
        $list  = $this->table($table)->where($params['where'])->group($params['group'])
                      ->order($params['order'])->page($params['page'])
                      ->field($params['field'])->select();

        $sum = $this->table($table)->where($params['where'])->field($params['field'])->find();

        //返回
        return ['total' => $total, 'list' => $list, 'sum' => $sum];
    }

    /**
     * 获取过期完结列表
     * <AUTHOR>
     * @date   2017-05-09
     *
     * @param  $beginDate 开始查询时间 - 2016-05-01
     * @param  $endDate 结束查询时间 - 2016-05-02
     * @param  $groupBy 数据聚合方式 - product:产品，ticket：门票，reseller：分销商，channel：渠道
     * @param  int  $page  页码 - 1
     * @param  int  $size  条数 - 200
     * @param  int/arr $fid 出售用户ID
     * @param  int/arr $level 所属层级
     * @param  int/arr $ticketId 门票ID
     * @param  int/arr $landId 景点ID
     * @param  int/arr $resellerId 分销商ID
     * @param  string  $excludeUids  需要排除掉的账号字符串 - 111,222,33,44
     * @param  int  $searchType  0分销商  1供应商
     *
     * @return
     */
    public function getFinishList($beginDate, $endDate, $groupBy = 'product', $page = 1, $size = 200, $fid = false, $level = false, $ticketId = false, $landId = false, $resellerId = false, $excludeUids = false, $searchType = 0)
    {
        //参数处理
        $params = $this->_formatParam('finish', $beginDate, $endDate, $groupBy, $page, $size, $fid, $level, $ticketId,
            $landId, $resellerId, $excludeUids, $searchType);
        //如果参数有问题的话
        if ($params === false) {
            return [];
        }

        $total = $this->table($this->_finishTable)->where($params['where'])->count("DISTINCT {$params['ext_field']}");
        if (in_array($groupBy, ['resellerAndTicket', 'lidAndTicket'])) {
            //TP在对一个以上的字段使用distinct时 特殊处理下
            $total = key($total);
        }

        $list = $this->table($this->_finishTable)->where($params['where'])->group($params['group'])
                     ->order($params['order'])->page($params['page'])
                     ->field($params['field'])->select();
        $sum  = $this->table($this->_finishTable)->where($params['where'])->field($params['field'])->find();

        //返回
        return ['total' => $total, 'list' => $list, 'sum' => $sum];
    }

    /**
     * 获取列表详情信息
     * <AUTHOR>
     * @date   2016-08-08
     *
     * @param  $beginDate 开始查询时间 - 2016-05-01
     * @param  $endDate 结束查询时间 - 2016-05-02
     * @param  $groupBy 数据聚合方式 - product:产品，ticket：门票，reseller：分销商，channel：渠道
     * @param  int  $page  页码 - 1
     * @param  int  $size  条数 - 200
     * @param  int/arr $fid 出售用户ID
     * @param  int/arr $level 所属层级
     * @param  int/arr $ticketId 门票ID
     * @param  int/arr $landId 景点ID
     * @param  int/arr $resellerId 分销商ID
     *
     * @return
     */
    public function getOrderDetails($beginDate, $endDate, $groupBy = 'product', $page = 1, $size = 200, $fid = false, $level = false, $ticketId = false, $landId = false, $resellerId = false, $targetId = 0, $type = 'order')
    {
        //参数处理
        $tableArr = [
            'order'   => $this->_orderTwo,
            'checked' => $this->_checkTwo,
            'cancel'  => $this->_cancelTable,
            'revoke'  => $this->_revokeTable,
        ];
        $table    = isset($tableArr[$type]) ? $tableArr[$type] : false;
        if (!$table) {
            return [];
        }

        if (!$targetId) {
            return [];
        }

        $params = $this->_formatParam($type, $beginDate, $endDate, $groupBy, $page, $size, $fid, $level, $ticketId,
            $landId, $resellerId, false, 0);

        //如果参数有问题的话
        if ($params === false) {
            return [];
        }

        if ($groupBy == 'product') {
            $params['where']['lid'] = $targetId;
        } else if ($groupBy == 'ticket') {
            $params['where']['ticket_id'] = $targetId;
        } else if ($groupBy == 'reseller') {
            $params['where']['reseller_id'] = $targetId;
        } else if ($groupBy == 'channel') {
            $params['where']['channel'] = $targetId;
        }

        //先获取每条的订单数，然后通过这些数据获取需要获取地几条到几条
        $numList = $this->table($table)->where($params['where'])->field('id, order_num')->order($params['order'])->select();

        $tmp = $this->_getQuery($numList, $page, $size);

        $total    = $tmp['total'];
        $ids      = $tmp['query_ids'];
        $startPos = $tmp['start_pos'];
        $endPos   = $tmp['end_pos'];

        //没有数组直接返回
        if (!$ids || ($startPos === false && $endPos === false)) {
            return ['total' => 0, 'list' => []];
        }

        //查询出数据
        $queryTmp = $this->table($table)->where([
            'id' => [
                'in',
                $ids,
            ],
        ])->field('orders_info')->order($params['order'])->select();

        //整理出需要的分页数据
        $list  = [];
        $count = count($queryTmp);
        foreach ($queryTmp as $key => $item) {
            $infos = @json_decode($item['orders_info'], true);
            $infos = is_array($infos) ? $infos : [];

            if ($count == 1) {
                if ($endPos === false) {
                    $list = array_splice($infos, $startPos - 1);
                } else {
                    $list = array_splice($infos, $startPos - 1, $endPos);
                }
            } else {
                if ($key == 0) {
                    if ($startPos === false) {
                        $pieceList = $infos;
                    } else {
                        $pieceList = array_splice($infos, $startPos - 1);
                    }
                } else if ($key == ($count - 1)) {
                    if ($endPos === false) {
                        $pieceList = $infos;
                    } else {
                        $pieceList = array_splice($infos, 0, $endPos);
                    }
                } else {
                    $pieceList = $infos;
                }

                $list = array_merge($list, $pieceList);
            }
        }

        //返回
        return ['total' => $total, 'list' => $list];
    }

    /**
     * 查询信息
     *
     * @param  date  $begin  开始时间
     * @param  date  $end  结束时间
     * @param  int  $type  报表类型  4预订 5验证 6取消 7撤销
     * @param  bool  $memberId  会员id
     * @param  bool  $level  层级
     * @param  bool  $ticketId  票类id
     * @param  bool  $landId  产品id
     * @param  bool  $resellerId  分销商id
     * @param  string  $excludeTest  是否去掉测试账号
     *
     * @return array
     */
    public function getOrderInfoByReport($begin, $end, $type = 1, $fid = false, $level = false, $ticketId = false, $landId = false, $resellerId = false, $excludeUids = false)
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return [];
        }

        if (in_array($type, [4, 5, 6, 7])) {
            return [];
        }

        $where = [
            'date' => ['between', [$begin, $end]],
        ];

        if ($fid !== false) {
            if (is_array($fid)) {
                $where['fid'] = ['in', $fid];
            } else {
                $where['fid'] = intval($fid);
            }
        }

        if ($level !== false) {
            $where['level'] = is_array($level) ? ['in', $level] : intval($level);
        }

        if (!empty($fid) && $level == 1) {
            unset($where['level']);
        }

        if ($ticketId !== false) {
            if ($ticketId) {
                $where['tid'] = is_array($ticketId) ? ['in', $ticketId] : intval($ticketId);
            }
        }

        if ($landId !== false) {
            if ($landId) {
                $where['lid'] = is_array($landId) ? ['in', $landId] : intval($landId);
            }
        }

        if ($resellerId !== false) {
            if ($resellerId) {
                $where['reseller_id'] = is_array($resellerId) ? ['in', $resellerId] : intval($resellerId);
            }
        }

        //排除账号
        if ($excludeUids !== false) {
            $excludeUids = trim(strval($excludeUids), ',');
            if ($excludeUids) {
                $where['_string'] = "fid not in ($excludeUids)";
            }
        }

        return $where;
    }

    /**
     * 获取某段时间内销售产品排行
     * <AUTHOR>
     * @date   2017-4-5
     *
     * @param  date  $begin  开始时间
     * @param  date  $end  结束时间
     *
     * @return array
     */
    public function getUseTop($begin, $end, $limit = 30)
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return [];
        }

        $field = 'lid, sum(order_num) as order_num,sum(order_ticket) as ticket_num';

        $begin = date('Ymd', strtotime($begin));
        $end   = date('Ymd', strtotime($end));

        $filter = [
            'date'  => ['between', [$begin, $end]],
            'level' => 1,
        ];

        $order = 'order_ticket desc';

        $data = $this->table($this->_checkTwo)->field($field)->where($filter)->group('lid')->order($order)
                     ->limit($limit)->select();

        return $data;
    }

    /**
     * 获取某一小时的检票 验证数量
     *
     * @param $hour
     * @param $type
     *
     * @return mixed
     */
    public function getLastHourData($hour, $type)
    {
        //如果统计应该是23点的数据 说明脚本执行时间在 第二天的0点
        if ($hour == 23) {
            $date = date('Ymd', time() - 3600);
        } else {
            $date = date('Ymd');
        }

        //做个处理 防止临界点出现问题
        $m = date('m', strtotime($date));
        $d = date('d', strtotime($date));
        $y = date('y', strtotime($date));

        $begin = mktime($hour, 0, 0, $m, $d, $y);

        if ($begin === false) {
            return [];
        }

        //切到小时报表
        if ($type == 1) {
            $table = $this->_orderTwoHour;
        } else {
            $table = $this->_checkTwoHour;
        }

        $filter = [
            'date_hour' => date("YmdH", $begin),
            'lvl'       => 1,
        ];

        $field = 'sum(order_num) as order_num, sum(order_ticket) as ticket_num';

        $res = $this->table($table)->where($filter)->field($field)->select();

        return is_array($res) ? $res : [];
    }

    /**
     * 获取某段时间内的平台总的销售金额
     * <AUTHOR>
     * @date   2017-4-1
     *
     * @param  date  $begin  开始时间   2017-4-18
     * @param  date  $end  结束时间   2017-4-18
     * @param  int  $memberId  用户ID
     *
     * @return int
     */
    public function getTotalSaleMoneyByTime($begin, $end, $memberId = '')
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return 0;
        }

        $begin = date('Ymd', strtotime($begin));
        $end   = date('Ymd', strtotime($end));

        $filter = [
            'date' => ['between', [$begin, $end]],
        ];

        if ($memberId) {
            if (is_array($memberId)) {
                $filter['fid'] = ['in', $memberId];
            } else {
                $filter['fid'] = $memberId;
            }
        }

        $total = $this->table($this->_checkTwo)->where($filter)->getField('SUM(sale_money)');

        return is_numeric($total) ? $total : 0;
    }

    /**
     * 获取某段时间内的平台总的销售金额 (按天汇总)
     * <AUTHOR>
     * @date   2024/04/16
     *
     * @param $begin
     * @param $end
     * @param $memberId
     *
     * @return int
     */
    public function getDailyTotalSaleMoneyByTime($begin, $end, $memberId = '')
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return 0;
        }

        $startDate = strtotime($begin); // 将开始日期字符串转换为Unix时间戳
        $endDate   = strtotime($end); // 将结束日期字符串转换为Unix时间戳

        $total = 0;
        // 循环从开始日期到结束日期
        for ($currentDate = $startDate; $currentDate <= $endDate; $currentDate += 86400) {
            $filterDate = date('Ymd', $currentDate);
            $filter     = [
                'date' => $filterDate,
            ];

            if ($memberId) {
                if (is_array($memberId)) {
                    $filter['fid'] = ['in', $memberId];
                } else {
                    $filter['fid'] = $memberId;
                }
            }

            $res = $this->table($this->_checkTwo)->where($filter)->getField('SUM(sale_money)');

            $total += (is_numeric($res) ? $res : 0);
        }

        return $total;
    }

    /**
     * 获取某段时间内的平台总的销售门票数
     * <AUTHOR>
     * @date   2017-4-18
     *
     * @param  date  $begin  开始时间   2017-4-18
     * @param  date  $end  结束时间   2017-4-18
     * @param  int  $type  1 = 检票 2 = 预订
     * @param  int  $memberId  会员ID
     *
     * @return int
     */
    public function getTotalTicketByTime($begin, $end, $type = 1, $memberId = '')
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return 0;
        }

        $begin = date('Ymd', strtotime($begin));
        $end   = date('Ymd', strtotime($end));

        $filter = [
            'date' => ['between', [$begin, $end]],
        ];

        if ($memberId) {
            if (is_array($memberId)) {
                $filter['fid'] = ['in', $memberId];
            } else {
                $filter['fid'] = $memberId;
            }
        } else {
            $filter['level'] = 1;
        }

        if ($type == 1) {
            $table = $this->_checkTwo;
        } else {
            $table = $this->_orderTwo;
        }

        $total = $this->table($table)->where($filter)->getField('SUM(order_ticket)');

        return is_numeric($total) ? $total : 0;
    }

    /**
     * 获取某段时间内的平台总的销售门票数(按天汇总)
     * <AUTHOR>
     * @date   2024/04/16
     *
     * @param $begin
     * @param $end
     * @param $type
     * @param $memberId
     *
     * @return int
     */
    public function getDailyTotalTicketByTime($begin, $end, $type = 1, $memberId = '')
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return 0;
        }

        $startDate = strtotime($begin); // 将开始日期字符串转换为Unix时间戳
        $endDate   = strtotime($end); // 将结束日期字符串转换为Unix时间戳

        $total = 0;
        // 循环从开始日期到结束日期
        for ($currentDate = $startDate; $currentDate <= $endDate; $currentDate += 86400) {
            $filterDate = date('Ymd', $currentDate); // 将Unix时间戳格式化为YYYYMMDD并添加到数组中

            $filter = [
                'date' => $filterDate,
            ];

            if ($memberId) {
                if (is_array($memberId)) {
                    $filter['fid'] = ['in', $memberId];
                } else {
                    $filter['fid'] = $memberId;
                }
            } else {
                $filter['level'] = 1;
            }

            if ($type == 1) {
                $table = $this->_checkTwo;
            } else {
                $table = $this->_orderTwo;
            }

            $res = $this->table($table)->where($filter)->getField('SUM(order_ticket)');

            $total += (is_numeric($res) ? $res : 0);
        }

        return $total;
    }

    /**
     * 获取会员检票报表数据
     * <AUTHOR>
     *
     * @param  array  $fidArr  供应商ID数组
     *
     * @return
     */
    public function getReportChecked($fidArr)
    {
        if (!is_array($fidArr) || $fidArr === false) {
            return false;
        }

        $group = 'fid';
        $field = 'fid,sum(order_num) as sum';
        $where = [
            'fid'   => ['in', $fidArr],
            'level' => 1,
        ];

        $res = $this->table($this->_checkTwo)->where($where)->field($field)->group($group)->select();

        return $res;
    }

    /**
     *
     * <AUTHOR>
     * @date   2016-08-13
     *
     * @param  $list
     * @param  $page
     * @param  $size
     *
     * @return
     */
    private function _getQuery($list, $page, $size)
    {
        //分页开始和结束的位置
        $startPos = ($page - 1) * $size + 1;
        $endPos   = $page * $size;

        //总数
        $total = 0;

        //开始和结束标识
        $startMark = false;
        $endMark   = false;

        $needStart = false;
        $needEnd   = false;

        foreach ($list as $item) {
            $id    = $item['id'];
            $count = $item['order_num'];
            $total += $count;

            //如果还没有结束计算
            if (!$endMark) {
                if ($total >= $startPos && !$startMark) {
                    //第一条数据的第几个位置开始取数据
                    $tmp       = $total - $count;
                    $needStart = $startPos - $tmp;
                    $startMark = true;
                }

                if ($total >= $endPos) {
                    $endMark = true;
                    //最末条数据的第几个位置结束取数据
                    $tmp     = $total - $count;
                    $needEnd = $endPos - $tmp;
                }

                //记录所有需要的ID
                if ($startMark) {
                    $resIds[] = $id;
                }
            }
        }

        //返回需要的数据
        $res = [
            'total'     => $total, //总统计数
            'query_ids' => $resIds, //需要查询的ID数组
            'start_pos' => $needStart, //第一条数据的第几个位置开始取数据
            'end_pos'   => $needEnd, //最末条数据的第几个位置结束取数据
        ];

        return $res;
    }

    /**
     * 获取脚本运行记录
     * <AUTHOR>
     * @date   2016-08-09
     *
     * @return
     */
    public function getTaskLogList($page = 1, $size = 20, $date = false)
    {
        $pageStr = "{$page},{$size}";
        $order   = "date desc";
        $where   = [];
        if ($date) {
            $where['date'] = $date;
        }

        $res = $this->table($this->_taskLogTable)->where($where)->order($order)->page($pageStr)->select();

        return $res;
    }

    /**
     * 根据日期获取某一天的脚本运行记录
     *
     * @param  date  eq:2016-10-12
     *
     * <AUTHOR>
     * @since  2016-10-12
     */
    public function getTaskLogByDate($date = false)
    {
        if ($date) {
            if (!strtotime($date)) {
                return false;
            }
            $date = date('Y-m-d', strtotime($date));
        } else {
            $date = date('Y-m-d', strtotime("-1 day"));
        }

        $where['date'] = $date;

        $res = $this->table($this->_taskLogTable)->where($where)->order('id desc')->find();

        return $res;
    }

    /**
     * 获取某段时间内检票报表内的fid
     *
     * @param  int  $bTime
     * @param  int  $eTime
     *
     * <AUTHOR>
     * @return
     */
    public function getCheckedFidArr($bTime, $eTime)
    {
        if (!strtotime($bTime) || !strtotime($eTime)) {
            return [];
        }
        $params = [
            'date' => [['egt', $bTime], ['elt', $eTime]],
        ];
        $res    = $this->table($this->_checkTwo)->distinct(true)->field('fid')->where($params)->select();
        if (is_array($res)) {
            return $res;
        }

        return [];
    }

    /**
     * 获取今日预订数据
     */
    public function getOrderToday($memberId = '')
    {
        $date   = date('Ymd', time());
        $filter = [
            'date' => $date,
        ];

        if ($memberId) {
            if (is_array($memberId)) {
                $filter['fid'] = ['in', $memberId];
            } else {
                $filter['fid'] = $memberId;
            }
        } else {
            $filter['level'] = 1;
        }

        $num = $this->table($this->_orderTwo)->where($filter)->getField('SUM(order_ticket)');

        return is_numeric($num) ? $num : 0;
    }

    /**
     * 获取今日验证数据
     */
    public function getCheckToday($memberId = '')
    {
        $date   = date('Ymd', time());
        $filter = [
            'date' => $date,
        ];

        if ($memberId) {
            if (is_array($memberId)) {
                $filter['fid'] = ['in', $memberId];
            } else {
                $filter['fid'] = $memberId;
            }
        } else {
            $filter['level'] = 1;
        }

        $num = $this->table($this->_checkTwo)->where($filter)->getField('SUM(order_ticket)');

        return is_numeric($num) ? $num : 0;
    }

    /**
     * 获取昨日预定数据
     * Create by zhangyangzhen
     * Date: 2018/5/3
     * Time: 9:58
     *
     * @param  string  $memberId
     *
     * @return int|string
     */
    public function getOrderByDate($date, $memberId = '')
    {
        $filter = [
            'date' => $date,
        ];

        if ($memberId) {
            if (is_array($memberId)) {
                $filter['fid'] = ['in', $memberId];
            } else {
                $filter['fid'] = $memberId;
            }
        } else {
            $filter['level'] = 1;
        }

        $num = $this->table($this->_orderTwo)->where($filter)->getField('SUM(order_ticket)');

        return is_numeric($num) ? $num : 0;
    }

    /**
     * 获取昨日检票数据
     * Create by zhangyangzhen
     * Date: 2018/5/3
     * Time: 9:58
     *
     * @param  string  $memberId
     *
     * @return int|string
     */
    public function getCheckByDate($date, $memberId = '')
    {
        $filter = [
            'date' => $date,
        ];

        if ($memberId) {
            if (is_array($memberId)) {
                $filter['fid'] = ['in', $memberId];
            } else {
                $filter['fid'] = $memberId;
            }
        } else {
            $filter['level'] = 1;
        }

        $num = $this->table($this->_checkTwo)->where($filter)->getField('SUM(order_ticket)');

        return is_numeric($num) ? $num : 0;
    }

    /**
     * 开始日期和插入的时间戳 获取数据（废弃）
     *
     * @param $date          int 日期
     * @param $beginTime     int 开始的时间戳
     * @param $endTime       int 结束的时间戳
     * @param $memberId      int 会员ID
     * @param $type          int =1 预订数据  =2 验证数据
     *
     * @see Statistics::getInfoByDateAndInsertTimeV2
     * @deprecated
     */
    public function getInfoByDateAndInsertTime($date, $beginTime, $endTime, $memberId, $type = 1)
    {
        if (!in_array($type, [1, 2])) {
            return [];
        }
        $filter = [
            'date'        => $date,
            'update_time' => ['between', [$beginTime, $endTime]],
        ];

        if (is_array($memberId)) {
            $filter['fid'] = ['in', $memberId];
        } else {
            $filter['fid'] = $memberId;
        }

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as ticket_num';

        $table = '';
        if ($type == 1) {
            $table = $this->_orderTwo;
        } elseif ($type == 2) {
            $table = $this->_checkTwo;
        }

        $data = $this->table($table)->where($filter)->field($field)->find();

        return is_array($data) ? $data : [];
    }

    /**
     * 开始日期和插入的时间戳 获取数据
     *
     * @param $date          int 日期
     * @param $beginTime     int 开始的时间戳
     * @param $endTime       int 结束的时间戳
     * @param $memberId      int 会员ID
     * @param $type          int =1 预订数据  =2 验证数据
     *
     */
    public function getInfoByDateAndInsertTimeV2($date, $beginTime, $endTime, $memberId, $type = 1)
    {
        if (!in_array($type, [1, 2])) {
            return [];
        }
        $filter = [
            'date'        => $date,
            'update_time' => ['between', [$beginTime, $endTime]],
        ];

        if (is_array($memberId)) {
            $filter['fid'] = ['in', $memberId];
        } else {
            $filter['fid'] = $memberId;
        }

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as ticket_num';

        $table = '';
        if ($type == 1) {
            $table = $this->_orderTwo;
        } elseif ($type == 2) {
            $table = $this->_checkTwo;
        }

        $data = $this->table($table)->where($filter)->field($field)->find();

        return is_array($data) ? $data : [];
    }

    //--------------------------------后台统计方法-----------------------------------------
    public function getPendingActionList()
    {
        return array(
            0 => '检票',
            1 => '预定',
            2 => '取消',
            3 => '撤销',
        );
    }

    /**
     * 重新统计数据时，清除之前的数据
     * <AUTHOR>
     * @date   2016-08-09
     *
     * @param  $day 哪天 - 2016-10-22
     * @param  $type 类型 checked ：检票，order:预定列表，cancel：取消，revoke：撤销
     *
     * @return
     */
    public function _clearPreData($day, $type)
    {
        $date = date('Y-m-d', strtotime($day));

        //首先删除之前的缓存文件数据
        $res = $this->_cacheDataArr($date, $type, 'clear');
        //如果删除失败了，返回失败
        if ($res == false) {
            return false;
        }
        switch ($type) {
            case 'checked':
                $table = $this->_checkTable;
                break;
            case 'order':
                $table = $this->_preOrderTable;
                break;
            case 'cancel':
                $table = $this->_cancelTable;
                break;
            case 'revoke':
                $table = $this->_revokeTable;
                break;
            case 'expire':
                $table = $this->_expireTable;
                break;
            case 'unpaid':
                $table = $this->_unpaidTable;
                break;
            case 'finish':
                $table = $this->_finishTable;
                break;
            default:
                $table = '';
                break;
        }

        if (!$table) {
            return false;
        }

        $date  = str_replace('-', '', $date);
        $where = ['date' => $date];
        $res   = $this->table($table)->where($where)->delete();

        return $res === false ? false : true;
    }

    /**
     * 根据表名删除数据
     *
     * <AUTHOR>
     * @date   2018-08-03
     */
    public function deleteTableData($table, $day, $fid = 0)
    {
        if (empty($table) || empty($day) || (!is_array($day) && !strtotime($day))) {
            return false;
        }

        !is_array($day) && $day = date('Ymd', strtotime($day));
        //获取where的日期字段
        $filter = $this->getDelWhereDateKey($table, $day);

        if ($fid) {
            $filter['fid'] = (int)$fid;
        }

        if (in_array($table, [$this->_terminalPassHourTable, $this->_terminalPassTable])) { //终端过闸报表需要限制下
            $filter['card_type'] = ['NOT IN', [47, 60, 61, 62, 63, 66]]; //员工卡和京津冀年卡
        }

        $res = $this->table($table)->where($filter)->delete();

        return $res;
    }

    /**
     * 获取删除日期对应的字段名
     * <AUTHOR>
     * @date 2021/6/21
     *
     * @param $table
     * @param $day
     *
     * @return array
     */
    public function getDelWhereDateKey($table, $day)
    {
        switch ($table) {
            case $this->_orderReserve :
            case $this->_orderReserveDaily :
            case $this->_orderReserveMonth :
            case $this->_showOrderReserveDaily :
            case $this->_showOrderReserveMonth :
                $dateKey = 'order_date';
                break;
            case $this->_customizeReport :
                $dateKey = 'cal_date';
                break;
            case $this->_terminalCheckedReport :
                $dateKey = 'day';
                break;
            case \Model\Report\StandingBook::STANDING_BOOK_ORDER :
            case \Model\Report\StandingBook::STANDING_BOOK_CHECKED :
                $dateKey = 'sta_date';
                break;
            case $this->_orderTwoHour:
            case $this->_checkTwoHour:
            case $this->_branchTerminalHourSummary:
            case $this->_terminalPassHourTable:
                $dateKey = 'date_hour';
                break;
            default :
                $dateKey = 'date';
                break;
        }

        if (is_array($day)) {
            $filter = [$dateKey => ['in', $day]];
        } else {
            $filter = [$dateKey => $day];
        }

        return $filter;
    }

    /**
     * 根据表名删除数据(分批删除)
     * <AUTHOR>
     * @date 2021/5/12
     *
     * @param  string  $table
     * @param  int  $day
     * @param  int  $fid
     * @param  int  $size
     *
     * @return bool|mixed
     */
    public function deleteTableDataByLimit($table, $day, $fid = 0, $size = 10000)
    {
        if (empty($table) || empty($day) || (!is_array($day) && !strtotime($day))) {
            return false;
        }

        !is_array($day) && $day = date('Ymd', strtotime($day));
        $filter = $this->getDelWhereDateKey($table, $day);

        if ($fid) {
            $filter['fid'] = (int)$fid;
        }

        if (in_array($table, [$this->_terminalPassHourTable, $this->_terminalPassTable])) { //终端过闸报表需要限制下
            $filter['card_type'] = ['NOT IN', [47, 60, 61, 62, 63, 66]]; //员工卡和京津冀年卡
        }

        $res = $this->table($table)->where($filter)->limit($size)->delete();

        return $res;
    }

    /**
     * 根据表名获取要删除数据总数
     * <AUTHOR>
     * @date 2021/5/12
     *
     * @param $table
     * @param $day
     * @param  int  $fid
     *
     * @return bool|mixed
     */
    public function getDeleteTableDataTotal($table, $day, $fid = 0)
    {
        if (empty($table) || empty($day) || (!is_array($day) && !strtotime($day))) {
            return 0;
        }

        !is_array($day) && $day = date('Ymd', strtotime($day));
        $filter = $this->getDelWhereDateKey($table, $day);

        if ($fid) {
            $filter['fid'] = (int)$fid;
        }

        if (in_array($table, [$this->_terminalPassHourTable, $this->_terminalPassTable])) { //终端过闸报表需要限制下
            $filter['card_type'] = ['NOT IN', [47, 60, 61, 62, 63, 66]]; //员工卡和京津冀年卡
        }

        $total = $this->table($table)->where($filter)->getField('count(id)');

        return is_numeric($total) ? $total : 0;
    }

    /**
     * 终端过闸报表，暂时不删除员工卡过闸记录，员工卡过闸不生成订单，暂时无法恢复
     * Create by zhangyangzhen
     * Date: 2019/7/17
     * Time: 11:39
     *
     * @param $day
     *
     * @return bool|mixed
     */
    public function deleteTerminalPassData($day)
    {
        if (!strtotime($day)) {
            return false;
        }

        $day    = date('Ymd', strtotime($day));
        $filter = [
            'date'      => $day,
            'card_type' => ['NOT IN', [47, 60, 61, 62, 63, 66]], //员工卡和京津冀年卡
        ];

        $res = $this->table($this->_terminalPassTable)->where($filter)->delete();

        return $res;
    }

    /**
     * 中间缓存数据处理
     * <AUTHOR>
     * @date   2016-08-07
     *
     * @param  string  $day  哪天 - 2016-10-22
     * @param  string  $type  类型 checked ：检票，order:预定列表，cancel：取消，revoke：撤销
     * @param  string  $operation  操作 get=获取，set=设置，clear=删除
     * @param  array  $dataArr  数据
     *
     * @return
     */
    private function _cacheDataArr($day, $type, $operation = 'get', $dataArr = [])
    {
        $path      = BASE_LOG_DIR . "/Calculate/{$type}";
        $cacheFile = $path . "/{$day}.log";

        //创建目录
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }

        if ($operation == 'get') {
            //获取数据
            if (file_exists($cacheFile)) {
                $tmp = file_get_contents($cacheFile);
                $tmp = @json_decode($tmp, true);

                return $tmp ? $tmp : [];
            } else {
                return [];
            }
        } elseif ($operation == 'set') {
            //缓存数据
            $tmp = @json_encode($dataArr);
            file_put_contents($cacheFile, $tmp);

            return true;
        } else {
            //清除缓存数据
            $res = true;
            if (file_exists($cacheFile)) {
                $res = @unlink($cacheFile);
            }

            return $res;
        }
    }

    /**
     * 获取报表的查询方式
     * <AUTHOR>
     * @date   2016-08-11
     *
     * @param  string  $type  报表类型
     * @param  $beginDate 开始查询时间 - 2016-05-01
     * @param  $endDate 结束查询时间 - 2016-05-02
     * @param  $groupBy 数据聚合方式 - product:产品，ticket：门票，reseller：分销商，channel：渠道
     * @param  int  $page  页码 - 1
     * @param  int  $size  条数 - 200
     * @param  int/arr $fid 出售用户ID
     * @param  int/arr $level 所属层级
     * @param  int/arr $ticketId 门票ID
     * @param  int/arr $landId 景点ID
     * @param  int/arr $resellerId 分销商ID
     * @param  string  $excludeUids  需要排除掉的账号字符串 - 111,222,33,44
     * @param  int  $searchType  0 分销商   1 供应商
     *
     * @return
     */
    private function _formatParam($type, $beginDate, $endDate, $groupBy = 'product', $page = 1, $size = 200, $fid = false, $level = false, $ticketId = false, $landId = false, $resellerId = false, $excludeUids = false, $searchType = 0)
    {
        $beginTime = strtotime($beginDate);
        $endTime   = strtotime($endDate);

        //参数错误
        if (!$beginTime || !$endTime || !in_array($groupBy, $this->_groupByArr)) {
            return false;
        }

        //处理查询条件
        $where = [];

        // 如果有传递时分秒，则分割出时分秒
        $beginArr = explode(' ', $beginDate);
        $endArr   = explode(' ', $endDate);
        if ((count($beginArr) == 2) || (count($endArr) == 2)) {

            if (count($beginArr) == 2 && substr($beginDate, 0, 10) == date('Y-m-d')) {
                $where['update_time'][] = array('EGT', $beginTime);
            }

            if (count($endArr) == 2 && substr($endDate, 0, 10) == date('Y-m-d')) {
                $where['update_time'][] = array('ELT', $endTime);
            }

        }

        $beginDate = date('Ymd', $beginTime);
        $endDate   = date('Ymd', $endTime);

        $where['date'] = array(array('EGT', $beginDate), array('ELT', $endDate));

        //数据会有问题，暂时都设置为分销商
        $searchType = 0;

        if ($fid !== false) {
            if ($fid) {
                if ($searchType == 0) {
                    $where['fid'] = is_array($fid) ? ['in', $fid] : intval($fid);
                } else {
                    $where['reseller_id'] = is_array($fid) ? ['in', $fid] : intval($fid);
                }
            }
        }

        if ($level !== false) {
            $where['level'] = is_array($level) ? ['in', $level] : intval($level);
        }

        if (!empty($fid) && $level == 1) {
            unset($where['level']);
        }

        if ($ticketId !== false) {
            if ($ticketId) {
                $where['tid'] = is_array($ticketId) ? ['in', $ticketId] : intval($ticketId);
            }
        }

        if ($landId !== false) {
            if ($landId) {
                $where['lid'] = is_array($landId) ? ['in', $landId] : intval($landId);
            }
        }

        if ($resellerId !== false) {
            if ($resellerId) {
                if ($searchType == 0) {
                    $where['reseller_id'] = is_array($resellerId) ? ['in', $resellerId] : intval($resellerId);
                } else {
                    $where['fid'] = is_array($resellerId) ? ['in', $resellerId] : intval($resellerId);
                }
            }
        }

        //排除账号
        if ($excludeUids !== false) {
            $excludeUids = trim(strval($excludeUids), ',');
            if ($excludeUids) {
                $where['_string'] = "fid not in ($excludeUids)";
            }
        }

        //处理聚合
        $field = $this->_getField($type);

        switch ($groupBy) {
            case 'ticket':
                $extField = 'tid';
                $group    = 'tid';
                break;
            case 'reseller':
                $extField = 'reseller_id';
                $group    = 'reseller_id';
                break;
            case 'channel':
                $extField = 'channel';
                $group    = 'channel';
                break;
            case 'fid':
                $extField = 'fid, lid';
                $group    = 'fid, lid';
                break;
            case 'date':
                $extField = 'date';
                $group    = 'date';
                break;
            case 'resellerAndTicket':
                $extField = 'reseller_id, tid';
                $group    = 'reseller_id, tid';
                break;
            case 'lidAndTicket':
                $extField = 'lid, tid';
                $group    = 'lid, tid';
                break;
            case 'lidAndTicketAndPayWay' :
                $extField = 'lid, tid, pay_way';
                $group    = 'lid, tid, pay_way';
                break;
            default:
                $extField = 'lid';
                $group    = 'lid';
                break;
        }

        $field   .= ",{$extField}";
        $pageStr = "{$page},{$size}";
        $orderBy = "order_num desc";
        if ($groupBy == 'date') {
            $orderBy = 'date desc';
        } else if ($groupBy == 'resellerAndTicket') {
            //方便合并展示分销商数据
            $orderBy = 'reseller_id desc, order_num desc';
        }

        //返回参数
        return [
            'where'     => $where,
            'group'     => $group,
            'field'     => $field,
            'page'      => $pageStr,
            'order'     => $orderBy,
            'ext_field' => $extField,
        ];
    }

    /**
     * 根据不同的报表取不同的字段
     * <AUTHOR>
     * @date   2016-08-11
     *
     * @param  $type 报表类型
     *
     * @return
     */
    private function _getField($type)
    {
        switch ($type) {
            case 'order':
            case 'order_v2':
                $field = 'sum(order_num) as order_num, sum(order_ticket) as ticket_num, sum(cancel_ticket) as cancel_ticket,
                sum(sale_money) as sale_money, sum(cost_money) as cost_money, sum(cancel_sale_money) as cancel_sale_money,
                sum(revoke_ticket) as revoke_ticket, sum(revoke_sale_money) as revoke_sale_money, reseller_id,fid';
                break;
            case 'checked':
            case 'checked_v2':
                $field = 'sum(order_num) as order_num, sum(order_ticket) as ticket_num, sum(revoke_ticket) as revoke_ticket,
                sum(sale_money) as sale_money, sum(finish_sale_money) as finish_sale_money,
                sum(cost_money) as cost_money,sum(finish_cost_money) as finish_cost_money,
                sum(revoke_cost_money) as revoke_cost_money,sum(revoke_sale_money) as revoke_sale_money,
                sum(finish_ticket) as finish_ticket,reseller_id,fid';
                break;
            case 'cancel':
            case 'revoke':
                $field = 'sum(order_num) as order_num, sum(ticket_num) as ticket_num,
                    sum(cancel_money) as cancel_money, sum(cost_money) as cost_money,
                    sum(service_money_in) as service_money_in, sum(service_money_out) as service_money_out,
                    reseller_id,fid';
                break;
            case 'unpaid':
                $field = 'sum(order_num) as order_num, sum(ticket_num) as ticket_num,
                sum(sale_money) as sale_money, reseller_id,fid';
                break;
            case 'expire':
                $field = 'sum(order_num) as order_num, sum(ticket_num) as ticket_num,
                sum(sale_money) as sale_money, sum(cost_money) as cost_money, sum(profit_money) as profit_money, reseller_id,fid';
                break;
            case 'fid':
                $field = 'sum(order_num) as order_num, sum(ticket_num) as ticket_num,
                    sum(sale_money) as sale_money,fid';
                break;
            default:
                $field = 'sum(order_num) as order_num, sum(ticket_num) as ticket_num,
                    sum(sale_money) as sale_money, sum(cost_money) as cost_money,
                    sum(coupon_num) as coupon_num, sum(coupon_money) as coupon_money,
                    reseller_id,fid';
                break;
            case 'finish':
                $field = 'sum(order_num) as order_num, sum(ticket_num) as ticket_num,
                    sum(sale_money) as sale_money, sum(cost_money) as cost_money,
                    reseller_id,fid';
                break;
        }

        return $field;
    }

    /**
     * 批量插入统计数据
     * <AUTHOR>
     * @date   2016-08-03
     *
     * @param  $dataArr
     *
     * @return
     */
    public function insertDataArr($dataArr, $type = 'checked')
    {
        switch ($type) {
            case 'checked':
                $res = $this->table($this->_checkTable)->addAll($dataArr);
                break;
            case 'cancel':
                $res = $this->table($this->_cancelTable)->addAll($dataArr);
                break;
            case 'revoke':
                $res = $this->table($this->_revokeTable)->addAll($dataArr);
                break;
            case 'order':
                $res = $this->table($this->_preOrderTable)->addAll($dataArr);
                break;
            case 'payway':
                $res = $this->table($this->_payWayCheckTable)->addAll($dataArr);
                break;
            case 'order_payway':
                $res = $this->table($this->_payWayOrderTable)->addAll($dataArr);
                break;
            case 'expire':
                $res = $this->table($this->_expireTable)->addAll($dataArr);
                break;
            case 'unpaid':
                $res = $this->table($this->_unpaidTable)->addAll($dataArr);
                break;
            case 'finish':
                $res = $this->table($this->_finishTable)->addAll($dataArr);
                break;
            case 'order_v2':
                $res = $this->table($this->_orderTwo)->addAll($dataArr);
                break;
            case 'checked_v2':
                $res = $this->table($this->_checkTwo)->addAll($dataArr);
                break;
            case 'pack_order':
                $res = $this->table($this->_orderPack)->addAll($dataArr);
                break;
            case 'pack_checked':
                $res = $this->table($this->_checkedPack)->addAll($dataArr);
                break;
            case 'ticket':
                $res = $this->table($this->_ticketReport)->addAll($dataArr);
                break;
            case 'team_order_pay':
                $res = $this->table($this->_teamOrderPay)->addAll($dataArr);
                break;
            case 'series_order':
                $res = $this->table($this->_seriesOrderTable)->addAll($dataArr);
                break;
            case 'series_checked':
                $res = $this->table($this->_seriesCheckedTable)->addAll($dataArr);
                break;
            case 'branchTerminal':
                $res = $this->table($this->_branchTerminalSummary)->addAll($dataArr);
                break;
            case 'branchTerminalHour':
                $res = $this->table($this->_branchTerminalHourSummary)->addAll($dataArr);
                break;
            case 'resource_order':
                $res = $this->table($this->_resourceOrderTable)->addAll($dataArr);
                break;
            case 'order_v2_hour' :
                $res = $this->table($this->_orderTwoHour)->addAll($dataArr);
                break;
            case 'checked_v2_hour' :
                $res = $this->table($this->_checkTwoHour)->addAll($dataArr);
                break;
            case 'reserve_order' :
                $res = $this->table($this->_orderReserve)->addAll($dataArr);
                break;
            case 'customize_report' :
                $res = $this->table($this->_customizeReport)->addAll($dataArr);
                break;
            case 'terminal_checked' :
                $res = $this->table($this->_terminalCheckedReport)->addAll($dataArr);
                break;
            case 'terminal_pass' :
                $res = $this->table($this->_terminalPassTable)->addAll($dataArr);
                break;
            case 'terminal_pass_hour' :
                $res = $this->table($this->_terminalPassHourTable)->addAll($dataArr);
                break;
            case 'order_reserve_daily' :
                $res = $this->table($this->_orderReserveDaily)->addAll($dataArr);
                break;
            case 'show_order_reserve_daily' :
                $res = $this->table($this->_showOrderReserveDaily)->addAll($dataArr);
                break;
        }

        return $res;
    }

    /**
     * 获取待处理汇总的日期
     * @return mixed
     */
    public function getPendingDate()
    {
        $field  = 'date, action';
        $filter = ['status' => 1];
        $order  = 'date ASC';
        $group  = 'date, action';
        $res    = $this->table($this->_pendingDayTable)->where($filter)->field($field)->group($group)->order($order)->select();

        return $res;
    }

    /**
     * 批量增加待处理汇总的日期
     *
     * @param  array  $dataActionArr  日期操作数组 （多记录）
     *                      date   日期  格式Y-m-d eg. 2017-01-01
     *                      action 操作
     * @param $memberID      操作者的id
     *
     * @return bool|string
     */
    public function addPendingDateMulti($dataActionArr, $memberID)
    {
        if (empty($dataActionArr) || !is_array($dataActionArr) || empty($memberID)) {
            return false;
        }

        $insertData = array();
        $nowtime    = date('Y-m-d H:i:s');
        foreach ($dataActionArr as $info) {
            $params       = array(
                'date'        => $info['date'],
                'status'      => 1,
                'action'      => $info['action'],
                'add_time'    => $nowtime,
                'update_time' => $nowtime,
                'operator_id' => $memberID,
            );
            $insertData[] = $params;
        }

        $res = $this->table($this->_pendingDayTable)->addAll($insertData);

        return $res;
    }

    /**
     * 单条插入
     * <AUTHOR>
     * @date   2017-4-25
     *
     * @param  date  $date  时间 eq:2017-4-25
     * @param  int  $action  0-验证,1-预定,2-取消,3-撤销
     * @param  int  $opId  操作人id
     *
     * @return bool|mixed
     */
    public function addPendingDate($date, $action, $opId = 1)
    {
        if (!strtotime($date) || !is_numeric($action) || !in_array($action, [0, 1, 2, 3]) || !is_numeric($opId)) {
            return false;
        }

        $data = [
            'date'        => date('Y-m-d', strtotime($date)),
            'status'      => 1,
            'action'      => $action,
            'add_time'    => date('Y-m-d H:i:s', time()),
            'update_time' => date('Y-m-d H:i:s', time()),
            'operator_id' => $opId,
        ];

        $res = $this->table($this->_pendingDayTable)->add($data);

        return $res;
    }

    /**
     * 更新待处理日期为已完成
     *
     * @param $date  日期 格式Y-m-d eg. 2017-01-01
     *
     * @return bool
     */
    public function completePendingDate($date, $action)
    {
        $nowtime = date('Y-m-d H:i:s');
        $filter  = [
            'date'   => $date,
            'status' => 1,
            'action' => $action,
        ];
        $params  = [
            'status'      => 0,
            'update_time' => $nowtime,
        ];

        $res = $this->table($this->_pendingDayTable)->where($filter)->data($params)->save();

        return $res;
    }

    /**
     * 获取数据
     *
     * @param $where 查询条件
     * @param $type  1 预订  2 验证  3 取消  4 撤销撤改 5 过期完结 7 验证日报
     */
    public function getInfo($where, $type = 1, $page = 1, $size = 500)
    {
        switch ($type) {
            case 1:
            default:
                // $res = $this->table($this->_preOrderTable)->where($where)->page($page, $size)->select();
                $res = [];
                break;
            case 2:
                //  $res = $this->table($this->_checkTable)->where($where)->page($page, $size)->select();
                $res = [];
                break;
            case 3:
                $res = $this->table($this->_cancelTable)->where($where)->page($page, $size)->select();
                break;
            case 4:
                $res = $this->table($this->_revokeTable)->where($where)->page($page, $size)->select();
                break;
            case 5:
                $res = $this->table($this->_payWayCheckTable)->where($where)->page($page, $size)->select();
                break;
            case 6:
                $res = $this->table($this->_finishTable)->where($where)->page($page, $size)->select();
                break;
        }

        return is_array($res) ? $res : [];
    }

    //获取旅行社团队验证报表总数
    public function getTravelAgencyTeamCheckTotal(array $where): int
    {
        $total = $this->table($this->_checkTeam)->where($where)->field('id')->count();
        return intval($total);
    }
    /**
     * 获取数据
     * <AUTHOR>
     *
     * @param  array  $where  查询条件
     * @param  int  $type  1 预订  2 验证  3, 预定月报表， 4，验证月报表, 5, 套票预定 6,套票验证 7，套票月预定  8,套票月验证 9 团队验证报表
     * 13 演出预定报表， 14演出验证报表
     * @param  int  $page
     * @param  int  $size
     *
     * @return array
     */
    public function getInfoV2($where, $type = 1, $page = 1, $size = 500, $field = '*')
    {
        switch ($type) {
            case 1:
            default:
                $table = $this->_yearMark ? $this->_orderTwo . '_' . $this->_yearMark : $this->_orderTwo;
                $res   = $this->table($table)->where($where)->field($field)->page($page, $size)->select();
                break;
            case 20 :
                $orderTable     = $this->_yearMark ? $this->_orderTwo . '_' . $this->_yearMark : $this->_orderTwo;
                $orderRes       = $this->table($orderTable)->where($where)->field($field)->page($page, $size)->select();
                $customizefield = 'cal_date, fid, reseller_id, lid, tid, window_real_orders_info, window_cancel_orders_info,sub_merchant_id';
                //获取定制报表的数据
                foreach ($where as $whereKey => $whereValue) {
                    if ($whereKey == 'date') {
                        $newWhere['cal_date'] = $whereValue;
                    } elseif ($whereKey == 'level') {
                        $newWhere['lvl'] = $whereValue;
                    } else {
                        $newWhere[$whereKey] = $whereValue;
                    }
                }

                $customizeRes = $this->table($this->_customizeReport)->where($newWhere)->field($customizefield)->page($page,
                    $size)->select();
                foreach ($orderRes as $key => $tmpOrder) {
                    $cusOrder       = [];
                    $cusCancelOrder = [];
                    foreach ($customizeRes as $ckey => $tmpCustomize) {
                        if (!empty($tmpOrder['window_real_orders_info'])) {
                            continue;
                        }

                        if ($tmpOrder['fid'] == $tmpCustomize['fid'] && $tmpOrder['lid'] == $tmpCustomize['lid']
                            && $tmpOrder['tid'] == $tmpCustomize['tid'] && $tmpOrder['reseller_id'] == $tmpCustomize['reseller_id']) {
                            $tmpCusOrderArr       = json_decode($tmpCustomize['window_real_orders_info'], true);
                            $tmpCusCancelOrderArr = json_decode($tmpCustomize['window_cancel_orders_info'], true);

                            $cusOrder       = array_merge($tmpCusOrderArr, $cusOrder);
                            $cusCancelOrder = array_merge($tmpCusCancelOrderArr, $cusCancelOrder);
                            unset($customizeRes[$ckey]);
                        }
                    }

                    $orderRes[$key]['window_real_orders_info']   = json_encode($cusOrder);
                    $orderRes[$key]['window_real_orders_info']   = json_encode($cusOrder);
                    $orderRes[$key]['window_cancel_orders_info'] = json_encode($cusCancelOrder);
                }

                $res = $orderRes;
                break;
            case 2:
                $table = $this->_yearMark ? $this->_checkTwo . '_' . $this->_yearMark : $this->_checkTwo;
                $res   = $this->table($table)->where($where)->field($field)->page($page, $size)->select();
                break;
            case 3:
                $table = $this->_yearMark ? $this->_orderTwoMonth . '_' . $this->_yearMark : $this->_orderTwoMonth;
                $res   = $this->table($table)->where($where)->field($field)->page($page, $size)->select();
                break;
            case 4:
                $table = $this->_yearMark ? $this->_checkTwoMonth . '_' . $this->_yearMark : $this->_checkTwoMonth;
                $res   = $this->table($table)->where($where)->field($field)->page($page, $size)->select();
                break;
            case 5:
                //套票预订，上线前先确认下是否补充
                $table = $this->_yearMark ? $this->_orderPack . '_' . $this->_yearMark : $this->_orderPack;
                $res = $this->table($table)->where($where)->field($field)->page($page, $size)->select();
                break;
            case 7:
                //套票预订月
                $table = $this->_yearMark ? $this->_orderPack . '_' . $this->_yearMark : $this->_orderPack;
                $res = $this->table($table)->where($where)->field($field)->page($page, $size)->select();
                break;
            case 6:
                //套票验证，上线前先确认下是否补充
                $table = $this->_yearMark ? $this->_checkedPack . '_' . $this->_yearMark : $this->_checkedPack;
                $res = $this->table($table)->where($where)->field($field)->page($page, $size)->select();
                break;
            case 8:
                //套票验证 月
                $table = $this->_yearMark ? $this->_checkedPack . '_' . $this->_yearMark : $this->_checkedPack;
                $res = $this->table($table)->where($where)->field($field)->page($page, $size)->select();
                break;
            case 9 :
                $res = $this->table($this->_checkTeam)->where($where)->field($field)->page($page, $size)->select();
                break;
            case 10 :
                $res = $this->table($this->_separateOrder)->where($where)->field($field)->page($page, $size)->select();
                break;
            case 11:
                $res = $this->table($this->_separateChedked)->where($where)->field($field)->page($page,
                    $size)->select();
                break;
            case 12: //分终端报表
                $res = $this->table($this->_branchTerminalSummary)->where($where)->field($field)->page($page,
                    $size)->select();
                break;

            case 13:
                $res = $this->table($this->_seriesOrderTable)->where($where)->field($field)->page($page,
                    $size)->select();
                break;
            case 14:
                $res = $this->table($this->_seriesCheckedTable)->where($where)->field($field)->page($page,
                    $size)->select();
                break;
            case 15:
                $res = $this->table($this->_terminalPassTable)->where($where)->field($field)->page($page,
                    $size)->select();
                break;
            case 16:
                $res = $this->table($this->_resourceOrderTable)->where($where)->field($field)->page($page,
                    $size)->select();
                break;
            case 17:
                $res = $this->table($this->_resourceOrderMonthTable)->where($where)->field($field)->page($page,
                    $size)->select();
                break;
            case 18:  //预订小时报表
                if (isset($where['level']) && !isset($where['lvl'])) {
                    $where['lvl'] = $where['level'];
                    unset($where['level']);
                }
                $table = $this->_yearMark ? $this->_orderTwoHour . '_' . $this->_yearMark : $this->_orderTwoHour;
                $res = $this->table($table)->where($where)->field($field)->page($page, $size)->select();
                break;
            case 19: //验证小时报表
                if (isset($where['level']) && !isset($where['lvl'])) {
                    $where['lvl'] = $where['level'];
                    unset($where['level']);
                }
                $table = $this->_yearMark ? $this->_checkTwoHour . '_' . $this->_yearMark : $this->_checkTwoHour;
                $res = $this->table($table)->where($where)->field($field)->page($page,
                    $size)->select();
                break;

            case 22: //分终端小时报表
                $res = $this->table($this->_branchTerminalHourSummary)->where($where)->field($field)->page($page,
                    $size)->select();
                // var_dump($this->getLastSql());exit;
                break;
            case 23: //演出预订月报表
                $res = $this->table($this->_seriesMonthOrderTable)->where($where)->field($field)->page($page,
                    $size)->select();
                break;
            case 24: //演出验证月报表
                $res = $this->table($this->_seriesMonthCheckedTable)->where($where)->field($field)->page($page,
                    $size)->select();
                break;
            case 25: //预订报表（分钟）
                $table = $this->_yearMark ? $this->_orderFiveMinuteReport . '_' . $this->_yearMark : $this->_orderFiveMinuteReport;
                $res   = $this->table($table)->where($where)->field($field)->page($page,
                    $size)->select();
                break;
            case 26: //验证报表（分钟）
                $table = $this->_yearMark ? $this->_checkFiveMinuteReport . '_' . $this->_yearMark : $this->_checkFiveMinuteReport;
                $res = $this->table($table)->where($where)->field($field)->page($page,
                    $size)->select();
                break;
        }

        return is_array($res) ? $res : [];
    }

    /**
     * 获取检票报表的总数
     *
     * @date   2017-07-13
     * <AUTHOR> Lan
     *
     * @param  int|string  $beginTime  起始时间
     * @param  int|string  $endTime  结束时间
     * @param  int|array  $landId  景区ID
     * @param  string  $filed  查询字段
     * @param  int  $level  转分销层级
     *
     * @return array
     */
    public function getCheckTotal($beginTime, $endTime, $landId, $filed = 'sum(order_ticket) as cnt', $level = 1)
    {
        if (!is_numeric($beginTime)) {
            $beginTime = strtotime($beginTime);
        }
        if (!is_numeric($endTime)) {
            $endTime = strtotime($endTime);
        }
        if (is_array($landId)) {
            $landId = ['in', $landId];
        }
        $where = [
            'update_time' => ['BETWEEN', [$beginTime, $endTime]],
            'lid'         => $landId,
            'level'       => $level,
        ];
        $res   = $this->table($this->_checkTwo)->field($filed)->where($where)->select();

        return $res ?: [];
    }

    /**
     * 获取指定用户某一天的销售数据
     *
     * @param  int|array  $fid  用户id
     * @param  string  $time  eq:20170101
     * @param  int  $type  1 预订    2 验证
     * @param  array  $extFilter    额外查询条件 ：套票子票  捆绑票子票···
     */
    public function getDataByFidTime($fid, $time, $type = 1, $extFilter = [])
    {
        if ((!is_numeric($fid) && !is_array($fid)) || !strtotime($time) || !in_array($type, [1, 2])) {
            return [];
        }

        switch ($type) {
            case 1:
                $table = $this->_orderTwo;
                break;
            case 2:
                $table = $this->_checkTwo;
                break;
        }

        $filter = [
            'date' => date('Ymd', strtotime($time)),
        ];

        if (is_numeric($fid)) {
            $filter['fid'] = $fid;
        } else {
            $filter['fid'] = ['in', $fid];
        }

        $filter = $this->filterSonTicket($filter, $extFilter);

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as ticket_num, SUM(sale_money) as sale_money';

        $data = $this->table($table)->where($filter)->field($field)->find();

        return is_array($data) ? $data : [];
    }

    /**
     * 获取指定用户某一天的真实销售数据（减去取消撤销等数据）
     * Create by zhangyangzhen
     * Date: 2019/12/16
     * Time: 14:28
     *
     * @param  int|array  $fid  用户ID
     * @param  string  $time  日期，格式20170101
     * @param  int  $type  1 预订    2 验证
     * @param  int|bool  $channel  渠道
     * @param array $extCondition ['lidList' => '允许产品列表', 'notLidList' => '排除的产品列表']
     *
     * @return array|mixed
     */
    public function getRealDataByFidTime($fid, $time, $type = 1, $channel = false, $extFilter = [], array $extCondition = [])
    {
        if (!$fid || (!is_numeric($fid) && !is_array($fid)) || !strtotime($time) || !in_array($type, [1, 2])) {
            return [];
        }
        switch ($type) {
            case 1:
                $table = $this->_orderTwo;
                $field = 'SUM(order_num) as order_num, SUM(cancel_num) as cancel_num, SUM(revoke_num) as revoke_num,
                SUM(order_ticket) as ticket_num, SUM(sale_money) as sale_money, SUM(cancel_ticket) as cancel_ticket, 
                SUM(revoke_ticket) as revoke_ticket, SUM(cancel_sale_money) as cancel_sale_money,
                SUM(revoke_sale_money) as revoke_sale_money';
                break;
            case 2:
                $table = $this->_checkTwo;
                $field = 'SUM(order_num) as order_num, SUM(order_ticket) as ticket_num, SUM(sale_money) as sale_money,
                SUM(revoke_ticket) as revoke_ticket, SUM(finish_ticket) as finish_ticket, SUM(revoke_sale_money) as revoke_sale_money,
                SUM(finish_sale_money) as finish_sale_money';
                break;
        }

        $filter = [
            'date' => date('Ymd', strtotime($time)),
        ];
        if (is_numeric($fid)) {
            $filter['fid'] = $fid;
        } else {
            $filter['fid'] = ['in', $fid];
        }
        if ($channel !== false) {
            $filter['channel'] = $channel + 0;
        }

        $filter = $this->filterSonTicket($filter, $extFilter);

        if (!empty($extCondition['lidList'])) {
            $filter['_complex'][] = [
                'lid' => ['in', $extCondition['lidList']]
            ];
        }
        if (!empty($extCondition['notLidList'])) {
            $filter['_complex'][] = [
                'lid' => ['not in', $extCondition['notLidList']]
            ];
        }

        $data = $this->table($table)->where($filter)->field($field)->find();

        return is_array($data) ? $data : [];
    }

    /**
     * 获取OTA分销商排行(大数据面板调用 如果是商家版的话 不限制只能是OTA的渠道)
     *
     * <AUTHOR>
     * @date   2017-9-7
     *
     * @param  int  $begin  开始时间 20170101
     * @param  int  $end  结束时间 20170101
     * @param  int  $mid  用户ID  为空取全部
     * @param  int  $limit  取多少条
     */
    public function getResellerRank($begin, $end, $memberId = '', $limit = 10)
    {
        $filter = ['date' => ['between', [$begin, $end]]];
        if ($memberId) {
            $filter['fid'] = $memberId;
        } else {
            $filter['channel']     = 20;
            $filter['reseller_id'] = ['neq', 112];
        }

        $field = 'reseller_id, SUM(order_num) as order_num';
        $data  = $this->table($this->_orderTwo)->where($filter)->field($field)->group('reseller_id')
                      ->order('order_num desc')
                      ->limit($limit)
                      ->select();

        return is_array($data) ? $data : [];
    }

    /**
     * 获取OTA分销商排行(大数据面板调用 如果是商家版的话 不限制只能是OTA的渠道)
     *
     * <AUTHOR>
     * @date   2017-11-6
     *
     * @param  int  $begin  开始时间 20170101
     * @param  int  $end  结束时间 20170101
     * @param  int|string  $memberId  用户ID  为空取全部
     * @param  int  $limit  取多少条
     *
     * @return array
     */
    public function getResellerRankCountTicket($begin, $end, $memberId = '', $limit = 10)
    {
        $filter = ['date' => ['between', [$begin, $end]]];
        if ($memberId) {
            if (is_array($memberId)) {
                $filter['fid'] = ['in', $memberId];
            } else {
                $filter['fid'] = $memberId;
            }
        } else {
            //管理员比较特殊，只需统计特定的几个OTA分销商
            $filter['channel']     = 20;
            $filter['reseller_id'] = ['in', $this->_needOtaReseller];
        }

        $field = 'reseller_id, SUM(order_ticket) as ticket_num';
        $data  = $this->table($this->_orderTwo)->where($filter)->field($field)->group('reseller_id')
                      ->order('ticket_num desc')
                      ->limit($limit)
                      ->select();

        return is_array($data) ? $data : [];
    }

    /**
     * 获取OTA分销商排行(大数据面板调用 如果是商家版的话 不限制只能是OTA的渠道)
     * 优化：1.走bi库；2.调整为按日期循环，避免一次性查询太多数据
     * <AUTHOR>
     * @date   2024/04/15
     *
     * @param $begin
     * @param $end
     * @param $memberId
     * @param $limit
     *
     * @return array
     */
    public function getDailyResellerRankTicketCounts($begin, $end, $memberId = '', $limit = 10)
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return [];
        }

        $startDate = strtotime($begin); // 将开始日期字符串转换为Unix时间戳
        $endDate   = strtotime($end); // 将结束日期字符串转换为Unix时间戳

        $results = [];
        // 循环从开始日期到结束日期
        for ($currentDate = $startDate; $currentDate <= $endDate; $currentDate += 86400) {
            $filterDate = date('Ymd', $currentDate); // 将Unix时间戳格式化为YYYYMMDD并添加到数组中
            $filter     = ['date' => $filterDate];
            if ($memberId) {
                if (is_array($memberId)) {
                    $filter['fid'] = ['in', $memberId];
                } else {
                    $filter['fid'] = $memberId;
                }
            } else {
                //管理员比较特殊，只需统计特定的几个OTA分销商
                $filter['channel']     = 20;
                $filter['reseller_id'] = ['in', $this->_needOtaReseller];
            }

            $field = 'reseller_id, SUM(order_ticket) as ticket_num';
            $data  = $this->table($this->_orderTwo)->where($filter)->field($field)->group('reseller_id')->order('ticket_num desc')->limit($limit)->select();

            $data = is_array($data) ? $data : [];

            // 将每天的汇总数据合并到$results中
            foreach ($data as $tmp) {
                $resellerId = $tmp['reseller_id'];
                $ticketNum  = $tmp['ticket_num'];
                if (!isset($results[$resellerId])) {
                    $results[$resellerId] = [
                        'ticket_num'  => 0,
                        'reseller_id' => $resellerId,
                    ];
                }

                $results[$resellerId]['ticket_num'] += $ticketNum;
            }
        }

        return array_values($results);
    }

    /**
     * 获取某段时间内，分销商销售总数
     * Create by zhangyangzhen
     * Date: 2018/4/26
     * Time: 15:05
     *
     * @param $begin
     * @param $end
     * @param  string  $memberId
     *
     * @return mixed
     */
    public function getTotalResellerTicket($begin, $end, $memberId = '')
    {
        $filter = ['date' => ['between', [$begin, $end]]];
        if ($memberId) {
            if (is_array($memberId)) {
                $filter['fid'] = ['in', $memberId];
            } else {
                $filter['fid'] = $memberId;
            }
        } else {
            //管理员比较特殊，只需统计特定的几个OTA分销商
            $filter['channel']     = 20;
            $filter['reseller_id'] = ['in', $this->_needOtaReseller];
        }

        $field = 'SUM(order_ticket) as ticket_num';
        $data  = $this->table($this->_orderTwo)
                      ->where($filter)
                      ->field($field)
                      ->find();

        return $data['ticket_num'] ?: 0;
    }

    /**
     * 获取7天产品排行
     * <AUTHOR>
     * @date   2017-9-11
     *
     * @param $date int  截止的时间点  如 20170906  则取 20170901-20170906的数据
     */
    public function getProductTop($date, $fid = '')
    {
        if (!strtotime($date)) {
            return [];
        }

        $begin = date('Ymd', strtotime($date) - 3600 * 24 * 7);
        $end   = $date;

        $group = 'lid';
        $field = 'lid,SUM(order_num) AS order_num, SUM(order_ticket) AS ticket_num';
        $order = 'ticket_num desc';

        $filter = [
            'date' => ['between', [$begin, $end]],
        ];

        if (!empty($fid)) {
            $filter['fid'] = $fid;
        } else {
            $filter['level'] = 1;
        }

        $data = $this->table($this->_orderTwo)->where($filter)->field($field)->group($group)->order($order)->limit(50)->select();

        return $data;
    }

    /**
     * 获取7天产品排行
     * <AUTHOR>
     * @date   2017-11-6
     *
     * @param  int  $begin  如 20170906
     * @param  int  $end
     * @param  string|int  $fid
     *
     * @return array
     */
    public function getProductTopByDate($begin, $end, $fid = '', $page = 1, $size = 50)
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return [];
        }

        $group = 'lid';
        $field = 'lid,SUM(order_num) AS order_num, SUM(order_ticket) AS ticket_num';
        $order = 'order_num desc';

        $filter = [
            'date' => ['between', [$begin, $end]],
        ];

        if (!empty($fid)) {
            if (is_array($fid)) {
                $filter['fid'] = ['in', $fid];
            } else {
                $filter['fid'] = $fid;
            }
        } else {
            $filter['level'] = 1;
        }

        $page = "{$page},{$size}";
        $data = $this->table($this->_orderTwo)->where($filter)->field($field)->group($group)->order($order)->page($page)->select();

        return $data;
    }

    /**
     * 获取时间段内的预定总数
     * Create by zhangyangzhen
     * Date: 2018/4/25
     * Time: 17:47
     *
     * @param $begin
     * @param $end
     * @param $fid
     *
     * @return array|mixed
     */
    public function getProductTotalTicket($begin, $end, $fid = '')
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return [];
        }

        $field = 'SUM(order_ticket) AS total';

        $filter = [
            'date' => ['between', [$begin, $end]],
        ];

        if (!empty($fid)) {
            if (is_array($fid)) {
                $filter['fid'] = ['in', $fid];
            } else {
                $filter['fid'] = $fid;
            }
        } else {
            $filter['level'] = 1;
        }

        $data = $this->table($this->_orderTwo)->where($filter)->field($field)->find();

        return $data['total'] ?: 0;
    }

    /**
     * 获取7天供应商排行
     * <AUTHOR>
     * @date   2018-03-22
     */
    public function getSupplierTopByDate($begin, $end, $fid = '')
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return [];
        }

        $group = 'fid';
        $field = 'fid, SUM(order_num) AS order_num, SUM(order_ticket) AS ticket_num';
        $order = 'ticket_num desc';

        $begin  = date('Ymd', strtotime($begin));
        $end    = date('Ymd', strtotime($end));
        $filter = [
            'date' => ['between', [$begin, $end]],
        ];

        if (!empty($fid)) {
            $filter['fid'] = $fid;
        } else {
            $filter['level'] = 1;
        }

        $data = $this->table($this->_orderTwo)->where($filter)->field($field)->group($group)->order($order)->limit(50)->select();

        return $data;
    }

    /**
     * 获取预订渠道的排行版
     *
     * <AUTHOR>
     * @date   2017-9-12
     *
     * @param  int  $begin  eq:20170101
     * @param  int  $end  eq:20170101
     * @param  int  $memberId  用户ID
     */
    public function getBookChannelRank($begin, $end, $memberId = '')
    {
        $filter = [
            'date' => ['between', [$begin, $end]],
        ];

        if ($memberId) {
            if (is_array($memberId)) {
                $filter['fid'] = ['in', $memberId];
            } else {
                $filter['fid'] = $memberId;
            }
        } else {
            $filter['level'] = 1;
        }

        $field = 'channel, SUM(order_num) as order_num, SUM(order_ticket) as ticket_num';
        $group = 'channel';

        $data = $this->table($this->_orderTwo)->group($group)->where($filter)->field($field)->order('order_num desc')->select();

        $orderMode    = load_config('order_mode');
        $orderMode[0] = '正常分销商下单';
        foreach ($data as $key => $item) {
            if (isset($orderMode[$item['channel']])) {
                $data[$key]['channel_name'] = $orderMode[$item['channel']];
            } else {
                $data[$key]['channel_name'] = '未知';
            }
        }

        return is_array($data) ? $data : [];
    }

    /**
     * 设置班结报表的入库操作
     * <AUTHOR>
     * @date   2017-10-19
     *
     * @param  array  $dataArr
     *
     * @return bool
     */
    public function setClassSettle($dataArr)
    {
        $res = $this->table($this->_classSettle)->addAll($dataArr);

        return $res;
    }

    /**
     * 设置班结取票报表的入库操作
     * <AUTHOR>
     * @date   2017-10-19
     *
     * @param  array  $dataArr
     *
     * @return bool
     */
    public function setClassTicketSettle($dataArr)
    {
        $res = $this->table($this->_classTicketSettle)->addAll($dataArr);

        return $res;
    }

    /**
     * 取得班结数据报表
     * <AUTHOR>
     * @date   2017-10-20
     * @return array
     *
     */
    public function getClassSettleReport($csrIdArr, $field = 'id, csr_id, op_id, lid, tid, terminal, sale_num, refund_num, money, type')
    {
        $filter = ['csr_id' => ['in', $csrIdArr]];
        $res    = $this->table($this->_classSettle)->where($filter)->field($field)->order('csr_id DESC,id ASC')->select();

        return $res ?: [];
    }

    /**
     * 取得班结数据报表
     * <AUTHOR>
     * @date   2017-10-20
     * @return array
     *
     */
    public function getClassTicketSettleReport($csrIdArr, $field = 'id, csr_id, op_id, lid, tid, terminal, sale_num, refund_num, money, type')
    {
        $filter = ['csr_id' => ['in', $csrIdArr]];
        $res    = $this->table($this->_classTicketSettle)->where($filter)->field($field)->order('csr_id DESC,id ASC')->select();

        return $res ?: [];
    }

    /**
     * 删除班结统计数据
     * <AUTHOR>
     * @date   2017-11-01
     *
     */
    public function delClassSettleReport($csrId)
    {
        $filter = ['csr_id' => $csrId];
        $res    = $this->table($this->_classSettle)->where($filter)->delete();

        return $res;
    }

    /**
     * 根据用户ID和时间段获取总票数 总订单数
     */
    public function getSumByFid($fid, $begin, $end)
    {
        if (empty($fid) || !strtotime($begin) || !strtotime($end)) {
            return false;
        }

        $filter = [
            'fid'  => $fid,
            'date' => ['between', [$begin, $end]],
        ];

        $data = $this->table($this->_orderTwo)->where($filter)
                     ->field('SUM(order_num) as order_num, SUM(order_ticket) as ticket_num')
                     ->find();

        return $data;
    }

    /**
     * 获取指定景区，指定日期的预定票数
     * Create by zhangyangzhen
     * Date: 2018/3/27
     * Time: 14:30
     *
     * @param $lid  景区id
     * @param $date 指定日期，格式：20180327
     *
     * @return int|string
     */
    public function getOrderByLidAndDate($lid, $date)
    {
        if (!isset($lid) || !is_numeric($lid)) {
            return false;
        }

        if (!isset($date) || !is_numeric($date)) {
            return false;
        }

        $filter = [
            'date'  => $date,
            'level' => 1,
            'lid'   => $lid,
        ];

        $num = $this->table($this->_orderTwo)->where($filter)->getField('SUM(ticket_num)');

        return is_numeric($num) ? $num : 0;
    }

    /**
     * 获取指定景区，指定日期的验证票数
     * Create by zhangyangzhen
     * Date: 2018/3/27
     * Time: 14:30
     *
     * @param $lid  景区id
     * @param $date 指定日期，格式：20180327
     *
     * @return int|string
     */
    public function getCheckByLidAndDate($lid, $date)
    {
        if (!isset($lid) || !is_numeric($lid)) {
            return false;
        }

        if (!isset($date) || !is_numeric($date)) {
            return false;
        }

        $filter = [
            'date'  => $date,
            'level' => 1,
            'lid'   => $lid,
        ];

        $num = $this->table($this->_checkTwo)->where($filter)->getField('SUM(order_ticket)');

        return is_numeric($num) ? $num : 0;
    }

    /**
     * 获取某个景区，某个日期的销售额
     * Create by zhangyangzhen
     * Date: 2018/3/27
     * Time: 16:18
     *
     * @param $lid  景区id
     * @param $date 日期  20180327
     *
     * @return int|string
     */
    public function getTotalSaleMoneyByLidAndDate($lid, $date)
    {
        if (!isset($lid) || !is_numeric($lid)) {
            return false;
        }

        if (!isset($date) || !is_numeric($date)) {
            return false;
        }

        $filter = [
            'level' => 1,
            'date'  => $date,
            'lid'   => $lid,
        ];

        $total = $this->table($this->_checkTwo)->where($filter)->getField('SUM(sale_money)');

        return is_numeric($total) ? $total : 0;
    }

    /**
     * 获取销售报表数据
     *
     * @date   2018-05-12
     * <AUTHOR> Lan
     *
     * @param  int  $beginTime  开始时间
     * @param  int  $endTime  结束时间
     * @param  int  $operator  操作员
     * @param  int  $landId  景区ID
     * @param  int  $ticketId  门票ID
     * @param  bool  $isStaff  是否员工
     * @param  int  $parentId  账号主id
     *
     * @return array
     */
    public function getOrderData($beginTime, $endTime, $operator, $landId = 0, $ticketId = 0, $isStaff = false, $parentId = '')
    {
        $beginTime = date('Ymd', $beginTime);
        $endTime   = date('Ymd', $endTime);
        $where     = ['date' => ['BETWEEN', [$beginTime, $endTime]]];
        if ($isStaff) {
            if (is_array($operator)) {
                $where['operate_id'] = ['in', $operator];
            } else {
                $where['operate_id'] = $operator;
            }
        } else {
            $where['fid'] = $operator;
        }

        if ($parentId) {
            $where['fid'] = $parentId;
        }

        if ($landId) {
            $where['lid'] = $landId;
        }

        if ($ticketId) {
            $where['tid'] = $ticketId;
        }

        $where['channel'] = 10;

        $field = 'order_num, sale_money, tid, pay_way, service_money, order_ticket, cancel_ticket, orders_info, cancel_sale_money, revoke_ticket, revoke_sale_money';
        $res   = $this->table($this->_orderTwo)->field($field)->where($where)->select();

        return $res ?: [];
    }

    /**
     * 获取销售取消数据
     *
     * @date   2018-06-20
     * <AUTHOR>
     *
     * @param  int  $beginTime  开始时间
     * @param  int  $endTime  结束时间
     * @param  int  $operator  操作员
     * @param  int  $landId  景区ID
     * @param  int  $ticketId  门票ID
     * @param  bool  $isStaff  是否员工
     * @param  int  $parentId  账号主id
     *
     * @return array
     */
    public function getCancelData($beginTime, $endTime, $operator, $landId = 0, $ticketId = 0, $isStaff = false, $parentId = '')
    {
        $beginTime = date('Ymd', $beginTime);
        $endTime   = date('Ymd', $endTime);
        $where     = ['date' => ['BETWEEN', [$beginTime, $endTime]]];
        if ($isStaff) {
            $where['operate_id'] = $operator;
        } else {
            $where['fid'] = $operator;
        }

        if ($parentId) {
            $where['fid'] = $parentId;
        }

        if ($landId) {
            $where['lid'] = $landId;
        }

        if ($ticketId) {
            $where['tid'] = $ticketId;
        }

        $where['channel'] = 10;

        $field = 'order_num, lid, tid, cancel_money, ticket_num, pay_way';
        $res   = $this->table('pft_report_cancel')->field($field)->where($where)->select();

        return $res ?: [];
    }

    /*
     * 根据用户ID和景区ID，日期获取按支付方式汇总的数据
     * <AUTHOR>
     * @date   2018-04-03
     *
     * @params int      $userId        用户ID
     * @params int      $lid           景区ID
     * @params int      $begin         开始时间
     * @params int      $end           结束时间
     * $params boolean  $isPrimary     是否主账号
     */
    public function getSummaryByUserIdAndDateAndLid($userId, $lid = '', $begin = '', $end = '', $isPrimary = false, $sid = false)
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return [];
        }

        $begin = date('Ymd', strtotime($begin));
        $end   = date('Ymd', strtotime($end));

        $filter = [
            'channel' => 10,
            'date'    => ['between', [$begin, $end]],
        ];

        if ($isPrimary) {
            $filter['fid'] = $userId;
        } else {
            $filter['operate_id'] = $userId;
            if (!empty($sid)) {
                $filter['fid'] = $sid;
            }
        }

        if (!empty($lid)) {
            $filter['lid'] = $lid;
        }

        $field = 'SUM(ticket_num) as ticket_num, SUM(cancel_money) as cancel_money, pay_way';

        $data = $this->table('pft_report_cancel')->where($filter)->group('pay_way')->field($field)->select();

        return is_array($data) ? $data : [];
    }

    /*
     * 根据用户ID和景区ID，日期获取按支付方式汇总的数据
     * <AUTHOR>
     * @date   2019-4-30
     *
     * @params int      $userId        用户ID
     * @params int      $lid           景区ID
     * @params int      $begin         开始时间
     * @params int      $end           结束时间
     * $params boolean  $isPrimary     是否主账号
     */
    public function getRevokeByUserIdAndDateAndLid($userId, $lid = '', $begin = '', $end = '', $isPrimary = false, $sid = false)
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return [];
        }

        $begin = date('Ymd', strtotime($begin));
        $end   = date('Ymd', strtotime($end));

        $filter = [
            'channel' => 10,
            'date'    => ['between', [$begin, $end]],
        ];

        if ($isPrimary) {
            $filter['fid'] = $userId;
        } else {
            $filter['operate_id'] = $userId;
            if (!empty($sid)) {
                $filter['fid'] = $sid;
            }
        }

        if (!empty($lid)) {
            $filter['lid'] = $lid;
        }

        $field = 'SUM(revoke_ticket) as ticket_num, SUM(revoke_sale_money) as cancel_money, pay_way';

        $data = $this->table('pft_report_order_two')->where($filter)->group('pay_way')->field($field)->select();

        return is_array($data) ? $data : [];
    }

    /**
     * 批量插入所有数据
     *
     * @param  array  $data
     */
    public function addAllData($dataArr, $type = '')
    {
        if (empty($dataArr)) {
            return false;
        }

        switch ($type) {
            case 'checked':
                $res = $this->table($this->_checkTable)->addAll($dataArr);
                break;
            case 'cancel':
                $res = $this->table($this->_cancelTable)->addAll($dataArr);
                break;
            case 'revoke':
                $res = $this->table($this->_revokeTable)->addAll($dataArr);
                break;
            case 'order':
                $res = $this->table($this->_preOrderTable)->addAll($dataArr);
                break;
            case 'check_pay':
                $res = $this->table($this->_payWayCheckTable)->addAll($dataArr);
                break;
            case 'order_pay':
                $res = $this->table($this->_payWayOrderTable)->addAll($dataArr);
                break;
            case 'finish':
                $res = $this->table($this->_finishTable)->addAll($dataArr);
                break;
            case 'order_v2':
                $res = $this->table($this->_orderTwo)->addAll($dataArr);
                break;
            case 'checked_v2':
                $res = $this->table($this->_checkTwo)->addAll($dataArr);
                break;
            case 'pack_order':
                $res = $this->table($this->_orderPack)->addAll($dataArr);
                break;
            case 'pack_checked':
                $res = $this->table($this->_checkedPack)->addAll($dataArr);
                break;
            case 'ticket':
                $res = $this->table($this->_ticketReport)->addAll($dataArr);
                break;
            case 'team_order_pay':
                $res = $this->table($this->_teamOrderPay)->addAll($dataArr);
                break;
            case 'team_check':
                $res = $this->table($this->_teamCheckPay)->addAll($dataArr);
                break;
            case 'team_order':
                $res = $this->table($this->_teamOrderTable)->addAll($dataArr);
                break;
            case 'series_order':
                $res = $this->table($this->_seriesOrderTable)->addAll($dataArr);
                break;
            case 'series_checked':
                $res = $this->table($this->_seriesCheckedTable)->addAll($dataArr);
                break;
            case 'annual_card_checked':
                $res = $this->table($this->_annualCardCheckedTable)->addAll($dataArr);
                break;
            case 'annual_card_order':
                $res = $this->table($this->_annualCardOrderTable)->addAll($dataArr);
                break;
            case 'terminal_pass':
                $res = $this->table($this->_terminalPassTable)->addAll($dataArr);
                break;
            case 'terminal_pass_hour':
                $res = $this->table($this->_terminalPassHourTable)->addAll($dataArr);
                break;
            case 'resource_order':
                $res = $this->table($this->_resourceOrderTable)->addAll($dataArr);
                break;
            case 'order_v2_hour' :
                $res = $this->table($this->_orderTwoHour)->addAll($dataArr);
                break;
            case 'checked_v2_hour' :
                $res = $this->table($this->_checkTwoHour)->addAll($dataArr);
                break;
            case 'reserve_order' :
                $res = $this->table($this->_orderReserve)->addAll($dataArr);
                break;
            case 'customize_report' :
                $res = $this->table($this->_customizeReport)->addAll($dataArr);
                break;
            case 'terminal_branch_hour' :
                $res = $this->table($this->_branchTerminalHourSummary)->addAll($dataArr);
                break;
            case 'terminal_checked' :
                $res = $this->table($this->_terminalCheckedReport)->addAll($dataArr);
                break;
            case 'order_v2_sta' :
                $res = $this->table(\Model\Report\StandingBook::STANDING_BOOK_ORDER)->addAll($dataArr);
                break;
            case 'checked_v2_sta' :
                $res = $this->table(\Model\Report\StandingBook::STANDING_BOOK_CHECKED)->addAll($dataArr);
                break;
            case 'minute_order_v2' :
                $res = $this->table($this->_orderFiveMinuteReport)->addAll($dataArr);
                break;
            case 'minute_checked_v2' :
                $res = $this->table($this->_checkFiveMinuteReport)->addAll($dataArr);
                break;
            case 'order_reserve_daily' :
                $res = $this->table($this->_orderReserveDaily)->addAll($dataArr);
                break;
            case 'order_reserve_month' :
                $res = $this->table($this->_orderReserveMonth)->addAll($dataArr);
                break;
            case 'show_order_reserve_daily' :
                $res = $this->table($this->_showOrderReserveDaily)->addAll($dataArr);
                break;
            case 'show_order_reserve_month' :
                $res = $this->table($this->_showOrderReserveMonth)->addAll($dataArr);
                break;
            //case 'customize_report_month' :
            //    $res = $this->table('pft_report_customize_month')->addAll($dataArr);
            //    break;
            default:
                $res = false;
        }

        return $res;
    }

    /**
     * 销毁链接
     *
     * <AUTHOR>
     * @date   2018-08-07
     * @return bool
     */
    public function forceShutdown()
    {
        return parent::forceShutdown();
    }

    /**
     * 获取验证报表总数通过fid跟date
     * <AUTHOR>
     * @date 2018-08-01
     */
    public function getCheckV2CountByFid($date, $fid)
    {
        if (empty($date) || empty($fid)) {
            return [];
        }

        $where = [
            'date' => $date,
            'fid'  => $fid,
        ];

        $count = $this->table('pft_report_checked_two')->where($where)->COUNT();

        return $count;
    }

    /**
     * 获取验证报表数据通过fid跟date
     * <AUTHOR>
     * @date 2018-08-01
     */
    public function getCheckV2ListByFid($date, $fid, $field = 'id', $page = 1, $size = 15)
    {
        if (empty($date) || empty($fid)) {
            return [];
        }

        $where = [
            'date' => $date,
            'fid'  => $fid,
        ];

        $res = $this->table('pft_report_checked_two')->where($where)->field($field)->page($page, $size)->select();

        return is_array($res) ? $res : [];
    }

    /**
     * 获取相关用户和8月份的订单信息
     * Create by zhangyangzhen
     * Date: 2018/8/16
     * Time: 17:12
     *
     * @param $memberId
     * @param  string  $field
     *
     * @return array
     */
    public function getOrderV2ByFidOrReseller($memberId, $field = 'id')
    {
        if (empty($memberId)) {
            return [];
        }

        if (is_array($memberId)) {
            $where['fid']         = ['in', $memberId];
            $where['reseller_id'] = ['in', $memberId];
            $where['_logic']      = 'OR';
        } else {
            $where['fid']         = $memberId;
            $where['reseller_id'] = $memberId;
            $where['_logic']      = 'OR';
        }

        $filter['date']     = ['egt', '20180801'];
        $filter['_complex'] = $where;

        $result = $this->table($this->_orderTwo)->where($filter)->field($field)->select();

        return !empty($result) ? true : false;
    }

    /**
     * 获取微商城，前一天完结的订单
     *
     * 目的：根据积分汇率，为散客增加积分
     *
     * @param   $sidArr 有开启满金额送积分的供应商id数组
     *
     * @return  array
     */
    public function getMallOrderForPoints($sidArr)
    {

        if (!$sidArr) {
            return [];
        }

        $date = date('Ymd', strtotime('yesterday'));

        $where = [
            'fid'         => ['in', $sidArr],
            'date'        => $date,
            'channel'     => 11,
            'reseller_id' => 12,
        ];

        $field  = 'operate_id as reseller_id,fid,sum(sale_money) as money';
        $result = $this->table($this->_checkTwo)->where($where)->field($field)->group('operate_id,fid')->select();

        return $result ?: [];
    }

    /**
     * 获取报表归档日志数据
     *
     * @param  array  $archiverTable  归档表名称
     * @param  array  $archiverYear  归档数据年份
     * @param  array  $result  归档操作结果
     * @param  string  $field  查询字段
     *
     * @return array
     */
    public function getArchiverLog($archiverTable = [], $archiverYear = [], $result = self::ARCHIVER_ACTION_SUCC, $field = "", $order = '')
    {
        $where = ['result' => $result];

        if ($archiverTable) {
            $where['archiver_table'] = ['IN', $archiverTable];
        }

        if ($archiverYear) {
            $where['archiver_year'] = ['IN', $archiverYear];
        }

        if (!$field) {
            $field = "archiver_table, archiver_year, result, remark";
        }

        if ($order) {
            $result = $this->table($this->_archiverLogTable)->where($where)->field($field)->order($order)->select();
        } else {
            $result = $this->table($this->_archiverLogTable)->where($where)->field($field)->select();
        }

        return $result ? $result : [];
    }

    /**
     * 获取报表数据推送的配置
     * Create by zhangyangzhen
     * Date: 2019/2/27
     * Time: 10:41
     */
    public function getReportPushConfig()
    {
        $config = $this->table($this->_reportPushConfig)->select();

        return !empty($config) ? $config : [];
    }

    /**
     * 服务器报表数据推送日志
     * Create by zhangyangzhen
     * Date: 2019/2/27
     * Time: 11:28
     *
     * @param  int  $memberId  会员ID
     * @param  int  $templateId  模板ID
     * @param  string  $url  文件地址
     * @param  int  $type  模板类型，1=预定报表，2验证报表
     *
     * @return bool
     */
    public function addReportPushLog($memberId, $templateId, $url, $type = 1, $status = 1)
    {
        if (!$memberId || !$templateId || !$url) {
            return false;
        }

        $data = [
            'fid'         => $memberId,
            'template_id' => $templateId,
            'type'        => $type,
            'file_url'    => $url,
            'status'      => $status,
            'create_at'   => date('Y-m-d H:i:s'),
            'update_at'   => date('Y-m-d H:i:s'),
        ];

        $res = $this->table($this->_reportPush)->add($data);

        return $res ?: false;
    }

    /**
     * 获取服务器报表数据推送日志
     * Create by zhangyangzhen
     * Date: 2019/2/27
     * Time: 11:36
     *
     * @param  int  $memberId  用户ID
     * @param  int  $date  日期，格式 20190227
     *
     * @return array
     */
    public function getReportPushLogByFid($fid, $date)
    {
        if (!$fid || empty($fid)) {
            return [];
        }

        $where = [
            'fid'    => $fid,
            'status' => ['neq', 3],
        ];

        if ($date) {
            $begin = $date . " 00:00:00";
            $end   = $date . " 23:59:59";

            $where['create_at'] = ['between', [$begin, $end]];
        }

        $data = $this->table($this->_reportPush)->where($where)->select();

        return !empty($data) ? $data : [];
    }

    /**
     * 更新报表推送记录
     * Create by zhangyangzhen
     * Date: 2019/3/1
     * Time: 10:37
     *
     * @param $logArr
     * @param  int  $status
     *
     * @return bool
     */
    public function updateReportPushLog($logArr, $status = 2)
    {
        $data = [
            'status'    => $status,
            'update_at' => date('Y-m-d H:i:s'),
        ];

        $where = [
            'id' => ['in', $logArr],
        ];

        return $this->table($this->_reportPush)->where($where)->save($data);
    }

    /***
     * 获取一段时间指定分销商上级供应商销售金额相关数据
     * @author: Cai Yiqiang
     * @date: 2019/1/15
     *
     * @param $beginTime
     * @param $endTime
     * @param $fid
     * @param $reseller
     * @param  bool  $sum
     * @param  int  $landId
     * @param  int  $ticketId
     *
     * @return array
     */
    public function getRecenterSaleDataBySupplier($beginTime, $endTime, $fid, $reseller, $sum = false, $landId = 0, $ticketId = 0)
    {
        if (!($beginTime && $endTime && $fid && $reseller)) {
            return [];
        }

        $beginTime = date('Ymd', $beginTime);
        $endTime   = date('Ymd', $endTime);

        $where = [
            'fid'         => $fid,
            'reseller_id' => $reseller,
            'date'        => ['BETWEEN', [$beginTime, $endTime]],
        ];

        if ($landId) {
            $where['lid'] = $landId;
        }

        if ($ticketId) {
            $where['tid'] = $ticketId;
        }

        // 资源中心记录的fid是分销商，sid是供应商， 报表的fid是供应商，reseller_id是分销商
        $field = 'fid as sid, reseller_id as fid, SUM(order_num) as money';
        $res   = $this->table($this->_orderTwo)->field($field)->where($where)->group('fid, reseller_id')->find();

        return is_array($res) ? $res : [];
    }

    /**
     * 获取景区订单分组总数
     * <AUTHOR>
     * @date   2019-04-11
     *
     * @param  string  $begin  开始时间
     * @param  string  $end  结束时间
     *
     * @return int
     */
    public function getProductTopByDateTotal(string $begin, string $end, $fid = ''): int
    {
        if (!strtotime($begin) || !strtotime($end)) {
            return 0;
        }

        $field = 'count(distinct lid) as count';

        $where = [
            'date'  => ['between', [$begin, $end]],
            'level' => 1,
        ];

        if (!empty($fid)) {
            if (is_array($fid)) {
                $where['fid'] = ['in', $fid];
            } else {
                $where['fid'] = $fid;
            }
        }

        $res = $this->table($this->_orderTwo)->where($where)->field($field)->find();

        return $res['count'] ?: 0;
    }

    /**
     * 查询数据是否重复 （普通业务不可调用，只用于测试检查）
     * <AUTHOR>
     * @date    2019-05-31
     *
     * @param  array  $where  查询条件
     * @param  string  $table
     *
     * @return  array
     */
    public function checkDataRepeat($where, $table)
    {
        $res = $this->table($table)->where($where)->field('id')->select();

        return is_array($res) ? $res : [];
    }

    /**
     * 添加分终端验证订单记录
     *
     * @param  string  $orderNum  订单号
     *
     * @return  boolean
     */
    public function addTerminalCheckedOrder($orderNum, $type)
    {
        $data = [
            'ordernum'    => $orderNum,
            'insert_time' => time(),
            'type'        => (int)$type,
        ];

        return $this->table($this->_terminalCheckedOrderTable)->add($data);
    }

    /**
     * 分终端验证:订单是否已经完成验证
     *
     * @param  string  $orderNum  订单号
     *
     * @return  boolean
     */
    public function isTerminalChecked($orderNum, $type)
    {
        $where  = [
            'ordernum' => (string)$orderNum,
            'type'     => (int)$type,
        ];
        $record = $this->table($this->_terminalCheckedOrderTable)->where($where)->field('id')->find();

        return $record ? true : false;

    }

    /**
     * 计算利润金额
     *
     * @return  int
     */
    private function _countProfitMoney($saleMoney = 0, $finishSaleMoney = 0, $revokeSaleMoney = 0, $costMoney = 0, $finishCostMoney = 0, $revokeCostMoney = 0)
    {
        $profitMoney = $saleMoney + $finishSaleMoney - $revokeSaleMoney - ($costMoney + $finishCostMoney - $revokeCostMoney);

        return $profitMoney;
    }

    /**
     * 统计某个供应商下某个分销商的票的一个月销售数量(这个月的第一天到当前天)
     *
     * @param  int  $fid  分销商id
     * @param  int  $tidArr  票id数组
     * @param  int  $channel  下单渠道
     *
     * <AUTHOR>
     * @date   2019/11/20
     *
     * @return array
     */
    public function getMultiTicketSaleNum($fid, $tidArr, $channel = 0)
    {
        $fid = (int)$fid;
        if (empty($fid) || empty($tidArr) || !is_array($tidArr)) {
            return [];
        }
        $beginTime = date('Ym01', time());
        $endTime   = date('Ymd', time());
        $where     = [
            'date' => ['BETWEEN', [$beginTime, $endTime]],
            'fid'  => $fid,
            'tid'  => ['IN', $tidArr],
        ];
        if (!empty($channel)) {
            $where['channel'] = (int)$channel;
        }
        $group = 'tid';
        $field = "sum(order_ticket) as sum_order_ticket, tid, fid";
        $res   = $this->table($this->_orderTwo)->where($where)->field($field)->group($group)->select();

        return $res;
    }

    /**
     * 获取供应商销量靠前的景区（谨慎使用，$number数量不可传太大）
     *
     * @param  int  $fid  分销商id
     * @param  int  $channel  下单渠道
     * @param  int  $number  获取前几条数据
     *
     * <AUTHOR>
     * @date   2019/11/20
     *
     * @return array
     */
    public function getSaleTopLand($fid, $channel = 0, $number = 3)
    {
        $fid = (int)$fid;
        if (empty($fid) || $number > 10) {
            return [];
        }
        $beginTime = date('Ymd', strtotime('-30 days'));
        $endTime   = date('Ymd', time());
        $where     = [
            'date' => ['BETWEEN', [$beginTime, $endTime]],
            'fid'  => $fid,
        ];
        if (!empty($channel)) {
            $where['channel'] = (int)$channel;
        }
        $field = "sum(order_ticket) as sale_num, lid";

        return $this->table($this->_orderTwo)->where($where)->field($field)->group('lid')->order('sale_num desc')->limit($number)->select();
    }

    /** 判断报表是否进行取票统计
     * <AUTHOR>
     * @date 2020/8/25
     *
     * @return bool
     */
    protected function _judgePrintNum()
    {
        //归档处理 ->归档移除 2020之前的移除取票统计
        if (empty($this->_yearMark) || $this->_yearMark >= 2020) {
            return true;
        }

        return false;
    }

    /**
     * 获取某个供应商下时间段内产生订单的分销商
     *
     * @param  int  $uid  当前供应商
     * @param  array  $fids  分销商
     * @param  string  $beginDateTime  下单时间
     * @param  string  $endDateTime  下单截止
     *
     * <AUTHOR>
     * @date   2020-11-19
     *
     * @return array
     */
    public function getDistributionOrderNum(int $uid, array $fids, string $beginDateTime, string $endDateTime)
    {
        if (empty($beginDateTime) || empty($endDateTime) || empty($uid) || empty($fids) || !is_array($fids)) {
            return [];
        }

        $where = [
            'date'        => ['BETWEEN', [$beginDateTime, $endDateTime]],
            'fid'         => $uid,
            'reseller_id' => ['IN', $fids],
            'order_num'   => ['GT', 0],
        ];

        $tableName = $this->_yearMark ? $this->_orderTwo . '_' . $this->_yearMark : $this->_orderTwo;
        $field     = "reseller_id";
        $res       = $this->table($tableName)->where($where)->field($field)->group('reseller_id')->select();

        return $res;
    }

    /**
     * 根据表名删除数据
     *
     * <AUTHOR>
     * @date   2018-08-03
     */
    public function deleteHourTableData(string $table, array $hourList, $fid = 0)
    {
        if (empty($hourList)) {
            return false;
        }

        if (!in_array($table, [$this->_checkTwoHour, $this->_orderTwoHour, $this->_branchTerminalHourSummary, $this->_terminalPassHourTable])) {
            return false;
        }

        $filter = ['date_hour' => ['IN', $hourList],];
        if ($fid) {
            $filter['fid'] = (int)$fid;
        }

        if ($this->_terminalPassHourTable == $table) { //终端过闸报表需要限制下
            $filter['card_type'] = ['NOT IN', [47, 60,61,62,63, 66]]; //员工卡和京津冀年卡
        }

        $res = $this->table($table)->where($filter)->delete();

        return $res;
    }

    public function getOrderV2HourList(string $beginTime, string $endTime, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $notInArr = [], $isSonTicket = 0, $hourSupportDaySpan = false, $subSid = 0, $extFilter = [])
    {
        $tableName = $this->_yearMark ? $this->_orderTwoHour . '_' . $this->_yearMark : $this->_orderTwoHour;

        $beginHour = date('YmdH', strtotime($beginTime));
        $endHour   = date('YmdH', strtotime($endTime));

        //小时报表跨天处理
        if ($hourSupportDaySpan) {
            //验证是否超过72小时
            if (StatisticsHandleBiz::judgeDateIntervalHours($beginTime, $endTime)) {
                return [];
            }

            //支持跨天的话，结束时间需要处理成上一小时的最后一分钟
            $endHour = StatisticsHandleBiz::hourReportEndDate($endTime);
        } else {
            if ($endHour - $beginHour > 23) {
                return [];
            }
        }


        $filter = ['date_hour' => ['between', [$beginHour, $endHour]]];

        //子商户
        $subSid = trim($subSid, ',');
        if (!empty($subSid)) {
            //子商户登录的时候需要过滤下
            if ($subSid == $fid) {
                $fid = 0;
                $filter['sub_merchant_id'] = $subSid;
            } else {
                $subSidArr = explode(',', $subSid);
                $filter['sub_merchant_id'] = ['in', $subSidArr];
            }
        }

        // 管理员查询权限
        if (!empty($fid)) {
            // 景区id $fid 有可能是数组， 这里需要判断
            if (is_array($fid)) {
                $filter['fid'] = ['in', $fid];
            } else {
                $fidArr = explode(',', $fid);
                // 判断是否模糊条件查询
                if (count($fidArr) > 1) {
                    $filter['fid'] = ['in', $fidArr];
                } else {
                    $filter['fid'] = $fid;
                }
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = ['in', $lid];
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($notInArr['not_lid'])) {
            $notLidArr = is_array($notInArr['not_lid']) ? $notInArr['not_lid'] : explode(',', $notInArr['not_lid']);
            !empty($notLidArr) && $filter['_complex'][] = ['lid' => ['not in', $notLidArr]];
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['lvl'] = $level;
        }

        if ($siteId !== false && $siteId != '') {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        //groupby去掉date
        $groupArr = explode(',', $groupBy);
        foreach ($groupArr as $key => $tmpGroup) {
            if ($tmpGroup == 'date') {
                unset($groupArr[$key]);
                //小时报表支持跨天需要按小时汇总
                if ($hourSupportDaySpan) {
                    $groupArr[] = 'date_hour';
                }
            }
        }

        $filterStr = "order_ticket <> 0 or revoke_ticket <> 0 or cancel_ticket <> 0 or after_sale_ticket_num <> 0";

        $groupBy = implode(',', $groupArr);

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(cancel_num) as cancel_num,
        SUM(cancel_ticket) as cancel_ticket, SUM(revoke_num) as revoke_num, SUM(revoke_ticket) as revoke_ticket,
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money, SUM(cancel_cost_money) as cancel_cost_money,
        SUM(cancel_sale_money) as cancel_sale_money, SUM(revoke_cost_money) as revoke_cost_money,
        SUM(revoke_sale_money) as revoke_sale_money, SUM(service_money) as service_money,
        SUM(after_sale_ticket_num) as after_sale_ticket_num, SUM(after_sale_refund_money) as after_sale_refund_money,
        SUM(after_sale_income_money) as after_sale_income_money';

        if ($this->_judgePrintNum()) {
            $field = $field . ', SUM(print_num) as print_num';
            $filterStr .= " or print_num <> 0";
        }

        !$isSonTicket && $filter['_string'] = $filterStr;

        //缺失逗号异常处理
        if (!empty($groupBy) && substr($groupBy, 0, 1) != ',') {
            $field = $field . ',' . $groupBy;
        }

        //子票维度
        if ($isSonTicket) {
            return $this->_getHourListByPackSonTicket($tableName, $filter, $groupBy, $field, $page, $size, $isExcel);
        }

        if ($isExcel) {
            $list = $this->table($tableName)->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($tableName)->where($filter)->field($field)->page($page,
                $size)->group($groupBy)->select();
        }

        $list = is_array($list) ? $list : [];

        $sum         = $this->table($tableName)->where($filter)->field($field)->find();
        $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
        $total       = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    public function getCheckV2HourList($beginTime, $endTime, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $notInArr = [], $isSonTicket = 0, $hourSupportDaySpan = false, $subSid = 0, $extFilter = [])
    {
        $tableName = $this->_yearMark ? $this->_checkTwoHour . '_' . $this->_yearMark : $this->_checkTwoHour;

        $beginHour = date('YmdH', strtotime($beginTime));
        $endHour   = date('YmdH', strtotime($endTime));

        //小时报表跨天处理
        if ($hourSupportDaySpan) {
            //验证是否超过72小时
            if (StatisticsHandleBiz::judgeDateIntervalHours($beginTime, $endTime)) {
                return [];
            }

            //支持跨天的话，结束时间需要处理成上一小时的最后一分钟
            $endHour = StatisticsHandleBiz::hourReportEndDate($endTime);
        } else {
            if ($endHour - $beginHour > 23) {
                return [];
            }
        }

        $filter = ['date_hour' => ['between', [$beginHour, $endHour]]];

        $subSid = trim($subSid, ',');
        if (!empty($subSid)) {
            //子商户登录的时候需要过滤下
            if ($subSid == $fid) {
                $fid = 0;
                $filter['sub_merchant_id'] = $subSid;
            } else {
                $subSidArr = explode(',', $subSid);
                $filter['sub_merchant_id'] = ['in', $subSidArr];
            }
        }

        // 管理员查询权限
        if (!empty($fid)) {
            // 景区id $fid 有可能是数组， 这里需要判断
            if (is_array($fid)) {
                $filter['fid'] = ['in', $fid];
            } else {
                $fidArr = explode(',', $fid);
                // 判断是否模糊条件查询
                if (count($fidArr) > 1) {
                    $filter['fid'] = ['in', $fidArr];
                } else {
                    $filter['fid'] = $fid;
                }
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = ['in', $lid];
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($notInArr['not_lid'])) {
            $notLidArr = is_array($notInArr['not_lid']) ? $notInArr['not_lid'] : explode(',', $notInArr['not_lid']);
            !empty($notLidArr) && $filter['_complex'][] = ['lid' => ['not in', $notLidArr]];
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['lvl'] = $level;
        }

        //站点
        if ($siteId !== false && $siteId != '') {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        !$isSonTicket && $filter['_string']  = "order_ticket <> 0 or revoke_ticket <> 0 or finish_ticket <> 0 or after_sale_ticket_num <> 0";

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, 
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money,
        SUM(finish_num) as finish_num, SUM(finish_ticket) as finish_ticket,
        SUM(finish_cost_money) as finish_cost_money, SUM(finish_sale_money) as finish_sale_money,
        SUM(revoke_ticket) as revoke_ticket, SUM(revoke_num) as revoke_num,
        SUM(revoke_cost_money) as revoke_cost_money, SUM(revoke_sale_money) as revoke_sale_money,today_check,
        SUM(service_money) AS service_money, SUM(after_sale_ticket_num) as after_sale_ticket_num,
        SUM(after_sale_refund_money) as after_sale_refund_money, SUM(after_sale_income_money) as after_sale_income_money';

        //groupby去掉date
        $groupArr = explode(',', $groupBy);
        foreach ($groupArr as $key => $tmpGroup) {
            if ($tmpGroup == 'date') {
                unset($groupArr[$key]);
                //小时报表支持跨天需要按小时汇总
                if ($hourSupportDaySpan) {
                    $groupArr[] = 'date_hour';
                }
            }
        }

        //groupby去掉date
        $groupBy = implode(',', $groupArr);

        if (!empty($groupBy)) {
            $field = $field . ',' . $groupBy;
        }

        //子票维度处理
        if ($isSonTicket) {
            return $this->_getHourListByPackSonTicket($tableName, $filter, $groupBy, $field, $page, $size, $isExcel);
        }

        if ($isExcel) {
            $list = $this->table($tableName)->where($filter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($tableName)->where($filter)->field($field)->page($page,
                $size)->group($groupBy)->select();
        }

        $list = is_array($list) ? $list : [];
        $sum  = $this->table($tableName)->where($filter)->field($field)->find();

        $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
        $total       = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 预订报表关联定制报表查询， 两个报表各自根据各维度聚合汇总(group by), 结果再join查询
     * <AUTHOR>
     * @date   2016-08-24
     *
     *
     * @return
     */
    public function getOrderV2JoinCustomizeList($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '',
        $groupBy = '', $level = '', $page = 1, $size = 15, $isExcel = false, $siteId = false, $notInArr = [], $isSonTicket = 0, $subSid = 0, $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName      = $this->_yearMark ? $this->_orderTwo . '_' . $this->_yearMark : $this->_orderTwo;
        $customizeTable = 'pft_report_customize';

        $beginDate = date('Ymd', strtotime($beginDate));
        $endDate   = date('Ymd', strtotime($endDate));
        $filter    = [
            'o.date' => ['between', [$beginDate, $endDate]],
        ];

        $customizeFilter = ['cal_date' => ['between', [$beginDate, $endDate]],];

        //子商户
        $subSid = trim($subSid, ',');
        if (!empty($subSid)) {
            //子商户登录的时候需要过滤下
            if ($subSid == $fid) {
                $fid = 0;
                $filter['o.sub_merchant_id']        = $subSid;
                $customizeFilter['sub_merchant_id'] = $subSid;
            } else {
                $subSidArr = explode(',', $subSid);
                $filter['o.sub_merchant_id']        = ['in', $subSidArr];
                $customizeFilter['sub_merchant_id'] = ['in', $subSidArr];
            }
        }

        // 管理员查询权限
        if (!empty($fid)) {
            // 景区id $fid 有可能是数组， 这里需要判断
            if (is_array($fid)) {
                $filter['o.fid']        = ['in', $fid];
                $customizeFilter['fid'] = ['in', $fid];
            } else {
                $fidArr = explode(',', $fid);
                // 判断是否模糊条件查询
                if (count($fidArr) > 1) {
                    $filter['o.fid']        = ['in', $fidArr];
                    $customizeFilter['fid'] = ['in', $fidArr];
                } else {
                    $filter['o.fid']        = $fid;
                    $customizeFilter['fid'] = $fid;
                }
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['o.lid']        = ['in', $lid];
                $customizeFilter['lid'] = ['in', $lid];
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['o.lid']        = ['in', $lidArr];
                    $customizeFilter['lid'] = ['in', $lidArr];
                } else {
                    $filter['o.lid']        = $lid;
                    $customizeFilter['lid'] = $lid;
                }
            }
        }

        if (!empty($notInArr['not_lid'])) {
            $notLidArr = is_array($notInArr['not_lid']) ? $notInArr['not_lid'] : explode(',', $notInArr['not_lid']);
            !empty($notLidArr) && $filter['_complex'][] = ['o.lid' => ['not in', $notLidArr]];
        }

        if (!empty($pid)) {
            $pidArr                 = explode(',', $pid);
            $filter['o.pid']        = ['in', $pidArr];
            $customizeFilter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['o.pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr                 = explode(',', $tid);
            $filter['o.tid']        = ['in', $tidArr];
            $customizeFilter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['o.tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup                  = explode(',', $resellerGroup);
            $filter['o.reseller_id']        = ['in', $resellerGroup];
            $customizeFilter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr                  = explode(',', $resellerId);
            $filter['o.reseller_id']        = ['in', $resellerIdArr];
            $customizeFilter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr                 = explode(',', $channel);
            $filter['o.channel']        = ['in', $channelArr];
            $customizeFilter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr                  = explode(',', $operateId);
            $filter['o.operate_id']        = ['in', $operateIdArr];
            $customizeFilter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr                  = explode(',', $payWay);
            $filter['o.pay_way']        = ['in', $payWayArr];
            $customizeFilter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['o.level']        = $level;
            $customizeFilter['level'] = $level;
        }

        if ($siteId !== false && $siteId != '') {
            $siteId                     = explode(',', $siteId);
            $filter['o.site_id']        = ['in', $siteId];
            $customizeFilter['site_id'] = ['in', $siteId];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filterSonTicket = $this->filterSonTicket([], $extFilter);
        if (isset($filterSonTicket['main_tid'])) {
            $filter['o.main_tid']        = $filterSonTicket['main_tid'];
            $customizeFilter['main_tid'] = $filterSonTicket['main_tid'];
        }
        if (isset($filterSonTicket['main_type'])) {
            $filter['o.main_type']        = $filterSonTicket['main_type'];
            $customizeFilter['main_type'] = $filterSonTicket['main_type'];
        }

        $filterStr = "o.order_ticket <> 0 or o.revoke_ticket <> 0 or o.cancel_ticket <> 0 or o.after_sale_ticket_num <> 0";
        if ($this->_judgePrintNum()) {
            $filterStr .= " or o.print_num <> 0";
        }
        !$isSonTicket && $filter['_string'] = $filterStr;

        //子票维度需要处理下
        if ($isSonTicket) {
            return $this->_getJoinCustomizeListByPackSonTicket($tableName, $customizeTable, $groupBy, $filter, $customizeFilter, $page, $size, $isExcel);
        }

        $firstSubField = 'o.date, o.fid, o.reseller_id, o.lid, o.tid, o.pid, o.level, o.operate_id, o.pay_way, o.channel, o.site_id, o.main_tid, o.sub_merchant_id, o.main_type,
                        SUM(o.order_num) as order_num, SUM(o.order_ticket) as order_ticket, SUM(o.cancel_num) as cancel_num,
                        SUM(o.cancel_ticket) as cancel_ticket, SUM(o.revoke_num) as revoke_num, SUM(o.revoke_ticket) as revoke_ticket,
                        SUM(o.cost_money) as cost_money, SUM(o.sale_money) as sale_money, SUM(o.cancel_cost_money) as cancel_cost_money,
                        SUM(o.cancel_sale_money) as cancel_sale_money, SUM(o.revoke_cost_money) as revoke_cost_money, 
                        SUM(o.revoke_sale_money) as revoke_sale_money, SUM(o.service_money) as service_money, 
                        SUM(o.after_sale_ticket_num) as after_sale_ticket_num,SUM(o.after_sale_refund_money) as after_sale_refund_money, 
                        SUM(o.after_sale_income_money) as after_sale_income_money';

        //归档处理 ->归档移除 2020之前的移除取票统计
        if ($this->_judgePrintNum()) {
            $firstSubField = $firstSubField . ', SUM(o.print_num) as print_num';
        }

        $firstGroupBY = $groupBy;
        if (strstr($firstGroupBY, 'operate_id') === false) {
            $firstGroupBY .= ',operate_id';
        }

        $firstSubSql = $this->table($tableName . ' o')
                            ->field($firstSubField)
                            ->where($filter)
                            ->group($firstGroupBY)
                            ->buildSql();

        $secondSubField = 'cal_date, fid, reseller_id, lid, tid, pid, lvl, main_tid, sub_merchant_id, operate_id, pay_way, channel, site_id, SUM(window_real_money) window_real_money, 
                        SUM(cast(window_cancel_money as signed) - cast(window_cancel_service_money as signed)) window_cancel_money';

        $customizeGroup = str_replace('date', 'cal_date', $groupBy);
        $customizeGroup = str_replace('level', 'lvl', $customizeGroup);

        $secondSubSql = $this->table($customizeTable)
                             ->field($secondSubField)
                             ->where($customizeFilter)
                             ->group($customizeGroup)
                             ->buildSql();

        $subField = 'o.date, o.fid, o.reseller_id, o.lid, o.tid, o.pid, o.level, o.operate_id, o.pay_way, o.channel, o.site_id, o.main_tid, o.sub_merchant_id,
                    order_num, order_ticket, cancel_num, cancel_ticket, revoke_num, revoke_ticket, cost_money, sale_money,
                    cancel_cost_money, cancel_sale_money, revoke_cost_money, revoke_sale_money, service_money, 
                    window_real_money, window_cancel_money, after_sale_ticket_num, after_sale_refund_money, after_sale_income_money';

        //归档处理 ->归档移除 2020之前的移除取票统计
        if ($this->_judgePrintNum()) {
            $subField = $subField . ', print_num';
        }

        $joinCondition = ' c.cal_date = o.date AND c.fid = o.fid AND c.reseller_id = o.reseller_id AND c.lid = o.lid 
                            AND c.tid = o.tid AND c.pid = o.pid AND c.lvl = o.level AND c.operate_id = o.operate_id AND 
                            c.pay_way = o.pay_way AND c.channel = o.channel AND c.site_id = o.site_id AND c.main_tid = o.main_tid AND c.sub_merchant_id = o.sub_merchant_id';

        $subSql = $this->table($firstSubSql . ' o')
                       ->join($secondSubSql . ' c ON ' . $joinCondition, 'LEFT')
                       ->field($subField)
                       ->where($filter)
                       ->buildSql();

        $field = 'SUM(a.order_num) as order_num, SUM(a.order_ticket) as order_ticket, SUM(a.cancel_num) as cancel_num,
            SUM(a.cancel_ticket) as cancel_ticket, SUM(a.revoke_num) as revoke_num, SUM(a.revoke_ticket) as revoke_ticket,
            SUM(a.cost_money) as cost_money, SUM(a.sale_money) as sale_money, SUM(a.cancel_cost_money) as cancel_cost_money,
            SUM(a.cancel_sale_money) as cancel_sale_money, SUM(a.revoke_cost_money) as revoke_cost_money, 
            SUM(a.revoke_sale_money) as revoke_sale_money, SUM(a.service_money) as service_money, 
            SUM(a.window_real_money) as window_real_money, SUM(a.window_cancel_money) as window_cancel_money, 
            SUM(a.after_sale_ticket_num) as after_sale_ticket_num, SUM(a.after_sale_refund_money) as after_sale_refund_money, 
            SUM(a.after_sale_income_money) as after_sale_income_money';

        //归档处理 ->归档移除 2020之前的移除取票统计
        if ($this->_judgePrintNum()) {
            $field = $field . ', SUM(a.print_num) as print_num';
        }

        //缺失逗号异常处理
        if (!empty($groupBy) && substr($groupBy, 0, 1) != ',') {
            $tmpGroup    = explode(',', $groupBy);
            $newTmpGroup = [];
            foreach ($tmpGroup as $tmp) {
                $newTmpGroup[] = 'a.' . $tmp;
            }

            if (!empty($newTmpGroup)) {
                $groupBy = implode(',', $newTmpGroup);
            }

            $field = $field . ',' . $groupBy;
        }

        if ($isExcel) {
            $queryRes = $this->table($subSql . ' a')->field($field)->group($groupBy)->select();
        } else {
            $queryRes = $this->table($subSql . ' a')->field($field)->group($groupBy)->page($page, $size)->select();
        }

        $list = is_array($queryRes) ? $queryRes : [];
        foreach ($list as $key => $tmp) {
            if (empty($tmp['window_real_money'])) {
                $list[$key]['window_real_money'] = 0;
            }
            if (empty($tmp['window_cancel_money'])) {
                $list[$key]['window_cancel_money'] = 0;
            }
        }

        //总计数据
        $sumRes = $this->table($subSql . ' a')->field($field)->find();
        $sum    = is_array($sumRes) ? $sumRes : [];

        // 总条数
        $subTotalSql = $this->table($subSql . ' a')->field('a.date')->group($groupBy)->buildSql();
        $total       = $this->table($subTotalSql . ' t')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }



    public function getReportDistinct(int $startDate, int $endDate, string $field, string $type = 'order')
    {
        if ($startDate == $endDate) {
            $where['date'] = $startDate;
        } else {
            $where['date'] = ['BETWEEN', [$startDate, $endDate]];
        }

        if ($type == 'order') {
            $table = $this->_orderTwo;
        } else {
            $table = $this->_checkTwo;
        }

        $field  = "DISTINCT({$field}) as {$field}";
        $result = $this->table($table)->where($where)->field($field)->select();
        return is_array($result) ? $result : [];
    }


    /**
     * 获取小时端检票数据
     * User: lanwanhui
     * Date: 2022/5/10
     *
     * @param int $page      页码
     * @param int $pageSize  每页大小
     * @param int $fid      供应商id
     * @param int $lid      景区id
     * @param int $begin    开始时间 yyyymmddhh
     * @param int $end      结束时间 yyyymmddhh
     *
     * @return array
     */
    public function getReportCheckHourList($page, $pageSize, $fid, $lid, $begin, $end)
    {
        if (empty($fid) || empty($begin) || empty($end)) {
            return [];
        }

        $where = [
            'fid' => $fid,
            'date_hour' => ['between', [$begin, $end]],
        ];

        if (!empty($lid)) {
            $where['lid'] = $lid;
        }

        $list = $this->table($this->_checkTwoHour)
            ->where($where)
            ->field('lid, tid, date_hour, SUM(order_ticket) as total')
            ->group('lid,tid,date_hour')
            ->page($page, $pageSize)
            ->select();

        if (empty($list)) {
            return ['list' => [], 'total' => 0];
        }

        $subOrderSql = $this->table($this->_checkTwoHour)
            ->field('id')
            ->where($where)
            ->group('lid,tid,date_hour')
            ->buildSql();

        $total = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total];

    }

    /**
     * 报表日汇总子票维度处理
     * <AUTHOR>
     * @date   2023/1/12
     *
     * @param  string  $tableName    查询主表名称
     * @param  array   $filter       查询条件
     * @param  string  $field        查询字段
     * @param  string  $groupBy      分组查询字段
     * @param  int     $page         页码
     * @param  int     $size         页数
     * @param  bool    $isExcel      是否Excel
     *
     * @return array|mixed
     */
    public function _getListByPackSonTicket(string $tableName, array $filter, string $field, string $groupBy, int $page = 1, int $size = 15, bool $isExcel = false)
    {
        $field != '*' && $field = $this->_addFieldToQuery($field, ['main_tid']);
        //子票查询条件
        $sonWhereStr = $this->_setQueryParamsToStr($filter, ['reseller_id', 'channel', 'operate_id', 'pay_way', 'site_id', 'fid']);
        //主票查询条件
        $parentWhereStr = $this->_setQueryParamsToStr($filter, ['lid', 'tid']);

        //过滤子票
        $subFilter             = $filter;
        $subFilter['main_tid'] = ['eq', 0];

        //分组查询
        $groupBy = $this->_addFieldToQuery($groupBy, ['main_tid']);

        //主票数据
        if ($isExcel) {
            $list = $this->table($tableName)->where($subFilter)->group($groupBy)->field($field)->select();
        } else {
            $list = $this->table($tableName)->where($subFilter)->group($groupBy)->field($field)->page($page, $size)->select();
        }
        $list = is_array($list) ? $list : [];

        //子票数据
        if (!empty($list)) {
            $mainTidArr = array_unique(array_column($list, 'tid'));
            $dateArr    = array_unique(array_column($list, 'date'));
            if (!empty($mainTidArr) && !empty($dateArr)) {
                $sonFilter = [
                    'main_tid' => ['IN', $mainTidArr],
                    'date'     => ['IN', $dateArr],
                ];
                !empty($sonWhereStr) && $sonFilter['_string'] = $sonWhereStr;
                $sonList = $this->table($tableName)->where($sonFilter)->group($groupBy)->field($field)->select();
                $sonList = is_array($sonList) ? $sonList : [];
                $list    = array_merge($list, $sonList);
            }
        }

        //获取总计数据
        $subOrderSumSql = $this->table($tableName)->where($subFilter)->field('tid')->group('tid')->buildSql();
        $sonWhereStr    = empty($sonWhereStr) ? "" : " and $sonWhereStr";
        $parentWhereStr = empty($parentWhereStr) ? "" : " and $parentWhereStr";
        unset($filter['lid'], $filter['tid']);
        $filter['_string'] = "(main_tid = 0 $parentWhereStr) or (main_tid in $subOrderSumSql $sonWhereStr)";
        $sum               = $this->table($tableName)->where($filter)->field($field)->find();

        //获取总行数
        $subSql = $this->table($tableName)->where($subFilter)->field('id')->group($groupBy)->buildSql();
        $total  = $this->table($subSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 报表关联定制报表查询（子票维度）
     * <AUTHOR>
     * @date   2023/1/16
     *
     * @param  string  $tableName          查询主表名称
     * @param  string  $customizeTable     查询定制版表名称
     * @param  string  $groupBy            分组查询字段
     * @param  array   $filter             主查询条件
     * @param  array   $customizeFilter    特殊定制表查询条件
     * @param  int     $page               页码
     * @param  int     $size               页数
     * @param  bool    $isExcel            会否导出excel
     *
     * @return array|mixed
     */
    private function _getJoinCustomizeListByPackSonTicket(string $tableName, string $customizeTable, string $groupBy = '', array $filter = [], array $customizeFilter = [], int $page = 1, int $size = 15, bool $isExcel = false)
    {
        //子票查询条件
        $sonWhereStr = $this->_setQueryParamsToStr($filter, ['o.reseller_id', 'o.channel', 'o.operate_id', 'o.pay_way', 'o.site_id', 'o.fid']);
        //主票查询条件
        $parentWhereStr = $this->_setQueryParamsToStr($filter, ['o.lid', 'o.tid']);

        //过滤子票
        $subFilter               = $filter;
        $subFilter['o.main_tid'] = ['eq', 0];

        //子票字段加入分组查询
        $groupBy = $this->_addFieldToQuery($groupBy, ['main_tid']);

        $firstSubField = 'o.date, o.fid, o.reseller_id, o.lid, o.tid, o.pid, o.level, o.operate_id, o.pay_way, o.channel, o.site_id, o.main_tid, o.sub_merchant_id,
                        SUM(o.order_num) as order_num, SUM(o.order_ticket) as order_ticket, SUM(o.cancel_num) as cancel_num,
                        SUM(o.cancel_ticket) as cancel_ticket, SUM(o.revoke_num) as revoke_num, SUM(o.revoke_ticket) as revoke_ticket,
                        SUM(o.cost_money) as cost_money, SUM(o.sale_money) as sale_money, SUM(o.cancel_cost_money) as cancel_cost_money,
                        SUM(o.cancel_sale_money) as cancel_sale_money, SUM(o.revoke_cost_money) as revoke_cost_money, 
                        SUM(o.revoke_sale_money) as revoke_sale_money, SUM(o.service_money) as service_money, SUM(o.print_num) as print_num';

        $firstGroupBY = $groupBy;
        $firstGroupBY = $this->_addFieldToQuery($firstGroupBY, ['operate_id']);

        $secondSubField = 'cal_date, fid, reseller_id, lid, tid, pid, lvl, operate_id, pay_way, channel, site_id, main_tid, sub_merchant_id, 
                        SUM(window_real_money) window_real_money, 
                        SUM(cast(window_cancel_money as signed) - cast(window_cancel_service_money as signed)) window_cancel_money';

        $customizeGroup = str_replace('date', 'cal_date', $groupBy);
        $customizeGroup = str_replace('level', 'lvl', $customizeGroup);
        $customizeGroup = $this->_addFieldToQuery($customizeGroup, ['operate_id']);

        //过滤子票
        $subCusFilter             = $customizeFilter;
        $subCusFilter['main_tid'] = ['eq', 0];

        $subField = 'o.date, o.fid, o.reseller_id, o.lid, o.tid, o.pid, o.level, o.operate_id, o.pay_way, o.channel, o.site_id, o.main_tid, o.sub_merchant_id,
                    order_num, order_ticket, cancel_num, cancel_ticket, revoke_num, revoke_ticket, cost_money, sale_money,
                    cancel_cost_money, cancel_sale_money, revoke_cost_money, revoke_sale_money, service_money, print_num, 
                    window_real_money, window_cancel_money';

        $joinCondition = ' c.cal_date = o.date AND c.fid = o.fid AND c.reseller_id = o.reseller_id AND c.lid = o.lid 
                            AND c.tid = o.tid AND c.pid = o.pid AND c.lvl = o.level AND c.operate_id = o.operate_id AND 
                            c.pay_way = o.pay_way AND c.channel = o.channel AND c.site_id = o.site_id and c.main_tid = o.main_tid and c.sub_merchant_id = o.sub_merchant_id';

        $field = 'SUM(a.order_num) as order_num, SUM(a.order_ticket) as order_ticket, SUM(a.cancel_num) as cancel_num,
            SUM(a.cancel_ticket) as cancel_ticket, SUM(a.revoke_num) as revoke_num, SUM(a.revoke_ticket) as revoke_ticket,
            SUM(a.cost_money) as cost_money, SUM(a.sale_money) as sale_money, SUM(a.cancel_cost_money) as cancel_cost_money,
            SUM(a.cancel_sale_money) as cancel_sale_money, SUM(a.revoke_cost_money) as revoke_cost_money, 
            SUM(a.revoke_sale_money) as revoke_sale_money, SUM(a.service_money) as service_money, 
            SUM(a.window_real_money) as window_real_money, SUM(a.window_cancel_money) as window_cancel_money';

        //归档处理 ->归档移除 2020之前的移除取票统计
        if ($this->_judgePrintNum()) {
            $field = $field . ', SUM(a.print_num) as print_num';
        }

        //缺失逗号异常处理
        if (!empty($groupBy) && substr($groupBy, 0, 1) != ',') {
            $tmpGroup    = explode(',', $groupBy);
            $newTmpGroup = [];
            foreach ($tmpGroup as $tmp) {
                $newTmpGroup[] = 'a.' . $tmp;
            }

            if (!empty($newTmpGroup)) {
                $groupBy = implode(',', $newTmpGroup);
            }

            $field = $field . ',' . $groupBy;
        }

        $firstSubSql  = $this->table($tableName . ' o')->field($firstSubField)->where($subFilter)->group($firstGroupBY)->buildSql();
        $secondSubSql = $this->table($customizeTable)->field($secondSubField)->where($subCusFilter)->group($customizeGroup)->buildSql();
        $subSql       = $this->table($firstSubSql . ' o')->join($secondSubSql . ' c ON ' . $joinCondition,
            'LEFT')->field($subField)->where($subFilter)->buildSql();

        //主票信息查询
        if ($isExcel) {
            $list = $this->table($subSql . ' a')->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($subSql . ' a')->field($field)->group($groupBy)->page($page, $size)->select();
        }
        $list = is_array($list) ? $list : [];

        //子票数据
        if (!empty($list)) {
            $mainTidArr = array_unique(array_column($list, 'tid'));
            $dateArr    = array_unique(array_column($list, 'date'));
            if (!empty($mainTidArr)) {
                $sonFilter             = [
                    'o.main_tid' => ['IN', $mainTidArr],
                    'o.date'     => ['IN', $dateArr],
                ];
                !empty($sonWhereStr) && $sonFilter['_string'] = $sonWhereStr;

                $sonSubFilter               = [
                    'o.main_tid' => ['IN', $mainTidArr],
                    'o.date'     => ['IN', $dateArr],
                ];
                !empty($sonWhereStr) && $sonSubFilter['_string'] = $sonWhereStr;

                $sonSubCusFilter             = $customizeFilter;
                $sonSubCusFilter['main_tid'] = ['IN', $mainTidArr];
                $sonSubCusFilter['cal_date'] = ['IN', $dateArr];
                unset($sonSubCusFilter['pid'], $sonSubCusFilter['lid'], $sonSubCusFilter['tid']);

                $firstSubSql  = $this->table($tableName . ' o')->field($firstSubField)->where($sonSubFilter)->group($firstGroupBY)->buildSql();
                $secondSubSql = $this->table($customizeTable)->field($secondSubField)->where($sonSubCusFilter)->group($customizeGroup)->buildSql();
                $subSql       = $this->table($firstSubSql . ' o')->join($secondSubSql . ' c ON ' . $joinCondition,
                    'LEFT')->field($subField)->where($sonFilter)->buildSql();

                $sonList      = $this->table($subSql . ' a')->field($field)->group($groupBy)->select();
                $sonList      = is_array($sonList) ? $sonList : [];
                $list = array_merge($list, $sonList);
            }
        }

        foreach ($list as $key => $tmp) {
            if (empty($tmp['window_real_money'])) {
                $list[$key]['window_real_money'] = 0;
            }
            if (empty($tmp['window_cancel_money'])) {
                $list[$key]['window_cancel_money'] = 0;
            }
        }

        //其他查询 汇总和总行数
        $subOtherSql = $this->table($tableName . ' o')->where($subFilter)->field('o.tid')->group('o.tid')->buildSql();

        //加入子票查询条件
        $sonWhereStr    = empty($sonWhereStr) ? "" : " and $sonWhereStr";
        $parentWhereStr = empty($parentWhereStr) ? "" : " and $parentWhereStr";
        unset($filter['o.lid'], $filter['o.tid']);
        $filter['_string'] = "(o.main_tid = 0 $parentWhereStr) or (o.main_tid in $subOtherSql $sonWhereStr)";

        $firstSubSql  = $this->table($tableName . ' o')->field($firstSubField)->where($filter)->group($firstGroupBY)->buildSql();
        $secondSubSql = $this->table($customizeTable)->field($secondSubField)->where($customizeFilter)->group($customizeGroup)->buildSql();

        $otherSql = $this->table($firstSubSql . ' o')->join($secondSubSql . ' c ON ' . $joinCondition,
            'LEFT')->field($subField)->where($filter)->buildSql();

        //总计数据
        $sumRes = $this->table($otherSql . ' a')->field($field)->find();
        $sum    = is_array($sumRes) ? $sumRes : [];

        // 总条数
        $subTotalFilter = ['a.main_tid' => ['eq', 0]];
        $subTotalSql    = $this->table($otherSql . ' a')->where($subTotalFilter)->field('a.date')->group($groupBy)->buildSql();
        $total          = $this->table($subTotalSql . ' t')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 月报表子票维度处理
     * <AUTHOR>
     * @date   2023/1/17
     *
     * @param  string  $tableName    表名称
     * @param  array   $filter       查询条件
     * @param  string  $field        查询字段
     * @param  string  $groupBy      分组查询字段
     * @param  int     $page         页码
     * @param  int     $size         椰树
     * @param  bool    $isExcel      是否是excel导出
     *
     * @return array|mixed
     */
    private function _getMonthListByPackSonTicket(string $tableName, array $filter, string $field, string $groupBy, int $page = 1, int $size = 15, bool $isExcel = false)
    {
        $field = $this->_addFieldToQuery($field, ['main_tid']);

        //子票查询条件
        $sonWhereStr = $this->_setQueryParamsToStr($filter, ['reseller_id', 'channel', 'operate_id', 'pay_way', 'site_id', 'fid']);
        //主票查询条件
        $parentWhereStr = $this->_setQueryParamsToStr($filter, ['lid', 'tid']);

        //过滤子票
        $subFilter             = $filter;
        $subFilter['main_tid'] = ['eq', 0];

        //分组查询
        $groupBy = $this->_addFieldToQuery($groupBy, ['main_tid']);

        //主票查询
        if ($isExcel) {
            $list = $this->table($tableName)->where($subFilter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($tableName)->where($subFilter)->field($field)->group($groupBy)->page($page, $size)->select();
        }
        $list = is_array($list) ? $list : [];

        //子票查询
        if (!empty($list)) {
            $mainTidArr = array_unique(array_column($list, 'tid'));
            $dateArr    = array_unique(array_column($list, 'date'));
            if (!empty($mainTidArr)) {
                $sonFilter             = [
                    'main_tid' => ['IN', $mainTidArr],
                    'date'     => ['IN', $dateArr],
                ];
                !empty($sonWhereStr) && $sonFilter['_string'] = $sonWhereStr;
                $sonList = $this->table($tableName)->where($sonFilter)->field($field)->group($groupBy)->select();
                $sonList = is_array($sonList) ? $sonList : [];
                $list    = array_merge($list, $sonList);
            }
        }

        //获取总计数据
        $subOrderSumSql = $this->table($tableName)->where($subFilter)->field('tid')->group('tid')->buildSql();
        $sonWhereStr    = empty($sonWhereStr) ? "" : " and $sonWhereStr";
        $parentWhereStr = empty($parentWhereStr) ? "" : " and $parentWhereStr";
        unset($filter['lid'], $filter['tid']);
        $filter['_string'] = "(main_tid = 0 $parentWhereStr) or (main_tid in $subOrderSumSql $sonWhereStr)";
        $sum               = $this->table($tableName)->where($filter)->field($field)->find();

        //获取总行数
        $subSql = $this->table($tableName)->where($subFilter)->field('id')->group($groupBy)->buildSql();
        $total  = $this->table($subSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 小时报表子票维度处理
     * <AUTHOR>
     * @date   2023/1/17
     *
     * @param  string  $tableName    表名称
     * @param  array   $filter       查询条件
     * @param  string  $groupBy      分组查询字段
     * @param  string  $field        查询字段
     * @param  int     $page         页码
     * @param  int     $size         页数
     * @param  bool    $isExcel      是否是导出excel
     *
     * @return array|mixed
     */
    private function _getHourListByPackSonTicket(string $tableName, array $filter, string $groupBy, string $field, int $page = 1, int $size = 10, bool $isExcel = false)
    {
        $field = $this->_addFieldToQuery($field, ['main_tid', 'date_hour']);
        //子票查询条件
        $sonWhereStr = $this->_setQueryParamsToStr($filter, ['reseller_id', 'channel', 'operate_id', 'pay_way', 'site_id', 'fid']);
        //主票查询条件
        $parentWhereStr = $this->_setQueryParamsToStr($filter, ['lid', 'tid']);

        //过滤子票
        $subFilter             = $filter;
        $subFilter['main_tid'] = ['eq', 0];

        //要按小时维度处理
        $groupBy = $this->_addFieldToQuery($groupBy, ['main_tid', 'date_hour']);

        //主票信息查询
        if ($isExcel) {
            $list = $this->table($tableName)->where($subFilter)->field($field)->group($groupBy)->select();
        } else {
            $list = $this->table($tableName)->where($subFilter)->field($field)->group($groupBy)->page($page, $size)->select();
        }
        $list = is_array($list) ? $list : [];

        //子票数据
        if (!empty($list)) {
            $mainTidArr = array_unique(array_column($list, 'tid'));
            $dateArr    = array_unique(array_column($list, 'date_hour'));
            if (!empty($mainTidArr)) {
                $sonFilter             = [
                    'main_tid'  => ['IN', $mainTidArr],
                    'date_hour' => ['IN', $dateArr],
                ];
                !empty($sonWhereStr) && $sonFilter['_string'] = $sonWhereStr;
                $sonList = $this->table($tableName)->where($sonFilter)->field($field)->group($groupBy)->select();
                $sonList = is_array($sonList) ? $sonList : [];
                $list    = array_merge($list, $sonList);
            }
        }

        //获取总计数据
        $subOrderSumSql = $this->table($tableName)->where($subFilter)->field('tid')->group('tid')->buildSql();
        $sonWhereStr    = empty($sonWhereStr) ? "" : " and $sonWhereStr";
        $parentWhereStr = empty($parentWhereStr) ? "" : " and $parentWhereStr";
        unset($filter['lid'], $filter['tid']);
        $filter['_string'] = "(main_tid = 0 $parentWhereStr) or (main_tid in $subOrderSumSql $sonWhereStr)";
        $sum               = $this->table($tableName)->where($filter)->field($field)->find();

        //获取总行数
        $subSql = $this->table($tableName)->where($subFilter)->field('id')->group($groupBy)->buildSql();
        $total  = $this->table($subSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 查询条件转为字串条件
     * 目前仅支持in，以及单个值
     * <AUTHOR>
     * @date   2023/2/25
     *
     * @param  array  $filter    条件
     * @param  array  $keys      转换字段
     *
     * @return string
     */
    public function _setQueryParamsToStr(array $filter, array $keys)
    {
        if (empty($filter) || empty($keys)) {
            return '';
        }
        $where = [];
        foreach ($keys as $key) {
            if (!empty($filter[$key])) {
                $keyName = $key;
                if (!strstr($keyName, '.')) {
                    $keyName = "`{$keyName}`";
                }
                //支持in
                if (is_array($filter[$key]) && isset($filter[$key][0]) && in_array(strtolower($filter[$key][0]),
                        ['in'])) {
                    !empty($filter[$key][1]) && $where[] = " $keyName {$filter[$key][0]} ('" . implode("','",
                            $filter[$key][1]) . "')";
                }

                //支持单个值
                if (!is_array($filter[$key])) {
                    $where[] = " $keyName = '{$filter[$key]}' ";
                }
            }
        }

        return implode(' and ', $where);
    }

    /**
     * 添加查询字段
     * <AUTHOR>
     * @date   2023/2/25
     *
     * @param  string  $field      字段 逗号隔开的
     * @param  array   $addKeys    添加字段
     *
     * @return string
     */
    private function _addFieldToQuery(string $field, array $addKeys)
    {
        if (empty($addKeys)) {
            return $field;
        }

        $fields = explode(',', $field);
        foreach ($addKeys as $key) {
            if (!in_array($key, $fields)) {
                $fields[] = $key;
            }
        }

        return implode(',', $fields);
    }

    /**
     * 预订报表（分钟）
     * <AUTHOR>
     * @date   2023/10/16
     *
     * @param          $beginDate
     * @param          $endDate
     * @param  string  $fid
     * @param  string  $lid
     * @param  string  $pid
     * @param  string  $tid
     * @param  string  $resellerGroup
     * @param  string  $resellerId
     * @param  string  $channel
     * @param  string  $operateId
     * @param  string  $payWay
     * @param  string  $groupBy
     * @param  string  $level
     * @param  int     $page
     * @param  int     $size
     * @param  false   $siteId
     * @param  array   $notInArr
     * @param  array   $extFilter
     *
     * @return array
     */
    public function getMinuteOrderV2List($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '', $groupBy = '', $level = '', $page = 1, $size = 15, $siteId = false, $notInArr = [], $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_orderFiveMinuteReport . '_' . $this->_yearMark : $this->_orderFiveMinuteReport;

        $beginMinute = date('YmdHi', strtotime($beginDate));
        $endMinute   = date('YmdHi', strtotime($endDate));
        $beginDate   = date('Ymd', strtotime($beginDate));
        $endDate     = date('Ymd', strtotime($endDate));
        $filter      = [
            'date'        => ['between', [$beginDate, $endDate]],
            'date_minute' => ['between', [$beginMinute, $endMinute]],
        ];

        // 景区id $fid 有可能是数组， 这里需要判断
        if (is_array($fid)) {
            $filter['fid'] = ['in', $fid];
        } else {
            $fidArr = explode(',', $fid);
            // 判断是否模糊条件查询
            if (count($fidArr) > 1) {
                $filter['fid'] = ['in', $fidArr];
            } else {
                $filter['fid'] = $fid;
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = ['in', $lid];
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($notInArr['not_lid'])) {
            $notLidArr = is_array($notInArr['not_lid']) ? $notInArr['not_lid'] : explode(',', $notInArr['not_lid']);
            !empty($notLidArr) && $filter['_complex'][] = ['lid' => ['not in', $notLidArr]];
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        if ($siteId !== false && $siteId != '') {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        //新增取票数量统计print_num by jackchb 20200819
        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, SUM(cancel_num) as cancel_num,
        SUM(cancel_ticket) as cancel_ticket, SUM(revoke_num) as revoke_num, SUM(revoke_ticket) as revoke_ticket,
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money, SUM(cancel_cost_money) as cancel_cost_money,
        SUM(cancel_sale_money) as cancel_sale_money, SUM(revoke_cost_money) as revoke_cost_money,
        SUM(revoke_sale_money) as revoke_sale_money, SUM(service_money) as service_money, 
        SUM(after_sale_ticket_num) as after_sale_ticket_num, SUM(after_sale_refund_money) as after_sale_refund_money,
        SUM(after_sale_income_money) as after_sale_income_money';

        //归档处理 ->归档移除 2020之前的移除取票统计
        if ($this->_judgePrintNum()) {
            $field = $field . ', SUM(print_num) as print_num';
        }

        //缺失逗号异常处理
        if (!empty($groupBy) && substr($groupBy, 0, 1) != ',') {
            $field = $field . ',' . $groupBy;
        }

//        if ($groupBy == 'reseller_id') {
//            //主要针对分销商只有 散客、云票务散客这样的用户数据
//            $baseSql = $this->table($tableName . ' FORCE INDEX(index_fid)')->where($filter)->group($groupBy);
//        } else {
            $baseSql = $this->table($tableName)->where($filter)->group($groupBy);
//        }

        $list = $baseSql->field($field)->page($page, $size)->select();

        $list = is_array($list) ? $list : [];
        $sum  = $this->table($tableName)->where($filter)->field($field)->find();

//        if ($groupBy == 'reseller_id') {
//            //主要针对分销商只有 散客、云票务散客这样的用户数据
//            $subOrderSql = $this->table($tableName . ' FORCE INDEX(index_fid)')->where($filter)->field('id')->group($groupBy)->buildSql();
//        } else {
            $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
//        }

        $total = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 验证报表（分钟）
     * <AUTHOR>
     * @date   2023/10/16
     *
     * @param          $beginDate
     * @param          $endDate
     * @param  string  $fid
     * @param  string  $lid
     * @param  string  $pid
     * @param  string  $tid
     * @param  string  $resellerGroup
     * @param  string  $resellerId
     * @param  string  $channel
     * @param  string  $operateId
     * @param  string  $payWay
     * @param  string  $groupBy
     * @param  string  $level
     * @param  int     $page
     * @param  int     $size
     * @param  false   $siteId
     * @param  array   $notInArr
     * @param  array   $extFilter
     *
     * @return array
     */
    public function getMinuteCheckV2List($beginDate, $endDate, $fid = '', $lid = '', $pid = '', $tid = '', $resellerGroup = '', $resellerId = '', $channel = '', $operateId = '', $payWay = '', $groupBy = '', $level = '', $page = 1, $size = 15, $siteId = false, $notInArr = [], $extFilter = [])
    {
        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return [];
        }

        $tableName = $this->_yearMark ? $this->_checkFiveMinuteReport . '_' . $this->_yearMark : $this->_checkFiveMinuteReport;

        $beginMinute = date('YmdHi', strtotime($beginDate));
        $endMinute   = date('YmdHi', strtotime($endDate));

        $beginDate = date('Ymd', strtotime($beginDate));
        $endDate   = date('Ymd', strtotime($endDate));
        $filter    = [
            'date'        => ['between', [$beginDate, $endDate]],
            'date_minute' => ['between', [$beginMinute, $endMinute]],
        ];

        // 景区id $fid 有可能是数组， 这里需要判断
        if (is_array($fid)) {
            $filter['fid'] = ['in', $fid];
        } else {
            $fidArr = explode(',', $fid);
            // 判断是否模糊条件查询
            if (count($fidArr) > 1) {
                $filter['fid'] = ['in', $fidArr];
            } else {
                $filter['fid'] = $fid;
            }
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $filter['lid'] = ['in', $lid];
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $filter['lid'] = ['in', $lidArr];
                } else {
                    $filter['lid'] = $lid;
                }
            }
        }

        if (!empty($notInArr['not_lid'])) {
            $notLidArr = is_array($notInArr['not_lid']) ? $notInArr['not_lid'] : explode(',', $notInArr['not_lid']);
            !empty($notLidArr) && $filter['_complex'][] = ['lid' => ['not in', $notLidArr]];
        }

        if (!empty($pid)) {
            $pidArr        = explode(',', $pid);
            $filter['pid'] = ['in', $pidArr];
        }

        if (empty($pid) && !empty($notInArr['not_pid'])) {
            $notPidArr = explode(',', $notInArr['not_pid']);
            !empty($notPidArr) && $filter['pid'] = ['not in', $notPidArr];
        }

        if (!empty($tid)) {
            $tidArr        = explode(',', $tid);
            $filter['tid'] = ['in', $tidArr];
        }

        if (empty($tid) && !empty($notInArr['not_tid'])) {
            $notTidArr = explode(',', $notInArr['not_tid']);
            !empty($notTidArr) && $filter['tid'] = ['not in', $notTidArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup         = explode(',', $resellerGroup);
            $filter['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr         = explode(',', $resellerId);
            $filter['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr        = explode(',', $channel);
            $filter['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr         = explode(',', $operateId);
            $filter['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr         = explode(',', $payWay);
            $filter['pay_way'] = ['in', $payWayArr];
        }

        if (!empty($level)) {
            $filter['level'] = $level;
        }

        //站点
        if ($siteId !== false && $siteId != '') {
            $siteId            = explode(',', $siteId);
            $filter['site_id'] = ['in', $siteId];
        }

        //不勾选套票子票展示的时候，需要过滤
        $filter = $this->filterSonTicket($filter, $extFilter);

        $field = 'SUM(order_num) as order_num, SUM(order_ticket) as order_ticket, 
        SUM(cost_money) as cost_money, SUM(sale_money) as sale_money,
        SUM(finish_num) as finish_num, SUM(finish_ticket) as finish_ticket,
        SUM(finish_cost_money) as finish_cost_money, SUM(finish_sale_money) as finish_sale_money,
        SUM(revoke_ticket) as revoke_ticket, SUM(revoke_num) as revoke_num,
        SUM(revoke_cost_money) as revoke_cost_money, SUM(revoke_sale_money) as revoke_sale_money,today_check,
        SUM(service_money) AS service_money, SUM(after_sale_ticket_num) as after_sale_ticket_num,
        SUM(after_sale_refund_money) as after_sale_refund_money, SUM(after_sale_income_money) as after_sale_income_money';

        if (!empty($groupBy)) {
            $field = $field . ',' . $groupBy;
        }

//        if ($groupBy == 'reseller_id' && in_array(ENV, ['TEST', 'IS_PFT_GRAY', 'PRODUCTION'])) {
//            //主要针对分销商只有 散客、云票务散客这样的用户数据
//            $baseSql = $this->table($tableName . ' FORCE INDEX(index_fid)')->where($filter)->group($groupBy);
//        } else {
            $baseSql = $this->table($tableName)->where($filter)->group($groupBy);
//        }

        $list = $baseSql->field($field)->page($page, $size)->select();

        $list = is_array($list) ? $list : [];
        $sum  = $this->table($tableName)->where($filter)->field($field)->find();

//        if ($groupBy == 'reseller_id' && in_array(ENV, ['TEST', 'IS_PFT_GRAY', 'PRODUCTION'])) {
//            //主要针对分销商只有 散客、云票务散客这样的用户数据
//            $subOrderSql = $this->table($tableName . ' FORCE INDEX(index_fid)')->where($filter)->field('id')->group($groupBy)->buildSql();
//        } else {
            $subOrderSql = $this->table($tableName)->where($filter)->field('id')->group($groupBy)->buildSql();
//        }

        $total = $this->table($subOrderSql . ' a')->count();

        return ['list' => $list, 'total' => $total, 'sum' => $sum];
    }

    /**
     * 刪除分钟报表数据
     * <AUTHOR>
     * @date   2023/10/17
     *
     * @param  string  $table
     * @param  string  $date
     * @param  array   $minuteList
     * @param  int     $fid
     *
     * @return false|int|mixed|string
     */
    public function deleteMinuteReportTableData(string $table, string $date = '', array $minuteList = [], int $fid = 0)
    {
        if (empty($minuteList) && empty($date)) {
            return false;
        }

        if (!in_array($table, [$this->_orderFiveMinuteReport, $this->_checkFiveMinuteReport])) {
            return false;
        }

        $filter = [];

        if ($fid) {
            $filter['fid'] = (int)$fid;
        }

        if (!empty($date)) {
            $filter['date'] = $date;
        }

        if (!empty($minuteList)) {
            $filter['date_minute'] = ['IN', $minuteList];
        }

        return $this->table($table)->where($filter)->delete();
    }

    /**
     * 报表子票条件处理
     * <AUTHOR>
     * @date   2024/03/10
     *
     * @param  array  $filter       过滤条件
     * @param  array  $extFilter    是否展示子票
     *
     * @return array
     */
    public function filterSonTicket($filter = [], $extFilter = [])
    {
        if (!is_array($filter)) {
            return $filter;
        }

        $showSelfSonTicket = intval($extFilter['show_self_son_ticket'] ?? 0);
        $showBindSonTicket = intval($extFilter['show_bind_son_ticket'] ?? 0);

        /**
         * 规则注明：
         * 1.两种子票都不需要展示的时候，只要判断main_tid字段小于1就可以了。
         * 2.如果两种其中一种需要展示，哪另一种不展示的话，就需要结合main_type再做下二次判断；如果两种都展示，则main_type没有筛选值，全部展示。
         */
        if (!$showSelfSonTicket && !$showBindSonTicket) {
            $filter['main_tid'] = ['lt', 1];
        } else {
            //如果两种其中一种需要展示，哪另一种不展示的话，
            //就需要结合main_type再做下二次判断；
            //如果两种都展示，则main_type没有筛选值，全部展示。
            $mainType = [
                'F' => 'F',
                'H' => 'H',
            ];

            if ($showSelfSonTicket) {
                unset($mainType['F']);
            }
            if ($showBindSonTicket) {
                unset($mainType['H']);
            }

            //展示子票是包含，而不是等于，所以用not in
            !empty($mainType) && $filter['main_type'] = ['not in', array_values($mainType)];
        }

        return $filter;
    }
}