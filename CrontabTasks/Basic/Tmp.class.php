<?php

namespace CrontabTasks\Basic;

use Business\Authority\Auth\MemsRole as MemsRoleBiz;
use Business\Authority\Auth\UserRolePack as UserRolePackBiz;

class Tmp
{
    public function deleteGroupSpecialityTask()
    {
        $lib = new \Library\Model('localhost_wsdl');

        $where = ['status' => 1];

        for ($i = 1; $i <= 10; $i++) {
            $res = $lib->table('pft_evolute_group_speciality_task')->where($where)->limit(40000)->delete();

            pft_log('auto_delete_group_speciality_task', json_encode([$res, $lib->_sql()]));
            usleep(5000);
        }
    }

    // /usr/bin/php /var/www/html/Service/Crontab/runNew.php Basic_Tmp showVenuesRelevanceProductNumHandle
    /**
     * 演出场馆关联产品处理
     * @return void
     */
    public function showVenuesRelevanceProductNumHandle()
    {
        if (ENV == 'production') {
            $landModel = new \Library\Model('BI_data');
        } else {
            $landModel = new \Library\Model('localhost_wsdl');
        }
        $showModel     = new \Library\Model('pft_business_case');
        $disStorageBiz = new \Business\PftShow\DisStorage();
        $showM         = new \Model\Product\Show();

        echo '开始处理了：'. microtime(true) . "\n\r";

        $limit = 100;


        //先获取出商家对应场馆的数量
        $venueMap = $showModel->table('pft_venues')->field('apply_did,count(id) as num')->where(['status' => 0])->group('apply_did')->select();
        //根据每个商家id去刷新对应商家场馆关联的数量
        foreach ($venueMap as $item) {
            echo '开始处理商家：'. $item['apply_did'] . "，({$item['num']})个场馆" . "\n\r";
            //搜索条件
            $where = ['apply_did' => $item['apply_did'], 'status' => 0];
            //获取总页数
            $totalPage = ceil($item['num'] / $limit);
            echo '总共'. $totalPage . '页' . "\n\r";
            //循环处理每页数据
            for ($i = 1; $i <= $totalPage; $i++) {
                echo '开始处理第'. $i . '页' . "\n\r";
                $venueList   = $showModel->table('pft_venues')->field('id')->where($where)->page($i)->limit($limit)->select();
                if (!$venueList) {
                    continue;
                }
                $venuesIdArr = array_column($venueList, 'id');

                //获取场馆id关联的景区产品数量
                $landWhere = ['apply_did' => $item['apply_did'], 'status' => ['in', [1, 2]], 'venus_id' => ['in', $venuesIdArr]];
                $landRes   = $landModel->table('uu_land')->where($landWhere)->field('venus_id, count(*) as product_num')->group('venus_id')->select();

                if ($landRes) {
                    $updateIdArr = array_column($landRes, 'venus_id');
                    //将对应数量更新到演出场馆表中
                    $sql = "UPDATE pft_venues SET product_num = CASE id ";
                    foreach ($landRes as $landItem) {
                        $sql .= "WHEN {$landItem['venus_id']} THEN {$landItem['product_num']} ";
                    }
                    $sql .= "END WHERE id IN (" . implode(',', $updateIdArr) . ")";
                    $res  = $showModel->table('pft_venues')->execute($sql);
                    if ($res === false) {
                        pft_log('debug/lee', json_encode(['ac' => 'showVenuesRelevanceProductNumHandle', 'res' => $res, 'sql' => $showModel->_sql()]));
                    }
                }

                //对用户分区模板处理
                $zoneMap = [];
                if ($venuesIdArr) {
                    foreach ($venuesIdArr as $venueId) {
                        $zoneInfo = $showM->getZoneList($venueId);
                        if ($zoneInfo) {
                            $zoneMap[intval($venueId)] = array_column($zoneInfo, 'zone_id');
                        }
                    }
                }

                //批量创建初始模板
                if ($zoneMap) {
                    foreach ($zoneMap as $venueId => $zoneIdArr) {
                        $disStorageBiz->createSystemTemplage($item['apply_did'], $item['apply_did'], $venueId, [], $zoneIdArr);
                    }
                }

                echo '第'. $i . '页处理完了' . "\n\r";
            }
        }

        //先获取出场馆的总数
        //$total     = $showModel->table('pft_venues')->field('id')->where($where)->count();
        //$totalPage = ceil($total / $limit);
        //
        //echo '处理数量： ' . $total . ' 总页数： '. $totalPage . "\n\r";
        //
        //for ($i = 1; $i <= $totalPage; $i++) {
        //    echo '开始处理第'. $i . '页' . "\n\r";
        //    //分页获取场馆id
        //    $venueList   = $showModel->table('pft_venues')->field('id')->where($where)->page($i)->limit($limit)->select();
        //    $venuesIdArr = array_column($venueList, 'id');
        //
        //    //获取场馆id关联的景区产品数量
        //    $landWhere = ['status' => ['in', [1, 2]], 'venus_id' => ['in', $venuesIdArr]];
        //    $landRes   = $landModel->table('uu_land')->where($landWhere)->field('venus_id, count(*) as product_num')->group('venus_id')->select();
        //
        //    if ($landRes) {
        //        $updateIdArr =  array_column($landRes, 'venus_id');
        //
        //        //将对应数量更新到演出场馆表中
        //        $sql  = "UPDATE pft_venues SET product_num = CASE id ";
        //        foreach ($landRes as $item) {
        //            $sql .= "WHEN {$item['venus_id']} THEN {$item['product_num']} ";
        //        }
        //        $sql .= "END WHERE id IN (" . implode(',', $updateIdArr) . ")";
        //        $res  = $showModel->table('pft_venues')->execute($sql);
        //        if ($res === false) {
        //            pft_log('debug/lee', json_encode(['ac' => 'showVenuesRelevanceProductNumHandle', 'res' => $res, 'sql' => $showModel->_sql()]));
        //        }
        //    }
        //
        //    unset($venueList, $venuesIdArr, $landRes);
        //
        //    echo '第'. $i . '页处理完成' . "\n\r";
        //}

        echo '处理结束了：'. microtime(true) . "\n\r";
    }

    // /usr/bin/php /var/www/html/Service/Crontab/runNew.php Basic_Tmp showOrderHandler
    /**
     * 演出场次数据维护脚本
     */
    public function showOrderHandler()
    {
        $params = $GLOBALS['argv'];
        if (ENV == 'production') {
            $lib = new \Library\Model('bi_myuu');
        } else {
            $lib = new \Library\Model('localhost_wsdl');
        }

        $page           = 1;
        $size           = 1000;
        $startTime      = '2024-01-01 00:00:00';
        $endTime        = '2024-01-31 23:59:59';

        if (isset($params[3]) && $params[3] && strtotime($params[3])) {
            $startTime = date('Y-m-d 00:00:00', strtotime($params[3]));
        }

        if (isset($params[4]) && $params[4] && strtotime($params[4])) {
            $endTime = date('Y-m-d 23:59:59', strtotime($params[4]));
        }

        if ($startTime > $endTime) {
            echo '开始时间不能大于结束时间';
            exit;
        }

        $where          = ['product_type' => 'H', 'ordertime' => ['BETWEEN', [$startTime, $endTime]]];
        $orderToolModel = new \Model\Order\OrderTools();
        $orderDetailApi = new \Business\JavaApi\Order\OrderDetailUpdate();

        echo '开始处理了：'. microtime(true) . "\n\r";

        while (true) {
            echo '开始处理第'. $page . '页，time: ' . microtime(true) . "\n\r";
            //先获取到需要处理的订单号
            $orderRes = $lib->table('uu_ss_order')->where($where)->page($page)->limit($size)->field('ordernum')->select();
            if (!$orderRes) {
                break;
            }
            $orderNumArr = array_column($orderRes, 'ordernum');

            //通过订单号获取对应订单详情的数据
            $orderDetailInfo = $orderToolModel->getOrderDetailInfo($orderNumArr);
            if (!$orderDetailInfo) {
                continue;
            }

            $batchNoticeInfo = [];
            foreach ($orderDetailInfo as $orderDetail) {
                if (!$orderDetail['series']) {
                    continue;
                }

                //获取到演出信息中的场次id
                $seriesArr = unserialize($orderDetail['series']);
                $venueId   = $seriesArr[0] ?? 0;
                $roundId   = $seriesArr[1] ?? 0;
                $zoneId    = $seriesArr[2] ?? 0;
                $roundTime = $seriesArr[4] ?? '';

                //没有场次id 说明有问题  就不继续处理了
                if (!$roundId) {
                    continue;
                }

                //解析下ext_content
                $extContent     = $orderDetail['ext_content'] ? json_decode($orderDetail['ext_content'], true) : [];
                $showTimeHandle = \Business\PftShow\Show::showTimeHandle($roundTime);
                if ($showTimeHandle) {
                    //将本次演出信息写入到订单的ext_content中
                    $extContent['round_id']         = $roundId;
                    $extContent['venue_id']         = $venueId;
                    $extContent['zone_id']          = $zoneId;
                    $extContent['round_start_time'] = $showTimeHandle['begin_time'];
                    $extContent['round_end_time']   = $showTimeHandle['end_time'];
                }

                //更新ext_content字段信息
                $updateRes = $orderDetailApi->orderDetailInfoUpdate($orderDetail['orderid'], ['ext_content' => json_encode($extContent)]);
                if ($updateRes['code'] != 200) {
                    pft_log('show/order/handle', json_encode(['ac' => 'showOrderHandler', 'orderid' => $orderDetail['orderid'], 'round_id' => $roundId, 'msg' => $updateRes['msg']]));
                }

                //关联关系处理  演出通知发送逻辑
                //$noticeRes = (new \Business\PftShow\Show())->orderNotice($orderDetail['orderid'], $venueId);
                //if ($noticeRes['code'] != 200) {
                //    pft_log('show/order/handle', json_encode(['ac' => 'orderNotice', 'params' => [$orderDetail['orderid'], $venueId], 'res' => $noticeRes], JSON_UNESCAPED_UNICODE), 2);
                //}
                $batchNoticeInfo[$venueId][] = $orderDetail['orderid'];
            }

            if ($batchNoticeInfo) {
                //关联关系处理  演出通知发送逻辑
                $noticeRes = (new \Business\PftShow\Show())->orderNoticeBatch($batchNoticeInfo);
                if ($noticeRes['code'] != 200) {
                    pft_log('show/order/handle', json_encode(['ac' => 'orderNoticeBatch', 'params' => $batchNoticeInfo, 'res' => $noticeRes], JSON_UNESCAPED_UNICODE), 2);
                }
            }

            //获取出数组中 $orderNumArr 的最后一个数据
            $lastOrderNum = end($orderNumArr);

            echo '第'. $page . '页处理完成， time: ' . microtime(true) . '。' . "最后处理的订单号为：{$lastOrderNum}" . "\n\r";

            //休眠500毫秒
            usleep(500000);

            $page++;
        }

        echo '处理结束了：'. microtime(true) . "\n\r";

//        exit("脚本执行完了");
    }

    /**
     * 批量处理商户角色定义的非套餐依赖类型
     * /usr/bin/php /var/www/html/Service/Crontab/runNew.php Basic/Tmp merchantDtypeBindPackRole
     */
    public function merchantDtypeBindPackRole()
    {
        try {
            if (ENV == 'PRODUCTION') {
                $memberIds = [258,259,260,261,267,268,1761,1797,6361,6693,7156,7157,7289,7290,20627,20631,39105,42205,49065,66658,74141,74171,75492,161268,240140,768598,1938520,2150244,4417622,7074283,7074320,7074426,7074451,7123254,9026549];
            } else if (ENV == 'TEST') {
                $memberIds = [258,259,260,261,267,268,1761,1797,6361,6693,7156,7157,7289,7290,20627,20631,39105,42205,49065,66658,74141,74171,75492,161268,240140,915915,916001,916002,917030,3180819,3180832,3181695,3181696,3279701,3279723,3279729,3279745,3279746,3279747,3279748,3279749,3279750,3279751,3279752,3279753,3279754,3279755,3279756,3279757,3279758,3279759,3279760,3279764,3279777,3279778,3279779,3279780,3279781,3279782,3279783,3279784];
            } else {
                $memberIds = [3410,3532,3562,4068,915152,922259,922955,925451,925452,926808,926809,927159,3277513,3277544,3279921,3279922,3279924,3279925,3279926,3279927,3279928,3279929,3279930,3279939,3279949,3279961,3279982,3279991,3279996,3280005,3282517,3282518,3282519,3282520,3282521,3282522,3282523,3282524,3282525,3282526,3282527,3282528,3288127,3288152,10211547,10221413,10221414,10221415,10221416,10221425];
            }
            $params = $GLOBALS['argv'];
            $dType  = $params[3] ?? -1;
            if ($dType == -1) {
                echo "处理的商户类型参数缺失，执行失败！\n\r";
                throw new \Exception("处理的商户类型参数缺失，执行失败！");
            }
            if (empty($memberIds) || !is_array($memberIds)) {
                echo "会员id列表参数缺失，执行失败！\n\r";
                throw new \Exception("会员id列表参数缺失，执行失败！");
            }

            //查询商户角色定义的非套餐依赖类型
            $memsList = (new MemsRoleBiz())->getRelationAll([], [], [], 2);
            $dtypeMap = array_column($memsList, 'pack_id', 'dtype');
            if (!in_array($dType, array_keys($dtypeMap))) {
                echo "没有该商户类型对应的角色包，请检查参数！\n\r";
                throw new \Exception("没有该商户类型对应的角色包，请检查参数！");
            }

            //获取角色包id
            $packId = $dtypeMap[$dType] ?? 0;
            if (!$packId) {
                echo "获取商户类型角色包id失败， 商户类型：{$dType}";
                throw new \Exception("获取商户类型角色包id失败， 商户类型：{$dType}");
            }

            //分批处理
            $chunkArr  = array_chunk($memberIds, 100);
            foreach ($chunkArr as $chunk) {
                //验证账号
                $userRes = (new \Business\JavaApi\Member\MemberQuery())->batchMemberInfoByIds($chunk);
                if ($userRes['code'] == 200 && !empty($userRes['data'])) {
                    $userMap = array_column($userRes['data'], null, 'id');
                }
                foreach ($chunk as $memberId) {
                    if (!isset($userMap[$memberId])){
                        echo "会员id：{$memberId}，会员不存在！\n\r";
                        pft_log("tmp/group_account_auth", "会员id：{$memberId}，会员不存在！");
                        continue;
                    }
                    if ((isset($userMap[$memberId]['dtype']) && $userMap[$memberId]['dtype'] != $dType)) {
                        echo "会员id：{$memberId}，会员类型错误！\n\r";
                        pft_log("tmp/group_account_auth", "会员id：{$memberId}，会员类型错误！");
                        continue;
                    }
                    //如果是命中商户类型，绑定角色包权限
                    $res = (new UserRolePackBiz())->merchantUserBindPackage($memberId, $memberId, 1, [$packId]);
                    if ($res['code'] != 200) {
                        $errorLog = ['参数：', $memberId, $memberId, [$packId], '结果：', $res];
                        echo "绑定商户类型角色包失败，原因：" . json_encode($errorLog, JSON_UNESCAPED_UNICODE) . " \n\r";
                        pft_log("tmp/group_account_auth", "绑定商户类型角色包失败，原因：" . json_encode($errorLog, JSON_UNESCAPED_UNICODE));
                    }
                }
            }
        } catch (\Exception $e) {
            $log  = json_encode([
                'request' => func_get_args(),
                'msg'     => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ], JSON_UNESCAPED_UNICODE);

            //异常错误记录
            pft_log("tmp/group_account_auth", $log);
        }

        echo "处理完成！\n\r";
        exit();
    }

    /**
     * 旧版角色数据清除
     * <AUTHOR>
     * @date   2025/06/18
     *
     * /usr/bin/php /var/www/html/Service/Crontab/runNew.php Basic/Tmp oldAuthDiscardScript
     */
    public function oldAuthDiscardScript()
    {
        //初始化
        $page           = 0;
        $size           = 100;
        $delSize        = 10;
        $allowDType     = [0, 1, 6, 2, 16, 17, 18, 7];
        $allowChannelId = 1;
        $adminFid       = 1;
        $staffDType     = 6;
        $totalList      = ['role' => 0, 'role_member' => 0, 'role_menu' => 0, 'role_element' => 0,];
        $logPath        = 'tmp/old_auth_discard/debug';
        $errLogPath     = 'tmp/old_auth_discard/error';
        $delLogPath     = 'tmp/old_auth_records';
        $staffNum = 0;

        //命令参数
        $params   = $GLOBALS['argv'];
        $fid      = $params[3] ?? 0;
        $testMode = ($params[4] ?? 1) == 1;
        $markText = $testMode ? '[测试模式-不执行实际操作] ' : '';

        echo "{$markText}开始处理角色数据...\n";
        pft_log($logPath, "{$markText}开始处理角色数据...");

        /**
         * 第一步循环获取角色ids和用户ids
         *          |
         * 第二步验证用户类型，符合新版用户类型
         *          |
         * 第三步获取删除的id
         *          |
         * 第四步删除数据
         *          |
         * 第五步打印/记入结果
         */

        try {
            $roleModel              = new \Model\Authority\Role();
            $roleElementModel       = new \Model\Authority\RoleElement();
            $roleMenuModel          = new \Model\Authority\RoleMenu();
            // $memberQueryApi         = new \Business\JavaApi\Member\MemberQuery();
            // $memberRelationQueryApi = new \Business\JavaApi\Member\MemberRelationQuery();

            $lastId = 0;

            // 开始循环
            while (true) {
                //初始化分页
                $page++;
                $condition = $fid ? ['fid' => $fid] : [];
                $pageNum   = $page;
                if ($lastId) {
                    $pageNum         = 1;
                    $condition['id'] = ['lt', $lastId];
                }

                //第一步循环获取角色ids和用户ids
                $roleList = $roleModel->getRoleListByPage($condition, 'id,fid,channel_id', 'id desc', $pageNum, $size);
                if (empty($roleList)) {
                    break;
                }
                $lastId = end($roleList)['id'];
                reset($roleList);

                //第二步验证用户类型，符合新版用户类型
                $memberIds    = array_map('intval', array_column($roleList, 'fid'));
                $allowUserIds = [];
                // $staffUserIds = [];
                $userResData = $this->_getMemberList(array_values(array_unique($memberIds)));
                if (!empty($userResData)) {
                    foreach ($userResData as $info) {
                        if (!in_array($info['dtype'], $allowDType) || $info['id'] == $adminFid) {
                            continue;
                        }
                        if ($info['dtype'] == $staffDType) {
                            // $staffUserIds[] = $info['id'];
                            $staffNum = $staffNum + 1;
                            continue;
                        }
                        $allowUserIds[] = $info['id'];
                    }
                }
                // if (!empty($staffUserIds)) {
                //     //员工数据处理
                //     $parentListRes = $memberRelationQueryApi->queryMemberStaffRelationList(['parentIds' =>[] ,'sonIds' => $staffUserIds]);
                //     if ($parentListRes['code'] != 200) {
                //         $err = json_encode([
                //             'msg'  => "员工数据处理失败，原因：" . json_encode($parentListRes, JSON_UNESCAPED_UNICODE),
                //             'data' => $staffUserIds,
                //         ], JSON_UNESCAPED_UNICODE);
                //         echo '[error]' . $err . "\n\r";
                //         pft_log($errLogPath, $markText . $err);
                //     } else {
                //         //获取主账号类型
                //         $parentIds     = [];
                //         $parentListMap = array_column($parentListRes['data'], 'parent_id', 'son_id');
                //         foreach ($staffUserIds as $staffUserId) {
                //             $parentId = $parentListMap[$staffUserId] ?? 0;
                //             if (!$parentId) {
                //                 $err = json_encode([
                //                     'msg' => "员工上级查询不存在， 员工ID：" . $staffUserId,
                //                 ], JSON_UNESCAPED_UNICODE);
                //                 echo '[error]' . $err . "\n\r";
                //                 pft_log($errLogPath, $markText . $err);
                //                 continue;
                //             }
                //             $parentIds[] = $parentId;
                //         }
                //         if (!empty($parentIds)) {
                //             $userResData = $this->_getMemberList(array_values(array_unique($parentIds)));
                //             if (!empty($userResData)) {
                //                 foreach ($userResData as $info) {
                //                     if (!in_array($info['dtype'], $allowDType) || $info['id'] == $adminFid) {
                //                         continue;
                //                     }
                //                     $allowUserIds[] = $info['id'];
                //                 }
                //             }
                //         }
                //     }
                // }
                //不存在允许的用户，跳过
                if (empty($allowUserIds)) {
                    continue;
                }

                //第三步获取删除的id
                $delRoleIds = [];
                $recordLog  = [];
                foreach ($roleList as $roleInfo) {
                    if (!in_array($roleInfo['fid'], $allowUserIds) || $roleInfo['channel_id'] != $allowChannelId) {
                        continue;
                    }
                    $delRoleIds[] = $roleInfo['id'];
                    $recordLog[]  = $roleInfo['fid'] . "-" . $roleInfo['id'];
                }
                //不存在要删除的角色，跳过
                if (empty($delRoleIds)) {
                    continue;
                }

                //每页预删除数据记录下
                echo "{$markText}当前页：{$page}, 删除角色数：" . count($delRoleIds) . "\n";
                pft_log($logPath, "{$markText}当前页：{$page}, 删除角色数：" . count($delRoleIds));
                pft_log($delLogPath,
                    json_encode(['msg' => "{$markText}当前页：{$page}", 'del_data' => $recordLog], JSON_UNESCAPED_UNICODE));
                pft_log($delLogPath, "   ");

                //第四步事务删除 pft_authority_role_element_relation -> pft_authority_role_menu_relation -> pft_authority_role_member_relation -> pft_authority_role
                $delRoleIds = array_values(array_unique($delRoleIds));

                //获取需要删除的总数
                $delRoleElementCount = $roleElementModel->getRoleElementCountByRoleIds($delRoleIds);
                $delRoleMenuCount    = $roleMenuModel->getRoleMenuCountByRoleIds($delRoleIds);
                $delRoleMemberCount  = $roleModel->getRoleMemberCountByRoleIds($delRoleIds);
                if (!$testMode) {
                    try {
                        //删除角色关联元素
                        if ($delRoleElementCount > $delSize) {
                            //数据过大，分批次删
                            $maxTimes = ceil($delRoleElementCount / $delSize);
                            for ($subPage = 1; $subPage <= $maxTimes; $subPage++) {
                                $delRoleElement = $roleElementModel->delRoleElementByRoleIds($delRoleIds, $delSize);
                                if ($delRoleElement === false) {
                                    throw new \Exception('删除角色关联元素失败');
                                }
                                $totalList['role_element'] += intval($delRoleElement);
                                echo "{$markText}当前页：{$page}, 删除角色关联元素数：" . intval($delRoleElement) . "\n";
                            }
                        } else {
                            $delRoleElement = $roleElementModel->delRoleElementByRoleIds($delRoleIds);
                            if ($delRoleElement === false) {
                                throw new \Exception('删除角色关联元素失败');
                            }
                            $totalList['role_element'] += intval($delRoleElement);
                        }

                        //删除角色关联菜单
                        if ($delRoleMenuCount > $delSize) {
                            //数据过大，分批次删
                            $maxTimes = ceil($delRoleMenuCount / $delSize);
                            for ($subPage = 1; $subPage <= $maxTimes; $subPage++) {
                                $delRoleMenu = $roleMenuModel->delRoleMenuByRoleIds($delRoleIds, $delSize);
                                if ($delRoleMenu === false) {
                                    throw new \Exception('删除角色关联菜单失败');
                                }
                                $totalList['role_menu'] += intval($delRoleMenu);
                                echo "{$markText}当前页：{$page}, 删除角色关联菜单数：" . intval($delRoleMenu) . "\n";
                            }
                        } else {
                            $delRoleMenu = $roleMenuModel->delRoleMenuByRoleIds($delRoleIds);
                            if ($delRoleMenu === false) {
                                throw new \Exception('删除角色关联菜单失败');
                            }
                            $totalList['role_menu'] += intval($delRoleMenu);
                        }

                        //删除角色关联用户
                        if ($delRoleMemberCount > $delSize) {
                            //数据过大，分批次删
                            $maxTimes = ceil($delRoleMemberCount / $delSize);
                            for ($subPage = 1; $subPage <= $maxTimes; $subPage++) {
                                $delRoleMember = $roleModel->delRoleMemberByRoleIds($delRoleIds, $delSize);
                                if ($delRoleMember === false) {
                                    throw new \Exception('删除角色关联用户失败');
                                }
                                $totalList['role_member'] += intval($delRoleMember);
                                echo "{$markText}当前页：{$page}, 删除角色关联用户数：" . intval($delRoleMember) . "\n";
                            }
                        } else {
                            $delRoleMember = $roleModel->delRoleMemberByRoleIds($delRoleIds);
                            if ($delRoleMember === false) {
                                throw new \Exception('删除角色关联用户失败');
                            }
                            $totalList['role_member'] += intval($delRoleMember);
                        }

                        //最后删除角色
                        $delRole = $roleModel->delRoleByIds($delRoleIds);
                        if ($delRole === false) {
                            throw new \Exception('删除角色失败');
                        }
                        $totalList['role'] += intval($delRole);

                    } catch (\Exception $e) {
                        $err = json_encode([
                            'msg'  => $e->getMessage(),
                            'file' => $e->getFile(),
                            'line' => $e->getLine(),
                        ], JSON_UNESCAPED_UNICODE);
                        echo '[error]' . $err . "\n\r";
                        pft_log($errLogPath, $markText . $err);
                    }
                } else {
                    $totalList['role_element'] += intval($delRoleElementCount);
                    $totalList['role_menu']    += intval($delRoleMenuCount);
                    $totalList['role_member']  += intval($delRoleMemberCount);
                    $totalList['role']         += count($delRoleIds);
                }

                usleep(100000); // 休眠 0.1 秒
            }

        } catch (\Throwable $e) {
            $err = json_encode([
                'msg'  => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ], JSON_UNESCAPED_UNICODE);
            echo '[error]' . $err . "\n\r";
            pft_log($errLogPath, $markText . $err);
        }

        //第五步打印/记录结果
        echo "结论：-------------------------------------------------------------------------\n\r";
        $text_1 = "{$markText}删除-pft_authority_role：" . ($totalList['role'] ?? 0) . "条";
        echo "{$text_1} \n\r";
        pft_log($logPath, $text_1);

        $text_2 = "{$markText}删除-pft_authority_role_member_relation：" . ($totalList['role_member'] ?? 0) . "条";
        echo "{$text_2} \n\r";
        pft_log($logPath, $text_2);

        $text_3 = "{$markText}删除-pft_authority_role_menu_relation：" . ($totalList['role_menu'] ?? 0) . "条";
        echo "{$text_3} \n\r";
        pft_log($logPath, $text_3);

        $text_4 = "{$markText}删除-pft_authority_role_element_relation：" . ($totalList['role_element'] ?? 0) . "条";
        echo "{$text_4} \n\r";
        pft_log($logPath, $text_4);
        echo "------------------------------------------------------------------------------\n\r";
        $text_5 = "{$markText}未删除-pft_authority_role员工角色数据：" . ($staffNum ?? 0) . "条";
        echo "{$text_5} \n\r";
        pft_log($logPath, $text_5);
        echo "------------------------------------------------------------------------------\n\r";
        echo "{$markText}处理完成！\n\r";
        pft_log($logPath, "{$markText}处理完成！");
        pft_log($logPath, "   ");
        echo "\n\r";
        echo "删除数据记录日志：/alidata/log/site/cli/{$delLogPath}/ \n\r";
        echo "删除过程日志：/alidata/log/site/cli/{$logPath}/ \n\r";
        echo "删除错误日志：/alidata/log/site/cli/{$errLogPath}/ \n\r";
        echo "\n\r";
        exit();
    }

    private function _getMemberList(array $memberIds)
    {
        static $cache = [];
        static $cacheOrder = []; // 用于记录缓存键的插入顺序

        // 最大缓存条目数
        $maxCacheSize = 1000;
        $validUsers = [];

        foreach ($memberIds as $id) {
            if (isset($cache[$id])) {
                // 如果已缓存，跳过请求
                $validUsers[$id] = $cache[$id];
            }
        }

        $toFetch = array_diff($memberIds, array_keys($validUsers));
        if (!empty($toFetch)) {
            $res = (new \Business\JavaApi\Member\MemberQuery())->batchMemberInfoByIds($toFetch);
            if ($res['code'] == 200 && !empty($res['data'])) {
                foreach ($res['data'] as $item) {
                    $id = $item['id'];
                    $dType = $item['dtype'];

                    // 如果缓存已满，移除最早加入的缓存
                    if (count($cache) >= $maxCacheSize) {
                        $oldestId = array_shift($cacheOrder); // 移除队列头部
                        unset($cache[$oldestId]);             // 删除对应缓存
                    }

                    // 更新缓存与访问顺序
                    $cache[$id] = [
                        'id' => $id,
                        'dtype' => $dType,
                    ];
                    $cacheOrder[] = $id; // 添加到队列尾部
                }
            }
        }

        return array_values(array_intersect_key($cache, array_flip($memberIds)));
    }
}