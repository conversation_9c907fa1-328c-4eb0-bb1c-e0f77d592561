<?php

namespace CrontabTasks\Basic;
use Library\Controller;

/**
 * qconf初始化配置
 * 
 * @date    2019-11-01
 * <AUTHOR>
 */

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}


class QconfInit extends Controller {


    public function mysqlInit() 
    {
        $zookeeper = new \Zookeeper('127.0.0.1:2181');
        $aclArray  = array(
            array(
                'perms'  => \Zookeeper::PERM_ALL,
                'scheme' => 'world',
                'id'     => 'anyone',
            )
        );

        $pathArr = ['/php', '/mysql'];

        $node = '';
        foreach ($pathArr as $val) {
            $node .= $val;
            if (!$zookeeper->exists($node)) {
                $zookeeper->create($node, '', $aclArray, 0);
            }
        }

        $dbconfig = C('DB');
        foreach ($dbconfig as $name => $item) {
            if ($zookeeper->exists($node)) {
                $res = $zookeeper->create('/php/mysql/' . $name, json_encode($item), $aclArray, 0);
                var_dump($res);
            }
        }
    }
}
