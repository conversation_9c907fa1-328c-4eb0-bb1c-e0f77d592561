<?php
/**
 * 购买未生效套餐(将来生效) 生效时可能dtype的变化 需要在生效当天将dtype改变下
 * <AUTHOR>
 * @date   2017-6-23
 */
namespace CrontabTasks\Basic;

use Library\Controller;
use Model\AppCenter\ModuleConfig;
use Library\Business\TerminalCache;

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

class AppCenterDtype extends Controller {

    private $_errorLog   = 'appcenter\dtype\error';
    private $_successLog = 'appcenter\dtype\success';

    public function index() {
        $params = $GLOBALS['argv'];
        $date   = isset($params[3]) && strtotime($params[3]) ? date('Ymd', strtotime($params[3])) : date('Ymd');

        $this->runTask($date);
    }

    public function runTask($date)
    {
        $configModel = new ModuleConfig();
        $memberBiz   = new \Business\Member\Member();
        $page        = 1;
        $needUpdate  = [];

        while(true) {

            $data = $configModel->getDtypeTaskByPage($date, $page);
            if ($data === false) {
                $errorMsg = "时间:{$date},page:{$page}";
                pft_log($this->_errorLog, $errorMsg);
            }

            if (empty($data)) {
                break;
            }

            foreach ($data as $item) {
                $res  = $memberBiz->updateMemberStatus($item['fid'], 0, $item['dtype']);
                $res2 = $memberBiz->updateFeeCodeSetZero($item['fid']);
                $res3 = $memberBiz->updateContractModel($item['fid'], 1);

                TerminalCache::DeleteMemberInfo($item['fid']);
                if ($res && $res2 && $res3) {
                    pft_log($this->_successLog, '队列更新'.$item['fid'].'账户类型'.$item['dtype']);
                    $needUpdate[] = $item['id'];
                } else {
                    pft_log($this->_errorLog, var_export($res,true).var_export($res2,true).var_export($res3,true).'记录'.$item['id'].'更新失败');
                }
            }

            $page++;
        }

        foreach ($needUpdate as $item) {
            $configModel->updateDtypeTaskStatus($item);
        }
    }
}