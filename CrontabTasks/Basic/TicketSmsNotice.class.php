<?php
/**
 * 票类短信通知
 * Author: yangwx
 * Date: 2019/9/27 0027
 */
namespace CrontabTasks\Basic;

//权限判断
if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

use Business\JavaApi\TicketApi;
use Library\Controller;
use Library\Resque\Queue;

class TicketSmsNotice extends Controller
{

    /**
     * 票类到期前7天短信通知
     * Author: yangwx
     * Date: 2019/9/27 0027
     */
    public function ticketSmsExpire()
    {
        pft_log('ticket_sms_notice', 'start');
        $num = 500; //每次请求500张门票
        $ticketIdListNew = [];
        $smsNoticeList = [];

        $ticketApi = new TicketApi();
        $expireRes = $ticketApi->getExpireBeforeTicket();
        if ($expireRes['code'] != 200) {
            $logData = [
                'key' => 'expire_before_notifiy_fail',
                'javaRes' => $expireRes,
                'time' => date('Y-m-d H:i:s')
            ];
            pft_log('ticket_sms_notice', json_encode($logData, JSON_UNESCAPED_UNICODE));
            exit;
        }

        if (!isset($expireRes['data'])) {
            pft_log('ticket_sms_notice', 'endEmpty');
            exit;
        }

         $logData = [
             'key' => 'expire_before_notifiy_success',
             'javaRes' => $expireRes,
             'time' => date('Y-m-d H:i:s')
         ];
         pft_log('ticket_sms_notice', json_encode($logData, JSON_UNESCAPED_UNICODE));

        $ticketIdList = $expireRes['data'];
        $count = count($ticketIdList);
        $page = ceil($count / $num);
        $indexPage = 0;

        //数据分页
        for ($i = 0; $i <= $page; $i++) {
            $arr = array_slice($ticketIdList, $indexPage, $num);
            if ($arr) {
                $ticketIdListNew[$i] = $arr;
            }
            $indexPage = $indexPage + $num;
        }

        //批量获取需要发送短信的相关信息
        foreach ($ticketIdListNew as $value) {
            $ticketIds = implode(',', $value);
            $queryRes = $ticketApi->queryTicketNoticeInfo($ticketIds);
            if ($queryRes['code'] == 200) {
                $smsNoticeList = array_merge($smsNoticeList, $queryRes['data']);
            } else {
                $logData = [
                    'key' => 'query_ticket_notice_info_fail',
                    'ticketIds' => $ticketIds,
                    'res' => $queryRes
                ];
                pft_log('ticket_sms_notice', json_encode($logData, JSON_UNESCAPED_UNICODE));
            }
        }

         $logData = [
             'key' => 'query_ticket_notice_info_success',
             'javaRes' => $smsNoticeList
         ];
         pft_log('ticket_sms_notice', json_encode($logData, JSON_UNESCAPED_UNICODE));

        //短信走队列发送
        foreach ($smsNoticeList as $item) {
            $smsContent = "您的产品{$item['pName']}（产品ID{$item['pId']}) 有票类即将于7天后到期，请及时延期。";
            $params = [
                'mobile'    => $item['phoneNum'],
                'action'    => 'customMsg',
                'params'    => [$item['memberId'], '票付通', $smsContent, $item['phoneNum'],'','',true,'票类到期']
            ];
            $job_id = Queue::push('notify', 'SmsNotify_Job',  $params);
            $jsonParams = json_encode($params, JSON_UNESCAPED_UNICODE);
            pft_log('queue/ticketSmsExpire', 'jobId:' . $job_id . ';params=' . $jsonParams, "month");
        }

        pft_log('ticket_sms_notice', 'end');
    }
}