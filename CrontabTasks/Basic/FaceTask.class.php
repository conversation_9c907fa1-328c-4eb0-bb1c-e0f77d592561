<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/9/26 0026
 * Time: 14:10
 */

namespace CrontabTasks\Basic;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

use Business\Order\Face;
use Library\Controller;
use Library\Resque\Queue;
use Model\Order\OrderTools;
use Model\Terminal\FaceSubTerminal;

class FaceTask extends Controller
{
    /**
     * 删除分终端人脸
     *
     * @author: linchen
     * @date:   2020-12-01
     */
    public function deleteBranchFace(){
        $faceBranchMdl = new FaceSubTerminal();
        $beginNum = 0;
        while (1){
            $faceBranchList = $faceBranchMdl->getFaceBranchUnHandleOrder($beginNum,200);
            if ($faceBranchList){
                $lastData = end($faceBranchList);
                $beginNum = $lastData['id'];
                $this->_handleData($faceBranchList);
            }else{
                echo '结束了';
                exit;
            }
        }

    }
    /**
     * 删除分终端人脸处理业务
     *
     * @author: linchen
     * @date:   2020-12-01
     */
    private function _handleData($faceList){
        //取出所有的订单号
        $orderList = array_unique(array_column($faceList,'ordernum'));
        $orderToolMdl = new OrderTools();
        $orderListInfo = $orderToolMdl->getOrderInfo($orderList,'ss.ordernum,ss.begintime,ss.endtime,ss.playtime,ss.status');
        $orderListInfo = array_column($orderListInfo,null,'ordernum');
        $updateId = [];
        foreach ($faceList as $key =>  $value){
            $orderInfo = $orderListInfo[$value['ordernum']];
            if (!in_array($orderInfo['status'],[0,7])){
                //删除人脸
                if ($value['face_id'] && $value['group_id']){
                    Face::setFaceIdToLock($value['group_id'], $value['face_id'], 600);
                    Queue::push( 'backend', 'Backend_Job', [
                            'action' => 'delBaiduFaceOrder',
                            'groupid'=> $value['group_id'],
                            'faceid' => $value['face_id'],
                        ]
                    );
                    $updateId[] = $value['id'];
                }
            }
        }
        if (!empty($updateId)){
            $faceBranchMdl = new FaceSubTerminal();
            $res = $faceBranchMdl->updateFaceSubTerminalOrderByIds($updateId,1);
            $logData = [
                'res' => $res,
                'data' => $updateId,
            ];
            pft_log('face/branchfacedelete',json_encode($logData));
        }
        return true;
    }
}
