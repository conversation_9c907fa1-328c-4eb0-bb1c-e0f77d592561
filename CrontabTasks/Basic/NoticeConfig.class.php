<?php
/**
 * 通知配置相关的定时任务
 * <AUTHOR>
 * @date 2021/3/30
 */
namespace CrontabTasks\Basic;

use Business\Notice\WxConfig;
use Library\Controller;

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

class NoticeConfig extends Controller
{

    /**
     * 微信用户信息同步
     * <AUTHOR>
     * @date 2020/7/16
     *
     * @return boolean
     */
    public function syncWxUserInfoData()
    {
        $wxConfigBiz = new WxConfig();
        $wxConfigBiz->syncWxUserInfoData();
        return true;
    }

    public function syncWxConfigData1()
    {
        $wxConfigBiz = new WxConfig();
        $wxConfigBiz->syncWxConfigData1();
        return true;
    }

    public function syncWxConfigData2()
    {
        $wxConfigBiz = new WxConfig();
        $wxConfigBiz->syncWxConfigData2();
        return true;
    }

    public function syncWxInfoTest() :void
    {
        $wxConfigBiz = new WxConfig();
        $wxConfigBiz->syncWxInfoTest();
    }

    public function syncProducConfigTest() :void
    {
        $wxConfigBiz = new WxConfig();
        $wxConfigBiz->syncProducConfigTest();
    }


}