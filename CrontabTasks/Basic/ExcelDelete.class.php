<?php
/**
 * 删除已经生成的文件
 * 任务列表从myuu.excel_task表获取
 * 每天执行一次
 *
 * <AUTHOR>
 * @since   2016-11-03
 */

namespace CrontabTasks\Basic;

use       Library\Controller;
use       Model\MassData;

//权限判断
if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

class ExcelDelete extends Controller {
    private $_model;
    private $_errPath = "/excel/deletelog";

    public function __construct() {
        set_time_limit(0);
        $this->_model = new MassData\ExcelTask();
    }

    public function run() {
        $data = $this->_model->getDeleteTask();
        if (empty($data) || !is_array($data)) {
            pft_log($this->_errPath, "没有数据");
            exit("没有数据");
        }

        $idArr = array_column($data, 'id');

        foreach ($idArr as $item) {
            $excelDir = EXCEL_DIR . '/' . $item;
            $zipDir   = DOWNLOAD_DIR . '/' . $item . '.zip';

            if (is_dir($excelDir)) {
                exec("rm -rf $excelDir");
            }

            if (is_file($zipDir)) {
                exec("rm -rf $zipDir");
            }
        }

        exit("完成");
    }
}