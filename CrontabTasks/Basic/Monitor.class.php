<?php
/**
 * 平台监控任务
 * <AUTHOR>
 * @date 2020-11-13
 */

namespace CrontabTasks\Basic;

use Library\Constants\DingTalkRobots;
use \Library\Resque\Queue;
use \Library\Tools\Helpers;

class Monitor
{
    //告警阈值
    private $_sizeThreshold = 234;

    /**
     * 平台队列监控
     * <AUTHOR>
     * @date 2020/11/13
     *
     * @return array
     */
    public function platformCommonQueue()
    {
        $queueSizeList = Queue::queueSize('setting');

        foreach ($queueSizeList as $queueName => $queueSize) {
            if ($queueSize >= $this->_sizeThreshold) {
                //队列的长度超过了
                $message = json_encode([
                    'queue_name' => $queueName,
                    'size'       => $queueSize,
                    'threshold'  => $this->_sizeThreshold,
                ], JSON_UNESCAPED_UNICODE);

                try {
                    //飞书告警
                    Helpers::sendGroupRobotTextMessages("平台队列【{$queueName}】拥堵严重，目前有超过 {$queueSize} 未处理！", 'queue_monitor');
                } catch (\Throwable $e) {}

                //微信通知
                Helpers::sendWechatWarningMessage("平台队列【{$queueName}】拥堵严重，目前有超过 {$queueSize} 未处理！", [], "告警通知", "请增加进程或增加服务器");

                //钉钉告警
                Helpers::sendDingTalkGroupRobotMessage($message, '平台队列消费延迟', '', DingTalkRobots::QUEUE_MONITOR_ROBOT);

                pft_log('queue_monitor', $message);
            }
        }
    }

    /**
     * 平台实时队列监控
     * <AUTHOR>
     * @date 2020/11/13
     *
     * @return array
     */
    public function platformRealtimeQueue()
    {
        $queueSizeList = Queue::queueSize('realtime_setting');

        foreach ($queueSizeList as $queueName => $queueSize) {
            if ($queueSize >= $this->_sizeThreshold) {
                //队列的长度超过了
                $message = json_encode([
                    'queue_name' => $queueName,
                    'size'       => $queueSize,
                    'threshold'  => $this->_sizeThreshold,
                ], JSON_UNESCAPED_UNICODE);

                try {
                    //飞书告警
                    Helpers::sendGroupRobotTextMessages("平台实时队列【{$queueName}】拥堵严重，目前有超过 {$queueSize} 未处理！", 'queue_monitor');
                } catch (\Throwable $e) {}

                //微信通知
                Helpers::sendWechatWarningMessage("平台实时队列【{$queueName}】拥堵严重，目前有超过 {$queueSize} 未处理！", [], "告警通知",
                    "请增加进程或增加服务器");

                //钉钉告警
                Helpers::sendDingTalkGroupRobotMessage($message, '平台实时队列消费延迟', '', DingTalkRobots::QUEUE_MONITOR_ROBOT);

                pft_log('queue_monitor', $message);
            }
        }
    }

    /**
     * 失败消息重新入队列
     * <AUTHOR>
     * @date 2020/11/13
     *
     * @return array
     */
    public function failQueue()
    {
        for ($i = 0; $i <= 500; $i++) {

            $failData = Queue::failedPop('setting');
            if (!$failData) {
                exit('已经没有数据了');
            }

            $str  = str_replace('\\', '', $failData);
            $data = json_decode($str, true);

            //将处理类，参数，队列取出来
            $payload = $data['payload'];
            $queue   = $data['queue'];
            $class   = $payload['class'];
            $args    = $payload['args'][0];

            if ($class) {
                $jobId = Queue::push($queue, $class, $args);

                $res = json_encode([
                    'queue' => $queue,
                    'class' => $class,
                    'args'  => $args,
                    'jobId' => $jobId,
                ]);
                pft_log('queueFail', $res);
            }
        }

    }
}