<?php
/**
 * 每天定时清除一些临时的数据库数据
 * 比如用于记录中间实时报表生成pft_report_real_task，索引表order_refer，ES数据同步表es_sync_status
 * 这些表，只要相应的数据已经生成了，这些中间表的数据就已经可以删除了
 *
 * <AUTHOR>
 * @date 2018-10-22
 *
 */
namespace CrontabTasks\Basic;

use Library\Cache\Cache;
use Library\Constants\RedisKey\AnnualKeyConst;
use Library\Controller;
use Library\MulityProcessHelper;
use Model\Order\RefundAuditModel;
use Model\Product\AnnualCard;
use Model\TradeRecord\OnlineRefund;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class ClearTmpData extends Controller
{
    //需要处理的任务
    private $_taskList = [
        [
            'name'      => '实时报表中间表', //任务名称
            'task'      => 'realReport', //方法名称
            //'max_nums'  => 2000000, //此次最多需要处理的数据，防止一次需要处理太多的数据，导致进程一致卡住
            'max_nums'  => 4500000, //调整到450w
            'each_nums' => 2000, //一次循环需要删除的数据量
        ],
        [
            'name'      => '订单索引表中间表', //任务名称
            'task'      => 'orderRefer', //方法名称
            'max_nums'  => 1500000, //此次最多需要处理的数据，防止一次需要处理太多的数据，导致进程一致卡住
            'each_nums' => 2000, //一次循环需要删除的数据量
        ],
        [
            'name'      => 'ES数据同步中间表', //任务名称
            'task'      => 'esSync', //方法名称
            'max_nums'  => 1500000, //此次最多需要处理的数据，防止一次需要处理太多的数据，导致进程一致卡住
            'each_nums' => 2000, //一次循环需要删除的数据量
        ],
        [
            'name'      => '九天早期对接系统轮询订单变更表', //任务名称
            'task'      => 'machineOrder', //方法名称
            'max_nums'  => 500000, //此次最多需要处理的数据，防止一次需要处理太多的数据，导致进程一致卡住
            'each_nums' => 2000, //一次循环需要删除的数据量
        ],
        [
            'name'      => '在线支付退款记录表', //任务名称
            'task'      => 'onlineRefund', //方法名称
            'max_nums'  => 500000, //此次最多需要处理的数据，防止一次需要处理太多的数据，导致进程一致卡住
            'each_nums' => 2000, //一次循环需要删除的数据量
        ],
        [
            'name'      => '退票审核', //任务名称
            'task'      => 'refundAudit', //方法名称
            'max_nums'  => 200000, //此次最多需要处理的数据，防止一次需要处理太多的数据，导致进程一致卡住
            'each_nums' => 2000, //一次循环需要删除的数据量
        ],
    ];

    private $_logPath = 'remove_tmp_data';

    /**
     * 开始跑脚本
     *
     * <AUTHOR>
     * @date   2018-06-19
     */
    public function runTask()
    {
        $code = 200;
        $msg  = '';
        try {
            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);

            $childCount = count($this->_taskList);
            $task       = new MulityProcessHelper($this, $childCount, 1);

            // 如果前面连接了数据库、redis等，最好在这之前关闭掉
            $task->run($this->_taskList);
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $logData = json_encode([
            'code' => $code,
            'msg'  => $msg], JSON_UNESCAPED_UNICODE);
        pft_log($this->_logPath, $logData);
    }

    /**
     * 子进程脚本
     */
    public function runWorker($taskInfoTmp)
    {
        $taskInfoTmp = array_values($taskInfoTmp);
        $taskInfo    = $taskInfoTmp[0];

        $taskName = $taskInfo['task'];
        $realFunc = $taskName . 'Task';

        $res = call_user_func_array([$this, $realFunc], [$taskInfo]);
    }

    /**
     * 实时报表中间表数据删除
     * <AUTHOR>
     * @data   2018-10-22
     *
     * @param  array  $taskInfo
     * @return bool
     */
    private function realReportTask($taskInfo)
    {
        $maxNums  = $taskInfo['max_nums'];
        $eachNums = $taskInfo['each_nums'];

        $logData = json_encode([
            'key' => '实时报表中间表数据删除 - 开始',
        ], JSON_UNESCAPED_UNICODE);
        pft_log($this->_logPath, $logData);

        $realReportModel = new \Model\Report\PftReportRealTask();

        $totalPage = ceil($maxNums / $eachNums);
        for ($i = 0; $i < $totalPage; $i++) {

            $expireTaskArr = $realReportModel->getExpireTaskIdArr($eachNums, $orderby = 'asc');

            //结束任务
            if (!$expireTaskArr) {
                return true;
            }

            //将数据进行删除
            $res = $realReportModel->delExpireTask($expireTaskArr);

            //将最后一个ID写入日志
            $delNum    = count($expireTaskArr);
            $lastDelId = array_pop($expireTaskArr);

            //将删除结果写日志
            $logData = json_encode([
                'key'     => '实时报表中间表数据删除',
                'last_id' => $lastDelId,
                'del_num' => $delNum,
                'res'     => $res,
            ], JSON_UNESCAPED_UNICODE);
            pft_log($this->_logPath, $logData);

            //休眠50ms
            usleep(50000);
        }

        return true;
    }


    /**
     * ES数据同步中间表数据删除
     * <AUTHOR>
     * @data   2018-10-22
     *
     * @param  array  $taskInfo
     * @return bool
     */
    private function esSyncTask($taskInfo)
    {
        $maxNums  = $taskInfo['max_nums'];
        $eachNums = $taskInfo['each_nums'];

        $logData = json_encode([
            'key' => 'ES数据同步中间表数据删除 - 开始',
        ], JSON_UNESCAPED_UNICODE);
        pft_log($this->_logPath, $logData);

        $esSyncModel = new \Model\Tools\EsSyncTask();

        $totalPage = ceil($maxNums / $eachNums);
        for ($i = 0; $i < $totalPage; $i++) {

            $expireTaskArr = $esSyncModel->getExpireEsTaskIdArr($eachNums, $orderby = 'asc');

            //结束任务
            if (!$expireTaskArr) {
                return true;
            }

            //将数据进行删除
            $res = $esSyncModel->delExpireEsTask($expireTaskArr);

            //将最后一个ID写入日志
            $delNum    = count($expireTaskArr);
            $lastDelId = array_pop($expireTaskArr);

            //将删除结果写日志
            $logData = json_encode([
                'key'     => 'ES数据同步中间表数据删除',
                'last_id' => $lastDelId,
                'del_num' => $delNum,
                'res'     => $res,
            ], JSON_UNESCAPED_UNICODE);
            pft_log($this->_logPath, $logData);

            //休眠50ms
            usleep(50000);
        }

        return true;
    }

    /**
     * 九天早期对接系统轮询订单变更表数据删除
     * <AUTHOR>
     * @data   2018-10-22
     *
     * @param  array  $taskInfo
     * @return bool
     */
    private function machineOrderTask($taskInfo)
    {
        $maxNums  = $taskInfo['max_nums'];
        $eachNums = $taskInfo['each_nums'];

        $logData = json_encode([
            'key' => '九天早期对接系统轮询订单变更表数据删除 - 开始',
        ], JSON_UNESCAPED_UNICODE);
        pft_log($this->_logPath, $logData);

        $orderMachineModel = new \Model\Ota\MachineSys();

        $totalPage = ceil($maxNums / $eachNums);
        for ($i = 0; $i < $totalPage; $i++) {

            $expireTaskArr = $orderMachineModel->getExpireTaskIdArr($eachNums, $orderby = 'asc');

            //结束任务
            if (!$expireTaskArr) {
                return true;
            }

            //将数据进行删除
            $res = $orderMachineModel->delExpireTask($expireTaskArr);

            //将最后一个ID写入日志
            $delNum    = count($expireTaskArr);
            $lastDelId = array_pop($expireTaskArr);

            //将删除结果写日志
            $logData = json_encode([
                'key'     => '九天早期对接系统轮询订单变更表数据删除',
                'last_id' => $lastDelId,
                'del_num' => $delNum,
                'res'     => $res,
            ], JSON_UNESCAPED_UNICODE);
            pft_log($this->_logPath, $logData);

            //休眠50ms
            usleep(50000);
        }

        return true;
    }

    private function onlineRefundTask($taskInfo)
    {
        $maxNums  = $taskInfo['max_nums'];
        $eachNums = $taskInfo['each_nums'];

        $logData = json_encode([
            'key' => '在线支付退款数据删除 - 开始',
        ], JSON_UNESCAPED_UNICODE);
        pft_log($this->_logPath, $logData);


        $onlineTradeModel = new OnlineRefund('localhost');
        $totalPage = ceil($maxNums / $eachNums);
        for ($i = 0; $i < $totalPage; $i++) {
            $expireTaskArr = $onlineTradeModel->getExpireOnlineRefundOrderIdArray($eachNums, $orderby = 'asc');
            //结束任务
            if (!$expireTaskArr) {
                break;
            }

            //将数据进行删除
            $res = $onlineTradeModel->delExpireTask($expireTaskArr, 'pft_order_refund');

            //将最后一个ID写入日志
            $delNum    = count($expireTaskArr);
            $lastDelId = array_pop($expireTaskArr);

            //将删除结果写日志
            $logData = json_encode([
                'key'     => '在线支付退款数据删除',
                'last_id' => $lastDelId,
                'del_num' => $delNum,
                'res'     => $res,
            ], JSON_UNESCAPED_UNICODE);
            pft_log($this->_logPath, $logData);
            //休眠50ms
            usleep(50000);
        }
        return true;
    }

    /**
     * 退票审核历史数据删除--uu_order_terminal_change
     *
     * @author: guanpeng
     * @date: 2019/5/22
     * @param $taskInfo
     * @return bool
     */
    public function refundAuditTask($taskInfo)
    {
        $maxNums  = $taskInfo['max_nums'];
        $eachNums = $taskInfo['each_nums'];

        $logData = json_encode([
            'key' => $taskInfo['name'].' - 开始',
        ], JSON_UNESCAPED_UNICODE);
        pft_log($this->_logPath, $logData);
        $onlineTradeModel = new RefundAuditModel('localhost_wsdl');
        $totalPage        = ceil($maxNums / $eachNums);
        $archiveModel     = new RefundAuditModel('archive');

        for ($i = 0; $i < $totalPage; $i++) {
            $expireTaskArr = $onlineTradeModel->getExpireDataIdArray($eachNums, $orderby = 'asc');
            //结束任务
            if (!$expireTaskArr) {
                break;
            }
            // 转储数据到归档的数据库实例上
            $transferData = $onlineTradeModel->getExpireDataDetailList($expireTaskArr);
            $archiveModel->transerExpireData($transferData);
            //将数据进行删除
            $res = $onlineTradeModel->delExpireTask($expireTaskArr);
            //将最后一个ID写入日志
            $delNum    = count($expireTaskArr);
            $lastDelId = array_pop($expireTaskArr);
            //将删除结果写日志
            $logData = json_encode([
                'key'     => $taskInfo['name'],
                'last_id' => $lastDelId,
                'del_num' => $delNum,
                'res'     => $res,
            ], JSON_UNESCAPED_UNICODE);
            pft_log($this->_logPath, $logData);
            //休眠50ms
            usleep(50000);
        }
        return true;
    }

    /**
     * 年卡虚拟卡集合锁定定时清除
     *
     * @author: linchen
     * @date: 2020/09/16
     * @return bool
     */
    public function annualVirtualLockClean()
    {
        $redis = Cache::getInstance('redis');
        $num   = $redis->scard(AnnualKeyConst::ANNUAL_VIRTUAL_NO_LOCK);
        pft_log('annual/virtualLock/num', '锁定数量' . $num);
        $annualVirtualData = $redis->sMembers(AnnualKeyConst::ANNUAL_VIRTUAL_NO_LOCK);
        if (empty($annualVirtualData)) {
            echo '无需处理';
            return false;
        }
        $annualCardMdl = new AnnualCard();
        $res           = $annualCardMdl->getStoreStatusForVirtual($annualVirtualData);
        $noSremData    = array_column($res, 'virtual_no');
        $remNum        = 0;
        foreach ($annualVirtualData as $key => $value) {
            if (!in_array($value, $noSremData)) {
                $redis->srem(AnnualKeyConst::ANNUAL_VIRTUAL_NO_LOCK, $value);
                $remNum++;
            }
        }
        pft_log('annual/virtualLock/virtual', json_encode($redis->sMembers(AnnualKeyConst::ANNUAL_VIRTUAL_NO_LOCK)));
        pft_log('annual/virtualLock/num', '清除数量' . $remNum);
        echo '清除数量' . $remNum;
        return true;
    }
}
