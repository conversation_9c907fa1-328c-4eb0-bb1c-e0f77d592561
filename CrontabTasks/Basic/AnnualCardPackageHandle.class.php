<?php

namespace CrontabTasks\Basic;

/**
 * 年卡套餐刷数据脚本
 * <AUTHOR>  Li
 * @date 2022-11-01
 */
class AnnualCardPackageHandle
{

    private $_businessModel    = null;
    private $_cardModel        = null;
    private $_javaApi          = null;
    private $_packageManageBiz = null;
    private $_packageBiz       = null;
    private $_logPath          = 'debug/annualPackageMigrate';

    public function __construct($logPath)
    {
        $this->_logPath = $logPath;
        pft_log($this->_logPath, 'card_package:start');
    }

    /**
     * 开始跑脚本
     *
     * <AUTHOR>  Li
     * @date 2022-11-01
     */
    public function runTask($idArrList, $where)
    {
        if (!$idArrList || !$where) {
            return ['code' => 403, 'msg' => '参数缺失'];
        }

        $code = 200;
        $msg  = '';

        try {
            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
            foreach ($idArrList as $i => $item) {
                echo '   进程中： 第'. ($i + 1) . '页处理完: 当前处理数量：' . count($item);
                $where['id'] = ['in', $item];
                $this->_unsetAllModel();
                $task = new \Library\MulityProcessHelper($this, 20, 500);
                $task->run($where); // 如果前面连接了数据库、redis等，最好在这之前关闭掉
            }


        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        pft_log($this->_logPath, "card_package:end:{$msg}");
    }


    /**
     * 子进程脚本l
     */
    public function runWorker($where)
    {
        $code = 200;
        $msg  = '';

        try {
            $this->runReserveWorker($where);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $str = 'card_package:';
        //$str .= $idList[0] . ' - ' . $idList[1] . ':';
        $str .= $code == 200 ? ':执行成功' : ':执行失败,原因:' . $msg;

        pft_log($this->_logPath, $str);
    }

    private function runReserveWorker($where)
    {
        if (!$where) {
            throw new \Exception('缺少参数', 403);
        }

        $limit = 500;
        //获取出总数
        $total = $this->_getBusinessModel()->table('pft_annual_card')->where($where)->count();
        //计算页数
        $totalPage = ceil($total / $limit);
        $field     = '*';

        for ($i = 1; $i <= $totalPage; $i++) {
            //查询出年卡表中 状态为非仓库中且pid不为0的数据
            $cardInfo = $this->_getBusinessModel()->table('pft_annual_card')->where($where)->page($i, $limit)->order('id asc')->field($field)->select();
            //获取年卡对应的订单号
            $virtualNoArr = array_column($cardInfo, 'virtual_no');
            $cardIdArr    = array_column($cardInfo, 'id');

            $cardOrderInfo = $this->_getBusinessModel()->table('pft_annual_card_mapping')->where(['virtual_no' => ['in', $virtualNoArr]])->group('virtual_no')->order('id asc')->select();
            $cardOrderMap  = array_column($cardOrderInfo, 'ordernum', 'virtual_no');
            $pidArr        = array_values(array_unique(array_column($cardInfo, 'pid')));

            $ticket = $this->_getJavaApi()->queryTicketInfoByProductIds($pidArr);
            //查询出当前票属性信息
            $ticketMap = [];
            $extInfo   = [];
            foreach ($ticket as $item) {
                $ticketMap[$item['ticket']['pid']] = [
                    'tid' => $item['ticket']['id'],
                    'lid' => $item['land']['id'],
                    'pid' => $item['product']['id'],
                    'sid' => $item['ticket']['apply_did'],
                ];
                $extInfo[$item['ticket']['pid']] = [
                    'available_time_period' => $item['ext']['available_time_period'] ?? '',
                    'annual_valid_type'     => $item['ext']['annual_valid_type'] ?? 0,
                    'annual_valid_day'      => $item['ext']['annual_valid_day'] ?? 30,
                    'annual_valid_start'    => $item['ext']['annual_valid_start'] ?? '',
                    'annual_valid_end'      => $item['ext']['annual_valid_end'] ?? '',
                ];
            }

            $sql  = "UPDATE pft_annual_card SET ext_info = CASE id ";
            $sql2 = "UPDATE pft_annual_card_order SET package_id = CASE virtual_no ";

            $sqlTrue  = false;
            $sql2True = false;

            $virtualStr = '';
            foreach ($cardInfo as $item) {
                //判断下是否存在时段套餐。 存在先跳过
                $cardPackage = $this->_getPackageBiz()->getPeriodPackageByCardAndSid($item['id'], $item['sid']);
                if ($cardPackage['code'] == 200 && !empty($cardPackage['data'])) {
                    continue;
                }

                if (!isset($extInfo[$item['pid']]) || !isset( $ticketMap[$item['pid']])) {
                    continue;
                }

                $sql     .= sprintf("WHEN %d THEN %s ", $item['id'], "'" . json_encode($extInfo[$item['pid']]) . "'");
                $sqlTrue = true;

                //年卡套餐后续处理
                //判断年卡状态为激活 、挂失、禁用的需要 写入年卡时段套餐信息、年卡套餐变更记录、年卡特权快照信息
                //年卡状态为未激活的的需要 写入年卡时段套餐信息、年卡特权快照信息
                $isActive   = in_array($item['status'], [0, 2]) && empty($item['avalid_begin']) ? false : true;
                $state      = in_array($item['status'], [0, 2]) && empty($item['avalid_begin']) ? 1 : 2;

                $startTime = $item['avalid_begin'];
                $endTime   = $item['avalid_end'];
                //年卡未激活或者已禁用 且票属性设置为固定有效期 套餐有效期需要调整
                if (in_array($item['status'], [0, 2]) && ($item['avalid_begin'] == 0 || $item['avalid_end']) && $extInfo[$item['pid']]['annual_valid_type'] == 2) {
                    $startTime = strtotime($extInfo[$item['pid']]['annual_valid_start']);
                    $endTime   = strtotime($extInfo[$item['pid']]['annual_valid_end']);
                }

                $handlerRes = $this->_getPackageManageBiz()->packageAfterHandlerForOrder($item['id'], $extInfo[$item['pid']]['annual_valid_type'], $isActive, $ticketMap[$item['pid']]['lid'], $item['pid'],
                    $item['sid'], $ticketMap[$item['pid']]['tid'], $item['virtual_no'], $cardOrderMap[$item['virtual_no']] ?? '', 1, $item['sid'], $startTime, $endTime, '', $extInfo[$item['pid']], $state);
                if ($handlerRes['code'] != 200) {
                    pft_log($this->_logPath . '/error', json_encode([
                        'ac'         => 'packageAfterHandlerForOrder',
                        'msg'        => '年卡套餐写入失败',
                        'handlerRes' => $handlerRes,
                        'params'     => [
                            'id'                => $item['id'],
                            'annual_valid_type' => $extInfo[$item['pid']]['annual_valid_type'],
                            'isActive'          => $isActive,
                            'lid'               => $ticketMap[$item['pid']]['lid'],
                            'pid'               => $ticketMap[$item['pid']]['pid'],
                            'sid'               => $item['sid'],
                            'tid'               => $ticketMap[$item['pid']]['tid'],
                            'virtual_no'        => $item['virtual_no'],
                            'ordernum'          => $cardOrderMap[$item['virtual_no']] ?? '',
                            'packageType'       => 1,
                            'opid'              => $item['sid'],
                            'avalid_begin'      => $item['avalid_begin'],
                            'avalid_end'        => $item['avalid_end'],
                            'extInfo'           => $extInfo[$item['pid']],
                            'state'             => $state,
                        ],
                    ], JSON_UNESCAPED_UNICODE));
                    continue;
                }

                //时段套餐写入成功之后，需要将对应的套餐id 通过虚拟卡号更新至对应年卡特权消费记录上
                $sql2 .= sprintf("WHEN %s THEN %s ", "'" . $item['virtual_no'] ."'", $handlerRes['data']);
                $sql2True = true;
                $virtualStr .= "'" . $item['virtual_no'] ."',";
            }

            $sql    .= "END WHERE id IN (" . implode(',', $cardIdArr) . ")";
            if ($sqlTrue) {
                $upRes = $this->_getBusinessModel()->execute($sql);
                if ($upRes === false) {
                    pft_log($this->_logPath . '/error', json_encode(['ac' => 'updateExtInfoByVirtualNo', 'msg' => '年卡扩展属性更新异常', 'sql' => $sql], JSON_UNESCAPED_UNICODE));
                }
            }

            if ($sql2True) {
                $sql2    .= "END WHERE virtual_no IN (" . rtrim($virtualStr,',') . ")";
                $upRes = $this->_getBusinessModel()->execute($sql2);
                if ($upRes === false) {
                    pft_log($this->_logPath . '/error', json_encode(['ac' => 'handleCardOrder', 'msg' => '年卡特权消费套餐id更新异常', 'sql' => $sql2], JSON_UNESCAPED_UNICODE));
                }
            }

            $logStr = 'runWork:';
            $logStr .= '最后更新成功的卡ID： ' . array_pop($cardIdArr);

            pft_log($this->_logPath, $logStr);
        }
    }

    protected function _unsetAllModel()
    {
        unset($this->_businessModel);
        unset($this->_javaApi);
        unset($this->_packageBiz);
        unset($this->_cardModel);
    }

    protected function _getBusinessModel()
    {
        if (empty($this->_businessModel)) {
            $this->_businessModel = new \Library\Model('pft_business_case');
        }

        return $this->_businessModel;
    }

    protected function _getJavaApi()
    {
        if (empty($this->_javaApi)) {
            $this->_javaApi = new \Business\CommodityCenter\Ticket();
        }

        return $this->_javaApi;
    }

    protected function _getPackageManageBiz()
    {
        if (empty($this->_packageManageBiz)) {
            $this->_packageManageBiz = new \Business\AnnualCard\PackageManage();
        }

        return $this->_packageManageBiz;
    }

    protected function _getPackageBiz()
    {
        if (empty($this->_packageBiz)) {
            $this->_packageBiz = new \Business\AnnualCard\Package();
        }

        return $this->_packageBiz;
    }

    protected function _getCardModel()
    {
        if (empty($this->_cardModel)) {
            $this->_cardModel = new \Model\Product\AnnualCard();
        }

        return $this->_cardModel;
    }
}