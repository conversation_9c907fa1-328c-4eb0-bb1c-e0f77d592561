<?php
/**
 * Created by PhpStorm.
 * User: chenguangpeng
 * Date: 12/15-015
 * Time: 18:34
 * 清新福建数据推送
 */

namespace CrontabTasks\Basic;

use Library\Business\Xcrypt;
use Library\Model;
use Library\Constants\Table\MainTableConst;

class FreshFujian
{
    const URL = 'http://piao.jingdian.com/service/';
    const OBJ = 'SCEN';

    private $model;
    private $conf;
    private $lid_list = [];

    public function __construct()
    {
        $this->model = new Model('slave');
        $this->conf  = include CONF_DIR . '/fresh_fujian.conf.php';
        foreach ($this->conf as $conf) {
            foreach ($conf['lid'] as $lid) {
                $this->lid_list[] = $lid;
            }
        }
    }

    /**
     * 心跳推送
     */
    public function HeartBeat()
    {
        $dcModel    = new \Model\DataCollection\DataCollection();
        $modelSlave = new Model('slave');

        $landApi    = new \Business\CommodityCenter\Land();

        $landIdArr = $this->lid_list;
        $saleridMap = [];
        if ($landIdArr) {
            $landInfo = $landApi->queryLandMultiQueryById($landIdArr);
            if ($landInfo) {
                foreach ($landInfo as $land) {
                    $saleridMap[$land['id']] = $land['salerid'];
                }
            }

        }

        $tm = strtotime('-15 minutes');
        foreach ($this->conf as $conf) {
            $salerid = $saleridMap[$conf['lid']] ?? 0;
            if (!$salerid) {
                continue;
            }

            $salerArr = [$salerid];
            $data     = $dcModel->getHeartBeatByScenicArr($salerArr, $page = 1, $size = 2000);
            echo $dcModel->_sql(), "\n";

            if (!$data) {
                continue;
            }

            $items = [];
            foreach ($data as $item) {
                $items[] = [
                    "gate_sn" => 'Terminal-' . $item['terminal_id'],
                    "status"  => $item['created_at'] >= $tm ? 1 : 99,
                    "image"   => "http://images.pft12301.cc/machine_tmp.jpg",
                ];
            }
            $data = [
                'op'   => 'gates',
                'data' => $items,
            ];
            $res  = $this->post($data, $conf['org_code'], $conf['org_secret']);
            var_dump($res);
        }
    }

    /**
     * 闸机入园  此方法只查10分钟 不兼容订单分库 <AUTHOR>
     *
     */
    public function CheckedIn()
    {
        $tm1 = date('Y-m-d H:i:s', strtotime('-30 minutes'));
        $tm2 = date('Y-m-d H:i:s');
        foreach ($this->conf as $conf) {
            $cnt = count($conf);
            if ($cnt == 1) {
                $where = ['lid' => $conf['lid']];
            } elseif ($cnt > 1) {
                $where = ['lid' => ['in', $conf['lid']]];
            } else {
                return false;
            }
            $where['dtime']  = ['between', [$tm1, $tm2]];
            $where['status'] = 1;
            $data            = $this->model->table(MainTableConst::TABLE_SS_ORDER)->field('dtime, lid,tid,ordername,ordertel,tnum')
                                           ->where($where)
                                           ->select();

            pft_log('/api/cncn/req', __METHOD__ . ":" . $this->model->_sql() . ';data:' . json_encode($data));
            $items = [];
            if (!$data) {
                continue;
            }
            foreach ($data as $row) {
                //云票务、安卓终端替换手机号
                $row['ordertel'] = $row['ordertel'] == '' || strlen($row['ordertel'] != 11) ? '18650296275' : $row['ordertel'];
                $items[]         = [
                    'in_time'       => $row['dtime'],
                    'cate_sn'       => 'pft-' . $row['tid'],
                    'identity_card' => '',
                    'phone'         => substr_replace($row['ordertel'], 111, 5, 3),
                    'username'      => $row['ordername'],
                    'channel'       => '99',
                    'count'         => $row['tnum'],
                ];
            }
            $data = [
                'op'   => 'guest',
                'data' => $items,
            ];
            $this->post($data, $conf['org_code'], $conf['org_secret']);
        }

    }

    /**
     * 新增或更新产品
     *
     */
    public function ProductInfo()
    {
        $landApi = new \Business\CommodityCenter\Land();
        foreach ($this->conf as $conf) {
            $cnt = count($conf);
            //if ($cnt == 1) {
            //    $where = ['id' => $conf['lid']];
            //} elseif ($cnt > 1) {
            //    $where = ['id' => ['in', $conf['lid']]];
            //} else {
            //    return false;
            //}

            if ($cnt == 1) {
                $landIdArr = [$conf['lid']];
            } elseif ($cnt > 1) {
                $landIdArr = $conf['lid'];
            } else {
                return false;
            }

            $landArr = $landApi->queryLandMultiQueryById($landIdArr);
            if (empty($landArr)) {
                return false;
            }

            foreach ($landArr as $item) {
                $item['tel'] = empty($item['tel']) ? '0591-8888888' : $item['tel'];
                $signData    = [
                    'op'              => 'scen_info', //操作对象 当前为保存景区信息
                    'name'            => $item['title'],//景区全称
                    'capability'      => 50000,//景区可容纳人数
                    'city_id'         => $conf['areaid'],//地区
                    'city_name'       => $conf['areaname'],//地区
                    'address'         => $item['address'],//详细地址
                    'description'     => '景区介绍', //景区介绍
                    'images'          => $item['imgpath'],//景区图片
                    'telephone'       => $item['tel'], //景区联系电话
                    'contact'         => '',//景区联系人
                    'at_time'         => '08:00',//早上开园时间
                    'pm_time'         => '17:30',//下午闭园时间
                    //特殊时间段备注
                    'opentime_remark' => $item['runtime'],
                ];
                $this->post($signData, $conf['org_code'], $conf['org_secret']);
            }
        }
    }

    /**
     * 更新门票信息
     *
     */
    public function TicketInfo()
    {
        foreach ($this->conf as $conf) {
            $cnt = count($conf);
            if ($cnt == 1) {
                $landIdArr = [$conf['lid']];
            } elseif ($cnt > 1) {
                $landIdArr = $conf['lid'];
            } else {
                return false;
            }
            $ticketApi = new \Business\CommodityCenter\Ticket();
            $ticketArr = $ticketApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable('id,title,tprice,notes',
                'apply_limit', '', '', '', null, $landIdArr);
            $info      = [];
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticketInfo) {
                    $info[$ticketInfo['ticket']['id']]['id']          = $ticketInfo['ticket']['id'];
                    $info[$ticketInfo['ticket']['id']]['title']       = $ticketInfo['ticket']['title'];
                    $info[$ticketInfo['ticket']['id']]['tprice']      = $ticketInfo['ticket']['tprice'];
                    $info[$ticketInfo['ticket']['id']]['notes']       = $ticketInfo['ticket']['notes'];
                    $info[$ticketInfo['ticket']['id']]['apply_limit'] = $ticketInfo['product']['apply_limit'];
                }
            }

            foreach ($info as $item) {
                $data = [
                    'op'      => 'ticket_cate_modify',
                    'cate_sn' => "pft-{$item['id']}",
                    'name'    => $item['title'],
                    'price'   => $item['tprice'],
                    'remark'  => '备注:' . $item['notes'],
                    'status'  => $item['apply_limit'] == 1 ? 1 : 99,
                ];
                $this->post($data, $conf['org_code'], $conf['org_secret']);
            }
        }
    }

    private function post($data, $org_code, $secret)
    {
        $sign        = $this->encryptData($data, $secret);
        $tmptime     = time();
        $params      = [
            //签名
            'sign'    => $sign,
            //组织机构代码证号
            'orgcode' => $org_code,
            //token
            'token'   => md5($org_code . $tmptime . $secret),
            //接口版本号
            'ver'     => '1.0',
            //平台接口固定值
            'obj'     => 'SCEN',
            //当前时间
            'time'    => $tmptime,
        ];
        $post_params = http_build_query($params);
        $res         = curl_post(self::URL, $post_params, 80, 10, '/api/cncn');
        $decode      = json_decode($res);
        if ($decode->code != 1) {
            $res = json_encode($decode, JSON_UNESCAPED_UNICODE);
            pft_log('/api/cncn/fail',
                "[组织信息:{$org_code}:{$secret}]result:$res,post_raw:" . json_encode($data, JSON_UNESCAPED_UNICODE));
        } else {
            pft_log('/api/cncn/success', "result:$res");
        }

        return $res;
    }

    /**
     * 加密数据
     *
     * @param  array  $data  需要加密的数据
     * @param  string  $secret  密钥
     *
     * @return string
     */
    private function encryptData(Array $data, $secret)
    {
        $obj = new Xcrypt(md5($secret), 'cbc', 'auto', MCRYPT_RIJNDAEL_128);
        $res = $obj->encrypt(serialize($data), 'upgrade');

        return $res;
    }

}