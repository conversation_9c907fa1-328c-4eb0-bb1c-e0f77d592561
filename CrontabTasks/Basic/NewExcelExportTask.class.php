<?php
/**
 * 报表导出的脚本
 * 任务列表从myuu.excel_task表获取
 * <AUTHOR>
 * @date 2022-02-09
 */

namespace CrontabTasks\Basic;

use Library\Controller;
use Model\MassData\ExcelTask;
use Library\Cache\Cache;
use Library\Tools\Helpers;
use Library\Constants\DingTalkRobots;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

//定义权限
defined("IS_EXCEL_EXPORT") or define("IS_EXCEL_EXPORT", 1);

class NewExcelExportTask extends Controller
{
    private $_excelTaskModel;
    private $_errPath   = "/excel_new/error";
    private $_sucPath   = "/excel_new/success";
    private $_nonPath   = "/excel_new/none";
    private $_debugPath = "/excel_new/debug";

    private $_vipUIds;

    //是否开启日志
    const IS_DEBUG_LOG = true;

    //最大可执行 30个任务
    const CHUNK_SIZE = 30;
    //有数据，但是连续多次未获取到数据，预警
    const ERROR_MAX_TIMES = 10;
    //每个用户每次可以执行上限
    const USER_MAX_TIMES = 3;

    //单个用户可以执行次数
    const USER_EXCEL_TASK_REDIS_KEY = 'excel_export_task:user:';
    //正在运行总数
    const EXCEL_RUN_TOTAL_REDIS_KEY = 'excel_export_task:run_total';
    //异常数据标记
    const EXCEL_RUN_ERROR_REDIS_KEY = 'excel_export_task:run_error:times';
    //锁键名
    const REDIS_LOCK_KEY = 'excel_export_task_get_lock_redis';
    //锁键名
    const TASK_ID_REDIS_LOCK_KEY = 'excel_export_task_get_id_lock_redis';

    //任务集合键名队列
    const TASK_SHARDING_LIST_KEY = "excel_export_sharding_list";
    //集合键名前缀
    const EXCEL_EXPORT_TASK_ID_SET = 'excel_export_id:';

    //最大分片数
    const TASK_MAX_SHARDING_SIZE = 30;
    //最小分片数
    const TASK_MIN_SHARDING_SIZE = 6;
    //分片大小
    const TASK_SHARDING_CHUNK_SIZE = 2;

    //当前执行的集合键名
    private $vipShardingKey = null;


    //获取最大任务数据
    const MAX_GET_TASK_NUM = 6;

    //测试环境用户id
    const TEST_VIP_UID_LIST = [6970];

    public function __construct()
    {
        set_time_limit(0);

        //获取vip用户
        $this->getVipMemberIds();
    }

    /**
     * 脚本入口
     * <AUTHOR>
     * @date 2022/2/9
     *
     */
    public function index()
    {
        try {
            $this->_debugLog('开始脚本');

            //判断12小时生成失败
            $this->_handleOvertimeTask();

            sleep(1);

            $taskList = $this->_getTaskList();
            $this->_debugLog(['获取任务数：', count($taskList)]);
            if (empty($taskList)) {
                exit();
            }

            $this->runWorker($taskList);

        } catch (\Exception $e) {
            $exception = $e->getMessage();
            pft_log($this->_errPath, $exception);
        }
    }

    /**
     * vip通道入口
     * <AUTHOR>
     * @date   2023/12/8
     *
     */
    public function runVipChannel()
    {
        try {
            $this->_debugLog('开始脚本');

            $taskIdList = $this->getTaskIdFromRedis();
            if (empty($taskIdList)) {
                pft_log($this->_nonPath, "未发现任务");
                exit();
            }

            $taskList = $this->_getTaskModel(true)->getTaskByIds($taskIdList);

            $this->runWorker($taskList);

        } catch (\Exception $e) {
            $exception = $e->getMessage();
            pft_log($this->_errPath, $exception);
        } finally {
            //只要结束，就移除批次任务
            $this->_removeCacheTask();
        }
    }

    /**
     * 获取任务Id
     * <AUTHOR>
     * @date   2023/12/8
     *
     * @return mixed
     * @throws
     */
    public function getTaskIdFromRedis()
    {
        $redis = Cache::getInstance('redis');
        // 获取本次要处理的数据
        $shardingKey = $redis->rPop(self::TASK_SHARDING_LIST_KEY);
        // 检查队列里剩余的数据还有多少，太少的话重新生成一批放进去
        $shardingLen = $redis->lLen(self::TASK_SHARDING_LIST_KEY);

        if ($shardingLen < self::TASK_MIN_SHARDING_SIZE) {
            $this->pushDataToRedis(self::TASK_SHARDING_CHUNK_SIZE);
        }

        // 如果第一次取失败了，重新取一次，还没有的话，退出
        if (!$shardingKey) {
            $shardingKey = $redis->rPop(self::TASK_SHARDING_LIST_KEY);
        }

        if (!$shardingKey) {
            throw new \Exception('列表数据为空', 201);
        }

        //标记下当前执行的任务key
        $this->vipShardingKey = $shardingKey;

        return $redis->sMembers($shardingKey);
    }

    /**
     * 下载任务推入到缓存
     * <AUTHOR>
     * @date   2023/12/8
     *
     * @param int $chunkSize
     *
     * @return bool
     */
    public function pushDataToRedis(int $chunkSize)
    {
        $lockResult = $this->_getIdLock();
        if (!$lockResult) {
            return false;
        }

        list($inFids, $notInFids) = $this->getCondition(true);
        $count  = $chunkSize * self::TASK_MAX_SHARDING_SIZE;
        $idList = $this->getTaskIdList($count, $inFids, $notInFids);
        if (empty($idList)) {
            //移除查询锁
            $this->_getIdRmLock();

            return true;
        }

        $model = $this->_getTaskModel(true);

        $model->startTrans();

        //将获取到的任务状态改为 正在生成中
        $updateRes = $model->updateRuning($idList);
        if (!$updateRes) {
            //移除查询锁
            $this->_getIdRmLock();

            return false;
        }

        $redisMain = Cache::getInstance('redis');

        // 数组分组
        $shardingData = array_chunk($idList, $chunkSize);

        $key = md5(uniqid(md5(microtime(true)), true));
        //开启redis事务
        $redisMain->multi(\Redis::PIPELINE);

        // 切片分组的key，存到redis的list类型里面，
        foreach ($shardingData as $chunkId => $chunkData) {
            $shardingKey = self::EXCEL_EXPORT_TASK_ID_SET . "$key:" . $chunkId;
            $redisMain->lpush(self::TASK_SHARDING_LIST_KEY, $shardingKey);
            $redisMain->sAddArray($shardingKey, $chunkData);
        }

        $redisMain->exec();

        $model->commit();

        //移除查询锁
        $this->_getIdRmLock();

        return true;
    }

    /**
     * 获取任务id
     * <AUTHOR>
     * @date   2023/12/8
     *
     * @param  int    $count
     * @param  array  $inFids
     * @param  array  $notInFids
     *
     * @return array
     */
    public function getTaskIdList(int $count = 0, array $inFids = [], array $notInFids = [])
    {
        //获取全部任务
        $taskList = $this->_getTaskModel(true)->getTaskIdList($count, $inFids, $notInFids);
        if (empty($taskList)) {
            return [];
        }

        return array_column($taskList, 'id');
    }

    /**
     * 任务执行
     * <AUTHOR>
     * @date 2022/2/9
     *
     * @param  array  $taskList  任务列表
     */
    public function runWorker(array $taskList)
    {
        $excelType = load_config('excelType', 'excel');
        foreach ($taskList as $task) {
            if (empty($task['excel_type'])) {
                pft_log($this->_nonPath, "{$task['id']}|未指定报表类型");
                continue;
            }

            if (!isset($excelType[$task['excel_type']]['class']) || !isset($excelType[$task['excel_type']]['action'])) {
                pft_log($this->_errPath, "{$task['id']}|报表类型未知, {$task['excel_type']}");
                continue;
            }

            $class  = $excelType[$task['excel_type']]['class'];
            $action = $excelType[$task['excel_type']]['action'];

            if (!class_exists($class) || !method_exists($class, $action)) {
                pft_log($this->_errPath, "{$task['id']}|class或action有误, {$class}_{$action}");
                continue;
            }

            //开始导出
            $isRunMark = false;//为true的时候，不用特殊处理，为false的时候需要手动更新下失败
            try {
                $run = new $class();
                $run->$action($task);

                //记录结果
                $this->_handleFinishTask(200, $task);
                $isRunMark = true;

            } catch (\Exception $e) {

                //记录结果
                $this->_handleFinishTask(400, $task);

                pft_log($this->_errPath, "{$task['id']}|" . $e->getMessage());

                $isRunMark = true;

                continue;
            } finally {
                //兜底策略，代码异常中断，没有捕获到异常时，为false更新为失败
                if (!$isRunMark) $this->_handleFinishTask(400, $task);
            }

            pft_log($this->_sucPath, "{$task['id']}|任务生成");
            //释放
            unset($run);
        }
    }

    /**
     * 获取任务列表
     * <AUTHOR>
     * @date 2022/2/17
     *
     * @return array
     */
    private function _getTaskList(bool $isVip = false)
    {
        list($inFids, $notInFids) = $this->getCondition($isVip);

        //获取任务id列表
        $taskIds = $this->_getTaskIds($inFids, $notInFids);
        if (empty($taskIds)) {
            pft_log($this->_nonPath, "未发现任务");

            return [];
        }

        //获取任务详情
        $taskList = $this->_getTaskModel()->getTaskListByIds($taskIds);
        if (empty($taskList)) {
            pft_log($this->_errPath, json_encode($taskIds) . ", 任务获取失败");

            return [];
        }

        return $taskList;
    }

    /**
     * 获取任务id
     * <AUTHOR>
     * @date 2022/2/9
     *
     * @return array|bool
     */
    private function _getTaskIds(array $inFids = [], array $notInFids = [])
    {
        $lockResult = $this->_getIdLock(self::REDIS_LOCK_KEY);
        if (!$lockResult) {
            pft_log($this->_errPath, "获取失败有锁");

            return false;
        }

        $redis = Cache::getInstance('redis');

        //获取全部任务
        $taskList = $this->_getTaskModel(true)->getOnetimeTaskList(1, 0, $inFids, $notInFids);
        if (empty($taskList)) {
            $redis->rm(self::EXCEL_RUN_ERROR_REDIS_KEY);
            //去除锁
            $this->_getIdRmLock(self::REDIS_LOCK_KEY);

            return [];
        }

        //取出任务id
        $listData = [];
        $runTotal = intval($redis->get(self::EXCEL_RUN_TOTAL_REDIS_KEY));
        $supTimes = self::CHUNK_SIZE - $runTotal; //可用次数 = 可执行上限 - 正在执行的总数
        $this->_debugLog("剩余可用次数：$supTimes");

        foreach ($taskList as $item) {
            //不限账号
            if ($this->_noLimitExportAccountCheck($item['fid'])) {
                $listData[] = $item['id'];
                continue;
            }
            if (count($listData) < $supTimes) {
                $userRedisKey = self::USER_EXCEL_TASK_REDIS_KEY . $item['fid'] . ':' . $item['member_id'];
                $userTimes    = intval($redis->get($userRedisKey));
                //限制每个用户只能占用3个
                if (!$userTimes || self::USER_MAX_TIMES > $userTimes) {
                    $this->_debugLog("用户当前使用：$userTimes, 用户：" . $item['fid'] . ', 操作人：' . $item['member_id']);
                    $listData[] = $item['id'];
                    $this->_addUserCache($item['fid'], $item['member_id']);
                }
                //设置与最大可以用数一致
                if (self::MAX_GET_TASK_NUM == count($listData)) {
                    break;
                }
            } else {
                break;
            }
        }

        //预警
        $this->_earlyWarning(empty($listData));

        //去除锁
        $this->_getIdRmLock(self::REDIS_LOCK_KEY);

        return $listData;
    }

    /**
     * 任务超时更新处理
     * <AUTHOR>
     * @date 2022/2/10
     *
     * @return bool
     */
    private function _handleOvertimeTask()
    {
        //获取超过12小时还在执行中的
        $endTime  = time() - 12 * 3600;
        $taskList = $this->_getTaskModel(true)->getOnetimeTaskList(2, $endTime);
        if (empty($taskList)) {
            return true;
        }

        $taskIds = array_column($taskList, 'id');

        //更新为失败
        $res = $this->_updateErrorStatus($taskIds);
        if (!$res) {
            pft_log($this->_errPath, json_encode(['超时更新失败', $taskIds]));
        }

        foreach ($taskList as $tmp) {
            //释放用户占用
            $this->_rmUserCache($tmp['fid'], $tmp['member_id']);
        }

        return true;
    }

    /**
     * 任务完成处理
     * <AUTHOR>
     * @date 2022/2/21
     *
     * @param  int  $code  结果code
     * @param  array  $task  任务详情
     *
     * @return bool
     */
    private function _handleFinishTask(int $code, array $task)
    {
        if (empty($task)) {
            pft_log($this->_errPath, "结果没有任务详情");

            return true;
        }
        $this->_debugLog(['处理结果', $code, $task]);
        $id  = $task['id'] ?? 0;
        $fid = $task['fid'] ?? 0;
        $memberId = $task['member_id'] ?? 0;
        switch ($code) {
            case 200:
                //脚本异常未执行成功，再次查询下状态
                $tmp = $this->_getTaskModel(true)->getTaskById(intval($id));
                if ($tmp === false || empty($tmp) || !isset($tmp['done']) || $tmp['done'] != 3) {
                    //更新为失败
                    $this->_debugLog(['未执行成功', $tmp, $task, intval($id)]);
                    //更新失败状态
                    $res = $this->_updateErrorStatus([$id]);
                    //释放用户占用
                    $res && $this->_rmUserCache($fid, $memberId);

                    break;
                }
                $this->_debugLog($fid . '-' . $id . '执行完成');
                //释放用户占用
                $this->_rmUserCache($fid, $memberId);
                break;
            case 400:
                //更新失败状态
                $res = $this->_updateErrorStatus([$id]);
                //释放用户占用
                $res && $this->_rmUserCache($fid, $memberId);
                break;

            default:
                break;
        }

        return true;
    }

    /**
     * 失败更新任务状态
     * <AUTHOR>
     * @date 2022/2/9
     *
     * @param  array  $taskId  任务id
     *
     * @return bool
     */
    private function _updateErrorStatus(array $taskId)
    {
        if (empty($taskId)) {
            return false;
        }
        $res = $this->_getTaskModel(true)->updateError($taskId);
        if (!$res) {
            $this->_debugLog(["生成失败，状态更新:", $taskId, $res]);
            pft_log($this->_errPath, implode(',', $taskId) . "|失败状态更新失败");

            return false;
        }

        return $res;
    }

    /**
     * 获取任务表模型
     * <AUTHOR>
     * @date 2022/2/9
     *
     * @param  bool  $set  是否重新获取
     *
     * @return ExcelTask
     */
    private function _getTaskModel(bool $set = false)
    {
        if (empty($this->_excelTaskModel) || $set) {
            $this->_excelTaskModel = new ExcelTask();
        }

        return $this->_excelTaskModel;
    }

    /**
     * 移除用户占用
     * <AUTHOR>
     * @date 2022/2/10
     *
     * @param  int  $fid  用户id
     *
     * @return bool
     */
    private function _rmUserCache($fid, $memberId = 0)
    {
        if ($this->_noLimitExportAccountCheck($fid)) {
            return true;
        }

        $redis = Cache::getInstance('redis');

        //释放用户占用
        $userRedisKey = self::USER_EXCEL_TASK_REDIS_KEY . $fid . ':'. $memberId;
        $userTimes    = intval($redis->get($userRedisKey));
        $userTimes--;
        $redis->set($userRedisKey, ($userTimes >= 0 ? $userTimes : 0), '', 3600 * 24);
        $this->_debugLog("用户解除占用完成$userTimes");

        //释放次数
        $runTimes = intval($redis->get(self::EXCEL_RUN_TOTAL_REDIS_KEY));
        $runTimes--;
        $redis->set(self::EXCEL_RUN_TOTAL_REDIS_KEY, ($runTimes >= 0 ? $runTimes : 0), '', 3600 * 24);
        $this->_debugLog("运行解除占用完成$runTimes");

        return true;
    }

    /**
     * 缓存累加
     * <AUTHOR>
     * @date 2022/2/18
     *
     * @param  int  $fid  用户id
     *
     * @return bool
     */
    private function _addUserCache($fid, $memberId)
    {
        if ($this->_noLimitExportAccountCheck($fid)) {
            return true;
        }

        $redis = Cache::getInstance('redis');

        //用户次数累加
        $userRedisKey = self::USER_EXCEL_TASK_REDIS_KEY . $fid . ':' . $memberId;
        $userTimes    = intval($redis->get($userRedisKey));
        $userTimes++;
        $redis->set($userRedisKey, $userTimes, '', 3600 * 24);

        //总次数累加
        $runTimes = intval($redis->get(self::EXCEL_RUN_TOTAL_REDIS_KEY));
        $runTimes++;
        $redis->set(self::EXCEL_RUN_TOTAL_REDIS_KEY, $runTimes, '', 3600 * 24);

        return true;
    }

    /**
     * 根据每次获取数量进行预警
     * <AUTHOR>
     * @date 2022/2/18
     *
     * @param  bool  $isEmpty  是否为空
     *
     * @return bool
     */
    private function _earlyWarning(bool $isEmpty = false)
    {
        $redis = Cache::getInstance('redis');
        if ($isEmpty) {
            $errorTimes = intval($redis->get(self::EXCEL_RUN_ERROR_REDIS_KEY));
            if ($redis->exists(self::EXCEL_RUN_ERROR_REDIS_KEY)) {
                $redis->incrBy(self::EXCEL_RUN_ERROR_REDIS_KEY, 1);
            } else {
                $redis->set(self::EXCEL_RUN_ERROR_REDIS_KEY, 1, '', 3600 * 24);
                $errorTimes = 1;
            }

            //预警处理
            if ($errorTimes >= self::ERROR_MAX_TIMES && intval($redis->get(self::EXCEL_RUN_TOTAL_REDIS_KEY)) == self::CHUNK_SIZE) {
                $this->_debugLog('已经连续10次，未获取异步下载任务');
                $this->_dingWarnging('已经连续10次，未获取异步下载任务，请注意！！！！', true);
                //预警结束，移除避免下次再次通知
                $redis->rm(self::EXCEL_RUN_ERROR_REDIS_KEY);
            }
        } else {
            $redis->rm(self::EXCEL_RUN_ERROR_REDIS_KEY);
        }

        return true;
    }

    /**
     * 钉钉通知
     * <AUTHOR>
     * @date 2022/2/10
     *
     * @param  string  $message
     * @param  bool  $atAll
     */
    private function _dingWarnging(string $message, bool $atAll = false)
    {
        if (ENV == 'PRODUCTION') {
            Helpers::sendDingTalkGroupRobotMessageRaw($message, DingTalkRobots::REAL_REPORT_ROBOI, $atAll);
        }
    }

    /**
     * 异常时可以通过这个情况缓存
     * <AUTHOR>
     * @date 2022/2/17
     */
    public function resetCache()
    {
        //清除测试数据
        $redis    = Cache::getInstance('redis');
        $userKeys = $redis->keys(self::USER_EXCEL_TASK_REDIS_KEY . '*');
        //移除全部
        $redis->del($userKeys);
        $redis->rm(self::EXCEL_RUN_TOTAL_REDIS_KEY);
        $redis->rm(self::EXCEL_RUN_ERROR_REDIS_KEY);

        echo json_encode(['code' => 200, 'msg' => '缓存清除完成'], JSON_UNESCAPED_UNICODE);
        exit();
    }

    /**
     * 调试日志
     * <AUTHOR>
     * @date 2022/2/17
     *
     * @param  string|array  $message  日志内容
     *
     * @return bool
     */
    private function _debugLog($message)
    {
        if (empty($message) || self::IS_DEBUG_LOG === false) {
            return true;
        }
        if (is_array($message)) {
            $message = json_encode($message, JSON_UNESCAPED_UNICODE);
        }

        pft_log($this->_debugPath, $message);

        return true;
    }

    /**
     * 判断账号是不是可以无限导出
     * <AUTHOR>
     * @date   2023/8/8
     *
     * @param int $fid  商户id
     *
     * @return bool
     */
    private function _noLimitExportAccountCheck($fid)
    {
        if (in_array($fid, $this->_vipUIds)) {
            return true;
        }

        return false;
    }

    /**
     * 获取vip通道用户
     * <AUTHOR>
     * @date   2023/11/28
     *
     * @return true
     */
    public function getVipMemberIds()
    {
        $sidList = self::TEST_VIP_UID_LIST;
        //配置走qconf
        if (in_array(ENV, ['PRODUCTION', 'TEST', 'DEVELOP'])) {
            if (extension_loaded('qconf')) {
                $sidList = \qconf::getConf("/php/platform/excel_export_task_whitelist");
                if (!empty($sidList)) {
                    $sidList = json_decode($sidList, true);
                } else {
                    $sidList = self::TEST_VIP_UID_LIST;
                }
            }
        }

        $this->_vipUIds = array_values($sidList);

        return true;
    }

    /**
     * 获取查询条件
     * <AUTHOR>
     * @date   2023/11/28
     *
     * @param  bool  $isVip
     *
     * @return array[]
     */
    public function getCondition(bool $isVip = false)
    {
        $notInArr = [];
        $inArr    = [];
        if ($isVip) {
            $inArr = $this->_vipUIds;
        } else {
            $notInArr = $this->_vipUIds;
        }

        return [$inArr, $notInArr];
    }

    /**
     * 清除连接
     * <AUTHOR>
     * @date   2023/11/28
     *
     * @return bool
     */
    protected function _unsetAllModel()
    {
        unset($this->_excelTaskModel);

        return true;
    }

    /**
     * 获取任务id加个锁
     * <AUTHOR>
     * @date   2023/12/14
     *
     * @return mixed
     */
    private function _getIdLock(string $key = '')
    {
        if (empty($key)) {
            $key = self::TASK_ID_REDIS_LOCK_KEY;
        }

        //锁实例
        $redis      = Cache::getInstance('redis');
        $lockResult = $redis->lock($key, 1, 60);

        $redis->initMaster();

        return $lockResult;
    }

    /**
     * 获取任务id移除个锁
     * <AUTHOR>
     * @date   2023/12/14
     *
     * @return bool
     */
    private function _getIdRmLock(string $key = '')
    {
        if (empty($key)) {
            $key = self::TASK_ID_REDIS_LOCK_KEY;
        }

        //锁实例
        $redis = Cache::getInstance('redis');

        if ($redis->lock_exist($key)) {
            $redis->unlock($key);
        }

        $redis->initMaster();

        return true;
    }

    /**
     * 移除当前执行的任务
     * <AUTHOR>
     * @date   2023/12/14
     *
     */
    private function _removeCacheTask()
    {
        if (!empty($this->vipShardingKey)) {
            Cache::getInstance('redis')->del($this->vipShardingKey);

            //重置
            $this->vipShardingKey = null;
        }
    }
}