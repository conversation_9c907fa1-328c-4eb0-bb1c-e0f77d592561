<?php

namespace CrontabTasks\Basic;

use Business\JavaApi\ProductApi;
use Library\Controller as Controller;
use Model\Product\Price;
use Model\Product\PriceGroup;

/**
 * 批量推送消息
 *
 * <AUTHOR>
 * @date   2019-08-29
 */
class TaskBatchSaveDisPrice extends Controller
{
    /**
     * 执行转分销页面批量转分销产品
     * <AUTHOR>
     * @date 2019/8/29
     */
    public function implementBatchSavePriceForDisTask()
    {
        $priceMode = new Price();
        $field     = 'id,gid,price,pid,aid,sid,dprice';
        $taskInfo  = $priceMode->getBatchSavePriceForDisTask(0, 50, 0,0, false, 0, $field, '', date('n'));
        if (empty($taskInfo)) {
            return false;
        }
        $gidArr    = array_column($taskInfo, 'gid');

        $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
        $groupFidRes        = $getEvoluteGroupBiz->queryGroupFidsByGroupIds($gidArr);
        $resellerGroup      = [];
        if ($groupFidRes['code'] == 200 && !empty($groupFidRes['data'])) {
            foreach ($groupFidRes['data'] as $item) {
                $resellerGroup[$item['groupId']][] = $item['fid'];
            }
        }

        foreach ($taskInfo as &$value) {
            //赋值分组中的分销商id
            $value['dids'] = $resellerGroup[$value['gid']];
        }

        //新版分销链配置biz
        $upEvoluteBiz = new \Business\Product\Update\EvoluteProduct();
        $groupBiz     = new \Business\Product\Update\EvoluteGroup();

        foreach ($taskInfo as $item) {
            //循环调用配置一级定价（结算价）接口
            if (empty($item['dids'])) {
                $status = 2;
                $reason = "分组人为空，配置价格失败";
                //更新数据库为配置失败
                $result = $priceMode->updatePriceForDisTask($item['id'], $status, $reason);
                if ($result === false) {
                    pft_log('disTask', json_encode($priceMode->getDbError(), JSON_UNESCAPED_UNICODE));
                }
            } else {
                //当分组中有成员的情况下才调用java配置结算价，配置分销链
                //$confList = [];
                ////重组数据
                //$confList[] = [
                //    'priceset' => [$item['price'] => $item['dids']],
                //    'pid'      => $item['pid'],
                //    'aid'      => $item['aid'],     //这里传的是票的上级供应商id
                //];
                //$sid        = $item['sid'];
                //调用java配置结算价
                //$res    = ProductApi::confEvolutes($sid, $confList, 0);

                $configRes    = $upEvoluteBiz->configEvoluteGroupProduct($item['sid'], $item['aid'], $item['pid'], $item['gid'], 0, 0,
                    (int)$item['dprice'], 1, $item['sid']);

                $res       = false;
                if ($configRes['code'] == 200) {
                    $res = true;
                }
                $status = 1;
                $reason = "";
                if ($res === false) {
                    pft_log('disTask', json_encode([
                        'ac' => 'configEvoluteGroupProduct',
                        'params' => $item,
                        'configRes' => $configRes,
                    ], JSON_UNESCAPED_UNICODE));
                    $status = 2;
                    $reason = "结算价配置失败";
                } else {
                    //java结算价配置成功更新分组中的分组定价
                    $originGroupInfo = $groupBiz->queryByGroupId($item['gid']);
                    if ($originGroupInfo['code'] != 200 || empty($originGroupInfo['data']) || $originGroupInfo['data']['sid'] != $item['sid']) {
                        return false;
                    }

                    //$groupMode->setDefaultSettlePrice($item['gid'], $item['pid'], $item['price']);
                }
                //更新数据库为配置失败
                $result = $priceMode->updatePriceForDisTask($item['id'], $status, $reason);
                //更新失败记录原因
                if ($result === false) {
                    pft_log('disTask', json_encode($priceMode->getDbError(), JSON_UNESCAPED_UNICODE));
                }
            }
        }
    }

    /**
     * 删除转分销转分销产品任务 每个月月初执行一次(每个月删除这个月前第四个月的数据)
     *
     * <AUTHOR>
     * @date 2020/1/10
     *
     *
     * @return bool
     */
    public function deleteBatchSavePriceForDisTask()
    {
        echo "Start at" . date('Y-m-d H:i:s', time()) . "\n";
        //获取当前月份
        $deleteMonth = date('n', strtotime('-4 month'));
        if (empty($deleteMonth)) {
            return false;
        }
        $priceMode = new Price();
        $res       = $priceMode->deleteBatchSavePriceForDisTask($deleteMonth);
        if ($res === false) {
            pft_log('disTask', "删除任务失败，月份是：" . $deleteMonth . "错误原因：" . json_encode($priceMode->getDbError(), JSON_UNESCAPED_UNICODE));
        }
        echo "End at" . date('Y-m-d H:i:s', time()) . "\n";
    }

}