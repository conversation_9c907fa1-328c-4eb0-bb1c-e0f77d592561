<?php
/**
 * 年卡增加特权数据——手工处理脚本
 */

namespace CrontabTasks\Basic;

class AnnualPackagePrivilege
{
    private $_logPath = '';
    // /usr/bin/php /var/www/html/Service/Crontab/runNew.php Basic_AnnualPackagePrivilege annualPackagePrivilegeAdd
    /**
     * 年卡套餐特权数据增补
     */
    public function annualPackagePrivilegeAdd()
    {
        $lib = new \Library\Model('pft_business_case');
        $package   = load_config('package', 'tmp_annual');
        $privilege = load_config('privilege', 'tmp_annual');

        //循环需要处理的套餐信息
        foreach ($package as $pid => $packageInfo) {
            $num      = 0;
            $tmpTotal = count($packageInfo);
            echo "产品ID：{$pid}下有{$tmpTotal}张卡\n";
            //每张卡-套餐对应数据整合
            foreach ($packageInfo as $packageId => $cardId) {
                if (isset($privilege[$pid]) && $privilege[$pid]) {
                    $saveData   = [];
                    $createTime = time();
                    //获取到特权门票配置信息
                    foreach ($privilege[$pid] as $groupId => $privilegeInfo) {
                        $saveData[] = [
                            'card_id'         => $cardId,
                            'tid'             => $privilegeInfo['tid'],
                            'aid'             => $privilegeInfo['supplier_aid'],
                            'use_limit_json'  => $privilegeInfo['limit'],
                            'use_recode_json' => json_encode([]),
                            'state'           => 2,
                            'group_id'        => $groupId,
                            'group_name'      => $privilegeInfo['name'],
                            'supplier_aid'    => $privilegeInfo['supplier_aid'],
                            'create_time'     => $createTime,
                            'package_id'      => $packageId,
                        ];
                    }

                    //写入套餐特权表中
                    $addRes = $lib->table('pft_annual_card_package_privilege')->addAll($saveData);
                    if ($addRes === false) {
                        $logData = [
                            'ac'       => 'privilege-add',
                            'msg'      => '特权增补失败',
                            'pid'      => $pid,
                            'saveData' => $saveData,
                        ];
                        pft_log($this->_logPath, json_encode($logData, JSON_UNESCAPED_UNICODE));
                    } else {
                        $num += 1;
                    }
                    echo date('m-d H:i:s')."产品ID：{$pid}-执行了{$num}张卡了 \n";
                }
            }
        }

        exit("脚本执行完了");
    }
}