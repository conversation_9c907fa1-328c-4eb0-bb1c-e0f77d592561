<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/9/26 0026
 * Time: 14:10
 */

namespace CrontabTasks\Basic;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

use Library\Cache\Cache;
use Library\Constants\RedisKey\ModuleKeyConst;
use Library\Controller;

class CountModule extends Controller
{
    private $redisKey = ModuleKeyConst::REDIS_MODULE_COUNT_USED;
    private $redisKeyAll = ModuleKeyConst::REDIS_MODULE_COUNT_ALL;
    /**
     * 计算模块数量(全部的)
     *
     * @author: linchen
     * @date:   2020-02-13
     */
    public function CountModuleAll()
    {
        $redis     = Cache::getInstance('redis');
        $redisData = $redis->get($this->redisKeyAll,'',true);
        if ($redisData){
            $redis->rm($this->redisKeyAll);
        }
        $listModel  = new \Model\AppCenter\ModuleList();
        $ModuleCountArr = $listModel->getAllModuleTotal();
        $redis->set($this->redisKeyAll,$ModuleCountArr,'','',true);
    }
    /**
     * 计算模块数量新(使用中的)
     *
     * @author: linchen
     * @date:   2020-02-13
     */
    public function CountModuleNew(){
        $redis     = Cache::getInstance('redis');
        $redisData = $redis->get($this->redisKey,'',true);
        if ($redisData){
            $redis->rm($this->redisKey);
        }
        $listModel  = new \Model\AppCenter\ModuleList();
        $time       = time();
        $ModuleCountArr = $listModel->getAllModuleTotalNew($time);
        $redis->set($this->redisKey,$ModuleCountArr,'','',true);
    }
}
