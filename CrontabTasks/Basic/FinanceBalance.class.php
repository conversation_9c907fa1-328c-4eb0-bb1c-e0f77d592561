<?php
/**
 * 财务平衡
 * <AUTHOR>
 * @date   2018-3-14
 */
namespace CrontabTasks\Basic;
use Controller\Tpl\costallocation;
use Library\Db\Driver\MysqliAsync;
use Library\MulityProcessHelper;
use Model\TradeRecord\PftMemberJournal;
use Model\Finance\Deposite;
use Model\TradeRecord\PftTransReport;

class FinanceBalance extends \Library\Controller {

    private $_journalModel;

    /**
     *
     */
    public function __construct() {
        set_time_limit(0);
    }

    private function _getJournalModel() {
        if (empty($this->_journalModel)) {
            $this->_journalModel = new PftMemberJournal();
        }

        return $this->_journalModel;
    }

    private function _getReportModel() {
        if (empty($this->_reportModel)) {
            $this->_reportModel = new \Model\Report\FinanceBalance();
        }

        return $this->_reportModel;
    }

    private function _getSyncModel() {
        if (empty($this->_syncModel)) {
            $sqlConfig = C('db');
            $summaryConfig = $sqlConfig['summary'];
            $this->_syncModel = new MysqliAsync($summaryConfig['db_host'], $summaryConfig['db_user'], $summaryConfig['db_pwd'], $summaryConfig['db_name'], $summaryConfig['db_port'], 100);
        }

        return $this->_syncModel;
    }

    /**
     * 执行
     */
    public function run() {
        pft_log('finance_balance', '开始运行');
        $params = $GLOBALS['argv'];
        $day    = isset($params[3]) && strtotime($params[3]) ? $params[3] : date('Y-m-d', strtotime("-1 days"));

        //获取今天所有有产生交易记录的会员ID
        $memberJournal = $this->_getJournalModel();
        $fidList       = $memberJournal->getJournalFidCountInTime($day, $day);
        $fidList       = array_column($fidList, 'fid');

        $params        = [
            'day' => $day,
        ];

        //删除旧数据
        $res = $this->_delete($day);
        if ($res === false) {
            pft_log('finance_balance', '删除数据出错');
            exit;
        }

        //多进程分析数据
        if (false) {
            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
            $task = new MulityProcessHelper($this, 10, 500);
            $task->run($fidList, $params); // 如果前面连接了数据库、redis等，最好在这之前关闭掉
        } else {
            $this->runWorker($fidList, $params);
        }

        pft_log('finance_balance', '分析结束');

        //检测是否平衡
        pft_log('finance_balance', '开始检测平衡');
        $this->_checkBalance($day);
        pft_log('finance_balance', '检测结束');
    }


    /**
     * 子进程执行
     */
    public function runWorker($fidList, $params) {
        if (empty($fidList)) {
            pft_log('finance_balance', 'fid不能为空');
        }

        $day = $params['day'];
        $day = date('Ymd', strtotime($day));
        //循环获取
        $page = 1;
        //末期余额
        $lastMoney = [];
        //当日的最后一笔交易后的余额
        while (true) {
            $return = $this->_getData($fidList, $day, $page);
            if (empty($return)) {
                break;
            }

            $insertData = [];
            foreach ($return['inCome'] as $fid => $fidInfo) {
                foreach ($fidInfo as $dType => $value) {
                    $insertData[] = [
                        'date'   => $day,
                        'fid'    => $fid,
                        'dtype'  => $dType,
                        'action' => 0,
                        'dmoney' => $value,
                    ];
                }
            }

            foreach ($return['outCome'] as $fid => $fidInfo) {
                foreach ($fidInfo as $dType => $value) {
                    $insertData[] = [
                        'date'   => $day,
                        'fid'    => $fid,
                        'dtype'  => $dType,
                        'action' => 1,
                        'dmoney' => $value,
                    ];
                }
            }

            $lastMoneyTmp = $return['lastMoney'];

            $lastMoneyNew = array_diff_key($lastMoneyTmp, $lastMoney);

            $lastMoney    = $lastMoney + $lastMoneyNew;

            $res = $this->_syncInsertData($insertData);
            if ($res === false) {
                $this->_checkInsertResult();
                $res = $this->_syncInsertData($insertData);
                if ($res === false) {
                    pft_log('finance_balance', '写入数据失败');
                    break;
                }
            }
            $page++;
        }

        $this->_checkInsertResult();

        if (!empty($lastMoney)) {
            $insertData = [];
            foreach ($lastMoney as $key => $value) {
                $insertData[] = [
                    'fid'    => $key,
                    'date'   => $day,
                    'lmoney' => $value,
                ];
            }

            $model = $this->_getReportModel();
            $res   = $model->insertSingleData($insertData);
            if (empty($res)) {
                pft_log('finance_balance', '本期余额写入失败');
            }
        }
    }

    /**
     * 获取数据
     * @param $fidList
     * @param $day
     * @param $page
     * @return array|bool
     */
    private function _getData($fidList, $day, $page) {
        $inCome    = [];
        $outCome   = [];
        $lastMoney = [];

        $journalModel = $this->_getJournalModel();
        $data = $journalModel->getInfoByFidArrAndTimeInPlatform($fidList, $day, 'fid, dtype, daction, dmoney, lmoney', $page, 1000);

        if (empty($data)) {
            return [];
        }

        foreach ($data as $value) {
            if ($value['daction'] == 0) {
                if (isset($inCome[$value['fid']]) && isset($inCome[$value['fid']][$value['dtype']])) {
                    $inCome[$value['fid']][$value['dtype']] += $value['dmoney'];
                } else {
                    $inCome[$value['fid']][$value['dtype']]  = $value['dmoney'];
                }
            } elseif ($value['daction'] == 1) {
                if (isset($outCome[$value['fid']]) && isset($outCome[$value['fid']][$value['dtype']])) {
                    $outCome[$value['fid']][$value['dtype']] += $value['dmoney'];
                } else {
                    $outCome[$value['fid']][$value['dtype']]  = $value['dmoney'];
                }
            }

            if (!isset($lastMoney[$value['fid']])) {
                $lastMoney[$value['fid']] = $value['lmoney'];
            }
        }

        return ['inCome' => $inCome, 'outCome' => $outCome, 'lastMoney' => $lastMoney];
    }

    /**
     * 写入数据
     * @param $insertData
     * @return bool|int|\mysqli_result
     */
    private function _syncInsertData($insertData) {
        if (empty($insertData)) {
            return 0;
        }

        $syncSql = $this->_getSyncModel();
        $sql = 'insert into pft_report_finance_balance(`date`, `fid`, `dtype`, `action`, `dmoney`) VALUES';

        foreach ($insertData as $value) {
            $sql .= "({$value['date']}, {$value['fid']}, {$value['dtype']}, {$value['action']}, {$value['dmoney']}),";
        }
        $sql = rtrim($sql, ',');

        $res = $syncSql->async_query($sql, '');

        return $res;
    }

    /**
     * 检测写入结果
     */
    private function _checkInsertResult() {
        $syncSql = $this->_getSyncModel();
        $syncSql->fetch();
    }

    /**
     * 删除旧数据
     */
    private function _delete($day) {
        //启动之前先删除数据
        $model  = $this->_getReportModel();
        $result = true;

        //删除交易汇总数据
        while (true) {
            $res = $model->deleteFinanceBalanceData($day);
            if ($res === false) {
                $result = false;
                break;
            }

            if (empty($res)) {
                break;
            }
        }

        //删除末期余额数据
        while (true) {
            $res = $model->deleteFinanceLmoneyData($day);
            if ($res === false) {
                $result = false;
                break;
            }

            if (empty($res)) {
                break;
            }
        }

        //删除平衡检测结果数据
        while (true) {
            $res = $model->deleteFinanceBalanceResultData($day);
            if ($res === false) {
                $result = false;
                break;
            }

            if (empty($res)) {
                break;
            }
        }

        return $result;
    }

    /**
     * 检测是否平衡
     */
    private function _checkBalance($day) {
        $model = new \Model\Report\FinanceBalance();
        $page  = 1;
        while (true) {
            //当日的余额情况
            $memberInfo = $model->getLmoneyByDate($day, $page);

            if (empty($memberInfo)) {
                break;
            }

            //当日的交易情况
            $memberIdArr = array_column($memberInfo, 'fid');
            $transInfo   = $model->getFinanceLmoneyByFidAndDate($memberIdArr, $day);

            $inCome      = [];
            $outCome     = [];
            foreach ($transInfo as $value) {
                if ($value['action'] == 0) {
                    $inCome[$value['fid']]  = $value['dmoney'];
                } else {
                    $outCome[$value['fid']] = $value['dmoney'];
                }
            }

            //获取上期余额并进行比较
            $insertData = [];
            foreach ($memberInfo as $value) {
                $preMemberInfo = $model->getPreLastMoneyByFidAndDate($value['fid'], $day);

                $preLastMoney  = isset($preMemberInfo['lmoney']) ? $preMemberInfo['lmoney'] : 0;

                $nowLastMoney  = $value['lmoney'];

                $tmpIncome     = isset($inCome[$value['fid']]) ? $inCome[$value['fid']] : 0;
                $tmpOutCome    = isset($outCome[$value['fid']]) ? $outCome[$value['fid']] : 0;
                $isBalance     = $preLastMoney + $tmpIncome - $tmpOutCome == $nowLastMoney;

                $insertData[]  = [
                    'fid'    => $value['fid'],
                    'date'   => date('Ymd', strtotime($day)),
                    'result' => (int)$isBalance
                ];
            }

            $res = $model->insertBalanceResultData($insertData);
            if (!$res) {
                pft_log('finance_balance', '检测结果写入失败');
            }

            $page++;
        }
    }

    /**
     * 统计专项款-日汇总
     *
     * @date   2018-04-26
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return array
     */
    public function summaryDailyFinance() {
        pft_log('financeBanance/summaryDailyFinance', 'begin');
        $model = new Deposite();
        $tradeModel = new PftTransReport();

        $page  = 0;
        $pageSize = 500;

        $day = date('Ymd');

        while(true) {
            ++$page;

            $res = $model->getFinancesCodeByPage($page, $pageSize);

            if ($res) {
                $data = [];
                foreach ($res as $val) {
                    $data[] = [
                        'fid' => $val['user_id'],
                        'money' => $val['lmoney'],
                        'date' => $day
                    ];
                }

                $tradeModel->addDailyMoneyOfCode($data);
            } else {
                break;
            }
        }

        $page  = 0;

        while(true) {
            ++$page;

            $res = $model->getFinancesSmsByPage($page, $pageSize);

            if ($res) {
                $data = [];
                foreach ($res as $val) {
                    $data[] = [
                        'fid' => $val['user_id'],
                        'money' => $val['lmoney'],
                        'date' => $day
                    ];
                }

                $tradeModel->addDailyMoneyOfSms($data);
            } else {
                break;
            }
        }

        pft_log('financeBanance/summaryDailyFinance', 'finish');
    }
}