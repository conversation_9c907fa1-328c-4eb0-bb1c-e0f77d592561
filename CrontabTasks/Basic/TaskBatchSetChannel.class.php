<?php

namespace CrontabTasks\Basic;

use Library\Controller;
use Controller\product\Channel;

/**
 * 渠道相关任务
 *
 * <AUTHOR>
 * @date   2016-11-22
 */
class TaskBatchSetChannel extends Controller
{
    private $_logPath      = 'product/task/channel/batchSetChannel';
    private $_channelModel = null;
    private $_proMaxNum    = 100;  // 任务每次运行处理商品数量上限 
    private $_task         = [];

    public function __construct()
    {
        set_time_limit(0);
        $this->_channelModel = new \Model\Product\Channel();
    }

    /**
     * 执行批量渠道设置
     * <AUTHOR>
     * @time   2016-11-30
     */
    public function runBatChannelTask()
    {
        // 获取任务
        $task = Task::getUserTask(1, 0);
        if (empty($task)) {
            exit('暂无任务');

        } elseif (is_array($task) && !empty($task)) {
            $this->_task = $task;

        } else {
            exit('未知错误');
        }

        // 任务运行加锁
        $islock = Task::updateUserTask(
            ['id' => $this->_task['id']],
            [
                'run_status'    => 1,
                'last_run_time' => time(),
            ]
        );
        if (!$islock) {
            exit('锁定任务失败');
        }

        // 可售商品动态数量判断
        $taskData = json_decode($this->_task['data'], true);
        $channel  = new Channel();
        $proArr   = $channel->getAllSaleProduct($this->_task['fid']);
        $proCount = is_array($proArr) ? count($proArr) : 0; // 更新当前可售商品总数量
        if ($proCount <= 0 || $proCount < $taskData['offset']) {
            $this->finishTask(
                ['id' => $this->_task['id']],
                [
                    'status'        => 2,
                    'ratio'         => 100,
                    'run_status'    => 0,
                    'last_run_time' => time(),
                ]
            );
        }

        // 更新偏移量 + 更新状态值 + 更新进度
        $diffCount = $proCount - $taskData['offset'];
        if ($diffCount <= $this->_proMaxNum) {
            $taskOffset = $taskData['offset'] + $diffCount; // 一次可完成
            $taskStatus = 2;
        } else {
            $taskOffset = $taskData['offset'] + $this->_proMaxNum;
            $taskStatus = 1;
        }
        $ratio = intval(100 * ($taskOffset) / $proCount);
        $ratio = $ratio > 100 ? 100 : ($ratio < 0 ? 0 : $ratio);

        $limitProArr = array_slice($proArr, $taskData['offset'], $this->_proMaxNum);

        // 设置销售渠道
        $res = $this->_channelModel->setProductSaleChannel(
            $this->_task['fid'],
            $limitProArr,
            $taskData['setChannel'],
            $this->_task['create_time']
        );

        if ($res) {
            // 设置成功
            $data = json_encode([
                'setChannel' => $taskData['setChannel'],
                'proCount'   => $proCount,
                'offset'     => $taskOffset,
            ]);
            $this->finishTask(
                ['id' => $this->_task['id']],
                [
                    'data'          => $data,
                    'status'        => $taskStatus,
                    'ratio'         => $ratio,
                    'run_status'    => 0,
                    'last_run_time' => time(),
                ]
            );

        } else {
            // 设置失败
            $log = "任务[编号{$this->_task['id']}-批量配置渠道]添加失败";
            @pft_log($this->_logPath, $log);
        }
        exit();
    }

    /**
     * 批量渠道设置完成
     * <AUTHOR>
     * @time   2016-12-05
     *
     * @param  array  $where  查询条件
     * @param  array  $data  数据
     */
    private function finishTask($where = [], $data = [])
    {
        $result = Task::updateUserTask($where, $data);

        // 任务完成
        if ($result && $data['status'] == '2') {

            // 发送消息
            $msg = [
                'memberID' => $this->_task['fid'],
                'title'    => $this->_task['id'] . '-全部产品渠道配置成功',
                'content'  => '你编号为' . $this->_task['id'] . '的全部产品渠道配置任务已执行成功，请您校验',
            ];
            $res = Task::sendNotify($msg);

            // 记录日志
            $log = "任务[编号{$this->_task['id']}-批量配置渠道]已完成";
            if ($res['errcode'] == 1000) {
                $log .= ',消息通知[memberID=' . $msg['memberID'] . ']成功';
            } else {
                $log .= ',消息通知[memberID=' . $msg['memberID'] . ']失败:' . $res['msg'];
            }
            @pft_log($this->_logPath, $log);

        }
        exit('本轮完成');
    }
}