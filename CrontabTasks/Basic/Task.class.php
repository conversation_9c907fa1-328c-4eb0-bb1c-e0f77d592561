<?php

namespace CrontabTasks\Basic;

use Business\Order\Refund;
use Business\Order\RefundAudit;
use Business\PftSystem\SysKeyMonitor;
use Library\Business\CtripTicketMachine as CtripTicket;
use Library\Business\Uface\Person;
use Library\Business\YunXun\YunXunSDK;
use Library\Cache\Cache;
use Library\Constants\DingTalkRobots;
use Library\Constants\Order\OrderStatus\CommonOrderStatus;
use Library\Constants\RedisKey\SmsKeyConst;
use Library\Model;
use Library\Tools\Helpers;
use Library\Tools\YarClient;
use Model\AppCenter\ModulePayment;
use Model\Member\Member;
use Model\Order\OrderExpireAction;
use Model\Order\OrderHandler;
use Model\Order\OrderTools;
use Model\Order\OrderTrack;
use Model\Order\RefundAuditModel;
use Model\Order\SmsJournal;
use Model\Order\SmsOrderSummary;
use Model\Ota\CtripTicketMachine as CtripMachine;
use Model\Product\AnnualCard;
use Model\Product\Task as taskModel;
use Model\Product\Ticket;
use Model\Report\TerminalDataSummary;
use Model\Terminal\FaceCompare;
use Model\TradeRecord\OnlineRefund;
use Business\ElectronicInvoices\Invoice as InvoiceBiz;
use Model\TradeRecord\OrderRefund;
use Library\Constants\Table\MainTableConst;

// 权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

/**
 * 任务公用类
 * 任务相关操作类文件名建议以Task开头
 * 例如：TaskBatchSetChannel
 *
 * <AUTHOR>
 * @date   2016-12-05
 */
class Task
{
    /**
     * 获取一条用户任务记录
     * <AUTHOR>
     * @time   2016-12-05
     *
     * @param  int  $type  任务类型 [从1开始]
     * @param  int  $runStatus  运行状态 [0未运行|1运行中]
     * @param  int  $taskid  任务ID
     *
     * @return mixed [-1参数错误|成功任务记录|失败返回[]]
     */
    public static function getUserTask($type = 0, $runStatus = 0, $taskid = 0)
    {
        if (!is_numeric($type) || $type <= 0 || !is_numeric($runStatus)) {
            return -1; // 参数错误
        }

        if (is_numeric($taskid) && $taskid > 0) {
            $where = ['id' => $taskid];

        } else {
            $where = [
                'type'       => $type,
                'run_status' => $runStatus,
                'status'     => ['in', '0,1'],
            ];
        }

        $taskModel = new taskModel();
        $task      = $taskModel->getUserTask($where, '*', 1);

        return $task ? $task[0] : [];
    }

    /**
     * 获取多条用户任务记录
     * <AUTHOR>
     * @time   2017-04-24
     *
     * @param  int  $type  任务类型 [从1开始]
     * @param  int  $runStatus  运行状态 [0未运行|1运行中]
     * @param  array  $taskid  任务ID集合
     * @param  bool  $useMap  是否使用任务ID作为返回键值 [默认false不使用]
     * @param  array  $taskStatus  任务状态
     *
     * @return mixed [-1参数错误|成功任务记录|失败返回[]]
     */
    public static function getUserTasks($type = 0, $runStatus = 0, $taskid = [], $useMap = false, $taskStatus = [0, 1])
    {
        if (!is_numeric($type) || $type <= 0 || !is_numeric($runStatus) || !is_array($taskStatus)) {
            return -1;
        }

        if (is_array($taskid) && $taskid) {
            $where = [
                'id' => ['IN', $taskid],
            ];

        } else {
            $where = [
                'type'       => $type,
                'run_status' => $runStatus,
                // 'status'     =>  ['in', '0,1']
                'status'     => ['in', $taskStatus],
            ];
        }

        $taskModel = new taskModel();
        $tasks     = $taskModel->getUserTask($where);

        if ($useMap && $tasks) {
            $temp = [];
            foreach ($tasks as $task) {
                $temp[$task['id']] = $task;
            }
            $tasks = $temp;
            unset($temp);
        }

        return $tasks ?: [];
    }

    /**
     * 更新用户任务
     * <AUTHOR>
     * @time   2016-12-05
     *
     * @param  array  $where  查询条件
     * @param  array  $data  数据
     *
     * @return mixed [-1参数错误|true更新成功|false更新失败]
     */
    public static function updateUserTask($where = [], $data = [])
    {
        $taskModel = new taskModel();

        return $taskModel->updateUserTask($where, $data);
    }

    /**
     * 发送系统消息通知
     * <AUTHOR>
     * @time   2016-12-05
     *
     * @param  array  $msg  通知内容 [title标题|content正文|memberID接收消息的会员ID]
     *
     * @return array [errcode标示|msg信息]
     */
    public static function sendNotify($msg = [])
    {
        Helpers::loadPrevClass('notice');
        Helpers::loadPrevClass('MemberAccount');

        $notice = new \pft\notice();
        $member = new \pft\Member\MemberAccount(
            Helpers::getPrevDb(),
            $msg['memberID'],
            Helpers::GetSoapInside()
        );

        return $notice->AddNotice(
            $msg['title'], $msg['content'], 1, 9999, 1, 0, '', '', $member, $msg['memberID']
        );
    }

    private function getRefundJournal($type = 0)
    {
        $refundModel = new OrderRefund('localhost');
        if ($type == 0) {
            //查询前一个小时的数据
            $bt   = strtotime("-120 minutes");
            $et   = time();
            //TODO 参数这样传根本查不到东西嘛
            $data = $refundModel->getRefundJournalList(0, 0, 0, 0, $bt, $et, 0, '', 0, 1, 1000);
        } else {
            //查询前一个小时的数据
            $bt   = strtotime("-7 days");
            $et   = time() - 10; //这边给他-10秒让他不会取到刚好这个时间点取消的订单
            $data = $refundModel->getRefundJournalList($bt, $et, 0, 0, 0, 0, 0, '', 0, 1, 1000);
        }
        pft_log('cli_task', __CLASS__ . '::' . __FUNCTION__ . ';data=' . json_encode($data));
        unset($refundModel);
        if (!$data) {
            return false;
        }

        return $data;
    }

    private function handlerRefundJournal($data)
    {
        $orderModel  = new OrderHandler();
        $modelMember = new Member('localhost');
        $business    = new Refund();
        foreach ($data['list'] as $item) {
            $res = $business->orderTradeRefund($item['id'], $item, '系统自动运行', $orderModel, $modelMember);
            if ($res['code'] != 200) {
                Refund::sendFailMessage($item['ordernum'], $item['id'], $res);
            }
        }
    }

    /**
     * 订单取消自动退款程序
     * 在系统异常启用退款审核时，订单可以取消成功，但无法退款
     *
     * @return bool
     */
    public function failRefundJournalRetry()
    {
        $data = $this->getRefundJournal();
        if ($data) {
            $this->handlerRefundJournal($data);
        }
        unset($data);
        $data2 = $this->getRefundJournal(1);
        if ($data2) {
            $this->handlerRefundJournal($data2);
        }
        unset($data2);
        echo 'success';
    }

    /**
     * 每10分钟检查是否有延迟退款的订单
     *
     * @author: guanpeng
     * @date: 2019/5/2
     */
    public function delayOrderRefundTask()
    {
        $data = $this->getRefundJournal();
        if ($data) {
            $this->handlerRefundJournal($data);
            echo 'success';
        } else {
            echo 'success(no data to run)';
        }
    }

    /**
     * 郑州一卡通 提现
     * <AUTHOR>
     * @date   2018-04-13
     */
    public function cardSolutionRefund()
    {
        $logPath = 'card_refund';
        pft_log($logPath, '退款任务开始执行');
        $refundModel = new \Model\CardSolution\Refund();
        $begin       = date('Y-m-d H:i:s', time() - 3600);
        $end         = date('Y-m-d H:i:s');
        //获取过去一小时的退款任务
        $data = $refundModel->getRefundList($begin, $end);
        if (empty($data)) {
            pft_log($logPath, '未找到待退款任务');
            exit;
        }

        $idArr = array_column($data, 'id');
        $res   = $refundModel->updateJournalStatus($idArr, 2);
        if (empty($res)) {
            pft_log($logPath, '更新退款任务状态失败');
            exit;
        }

        $refundBusiness = new \Business\CardSolution\Refund();
        foreach ($data as $value) {
            $return    = $refundBusiness->refundTask($value);
            $realMoney = $return['real_money'] + $value['real_money'];
            if ($return['code'] == 200) {
                //成功
                $result = '成功';
                $memo   = '';
                $res    = $refundModel->updateStatusByJournalId($value['id'], 1, $realMoney);
                if (empty($res)) {
                    pft_log($logPath, $value['id'] . '更新状态失败, 处理结果:' . $result . ', 原因:' . $memo);
                }
            } else {
                //失败
                $result     = '失败';
                $errorTimes = $value['times'] + 1;
                $memo       = isset($return['msg']) ? $return['msg'] : '';
                $res        = $refundModel->updateStatusByJournalId($value['id'], 3, $realMoney, $errorTimes, $memo);
                pft_log($logPath, $value['id'] . '出现失败, 处理结果:' . $result . ', 原因:' . $memo);
            }
        }
    }

    /**
     * 每日定时删除人脸识别使用到的人脸图片
     */
    public function clearFacePhotos()
    {
        $expireTime = time();
        $model      = new FaceCompare();
        $data       = $model->getExpireData($expireTime);
        if ($data) {
            $model->updateFaceStatusMulti(array_column($data, 'id'), 1);
        }
    }

    /**
     * 定时清除设备上的过期人脸数据
     * <AUTHOR> Chen
     * @date 2018/10/19
     *
     * @return bool
     */
    public function handlerUfacePhotos()
    {
        $actionList = ['push', 'clear'];
        $action     = isset($GLOBALS['argv'][3]) ? $GLOBALS['argv'][3] : 'push';
        $model      = new FaceCompare();
        $timeStamp  = time();

        // 推送，使用年月日格式
        if ($action == 'push') {
            $message         = "人脸授权\n";
            $expireStartTime = date('Ymd');
            $expireTime      = 0;
            $method          = "devicesAuth";
        } elseif ($action == 'clear') {
            $message = "人脸消权\n";
            // 清除，使用时间戳格式
            $expireStartTime = strtotime('-2 days');
            $expireTime      = $timeStamp;
            $method          = "devicesAuthDelete";
        } else {
            exit("Invalid Action\n");
        }
        // RPC 服务
        $client    = new YarClient('face');
        $RpcResult = $client->call('/Face/Vip/faceDeviceClearAuthTask', []);
        $message   .= "RPC 清理数:" . is_array($RpcResult['res']['data']) ? $RpcResult['res']['data']['total'] : 0 . "\n";
        $RpcResult = $client->call('/Face/Vip/faceDeviceAuthTask', []);
        $message   .= "RPC 授权数:" . is_array($RpcResult['res']['data']) ? $RpcResult['res']['data']['total'] : 0 . "\n";

        $TaskList = $model->getUFaceTask($expireStartTime, $expireTime, $action);
        pft_log('cli_task', __CLASS__ . '::' . __FUNCTION__ . ';data=' . json_encode([
                $method,
                ['st' => $expireStartTime, 'et' => $expireTime, 'ac' => $action],
                $TaskList,
            ], JSON_UNESCAPED_UNICODE));
        $noCancelIds = [];
        if ($action == 'clear') {
            //如果人脸还有其他今天以后的任务，不取消授权
            $guidIds     = array_column($TaskList, 'guid');
            $faceIdOrder = $model->getAllGuidTask($guidIds);
            foreach ($faceIdOrder as $key => $value) {
                if ($value['clear_time'] > $timeStamp) {
                    $noCancelIds[] = $value['guid'];
                }
            }
        }
        //exit;
        if (!$TaskList) {
            //$message .= "没有任务需要处理\n" . date('Y-m-d H:i:s');
            //Helpers::sendDingTalkGroupRobotMessageRaw($message, DingTalkRobots::FACE_CRONTABLE);

            return false;
        }
        $sdk    = new Person();
        $okList = $failList = [];
        foreach ($TaskList as $item) {
            if (in_array($item['guid'], $noCancelIds)) {
                //如果人脸还有其他今天以后的任务，不取消授权
                continue;
            }
            $result = call_user_func_array([$sdk, $method], [$item['guid'], $item['deviceKeys']]);
            //$deleteRes = $sdk->devicesAuthDelete($item['guid'], $item['deviceKey']);
            if ($result['code'] == 'GS_SUS205' || $result['code'] == 'GS_SUS204') {
                $okList[] = $item['id'];
            } else {
                $failList[] = [
                    'id'  => $item['id'],
                    'msg' => $result['msg'],
                ];
            }
        }
        if ($okList) {
            $model->updateFaceClearTask($okList);
        }
        if ($failList) {
            pft_log('cli_task',
                __CLASS__ . '::' . __FUNCTION__ . ';data=' . json_encode($failList, JSON_UNESCAPED_UNICODE));
        }
        // $message .= "成功:" . count($okList) . "\n失败:" . count($failList) . "\n" . date('Y-m-d H:i:s');
        // Helpers::sendDingTalkGroupRobotMessageRaw($message, DingTalkRobots::FACE_CRONTABLE);

        return true;
    }

    /**
     * 过期人脸订单删除
     * 执行方式：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Basic_Task handlerUfaceExpireOrder
     * <AUTHOR>
     * @date 2019/02/19
     */
    public function handlerUfaceExpireOrder()
    {
        $model    = new FaceCompare();
        $playDate = date('Ymd');
        $beginDate = date('Ymd', strtotime('- 60 days'));   // 把时间限定在2个月内过期的。
        $Orders   = $model->getExpireFaceOrder($playDate, $beginDate);

        if (!count($Orders)) {
            pft_log('face/expire_delete', '{"type":"没有需要处理的订单"}');

            return false;
        }
        $orderIds = array_column($Orders, 'ordernum');
        $model->unbindFaceOrder($orderIds, 0, '', 1);

        $groupList  = array_column($Orders, 'groupid');
        $faceIdList = array_column($Orders, 'faceid');

        $deviceData = [];
        $devices    = $model->getDeviceByTerminalId($groupList);
        foreach ($devices as $val) {
            if (empty($deviceData[$val['terminal_id']])) {
                $deviceData[$val['terminal_id']] = $val['deviceKey'];
            } else {
                $deviceData[$val['terminal_id']] = $deviceData[$val['terminal_id']] . ',' . $val['deviceKey'];
            }
        }

        $faceInfos = $model->getFaceInfoByFaceId(false, $faceIdList, 'faceid,guid');
        $faceData  = [];
        foreach ($faceInfos as $val) {
            $faceData[$val['faceid']] = $val['guid'];
        }
        $person     = new Person();
        $okCnt      = $failCnt = 0;
        $baiduModel = new \Business\PftSystem\FaceCompare('baidu');
        $logData    = [];
        foreach ($Orders as $v) {
            if ($v['face_type'] == 2) {
                //uface
                $deleteRes = $person->devicesAuthDelete($faceData[$v['faceid']], $deviceData[$v['groupid']]);
                if ($deleteRes['code'] == 'GS_SUS205' || $deleteRes['code'] == 'GS_SUS204') {
                    $okCnt += 1;
                } else {
                    $failCnt += 1;
                }
                $logData[] = [
                    'type'     => 'uface_delete',
                    'ordernum' => $v['ordernum'],
                    'faceid'   => $v['faceid'],
                    'groupid'  => $v['groupid'],
                    'code'     => $deleteRes['code'],
                ];
            }
            else {
                //百度
                $deleteRes = $baiduModel->delFaceOrder($v['groupid'], $v['faceid']);
                if ($deleteRes['code'] == 200) {
                    $okCnt += 1;
                } else {
                    $failCnt += 1;
                }
                $logData[] = [
                    'type'     => 'baidu_delete',
                    'ordernum' => $v['ordernum'],
                    'faceid'   => $v['faceid'],
                    'groupid'  => $v['groupid'],
                    'code'     => $deleteRes['code'],
                ];
            }
        }
        pft_log('face/expire_delete', json_encode($logData, JSON_UNESCAPED_UNICODE));

        return true;

    }

    public function resetSystemErrorsCnt()
    {
        $startTime    = microtime_float();
        $smsChannelId = load_config('platform_list', 'sms_ac');
        foreach ($smsChannelId as $id => $name) {
            echo $id, "\n";
            SysKeyMonitor::clearSmsErrorTimes($id);
        }
        SysKeyMonitor::clearPayChannelErrorTimes();
        $endTime = microtime_float();
        $ustTime = $endTime - $startTime;
        $msg     = "系统错误次数清除成功\n - 系统耗时[{$ustTime}]秒";
        $msg     .= "- 服务器:" . gethostname();
        //Helpers::sendDingTalkGroupRobotMessage($msg, "定时任务提醒", __CLASS__ . '::' . __FUNCTION__,
        //    DingTalkRobots::DEFAULT_ROBOT);
        //file_get_contents('http://tool.pft12301.com/webhook/1014/send?title='
        //    .urlencode('🏦系统错误次数清除成功🏦').'&desp='.urlencode($msg));
        // 查询余额
       /* $yxSdk   = new YunXunSDK();
        $yxRes   = $yxSdk->getBalanceMoney();
        $yxMoney = isset($yxRes['Blance']) ? $yxRes['Blance'] : 0;
        $msg     = "云讯短信通道余额剩余:{$yxMoney}元\n";
        if ($yxMoney < 2000) {
            $msg .= "*** 余额紧张，请联系志萍确认充值 ***";
        }
        file_get_contents('http://tool.pft12301.com/webhook/1014/send?title='
            .urlencode('🏦短信平台余额查询🏦').'&desp='.urlencode($msg));*/
        //Helpers::sendDingTalkGroupRobotMessageRaw($msg, DingTalkRobots::FINANCE_CHECK);
    }

    /**
     * @deprecated
     */
    public function orderAutoExpire()
    {
        pft_log('orderMoveV1/debug/orderAutoExpire', '还有在调用...');
        $startTime     = microtime_float();
        $masterModel   = new \Library\Model('localhost_wsdl');
        $modelTerminal = new \Library\Model('terminal');
        $expireModel   = new OrderExpireAction();
        $ticketModel   = new Ticket('slave');

        //获取最近三天的过期订单，防止前两天订单意外没有过期，这次一起过期
        $yesterday       = date("Y-m-d", strtotime('-1 day'));
        $beforeYesterday = date("Y-m-d", strtotime('-2 day'));
        $threeDaysAgo    = date("Y-m-d", strtotime('-3 day'));
        $endTimeArr      = [$threeDaysAgo, $beforeYesterday, $yesterday];

        $map = [];
        //if (ENV == 'PRODUCTION') {
        //    $map['s.id'] = ['gt', 100535579];
        //}

        $map               = array_merge($map, [
            'status'    => ['in', [0, 4, 7, CommonOrderStatus::WAIT_APPOINTMENT_CODE, 80, 81]],
            's.endtime' => ['in', $endTimeArr],
        ]);
        $map['pay_status'] = ['neq', 2];
        $count             = $masterModel->table(MainTableConst::TABLE_SS_ORDER . ' s')->where($map)->count();

        echo "过期订单数量:{$count}\n";
        if (!$count) {
            exit("不存在过期订单\n");
        }

        $pageSize = 1000;
        $page     = ceil($count / $pageSize);

        $teamModel = new \Model\Order\TeamOrderSearch();

        for ($i = 1; $i <= $page; $i++) {
            // 获取过期订单
            $orderInfoList = $this->getExpireOrder($masterModel, $map, 0, $pageSize);
            // 修改平台订单状态
            $this->changeOrderStatus($masterModel, $orderInfoList['list']);
            // 追踪记录
            $this->orderTrack($orderInfoList['detail']);
            // 订单完结
            $this->finishOrder($ticketModel, $expireModel, $orderInfoList['detail']);
            //更改团单子单状态
            $teamModel->expireSonOrder($orderInfoList['list']);
            //更新团单主单状态
            $teamModel->expireMainOrder($orderInfoList['list']);
            usleep(100000);
        }

        //生产环境才进行通知
        if (ENV == 'PRODUCTION') {
            $endTime = microtime_float();
            $ustTime = $endTime - $startTime;
            $msg     = "{$yesterday}处理了[{$count}]笔过期订单,循环处理[{$page}]次(每次[{$pageSize}]笔)\n - 系统耗时[{$ustTime}]秒";
            $msg     .= "- 服务器:" . gethostname();
            Helpers::sendDingTalkGroupRobotMessage($msg, "订单过期提醒", __CLASS__ . '::' . __FUNCTION__,
                DingTalkRobots::DEFAULT_ROBOT);
        }
    }

    /**
     * @deprecated
     */
    private function getExpireOrder(Model $masterModel, $map, $offset, $limit)
    {
        $orderInfoList = [];
        $orderIdList   = [];
        $data          = $masterModel->table(MainTableConst::TABLE_SS_ORDER . ' s')->join(MainTableConst::TABLE_ORDER_ADDON . ' n on s.ordernum=n.orderid',
            'LEFT')
                                     ->field('s.id,n.ifpack,s.ordernum,s.tnum,s.tid,s.status')
                                     ->where($map)
                                     ->limit($offset, $limit)
                                     ->select();
        echo 'getExpireOrder:' . $masterModel->_sql(), "\n";
        foreach ($data as $item) {
            $orderInfoList[$item['ordernum']] = $item;
            //这里判断是否套票
            if ($item['ifpack'] == 1) {
                //主票时处理
            }
            $orderIdList[] = $item['ordernum'];
        }

        return ['list' => $orderIdList, 'detail' => $orderInfoList];
    }

    /**
     * 修改订单状态为过期
     *
     * @param  \Library\Model  $masterModel
     * @param $orderNumList
     *
     * @return bool
     */
    private function changeOrderStatus(Model $masterModel, $orderNumList)
    {
        $api = new \Business\JavaApi\Order\OrderInfoUpdate();
        $res = $api->batchBaseOrderInfoUpdate($orderNumList, ['status' => 2]);
        if ($res['code'] != 200) {
            pft_log("order/expire/status", json_encode($res));
        }

        $api = new \Business\JavaApi\Order\OrderTouristUpdate();
        $res = $api->BatchTouristInfoUpdate($orderNumList, ['checkState' => 4], ['checkState' => 0]);
        if ($res['code'] != 200) {
            pft_log("order/expire/status", json_encode($res));
        }

        return true;
    }

    /**
     * 订单追踪
     *
     * @param $orderInfoList
     */
    private function orderTrack($orderInfoList)
    {
        pft_log('order/expire/track', "OrderTrack:" . json_encode($orderInfoList));
        $data       = [];
        $orderModel = new OrderTools();
        $trackModel = new OrderTrack();
        foreach ($orderInfoList as $ordernum => $item) {
            $used_num = $item['tnum'] - ((int)$orderModel->getVerifiedNum($ordernum));
            $data[]   = [
                'ordernum'       => $ordernum,
                'action'         => \Model\Order\OrderTrack::ORDER_EXPIRE,
                'tid'            => $item['tid'],
                'tnum'           => $used_num,
                'left_num'       => 0,
                'source'         => 19,
                'terminal'       => 0,
                'branchTerminal' => 0,
                'id_card'        => 0,
                'SalerID'        => 0,
                'insertTime'     => date('Y-m-d H:i:s'),
                'oper_member'    => 0,
            ];
        }
        $trackModel->addTrackMulti($data);
    }

    /**
     * 过期订单是否需要自动处理
     *
     * @param  \Model\Product\Ticket  $ticketModel
     * @param  \Model\Order\OrderExpireAction  $expireModel
     * @param $orderInfoList
     */
    private function finishOrder(Ticket $ticketModel, OrderExpireAction $expireModel, $orderInfoList)
    {
        $actionArr          = [
            2 => '自动完结',
            3 => '自动验证',
            4 => '自动取消',
        ];
        $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
        foreach ($orderInfoList as $ordernum => $item) {
            $tid = $item['tid'];
            try {
                $ticketRes = $commodityTicketBiz->queryTicketInfoById($tid,
                    'expire_action, expire_action_days, expire_action_fee', '', 'p_type');
                pft_log('order/expire_action', json_encode(['tid', $ordernum, $tid, $ticketRes]));
                if (empty($ticketRes)) {
                    throw new \Exception('票属性获取失败');
                }
                $ticketInfo = array_merge($ticketRes['ticket'], $ticketRes['land']);
            } catch (\Exception $e) {
                continue;
            }

            $expireAction = intval($ticketInfo['expire_action']);
            $expireDay    = intval($ticketInfo['expire_action_days']);
            if ($ticketInfo['p_type'] == 'J') {
                if ($item['status'] == 80) {  //代发货
                    //这个走取消的
                    $expireAction = 4;
                } elseif ($item['status'] == 81) {  //已发货
                    //这个走验证的
                    $expireAction = 3;
                } else {
                    //跳过吧
                    continue;
                }
            }
            if ($expireAction == 4) {
                if ($ticketInfo['p_type'] == 'J') {
                    $expireFee = false;
                } else {
                    $expireFee = number_format($ticketInfo['expire_action_fee'], 2);
                }
            } else {
                $expireFee = false;
            }

            //不处理的直接略过
            if (!array_key_exists($expireAction, $actionArr)) {
                continue;
            }
            //将数据插入任务表
            $res = $expireModel->addTask($ordernum, $tid, time(), $expireDay, $expireAction, $expireFee);
            //写入日志
            pft_log('order/expire_action', json_encode(['res', $ordernum, $expireAction, $res]));
        }
    }


    /**
     * 重新处理在线支付退款失败的请求
     * 由于支付平台每天凌晨都会自动清分交易金额到银行账户，会导致原路退款时金额不足导致退款失败
     * 每日12点、18点定时处理一次
     *
     * @return bool
     */
    public function autoOrderRefund()
    {
        $model     = new OnlineRefund();
        $yesterday = strtotime('-3 days');
        $endtime   = time();
        $data      = $model->getRefundFailRequest($yesterday, $endtime, 2000, 5);
        echo $model->_sql(), "\n";
        //exit;
        if (!$data) {
            return true;
        }
        $totalCnt = count($data);
        $okCnt    = $failCnt = 0;
        $okIdList = $failIdList = [];
        foreach ($data as $item) {
            // 其它非订单的暂不支持自动退款，可能存在押金现场给游客退现金了，待与产品沟通确认方案。
            if ($item['action']!='index') continue;

            $action = !$item['action'] ?'index' : $item['action'];
            $res = curl_post(PAY_DOMAIN . "/r/OnlineRefund/{$action}/", json_decode($item['content']));
            $obj = json_decode($res);
            if ($obj->code == 200) {
                $okCnt      += 1;
                $okIdList[] = $item['id'];
            } else {
                $failIdList[] = $item['id'];
                $failCnt      += 1;
            }
            usleep(100000);
        }
        if (count($okIdList) > 0) {
            $model->deleteRefundFailRequest($okIdList);
        }
        if (count($failIdList) > 0) {
            $model->increseRetryTime($failIdList);
        }
        $dingTalkMsg = "总条数:{$totalCnt},处理成功条数:{$okCnt},处理失败条数:{$failCnt}";
        file_get_contents('http://tool.pft12301.com/webhook/1008/send?title=' .urlencode('退款失败定时重试任务') . '&desp='.urlencode($dingTalkMsg));
    }

    /**
     * 第三方系统定时数据推送
     */
    public function thirdSystemDataHourlyPush()
    {
        $confList = load_config('common', 'tsd_push');
        foreach ($confList as $item) {
            if (!class_exists($item['pftClass'])) {
                continue;
            }
            $obj       = new $item['pftClass'];
            $pftAction = $item['pftAction'];
            call_user_func([$obj, $pftAction], $item);

            unset($obj);
        }
    }

    /**
     * 定时取消携程订单超过3分钟未完成的订单
     *
     * <AUTHOR>
     * @date   2018-05-30
     */
    public function timingSyncCancelCtripOrder()
    {
        $model        = new CtripMachine();
        $beforeTime   = time() - 180;
        $exceptStatus = "5,7,8";
        //获取出支付时间超过180秒的订单
        $cancelIdList = $model->getUnpayOrder($beforeTime, $exceptStatus);
        print_r($cancelIdList);
        //不为空时
        if (!empty($cancelIdList) && count($cancelIdList) > 0) {
            $updateList = [];
            $biz        = new CtripTicket();
            foreach ($cancelIdList as $item) {
                //获取到携程接口返回的状态
                $res = $biz->cancelCtripOrder($item, 1);
                //如果携程接口返回取消成功之后，  将取消成功的平台订单id存到数组中用于批量更新
                if ($res['code'] == 200) {
                    $updateList[] = $item;
                }
            }
            pft_log('cli_task',
                __CLASS__ . '::' . __FUNCTION__ . ';ctrip_order_auto_cancel=' . json_encode($updateList));
            //批量更新携程已取消成功的  pft_ctrip_machine_order表中的订单状态
            $model->cancelCtripOrder($updateList);
        }
    }

    public function appCenterPackageSummary()
    {
        $bta   = date('Y-m-01');
        $bt    = strtotime($bta);
        $et    = time();
        $month = date('m');
        $model = new ModulePayment();
        $data  = $model->summaryPackage($bt, $et);
        $title   = "🐼每月汇总({$month}月)\n\n";
        //$msg  .= str_pad("套餐名称", 16, ' ',STR_PAD_LEFT)."|".str_pad('总数', 6,' ', STR_PAD_LEFT)."|".str_pad('价格', 10,' ',STR_PAD_LEFT). "|总额\n";
        $msg        = "套餐名称\t|总数\t|价格\t|总额\t|\n";
        $totalMoney = 0;
        $totalCnt   = 0;
        foreach ($data as $key => $item) {
            $fee        = $item['fee'] / 100;
            $cnt        = str_pad($item['cnt'], 3, ' ', STR_PAD_LEFT);
            $totalFee   = $fee * $item['cnt'];
            $totalMoney += $totalFee;
            $totalCnt   += $item['cnt'];
            $totalFee   = number_format($totalFee);
            $msg        .= "{$item['name']}({$item['package_id']})\t|{$cnt}\t|{$fee}\t|{$totalFee}\t|\n";
        }
        $totalMoney = number_format($totalMoney);
        $msg        .= "\n 总数量:{$totalCnt},总金额:{$totalMoney}";
        $msg        .= "\n\n汇总时间段:" . $bta . '至' . date('Y-m-d H:i:s') . "\n";
        echo $msg;

        file_get_contents('http://tool.pft12301.com/webhook/1003/send?title='
            .urlencode($title).'&desp='.urlencode($msg));

        Helpers::sendDingTalkGroupRobotMessageRaw($title.$msg, DingTalkRobots::MARKET_NEWS);
    }

    public function ctripOrderSummary()
    {
        $bt           = isset($GLOBALS['argv'][3]) ? $GLOBALS['argv'][3] : date('Y-m-d 00:00:00');
        $et           = isset($GLOBALS['argv'][4]) ? $GLOBALS['argv'][4] : date('Y-m-d 23:59:00');
        $bt           = strtotime($bt);
        $et           = strtotime($et);
        $model        = new CtripMachine();
        $data         = $model->summaryOrders($bt, $et);
        $totalTickets = 0;
        $dateTitle    = "日期：" . date('m月d日', $bt) . '至' . date('m月d日', $et) . "\n";
        $sendTime     = date('Y-m-d H:i:s');
        $firstMsg     = $dateTitle . "成交订单:{$data['summary'][7]}(笔)\n取消订单:{$data['summary'][8]}(笔)\n";
        $tidList      = array_column($data['detail'], 'pft_tid');
        $tidList      = array_unique($tidList);
        //print_r($tidList);
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidList, 'id,title,landid', '', 'title');

        $tickets = [];
        foreach ($ticketArr as $ticketInfos) {
            $tickets[$ticketInfos['ticket']['id']] = [
                'tid'    => $ticketInfos['ticket']['id'],
                'ttitle' => $ticketInfos['ticket']['title'],
                'landid' => $ticketInfos['ticket']['landid'],
                'ltitle' => $ticketInfos['land']['title'],
            ];
        }

        $ticketMap           = [];
        $scenicMap           = [];
        $detailSummaryReport = [];
        foreach ($tickets as $ticket) {
            $ticketMap[$ticket['tid']]      = $ticket;
            $scenicMap[$ticket['landid']][] = $ticket;
        }
        foreach ($data['detail'] as $item) {
            $lid                                                       = $ticketMap[$item['pft_tid']]['landid'];
            $item['product']                                           = $ticketMap[$item['pft_tid']]['ttitle'];
            $detailSummaryReport[$lid]['title']                        = $ticketMap[$item['pft_tid']]['ltitle'];
            $detailSummaryReport[$lid]['data'][$item['orderstatus']][] = $item;
        }
        //$summaryReport = [];
        $msg = "明细汇总\n\n";
        foreach ($detailSummaryReport as $items) {
            $msg .= "{$items['title']}\n";
            foreach ($items['data'] as $status => $item) {
                if ($status != 7 && $status != 5) {
                    continue;
                }

                //if ($status == 7) {
                //    $msg .= "\t成功订单\n";
                //} else {
                //    $msg .= "\t取消订单\n";
                //}
                //print_r($item);
                foreach ($item as $subItems) {
                    if ($status == 7 || $status == 5) {
                        $totalTickets += $subItems['qty'];
                    }
                    $msg .= "\t{$subItems['product']} X {$subItems['qty']}(张) \n";
                }
            }
            $msg .= "\n";
        }
        $msg .= "通知时间:{$sendTime}\n";
        echo $msg;
        $firstMsg .= "出票总数:{$totalTickets}\n";
        Helpers::sendDingTalkGroupRobotMessageRaw($firstMsg, DingTalkRobots::CTRIP_DATA_REPORT);
        Helpers::sendDingTalkGroupRobotMessageRaw($msg, DingTalkRobots::CTRIP_DATA_REPORT);
    }

    // /usr/bin/php /var/www/html/Service/Crontab/runNew.php Basic_Task refreshNNAccessToken
    /**
     * 定时刷新诺诺发票用户的access_token
     *
     * @return bool
     */
    public function refreshNNAccessToken()
    {
        $invoiceBiz  = new InvoiceBiz();
        $invoiceApi  = new \Business\ElectronicInvoices\InvoiceApi();
        $invoiceInfo = $invoiceApi->getNeedRefreshTokenEnterprise();
        pft_log('debug/lee', 'result: ' . json_encode($invoiceInfo));
        if ($invoiceInfo['code'] == 200 && !empty($invoiceInfo['data'])) {
            foreach ($invoiceInfo['data'] as $key => $value) {
                if (!empty($value['user_id']) && !empty($value['refresh_token']) && !empty($value['tariff_number']) && !empty($value['sid'])) {
                    //判断下时间差 如果时间差在23小时58分之内的都不继续刷新token或者时间长时间未更新，小于当前时间的。或者历史数据，上次更新时间超过1天的不做更新token
                    $nowTime  = time();
                    //判断更新时间 如果更新时间大于当前时间 说明是619上线后更新的 update_time = 过期时间  否则时间就是上次的更新时间  新旧数据需要判断时间差，时间差超过1天或者 当天已经更新过的就不在处理了
                    $diffTime = $value['update_time'] - $nowTime;

                    //过期时间和当前时间的时差在10分钟之内的回去刷新token
                    if ($diffTime && $diffTime > 0 && $diffTime < 60 * 10) {
                        $res = $invoiceBiz->refreshAccessToken($value['refresh_token'], $value['user_id'],
                            $value['tariff_number'], $value['sid'], $value['sys_id'] ?? 0);
                        if ($res === false) {
                            pft_log('Invoice/fail', json_encode([
                                'refresh_fail',
                                $value,
                            ]));
                        }
                    }
                }
            }
        }
    }

    /**
     * 每天定时检查设备授权状态
     * @Author: zhujb
     * 2019/2/18
     */
    public function checkDeviceAuthStatus()
    {
        $yarClient = new YarClient('device');
        $yarClient->call('Device/Device/checkDeviceAuthStatus', []);
    }

    /**
     * 每天定时汇总设备运行数据
     * @Author: zhujb
     * 2019/11/13
     */
    public function summaryDeviceBusiness()
    {
        $yarClient = new YarClient('device');
        $yarClient->call('Device/Device/recordDeviceBusinessSummary', []);
    }

    /**
     * 每日设备活跃数，增量数据记录
     * @Author: zhujb
     * 2019/12/26
     */
    public function deviceIncRecord()
    {
        $yarClient = new YarClient('device');
        $yarClient->call('Device/Device/deviceIncRecord', []);
    }

    /**
     * 删除半年前的数据
     *
     * @author: guanpeng
     * @date: 2019/3/26
     */
    public function deleteHistorySmsJournals()
    {
        $time  = strtotime("-180 days");
        $model = new SmsJournal();
        $model->deleteJournals($time);
    }

    /**
     * 年卡续费提醒
     *
     * @author: guanpeng
     * @date: 2019/3/27
     */
    public function annualCardRenewNotify()
    {
        $annualMdl = new AnnualCard();
        $list      = $annualMdl->getWxAnnualConfigList();
        if (empty($list)) {
            return false;
        }

        $wxOpenModel  = new \Model\Wechat\WxOpen();
        $annualBiz    = new \Business\Product\AnnualCard();
        $time         = date('Y-m-d', time());
        $bindWxConfig = $wxOpenModel->getAppIdByFid(array_column($list, 'member_id'), 'fid,appid', 1, true);

        $configs = [];
        foreach ($bindWxConfig as $item) {
            $configs[$item['fid']] = $item['appid'];
        }
        foreach ($list as $key => $value) {
            $duffDay = strtotime($time) - strtotime(date('Y-m-d', $value['begin_time']));
            $dayNum  = ceil($duffDay / 86400);
            if ($dayNum % $value['space_day'] != 0 || $dayNum < 1) {
                continue;
            }
            $expireTime = strtotime('+' . $value['validity'] . 'day');
            $annualBiz->getSendAnnualNoticeUser($value['member_id'], $configs[$value['member_id']], $expireTime,
                $value['notice_num']);
        }
    }

    /**
     * 2分钟执行一次删除es中的数据
     *
     */
    public function delEsData()
    {
        pft_log('order_es_sync/delete', '运行删除ES数据');

        //// 搜索所有需要删除的es同步后的数据
        //$esSyncModel = new \Model\Tools\EsSyncTask('slave');
        //$cacheKey    = "config:del_es_data";
        //$redis       = Cache::getInstance('redis');
        //$nextTime    = $redis->get($cacheKey);
        //$t           = time();
        //// 如果执行时间未到，跳过
        //if ($nextTime && $nextTime > $t) {
        //    return false;
        //}
        //$esInfoResArr = $esSyncModel->selectEsOrderInfoByFlagStatus(Z1, 3, $field = 'id, track_id', 1, 600);
        //if (empty($esInfoResArr)) {
        //    // 如果没数据，时间完后延迟30分钟
        //    $nextTime = strtotime("+30 minutes", $t);
        //    $redis->set($cacheKey, $nextTime);
        //
        //    return false;
        //}
        //
        //$trackIdArr = array_column($esInfoResArr, 'track_id');
        //pft_log('order_es_sync/delete', '运行删除ES数据:' . json_encode($trackIdArr, JSON_UNESCAPED_UNICODE));
        //
        //// 删除es数据 更新es_sync_status 为5的待删除状态
        //$toolModel = new \Model\Order\OrderTools();
        //$toolModel->delEsStatusData($trackIdArr);

        exit('完成');
    }

    /**
     * 比较今年今日和去年今日的下单验证数据
     * Create by zhangyangzhen
     * Date: 2019/4/24
     * Time: 14:20
     */
    public function compareDataWithLastYear()
    {
        //php run.php cli_Task CompareDataWithLastYear 2019-04-04 17  第4个参数为日期，第5个参数为小时
        $argv = $GLOBALS['argv'];

        $nextHour = $argv[4] ?: date('H');
        $hour     = intval($nextHour) - 1; //返回上一个小时的数据
        $today    = $argv[3] ?: date('Y-m-d');

        if ($argv[3]) {
            $lastTm  = strtotime($argv[3]);
            $year    = date('Y', $lastTm) - 1;
            $lastday = $year . '-' . date('m-d', $lastTm);
        } else {
            $lastTm  = strtotime('-1 year');
            $lastday = date('Y-m-d', $lastTm);
        }

        $model = new TerminalDataSummary();
        //去年今日与今年今日的下单&验证数据
        $todayCreateSummary   = $model->createMonthSummary($today, $today);
        $lastdayCreateSummary = $model->createMonthSummary($lastday, $lastday);
        $todayPrintSummary    = $model->monthSummary($today);
        $lastdayPrintSummary  = $model->monthSummary($lastday);

        $todayCreateCnt   = $todayCreateSummary[0]['create_cnt'] ?: 0;
        $todayPrintCnt    = $todayPrintSummary[0]['print_cnt'] ?: 0;
        $lastdayCreateCnt = $lastdayCreateSummary[0]['create_cnt'] ?: 0;
        $lastdayPrintCnt  = $lastdayPrintSummary[0]['print_cnt'] ?: 0;

        //计算今年与去年的倍数
        if ($todayCreateCnt == 0 || $lastdayCreateCnt == 0) {
            $createCompare = 0;
        } else {
            $createCompare = round($todayCreateCnt / $lastdayCreateCnt, 2);
        }

        if ($todayPrintCnt == 0 || $lastdayPrintCnt == 0) {
            $printCompare = 0;
        } else {
            $printCompare = round($todayPrintCnt / $lastdayPrintCnt, 2);
        }

        //去年今日某时与今年今日某时的下单&验证数据
        $hourCreateSummary    = $model->getCreateSummaryByDateAndHour($today, $hour);
        $lastHourCreate       = $model->getCreateSummaryByDateAndHour($lastday, $hour);
        $hourPrintSummary     = $model->getPrintSummaryByDateAndHour($today, $hour);
        $lastHourPrintSummary = $model->getPrintSummaryByDateAndHour($lastday, $hour);

        $hourCreateCnt     = $hourCreateSummary['create_cnt'] ?: 0;
        $lastHourCreateCnt = $lastHourCreate['create_cnt'] ?: 0;
        $hourPrintCnt      = $hourPrintSummary['print_cnt'] ?: 0;
        $lastHourPrintCnt  = $lastHourPrintSummary['print_cnt'] ?: 0;

        //计算今年与去年的倍数
        if ($hourCreateCnt == 0 || $lastHourCreateCnt == 0) {
            $hourCreateCompare = 0;
        } else {
            $hourCreateCompare = round($hourCreateCnt / $lastHourCreateCnt, 2);
        }

        if ($hourPrintCnt == 0 || $lastHourPrintCnt == 0) {
            $hourPrintCompare = 0;
        } else {
            $hourPrintCompare = round($hourPrintCnt / $lastHourPrintCnt, 2);
        }

        //获取去年当前小时的下单验证数据，并预测今年的数据
        $currentCreate = $model->getCreateSummaryByDateAndHour($lastday, $nextHour);
        $currentPrint  = $model->getPrintSummaryByDateAndHour($lastday, $nextHour);
        $expectCreate  = ceil($currentCreate['create_cnt'] * $hourCreateCompare);
        $expectPrint   = ceil($currentPrint['print_cnt'] * $hourPrintCompare);
        $weekList      = ['日', '一', '二', '三', '四', '五', '六'];
        //返回给钉钉推送的数据，不能用编辑器换行，会出现格式错乱
        $msg = "下单数量：\n{$lastday}：{$lastdayCreateCnt}\n"
               . "\n{$today} {$hour}时：{$hourCreateCnt}\n"
               . "{$lastday} {$hour}时：{$lastHourCreateCnt}\n小时下单为去年{$hourCreateCompare}倍\n"
               . "{$nextHour}时预计下单数：{$expectCreate}\n\n"
               . "入园数量：\n{$lastday}：{$lastdayPrintCnt}\n"
               . "\n{$today} {$hour}时：{$hourPrintCnt}\n{$lastday} {$hour}时：{$lastHourPrintCnt}\n"
               . "小时验证为去年{$hourPrintCompare}倍\n{$nextHour}时预计验证数：{$expectPrint}\n\n"
               . "去年今日：" . $lastday . '(' . $weekList[date('w', $lastTm)] . ')';

        Helpers::sendDingTalkGroupRobotMessageRaw($msg, DingTalkRobots::DATA_COMPARE);
    }

    /**
     * 因系统异常需要退票审核的订单数据处理
     * @author: guanpeng
     * @date: 2019/5/22
     */
    public function handlerSystemExceptionOrders()
    {
        //直接进行废弃
        return false;

        //if (!SysConfig::getGlobalRefundAduit()) {
        //    return false;
        //}
        //$model     = new RefundAuditModel('slave');
        //$now       = time();
        //$startTime = date('Y-m-d H:i:s', $now - 7200); // 2个小时前的数据，2*60*60
        //$dataList  = $model->getSysExceptionList($startTime, date('Y-m-d H:i:s', $now));
        //if (!$dataList) {
        //    return false;
        //}
        //$model          = null;
        //$refundBiz      = new Refund();
        //$refundAuditBiz = new RefundAudit();
        //$failList       = $successList = [];
        //
        //foreach ($dataList as $item) {
        //    $refundAuditBiz->setLoginInfo(['sid' => $item['apply_did'], 'memberID' => 1, 'dtype' => 0]);
        //    $result = $refundAuditBiz->updateRefundAudit($item['ordernum'], 1, '系统自动审核', 1, $item['id']);
        //    if (is_numeric($result)) {
        //        $failList[] = [
        //            'data' => $item,
        //            'res'  => [$result, $refundBiz->getMsg($result)],
        //        ];
        //    } else {
        //        $successList[] = $item;
        //    }
        //}
        //if (count($failList)) {
        //    $failJson = json_encode($failList, JSON_UNESCAPED_UNICODE);
        //    $message  = "订单退票审核失败\n\n" . $failJson;
        //    Helpers::sendDingTalkGroupRobotMessageRaw($message, DingTalkRobots::DEFAULT_ROBOT);
        //}
        //pft_log('cli_task', '退票审核;success=' . json_encode($successList, JSON_UNESCAPED_UNICODE) . ';fail=' . $failJson);
        //
        //return true;
    }

    /**
     * 定时更新有赞店铺的token
     * 提前token过期时间的三天前进行更新，每次运行时间不得超过半小时
     *
     */
    public function refreshYouZanToken()
    {
        $startTime           = time();
        $time                = time() + (3600 * 24 * 3);   // 提前三天去更新他们的token
        $otherSysConfigModel = new \Model\Ota\OtherSysConfig();

        $youzanTokenWillExpiresArr = $otherSysConfigModel->getYouZanTokenListByExpiresTime($time);

        if (!empty($youzanTokenWillExpiresArr)) {
            // 循环调用需要更新的令牌的数据
            foreach ($youzanTokenWillExpiresArr as $item) {

                $jobId = \Library\Resque\Queue::push('cooperation_system', 'GroupOrder_Job',
                    [
                        'groupKey' => 6,
                        'method'   => 'refreshTokenFromYouZan',
                        'source'   => ['refreshToken' => $item['refresh_token']],
                    ]
                );
            }
        }

        // 比较当前运行时间
        $runingTime = time() - $startTime;
        if ($runingTime > 300) {
            // 运行超过5分钟记录日志排查
            $logData = json_encode([
                'key'   => '有赞 定时任务更新access_token运行时间过长',
                'stime' => $startTime,
                'etime' => time(),
            ], JSON_UNESCAPED_UNICODE);
            pft_log('other_systerm_request', $logData, 3);
        }

        // 有赞更新token数量
        if (empty($youzanTokenWillExpiresArr) || !is_array($youzanTokenWillExpiresArr)) {
            $youzanTokenWillExpiresNum = 0;
        } else {
            $youzanTokenWillExpiresNum = count($youzanTokenWillExpiresArr);
        }

        $logData = json_encode([
            'key'   => '有赞 定时任务更新access_token',
            'stime' => $startTime,
            'etime' => time(),
            'num'   => $youzanTokenWillExpiresNum,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('other_systerm_request', $logData, 3);

        exit;
    }

    /**
     * 过期会员关系审核状态
     *
     * @param $params
     *
     * @return array|string
     */
    public function expireMemberRelationShipAuditRelationship()
    {
        return false;
    }



    /**
     * 统计短信发送的数量--每30分钟执行一次
     * <AUTHOR>
     * @date  2020-10-07
     * @return array|string
     */
    public function summarySmsSendInfo()
    {
        $redisService = Cache::getInstance('redis');
        $msg          = '执行成功';
        try {
            $lockRes = $redisService->get(SmsKeyConst::SMS_SUMMARY_LOCK);
            if ($lockRes) {
                echo '还在锁中';

                return false;
            }
            $redisService->set(SmsKeyConst::SMS_SUMMARY_LOCK, 1, '', 600);
            $smsOrderSummaryMdl = new SmsOrderSummary();
            $forNum             = 30;  //循环次数
            $taskNum            = 1000;  //一次处理条数

            for ($i = 0; $i < $forNum; $i++) {
                $taskData = [];
                for ($b = 0; $b < $taskNum; $b++) {
                    $data = $redisService->rPop(SmsKeyConst::SMS_SUMMARY_KEY);
                    if (!$data) {
                        break;
                    }
                    $jsonData   = @json_decode($data, true);
                    $taskData[] = [
                        'mobile'       => $jsonData['mobile'],
                        //'content'      => '',//$jsonData['content'],
                        'create_time'  => $jsonData['sendTime'],
                        'temp_id'      => $jsonData['tempId'],
                        'sms_supplier' => $jsonData['sms_supplier'],
                        'num'          => $jsonData['num'] ?: 1,
                    ];
                }
                if (empty($taskData)) {
                    break;
                }
                pft_log('smsSummary/task_count', '处理了:' . count($taskData));
                $result = $smsOrderSummaryMdl->addOrderSmsSendLog($taskData);
                if (!$result) {
                    pft_log('smsSummary/sql_error', $smsOrderSummaryMdl->getLastSql());
                }
            }
        } catch (\Exception $e) {
            $msg = $e->getMessage();
        }
        $redisService->rm(SmsKeyConst::SMS_SUMMARY_LOCK);
        echo $msg;

        return true;
    }

    /**
     * 汇总上个月短信的发送量并钉钉通知财务
     * @author: Guangpeng Chen
     *
     * @date: 2020/10/9
     * @return bool
     */
    public function summarySmsAndNotify()
    {
        $redisService = Cache::getInstance('redis');
        if ($redisService->get(SmsKeyConst::SMS_SUMMARY_SEND_DING_DING)) {
            echo 'Task is running';
            return false;
        }

        $currentDate = new \DateTime();

        // 获取上个月的日期
        $lastMonth = $currentDate->sub(new \DateInterval('P1M'));

        // 获取上个月的第一天
        $firstDay = new \DateTime($lastMonth->format('Y-m-01'));

        // 获取上个月的最后一天
        $lastDay = new \DateTime($lastMonth->format('Y-m-t'));

        // 输出结果
        //echo "上个月的第一天：" . $firstDay->getTimestamp() . "\n";
        //echo "上个月的最后一天：" . $lastDay->getTimestamp() . "\n";

        $channelConf = load_config('platform_list','sms_ac');
        $redisService->set(SmsKeyConst::SMS_SUMMARY_SEND_DING_DING, 1, '', 3600);
        $smsOrderSummaryMdl = new SmsOrderSummary();
        //$beginLastMonth     = strtotime(date('Y-m-1 00:00:00', strtotime('last month')));
        //$endLastMonth       = strtotime(date('Y-m-d 23:59:59', strtotime(date('Y-m-1') . '-1 day')));
        $sendInfo           = $smsOrderSummaryMdl->getSmsSendCountByChannel($firstDay->getTimestamp(), $lastDay->getTimestamp());
        $dingMsg            = '月份：'. $lastMonth->format('y年m月')."\n短信费用月度汇总\n通道ID\t|发送数量|实际收费条数\n";
        if ($sendInfo) {
            foreach ($sendInfo as $key => $value) {
                $dingMsg .= "{$channelConf[$value['sms_supplier']]}\t|{$value['send_num']}\t|{$value['charge_num']}\n";
            }
        } else {
            $dingMsg = '上个月的没数据';
        }
        echo $dingMsg,"\n";
        file_get_contents('http://tool.pft12301.com/webhook/1014/send?title='
            .urlencode('🏦上月发送发送汇总🏦').'&desp='.urlencode($dingMsg));
        //Helpers::sendDingTalkGroupRobotMessageRaw($dingMsg, DingTalkRobots::FINANCE_CHECK);
        $redisService->rm(SmsKeyConst::SMS_SUMMARY_SEND_DING_DING);
        echo 'Success';
        return true;
    }
}
