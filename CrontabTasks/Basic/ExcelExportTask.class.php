<?php
/**
 * 报表导出的脚本
 * 任务列表从myuu.excel_task表获取
 * 每两分钟获取一次
 *
 * <AUTHOR>
 * @since   2016-11-03
 */

namespace CrontabTasks\Basic;

use       Library\Controller;
use Library\MulityProcessHelper;
use       Model\MassData;
use Task\MemberTradeRecord;

//权限判断
if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

//定义权限
defined("IS_EXCEL_EXPORT") or define("IS_EXCEL_EXPORT", 1);

class ExcelExportTask extends Controller {

    private $_model;
    private $_errPath = "/excel/error";
    private $_sucPath = "/excel/success";
    private $_nonPath = "/excel/none";


    public function __construct() {
        set_time_limit(0);
        $this->_model = new MassData\ExcelTask();
    }

    /**
     * 入口
     */
    public function index() {
        //获取任务列表
        $taskList = $this->_model->getTaskList();

        if (!$taskList) {
            pft_log($this->_nonPath, "未发现任务");
            exit;
        }

        //        if (function_exists('pcntl_fork')) {
        //            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
        //            $task = new MulityProcessHelper($this, 8);
        //            $task->run($taskList); // 如果前面连接了数据库、redis等，最好在这之前关闭掉
        //        } else {
        //            $this->runWorker($taskList);
        //        }
        $this->runWorker($taskList);
    }

    public function runWorker($taskList) {
        $excelType = load_config('excelType', 'excel');
        foreach ($taskList as $task) {
            if (empty($task['excel_type'])) {
                pft_log($this->_nonPath, "{$task['id']}|未指定报表类型");
                continue;
            }

            if (!isset($excelType[$task['excel_type']]['class']) || !isset($excelType[$task['excel_type']]['action'])) {
                pft_log($this->_errPath, "{$task['id']}|报表类型未知, {$task['excel_type']}");
                continue;
            }

            $class  = $excelType[$task['excel_type']]['class'];
            $action = $excelType[$task['excel_type']]['action'];

            if (!class_exists($class) || !method_exists($class, $action)) {
                pft_log($this->_errPath, "{$task['id']}|class或action有误, {$class}_{$action}");
                continue;
            }

            //开始导出
            try {
                $run = new $class();
                $run->$action($task);
            } catch (\Exception $e) {
                pft_log($this->_errPath, "{$task['id']}|".$e->getMessage());
                continue;
            }

            pft_log($this->_sucPath, "{$task['id']}|任务生成");
            //释放
            unset($run);
        }
    }
}