<?php
/**
 * 假日模式任务
 * <AUTHOR>
 * @date   2025/04/21
 */

namespace CrontabTasks\Basic;

use Business\PftSystem\VacationModeBiz as VacationModeBiz;
use Library\Tools\Helpers;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class VacationModeTask
{

    /**
     * 执行关闭假期模式的任务
     *
     * @命令：sudo /usr/bin/php /var/www/html/Service/Crontab/runNew.php Basic/VacationModeTask runTask
     *
     * 本函数尝试关闭全局假期模式状态如果关闭操作遇到任何异常，将捕获异常并记录相关信息
     * 如果操作成功，将输出成功消息；如果操作失败，将输出错误消息
     */
    public function runTask()
    {
        $msg = '';
        try {
            // 尝试关闭全局假期模式状态
            $resMsg = (new VacationModeBiz())->closeGlobalStatus();
            if ($resMsg != "") {
                $msg = $resMsg;
            }
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
            // 记录异常日志
            pft_log("vacation_mode_task/error", json_encode([
                'code' => $code,
                'msg'  => $msg,
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ], JSON_UNESCAPED_UNICODE));
        }

        Helpers::sendGroupRobotTextMessages(sprintf("执行关闭假期模式任务完成，返回信息:%s", $resMsg ?? $msg),
            'vacation_mode_close_task_notice');

        // 输出操作结果消息
        echo $resMsg ?? $msg;
    }

}