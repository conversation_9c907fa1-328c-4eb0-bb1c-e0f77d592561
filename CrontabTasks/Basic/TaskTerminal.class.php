<?php
namespace CrontabTasks\Basic;

use Library\Tools\YarClient;

// 权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

/**
 * 终端相关定时任务
 * <AUTHOR>
 * @date   2022-1-5
 */
class TaskTerminal
{

    /**
     * 凌晨自动关闭闸机日志上报
     * User: lanwanhui
     * Date: 2021/1/5
     */
    public function closeLogReport(){
        $client    = new YarClient('face');
        $rs = $client->call('/Device/Device/closeLogWriteTask', []);
        $logData = [
             'msg'=>'凌晨自动关闭闸机日志上报任务',
             'rs' =>   $rs,
        ];
        pft_log('cli_task_terminal_close_report_log', json_encode($logData, JSON_UNESCAPED_UNICODE));
    }

}