<?php
/**
 * 在线支付渠道每日对账功能
 *
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2020-11-24
 * Time: 14:01
 */

namespace CrontabTasks\Basic;

use Business\Finance\OnlinePayment;
use Library\Constants\DingTalkRobots;
use Library\FtpClient\FtpException;
use Library\Model;
use Library\Tools\Helpers;

class OnlinePaymentCheck
{
    private $tbAlipayRecLog = 'pft_alipay_rec';
    private $tbCombineLog = 'pft_orderpay_combine';
    private $tbTradeJournal = 'pft_trade_journal';

    private $sourceTList = [0 => '支付宝', 1 => '微信支付', 20 => '易宝支付'];

    // public function __construct()
    // {
    //     parent::__construct('localhost');
    // }

    private $model = null;
    private function getModel()
    {
        if (is_null($this->model)) {
            $this->model = new Model('localhost');
        }
        return $this->model;
    }

    /**
     * 更新交易流水表里面里面的标识
     *
     * @param $btm
     * @param $etm
     *
     * <AUTHOR>
     * @date 2020-11-24
     */
    private function updateCombineOrder(int $btm, int $etm)
    {
        $map          = [
            'created_at' => ['between', [$btm, $etm]],
            'pay_status' => ['in', [1, 2]],
        ];
        $orderNumList = $this->getModel()->table($this->tbCombineLog)->where($map)->getField("ordernum", true);
        echo $this->getModel()->_sql(), "\n";
        // 更新流水表里面是合并付款的标识
        $this->getModel()->table($this->tbAlipayRecLog)
             ->where(['out_trade_no' => ['in', $orderNumList]])
             ->data(['is_sub_order' => 1])
             ->save();
    }

    /**
     * 线下一卡通充值有bug，独立收款的记录没有记merchant_id
     * @author: Guangpeng Chen
     * @date: 2020/11/25
     *
     * @param  string  $bt  开始时间
     * @param  string  $et  结束时间
     *
     * @return array
     */
    private function updateMerchantId(string $bt, string $et)
    {
        $map  = [
            'rectime'            => ['between', [$bt, $et]],
            'template_item_code' => 1028,
            'daction'            => 0,
            'subject_code'       => ['in', [1201, 1202]],
            'memo'               => ['exp', 'like "%独立收款账户)"'],
        ];
        $data = $this->getModel()->table($this->tbTradeJournal)->where($map)->field('fid,orderid')->select();
        echo $this->getModel()->_sql(), "\n";

        if ($data) {
            $updateData = [];
            foreach ($data as $item) {
                $updateData[$item['fid']][] = $item['orderid'];
            }
            pft_log('pay/journal_check', json_encode($updateData));
            foreach ($updateData as $fid => $orders) {
                $this->getModel()->table($this->tbAlipayRecLog)
                     ->where(['out_trade_no' => ['in', $orders]])
                     ->data(['merchant_id' => $fid])
                     ->save();
            }
        }

        return $data;
    }

    public function startCheck($bt, $et)
    {
        $map  = [
            'dtime'        => ['between', [$bt, $et]],
            'is_sub_order' => 0,
            'status'       => ['in', [1, 2]],
            'merchant_id'  => 0,
            'sourceT'      => ['in', array_keys($this->sourceTList)],
        ];
        $data = $this->getModel()->table($this->tbAlipayRecLog)
                     ->field("sourceT,SUM(total_fee) as total_fee, COUNT(*) AS cnt")
                     ->group('sourceT')
                     ->where($map)
                     ->order('sourceT asc')
                     ->select();
        echo $this->getModel()->_sql();
        if (empty($data)) {
            echo "没有找到数据\n";

            return false;
        }
        $csvFileName = '/tmp/trade_summary_' . date('YmdHi') . '.csv';
        $fp          = fopen($csvFileName, "w+");
        fputcsv($fp, ["交易渠道", "交易金额", "交易笔数"]);
        $dingMsg  = "每日在线支付金额汇总\n支付渠道\t\t交易金额\t\t交易单数\n";
        $tmpCheck = [];
        foreach ($data as $list) {
            $tmpCheck[$list['sourceT']] = $list['total_fee'];
            $list['sourceT']            = $this->sourceTList[$list['sourceT']] . "({$list['sourceT']})";
            $dingMsg                    .= "{$list['sourceT']},{$list['total_fee']},{$list['cnt']}\n";
            fputcsv($fp, $list);
        }
        fclose($fp);
        $dingMsg .= "\n\n自动比对\n\n";
        // get log file
        $yeepayFalg = $wepayFalg = true;
        $onlinePaySum = $this->getBills();
        if (bccomp($onlinePaySum['yeepay']['total_money'],$tmpCheck[20], 2)!=0) {
            $yeepayFalg = false;
            $dingMsg .= "易宝支付：糟糕！账不会平\n实收：{$onlinePaySum['yeepay']['total_money']},"
                        ."交易单数:{$onlinePaySum['yeepay']['total_trades']},"
                        ."差异:" . ($onlinePaySum['yeepay']['total_money'] - $tmpCheck[20]) . "\n";
        } else {
            $dingMsg .= "易宝支付：恭喜，账会平\n";
        }
        if (bccomp($onlinePaySum['wepay']['total_money'], $tmpCheck[1], 2)!=0) {
            $wepayFalg = false;
            $dingMsg .= "微信支付：糟糕！账不会平\n实收：{$onlinePaySum['wepay']['total_money']},"
                        ."交易单数:{$onlinePaySum['wepay']['total_trades']},差异:"
                        . ($onlinePaySum['wepay']['total_money'] - $tmpCheck[1]) . "\n";
        } else {
            $dingMsg .= "微信支付：恭喜，账会平\n";
        }
        $dingMsg .= "\n对账时间段：{$bt}~{$et}\n";
        echo date("m-d H:i:s") . ":汇总结束，csv文件路径:{$csvFileName}\n";
        echo $dingMsg;
        Helpers::sendDingTalkGroupRobotMessageRaw($dingMsg, DingTalkRobots::FINANCE_CHECK);
        if (!$wepayFalg) {
            $this->compareDiffOrders($bt, $et, 1);
        }
        if (!$yeepayFalg) {
            $this->compareDiffOrders($bt, $et, 20);
        }
        return $data;
    }

    public function run()
    {
        $help = "使用方式：\n php Crontab/runNew.php Basic/OnlinePaymentCheck run 开始日期(yyyy-mm-dd) 结束日期(yyyy-mm-dd) \n";
        echo $help;
        echo date("m-d H:i:s") . ":汇总开始\n";
        echo '易宝支付已迁移支付中心，停用';
        return false;
        $yestoday = strtotime("-1 day");
        $params   = $GLOBALS['argv'];
        $bt       = $params[3] ?: date('Y-m-d 00:00:00', $yestoday);
        $et       = $params[4] ?: date('Y-m-d 23:59:59', $yestoday);
        $btm      = strtotime($bt);
        $etm      = strtotime($et);

        $this->updateCombineOrder($btm, $etm);
        $this->updateMerchantId($bt, $et);
        $this->startCheck($bt, $et);
    }

    public function downloadBills()
    {
        $tm          = strtotime("-1 days");
        $wepayAppids = ['wxd1e8494ae3b6d821', 'wxd72be21f7455640d', 'wxe0d4cb8dbc61dbb4'];
        $biz         = new OnlinePayment();
        $dingTalkMsg = "对账单下载任务执行完毕（微信、易宝支付）\n";
        // 易宝
        try {
            $ypCsv = $biz->downloadYeePayBill($tm);
            file_put_contents('/tmp/yeepay.log', $ypCsv);
        } catch (\Exception $e) {
            echo "易宝对账单下载失败:", $e->getMessage();
            $dingTalkMsg .= "易宝下载失败:" . $e->getMessage();
        }
        // 微信支付
        $wpCsv = [];
        foreach ($wepayAppids as $appid) {
            $wpCsv[] = $biz->downloadWepayBill($tm, 'SUCCESS', $appid);
        }
        file_put_contents('/tmp/wepay.log', implode($wpCsv, ','));
        Helpers::sendDingTalkGroupRobotMessageRaw($dingTalkMsg, DingTalkRobots::FINANCE_CHECK);
    }

    public function getBills()
    {
        $biz = new OnlinePayment();
        if (!file_exists('/tmp/yeepay.log') || !file_exists('/tmp/wepay.log')) {
            return false;
        }
        $ypLog = file_get_contents('/tmp/yeepay.log');
        $wpLog = explode(',', file_get_contents('/tmp/wepay.log'));
        $yp    = $biz->summaryYeepayTrade($ypLog);
        $wp    = ['total_money' => 0, 'total_fee' => 0, 'total_trades'=>0];
        foreach ($wpLog as $log) {
            $tmp                = $biz->summaryWepayTrade($log);
            $wp['total_money']  += $tmp['total_money'];
            $wp['total_fee']    += $tmp['total_fee'];
            $wp['total_trades'] += $tmp['total_trades'];
        }

        return ['yeepay' => $yp, 'wepay' => $wp];
    }

    public function compareDiffOrders($bt, $et, $sourceT=1)
    {
        $map     = [
            'dtime'        => ['between', [$bt, $et]],
            'sourceT'      => $sourceT,
            'status'       => ['in', [1, 2]],
            'merchant_id'  => 0,
            'is_sub_order' => 0,
        ];
        $pftOrderNums = $this->getModel()->table($this->tbAlipayRecLog) ->where($map)
                     ->order('id desc')->getField('out_trade_no', true);
        $this->model = null;
        $biz = new OnlinePayment();
        if ($sourceT == 1) {
            $wpLog = explode(',', file_get_contents('/tmp/wepay.log'));
            $remoteOrders = [];
            foreach ($wpLog as $logPath) {
                $remoteOrders = array_merge($remoteOrders, $biz->summaryWepayTrade($logPath, true));
            }
            list($diff1, $diff2) = $this->getDiffOrderNums($pftOrderNums, $remoteOrders);
            $dingMsg = "差异订单\n票付通vs微信支付\n\t数量:".count($diff1)."\n\t订单号:" .implode(',', $diff1)."\n微信支付vs票付通\n\t数量:".count($diff2)."\n\t订单号:" .implode(',', $diff2);
            Helpers::sendDingTalkGroupRobotMessageRaw($dingMsg, DingTalkRobots::FINANCE_CHECK);
        }
        if ($sourceT == 20) {
            $logPath = '/tmp/yeepay.log';
            $remoteOrders = $biz->summaryYeepayTrade($logPath, true);
            list($diff1, $diff2) = $this->getDiffOrderNums($pftOrderNums, $remoteOrders);
            $dingMsg = "差异订单\n票付通vs易宝\n\t数量:".count($diff1)."\n\t订单号:" .implode(',', $diff1)."\n易宝vs票付通:\n\t数量:".count($diff2)."\n\t订单号:" .implode(',', $diff2);
            Helpers::sendDingTalkGroupRobotMessageRaw($dingMsg, DingTalkRobots::FINANCE_CHECK);
        }
        echo $dingMsg;
        return [$diff1, $diff2];
    }
    private function getDiffOrderNums($pftOrderNums, $remoteOrders)
    {
        $diff1 = array_diff($pftOrderNums, $remoteOrders);
        $diff2 = array_diff($remoteOrders, $pftOrderNums);
        return [$diff1, $diff2];
    }
    public function exportTradeDetail()
    {
        $help    = "使用方式：\n php Crontab/runNew.php Basic/OnlinePaymentCheck exportTradeDetail 开始日期(yyyy-mm-dd) 结束日期(yyyy-mm-dd) 支付渠道(int)";
        $params  = $GLOBALS['argv'];
        $bt      = $params[3] ?: exit("开始时间错误\n$help");
        $et      = $params[4] ?: exit("结束时间错误\n$help");
        $sourceT = isset($params[5]) ? $params[5] + 0 : false;
        $map     = [
            'dtime'        => ['between', [$bt, $et]],
            'status'       => ['in', [1, 2]],
            'merchant_id'  => 0,
            'is_sub_order' => 0,
        ];
        if ($sourceT !== false) {
            $map['sourceT'] = $sourceT;
        }
        echo "开始导出数据:\n";
        $data = $this->getModel()->table($this->tbAlipayRecLog)->field('create_time,concat("\t",out_trade_no) as od,concat("\t",trade_no) as trade_no,total_fee,sourceT')
                     ->where($map)
                     ->order('id desc')
                     ->select();
        echo $this->getModel()->_sql();
        if (empty($data)) {
            echo "没有找到数据\n";

            return false;
        }
        $csvFileName = '/tmp/trade_detail_' . $sourceT . '_' . date('YmdHi') . '.csv';
        $fp          = fopen($csvFileName, "w+");
        fputcsv($fp, ["交易时间", "票付通订单号", "支付平台交易号", "交易金额", "交易渠道"]);
        foreach ($data as $list) {
            $list['sourceT'] = $this->sourceTList[$list['sourceT']] . "({$list['sourceT']})";
            fputcsv($fp, $list);
        }
        fclose($fp);
        echo "导出数据结束，csv文件路径:{$csvFileName}\n";
    }
}