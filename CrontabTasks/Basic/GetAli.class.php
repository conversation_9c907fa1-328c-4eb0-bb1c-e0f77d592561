<?php
namespace CrontabTasks\Basic;

class GetAli {

    private $_host    = "http://ali-spot.showapi.com";
    private $_path    = "/level2";
    private $_method  = "GET";
    private $_appcode = "3ca61802142d47bebd00f65882d9c9e9";

    public function getFengJing() {
        $params = $GLOBALS['argv'];
        $host = "http://ali-spot.showapi.com";
        $path = "/spotList";
        $method = "GET";
        $appcode = "3ca61802142d47bebd00f65882d9c9e9";

        $headers = array();
        array_push($headers, "Authorization:APPCODE " . $appcode);

        $page  = 1;
        $proId = $params[3];

        while(true) {
            $querys = "page={$page}&proId={$proId}";
            $bodys = "";
            $url = $host . $path . "?" . $querys;

            $curl = curl_init();
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($curl, CURLOPT_FAILONERROR, false);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HEADER, false);
            if (1 == strpos("$" . $host, "https://")) {
                curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            }
            $content = curl_exec($curl);

            $logRes = $this->log($proId, $page, $content);
            if ($logRes) {
                $str = "成功记录省份{$proId}第{$page}页的信息\n";
                echo $str;
                pft_log('ali_scenic_ok', $str);
            } else {
                $str = "失败记录省份{$proId}第{$page}页的信息\n";
                echo $str;
                pft_log('ali_scenic_fail', $str);
            }

            $data = json_decode($content, true);

            $contentList = $data['showapi_res_body']['pagebean']['contentlist'];

            if (!is_array($contentList) || empty($contentList)) {
                break;
            }

            foreach ($contentList as $item) {
                $insert[] = [
                    'province_id' => $item['proId'],
                    'city_id'     => $item['cityId'],
                    'lon'         => $item['location']['lon'] * 100000000,
                    'lat'         => $item['location']['lat'] * 100000000,
                    'city_name'   => $item['cityName'],
                    'summary'     => $item['summary'],
                    'areaid'      => $item['areaId'],
                    'province_name' => $item['proName'],
                    'area_name'   => $item['areaName'],
                    'address'     => $item['address'],
                    'picList'     => json_encode($item['picList']),
                    'opentime'    => $item['opentime'],
                    'scenic_id'   => $item['id'],
                    'name'        => $item['name'],
                ];
            }

            $model = new \Model\Product\AliCountryScenic();
            $res = $model->insertData($insert);
            unset($insert);

            if ($res) {
                $str = "插入数据库成功：省份{$proId}第{$page}页的信息\n";
                echo $str;
                pft_log('ali_scenic_ok', $str);
            } else {
                $str = "插入数据库失败：省份{$proId}第{$page}页的信息\n";
                echo $str;
                pft_log('ali_scenic_fail', $str);
            }

            $totalPage = $data['showapi_res_body']['pagebean']['allPages'];

            if ($page >= $totalPage) {
                break;
            }

            $page++;
        }

    }

    public function log($id, $page, $content) {
        $tmpPath  = BASE_LOG_DIR . '/ali/';
        $fileName = $id . '_' . $page . '.log';

        //如果文件不存在，就创建文件
        if (!file_exists($tmpPath)) {
            $res = mkdir($tmpPath, 0777, true);
            if (!$res) {
                return false;
            }
        }

        $content = $content . "\r\n";
        $fileName = $tmpPath . $fileName;

        $res = @file_put_contents($fileName, $content, FILE_APPEND);
        if ($res) {
            return true;
        } else {
            return false;
        }
    }

}