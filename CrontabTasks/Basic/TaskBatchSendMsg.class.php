<?php
namespace CrontabTasks\Basic;

use Library\Controller as Controller;
use Library\Resque\Queue as Queue;
use Library\Tools as Tools;
use Library\Constants\Table\MainTableConst;

/**
 * 批量推送消息
 *
 * <AUTHOR>
 * @date   2017-04-14
 */
class TaskBatchSendMsg extends Controller
{
    private $_logPath       = 'task/batchSendMsg';
    private $_maxNum        = 500;  // 任务每次运行处理的最大记录
    private $_taskMsgModel  = null;

    public function __construct()
    {
        set_time_limit(0);
        $this->_taskMsgModel = new \Model\Notice\Task();
    }

    /**
     * 动态推送批量消息
     * 礼券过期/生日祝福
     * <AUTHOR>
     * @time   2017-05-08
     */
    public function runBatPushDayTask()
    {
        // 自动检测并修复异常数据.
        //用户二期 - 信息获取修改
        $this->_batParseIdcardBirth();

        // 当日修改 动态推送任务，次日生效！
        $taskArr = $this->_getDynamicPushTaskList();
        if (!$taskArr) {
            exit('没有要执行的数据');
        }

        $cardModel   = new \Model\Product\MemberCard('remote_1');
        $couponModel = new \Model\Order\Coupon();

        $mModel      = $this->_getMemberModel();
        $wxModel     = $this->_getWxModel();
        $wxOpenModel = new \Model\Wechat\WxOpen();

        // 批量获取账户余额
        $aidMoney = $mModel->getMoneyBatch(array_unique(array_column($taskArr, 'fid')));

        // 循环执行任务
        $count = 0; // 消息累计发送次数
        foreach ($taskArr as $task) {

            $aid      = $task['fid']; // 任务发起者
            $formData = json_decode($task['data'], true); // 解析任务配置信息
            if (!$aid || !$formData) {
                continue;
            }

            // 开始执行
            $title      = $formData['title'];      // 消息名称
            $content    = $formData['content'];    // 消息内容[不包括模板]
            $msgType    = $formData['msgType'];    // 消息类型[0通用1生日2礼券]
            $recType    = $formData['recType'];    // 接收类型[0系统自动筛选1指定会员]
            $channel    = $formData['channel'];    // 推送渠道[0微信1短信(默认0)可多选]
            $sendType   = $formData['sendType'];   // 推送类型[0立即推送1定时推送2动态推送]
            $sendTime   = $formData['sendTime'];   // 发送时间[立即推送0，定时推送日期精确到分，动态推送单位天数]
            $saveType   = $formData['saveType'];   // 保存类型[0保存1保存并执行]
            $excelUrl   = $formData['excelUrl'];   // xls的URL地址
            unset($formData);

            // 可执行校验 + 动态推送校验
            if ($saveType != 1 || $sendType != 2) {
                continue;
            }

            // 渠道校验
            $wxChannel   = 0; // 微信渠道 [0关闭，1开启]
            $smsChannel  = 0; // 短信渠道 [0关闭，1开启]

            if (in_array(0, $channel)) {
                $wxChannel = 1;
            }

            if (in_array(1, $channel)) {
                $smsChannel = 1;
            }

            if (!$wxChannel && !$smsChannel) {
                continue;
            }

            // 计算指定日期
            $maxAllowDays = 90; // 通知提前天数上限 [>=1]
            $minAllowDays = 1;  // 通知提前天数下限 [>=1]

            if ($sendTime > $maxAllowDays || $sendTime < $minAllowDays) {
                // 动态推送 设定时间非法
                continue;
            }

            // 指定日期
            $tm         = time() + 86400 * $sendTime;
            $date       = date('Y-m-d', $tm);
            $birthday   = date('md', $tm);

            // 不同类型的数据获取
            $type = ''; // 消息类型名称
            if ($msgType == 2) {  // 礼券消息

                $type = '礼券消息';

                // 获取有效期截止到指定日期的礼券
                $res = $couponModel->getDateExpireCoupon($aid, $date);
                if (!$res) {
                    continue;
                }

                $fidCidMap = []; // 用户ID和优惠券ID的映射
                $cidArr    = []; // 优惠券ID集合
                foreach ($res as $item) {

                    $fid  = $item['fid'];       // 优惠券所属用户ID
                    $cid  = $item['coupon_id']; // 优惠券ID
                    $num  = $item['num'];

                    if (!$fid || !$cid) {
                        continue;
                    }

                    if (!isset($fidCidMap[$fid]) || !array_key_exists($cid, $fidCidMap[$fid])) {
                        $fidCidMap[$fid][$cid] = $num;
                    }

                    if (!in_array($cid, $cidArr)) {
                        $cidArr[] = $cid;
                    }
                }
                unset($res);

                // 批量获取优惠券信息
                $field  = 'id, coupon_name as name';
                $couponInfo = $couponModel->getCouponInfoByBatchId($cidArr, $field, true);

                $allUserId = array_keys($fidCidMap); 
                if (!$allUserId) {
                    continue;
                }

            } elseif ($msgType == 1) { // 生日祝福消息

                $type = '生日祝福';

                // 获取指定生日的用户
                $allUserId = $this->_getBirthUserIdArr($birthday);

                // 批量取得会员卡用户信息 
                $res = $this->_getMemberCardInfo($aid, $allUserId, true);
                if ($res && is_array($res)) {
                    $cardInfoMap  = $res['cardInfoMap'];
                    $allUserId    = $res['wxUserIdArr']; // 会员ID
                    unset($res);
                }

                if (!$allUserId) {
                    continue;
                }

            } else {  // 非法类型
                continue;
            }

            // 批量获取用户信息
            $allUserInfo  = $mModel->getMemberInfoByMulti(
                    $allUserId, 'id', 'dname,mobile,id', true
                );

            // 微信渠道相关数据
            if ($wxChannel) {

                $wxUserIdArr = []; // 符合微信发送条件的用户ID集合
                $creditMoney = []; // 批量用户的授信余额

                $appInfo = $wxOpenModel->getAppId($aid, 'fid,appid');

                if ($appId = $appInfo['appid']) {

                    if ($msgType == 2) {

                        // 批量取得会员卡用户信息
                        $cardInfoMap = []; // 会员卡信息
                        $res = $this->_getMemberCardInfo($aid, $allUserId, true);
                        if ($res && is_array($res)) {
                            $cardInfoMap   = $res['cardInfoMap'];
                            $wxUserIdArr   = $res['wxUserIdArr'];
                            unset($res);
                        }

                    } elseif ($msgType == 1) {
                        $wxUserIdArr = $allUserId;
                    }

                    if ($wxUserIdArr) {
                        // 用户Openid集合
                        $wxBindOpenidMap = $this->_getWxBindOpenidMap($appId, $wxUserIdArr);
                        if ($wxBindOpenidMap && is_array($wxBindOpenidMap)) {
                            $wxUserIdArr = array_keys($wxBindOpenidMap);

                            // 批量获得任务发起者给微信接收者的授信余额
                            $creditMoney = $mModel->getCreditMondyBatch($wxUserIdArr, $aid);
                        }
                    }
                }

            }

            // 逐个用户发送
            $wxRecUserId  = [];     // 实际微信接收用户ID
            $smsRecUserId = [];     // 实际短信接收用户ID
            $enoughMoney  = true;   // 账户余额充足true | false则不足
            foreach ($allUserId as $userId) {

                $userInfo = $allUserInfo[$userId];
                if (!$userInfo) {
                    continue;
                }

                // 根据当前任务的消息类型，取得模板内容
                $tplContent = $this->_getMsgContent(
                        $msgType,
                        $aid,
                        $userInfo['id'],
                        $userInfo['dname'],
                        $cardInfoMap[$userId]['card_no'] ?: '',
                        $fidCidMap[$userId] ?: [],
                        $date,
                        $couponInfo ?: []
                    );

                // 不符合发送条件，跳过发送
                if ($tplContent === false) {
                    continue;
                }

                // 消息内容
                $remarkData = str_replace('{content}', $content, $tplContent);

                // 准备发送
                $wxDataArr  = [];
                $smsDataArr = [];

                // 微信
                if ($wxChannel && in_array($userId, $wxUserIdArr)) {

                    $wxRecUserId[] = $userInfo['dname'] . '(ID:' . $userId . ')';

                    $tData = [];

                    // 获得微信模板各项数据 [会员卡号 手机号 昵称 授信余额]
                    $tData['taskId']  = $task['id']; 
                    $tData['fid']     = $userId;
                    $tData['dname']   = $userInfo['dname'] ?: '';                // 用户名
                    $tData['phone']   = $userInfo['mobile'] ?: '';               // 手机号
                    $tData['cardNo']  = $cardInfoMap[$userId]['card_no'] ?: '';  // 会员卡号
                    $tData['first']   = $title;                                  // 内容头部
                    $tData['remark']  = $remarkData;                             // 内容底部
                    $tData['level']   = '普通';                                  //
                    $tData['money']   = '¥0.00';                                 // 授信额度
                    if (isset($creditMoney[$userId])) {
                        $tData['money'] = '¥'.sprintf(
                           '%.2f', 
                           ($creditMoney[$userId]['basecredit'] + $creditMoney[$userId]['kmoney'] + 0) / 100
                       );
                    }

                    $wxDataArr[] = $tData;

                    // 发送微信消息
                    $this->_sendWxMsg($appId, $wxBindOpenidMap, $wxDataArr);
                }

                // 短信
                if ($smsChannel) {

                    // 账户余额是否足够支付短信费用
                    $leftMoney = $this->_checkSmsMoney($remarkData, $aid, $aidMoney[$aid]);

                    if ($userInfo['mobile'] && ismobile($userInfo['mobile']) && $leftMoney !== false) {
                        $smsRecUserId[] = $userInfo['dname'] . '(ID:' . $userId . ')';

                        // 虚拟扣费
                        $aidMoney[$aid] = $leftMoney;

                        // 发短信
                        $smsDataArr[] = [
                            'mobile'    => $userInfo['mobile'],
                            'content'   => $remarkData,
                            'aid'       => $aid,
                            'fid'       => $userId,
                            'taskid'    => $task['id']
                        ];
                        $this->_sendSmsMsg($smsDataArr);

                    } elseif ($leftMoney === false) {
                        // 余额不足提醒
                        $enoughMoney = false;
                    }

                }

                // 休息一会, 防止占用队列
                if ($smsDataArr || $wxDataArr) {
                    $count++;
                }

                if ($count > $this->_maxNum) {
                    sleep(3*60); // 3min
                    $count = 0;
                }
            }

            // 单条任务处理完毕
            $result = Task::updateUserTask(
                    ['id' => $task['id']],
                    ['last_run_time' => time()]
                );

            // 单条任务处理完毕 发送消息
            Task::sendNotify([
                'memberID'  => $aid,
                'title'     => "动态推送[{$title}]通知",
                'content'   => "今日推送{$type}[{$title}][{$task['id']}]详情：".
                                '<br/>已发送微信通知'.count($wxRecUserId).'人:'.implode(',', $wxRecUserId).
                                '<br/>已发送短信通知'.count($smsRecUserId).'人:'.implode(',', $smsRecUserId).
                                ($enoughMoney ? '' : '[账户余额不足，短信未全部申请发送]')
            ]);

            // if (false === $result) {
            //     $log = '任务[编号'.$task['id'].'-批量消息推送]处理失败';
            //     @pft_log($this->_logPath, $log);
            // }
        }
        exit('本日完成');
    }

    /**
     * 定时推送批量消息
     * 通用消息/生日祝福
     * <AUTHOR>
     * @time   2017-04-14
     */
    public function runBatPushTimeTask()
    {
        // 等待发送的消息列表
        $msgArr = $this->_getWaitSendMsgList();
        if (!$msgArr) {
            exit('没有要执行的数据');
        }

        // 公用数据
        $allTaskId = []; // 任务ID集合
        $allUserId = []; // 用户ID集合

        // 批量得到任务数据
        $allTaskId    = array_unique(array_column($msgArr, 'task_id'));
        $allTasksInfo = Task::getUserTasks(3, 0, $allTaskId, true);

        // 批量得到会员信息
        $memberModel  = $this->_getMemberModel();
        $allUserId    = array_unique(array_column($msgArr, 'fid'));
        $data         = array_column($msgArr, 'data','fid');
        $excelData    = [];
        foreach ($data as $k=>$values){
            $excelData[$k]=json_decode($values,true);
        }
        if ($allUserId != [0]) {
            $allUserInfo  = $memberModel->getMemberInfoByMulti($allUserId, 'id', 'dname,mobile,id', true);
        }

        $smsTaskIdArr = []; // 短信任务ID集合
        $wxTaskIdArr  = []; // 微信任务ID集合
        $taskMsgArr   = []; // 任务内容[按类型分组]
        $wxAidFidArr  = []; // 微信消息接收者ID数组[按消息发布者ID分组]
        $smsAidFidArr = []; // 短信消息接收者ID数组[按消息发布者ID分组]
        $smsMsgIdArr  = []; // 短信消息ID集合
        $wxMsgIdArr   = []; // 微信消息ID集合
        $allMsgIdArr  = []; // 所有任务ID集合

        // 分离任务
        foreach ($msgArr as $msg) {

            // 短信任务
            if ($msg['send_sms'] == 1) {

                $smsMsgIdArr[] = $msg['id'];

                if (!in_array($msg['task_id'], $smsTaskIdArr)) {
                    $smsTaskIdArr[] = $msg['task_id'];
                }

                // 短信消息列表
                $taskMsgArr['sms'][$msg['task_id']][] = $msg;

                // 接收短信的用户ID
                if ($msg['fid'] && $msg['aid']) {
                    if (!$smsAidFidArr[$msg['aid']] || !in_array($msg['fid'], $smsAidFidArr[$msg['aid']])) {
                        $smsAidFidArr[$msg['aid']][] = $msg['fid'];
                    }
                }
            }

            // 微信任务
            if ($msg['send_wx'] == 1) {

                // 微信任务ID集合
                if (!in_array($msg['task_id'], $wxTaskIdArr)) {
                    $wxTaskIdArr[] = $msg['task_id'];
                }

                // 微信消息列表
                $taskMsgArr['wx'][$msg['task_id']][] = $msg;

                // 同一操作者下的所有fid
                if (!$wxAidFidArr[$msg['aid']] || !in_array($msg['fid'], $wxAidFidArr[$msg['aid']])) {
                    $wxAidFidArr[$msg['aid']][] = $msg['fid'];
                }

                // 微信消息ID集合
                $wxMsgIdArr[] = $msg['id'];
            }

        }

        // 所有任务
        $allMsgIdArr =  array_column($msgArr, 'id');
        unset($msgArr);

        // 任务运行锁
        $this->_changeMsgStatus('running', $allMsgIdArr);

        // 存在发送短信的任务
        if ($smsTaskIdArr) {

            // 短信任务锁定：异常情况下，不重发短信，以免占用队列 和 重复收费
            $this->_changeMsgStatus('sms', $smsMsgIdArr, 1, 2);

            // 循环短信任务列表
            foreach ($smsTaskIdArr as $taskId) {

                // 获取当前任务表单配置参数
                $taskInfo = $allTasksInfo[$taskId];
                if (!$taskInfo) {
                    continue;
                }

                // 获取当前任务表单配置参数
                $formData = json_decode($taskInfo['data'], true);

                // 批量取得会员卡用户信息
                if ($smsAidFidArr[$taskInfo['fid']]) {
                    $res = $this->_getMemberCardInfo($taskInfo['fid'], $smsAidFidArr[$taskInfo['fid']], false);
                    if ($res === false) {
                        continue;
                    } else {
                        $smsCardInfoMap = $res['cardInfoMap'];
                        unset($res);
                    }
                }

                // 短信模板数据
                $sendDate   = [];
                $smsDataArr = [];

                // 循环当前任务对应的待发送短信消息列表
                foreach ($taskMsgArr['sms'][$taskId] as $smsMsg) {

                    $userId = $smsMsg['fid']; // 接收消息的用户ID 

                    // 跳过 异常情况下 没有 短信发送者的表数据
                    if (!$smsMsg['aid']) {
                        continue;
                    }

                    // 解析当前消息的excel内容
                    $excelData = json_decode($smsMsg['data'], true);

                    // 短信模板内容
                    $mobile      = 0;   // 接收者手机号   
                    $remarkData  = '';  // 消息内容

                    if (!$userId) {
                        // 发短信给非平台用户
                        if (!$excelData['mobile'] || !ismobile($excelData['mobile'])) {
                            continue;
                        }

                        $mobile     = $excelData['mobile']; // 手机号

                        // 根据当前任务的消息类型，取得短信模板内容
                        $tplContent = $this->_getMsgContent($formData['msgType'], $smsMsg['aid'], 1);

                    } else {

                        // 发短信给已绑定手机号的平台用户
                        $userInfo   = $allUserInfo[$userId]; // 接收消息的用户数据
                        if ($userInfo['mobile'] && ismobile($userInfo['mobile'])) {
                            $mobile = $userInfo['mobile'];
                        }

                        // 表格导入的手机号 优先
                        if ($excelData['mobile'] && ismobile($excelData['mobile'])) {
                            $mobile = $userInfo['mobile'];
                        }

                        // 过滤无效手机号
                        if (!$mobile){
                            continue;
                        }

                        // 根据当前任务的消息类型，取得短信模板内容
                        $tplContent = $this->_getMsgContent(
                                $formData['msgType'], 
                                $smsMsg['aid'],
                                $userInfo['id'],
                                $userInfo['dname'],
                                $smsCardInfoMap[$userId]['card_no'] ?: ''
                            );
                    }

                    // 不符合发送条件
                    if ($tplContent === false) {
                        continue;
                    }

                    // 消息内容替换 [excel导入内容优先]
                    $excelContent = $this->_getExcelContent($smsMsg['data']);
                    if (!$excelContent){
                        // 使用表单内容
                        $remarkData = str_replace('{content}', $formData['content'], $tplContent);

                    } else {
                        // 使用excel内容
                        $remarkData = str_replace('{content}', $excelContent, $tplContent);
                    }

                    // 短信模板参数
                     $sendDate['mobile']    = $mobile;
                     $sendDate['content']   = $remarkData;
                     $sendDate['aid']       = $smsMsg['aid'];       // 发送者
                     $sendDate['taskId']    = $smsMsg['task_id'];   // 任务表ID
                     $sendDate['msgId']     = $smsMsg['id'];        // 消息表ID
                     $sendDate['fid']       = $smsMsg['fid'];       // 接收者ID

                     $smsDataArr[] = $sendDate;

                     unset($sendDate);
                }

                // 调用封装的短信发送方法
                $this->_sendSmsMsg($smsDataArr);

                unset($smsDataArr);
            }
            unset(
                $taskMsgArr['sms'],
                $tplContent,
                $taskInfo,
                $formData,
                $userInfo,
                $remarkData
            );
        }

        // 存在发送微信的任务
        if ($wxTaskIdArr && count($wxAidFidArr)) {

            // 批量获取所有发送者appid
            $wxAidArr   = array_keys($wxAidFidArr);

            $wxOpenModel = new \Model\Wechat\WxOpen();
            $allAppInfo = $wxOpenModel->getAppId($wxAidArr, 'fid,appid', true);

            if (!$allAppInfo) {
                exit('平台账户['.implode(',', $wxAidArr).']均未授权公众号');
            } else {
                unset($wxAidArr);
            }

            // 发送者id和appid映射处理
            $temp = [];
            foreach ($allAppInfo as $item) {
                $temp[$item['fid']] = $item['appid'];
            }
            $allAppInfo = $temp;
            unset($temp);

            // 循环微信任务列表
            foreach ($wxTaskIdArr as $taskId) {

                // 任务详情
                $taskInfo = $allTasksInfo[$taskId];
                if (!$taskInfo) {
                    continue;
                }

                // 获取当前任务表单配置参数
                $formData = json_decode($taskInfo['data'], true);

                // 任务发起者ID
                $aid = $taskInfo['fid'];

                // 任务发起者的公众号appid
                $appId = $allAppInfo[$aid];

                // 本次任务要发送的微信接收者ID集合
                $wxUserIdArr = $wxAidFidArr[$aid];

                // 绑定任务发起者公众号的Openid集合
                $wxBindOpenidMap = $this->_getWxBindOpenidMap($appId, $wxUserIdArr);
                if ($wxBindOpenidMap === false) {
                    continue;
                } else {
                    // 获得已绑定微信发送者公众号的用户ID集合
                    $wxUserIdArr = array_keys($wxBindOpenidMap);
                }

                // 会员卡用户信息
                $res = $this->_getMemberCardInfo($aid, $wxUserIdArr, true);
                if ($res === false) {
                    continue;
                } else {
                    $cardInfoMap = $res['cardInfoMap'];
                    $wxUserIdArr = $res['wxUserIdArr'];
                    unset($res);
                }

                // 此处可添加更多过滤......

                // 批量获得任务发起者给微信接收者的授信余额
                $creditMoney = $memberModel->getCreditMondyBatch($wxUserIdArr, $aid);

                // 微信模板数据
                $tData      = [];
                $wxDataArr  = [];

                // 循环当前任务对应的待发送微信消息列表
                foreach ($taskMsgArr['wx'][$taskId] as $wxMsg) {

                    $userId     = $wxMsg['fid'];         // 接收消息的用户ID 
                    $userInfo   = $allUserInfo[$userId]; // 接收消息的用户数据

                    // 跳过无效微信接收者
                    if (!in_array($userId, $wxUserIdArr)) {
                        continue;
                    }

                    // 根据当前任务的消息类型，取得微信内容
                    $tplContent = $this->_getMsgContent(
                            $formData['msgType'], 
                            $wxMsg['aid'],
                            $userInfo['id'],
                            $userInfo['dname'],
                            $cardInfoMap[$userId]['card_no'] ?: ''
                        );

                    // 不符合发送条件，跳过发送
                    if ($tplContent === false) {
                        continue;
                    }

                    // 消息内容替换
                    $excelContent = $this->_getExcelContent($wxMsg['data']);
                    if (!$excelContent){
                        // 使用表单内容
                        $remarkData = "\n\n".$formData['content'];
                    } else {
                        // 使用excel内容
                        $remarkData = "\n\n".$excelContent;
                    }

                     // 获得微信模板各项数据 [会员卡号 手机号 昵称 授信余额]
                     $tData['taskId']  = $taskId;
                     $tData['mgsId']   = $wxMsg['id'];
                     $tData['fid']     = $userId;
                     $tData['dname']   = $userInfo['dname'] ?: '';                // 用户名
                     $tData['phone']   = $userInfo['mobile'] ?: '';               // 手机号
                     $tData['cardNo']  = $cardInfoMap[$userId]['card_no'] ?: '';  // 会员卡号
                     $tData['first']   = $formData['title']."\n";                      // 内容头部
                     $tData['remark']  = $remarkData;                             // 内容底部
                     $tData['landName']= $excelData[$userId]['landName'];         // 景区名称
                     $tData['useTime']= $excelData[$userId]['useTime'];         // 使用时间


                     $wxDataArr[] = $tData;
                }

                // 批量发送微信消息
                $this->_sendWxMsg($appId, $wxBindOpenidMap, $wxDataArr);

                unset(
                    $wxBindOpenidMap,
                    $wxDataArr,
                    $tData
                );
            }

            // 微信任务锁： 异常情况下，可重发微信
            $this->_changeMsgStatus('wx', $wxMsgIdArr, 1, 2);
        }

        // 本轮任务处理完成
        $this->_changeMsgStatus('finish', $allMsgIdArr);

        // 任务状态修改
        $taskIdArr = array_merge($smsTaskIdArr, $wxTaskIdArr);
        if ($taskIdArr) {
            $this->_changeTaskStatus($taskIdArr);
        }

        exit('本轮完成');
    }

    /**
     * 验证账户余额是否足够支付短信费
     * <AUTHOR>
     * @time   2017-05-23
     *
     * @param  string $content        短信内容
     * @param  int    $aid            扣费用户ID
     * @param  int    $accountMoney   扣费用户账户余额
     * @return mixed  [返回剩余金额|false说明余额不足]
     */
    private function _checkSmsMoney($content = '', $aid = 0, $accountMoney = 0)
    {
        if (!$content || !$aid || !$accountMoney) {
            return false;
        }

        $api = new \Business\JavaApi\Member\MemberConfig();
        $cfgRes = $api->getConfigWithMemberId($memberId);
        if ($cfgRes['code'] != 200 || !$cfgRes['data']) {
            return false;
        }

        $fee_sms = $cfgRes['data']['fee_sms'];
        $dmoney  = $fee_sms * ceil(mb_strlen($content)/67);

        $leftMoney = $accountMoney - $dmoney;

        return $leftMoney >= 0 ? $leftMoney : false;
    }

    /**
     * 判断任务是否全部完成
     * <AUTHOR>
     * @date   2017-05-15
     *      
     * @param  array   $taskIdArr   任务ID数组[1001,1002,...]
     * @return bool
     */
    private function _changeTaskStatus($taskIdArr = [])
    {
        if (!$taskIdArr || !is_array($taskIdArr)) {
            return false;
        }

        $where = ['status' => 1];

        foreach ($taskIdArr as $key => $taskId) {
            // 是否存在未执行完的任务
            $where['task_id'] = $taskId;
            $res = $this->_taskMsgModel->getBatSendTaskDetail($where, 'id', 1, 'send_time desc');
            if ($res) {
                unset($taskIdArr[$key]);
            }
        }

        if ($taskIdArr) {
            Task::updateUserTask(
                    ['id' => ['IN', array_values($taskIdArr)]],
                    ['status' => 2, 'last_run_time'=>time()]
                );
        }

        return true;
    }

    /**
     * 消息记录的执行状态修改
     * <AUTHOR>
     * @date   2017-04-25
     *      
     * @param  string  $channel    发送渠道标志 [wx|sms]
     * @param  array   $msgIdArr   消息记录ID数组[1001,1002,...]
     * @param  int     $oldStatus  修改前的状态标志
     * @param  int     $newStatus  修改后的状态标志 [null则不修改状态]
     * @return bool
     */
    private function _changeMsgStatus($channel = '', $msgIdArr = [], $oldStatus = 1, $newStatus = null)
    {
        if (!in_array($channel, ['wx', 'sms', 'running', 'finish']) ||
            !$msgIdArr ||
            !is_array($msgIdArr) ||
            !is_numeric($oldStatus))
        {
            return true;
        }

        // 消息的运行状态 [运行中/完成]
        if (in_array($channel, ['running', 'finish'])) {
            $where = [
                'id' => ['IN', $msgIdArr]
            ];

            if ($channel == 'running') {
                $data  = [
                    'status' => 3
                ];

            } elseif ($channel == 'finish') {
                $data  = [
                    'status' => 2
                ];
            }

            $this->_taskMsgModel->updateBatSendTaskDetail($where, $data);
            return true;
        }

        // 消息的不同发送渠道 [微信/短信]
        if ($channel == 'wx') {
            $field = 'send_wx';
            $time  = 'send_wx_time';

        } elseif ($channel == 'sms') {
            $field = 'send_sms';
            $time  = 'send_sms_time';
        }

        $where = [
            'id'        => ['IN', $msgIdArr],
            $field  => $oldStatus
        ];

        // 更新时间
        $data  = [
            $time  => time()
        ];

        // 更新状态
        if (!is_null($newStatus) && is_numeric($newStatus)) {
            $data[$field] = $newStatus;
        }

        $res = $this->_taskMsgModel->updateBatSendTaskDetail($where, $data);

        return true;
    }

    /**
     * 等待发送的消息列表
     * <AUTHOR>
     * @date   2017-04-12
     *      
     * @param  int    $endTime  时间戳 获得小于此时间的待发送消息记录
     * @param  int    $limit    要获取的消息记录数量限制
     * @return array
     */
    private function _getWaitSendMsgList($endTime = 0, $limit = 0)
    {
        if (!is_numeric($endTime) || !$endTime) {
            $endTime = time();
        }

        if (!is_numeric($limit) || !$limit) {
            $limit = $this->_maxNum;
        }
        // 如果不加时间段限制的话数据库查询太慢了，没必要
        $sendTimeStart = strtotime("-10 days");
        $where = [
            'status'    => 1,
            'send_time' => ['between', [$sendTimeStart,$endTime]],
            '_string'   => 'send_sms=1 or send_wx=1'
        ];
        $field = 'id,fid,aid,task_id,send_time,send_sms,send_sms_time,send_wx,send_wx_time,status,level,msg_type,comment,data';
        $order  = 'level asc, send_time asc, task_id asc ';
        $msgArr = $this->_taskMsgModel->getBatSendTaskDetail($where, $field, $limit, $order);

        return $msgArr ?: [];
    }

    /**
     * 动态推送的已启动任务列表
     * <AUTHOR>
     * @date   2017-04-12
     * @return array
     */
    private function _getDynamicPushTaskList()
    {
        $taskArr = Task::getUserTasks(4, 0, [], false, [1]);
        return $taskArr ?: [];
    }

    /**
     * 发送微信通知
     * <AUTHOR>
     * @date   2017-04-12
     * 
     * @param  string    $appId         公众号appid
     * @param  array     $wxBindInfo    用户ID和OPENID映射数组 [[3385=>'32..fws'],...]
     * @param  array     $wxDataArr     微信模板数据数组
     * @return bool
     */
    private function _sendWxMsg($appId = '', $wxBindInfo = [], $wxDataArr = [])
    {
        if (!is_array($wxDataArr) || !$wxDataArr || !$appId) {
            return true;
        }

        // 循环发送
        foreach ($wxDataArr as $wxData) {

            $fid  = $wxData['fid'];

            $data = [
                'first'     => ['value' => $wxData['first'], 'color' => '#173177'],
                'keyword1'  => ['value' => $wxData['landName'], 'color' => '#ff0000'],
                'keyword2'  => ['value' => $wxData['useTime'], 'color' => '#173177'],
                //'keyword3'  => ['value' => $wxData['phone'], 'color' => '#173177'],
                'keyword3'  => ['value' => $wxData['dname'].'-'.$wxData['cardNo'], 'color' => '#173177'],
                // 'keyword4'  => ['value' => $wxData['landName'], 'color' => '#ff0000'],
                //'keyword5'  => ['value' => $wxData['useTime'], 'color' => '#173177'],
                'remark'    => ['value' => $wxData['remark'], 'color' => '#ff0000']
            ];

            if (is_array($wxBindInfo[$fid]) && $wxBindInfo[$fid]) {
                foreach ($wxBindInfo[$fid] as $tOpenid) {
                    $modelData =  [
                        'appid'     => $appId,
                        'openid'    => $tOpenid,
                        'data'      => $data,
                        'tplid'     => 'BATSEND',
                        'color'     => '#FF0000',
                        'url'       => '',
                        'params'    => [
                            'fid'     => $wxData['fid'],
                            'taskId'  => $wxData['taskId'],
                            'mgsId'   => $wxData['mgsId'],
                        ]

                    ];

                    // 发送
                    $jobId = Queue::push('batwxnotify', 'WxNotify_Job', $modelData);

                    // 本地打印
                    if (ENV != 'PRODUCTION') {
                        pft_log('queue/batpush/wx', 'jobId:' . $jobId, "month");
                        pft_log('queue/batpush/wx', "批量推送微信|" .  json_encode($modelData, JSON_UNESCAPED_UNICODE));
                    }
                }
            }
        }

        return true;
    }

    /**
     * 发送短信通知
     * <AUTHOR>
     * @date   2017-05-05
     * 
     * @param  array  $smsDataArr  用户ID数组 [3385,55...]
     * @return bool
     */
    private function _sendSmsMsg($smsDataArr = [])
    {
        if ($smsDataArr && is_array($smsDataArr)) {

            foreach ($smsDataArr as $smsData) {

                // 发送
                $jobId = Queue::push('batsmsnotify', 'SmsNotify_Job',  [
                    'mobile'    => $smsData['mobile'],
                    'action'    => 'batSendMsg',
                    'params'    => [
                        'content' => $smsData['content'] ?: '',
                        'aid'     => $smsData['aid'] ?: 0,
                        'fid'     => $smsData['fid'] ?: 0,
                        'taskId'  => $smsData['taskId'] ?: 0,
                        'msgId'   => $smsData['msgId'] ?: 0
                    ],
                ]);

                // 本地打印
                if (ENV != 'PRODUCTION') {
                    pft_log('queue/batpush/sms', 'jobId:' . $jobId, "month");
                    pft_log('queue/batpush/sms', "批量推送短信|" .  json_encode($smsData, JSON_UNESCAPED_UNICODE));
                }
            }
        }

        return true;
    }

    /**
     * 获得绑定指定公众号的平台用户的openid，并按用户ID分组返回
     * <AUTHOR>
     * @date   2017-04-25
     * 
     * @param  string    $appId                 指定公众号appid
     * @param  array     $wxUserIdArr           用户ID数组 [3385,55...]
     * @return array/bool [无匹配false|['fid'=>[openid1,openid2,...], 'fid2'=>[openid1,openid2,...],...]    
     */
    private function _getWxBindOpenidMap($appId = '', $wxUserIdArr = [])
    {
        if (!is_string($appId) || !$appId || !is_array($wxUserIdArr) || !$wxUserIdArr) {
            return false;
        }

        // 绑定指定公众号的Openid集合
        $wxBindInfo = $this->_getWxModel()->getBatchWxInfo($appId, $wxUserIdArr);
        if (!$wxBindInfo) {
            return false;
        }

        // openid 按 fid 分组
        $wxBindMap = [];
        foreach ($wxBindInfo as $item) {
            // 过滤掉没有 openid 的问题用户
            if ($item['fromusername']) {
                $wxBindMap[$item['fid']][] = $item['fromusername'];
            }
        }

        return $wxBindMap ?: false;
    }

    /**
     * 得到绑定会员卡的用户
     * <AUTHOR>
     * @date   2017-04-25
     * 
     * @param  int       $aid                   会员卡供应商ID
     * @param  array     $wxUserIdArr           用户ID数组 [3385,55...]
     * @param  bool      $mustMemberCardUser    是否必须是会员卡用户[默认true是]
     * @return array/bool [没有符合的条件false|执行成功返回关联数组]    
     */
    private function _getMemberCardInfo($aid = 0, $wxUserIdArr = [], $mustMemberCardUser = true)
    {
        if (!is_array($wxUserIdArr) || !$wxUserIdArr) {
            return false;
        }

        // 会员卡用户信息
        $cardModel   = $this->_getMemberCardModel();
        $cardInfoMap = $cardModel->getBatchMemberCardInfo(
            $aid, ['memberID' => ['in', $wxUserIdArr]]
        );

        // 必须是会员卡用户   
        if ($mustMemberCardUser) {
            if (!$cardInfoMap) {
                return false;
            }
            $wxUserIdArr = array_unique(array_keys($cardInfoMap));
        }

        $return = [
            'wxUserIdArr'   => $wxUserIdArr,
            'cardInfoMap'   => $cardInfoMap
        ];

        return $return;
    }

    /**
     * 获取消息模板内容
     * <AUTHOR>
     * @date   2017-04-25
     * 
     * @param  int/string   $msgType      消息类型 [0,1,2]
     * @param  int          $aid          供应商ID
     * @param  int          $userId       用户ID
     * @param  string       $userDName    用户dname
     * @param  array        $cidNumMap    当前用户对于的礼券Id和数量映射[77=>1, 88=>2, ...]
     * @param  string       $date         礼券过期时间, 配合cidNumMap使用。[例如：2017-04-05]
     * @param  array        $couponInfo   礼券信息数组，配合cidNumMap使用。[77=>['id'=>77,'name'=>'xx优惠券'], ...]
     * @return bool         false参数错误或不符合消息类型发送者
     */
    private function _getMsgContent($msgType = 0, $aid = 0, $userId = 0, $userDName = '', $cardno = '', $cidNumMap = [], $date = '', $couponInfo = [])
    {
        if (
            !$userId ||
            !$aid ||
            !is_numeric($userId) ||
            !is_string($userDName) ||
            !is_numeric($aid) ||
            !is_string($cardno) ||
            !is_array($cidNumMap)
         ) {
            return false;
        }

        // 得到短信模板
        $conf = load_config('jst','sms_ac');
        $conf = $conf['templates'];
        if (!$conf) {
            exit('短信模板配置文件丢失');
        }

        switch ((string)$msgType) {

            case '2': // 礼券

                if (!$cidNumMap || !$date || !is_array($couponInfo)) {
                    return false;
                }

                $coupon = '';
                $limit  = 1; // 限制通知的过期礼券条数，防止太多
                $tpl    = $conf['BATPUSH_COUPON'];

                foreach ($cidNumMap as $cid => $num) {
                    if (--$limit < 0) {
                        break;
                    }
                    if (isset($couponInfo[$cid]['name'])) {
                        $coupon .= $couponInfo[$cid]['name'].'*'.$num.',';
                    }
                }

                $parama = [
                    '{dname}'   => $userDName ?: '',
                    '{date}'    => $date,
                    '{coupon}'  => rtrim($coupon,',') . $hasMore
                ];

                $tplContent = $this->_templateReplace($tpl, $parama);
                break;

            case '0': // 通用
                $tpl    = $conf['BATPUSH_COMMON'];
                $parama = [
                    '{cardno}' => $cardno ?: '',
                    '{dname}'  => $userDName ?: '',
                ];
                $tplContent = $this->_templateReplace($tpl, $parama);
                break;

            case '1': // 生日
                $tpl    = $conf['BATPUSH_BIRTHDAY'];
                $parama = [
                    '{dname}' => $userDName ?: '',
                ];
                $tplContent = $this->_templateReplace($tpl, $parama);

            default:
                break;
        }

        return $tplContent ?: false;
    }

    /**
     * 模板数据替换
     * <AUTHOR>
     * @date   2017-04-26
     * 
     * @param string $tpl    模板
     * @param array  $params 模板替换的数据
     */
    private function _templateReplace($tpl = '', $params = [])
    {
        $search  = array_keys($params);
        $replace = array_values($params);
        return str_replace($search, $replace, $tpl) ?: '';
    }

    /**
     * Excel导入的消息内容
     * <AUTHOR>
     * @date   2017-04-26
     * 
     * @param  array $msgDataField pft_bat_task_msg表的data字段
     * @return excel的内容字段
     */
    private function _getExcelContent($msgDataField = '')
    {
        if (!$msgDataField || !is_string($msgDataField)) {
            return '';
        }
        $data = json_decode($msgDataField, true);
        return trim($data['content']) ? trim($data['content']) : '';
    }

    /**
     * 获得指定日期的生日会员
     * <AUTHOR>
     * @time   2017-05-09
     *
     * @param   int    $date  月日, [501 == 五月一日]
     * @return  array  当日生日的所有用户ID
     */
    private function _getBirthUserIdArr($date = 0)
    {
        if (!$date) {
            return [];
        }

        //用户二期 - 信息获取修改
        $MemberBus = new \Business\Member\Member();
        $res = $MemberBus->getMemberListByBirthday($date, $field = 'id as fid');
        if ($res) {
            $allUserId = array_column($res, 'fid');
        }

        return $allUserId ?: [];
    }

    /**
     * 自动检测并修复有身份证却没生日的记录
     * <AUTHOR>
     * @time   2017-05-09
     */
    private function _batParseIdcardBirth()
    {
        $CustomerModel =  new \Model\Member\Customer();
        $where      = [
            'id_card_no'  => ['NEQ', ''],
            'birthday'    => ['EQ', 0],
        ];

        // 判断是否存在异常数据
        $res = $CustomerModel->table(MainTableConst::TABLE_CUSTOMER)->where($where)->count();
        if (!$res) {
            return true;
        }

        // 批量修复数据
        $field      = 'id,id_card_no';
        $limit      = 2000;
        $continue   = true;
        $nowId      = 0;
        while ($continue) {
            $where['id'] = ['gt', $nowId];
            $res = $CustomerModel->table(MainTableConst::TABLE_CUSTOMER)->field($field)->where($where)->limit($limit)->select();
            if (!$res) {
                $continue = false;

            } else {
                $count = count($res);
                if (!$count) {
                    $continue = false;
                } else {
                    $idStr = '';
                    $nowId = $res[$count-1]['id'];
                    $sql   = 'UPDATE '.MainTableConst::TABLE_CUSTOMER.' SET birthday = CASE id';

                    foreach ($res as $data) {
                        $cardNo = $data['id_card_no'];
                        if (Tools::personID_format_err(trim($cardNo))) {
                            $sql .= ' WHEN \'' . $data['id'] . '\' THEN ' . intval(mb_substr($cardNo, 10, 4));
                            $idStr .= $data['id'] . '\',\'';
                        }
                    }
                    if ($idStr) {
                        $sql .= ' END WHERE 1=1 and id IN (\'' . rtrim($idStr,',\'') . '\')';
                        $CustomerModel->execute($sql);
                    }
                }
            }
            sleep(1);
        }

        return true;;
    }

    /**
     * 获得微信绑定关联表的模型对象
     * <AUTHOR>
     * @date   2017-04-25
     */
    private function _getWxModel()
    {
        static $wxModel;
        if (!$wxModel) {
            $wxModel = new \Model\Wechat\WxMember();
        }
        return $wxModel;
    }

    /**
     * 获得微信绑定关联表的模型对象
     * <AUTHOR>
     * @date   2017-04-25
     */
    private function _getMemberCardModel()
    {
        static $cardModel;
        if (!$cardModel) {
            $cardModel = new \Model\Product\MemberCard();
        }
        return $cardModel;
    }

    /**
     * 获得 Member 模型实例
     * <AUTHOR>
     * @time   2017-04-25
     */
    private function _getMemberModel()
    {
        static $mModel;
        if (is_null($mModel)) {
            $mModel = new \Model\Member\Member();
        }
        return $mModel;
    }

}