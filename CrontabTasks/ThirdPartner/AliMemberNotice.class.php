<?php
/**
 * 阿里供销授权到期预警推送
 */

namespace CrontabTasks\ThirdPartner;

use Library\Cache\Cache;
use Library\Controller;
use Library\Resque\Queue;
use Model\Member\Member;
use Model\Ota\AliMemberConf;
use Model\Wechat\WxMember;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class AliMemberNotice extends Controller
{
    protected $sendCacheKey = 'ali_market:expire:noticed:memberid:';

    /**
     * php /var/www/html/Service/Crontab/runNew.php ThirdPartner/AliMemberNotice PushNotice
     * 授权到期预警推送
     * <AUTHOR>
     * @date 2021-11-14
     */
    public function PushNotice()
    {
        //缓存获取
        $cacheRedis = Cache::getInstance('redis');
        $aliModel   = new AliMemberConf();
        $memberList = $aliModel->getOpenNoticeUser();
        if ($memberList) {
            foreach ($memberList as $noticeItem) {
                if ($noticeItem['expire_time'] - time() < 0) {
                    continue;
                }

                //查询缓存内是否有记录发送记录,已发送过的不进行发送。
                $sendCacheKey = $this->sendCacheKey . $noticeItem['member_id'];
                if ($cacheRedis->get($sendCacheKey)) {
                    continue;
                }

                $expireIn = ceil(($noticeItem['expire_time'] - strtotime(date('Y-m-d'))) / 86400);
                if ($noticeItem['notice_days'] >= $expireIn) {
                    $memberModel = new Member();
                    $memberInfo  = $memberModel->getMemberInfo($noticeItem['member_id'], 'id', 'dname');
                    $dName       = $memberInfo['dname'] ?? "";
                    $cacheRedis->set($sendCacheKey, 's', '', $noticeItem['expire_time'] - time());
                    if ($noticeItem['notice_type'] == 1) {
                        $wxModel = new WxMember();
                        //缓存获取
                        $time           = $wxModel->wxBindListTime;//缓存时间
                        $cacheKey       = sprintf($wxModel->wxBindListKey, $noticeItem['member_id']);
                        $aliWarningList = $cacheRedis->get($cacheKey);
                        $aliWarningList = json_decode($aliWarningList, true);
                        if (!$aliWarningList) {
                            //设置缓存
                            $aliWarningList = $wxModel->getWxInfo($noticeItem['member_id'], PFT_WECHAT_APPID, 1, '',
                                true);
                            if ($aliWarningList) {
                                $cacheRedis->set($cacheKey, json_encode($aliWarningList), '', $time);
                            }
                        }

                        if ($aliWarningList) {
                            foreach ($aliWarningList as $item) {
                                //绑定微信是否开启授信预警 /还款预警
                                $noticeinfo = isset($item['notice_info']) ? json_decode($item['notice_info'],
                                    true) : null;
                                if ($noticeinfo['aliExpireNotice'] == 1) {
                                    $data = [
                                        'first'    => ['value' => '阿里授权即将到期', 'color' => '#ff3300'],
                                        'keyword1' => ['value' => date('Y-m-d H:i:s'), 'color' => '#ff9900'],
                                        'keyword2' => [
                                            'value' => "名称:{$dName}" . "\n" . "账号:{$noticeItem['member_id']}" . "\n" . "您的阿里供销平台授权将于{$expireIn}天后过期",
                                            'color' => '#ff9900',
                                        ],
                                        'remark'   => ['value' => "请尽快登录票付通平台操作", 'color' => '#0000ff'],
                                    ];

                                    if (!$item['fromusername']) {
                                        pft_log('ota/ali_notify',
                                            "阿里供销发送预警通知失败,openid为空。data:" . json_encode($data, JSON_UNESCAPED_UNICODE),
                                            "month");
                                        continue;
                                    }

                                    $job_id = Queue::push('notify', 'WxNotify_Job',
                                        array(
                                            'data'   => $data,
                                            'openid' => $item['fromusername'],
                                            'tplid'  => 'WARNING',
                                            'url'    => "",
                                            'color'  => '#FF0000',
                                        )
                                    );

                                    pft_log('ota/ali_notify', json_encode([
                                        'jobId'  => $job_id,
                                        'data'   => $data,
                                        'openid' => $item['fromusername'],
                                    ]));
                                }
                            }
                        } else {
                            pft_log('ota/ali_notify', "该账号下未找到绑定的微信{$noticeItem['member_id']}");
                        }
                    } else {
                        //短信通知
                        $data = [
                            'type'       => 'aliSmsNotify',
                            'memberId'   => $noticeItem['member_id'],
                            'dName'      => $dName,
                            'expireDays' => $expireIn,
                            'mobile'     => $noticeItem['mobile'],
                        ];

                        \Library\Resque\Queue::push(
                            'notify', 'AliExpireNotice_Job', $data
                        );
                    }
                }
            }
        }

    }
}