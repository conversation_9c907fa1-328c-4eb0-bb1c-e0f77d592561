<?php
namespace CrontabTasks\ThirdPartner;

/**
 * 白水洋导码脚本
 * <AUTHOR>
 * @date   2018-05-14
 */
use Library\Controller;
use Model\Order\ExchangeCode;
use Model\Product\Land;
use Model\Product\Ticket;

class ImportCode extends Controller {
    public function index() {
        global $argv;
        //前缀
        $prefix = $argv[3];
        //起始
        $begin  = $argv[4];
        //截至
        $end    = $argv[5];
        //票类ID
        $tid    = $argv[6];
        //价格
        $price  = $argv[7];

        $model  = new ExchangeCode();
        $ticketModel = new Ticket();
        $ticketInfo  = $ticketModel->getTicketInfoById($tid, 'landid');
        if (empty($ticketInfo)) {
            exit('未找到该票类');
        }
        $landId = $ticketInfo['landid'];

        $landModel = new Land();
        $landInfo  = $landModel->getLandInfo($landId, false, 'salerid');
        $salerId   = $landInfo['salerid'];

        $data   = [];
        for ($i = $begin; $i <= $end; $i++) {
            $data[] = [
                'tid'       => $tid,
                'salerid'   => $salerId,
                'price'     => $price,
                'status'    => 0,
                'used_at'   => 0,
                'ordernum'  => 0,
                'batch_num' => 0,
                'mac_addr'  => 0,
                'device_id' => '',
                'scode'     => $prefix . $i,
                'scode_idx' => substr($i, -6, 6),
            ];

            if (count($data) > 500) {
                $res = $model->addExchangeCode($data);
                if (empty($res)) {
                    pft_log(1, $model->getDbError());
                }
                $data = [];
            }
        }

        $res = $model->addExchangeCode($data);
        if (empty($res)) {
            pft_log(1, $model->getDbError());
        }
    }
}