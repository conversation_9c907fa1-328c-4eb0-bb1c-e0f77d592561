<?php
/**
 * 支付宝对接订单推送
 */
namespace CrontabTasks\ThirdPartner;

use Throwable;
use Library\Controller;
use InvalidArgumentException;
use Business\Order\OrderList;
use Model\Ota\GovernmentDataSys;
use CrontabTasks\ThirdPartner\AliPay\Ticket;
use CrontabTasks\ThirdPartner\AliPay\Exceptions\InvalidResponseException;

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

class AliPayPush extends Controller
{
    const GOVERNMENT_SYS_TYPE = 23;

    /**
     * php /var/www/html/Service/Crontab/runNew.php ThirdPartner/AliPayPush OrderDataPush
     * 订单数据推送
     * <AUTHOR>
     * @date 2021-07-03
     * @return void
     */
    public function OrderDataPush(): void
    {
        // 推送间隔 1 小时
        // 通过 lid pay_status 已支付推送
        try {
            // 固定值
            $timeTypeConf = load_config('time_type', 'orderSearch');
            $selectType   = 0;
            if (isset($timeTypeConf[$selectType])) {
                $dbStart = $timeTypeConf[$selectType][0];
                $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
            } else {
                $dbStart = false;
                $dbEnd   = false;
            }

            $pageSize       = 15; // 每页条数
            $payStatus      = 1; // 支付方式 0: 现场支付 1: 已支付 2: 未支付
            $orderSource    = 1;
            $orderStartTime = date('Y-m-d H:i:s', strtotime('-1 hours')); // 获取 1 小时前的订单
            $orderEndTime   = date('Y-m-d H:i:s');

            // 根据配置获取 lid
            $govModel      = new GovernmentDataSys();
            $field         = 'id,apply_id';
            $govDataSysMap = $govModel->getGdListByType(self::GOVERNMENT_SYS_TYPE, $field);
            if (empty($govDataSysMap)) {
                throw new InvalidArgumentException('支付宝推送配置信息不存在');
            }

            $orderModel = new OrderList();
            foreach ($govDataSysMap as $goDataSys) {
                $govDataAttr = $govModel->getAttrByGdsId($goDataSys['id']);
                if (empty($govDataAttr)) {
                    throw new InvalidArgumentException('支付宝推送配置扩展信息不存在');
                }

                $applyId = $goDataSys['apply_id']; // 供应商id
                [
                    'land_id'       => $landId, // 后台配置 表示景区的id
                    'scenic_app_id' => $scenicAppId, // 后台配置 景区小程序id
                    'source_system' => $sourceSystem, // 后台配置 服务商名称
                    'status'        => $status, // 后台配置 0：无效 1：有效
                    'ticket_id'     => $ticketId, // 后台配置 门票商品id
                    'ticket_type'   => $ticketType, // 后台配置 门票类型 NORMAL: 普通 GROUP: 套票 PERIOD: 时段票 REGION: 区域票
                ] = $govDataAttr;

                // 通过 lid 查询订单信息
                $lidArr = [$landId];
                $tidArr = [$ticketId];
                $page   = 1; // 页数
                while (true) {
                    // 通过 apply_id,lid,tid 获取已支付的订单信息
                    $javaOrderResponse = $orderModel->getBusinessListByOrderService($applyId, $applyId, 0, [$applyId],
                        $dbStart, $dbEnd, $page, $pageSize, false, false,
                        false, false, $orderStartTime, $orderEndTime,
                        false, false, false, false,
                        false, false, false, $payStatus,
                        false, false, false, false,
                        false, $lidArr, false, false, false, $tidArr, $orderSource);
                    if ($javaOrderResponse['code'] != 200) {
                        throw new InvalidArgumentException($javaOrderResponse['msg']);
                    }
                    if (empty($javaOrderResponse['data']['list'])) {
                        break;
                    }
                    $page++;

                    // 支付宝推送类实例化
                    $ticketObj = new Ticket([
                        //'debug' => true,
                        'appid' => $scenicAppId,
                    ]);

                    $orderList = $javaOrderResponse['data']['list'];
                    foreach ($orderList as $order) {
                        // 已取消的订单不用推送
                        if (in_array($order['ori_status'], [3])) {
                            continue;
                        }

                        $pushDataMap = [];
                        $orderNum    = $order['ordernum'];
                        $orderDetail = $orderModel->handleOrderDetail($orderNum); // 获取游玩人信息
                        $landName    = $orderDetail['land_name']; // 门票名

                        $extInfo = []; // 扩展信息
                        $requestData = [
                            'source_system' => $sourceSystem, // 后台配置 服务商名称
                            'scenic_app_id' => $scenicAppId, // 后台配置 景区小程序id
                            'outer_scenic_id' => $landId, // 后台配置 表示景区的id
                            'outer_ticket_id' => $ticketId, // 后台配置 门票商品id
                            'ticket_type' => $ticketType, // 后台配置 门票类型 NORMAL: 普通 GROUP: 套票 PERIOD: 时段票 REGION: 区域票
                            'name' => $landName, // 门票名称
                            'status' => $status, // 后台配置 0：无效 1：有效
                            'ticket_modified_time' => date('Y-m-d H:i:s'), // 更新时间
                        ];

                        if (!empty($orderDetail['ordername'])) {
                            $extInfo[] = [
                                'key'   => 'contact_name', // 游客姓名
                                'value' => $orderDetail['ordername'],
                            ];
                        }
                        if (!empty($orderDetail['contact_tel'])) {
                            $extInfo[] = [
                                'key'   => 'contact_mobile', // 游客手机号
                                'value' => $orderDetail['contact_tel'],
                            ];
                        }
                        if (!empty($orderDetail['person_id'])) {
                            $extInfo = array_merge($extInfo, [
                                [
                                    'key'   => 'contact_cert_type', // 证件类型,固定身份证
                                    'value' => '身份证',
                                ],
                                [
                                    'key'   => 'contact_cert_no', // 游客身份证
                                    'value' => $orderDetail['person_id'],
                                ],
                            ]);
                        }
                        if (!empty($extInfo)) {
                            $requestData['ext_info'] = $extInfo;
                        }
                        $pushDataMap[] = $requestData;

                        // 判断本票实名制人数
                        if (!empty($orderDetail['tourist_info'])) {
                            $pushDataMap = [];

                            // 一笔订单三张票,三个游玩人信息,调取同步接口三次,传三个游玩人信息
                            foreach ($orderDetail['tourist_arr'] as $tourist) {
                                $extInfo = []; // 扩展信息

                                // 取消票不推送
                                if ($tourist['check_state'] == 2) {
                                    continue;
                                }
                                if (!empty($tourist['tourist'])) {
                                    $extInfo[] = [
                                        'key'   => 'contact_name', // 游客姓名
                                        'value' => $tourist['tourist'],
                                    ];
                                }
                                if (!empty($tourist['mobile'])) {
                                    $extInfo[] = [
                                        'key'   => 'contact_mobile', // 游客手机号
                                        'value' => $tourist['mobile'],
                                    ];
                                }
                                if (!empty($tourist['idcard'])) {
                                    $extInfo = array_merge($extInfo, [
                                        [
                                            'key'   => 'contact_cert_type', // 证件类型,固定身份证
                                            'value' => '身份证',
                                        ],
                                        [
                                            'key'   => 'contact_cert_no', // 游客身份证
                                            'value' => $tourist['idcard'],
                                        ],
                                    ]);
                                }

                                if (!empty($extInfo)) {
                                    $requestData['ext_info'] = $extInfo;
                                }
                                $pushDataMap[] = $requestData;
                            }
                        }

                        foreach ($pushDataMap as $pushData) {
                            // 推送支付宝
                            $response = $ticketObj->apply($pushData);
                            $logData = [
                                'request' => [
                                    'url'  => $ticketObj->getGateway(),
                                    'data' => $ticketObj->getOptions()->getConfig(),
                                ],
                                'response' => $response,
                            ];
                            pft_log('alipay/ticketOrder/push',
                                json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                        }
                    }
                }
            }
        } catch (Throwable $e) {
            $msg     = $e->getMessage();
            $raw     = [];
            $request = [];
            if ($e instanceof InvalidResponseException) {
                $raw = $e->getRaw();
            }
            if (!empty($ticketObj)) {
                /** @var Ticket $ticketObj */
                $request = [
                    'url'  => $ticketObj->getGateway(),
                    'data' => $ticketObj->getOptions()->getConfig(),
                ];
            }
            $logData = [
                'exceptionMsg'  => $msg,
                'exceptionData' => $raw,
                'request'       => $request,
            ];
            pft_log('alipay/ticketOrder/push', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        }
    }
}