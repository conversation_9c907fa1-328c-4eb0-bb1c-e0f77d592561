<?php
/**
 * 入园动态码推送美团
 *
 * <AUTHOR>
 * @date 2020-06-22
 *
 */
namespace CrontabTasks\ThirdPartner;


use Library\Controller;
use Library\Cache\Cache;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class DynamicCode extends Controller
{

    //动态码有序集合队列
    const SORTED_SET_KEY = 'dynamic_code:sorted_set:1';

    //const SORTED_SET_KEY = 'dynamic_code:sorted_set:test7';

    //同步失败的动态码数据
    const FAIL_CODE_KEY = 'dynamic_code:push_fail:code';
    //订单的当前动态码相关数据
    const ORDER_DYNAMIC_SET_KEY = 'dynamic_code:order_value:';

    private $_debugLog = 'dynamic_code/';

    //动态码缓存存储数据中 code与rid分隔符
    private $_codeRidDelimiter = '|';

    //动态码缓存存储数据中 多个code|rid串分隔符
    private $_codeRidStrDelimiter = ':';


    /**
     * 动态码更新
     *
     *   code,lid,rid,time_space
     *   4012312311,12,3232,180
     * <AUTHOR>
     * @date    2020-05-22
     *
     * php /var/www/html/Service/Crontab/runNew.php ThirdPartner/DynamicCode codeSync
     */

    public function codeSync()
    {

        while (true) {

            try {

                $hour = date('H');
                //if ($hour > 23 || $hour < 7) {
                //    continue;
                //}

                sleep(10);

                $formatOrderCode    = [];
                $updateNewCodeData  = [];
                $currentTime        = time();

                $redis = \Library\Cache\RedisCache::Connect('terminal');

                //获取当前有序集合里到期的数据
                $needUpdateData = $redis->zRangeByScore(self::SORTED_SET_KEY, 0, $currentTime);

                if (!$needUpdateData) {
                    echo "no data...". "\r\n";
                    pft_log($this->_debugLog, '无需处理的数据');
                    continue;
                }

                $dynamicCodeModel = new \Model\EnterLand\DynamicCode();
                $params = [self::SORTED_SET_KEY];

                $stringParams = [];

                //按订单维度更新
                foreach ($needUpdateData as $oldCodeData) {

                    // 拆解后的数据
                    $splitOldData   = $this->_splitCodeData($oldCodeData);
                    //订单的过期时间
                    $codeExpireTime = $splitOldData['expire_time'];
                    //过期踢出队列
                    if ($currentTime >= $codeExpireTime) {
                        $redis->zRem(self::SORTED_SET_KEY, $oldCodeData);
                        continue;
                    }

                    //订单号
                    $orderid  = $splitOldData['orderid'];
                    //供应商ID
                    $applyDid = $splitOldData['apply_did'];

                    //景区id
                    $lid     = $splitOldData['lid'];
                    //存储该笔订单当前有效的码  格式 code|rid:code|rid:code|rid   每个code|rid对应入园凭证
                    $codeStr = $splitOldData['code'];

                    //下次过期时间
                    $newExpireTime = $splitOldData['time_space'] + $currentTime;
                    //切割成 code|rid 格式字符串的数据
                    $oldCodeArr = explode($this->_codeRidStrDelimiter, $codeStr);

                    //生产新码
                    $newCodeList = $dynamicCodeModel->createDynamicCode(count($oldCodeArr));
                    $newCodeStr  = '';
                    // var_dump($oldCodeArr);
                    foreach ($oldCodeArr as $tmpStr) {

                        //新的动态码
                        $newCode    = array_pop($newCodeList);
                        $codeRidArr = explode($this->_codeRidDelimiter, $tmpStr);

                        $oldCode = $codeRidArr[0];
                        $rid     = $codeRidArr[1];

                        //生成新的动态码表记录数据
                        $updateNewCodeData[] = [
                            'rid'           => $rid,
                            'lid'           => $lid,
                            'dynamic_code'  => $newCode,
                            'expire_time'   => $newExpireTime,
                            'create_time'   => $currentTime
                        ];

                        $formatOrderCode[$orderid][] = [
                            'old_dynamic_code' => $oldCode,
                            'new_dynamic_code' => $newCode,
                            'apply_did'        => $applyDid,
                        ];

                        $newCodeRidStr = $newCode . $this->_codeRidDelimiter . $rid;
                        if ($newCodeStr) {
                            $newCodeStr = $newCodeStr . $this->_codeRidStrDelimiter . $newCodeRidStr;
                        } else {
                            $newCodeStr = $newCodeRidStr;
                        }

                        echo "old:{$oldCode}, new:{$newCode}" . "\r\n";
                    }

                    //新动态码数据
                    $newCodeData = $this->_formatNewCodeData($orderid, $applyDid, $newCodeStr, $lid, $splitOldData['time_space'], $codeExpireTime);
                    //var_dump($newCodeData);exit;
                    //下次执行时间
                    $params[] = $newExpireTime;
                    //保证唯一性
                    $params[] = $newCodeData;

                    $stringParams[] = [(self::ORDER_DYNAMIC_SET_KEY . $orderid) => $newCodeData];
                }
               // var_Dump($stringParams);exit;
                if (!$updateNewCodeData) {
                    echo "no update data...". "\r\n";
                    pft_log($this->_debugLog, '无需处理的数据');
                    continue;
                }

                //先更新新码到库中
                $addRes = $dynamicCodeModel->addValidCode($updateNewCodeData);
                if (!$addRes) {
                    echo "update new code to db fail...". "\r\n";
                    pft_log($this->_debugLog, json_encode(['更新新码失败', $updateNewCodeData]));
                    continue;
                }

                //推送
                $otaOrderBiz = new \Business\Ota\Order();
                $noticeRes   = $otaOrderBiz->noticeMtChangeVoucher($formatOrderCode);

                //$noticeRes = ['code' => 200, 'data' =>[]];
                pft_log('dynamic_code/update_res/', json_encode([$formatOrderCode, $noticeRes]));

                //推送失败
                if ($noticeRes['code'] == 200 && !empty($noticeRes['data'])) {

                    //默认过期时间
                    empty($newExpireTime) && $newExpireTime = time() + 60;
                    //上次请求是否有过期的
                    $lastOldCode = $redis->sMembers(self::FAIL_CODE_KEY);
                    //本次请求过期的
                    $oldCodeArr = array_column($noticeRes['data'], 'voucher');

                    $failVoucherListArr = [];
                    foreach ($oldCodeArr as $oldFailItem) {
                        $failVoucherListArr = array_merge($failVoucherListArr, $oldFailItem);
                    }

                    $updateOldCode = array_merge($failVoucherListArr, $lastOldCode);
                    //可能由于网络或其他情况造成的推送失败，旧动态码和新动态码都要有效,保证都能入园
                    $updateRes  = $dynamicCodeModel->updateCodeExpireTime($updateOldCode, $newExpireTime);
                    !$updateRes && pft_log($this->_debugLog, json_encode(['更新码过期时间失败', $oldCodeArr, $newExpireTime]));

                    if ($failVoucherListArr) {
                        $redis->sAddArray(self::FAIL_CODE_KEY, $failVoucherListArr);
                        $redis->expire(self::FAIL_CODE_KEY, 3600);
                    }
                }

                //删除
                $delRes = $redis->zRemRangeByScore(self::SORTED_SET_KEY, 0, $currentTime);
                if (!$delRes) {
                    pft_log($this->_debugLog, json_encode(['删除集合数据失败', $currentTime]));
                }

                //更新推送时间
                call_user_func_array([$redis, 'zAdd'], $params);
                call_user_func_array([$redis, 'mset'], $stringParams);
                pft_log('dynamic_code/debug/stringParams', json_encode([$formatOrderCode, $noticeRes]));
                unset($needUpdateData, $formatOrderCode, $params, $updateNewCodeData);

            } catch (\Exception $e) {

                $msg = $e->getMessage();
                echo $msg;
                pft_log($this->_debugLog, json_encode($msg));
                continue;

            } finally {


            }
        }
    }


    /**
     * 数据拆分
     * <AUTHOR>
     * @date    2020-05-22
     *
     * @param  array  $codeData     旧新码对应数据
     *
     * @return  array
     *
     */
    private function _splitCodeData(string $codeData)
    {
        $tmpArr = explode(',', $codeData);
        //订单号
        $orderid = $tmpArr[0];
        //订单号
        $applyDid = $tmpArr[1];
        //旧码
        $oldCode  = $tmpArr[2];
        //景区id
        $lid = $tmpArr[3];
        //码更新相隔的时长，单位秒
        $timeSpace = $tmpArr[4];
        //过期时间
        $expireTime = $tmpArr[5];

        return ['orderid' => $orderid, 'apply_did' => $applyDid, 'code' => $oldCode, 'lid' => $lid, 'time_space' => $timeSpace, 'expire_time' => $expireTime];
    }


    /**
     * 生成新动态码数据, 格式 orderid,applyDid,code,lid,rid,time_space  订单号,动态码,景区id,关联id,下次更新间隔
     *
     * <AUTHOR>
     * @date    2020-05-22
     *
     * @param  string   $newCodeStr        新动态码
     * @param  string   $applyDid       供应商id
     * @param  integer  $lid            景区id
     * @param  integer  $timeSpace      下次更新间隔,单位秒
     * @param  integer  $codeExpireTime 过期时间
     *
     * @return  string
     *
     */
    private function _formatNewCodeData(string $orderid, int $applyDid, string $newCodeStr, int $lid, int $timeSpace, int $codeExpireTime)
    {
        if (!$orderid || !$newCodeStr || !$lid || !$timeSpace) {
            return '';
        }

        $formatStr = "%s,%u,%s,%u,%u,%u";
        return sprintf($formatStr, $orderid, $applyDid, $newCodeStr, $lid, $timeSpace,$codeExpireTime);
    }
}