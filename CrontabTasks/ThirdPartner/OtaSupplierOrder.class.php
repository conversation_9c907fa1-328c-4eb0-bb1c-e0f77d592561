<?php

namespace CrontabTasks\ThirdPartner;

use Library\Controller;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class OtaSupplierOrder extends Controller
{
    public function supplierOrderCount()
    {
        $time      = microtime(true);
        $beginDate = date('Y-m-01', strtotime('-1 month'));
        $endDate   = date('Y-m-t', strtotime('-1 month'));

        $statisticsService = new \Business\Ota\OtaStatistics();

        //获取上个月有订单的fid
        $sysConfigModel = new \Model\Ota\SysConfig();
        $getFidRes      = $statisticsService->getAllApiOrderFids($beginDate, $endDate);

        if ($getFidRes['code'] != 200) {
            exit('无订单数据');
        }

        $fidArr = $getFidRes['data'];
        $i      = 0;
        foreach ($fidArr as $fidValue) {
            $fid = $fidValue['fid'];
            //有权限的对接列表
            $csysIds = $sysConfigModel->getSysMember($fid);
            if (!empty($csysIds)) {
                foreach ($csysIds as $csys) {
                    //设置好每个月的
                    $LastMonthOrder = $statisticsService->setLastMonthOrder($fid, $beginDate, $endDate, $csys);
                    echo json_encode([[$fid, $beginDate, $endDate, $csys], $LastMonthOrder],
                            JSON_UNESCAPED_UNICODE) . PHP_EOL;
                    $i++;
                }
            }

            if ($i % 100 == 0) {
                sleep(1);
            }

        }
        $time = sprintf("%.2f", microtime(true) - $time);
        exit('done,花费时间' . $time . '秒');
    }
}