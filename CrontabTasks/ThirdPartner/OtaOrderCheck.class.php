<?php
/**
 * 该异步任务主要任务：对接第三方的订单（同城，去哪儿，方特，全面客流，携程）游客在他们那边核销后，
 * 他们不会主动通知我们，所以需要通过主要查询接口，定时去查询这些订单的状态，然后在我们自己平台进行核销
 *
 * 添加任务：三方系统凭证码轮询查询记录和通知OTA
 *
 * 策略：最近三个月的这几个三方系统的订单都需要查询，每个订单的查询间隔是15分钟
 *       query：主要负责拉取订单的状态和记录主动查询的时间
 *       check: 主要负责订单在我们平台的主动核销
 *
 * User: zhangyangzhen
 * Modify: dwer.cn
 * Date: 2018/4/10
 * Time: 14:21
 */

namespace CrontabTasks\ThirdPartner;

use Business\Order\OrderOta;
use Business\Ota\Order as OtaOrderBiz;
use Library\Cache\Cache;
use Library\Controller;
use Model\Order\AllApiOrderModel;
use Model\Ota\Notify as NotifyModel;
use Model\Ota\OtaQueryModel;
use Model\Ota\SysConfig;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class OtaOrderCheck extends Controller
{
    private $_all_api_order;
    private $_ota_query;
    private $PFT_infunction; //AllFunction模型

    const __ALL_API_ORDER__ = 'all_api_order';

    private $_queryDebugPath = 'ota_order_check/query_debug';
    private $_checkDebugPath = 'ota_order_check/check_debug';
    private $_errorPath      = 'ota_order_check/error';

    private $_cachePrefix = 'ota_order_check:';

    public function __construct()
    {
        //引用控制器基类
        include_once '/var/www/html/ota/common/CtrlBase.class.php';
    }

    /**
     * 去哪儿订单状态查询，最近15天订单
     * <AUTHOR>
     * @date   2018-05-15
     *
     * @return
     */
    public function qunarQuery()
    {
        $defaultTime    = strtotime(date('Y-m-d'));
        $startTimestamp = strtotime('-15 day', $defaultTime);
        $endTimestamp   = time() - 600; //最近10分钟的订单不去查询
        $intervalTime   = 20 * 60; //每20分钟查询一次
        $coopBArr       = [30];

        $this->_baseQuery($startTimestamp, $endTimestamp, $intervalTime, $coopBArr);
    }

    /**
     * 携程订单状态查询，最近15天订单
     * <AUTHOR>
     * @date   2018-05-15
     *
     * @return
     */
    public function ctripQuery()
    {
        $defaultTime    = strtotime(date('Y-m-d'));
        $startTimestamp = strtotime('-15 day', $defaultTime);
        $endTimestamp   = time() - 600; //最近10分钟的订单不去查询
        $intervalTime   = 20 * 60; //每20分钟查询一次
        $coopBArr       = [68];

        $this->_baseQuery($startTimestamp, $endTimestamp, $intervalTime, $coopBArr);
    }

    /**
     * 同程+方特对接系统+全面客流 订单状态查询，最近15天订单
     * <AUTHOR>
     * @date   2018-05-15
     *
     * @return
     */
    public function tcQuery()
    {
        $defaultTime    = strtotime(date('Y-m-d'));
        $startTimestamp = strtotime('-15 day', $defaultTime);
        $endTimestamp   = time() - 600; //最近10分钟的订单不去查询
        $intervalTime   = 20 * 60; //每20分钟查询一次
        $coopBArr       = [25, 31, 58];

        $this->_baseQuery($startTimestamp, $endTimestamp, $intervalTime, $coopBArr);
    }

    /**
     * 武汉数文 订单状态查询，最近15天订单
     * User: Liucm
     * Date: 2021/1/18
     * Time: 19:55
     */
    public function whswQuery()
    {
        $defaultTime    = strtotime(date('Y-m-d'));
        $startTimestamp = strtotime('-15 day', $defaultTime);
        $endTimestamp   = time() - 600; //最近10分钟的订单不去查询
        $intervalTime   = 20 * 60; //每20分钟查询一次
        $coopBArr       = [212];

        $this->_baseQuery($startTimestamp, $endTimestamp, $intervalTime, $coopBArr);
    }

    /**
     * 北京环球影视 订单状态查询，最近15天订单
     * <AUTHOR>
     * @date 2021-05-07
     */
    public function bjhqysQuery()
    {
        $defaultTime    = strtotime(date('Y-m-d'));
        $startTimestamp = strtotime('-15 day', $defaultTime);
        $endTimestamp   = time() - 600; //最近10分钟的订单不去查询
        $intervalTime   = 20 * 60; //每20分钟查询一次
        $coopBArr       = [252];

        $this->_baseQuery($startTimestamp, $endTimestamp, $intervalTime, $coopBArr);
    }

    /**
     * 欧道V2 订单状态查询，最近15天订单
     * <AUTHOR>
     * @date 2021-05-27
     */
    public function OtsV2Query()
    {
        $defaultTime    = strtotime(date('Y-m-d'));
        $startTimestamp = strtotime('-15 day', $defaultTime);
        $endTimestamp   = time() - 600; //最近10分钟的订单不去查询
        $intervalTime   = 20 * 60; //每20分钟查询一次
        $coopBArr       = [257];

        $this->_baseQuery($startTimestamp, $endTimestamp, $intervalTime, $coopBArr);
    }

    /**
     * 较早的历史订单状态查询
     * 3个月前到15天前的订单
     * <AUTHOR>
     * @date   2018-05-15
     *
     * @return
     */
    public function historyQuery()
    {
        $defaultTime = strtotime(date('Y-m-d'));

        $startTimestamp = strtotime('-3 month', $defaultTime);
        $endTimestamp   = strtotime('-15 day', $defaultTime); //最近10分钟的订单不去查询
        $intervalTime   = 120 * 60; //每120分钟查询一次
        $coopBArr       = [25, 30, 31, 58, 68, 212, 252];

        $this->_baseQuery($startTimestamp, $endTimestamp, $intervalTime, $coopBArr);
    }

    /**
     * 三方订单凭证码查询、记录、通知OTA
     * 主要是这几个三方系统：同程、自我游、携程、新美大，去哪儿
     *
     * <AUTHOR>
     * @date   2018-06-26
     *
     * @return
     */
    public function apiCodeQuery()
    {
        //统一获取模型
        $apiModel = $this->_getAllApiOrder();
        $otaModel = $this->_getOtaQuery();

        //查询近2-8分钟的第三方订单记录
        $startTime = date('Y-m-d H:i:s', time() - 8 * 60);
        $endTime   = date('Y-m-d H:i:s', time() - 2 * 60);
        $coopBList = [7, 25, 68, 30]; //7:自我游, 25:同程, 30:去哪儿, 68:携程, 252: 北京环球影视;
        $field     = 'id,pftOrder,bCode,coopB,apiCode,apiOrder,tempOrder,cTime';
        $size      = 1000;
        $orderList = $apiModel->getMomentListInfoForApiCode($coopBList, $startTime, $endTime, $field, $size);

        if ($orderList) {
            //首先将不需要查询的订单设置为"非异步返码，不要查询"
            $noSyncList = [];
            foreach ($orderList as $key => $item) {
                if (!$item['bCode'] || $item['apiOrder'] == 'tmp_' . $item['tempOrder']) {
                    $noSyncList[] = $item['id'];
                    unset($orderList[$key]);
                }
            }

            //先将订单都改为已查询状态避免重复查询
            foreach ($orderList as $order) {
                $apiModel->updateCodeQueryStatus($order['id'], 1);
            }

            //开始查询三方系统凭证码
            $otaOrderBiz = new OtaOrderBiz();
            $notifyModel = new NotifyModel();
            $orderOtaBiz = new OrderOta();

            foreach ($orderList as $item) {
                //不需要查询的订单
                if (in_array($item['id'], $noSyncList)) {
                    continue;
                }

                $coopB    = $item['coopB'];
                $pftOrder = $item['pftOrder'];
                $apiOrder = $item['apiOrder'];
                $tid      = $item['bCode'];
                $ctime    = $item['cTime'];

                $ticketInfo = $otaModel->getTicketInfoById($tid, 'apply_did,landid');
                $lid        = $ticketInfo['landid'];
                $applyDid   = $ticketInfo['apply_did'];

                //检查是否异步返码产品
                $csysLandInfo = $otaModel->getCsysLandInfoByLid($lid, 'notice_code');
                if ($csysLandInfo['notice_code'] != 4) {
                    continue;
                }

                //向第三方查询消费码
                //加载配置文件   
                // 使用coopB去con_sys表查询sourceT
                //  2: include ota 方式
                //  3: jsonRpc 调用 ota_supplier
                $sysConfig = new SysConfig();
                $csysInfoByCoopBs = $sysConfig->getFieldByCoopB($coopB, 'sourceT');
                if (isset($csysInfoByCoopBs['sourceT']) && $csysInfoByCoopBs['sourceT'] == 3) {
                    $openOtaBiz = new \Business\Ota\Open\Order();
                    $apiOrderRes = $openOtaBiz->getApiOrderDetail($item['tempOrder'], $applyDid, $lid);
                    if ($apiOrderRes['code'] == 200) {
                        switch ($coopB) {
                            // 北京环球影视
                            case '252':
                                if ($apiOrderRes['data']['orderStatus'] != 'NORMAL') {
                                    $apiOrderRes['code'] = 201; // 出票状态有误,不能修改apiCode
                                }
                                break;
                        }
                    }
                } else {
                    $apiOrderRes = $otaOrderBiz->queryApiCode($coopB, $applyDid, $pftOrder, $apiOrder);
                }

                if ($apiOrderRes['code'] == 200) {
                    //查询成功 更新并且通知下游
                    $apiCode      = $apiOrderRes['data']['apiCode'];
                    $apiQrcodeUrl = '';
                    if (!empty($apiOrderRes['data']['qrcodeUrl'])) {
                        $apiQrcodeUrl = $apiOrderRes['data']['qrcodeUrl'];
                    }                  
                    $updateRes = $otaOrderBiz->updateApiCode($pftOrder, $apiOrder, $tid, $apiCode, true, $apiQrcodeUrl);
                    //更新失败
                    if ($updateRes['code'] == 201) {
                        //更新code_query_status的状态
                        $apiModel->updateCodeQueryStatus($item['id'], 0);
                        continue;
                    }
                    //将第三方系统的凭证码更新到订单扩展表
                    $orderOtaBiz->updateThirdCodeToAddon($pftOrder, $apiCode);

                } else {
                    //如果查询失败(包括超时)的话，将数据插入失败队列，然后统一由失败队列去重试
                    $action   = 6;
                    $tmpCoopB = 300; //异步返码的统一用这个
                    $nextTime = 300; //秒
                    $extData  = [ //重试需要的所有的数据
                        'coopB'    => $coopB,
                        'applyDid' => $applyDid,
                        'pftOrder' => $pftOrder,
                        'apiOrder' => $apiOrder,
                        'lid'      => $lid,
                        'tid'      => $tid,
                        'cTime'    => $ctime
                    ];

                    $res = $notifyModel->addNotify($pftOrder, $tmpCoopB, $action, $extData, $nextTime);

                    if (!$res) {
                        //更新code_query_status的状态
                        $apiModel->updateCodeQueryStatus($item['id'], 0);
                    }
                }
            }
        }
    }

    /**
     * 第三方订单状态查询
     *
     * Create by zhangyangzhen
     * Modify by dwer.cn
     * Date: 2018/4/19
     * Time: 16:50
     *
     * @param  int  $startTime  开始查询订单的时间
     * @param  int  $startTime  开始查询订单的时间
     * @param  int  $intervalTime  订单间隔多长时间查询一次
     * @param  array  $coopBArr  需要查询的渠道
     *
     */
    public function _baseQuery($startTimestamp, $endTimestamp, $intervalTime, $coopBArr = [25, 30, 31, 58, 68])
    {
        $PFT_infunction = $this->_getAllFunction();
        $cacheHandle    = Cache::getInstance('redis');

        $startTime = date('Y-m-d H:i:s', $startTimestamp);
        $endTime   = date('Y-m-d H:i:s', $endTimestamp);

        //120分钟前的时间
        $queryTime = time() - $intervalTime;

        $where = [
            'coopB'         => ['in', $coopBArr],
            'cTime'         => ['between', [$startTime, $endTime]],
            'select_time'   => ['ELT', $queryTime],
            'handleStatus'  => 0,
            'oStatus'       => ['in', '0,4,5,6,7,8,9'],
            'select_status' => 0,
        ];

        $apiModel   = $this->_getAllApiOrder();
        $otaModel   = $this->_getOtaQuery();
        $orderModel = new \Model\Order\OrderTools('slave');

        //每次只查询2000条数据
        $size  = 5500;
        $field = 'id,pftOrder,bCode,coopB,apiOrder,tempOrder,oStatus';
        $data  = $apiModel->getPollList($field, $where, 0, $size, 'id desc');
        $data  = $data ?: [];

        //因为查询订单很慢，所以首先就将订单的查询时间更新
        $allIdArr = array_column($data, 'id');
        $apiModel->updateQueryTimeBatch($allIdArr);

        //将部分验证的订单的状态一次性查询出来
        $partOrderArr     = [];
        $allCheckOrderArr = [];
        foreach ($data as $tmpVal) {
            if ($tmpVal['oStatus'] == 8) {
                $partOrderArr[] = strval($tmpVal['pftOrder']);
            }
        }

        if ($partOrderArr) {
            $baseOrderList = $orderModel->getOrderListNew($partOrderArr, 'ordernum, status');
            foreach ($baseOrderList as $tmpVal) {
                if ($tmpVal['status'] == 1) {
                    //订单在平台已经验证
                    $allCheckOrderArr[] = $tmpVal['ordernum'];
                }
            }
        }

        foreach ($data as $key => $val) {
            if (!$val['bCode']) {
                continue;
            }

            if ($val['apiOrder'] == 'tmp_' . $val['tempOrder']) {
                continue;
            }

            //这边发现很多订单在平台已经验证，但是在三方订单表还是部分验证的状态，导致重复去查询了
            //TODO:主要是AllFunction里面的方法对于分批验证参数的情况下处理有问题，后面安排处理
            if ($val['oStatus'] == 8 && in_array($val['pftOrder'], $allCheckOrderArr)) {
                //将三方系统表的订单状态修改为已经验证
                $apiModel->checkOrder($val['pftOrder']);
                pft_log('ota_order_check/part_check', json_encode([$val['pftOrder']]));

                continue;
            }

            $res = $otaModel->getTicketInfoById($val['bCode']);
            $aid = $res['apply_did'];
            $lid = $res['landid'];
            switch ($val['coopB']) {
                //更新同程订单
                case '25':
                    $startTime = microtime(true);

                    include_once '/var/www/html/ota/TongCheng/tc.class.php';
                    $tc             = new \TongCheng($aid);
                    $getOrderStatus = $tc->QueryOrder($val['apiOrder']);

                    $endTime  = microtime(true);
                    $costTime = $endTime - $startTime;

                    //记录查询日志
                    pft_log($this->_queryDebugPath, json_encode([
                        'query',
                        $costTime,
                        $val['coopB'],
                        $val['apiOrder'],
                        $val['pftOrder'],
                        $getOrderStatus,
                    ]));

                    if ($getOrderStatus == 'J' || $getOrderStatus == 'V') {
                        $apiModel->updateInfoById($val['id'], 1);
                    } else {
                        $apiModel->updateInfoById($val['id'], 0);
                    }
                    break;

                //更新去哪儿订单
                case '30':
                    $startTime = microtime(true);

                    include_once '/var/www/html/ota/To_Qunar/Qunar2B.class.php';
                    $qunar2B = new \Qunar2B($aid);

                    //初始化配置有问题
                    $initError = $qunar2B->_init();
                    if ($initError !== false) {
                        break;
                    }

                    $selQunarOrder = $qunar2B->orderDetail($val['tempOrder']);

                    $endTime  = microtime(true);
                    $costTime = $endTime - $startTime;

                    //记录查询日志
                    pft_log($this->_queryDebugPath, json_encode([
                        'query',
                        $costTime,
                        $val['coopB'],
                        $val['apiOrder'],
                        $val['pftOrder'],
                        $selQunarOrder,
                    ]));

                    if ($selQunarOrder == '已使用' || $selQunarOrder == '部分已使用') {
                        $apiModel->updateInfoById($val['id'], 1);
                    } else {
                        $apiModel->updateInfoById($val['id'], 0);
                    }
                    break;

                //更新方特订单
                case '31':
                    $startTime = microtime(true);

                    include_once '/var/www/html/ota/FangTe/ft.class.php';
                    $ft             = new \FangTe($aid);
                    $getOrderStatus = $ft->orderdetail($val['apiOrder']);

                    $endTime  = microtime(true);
                    $costTime = $endTime - $startTime;

                    //记录查询日志
                    pft_log($this->_queryDebugPath, json_encode([
                        'query',
                        $costTime,
                        $val['coopB'],
                        $val['apiOrder'],
                        $val['pftOrder'],
                        $getOrderStatus,
                    ]));

                    //订单状态（0:未出票 2:已出票  3：已取消）
                    if ($getOrderStatus == 2) {
                        $apiModel->updateInfoById($val['id'], 1);
                    } else {
                        $apiModel->updateInfoById($val['id'], 0);
                    }
                    break;

                //全面客流对接系统
                case '58':
                    $startTime = microtime(true);

                    include_once '/var/www/html/ota/Ots/Ots.class.php';
                    $ots    = new \Ots($aid);
                    $isUsed = $ots->detail($val['tempOrder'], $val['apiOrder']);

                    $endTime  = microtime(true);
                    $costTime = $endTime - $startTime;

                    //记录查询日志
                    pft_log($this->_queryDebugPath,
                        json_encode(['query', $costTime, $val['coopB'], $val['apiOrder'], $val['pftOrder'], $isUsed]));

                    if ($isUsed) {
                        $apiModel->updateInfoById($val['id'], 1);
                    } else {
                        $apiModel->updateInfoById($val['id'], 0);
                    }
                    break;

                //更新携程
                case '68':
                    $startTime = microtime(true);

                    include_once '/var/www/html/ota/Ctrip/Ctrip.class.php';
                    $ctrip  = new \Ctrip($aid);
                    $optRes = $ctrip->detectOrderInfo($val['tempOrder'], $val['apiOrder'], $val['oStatus']);

                    $endTime  = microtime(true);
                    $costTime = $endTime - $startTime;

                    //记录查询日志
                    pft_log($this->_queryDebugPath,
                        json_encode(['query', $costTime, $val['coopB'], $val['apiOrder'], $val['pftOrder'], $optRes]));

                    if (!empty($optRes['action'])) {
                        if ($optRes['action'] == 'check') {

                            //将其他的查询数据记录缓存
                            $cacheKey = $this->_cachePrefix . "check:{$val['pftOrder']}";
                            $cacheHandle->set($cacheKey, json_encode($optRes['data']), '', 7200);

                            $apiModel->updateInfoById($val['id'], 1);
                        } else if ($optRes['action'] == 'refund') {

                            //将其他的查询数据记录缓存
                            $cacheKey = $this->_cachePrefix . "refund:{$val['pftOrder']}";
                            $cacheHandle->set($cacheKey, json_encode($optRes['data']), '', 7200);

                            $apiModel->updateInfoById($val['id'], 2);
                        }
                    } else {
                        $apiModel->updateInfoById($val['id'], 0);
                    }
                    break;
                case "212":
                    $startTime  = microtime(true);
                    $openOtaBiz = new \Business\Ota\Open\Order();
                    $result     = $openOtaBiz->getApiOrderDetail($val['tempOrder'], $aid, $lid);

                    $endTime  = microtime(true);
                    $costTime = $endTime - $startTime;

                    $logArr = [
                        'key'      => '武汉数文订单查询',
                        'request'  => [$val['tempOrder'], $aid, $lid],
                        'response' => $result,
                        'costTime' => $costTime,
                    ];
                    //记录查询日志
                    pft_log($this->_queryDebugPath, json_encode($logArr));

                    if ($result['code'] == 200 && $result['data']['orderStatus'] == 2) {
                        $apiModel->updateInfoById($val['id'], 1);
                    } else {
                        $apiModel->updateInfoById($val['id'], 0);
                    }
                    break;
                case "252":
                    $startTime  = microtime(true);
                    $openOtaBiz = new \Business\Ota\Open\Order();
                    $result     = $openOtaBiz->getApiOrderDetail($val['tempOrder'], $aid, $lid);

                    $endTime  = microtime(true);
                    $costTime = $endTime - $startTime;

                    $logArr = [
                        'key'      => '北京环球影视订单查询',
                        'request'  => [
                            $val['tempOrder'],
                            $aid,
                            $lid
                        ],
                        'response' => $result,
                        'costTime' => $costTime,
                    ];
                    //记录查询日志
                    pft_log($this->_queryDebugPath, json_encode($logArr));

                    if ($result['code'] == 200 && $result['data']['orderStatus'] == 'COMPLETED') {
                        $apiModel->updateInfoById($val['id'], 1); // 1: 可以核销 0: 不可核销 2: 退票
                    } elseif ($result['code'] == 200 && $result['data']['orderStatus'] == 'REFUND') {
                        $apiModel->updateInfoById($val['id'], 2); // 1: 可以核销 0: 不可核销 2: 退票
                    } else {
                        $apiModel->updateInfoById($val['id'], 0);
                    }
                    break;
                case "257": 
                    $startTime  = microtime(true);
                    $openOtaBiz = new \Business\Ota\Open\Order();
                    $result     = $openOtaBiz->getApiOrderDetail($val['tempOrder'], $aid, $lid);

                    $endTime  = microtime(true);
                    $costTime = $endTime - $startTime;

                    $logArr = [
                        'key'      => '欧道V2订单查询',
                        'request'  => [
                            $val['tempOrder'],
                            $aid,
                            $lid
                        ],
                        'response' => $result,
                        'costTime' => $costTime,
                    ];
                    //记录查询日志

                    pft_log($this->_queryDebugPath, json_encode($logArr));
                    if ($result['code'] == 200 && $result['data']['orderStatus'] == 'Used') {
                        $apiModel->updateInfoById($val['id'], 1); // 1: 可以核销 0: 不可核销 2: 退票
                    } elseif ($result['code'] == 200 && $result['data']['orderStatus'] == 'Canceled') {
                        $apiModel->updateInfoById($val['id'], 2); // 1: 可以核销 0: 不可核销 2: 退票
                    } else {
                        $apiModel->updateInfoById($val['id'], 0);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 订单核销
     * Create by zhangyangzhen
     * Date: 2018/4/19
     * Time: 16:49
     */
    public function check()
    {
        $PFT_infunction = $this->_getAllFunction();
        $cacheHandle    = Cache::getInstance('redis');

        //获取过去最近3个月订单
        $howLong   = '-2 month';
        $timeBegin = date('Y-m-d H:i:s', strtotime($howLong));
        $where = [
            'coopB'         => ['in', '25,30,31,58,68,212'],
            'cTime'         => ['gt', $timeBegin],
            'handleStatus'  => 0,
            'oStatus'       => ['in', '0,4,5,6,7,8,9'],
            'select_status' => ['in', '1,2'], //已经可以进行核销的订单
        ];

        $apiModel = $this->_getAllApiOrder();
        $otaModel = $this->_getOtaQuery();

        //每次核销2000笔订单
        $size  = 2000;
        $field = 'id,pftOrder,bCode,coopB,apiOrder,tempOrder,select_status';
        $list  = $apiModel->getPollList($field, $where, 0, $size, 'cTime desc');
        $list  = $list ?: [];

        foreach ($list as $key => $val) {
            if (!$val['bCode']) {
                continue;
            }

            if ($val['apiOrder'] == 'tmp_' . $val['tempOrder']) {
                continue;
            }

            if ($val['coopB'] == 68) {
                //携程需要特殊处理
                if ($val['select_status'] == 1) {
                    //验证,携程有分批验证
                    $cacheKey = $this->_cachePrefix . "check:{$val['pftOrder']}";
                    $data     = $cacheHandle->get($cacheKey);

                    //如果获取数据失败的话,直接将查询状态回滚
                    if (!$data) {
                        pft_log($this->_errorPath, json_encode([$cacheKey, $data]));
                        continue;
                    }

                    $data        = json_decode($data, true);
                    $checkNumArr = $data['checkNumArr'];
                    $res         = $PFT_infunction->api_OrderCheck_IN($val['tempOrder'], $checkNumArr, false, false);

                    if ($res == 200) {
                        //删除缓存
                        $cacheHandle->rm($cacheKey);
                    }
                } else {
                    //退票结果,携程只有全部退
                    $cacheKey = $this->_cachePrefix . "refund:{$val['pftOrder']}";
                    $data     = $cacheHandle->get($cacheKey);

                    //如果获取数据失败的话,直接将查询状态回滚
                    if (!$data) {
                        pft_log($this->_errorPath, json_encode([$cacheKey, $data]));
                        continue;
                    }

                    $data   = json_decode($data, true);
                    $result = $data['result'];

                    // 获取用户订单状态数据
                    $orderReferModel = new \Model\Order\OrderTools();
                    $orderField      = 'status';
                    $orderInfoArr    = $orderReferModel->getOrderInfo($val['pftOrder'], $orderField);
                    $isUnderAudit    = true;
                    if ($orderInfoArr['status'] == 11) {
                        // 获取订单审核记录数据
                        $refundAuditModel = new \Model\Order\RefundAuditModel();
                        $refundAuditRes   = $refundAuditModel->isAllUnderAudit([$val['pftOrder']]);
                        if ($refundAuditRes === false) {
                            $isUnderAudit = false;
                        }
                    }

                    // 判断订单状态是否是待出票状态并且是没有审核中的记录
                    if ($isUnderAudit === false && $result === true) {
                        // 调用订单取消接口
                        $res = $PFT_infunction->Modify_Order($val['pftOrder'], 0, true, -1, false, '待出票查询出票失败取消订单');
                        if ($res == 100) {
                            $res = 200;
                        }   
                    } else {
                        $res = $PFT_infunction->apiOrderRefundAudit($val['tempOrder'], $result, false, false, '', -1);
                    }

                    if ($res == 200) {
                        //删除缓存
                        $cacheHandle->rm($cacheKey);
                    }
                }
            } else {
                $res = $PFT_infunction->api_OrderCheck_IN($val['pftOrder']);
            }

            //如果已经过期的订单，把票付通这边状态修改成已经验证，防止重复查询
            if ($res == 288) {
                $checkRes = $apiModel->checkOrder($val['pftOrder']);
                pft_log($this->_errorPath,
                    json_encode(['订单已经过期', $val['pftOrder'], $res, $checkRes], JSON_UNESCAPED_UNICODE));
            }

            //记录每次核销的状态
            pft_log($this->_checkDebugPath, json_encode(['check', $val['coopB'], $val['pftOrder'], $res]));
        }
    }

    /**
     * 获取all_api_order模型
     * Create by zhangyangzhen
     * Date: 2018/4/12
     * Time: 10:53
     * @return AllApiOrderModel
     */
    private function _getAllApiOrder()
    {
        if (!isset($this->_all_api_order)) {
            $this->_all_api_order = new AllApiOrderModel();
        }

        return $this->_all_api_order;
    }

    /**
     * 获取otaQuery模型
     * Create by zhangyangzhen
     * Date: 2018/4/12
     * Time: 16:41
     * @return OtaQueryModel
     */
    private function _getOtaQuery()
    {
        if (!isset($this->_ota_query)) {
            $this->_ota_query = new OtaQueryModel();
        }

        return $this->_ota_query;
    }

    /**
     * 获取allfunction模型
     * Create by zhangyangzhen
     * Date: 2018/4/19
     * Time: 11:20
     * @return \AllFunction
     */
    private function _getAllFunction()
    {
        if (!isset($this->PFT_infunction)) {
            include '/var/www/html/ota/ALLFunction.php';
            $this->PFT_infunction = new \AllFunction();
        }

        return $this->PFT_infunction;
    }
}
