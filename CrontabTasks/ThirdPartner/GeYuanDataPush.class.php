<?php
/**
 * 扬州市个园数据推送
 * <AUTHOR>
 * @date 2019/8/15
 */

namespace CrontabTasks\ThirdPartner;

use Library\Controller;
use Model\DataCollection\DataCollection;
use Model\Product\Land;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class GeYuanDataPush extends Controller
{

    //通用参数
    private $pushUrl  = 'http://api.jswlpt.com:8888/ms_scepassflow/api/realTimeInfo?';//接口地址
    private $nonceStr = 'kjiuhyhb3478765gt67hy98ujnza5qs6';//32位字符串

    //个园参数
    private $applyId           = 591889; //供应商id
    private $scenicId          = 'ec4dc19c-bd68-11e9-a65b-000c297b3bf7';//对接方景区id
    private $appId             = 'scenic110-0111';//appid
    private $statisticsPlaceId = '42005321002028@1';//编号
    private $key               = 'DD9E6AB5574821845F5343104802A8D5';//key
    private $logGe             = 'data_push/ge_yuan';//日志路径

    //何园参数
    private $applyIdHe           = 591906; //供应商id
    private $scenicIdHe          = 'ec4e4144-bd68-11e9-a65b-000c297b3bf7';//对接方景区id
    private $statisticsPlaceIdHe = '42005321002029@1';//编号
    private $logHe               = 'data_push/he_yuan';//日志路径
    private $appIdHe             = 'scenic116-0117';//appid
    private $keyHe               = '428FCF756BB345CAA27C06D14F9BD417';//key

    /**
     * 每5分钟推送数据
     * <AUTHOR>
     * @date 2019/8/15
     *
     * @return boolean
     */
    public function postData()
    {
        //个园请求
        $this->jianshuPostData($this->applyId, $this->scenicId, $this->statisticsPlaceId, $this->appId, $this->nonceStr,
            $this->key, $this->pushUrl, $this->logGe);

        //何园请求
        $this->jianshuPostData($this->applyIdHe, $this->scenicIdHe, $this->statisticsPlaceIdHe, $this->appIdHe,
            $this->nonceStr, $this->keyHe, $this->pushUrl, $this->logHe);

        return true;
    }

    /**
     * 江苏省平台数据通用推送
     * <AUTHOR>
     * @date 2020/5/27
     *
     * @param  int $applyId 供应商id
     * @param  string $scenicId 对接方景区id
     * @param  string $statisticsPlaceId 编号
     * @param  string $appId appid
     * @param  string $nonceStr 32位字符串
     * @param  string $key key
     * @param  string $pushUrl 接口地址
     * @param  string $log 日志路径
     *
     * @return boolean
     */
    public function jianshuPostData($applyId, $scenicId, $statisticsPlaceId, $appId, $nonceStr, $key, $pushUrl, $log)
    {
        $dataCollectionModel = new DataCollection();
        //$landModel           = new Land('slave');
        $timeNow             = date("Y-m-d H:i:s");//当前时间戳
        $timeHour            = date("Y-m-d") . ' 00:00:00';
        $enterTotal          = 0;//入园人数统计
        $leaveTotal          = 0;//出园人数统计

        $javaApi    = new \Business\CommodityCenter\Land();
        $landIdList = $javaApi->queryLandMultiQueryByApplyDid([$applyId], '', '', false, [1]);
        //$landIdList = $landModel->getLandField($applyId, 1, 'id,terminal');

        if (!empty($landIdList)) {
            $landArr = array_column($landIdList, 'id');
            //获取入园数据
            $enterDataRes = $dataCollectionModel->getEnterData($timeHour, $timeNow, $landArr);
            if ($enterDataRes) {
                $enterTotal = (int)$enterDataRes[0]['cnt'];
            }

            //获取出园数据
            $leaveDataRes = $dataCollectionModel->getLeaveData($timeHour, $timeNow, $landArr);
            if ($leaveDataRes) {
                $leaveTotal = (int)$leaveDataRes[0]['cnt'];
            }
        }

        //将查询到的总客流数据进一步处理成对方要的
        $pushData = [
            'scenicId' => $scenicId,
            'list'     => [
                [
                    'statisticsPlaceId' => $statisticsPlaceId,
                    'inCount'           => $enterTotal,
                    'outCount'          => $leaveTotal,
                    'personCount'       => $enterTotal - $leaveTotal,
                ],
            ],
        ];

        //url参数，按照ASCII 码从小到大排序（字典序）
        $urlParams = "appId=" . $appId . "&nonce_str=" . $nonceStr . "&total=" . $enterTotal;
        //生成签名
        $sign = md5($urlParams . "&key=" . $key);
        $sign = strtoupper($sign);
        //请求url
        $requestUrl = $pushUrl . $urlParams . "&sign=" . $sign;

        $postData = json_encode($pushData);

        $pushRes = curl_post($requestUrl, $postData, 80, 25, '/api/curl_post',
            ['Content-Type:application/json']);

        $logData = [
            'requestUrl' => $requestUrl,
            'pushData'   => $pushData,
            'pushRes'    => $pushRes,
            'date'       => date('Y-m-d H:i:s'),
        ];
        pft_log($log, json_encode($logData, JSON_UNESCAPED_UNICODE));

        return true;
    }

}