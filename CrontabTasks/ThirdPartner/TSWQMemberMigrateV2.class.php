<?php
/**
 * 汤山温泉会员卡数据迁移
 * <AUTHOR> Li
 */
namespace CrontabTasks\ThirdPartner;

use Library\Controller;
use Library\Tools\Helpers;
use Model\Member\Member;
use Model\Product\ParkCard;
use Model\Product\BaseCard;
use Library\MulityProcessHelper;
use Business\JavaApi\Fund\CardApi;

//权限判断
if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

class TSWQMemberMigrateV2 extends Controller 
{

    //csv文件路径
    const  CSV_PATH         = '/home/<USER>/memberCard.xlsx';
    //供应商id
    const SID               = 6970;
    //卡类型  园区卡
    const CARD_TYPE         = 5;
    //开几个子进程
    const PROCESS_NUM       = 10;

    /**
     * 入口
     * <AUTHOR> Li
     * @date   2018-10-17
     */
    public function run() 
    {
        $data = $this->_getMemberArr(self::CSV_PATH, 'xlsx');

        if (!$data) {
            exit('没有数据');
        }
        
        //总条数
        $count = count($data);

        defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
        $task = new MulityProcessHelper($this, self::PROCESS_NUM, $count / self::PROCESS_NUM);
        $task->run($data, 'account');
    }

    /**
     * 获取导入信息
     * <AUTHOR> Li
     * @date   2018-10-15
     */
    private function _getMemberArr($path, $type) 
    {
        //引入文件
        $this->_requireExcelFile();

        //获取Excel文件
        $excelArray = $this->_readExcelDetail($path, $type);

        if (empty($excelArray)) {
            exit('没有数据');
        }

        return $excelArray;
    }

    /**
     * 读取excel
     * @param file $filename 文件
     * @param sring $fileType 文件后缀 .xlsx
     * @return array
     */
    private function _readExcelDetail($filename, $fileType)
    {
        if ($fileType == 'xlsx') {
            $objReader = \PHPExcel_IOFactory::createReader('Excel2007');
            $objReader->setReadDataOnly(true);
            $objPHPExcel = $objReader->load($filename);
            $objPHPExcel->setActiveSheetIndex(0);
            $objWorksheet = $objPHPExcel->getActiveSheet();
            $hightestrow = $objWorksheet->getHighestRow();
            $highestColumn = $objWorksheet->getHighestColumn();
            $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);
            $excelData = array();
        } else {
            $objReader = \PHPExcel_IOFactory::createReader('Excel5');
            $objReader->setReadDataOnly(true);
            $objPHPExcel = $objReader->load($filename);
            $objWorksheet = $objPHPExcel->getActiveSheet();
            $hightestrow = $objWorksheet->getHighestRow();
            $highestColumn = $objWorksheet->getHighestColumn();
            $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);
            $excelData = array();
        }

        for ($row = 2; $row <= $hightestrow; $row++) {
            for ($col = 0; $col < $highestColumnIndex; $col++) {
                $excelData[$row-1][] = (string) $objWorksheet->getCellByColumnAndRow($col, $row)->getValue();
            }
        }

        return $excelData;

    }

    /**
     * 引入
     */
    private function _requireExcelFile()
    {
        Helpers::composerAutoload();
        //include_once(HTML_DIR . '/Service/Library/Business/PHPExcel/PHPExcel.php');

    }

    /**
     * 数据迁移
     * <AUTHOR> Li
     * @date   2018-10-17
     * @param  array     $data 要处理的数据
     */
    public function runWorker($data , $type) 
    {
        if ($type == 'account' && !empty($data)) {
            $this->_createAccountAction($data);
        } else {
            exit('没有数据');
        }
    }

    /**
     * 生成新账号
     * 
     * <AUTHOR> Li
     * @date   2018-10-17
     * @param  array $data 需要转换成一卡通的用户信息
     * @param  string $data[0] 会员卡号（实体卡号）
     * @param  string $data[1] 会员卡剩余余额
     * @param  string $data[2] 会员卡需要绑定对应的一卡通物理卡号
     * @return bool  
     *
     */
    private function _createAccountAction($data)
    {
        //加载会员模型
        $memberModel = new Member();
        $ParkModel   = new ParkCard();

        foreach ($data as $key => $value) {
            //传入校验参数
            $params = $this->_checkData($value);
            if ($params === false) {
                continue;
            }

            //TODO:: 1.创建新一卡通用户并激活
            $result = $ParkModel->create($params['physicsNo'], self::SID, self::SID, '', $params['cardNo']);

            //激活失败
            if ($result === false) {
                //$ParkModel->rollback();
                pft_log('tswq/fail', json_encode(['激活失败 - 创建卡失败', self::SID, $params['cardNo'], $params['physicsNo'], $result], JSON_UNESCAPED_UNICODE));
                continue;
            }

            //TODO:: 2.生成一卡通用户与供应商授信记录
            //初始化授信记录
            $memberId    = $result['member_id'];
            $checkCredit = $memberModel->checkCreditExitst($memberId, self::SID);
            if (!$checkCredit) {
                pft_log('tswq/fail', json_encode(['激活失败 - 授信关系设置失败', self::SID, $memberId, $params['physicsNo'], $checkCredit], JSON_UNESCAPED_UNICODE));
                continue;
            }

            //TODO:: 3.汤山会员卡余额转一卡通授信余额
            $cardApi = new CardApi();
            //交易备注
            $remark  = '唐山会员卡余额转票付通一卡通授信';
            //内部交易号
            $orderId = 'YKT' . $memberId . time();
            
            $res     = $cardApi->cashRecharge($memberId, self::SID, self::SID, $orderId, abs($params['money'] * 100), $remark);

            //失败之后 记录日志
            if ($res['code'] != 200) {
                pft_log('tswq/fail', json_encode(['唐山会员卡余额转票付通一卡通授信失败', $memberId, self::SID, $memberId, $orderId, abs($params['money'] * 100), $remark, $res], JSON_UNESCAPED_UNICODE));
                continue;
            }

            pft_log('tswq/success', json_encode(['处理成功的会员', '供应商ID: ' . self::SID, '会员ID: ' . $memberId, '，一卡通余额：' . abs($params['money'] * 100), '一卡通实体卡号: ' . $params['cardNo'], '物理卡号: ' . $params['physicsNo']], JSON_UNESCAPED_UNICODE));
        }
    }
     /**
     * 检测数据是否合法
     * 
     * <AUTHOR> Li
     * @date   2018-10-17
     * @param  array $data 需要转换成一卡通的用户信息
     * @return bool  
     *
     */
    private function _checkData($data)
    {
        $params['cardNo']    = $data[0];    //会员卡对应一卡通实体卡号
        $params['money']     = $data[1];    //会员卡剩余金额对应一卡通授信金额
        $params['physicsNo'] = $data[2];    //一卡通物理卡号
        
        if (empty($params['cardNo'])) {
            pft_log('tswq/fail', json_encode(['卡号不能为空', json_encode($params)], JSON_UNESCAPED_UNICODE));
            return false;
        }

        if (!is_numeric($params['money'])) {
            pft_log('tswq/fail', json_encode(['金额错误', json_encode($params)], JSON_UNESCAPED_UNICODE));
            return false;
        }

        if (!$this->_checkPhysicsNo($params['physicsNo'])) {
            pft_log('tswq/fail', json_encode(['物理卡号格式错误或已存在', json_encode($params)], JSON_UNESCAPED_UNICODE));
            return false;
        }

        return $params;
    }

    /**
     * 验证物理卡号有效性
     * 
     * <AUTHOR> Li
     * @date   2018-10-17
     * @param  array $physicsNo 物理卡号
     * @return bool  
     *
     */
    private function _checkPhysicsNo($physicsNo)
    {
        //获取模型
        $ParkModel = new ParkCard();

        //判断物理卡号格式是否合法
        if (!preg_match('/^[0-9a-zA-Z]{1,20}$/i', $physicsNo)) {
            pft_log('tswq/fail', json_encode(['物理卡号格式错误', json_encode($physicsNo)], JSON_UNESCAPED_UNICODE));
            return false;
        }

        //验证物理卡是否存在
        $result = $ParkModel->getCardInfo($physicsNo);
        if ($result !== false) {
            pft_log('tswq/fail', json_encode(['物理卡号已存在', json_encode($physicsNo)], JSON_UNESCAPED_UNICODE));
            return false;
        }

        return true;
    }

}
