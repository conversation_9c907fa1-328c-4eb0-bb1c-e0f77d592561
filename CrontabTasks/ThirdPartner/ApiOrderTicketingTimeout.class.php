<?php
/**
 * 短信通知用户第三方异步返码的订单 脚本运行时间前20分钟到前10分钟的订单
 * <AUTHOR>
 * @since 2018-05-21
 *
 */

namespace CrontabTasks\ThirdPartner;

use Business\JsonRpcApi\MessageService\MessageService;
use Library\Cache\Cache;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Util\EnvUtil;

class ApiOrderTicketingTimeout
{
    // 设置超时时间 单位s
    private $_timeOut       = 600;
    private $_maxTimeoutNum = 10;

    /**
     * 十分钟执行一次
     * 短信通知第三方订单出票超时得订单
     *
     */
    public function smsNoticeTicketingTimeout()
    {
        $allApiOrderModel = new \Model\Ota\AllApiOrderModel();
        $cacheRedis       = Cache::getInstance('redis');
        $cacheKey         = "smsNoticeTicketingTimeoutTime";

        $beginTimeStamp = $cacheRedis->get($cacheKey);
        $endTimeStamp   = time() - $this->_timeOut;
        if (empty($beginTimeStamp)) {
            $beginTimeStamp = $endTimeStamp - $this->_timeOut;
        } else {
            $beginTimeStamp = $beginTimeStamp + 1;
        }
        $beginTime = date('Y-m-d H:i:s', $beginTimeStamp);
        $endTime   = date('Y-m-d H:i:s', $endTimeStamp);
        $cacheRedis->set($cacheKey, $endTimeStamp, '', '3600');

        pft_log('order/AsyncCodeJob/ticketing_sms_notice',
            '运行：' . json_encode([$beginTime, $endTime], JSON_UNESCAPED_UNICODE));

        $listArr = $allApiOrderModel->getListByStatus(10, $beginTime, $endTime,
            'tempOrder,apiOrder,id,cTime,oStatus,fid,coopB, handleStatus, bCode');

        if (empty($listArr)) {
            return false;
        }
        // $memberIdArr    = array_unique(array_column($listArr, 'fid'));
        $coopBArr = array_unique(array_column($listArr, 'coopB'));
        $bCodeArr = array_column($listArr, 'bCode');

        if (!$bCodeArr || !$coopBArr) {
            return false;
        }

        $sysConfigModel = new \Model\Ota\SysConfig();
        $ticketModel    = new \Model\Product\Ticket();

        $tidArr        = array_unique($bCodeArr);
        $ticketInfoArr = $ticketModel->getTicketList($tidArr, 'id,landid,apply_did');

        if (empty($ticketInfoArr)) {
            pft_log('order/AsyncCodeJob/ticketing_sms_notice',
                '查询门票信息出错：' . json_encode($tidArr, JSON_UNESCAPED_UNICODE));
        }

        $memberIdArr = array_unique(array_column($ticketInfoArr, 'apply_did'));
        $landidArr = array_unique(array_column($ticketInfoArr, 'landid'));

        $bCodeRepeatArr = array_count_values($bCodeArr);
        // 十分钟内同一门票出票超时 -- 景区异步将改成不发码状态
        $arriveMaxNumLidArr = [];
        foreach ($bCodeRepeatArr as $bCode => $bCodeTimeoutNum) {
            if ($bCodeTimeoutNum > $this->_maxTimeoutNum && isset($ticketInfoArr[$bCode]['landid'])) {
                $arriveMaxNumLidArr[] = $ticketInfoArr[$bCode]['landid'];
            }
        }

        // 获取商家配置
        $otaOrderBiz      = new \Business\Ota\Order();
        $result = $otaOrderBiz->getOrderTimeoutSmsConfigMap($memberIdArr);
        $smsConfigMap = $result['code'] == 200 ? $result['data'] : [];


        // tid 数组找到lid数组
        if ($arriveMaxNumLidArr) {
            // 更新这些景点为不返码
            $lidArr          = array_unique($arriveMaxNumLidArr);
            $upNoticeCodeRes = $sysConfigModel->upNoticeCodeByLids($lidArr, 3);
            pft_log('order/AsyncCodeJob/ticketing_sms_notice',
                '批量更新景点不发码：' . json_encode($lidArr, JSON_UNESCAPED_UNICODE));
            if (!$upNoticeCodeRes) {
                pft_log('order/AsyncCodeJob/ticketing_sms_notice',
                    '批量更新不发码出错或已修改：' . json_encode($lidArr, JSON_UNESCAPED_UNICODE));
            } else {
                $logRes = (new \Business\Ota\Upstream\SystemLog())->addTimeOutSystemLog($lidArr);
                if ($logRes['code'] != 200 || (isset($logRes['data']['error']) && count($logRes['data']['error']) > 0)) {
                    pft_log('order/AsyncCodeJob/ticketing_sms_notice',
                        '批量写入不发码操作记录异常或存在失败的情况：' . json_encode(['lidArr' => $lidArr, 'res' => $logRes], JSON_UNESCAPED_UNICODE));
                }
            }
        }

        $sysInfoArr = $sysConfigModel->getCsysInfoByCoopBs($coopBArr, 'coopB,name', true);
        // 获取这些用户的手机信息
        $memberModel   = new \Model\Member\Member();
        $memberInfoArr = $memberModel->getMemberInfoByMulti($memberIdArr, 'id', 'id,mobile', true);
        // 获取绑定配置
        $sysResArr      = $sysConfigModel->getCsysByLids($landidArr, "lid,csysid,notice_code");

        // 获取用户配置
        $smsConfigs = [];
        // 十分钟内的订单短信一起发了把
        $data = [];
        foreach ($listArr as $listVal) {
            $memberId = isset($ticketInfoArr[$listVal['bCode']]['apply_did']) ? $ticketInfoArr[$listVal['bCode']]['apply_did'] : 0;
            // 判断是否配置了短信通知, 如果配置不通知跳过
            if (isset($smsConfigMap[$memberId])) {
                $landid = $ticketInfoArr[$listVal['bCode']]['landid'];
                $noticeCode = $sysResArr[$landid]['notice_code'];
                $noticeType = $smsConfigMap[$memberId]['notice_type'] == "" ? [] : explode(',', $smsConfigMap[$memberId]['notice_type']);
                if ($smsConfigMap[$memberId]['status'] != 1 || !in_array($noticeCode, $noticeType)) {
                    continue;
                }
            }
            if (!empty($memberId)) {
                $data[$listVal['coopB']][$memberId][] = $listVal['tempOrder'];
            }
        }

        if (empty($data)) {
            return false;
        }

        foreach ($data as $coopBKey => $dataVal) {
            $systemName = $sysInfoArr[$coopBKey] ? $sysInfoArr[$coopBKey]['name'] : '';
            foreach ($dataVal as $userKey => $orderVal) {
                $userMobile = $memberInfoArr[$userKey] ? $memberInfoArr[$userKey]['mobile'] : '';
                if (isset($smsConfigMap[$userKey]) && !empty($smsConfigMap[$userKey]['notice_mobile'])) {
                    $userMobile = $smsConfigMap[$userKey]['notice_mobile'];
                }
                $smsResArr  = $this->_smsMsg($userMobile, $orderVal, $systemName);

                $msg = 'params:' . json_encode([$beginTime, $endTime, $userMobile, $systemName, $orderVal],
                        JSON_UNESCAPED_UNICODE);
                $msg .= 'res:' . json_encode($smsResArr, JSON_UNESCAPED_UNICODE);
                // 记录日志发送日志
                pft_log('order/AsyncCodeJob/ticketing_sms_notice', $msg);
            }
        }
        exit('完成');
    }

    /**
     * 通知十分钟内未出票通知的订单
     *
     * @param $mobile       通知手机号
     * @param array $orderArr     订单号数组
     * @param $systemName   三方系统名
     *
     * @return array
     */
    private function _smsMsg($mobile, $orderArr, $systemName)
    {
        if (!($mobile && $orderArr && $systemName)) {
            return ['code' => 203, 'msg' => '参数错误'];
        }
        $orderStr = implode(',', $orderArr);
        $messageServiceApi = \Library\Container::pull(MessageService::class);
        [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'receive_code_timeout', $mobile,
            [$orderStr, strval($systemName)], 0, '', '出票超时通知');
        if ($approval) {
            if (!EnvUtil::isRelease()) {
                pft_log('message_service', json_encode(['出票超时通知', __METHOD__, $mobile, [$orderStr, strval($systemName)], $res], JSON_UNESCAPED_UNICODE));
            }
        } else {
            $smsLib   = SmsFactory::getFactory($mobile, '', true);
            $orderStr = implode(',', $orderArr);

            $res = $smsLib->receiveOtaCodeTimeout($orderStr, $systemName);
        }
        if ($res['code'] == 200) {
            return ['code' => 200, 'msg' => '短信发送成功'];
        }

        return ['code' => 204, 'msg' => '短信发送失败'];
    }
}