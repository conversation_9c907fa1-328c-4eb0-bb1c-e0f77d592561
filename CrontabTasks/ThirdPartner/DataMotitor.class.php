<?php
/**
 * *监控推送到九天达的凭证码
 * 异常情况是这样的：有时候下单的时候没有发送Relation请求，导致凭证码没有推送到九天
 * 这边实时检测如果出现这样的情况，重新进行推送
 *
 * <AUTHOR>
 * @date 2016-11-17
 *
 */
namespace CrontabTasks\ThirdPartner;

use Library\Controller;
use Library\Resque\Queue;
use Library\Tools\Helpers;
use Model\Order\OrderSubmit;
use Model\Order\OrderTools;
use Model\Product\Land;
use Model\TradeRecord\OnlineTrade;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}
class DataMotitor extends Controller
{
    private $_logPath = 'motitor/jtd_code';

    //最近几分钟
    private $_min = 2;

    //最大的错误数
    private $_errorNum = 10;

    //需要推送监控到阿里云的类型，具体需要推送的数据由相应的方法提供
    private $_pushType = [
        'redis', //redis相关的监控
    ];

    public function __construct()
    {
        //运行时间不做限制
        set_time_limit(0);
    }

    /**
     * 定时清除1个月之前的数据——订单邮件通知
     */
    public function clearOrderMailList()
    {
        $os = new OrderSubmit();
        $os->clearMail();
    }

    /**
     * 监控推送到九天达的凭证码
     * 异常情况是这样的：有时候下单的时候没有发送Relation请求，导致凭证码没有推送到九天
     * 这边实时检测如果出现这样的情况，重新进行推送
     *
     * <AUTHOR>
     * @date   2016-11-17
     *
     * @return
     */
    public function runCodePush()
    {
        $lastId = $this->_getLastId();
        if (!$lastId) {
            //没有需要处理的数据
            return false;
        }

        $field = 'id,pftOrder,handleStatus';
        $where = [
            'coopB'        => 17,
            'apiCode'      => '',
            'handleStatus' => ['in', [0, 2]],
            'id'           => ['gt', $lastId],
        ];

        $apiModel = new \Model\Ota\AllApiOrderModel();
        $apiList  = $apiModel->selectInfo($field, $where, '0,20');

        if (!$apiList) {
            //没有需要处理的数据
            return false;
        }

        $refundModel = new \Model\Order\RefundAuditModel();
        $ticketModel = new \Model\Product\Ticket();

        $tmpId    = '';
        $orderArr = [];

        foreach ($apiList as $item) {
            $ordernum   = strval($item['pftOrder']);
            $orderArr[] = $ordernum;

            $tmpId = $item['id'];
        }

        //票类信息
        $ticketArr = [];

        //将需要处理的数据写入日志
        pft_log($this->_logPath, json_encode($orderArr), 'month');

        //加载ota处理文件
        if (!defined('COOL_OTA_DIR')) {
            $otaGatewayFile = HTML_DIR . '/ota/common/OTAGateway.class.php';
            include $otaGatewayFile;
        }

        //获取订单相关信息
        $list = $refundModel->getInfoForAuditCheck($orderArr, false);
        $list = $list && is_array($list) ? $list : []; // Invalid argument supplied for foreach()

        foreach ($list as $item) {
            $apiCode   = $item['code'];
            $payStatus = $item['pay_status'];
            $ordernum  = $item['ordernum'];
            $tid       = $item['tid'];
            $lid       = $item['lid'];

            if (isset($ticketArr[$ordernum])) {
                $applyDid = $ticketArr[$ordernum];
            } else {
                $ticketInfo = $ticketModel->getTicketInfoById($tid, 'apply_did');
                if (!$ticketInfo) {
                    continue;
                }

                $applyDid             = $ticketInfo['apply_did'];
                $ticketArr[$ordernum] = $applyDid;
            }

            $params = [
                'Ordern'    => $ordernum,
                'UUcode'    => $apiCode,
                'PayStatus' => $payStatus,
                'Ltitle'    => '',
                'Ttitle'    => '',
                'LandId'    => $lid,
                'ApplyId'   => $applyDid,
                'Action'    => 'Relation',
                'From'      => 'DataMotitor', //来源标识
            ];

            $res = \OTAGateway::dispatch($params);

            //将处理结果写入日志
            $params['res'] = $res;
            pft_log($this->_logPath, json_encode($params), 'month');
        }

        //加最后的ID写入文件
        $this->_lastQueryId($tmpId);
    }

    /**
     * 数据库错误日志提醒
     * <AUTHOR>
     * @date   2017-01-13
     *
     * @return
     */
    public function sqlErrorNotice()
    {
        $machineIp = $this->_getIp();

        //检测新的数据库模型
        $contents = $this->_getContents('new', 500);
        $res      = $this->_logCheck($contents);
        if ($res) {
            //发送微信提醒
            $msg = "[{$machineIp}] Model方法下产生SQL错误过多，请到sql_error下排查。";
            $this->_sendWx($msg);
        }

        //检测cli模式下新的数据库模型
        $contents = $this->_getContents('cli', 500);
        $res      = $this->_logCheck($contents);
        if ($res) {
            //发送微信提醒
            $msg = "[{$machineIp}] Model方法下产生SQL错误过多，请到cli/sql_error下排查。";
            $this->_sendWx($msg);
        }

        //检测旧的sql模型下
        $contents = $this->_getContents('wsdl', 500);
        $res      = $this->_logCheck($contents);
        if ($res) {
            //发送微信提醒
            $msg = "[{$machineIp}] 旧的数据库查询方法产生SQL错误过多，请到sql_error/wsdl_xxxx下排查。";
            $this->_sendWx($msg);
        }
    }

    public function checkPayResult()
    {
        //TODO::获取票付通的支付记录
        $onlineTrade       = new OnlineTrade('slave');
        $start_time        = date('Y-m-d H:i:s', strtotime("-11 mins"));
        $end_time          = date('Y-m-d H:i:s', strtotime("-1 mins"));
        $unpay_orders      = $onlineTrade->getUnpayList(null, $start_time, $end_time);
        $payResultCheckUrl = [
            0 => 'http://pay.12301.cc/r/pay_Alipay/payResultCheck',
            1 => 'http://pay.12301.cc/r/pay_WxPay/payResultCheck',
            8 => 'http://pay.12301.cc/r/pay_CmbcWxPay/payResultCheck',
        ];
        if ($unpay_orders) {
            //$wepayLib = new WxPayLib(PFT_WECHAT_APPID);
            foreach ($unpay_orders as $items) {
                $pay_scen  = $items['pay_method'] == 2 ? 1 : 2;
                $payParams = http_build_query([
                    'ordernum' => $items['out_trade_no'],
                    'pay_scen' => $pay_scen,
                ]);
                $res = curl_post($payResultCheckUrl[$items['sourceT']], $payParams);
                pft_log('cli_task/pay_result_check', "sourceT:{$items['sourceT']},params:$payParams,res:$res");
                /*$result_array = $wepayLib->query($items['out_trade_no']);
            if ($result_array['err_code'] == 'ORDERNOTEXIST') {
            $result_array = $wepayLib->query('qr_' . $items['out_trade_no']);
            }
            if ($result_array['trade_state'] == 'SUCCESS') {
            if (strlen($items['pay_method']) == 1) {
            $dir = 'recharge/';
            $url = "pay.12301.cc/recharge/wepay.php";
            } elseif($items['pay_method'] == 2 ) {
            $dir = 'data/';
            $url = "pay.12301.cc/order/mobile_wepay_notify.php";
            }
            $dir .= substr($result_array['time_end'], 0, 4) . '/' . substr($result_array['time_end'], 4, 2) . '/' . substr($result_array['time_end'], 6, 2);
            $tip = "建议处理方式：到[173]服务器下的/alidata/log/site/wepay/$dir.log查找日志,提交原始XML数据到$url";
            Helpers::sendWechatWarningMessage(
            "各位小主不好了,有一笔微信支付交易失败!"
            . "\n交易时间:{$result_array['time_end']}"
            . "\n交易金额:{$result_array['total_fee']}分"
            . "\n微信交易号:{$result_array['transaction_id']}"
            . "\n票付通订单号:{$items['out_trade_no']}\n$tip",
            [],
            "微信支付交易失败", "服务器:[" . $this->_getIp() . "]|10mins/per");
            }*/
            }
        } else {
            pft_log('cli_task/pay_result_check', 'trade ok');
        }

    }

    /**
     * 监控数据上报阿里云监控
     * <AUTHOR>
     * @date   2017-03-13
     *
     * @return
     */
    public function aliPush()
    {
        //需要上报的数据数组
        $collectData = [];

        foreach ($this->_pushType as $type) {
            $method = '_' . $type . 'PushData';

            if (method_exists($this, $method)) {
                $tmpData = $this->$method();
                if ($tmpData && is_array($tmpData)) {
                    $collectData = array_merge($collectData, $tmpData);
                }
            }
        }

        $aliMonitor = new \Library\Business\AliMonitor();
        foreach ($collectData as $item) {
            $metricName = $item['metricName'];
            $value      = $item['value'];
            $unit       = $item['unit'];
            $dimensions = $item['dimensions'];

            $aliMonitor->collect($metricName, $value, $unit, $dimensions);
        }

        //最后进行推送
        $res = $aliMonitor->put($accountType = 1);
        if ($res) {
            echo '上报成功';
            exit();
        } else {
            echo '上报失败';
            exit();
        }
    }


    /**
     * 获取最后的ID
     * <AUTHOR>
     * @date   2016-11-17
     *
     * @return
     */
    private function _getLastId()
    {
        $lastId = $this->_lastQueryId();
        if ($lastId < 1) {
            //如果还没有上次查询ID, 从数据库里面获取一天前的相关数据
            $apiModel = new \Model\Ota\AllApiOrderModel();

            $seconds   = 86400;
            $timestamp = date('Y-m-d H:i:s', time() - $seconds);
            $where     = [
                'coopB'        => 17,
                'apiCode'      => '',
                'handleStatus' => ['in', [0, 2]],
                'cTime'        => ['gt', $timestamp],
            ];

            $field   = 'id';
            $orderBy = 'id asc';

            $info = $apiModel->getInfo($field, $where, $orderBy);
            if ($info) {
                return $info['id'];
            } else {
                return false;
            }
        } else {
            return $lastId;
        }
    }

    /**
     * 将最新的一次查询的ID写入文件
     * <AUTHOR>
     * @date   2016-11-17
     *
     * @param   $lastId 最后一次ID
     * @return
     */
    private function _lastQueryId($lastId = false)
    {
        $path = BASE_LOG_DIR . '/' . $this->_logPath;
        $file = $path . '/last_id.log';

        if (!file_exists($path)) {
            $res = mkdir($path, 0777, true);
        }

        //如果是写入数据
        if ($lastId) {
            file_put_contents($file, intval($lastId));
            return true;
        }

        $id = 0;
        if (file_exists($file)) {
            $id = file_get_contents($file);
        }

        return $id;
    }

    /**
     * 检测错误
     * <AUTHOR>
     * @date   2017-01-13
     *
     * @param  $contents 内容数组
     * @return
     */
    private function _logCheck($contents)
    {
        if (!$contents || !is_array($contents)) {
            return false;
        }

        //所有日志的时间
        $timeArr = [];

        //从日志文件中获取时间戳
        foreach ($contents as $item) {
            //取出时间戳
            $match = [];
            preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $item, $match);
            if ($match && isset($match[0])) {
                $timeArr[] = strtotime($match[0]);
            }
        }

        $tmpErrorNum = 0;
        $lastTime    = time() - $this->_min * 60;

        foreach ($timeArr as $tmpTime) {
            if ($tmpTime >= $lastTime) {
                $tmpErrorNum += 1;
            }
        }

        //判断错误次数是不是已经超过了
        if ($tmpErrorNum >= $this->_errorNum) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取日志文件的最后几行
     * <AUTHOR>
     * @date   2017-01-13
     *
     * @param  string $type 类型
     * @param  int $num 几行
     * @return array
     */
    private function _getContents($type = 'new', $num = 100)
    {
        if ($type == 'new') {
            //新的模型的日志
            $logfile = BASE_LOG_DIR . '/sql_error/' . date('Y') . '/' . date('m') . '/' . date('d') . '.log';
        } else if ($type == 'cli') {
            //新的模型的日志 - 控制台下面日志
            $logfile = BASE_LOG_DIR . '/cli/sql_error/' . date('Y') . '/' . date('m') . '/' . date('d') . '.log';
        } else if ($type == 'wsdl') {
            //之前的查询日志   wsdl_20170105.log
            $logfile = BASE_LOG_DIR . '/sql_error/wsdl_' . date('Ymd') . '.log';
        } else {
            return false;
        }

        $contents = tail_file($logfile, $num);
        return $contents;
    }

    /**
     * 获取服务器的内网IP
     * <AUTHOR>
     * @date   2017-01-13
     *
     * @return
     */
    private function _getIp()
    {
        @exec('/sbin/ifconfig eth0 | sed -n \'s/^ *.*addr:\\([0-9.]\\{7,\\}\\) .*$/\\1/p\'', $arr);
        $res = isset($arr[0]) ? $arr[0] : '';
        return $res;
    }

    /**
     *  发送微信
     * <AUTHOR>
     * @date   2016-09-02
     *
     * @param  $openList
     * @param  $message
     * @return
     */
    private function _sendWx($message)
    {
        $openArr = load_config('api_warning_openid');
        //接口参数
        Helpers::sendWechatWarningMessage($message, $openArr, 'OTA告警信息', '');
        return true;
    }

    /**
     * 需要推送的redis的数据
     * <AUTHOR>
     * @date   2017-03-13
     *
     * @return
     */
    private function _redisPushData()
    {
        $collectData = [];

        $tplData = [
            'metricName' => 'redis_monitor',
            'value'      => 0,
            'unit'       => 'Megabytes',
            'dimensions' => [
                'used_memory'             => '',
                'used_memory_rss'         => '',
                'mem_fragmentation_ratio' => '',
                'connected_clients'       => '',
            ],
        ];

        //连接redis获取运行情况
        $redisHandle = \Library\Cache\RedisCache::Connect();
        $info        = $redisHandle->info();

        foreach ($info as $key => $item) {
            $data = $tplData;
            if ($key == 'used_memory') {
                //转换为M
                $data['value']                     = $item / 1000000;
                $data['dimensions']['used_memory'] = 'used_memory';
            } else if ($key == 'used_memory_rss') {
                //转换为M
                $data['value']                         = $item / 1000000;
                $data['dimensions']['used_memory_rss'] = 'used_memory_rss';
            } else if ($key == 'mem_fragmentation_ratio') {
                $data['value']                                 = $item;
                $data['dimensions']['mem_fragmentation_ratio'] = 'mem_fragmentation_ratio';
            } else if ($key == 'connected_clients') {
                $data['value']                           = $item;
                $data['dimensions']['connected_clients'] = 'connected_clients';
            } else {
                continue;
            }

            $collectData[] = $data;
        }

        return $collectData;
    }
}
