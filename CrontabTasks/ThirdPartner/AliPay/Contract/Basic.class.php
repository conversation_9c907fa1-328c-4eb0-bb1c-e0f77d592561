<?php

namespace CrontabTasks\ThirdPartner\AliPay\Contract;

use CrontabTasks\ThirdPartner\AliPay\Exceptions\InvalidArgumentException;
use CrontabTasks\ThirdPartner\AliPay\Exceptions\InvalidResponseException;

abstract class Basic
{
    /**
     * 支持配置
     * @var DataArray
     */
    protected $config;

    /**
     * 当前请求数据
     * @var DataArray
     */
    protected $options;

    /**
     * DzContent数据
     * @var DataArray
     */
    protected $params;

    /**
     * 正常请求网关
     * @var string
     */
    protected $gateway = '';

    /**
     * AliPay constructor.
     * @param array $options
     */
    public function __construct(array $options)
    {
        $this->params = new DataArray([]);
        //$this->config = new DataArray($options);
        if (empty($options['appid'])) {
            throw new InvalidArgumentException("Missing Config -- [appid]");
        }
        //if (empty($options['public_key'])) {
        //    throw new InvalidArgumentException("Missing Config -- [public_key]");
        //}
        //if (empty($options['private_key'])) {
        //    throw new InvalidArgumentException("Missing Config -- [private_key]");
        //}

        $config = load_config($options['appid'], 'alipay');
        if (empty($config)) {
            throw new InvalidArgumentException("Invalid Config -- [appid]");
        }
        $this->gateway = $config['gatewayUrl'];
        if (!file_exists($config['merchant_public_key_file'])) {
            throw new InvalidArgumentException("Missing Config -- [public_key]");
        }
        if (!file_exists($config['merchant_private_key_file'])) {
            throw new InvalidArgumentException("Missing Config -- [private_key]");
        }
        if (!empty($options['debug'])) {
            $this->gateway = 'https://openapi.alipaydev.com/gateway.do?charset=utf-8';
        }
        $options['public_key'] = file_get_contents($config['merchant_public_key_file']);
        $options['private_key'] = file_get_contents($config['merchant_private_key_file']);

        $this->config = new DataArray($options);
        $this->options = new DataArray([
            'app_id'    => $this->config->get('appid'),
            'charset'   => empty($options['charset']) ? 'utf-8' : $options['charset'],
            'format'    => 'JSON',
            'version'   => '1.0',
            'sign_type' => empty($config['sign_type']) ? 'RSA2' : $config['sign_type'],
            'timestamp' => date('Y-m-d H:i:s'),
        ]);
    }

    /**
     * @return string
     */
    public function getGateway(): string
    {
        return $this->gateway;
    }

    /**
     * @return DataArray
     */
    public function getOptions(): DataArray
    {
        return $this->options;
    }

    /**
     * 获取数据签名
     * @return string
     */
    protected function getSign()
    {
        $content = wordwrap($this->trimCert($this->config->get('private_key')), 64, "\n", true);
        $string = "-----BEGIN RSA PRIVATE KEY-----\n{$content}\n-----END RSA PRIVATE KEY-----";
        if ($this->options->get('sign_type') === 'RSA2') {
            openssl_sign($this->getSignContent($this->options->get(), true), $sign, $string, OPENSSL_ALGO_SHA256);
        } else {
            openssl_sign($this->getSignContent($this->options->get(), true), $sign, $string, OPENSSL_ALGO_SHA1);
        }
        return base64_encode($sign);
    }

    /**
     * 去除证书前后内容及空白
     * @param string $sign
     * @return string
     */
    protected function trimCert($sign)
    {
        return preg_replace(['/\s+/', '/\-{5}.*?\-{5}/'], '', $sign);
    }

    /**
     * 数据签名处理
     * @param array $data 需要进行签名数据
     * @param boolean $needSignType 是否需要sign_type字段
     * @return bool|string
     */
    private function getSignContent(array $data, $needSignType = false)
    {
        list($attrs,) = [[], ksort($data)];
        if (isset($data['sign'])) unset($data['sign']);
        if (empty($needSignType)) unset($data['sign_type']);
        foreach ($data as $key => $value) {
            if ($value === '' || is_null($value)) continue;
            array_push($attrs, "{$key}={$value}");
        }
        return join('&', $attrs);
    }

    /**
     * 数据包生成及数据签名
     * @param array $options
     */
    protected function applyData($options)
    {
        $this->options->set('biz_content', json_encode($this->params->merge($options), 256));
        $this->options->set('sign', $this->getSign());
    }

    /**
     * 请求接口并验证访问数据
     *
     * @param array $options
     *
     * @return array|boolean
     * @throws InvalidResponseException
     */
    protected function getResult($options)
    {
        $this->applyData($options);
        $method = str_replace('.', '_', $this->options['method']) . '_response';
        //print_r($this->options->get());
        //print_r($this->config->get());
        $data = json_decode(Tools::get($this->gateway, $this->options->get()), true);
        //var_dump($data);
        //exit;
        if (!isset($data[$method]['code']) || $data[$method]['code'] !== '10000') {
            throw new InvalidResponseException(
                "Error: " .
                (empty($data[$method]['code']) ? '' : "{$data[$method]['msg']} [{$data[$method]['code']}]\r\n") .
                (empty($data[$method]['sub_code']) ? '' : "{$data[$method]['sub_msg']} [{$data[$method]['sub_code']}]\r\n"),
                $data[$method]['code'], $data
            );
        }
        return $data[$method];
    }

    /**
     * 应用数据操作
     * @param array $options
     * @return mixed
     */
    abstract public function apply($options);
}