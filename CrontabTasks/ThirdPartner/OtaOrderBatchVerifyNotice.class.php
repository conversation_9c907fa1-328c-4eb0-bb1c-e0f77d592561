<?php

namespace CrontabTasks\ThirdPartner;

use Library\Controller;
use Library\Resque\Queue;
use Model\Ota\ApiBatchNoticeTaskModel;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class OtaOrderBatchVerifyNotice extends Controller
{
    /**
     * php /var/www/html/Service/Crontab/runNew.php ThirdPartner/OtaOrderBatchVerifyNotice run
     *
     * 订单批量推送入队列
     * <AUTHOR>
     * @date 2022-09-16
     */
    public function run(): void
    {
        $apiBatchNoticeTaskModel = new ApiBatchNoticeTaskModel();
        $startedAt               = date('Y-m-d H:i:s', strtotime('-1 day'));

        $field         = 'id,aid,operator_id';
        $readyTaskList = $apiBatchNoticeTaskModel->getReadyTaskList($startedAt, $field);
        if (!empty($readyTaskList)) {
            $taskIdList = [];

            foreach ($readyTaskList as $task) {
                $taskId     = $task['id'];
                $aid        = $task['aid'];
                $operatorId = $task['operator_id'];

                Queue::push('cooperation_system', 'OtaOrderBatchVerifyNotice_Job', [
                        'task_id'     => $taskId,
                        'aid'         => $aid,
                        'operator_id' => $operatorId,
                        'request_id'  => md5(date('YmdHis', time()) . rand(100000, 999999) . uniqid()),
                    ]
                );

                $result = $apiBatchNoticeTaskModel->updateTaskStatus($taskId, 0, 1);
                if ($result) {
                    $taskIdList[] = $taskId;
                }
            }

            if (!empty($taskIdList)) {

                pft_log('ota_order_batch_verify_notice', json_encode([
                    'desc'         => '定时任务处理中',
                    'task_id_list' => $taskIdList,
                ]));
                return;
            }
        }

        pft_log('ota_order_batch_verify_notice', json_encode([
            'desc' => '定时任务处理数据不存在',
        ]));
    }
}