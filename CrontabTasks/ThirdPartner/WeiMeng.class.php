<?php

/**
 *定时刷新微盟token
 */

namespace CrontabTasks\ThirdPartner;

use Business\OtaApi\OtaBase;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

const __OTA_REFRESH_WEIMENG_TOKEN__ = 'ThirdPartner/WeiMengToken/referToken';

class <PERSON><PERSON>eng extends OtaBase
{
    /**
     * 定时刷新微盟token
     * User: Liucm
     * Date: 2021/3/4
     * Time: 18:12
     */
    public function refreshToken()
    {
        $startTime = microtime(true);
        $res       = $this->call(__OTA_REFRESH_WEIMENG_TOKEN__, []);
        $endTime   = microtime(true);
        $costTime  = $endTime - $startTime;
        var_dump([$res, $costTime]);
        exit();
    }
}