<?php
/**
 * 楠溪江推送数据
 * <AUTHOR>
 * Date: 2018/7/25
 * Time: 16:36
 */

namespace CrontabTasks\ThirdPartner;

use Library\Controller;
use Library\MulityProcessHelper;
use Model\Order\TeamOrderSearch;
use Model\Member\Member;
use Model\Order\OrderRefer;
use Model\Product\Area;
use Model\Order\OrderTrack;
use Model\Product\Land;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class NanPostData extends Controller
{

    // 分页
    private $pieceNum = 2000;

    const PRINT_POST_URL = 'http://68.nxj.cn/orderData/save.jspx';
    const KEY            = 'nxj152939171340896062';

    private $teamModel;
    private $memberModel;
    private $orderModel;
    private $areaModel;
    private $trackModel;
    private $landModel;
    private $_idCardProvince;
    private $_idCardCity;
    private $_telProvince;
    private $_telCity;

    /**
     * 推送数据
     * <AUTHOR>
     * @date 2018-07-25
     */
    public function postData()
    {
        $params = $GLOBALS['argv'];

        if (isset($params[3])) {
            $day  = $params[3];
            $tmp  = strtotime($day);
            $date = $tmp == false ? date('Y-m-d') : date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $dateList = $this->getDateList($date);
        $task     = new MulityProcessHelper($this, 4, 1);
        $task->run($dateList, 'team');
    }

    /**
     * 推送普通订单, 因为数据库保存的日期问题 不使用多进程
     * <AUTHOR>
     * @date 2018-07-25
     */
    public function postNormalData()
    {
        $params = $GLOBALS['argv'];

        if (isset($params[3])) {
            $day  = $params[3];
            $tmp  = strtotime($day);
            $date = $tmp == false ? date('Y-m-d') : date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $this->runNormalWorker($date);
    }

    /**
     * 任务
     * <AUTHOR>
     * @date 2018-07-31
     */
    public function runWorker($date, $type = false)
    {
        if (!is_array($date) || !isset($date[0]) || !isset($date[1]) || !strtotime($date[0]) || !strtotime($date[1]) ||
            (strtotime($date[1]) - strtotime($date[0]) > 3600 * 24)) {
            @pft_log('nanxijiang/fail', '日期格式错误:' . json_encode($date), 'day');
            exit('日期格式错误');
        }

        $code = 200;
        $msg  = '';

        switch ($type) {
            case 'team':
                $this->runTeamWorker($date);
                break;
            case 'normal':
                break;
            default:
                $this->runTeamWorker($date);
                break;
        }
    }

    /**
     * 普通订单推送
     * <AUTHOR>
     * @date 2018-07-31
     */
    public function runNormalWorker($date)
    {
        if (!strtotime($date)) {
            @pft_log('nanxijiang/fail', '日期格式错误:' . json_encode($date), 'day');
            exit('日期格式错误');
        }

        $date = date('Ymd', strtotime($date));

        // 从报表中获取数据
        $statisticsModel = new \Model\Report\Statistics();
        $count           = $statisticsModel->getCheckV2CountByFid($date, 954117);
        $totalPage       = ceil($count / $this->pieceNum);
        for ($page = 1; $page <= $totalPage; $page++) {
            // 获取验证报表列表
            $list = $statisticsModel->getCheckV2ListByFid($date, 954117, 'orders_info, revoke_orders_info', $page,
                $this->pieceNum);

            // 验证订单数组
            $checkArr = [];
            // 撤销订单数组
            $revokeArr = [];

            // 遍历列表
            foreach ($list as $item) {
                $ordersInfo = json_decode($item['orders_info'], true);
                $revokeInfo = json_decode($item['revoke_orders_info'], true);

                foreach ($ordersInfo as $value) {

                    if (isset($checkArr[$value[0]])) {
                        $checkArr[$value[0]] += $value[1];
                    } else {
                        $checkArr[$value[0]] = $value[1];
                    }
                }

                foreach ($revokeInfo as $value) {

                    if (isset($revokeArr[$value[0]])) {
                        $revokeArr[$value[0]] += $value[1];
                    } else {
                        $revokeArr[$value[0]] = $value[1];
                    }
                }
            }

            $checkArrKey = array_keys($checkArr);
            $checkArrKey = array_map(function ($key) {
                return strval($key);
            }, $checkArrKey);

            // 获取所有的订单的订单信息
            $memberModel      = $this->getMemberModel();
            $orderToolModel   = new \Model\Order\OrderTools();
            $ordersInfoArr    = $orderToolModel->getOrderInfo($checkArrKey,
                'ordernum, aid, lid, tid, tnum, tprice, ordertel, ordertime, playtime, personid, ordermode, member');
            $memberIdArr      = array_column($ordersInfoArr, 'member');
            $aidArr           = array_column($ordersInfoArr, 'aid');
            $allMemberIdArr   = array_merge($aidArr, $memberIdArr);
            $allMemberIdArr   = array_unique($allMemberIdArr);
            $memberInfoArr    = $memberModel->getMemberAccountByIdArr($allMemberIdArr);
            $ordersInfoRes    = [];
            $otaOrdersInfoRes = [];
            foreach ($ordersInfoArr as $item) {
                // 剔除团队订单
                if ($item['ordermode'] == 24) {
                    continue;
                }
                $item['memberName'] = isset($memberInfoArr[$item['member']]) ? $memberInfoArr[$item['member']] : '';
                $item['aidName']    = isset($memberInfoArr[$item['aid']]) ? $memberInfoArr[$item['aid']] : '';
                // ota和普通订单区分推送
                if ($item['ordermode'] == 20) {
                    $otaOrdersInfoRes[$item['ordernum']] = $item;
                } else {
                    $ordersInfoRes[$item['ordernum']] = $item;
                }
            }

            // 获取所有产品的名称
            $lidArr = array_column($ordersInfoArr, 'lid');
            //$landModel  = $this->getLandModel();
            //$landInfoArr = $landModel->getLandInfoByMuli($lidArr, 'id, title');

            $javaAPi     = new \Business\CommodityCenter\Land();
            $landInfoArr = $javaAPi->queryLandMultiQueryById($lidArr);
            $landInfoRes = [];
            foreach ($landInfoArr as $item) {
                $landInfoRes[$item['id']] = $item['title'];
            }

            // 推送普通旅行社订单验证数据
            if (!empty($ordersInfoRes)) {
                $this->postNormalCheckData($ordersInfoRes, $landInfoRes, $checkArr, 'travel');
            }
            // 推送普通ota订单验证数据
            if (!empty($otaOrdersInfoRes)) {
                $this->postNormalCheckData($otaOrdersInfoRes, $landInfoRes, $checkArr, 'ota');
            }
            // 推送撤销数据
            $this->postNormalRevokeData($revokeArr);
        }

    }

    /**
     * 团队订单任务
     * <AUTHOR>
     * @date 2018-07-31
     */
    public function runTeamWorker($date)
    {
        $teamModel   = $this->getTeamModel();
        $memberModel = $this->getMemberModel();

        $total = $teamModel->getTotalCheckNum($date[0], $date[1]);
        //根据订单号分批查询订单数据
        $totalPage = ceil($total / $this->pieceNum);

        for ($i = 1; $i <= $totalPage; $i++) {

            //通过订单ID获取订单信息
            $data = $teamModel->getTotalCheckInfo($date[0], $date[1], $i, $this->pieceNum);
            if (empty($data)) {
                continue;
            }

            // 主订单数组集合
            $mainOrderNum = array_column($data, 'order_id');
            // 获取所有主订单的信息
            $mainOrdersInfo = $this->getMainOrderInfo($mainOrderNum);
            $mainOrdersRes  = [];
            foreach ($mainOrdersInfo as $item) {
                // 去除不是楠溪江订单
                if (!$this->checkNanXiJiangTicket($item['aid'])) {
                    continue;
                }

                $mainOrdersRes[$item['ordernum']] = $item;
            }

            $mainOrderNum = array_keys($mainOrdersRes);

            if (empty($mainOrderNum)) {
                // 无楠溪江数据
                return;
            }

            // 查出团单主订单和子订单关联的数据
            $linkInfo = $this->getSonOrderInfo($mainOrderNum);
            // 主订单和子订单关联的数组
            $teamOrderLinkSon = $linkInfo['team_order'];
            // 所有的子订单号
            $sonOrderNum = $linkInfo['son_order'];
            $sonOrderNum = array_map(function ($orderNum) {
                return strval($orderNum);
            }, $sonOrderNum);
            // 获取所有的子订单的订单信息
            $orderToolModel   = new \Model\Order\OrderTools();
            $sonOrdersInfo    = $orderToolModel->getOrderInfo($sonOrderNum,
                'ordernum, aid, lid, tid, tnum, tprice, ordermode');
            $sonOrdersInfoRes = [];
            foreach ($sonOrdersInfo as $item) {
                $sonOrdersInfoRes[$item['ordernum']] = $item;
            }

            // 获取所有产品的名称
            $lidArr      = array_unique(array_column($sonOrdersInfo, 'lid'));
            //$landModel   = $this->getLandModel();
            //$landInfoArr = $landModel->getLandInfoByMuli($lidArr, 'id, title');

            $javaAPi     = new \Business\CommodityCenter\Land();
            $landInfoArr = $javaAPi->queryLandMultiQueryById($lidArr);
            $landInfoRes = [];
            foreach ($landInfoArr as $item) {
                $landInfoRes[$item['id']] = $item['title'];
            }

            // 获取所有订单中的aid对应的dname, 以及导游id
            $mids = array_merge(array_column($mainOrdersInfo, 'guide'), array_column($sonOrdersInfo, 'aid'));
            $mids = array_merge($mids, array_column($mainOrdersInfo, 'fid'));
            $mids = array_unique($mids);
            // 已经处理好了  id => dname
            $memberRes = $memberModel->getMemberAccountByIdArr($mids);

            //处理数据并推送
            $this->handle($mainOrdersRes, $sonOrdersInfoRes, $memberRes, $teamOrderLinkSon, $landInfoRes);
        }

    }

    /**
     * 普通订单 验证数据推送
     * <AUTHOR>
     * @date 2018-08-01
     *
     * @param array $orderInfos 普通订单信息数组
     * @param array $landInfoRes 产品id => 产品name
     * @param array $checkArr 报表获取的 验证订单=>验证数量
     */
    private function postNormalCheckData($orderInfos, $landInfoRes, $checkArr, $type = 'travel')
    {
        // 推送验证数据
        $postData = [
            // 秘钥
            'key'      => self::KEY,
            // 类型 固定travel
            'type'     => $type,
            'isCancel' => 'false',
            'list'     => [],
        ];

        // 遍历所有订单信息
        foreach ($checkArr as $ordernum => $tnum) {
            // 跳过没有订单信息的数据
            if (!isset($orderInfos[$ordernum])) {
                continue;
            }

            $postSon = [];

            $postSon[] = [
                // 景区名称
                'name'        => isset($landInfoRes[$orderInfos[$ordernum]['lid']]) ? $landInfoRes[$orderInfos[$ordernum]['lid']] : '',
                // 景区id
                'waresId'     => isset($orderInfos[$ordernum]['lid']) ? (int)$orderInfos[$ordernum]['lid'] : 0,
                // 门票id
                'sonTypeId'   => isset($orderInfos[$ordernum]['tid']) ? (int)$orderInfos[$ordernum]['tid'] : 0,
                // 数量
                'num'         => $tnum,
                // 手机唯一编号
                'machineBill' => isset($orderInfos[$ordernum]['ordertel']) ? $orderInfos[$ordernum]['ordertel'] : '',
                // 单价
                'price'       => isset($orderInfos[$ordernum]['tprice']) ? (double)round($orderInfos[$ordernum]['tprice'] / 100,
                    2) : 0,
                // 总金额
                'amount'      => (double)round($orderInfos[$ordernum]['tprice'] * $orderInfos[$ordernum]['tnum'] / 100,
                    2),
                // 订单号
                'ordernum'    => 'son_' . $ordernum,
            ];

            $sex = '';
            $age = '';
            // 根据用户的身份证信息获取用户的客源地
            if (isset($orderInfos[$ordernum]['personid']) && $orderInfos[$ordernum]['personid']) {
                $provinceCityArr = $this->_getProvinceCityByCardId($orderInfos[$ordernum]['personid']);
                $sex             = substr($orderInfos[$ordernum]['personid'],
                    (strlen($orderInfos[$ordernum]['personid']) == 15 ? -1 : -2), 1) % 2 ? true : false;
                $ageArr          = \Library\Tools::getAgeFromIdCard($orderInfos[$ordernum]['personid'], time());
                $age             = $ageArr['y'];
                $province        = $provinceCityArr['province'];
                $city            = $provinceCityArr['city'];
            } elseif (isset($orderInfos[$ordernum]['ordertel']) && $orderInfos[$ordernum]['ordertel']) {
                $provinceCityArr = $this->_getProvinceCityByTel($orderInfos[$ordernum]['ordertel']);
                $province        = $provinceCityArr['province'];
                $city            = $provinceCityArr['city'];
            } else {
                $province = '未知';
                $city     = '未知';
            }

            // 推送的主订单数据
            $postMain = [
                // 主订单号
                'ordernum'   => $ordernum,
                // 旅行社名称
                'travelName' => !empty($orderInfos[$ordernum]['aidName']) ? $orderInfos[$ordernum]['aidName'] : '楠溪江风景区',
                // ota 用户名
                'otaName'    => isset($orderInfos[$ordernum]['memberName']) ? $orderInfos[$ordernum]['memberName'] : '',
                // 来源-省
                'province'   => $province,
                // 来源-市
                'city'       => $city,
                // 手机号
                'orderPhone' => isset($orderInfos[$ordernum]['ordertel']) ? $orderInfos[$ordernum]['ordertel'] : '',
                // 身份证
                'card'       => isset($orderInfos[$ordernum]['personid']) ? $orderInfos[$ordernum]['personid'] : '',
                // 性别
                'sex'        => $sex,
                // 年龄
                'age'        => $age,
                // 导游姓名
                'gudideName' => '',
                // 下单时间
                'createTime' => isset($orderInfos[$ordernum]['ordertime']) ? $orderInfos[$ordernum]['ordertime'] : '',
                // 游完时间， 因游完时间数据库记录的是 yyyy-MM-dd  需要转化为yyyy-MM-dd HH:mm:ss
                'usingTime'  => isset($orderInfos[$ordernum]['playtime']) ? date('Y-m-d H:i:s',
                    strtotime($orderInfos[$ordernum]['playtime'])) : '',
                // 订单总票数
                'votes'      => $tnum,
                // 子订单信息
                'orderItem'  => $postSon,
            ];

            $postData['list'][] = $postMain;

            if (count($postData['list']) >= 5) {
                $res = curl_post(self::PRINT_POST_URL, json_encode($postData), 80, 25, '/api/curl_post', [
                    'Content-Type: text/html;charset=UTF-8',
                ]);
                // 结果处理
                $res = $this->resHandle($res);
                if ($res['code'] != 200) {
                    // 记录失败日志
                    @pft_log('nanxijiang/fail', '楠溪江推送数据失败:' . $res['msg'] . ';推送数据:' . json_encode($postData), 'day');
                } else {
                    @pft_log('nanxijiang/success', '楠溪江推送数据成功:' . $res['msg'] . ';推送数据:' . json_encode($postData),
                        'day');
                }
                // 清空数据
                $postData['list'] = [];
            }

        }

        if (!empty($postData['list'])) {
            $res = curl_post(self::PRINT_POST_URL, json_encode($postData), 80, 25, '/api/curl_post', [
                'Content-Type: text/html;charset=UTF-8',
            ]);

            // 结果处理
            $res = $this->resHandle($res);
            if ($res['code'] != 200) {
                // 记录失败日志
                @pft_log('nanxijiang/fail', '楠溪江推送数据失败:' . $res['msg'] . ';推送数据:' . json_encode($postData), 'day');
            } else {
                @pft_log('nanxijiang/success', '楠溪江推送数据成功:' . $res['msg'] . ';推送数据:' . json_encode($postData), 'day');
            }
        }

    }

    /**
     * 普通订单 撤销数据推送, 目前直接推送订单就好
     * <AUTHOR>
     * @date 2018-08-01
     *
     * @param array $revokeArr 撤销报表数据  订单号=>数量
     */
    private function postNormalRevokeData($revokeArr)
    {
        // 推送撤销数据
        $postData = [
            // 秘钥
            'key'      => self::KEY,
            // 类型 固定travel
            'type'     => 'travel',
            'isCancel' => 'true',
            'list'     => [],
        ];

        $postData['list'] = array_keys($revokeArr);

        if (!empty($postData['list'])) {
//            @pft_log('nanxijiang/test', '楠溪江推送数据:'. json_encode($postData), 'day');
            $res = curl_post(self::PRINT_POST_URL, json_encode($postData), 80, 25, '/api/curl_post', [
                'Content-Type: text/html;charset=UTF-8',
            ]);

            // 结果处理
            $res = $this->resHandle($res);
            if ($res['code'] != 200) {
                // 记录失败日志
                @pft_log('nanxijiang/fail', '楠溪江推送数据失败:' . $res['msg'] . ';推送数据:' . json_encode($postData), 'day');
            } else {
                @pft_log('nanxijiang/success', '楠溪江推送数据成功:' . $res['msg'] . ';推送数据:' . json_encode($postData), 'day');
            }
        }

    }

    /**
     * 处理数据并推送
     * <AUTHOR>
     * @date 2018-07-25
     *
     * @param array $mainOrdersRes 主订单信息   ordernum => item
     * @param array $sonOrdersInfoRes 子订单信息 ordernum => item
     * @param array $memberRes 用户信息  id => dname
     * @param array $teamOrderLinkSon 主订单和子订单关联的数组
     * @param array $landInfoRes 产品id => 产品name
     */
    private function handle($mainOrdersRes, $sonOrdersInfoRes, $memberRes, $teamOrderLinkSon, $landInfoRes)
    {
        // 要推送的数据   10条推送一次
        $postData = [
            // 秘钥
            'key'      => self::KEY,
            // 类型 固定travel
            'type'     => 'travel',
            'isCancel' => 'false',
            'list'     => [],
        ];

        foreach ($mainOrdersRes as $mainOrdernum => $mainOrder) {

            // 主订单跟子订单关联数组
            $sonOrders = $teamOrderLinkSon[$mainOrdernum];

            // 子订单所有数量总和；
            $tnum = 0;

            // 推送的子订单信息
            $postSon = [];
            foreach ($sonOrders as $item) {
                $num       = isset($sonOrdersInfoRes[$item]['tnum']) ? (int)$sonOrdersInfoRes[$item]['tnum'] : 0;
                $tnum      += $num;
                $postSon[] = [
                    // 景区名称
                    'name'        => isset($landInfoRes[$sonOrdersInfoRes[$item]['lid']]) ? $landInfoRes[$sonOrdersInfoRes[$item]['lid']] : '',
                    // 景区id
                    'waresId'     => isset($sonOrdersInfoRes[$item]['lid']) ? (int)$sonOrdersInfoRes[$item]['lid'] : 0,
                    // 门票id
                    'sonTypeId'   => isset($sonOrdersInfoRes[$item]['tid']) ? (int)$sonOrdersInfoRes[$item]['tid'] : 0,
                    // 数量
                    'num'         => $num,
                    // 手机唯一编号
                    'machineBill' => isset($sonOrdersInfoRes[$item]['ordertel']) ? $sonOrdersInfoRes[$item]['ordertel'] : '',
                    // 单价
                    'price'       => isset($sonOrdersInfoRes[$item]['tprice']) ? (double)round($sonOrdersInfoRes[$item]['tprice'] / 100,
                        2) : 0,
                    // 总金额
                    'amount'      => (double)round($sonOrdersInfoRes[$item]['tprice'] * $sonOrdersInfoRes[$item]['tnum'] / 100,
                        2),
                    // 订单号
                    'ordernum'    => $item,
                ];
            }

            $province = '';
            $city     = '';
            // 通过用户身份证获取手机号来获取用户的客源地信息
            if (isset($mainOrder['province']) && $mainOrder['province']) {
                $province = $this->getAreaModel()->getProvinceAndCityNameById($mainOrder['province']);
            }
            // 通过用户身份证获取手机号来获取用户的客源地信息
            if (isset($mainOrder['city']) && $mainOrder['city']) {
                $city = $this->getAreaModel()->getProvinceAndCityNameById($mainOrder['city']);
            }
            if (empty($province) && empty($city) && isset($mainOrder['contacttel']) && $mainOrder['contacttel']) {
                $provinceCityArr = $this->_getProvinceCityByTel($mainOrder['contacttel']);
                $province        = $provinceCityArr['province'];
                $city            = $provinceCityArr['city'];
            }

            // 推送的主订单数据
            $postMain = [
                // 主订单号
                'ordernum'   => $mainOrdernum,
                // 旅行社名称
                'travelName' => !empty($memberRes[$mainOrder['fid']]) ? $memberRes[$mainOrder['fid']] : '楠溪江风景区',
                // 来源-省
                'province'   => $province,
                // 来源-市
                'city'       => $city,
                // 导游姓名
                'gudideName' => isset($memberRes[$mainOrder['guide']]) ? $memberRes[$mainOrder['guide']] : $mainOrder['ordername'],
                // 下单时间
                'createTime' => isset($mainOrder['ordertime']) ? $mainOrder['ordertime'] : '',
                // 游完时间， 因游完时间数据库记录的是 yyyy-MM-dd  需要转化为yyyy-MM-dd HH:mm:ss
                'usingTime'  => date('Y-m-d H:i:s', strtotime($mainOrder['playtime'])),
                // 订单总票数
                'votes'      => isset($tnum) ? (int)$tnum : 0,
                // 子订单信息
                'orderItem'  => $postSon,
            ];

            $postData['list'][] = $postMain;

            if (count($postData['list']) >= 5) {
                // @pft_log('nanxijiang/test', '楠溪江推送数据:'. json_encode($postData), 'day');
                $res = curl_post(self::PRINT_POST_URL, json_encode($postData), 80, 25, '/api/curl_post', [
                    'Content-Type: text/html;charset=UTF-8',
                ]);
                // 结果处理
                $res = $this->resHandle($res);
                if ($res['code'] != 200) {
                    // 记录失败日志
                    @pft_log('nanxijiang/fail', '楠溪江推送数据失败:' . $res['msg'] . ';推送数据:' . json_encode($postData), 'day');
                } else {
                    @pft_log('nanxijiang/success', '楠溪江推送数据成功:' . $res['msg'] . ';推送数据:' . json_encode($postData),
                        'day');
                    @pft_log('nanxijiang/success', '楠溪江推送原始数据:' . json_encode($postData), 'day');
                }
                // 清空数据
                $postData['list'] = [];
            }

        }

        if (!empty($postData['list'])) {
//            @pft_log('nanxijiang/test', '楠溪江推送数据:'. json_encode($postData), 'day');
            $res = curl_post(self::PRINT_POST_URL, json_encode($postData), 80, 25, '/api/curl_post', [
                'Content-Type: text/html;charset=UTF-8',
            ]);

            // 结果处理
            $res = $this->resHandle($res);
            if ($res['code'] != 200) {
                // 记录失败日志
                @pft_log('nanxijiang/fail', '楠溪江推送数据失败:' . $res['msg'] . ';推送数据:' . json_encode($postData), 'day');
            } else {
                @pft_log('nanxijiang/success', '楠溪江推送数据成功:' . $res['msg'] . ';推送数据:' . json_encode($postData), 'day');
            }
        }

    }

    /**
     * 结果处理
     * <AUTHOR>
     * @date 2018-06-27
     */
    private function resHandle($res)
    {
        if (empty($res)) {
            return ['code' => 204, 'msg' => '请求未响应'];
        }

        $res = json_decode($res, true);

        if ($res['status'] != 200) {
            return ['code' => 204, 'msg' => $res['message']];
        }

        return ['code' => 200, 'msg' => $res['message']];
    }

    /**
     * 获取子订单数据
     *
     * <AUTHOR>
     * @date   2018-06-21
     */
    private function getSonOrderInfo($orderArr)
    {
        $orderArr       = array_map(function ($v) {
            return strval($v);
        }, $orderArr);
        $teamOrderModel = $this->getTeamModel();

        $orderInfo = $teamOrderModel->getSonOrderInfoByMainOrderNum($orderArr);
        $orderRes  = [];
        $allOrder  = [];
        if (is_array($orderInfo)) {
            foreach ($orderInfo as $item) {
                $orderRes[$item['main_ordernum']][] = $item['son_ordernum'];
                $allOrder[]                         = $item['son_ordernum'];
            }
        }

        return ['team_order' => $orderRes, 'son_order' => $allOrder];
    }

    /**
     * 获取团队订单数据
     *
     * <AUTHOR>
     * @date   2018-06-21
     */
    private function getMainOrderInfo($orderArr)
    {
        $orderArr = array_map(function ($v) {
            return strval($v);
        }, $orderArr);

        $teamOrderModel = $this->getTeamModel();
        $mainOrderInfo  = $teamOrderModel->getMainOrderInfoByOrderNum($orderArr);

        return $mainOrderInfo;
    }

    /**
     * 拆解日期
     */
    private function getDateList($date)
    {
        if (!strtotime($date)) {
            throw new \Exception($date . '日期格式错误');
        }

        $date = date('Y-m-d', strtotime($date));

        $dateList = [
            $date . ' 00:00:00',
            $date . ' 08:59:59',
            $date . ' 09:00:00',
            $date . ' 12:59:59',
            $date . ' 13:00:00',
            $date . ' 16:59:59',
            $date . ' 17:00:00',
            $date . ' 23:59:59',
        ];

        return $dateList;
    }

    private function getTrackModel()
    {
        if (!$this->trackModel) {
            $this->trackModel = new OrderTrack($isSlave = true);
        }

        return $this->trackModel;
    }

    /**
     * 判断是否楠溪江订单号
     * <AUTHOR>
     * @date 2018-06-26
     */
    private function checkNanXiJiangTicket($aid)
    {

        if ($aid == 954117) {
            return true;
        }

        return false;
    }

    private function getTeamModel()
    {
        if (empty($this->teamModel)) {
            $this->teamModel = new TeamOrderSearch();
        }

        return $this->teamModel;
    }

    private function getMemberModel()
    {
        if (empty($this->memberModel)) {
            $this->memberModel = new Member('slave');
        }

        return $this->memberModel;
    }

    private function getOrderReferModel($force = false)
    {
        if (empty($this->_orderReferModel) || $force) {
            $this->orderModel = new OrderRefer();
        }

        return $this->orderModel;
    }

    private function getAreaModel()
    {
        if (empty($this->areaModel)) {
            $this->areaModel = new Area();
        }

        return $this->areaModel;
    }

    private function getLandModel()
    {
        if (empty($this->landModel)) {
            $this->landModel = new Land();
        }

        return $this->landModel;
    }

    /**
     * 通过用户身份证获取客源地
     *
     * @param $cardId
     *
     * @return array
     */
    private function _getProvinceCityByCardId($cardId)
    {
        if (empty($cardId)) {
            return ['provice' => '', 'city' => ''];
        }
        $areaModel = $this->getAreaModel();
        if (empty($this->_idCardProvince)) {
            $this->_idCardProvince = load_config('id_card_province_code', 'account');
        }
        if (empty($this->_idCardCity)) {
            $this->_idCardCity = load_config('id_card_city', 'account');
        }

        $idCardProvince = $this->_idCardProvince;
        $idCardCity     = $this->_idCardCity;
        $idProvinceCode = substr($cardId, 0, 2);
        $idCityCode     = substr($cardId, 0, 4);
        $provinceCode   = $idCardProvince[$idProvinceCode];
        $province       = $areaModel->getProvinceAndCityNameById($provinceCode);
        $city           = $idCardCity[$idCityCode];

        return ['province' => $province, 'city' => $city];
    }

    /**
     * 通过手机号获取客源地
     *
     * @param $telNum
     *
     * @return array
     */
    private function _getProvinceCityByTel($telNum)
    {
        if (empty($telNum)) {
            return ['provice' => '', 'city' => ''];
        }
        $model   = new \Model\BigDataPanel\TelArea();
        $key     = substr($telNum, 0, 7);
        $telInfo = $model->getInfoByTelArr([$key]);
        if (empty($telInfo)) {
            return ['provice' => '', 'city' => ''];
        }

        if (empty($this->_telProvince)) {
            $this->_telProvince = load_config('tel_province', 'account');
        }
        if (empty($this->_telCity)) {
            $this->_telCity = load_config('tel_city', 'account');
        }

        $telProvinceName = $this->_telProvince;
        $telCityName     = $this->_telCity;
        $province        = isset($telProvinceName[$telInfo[0]['province']]) ? $telProvinceName[$telInfo[0]['province']] : '';
        $city            = isset($telCityName[$telInfo[0]['city']]) ? $telCityName[$telInfo[0]['city']] : '';

        return ['province' => $province, 'city' => $city];
    }

}