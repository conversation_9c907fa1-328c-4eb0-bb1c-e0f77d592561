<?php
/**
 * 洛阳数据推送
 * Author: yangwx
 * Date: 2019/8/15 0015
 */
namespace CrontabTasks\ThirdPartner;

use Library\Controller;
use Model\DataCollection\DataCollection;
use Model\Product\Land;

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}


class LuoYangPushData extends Controller
{

    private $pushUrl = 'http://qmt.lyta.com.cn/scenic/report';//接口地址

    private $loginName = 'C141032300002001';//登录账号

    private $pwd = 'dms65082116';//登录密码

    private $applyId = 2660681; //供应商id


    /**
     * 每小时推送数据
     * （推送前一小时数据客流数据）
     * <AUTHOR>
     * @date 2019/8/15
     *
     * @return boolean
     */
    public function postData()
    {
        $dataCollectionModel = new DataCollection();
        //$landModel = new Land('slave');

        $timeNow = date("Y-m-d H:i:s");//当前时间戳
        $timeHour = date("Y-m-d H:i:s", strtotime("-1 hour"));//一个小时前的时间
        $enterTotal = 0;//入园人数统计
        $leaveTotal = 0;//出园人数统计

        //$landIdList = $landModel->getLandField($this->applyId, 1, 'id,terminal');

        $javaApi    = new \Business\CommodityCenter\Land();
        $landIdList = $javaApi->queryLandMultiQueryByApplyDid([$this->applyId], '', '', false, [1]);

        if (!empty($landIdList)) {
            $landArr = array_column($landIdList, 'id');
            //获取入园数据
            $enterDataRes = $dataCollectionModel->getEnterData($timeHour, $timeNow, $landArr);
            if ($enterDataRes) {
                $enterTotal = (int)$enterDataRes[0]['cnt'];
            }

            //获取出园数据
            $leaveDataRes = $dataCollectionModel->getLeaveData($timeHour, $timeNow, $landArr);
            if ($leaveDataRes) {
                $leaveTotal = (int)$leaveDataRes[0]['cnt'];
            }
        }

        //随机获取分配规则
        $randNum = mt_rand(0, 2);
        $chance = [0.55, 0.62, 0.69];//分配规则

        //分配第一台闸机的数据
        $pushEnterOne = intval($enterTotal * $chance[$randNum]);
        $pushLeaveOne = intval($leaveTotal * $chance[$randNum]);

        //剩下的分配给第二台闸机的数据
        $pushEnterTwo = $enterTotal - $pushEnterOne;
        $pushLeavetwo = $leaveTotal - $pushLeaveOne;

        //将查询到的总客流数据进一步处理成对方要的
        $pushData = [
            'loginName'  => $this->loginName,
            'pwd'        => $this->pwd,
            'data'       => [
                [
                    'inNum'      => strval($pushEnterOne),
                    'outNum'     => strval($pushLeaveOne),
                    'channelId'  => '01',
                    'recordTime' => $timeNow
                ],
                [
                    'inNum'      => strval($pushEnterTwo),
                    'outNum'     => strval($pushLeavetwo),
                    'channelId'  => '02',
                    'recordTime' => $timeNow
                ],
            ]
        ];

        $postData = 'jsonParams=' .  urlencode(json_encode($pushData));
        $pushRes =  curl_post($this->pushUrl, $postData);

        $logData = [
            'logInfo'  => '黛眉山数据推送',
            'logType'  => 'dms_push',
            'enterTotal' => $enterTotal,
            'leaveTotal' => $leaveTotal,
            'pushData' => $pushData,
            'pushRes'  => $pushRes
        ];
        pft_log('luo_yang_push', json_encode($logData, JSON_UNESCAPED_UNICODE));
        return true;
    }


}