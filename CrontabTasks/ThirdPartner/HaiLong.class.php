<?php
/**
 * Created by PhpStorm.
 * User: zhang<PERSON>zhen
 * Date: 2018/5/10
 * Time: 10:25
 */

namespace CrontabTasks\ThirdPartner;

use Business\Order\OrderQueryJavaService;
use Business\Order\OrderQueryMove;
use Library\Controller;
use Model\Order\OrderFailed;
use Model\Order\OrderQuery;

class HaiLong extends Controller
{
    const __CODE_SUCCESS__ = 1001;  //成功
    const __CODE_FAILED__  = 1002;  //失败
    const __AUTH_FAILED__  = 1004;  //用户认证失败

    private $username     = 'thdpwt';       //第三方提供的账号
    private $password     = 'thdpwt123';    //第三方提供的密码
    private $timeout      = 30;             //超时时间
    private $lid          = [86114];          //景区id
    private $_checkMethod = 'gatePersonFlowOne';  //景区/景点入园游客信息推送接口方法
    private $_orderMethod = 'ticketOrderOne';     //景区/景点下单和取消推送接口方法

    private $_order_query;  //order_query模型
    private $_order_failed; //order_failed模型

    //接口地址
    private $_url = 'http://120.79.85.68:9000/itmp/third-api/';

    /**
     * 景区/景点入园游客信息推送
     * Create by zhangyangzhen
     * Date: 2018/5/15
     * Time: 15:42
     */
    public function run()
    {
        $orderFailedModel = $this->_getOrderFailedModel();
        //从订单表查询过去15分钟的数据
        $datetime = date('Y-m-d H:i:s', time() - 15 * 60);
        //$otherParams       = [
        //    'dtime'  => [[$datetime],'egt']
        //];
        //$selectParams     = [
        //    'orderBy'    => ['dtime' => 'desc'],
        //    'field'      => '*'
        //];
        //$orderQueryJavaBiz = new OrderQueryJavaService();
        //$data = $orderQueryJavaBiz->getOrderInfoByLidAndOtherParam($this->lid,$otherParams,$selectParams);

        //订单查询迁移
        $orderQueryLib = new OrderQueryMove();
        $data = $orderQueryLib->getOrderInfByLidDtime([$this->lid], $datetime);

        if (!empty($data)) {
            foreach ($data as $key => $val) {
                $postData = $this->_handleParams($val);
                $response = $this->curl_post(json_encode($postData), $this->_checkMethod);
                //高峰期数据量大，对方有可能返回false
                if ($response) {
                    $response = json_decode($response, true);
                    if ($response['code'] != self::__CODE_SUCCESS__) {
                        $addFailData = $this->_handleAddData($postData['lid'], $postData, $response, 1);
                        $orderFailedModel->addFailedOrderData($addFailData);
                    }
                }
            }
        }

        //从推送失败订单记录中查询数据，并再次推送
        $failData = $orderFailedModel->getFailOrderList();
        if (!empty($failData)) {
            foreach ($failData as $key => $val) {
                //type = 1验证 2下单 3取消，推送接口不一样
                if ($val['type'] == 1) {
                    $response = $this->curl_post($val['content'], $this->_checkMethod);
                } else {
                    $response = $this->curl_post($val['content'], $this->_orderMethod);
                }

                if ($response) {
                    $response = json_decode($response, true);
                    //再次推送失败，更新推送失败次数和推送时间
                    if ($response['code'] != self::__CODE_SUCCESS__) {
                        $orderFailedModel->updatePushCountById($val['id'], $val['push_count'] + 1);
                    } else {
                        $orderFailedModel->updateFailOrderToSuccess($val['id'], $response);
                    }
                }
            }
        }
    }

    /**
     * 向第三方发送请求
     * Create by zhangyangzhen
     * Date: 2018/5/11
     * Time: 10:07
     *
     * @param $postData     请求数据
     * @param $method       请求方法
     *
     * @return bool|mixed
     */
    private function curl_post($postData, $method)
    {
        $timestamp   = time();
        $auth        = $this->getSign($timestamp);
        $http_header = [
            'Content-Type:application/json',
            'userName:' . $this->username,
            'timestamp:' . $timestamp,
            'auth:' . $auth,
        ];
        $response    = curl_post($this->_url . $method, $postData, '', $this->timeout, '/api/curl_post', $http_header);

        return $response;
    }

    /**
     * 处理第三方请求数据
     * Create by zhangyangzhen
     * Date: 2018/5/14
     * Time: 15:30
     *
     * @param $data
     *
     * @return array
     */
    private function _handleParams($data)
    {
        $postData = [
            //景区/景点id
            'scId'              => $data['lid'],
            //游客身份证
            'vId'               => $data['personid'],
            //游客姓名
            'vName'             => $data['ordername'],
            //游客性别
            'vSex'              => '',
            //游客联系方式
            'vTel'              => $data['ordertel'],
            //游客类型  1散客  0团队
            'vType'             => $data['tnum'] == 1 ? 1 : 0,
            //入园道闸编号
            'vEntranceGateId'   => '',
            //入园道闸名称
            'vEntranceGateName' => '',
            //入园人数
            'vEntranceCount'    => $data['tnum'] ?: 1,
            //入园时间
            'vEntranceTime'     => $data['dtime'],
            //推送时间
            'pushTime'          => date('Y-m-d H:i:s'),
            //备注
            'mark'              => '',
        ];

        return $postData;
    }

    /**
     * 生成auth签名
     * Create by zhangyangzhen
     * Date: 2018/5/11
     * Time: 9:24
     *
     * @param $timestamp    时间戳
     *
     * @return string   加密签名算法
     */
    private function getSign($timestamp)
    {
        return md5($this->username . md5($this->password) . $timestamp);
    }

    /**
     * 获取order_query模型
     * Create by zhangyangzhen
     * Date: 2018/5/14
     * Time: 10:57
     * @return OrderQuery
     */
    private function _getOrderQueryModel()
    {
        if (!isset($this->_order_query)) {
            $this->_order_query = new OrderQuery();
        }

        return $this->_order_query;
    }

    /**
     * 获取order_failed模型
     * Create by zhangyangzhen
     * Date: 2018/5/14
     * Time: 10:59
     * @return OrderFailed
     */
    private function _getOrderFailedModel()
    {
        if (!isset($this->_order_failed)) {
            $this->_order_failed = new OrderFailed();
        }

        return $this->_order_failed;
    }

    /**
     * 处理海龙接口推送失败的数据，存入数据库
     * Create by zhangyangzhen
     * Date: 2018/7/20
     * Time: 11:55
     *
     * @param $lid              景区id
     * @param $content          推送内容
     * @param  array  $response  处理结果
     * @param  int  $type  订单类型：1验证，2下单，3取消
     *
     * @return array    插入数据库的数据
     */
    private function _handleAddData($lid, $content, $response = [], $type = 1)
    {
        $content = json_encode($content);

        $addData = [
            'lid'        => $lid,
            'content'    => $content,
            'type'       => $type,
            'push_count' => 1,
            'push_time'  => date('Y-m-d H:i:s'),
            'code'       => $response['code'],
            'msg'        => $response['message'],
        ];

        return $addData;
    }
}