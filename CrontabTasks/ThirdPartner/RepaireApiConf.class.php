<?php
namespace CrontabTasks\ThirdPartner;
use Library\Controller;

// 权限判断
if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

/**
 * 更新对接数据   文件名待修改
 * <AUTHOR>
 * @date   2018-8-2
 * php run.php cli_RepaireApiConf insertConf
 */
class RepaireApiConf
{
    public function insertConf()
    {
        set_time_limit(0);

        $i          = 1;
        $cacheRedis = \Library\Cache\Cache::getInstance('redis');
        $cacheRedis->set("apicofpage", $i, '', 3600);

        while (true) {

            $page        = $cacheRedis->get("apicofpage");
            $confModel   = new \Model\Ota\SysConfig();
            $csysInfoArr = $confModel->getInfoByCsyId([5,7], 'apply_did,lid,csysid', $page, 100);

            $i++;
            $cacheRedis->set("apicofpage", $i, '', 3600);

            if (empty($csysInfoArr)) {
                exit('完成');
            }

            $lidArr = array_column($csysInfoArr, 'lid');
            $lidCsyIdConfArr = $confModel->getCsysLidConfigList([5,7], $lidArr);
            if (!empty($lidCsyIdConfArr)) {
                $lidCsysLidArr = array_column($lidCsyIdConfArr, 'lid');
                $lidArr        = array_diff($lidArr, $lidCsysLidArr);
            }

            if (empty($lidArr)) {
                continue;
            }

            $needInsetLidArr = [];
            foreach ($csysInfoArr as $key => $csysVal) {
                if (!in_array($csysVal['lid'], $lidArr)) {
                    unset($csysInfoArr[$key]);
                } else {
                    $accesArr = $confModel->getAccSysAuthDetailByCsysIds($csysVal['apply_did'], $csysVal['csysid']);
                    $csysVal['supplierIdentity'] = $accesArr[$csysVal['csysid']]['supplierIdentity'];
                    $csysVal['signkey']          = $accesArr[$csysVal['csysid']]['signkey'];
                    $needInsetLidArr[]           = $csysVal;
                }
            }

            if (empty($needInsetLidArr)) {
                continue;
            }

            $insetRes = $confModel->saveCsyLidConfAll($needInsetLidArr);
            pft_log('repaire/apiConf', 'page:' . $page . '|' . $confModel->_sql());

            sleep(2);
        }
        exit('完成');
    }

    /**
     * php /var/www/html/Service/Crontab/runNew.php ThirdPartner/RepaireApiConf runResourceProductUpdateByLidList
     * 批量更新驴妈妈产品景点信息
     * 
     */
    public function runResourceProductUpdateByLidList()
    {
        set_time_limit(0);
        $lmmLidList = [203290,200541,201326,203344,221771,203873,203245,206147,200875,202664,220450,
                        206656,204172,202445,205280,201531,201695,204072,201529,221114,203564,201087,201247,203728,210969,207664,202942];

        $ctripLidList = [
            182128,211100,180324,180217,182623,227359,227693,181501,181883,243870,181961,180298,227460,180122,179907,181864,244951,180437,180374,179811,182619,178732
        ];

        $lib = new \Library\JsonRpc\PftRpcClient('resource_product');

        foreach ($lmmLidList as $lmmLidItem) {
            $method = 'lvmama/pull';
            $module = 'lvmama';
           
            try {
                $res  = $lib->call($method, [$lmmLidItem, 2], $module);
                $data = $res['data'];
                $code = $res['code'];
                $msg  = $res['message'];
            } catch (\Exception $e) {
                $msg  = $e->getMessage();
                $code = $e->getCode();
                $data = [];
            }

            echo $lmmLidItem . '|' . $code . '|' . $msg;
            pft_log('ota/resource/update', 'lmm:' . json_encode([$lmmLidItem, $code, $msg, $data]));
            sleep(1);
        }

        foreach ($ctripLidList as $ctripLidItem) {
            $method = 'xiecheng/pull';
            $module = 'ctrip';
           
            try {
                $res  = $lib->call($method, [$ctripLidItem, 2], $module);
                $data = $res['data'];
                $code = $res['code'];
                $msg  = $res['message'];
            } catch (\Exception $e) {
                $msg  = $e->getMessage();
                $code = $e->getCode();
                $data = [];
            }

            echo $ctripLidItem . '|' . $code . '|' . $msg;
            pft_log('ota/resource/update', 'ctrip:' . json_encode([$ctripLidItem, $code, $msg, $data]));
            sleep(1);
        }

        exit("完成");
    }

    /**
     * php /var/www/html/Service/Crontab/runNew.php ThirdPartner/RepaireApiConf runResourceProductUpdate 2 1577979662
     * 节假日前批量刷新数据
     * 
     */
    public function runResourceProductUpdate()
    {
        set_time_limit(0);
        $params     = $GLOBALS['argv'];
        $pullConfId = $params[3] ?? 2;
        $startTime  = $params[4] ?? '1606923662';

        $resourceModel = new \model\Ota\Resource();
        $lib  = new \Library\JsonRpc\PftRpcClient('resource_product');
        
        $pageArr = [1,2,3,4,5,6,7,8];
        foreach ($pageArr as $pageItme) {
            $thirdBindLandInfoArr = $resourceModel->getThirdLandRelationByConfIdAndTime($pullConfId, $startTime, $pageItme, 1000);
            if (!empty($thirdBindLandInfoArr)) {
                foreach ($thirdBindLandInfoArr as $key => $item) {
                    // 判断调用是对应系统的拉取方法
                    if ($pullConfId == 2) {
                        $method = 'xiecheng/pull';
                        $module = 'ctrip';
                    } elseif ($pullConfId == 3) {
                        $method = 'lvmama/pull';
                        $module = 'lvmama';
                    }
    
                    try {
                        $res  = $lib->call($method, [$item['pft_land_id'], 2], $module);
                        $data = $res['data'];
                        $code = $res['code'];
                        $msg  = $res['message'];
                    } catch (\Exception $e) {
                        $msg  = $e->getMessage();
                        $code = $e->getCode();
                        $data = [];
                    }
    
                    echo $key . '||' . $item['pft_land_id']  . '||' . date("Y-m-d H:i:s", time())  . '||' . $pageItme . "\r\n"; 
                    sleep(1);
                }
            }
        }

        echo "完成";
        exit;
    }

    /**
     * 每十分钟通知美团统一预定离园人数
     * php /var/www/html/Service/Crontab/runNew.php ThirdPartner/RepaireApiConf noticeMtLeaveParkNum
     *
     */
    public function noticeMtLeaveParkNum()
    {
        $requestArr = [
            'date' => date("Y-m-d H:i:s", time())
        ];
        $response = \Library\Tools\Helpers::callGroupSystem(8, $requestArr, 'Other_NoticeLeaveParkData');
        echo "完成";
        exit;
    }
}