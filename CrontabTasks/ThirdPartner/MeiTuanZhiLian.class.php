<?php

/**
 *uuqunar数据迁移到 meituanzhilian
 */

namespace CrontabTasks\ThirdPartner;

use Business\OtaApi\OtaBase;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class MeiTuanZhiLian extends OtaBase
{

    /**
     * uuqunar数据迁移美团直连
     *
     * Author : liucm
     * Date : 2021/8/2
     */
    public function dataMigration()
    {
        $uuqnaerModel   = new \Model\qunaer\qunaer();
        $meituanService = new \Business\Ota\MeiTuanZhiLian();
        $res            = $uuqnaerModel->getMeiTuanZhiLianInfo();
        $i              = 0;
        $fiData         = [];
        foreach ($res as $value) {
            $fid = $value['fid'];
            //根据fid 查询信息
            $MeiTuanZhiLianConf = $uuqnaerModel->getMeiTuanZhiLianByFid($fid);
            if ($MeiTuanZhiLianConf) {
                $sid       = $MeiTuanZhiLianConf['fid'];
                $partnerId = $MeiTuanZhiLianConf['supplierIdentity'];
                [$clientId, $clientSecret] = explode('|', $MeiTuanZhiLianConf['signkey']);
                if (empty($sid) || empty($partnerId) ||empty($clientId) || empty($clientSecret)) {
                    //记录信息错误的fid
                    $fiData[] = $fid;
                } else {
                    //插入或更新数据
                    $insertRes = $meituanService->setMeiTuanZhiLianConf($sid, $partnerId, $clientId,
                        $clientSecret);
                }
            }
            //每查询100次 sleep1秒
            if ($i % 100 == 0) {
                echo $i;
                sleep(1);
            }
            $i++;

        }
        $logData = [
            'key'  => '美团直连信息同步',
            'fids' => $fiData,
        ];
        pft_log('ota/meituanzhilian', json_encode($logData, JSON_UNESCAPED_UNICODE));
        echo 'done';
        exit();
    }
}