<?php
namespace CrontabTasks\DataFixed;

use Library\Controller;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class Payment extends Controller
{
    public function addNewSourceT($command, $controller, $action, $channelId, $payMode, $payType, $sourceT, $subjectCode, $desc) {
        if (is_null($channelId) || is_null($payMode) || is_null($payType) || is_null($sourceT) || is_null($subjectCode) || !$desc) {
            echo 'bash: php runNew.php DataFixed_Payment addNewSourceT [channelId] [payMode] [payType] [sourceT] [subjectCode] [备注]';exit;
        }
        $rootPath = dirname(__DIR__, 2);
        $isOnlinePay = false;
        while (true) {
            echo '添加的支付类型是否在线支付[Y/n]:', PHP_EOL;
            fscanf(STDIN, '%s', $input);
            $input = strtoupper($input);
            if ($input === 'N') {
                break;
            } else if ($input === 'Y') {
                $isOnlinePay = true;
                break;
            }
        }
        if ($isOnlinePay) {
            $this->appendElement(
                $rootPath . '/Business/Order/Refund.class.php',
                '\\$otherPayModeAndTypeMap\s*=',
                "{$payMode} => {$payType},//{$desc}",
                " {$payMode} => {$payType},"
            );
            $this->appendElement(
                $rootPath . '/Conf/business.conf.php',
                '\'online_pay_mode\'\s*=\>',
                "{$payMode},//{$desc}",
                " {$payMode},"
            );
        } else {
            $this->appendElement(
                $rootPath . '/Business/Pay/PayComplete.class.php',
                '\$this-\>_offlinePay\s*=',
                "{$payMode},//{$desc}",
                " {$payMode},"
            );
        }
        $this->appendElement(
            $rootPath . '/Business/Pay/PayComplete.class.php',
            'private \\$_sourceToNewSMS\s*=',
            "{$sourceT},//{$desc}",
            " {$sourceT},"
        );
        $this->appendElement(
            $rootPath . '/Conf/business.conf.php',
            '\'order_pay_mode\'\s*=\>',
            "{$payMode} => '{$desc}',",
            " {$payMode} => '{$desc}',"
        );
        $this->appendElement(
            $rootPath . '/Conf/business.conf.php',
            '\'pay_type\'\s*=\>',
            "{$payType} => '{$desc}',",
            " {$payType} => '{$desc}',"
        );
        $this->appendElement(
            $rootPath . '/Conf/business.conf.php',
            '\'sourceT_pay_mode_map\'\s*=\>',
            "{$sourceT} => {$payMode},//{$desc}",
            " {$sourceT} => {$payMode},"
        );
        $this->appendElement(
            $rootPath . '/Conf/business.conf.php',
            '\'sourceT_pay_type_map\'\s*=\>',
            "{$sourceT} => {$payType},//{$desc}",
            " {$sourceT} => {$payType},"
        );

        //*********************************************
        //*        trackSource由平台自行生成            *
        //*********************************************/
        $result = $this->getLastMapKey(
            $rootPath . '/Conf/business.conf.php',
            '\'track_source\'\s*=\>'
        );
        if ($result['status']) {
            if (filter_var($result['data']['key'], FILTER_VALIDATE_INT) !== false) {
                $lastTrackSource = intval($result['data']['key']);
                $trackSource = $lastTrackSource + 1;
                $code = $this->appendElement(
                    $rootPath . '/Conf/business.conf.php',
                    '\'track_source\'\s*=\>',
                    "{$trackSource} => '{$desc}',",
                    $desc
                );
                if ($code !== 1012) {
                    //如果描述已经存在，则不生成新的映射
                    $this->appendElement(
                        $rootPath . '/Conf/business.conf.php',
                        '\'sourceT_track_source_map\'\s*=\>',
                        "{$sourceT} => {$trackSource},//{$desc}",
                        " {$sourceT} => {$trackSource},"
                    );
                }
            }
        }

        $this->appendElement(
            $rootPath . '/Conf/business.conf.php',
            '\'payway_list\'\s*=\>',
            "'{$payMode}' => '{$desc}',",
            $desc
        );


        $this->appendElement(
            $rootPath . '/Conf/orderSearch.conf.php',
            '\'pay_mode_two\'\s*=\>',
            "{$payMode} => ['name' => '{$desc}', 'key' => {$payMode}],"
        );
        $this->appendElement(
            $rootPath . '/Conf/orderSearch.conf.php',
            '\'pay_mode\'\s*=\>',
            "{$payMode} => '{$desc}',"
        );

        $this->appendElement(
            $rootPath . '/Conf/trade_record.conf.php',
            '\'pay_type\'\s*=\>',
            "{$payType} => [{$payType}, '{$desc}', 0],"
        );
        echo '【', $rootPath . '/Conf/trade_record.conf.php', '】', '$pay_type 所属支付大类请自行确认，默认0', PHP_EOL;

        $this->appendElement(
            $rootPath . '/Conf/trade_record.conf.php',
            '\'account_type\'\s*=\>',
            "{$payType} => '{$desc}',"
        );

        $this->appendElement(
            $rootPath . '/Conf/trade_record.conf.php',
            '\'ptype_2_subject_code\'\s*=\>',
            "{$payType} => {$subjectCode},//{$desc}"
        );

        $this->appendCase(
            $rootPath . '/Controller/OnlineRefund.class.php',
            '\/\/收银台服务的支付统一退款方法',
            "case {$payMode}://{$desc}",
            "case {$payMode}:"
        );
//----------------------------------------------
        echo "【Controller/OnlineRefund.class.php】下的_selectPayMode函数，请自行确认。", PHP_EOL;
    }

    public function getLastMapKey($file, $userPattern)
    {
        $result = $this->getElement($file, $userPattern);
        if (!$result['status']) {
            return $this->fail($result['msg']);
        }
        $matches = $result['data']['matches'];
        $rawText = rtrim($matches[1]);
        $lines = explode(PHP_EOL, $rawText);
        $lastLine = array_pop($lines);
        if (preg_match('/^\s*(.*?)\s*=>\s*(.*?)\s*,/', $lastLine, $matches)) {
            return $this->ok(['key' => $matches[1], 'value' => $matches[2]]);
        }
        return $this->fail('未找到对应的key');
    }

    public function appendElement($file, $userPattern, $element, $identity = null)
    {
        $result = $this->getElement($file, $userPattern);
        if (!$result['status']) {
            echo $result['msg'], PHP_EOL;
            return 0;
        }
        $code = $result['data']['code'];
        $matches = $result['data']['matches'];
        return $this->engine($code, $matches, $file, $userPattern, $element, $identity);
    }

    public function appendCase($file, $userPattern, $element, $identity = null)
    {
        if (!is_file($file)) {
            echo "【{$file}】{$userPattern} 不存在";
            return 0;
        }
        $code = file_get_contents($file);
        $pattern = "/((?:\s*case (?:.*?):.*\n)+)(\s*)$userPattern/";
        if (!preg_match($pattern, $code, $matches)) {
            echo "【{$file}】{$userPattern} 找不到修改点";
            return 0;
        }
        return $this->engine($code, $matches, $file, $userPattern, $element, $identity, function ($spaceLength) {
            return str_pad('', max(0, $spaceLength - 4), ' ', STR_PAD_LEFT);
        });
    }

    protected function getElement($file, $userPattern)
    {
        if (!is_file($file)) {
            return $this->fail("【{$file}】{$userPattern} 不存在");
        }
        $code = file_get_contents($file);
        $pattern = "/($userPattern\s*\[.*\n(?:(?:\s*'?-?\d+'?\s*=>.*\n)|(?:\s*-?\d+\s*,.*\n)|(?:\s*\/\/.*\n)|(?:\s*-?\d+\s*,))+)(\s*)][;|,]/";
        if (!preg_match($pattern, $code, $matches)) {
            return $this->fail("【{$file}】{$userPattern} 找不到修改点");
        }
        return $this->ok(['code' => $code, 'matches' => $matches]);
    }


    protected function engine($code, $matches, $file, $userPattern, $element, $identity = null, \Closure $padClosure = null) {
        $identity = $identity ?: $element;
        if (strpos($matches[1], $identity) !== false) {
            return 1012;
        }
        $spaceLength = mb_strlen($matches[2]);
        if ($padClosure) {
            $spaceStr = call_user_func($padClosure, $spaceLength);
        } else {
            $spaceStr = str_pad('', $spaceLength + 4, ' ', STR_PAD_LEFT);
        }
        $start = strpos($code, $matches[1]);
        $length = strlen($matches[1]);
        $end = $start + $length;
        $leftCode = substr($code, $end);
//        echo $spaceLength, '|', $spaceStr, '|', $index, '|', $length, PHP_EOL;die;
        $fp = fopen($file, 'r+');
        fseek($fp, $end, SEEK_CUR);
        fwrite($fp, "{$spaceStr}{$element}\n");
        fwrite($fp, $leftCode);
        fclose($fp);
        echo <<<EOT
【{$file}】{$userPattern} 生成新的代码：
*****************************
$element
******************************\n\n
EOT;
        return 200;
    }

    protected function returnData($status, $msg = '', $data = [])
    {
        return compact('status', 'msg', 'data');
    }

    protected function ok($data)
    {
        return $this->returnData(true, '', $data);
    }

    protected function fail($msg)
    {
        return $this->returnData(false, $msg);
    }
}