<?php
/**
 * 同步岗位数据脚本
 * <AUTHOR>
 * @date   2023/5/23
 */

namespace CrontabTasks\Authority;

use Business\Authority\Auth\Config;
use Business\Authority\AuthLogic as AuthLogicBiz;
use Model\Authority\DataSynTmp as DataSynTmpModel;
use Model\Authority\Role as RoleModel;

class SynJobs extends Base
{

    const FRONT_APP_IDS = [
        AuthLogicBiz::FRONT_APP_DEFAULT_ID,
        AuthLogicBiz::FRONT_APP_USER_CENTER_ID,
    ];

    /**
     * 入口文件
     * 命令： php /var/www/html/Service/Crontab/runNew.php Authority/SynJobs run
     *
     * 注：定时任务需要开启单例执行
     * <AUTHOR>
     * @date   2022/11/17
     *
     */
    public function run()
    {
        $this->recordDate(true);

        $this->runWork();

        $this->recordDate(false, true);
    }

    /**
     * 脚本执行
     * <AUTHOR>
     * @date   2023/5/23
     *
     */
    protected function runWork()
    {
        $page      = 1;
        $size      = 100;
        $endTime   = time() - 2;
        $beginTime = $endTime - 3600 * 10;

        while (true) {
            //获取需要同步的操作记录
            $listRes = $this->getOperList(0, $beginTime, $endTime, $page, $size);
            if (empty($listRes)) {
                break;
            }

            $successArr = [];
            $errorArr   = [];
            foreach ($listRes as $tmp) {
                $operType = $tmp['oper_type'] ?? 0;
                $recordId = $tmp['id'] ?? 0;
                if (!$operType) {
                    continue;
                }
                switch ($operType) {
                    case 1:
                        //旧版同步新版
                        $res = $this->oldToNewHandle($tmp);
                        break;
                    case 2:
                        //新版同步旧版
                        $res = $this->newToOldHandle($tmp);
                        break;
                    default:
                        $res = false;
                }

                if (!$res) {
                    $errorArr[] = $recordId;
                    continue;
                }

                $successArr[] = $recordId;
            }

            //更新记录状态为失败
            if (!empty($errorArr)) {
                $errorUpdateRes = $this->updateStateDone($errorArr, 'error');
                if (!$errorUpdateRes) {
                    $this->_outInfo('更新同步失败记录状态失败');
                }
            }
            //更新记录状态为成功
            if (!empty($successArr)) {
                $successUpdateRes = $this->updateStateDone($successArr);
                if (!$successUpdateRes) {
                    $this->_outInfo('更新同步成功记录状态失败');
                }
            }

            $page++;
        }
    }

    /**
     * 旧的同步新的处理
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param  array  $tmp    操作数据
     *
     * @return false
     */
    protected function oldToNewHandle($tmp)
    {
        $sid      = $tmp['sid'] ?? 0;
        $memberId = $tmp['member_id'] ?? 0;
        $ext      = $tmp['ext'] ?? '[]';
        $params   = json_decode($ext, true);
        $type     = $params['type'] ?? '';

        switch ($type) {
            case 'create'://创建岗位 -> 需要新增岗位关联标记
                $res = $this->newCreate($sid, $memberId, $params);
                break;
            case 'edit'://岗位编辑
                $res = $this->newEdit($sid, $memberId, $params);
                break;
            case 'del'://岗位删除
                $res = $this->newDel($sid, $memberId, $params);
                break;
            case 'data_resource'://岗位数据权限编辑保存
                $res = $this->newDataResource($sid, $memberId, $params);
                break;
            case 'staff_give'://员工授予岗位
                $res = $this->newStaffGive($sid, $memberId, $params);
                break;
            case 'staff_del'://员工删除->清空角色包
                $res = $this->newStaffDel($sid, $memberId, $params);
                break;
            default:
                $res = false;
        }

        return $res;
    }

    /**
     * 新的同步旧的处理
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param  array  $tmp    操作数据
     *
     * @return false
     */
    protected function newToOldHandle($tmp)
    {
        $sid      = $tmp['sid'] ?? 0;
        $memberId = $tmp['member_id'] ?? 0;
        $ext      = $tmp['ext'] ?? '[]';
        $params   = json_decode($ext, true);
        $type     = $params['type'] ?? '';

        switch ($type) {
            case 'create'://创建岗位 -> 需要新增岗位关联标记 新版交互问题，没有岗位权限不保存 - 废弃！！！
                $res = $this->oldCreate($sid, $memberId, $params);
                break;
            case 'edit'://编辑岗位
                $res = $this->oldEdit($sid, $memberId, $params);
                break;
            case 'edit_auth'://编辑岗位
                $res = $this->oldEditAuth($sid, $memberId, $params);
                break;
            case 'del'://岗位删除->涉及岗位变更
                $res = $this->oldDel($sid, $memberId, $params);
                break;
            case 'data_resource'://岗位数据权限编辑保存
                $res = $this->oldDataResource($sid, $memberId, $params);
                break;
            case 'staff_give'://员工授予岗位
                $res = $this->oldStaffGive($sid, $memberId, $params);
                break;
            case 'staff_del'://员工删除  旧版权限员工删除没有移除权限 -  废弃！！！
                $res = $this->oldStaffDel($sid, $memberId, $params);
                break;
            default:
                $res = false;
        }

        return $res;
    }

    /**
     * 新版创建岗位
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param  int    $sid         商户id
     * @param  int    $memberId    用户id
     * @param  array  $params      参数集合
     *
     * @return false
     */
    protected function newCreate($sid, $memberId, $params)
    {
        try {

            $name       = $params['name'] ?? '';
            $desc       = $params['desc'] ?? '';
            $tag        = $params['tag'] ?? '';
            $roleId     = $params['role_id'] ?? 0;
            $roleTagArr = array_values(array_unique(array_filter(explode(',', $tag))));

            if (empty($name) || empty($roleTagArr) || !$roleId) {
                throw new \Exception('同步创建新版岗位失败，参数错误', 400);
            }

            //创建空的角色包
            $packId = $this->createRolePackage($name, $sid);
            if (!$packId) {
                throw new \Exception("用户{$sid}岗位[{$name}]同步创建角色包失败", 400);
            }

            //判断是否有岗位管理权限,存在则移除
            if (in_array('oper', $roleTagArr)) {
                unset($roleTagArr[array_search('oper', $roleTagArr)]);
                $roleTagArr = array_values($roleTagArr);
                $this->_outInfo("用户{$sid}岗位[{$name}]存在员工管理");
            }

            //查询 这边因为商户端菜单树拆成多个系统应用，这边处理下 ！！！！
            foreach (self::FRONT_APP_IDS as $frontAppId) {
                $allMenuIds = []; //含链路
                $resData = $this->batchQuerySysMenuByTag($roleTagArr, $frontAppId);
                $path    = array_column($resData, 'path');
                foreach ($path as $item) {
                    $arr        = array_filter(explode('/', $item));
                    $allMenuIds = array_merge($allMenuIds, $arr);
                }

                $allMenuIds = array_values(array_unique($allMenuIds));
                if (empty($allMenuIds)) {
                    continue;
                }

                //赋权
                $packGive = $this->saveRolePackageAndMenu($packId, "job_{$sid}_{$name}", $allMenuIds, $frontAppId, $sid);
                if (!$packGive) {
                    throw new \Exception("用户{$sid}岗位[{$name}]同步创建角色包权限失败", 400);
                }
            }

            //创建岗位
            $jobId = $this->createJobs($sid, $name, $packId, $desc, $sid);
            if (!$jobId) {
                throw new \Exception("用户{$sid}岗位[{$name}]同步创建岗位关联失败", 400);
            }

            //创建数据双写标记
            $synMark = $this->createSynMark($sid, $roleId, $jobId);
            if (!$synMark) {
                throw new \Exception(json_encode(["用户{$sid}岗位[{$name}]双写标记失败", '参数', [$sid, $roleId, $jobId]],
                    JSON_UNESCAPED_UNICODE), 400);
            }

        } catch (\Exception $e) {
            $this->_outInfo($e->getMessage());
            $this->_log([
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 新版编辑
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param  int    $sid         商户id
     * @param  int    $memberId    用户id
     * @param  array  $params      参数集合
     *
     * @return false
     */
    protected function newEdit($sid, $memberId, $params)
    {
        try {
            $name       = $params['name'] ?? '';
            $desc       = $params['desc'] ?? '';
            $tag        = $params['tag'] ?? '';
            $oldId      = $params['id'] ?? 0;
            $roleTagArr = array_values(array_unique(array_filter(explode(',', $tag))));

            if (!$oldId || empty($name) || empty($roleTagArr)) {
                throw new \Exception('同步编辑新版岗位失败，参数错误', 400);
            }

            //查询获取新的岗位id，然后查询出角色包，然后角色包更新权限
            $markInfo = $this->getSynMark($sid, $oldId);
            if (empty($markInfo)) {
                throw new \Exception("用户{$sid}岗位[{$name}]旧岗位id[{$oldId}]获取新版岗位信息失败", 400);
            }

            //获取新版岗位id
            $jobId = $markInfo['new_id'] ?? 0;
            if (!$jobId) {
                throw new \Exception("用户{$sid}岗位[{$name}]旧岗位id[{$oldId}]获取新版岗位id失败", 400);
            }

            //获取岗位详情
            $jobInfo = $this->getJobsInfoById($sid, $jobId);
            if (!isset($jobInfo['fid']) || $jobInfo['fid'] != $sid || empty($jobInfo['pack_id'])) {
                throw new \Exception("用户{$sid}岗位[{$name}]新岗位id[{$jobId}]获取新版岗位详情失败", 400);
            }

            $jobName = $jobInfo['name'];
            $jobDesc = $jobInfo['desc'];
            //更新岗位基本信息
            if ($jobName != $name || $jobDesc != $desc) {
                $updateJob = $this->updateJobs($sid, $jobId, $name, $desc);
                if (!$updateJob) {
                    throw new \Exception("用户{$sid}岗位[{$name}]描述[{$desc}]新岗位id[{$jobId}]更新基本信息失败", 400);
                }
            }

            //判断是否有岗位管理权限,存在则移除
            if (in_array('oper', $roleTagArr)) {
                unset($roleTagArr[array_search('oper', $roleTagArr)]);
                $roleTagArr = array_values($roleTagArr);
                $this->_outInfo("用户{$sid}岗位[{$name}]存在员工管理");
            }

            //获取角色包id
            $packId = $jobInfo['pack_id'];

            //查询 这边因为商户端菜单树拆成多个系统应用，这边处理下 ！！！！
            $allMenuIds = []; //含链路
            foreach (self::FRONT_APP_IDS as $frontAppId) {
                $resData = $this->batchQuerySysMenuByTag($roleTagArr, $frontAppId);
                $path    = array_column($resData, 'path');
                foreach ($path as $item) {
                    $arr        = array_filter(explode('/', $item));
                    $allMenuIds = array_merge($allMenuIds, $arr);
                }

                $allMenuIds = array_values(array_unique($allMenuIds));
                // if (empty($allMenuIds)) {
                //     throw new \Exception("用户{$sid}岗位[{$name}]权限菜单id为空", 400);
                // }

                //赋权
                $packGive = $this->saveRolePackageAndMenu($packId, "job_{$sid}_{$name}", $allMenuIds, $sid);
                if (!$packGive) {
                    throw new \Exception("用户{$sid}岗位[{$name}]同步创建角色包权限失败", 400);
                }
            }

        } catch (\Exception $e) {
            $this->_outInfo($e->getMessage());
            $this->_log([
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 新版岗位删除
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param $sid
     * @param $memberId
     * @param $params
     *
     * @return false
     */
    protected function newDel($sid, $memberId, $params)
    {
        try {
            $oldId = $params['id'] ?? 0;

            if (!$oldId) {
                throw new \Exception('同步删除新版岗位失败，参数错误', 400);
            }

            //查询获取新的岗位id，然后查询出角色包，然后角色包更新权限
            $markInfo = $this->getSynMark($sid, $oldId);
            if (empty($markInfo)) {
                throw new \Exception("用户{$sid}旧岗位id[{$oldId}]获取新版岗位信息失败", 400);
            }

            //获取新版岗位id
            $jobId = $markInfo['new_id'] ?? 0;
            if (!$jobId) {
                throw new \Exception("用户{$sid}旧岗位id[{$oldId}]获取新版岗位id失败", 400);
            }

            //获取岗位详情
            $jobInfo = $this->getJobsInfoById($sid, $jobId);
            if (!isset($jobInfo['fid']) || $jobInfo['fid'] != $sid || empty($jobInfo['pack_id'])) {
                throw new \Exception("用户{$sid}新岗位id[{$jobId}]获取新版岗位详情失败", 400);
            }

            //获取角色包id
            $packId = $jobInfo['pack_id'];

            //获取绑定的员工列表
            $rolePackUserRes = $this->queryUserListByRolePackageIds($sid, [$packId]);
            if ($rolePackUserRes['code'] != 200) {
                throw new \Exception("用户{$sid}新岗位id[{$jobId}]获取绑定的员工列表失败, " . $rolePackUserRes['msg'], 400);
            }
            $rolePackUserResMap = array_column($rolePackUserRes['data'] ?? [], 'userIdList', 'rolePackageId');
            $userIdList         = $rolePackUserResMap[$packId] ?? [];

            //解除岗位绑定关系
            $userSidList = [];
            foreach ($userIdList as $uid) {
                if ($sid == $uid) {
                    continue;
                }
                $userSidList[] = [
                    'userId' => $uid,
                    'sid'    => $sid,
                ];
            }

            //用户权限解绑
            if (!empty($userSidList)) {
                $userUnBindRes = $this->userUnBindPackage($userSidList, [$packId], $sid);
                if (!$userUnBindRes) {
                    throw new \Exception("用户{$sid}新岗位id[{$jobId}]解除用户岗位关联失败", 400);
                }
            }

            //删除角色包
            $delRolePackRes = $this->delRolePackageById($packId);
            if (!$delRolePackRes) {
                throw new \Exception("用户{$sid}角色包id[{$packId}]删除角色包失败", 400);
            }

            //删除原始岗位
            $delJobsRes = $this->delJobs($sid, $jobId);
            if (!$delJobsRes) {
                throw new \Exception("用户{$sid}新岗位id[{$jobId}]删除岗位失败", 400);
            }

        } catch (\Exception $e) {
            $this->_outInfo($e->getMessage());
            $this->_log([
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 新版数据权限保存
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param $sid
     * @param $memberId
     * @param $params
     *
     * @return false
     */
    protected function newDataResource($sid, $memberId, $params)
    {
        try {
            $oldId   = $params['role_id'] ?? 0;
            $type    = $params['scope_type'] ?? 0;
            $tag     = $params['data_tag'] ?? '';
            $limitId = $params['limit_id'] ?? '';

            if (!$oldId || !$type || empty($tag)) {
                throw new \Exception('数据范围设置失败，参数错误', 400);
            }

            //查询获取新的岗位id，然后查询出角色包，然后角色包更新权限
            $markInfo = $this->getSynMark($sid, $oldId);
            if (empty($markInfo)) {
                throw new \Exception("用户{$sid}旧岗位id[{$oldId}]获取新版岗位信息失败", 400);
            }

            //获取新版岗位id
            $jobId = $markInfo['new_id'] ?? 0;
            if (!$jobId) {
                throw new \Exception("用户{$sid}旧岗位id[{$oldId}]获取新版岗位id失败", 400);
            }

            //获取岗位详情
            $jobInfo = $this->getJobsInfoById($sid, $jobId);
            if (!isset($jobInfo['fid']) || $jobInfo['fid'] != $sid) {
                throw new \Exception("用户{$sid}新岗位id[{$jobId}]获取新版岗位详情失败", 400);
            }

            $config       = [];
            $config[$tag] = $type;
            $result       = $this->saveDataAuthByJobId($jobId, $config, $limitId);
            if (!$result) {
                throw new \Exception("用户{$sid}新岗位id[{$jobId}]设置岗位数据权限失败", 400);
            }

        } catch (\Exception $e) {
            $this->_outInfo($e->getMessage());
            $this->_log([
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 新版岗位授予权限
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param $sid
     * @param $memberId
     * @param $params
     *
     * @return false
     */
    protected function newStaffGive($sid, $memberId, $params)
    {
        try {
            $oldIds   = $params['role_ids'] ?? '';
            $userId   = $params['user_id'] ?? 0;
            $oldIdArr = array_unique(array_filter(explode(',', $oldIds)));

            if (!$userId) {
                throw new \Exception('新版岗位授予权限设置失败，参数错误', 400);
            }

            $packIds = [];
            if (!empty($oldIdArr)) {
                //查询获取新的岗位id，然后查询出角色包，然后角色包更新权限
                $markList = $this->getSynMarkList($sid, $oldIdArr);
                if (empty($markList)) {
                    throw new \Exception("用户{$sid}旧岗位id[{$oldIds}]获取新版岗位信息失败", 400);
                }

                //获取新版岗位id
                $jobIds = array_column($markList, 'new_id');
                if (empty($jobIds) || count($jobIds) != count($oldIdArr)) {
                    throw new \Exception("用户{$sid}旧岗位id[{$oldIds}]获取新版岗位id失败", 400);
                }

                //获取岗位详情
                $jobList = $this->getJobsList($sid, $jobIds);
                foreach ($jobList as $jobInfo) {
                    $jobId = $jobInfo['id'];
                    if (!isset($jobInfo['fid']) || $jobInfo['fid'] != $sid || empty($jobInfo['pack_id'])) {
                        throw new \Exception("用户{$sid}新岗位id[{$jobId}]获取新版岗位详情失败", 400);
                    }
                    $packIds[] = intval($jobInfo['pack_id']);
                }
            }

            //重新覆盖员工角色包
            $result = $this->merchantUserBindPackage($sid, $userId, $sid, $packIds);
            if (!$result) {
                throw new \Exception("用户{$sid}旧岗位id[{$oldIds}]新版重新覆盖员工角色包失败", 400);
            }

        } catch (\Exception $e) {
            $this->_outInfo($e->getMessage());
            $this->_log([
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 新版员工删除
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param  int    $sid         商户id
     * @param  int    $memberId    用户id
     * @param  array  $params      参数集合
     *
     * @return bool
     */
    protected function newStaffDel($sid, $memberId, $params)
    {
        try {
            $userId = $params['user_id'] ?? 0;

            if (!$userId) {
                throw new \Exception('新版员工删除失败，参数错误', 400);
            }

            //获取角色包id
            $packIds = [];

            //重新覆盖员工角色包
            $result = $this->merchantUserBindPackage($sid, $userId, $memberId, $packIds);
            if (!$result) {
                throw new \Exception("用户{$sid}删除，清空角色包失败", 400);
            }

        } catch (\Exception $e) {
            $this->_outInfo($e->getMessage());
            $this->_log([
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 旧版创建岗位
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param $sid
     * @param $memberId
     * @param $params
     *
     * @return false
     */
    protected function oldCreate($sid, $memberId, $params)
    {
        return false;
    }

    /**
     * 旧版编辑岗位
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param $sid
     * @param $memberId
     * @param $params
     *
     * @return false
     */
    protected function oldEdit($sid, $memberId, $params)
    {
        try {
            $name  = $params['title'] ?? '';
            $desc  = $params['remarks'] ?? '';
            $newId = $params['job_id'] ?? 0;

            if (!$newId || empty($name)) {
                throw new \Exception('同步编辑旧版岗位失败，参数错误', 400);
            }

            //新版交互跟旧版的有差异，如果没有标记过的，可能新版岗位还没配置权限，所以这边直接返回成功
            $markInfo = $this->getSynMark($sid, 0, $newId);
            if (empty($markInfo)) {
                return true;
            }

            $oldId  = $markInfo['old_id'];
            $result = $this->oldEditRole($oldId, $sid, $name, $desc);
            if (!$result) {
                throw new \Exception('同步编辑旧版岗位名称和描述失败', 400);
            }

        } catch (\Exception $e) {
            $this->_outInfo($e->getMessage());
            $this->_log([
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 旧版编辑岗位
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param $sid
     * @param $memberId
     * @param $params
     *
     * @return bool
     */
    protected function oldEditAuth($sid, $memberId, $params)
    {
        try {
            $menuIds   = $params['ids_str'] ?? '';
            $newId     = $params['job_id'] ?? 0;
            $menuIdArr = array_unique(array_filter(explode(',', $menuIds)));

            if (!$newId || empty($menuIdArr)) {
                throw new \Exception('同步编辑旧版岗位权限失败，参数错误', 400);
            }

            $menuTags = [];
            //新版id转tag
            $resData = $this->batchQuerySysMenuInfoById($menuIdArr);
            if (!empty($resData)) {
                $menuTags = array_column($resData, 'tag');
            }

            //tag为空抛出异常
            if (empty($menuTags)) {
                throw new \Exception('同步编辑旧版岗位权限失败，tag菜单标识缺失', 400);
            }

            if (count($menuIdArr) != count($menuTags)) {
                //权限缺漏标记
                $this->_outInfo("用户{$sid}新岗位[{$newId}]权限转换tag权限标识存在缺失");
            }

            //新版交互跟旧版的有差异，如果没有标记过的，可能新版岗位还没配置权限，所以这边直接返回成功
            $markInfo = $this->getSynMark($sid, 0, $newId);
            if (empty($markInfo)) {
                //获取岗位详情
                $jobInfo = $this->getJobsInfoById($sid, $newId);
                if (!isset($jobInfo['fid']) || $jobInfo['fid'] != $sid || empty($jobInfo['pack_id'])) {
                    throw new \Exception("用户{$sid}新岗位id[{$newId}]同步旧版岗位权限，获取新版岗位详情失败", 400);
                }

                $jobName = $jobInfo['name'];
                $jobDesc = $jobInfo['desc'];

                $result = $this->oldCreateRole($sid, $jobName, $jobDesc, implode(',', $menuTags), $newId);
                if (!$result) {
                    throw new \Exception('同步编辑旧版岗位权限失败，旧版岗位权限编辑报错', 400);
                }

                return true;
            }

            //旧版岗位角色id
            $oldId = $markInfo['old_id'];

            //编辑旧版岗位权限
            $result = $this->oldEditRoleAuth($oldId, implode(',', $menuTags));
            if (!$result) {
                throw new \Exception('同步编辑旧版岗位权限失败，旧版岗位权限编辑报错', 400);
            }

        } catch (\Exception $e) {
            $this->_outInfo($e->getMessage());
            $this->_log([
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 旧版岗位删除
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param $sid
     * @param $memberId
     * @param $params
     *
     * @return false
     */
    protected function oldDel($sid, $memberId, $params)
    {
        try {
            $newId    = $params['job_id'] ?? 0;
            $changeId = $params['change_id'] ?? 0;
            $isChange = $params['is_change'] ?? 0; //是否变更岗位标识 1是变更 其他不变更

            if (!$newId || ($isChange == 1 && !$changeId)) {
                throw new \Exception('同步删除旧版岗位失败，参数错误', 400);
            }

            //系统预设的（2）
            if ($changeId == 2) {
                $changeId = 0;
                $isChange = 0;
            }

            //查询获取新的岗位id，然后查询出角色包，然后角色包更新权限
            $markInfo = $this->getSynMark($sid, 0, $newId);
            if (empty($markInfo)) {
                throw new \Exception("用户{$sid}新岗位id[{$newId}]获取旧版岗位信息失败", 400);
            }

            //获取旧版岗位id
            $roleId = $markInfo['old_id'] ?? 0;
            if (!$roleId) {
                throw new \Exception("用户{$sid}新岗位id[{$newId}]获取旧版岗位id失败", 400);
            }

            $staffIds = $this->getRoleListFilterSystem($sid, $roleId);

            //删除岗位
            $result = $this->oldDelRole($sid, $roleId);
            if (!$result) {
                throw new \Exception("用户{$sid}新岗位id[{$newId}]旧岗位[{$roleId}]新岗位删除同步旧版岗位删除失败", 400);
            }

            //删除变更岗位
            if ($isChange == 1 && $changeId && !empty($staffIds)) {
                //查询获取新的岗位id，然后查询出角色包，然后角色包更新权限
                $markInfo = $this->getSynMark($sid, 0, $changeId);
                if (empty($markInfo)) {
                    throw new \Exception("用户{$sid}删除替换的新岗位id[{$changeId}]获取旧版岗位信息失败", 400);
                }

                $changeRoleId = $markInfo['old_id'] ?? 0;

                foreach ($staffIds as $userId) {
                    //同步旧版员工授予岗位
                    $result = $this->oldGiveRole($memberId, $userId, [$changeRoleId]);
                    if (!$result) {
                        throw new \Exception("用户{$sid}旧岗位id[{$changeRoleId}]员工[{$userId}]旧版员工授予岗位失败", 400);
                    }
                }
            }

        } catch (\Exception $e) {
            $this->_outInfo($e->getMessage());
            $this->_log([
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 岗位数据权限编辑保存
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param $sid
     * @param $memberId
     * @param $params
     *
     * @return false
     */
    protected function oldDataResource($sid, $memberId, $params)
    {
        try {
            $limitId = $params['limit_id'] ?? '';
            $newId   = $params['job_id'] ?? 0;
            $config  = $params['config'] ?? [];

            if (!$newId || empty($config)) {
                throw new \Exception('同步编辑旧版岗位数据权限失败，参数错误', 400);
            }

            //新版交互跟旧版的有差异，如果没有标记过的，可能新版岗位还没配置权限，所以这边直接返回成功
            $markInfo = $this->getSynMark($sid, 0, $newId);
            if (empty($markInfo)) {
                return true;
            }

            //旧版岗位角色id
            $oldId = $markInfo['old_id'];

            //处理旧版岗位数据权限
            foreach ($config as $tag => $val) {
                $res = $this->oldSetDataScope($oldId, $tag, $val, $limitId);
                if (!$res) {
                    $this->_outInfo("用户[{$sid}]资源标识[{$tag}]配置[{$val}]旧版岗位数据处理失败");
                    $this->_log("用户[{$sid}]资源标识[{$tag}]配置[{$val}]旧版岗位数据处理失败");
                }
            }
        } catch (\Exception $e) {
            $this->_outInfo($e->getMessage());
            $this->_log([
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 旧版员工授予岗位
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param $sid
     * @param $memberId
     * @param $params
     *
     * @return false
     */
    protected function oldStaffGive($sid, $memberId, $params)
    {
        try {
            $userId   = $params['user_id'] ?? 0;
            $newIds   = $params['job_ids'] ?? '';
            $newIdArr = array_unique(array_filter(explode(',', $newIds)));

            if (!$userId) {
                throw new \Exception('旧版员工授予岗位设置失败，参数错误', 400);
            }

            //系统预设岗位不用同步
            if (in_array(2, $newIdArr)) {
                unset($newIdArr[array_search('2', $newIdArr)]);
                $newIdArr = array_values($newIdArr);
            }

            $roleIds = [];
            if (!empty($newIdArr)) {
                //查询获取新的岗位id，然后查询出角色包，然后角色包更新权限
                $markList = $this->getSynMarkList($sid, [], $newIdArr);
                if (empty($markList)) {
                    throw new \Exception("用户{$sid}新岗位id[{$newIds}]获取旧版岗位信息失败", 400);
                }

                //获取新版岗位id
                $roleIds = array_column($markList, 'old_id');
                if (empty($roleIds)) {
                    throw new \Exception("用户{$sid}新岗位id[{$newIds}]获取旧版岗位id失败", 400);
                }

                if (count($roleIds) != count($newIdArr)) {
                    $this->_log([
                        'error_msg' => "用户{$sid}新岗位id[{$newIds}]获取旧版岗位id有缺漏",
                        'old_ids'   => $roleIds,
                        'new_ids'   => $newIdArr,
                    ]);
                }
            }

            //同步旧版员工授予岗位
            $result = $this->oldGiveRole($memberId, $userId, $roleIds);
            if (!$result) {
                throw new \Exception("用户{$sid}旧岗位id[" . implode(',', $roleIds) . "]旧版员工授予岗位失败", 400);
            }

        } catch (\Exception $e) {
            $this->_outInfo($e->getMessage());
            $this->_log([
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 旧版员工删除
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param $sid
     * @param $memberId
     * @param $params
     *
     * @return false
     */
    protected function oldStaffDel($sid, $memberId, $params)
    {
        return false;
    }

    /**
     * 获取操作记录
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param  int  $sid          商户id
     * @param  int  $beginTime    开始时间
     * @param  int  $endTime      结束时间
     * @param  int  $page         页码
     * @param  int  $size         页数
     *
     * @return array|false|string
     */
    protected function getOperList($sid = 0, $beginTime = 0, $endTime = 0, $page = 1, $size = 100)
    {
        return (new DataSynTmpModel())->getOperList($sid, $beginTime, $endTime, $page, $size);
    }

    /**
     * 更新操作记录状态
     * <AUTHOR>
     * @date   2023/5/23
     *
     * @param  int|array  $id      记录id
     * @param  string     $type    操作类型
     *
     * @return bool|int|string
     */
    protected function updateStateDone($id, $type = 'success')
    {
        switch ($type) {
            case 'success':
                $state = 1;
                break;
            case 'error':
                $state = 2;
                break;
            default:
                $state = 0;
        }

        return (new DataSynTmpModel())->updateStateDone($id, $state);
    }

    /**
     * 创建角色包
     * <AUTHOR>
     * @date   2023/5/17
     *
     * @param  string  $name          角色包名称
     * @param  int     $operatorId    操作人
     *
     * @return int|mixed
     */
    protected function createRolePackage(string $name, int $operatorId = 1)
    {
        if (empty($name) || !$operatorId) {
            return 0;
        }
        $res = (new \Business\JavaApi\Authority\MerchantRolePackageService())->merchantCreateRolePackage($name,
            $operatorId);
        if ($res['code'] != 200) {
            return 0;
        }

        //返回列表
        return $res['data'] ?? 0;
    }

    /**
     * 保存角色包权限
     * <AUTHOR>
     * @date   2023/5/18
     *
     * @param  int     $rolePackId    角色包id
     * @param  string  $roleName      角色名
     * @param  array   $menuList      菜单id集合
     * @param  int     $operatorId    操作人
     * @param  int     $platformId    平台id
     * @param  int     $orgId         机构id
     *
     * @return bool
     */
    protected function saveRolePackageAndMenu(int $rolePackId, string $roleName, array $menuList, int $frontAppId, int $operatorId = 1, int $platformId = Config::PLATFORM_ID, int $orgId = Config::ORG_ID)
    {
        if (!$rolePackId || empty($roleName)) {
            return false;
        }

        $createRes = (new \Business\JavaApi\Authority\MerchantRolePackageService())->setRolePackageAndMenu($rolePackId,
            $roleName, $menuList, $frontAppId, $orgId, $platformId, $operatorId);
        if ($createRes['code'] != 200) {
            return false;
        }

        return true;
    }

    /**
     * 批量通过tag查询菜单信息
     * <AUTHOR>
     * @date   2023/4/20
     *
     * @param  array  $tags          tag标识集合
     * @param  int    $frontAppId    系统应用id
     *
     * @return array
     */
    public function batchQuerySysMenuByTag(array $tags, int $frontAppId)
    {
        if (empty($tags) || !$frontAppId) {
            return [];
        }

        $res = (new \Business\JavaApi\Authority\MerchantMenuService())->batchQuerySysMenuByTag($tags, $frontAppId);
        if ($res['code'] != 200) {
            return [];
        }

        //返回列表
        return $res['data'] ?? [];
    }

    /**
     * 创建岗位
     * <AUTHOR>
     * @date   2023/5/17
     *
     * @param  int     $fid       岗位id
     * @param  string  $name      名称
     * @param  int     $packId    角色包id
     * @param  string  $desc      描述
     * @param  int     $operId    操作人
     *
     * @return int
     */
    protected function createJobs(int $fid, string $name, int $packId, string $desc = '', int $operId = 1)
    {
        if (!$fid || $name == '' || !$packId) {
            return 0;
        }

        $res = (new \Model\Authority\JobsRole())->createJobs($fid, $name, $packId, $operId, $desc);

        return intval($res);
    }

    /**
     * 更新岗位基本信息
     * <AUTHOR>
     * @date   2023/5/24
     *
     * @param  int     $fid      商户id
     * @param  int     $jobId    岗位id
     * @param  string  $name     岗位名称
     * @param  string  $desc     岗位描述
     *
     * @return int
     */
    protected function updateJobs(int $fid, int $jobId, string $name, string $desc = '')
    {
        if (!$fid || $name == '') {
            return 0;
        }

        $res = (new \Model\Authority\JobsRole())->updateJobs($jobId, $fid, $name, $desc);

        return intval($res);
    }

    /**
     * 获取岗位详情
     * <AUTHOR>
     * @date   2023/5/24
     *
     * @param  int  $fid      商户id
     * @param  int  $jobId    岗位id
     *
     * @return array
     */
    protected function getJobsInfoById(int $fid, int $jobId)
    {
        if (!$fid || !$jobId) {
            return [];
        }

        return (new \Model\Authority\JobsRole())->getJobsInfoById($fid, $jobId);
    }

    /**
     * 获取岗位列表
     * <AUTHOR>
     * @date   2023/5/24
     *
     * @param  int    $fid       商户id
     * @param  array  $jobIds    岗位id集合
     *
     * @return array|false|string
     */
    protected function getJobsList($fid, $jobIds)
    {
        if (!$fid || empty($jobIds)) {
            return [];
        }

        return (new \Model\Authority\JobsRole())->getAllJobs($fid, '', [], $jobIds);
    }

    /**
     * 获取标记
     * <AUTHOR>
     * @date   2023/5/18
     *
     * @param  int  $fid      商户id
     * @param  int  $oldId    旧标识
     * @param  int  $newId    新标识
     *
     * @return array|false|string
     */
    protected function getSynMark($fid, $oldId = 0, $newId = 0)
    {
        return (new DataSynTmpModel())->getMarkInfo($fid, $oldId, $newId);
    }

    /**
     * 获取标记列表
     * <AUTHOR>
     * @date   2023/5/18
     *
     * @param  int        $fid      商户id
     * @param  int|array  $oldId    旧标识
     * @param  int|array  $newId    新标识
     *
     * @return array|false|string
     */
    protected function getSynMarkList($fid, $oldId = 0, $newId = 0)
    {
        return (new DataSynTmpModel())->getMarkList($fid, $oldId, $newId);
    }

    /**
     * 查询角色包关联用户列表
     * <AUTHOR>
     * @date   2023/5/24
     *
     * @param  int    $sid               商户id
     * @param  array  $rolePackageIds    角色包ids
     *
     * @return array
     */
    protected function queryUserListByRolePackageIds(int $sid, array $rolePackageIds)
    {
        return (new \Business\Authority\Auth\UserRolePack())->queryUserListByRolePackageIds($sid, $rolePackageIds);
    }

    /**
     * 角色包和用户解绑
     * <AUTHOR>
     * @date   2023/5/24
     *
     * @param  array  $userSidList        用户信息集合
     * @param  array  $rolePackageList    角色包列表
     * @param  int    $operatorId         操作人
     *
     * @return bool
     */
    protected function userUnBindPackage(array $userSidList, array $rolePackageList, int $operatorId)
    {
        $res = (new \Business\JavaApi\Authority\MerchantUserPackageService())->userUnBindPackage($userSidList,
            $rolePackageList, $operatorId);
        if ($res['code'] != 200) {
            return false;
        }

        return true;
    }

    /**
     * 删除角色包id
     * <AUTHOR>
     * @date   2023/5/24
     *
     * @param  int  $rolePackageId    角色包id
     *
     * @return bool
     */
    protected function delRolePackageById(int $rolePackageId)
    {
        $res = (new \Business\JavaApi\Authority\RolePackageService())->delRolePackageById($rolePackageId);
        if ($res['code'] != 200) {
            return false;
        }

        return true;
    }

    /**
     * 删除岗位
     * <AUTHOR>
     * @date   2023/5/24
     *
     * @param  int  $fid
     * @param  int  $jobId
     *
     * @return bool|int|string
     */
    protected function delJobs(int $fid, int $jobId)
    {
        return (new \Model\Authority\JobsRole())->delJobs($fid, $jobId);
    }

    /**
     * 设置岗位数据范围
     * <AUTHOR>
     * @date   2023/5/24
     *
     * @param  int     $jobId      岗位id
     * @param  array   $config     配置参数
     * @param  string  $limitId    部分限制
     *
     * @return bool
     */
    protected function saveDataAuthByJobId(int $jobId, array $config, string $limitId = '')
    {
        $result = (new \Business\Authority\Auth\DataResource())->saveDataAuthByJobId($jobId, $config, $limitId);
        if ($result['code'] != 200) {
            return false;
        }

        return true;
    }

    /**
     * 新版员工授予岗位
     * <AUTHOR>
     * @date   2023/5/24
     *
     * @param  int    $sid                商户id
     * @param  int    $userId             用户id
     * @param  int    $operatorId         操作人
     * @param  array  $rolePackageList    角色包id集合
     *
     * @return bool
     */
    protected function merchantUserBindPackage(int $sid, int $userId, int $operatorId, array $rolePackageList)
    {
        $res = (new \Business\JavaApi\Authority\MerchantUserRelationPackageService())->merchantUserBindPackage($sid,
            $userId, $operatorId, $rolePackageList);
        if ($res['code'] != 200) {
            return false;
        }

        return true;
    }

    /**
     * 旧版角色编辑
     * <AUTHOR>
     * @date   2023/5/24
     *
     * @param  int     $id               角色id
     * @param  int     $fid              商户id
     * @param  string  $name             角色名称
     * @param  string  $desc             角色描述
     * @param  string  $effectEnd        失效时间
     * @param  int     $isSystemLimit    1是系统预设 0否
     *
     * @return bool
     */
    protected function oldEditRole($id, $fid, $name, $desc, $effectEnd = '2029-01-01', $isSystemLimit = 0)
    {
        $res = (new \Business\Authority\Base\AuthUserRole())->editRole($id, $fid, $name, $desc, $effectEnd,
            $isSystemLimit);
        if ($res['code'] != 200) {
            return false;
        }

        return true;
    }

    /**
     * 批量根据id获取menu信息
     * <AUTHOR>
     * @date   2023/5/25
     *
     * @param  array  $menuIds
     *
     * @return array
     */
    protected function batchQuerySysMenuInfoById(array $menuIds)
    {
        $res = (new \Business\JavaApi\Authority\MenuService())->batchQuerySysMenuInfoById($menuIds);
        if ($res['code'] != 200) {
            return [];
        }

        return is_array($res['data']) ? $res['data'] : [];
    }

    /**
     * 旧版编辑角色权限
     * <AUTHOR>
     * @date   2023/5/25
     *
     * @param  int     $roleId     角色id
     * @param  string  $tag        菜单标识
     * @param  int     $channel    渠道
     *
     * @return bool
     */
    protected function oldEditRoleAuth($roleId, $tag, $channel = 1)
    {
        $authRole = new \Business\Authority\Base\AuthUserRole();

        try {
            $roleMenuModel = new \Model\Authority\RoleMenu();
            // 获取已有的关联
            $relateRes = $roleMenuModel->getRoleMenuByChannel($channel, $roleId,
                'rm.id as id, rm.menu_tag as tag', 0);
            $tagsUsed = array_column($relateRes, 'tag');
            if (in_array('oper', $tagsUsed)) {
                $tagArr = explode(',', $tag);
                $tagArr[] = 'oper';
                $tag = implode(',', $tagArr);
            }

            $model = new \Model\Authority\Role();
            // 开启事务
            $model->startTrans();

            // 更新角色菜单
            $res = $authRole->editRoleMenu($tag, $roleId, $channel);
            if ($res['code'] != 200) {
                throw new \Exception($res['msg'], $res['code']);
            }

            // 更新角色元素
            $res = $authRole->editRoleElement($tag, $roleId, $channel);
            if ($res['code'] != 200) {
                throw new \Exception($res['msg'], $res['code']);
            }

            // 提交事务
            $model->commit();

        } catch (\Exception $e) {
            // 事务回滚
            $model->rollback();

            $this->_outInfo($e->getMessage());
            $this->_log([
                'action'  => func_get_args(),
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 旧版岗位数据权限设置
     * <AUTHOR>
     * @date   2023/5/25
     *
     * @param  int     $roleId         角色id
     * @param  string  $resourceTag    资源标识
     * @param  int     $type           配置值
     * @param  string  $limitId        具体员工限制
     * @param  int     $channel        渠道
     *
     * @return bool
     */
    protected function oldSetDataScope($roleId, $resourceTag, $type, $limitId = '', $channel = 1)
    {
        return (new \Business\Authority\Base\AuthDataScope())->handleScope($roleId, $resourceTag, $type, $limitId,
            $channel);
    }

    /**
     * 旧版员工授予岗位
     * <AUTHOR>
     * @date   2023/5/25
     *
     * @param  int    $operateId    操作人
     * @param  int    $memberId     员工
     * @param  array  $roleIdArr    岗位id集合
     *
     * @return bool
     */
    protected function oldGiveRole($operateId, $memberId, $roleIdArr)
    {
        $authRole = new \Business\Authority\Base\AuthUserRole();

        try {
            // 判断用户是否有系统默认设置的权限， 如果有则设置失效
            $res = $authRole->checkUserSystemRole($memberId);
            if (!$res) {
                throw new \Exception('设置系统默认角色失败', 400);
            }

            // 赋予角色权限
            $res = $authRole->giveRole($operateId, $memberId, $roleIdArr);

            if ($res['code'] != 200) {
                throw new \Exception($res['msg'], $res['code']);
            }

        } catch (\Exception $e) {
            $this->_outInfo($e->getMessage());
            $this->_log([
                'action'  => func_get_args(),
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 旧版删除角色
     * <AUTHOR>
     * @date   2023/5/25
     *
     * @param  int  $memberId    账号id
     * @param  int  $id          岗位角色id
     *
     * @return bool
     */
    protected function oldDelRole($memberId, $id)
    {
        if (empty($id)) {
            return false;
        }

        $authRole = new \Business\Authority\Base\AuthUserRole();

        return $authRole->delRole($memberId, $id);
    }

    /**
     * 获取岗位列表和拥有岗位人员
     * <AUTHOR>
     * @date   2023/5/17
     *
     * @param  int  $memberId
     * @param  int  $roleId
     *
     * @return array
     */
    protected function getRoleListFilterSystem($memberId = 0, $roleId = 0)
    {
        if (!$memberId || !$roleId) {
            return [];
        }

        // 获取用户列表
        $memberModel = (new \Model\Member\Member());
        $memberRes   = $memberModel->getStaffList($memberId, '', 1, 10000);
        // 获取memberIds
        $memberIds = array_column($memberRes['list'], 'id');

        // 获取对应岗位
        $roleMemberModel = (new \Model\Authority\RoleMember());
        $roleMemberRes   = $roleMemberModel->getMemberRelationByMemberIdArr($memberIds,
            'rm.id, rm.role_id, rm.member_id, rm.create_time', 0);

        $staffIds = [];
        foreach ($roleMemberRes as $staff) {
            if ($staff['role_id'] == $roleId) {
                $staffIds[] = $staff['member_id'];
            }
        }

        return $staffIds;
    }

    /**
     * 旧版创建岗位角色
     * <AUTHOR>
     * @date   2023/5/25
     *
     * @param  int     $fid
     * @param  string  $name
     * @param  string  $desc
     * @param  string  $tag
     *
     * @return bool
     */
    protected function oldCreateRole($fid, $name, $desc, $tag, $jobId)
    {
        $authRole = new \Business\Authority\Base\AuthUserRole();

        $effectStart  = date("Y/m/d");
        $effectEnd    = "2029-01-01";
        $isSystemInit = 0;
        $channelId    = 1;

        $model = new \Model\Authority\Role();

        try {
            // 开启事务
            $model->startTrans();

            // 创建角色
            $res = $authRole->createRole($fid, $channelId, $name, $desc, $effectStart, $effectEnd, $isSystemInit);
            if ($res['code'] != 200) {
                throw new \Exception($res['msg'], $res['code']);
            }
            // 角色ID
            $roleId = $res['data'];

            // 创建角色菜单关联
            $res = $authRole->createRoleMenu($tag, $roleId, $channelId);
            if ($res['code'] != 200) {
                throw new \Exception($res['msg'], $res['code']);
            }

            // 创建角色元素关联
            $res = $authRole->createRoleElement($tag, $roleId, $channelId);
            if ($res['code'] != 200) {
                throw new \Exception($res['msg'], $res['code']);
            }

            //创建数据双写标记
            $synMark = $this->createSynMark($fid, $roleId, $jobId);
            if (!$synMark) {
                throw new \Exception(json_encode(["用户{$fid}岗位[{$name}]双写标记失败", '参数', [$fid, $roleId, $jobId]],
                    JSON_UNESCAPED_UNICODE), 400);
            }

            // 事务提交
            $model->commit();

        } catch (\Exception $e) {
            // 事务回滚
            $model->rollback();

            $this->_outInfo($e->getMessage());
            $this->_log([
                'action'  => func_get_args(),
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'msg'     => $e->getMessage(),
            ]);

            return false;
        }

        return true;
    }

    /**
     * 创建标记
     * <AUTHOR>
     * @date   2023/5/18
     *
     * @param  int  $fid      商户id
     * @param  int  $oldId    旧标识
     * @param  int  $newId    新标识
     *
     * @return false|int|mixed|string
     */
    protected function createSynMark($fid, $oldId = 0, $newId = 0)
    {
        return (new DataSynTmpModel())->createSyn($fid, $oldId, $newId);
    }
}