<?php
/**
 * 权限中心脚本基础方法
 * <AUTHOR>
 * @date   2022/11/17
 */

namespace CrontabTasks\Authority;

class Base
{
    //不同环境：生产->预生产->本地/内网

    //前台应用id
    const FRONT_APP_ID = ENV == 'PRODUCTION' ? 10 : (ENV == 'TEST' ? 47 : 72);
    //平台id
    const PLATFORM_ID = ENV == 'PRODUCTION' ? 10100 : (ENV == 'TEST' ? 10010 : 11);
    //组织id 目前还未确认时固定值还是变化的
    const ORG_ID = ENV == 'PRODUCTION' ? 15 : (ENV == 'TEST' ? 35 : 78);

    //主url
    const DOMAINNAME = ENV == 'PRODUCTION' ? 'my.12301.cc' : (ENV == 'TEST' ? 'my.12301dev.com' : 'my.12301.local');

    //日志地址
    protected $_logDir = 'crontab_html/authority_script_handle';
    //脚本开始时间
    protected $_startDate = '';
    //导入菜单文件名
    protected $_createMenufileName = 'menu';
    //导入角色包文件名
    protected $_createPackfileName = 'pack';

    /**
     * 获取原始数据
     * <AUTHOR>
     * @date   2022/11/17
     *
     * @param $name
     *
     * @return array|mixed
     */
    public function getJsonArr($name)
    {
        $file =  __DIR__ . '/json/' . $name . '.json';
        if (!file_exists($file)) {
            return [];
        }

        return json_decode(file_get_contents($file), true);
    }

    /**
     * 打印日志
     * <AUTHOR>
     * @date   2022/11/17
     *
     */
    protected function _outInfo()
    {
        $msgArr = func_get_args();
        echo json_encode($msgArr, JSON_UNESCAPED_UNICODE) . "\n";
    }

    /**
     * 记录下执行时间
     * <AUTHOR>
     * @date   2022/8/26
     *
     * @param  bool  $isStart
     * @param  bool  $isEnd
     *
     * @return bool
     */
    protected function recordDate(bool $isStart = false, bool $isEnd = false): bool
    {
        //记录下时间
        $date = date("Y-m-d H:i:s");

        if ($isStart) {
            $this->_startDate = $date;
            $this->_outInfo("开始时间：" . $date);
            $this->_log(["开始时间：" . $date]);
        }

        if ($isEnd) {
            $this->_outInfo("结束时间：" . $date);
            $this->_outInfo("耗时：" . (strtotime($date) - strtotime($this->_startDate)) . "s");
            $this->_log(["结束时间：" . $date, "耗时：" . (strtotime($date) - strtotime($this->_startDate)) . "s"]);
        }

        return true;
    }

    /**
     * 记录日志
     * <AUTHOR>
     * @date   2022/11/17
     *
     * @param $msg
     */
    protected function _log($msg)
    {
        $log = json_encode([
            'msg' => $msg,
        ], JSON_UNESCAPED_UNICODE);

        pft_log($this->_logDir, $log);
    }
}