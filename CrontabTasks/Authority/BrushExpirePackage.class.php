<?php
/**
 * 套餐过期加过期套餐角色包
 * <AUTHOR>
 * @date   2023/5/19
 */

namespace CrontabTasks\Authority;

use Model\AppCenter\ModuleList as ModuleListModel;

class BrushExpirePackage extends Base
{
    //是否预处理：0否1是
    private $_pretreatment = 1;

    //指定用户id操作
    private $_fid = '';

    //供应商过期角色包
    const EXPIRED_SUPPLIER_PACK_ROLE_ID = ENV == 'PRODUCTION' ? 229 : (ENV == 'TEST' ? 141 : 790);

    //分销商过期角色包
    const EXPIRED_DISTRIBUTOR_PACK_ROLE_ID = ENV == 'PRODUCTION' ? 228 : (ENV == 'TEST' ? 142 : 791);

    /**
     * 刷数据前可以copy一份到内网，计算下刷的数据量
     */

    /**
     * 入口文件
     * 命令： php /var/www/html/Service/Crontab/runNew.php Authority/BrushExpirePackage run -pretreatment -fid
     * fid支持批量用户，逗号隔开：3385,6970
     * <AUTHOR>
     * @date   2022/11/17
     *
     */
    public function run()
    {
        $this->recordDate(true);

        //需要指定用户处理
        $params = $GLOBALS['argv'];

        //预处理判断
        $this->_pretreatment = $params[3] ?? 1;

        //指定商户数据同步
        $this->_fid = $params[4] ?? '';

        $this->runWork();

        $this->recordDate(false, true);
    }

    /**
     * 脚本逻辑
     * <AUTHOR>
     * @date   2023/5/22
     *
     */
    protected function runWork()
    {
        $fids = [];
        if (!empty($this->_fid)) {
            $fids = explode(',', $this->_fid);
            $this->_outInfo('指定用户处理，处理数：' . count($fids));
        }

        $memberApi = new \Business\JavaApi\Member\Query\MemberQuery();

        $page = 1;
        $size = (10 > count($fids) && !empty($fids)) ? count($fids) : 10;
        $max  = empty($fids) ? 760000 : count($fids);//760000;

        while (true) {
            try {
                if (($page * $size) > $max) {
                    $this->_outInfo('执行总数：' . (($page - 1) * $size));
                    break;
                }

                $userListRes = $this->getUserList([], $fids, $page, $size);
                if (empty($userListRes)) {
                    $this->_outInfo('-执行总数：' . (($page - 1) * $size));
                    break;
                }

                //重新验证下用户是否有在生效
                $memberIds = array_column($userListRes, 'member_id');
                $usedRes   = $this->getUsedUser($memberIds);
                //正在使用的用户id
                $usedMemberIds = array_column($usedRes, 'member_id');

                //过期用户id
                $expiredMemberIds = array_diff($memberIds, $usedMemberIds);
                if (empty($expiredMemberIds)) {
                    throw new \Exception("没有过期用户", 400);
                }

                //获取用户信息
                $memberInfoRes = $memberApi->batchMemberInfoByIds($expiredMemberIds);
                $fidDtypeMap   = array_column($memberInfoRes['data'] ?? [], 'dtype', 'id');
                if (empty($fidDtypeMap)) {
                    throw new \Exception('用户信息获取失败，查询id：' . implode(',', $expiredMemberIds), 400);
                }

                //更新过期角色包
                foreach ($expiredMemberIds as $sid) {
                    $dtype = $fidDtypeMap[$sid] ?? -1;
                    if ($dtype == -1) {
                        $this->_outInfo("商户类型获取失败, id：{$sid}");
                        continue;
                    }

                    //目前套餐过期只存在供应商和分销商
                    if (!in_array($dtype, [0, 1])) {
                        $this->_outInfo("【商户类型不符】商户：{$sid}, 类型：{$dtype}");
                        continue;
                    }

                    $packId = $dtype != 1 ? self::EXPIRED_SUPPLIER_PACK_ROLE_ID : self::EXPIRED_DISTRIBUTOR_PACK_ROLE_ID;

                    if (!$this->_pretreatment) {
                        $bindRes = $this->bindRolePack($sid, $sid, $sid, [$packId]);
                        if ($bindRes['code'] != 200) {
                            //更新失败
                            $this->_outInfo("商户：{$sid}, 类型：{$dtype}, 角色包：{$packId}, 结果：更新失败");
                            continue;
                        }

                        //日志记录下成功的结果
                        $this->_log("商户：{$sid}, 类型：{$dtype}, 角色包：{$packId}, 结果：更新成功");

                        $this->_outInfo("商户：{$sid}, 类型：{$dtype}, 角色包：{$packId}, 结果：更新成功");
                    } else {

                        $this->_outInfo("【预处理成功】商户：{$sid}, 类型：{$dtype}, 角色包：{$packId}");
                    }
                }

            } catch (\Exception $e) {
                $code = $e->getCode();
                $msg  = $e->getMessage();

                if ($code != 200) {
                    $this->_log($msg);
                    $this->_outInfo($msg);
                }
            }

            $page++;
        }
    }

    /**
     * 获取已过期的用户Id
     * <AUTHOR>
     * @date   2023/5/19
     *
     * @param  int    $page
     * @param  int    $size
     * @param  array  $packageIds
     * @param  array  $fids
     *
     * @return array
     */
    protected function getUserList(array $packageIds = [], array $fids = [], int $page = 1, int $size = 100)
    {
        $model = new ModuleListModel();

        $where = [
            'begin_time' => ['lt', strtotime(date("Y-m-d 00:00:00"))],
            'end_time'   => ['lt', strtotime(date("Y-m-d 00:00:00"))],
        ];

        if (!empty($packageIds)) {
            $where['package_id'] = ['in', $packageIds];
        }
        if (!empty($fids)) {
            $where['member_id'] = ['in', $fids];
        }

        // $where['_string'] = 'begin_time<>end_time';

        $res = $model->table($model::PFT_MODULE_PACKAGE_LOG)->where($where)->field('id,member_id,package_id')->group('member_id')->page($page,
            $size)->select();

        return is_array($res) ? $res : [];
    }

    /**
     * 获取商户是否有正在使用的套餐
     * <AUTHOR>
     * @date   2023/5/19
     *
     * @param $fid
     *
     * @return array
     */
    protected function getUsedUser($fid)
    {
        $model = new ModuleListModel();

        $where = [
            'begin_time' => ['elt', strtotime(date("Y-m-d 00:00:00"))],
            'end_time'   => ['egt', strtotime(date("Y-m-d 23:59:59"))],
        ];
        if ($fid) {
            if (is_array($fid)) {
                $where['member_id'] = ['IN', $fid];
            } else {
                $where['member_id'] = $fid;
            }
        }
        $where['_string'] = 'begin_time<>end_time';

        $res = $model->table($model::PFT_MODULE_PACKAGE_LOG)->where($where)->field('id,member_id,package_id,count(`id`) as total')->group('member_id')->select();

        return is_array($res) ? $res : [];
    }

    /**
     * 获取供应商套餐id
     * <AUTHOR>
     * @date   2023/5/19
     *
     * @return array|false|string
     */
    protected function getPackageIdsByRole($role = 0)
    {
        $model = new ModuleListModel();

        $where = [
            'role' => ($role != 1 ? 0 : 1),
        ];

        $res = $model->table($model::PFT_MODULE_PACKAGE)->where($where)->field('id')->select();

        return is_array($res) ? $res : [];
    }

    /**
     * 覆盖用户角色包
     * <AUTHOR>
     * @date   2023/5/19
     *
     * @param         $sid
     * @param         $userId
     * @param         $operatorId
     * @param  array  $rolePackageList
     *
     * @return array
     */
    protected function bindRolePack($sid, $userId, $operatorId, $rolePackageList = [])
    {
        return (new \Business\JavaApi\Authority\MerchantUserRelationPackageService())->merchantUserBindPackage($sid,
            $userId, $operatorId, $rolePackageList);
    }
}