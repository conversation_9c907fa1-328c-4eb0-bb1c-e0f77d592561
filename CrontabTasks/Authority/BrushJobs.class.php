<?php
/**
 * 同步岗位权限
 * <AUTHOR>
 * @date   2023/5/17
 */

namespace CrontabTasks\Authority;

use Model\Authority\Role as RoleModel;
use Business\Authority\AuthLogic as AuthLogicBiz;
use Business\Authority\Auth\Config;
use Model\Authority\DataSynTmp as DataSynTmpModel;
use Model\Authority\DataResource as DataResourceModel;

class BrushJobs extends Base
{

    const FRONT_APP_IDS = [
        AuthLogicBiz::FRONT_APP_DEFAULT_ID,
        AuthLogicBiz::FRONT_APP_USER_CENTER_ID,
    ];

    /**
     * 入口文件
     * 命令： php /var/www/html/Service/Crontab/runNew.php Authority/BrushJobs run
     * fid支持批量用户，逗号隔开：3385,6970
     * <AUTHOR>
     * @date   2022/11/17
     *
     */
    public function run()
    {
        $this->recordDate(true);

        //需要指定用户处理
        $params = $GLOBALS['argv'];

        //指定商户数据同步
        $fid = $params[3] ?? '';

        $this->runWork($fid);

        $this->recordDate(false, true);
    }

    /**
     * 脚本执行
     * <AUTHOR>
     * @date   2023/5/18
     *
     * @param  int  $fid
     */
    protected function runWork($fid = '')
    {
        //TODO::需要补充能获取用户列表的方法
        $fids = [];
        if (!empty($fid)) {
            $fids = explode(',', $fid);
        }

        foreach ($fids as $sid) {
            $page = 1;
            $size = 1;
            while (true) {
                try {
                    $roleList = $this->getRoleListFilterSystem($sid, $page, $size);
                    if (empty($roleList)) {
                        $this->_outInfo("获取用户{$sid}岗位列表为空");
                        break;
                    }
                    //单个处理
                    $roleInfo = $roleList[0] ?? [];
                    if (empty($roleInfo)) {
                        throw new \Exception("用户{$sid}岗位数据异常", 400);
                    }

                    //拥有该岗位的员工
                    $staffIds = $roleInfo['staff_ids'] ?? [];
                    $name     = $roleInfo['name'] ?? '';
                    $desc     = $roleInfo['desc'] ?? '';
                    $roleId   = $roleInfo['id'] ?? 0;
                    if (empty($name) || !$roleId) {
                        throw new \Exception("用户{$sid}岗位信息缺失", 400);
                    }

                    //要查询是否已经处理过了pft_authority_user_data_syn_tmp
                    $checkMark = $this->getSynMark($sid, $roleId);
                    if ($checkMark) {
                        throw new \Exception("用户{$sid}岗位[{$name}]已标识, ID:{$checkMark['id']}", 400);
                    }

                    //获取岗位数据权限配置
                    $dataResourceList = $this->getDataResourceScope($roleId);
                    //获取岗位权限tag
                    $roleTagArr = $this->getRoleUseTag($roleId);
                    if (empty($roleTagArr)) {
                        throw new \Exception("用户{$sid}岗位[{$name}]权限为空", 400);
                    }

                    //是否拥有岗位权限
                    $isOperator = false;
                    //判断是否有岗位管理权限,存在则移除
                    if (in_array('oper', $roleTagArr)) {
                        unset($roleTagArr[array_search('oper', $roleTagArr)]);
                        $roleTagArr = array_values($roleTagArr);
                        $isOperator = true;
                        $this->_outInfo("用户{$sid}岗位[{$name}]存在员工管理");
                    }

                    //创建空的角色包
                    $packId = $this->createRolePackage($name, $sid);
                    if (!$packId) {
                        throw new \Exception("用户{$sid}岗位[{$name}]同步创建角色包失败", 400);
                    }

                    //查询 这边因为商户端菜单树拆成多个系统应用，这边处理下 ！！！！
                    foreach (self::FRONT_APP_IDS as $frontAppId) {
                        $allMenuIds = []; //含链路
                        $resData    = $this->batchQuerySysMenuByTag($roleTagArr, $frontAppId);
                        $path       = array_column($resData, 'path');
                        foreach ($path as $item) {
                            $arr        = array_filter(explode('/', $item));
                            $allMenuIds = array_merge($allMenuIds, $arr);
                        }
                        $allMenuIds = array_values(array_unique($allMenuIds));
                        // if (empty($allMenuIds)) {
                        //     throw new \Exception("用户{$sid}岗位[{$name}]权限菜单id为空", 400);
                        // }
                        //赋权
                        $packGive = $this->saveRolePackageAndMenu($packId, "job_{$sid}_{$name}", $allMenuIds,
                            $frontAppId, $sid);
                        if (!$packGive) {
                            throw new \Exception("用户{$sid}岗位[{$name}]同步创建角色包权限失败", 400);
                        }
                    }

                    //创建岗位
                    $jobId = $this->createJobs($sid, $name, $packId, $desc, $sid);
                    if (!$jobId) {
                        throw new \Exception("用户{$sid}岗位[{$name}]同步创建岗位关联失败", 400);
                    }

                    //处理下岗位数据权限刷入
                    if (!empty($dataResourceList)) {
                        $dataResourceRecord = [];
                        foreach ($dataResourceList as $tmp) {
                            $dataResourceRecord[] = [
                                'job_id'      => $jobId,
                                'resource_id' => $tmp['resource_id'],
                                'auth_type'   => $tmp['type'],
                                'limit_id'    => $tmp['limit_id'],
                                'update_time' => $tmp['update_time'],
                            ];
                        }

                        $insertDataResourceScopeRes = $this->insertDataResourceScope($dataResourceRecord);
                        if (!$insertDataResourceScopeRes) {
                            $this->_outInfo("用户{$sid}岗位[{$name}]同步创建数据权限失败");
                            $this->_log("用户{$sid}岗位[{$name}]角色id[{$roleId}]新岗位id[{$jobId}]同步创建数据权限失败");
                        }
                    }

                    //存在员工，重新赋权
                    if (!empty($staffIds)) {
                        $packIds = [$packId];
                        //有岗位管理附上系统岗位
                        if ($isOperator) {
                            $adminJobs = $this->getSysStaffJobs();
                            $packIds   = array_merge(array_column($adminJobs, 'id'), $packIds);
                            $packIds   = array_map('intval', $packIds);
                        }

                        //给员工赋权
                        $userSidList = [];
                        foreach ($staffIds as $userId) {
                            $userSidList[] = [
                                'userId' => $userId,
                                'sid'    => $sid,
                            ];
                        }
                        $bindRes = $this->userBindPackage($userSidList, $packIds, $sid);
                        if (!$bindRes) {
                            throw new \Exception("用户{$sid}岗位[{$name}]同步员工关联岗位失败", 400);
                        }
                    }

                    //创建数据双写标记
                    $synMark = $this->createSynMark($sid, $roleId, $jobId);
                    if (!$synMark) {
                        throw new \Exception(json_encode(["用户{$sid}岗位[{$name}]双写标记失败", '参数', [$sid, $roleId, $jobId]],
                            JSON_UNESCAPED_UNICODE), 400);
                    }

                    $page++;

                } catch (\Exception $e) {
                    $msg = $e->getMessage();

                    $this->_outInfo($msg);
                    $this->_log($msg);
                    $page++;
                }
            }
        }
    }

    /**
     * 获取岗位列表和拥有岗位人员
     * <AUTHOR>
     * @date   2023/5/17
     *
     * @param         $memberId
     * @param         $channelId
     * @param         $page
     * @param         $size
     *
     * @return array
     */
    protected function getRoleListFilterSystem($memberId = 0, $page = 1, $size = 100, $channelId = 1)
    {
        if (empty($memberId)) {
            return [];
        }

        $status = 0;//是否删除1是0否
        $res    = $this->getRoleList($memberId, $channelId, $page, $size, $status);
        $res    = $this->handleRoleList($res ?? []);
        $list   = $res;
        if (!empty($res)) {
            // 获取用户列表
            $memberModel = (new \Model\Member\Member());
            $memberRes   = $memberModel->getStaffList($memberId, '', 1, 10000);
            // 获取memberIds
            $memberIds = array_column($memberRes['list'], 'id');

            // 获取对应岗位
            $roleMemberModel = (new \Model\Authority\RoleMember());
            $roleMemberRes   = $roleMemberModel->getMemberRelationByMemberIdArr($memberIds,
                'rm.id, rm.role_id, rm.member_id, rm.create_time', 0);
            $list            = [];
            foreach ($res as $role) {
                $sum      = 0;
                $staffIds = [];
                foreach ($roleMemberRes as $staff) {
                    if ($staff['role_id'] == $role['id']) {
                        $sum        += 1;
                        $staffIds[] = $staff['member_id'];
                    }
                }
                $role['total']     = $sum;
                $role['staff_ids'] = $staffIds;

                if ($role['status'] != 1) {
                    $list[] = $role;
                }
            }
        }

        return $list;
    }

    /**
     * 获取角色列表，包括删除的 后续需求原因,
     * <AUTHOR>
     * @date   2019-05-31
     *
     * @param  int     $fid          用户Id
     * @param  string  $channelId    渠道Id
     * @param  int     $page         当前页
     * @param  int     $size         获取数目
     *
     * @return array
     */
    protected function getRoleList($fid, $channelId = 1, $page = 1, $size = 15, $status = -1)
    {
        if (empty($fid)) {
            return [];
        }

        $where = [
            'fid'            => $fid,
            'is_system_init' => 0,
        ];

        if (!empty($channelId)) {
            $where['channel_id'] = $channelId;
        }

        if ($status != -1) {
            $where['is_deleted'] = $status;
        }

        $field = 'id, name, desc, create_time, is_deleted as status, channel_id';

        $res = (new RoleModel())->table(RoleModel::ROLE_TABLE)->where($where)->field($field)->page($page,
            $size)->select();

        return is_array($res) ? $res : [];
    }

    /**
     * 对角色列表数据返回处理
     * <AUTHOR>
     * @date   2019-06-04
     *
     * @param  array  $list
     *
     * @return array
     */
    protected function handleRoleList($list)
    {
        if (empty($list)) {
            return [];
        }

        // 获取渠道map
        $channelMap = load_config('channel', 'authority');

        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'id'          => $item['id'],
                'name'        => $item['name'],
                'desc'        => $item['desc'],
                'status'      => $item['status'],
                'channel'     => $channelMap[$item['channel_id']],
                'create_time' => date('Y-m-d H:i', $item['create_time']),
                'total'       => 0,
            ];
        }

        return $data;
    }

    /**
     * 获取角色勾选的tag权限
     * <AUTHOR>
     * @date   2023/5/17
     *
     * @param $roleId
     *
     * @return array
     */
    protected function getRoleUseTag($roleId)
    {
        if (!$roleId) {
            return [];
        }

        // 获取角色拥有的菜单tag_name列表
        $menuTagList = (new \Business\Authority\Base\AuthMenu())->getRoleMenuTagList($roleId);
        // 获取角色拥有的元素tag_name列表
        $elementTagList = (new \Business\Authority\Base\AuthElement())->getRoleElementTagList($roleId);

        return array_merge($menuTagList, $elementTagList);
    }

    /**
     * 批量通过tag查询菜单信息
     * <AUTHOR>
     * @date   2023/4/20
     *
     * @param  array  $tags          tag标识集合
     * @param  int    $frontAppId    系统应用id
     *
     * @return array
     */
    public function batchQuerySysMenuByTag(array $tags, int $frontAppId)
    {
        if (empty($tags) || !$frontAppId) {
            return [];
        }

        $res = (new \Business\JavaApi\Authority\MerchantMenuService())->batchQuerySysMenuByTag($tags, $frontAppId);
        if ($res['code'] != 200) {
            return [];
        }

        //返回列表
        return $res['data'] ?? [];
    }

    /**
     * 创建角色包
     * <AUTHOR>
     * @date   2023/5/17
     *
     * @param  string  $name          角色包名称
     * @param  int     $operatorId    操作人
     *
     * @return int|mixed
     */
    protected function createRolePackage(string $name, int $operatorId = 1)
    {
        if (empty($name) || !$operatorId) {
            return 0;
        }
        $res = (new \Business\JavaApi\Authority\MerchantRolePackageService())->merchantCreateRolePackage($name,
            $operatorId);
        if ($res['code'] != 200) {
            return 0;
        }

        //返回列表
        return $res['data'] ?? 0;
    }

    /**
     * 创建岗位
     * <AUTHOR>
     * @date   2023/5/17
     *
     * @param  int     $fid       岗位id
     * @param  string  $name      名称
     * @param  int     $packId    角色包id
     * @param  string  $desc      描述
     * @param  int     $operId    操作人
     *
     * @return int
     */
    protected function createJobs(int $fid, string $name, int $packId, string $desc = '', int $operId = 1)
    {
        if (!$fid || $name == '' || !$packId) {
            return 0;
        }

        $res = (new \Model\Authority\JobsRole())->createJobs($fid, $name, $packId, $operId, $desc);

        return intval($res);
    }

    /**
     * 获取系统预设管理岗位
     * 目前只有一个，后续要用到这个脚本需要注意，是否有多个
     * <AUTHOR>
     * @date   2023/5/18
     *
     * @return array
     */
    protected function getSysStaffJobs()
    {
        $res = (new \Business\Authority\Auth\Jobs())->getSysStaffJobs();

        return is_array($res) ? $res : [];
    }

    /**
     * 保存角色包权限
     * <AUTHOR>
     * @date   2023/5/18
     *
     * @param  int     $rolePackId    角色包id
     * @param  string  $roleName      角色名
     * @param  array   $menuList      菜单id集合
     * @param  int     $operatorId    操作人
     * @param  int     $platformId    平台id
     * @param  int     $orgId         机构id
     *
     * @return bool
     */
    protected function saveRolePackageAndMenu(int $rolePackId, string $roleName, array $menuList, int $frontAppId, int $operatorId = 1, int $platformId = Config::PLATFORM_ID, int $orgId = Config::ORG_ID)
    {
        if (!$rolePackId || empty($roleName)) {
            return false;
        }

        $createRes = (new \Business\JavaApi\Authority\MerchantRolePackageService())->setRolePackageAndMenu($rolePackId,
            $roleName, $menuList, $frontAppId, $orgId, $platformId, $operatorId);
        if ($createRes['code'] != 200) {
            return false;
        }

        return true;
    }

    /**
     * 用户绑定角色包
     * <AUTHOR>
     * @date   2023/4/12
     *
     * @param  array  $userSidList        用户数据
     * @param  array  $rolePackageList    角色包id
     * @param  int    $operatorId         操作人
     *
     * @return bool
     */
    protected function userBindPackage(array $userSidList, array $rolePackageList, int $operatorId)
    {
        if (empty($rolePackageList) || empty($userSidList) || !$operatorId) {
            return false;
        }

        $res = (new \Business\JavaApi\Authority\MerchantUserPackageService())->userBindPackage($userSidList,
            $rolePackageList, $operatorId);
        if ($res['code'] != 200) {
            return false;
        }

        return true;
    }

    /**
     * 获取标记
     * <AUTHOR>
     * @date   2023/5/18
     *
     * @param  int  $fid      商户id
     * @param  int  $oldId    旧标识
     * @param  int  $newId    新标识
     *
     * @return array|false|string
     */
    protected function getSynMark($fid, $oldId = 0, $newId = 0)
    {
        return (new DataSynTmpModel())->getMarkInfo($fid, $oldId, $newId);
    }

    /**
     * 创建标记
     * <AUTHOR>
     * @date   2023/5/18
     *
     * @param  int  $fid      商户id
     * @param  int  $oldId    旧标识
     * @param  int  $newId    新标识
     *
     * @return false|int|mixed|string
     */
    protected function createSynMark($fid, $oldId = 0, $newId = 0)
    {
        return (new DataSynTmpModel())->createSyn($fid, $oldId, $newId);
    }

    /**
     * 旧版角色id
     * <AUTHOR>
     * @date   2023/5/22
     *
     * @param  int  $roleId    旧版角色id
     *
     * @return array
     */
    protected function getDataResourceScope($roleId)
    {
        // \Model\Authority\DataResource::getScopeByRoleIdArr
        return (new DataResourceModel())->getScopeByRoleId($roleId, "*");
    }

    /**
     * 批量写入岗位数据权限数据
     * <AUTHOR>
     * @date   2023/5/22
     *
     * @param $data
     *
     * @return false|int|mixed|string
     */
    protected function insertDataResourceScope($data)
    {
        if (empty($data)) {
            return 0;
        }

        return (new DataResourceModel())->table("pft_authority_data_resource_record_business")->addAll($data);
    }
}