<?php
/**
 * 订单相关的一些定时任务
 * <AUTHOR>
 * @date 2018-07-15
 */

namespace CrontabTasks\Order;

use Business\ReservationSpecialTeam\TeamOrder;
use Library\OrderCodePool;
use Library\UnionCode\UnionCodeUtil;
use Model\Order\OrderTools;
use Model\Order\SpecilTeamOrder;
use Model\Product\Land;
use Library\Constants\Table\MainTableConst;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class Order
{

    public $codeUpdateLandId = 0;

    /**
     * 每天凌晨生成景区凭证码池
     *
     * 备注：暂时是获取前一天有下单的景区ID,然后判断如果当前池子的使用量超过了80%就重新生成
     *
     * <AUTHOR>
     * @date   2018-07-16
     *
     * @param $lid int|bool 景区ID
     *
     * @return mix
     */
    public function supplementCodePool()
    {
        global $argv;
        $lid = isset($argv[3]) ? $argv[3] : 0;
        $lid = $lid ? intval($lid) : 0;
        if ($this->codeUpdateLandId > 0) {
            $lid = $this->codeUpdateLandId;
        }
        //开始日志
        pft_log('order/supplepool', "start:{$lid}");

        if ($lid) {
            //具体只更新某个景区的凭证码池子
            $landModel = new Land();
            $landInfo  = $landModel->getLandInfo($lid, $extra = false, $field = 'terminal_type, resourceID, p_type');

            if ($landInfo) {
                //$res = OrderCodePool::supplement($lid, $landInfo['p_type'], $landInfo['resourceID'],
                //    $landInfo['terminal_type'], $forceDate = true);
                $res = UnionCodeUtil::supplement($lid, $landInfo['p_type'], $landInfo['terminal_type'], true,
                    $landInfo['resourceID']);
                pft_log('order/supplepool', json_encode([$lid, $landInfo, $res], JSON_UNESCAPED_UNICODE));
            }
        } else {
            //按下单量去部分更新
            $yesterday = date('Y-m-d', strtotime('-1 day'));
            $startTime = $yesterday . ' 00:00:00';
            $endTime   = $yesterday . ' 23:59:59';

            $orderTool = new OrderTools();
            $landModel = new Land();

            $total = $orderTool->getActiveLand($startTime, $endTime, $isGetAll = true);
            $size  = 4000;
            $pages = ceil($total / $size);

            for ($page = 1; $page <= $pages; $page++) {
                $lidArr = $orderTool->getActiveLand($startTime, $endTime, $isGetAll = false, $page, $size);

                foreach ($lidArr as $lid) {
                    //获取景区数据
                    $landInfo = $landModel->getLandInfo($lid, $extra = false,
                        $field = 'terminal_type, resourceID, p_type');

                    //$res = OrderCodePool::supplement($lid, $landInfo['p_type'], $landInfo['resourceID'], $landInfo['terminal_type']);
                    $res = UnionCodeUtil::supplement($lid, $landInfo['p_type'], $landInfo['terminal_type'], false,
                        $landInfo['resourceID']);

                    pft_log('order/supplepool', json_encode([$lid, $landInfo, $res], JSON_UNESCAPED_UNICODE));
                }
            }
        }

    }

    /**
     * 根据当天下单情况，补充凭证码池
     *
     * <AUTHOR>
     */
    public function supplementCodePoolPerHour()
    {
        //根据当天下单量判断是否执行
        $yesterday = date('Y-m-d');
        $startTime = $yesterday . ' 00:00:00';
        $endTime   = $yesterday . ' 23:59:59';

        $orderTool = new OrderTools('slave');
        $landModel = new Land('slave');

        $total = $orderTool->getActiveLand($startTime, $endTime, $isGetAll = true);
        $size  = 4000;
        $pages = ceil($total / $size);

        for ($page = 1; $page <= $pages; $page++) {
            $lidArr = $orderTool->getActiveLand($startTime, $endTime, $isGetAll = false, $page, $size);

            foreach ($lidArr as $lid) {
                //获取景区数据
                $landInfo = $landModel->getLandInfo($lid, $extra = false,
                    $field = 'terminal_type, resourceID, p_type');

                // $res = OrderCodePool::supplement($lid, $landInfo['p_type'], $landInfo['resourceID'],$landInfo['terminal_type'], false, 0.2);
                $res = UnionCodeUtil::supplement($lid, $landInfo['p_type'], $landInfo['terminal_type'], false,
                    $landInfo['resourceID'], 0.2);

                pft_log('order/supplepool', json_encode([$lid, $landInfo, $res], JSON_UNESCAPED_UNICODE));
            }
        }
    }
}
