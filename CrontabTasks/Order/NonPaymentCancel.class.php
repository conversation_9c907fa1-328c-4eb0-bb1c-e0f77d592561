<?php

/**
 * 未支付订单取消脚本
 * <AUTHOR>
 */
 
namespace CrontabTasks\Order;
use Business\AgreementTicket\Storage;
use Library\Controller;
use Library\MulityProcessHelper;
use Model\Order\OrderTools;
use Model\Product\Presuming;
use Model\Product\Ticket;
use Library\Tools\Helpers;
use Library\MessageNotify\FzZwxSms;
use Business\Order\Modify;
use Library\Constants\OrderConst;
use Business\Product\Show as ShowBiz;


if(!defined('PFT_CLI')) {
    exit('Access Deny');
}


class NonPaymentCancel extends Controller {

    //处理多少小时内的订单
    const HOURS_AGO     = 6;
    //处理的进程数量
    const PROCESS_NUM   = 8;
    //每个进程每次获取订单数
    const PER_ORDER_GET = 1000;
    //每个进程每次取消订单数
    const PER_ORDER_DEAL = 50;

    public function run() {

        $processHelper = new MulityProcessHelper(new \stdClass, self::PROCESS_NUM);
        $processHelper->runWithCallback([__NAMESPACE__ . '\NonPaymentCancel', 'cancelAction']);
        echo 'success';
    }


    /**
     * 取消未支付的订单
     * 每个子进程处理指定时间范围的订单
     * <AUTHOR>
     * @date   2018-05-10
     * @param  int     $processIndex 进程索引,从0开始
     */
    public static function cancelAction($processIndex = 0) {

        //每个进程处理多少小时的数据
        $hours = self::HOURS_AGO / self::PROCESS_NUM;
        //查询开始时间
        $begin = time() - (self::HOURS_AGO - $processIndex * $hours) * 3600;
        //查询结束时间
        $end   = $begin + $hours * 3600;

        $begin = date('Y-m-d H:i:s', $begin);
        $end   = date('Y-m-d H:i:s', $end);

        $toolModel      = new OrderTools('slave');
        $presumingModel = new Presuming();
        // $ticModel       = new Ticket('slave');
        // $soapClient     = Helpers::GetSoapInside();
        $modifyBiz      = new Modify;
        $commodityTicketBiz = new \Business\CommodityCenter\Ticket();

        $extenalCodeBiz = new \Business\ExternalCode\CodeManage();
        $agreementStorageBiz = new Storage();

        $ticketField = 'id,landid,title,tprice,reb,discount,delaydays,use_early_days,status,pay,notes,ddays,getaddr,smslimit,s_limit_up,s_limit_low,
                            buy_limit_up,if_verify,buy_limit_low,open_time,end_time,apply_did,pid,cancel_cost,reb_type,order_start,max_order_days,cancel_auto_onMin,
                            delaytype,delaytime,order_end,order_limit,expire_action,expire_action_days,batch_check,batch_day_check,batch_diff_identities,refund_audit,
                            refund_rule,refund_early_time,cancel_notify_supplier,re_integral,revoke_audit,delay_refund_time,print_ticket_limit,modify_limit_time';
        //获取未支付的订单数据
        //$orders = $toolModel->getNonpaymentOrders($begin, $end, self::PER_ORDER_GET);

        $queryParams = [$begin, $end, 1, self::PER_ORDER_GET];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderPage','getUnpaidOrder', $queryParams);
        $orderDetailQuery = new \Business\JavaApi\Order\Query\OrderDetailQuery();

        if ($queryRes['code'] != 200) {
            pft_log('order/cancel', json_encode($queryRes));
        }

        $orders = $queryRes['data']['list'];

        $cancelNum = 0;
        foreach ($orders as $item) {
            //本次处理订单数达到上限
            if ($cancelNum > self::PER_ORDER_DEAL) {
                continue;
            }
            //订单号是否在黑名单内
            $blackArr = self::_blackOrderLog('read');
            if (in_array($item['ordernum'], $blackArr) || $item['lid'] == 5322 || $item['lid'] == 4548) {
                continue;
            }
            //如果是预消费类订单，不进行处理
            if ($presumingModel->isOrderNeedDelay($item['ordernum'])) {
                continue;
            }
            //联票的取消规则以主票为准
            $main = false;
            if ($item['concat_id'] && $item['concat_id'] != $item['ordernum']) {
                $main = $toolModel->getOrderInfo($item['concat_id']);
            }
            //获取门票配置
            $tid = $main ? $main['tid'] : $item['tid'];
            $ticketData = $commodityTicketBiz->queryTicketInfoById($tid,$ticketField,'','p_type');
            $ticketDto  = isset($ticketData['ticket']) ? $ticketData['ticket'] : [];
            $landDto  = isset($ticketData['land']) ? $ticketData['land'] : [];
            $ticketInfo = array_merge($ticketDto,$landDto);
            //$ticketInfo = $ticModel->getTicketInfoById($tid);
            if (!$ticketInfo) {
                continue;
            }
            //所有走购物车的订单都跳过超时自动取消
            $orderDetailArrRes = $orderDetailQuery->getOrderWithDetail([$item['ordernum']]);
            if ($orderDetailArrRes['code'] == 200) {
                $orderInfo = $orderDetailArrRes['data'][0];
                $orderFxDetail = $orderInfo['fxDetails'];
                if (is_array($orderFxDetail) && $orderFxDetail['extContent']) {
                    $orderFxDetailExtContent = @json_decode($orderFxDetail['extContent'], true);
                    if ($orderFxDetailExtContent['saleChannel'] == 102) {
                        continue;
                    }
                }
            }

            //云票务未支付的订单，发短信提醒(预约出票) 影响到我年卡虚拟卡清除业务了注释了
//            if ($item['ordermode'] == 10 && Helpers::isMobile($item['ordertel'])) {
//                self::_sendAppointmentSms($item, $ticketInfo);
//                continue;
//            }

            //用户配置的超时取消订单时间
            $minutes = $ticketInfo['cancel_auto_onMin'];
            if ((int)$minutes <= 0) {
                continue;
            }
            // $minutes = 10;
            //延迟3分钟，以防第三方接口服务器存在时间差
            $seconds   = ($minutes + 3) * 60;
            //下单时间
            $orderTime = strtotime($item['ordertime']);
            // 报团模式未支付的订单超过一周未支付则取消---叶星提的,为报团计调下单（ordermode==44）时，超过一周后未支付自动取消
            if ($item['ordermode'] == 24 || $item['ordermode'] == 44) {
                $seconds = 604800;
            }
            if (time() - $orderTime > $seconds) {
                //套票只允许主票进行取消操作
                $pack = $toolModel->getOrderAddonInfo($item['ordernum']);
                if ($pack && $pack['ifpack'] == 2) {
                    continue;
                }
                //取消订单
                try {
                    $cancelNum++;

                    $res = $modifyBiz->cancelParamsCommonFormat($item['ordernum'], 1, OrderConst::EXPIRED_SYSTEM_CANCEL);
                    if (!in_array($res['code'], [200])) {
                        @self::_blackOrderLog('write', $item['ordernum']);
                    }

                    //释放演出类座位
                    $showBiz = new ShowBiz();
                    $showRes = $showBiz->releaseSeatByOrdernum($item['ordernum'], $item['tnum']);

                    //释放第三方消费码
                    $extenalCodeRes = $extenalCodeBiz->releaseCodeByOrderId($item['ordernum']);

                    //协议票异步任务
                    //$agreementStorageBiz->cancelStorageHandle($item['ordernum'], (int)$item['tnum']);

                    //记录取消日志
                    @pft_log('order/cancel', json_encode([$item['ordernum'], $item['tnum'], $res, $showRes, $extenalCodeRes]));

                    @self::_writeLog($item['ordernum'] . ':' . json_encode($res), 'order_cancel_success.txt');
                } catch (\Exception $e) {
                    @self::_blackOrderLog('write', $item['ordernum']);

                    //记录错误
                    $msg = $e->getMessage();
                    @self::_writeLog($item['ordernum'] . '|' . $msg, 'order_cancel_fail.txt');
                    continue;
                }
            }
        }
    }


    /**
     * 发送云票务预约短息
     * <AUTHOR>
     * @date   2018-05-10
     * @param  array     $order  订单信息
     * @param  array     $ticket 门票信息
     * @return mix
     */
    private static function _sendAppointmentSms($order, $ticket) {

        static $smsObj;
        if (!is_object($smsObj)) {
            $smsObj = new FzZwxSms();
        }

        if (stripos($order['series'], '演出') !== false) {
            $series = unserialize($order['series']);
            $name = "演出时间:{$series['4']} {$ticket['title']}";
        } else {
            $name = $ticket['title'];
        }
        if ($ticket['p_type'] == 'I'){
            $smsTemplate = 'appointment_cancel_annual';
            $temp = [
                '{1}' => $order['playtime'],
                '{2}' => "{$name}{$order['tnum']}张",
            ];
        }else{
            $smsTemplate = 'appointment_cancel';
            $temp = [
                '{1}' => $order['playtime'],
                '{2}' => "{$name}{$order['tnum']}张",
                '{3}' => $order['totalmoney']/100
            ];
        }

        @self::_blackOrderLog('write', $order['ordernum']);
        return $smsObj->doSendSMS($order['ordertel'], $temp, $smsTemplate);
    }


    private static function _writeLog($content, $file) {
        $dir = BASE_LOG_DIR . '/order/';
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
        $file = $dir . $file;
        $fp   = fopen($file, "a");
        flock($fp, LOCK_EX);
        fwrite($fp, date("Y-m-d H:i:s") . ":" . $content . "\n");
        flock($fp, LOCK_UN);
        fclose($fp);
    }

    //取消出错，需要跳过的黑名单
    private static function _blackOrderLog($type = 'write', $orderid = '') {
        $dir = BASE_LOG_DIR . '/order/';
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
        $file = $dir . 'black_order.txt';
        $fp   = fopen($file, "a");
        if ($type == 'write') {
            flock($fp, LOCK_EX);
            fwrite($fp, $orderid . ",");
            flock($fp, LOCK_UN);
            fclose($fp);
        } else {
            $content = file_get_contents($file);
            return explode(',', $content);
        }
    }

}
