<?php

namespace CrontabTasks\Order;

use Business\JavaApi\Log\EsData;
use Business\JavaApi\Log\TrackQuery;
use Library\Controller;
use Library\Model;

/**
 * 修补es 订单追踪数据
 */

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

class RepairEsOrder extends Controller 
{

    public function run()
    {
        $model = new Model('slave');

        $where = [
            'insertTime' => ['between', ['2020-11-04 00:00:00', '2020-11-04 23:59:59']],
            'action' => 5
        ];

        $page = 1;
        $size = 1000;

        $esApi    = new EsData();
        $trackApi = new TrackQuery();
        while (1) {
            $list = $model->table('pft_order_track')->where($where)->order('insertTime asc')->page($page, $size)->select();
            if (!$list) {
                break;
            }

            $page++;

            //判断是否存在
            $ids    = array_column($list, 'id');
            foreach ($ids as &$item) {
                $item = (int)$item;
            }
            $res = $trackApi->queryOrderTrackLogByIds($ids);
            if ($res['code'] != 200) {
                var_dump($res);die;
            }

            $exists = $res['data'];
            foreach ($list as $key => $item) {
                if (in_array($item['id'], $exists)) {
                    unset($list[$key]);
                }
            }

            if (!$list) continue;

            $list = array_values($list);
            $res = $esApi->addTracks($list);
            var_dump($res);
        }

        var_dump('转完了...');
    }
}