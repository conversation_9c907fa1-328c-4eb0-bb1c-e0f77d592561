<?php

namespace CrontabTasks\Order;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

use Library\Controller;

class CodeOccupy extends Controller
{

    /**
     * 删除过期的凭证码占用记录
     * <AUTHOR>
     * @date   2019-10-24
     */
    public function delOccupyCode() {
        $begin = date('Y-m-d H:i:s');
        $biz = new \Business\Order\CodeOccupy();
        $res = $biz->expireDelete();
        $end = date('Y-m-d H:i:s');

        $log = [
            'begin' => $begin,
            'end'   => $end,
            'res'   => $res
        ];
        @pft_log('code/del_occupy_code', json_encode($log));
    }
}
