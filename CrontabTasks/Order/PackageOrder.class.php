<?php

namespace CrontabTasks\Order;
use Library\Controller;

/**
 * 手动生成套票子票
 *
 * @date    2019-11-15
 * <AUTHOR>
 */

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

class PackageOrder extends Controller {

    /**
     * 生成套票子票
     * <AUTHOR>
     * @date   2019-11-15
     */
    public function run()
    {
        $params = $GLOBALS['argv'];
        if (!isset($params[3])) {
            die('请输入主订单号');
        }

        //主订单号信息
        $ordernum   = strval($params[3]);
        $orderModel = new \Model\Order\OrderTools('slave');
        $mainInfo   = $orderModel->getOrderInfo($ordernum);
        if (!$mainInfo) {
            die('未查到主订单号信息');
        }

        //联系人
        $orderName = $mainInfo['ordername'];
        //联系手机号
        $orderTel  = $mainInfo['ordertel'];
        //联系人手机号区号 86=中国
        $mobileArea = $mainInfo['mobile_area'];
        //联系人手机号区号归属地 CN=中国
        $mobileRegion = $mainInfo['mobile_region'];
        //主票pid
        $parentPid = $mainInfo['pid'];
        //游玩日期
        $playTime  = $mainInfo['playtime'];
        //主订单票数
        $parentTnum = $mainInfo['tnum'];
        // 支付方式
        $payMode = $mainInfo['paymode'];

        //获取产品类型
        $ticketApi = new \Business\Product\Ticket();
        $ticketRes = $ticketApi->getListForOrderNew($mainInfo['tid']);
        if (!in_array($ticketRes['land_info']['p_type'], ['F', 'H'])) {
            die('非套票订单');
        }
        $ptype = $ticketRes['land_info']['p_type'];

        $redisObj          = \Library\Cache\Cache::getInstance('redis');
        $showInfoKey       = 'childShowInfoJson:' . $mainInfo['ordernum'];
        $childShowInfoJson = $redisObj->get($showInfoKey);
        $childShowInfoArr  = [];

        if (in_array($ptype, ['F', 'H']) && !empty($childShowInfoJson)) {
            $childShowInfoArr = json_decode($childShowInfoJson, true);
            // 修改时效时间
            $redisObj->set($showInfoKey, $childShowInfoJson, '', 720);
        }

        $childSpecialSettingArr = [
            'parent_contact_info_more' => [
                'mobileArea' => $mobileArea,
                'mobileRegion' => $mobileRegion,
            ]
        ];

        try {
            $packageBiz = new \Business\Order\PackageSubmit();
            $res = $packageBiz->submitChildrenOrder($orderName, $orderTel, $parentPid, $ordernum, $playTime, $parentTnum,
                $ptype, $payMode, '', '', [], $childShowInfoArr, 0, $childSpecialSettingArr);
        } catch (\Exception $e) {
            var_dump($e->getMessage());
        }
    }

}
