<?php

namespace CrontabTasks\Order;

use Business\JavaApi\Log\EsData;
use Business\JavaApi\Log\TrackQuery;
use Business\Order\PackageSubmit;
use Library\Controller;
use Library\Model;



if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

class PackageOrderNotice extends Controller
{
    //轮询 5分钟一次
    private $_packageOrderNoticeReview = 'order/package';
    public function run()
    {
        $packageSubmit = new PackageSubmit();
        $reviewType = $packageSubmit::REVIEW_TYPE;
        $res = $packageSubmit ->reviewPackOrderNotice($packageSubmit::REVIEW_TYPE_REVIEW);
        pft_log($this->_packageOrderNoticeReview,
            json_encode(["{$reviewType[$packageSubmit::REVIEW_TYPE_REVIEW]}轮询结果",$res],JSON_UNESCAPED_UNICODE));

        $res = $packageSubmit ->reviewPackOrderNotice($packageSubmit::REVIEW_TYPE_FAILED);
        pft_log($this->_packageOrderNoticeReview,
            json_encode(["{$reviewType[$packageSubmit::REVIEW_TYPE_FAILED]}轮询结果",$res],JSON_UNESCAPED_UNICODE));

        $res = $packageSubmit ->reviewPackOrderNotice($packageSubmit::REVIEW_TYPE_COMPLETED);
        pft_log($this->_packageOrderNoticeReview,
            json_encode(["{$reviewType[$packageSubmit::REVIEW_TYPE_COMPLETED]}轮询结果",$res],JSON_UNESCAPED_UNICODE));
    }
}