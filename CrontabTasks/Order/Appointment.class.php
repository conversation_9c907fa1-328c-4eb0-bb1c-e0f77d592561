<?php

namespace CrontabTasks\Order;

use Business\Order\OrderQueryMove;
use Library\Controller;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class Appointment extends Controller
{

    /**
     * 同步订单信息
     *
     * @return array|bool
     * <AUTHOR>
     * @date 2021/9/2
     *
     *
     */
    public function syncOrderStatus()
    {
        $appointModel = new \Model\Order\Appointment();
        $orderArr     = $appointModel->getOrderListByCreateTime();
        if (!$orderArr) {
            return true;
        }
        $orderArr   = array_column($orderArr, 'order_num');
        //订单查询迁移
        $orderMove   = new OrderQueryMove();
        $newOrderMap = array_chunk($orderArr, 3000);
        foreach ($newOrderMap as $value) {
            $orderMap = $orderMove->getListByOrderNumNew($value);
            if (!$orderMap) {
                continue;
            }
            $newData = [];
            foreach ($orderMap as $item) {
                $newData[$item['status']][] = $item['ordernum'];
            }
            foreach ($newData as $staus => $ordernums) {
                $appointModel->updateOrderStatusByOrderNum($ordernums, $staus);
            }
        }

        return true;
    }
}