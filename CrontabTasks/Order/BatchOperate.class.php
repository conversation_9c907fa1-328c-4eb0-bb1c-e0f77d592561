<?php
/**
 * 订单批量操作
 * <AUTHOR>
 * @date   2022/5/7
 */

namespace CrontabTasks\Order;

use Library\Controller;
use Library\Cache\Cache;
use Model\Order\BatchOperate as BatchOperateModel;
use Model\Order\BatchOrder as BatchOrderModel;
use Business\Order\BatchOperate as BatchOperateBiz;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}
//
// /usr/bin/php /var/www/html/Service/Crontab/runNew.php Order/BatchOperate run
class BatchOperate extends Controller
{
    const PROCESS_MAX                  = 10; //进程上限
    const EVERY_PROCESS_MAX            = 5; //单一进程处理数
    const BATCH_ORDER_MEMBER_CACHE_KEY = 'batch_operate:member:%u:%u'; //用户执行标识缓存 用户id + 操作
    const REDIS_LOCK_KEY               = 'batch_operate_task_get_lock_redis';//锁键名

    const LOG_PATH = 'batch_operate/'; //日志地址

    //批量续费标识
    const BATCH_DISTRIBUTION_RENEW_KEY = 5;
    //批量下单标识
    const BATCH_ORDER_KEY = 99;

    //批量操作处理方法
    const HANDLE_FUNCTION = [
        1  => ['class' => '\Business\Order\BatchOperate', 'action' => 'batchValidDate'],
        2  => ['class' => '\Business\Order\BatchOperate', 'action' => 'batchCancel'],
        3  => ['class' => '\Business\Order\BatchOperate', 'action' => 'batchCheck'],
        4  => ['class' => '\Business\Order\BatchOperate', 'action' => 'batchFinish'],
        self::BATCH_DISTRIBUTION_RENEW_KEY  => ['class' => '\Business\DistributionCenter\DistributorOperation', 'action' => 'batchDistributorRenew'],
        self::BATCH_ORDER_KEY => ['class' => '\Business\Order\BatchOrder', 'action' => 'batchOrderRun'],
    ];

    public function run()
    {
        try {
            //锁实例
            $redis      = Cache::getInstance('redis');
            //$lockResult = $redis->lock(self::REDIS_LOCK_KEY, 1, 60);
            // if (!$lockResult) {
            //     $this->_log('获取失败有锁');
            //
            //     return false;
            // }

            $recordList = $this->getRecord();
            $list       = $recordList['list'] ?? [];
            $runList    = $recordList['run_list'] ?? [];
            if (empty($list)) {
                $redis->rm(self::REDIS_LOCK_KEY);//去除锁

                return ['code' => 200, 'msg' => '没有可执行的'];
            }

            //验证剩余进程数
            $surplus = $this->_getSurplus($runList);
            if (!$surplus) {
                $redis->rm(self::REDIS_LOCK_KEY);//去除锁
                throw new \Exception('暂无可用进程', 400);
            }

            //策略：数据过滤，通过则写入缓存， 后续操作失败需要移除用户缓存
            $listRes = $this->_strategyHandle($list, $surplus);
            $ids     = $listRes['ids'] ?? [];
            $list    = $listRes['list'] ?? [];
            if (empty($ids) || empty($list)) {
                $redis->rm(self::REDIS_LOCK_KEY);//去除锁
                return ['code' => 200, 'msg' => '没有可执行的'];
            }

            $this->_log("剩余进程：{$surplus}，总数：" . count($list) . "，将执行：" . count($ids));

            //更新记录状态
            $res = $this->_updateMainSate($ids);
            if ($res['code'] != 200) {
                $this->_rmMemberListCache($list);//需要移除用户缓存
                $redis->rm(self::REDIS_LOCK_KEY);//去除锁
                $this->_log(["操作记录状态更新失败", func_get_args(), $res], 2);
                throw new \Exception('操作记录状态更新失败!', 400);
            }

            //去除锁, 更新完状态才能移除锁
            $redis->rm(self::REDIS_LOCK_KEY);

            //多线程处理
            $this->_mulityProcessHandle($list);

        } catch (\Exception $e) {
            //错误记录
            $error = [
                'code' => $e->getCode(),
                'msg'  => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ];
            $this->_log($error, 2);
        }
    }

    /**
     * 多线程处理
     * <AUTHOR>
     * @date   2022/5/11
     *
     * @param  array  $list 处理列表
     */
    private function _mulityProcessHandle(array $list)
    {
        $procNum = count($list);
        defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
        $task = new \Library\MulityProcessHelper($this, $procNum, 1);
        $task->run($list); // 如果前面连接了数据库、redis等，最好在这之前关闭掉
    }

    /**
     * 子进程执行
     * <AUTHOR>
     * @date   2022/5/6
     *
     * @param  array  $list    任务参数
     *
     * @return bool
     */
    public function runWorker(array $list)
    {
        try {
            if (empty($list)) {
                return true;
            }

            foreach ($list[0] ?? [] as $item) {
                $type = $item['action_type'] ?? 0;
                if (!$type) {
                    continue;
                }
                $config = self::HANDLE_FUNCTION[$type] ?? [];

                if (!empty($config)) {
                    $class  = $config['class'];
                    $action = $config['action'];

                    if (!class_exists($class) || !method_exists($class, $action)) {
                        //移除缓存
                        $this->_rmMemberCache($item['member_id'], $item['action_type']);
                        throw new \Exception('处理方法不存在', 400);
                    }

                    $handle = new $class;
                    $handle->$action($item);

                    //这里延迟下，避免获取不到成功的状态
                    sleep(1);

                    if ($type != self::BATCH_ORDER_KEY) {
                        $args = [
                            'action' => 'batch_operate_update_state',
                            'data'   => [
                                'id'        => $item['id'],
                                'state'     => BatchOperateModel::MAIN_STATE_DONE,
                                'old_state' => BatchOperateModel::MAIN_STATE_RUN,
                            ],
                        ];

                        $jobId = \Library\Resque\Queue::push('independent_system', 'BatchOperate_Job', $args);
                        $this->_log(["队列更新", $args, $jobId]);
                    }

                    $this->_log(["清除用户日志开始：", $item]);

                    //移除缓存
                    $this->_rmMemberCache($item['member_id'], $item['action_type']);
                }
            }
        } catch (\Exception $e) {
            //错误记录
            $error = [
                'code' => $e->getCode(),
                'msg'  => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ];
            $this->_log($error, 2);
        }

        return true;
    }

    /**
     * 获取剩余进程数
     * <AUTHOR>
     * @date   2022/5/6
     *
     * @param  array  $runList    正在运行的数据
     *
     * @return int
     */
    private function _getSurplus(array $runList)
    {
        //取模算出已用进程数
        $chunk = [];
        foreach ($runList as $item) {
            $memberId = $item['member_id'] ?? 0;
            if (!$memberId) {
                continue;
            }

            if (!isset($chunk[$memberId % self::PROCESS_MAX])) {
                $chunk[$memberId % self::PROCESS_MAX] = [];
            }

            $chunk[$memberId % self::PROCESS_MAX][] = $item;
        }

        $use     = count($chunk);
        $surplus = self::PROCESS_MAX - $use;

        return $surplus < 0 ? 0 : $surplus;
    }

    /**
     * 策略处理
     * <AUTHOR>
     * @date   2022/5/6
     *
     * @param  array  $list       待执行数据
     * @param  int    $surplus    剩余线程数
     *
     * @return array
     */
    private function _strategyHandle(array $list, int $surplus)
    {
        $chunk = [];
        $ids   = [];
        $redis = Cache::getInstance('redis');
        foreach ($list as $item) {
            $memberId   = $item['member_id'] ?? 0;
            $actionType = $item['action_type'] ?? 0;
            if (!$memberId || !$actionType) {
                continue;
            }

            //判断是否有在执行中
            $cacheKey   = sprintf(self::BATCH_ORDER_MEMBER_CACHE_KEY, $memberId, $actionType);
            $checkCache = $redis->get($cacheKey);
            if ($checkCache !== false) {
                continue;
            }

            if (!isset($chunk[$memberId % $surplus])) {
                $chunk[$memberId % $surplus] = [];
            }

            //每个进程限制执行数量
            if (count($chunk[$memberId % $surplus]) >= self::EVERY_PROCESS_MAX) {
                continue;
            }

            $chunk[$memberId % $surplus][] = $item;

            $ids[$actionType][] = $item['id'];

            $redis->set($cacheKey, '1', '', 60 * 60);
        }

        return [
            'list' => $chunk,
            'ids'  => $ids,
        ];
    }

    /**
     * 移除制指定用户缓存
     * <AUTHOR>
     * @date   2022/5/9
     *
     * @param  int  $memberId 用户id
     * @param  int  $actionType 操作类型
     *
     * @return bool
     */
    private function _rmMemberCache(int $memberId, int $actionType)
    {
        $this->_log(["清除用户日志：", $memberId, $actionType]);
        if (!$memberId || !$actionType) {
            return false;
        }

        //移除缓存
        $redis    = Cache::getInstance('redis');
        $cacheKey = sprintf(self::BATCH_ORDER_MEMBER_CACHE_KEY, $memberId, $actionType);
        $res = $redis->rm($cacheKey);

        $this->_log(["清除用户完成：", $cacheKey, $res]);
        return true;
    }

    /**
     * 批量移除用户缓存
     * <AUTHOR>
     * @date   2022/5/9
     *
     * @param  array  $list 批量操作数据
     *
     * @return bool
     */
    private function _rmMemberListCache(array $list)
    {
        if (empty($list)) {
            return false;
        }

        //需要移除用户缓存
        foreach ($list as $item) {
            $item = is_array($item) ? $item : [];
            foreach ($item as $tmp) {
                $memberId   = $tmp['member_id'] ?? 0;
                $actionType = $tmp['action_type'] ?? 0;
                if (!$memberId || !$actionType) {
                    continue;
                }

                //移除缓存
                $this->_rmMemberCache($memberId, $actionType);
            }
        }

        return true;
    }

    /**
     * 获取正在执行的和未执行的数据
     * <AUTHOR>
     * @date   2022/5/6
     *
     * @return array
     */
    private function getRecord()
    {
        $model      = new BatchOperateModel();
        $orderModel = new BatchOrderModel();

        $end        = time();
        $begin      = $end - 60 * 60 * 48; //取48h内的数据
        $field      = ""; //查询字段
        $id         = 0;
        $serialCode = ''; //记录编号
        $memberId   = 0; //用户id
        $operator   = 0; //操作人
        $state      = [$model::MAIN_STATE_WAIT, $model::MAIN_STATE_RUN]; //状态
        $actionType = 0; //操作类型
        $whereRaw   = [
            'create_time' => ['between', [$begin, $end]],
        ]; //其他条件
        $order = 'id asc';
        //批量操作
        $operateList = $model->getMainList($field, $id, $serialCode, $memberId, $operator, $state, $actionType, $whereRaw, $order);
        //批量操作下单
        $orderList = $orderModel->getMainList($field, $id, $serialCode, $memberId, $operator, $state, $whereRaw, $order);
        foreach ($orderList as &$tmp) {
            //增加操作类型
            $tmp['action_type'] = self::BATCH_ORDER_KEY;
        }
        //组合要处理的数据
        $recordList = array_merge($operateList, $orderList);
        if (empty($recordList)) {
            return [];
        }

        $runList  = []; //正在执行
        $waitList = []; //待执行
        foreach ($recordList as $item) {
            switch ($item['state']) {
                case $model::MAIN_STATE_WAIT:
                    $waitList[] = $item;
                    break;
                case $model::MAIN_STATE_RUN:
                    $runList[] = $item;
                    break;
            }
        }

        return [
            'list'     => $waitList,
            'run_list' => $runList,
        ];
    }

    /**
     * 更新批量操作状态
     * <AUTHOR>
     * @date   2022/5/19
     *
     * @param  array  $ids id数据
     *
     * @return array
     */
    private function _updateMainSate(array $ids)
    {
        if (empty($ids)) {
            return ['code' => 400, 'msg' => '参数异常'];
        }

        $actionType = BatchOperateBiz::ACTION_TYPE;
        $actionType[] = self::BATCH_ORDER_KEY;
        $actionType[] = self::BATCH_DISTRIBUTION_RENEW_KEY;
        $orderIds = [];
        $operIds  = [];
        foreach ($ids as $type => $id) {
            if (!in_array($type, $actionType)) {
                continue;
            }
            if ($type == self::BATCH_ORDER_KEY) {
                $orderIds = array_merge($orderIds, $id);
            } else {
                $operIds = array_merge($operIds, $id);
            }
        }

        $state    = BatchOperateModel::MAIN_STATE_RUN;
        $oldState = BatchOperateModel::MAIN_STATE_WAIT;

        //更新记录状态
        $res = [];
        if (!empty($operIds)) {
            $res = (new BatchOperateBiz())->updateMainSate($operIds, $state, $oldState);
        }

        if (!empty($orderIds)) {
            //更新下状态：在处理中
            $updateMainStatus = (new \Model\Order\BatchOrder())->mainHandle($orderIds);
            $res = ['code' => 200, 'msg' => '更新成功'];
            if (!$updateMainStatus) {
                pft_log('order_batch/error', "更新处理中状态失败：" . json_encode($orderIds));
                $res = ['code' => 400, 'msg' => '更新失败'];
            }
        }

        if (empty($res)) {
            $res = ['code' => 400, 'msg' => '无可更新的'];
        }

        return $res;
    }

    /**
     * 日志记录
     * <AUTHOR>
     * @date   2022/5/6
     *
     * @param  string|array  $msg     日志信息
     * @param  int           $mode    日志类型 1成功 2失败  3调试
     *
     * @return bool
     */
    private function _log($msg, $mode = 3)
    {
        $path = self::LOG_PATH;
        switch ($mode) {
            case 1:
                $path .= 'success';
                break;
            case 2:
                $path .= 'fail';
                break;
            default:
                $path .= 'debug';
        }

        if (is_array($msg)) {
            $msg = json_encode($msg, JSON_UNESCAPED_UNICODE);
        }

        pft_log($path, $msg);

        return true;
    }
}