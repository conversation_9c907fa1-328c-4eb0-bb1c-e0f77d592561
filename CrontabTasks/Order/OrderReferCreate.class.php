<?php
namespace CrontabTasks\Order;

use Library\Controller;
use Model\Order\OrderHandler;

class OrderReferCreate extends Controller {
    private $_successSyncPath = 'order_refer_sync/success';
    private $_errorSyncPath   = 'order_refer_sync/error';
    private $_noneSyncPath    = 'order_refer_sync/none';

    public function __construct() {
        set_time_limit(0);
    }

    /**
     * 重新同步数据 为了防止mysql触发器 未更新到
     */
    public function runUpdate() {
        //获取过去1分钟未同步的数据
        $handleModel = new OrderHandler();
        $res         = $handleModel->getUnSyncData();
        if (empty($res)) {
            pft_log($this->_noneSyncPath, '未找到');
        }

        if (!empty($res)) {
            foreach ($res as $item) {
                $referData[$item['ordernum']] = $item;
            }
            $orderNum  = array_column($res, 'ordernum');
            $orderData = $handleModel->getOrderInfo($orderNum, 'ordernum, pay_status, status, dtime');

            foreach ($orderData as $item) {
                $tmpOrderNum = $item['ordernum'];
                if (!isset($referData[$tmpOrderNum])) {
                    continue;
                }
                $tmpOrderInfo = $referData[$tmpOrderNum];
                if ($tmpOrderInfo['status'] != $item['status'] || $tmpOrderInfo['pay_status'] != $item['pay_status']
                || $tmpOrderInfo['dtime'] != $item['dtime']) {
                    //需要手动更新
                    $updateData = [
                        'status'     => $item['status'],
                        'pay_status' => $item['pay_status'],
                        'dtime'      => $item['dtime']
                    ];

                    //$res = $handleModel->updateReferOrder($tmpOrderNum, $updateData);

                    $queryParams = [$tmpOrderNum, $item['status'], $item['pay_status'], $item['dtime']];
                    $queryRes    = \Business\JavaApi\Order\Query\Container::query('OrderReferModify',
                        'modifyOrderReferStatusAndPayStatusByOrderNum', $queryParams);

                    $res = false;
                    if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                        $res = true;
                    }

                    if ($res) {
                        pft_log($this->_successSyncPath, "{$tmpOrderNum}成功");
                    } else {
                        $errorStr = $handleModel->getDbError();
                        pft_log($this->_errorSyncPath, "{$tmpOrderNum}失败:$errorStr");
                    }
                }
            }
        }
    }
}
