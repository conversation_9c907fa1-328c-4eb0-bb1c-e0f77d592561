<?php

namespace CrontabTasks\Order;

use Business\Product\AnnualCard;
use Library\Controller;

/**
 * 重試年卡異步下單的
 *
 * @date    2018-12-12
 * <AUTHOR>
 */

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class AsyncOrderAnnual extends Controller
{

    public function retryOrder()
    {
        $annual = new AnnualCard();
        $annual->retryOrder();
    }

    public function reOrder()
    {
        $params = $GLOBALS['argv'];
        $sid    = $cardOrderId = 0;
        if (isset($params[3])) {
            $sid = $params[3];
        }
        if (isset($params[4])) {
            $cardOrderId = $params[4];
        }

        $annual = new AnnualCard();
        $result = $annual->reOrder($sid, $cardOrderId);

        if ($result === false) {
            pft_log('debug/leeRobot', json_encode([
                'action'        => 'reOrder',
                'sid'           => $sid,
                'card_order_id' => $cardOrderId,
                'msg' => '人工重试失败',
            ], JSON_UNESCAPED_UNICODE));
            echo '人工重试失败';exit;
        }
        echo '处理完成';exit;
    }
}
