<?php

namespace CrontabTasks\Order;

use Business\JavaApi\Order\Query\Container;
use Business\NewJavaApi\ApprovalFlowCenter\Process;
use Library\Constants\Table\AuditCenter;
use Model\Order\OrderTerminal;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

/**
 * 退票审核同步子商户id
 */
class OrderRefundAuditSubSidSync
{
    public function syncSubSidFromFxDetailExtContent()
    {
        $orderTerminalModel = new OrderTerminal();
        $where              = [
            'sub_merchant_id' => -1
        ];
        // 获取最早一条未同步的id
        $res = $orderTerminalModel->table(AuditCenter::TABLE_TERMINAL_CHANGE)->field('id')->where($where)->order('id asc')->find();

        if (!$res) {
            $this->recodeLogAndPrint('没有需要处理的数据');
            return true;
        }

        $firstTodoId = (int) $res['id'];

        $size = 100;
        $loopStart = 0;
        $loopMax = 1000;

        $startId = $firstTodoId;

        while (true) {
            usleep(100000);//缓0.1s，不要那么快

            $loopStart++;
            if ($loopStart > $loopMax) {
                $this->recodeLogAndPrint('本次脚本执行告一段落，下个进程继续处理...');
                break;
            }

            $where = [
                'id' => ['egt', $startId],
                'sub_merchant_id' => -1,
            ];

            $terminalChangeList = $orderTerminalModel->table(AuditCenter::TABLE_TERMINAL_CHANGE)->field('id,ordernum,p_type,fxid,approval_code')->where($where)->limit($size)->select();
            (ENV != 'PRODUCTION') && $this->recodeLogAndPrint($orderTerminalModel->getLastSql());
            if (!$terminalChangeList) {
                $this->recodeLogAndPrint('截止当下，已全部同步完成');
                break;
            }
            $orderNumArray = array_column($terminalChangeList,'ordernum');
            $operatorMap = array_column($terminalChangeList,'fxid','ordernum');
            $OrderTerminalAidSplit  = new \Model\Order\OrderTerminalAidSplit();
            list($res,$msg,$resData) = $OrderTerminalAidSplit->batchSaveAuditAids($orderNumArray,$operatorMap);
            if(!$res){
                pft_log('data/TerminalChangeAidSplit',json_encode("sync_error_{$msg}:".json_encode($orderNumArray,JSON_UNESCAPED_UNICODE),JSON_UNESCAPED_UNICODE));
            }
            else{
                pft_log('data/TerminalChangeAidSplit',json_encode("sync_success:".json_encode($orderNumArray,JSON_UNESCAPED_UNICODE),JSON_UNESCAPED_UNICODE));
            }
            $terminalChangeIds = array_column($terminalChangeList, 'id');
            sort($terminalChangeIds);

            $tmpLastId = array_pop($terminalChangeIds);
            if ($tmpLastId > $startId) {
                $startId = $tmpLastId + 1;
            }

            $orderNumArr = array_column($terminalChangeList, 'ordernum');

            $orderDetailArrRes = Container::query('orderDetail', 'queryOrderDetailsByOrdernums', [$orderNumArr]);
            if (($orderDetailArrRes['code'] != 200)) {
                $this->recodeLogAndPrint('订单请求异常，跳出本次执行，继续处理下一批。 ordernums=' . json_encode([$orderNumArr]));
                continue;
            }

            $orderDetailMapArr = array_column($orderDetailArrRes['data'], null, 'orderid');


            foreach ($terminalChangeList as $terminalChangeItem) {
                $id = $terminalChangeItem['id'];
                $orderNum = $terminalChangeItem['ordernum'];
                $pType = $terminalChangeItem['p_type'];
                $approvalCode = $terminalChangeItem['approval_code'];
                if ($pType == 'Q') {
                    // 没有子商户id
                    $subMerchantId = 0;
                } else {
                    $orderDetail = $orderDetailMapArr[$orderNum];
                    if (!$orderDetail) {
                        continue;
                    }


                    $orderDetailExtContent = json_decode($orderDetail['ext_content'], true);
                    if ($orderDetailExtContent['subSid']) {
                        // 有子商户id
                        $subMerchantId = $orderDetailExtContent['subSid'];
                    } else {
                        // 没有子商户id
                        $subMerchantId = 0;
                    }
                }

                $updateRes = $orderTerminalModel->table(AuditCenter::TABLE_TERMINAL_CHANGE)->where([
                    'id'              => $id,
                    'ordernum'        => $orderNum,
                    'sub_merchant_id' => -1,
                ])->save([
                    'sub_merchant_id' => $subMerchantId,
                ]);
                (ENV != 'PRODUCTION') && $this->recodeLogAndPrint($orderTerminalModel->getLastSql());

                if(!empty($approvalCode)){
                    (new Process())->updateProcessExtForsubMerchantId($approvalCode,$subMerchantId);
                    $this->recodeLogAndPrint(json_encode(['更新审批中心',$approvalCode,$subMerchantId],JSON_UNESCAPED_UNICODE));
                }
                if ($updateRes === false) {
                    $this->recodeLogAndPrint(sprintf("当前更新失败, id=%s", $id));
                }
            }

        }

        return true;
    }

    private function recodeLogAndPrint($message)
    {
        pft_log('terminalChange', $message);

        if (!strpos($message, "\n")) {
            $message .= "\n";
        }
        echo $message;
    }
}