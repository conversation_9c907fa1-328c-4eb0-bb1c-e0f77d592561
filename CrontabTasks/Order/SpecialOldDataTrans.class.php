<?php

namespace CrontabTasks\Order;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

use Library\Controller;

/**
 * 旧的特产订单数据转化
 */
class SpecialOldDataTrans extends Controller
{
    
    public function consigneeDataTrans()
    {
        //获取特产订单
        $model = new \Library\Model('slave');
        $model2 = new \Library\Model('pft001');
        $model3 = new \Library\Model('localhost');

        $where = [
            'ordertime' => ['between', ['2020-02-01 00:00:00', '2020-04-30 23:59:59']],
            'p_type' => 'J'
        ];

        $list = $model->table('pft_order_refer')->where($where)->getField('ordernum', true);

        if ($list) {
            $orderUserInfo = [];
            $orderChunk = array_chunk($list, 1000);
            foreach ($orderChunk as $chunk) {
                $where = [
                    'orderid' => ['in', $chunk]
                ];

                $orders = $model->table('uu_order_fx_details')->where($where)->getField('orderid,series,product_ext', true);
                foreach ($orders as $orderid => $data) {
                    $series = $data['series'];
                    $productExt = $data['product_ext'];
                    if ($series) {
                        $seriesArr = explode('|', $series);
                        if ($seriesArr[0]) {
                            //收货人信息
                            $where = [
                                'id' => $seriesArr[0]
                            ];

                            $contactInfo = $model2->table('pft_shipping_addr')->where($where)->find();
                            if ($contactInfo) {
                                $provinceCode = 0;
                                $cityCode     = 0;
                                $townCode     = 0;
                                if ($contactInfo['province']) {
                                    $where = [
                                        'name' => ['in', [$contactInfo['province'], $contactInfo['province'].'市',$contactInfo['province'].'省']]
                                    ];
                                    $provinceCode = $model->table('pft_common_area_code')->where($where)->getField('code');
                                }
                                if ($contactInfo['city']) {
                                    $where = [
                                        'name' => ['in', [$contactInfo['city'], $contactInfo['city'].'市',$contactInfo['city'].'区']]
                                    ];
                                    if ($provinceCode) {
                                        $where['parent_code'] = $provinceCode;
                                    }
                                    $cityCode = $model->table('pft_common_area_code')->where($where)->getField('code');
                                }
                                if ($contactInfo['town']) {
                                    $where = [
                                        'name' => ['in', [$contactInfo['town'], $contactInfo['town'].'区',$contactInfo['town'].'镇']]
                                    ];
                                    if ($cityCode) {
                                        $where['parent_code'] = $cityCode;
                                    }
                                    $townCode = $model->table('pft_common_area_code')->where($where)->getField('code');
                                }

                                if ($model->table('order_user_info')->where(['ordernum' => (string)$orderid])->getField('id')) {
                                    //更新
                                    $update = [
                                        'province_code' => (int) $provinceCode,
                                        'city_code'     => (int) $cityCode,
                                        'town_code'     => (int) $townCode,
                                        'address'       => $contactInfo['detail_addr']
                                    ];
                                    $model3->table('order_user_info')->where(['ordernum' => (string)$orderid])->limit(1)->save($update);
                                } else {
                                    //插入
                                    $orderUserInfo[] = [
                                        'ordernum'  => (string)$orderid,
                                        'ordername' => $contactInfo['name'],
                                        'ordertel'  => $contactInfo['mobile'],
                                        'province_code' => (int) $provinceCode,
                                        'city_code'     => (int) $cityCode,
                                        'town_code'     => (int) $townCode,
                                        'address'       => $contactInfo['detail_addr'],
                                        'create_time'   => time()
                                    ];
                                    $model3->table('order_user_info')->add($orderUserInfo);
                                }
                            }
                        }
                    }

                    if ($series) {
                        $temp = json_decode($productExt, true);
                        $temp['deliveryType'] = "0";
                        //快递
                        $extData = ['product_ext' => json_encode($temp)];
                    } else {
                        $temp = json_decode($productExt, true);
                        $temp['deliveryType'] = "1";
                        //自提
                        $extData = ['product_ext' => json_encode($temp)];
                    }
                    $model3->table('uu_order_fx_details')->where(['orderid' => (string)$orderid])->limit(1)->save($extData);
                }
            }
        }
    }

}
