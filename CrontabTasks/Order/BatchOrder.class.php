<?php

namespace CrontabTasks\Order;

use Library\Controller;
use Library\Cache\Cache;
use \Model\Order\BatchOrder as BatchOrderModel;

/**
 * 批量下单
 *
 * @date    2020-04-08
 * <AUTHOR>
 */

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class BatchOrder extends Controller
{
    public function run()
    {
        // //批量下单走这里，包含特产和其他批量下单
        // $biz = new \Business\Order\BatchOrder();
        // $biz->batchOrderRun();
    }
}
