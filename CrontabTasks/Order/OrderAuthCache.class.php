<?php
/**
 * 下单时应用中心服务限制 缓存更新
 *
 * <AUTHOR>
 * @date   2021-11-19
 */

namespace CrontabTasks\Order;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class OrderAuthCache
{

    /**
     *  php /var/www/html/Service/Crontab/runNew.php Order/OrderAuthCache reset 2021-04-04
     * 更新用户当前套餐及分销应用信息缓存
     *
     * <AUTHOR>
     * @date   2021-11-16
     *
     *
     * @return array
     */
    public function reset()
    {
        echo "开始处理...\r\n";

        $params = $GLOBALS['argv'];

        if (isset($params[3])) {
            $date = date('Ymd',strtotime($params[3]));
        } else {
            $date = date('Ymd',strtotime("-1 days"));
        }

        //获取供应商分销商用户
        $statisReportModel = new \Model\Report\Statistics();
        $fidList = $statisReportModel->getReportDistinct($date, $date, 'fid');
        $resellerIdList = $statisReportModel->getReportDistinct($date, $date, 'reseller_id');

        $fidList        = array_column($fidList, 'fid');
        $resellerIdList = array_column($resellerIdList, 'reseller_id');
        $filterId       = [10, 11, 12, 19, 20, 112];
        $resellerIdList = array_diff($resellerIdList, $filterId);

        $memberIdArr = array_unique(array_merge($resellerIdList, $fidList));
        $total       = count($memberIdArr);

        if ($total == 0) {
            echo "处理结束, 用户总数为0 \r\n";
            exit;
        } else {
            echo "获取用户数{$total}, 开始更新缓存... \r\n";
        }

        $moduleBiz  = new \Business\AppCenter\Module();
        $packageBiz = new \Business\AppCenter\Package();

        $perHandleNum = 200;
        $handleTimes  = ceil($total / $perHandleNum);

        for ($i = 0; $i < $handleTimes; $i++) {
            $inputMember = array_slice($memberIdArr, $i * $perHandleNum, $perHandleNum);
            $moduleRes  = $moduleBiz->getUserModuleUsed($inputMember, ['prod_sale'], false);
            $packageRes = $packageBiz->getUserPackageInfo($inputMember, false);
            $tmpNum = $i + 1;
            $resModule  = $this->_log($moduleRes,'module');
            echo "更新分销应用缓存, 第{$tmpNum}批, 成功{$resModule['success']}, 失败{$resModule['fail']} \r\n";

            $resPackage = $this->_log($packageRes,'package');
            echo "更新套餐缓存, 第{$tmpNum}批, 成功{$resPackage['success']}, 失败{$resPackage['fail']} \r\n";
        }

        echo "处理结束";
    }


    /**
     * 记录下日志
     *
     * <AUTHOR>
     * @date   2021-11-16
     *
     *
     */
    private function _log($data, $type)
    {
        $notDataList = [];
        $dataList    = [];
        foreach ($data as $userId => $userInfo) {
            $userInfo ? $dataList[] = $userId : $notDataList[] = $userId;
        }

        if ($notDataList) {
            pft_log('order/appcenter_service_auth/cache_set_fail', json_encode([
                'dec'         => '缓存数据为空',
                'cache_type' => $type,
                'user_id'    => implode(',', $notDataList)

            ], JSON_UNESCAPED_UNICODE));
        }

        if ($dataList) {
            pft_log('order/appcenter_service_auth/cache_set_success', json_encode([
                'dec'        => '缓存数据为空',
                'cache_type' => $type,
                'user_id'    => implode(',', $dataList)

            ], JSON_UNESCAPED_UNICODE));
        }

        return ['success' => count($dataList), 'fail' => count($notDataList)];
    }

}