<?php
/**
 * 自动补单脚本
 * 获取过去一小时所有的超时订单
 *
 * <AUTHOR>
 *
 * @since 2016-08-30
 */

namespace CrontabTasks\Order;

use Model\Ota;
use Model\Order;
use Library\Cache\Cache;

include_once HTML_DIR . '/ota/ALLFunction.php';
$GLOBALS['PFT_infunction'] = new \ALLFunction();

class AutoRepaireOrder
{

    private $_orderSubmit;
    private $_redis;
    private $_otaQueryModel;
    private $_orderToolModel;
    private $_otaGateWay;
    static  $inSideSoap;

    public function __construct()
    {

        $this->_allApiOrderModel    = new Ota\AllApiOrderModel();
        $this->_otaQueryModel       = new Ota\OtaQueryModel();
        $this->_orderSubmit         = new Order\OrderSubmit();
        $this->_redis               = Cache::getInstance('redis');
        $this->_orderToolModel      = new \Model\Order\OrderTools('slave');
        include_once HTML_DIR . '/ota/common/OTAGateway.class.php';
        $this->_otaGateWay = new \OTAGateway();
    }

    /**
     * php /var/www/html/Service/Crontab/runNew.php Order/AutoRepaireOrder index
     * 
     * 
     */
    public function index()
    {
        $isDo = $this->_redis->get('autorepaire');

        //记录开始日志
        pft_log('order_repair', '开始补单 - ' . 'isDo:' . $isDo);

        if ($isDo == 1) {
            //上一轮还没结束的前提下,直接轮空
            exit;
        }
        //开始的标志位
        $this->_redis->set('autorepaire', 1);

        //设置4分钟超时，防止后面没有重置autorepaire缓存
        $expireTime = 240;
        $this->_redis->expire('autorepaire', $expireTime);

        $logTime = '';

        for ($i = 0; $i < 3; $i++) {
            //取超时数据
            $orderArr = $this->_allApiOrderModel->getAllOverTimeOrder();

            //记录这一轮开始时间  下一轮开始时保证至少间隔30s
            if (empty($logTime)) {
                $logTime = time();
            } else {
                $time = time() - $logTime < 30 ? (30 - (time() - $logTime)) : 0;

                if ($time != 0) {
                    sleep($time);
                }
            }

            //没有符合的数据 下一轮
            if (!is_array($orderArr) || empty($orderArr)) {
                continue;
            }

            //循环处理
            foreach ($orderArr as $param) {
                $orderInfo = $this->_orderToolModel->getOrderInfo($param['pftOrder'], 'pay_status, status');

                //不符合条件的超时数据跳过
                if (empty($orderInfo) || ($orderInfo['pay_status'] != 0 && $orderInfo['pay_status'] != 1) || !in_array($orderInfo['status'], [0, 11])) {
                    continue;
                }

                //开始补单
                $this->orderRepair($param);
            }
        }
        //清除标志位 本轮完成
        $this->_redis->set('autorepaire', 2);

        //记录结束补单
        pft_log('order_repair', '结束补单');

        exit('完成');
    }

    /**
     * 请求第三方系统
     * 
     */
    private function callThird($params)
    {
        $version = $this->_getSysVersion($params['landid']);
        if (!empty($version) && $version == 'new') {
            $rMret = \Library\Tools\Helpers::callThirdSystemJsonRpc($params);
        } else {
            // $rMret = \Library\Tools\Helpers::callThirdSystem($params);
            $rMret = $this->_otaGateWay->dispatch($params);
        }
        return $rMret;
    }

    /**
     * 补单操作
     *
     */
    public function orderRepair($param)
    {
        //获取数据
        //$conIdRes = $this->_otaQueryModel->getInfo('pft_con_sys', 'id', '', ['coopB' => $param['coopB']]);

        $sysConfigInfoArr = new \Model\Ota\SysConfig();
        $infoArr          = $sysConfigInfoArr->getCsysInfoByCoopBs($param['coopB'], 'id');
        $conIdRes         = $infoArr[0];

        $conId           = $conIdRes['id'];
        $info            = $this->_otaQueryModel->selectInfoByIdInTicket('landid, apply_did', $param['bCode']);
        $param           = array_merge($param, $info);
        $param['Action'] = 'repaire';

        //进入OTA补单流程
        $this->_setOverTime($conId);
        // $res = $this->_otaGateWay->dispatch($param);
        $res = $this->callThird($param);
        $this->_resetOverTime($conId);

        //把补单次数更新进数据库 补单次数超过三次 发送短信
        $singleNum = $param['single_num'] + 1;
        $this->_allApiOrderModel->updateTable(['single_num' => $singleNum], ['id' => $param['id']]);

        $resArr   = explode('|', $res);
        $errorStr = '';
        if ($resArr[0] != 200) {
            $errorStr = $param['pftOrder'] . '补单失败,原因是:' . $res;
        } else {
            $orderRes = explode('&', $resArr[5]);
            if ($orderRes[0] == $orderRes[1]) {
                $errorStr = $param['pftOrder'] . '补单超时';

                //根据历史记录，这个逻辑都没有走到，如果需要发送告警信息，后续完善
                //这边暂时先屏蔽
                ////补单超时判断补单次数 满三次发送短信
                //if ($singleNum >= 3) {
                //    //<AUTHOR> | @date 2018/10/12 | 会员表查询模块统一
                //    $Member  = new \Business\Member\Member();
                //    $memInfo = $Member->getInfo($param['fid']);
                //    if (!empty($memInfo['mobile'])) {
                //        $content    = "系统公告：尊敬的" . $memInfo['dname'] . "，" . $param['pftOrder'] . "订单补单次数已超３次，不进入自动补单列表；请核实第三方系统是否有生成订单并及时处理";
                //        $inSideSoap = self::getInSideSoap();
                //        $inSideSoap->Send_SMS_V($memInfo['mobile'], $content, -1, 'fzpft');
                //    }
                //}
            }

            if ($orderRes[0] == 'tmp_' . $orderRes[1]) {
                $errorStr = $param['pftOrder'] . '补单超时';
                pft_log('order_repair', $errorStr . "\n");
                return false; 
            }
        }

        if (!empty($errorStr)) {
            pft_log('order_repair', $errorStr . "\n");
            $this->repaireNotify($param, 0);

            return false;
        }
        $this->repaireNotify($param, $orderRes[0]);
        pft_log('order_repair', $param['pftOrder'] . "补单成功\n");
        unset($param);

        return true;
    }

    /**
     * 补单结果通知第三方
     *
     */

    public function repaireNotify($param, $apiOrder)
    {
        //<AUTHOR> | @date 2018/10/12 | 会员表查询模块统一
        $Member = new \Business\Member\Member();
        $res    = $Member->getMemberTokenUrl($param['fid']);
        $orderInfo = $this->_orderToolModel->getOrderInfo($param['pftOrder'], 'remotenum');


        if (empty($res['dcodeURL']) || empty($res['account']) || empty($orderInfo['remotenum'])) {
            pft_log('order_repair', $param['pftOrder'] . "通知OTA失败，不满足通知条件\n");

            return false;
        }
        $url       = $res['dcodeURL'];
        $account   = $res['account'];
        $orderCall = $orderInfo['remotenum'];

        $partnerMemberModel = new \Model\Member\PartnerMember();
        $data        = $partnerMemberModel->getDataexByAccount($account, 'pwd');

        $pwd        = isset($data['pwd']) ? $data['pwd'] : '';
        $verifyCode = md5($account . $pwd);
        $result     = empty($apiOrder) ? 'fail' : 'success';

        $postData = [
            'VerifyCode'  => $verifyCode,
            'Order16U'    => $param['pftOrder'],
            'ActionTime'  => date('yyyy-MM-dd HH:mm:ss', time()),
            'OrderCall'   => $orderCall,
            'Tnumber'     => $param['oStnum'],
            'OrderState'  => 10,
            'OrderStatus' => $result,
        ];
        $postData = json_encode($postData);
        $curlRes  = curl_post($url, $postData);
        if ($curlRes == false) {
            pft_log('order_repair', $param['pftOrder'] . "通知OTA失败，超时\n");

            return false;
        }
        pft_log('order_repair', $param['pftOrder'] . "通知OTA成功\n");
    }

    /**
     * 设置超时时间
     *
     */
    private function _setOverTime($coopid)
    {
        $this->otaTime = $this->_orderSubmit->GetThirdSystemRequestTime(0, $coopid);
        $this->_redis->set("coop_reqtime:$coopid", 30);
    }

    /**
     * 恢复超时时间
     *
     */
    private function _resetOverTime($coopid)
    {
        $this->_redis->set("coop_reqtime:$coopid", $this->otaTime);
    }

    /**
     * getLocalSoap
     * @return object SoapClient 票付通内部接口
     */
    public static function getInSideSoap()
    {
        //根据配置获取接口地址
        $location = OPEN_URL ? OPEN_URL : 'http://open.12301dev.com/';

        if (!self::$inSideSoap) {
            self::$inSideSoap = new \SoapClient(null, [
                    "location" => $location . "openService/pft_insideMX.php",
                    "uri"      => "www.16u.com?ac_16u=16ucom|pw_16u=c33367701511b4f6020ec61ded352059|auth_16u=true",
                ]
            );
        }

        return self::$inSideSoap;
    }

    /**
     * 根据景点id获取绑定的系统的版本号
     * 
     */
    private function _getSysVersion($lid)
    {
        // $otaQueryModel = new \Model\Ota\OtaQueryModel();
        // $coopInfo      = $otaQueryModel->getCsysid($lid);
        $coopInfo      = $this->_otaQueryModel->getCsysid($lid);
        $csysId        = $coopInfo['csysid'];

        // 获取系统的版本号
        $sysConfigModel   = new \Model\Ota\SysConfig();
        $configField      = 'id, version';
        $sysConfigInfoArr = $sysConfigModel->getSysConfigInfo($csysId, $configField);

        return empty($sysConfigInfoArr['version']) ? '' : $sysConfigInfoArr['version'];
    }
}