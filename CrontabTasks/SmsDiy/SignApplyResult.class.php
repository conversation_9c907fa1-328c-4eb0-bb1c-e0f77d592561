<?php
/**
 * 短信自定义抬头
 * 自动获取运营商审核结果脚本
 * 默认 2点30分开始执行
 *
 * <AUTHOR>
 * @date   2020-12-25
 */

namespace CrontabTasks\SmsDiy;

use Library\Controller;
use Model\SmsDiy\SmsApplyLog as LogModel;
use Model\SmsDiy\SmsSignApply as SignModel;
use Library\Business\FzZwx\sdk as SmsSdkBiz;
use Model\SmsDiy\SmsTemplate as TempModel;

class SignApplyResult extends Controller
{
    //日志
    private $_logPath = 'sms_diy/sign/task';
    //是否打印
    private $_isPrint = false;

    public function run()
    {
        pft_log($this->_logPath, '脚本开始执行');
        echo "脚本开始执行\n";
        $logModel = new LogModel();

        //所有商家
        $sid = 0;
        //页码
        $page = 1;
        //页数
        $size = 1;

        $total = $logModel->getLogList($sid, $logModel::_applyTypeSign, true, $page, $size,
            $logModel::_auditStatusWait);
        if (!$total) {
            pft_log($this->_logPath, '无数据');
            exit("无数据\n");
        }

        $this->_print_echo("需要执行:$total");
        pft_log($this->_logPath, '需要执行: ' . $total);

        while ($page <= $total) {
            sleep(1);
            $tmpData = $logModel->getLogList($sid, $logModel::_applyTypeSign, false, $page, $size,
                $logModel::_auditStatusWait);
            if (empty($tmpData[0]['content'])) {
                $message = is_array($tmpData) ? json_encode($tmpData, JSON_UNESCAPED_UNICODE) : '';
                pft_log($this->_logPath, '申请信息不存在' . $message);
                $this->_print_echo("申请信息不存在, 下一个");
                $page++;
                continue;
            }
            $logInfo = $tmpData[0];
            $content = json_decode($logInfo['content'], true);
            $signName = isset($content['sign_name']) ? $content['sign_name'] : '';
            $this->_print_echo($logInfo['content']);
            $resultId = isset($content['result_id']) ? $content['result_id'] : 0;

            $states = $logInfo['state'];
            $this->_print_echo("状态:$states");

            if (!$resultId) {
                pft_log($this->_logPath, '无查询标识');
                $this->_print_echo("无查询标识, 下一个");
                $page++;
                continue;
            }
            if (empty($logInfo['apply_id'])) {
                pft_log($this->_logPath, '申请数据不存在');
                $this->_print_echo("申请数据不存在, 下一个");
                $page++;
                continue;
            }
            if (empty($logInfo['sid'])) {
                pft_log($this->_logPath, '用户信息不存在');
                $this->_print_echo("用户信息不存在, 下一个");
                $page++;
                continue;
            }

            $signId   = $logInfo['apply_id'];
            $sid      = $logInfo['sid'];
            $recordId = $logInfo['record_id'];
            $this->_print_echo("id: $recordId");

            if (in_array(ENV, ['PRODUCTION','TEST'])) {
                $smsBiz = new SmsSdkBiz(); //目前仅支持中网信
                $result = $smsBiz->applySmsSignReview($resultId);

                if (!isset($result['code'])) {
                    $message = is_array($result) ? json_encode($result, JSON_UNESCAPED_UNICODE) : $resultId;
                    pft_log($this->_logPath, '查询异常, 请稍后再试' . $message);
                    $this->_print_echo("查询异常, 请稍后再试, 下一个");
                    $page++;
                    continue;
                }

            } else {
                //非生产环境直接通过
                $result = [
                    'code'   => 1007,
                    'result' => '通过',
                ];
            }

            $code = $result['code'];

            if ($code == 1005) { //待审核 无需更新结果
                $this->_print_echo("待审核, 下一个");
                $page++;
                continue;
            }

            $signModel = new SignModel();
            $logModel  = new LogModel();
            $tempModel = new TempModel(true);

            $state  = 0;
            $remark = '';

            if ($code == 1006) { //拒绝  需要更细更新
                $state  = $signModel::_auditStatusFail;
                $remark = $result['result'];
                $this->_print_echo("拒绝");
            }

            if ($code == 1007) { //通过
                $this->_print_echo("通过");
                $state  = $signModel::_auditStatusSuccess;
                $remark = $result['result'];
            }

            //验证产品短信模板信息
            $tempMap = $tempModel->getInfoByAid($sid);

            if ($state) {
                $signModel->startTrans();
                $updateApply = $signModel->saveApplyByState($signId, $sid, $state);
                if (!$updateApply) {
                    $signModel->rollback();
                    pft_log($this->_logPath, '申请信息更新失败' . json_encode([$signId, $sid, $state]));
                    $this->_print_echo("申请信息更新失败, 下一个");
                    $page++;
                    continue;
                }
                $updateLog = $logModel->saveLog($recordId, $remark, $state);
                if (!$updateLog) {
                    $signModel->rollback();
                    pft_log($this->_logPath, '记录信息更新失败' . json_encode([$recordId, $remark, $state]));
                    $this->_print_echo("记录信息更新失败, 下一个");
                    $page++;
                    continue;
                }

                //审核通过写入到模板表
                if ($state == $signModel::_auditStatusSuccess) {
                    if (isset($tempMap['sms_sign'])) {
                        //检测是否含通用票类模板
                        $checkRes = $tempModel->getInfoByAidAndPid($sid, 0);
                        if (!$checkRes) {
                            //加入通用票类模板
                            $tempResult = $tempModel->addSign($sid, $signName);
                            if (!$tempResult) {
                                $signModel->rollback();
                                pft_log($this->_logPath, '抬头应用加入处理失败' . json_encode([$recordId, $remark, $state]));
                                $this->_print_echo("抬头应用加入处理失败, 下一个");
                                $page++;
                                continue;
                            }
                        }
                        //编辑
                        $tempResult = $tempModel->saveSign($sid, $signName);
                        if (!$tempResult) {
                            $signModel->rollback();
                            pft_log($this->_logPath, '抬头应用更新处理失败' . json_encode([$recordId, $remark, $state]));
                            $this->_print_echo("抬头应用更新处理失败, 下一个");
                            $page++;
                            continue;
                        }
                    } else {
                        //新增
                        $tempResult = $tempModel->addSign($sid, $signName);
                        if (!$tempResult) {
                            $signModel->rollback();
                            pft_log($this->_logPath, '抬头应用加入处理失败' . json_encode([$recordId, $remark, $state]));
                            $this->_print_echo("抬头应用加入处理失败, 下一个");
                            $page++;
                            continue;
                        }
                        //如果已存在短信子自定义模板，更新全部产品
                        $useTotal = $tempModel->getTempTotalByAid($sid);
                        if ($useTotal > 1) {
                            $updateAllTemp = $tempModel->saveSign($sid, $signName);
                            if (!$updateAllTemp) {
                                $signModel->rollback();
                                pft_log($this->_logPath, '抬头应用更新处理失败' . json_encode([$recordId, $remark, $state]));
                                $this->_print_echo("抬头应用更新处理失败, 下一个");
                                $page++;
                                continue;
                            }
                        }
                    }
                }

                $signModel->commit();

                $page--;
                $total--;
            } else {
                $this->_print_echo("当前状态无须处理");
                $page++;
            }
        }

        echo "脚本执行完成\n";
    }

    private function _print_echo(string $msg = '')
    {
        if ($this->_isPrint) {
            echo $msg . "\n";
        }

    }
}
