<?php
/**
 * 物理卡号处理类
 * <AUTHOR> Li
 */

namespace CrontabTasks\Product;

use Library\Controller;
use Model\Product\BaseCard;
use Model\AdminConfig\StaffManage;
use Model\Product\AnnualCard;
use Model\Product\ParkCard;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class PhysicsNoPush extends Controller
{
    private $_maxNum = 100;

    /**
     * 生成提供给闸机下载的物理卡号TXT文件
     * @Author: PeiJun Li
     * @Data: 2019-07-09
     */
    public function getPullTxt()
    {
        pft_log('debug/lee', json_encode(['开始处理txt文件了', date('Y-m-d H:i:s')], JSON_UNESCAPED_UNICODE));
        //查询出pft_card type=1或type=3或type=7的数据
        //处理员工卡
        $staffArrs = $staffIdArr = [];
        //处理年卡
        $annualCardArrs = $annualCardIdArr = [];
        //处理身份证会员卡
        $idCardNoMemberArr = $idCardNoMemberIdArr = [];

        $baseCardModel   = new BaseCard();
        $staffModel      = new StaffManage();
        $annualCardModel = new AnnualCard();
        $packCardModel   = new ParkCard();
        $staffCardInfo   = $baseCardModel->getCardInfoArr();
        foreach ($staffCardInfo as $item) {
            switch ($item['type']) {
                case 1:
                    $staffIdArr[]           = $item['id'];
                    $staffArrs[$item['id']] = $item['physics_no'];
                    break;
                case 3:
                    $physicsNo                   = $annualCardArrs[] = dechex($item['physics_no']) == 0 ? $item['physics_no'] : dechex($item['physics_no']);
                    $annualCardIdArr[$physicsNo] = $item['physics_no'];
                    break;
                case 7:
                    $idCardNoMemberIdArr[]          = $item['id'];
                    $idCardNoMemberArr[$item['id']] = $item['physics_no'];
                    break;
            }
        }

        //通过对应类型数据到对应业务表中查询出供应商下的明细
        //查出供应商下的员工
        $staffArr = $staffModel->getCardByCardIdArr($staffIdArr);
        $sidStaff = [];
        //获取供应商集合
        $applyDidArr = array_column($staffArr, 'apply_did');
        foreach ($staffArr as $item) {
            $sidStaff[$item['apply_did']][] = ['physics_no' => $staffArrs[$item['card_id']], 'type' => 1];
        }

        //查出供应商下的年卡
        $annualCardArr = [];
        $physicsNoArr  = array_filter($annualCardArrs);
        $totalPage     = ceil(count($physicsNoArr) / $this->_maxNum);
        for ($i = 0; $i < $totalPage; $i++) {
            $tmpPhysicsNoArr  = array_splice($physicsNoArr, $i, $this->_maxNum);
            $tmpAnnualCardArr = $annualCardModel->getCardsByPhysicsArr($tmpPhysicsNoArr, 'sid,physics_no');
            if ($tmpAnnualCardArr) {
                $annualCardArr = array_merge($annualCardArr, $tmpAnnualCardArr);
            }

        }

        $sidAnnualCard = [];
        //获取供应商集合
        $applyDidArr = array_merge($applyDidArr, array_column($annualCardArr, 'sid'));
        foreach ($annualCardArr as $item) {
            $sidAnnualCard[$item['sid']][] = ['physics_no' => $annualCardIdArr[$item['physics_no']], 'type' => 3];
        }

        $packCardInfo = $packCardModel->getParkCardByCardIdArr($idCardNoMemberIdArr);
        $sidPackCard  = [];
        //获取供应商集合
        $applyDidArr = array_merge($applyDidArr, array_column($packCardInfo, 'sid'));
        foreach ($packCardInfo as $item) {
            $sidPackCard[$item['sid']][] = ['physics_no' => $idCardNoMemberArr[$item['card_id']], 'type' => 7];
        }

        //以供应商为单位生成TXT文件
        $applyDidArr = array_unique($applyDidArr);
        foreach ($applyDidArr as $applyDid) {
            $totalStaff    = [];
            $totalAnnual   = [];
            $totalPackCard = [];
            if (isset($sidStaff[$applyDid])) {
                $totalStaff = $sidStaff[$applyDid];
            }
            if (isset($sidAnnualCard[$applyDid])) {
                $totalAnnual = $sidAnnualCard[$applyDid];
            }
            if (isset($sidPackCard[$applyDid])) {
                $totalPackCard = $sidPackCard[$applyDid];
            }

            $totalArray = array_merge($totalStaff, array_merge($totalAnnual, $totalPackCard));
            if ($totalArray) {
                $txt = '';
                foreach ($totalArray as $item) {
                    $txt .= $item['physics_no'] . '|' . $item['type'] . ',';
                }
                $myfile = fopen(EXCEL_DIR . $applyDid . ".txt", "w") or die("Unable to open file!");  //w  重写  a追加
                fwrite($myfile, $txt);
                fclose($myfile);
            } else {
                continue;
            }
        }
        pft_log('debug/lee', json_encode(['txt文件处理完了', date('Y-m-d H:i:s')], JSON_UNESCAPED_UNICODE));
        exit('处理完成');
    }
}
