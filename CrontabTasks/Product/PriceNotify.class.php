<?php

namespace CrontabTasks\Product;

use Library\Controller;
use Model\Product\Price;

/**
 * 定时清理价格配置的日志
 * 
 * @date    2017-05-15
 * <AUTHOR>
 */

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}


class PriceNotify extends Controller {

    //保留多少天的记录
    const __SAVE_DAY__ = 30;
    //脚本最大执行时间
    const __MAX_EXECUTION_TIME__  = 1800;

    public function __construct() {
        //初始化价格模型
        $this->_priceModel = new Price();
    }


    /**
     * 清理日志
     * 
     * <AUTHOR>
     * @date   2017-05-15
     */
    public function clearRecords() {

        pft_log('price/autoClear', date('Y-m-d H:i:s'));

        $beginTime = time();
        //第一次运行可能数据较多，分批次处理
        while (1) {
            $res = $this->_priceModel->clearRecords(self::__SAVE_DAY__, 5000);

            pft_log('price/autoClear', time());

            if (time() - $beginTime > self::__MAX_EXECUTION_TIME__) {
                break;
            }

            //没有要删除的记录
            if (!$res) {
                break;
            }

            sleep(2);
        }
        
    }

}