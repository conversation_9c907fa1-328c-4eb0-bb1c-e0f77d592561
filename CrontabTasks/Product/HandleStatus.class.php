<?php

/**
 * 供应商套餐到期超过一定天数下架供应商的自供应产品
 *
 * <AUTHOR>
 * @date   2021-11-19
 */


namespace CrontabTasks\Product;

class HandleStatus
{

    private $_logDir = 'package_expire/product_off/';

    //php /var/www/html/Service/Crontab/runNew.php Product/HandleStatus packageExpireUpdateStatus
    public function packageExpireUpdateStatus()
    {
        $params = $GLOBALS['argv'];

        if (isset($params[3]) && isset($params[4])) {
            $expireStartTime = strtotime($params[3]);
            $expireEndTime   = strtotime($params[4]);
        } else {
            $expireStartTime = strtotime(date('Ymd', strtotime('-50 days')));
            $expireEndTime   = strtotime(date('Ymd', strtotime('-30 days')));
        }

        $packageExpireRes = (new \Business\AppCenter\Package())->supplyPackageExpireMember($expireStartTime, $expireEndTime);
        if ($packageExpireRes['code'] != 200) {
            pft_log($this->_logDir . 'fail', json_encode([
                'start_time' => date('Ymd H:i:s',$expireStartTime),
                'end_time'   => date('Ymd H:i:s',$expireEndTime),
                'err_msg'    => '查询供应商套餐过期用户数据错误',
                'res'        => $packageExpireRes,

            ], JSON_UNESCAPED_UNICODE));
            exit('查询供应商套餐过期用户数据错误');
        }

        if (empty($packageExpireRes['data'])) {
            pft_log($this->_logDir . 'success', json_encode([
                'start_time' => date('Ymd H:i:s',$expireStartTime),
                'end_time'   => date('Ymd H:i:s',$expireEndTime),
                'msg'        => '未查询到供应商套餐过期用户数据',
                'res'        => $packageExpireRes,

            ], JSON_UNESCAPED_UNICODE));
            exit('未查询到供应商套餐过期用户数据');
        }

        $memberIdArr = $packageExpireRes['data'];
        $productZgyBiz = new \Business\Product\ProductZgyList();
        $productBiz    = new \Business\Product\Product();

        foreach ($memberIdArr as $memberId) {
            $page      = 1;
            $pageSize  = 20;

            while (true) {

                $result = $productZgyBiz->getJavaInterface(1, false, $memberId, $page, $pageSize);
                if ($result['code'] != 200) {
                    pft_log($this->_logDir . 'fail', json_encode([
                        'member_id'  => $memberId,
                        'page'       => $page,
                        'page_size'  => $pageSize,
                        'err_msg'    => '查询供应商自供应产品失败',
                        'res'        => $result,

                    ], JSON_UNESCAPED_UNICODE));
                    exit('查询供应商套餐过期用户数据错误');
                }

                if (empty($result['data']) || $result['data']['totalCount'] == 0 || empty($result['data']['products'])) {
                    break;
                }

                foreach ($result['data']['products'] as $product) {
                    $operRes = $productBiz->soldOut($memberId, 1, $product['landId']);
                    $subDir  = $operRes['code'] == 200 ? 'success' : 'fail';
                    pft_log($this->_logDir . $subDir, json_encode([
                        'member_id' => $memberId,
                        'land_id'   => $product['landId'],
                        'res'       => $operRes,
                    ], JSON_UNESCAPED_UNICODE));
                }

                $page++;
              //  var_dump($memberId, $result);exit;
            }

            continue;
        }

        exit('供应商套餐过期下架自供应产品操作结束..');
    }
}