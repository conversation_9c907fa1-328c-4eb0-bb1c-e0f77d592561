<?php
/**
 * 年卡有效期更新
 * <AUTHOR> Li
 * @date 2020-03-25
 */

namespace CrontabTasks\Product;

use Library\Cache\Cache;
use Library\Controller;
use Library\Tools\Helpers;
use Library\MulityProcessHelper;
use Model\Product\AnnualCard;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class AnnualCardPostponed extends Controller
{
    private $excelArray = [

        //生产池州
        //2010497 => '/home/<USER>/2010497AnnualCard.xlsx',//excel文件路径
        //生产威海小螺号
        //2885755 => '/home/<USER>/2885755AnnualCard.xlsx',//excel文件路径
        //威高海洋馆
        //1217846 => '/tmp/1217846AnnualCard.xls',//excel文件路径
        //邯郸佛山
        //1537751 => '/tmp/1537751AnnualCard.xls',//excel文件路径
        //朝阳年卡
        277078 => '/tmp/126814AnnualCard2.xls',//excel文件路径
        //萌宠乐园
        //450077 => '/tmp/564747AnnualCard.xls',//excel文件路径
        //永定河
        //567997 => '/tmp/567997AnnualCard.xls',//excel文件路径
    ];

    private $annualCardModel = null;
    private $redisClient     = null;

    public function getAnnualCardModel()
    {
        if (empty($this->annualCardModel)) {
            $this->annualCardModel = new AnnualCard();
        }

        return $this->annualCardModel;
    }

    /**
     * 多进程任务入口
     * <AUTHOR> Li
     * @date 2020-03-25
     *
     * @param $data
     * @param $param
     *
     * @return array
     */
    public function runWorker($data, $params)
    {
        if (!isset($params['action'])) {
            exit('请选择要执行的方法');
        }

        if (!isset($params['sid'])) {
            exit('供应商信息缺失');
        }

        $action = $params['action'];
        $sid    = $params['sid'];
        $this->{$action}($data, $sid);

        return true;
    }

    /**
     * 延期执行入口
     * <AUTHOR> Li
     * @date 2020-03-25
     *
     * @return array
     */
    public function postponAnnualCard()
    {
        if (empty($this->excelArray)) {
            exit('没有指明文件');
        }
        pft_log('debug/execl_statistics', 'start');
        //自动加载
        Helpers::composerAutoload();
        $this->redisClient = Cache::getInstance('redis');
        $startTime = time();
        $task = new MulityProcessHelper($this, 1, 0);

        foreach ($this->excelArray as $sid => $value) {
            $params           = ['action' => 'runPostponAnnualCard'];
            $data             = $this->getAnnualCardInfo($value);
            $params['sid']    = $sid;

            $task->run($data, $params);
        }

        //统计处理数据
        $this->statisticsData();
        $endTime = time();
        $time = $endTime - $startTime;
        pft_log('debug/execl_statistics', 'end' . '|耗时：' . $time . 's');
        echo '耗时：' . $time;
        return true;

    }

    /**
     * 延期执行方法
     * <AUTHOR> Li
     * @date 2020-03-25
     *
     * @param $data
     *
     * @return array
     */
    public function runPostponAnnualCard($data, $sid)
    {
        $logPath       = 'debug/execl_cz1';
        $virtualIndex  = 4;
        $avalidEndIndex= 6;
        $addMonth      = 7;
        $total         = count($data);
        $cz1TotalKey   = $this->getCz1RedisKey('cz1', 'total');
        $cz1SuccessKey = $this->getCz1RedisKey('cz1', 'success');
        $this->redisClient->incr($cz1TotalKey, $total);//总数累计

        $annualCardModel = $this->getAnnualCardModel();
        $map             = ['sid' => $sid];
        
        foreach ($data as $key => $value) {
            if (empty($value[$virtualIndex])) {
                pft_log($logPath, 'empty:' . implode('##', $value));
                continue;
            }

            $checkRes = $annualCardModel->checkCard('virtual_no', $value[$virtualIndex], $map, 'id,avalid_end');
            if (!$checkRes) {
                pft_log($logPath, 'noExist:' . implode('##', $value));
                continue;
            }

            $tmpDate = date('Y-m-d', strtotime($value[$avalidEndIndex]));
            if (isset($value[$addMonth]) && !empty($value[$addMonth])) {
                $avalidEnd  = strtotime("{$tmpDate} 23:59:59 +{$value[$addMonth]} month");
            } else {
                $avalidEnd  = strtotime($tmpDate. ' 23:59:59');
            }
            
            $updataData = [
                'avalid_end' => $avalidEnd,
            ];

            $updataRes = $annualCardModel->updateAnnualById($checkRes['id'], $updataData);
            if (!$updataRes) {
                $logData = [
                    'data'       => $value,
                    'updataRes'  => $updataRes,
                    'checkResId' => $checkRes['id'],
                    'updataData' => $updataData,
                ];
                pft_log($logPath, 'updataFail:' . json_encode($logData, JSON_UNESCAPED_UNICODE));
                continue;
            }

            $this->redisClient->incr($cz1SuccessKey, 1);//成功数累计
        }

        return true;
    }

    /**
     * 获取延期导入信息
     * <AUTHOR> Li
     * @date 2020-03-25
     *
     * @param $path
     * @param $type
     *
     * @return array
     */
    private function getAnnualCardInfo($path)
    {
        //获取Excel文件
        $excelArray = $this->readExcelDetail($path);

        if (empty($excelArray)) {
            exit('没有数据');
        }

        return $excelArray;
    }

    /**
     * 读取excel
     * <AUTHOR> Li
     * @date 2020-03-25
     *
     * @param $filename 文件
     *
     * @return array
     */
    private function readExcelDetail($filename)
    {
        //$objReader = \PHPExcel_IOFactory::createReader('Excel2007');
        $objReader = \PHPExcel_IOFactory::createReader('Excel5');
        $objReader->setReadDataOnly(true);
        $objPHPExcel = $objReader->load($filename, $encode = 'utf-8');
        $objPHPExcel->setActiveSheetIndex(0);
        $objWorksheet       = $objPHPExcel->getActiveSheet();
        $hightestrow        = $objWorksheet->getHighestRow();
        $highestColumn      = $objWorksheet->getHighestColumn();
        $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);
        $excelData          = array();

        for ($row = 2; $row <= $hightestrow; $row++) {
            for ($col = 0; $col < $highestColumnIndex; $col++) {
                $tmpValue = (string)$objWorksheet->getCellByColumnAndRow($col, $row)->getValue();
                if (empty($tmpValue)) {
                    continue;
                }
                $excelData[$row - 1][] = $tmpValue;
            }
        }

        return $excelData;

    }

    /**
     * 统计处理数据
     * <AUTHOR> Li
     * @date 2020-03-25
     *
     * @return array
     */
    private function statisticsData()
    {
        $array = [
            'cz1' => ['total', 'success'],
            'cz2' => ['total', 'success'],
            'ydh' => ['total', 'success'],
        ];

        foreach ($array as $key => $value) {
            $total   = $this->redisClient->get($this->getCz1RedisKey($key, $value[0]));
            $success = $this->redisClient->get($this->getCz1RedisKey($key, $value[1]));
            pft_log('debug/execl_statistics', $key . 'Total:' . $total . '||' . $key . 'Success:' . $success);
        }

        foreach ($array as $key => $value) {
            $this->redisClient->delete($this->getCz1RedisKey($key, $value[0]));
            $this->redisClient->delete($this->getCz1RedisKey($key, $value[1]));
        }

        return true;
    }

    private function getCz1RedisKey($type = 'cz1', $param = 'total')
    {
        return "annual_card_postponed:" . $type . ':' . $param;
    }

}