<?php
/**
 * 年卡数据导入
 * <AUTHOR> Li
 * @date 2020-06-13
 */

namespace CrontabTasks\Product;

use Business\Member\RegisterAction;
use Library\Constants\MemberConst;
use Library\Controller;

use Model\Member\Member;
use Model\Product\AnnualCard;
use Library\MulityProcessHelper;
use Pimple\Container;

class AnnualCardImport extends Controller
{

    //csv文件路径
    //const  CSV_PATH = '/tmp/one.csv';
    //const  CSV_PATH = '/tmp/two.csv';
    //const  CSV_PATH = '/tmp/three.csv';
    const  CSV_PATH = '/tmp/fore.csv';
    //开几个子进程
    const PROCESS_NUM = 1;
    private $num = 1;
    //供应商id
    //const SID = 2199301;
    //const SID = 3385;  // 内网
    //年卡有效天数
    //const AVALID_DAYS = 3650;
    //年卡套餐
    //private $_pid = 495204;
    //private $_pid = 346397;  // 内网
    //private $_pid = 210676;  // dev
    //有效期开始时间
    //private $_startTime = 0;
    //有效期结束时间
    //private $_endTime = 0;


    /**
     * 入口
     * <AUTHOR>  Li
     * @date   2020-12-24
     */
    public function createAccount()
    {

        $fp = fopen(self::CSV_PATH, 'r');
        fgetcsv($fp);

        $data = [];
        while ($row = fgetcsv($fp)) {
            $data[] = $row;
            if (count($data) > 100000) {
                break;
            }
        }

        if (!$data) {
            exit('没有数据');
        }

        //总条数
        $count = count($data);

        defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
        $task = new MulityProcessHelper($this, self::PROCESS_NUM, $count / self::PROCESS_NUM);
        $task->run($data, 'account');
    }

    public function createCard()
    {
        $fp = fopen(self::CSV_PATH, 'r');
        fgetcsv($fp);

        $data = [];
        while ($row = fgetcsv($fp)) {
            $data[] = $row;
            if (count($data) > 100000) {
                break;
            }
        }

        if (!$data) {
            exit('没有数据');
        }

        //总条数
        $count = count($data);

        defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
        $task = new MulityProcessHelper($this, self::PROCESS_NUM, $count / self::PROCESS_NUM);
        $task->run($data, 'card');
    }

    /**
     * 数据处理
     * <AUTHOR>  Li
     * @date   2020-12-24
     *
     * @param  array  $data  要处理的数据
     */
    public function runWorker($data, $type)
    {

        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();

        //模型容器
        $this->container = new Container();

        $this->container['member_master'] = function () {
            return new Member();
        };
        $this->container['annual']        = function () {
            return new AnnualCard();
        };

        $this->container['customer']        = function () {
            return new \Model\Member\Customer();
        };
        $this->container['customer_biz']        = function () {
            return new \Business\Member\Customer();
        };

        $this->container['register']        = function () {
            return new RegisterAction();
        };

        if ($type == 'account') {
            $this->_createAccount($data);
        }

        if ($type == 'card') {
            $this->_createCard($data);
        }
    }

    private function _createAccount($data)
    {
        if (!$data) {
            return false;
        }

        foreach ($data as &$row) {
            if (empty($row[0])) {
                continue;
            }
            $row[3] = iconv("GBK", "UTF-8", $row[3]);
            $row[4] = substr($row[4], 1, 18);
            $row[6] = strtotime($row[6]);
            $row[7] = strtotime($row[7]) + (86400 - 1);

            $res = $this->_createAccountAction($row);
            if (!$res['mid']) {
                pft_log('annualcard/import',
                    json_encode(['_createAccount' => $res], JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR));
            }


            //是否是合法的身份证
            //if (\idcard_checksum18($row[4])) {
            //    $res = $this->_createAccountAction($row);
            //    if (!$res['mid']) {
            //        pft_log('annualcard/import',
            //            json_encode(['_createAccount' => $res], JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR));
            //    }
            //} else {
            //    pft_log('annualcard/import',
            //        json_encode(['_createAccount' => '身份证不合法', 'id_card_no' => $row[4]], JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR));
            //}
        }
        echo '<pre>';print_r('账号创建完成');
    }

    private function _createCard($data)
    {
        if (!$data) {
            return false;
        }

        foreach ($data as &$row) {
            if (empty($row[0])) {
                continue;
            }

            $row[3] = iconv("GBK", "UTF-8", $row[3]);
            $row[4] = substr($row[4], 1, 18);
            $row[6] = strtotime($row[6]);
            $row[7] = strtotime($row[7]) + (86400 - 1);

            $annuaLRes = $this->_createAnnual($row);
            if (!$annuaLRes) {
                pft_log('annualcard/import', json_encode(['_createCard' => '年卡生成失败'],
                    JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR));
            }
            //是否是合法的身份证
            //if (\idcard_checksum18($row[4])) {
            //  $annuaLRes = $this->_createAnnual($row);
            //  if (!$annuaLRes) {
            //  pft_log('annualcard/import', json_encode(['_createCard' => '年卡生成失败'],
            //      JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR));
            //  }
            //} else {
            //    pft_log('annualcard/import',
            //        json_encode(['_createCard' => '身份证不合法', 'id_card_no' => $row[4]], JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR));
            //}
        }
        echo '<pre>';print_r('年卡创建完成');
    }

    /**
     * 生成账号
     * <AUTHOR>  Li
     * @date   2020-12-24
     *
     * @param  mix
     */
    private function _createAccountAction($cardInfo)
    {

        if ($cardInfo[4]) {
            $customerId    = $this->container['customer']->getCustomerIdByIdCardNo($cardInfo[4]);
            if ($customerId) {
                return ['mid' => 0, 'error' => '身份证存在'];
            }
        }

        $request = [
            'name'        => $cardInfo[3],
            'bind_mobile' => $cardInfo[5],
            'type'        => MemberConst::ROLE_TOURIST,
            'avatar'      => 'http://www.12301.cc/images/touxiang.png',
            'passwd'      => md5(md5(substr($cardInfo[5], 12))),
            'identifier'  => $cardInfo[5],
            'customer_id' => $customerId ?? 0,
            'info_source' => MemberConst::INFO_OTHERS,
            'page_source' => MemberConst::PAGE_PLATFORM,
        ];

        if ($cardInfo[4]) {
            $request['id_card']     = $cardInfo[4];
            $request['identifier']  = $cardInfo[4];
        }

        $registerRes    = $this->container['register']->register((object)$request);
        if ($registerRes['code'] == 200) {
            pft_log('annualcard/import',
                json_encode(['ac' => '_createAccountAction',  'info'=> "用户生成成功： 手机号：{$cardInfo[5]}， 身份证号{$cardInfo[4]}, 姓名：{$cardInfo[3]}"], JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR));
            return ['mid' => $registerRes['data']['member_id']];
        } else {
            pft_log('annualcard/error',
                json_encode(['ac' => '_createAccountAction',  'info'=> "用户生成失败： 手机号：{$cardInfo[5]}， 身份证号{$cardInfo[4]}, 姓名：{$cardInfo[3]}，原因： {$registerRes['msg']}"], JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR));
            return ['mid' => 0, 'error' => $registerRes['msg']];
        }
    }

    /**
     * 创建平台的年卡信息
     * <AUTHOR>  Li
     * @date   2020-12-24
     *
     * @param  array  $cardInfo  导入的年卡信息
     *
     * @return mix
     */
    private function _createAnnual($cardInfo)
    {
        //根据身份证号码获取用户id
        //用户二期 - 信息获取修改
        if ($cardInfo[4]) {
            $memberId    = $this->container['customer_biz']->getMemberIdByIdCardNoForAnnualCard($cardInfo[4]);

            if (empty($memberId)) {
                $memberId    = $this->container['member_master']->getMemberInfo($cardInfo[5], 'mobile')['id'];
            }
        } else {
            $memberId    = $this->container['member_master']->getMemberInfo($cardInfo[5], 'mobile')['id'];
        }

        if (!$memberId) {
            return false;
        }

        //激活时间计算
        $activeTime = time();
        //生成虚拟卡号
        $virtualNo = $this->generateVirtualNo();

        $data = [
            'memberid'     => $memberId,
            'sid'          => $cardInfo[0],
            'pid'          => $cardInfo[1],
            'virtual_no'   => $virtualNo,
            'physics_no'   => 0,
            'card_no'      => $cardInfo[2],
            'status'       => 1,
            'annual_status'=> 1,
            'create_time'  => time(),
            'sale_time'    => $activeTime,
            'active_time'  => $activeTime,
            'update_time'  => $activeTime,
            'avalid_begin' => $cardInfo[6],
            'avalid_end'   => $cardInfo[7],
            'dname'        => $cardInfo[3],
            'id_card_no'   => $cardInfo[4] ?? '',
            'mobile'       => $cardInfo[5],
        ];

        $recordId = $this->container['annual']->table('pft_annual_card')->add($data);

        if ($recordId) {
            pft_log('annualcard/import',
                json_encode(['ac' => '_createAnnualAction',  'info'=> "年卡创建成功： 手机号：{$cardInfo[5]}， 身份证号{$cardInfo[4]}, 姓名：{$cardInfo[3]}，虚拟卡号：{$virtualNo}"], JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR));
            return true;
        } else {
            pft_log('annualcard/error',
                json_encode(['ac' => '_createAnnualAction',  'info'=> "年卡创建失败： 手机号：{$cardInfo[5]}， 身份证号{$cardInfo[4]}, 姓名：{$cardInfo[3]}，虚拟卡号：{$virtualNo}"], JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR));
            return false;
        }
    }

    public function generateVirtualNo()
    {
        //生成虚拟卡号
        $nums   = '0123456789';
        $letter = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';

        $virtualNo = substr(str_shuffle($nums . $letter), 0, 7);
        $sum       = 0;
        foreach (str_split($virtualNo) as $le) {
            if (is_numeric($le)) {
                $sum += $le;
            }
        }
        $virtualNo .= strrev($sum)[0];

        $oldCardInfo = $this->container['annual']->getCardInfoByVirtual([$virtualNo], 'virtual_no');


        if ($oldCardInfo && $this->num < 10) {
            $this->num++;
            return $this->generateVirtualNo();
        }

        return $virtualNo;
    }

}
