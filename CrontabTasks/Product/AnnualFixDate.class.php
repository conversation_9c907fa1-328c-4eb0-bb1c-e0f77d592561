<?php
/**
 * 年卡固定有效期
 * <AUTHOR>
 */
namespace CrontabTasks\Product;
use Library\Controller;

use Model\Product\AnnualCard;
use Model\Product\Ticket;
use Library\MulityProcessHelper;

class AnnualFixDate extends Controller {

    /**
     * 执行入口
     * <AUTHOR>
     * @date   2018-08-03
     */
    public function run() {

        //获取cpu核心数
        $content = file_get_contents('/proc/cpuinfo');
        if (!$content) {
            exit('cpu核心数获取失败');
        }

        preg_match_all('/processor/', $content, $matches);
        $cpuNum = count($matches);
        if (!$cpuNum) {
            exit('cpu核心数获取失败');
        }

        $processHelper = new MulityProcessHelper(new \stdClass, $cpuNum);
        $processHelper->runWithCallback([$this, 'fixAction']);

    }


    /**
     * 转化有效期
     * <AUTHOR>
     * @date   2018-08-03
     * @param  integer    $processIndex 子进程index
     */
    public static function fixAction($processIndex, $processNum) {

        //公共多少数据
        $cardModel = new AnnualCard();
        $ticModel  = new Ticket('slave');
        $count = $cardModel->countNotFixedDate();

        $perSize = ceil($count / $processNum);

        $begin = ($processIndex) * $perSize;
        $end   = $begin + $perSize;
        //每次获取一千条
        $times = ceil($perSize / 1000);

        for ($i = 1; $i <= $times; $i++) {
            $annualMap = [];
            $page = $processIndex * $times + $i;
            $data = $cardModel->getNotFixedDateList($page, 1000);

            if (!$data) {
                continue;
            }
            //年卡套餐pid
            $pidArr = array_unique(array_column($data, 'pid'));
            //批量获取门票信息
            $tmpPidMap = $ticModel->getTicketInfoByPidArr($pidArr, 'pid,delaytype,delaydays,order_start,order_end');
            $ticketMap = [];
            foreach ($tmpPidMap as $item) {
                $ticketMap[$item['pid']] = $item;
            }
            $annualMap = self::parseAvalidDate($data, $ticketMap);

            if ($annualMap) {
                $res = $cardModel->fixedDateMulti($annualMap);
                if (!$res) {
                    pft_log('annual/fixed_date', $cardModel->_sql());
                }
            }
        }
    }

    private static function parseAvalidDate($data, $ticketMap) {

        $return = [];
        foreach ($data as $item) {
            if (!isset($ticketMap[$item['pid']])) {
                continue;
            }

            $config = $ticketMap[$item['pid']];
            $delaydays = $config['delaydays'];
            $day = 3600 * 24;

            switch ($config['delaytype']) {
                case 0 :
                    if ($item['active_time'] == 0) {
                        continue 2;
                    }
                    //激活后多少天有效
                    $begin = strtotime(date('Y-m-d', $item['active_time']));
                    $end   = $begin + $delaydays * $day - 1;
                    break;
                case 1 :
                    if ($item['sale_time'] == 0) {
                        continue 2;
                    }
                    //售出后多少天有效
                    $begin = strtotime(date('Y-m-d', $item['sale_time']));
                    $end   = $begin + $delaydays * $day - 1;
                    break;
                case 2:
                    //固定区间有效
                    $begin = strtotime($config['order_start']);
                    $end   = strtotime($config['order_end']);
                    break;
                default:
                    continue;
            }

            $return[$item['id']] = ['begin' => $begin, 'end' => $end];
        }

        return $return;
    }


}