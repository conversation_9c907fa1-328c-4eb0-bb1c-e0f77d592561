<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/9/26 0026
 * Time: 14:10
 */

namespace CrontabTasks\Product;
use Model\Product\AnnualCard as CardModel;
//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

use Library\Controller;
use Model\Cmb\CmbFriend;
use Model\Member\Member;
use Model\Product\AnnualCard;

class CmbAnnual extends Controller
{
    /**
     * 禁用招行1个月未使用的年卡
     * author  linchen
     * Date: 2018-12-10
     */
   public function updateCmbAnnual(){
        $start          = 0;
        $long           = 500;
        $CmbModel       = new CmbFriend();
        $count          = $CmbModel->getNotUsedUser($start,$long,true);
        $num            = ceil($count/$long);
        $productArray   = load_config('product','cmb_pay');
        $memModel       = new Member('slave');
        $memberData     = $memModel->getMemberInfo((string)$productArray['sid'], 'account');
        $sid            = $memberData['id'];
        $annualModel    = new AnnualCard('slave');
        $time           = time();
       $annualPrivBiz   = new \Business\Product\AnnualCardPrivilege();

        for ($i=0;$i<$num;$i++){
           $list   = $CmbModel->getNotUsedUser($start,$long,false);
           foreach ($list as $key => $value){

                   $total = $annualPrivBiz->getHistoryOrder($sid, $value['memberid'], 1, 1, 'count');
                   if ($total > 0 ){
                        $CmbModel->updateOneProductUsed($value['id']);
                   }else{
                       if ($time - strtotime($value['paytime']) > 2592000) {            //超过30天了
                           $card = $annualModel->getCmbAnnualCard($sid, $value['memberid']);
                           if ($time - $value['updatetime'] < 2592000 && $value['updatetime'] != 0 ){
                               continue;
                           }else if ($value['active_num'] > 0)
                           {
                               pft_log('cmb/forbitagain',$value['memberid'].'的年卡又超过30天没用，'.$card['id'].'被我禁用拉,供应商是'.$memberData['dname']);
                           }
                           if ($card){
                               $result = $annualModel->forbiddenCard($sid,$value['memberid'],$card['id']);
                               if ($result){
                                   $CmbModel->updateOneProductUsed($value['id']);
                                   pft_log('cmb/forbit',$value['memberid'].'的年卡超过30天没用，'.$card['id'].'被我禁用拉,供应商是'.$memberData['dname']);
                               }
                           }
                       }
                   }
           }
           $start += $long;
        }
   }
}
