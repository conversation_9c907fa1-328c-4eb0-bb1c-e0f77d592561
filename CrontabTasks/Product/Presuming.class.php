<?php
/**
 * 预消费类产品定时任务
 *
 *
 * <AUTHOR>
 * @date 2017-01-08
 *
 */

namespace CrontabTasks\Product;

use Business\Order\Modify;
use Library\Constants\OrderConst;
use Library\Controller;
use Model\Order\OrderSubmit;
use Model\Order\OrderTools as OrderModel;
use Model\Product\Presuming as PresumingModel;
use Model\Order\OrderHandler as OrderHandler;
use Model\Product\HotSpringCard;
use Business\Product\Presuming as PresumingBiz;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class Presuming extends Controller
{

    //日志目录
    private $_logpath = 'product/presuming';

    public function cancelOrders()
    {
        $presumingModel = new PresumingModel();
        $expireOrders   = $presumingModel->getExpireOrders(1, 500);

        //记录开始执行任务
        pft_log($this->_logpath, json_encode(['msg' => 'start', 'count' => count($expireOrders)]));

        if (!$expireOrders) {
            //没有任务需要处理
            exit('no data');
        }

        //模型
        $orderModel = new OrderModel();
        $modifyBiz  = new Modify();
        //  $soapModel  = self::getSoap();

        foreach ($expireOrders as $item) {
            $orderId = $item['orderid'];
            if (!$orderId) {
                continue;
            }

            //获取订单信息
            $orderInfo = $orderModel->getOrderInfo($orderId);

            //如果订单不存在或是已经支付
            if (!$orderInfo || $orderInfo['pay_status'] != 2) {
                //设置已经处理过
                $res = $presumingModel->cancelDelayOrders([$orderId]);

                pft_log($this->_logpath, json_encode(['orderid' => $orderId, 'res' => $res]));
            } else {
                try {
                    //需要取消订单 （看出来他是未支付的）
                    $res = $modifyBiz->cancelParamsCommonFormat($orderId, 1, OrderConst::PRESUMING_CANCEL);

                    //日志
                    pft_log($this->_logpath, json_encode(['orderid' => $orderId, 'soap_res' => $res]));

                    if ($res['code'] == 200) {
                        //订单取消成功
                        $res = $presumingModel->cancelDelayOrders([$orderId]);

                        //日志
                        pft_log($this->_logpath, json_encode(['orderid' => $orderId, 'cancel_res' => $res]));
                    } else {
                        continue;
                    }
                } catch (\Exception $e) {
                    pft_log($this->_logpath, json_encode(['orderid' => $orderId, 'cancel_err' => $e->getMessage()]));

                    continue;
                }
            }
        }
    }

    /**
     * 检测子票是否全部生成
     * <AUTHOR> Li
     * @date   2018-12-27
     *
     * @param  string  $ordernum  主票订单号
     *
     * @return bool
     */
    public function presumingCheck()
    {
        $presumingBiz = new PresumingBiz();
        $result       = $presumingBiz->presumingCheck();

        if ($result['code'] != 200) {
            //pft_log($this->_logpath, json_encode(['手牌订单绑定失败', $result], JSON_UNESCAPED_UNICODE));
        }
    }

}
