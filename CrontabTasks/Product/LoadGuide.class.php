<?php
/**
 * php /var/www/html/Service/Crontab/runNew.php Product_LoadGuide handel
 * 导入导游历史数据
 * User: lanwanhui
 * Date: 2021/6/18
 * Time: 18:47
 */

namespace CrontabTasks\Product;

use Library\Model;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}


class LoadGuide
{

    private $model = null;

    /**
     * 处理导游历史数据
     * User: lanwanhui
     * Date: 2021/6/18
     */
    public function handel(){

        echo "开始处理\n";

        $this->model = new Model('pft_business_case');

        $relationList = $this->model->table('pft_guide_relation')->where(['fid' => 0, 'state' => 1])->select();

        if (empty($relationList)) {
            echo "无可处理数据\n";
            exit;
        }

        foreach ($relationList as $relation) {

            $updateRelationId = [];
            $deleteRelationId = [];
            $fidList          =  $this->getSonIdsByTopUid($relation['sid']);
            $oldRelationList  = $this->model->table('pft_guide_relation')
                ->field('id,fid,state')
                ->where(['guide_id' => $relation['guide_id'], 'sid' => $relation['sid']])
                ->select();

            foreach ($oldRelationList as $value) {

                //旧的配置已经存在了
                if (in_array($value['fid'], $fidList)){
                    $key = array_search($value['fid'],$fidList);
                    unset($fidList[$key]);
                    //原来状态是停用的更新为可用
                    if ($value['state'] != 1) {
                        $updateRelationId[] = $value['id'];
                    }
                //新的配置不存在
                } else {
                    //原来状态是可用的需要删除
                    if ($value['state'] == 1) {
                        $deleteRelationId[]  = $value['id'];
                    }
                }
            }

            $this->handelData($relation, $fidList, $updateRelationId, $deleteRelationId);

            echo "处理成功guide_id={$relation['guide_id']}\n";

        }

        echo "处理结束\n";

    }


    /**
     * 处理数据
     * User: lanwanhui
     * Date: 2021/9/18
     *
     * @param array $relation          原关系记录
     * @param array $fidList           要添加的分销商id
     * @param array $updateRelationId  要更新的关系
     * @param array $deleteRelationId  要删除的关系
     *
     */
    private function handelData(array $relation, array $fidList, array $updateRelationId, array $deleteRelationId){

        $this->model->startTrans();

        try {

            $rs = $this->model->table('pft_guide_record')->where(['id' => $relation['guide_id']])->limit(1)->save(['share_type'=>3]);
            if ($rs === false) {
                throw new \Exception('更新共享类型失败');
            }

            if (!empty($updateRelationId)) {
                $rs =  $this->model->table('pft_guide_relation')
                    ->where(['id' => ['in',$updateRelationId], 'state' => 2])
                    ->save(['state'=>1]);
                if ($rs === false) {
                    throw new \Exception('更新关系为可用失败');
                }
            }

            if (!empty($deleteRelationId)) {
                $rs =  $this->model->table('pft_guide_relation')
                    ->where(['id' => ['in', $deleteRelationId], 'state' => 1])
                    ->save(['state' => 2]);
                if ($rs === false) {
                    throw new \Exception('更新关系为不可用失败');
                }
            }

            $addRelationData = [];
            foreach ($fidList as $fid) {
                $addRelationData[] = [
                    'guide_id'    => $relation['guide_id'],
                    'fid'         => $fid,
                    'group_id'    => 0,
                    'sid'         => $relation['sid'],
                    'creater_id'  => $relation['creater_id'],
                    'state'       => 1,
                    'create_time' => time(),
                    'update_time' => 0,
                ];
            }
            if (!empty($addRelationData)) {
                $rs = $this->model->table('pft_guide_relation')->addAll($addRelationData);
                if (!$rs) {
                    throw new \Exception('添加关系失败');
                }
            }

            $this->model->commit();

        } catch (\Throwable $e) {

            $this->model->rollback();

            $logData = [
                'relation'          => $relation,
                'fidList'           => $fidList,
                'updateRelationId'  => $updateRelationId,
                'deleteRelationId'  => $deleteRelationId,
                'dbError'           => $this->model->getDbError(),
                'lastSql'           => $this->model->getLastSql()
            ];

            pft_log('guide_share_type_handel', json_encode($logData));

            echo "处理失败guide_id={$relation['guide_id']}\n";

        }

    }


    /**
     * 查找一二级分销商
     * User: lanwanhui
     * Date: 2021/9/18
     *
     * @param int $parentId  上级id
     *
     * @return array
     */
    private function getSonIdsByTopUid(int $parentId)
    {
        $relationQuery =  new \Business\JavaApi\Member\Query\CommonRelationQuery();

        $param = [[$parentId], [], 1, 10000, [], [0], [0]];

        $rs = call_user_func_array([$relationQuery, 'querySubIdListByConditionOr'] ,$param);

        $sonIdArr1 = $rs['data']['list'] ?? [];
        if (empty($sonIdArr1)) {
            return [];
        }

        $sonIdArr2 = [];
        $param = [$sonIdArr1, [], 1, 10000, [], [0], [0]];
        $rs = call_user_func_array([$relationQuery, 'querySubIdListByConditionOr'] ,$param);
        $sonIdArr2 = $rs['data']['list'] ?? [];

        $sonIdArr = array_values(array_unique(array_merge($sonIdArr1,$sonIdArr2)));

        $key = array_search($parentId, $sonIdArr);
        if ($key !== false) {
            unset($sonIdArr[$key]);
        }

        return $sonIdArr;

    }

}