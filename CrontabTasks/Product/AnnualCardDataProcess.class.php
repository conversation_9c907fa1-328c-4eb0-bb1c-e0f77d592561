<?php
/**
 * 年卡补卡数据修复 由于补卡自动生成的订单号有重复,导致写入交易流水失败， 故此该脚本用户修复缺少的交易流水数据
 * <AUTHOR> Li
 * @date   2019-11-08
 * TODO::通过年卡交易追踪表获取到补卡类型重复的订单号  以白水洋的供应商为主 修复其他供应商对应的交易流水
 */

namespace CrontabTasks\Product;

use Library\Controller;

use Model\Product\AnnualCard;
use Business\JavaApi\Fund\AnnualCardApi;

class AnnualCardDataProcess extends Controller
{
    private $_annualApi = '';

    /**
     * 执行入口
     * <AUTHOR> Li
     * @date   2019-11-08
     */
    public function run()
    {
        $annualModel = new AnnualCard();
        $api         = new \Business\JavaApi\Trade\Query();
        $result      = $annualModel->getAnnualTradeExceptionData();

        //TODO::通过获取到的数据查询流水表中是否有对应的数据  没有对应数据 对订单流水表进行补充并修改年卡追踪表的对应信息
        if (!empty($result)) {
            foreach ($result as $item) {
                $outTradeNo = 'PAY_' . $item['ordernum'] . $item['payway'];

                $condition['fid']         = $item['member_id'];
                $condition['aid']         = $item['sid'];
                $condition['tradeno_arr'] = [$outTradeNo];
                $condition['order_arr']   = [$item['ordernum']];
                $condition['daction']     = 1;

                $recordInfo = $api->memberQuery($condition);
                //$recordInfo = $tradModele->getOneJournalInfo($item['member_id'], $item['sid'], $outTradeNo, 0);
                if (isset($recordInfo['code']) && $recordInfo['code'] == 200 && !empty($recordInfo['data'])) {
                } else {
                    //补充交易流水
                    $orderId    = str_replace('.', '', microtime(true));
                    $outTradeNo = 'PAY_' . $orderId . $item['payway'];
                    $memo       = "补卡手续费|流水号:{$outTradeNo}";
                    $this->onlineSupplement2Supplier($item['member_id'], $item['sid'], $orderId, $item['payway'],
                        $item['pay_money'], $outTradeNo, $memo, $item['add_time']);
                    //更新年卡追踪表的订单号
                    $res = $annualModel->changeAnnualCardTradeOrdernumById($item['id'], $item['ordernum'], $orderId,
                        $memo);
                    if ($res === false) {
                        pft_log('annual_card_response',
                            json_encode(['年卡追踪表记录更新失败', $item['id'], $item['ordernum'], $orderId],
                                JSON_UNESCAPED_UNICODE), 3);
                    }
                }
            }
        }
        echo '数据处理完成';
    }

    private function onlineSupplement2Supplier($memberId = 0, $aid, $orderId, $payType, $payMoney, $outTradeNo, $memo = '', $tradeTime = 0)
    {
        $retry  = 0;
        $result = $this->getAnnualApi()->onlineSupplement2Supplier($memberId, $aid, $aid, $orderId, $payType, $payMoney,
            $outTradeNo, $memo, $tradeTime);
        if ($result['code'] == 408) {
            //如果是请求超时了，重新进行请求
            sleep(1);
            $retry  = 1;
            $result = $this->getAnnualApi()->onlineSupplement2Supplier($memberId, $aid, $aid, $orderId, $payType,
                $payMoney, $outTradeNo, $memo, $tradeTime);
        }

        if ($result['code'] != 200) {
            pft_log('annual_card_response', json_encode([
                '年卡补卡流水处理失败',
                $memberId,
                $aid,
                $aid,
                $orderId,
                $payType,
                $payMoney,
                $outTradeNo,
                $memo,
                $tradeTime,
                $retry,
            ], JSON_UNESCAPED_UNICODE), 3);
        }
    }

    private function getAnnualApi()
    {
        if (empty($this->_annualApi)) {
            $this->_annualApi = new AnnualCardApi();
        }

        return $this->_annualApi;
    }
}