<?php
/*
 * 高可用演出产品实时任务
 *
 * <AUTHOR>
 * @date   2018-01-16
 */
namespace CrontabTasks\Product;

use Business\Product\Show as ShowBiz;
use Library\Controller;
use Model\Product\Show as ShowModel;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class Show extends Controller
{
    /**
     * 生成
     * <AUTHOR>
     * @date   2018-01-16
     *
     * @return
     */
    public function createRoundExt()
    {
        $showModel = new ShowModel();
        $showBiz   = new ShowBiz();

        //开始的时间
        $startTime = '2018-03-13 21:10:00';

        $handleList = $showModel->getHandleRoundList($startTime, $page = 1, $size = 100, $order = 'desc');

        foreach ($handleList as $item) {
            $venueId = $item['venus_id'];
            $roundId = $item['id'];
            $sid     = $item['opid'];
            $isNew   = $item['is_new'];

            $res = $showBiz->createRoundSeat($venueId, $roundId, $sid, $isNew, true);

            if ($res['code'] != 3) {
                pft_log('show_task/debug', json_encode([$venueId, $roundId, $isNew, $res]));
            }

            if ($res['code'] != 1) {
                //发送提醒
                $errorMsg = $res['msg'];
            }
        }
    }

    /**
     * 将两个月之前的场次座位数据进行归档
     * 每个月的一号去跑这个数据
     * <AUTHOR>
     * @date   2018-03-14
     *
     * @return
     */
    public function archiveRound()
    {
        global $argv;

        if (isset($argv[3]) && isset($argv[4])) {
            $startDate = date('Y-m-d', strtotime($argv[3]));
            $endDate   = date('Y-m-d', strtotime($argv[4]));
        } else {
            $timeTmp = strtotime(date('Y-m-01 00:00:00'));
            $timeTmp = strtotime('-2 month', $timeTmp);
            $endDate = date('Y-m-d', $timeTmp - 3600);

            $timeTmp   = strtotime('-1 month', $timeTmp);
            $startDate = date('Y-m-d', $timeTmp);
        }

        if (!$startDate || !$endDate) {
            pft_log('show_archive/error', json_encode(['没有需要处理的场次', $startDate, $endDate]));
            return false;
        }

        //获取两个月之前的所有场次数据
        $showModel = new ShowModel();
        $roundList = $showModel->getArchiveRoundList($startDate, $endDate);

        //记录处理的场次数据
        pft_log('show_archive/debug', json_encode(['round_list', $startDate, $endDate, $roundList]));

        if (!$roundList) {
            pft_log('show_archive/error', json_encode(['没有需要处理的场次', $startDate, $endDate]));
        }
        $roundIdList = array_column($roundList, 'id');

        foreach ($roundIdList as $roundId) {
            $res = $showModel->migrationsRoundSeats($roundId);

            if ($res) {
                pft_log('show_archive/debug', json_encode(['res', $roundId, $res]));
            } else {
                pft_log('show_archive/error', json_encode(['res', $roundId, $res]));
            }
        }
    }
}
