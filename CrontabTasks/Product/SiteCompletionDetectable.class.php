<?php
/**
 * php runNew.php Product_SiteCompletionDetectable handel
 * 执行设备型号数据处理
 * User: lanwanhui
 * Date: 2021/5/24
 * Time: 18:47
 */

namespace CrontabTasks\Product;

use Library\Model;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}


class SiteCompletionDetectable
{

    private $Model = null;

    /**
     * 完善站点表可检门票
     * User: lanwanhui
     * Date: 2021/5/24
     */
    public function handel(){

        echo "开始完善站点表数据\n";

        $nowTime = time();

        $this->Model = new Model('PTerminalManager');

        $siteArr = $this->Model->table('pft_site_supervise')->field('id,detectable_pid')->select();
        if (empty($siteArr)) {
            exit("无站点数据\n");
        }

        foreach ($siteArr as $site) {

            if (empty($site['detectable_pid'])) {
                continue;
            }

            $lidArr = explode(',', $site['detectable_pid']);

            $addData = [];

            foreach ($lidArr as $lid) {

                if (empty($lid) || !is_numeric($lid) || $lid < 1) {
                    continue;
                }

                $addData[] = [
                    'site_id'      => $site['id'],
                    'lid'          => $lid,
                    'tids'         => '',
                    'is_del'       => 0,
                    'create_time'  => $nowTime,
                ];
            }

            if (empty($addData)) {
                continue;
            }

            $res = $this->Model->table('pft_site_check_ticket')->addAll($addData);

            if ($res === false) {
                echo "站点id={$site['id']},修改失败,error={$this->Model->getDbError()}\n";
                pft_log('site_completion_detectable', "siteId={$site['id']},修改失败,error={$this->Model->getDbError()}");
            } else {
                echo "站点id={$site['id']},修改成功\n";
            }
        }

        echo "end\n";

    }
}