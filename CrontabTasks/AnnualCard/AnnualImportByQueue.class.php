<?php
declare(ticks=1);
/**
 * @deprecate 这里的实现过于复杂 仅有参考价值
 * 一般导入小数据量参考CrontabTasks/AnnualCard/WuhanAnimalPark.class.php
 * 大数据量改造CrontabTasks/AnnualCard/WuhanAnimalPark.class.php为从redis消费
 * 年卡导入 cli方式 采用队列实现方式
 * 考虑excel过大时候 内存占用过高 执行到最后返回server error
 * 这里把它们转换成csv然后逐行读取放入redis队列
 * 注意有前导零的数据要修改格式 不要漏掉 或者在写入队列的时候根据长度补上相应数量的0
 * 最后开启多个进程消费
 * 入口文件
 * /usr/local/php/bin/php /var/www/html/Service/Crontab/runNew.php AnnualCard_AnnualImportByQueue start
 *
 * 统计导入的成功失败总数据
 * 年卡 statisticsCardTotal
 * 年卡订单 statisticsOrderTotal
 * @notice 根据excel表头的不同更改 handleOrder传入参数的索引或handleCard传入参数的索引
 * 不同产品或供应商需要修改_sid、_pid、_orderName、_orderTel这些类属性
 */

namespace CrontabTasks\AnnualCard;

use Library\Cache\Cache;
use Library\Controller;
use Library\Exception;
use Library\MulityProcessHelper;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class AnnualImportByQueue extends Controller
{
    private $virtual_no_mode = 1; //虚拟卡号生成模式：1-导入的时候即时生成 2-商户后台录卡
    private $card_type = 'physics';// virtual-虚拟卡 physics-物理卡
    private $active_mode = 1; //1-导入不激活的数据 2-导入已激活的数据
    private $excelArray = [
        //南阳年卡导入里面包含手机号 姓名 身份证
        //https://www.tapd.cn/67423817/prong/stories/view/1167423817001121910
        'runNy' => [
            '/tmp/import_annual.csv',
        ],
        'runCrate' => [
            '/tmp/import_annual.csv',//生成年卡订单
        ],
    ];
    private $_memberModel = null;
    private $_memberRegisterBiz = null;
    //private $_pid = 1966879;
    //private $_sid = 27225070;
    //dev
    //private $_pid = 492389;
    //private $_pid = 492406;
    //prod
    //这个pid就是pft_annual_card表的pid
    //具体可以通过商户后台编辑票属性页面接口/r/product_HandleTicket/getTickets返回的data.ticket.product_id获得
    private $_pid = ENV == 'PRODUCTION' ? 2178211 : 501270;//武汉动物园年票
    private $_sid = ENV == 'PRODUCTION' ? 34779789 : 6970;//武汉动物园管理处
    private $_orderName = '武汉动物园年票';
    private $_orderTel = '17386135438';
    private $_logPath = 'annual/AnnualImportByQueue';
    private $_logPathForBase = 'annual/AnnualImportByQueue/base';
    //导入未激活年卡 日志路径 便于失败后日志里的数据重复导入
    private $_create_fail = 'annual/create_fail_queue_consume';
    private $redisClient;
    //已经导入成功的物理卡号--下单触发
    private $success_not_active_order_key = 'annual_import:not_act_order:success';
    //已经导入成功的物理卡号集合--导入卡触发
    private $success_ny_order_key = 'annual_import:ny_order:success';
    private $day = 1;
    //消费进程数目
    private $process_num = 10;
    private $_productMap = [];

    //set_key是所有的待导入物理卡号；用来比对所有和成功的差集 确定失败的物理卡号
    private $set_key = 'annual:import:csvDataForOrderSet';
    //用于导入卡的队列key
    private $q_key_card = 'annual:import:csvForCard';
    //用于导入卡订单的队列key
    private $q_key_order = 'annual:import:csvForCardOrder';
    //子进程上限
    private $maxProcesses = 50;
    //处理卡信息的子进程数量
    private $child;
    //处理订单信息的子进程数量
    private $child2;

    /**
     * 入口文件
     */
    public function start()
    {
        $this->init();
        //第一步 构建用于卡消费队列
        $this->csvDataEnqueueForCard();
        //第二步 创建进程去消费队列里的数据 写入年卡表、套餐表、特权表
        $this->csvDataFillAnnualCard($this->q_key_card);
        //第三步 构建用于订单消费的队列
        $this->csvDataEnqueueForCardOrder();
        //第四步 创建进程去消费队列里的数据
        $this->csvDataFillAnnualOrder($this->q_key_order);
        \Swoole\Event::wait();
        //统计卡处理数据情况
        //$this->statisticsCardTotal();
        //统计卡订单数据处理情况
        // $this->statisticsOrderTotal();
    }

    public function testCard()
    {
        $rds = Cache::getInstance('redis');
        while(1) {
            $data_pop = $rds->rPop($this->q_key_card);//无任务时,阻塞等待
            //brPop($key, $time_out)弹出的是数组需要json_encode下
            //echo "弹出数据：" . $data_pop . PHP_EOL;
            if (!$data_pop) {
                echo "队列里没有数据了" . "\n";
                break;
            }
        }
    }

    public function testOrder()
    {
        $rds = Cache::getInstance('redis');
        while(1) {
            $data_pop = $rds->rPop($this->q_key_order);//无任务时,阻塞等待
            //brPop($key, $time_out)弹出的是数组需要json_encode下
            //echo "弹出数据：" . $data_pop . PHP_EOL;
            if (!$data_pop) {
                echo "队列里没有数据了" . "\n";
                break;
            }
        }
    }

    private function init()
    {
        // install signal handler for dead kids
        pcntl_signal(SIGCHLD, array($this, "sigHandler"));
        set_time_limit(0);
        //队列处理不超时,解决redis报错:read error on connection
        ini_set('default_socket_timeout', -1);
        $this->redisClient = Cache::getInstance('redis');
    }

    public function statisticsCardTotal()
    {
        $this->redisClient = Cache::getInstance('redis');
        //统计处理数据
        $queue = $this->redisClient->lLen($this->q_key_card);
        echo "共导入：" . $this->redisClient->sCard($this->success_ny_order_key) . "条数据到pft_annual_card表,队列里还有" . $queue . "条数据" . PHP_EOL;
        if ($this->card_type == 'physics') {
            //所有的物理号与成功的物理号取差集 找出导入失败的物理卡号 然后重新导入失败的
            $diff = $this->redisClient->sDiff($this->set_key, $this->success_ny_order_key);
            if (count($diff) > 100) {
                pft_log($this->_logPath . '/card_fail', json_encode($diff));
            } else {
                echo '导入失败的物理卡号：' . json_encode($diff) . PHP_EOL;
            }
        }
    }

    public function statisticsOrderTotal()
    {
        $this->redisClient = Cache::getInstance('redis');
        $queue = $this->redisClient->lLen($this->q_key_order);
        echo "共导入：" . $this->redisClient->sCard($this->success_not_active_order_key) . "条订单数据,队列里还有" . $queue . "条数据" . PHP_EOL;
        if ($this->card_type == 'physics') {
            //所有的物理号与成功的物理号取差集 找出导入失败的物理卡号 然后重新导入失败的
            $diff = $this->redisClient->sDiff($this->set_key, $this->success_not_active_order_key);
            if (count($diff) > 100) {
                pft_log($this->_create_fail, json_encode($diff));
            } else {
                echo '导入失败的物理卡号：' . json_encode($diff) . PHP_EOL;
            }
        }
    }

    public function emptyLog()
    {
        $file = date('Y') . '/' . date('m') . '/' . date('d') . '.log';
        $path = [
            '/alidata/log/site/cli/annual/AnnualImportByQueue/' . $file,
            '/alidata/log/site/cli/annual/AnnualImportByQueue/for_annual/' . $file,
            '/alidata/log/site/cli/annual/AnnualImportByQueue/for_order/' . $file,
            '/alidata/log/site/cli/annual/AnnualImportByQueue/for_pack/' . $file,
            '/alidata/log/site/cli/annual/AnnualImportByQueue/for_up/' . $file,
            '/alidata/log/site/cli/annual/create_fail_queue_consume/' . $file,
        ];
        foreach ($path as $item) {
            if (file_exists($item)) {
                file_put_contents($item, '');
            }
        }
    }

    /**
     * 鉴于生成的虚拟卡号每次最多3000 所以导入的时候要控制好数量也为3000
     * 或者如果通过商户后台已经录卡了100w个 则这里获取虚拟卡号的逻辑是随机获取一个改商户该产品下没有使用的
     * 如果有物理卡的就要填充pft_card表
     * 如果年卡有特权信息的 就要处理年卡特权信息
     * <AUTHOR>
     * @Date 2023/6/20 11:03
     * @param $value
     * @return void
     * @throws Exception
     */
    private function handleCardNy($value)
    {
        $total = 1;
        $annualCardModel = new \Model\Product\AnnualCard();
        $packageBiz = new \Business\AnnualCard\PackageManage();
        $virtualNoArr = $this->getVirtualNumber($total, $annualCardModel);
        //取出一个虚拟卡号
        $virtualNo = array_shift($virtualNoArr);
        if ($this->card_type == 'physics') {
            $this->physicsBusiness($value, $virtualNo, $annualCardModel, $packageBiz);
        } else {
            $this->virtualBusiness($value, $virtualNo, $annualCardModel, $packageBiz);
        }
    }

    private function getVirtualNumber($total, $annualCardModel)
    {
        $annualCardBis = new \Business\Product\AnnualCard();
        if ($this->virtual_no_mode == 1) {
            //创建虚拟卡号 每次生成3000 如果数据库中有重复的则进行去重后补充够3000个
            $virtualNoRes = $annualCardBis->createVirtualNo($total);
            if ($virtualNoRes['code'] != 200 || empty($virtualNoRes['data']['success'])) {
                throw new Exception('虚拟卡号生成失败' . __LINE__);
            }
            $virtualNoArr = $virtualNoRes['data']['success'];
        } else {
            //从表里查出
            $options['page'] = 1;
            $options['page_size'] = $total;
            $options['status'] = \Business\Product\AnnualCardConst::STATUS_IN_STOCK;
            $virtualNoRes = $annualCardModel->getAnnualCards($this->_sid, $this->_pid, $options);
            if (empty($virtualNoRes)) {
                throw new Exception('未找到虚拟卡号数据' . __LINE__);
            }
            $virtualNoArr = array_column($virtualNoRes, 'virtual_no');
        }
        return $virtualNoArr;
    }

    /**
     * 物理卡号导入业务
     * 物理卡号需要初始化pft_card 虚拟卡号不需要往这个表写数据
     */
    private function physicsBusiness($value, $virtualNo, $annualCardModel, $packageBiz)
    {
        $origin_physics = $value['p'];
        $physics_no = dechex(intval($origin_physics));
        $card_no = $value['c'];
        if (empty($card_no) || $physics_no == '0') {
            pft_log($this->_logPath, "物理卡号数据有误，跳过--" . __LINE__ . '---' . json_encode($value));
            return;
        }
        if ($this->redisClient->sIsMember($this->success_ny_order_key, $origin_physics)) {
            echo $origin_physics . ':' . '该物理卡号已经导入--' . __LINE__ . PHP_EOL;
            pft_log($this->_logPath, $origin_physics . ':' . '该物理卡号已经导入--' . __LINE__);
            return;
        }
        $time = time();
        $annualCardBase = new \Model\Product\BaseCard();
        $ret = $annualCardBase->createBaseCard($origin_physics, 3, $time);
        if ($ret === false) {
            echo '年卡BaseCard创建失败--' . __LINE__ . PHP_EOL;
            echo json_encode([
                    'sid' => $this->_sid,
                    'physics_no' => $origin_physics,
                    'type' => 3,
                    'add_time' => $time,
                ]) . PHP_EOL;
            pft_log($this->_logPathForBase, json_encode([
                'ac' => __METHOD__,
                'res' => '年卡BaseCard创建失败--' . __LINE__,
                'data' => [
                    'sid' => $this->_sid,
                    'physics_no' => $origin_physics,
                    'type' => 3,
                    'add_time' => $time,
                ],
            ], JSON_UNESCAPED_UNICODE));
        }
        $tmpAnnualCardInfo = [
            'memberid' => 0,
            'physics_no' => $physics_no,
            'card_no' => $card_no,
            'sale_time' => 0,
            'active_time' => 0,
            'update_time' => 0,
            'avalid_begin' => 0,
            'avalid_end' => 0,
        ];
        $this->processAnnualCardByMode($tmpAnnualCardInfo, $virtualNo, $annualCardModel, $packageBiz, $origin_physics);
    }

    /**
     * 虚拟卡号导入业务
     */
    private function virtualBusiness($value, $virtualNo, $annualCardModel, $packageBiz)
    {
        $tmpAnnualCardInfo = [
            'memberid' => 0,
            'physics_no' => 0,
            'card_no' => '',
            'sale_time' => 0,
            'active_time' => 0,
            'update_time' => 0,
            'avalid_begin' => 0,
            'avalid_end' => 0,
            'dname' => '',
            'mobile' => '',
            'id_card_no' => '',
        ];
        //如果是导入已激活的卡信息 则需要给年卡补充会员和额外信息
        if ($this->active_mode == 2) {
            $nameIdx = 0;
            $mobileIdx = 1;
            $idCardIdx = 2;
            $startDateIdx = 3;
            $endDateIdx = 4;
            //用户创建（存在新建用户 否则返回对应用户id）
            $paramsObj = (object)[
                'ordertel' => $value[$mobileIdx],
                'ordername' => $value[$nameIdx],
            ];
            $userObj = new \stdClass();
            $userObj->name = $paramsObj->ordername;
            $userObj->mobile = $paramsObj->ordertel;
            $bindMemberId = $this->_getBindMemberId($userObj);
            if (!$bindMemberId) {
                pft_log($this->_logPath, json_encode([
                    'ac' => __METHOD__,
                    'res' => '用户生成失败' . __LINE__,
                    'paramsObj' => $paramsObj,
                ], JSON_UNESCAPED_UNICODE));
                return;
            }
            //判断门票id 姓名 手机号
            if (!$value[$nameIdx] || !$value[$mobileIdx] || !\Library\Tools\Helpers::isMobile($value[$mobileIdx])) {
                pft_log($this->_logPath, 'empty--' . __LINE__ . ':' . implode('##', $value));
                return;
            }
            //有身份证情况判断下身份证格式
            if ($value[$idCardIdx] && !idcard_checksum18($value[$idCardIdx])) {
                pft_log($this->_logPath, 'id_card_error--' . __LINE__ . ':' . implode('##', $value));
                return;
            }
            //判断年卡有效期格式
            if ((strlen($value[$startDateIdx]) != 8 || strlen($value[$endDateIdx]) != 8) || (!strtotime($value[$startDateIdx]) || !strtotime($value[$endDateIdx])) || (strtotime($value[$endDateIdx]) < strtotime($value[$startDateIdx]))) {
                pft_log($this->_logPath, 'time_error--' . __LINE__ . ':' . implode('##', $value));
                return;
            }
            //激活时间计算
            $activeTime = time();
            $startTime = strtotime(date('Y-m-d 00:00:00', strtotime($value[$startDateIdx])));
            $endTime = strtotime(date('Y-m-d 23:59:59', strtotime($value[$endDateIdx])));
            $tmpAnnualCardInfo['memberid'] = $bindMemberId;
            $tmpAnnualCardInfo['status'] = \Business\Product\AnnualCardConst::STATUS_ACTIVE;
            $tmpAnnualCardInfo['sale_time'] = $activeTime;
            $tmpAnnualCardInfo['active_time'] = $activeTime;
            $tmpAnnualCardInfo['update_time'] = $activeTime;
            $tmpAnnualCardInfo['avalid_begin'] = $startTime;
            $tmpAnnualCardInfo['avalid_end'] = $endTime;
            $tmpAnnualCardInfo['dname'] = $value[$nameIdx];
            $tmpAnnualCardInfo['id_card_no'] = trim($value[$idCardIdx]) ?? '';
            $tmpAnnualCardInfo['mobile'] = $value[$mobileIdx];
            $tmpAnnualCardInfo['annual_status'] = \Business\Product\AnnualCardConst::ANNUAL_STATUS_ACTIVE;
            $tmpAnnualCardInfo['ext_info'] = json_encode($this->_productMap['extInfo'][$this->_pid]);
        } else {
            $mobile = $value[0];
            $id_card_no = $value[1];
            $dname = $value[2];
            //需要校验下手机号 身份证 和 姓名
            if (!\Library\Tools\Helpers::isMobile($mobile) || !$dname || !idcard_checksum18($id_card_no)) {
                pft_log($this->_logPath, '基础信息校验有误，请修复后导入--' . __LINE__ . json_encode($value));
                return;
            }
            if ($this->redisClient->sIsMember($this->success_ny_order_key, $virtualNo)) {
                echo $virtualNo . ':' . '该虚拟卡号已经导入--' . __LINE__ . PHP_EOL;
                pft_log($this->_logPath, $virtualNo . ':' . '该虚拟卡号已经导入--' . __LINE__);
                return;
            }
            $tmpAnnualCardInfo['dname'] = $dname;
            $tmpAnnualCardInfo['id_card_no'] = $id_card_no;
            $tmpAnnualCardInfo['mobile'] = $mobile;
        }
        $this->processAnnualCardByMode($tmpAnnualCardInfo, $virtualNo, $annualCardModel, $packageBiz, $virtualNo);
    }

    private function processAnnualCardByMode($tmpAnnualCardInfo, $virtualNo, $annualCardModel, $packageBiz, $no)
    {
        if ($this->virtual_no_mode == 1) {
            //这种模式属于创建
            $tmpAnnualCardInfo['sid'] = $this->_sid;
            $tmpAnnualCardInfo['pid'] = $this->_pid;
            $tmpAnnualCardInfo['virtual_no'] = $virtualNo;
            $tmpAnnualCardInfo['create_time'] = time();
            $tmpAnnualCardInfo['status'] = $tmpAnnualCardInfo['status'] ?? \Business\Product\AnnualCardConst::STATUS_IN_STOCK;
            //$ins[] = $tmpAnnualCardInfo;
            $cardId = $annualCardModel->createAnnualCardSingle($tmpAnnualCardInfo);
            if ($cardId === false) {
                echo '年卡创建失败----origin_physics_no=' . $no . PHP_EOL;
                pft_log($this->_logPath . '/for_annual', json_encode([
                    'ac' => __METHOD__,
                    'res' => '年卡创建失败---' . __LINE__,
                    'data' => $tmpAnnualCardInfo,
                ], JSON_UNESCAPED_UNICODE));
                return;
            }
            //年卡套餐后续处理
            /*$handlerRes = $packageBiz->packageAfterHandlerForOrder($cardId, $this->_productMap['extInfo'][$this->_pid]['annual_valid_type'], false, $this->_productMap['ticketMap'][$this->_pid]['lid'], $this->_pid,
                $this->_sid, $this->_productMap['ticketMap'][$this->_pid]['tid'], $virtualNo, '', 1, $this->_sid, 0, 0, '年卡导入', $this->_productMap['extInfo'][$this->_pid]);
            if ($handlerRes['code'] != 200) {
                echo '年卡套餐后续处理失败----' . $handlerRes['msg'] . '----cardId=' . $cardId . PHP_EOL;
                pft_log($this->_logPath . '/for_pack', json_encode([
                        'ac' => __METHOD__,
                        'res' => $handlerRes['msg'],
                        'params' => [$no, $this->_productMap['extInfo'][$this->_pid]['annual_valid_type'], false, $this->_productMap['ticketMap'][$this->_pid]['lid'], $this->_pid,
                            $this->_sid, $this->_productMap['ticketMap'][$this->_pid]['tid'], $virtualNo, '', 1, $this->_sid, 0, 0, '年卡导入', $this->_productMap['extInfo'][$this->_pid]],
                    ], JSON_UNESCAPED_UNICODE) . __LINE__);
                return;
            }*/
            $this->redisClient->sAdd($this->success_ny_order_key, $no);
        } else {
            //这种模式属于更新
            $res = $annualCardModel->updateAnnualCardSingle(['virtual_no' => $virtualNo], $tmpAnnualCardInfo);
            if ($res === false) {
                echo '年卡更新失败---' . __LINE__ . PHP_EOL;
                pft_log($this->_logPath . '/for_up', json_encode([
                    'ac' => 'handleCardNy',
                    'res' => '年卡更新失败---' . __LINE__,
                    'data' => $tmpAnnualCardInfo,
                ], JSON_UNESCAPED_UNICODE));
                return;
            }
            $this->redisClient->sAdd($this->success_ny_order_key, $no);
        }
    }

    public function runCreateMulti()
    {
        if (empty($this->excelArray['runCrate'])) {
            exit('没有指明文件');
        }
        pft_log('debug/runCreate', 'start---' . __LINE__);
        $this->redisClient = Cache::getInstance('redis');
        $this->redisClient->setOption(\Redis::OPT_READ_TIMEOUT, -1);
        $startTime = time();
        $task = new MulityProcessHelper($this, $this->process_num, 0);
        $task->runWithCallback(function () {
            $this->csvDataFillAnnualOrder($this->q_key_order);
        });
        $time = time() - $startTime;
        pft_log('debug/runCreate', 'end--' . __LINE__ . '|耗时：' . $time . 's');
        echo '耗时：' . $time;
        return true;
    }

    /**
     * 生成批量下单信息
     */
    private function handleOrder($value)
    {
        echo __METHOD__ . '开始：' . PHP_EOL;
        $annualCardBis = new \Business\Product\AnnualCard();
        $annualCardModel = new \Model\Product\AnnualCard();
        //根据不同业务情况的表头做不同处理（比如有的没有物理卡号只有虚拟卡号）
        //@todo 具体序号以实际excel为准
        if ($this->card_type == 'physics') {
            $origin_physics = $value['p'];
            $physicsNo = dechex(intval($origin_physics));
            $cardNo = $value['c'];
            $mobile = $this->_orderTel;
            $dname = $this->_orderName;
            $id_card_no = '';
            $order_type = 1;
            $cardInfo = $annualCardModel->checkCard('physics_no', $physicsNo, [], 'virtual_no');
            if (empty($cardInfo)) {
                echo __METHOD__ . '未找到card_info=' . $physicsNo . PHP_EOL;
                return;
            }
        } else {
            $mobile = $value[0];
            $id_card_no = $value[1];
            $dname = $value[2];
            $options = [
                'mobile' => $mobile,
                'id_card_no' => $id_card_no,
                'page' => 1,
                'page_size' => 1,
            ];
            $cardInfoRet = $annualCardModel->getAnnualCards($this->_sid, $this->_pid, $options);
            if (empty($cardInfoRet[0])) {

                echo json_encode($value) . ':' . '该年卡信息未找到--' . __LINE__ . PHP_EOL;
                pft_log($this->_logPath . '/for_order', json_encode($value) . ':' . '该年卡信息未找到--' . __LINE__);
                return;
            }
            $cardInfo = $cardInfoRet[0];
            $physicsNo = 0;
            $cardNo = '';
            $order_type = 0;
        }
        //虚拟卡号
        $virtualNo = $cardInfo['virtual_no'];
        $no = $this->card_type == 'physics' ? $origin_physics : $virtualNo;
        //如果redis set里面有这个虚拟卡号 证明已经导入过了 不用重复导入
        $exists = $this->redisClient->sIsMember($this->success_not_active_order_key, $no);
        if ($exists) {
            echo $virtualNo . ':' . '该虚拟卡号已经导入--' . __LINE__ . PHP_EOL;
            pft_log($this->_logPath . '/for_order', $virtualNo . ':' . '该虚拟卡号已经导入--' . __LINE__);
            return;
        }
        $handleData = [
            'aid' => $this->_sid,//上级供应商id
            'order_type' => $order_type, //下单类型，0：虚拟卡购买，1物理卡购买，2年卡续费
            'pid' => $this->_pid,//产品id
            'paymode' => 3,//自供自销
            'mid' => $this->_sid,//下单人
            'memo' => '',
            'ordertel' => $mobile,//联系人手机
            'ordername' => $dname,//联系人姓名
            'ordermode' => 0,//默认平台
            'id_card' => $id_card_no, //身份证号码
            'avatar' => [],
            'virtual_no' => $virtualNo,
            'physics_no' => $physicsNo,//物理卡号
            'card_no' => $cardNo,//实体卡号
            'inactive' => 1,  // 年卡下单是否激活  0激活 1不激活
            'rand_math' => mt_rand(1000, 9999) . $virtualNo, // 添加一个随机数，避免批量年卡下单枷锁
            'card_type' => $this->card_type,
        ];
        $result = $annualCardBis->orderForCard($this->_sid, (object)$handleData);
        if ($result['code'] != 200) {
            echo $result['msg'] . PHP_EOL;
            echo json_encode($handleData, JSON_UNESCAPED_UNICODE) . '--' . __LINE__ . PHP_EOL;
            pft_log($this->_logPath . '/for_order', json_encode($handleData, JSON_UNESCAPED_UNICODE) . '--' . __LINE__);
        } else {
            //记录处理成功的 防止重放的时候重复插入数据到表
            $this->redisClient->sAdd($this->success_not_active_order_key, $no);
        }
    }

    /**
     * 将大的excel文件另存为csv逗号分隔utf8格式的文件
     * 通过fgetcsv进行逐行读取 防止内存占用过大
     * 读取的数据放入队列
     * 然后开多个消费端消费
     * <AUTHOR>
     * @Date 2023/6/20 18:19
     * @return void
     */
    private function csvDataEnqueueForCard()
    {
        echo __METHOD__ . '开始：' . PHP_EOL;
        if (empty($this->excelArray['runNy'])) {
            exit('没有指明文件');
        }
        $time = time();
        $redis_key = $this->q_key_card;
        $this->redisClient = Cache::getInstance('redis');
        $this->redisClient->setOption(\Redis::OPT_READ_TIMEOUT, -1);
        $total = 0;
        foreach ($this->excelArray['runNy'] as $path) {
            $handle = fopen($path, "r");
            if ($handle === false) {
                exit('获取文件句柄错误');
            }
            $i = 0;
            while (($data = fgetcsv($handle)) !== FALSE) {
                $i++;
                if ($i < 2) {
                    continue;
                }
                //写入到队列
                //0-physics_no 1-card_no
                if ($this->card_type == 'physics') {
                    //p-physics_no c-card_no
                    $physics = false !== strpos($data[0], 'WD') ? substr($data[0], 2) : $data[0];
                    $this->redisClient->lPush($redis_key, json_encode(['p' => $physics, 'c' => $data[1]]));
                } else {
                    $mobile = $data[0];
                    $id_card_no = $data[1];
                    $name = $data[2];
                    //需要校验下手机号 身份证 和 姓名
                    if (!\Library\Tools\Helpers::isMobile($mobile) || !$name || !idcard_checksum18($id_card_no)) {
                        pft_log($this->_logPath, '基础信息校验有误，请修复后导入--' . __LINE__ . json_encode($data));
                        continue;
                    }
                    $this->redisClient->lPush($redis_key, json_encode(['m' => $mobile, 'i' => $id_card_no, 'n' => $name]));
                }
                //测试下是否有重复的物理卡号
                $total++;
            }
            fclose($handle);
        }
        $this->redisClient->expire($redis_key, $this->day * 24 * 3600);
        echo __METHOD__ . "共导入" . $total . "条数据，共耗时：" . (time() - $time) . 's' . PHP_EOL;
    }

    private function csvDataEnqueueForCardOrder()
    {
        echo __METHOD__ . '开始：' . PHP_EOL;
        if (empty($this->excelArray['runCrate'])) {
            exit('没有指明文件');
        }
        $time = time();
        $redis_key = $this->q_key_order;
        $this->redisClient = Cache::getInstance('redis');
        $this->redisClient->setOption(\Redis::OPT_READ_TIMEOUT, -1);
        $total = 0;
        $count = 0;
        foreach ($this->excelArray['runCrate'] as $path) {
            $handle = fopen($path, "r");
            if ($handle === false) {
                exit('获取文件句柄错误');
            }
            $i = 0;
            while (($data = fgetcsv($handle)) !== FALSE) {
                $i++;
                if ($i < 2) {
                    continue;
                }
                //写入到队列
                if ($this->card_type == 'physics') {
                    //0-physics_no 1-card_no
                    //p-physics_no c-card_no
                    $physics = false !== strpos($data[0], 'WD') ? substr($data[0], 2) : $data[0];
                    $this->redisClient->lPush($redis_key, json_encode(['p' => $physics, 'c' => $data[1]]));
                    $this->redisClient->sAdd($this->set_key, $data[0]);
                } else {
                    $mobile = $data[0];
                    $id_card_no = $data[1];
                    $name = $data[2];
                    //需要校验下手机号 身份证 和 姓名
                    if (!\Library\Tools\Helpers::isMobile($mobile) || !$name || !idcard_checksum18($id_card_no)) {
                        pft_log($this->_logPath, '基础信息校验有误，请修复后导入--' . __LINE__ . json_encode($data));
                        continue;
                    }
                    $this->redisClient->lPush($redis_key, json_encode(['m' => $mobile, 'i' => $id_card_no, 'n' => $name]));
                }
                $total++;
            }
            fclose($handle);
        }
        $this->redisClient->expire($redis_key, $this->day * 24 * 3600);
        if ($this->redisClient->exists($this->set_key)) {
            $this->redisClient->expire($this->set_key, $this->day * 24 * 3600);
        }
        echo "共导入" . $total . "条数据，共耗时：" . (time() - $time) . 's' . PHP_EOL;
    }

    private function csvDataFillAnnualCard($redis_key)
    {
        echo __METHOD__ . '开始：' . PHP_EOL;
        $javaApi = new \Business\CommodityCenter\Ticket();
        try {
            //@todo 需要确认下产品id
            $ticket = $javaApi->queryTicketInfoByProductIds([$this->_pid]);
            if (empty($ticket)) {
                echo '年卡特权票信息获取失败--' . __LINE__ . PHP_EOL;
                pft_log('debug/csvDataFillAnnualCard', '年卡特权票信息获取失败--' . __LINE__);
                return false;
            }
        } catch (\Exception $e) {
            echo $e->getMessage();
            pft_log('debug/csvDataFillAnnualCard', '年卡套餐信息获取失败--' . __LINE__);
            return false;
        }
        $this->_mapJavaTicketFields($ticket);
        //$redis_key = $this->q_key_card;
        while (true) {
            try {
                if ($this->child < $this->maxProcesses) {
                    $rds = Cache::getInstance('redis');
                    $data_pop = $rds->rPop($redis_key);//无任务时,阻塞等待
                    //brPop($key, $time_out)弹出的是数组需要json_encode下
                    //echo "弹出数据：" . $data_pop . PHP_EOL;
                    if (!$data_pop) {
                        echo "队列里没有数据了" . "\n";
                        break;
                    }
                    echo "生成新的子进程 现在有 $this->child 个子进程\n";
                    $this->child++;
                    $process = new \Swoole\Process(function (\swoole_process $worker) {
                        echo __METHOD__ . '开始：' . PHP_EOL;
                        $GLOBALS['worker'] = $worker;
                        \Swoole\Event::add($worker->pipe, function ($pipe) {
                            $worker = $GLOBALS['worker'];
                            $recv = $worker->read(); //send data to master
                            sleep(rand(1, 3));
                            //echo "From Master: $recv\n";
                            $arr = json_decode($recv, true);
                            if (json_last_error() !== JSON_ERROR_NONE) {
                                echo '消费信息json_decode出错: ' . json_last_error_msg() . '===data===' . $recv . '---' . __LINE__ . PHP_EOL;
                                pft_log($this->_logPath . '/for_card', '消费信息json_decode出错: ' . json_last_error_msg() . '===data===' . $recv . '---' . __LINE__);
                                $worker->exit(0);
                            }
                            //不同导入的表头数量不一样 比如物理卡号导入可能是2个(物理卡号、实体卡号) 虚拟卡号是3个（手机、身份证、姓名） 这个根据具体情况修改数量
                            $cnt = $this->card_type == 'physics' ? 2 : 3;
                            if (count($arr) != $cnt) {
                                echo '消费信息有误: ' . $recv . '---' . __LINE__ . PHP_EOL;
                                pft_log($this->_logPath . '/for_card', '消费信息有误: ' . $recv . '---' . __LINE__);
                                $worker->exit(0);
                            }
                            //子进程业务处理逻辑
                            try {
                                $this->handleCardNy($arr);
                            } catch (Exception $e) {
                                echo $e->getMessage();
                            }
                            $worker->exit(0);
                        });
                        exit;
                    });
                    $process->write($data_pop);
                    $pid = $process->start();
                }
            } catch (\Throwable $e) {
                echo $e->getMessage();
            }
        }
    }

    private function csvDataFillAnnualOrder($redis_key)
    {
        echo __METHOD__ . '开始：' . PHP_EOL;
        while (true) {
            try {
                if ($this->child2 < $this->maxProcesses) {
                    $rds = Cache::getInstance('redis');
                    $data_pop = $rds->rPop($redis_key);//无任务时,阻塞等待
                    //brPop($key, $time_out)弹出的是数组需要json_encode下
                    //echo "弹出数据：" . $data_pop . PHP_EOL;
                    if (!$data_pop) {
                        echo "队列里没有数据了" . "\n";
                        break;
                    }
                    echo "生成新的子进程 现在有 $this->child2 个子进程\n";
                    $this->child2++;
                    $process = new \Swoole\Process(function (\swoole_process $worker) {
                        echo __METHOD__ . '开始：' . PHP_EOL;
                        $GLOBALS['worker2'] = $worker;
                        \Swoole\Event::add($worker->pipe, function ($pipe) {
                            $worker = $GLOBALS['worker2'];
                            $recv = $worker->read(); //send data to master
                            sleep(rand(1, 3));
                            //echo "From Master: $recv\n";
                            $arr = json_decode($recv, true);
                            if (json_last_error() !== JSON_ERROR_NONE) {
                                echo 'processOrder消费信息json_decode出错: ' . json_last_error_msg() . '===data===' . $recv . '---' . __LINE__ . PHP_EOL;
                                pft_log($this->_logPath . '/for_order', 'processOrder消费信息json_decode出错: ' . json_last_error_msg() . '===data===' . $recv . '---' . __LINE__);
                                $worker->exit(0);
                            }
                            //不同导入的表头数量不一样 比如物理卡号导入可能是2个(物理卡号、实体卡号) 虚拟卡号是3个（手机、身份证、姓名） 这个根据具体情况修改数量
                            $cnt = $this->card_type == 'physics' ? 2 : 3;
                            if (count($arr) != $cnt) {
                                echo 'processOrder消费信息有误: ' . $recv . '---' . __LINE__ . PHP_EOL;
                                pft_log($this->_logPath . '/for_order', 'processOrder消费信息有误: ' . $recv . '---' . __LINE__);
                                $worker->exit(0);
                            }
                            //子进程业务处理逻辑
                            $this->handleOrder($arr);
                            $worker->exit(0);
                        });
                        exit();
                    });
                    $process->write($data_pop);
                    $pid = $process->start();
                }
            } catch (\Throwable $e) {
                echo $e->getMessage();
            }
        }
    }

    private function _mapJavaTicketFields(array $ticket)
    {
        //组装产品信息
        $ticketMap = [];
        $extInfo = [];
        foreach ($ticket as $item) {
            $ticketMap[$item['ticket']['pid']] = [
                'tid' => $item['ticket']['id'],
                'lid' => $item['land']['id'],
                'pid' => $item['product']['id'],
                'sid' => $item['ticket']['apply_did'],
            ];
            $extInfo[$item['ticket']['pid']] = [
                'available_time_period' => $item['ext']['available_time_period'] ?? '',
                'annual_valid_type' => $item['ext']['annual_valid_type'] ?? 0,
                'annual_valid_day' => $item['ext']['annual_valid_day'] ?? 30,
                'annual_valid_start' => $item['ext']['annual_valid_start'] ?? '',
                'annual_valid_end' => $item['ext']['annual_valid_end'] ?? '',
            ];
        }

        $this->_productMap = [
            'ticketMap' => $ticketMap,
            'extInfo' => $extInfo,
        ];
    }

    private function sigHandler($signo)
    {
        //echo "Recive: $signo \r\n";
        switch ($signo) {
            case SIGCHLD:
                while ($ret = \Swoole\Process::wait(false)) {
                    //echo "PID={$ret['pid']}\n";
                    $this->child--;
                }
        }
    }

    /**
     * 注册用户
     * @param \stdClass $userObj
     *
     * @return int|mixed
     * @throws \Exception
     */
    public function _getBindMemberId(\stdClass $userObj)
    {
        $name = $userObj->name;
        $mobile = $userObj->mobile;
        $avatar = $userObj->avatar ?: '';
        $idCard = $userObj->idCard ?: '';
        $address = $userObj->address ?: '';
        $province = (int)$userObj->province;
        $city = (int)$userObj->city;
        //散客角色是否存在，不存在则创建
        $memberId = 0;
        $customerId = $this->parseCustomerIdByMobile($mobile);
        if ($customerId) {
            //角色是否存在
            $role = $this->getMemberModel()->getTheRoleInfo($customerId, \Library\Constants\MemberConst::ROLE_TOURIST, 'id');
            if ($role) {
                $memberId = $role['id'];
            }
        }
        //生成新账户
        if (!$memberId) {
            $request = [
                'name' => $name,
                'type' => \Library\Constants\MemberConst::ROLE_TOURIST,
                'avatar' => $avatar,
                'passwd' => substr($mobile, 5),
                'address' => $address,
                'id_card' => $idCard,
                'city' => $city,
                'province' => $province,
                'identifier' => $mobile,
                'customer_id' => $customerId,
                'info_source' => \Library\Constants\MemberConst::INFO_MOBILE,
                'page_source' => \Library\Constants\MemberConst::PAGE_PLATFORM,
            ];
            $registerRes = $this->getMemberRegisterBiz()->register((object)$request);
            if ($registerRes['code'] == 200) {
                $memberId = $registerRes['data']['member_id'];
            }
        }
        return $memberId;
    }

    /**
     * 获取customer_id
     * @param string $mobile 手机号
     * @return int|mixed
     * @throws \Exception
     */
    public function parseCustomerIdByMobile($mobile)
    {
        if (!$mobile) {
            return 0;
        }
        //先查角色表
        $role = $this->getMemberModel()->getMemberInfo($mobile, 'mobile', 'customer_id');
        if ($role) {
            return $role['customer_id'];
        }
        //再查客户表
        $customer = $this->getMemberModel()->getCustomerInfo($mobile, 'mobile', 'customer_id');
        if ($customer) {
            return $customer['customer_id'];
        }
        return 0;
    }

    private function getMemberModel()
    {
        if (!$this->_memberModel) {
            $this->_memberModel = new \Model\Member\Member;
        }
        return $this->_memberModel;
    }

    private function getMemberRegisterBiz()
    {
        if (!$this->_memberRegisterBiz) {
            $this->_memberRegisterBiz = new \Business\Member\RegisterAction;
        }
        return $this->_memberRegisterBiz;
    }
}
