<?php
/**
 * 悠游兰州
 * 景区：新都65161852，年卡需要新增/删除产品权益
 */

namespace CrontabTasks\AnnualCard;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class Yylz
{
    private $excel = '/tmp/yylz.csv';
    //private $delGroup = [7315, 6518, 11761, 11762, 11775, 11913, 13717, 13718, 7654, 11757, 11758, 11773, 11911, 13715, 13718, 7651];
    private $addGroup = [
        'supplier_aid' => 10117203,
        'use_limit_json' => '{"all": 1, "day": 1, "month": 1}',
        'use_time_limit' => '{"type": 0, "end_time": "", "begin_time": ""}',
    ];
    //由于不同套餐对应不同的group_id, 写入的时候需要区分下 这里的key为pft_annual_card_period_package的tid
    //也是年卡产品票属性编辑页面地址栏的prod_id
    private $groupMap = [
        //悠游兰州（省内游客）
        901224 => [
            [
                'group_id' => 17422,
                'group_name' => '黄河文化博物馆-黄河文化博物馆',
                //pft_annual_card_privilege_relation里的tid
                'pri_tid' => 2107767,
            ],
            [
                'group_id' => 17423,
                'group_name' => '刘家峡恐龙国家地质公园-刘家峡恐龙国家地质公园',
                //pft_annual_card_privilege_relation里的tid
                'pri_tid' => 2107952,
            ]
        ],
        //悠游兰州（省外游客）
        901225 => [
            [
                'group_id' => 17424,
                'group_name' => '黄河文化博物馆-黄河文化博物馆',
                //pft_annual_card_privilege_relation里的tid
                'pri_tid' => 2107767,
            ],
            [
                'group_id' => 17425,
                'group_name' => '刘家峡恐龙国家地质公园-刘家峡恐龙国家地质公园',
                //pft_annual_card_privilege_relation里的tid
                'pri_tid' => 2107952,
            ]
        ],
        //团购年卡
        924955 => [
            [
                'group_id' => 17426,
                'group_name' => '黄河文化博物馆-黄河文化博物馆',
                //pft_annual_card_privilege_relation里的tid
                'pri_tid' => 2107767,
            ],
            [
                'group_id' => 17427,
                'group_name' => '刘家峡恐龙国家地质公园-刘家峡恐龙国家地质公园',
                //pft_annual_card_privilege_relation里的tid
                'pri_tid' => 2107952,
            ],
        ],
    ];
    private $aid = 10117203;

    private $logPath = 'yylz/annual';

    //测试参数
    /*
    private $delGroup = [2553, 2555, 2557, 2558];
    private $addGroup = [
        'supplier_aid' => 3385,
        'use_limit_json' => '{"all": 1, "day": 1, "month": 1}',
        'use_time_limit' => '{"type": 0, "end_time": "", "begin_time": ""}',
    ];
     private $groupMap = [
        //悠游兰州（省内游客）
        498850 => [
            [
                'group_id' => 2559,
                'group_name' => 'lmm-杨家溪-票1',
                //pft_annual_card_privilege_relation里的tid
                'pri_tid' => 477761,
            ],
            [
                'group_id' => 2260,
                'group_name' => 'lmm-杨家溪-票2',
                //pft_annual_card_privilege_relation里的tid
                'pri_tid' => 477762,
            ]
        ],
        //悠游兰州（省外游客）
        498851 => [
            [
                'group_id' => 2561,
                'group_name' => 'lmm-杨家溪-票1',
                //pft_annual_card_privilege_relation里的tid
                'pri_tid' => 477761,
            ],
            [
                'group_id' => 2562,
                'group_name' => 'lmm-杨家溪-票2',
                //pft_annual_card_privilege_relation里的tid
                'pri_tid' => 477762,
            ]
        ],
        498852 => [
            [
                'group_id' => 2563,
                'group_name' => 'lmm-杨家溪-票2',
                //pft_annual_card_privilege_relation里的tid
                'pri_tid' => 477762,
            ]
        ],
    ];
    private $aid = 3385;*/

    #/usr/local/php/bin/php /var/www/html/Service/Crontab/runNew.php AnnualCard_Yylz run
    public function run()
    {
        //解析出所有虚拟卡号
        $data = $this->parseCsvData();
        $chunks = array_chunk($data, 1000);
        $cardIds = [];
        $lib = new \Library\Model('pft_business_case');
        $createTime = time();
        $addPrivilegeNums = 0;
        $j = 0;
        foreach ($chunks as $chunk) {
            $j++;
            $cards = $lib->table('pft_annual_card')
                ->where(['virtual_no' => ['in', $chunk]])
                ->field('id,virtual_no')
                ->select();
            if (empty($cards)) {
                echo "chunk" . $j . "未找到年卡\n";
                echo $lib->table('pft_annual_card')->getLastSql() . "\n";
                pft_log($this->logPath, "chunk" . $j . "未找到年卡:" . $lib->table('pft_annual_card')->getLastSql());
                continue;
            }
            //$cards = array_column($cards, null, 'virtual_no');
            $cardIds = array_merge($cardIds, array_column($cards, 'id'));
            //查询套餐 考虑有续费情况 需要查出state为1和2的套餐
            $packages = $lib->table('pft_annual_card_period_package')
                ->where([
                    'sid' => $this->aid,
                    'virtual_no' => ['in', $chunk],
                    'state' => ['in', [1, 2]]
                ])
                ->field('id,card_id,tid,state')
                ->select();
            if (empty($packages)) {
                echo "chunk" . $j . "未找到套餐\n";
                echo $lib->table('pft_annual_card_period_package')->getLastSql() . "\n";
                pft_log($this->logPath, "chunk" . $j . "未找到套餐:" . $lib->table('pft_annual_card_period_package')->getLastSql());
                continue;
            }
            $insPrivileges = [];
            foreach ($packages as $package) {
                if (!isset($this->groupMap[$package['tid']])) {
                    echo "套餐数据未匹配到，请确认\n";
                    echo $lib->table('pft_annual_card_period_package')->getLastSql() . "\n";
                    pft_log($this->logPath, "套餐数据未匹配到，请确认:" . $lib->table('pft_annual_card_period_package')->getLastSql());
                    continue;
                }
                foreach ($this->groupMap[$package['tid']] as $value) {
                    //这里需要填充两个特权票
                    $insPrivileges[] = [
                        'card_id' => $package['card_id'],
                        'tid' => $value['pri_tid'],
                        'aid' => $this->aid,
                        'use_limit_json' => $this->addGroup['use_limit_json'],
                        'use_recode_json' => json_encode([]),
                        'state' => 2,
                        'group_id' => $value['group_id'],
                        'group_name' => $value['group_name'],
                        'supplier_aid' => $this->addGroup['supplier_aid'],
                        'create_time' => $createTime,
                        'package_id' => $package['id'],
                        //夜游卡特权可用时间段
                        'use_time_limit' => $this->addGroup['use_time_limit']
                    ];
                }
            }
            $res = $lib->table('pft_annual_card_package_privilege')->addAll($insPrivileges);
            $tc = $res ? count($insPrivileges) : 0;
            $addPrivilegeNums += $tc;
            echo "chunk" . $j . "新增特权数量" . $tc . "\n";
            pft_log($this->logPath, "chunk" . $j . "新增特权数量" . $tc);
        }
        //$chunkIds = array_chunk($cardIds, 500);
        //作废旧套餐(不用)
        //作废旧特权
        /*$discardPrivilegeNums = 0;
        $i = 0;
        foreach ($chunkIds as $chunk) {
            $i++;
            $discardPrivilegeNums += $lib->table('pft_annual_card_package_privilege')
                ->where([
                    'card_id' => ['in', $chunk],
                    'aid' => $this->aid,
                    'group_id' => ['in', $this->delGroup]
                ])
                ->data(['state' => 5])->save();
            echo "chunk" . $i . " 失效特权数据量：" . $discardPrivilegeNums . "\n";
        }*/
        echo "总新增特权数据量：" . $addPrivilegeNums . "\n";
        pft_log($this->logPath, "总新增特权数据量：" . $addPrivilegeNums);
        //echo "总失效特权数据量：" . $discardPrivilegeNums . "\n";
    }

    public function parseCsvData()
    {
        $ret = [];
        $handle = fopen($this->excel, "r");
        if ($handle === false) {
            exit('获取文件句柄错误');
        }
        $i = 0;
        $count = 0;
        while (($data = fgetcsv($handle)) !== FALSE) {
            $i++;
            if ($i < 2) {
                continue;
            }
            //写入到队列
            $virtualNo = $data[0];
            if (!empty($virtualNo)) {
                $ret[] = $virtualNo;
                $count++;
            }
        }
        fclose($handle);
        echo "读取到" . $count . "条数据" . PHP_EOL;
        return $ret;
    }
}
