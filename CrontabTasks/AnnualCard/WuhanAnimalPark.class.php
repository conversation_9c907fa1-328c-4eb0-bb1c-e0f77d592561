<?php
/**
 * 武汉动物园年卡
 * 物理卡导入
 * https://www.tapd.cn/67423817/prong/stories/view/1167423817001141286
 */

namespace CrontabTasks\AnnualCard;

use Library\MulityProcessHelper;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class WuhanAnimalPark
{
    private $importMode = 2;//1-只导入实体卡库存 2-导入成已售不激活
    private $excel = '/tmp/import_annual.csv';
    private $_pid;//武汉动物园年票
    private $_sid;//武汉动物园管理处
    private $_orderName = '武汉动物园年票';
    private $_orderTel = '17386135438';
    private $_logPath = 'annual/wuhanAnimal';
    private $card_type = 'physics';// virtual-虚拟卡 physics-物理卡

    #/usr/local/php/bin/php /var/www/html/Service/Crontab/runNew.php AnnualCard_WuhanAnimalPark run

    public function run()
    {
        //这个pid就是pft_annual_card表的pid
        //具体可以通过商户后台编辑票属性页面接口/r/product_HandleTicket/getTickets返回的data.ticket.product_id获得
        if (ENV == 'PRODUCTION' || ENV == 'GRAY') {
            $this->_sid = 34779789;//武汉动物园管理处
            $this->_pid = 2178211;//武汉动物园管理处
        } else {
            $this->_sid = 6970;
            $this->_pid = 501270;
        }
        //解析csv数据
        $data = $this->parseCsvData();
        //处理物理卡业务
        defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
        //如果有9998条数据 就是：开启min(100, 总数据量9998/dynamicParam50)=100个子进程，每个进程处理数据量ceil(总数据量9998/子进程数量100)=100
        $task = new MulityProcessHelper($this, 100, 50);
        $task->run($data);
        return true;
    }

    public function runWorker($chunk)
    {
        //pft_log('annual/wuhan', "收到要执行的slice:" . json_encode($chunk));
        $total = count($chunk);
        //创建虚拟卡号 每次生成$total 如果数据库中有重复的则进行去重后补充够3000个
        $annualCardBis = new \Business\Product\AnnualCard();
        $virtualNoRes = $annualCardBis->createVirtualNo($total);
        if ($virtualNoRes['code'] != 200 || empty($virtualNoRes['data']['success'])) {
            echo "虚拟卡号生成失败\n";
            return false;
        }
        $virtualNoArr = $virtualNoRes['data']['success'];
        $annualCardModel = new \Model\Product\AnnualCard();
        $annualCardBase = new \Model\Product\BaseCard();
        $res = [];
        foreach ($chunk as $value) {
            $origin_physics = $value['p'];
            $physics_no = dechex(intval($origin_physics));
            $card_no = $value['c'];
            if (empty($card_no) || $physics_no == '0') {
                echo "物理卡号数据有误，跳过: " . json_encode($value) . "\n";
                pft_log($this->_logPath, "物理卡号数据有误，跳过: " . json_encode($value));
                continue;
            }
            $time = time();
            //==========================创建基础卡================================//
            $ret = $annualCardBase->createBaseCard($origin_physics, 3, $time, $this->_sid);
            if ($ret === false) {
                echo '年卡BaseCard创建失败 可能原因：物理ID唯一索引拦截，origin_physics='.$origin_physics . __LINE__ . PHP_EOL;
                pft_log($this->_logPath, json_encode([
                    'ac' => __METHOD__,
                    'res' => '年卡BaseCard创建失败 可能原因：物理ID唯一索引拦截,跳过',
                    'sid' => $this->_sid,
                    'physics_no' => $origin_physics
                ], JSON_UNESCAPED_UNICODE));
                continue;
            }
            //如果生成的虚拟卡号分配完了 需要重新生成下
            if (empty($virtualNoArr)) {
                $virtualNoRes = $annualCardBis->createVirtualNo($total);
                if ($virtualNoRes['code'] != 200) {
                    echo '虚拟卡号生成失败' . "\n";
                    return false;
                }
                $virtualNoArr = $virtualNoRes['data']['success'];
            }
            $virtualNo = array_shift($virtualNoArr);
            $tmpAnnualCardInfo = [
                'sid' => $this->_sid,
                'pid' => $this->_pid,
                'memberid' => 0,
                'virtual_no' => $virtualNo,
                'physics_no' => $physics_no,
                'card_no' => $card_no,
                'sale_time' => 0,
                'active_time' => 0,
                'update_time' => 0,
                'avalid_begin' => 0,
                'avalid_end' => 0,
                'create_time' => time(),
                'status' => \Business\Product\AnnualCardConst::STATUS_IN_STOCK,
            ];
            //========================创建年卡和套餐特权数据========================//
            $cardId = $annualCardModel->createAnnualCardSingle($tmpAnnualCardInfo);
            if ($cardId === false) {
                echo '年卡创建失败 origin_physics_no=' . $origin_physics . PHP_EOL;
                pft_log($this->_logPath . '/for_annual', json_encode([
                    'ac' => __METHOD__,
                    'res' => '年卡创建失败',
                    'data' => $tmpAnnualCardInfo,
                ], JSON_UNESCAPED_UNICODE));
                return false;
            }
            //==========================创建年卡订单==============================//
            //如果只导入物理卡的库存则不用下单  导入库存的年卡都是status=3库存中 下单后会变为：不激活status=0待激活 激活status=1
            if ($this->importMode == 2) {
                $mobile = $this->_orderTel;
                $dname = $this->_orderName;
                $id_card_no = '';
                $cardInfo = $annualCardModel->checkCard('physics_no', $physics_no, [], 'virtual_no');
                if (empty($cardInfo)) {
                    echo __METHOD__ . '未找到物理卡：' . $physics_no . PHP_EOL;
                    return false;
                }
                $handleData = [
                    'aid' => $this->_sid,//上级供应商id
                    'order_type' => 1, //下单类型，0：虚拟卡购买，1物理卡购买，2年卡续费
                    'pid' => $this->_pid,//产品id
                    'paymode' => 3,//自供自销
                    'mid' => $this->_sid,//下单人
                    'memo' => '',
                    'ordertel' => $mobile,//联系人手机
                    'ordername' => $dname,//联系人姓名
                    'ordermode' => 0,//默认平台
                    'id_card' => $id_card_no, //身份证号码
                    'avatar' => [],
                    'virtual_no' => $virtualNo,
                    'physics_no' => $physics_no,//物理卡号
                    'card_no' => $card_no,//实体卡号
                    'inactive' => 1,  // 年卡下单是否激活  0激活 1不激活
                    'rand_math' => mt_rand(1000, 9999) . $virtualNo, // 添加一个随机数，避免批量年卡下单枷锁
                    'card_type' => $this->card_type,
                ];
                //这里会处理后续套餐
                $result = $annualCardBis->orderForCard($this->_sid, (object)$handleData);
                pft_log($this->_logPath . '/result', json_encode($res));
                if ($result['code'] != 200) {
                    echo $result['msg'] . PHP_EOL;
                    echo json_encode($handleData, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                    pft_log($this->_logPath . '/for_order', json_encode($handleData, JSON_UNESCAPED_UNICODE) . '--' . __LINE__);
                }
            }
        }
    }

    public function parseCsvData()
    {
        $ret = [];
        $handle = fopen($this->excel, "r");
        if ($handle === false) {
            exit('获取文件句柄错误');
        }
        $i = 0;
        $count = 0;
        //$redis = Cache::getInstance('redis');
        //$redis->setOption(\Redis::OPT_READ_TIMEOUT, -1);
        while (($data = fgetcsv($handle)) !== FALSE) {
            $i++;
            if ($i < 2) {
                continue;
            }
            //写入到队列
            $physics = false !== strpos($data[0], 'WD') ? substr($data[0], 2) : $data[0];
            $physicsNo = $physics;
            $cardNo = $data[1];
            if (!empty($physicsNo)) {
                $ret[] = ['p' => $physics, 'c' => $cardNo];
                //$redis->lPush($this->redisKey, json_encode(['p' => $physics, 'c' => $cardNo]));
                $count++;
            }
        }
        fclose($handle);
        echo "读取到" . $count . "行数据" . PHP_EOL;
        return $ret;
    }
}
