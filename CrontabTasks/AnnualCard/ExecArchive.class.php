<?php
/**
 * 年卡失效套餐和特权数据实时归档
 */

namespace CrontabTasks\AnnualCard;
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class ExecArchive
{
    private $packageTable = 'pft_annual_card_period_package';
    private $privilegeTable = 'pft_annual_card_package_privilege';
    private $packageFields = [
        'id', 'card_id', 'lid', 'pid',
        'sid', 'tid', 'virtual_no', 'state',
        'start_time', 'end_time', 'create_time', 'update_time',
        'ordernum', 'type', 'operator_id', 'remark',
        'ext_info', 'delay_enable'
    ];
    private $privilegeFields = [
        'id', 'card_id', 'tid', 'aid',
        'use_limit_json', 'use_recode_json', 'state', 'group_id',
        'group_name', 'supplier_aid', 'create_time', 'update_time',
        'package_id', 'use_time_limit',
    ];
    //套餐分页限制
    private $packageLimit = 10000;
    //失效特权分页限制
    private $invalidPrivilegeLimit = 10000;
    //循环执行间隔秒数
    private $sleep = 6;
    //每次处理插入删除数据量
    private $chunkSize = 500;
    private $logPathErr = 'annual/archive';

    #/usr/local/php/bin/php /var/www/html/Service/Crontab/runNew.php AnnualCard_ExecArchive run
    public function run()
    {
        $start = time();
        $lib = new \Library\Model('pft_business_case');
        $config = load_config('time_type', 'annualSearch');
        $et = !empty($config[0][0]) ? strtotime($config[0][0]) : strtotime('-1 year');
        if ($et > time()) {
            $et = strtotime('-1 year');
        }
        //查询失效套餐
        $where1['end_time'] = [['GT', 0], ['LT', $et]];
        //由于年卡套餐归档后会影响年卡退已激活一期需求中的多套餐不可退的判断逻辑
        //这里暂时不归档这种的套餐，只归档失效年卡下的套餐
        /*$where2['end_time'] = ['GT', time()];
        $where2['state'] = ['in', [3, 4, 5]];
        $packages1 = $lib->table($this->packageTable)
            ->where($where1)
            ->limit($this->packageLimit)
            ->select();
        $packages2 = $lib->table($this->packageTable)
            ->where($where2)
            ->limit($this->packageLimit)
            ->select();
        $packages = array_merge($packages1, $packages2);*/
        $count = $lib->table($this->packageTable)->where($where1)->count();
        if (!$count) {
            echo '暂时没有要归档的数据' . PHP_EOL;
            pft_log($this->logPathErr, '暂时没有要归档的数据,本次归档完成');
            return;
        }
        if ($count > $this->packageLimit) {
            $pages = ceil($count / $this->packageLimit);
            for ($i = 1; $i <= $pages; $i++) {
                $st = time();
                $packages = $lib->table($this->packageTable)
                    ->where($where1)
                    ->page($i, $this->packageLimit)
                    ->select();
                $this->execArchive($lib, $packages);
                $cost = time() - $st;
                pft_log($this->logPathErr, "第 $i 次归档完成，执行耗时 $cost 秒，等待 $this->sleep 秒后进行下次归档");
                sleep($this->sleep);
            }
            $cost = time() - $start;
            pft_log($this->logPathErr, "本次归档完成，执行耗时 $cost 秒，含睡眠时间" . $pages * $this->sleep . '秒');
        } else {
            $packages = $lib->table($this->packageTable)
                ->where($where1)
                ->limit($this->packageLimit)
                ->select();
            $this->execArchive($lib, $packages);
            $cost = time() - $start;
            pft_log($this->logPathErr, "本次归档完成，执行耗时 $cost 秒");
        }
    }

    public function execArchive($lib, $packages)
    {
        pft_log($this->logPathErr, '开始执行,本次套餐数据量：' . count($packages));
        //var_dump($packages);
        try {
            $lib->table($this->packageTable)->startTrans();
            $chunks = array_chunk($packages, $this->chunkSize);
            $i = 0;
            foreach ($chunks as $chunk) {
                $i++;
                echo "开始执行chunk$i,数据量：" . count($chunk) . PHP_EOL;
                pft_log($this->logPathErr, "开始执行chunk$i,数据量：" . count($chunk));
                $insPackage1 = [];
                $insPackage2 = [];
                $insPackage3 = [];
                foreach ($chunk as $package) {
                    $remainder = $package['card_id'] % 3;
                    switch ($remainder) {
                        case 0:
                            $insPackage1[] = $this->generatePackageData($package);
                            break;
                        case 1:
                            $insPackage2[] = $this->generatePackageData($package);
                            break;
                        case 2:
                            $insPackage3[] = $this->generatePackageData($package);
                            break;
                    }
                }
                //将数据插入到归档表
                if (!empty($insPackage1)) {
                    $lib->table($this->packageTable . '_00')->addAll($insPackage1);
                }
                if (!empty($insPackage2)) {
                    $lib->table($this->packageTable . '_01')->addAll($insPackage2);
                }
                if (!empty($insPackage3)) {
                    $lib->table($this->packageTable . '_02')->addAll($insPackage3);
                }
                $packageIds = array_column($chunk, 'id');
                //查询套餐下的特权
                $privileges = $lib->table($this->privilegeTable)
                    ->where([
                        'package_id' => ['in', $packageIds]
                    ])->select();
                $this->dealWithPrivilegeData($lib, $privileges);
                //删除归档完成的数据
                pft_log($this->logPathErr, "执行删除归档完的$this->packageTable " . json_encode($packageIds));
                $lib->table($this->packageTable)->where(['id' => ['in', $packageIds]])->delete();
            }
            //由于存在套餐未过期 特权票合作到期把特权刷掉的情况 这类特权也要归档下
            //$this->dealInvalidPackage($lib);
            $lib->table($this->packageTable)->commit();
        } catch (\Exception $e) {
            $lib->table($this->packageTable)->rollback();
            $log = [
                'ac' => __METHOD__,
                'err' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ];
            pft_log($this->logPathErr, '执行年卡归档发生错误：' . json_encode($log));
        }
    }

    public function dealInvalidPackage($lib)
    {
        $invalidPrivileges = $lib->table($this->privilegeTable)
            ->where(['state' => ['GT', 2]])
            ->limit($this->invalidPrivilegeLimit)
            ->select();
        if (empty($invalidPrivileges)) {
            return false;
        }
        $chunks = array_chunk($invalidPrivileges, $this->chunkSize);
        foreach ($chunks as $chunk) {
            $this->dealWithPrivilegeData($lib, $chunk);
        }
        return true;
    }

    private function generatePackageData($package): array
    {
        $ret = [];
        foreach ($this->packageFields as $field) {
            $ret[$field] = $package[$field];
            //年卡套餐过期的定时任务可能在归档定时任务操纵之后
            //造成归档套餐状态没及时改为失效 这里强制使用失效状态
            if ($field == 'state' && $package['state'] != 3) {
                $ret['state'] = 3;
            }
        }
        return $ret;
    }

    private function generatePrivilegeData($privilege): array
    {
        $ret = [];
        foreach ($this->privilegeFields as $field) {
            $ret[$field] = $privilege[$field];
        }
        return $ret;
    }

    public function dealWithPrivilegeData($lib, array $privileges)
    {
        $insPrivilege1 = [];
        $insPrivilege2 = [];
        $insPrivilege3 = [];
        $privilegeIds = [];
        if (!empty($privileges)) {
            foreach ($privileges as $privilege) {
                $privilegeIds[] = $privilege['id'];
                $remainder = $privilege['card_id'] % 3;
                switch ($remainder) {
                    case 0:
                        $insPrivilege1[] = $this->generatePrivilegeData($privilege);
                        break;
                    case 1:
                        $insPrivilege2[] = $this->generatePrivilegeData($privilege);
                        break;
                    case 2:
                        $insPrivilege3[] = $this->generatePrivilegeData($privilege);
                        break;
                }
            }
        }
        if (!empty($insPrivilege1)) {
            $lib->table($this->privilegeTable . '_00')->addAll($insPrivilege1);
        }
        if (!empty($insPrivilege2)) {
            $lib->table($this->privilegeTable . '_01')->addAll($insPrivilege2);
        }
        if (!empty($insPrivilege3)) {
            $lib->table($this->privilegeTable . '_02')->addAll($insPrivilege3);
        }
        if (!empty($privilegeIds)) {
            $chunks = array_chunk($privilegeIds, $this->chunkSize);
            foreach ($chunks as $chunk) {
                pft_log($this->logPathErr, "执行删除归档完的$this->privilegeTable " . json_encode($chunk));
                $lib->table($this->privilegeTable)->where(['id' => ['in', $chunk]])->delete();
            }
        }
    }

    public function recoverNoneActiveData()
    {

    }
    private function recoverLogic($table)
    {
        $lib = new \Library\Model('pft_business_case');
        $count = $lib->where($table)->where(['end_time' => 0])->count();
        if (!$count) {
            echo "表 $table 处理完成\n";
            pft_log($this->logPathErr, "表 $table 处理完成");
            return;
        }
        if ($count > $this->packageLimit) {
            $pages = ceil($count / $this->packageLimit);
            for ($i = 1; $i <= $pages; $i++) {
                $packages = $lib->table($table)->where(['end_time' => 0])->page($i, $this->packageLimit)->select();
                $this->execRecover($lib, $packages);
            }
        } else {

        }
    }

    public function execRecover()
    {

    }
}