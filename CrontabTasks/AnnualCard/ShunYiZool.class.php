<?php
/**
 * 北京-顺义动物园
 * 年卡需要新增/删除产品权益
 */

namespace CrontabTasks\AnnualCard;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class ShunYiZool
{
    private $logPath = 'annual/crontab';
    private $aid = 12772068;
    private $json1688 = '[
	{
		"tid" : 2409406,
		"group_id" : 23379,
		"group_name" : "顺义虎乐园-通用会员卡"
	},
	{
		"tid" : 2333508,
		"group_id" : 23380,
		"group_name" : "顺义虎乐园-单人门票"
	},
	{
		"tid" : 2409901,
		"group_id" : 23388,
		"group_name" : "电动项目-海盗船"
	},
	{
		"tid" : 2409902,
		"group_id" : 23389,
		"group_name" : "电动项目-旋转木马"
	},
	{
		"tid" : 2409903,
		"group_id" : 23390,
		"group_name" : "电动项目-太空穿梭"
	},
	{
		"tid" : 2409905,
		"group_id" : 23391,
		"group_name" : "电动项目-超级小纵队"
	},
	{
		"tid" : 2409908,
		"group_id" : 23392,
		"group_name" : "电动项目-八臂小飞机"
	},
	{
		"tid" : 2409910,
		"group_id" : 23393,
		"group_name" : "电动项目-小鹿蹦床"
	},
	{
		"tid" : 2409912,
		"group_id" : 23394,
		"group_name" : "电动项目-超级小摩托"
	},
	{
		"tid" : 2333524,
		"group_id" : 23395,
		"group_name" : "顺义虎乐园-亲子一大一小门票"
	},
	{
		"tid" : 2409914,
		"group_id" : 23396,
		"group_name" : "萌宠电玩-疯狂套圈02"
	},
	{
		"tid" : 2409917,
		"group_id" : 23397,
		"group_name" : "萌宠电玩-射弩"
	},
	{
		"tid" : 2409918,
		"group_id" : 23398,
		"group_name" : "萌宠电玩-疯狂套圈01"
	},
	{
		"tid" : 2409919,
		"group_id" : 23399,
		"group_name" : "萌宠电玩-小黄鸭"
	},
	{
		"tid" : 2409920,
		"group_id" : 23400,
		"group_name" : "萌宠电玩-娃娃屋"
	},
	{
		"tid" : 2409929,
		"group_id" : 23401,
		"group_name" : "爬行馆-单人票"
	},
	{
		"tid" : 2409935,
		"group_id" : 23402,
		"group_name" : "爬行馆-亲子票一大一小"
	},
	{
		"tid" : 2409937,
		"group_id" : 23403,
		"group_name" : "开心套大鹅-单人票88"
	},
	{
		"tid" : 2409938,
		"group_id" : 23404,
		"group_name" : "开心套大鹅-单人票50"
	},
	{
		"tid" : 2409939,
		"group_id" : 23405,
		"group_name" : "环游小火车-单人票"
	},
	{
		"tid" : 2409940,
		"group_id" : 23406,
		"group_name" : "环游小火车-双人票"
	},
	{
		"tid" : 2409942,
		"group_id" : 23407,
		"group_name" : "撒欢乐园-单人票"
	},
	{
		"tid" : 2409948,
		"group_id" : 23408,
		"group_name" : "撒欢乐园-亲子票一大一小"
	},
	{
		"tid" : 2409946,
		"group_id" : 23409,
		"group_name" : "掏矿星球-掏矿星球"
	},
	{
		"tid" : 2409952,
		"group_id" : 23410,
		"group_name" : "小马-小马骑行"
	},
	{
		"tid" : 2409954,
		"group_id" : 23411,
		"group_name" : "小马-小马车"
	},
	{
		"tid" : 2409957,
		"group_id" : 23412,
		"group_name" : "卡丁车-单人票"
	},
	{
		"tid" : 2409959,
		"group_id" : 23413,
		"group_name" : "卡丁车-亲子票"
	},
	{
		"tid" : 2409964,
		"group_id" : 23414,
		"group_name" : "疯狂钓虾馆-疯狂钓虾馆"
	},
	{
		"tid" : 2433156,
		"group_id" : 23846,
		"group_name" : "手工探宝-小恐龙"
	},
	{
		"tid" : 2433157,
		"group_id" : 23847,
		"group_name" : "手工探宝-大恐龙"
	},
	{
		"tid" : 2433158,
		"group_id" : 23848,
		"group_name" : "手工探宝-探宝"
	},
	{
		"tid" : 2433159,
		"group_id" : 23849,
		"group_name" : "手工探宝-存钱罐"
	},
	{
		"tid" : 2433160,
		"group_id" : 23850,
		"group_name" : "手工探宝-涂鸦扇子"
	},
	{
		"tid" : 2433161,
		"group_id" : 23851,
		"group_name" : "手工探宝-小号水晶瓶"
	},
	{
		"tid" : 2433162,
		"group_id" : 23852,
		"group_name" : "手工探宝-中号水晶瓶"
	},
	{
		"tid" : 2433163,
		"group_id" : 23853,
		"group_name" : "手工探宝-大号水晶瓶"
	},
	{
		"tid" : 2433164,
		"group_id" : 23854,
		"group_name" : "手工探宝-精美首饰盒"
	},
	{
		"tid" : 2444087,
		"group_id" : 24497,
		"group_name" : "会员卡1688"
	}
]';
    private $json988 = '[
	{
		"tid" : 2409406,
		"group_id" : 23368,
		"group_name" : "顺义虎乐园-通用会员卡"
	},
	{
		"tid" : 2409901,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409902,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409903,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409905,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409908,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409910,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409912,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2333508,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2333524,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2333634,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2333657,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2422521,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2444336,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2444347,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2445144,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2445138,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409914,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409917,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409918,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409919,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409920,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409921,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409937,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409938,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409939,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409940,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409942,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409948,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409946,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409952,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409954,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409957,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409959,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2409964,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2433156,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2433157,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2433158,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2433159,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2433160,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2433161,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2433162,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2433163,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2433164,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	},
	{
		"tid" : 2444087,
		"group_id" : 24499,
		"group_name" : "会员卡998"
	}
]';

    #/usr/local/php/bin/php /var/www/html/Service/Crontab/runNew.php AnnualCard_ShunYiZool run
    public function run()
    {
        $privilegeMap = [
            1688 => [
                'use_limit_json' => '{"all": 100, "day": 100, "month": 100}',
                'use_time_limit' => '{"type": 0, "end_time": "", "begin_time": ""}',
                'holiday_limit' => '{"type": 0, "holiday_limit": []}',
                'groupList' => json_decode($this->json1688, true),
            ],
            988 => [
                'use_limit_json' => '{"all": 50, "day": 50, "month": 50}',
                'use_time_limit' => '{"type": 0, "end_time": "", "begin_time": ""}',
                'holiday_limit' => '{"type": 0, "holiday_limit": []}',
                'groupList' => json_decode($this->json988, true),
            ],
        ];
        //解析出所有虚拟卡号
        //$data = $this->parseCsvData();
        $data = [
            "LDKMKG44", "9NOQ9GE8", "TZDQ37S0", "0WV3BTC3", "8SWX4NR2", "MMPK2KI2", "C8UGOD31",
            "OKBGD6O6", "Y8KUTUW8", "6T7Y5YF8", "FC65WPQ1", "U2MNP8V0", "BT1D8BW9", "M1LEBK78",
            "5E3EVU64", "53916JU4", "RLK2MWK2", "IDORWID0", "1GDI0FM1", "T3465KI8", "RA1M25P8",
            "MEMKIBW0", "YGSQ0QU0", "GMTLRXX0", "NAQEHTS0", "YK5Y4QC9", "0TO70PW7", "7W5VGA02",
            "7T50HTL2", "1MF7TGQ8", "S9KY5F71", "F8HCAH31", "0NLV0C55", "2D8JMCU0", "XNJDSFC0",
            "939WRYJ1", "W6KWIO84", "GSLP1179", "ZH331SX7", "CS8FBKL8", "NH05F6A1", "RGVCEYP0",
            "1J6L5UH2", "RE1KQXP1", "TRWXUKB0", "2L6LOE75", "LEFW65C1", "G5QYN5C0", "R9O425E0",
            "KTN09GJ9", "YIN5C3Y8", "QSEVNQQ0", "SSR1TAS1", "CM3PP4I7", "FEY99ZZ8", "KKH84O57",
            "3DPWHWP3", "MDI0C2T2", "7CSD2W76", "KEKGOT22", "CL9I6Z27", "DMY3JLW3", "5WAEZR94",
            "RE05BT94", "L36WNFP9", "3W41T480", "J1A1QNG2", "OXD6UM73", "K13476D1", "8OYW1I76",
            "WTP2IN02", "0NK4MGP4", "F4FWH329", "BO9B5CG4", "1S7PLT75", "9Y92ONQ0", "QKILINS0",
            "X9Z2D1Z2", "I25UXSS7", "B9MTTAJ9", "Q3VKU182", "MCTOYAV0", "WSHQNE55", "D9JGPV98",
            "979SORS5", "21WVT889", "7AKRYI85", "G85RLGI3", "FRW6IXK6", "74IJ2SQ3", "SI9CHF54",
            "WTTWR0S0", "UAY7I8W5", "2DASLQB2", "GL8J72X7", "VCXDCNE0", "O3LWO3R6", "RDOYHO22",
            "ESZNLRF0", "KOYNY291", "NYXLWEG0", "SF5DPNX5", "3PQZOOU3", "T2U3X779", "CKO1DBJ1",
            "KMB9KAA9", "AXQ7BT63", "1GLODR34", "NF81P8G7", "3DITL328", "EAMS6VL6", "L41FOHW5",
            "PN3J6WA9", "B73277X6", "XZZR76C3", "XDGY4IU4", "PJFH3SO3", "7F0MFCG7", "3UIU6W43",
            "5982I3K7", "SU5351A4"
        ];
        $chunks = array_chunk($data, 20);
        $lib = new \Library\Model('pft_business_case');
        $createTime = time();
        $addPrivilegeNums = 0;
        $j = 0;
        foreach ($chunks as $chunk) {
            $j++;
            $cards = $lib->table('pft_annual_card')
                ->where(['virtual_no' => ['in', $chunk]])
                ->field('id,virtual_no,pid')
                ->select();
            if (empty($cards)) {
                echo "chunk" . $j . "未找到年卡\n";
                echo $lib->table('pft_annual_card')->getLastSql() . "\n";
                pft_log($this->logPath, "chunk" . $j . "未找到年卡:" . $lib->table('pft_annual_card')->getLastSql());
                continue;
            }
            $cardMaps = array_column($cards, null, 'virtual_no');
            $cardIds = array_column($cards, 'id');
            //需要先删除之前的特权然后再重新加特权
            $lib->table('pft_annual_card_package_privilege')
                ->where([
                    'card_id' => ['in', $cardIds],
                    'aid' => $this->aid,
                ])
                ->data(['state' => 5])->save();
            $cardPrivileges = $lib->table('pft_annual_card_package_privilege')
                ->where(['card_id' => ['in', $cardIds]])
                ->field('tid,card_id,use_recode_json')
                ->select();
            $cardPrivilegesMap = [];
            foreach ($cardPrivileges as $cardPrivilege) {
                $cardPrivilegesMap[$cardPrivilege['card_id']][$cardPrivilege['tid']] = $cardPrivilege['use_recode_json'];
            }
            //查询套餐 考虑有续费情况 需要查出state为1和2的套餐
            $packages = $lib->table('pft_annual_card_period_package')
                ->where([
                    'sid' => $this->aid,
                    'virtual_no' => ['in', $chunk],
                    'state' => ['in', [1, 2]]
                ])
                ->field('id,card_id,tid,state,virtual_no')
                ->select();
            if (empty($packages)) {
                echo "chunk" . $j . "未找到套餐\n";
                echo $lib->table('pft_annual_card_period_package')->getLastSql() . "\n";
                pft_log($this->logPath, "chunk" . $j . "未找到套餐:" . $lib->table('pft_annual_card_period_package')->getLastSql());
                continue;
            }
            $insPrivileges988 = [];
            $insPrivileges1688 = [];
            foreach ($packages as $package) {
                if ($cardMaps[$package['virtual_no']]['pid'] == 2407032) {
                    //988套餐
                    foreach ($privilegeMap[988]['groupList'] as $value) {
                        //这里需要填充两个特权票
                        $insPrivileges988[] = [
                            'card_id' => $package['card_id'],
                            'tid' => $value['tid'],
                            'aid' => $this->aid,
                            'use_limit_json' => $privilegeMap[988]['use_limit_json'],
                            'use_recode_json' => $cardPrivilegesMap[$package['card_id']][$value['tid']] ?? json_encode([]),
                            //如果tid一样 要同步过来
                            'state' => 2,
                            'group_id' => $value['group_id'],
                            'group_name' => $value['group_name'],
                            'supplier_aid' => $this->aid,
                            'create_time' => $createTime,
                            'package_id' => $package['id'],
                            'use_time_limit' => $privilegeMap[988]['use_time_limit'],
                            'holiday_limit' => $privilegeMap[988]['holiday_limit'],
                        ];
                    }
                } elseif ($cardMaps[$package['virtual_no']]['pid'] == 2407416) {
                    //1688套餐
                    foreach ($privilegeMap[1688]['groupList'] as $value) {
                        //这里需要填充两个特权票
                        $insPrivileges1688[] = [
                            'card_id' => $package['card_id'],
                            'tid' => $value['tid'],
                            'aid' => $this->aid,
                            'use_limit_json' => $privilegeMap[1688]['use_limit_json'],
                            'use_recode_json' => $cardPrivilegesMap[$package['card_id']][$value['tid']] ?? json_encode([]),
                            //如果tid一样 要同步过来
                            'state' => 2,
                            'group_id' => $value['group_id'],
                            'group_name' => $value['group_name'],
                            'supplier_aid' => $this->aid,
                            'create_time' => $createTime,
                            'package_id' => $package['id'],
                            'use_time_limit' => $privilegeMap[1688]['use_time_limit'],
                            'holiday_limit' => $privilegeMap[1688]['holiday_limit'],
                        ];
                    }
                } else {
                    echo $package['virtual_no'] . "未找到套餐\n";
                }
            }
            //echo json_encode($insPrivileges988)."\n";
            //echo json_encode($insPrivileges1688)."\n";
            $res1 = $lib->table('pft_annual_card_package_privilege')->addAll($insPrivileges988);
            $res2 = $lib->table('pft_annual_card_package_privilege')->addAll($insPrivileges1688);
            $addPrivilegeNums += $res1;
            $addPrivilegeNums += $res2;
        }
        echo "done：" . $addPrivilegeNums . "\n";
    }
}
