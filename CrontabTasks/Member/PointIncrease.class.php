<?php

/**
 * 定时跑任务，增加积分
 */

namespace CrontabTasks\Member;

use Controller\CreditsMall\PointsManage;
use Library\Controller;
use Model\Report\Statistics;
use Business\CreditsMall\Points;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class PointIncrease extends Controller {


    public function __construct() {
        set_time_limit(0);
    }

    /**
     * 报表汇总完成后，查询前一天，可以增加积分的订单,按照对应商户的汇率，为散客增加积分
     */
    public function afterSummary() {
        // 查询表  summary/pft_report_checked_two
        $pointBiz = new Points(1, 1);
        $pointBiz->presentedForCheckedOrder();
    }
}