<?php
/**
 * 月结用户冻结
 * User: liuzuliang
 * Date: 2019/01/16
 */

namespace CrontabTasks\Member;

//权限判断
if(!defined('PFT_CLI')) {
    exit('Access Deny');
}
use Library\Controller;
use Pimple\Container;
use Model\Finance\FreezeMonthlyBalance as BalanceModel;
use Model\Finance\SettleBlance as SettleBlanceModel;

class AmountFrozen extends Controller{
    private $_container;

    public function __construct() {
        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();
        //模型容器
        $this->_container = new Container();

        $this->_container['balance_model'] = function () {
            return new BalanceModel(); //月份冻结余额模型
        };

        $this->_container['settleBlance_model'] = function () {
            return new SettleBlanceModel();  //用户余额清分模型
        };

        $this->_container['accountMoney_business'] = function () {
            return new \Business\Finance\AccountMoney();  //账户资金相关的封装
        };
        $this->_container['withdraw_business'] = function () {
            return new \Business\Finance\Withdraw();  //用户冻结余额
        };
    }

    /**
     * 月结用户冻结金额每个月定时查询保存脚本
     */
    public function run(){
        pft_log('freeze/start', date("Y-m-d h:i:s"));
        //查询月结方式的用户
        $fidArr = $this->_container['settleBlance_model']->getWithdrawSetFidList(3);
        if(!$fidArr){
            pft_log('freeze/error', json_encode(['time' => date("Y-m-d h:i:s"),'key' => '沒有用户']));
            die;
        }

        //获取账户余额自动清分每日清分任务表初始化金额
        $montyMark = date('Y01m');//清算标记
        $initMoneyArr = $this->_container['settleBlance_model']->getWithdrawRecordByFidArr($fidArr, $montyMark);

        $day = date('Ym', strtotime('-1 month'));
        foreach ($fidArr as $k => $v){
            $tmpMoney = 0;

            if(isset($initMoneyArr[$v])){
                $tmpMoney = (int)$initMoneyArr[$v];
            }else{
                //清算任务没有这个人数据
                pft_log('freeze/errorId', json_encode(['memberId' => $v , 'initMoney' => 'false']));
            }

            //获取当前可提现金额
            $amountFrozen = $this->getWithdrawalAmount($v);

            //清算金额小于可提现金额，取小的
            if ($tmpMoney < $amountFrozen) {
                //清分金额必须大于0
                $amountFrozen = $tmpMoney;
            }

            $tmp[] = [
                'fid'           => $v ?: 0,
                'amount_frozen' => $amountFrozen ?: 0,
                'day'           => $day,
                'create_time'   => time()
            ];
        }

        $res = $this->_container['balance_model']->table(BalanceModel::__FREEZE_MONTHLY_BALANCE_TABLE)->addAll($tmp);
        if($res){
            pft_log('freeze/success', json_encode(['time' => date("Y-m-d h:i:s"),'res' => $res]));
        }else{
            pft_log('freeze/error', json_encode(['time' => date("Y-m-d h:i:s"),'res' => $res, 'memberIdArr' => $fidArr]));
        }
    }

    /**
     * 获取某个用户可提现的金额 ：账户余额 - 冻结金额
     * <AUTHOR>
     * @date   2019-02-19
     * @param  int $memberId 用户id
     * @return int 用户的可提现金额 - 分
     */
    public function getWithdrawalAmount($memberId){
        if (!$memberId){
            return 0;
        }
        //获取账户冻结金额(包含上月下单本月验证的订单)
        $data = $this->_container['settleBlance_model']->unusedOrderDetail($memberId ,true);
        if($data == false){
            pft_log('freeze/errorId', json_encode(['memberId' => $memberId , 'freMoney' => 'false']));
            return 0;
        }
        //冻结金额
        $freMoney  = $data['total_data']['money'];

        //获取用户用户账户余额
        $accountMoneyArr = $this->_container['accountMoney_business']->getAccountMoney($memberId);
        if ($accountMoneyArr['code'] != 200) {
            pft_log('freeze/errorId', json_encode(['memberId' => $memberId , 'accountMoney' => 'false']));
            return 0;
        }
        //账户余额
        $accountMoney = $accountMoneyArr['data']['money'];
        pft_log('freeze/data', json_encode(['memberId' => $memberId, 'accountMoney' => $accountMoney, 'freMoney' => $freMoney]));

        //可提现金额
        $withdrawMoney = $accountMoney - $freMoney;
        if (bccomp($withdrawMoney, 0, 5) === -1) {
            //提现金额比0小的也记录一下
            pft_log('freeze/withdrawMoney', json_encode(['memberId' => $memberId , 'withdrawMoney' => $withdrawMoney]));
            $money = 0;
        }else {
            $money = $withdrawMoney;
        }

        return $money;
    }

    /**
     * 开放一个查询用户当前账户余额，冻结资金的入口
     * <AUTHOR>
     * @date   2019-02-22
     */
    public function getAccountInfo(){
        global $argv;
        if (!isset($argv[3]) || !is_numeric($argv[3])) {
            exit('请输入用户id');
        }
        $memberId = $argv[3];

        //获取账户冻结金额(包含上月下单本月验证的订单)
        $data = $this->_container['settleBlance_model']->unusedOrderDetail($memberId ,true);
        //冻结金额
        $freMoney  = $data['total_data']['money'];

        //获取用户用户账户余额
        $accountMoneyArr = $this->_container['accountMoney_business']->getAccountMoney($memberId);
        //账户余额
        $accountMoney = $accountMoneyArr['data']['money'];

        //可提现金额
        $withdrawMoney = $accountMoney - $freMoney;
        echo '冻结金额 = '.$freMoney."\n";
        echo '账户余额 = '.$accountMoney."\n";
        echo '可提现金额 = '.$withdrawMoney."\n";
    }
    /**
     * 获取某个用户可提现的订单
     * <AUTHOR>
     * @date   2019-04-28
     */
    public function getAccountCanWithdraw(){
        if (date('j') != 1){
            exit('不是一号');
        }
        $fidArr = $this->_container['settleBlance_model']->getWithdrawSetFidList(3);
        if(!$fidArr){
            pft_log('withdraw/error', json_encode(['time' => date("Y-m-d h:i:s"),'key' => '沒有用户']));
            exit;
        }
        $montyMark = date('Y01m',strtotime('-1 months'));//清算标记
        foreach ($fidArr as $key => $value){
            $result = $this->_container['withdraw_business']->getAccountVerifyOrder($value,$montyMark);
            pft_log('withdraw/success',json_encode($result));
        }
    }
}