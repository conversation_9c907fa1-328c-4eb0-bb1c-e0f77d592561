<?php
/**
 * Created by PhpStorm.
 * 后台计算供应商的订单数写入启用日期
 *
 * User: banjin
 * Date: 2017/4/24
 * Time: 16:43
 */

namespace CrontabTasks\Member;

use Library\Controller;
use Model\Member\Member;
use Model\Member\MemberEnable as Enable;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}
use Model\Report\Statistics;

class MemberEnable extends Controller
{
    /**
     * 供应商信息拓展表操作
     */
    public function runMemberEnable()
    {
        echo date("h:i:sa");
        $where['dtype']    = 0;
        $where['group_id'] = array('NEQ', 2);
        $map['enable_num'] = array('NEQ', 0);
        $exp['enable_num'] = 0;

        $member          = new Member();
        $enable          = new Enable();
        $statisticsModel = new Statistics();

        $page       = 1;
        $size       = 10;
        $fidArr     = [];
        $groupIdArr = [1,3,4,5,6];
        $statusArr  = [];
        $dtypeArr   = [0];
        $salesIDArr = [];
        $kefuIDArr  = [];

        $queryParams = [$page, $size, $fidArr, $groupIdArr, $statusArr, $dtypeArr, $salesIDArr, $kefuIDArr];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery',
            'queryMemberByCondition', $queryParams);
        $memberInfo = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data']['list'])) {
            $memberInfo = $queryRes['data']['list'];
            $totalPage  = ceil($queryRes['data']['total'] / $queryRes['data']['pageSize']);
            if ($totalPage > 1) {
                for ($i = 2; $i <= $totalPage; $i++) {
                    $queryParams = [$i, $size, $fidArr, $groupIdArr, $statusArr, $dtypeArr, $salesIDArr, $kefuIDArr];
                    $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery',
                        'queryMemberByCondition', $queryParams);
                    if ($queryRes['code'] == 200 && !empty($queryRes['data']['list'])) {
                        $memberInfo = array_merge($memberInfo, $queryRes['data']['list']);
                    }
                }
            }
        }

        //获取平台所有的供应商 去除测试账号
        //$memberInfo = $member->getInfoInMember('id', $where);
        //获取启用用户表 启用订单数 >= 50 的 fid
        $enableInfo = $enable->getMemberEnable('fid', $map);
        //获取启用用户表 启用订单数 = 0 的 fid
        $unableInfo = $enable->getMemberEnable('fid', $exp);

        foreach ($memberInfo as $row) {
            $val[] = $row['id'];
        }

        //获取平台所有尚未启用的 供应商fid
        $diffInfo = array_diff($val, $enableInfo);

        //根据检票报表 获取 尚未启用的供应商订单数
        $orderCheck = $statisticsModel->getReportChecked($diffInfo);

        if ($unableInfo) {
            $orderUnCheck = $statisticsModel->getReportChecked($unableInfo);
        }

        if (!$orderCheck) {
            pft_log('enable/error', "获取供应商检票报表失败:", 'month');
            exit;
        }

        //插入供应商一手供应验证订单数大于50单 至 启用表
        if ($orderCheck) {
            $report = array_values($orderCheck);
            foreach ($report as $k => $key) {
                if ($key['sum'] >= 50) {
                    $insert[] = array(
                        'fid'         => $key['fid'],
                        'enable_num'  => (int) $key['sum'],
                        'enable_time' => time(),
                    );
                }
            }
            if ($insert) {
                $res = $enable->insertSupplierInfo($insert);
                if (!$res) {
                    pft_log('enable/error', "插入供应商拓展表更改失败:", 'month');
                } else {
                    pft_log('enable/success', "插入供应商拓展表成功:", 'month');
                }
            }
        }

        //补漏的供应商
        if ($orderUnCheck) {
            foreach ($orderUnCheck as $key => $value) {
                if ($value['sum'] >= 50) {
                    $res = $enable->operateSupplierEnable($value['fid'], 2, (int) $value['sum']);
                }
            }
        }

        echo date("h:i:sa");
    }
}
