<?php
/**
 * 用户余额清分后台配置控制器
 * 清分模型：1=日结，2=周结，3=月结
 * 资金冻结类型：1=冻结未使用的总额，2=按比例或是具体数额冻结
 *
 *
 * <AUTHOR>
 * @date 2016-01-20
 *
 */
namespace CrontabTasks\Member;

use Business\Finance\Withdraw as WithdrawFund;
use Business\Order\Modify;
use Library\Constants\OrderConst;
use Library\Controller;
use Model\Order\AllApiOrderModel;
use Model\Order\OrderHandler;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class SettleBlance extends Controller
{
    private $_logPath = 'auto_withdraw/auto';

    //订单模型
    private $_handlerModel = null;

    //第三方订单模型
    private $_thirdModel = null;

    //清分模型
    private $_settleBlanceModel = null;

    public function __construct()
    {
        //运行时间不做限制
        set_time_limit(0);
    }

    /**
     * 按设定的规则生成清分记录
     * 可以一个小时执行一次
     *
     * <AUTHOR>
     * @date   2016-06-09
     *
     * @return
     */
    public function generateTransRecord()
    {
        //日结
        $this->runDaySettle();

        //周结,每周一运行
        if (date('w') == 1) {
            $this->runWeekSettle();
        }

        //月结,每月1号运行
        if (date('j') == 1) {
            $this->runMonthSettle();
        }
    }

    /**
     * 检测生成日结清分记录
     * <AUTHOR>
     * @date   2016-06-15
     *
     * @return
     */
    public function runDaySettle()
    {
        $settleBlanceModel = new \Model\Finance\SettleBlance();

        //获取日结的记录
        $dayMark = $settleBlanceModel->getCurrnetCircleMark($mode = 1);

        $dayList = $settleBlanceModel->getSettingList(1, 200, false, 1, $dayMark);

        //日志数据
        $logData = [];

        foreach ($dayList as $item) {
            $timeArr = $settleBlanceModel->createSettleTime($item['mode'], $dayMark, $item['close_time']);

            $settleTime   = strtotime($timeArr['settle_time']);
            $transferTime = strtotime($timeArr['transfer_time']);
            $fid          = $item['fid'];

            $freezeData                = @json_decode($item['freeze_data'], true);
            $freezeData                = $freezeData ? $freezeData : [];
            $freezeData['freeze_type'] = $item['freeze_type'];
            $freezeData['service_fee'] = $item['service_fee'];

            $res = $settleBlanceModel->createAutoRecord($fid, $settleTime, $transferTime, $dayMark, 1, $freezeData);

            //清分数据
            $logData[] = [
                'fid'          => $fid,
                'settleTime'   => $timeArr['settle_time'],
                'transferTime' => $timeArr['transfer_time'],
                'result'       => $res ? 1 : 0,
            ];
        }

        pft_log($this->_logPath, "生成清分记录（日结标识：{$dayMark}）:");
        foreach ($logData as $item) {
            pft_log($this->_logPath, json_encode($item));
        }
    }

    /**
     * 检测生成周结清分记录
     * <AUTHOR>
     * @date   2016-06-15
     *
     * @return
     */
    public function runWeekSettle()
    {
        $settleBlanceModel = new \Model\Finance\SettleBlance();

        //日志数据
        $logData = [];

        //获取周结的记录
        $weekMark = $settleBlanceModel->getCurrnetCircleMark($mode = 2);

        $weekList = $settleBlanceModel->getSettingList(1, 200, false, 2, $weekMark);
        foreach ($weekList as $item) {
            $timeArr = $settleBlanceModel->createSettleTime($item['mode'], $weekMark, $item['close_time'], $item['close_date']);

            $settleTime   = strtotime($timeArr['settle_time']);
            $transferTime = strtotime($timeArr['transfer_time']);
            $fid          = $item['fid'];

            $freezeData                = @json_decode($item['freeze_data'], true);
            $freezeData                = $freezeData ? $freezeData : [];
            $freezeData['freeze_type'] = $item['freeze_type'];
            $freezeData['service_fee'] = $item['service_fee'];

            $res = $settleBlanceModel->createAutoRecord($fid, $settleTime, $transferTime, $weekMark, 2, $freezeData);

            //清分数据
            $logData[] = [
                'fid'          => $fid,
                'settleTime'   => $timeArr['settle_time'],
                'transferTime' => $timeArr['transfer_time'],
                'result'       => $res ? 1 : 0,
            ];
        }

        pft_log($this->_logPath, "生成清分记录（周结标识：{$weekMark}）:");
        foreach ($logData as $item) {
            pft_log($this->_logPath, json_encode($item));
        }
    }

    /**
     * 检测生成月结清分记录
     * <AUTHOR>
     * @date   2016-06-15
     *
     * @return
     */
    public function runMonthSettle()
    {
        $settleBlanceModel = new \Model\Finance\SettleBlance();

        //日志数据
        $logData = [];

        //获取月结的记录
        $montyMark = $settleBlanceModel->getCurrnetCircleMark($mode = 3);

        $montyList = $settleBlanceModel->getSettingList(1, 200, false, 3, $montyMark);
        foreach ($montyList as $item) {
            $timeArr = $settleBlanceModel->createSettleTime($item['mode'], $montyMark, $item['close_time'], $item['close_date'], $item['transfer_time'], $item['transfer_date']);

            $settleTime   = strtotime($timeArr['settle_time']);
            $transferTime = strtotime($timeArr['transfer_time']);
            $fid          = $item['fid'];

            $freezeData                = @json_decode($item['freeze_data'], true);
            $freezeData                = $freezeData ? $freezeData : [];
            $freezeData['freeze_type'] = $item['freeze_type'];
            $freezeData['service_fee'] = $item['service_fee'];

            $res = $settleBlanceModel->createAutoRecord($fid, $settleTime, $transferTime, $montyMark, 3, $freezeData);

            //清分数据
            $logData[] = [
                'fid'          => $fid,
                'settleTime'   => $timeArr['settle_time'],
                'transferTime' => $timeArr['transfer_time'],
                'result'       => $res ? 1 : 0,
            ];
        }

        pft_log($this->_logPath, "生成清分记录（月结标识：{$montyMark}）:");
        foreach ($logData as $item) {
            pft_log($this->_logPath, json_encode($item));
        }
    }

    /**
     * 运行清算任务
     * @deprecated 已废弃，代码已迁移至中台
     * <AUTHOR>
     * @date   2016-06-09
     *
     * @return
     */
    public function runSettleTask()
    {
        $settleBlanceModel = $this->_getSettleModel();

        //日志数据
        $logData = [];

        $settleList = $settleBlanceModel->getSettleList(1, 100);

        foreach ($settleList as $item) {
            //清算金额
            $fid       = $item['fid'];
            $id        = $item['id'];
            $mark      = $item['cycle_mark'];
            $mode      = $item['mode'];
            $money     = $item['init_money'];
            $frozeData = json_decode($item['froze_data'], true);

            $settleInfo = $settleBlanceModel->settleAmount($fid, $mode, $mark, $frozeData, $money);

            //状态
            $status = $settleInfo['status'];

            //具体清分的结果，如果清算失败就不进行清分
            $transRes = -1;

            if ($status === -1) {
                //记录数据错误
                $res = $settleBlanceModel->stopSettle($id, '自动清分配置错误');
            } else if ($status === -2) {
                //清分关闭
                $res = $settleBlanceModel->stopSettle($id, '自动清分处于关闭状态');
            } else if ($status === -3) {
                //账户余额没有钱
                $amoney = round($settleInfo['amoney'] / 100, 2);
                $res    = $settleBlanceModel->stopSettle($id, "账号余额已经没有钱了，账户余额：{$amoney}元");
            } else if ($status === -4) {
                //获取未使用订单信息的时候报错
                $res = $settleBlanceModel->stopSettle($id, '获取未使用订单金额的时候系统报错');

            } else if ($status === -5) {
                $amoney      = round($settleInfo['amoney'] / 100, 2);
                $freezeMoney = round($settleInfo['freeze_money'] / 100, 2);

                $res = $settleBlanceModel->stopSettle($id, "账号余额不足，账户余额：{$amoney}元，清分冻结余额：{$freezeMoney}元", $settleInfo['amoney'], $settleInfo['freeze_money']);
            } else if ($status === -6) {
                $transMoney = round($settleInfo['trans_money'] / 100, 2);
                $limitMoney = round($settleInfo['limit_money'] / 100, 2);

                $res = $settleBlanceModel->stopSettle($id, "提现金额{$transMoney}元不足最低提现最低额度{$limitMoney}元", $settleInfo['amoney'], $settleInfo['freeze_money']);
            } else {
                //正常清算
                $freezeMoney   = $settleInfo['freeze_money'];
                $transferMoney = $settleInfo['transfer_money'];
                $remarkData    = $settleInfo['remark_data'];

                $amoney = $settleInfo['amoney'];
                if (isset($remarkData['type'])) {
                    if ($remarkData['type'] == 1) {
                        $remark = "按比例冻结，冻结比例：{$remarkData['value']}%";
                    } else {
                        $remark = "按具体金额冻结，冻结金额：{$remarkData['value']}元";
                    }
                } else {
                    //未使用订单
                    $tmpMoney = round($freezeMoney / 100, 2);
                    $remark   = "需冻结未使用订单情况：总订单数={$remarkData['order_num']}, 总票数={$remarkData['ticket_num']}, 总金额={$tmpMoney}元";
                }

                $res = $settleBlanceModel->updateSettleInfo($id, $freezeMoney, $transferMoney, $amoney, $remark);

                //如果清算都正常的话，顺便就进行打款清分
                $transRes = $this->_transMoney($id, $fid, $freezeMoney, $transferMoney, $mode);
            }

            //清分数据
            $logData[] = [
                'fid'       => $fid,
                'id'        => $id,
                'status'    => $status,
                'result'    => $res ? 1 : 0,
                'trans_res' => $transRes,
            ];
        }

        $count = count($logData);
        pft_log($this->_logPath, "运行清算任务({$count}):");
        foreach ($logData as $item) {
            pft_log($this->_logPath, json_encode($item));
        }
    }

    /**
     * 运行打款任务
     * @deprecated 已废弃，代码已迁移至中台
     * <AUTHOR>
     * @date   2016-06-09
     *
     * @return
     */
    public function runTransTask()
    {
        $settleBlanceModel = new \Model\Finance\SettleBlance();

        $count        = 0;
        $transferList = $settleBlanceModel->getTransferList(1, 100);
        foreach ($transferList as $item) {
            //参数
            $id            = $item['id'];
            $fid           = $item['fid'];
            $freezeMoney   = $item['freeze_money'];
            $transferMoney = $item['transfer_money'];
            $mode          = $item['mode'];

            $res = $this->_transMoney($id, $fid, $freezeMoney, $transferMoney, $mode);
            $count += 1;
        }

        pft_log($this->_logPath, "具体清分任务({$count}):");
    }

    /**
     * 具体清分
     * <AUTHOR>
     * @date   2018-03-21
     *
     * @param  int $id 自动清分记录ID
     * @param  int $fid 用户ID
     * @param  int $freezeMoney 冻结金额 - 分
     * @param  int $transferMoney 提现金额 - 分
     * @param  int $mode 提现模式
     *
     * @return bool
     */
    public function _transMoney($id, $fid, $freezeMoney, $transferMoney, $mode)
    {
        $settleBlanceModel = $this->_getSettleModel();

        //提现数据检测
        $transInfo = $settleBlanceModel->preTransMoney($id, $fid, $freezeMoney, $transferMoney, $mode);

        //错误处理
        $status = $transInfo['status'];
        if ($status === -1) {
            //配置出错了
            $res = $settleBlanceModel->updateTransferInfo($id, 1, '转账失败，自动清分配置错误');
        } else if ($status == -2) {
            //账户余额没有钱
            $amoney = round($transInfo['amoney'] / 100, 2);
            $res    = $settleBlanceModel->updateTransferInfo($id, 1, "转账失败，账号余额已经没有钱了，账户余额：{$amoney}元");
        } else if ($status == -3) {
            //余额不够，不能清分
            $amoney        = round($transInfo['amoney'] / 100, 2);
            $freezeMoney   = round($transInfo['freeze_money'] / 100, 2);
            $transferMoney = round($transInfo['transfer_money'] / 100, 2);

            $res = $settleBlanceModel->updateTransferInfo($id, 1, "转账失败，账号余额不足，账户余额：{$amoney}元，提现金额：{$transferMoney}元，不可提现金额：{$freezeMoney}元");
        } else if ($status == -4) {
            //剩余金额不足以支付提现手续费
            $amoney        = round($transInfo['amoney'] / 100, 2);
            $feeMoney      = round($transInfo['fee_money'] / 100, 2);
            $transferMoney = round($transInfo['transfer_money'] / 100, 2);

            $res = $settleBlanceModel->updateTransferInfo($id, 1, "转账失败，剩余金额不足以支付提现手续费，账户余额：{$amoney}元，提现金额：{$transferMoney}元，提现手续费：{$feeMoney}元");
        } else {
            //调用具体的提现接口
            $serviceFee    = $transInfo['service_fee'];
            $serviceCharge = $transInfo['feeMoney'];
            $accountInfo   = $transInfo['account_info'];
            $isHumanAuth   = $transInfo['is_human_auth'];
            $feeCutWay     = $transInfo['feeCutWay'];
            $accountMoney  = $transInfo['amoney'];
            $operatorName  = '系统自动提现';
            $accountType   = 1;
            $isAuto        = true;

            //最多可提现金额
            //这个逻辑实现的很有问题，后期进行修改
            $settleblance         = $this->_getSettleModel();
            $creditBalancePresent = $settleblance->getMaximumWithDrawMoney($fid);

            $withdrawFund = new WithdrawFund();
            $transRes     = $withdrawFund->addRecord($fid, 1, $transferMoney, $accountMoney, $serviceFee, $feeCutWay, $accountType, $creditBalancePresent, $accountInfo, $operatorName, $isAuto, $isHumanAuth);

            if ($transRes[0] == 0) {
                //提现成功
                if (!$feeCutWay) {
                    $transferMoney -= $serviceCharge;
                    $feeWay = '提现金额中扣除';
                } else {
                    $feeWay = '账户余额扣除';
                }
                $remark = '提现成功,' . $feeWay . '提现手续费为' . round($serviceCharge / 100, 2) . '元';
                $res    = $settleBlanceModel->updateTransferInfo($id, 0, $remark, $transferMoney);
            } else {
                //系统错误了，提现出现问题
                $errorMsg = $transRes[1];
                $res = $settleBlanceModel->updateTransferInfo($id, 3, "转账失败【{$errorMsg}】");
            }
        }

        //清分数据
        $logData = [
            'trans'  => 1,
            'fid'    => $fid,
            'id'     => $id,
            'status' => $status,
            'result' => $res ? 1 : 0,
        ];

        pft_log($this->_logPath, json_encode($logData));

        return true;
    }

    /**
     * 获取清分模型
     * <AUTHOR>
     * @date   2018-03-21
     *
     * @return
     */
    private function _getSettleModel()
    {
        if (!$this->_settleBlanceModel) {
            $this->_settleBlanceModel = new \Model\Finance\SettleBlance();
        }

        return $this->_settleBlanceModel;
    }

    /**
     * 过期订单自动处理任务
     * <AUTHOR>
     * @date   2017-05-08
     *
     * @return array
     */
    public function runExpireTask()
    {
        $types = [
            'autoFinish', //自动完结
            'autoCheck',  //自动验证
            'autoCancel', //自动取消
        ];

        $type = $GLOBALS['argv'][3] ?: $types[0];
        $type = in_array($type, $types) ? $type : $types[0];

        switch ($type) {
            case 'autoFinish':
                $typesCode = [0, 2]; //0不知道干啥的，也捞出来放到第一项处理
                break;
            case 'autoCheck':
                $typesCode = [3];
                break;
            case 'autoCancel':
                $typesCode = [4];
                break;
        }
        if (!$typesCode) {
            exit('types code error');
        }

        pft_log('expire', 'start');
        $nowTime     = time();
        $stopHour = ['02', '03', '04'];
        $fullHour = ['00', '01', '05', '06', '07', '08'];
        $slowHour = ['09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'];
        $nowH = date('H', $nowTime);
        if(in_array($nowH, $stopHour)) {
            pft_log('expire', 'stop end');
            exit('stop hour');
        } else if(in_array($nowH, $fullHour)) {
            // 为了避免处理数据太多导致MySQL连接超时无法查到数据的问题，这里分多次处理
            for ($i=0; $i < 9; $i++) {
                $this->expireTask($nowTime, $typesCode);
            }
            //脚本结束
            pft_log('expire', 'end');
            exit();
        } else if(in_array($nowH, $slowHour)) {
            //慢速模式下, 每次拉取500条, 每100条sleep 1秒
            $this->expireTask($nowTime, $typesCode, 500, 100, 1);
            pft_log('expire', 'slow end');
            exit();
        }
        pft_log('expire', 'noting');
    }
    private function expireTask($nowTime, $typesCode, $size = 1000, $sleepByNum = 0, $sleep = 0)
    {
        $batchId = str_replace('.', '', microtime(true));
        pft_log('expire', json_encode(['batch start', $batchId]));

        $expireModel = new \Model\Order\OrderExpireAction();

        $startTime = intval($nowTime);
        $actionTimes = [
            $startTime - 7*86400,
            $startTime
        ];

        $expireActionList = $expireModel->getTaskByCron($actionTimes, $typesCode, 1, $size);
        if (!$expireActionList) {
            pft_log('expire', 'stop - 0');
            exit('nothing to do');
        }
        $ticketModel = new \Model\Product\Ticket();
        $orderModel  = new \Model\Order\OrderTools();
        $landModel   = new \Model\Product\Land();
        $refundModel = new \Model\Order\RefundAuditModel();
        $otaProductBiz = new \Business\Ota\Product();
        //存储已查询是的票信息避免反复查
        $ticketDataArr = [];
        foreach ($expireActionList as $i=>$item) {
            $taskId     = $item['id'];
            $orderId    = $item['orderid'];
            $tid        = $item['tid'];
            $expireTime = $item['expire_time'];
            $actionType = $item['action_type'];
            $expireFee  = $item['action_fee'];

            pft_log('expire', json_encode(['order start', $batchId, $orderId]));

            //判断当前订单的状态
            $orderInfo = $orderModel->getOrderInfo($orderId, 'status, lid', false, false, 'verified_num,origin_num,refund_num');
            if (!$orderInfo) {
                //订单信息不存在
                $res = $expireModel->updateTask($taskId, $result = 3, $remark = '订单信息不存在');
                pft_log('expire', json_encode(['no order_info', $batchId, $orderId, $res, $orderInfo, $orderModel->_sql(), $orderModel->getDbError()]));
                continue;
            }

            //如果订单在退票审核中，就不能继续了
            $isUnderAudit = $refundModel->isAllUnderAudit([$orderId]);
            if ($isUnderAudit) {
                $res = $expireModel->updateTask($taskId, $result = 3, $remark = '订单退票审核');
                pft_log('expire', json_encode(['under audit', $res, $batchId, $orderId]));
                continue;
            }

            $status      = $orderInfo['status'];
            $lid         = $orderInfo['lid'];
            $verifiedNum = isset($orderInfo['verified_num']) ? intval($orderInfo['verified_num']) : 0;

            if (!in_array($status,[2,80])) { //针对下单后未提货自动取消的80
                //订单状态不是过期，不需要处理
                $res = $expireModel->updateTask($taskId, $result = 3, $remark = '订单状态不是过期，不需要处理');
                pft_log('expire', json_encode(['no expire', $res, $batchId, $orderInfo]));

                continue;
            }

            $landInfo   = $landModel->getLandInfo($lid, false, 'p_type');
            //避免数据反复查询
            if (empty($ticketDataArr[$tid])) {
                $ticketData = $ticketModel->getTicketInfoExtById($tid, 'id,apply_did');
                $ticketDataArr[$tid] = $ticketData;
            } else {
                $ticketData = $ticketDataArr[$tid];
            }
            $ticketInfo = $ticketData['ticket'];
            if (!$landInfo || !$ticketInfo) {
                //景区数据找不到
                $res = $expireModel->updateTask($taskId, $result = 3, $remark = '景区数据找不到');
                pft_log('expire', json_encode(['no ticket_info', $res, $batchId, $landInfo, $ticketInfo]));

                continue;
            }
            $ticketExt = $ticketData['ext'];
            $autoCancelCheckTakeTicket  = $ticketExt['order_not_auto_cancel']; //已取票、部分取票订单不自动取消 1：是 0：否

            // 切换门票获取第三方系统信息
            $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTid($tid);
            if (empty($thirdBindInfoArr)) {

            }
            $Mdetails = $thirdBindInfoArr['Mdetails'];

            $ptype    = $landInfo['p_type'];
            // $Mdetails = $ticketInfo['Mdetails'];
            $applyDid = $ticketInfo['apply_did'];

            //判断订单是不是对接第三方系统的 - 现在不处理
            // if ($ptype != 'H' && $Mdetails == 1) {
            //     //对接第三方系统的订单，不能做自动处理
            //     $res = $expireModel->updateTask($taskId, $result = 3, $remark = '对接第三方系统的订单');
            //     pft_log('expire', json_encode(['third order', $res, $ptype, $Mdetails]));

            //     continue;
            // }

            //如果是第三方订单的话，需要将第三方订单记录表的状态修改为已经验证
            $isThirdOrder = false;
            if ($ptype != 'H' && $Mdetails == 1) {
                $isThirdOrder = true;
            }

            if ($actionType == 2) {
                //自动完结处理
                $tmpResult = $this->_finishOrder($orderId, $opId = 0, $isThirdOrder);
            } else if ($actionType == 3) {
                //自动验证
                $tmpResult = $this->_checkOrder($orderId, $isThirdOrder);
            } else if ($actionType == 4) {
                $expireFee = number_format($expireFee, 2);

                if ($expireFee >= 0 && $expireFee <= 100) {
                    $autoCancelFee = $expireFee;
                } else {
                    $autoCancelFee = false;
                }

                //自动取消订单 补充已取票和部分取票不取消
                if ($autoCancelCheckTakeTicket) {
                    $printInfo        = $orderModel->getPackOrdersInfoByOrderId($orderId, 'ifprint,orderid');
                    if (!empty($printInfo)) {
                        $ifPrintData = $printInfo[0];
                        if (!in_array($ifPrintData['ifprint'], [1, 2])) {
                            pft_log('expire', "未取票可退款, 订单号:  $orderId , 票id: $tid");
                            $tmpResult = $this->_cancelOrder($orderId, $autoCancelFee, $applyDid, $verifiedNum);
                        }
                    }
                } else {
                    $tmpResult = $this->_cancelOrder($orderId, $autoCancelFee, $applyDid, $verifiedNum);
                }
            }

            //将状态更新
            $result = $tmpResult['result'];
            $remark = $tmpResult['remark'];
            $res    = $expireModel->updateTask($taskId, $result, $remark);

            //处理成功 分销专员业务佣金结算
//            if (1 == $result) {
//                $data = ['action' => 'expire', 'data' => ['ordernum' => $orderId, 'expire_action' => $actionType]];
//                \Library\Resque\Queue::push('independent_system', 'MultiDist_Job', $data);
//            }

            //写入日志
            pft_log('expire', json_encode(['res', $batchId, $orderId, $actionType, $expireFee, $res, $tmpResult, $sleep]));
            if($sleep > 0 && $sleepByNum > 0 && (($i+1) % $sleepByNum == 0)) {
                sleep($sleep);
            }
        }
    }
    /**
     * 订单完结处理
     * <AUTHOR>
     * @date   2017-05-08
     *
     * @param  string $ordernum
     * @return array
     */
    private function _finishOrder($ordernum, $opId = 0, $isThirdOrder)
    {
        $thirdModel = $this->_getThirdOrderModel();

        $orderBiz   = new \Business\Order\Modify();
        $res        = $orderBiz->finishOrder($ordernum, $opId, 19, '过期自动完结');

        if ($res['code'] == 200) {
            //因为如果第三方订单表订单没有验证的话，会去循环查询第三方状态，所以在这边统一修改状态
            if ($isThirdOrder) {
                $thirdRes = $thirdModel->checkOrder($ordernum);

                pft_log('expire_third', json_encode([$ordernum, $thirdRes]));
            }

            return ['result' => 1, 'remark' => '完结成功'];
        } else {
            return ['result' => 2, 'remark' => '完结失败 - ' . $res['msg']];
        }
    }

    /**
     * 订单验证处理
     * <AUTHOR>
     * @date   2017-05-08
     *
     * @param  string $ordernum
     * @param  int $isThirdOrder 是不是第三方订单
     * @return array
     */
    private function _checkOrder($ordernum, $isThirdOrder = false)
    {
        $model      = $this->_getHandlerModel();
        $thirdModel = $this->_getThirdOrderModel();

        $res = $model->CheckOrderSimply($ordernum, $memberId = 0, $order_info = null, $memo = '过期自动验证', $source = 19);
        if ($res === true) {
            //因为如果第三方订单表订单没有验证的话，会去循环查询第三方状态，所以在这边统一修改状态
            if ($isThirdOrder) {
                $thirdRes = $thirdModel->checkOrder($ordernum);

                pft_log('expire_third', json_encode([$ordernum, $thirdRes]));
            }

            return ['result' => 1, 'remark' => '验证成功'];
        } else {
            $code = $res['code'];
            $msg  = $res['msg'];

            return ['result' => 2, 'remark' => '验证失败 - ' . $msg . "({$code})"];
        }
    }

    /**
     * 订单取消处理
     * <AUTHOR>
     * @date   2017-05-08
     *
     * @param  string $ordernum
     * @return array
     */
    private function _cancelOrder($ordernum, $autoCancelFee, $opId, $leftNum = 0)
    {
       // $insideSoap = \Library\Tools\Helpers::GetSoapInside();
        $modifyBiz        = new Modify();
        $cancelRemarkArr  = ['remark' => '过期自动取消'];
        $cancelSpecialArr = [
            'is_need_audit'   => false,
            'auto_cancel_fee' => $autoCancelFee,
            'is_cancel_sub'   => false
        ];
        try {
            $res = $modifyBiz->cancelParamsCommonFormat($ordernum,$opId,OrderConst::SET_EXPIRED_CANCEL,'',
                -1,'common',$cancelRemarkArr,[],[],$cancelSpecialArr);

            if ($res['code'] == 200) {
                return ['result' => 1, 'remark' => '取消成功'];
            } else {
                return ['result' => 2, 'remark' => "取消失败 - ".$res['msg']];
            }

        } catch (Exception $e) {
            //接口报错了，不知道接口调用情况，需要重新处理
            return ['result' => 0, 'remark' => '接口超时，需要重新处理'];
        }
    }

    /**
     * 获取订单模型
     * @return Model
     */
    private function _getHandlerModel()
    {
        if (!$this->_handlerModel) {
            $this->_handlerModel = new OrderHandler();
        }

        return $this->_handlerModel;
    }

    /**
     * 获取第三方订单模型
     * @return Model
     */
    private function _getThirdOrderModel()
    {
        if (!$this->_thirdModel) {
            $this->_thirdModel = new AllApiOrderModel();
        }

        return $this->_thirdModel;
    }

}
