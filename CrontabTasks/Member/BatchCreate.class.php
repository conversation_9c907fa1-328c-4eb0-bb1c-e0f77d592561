<?php

namespace CrontabTasks\Member;

//权限判断
if(!defined('PFT_CLI')) {
   exit('Access Deny');
}

use Business\JavaApi\Product\EvoluteGroupUser;
use Business\Member\RegisterAction;
use Library\Constants\MemberConst;
use Library\Controller;
use PharIo\Manifest\Library;

/**
 * 批量添加分销商(项目需求)
 *
 * <AUTHOR>
 */
class BatchCreate extends Controller
{
    private $csvPath = '/root/dis.csv';

    private $groupMap = [
        '旅行社'     => 485423,
        '外埠旅行社' => 485427,
        '企事业单位' => 485428,
        '大学生票'   => 485429,
        '公司外联'   => 485430,
        '宾馆酒店'   => 485431,
        '银行合作'   => 485432,
        '夏冬令营（夜宿）' => 485433,
        '零元票' => 485436,
        '增值产品' => 485438,
        '爱博营地' => 485439,
        '员工票' => 485440,
        '老年团' => 485441,
        '军人五折票' => 485442
    ];

    private $newGroupMap = [
        '旅行社'     => 31492743,
        '外埠旅行社' => 31492747,
        '企事业单位' => 31492748,
        '大学生票'   => 31492749,
        '公司外联'   => 31492750,
        '宾馆酒店'   => 31492751,
        '银行合作'   => 31492752,
        '夏冬令营（夜宿）' => 31492753,
        '零元票' => 31492756,
        '增值产品' => 31492758,
        '爱博营地' => 31492759,
        '员工票' => 31492760,
        '老年团' => 31492761,
        '军人五折票' => 31492762
    ];

    private $groupMap2 = [
        '旅行社'     => 66893,
        '外埠旅行社' => 66893,
        '企事业单位' => 66893,
        '大学生票'   => 66893,
        '公司外联'   => 66893,
        '宾馆酒店'   => 66893,
        '银行合作'   => 66893,
        '夏冬令营（夜宿）' => 66893,
        '零元票' => 66893,
        '增值产品' => 66893,
        '爱博营地' => 66893,
        '员工票' => 66893,
        '老年团' => 66893,
        '军人五折票' => 66893
    ];

    private $newGroupMap2 = [
        '旅行社'     => 30720475,
        '外埠旅行社' => 30720475,
        '企事业单位' => 30720475,
        '大学生票'   => 30720475,
        '公司外联'   => 30720475,
        '宾馆酒店'   => 30720475,
        '银行合作'   => 30720475,
        '夏冬令营（夜宿）' => 30720475,
        '零元票' => 30720475,
        '增值产品' => 30720475,
        '爱博营地' => 30720475,
        '员工票' => 30720475,
        '老年团' => 30720475,
        '军人五折票' => 30720475
    ];

    public function run()
    {
        $fp = fopen($this->csvPath, 'r');

        $data = [];
        while ($line = fgetcsv($fp)) {
            $line[0] = iconv("gbk", "utf-8", $line[0]);
            $line[1] = iconv("gbk", "utf-8", $line[1]);
            $line[2] = iconv("gbk", "utf-8", $line[2]);
            $line[3] = iconv("gbk", "utf-8", $line[3]);
            $line[4] = iconv("gbk", "utf-8", $line[4]);

            $data[] = $line;
        }

        $memberBiz  = new \Business\member\Member();
        $model      = new \Library\Model();
        $api        = new EvoluteGroupUser();
        $priceBiz   = new \Business\Product\Price();

        unset($data[0], $data[1]);

        $repeat = [];
        foreach ($data as $item) {
            //手机号是否存在
            $customerId = $memberBiz->parseCustomerIdByMobile($item[3]);
            if ($customerId) {
                $repeat[] = $item[3];
                continue;
            }

            if ($item[1] == '外埠旅行社') {
                $com = '旅行社';
            }

            if ($item[1] == '宾馆酒店') {
                $com = '酒店';
            }

            $request = [
                'name'          => $item[0],
                'type'          => 1,
                'status'        => 0,
                'passwd'        => md5(md5('pft12301V')),
                'avatar'        => '',
                'identifier'    => $item[3],
                'page_source'   => MemberConst::PAGE_PLATFORM,
                'info_source'   => MemberConst::INFO_MOBILE,
                'com_type'      => $com,
                'address'       => $item[4]
            ];

            $registerAction = new RegisterAction();
            $result = $registerAction->register((object)$request);
            if ($result['code'] == 200) {
                $memberId = $result['data']['member_id'];
                $sid = 8621779;

                $groupId = $this->groupMap[$item[1]] ?? 0;
                $newGid  = $this->newGroupMap[$item[1]] ?? 0;

                $relation = [
                    'parent_id' => $sid,
                    'son_id' => $memberId,
                    'son_id_type' => 0,
                    'ship_type' => 0,
                    'status' => 0,
                    'rectime' => date('Y-m-d H:i:s'),
                    'price_group_id' => $groupId,
                    'remark' => '8621779导入'
                ];

                if (!$model->table('pft_member_relationship')->add($relation)) {
                    var_dump('relation创建失败:' . $item[3]);
                }

                $joinRes = $priceBiz->joinGroup($groupId, $memberId, $sid);
                if ($joinRes['code'] != 200) {
                    var_dump('group创建失败:' . $item[3]);
                }

                $res = $api->addDistributor($sid, $memberId, $sid, $newGid);
                if ($res['code'] != 200) {
                    var_dump('addDistributor创建失败:' . $item[3]);
                }

                // var_dump($item[3]);die;
            } else {
                $repeat[] = $item[3];
            }
        }

        echo implode(',', $repeat);die;
    }
}
