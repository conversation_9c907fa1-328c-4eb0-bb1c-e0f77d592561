<?php
/**
 * Created by Sublime.
 * User: chenyanbin
 * Date: 2017/1/12
 * Time: 16:34
 */

namespace CrontabTasks\Member;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

use Library\Controller;
use Library\Resque\Queue;
use Model\Member\Member;
use Model\Member\ExpenseWarning as Expense;

class ExpenseWarning extends Controller
{
    /**
     * @var \Model\Member\ExpenseWarning
     */
    private $_model;

    public function __construct()
    {
        $this->_model = new \Model\Member\ExpenseWarning();
    }

    /**
     * 用户余额提醒
     */
    public function runExpenseDistributor()
    {
        $this->_model->CheckAccount();
    }

    /**
     * 用户余额提醒-200
     */
    public function runExpenseDistributorZone()
    {
        $this->_model->CheckAccountZone();
    }

    /**
     * 发送余额提醒短信
     */
    public function runExpenseSmsend()
    {
        $Expense     = new Expense();
        $memberModel = new Member('slave');
        $data        = $Expense->getInfoSendStatus();
        $ids         = [];

        foreach ($data as $_idx => $items) {
            //循环获取未发送短信的数据
            if (($items['issms'] == 0 && $items['arrearstime'] != 0)) {
                //获取未发送个短信的用户信息
                $Info = $memberModel->getMemberInfo($items['fid'], 'id');
                //排除测试组账号等非正常用户账号
                if ($items['fid'] != 1 && strlen($Info['account']) == 6 && $Info['mobile'] && $Info['group_id'] != 2) {
                    //执行发送短信队列
                    $job_id = Queue::push('notify', 'SmsNotify_Job', [
                        'mobile' => $Info['mobile'],
                        'action' => 'moneyWarn',
                        'params' => [$Info['account'], "0"],
                    ]);

                    $ids[] = $items['fid'];
                    pft_log('wx/balance_warning/ok', 'jobId:' . $job_id, "month");
                }
            }
        }
        //更新数据
        if ($ids) {
            $Expense->updataSms($ids, 0);
        }
        unset($ids);
    }

    /**
     * 平台账户余额预警通知
     *
     * @return bool
     */
    public function earlyMoneyWarning()
    {
        $page = 1;
        $size = 200;
        while (true) {
            $result = $this->_model->getMemberMoneyEarlyWarningSet($page, $size);
            if ($result == false) {
                break;
            }

            $memberModel = new Member('slave');

            $dateY=date('y');
            $dateM=date('m');
            $dateD=date('d');
            $dateH=date('H');

            $queryParams = [array_keys($result['notifyList'])];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                $queryParams);

            if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                return false;
            }
            $memberInfo = array_column($queryRes['data'], null, 'id');

            foreach ($result['notifyList'] as $fid=>$leftMoney) {
                //$mobile = $memberModel->getMemberCacheById($fid, 'mobile');
                $mobile = $memberInfo[$fid]['mobile'];
                echo $fid,'---',$mobile,"\n";
                $params = [
                    'mobile' => $mobile,
                    'action' => 'earlyMoneyWarning',
                    'params' => [
                        ($result['configSet'][$fid] / 100) . '',
                        $dateY . '',
                        $dateM . '',
                        $dateD . '',
                        $dateH . '',
                        ($leftMoney / 100) . '',
                    ],
                ];
                $job_id = Queue::push('notify', 'SmsNotify_Job', $params);
                pft_log('queue/earlyMoneyWarning', 'jobId:' . $job_id . ';params=' . json_encode($params), "month");
            }

            if ($page * $size >= $result['total']) {
                break;
            }

            $page++;
        }
    }

}