<?php

namespace CrontabTasks\Marketing;

use Library\Controller;

use Business\Mall\AllDis as AllDisBiz;

/**
 * 佣金自动发放
 *
 * @date    2017-05-15
 * <AUTHOR>
 */

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class Alldis extends Controller
{
    /**
     * 发放佣金
     * <AUTHOR>
     * @date   2018-02-01
     */
    public function issue()
    {
        $alldisBiz = new AllDisBiz();
        $alldisBiz->autoIssue();
    }

    /**
     * 发放佣金 每分钟1次
     * <AUTHOR> lingfeng
     */
    public function issueOneMinute()
    {
        $alldisBiz = new AllDisBiz();
        $alldisBiz->issueOneMinute();
    }

    /**
     * 发放佣金 每分钟15次
     * <AUTHOR> lingfeng
     */
    public function issueFifteenMinute()
    {
        $alldisBiz = new AllDisBiz();
        $alldisBiz->issueFifteenMinute();
    }

    /**
     * 把redis里的buy跟look的写入数据库
     * author  leafzl
     * Date:  2018-11-27
     */
    public function persistence()
    {
        $alldisBiz = new AllDisBiz();
        $alldisBiz->persistenceFromRedis();
    }

    public function syncOrderStatus()
    {
        $alldisBiz = new AllDisBiz();
        $alldisBiz->syncOrderStatus();
    }

    /**
     * 同步订单 每分钟1次
     * <AUTHOR> lingfeng
     */
    public function syncOrderStatusOneMinute()
    {
        $alldisBiz = new AllDisBiz();
        $alldisBiz->syncOrderStatusLimit(1);
    }

    /**
     * 同步订单 每5分钟1次
     * <AUTHOR> lingfeng
     */
    public function syncOrderStatusFiveMinute()
    {
        $alldisBiz = new AllDisBiz();
        $alldisBiz->syncOrderStatusLimit(7);
    }

    /**
     * 同步订单 每15分钟1次
     * <AUTHOR> lingfeng
     */
    public function syncOrderStatusFifteenMinute()
    {
        $alldisBiz = new AllDisBiz();
        $alldisBiz->syncOrderStatusFifteenMinute();
    }

    /**
     * 开票 每天0点10分执行
     * @return void
     */
    public function invoicing($_ = '', $__ = '', $___ = '', string $timestamp = '')
    {
        $alldisBiz = new AllDisBiz();
        $alldisBiz->invoicing(ENV === 'PRODUCTION' ? '' : $timestamp);
    }
}
