<?php
/**
 * 推广产品数据无效状态检测校正
 * <AUTHOR>
 * @date 2020/6/1
 */
namespace CrontabTasks\Marketing;

use Library\Controller;
use Model\Product\Ticket;

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

class MultiDistCheckState extends Controller
{
    /**
     * 推广产品数据无效状态检测校正
     * <AUTHOR>
     * @date 2020/6/22
     *
     * @return boolean
     */
    public function checkProductState()
    {
        $distProductBus = new \Business\MultiDist\Product();
        $distProductBus->checkProductState();
        $distProductBus->expireProductState();
        return true;
    }

    /**
     * 用户信息同步
     * <AUTHOR>
     * @date 2020/7/16
     *
     * @return boolean
     */
    public function syncMemberInfo()
    {
        $distMemberLib = new \Business\MultiDist\Member();
        $distMemberLib->syncMemberInfo();
        return true;
    }

    /**
     * 产品价格同步
     * <AUTHOR>
     * @date 2020/8/14
     *
     * @return boolean
     */
    public function syncProductPriceInfo()
    {
        $distMemberLib = new \Business\MultiDist\Product();
        $distMemberLib->syncProductPriceInfo();
        return true;
    }

    /**
     * 分销专员v1权限菜单同步为v1.1
     * <AUTHOR>
     * @date 2020/8/24
     *
     * @return boolean
     */
    public function syncMemberAuthority()
    {
        $distMemberLib = new \Business\MultiDist\Member();
        $distMemberLib->syncMemberAuthority();
        return true;
    }

    private $ticketId = [];

    public function refundOptHandleData1()
    {

        pft_log('refund/refundOptHandleData1', '开始');

        $page = 0;
        $size = 500;

        $ticketModel = new Ticket();
        while (true) {
            $page++;
            pft_log('refund/refundOptHandleData1', 'page:' . $page);
            $filter = [
                't.status' => 1,
                't.refund_rule' => ['IN', [0, 1, 4]],
                'l.p_type' => ['IN', ['A', 'B', 'C', 'F', 'G']]
            ];

            if (!empty($this->ticketId)) {
                $filter['t.id'] = ['IN', $this->ticketId];
            }
            $taskList = $ticketModel
                ->table('uu_jq_ticket t')
                ->join('uu_land as l ON l.id = t.landid', 'LEFT')
                ->where($filter)
                ->field('t.landid,t.status,t.id,t.refund_early_time,t.refund_rule,t.apply_did,t.cancel_cost,t.reb,l.p_type')
                ->page($page, $size)
                ->select();
            if (empty($taskList)) {
                break;
            }

            $params = [];
            //退票规则  0=有效期前X天内可退 1=有效期后X天内可退 2=不可退  3=随时退 -1 表示不可退而且是可以提现
            foreach ($taskList as $value) {
                if ($value['refund_rule'] == 0) {
                    $refundBeforeEarlyTime = 23 * 60 + 59;
                    $params[] = [
                        'ticketId' => $value['id'],
                        'key'      => 'refund_before_early_time',
                        'val'      => (string)$refundBeforeEarlyTime,
                    ];
                }

                if ($value['refund_rule'] == 1) {
                    $oldRefundEarlyTime = $value['refund_early_time'];

                    $newRefundEarlyTime = $this->changeMune($oldRefundEarlyTime);

                    $params[] = [
                        'ticketId' => $value['id'],
                        'key'      => 'refund_before_early_time',
                        'val'      => (string)$newRefundEarlyTime,
                    ];
                }

                if (!empty($value['reb']) || !empty($value['cancel_cost'])) {
                    //有手续费
                    if (!empty($value['reb'])) {
                        $params[] = [
                            'ticketId' => $value['id'],
                            'key'      => 'refund_cost_fixed_type',
                            'val'      => "0",
                        ];
                    }

                    if (!empty($value['cancel_cost'])) {

                        $cancelCost = json_decode($value['cancel_cost'], true);

                        if (is_array($cancelCost)) {
                            $refundCostStepRule = [
                                'step_rule_common' => [
                                    'step_rule_common_rule1' => 0,
                                ]
                            ];

                            $cancelCostNew = [];
                            foreach ($cancelCost as $cancelCostTmp) {
                                $cancelEncdennc = $cancelCostTmp['c_days'];
                                $cancelCostTmp['c_days'] = $this->changeMune($cancelCostTmp['c_days']);

                                $cancelCostNew[$cancelEncdennc] = [
                                    'c_cost' => $cancelCostTmp['c_cost'],
                                    'c_type' => $cancelCostTmp['c_type'],
                                    'c_days' => -$cancelCostTmp['c_days'],
                                ];
                            }

                            krsort($cancelCostNew);
                            $cancelCostNew = array_values($cancelCostNew);

                            foreach ($cancelCostNew as $mmmKey => $kenTmp) {

                                if ($cancelCostNew[$mmmKey - 1]['c_type'] == 0) {
                                    $dmskdcskd = 1;
                                } else {
                                    $dmskdcskd = 0;
                                }

                                if ($mmmKey == 0) {
                                    $refundCostStepRule['step_rule_list'][] = [
                                        'step_rule_list_minu' => $kenTmp['c_days'],
                                        'step_rule_list_value' => 0,
                                        'step_rule_list_type' => 1,
                                    ];
                                } else {
                                    if ($dmskdcskd == 0) {
                                        $cancelCostNew[$mmmKey - 1]['c_cost'] = $cancelCostNew[$mmmKey - 1]['c_cost'] / 100;
                                    }
                                    $refundCostStepRule['step_rule_list'][] = [
                                        'step_rule_list_minu' => $kenTmp['c_days'],
                                        'step_rule_list_value' => $cancelCostNew[$mmmKey - 1]['c_cost'],
                                        'step_rule_list_type' => $dmskdcskd,
                                    ];
                                }

                                $nuew = $mmmKey;
                            }

                            if ($cancelCostNew[$nuew]['c_type'] == 0) {
                                $dmskdcskdss = 1;
                            } else {
                                $dmskdcskdss = 0;
                            }
                            if ($dmskdcskdss == 0) {
                                $cancelCostNew[$nuew]['c_cost'] = $cancelCostNew[$nuew]['c_cost'] / 100;
                            }
                            $refundCostStepRule['step_rule_end'] = [
                                'step_rule_end_value' => $cancelCostNew[$nuew]['c_cost'],
                                'step_rule_list_type' => $dmskdcskdss,
                            ];

                            $params[] = [
                                'ticketId' => $value['id'],
                                'key'      => 'refund_cost_step_rule',
                                'val'      => json_encode($refundCostStepRule, JSON_UNESCAPED_UNICODE),
                            ];
                        }

                    }
                }
            }
            $countNew = count($params);
            pft_log('refund/refundOptHandleData1', "插入数据量:{$countNew}");
            $ticketExtConf = new \Business\JavaApi\Ticket\TicketExtendAttr();
            $extSaveRes    = $ticketExtConf->rawSave($params);
            if ($extSaveRes['code'] != 200) {
                pft_log('refund/error/refundOptHandleData1', json_encode([$page, $params, $extSaveRes], JSON_UNESCAPED_UNICODE));
            }

        }
        pft_log('refund/refundOptHandleData1', '结束');
    }

    public function refundOptHandleData2()
    {

        pft_log('refund/refundOptHandleData2', '开始');

        $page = 0;
        $size = 500;

        $ticketModel = new Ticket();
        while (true) {
            $page++;
            pft_log('refund/refundOptHandleData2', 'page:' . $page);
            $filter = [
                't.status' => 1,
                't.refund_rule' => 3,
                'l.p_type' => ['IN', ['A', 'B', 'C', 'F', 'G']]
            ];

            if (!empty($this->ticketId)) {
                $filter['t.id'] = ['IN', $this->ticketId];
            }
            $taskList = $ticketModel
                ->table('uu_jq_ticket t')
                ->join('uu_land as l ON l.id = t.landid', 'LEFT')
                ->where($filter)
                ->field('t.landid,t.status,t.id,t.refund_early_time,t.refund_rule,t.apply_did,t.cancel_cost,t.reb,l.p_type')
                ->page($page, $size)
                ->select();
            if (empty($taskList)) {
                break;
            }

            $params = [];
            //退票规则  0=有效期前X天内可退 1=有效期后X天内可退 2=不可退  3=随时退 -1 表示不可退而且是可以提现
            foreach ($taskList as $value) {
                if ($value['refund_rule'] == 0) {
                    $refundBeforeEarlyTime = 23 * 60 + 59;
                    $params[] = [
                        'ticketId' => $value['id'],
                        'key'      => 'refund_before_early_time',
                        'val'      => (string)$refundBeforeEarlyTime,
                    ];
                }

                if ($value['refund_rule'] == 1) {
                    $oldRefundEarlyTime = $value['refund_early_time'];

                    $newRefundEarlyTime = $this->changeMune($oldRefundEarlyTime);

                    $params[] = [
                        'ticketId' => $value['id'],
                        'key'      => 'refund_before_early_time',
                        'val'      => (string)$newRefundEarlyTime,
                    ];
                }

                if (!empty($value['reb']) || !empty($value['cancel_cost'])) {
                    //有手续费
                    if (!empty($value['reb'])) {
                        $params[] = [
                            'ticketId' => $value['id'],
                            'key'      => 'refund_cost_fixed_type',
                            'val'      => "0",
                        ];
                    }

                    if (!empty($value['cancel_cost'])) {

                        $cancelCost = json_decode($value['cancel_cost'], true);

                        if (is_array($cancelCost)) {
                            $refundCostStepRule = [
                                'step_rule_common' => [
                                    'step_rule_common_rule1' => 0,
                                ]
                            ];

                            $cancelCostNew = [];
                            foreach ($cancelCost as $cancelCostTmp) {
                                $cancelEncdennc = $cancelCostTmp['c_days'];
                                $cancelCostTmp['c_days'] = $this->changeMune($cancelCostTmp['c_days']);

                                $cancelCostNew[$cancelEncdennc] = [
                                    'c_cost' => $cancelCostTmp['c_cost'],
                                    'c_type' => $cancelCostTmp['c_type'],
                                    'c_days' => -$cancelCostTmp['c_days'],
                                ];
                            }

                            krsort($cancelCostNew);
                            $cancelCostNew = array_values($cancelCostNew);

                            foreach ($cancelCostNew as $mmmKey => $kenTmp) {

                                if ($cancelCostNew[$mmmKey - 1]['c_type'] == 0) {
                                    $dmskdcskd = 1;
                                } else {
                                    $dmskdcskd = 0;
                                }

                                if ($mmmKey == 0) {
                                    $refundCostStepRule['step_rule_list'][] = [
                                        'step_rule_list_minu' => $kenTmp['c_days'],
                                        'step_rule_list_value' => 0,
                                        'step_rule_list_type' => 1,
                                    ];
                                } else {
                                    if ($dmskdcskd == 0) {
                                        $cancelCostNew[$mmmKey - 1]['c_cost'] = $cancelCostNew[$mmmKey - 1]['c_cost'] / 100;
                                    }
                                    $refundCostStepRule['step_rule_list'][] = [
                                        'step_rule_list_minu' => $kenTmp['c_days'],
                                        'step_rule_list_value' => $cancelCostNew[$mmmKey - 1]['c_cost'],
                                        'step_rule_list_type' => $dmskdcskd,
                                    ];
                                }

                                $nuew = $mmmKey;
                            }

                            if ($cancelCostNew[$nuew]['c_type'] == 0) {
                                $dmskdcskdss = 1;
                            } else {
                                $dmskdcskdss = 0;
                            }
                            if ($dmskdcskdss == 0) {
                                $cancelCostNew[$nuew]['c_cost'] = $cancelCostNew[$nuew]['c_cost'] / 100;
                            }
                            $refundCostStepRule['step_rule_end'] = [
                                'step_rule_end_value' => $cancelCostNew[$nuew]['c_cost'],
                                'step_rule_list_type' => $dmskdcskdss,
                            ];

                            $params[] = [
                                'ticketId' => $value['id'],
                                'key'      => 'refund_cost_step_rule',
                                'val'      => json_encode($refundCostStepRule, JSON_UNESCAPED_UNICODE),
                            ];
                        }

                    }
                }
            }

            $countNew = count($params);
            pft_log('refund/refundOptHandleData2', "插入数据量:{$countNew}");
            $ticketExtConf = new \Business\JavaApi\Ticket\TicketExtendAttr();
            $extSaveRes    = $ticketExtConf->rawSave($params);
            if ($extSaveRes['code'] != 200) {
                pft_log('refund/error/refundOptHandleData2', json_encode([$page, $params, $extSaveRes], JSON_UNESCAPED_UNICODE));
            }

        }
        pft_log('refund/refundOptHandleData2', '结束');
    }


    public function refundOptHandleData3()
    {

        pft_log('refund/refundOptHandleData3', '开始');

        $page = 0;
        $size = 500;

        $ticketModel = new Ticket();
        while (true) {
            $page++;
            pft_log('refund/refundOptHandleData3', 'page:' . $page);
            $filter = [
                't.status' => 2,
                't.refund_rule' => ['IN', [0, 1, 4]],
                'l.p_type' => ['IN', ['A', 'B', 'C', 'F', 'G']]
            ];

            if (!empty($this->ticketId)) {
                $filter['t.id'] = ['IN', $this->ticketId];
            }
            $taskList = $ticketModel
                ->table('uu_jq_ticket t')
                ->join('uu_land as l ON l.id = t.landid', 'LEFT')
                ->where($filter)
                ->field('t.landid,t.status,t.id,t.refund_early_time,t.refund_rule,t.apply_did,t.cancel_cost,t.reb,l.p_type')
                ->page($page, $size)
                ->select();
            if (empty($taskList)) {
                break;
            }

            $params = [];
            //退票规则  0=有效期前X天内可退 1=有效期后X天内可退 2=不可退  3=随时退 -1 表示不可退而且是可以提现
            foreach ($taskList as $value) {
                if ($value['refund_rule'] == 0) {
                    $refundBeforeEarlyTime = 23 * 60 + 59;
                    $params[] = [
                        'ticketId' => $value['id'],
                        'key'      => 'refund_before_early_time',
                        'val'      => (string)$refundBeforeEarlyTime,
                    ];
                }

                if ($value['refund_rule'] == 1) {
                    $oldRefundEarlyTime = $value['refund_early_time'];

                    $newRefundEarlyTime = $this->changeMune($oldRefundEarlyTime);

                    $params[] = [
                        'ticketId' => $value['id'],
                        'key'      => 'refund_before_early_time',
                        'val'      => (string)$newRefundEarlyTime,
                    ];
                }

                if (!empty($value['reb']) || !empty($value['cancel_cost'])) {
                    //有手续费
                    if (!empty($value['reb'])) {
                        $params[] = [
                            'ticketId' => $value['id'],
                            'key'      => 'refund_cost_fixed_type',
                            'val'      => "0",
                        ];
                    }

                    if (!empty($value['cancel_cost'])) {

                        $cancelCost = json_decode($value['cancel_cost'], true);

                        if (is_array($cancelCost)) {
                            $refundCostStepRule = [
                                'step_rule_common' => [
                                    'step_rule_common_rule1' => 0,
                                ]
                            ];

                            $cancelCostNew = [];
                            foreach ($cancelCost as $cancelCostTmp) {
                                $cancelEncdennc = $cancelCostTmp['c_days'];
                                $cancelCostTmp['c_days'] = $this->changeMune($cancelCostTmp['c_days']);

                                $cancelCostNew[$cancelEncdennc] = [
                                    'c_cost' => $cancelCostTmp['c_cost'],
                                    'c_type' => $cancelCostTmp['c_type'],
                                    'c_days' => -$cancelCostTmp['c_days'],
                                ];
                            }

                            krsort($cancelCostNew);
                            $cancelCostNew = array_values($cancelCostNew);

                            foreach ($cancelCostNew as $mmmKey => $kenTmp) {

                                if ($cancelCostNew[$mmmKey - 1]['c_type'] == 0) {
                                    $dmskdcskd = 1;
                                } else {
                                    $dmskdcskd = 0;
                                }

                                if ($mmmKey == 0) {
                                    $refundCostStepRule['step_rule_list'][] = [
                                        'step_rule_list_minu' => $kenTmp['c_days'],
                                        'step_rule_list_value' => 0,
                                        'step_rule_list_type' => 1,
                                    ];
                                } else {
                                    if ($dmskdcskd == 0) {
                                        $cancelCostNew[$mmmKey - 1]['c_cost'] = $cancelCostNew[$mmmKey - 1]['c_cost'] / 100;
                                    }
                                    $refundCostStepRule['step_rule_list'][] = [
                                        'step_rule_list_minu' => $kenTmp['c_days'],
                                        'step_rule_list_value' => $cancelCostNew[$mmmKey - 1]['c_cost'],
                                        'step_rule_list_type' => $dmskdcskd,
                                    ];
                                }

                                $nuew = $mmmKey;
                            }

                            if ($cancelCostNew[$nuew]['c_type'] == 0) {
                                $dmskdcskdss = 1;
                            } else {
                                $dmskdcskdss = 0;
                            }
                            if ($dmskdcskdss == 0) {
                                $cancelCostNew[$nuew]['c_cost'] = $cancelCostNew[$nuew]['c_cost'] / 100;
                            }
                            $refundCostStepRule['step_rule_end'] = [
                                'step_rule_end_value' => $cancelCostNew[$nuew]['c_cost'],
                                'step_rule_list_type' => $dmskdcskdss,
                            ];

                            $params[] = [
                                'ticketId' => $value['id'],
                                'key'      => 'refund_cost_step_rule',
                                'val'      => json_encode($refundCostStepRule, JSON_UNESCAPED_UNICODE),
                            ];
                        }

                    }
                }
            }

            $countNew = count($params);
            pft_log('refund/refundOptHandleData3', "插入数据量:{$countNew}");
            $ticketExtConf = new \Business\JavaApi\Ticket\TicketExtendAttr();
            $extSaveRes    = $ticketExtConf->rawSave($params);
            if ($extSaveRes['code'] != 200) {
                pft_log('refund/error/refundOptHandleData3', json_encode([$page, $params, $extSaveRes], JSON_UNESCAPED_UNICODE));
            }

        }
        pft_log('refund/refundOptHandleData3', '结束');
    }


    public function refundOptHandleData4()
    {

        pft_log('refund/refundOptHandleData4', '开始');

        $page = 0;
        $size = 500;

        $ticketModel = new Ticket();
        while (true) {
            $page++;
            pft_log('refund/refundOptHandleData4', 'page:' . $page);
            $filter = [
                't.status' => 2,
                't.refund_rule' => ['IN', [3]],
                'l.p_type' => ['IN', ['A', 'B', 'C', 'F', 'G']]
            ];

            if (!empty($this->ticketId)) {
                $filter['t.id'] = ['IN', $this->ticketId];
            }
            $taskList = $ticketModel
                ->table('uu_jq_ticket t')
                ->join('uu_land as l ON l.id = t.landid', 'LEFT')
                ->where($filter)
                ->field('t.landid,t.status,t.id,t.refund_early_time,t.refund_rule,t.apply_did,t.cancel_cost,t.reb,l.p_type')
                ->page($page, $size)
                ->select();
            if (empty($taskList)) {
                break;
            }

            $params = [];
            //退票规则  0=有效期前X天内可退 1=有效期后X天内可退 2=不可退  3=随时退 -1 表示不可退而且是可以提现
            foreach ($taskList as $value) {
                if ($value['refund_rule'] == 0) {
                    $refundBeforeEarlyTime = 23 * 60 + 59;
                    $params[] = [
                        'ticketId' => $value['id'],
                        'key'      => 'refund_before_early_time',
                        'val'      => (string)$refundBeforeEarlyTime,
                    ];
                }

                if ($value['refund_rule'] == 1) {
                    $oldRefundEarlyTime = $value['refund_early_time'];

                    $newRefundEarlyTime = $this->changeMune($oldRefundEarlyTime);

                    $params[] = [
                        'ticketId' => $value['id'],
                        'key'      => 'refund_before_early_time',
                        'val'      => (string)$newRefundEarlyTime,
                    ];
                }

                if (!empty($value['reb']) || !empty($value['cancel_cost'])) {
                    //有手续费
                    if (!empty($value['reb'])) {
                        $params[] = [
                            'ticketId' => $value['id'],
                            'key'      => 'refund_cost_fixed_type',
                            'val'      => "0",
                        ];
                    }

                    if (!empty($value['cancel_cost'])) {

                        $cancelCost = json_decode($value['cancel_cost'], true);

                        if (is_array($cancelCost)) {
                            $refundCostStepRule = [
                                'step_rule_common' => [
                                    'step_rule_common_rule1' => 0,
                                ]
                            ];

                            $cancelCostNew = [];
                            foreach ($cancelCost as $cancelCostTmp) {
                                $cancelEncdennc = $cancelCostTmp['c_days'];
                                $cancelCostTmp['c_days'] = $this->changeMune($cancelCostTmp['c_days']);

                                $cancelCostNew[$cancelEncdennc] = [
                                    'c_cost' => $cancelCostTmp['c_cost'],
                                    'c_type' => $cancelCostTmp['c_type'],
                                    'c_days' => -$cancelCostTmp['c_days'],
                                ];
                            }

                            krsort($cancelCostNew);
                            $cancelCostNew = array_values($cancelCostNew);

                            foreach ($cancelCostNew as $mmmKey => $kenTmp) {

                                if ($cancelCostNew[$mmmKey - 1]['c_type'] == 0) {
                                    $dmskdcskd = 1;
                                } else {
                                    $dmskdcskd = 0;
                                }

                                if ($mmmKey == 0) {
                                    $refundCostStepRule['step_rule_list'][] = [
                                        'step_rule_list_minu' => $kenTmp['c_days'],
                                        'step_rule_list_value' => 0,
                                        'step_rule_list_type' => 1,
                                    ];
                                } else {
                                    if ($dmskdcskd == 0) {
                                        $cancelCostNew[$mmmKey - 1]['c_cost'] = $cancelCostNew[$mmmKey - 1]['c_cost'] / 100;
                                    }
                                    $refundCostStepRule['step_rule_list'][] = [
                                        'step_rule_list_minu' => $kenTmp['c_days'],
                                        'step_rule_list_value' => $cancelCostNew[$mmmKey - 1]['c_cost'],
                                        'step_rule_list_type' => $dmskdcskd,
                                    ];
                                }

                                $nuew = $mmmKey;
                            }

                            if ($cancelCostNew[$nuew]['c_type'] == 0) {
                                $dmskdcskdss = 1;
                            } else {
                                $dmskdcskdss = 0;
                            }
                            if ($dmskdcskdss == 0) {
                                $cancelCostNew[$nuew]['c_cost'] = $cancelCostNew[$nuew]['c_cost'] / 100;
                            }
                            $refundCostStepRule['step_rule_end'] = [
                                'step_rule_end_value' => $cancelCostNew[$nuew]['c_cost'],
                                'step_rule_list_type' => $dmskdcskdss,
                            ];

                            $params[] = [
                                'ticketId' => $value['id'],
                                'key'      => 'refund_cost_step_rule',
                                'val'      => json_encode($refundCostStepRule, JSON_UNESCAPED_UNICODE),
                            ];
                        }

                    }
                }
            }

            $countNew = count($params);
            pft_log('refund/refundOptHandleData4', "插入数据量:{$countNew}");
            $ticketExtConf = new \Business\JavaApi\Ticket\TicketExtendAttr();
            $extSaveRes    = $ticketExtConf->rawSave($params);
            if ($extSaveRes['code'] != 200) {
                pft_log('refund/error/refundOptHandleData4', json_encode([$page, $params, $extSaveRes], JSON_UNESCAPED_UNICODE));
            }

        }
        pft_log('refund/refundOptHandleData4', '结束');
    }


    public function changeMune($oldRefundEarlyTime)
    {
        $tmpDay = intval($oldRefundEarlyTime / 1440);
        $lastTmp = 1440 - ($oldRefundEarlyTime - $tmpDay * 1440);
        $tmphour = intval($lastTmp / 60);
        $tmpminute = intval($lastTmp - $tmphour * 60);

        $newRefundEarlyTime = $tmpDay * 1440 + $tmphour * 60 + $tmpminute;
        return $newRefundEarlyTime;
    }

}