<?php
/*
 * 优惠券相关的订单任务
 *
 * <AUTHOR>
 * @date   2018-11-16
 */
namespace CrontabTasks\Marketing;

use Library\Controller;
use Library\Hashids\SmsHashids;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class Coupon extends Controller
{
    private $_sendCouponLog = 'coupon_send';
    private $_useCouponLog = 'coupon/use';

    /**
     * 发放优惠券
     *   pft_send_coupon_log status=1的数据进行任务处理发放优惠券
     *
     * <AUTHOR>
     * @date   2018-11
     *
     */
    public function sendUserCoupon()
    {
        $couponModel = new \Model\Product\Coupon('remote_1');
        $couponOrderModel= new \Model\Order\Coupon('remote_1');
        $sendCouponLog = $couponModel->getSendCouponLogByStatus(1, 1, 1000, 'id, fid, sid, activity_id, coupon_id, num, remarks');
        if (empty($sendCouponLog)) {
            return false;
        }

        $couponIdArr = array_column($sendCouponLog, 'coupon_id');
        $couponInfoArr = $couponModel->getCouponByIds($couponIdArr, 'id, use_startdate, use_enddate, condition, collect_days, apply_did, effective_type');
        if (empty($couponIdArr)) {
            return false;
        }

        // 处理优惠券信息
        $couponDataArr = [];
        foreach ($couponInfoArr as $couponItem) {
            $couponDataArr[$couponItem['id']] = $couponItem;
        }

        // 实例化加密函数
        $hashIds = new SmsHashids();
        // 记录更新成功的发券记录id
        $successLogIdArr = [];


        // 循环插入优惠券数据
        pft_log("coupon/info", '待发券数据----'.print_r($sendCouponLog, true).PHP_EOL);
        foreach ($sendCouponLog as $item) {

            // 没有优惠券数据或者优惠券数据不存在的清空
            if (!isset($couponDataArr[$item['coupon_id']]) || empty($couponDataArr[$item['coupon_id']])) {
                continue;
            }

            $startDate = $couponDataArr[$item['coupon_id']]['use_startdate'];
            $endDate   = $couponDataArr[$item['coupon_id']]['use_enddate'];

            if ($couponDataArr[$item['coupon_id']]['effective_type'] == 0 || $couponDataArr[$item['coupon_id']]['effective_type'] == 2) {
                $startDate = $endDate = date('Y-m-d');
            }

            //得到优惠券后多少天有效
            if ($couponDataArr[$item['coupon_id']]['effective_type'] == 4 || $couponDataArr[$item['coupon_id']]['effective_type'] == 5) {
                $startDate = date('Y-m-d', time());
                $endDate   = date('Y-m-d', time() + $couponDataArr[$item['coupon_id']]['collect_days']*86400);
            }

            $orderNum = '';
            if (!empty($item['remarks'])) {
                $remarksArr = json_decode($item['remarks'], true);
                if (is_array($remarksArr)) {
                    $orderNum = $remarksArr['order_num'];
                }
            }

            if (!empty($orderNum)) {
                // 查询订单状态 ： 非正常的订单不能进行领取优惠券
                // 查询订单状态 ： 非正常的订单不能进行领取优惠券
                $biz = new \Business\Order\MergeOrder();
                if ($biz->isCombineOrder($orderNum)) {
                    // 获取关联的订单号
                    $model         = new \Model\Order\Payment();
                    $orderList     = $model->getCombileLogByTradeId($orderNum);
                    if (empty($orderList)) {
                        continue;
                    }
                    //获取合并付款的所有订单
                    $orderNumArr = array_column($orderList, 'ordernum');
                } else {
                    $orderNumArr = [$orderNum];
                }

                $orderToolModel = new \Model\Order\OrderTools('slave');
                $orderInfoArr = $orderToolModel->getOrderInfo($orderNumArr, 'member,status,pay_status');
                if (empty($orderInfoArr)) {
                    $couponModel->setSendCouponLogStatus([$item['id']], 3);
                    pft_log($this->_sendCouponLog, $item['coupon_id'].'#'.$orderNum.'#领取失败,订单号有误', 2);
                    continue;
                }

                foreach ($orderInfoArr as $orderItem) {
                    if ($orderItem['pay_status'] != 1) {
                        $couponModel->setSendCouponLogStatus([$item['id']], 3);
                        pft_log($this->_sendCouponLog, $item['coupon_id'].'#'.$orderNum.'#领取失败,未支付订单不可领取', 2);
                        continue;
                    }
                    if ($orderItem['status'] == 3) {
                        $couponModel->setSendCouponLogStatus([$item['id']], 3);
                        pft_log($this->_sendCouponLog, $item['coupon_id'].'#'.$orderNum.'#领取失败,已取消订单不可领取', 2);
                        continue;
                    }
                }
            }

            $arrayCollect = array(
                'fid'         => $item['fid'],
                'aid'         => $item['sid'],
                'coupon_id'   => $item['coupon_id'],
                'dstatus'     => 0,
                'start_date'  => $startDate,
                'end_date'    => $endDate,
                'ordernum'    => $orderNum,
                'create_time' => date('Y-m-d H:i:s', time()),
                'active_id'   => $item['activity_id'],
            );

            //这里可以批量插入，但是批量更新优惠码得再思考
            $lastId = false;
            $res    = false;

            for ($i=0; $i < $item['num']; $i++) {
                pft_log("coupon/info", '发券数据数据----'.print_r($sendCouponLog, true).PHP_EOL);
                $lastId = $couponOrderModel->insertCoupon($arrayCollect);
                if ($lastId) {
                    $couponCode = $hashIds->encode($lastId . $item['coupon_id'] . $item['fid']);
                    $res = $couponOrderModel->setCoupon($lastId, $couponCode);
                    if (!$res) {
                        pft_log($this->_sendCouponLog, $lastId.'#'.$couponCode.'#更新优惠券唯一码失败', 2);
                    }

                    // 不在成功的数组中不去更新
                    if (!in_array($item['id'], $successLogIdArr)) {
                        // 记录更新成功
                        $successLogIdArr[] = $item['id'];
                        // 每十条更新一次
                        if (count($successLogIdArr) > 9) {
                            $setSendLogRes = $couponModel->setSendCouponLogStatus($successLogIdArr, 2, true);
                            $successLogIdArr = [];
                            // 记录更新失败的日志 -- 顺便可以报警下,可能会重发优惠券
                            if (!$setSendLogRes) {
                                pft_log($this->_sendCouponLog, '更新发券记录表失败:' . json_encode($successLogIdArr), 2);
                            }
                        }
                    }
                } else {
                    pft_log($this->_sendCouponLog, $item['coupon_id'].'#'.$orderNum.'#领取失败:' . json_encode($arrayCollect), 2);
                }
            }
        }

        // 少于十条的更新一次
        if (!empty($successLogIdArr)) {
            $setSendLogRes = $couponModel->setSendCouponLogStatus($successLogIdArr, 2, true);
            $successLogIdArr = [];
            // 记录更新失败的日志 -- 顺便可以报警下,可能会重发优惠券
            if (!$setSendLogRes) {
                pft_log($this->_sendCouponLog, '更新发券记录表失败:' . json_encode($successLogIdArr), 2);
            }
        }
    }

    /**
     * 根据优惠券使用记录表更新用户优惠券记录
     * <AUTHOR>
     * @date 2018-11
     *
     */
    public function useCoupon()
    {
        $orderCouponModel = new \Model\Order\Coupon('remote_1');
        // 已使用 未同步的优惠码进行同步到 pft_member_coupon
        $yesterdayBeginTime = strtotime(date('Y-m-d',strtotime('-1 day')));
        $coponUseLogListArr = $orderCouponModel->getCouponUseLogListByStatusAndAsyncStatus(2, 1, $yesterdayBeginTime, 1, 1000, 'fid, aid, use_order_num, tradeid, coupon_code');
        if (empty($coponUseLogListArr)) {
            return false;
        }

        $useCouponCodeArr = [];
        foreach ($coponUseLogListArr as $item) {
            $couponCodeArr = explode(',', $item['coupon_code']);
            foreach ($couponCodeArr as $couponCode) {
                $useCouponCodeArr[$couponCode]['orderNum'][] = $item['use_order_num'];
                $useCouponCodeArr[$couponCode]['tradeid'] = $item['tradeid'];
                $useCouponCodeArr[$couponCode]['fid'] = $item['fid'];
                $useCouponCodeArr[$couponCode]['orderNum'] = array_unique($useCouponCodeArr[$couponCode]['orderNum']);
            }
        }

        $successUpTradeIdArr = [];
        $successCouponCodeArr = [];
        foreach ($useCouponCodeArr as $useCouponKey => $useCouponCode) {
            $orderStr = implode(',', $useCouponCode['orderNum']);
            // 记录优惠券使用合并付款的交易订单号
            $upRes = $orderCouponModel->updateCouponByCouponCode($useCouponCode['tradeid'], $useCouponKey, $useCouponCode['fid']);
            if ($upRes) {
                $successUpTradeIdArr[] = $useCouponCode['tradeid'];
                $successCouponCodeArr[] = $useCouponKey;
            } else {
                // 失败记录日志
                pft_log($this->_useCouponLog, '更新失败:' . json_encode([$useCouponCode['tradeid'], $useCouponKey, $useCouponCode['fid']]));
            }
        }
        if (!empty($successUpTradeIdArr)) {
            $orderCouponModel->setUseCouponAsyncStatusByTradeIds($successUpTradeIdArr);
        }

        // 更新优惠券扩展信息的使用张数
        if (!empty($successCouponCodeArr)) {
            // 更新发行券的使用数量
            $allCouponInfoArr = $orderCouponModel->getUseCoupon('coupon_code', $successCouponCodeArr, 'id,coupon_id,coupon_code');
            if (!empty($allCouponInfoArr)) {
                $allCouponUseInfoArr = [];

                $productCouponModel = new \Model\Product\Coupon('remote_1');
                foreach ($allCouponInfoArr as $infoItem) {
                    $allCouponUseInfoArr[$infoItem['coupon_id']] += 1;
                }
                foreach ($allCouponUseInfoArr as $useKey => $useItem) {
                    $productCouponModel->incCouponUsedNum($useKey, $useItem);
                }
            }
        }
    }

    /**
     * 根据优惠券开始结束时间，自动更新状态
     * @Author: zhujb
     * 2018/11/19
     */
    public function scanCouponStatus()
    {
        // 当前时间
        $curTimestamp = time();
        $inHandIds = [];
        $inEndIds = [];
        $marketModel = new \Model\Market\Market();

        // 1:搜索未开始活动
        $noBeginMarkerList = $marketModel->getNoBeginMarketing('', $page=1, $pageSize=100, $field='id,activity_bt,activity_et,activity_status');
        if (!empty($noBeginMarkerList)) {
            $inHandIds = array_column($noBeginMarkerList, 'id');
        }

        // 更新进行中
        if (!empty($inHandIds)) {
            $marketModel->updateMarketing($inHandIds, ['activity_status'=>3]);
        }

        // 2:搜索未结束活动
        $noEndMarkerList = $marketModel->getNoOverMarketing('', $page=1, $pageSize=100, $field='id,activity_bt,activity_et,activity_status');
        if (!empty($noEndMarkerList)) {
            $inEndIds = array_column($noEndMarkerList, 'id');
        }

        // 更新已结束
        if (!empty($inEndIds)) {
            $marketModel->updateMarketing($inEndIds, ['activity_status'=>4]);
        }

        // 3:使用记录中占用的优惠券 订单已经取消的进行释放优惠券 -- 只释放当天的
        $orderCouponModel = new \Model\Order\Coupon('remote_1');
        $yesterdayBeginTime = strtotime(date('Y-m-d',strtotime('-1 day')));
        $coponUseLogListArr = $orderCouponModel->getCouponUseLogListByStatusAndAsyncStatus(1, 1, $yesterdayBeginTime, 1, 100, 'fid, aid, use_order_num, tradeid, coupon_code');

        if (!empty($coponUseLogListArr)) {
            $useOrderOrderNumArr = array_column($coponUseLogListArr, 'use_order_num');

            $cashPayArr = [];         // 现金支付的错误数据订单
            $noPayExpireArr = [];     // 未支付没自动取消过期的订单
            $noPayHavaCancelArr = []; // 已经取消的订单
            // 获取里面不需要继续跑脚本的数据
            $orderQueryModel = new \Model\Order\OrderTools();
            $orderInfoArr = $orderQueryModel->getOrderInfo($useOrderOrderNumArr, 'pay_status, ordernum, paymode, status');
            if (!empty($orderInfoArr)) {
                foreach ($orderInfoArr as $orderItem) {
                    if ($orderItem['status'] == 3 && $orderItem['pay_status'] == 2) {
                        $noPayHavaCancelArr[] = $orderItem['ordernum'];
                    }
                    if (in_array($orderItem['status'], [2, 8]) && $orderItem['pay_status'] == 2) {
                        // 未支付运行了过期完结的
                        $noPayExpireArr[] = $orderItem['ordernum'];
                    }
                    // 现金支付和现场支付
                    if ($orderItem['paymode'] == 9 || $orderItem['pay_status'] == 0) {
                        $cashPayArr[] = $orderItem['ordernum'];
                    }
                }

                if (!empty($noPayHavaCancelArr)) {
                    $orderCouponModel->setUseCouponStatusByUseOrder($noPayHavaCancelArr, 1, 3);
                }
                if (!empty($cashPayArr)) {   // 更新成状态为 4 的异常现金支付
                    $orderCouponModel->setUseCouponStatusByUseOrder($cashPayArr, 1, 4);
                }
                if (!empty($noPayExpireArr)) {
                    $orderCouponModel->setUseCouponStatusByUseOrder($noPayExpireArr, 1, 3);
                }
            }
        }

        // 4:优惠券固定有效期的  过期自动下架
        $couponModel = new \Model\Product\Coupon('remote_1');
        $expireCouponArr = $couponModel->expireCouponListByValidDate(1, date('Y-m-d H:i:s', time()));
        if (!empty($expireCouponArr)) {
            $expireCouponIdArr = array_column($expireCouponArr, 'id');
            $couponModel->setCouponStatusByCoupons($expireCouponIdArr, 1);
            // 自动解绑
            $marketModel->setMarketCouponStatusByCouponIds($expireCouponIdArr, 0);
        }
    }
}
