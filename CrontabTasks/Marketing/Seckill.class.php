<?php

namespace CrontabTasks\Marketing;

use Business\Order\Modify;
use Library\Constants\OrderConst;
use Library\Controller;
use Model\Mall\Seckill as Model;
use Model\Order\OrderTools;
use Business\Wechat\Seckill as SeckillBiz;

/**
 * 抢购超时自动取消
 * 
 * @date    2017-05-15
 * <AUTHOR>
 */

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}


class Seckill extends Controller {

    public function __construct() {
        //初始化价格模型
        $this->_seckillModel = new Model();
    }


    /**
     * 自动取消订单
     * 
     * <AUTHOR>
     * @date   2017-05-15
     */
    public function cancel() {

        //获取订单
        $list = $this->_seckillModel->getOrderToCancel();

        if ($list) {
            $modifyBiz = new Modify();
            $idArr  = array_unique(array_column($list, 'seckill_id'));
            //订单状态判断
            $orderArr = array_column($list, 'ordernum');
//            $orderModel = new OrderTools('slave');
//            $payMap = $orderModel->getOrderPayStatus($orderArr);
            $queryParams = [$orderArr,true,'pay_status'];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder','queryOrderInfoByOrdernumList', $queryParams);
            $payMap = [];
            if ($queryRes['code'] == 200) {
                $payMap = $queryRes['data'];
            }
            //获取结束时间
            $seckills = $this->_seckillModel->getSeckillMulti($idArr, 'id,cancel_time');

            foreach ($list as $orderid => $detail) {

                if ($payMap[$orderid] != 2) {
                    continue;
                }
                if (!isset($seckills[$detail['seckill_id']])) {
                    continue;
                }
                if ($seckills[$detail['seckill_id']] == -1) {
                    continue;
                }
                if (time() - $detail['create_time'] < $seckills[$detail['seckill_id']] * 60) {
                    continue;
                }
                $res  = $modifyBiz->cancelParamsCommonFormat($orderid,1,OrderConst::SECKILL_SYSTEM_CANCEL);
                pft_log('seckill/cancel', $orderid . '|' . json_encode($res), 'day');
            }
        }

    }


    /**
     * 抢购提醒
     * <AUTHOR>
     * @date   2017-12-28
     */
    public function warning() {

        $seckillBiz = new SeckillBiz();
        $seckillBiz->sendWarning();
    }

}