<?php
/**
 * 分销专员队列失败重试脚本
 * <AUTHOR>
 * @date 2020/4/27
 */

namespace CrontabTasks\Marketing;

use Library\Controller;
use Model\MultiDist\Member;

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

class MultiDistQueueRetry extends Controller
{

    private $_multiDistTradeBiz;

    private $_distProductBus;

    private $_distMemberModel;

    private $_distProductModel;

    /**
     * 队列失败重试
     * <AUTHOR>
     * @date 2020/4/27
     *
     * @return boolean
     */
    public function queueFailRetry()
    {
        $result  = false;
        $page    = 1;
        $size    = 500;//每次取500条数据处理
        $memberModel = new Member();
        $startTime = strtotime('-2 days');
        $endTime   = time();
        //分批量处理数据
        while (true) {
            $data      = $memberModel->getQueueFail($startTime, $endTime, $page, $size);
            if (!$data) {
                break;
            }

            foreach ($data as $value) {
                //请求
                $actions = $memberModel->getQueueActionMapByInt($value['action_type']);
                //参数
                $params  =  json_decode($value['params'], true);

                //订单号
                $ordernum       = isset($params['ordernum']) ? $params['ordernum'] : '';
                //订单过期后的自动处理方式， 2=完结 3=验证 4=取消
                $expireAction   = isset($params['expire_action']) ? $params['expire_action'] : '';
                //用户id
                $memberId       = isset($params['mid']) ? $params['mid'] : '';
                //要处理的票ID
                $tid            = isset($params['tid']) ? $params['tid'] : '';
                //产品更新同步类型
                $productType    = isset($params['productType']) ? $params['productType'] : '';
                //上级供应商ID
                $sid            = isset($params['sid']) ? $params['sid'] : '';
                //要更新的产品数据
                $productData    = isset($params['productData']) ? $params['productData'] : '';
                //转分销特殊数据
                $disData        = isset($params['data']) ? $params['data'] : [];
                //父ID
                $parentId       = isset($params['parent_id']) ? $params['parent_id'] : '';
                //子ID
                $sonId          = isset($params['son_id']) ? $params['son_id'] : '';
                //分销专员等级
                $level          = isset($params['level']) ? $params['level'] : '';
                //商品ID
                $gid          = isset($params['gid']) ? $params['gid'] : '';
                //状态
                $state    = isset($params['state']) ? $params['state'] : '';

                //分销专员业务订单支付的佣金计算
                if ('order' == $actions) {
                    $result = $this->_order($ordernum);
                }

                //分销专员业务订单验证的佣金计算
                if ('check' == $actions) {
                    $result = $this->_check($ordernum);
                }

                //分销专员业务订单验证的佣金计算
                if ('cancel' == $actions) {
                    $result = $this->_cancel($ordernum);
                }

                //分销专员业务订单验证的佣金计算
                if ('expire' == $actions) {
                    $result = $this->_expire($ordernum, $expireAction);
                }

                //分销专员业务订单完结订单（已过期的订单）
                if ('finsh' == $actions) {
                    $result = $this->_finish($ordernum);
                }

                //分销专员自供应产品数据初始化
                if ($actions == 'init_product') {
                    $result = $this->_initProductPull($memberId);

                }

                ////分销专员自供应产品数据初始化
                //if ($actions == 'init_self') {
                //    $result = $this->_initSelf($memberId);
                //
                //}
                //
                ////分销专员转分销产品数据初始化
                //if ($actions == 'init_dist') {
                //    $result = $this->_initDist($memberId);
                //}

                //分销专员资源中心产品数据初始化
                if ($actions == 'init_resource') {
                    $result = $this->_initResource($memberId);
                }

                //分销专员自供应上/下架数据同步
                if ($actions == 'updata_self') {
                    $result = $this->_updataSelf($memberId, $tid, $productType);
                }

                //分销专员自供应产品名称/票名称数据同步
                if ($actions == 'updata_self_data') {
                    $result = $this->_updataSelfData($memberId, $tid, $productData,$productType);
                }

                //特产商品数据同步
                if ($actions == 'specialty_product') {
                    $result = $this->_specialtyProduct($gid, $memberId, $state);
                }

                //分销专员转分销开启/关闭数据同步
                if ($actions == 'updata_dis') {
                    $result = $this->_updataDis($disData);
                }

                //分销专员分销关系删除数据同步
                if ($actions == 'del_dis') {
                    $result = $this->_delDis($parentId, $sonId);
                }

                //分销专员资源中心采购/取消采购数据同步
                if ($actions == 'updata_resource') {
                   // $result = $this->_updataResource($tid, $sid, $memberId,$productType);
                }

                //运营商开通功能，
                if ($actions == 'module_open') {
                    $result = $this->_moduleOpenInit($memberId);
                }

                //菜单权限开通，
                if ($actions == 'menu_open') {
                    $result = $this->_menuOpenInit($memberId, $level);
                }

                if ($result) {
                    //处理成功
                    $memberModel->modQueueFail(['id' => $value['id']], ['state' => 2, 'times' => ['exp', 'times+1']]);
                } else {
                    //处理失败
                    $memberModel->modQueueFail(['id' => $value['id']], ['times' => ['exp', 'times+1']]);
                }
            }

            $page++;
            if ($page > 1000) {
                break;
            }
        }

        return $result;
    }


    /**
     * 运营商开通分销专员功能
     * <AUTHOR>
     * @date    2020-04-03
     *
     * @param   integer  $mid   用户id
     * @return  boolean
     *
     */
    private function _moduleOpenInit($mid)
    {
        $res = (new \Business\MultiDist\Member($mid))->topChain();
        if ($res['code'] != 200) {
            return false;
        }
        return true;
    }

    /**
     * 菜单权限开通分销专员功能
     * <AUTHOR>
     * @date 2020/5/23
     *
     * @param int $mid 用户id
     * @param int $level 等级
     *
     * @return boolean
     */
    private function _menuOpenInit($mid, $level)
    {
        $authContextBus = new \Business\Authority\AuthContext();
        $authContextBus->setMarkSourceFunc('MultiDistQueueRetry/_menuOpenInit');
        $authMenuRes = $authContextBus->multiDistUserOpen($mid, $level);
        if (!$authMenuRes) {
            return false;
        }
        return true;
    }


    /**
     * 下单时的参数检测，是否是分销专员推广的订单
     * <AUTHOR>
     * @date    2020-04-03
     *
     * @param   string  $orderNum   订单号
     * @return  boolean|string
     *
     */
    private function _orderParamsCheck($orderNum)
    {
        //订单下单时的请求参数
        $orderParams = (new \Model\Order\OrderLog())->getOrderParams($orderNum);
        if (!$orderParams) {
            return false;
        }

        $orderParams = json_decode($orderParams, true);
        $paramKey = \Business\MultiDist\Trade::ORDER_PARAM_KEY;

        //是否有带分销专员推广的chain_uuid参数
        $orderChainUuid = isset($orderParams[$paramKey]) ? $orderParams[$paramKey] : '';
        if (!$orderChainUuid) {
            return false;
        }

        return $orderParams[$paramKey];
    }


    /**
     * 下单支付后的佣金结算流程
     * <AUTHOR>
     * @date    2020-04-03
     *
     * @param   string  $orderNum   订单号
     * @return  boolean
     *
     */
    private function _order($orderNum)
    {
        if (!$orderNum) {
            return false;
        }
        //订单下单时的请求参数
        $orderChainUuid = $this->_orderParamsCheck($orderNum);
        if (!$orderChainUuid) {
            return true;
        }

        $res = $this->_getMultiDistTradeBiz()->orderPay($orderNum, $orderChainUuid);
        return $res;
    }


    /**
     * 验证核销后的佣金结算流程
     * <AUTHOR>
     * @date    2020-04-03
     *
     * @param   string      $orderNum         订单号
     * @return  boolean
     *
     */
    private function _check($orderNum, $isFinish = false)
    {
        if (!$orderNum) {
            return false;
        }
        $isMultiDistOrder = (new \Model\MultiDist\Trade())->isMultiDistOrder($orderNum);
        if (!$isMultiDistOrder) {
            return true;
        }

        $res = $this->_getMultiDistTradeBiz()->orderCheck($orderNum, $isFinish);
        return $res;
    }


    /**
     * 订单取消后的佣金结算流程
     * <AUTHOR>
     * @date    2020-04-03
     *
     * @param   string      $orderNum       订单号
     * @return  boolean
     *
     */
    private function _cancel($orderNum)
    {
        if (!$orderNum) {
            return false;
        }
        $isMultiDistOrder = (new \Model\MultiDist\Trade())->isMultiDistOrder($orderNum);
        if (!$isMultiDistOrder) {
            return true;
        }

        $res = $this->_getMultiDistTradeBiz()->orderCancel($orderNum);
        return $res;
    }


    /**
     * 订单完结后的佣金结算流程
     * <AUTHOR>
     * @date    2020-04-03
     *
     * @param   string      $orderNum       订单号
     * @return  boolean
     *
     */
    private function _finish($orderNum)
    {
        return $this->_check($orderNum, true);
    }


    /**
     * 订单自动过期后的处理
     * <AUTHOR>
     * @date    2020-04-03
     *
     * @param   string      $orderNum       订单号
     * @param   string      $action         过期后的处理方式，2=完结 3=验证 4=取消  (与order_expire_task表action_type字段值一致)
     * @return  boolean
     *
     */
    private function _expire($orderNum, $action)
    {
        $res = false;
        if (!$orderNum || !in_array($action, [2, 3, 4])) {
            return $res;
        }

        //完结
        if (2 == $action) {
            $res = $this->_finish($orderNum);
        }

        //验证
        if (3 == $action) {
            $res = $this->_check($orderNum);
        }

        //取消
        if (4 == $action) {
            $res = $this->_cancel($orderNum);
        }

        return $res;
    }


    /**
     * 分销专员交易业务类
     * @return \Business\MultiDist\Trade
     */
    private function _getMultiDistTradeBiz()
    {
        if (!$this->_multiDistTradeBiz) {
            $this->_multiDistTradeBiz = new \Business\MultiDist\Trade();
        }

        return $this->_multiDistTradeBiz;
    }

    /**
     * 分销专员产品数据初始化
     * <AUTHOR>
     * @date 2020/9/23
     *
     * @param  int  $mid 用户ID
     *
     * @return array
     */
    private function _initProductPull(int $mid)
    {
        if (!$mid) {
            return false;
        }

        $distProductBus   = $this->_distProductBus();

        $result = $distProductBus->syncProductPull($mid);
        if ($result['code'] != 200) {
            return false;
        }

        return true;
    }

    ///**
    // * 分销专员自供应产品数据初始化
    // * <AUTHOR>
    // * @date 2020/4/9
    // *
    // * @param  int  $mid  用户ID
    // *
    // * @return boolean
    // */
    //private function _initSelf(int $mid)
    //{
    //    if (!$mid) {
    //        return false;
    //    }
    //
    //    $distProductBus   = $this->_distProductBus();
    //    $distMemberModel  = $this->_distMemberModel();
    //
    //    $logPath = 'multi_dist/debug/initSelf';
    //    $dinName = '自供应产品同步失败';
    //    $isRun   = true;//运行标识初始化（分批量处理数据，当数据处理完后赋值为false）
    //    $page    = 1;
    //    $size    = 100;//每次取100条数据处理
    //
    //    pft_log($logPath, '开始同步|mid:' . $mid);
    //
    //    //获取分销专员数据
    //    $membeInfo = $distMemberModel->getChainByUid($mid, 0);
    //    if (!$membeInfo) {
    //        pft_log($logPath, '获取分销专员数据失败|mid:' . $mid);
    //        return false;
    //    }
    //
    //    $uuid = $membeInfo[0]['uuid'];
    //    $error = false;
    //
    //    $distMemberModel->startTrans();
    //    //分批量处理数据
    //    while ($isRun) {
    //        //100ms后再次获取
    //        usleep(100000);
    //
    //        $syncRes = $distProductBus->syncSelf($mid, $uuid, $page, $size);
    //        if ($syncRes['code'] != 200) {
    //            $error = true;
    //            $isRun = false;
    //        }
    //
    //        //判断数据是否已经处理完，处理完后终止程序
    //        if (!$syncRes['data']['is_more']) {
    //            $isRun = false;
    //        }
    //
    //        $page++;
    //        if ($page > 100) {
    //            $error = true;
    //            $isRun = false;
    //        }
    //    }
    //    $syncResJson = json_encode($syncRes, JSON_UNESCAPED_UNICODE);
    //    if ($error) {
    //        pft_log($logPath,
    //            '同步失败|mid:' . $mid . '|syncRes:' . $syncResJson);
    //        $distProductBus->dingWarn($dinName, [$mid], 'init_product');
    //        $distMemberModel->rollback();
    //    } else {
    //        $distMemberModel->commit();
    //    }
    //
    //    pft_log($logPath, '同步结束|mid:' . $mid);
    //
    //    if ($error) {
    //        return false;
    //    }
    //
    //    $distProductBus::pushStatisProduct($uuid);
    //
    //    return true;
    //}

    ///**
    // * 分销专员转分销产品数据初始化
    // * <AUTHOR>
    // * @date 2020/4/9
    // *
    // * @param  int  $mid  用户ID
    // *
    // * @return boolean
    // */
    //private function _initDist(int $mid)
    //{
    //    if (!$mid) {
    //        return false;
    //    }
    //
    //    $distProductBus   = $this->_distProductBus();
    //    $distMemberModel  = $this->_distMemberModel();
    //
    //    $logPath = 'multi_dist/debug/initDist';
    //    $dinName = '转分销产品同步失败';
    //    $isRun   = true;//运行标识初始化（分批量处理数据，当数据处理完后赋值为false）
    //    $page    = 1;
    //    $size    = 20;//每次取20条数据处理
    //
    //    pft_log($logPath, '开始同步|mid:' . $mid);
    //
    //    //获取分销专员数据
    //    $membeInfo = $distMemberModel->getChainByUid($mid, 0);
    //    if (!$membeInfo) {
    //        pft_log($logPath, '获取分销专员数据失败|mid:' . $mid);
    //        return false;
    //    }
    //
    //    $uuid = $membeInfo[0]['uuid'];
    //    $error = false;
    //
    //    $distMemberModel->startTrans();
    //    //分批量处理数据
    //    while ($isRun) {
    //
    //        //100ms后再次获取
    //        usleep(100000);
    //        $syncRes = $distProductBus->syncDist($mid, $uuid, $page, $size);
    //
    //        if ($syncRes['code'] != 200) {
    //            $error = true;
    //            $isRun = false;
    //        }
    //
    //        //判断数据是否已经处理完，处理完后终止程序
    //        if (!$syncRes['data']['is_more']) {
    //            $isRun = false;
    //        }
    //
    //        $page++;
    //        if ($page > 200) {
    //            $error = true;
    //            $isRun = false;
    //        }
    //    }
    //    $syncResJson = json_encode($syncRes, JSON_UNESCAPED_UNICODE);
    //    if ($error) {
    //        pft_log($logPath,
    //            '同步失败|mid:' . $mid . '|syncRes:' . $syncResJson);
    //        $distProductBus->dingWarn($dinName, [$mid], 'init_product');
    //        $distMemberModel->rollback();
    //    } else {
    //        $distMemberModel->commit();
    //    }
    //
    //    pft_log($logPath, '同步结束|mid:' . $mid);
    //
    //    if ($error) {
    //        return false;
    //    }
    //
    //    $distProductBus::pushStatisProduct($uuid);
    //
    //    return true;
    //}

    /**
     * 分销专员资源中心产品数据初始化
     * <AUTHOR>
     * @date 2020/4/9
     *
     * @param  int  $mid  用户ID
     *
     * @return boolean
     */
    private function _initResource(int $mid)
    {
        if (!$mid) {
            return false;
        }

        $distProductBus   = $this->_distProductBus();
        $distMemberModel  = $this->_distMemberModel();

        $logPath = 'multi_dist/debug/initResource';
        $dinName = '资源中心产品同步失败';
        $isRun   = true;//运行标识初始化（分批量处理数据，当数据处理完后赋值为false）
        $page    = 1;
        $size    = 50;//每次取50条数据处理

        pft_log($logPath, '开始同步|mid:' . $mid);

        //获取分销专员数据
        $membeInfo = $distMemberModel->getChainByUid($mid, $distMemberModel::TOP_LEVEL);
        if (!$membeInfo) {
            pft_log($logPath, '获取分销专员数据失败|mid:' . $mid);
            return false;
        }

        $uuid = $membeInfo[0]['uuid'];
        $error = false;

        $distMemberModel->startTrans();
        //分批量处理数据
        while ($isRun) {
            //100ms后再次获取
            usleep(100000);

            $syncRes = $distProductBus->syncResource($mid, $uuid, $page, $size);
            if ($syncRes['code'] != 200) {
                $error = true;
                $isRun = false;
            }

            //判断数据是否已经处理完，处理完后终止程序
            if (!$syncRes['data']['is_more']) {
                $isRun = false;
            }

            $page++;
            if ($page > 100) {
                $error = true;
                $isRun = false;
            }
        }

        $syncResJson = json_encode($syncRes, JSON_UNESCAPED_UNICODE);
        if ($error) {
            pft_log($logPath,
                '同步失败|mid:' . $mid . '|syncRes:' . $syncResJson);
            $distProductBus->dingWarn($dinName, [$mid], 'init_product');
            $distMemberModel->rollback();
        } else {
            $distMemberModel->commit();
        }

        pft_log($logPath, '同步结束|mid:' . $mid);

        if ($error) {
            return false;
        }

        $distProductBus::pushStatisProduct($uuid);

        return true;
    }

    /**
     * 自供应商上/下架数据同步
     * <AUTHOR>
     * @date 2020/4/11
     *
     * @param  int  $mid 用户ID
     * @param  int  $upId 要处理的ID
     * @param  string  $type p_on=产品上架 p_off=产品下架 t_on=票上架 t_off=票下架
     *
     * @return boolean
     */
    private function _updataSelf(int $mid, int $upId, string $type)
    {
        if (!$mid || !$upId || !$type) {
            return false;
        }

        $distProductBus   = $this->_distProductBus();

        $syncRes = false;
        $dinName = '自供应商上/下架数据同步失败';
        $logPath = 'multi_dist/error/updataSelf';
        $dinParam = json_encode([$mid, $upId, $type], JSON_UNESCAPED_UNICODE);
        $dinType = 'update_product';

        //产品上架
        if ($type == 'p_on') {
            $syncRes = $distProductBus->updataSelfByPon($mid, $upId);
        }

        //产品下架
        if ($type == 'p_off') {
            $syncRes = $distProductBus->updataSelfByPoff($mid, $upId);
        }

        //票上架
        if ($type == 't_on') {
            $syncRes = $distProductBus->updataSelfByTon($mid, $upId);
        }

        //票下架
        if ($type == 't_off') {
            $syncRes = $distProductBus->updataSelfByToff($mid, $upId);
        }

        $syncResJson = json_encode($syncRes, JSON_UNESCAPED_UNICODE);

        if ($syncRes['code'] != 200) {
            pft_log($logPath,
                '同步失败|mid:' . $mid . '|syncRes:' . $syncResJson);
            $distProductBus->dingWarn($dinName, [$dinParam], $dinType);
        }

        if ($syncRes['code'] != 200) {
            return false;
        }

        return true;

    }

    /**
     * 自供应产品名称/票名称数据同步
     * <AUTHOR>
     * @date 2020/4/11
     *
     * @param  int  $mid 用户ID
     * @param  int  $upId 要处理的ID
     * @param  string  $data 更新的数据
     * @param  string  $type p_name=产品名称 t_name=票名称
     *
     * @return boolean
     */
    private function _updataSelfData(int $mid, int $upId, string $data, string $type)
    {
        if (!$mid || !$upId || !$data || !$type) {
            return false;
        }

        $distProductBus   = $this->_distProductBus();
        //$distProductModel = $this->_distProductModel();

        $syncRes    = false;
        $dinName    = '自供应产品名称/票名称数据同步失败';
        $logPath    = 'multi_dist/error/updataSelfData';
        $dinParam   = json_encode([$mid, $upId, $data, $type], JSON_UNESCAPED_UNICODE);
        $dinType    = 'update_product';
        $content    = "|mid:{$mid}|upid:{$upId}|data:{$data}|type:{$type}";
        $errContent = "同步失败" . $content;

        //自供应产品名称同步
        if ($type == 'p_name') {
            $syncRes = $distProductBus->updataSelfDataByPname($mid, $upId, $data);
        }

        //自供应票名称同步
        if ($type == 't_name') {
            $syncRes = $distProductBus->updataSelfDataByTname($mid, $upId, $data);
        }

        $syncResJson = json_encode($syncRes, JSON_UNESCAPED_UNICODE);

        if ($syncRes['code'] != 200) {
            pft_log($logPath, $errContent . '|syncRes:' . $syncResJson);
            $distProductBus->dingWarn($dinName, [$dinParam], $dinType);
        }

        if ($syncRes['code'] != 200) {
            return false;
        }

        return true;
    }


    /**
     * 特产商品数据同步
     * <AUTHOR>
     * @date 2020/9/28
     *
     * @param  int  $gid 商品ID
     * @param  int  $mid 用户ID
     *
     * @return array
     */
    public function _specialtyProduct(int $gid, int $mid, int $state)
    {
        if (empty($mid) || empty($productData)) {
            return false;
        }

        $distProductBus   = $this->_distProductBus();

        $result = $distProductBus->specialtyProduct($gid, $mid, $state);
        if ($result['code'] != 200) {
            return false;
        }

        return true;
    }

    /**
     * 转分销开启/关闭数据同步
     * <AUTHOR>
     * @date 2020/4/11
     *
     * @param  int  $tid 票id
     * @param  int  $sid 上级供应商ID
     * @param  int  $fid 分销商ID
     * @param  string  $type on_off=开启/关闭 off_all=关闭所有
     *
     * @return boolean
     */
    private function _updataDis(array $data)
    {
        if (!$data) {
            return false;
        }

        $distProductBus   = $this->_distProductBus();
        $distProductModel = $this->_distProductModel();

        $syncRes    = false;
        $dinName    = '转分销开启/关闭数据同步失败';
        $logPath    = 'multi_dist/error/updataDis';
        $dinParam   = json_encode($data, JSON_UNESCAPED_UNICODE);
        $dinType    = 'update_product';
        $content    = "|data:" . json_encode($data, JSON_UNESCAPED_UNICODE);
        $errContent = "同步失败" . $content;
        $error      = false;


        //$distProductModel->startTrans();

        if (!empty($data['list'])) {
            foreach ($data['list'] as $value) {
                foreach ($value['priceset'] as $key => $item) {
                    if ($key != -1) {
                        //转分销开启同步
                        $syncRes = $distProductBus->openEvoluteConfig($value['pid'], $data['fid'], $item);
                    } else {
                        //转分销关闭同步
                        $syncRes = $distProductBus->updataDisByOff($value['pid'], $data['fid'], $item);
                    }

                    if ($syncRes['code'] != 200) {
                        $error = true;
                        break 2;
                    }
                }
            }
        } else {
            //关闭所有转分销
            $syncRes = $distProductBus->updataDisByOffAll($data['pid'], $data['fid']);
            if ($syncRes['code'] != 200) {
                $error = true;
            }
        }

        $syncResJson = json_encode($syncRes, JSON_UNESCAPED_UNICODE);

        if ($error) {
            pft_log($logPath,
                $errContent . '|syncRes:' . $syncResJson);
            $distProductBus->dingWarn($dinName, [$dinParam], $dinType);
            //$distProductModel->rollback();
        } else {
            //$distProductModel->commit();
        }

        if ($error) {
            return false;
        }

        return true;
    }

    /**
     * 分销关系删除数据同步
     * <AUTHOR>
     * @date 2020/5/7
     *
     * @param  int  $parentId 父id
     * @param  int  $sonId 子id
     *
     * @return array
     */
    public function _delDis(int $parentId, int $sonId)
    {
        if (!$parentId || !$sonId) {
            return false;
        }

        $distProductBus   = $this->_distProductBus();

        $dinName    = '分销关系删除数据同步失败';
        $logPath    = 'multi_dist/error/updataResource';
        $dinParam   = json_encode([$parentId, $sonId], JSON_UNESCAPED_UNICODE);
        $dinType    = 'update_product';
        $content    = "|parentId:{$parentId}|sonId:{$sonId}";
        $errContent = "同步失败" . $content;


        $syncRes = $distProductBus->delDisByOff($parentId, $sonId);

        $syncResJson = json_encode($syncRes, JSON_UNESCAPED_UNICODE);

        if ($syncRes['code'] != 200) {
            pft_log($logPath,
                $errContent . '|syncRes:' . $syncResJson);
            $distProductBus->dingWarn($dinName, [$dinParam], $dinType);
            return false;
        }

        return true;
    }

    /**
     * 资源中心采购/取消采购数据同步
     * <AUTHOR>
     * @date 2020/4/11
     *
     * @param  int  $tid 票id
     * @param  int  $sid 上级供应商id
     * @param  int  $fid 分销商id
     * @param  string  $type on=采购 off=取消采购 off_all=取消所有
     *
     * @return boolean
     */
    private function _updataResource(int $tid, int $sid, int $fid = 0, string $type)
    {
        if (!$tid || !$sid || !$type) {
            return false;
        }

        $distProductBus   = $this->_distProductBus();
        $syncRes    = false;
        $dinName    = '资源中心采购/取消采购数据同步失败';
        $logPath    = 'multi_dist/error/updataResource';
        $dinParam   = json_encode([$tid, $sid, $fid, $type], JSON_UNESCAPED_UNICODE);
        $dinType    = 'update_product';
        $content    = "|tid:{$tid}|sid:{$sid}|fid:{$fid}|type:{$type}";
        $errContent = "同步失败" . $content;

        //资源中心采购
        if ($type == 'on') {
            if (!$fid) {
                return false;
            }
            $syncRes = $distProductBus->updataResourceByOn($tid, $sid, $fid);
        }

        //资源中心取消采购
        if ($type == 'off') {
            if (!$fid) {
                return false;
            }
            $syncRes = $distProductBus->updataResourceByOff($tid, $sid, $fid);
        }

        //资源中心取消所有采购
        if ($type == 'off_all') {
            $syncRes = $distProductBus->updataResourceByOffAll($tid, $sid);
        }

        $syncResJson = json_encode($syncRes, JSON_UNESCAPED_UNICODE);

        if ($syncRes['code'] != 200) {
            pft_log($logPath,
                $errContent . '|syncRes:' . $syncResJson);
            $distProductBus->dingWarn($dinName, [$dinParam], $dinType);
        }

        if ($syncRes['code'] != 200) {
            return false;
        }

        return true;
    }



    /**
     * 实例化产品业务
     * <AUTHOR>
     * @date 2020/4/9
     *
     * @return \Business\MultiDist\Product
     */
    private function _distProductBus()
    {
        if (!$this->_distProductBus) {
            $this->_distProductBus = new \Business\MultiDist\Product();
        }

        return $this->_distProductBus;
    }



    /**
     * 实例化分销专员业务
     * <AUTHOR>
     * @date 2020/4/9
     *
     * @return \Model\MultiDist\Member
     */
    private function _distMemberModel()
    {
        if (!$this->_distMemberModel) {
            $this->_distMemberModel = new \Model\MultiDist\Member();
        }

        return $this->_distMemberModel;
    }



    /**
     * 实例化分销专员业务
     * <AUTHOR>
     * @date 2020/4/9
     *
     * @return \Model\MultiDist\Product
     */
    private function _distProductModel()
    {
        if (!$this->_distProductModel) {
            $this->_distProductModel = new \Model\MultiDist\Product();
        }

        return $this->_distProductModel;
    }



}