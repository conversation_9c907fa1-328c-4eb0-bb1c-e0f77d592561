<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 2019/2/1
 * Time: 15:18
 */

namespace CrontabTasks\Report;

use Business\JavaApi\Member\MemberQuery;
use Library\Controller;
use Model\Member\MemberRelationship;
use Model\Report\StatisticsV2;
use Model\Report\StatisticsV3;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class DistributorSaleInfo extends Controller
{
    private $logPath = 'DistributorSaleInfo/error';

    /**
     * 每天凌晨一点四十 计算供应商的销售商的基本销售信息
     * <AUTHOR>
     * @date   2020/1/16
     *
     * @return array
     */
    public function calculateSaleInfo()
    {
        $size    = 500;
        $page    = 1;
        $subSize = 20;

        //按天处理，时间格式00:00:00-23:59:59，不含今天。往前180天
        $endTime   = time() - 1 * 24 * 60 * 60;
        $endTime   = strtotime(date('Y-m-d 23:59:59', $endTime));
        //$beginTime = $endTime - (180 * 24 * 60 * 60 - 1);
        $dateList  = [
            'half_year' => [
                'begin_time' => $endTime - (180 * 24 * 60 * 60 - 1),
                'end_time'   => $endTime,
            ],
            'quarter'   => [
                'begin_time' => $endTime - (90 * 24 * 60 * 60 - 1),
                'end_time'   => $endTime,
            ],
            'month'     => [
                'begin_time' => $endTime - (30 * 24 * 60 * 60 - 1),
                'end_time'   => $endTime,
            ],
        ];

        $expTime = 2 * 24 * 60 * 60;
        //每次处理数据的最大条数
        //$maxNum    = 1000;

        //打印开始结束时间
        echo '执行周期：' . json_encode($dateList, JSON_UNESCAPED_UNICODE) . "\n";

        //初始化相关模型
        $memberModel = new MemberQuery();
        $shipModel   = new MemberRelationship();
        //$staticV2    = new StatisticsV2();
        $staticV3 = new StatisticsV3();
        $javaApi  = new \Business\JavaApi\Product\EvoluteHalfYearSaleSync();

        $redis = \Library\Cache\Cache::getInstance('redis');
        $key   = "distribution_list_sale_info_supplier:";

        $idArr         = [];
        $accountIdArr  = [];
        $customerIdArr = [];
        $accountArr    = [];
        $dTypeArr      = [0, 1];
        $mobileArr     = [];
        $statusArr     = [0, 3];

        $countRes = $memberModel->queryFidsByCondition($page, $size, $idArr, $accountIdArr, $customerIdArr, $accountArr,
            $dTypeArr, $mobileArr, $statusArr);
        if (empty($countRes)) {
            $count = 0;
        } else {
            $count = $countRes['total'];
        }

        $totalPage = ceil($count / $size);

        while (true) {
            $result = $memberModel->queryFidsByCondition($page, $size, $idArr, $accountIdArr, $customerIdArr,
                $accountArr, $dTypeArr, $mobileArr, $statusArr, $pageCount = 1);
            if (!empty($result)) {
                foreach ($result['list'] as $aid) {
                    //获取供应商的所有分销商
                    //$aid     = $aid['id'];
                    $disList = $shipModel->getResellerInRelation($aid);

                    if ($disList) {
                        $disList   = array_column($disList, 'son_id');
                        $disLength = count($disList);

                        $tmpSaleInfo = [];
                        //查询报表获取相关数据 控制单次查询数量
                        for ($subPage = 1; $subPage <= (int)ceil($disLength / $subSize); $subPage++) {
                            $subDisList = array_slice($disList, ($subPage - 1) * $subSize, $subSize);
                            //初始值
                            $tmpSubDisSaleInfo = [];
                            foreach ($subDisList as $resellerId) {
                                $tmpSubDisSaleInfo[$resellerId] = [
                                    'sid'                 => $aid,
                                    'fid'                 => $resellerId,
                                    'orderNum'            => 0,
                                    'ticketNum'           => 0,
                                    'totalMoney'          => 0,
                                    'ninetyDayOrderNum'   => 0,
                                    'ninetyDayTicketNum'  => 0,
                                    'ninetyDayTotalMoney' => 0,
                                    'thirtyDayOrderNum'   => 0,
                                    'thirtyDayTicketNum'  => 0,
                                    'thirtyDayTotalMoney' => 0,
                                ];
                            }
                            foreach ($dateList as $dateKey => $item) {
                                $beginTime      = $item['begin_time'];
                                $endTime        = $item['end_time'];
                                $subDisSaleInfo = $staticV3->getRecenterSaleDataBySupplier($beginTime, $endTime, $aid,
                                    $subDisList);
                                if (empty($subDisSaleInfo)) {
                                    continue;
                                }
                                foreach ($subDisSaleInfo as $info) {
                                    if (!isset($tmpSubDisSaleInfo[$info['fid']])) {
                                        $tmpSubDisSaleInfo[$info['fid']] = [
                                            'sid'                 => $aid,
                                            'fid'                 => $info['fid'],
                                            'orderNum'            => 0,
                                            'ticketNum'           => 0,
                                            'totalMoney'          => 0,
                                            'ninetyDayOrderNum'   => 0,
                                            'ninetyDayTicketNum'  => 0,
                                            'ninetyDayTotalMoney' => 0,
                                            'thirtyDayOrderNum'   => 0,
                                            'thirtyDayTicketNum'  => 0,
                                            'thirtyDayTotalMoney' => 0,
                                        ];
                                    }
                                }
                                switch ($dateKey) {
                                    case 'half_year':
                                        foreach ($subDisSaleInfo as $info) {
                                            $tmpSubDisSaleInfo[$info['fid']]['orderNum']   += $info['orderNum'];
                                            $tmpSubDisSaleInfo[$info['fid']]['ticketNum']  += $info['ticketNum'];
                                            $tmpSubDisSaleInfo[$info['fid']]['totalMoney'] += $info['totalMoney'];
                                        }
                                        break;
                                    case 'quarter':
                                        foreach ($subDisSaleInfo as $info) {
                                            $tmpSubDisSaleInfo[$info['fid']]['ninetyDayOrderNum']   += $info['orderNum'];
                                            $tmpSubDisSaleInfo[$info['fid']]['ninetyDayTicketNum']  += $info['ticketNum'];
                                            $tmpSubDisSaleInfo[$info['fid']]['ninetyDayTotalMoney'] += $info['totalMoney'];
                                        }
                                        break;
                                    default:
                                        foreach ($subDisSaleInfo as $info) {
                                            $tmpSubDisSaleInfo[$info['fid']]['thirtyDayOrderNum']   += $info['orderNum'];
                                            $tmpSubDisSaleInfo[$info['fid']]['thirtyDayTicketNum']  += $info['ticketNum'];
                                            $tmpSubDisSaleInfo[$info['fid']]['thirtyDayTotalMoney'] += $info['totalMoney'];
                                        }
                                }
                            }
                            $tmpSaleInfo = array_merge($tmpSaleInfo, array_values($tmpSubDisSaleInfo));
                        }

                        if (count($tmpSaleInfo)) {
                            //处理销售数据放入缓存
                            $disSaleInfo = [];
                            foreach ($tmpSaleInfo as $info) {
                                //格式处理下 需要处理成int，避免string
                                $info['sid']               = intval($info['sid']);
                                $info['fid']               = intval($info['fid']);
                                $disSaleInfo[$info['fid']] = $info;
                            }
                            //往中台同步半年销售数据
                            if ($disSaleInfo) {
                                $tmpDisSaleInfo = array_values($disSaleInfo);
                                //一次最多500个
                                for ($i = 1; $i <= (int)ceil(count($tmpDisSaleInfo) / $size); $i++) {
                                    $syncData = array_slice($tmpDisSaleInfo, ($i - 1) * $size, $size);
                                    $javaApi->sync(array_values($syncData));
                                }
                            }

                            $redis->set($key . $aid, json_encode($disSaleInfo), '', $expTime);
                        } else {
                            $redis->del($key . $aid);
                        }
                    }

                }
            }

            if ($page >= $totalPage) {
                break;
            }

            $page++;
        }
        exit('执行完了');
        //获取供应商总数
        //$countQuery = $memberModel->getBatchSupplierList(1, 1, true);
        //if ($countQuery && $total = $countQuery['total']) {
        //    for (; $page <= (int)ceil($total / $size); $page++) {
        //        $result = $memberModel->queryFidsByCondition($page, $size);
        //        if ($result) {
        //            foreach ($result['list'] as $aid) {
        //                //获取供应商的所有分销商
        //                $aid     = $aid['id'];
        //                $disList = $shipModel->getResellerInRelation($aid, 'son_id');
        //
        //                if ($disList) {
        //                    $disList   = array_column($disList, 'son_id');
        //                    $disLength = count($disList);
        //
        //                    $tmpSaleInfo = [];
        //                    //查询报表获取相关数据 控制单次查询数量
        //                    for ($subPage = 1; $subPage <= (int)ceil($disLength / $subSize); $subPage++) {
        //                        $subDisList     = array_slice($disList, ($subPage - 1) * $subSize, $subSize);
        //                        $subDisSaleInfo = $staticV2->getRecenterSaleDataBySupplier($beginTime, $endTime, $aid,
        //                            $subDisList);
        //                        $tmpSaleInfo    = array_merge($tmpSaleInfo, $subDisSaleInfo);
        //                    }
        //
        //                    if (count($tmpSaleInfo)) {
        //                        //处理销售数据放入缓存
        //                        $disSaleInfo = [];
        //                        foreach ($tmpSaleInfo as $info) {
        //                            $disSaleInfo[$info['fid']] = $info;
        //                        }
        //                        $redis->set($key . $aid, json_encode($disSaleInfo), '', $expTime);
        //                    } else {
        //                        $redis->del($key . $aid);
        //                    }
        //
        //                }
        //            }
        //        }
        //    }
        //}
    }
}