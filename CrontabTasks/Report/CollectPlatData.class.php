<?php
/**
 * 平台运营数据收集脚本
 * Created by PhpStorm.
 * User: zhangyangzhen
 * Date: 2018/9/27
 * Time: 15:20
 */

namespace CrontabTasks\Report;

use Library\Controller;
use Library\Tools\Helpers;
use Model\Member\Member;
use Model\Member\MemberLog;
use Model\Report\TerminalDataSummary;
use Model\TerminalManage\OperationLog;

class CollectPlatData extends Controller
{
    private $memberLogModel;
    private $operationLogModel;
    private $memberModel;

    public function index()
    {
        $yesterday      = date('Y-m-d', strtotime('-1 day'));

        //获取前一天订单数，票数，金额
        $terminalModel  = new TerminalDataSummary();
        $orderRes       = $terminalModel->getYesterdayCount($yesterday);

        $begin          = $yesterday . " 00:00:00";
        $end            = $yesterday . " 23:59:59";

        //在pft_login_log表里用的时间戳
        $begin  = strtotime($begin);
        $end    = strtotime($end);

        //获取自助机，手持机、闸机、云票务的登录次数
        $memberLog   = $this->_getMemberLogModel();
        $selfCount   = $memberLog->getLoginCount($begin, $end, 7);  //自助机
        $handleCount = $memberLog->getLoginCount($begin, $end, 8);  //手持机
        $gateCount   = $memberLog->getLoginCount($begin, $end, 9);  //闸机
        $cloudCount  = $memberLog->getLoginCount($begin, $end, 10); //云票务

        //获取登录过平台或微平台的供应商、分销商
        $res = $memberLog->getLoginPeople($begin, $end, [1, 3]);

        $memberIdArr = [];
        foreach ($res as $val) {
            $memberIdArr[] = $val['mid'];
        }

        $api = new \Business\JavaApi\Member\MemberQuery();

        $where = [
            'idList' => $memberIdArr,
            'dTypes' => [0]
        ];
        $suRes = $api->queryMemberByMemberQueryInfoCount($where);
        if ($suRes['code'] == 200) {
            $supplier = $suRes['data'];
        } else {
            $supplier = 0;
        }

        $where = [
            'idList' => $memberIdArr,
            'dTypes' => [1]
        ];
        $suRes = $api->queryMemberByMemberQueryInfoCount($where);
        if ($suRes['code'] == 200) {
            $distributor = $suRes['data'];
        } else {
            $distributor = 0;
        }

//        $returnData = [
//            'totalOrder'    => $orderRes['ordernum'],
//            'totalTicket'   => $orderRes['ticketnum'],
//            'totalMoney'    => $orderRes['totalMoney'],
//            'cloudCount'    => $cloudCount,
//            'selfCount'     => $selfCount,
//            'handleCount'   => $handleCount,
//            'gateCount'     => $gateCount,
//            'supplier'      => $supplier,
//            'distributor'   => $distributor
//        ];
        $sendMsg  = "昨日订单量：" . $orderRes['ordernum'] . "，票数：" . $orderRes['ticketnum'] . "，交易金额：" . $orderRes['totalMoney'] . "\n";
        $sendMsg .= "活跃供应商：" . $supplier . "\n";
        $sendMsg .= "活跃分销商：" . $distributor . "\n";
        $sendMsg .= "云票务启动次数：" . $cloudCount . "\n";
        $sendMsg .= "手持机启动次数：" . $handleCount . "\n";
        $sendMsg .= "自助机启动次数：" . $selfCount . "\n";
        $sendMsg .= "闸机签到次数：" . $gateCount;

        $token = '';

        Helpers::sendDingTalkGroupRobotMessageRaw($sendMsg, $token);
    }

    /**
     * 获取Model\Member\MemberLog用户日志模型
     * Create by zhangyangzhen
     * Date: 2018/9/27
     * Time: 17:14
     * @return MemberLog
     */
    private function _getMemberLogModel()
    {
        if (empty($this->memberLogModel)) {
            $this->memberLogModel = new MemberLog();
        }

        return $this->memberLogModel;
    }

    /**
     * 获取Model\TerminalManage\OperationLog云票务用户操作日志模型
     * Create by zhangyangzhen
     * Date: 2018/9/27
     * Time: 17:15
     * @return OperationLog
     */
    private function _getOperationLogModel()
    {
        if (empty($this->operationLogModel)) {
            $this->operationLogModel = new OperationLog();
        }

        return $this->operationLogModel;
    }

    /**
     * 获取Model\Member\Member用户模型
     * Create by zhangyangzhen
     * Date: 2018/9/28
     * Time: 14:04
     * @return Member
     */
    private function _getMemberModel()
    {
        if (empty($this->memberModel)) {
            $this->memberModel = new Member();
        }

        return $this->memberModel;
    }
}