<?php
/**
 * 销售报表的守护程序
 * 如果销售报表生成数据失败 将由此脚本重新生成一次
 * 销售报表脚本：./ReportStatistics.class.php
 *
 * <AUTHOR>
 * @since  2016-10-12
 */

namespace CrontabTasks\Report;
use       Library\Controller;
use       Controller\report;

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

class ReportProtect extends Controller {

    private $_logPath = 'statistic_report_protect';
    private $_model   = null;

    public function __construct() {

        set_time_limit(0);

        $this->_model          = new \Model\Report\Statistics();

        $this->_pftTransReport = new report\PftTransReport();
    }

    /**
     * 入口
     */
    public function index() {

        pft_log($this->_logPath, "脚本启动成功");

        $res = $this->_model->getTaskLogByDate();

        if ($res === false) {
            pft_log($this->_logPath, "需要获取的时间有误");
        }

        if (empty($res)) {

            $this->_runTask(1, 1, 1, 1, 1);

            exit;
        } elseif (is_array($res) && !empty($res)) {

            $infoArr  = $res['info'];
            $infoArr  = @json_decode($infoArr, true);

            $checked  = (isset($infoArr['checked']) && $infoArr['checked'] == 1) ? 0 : 1;
            $order    = (isset($infoArr['order']) && $infoArr['order'] == 1) ? 0 : 1;
            $cancel   = (isset($infoArr['cancel']) && $infoArr['cancel'] == 1) ? 0 : 1;
            $revoke   = (isset($infoArr['revoke']) && $infoArr['revoke'] == 1) ? 0 : 1;
            $tranSum  = (isset($infoArr['tranSum']) && $infoArr['tranSum'] == 1) ? 0 : 1;

            $this->_runTask($checked, $order, $cancel, $revoke, $tranSum);

            exit;
        }
        $time = time();
        pft_log($this->_logPath, "莫名其妙失败 时间：{$time}");
    }

    /**
     * 执行相应任务
     *
     * @param $checked = 1  检票报表任务       启动
     * @param $order   = 1  预定报表任务       启动
     * @param $cancel  = 1  修改/取消报表任务   启动
     * @param $revoke  = 1  撤销/撤改报表任务   启动
     * @param $tranSum = 1  汇总报表任务       启动
     */
    private function _runTask($checked = 1, $order = 1, $cancel = 1, $revoke = 1, $tranSum = 1) {

        $date  = date('Y-m-d', strtotime('-1 days'));
        $reRun = '';

        if ($checked == 1) {
            $this->_model->runCheckedTask($date);
            $reRun .= '验证报表|';
        }

        if ($order == 1) {
            $this->_model->runOrderTask($date);
            $reRun .= '预定报表|';
        }

        if ($cancel == 1) {
            $this->_model->runCancelTask($date);
            $reRun .= '修改/取消报表|';
        }

        //if ($revoke == 1) {
        //    $this->_model->runRevokeTask($date);
        //    $reRun .= '撤改/撤销报表|';
        //}

        if ($tranSum == 1) {
            $this->_pftTransReport->runTask($date);
            $reRun .= '交易记录汇总报表|';
        }

        $date = date('Y-m-d H:i:s', time());
        $logData = "报表守护程序运行成功：时间:{$date}\n";
        $logData .= "重跑的报表有：{$reRun}";
        pft_log($this->_logPath, $logData);
    }




}