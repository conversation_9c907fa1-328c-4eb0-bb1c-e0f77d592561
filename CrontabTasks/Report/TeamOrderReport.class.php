<?php

namespace CrontabTasks\Report;

use Business\JavaApi\DistributionCenter\GroupQuery;
use Business\JavaApi\Order\Query\UusOrder;
use Library\Constants\Team\TeamConst;
use Library\Controller;
use Model\Order\OrderTools;
use Model\Order\TeamOrderSearch;
use Model\Team\TeamReportRealTask;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class TeamOrderReport extends Controller
{
    private $_limit = 500;
    private $_error = "team_report/run_error";
    private $_debug = "team_report/run";
    private $_teamOrderModel;
    private $_teamReportRealTaskModel;
    private $_orderDiscountApi;
    private $_teamOrderReport;
    private $_trackModel;
    private $groupBiz;

    /**
     * /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/TeamOrderReport runTeamOrderReport
     *
     * @return bool
     * @throws \Exception
     */
    public function runTeamOrderReport()
    {
        //获取需要执行的数据
        $this->_teamReportRealTaskModel = $this->_getTeamReportRealTaskModel();
        $field = "id, team_order_id, action_type, operator_id, num, son_order_num, memo, ext_content";
        $list = $this->_teamReportRealTaskModel->getRealTaskList($this->_limit, $field);
        if (empty($list)) {
            pft_log($this->_debug, "无任务需要执行");

            return true;
        }
        //action_type 操作类型 0:下单 1：取消  2：撤销撤改 3：新增票数 4：打印票类 5：验证订单
        $teamOrderIdArr = array_column($list, 'team_order_id');
        //$list           = array_key($list, 'team_order_id');
        //获取团单订单信息
        $this->_teamOrderModel = new TeamOrderSearch();
        $teamOrderInfo = $this->_teamOrderModel->getMainOrderInfoByOrderNum($teamOrderIdArr);
        if (empty($teamOrderInfo)) {
            pft_log($this->_error, "团单主订单信息获取失败：" . $this->_teamOrderModel->getDbError());

            return false;
        }
        foreach ($list as $value) {
            $extContent = json_decode($value['ext_content'], true);
            switch ($value['action_type']) {
                case TeamConst::_REAL_TASK_ORDER_:
                case TeamConst::_REAL_TASK_ORDER_TERMINAL_:
                    $splitInfo = $this->_getOrderChildOrderInfo($value['team_order_id'], $value['num'], $value['action_type']);
                    if (empty($splitInfo)) {
                        pft_log($this->_error, "订单分销数据获取出错");
                    }
                    $this->_insertTeamOrderReport($teamOrderInfo, $value, $splitInfo);
                    break;
                case TeamConst::_REAL_TASK_REVOKE_:
                    $checkSplitInfo = $this->_getCheckOrderInfo($value['son_order_num'], $value['num'], $value['action_type'], $extContent);
                    if (empty($checkSplitInfo)) {
                        pft_log($this->_error, "订单分销数据获取出错");
                    }
                    $this->_insertTeamOrderCheckReport($teamOrderInfo, $value, $checkSplitInfo);
                case TeamConst::_REAL_TASK_CANCEL_:
                case TeamConst::_REAL_TASK_ADD_TICKET_:
                case TeamConst::_REAL_TASK_PRINT_:
                case TeamConst::_REAL_TASK_ADD_ORDER_:
                case TeamConst::_REAL_TASK_ADD_ORDER_TERMINAL_:
                case TeamConst::_REAL_TASK_ADD_TICKET_TERMINAL_:
                    $splitInfo = $this->_getOtherTypeChildOrderInfo($value['son_order_num'], $value['action_type'], $value['num'], $extContent);
                    if (empty($splitInfo)) {
                        pft_log($this->_error, "订单分销数据获取出错");
                    }
                    $this->_insertTeamOrderReport($teamOrderInfo, $value, $splitInfo);
                    break;
                case TeamConst::_REAL_TASK_CHECK_:
                    $checkSplitInfo = $this->_getCheckOrderInfo($value['son_order_num'], $value['num'], $value['action_type'], $extContent);
                    if (empty($checkSplitInfo)) {
                        pft_log($this->_error, "订单分销数据获取出错");
                    }
                    $this->_insertTeamOrderCheckReport($teamOrderInfo, $value, $checkSplitInfo);
                    break;
                default:
                    break;
            }
        }

        return true;
    }

    /**
     * 添加团队预定报表数据
     *
     * @param array $teamOrderInfo 团单信息
     * @param array $taskInfo 任务数据
     * @param array $splitInfo 需要添加的数据
     */
    private function _insertTeamOrderReport($teamOrderInfo, $taskInfo, $splitInfo)
    {
        $error = "";
        $done = 2;
        $teamOrderReportModel = new \Model\Team\TeamOrderReport();
        $teamOrderInfo = array_key($teamOrderInfo, 'ordernum');
        $insertData = [];
        foreach ($splitInfo as $orderId => $teamValue) {
            foreach ($teamValue as $fid => $value) {
                $splitInfo[$orderId][$fid]['operator_id'] = $taskInfo['operator_id'];
                $splitInfo[$orderId][$fid]['date'] = date('YmdH');
                $splitInfo[$orderId][$fid]['guide'] = $teamOrderInfo[$taskInfo['team_order_id']]['guide'];
                $splitInfo[$orderId][$fid]['province'] = $teamOrderInfo[$taskInfo['team_order_id']]['province'] ?? 0;
                $splitInfo[$orderId][$fid]['city'] = $teamOrderInfo[$taskInfo['team_order_id']]['city'] ?? 0;
                $splitInfo[$orderId][$fid]['country'] = $teamOrderInfo[$taskInfo['team_order_id']]['county'] ?? 0;
                $splitInfo[$orderId][$fid]['is_lodging'] = $teamOrderInfo[$taskInfo['team_order_id']]['is_lodging'];
                $splitInfo[$orderId][$fid]['team_type_id'] = $teamOrderInfo[$taskInfo['team_order_id']]['team_type_id'];
                $splitInfo[$orderId][$fid]['order_source'] = $teamOrderInfo[$taskInfo['team_order_id']]['ordertype'] == 1 ? 1 : 2;
                $splitInfo[$orderId][$fid]['create_time'] = time();
                $splitInfo[$orderId][$fid]['orders_info'] = json_encode($value['orders_info'], JSON_UNESCAPED_UNICODE);
                $splitInfo[$orderId][$fid]['orders_cancel_info'] = json_encode($value['orders_cancel_info'], JSON_UNESCAPED_UNICODE);
                $splitInfo[$orderId][$fid]['revoke_orders_info'] = json_encode($value['revoke_orders_info'], JSON_UNESCAPED_UNICODE);
                $splitInfo[$orderId][$fid]['print_orders_info'] = json_encode($value['print_orders_info'], JSON_UNESCAPED_UNICODE);
            }
            //处理完数据入库
            $insertData = array_merge($splitInfo[$orderId], $insertData);
        }
        $result = $teamOrderReportModel->addAllReportData($insertData);
        if ($result === false) {
            $done = 3;
            $error = $teamOrderReportModel->getDbError();
        }
        if (!$this->_teamReportRealTaskModel) {
            $this->_teamReportRealTaskModel = new TeamReportRealTask();
        }
        $taskRes = $this->_teamReportRealTaskModel->updateTaskStatus($taskInfo['id'], $done, $error);
        if ($taskRes === false) {
            pft_log($this->_error, "定时任务状态更新失败，原因" . $this->_teamReportRealTaskModel->getDbError());
        }


        return true;
    }

    /**
     * 添加团队验证报表数据
     *
     * @param array $teamOrderInfo 团单信息
     * @param array $taskInfo 任务数据
     * @param array $splitInfo 需要添加的数据
     *
     * @return bool
     */
    private function _insertTeamOrderCheckReport($teamOrderInfo, $taskInfo, $splitInfo)
    {
        $error = "";
        $done = 2;
        $teamOrderReportModel = new \Model\Team\TeamOrderReport();
        $teamOrderInfo = array_key($teamOrderInfo, 'ordernum');
        $insertData = [];
        foreach ($splitInfo as $orderId => $teamValue) {
            foreach ($teamValue as $fid => $value) {
                $splitInfo[$orderId][$fid]['operator_id'] = $taskInfo['operator_id'];
                $splitInfo[$orderId][$fid]['date'] = date('YmdH');
                $splitInfo[$orderId][$fid]['guide'] = $teamOrderInfo[$taskInfo['team_order_id']]['guide'];
                $splitInfo[$orderId][$fid]['province'] = $teamOrderInfo[$taskInfo['team_order_id']]['province'] ?? 0;
                $splitInfo[$orderId][$fid]['city'] = $teamOrderInfo[$taskInfo['team_order_id']]['city'] ?? 0;
                $splitInfo[$orderId][$fid]['order_source'] = $teamOrderInfo[$taskInfo['team_order_id']]['ordertype'] == 1 ? 1 : 2;
                $splitInfo[$orderId][$fid]['country'] = $teamOrderInfo[$taskInfo['team_order_id']]['county'] ?? 0;
                $splitInfo[$orderId][$fid]['is_lodging'] = $teamOrderInfo[$taskInfo['team_order_id']]['is_lodging'];
                $splitInfo[$orderId][$fid]['team_type_id'] = $teamOrderInfo[$taskInfo['team_order_id']]['team_type_id'];
                $splitInfo[$orderId][$fid]['create_time'] = time();
                $splitInfo[$orderId][$fid]['orders_info'] = json_encode($value['orders_info'], JSON_UNESCAPED_UNICODE);
                $splitInfo[$orderId][$fid]['revoke_orders_info'] = json_encode($value['revoke_orders_info'], JSON_UNESCAPED_UNICODE);
            }
            //处理完数据入库
            $insertData = array_merge($splitInfo[$orderId], $insertData);
        }
        $result = $teamOrderReportModel->addAllReportCheckData($insertData);
        if ($result === false) {
            $done = 3;
            $error = $teamOrderReportModel->getDbError();
        }
        if (!$this->_teamReportRealTaskModel) {
            $this->_teamReportRealTaskModel = new TeamReportRealTask();
        }
        $taskRes = $this->_teamReportRealTaskModel->updateTaskStatus($taskInfo['id'], $done, $error);
        if ($taskRes === false) {
            pft_log($this->_error, "定时任务状态更新失败，原因" . $this->_teamReportRealTaskModel->getDbError());
        }


        return true;
    }

    /**
     * 根据团单号获取子订单信息
     *
     * @param string $teamOrderId 团单号数组
     * @param int $num 团单号数组
     * @param int $actionType 操作类型 0:下单 1：取消  2：撤销撤改 3：新增票数 4：打印票类 5：团单添加订单的数据 6：团单验证 7:云票务下单 8：云票务添加订单的数据
     *
     * @return array
     * @throws \Exception
     */
    private function _getOrderChildOrderInfo($teamOrderId, $num, $actionType)
    {
        if (empty($teamOrderId)) {
            return [];
        }
        if (empty($this->_teamOrderModel)) {
            $this->_teamOrderModel = new TeamOrderSearch();
        }
        $field = "buyerid, sellerid, order_level, pmode, sale_money, cost_money, l.son_ordernum, l.main_ordernum";
        //获取团单层级关系
        $childOrderList = $this->_teamOrderModel->getSonOrderInfoByMainOrderNumV2($teamOrderId, 0, 0, 0, 0, $field);
        if (empty($childOrderList)) {
            pft_log($this->_error, "{$teamOrderId}订单分销数据获取出错原因：" . $this->_teamOrderModel->getDbError());

            return [];
        }
        //获取子订单信息
        $sonOrderNum = array_values(array_unique(array_column($childOrderList, 'son_ordernum')));
        $orderApi = new UusOrder();
        $orderInfo = $orderApi->queryOrderInfoByOrdernumList($sonOrderNum, true);
        if (empty($orderInfo['data'])) {
            pft_log($this->_error, "子订单数据获取错误：" . json_encode($orderInfo, JSON_UNESCAPED_UNICODE) . "参数" .
                json_encode($sonOrderNum, JSON_UNESCAPED_UNICODE));

            return [];
        }
        //获取订单优惠券抵扣金额
        //获取优惠金额信息
        $orderDiscountApi = $this->_getOrderDiscountApi();
        $orderDiscountInfo = $orderDiscountApi->batchQueryList($sonOrderNum);
        $orderDiscountInfo = empty($orderDiscountInfo['data']) ? [] : $orderDiscountInfo['data'];
        $discountInfo = [];
        foreach ($orderDiscountInfo as $value) {
            if (in_array($value['couponType'], [4, 5])) {
                $discountInfo[$value['orderNum']] += $value['couponPrice'];
            }
        }
        //获取订单下单时的数量
        $teamOrderReportBz = $this->_getTeamOrderReport();
        $orderTrackInfo = $teamOrderReportBz->getTrackInfoByOrder($sonOrderNum, 0);
        //获取支付的时候数据信息
        $terminalPayTrackInfo = $teamOrderReportBz->getTerminalPayTrackInfoByOrder($sonOrderNum);
        $orderInfo = $orderInfo['data'];
        $returnData = [];
        foreach ($childOrderList as $value) {
            $windowSaleMoney = 0;
            if ($actionType == TeamConst::_REAL_TASK_ORDER_TERMINAL_ && isset($terminalPayTrackInfo[$value['son_ordernum']])) {
                $windowSaleMoney = $value['sale_money'] * $terminalPayTrackInfo[$value['son_ordernum']]['tnum'];
            }
            $discountMoney = $discountInfo[$value['son_ordernum']] ?? 0;
            if ($value['buyerid'] != $orderInfo[$value['son_ordernum']]['member'] || $value['sellerid'] != $orderInfo[$value['son_ordernum']]['aid']) {
                $discountMoney = 0;
            }
            //获取fid在aid的分销商分组
            $groupInfo = $this->getGroupBiz()->queryOneGroupAndDistributor(intval($value['buyerid']), intval($value['sellerid']));
            $returnData[$value['son_ordernum']][$value['buyerid']] = [
                'fid' => $value['buyerid'],
                'aid' => $value['sellerid'],
                'buyer_group_id' => $groupInfo[0]['groupId'] ?? 0,
                'team_order_id' => $value['main_ordernum'] ?? 0,
                'lid' => $orderInfo[$value['son_ordernum']]['lid'],
                'tid' => $orderInfo[$value['son_ordernum']]['tid'],
                'level' => $value['order_level'],
                'order_type' => $orderInfo[$value['son_ordernum']]['ordermode'],
                'pay_mode' => $value['pmode'],
                'order_ticket_num' => $orderTrackInfo[$value['son_ordernum']]['tnum'],
                'sale_money' => $value['sale_money'] * $orderTrackInfo[$value['son_ordernum']]['tnum'],
                'window_sale_money' => $windowSaleMoney,
                'cost_money' => $value['cost_money'] * $orderTrackInfo[$value['son_ordernum']]['tnum'],
                'orders_info' => [
                    $value['son_ordernum'] => [
                        'team_order_id' => $teamOrderId,
                        'tnum' => $orderTrackInfo[$value['son_ordernum']]['tnum'],
                        'discount_money' => $discountMoney,
                    ],
                ],
                'orders_cancel_info' => [],
                'revoke_orders_info' => [],
                'print_orders_info' => [],
                'discount_money' => $discountMoney,
            ];
        }

        return $returnData;
    }

    /**
     * 根据子单号获取子订单信息
     *
     * @param string $sonOrderNum 订单号
     * @param int $actionType 操作类型 0:下单 1：取消  2：撤销撤改 3：新增票数 4：打印票类 5：团单添加订单的数据 6：团单验证 7:云票务下单 8：云票务添加订单的数据 9 :云票务新增票数
     * @param int $num 本次操作数量
     * @param array $extContent
     * @return array
     * @throws \Exception
     */
    private function _getOtherTypeChildOrderInfo($sonOrderNum, $actionType, $num, $extContent = [])
    {
        if (empty($sonOrderNum)) {
            return [];
        }
        if (empty($this->_teamOrderModel)) {
            $this->_teamOrderModel = new TeamOrderSearch();
        }
        $field = "buyerid, sellerid, order_level, pmode, sale_money, cost_money, l.son_ordernum, l.main_ordernum";
        //获取团单层级关系
        $childOrderList = $this->_teamOrderModel->getSonOrderSplitInfoByOrderNum($sonOrderNum, 0, 0, 0, 0, $field);
        if (empty($childOrderList)) {
            pft_log($this->_error, "{$sonOrderNum}订单分销数据获取出错原因：" . $this->_teamOrderModel->getDbError());

            return [];
        }
        $orderToolModel = new OrderTools();
        $orderInfo = $orderToolModel->getOrderInfo($sonOrderNum);
        if (empty($orderInfo)) {
            pft_log($this->_error, "子订单数据获取错误：" . json_encode($orderInfo, JSON_UNESCAPED_UNICODE));

            return [];
        }
        $orderExtContent = empty($orderInfo['ext_content']) ? [] : json_decode($orderInfo['ext_content'], true);
        $orderDiscountMoney = 0;
        if (((isset($orderExtContent['bargainPricePolicyId']) && !empty($orderExtContent['bargainPricePolicyId'])) || (isset($orderExtContent['specifyReducedPolicyId']) && !empty($orderExtContent['specifyReducedPolicyId'])))
            && in_array($actionType, [TeamConst::_REAL_TASK_CANCEL_, TeamConst::_REAL_TASK_REVOKE_,
                TeamConst::_REAL_TASK_ADD_ORDER_, TeamConst::_REAL_TASK_ADD_ORDER_TERMINAL_])) {
            //通过下标获取到对应下标优惠券抵扣金额数值
            $serialNumber = $extContent['serial_number'];
            $idxArr = [];
            if (!empty($serialNumber)) {
                $idxInfo = explode(',', $serialNumber);
                foreach ($idxInfo as $item) {
                    $idx = explode('-', $item);
                    for ($i = $idx[0]; $i <= $idx[1]; $i++) {
                        $idxArr[] = $i;
                    }
                }
            }
            $tempOrderDiscountRes = $this->_getOrderDiscountApi()->list($sonOrderNum, 0, $idxArr);
            foreach ($tempOrderDiscountRes['data'] as $tmp) {
                if (in_array($tmp['couponType'], [4, 5])) {
                    $orderDiscountMoney += $tmp['couponPrice'];
                }
            }
        }
        //获取支付的时候数据信息
        $teamOrderReportBz = $this->_getTeamOrderReport();
        $terminalPayTrackInfo = $teamOrderReportBz->getTerminalPayTrackInfoByOrder([$sonOrderNum]);
        $returnData = [];
        foreach ($childOrderList as $value) {
            $cancelNum = $revokeNum = $printNum = $orderNum = 0;
            $cancelMoney = $revokeMoney = $printMoney = $orderMoney = $costMoney = $windowSaleMoney = 0;
            $cancelOrderInfo = $revokeOrderInfo = $printOrderInfo = $ordersInfo = [];
            $tmpDiscountMoney = $orderDiscountMoney;
            if ($value['buyerid'] != $orderInfo['member'] || $value['sellerid'] != $orderInfo['aid']) {
                $tmpDiscountMoney = 0;
            }
            switch ($actionType) {
                case TeamConst::_REAL_TASK_CANCEL_:
                    $cancelDiscountMoney = $tmpDiscountMoney;
                    $cancelNum = $num;
                    $cancelMoney = $num * $value['sale_money'];
                    $costMoney = $num * $value['cost_money'];
                    $cancelOrderInfo = [
                        $sonOrderNum => [
                            'team_order_id' => $value['main_ordernum'],
                            'tnum' => $num,
                            'cancel_discount_money' => $cancelDiscountMoney,
                        ],
                    ];
                    break;
                case TeamConst::_REAL_TASK_REVOKE_:
                    $revokeDiscountMoney = $tmpDiscountMoney;
                    $revokeNum = $num;
                    $revokeMoney = $num * $value['sale_money'];
                    $costMoney = $num * $value['cost_money'];
                    $revokeOrderInfo = [
                        $sonOrderNum => [
                            'team_order_id' => $value['main_ordernum'],
                            'tnum' => $num,
                            'revoke_discount_money' => $revokeDiscountMoney,
                        ],
                    ];
                    break;
                case TeamConst::_REAL_TASK_ADD_TICKET_:
                case TeamConst::_REAL_TASK_ADD_ORDER_:
                case TeamConst::_REAL_TASK_ADD_ORDER_TERMINAL_:
                case TeamConst::_REAL_TASK_ADD_TICKET_TERMINAL_:
                    $orderNum = $num;
                    $orderMoney = $num * $value['sale_money'];
                    $costMoney = $num * $value['cost_money'];
                    $discountMoney = 0;
                    if (in_array($actionType, [TeamConst::_REAL_TASK_ADD_ORDER_,
                        TeamConst::_REAL_TASK_ADD_ORDER_TERMINAL_])) {
                        $discountMoney = $tmpDiscountMoney;
                    }
                    $ordersInfo = [
                        $sonOrderNum => [
                            'team_order_id' => $value['main_ordernum'],
                            'tnum' => $num,
                            'discount_money' => $discountMoney,
                        ],
                    ];
                    if (in_array($actionType, [TeamConst::_REAL_TASK_ADD_TICKET_TERMINAL_])) {
                        $windowSaleMoney = $num * $value['sale_money'];
                    }
                    if (in_array($actionType,
                            [TeamConst::_REAL_TASK_ADD_ORDER_TERMINAL_]) && isset($terminalPayTrackInfo[$sonOrderNum])) {
                        $windowSaleMoney = $terminalPayTrackInfo[$sonOrderNum]['tnum'] * $value['sale_money'];
                    }

                    break;
                case TeamConst::_REAL_TASK_PRINT_:
                    $printNum = $num;
                    $printOrderInfo = [
                        $sonOrderNum => [
                            'team_order_id' => $value['main_ordernum'],
                            'tnum' => $num,
                        ],
                    ];
                    break;
            }
            $groupInfo = $this->getGroupBiz()->queryOneGroupAndDistributor(intval($value['buyerid']), intval($value['sellerid']));
            $returnData[$sonOrderNum][$value['buyerid']] = [
                'fid' => $value['buyerid'],
                'aid' => $value['sellerid'],
                //分销商组
                'buyer_group_id' => $groupInfo[0]['groupId'] ?? 0,
                'team_order_id' => $value['main_ordernum'] ?? 0,
                'lid' => $orderInfo['lid'],
                'tid' => $orderInfo['tid'],
                'level' => $value['order_level'],
                'order_type' => $orderInfo['ordermode'],
                'order_source' => $orderInfo['ordermode'],
                'pay_mode' => $value['pmode'],
                'cancel_ticket_num' => $cancelNum,
                'revoke_ticket_num' => $revokeNum,
                'print_ticket_num' => $printNum,
                'sale_money' => $orderMoney,
                'window_sale_money' => $windowSaleMoney,
                'cost_money' => $costMoney,
                'cancel_sale_money' => $cancelMoney,
                'revoke_sale_money' => $revokeMoney,
                'order_ticket_num' => $orderNum,
                'orders_info' => $ordersInfo,
                'orders_cancel_info' => $cancelOrderInfo,
                'revoke_orders_info' => $revokeOrderInfo,
                'print_orders_info' => $printOrderInfo,
                'cancel_discount_money' => $cancelDiscountMoney ?? 0,
                'revoke_discount_money' => $revokeDiscountMoney ?? 0,
                'discount_money' => $discountMoney ?? 0,
            ];
        }

        return $returnData;
    }


    /**
     * 根据子单号获取验证子订单信息
     *
     * @param string $sonOrderNum 订单号
     * @param int $num 本次操作数量
     * @param int $actionType 操作类型 0:下单 1：取消  2：撤销撤改 3：新增票数 4：打印票类 5：团单添加订单的数据 6：团单验证 7:云票务下单 8：云票务添加订单的数据
     * @param array $extContent
     * @return array
     * @throws \Exception
     */
    private function _getCheckOrderInfo($sonOrderNum, $num, $actionType, $extContent = [])
    {
        if (empty($sonOrderNum)) {
            return [];
        }
        if (empty($this->_teamOrderModel)) {
            $this->_teamOrderModel = new TeamOrderSearch();
        }
        $field = "buyerid, sellerid, order_level, pmode, sale_money, cost_money, l.son_ordernum, l.main_ordernum";
        //获取团单层级关系
        $childOrderList = $this->_teamOrderModel->getSonOrderSplitInfoByOrderNum($sonOrderNum, 0, 0, 0, 0, $field);
        if (empty($childOrderList)) {
            pft_log($this->_error, "{$sonOrderNum}订单分销数据获取出错原因：" . $this->_teamOrderModel->getDbError());

            return [];
        }
        $orderToolModel = new OrderTools();
        $orderInfo = $orderToolModel->getOrderInfo($sonOrderNum);
        if (empty($orderInfo)) {
            pft_log($this->_error, "{$sonOrderNum}子订单数据获取错误：" . json_encode($orderInfo, JSON_UNESCAPED_UNICODE));

            return [];
        }
        $orderExtContent = empty($orderInfo['ext_content']) ? [] : json_decode($orderInfo['ext_content'], true);
        $orderDiscountMoney = 0;
        if ((isset($orderExtContent['bargainPricePolicyId']) && !empty($orderExtContent['bargainPricePolicyId'])) || (isset($orderExtContent['specifyReducedPolicyId']) && !empty($orderExtContent['specifyReducedPolicyId']))) {
            //通过下标获取到对应下标优惠券抵扣金额数值
            $serialNumber = $extContent['serial_number'];
            $idxArr = [];
            if (!empty($serialNumber)) {
                $idxInfo = explode(',', $serialNumber);
                foreach ($idxInfo as $item) {
                    $idx = explode('-', $item);
                    for ($i = $idx[0]; $i <= $idx[1]; $i++) {
                        $idxArr[] = $i;
                    }
                }
            }
            $tempOrderDiscountRes = $this->_getOrderDiscountApi()->list($sonOrderNum, 0, $idxArr);
            foreach ($tempOrderDiscountRes['data'] as $tmp) {
                if (in_array($tmp['couponType'], [4, 5])) {
                    $orderDiscountMoney += $tmp['couponPrice'];
                }
            }
        }
        $returnData = [];
        //是否当天验证 1 是 2 不是
        $orderMoney = $costMoney = $revokeSaleMoney = 0;
        $ordersInfo = $revokeOrderInfo = [];
        $checkTicketNum = $revokeTicketNum = 0;
        foreach ($childOrderList as $value) {
            $tmpDiscountMoney = $orderDiscountMoney;
            if ($value['buyerid'] != $orderInfo['member'] || $value['sellerid'] != $orderInfo['aid']) {
                $tmpDiscountMoney = 0;
            }
            if ($actionType == TeamConst::_REAL_TASK_REVOKE_) {
                $revokeDiscountMoney = $tmpDiscountMoney;
                $revokeSaleMoney = $num * $value['sale_money'];
                $revokeTicketNum = $num;
                $revokeOrderInfo = [
                    $sonOrderNum => [
                        'team_order_id' => $value['main_ordernum'],
                        'tnum' => $num,
                        'revoke_discount_money' => $revokeDiscountMoney,
                    ],
                ];
            } else if ($actionType == TeamConst::_REAL_TASK_CHECK_) {
                $discountMoney = $tmpDiscountMoney;
                $orderMoney = $num * $value['sale_money'];
                $costMoney = $num * $value['cost_money'];
                $checkTicketNum = $num;
                $ordersInfo = [
                    $sonOrderNum => [
                        'team_order_id' => $value['main_ordernum'],
                        'tnum' => $num,
                        'discount_money' => $discountMoney,
                    ],
                ];
                $todayCheck = 2;
                if (strtotime(date('Ymd')) == strtotime(date('Ymd', strtotime($orderInfo['ordertime'])))) {
                    $todayCheck = 1;
                }
            }
            $groupInfo = $this->getGroupBiz()->queryOneGroupAndDistributor(intval($value['buyerid']), intval($value['sellerid']));
            $returnData[$sonOrderNum][$value['buyerid']] = [
                'fid' => $value['buyerid'],
                'aid' => $value['sellerid'],
                //分销商组
                'buyer_group_id' => $groupInfo[0]['groupId'] ?? 0,
                'team_order_id' => $value['main_ordernum'],
                'lid' => $orderInfo['lid'],
                'tid' => $orderInfo['tid'],
                'level' => $value['order_level'],
                'order_type' => $orderInfo['ordermode'],
                'pay_mode' => $value['pmode'],
                'sale_money' => $orderMoney,
                'cost_money' => $costMoney,
                'check_ticket_num' => $checkTicketNum,
                'orders_info' => $ordersInfo ?? [],
                'today_check' => $todayCheck ?? 0,
                'revoke_ticket_num' => $revokeTicketNum,
                'revoke_sale_money' => $revokeSaleMoney,
                'revoke_orders_info' => $revokeOrderInfo,
                'discount_money' => $discountMoney ?? 0,
                'revoke_discount_money' => $revokeDiscountMoney ?? 0,
            ];
        }

        return $returnData;
    }


    private function _getOrderDiscountApi()
    {
        if (empty($this->_orderDiscountApi)) {
            $this->_orderDiscountApi = new \Business\JavaApi\Order\OrderTicketDiscounts();
        }
        return $this->_orderDiscountApi;
    }

    private function _getTeamOrderReport()
    {
        if (empty($this->_teamOrderReport)) {
            $this->_teamOrderReport = new \Business\TeamOrder\TeamOrderReport();
        }
        return $this->_teamOrderReport;

    }

    private function _getTeamReportRealTaskModel()
    {
        if (empty($this->_teamReportRealTaskModel)) {
            $this->_teamReportRealTaskModel = new TeamReportRealTask();
        }
        return $this->_teamReportRealTaskModel;
    }

    private function _getTrackModel()
    {
        if (empty($this->_trackModel)) {
            $this->_trackModel = new \Model\Order\OrderTrack();
        }

        return $this->_trackModel;
    }

    private function getGroupBiz()
    {
        if (empty($this->groupBiz)) {
            $this->groupBiz = new GroupQuery();
        }
        return $this->groupBiz;
    }
}