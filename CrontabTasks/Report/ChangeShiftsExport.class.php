<?php
namespace CrontabTasks\Report;

use Business\Report\ChangeShiftsBusiness;
use Business\Report\Debug\Debug;
use Business\Report\Debug\LogPath;
use Business\Statistics\statistics;
use Library\Controller;

/**
 * @Author: Lerko
 * @Date:   2017-10-23 13:31:36
 * @Email: <EMAIL>
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2017-11-06 16:48:58
 */
/**
 * 班结报表数据入库定时任务cli
 * 1分钟执行一次 统计这一分钟内所有需要统计的信息
 */
class ChangeShiftsExport extends Controller
{
    use Debug;
    private $business;
    private $businessBiz;

    public function __construct()
    {
        set_time_limit(0);//脚本运行时间不受时间限制
        $this->business    = new ChangeShiftsBusiness();
        $this->businessBiz = new statistics();
    }

    /**
     * 调用导出班结数据
     * <AUTHOR>
     * @DateTime 2017-10-24T11:41:18+0800
     */
    public function export()
    {
        $this->debug(false);
        try {
            $this->business->debug(false);
            $data = $this->business->getExportList();
            foreach ($data as $key => $value) {
                $staff_ids    = [];
                $terminal_ids = [];
                if ($value->getcsChannel() == 6) {
                    $type         = 2;
                    $terminal_ids = $value->setChilderJsonStr(false)->getDataIds();
                } else {
                    $type      = 1;
                    $staff_ids = $value->setChilderJsonStr(false)->getDataIds();
                    /** 转换终端数据为int类型的数据 */
                    ChangeShiftsBusiness::trunArrayValueToInt($terminal_ids);
                }
                $yestoday_time = date("Y-m-d", strtotime("-1day")) . " ";
                $today_time    = date("Y-m-d") . " ";
                $this->debuing("yestoday_time", $yestoday_time);
                $this->debuing("today_time", $today_time);

                /** @var string 统计的开始时间 一般为前一天的时间 */
                $start_time = $yestoday_time . $value->setTimeFormatToStr(true)->getyesterdayCsTime();
                /** @var string 统计的结束时间 */
                $end_time   = $today_time . $value->setTimeFormatToStr(true)->gettodayCsTime();
                /** @var 获取统计需要的渠道信息 setcsChannelMapToStatistcs(true) 将系统渠道映射为统计方式的渠道 */
                $channel    = [$value->setcsChannelMapToStatistcs(true)->getcsChannel()];
                $pid        = [$value->getPid()];
                var_dump($channel);
                /** 调用统计函数 */
                $result = $this->businessBiz->setClassSettleReport(
                    $start_time,
                    $end_time,
                    $channel,
                    $type,
                    $staff_ids,
                    $pid,
                    $terminal_ids
                );
                var_dump($result);
                if ($result['code'] != 200) {
                    \pft_log(LogPath::ERROR, json_encode($value->toArray()) . json_encode($result) . "\n");
                } else {
                    \pft_log(LogPath::SUCCESS, json_encode($value->toArray()) . json_encode($result) . "\n");
                }
            }
        } catch (\Exception $e) {
            echo $e->getMessage();
            \pft_log(LogPath::ERROR, $e->getMessage() . "\n");
        }
    }
}
