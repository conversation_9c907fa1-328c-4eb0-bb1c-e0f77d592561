<?php
/**
 * 出入园统计任务
 * <AUTHOR>
 * @date   2020-09-13
 */

namespace CrontabTasks\Report;

//权限判断
use Library\Cache\Cache;
use Library\Constants\RedisKey\DcKeyConst;
use Library\Constants\Roll;
use Model\DataCollection\DataCollection;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class DcCount
{
    private $taskNum = 1000;   //10秒执行1000条

    public function DcCountTask()
    {
        $redisService = Cache::getInstance('redis');
        $lockRes      = $redisService->lock(DcKeyConst::DC_IN_OUT_TASK_LOCK, 1, 300);
        if (!$lockRes) {
            echo '正在锁中';

            return false;   //等待上一个任务执行完成
        }
        $taskData = [];
        for ($i = 0; $i < $this->taskNum; $i++) {
            $data = $redisService->rPop(DcKeyConst::DC_IN_OUT_TASK);
            if (!$data) {
                break;
            }
            $taskData[] = @json_decode($data, true);
        }
        if (empty($taskData)) {
            echo '无任务';
            $redisService->lock_rm(DcKeyConst::DC_IN_OUT_TASK_LOCK);

            return true;
        }

        pft_log('collectParkSummary/task_count','处理了:'.count($taskData));

        $dcModel         = new DataCollection();
        $needAddInfoData = [];
        $resAddData      = [];
        $resUpdateData   = [];
        foreach ($taskData as $key => $value) {
            //0-入园 1-出园 2-员工入园 3-? 4-预检
            if (!$value['deviceKey'] || !in_array($value['roll'], Roll::ALLOW_ROLLS)) {  //特征码必填
                continue;
            }

            //单次的数量
            $oneNum = $value['num'];

            $nowDate   = date('Ymd', $value['nowTime']);
            $keyString = $value['landId'] . ':' . $nowDate . ':' . $value['deviceKey'] . ':' . $value['roll'];
            if (isset($resAddData[$keyString])) {    //已经存在就覆盖新增  反正都是按顺序添加的
                $oneNum                 = $value['num'] - $resAddData[$keyString]['num'];
                $resAddData[$keyString] = $value;
            } elseif (isset($resUpdateData[$keyString])) { //已经存在就覆盖更新
                $oneNum                    = $value['num'] - $resUpdateData[$keyString]['num'];
                $resUpdateData[$keyString] = $value;
            } else {
                $queryRes = $dcModel->getParkSummaryData($value['landId'], $nowDate, $value['deviceKey'],
                    $value['roll']);
                if ($queryRes) {
                    $oneNum                    = $value['num'] - $queryRes['num'];
                    $resUpdateData[$keyString] = $value;
                } else {
                    $resAddData[$keyString] = $value;
                }
            }

            $needAddInfoData[] = [
                'sid'         => $value['sid'] ?? 0,
                'land_id'     => $value['landId'],
                'num'         => $oneNum,
                'create_time' => $value['nowTime'],
                'device_key'  => $value['deviceKey'],
                'roll'        => $value['roll'],
                'all_num'     => $value['num'],
            ];

        }
        foreach ($resUpdateData as $k => $v) {
            $updateDate = date('Ymd', $v['nowTime']);
            $updateRes  = $dcModel->updateParkSummary($v['landId'], $updateDate, $v['deviceKey'], $v['roll'],
                $v['num'], $v['nowTime']);
            if (!$updateRes) {
                $logData = [
                    'terminal_id' => $v['terminalId'],
                    'salerid'     => $v['salerId'],
                    'num'         => $v['num'],
                    'roll'        => $v['roll'],
                    'sql'         => $dcModel->_sql(),
                ];
                pft_log('collectParkSummary', 'update' . json_encode($logData));
            }
        }
        $insertData = [];
        foreach ($resAddData as $add) {
            $addDate      = date('Ymd', $add['nowTime']);
            $insertData[] = [
                'sid'        => $add['sid'] ?? 0,
                'land_id'    => $add['landId'],
                'date'       => $addDate,
                'device_key' => $add['deviceKey'],
                'roll'       => $add['roll'],
                'num'        => $add['num'],
                'createtime' => $add['nowTime'],
                'updatetime' => 0,
            ];
        }

        if (!empty($insertData)) {
            $addRes = $dcModel->insertParkSummary($insertData);
            if (!$addRes) {
                $logData = [
                    'sql' => $dcModel->_sql(),
                ];
                pft_log('collectParkSummary', 'add' . json_encode($logData));
            }
        }

        if (!empty($needAddInfoData)) {
            $addInfoRes = $dcModel->insertParkSummaryInfo($needAddInfoData);
            if (!$addInfoRes) {
                $logData = [
                    'sql' => $dcModel->_sql(),
                ];
                pft_log('collectParkSummary', 'addInfo' . json_encode($logData));
            }
        }
        $redisService->lock_rm(DcKeyConst::DC_IN_OUT_TASK_LOCK);
    }


    public function fillSidForDaycountInfo()
    {
        $dcModel = new DataCollection();
        //$landModel = new \Model\Product\Land();
        $sql = 'SELECT DISTINCT land_id FROM `pft_dc_daycount_info`';

        $lids = $dcModel->query($sql);

        $javaApi = new \Business\CommodityCenter\Land();
        $landInfo = $javaApi->queryLandMultiQueryById($lids);
        $sidMap   = array_column($landInfo, 'apply_did', 'id');

        foreach ($lids as $item) {
            $lid = $item['land_id'];
            $sid = $sidMap[$lid] ?? 0;

            //每次更新3000条
            while (1) {
                $where = ['land_id' => $lid];
                $data  = ['sid' => $sid];

                $res = $dcModel->table('pft_dc_daycount_info')->where($where)->limit(3000)->save($data);
                if (!$res) {
                    break;
                }
            }
        }

        var_dump('done');
    }


    public function fillSidForDaycountTwo()
    {
        $dcModel = new DataCollection();
        //$landModel = new \Model\Product\Land();
        $sql = 'SELECT DISTINCT land_id FROM `pft_dc_daycount_two`';

        $lids = $dcModel->query($sql);

        $javaApi = new \Business\CommodityCenter\Land();
        $landInfo = $javaApi->queryLandMultiQueryById($lids);
        $sidMap   = array_column($landInfo, 'apply_did', 'id');

        foreach ($lids as $item) {
            $lid = $item['land_id'];
            $sid = $sidMap[$lid] ?? 0;

            //每次更新3000条
            while (1) {
                $where = ['land_id' => $lid];
                $data  = ['sid' => $sid];

                $res = $dcModel->table('pft_dc_daycount_two')->where($where)->limit(3000)->save($data);
                if (!$res) {
                    break;
                }
            }
        }

        var_dump('done');
    }

    /**
     * 摄像头数据入库
     * @return bool
     *
     */
    public function CameraDcCountTask()
    {
        $redisService = Cache::getInstance('redis');
        $lockRes      = $redisService->lock('DC_CAMERA_DATA_TASK', 1, 300);
        if (!$lockRes) {
            echo '正在锁中';
            return false;   //等待上一个任务执行完成
        }
        $taskData = [];
        for ($i = 0; $i < $this->taskNum; $i++) {
            $data = $redisService->rPop('CAMERA::INOUT::DATA::TASK');
            if (!$data) {
                break;
            }
            $taskData[] = @json_decode($data, true);
        }
        if (empty($taskData)) {
            echo '无任务';
            $redisService->lock_rm('DC_CAMERA_DATA_TASK');
            return true;
        }

        $dcModel         = new DataCollection();
        $resAddData      = [];
        foreach ($taskData as $key => $value) {
            if (!$value['macAddress'] ) {  //设备唯一码
                continue;
            }

            //出入园数据都为0的话就不往数据库记录了
            if (empty($value['totalIn']) && empty($value['totalOut'])){
                continue;
            }

            $nowDate   = $value['date'];
            $keyString = $value['macAddress'] . ':' . $nowDate ;
            if (isset($resAddData[$keyString])) {
                $oneIn                  = $value['totalIn'] - $resAddData[$keyString]['totalIn'];
                $oneOut                 = $value['totalOut'] - $resAddData[$keyString]['totalOut'];
                $resAddData[$keyString] = $value;
            } else {
                $queryRes = $dcModel->getCameraDataCountByDate($nowDate, $value['macAddress']);
                if ($queryRes) {
                    $oneIn                  = $value['totalIn'] - ($queryRes['0']['num']?? 0);
                    $oneOut                 = $value['totalOut'] - ($queryRes['1']['num']?? 0);
                    $resAddData[$keyString] = $value;
                } else {
                    $resAddData[$keyString] = $value;
                    $oneIn                  = $value['totalIn'];
                    $oneOut                 = $value['totalOut'];
                }
            }

            $needAddInfoInData[] = [
                'num'         => $oneIn,
                'create_time' => $value['nowTime'],
                'mac_address' => $value['macAddress'],
                'roll'        => 0,
                'all_num'     => $value['totalIn'],
                'create_date' => $value['date']
            ];

            $needAddInfoOutData[] = [
                'num'         => $oneOut,
                'create_time' => $value['nowTime'],
                'mac_address' => $value['macAddress'],
                'roll'        => 1,
                'all_num'     => $value['totalOut'],
                'create_date' => $value['date']
            ];
        }

        //都为空的话这边数组合并会报错
        if (empty($needAddInfoInData) && empty($needAddInfoOutData)) {
            $redisService->lock_rm('DC_CAMERA_DATA_TASK');
            return false;
        }
        $addInfoRes = $dcModel->saveCameraDataCount(array_merge($needAddInfoInData,$needAddInfoOutData));
        if (!$addInfoRes) {
            $logData = [
                'sql' => $dcModel->_sql(),
            ];
            pft_log('collectParkSummary', 'addInfo' . json_encode($logData));
        }
        $redisService->lock_rm('DC_CAMERA_DATA_TASK');
    }
}