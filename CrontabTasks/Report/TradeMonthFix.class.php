<?php

namespace CrontabTasks\Report;
use Library\Controller;

/**
 * 交易记录月份修复脚本
 * 
 * @date    2019-04-08
 * <AUTHOR>
 */

if(!defined('PFT_CLI')) {
    exit('Access Deny');
}


class TradeMonthFix extends Controller {

    public function run() {
        $model = new \Library\Model();

        while (1) {
            $list = $model->query("SELECT id FROM pft_trade_journal WHERE MONTH(rectime) <> recmonth limit 5000");
            if (!$list) {
                break;
            }
            $idArr = array_column($list, 'id');
            $where = [
                'id' => ['in', $idArr]
            ];
     
            $data = ['recmonth' => ['exp', 'month(rectime)']];

            $res = $model->table('pft_trade_journal')->where($where)->save($data);
            if (!$res) {
                var_dump($model->_sql());
            }
            sleep(2);
        }
        var_dump('跑完了');
    }
}
