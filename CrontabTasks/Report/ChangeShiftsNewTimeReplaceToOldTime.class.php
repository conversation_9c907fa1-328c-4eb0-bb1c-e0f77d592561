<?php

namespace CrontabTasks\Report;

use Business\Report\ChangeShiftsBusiness;
use Library\Controller;

/**
 * @Author: CYQ19931115
 * @Date:   2017-11-02 10:17:08
 * @Last Modified by:   CYQ19931115
 * @Last Modified time: 2017-11-03 10:58:56
 */
/**
 * 批量替换新事件到旧的时间   但是更新时间不变
 */
class ChangeShiftsNewTimeReplaceToOldTime extends Controller
{
    private $business;
    public function __construct()
    {
        $this->business = new ChangeShiftsBusiness();
    }

    /**
     * 替换新的设置时间到旧的设置时间
     * <AUTHOR>
     * @dateTime 2017-11-02T10:32:45+0800
     */
    public function replaceTime()
    {
        $result = $this->business->replaceTime();
        var_dump($result);
        if ($result === false) {
            \pft_log("change_shifts_log", "时间替换失败：有可能是sql错误请检查sql错误");
        } elseif ($result === 0) {
            \pft_log("change_shifts_log", "没有记录需要更换时间");
        } else {
            \pft_log("change_shifts_log", "时间替换完成");
        }
    }
}
