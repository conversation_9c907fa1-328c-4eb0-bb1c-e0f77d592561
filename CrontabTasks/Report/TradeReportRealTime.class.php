<?php
/**
 * 交易报表
 * <AUTHOR>
 * @date   2019-07-20
 */
namespace CrontabTasks\Report;

use Model\Report\TradeReport as TradeReportModel;
use Model\TradeRecord\PftMemberJournal as PftMemberJournalModel;
use Library\Cache;

class TradeReportRealTime
{
    //日志
    const LOG_DIR   = 'real_time_trade_report';
    //缓存键值
    const CACHE_KEY = 'trade_report:journal:id';

    //缓存对象
    private $_cache;
    //当前日期
    private $_currentDate;
    //交易报表数据模型
    private $_tradeReportModel;
    //交易流水数据模型
    private $_journalModel;


    public function __construct()
    {
        $this->_tradeReportModel = new TradeReportModel();
        $this->_journalModel     = new PftMemberJournalModel();
        $this->_currentDate = date('Ymd');
        $this->_cache       = Cache\Cache::getInstance('redis');
    }


    /**
     * 实时汇总交易报表数据
     * <AUTHOR>
     * @date    2019-07-18
     * @return  array
     */
    public function create()
    {
        $begin = date('Y-m-d') . ' 00:00:00';
        $end   = date('Y-m-d') . ' 23:59:59';

        $lastId = $this->_getLastId();

        if (empty($lastId)) {

            if (false == $this->_isFirstTime()) {
                //缓存数据错误
                pft_log(self::LOG_DIR, '获取缓存数据错误');
                exit;
            }
            //直接通过时间区间去获取当天的数据
            $lastId = 0;
        }

        //交易流水数据
        $journalData = $this->_journalModel->getSubjectDataById($lastId, $begin, $end);
        $formatData  = $this->_handleData($journalData);

        $insertInComeData  = [];
        $insertOutComeData = [];

        //收入
        if (!empty($formatData['inCome'])) {
            $insertInComeData = $this->_insertData($formatData['inCome'], TradeReportModel::ACTION_INCOME);
        }

        //支出
        if (!empty($formatData['outCome'])) {
            $insertOutComeData = $this->_insertData($formatData['outCome'], TradeReportModel::ACTION_OUTCOME);
        }

        $insertData = array_merge($insertInComeData, $insertOutComeData);

        //更新交易报表数据
        if (!empty($insertData)) {
            //写入数据
            $insertRes  = $this->_tradeReportModel->insertTradeData($insertData);
            !$insertRes && pft_log(self::LOG_DIR, '更新交易报表数据失败:' . json_encode($lastId, $journalData, $insertData));
        }

        //更新交易报表账本余额数据
        if (!empty($formatData['lastMoney'])) {
            $this->_updateBalance($formatData['lastMoney']);
        }

        //更新执行的缓存
        if (!empty($formatData['maxId'])) {
            //设置本次执行流水数据的最终id
            $this->_cache->set(self::CACHE_KEY, $formatData['maxId'], '', 3600 * 24);
        }
    }


    /**
     * 更新交易报表数据
     * <AUTHOR>
     * @date    2019-07-18
     * @param   array     $data   处理汇总数据
     * @return  array
     */
    private function _insertData($data, $action)
    {
        $insertData = [];

        if (!in_array($action, [TradeReportModel::ACTION_OUTCOME, TradeReportModel::ACTION_INCOME])) {
            return $insertData;
        }

        foreach ($data as $fid => $subjectInfo) {
            foreach ($subjectInfo as $subjectCode => $dtypeInfo) {
                foreach ($dtypeInfo as $dtype => $value) {
                    $insertData[] = [
                        'date'         => $this->_currentDate,
                        'fid'          => $fid,
                        'subject_code' => $subjectCode,
                        'dtype'        => $dtype,
                        'action'       => $action,
                        'dmoney'       => $value,
                        'create_time'  => time(),
                    ];
                }
            }
        }
        return $insertData;
    }


    /**
     * 更新交易报表余额数据 //todo: 改成批量更新
     * <AUTHOR>
     * @date    2019-07-18
     * @param   array     $balanceData   余额数据
     * @return  array
     */
    private function _updateBalance($balanceData)
    {
        foreach ($balanceData as $fid => $subject) {
            foreach ($subject as $subjectCode => $money) {

                $res = $this->_tradeReportModel->updateBalance($fid, $this->_currentDate, $subjectCode, $money);
                !$res && pft_log(self::LOG_DIR, "更新余额失败: fid-{$fid}, subject-{$subjectCode}, money-{$money}");

            }
        }
    }


    /**
     * 汇总流水数据
     * <AUTHOR>
     * @date    2019-07-18
     * @param   array     $data   处理汇总数据
     * @return  array
     */
    private function _handleData($data)
    {
        $inCome    = [];
        $outCome   = [];
        $lastMoney = [];
        $maxId     = 0;

        foreach ($data as $value) {

            $maxId   = $value['id'];
            //商家id
            $fid     = $value['fid'];
            //费用项
            $dtype   = $value['template_item_code'];
            //账本
            $subject = $value['subject_code'];
            //交易金额
            $dmoney  = $value['dmoney'];

            if ($value['daction'] == 0) {

                if (isset($inCome[$fid]) && isset($inCome[$fid][$subject]) && isset($inCome[$fid][$subject][$dtype])) {
                    $inCome[$fid][$subject][$dtype] += $dmoney;
                } else {
                    $inCome[$fid][$subject][$dtype]  = $dmoney;
                }

            } elseif ($value['daction'] == 1) {

                if (isset($outCome[$fid]) && isset($outCome[$fid][$subject])) {
                    $outCome[$fid][$subject][$dtype] += $dmoney;
                } else {
                    $outCome[$fid][$subject][$dtype]  = $dmoney;
                }
            }

            //跟查询数据的升降序有关
            $lastMoney[$fid][$subject] = $value['lmoney'];
        }

        return ['inCome' => $inCome, 'outCome' => $outCome, 'lastMoney' => $lastMoney, 'maxId' => $maxId];
    }


    /**
     * 从缓存获取上次获取到的流水数据id
     * <AUTHOR>
     * @date    2019-07-18
     * @return  integer
     */
    private function _getLastId()
    {
        return $this->_cache->get(self::CACHE_KEY);
    }


    /**
     * 是否当天的第一次处理数据
     * <AUTHOR>
     * @date    2019-07-18
     * @return  integer
     */
    private function _isFirstTime()
    {
        $todayCount = $this->_tradeReportModel->getCount($this->_currentDate);
        return $todayCount ? false : true;
    }

}