<?php

namespace CrontabTasks\Report;

use Library\Controller;
use Model\Report\CustomSar;

/**
 * 定制-生成分账报表
 */
class CustomSarReport extends Controller
{
    private $customSarReportBiz = null;

    //脚本依赖这个表summary.pft_report_sid_tid_relation，这里也需要设置白名单 \Business\Report\MinuteReport::ALLOW_MEMBER_IDS
    private $sidWhitelist = ENV == 'PRODUCTION' ? [
        //测试用户id
        28459860,
        27823449,
        27379841,
        27379854,
        27379871,
        33000294,
        33000356,
        13701405,
        16605577,
        12772761,
        //瘦西湖id
        29821585,
    ] : (ENV == 'TEST' ? [
        3263539,
        3263540,
        3263541,
        3263542,
        3265378,
        3265380,
        3250235,
        3262552,
        3265372,
        6970,
        3385,
    ] : [
        3385,
        6970,
    ]);

    /**
     * 创建初始化数据！！！！！！！！！！！
     * /usr/local/php/bin/php /var/www/html/Service/Crontab/runNew.php Report/CustomSarReport createDefaultData 3385
     *
     * @return void
     */
    public function createDefaultData()
    {
        $argv = func_get_args();
        $sid = intval($argv[3] ?? 0);
        $name = strval($argv[4] ?? '分账主体');
        if (!$sid) {
            echo '参数错误!', PHP_EOL;
            return;
        }
        $result = $this->getCustomSarReportBiz()->createDefaultData($sid, $name);
        if ($result) {
            echo 'Done!', PHP_EOL;
        } else {
            echo 'Fail', PHP_EOL;
        }
    }

    /**
     * 初始化项目-修复配置数据！！！！！！！！！！！
     * /usr/local/php/bin/php /var/www/html/Service/Crontab/runNew.php Report/CustomSarReport runHistoryStep1FixedObjectTicket 3385
     * @return void
     */
    public function runHistoryStep1FixedObjectTicket() {
        $argv = func_get_args();
        $sid = intval($argv[3] ?? 0);
        if (!$sid) {
            echo '参数错误!', PHP_EOL;
            return;
        }
        $result = $this->getCustomSarReportBiz()->runHistoryStep1FixedObjectTicket($sid);
        if ($result === true) {
            echo 'Done!', PHP_EOL;
        } else {
            echo 'Fail:', $result, PHP_EOL;
        }
    }

    /**
     * 初始化项目-修复配置数据！！！！！！！！！！！
     * /usr/local/php/bin/php /var/www/html/Service/Crontab/runNew.php Report/CustomSarReport runHistoryStep2 3385 "2023-10-14 00:00:00" "2023-10-15 00:00:00"
     * @return void
     * @throws \Exception
     */
    public function runHistoryStep2() {
        $argv = func_get_args();
        $sid = intval($argv[3] ?? 0);
        $startDate = $argv[4] ?? 0;
        $endDate = $argv[5] ?? 0;
        $reportType = $argv[6] ?? 0x03;
        if (!$startDate || !$endDate) {
            throw new \Exception('参数错误');
        }
        $sidCollection = $sid ? [$sid] : $this->sidWhitelist;
        echo 'Start...', date('Y-m-d H:i:s'), PHP_EOL;
        $result = $this->getCustomSarReportBiz()->runHistoryStep2($sidCollection, $startDate, $endDate, $reportType);
        echo date('Y-m-d H:i:s'), ' ';
        if ($result === true) {
            echo 'Done!', PHP_EOL;
        } else {
            echo 'Fail:', $result, PHP_EOL;
        }
    }

    /**
     * 5分钟任务
     * /usr/local/php/bin/php /var/www/html/Service/Crontab/runNew.php Report/CustomSarReport fiveMinutes 【供应商ID】
     *
     * @return void
     */
    public function fiveMinutes()
    {
        $argv = func_get_args();
        $sid = intval($argv[3] ?? 0);
        $sidCollection = $sid ? [$sid] : $this->sidWhitelist;
        $this->getCustomSarReportBiz()->fiveMinutes($sidCollection);
    }

    /**
     * 次日重跑数据
     * /usr/local/php/bin/php /var/www/html/Service/Crontab/runNew.php Report/CustomSarReport daily 【供应商ID】
     *
     * @return void
     */
    public function daily()
    {
        $argv = func_get_args();
        $sid = intval($argv[3] ?? 0);
        $sidCollection = $sid ? [$sid] : $this->sidWhitelist;
        $this->getCustomSarReportBiz()->daily($sidCollection);
    }

    /**
     * @return \Business\Report\CustomSarReport
     */
    protected function getCustomSarReportBiz()
    {
        if (!$this->customSarReportBiz) {
            $this->customSarReportBiz = new \Business\Report\CustomSarReport();
        }
        return $this->customSarReportBiz;
    }
}