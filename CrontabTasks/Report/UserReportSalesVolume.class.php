<?php
/**
 * 按供应商或者分销商跑全部报表数据
 */

namespace CrontabTasks\Report;

use Library\Controller;
use Library\JsonRpc\PftRpcClient;

class UserReportSalesVolume extends Controller
{

    protected $_logPath    = 'report/getSalesVolume';
    protected $_getMethod_ = 'getSalesVolume';
    protected $_year       = [2017,2018,2019,2020,2021];
    protected $_month      = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];
    protected $_result     = [];
    protected $_totalNum   = 0;
    protected $_mPage      = 1; //用户分页
    protected $_isSave     = 0; //0不保存，1保存
    protected $_isUser     = 0; //0供应商 1分销商 2分销商和供应商

    protected $memberMode = '';

    /**
     * 入口
     * /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/UserReportSalesVolume index 0 0 2
     */
    public function index()
    {
        //3 是否保存 4起始页 5-0供应商 1分销商 2分销商和供应商
        $params = $GLOBALS['argv'];
        $this->_isSave = empty($params[3]) ? 0 : 1;
        $this->_mPage = empty($params[4]) ? 1 : intval($params[4]);
        $this->_isUser = empty($params[5]) ? 0 : intval($params[5]);
        echo "开始脚本\n";
        $this->_memberPage();
    }

    /**
     * 获取供应商和分销商
     * <AUTHOR>
     * @date 2021/3/19
     *
     */
    protected function _memberPage()
    {
        sleep(6);
        $dtype = [0];
        if ($this->_isUser == 1) {
            $dtype = [1];
        } else if ($this->_isUser == 2) {
            $dtype = [0, 1];
        }
        if (empty($this->memberMode)) {
            $this->memberMode = new \Model\Member\Member();
        }
        $member = $this->memberMode->getInfoMemberByCondition('id', false, false, $dtype, false, false, false,false, $this->_mPage, 1000);
        if (empty($member)) {
            pft_log($this->_logPath, "salesVolume:脚本结束");
            echo $this->_mPage . "脚本结束\n";
            exit();
        }
        $memberIds = array_column($member, 'id');
        if (in_array(3385, $memberIds) || in_array(6970, $memberIds)) {
            pft_log($this->_logPath . '/page', "出现标识数据~！！！！");
        }
        pft_log($this->_logPath . '/page', "页数： " . $this->_mPage . "，id集：" . json_encode($memberIds));
        $this->reportStart($memberIds);
    }

    /**
     * 统计
     * <AUTHOR>
     * @date 2021/3/19
     *
     * @param $memberIds
     */
    protected function reportStart($memberIds)
    {
        $code = 200;
        $msg  = '';
        try {
            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
            $task = new \Library\MulityProcessHelper($this, 5, 1);
            //使用多进程之前 释放掉已有的所有链接
            //$this->_unsetAllModel();
            $task->run($memberIds); // 如果前面连接了数据库、redis等，最好在这之前关闭掉
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
            pft_log($this->_logPath, "salesVolume:end:{$msg}");
        }
        $this->_mPage = intval($this->_mPage) + 1;
        $this->_memberPage();
    }

    /**
     * 执行处理
     * <AUTHOR>
     * @date 2021/3/19
     *
     * @param $memberIds
     */
    public function runWorker($memberIds)
    {
        //
        $code = 200;
        $msg  = '';
        if (is_array($memberIds)) {
            $memberIds = array_values(array_unique($memberIds));
        }
        try {
            $this->_yearRun($memberIds);
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }
    }

    /**
     * 按年执行
     * <AUTHOR>
     * @date 2021/3/19
     *
     * @param $memberIds
     * @param  int  $y
     * @param  int  $m
     *
     * @return true
     */
    private function _yearRun($memberIds, $y = 0, $m = 0)
    {
        if (!isset($this->_month[$m])) {
            $y++;
            $m = 0;
        }
        if (!isset($this->_year[$y])) {
            //触发保存
            if ($this->_isSave) {
                $this->_saveFidData();
            }
            //重置
            $this->_result = [];
            return true;
//            echo "脚本结束3\n";
//            exit();
        }
        $this->_reportPage($memberIds, 1, $this->_year[$y], $this->_month[$m], $y, $m);
    }

    /**
     * 获取指定范围的商家票类统计数据
     * <AUTHOR>
     * @date 2021/3/17
     *
     * @param $page
     * @param $year
     * @param $month
     * @param $y
     * @param $m
     *
     * @return mixed
     */
    private function _reportPage($memberIds, $page, $year, $month, $y, $m)
    {
        $pageSize = 1000;
        $dateType = 2;
        $data = [
            'startTime' => strtotime($year . $month . '01 00:00:00'),
            'endTime'   => strtotime('first day of +1 month', strtotime($year . $month . '01 00:00:00')) - 1,
            'fid'       => $memberIds,
            'page'      => $page,
            'size'      => $pageSize,
            'dateType'  => $dateType,
        ];
        if ($this->_totalNum == 0) {
            //设置返回分页
            $data['needTotal'] = true;
        }
        $result = $this->requestCall($this->_getMethod_, $data);
        if (!empty($result['list'])) {
            $this->_totalNum = isset($result['total']) ? $result['total'] : $this->_totalNum;
            $ratio           = round(($pageSize * $page) / $this->_totalNum, 2) * 100;
            $ratio           = $ratio > 100 ? 100 : $ratio;
            //文本提示
            echo "当前第 $page 页, 统计节点: " .
                 (date("Y-m-d", $data['startTime'])) . "~" . (date("Y-m-d", $data['endTime'])) .
                 ", 每页数量: $pageSize, 总数:" .
                 $this->_totalNum .
                 ", 统计进度: " . $ratio . "%" . "\n";
            //数据处理
            $list = is_array($result['list']) ? $result['list'] : [];
            foreach ($list as $item) {
                $fid = $item['fid'];
                $rid = $item['resellerId'];
                $lid = $item['lid'];
                $key = $fid . '|' . $rid . '|' . $lid;
                //票数汇总
                if (isset($this->_result[$key])) {
                    $this->_result[$key]['order']  += +$item['orderTicket'];
                    $this->_result[$key]['cancel'] += +$item['cancelTicket'];
                    $this->_result[$key]['revoke'] += +$item['revokeTicket'];
                } else {
                    $this->_result[$key] = [
                        'order'  => $item['orderTicket'],
                        'cancel' => $item['cancelTicket'],
                        'revoke' => $item['revokeTicket'],
                    ];
                }
            }

            pft_log($this->_logPath, "salesVolume:查询总数：" . $this->_totalNum);

            //下一页
            if (($page * $pageSize) < $this->_totalNum) {
                $page++;

                #sleep(1);
                return $this->_reportPage($memberIds, $page, $year, $month, $y, $m);
            } else {
                sleep(1);
                //echo "******************************************************************************************************************************** \n";
                //已查询完当前年份
                $m++;
                $this->_yearRun($memberIds, $y, $m);
            }
        } else {
            //echo "无统计数据 : $page \n";
            //echo "******************************************************************************************************************************** \n";
            $m++;
            $this->_yearRun($memberIds, $y, $m);
            pft_log($this->_logPath, "查询失败，查询参数：" . json_encode($data));
        }
    }

    /**
     * 执行保存
     * <AUTHOR>
     * @date 2021/3/17
     *
     * @return bool
     */
    private function _saveFidData()
    {
        $data = $this->_handleResult();
        //pft_log($this->_logPath, json_encode($data, JSON_UNESCAPED_UNICODE));
        if (empty($data)) {
            return true;
        }
        $this->insertInfo($data);
        echo "影响条数:" . count($data) . "\n";
        echo "**************************** \n";

        return true;
    }

    private function insertInfo($data)
    {
        $model = new \Library\Model('summary');
        //插入统计数据
        $model->table('pft_report_member_sales_volume_tmp')->addAll($data);
        pft_log($this->_logPath, "salesVolume:写入数据：" . json_encode($data));

    }

    private function _handleResult()
    {
        $result = is_array($this->_result) ? $this->_result : [];
        $data = [];
        foreach ($result as $key => $value) {
            $keyArr = explode("|", $key);
            $sid    = $keyArr[0];
            $fid    = $keyArr[1];
            $lid    = $keyArr[2];
            $order  = $value['order'];
            $cancel = $value['cancel'];
            $revoke = $value['revoke'];

            if (!$fid || !$sid || !$lid) {
                pft_log($this->_logPath, "salesVolume:数据异常，$key");
                continue;
            }

            $data[] = [
                'fid' => $fid,
                'sid' => $sid,
                'lid' => $lid,
                'order_ticket' => $order,
                'cancel_ticket' => $cancel,
                'revoke_ticket' => $revoke,
                'create_time' => time(),
                'update_time' =>time(),
            ];
        }

        return $data;
    }

    /**
     * json-rpc 请求
     * <AUTHOR>
     * @date 2021/3/12
     *
     * @param $method
     * @param $params
     * @param  string  $callService
     *
     * @return array
     */
    private function requestCall($method, $params, $callService = 'statistics')
    {
        if (!is_array($params)) {
            pft_log($this->_logPath, "参数不是数组");
        }

        if (!isset($method)) {
            pft_log($this->_logPath, "方法缺失");
        }

        $module = 'report'; //模块名

        try {
            $lib  = new PftRpcClient($callService);
            $res  = $lib->call($method, $params, $module);
            $data = $res['data'];
            $code = $res['code'];
            $msg  = $res['msg'];
            echo $msg;
        } catch (\Exception $e) {
            $msg  = $e->getMessage();
            $code = $e->getCode();
            $data = [];
            pft_log($this->_logPath, json_encode([$code, $msg, $data], JSON_UNESCAPED_UNICODE));
        }

        return $data;
    }
}