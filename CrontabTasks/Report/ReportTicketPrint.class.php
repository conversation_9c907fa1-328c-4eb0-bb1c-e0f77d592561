<?php
namespace CrontabTasks\Report;

use Model\TerminalManage\PaperTicketprintSummary;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

/**
 * 每日数据汇总定时任务 php runNew.php Report_ReportTicketPrint handel
 * 数据补漏             php runNew.php Report_ReportTicketPrint replenish yyyy-mm-dd
 * 每天凌晨汇总终端取票数量
 * User: lanwanhui
 * Date: 2021/8/30
 */
class ReportTicketPrint
{

    /**
     * 定时任务汇总每日出票数据
     * User: lanwanhui
     * Date: 2021/8/30
     */
    public function handel(){

        //昨天日期
        $date = date("Ymd",strtotime("-1 day"));

        $this->summary($date);

        pft_log('paperticketprint_summary','汇总数据 '.$date);

    }


    /**
     * 数据补漏
     * User: lanwanhui
     * Date: 2021/6/24
     *
     * @param $arg0
     * @param $arg1
     * @param $arg2
     * @param string $date 日期
     *
     */
    public function replenish($arg0, $arg1, $arg2, $date = '')
    {
        if(empty($date) || strlen($date) != 10 ){
            exit("yyyy-mm-dd日期不能为空或长度错误\n");
        }

        $dateArr =explode('-',$date);
        if (!$dateArr || count($dateArr) != 3) {
            exit("yyyy-mm-dd日期格式错误1\n");
        }

        if (!checkdate($dateArr[1], $dateArr[2], $dateArr[0])){
            exit("yyyy-mm-dd日期格式错误2\n");
        }

        if (!strtotime($date)) {
            exit("yyyy-mm-dd日期格式错误3\n");
        }

        $date = date('Ymd',strtotime($date));

        pft_log('order_check_summary_replenish',$date);

        $this->summary($date);

    }


    /**
     * 处理汇总数据
     * User: lanwanhui
     * Date: 2021/8/31
     *
     * @param string $date 年月日日期yyyymmdd
     *
     */
    private function summary(int $date){

        $paperTicketprintModel =  new PaperTicketprintSummary();

        //有汇总过就不要汇总了
        $summaryInfo = $paperTicketprintModel->getSummaryInfoByCreateDate($date);
        if (!empty($summaryInfo)) {
            pft_log('paperticketprint_summary','数据已经汇总过' . $date);
            return;
        }

        $summaryList = $paperTicketprintModel->getRecordSummaryList($date);
        if (empty($summaryList)) {
            pft_log('paperticketprint_summary','无可汇总数据 '.$date);
            return;
        }

        $summaryList = array_chunk($summaryList,100);

        foreach ($summaryList as $summary) {

            $insertData = [];

            foreach ($summary as $value) {
                $insertData[] = [
                    //景区
                    'lid'            => $value['lid'],
                    //门票
                    'tid'            => $value['tid'],
                    //出票数量
                    'print_num'      => $value['print_num'],
                    //站点id
                    'site_id'        => $value['site_id'],
                    //供应商id
                    'sid'            => $value['sid'],
                    //设备类型
                    'device_type'    => $value['device_type'],
                    //出票年月
                    'create_month'   => date('Ym',strtotime($date)),
                    //纸票类型
                    'paper_type'     => $value['device_type'],
                    //操作员
                    'operator_id'    => $value['operator_id'],
                    //日期
                    'create_date'    => $date,
                ];
            }

            if (!empty($insertData)) {
                $rs = $paperTicketprintModel->addSummarys($insertData);
                if (!$rs) {
                    pft_log('paperticketprint_summary_error',json_encode($insertData));
                }
            }

        }

        pft_log('paperticketprint_summary','汇总结束 '.$date);

    }

}