<?php

/**
 *	交易记录汇总  脚本
 *  获取各个商户/交易类型/支付类型（交易账户）在昨日00：00至23：59的交易明细
 *  <AUTHOR>
 */

namespace CrontabTasks\Report;
use Business\JsonRpcApi\MessageService\MessageService;
use Library\Container;
use Library\Controller;
use Library\Util\EnvUtil;
use Model\TradeRecord;


//权限判断
if(!defined('PFT_CLI')) {
    exit('Access Deny');
}

class TransReport extends Controller{

    private $_pftMemberJournal;
    private $_pftTransReport;
    /** 
     * 入口
     */
    public function index() {
        $params = $GLOBALS['argv'];

        // 初始化数据表
        $this->_pftMemberJournal = new TradeRecord\PftMemberJournal();
        $this->_pftTransReport   = new TradeRecord\PftTransReport();

        if (isset($params[3]) && strtotime($params[3])) {
            $time     = $params[3];
            $execTime = date('Y-m-d', strtotime($time));
        } else {
            $time     = date('Y-m-d');
            $execTime = date('Y-m-d', strtotime($time) - 3600 * 24);
        }

        //开始任务
        pft_log('trans_report', json_encode(['start', $execTime]));

        try {

            //跑数据前删除指定日期的数据
            $del = $this->_pftTransReport->deleteData($execTime);
            if ($del === false) {
                throw new \Exception("删除原数据 出错");
            }

            //获取脚本执行当日的前一天所有有交易记录的fid和信用账户里的aid
            $res = $this->_pftMemberJournal->getTransFids($execTime);

            if ($res === false) {
                throw new \Exception("获取fid信息 出错");
            }

            //通过每个fid和账户类型（$b）搜索昨天的交易记录 信用账户比较特殊 还要搜索aid
            $insertTotalArr  = [];
            $insertDetailArr = [];
            //账户类型数据 插入结果
            $insertTotalRes  = true;
            //交易类型数据 插入结果
            $insertDetailRes = true;
            //是否有待插入的账户类型数据
            $totalPart       = false;
            //是否有待插入的交易类型数据
            $detailPart      = false;

            foreach ($res as $key => $val) {
                //根据每个用户去查询当天的交易记录 并处理数据 $val 用户ID
                $data = $this->_pftMemberJournal->getListInfoByFidAndActype((int)$val, $execTime);

                if (isset($data['code']) && $data['code'] == 'fail') {
                    throw new \Exception($data['msg']);
                }

                //某用户的某账户类型的 支出与收入 汇总
                if ($data['totalArr']) {
                    $resultTotal[]  = $data['totalArr'];
                }
                //某用户的某账户下的某种交易类型的 支出与收入 汇总
                if ($data['detailArr']) {
                    $resultDetail[] = $data['detailArr'];
                }
                unset($data);

                if (!empty($resultTotal)) {
                    $totalPart      = true;     //是否有最后一次未插入的数据
                    $insertTotalArr = array_merge($insertTotalArr, $resultTotal);
                    if (count($insertTotalArr) >= 500) {
                        $totalPart      = false;
                        $insertTotalRes = $this->_pftTransReport->insertTotalDataIntoTable($insertTotalArr, $execTime);
                        $insertTotalArr = [];
                    }
                    unset($resultTotal);
                }

                if (!empty($resultDetail)) {
                    $detailPart      = true;
                    $insertDetailArr = array_merge($insertDetailArr, $resultDetail);
                    if (count($insertDetailArr) >= 500) {
                        $detailPart  = false;
                        $insertDetailRes = $this->_pftTransReport->insertDetailDataIntoTable($insertDetailArr, $execTime);
                        $insertDetailArr = [];
                    }
                    unset($resultDetail);
                }

                if ($insertTotalRes === false || $insertDetailRes === false) {
                    throw new \Exception("数据写入失败");
                }
            }

            //最后一部分的数据处理
            if ($totalPart) {
                $insertTotalRes = $this->_pftTransReport->insertTotalDataIntoTable($insertTotalArr, $execTime);
            }

            if ($detailPart) {
                $insertDetailRes = $this->_pftTransReport->insertDetailDataIntoTable($insertDetailArr, $execTime);
            }

            if ($insertTotalRes === false || $insertDetailRes === false) {
                throw new \Exception("数据写入失败");
            }

            $this->_pftTransReport->recordTaskInfo($execTime, 1);

        } catch (\Exception $e) {
            $this->_pftTransReport->recordTaskInfo($execTime, 0);
            pft_log('trans_report', $e->getMessage());
            //失败 发送短信通知
            $mobileArr = load_config('trans_report', 'business');
            foreach ($mobileArr as $tel) {
                $sendData = ["TransReport_index", $execTime];
                $messageServiceApi = Container::pull(MessageService::class);
                [$approval, $result] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'report_exec_fail', $tel, $sendData);
                if ($approval) {
                    if (!EnvUtil::isRelease()) {
                        pft_log('message_service', json_encode(['transReport生成报表数据失败', __METHOD__, [$tel, $sendData], $result], JSON_UNESCAPED_UNICODE));
                    }
                } else {
                    /** @deprecated 放量结束后删除 */
                    $smsLib   = new \Library\MessageNotify\Platform\FzZwxSms($tel);
                    $result = $smsLib->reportExecFail($sendData);
                    if (!EnvUtil::isRelease()) {
                        pft_log('message_service', json_encode(['transReport生成报表数据失败.old', __METHOD__, [$tel, $sendData], $result], JSON_UNESCAPED_UNICODE));
                    }
                }
            }
        }

        //开始任务
        pft_log('trans_report', json_encode(['end', $execTime]));
    }

    /**
     * 专项款日汇总
     *
     * @date   2018-04-26
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return array
     */
    public function dailyMoneySummary() {
        pft_log('dailyMoneySummary', 'begin');

    }
}