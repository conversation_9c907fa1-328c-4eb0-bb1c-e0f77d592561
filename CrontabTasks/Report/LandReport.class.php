<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>
 * Date: 2017/4/1
 * Time: 11:11
 */

namespace CrontabTasks\Report;

use Model\DataCollection\DataCollection;

if (PHP_SAPI != 'cli') {
    exit('error');
}
defined('PFT_INIT') or exit('Access Denied');

/**
 * 景区统计类
 * Class LandReport
 * @package CrontabTasks\Report
 */
class LandReport
{
    private $_model = null;
    private $_logPath = 'landStat';

    public function __construct()
    {
        $this->_model = new DataCollection();

        //运行时间不做限制
        set_time_limit(0);
    }

    /**
     * 运行入口
     */
    public function run()
    {
        //$this->summary();die;
        pft_log($this->_logPath, 'start');
        $params = $GLOBALS['argv'];

        if (isset($params[3])) {
            $day       = $params[3];
            $beginTime = strtotime($day . ' 00:00:00');
            $endTime   = strtotime($day . ' 23:59:59');
        } else {
            $beginTime = strtotime(date('Y-m-d 00:00:00', strtotime('-1 days')));
            $endTime   = strtotime(date('Y-m-d 23:59:59', strtotime('-1 days')));
        }

        $field = 'sum(leave_num) as leave_num, land_id, roll, gate_type, time, resource_id, sid';
        $data  = $this->_model->getSummaryData($beginTime, $endTime, $field);

        if (empty($data)) {
            pft_log($this->_logPath, '无数据');
            return false;
        }

        $this->_model->startTrans();

        $delRes = $this->_model->delLeaveData($beginTime, $endTime);
        if (!$delRes) {
            pft_log($this->_logPath, '删除数据失败:' . $this->_model->_sql());
            $this->_model->rollback();
            return false;
        }

        $addRes = $this->_model->addLeaveData($data);
        if (!$addRes) {
            pft_log($this->_logPath, '写入数据失败:' . $this->_model->_sql());
            $this->_model->rollback();
            return false;
        }

        $this->_model->commit();

        pft_log($this->_logPath, 'end');
    }

    /**
     * 统计每日实时景区人数
     *
     * @deprecated 陈年旧代码了
     */
    private function summary()
    {
        pft_log('landStat', 'start');
        $beginTime       = strtotime(date('Y-m-d 00:00:00', strtotime('-1 days')));
        $endTime         = strtotime(date('Y-m-d 23:59:58', strtotime('-1 days')));
        $collectionModel = new DataCollection();
        $flag            = true;
        $page            = 1;
        $pageSize        = 500;
        while ($flag) {
            $data = $collectionModel->summaryLeaveData($beginTime, $endTime, $page, $pageSize);
            if ($data) {
                $landArr = [];
                $dataArr = [];
                foreach ($data as $val) {
                    $landArr[] = intval($val['land_id']);
                    $leaveData = [];

                    $leaveData['leave_num'] = $val['cnt'];
                    $leaveData['land_id']   = $val['land_id'];
                    $leaveData['time']      = $endTime + 1;

                    $dataArr[] = $leaveData;
                }

                $res = $collectionModel->addLeaveData($dataArr);
                if ($res) {
                    $res = $collectionModel->delLeaveData($beginTime, $endTime, $landArr);
                    if (!$res) {
                        pft_log('landStat', $collectionModel->_sql());
                    }
                } else {
                    pft_log('landStat', $collectionModel->_sql());
                }
            } else {
                $flag = false;
            }
        }
        //脚本结束
        pft_log('landStat', 'end');
    }
}