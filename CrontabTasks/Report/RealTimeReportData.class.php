<?php
namespace CrontabTasks\Report;


use Business\Statistics\RealTimeTerminalPass;
use Library\Cache\CacheRedis;
use Library\Constants\WebHookSendKeyConst;
use Library\Swoole\Process;
use Library\Util\Team\TeamUtil;
use Model\Order\OrderQuery;
use Model\Order\OrderTrack;
use Model\Order\TeamOrderSearch;
use Model\Report\PftReportRealTask;
use Model\Report\StatisticsV2;
use Business\Statistics\RealTimeAnnualCard;
use Library\Cache\Cache;
use \swoole_process;
use Library\Tools\Helpers;
use Library\Constants\DingTalkRobots;

class RealTimeReportData extends \Library\Controller
{

    const __ERROR_LOG__   = '/real_time/error';
    const __SUCCESS_LOG__ = '/real_time/success';
    const __NONE_LOG__    = '/real_time/none';
    const __EXCEPTION_LOG__ = '/real_time/exception';

    const __TEAM_ERROR_LOG__   = '/real_time/team_error';
    const __TEAM_SUCCESS_LOG__ = '/real_time/team_success';
    const __TEAM_NONE_LOG__    = '/real_time/team_none';

    const __DEBUG_LOG__ = '/real_time/debug';

    //操作信息
    private $_operateInfo = [];

    private $shutdownSignal = false;

    /**
     * 执行队列任务
     * 1. 数据库获取任务
     * 2. 成功  更新表 该任务 置为 已完成
     * 3. 失败  更新表 错误次数+1
     */
    public function index()
    {

        declare(ticks = 1);
        pcntl_signal(SIGTERM, [$this, 'shutdownSignal']);

        $failCounter = 0;
        // 由于要转到supervisor控制，这里需要使用一个死循环来控制,
        // 代码修改后需要在Supervisor下重启real_statistics进程才会生效
        while (true) {
            if ($this->shutdownSignal) {
                break;
            }

            sleep(1);
            try {

                $requestId = str_replace('.', '', microtime(true));

                // 数据库连接是否会超时？（tp框架底层数据库连接有检测）
                $taskModel  = new PftReportRealTask();
                $taskIdList = $taskModel->getTaskIdFromRedis($requestId);
                if ($taskIdList['code'] != 200 || empty($taskIdList['data'])) {
                    if (++$failCounter > 500) {
                        $failCounter = 0;
                        /** @var CacheRedis $redis */
                        $redis = Cache::getInstance('redis');
                        $isKeepalive = $redis->masterHealthy();
                        if (!$isKeepalive) {
                            if(function_exists('gethostname')) {
                                $hostname = gethostname();
                            }
                            else {
                                $hostname = php_uname('n');
                            }
                            Helpers::sendGroupRobotTextMessages("服务器【{$hostname}】，进程ID【" . getmypid() . "】", 'redis_reconnect');
                            pft_log(self::__NONE_LOG__, '未找到任务[KA=0]');
                        } else {
                            pft_log(self::__NONE_LOG__, '未找到任务[KA=1]');
                        }
                    } else {
                        pft_log(self::__NONE_LOG__, '未找到任务');
                    }
                    continue;
                }

                $taskList  = $taskModel->getTaskById($taskIdList['data']);
                $this->runWorker($taskList);

            } catch (\Exception $e) {
                $exception = $e->getMessage();
                pft_log(self::__EXCEPTION_LOG__, $exception);
            } finally {
                //异常时也要移除,重新入队列汇总
                $taskModel->removeCacheTask();
            }
        }

        pft_log(self::__NONE_LOG__, 'end job');
    }

    public function shutdownSignal()
    {
        $this->shutdownSignal = true;
    }

    /**
     * 实时任务重试入口
     * 重试次数3次
     * <AUTHOR>
     * @date 2021/5/15
     *
     */
    public function retryIndex()
    {
        //实时任务重试入口
        //最大延迟计算规则：（间隔秒数 * redis缓存里面最大数成员数）
        //redis缓存里面最大数成员数   每次加入的最大数 + 剩余数
        //重试最大延迟控制在1小时， 所以间隔240 redis最大数为15
        while (true) {
            sleep(240); //4分钟
            try {

                $requestId = str_replace('.', '', microtime(true));

                // 数据库连接是否会超时？（tp框架底层数据库连接有检测）
                $taskModel  = new PftReportRealTask();
                $taskIdList = $taskModel->getRetryTaskIdFromRedis($requestId);
                if ($taskIdList['code'] != 200 || empty($taskIdList['data'])) {
                    continue;
                }

                $taskList  = $taskModel->getTaskById($taskIdList['data']);
                $this->runRetryWorker($taskList);

            } catch (\Exception $e) {
                $exception = $e->getMessage();
                pft_log(self::__EXCEPTION_LOG__, $exception);
            } finally {
                //异常时也要移除,重新入队列汇总
                $taskModel->removeRetryCacheTask();
            }
        }
    }

    /**
     * 执行团队队列任务
     * 1. 数据库获取任务
     * 2. 成功  更新表 该任务 置为 已完成
     * 3. 失败  更新表 错误次数+1
     */
    public function runTeamRealTask()
    {
        $teamModel = new TeamOrderSearch();
        $taskList  = $teamModel->getTeamRealList();

        if (empty($taskList)) {
            pft_log(self::__TEAM_NONE_LOG__, '未找到任务');
            exit;
        }

        $this->runTeamWorker($taskList);
    }

    /**
     * 重新执行
     * 1. 数据库获取任务
     * 2. 成功  更新表 该任务 置为 已完成
     * 3. 失败  更新表 错误次数+1
     *
     * <AUTHOR>
     * @date 2021/5/15
     *
     * @param $taskList
     */
    public function runRetryWorker($taskList)
    {
        $this->runWorker($taskList);
    }

    public function runWorker($taskList)
    {
        $taskModel = new PftReportRealTask();

        //记录开始和结束时间
        $requestId = str_replace('.', '', microtime(true));
        pft_log(self::__SUCCESS_LOG__, "请求ID:{$requestId}-开始");

        foreach ($taskList as $item) {
            $result = [];
            //站点
            $siteId = $item['SalerID'];
            switch ($item['flag']) {
                case 4:
                case 16:
                    $reportBusiness = new \Business\Statistics\RealTimeStatistics();
                    $msg            = @json_decode($item['memo'], true);

                    if (empty($msg) || $msg['orderpayway'] == 2) {
                        $res          = $reportBusiness->createOrderPayWayData($item['ordernum'], $item['num'], $item['insert_time'], 1, $item['oper_member'], $item['flag']);
                        $res['title'] = '预订取消报表结合:';
                        $res['type']  = 'orderpayway';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        $result[]     = $res;
                    }

                    if (empty($msg) || $msg['order_v2'] == 2 || empty($msg['order_v2'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $siteId        = $info['site_id'];
                        $res           = $reportBusiness->createOrderTwoData($item['ordernum'], $item['num'], $item['insert_time'], 1, $operateMember, $siteId, $item['flag']);
                        $res['title']  = '新版预订报表:';
                        $res['type']   = 'order_v2';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    if (empty($msg)) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $siteId        = $info['site_id'];
                        $res           = $reportBusiness->createCustomizeData($item['ordernum'], $item['num'], $item['insert_time'], $siteId, $item['flag']);
                        $res['title']  = '特殊定制型报表:';
                        $res['type']   = 'customize_report';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    if (empty($msg) || $msg['resource_order'] == 2 || empty($msg['resource_order'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $siteId        = $info['site_id'];
                        $res           = $reportBusiness->createResourceOrderData($item['ordernum'], $item['num'], $item['insert_time'], 1, $operateMember, $siteId);
                        $res['title']  = '资源中心预定报表:';
                        $res['type']   = 'resource_order';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    if (empty($msg) || $msg['pack_order'] == 2 || empty($msg['pack_order'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createPackOrder($item['ordernum'], $item['num'], $item['insert_time'], 1, $operateMember, $siteId);
                        $res['title']  = '套票预订报表:';
                        $res['type']   = 'pack_order';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        //套票特殊处理 有的订单不是套票 不会记录套票报表 会返回失败 后面会继续统计此订单 浪费资源 所以都默认为成功
                        $res['code'] = true;
                        $result[]    = $res;
                    }

                    //分账报表
                    if (empty($msg) || $msg['separate_order'] == 2 || empty($msg['separate_order'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $separateBussiness = new \Business\Statistics\RealTimeSeparate($item['ordernum'], $item['num'], $item['insert_time'], 1, $operateMember, $siteId);
                        $tmp = $separateBussiness->realTimeSeparateOrder();
                        $tmp['title']  = '分账预订报表:';
                        $tmp['type']   = 'separate_order';
                        $tmp['order']  = $item['ordernum'];
                        $tmp['id']     = $item['id'];
                        $result[]      = $tmp;
                    }
                    TeamUtil::debug([
                        'tag' => 'real_time_report',
                        'item' => $item,
                    ]);
                    if (empty($msg) || empty($msg['team_order']) || in_array($msg['team_order'], [1, 2])) {
                        $res          = $reportBusiness->createTeamOrderPay($item['ordernum'], $item['num'], $item['insert_time'], $item['oper_member'], $item['flag']);
                        $res['title'] = '云票务团队报表:';
                        $res['type']  = 'team_order';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        //团队订单 有的订单不是团单 不会记录 会返回失败 后面会继续统计此订单 浪费资源 所以都默认为成功
                        $res['code'] = true;
                        $result[]    = $res;
                    }

                    //如果存在购票终端才进行统计
                    if (isset($item['branch_terminal']) && $item['branch_terminal']) {
                        if (empty($msg) || $msg['terminal_buy'] == 2 || empty($msg['terminal_buy'])) {
                            //统计汇总信息
                            $statisticsV2 = new StatisticsV2();
                            $tmpRes       = $statisticsV2->realtimeTerminalBuyTask($item['ordernum'], $item['num'], $item['branch_terminal'],$item['flag']);

                            $res          = [];
                            $res['title'] = '终端购票汇总';
                            $res['type']  = 'terminal_buy';
                            $res['order'] = $item['ordernum'];
                            $res['id']    = $item['id'];

                            if ($tmpRes) {
                                $res['code'] = true;
                                $res['msg']  = '终端购票汇总成功';
                            } else {
                                $res['code'] = false;
                                $res['msg']  = '终端购票汇总失败';
                            }
                            $result[] = $res;
                        }
                    }

                    // 演出预定报表
                    if (empty($msg) || $msg['series_order'] == 2 || empty($msg['series_order'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $siteId        = $info['site_id'];
                        $res           = $reportBusiness->createSeriesOrderData($item['ordernum'], $item['num'], $item['insert_time'], 1, $operateMember, $siteId, $item['flag']);
                        $res['title']  = '演出预定报表:';
                        $res['type']   = 'series_order';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    //新版预约报表
                    if (empty($msg) || $msg['order_reserve_daily'] == 2 || empty($msg['order_reserve_daily'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createOrderReserveData($item['ordernum'], $item['num'], $item['insert_time'], 1, $operateMember, $item['flag']);
                        $res['title']  = '预约日报表:';
                        $res['type']   = 'order_reserve_daily';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    //演出预约报表
                    if (empty($msg) || $msg['show_order_reserve_daily'] == 2 || empty($msg['show_order_reserve_daily'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createShowOrderReserveData($item['ordernum'], $item['num'], $item['insert_time'], 1, $operateMember, $item['flag']);
                        $res['title']  = '演出预约报表:';
                        $res['type']   = 'show_order_reserve_daily';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    break;
                case 5:
                    $reportBusiness = new \Business\Statistics\RealTimeStatistics();
                    $msg            = @json_decode($item['memo'], true);
                    //对应pft_order_track表中的source字段  32=分终端入园   53=分终端入园后的到期自动验证
                    $trackSource    = isset($msg['trackSource']) ? $msg['trackSource'] : -1;
                    $actionIdxs     = $msg['serial_number'] ?? '';

                    if ($this->_inCheckedCondition($msg)) {
                        //新版验证
                        $res = $reportBusiness->createCheckTwoData($item['ordernum'], $item['num'], $item['insert_time'], 1, $item['oper_member'], $siteId, $item['flag'], $actionIdxs, $item['id']);
                        $res['title'] = '新版验证报表:';
                        $res['type']  = 'check_v2';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        $result[]     = $res;
                    }

                    if ($this->_inCheckedCondition($msg, 'pack_checked')) {
                        //应收
                        $res = $reportBusiness->createPackCheck($item['ordernum'], $item['num'], $item['insert_time'], 1, $item['oper_member'], $siteId);

                        $res['title'] = '验证套票报表:';
                        $res['type']  = 'pack_checked';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        //套票特殊处理 有的订单不是套票 不会记录套票报表 会返回失败 后面会继续统计此订单 浪费资源 所以都默认为成功
                        $res['code'] = true;
                        $result[]    = $res;
                    }

                    //分账验证报表
                    if (empty($item['branch_terminal']) && (empty($msg) || $msg['separate_check'] == 2 || empty($msg['separate_check']))) {
                        $separateBussiness = new \Business\Statistics\RealTimeSeparate($item['ordernum'], $item['num'], $item['insert_time'], 1, $item['oper_member'], $siteId);
                        $tmp = $separateBussiness->realTimeSeparateChecked();
                        $tmp['title']  = '分账验证报表:';
                        $tmp['type']   = 'separate_check';
                        $tmp['order']  = $item['ordernum'];
                        $tmp['id']     = $item['id'];
                        $result[]      = $tmp;
                    }

                    // 演出验证报表
                    if (empty($msg) || $msg['series_checked'] == 2 || empty($msg['series_checked'])) {
                        $res           = $reportBusiness->createSeriesCheckedData($item['ordernum'], $item['num'], $item['insert_time'], 1, $item['oper_member'], $siteId);
                        $res['title']  = '演出验证报表:';
                        $res['type']   = 'series_checked';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    //分终端验证汇总
                    if (isset($item['branch_terminal']) && $item['branch_terminal']) {
                        if ($msg['branch_terminal_checked'] == 2 || empty($msg['branch_terminal_checked'])) {

                            $tmpRes       = $reportBusiness->realtimeBranchTerminalCheckedTask($item['ordernum'], $item['num'], $item['branch_terminal'], $item['oper_member'], $item['insert_time'],$trackSource);
                            $res          = [];
                            $res['title'] = '分终端检票汇总';
                            $res['type']  = 'branch_terminal_checked';
                            $res['order'] = $item['ordernum'];
                            $res['id']    = $item['id'];

                            if ($tmpRes) {
                                $res['code'] = true;
                                $res['msg']  = '分终端检票汇总成功';
                            } else {
                                $res['code'] = false;
                                $res['msg']  = '分终端检票汇总失败';
                            }
                            $result[] = $res;
                        }
                    }

                    //终端过闸汇总报表
                    if (empty($item['branch_terminal']) && (empty($msg) || $msg['terminal_pass'] == 2 || empty($msg['terminal_pass']))) {
                        $model         = new RealTimeTerminalPass($item['ordernum'], $item['num'], $item['insert_time'], $item['oper_member'], $siteId);
                        $tmpRes        = $model->realTimeTerminalPass();

                        $tmp           = [];
                        $tmp['title']  = '终端过闸汇总报表:';
                        $tmp['type']   = 'terminal_pass';
                        $tmp['order']  = $item['ordernum'];
                        $tmp['id']     = $item['id'];

                        if ($tmpRes) {
                            $tmp['code'] = true;
                            $tmp['msg']  = "终端过闸汇总成功";
                        } else {
                            $tmp['code'] = false;
                            $tmp['msg']  = "终端过闸汇总失败";
                        }

                        $result[] = $tmp;
                    }

                    //终端验证报表
                    if (empty($item['terminal_checked']) && (empty($msg) || $msg['terminal_checked'] == 2 || empty($msg['terminal_checked']))) {
                        $tmpRes        = $reportBusiness->realtimeTerminalCheckedTask($item['ordernum'], $item['num'], $item['branch_terminal'], $item['oper_member'], $item['insert_time'], 'check', $msg);

                        $tmp           = [];
                        $tmp['title']  = '终端验证汇总报表:';
                        $tmp['type']   = 'terminal_checked';
                        $tmp['order']  = $item['ordernum'];
                        $tmp['id']     = $item['id'];

                        if ($tmpRes) {
                            $tmp['code'] = true;
                            $tmp['msg']  = '终端检票汇总成功';
                        } else {
                            $tmp['code'] = false;
                            $tmp['msg']  = '终端检票汇总失败';
                        }

                        $result[] = $tmp;
                    }

                    break;
                case 1:
                case 2:
                    $reportBusiness = new \Business\Statistics\RealTimeStatistics();
                    $msg            = @json_decode($item['memo'], true);
                    $actionIdxs     = $msg['serial_number'] ?? '';

                    if (empty($msg) || $msg['cancel'] == 2) {
                        //取消报表
                        $res          = $reportBusiness->createCancelData($item['ordernum'], $item['num'], $item['insert_time'], 1, $item['oper_member'], $item['flag'], $actionIdxs);
                        $res['title'] = '取消:';
                        $res['type']  = 'cancel';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        $result[]     = $res;
                    }

                    if (empty($msg) || $msg['orderpayway'] == 2) {
                        //预定+取消报表
                        $res          = $reportBusiness->createOrderPayWayData($item['ordernum'], $item['num'], $item['insert_time'], 2, $item['oper_member'], $item['flag'], $actionIdxs);
                        $res['title'] = '预订取消报表结合:';
                        $res['type']  = 'orderpayway';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        $result[]     = $res;
                    }

                    if (empty($msg) || $msg['order_v2'] == 2 || empty($msg['order_v2'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $siteId        = $info['site_id'];
                        $res           = $reportBusiness->createOrderTwoData($item['ordernum'], $item['num'], $item['insert_time'], 2, $item['oper_member'], $siteId, $item['flag'], $actionIdxs);
                        $res['title']  = '新版预订报表:';
                        $res['type']   = 'customize_report';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    if (empty($msg)) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $siteId        = $info['site_id'];
                        $res           = $reportBusiness->createCustomizeData($item['ordernum'], $item['num'], $item['insert_time'], $siteId, $item['flag'],  $item['oper_member'] );
                        $res['title']  = '特殊定制型报表';
                        $res['type']   = 'order_v2';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    if (empty($msg) || $msg['resource_order'] == 2 || empty($msg['resource_order'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $siteId        = $info['site_id'];
                        $res           = $reportBusiness->createResourceOrderData($item['ordernum'], $item['num'], $item['insert_time'], 2, $item['oper_member'], $siteId);
                        $res['title']  = '资源中心预定报表:';
                        $res['type']   = 'resource_order';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    if (empty($msg) || $msg['pack_order'] == 2 || empty($msg['pack_order'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createPackOrder($item['ordernum'], $item['num'], $item['insert_time'], 2, $item['oper_member'], $siteId);
                        $res['title'] = '预订套票报表:';
                        $res['type']  = 'pack_order';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        //套票特殊处理 有的订单不是套票 不会记录套票报表 会返回失败 后面会继续统计此订单 浪费资源 所以都默认为成功
                        $res['code'] = true;
                        $result[]    = $res;
                    }

                    //分账预订取消报表
                    if (empty($msg) || $msg['separate_order'] == 2 || empty($msg['separate_order'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $separateBussiness = new \Business\Statistics\RealTimeSeparate($item['ordernum'], $item['num'], $item['insert_time'], 2, $operateMember, $siteId);
                        $tmp = $separateBussiness->realTimeSeparateOrder();
                        $tmp['title']  = '分账预订报表:';
                        $tmp['type']   = 'separate_order';
                        $tmp['order']  = $item['ordernum'];
                        $tmp['id']     = $item['id'];
                        $result[]      = $tmp;
                    }

                    // 演出预定报表
                    if (empty($msg) || $msg['series_order'] == 2 || empty($msg['series_order'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $siteId        = $info['site_id'];
                        $res           = $reportBusiness->createSeriesOrderData($item['ordernum'], $item['num'], $item['insert_time'], 2, $operateMember, $siteId, $item['flag']);
                        $res['title']  = '演出预定报表:';
                        $res['type']   = 'series_order';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    //新版预约报表
                    if (empty($msg) || $msg['order_reserve_daily'] == 2 || empty($msg['order_reserve_daily'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createOrderReserveData($item['ordernum'], $item['num'], $item['insert_time'], 2, $operateMember, $item['flag'], $actionIdxs);
                        $res['title']  = '新版预约报表:';
                        $res['type']   = 'order_reserve_daily';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    //演出预约报表
                    if (empty($msg) || $msg['show_order_reserve_daily'] == 2 || empty($msg['show_order_reserve_daily'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createShowOrderReserveData($item['ordernum'], $item['num'], $item['insert_time'], 2, $operateMember, $item['flag'], $actionIdxs);
                        $res['title']  = '演出预约报表:';
                        $res['type']   = 'show_order_reserve_daily';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    break;
                case 6:
                case 7:
                    $reportBusiness = new \Business\Statistics\RealTimeStatistics();
                    $msg            = @json_decode($item['memo'], true);
                    $actionIdxs     = $msg['serial_number'] ?? '';
                    //                    if (empty($msg) || $msg['revoke'] == 2) {
                    //                        $res          = $reportBusiness->createCancelData($item['ordernum'], $item['num'], $item['insert_time'], 2, $item['oper_member']);
                    //                        $res['title'] = '撤销:';
                    //                        $res['type']  = 'revoke';
                    //                        $res['order'] = $item['ordernum'];
                    //                        $res['id']    = $item['id'];
                    //                        $result[]     = $res;
                    //                    }
//                    if (empty($msg) || $msg['payway'] == 2) {
//                        $res          = $reportBusiness->createPayWayData($item['ordernum'], $item['num'], $item['insert_time'], 2);
//                        $res['title'] = '应收:';
//                        $res['type']  = 'payway';
//                        $res['order'] = $item['ordernum'];
//                        $res['id']    = $item['id'];
//                        $result[]     = $res;
//                    }
                    if (empty($msg) || $msg['orderpayway'] == 2) {
                        //预定+取消+撤销报表
                        $res          = $reportBusiness->createOrderPayWayData($item['ordernum'], $item['num'], $item['insert_time'], 2, $item['oper_member'], $item['flag'], $actionIdxs);
                        $res['title'] = '预订取消报表结合:';
                        $res['type']  = 'orderpayway';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        $result[]     = $res;
                    }
                    if (empty($msg) || $msg['order_v2'] == 2 || empty($msg['order_v2'])) {

                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        //预定的取下单的站点
                        $siteId = $info['site_id'];
                        $res    = $reportBusiness->createOrderTwoData($item['ordernum'], $item['num'], $item['insert_time'], 3, $item['oper_member'], $siteId, $item['flag'], $actionIdxs);

                        $res['title'] = '新版预订报表:';
                        $res['type']  = 'order_v2';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        $result[]     = $res;
                    }

                    if (empty($msg)) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $siteId        = $info['site_id'];
                        $res           = $reportBusiness->createCustomizeData($item['ordernum'], $item['num'], $item['insert_time'], $siteId, $item['flag'],  $item['oper_member'] );
                        $res['title']  = '新版预订报表:';
                        $res['type']   = 'order_v2';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    if (empty($msg) || $msg['resource_order'] == 2 || empty($msg['resource_order'])) {

                        $info   = $this->getOperateMember($item['ordernum']);
                        //预定的取下单的站点
                        $siteId = $info['site_id'];
                        $res    = $reportBusiness->createResourceOrderData($item['ordernum'], $item['num'], $item['insert_time'], 3, $item['oper_member'], $siteId);

                        $res['title'] = '资源中心预定报表:';
                        $res['type']  = 'resource_order';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        $result[]     = $res;
                    }
                    if (empty($msg) || $msg['pack_order'] == 2 || empty($msg['pack_order'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createPackOrder($item['ordernum'], $item['num'], $item['insert_time'], 2, $item['oper_member'], $siteId);

                        $res['title'] = '预订套票报表:';
                        $res['type']  = 'pack_order';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        //套票特殊处理 有的订单不是套票 不会记录套票报表 会返回失败 后面会继续统计此订单 浪费资源 所以都默认为成功
                        $res['code'] = true;
                        $result[]    = $res;
                    }

                    if (empty($msg) || empty($msg['check_v2']) || $msg['check_v2'] == 2) {
                        //新版验证
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createCheckTwoData($item['ordernum'], $item['num'], $item['insert_time'], 3, $item['oper_member'], $siteId, $item['flag'], $actionIdxs);

                        $res['title'] = '新版验证报表:';
                        $res['type']  = 'check_v2';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        $result[]     = $res;
                    }

                    //如果存在购票终端才进行统计
                    if (isset($item['branch_terminal']) && $item['branch_terminal']) {
                        if (empty($msg) || $msg['terminal_buy'] == 2 || empty($msg['terminal_buy'])) {
                            //统计汇总信息
                            $statisticsV2 = new StatisticsV2();
                            $tmpRes       = $statisticsV2->realtimeTerminalRevokeTask($item['ordernum'], $item['num'], $item['branch_terminal'], $item['insert_time'], $actionIdxs);

                            $res          = [];
                            $res['title'] = '终端撤销撤改汇总';
                            $res['type']  = 'terminal_buy';
                            $res['order'] = $item['ordernum'];
                            $res['id']    = $item['id'];

                            if ($tmpRes) {
                                $res['code'] = true;
                                $res['msg']  = '终端撤销撤改汇总成功';
                            } else {
                                $res['code'] = false;
                                $res['msg']  = '终端撤销撤改汇总失败';
                            }
                            $result[] = $res;
                        }
                    }

                    // 演出预定报表
                    if (empty($msg) || $msg['series_order'] == 2 || empty($msg['series_order'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $siteId        = $info['site_id'];
                        $res           = $reportBusiness->createSeriesOrderData($item['ordernum'], $item['num'], $item['insert_time'], 3, $operateMember, $siteId, $item['flag']);
                        $res['title']  = '演出预定报表:';
                        $res['type']   = 'series_order';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    // 演出验证报表
                    if (empty($msg) || $msg['series_checked'] == 2 || empty($msg['series_checked'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createSeriesCheckedData($item['ordernum'], $item['num'], $item['insert_time'], 3, $operateMember, $siteId);
                        $res['title']  = '演出验证报表:';
                        $res['type']   = 'series_checked';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    //终端验证报表
                    if (empty($item['terminal_checked']) && (empty($msg) || $msg['terminal_checked'] == 2 || empty($msg['terminal_checked']))) {
                        $tmpRes = $reportBusiness->realtimeTerminalCheckedTask($item['ordernum'], $item['num'],
                            $item['branch_terminal'], $item['oper_member'], $item['insert_time'], 'revoke', $msg);

                        $tmp          = [];
                        $tmp['title'] = '终端验证汇总报表:';
                        $tmp['type']  = 'terminal_checked';
                        $tmp['order'] = $item['ordernum'];
                        $tmp['id']    = $item['id'];

                        if ($tmpRes) {
                            $tmp['code'] = true;
                            $tmp['msg']  = '终端检票汇总成功';
                        } else {
                            $tmp['code'] = false;
                            $tmp['msg']  = '终端检票汇总失败';
                        }

                        $result[] = $tmp;
                    }

                    //新版预约报表
                    if (empty($msg) || $msg['order_reserve_daily'] == 2 || empty($msg['order_reserve_daily'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createOrderReserveData($item['ordernum'], $item['num'], $item['insert_time'], 3, $operateMember, $item['flag'], $actionIdxs);
                        $res['title']  = '新版预约报表:';
                        $res['type']   = 'order_reserve_daily';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    //演出预约报表
                    if (empty($msg) || $msg['show_order_reserve_daily'] == 2 || empty($msg['show_order_reserve_daily'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createShowOrderReserveData($item['ordernum'], $item['num'], $item['insert_time'], 3, $operateMember, $item['flag'], $actionIdxs);
                        $res['title']  = '演出预约报表:';
                        $res['type']   = 'show_order_reserve_daily';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    break;
                case 17:
                    $reportBusiness = new \Business\Statistics\RealTimeStatistics();
                    $msg            = @json_decode($item['memo'], true);

                    if (empty($msg) || empty($msg['check_v2']) || $msg['check_v2'] == 2) {
                        //新版验证报表
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createCheckTwoData($item['ordernum'], $item['num'], $item['insert_time'], 2, $operateMember, $siteId, $item['flag']);

                        $res['title'] = '新版验证报表:';
                        $res['type']  = 'check_v2';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        $result[]     = $res;
                    }

                    if (empty($msg) || empty($msg['pack_checked']) || $msg['pack_checked'] == 2) {
                        //
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createPackCheck($item['ordernum'], $item['num'], $item['insert_time'], 2, $operateMember, $siteId);

                        $res['title'] = '验证套票报表:';
                        $res['type']  = 'pack_checked';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        //套票特殊处理 有的订单不是套票 不会记录套票报表 会返回失败 后面会继续统计此订单 浪费资源 所以都默认为成功
                        $res['code'] = true;
                        $result[]    = $res;
                    }

                    if (empty($msg) || empty($msg['separate_check']) || $msg['separate_check'] == 2) {

                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        //分账报表
                        $separateBussiness = new \Business\Statistics\RealTimeSeparate($item['ordernum'], $item['num'], $item['insert_time'], 2, $operateMember, $siteId);
                        $tmp = $separateBussiness->realTimeSeparateChecked();
                        $tmp['title']  = '分账验证报表:';
                        $tmp['type']   = 'separate_check';
                        $tmp['order']  = $item['ordernum'];
                        $tmp['id']     = $item['id'];
                        $result[]      = $tmp;
                    }

                    // 演出验证报表
                    if (empty($msg) || $msg['series_checked'] == 2 || empty($msg['series_checked'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createSeriesCheckedData($item['ordernum'], $item['num'], $item['insert_time'], 2, $operateMember, $siteId);
                        $res['title']  = '演出验证报表:';
                        $res['type']   = 'series_checked';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    break;

                case 3:
                    $reportBusiness = new \Business\Statistics\RealTimeStatistics();
                    $msg            = @json_decode($item['memo'], true);
                    //云票务出票报表
                    if (empty($msg) || empty($msg['ticket']) || $msg['ticket'] == 2) {
                        $res            = $reportBusiness->createTicket($item['ordernum'], $item['num'], $item['insert_time'], $item['oper_member'], $msg);
                        $res['title']   = '云票务出票报表:';
                        $res['type']    = 'ticket';
                        $res['order']   = $item['ordernum'];
                        $res['id']      = $item['id'];
                        //套票特殊处理 有的订单不是套票 不会记录套票报表 会返回失败 后面会继续统计此订单 浪费资源 所以都默认为成功
                        $result[] = $res;
                    }
                    //取票统计
                    if (empty($msg) || $msg['order_v2'] == 2 || empty($msg['order_v2'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        //预定的取下单的站点
                        $siteId = $info['site_id'];
                        $res    = $reportBusiness->createOrderTwoData($item['ordernum'], $item['num'], $item['insert_time'], 4, $item['oper_member'], $siteId);

                        $res['title'] = '新版预订报表:';
                        $res['type']  = 'order_v2';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        $result[]     = $res;
                    }
                    //套票预订报表
                    if (empty($msg) || $msg['pack_order'] == 2 || empty($msg['pack_order'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        //预定的取下单的站点
                        $siteId        = $info['site_id'];
                        $res           = $reportBusiness->createPackOrder($item['ordernum'], $item['num'], $item['insert_time'], 4, $operateMember, $siteId);
                        $res['title']  = '套票预订报表:';
                        $res['type']   = 'pack_order';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        //套票特殊处理 有的订单不是套票 不会记录套票报表 会返回失败 后面会继续统计此订单 浪费资源 所以都默认为成功
                        $res['code'] = true;
                        $result[]    = $res;
                    }
                    break;

                case 38:
                    $reportBusiness = new \Business\Statistics\RealTimeStatistics();
                    $msg            = @json_decode($item['memo'], true);
                    //对应pft_order_track表中的source字段  32=分终端入园   53=分终端入园后的到期自动验证
                    $trackSource    = isset($msg['trackSource']) ? $msg['trackSource'] : -1;
                    $actionIdxs     = $msg['serial_number'] ?? '';
                    //新版验证 增加售后字段
                    $res = $reportBusiness->createCheckTwoData($item['ordernum'], $item['num'], $item['insert_time'], 38, $item['oper_member'], $siteId, $item['flag'], $actionIdxs, $item['id'],$msg);
                    $res['title'] = '新版验证报表:';
                    $res['type']  = 'check_v2';
                    $res['order'] = $item['ordernum'];
                    $res['id']    = $item['id'];
                    $result[]     = $res;

                    if (empty($msg) || $msg['order_v2'] == 2 || empty($msg['order_v2'])) {
                        //下单信息获取，操作人取当前操作人，站点取下单的站点
                        $info       = $this->getOperateMember($item['ordernum']);
                        $siteId     = $info['site_id'];
                        $actionIdxs = $msg['serial_number'] ?? '';

                        //预订报表记入售后数据
                        $res          = $reportBusiness->createOrderTwoData($item['ordernum'], $item['num'], $item['insert_time'], 38, $item['oper_member'], $siteId, $item['flag'], $actionIdxs, $msg);
                        $res['title'] = '新版预订报表:';
                        $res['type']  = 'order_v2';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];
                        $result[]     = $res;
                    }

                    break;
                //年卡报表相关
                case RealTimeAnnualCard::ACTION_SALE_CARD :
                case RealTimeAnnualCard::ACTION_RESUME_CARD :
                case RealTimeAnnualCard::ACTION_REAPPLY_CARD :
                case RealTimeAnnualCard::ACTION_CANCEL_CARD :
                case RealTimeAnnualCard::ACTION_ACTIVITE_CARD :
                case RealTimeAnnualCard::ACTION_CHECK_CARD:
                case RealTimeAnnualCard::ACTION_UPGRADE_CARD:
	                $msg = @json_decode($item['memo'], true);
                    if (empty($msg) || empty($msg['annual_card']) || $msg['annual_card'] == 2) {
                        $annualCardReport = new \Business\Statistics\RealTimeAnnualCard($item['ordernum'], $item['num'], $item['insert_time'], $item['flag'], $item['oper_member'], $siteId);
                        $res = $annualCardReport->create();
                        $res['title']   = '年卡报表:';
                        $res['type']    = 'annual_card';
                        $res['order']   = $item['ordernum'];
                        $res['id']      = $item['id'];
                        $result[]       = $res;
                    }
                    break;
                case 24:
                    //多景点验证汇总（入园核销分离版本）
                    $msg = @json_decode($item['memo'], true);
                    if ($msg['scenic_checked'] == 2 || empty($msg['scenic_checked'])) {
                        $recordIdArr = explode(',', $msg['id']);

                        $reportBusiness = new \Business\Statistics\RealTimeStatistics();
                        $tmpRes         = $reportBusiness->realtimeScenicCheckTask($item['ordernum'], $item['num'], $item['branch_terminal'], $item['oper_member'], $recordIdArr, $item['insert_time']);

                        $res          = [];
                        $res['title'] = '多景点入园汇总';
                        $res['type']  = 'scenic_checked';
                        $res['order'] = $item['ordernum'];
                        $res['id']    = $item['id'];

                        if ($tmpRes) {
                            $res['code'] = true;
                            $res['msg']  = '多景点入园汇总成功';
                        } else {
                            $res['code'] = false;
                            $res['msg']  = '多景点入园汇总失败';
                        }
                        $result[] = $res;
                    }
                    break;
                    
                case 32:
                    //预约
                    $reportBusiness = new \Business\Statistics\RealTimeStatistics();
                    $msg = @json_decode($item['memo'], true);
                    //新版预约报表
                    if (empty($msg) || $msg['order_reserve_daily'] == 2 || empty($msg['order_reserve_daily'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createOrderReserveData($item['ordernum'], $item['num'], $item['insert_time'], 4, $operateMember, $item['flag']);
                        $res['title']  = '新版预约报表:';
                        $res['type']   = 'order_reserve_daily';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    //演出预约报表
                    if (empty($msg) || $msg['show_order_reserve_daily'] == 2 || empty($msg['show_order_reserve_daily'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createShowOrderReserveData($item['ordernum'], $item['num'], $item['insert_time'], 4, $operateMember, $item['flag']);
                        $res['title']  = '演出预约报表:';
                        $res['type']   = 'show_order_reserve_daily';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    break;

                case 33:
                    //改签  追踪记录的和实时报表中间表的存在差异 追踪记录是改签是22
                    $reportBusiness = new \Business\Statistics\RealTimeStatistics();
                    $msg = @json_decode($item['memo'], true);
                    //新版预约报表
                    if (empty($msg) || $msg['order_reserve_daily'] == 2 || empty($msg['order_reserve_daily'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createOrderReserveData($item['ordernum'], $item['num'], $item['insert_time'], 5, $operateMember, 22);
                        $res['title']  = '新版预约报表:';
                        $res['type']   = 'order_reserve_daily';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    //演出预约报表
                    if (empty($msg) || $msg['show_order_reserve_daily'] == 2 || empty($msg['show_order_reserve_daily'])) {
                        $info          = $this->getOperateMember($item['ordernum']);
                        $operateMember = $info['operate_id'];
                        $res           = $reportBusiness->createShowOrderReserveData($item['ordernum'], $item['num'], $item['insert_time'], 5, $operateMember, 22, $item['flag']);
                        $res['title']  = '演出预约报表:';
                        $res['type']   = 'show_order_reserve_daily';
                        $res['order']  = $item['ordernum'];
                        $res['id']     = $item['id'];
                        $result[]      = $res;
                    }

                    break;

                case 34:
                    $reportBusiness = new \Business\Statistics\RealTimeStatistics();
                    $msg = @json_decode($item['memo'], true);
                    $actionIdxs = $msg['serial_number'] ?? '';
                    $res = $reportBusiness->createCheckTwoData($item['ordernum'], $item['num'], $item['insert_time'], 1, $item['oper_member'], $siteId, $item['flag'], $actionIdxs, $item['id']);
                    $res['title'] = '新版验证报表:';
                    $res['type']  = 'check_v2';
                    $res['order'] = $item['ordernum'];
                    $res['id']    = $item['id'];
                    $result[]     = $res;
                    break;
                default:
                    $result = [];
            }

            //更新之前查询一次memo信息
            $tmpTaskInfo = $taskModel->getTaskById([$item['id']]);
            $item['memo'] = $tmpTaskInfo[0]['memo'];

            $memo  = empty(@json_decode($item['memo'], true)) ? [] : json_decode($item['memo'], true);
            $done  = 1;
            $times = 0;
            foreach ($result as $value) {
                $taskName = $value['title'];
                $msg      = $taskName . $value['order'] . ":" . $value['msg'];
                if (!$value['code']) {
                    $times   = $item['times'] + 1;
                    $tmpMemo = [$value['type'] => 2];
                    $memo = is_array($memo) ? array_merge($memo, $tmpMemo) : $tmpMemo;
                    $done    = 0;
                    pft_log(self::__ERROR_LOG__, $msg);
                } else {
                    $tmpMemo = [$value['type'] => 1];
                    $memo = is_array($memo) ? array_merge($memo, $tmpMemo) : $tmpMemo;
                    pft_log(self::__SUCCESS_LOG__, $msg);
                }
            }
            $memo = json_encode($memo);
            $taskModel->updateTask($item['id'], $done, $times, $memo);
        }

        //消费完成，数量上报
        if (!empty($taskList)) {
            (new \Business\Statistics\ReportPushMonitorIndex())->pushReportRealTaskCount(count($taskList));
        }

        pft_log(self::__SUCCESS_LOG__, "请求ID:{$requestId}-结束");
    }

    /**
     * 获取下单的操作员工
     * <AUTHOR>
     * @date   2017-11-13
     */
    private function getOperateMember($orderNum)
    {
        if (empty($orderNum)) {
            return 0;
        }

        if (isset($this->_operateInfo[$orderNum])) {
            //pft_log('cache_test/operate', json_encode(['from_params:', $this->_operateInfo[$orderNum]]));
            return $this->_operateInfo[$orderNum];
        }

        //查询缓存
        $redis       = Cache::getInstance('redis');
        $cacheKey    = 'report:operateor:' . $orderNum ;
        $operateInfo = $redis->get($cacheKey);

        if ($operateInfo !== false) {
            $operateInfo = json_decode($operateInfo, true);
            //pft_log('cache_test/operate', json_encode(['from_cache:', $operateInfo]));
            $this->_operateInfo[$orderNum] = $operateInfo;
            return $operateInfo;
        }

        //查询数据库
        $model     = new OrderTrack();
        $res       = $model->getOperateMember($orderNum);
        $operateId = isset($res['oper_member']) ? $res['oper_member'] : 0;
        $siteId    = isset($res['SalerID']) ? $res['SalerID'] : 0;

        $operateInfo = ['operate_id' => $operateId, 'site_id' => $siteId];
        //写入缓存
        $redis->set($cacheKey, json_encode($operateInfo), '', 300);
        //写入变量
        $this->_operateInfo[$orderNum] = $operateInfo;
        //pft_log('cache_test/operate', json_encode(['from_db:', $operateInfo]));
        return $operateInfo;
    }

    /**
     * 团队订单的实时报表
     *
     * <AUTHOR>
     * @date   2018-06-25
     */
    public function runTeamWorker($taskList)
    {
        $reportBusiness = new \Business\Statistics\RealTimeStatistics();
        $teamModel      = new TeamOrderSearch();

        foreach ($taskList as $item) {
            $res = $reportBusiness->createTeamCheck($item);
            if ($res['code'] != 200) {
                //失败
                $errMsg = $res['msg'];
                $teamModel->updateTeamRealStatus($item['id'], 3);
                pft_log(self::__TEAM_ERROR_LOG__, $item['id'] . ':' . $item['order_id'] . ':' . $errMsg);
            } else {
                //成功
                $teamModel->updateTeamRealStatus($item['id'], 2);
                pft_log(self::__TEAM_SUCCESS_LOG__, $item['id'] . ':' . $item['order_id']);
            }
        }
    }


    /**
     * 实时报表延迟性监控
     * 命令：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/RealTimeReportData pushRealTaskStatus
     * <AUTHOR>
     * @date    2019-09-09
     *
     */
    public function pushRealTaskStatus()
    {
        //需要警戒的数
        $warningNum      = 3000; //待执行数
        $doingWarningNum = 3000; //执行中的
        $errorDoingNum   = 100; //一直在执行中

        //是否开启假日模式，开启的话需要提升下预警上限
        $vacationMode = (int)(new \Business\PftSystem\VacationModeBiz())->isOpenVacation();
        if ($vacationMode) {
            $warningNum      = 10000;
            $doingWarningNum = 10000;
            $errorDoingNum   = 400;
        }

        $startTime = strtotime(date('Ymd'));
        $endTime   = strtotime(date("Ymd", strtotime("+1 day")));

        $currentTime = time();

        $realReportModel = new PftReportRealTask();

        //优化下这个查询会有慢语句
        $maxTime = $realReportModel->getMaxTime($currentTime - 60, strtotime(date('Y-m-d 23:59:59', $currentTime)));
        //如果当前时间没有数据，则取一整天的数据最大时间，避免凌晨实时异常，无法通知
        if (empty($maxTime)) {
            $maxTime = $realReportModel->getMaxTime($startTime, $endTime);
        }

        $template = "报表实时数据延迟 %u 分钟, 当前处理中任务数: %u, 待处理的任务数：%u, 异常处理中任务数: %u";

        if ($maxTime) {
            //实时报表延迟时长(分钟)
            $delayTime = ceil((time() - $maxTime) / 60);
            //处理中的任务数
            $doingTaskNum = $realReportModel->getTaskStatus($startTime, $endTime, $realReportModel::TASK_STATUS_DOING);
            //待处理的任务数
            $unDoTaskNum = $realReportModel->getTaskStatus($startTime, $endTime, $realReportModel::TASK_STATUS_UNDO);
            //异常数据还在处理中 0.5个小时了还在处理中的数据
            $errorDoingTaskNum = $realReportModel->getTaskStatus($startTime, (time() - 1800),
                $realReportModel::TASK_STATUS_DOING);

            // //钉钉告警
            // if (ENV == 'PRODUCTION') {
                $message = sprintf($template, $delayTime, $doingTaskNum, $unDoTaskNum, $errorDoingTaskNum);
                //处理中和待处理的都要比较下，超出上限钉钉预警
                if ($unDoTaskNum >= $warningNum || $doingTaskNum >= $doingWarningNum || $errorDoingTaskNum >= $errorDoingNum) {
                    //小勇
                    // $atMobile = [13255919916];
                    // $atAll    = true;
                    $message .= "，\n具体预警：";
                    $message .= "\n处理中：[$doingTaskNum / $doingWarningNum] " . ($doingTaskNum >= $doingWarningNum ? '- 异常' : '- 正常');
                    $message .= "，\n未处理：[$unDoTaskNum / $warningNum] " . ($unDoTaskNum >= $warningNum ? '- 异常' : '- 正常');
                    $message .= "，\n异常处理中：[$errorDoingTaskNum / $errorDoingNum] " . ($errorDoingTaskNum >= $errorDoingNum ? '- 异常' : '- 正常');
                    $message .= "，\n-----------------------\n请确认报表实时任务是否异常";

                    // Helpers::sendDingTalkGroupRobotMessageRaw($message, DingTalkRobots::REAL_REPORT_ROBOI, $atAll, $atMobile);

                    //统一通知机器人
                    $res = Helpers::sendGroupRobotTextMessages($message, WebHookSendKeyConst::REPORT_REAL_EARLY_WARNING);

                    //结果直接输出
                    echo $message . "\n";
                    echo json_encode([$res], JSON_UNESCAPED_UNICODE) . "\n";
                }
            // }
        }
    }

    /**
     * 是否计入验证报表条件
     * @date    2021-05-13
     * @param   int $chunkSize 分组大小
     * @return  boolean
     */
    private function _inCheckedCondition($msg, $type = 'check_v2')
    {
        //对应pft_order_track表中的source字段  32=分终端入园   53=分终端入园后的到期自动验证
        $trackSource    = isset($msg['trackSource']) ? $msg['trackSource'] : -1;
        //门票是否在某个分终端上首次入园 1=是 2=否
        $isBranchFirst  = isset($msg['is_branch_first']) ? $msg['is_branch_first'] : 0;
        //实际操作渠道
        $realSource   = $msg['real_source'] ?? -1;

        //是否处理过该条信息
        $msgDone = false;
        
        if ($type == 'check_v2') {
            if (empty($msg) || empty($msg['check_v2']) || $msg['check_v2'] == 2) {
                $msgDone = true;
            }
        }

        if ($type == 'pack_checked') {
            if (empty($msg) || empty($msg['pack_checked']) || $msg['pack_checked'] == 2) {
                $msgDone = true;
            }
        }

        //预售券订单加个判断下，flag=5  $realSource !=85才计入报表
        if ($realSource == 85 && $msgDone) {
            return false;
        }

        //分终端业务下首次入园记录报表
        if ($trackSource == 32 && $isBranchFirst == 1 && $msgDone) {
            return true;
        }

        //非分终端
        if ($trackSource == -1 && $msgDone) {
            return true;
        }

        return false;
    }

    /**
     * 批量更新done字段值
     * <AUTHOR>
     * @date   2024/04/16
     *
     * 命令：php /var/www/html/Service/Crontab/runNew.php Report/RealTimeReportData batchUpdateDoneField
     *
     */
    public function batchUpdateDoneField()
    {
        $params = $GLOBALS['argv'];

        $limitSize = $params[3] ?? 1000;
        $flag      = $params[4] ?? 0;
        $date      = date('Y-m-d 23:59:59', strtotime("-1 days"));

        //记录开始和结束时间
        $startTime = str_replace('.', '', microtime(true));
        pft_log(self::__DEBUG_LOG__, "已过期未执行的批量更新:{$startTime}-开始");

        try {
            $realReportModel = new PftReportRealTask();

            //过期时间
            $expireTime = strtotime($date);

            $total = $realReportModel->getPendingTaskCount($expireTime, $flag);

            if (!$total) {
                return true;
            }

            $totalPage = ceil($total / $limitSize);

            for ($i = 1; $i <= $totalPage; $i++) {
                $res = $realReportModel->updatePendingTaskDone($expireTime, $limitSize, $flag);
                if ($res === false) {
                    pft_log(self::__DEBUG_LOG__, "已过期未执行的批量更新异常，params:". json_encode([$expireTime, $limitSize, $flag]));

                    echo "已过期未执行的批量更新异常，params:". json_encode([$expireTime, $limitSize, $flag]) . "\n";

                    throw new \Exception("已过期未执行的批量更新异常");
                }

                pft_log(self::__DEBUG_LOG__, "已过期未执行的批量更新成功，params:". json_encode([$expireTime, $limitSize, $flag, $i, $realReportModel->getLastSql()]));
            }
        } catch (\Exception $e) {
            $log = json_encode([
                'msg'  => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ], JSON_UNESCAPED_UNICODE);
            pft_log(self::__DEBUG_LOG__, "已过期未执行的批量更新:{$log}-异常");
        }


        echo "已过期未执行的批量更新完成，更新总数:". $total. "\n";

        $endTime = str_replace('.', '', microtime(true));
        pft_log(self::__DEBUG_LOG__, "已过期未执行的批量更新:{$endTime}-结束");

        return true;
    }
}
