<?php

namespace CrontabTasks\Report;

use Library\Controller;
use Model\Report\Statistics as StatisticsModel;
use Business\CommodityCenter\Land as LandApi;
use Model\CommonConfig\Config as ConfigModel;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class ReportScript extends Controller
{
    private $_stStatisticsModel;

    /**
     * 要处理的表名
     * @var string[] 报表表名列表
     */
    protected $tableList = [
        "pft_report_order_two"                => [
            'date_format' => 'Ymd',
            'date_name'   => 'date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_order_two_hour"           => [
            'date_format' => 'Ymd',
            'date_name'   => 'date_hour',
            'begin'       => '00',
            'end'         => '23',
        ],
        "pft_report_order_five_minute"        => [
            'date_format' => 'Ymd',
            'date_name'   => 'date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_customize"                => [
            'date_format' => 'Ymd',
            'date_name'   => 'cal_date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_checked_two"              => [
            'date_format' => 'Ymd',
            'date_name'   => 'date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_checked_two_hour"         => [
            'date_format' => 'Ymd',
            'date_name'   => 'date_hour',
            'begin'       => '00',
            'end'         => '23',
        ],
        "pft_report_checked_five_minute"      => [
            'date_format' => 'Ymd',
            'date_name'   => 'date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_order_reserve_daily"      => [
            'date_format' => 'Ymd',
            'date_name'   => 'order_date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_show_order_reserve_daily" => [
            'date_format' => 'Ymd',
            'date_name'   => 'order_date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_series_order"             => [
            'date_format' => 'Ymd',
            'date_name'   => 'date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_series_checked"           => [
            'date_format' => 'Ymd',
            'date_name'   => 'date',
            'begin'       => '',
            'end'         => '',
        ],
    ];

    /**
     * 要处理的表名
     * @var string[] 报表表名列表
     */
    protected $tableMonthList = [
        "pft_report_order_two_month"          => [
            'date_format' => 'Ym',
            'date_name'   => 'date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_customize_month"          => [
            'date_format' => 'Ym',
            'date_name'   => 'cal_date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_checked_two_month"        => [
            'date_format' => 'Ym',
            'date_name'   => 'date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_order_reserve_month"      => [
            'date_format' => 'Ym',
            'date_name'   => 'order_date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_show_order_reserve_month" => [
            'date_format' => 'Ym',
            'date_name'   => 'order_date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_series_order_month"       => [
            'date_format' => 'Ym',
            'date_name'   => 'date',
            'begin'       => '',
            'end'         => '',
        ],
        "pft_report_series_checked_month"     => [
            'date_format' => 'Ym',
            'date_name'   => 'date',
            'begin'       => '',
            'end'         => '',
        ],
    ];

    private $_defaultType = 'F';

    private $_checkType = false;

    /**
     * @command sudo /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportScript runAddMainTypeDay
     * <AUTHOR>
     * @date    2024/04/28
     *
     */
    public function runAddMainTypeDay()
    {
        $params = $GLOBALS['argv'];

        if (!empty($params[3])) {
            $this->_checkType = true;
        }

        $startDateStr = '2023-01-01';
        if (!empty($params[4])) {
            $startDateStr = $params[4];
        }

        $endDateStr = date('Y-m-d', strtotime('-1 day'));
        if (!empty($params[5])) {
            $endDateStr = $params[5];
        }

        $runTable = '';
        if (!empty($params[6])) {
            $runTable = $params[6];
        }

        echo json_encode($params, JSON_UNESCAPED_UNICODE) . "\n";

        // 创建 DateTime 对象
        $startDate = new \DateTime($startDateStr);
        $endDate   = new \DateTime($endDateStr);

        $tableList = array_keys($this->tableList);

        while ($tableList) {
            $tableName = array_shift($tableList);

            if (!empty($runTable) && $tableName != $runTable) {
                continue;
            }

            // 当前处理的日期
            $currentDate = clone $startDate;

            while ($currentDate <= $endDate) {
                echo "处理表：{$tableName}，日期：{$currentDate->format('Y-m-d')}\n";

                //按天分页处理
                $this->_handleEveryDay($tableName, $currentDate->format('Y-m-d'));

                // 将日期增加一天，准备处理下一天的表名
                $currentDate->modify('+1 day');
            }
        }
    }

    /**
     * @command sudo /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportScript runAddMainTypeMonth
     * <AUTHOR>
     * @date    2024/04/28
     *
     */
    public function runAddMainTypeMonth()
    {
        $params = $GLOBALS['argv'];

        if (!empty($params[3])) {
            $this->_checkType = true;
        }

        $startDateStr = '2023-01-01';
        if (!empty($params[4])) {
            $startDateStr = $params[4];
        }

        $endDateStr = date('Y-m-t', strtotime('-1 month')); // 默认为当前月份的最后一天
        if (!empty($params[5])) {
            $endDateStr = $params[5];
        }

        $runTable = '';
        if (!empty($params[6])) {
            $runTable = $params[6];
        }

        echo json_encode($params, JSON_UNESCAPED_UNICODE) . "\n";

        $startDate = new \DateTime($startDateStr);
        $endDate   = new \DateTime($endDateStr);

        $tableMonthList = array_keys($this->tableMonthList);
        while ($tableMonthList) {
            $tableName = array_shift($tableMonthList);

            if (!empty($runTable) && $tableName != $runTable) {
                continue;
            }

            // 当前处理的日期
            $currentDate = clone $startDate;

            while ($currentDate <= $endDate) {
                $currentMonth = $currentDate->format('Ym');
                echo "处理表：{$tableName}，月份：{$currentMonth}\n";

                //按天分页处理
                $this->_handleEveryMonth($tableName, $currentMonth);

                // 将日期增加一天，准备处理下一天的表名
                $currentDate->modify('+1 month');
            }
        }
    }

    /**
     * 同步旧的配置数据到新的配置表
     * <AUTHOR>
     * @date    2024/04/28
     *
     * @command sudo /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportScript synConfigData
     *
     */
    public function synConfigData()
    {
        $params = $GLOBALS['argv'];
        $tag    = $params[3] ?? 'platform_son_ticket_show';

        $model = new  ConfigModel();
        $total = $model->table(ConfigModel::PFT_COMMON_CONFIG)->where(['tag_key' => $tag])->count();
        if (!intval($total)) {
            echo "没有配置数据\n";
            exit();
        }

        $pageSize  = 50;
        $totalPage = ceil((int)$total / $pageSize);

        $configTag = 'show_self_son_ticket';

        $tagKeyArr = array_column(load_config('statistics_scope', 'report_config'), 'tag_key');

        $updateData = [];
        for ($i = 1; $i <= $totalPage; $i++) {
            $offset = ($i - 1) * $pageSize;
            $list   = $model->table(ConfigModel::PFT_COMMON_CONFIG)->where(['tag_key' => $tag])->limit($offset,
                $pageSize)->select();
            $list   = is_array($list) ? $list : [];

            foreach ($list as $item) {
                if (empty($item['tag_val'])) {
                    continue;
                }

                $tagVal = json_decode($item['tag_val'], true);

                if (!isset($tagVal[$configTag])) {
                    continue;
                }

                $configTagVal = $tagVal[$configTag];

                $configData = [];
                foreach ($tagKeyArr as $tagKey) {
                    $isSelect = 0;
                    if ($tagKey == $configTag) {
                        $isSelect = (int)$configTagVal;
                    }

                    $configData[] = [
                        'tag_key'   => $tagKey,
                        'is_select' => $isSelect,
                    ];
                }

                $updateData[] = [
                    'sid'            => $item['sid'],
                    'member_id'      => $item['member_id'],
                    'config_content' => json_encode($configData),
                    'create_time'    => $item['create_time'],
                    'update_time'    => $item['update_time'],
                ];

                if (count($updateData) >= 200) {
                    $this->insertConfigData($updateData);

                    $updateData = [];
                }
            }
        }
        if (!empty($updateData)) {
            $this->insertConfigData($updateData);
        }
    }

    private function _handleEveryDay($tableName, $date)
    {
        if (empty($date) || !strtotime($date) || empty($tableName)) {
            return false;
        }

        $config = $this->tableList[$tableName] ?? [];

        if (empty($config)) {
            return false;
        }

        $dateFormat = $config['date_format'] ?? '';
        $dateName   = $config['date_name'] ?? '';
        $begin      = $config['begin'] ?? '';
        $end        = $config['end'] ?? '';

        if (empty($dateFormat) || empty($dateName)) {
            return false;
        }

        $begin = date($dateFormat, strtotime($date)) . $begin;
        $end   = date($dateFormat, strtotime($date)) . $end;

        $where = [
            $dateName  => ['between', [$begin, $end]],
            'main_tid' => ['neq', 0],
        ];

        $this->_handelData($tableName, $date, $where);

        echo "处理表：{$tableName}，日期：{$date}，已完成\n";

    }

    private function _handleEveryMonth($tableName, $month)
    {
        $config = $this->tableMonthList[$tableName] ?? [];

        if (empty($config)) {
            return false;
        }

        $dateFormat = $config['date_format'] ?? '';
        $dateName   = $config['date_name'] ?? '';
        $begin      = $config['begin'] ?? '';
        $end        = $config['end'] ?? '';

        if (empty($dateFormat) || empty($dateName)) {
            return false;
        }

        $begin = $month . $begin;
        $end   = $month . $end;

        $where = [
            $dateName  => ['between', [$begin, $end]],
            'main_tid' => ['neq', 0],
        ];

        $this->_handelData($tableName, $month, $where);

        echo "处理表：{$tableName}，日期：{$month}，已完成\n";
    }

    private function _handelData($tableName, $date, $where)
    {
        $id = 0;

        $limit = 200;

        $limitSave = 500;

        $checkType = $this->_checkType;

        $landApi = new LandApi();

        $data = [];

        while (true) {
            if ($id) {
                $where['id'] = ['gt', $id];
            }

            $dataList = $this->_getStatisticsModel()->table($tableName)->where($where)->order('id asc')->limit($limit)->select();
            if (empty($dataList) || !isset($dataList[0]['main_type'])) {
                break;
            }

            $endItem = end($dataList);

            $id = $endItem['id'] ?? 0;
            if (!$id) {
                break;
            }

            echo $id . "\n";

            $tidArr = array_values(array_unique(array_column($dataList, 'main_tid')));

            $idMap = array_column($dataList, 'main_tid', 'id');

            $landMap = [];
            if ($checkType) {
                $landInfoArr = $landApi->getLandInfoByArrTidToJava(array_map('intval', $tidArr));
                //验证是否有套票类型
                if ($landInfoArr) {
                    $landMap = array_column($landInfoArr, 'p_type', 'id');
                }
            }

            if ($checkType && empty($landMap)) {
                break;
            }

            foreach ($idMap as $idKey => $tid) {
                $type          = $landMap[$tid] ?? $this->_defaultType;
                $data[$type][] = $idKey;

                foreach ($data as $typeKey => $itemList) {
                    $itemList = array_values(array_unique($itemList));
                    if (count($itemList) >= $limitSave) {
                        //更新类型
                        $res = $this->_getStatisticsModel()->table($tableName)->where([
                            'id' => [
                                'in',
                                $itemList,
                            ],
                        ])->save(['main_type' => $typeKey]);
                        //更新失败
                        if (!$res) {
                            pft_log('report_update/error',
                                json_encode([$tableName, $typeKey, $itemList], JSON_UNESCAPED_UNICODE));
                            continue;
                        }

                        $data[$typeKey] = [];

                        pft_log('report_update/main_type',
                            json_encode([$tableName, $typeKey, $itemList], JSON_UNESCAPED_UNICODE));
                        //echo "处理表：{$tableName}，日期：{$date}，完成：" . json_encode(array_keys($data)) . count($itemList) . "\n";
                    }
                }
            }
        }

        foreach ($data as $typeKey => $itemList) {
            $itemList = array_values(array_unique($itemList));
            if (!empty($itemList)) {
                //更新类型
                $res = $this->_getStatisticsModel()->table($tableName)->where([
                    'id' => [
                        'in',
                        $itemList,
                    ],
                ])->save(['main_type' => $typeKey]);
                //更新失败
                if (!$res) {
                    pft_log('report_update/error',
                        json_encode([$tableName, $typeKey, $itemList], JSON_UNESCAPED_UNICODE));
                    continue;
                }

                $data[$typeKey] = [];

                pft_log('report_update/main_type',
                    json_encode([$tableName, $typeKey, $itemList], JSON_UNESCAPED_UNICODE));

                echo "处理表：{$tableName}，日期：{$date}，完成：" . json_encode(array_keys($data)) . count($itemList) . "\n";
            }
        }

        return true;
    }

    /**
     * @command sudo /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportScript runAddMainTypeId
     * <AUTHOR>
     * @date    2024/04/28
     *
     */
    public function runAddMainTypeId()
    {
        $params = $GLOBALS['argv'];

        $checkType = false;
        if (!empty($params[3])) {
            $checkType = true;
        }

        $startId = 0;
        if (!empty($params[4])) {
            $startId = intval($params[4]);
        }

        $endId = 0;
        if (!empty($params[5])) {
            $endId = intval($params[5]);
        }

        if (!$startId || !$endId) {
            echo "id参数错误 \n";
            exit();
        }

        $tableName = '';
        if (!empty($params[6])) {
            $tableName = $params[6];
        }

        $limitSave = 50;
        if (!empty($params[7])) {
            $limitSave = intval($params[7]);
        }

        $id = $startId;

        if ($checkType) {
            $landApi = new LandApi();
        }

        $data = [];
        $num = 0;
        while (true) {
            if ($id >= $endId) {
                echo "id达到上限，停止！ \n";
                break;
            }
            $where = [
                'id' => ['gt', $id],
            ];

            $info = $this->_getStatisticsModel()->table($tableName)->where($where)->field('id,main_tid,main_type')->find();
            if (empty($info) || !isset($info['main_type'])) {
                break;
            }


            $id = $info['id'] ?? 0;
            if (empty($id)) {
                break;
            }

            if (empty($info['main_tid']) || !empty($info['main_type'])) {
                continue;
            }

            $data[] = $info;

            $num++;

            if (count($data) >= $limitSave) {
                $this->_handleEveryLine($data, $tableName, $landApi ?? '', $checkType);

                $data = [];

                //停顿300毫秒
                usleep(300 * 1000);
            }
        }

        if (!empty($data)) {
            $this->_handleEveryLine($data, $tableName, $landApi ?? '', $checkType);
        }

        echo "处理表：{$tableName}，完成，总数：{$num} \n";

        return true;
    }

    private function _handleEveryLine($data, $tableName, $landApi, $checkType = false)
    {
        $idMap   = array_column($data, 'main_tid', 'id');
        $landMap = [];
        if ($checkType) {
            $tidArr      = array_values(array_unique(array_column($data, 'main_tid')));
            $landInfoArr = $landApi->getLandInfoByArrTidToJava(array_map('intval', $tidArr));
            //验证是否有套票类型
            if ($landInfoArr) {
                $landMap = array_column($landInfoArr, 'p_type', 'id');
            }
        }

        if ($checkType && empty($landMap)) {
            echo "main_tid详情获取失败 \n";
            pft_log('report_update/error',
                json_encode([$tableName, 'main_tid详情获取失败', $idMap], JSON_UNESCAPED_UNICODE));

            return true;
        }

        $updateData = [];
        foreach ($idMap as $idKey => $tid) {
            $type                = $landMap[$tid] ?? $this->_defaultType;
            $updateData[$type][] = $idKey;
        }

        foreach ($updateData as $typeKey => $itemList) {
            $itemList = array_values(array_unique($itemList));
            //更新类型
            $res = $this->_getStatisticsModel()->table($tableName)->where([
                'id' => [
                    'in',
                    $itemList,
                ],
            ])->save(['main_type' => $typeKey]);
            //更新失败
            if (!$res) {
                echo "处理表：{$tableName}，更新失败， 类型：" . $typeKey . "， 总数：" . count($itemList) . "\n";

                pft_log('report_update/error', json_encode([$tableName, $typeKey, $itemList], JSON_UNESCAPED_UNICODE));
                continue;
            }

            pft_log('report_update/main_type', json_encode([$tableName, $typeKey, $itemList], JSON_UNESCAPED_UNICODE));

            echo "处理表：{$tableName}，完成， 类型：" . $typeKey . "， 总数：" . count($itemList) . "\n";
        }

        return true;
    }

    /**
     * 获取数据模型
     * <AUTHOR>
     * @date   2024/04/02
     *
     * @return StatisticsModel
     */
    private function _getStatisticsModel()
    {
        if (empty($this->_stStatisticsModel)) {
            $this->_stStatisticsModel = new StatisticsModel();
        }

        return $this->_stStatisticsModel;
    }

    /**
     * 同步旧配置到新表
     * <AUTHOR>
     * @date   2024/04/28
     *
     * @param $updateData
     *
     * @return true|void
     */
    private function insertConfigData($updateData)
    {
        if (empty($updateData)) {
            return true;
        }

        $sidArr      = array_column($updateData, 'sid');
        $staticModel = new StatisticsModel();

        $checkRes = $staticModel->table('pft_report_statistics_scope_config')->where([
            'sid' => [
                'in',
                $sidArr,
            ],
        ])->select();

        $checkList = is_array($checkRes) ? $checkRes : [];

        $unAllowSidArr = array_column($checkList, 'sid');

        foreach ($updateData as $key => &$value) {
            if (in_array($value['sid'], $unAllowSidArr)) {
                unset($updateData[$key]);
            }
        }

        unset($value);

        if (!empty($unAllowSidArr)) {
            echo "已存在数据，无需同步，用户：" . json_encode($unAllowSidArr) . "\n";
        }

        if (count($sidArr) == count($unAllowSidArr)) {
            echo "没有需要更新的数据\n";

            return true;
        }

        $res = $staticModel->table('pft_report_statistics_scope_config')->addAll(array_values($updateData));
        if (!$res) {
            echo "更新失败\n";
            exit();
        }

        echo "处理了：" . count($updateData) . "\n";

        return true;
    }
}