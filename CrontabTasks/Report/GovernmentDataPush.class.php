<?php
/**
 * 政府数据平台对接任务
 * <AUTHOR>
 * @date 2019/12/2 0002
 */

namespace CrontabTasks\Report;

use Business\JavaApi\TicketApi;
use Business\Order\OrderTrackQuery;
use Library\Controller;
use Model\DataCollection\DataCollection;
use Model\Order\OrderTrack;
use Model\Ota\GovernmentDataSys;
use Model\Product\Land;
use Library\MulityProcessHelper;
use Rpc\Order\OrderTools;
use Model\Report\GovernmentTouristStatistics;
use Business\JavaApi\Product\Ticket;
use Business\Product\TimeShare as TimeShareBiz;
use Library\Cache\Cache;
use Business\JavaApi\Product\TimeShareOrdeStore;
use Business\JavaApi\Order\Query\UusOrder;
use Business\JavaApi\Order\Query\OrderDetailQuery;
use Library\PftSoapClient;
use Business\JavaApi\Product\LandStorage;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class GovernmentDataPush extends Controller
{
    private $pushUrl     = 'https://wljg.dct.jiangxi.gov.cn/upload-data/tourist/'; //江西省数据平台接口地址
    private $cqUrl       = 'https://api-rftcloud.1000fun.com';                     //重庆数据平台接口地址      测试 'https://test.api.taichi.iotourism.com';
    private $ShangRaoUrl = 'https://openapi.all4tour.cn/zjxtdj/';                  //上饶市预约平台数据推送地址 测试（http://test.openapi.all4tour.cn/zjxtdj/）
    private $ShangHaiUrl = 'http://jq.shanghai12301.com/LYJWebService/PassengerInfo/PassengerInfoWebService.asmx'; // 上海文旅平台数据推送地址

    /**
     * 多进程任务入口
     * <AUTHOR>
     * @date 2019/12/2 0002
     *
     * @param $data
     * @param $param
     *
     * @return array
     */
    public function runWorker($data, $param)
    {
        switch ($param['type']) {
            case 'jxMinute':
                $this->runJxMinuteWorker($data);
                break;
            case 'jxDay':
                $this->runJxDayWorker($data);
                break;
            default:
                break;
        }

        return true;
    }


    /***************************************************四川省数据平台****************************************************/

    /**
     * 四川省数据平台实时推送(10分钟)
     * <AUTHOR>
     * @date 2020/6/5
     *
     * @return boolean
     */
    public function pushDataByScMinute()
    {
        $type                   = GovernmentDataSys::GOVERNMENT_SYS_TYPE_SC;//四川省数据平台类型
        $governmentDataSysModel = new GovernmentDataSys();
        $governmentDataPushBus  = new \Business\Government\GovernmentDataPush();

        $field = 'id,apply_id,land_code,land_key';
        $data  = $governmentDataSysModel->getGdListByType($type, $field);

        $gdsIds = array_column($data, 'id');
        if (empty($gdsIds)) {
            return false;
        }

        $attrRes = $governmentDataSysModel->getAttrByGdsIds($gdsIds);
        if (empty($attrRes)) {
            return false;
        }

        //获取属性数据
        $attrData = [];
        foreach ($attrRes as $item) {
            $attrData[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        foreach ($data as $value) {
            $landKey = explode('|', $value['land_key']);
            if (empty($landKey[0]) || empty($landKey[1])) {
                continue;
            }

            $attrDataMap = $attrData[$value['id']] ?? [];
            if (empty($attrDataMap['land_id']) || empty($value['land_code'])) {
                continue;
            }

            //朱德故里因为套票对接美团子票无法开启分时预约特殊处理
            if ($value['apply_id'] == 5421411) {
                $this->zhuDeDataPush($attrDataMap, $value['land_code'], $landKey[0], $landKey[1]);
                continue;
            }

            //景区预约量及库存数组5天数据
            $nowTime   = [];
            $nowTime[] = date('Y-m-d');
            for ($i = 1; $i <= 4; $i++) {
                $timeString = "+{$i} day";
                $nowTime[]  = date("Y-m-d", strtotime($timeString));
            }
            $bookList = $governmentDataPushBus->getBookListFromSc($attrDataMap['land_id'], $nowTime);
            if ($bookList['code'] != $governmentDataPushBus::CODE_SUCCESS) {
                $log = [
                    'key'      => 'get_book',
                    'date'     => date('Y-m-d H:i:s'),
                    'apply_id' => $value['apply_id'],
                    'land_id'  => $attrDataMap['land_id'],
                    'now_time' => $nowTime,
                    'result'   => $bookList,
                ];
                pft_log('government/shangdon/error', json_encode($log, JSON_UNESCAPED_UNICODE));
            }

            $data = [];
            foreach ($nowTime as $tmp) {
                $data[] = [
                    'date'         => $tmp,
                    'orderNum'     => $bookList['data']['params'][$tmp]['order_num'] ?? 0,
                    'stock'        => $bookList['data']['params'][$tmp]['stock'] ?? 0,
                    'resourceType' => $governmentDataSysModel->resourceType[$attrDataMap['resource_type']] ?? 'CONTENT_TYPE_SCENERY',
                    'code'         => $value['land_code'],
                    'totalStock'   => $attrDataMap['total_stock'],
                ];
            }

            $governmentDataPushBus->scenicSubmitDataFromSc($data, $landKey[0], $landKey[1], $value['apply_id']);

        }

        return true;
    }

    /**
     * 朱德故里对接四川省平台处理
     * <AUTHOR>
     * @date 2020/6/24
     *
     * @param  array $attrDataMap 属性
     * @param  string $landCode 资源编码
     * @param  string $appid APP_ID
     * @param  string $appsecre APP_SECRET
     *
     */
    public function zhuDeDataPush($attrDataMap, $landCode, $appid, $appsecre)
    {
        $governmentDataPushBus = new \Business\Government\GovernmentDataPush();
        $tid                   = explode(',', $attrDataMap['ticket_id']);//朱德故里套票ID
        $applyId               = 5421411;//供应商ID
        $startDate             = date('Y-m-d');
        $endDate               = date("Y-m-d", strtotime("+5 day"));

        $totalArray = [];
        foreach ($tid as $tidTmp) {
            $calendar = TicketApi::getCalendarPriceAndStorage($tidTmp, $startDate, $endDate);
            foreach ($calendar as $tmps) {
                $orderNums = $tmps['soldStorage'] ?? 0;
                if (empty($totalArray[$tmps['time']])) {
                    $totalArray[$tmps['time']] = 0;
                }
                $totalArray[$tmps['time']] = $totalArray[$tmps['time']] + $orderNums;
            }

        }

        $governmentDataSysModel = new GovernmentDataSys();

        foreach ($calendar as $tmp) {
            $orderNum     = $totalArray[$tmp['time']] ?? 0;
            $dayOfStorage = $tmp['dayOfStorage'] ?? 0;
            if ($dayOfStorage == -1) {
                $dayOfStorage = 8000;
            }
            $stock  = $dayOfStorage - $orderNum;
            $data[] = [
                'date'         => $tmp['time'],
                'orderNum'     => $orderNum,
                'stock'        => $stock,
                'resourceType' => $governmentDataSysModel->resourceType[$attrDataMap['resource_type']] ?? 'CONTENT_TYPE_SCENERY',
                'code'         => $landCode,
                'totalStock'   => $attrDataMap['total_stock'],
            ];
        }

        $governmentDataPushBus->scenicSubmitDataFromSc($data, $appid, $appsecre, $applyId);

    }

    /***************************************************四川省数据平台****************************************************/

    /***************************************************山东省数据平台****************************************************/

    /**
     * 山东省数据平台实时推送(5分钟)
     * <AUTHOR>
     * @date 2020/6/5
     *
     * @return boolean
     */
    public function pushDataBySdMinute()
    {
        $type = GovernmentDataSys::GOVERNMENT_SYS_TYPE_SD;//山东省数据平台类型

        $governmentDataSysModel = new GovernmentDataSys();

        $field = 'id,apply_id,land_code,land_key';
        $data  = $governmentDataSysModel->getGdListByType($type, $field);

        $governmentDataPushBus = new \Business\Government\GovernmentDataPush();
        $dataCollectionModel   = new DataCollection();
        $timeNow               = date("Y-m-d H:i:s");//当前时间戳
        $timeHour              = date("Y-m-d") . ' 00:00:00';

        $gdsIds = array_column($data, 'id');
        if (empty($gdsIds)) {
            return false;
        }

        $attrRes = $governmentDataSysModel->getAttrByGdsIds($gdsIds);
        if (empty($attrRes)) {
            return false;
        }

        //获取属性数据
        $attrData = [];
        foreach ($attrRes as $item) {
            $attrData[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        foreach ($data as $value) {
            $landKey = explode('|', $value['land_key']);
            if (empty($landKey[0]) || empty($landKey[1])) {
                continue;
            }

            $tokenRes = $governmentDataPushBus->getTokenFromSd($landKey[0], $landKey[1], $value['id']);
            if (empty($tokenRes)) {
                continue;
            }

            $attrDataMap = $attrData[$value['id']] ?? [];
            if (empty($attrDataMap['land_id']) || empty($value['land_code'])) {
                continue;
            }

            //获取入园数据
            $enterDataRes = $dataCollectionModel->getEnterData($timeHour, $timeNow, $attrDataMap['land_id']);
            if ($enterDataRes) {
                $enterTotal = (int)$enterDataRes[0]['cnt'];
            }

            //获取出园数据
            $leaveDataRes = $dataCollectionModel->getLeaveData($timeHour, $timeNow, $attrDataMap['land_id']);
            if ($leaveDataRes) {
                $leaveTotal = (int)$leaveDataRes[0]['cnt'];
            }

            //景区预约量及余票量数组30天数据
            $nowTime   = [];
            $nowTime[] = date('Y-m-d');
            for ($i = 1; $i <= 29; $i++) {
                $timeString = "+{$i} day";
                $nowTime[]  = date("Y-m-d", strtotime($timeString));
            }
            $bookList = $governmentDataPushBus->getBookListFromSd($attrDataMap['land_id'], $nowTime);
            if ($bookList['code'] != $governmentDataPushBus::CODE_SUCCESS) {
                $log = [
                    'key'      => 'get_book',
                    'date'     => date('Y-m-d H:i:s'),
                    'apply_id' => $value['apply_id'],
                    'land_id'  => $attrDataMap['land_id'],
                    'now_time' => $nowTime,
                    'result'   => $bookList,
                ];
                pft_log('government/shangdon/error', json_encode($log, JSON_UNESCAPED_UNICODE));
            }

            //入园人数小于出园人数的时候,瞬时客流统一传0
            $realtimeTourists = $enterTotal - $leaveTotal;
            if ($realtimeTourists < 0) {
                $realtimeTourists = 0;
            }

            $data = [
                'scenic_id'         => $value['land_code'],
                'book_list'         => $bookList['data']['params'],
                'scenic_status'     => $attrDataMap['scenic_status'],
                'notice'            => $attrDataMap['sub_notice'] ?? '',
                'realtime_tourists' => $realtimeTourists,
                'total_tourists'    => $enterTotal,
                'request_time'      => time(),
                'out_tourists'      => $leaveTotal
            ];
            $governmentDataPushBus->scenicSubmitDataFromSd($data, $tokenRes['access_token'], $value['apply_id']);

        }

        return true;
    }

    /**
     * 山东省数据平台实时推送(5分钟)
     * <AUTHOR>
     * @date 2020/6/5
     *
     * @return boolean
     */
    public function pushDataBySdMinuteNew()
    {
        $type = 33;//山东省数据平台类型(新)

        $governmentDataSysModel = new GovernmentDataSys();

        $field = 'id,apply_id,land_code,land_key';
        $data  = $governmentDataSysModel->getGdListByType($type, $field);

        $governmentDataPushBus = new \Business\Government\GovernmentDataPush();
        $dataCollectionModel   = new DataCollection();
        $timeNow               = date("Y-m-d H:i:s");//当前时间戳
        $timeHour              = date("Y-m-d") . ' 00:00:00';

        $gdsIds = array_column($data, 'id');
        if (empty($gdsIds)) {
            return false;
        }

        $attrRes = $governmentDataSysModel->getAttrByGdsIds($gdsIds);
        if (empty($attrRes)) {
            return false;
        }

        //获取属性数据
        $attrData = [];
        foreach ($attrRes as $item) {
            $attrData[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        foreach ($data as $value) {
            $landKey = explode('|', $value['land_key']);
            if (empty($landKey[0]) || empty($landKey[1])) {
                continue;
            }

            $tokenRes = $governmentDataPushBus->getTokenFromSdNew($landKey[0], $landKey[1], $value['id']);
            if (empty($tokenRes)) {
                continue;
            }

            $attrDataMap = $attrData[$value['id']] ?? [];
            if (empty($attrDataMap['land_id']) || empty($value['land_code'])) {
                continue;
            }

            // 查询新表
            $enterTotal = 0;
            $leaveTotal = 0;
            // 获取入园数据
            $enterDataRes = $dataCollectionModel->getEnterAndOutData($timeNow, $timeHour, [$attrDataMap['land_id']],
                0, 'sum(num) as cnt');
            if ($enterDataRes) {
                $enterTotal = (int)$enterDataRes[0]['cnt'];
            }

            // 获取出园数据
            $leaveDataRes = $dataCollectionModel->getEnterAndOutData($timeNow, $timeHour, [$attrDataMap['land_id']],
                1, 'sum(num) as cnt');
            if ($leaveDataRes) {
                $leaveTotal = (int)$leaveDataRes[0]['cnt'];
            }

            if ($enterTotal == 0 && $leaveTotal == 0) {
                //获取入园数据
                $enterDataRes = $dataCollectionModel->getEnterData($timeHour, $timeNow, $attrDataMap['land_id']);
                if ($enterDataRes) {
                    $enterTotal = (int)$enterDataRes[0]['cnt'];
                }

                //获取出园数据
                $leaveDataRes = $dataCollectionModel->getLeaveData($timeHour, $timeNow, $attrDataMap['land_id']);
                if ($leaveDataRes) {
                    $leaveTotal = (int)$leaveDataRes[0]['cnt'];
                }
            }

            //景区预约量及余票量数组30天数据
            $nowTime   = [];
            $nowTime[] = date('Y-m-d');
            for ($i = 1; $i <= 29; $i++) {
                $timeString = "+{$i} day";
                $nowTime[]  = date("Y-m-d", strtotime($timeString));
            }
            $bookList = $governmentDataPushBus->getBookListFromSd($attrDataMap['land_id'], $nowTime);
            if ($bookList['code'] != $governmentDataPushBus::CODE_SUCCESS) {
                $log = [
                    'key'      => 'get_book',
                    'date'     => date('Y-m-d H:i:s'),
                    'apply_id' => $value['apply_id'],
                    'land_id'  => $attrDataMap['land_id'],
                    'now_time' => $nowTime,
                    'result'   => $bookList,
                ];
                pft_log('government/shangdon/error', json_encode($log, JSON_UNESCAPED_UNICODE));
            }

            //入园人数小于出园人数的时候,瞬时客流统一传0
            $realtimeTourists = $enterTotal - $leaveTotal;
            if ($realtimeTourists < 0) {
                $realtimeTourists = 0;
            }

            $data = [
                'scenic_id'         => $value['land_code'],
                'book_list'         => $bookList['data']['params'],
                'scenic_status'     => $attrDataMap['scenic_status'],
                'notice'            => $attrDataMap['sub_notice'] ?? '',
                'realtime_tourists' => $realtimeTourists,
                'total_tourists'    => $enterTotal,
                'request_time'      => time(),
                'out_tourists'      => $leaveTotal
            ];
            $governmentDataPushBus->scenicSubmitDataFromSdNew($data, $tokenRes['access_token'], $value['apply_id']);

        }

        return true;
    }


    /**
     * php  /var/www/html/Service/Crontab/runNew.php  Report/GovernmentDataPush pushReserveDataBySdMinute
     * 山东省预约游客信息上传接口(每天凌晨 2-5 点上传前一天的全量数据)
     * <AUTHOR>
     * @date 2022/01/11
     *
     * @return boolean
     */
    public function pushReserveDataBySdMinute()
    {
        $type = 33;//山东省数据平台类型

        $governmentDataSysModel = new GovernmentDataSys();

        $field = 'id,apply_id,land_code,land_key';
        $data  = $governmentDataSysModel->getGdListByType($type, $field);

        $governmentDataPushBus = new \Business\Government\GovernmentDataPush();
        $gdsIds                = array_column($data, 'id');
        if (empty($gdsIds)) {
            return false;
        }

        $attrRes = $governmentDataSysModel->getAttrByGdsIds($gdsIds);
        if (empty($attrRes)) {
            return false;
        }

        //获取属性数据
        $attrData = [];
        foreach ($attrRes as $item) {
            $attrData[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        foreach ($data as $value) {
            //$value['land_key'] = "wlyabcde4imnoruyz4|wlyab2cde3fgh4i8j5lmn6p7q86s79wxyz4";
            //$value['land_code'] = '2c90938f3b506673013b555513680c7c';
            $landKey           = explode('|', $value['land_key']);
            if (empty($landKey[0]) || empty($landKey[1])) {
                continue;
            }

            $tokenRes = $governmentDataPushBus->getTokenFromSdNew($landKey[0], $landKey[1], $value['id']);
            if (empty($tokenRes)) {
                continue;
            }

            $attrDataMap = $attrData[$value['id']] ?? [];
            if (empty($attrDataMap['land_id']) || empty($value['land_code'])) {
                continue;
            }

            //景区昨日报表数据
            $startTime   = strtotime(date('Y-m-d 00:00:00', strtotime('-1 days')));
            $endTime     = strtotime(date('Y-m-d 23:59:59', strtotime('-1 days')));
            $reportModel = new GovernmentTouristStatistics();
            $reserInfo   = $reportModel->getReserveOrderNumByLids([$attrDataMap['land_id']],
                $value['apply_id'], $startTime, $endTime);

            $orderNumArr = [];
            foreach ($reserInfo as $reItem) {
                $ordersArr = json_decode($reItem['orders_info'], true);
                if (empty($ordersArr)) {
                    continue;
                }

                foreach ($ordersArr as $item) {
                    $orderNumArr[] = $item[0];
                }
            }

            $orderApi       = new OrderDetailQuery();
            $orderDetailArr = $orderApi->getOrderWithDetail($orderNumArr);
            if ($orderDetailArr['code'] == 200) {
                foreach ($orderDetailArr['data'] as $orderDetItem) {
                    $reserveTime = json_decode($orderDetItem['fxDetails']['extContent'], true);
                    if (empty($reserveTime['sectionTimeStr'])) {
                        continue;
                    }

                    list($startTime, $endTime) = explode('-', $reserveTime['sectionTimeStr']);
                    $playDate                                          = date('Y-m-d',
                        strtotime($orderDetItem['playtime']));
                    $startTime                                         = strtotime($playDate . $startTime);
                    $endTime                                           = strtotime($playDate . $endTime);
                    $reserveTimeAndOrderNum[$orderDetItem['ordernum']] = [
                        'startTime' => $startTime,
                        'endTime'   => $endTime,
                    ];
                }
            } else {
                $log = [
                    'orderNum' => $orderNumArr,
                    'response' => $orderDetailArr,
                ];
                pft_log('government/shangdon/error', "山东获取订单信息失败" . json_encode($log, JSON_UNESCAPED_UNICODE));
                continue;
            }

            $orderQueryModel = new \Model\Order\OrderQuery();
            $touristListArr  = $orderQueryModel->getTouristList($orderNumArr);
            $touristBookInfo = [];
            foreach ($touristListArr as $tourItem) {
                $touristBookInfo[] = [
                    'book_start_time' => $reserveTimeAndOrderNum[$tourItem['orderid']]['startTime'],
                    'book_end_time'   => $reserveTimeAndOrderNum[$tourItem['orderid']]['endTime'],
                    'name'            => $tourItem['tourist'],
                    'id_card'         => $tourItem['idcard'],
                    'phone'           => !empty($tourItem['mobile']) ? $tourItem['mobile'] : "",
                    'book_in_time'    => $reserveTimeAndOrderNum[$tourItem['orderid']]['startTime'],
                ];
            }

            if (empty($touristBookInfo)) {
                $log = [
                    'fid'      => $value['apply_id'],
                    'orderNum' => $orderNumArr,
                ];
                pft_log('government/shangdon/error', "当天无预约信息" . json_encode($log, JSON_UNESCAPED_UNICODE));
                continue;
            }

            $data = [
                'scenic_id'         => $value['land_code'],
                'request_time'      => time(),
                'tourist_book_info' => $touristBookInfo,
            ];

            $governmentDataPushBus->scenicSubmitReserveDataFromSdNew($data, $tokenRes['access_token'], $value['apply_id']);
        }

        return true;
    }

    /**
     * php  /var/www/html/Service/Crontab/runNew.php  Report/GovernmentDataPush pushCheckDataBySdMinute
     * 山东省预约游客信息上传接口(每天凌晨 2-5 点上传前一天的全量数据)
     * <AUTHOR>
     * @date 2022/01/11
     *
     * @return boolean
     */
    public function pushCheckDataBySdMinute()
    {
        $type = 33;//山东省数据平台类型

        $governmentDataSysModel = new GovernmentDataSys();

        $field = 'id,apply_id,land_code,land_key';
        $data  = $governmentDataSysModel->getGdListByType($type, $field);

        $governmentDataPushBus = new \Business\Government\GovernmentDataPush();
        $gdsIds                = array_column($data, 'id');
        if (empty($gdsIds)) {
            return false;
        }

        $attrRes = $governmentDataSysModel->getAttrByGdsIds($gdsIds);
        if (empty($attrRes)) {
            return false;
        }

        //获取属性数据
        $attrData = [];
        foreach ($attrRes as $item) {
            $attrData[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        foreach ($data as $value) {
            //$value['land_key'] = "wlyabcde4imnoruyz4|wlyab2cde3fgh4i8j5lmn6p7q86s79wxyz4"; //TODO
            //$value['land_code'] = '2c90938f3b506673013b555513680c7c';
            $landKey           = explode('|', $value['land_key']);
            if (empty($landKey[0]) || empty($landKey[1])) {
                continue;
            }

            $tokenRes = $governmentDataPushBus->getTokenFromSdNew($landKey[0], $landKey[1], $value['id']);
            if (empty($tokenRes)) {
                continue;
            }

            $attrDataMap = $attrData[$value['id']] ?? [];
            if (empty($attrDataMap['land_id']) || empty($value['land_code'])) {
                continue;
            }

            //景区昨日报表数据
            $startTime   = strtotime(date('Y-m-d 00:00:00', strtotime('-1 days')));
            $endTime     = strtotime(date('Y-m-d 23:59:59', strtotime('-1 days')));
            $reportModel = new GovernmentTouristStatistics();
            $reserInfo   = $reportModel->getCheckedOrderNumByLids([$attrDataMap['land_id']],
                $value['apply_id'], $startTime, $endTime);
            $orderNumArr = [];
            $orderTime = [];
            foreach ($reserInfo as $reItem) {
                $ordersArr = json_decode($reItem['orders_info'], true);
                if (empty($ordersArr)) {
                    continue;
                }

                foreach ($ordersArr as $item) {
                    $orderNumArr[]       = $item[0];
                    $orderTime[$item[0]] = $reItem['update_time'];
                }
            }

            $orderQueryModel = new \Model\Order\OrderQuery();
            $touristListArr  = $orderQueryModel->getTouristList($orderNumArr);
            $touristCheckInfo = [];
            foreach ($touristListArr as $tourItem) {
                $touristCheckInfo[] = [
                    'in_time' => $orderTime[$tourItem['orderid']],
                    'name'    => $tourItem['tourist'],
                    'id_card' => $tourItem['idcard'],
                    'phone'   => !empty($tourItem['mobile']) ? $tourItem['mobile'] : "",
                ];
            }

            if (empty($touristCheckInfo)) {
                $log = [
                    'fid'      => $value['apply_id'],
                    'orderNum' => $orderNumArr,
                ];
                pft_log('government/shangdon/error', "当天无核销信息" . json_encode($log, JSON_UNESCAPED_UNICODE));
                continue;
            }

            $data = [
                'scenic_id'       => $value['land_code'],
                'request_time'    => time(),
                'tourist_in_info' => $touristCheckInfo,
            ];
            $governmentDataPushBus->scenicSubmitCheckDataFromSdNew($data, $tokenRes['access_token'], $value['apply_id']);
        }

        return true;
    }

    /**
     * 山东省数据平台实时推送(1分钟)
     * 山东省 九龙峪公安 单独配置1分钟一次
     * <AUTHOR>
     * @date 2021/2/4
     *
     * @return boolean
     */
    public function pushDataToSdJiuLong()
    {
        $governmentDataPushBus = new \Business\Government\GovernmentDataPush();
        $dataCollectionModel   = new DataCollection();
        $timeNow               = date("Y-m-d H:i:s");//当前时间戳
        $timeHour              = date("Y-m-d") . ' 00:00:00';
        $tokenRes              = $governmentDataPushBus->getTokenFromSd("d723fb22116942adbe6c6d943af3d6e2",
            "486045c7f8a9b0fb868f8026fc69618c", 24);
        if (empty($tokenRes)) {
            pft_log('government/SDJL/data', json_encode(['res' => $tokenRes]));
            exit();
        }

        //获取入园数据
        $enterDataRes = $dataCollectionModel->getEnterData($timeHour, $timeNow, 193249);
        if ($enterDataRes) {
            $enterTotal = (int)$enterDataRes[0]['cnt'];
        }

        //获取出园数据
        $leaveDataRes = $dataCollectionModel->getLeaveData($timeHour, $timeNow, 193249);
        if ($leaveDataRes) {
            $leaveTotal = (int)$leaveDataRes[0]['cnt'];
        }

        //景区预约量及余票量数组30天数据
        $nowTime   = [];
        $nowTime[] = date('Y-m-d');
        for ($i = 1; $i <= 29; $i++) {
            $timeString = "+{$i} day";
            $nowTime[]  = date("Y-m-d", strtotime($timeString));
        }
        $bookList = $governmentDataPushBus->getBookListFromSd(193249, $nowTime);
        if ($bookList['code'] != $governmentDataPushBus::CODE_SUCCESS) {
            $log = [
                'key'      => 'get_book',
                'date'     => date('Y-m-d H:i:s'),
                'apply_id' => 5719224,
                'land_id'  => 193249,
                'now_time' => $nowTime,
                'result'   => $bookList,
            ];
            pft_log('government/SDJL/error', json_encode($log, JSON_UNESCAPED_UNICODE));
        }

        //入园人数小于出园人数的时候,瞬时客流统一传0
        $realtimeTourists = $enterTotal - $leaveTotal;
        if ($realtimeTourists < 0) {
            $realtimeTourists = 0;
        }

        $data = [
            'scenic_id'         => "21e0cead5d934e6cebf1ed8aa3989a2d",
            'book_list'         => $bookList['data']['params'],
            'scenic_status'     => 1,
            'notice'            => '',
            'realtime_tourists' => $realtimeTourists,
            'total_tourists'    => $enterTotal,
        ];
        $governmentDataPushBus->scenicSubmitDataFromSd($data, $tokenRes['access_token'], 5719224);
    }


    /***************************************************山东省数据平台****************************************************/

    /***************************************************江西省数据平台****************************************************/

    /**
     * 江西省实时推送（15分钟）
     * <AUTHOR>
     * @date 2019/12/2 0002
     *
     * @return boolean
     */
    public function pushDataByJxMinute()
    {
        $type = GovernmentDataSys::GOVERNMENT_SYS_TYPE_JX;//江西省数据平台类型

        $governmentDataSysModel = new GovernmentDataSys();

        $field   = 'id,apply_id,land_code,land_key';
        $resList = $governmentDataSysModel->getGdListByType($type, $field);
        $resList = $this->appendGovAttr($resList);
        foreach ($resList as $key => $value) {
            $sidArr = ['359356', '11108923'];
            if (in_array($value['apply_id'], $sidArr)) {
                unset($resList[$key]);
            }
        }

        if ($resList) {
            $task = new MulityProcessHelper($this, 10, 0);
            $task->run($resList, ['type' => 'jxMinute']);
        }

        return true;

    }

    /**
     * 江西省实时推送（10分钟） 账号：127022
     * <AUTHOR>
     * @date 2021-06-08
     */
    public function pushDataByJxTenMinute()
    {
        $type = GovernmentDataSys::GOVERNMENT_SYS_TYPE_JX;//江西省数据平台类型

        $governmentDataSysModel = new GovernmentDataSys();

        $apply_id = '359356'; // 供应商id
        $resList = $governmentDataSysModel->getInfoByApplyIdType($apply_id, $type);
        if ($resList) {
            $task = new MulityProcessHelper($this, 10, 0);
            $list = [$resList];
            $task->run($list, ['type' => 'jxMinute']);
        }

        return true;
    }

    /**
     * 江西省每天推送
     * <AUTHOR>
     * @date 2019/12/2 0002
     *
     * @return boolean
     */
    public function pushDataByJxDay()
    {
        $type = GovernmentDataSys::GOVERNMENT_SYS_TYPE_JX;//江西省数据平台类型

        $governmentDataSysModel = new GovernmentDataSys();

        $field   = 'id,apply_id,land_code,land_key';
        $resList = $governmentDataSysModel->getGdListByType($type, $field);

        foreach ($resList as $key => $value) {
            $sidArr = ['11108923'];
            if (in_array($value['apply_id'], $sidArr)) {
                unset($resList[$key]);
            }
        }
        $resList = $this->appendGovAttr($resList);
        if ($resList) {
            $task = new MulityProcessHelper($this, 5, 0);
            $task->run($resList, ['type' => 'jxDay']);
        }

        return true;
    }

    /**
     * 添加属性字段
     * @param $resList
     * @return array
     */
    private function appendGovAttr($resList) {
        if (empty($resList)) {
            return $resList;
        }
        $gdsIdArr = array_column($resList, 'id');
        $govModel = new GovernmentDataSys();
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            return $resList;
        }
        //将配置列表和配置列表属性整合成同一个列表
        foreach ($resList as &$list) {
            foreach ($govAttrList as $attrList) {
                if ($list['id'] == $attrList['gds_id']) {
                    $list[$attrList['attr_key']] = $attrList['attr_val'];
                }
            }
        }
        return $resList;
    }

    /**
     * 江西省实时数据推送
     * <AUTHOR>
     * @date 2019/12/3 0003
     *
     * @param $data
     *
     * @return boolean
     */
    private function runJxMinuteWorker($data)
    {
        $dataCollectionModel = new DataCollection();
        //$landModel           = new Land('slave');

        $javaApi        = new \Business\CommodityCenter\Land();
        $requestUrl     = $this->pushUrl . 'real-people-number';//累计入园
        $requestExitUrl = $this->pushUrl . 'real-exit-people-number';//累计出园

        $timeNow = date("Y-m-d H:i:s");//当前时间戳
        //$timeHour = date("Y-m-d H:i:s", strtotime("-15 minute"));//15分钟前的时间
        $timeHour = date("Y-m-d") . ' 00:00:00';

        foreach ($data as $value) {
            $enterTotal = 0;//入园人数统计
            $leaveTotal = 0;//出园人数统计

            $landIdList = $javaApi->queryLandMultiQueryByApplyDid([$value['apply_id']], '', '', false, [1, 2]);
            //$landIdList = $landModel->getLandField($value['apply_id'], ['in', [1, 2]], 'id,terminal');

            if (!empty($landIdList)) {
                $landArr = array_column($landIdList, 'id');
                $applyIdList = [359356, 15774243, 6007291, 27227740, 4125604, 15883117]; // 查询新表

                //获取入园数据
                if (in_array($value['apply_id'], $applyIdList)) {
                    $enterDataRes = $dataCollectionModel->getDataNew($timeHour, $timeNow, 0, $landArr);
                } else {
                    $enterDataRes = $dataCollectionModel->getEnterData($timeHour, $timeNow, $landArr);
                }

                if ($enterDataRes) {
                    $enterTotal = (int)$enterDataRes[0]['cnt'];
                }

                //获取出园数据
                if (in_array($value['apply_id'], $applyIdList)) {
                    $leaveDataRes = $dataCollectionModel->getDataNew($timeHour, $timeNow, 1, $landArr);
                } else {
                    $leaveDataRes = $dataCollectionModel->getLeaveData($timeHour, $timeNow, $landArr);
                }

                if ($leaveDataRes) {
                    $leaveTotal = (int)$leaveDataRes[0]['cnt'];
                }
            }

            //入园人数统计数据格式封装
            $pushDataEnter = [
                'scenicCode' => $value['land_code'],
                'upTime'     => $timeNow,
                'total'      => $enterTotal,
                'secret'      => $value['secret'] ?? '',
            ];

            //出园人数统计数据格式封装
            $pushDataLeave = [
                'scenicCode' => $value['land_code'],
                'upTime'     => $timeNow,
                'total'      => $leaveTotal,
                'secret'      => $value['secret'] ?? '',
            ];

            $pushResEn = $this->postRequest($requestUrl, $pushDataEnter, $value['land_key']);
            $pushResIn = $this->postRequest($requestExitUrl, $pushDataLeave, $value['land_key']);

            $logData = [
                'logKey'     => 'jx_minute_push_' . $value['apply_id'],
                'applyId'    => $value['apply_id'],
                'pushDataEn' => $pushDataEnter,
                'pushDataIn' => $pushDataLeave,
                'pushResEn'  => $pushResEn,
                'pushResIn'  => $pushResIn,
            ];
            pft_log('government/jiangxi/debug', json_encode($logData, JSON_UNESCAPED_UNICODE));
        }

        return true;

    }

    /**
     * 江西省每天数据推送
     * <AUTHOR>
     * @date 2019/12/3 0003
     *
     * @param $data
     *
     * @return boolean
     */
    private function runJxDayWorker($data)
    {
        $dataCollectionModel = new DataCollection();
        //$landModel           = new Land('slave');
        $javaApi    = new \Business\CommodityCenter\Land();
        $requestUrl = $this->pushUrl . 'real-gate-day';//累计入园

        $timeDate = date("Y-m-d", strtotime("-1 day"));
        $timeNow  = $timeDate . ' 00:00:00';//昨天开始时间
        $timeHour = $timeDate . ' 23:59:59';//昨天结束时间

        foreach ($data as $value) {
            $enterTotal = 0;//入园人数统计
            //$landIdList = $landModel->getLandField($value['apply_id'], ['in', [1, 2]], 'id,terminal');

            $landIdList = $javaApi->queryLandMultiQueryByApplyDid([$value['apply_id']], '', '', false, [1, 2]);

            if (!empty($landIdList)) {
                $landArr = array_column($landIdList, 'id');
                //获取入园数据

                $applyIdList = [359356, 15774243, 6007291, 27227740, 4125604, 15883117]; // 查询新表

                //获取入园数据
                if (in_array($value['apply_id'], $applyIdList)) {
                    $enterDataRes = $dataCollectionModel->getDataNew($timeNow, $timeHour, 0, $landArr);
                } else {
                    $enterDataRes = $dataCollectionModel->getEnterData($timeNow, $timeHour, $landArr);
                }

                if ($enterDataRes) {
                    $enterTotal = (int)$enterDataRes[0]['cnt'];
                }
            }

            //入园人数统计数据格式封装
            $pushDataEnter[] = [
                'scenicCode' => $value['land_code'],
                'upTime'     => $timeDate,
                'total'      => $enterTotal,
                'secret'      => $value['secret'] ?? '',
            ];

            $pushResEn = $this->postRequest($requestUrl, $pushDataEnter, $value['land_key']);

            $logData       = [
                'logKey'     => 'jx_day_push_' . $value['apply_id'],
                'applyId'    => $value['apply_id'],
                'pushDataEn' => $pushDataEnter,
                'pushResEn'  => $pushResEn,
            ];
            $pushDataEnter = [];
            pft_log('government/jiangxi/debug', json_encode($logData, JSON_UNESCAPED_UNICODE));
        }

        return true;

    }

    /***************************************************江西省数据平台****************************************************/

    /***************************************************葛仙山数据推送市平台***********************************************/

    private $gexianshanUrl = 'https://openapi.all4tour.cn/zjxtdj';

    private $gexianshanCode = 'yanshangexianshan';

    private $gexianshanSecret = 'fBPre101LqpwCWzW8xRHlrEVKxSf06uh';

    private $gexianshanApplyId = 2020147;

    /**
     * 葛仙山实时数据推送市平台
     * <AUTHOR>
     * @date 2019/12/25 0025
     *
     * @return boolean
     */
    public function gexianshanMinutePush()
    {
        $dataCollectionModel = new DataCollection();
        //$landModel           = new Land('slave');
        $requestUrl          = $this->gexianshanUrl . '/data/tourist/real-people-number';//累计入园
        $requestExitUrl      = $this->gexianshanUrl . '/data/tourist/real-exit-people-number';//累计出园
        $timeNews            = time();

        $timeNow  = date("Y-m-d H:i:s");//当前时间戳
        $timeHour = date("Y-m-d") . ' 00:00:00';

        $enterTotal = 0;//入园人数统计
        $leaveTotal = 0;//出园人数统计

        $javaApi    = new \Business\CommodityCenter\Land();
        $landIdList = $javaApi->queryLandMultiQueryByApplyDid([$this->gexianshanApplyId], '', '', false, [1]);
        //$landIdList = $landModel->getLandField($this->gexianshanApplyId, 1, 'id,terminal');

        if (!empty($landIdList)) {
            $landArr = array_column($landIdList, 'id');
            //获取入园数据

            $enterDataRes = $dataCollectionModel->getEnterData($timeHour, $timeNow, $landArr);

            if ($enterDataRes) {
                $enterTotal = (int)$enterDataRes[0]['cnt'];
            }

            //获取出园数据
            $leaveDataRes = $dataCollectionModel->getLeaveData($timeHour, $timeNow, $landArr);
            if ($leaveDataRes) {
                $leaveTotal = (int)$leaveDataRes[0]['cnt'];
            }

        }

        //入园人数统计数据格式封装
        $bodyArr       = [
            'time'    => $timeNow,
            'devices' => [
                [
                    'device'  => '葛仙山闸机',
                    'tickets' => [
                        [
                            'ticket' => '葛仙山门票',
                            'count'  => $enterTotal,
                        ],
                    ],
                ],
            ],
        ];
        $bodyArr       = json_encode($bodyArr);
        $sign          = $this->getGxsSign($this->gexianshanSecret, $bodyArr, $timeNews * 1000);
        $headerArr     = [
            'scenicCode' => $this->gexianshanCode,
            'time'       => $timeNews * 1000,
            'sign'       => $sign,
        ];
        $pushDataEnter = [
            'header' => $headerArr,
            'body'   => $bodyArr,
        ];
        $header        = ['Content-Type:application/json'];
        $jsonData      = json_encode($pushDataEnter);
        $pushResEn     = curl_post($requestUrl, $jsonData, 80, 25, '/api/curl_post', $header);

        //出园人数统计数据格式封装
        $bodyArrIn     = [
            'time'    => $timeNow,
            'devices' => [
                [
                    'device' => '葛仙山闸机',
                    'count'  => $leaveTotal,
                ],
            ],
        ];
        $bodyArrIn     = json_encode($bodyArrIn);
        $signIn        = $this->getGxsSign($this->gexianshanSecret, $bodyArrIn, $timeNews * 1000);
        $headerArr     = [
            'scenicCode' => $this->gexianshanCode,
            'time'       => $timeNews * 1000,
            'sign'       => $signIn,
        ];
        $pushDataLeave = [
            'header' => $headerArr,
            'body'   => $bodyArrIn,
        ];
        $jsonData      = json_encode($pushDataLeave);
        $pushResIn     = curl_post($requestExitUrl, $jsonData, 80, 25, '/api/curl_post', $header);

        //日志
        $logData = [
            'logKey'     => 'gxs_minute_push',
            'applyId'    => $this->gexianshanApplyId,
            'pushDataEn' => $pushDataEnter,
            'pushDataIn' => $pushDataLeave,
            'pushResEn'  => $pushResEn,
            'pushResIn'  => $pushResIn,
        ];
        pft_log('government/gexianshan/debug', json_encode($logData, JSON_UNESCAPED_UNICODE));

        return true;
    }

    /**
     * 葛仙山日数据推送市平台
     * <AUTHOR>
     * @date 2019/12/25 0025
     *
     * @return boolean
     */
    public function gexianshanDayPush()
    {
        $dataCollectionModel = new DataCollection();
        //$landModel           = new Land('slave');
        $requestUrl = $this->gexianshanUrl . '/data/tourist/real-gate-day';//累计入园
        $timeNews   = time();

        $timeDate = date("Y-m-d", strtotime("-1 day"));
        $timeNow  = $timeDate . ' 00:00:00';//昨天开始时间
        $timeHour = $timeDate . ' 23:59:59';//昨天结束时间

        $enterTotal = 0;//入园人数统计
        //$landIdList = $landModel->getLandField($this->gexianshanApplyId, 1, 'id,terminal');
        //
        //if (!empty($landIdList)) {
        //    $landArr = array_column($landIdList, 'id');
        //    //获取入园数据
        //}

        $enterDataRes = $dataCollectionModel->getEnterData($timeNow, $timeHour, [111391]);

        if ($enterDataRes) {
            $enterTotal = (int)$enterDataRes[0]['cnt'];
        }

        //入园人数统计数据格式封装
        $bodyArr[] = [
            'day'   => $timeDate,
            'count' => $enterTotal,
        ];
        $bodyArr   = json_encode($bodyArr);

        //生成签名
        $sign = $this->getGxsSign($this->gexianshanSecret, $bodyArr, $timeNews * 1000);

        $headerArr = [
            'scenicCode' => $this->gexianshanCode,
            'time'       => $timeNews * 1000,
            'sign'       => $sign,
        ];

        $pushDataEnter = [
            'header' => $headerArr,
            'body'   => $bodyArr,
        ];

        $header   = ['Content-Type:application/json'];
        $jsonData = json_encode($pushDataEnter);

        $pushResEn = curl_post($requestUrl, $jsonData, 80, 25, '/api/curl_post', $header);

        $logData       = [
            'logKey'     => 'gxs_day_push',
            'applyId'    => $this->gexianshanApplyId,
            'pushDataEn' => $pushDataEnter,
            'pushResEn'  => $pushResEn,
        ];
        $pushDataEnter = [];
        pft_log('government/gexianshan/debug', json_encode($logData, JSON_UNESCAPED_UNICODE));

        return true;
    }

    /**
     * 获取签名
     * <AUTHOR>
     * @date 2019/12/26 0026
     *
     * @return string
     */
    private function getGxsSign($secret, $body, $time)
    {
        $string = $secret . '&' . $body . '&' . $time;
        $sign   = md5($string);

        return $sign;
    }


    /***************************************************葛仙山数据推送市平台***********************************************/

    /**
     * 发起post请求
     * <AUTHOR>
     * @date 2019/12/5 0005
     *
     * @param $url
     * @param $data
     * @param $key
     *
     * @return array
     */
    private function postRequest($url, $data, $key)
    {
        $pushResEn = curl_post($url, json_encode($data), 80, 25, '/api/curl_post',
            ['appKey:' . $key, 'Content-Type:application/json']);

        return $pushResEn;
    }

    /**
     * 推送数据到河北
     * <AUTHOR>
     */
    public function pushDataToHeBeiReservation()
    {
        // 获取需要对接到对方平台的景区数据
        $model = new GovernmentDataSys();

        $field   = 'id,apply_id,land_code,land_key';
        $govList = $model->getGdListByType(GovernmentDataSys::GOVERNMENT_SYS_TYPE_HB, $field);
        if (empty($govList)) {
            pft_log('government/hebei/error', '获取对应配置列表为空, sql:' . $model->getLastSql());

            return;
        }
        $govListMap = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
        }

        // 获取gov id -> 获取government_data_attr
        $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $model->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/hebei/error', '获取对应的data_attr表数据为空, sql:' . $model->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        // 获取对应的所有tid
        $tidArr        = [];
        $ticketInfoArr = array_column($govWithAttrListMap, 'land_ticket_info');
        foreach ($ticketInfoArr as $item) {
            $item   = json_decode($item, true);
            $tidArr = array_merge($tidArr, $item);
        }
        pft_log('government/hebei/log', 'tid = ' . json_encode($tidArr));
        // 获取订单追踪记录
        $endTime   = date('Y-m-d H:i:s', time());
        $beginTime = date('Y-m-d H:i:s', strtotime('-1 minutes'));

        $orderTool = new OrderTools();
        //$orderTrack = new OrderTrack();

        $actionArr = [0, 1, 2, 5];
        $where     = [
            'insertTime' => ['between', [$beginTime, $endTime]],
            'action'     => ['in', $actionArr],
            'tid'        => ['in', $tidArr],
        ];

        $queryParams = [$endTime, $beginTime, false, $actionArr, $tidArr];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderTrack', 'queryOrderTrackCountByInsertTime',
            $queryParams);
        $total       = 0;
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $total = $queryRes['data'];
        }

        //$total      = $orderTrack->getTotal($where);
        pft_log('government/hebei/log', '总条数为=' . json_encode($total) . '查询条件=' . json_encode($where));
        $pageSize = 50;
        for ($page = 1; $page <= ceil($total / $pageSize); $page++) {
            // 获取最终数据记录
            //$list        = $orderTrack->getList($where, 'ordernum, action, left_num', $page, $pageSize);

            //订单查询迁移二期
            $orderTrackQueryLib = new OrderTrackQuery();
            $list               = $orderTrackQueryLib->getOrderRecordNew($page, $pageSize, $where);

            $ordernumArr = array_unique(array_column($list, 'ordernum'));

            $reservationList = $model->getHebeiTimeShareReservationByOrdernumArr($ordernumArr,
                'id, ordernum, reservation_id, reservation_status');
            $reservationMap  = [];
            foreach ($reservationList as $item) {
                $reservationMap[$item['ordernum']] = $item;
            }

            // 获取订单信息
            $orderInfoList = $orderTool->getOrderInfo($ordernumArr,
                'member, ordernum, ordername, ordertel, lid, ordertime, tnum', 'ext_content');
            $orderInfoList = json_decode($orderInfoList, true);
            $orderInfoList = $orderInfoList['data'];
            $orderInfoMap  = [];
            foreach ($orderInfoList as $item) {
                $orderInfoMap[$item['ordernum']] = $item;
            }

            foreach ($list as $item) {
                $orderInfoTmp       = $orderInfoMap[$item['ordernum']];
                $govInfoTmp         = $govWithAttrListMap[$orderInfoTmp['lid']];
                $reservationInfoTmp = $reservationMap[$item['ordernum']] ?? [];
                $times              = json_decode($govInfoTmp['times'], true);
                $extContent         = json_decode($orderInfoTmp['ext_content'], true);
                $orderTimeId        = $extContent['sectionTimeId'];
                $sessionId          = '';
                $reservationId      = '';
                foreach ($times as $timeItem) {
                    if ($timeItem['time_id'] == $orderTimeId) {
                        $sessionId     = $timeItem['SessionsId'];
                        $reservationId = $timeItem['reservationId'];
                    }
                }

                list($appid, $appSecret) = explode('|', $govInfoTmp['land_key']);
                pft_log('government/hebei/log', '本次发送的land_key = ' . json_encode($govInfoTmp));
                //手机号为空时默认赋值
                if ($orderInfoTmp['ordertel'] == "") {
                    if ($appid == "") {
                        continue;
                    } else {
                        $orderInfoTmp['ordertel'] = $this->getordertel($appid);
                    }
                }

                // 判断action
                switch ($item['action']) {
                    case 0:
                        // 下单
                        if (!isset($reservationMap[$item['ordernum']])) {

                            $res = $this->timeShareOrder($appid, $appSecret,
                                $orderInfoTmp, $govInfoTmp['land_code'],
                                $reservationId, $sessionId, '测试');
                            pft_log('government/hebei/log', '本次推送的结果 = ' . json_encode($res));
                            if ($res['errorCode'] === '00000') {
                                $model->addHebeiTimeShareReservation([
                                    'ordernum'                  => $item['ordernum'],
                                    'reservation_id'            => $res['data']['id'],
                                    'reservation_status'        => 1,
                                    'reservation_time_share_id' => $reservationId,
                                    'reservation_session_id'    => $sessionId,
                                    'create_time'               => time(),
                                    'update_time'               => time(),
                                ]);
                            }

                        }
                        break;
                    case 2:
                        if (isset($reservationMap[$item['ordernum']])) {
                            $this->timeShareCancel($appid, $appSecret,
                                $reservationInfoTmp);
                        }
                        // 取消
                        break;
                    case 5:
                        if (isset($reservationMap[$item['ordernum']])) {
                            $this->timeShareChecked($appid, $appSecret,
                                $reservationInfoTmp, $govInfoTmp['land_code']);
                        }
                        // 验证
                        break;
                }
            }
        }

    }

    private function timeShareOrder($appId, $appKey, $orderInfo, $scenicId, $reservationId, $reservationSessionId, $scenicName)
    {
        // 预约参数
        $data = [
            'openId'               => $orderInfo['member'] . mt_rand(1, 100000),
            'scenicId'             => $scenicId,
            'reservationId'        => $reservationId,
            'reservationSessionId' => $reservationSessionId,
            'scenicName'           => $scenicName,
            'name'                 => $orderInfo['ordername'],
            'phone'                => $orderInfo['ordertel'],
            'carryFlag'            => 1,
            'carryCou'             => ($orderInfo['tnum'] > 10) ? 10 : $orderInfo['tnum'] - 1,
            'reservationDate'      => date('Y-m-d', strtotime($orderInfo['playtime'])),
        ];
        pft_log('government/hebei/log', '本次推送的数据 = ' . json_encode($data));
        $biz = new \Business\Government\GovernmentDataPush();
        $res = $biz->postHebeiReservation($appId, $appKey, '/reservation/appoint',
            $data);

        $res = json_decode($res, true);

        return $res;
    }

    private function timeShareCancel($appId, $appKey, $timeShareInfo)
    {
        $data = [
            'id' => $timeShareInfo['reservation_id'],
        ];

        $biz = new \Business\Government\GovernmentDataPush();
        $res = $biz->postHebeiReservation($appId, $appKey, '/reservation/cancleAppoint',
            $data);

        $res = json_decode($res, true);

        return $res;
    }

    private function timeShareChecked($appId, $appKey, $timeShareInfo, $scenicId)
    {
        $data = [
            'id'         => $timeShareInfo['reservation_id'],
            'scenicId'   => $scenicId,
            'scanUserId' => -1,
        ];

        $biz = new \Business\Government\GovernmentDataPush();
        $res = $biz->postHebeiReservation($appId, $appKey, '/reservation-record/scan',
            $data);

        $res = json_decode($res, true);

        return $res;
    }

    private function getordertel($appid)
    {
        //广府
        if ($appid == "app1272494246338760705") {
            return ***********;
        }
        //娲皇宫
        if ($appid == "app1272494246326177794") {
            return ***********;
        }
    }

    /**
     * 分离订单追踪列表数据, 分成下单， 修改， 验证, 取消列表数据,
     * 如果同时存在下单以及取消 ， 则不推送
     * 如果存在下单以及取消的， 则计算完后只推送一次
     * <AUTHOR>
     *
     * @param  array $list
     *
     * @return array
     */
    private function separateOrderTrack(array $list): array
    {
        $orderArr   = [];
        $editArr    = [];
        $checkedArr = [];
        $cancelArr  = [];

        $listMap = [];

        foreach ($list as $item) {
            $listMap[$item['ordernum']][] = $item;
        }

        foreach ($listMap as $item) {
            if (count($item) > 2) {
                $actionArr = array_column($item, 'action');
                if (in_array(2, $actionArr) && in_array(01, $actionArr)) {
                    // 如果存在下单并取消 则不推送该订单数据
                    continue;
                } else if (in_array(1, $actionArr) && in_array(0, $actionArr)) {
                    // 如果有下单以及修改则需要计算数量
                    // 按照时间进行排序
                    array_multisort($item, array_column($item, 'insertTime'));
                    $leftNum     = 0;
                    $ordernumTmp = '';
                    foreach ($item as $orderTrackInfo) {
                        $ordernumTmp = $orderTrackInfo['ordernum'];
                        if ($orderTrackInfo['action'] == 2) {
                            $leftNum = $orderTrackInfo['left_num'];
                        }

                        if ($orderTrackInfo['action'] == 5) {
                            // 记录验证的
                            $checkedArr[] = [
                                'ordernum' => $orderTrackInfo['ordernum'],
                                'left_num' => $orderTrackInfo['left_num'],
                            ];
                        }
                    }

                    // 记录成下单信息
                    if (!empty($leftNum)) {
                        $orderArr[] = [
                            'ordernum' => $ordernumTmp,
                            'left_num' => $leftNum,
                        ];
                    }

                } else if (in_array(1, $actionArr) && in_array(5, $actionArr)) {
                    // 存在修改以及验证
                    array_multisort($item, array_column($item, 'insertTime'));

                    $leftNum     = 0;
                    $ordernumTmp = '';
                    foreach ($item as $orderTrackInfo) {
                        if ($orderTrackInfo['action'] == 2) {
                            $leftNum = $orderTrackInfo['left_num'];
                        }

                        if ($orderTrackInfo['action'] == 5) {
                            // 记录验证的
                            $checkedArr[] = [
                                'ordernum' => $orderTrackInfo['ordernum'],
                                'left_num' => $orderTrackInfo['left_num'],
                            ];
                        }
                    }

                    $editArr[] = [
                        'ordernum' => $ordernumTmp,
                        'left_num' => $leftNum,
                    ];

                } else {

                }

            } else {
                $orderTrackInfo = $item[0];
                switch ($orderTrackInfo['action']) {
                    case 0:
                        $orderArr[] = [
                            'ordernum' => $orderTrackInfo['ordernum'],
                            'left_num' => $orderTrackInfo['left_num'],
                        ];
                        break;
                    case 1:
                        $editArr[] = [
                            'ordernum' => $orderTrackInfo['ordernum'],
                            'left_num' => $orderTrackInfo['left_num'],
                        ];
                        break;
                    case 2:
                        $cancelArr[] = [
                            'ordernum' => $orderTrackInfo['ordernum'],
                            'left_num' => $orderTrackInfo['left_num'],
                        ];
                        break;
                    case 5:
                        $checkedArr[] = [
                            'ordernum' => $orderTrackInfo['ordernum'],
                            'left_num' => $orderTrackInfo['left_num'],
                        ];
                        break;
                }
            }
        }

        return [$orderArr, $editArr, $checkedArr, $cancelArr];
    }

    /**
     * 推送订单数据到保定文旅
     * <AUTHOR>
     */
    public function pushDataToBaoDing()
    {
        // 获取需要对接到对方平台的景区数据
        $model = new GovernmentDataSys();

        $field   = 'id,apply_id,land_code,land_key';
        $govList = $model->getGdListByType(GovernmentDataSys::GOVERNMENT_SYS_TYPE_BD, $field);
        if (empty($govList)) {
            pft_log('government/BD/error', '获取对应配置列表为空, sql:' . $model->getLastSql());

            return;
        }

        $govListMap = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
        }

        // 获取gov id -> 获取government_data_attr
        $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $model->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/BD/error', '获取对应的data_attr表数据为空, sql:' . $model->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        // 获取对应的所有tid
        $tidArr        = [];
        $ticketInfoArr = array_column($govWithAttrListMap, 'land_ticket_info');
        foreach ($ticketInfoArr as $item) {
            $item   = json_decode($item, true);
            $tidArr = array_merge($tidArr, $item);
        }

        //门票为空时直接返回
        if (empty($tidArr)) {
            pft_log('government/BD/error', '门票为空,检查配置' . json_encode($govWithAttrListMap));

            return;
        }

        // 获取订单追踪记录
        $endTime   = date('Y-m-d H:i:s', time());
        $beginTime = date('Y-m-d H:i:s', strtotime('-1 minutes'));

        $orderTool = new OrderTools();
        //$orderTrack = new OrderTrack();
        $actionArr = [0, 1, 2, 5];
        $where     = [
            'insertTime' => ['between', [$beginTime, $endTime]],
            'action'     => ['in', [0, 1, 2, 5]],
            'tid'        => ['in', $tidArr],
        ];

        $queryParams = [$endTime, $beginTime, false, $actionArr, $tidArr];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderTrack', 'queryOrderTrackCountByInsertTime',
            $queryParams);
        $total       = 0;
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $total = $queryRes['data'];
        }
        //$total      = $orderTrack->getTotal($where);

        //记录下日志
        pft_log('government/BD/log', '总条数为=' . json_encode($total) . '查询条件=' . json_encode($where));

        $pageSize = 20;
        for ($page = 1; $page <= ceil($total / $pageSize); $page++) {
            // 获取最终数据记录
            //$list        = $orderTrack->getList($where, 'ordernum, action, left_num, tnum', $page, $pageSize);

            //订单查询迁移二期
            $orderTrackQueryLib = new OrderTrackQuery();
            $list               = $orderTrackQueryLib->getOrderRecordNew($page, $pageSize, $where);

            $ordernumArr = array_unique(array_column($list, 'ordernum'));

            $reservationList = $model->getHebeiTimeShareReservationByOrdernumArr($ordernumArr,
                'id, ordernum, reservation_id, reservation_status');
            $reservationMap  = [];
            foreach ($reservationList as $item) {
                $reservationMap[$item['ordernum']] = $item;
            }

            // 获取订单信息
            $orderInfoList = $orderTool->getOrderInfo($ordernumArr,
                'member, ordernum, ordername, ordertel, lid, ordertime, tnum, tprice, totalmoney, personid ',
                'ext_content');
            $orderInfoList = json_decode($orderInfoList, true);
            $orderInfoList = $orderInfoList['data'];
            $orderInfoMap  = [];
            foreach ($orderInfoList as $item) {
                $orderInfoMap[$item['ordernum']] = $item;
            }

            foreach ($list as $item) {
                $orderInfoTmp = $orderInfoMap[$item['ordernum']];
                $govInfoTmp   = $govWithAttrListMap[$orderInfoTmp['lid']];  //订单信息中的lid 与配置项的land_id 相同时将订单与配置进行关联

                //退票或检票操作后留下来的数量
                $totalnum = $item['tnum'] + $item['left_num'];

                list($appid, $appSecret) = explode('|', $govInfoTmp['land_key']);

                $bizContent = [
                    'body'     => [
                        [
                            'id'                 => time() . rand(1, 10000000),
                            //给对方的id，不明确这里要传什么
                            'order_detail_no'    => $orderInfoTmp['ordernum'],
                            //这两个订单号就传一样的
                            'order_no'           => $orderInfoTmp['ordernum'],
                            //这两个订单号就传一样的
                            'order_type'         => $govInfoTmp['resource_type'],
                            //对方只要数字
                            'order_status'       => 'valid',
                            // 订单都是有效的
                            'scenic_code'        => $govInfoTmp['land_code'],
                            //对方的景区code ，用来制作签名 和通过验证用的
                            'scenic_name'        => $govInfoTmp['goods_name'],
                            // 写死， 也可以从数据库获取
                            'goods_code'         => $govInfoTmp['goods_code'],
                            // 数据库配置项之一  产品编码
                            'goods_name'         => $govInfoTmp['goods_name'],
                            // 数据库配置项之一  产品名称
                            'distributor_code'   => $govInfoTmp['distributor_code'],
                            // 数据库配置项之一  分销商编码
                            'distributor_name'   => $govInfoTmp['distributor_name'],
                            // 数据库配置项之一  分销商名称
                            'quantity'           => intval($orderInfoTmp['tnum']),
                            // 票的总数
                            'checked_quantity'   => 0,
                            // 这两个值暂时都为0， 在下面具体退票，检票操作发送的时候再进行修改
                            'refunded_quantity'  => 0,
                            //这两个值暂时都为0， 在下面具体退票，检票操作发送的时候再进行修改
                            'unchecked_quantity' => intval($orderInfoTmp['tnum']),
                            //刚下单时的未检的票等于总数
                            'price'              => $orderInfoTmp['tprice'] / 100,
                            //价格 string类型的  除以100
                            'total_price'        => $orderInfoTmp['totalmoney'] / 100,
                            //价格 string类型的  除以100
                            'link_name'          => $orderInfoTmp['ordername'],
                            //联系人 (对方接口可以为空)
                            'link_cert_no'       => $orderInfoTmp['personid'],
                            //身份证 (对方接口可以为空)
                            'link_tel'           => $orderInfoTmp['ordertel'],
                            //电话 (对方接口可以为空)
                            'create_time'        => date("Y-m-d H:i:s", time()),
                            //创建时间
                            'modify_time'        => date("Y-m-d H:i:s", time()),
                            //修改时间 对方说跟创建时间一样即可
                        ],
                    ],
                    'parkCode' => $govInfoTmp['land_code'],
                ];

                // 判断action
                switch ($item['action']) {
                    case 0:
                        // 下单
                        if (!isset($reservationMap[$item['ordernum']])) {

                            $bizContent = json_encode($bizContent);
                            $res        = $this->getBaoDingSisnAndSend($bizContent, $appid, $appSecret);
                            pft_log('government/BD/log', '本次推送的结果 = ' . $res);
                            $res = json_decode($res, true);

                            if ($res['code'] === '10000') {
                                $model->addHebeiTimeShareReservation([
                                    'ordernum'    => $item['ordernum'],
                                    'create_time' => time(),
                                    'update_time' => time(),
                                ]);
                            }

                        }
                        break;
                    case 2:
                        if (isset($reservationMap[$item['ordernum']])) {

                            $bizContent['body'][0]['quantity']           = $totalnum;                      //退票操作， 退票前的总数
                            $bizContent['body'][0]['refunded_quantity']  = intval($item['tnum']);          //退票操作， 这里改变要退票的数量
                            $bizContent['body'][0]['unchecked_quantity'] = intval($item['left_num']);      //退票操作， 这里改变未检票的数量
                            $bizContent                                  = json_encode($bizContent);
                            $res                                         = $this->getBaoDingSisnAndSend($bizContent,
                                $appid, $appSecret);

                            pft_log('government/BD/log', '本次退票的结果 = ' . $res);
                        }
                        // 取消
                        break;
                    case 5:
                        if (isset($reservationMap[$item['ordernum']])) {

                            $bizContent['body'][0]['quantity']           = $totalnum;                      //检票操作， 检票前的总数
                            $bizContent['body'][0]['checked_quantity']   = intval($item['tnum']);          //检票操作， 这里改变要检票的数量
                            $bizContent['body'][0]['unchecked_quantity'] = intval($item['left_num']);      //检票操作， 这里改变未检票的数量
                            $bizContent                                  = json_encode($bizContent);
                            $res                                         = $this->getBaoDingSisnAndSend($bizContent,
                                $appid, $appSecret);

                            pft_log('government/BD/log', '本次检票的结果 = ' . $res);

                        }
                        // 验证
                        break;
                }
            }
        }
    }

    public function getBaoDingSisnAndSend($bizContent, $appid, $appsecret)
    {
        // 获取uuid
        $chars = md5(uniqid(mt_rand(), true));
        $uuid  = substr($chars, 0, 8) . '-'
                 . substr($chars, 8, 4) . '-'
                 . substr($chars, 12, 4) . '-'
                 . substr($chars, 16, 4) . '-'
                 . substr($chars, 20, 12);

        $appidv      = 'appId' . $appid;
        $bizContentv = 'bizContent' . $bizContent;
        $namev       = 'name' . 'push.bigdata.third.orderInfo';
        $requestidv  = 'requestId' . $uuid;
        $timestamp   = 'timestamp' . time();
        $versionv    = 'version' . '1.0';
        $all         = $appidv . $bizContentv . $namev . $requestidv . $timestamp . $versionv;
        $sign        = strtoupper(md5($appsecret . $all . $appsecret));

        $postdata = [
            'requestId'  => $uuid,
            'appId'      => $appid,
            'bizContent' => $bizContent,
            'name'       => 'push.bigdata.third.orderInfo',
            'sign'       => $sign,
            'version'    => '1.0',
            'timestamp'  => time(),
        ];
        pft_log('government/BD/log', '本次推送的数据 = ' . json_encode($postdata));

        return $this->doCurl('http://apiesb.zhiyoubao.com/push.bigdata.third.orderInfo', $postdata);
    }

    public function doCurl($url, $data = [], $timeout = 30)
    {
        if (empty($url)) {
            return json_encode(['code' => 203, 'msg' => '缺少请求地址', 'data' => []]);
        }

        // 要求对方返回json值
        $headerArr = ['Content-Type: application/x-www-form-urlencoded;'];
        $data      = http_build_query($data);
        $response  = curl_post($url, $data, 80, $timeout, 'api/curl_post', $headerArr, true);
        if (isset($response['status']) && $response['status'] == false) {
            // 请求对方错误
            return json_encode(['code' => $response['code'], 'msg' => $response['err_msg'], 'data' => []]);
        } else {
            return $response;
        }
    }

    /**
     * 推送下单数据到绵阳文旅
     * <AUTHOR>
     */
    public function pushDataToMianYang()
    {

        $Date       = date('Y-m-d', time());
        $ticket     = new TicketApi();
        $soldTicket = $ticket->getCalendarPriceAndStorage(446501, $Date, $Date);
        pft_log("government/mianyang/log", "本次查询的数据为" . json_encode(["data" => $soldTicket, "time" => time()]));
        $access_appid = 'iyu8rvycd0kh';                    // id
        $access_key   = 'HNRhgxf6lcgqIoxStnsUAycQyN3aOR';  // key
        $resourceCode = 'A51070047866';                    //资源编码
        $resourceType = 'CONTENT_TYPE_SCENERY';            //资源类型      （上线前跟景区协商）
        $maxBear      = '9000';                            //景区最大承载量 （上线前跟景区协商）
        $time         = time();

        $data = [
            'date'         => date('Y-m-d', $time),
            //日期(yyyy-MM-dd)
            'orderNum'     => isset($soldTicket[0]['soldStorage']) ? $soldTicket[0]['soldStorage'] : 0,
            //当天截至当前时间点的预约数量
            'stock'        => isset($soldTicket[0]['dayOfStorage']) ? $soldTicket[0]['dayOfStorage'] : -1,
            //剩余库存  -1 代表库存未知
            'resourceType' => $resourceType,
            //资源类型 CONTENT_TYPE_SCENERY(景区) CONTENT_TYPE_VENUE(文化场馆)
            'resourceCode' => $resourceCode,
            //资源编码（由对方景区提供）
            'totalStock'   => $maxBear,
            //景区最大承载量
        ];

        //按键值升序对数组进行排序
        ksort($data);

        //取值拼接
        $str = "";
        foreach ($data as $item) {
            $str .= $item;
        }

        //签名制作
        $access_signature = base64_encode(hash_hmac('sha256', $access_appid . $access_key . $time . $str, "", true));

        //请求头部信息
        $header = [
            'Content-Type:application/x-www-form-urlencoded',
            "ACCESS_APPID:{$access_appid}",
            "ACCESS_SIGNATURE:{$access_signature}",
            "ACCESS_TIMESTAMP:{$time}",
            "ACCESS_KEY:{$access_key}",
        ];

        $res = $this->doCurlToMianYang('http://218.89.178.88/api/mywgl/OrderStock', $data, $header);
        pft_log("government/mianyang/log",
            "本次推送数据" . json_encode(["data" => $data, "header" => $header, "res" => $res]));
    }

    /**
     * 推送入园数据到绵阳文旅
     * <AUTHOR>
     */
    public function pushEnterNumToMianYang()
    {
        $dataCollectionModel = new DataCollection();

        $timeNow  = date('Y-m-d', time()) . '00:00:00';  //今天开始时间  0点0分0秒
        $timeHour = date('Y-m-d H:i:s', time());          //今天结束时间  现在

        $enterDataRes = $dataCollectionModel->getEnterData($timeNow, $timeHour, [163675, 113363]);//通过七曲山的景区id获取当天入园信息

        if (!isset($enterDataRes[0]["cnt"]) || $enterDataRes[0]["cnt"] == null) {
            $enterDataRes[0]["cnt"] = 0;
        }

        $access_appid = 'iyu8rvycd0kh';                    // id
        $access_key   = 'HNRhgxf6lcgqIoxStnsUAycQyN3aOR';  // key
        $resourceCode = 'A51070047866';                    //资源编码
        $resourceType = 'CONTENT_TYPE_SCENERY';            //资源类型      （上线前跟景区协商）
        $time         = time();

        $data = [
            'date'         => date('Y-m-d', $time),     //日期(yyyy-MM-dd)
            'currNum'      => -1,                              //当前园区游客人数，无法获取取值 -1
            'total'        => intval($enterDataRes[0]["cnt"]), //入园游客总数
            'resourceType' => $resourceType,                   //资源类型 CONTENT_TYPE_SCENERY(景区) CONTENT_TYPE_VENUE(文化场馆)
            'resourceCode' => $resourceCode,                   //资源编码（由对方景区提供）
        ];

        //按键值升序对数组进行排序
        ksort($data);

        //取值拼接
        $str = "";
        foreach ($data as $item) {
            $str .= $item;
        }

        //签名制作
        $access_signature = base64_encode(hash_hmac('sha256', $access_appid . $access_key . $time . $str, "", true));

        $header = [
            'Content-Type:application/x-www-form-urlencoded',
            "ACCESS_APPID:{$access_appid}",
            "ACCESS_SIGNATURE:{$access_signature}",
            "ACCESS_TIMESTAMP:{$time}",
            "ACCESS_KEY:{$access_key}",
        ];

        $res = $this->doCurlToMianYang('http://218.89.178.88/api/mywgl/PeopleNum', $data, $header);
        pft_log("government/mianyang/log",
            "本次推送数据" . json_encode(["data" => $data, "header" => $header, "res" => $res]));
    }

    /**
     * 绵阳文旅的Curl请求
     * <AUTHOR>
     */
    public function doCurlToMianYang($url, $data = [], $header = [], $timeout = 30)
    {
        if (empty($url)) {
            return json_encode(['code' => 203, 'msg' => '缺少请求地址', 'data' => []]);
        }

        $data     = http_build_query($data);
        $response = curl_post($url, $data, 12301, $timeout, 'api/curl_post', $header);
        if (isset($response['status']) && $response['status'] == false) {
            // 请求对方错误
            return json_encode(['code' => $response['code'], 'msg' => $response['err_msg'], 'data' => []]);
        } else {
            return $response;
        }
    }

    /**
     * /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/GovernmentDataPush pushDataForChongQing
     * 重庆文旅对接 预约/票务数据每10分钟定时上传（批量数据）
     * <AUTHOR>
     */
    public function pushDataForChongQing()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel        = new GovernmentDataSys();
        $model           = new GovernmentTouristStatistics();
        $orderQueryModel = new \Model\Order\OrderQuery();
        $field           = 'id,apply_id,land_code,land_key';
        $govList         = $govModel->getGdListByType(GovernmentDataSys::GOVERNMENT_SYS_TYPE_CQ, $field);
        if (empty($govList)) {
            pft_log('government/cqq/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/cqq/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

//        从景区id获取票id
        foreach ($govWithAttrListMap as $landItem) {
            list($appKey, $appSecret) = explode('|', $landItem['land_key']);
            $resourceCode = $landItem['land_code'];
            $fid          = $landItem['apply_id'];
            $landArr      = json_decode($landItem['land_id'], true);
            // 获取毫秒时间戳
            list($msec, $sec) = explode(' ', microtime());
            $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

            // 32位随机字符串
            $chars = md5(uniqid(mt_rand(), true));

            //初始化数组的部分值
            $pushData = [
                'appKey'   => $appKey,                            //广电云颁发的Key
                'nonceStr' => $chars,                             //32位随机串
                'ts'       => $msectime,                          //13位时间戳
                'syncTime' => date('YmdHi'),               //同步时刻:yyyyMMddHHmm 如：201909011110
            ];

            //查询限制条件  当天近10分钟该供应商下的数据
            $filter = [
                'date'        => date('Ymd'),
                'update_time' => array('between', [strtotime('-10 minutes'), time()]),
                'fid'         => $fid,
                'lid'         => ['in', $landArr]
            ];

            //根据条件找寻报表里的订单号
            $res                  = $model->getReserInfos($filter, 'order_ticket,cost_money,orders_info');
            $orderNumWithMoneyMap = [];
            $orderIdArr           = [];
            if (!empty($res)) {
                foreach ($res as $OrderItem) {
                    if (empty($OrderItem['orders_info'])) {
                        continue;
                    }
                    $orderIdList = json_decode($OrderItem['orders_info'], true);
                    foreach ($orderIdList as $ListItem) {
                        $orderNumWithMoneyMap[$ListItem[0]] = $OrderItem['cost_money'] / $OrderItem['order_ticket'];
                        $orderIdArr[]                       = $ListItem[0];
                    }
                }
            }

            $queryParams    = [$orderIdArr];
            $queryRes       = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo',
                'queryOrderTouristInfoByOrderId', $queryParams);
            $touristInfoArr = [];
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $touristInfoArr = $queryRes['data'];
            }
            //$touristInfoArr = $orderQueryModel->getTouristList($orderIdArr);

            if (!empty($touristInfoArr)) {
                foreach ($touristInfoArr as $tourItem) {
                    if (!empty($tourItem)) {
                        if ($tourItem['voucher_type'] == "1") {
                            $pushData['records'][] = [
                                'resourceCode'   => $resourceCode,
                                //广电云颁发的景区编码
                                'touristsDate'   => date('Ymd'),
                                //预约/票务日期
                                'touristsTime'   => '09001800',
                                //预约/票务时间段
                                'idType'         => 'ID_CARD',
                                //证件类型
                                'touristsFrom'   => substr($tourItem['idcard'], 0, 6),
                                //身份证前6位代表区域
                                'touristsBorn'   => substr($tourItem['idcard'], 6, 4),
                                //身份证上面的出生年份
                                'touristsGender' => (($tourItem['idcard'][16] % 2 == "0")) ? "2" : "1",
                                //身份证第17位偶数女性 奇数男性
                                'touristsType'   => 'TOURIST',
                                //游客类别
                                'ticketFee'      => $orderNumWithMoneyMap[$tourItem['orderid']],
                                //看一下取不取的到,取不到就为0吧
                                'dataFrom'       => 1,
                                //数据来源类别 1：票务系统 2：预约系统
                                'id'             => $tourItem['idcard']
                                //游客表自己给自己看的
                            ];
                        } else {
                            $pushData['records'][] = [
                                'resourceCode' => $resourceCode,
                                //广电云颁发的景区编码
                                'touristsDate' => date('Ymd'),
                                //预约/票务日期
                                'touristsTime' => '09001800',
                                //预约/票务时间段
                                'idType'       => 'OTHER',
                                //证件类型  没有身份证信息的话就为其他
                                'touristsType' => 'TOURIST',
                                //游客类别
                                'ticketFee'    => $orderNumWithMoneyMap[$tourItem['orderid']],
                                //看一下取不取的到,取不到就为0吧
                                'dataFrom'     => 1,
                                //数据来源类别 1：票务系统 2：预约系统
                                'id'           => $tourItem['orderid']
                                //游客表自己给自己看的
                            ];
                        }
                    }
                }
            }
            if (empty($pushData['records'])) {
                continue; //如果没有数据的话 跳出本次循环
            }
            $pushData['records'] = json_encode($pushData['records']);
            $pushData['sign']    = $this->genSignForChongQing($pushData, $appSecret);
            $sendres             = $this->doCurlForCQ($this->cqUrl . '/api/rftcloud/appoinment/openapi/report/appointRecords',
                json_encode($pushData));
            pft_log("government/cqq/data",
                json_encode(['push' => $pushData, 'res' => $sendres], JSON_UNESCAPED_UNICODE));
            //使用完清空上一次的变量
            unset($pushData);
            unset($res);
            unset($touristInfoArr);
        }
    }

    /**
     * 重庆文旅对接 推送核销数据（10分钟）
     * <AUTHOR>
     */
    public function pushCheckForChongQing()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel = new GovernmentDataSys();
        $model    = new GovernmentTouristStatistics();

        $field   = 'id,apply_id,land_code,land_key';
        $govList = $govModel->getGdListByType(GovernmentDataSys::GOVERNMENT_SYS_TYPE_CQ, $field);
        if (empty($govList)) {
            pft_log('government/cqq/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/cqq/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        if (empty($govAttrListMap)) {
            pft_log('government/cqq/error', json_encode(['value' => $value, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        foreach ($govWithAttrListMap as $landItem) {
            list($appKey, $appSecret) = explode('|', $landItem['land_key']);
            $resourceCode = $landItem['land_code'];
            $fid          = $landItem['apply_id'];
            $lidArr       = json_decode($landItem['land_id'], true);
            // 获取毫秒时间戳
            list($msec, $sec) = explode(' ', microtime());
            $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

            // 32位随机字符串
            $chars = md5(uniqid(mt_rand(), true));

            $pushData = [
                'appKey'       => $appKey,                            //广电云颁发的Key
                'nonceStr'     => $chars,                             //32位随机串
                'ts'           => $msectime,                          //13位时间戳
                'syncTime'     => date('YmdHi'),               //同步时刻:yyyyMMddHHmm 如：201909011110
                'resourceCode' => $resourceCode,                      //资源编码
                'checkDate'    => date('Ymd'),                 //核销日期
                'dataFrom'     => 1,                                  //数据来源
            ];

            $checkedNum = $model->getCheckedNumByFidAndLid($fid, $lidArr);
            pft_log('government/cqq/checksql', json_encode(['sql' => $model->getLastSql()], JSON_UNESCAPED_UNICODE));
            if (isset($checkedNum[0]['ckNum'])) {
                $pushData['checkSum'] = intval($checkedNum[0]['ckNum']);
            } else {
                $pushData['checkSum'] = 0;
            }
            $pushData['sign'] = $this->genSignForChongQing($pushData, $appSecret);
            $sendres          = $this->doCurlForCQ($this->cqUrl . '/api/rftcloud/appoinment/openapi/report/checkIn',
                json_encode($pushData));

            pft_log('government/cqq/check', json_encode(['push' => $pushData, 'res' => $sendres]));
            unset($res);
        }
    }

    /**
     * 重庆文旅对接 推送取消数据 10分钟一次
     * <AUTHOR>
     */
    public function pushCancelForChongQing()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel = new GovernmentDataSys();
        $model    = new GovernmentTouristStatistics();
        $ticket   = new Ticket();

        $field   = 'id,apply_id,land_code,land_key';
        $govList = $govModel->getGdListByType(GovernmentDataSys::GOVERNMENT_SYS_TYPE_CQ, $field);
        if (empty($govList)) {
            pft_log('government/cqq/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/cqq/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        if (empty($govAttrListMap)) {
            pft_log('government/cqq/error', json_encode(['value' => $value, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        foreach ($govWithAttrListMap as $landItem) {
            list($appKey, $appSecret) = explode('|', $landItem['land_key']);
            $resourceCode = $landItem['land_code'];
            $fid          = $landItem['apply_id'];
            $lidArr       = json_decode($landItem['land_id'], true);
            // 获取毫秒时间戳
            list($msec, $sec) = explode(' ', microtime());
            $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

            // 32位随机字符串
            $chars = md5(uniqid(mt_rand(), true));

            $pushData = [
                'appKey'       => $appKey,                            //广电云颁发的Key
                'nonceStr'     => $chars,                             //32位随机串
                'ts'           => $msectime,                          //13位时间戳
                'syncTime'     => date('YmdHi'),               //同步时刻:yyyyMMddHHmm 如：201909011110
                'resourceCode' => $resourceCode,                      //资源编码
                'cancelDate'   => date('Ymd'),                 //取消日期
                'dataFrom'     => 1,                                  //数据来源
            ];

            $canceledNum = $model->getCanceledNumByFidAndLid($fid, $lidArr);
            if (isset($canceledNum[0]['cancelNum'])) {
                $pushData['cancelSum'] = intval($canceledNum[0]['cancelNum']);
            } else {
                $pushData['cancelSum'] = 0;
            }
            $pushData['sign'] = $this->genSignForChongQing($pushData, $appSecret);
            $sendres          = $this->doCurlForCQ($this->cqUrl . '/api/rftcloud/appoinment/openapi/report/cancel',
                json_encode($pushData));

            pft_log('government/cqq/cancel',
                json_encode(['push' => $pushData, 'res' => $sendres], JSON_UNESCAPED_UNICODE));

            unset($tickets);
        }
    }

    /**
     * 重庆文旅对接 推送每日开放时段 每天1次
     * <AUTHOR>
     */
    public function pushTimeForChongQing()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel = new GovernmentDataSys();

        $field   = 'id,apply_id,land_code,land_key';
        $govList = $govModel->getGdListByType(GovernmentDataSys::GOVERNMENT_SYS_TYPE_CQ, $field);
        if (empty($govList)) {
            pft_log('government/cqq/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/cqq/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        if (empty($govAttrListMap)) {
            pft_log('government/cqq/error', json_encode(['value' => $value, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        foreach ($govWithAttrListMap as $landItem) {
            list($appKey, $appSecret) = explode('|', $landItem['land_key']);
            $resourceCode = $landItem['land_code'];

            // 获取毫秒时间戳
            list($msec, $sec) = explode(' ', microtime());
            $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

            // 32位随机字符串
            $chars = md5(uniqid(mt_rand(), true));

            if (isset($landItem['times'])) {
                $timeArr          = json_decode($landItem['times'], true);
                $startTime        = intval($timeArr[0]);
                $endTime          = intval($timeArr[1]);
                $pushData         = [
                    'appKey'       => $appKey,                            //广电云颁发的Key
                    'nonceStr'     => $chars,                             //32位随机串
                    'ts'           => $msectime,                          //13位时间戳
                    'syncTime'     => date('YmdHi'),               //同步时刻:yyyyMMddHHmm 如：201909011110
                    'resourceCode' => $resourceCode,                      //资源编码
                    'dataFrom'     => 1,                                  //数据来源
                    'day'          => intval(date('Ymd')),         //日期
                    'timeFrames'   => json_encode([
                            [
                                'startTime' => $startTime,
                                'endTime'   => $endTime,
                            ],
                        ]
                    ),
                ];
                $pushData['sign'] = $this->genSignForChongQing($pushData, $appSecret);
                $sendres          = $this->doCurlForCQ($this->cqUrl . '/api/rftcloud/appoinment/openapi/report/timeFrames',
                    json_encode($pushData));
                pft_log('government/cqq/times',
                    json_encode(['push' => $pushData, 'res' => $sendres], JSON_UNESCAPED_UNICODE));
                unset($pushData);
            }
        }
    }

    /**
     * 重庆文旅对接 月度预约/票务汇总上传接口 每月1号
     * <AUTHOR>
     */
    public function pushReportForChongQing()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel = new GovernmentDataSys();
        $model    = new GovernmentTouristStatistics();
        $ticket   = new Ticket();

        $field   = 'id,apply_id,land_code,land_key';
        $govList = $govModel->getGdListByType(GovernmentDataSys::GOVERNMENT_SYS_TYPE_CQ, $field);
        if (empty($govList)) {
            pft_log('government/cqq/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/cqq/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        if (empty($govAttrListMap)) {
            pft_log('government/cqq/error', json_encode(['value' => $value, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        foreach ($govWithAttrListMap as $landItem) {
            list($appKey, $appSecret) = explode('|', $landItem['land_key']);
            $resourceCode = $landItem['land_code'];
            $fid          = $landItem['apply_id'];
            $lidArr       = json_decode($landItem['land_id'], true);
            // 获取毫秒时间戳
            list($msec, $sec) = explode(' ', microtime());
            $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

            // 32位随机字符串
            $chars = md5(uniqid(mt_rand(), true));

            $pushData = [
                'appKey'       => $appKey,                                           //广电云颁发的Key
                'nonceStr'     => $chars,                                            //32位随机串
                'ts'           => $msectime,                                         //13位时间戳
                'syncTime'     => date('YmdHi'),                             //同步时刻:yyyyMMddHHmm 如：201909011110
                'resourceCode' => $resourceCode,                                    //资源编码
                'reportMonth'  => date('Ym', strtotime('-1 month')),    //月度 如201909
                'dataFrom'     => 1,                                                //数据来源
            ];

            if (!empty($fid)) {
                $totalNum = $model->getPeopleNum($fid, $lidArr);
                if (isset($totalNum[0]['orderNum']) && isset($totalNum[0]['cancelNum'])) {
                    $pushData['reportSum'] = $totalNum[0]['orderNum'] - $totalNum[0]['cancelNum'];
                } else {
                    $pushData['reportSum'] = 0; //没有的话就报0
                }
            } else {
                $pushData['reportSum'] = 0; //没有的话就报0
            }

            $pushData['sign'] = $this->genSignForChongQing($pushData, $appSecret);
            $sendres          = $this->doCurlForCQ($this->cqUrl . '/api/rftcloud/appoinment/openapi/report/month',
                json_encode($pushData));
            pft_log('government/cqq/Report',
                json_encode(['push' => $pushData, 'res' => $sendres], JSON_UNESCAPED_UNICODE));
            unset($tickets);
        }
    }

    /**
     * 重庆文旅对接 景区实时游客量接口 每10分钟
     * <AUTHOR>
     */
    public function pushrealReceptionForChongQing()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel            = new GovernmentDataSys();
        $model               = new GovernmentTouristStatistics();
        $ticket              = new Ticket();
        $dataCollectionModel = new DataCollection();

        $field   = 'id,apply_id,land_code,land_key';
        $govList = $govModel->getGdListByType(GovernmentDataSys::GOVERNMENT_SYS_TYPE_CQ, $field);
        if (empty($govList)) {
            pft_log('government/cqq/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/cqq/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        if (empty($govAttrListMap)) {
            pft_log('government/cqq/error', json_encode(['value' => $value, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        foreach ($govWithAttrListMap as $landItem) {
            list($appKey, $appSecret) = explode('|', $landItem['land_key']);
            $resourceCode = $landItem['land_code'];

            $landItem['land_id'] = json_decode($landItem['land_id']);

            // 获取毫秒时间戳
            list($msec, $sec) = explode(' ', microtime());
            $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

            // 32位随机字符串
            $chars = md5(uniqid(mt_rand(), true));

            $pushData = [
                'appKey'       => $appKey,                            //广电云颁发的Key
                'nonceStr'     => $chars,                             //32位随机串
                'ts'           => $msectime,                          //13位时间戳
                'syncTime'     => date('YmdHi'),               //同步时刻:yyyyMMddHHmm 如：201909011110
                'resourceCode' => $resourceCode,                      //资源编码
                'dataFrom'     => 1,                                  //数据来源
            ];

            $timeNow             = date('Y-m-d 00:00:01');
            $timeHour            = date('Y-m-d H:i:s');
            $enterDataRes        = $dataCollectionModel->getEnterData($timeNow, $timeHour, $landItem['land_id']);
            $leaveDateRes        = $dataCollectionModel->getLeaveData($timeNow, $timeHour, $landItem['land_id']);
            $enterNum            = $enterDataRes[0]['cnt'] ?? 0;
            $leaveNum            = $leaveDateRes[0]['cnt'] ?? 0;
            $pushData['realSum'] = $enterNum - $leaveNum;
            $pushData['sign']    = $this->genSignForChongQing($pushData, $appSecret);
            $sendres             = $this->doCurlForCQ($this->cqUrl . '/api/rftcloud/appoinment/openapi/report/realReception',
                json_encode($pushData));
            pft_log('government/cqq/totalPeopleNum',
                json_encode(['push' => $pushData, 'res' => $sendres], JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 重庆文旅对接平台签名制作
     * <AUTHOR>
     */
    public function genSignForChongQing($params, $appSecret)
    {
        //按键值升序对数组进行排序
        ksort($params);

        //取值拼接 如果有值为NULL，忽略该项
        $str = "";
        foreach ($params as $key => $item) {
            if (!isset($item)) {
                continue;
            } else {
                $str .= $key . "=" . $item . '&';
            }
        }

        //签名制作
        return strtoupper(hash_hmac('sha256', $str . 'appSecret=' . $appSecret, $appSecret));
    }

    public function doCurlForCQ($url, $data = [], $timeout = 30)
    {
        if (empty($url)) {
            return json_encode(['code' => 203, 'msg' => '缺少请求地址', 'data' => []]);
        }
        $headerArr = ['Content-Type: application/json;'];
        $response  = curl_post($url, $data, 80, $timeout, 'api/curl_post', $headerArr, true);
        if (isset($response['status']) && $response['status'] == false) {
            // 请求对方错误
            return json_encode(['code' => $response['code'], 'msg' => $response['err_msg'], 'data' => []]);
        } else {
            return $response;
        }
    }

    /**
     * 绵阳公安厅购票信息上传接口
     * <AUTHOR>
     */
    public function pushDataForMyPolice()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel        = new GovernmentDataSys();
        $govTourModel    = new GovernmentTouristStatistics();
        $orderQueryModel = new \Model\Order\OrderQuery();

        $field   = 'id,apply_id,land_code,land_key,land_name';
        $govList = $govModel->getGdListByType(13, $field);
        if (empty($govList)) {
            pft_log('government/cqq/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/cqq/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        if (empty($govAttrListMap)) {
            pft_log('government/cqq/error', json_encode(['value' => $value, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        foreach ($govWithAttrListMap as $landItem) {
            //先调用一次心跳接口，防止上传失败
            $biz     = new \Business\Government\GovernmentDataPush();
            $beatRes = $biz->postDataMyPolice('beat', $landItem['land_code']);
            pft_log('government/myPolice/beat', json_encode(['beat' => $beatRes, 'time' => time()]));
            if ($beatRes != false) {
                $orderInfoArr = $govTourModel->getOrderInfosByLandId($landItem['land_id'], $landItem['apply_id']);
                if (!empty($orderInfoArr)) {
                    foreach ($orderInfoArr as $orderItem) {
                        if (!empty($orderItem['orders_info'])) {
                            $orders_infoArr = json_decode($orderItem['orders_info']);
                            if (!empty($orders_infoArr)) {
                                $orderIdArr         = array_column($orders_infoArr, '0');
                                $queryParams        = [$orderIdArr];
                                $queryRes           = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo',
                                    'queryOrderTouristInfoByOrderId', $queryParams);
                                $touristListInfoArr = [];
                                if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                                    $touristListInfoArr = $queryRes['data'];
                                }
                                //$touristListInfoArr = $orderQueryModel->getTouristList($orderIdArr);
                                $orderTouristListInfoArr = [];
                                foreach ($touristListInfoArr as $tourisItem) {
                                    $orderTouristListInfoArr[$tourisItem['orderid']][] = $tourisItem;
                                }

                                foreach ($orders_infoArr as $touristId) {
                                    //$touristInfoArr = $orderQueryModel->getTouristList($touristId[0]);
                                    $touristInfoArr = $orderTouristListInfoArr[$touristId[0]];
                                    foreach ($touristInfoArr as $touristItem) {
                                        if ($touristItem['voucher_type'] != 1) {
                                            continue;
                                        } else {
                                            list($reDataId, $ckDataId) = explode('|', $landItem['land_key']);
                                            $postdata = [
                                                'DDBH'    => $touristItem['orderid'],               //订单编号
                                                'GPSJ'    => date('Y-m-d H:i:s'),            //购票时间
                                                'GPRXM'   => $touristItem['tourist'] ?? "",         //姓名
                                                'GPRZJHM' => $touristItem['idcard'],                //购票人身份证号码
                                                'GPRLXDH' => $touristItem['mobile'] ?? "",          //购票人联系电话
                                                'JDMC'    => $landItem['land_name'],                //景点名称
                                                'JDDZ'    => $landItem['land_addr'],                //景点地址
                                            ];
                                            $pushRes  = $biz->postDataMyPolice('reserve', $landItem['land_code'],
                                                $reDataId,
                                                $postdata);
                                            pft_log('government/myPolice/push',
                                                json_encode(['push' => $pushRes, 'time' => time()]));
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    continue;
                }
            }
        }
    }

    /**
     * 绵阳公安厅核销信息上传接口
     * <AUTHOR>
     */
    public function pushCheckForMyPolice()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel        = new GovernmentDataSys();
        $govTourModel    = new GovernmentTouristStatistics();
        $orderQueryModel = new \Model\Order\OrderQuery();

        $field   = 'id,apply_id,land_code,land_key,land_name';
        $govList = $govModel->getGdListByType(13, $field);
        if (empty($govList)) {
            pft_log('government/cqq/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/cqq/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        if (empty($govAttrListMap)) {
            pft_log('government/cqq/error', json_encode(['value' => $value, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        foreach ($govWithAttrListMap as $landItem) {
            //先调用一次心跳接口，防止上传失败
            $biz     = new \Business\Government\GovernmentDataPush();
            $beatRes = $biz->postDataMyPolice('beat', $landItem['land_code']);
            if ($beatRes != false) {
                $orderInfoArr = $govTourModel->getCheckInfosByLandId($landItem['land_id'], $landItem['apply_id']);
                if (!empty($orderInfoArr)) {
                    foreach ($orderInfoArr as $orderItem) {
                        if (!empty($orderItem['orders_info'])) {
                            $orders_infoArr = json_decode($orderItem['orders_info']);
                            if (!empty($orders_infoArr)) {
                                $orderIdArr         = array_column($orders_infoArr, '0');
                                $queryParams        = [$orderIdArr];
                                $queryRes           = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo',
                                    'queryOrderTouristInfoByOrderId', $queryParams);
                                $touristListInfoArr = [];
                                if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                                    $touristListInfoArr = $queryRes['data'];
                                }
                                //$touristListInfoArr = $orderQueryModel->getTouristList($orderIdArr);
                                $orderTouristListInfoArr = [];
                                foreach ($touristListInfoArr as $tourisItem) {
                                    $orderTouristListInfoArr[$tourisItem['orderid']][] = $tourisItem;
                                }

                                foreach ($orders_infoArr as $touristId) {
                                    //$touristInfoArr = $orderQueryModel->getTouristList($touristId[0]);
                                    $touristInfoArr = $orderTouristListInfoArr[$touristId[0]];
                                    foreach ($touristInfoArr as $touristItem) {
                                        if ($touristItem['voucher_type'] != 1) {
                                            continue;
                                        } else {
                                            list($reDataId, $ckDataId) = explode('|', $landItem['land_key']);
                                            $postdata = [
                                                'GPRXM'   => $touristItem['tourist'] ?? "",         //姓名
                                                'GPRZJHM' => $touristItem['idcard'],                //购票人身份证号码
                                                'GPRLXDH' => $touristItem['mobile'] ?? "",          //购票人联系电话
                                                'JDMC'    => $landItem['land_name'],                //景点名称
                                                'JDDZ'    => $landItem['land_addr'],                //景点地址
                                                'JPSJ'    => date('Y-m-d H:i:s', $orderItem['update_time']),//检票时间
                                            ];

                                            $pushRes = $biz->postDataMyPolice('check', $landItem['land_code'],
                                                $ckDataId,
                                                $postdata);
                                            pft_log('government/myPolice/check',
                                                json_encode(['push' => $pushRes, 'time' => date('Y-m-d H:i:s')]));
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    continue;
                }
            }
        }
    }

    /**
     * php  /var/www/html/Service/Crontab/runNew.php  Report/GovernmentDataPush pushDataForZiGongPolice
     * 自贡公安厅购票信息上传接口
     * <AUTHOR>
     */
    public function pushDataForZiGongPolice()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel        = new GovernmentDataSys();
        $govTourModel    = new GovernmentTouristStatistics();
        $orderQueryModel = new \Model\Order\OrderQuery();

        $field   = 'id,apply_id,land_code,land_key,land_name';
        $govList = $govModel->getGdListByType(17, $field);
        if (empty($govList)) {
            pft_log('government/cqq/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/cqq/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        if (empty($govAttrListMap)) {
            pft_log('government/cqq/error', json_encode(['value' => $value, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        foreach ($govWithAttrListMap as $landItem) {
            //先调用一次心跳接口，防止上传失败
            $biz     = new \Business\Government\GovernmentDataPush();
            $beatRes = $biz->postDataZiGongPolice('beat', $landItem['land_code']);
            pft_log('government/ZiGongPolice/beat',
                json_encode(['beat' => $beatRes, 'landCode' => $landItem['land_code'], 'time' => time()]));
            if ($beatRes != false) {
                $orderInfoArr = $govTourModel->getOrderInfosByLandId($landItem['land_id'], $landItem['apply_id']);
                if (!empty($orderInfoArr)) {
                    foreach ($orderInfoArr as $orderItem) {
                        if (!empty($orderItem['orders_info'])) {
                            $orders_infoArr = json_decode($orderItem['orders_info']);
                            if (!empty($orders_infoArr)) {
                                $orderIdArr         = array_column($orders_infoArr, '0');
                                $queryParams        = [$orderIdArr];
                                $queryRes           = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo',
                                    'queryOrderTouristInfoByOrderId', $queryParams);
                                $touristListInfoArr = [];
                                if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                                    $touristListInfoArr = $queryRes['data'];
                                }
                                //$touristListInfoArr = $orderQueryModel->getTouristList($orderIdArr);
                                $orderTouristListInfoArr = [];
                                foreach ($touristListInfoArr as $tourisItem) {
                                    $orderTouristListInfoArr[$tourisItem['orderid']][] = $tourisItem;
                                }

                                foreach ($orders_infoArr as $touristId) {
                                    //$touristInfoArr = $orderQueryModel->getTouristList($touristId[0]);
                                    $touristInfoArr = $orderTouristListInfoArr[$touristId[0]];
                                    foreach ($touristInfoArr as $touristItem) {
                                        if ($touristItem['voucher_type'] != 1) {
                                            continue;
                                        } else {
                                            list($reDataId, $ckDataId) = explode('|', $landItem['land_key']);
                                            $postdata = [
                                                'DDBH'    => $touristItem['orderid'],               //订单编号
                                                'GPSJ'    => date('Y-m-d H:i:s'),            //购票时间
                                                'GPRXM'   => $touristItem['tourist'] ?? "",         //姓名
                                                'GPRZJHM' => $touristItem['idcard'],                //购票人身份证号码
                                                'GPRLXDH' => $touristItem['mobile'] ?? "",          //购票人联系电话
                                                'JDMC'    => $landItem['land_name'],                //景点名称
                                                'JDDZ'    => $landItem['land_addr'],                //景点地址
                                            ];
                                            $pushRes  = $biz->postDataZiGongPolice('reserve', $landItem['land_code'],
                                                $reDataId,
                                                $postdata);
                                            pft_log('government/ZiGongPolice/push',
                                                json_encode([
                                                    'push'     => $pushRes,
                                                    'postData' => $postdata,
                                                    'time'     => time(),
                                                ]));
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    continue;
                }
            }
        }
    }

    /**
     * 自贡公安厅核销信息上传接口
     * <AUTHOR>
     */
    public function pushCheckForZiGongPolice()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel        = new GovernmentDataSys();
        $govTourModel    = new GovernmentTouristStatistics();
        $orderQueryModel = new \Model\Order\OrderQuery();

        $field   = 'id,apply_id,land_code,land_key,land_name';
        $govList = $govModel->getGdListByType(17, $field);
        if (empty($govList)) {
            pft_log('government/cqq/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/cqq/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        if (empty($govAttrListMap)) {
            pft_log('government/cqq/error', json_encode(['value' => $value, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        foreach ($govWithAttrListMap as $landItem) {
            //先调用一次心跳接口，防止上传失败
            $biz     = new \Business\Government\GovernmentDataPush();
            $beatRes = $biz->postDataZiGongPolice('beat', $landItem['land_code']);
            if ($beatRes != false) {
                $orderInfoArr = $govTourModel->getCheckInfosByLandId($landItem['land_id'], $landItem['apply_id']);
                if (!empty($orderInfoArr)) {
                    foreach ($orderInfoArr as $orderItem) {
                        if (!empty($orderItem['orders_info'])) {
                            $orders_infoArr = json_decode($orderItem['orders_info']);
                            if (!empty($orders_infoArr)) {
                                $orderIdArr         = array_column($orders_infoArr, '0');
                                $queryParams        = [$orderIdArr];
                                $queryRes           = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo',
                                    'queryOrderTouristInfoByOrderId', $queryParams);
                                $touristListInfoArr = [];
                                if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                                    $touristListInfoArr = $queryRes['data'];
                                }
                                //$touristListInfoArr = $orderQueryModel->getTouristList($orderIdArr);
                                $orderTouristListInfoArr = [];
                                foreach ($touristListInfoArr as $tourisItem) {
                                    $orderTouristListInfoArr[$tourisItem['orderid']][] = $tourisItem;
                                }

                                foreach ($orders_infoArr as $touristId) {
                                    //$touristInfoArr = $orderQueryModel->getTouristList($touristId[0]);
                                    $touristInfoArr = $orderTouristListInfoArr[$touristId[0]];
                                    foreach ($touristInfoArr as $touristItem) {
                                        if ($touristItem['voucher_type'] != 1) {
                                            continue;
                                        } else {
                                            list($reDataId, $ckDataId) = explode('|', $landItem['land_key']);
                                            $postdata = [
                                                'GPRXM'   => $touristItem['tourist'] ?? "",         //姓名
                                                'GPRZJHM' => $touristItem['idcard'],                //购票人身份证号码
                                                'GPRLXDH' => $touristItem['mobile'] ?? "",          //购票人联系电话
                                                'JDMC'    => $landItem['land_name'],                //景点名称
                                                'JDDZ'    => $landItem['land_addr'],                //景点地址
                                                'JPSJ'    => date('Y-m-d H:i:s', $orderItem['update_time']),//检票时间
                                            ];

                                            $pushRes = $biz->postDataZiGongPolice('check', $landItem['land_code'],
                                                $ckDataId,
                                                $postdata);
                                            pft_log('government/myPolice/check',
                                                json_encode(['push' => $pushRes, 'postData' => $postdata, 'time' => date('Y-m-d H:i:s')]));
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    continue;
                }
            }
        }
    }

    /**
     * 杭州七里扬帆数据上报
     * <AUTHOR>
     */
    public function pushDataForHz()
    {
        $dataCollectionModel = new DataCollection();
        $memberModel         = new GovernmentTouristStatistics();
        $redis               = Cache::getInstance('redis');        //redis
        $beginTime           = date('Y-m-d 00:00:01');           //当天开始时间
        $endTime             = date('Y-m-d H:i:s', time());      //当天结束时间
        $date                = date('Y-m-d');                    //当天时间

        //当天入园人数收集
        $checkinNumArr = $dataCollectionModel->getEnterData($beginTime, $endTime, [
            84225,
            84094,
            83678,
            105499,
            117394,
            117395,
            117396,
            117397,
            117538,
            117539,
            117540,
            117541,
            117542,
            117543,
            113936,
            118727,
            128376,
            128376,
            127711,
            118728,
            117545,
            117537,
            117401,
            117544,
            118721,
            118730,
            118731,
        ]);

        if (!empty($checkinNumArr[0]['cnt'])) {
            $checkinNum = $checkinNumArr[0]['cnt'];
        } else {
            $checkinNum = 0;
        }

        //当天纸质入园人数(通过手持机和闸机验证的)
        //$filter = [
        //    'date'    => date('Ymd'),
        //    'lid'     => [
        //        'in',
        //        [
        //            84225,
        //            84094,
        //            83678,
        //            105499,
        //            117394,
        //            117395,
        //            117396,
        //            117397,
        //            117538,
        //            117539,
        //            117540,
        //            117541,
        //            117542,
        //            117543,
        //            113936,
        //            118727,
        //            128376,
        //            128376,
        //            127711,
        //            118728,
        //            117545,
        //            117537,
        //            117401,
        //            117544,
        //            118721,
        //            118730,
        //            118731,
        //        ],
        //    ],
        //    'fid'     => 1001766,
        //    'channel' => ['in', [10, 15]],
        //];
        //
        //$field = 'sum(order_ticket) as enterNum';
        //
        //$paperEnNumArr = $memberModel->getEnterNum($filter, $field);
        //if (!empty($paperEnNumArr[0]['enterNum'])) {
        //    $paperEnNum = $paperEnNumArr[0]['enterNum'];
        //} else {
        //    $paperEnNum = 0;
        //}

        //确定路径是否存在
        if (!is_dir("/alidata/download_data/report_data/government_report/hz/")) {
            mkdir("/alidata/download_data/report_data/government_report/hz/", 0777, true);
        }
        //生成csv文件并插入数据
        $fp = fopen("/alidata/download_data/report_data/government_report/hz/{$date}.csv", 'a');
        if (!$fp) {
            chmod("/alidata/download_data/report_data/government_report/hz/", 0755);
            $fp = fopen("/alidata/download_data/report_data/government_report/hz/{$date}.csv", 'a');
        }

        $csv_body = [
            [$checkinNum, 0, date('Y-m-d H:i:s'), '七里扬帆', '建德市', '运行中', ''],
        ];
        $content  = '';
        foreach ($csv_body as $k => $v) {
            $content .= implode(',', $v) . PHP_EOL;
        }

        //当天首次插入数据时生成表头
        $res = $redis->get("hz:{$date}");
        if (!$res) {
            $csv_header = ['checkin_num', 'ticket_num', 'date_str', 'scenic', 'region', 'status', 'description'];
            $header     = implode(',', $csv_header) . PHP_EOL;
            $csv        = $header . $content;
            $redis->set("hz:{$date}", "x", '', 3600 * 24);
        } else {
            $csv = $content;
        }
        fwrite($fp, $csv);
        fclose($fp);

        //文件准备完成准备发送
        $sn          = md5("piaofutong" . "bianjie_scenic_point" . "e146e8104bb24adee92f66c75fdd60eb" . time());
        $ts          = time();
        $url         = "http://hzlw.maicedata.com/cdl_sink/uploaddata/partner/piaofutong/data/bianjie_scenic_point?ts={$ts}&sn={$sn}&city=330100";
        $http_header = ['Content-Type:text/plain;charset=utf-8'];
        $post_data   = realpath("/alidata/download_data/report_data/government_report/hz/{$date}.csv");
        $file        = file_get_contents($post_data);
        $res         = curl_post($url, $file, 80, 25, '/api/curl_post', $http_header);
        pft_log('government/hz/report',
            json_encode(['res' => $res, 'cn' => $checkinNum, 'pn' => 0], JSON_UNESCAPED_UNICODE));

        //如果当前时间小于当天的 1点，删除过期文件
        if (time() < strtotime(date('Y-m-d 01:00:00'))) {
            //删除非当天的文件
            if (is_dir('/alidata/download_data/report_data/government_report/hz/')) {
                //判断是否打开成功
                if ($handle = opendir('/alidata/download_data/report_data/government_report/hz/')) {
                    //读取文件
                    while ($file = readdir($handle)) {
                        if ($file != '.' && $file != '..') {
                            if ($file != $date . '.csv') {
                                unlink('/alidata/download_data/report_data/government_report/hz/' . $file);
                            }
                        }
                    }
                    //关闭文件夹
                    closedir($handle);
                }
            }
        }
    }

    /**
     * 河南文旅对接  上报分时预约数据
     * <AUTHOR>
     */
    public function pushDataForHeNan()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel            = new GovernmentDataSys();
        $dataCollectionModel = new DataCollection();
        //$timeshare = new TimeShareBiz();
        $field   = 'id,apply_id,land_code,land_key,land_name';
        $govList = $govModel->getGdListByType(14, $field);
        if (empty($govList)) {
            pft_log('government/HeNan/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
        }

        // 获取gov id -> 获取government_data_attr
        $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/HeNan/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        $lidArr             = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
            $lidArr[]                              = intval($value['land_id']);
        }

        if (empty($govAttrListMap)) {
            pft_log('government/HeNan/error', json_encode(['value' => $value, 'govAttrlist' => $govAttrListMap]));

            return;
        }
        $timeShareOrdeStoreApi = new TimeShareOrdeStore();

        // foreach ($govWithAttrListMap as $landItem) {
        //     $lidArr[] = intval($landItem['land_id']);
        // }

        if (empty($lidArr)) {
            pft_log('government/HeNan/empty', json_encode(['govAttrlist' => $govWithAttrListMap]));

            return;
        }

        $startTime    = date('Y-m-d');
        $endTime      = date('Y-m-d', strtotime('+30 days'));
        $timeToday    = date('Y-m-d 00:00:01');
        $timeTodayNow = date('Y-m-d H:i:s', time());
        $timeShareRes = $timeShareOrdeStoreApi->querySectionTimeSetRecordForDetail($lidArr, $startTime, $endTime);
        foreach ($govWithAttrListMap as $landItem) {
            if ($timeShareRes['code'] == 200 && !empty($timeShareRes['data'])) {
                foreach ($timeShareRes['data'] as $timeShareItem) {
                    if ($landItem['land_id'] == $timeShareItem['lid']) {
                        foreach ($timeShareItem['list'] as $timelistItem) {
                            foreach ($timelistItem['dateInfo'] as $dateInfoItem) {
                                $datedata[$timelistItem['date']][] = [
                                    'label'      => $dateInfoItem['label'],
                                    'book_num'   => $dateInfoItem['bookNum'],
                                    'remain_num' => $dateInfoItem['remainNum'],
                                    'start_time' => strtotime("{$timelistItem['date']}.{$dateInfoItem['startTime']}"),
                                    'end_time'   => strtotime("{$timelistItem['date']}.{$dateInfoItem['endTime']}"),
                                ];
                            }
                        }
                    }
                }
            }

            if (!empty($datedata)) {
                $enterNumArr = $dataCollectionModel->getEnterData($timeToday, $timeTodayNow, $landItem['land_id']);
                $leaveNumArr = $dataCollectionModel->getLeaveData($timeToday, $timeTodayNow, $landItem['land_id']);

                if (empty($enterNumArr[0]['cnt'])) {
                    $enterNum = 0;
                }

                if (empty($leaveNumArr[0]['cnt'])) {
                    $leaveNum = 0;
                }

                switch ($landItem['land_status']) {
                    case '0':
                        $notice = '闭园';
                        break;
                    case '1':
                        $notice = '正常开放';
                        break;
                    case '2':
                        $notice = '限流预警';
                        break;
                    case '3':
                        $notice = '停止入园';
                        break;
                    default:
                        $notice = '正常开放';
                }

                $pushdata = [
                    [
                        'scenic_id'         => $landItem['land_code'],
                        'book_list'         => [$datedata],
                        'scenic_status'     => $landItem['land_status'],
                        'notice'            => $notice,
                        'realtime_tourists' => $enterNum - $leaveNum,
                        'total_tourist'     => $enterNum ?? 0,
                    ]
                ];

                $header   = ['Content-Type:application/json', "appKey:{$landItem['land_key']}"];
                $pushdata = json_encode($pushdata);
                $response = curl_post('https://bigd.inhct.cn/data-api/api/bookData', $pushdata, 80, 25, '',
                    $header, true);

                // 记录日志
                pft_log('government/HeNan/data', json_encode(['post' => $pushdata, 'res' => $response, 'header' => $header]));
            }
            unset($datedata);
        }

        //foreach ($govWithAttrListMap as $landItem) {
        //    //先查询该景区下有几个分时规则
        //    $rules = $timeshare->getConfList((int)$landItem['land_id']);
        //    if (isset($rules['list'])) {
        //        //把分时规则放进一个数组
        //        foreach ($rules['list'] as $ruleItem) {
        //            $ruleIds[] = $ruleItem['sectionDateId'];
        //        }
        //    }
        //
        //    if (isset($ruleIds)){
        //        //该景区下有多少条分时规则就循环几次
        //        foreach ($ruleIds as $ruleidItem){
        //            //查询近30天内的该分时规则的售票情况
        //            for ($i=0;$i<1;$i++){
        //                $date = date('Y-m-d',strtotime("+{$i} days"));
        //                $res = $timeshare->querySectionDeductionRecordForDetail($ruleidItem,$date);
        //                if (isset($res['list'])){
        //                    foreach ($res['list'] as $item) {
        //                        $datedata[$date][] = [
        //                            'label'      => substr($item['beginTime'], 0, 2) . ':' . substr($item['beginTime'], 2,
        //                                    2) . '-' . substr($item['endTime'], 0, 2) . ':' . substr($item['endTime'], 2,
        //                                    2),
        //                            'book_num'   => $item['soldStorage'],
        //                            'start_time' => strtotime($date . $item['beginTime']),
        //                            'end_time'   => strtotime($date . $item['endTime']),
        //                            'remain_num' => $item['totalStorage'] - $item['soldStorage'],
        //                        ];
        //                    }
        //                }
        //            }
        //        }
        //        $pushdata[] = [
        //            'scenic_id'         => $landItem['land_code'],
        //            'book_list'         => [$datedata],
        //            'scenic_status'     => $landItem['land_status'],
        //            'notice'            => $landItem['notice'],
        //            'realtime_tourists' => 2000,
        //            'total_tourist'     => 10000,
        //        ];
        //        unset($datedata);
        //    }
        //    unset($ruleIds);
    }

    /**
     * 河南文旅对接  上报日游客量
     *
     *  昨天：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/GovernmentDataPush pushDailyEnterNumForHeNan 2022-04-29
     *  指定日期：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/GovernmentDataPush pushDailyEnterNumForHeNan 2022-04-29
     * <AUTHOR>
     */
    public function pushDailyEnterNumForHeNan()
    {
        $params       = $GLOBALS['argv'];
        $runTimestamp = strtotime('-1 days');

        //如果命令行有带运行日期的话
        if (isset($params[3]) and strtotime($params[3])) {
            $runTimestamp = strtotime($params[3]);
        }

        // 获取需要对接到对方平台的景区数据
        $govModel            = new GovernmentDataSys();
        $dataCollectionModel = new DataCollection();
        $field               = 'id,apply_id,land_code,land_key';
        $govList             = $govModel->getGdListByType(GovernmentDataSys::GOVERNMENT_SYS_TYPE_HN, $field);
        if (empty($govList)) {
            pft_log('government/HeNan/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/HeNan/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        foreach ($govWithAttrListMap as $landItem) {
            $beginTime = date('Ymd 00:00:01', $runTimestamp);
            $endTime   = date('Ymd 23:59:59', $runTimestamp);

            $landId = $landItem['land_id'];
            $landIdList = [215604, 67799];
            if (in_array($landId, $landIdList)) {
                $res = $dataCollectionModel->getDataNew($beginTime, $endTime, 0, $landId);
            } else {
                $res = $dataCollectionModel->getEnterData($beginTime, $endTime, $landId);
            }

            $enterNum  = $res[0]['cnt'] ?? 0;
            $pushdata  = [
                [
                    'scenicCode' => $landItem['land_code'],
                    'day'        => date('Y-m-d', $runTimestamp),
                    'count'      => $enterNum,
                ],
            ];

            //每个景区分开推送
            $headerArr = [
                'Content-Type:application/json',
                "appKey:{$landItem['land_key']}",
            ];

            $pushdata = json_encode($pushdata);
            $response = curl_post('https://bigd.inhct.cn/data-api/api/daySummary', $pushdata, 80, 25, '/api/curl_post',
                $headerArr);
            pft_log('government/HeNan/dailyReport',
                json_encode(['res' => $response, 'push' => $pushdata, 'header' => $headerArr]));
            if (isset($response['status']) && ($response['status'] == false || $response['status'] == 28)) {
                //响应错误的话再推一次
                curl_post('https://bigd.inhct.cn/data-api/api/daySummary', $pushdata, 80, 25, '/api/curl_post',
                    $headerArr);
            }

            //直接输出推送结果
            $pushMsg = json_encode([
                'key'        => '推送结果',
                'scenicCode' => $landItem['land_code'],
                'res'        => $response,
            ], JSON_UNESCAPED_UNICODE);
            echo $pushMsg;
        }
    }

    /**
     * 河南文旅对接  上报每15分钟入园游客量
     * <AUTHOR>
     */
    public function pushMinuteEnterNumForHeNan()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel            = new GovernmentDataSys();
        $dataCollectionModel = new DataCollection();
        $field               = 'id,apply_id,land_code,land_key';
        $govList             = $govModel->getGdListByType(GovernmentDataSys::GOVERNMENT_SYS_TYPE_HN, $field);

        if (empty($govList)) {
            pft_log('government/HeNan/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/HeNan/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        //对方接口不支持批量上传
        foreach ($govWithAttrListMap as $landItem) {
            $beginTime = date('Ymd 00:00:01');
            $endTime   = date('Ymd His', time());

            $landId = $landItem['land_id'];
            $landIdList = [215604, 67799];
            if (in_array($landId, $landIdList)) {
                $res = $dataCollectionModel->getDataNew($beginTime, $endTime, 0, $landId);
            } else {
                $res = $dataCollectionModel->getEnterData($beginTime, $endTime, $landId);
            }

            $enterNum  = $res[0]['cnt'] ?? 0;
            $pushdata  = [
                'scenicCode' => $landItem['land_code'],
                'upTime'     => date('Y-m-d H:i'),
                'total'      => $enterNum,
            ];

            $headerArr = [
                'Content-Type:application/json',
                "appKey:{$landItem['land_key']}",
            ];
            $pushdata  = json_encode($pushdata);
            $response  = curl_post('https://bigd.inhct.cn/data-api/api/dayInCount', $pushdata, 80, 25,
                '/api/curl_post', $headerArr);
            pft_log('government/HeNan/dailyReportIn',
                json_encode(['res' => $response, 'push' => $pushdata, 'header' => $headerArr]));
            if (isset($response['status']) && ($response['status'] == false || $response['status'] == 28)) {
                //响应错误的话再推一次
                curl_post('https://bigd.inhct.cn/data-api/api/dayInCount', $pushdata, 80, 25, '/api/curl_post',
                    $headerArr);
            }
        }
    }

    /**
     * 河南文旅对接  上报每15分钟出园游客量
     * <AUTHOR>
     */
    public function pushMinuteLeaveNumForHeNan()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel            = new GovernmentDataSys();
        $dataCollectionModel = new DataCollection();
        $field               = 'id,apply_id,land_code,land_key';
        $govList             = $govModel->getGdListByType(GovernmentDataSys::GOVERNMENT_SYS_TYPE_HN, $field);

        if (empty($govList)) {
            pft_log('government/HeNan/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/HeNan/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
        }

        //对方接口不支持批量上传
        foreach ($govWithAttrListMap as $landItem) {
            $beginTime = date('Ymd 00:00:01');
            $endTime   = date('Ymd His', time());
            
            $landId = $landItem['land_id'];
            $landIdList = [215604, 67799];
            if (in_array($landId, $landIdList)) {
                $res = $dataCollectionModel->getDataNew($beginTime, $endTime, 0, $landId);
            } else {
                $res = $dataCollectionModel->getLeaveData($beginTime, $endTime, $landId);
            }

            $enterNum  = $res[0]['cnt'] ?? 0;
            $pushdata  = [
                'scenicCode' => $landItem['land_code'],
                'upTime'     => date('Y-m-d H:i'),
                'total'      => $enterNum,
            ];

            $headerArr = [
                'Content-Type:application/json',
                "appKey:{$landItem['land_key']}",
            ];
            $pushdata  = json_encode($pushdata);
            $response  = curl_post('https://bigd.inhct.cn/data-api/api/dayOutCount', $pushdata, 80, 25,
                '/api/curl_post', $headerArr);
            pft_log('government/HeNan/dailyReportOut',
                json_encode(['res' => $response, 'push' => $pushdata, 'header' => $headerArr]));
            if (isset($response['status']) && ($response['status'] == false || $response['status'] == 28)) {
                //响应错误的话再推一次
                curl_post('https://bigd.inhct.cn/data-api/api/dayOutCount', $pushdata, 80, 25, '/api/curl_post',
                    $headerArr);
            }
        }
    }

    /**
     * 陕西文旅对接  上报分时预约数据
     * <AUTHOR>
     */
    public function pushDataForShangXi()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel            = new GovernmentDataSys();
        $dataCollectionModel = new DataCollection();
        //$timeshare = new TimeShareBiz();
        $field   = 'id,apply_id,land_code,land_key,land_name';
        $govList = $govModel->getGdListByType(12, $field);
        if (empty($govList)) {
            pft_log('government/ShangXi/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
        }

        // 获取gov id -> 获取government_data_attr
        $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/ShangXi/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        $lidArr             = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
            $lidArr[]                              = intval($value['land_id']);
        }

        if (empty($govAttrListMap)) {
            pft_log('government/ShangXi/error', json_encode(['value' => $value, 'govAttrlist' => $govAttrListMap]));

            return;
        }
        $timeShareOrdeStoreApi = new TimeShareOrdeStore();

        // foreach ($govWithAttrListMap as $landItem) {
        //     $lidArr[] = intval($landItem['land_id']);
        // }

        if (empty($lidArr)) {
            pft_log('government/ShangXi/empty', json_encode(['govAttrlist' => $govWithAttrListMap]));

            return;
        }

        $startTime = date('Y-m-d');
        $endTime   = date('Y-m-d', strtotime('+30 days'));
        $timeToday = date('Y-m-d 00:00:01');
        $timeTodayNow = date('Y-m-d H:i:s',time());
        $lidArrChunk = array_chunk($lidArr, 5);
        $timeShareData = [];
        foreach ($lidArrChunk as $value) {
            $timeShareRes = $timeShareOrdeStoreApi->querySectionTimeSetRecordForDetail($value, $startTime, $endTime);
            if ($timeShareRes['code'] == 200 && !empty($timeShareRes['data'])) {
                $timeShareData = array_merge($timeShareData, $timeShareRes['data']);
            }
        }

        $pushdata = [];
        foreach ($govWithAttrListMap as $landItem) {
            foreach ($timeShareData as $timeShareItem) {
                if ($landItem['land_id'] == $timeShareItem['lid']) {
                    foreach ($timeShareItem['list'] as $timelistItem) {
                        foreach ($timelistItem['dateInfo'] as $dateInfoItem) {
                            $datedata[$timelistItem['date']][] = [
                                'label'      => $dateInfoItem['label'],
                                'book_num'   => $dateInfoItem['bookNum'],
                                'remain_num' => $dateInfoItem['remainNum'],
                                'start_time' => strtotime("{$timelistItem['date']}.{$dateInfoItem['startTime']}"),
                                'end_time'   => strtotime("{$timelistItem['date']}.{$dateInfoItem['endTime']}"),
                            ];
                        }
                    }
                }
            }

            if (!empty($datedata)) {
                $enterNumArr = $dataCollectionModel->getEnterData($timeToday, $timeTodayNow, $landItem['land_id']);
                $leaveNumArr = $dataCollectionModel->getLeaveData($timeToday, $timeTodayNow, $landItem['land_id']);

                if (empty($enterNumArr[0]['cnt'])) {
                    $enterNum = 0;
                }

                if (empty($leaveNumArr[0]['cnt'])) {
                    $leaveNum = 0;
                }

                switch ($landItem['land_status']) {
                    case '0':
                        $notice = '闭园';
                        break;
                    case '1':
                        $notice = '正常开放';
                        break;
                    case '2':
                        $notice = '限流预警';
                        break;
                    case '3':
                        $notice = '停止入园';
                        break;
                    default:
                        $notice = '正常开放';
                }

                $pushdata[] = [
                    'scenic_id'         => $landItem['land_code'],
                    'book_list'         => [$datedata],
                    'scenic_status'     => $landItem['land_status'],
                    'notice'            => $notice,
                    'realtime_tourists' => $enterNum - $leaveNum,
                    'total_tourists'    => $enterNum ?? 0,
                ];
            }
            unset($datedata);
        }

        $header   = ['Content-Type:application/json', "appKey:{$landItem['land_key']}"];
        $pushdata = json_encode($pushdata);
        $response = curl_post('https://bigd.tourage.cn/upload-data/scenicReserveData/bookTravelerData', $pushdata, 80,
            25, '',
            $header, true);
        //记录日志
        pft_log('government/ShangXi/data', json_encode(['post' => $pushdata, 'res' => $response, 'header' => $header]));
    }

    /**
     * 湖南分时预约上报
     * <AUTHOR>
     */
    public function pushToHuNanReservation()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel = new GovernmentDataSys();
        $touModel = new GovernmentTouristStatistics();
        $field    = 'id,apply_id,land_code,land_key';
        $govList  = $govModel->getGdListByType(GovernmentDataSys::GOVERNMENT_SYS_TYPE_HuN, $field);
        if (empty($govList)) {
            pft_log('government/Hunan/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/Hunan/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        //取该供应商下所有的lid
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $attrkey => $attrvalue) {
            foreach ($govListMap as $govkey => $govvalue) {
                if ($attrkey == $govkey) {
                    $govWithAttrListMap[$govvalue['apply_id']] = array_merge($govAttrListMap[$attrkey],
                        $govListMap[$govkey]);
                }
            }
        }

        if (empty($govWithAttrListMap)) {
            pft_log('government/Hunan/error',
                json_encode(['govList' => $govListMap, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        $timeShareOrdeStoreApi = new TimeShareOrdeStore();

        $startTime = date('Y-m-d');
        $endTime   = date('Y-m-d', strtotime('+5 days'));
        $today     = date('Ymd');
        $url       = "http://www.daqctc.com/api/res/api/external/multiNoticeOrderStock";
        //获取毫秒时间戳
        list($msec, $sec) = explode(' ', microtime());
        $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
        foreach ($govWithAttrListMap as $landItem) {
            list($appid, $appsecret) = explode('|', $landItem['land_key']);
            $landids = json_decode($landItem['lids'], true);
            $res     = $timeShareOrdeStoreApi->querySectionTimeSetRecordForDetail($landids, $startTime, $endTime);
            //查询该供应商下的所有lid当天的核销总数
            if (!empty($landids) && !empty($landItem['apply_id'])) {
                $filter   = [
                    'date' => $today,
                    'fid'  => $landItem['apply_id'],
                    'lid'  => ['in', $landids],
                ];
                $innerNum = $touModel->getEnterNum($filter, 'SUM(order_ticket) as innerNum');
            }

            if (!empty($res['data'])) {
                foreach ($res['data'] as $item) {
                    if (!empty($item['list'])) {
                        foreach ($item['list'] as $listitem) {
                            if (!empty($listitem['dateInfo'])) {
                                //将该供应商下所有产品的所有分时票相加出结果
                                $booknum   = 0;
                                $remainnum = 0;
                                $totalnum  = 0;
                                foreach ($listitem['dateInfo'] as $dateitem) {
                                    $booknum   += $dateitem['bookNum'];
                                    $remainnum += $dateitem['remainNum'];
                                    $totalnum  += $dateitem['bookNum'] + $dateitem['remainNum'];
                                }

                                $reserInfos[$item['lid']][$listitem['date']] = [
                                    'booknum'   => $booknum,
                                    'remainnum' => $remainnum,
                                    'totalnum'  => $totalnum,
                                ];
                            }
                        }
                    }
                }
            }

            if (!empty($reserInfos)) {
                $booknums   = [];
                $remainnums = [];
                $totalnums  = [];
                foreach ($reserInfos as $key => $reserItem) {
                    foreach ($reserItem as $daykey => $dayitem) {
                        $booknums[$daykey]   += $reserInfos[$key][$daykey]['booknum'];
                        $remainnums[$daykey] += $reserInfos[$key][$daykey]['remainnum'];
                        $totalnums[$daykey]  += $reserInfos[$key][$daykey]['totalnum'];
                    }
                }

                //5天的数据格式
                for ($i = 0; $i < 5; $i++) {
                    $postdata[] = [
                        'date'         => date('Y-m-d', strtotime("+{$i} days")),
                        'orderNum'     => $booknums[date('Y-m-d', strtotime("+{$i} days"))],
                        'stock'        => $remainnums[date('Y-m-d', strtotime("+{$i} days"))],
                        'resourceType' => $landItem['type'],
                        'code'         => $landItem['land_code'],
                        'totalStock'   => $totalnums[date('Y-m-d', strtotime("+{$i} days"))],
                        'innerNum'     => 0,
                    ];
                }
                $postdata[0]['innerNum'] = $innerNum[0]['innerNum'] ?? 0;
            }

            if (!empty($postdata)) {
                //参数转为json格式
                $postdata = json_encode($postdata);
                //拼接字符串
                $str = "{$msectime}POST{$url}{$postdata}";
                //sha256->base64制作签名
                $sign = base64_encode(hash_hmac('sha256', $str, $appsecret, true));
                //请求头部
                $headerArr = [
                    "ACCESS-APP-ID:{$appid}",
                    "ACCESS-SIGNATURE:{$sign}",
                    "ACCESS-TIMESTAMP:{$msectime}",
                    "Content-Type:application/json",
                ];

                //发送curl请求
                $response = curl_post($url, $postdata, 80, 25, '/api/curl_post', $headerArr, true, '', 'post');
                //记录日志
                pft_log('government/HuNan/data',
                    json_encode(['post' => $postdata, 'res' => $response, 'header' => $headerArr]));
            }
            unset($postdata);
            unset($reserInfos);
        }
    }

    /**
     * 日度累计游客量上报接口(上饶) 频率一天
     * <AUTHOR>
     */
    public function pushToShangRaoDailyReport()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel            = new GovernmentDataSys();
        $dataCollectionModel = new DataCollection();
        $field               = 'id,apply_id,land_code,land_key';
        $cache               = Cache::getInstance('redis');
        $govList             = $govModel->getGdListByType(16, $field);
        if (empty($govList)) {
            pft_log('government/ShangRao/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/ShangRao/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        //取该供应商下所有的lid
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $attrkey => $attrvalue) {
            foreach ($govListMap as $govkey => $govvalue) {
                if ($attrkey == $govkey) {
                    $govWithAttrListMap[$govvalue['apply_id']] = array_merge($govAttrListMap[$attrkey],
                        $govListMap[$govkey]);
                }
            }
        }

        if (empty($govWithAttrListMap)) {
            pft_log('government/ShangRao/error',
                json_encode(['govList' => $govListMap, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        foreach ($govWithAttrListMap as $applyItem) {
            //把昨天的链表中的数据全取出来，并相加得到昨天的所有时间段总和
            $lastdate  = date('Y-m-d', strtotime('-1 days'));
            $totalFnum = $cache->get("government:report:config:shangrao:randNumList:date:{$lastdate}:land:{$applyItem['land_code']}");
            if (!empty($totalFnum)) {
                $cache->del("government:report:config:shangrao:randNumList:date:{$lastdate}:land:{$applyItem['land_code']}");
            }
            //统计闸机的入园总数
            $deviceInfo = json_decode($applyItem['device_info'], true);
            if (empty($deviceInfo)) {
                continue;
            }
            $deviceKeyArr = array_column($deviceInfo, 'device_key');
            $totalNum     = 0;
            if (!empty($deviceKeyArr)) {
                //根据闸机特征码搜索昨日入园总数
                $countRes = $dataCollectionModel->getParkStatisticsDeviceData($deviceKeyArr);
                $totalNum = (int)$countRes[0]['totalNum'] ?? 0;
            }
            //统计摄像头的入园总数
            $cameraKeyArr = array_column($deviceInfo, 'camera_key');
            $totalcaNum   = 0;
            if (!empty($cameraKeyArr)) {
                //根据摄像头mac地址搜索昨日入园总数
                $Cameracount = $dataCollectionModel->getParkStatisticsCameraData($cameraKeyArr);
                $totalcaNum  = (int)$Cameracount[0]['totalNum'] ?? 0;
            }

            // 获取毫秒时间戳
            list($msec, $sec) = explode(' ', microtime());
            $msectime    = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
            $pushBodyArr = [
                [
                    'day'   => date('Y-m-d'),
                    'count' => $totalNum + $totalFnum + $totalcaNum,
                ],
            ];

            //签名
            $sign      = MD5($applyItem['land_key'] . '&' . json_encode($pushBodyArr) . '&' . $msectime);
            $headerArr = [
                "Content-Type:application/json",
            ];

            $pushHeaderArr = [
                'scenicCode' => $applyItem['land_code'],
                'time'       => $msectime,
                'sign'       => $sign,
            ];

            $pushResult = curl_post($this->ShangRaoUrl . '/data/tourist/real-gate-day',
                json_encode(['header' => $pushHeaderArr, 'body' => json_encode($pushBodyArr)]), 80, 25, '', $headerArr);
            pft_log('government/ShangRao/daily',
                json_encode(['res' => $pushResult, 'header' => $pushHeaderArr, 'body' => json_encode($pushBodyArr)]));
        }
    }

    /**
     * 当天实时累计游客量上报接口(上饶) 频率5分钟
     * <AUTHOR>
     */
    public function pushToShangRaoTotalNum()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel            = new GovernmentDataSys();
        $dataCollectionModel = new DataCollection();
        $field               = 'id,apply_id,land_code,land_key';
        $govList             = $govModel->getGdListByType(16, $field);
        $cache               = Cache::getInstance('redis');
        if (empty($govList)) {
            pft_log('government/ShangRao/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/ShangRao/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        //取该供应商下所有的lid
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $attrkey => $attrvalue) {
            foreach ($govListMap as $govkey => $govvalue) {
                if ($attrkey == $govkey) {
                    $govWithAttrListMap[$govvalue['apply_id']] = array_merge($govAttrListMap[$attrkey],
                        $govListMap[$govkey]);
                }
            }
        }

        if (empty($govWithAttrListMap)) {
            pft_log('government/ShangRao/error',
                json_encode(['govList' => $govListMap, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        $isable  = $cache->get("government:report:config:shangrao:isable");
        $confStr = $cache->get("government:report:config:shangrao:startDate|endDate|startTime|endTime|max|min");
        //isable = 1 代表配置生效
        $randNumTotal = 0;
        $date         = date('Y-m-d');
        if ($isable == 1) {
            if (!empty($confStr)) {
                list($startDate, $endDate, $startTime, $endTime, $randList) = explode('|', $confStr);
                $randListArr = json_decode($randList, true);
                //先判断是否在设置的有效期天内
                if (!empty($startDate) && !empty($endDate) && date('Y-m-d') >= $startDate && date('Y-m-d') <= $endDate) {
                    //再判断是否在设置一天内的时间段，在的话则生效并推入链表
                    if ($startTime <= date('H') && date('H') < $endTime) {
                        $needAddFalse = true;
                        $needIncreate = true;
                    }
                }
            }
        }

        foreach ($govWithAttrListMap as $applyItem) {
            $deviceInfo = json_decode($applyItem['device_info'], true);
            if (!empty($deviceInfo)) {
                $deviceKeyArr = array_column($deviceInfo, 'device_key');
                if (!empty($deviceKeyArr)) {
                    $countRes = $dataCollectionModel->getDeviceEnterDataByTime($deviceKeyArr);
                    $countArr = [];
                    if (!empty($countRes)) {
                        foreach ($countRes as $couItem) {
                            $countArr[$couItem['device_key']][$couItem['land_id']] = [
                                'totalEnterNum' => $couItem['totalEnterNum'],
                            ];
                        }
                    }

                    if (is_array($deviceInfo)) {
                        $devices = [];
                        foreach ($deviceInfo as $deItem) {
                            $tickets = [];
                            if (isset($deItem['device_key'])) {
                                foreach ($deItem['land_info'] as $landItem) {
                                    $tickets[] = [
                                        'ticket' => $landItem['land_name'],
                                        'count'  => $countArr[$deItem['device_key']][$landItem['lid']]['totalEnterNum'] ?? 0,
                                    ];
                                }
                                $devices[] = [
                                    'device'  => $deItem['device_key'],
                                    'tickets' => $tickets,
                                ];
                            }
                        }
                    }
                }

                $cameraKeyArr = array_column($deviceInfo, 'camera_key');
                if (!empty($cameraKeyArr)) {
                    //根据摄像头mac地址搜索昨日入园总数
                    $cameracount = $dataCollectionModel->getParkStatisticsCameraDataToday($cameraKeyArr);
                    if (!empty($cameracount)) {
                        $cameraArr = array_column($cameracount, null, 'mac_address');
                    }
                    $cameras = [];
                    foreach ($deviceInfo as $caItem) {
                        if (isset($caItem['camera_key'])) {
                            $cameras[] = [
                                'device'  => $caItem['camera_key'],
                                'tickets' => [
                                    [
                                        'ticket' => $caItem['land_info'][0]['land_name'] ?? "婺源通票产品",
                                        'count'  => $cameraArr[$caItem['camera_key']]['totalNum'] ?? 0,
                                    ],
                                ],
                            ];
                        }
                    }

                    //如果摄像头数据不为空的话，将闸机数据和摄像头数据进行合并
                    if (!is_null($devices) && !is_null($cameras)) {
                        $devices = array_merge($devices, $cameras);
                    } elseif (is_null($devices)) {
                        $devices = $cameras;
                    }
                }

                if (null != $cache->get("government:report:config:shangrao:randNumList:date:{$date}:land:{$applyItem['land_code']}")) {
                    $needAddFalse = true;
                }
                if ($needAddFalse) {
                    //如果需要自增的话再往链表内推假数据
                    $randNumTotal = $cache->get("government:report:config:shangrao:randNumList:date:{$date}:land:{$applyItem['land_code']}");
                    $randNumTotal = $randNumTotal ?? 0;
                    if ($needIncreate) {
                        $randNum      = rand($randListArr[$applyItem['land_code']]['min'],
                            $randListArr[$applyItem['land_code']]['max']);
                        $randNumTotal = $randNumTotal + $randNum;
                        $cache->set("government:report:config:shangrao:randNumList:date:{$date}:land:{$applyItem['land_code']}",
                            $randNumTotal, 3600 * 48);
                    }

                    $ticketNum = 0;
                    foreach ($devices as $deInfoItem) {
                        $ticketNum += count($deInfoItem['tickets']);
                    }

                    if ($ticketNum != 0) {
                        //总量除闸机数，把多出来的部分平均分给每台闸机
                        $average = $randNumTotal / $ticketNum;
                        //除不尽的部分取余数
                        $reamin = $randNumTotal % $ticketNum;
                        foreach ($devices as &$decotItem) {
                            foreach ($decotItem['tickets'] as &$ticketsItem) {
                                $ticketsItem['count'] = $ticketsItem['count'] + intval($average);
                            }
                        }
                        $devices[0]['tickets'][0]['count'] = $devices[0]['tickets'][0]['count'] + $reamin;
                    }
                }

                $pushBodyArr = [
                    'time'    => date('Y-m-d H:i:s'),
                    'devices' => $devices,
                ];

                $devicesTotalNum = 0;
                foreach ($devices as $deviceItem) {
                    $deviceNum = 0;
                    if (!empty($deviceItem['tickets'])) {
                        foreach ($deviceItem['tickets'] as $tItem) {
                            $deviceNum += $tItem['count'];
                        }
                    }
                    $devicesTotalNum += $deviceNum;
                }
                //将该景点的所有设备入园总和记录，并存入redis，供出园数比较
                $cache->set("government:report:config:shangrao:landTotalNum:date:{$date}:land:{$applyItem['land_code']}",
                    $devicesTotalNum, '', 3600 * 24);

                // 获取毫秒时间戳
                list($msec, $sec) = explode(' ', microtime());
                $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

                //签名
                $sign = MD5($applyItem['land_key'] . '&' . json_encode($pushBodyArr) . '&' . $msectime);

                $headerArr = [
                    "Content-Type:application/json",
                ];

                $pushHeaderArr = [
                    'scenicCode' => $applyItem['land_code'],
                    'time'       => $msectime,
                    'sign'       => $sign,
                ];

                $pushResult = curl_post($this->ShangRaoUrl . '/data/tourist/real-people-number',
                    json_encode(['header' => $pushHeaderArr, 'body' => json_encode($pushBodyArr)]), 80, 25, '',
                    $headerArr);
                pft_log('government/ShangRao/realpeoplenum',
                    json_encode([
                        'res'    => $pushResult,
                        'header' => $pushHeaderArr,
                        'body'   => json_encode($pushBodyArr, JSON_UNESCAPED_UNICODE),
                    ], JSON_UNESCAPED_UNICODE));
                unset($devices);
                unset($cameras);
            }
        }
    }

    /**
     * 当天实时累计游客出园人数上报接口(上饶) 频率5分钟
     * <AUTHOR>
     */
    public function pushToShangRaoTotalLeaveNum()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel            = new GovernmentDataSys();
        $dataCollectionModel = new DataCollection();
        $field               = 'id,apply_id,land_code,land_key';
        $govList             = $govModel->getGdListByType(16, $field);
        $cache               = Cache::getInstance('redis');
        if (empty($govList)) {
            pft_log('government/ShangRao/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govListMap = [];
        $gdsIdArr   = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
            $gdsIdArr[]              = $item['id'];
        }

        // 获取gov id -> 获取government_data_attr
        // $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/ShangRao/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());

            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        //取该供应商下所有的lid
        $govWithAttrListMap = [];
        foreach ($govAttrListMap as $attrkey => $attrvalue) {
            foreach ($govListMap as $govkey => $govvalue) {
                if ($attrkey == $govkey) {
                    $govWithAttrListMap[$govvalue['apply_id']] = array_merge($govAttrListMap[$attrkey],
                        $govListMap[$govkey]);
                }
            }
        }

        if (empty($govWithAttrListMap)) {
            pft_log('government/ShangRao/error',
                json_encode(['govList' => $govListMap, 'govAttrlist' => $govAttrListMap]));

            return;
        }

        $isable  = $cache->get("government:report:config:shangrao:isable");
        $confStr = $cache->get("government:report:config:shangrao:startDate|endDate|startTime|endTime|max|min");
        //isable = 1 代表配置生效
        $randNumTotal = 0;
        $date         = date('Y-m-d');
        if ($isable == 1) {
            if (!empty($confStr)) {
                list($startDate, $endDate, $startTime, $endTime, $randList) = explode('|', $confStr);
                $randListArr = json_decode($randList, true);
                //先判断是否在设置的有效期天内
                if (!empty($startDate) && !empty($endDate) && date('Y-m-d') >= $startDate && date('Y-m-d') <= $endDate) {
                    //再判断是否在设置一天内的时间段，在的话则生效并推入链表
                    if ($startTime <= date('H') && date('H') < $endTime) {
                        $needAddFalse = true;
                        $needIncreate = true;
                    }
                }
            }
        }

        foreach ($govWithAttrListMap as $applyItem) {
            $deviceInfo = json_decode($applyItem['device_info'], true);
            if (!empty($deviceInfo)) {
                $deviceKeyArr = array_column($deviceInfo, 'device_key');
                if (!empty($deviceKeyArr)) {
                    $countRes = $dataCollectionModel->getDeviceLeaveDataByTime($deviceKeyArr);
                    if (!empty($countRes)) {
                        $countArr = [];
                        foreach ($countRes as $couItem) {
                            $countArr[$couItem['device_key']] = $couItem['totalLeaveNum'];
                        }
                    }

                    if (is_array($deviceInfo)) {
                        $devices = [];
                        foreach ($deviceInfo as $deItem) {
                            if (!empty($deItem['device_key'])) {
                                $devices[] = [
                                    'device' => $deItem['device_key'],
                                    'count'  => $countArr[$deItem['device_key']] ?? 0  //如果没有检索到该闸机的数据，直接上报0
                                ];
                            }
                        }
                    }
                }

                $cameraKeyArr = array_column($deviceInfo, 'camera_key');
                if (!empty($cameraKeyArr)) {
                    //根据摄像头mac地址搜索昨日入园总数
                    $cameracount = $dataCollectionModel->getParkStatisticsCameraDataTodayOut($cameraKeyArr);
                    if (!empty($cameracount)) {
                        $cameraArr = array_column($cameracount, null, 'mac_address');
                    }
                    $cameras = [];
                    foreach ($deviceInfo as $caItem) {
                        if (isset($caItem['camera_key'])) {
                            $cameras[] = [
                                'device' => $caItem['camera_key'],
                                'count'  => $cameraArr[$caItem['camera_key']]['totalNum'] ?? 0,
                            ];
                        }
                    }
                    //如果摄像头数据不为空的话，将闸机数据和摄像头数据进行合并
                    if (!is_null($devices) && !is_null($cameras)) {
                        $devices = array_merge($devices, $cameras);
                    } elseif (is_null($devices)) {
                        $devices = $cameras;
                    }
                }

                if (null != $cache->get("government:report:config:shangrao:leave:randNumList:date:{$date}:land:{$applyItem['land_code']}")) {
                    $needAddFalse = true;
                }
                if ($needAddFalse) {
                    //如果需要自增的话再往链表内推假数据
                    $randNumTotal = $cache->get("government:report:config:shangrao:leave:randNumList:date:{$date}:land:{$applyItem['land_code']}");
                    $randNumTotal = $randNumTotal ?? 0;
                    if ($needIncreate) {
                        $randNum      = rand($randListArr[$applyItem['land_code']]['outmin'],
                            $randListArr[$applyItem['land_code']]['outmax']);
                        $randNumTotal = $randNumTotal + $randNum;
                        $cache->set("government:report:config:shangrao:leave:randNumList:date:{$date}:land:{$applyItem['land_code']}",
                            $randNumTotal, 3600 * 48);
                    }

                    if (count($devices) != 0) {
                        //总量除闸机数，把多出来的部分平均分给每台闸机
                        $average = $randNumTotal / count($devices);
                        //除不尽的部分取余数
                        $reamin = $randNumTotal % count($devices);
                        foreach ($devices as &$deItem) {
                            $deItem['count'] += intval($average);
                        }
                        $devices[0]['count'] += $reamin;
                    }
                }

                //取一下该景点入园数跟出园数做一个比较,如果总出园大于总入园，则修改出园数量
                $landTotalLeaveNum = 0;
                foreach ($devices as $devicesItem) {
                    $landTotalLeaveNum += $devicesItem['count'];
                }
                $lastEnterNum = $cache->get("government:report:config:shangrao:landTotalNum:date:{$date}:land:{$applyItem['land_code']}");
                if ($landTotalLeaveNum > $lastEnterNum) {
                    $average = $lastEnterNum / count($devices);
                    foreach ($devices as &$devicesItem) {
                        $devicesItem['count'] = intval($average);
                    }
                }

                $pushBodyArr = [
                    'time'    => date('Y-m-d H:i:s'),
                    "devices" => $devices,
                ];

                //获取毫秒时间戳
                list($msec, $sec) = explode(' ', microtime());
                $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

                //签名
                $sign = MD5($applyItem['land_key'] . '&' . json_encode($pushBodyArr) . '&' . $msectime);

                $pushHeaderArr = [
                    'scenicCode' => $applyItem['land_code'],
                    'time'       => $msectime,
                    'sign'       => $sign,
                ];

                $headerArr = [
                    "Content-Type:application/json",
                ];

                $pushResult = curl_post($this->ShangRaoUrl . '/data/tourist/real-exit-people-number',
                    json_encode(['header' => $pushHeaderArr, 'body' => json_encode($pushBodyArr)]), 80, 25, '',
                    $headerArr);
                pft_log('government/ShangRao/leave',
                    json_encode([
                        'res'    => $pushResult,
                        'header' => $pushHeaderArr,
                        'body'   => json_encode($pushBodyArr),
                    ], JSON_UNESCAPED_UNICODE));
                unset($devices);
                unset($cameras);
            }
        }
    }

    /**
     * 葛仙山订单状态变更通知
     * <AUTHOR>
     */
    public function pushToGeXianShanCancel()
    {
        $lids = [
            111391,111392,111393,111396,111397,112440,150783,150790,160450,183663,187985,193726,220859,220899
        ];
        $fid  = 2020147;
        //$lids = [
        //    75379,
        //    87645,
        //];
        //$fid = 6970;
        $touModel   = new GovernmentTouristStatistics();
        $orderInfos = $touModel->getModifyOrderbyLandId($lids, $fid);
        if (!empty($orderInfos)) {
            foreach ($orderInfos as $orderItem) {
                if (!empty($orderItem['cancel_orders_info'])) {
                    $cancelOrders = json_decode($orderItem['cancel_orders_info'], true);
                    foreach ($cancelOrders as $celItem) {
                        $data['data'][] = $celItem[0];
                    }
                }
            }

            if (!empty($data)) {
                $headerArr = ['Content-Type:application/json'];
                $response  = curl_post('https://gxc.3dy.me/gxc/public/ticket', json_encode($data), 80, 25, '',
                    $headerArr);
            }
        }
        pft_log('government/gxs_report/data',
            json_encode([
                'res'        => $response ?? "",
                'orderinfos' => $orderInfos ?? "",
                'time'       => date('Y-m-d H:i:s', time()),
            ]));
    }

    /**
     * 少华山每日收入数据汇总
     * <AUTHOR>
     */
    public function pushToShaoHuaShanIncome()
    {
        $incomeUrl = 'http://datacollect.juntuyun.com/api/business/summary/dayTotalAmount';
        $headerArr = ['Content-Type:application/x-www-form-urlencoded'];
        $appid     = '0IDib3uC';
        $appkey    = 'e19e716d282aa4d425793cfea4dd7b08a0fb5892';
        $fid       = 1055147;
        $dataTime  = date('Y-m-d H:i:s');
        // 获取uuid
        $chars = md5(uniqid(mt_rand(), true));
        $uuid  = substr($chars, 0, 8) . '-'
                 . substr($chars, 8, 4) . '-'
                 . substr($chars, 12, 4) . '-'
                 . substr($chars, 16, 4) . '-'
                 . substr($chars, 20, 12);

        $staModel = new GovernmentTouristStatistics();
        //少华山要按照索道票和门票的价格分别上报。1055147|163148|444760 这张套票里有景区和索道。 景区30 索道100 拆出来单独算。
        //除套票外门票收入 444759,444763
        $IncomeArr = $staModel->getTotalIncomeByFid($fid, [444759, 444763]);
        //套票计算
        $packIncomeArr = $staModel->getTicketCountByFid($fid, [444760]);
        if (!empty($packIncomeArr)) {
            $gateIncome  = (($IncomeArr[0]['income'] = $IncomeArr[0]['income'] ?? 0) / 100) + ($packIncomeArr * 30);  //景区票加上套票的数量 * 30
            $cableIncome = $packIncomeArr * 100;                                                                      //套票的数量 * 100 对方单位为元
        }

        //上报门票收入
        $postGateDataArr = [
            'requuid'    => $uuid,
            'appid'      => $appid,
            'formatCode' => "01",
            'income'     => $gateIncome ?? ($IncomeArr[0]['income'] ?? 0),
            'dataTime'   => $dataTime,
        ];
        ksort($postGateDataArr);
        $sign                    = strtoupper(MD5(urldecode(http_build_query($postGateDataArr)) . "&secret={$appkey}"));
        $postGateDataArr['sign'] = $sign;
        $pushGate                = curl_post($incomeUrl, http_build_query($postGateDataArr), '80', 25, '', $headerArr,
            true);
        //上报索道收入
        $postCableDataArr = [
            'requuid'    => $uuid,
            'appid'      => $appid,
            'formatCode' => "02",
            'income'     => $cableIncome ?? 0,
            'dataTime'   => $dataTime,
        ];
        ksort($postCableDataArr);
        $sign                     = strtoupper(MD5(urldecode(http_build_query($postCableDataArr)) . "&secret={$appkey}"));
        $postCableDataArr['sign'] = $sign;
        $pushCable                = curl_post($incomeUrl, http_build_query($postCableDataArr), '80', 25, '', $headerArr,
            true);
        pft_log('government/ShaoHuaShan/Income',
            json_encode([
                'gate'      => $pushGate,
                'gatebody'  => $postGateDataArr,
                'cable'     => $pushCable,
                'cablebody' => $postCableDataArr,
                'income'    => $IncomeArr,
                'pack'      => $packIncomeArr,
            ]));
    }

    public function pushToShaoHuaShanPeople()
    {
        $peopleUrl = 'http://datacollect.juntuyun.com/api/business/summary/dayTotalVisitor';
        $headerArr = ['Content-Type:application/x-www-form-urlencoded'];
        $appid     = '0IDib3uC';
        $appkey    = 'e19e716d282aa4d425793cfea4dd7b08a0fb5892';
        $fid       = 1055147;
        $dataTime  = date('Y-m-d H:i:s');
        // 获取uuid
        $chars = md5(uniqid(mt_rand(), true));
        $uuid  = substr($chars, 0, 8) . '-'
                 . substr($chars, 8, 4) . '-'
                 . substr($chars, 12, 4) . '-'
                 . substr($chars, 16, 4) . '-'
                 . substr($chars, 20, 12);

        $staModel     = new GovernmentTouristStatistics();
        $gatecheckNum = $staModel->getCheckInfosByFid($fid, [444759, 444763]);
        $packcheckNum = $staModel->getCheckInfosByFid($fid, [444760]);
        if (!empty($packcheckNum[0]['cknum'])) {
            $gateIcknum = (($gatecheckNum[0]['cknum'] = $gatecheckNum[0]['cknum'] ?? 0)) + ($packcheckNum[0]['cknum'] = $packcheckNum[0]['cknum'] ?? 0);
            $cablecknum = $packcheckNum[0]['cknum'];
        }

        $postGateDataArr = [
            'requuid'    => $uuid,
            'appid'      => $appid,
            'formatCode' => "01",
            'visitorNum' => $gateIcknum ?? ($gatecheckNum[0]['cknum'] ?? 0),
            'dataTime'   => $dataTime,
        ];
        ksort($postGateDataArr);
        $sign                    = strtoupper(MD5(urldecode(http_build_query($postGateDataArr)) . "&secret={$appkey}"));
        $postGateDataArr['sign'] = $sign;
        $pushGate                = curl_post($peopleUrl, http_build_query($postGateDataArr), '80', 25, '', $headerArr,
            true);

        $postCableDataArr = [
            'requuid'    => $uuid,
            'appid'      => $appid,
            'formatCode' => "02",
            'visitorNum' => $cablecknum ?? 0,
            'dataTime'   => $dataTime,
        ];
        ksort($postCableDataArr);
        $sign                     = strtoupper(MD5(urldecode(http_build_query($postCableDataArr)) . "&secret={$appkey}"));
        $postCableDataArr['sign'] = $sign;
        $pushCable                = curl_post($peopleUrl, http_build_query($postCableDataArr), '80', 25, '', $headerArr,
            true);
        pft_log('government/ShaoHuaShan/People',
            json_encode([
                'gateres'   => $pushGate,
                'gatebody'  => $postGateDataArr,
                'cable'     => $pushCable,
                'cablebody' => $postCableDataArr,
                'gate'      => $gatecheckNum,
                'pack'      => $packcheckNum,
            ]));
    }

    /**
     * 遂宁市购票信息上报
     * User: Liucm
     * Date: 2020/12/8
     * Time: 14:02
     */
    public function pushDataToSuiNing()
    {
        $endTime   = date('Y-m-d H:i:s');
        $beginTime = date('Y-m-d H:i:s', time() - 5 * 60);
        //首先获取一个锁，如果没锁继续运行，如果有锁终止，并加一个时间标识,java接口限定最大时间为10分钟
        $redis         = Cache::getInstance('redis');
        $redisLock     = 'pushDataSuiNing:lock';
        $redisTimeLock = 'pushDataSuiNing:time';
        $redisLockRes  = $redis->get($redisLock);
        if ($redisLockRes) {
            $redis->set($redisTimeLock, 1, '', 10 * 60);
            echo "本次跳过";
            exit();
        } else {
            $redis->set($redisLock, 1, '', 9 * 60);
        }
        //获取时间标识，要是存在则时间跨度为10分钟，并删除此标识
        $redisTimeRes = $redis->get($redisTimeLock);
        if ($redisTimeRes) {
            $beginTime = date('Y-m-d H:i:s', time() - 10 * 60);
            $redis->del($redisTimeLock);
        }

        //获取获取景区配置列表
        $type    = GovernmentDataSys::GOVERNMENT_SYS_TYPE_SUINING;
        $field   = 'id,apply_id,land_code,land_key,land_name';
        $logPath = 'government/suining/error';
        $govList = $this->_getLandConfList($type, $field, $logPath);

        //获取订单信息
        $orderInfoArr = [];
        if (!empty($govList)) {
            foreach ($govList as $landItem) {
                //景区大于10个进行截取
                $landArrTmp = explode(',', $landItem['land_id']);
                $landArr    = array_chunk($landArrTmp, 10);
                $page       = 1;
                $size       = 1;
                $list       = [];

                foreach ($landArr as $lidArr) {
                    $list = $this->_getOrderInfo($page, $size, $beginTime, $endTime, $lidArr, $landItem['apply_id'],
                        $list);
                    if (!empty($list)) {
                        foreach ($list as $orderInfo) {
                            $orderInfoArr[] = [
                                'ddbh'     => $orderInfo['ordernum'],
                                //订单编号
                                'xdsj'     => date('Y-m-d H:i:s', $orderInfo['orderTime']),
                                //下单时间
                                'zfsj'     => $orderInfo['payTime'] ? date('Y-m-d H:i:s', $orderInfo['payTime']) : '',
                                //支付时间
                                'zfzt'     => $orderInfo['status'] == 1 ? 1 : 0,
                                //支付状态
                                'qprxm'    => $orderInfo['ordername'] ?? "",
                                //姓名
                                'qprsfzh'  => $orderInfo['personid'],
                                //购票人身份证号码
                                'qprsjh'   => $orderInfo['ordertel'] ?? "",
                                //购票人联系电话
                                'jdmc'     => $landItem['land_name'],
                                //景点名称
                                'jddz'     => $landItem['land_area'],
                                //景点地址
                                'secretid' => $landItem['land_key'],
                                //秘钥
                            ];
                        }
                    }
                }
            }
        }

        //推送数据至遂宁
        if (!empty($orderInfoArr)) {
            $biz = new \Business\Government\GovernmentDataPush();
            foreach ($orderInfoArr as $postdataItem) {
                $biz->postDataToSuiNing(1, $postdataItem);
            }
        }

        $redis->del($redisLock);

        echo "success";
        exit();
    }

    /**
     * 遂宁文旅对接 推送入园数据（5分钟）
     * User: Liucm
     * Date: 2020/12/28
     * Time: 10:56
     */
    public function pushCheckForSuiNing()
    {
        //获取获取景区配置列表
        $type    = GovernmentDataSys::GOVERNMENT_SYS_TYPE_SUINING;
        $field   = 'id,apply_id,land_code,land_key,land_name';
        $logPath = 'government/suining/error';
        $govList = $this->_getLandConfList($type, $field, $logPath);

        //获取景区下入园的订单
        $govTourModel    = new GovernmentTouristStatistics();
        $orderQueryModel = new \Model\Order\OrderQuery();
        $postdata        = [];
        foreach ($govList as $landItem) {
            $lidArr = explode(',', $landItem['land_id']);
            if (!empty($lidArr)) {
                foreach ($lidArr as $lItem) {
                    $orderInfo = $govTourModel->getCheckInfosByLandIdAndFidAndFiveMinute($lItem, $landItem['apply_id'],
                        'orders_info,update_time');
                    if (!empty($orderInfo)) {
                        foreach ($orderInfo as $orderItem) {
                            $orders_infoArr = json_decode($orderItem['orders_info']);
                            if (!empty($orders_infoArr)) {
                                $orderIdArr = array_column($orders_infoArr, '0');
                                //获取订单中入园人的具体信息
                                $touristListInfoArr = $orderQueryModel->getTouristList($orderIdArr);
                                foreach ($touristListInfoArr as $touristItem) {
                                    if ($touristItem['voucher_type'] != 1) {
                                        continue;
                                    } else {
                                        $postdata[] = [
                                            'xm'       => $touristItem['tourist'],          //姓名
                                            'sfzhm'    => $touristItem['idcard'] ?? '',     //身份证号码
                                            'jdmc'     => $landItem['land_name'] ?? '',     //景点名称
                                            'jddd'     => $landItem['land_area'] ?? '',     //景点地点
                                            'jrjqsj'   => date('Y-m-d H:i:s', $orderItem['update_time']), //进入时间
                                            'secretid' => $landItem['land_key'],            //秘钥
                                        ];

                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        //推送数据至遂宁
        if (!empty($postdata)) {
            $biz = new \Business\Government\GovernmentDataPush();
            foreach ($postdata as $postdataItem) {
                $biz->postDataToSuiNing(2, $postdataItem);
            }
        }

        echo "success";
        exit();
    }

    /**
     * 获取获取景区配置列表
     * User: Liucm
     * Date: 2020/12/31
     * Time: 17:07
     */
    private function _getLandConfList($type = 0, $field = '*', $logPath = 'government/error')
    {
        if (empty($type)) {
            return [];
        }
        $govModel = new GovernmentDataSys();
        $govList  = $govModel->getGdListByType($type, $field);
        if (empty($govList)) {
            $errorData = [
                'key' => '获取对应配置列表为空',
                'sql' => $govModel->getLastSql(),
            ];
            pft_log($logPath, json_encode($errorData));

            return [];
        }

        $gdsIdArr = [];
        foreach ($govList as $item) {
            $gdsIdArr[] = $item['id'];
        }

        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);

        if (empty($govAttrList)) {
            $errorData = [
                'key' => '获取对应的data_attr表数据为空',
                'sql' => $govModel->getLastSql(),
            ];
            pft_log($logPath, json_encode($errorData));

            return [];
        }

        //将配置列表和配置列表属性整合成同一个列表
        foreach ($govList as &$list) {
            foreach ($govAttrList as $attrList) {
                if ($list['id'] == $attrList['gds_id']) {
                    $list[$attrList['attr_key']] = $attrList['attr_val'];
                }
            }
        }

        return $govList;
    }

    /**
     * 获取获取景区配置列表
     * User: Liucm
     * Date: 2020/12/31
     * Time: 17:07
     */
    private function _getOrderInfo($page, $size, $beginTime, $endTime, $lidArr, $aid, $list = [])
    {
        $orderInfo = new \Business\JavaApi\Order\OrderInfo();
        $result    = $orderInfo->getOrderInfoList($page, $size, $beginTime, $endTime, $lidArr, $aid);

        if ($result['code'] != 200) {
            $data = [
                'key'      => '遂宁获取订单数据错误',
                'request'  => [$page, $size, $beginTime, $endTime, $lidArr, $aid],
                'response' => $result,
            ];
            pft_log('government/suining/error', json_encode($data));

            return $list;
        }

        if ($result['code'] == 200 && !empty($result['data']['list'])) {
            $list = array_merge($result['data']['list'], $list);
        }

        if (count($result['data']['list']) == $size) {
            $page++;

            return $this->_getOrderInfo($page, $size, $beginTime, $endTime, $lidArr, $aid, $list);
        }

        return $list;
    }

    /**
     * 推送朱德故里每日入园实时总数到四川平台
     * <AUTHOR>
     * @date 2021/01/25
     * schedule  10分钟
     */
    public function pushDailyReportToZhuDe()
    {
        $dataCollectionModel = new DataCollection();
        $url                 = "http://zhgl.tsichuan.com/push/big/screen/entrance/guard";     //正式环境地址
        $resourceCode        = 'A51130047852';                                                //资源编码
        $resourceType        = 'scenery';                                                     //资源类别
        $aeskey              = 'js7ksl3nhnfivl4m';                                            //Aes密钥
        $aesIv               = '3859345501849051';                                            //Aes向量
        $pushTime            = date('Y-m-d H:i:s');                                    //推送时间
        $total               = 0;

        //闸机特征码数组 朱德故里
        $deviceKeyArr = [
            'db58f98a8649b9d67c7c9e29aa11af61',
            'cb7a3a555f8ea15f2e97fb875be91cca',
            '32e96eef2ec6e59f3c32eed006e67afc',
            '96e0882ba97c70908b96b2f5ab6a573c',
            '4b86970002b916d6f6e1b1a739e65ec3',
            '326f85e2aed4e888eafb12255ef314ae',
            '3f063ca050317e0fad4563adcf356d5b',
            '9113a295fc817b5ed81dc7fe48be5393',
            'fd288a254b7a4c25341bc6cf31184511',
            'f244aeff98b68a63e59146be9dced301',
            '2c1df38a598736e818e5134f864698fe',
            '5a5d2429a2478e6d2887d784266953ab',
        ];

        //根据闸机特征码匹配当天该景点的入园总量
        $countRes = $dataCollectionModel->getDeviceEnterDataByTime($deviceKeyArr);
        if (!empty($countRes)) {
            foreach ($countRes as $Item) {
                $total += $Item['totalEnterNum'];
            }
        }

        $str = json_encode([
            'total'        => $total,
            'resourceCode' => $resourceCode,
            'sourceType'   => "1",
            'pushTime'     => $pushTime,
            'resourceType' => $resourceType,
        ]);

        //将字符串进行Aes加密
        $encrtptRes = bin2hex(openssl_encrypt($str, 'AES-128-CBC', $aeskey, 1, $aesIv));
        $postdata   = [
            'data' => $encrtptRes,
        ];
        $postdata   = urldecode(http_build_query($postdata));
        $result     = curl_post($url, $postdata, 80, 25, '', [], true, '', 'get');
        pft_log('government/zhude/report',
            json_encode(['ori_data' => $str, 'en_data' => $encrtptRes, 'result' => $result]));
    }

    /**
     * 甘肃省预约数据推送
     * <AUTHOR>
     * @date 2021/03/08
     *
     */
    public function pushReservationDataToGanSu()
    {
        //预约推送地址
        $url = 'https://tms.yougansu.com/tmsopenapi/openapi/ticket/upload';

        $orderApi        = new OrderDetailQuery();
        $reportModel     = new GovernmentTouristStatistics();
        $orderQueryModel = new \Model\Order\OrderQuery();

        $govinfoList = $this->_getLandConfList(18, 'id,apply_id,land_code,land_key');
        if (empty($govinfoList)) {
            pft_log('government/GanSu/error', "甘肃省景点列表为空");

            return;
        }

        foreach ($govinfoList as $landItem) {
            list($name, $password) = explode('|', $landItem['land_key']);
            $landCode = $landItem['land_code'];
            $applyId  = $landItem['apply_id'];
            $token    = $this->getAccessTokenFromGanSu($name, $password);
            if (empty($token)) {
                pft_log('government/GanSu/error', "未获取到Token,name:{$name},pas:{$password}");
                continue;
            }

            $ticketStr = json_decode($landItem['ticketStr'], true);
            if (empty($ticketStr)) {
                continue;
            }

            $tidAndStuArr = array_column($ticketStr, 'tids');
            if (empty($tidAndStuArr)) {
                continue;
            }

            $tidArr = [];
            foreach ($tidAndStuArr as $tItem) {
                foreach ($tItem as $ticket) {
                    if ($ticket['status'] == true) {
                        $tidArr[] = $ticket['tid'];
                    }
                }
            }

            $reserInfo = $reportModel->getReserveOrderNumByTids($tidArr, $applyId);
            if (empty($reserInfo)) {
                continue;
            }

            $orderNumArr = [];
            foreach ($reserInfo as $reItem) {
                $ordersArr = json_decode($reItem['orders_info'], true);
                if (empty($ordersArr)) {
                    continue;
                }

                foreach ($ordersArr as $item) {
                    $orderNumArr[] = $item[0];
                }
            }

            $orderDetailArr = $orderApi->getOrderWithDetail($orderNumArr);
            $touristListArr = $orderQueryModel->getTouristList($orderNumArr);
            //对方不支持批量的数据上报，需要将数据拆成单条发送
            if ($orderDetailArr['code'] == 200 && !empty($orderDetailArr['data'])) {
                foreach ($orderDetailArr['data'] as $detItem) {
                    foreach ($touristListArr as $touItem) {
                        if ($touItem['orderid'] == $detItem['ordernum']) {
                            $sectionTime = json_decode($detItem['fxDetails']['extContent'], true);
                            $startTime   = substr($sectionTime['sectionTimeStr'], 0, 5);
                            $endTime     = substr($sectionTime['sectionTimeStr'], 6, 10);
                            if (empty($startTime) || empty($endTime)) {
                                continue;
                            }
                            $postdata   = json_encode([
                                'token'          => $token,
                                'name'           => empty($touItem['tourist']) ? "***" : $touItem['tourist'],
                                //和对方约定没有的话传默认值星号
                                'tel'            => empty($touItem['mobile']) ? "***" : $touItem['mobile'],
                                //和对方约定没有的话传默认值星号
                                'card'           => empty($touItem['idcard']) ? "***" : $touItem['idcard'],
                                //和对方约定没有的话传默认值星号
                                'orderTime'      => date("Y-m-d H:i:s", $detItem['orderTime']),
                                'reserveNumber'  => "1",
                                'makeDate'       => date('Y-m-d', strtotime($detItem['playtime'])),
                                'startTime'      => empty($startTime) ? "00:00" : $startTime,
                                'endTime'        => empty($endTime) ? "23:00" : $endTime,
                                'scenicSpotCode' => $landCode,
                                'ticketCode'     => (string)$detItem['tid'],
                                'uploadTime'     => date('Y-m-d H:i:s', time()),
                            ]);
                            $header     = ['Content-Type:application/json'];
                            $pushResult = curl_post($url, $postdata, 443, 25, '', $header, true);
                            pft_log('government/GanSu/report',
                                json_encode(['post' => $postdata, 'result' => $pushResult]));
                        }
                    }
                }
            }
        }
    }

    /**
     * 甘肃省预约数据推送
     * <AUTHOR>
     * @date 2021/03/08
     *
     */
    public function pushCheckedDataToGanSu()
    {
        $reportModel     = new GovernmentTouristStatistics();
        $orderQueryModel = new \Model\Order\OrderQuery();

        //核销推送地址
        $url         = 'https://tms.yougansu.com/tmsopenapi/openapi/ticket/uploadPassengerFlow';
        $govinfoList = $this->_getLandConfList(18, 'id,apply_id,land_code,land_key');

        if (empty($govinfoList)) {
            pft_log('government/GanSu/error', "甘肃省景点列表为空");

            return;
        }

        foreach ($govinfoList as $landItem) {
            list($name, $password) = explode('|', $landItem['land_key']);
            $landCode = $landItem['land_code'];
            $applyId  = $landItem['apply_id'];
            $token    = $this->getAccessTokenFromGanSu($name, $password);
            if (empty($token)) {
                pft_log('government/GanSu/error', "未获取到Token,name:{$name},pas:{$password}");
                continue;
            }

            $ticketStr = json_decode($landItem['ticketStr'], true);
            if (empty($ticketStr)) {
                continue;
            }

            $tidAndStuArr = array_column($ticketStr, 'tids');
            if (empty($tidAndStuArr)) {
                continue;
            }

            $tidArr = [];
            foreach ($tidAndStuArr as $tItem) {
                foreach ($tItem as $ticket) {
                    if ($ticket['status'] == true) {
                        $tidArr[] = $ticket['tid'];
                    }
                }
            }

            $checkedInfo = $reportModel->getCheckedOrderNumByTids($tidArr, $applyId);
            if (empty($checkedInfo)) {
                continue;
            }

            $orderNumArr = [];
            $tidInfoMap  = [];
            foreach ($checkedInfo as $reItem) {
                $ordersArr = json_decode($reItem['orders_info'], true);
                if (empty($ordersArr)) {
                    continue;
                }

                foreach ($ordersArr as $item) {
                    $orderNumArr[]         = $item[0];
                    $tidInfoMap[$item[0]]  = $reItem['tid'];
                    $timeInfoMap[$item[0]] = $reItem['update_time'];
                }
            }

            $touristListArr = $orderQueryModel->getTouristList($orderNumArr);
            if (!empty($touristListArr)) {
                foreach ($touristListArr as $touItem) {
                    if ($touItem['check_state'] == 1) {
                        $postdata   = json_encode([
                            'token'          => $token,
                            'scenicSpotCode' => $landCode,
                            'ticketCode'     => $tidInfoMap[$touItem['orderid']] ?? 0,
                            'userCount'      => "1",
                            'userTime'       => empty(date('Y-m-d H:i:s',
                                $timeInfoMap[$touItem['orderid']])) ? date('Y-m-d H:i:s') : date('Y-m-d H:i:s',
                                $timeInfoMap[$touItem['orderid']]),
                            'name'           => empty($touItem['tourist']) ? "***" : $touItem['tourist'],
                            'tel'            => empty($touItem['mobile']) ? "***" : $touItem['mobile'],
                            'card'           => empty($touItem['idcard']) ? "***" : $touItem['idcard'],
                        ]);
                        $header     = ['Content-Type:application/json'];
                        $pushResult = curl_post($url, $postdata, 443, 25, '', $header, true);
                        pft_log('government/GanSu/checked',
                            json_encode(['post' => $postdata, 'result' => $pushResult]));
                    }
                }
            }
        }
    }

    /**
     * 甘肃省token信息获取
     * <AUTHOR>
     * @date 2021/03/08
     *
     */
    public function getAccessTokenFromGanSu($name, $password)
    {
        $cache = Cache::getInstance('redis');
        if (!$cache->get("government:report:token:{$name}")) {
            $tokenUrl = 'https://tms.yougansu.com/tmsopenapi/openapi/ticket/accessToken';
            $postdata = json_encode([
                "name"     => $name,
                "password" => $password,
            ]);
            $header   = ["Content-type:application/json"];
            $tokenRes = curl_post($tokenUrl, $postdata, 443, 25, "", $header);
            $tokenArr = json_decode($tokenRes, true);
            $token    = $tokenArr['data']['token'];
            $cache->set("government:report:token:{$name}", $token, "", 31536000 * 2);
        }

        $token = $cache->get("government:report:token:{$name}");

        return $token;
    }

    /**
     * 遵义会议实名制闸机信息
     * User: Liucm
     * Date: 2021/2/19
     * Time: 14:07
     */
    public function pushCheckForZunYi()
    {
        //获取获取景区配置列表
        $type    = GovernmentDataSys::GOVERNMENT_SYS_TYPE_ZUNYI;
        $field   = 'id,apply_id,land_code';
        $logPath = 'government/zunyi/error';
        $govList = $this->_getLandConfList($type, $field, $logPath);
        //获取景区下入园的订单
        $govTourModel    = new GovernmentTouristStatistics();
        $orderQueryModel = new \Model\Order\OrderQuery();
        $postdata        = [];
        $time            = 60;//拉取1分钟内的数据
        foreach ($govList as $landItem) {
            $lidArr = explode(',', $landItem['land_id']);
            if (!empty($lidArr)) {
                foreach ($lidArr as $lItem) {
                    $orderInfo = $govTourModel->getCheckInfosByLandIdAndFidAndMinute($lItem, $landItem['apply_id'],
                        $time,
                        'orders_info,update_time');
                    if (!empty($orderInfo)) {
                        foreach ($orderInfo as $orderItem) {
                            $orders_infoArr = json_decode($orderItem['orders_info']);

                            if (!empty($orders_infoArr)) {
                                $orderIdArr = array_column($orders_infoArr, '0');
                                //获取订单中入园人的具体信息
                                $touristListInfoArr = $orderQueryModel->getTouristList($orderIdArr);
                                foreach ($touristListInfoArr as $touristItem) {
                                    if ($touristItem['voucher_type'] == 1) {
                                        $certtype = 2;
                                    } elseif ($touristItem['voucher_type'] == 2) {
                                        $certtype = 'I';
                                    } elseif ($touristItem['voucher_type'] == 5) {
                                        $certtype = 'J';
                                    } else {
                                        continue;
                                    }
                                    $postdata[] = [
                                        'deviceid' => $landItem['land_code'], //设备ID
                                        'sfzh'     => $touristItem['idcard'] ?? '', //身份证号码
                                        'xm'       => $touristItem['tourist'],  //姓名
                                        'readtime' => date('Y-m-d H:i:s', $orderItem['update_time']), //进入时间
                                        'certtype' => $certtype,
                                    ];

                                }
                            }
                        }
                    }
                }
            }
        }
        //推送数据至遵义
        if (!empty($postdata)) {
            $biz = new \Business\Government\GovernmentDataPush();
            foreach ($postdata as $postdataItem) {
                $biz->postDataToZunYi($postdataItem);
            }
        }

        echo "success";
        exit();
    }

    /**
     * 磁器口对接 推送入园数据（5分钟）
     * User: qiuxi
     * Date: 2021/04/12
     * Time: 15:56
     */
    public function pushCheckForCiQiKou()
    {
        //获取获取景区配置列表
        $lidArr = [
            ['apply_id' => '6305934', 'land_name' => '重庆磁器口景区', 'land_id' => '198484'],
        ];
        //获取景区下入园的订单
        $govTourModel    = new GovernmentTouristStatistics();
        $orderQueryModel = new \Model\Order\OrderQuery();
        $postdata        = [];
        if (!empty($lidArr)) {
            foreach ($lidArr as $lItem) {
                $orderInfo = $govTourModel->getCheckInfosByLandIdAndFidAndFiveMinute($lItem['land_id'],
                    $lItem['apply_id'],
                    'orders_info,update_time');
                if (!empty($orderInfo)) {
                    foreach ($orderInfo as $orderItem) {
                        $orders_infoArr = json_decode($orderItem['orders_info']);
                        if (!empty($orders_infoArr)) {
                            $orderIdArr = array_column($orders_infoArr, '0');
                            //获取订单中入园人的具体信息
                            $touristListInfoArr = $orderQueryModel->getTouristList($orderIdArr);
                            $orderInfoMap  = [];
                            if (!empty($touristListInfoArr)) {
                                // 获取订单信息
                                $orderInfoList = (new OrderTools())->getOrderInfo(array_unique(array_column($touristListInfoArr, 'orderid')),
                                    'ordernum,ordertel,playtime,ordertime');
                                $orderInfoList = json_decode($orderInfoList, true);
                                $orderInfoList = $orderInfoList['data'];
                                foreach ($orderInfoList as $item) {
                                    if (!isset($orderInfoMap[$item['ordernum']])) {
                                        $orderInfoMap[$item['ordernum']] = [];
                                    }
                                    $orderInfoMap[$item['ordernum']]['playtime']  = $item['playtime'];
                                    $orderInfoMap[$item['ordernum']]['ordertel']  = $item['ordertel'];
                                    $orderInfoMap[$item['ordernum']]['ordertime'] = $item['ordertime'];
                                }
                                // 获取预约时段
                                $timeRes = (new OrderTools())->getOrderDetailInfo(array_unique(array_column($touristListInfoArr, 'orderid')), 'orderid,ext_content');
                                if (!empty($timeRes)) {
                                    $timeRes = json_decode($timeRes, true);
                                }
                                if (!empty($timeRes['data']) && $timeRes['code'] == 200) {
                                    foreach ($timeRes['data'] as $item) {
                                        $extContent = !empty($item['ext_content']) ? json_decode($item['ext_content'], true) : [];
                                        if (isset($extContent['sectionTimeStr'])) {
                                            if (!isset($orderInfoMap[$item['orderid']])) {
                                                $orderInfoMap[$item['orderid']] = [];
                                            }
                                            $orderInfoMap[$item['orderid']]['sectionTimeStr'] = $extContent['sectionTimeStr'];
                                        }
                                    }
                                }
                            }
                            foreach ($touristListInfoArr as $touristItem) {
                                if ($touristItem['voucher_type'] != 1) {
                                    continue;
                                } else {
                                    if ($touristItem['check_state'] == 0) {
                                        $touristItem['check_state'] = '未验证';
                                    } else if ($touristItem['check_state'] == 1) {
                                        $touristItem['check_state'] = '已验证';
                                    } else if ($touristItem['check_state'] == 2) {
                                        $touristItem['check_state'] = '已取消';
                                    } else if ($touristItem['check_state'] == 4) {
                                        $touristItem['check_state'] = '过期';
                                    } else if ($touristItem['check_state'] == 7) {
                                        $touristItem['check_state'] = '部分验证';
                                    } else {
                                        $touristItem['check_state'] = '已取消';
                                    }

                                    $yysd = $orderInfoMap[$touristItem['orderid']]['sectionTimeStr'] ?? ''; // 预约时段
                                    if (empty($yysd)) {
                                        $datetime = date('H');
                                        if ($datetime < 12) {
                                            $yysd = '上午';
                                        } else {
                                            $yysd = '下午';
                                        }
                                    }

                                    // XXLRSJ 订单数据入库时间
                                    // YYSJ 是游客下单时间
                                    // YYCGRQ 是游客预计游玩的时间

                                    $postdata[] = [
                                        'JDMC'     => $lItem['land_name'] ?? '',     // 景点名称
                                        'SJSSQX'   => '500106',
                                        'XM'       => $touristItem['tourist'] ?? '',          // 姓名
                                        'ZJHM'     => $touristItem['idcard'] ?? '',      // 身份证号码
                                        'LXDH'     => $orderInfoMap[$touristItem['orderid']]['ordertel'] ?? '',      // 联系电话
                                        'YYSJ'     => isset($orderInfoMap[$touristItem['orderid']]['ordertime']) ? date('YmdHis', strtotime($orderInfoMap[$touristItem['orderid']]['ordertime'])) : '', // 预约时间-  游客下单时间
                                        'DDBH'     => $touristItem['orderid'] ?? '',   // 订单编号
                                        'YYCGRQ'   => isset($orderInfoMap[$touristItem['orderid']]['playtime']) ? date('Ymd', strtotime($orderInfoMap[$touristItem['orderid']]['playtime'])) : '',   // 预约参观时间
                                        'YYSD'     => $yysd,
                                        'YYZT'     => $touristItem['check_state'], // 预约状态
                                        'XXLRSJ'   => isset($orderInfoMap[$touristItem['orderid']]['ordertime']) ? date('YmdHis', strtotime($orderInfoMap[$touristItem['orderid']]['ordertime'])) : '', //进入时间
                                        'XJ_ID'    => '500106', // 上报数据单位
                                        'SJLY'     => '福建票付通信息科技有限公司', // 数据来源
                                        'GSZCH'    => '91350100097974396N', // 注册工商号
                                    ];

                                }
                            }
                        }
                    }
                }
            }
        }
        //推送数据至遂宁
        if (!empty($postdata)) {
            $biz = new \Business\Government\GovernmentDataPush();
            foreach ($postdata as $postdataItem) {
                $biz->postDataToCQK($postdataItem);
            }
        }

        echo "success";
        exit();
    }

    /**
     * 上海文旅对接 更新景点实时客流情况
     * <AUTHOR>
     * @date 2021-05-13
     */
    public function pushToShangHaiWenLvUpdatePassengerInfo()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel = new GovernmentDataSys();
        $field    = 'id,apply_id,land_code,land_key';
        $govList  = $govModel->getGdListByType(21, $field);
        if (empty($govList)) {
            pft_log('government/ShangHai/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());
            return;
        }

        $client = new PftSoapClient($this->ShangHaiUrl, [], 10, false);
        foreach ($govList as $item) {
            $id = $item['id'];
            $govAttrList = $govModel->getAttrByGdsId($id);
            if (empty($govAttrList)) {
                pft_log('government/ShangHai/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());
                continue ;
            }

            $comfort = json_decode($govAttrList['comfort'], true); // 舒适度
            $deviceInfo = json_decode($govAttrList['device_info'], true);
            if (empty($comfort) || empty($deviceInfo)) {
                pft_log('government/ShangHai/error', '扩展属性不存在' . $govModel->getLastSql());
                continue ;
            }

            $deviceKeyArr = array_column($deviceInfo, 'device_key'); // 闸机特征码
            if (empty($deviceKeyArr)) {
                pft_log('government/ShangHai/error', '闸机特征码不存在' . $govModel->getLastSql());
                continue ;
            }

            // 查询入园人数和离园人数
            $dataCollectionModel = new DataCollection();
            $inRes = $dataCollectionModel->getDeviceEnterDataByTime($deviceKeyArr);
            $leaveRes = $dataCollectionModel->getDeviceLeaveDataByTime($deviceKeyArr);
            $inNum = array_sum(array_column($inRes, 'totalEnterNum')); // 入园人数
            $leaveNum = array_sum(array_column($leaveRes, 'totalLeaveNum')); // 离园人数
            $num = ($inNum - $leaveNum) < 0 ? 0 : ($inNum - $leaveNum); // 在园人数 = 入园 - 离园
            // 舒适度格式化
            $comfortFormat = [];
            foreach ($comfort as $value) {
                $comfortFormat[$value['num']] = $value['title'];
            }
            $ssd = '';
            if (isset($comfortFormat[$num])) {
                $ssd = $comfortFormat[$num];
            } else {
                $comfortFormat[$num] = '';
                ksort($comfortFormat);
                $count = 0;
                foreach ($comfortFormat as $key => $value) {
                    if ($key == $num) {
                        if ($count == 0) {
                            $ssd = next($comfortFormat);
                        }
                        break;
                    }
                    $ssd = $value;
                    $count++;
                }
            }
            $time = date('Y-m-d H:i', time()); // 时间
            $username = trim($item['land_code']);
            $password = trim($item['land_key']);
            $xmlData = <<<XML
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:UpdatePassengerInfo>
         <tem:username>$username</tem:username>
         <tem:password>$password</tem:password>
         <tem:num>$num</tem:num>
         <tem:ssd>$ssd</tem:ssd>
         <tem:time>$time</tem:time>
      </tem:UpdatePassengerInfo>
   </soapenv:Body>
</soapenv:Envelope>
XML;
            $pushResult = $client->__doRequest($xmlData, $this->ShangHaiUrl, 'UpdatePassengerInfo', 1, 0);
            $logData = array(
                'request' => $xmlData,
                'response' => $pushResult,
            );
            pft_log('government/ShangHai/realpeoplenum', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        }
    }

    /**
     * 上海文旅对接 更新今日景点客流总数
     * <AUTHOR>
     * @date 2021-05-24
     */
    public function pushToShangHaiWenLvUpdateTodayPassenger()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel = new GovernmentDataSys();
        $field    = 'id,apply_id,land_code,land_key';
        $govList  = $govModel->getGdListByType(21, $field);
        if (empty($govList)) {
            pft_log('government/ShangHai/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());
            return;
        }

        $client = new PftSoapClient($this->ShangHaiUrl, [], 10, false);
        foreach ($govList as $item) {
            $id = $item['id'];
            $govAttrList = $govModel->getAttrByGdsId($id);
            if (empty($govAttrList)) {
                pft_log('government/ShangHai/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());
                continue ;
            }

            $deviceInfo = json_decode($govAttrList['device_info'], true);
            if (empty($deviceInfo)) {
                pft_log('government/ShangHai/error', '扩展属性不存在' . $govModel->getLastSql());
                continue ;
            }

            $deviceKeyArr = array_column($deviceInfo, 'device_key'); // 闸机特征码
            if (empty($deviceKeyArr)) {
                pft_log('government/ShangHai/error', '闸机特征码不存在' . $govModel->getLastSql());
                continue ;
            }

            // 查询入园人数和离园人数
            $dataCollectionModel = new DataCollection();
            $inRes = $dataCollectionModel->getDeviceEnterDataByTime($deviceKeyArr);
            $num = array_sum(array_column($inRes, 'totalEnterNum')); // 入园人数
            $time = date('Y-m-d H:i', time()); // 时间
            $username = trim($item['land_code']);
            $password = trim($item['land_key']);
            $xmlData = <<<XML
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:UpdateTodayPassenger>
         <tem:username>$username</tem:username>
         <tem:password>$password</tem:password>
         <tem:num>$num</tem:num>
         <tem:time>$time</tem:time>
      </tem:UpdateTodayPassenger>
   </soapenv:Body>
</soapenv:Envelope>
XML;
            $pushResult = $client->__doRequest($xmlData, $this->ShangHaiUrl, 'UpdateTodayPassenger', 1, 0);
            $logData = array(
                'request' => $xmlData,
                'response' => $pushResult,
            );
            pft_log('government/ShangHai/allpeoplenum', json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        }
    }

    /**
     * 江西省上报分时预约数据
     * <AUTHOR>
     * @date 2021-06-08
     */
    public function pushDataForJiangXiFenShi()
    {
        // 获取需要对接到对方平台的景区数据
        $govModel            = new GovernmentDataSys();
        $dataCollectionModel = new DataCollection();
        $landStorageBiz      = new LandStorage();
        $field     = 'id,apply_id,land_code,land_key,land_name';
        $govList   = $govModel->getGdListByType(22, $field);
        if (empty($govList)) {
            pft_log('government/JiangXiFenShi/error', '获取对应配置列表为空, sql:' . $govModel->getLastSql());
            return;
        }

        $govListMap = [];
        foreach ($govList as $item) {
            $govListMap[$item['id']] = $item;
        }

        // 获取gov id -> 获取government_data_attr
        $gdsIdArr    = array_column($govList, 'id');
        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);
        if (empty($govAttrList)) {
            pft_log('government/JiangXiFenShi/error', '获取对应的data_attr表数据为空, sql:' . $govModel->getLastSql());
            return;
        }

        $govAttrListMap = [];
        foreach ($govAttrList as $item) {
            $govAttrListMap[$item['gds_id']][$item['attr_key']] = $item['attr_val'];
        }

        // lid => info
        $govWithAttrListMap = [];
        $lidArr             = [];
        foreach ($govAttrListMap as $key => $value) {
            $govWithAttrListMap[$value['land_id']] = array_merge($value, $govListMap[$key]);
            $lidArr[] = intval($value['land_id']);
        }

        if (empty($govAttrListMap)){
            pft_log('government/JiangXiFenShi/error', json_encode(['value'=>$value,'govAttrlist'=>$govAttrListMap]));
            return;
        }

        if (empty($lidArr)){
            pft_log('government/JiangXiFenShi/empty', json_encode(['govAttrlist'=>$govWithAttrListMap]));
            return;
        }

        $noTimeShareLid = [
            //婺源产品
            231798,
            231800,
            231796,
            231792,
            231788,
            231776,
            225340,
            225341,

            //三百山产品
            347434,
            274681,
        ];

        $lidArr    = array_diff($lidArr, $noTimeShareLid);
        $startTime = date('Y-m-d');
        $endTime   = date('Y-m-d', strtotime('+7 days'));
        $timeToday = date('Y-m-d 00:00:01');
        $timeTodayNow = date('Y-m-d H:i:s',time());
        $timeShareOrdeStoreApi = new TimeShareOrdeStore();
        $lidArrChunk = array_chunk($lidArr, 5);
        $timeShareData = [];
        foreach ($lidArrChunk as $value) {
            $timeShareRes = $timeShareOrdeStoreApi->querySectionTimeSetRecordForDetail($value, $startTime, $endTime);
            if ($timeShareRes['code'] == 200 && !empty($timeShareRes['data'])) {
                $timeShareData = array_merge($timeShareData, $timeShareRes['data']);
            }
        }

        // 不开启分时预约的特殊景点id
        foreach ($noTimeShareLid as $ntItem) {
            //打个日志记录一下java的数据正不正常
            $staQueryTime = time();
            $noTimeShareStoInfo = $landStorageBiz->queryLandStorageByLandId($ntItem, $startTime, $endTime);
            pft_log('government/JiangXiFenShi/JavaQuery', json_encode([
                'start'    => $staQueryTime,
                'end'      => time(),
                'params'   => [$ntItem, $startTime, $endTime],
                'response' => $noTimeShareStoInfo,
            ]));
            if ($noTimeShareStoInfo['code'] == 200 && !empty($noTimeShareStoInfo['data'])) {
                $noTimeShareStoArr = [
                    'lid'  => $ntItem,
                    'list' => [],
                ];
                foreach ($noTimeShareStoInfo['data'] as $dateList) {
                    $noTimeShareStoArr['list'][] = [
                        'date'     => $dateList['date'],
                        'dateInfo' => [[
                            'bookNum'   => $dateList['saleStorage'],
                            'remainNum' => $dateList['storage'] == -1 ? 99999 : $dateList['storage'],
                            'label'     => "00:00-23:59",
                            'startTime' => '0000',
                            'endTime'   => '2359',
                        ]],
                    ];
                }

                array_push($timeShareData, $noTimeShareStoArr);
            }
        }
        $pushdata = [];
        foreach ($govWithAttrListMap as $landItem) {
            foreach ($timeShareData as $timeShareItem) {
                if ($landItem['land_id'] == $timeShareItem['lid']) {
                    foreach ($timeShareItem['list'] as $timelistItem) {
                        foreach ($timelistItem['dateInfo'] as $dateInfoItem) {
                            $datedata[$timelistItem['date']][] = [
                                'label'      => $dateInfoItem['label'],
                                'book_num'   => $dateInfoItem['bookNum'],
                                'remain_num' => $dateInfoItem['remainNum'],
                                'start_time' => strtotime("{$timelistItem['date']}.{$dateInfoItem['startTime']}"),
                                'end_time'   => strtotime("{$timelistItem['date']}.{$dateInfoItem['endTime']}"),
                            ];
                        }
                    }
                }
            }

            if (!empty($datedata)) {
                //$enterNumArr = $dataCollectionModel->getEnterData($timeToday,$timeTodayNow,$landItem['land_id']);
                //$leaveNumArr = $dataCollectionModel->getLeaveData($timeToday,$timeTodayNow,$landItem['land_id']);
                //
                //if (empty($enterNumArr[0]['cnt'])){
                //    $enterNum = 0;
                //}
                //
                //if (empty($leaveNumArr[0]['cnt'])){
                //    $leaveNum = 0;
                //}

                switch ($landItem['land_status']){
                    case '0':
                        $notice = '闭园';
                        break;
                    case '1':
                        $notice = '正常开放';
                        break;
                    case '2':
                        $notice = '限流预警';
                        break;
                    case '3':
                        $notice = '停止入园';
                        break;
                    default:
                        $notice = '正常开放';
                }

                $pushdata[] = [
                    'scenic_id'         => $landItem['land_code'],
                    'book_list'         => [$datedata],
                    'scenic_status'     => $landItem['land_status'],
                    'notice'            => $notice,
                    'secret'            => $landItem['secret'] ?? '',
                    //'realtime_tourists' => $enterNum - $leaveNum,
                    //'total_tourists'    => $enterNum?? 0,
                ];
            }
            unset($datedata);
        }

        $header   = ['Content-Type: application/json', "appKey: {$landItem['land_key']}"];
        $pushdata = json_encode($pushdata);
        $response = curl_post('https://wljg.dct.jiangxi.gov.cn/upload-data/scenicReserveData/bookTravelerData', $pushdata, 80, 25 ,'',
            $header,true);

        //记录日志
        pft_log('government/JiangXiFenShi/data',json_encode(['post'=>$pushdata,'res'=>$response,'header'=>$header]));
    }

    /**
     * 北戴河预约数据推送
     * <AUTHOR>
     * @date 2021/07/28
     *
     */
    public function pushReservationDataToBeiDaiHe()
    {
        //token获取地址
        $tokenUrl = "https://dc.beidaihe.gov.cn/bdh/token";
        //预约数据推送地址
        $pushUrl  = "https://dc.beidaihe.gov.cn/bdh/scenic/order";

        $reportModel     = new GovernmentTouristStatistics();
        $orderQueryModel = new \Model\Order\OrderQuery();
        $govinfoList     = $this->_getLandConfList(24, 'id,apply_id,land_code,land_key,land_name');
        if (empty($govinfoList)) {
            pft_log('government/BeiDaiHe/error', "北戴河景点配置列表为空");
            return;
        }

        foreach ($govinfoList as $landItem) {
            $appid             = $landItem['land_code'];
            $secret            = $landItem['land_key'];
            $applyId           = $landItem['apply_id'];
            $landWithTicketArr = json_decode($landItem['land_tickets'], true);
            if (empty($landWithTicketArr)) {
                pft_log('government/BeiDaiHe/error',
                    "北戴河景点{$landItem['land_name']}配置景点门票信息有误" . json_encode($landItem, JSON_UNESCAPED_UNICODE));
                continue;
            }

            $tidArr = [];
            foreach ($landWithTicketArr as $ticketItem) {
                $tidArr = array_merge(array_column($ticketItem['tids'],'id'), $tidArr);
            }

            if (empty($tidArr)) {
                pft_log('government/BeiDaiHe/error',
                    "北戴河景点{$landItem['land_name']}配置景点门票为空" . json_encode($landItem, JSON_UNESCAPED_UNICODE));
                continue;
            }

            $reserInfo = $reportModel->getReserveOrderNumByTids($tidArr, $applyId);
            if (empty($reserInfo)) {
                pft_log('government/BeiDaiHe/error',
                    "北戴河景点{$landItem['land_name']}获取订单列表为空" . $reportModel->getLastSql());
                continue;
            }

            $orderNumArr = [];
            foreach ($reserInfo as $reItem) {
                $ordersArr = json_decode($reItem['orders_info'], true);
                if (empty($ordersArr)) {
                    continue;
                }

                foreach ($ordersArr as $item) {
                    $orderNumArr[] = $item[0];
                }
            }

            $touristListArr = $orderQueryModel->getTouristList($orderNumArr);
            //对方不支持批量的数据上报，需要将数据拆成单条发送
            foreach ($touristListArr as $tourItem) {
                $token = $this->getAccessTokenFromBeiDaiHe($appid, $secret, $tokenUrl);
                if (!empty($tourItem['voucher_type']) && $tourItem['voucher_type'] == 1) {
                    $postdata   = json_encode([
                        'name'       => $tourItem['tourist'],
                        'idcard'     => $tourItem['idcard'],
                        'idcardType' => "身份证",                 //对方仅支持身份证上传
                        'phone'      => $tourItem['mobile'],
                        'scenicName' => $landItem['land_name'],
                    ]);
                    $header     = ['Content-Type:application/json'];
                    $url        = $pushUrl."?access_token={$token}";
                    $pushResult = curl_post($url, $postdata, 80, 25, '', $header, true);
                }
                pft_log('government/GanSu/report', json_encode(['post' => $postdata, 'result' => $pushResult]));
            }
        }
    }

    /**
     * 北戴河入园数据推送
     * <AUTHOR>
     * @date 2021/07/28
     *
     */
    public function pushCheckedDataToBeiDaiHe()
    {
        //token获取地址
        $tokenUrl = "https://dc.beidaihe.gov.cn/bdh/token";
        //入园数据推送地址
        $pushUrl = "https://dc.beidaihe.gov.cn/bdh/scenic/enter";

        $reportModel     = new GovernmentTouristStatistics();
        $orderQueryModel = new \Model\Order\OrderQuery();
        $govinfoList     = $this->_getLandConfList(24, 'id,apply_id,land_code,land_key,land_name');
        if (empty($govinfoList)) {
            pft_log('government/BeiDaiHe/error', "北戴河景点配置列表为空");

            return;
        }

        foreach ($govinfoList as $landItem) {
            $appid             = $landItem['land_code'];
            $secret            = $landItem['land_key'];
            $applyId           = $landItem['apply_id'];
            $landWithTicketArr = json_decode($landItem['land_tickets'], true);
            if (empty($landWithTicketArr)) {
                pft_log('government/BeiDaiHe/error',
                    "北戴河景点{$landItem['land_name']}配置景点门票信息有误" . json_encode($landItem, JSON_UNESCAPED_UNICODE));
                continue;
            }

            $tidArr = [];
            foreach ($landWithTicketArr as $ticketItem) {
                $tidArr = array_merge(array_column($ticketItem['tids'],'id'), $tidArr);
            }
            if (empty($tidArr)) {
                pft_log('government/BeiDaiHe/error',
                    "北戴河景点{$landItem['land_name']}配置景点门票为空" . json_encode($landItem, JSON_UNESCAPED_UNICODE));
                continue;
            }

            $reserInfo = $reportModel->getCheckedOrderNumByTids($tidArr, $applyId);
            if (empty($reserInfo)) {
                pft_log('government/BeiDaiHe/error',
                    "北戴河景点{$landItem['land_name']}获取订单列表为空" . $reportModel->getLastSql());
                continue;
            }

            $orderNumArr = [];
            foreach ($reserInfo as $reItem) {
                $ordersArr = json_decode($reItem['orders_info'], true);
                if (empty($ordersArr)) {
                    continue;
                }

                foreach ($ordersArr as $item) {
                    $orderNumArr[] = $item[0];
                }
            }

            $touristListArr = $orderQueryModel->getTouristList($orderNumArr);
            //对方不支持批量的数据上报，需要将数据拆成单条发送
            foreach ($touristListArr as $tourItem) {
                $token = $this->getAccessTokenFromBeiDaiHe($appid, $secret, $tokenUrl);
                if (!empty($tourItem['voucher_type']) && $tourItem['voucher_type'] == 1) {
                    $postdata   = json_encode([
                        'name'       => $tourItem['tourist'],
                        'idcard'     => $tourItem['idcard'],
                        'idcardType' => "身份证",                 //对方仅支持身份证上传
                        'phone'      => $tourItem['mobile'],
                        'scenicName' => $landItem['land_name'],
                    ]);
                    $header     = ['Content-Type:application/json'];
                    $url        = $pushUrl . "?access_token={$token}";
                    $pushResult = curl_post($url, $postdata, 80, 25, '', $header, true);
                    $resultArr  = json_decode($pushResult, true);
                    if ($resultArr['returnCode'] == 0) {
                        $cache    = Cache::getInstance('redis');
                        $time     = date('Y-m-d H:i', time() + $landItem['leave_time']);
                        $cacheKey = "government:report:beidaihe:leave:{$time}";
                        if (!$cache->get($cacheKey)) {
                            $data = json_encode([
                                [
                                    'appid'  => $appid,
                                    'secret' => $secret,
                                    'data'   => $postdata,
                                ],
                            ]);
                            $cache->set($cacheKey, $data, '', $landItem['leave_time'] + 1000);
                        } else {
                            $oldData = $cache->get($cacheKey);
                            $oldData = json_decode($oldData, true);
                            $newData = [
                                'appid'  => $appid,
                                'secret' => $secret,
                                'data'   => $postdata,
                            ];
                            array_push($oldData, $newData);
                            $cache->set($cacheKey, json_encode($oldData), '', $landItem['leave_time'] + 1000);
                        }
                    }
                    pft_log('government/BeiDaiHe/pushResult',
                        json_encode(['post' => $postdata, 'result' => $pushResult]));
                } else {
                    pft_log('government/BeiDaiHe/pushError',
                        json_encode(['post' => json_encode($tourItem), 'result' => '出行人证件类型不为身份证,取消推送']));
                }
            }
        }
    }

    /**
     * 北戴河出园数据推送
     * <AUTHOR>
     * @date 2021/07/28
     *
     */
    public function pushLeaveDataToBeiDaiHe()
    {
        //token获取地址
        $tokenUrl = "https://dc.beidaihe.gov.cn/bdh/token";
        //出园数据推送地址
        $pushUrl       = "https://dc.beidaihe.gov.cn/bdh/scenic/leave";
        $cache         = Cache::getInstance('redis');
        $time          = date('Y-m-d H:i');
        $cacheKey      = "government:report:beidaihe:leave:{$time}";
        $minuteDataArr = json_decode($cache->get($cacheKey), true);
        if (!empty($minuteDataArr)) {
            foreach ($minuteDataArr as $pushItem) {
                $token      = $this->getAccessTokenFromBeiDaiHe($pushItem['appid'], $pushItem['secret'], $tokenUrl);
                $postdata   = $pushItem['data'];
                $header     = ['Content-Type:application/json'];
                $url        = $pushUrl . "?access_token={$token}";
                $pushResult = curl_post($url, $postdata, 80, 25, '', $header, true);
                pft_log('government/BeiDaiHe/pushLeaveResult',
                    json_encode(['post' => $postdata, 'result' => $pushResult]));
            }

            return;
        }
        pft_log('government/BeiDaiHe/pushLeaveEmptyResult',
            json_encode(['key' => $cacheKey, 'data' => $cache->get($cacheKey), 'result' => '该时段数据为空']));
    }

    /**
     * 北戴河token获取
     * <AUTHOR>
     * @date 2021/7/28
     *
     * @param $appid
     * @param $secret
     *
     * @return string
     */
    private function getAccessTokenFromBeiDaiHe($appid, $secret, $url)
    {
        $cache    = Cache::getInstance('redis');
        $cacheKey = "government:report:beidaihe:{$appid}";
        $data     = [
            "appid"  => $appid,
            "secret" => $secret,
        ];
        $data     = http_build_query($data);
        if (!$cache->get($cacheKey)) {
            $tokenString = curl_post($url, $data, 80, 25, '/api/curl_post', [], true, '', 'get');
            $tokenArr    = json_decode($tokenString, true);
            $cache->set($cacheKey, $tokenArr['access_token'], '', $tokenArr['expires_in']);
            $token = $tokenArr['access_token'];
        } else {
            $token       = $cache->get($cacheKey);
            $leftSeconds = $cache->ttl($cacheKey);
            if ($leftSeconds <= 1) {
                sleep(1);
                $tokenString = curl_post($url, $data, 80, 25, '/api/curl_post', [], true, '', 'get');
                $tokenArr    = json_decode($tokenString, true);
                $cache->set($cacheKey, $tokenArr['access_token'], '', $tokenArr['expires_in']);
                $token = $tokenArr['access_token'];
            }
        }

        return $token;
    }
}
