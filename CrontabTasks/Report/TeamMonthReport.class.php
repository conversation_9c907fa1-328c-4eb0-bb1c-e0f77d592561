<?php
/**
 * 团单预定、验证月报表统计
 */

namespace CrontabTasks\Report;

use Library\Controller;


//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class TeamMonthReport extends Controller
{

    private $_logPath = 'team_month_statistic';

    public function __construct()
    {
        //运行时间不做限制
        set_time_limit(0);
    }

    /**
     * /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/TeamMonthReport runMonthTaskV2
     * 月报表任务
     * <AUTHOR>
     */
    public function runMonthTaskV2()
    {
        $params = $GLOBALS['argv'];

        if (isset($params[4])) {
            $tmp = strtotime($params[4]);
            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }
            $date = date('Ym', $tmp);
        } else {
            //统计前一月的数据
            $date = date('Ym', strtotime('-1 month'));
        }
        $taskTypeStr = $params[3] ?? '';
        if (!empty($taskTypeStr)) {
            switch ($taskTypeStr) {
                //预定月报表
                case 'team_order_month':
                    $orderMonthBusiness = new \Business\Statistics\CreateTeamOrderMonthReport($this->_logPath);
                    $orderMonthBusiness->runTask($date);
                    break;
                //验证月报表
                case 'team_check_month':
                    $checkMonthBusiness = new \Business\Statistics\CreateTeamCheckedMonthReport($this->_logPath);
                    $checkMonthBusiness->runTask($date);
                    break;
                case 'all':
                    $orderMonthBusiness = new \Business\Statistics\CreateTeamOrderMonthReport($this->_logPath);
                    $orderMonthBusiness->runTask($date);
                    sleep(10);
                    $checkMonthBusiness = new \Business\Statistics\CreateTeamCheckedMonthReport($this->_logPath);
                    $checkMonthBusiness->runTask($date);
                    break;
            }
        } else {
            $orderMonthBusiness = new \Business\Statistics\CreateTeamOrderMonthReport($this->_logPath);
            $orderMonthBusiness->runTask($date);
            sleep(10);
            $checkMonthBusiness = new \Business\Statistics\CreateTeamCheckedMonthReport($this->_logPath);
            $checkMonthBusiness->runTask($date);
        }
        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }
}