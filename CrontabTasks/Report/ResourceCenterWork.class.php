<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 2019/1/7
 * Time: 16:56
 */

namespace CrontabTasks\Report;

use Business\JavaApi\Product\ResourceCenterApi;
use Business\JavaApi\Resource\ResourceEvolute;
use Library\Controller;
use Model\Report\Statistics;


if(!defined('PFT_CLI')) {
    exit('Access Deny');
}


class ResourceCenterWork extends Controller
{
    private $_staticModel;

    private $_rcBizApi;

    private $_resourceEvoluteApi;

    private $_size = 500;

    /***
     * 资源中心计算合作的分销商的近一个月销售量
     * @author: <PERSON><PERSON>
     * @date: 2019/1/11
     */
    public function calDistributorsSale() {
        //批量获取fid aid pid
        //summaryDB中进行批量查询30天数据
        $staticModel        = $this->_getStaticModel();
        $rcBizApi           = $this->_getRCBizApi();
        $resourceEvoluteApi = $this->_getResourceEvoluteApi();

        //预先获取，确认数据总数，循环处理次数
        $result = $resourceEvoluteApi->queryAllResourceEvoluteByPaging(1, 1, 1);
        $result = $result['data'] ?? [];
        if ($result && $result['total']) {
            $endTime   = strtotime(date('Ymd', time() - 3600 * 24 * 1));//根据运行时间 看要隔一天还是隔两天
            $beginTime = $endTime - 3600 * 24 * 30;
            $total  = (int)ceil($result['total'] / $this->_size);

            for ($page = 1; $page <= $total; $page++) {
                $data = [];
                $query = $resourceEvoluteApi->queryAllResourceEvoluteByPaging(1, $page, $this->_size);
                $query = $query['data'] ?? [];
                if ($query && $query['list']) {

                    $list = $query['list'];
                    foreach ($list as $item) {

                        $saleInfo = $staticModel->getRecenterSaleDataBySupplier($beginTime, $endTime, $item['sid'], $item['fid'], true);

                        if ($saleInfo) {
                            if (isset($saleInfo['money'])) {
                                $saleInfo['saleroomRecent'] = intval($saleInfo['money']);
                                unset($saleInfo['money']);
                            }
                            $data[] = $saleInfo;
                        }
                    }

                    $resourceEvoluteApi->saveResourceEvolute(1, $data);
                }

            }

        }

    }

    /***
     * 获取报表模型
     * @author: Cai Yiqiang
     * @date: 2019/1/11
     * @return Statistics
     */
    private function _getStaticModel() {
        if (!$this->_staticModel) {
            $this->_staticModel = new Statistics();
        }

        return $this->_staticModel;
    }

    /***
     * 获取资源中心接口业务
     * @author: Cai Yiqiang
     * @date: 2019/1/16
     * @return ResourceCenterApi
     */
    private function _getRCBizApi() {
        if (!$this->_rcBizApi) {
            $this->_rcBizApi = new ResourceCenterApi();
        }

        return $this->_rcBizApi;
    }

    /**
     * 资源中心分销关系服务api
     * <AUTHOR>
     * @date 2022/1/12
     *
     * @return ResourceEvolute
     */
    private function _getResourceEvoluteApi() {
        if (empty($this->_resourceEvoluteApi)) {
            $this->_resourceEvoluteApi = new ResourceEvolute();
        }

        return $this->_resourceEvoluteApi;
    }

}