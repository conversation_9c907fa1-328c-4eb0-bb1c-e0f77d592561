<?php
/**
 * 大面板数据的脚本
 * 使用方法 php /Service/Crontab/run.php cli_BigDataPanelScript all 2017-04-05
 * all  => 执行全部任务
 * book => 在线预订占比任务
 *
 *
 * 时间参数可选，未指定则为脚本执行日期前一天
 *
 * <AUTHOR>
 * @date   2017-4-5
 */

namespace CrontabTasks\Report;

use Business\JavaApi\CommodityCenter\Product;
use Business\JavaApi\Member\MemberExtQuery;
use Business\JavaApi\Member\MemberQuery;
use Business\Order\OrderQueryJavaService;
use Library\Cache\Cache;
use Library\Tools\Helpers;
use Model\BigDataPanel;
use Model\Member\Member;
use Model\Order\BuyChain;
use Model\Order\OrderQuery;
use Model\Product\Land;
use Model\Report\Statistics;
use Business\Report\BigDataPanel as BigDataPanelBiz;
use Model\Order\OrderTrack;
use Model\Order\OrderTools;

class BigDataPanelScript extends \Library\Controller
{

    private $_redisPreKey    = 'bigdata:';
    private $_log            = 'bigdata/';
    private $_dayCode;
    private $_redis;
    private $_data           = [];
    private $_orderModel;
    private $_landModel;
    private $_orderData;
    private $_checkData;
    private $_disChannel     = [];
    private $_trip           = [];
    private $_trailData      = [];
    private $_onlineOrder    = [];
    private $_tourist        = ['province' => [], 'city' => []];
    private $_tripNumbersModel;
    private $_orderBookModel;
    private $_channelModel;
    private $_chainModel;
    private $_disChainRes;
    private $_resellerId;
    private $_tripNum        = ['one' => 0, 'two' => 0, 'three' => 0, 'four' => 0];
    private $_onlineOrderNum = [
        'the_same_day'        => 0,
        'before_one'          => 0,
        'before_three'        => 0,
        'before_seven'        => 0,
        'before_other'        => 0,
        'the_same_day_ticket' => 0,
        'before_one_ticket'   => 0,
        'before_three_ticket' => 0,
        'before_seven_ticket' => 0,
        'before_other_ticket' => 0,
    ];

    //线下预订方式 10 云票务  12 自助机  14 闸机  15 智能终端  18 年卡  其他都算成在线预定
    private $_offLineOrderMode = [10, 12, 14, 15, 18];

    //缓存文件最大300K左右
    private $_maxCacheFileSize = 300 * 1024;

    //缓存文件最大个数
    private $_maxCacheFileNum = 0;

    protected $_trackModel;

    private $_bigAgeModel;

    private $_bigSexModel;

    private $_bigTouristPlaceModel;

    private $_orderToolModel;

    public function runTaskDay()
    {
        $this->_orderModel = new OrderQuery();
        $this->_chainModel = new BuyChain();
        $this->_redis      = Cache::getInstance('redis');

        $params = $GLOBALS['argv'];
        $app    = strtolower($params[3]);
        $time   = (isset($params[4]) && strtotime($params[4])) ? $params[4] : date('Ymd', strtotime("-1 day"));
        
        $this->_dayCode = $time;

        if ($app == 'all' || empty($app)) {
            $this->runAllTask($time);
        } else {
            switch ($app) {
                case 'book':
                    //在线预订统计 和 出行人数统计 分销渠道 游客地分析
                    $this->_multiProcessBookAndTrip($time);
                    break;
                case 'use':
                    //产品使用排行
                    //$this->_getProductTop($time);
                    break;
                case 'statistics':
                    //统计任务
                    $this->_getStatistics($time);
                    break;
                case 'other':
                    //其他处理
                    $this->_other($time);
                    break;
            }
        }
        echo 'Job Done';
    }

    /**
     * 启动全部任务
     */
    public function runAllTask($time)
    {
        $this->_multiProcessBookAndTrip($time);
        // sleep(1);
        // $this->_getProductTop($time);
        sleep(1);
        $this->_getStatistics($time);
        sleep(1);
        $this->_other($time);
    }

    /**
     * 每小时启动一次
     */
    public function runTaskHour()
    {
        $params            = $GLOBALS['argv'];
        $this->_orderModel = new OrderQuery();
        $this->_redis      = Cache::getInstance('redis');
        //获取验证 票数(按小时计)  24点会被记成0 特殊处理下
        $hour = date('H');
        if ($hour == 0) {
            $hour = 23;
        } else {
            $hour = $hour - 1;
        }
        $hour = (isset($params[3])) ? $params[3] : $hour;

        $this->_getHourData($hour);
        $this->_insertHourData($hour);
    }

    /**
     * 每5s启动一次
     */
    public function runTaskSecond()
    {
        //获取游客轨迹
        $this->_orderModel = new OrderQuery();
        $this->_getTouristTrail();
        $this->_insertTouristTrail();
    }

    private function _multiProcessBookAndTrip($date)
    {
        pft_log('/big_data_panel/', 'book_trip:start');
        if (!strtotime($date)) {
            return ['code' => 403, 'msg' => '时间格式错误'];
        }

        try {

            $maxNum   = 6;
            $dateList = $this->_getDateList($date);
            $dateNum  = count($dateList);

            $this->_clearPreData($date);
            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
            $times = ceil($dateNum / $maxNum);

            for ($i = 0; $i < $times; $i++) {
                sleep(1);
                $handleDateList = array_slice($dateList, $i * $maxNum, $maxNum);
                $procNum        = count($handleDateList);

                //使用多进程之前 释放掉已有的所有链接
                $this->_unsetAllModel();
                $task = new \Library\MulityProcessHelper($this, $procNum, 1);
                $task->run($handleDateList); // 如果前面连接了数据库、redis等，最好在这之前关闭掉
            }

            //获取最近30天的游客地top10
            $model  = $this->_getBigTouristPlaceModel();
            $begin  = date('Ymd', strtotime("-30 day"));
            $end    = date('Ymd', strtotime("-1 day"));
            
            // 初始化统计容器
            $agg = [
                'province' => [],
                'city'     => [],
            ];
            
            // 按天循环查询并累加
            for ($ts = strtotime($begin); $ts <= strtotime($end); $ts += 86400) {
                $curDate = date('Ymd', $ts);
                $tmp = $model->getInfoByDate($curDate, $curDate);
                
                // 累加省份数据
                if (!empty($tmp['province'])) {
                    foreach ($tmp['province'] as $item) {
                        $code = $item['code'];
                        if (!isset($agg['province'][$code])) {
                            $agg['province'][$code] = [
                                'code'   => $code,
                                'num'    => 0,
                                'ticket' => 0,
                            ];
                        }
                        $agg['province'][$code]['num']    += (int)$item['num'];
                        $agg['province'][$code]['ticket'] += (int)$item['ticket'];
                    }
                }
                
                // 累加城市数据
                if (!empty($tmp['city'])) {
                    foreach ($tmp['city'] as $item) {
                        $code = $item['code'];
                        if (!isset($agg['city'][$code])) {
                            $agg['city'][$code] = [
                                'code'   => $code,
                                'num'    => 0,
                                'ticket' => 0,
                            ];
                        }
                        $agg['city'][$code]['num']    += (int)$item['num'];
                        $agg['city'][$code]['ticket'] += (int)$item['ticket'];
                    }
                }
            }
            
            // 将累加结果转换为索引数组
            $result = [
                'province' => array_values($agg['province']),
                'city'     => array_values($agg['city']),
            ];

            //固化
            $time  = date('Ymd', strtotime($date));
            $value = json_encode($result);
            $key   = $this->_redisPreKey . 'place:' . $time;
            $redis = Cache::getInstance('redis');
            $redis->set($key, $value, '', 3600 * 24);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        pft_log('/big_data_panel/', 'book_trip:end');
    }

    private function _unsetAllModel()
    {
        unset($this->_orderBookModel);
        unset($this->_tripNumbersModel);
        unset($this->_channelModel);
        unset($this->_chainModel);
        unset($this->_bigSexModel);
        unset($this->_bigAgeModel);
        unset($this->_bigTouristPlaceModel);
        unset($this->_orderToolModel);
        unset($this->_redis);
    }

    private function _clearPreData($date)
    {
        $bookOrderModel = $this->_getOrderBookModel();
        //删除数据
        $bookOrderModel->deleteData($date);

        //出行人数结果固化
        $tripModel = $this->_getTripNumbersModel();
        //删除执行日期已有的数据
        $tripModel->deleteData($date);

        $DistriChannelModel = $this->_getDistriChannelModel();
        $DistriChannelModel->deleteData($date);

        $ageModel = $this->_getBigAgeModel();
        //插入数据前删数据
        $ageModel->deleteData($date);

        $sexModel = $this->_getBigSexModel();
        //插入数据前删数据
        $sexModel->deleteData($date);

        $model = $this->_getBigTouristPlaceModel();
        //删除数据
        $model->deleteData($date);
    }

    public function runWorker($dateList)
    {
        $code = 200;
        $msg  = '';

        if (is_array($dateList)) {
            $dateList = array_shift($dateList);
        }

        try {
            $this->_orderModel = new OrderQuery();
            $this->_chainModel = new BuyChain();
            $this->_redis      = Cache::getInstance('redis');
            $this->_bookAndTrip($dateList);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $str = 'book_trip:';
        $str .= $dateList[0] . ' - ' . $dateList[1] . ':';
        $str .= $code == 200 ? ':执行成功' : ':执行失败,原因:' . $msg;

        pft_log('/big_data_panel/', $str);
    }

    /**
     * 拆解日期(新)
     */
    protected function _getDateList($date)
    {
        if (!strtotime($date)) {
            throw new \Exception($date . '日期格式错误');
        }

        $date     = date('Y-m-d', strtotime($date));
        $dateList = [
            [$date . ' 00:00:00', $date . ' 07:59:59'],
            [$date . ' 08:00:00', $date . ' 08:59:59'],
            [$date . ' 09:00:00', $date . ' 09:59:59'],
            [$date . ' 10:00:00', $date . ' 10:59:59'],
            [$date . ' 11:00:00', $date . ' 11:59:59'],
            [$date . ' 12:00:00', $date . ' 12:59:59'],
            [$date . ' 13:00:00', $date . ' 13:59:59'],
            [$date . ' 14:00:00', $date . ' 14:59:59'],
            [$date . ' 15:00:00', $date . ' 15:59:59'],
            [$date . ' 16:00:00', $date . ' 16:59:59'],
            [$date . ' 17:00:00', $date . ' 19:59:59'],
            [$date . ' 20:00:00', $date . ' 23:59:59'],
        ];

        return $dateList;
    }

    /**
     * 在线预订 和 出行人数统计 分销渠道分析 游客地分析
     * <AUTHOR>
     * @date   2017-4-5
     *
     * @param  date $time 预定日期 2017-4-5
     */
    private function _bookAndTrip($dateList)
    {
        $size  = 3000;
        $total = $this->_getTrackTotal($dateList, 'order_v2');

        $totalPage = ceil($total / $size);

        for ($page = 1; $page <= $totalPage; $page++) {

            $orderTrackList = $this->_getTrackList($dateList, $page, $size);
            $orderArr       = array_column($orderTrackList, 'ordernum');

            $this->_data = $this->_getOrderInfo($orderArr);

            //取出分销链
            $disChainsInfo = $this->_chainModel->getListByOrderId($orderArr,
                'orderid, buyerid, sellerid, cost_money, sale_money, pmode, level');
            if (!$disChainsInfo) {
                continue;
            }

            foreach ($disChainsInfo as $item) {
                $this->_disChainRes[$item['orderid']][] = $item;
            }

            //var_dump($this->_disChainRes);exit;
            //昨日预定人数分析 1人 3人 ...
            $this->_handleYesterdayBook();

            //在线预定人数分析 提前1天 3天 ...
            $this->_handleBeforeBook();

            //分销渠道分析 @TODO 可能不太完善
            $this->_handleChannel();

            //游客地分析
            $this->_handleTouristPlace();

            //性别 和 年龄段 (根据身份证)
            $this->_handleSexAndAge();

            //unset
            unset($this->_data);
            unset($this->_disChinsRes);
            unset($orderArr);
            unset($disChainsInfo);
            unset($orderTrackList);
        }

        $time = date('Ymd', strtotime($dateList[0]));
        $this->_insertYesterdayBook($time);
        $this->_insertBeforeBook($time);
        $this->_insertChannel($time);
        $this->_insertTouristPlace($time);
        $this->_insertSex($time);
        $this->_insertAge($time);
    }

    /**
     * 获取近7天产品使用top10
     * <AUTHOR>
     * @date   2017-4-5
     *
     * @param  date $time 统计日期 2017-4-5
     * @deprecated 废弃
     */
    private function _getProductTop($time)
    {
        $time  = date('Ymd', strtotime($time));
        $end   = date('Y-m-d', strtotime("-1 days", strtotime($time)));
        $begin = date('Y-m-d', strtotime("-7 days", strtotime($time)));

        $model  = new Statistics();
        $data   = $model->getUseTop($begin, $end, 3000);
        $lidArr = array_column($data, 'lid');
        if (empty($lidArr)) {
            return [];
        }

        //获取产品信息
        //$landModel = new Land();
        //$landInfo  = $landModel->getLandInfoByMuli($lidArr);

        $javaAPi  = new \Business\CommodityCenter\Land();
        $landInfo = $javaAPi->queryLandMultiQueryById($lidArr);
        if (!empty($landInfo) && is_array($landInfo)) {
            foreach ($landInfo as $item) {
                $landRes[$item['id']] = $item['resourceID'];
            }
        }

        $resoureSumArr = [];
        foreach ($data as $item) {
            $resourceID = isset($landRes[$item['lid']]) ? $landRes[$item['lid']] : '';
            if (empty($resourceID)) {
                continue;
            }

            if (!isset($resoureSumArr[$resourceID])) {
                $resoureSumArr[$resourceID]['order_num']  = 0;
                $resoureSumArr[$resourceID]['ticket_num'] = 0;
            }
            $resoureSumArr[$resourceID]['order_num']  += $item['order_num'];
            $resoureSumArr[$resourceID]['ticket_num'] += $item['ticket_num'];
            $resoureSumArr[$resourceID]['resourceID'] = $resourceID;
        }

        $sortOrderNumArr  = array_column($resoureSumArr, 'order_num');
        $sortTicketNumArr = array_column($resoureSumArr, 'ticket_num');
        array_multisort($sortTicketNumArr, SORT_DESC, $sortOrderNumArr, SORT_DESC, $resoureSumArr);

        // 获取排前30的数据
        $resArr = array_slice($resoureSumArr, 0, 30);
        // 批量获取资源的名字
        $resourceIDArr = array_column($resArr, 'resourceID');

        $landResourceModel = new \Model\Product\LandResource();
        $resourceInfoArr   = $landResourceModel->getResourceListByIdArr($resourceIDArr, 'id,title');

        $res = [];
        foreach ($resArr as $item) {
            $res[] = [
                'name'   => isset($resourceInfoArr[$item['resourceID']]) ? $resourceInfoArr[$item['resourceID']] : '未知',
                'order'  => $item['order_num'],
                'ticket' => $item['ticket_num'],
            ];
        }

        //固化信息
        $key   = $this->_redisPreKey . 'producttop:' . $time;
        $value = json_encode($res);
       // $this->_redis->set($key, $value, '', 3600 * 24);

        $model = new BigDataPanel\PftBigProductTop();
        //插入数据前删除
        $model->deleteData($time);
        $result    = $model->insertData($time, $value);
        $logResult = empty($result) ? 'fail' : 'success';
        pft_log($this->_log, '产品使用排行脚本执行结果:' . $logResult);
    }

    /**
     * 供应商数量
     * <AUTHOR>
     * @date   2017-4-14
     */
    private function _getStatistics($time)
    {
        $statisticsModel = new BigDataPanel\PftBigStatistics();
        $time            = date('Ymd', strtotime($time));

        //删除数据
        $statisticsModel->deleteData($time);

        $statisticsInfo = $statisticsModel->getLastInfo();

        $memberModel    = new Member();
        $memberQueryBiz = new MemberQuery();
        //前一天的member表结束ID
        $memberLastId = $statisticsInfo['member_last_id'] ?: 0;
        //最新的member表结束ID
        $newMemLastId = $memberQueryBiz->queryMemberLastId('member');

        //供应商数量
        $applyNumRes = $memberQueryBiz->queryIdScopeCount($memberLastId, $newMemLastId, 0);
        if ($applyNumRes['code'] != 200) {
            $applyNum = 0;
        } else {
            $applyNum = $applyNumRes['data'];
        }

        //分销商数量
        $fxNumRes = $memberQueryBiz->queryIdScopeCount($memberLastId, $newMemLastId, 1);
        if ($fxNumRes['code'] != 200) {
            $fxNum = 0;
        } else {
            $fxNum = $fxNumRes['data'];
        }

        //旅行社数量 景区数量
        $extLastId = $statisticsInfo['ext_last_id'] ?: 0;

        $newExtId     = $memberQueryBiz->queryMemberLastId('ext');
        $CustomerBus  = new \Business\Member\Customer();
        $memberExtApi = new MemberExtQuery();
        $corpKind     = $CustomerBus->getCorpKind('旅行社');
        $travelAgency = $memberExtApi->queryMemberExtComType($extLastId, $newExtId, $corpKind);

        $corpKind = $CustomerBus->getCorpKind('景区');
        $landNum  = $memberExtApi->queryMemberExtComType($extLastId, $newExtId, $corpKind);

        $landModel = new Land();
        //前一天的land表结束ID
        $LandLastId = $statisticsInfo['land_last_id'] ?: 0;
        //最新的product表结束ID
        $commodityProductApi = new Product();
        $productLastData     = $commodityProductApi->getProductLastId();
        if ($productLastData['code'] != 200) {
            $newProductId = 0;
        } else {
            $newProductId = $productLastData['data'];
        }
        //最新的land表结束ID
        $javaApi   = new \Business\CommodityCenter\Land();
        $newLandId = $javaApi->queryLastLandId();

        //最新的land表结束ID
        $productNum = (int)$newLandId - (int)$LandLastId;

        $data = [
            'date'            => $time,
            'land'            => $landNum,
            'product'         => $productNum,
            'supplier'        => $applyNum,
            'distributor'     => $fxNum,
            'travel'          => $travelAgency,
            'member_last_id'  => $newMemLastId,
            'product_last_id' => $newProductId,
            'land_last_id'    => $newLandId,
            'ext_last_id'     => $newExtId,
        ];

        //插入数据
        $result    = $statisticsModel->insertData($data);
        $logResult = empty($result) ? 'fail' : 'success';
        pft_log($this->_log, '产品数量/供应商数量/分销商数量/旅行社数量统计脚本执行结果:' . $logResult);

        $data  = $statisticsModel->getData();
        $value = json_encode($data);
        $key   = $this->_redisPreKey . 'statistics:' . $time;
        if (empty($this->_redis)) {
            $this->_redis = Cache::getInstance('redis');
        }
        $this->_redis->set($key, $value, '', 3600 * 24);
    }

    /**
     * 出行人数数据处理
     */
    private function _handleYesterdayBook()
    {
        $tmpTripData = $this->_cacheDataArr($this->_dayCode, 'yesterdaybook', 'get');
        $this->_trip = $tmpTripData;

        $lidArr = array_column($this->_data, 'lid');
        //$landModel = $this->_getLandModel();
        //$landRes   = $landModel->getLandInfoByMuli($lidArr, 'id, apply_did');

        $javaAPi  = new \Business\CommodityCenter\Land();
        $landInfo = $javaAPi->queryLandMultiQueryById($lidArr);
        $landInfo = [];

        if (!empty($landRes) && is_array($landRes)) {
            foreach ($landRes as $item) {
                $landInfo[$item['id']] = $item['apply_did'];
            }
        }

        foreach ($this->_data as $item) {
            if (!isset($this->_disChainRes[$item['ordernum']])) {
                continue;
            }

            $chain = $this->_disChainRes[$item['ordernum']];

            foreach ($chain as $value) {
                //最末级加一级
                if (in_array($value['level'],
                        [0, -1]) && ($value['buyerid'] != 112) && ($value['buyerid'] != $value['sellerid'])) {
                    $chain[] = [
                        'sellerid' => $value['buyerid'],
                        'buyerid'  => 112,
                        'level'    => -1,
                    ];
                }
                //供应商
                $aid = $value['sellerid'];
                //层级
                if (in_array($value['level'], [0, 1])) {
                    $level = 1;
                } else {
                    $level = $value['level'];
                }
                if ($item['tnum'] == 1) {
                    //1人
                    if (isset($tmpTripData[$aid][$item['lid']][$item['pid']][$level]['level_one'])) {
                        $tmpTripData[$aid][$item['lid']][$item['pid']][$level]['level_one']++;
                    } else {
                        $tmpTripData[$aid][$item['lid']][$item['pid']][$level]['level_one'] = 1;
                    }
                } elseif ($item['tnum'] >= 2 && $item['tnum'] <= 5) {
                    //2-5人
                    if (isset($tmpTripData[$aid][$item['lid']][$item['pid']][$level]['level_two'])) {
                        $tmpTripData[$aid][$item['lid']][$item['pid']][$level]['level_two']++;
                    } else {
                        $tmpTripData[$aid][$item['lid']][$item['pid']][$level]['level_two'] = 1;
                    }
                } elseif ($item['tnum'] >= 6 && $item['tnum'] <= 10) {
                    //6-10人
                    if (isset($tmpTripData[$aid][$item['pid']]['level_three'])) {
                        $tmpTripData[$aid][$item['lid']][$item['pid']][$level]['level_three']++;
                    } else {
                        $tmpTripData[$aid][$item['lid']][$item['pid']][$level]['level_three'] = 1;
                    }
                } elseif ($item['tnum'] > 10) {
                    //10人以上
                    if (isset($tmpTripData[$aid][$item['lid']][$item['pid']][$level]['level_four'])) {
                        $tmpTripData[$aid][$item['lid']][$item['pid']][$level]['level_four']++;
                    } else {
                        $tmpTripData[$aid][$item['lid']][$item['pid']][$level]['level_four'] = 1;
                    }
                }
            }
        }

        $this->_cacheDataArr($this->_dayCode, 'yesterdaybook', 'set', $tmpTripData);
    }

    /**
     * 出行人数结果存库
     */
    private function _insertYesterdayBook($time)
    {
        //缓存取数据
        $tmpTripData = $this->_cacheDataArr($this->_dayCode, 'yesterdaybook', 'get');
        //出行人数结果固化
        $model = $this->_getTripNumbersModel();
        //删除执行日期已有的数据
        //$model->deleteData($time);
        $insertData = [];

        foreach ($tmpTripData as $aid => $aidInfo) {
            foreach ($aidInfo as $lid => $lidInfo) {
                foreach ($lidInfo as $pid => $pidInfo) {
                    foreach ($pidInfo as $level => $value) {
                        $insertData[] = [
                            'date'        => date('Ymd', strtotime($time)),
                            'level_one'   => $value['level_one'] ?: 0,
                            'level_two'   => $value['level_two'] ?: 0,
                            'level_three' => $value['level_three'] ?: 0,
                            'level_four'  => $value['level_four'] ?: 0,
                            'insert_time' => time(),
                            'level'       => $level,
                            'aid'         => $aid,
                            'lid'         => $lid,
                            'pid'         => $pid,
                        ];
                    }
                }

                if (count($insertData) >= 500) {
                    $result    = $model->insertData($insertData);
                    $logResult = empty($result) ? 'success' : 'fail';
                    pft_log($this->_log, '出行人数脚本执行结果:' . $logResult);
                    $insertData = [];
                }
            }
        }

        //最后一部分数据插入
        if ($insertData) {
            $result    = $model->insertData($insertData);
            $logResult = empty($result) ? 'fail' : 'success';
            pft_log($this->_log, '出行人数脚本执行结果:' . $logResult);
        }

        //将统计数据 存入redis
        $begin = date('Ymd', strtotime("-30 day"));
        $end   = date('Ymd', strtotime("-1 day"));
        
        // 初始化统计变量
        $levelOne = $levelTwo = $levelThree = $levelFour = 0;
        
        // 按天循环查询并累加
        for ($ts = strtotime($begin); $ts <= strtotime($end); $ts += 86400) {
            $curDate = date('Ymd', $ts);
            $res = $model->getDataSummaryByTime($curDate, $curDate);
            
            $levelOne   += (int)$res['level_one'];
            $levelTwo   += (int)$res['level_two'];
            $levelThree += (int)$res['level_three'];
            $levelFour  += (int)$res['level_four'];
        }

        $total = $levelOne + $levelTwo + $levelThree + $levelFour;

        if (!$total) {
            $count = [
                'levelOne'   => 0,
                'levelTwo'   => 0,
                'levelThree' => 0,
                'levelFour'  => 0,
            ];

        } else {
            $count = [
                'levelOne'   => $levelOne / $total,
                'levelTwo'   => $levelTwo / $total,
                'levelThree' => $levelThree / $total,
                'levelFour'  => $levelFour / $total,
            ];
        }

        $value = json_encode($count);
        $key   = $this->_redisPreKey . 'trip:' . $time;
        $this->_redis->set($key, $value, '', 3600 * 24);

        //释放
        unset($this->_tripNum);
        //清除缓存
        $this->_cacheDataArr($this->_dayCode, 'yesterdaybook', 'del');
    }

    /**
     * 提前预定人数数据处理
     */
    private function _handleBeforeBook()
    {
        $tmpBeforeData = $this->_cacheDataArr($this->_dayCode, 'beforebook', 'get');
        foreach ($this->_data as $item) {
            //在线预订人数
            $playTime  = strtotime($item['playtime']);
            $orderTime = strtotime($item['ordertime']);

            if (!isset($this->_disChainRes[$item['ordernum']])) {
                continue;
            }

            $chain = $this->_disChainRes[$item['ordernum']];

            foreach ($chain as $value) {
                //最末级加一级
                if (in_array($value['level'],
                        [0, -1]) && ($value['buyerid'] != 112) && ($value['buyerid'] != $value['sellerid'])) {
                    $chain[] = [
                        'sellerid' => $value['buyerid'],
                        'buyerid'  => 112,
                        'level'    => -1,
                    ];
                }
                //供应商
                $aid = $value['sellerid'];
                //层级
                if (in_array($value['level'], [0, 1])) {
                    $level = 1;
                } else {
                    $level = $value['level'];
                }
                if (date('m', $playTime) == date('m', $orderTime) && date('d', $playTime) == date('d', $orderTime)) {
                    //当天
                    if (isset($tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['the_same_day'])) {
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['the_same_day']++;
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['the_same_day_ticket'] += $item['tnum'];
                    } else {
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['the_same_day']        = 1;
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['the_same_day_ticket'] = 1;
                    }
                }
                if (date('m', $playTime) == date('m', $orderTime) && ((date('d', $playTime) - date('d',
                                $orderTime)) == 1)) {
                    //提前1天
                    if (isset($tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_one'])) {
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_one']++;
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_one_ticket'] += $item['tnum'];
                    } else {
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_one']        = 1;
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_one_ticket'] = 1;
                    }
                }
                if (date('m', $playTime) == date('m', $orderTime) && ((date('d', $playTime) - date('d',
                                $orderTime)) == 3)) {
                    //提前3天
                    if (isset($tmpBeforeData[$aid][$item['lid']][$item['pid']][$level]['before_three'])) {
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_three']++;
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_three_ticket']++;
                    } else {
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_three']        = 1;
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_three_ticket'] = 1;
                    }
                }
                if (date('m', $playTime) == date('m', $orderTime) && ((date('d', $playTime) - date('d',
                                $orderTime)) == 7)) {
                    //提前7天
                    if (isset($tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_seven'])) {
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_seven']++;
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_seven_ticket'] += $item['tnum'];
                    } else {
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_seven']        = 1;
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_seven_ticket'] = 1;
                    }
                }
                if (date('m', $playTime) != date('m', $orderTime) || ((date('d', $playTime) - date('d',
                                $orderTime)) > 7)) {
                    //提前7天以上
                    if (isset($tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_other'])) {
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_other']++;
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_other_ticket'] += $item['tnum'];
                    } else {
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_other']        = 1;
                        $tmpBeforeData[$aid][$item['lid']][$item['pid']][$item['ordermode']][$level]['before_other_ticket'] = 1;
                    }
                }

            }
        }
        $this->_cacheDataArr($this->_dayCode, 'beforebook', 'set', $tmpBeforeData);
    }

    /**
     * 提前预订人数结果存库
     */
    private function _insertBeforeBook($time)
    {
        $tmpBeforeData = $this->_cacheDataArr($this->_dayCode, 'beforebook', 'get');
        $model         = $this->_getOrderBookModel();
        ////删除数据
        //$model->deleteData($time);
        $insertData = [];
        foreach ($tmpBeforeData as $aid => $aidInfo) {
            foreach ($aidInfo as $lid => $pidInfo) {
                foreach ($pidInfo as $pid => $orderModeInfo) {
                    foreach ($orderModeInfo as $orderMode => $levelInfo) {
                        foreach ($levelInfo as $level => $value) {
                            $insertData[] = [
                                'date'                => date('Ymd', strtotime($time)),
                                'the_same_day'        => $value['the_same_day'] ?: 0,
                                'before_one'          => $value['before_one'] ?: 0,
                                'before_three'        => $value['before_three'] ?: 0,
                                'before_seven'        => $value['before_seven'] ?: 0,
                                'before_other'        => $value['before_other'] ?: 0,
                                'the_same_day_ticket' => $value['the_same_day_ticket'] ?: 0,
                                'before_one_ticket'   => $value['before_one_ticket'] ?: 0,
                                'before_three_ticket' => $value['before_three_ticket'] ?: 0,
                                'before_seven_ticket' => $value['before_seven_ticket'] ?: 0,
                                'before_other_ticket' => $value['before_other_ticket'] ?: 0,
                                'aid'                 => $aid,
                                'pid'                 => $pid,
                                'lid'                 => $lid,
                                'level'               => $level,
                                'order_mode'          => $orderMode,
                                'insert_time'         => time(),
                            ];
                        }
                    }
                    if (count($insertData) >= 500) {
                        $result    = $model->insertData($insertData);
                        $logResult = empty($result) ? 'success' : 'fail';
                        pft_log($this->_log, '提前预定人数脚本执行结果:' . $logResult);
                        $insertData = [];
                    }
                }
            }
        }

        if ($insertData) {
            $result    = $model->insertData($insertData);
            $logResult = empty($result) ? 'fail' : 'success';
            pft_log($this->_log, '提前预定人数脚本执行结果:' . $logResult);
        }

        //提前天数出游
        //默认的起止时间  过去30天
        $defaultBegin = date('Ymd', strtotime("-30 day"));
        $defaultEnd   = date('Ymd', strtotime("-1 day"));
        //按天遍历验证数据
        $data = $this->getBookDataByTime($defaultBegin, $defaultEnd);

        //总订单数
        $total = $data['the_same_day'] + $data['before_one'] + $data['before_three'] +
                 $data['before_seven'] + $data['before_other'];

        if ($total == 0) {
            $count = [
                'the_same_day'        => 0,
                'before_one'          => 0,
                'before_three'        => 0,
                'before_seven'        => 0,
                'before_other'        => 0,
                'the_same_day_ticket' => 0,
                'before_one_ticket'   => 0,
                'before_three_ticket' => 0,
                'before_seven_ticket' => 0,
                'before_other_ticket' => 0,
            ];
        } else {
            $count = [
                //当天预定的比例 （下同）
                'the_same_day'        => $data['the_same_day'] / $total,
                //提前一天
                'before_one'          => $data['before_one'] / $total,
                //提前三天
                'before_three'        => $data['before_three'] / $total,
                //提前七天
                'before_seven'        => $data['before_seven'] / $total,
                //其他
                'before_other'        => $data['before_other'] / $total,
                //当天预定的票数 （下同）
                'the_same_day_ticket' => $data['the_same_day_ticket'],
                //提前一天
                'before_one_ticket'   => $data['before_one_ticket'],
                //提前三天
                'before_three_ticket' => $data['before_three_ticket'],
                //提前七天
                'before_seven_ticket' => $data['before_seven_ticket'],
                //其他
                'before_other_ticket' => $data['before_other_ticket'],
            ];
        }

        $value = json_encode($count);
        $key   = $this->_redisPreKey . 'order:' . $time;

        $this->_redis->set($key, $value, '', 3600 * 24);

        //获取近三十天线上预订比例和票数
        //总票数
        $total = $count['the_same_day_ticket'] + $count['before_one_ticket'] + $count['before_three_ticket'] +
                 $count['before_seven_ticket'] + $count['before_other_ticket'];

        if ($total == 0) {
            $onLineRes = [
                'scale'      => 0,
                'on_ticket'  => 0,
                'off_ticket' => 0,
            ];
        } else {
            $res = $this->getBookDataByMode($defaultBegin, $defaultEnd);
            //线上预定的票数
            $num = $res['ticket'];

            $scale     = $num / $total;
            $onLineRes = [
                'scale'      => $scale,
                'on_ticket'  => $num,
                'off_ticket' => $total - $num,
            ];
        }
        $onLineRes = json_encode($onLineRes);

        $key = $this->_redisPreKey . 'onlinescale:' . $time;
        $this->_redis->set($key, $onLineRes, '', 3600 * 24);

        //释放
        unset($this->_onlineOrderNum);
        //清除缓存
        $this->_cacheDataArr($this->_dayCode, 'beforebook', 'del');
    }

    /**
     * 分销渠道分析
     */
    private function _handleChannel()
    {
        foreach ($this->_data as $item) {
            if (!isset($this->_disChainRes[$item['ordernum']])) {
                //没有分销信息的订单 跳过
                continue;
            }

            if (empty($item['tnum'])) {
                //如果当天退票 不计
                continue;
            }

            $chainRes = $this->_disChainRes[$item['ordernum']];

            foreach ($chainRes as $key => $value) {
                if (in_array($value['level'],
                        [0, -1]) && ($value['buyerid'] != 112) && ($value['buyerid'] != $value['sellerid'])) {
                    $chain[] = [
                        'sellerid' => $value['buyerid'],
                        'buyerid'  => 112,
                        'level'    => -1,
                    ];
                }

                //层级
                if (in_array($value['level'], [0, 1])) {
                    $level = 1;
                } else {
                    $level = $value['level'];
                }

                if ($value['sellerid'] == $value['buyerid']) {
                    //自供自销
                    if (!isset($this->_disChannel[1][$value['sellerid']][$item['lid']][$level])) {
                        $this->_disChannel[1][$value['sellerid']][$item['lid']][$level]['order_num']  = 1;
                        $this->_disChannel[1][$value['sellerid']][$item['lid']][$level]['ticket_num'] = $item['tnum'];
                    } else {
                        $this->_disChannel[1][$value['sellerid']][$item['lid']][$level]['order_num']++;
                        $this->_disChannel[1][$value['sellerid']][$item['lid']][$level]['ticket_num'] += $item['tnum'];
                    }
                } else {
                    //分销
                    if (!isset($this->_disChannel[2][$value['sellerid']][$item['lid']][$level])) {
                        $this->_disChannel[2][$value['sellerid']][$item['lid']][$level]['order_num']  = 1;
                        $this->_disChannel[2][$value['sellerid']][$item['lid']][$level]['ticket_num'] += $item['tnum'];
                    } else {
                        $this->_disChannel[2][$value['sellerid']][$item['lid']][$level]['order_num']++;
                        $this->_disChannel[2][$value['sellerid']][$item['lid']][$level]['ticket_num'] += $item['tnum'];
                    }
                }
            }
        }
    }

    /**
     * 分销渠道存库
     */
    private function _insertChannel($time)
    {
        $model = $this->_getDistriChannelModel();
        //$model->deleteData($time);

        $insertData = [];
        //自供自销 OTA分销
        foreach ($this->_disChannel as $key => $info) {
            foreach ($info as $sellerId => $lidInfo) {
                foreach ($lidInfo as $lid => $levelInfo) {
                    foreach ($levelInfo as $level => $item) {
                        $insertData[] = [
                            'date'        => date('Ymd', strtotime($time)),
                            'type'        => $key,
                            'order_num'   => $item['order_num'],
                            'ticket_num'  => $item['ticket_num'],
                            'reseller_id' => $sellerId,
                            'lid'         => $lid,
                            'level'       => $level,
                        ];
                    }
                    if (count($insertData) > 500) {
                        $result = $model->insertData($insertData);

                        $logResult = empty($result) ? 'fail' : 'success';
                        pft_log($this->_log, '销售渠道分析脚本执行结果:' . $logResult);
                        unset($insertData);
                    }
                }
            }
        }

        if (!empty($insertData)) {
            $result = $model->insertData($insertData);

            $logResult = empty($result) ? 'fail' : 'success';
            pft_log($this->_log, '销售渠道分析脚本执行结果:' . $logResult);
            unset($insertData);
        }

        //自供自销 分销
        $begin = date('Ymd', strtotime("-30 day"));
        $end   = date('Ymd', strtotime("-1 day"));

        $data  = $model->getInfoByDateCountTicket($begin, $end);
        $value = json_encode($data);
        $key   = $this->_redisPreKey . 'channel:' . $time;
        $this->_redis->set($key, $value, '', 3600 * 24);

        //获取分销渠道排行数据
        $data  = $model->getResellerRankTicketCountsFromBi($begin, $end);
        $value = json_encode($data);
        $key   = $this->_redisPreKey . 'reseller:' . $time;

        $this->_redis->set($key, $value, '', 3600 * 24);

        //释放
        unset($this->_disChannel);
        unset($this->_resellerId);
    }

    /**
     * 游客地分析
     */
    private function _handleTouristPlace()
    {
        foreach ($this->_data as &$item) {
            //            //根据身份证 根据身份证的的城市暂时设成0
            //            if (!empty($item['personid']) && (strlen($item['personid']) == 15 || strlen($item['personid'] == 18))) {
            //                $key = substr($item['personid'], 0, 2);
            //                if (array_key_exists($key, $passportRule)) {
            //                    if (isset($this->_tourist[$passportRule[$key]['tel_id']])) {
            //                        $this->_tourist[$passportRule[$key]['tel_id']][0]['num']++;
            //                    } else {
            //                        $this->_tourist[$passportRule[$key]['tel_id']][0]['num']  = 1;
            //                    }
            //                    continue;
            //                }
            //            } elseif (!empty($item['ordertel'])) {
            //                //根据手机号 一次性扔进数据库查询
            //                $key      = substr($item['ordertel'], 0, 7);
            //                $telArr[] = $key;
            //            }
            //根据手机号查询市

            if (!empty($item['personid']) && (strlen($item['personid']) == 15 || strlen($item['personid']) == 18)) {
                // 先判断是否存在身份证，如果存在身份证就记录身份证的地址，否则记录手机号地址
                $key             = substr($item['personid'], 0, 4);
                $item['id_card'] = $key;
                $passport[]      = $key;
            } else {
                $key              = substr($item['ordertel'], 0, 7);
                $item['ordertel'] = $key;
                $telArr[]         = $key;
            }

        }

        // 对手机号地址进行处理
        $model   = new BigDataPanel\TelArea();
        $telInfo = $model->getInfoByTelArr($telArr);
        foreach ($telInfo as $item) {
            $tel = $item['tel'];
            unset($item['tel']);
            $telRes[$tel] = $item;
        }

        // 载入地址对应数组, 因为城市编码也包括省会编码,所以只要对身份证的城市编码进行判断就好
        $idCardCity = load_config('id_card_city', 'account');
        foreach ($this->_data as $v) {
            if (empty($v['ordertel']) && empty($v['id_card'])) {
                //号码空 跳过
                continue;
            }

            if (!isset($idCardCity[$v['id_card']]) && !(isset($telRes[$v['ordertel']]['city']) || isset($telRes[$v['ordertel']]['province']))) {
                //该号码没有对应省份或城市 跳过
                continue;
            }

            if (!isset($this->_disChainRes[$v['ordernum']])) {
                continue;
            }

            $chain = $this->_disChainRes[$v['ordernum']];

            foreach ($chain as $value) {
                //最末级加一级
                if (in_array($value['level'],
                        [0, -1]) && ($value['buyerid'] != 112) && ($value['buyerid'] != $value['sellerid'])) {
                    $chain[] = [
                        'sellerid' => $value['buyerid'],
                        'buyerid'  => 112,
                        'level'    => -1,
                    ];
                }
                //供应商
                $aid = $value['sellerid'];

                //层级
                if (in_array($value['level'], [0, 1])) {
                    $level = 1;
                } else {
                    $level = $value['level'];
                }

                $lid  = $v['lid'];
                $tnum = $v['tnum'];

                if (!empty($v['id_card'])) {
                    // 直接保存身份证前4位编码
                    $city = $v['id_card'];
                } else {
                    $city = $telRes[$v['ordertel']]['city'];
                }

                if (!isset($this->_tourist['city'][$city][$aid][$lid][$level])) {
                    $this->_tourist['city'][$city][$aid][$lid][$level]['num']    = 1;
                    $this->_tourist['city'][$city][$aid][$lid][$level]['ticket'] = $tnum;
                } else {
                    $this->_tourist['city'][$city][$aid][$lid][$level]['num']++;
                    $this->_tourist['city'][$city][$aid][$lid][$level]['ticket'] += $tnum;
                }

                if (!empty($v['id_card'])) {
                    // 直接保存身份证前4位编码
                    $province = $v['id_card'];
                } else {
                    $province = $telRes[$v['ordertel']]['province'];
                }

                if (!isset($this->_tourist['province'][$province][$aid][$lid][$level])) {
                    $this->_tourist['province'][$province][$aid][$lid][$level]['num']    = 1;
                    $this->_tourist['province'][$province][$aid][$lid][$level]['ticket'] = $tnum;
                } else {
                    $this->_tourist['province'][$province][$aid][$lid][$level]['num']++;
                    $this->_tourist['province'][$province][$aid][$lid][$level]['ticket'] += $tnum;
                }
            }

            // 2023-8-15 移除（仙盖山）特供接口，8月会下掉这个表
            /*
            $addData      = [
                'lid'      => $v['lid'],
                'ordernum' => $v['ordernum'],
                'code'     => $v['id_card'] ?: $telRes[$v['ordertel']]['city'],
                'name'     => $v['ordername'],
                'id_card'  => $v['personid'],
                'date'     => date('Ymd', strtotime($v['ordertime'])),
            ];
            $touristModel = new BigDataPanel\PftBigTourist();
            $touristModel->insertData($addData);
            */
        }
    }

    private function _insertTouristPlace($time)
    {
        $model = $this->_getBigTouristPlaceModel();
        ////删除数据
        //$model->deleteData($time);

        foreach ($this->_tourist['province'] as $province => $provinceInfo) {
            foreach ($provinceInfo as $aid => $aidInfo) {
                foreach ($aidInfo as $lid => $lidInfo) {
                    foreach ($lidInfo as $level => $info) {
                        $data[] = [
                            'date'   => date('Ymd', strtotime($time)),
                            'aid'    => $aid,
                            'code'   => $province,
                            'type'   => 1,
                            'num'    => $info['num'],
                            'ticket' => $info['ticket'],
                            'level'  => $level,
                            'lid'    => $lid,
                        ];
                        if (count($data) > 100) {
                            $result    = $model->insertData($data);
                            $logResult = empty($result) ? 'fail' : 'success';
                            pft_log($this->_log, '出游地脚本执行结果:' . $logResult);
                            unset($data);
                        }
                    }
                }
            }
        }

        foreach ($this->_tourist['city'] as $city => $cityInfo) {
            foreach ($cityInfo as $aid => $aidInfo) {
                foreach ($aidInfo as $lid => $lidInfo) {
                    foreach ($lidInfo as $level => $info) {
                        $data[] = [
                            'date'   => date('Ymd', strtotime($time)),
                            'aid'    => $aid,
                            'code'   => $city,
                            'type'   => 2,
                            'num'    => $info['num'],
                            'ticket' => $info['ticket'],
                            'level'  => $level,
                            'lid'    => $lid,
                        ];
                        if (count($data) > 100) {
                            $result    = $model->insertData($data);
                            $logResult = empty($result) ? 'fail' : 'success';
                            pft_log($this->_log, '出游地脚本执行结果:' . $logResult);
                            unset($data);
                        }
                    }
                }
            }
        }

        if (!empty($data)) {
            $result    = $model->insertData($data);
            $logResult = empty($result) ? 'fail' : 'success';
            pft_log($this->_log, '出游地脚本执行结果:' . $logResult);
            unset($data);
        }

        // //获取最近30天的游客地top10
        // $begin  = date('Ymd', strtotime("-30 day"));
        // $end    = date('Ymd', strtotime("-1 day"));
        // $result = $model->getInfoByDate($begin, $end);
        // $result = $result ?: [];
        //
        // //固化
        // $value = json_encode($result);
        // $key   = $this->_redisPreKey . 'place:' . $time;
        // $this->_redis->set($key, $value, '', 3600 * 24);

        //释放
        unset($this->_tourist);
    }

    /**
     * 性别和年龄段
     */
    private function _handleSexAndAge()
    {
        $tmpSexData = $this->_cacheDataArr($this->_dayCode, 'sex', 'get');
        $tmpAgeData = $this->_cacheDataArr($this->_dayCode, 'age', 'get');

        foreach ($this->_data as $item) {
            if (!isset($this->_disChainRes[$item['ordernum']])) {
                continue;
            }

            $chain = $this->_disChainRes[$item['ordernum']];

            if (!empty($item['personid'])) {
                $sex  = 0;
                $born = 0;
                if (strlen($item['personid']) == 15) {
                    //15位身份证号取最后一位
                    $sex  = substr($item['personid'], -1, 1);
                    $born = substr($item['personid'], 6, 2);
                }
                if (strlen($item['personid']) == 18) {
                    //18位身份证号取最后第二位
                    $sex  = substr($item['personid'], -2, 1);
                    $born = substr($item['personid'], 6, 4);
                }

                //奇数男性 2    偶数女性 1
                $sex = $sex % 2 == 0 ? 1 : 2;

                //年龄
                $nowYear = date('Y');
                $age     = $nowYear - $born;

                foreach ($chain as $value) {
                    //最末级加一级
                    if (in_array($value['level'],
                            [0, -1]) && ($value['buyerid'] != 112) && ($value['buyerid'] != $value['sellerid'])) {
                        $chain[] = [
                            'sellerid' => $value['buyerid'],
                            'buyerid'  => 112,
                            'level'    => -1,
                        ];
                    }
                    //供应商
                    $aid = $value['sellerid'];
                    //层级
                    if (in_array($value['level'], [0, 1])) {
                        $level = 1;
                    } else {
                        $level = $value['level'];
                    }

                    //性别数据分析
                    if (!isset($tmpSexData[$aid][$item['lid']][$item['pid']][$sex][$level])) {
                        $tmpSexData[$aid][$item['lid']][$item['pid']][$sex][$level]['order_num']  = 1;
                        $tmpSexData[$aid][$item['lid']][$item['pid']][$sex][$level]['ticket_num'] = $item['tnum'];
                    } else {
                        $tmpSexData[$aid][$item['lid']][$item['pid']][$sex][$level]['order_num']++;
                        $tmpSexData[$aid][$item['lid']][$item['pid']][$sex][$level]['ticket_num'] += $item['tnum'];
                    }

                    //年龄数据分析
                    if ($age >= 0 && $age <= 6) {
                        if (!isset($tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_one'])) {
                            //0-6岁 订单数
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_one'] = 1;
                            //0-6岁 票数
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_one_ticket'] = $item['tnum'];
                        } else {
                            //0-6岁 订单数
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_one']++;
                            //0-6岁 票数
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_one_ticket'] += $item['tnum'];
                        }
                    }

                    if ($age >= 7 && $age <= 17) {
                        if (!isset($tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_two'])) {
                            //7-17岁 订单数
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_two']        = 1;
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_one_ticket'] = $item['tnum'];
                        } else {
                            //7-17岁 订单数
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_two']++;
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_two_ticket'] += $item['tnum'];
                        }
                    }

                    if ($age >= 18 && $age <= 40) {
                        if (!isset($tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_three'])) {
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_three']        = 1;
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_three_ticket'] = $item['tnum'];
                        } else {
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_three']++;
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_three_ticket'] += $item['tnum'];
                        }
                    }

                    if ($age >= 41 && $age <= 60) {
                        if (!isset($tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_four'])) {
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_four']        = 1;
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_four_ticket'] = $item['tnum'];
                        } else {
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_four']++;
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_four_ticket'] += $item['tnum'];
                        }
                    }

                    if ($age >= 61 && $age <= 100) {
                        if (!isset($tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_five'])) {
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_five']        = 1;
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_five_ticket'] = $item['tnum'];
                        } else {
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_five']++;
                            $tmpAgeData[$aid][$item['lid']][$item['pid']][$level]['level_five_ticket'] += $item['tnum'];
                        }
                    }
                }
            }
        }

        $this->_cacheDataArr($this->_dayCode, 'sex', 'set', $tmpSexData);
        $this->_cacheDataArr($this->_dayCode, 'age', 'set', $tmpAgeData);
    }

    /**
     * 性别数据插入数据库
     */
    private function _insertSex($time)
    {
        $tmpSexData    = $this->_cacheDataArr($this->_dayCode, 'sex', 'get');
        $sexInsertData = [];
        $sexModel      = $this->_getBigSexModel();
        //
        ////插入数据前删数据
        //$sexModel->deleteData($time);

        foreach ($tmpSexData as $aid => $lidInfo) {
            foreach ($lidInfo as $lid => $pidInfo) {
                foreach ($pidInfo as $pid => $sexInfo) {
                    foreach ($sexInfo as $sex => $levelInfo) {
                        foreach ($levelInfo as $level => $value) {
                            $sexInsertData[] = [
                                'aid'        => $aid,
                                'lid'        => $lid,
                                'pid'        => $pid,
                                'date'       => date('Ymd', strtotime($time)),
                                'sex'        => $sex,
                                'level'      => $level,
                                'order_num'  => $value['order_num'],
                                'ticket_num' => $value['ticket_num'],
                            ];
                        }
                    }
                    if (count($sexInsertData) >= 500) {
                        $result    = $sexModel->insertData($sexInsertData);
                        $logResult = empty($result) ? 'fail' : 'sunccess';
                        pft_log($this->_log, '性别分析脚本执行结果:' . $logResult);
                        $sexInsertData = [];
                    }
                }
            }
        }

        if (!empty($sexInsertData)) {
            $result    = $sexModel->insertData($sexInsertData);
            $logResult = empty($result) ? 'fail' : 'success';
            pft_log($this->_log, '性别分析脚本执行结果:' . $logResult);
        }

        //默认获取近三十天的数据
        $begin = date('Ymd', strtotime("-30 day"));
        $end   = date('Ymd', strtotime("-1 day"));
        
        // 初始化累加器
        $data = [
            '1' => 0, // 男
            '2' => 0, // 女
        ];
        
        // 按天循环查询并累加
        for ($ts = strtotime($begin); $ts <= strtotime($end); $ts += 86400) {
            $curDate = date('Ymd', $ts);
            $tmp = $sexModel->getDataByTime($curDate, $curDate);
            
            if (!empty($tmp)) {
                foreach ($tmp as $sex => $ticket) {
                    if (!isset($data[$sex])) {
                        $data[$sex] = 0;
                    }
                    $data[$sex] += (int)$ticket;
                }
            }
        }

        //固化
        $value = json_encode($data);
        $key   = $this->_redisPreKey . 'sex:' . $time;
        $this->_redis->set($key, $value, '', 3600 * 24);

        //清除缓存
        $this->_cacheDataArr($this->_dayCode, 'sex', 'del');
    }

    /**
     * 年龄段数据插入数据库
     */
    private function _insertAge($time)
    {
        $tmpSexData    = $this->_cacheDataArr($this->_dayCode, 'age', 'get');
        $ageInsertData = [];
        $ageModel      = $this->_getBigAgeModel();
        //
        ////插入数据前删数据
        //$ageModel->deleteData($time);

        foreach ($tmpSexData as $aid => $aidInfo) {
            foreach ($aidInfo as $lid => $pidInfo) {
                foreach ($pidInfo as $pid => $levelInfo) {
                    foreach ($levelInfo as $level => $value) {
                        $ageInsertData[] = [
                            'aid'                => $aid,
                            'pid'                => $pid,
                            'lid'                => $lid,
                            'date'               => date('Ymd', strtotime($time)),
                            'level'              => $level,
                            'level_one'          => isset($value['level_one']) ? $value['level_one'] : 0,
                            'level_two'          => isset($value['level_two']) ? $value['level_two'] : 0,
                            'level_three'        => isset($value['level_three']) ? $value['level_three'] : 0,
                            'level_four'         => isset($value['level_four']) ? $value['level_four'] : 0,
                            'level_five'         => isset($value['level_five']) ? $value['level_five'] : 0,
                            'level_one_ticket'   => isset($value['level_one_ticket']) ? $value['level_one_ticket'] : 0,
                            'level_two_ticket'   => isset($value['level_two_ticket']) ? $value['level_two_ticket'] : 0,
                            'level_three_ticket' => isset($value['level_three_ticket']) ? $value['level_three_ticket'] : 0,
                            'level_four_ticket'  => isset($value['level_four_ticket']) ? $value['level_four_ticket'] : 0,
                            'level_five_ticket'  => isset($value['level_five_ticket']) ? $value['level_five_ticket'] : 0,
                        ];
                    }
                }
                if (count($ageInsertData) >= 500) {
                    $result    = $ageModel->insertData($ageInsertData);
                    $logResult = empty($result) ? 'fail' : 'success';
                    pft_log($this->_log, '年龄分析脚本执行结果:' . $logResult);
                    $ageInsertData = [];
                }
            }
        }

        if (!empty($ageInsertData)) {
            $result = $ageModel->insertData($ageInsertData);
            echo $ageModel->getDbError();
            $logResult = empty($result) ? 'fail' : 'success';
            pft_log($this->_log, '年龄分析脚本执行结果:' . $logResult);
        }

        //默认获取近三十天的数据
        $begin = date('Ymd', strtotime("-30 day"));
        $end   = date('Ymd', strtotime("-1 day"));
        
        // 初始化累加数组
        $data = [
            'level_one'          => 0,
            'level_two'          => 0,
            'level_three'        => 0,
            'level_four'         => 0,
            'level_five'         => 0,
            'level_one_ticket'   => 0,
            'level_two_ticket'   => 0,
            'level_three_ticket' => 0,
            'level_four_ticket'  => 0,
            'level_five_ticket'  => 0,
        ];
        
        // 按天循环查询并累加
        for ($ts = strtotime($begin); $ts <= strtotime($end); $ts += 86400) {
            $curDate = date('Ymd', $ts);
            $tmp = $ageModel->getDataByTime($curDate, $curDate);
            
            if (!empty($tmp)) {
                foreach ($data as $field => $val) {
                    if (isset($tmp[$field])) {
                        $data[$field] += (int)$tmp[$field];
                    }
                }
            }
        }

        //固化
        $value = json_encode($data);
        $key   = $this->_redisPreKey . 'age:' . $time;
        $this->_redis->set($key, $value, '', 3600 * 24);

        //清除缓存
        $this->_cacheDataArr($this->_dayCode, 'age', 'del');
    }

    /**
     * 获取过去50笔订单的游客轨迹
     */
    private function _getTouristTrail()
    {
        $orderQueryJavaBiz = new OrderQueryJavaService();
        $nowTime           = date('Y-m-d H:i:s');
        $selectParams      = [
            'field'   => 'ordertime, ordertel, lid',
            'limit'   => 5,
            'orderBy' => ['id' => 'desc'],
        ];
        $data              = $orderQueryJavaBiz->getOrderInfoByOrderTimeAndOtherParam([[$nowTime], 'elt'], [],
            $selectParams);

        $res = [];
        foreach ($data as $item) {
            //批量获取订单的lid ordertel
            $lidArr[] = $item['lid'];
            $telArr[] = substr($item['ordertel'], 0, 7);
            $res[]    = [
                'begin' => substr($item['ordertel'], 0, 7),
                'end'   => $item['lid'],
            ];
        }

        $telRes = [];
        if (!empty($telArr)) {
            $model   = new BigDataPanel\TelArea();
            $telArr  = array_unique($telArr);
            $telArr  = array_filter($telArr);
            $telInfo = $model->getInfoByTelArr($telArr);
            foreach ($telInfo as $item) {
                $tel = substr($item['tel'], 0, 7);
                unset($item['tel']);
                $telRes[$tel] = $item['city'];
            }
        }

        $lidRes = [];
        if (!empty($lidArr)) {
            //$model   = new Land();
            $lidArr = array_unique($lidArr);
            $lidArr = array_filter($lidArr);
            //$lidInfo = $model->getLandInfoByMuli($lidArr, 'id, area');

            $javaAPi = new \Business\CommodityCenter\Land();
            $lidInfo = $javaAPi->queryLandMultiQueryById($lidArr);
            foreach ($lidInfo as $item) {
                $areaArr             = explode("|", $item['area']);
                $city                = isset($areaArr[1]) ? $areaArr[1] : 0;
                $lidRes[$item['id']] = $city;
            }
        }

        $telConfig  = load_config('tel_city', 'account');
        $cityConfig = load_config('cities', 'account');
        foreach ($cityConfig as $province) {
            foreach ($province as $cityInfo) {
                $cityRes[$cityInfo['area_id']] = $cityInfo['area_name'];
            }
        }

        foreach ($res as $item) {
            if (!isset($telConfig[$telRes[$item['begin']]])) {
                continue;
            }
            if (!isset($cityRes[$lidRes[$item['end']]])) {
                continue;
            }
            $begin              = isset($telConfig[$telRes[$item['begin']]]) ? $telConfig[$telRes[$item['begin']]] : '未知';
            $end                = isset($cityRes[$lidRes[$item['end']]]) ? $cityRes[$lidRes[$item['end']]] : '未知';
            $this->_trailData[] = [
                'begin' => $begin,
                'end'   => $end,
            ];
        }
    }

    /**
     * 游客轨迹
     * 存入数据库
     */
    private function _insertTouristTrail()
    {
        $model     = new BigDataPanel\PftBigTrail();
        $result    = $model->insertData($this->_trailData);
        $logResult = empty($result) ? 'fail' : 'success';
        pft_log($this->_log, '游客轨迹脚本执行结果:' . $logResult);
    }

    /**
     * 今年的检票数（累积接待游客数）  今年的预订票数（截止到脚本执行的上一日）
     *
     * @param $time
     */
    private function _other($time)
    {
        pft_log('bigdata/pre_cache', 'start');

        $bigDataBiz = new BigDataPanelBiz();

        //平台销售额
        $saleRes = $bigDataBiz->getYearSaleMoney($isSuper = true, 0, $time);
        pft_log('bigdata/pre_cache', json_encode(['sale', $saleRes]));

        //平台预定数量
        $ticketOrderRes = $bigDataBiz->getYearTicektnum($dataType = 2, $isSuper = true, 0, $time);
        pft_log('bigdata/pre_cache', json_encode(['ticket_order', $ticketOrderRes]));

        //平台验证数量
        $ticketCheckRes = $bigDataBiz->getYearTicektnum($dataType = 1, $isSuper = true, 0, $time);
        pft_log('bigdata/pre_cache', json_encode(['ticket_check', $ticketCheckRes]));

        return true;
    }

    /**
     * 每小时统计订单 检票数量
     */
    private function _getHourData($hour)
    {
        $model            = new Statistics(Statistics::BI_SUMMARY_PF);
        $this->_orderData = $model->getLastHourData($hour, 1);
        $this->_checkData = $model->getLastHourData($hour, 2);
    }

    /**
     * 每小时统计订单 检票数量
     */
    private function _insertHourData($hour)
    {
        //如果统计应该是23点的数据 说明脚本执行时间在 第二天的0点
        if ($hour == 23) {
            $date = date('Ymd', time() - 3600);
        } else {
            $date = date('Ymd');
        }
        $model = new BigDataPanel\PftBigHourData();

        //先删除数据
        $model->deleteOrderData($hour, $date);

        foreach ($this->_orderData as $value) {
            $insertData[] = [
                'date'   => $date,
                'hour'   => $hour,
                'order'  => empty($value['order_num']) ? 0 : $value['order_num'],
                'ticket' => empty($value['ticket_num']) ? 0 : $value['ticket_num'],
            ];
            if (count($insertData) > 500) {
                $res       = $model->insertOrderData($insertData);
                $logResult = empty($res) ? 'fail' : 'success';
                pft_log($this->_log, '每小时预订订单数量执行结果:' . $logResult);
                unset($insertData);
            }
        }

        if (!empty($insertData)) {
            $res       = $model->insertOrderData($insertData);
            $logResult = empty($res) ? 'fail' : 'success';
            pft_log($this->_log, '每小时预订订单数量执行结果:' . $logResult);
            unset($insertData);
        }

        //先删除数据
        $model->deleteCheckData($hour, $date);

        foreach ($this->_checkData as $value) {
            $insertData[] = [
                'date'   => $date,
                'hour'   => $hour,
                'order'  => empty($value['order_num']) ? 0 : $value['order_num'],
                'ticket' => empty($value['ticket_num']) ? 0 : $value['ticket_num'],
            ];
            if (count($insertData) > 500) {
                $res       = $model->insertCheckData($insertData);
                $logResult = empty($res) ? 'fail' : 'success';
                pft_log($this->_log, '每小时预订订单数量执行结果:' . $logResult);
                unset($insertData);
            }
        }

        if (!empty($insertData)) {
            $res       = $model->insertCheckData($insertData);
            $logResult = empty($res) ? 'fail' : 'success';
            pft_log($this->_log, '每小时预订订单数量执行结果:' . $logResult);
            unset($insertData);
        }

        unset($this->_orderData);
        unset($this->_checkData);
    }

    private function _getLandModel()
    {
        if (empty($this->_landModel)) {
            $this->_landModel = new Land();
        }

        return $this->_landModel;
    }

    private function _getTripNumbersModel()
    {
        if (empty($this->_tripNumbersModel)) {
            $this->_tripNumbersModel = new BigDataPanel\PftBigTripNumbers();
        }

        return $this->_tripNumbersModel;
    }

    private function _getOrderBookModel()
    {
        if (empty($this->_orderBookModel)) {
            $this->_orderBookModel = new BigDataPanel\PftBigOrderBook();
        }

        return $this->_orderBookModel;
    }

    private function _getDistriChannelModel()
    {
        if (empty($this->_channelModel)) {
            $this->_channelModel = new BigDataPanel\PftBigDistriChannel();
        }

        return $this->_channelModel;
    }

    private function _getBigAgeModel()
    {
        if (empty($this->_bigAgeModel)) {
            $this->_bigAgeModel = new BigDataPanel\PftBigAge();
        }

        return $this->_bigAgeModel;
    }

    private function _getBigSexModel()
    {
        if (empty($this->_bigSexModel)) {
            $this->_bigSexModel = new BigDataPanel\PftBigSex();
        }

        return $this->_bigSexModel;
    }

    private function _getBigTouristPlaceModel()
    {
        if (empty($this->_bigTouristPlaceModel)) {
            $this->_bigTouristPlaceModel = new BigDataPanel\PftBigTouristPlace();
        }

        return $this->_bigTouristPlaceModel;
    }

    /**
     * 文件缓存
     *
     * @param $day
     * @param $type
     * @param  string $operation
     * @param  array $dataArr
     *
     * @return array|bool|mixed
     */
    private function _cacheDataArr($day, $type, $operation = 'get', $dataArr = [])
    {
        $pid       = getmypid();
        $path      = BASE_LOG_DIR . "/BigDataCache/{$type}";
        $cacheFile = $path . "/{$day}_{$pid}.log";

        //创建目录
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }

        if ($operation == 'get') {
            //获取数据
            if (file_exists($cacheFile)) {
                $tmp = file_get_contents($cacheFile);
                $tmp = @json_decode(gzdecode($tmp), true);

                return $tmp ? $tmp : [];
            } else {
                return [];
            }
        } elseif ($operation == 'set') {
            //缓存数据
            $tmp = gzencode(json_encode($dataArr));
            file_put_contents($cacheFile, $tmp);

            return true;
        } else {
            //清除缓存数据
            $res = true;
            if (file_exists($cacheFile)) {
                $res = @unlink($cacheFile);
            }

            return $res;
        }
    }

    protected function _getTrackTotal($date, $type, $orderNum = [])
    {
        $actionArr = [4];
        if (empty($actionArr)) {
            throw new \Exception('action列表不存在' . $type, 403);
        }

        $startTime      = date('Y-m-d H:i:s', strtotime($date[0]));
        $endTime        = date('Y-m-d H:i:s', strtotime($date[1]));
        $isChangeTicket = true;

        $total       = 0;
        $queryParams = [$endTime, $startTime, $isChangeTicket, $actionArr];

        $queryRes = \Business\JavaApi\Order\Query\Container::query('orderTrack', 'queryOrderTrackCountByInsertTime',
            $queryParams);

        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $total = $queryRes['data'];
        }

        return $total;
    }

    /**
     * 获取追踪数据
     */
    protected function _getTrackList($date, $page, $size, $orderNum = [])
    {
        $actionArr = [4];
        $filter    = [
            'action' => ['in', $actionArr],
        ];

        $startTime = date('Y-m-d H:i:s', strtotime($date[0]));
        $endTime   = date('Y-m-d H:i:s', strtotime($date[1]));

        $filter['insertTime'] = ['between', [$startTime, $endTime]];

        //由于分终端验证时 未全部验证的 完结票数为0  所以要取等于0的记录
        $filter['tnum'] = ['gt', 0];
        if (!empty($orderNum) && is_array($orderNum)) {
            $filter['ordernum'] = ['IN', $orderNum];
        }

        $field     = 'id, ordernum, tnum, left_num, action, branchTerminal, oper_member, source, SalerID';
        $trackList = $this->_getTrackModel()->getList($filter, $field, $page, $size);

        return is_array($trackList) ? $trackList : [];
    }

    protected function _getTrackModel($force = false)
    {
        if (empty($this->_trackModel) || $force) {
            $this->_trackModel = new OrderTrack(true);
        }

        return $this->_trackModel;
    }

    /**
     * 获取订单数据
     *
     * @param $orderArr
     * @param  bool $isGetPayStatus
     * @param  string $extField
     *
     * @return array
     */
    protected function _getOrderInfo($orderArr)
    {
        if (!$orderArr) {
            return [];
        }

        $orderToolModel = $this->_getOrderToolModel();

        //分批获取订单数据，怕一次性数据太多了
        $piece = ceil(count($orderArr) / 3000);
        $res   = [];

        $extField = 'ordertime, playtime, ordernum, ordermode, ordertel, tnum, lid, pid, personid, member, aid, ordername';
        for ($i = 0; $i < $piece; $i++) {
            $queryIds = array_slice($orderArr, $i * 3000, 3000);

            //将订单ID转换为字符类型
            $queryIds = array_map(function ($v) {
                return strval($v);
            }, $queryIds);

            $tmp = $orderToolModel->getOrderList($queryIds, $extField, false, false, false);

            if ($tmp && is_array($tmp)) {
                $res = array_merge($res, $tmp);
            }
        }

        return $res;
    }

    protected function _getOrderToolModel($force = false)
    {
        if (empty($this->_orderToolModel) || $force) {
            $this->_orderToolModel = new OrderTools();
        }

        return $this->_orderToolModel;
    }

    public function getBookDataByTime($begin, $end)
    {
        $total = [
            'the_same_day' => 0,
            'before_one' => 0,
            'before_three' => 0,
            'before_seven' => 0,
            'before_other' => 0,
            'the_same_day_ticket' => 0,
            'before_one_ticket' => 0,
            'before_three_ticket' => 0,
            'before_seven_ticket' => 0,
            'before_other_ticket' => 0,
        ];
        while ($begin <= $end) {
            $res   = (new BigDataPanel\PftBigOrderBook())->getDataByTime($begin, $begin);
            $begin = date('Ymd', strtotime($begin . ' +1 day'));
            $total['the_same_day'] += $res['the_same_day'];
            $total['before_one'] += $res['before_one'];
            $total['before_three'] += $res['before_three'];
            $total['before_seven'] += $res['before_seven'];
            $total['before_other'] += $res['before_other'];
            $total['the_same_day_ticket'] += $res['the_same_day_ticket'];
            $total['before_one_ticket'] += $res['before_one_ticket'];
            $total['before_three_ticket'] += $res['before_three_ticket'];
            $total['before_seven_ticket'] += $res['before_seven_ticket'];
            $total['before_other_ticket'] += $res['before_other_ticket'];
        }
        return $total;
    }

    public function getBookDataByMode($begin, $end)
    {
        $total = [
            'order'  => 0,
            'ticket' => 0,
        ];
        while ($begin <= $end) {
            $res   = (new BigDataPanel\PftBigOrderBook())->getDataByMode($begin, $begin, $this->_offLineOrderMode);
            $begin = date('Ymd', strtotime($begin . ' +1 day'));
            $total['order'] += $res['order'];
            $total['ticket'] += $res['ticket'];
        }
        return $total;
    }
}
