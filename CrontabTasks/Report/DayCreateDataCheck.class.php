<?php
/**
 * 每日报表数据生成检测
 * User: xiexy
 * Date: 2021-03-24
 */

namespace CrontabTasks\Report;
use \Library\Cache\Cache;
use Library\Tools\Helpers;
use Library\Constants\DingTalkRobots;
use Library\Constants\WebHookSendKeyConst;

class DayCreateDataCheck
{
    //钉钉机器人token
    //private $_dingAccessToken = 'e61f712b077d28b7967c9d6036f3348319cab857d779dcffa08a0553ec7ee9ac';
    private $_dingAccessToken = DingTalkRobots::REAL_REPORT_ROBOI; //统一到机器人配置文件下
    //钉钉消息关键词
    private $_keyWord = "报表告警:\r";


    public function check()
    {
        //暂时对预计及验证报表进行监控
        $cacheKeyList = [
            'statis_report:create_data:order_v2',
            'statis_report:create_data:check_v2',
        ];

        $cacheKeyListMap = [
            'statis_report:create_data:order_v2' => '预订报表',
            'statis_report:create_data:check_v2' => '验证报表',
        ];

        $dingAt   = false;
        $warnging = '';
        $cache    = Cache::getInstance('redis');

        foreach ($cacheKeyList as $cacheKey) {
            $cacheValue = $cache->sMembers($cacheKey);
            if (!empty($cacheValue)) {
                foreach ($cacheValue as $tmpValue) {
                    $dingAt = true;
                    $warnging .= ($cacheKeyListMap[$cacheKey] ?? '') . " [{$tmpValue}] 生成数据失败。\n-----------------------\n";
                }
            }
        }

        $tableList = [
            'pft_report_order_two',
            'pft_report_checked_two',
            'pft_report_pack_order',
            'pft_report_pack_checked',
        ];

        $tableListMap = [
            'pft_report_order_two' => '预订日报表',
            'pft_report_checked_two' => '验证日报表',
            'pft_report_pack_order' => '套票预订日报表',
            'pft_report_pack_checked' => '套票验证日报表',
        ];

        $model = new \Model\Report\Statistics();
        $date  = date('Ymd', strtotime('-1 days'));
        $where = ['date' => $date];

        foreach ($tableList as $table) {
            $count = $model->table($table)->where($where)->count();
            $warnging .= ($tableListMap[$table] ?? '') . " {$table}：生成[{$date}]日报表数据记录数：{$count}\n";

            $warngingCount = in_array($table, ['pft_report_checked_two', 'pft_report_order_two']) ? 20000 : 2000;
            if ($count < $warngingCount) {
                $dingAt = true;
                $warnging .= ($tableListMap[$table] ?? '') . " {$table}：生成的数据量较少\n-----------------------\n";
            }
        }

        $res = '';
        if (!empty($warnging)) {
            // $warnging = $this->_keyWord . $warnging;
            // $this->_dingWarnging($warnging, $dingAt);

            $warnging .= $dingAt ? '报表数据生成异常，请确认核实' : '';

            //统一通知机器人
            $configKey = $dingAt ? WebHookSendKeyConst::REPORT_NEXT_DAY_EARLY_WARNING : WebHookSendKeyConst::REPORT_NEXT_DAY_NOTICE;
            $res = Helpers::sendGroupRobotTextMessages($warnging, $configKey);
        }

        //结果直接输出
        echo $warnging . "\n";
        echo json_encode([$res], JSON_UNESCAPED_UNICODE) . "\n";

        pft_log('statistic_report_check', json_encode([$warnging, $res], JSON_UNESCAPED_UNICODE));
    }


    public function _dingWarnging(string $message, bool $atAll = false)
    {
        $atMobile = [];
        if ($atAll) {
            $atMobile = [13255919916];
        }

        if (ENV == 'PRODUCTION') {
            Helpers::sendDingTalkGroupRobotMessageRaw($message, $this->_dingAccessToken, $atAll, $atMobile);
        }
    }

    /**
     * 重试
     */
    public function retry()
    {


    }


    public function test()
    {
        //$message = $this->_keyWord . ' message test...';
        //$this->_dingWarnging($message);
    }


}