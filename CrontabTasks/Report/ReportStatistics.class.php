<?php
/**
 * 报表统计
 *
 * <AUTHOR>
 * @date 2016-01-20
 *
 */

namespace CrontabTasks\Report;

use Business\Statistics\CreateCancelReport;
use Business\Statistics\CreateCheckV2HourReport;
use Business\Statistics\CreateCheckV2Report;
use Business\Statistics\CreateReserveReport;
use Business\Statistics\CreateOrderPayReport;
use Business\Statistics\CreateOrderV2HourReport;
use Business\Statistics\CreateOrderV2Report;
use Business\Statistics\CreatePackCheckedReport;
use Business\Statistics\CreatePackOrderReport;
use Business\Statistics\CreateResourceOrderMonthReport;
use Business\Statistics\CreateResourceOrderReport;
use Business\Statistics\CreateTeamCheckReport;
use Business\Statistics\AnnualCardOrderReport;
use Business\Statistics\AnnualCardCheckReport;
use Business\Statistics\CreateTerminalBuyReport;
use Business\Statistics\TerminalPassReport;
use Business\Statistics\CreateTerminalPassHourReport;
use Library\Controller;
use Business\Statistics\CreateSeparateOrderReport;
use Business\Statistics\CreateSeparateCheckedReport;
use Business\Statistics\CreateSeriesOrderReport;
use Business\Statistics\CreateSeriesCheckedReport;
use Model\Report\StatisticsV2;
use Business\Statistics\TradeReport;
use Business\Statistics\TradeReportMonth;
use Business\Statistics\CreateCustomizeReport;
use Business\Statistics\CreateMinuteReportToday;

use Business\Statistics\CreateTerminalBranchHourReport;
use Business\Statistics\CreateTerminalBranchReport;
use Business\Statistics\CreateCustomizeMonthReport;
use Business\Statistics\CreateSeriesOrderMonthReport;
use Business\Statistics\CreateSeriesCheckMonthReport;
use Business\Statistics\CreateTerminalCheckedReport;

//台账
use Business\Statistics\CreateStandingBookOrder;
use Business\Statistics\CreateStandingBookChecked;

//分钟报表
use Business\Statistics\CreateOrderV2MinuteReport;
use Business\Statistics\CreateCheckV2MinuteReport;

use \Library\Cache\Cache;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class ReportStatistics extends Controller
{

    private $_logPath = 'statistic_report';

    public function __construct()
    {
        //运行时间不做限制
        set_time_limit(0);

        $this->_model = new \Model\Report\Statistics();
    }

    /**
     * 已废弃
     * 日报表    php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTask 2021-04-04 order_v2 0 1
     * 3是执行日期  4报表类型 5指定商户 6新旧版分时段处理
     */
    public function runTask()
    {
        $params = $GLOBALS['argv'];

        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $fid = isset($params[5]) ? $params[5] : 0;
        if (isset($params[6])) {
            $timeSepareType = $params[6];
        } else {
            $timeSepareType = (int) (new \Business\PftSystem\VacationModeBiz())->isOpenVacation();
        }

        if (!isset($params[4])) {
            $orderReportBusiness = new CreateOrderV2Report($this->_logPath);
            $orderReportBusiness->runTask($date, $fid, $timeSepareType);

            $orderReportBusiness = new CreateCheckV2Report($this->_logPath);
            $orderReportBusiness->runTask($date, $fid, $timeSepareType);

            $orderReportBusiness = new CreateReserveReport($this->_logPath);
            $orderReportBusiness->runTask($date, $fid, $timeSepareType);
        }

        //需要执行的报表
        if (isset($params[4])) {
            $listArr = explode(',', $params['4']);
            if (!empty($listArr)) {
                foreach ($listArr as $item) {
                    switch ($item) {

                        //新版预定
                        case 'order_v2':
                            $orderReportBusiness = new CreateOrderV2Report($this->_logPath);
                            $orderReportBusiness->runTask($date, $fid, $timeSepareType);
                            break;

                        //新版验证
                        case 'checked_v2':
                            $orderReportBusiness = new CreateCheckV2Report($this->_logPath);
                            $orderReportBusiness->runTask($date, $fid, $timeSepareType);
                            break;

                        //预约报表
                        case 'reserve_order':
                            $orderReportBusiness = new CreateReserveReport($this->_logPath);
                            $orderReportBusiness->runTask($date, $fid, $timeSepareType);
                            break;
                    }
                }
            }
        }

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 预订报表定时任务
     * 日报表    php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTaskOrderV2 2021-04-04 0 1
     * 3是执行日期  4指定商户 5新旧版分时段处理
     */
    public function runTaskOrderV2()
    {
        $params = $GLOBALS['argv'];

        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $fid = isset($params[4]) ? $params[4] : 0;
        if (isset($params[5])) {
            $timeSepareType = $params[5];
        } else {
            $timeSepareType = (int) (new \Business\PftSystem\VacationModeBiz())->isOpenVacation();
        }
        $cache = \Library\Cache\RedisCache::Connect('master');
        $orderReportBusiness = new CreateOrderV2Report($this->_logPath,$date);
        list($reportResultError,$reportResultSuccess) = $orderReportBusiness->getReportResultKey();
        if(!$cache->exists($reportResultSuccess) || $cache->exists($reportResultError)){
            echo "执行脚本: runTaskOrderV2,[{$cache->exists($reportResultSuccess)},{$cache->exists($reportResultError)}] \n";
            $orderReportBusiness->runTask($date, $fid, $timeSepareType);
        }
        else{
            echo "无需重试: runTaskOrderV2,[{$cache->exists($reportResultSuccess)},{$cache->exists($reportResultError)}] \n";
        }
        $cache->close();
        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 验证报表定时任务
     * 日报表    php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTaskCheckedV2 2021-04-04 0 1
     * 3是执行日期  4指定商户 5新旧版分时段处理
     */
    public function runTaskCheckedV2()
    {
        $params = $GLOBALS['argv'];

        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $fid = isset($params[4]) ? $params[4] : 0;
        if (isset($params[5])) {
            $timeSepareType = $params[5];
        } else {
            $timeSepareType = (int) (new \Business\PftSystem\VacationModeBiz())->isOpenVacation();
        }
        $cache = \Library\Cache\RedisCache::Connect('master');
//        $cache->setOption(\Redis::OPT_READ_TIMEOUT,-1);
        $orderReportBusiness = new CreateCheckV2Report($this->_logPath,$date);
        list($reportResultError,$reportResultSuccess) = $orderReportBusiness->getReportResultKey();
        if(!$cache->exists($reportResultSuccess) || $cache->exists($reportResultError)){
            echo "执行脚本: runTaskOrderV2,[{$cache->exists($reportResultSuccess)},{$cache->exists($reportResultError)}] \n";
            $orderReportBusiness->runTask($date, $fid, $timeSepareType);
        }
        else{
            echo "无需重试: runTaskOrderV2,[{$cache->exists($reportResultSuccess)},{$cache->exists($reportResultError)}] \n";
        }
        $cache->close();
        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 预约报表定时任务
     * 日报表    php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTaskReserve 2021-04-04 0 1
     * 3是执行日期  4指定商户 5新旧版分时段处理
     */
    public function runTaskReserve()
    {
        $params = $GLOBALS['argv'];

        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $fid = isset($params[4]) ? $params[4] : 0;
        if (isset($params[5])) {
            $timeSepareType = $params[5];
        } else {
            $timeSepareType = (int) (new \Business\PftSystem\VacationModeBiz())->isOpenVacation();
        }

        $orderReportBusiness = new CreateReserveReport($this->_logPath);
        $orderReportBusiness->runTask($date, $fid, $timeSepareType);

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 小时报表    php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTaskHour 2021-04-04 order_v2_hour 0
     */
    public function runTaskHour()
    {
        $params = $GLOBALS['argv'];
        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $type = isset($params[4]) ? $params[4] : '';
        $fid  = isset($params[5]) ? $params[5] : 0;
        if (isset($params[6])) {
            $timeSepareType = $params[6];
        } else {
            $timeSepareType = (int) (new \Business\PftSystem\VacationModeBiz())->isOpenVacation();
        }

        if (!$type) {

            $orderReportBusiness = new CreateOrderV2HourReport($this->_logPath,$date);
            $orderReportBusiness->runTask($date, $fid, $timeSepareType);

            $orderReportBusiness = new CreateCheckV2HourReport($this->_logPath,$date);
            $orderReportBusiness->runTask($date, $fid, $timeSepareType);

            $terminalBranchBusiness = new CreateTerminalBranchHourReport($this->_logPath);
            $terminalBranchBusiness->runTask($date);

            //终端过闸汇总小时报表
            $orderReportBusiness = new CreateTerminalPassHourReport($this->_logPath);
            $orderReportBusiness->runTask($date);

        } else {
            switch ($type) {
                //新版预定(按小时汇总)
                case 'order_v2_hour':
                    $orderReportBusiness = new CreateOrderV2HourReport($this->_logPath,$date);
                    $orderReportBusiness->runTask($date, $fid, $timeSepareType);
                    break;

                //新版验证
                case 'checked_v2_hour':
                    $orderReportBusiness = new CreateCheckV2HourReport($this->_logPath,$date);
                    $orderReportBusiness->runTask($date, $fid, $timeSepareType);
                    break;

                case 'terminal_branch_hour':
                    $terminalBranchBusiness = new CreateTerminalBranchHourReport($this->_logPath);
                    $terminalBranchBusiness->runTask($date);
                    break;

                //终端过闸汇总小时报表
                case 'terminal_pass_hour':
                    $orderReportBusiness = new CreateTerminalPassHourReport($this->_logPath);
                    $orderReportBusiness->runTask($date);
                    break;
            }
        }

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 小时报表重试脚本    php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTaskHourV2 2021-04-04 order_v2_hour 0
     */
    public function runTaskHourV2()
    {
        $params = $GLOBALS['argv'];
        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $type = isset($params[4]) ? $params[4] : '';
        $fid  = isset($params[5]) ? $params[5] : 0;
        if (isset($params[6])) {
            $timeSepareType = $params[6];
        } else {
            $timeSepareType = (int) (new \Business\PftSystem\VacationModeBiz())->isOpenVacation();
        }
        $cache = \Library\Cache\RedisCache::Connect('master');
//        $cache->setOption(\Redis::OPT_READ_TIMEOUT,-1);
        if (!$type) {
            $orderReportBusiness = new CreateOrderV2HourReport($this->_logPath,$date);
            list($reportResultError,$reportResultSuccess) = $orderReportBusiness->getReportResultKey();
            if(!$cache->exists($reportResultSuccess) || $cache->exists($reportResultError)){
                $orderReportBusiness->runTask($date, $fid, $timeSepareType);
            }
            $orderReportBusiness = new CreateCheckV2HourReport($this->_logPath,$date);
            list($reportResultError,$reportResultSuccess) = $orderReportBusiness->getReportResultKey();
            if(!$cache->exists($reportResultSuccess) || $cache->exists($reportResultError)){
                $orderReportBusiness->runTask($date, $fid, $timeSepareType);
            }
        } else {
            switch ($type) {
                //新版预定(按小时汇总)
                case 'order_v2_hour':
                    $orderReportBusiness = new CreateOrderV2HourReport($this->_logPath,$date);
                    list($reportResultError,$reportResultSuccess) = $orderReportBusiness->getReportResultKey();
                    if(!$cache->exists($reportResultSuccess) || $cache->exists($reportResultError)){
                        $orderReportBusiness->runTask($date, $fid, $timeSepareType);
                    }
                    break;

                //新版验证
                case 'checked_v2_hour':
                    $orderReportBusiness = new CreateCheckV2HourReport($this->_logPath,$date);
                    list($reportResultError,$reportResultSuccess) = $orderReportBusiness->getReportResultKey();
                    if(!$cache->exists($reportResultSuccess) || $cache->exists($reportResultError)){
                        $orderReportBusiness->runTask($date, $fid, $timeSepareType);
                    }
                    break;
            }
        }
        $cache->close();
        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTaskV2 2021-06-01 pack_order -[参数5：指定商户]- -[参数6：分终端数据使用入园计入报表版本]- -[参数7：细分时间段还是旧版时间段]-
     * 例子：php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTaskV2 2021-07-02 pack_order 0 0 1
     */

    public function runTaskV2()
    {
        $params = $GLOBALS['argv'];
        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $fid = isset($params[5]) ? $params[5] : 0;
        $tmpParams = isset($params[6]) ? $params[6] : 0;

        //细分时段处理
        if (isset($params[7])) {
            $timeSepareType = $params[7];
        } else {
            $timeSepareType = (int) (new \Business\PftSystem\VacationModeBiz())->isOpenVacation();
        }

        if (!isset($params[4])) {

            //迁移至单独任务：\CrontabTasks\Report\ReportStatistics::runPackOrderTask
            // $orderReportBusiness = new CreatePackOrderReport($this->_logPath);
            // $orderReportBusiness->runTask($date, $fid, $timeSepareType);
            //
            // $orderReportBusiness = new CreatePackCheckedReport($this->_logPath);
            // $orderReportBusiness->runTask($date,$fid, $tmpParams, $timeSepareType);

            $orderReportBusiness = new CreateCancelReport($this->_logPath);
            $orderReportBusiness->runTask($date, $timeSepareType);

            //迁移至单任务：\CrontabTasks\Report\ReportStatistics::runOrderPayTask
            // $orderReportBusiness = new CreateOrderPayReport($this->_logPath);
            // $orderReportBusiness->runTask($date, $timeSepareType);
            //暂停10秒 数据库重新链接
            sleep(10);

            //分账预订
            //$orderReportBusiness = new CreateSeparateOrderReport($this->_logPath);
            //$orderReportBusiness->runTask($date);
            //sleep(10);
            ////分账验证
            //$orderReportBusiness = new CreateSeparateCheckedReport($this->_logPath);
            //$orderReportBusiness->runTask($date);
            //sleep(10);

            //迁至单任务：\CrontabTasks\Report\ReportStatistics::runAnnualCardTask
            // //年卡预订、激活报表
            // $orderReportBusiness = new AnnualCardOrderReport($this->_logPath);
            // $orderReportBusiness->runTask($date);
            // sleep(10);
            // //年卡验证报表
            // $orderReportBusiness = new AnnualCardCheckReport($this->_logPath);
            // $orderReportBusiness->runTask($date);
            // sleep(10);
            $orderReportBusiness = new CreateTeamCheckReport($this->_logPath);
            $orderReportBusiness->runTask($date);
            sleep(10);


            //演出预定
            //$orderReportBusiness = new CreateSeriesOrderReport($this->_logPath);
            //$orderReportBusiness->runTask($date);
            //sleep(10);
            ////演出验证
            //$orderReportBusiness = new CreateSeriesCheckedReport($this->_logPath);
            //$orderReportBusiness->runTask($date);
            //sleep(10);

            // 迁至单任务：\CrontabTasks\Report\ReportStatistics::runTerminalPassTask
            // $orderReportBusiness = new TerminalPassReport($this->_logPath);
            // $orderReportBusiness->runTask($date);
            // sleep(10);

            //$resourceOrderBusiness = new CreateResourceOrderReport($this->_logPath);
            //$resourceOrderBusiness->runTask($date);

            // 迁至单任务：\CrontabTasks\Report\ReportStatistics::runTerminalBranchTaskV2
            // $terminalBranchBusiness = new CreateTerminalBranchReport($this->_logPath);
            // $terminalBranchBusiness->runTask($date);

            // sleep(10);

            // 迁至单任务：\CrontabTasks\Report\ReportStatistics::runStandingBookTask
            // //台账预订
            // $standingBookOrderBusiness = new CreateStandingBookOrder($this->_logPath);
            // $standingBookOrderBusiness->runTask($date);
            //
            // sleep(10);
            //
            // //台账验证
            // $standingBookCheckBusiness = new CreateStandingBookChecked($this->_logPath);
            // $standingBookCheckBusiness->runTask($date);
        }

        //需要执行的报表
        if (isset($params[4])) {
            $listArr = explode(',', $params['4']);
            if (!empty($listArr)) {
                foreach ($listArr as $item) {
                    switch ($item) {

                        //套票预定
                        case 'pack_order':
                            $orderReportBusiness = new CreatePackOrderReport($this->_logPath);
                            $orderReportBusiness->runTask($date, $fid, $timeSepareType);
                            break;

                        //套票验证
                        case 'pack_checked':
                            $orderReportBusiness = new CreatePackCheckedReport($this->_logPath);
                            $orderReportBusiness->runTask($date, $fid, $tmpParams, $timeSepareType);
                            break;

                        //旧版取消
                        case 'cancel':
                            $orderReportBusiness = new CreateCancelReport($this->_logPath);
                            $orderReportBusiness->runTask($date, $timeSepareType);
                            break;

                        //营业汇总
                        case 'order_pay':
                            $orderReportBusiness = new CreateOrderPayReport($this->_logPath);
                            $orderReportBusiness->runTask($date, $timeSepareType);
                            break;

                        //团队验证报表
                        case 'team_check':
                            $orderReportBusiness = new CreateTeamCheckReport($this->_logPath);
                            $orderReportBusiness->runTask($date);
                            break;

                        //分账预订报表
                        case 'separate_order':
                            $orderReportBusiness = new CreateSeparateOrderReport($this->_logPath);
                            $orderReportBusiness->runTask($date);
                            break;

                        //分账验证报表
                        case 'separate_check':
                            $orderReportBusiness = new CreateSeparateCheckedReport($this->_logPath);
                            $orderReportBusiness->runTask($date);
                            break;

                        //演出预定报表
                        case 'series_order':
                            $orderReportBusiness = new CreateSeriesOrderReport($this->_logPath);
                            $orderReportBusiness->runTask($date);
                            break;
                        case 'series_checked':
                            $orderReportBusiness = new CreateSeriesCheckedReport($this->_logPath);
                            $orderReportBusiness->runTask($date);
                            break;

                        //年卡预订、激活报表
                        #php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTaskV2 2023-12-22 annual_card_order 34779789
                        case 'annual_card_order':
                            $orderReportBusiness = new AnnualCardOrderReport($this->_logPath);
                            $orderReportBusiness->runTask($date);
                            break;

                        //年卡验证报表
                        case 'annual_card_check':
                            $orderReportBusiness = new AnnualCardCheckReport($this->_logPath);
                            $orderReportBusiness->runTask($date);
                            break;

                        //终端过闸汇总报表
                        case 'terminal_pass':
                            $orderReportBusiness = new TerminalPassReport($this->_logPath);
                            $orderReportBusiness->runTask($date);
                            break;

                        //资源中心预定报表
                        case 'resource_order':
                            $resourceOrderBusiness = new CreateResourceOrderReport($this->_logPath);
                            $resourceOrderBusiness->runTask($date);
                            break;

                        //定制型报表
                        case 'customize_report':
                            $customizeReportBusiness = new CreateCustomizeReport($this->_logPath);
                            $customizeReportBusiness->runTask($date);
                            break;

                        //定制型报表
                        case 'terminal_branch':
                            $terminalBranchBusiness = new CreateTerminalBranchReport($this->_logPath);
                            $terminalBranchBusiness->runTask($date);
                            break;

                        //台账预订
                        case 'order_v2_sta':
                            $standingBookOrderBusiness = new CreateStandingBookOrder($this->_logPath);
                            $standingBookOrderBusiness->runTask($date);
                            break;

                        //台账验证
                        case 'checked_v2_sta':
                            $standingBookCheckBusiness = new CreateStandingBookChecked($this->_logPath);
                            $standingBookCheckBusiness->runTask($date);
                            break;
                    }
                }
            }
        }

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 套票日报表入口
     * <AUTHOR>
     * @date   2025/04/01
     *
     * @命令：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runPackOrderTask -[参数3：日期Y-m-d]- -[参数4：执行类型]- -[参数5：指定商户]- -[参数6：分终端数据使用入园计入报表版本]- -[参数7：细分时间段还是旧版时间段]-
     * @例子：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runPackOrderTask 2025-04-01 pack_order 0 0 1
     */
    public function runPackOrderTask()
    {
        $params = $GLOBALS['argv'];
        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }
            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $fid       = $params[5] ?? 0;
        $tmpParams = $params[6] ?? 0;

        //细分时段处理，假日模式下时间颗粒更细
        $timeSepareType = $params[7] ?? (int)(new \Business\PftSystem\VacationModeBiz())->isOpenVacation();
        if (!isset($params[4])) {
            $orderReportBusiness = new CreatePackOrderReport($this->_logPath);
            $orderReportBusiness->runTask($date, $fid, $timeSepareType);

            $orderReportBusiness = new CreatePackCheckedReport($this->_logPath);
            $orderReportBusiness->runTask($date, $fid, $tmpParams, $timeSepareType);
        } else {
            //需要执行的报表
            $type = $params['4'];
            if (!empty($type)) {
                switch ($type) {
                    //套票预定
                    case 'pack_order':
                        $orderReportBusiness = new CreatePackOrderReport($this->_logPath);
                        $orderReportBusiness->runTask($date, $fid, $timeSepareType);
                        break;
                    //套票验证
                    case 'pack_checked':
                        $orderReportBusiness = new CreatePackCheckedReport($this->_logPath);
                        $orderReportBusiness->runTask($date, $fid, $tmpParams, $timeSepareType);
                        break;
                    default:
                        echo "无效执行类型!";
                        exit;
                }
            }
        }

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 营业汇总报表入口
     * <AUTHOR>
     * @date   2025/04/01
     *
     * @命令：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runOrderPayTask -[参数3：日期Y-m-d]- -[参数4：细分时间段还是旧版时间段]-
     * @例子：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runOrderPayTask 2025-04-01 1
     */
    public function runOrderPayTask()
    {
        $params = $GLOBALS['argv'];
        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }
            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        //细分时段处理，假日模式下时间颗粒更细
        $timeSepareType = $params[4] ?? (int)(new \Business\PftSystem\VacationModeBiz())->isOpenVacation();

        $orderReportBusiness = new CreateOrderPayReport($this->_logPath);
        $orderReportBusiness->runTask($date, $timeSepareType);

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 年卡报表入口
     * <AUTHOR>
     * @date   2025/04/01
     *
     * @命令：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runAnnualCardTask -[参数3：日期Y-m-d]- -[参数4：执行类型]-
     * @例子：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runAnnualCardTask 2025-04-01 annual_card_order
     */
    public function runAnnualCardTask()
    {
        $params = $GLOBALS['argv'];
        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }
            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        if (!isset($params[4])) {
            //年卡预订、激活报表
            $orderReportBusiness = new AnnualCardOrderReport($this->_logPath);
            $orderReportBusiness->runTask($date);
            sleep(10);
            //年卡验证报表
            $orderReportBusiness = new AnnualCardCheckReport($this->_logPath);
            $orderReportBusiness->runTask($date);
        } else {
            //需要执行的报表
            $type = $params['4'];
            if (!empty($type)) {
                switch ($type) {
                    case 'annual_card_order':
                        $orderReportBusiness = new AnnualCardOrderReport($this->_logPath);
                        $orderReportBusiness->runTask($date);
                        break;
                    case 'annual_card_check':
                        $orderReportBusiness = new AnnualCardCheckReport($this->_logPath);
                        $orderReportBusiness->runTask($date);
                        break;
                    default:
                        echo "无效执行类型!";
                        exit;
                }
            }
        }

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 台账报表入口
     * <AUTHOR>
     * @date   2025/04/01
     *
     * @命令：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runStandingBookTask -[参数3：日期Y-m-d]- -[参数4：执行类型]-
     * @例子：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runStandingBookTask 2025-04-01 order_v2_sta
     */
    public function runStandingBookTask()
    {
        $params = $GLOBALS['argv'];
        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }
            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }
        if (!isset($params[4])) {
            //台账预订
            $standingBookOrderBusiness = new CreateStandingBookOrder($this->_logPath);
            $standingBookOrderBusiness->runTask($date);
            sleep(10);
            //台账验证
            $standingBookCheckBusiness = new CreateStandingBookChecked($this->_logPath);
            $standingBookCheckBusiness->runTask($date);
        } else {
            //需要执行的报表
            $type = $params['4'];
            if (!empty($type)) {
                switch ($type) {
                    case 'order_v2_sta':
                        $standingBookOrderBusiness = new CreateStandingBookOrder($this->_logPath);
                        $standingBookOrderBusiness->runTask($date);
                        break;
                    case 'checked_v2_sta':
                        $standingBookCheckBusiness = new CreateStandingBookChecked($this->_logPath);
                        $standingBookCheckBusiness->runTask($date);
                        break;
                    default:
                        echo "无效执行类型!";
                        exit;
                }
            }
        }

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 检票报表入口
     * <AUTHOR>
     * @date   2025/04/01
     *
     * @命令：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTerminalPassTask -[参数3：日期Y-m-d]-
     * @例子：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTerminalPassTask 2025-04-01
     */
    public function runTerminalPassTask()
    {
        $params = $GLOBALS['argv'];
        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }
            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $orderReportBusiness = new TerminalPassReport($this->_logPath);
        $orderReportBusiness->runTask($date);

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 分终端报表入口
     * <AUTHOR>
     * @date   2025/04/01
     *
     * @命令：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTerminalBranchTaskV2 -[参数3：日期Y-m-d]-
     * @例子：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTerminalBranchTaskV2 2025-04-01
     */
    public function runTerminalBranchTaskV2()
    {
        $params = $GLOBALS['argv'];
        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }
            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $terminalBranchBusiness = new CreateTerminalBranchReport($this->_logPath);
        $terminalBranchBusiness->runTask($date);

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 月报表任务
     * <AUTHOR>
     */
    public function runMonthTaskV2()
    {
        $params = $GLOBALS['argv'];

        if (isset($params[4])) {
            $day = $params[4];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Ym', $tmp);

        } else {
            //统计前一月的数据
            $date = date('Ym', strtotime('-1 month'));
        }

        $taskTypeStr = isset($params[3]) ? $params[3] : '';
        $version = isset($params[5]) ? $params[5] : 2;

        if (!empty($taskTypeStr)) {

            switch ($taskTypeStr) {
                //预定月报表
                case 'order_v2_month':
                    $orderMonthBusiness = new \Business\Statistics\CreateOrderMonthReport($this->_logPath);
                    $orderMonthBusiness->runTask($date, $version);
                    break;

                //验证月报表
                case 'checked_v2_month':
                    $checkMonthBusiness = new \Business\Statistics\CreateCheckMonthReport($this->_logPath);
                    $checkMonthBusiness->runTask($date);
                    break;
                //资源预定月报表
                case 'resource_order_month':
                    $resourceOrderMonth = new CreateResourceOrderMonthReport($this->_logPath);
                    $resourceOrderMonth->runTask($date);
                    break;

                //定制报表
                case 'customize_month':
                    $customizeMonth = new CreateCustomizeMonthReport($this->_logPath);
                    $customizeMonth->runTask($date);
                    break;

                //演出预订月报表
                case 'show_order_month':
                    $showOrderMonth = new CreateSeriesOrderMonthReport($this->_logPath);
                    $showOrderMonth->runTask($date);
                    break;

                //演出验证月报表
                case 'show_check_month':
                    $showCheckMonth = new CreateSeriesCheckMonthReport($this->_logPath);
                    $showCheckMonth->runTask($date);
                    break;

                //瘦西湖分账子票月报表
                case 'custom_sar_sub_pack_month':
                    $checkMonthBusiness = new \Business\Statistics\CreateCustomSubPackMonthReport($this->_logPath);
                    $checkMonthBusiness->runTask($date);
                    break;
            }
        } else {

            $orderMonthBusiness = new \Business\Statistics\CreateOrderMonthReport($this->_logPath);
            $orderMonthBusiness->runTask($date, $version);

            sleep(10);

            $checkMonthBusiness = new \Business\Statistics\CreateCheckMonthReport($this->_logPath);
            $checkMonthBusiness->runTask($date);

            sleep(10);

            $resourceOrderMonth = new CreateResourceOrderMonthReport($this->_logPath);
            $resourceOrderMonth->runTask($date);

            sleep(10);

            $customizeMonth = new CreateCustomizeMonthReport($this->_logPath);
            $customizeMonth->runTask($date);

            sleep(10);

            //演出预订月报表
            $showOrderMonth = new CreateSeriesOrderMonthReport($this->_logPath);
            $showOrderMonth->runTask($date);

            sleep(10);

            //演出验证月报表
            $showCheckMonth = new CreateSeriesCheckMonthReport($this->_logPath);
            $showCheckMonth->runTask($date);

            //瘦西湖分账子票月报表
            sleep(10);
            $checkMonthBusiness = new \Business\Statistics\CreateCustomSubPackMonthReport($this->_logPath);
            $checkMonthBusiness->runTask($date);

        }

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 预定月报表直接跑一年
     * <AUTHOR>
     * @date 2018-01-09
     * @return bool
     */
    public function runMonthReportOneYear()
    {
        $params = $GLOBALS['argv'];

        $nowMonth = date('m');
        $year     = date('Y');

        $calMonth       = isset($params[4]) ? $params[4] : $nowMonth;
        $calYear        = isset($params[3]) ? $params[3] : $year;
        $calMonth       = ($calMonth >= 1 && $calMonth <= 12) ? $calMonth : $nowMonth;
        $statisticModel = new \Model\Report\StatisticsV2();

        for ($month = 1; $month <= $calMonth; $month++) {

            $tmpTime = strtotime($calYear . '-' . $month);
            $date    = date('Y-m', $tmpTime);

            $res = $statisticModel->runAllMonthTask($date);

            if (!$res) {
                pft_log('month_year', $date . ': 月数据失败');
            } else {
                pft_log('month_year', $date . ': 月数据成功');
            }
        }

        return true;
    }

    /**
     * 运行统计任务 - 处理待处理日期表中日期，重新统计
     * <AUTHOR>
     * @date   2017-02-14
     *
     * @return
     */
    public function runPendingDateTask()
    {
        $pendingDateRes = $this->_model->getPendingDate();
        if (!$pendingDateRes) {
            exit("没有需要处理的日期\r\n");
        }

        $pendingDateList = array();
        foreach ($pendingDateRes as $info) {
            $tempDate   = $info['date'];
            $tempAction = $info['action'];
            //把操作按日期整合
            if (isset($pendingDateList[$tempDate])) {
                //去除重复操作
                if (!in_array($tempAction, $pendingDateList[$tempDate])) {
                    $pendingDateList[$tempDate][] = $tempAction;
                }
            } else {
                $pendingDateList[$tempDate]   = array();
                $pendingDateList[$tempDate][] = $tempAction;
            }
        }

        //获取配置的操作列表
        $actionList = $this->_model->getPendingActionList();

        //按日期与操作执行汇总任务
        foreach ($pendingDateList as $date => $actionArr) {

            foreach ($actionArr as $action) {
                //执行汇总任务
                $res = $this->_model->runSpecTask($date, $action);
                if ($res) {
                    echo "{$date} - {$actionList[$action]}数据统计成功\r\n";
                    $logData = json_encode(['day' => $date, 'action' => $actionList[$action], 'msg' => 'succ'],
                        JSON_UNESCAPED_UNICODE);
                    pft_log($this->_logPath, $logData);
                    //更新已完成此日对伊统计操作
                    $this->_model->completePendingDate($date, $action);

                } else {
                    echo "{$date} - {$actionList[$action]}数据统计失败\r\n";
                    $logData = json_encode(['day' => $date, 'action' => $actionList[$action], 'msg' => 'error'],
                        JSON_UNESCAPED_UNICODE);
                    pft_log($this->_logPath, $logData);

                }
            }
        }
        exit("处理结束\r\n");
    }

    //设置终端今日汇总数据 （目前没有在定时任务中）
    public function runTerminalTask()
    {
        $params = $GLOBALS['argv'];

        $nowMonth = date('m');
        $year     = date('Y');
        $day      = date('d');

        echo "开始跑终端今日汇总数据\r\n";

        $calMonth = isset($params[3]) ? $params[3] : $nowMonth;
        $calMonth = ($calMonth >= 1 && $calMonth <= 12) ? $calMonth : $nowMonth;
        $calMonth = $calMonth > $nowMonth ? $nowMonth : $calMonth;

        $beginDay = isset($params[4]) ? $params[4] : 1;
        $endDay   = isset($params[5]) ? $params[5] : 31;

        //获取具体哪天
        $tmpTimestamp = strtotime($year . '-' . $calMonth . '-01');
        $maxDays      = date('t', $tmpTimestamp);
        $endDay       = $endDay > $maxDays ? $maxDays : $endDay;

        if ($calMonth == $nowMonth) {
            $endDay = $endDay >= $day ? ($day - 1) : $endDay;
        }

        //这边做下限制，如果是2017-04-20号以后的数据程序有实时汇总，不需要异步去重新统计
        $endDate      = strtotime($year . '-' . $calMonth . '-' . $endDay);
        $startCalDate = strtotime('2017-04-20');

        if ($endDate >= $startCalDate) {
            //exit("2017-04-20号以后的数据程序有实时汇总，不需要异步去重新统计\r\n");
        }

        //模型
        $statisticModel = new \Model\Report\StatisticsV2();

        //按天去统计
        for ($i = $beginDay; $i <= $endDay; $i++) {
            $tmp  = strtotime($year . '-' . $calMonth . '-' . $i);
            $date = date('Y-m-d', $tmp);

            $res = $statisticModel->runTerminalCheckTask($date);
            if ($res) {
                echo "{$date} - 终端数据统计成功\r\n";
                $logData = json_encode(['type' => 'terminal', 'day' => $date, 'msg' => 'succ']);
                pft_log($this->_logPath, $logData);
            } else {
                echo "{$date} - 终端数据统计失败\r\n";
                $logData = json_encode(['type' => 'terminal', 'day' => $date, 'msg' => 'error']);
                pft_log($this->_logPath, $logData);
            }

            sleep(1);
        }

        //结束
        exit("结束跑终端今日汇总数据\r\n");
    }

    //终端购票汇总
    public function runTerminalBuyTask()
    {
        $params = $GLOBALS['argv'];

        if (isset($params[3])) {
            $day  = $params[3];
            $tmp  = strtotime($day);
            $date = $tmp == false ? date('Y-m-d') : date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        //模型
        $statisticModel = new \Model\Report\StatisticsV2();

        //按天去统计
//        $date = '2018-07-17';
        $res = $statisticModel->dayTerminalBuyTask($date);
        if ($res) {
            echo "{$date} - 终端购票数据统计成功\r\n";
            $logData = json_encode(['type' => 'terminal_buy', 'day' => $date, 'msg' => 'succ']);
            pft_log($this->_logPath, $logData);
        } else {
            echo "{$date} - 终端购票数据统计失败\r\n";
            $logData = json_encode(['type' => 'terminal_buy', 'day' => $date, 'msg' => 'error']);
            pft_log($this->_logPath, $logData);
        }

        //结束
        exit("结束终端购票汇总\r\n");
    }


    //终端购票汇总
    #php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTerminalBuyTaskV2 2025-04-04 terminal_buy 37302517
    public function runTerminalBuyTaskV2()
    {
        $params = $GLOBALS['argv'];
        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $type = isset($params[4]) ? $params[4] : '';
        $fid  = isset($params[5]) ? $params[5] : 0;
        if (isset($params[6])) {
            $timeSepareType = $params[6];
        } else {
            $timeSepareType = (int) (new \Business\PftSystem\VacationModeBiz())->isOpenVacation();
        }
        if (!$type) {
            $orderReportBusiness = new CreateTerminalBuyReport($this->_logPath);
            $orderReportBusiness->runTask($date, $fid, $timeSepareType);
        } else {
            switch ($type) {
                //新版预定(按小时汇总)
                case 'terminal_buy':
                    $orderReportBusiness = new CreateTerminalBuyReport($this->_logPath);
                    $orderReportBusiness->runTask($date, $fid, $timeSepareType);
                    break;

            }
        }

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 分终端检票汇总任务 以及 多景点入园汇总
     * Create by zhangyangzhen
     * Date: 2019/4/30
     * Time: 16:14
     * @throws \Exception
     */
    public function runTerminalBranchTask()
    {
        //$params = $GLOBALS['argv'];
        //
        //if (isset($params[3])) {
        //    $day  = $params[3];
        //    $tmp  = strtotime($day);
        //    $date = $tmp == false ? date('Y-m-d') : date('Y-m-d', $tmp);
        //} else {
        //    //统计前一天的数据
        //    $date = date('Y-m-d', strtotime('-1 days'));
        //}
        //
        //pft_log($this->_logPath, '分终端检票汇总|' . $date . '|' . json_encode($params));
        //
        ////模型
        //$statisticModel = new StatisticsV2();
        //$res = $statisticModel->dayTerminalBranchSummaryTask($date);
        //
        //if ($res) {
        //    echo "{$date} - 分终端检票汇总数据统计成功\r\n";
        //    $logData = json_encode(['type' => 'terminal_branch', 'day' => $date, 'msg' => 'succ']);
        //    pft_log($this->_logPath, $logData);
        //} else {
        //    echo "{$date} - 分终端检票汇总数据统计失败\r\n";
        //    $logData = json_encode(['type' => 'terminal_branch', 'day' => $date, 'msg' => 'error']);
        //    pft_log($this->_logPath, $logData);
        //}
        //
        //$result = $statisticModel->dayScenicSummaryTask($date);
        //if ($result) {
        //    echo "{$date} - 多景点入园汇总数据统计成功\r\n";
        //    $logData = json_encode(['type' => 'scenic_checked', 'day' => $date, 'msg' => 'succ']);
        //    pft_log($this->_logPath, $logData);
        //} else {
        //    echo "{$date} - 多景点入园汇总数据统计失败\r\n";
        //    $logData = json_encode(['type' => 'scenic_checked', 'day' => $date, 'msg' => 'error']);
        //    pft_log($this->_logPath, $logData);
        //}

        //结束
        exit("该任务已废弃\r\n");
    }

    /**
     * 交易报表汇总
     * 命令: /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTradeReport [-3- date 日期 2021-11-12] [-4- fid 用户id] [-5- 账本类型 逗号隔开]
     * 例子：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTradeReport 2021-11-12 0 2601,1101
     * Create by xiexy
     * Date: 2019/7/19
     * Time: 16:14
     * @throws \Exception
     */
    public function runTradeReport()
    {
        pft_log(TradeReport::ERROR_LOG, '交易报表：start');

        $params = $GLOBALS['argv'];

        //汇总日期
        if (isset($params[3])) {
            $day  = $params[3];
            $tmp  = strtotime($day);
            $date = $tmp == false ? date('Ymd', strtotime("-1 days")) : date('Ymd', $tmp);
        } else {

            $date = date('Ymd', strtotime("-1 days"));
        }

        //指定用户
        $fid = $params[4] ?? '';

        //指定账本类型 逗号隔开
        $booksCodeStr = $params[5] ?? 0;
        $booksCode = empty($booksCodeStr) ? [] : explode(',', $booksCodeStr);

        //交易报表
        $tradeReport = new TradeReport($this->_logPath);
        $tradeReport->run($date, $fid, $booksCode);
        pft_log(TradeReport::ERROR_LOG, $date . '交易报表：end');
    }

    /**
     * 交易报表月汇总
     * 命令: /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTradeReportMonth [-3- date 日期 202111] [-4- fid 用户id] [-5- 账本类型 逗号隔开]
     * 例子：/usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTradeReportMonth 202111 0 2601,1101
     * Create by xiexy
     * Date: 2019/7/19
     * Time: 16:14
     * @throws \Exception
     */
    public function runTradeReportMonth()
    {
        pft_log(TradeReportMonth::ERROR_LOG, '交易报表：start');

        $params = $GLOBALS['argv'];
        if (isset($params[3])) {
            $month = $params[3];
        } else {
            $month = date('Ym', strtotime('-1 month'));
        }

        //指定用户
        $fid = $params[4] ?? '';

        //指定账本类型 逗号隔开
        $booksCodeStr = $params[5] ?? 0;
        $booksCode = empty($booksCodeStr) ? [] : explode(',', $booksCodeStr);

        //交易报表
        $tradeReport = new TradeReportMonth($this->_logPath);
        $tradeReport->run($month, $fid, $booksCode);
        pft_log(TradeReportMonth::ERROR_LOG, '交易报表：end');
    }

    /**
     * 终端汇总
     * 命令：php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTerminalTaskV2 [-3-日期：2021-11-23] [-4-汇总类型：terminal_checked]
     * 例子：php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTerminalTaskV2 2021-11-23 terminal_checked
     * <AUTHOR>
     * @date 2021/11/23
     *
     */
    public function runTerminalTaskV2()
    {
        $params = $GLOBALS['argv'];

        //日期参数
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);

        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        //汇总类型
        if (!isset($params[4])) {
            //终端验证汇总
            $terminalCheckedBusiness = new CreateTerminalCheckedReport($this->_logPath);
            $terminalCheckedBusiness->runTask($date);
        } else {
            $listArr = explode(',', $params['4']);
            if (!empty($listArr)) {
                foreach ($listArr as $item) {
                    switch ($item) {
                        //终端验证汇总
                        case 'terminal_checked':
                            $terminalCheckedBusiness = new CreateTerminalCheckedReport($this->_logPath);
                            $terminalCheckedBusiness->runTask($date);
                            break;
                    }
                }
            }
        }

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }


    /**
     * 分钟销售报表（今日五分钟间隔运行脚本） - 定制
     * php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics minuteReportToDayTask 202310161710 3385
     */
    public function minuteReportToDayTask()
    {
        $params = $GLOBALS['argv'];

        $isReRun = false;

        if (isset($params[3]) || isset($params[4])) {
            $isReRun = true;
        }

        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);
            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $minute = floor(date("i", $tmp) / 5) * 5;
            $minute < 10 && $minute = "0{$minute}";
            $date = date("Y-m-d H:{$minute}", $tmp);
        } else {
            //统计近五分钟的数据
            $runTime = time() - 300;
            $minute  = floor(date("i", $runTime) / 5) * 5;
            $minute < 10 && $minute = "0{$minute}";
            $date = date("Y-m-d H:{$minute}", $runTime);
        }

        $fid = isset($params[4]) ? $params[4] : 0;

        $logPath = 'statistic_today_report';

        $orderReportBusiness = new CreateMinuteReportToday($logPath);
        $orderReportBusiness->runTask($date, $fid, $isReRun);

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 分钟销售报表（今日五分钟间隔运行脚本） - 定制
     * php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics minuteReportTask 2023-10-16 minute_order_v2 3385
     */
    public function minuteReportTask()
    {
        $params = $GLOBALS['argv'];

        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $fid = isset($params[5]) ? $params[5] : 0;

        if (!isset($params[4])) {
            $orderReportBusiness = new CreateOrderV2MinuteReport($this->_logPath);
            $orderReportBusiness->runTask($date, $fid);

            $orderReportBusiness = new CreateCheckV2MinuteReport($this->_logPath);
            $orderReportBusiness->runTask($date, $fid);
        }

        //需要执行的报表
        if (!empty($params[4])) {
            switch ($params[4]) {

                //分钟预定
                case 'minute_order_v2':
                    $orderReportBusiness = new CreateOrderV2MinuteReport($this->_logPath);
                    $orderReportBusiness->runTask($date, $fid);
                    break;

                //分钟验证
                case 'minute_checked_v2':
                    $orderReportBusiness = new CreateCheckV2MinuteReport($this->_logPath);
                    $orderReportBusiness->runTask($date, $fid);
                    break;
            }

        }

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 更新票和用户关系 - 定制
     * php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics reportSidTidRelationTask 3385
     */
    public function reportSidTidRelationTask()
    {
        $params = $GLOBALS['argv'];

        $fid = isset($params[3]) ? $params[3] : 0;

        $logPath = 'statistic_relation_report';

        $orderReportBusiness = new CreateMinuteReportToday($logPath);
        $orderReportBusiness->runTaskRelation($fid);

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 预约日报表定时任务
     * 日报表    php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTaskReserveDaily 2021-04-04 0
     * 3是执行日期  4指定商户
     */
    public function runTaskReserveDaily()
    {
        $params = $GLOBALS['argv'];

        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $fid = isset($params[4]) ? $params[4] : 0;

        if (isset($params[5])) {
            $timeSepareType = $params[5];
        } else {
            $timeSepareType = (int) (new \Business\PftSystem\VacationModeBiz())->isOpenVacation();
        }

        $orderReportBusiness = new \Business\Statistics\CreateReserveDailyReport($this->_logPath);
        $orderReportBusiness->runTask($date, $fid, $timeSepareType);

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }


    /**
     * 预约日报表定时任务
     * 日报表    php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTaskReserveMonth 2023-11-01
     * 3是执行日期  4指定商户
     */
    public function runTaskReserveMonth()
    {
        $params = $GLOBALS['argv'];

        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Ym', $tmp);

        } else {
            //统计前一月的数据
            $date = date('Ym', strtotime('-1 days'));
        }


        $orderReportBusiness = new \Business\Statistics\CreateReserveMonthReport($this->_logPath);
        $orderReportBusiness->runTask($date);

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 演出预约日报表定时任务
     * 日报表    php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTaskShowReserveDaily 2024-01-01 0
     * 3是执行日期  4指定商户
     */
    public function runTaskShowReserveDaily()
    {
        $params = $GLOBALS['argv'];

        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }

        $fid = isset($params[4]) ? $params[4] : 0;

        $orderReportBusiness = new \Business\Statistics\CreateShowReserveDailyReport($this->_logPath);
        $orderReportBusiness->runTask($date, $fid);

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 演出预约日报表定时任务
     * 日报表    php /var/www/html/Service/Crontab/runNew.php Report/ReportStatistics runTaskShowReserveMonth 2024-01-01
     * 3是执行日期  4指定商户
     */
    public function runTaskShowReserveMonth()
    {
        $params = $GLOBALS['argv'];

        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Ym', $tmp);

        } else {
            //统计前一月的数据
            $date = date('Ym', strtotime('-1 days'));
        }


        $orderReportBusiness = new \Business\Statistics\CreateShowReserveMonthReport($this->_logPath);
        $orderReportBusiness->runTask($date);

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }
}