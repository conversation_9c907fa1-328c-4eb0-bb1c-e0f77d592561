<?php
/**
 * 汇总电子发票需要的数据
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2017/5/31
 * Time: 17:27
 */

namespace CrontabTasks\Report;

use Library\Controller;
use Model\Member\Invoice;
use Model\TradeRecord\PftTransReport;

class InvoiceTrans extends Controller
{
    private $_day;
    private $_modelReport;
    private $_modelInvoice;
    private $_logPath;
    public function __construct()
    {
        $this->_day          = date('d');
        $this->_logPath      = 'invoiceRun';
        $this->_modelReport  = new PftTransReport();
        $this->_modelInvoice = new Invoice();
    }

    /**
     * 自动脚本具体方法入口
     *
     * @date   2017-05-31
     * <AUTHOR>
     *
     * @return mixed
     */
    public function run()
    {

        //$this->_transDay();//日汇总的数据-模块费和套餐费 专项短信费预存 专项凭证费预存   迁移到java实时生成
        //$this->_transMonth();
        if ($this->_day == '01') {
            sleep(1);
            $this->_transMonth();
            //$this->_transMonthBalance();
        }
    }

    /**
     * 错误数据手动执行修复方法
     * php72 run.php cli_InvoiceTrans reRunMonth 190401 190430 2019-05-01
     * /usr/bin/php run.php cli_InvoiceTrans reRunMonth 190417 190417
     *
     */
    public function reRunMonth()
    {
        $params = $GLOBALS['argv'];

        //时间
        if (!isset($params[3]) || !isset($params[4])) {
            echo "argv 参数错误";exit;
        }

        $beginTime = $params[3]; // 190417
        $endTime   = $params[4]; // 190417

//        if (isset($params[5])) {
//            // 先删除需要重新跑的数据
//            $delTime = $params[5];
//            $delRes  = $this->_modelInvoice->deleteData($delTime);
//            pft_log($this->_logPath, '#删除日期记录:' . $delTime . "#数量:" . $delRes);
//        }

        pft_log($this->_logPath, '#手动更新日期记录:' . $beginTime . "#:" . $endTime);
        $this->_reTransMonth($beginTime, $endTime);
    }

    /**
     * 错误数据手动执行修复方法
     * php72 run.php cli_InvoiceTrans reRunDay 190401 190430 2019-05-01
     * /usr/bin/php run.php cli_InvoiceTrans reRunDay 190417 190417
     *
     */
    public function reRunDay()
    {
        $params = $GLOBALS['argv'];

        //时间
        if (!isset($params[3]) || !isset($params[4])) {
            echo "argv 参数错误";exit;
        }

        $beginTime = $params[3]; // 190417
        $endTime   = $params[4]; // 190417

        pft_log($this->_logPath, '#手动更新日期记录:' . $beginTime . "#:" . $endTime);
        $this->_reTransDay($beginTime, $endTime);
    }

    /**
     * 日汇总的数据-模块费和套餐费
     *
     * @date   2017-05-31
     * <AUTHOR> Lan
     *
     */
    private function _transDay()
    {
        pft_log($this->_logPath, 'begin');
        $type      = [26, 32, 65, 66];   //平台套餐续费  应用模块购买  专项短信费预存 专项凭证费预存
        $beginTime = date('Ymd', mktime(0, 0, 0, date('m'), date('d') - 1, date('Y'))); //昨天0点
        $endTime   = date('Ymd', mktime(0, 0, 0, date('m'), date('d'), date('Y')) - 1); //昨天最后一秒

        $beginTime = intval(substr($beginTime, 2));
        $endTime   = intval(substr($endTime, 2));

        $field  = 'fid, dtype, expense, trans_time';
        $fidArr = $this->_modelReport->getFid($beginTime, $endTime, $type);

        if (!$fidArr) {
            pft_log($this->_logPath, 'no_data');
            return;
        }

        $page     = 1;
        $pageSize = 500;

        while (1) {
            $offSet  = ($page - 1) * $pageSize;
            $fidTemp = array_slice($fidArr, $offSet, $pageSize);
            if ($fidTemp) {
                $page++;
                $res = $this->_modelReport->getDataByType($type, $fidTemp, $beginTime, $endTime, $field, 'fid, dtype');
                if ($res) {
                    $tempArr = [];
                    foreach ($res as $key => $val) {
                        if ($this->_modelInvoice->checkRecord($val['fid'], $val['trans_time'], $val['dtype'], $val['expense'])) {
                            continue;
                        }
                        $tempArr[$key]['fid']        = $val['fid'];
                        $tempArr[$key]['trade_type'] = $val['dtype'];
                        $tempArr[$key]['money']      = $val['expense'];
                        $tempArr[$key]['time']       = time();
                    }
                    $this->_modelInvoice->insertDayData($tempArr);

                    unset($tempArr);
                }
            } else {
                break;
            }
        }
        pft_log($this->_logPath, 'finish');
    }

    /**
     * 月汇总的数据-短信费、提现手续费、电子凭证费
     *
     * @date   2017-05-31
     * <AUTHOR> Lan
     *
     */
    private function _transMonth()
    {
        pft_log($this->_logPath, 'begin', 'month');
        $type = [7, 8, 9, 97];  //平台余额提现 电子凭证费  短信息费

        $beginTime = date('Ym01', mktime(0, 0, 0, date('m') - 1, 1, date('Y'))); //上个月1号0点
        $endTime   = date('Ymd', mktime(0, 0, 0, date('m'), 1, date('Y')) - 1); //上个月最后一秒
        $beginTime = intval(substr($beginTime, 2));
        $endTime   = intval(substr($endTime, 2));

        $fidArr = $this->_modelReport->getFid($beginTime, $endTime, $type);

        if (!$fidArr) {
            pft_log($this->_logPath, 'no_data');
            return;
        }
        pft_log($this->_logPath . '/fid', print_r($fidArr, true));
        $field    = 'fid, dtype, sum(expense) as expense';
        $page     = 1;
        $pageSize = 500;

        while (1) {
            $offSet  = ($page - 1) * $pageSize;
            $fidTemp = array_slice($fidArr, $offSet, $pageSize);
            if ($fidTemp) {
                $page++;
                $res = $this->_modelReport->getDataByType($type, $fidTemp, $beginTime, $endTime, $field, 'fid, dtype');
                pft_log($this->_logPath . '/sql', $this->_modelReport->_sql());

                $income    = $this->_modelReport->getWithdrawBackList($fidTemp, $beginTime, $endTime, 'fid, sum(income) as income', 'fid');
                $incomeArr = [];
                if ($income) {
                    foreach ($income as $val) {
                        $incomeArr[$val['fid']] = $val['income'] ? $val['income'] : 0;
                    }
                }

                if ($res) {
                    $tempArr = [];
                    foreach ($res as $key => $val) {
                        $tempArr[$key]['fid']        = $val['fid'];
                        $tempArr[$key]['trade_type'] = $val['dtype'];
                        if ($val['dtype'] == 9) {
                            $tempArr[$key]['money'] = isset($incomeArr[$val['fid']]) ? $val['expense'] - $incomeArr[$val['fid']] : $val['expense'];
                        } else {
                            $tempArr[$key]['money'] = $val['expense'];
                        }

                        $tempArr[$key]['time'] = time();
                    }

                    pft_log($this->_logPath . '/invoice', print_r($tempArr, true));
                    $this->_modelInvoice->insertDayData($tempArr);
                    unset($tempArr);
                }
            } else {
                break;
            }
        }
        pft_log($this->_logPath, 'finish', 'month');
    }

    /**
     * 日汇总的数据-模块费和套餐费
     *
     * @date   2017-05-31
     * <AUTHOR> Lan
     *
     */
    private function _reTransDay($beginTime, $endTime)
    {
        pft_log($this->_logPath, 'begin');
        $type      = [26, 32, 65, 66]; //平台套餐续费  应用模块购买  专项短信费预存 专项凭证费预存

        $field  = 'fid, dtype, expense, trans_time';
        $fidArr = $this->_modelReport->getFid($beginTime, $endTime, $type);

        if (!$fidArr) {
            pft_log($this->_logPath, 'no_data');
            return;
        }

        $page     = 1;
        $pageSize = 500;

        while (1) {
            $offSet  = ($page - 1) * $pageSize;
            $fidTemp = array_slice($fidArr, $offSet, $pageSize);
            if ($fidTemp) {
                $page++;
                $res = $this->_modelReport->getDataByType($type, $fidTemp, $beginTime, $endTime, $field, 'fid, dtype');
                if ($res) {
                    $tempArr = [];
                    foreach ($res as $key => $val) {
                        if ($this->_modelInvoice->checkRecord($val['fid'], $val['trans_time'], $val['dtype'], $val['expense'])) {
                            continue;
                        }
                        $tempArr[$key]['fid']        = $val['fid'];
                        $tempArr[$key]['trade_type'] = $val['dtype'];
                        $tempArr[$key]['money']      = $val['expense'];
                        $tempArr[$key]['time']       = time();
                    }
                    $this->_modelInvoice->insertDayData($tempArr);

                    unset($tempArr);
                }
            } else {
                break;
            }
        }
        pft_log($this->_logPath, 'finish');
    }

    /**
     * 月汇总的数据-短信费、提现手续费、电子凭证费
     *
     * @date   2017-05-31
     * <AUTHOR> Lan
     *
     */
    private function _reTransMonth($beginTime, $endTime)
    {
        pft_log($this->_logPath, 'begin', 'month');
        $type = [7, 8, 9, 97];  //平台余额提现  电子凭证费扣除 短信息费扣除

        $fidArr = $this->_modelReport->getFid($beginTime, $endTime, $type);

        if (!$fidArr) {
            pft_log($this->_logPath, 'no_data');
            return;
        }
        pft_log($this->_logPath . '/fid', print_r($fidArr, true));
        $field    = 'fid, dtype, sum(expense) as expense';
        $page     = 1;
        $pageSize = 500;

        while (1) {
            $offSet  = ($page - 1) * $pageSize;
            $fidTemp = array_slice($fidArr, $offSet, $pageSize);
            if ($fidTemp) {
                $page++;
                $res = $this->_modelReport->getDataByType($type, $fidTemp, $beginTime, $endTime, $field, 'fid, dtype');
                pft_log($this->_logPath . '/sql', $this->_modelReport->_sql());

                $income    = $this->_modelReport->getWithdrawBackList($fidTemp, $beginTime, $endTime, 'fid, sum(income) as income', 'fid');
                $incomeArr = [];
                if ($income) {
                    foreach ($income as $val) {
                        $incomeArr[$val['fid']] = $val['income'] ? $val['income'] : 0;
                    }
                }

                if ($res) {
                    $tempArr = [];
                    foreach ($res as $key => $val) {
                        $tempArr[$key]['fid']        = $val['fid'];
                        $tempArr[$key]['trade_type'] = $val['dtype'];
                        if ($val['dtype'] == 9) {
                            $tempArr[$key]['money'] = isset($incomeArr[$val['fid']]) ? $val['expense'] - $incomeArr[$val['fid']] : $val['expense'];
                        } else {
                            $tempArr[$key]['money'] = $val['expense'];
                        }

                        $tempArr[$key]['time'] = time();
                    }

                    pft_log($this->_logPath . '/invoice', print_r($tempArr, true));
                    $this->_modelInvoice->insertDayData($tempArr);
                    unset($tempArr);
                }
            } else {
                break;
            }
        }
        pft_log($this->_logPath, 'finish', 'month');
    }

    /**
     * 月汇总的数据-短信费、提现手续费、电子凭证费-专项款的
     *
     * @date   2018-04-25
     * <AUTHOR> Lan
     *
     */
    private function _transMonthBalance()
    {
        pft_log($this->_logPath, 'begin', 'month');
        $type = [7, 8]; //平台余额提现  电子凭证费扣除

        $beginTime = date('Ym01', mktime(0, 0, 0, date('m') - 1, 1, date('Y'))); //上个月1号0点
        $endTime   = date('Ymd', mktime(0, 0, 0, date('m'), 1, date('Y')) - 1); //上个月最后一秒
        //$beginTime = intval(substr($beginTime, 2));
        //$endTime   = intval(substr($endTime, 2));

        $fidArr = $this->_modelReport->getFid($beginTime, $endTime, $type);

        if (!$fidArr) {
            pft_log($this->_logPath, 'no_data');
            return;
        }

        pft_log($this->_logPath . '/fid', print_r($fidArr, true));

        $field = 'fid, dtype, sum(dmoney) as money';

        $page     = 1;
        $pageSize = 500;

        while (1) {
            $offSet  = ($page - 1) * $pageSize;
            $fidTemp = array_slice($fidArr, $offSet, $pageSize);
            if ($fidTemp) {
                $page++;
                $res = $this->_modelReport->getBalanceDataByType($type, $fidTemp, $beginTime, $endTime, $field, 'fid, dtype');

                if ($res) {
                    $tempArr = [];
                    foreach ($res as $key => $val) {
                        $tempArr[$key]['fid']        = $val['fid'];
                        $tempArr[$key]['trade_type'] = $val['dtype'];

                        $tempArr[$key]['money'] = $val['money'];

                        $tempArr[$key]['time'] = time();
                    }

                    $this->_modelInvoice->insertDayData($tempArr);
                    unset($tempArr);
                }
            } else {
                break;
            }
        }
        pft_log($this->_logPath, 'finish', 'month');
    }
}
