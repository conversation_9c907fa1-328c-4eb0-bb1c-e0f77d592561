<?php
/**
 * 报表推送预热
 * <AUTHOR>
 * @date   2024/05/10
 */

namespace CrontabTasks\Report;

use Business\Statistics\PushPreheat\CleanScript;
use Business\Statistics\PushPreheat\RunScript;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class ReportPushPreheat
{
    public function __construct()
    {
        //运行时间不做限制
        set_time_limit(0);
    }

    /**
     * 报表推送预热任务入口
     * <AUTHOR>
     * @date   2024/05/10
     *
     * 命令：sudo /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportPushPreheat run
     *
     */
    public function run()
    {
        try {

            $startTime = microtime(true); //开始时间

            $result = (new RunScript())->run();

            //计算执行时间
            $runTimes = round((microtime(true) - $startTime), 3);

            echo json_encode([$result, "{$runTimes}s"], JSON_UNESCAPED_UNICODE) . "\n";

            echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/push_preheat/run_script/下是否有错误日志' . "\r\n";
        } catch (\Exception $e) {
            echo json_encode([
                'msg'  => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ], JSON_UNESCAPED_UNICODE);

            echo "\n";

            exit(255);
        }
    }

    /**
     * 报表推送预热任务定期清理入口
     * <AUTHOR>
     * @date   2024/05/10
     *
     *  命令：sudo /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/ReportPushPreheat clean
     *
     */
    public function clean()
    {
        try {
            $result = (new CleanScript())->run();
            echo json_encode([$result], JSON_UNESCAPED_UNICODE) . "\n";
            echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/push_preheat/clean_script/下是否有错误日志' . "\r\n";
        } catch (\Exception $e) {
            echo json_encode([
                'msg'  => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ], JSON_UNESCAPED_UNICODE);

            echo "\n";

            exit(255);
        }
    }
}