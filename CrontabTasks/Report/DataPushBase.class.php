<?php

/**
 * 数据推送基类
 *
 * Class DataPushBase
 * @package CrontabTasks\Report
 */

namespace CrontabTasks\Report;

use Library\Controller;
use Model\Ota\GovernmentDataSys;
use Library\JsonRpc\PftRpcClient;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class DataPushBase extends Controller
{

    /**
     * 获取获取景区配置列表
     * User: Liucm
     * Date: 2020/12/31
     * Time: 17:07
     */
    protected function _getLandConfList($type = 0, $field = '*', $logPath = 'government/error')
    {
        if (empty($type)) {
            return [];
        }
        $govModel = new GovernmentDataSys();
        $govList  = $govModel->getGdListByType($type, $field);
        if (empty($govList)) {
            $errorData = [
                'key' => '获取对应配置列表为空',
                'sql' => $govModel->getLastSql(),
            ];
            pft_log($logPath, json_encode($errorData));
            return [];
        }

        $gdsIdArr = [];
        foreach ($govList as $item) {
            $gdsIdArr[] = $item['id'];
        }

        $govAttrList = $govModel->getAttrByGdsIds($gdsIdArr);

        if (empty($govAttrList)) {
            $errorData = [
                'key' => '获取对应的data_attr表数据为空',
                'sql' => $govModel->getLastSql(),
            ];
            pft_log($logPath, json_encode($errorData));
        }

        if (!empty($govAttrList)) {
            //将配置列表和配置列表属性整合成同一个列表
            foreach ($govList as &$list) {
                foreach ($govAttrList as $attrList) {
                    if ($list['id'] == $attrList['gds_id']) {
                        $list[$attrList['attr_key']] = $attrList['attr_val'];
                    }
                }
            }
        }
        return $govList;
    }

    /**
     * 请求云票务jsonRpc接口
     *
     * @param $callService
     * @param $method
     * @param $params
     * @param $module
     *
     * @return array
     * Author : liucm
     * Date : 2021/8/3
     */
    protected function jsonRpcRequest($callService, $method, $params, $module)
    {
        try {
            $lib  = new PftRpcClient($callService);
            $res  = $lib->call($method, $params, $module);
            $data = $res['data'];
            $code = $res['code'];
            $msg  = $res['msg'];
        } catch (\Exception $e) {
            $msg  = $e->getMessage();
            $code = $e->getCode();
            $data = [];
        }
        return ['code' => $code, 'data' => $data, 'msg' => $msg];
    }

}