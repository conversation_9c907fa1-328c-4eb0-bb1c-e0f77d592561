<?php
/**
 * Created by PhpStorm.
 * User: cgp
 * Date: 16/5/16
 * Time: 23:18
 */

namespace CrontabTasks\Report;

if (PHP_SAPI != 'cli') {
    exit('error');
}
defined('PFT_INIT') or exit('Access Denied');

class OrderReport
{
    public function OrderSummaryByLid()
    {
        $date  = date('Ymd', strtotime('-1 days'));
        $model = new \Model\Report\ApplyerReport();
        $model->OrderSummaryByLid($date);
    }
}