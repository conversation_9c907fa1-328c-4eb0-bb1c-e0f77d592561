<?php

/**
 * 江西省政府数据平台对接任务
 *
 * Class JiangXiDataPush
 * @package CrontabTasks\Report
 */

namespace CrontabTasks\Report;

use Model\Ota\GovernmentDataSys;

if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class JiangXiDataPush extends DataPushBase
{
    private $pushUrl  = 'https://wljg.dct.jiangxi.gov.cn/upload-data/tourist/'; //江西省数据平台接口地址
    private $_logPath = 'government/jiangxi/error';

    /**
     * 新版江西省实时数据推送
     *
     * @return bool|void
     * Author : liucm
     * Date : 2021/8/3
     */
    public function pushDataByJxMinute()
    {
        $type = GovernmentDataSys::GOVERNMENT_SYS_TYPE_JX;//江西省数据平台类型

        $field   = 'id,apply_id,land_code,land_key';
        $logPath = $this->_logPath;
        $govList = $this->_getLandConfList($type, $field, $logPath);

        $requestUrl     = $this->pushUrl . 'real-people-number';//累计入园
        $requestExitUrl = $this->pushUrl . 'real-exit-people-number';//累计出园
        $timeNow        = date("Y-m-d H:i:s");//当前时间戳
        $timeHour       = date("Y-m-d") . ' 00:00:00';

        foreach ($govList as $item) {
            $enterTotal = 0;//入园人数统计
            $leaveTotal = 0;//出园人数统计
            //如果以后全部都传 记得过滤这个，这个单独10分钟上传
            /*            if ($item['apply_id'] == '359356') {
                            continue;
                        }*/
            //要上传的景区
            $wihteList = $this->wihteList();
            $sid       = $item['apply_id'];
            if (in_array($sid, $wihteList)) {
                $callService = 'pft_scenic_local_service';
                $method      = 'Statistics/LockAge/getEnterParkInfoByTime';

                //自定义闸机
                $deviceData = [];
                if (isset($item['device_type']) && $item['device_type'] == 1) {
                    $deviceData = explode(',', $item['device_data']);
                }

                $enterParams = [
                    'sid'        => $sid,
                    'beginTime'  => strtotime($timeHour),
                    'endTime'    => strtotime($timeNow),
                    'deviceData' => $deviceData,
                    'roll'       => [0],//入园
                    'groupBy'    => [],
                ];
                $module      = 'service';
                //入园
                $enterRes = $this->jsonRpcRequest($callService, $method, $enterParams, $module);

                if ($enterRes['code'] == 200) {
                    $enterData  = $enterRes['data'];
                    $enterTotal = $enterData['enterData'][0]['pass_num'] ?? 0;
                }

                $levelParams = [
                    'sid'        => $sid,
                    'beginTime'  => strtotime($timeHour),
                    'endTime'    => strtotime($timeNow),
                    'deviceData' => $deviceData,
                    'roll'       => [1],//出园
                    'groupBy'    => [],
                ];
                //出园
                $levelRes = $this->jsonRpcRequest($callService, $method, $levelParams, $module);

                if ($levelRes['code'] == 200) {
                    $levelData  = $levelRes['data'];
                    $leaveTotal = $levelData['enterData'][0]['pass_num'] ?? 0;
                }

                //入园人数统计数据格式封装
                $pushDataEnter = [
                    'scenicCode' => $item['land_code'],
                    'upTime'     => $timeNow,
                    'total'      => $enterTotal,
                ];

                //出园人数统计数据格式封装
                $pushDataLeave = [
                    'scenicCode' => $item['land_code'],
                    'upTime'     => $timeNow,
                    'total'      => $leaveTotal,
                    'secret'      => $item['secret'] ?? '',
                ];

                $pushResEn = $this->postRequest($requestUrl, $pushDataEnter, $item['land_key']);
                $pushResIn = $this->postRequest($requestExitUrl, $pushDataLeave, $item['land_key']);

                $logData = [
                    'logKey'      => 'jx_minute_push_' . $item['apply_id'],
                    'applyId'     => $item['apply_id'],
                    'pushDataEn'  => $pushDataEnter,
                    'pushDataIn'  => $pushDataLeave,
                    'pushResEn'   => $pushResEn,
                    'pushResIn'   => $pushResIn,
                    'enterParams' => [$enterParams, $enterRes],
                    'levelParams' => [$levelParams, $levelRes],
                ];
                pft_log($this->_logPath, json_encode($logData, JSON_UNESCAPED_UNICODE));
            }

        }
        echo 'done';
        return true;
    }

    /**
     * 新版江西省每天数据推送
     *
     * @return bool
     * Author : liucm
     * Date : 2021/8/3
     */
    public function pushDataByJxDay()
    {
        $type = GovernmentDataSys::GOVERNMENT_SYS_TYPE_JX;//江西省数据平台类型

        $field   = 'id,apply_id,land_code,land_key';
        $logPath = 'government/suining/error';
        $govList = $this->_getLandConfList($type, $field, $logPath);

        $requestUrl = $this->pushUrl . 'real-gate-day';//累计入园

        $timeDate  = date("Y-m-d", strtotime("-1 day"));
        $beginTime = date("Ymd", strtotime("-1 day"));
        $endTime   = $beginTime;

        foreach ($govList as $item) {
            $wihteList = $this->wihteList();
            $sid       = $item['apply_id'];
            if (in_array($sid, $wihteList)) {
                $enterTotal  = 0;//入园人数统计
                $callService = 'pft_scenic_local_service';
                $method      = 'Statistics/LockAge/getEnterParkInfoByDay';
                //自定义闸机
                $deviceData = [];
                if (isset($item['device_type']) && $item['device_type'] == 1) {
                    $deviceData = explode(',', $item['device_data']);
                }
                $params = [
                    'sid'        => $sid,
                    'beginTime'  => $beginTime,
                    'endTime'    => $endTime,
                    'deviceData' => $deviceData,
                    'roll'       => [0],//入园
                    'groupBy'    => [],
                ];

                $module = 'service';

                //入园
                $enterRes = $this->jsonRpcRequest($callService, $method, $params, $module);

                if ($enterRes['code'] == 200) {
                    $enterData  = $enterRes['data'];
                    $enterTotal = $enterData['enterData'][0]['pass_num'] ?? 0;
                }

                //入园人数统计数据格式封装
                $pushDataEnter[] = [
                    'scenicCode' => $item['land_code'],
                    'upTime'     => $timeDate,
                    'total'      => $enterTotal,
                    'secret'     => $item['secret'] ?? '',
                ];
                $pushResEn       = $this->postRequest($requestUrl, $pushDataEnter, $item['land_key']);

                $logData       = [
                    'logKey'     => 'jx_day_push_' . $item['apply_id'],
                    'applyId'    => $item['apply_id'],
                    'pushDataEn' => $pushDataEnter,
                    'pushResEn'  => $pushResEn,
                ];
                $pushDataEnter = [];
                pft_log($this->_logPath, json_encode($logData, JSON_UNESCAPED_UNICODE));
            }
        }

        return true;
    }

    /**
     * @param $url
     * @param $data
     * @param $key
     *
     * @return array|bool|mixed
     * Author : liucm
     * Date : 2021/8/5
     */
    private function postRequest($url, $data, $key)
    {
        $pushResEn = curl_post($url, json_encode($data), 80, 25, '/api/curl_post',
            ['appKey:' . $key, 'Content-Type:application/json']);

        return $pushResEn;
    }

    private function wihteList()
    {
        return [
            '11108923', //三百山
        ];
    }
}