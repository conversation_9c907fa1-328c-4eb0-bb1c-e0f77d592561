<?php
/**
 * 每天凌晨推送前一天的预定报表和验证报表数据到服务器（目前仅张壁古堡）
 * Created by PhpStorm.
 * User: zhangyangzhen
 * Date: 2019/2/25
 * Time: 10:04
 */

namespace CrontabTasks\Report;

use Library\Cache\Cache;
use Library\Controller;
use Library\CSVGener;
use Library\Tools\Helpers;
use Model\Report\Statistics;
use Business\Statistics\statistics as BizStatis;
use OSS\OssClient;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class ReportServerPush extends Controller
{
    private $_CSV;
    private $_excelPath;
    private $_excelName;
    private $_file;
    private $_titleArr;
    private $_head;
    private $_orderHeadV2;
    private $_pushDate = null;

    private $_statisModel    = null;
    private $_bizStatisModel = null;

    //新版预订报表统计指标对应的名称
    private $_needOrderTarget = [
        'order_ticket'  => '预订票数',
        'cancel_ticket' => '取消票数',
        'revoke_ticket' => '撤销票数',
    ];
    //新版验证报表统计指标对应的名称
    private $_needCheckTarget = [
        'finish_ticket'    => '完结票数',
        'today_ticket'     => '当日下单验证票数',
        'not_today_ticket' => '非当日下单验证票数',
        'total_ticket'     => '验证总数',
        'all_ticket'       => '总票数',
        'revoke_ticket'    => '撤销票数',
    ];

    public function run()
    {
        global $argv;

        $date = $argv[3];//日期参数

        //实例化CSV操作类
        $this->_CSV = CSVGener::getInstance();

        $statisModel = $this->_getStatisModel();
        $pushConfig  = $statisModel->getReportPushConfig();
        //报表模板数据模型
        $templateModel = new \Model\Report\Template();

        //没有传参就取昨天的日期
        if (empty($date)) {
            $date = date('Y-m-d', strtotime('-1 day'));
        }

        foreach ($pushConfig as $key => $value) {
            $templateIds = explode(',', $value['template_ids']);
            foreach ($templateIds as $tplId) {
                //获取模板信息
                $template = $templateModel->getTemplateIgnoreStatus($tplId, $value['fid']);
                $param    = [
                    'fid'              => $value['fid'],
                    'begin_date'       => $date,
                    'end_date'         => $date,
                    'search_config_id' => $tplId,
                    'template'         => $template,
                ];

                switch ($template['type']) {
                    //预定报表
                    case 1:
                        $params = $this->_handleOrderParams($param);
                        break;
                    //验证报表
                    case 2:
                        $params = $this->_handleCheckParams($param);
                        break;
                    default:
                        break;
                }

                if ($params == false) {
                    continue;
                }

                $params['reportType'] = $template['type'];

                //生成Excel
                $this->_buildExcel($params);

                //上传本地文件到阿里oss
                $res = Helpers::uploadFileAliOss('.csv', $this->_excelName, $this->_file);
                print_r($res);
                //记录上传成功或失败记录到数据库
                if ($res['code'] != 200) {
                    $statisModel->addReportPushLog($value['fid'], $tplId, $res['msg'], $template['type'], 3);
                } else {
                    //阿里云oss上传成功后删除本地文件
                    unlink($this->_file);
                    $statisModel->addReportPushLog($value['fid'], $tplId, $res['data']['src'], $template['type'], 1);
                }
            }
        }
    }

    /**
     * 预定报表参数解析
     * Create by zhangyangzhen
     * Date: 2019/2/26
     * Time: 14:59
     *
     * @param  array  $params
     *
     * @return bool
     */
    private function _handleOrderParams($params)
    {
        $template = $params['template'];
        $fid      = $params['fid'];
        unset($params['template']);
        unset($params['fid']);

        $this->_pushDate = $params['begin_date'];

        // 获取模板
        $statisticsBusiness = $this->_getBizStatisModel();

        //统计纬度
        $target = json_decode($template['target'], true);
        //可以被统计的纬度 清除掉没用的 整理纬度对应的汉字描述
        $titleArr = [];
        foreach ($target as $key => $value) {
            if (!array_key_exists($value, $this->_needOrderTarget)) {
                unset($target[$key]);
            }

            $titleArr[] = $value;
        }
        $this->_titleArr = $titleArr;

        // 判断模板是否有设置默认参数，同时无搜索条件
        $itemValue = json_decode($template['item_value'], true);
        if (!empty($itemValue)) {
            list($params['reseller_id'], $params['reseller_group'], $params['pid'], $params['lid'], $params['tid'], $params['operate_id'], $params['channel'], $params['pay_way'], $params['site_id']) = $statisticsBusiness->templateValueMap($itemValue);
        }

        $exHead = [];
        foreach ($titleArr as $value) {
            $exHead[] = $this->_needOrderTarget[$value];
        }

        $this->_orderHeadV2 = load_config('orderHeadV2', 'excel_head');

        $head        = $this->_reportNewHeadMerge($this->_orderHeadV2, $exHead, count($this->_orderHeadV2));
        $this->_head = $head;

        $params = $statisticsBusiness->paramsHandleNew($params, $fid);

        if ($params['code'] === false) {
            return false;
        }

        $params = $params['where'];

        $this->_excelName = "report2server/{$fid}/book_{$this->_pushDate}";
//        EXCEL存放路径
        $this->_excelPath = '/tmp/';

        return $params;
    }

    /**
     * 验证报表参数解析
     * Create by zhangyangzhen
     * Date: 2019/2/26
     * Time: 15:56
     *
     * @param  array  $params
     *
     * @return bool
     */
    private function _handleCheckParams($params)
    {
        $fid      = $params['fid'];
        $template = $params['template'];
        unset($params['fid']);
        unset($params['template']);

        $this->_pushDate = $params['begin_date'];

        // 获取模板
        $statisticsBusiness = $this->_getBizStatisModel();

        //统计纬度
        $target = json_decode($template['target'], true);
        //可以被统计的纬度 清除掉没用的 整理纬度对应的汉字描述
        $titleArr = [];
        foreach ($target as $key => $value) {
            if (!array_key_exists($value, $this->_needCheckTarget)) {
                unset($target[$key]);
            }

            $titleArr[] = $value;
        }
        $this->_titleArr = $titleArr;

        // 判断模板是否有设置默认参数，同时无搜索条件
        $itemValue = json_decode($template['item_value'], true);
        if (!empty($itemValue)) {
            list($params['reseller_id'], $params['reseller_group'], $params['pid'], $params['lid'], $params['tid'], $params['operate_id'], $params['channel'], $params['pay_way'], $params['site_id']) = $statisticsBusiness->templateValueMap($itemValue);
        }

        $exHead = [];
        foreach ($titleArr as $value) {
            $exHead[] = $this->_needCheckTarget[$value];
        }

        $checkHeadV2 = load_config('checkHeadV2', 'excel_head');
        $this->_head = $this->_reportNewHeadMerge($checkHeadV2, $exHead, count($checkHeadV2));

        $params = $statisticsBusiness->paramsHandleNew($params, $fid);
        if ($params['code'] === false) {
            return false;
        }

        $params = $params['where'];

        $this->_excelName = "report2server/{$fid}/check_{$this->_pushDate}";
//        EXCEL存放路径
        $this->_excelPath = '/tmp/';

        return $params;
    }

    /**
     * 生成excel
     * Create by zhangyangzhen
     * Date: 2019/2/26
     * Time: 17:59
     *
     * @param $params
     */
    private function _buildExcel($params)
    {
        //报表类型，1=预定，2=验证
        $reportType = $params['reportType'];
        unset($params['reportType']);

        $statisticsBusiness = $this->_getBizStatisModel();
        $page               = 1;
        $pageSize           = 1000;

        $this->_file = $this->_excelPath . $this->_excelName . '.csv';

        $this->_CSV->CSVFileSet($this->_file);
        $this->_CSV->CSVWrite($this->_head);

        // 获取日期类型
        $dateType = $params['date_type'];
        unset($params['date_type']);

        if ($reportType == 1) {
            $type = ($dateType == 2) ? 3 : 1;
        } elseif ($reportType == 2) {
            $type = ($dateType == 2) ? 4 : 2;
        }

        while (true) {
            if ($reportType == 1) {
                $data = $statisticsBusiness->getOrderInfoV2($params, $this->_titleArr, $page, $pageSize, $type);
            } elseif ($reportType == 2) {
                $data = $statisticsBusiness->getCheckInfoV2($params, $this->_titleArr, $page, $pageSize, $type);
            }

            if (empty($data)) {
                break;
            }

            foreach ($data as $item) {
                $this->_CSV->CSVWrite($item);
            }

            $page++;
        }
    }

    /**
     * 新版报表要求统计灵活，动态拼接excel title
     * <AUTHOR>
     *
     * @param $head
     * @param $ex
     * @param $offset
     *
     * @return mixed
     */
    private function _reportNewHeadMerge($head, $ex, $offset)
    {
        array_splice($head, $offset, 0, $ex);

        return $head;
    }

    /**
     * 报表模型
     * Create by zhangyangzhen
     * Date: 2019/2/26
     * Time: 15:01
     * @return Statistics|null
     */
    private function _getStatisModel()
    {
        if (empty($this->_statisModel)) {
            $this->_statisModel = new Statistics();
        }

        return $this->_statisModel;
    }

    /**
     * 报表模型
     * Create by zhangyangzhen
     * Date: 2019/2/26
     * Time: 14:57
     */
    private function _getBizStatisModel()
    {
        if (empty($this->_bizStatisModel)) {
            $this->_bizStatisModel = new BizStatis();
        }

        return $this->_bizStatisModel;
    }
}