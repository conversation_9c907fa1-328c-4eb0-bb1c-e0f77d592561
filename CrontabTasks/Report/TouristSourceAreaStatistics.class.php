<?php
/**
 * 客源地报表脚本
 * <AUTHOR>
 * @date   2024/07/08
 */

namespace CrontabTasks\Report;

use Business\Statistics\TouristSourceArea\CreateStatistics as CreateStatisticsBiz;
use Business\Statistics\TouristSourceArea\CleanUpData as CleanUpDataBiz;

//权限判断
if (!defined('PFT_CLI')) {
    exit('Access Deny');
}

class TouristSourceAreaStatistics
{
    //日志文件路径
    private $_logPath = 'tourist_source_area_report';

    public function __construct()
    {
        //运行时间不做限制
        set_time_limit(0);
    }

    /**
     * 每小时游客追踪记录处理客源地任务
     * 条件：结束时间大于当前执行的时间 supplier_id in 、type=3 、 end_time > current_time
     * <AUTHOR>
     * @date   2024/07/08
     *
     * @命令：sudo /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/TouristSourceAreaStatistics statisticsTask
     *
     * @参数 3开始时间 4结束时间 5指定供应商ID 6每次处理条数（数据源查询pageSize）
     *
     */
    public function statisticsTask()
    {
        $params = $GLOBALS['argv'];

        if (!empty($params[3])) {
            if (!strtotime($params[3])) {
                echo "参数3格式错误";
                exit();
            }
        }

        if (!empty($params[4])) {
            if (!strtotime($params[4])) {
                echo "参数4格式错误";
                exit();
            }
        }

        //统计前一小时数据 默认
        $startTime = date('Y-m-d H:00:00', strtotime('-1 hours'));
        $endTime   = date('Y-m-d H:59:59', strtotime('-1 hours'));

        //指定时间
        if (!empty($params[3])  && !empty($params[4])) {
            $startTime = date('Y-m-d H:00:00', strtotime($params[3]));
            $endTime   = date('Y-m-d H:59:59', strtotime($params[4]));
        }

        //指定供应商ID
        $supplierId = empty($params[5]) ? 0 : intval($params[5]);

        //处理页数
        $pageSize = empty($params[6]) ? 100 : intval($params[6]);

        $extConfig = [
            'pageSize' => $pageSize
        ];

        $createStatisticsBiz = new CreateStatisticsBiz($this->_logPath, $extConfig);

        $createStatisticsBiz->runCreateTask($startTime, $endTime, $supplierId);

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 定时处理开通刷数据业务
     * 条件：处理状态为待处理的 supplier_id in 、type=4 、status=1
     * <AUTHOR>
     * @date   2024/07/08
     *
     * @命令：sudo /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/TouristSourceAreaStatistics statisticsHistoryTask
     *
     *  @参数 3每次处理条数（数据源查询pageSize）
     */
    public function statisticsHistoryTask()
    {
        $params = $GLOBALS['argv'];
        //处理页数
        $pageSize = empty($params[3]) ? 100 : intval($params[3]);

        $extConfig = [
            'pageSize' => $pageSize
        ];

        $createStatisticsBiz = new CreateStatisticsBiz($this->_logPath, $extConfig);

        $createStatisticsBiz->runHistoryTask();

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }

    /**
     * 2. 每天凌晨清除客源地数据任务（已归档数据不删除）
     * 条件：结束时间小于当前执行的时间 supplier_id in 、type=3 、 end_time < current_date
     * <AUTHOR>
     * @date   2024/07/08
     *
     * @命令：sudo /usr/bin/php /var/www/html/Service/Crontab/runNew.php Report/TouristSourceAreaStatistics cleanUpDataTask
     *
     * @参数 3每次处理条数（数据源查询pageSize）  4定供应商ID 5开始时间 6结束时间
     */
    public function cleanUpDataTask()
    {
        $params = $GLOBALS['argv'];
        //处理页数
        $pageSize = empty($params[3]) ? 100 : intval($params[3]);

        //指定供应商ID
        $supplierId = empty($params[4]) ? 0 : intval($params[4]);

        //前一天鹅数据 默认
        $startTime = date('Y-m-d 00:00:00', strtotime('-1 day'));
        $endTime   = date('Y-m-d 23:59:59', strtotime('-1 day'));

        //指定时间
        if (!empty($params[5])  && !empty($params[6])) {
            $startTime = date('Y-m-d 00:00:00', strtotime($params[5]));
            $endTime   = date('Y-m-d 23:59:59', strtotime($params[6]));
        }

        $extConfig = [
            'pageSize' => $pageSize
        ];

        $cleanUpDataBiz = new CleanUpDataBiz($this->_logPath, $extConfig);

        $cleanUpDataBiz->runTask($startTime, $endTime, $supplierId);

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }
}