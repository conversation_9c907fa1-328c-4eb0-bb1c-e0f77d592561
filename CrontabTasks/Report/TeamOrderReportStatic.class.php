<?php

namespace CrontabTasks\Report;

use Business\Statistics\CreateTeamCheckReportV2;
use Business\Statistics\CreateTeamOrderReportV2;
use Library\Controller;

class TeamOrderReportStatic extends Controller
{
    private $_logPath = 'team_statistic_report';

    /**
     * 团单日报表    php /var/www/html/Service/Crontab/runNew.php Report/TeamOrderReportStatic runTask
     * 3是执行日期  4报表类型 5指定商户 6新旧版分时段处理
     *
     *
     */
    public function runTask()
    {
        $params = $GLOBALS['argv'];
        //时间
        if (isset($params[3])) {
            $day = $params[3];
            $tmp = strtotime($day);

            if (!$tmp) {
                echo '时间参数错误';
                exit;
            }

            $date = date('Y-m-d', $tmp);
        } else {
            //统计前一天的数据
            $date = date('Y-m-d', strtotime('-1 days'));
        }
        if (!isset($params[4])) {
            $orderReportBusiness = new CreateTeamOrderReportV2($this->_logPath);
            $orderReportBusiness->runTask($date);

            $orderReportBusiness = new CreateTeamCheckReportV2($this->_logPath);
            $orderReportBusiness->runTask($date);
        }
        //需要执行的报表
        if (isset($params[4])) {
            $listArr = explode(',', $params['4']);
            if (!empty($listArr)) {
                foreach ($listArr as $item) {
                    switch ($item) {
                        case 'order':
                            $orderReportBusiness = new CreateTeamOrderReportV2($this->_logPath);
                            $orderReportBusiness->runTask($date);
                            break;
                        case "check":
                            $orderReportBusiness = new CreateTeamCheckReportV2($this->_logPath);
                            $orderReportBusiness->runTask($date);
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        echo '执行结束, 请查看' . BASE_LOG_DIR . '/cli/' . $this->_logPath . '下是否有错误日志' . "\r\n";
    }
}