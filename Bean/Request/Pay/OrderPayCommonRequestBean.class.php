<?php

namespace Bean\Request\Pay;

use Bean\SplBean;

class OrderPayCommonRequestBean extends SplBean
{

    protected $outTradeNo;  //订单号
    protected $merchantId;  //收款商户sid
    protected $subject;     //订单描述
    protected $payType;     //支付方式1-支付宝 2-微信
    protected $channelId;   //支付渠道id  1-招行
    protected $fontUrl;   //支付成功跳转地址
    protected $verify;    //是否验证
    protected $attach; //扩展信息
    //交易过期时间 单位秒
    protected $orderExpire;


    public function __construct(array $data = null, $autoCreateProperty = false)
    {
        parent::__construct($data, $autoCreateProperty);

    }

    /**
     * @return mixed
     */
    public function getOutTradeNo()
    {
        return $this->outTradeNo;
    }

    /**
     * @param  mixed  $outTradeNo
     */
    public function setOutTradeNo($outTradeNo)
    {
        $this->outTradeNo = $outTradeNo;
    }

    /**
     * @return mixed
     */
    public function getMerchantId()
    {
        return $this->merchantId;
    }

    /**
     * @param  mixed  $merchantId
     */
    public function setMerchantId($merchantId)
    {
        $this->merchantId = $merchantId;
    }

    /**
     * @return mixed
     */
    public function getSubject()
    {
        return $this->subject;
    }

    /**
     * @param  mixed  $subject
     */
    public function setSubject($subject)
    {
        $this->subject = $subject;
    }

    /**
     * @return mixed
     */
    public function getPayType()
    {
        return $this->payType;
    }

    /**
     * @param  mixed  $payType
     */
    public function setPayType($payType)
    {
        $this->payType = $payType;
    }

    /**
     * @return mixed
     */
    public function getChannelId()
    {
        return $this->channelId;
    }

    /**
     * @param  mixed  $channelId
     */
    public function setChannelId($channelId)
    {
        $this->channelId = $channelId;
    }

    /**
     * @return mixed
     */
    public function getFontUrl()
    {
        return $this->fontUrl;
    }

    /**
     * @param  mixed  $fontUrl
     */
    public function setFontUrl($fontUrl)
    {
        $this->fontUrl = $fontUrl;
    }

    /**
     * @return mixed
     */
    public function getVerify()
    {
        return $this->verify;
    }

    /**
     * @param  mixed  $verify
     */
    public function setVerify($verify)
    {
        $this->verify = $verify;
    }
    /**
     * @return mixed
     */
    public function getAttach()
    {
        return $this->attach;
    }

    /**
     * @param mixed $attach
     */
    public function setAttach($attach): void
    {
        $this->attach = $attach;
    }
}