<?php

use Business\AnnualCard\CardManage;
use Business\AnnualCard\PhysicsOperateService;
use Business\Face\AnnualAliFaceBiz;
use Business\JavaApi\Order\Order;
use Business\JsonRpcApi\MessageService\MessageService;
use Library\Container;
use Library\MessageNotify\PFTSMSInterface;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Tools\Helpers;
use Business\Product\AnnualCardConst;
use Business\AnnualCard\Package as PackageBiz;
use Library\Util\EnvUtil;
use Model\Annual\AliFaceMapModel;
use Model\Product\AnnualCard;
use Model\Product\BaseCard;

/**
 * 年卡批量下单异步任务
 * 注意：这里的代码改动后需要重启生效
 * 日志是在 /alidata/log/site/cli/annual/task/目录下
 * <AUTHOR>
 * @date 2020-07-03
 */

class AnnualCardTask_Job
{
    public $args = [];
    private $logPath = 'annual/task';
    private $failPath = 'annual/task/fail';

    private $addPrivilegeLogPath = 'annual/task/add_privilege';
    private $delPrivilegeLogPath = 'annual/task/delete_privilege';
    private $postponeLogPath = 'annual/task/postpone_privilege';
    private $importGiftCard = 'annual/task/import_gift_card';
    private $baseCardModel = null;
    private $annualCardModel = null;
    /**
     * 执行入口
     *
     * <AUTHOR>
     * @date   2020-07-03
     */
    public function perform()
    {
        pft_log('annual/task/args', json_encode($this->args));
        if (!is_null($this->args['payTrackType'])) {
            $annualCardBis   = new \Business\Product\AnnualCard();
            $annualCardTask  = new \Model\Product\AnnualCardTask();
            if (in_array($this->args['payTrackType'], ['disabled', 'recover'])) {
                $table = $this->args['saveData']['table'];
                $annualCardModel = new \Model\Product\AnnualCard(0, 0, $table);
            } else {
                $annualCardModel = new \Model\Product\AnnualCard();
            }
            $cardMode        = new \Model\Product\BaseCard();
            $cardManageService = new \Business\AnnualCard\AnnualCardManage();

            switch ($this->args['payTrackType']) {
                //下单
                case 'order':
                    $mid          = $this->args['saveData']['mid'];
                    $param        = $this->args['saveData']['param'];
                    $virtualNoArr = $this->args['saveData']['virtualNoArr'];
                    $taskId       = $this->args['saveData']['taskId'];

                    if (!$virtualNoArr || !is_array($virtualNoArr) || !$mid || !$param) {
                        pft_log('annual/task', json_encode(['数据缺失', $this->args]));
                        return false;
                    }
                    $useMultiTourist = !empty($param['holder_list']); //是否使用多个持卡人

                    foreach ($virtualNoArr as $i => $virtualNo) {
                        $ordertel         = $param['ordertel'] ?? '';
                        $ordername        = $param['ordername'] ?? '';
                        $idcard           = $param['id_card'] ?? '';
                        $avatar           = $param['avatar'] ?? [];
                        $voucherType      = $param['voucher_type'] ?? 1;
                        $affiliatesPerson = $param['affiliates_person'] ?? [];
                        $auditInfo        = [];
                        if ($useMultiTourist) {
                            $ordertel         = $param['holder_list'][$i]['ordertel'] ?? '';
                            $ordername        = $param['holder_list'][$i]['ordername'] ?? '';
                            $idcard           = $param['holder_list'][$i]['id_card'] ?? '';
                            $avatar           = $param['holder_list'][$i]['avatar'] ?? [];
                            $voucherType      = $param['holder_list'][$i]['voucher_type'] ?? 1;
                            $affiliatesPerson = $param['holder_list'][$i]['affiliates_person'] ?? [];
                            $auditInfo        = $param['holder_list'][$i]['audit_info'] ?? [];
                        }

                        $handleData = [
                            'aid'               => $param['aid'],
                            'order_type'        => $param['order_type'],
                            'pid'               => $param['pid'],
                            'paymode'           => $param['paymode'],
                            'mid'               => $mid,
                            'memo'              => $param['memo'],
                            'ordertel'          => $ordertel,
                            'ordername'         => $ordername,
                            'ordermode'         => $param['ordermode'],
                            'id_card'           => $idcard,
                            'avatar'            => $avatar,
                            'virtual_no'        => $virtualNo,
                            'inactive'          => $param['inactive'] ?: 0,  // 0需要激活, 1不需要激活操作
                            'rand_math'         => mt_rand(100, 999) . $virtualNo, // 添加一个随机数，避免批量年卡下单枷锁
                            'card_type'         => 'virtual',
                            'voucher_type'      => $voucherType, //年卡证件类型
                            'affiliates_person' => $affiliatesPerson, //附属持卡人
                            'audit_info'        => $auditInfo, //年卡激活审核信息
                        ];
                        $result     = $annualCardBis->orderForCard($mid, (object)$handleData);
                        $ext        = ['voucher_type' => $voucherType];

                        if ($result['code'] == 200) {
                            $ordernum   = $result['data']['ordernum'];
                            $totalMoney = $result['data']['totalMoney'];

                            $taskRes = $annualCardTask->updateTaskDetail($taskId, $virtualNo, $param['aid'], '', $ordernum,
                                $totalMoney, 1, $ext);
                            if (!$taskRes) {
                                pft_log('annual/task',
                                    json_encode([
                                        '详情状态更新失败',
                                        $taskId,
                                        $virtualNo,
                                        $param['aid'],
                                        $ordernum,
                                        $totalMoney,
                                        $result,
                                    ]));
                            }
                        } else {
                            $taskRes = $annualCardTask->updateTaskDetail($taskId, $virtualNo, $param['aid'],
                                $result['msg'], $ext);
                            if (!$taskRes) {
                                pft_log('annual/task',
                                    json_encode(['详情状态更新失败', $taskId, $virtualNo, $param['aid'], $result]));
                            }
                        }
                    }

                    $time       = time();
                    $updateTask = $annualCardTask->updateTask($taskId, 1, $time);
                    $revokeRes  = $annualCardModel->lockOrUnLockAnnualCard($virtualNoArr, 0);
                    if (!$updateTask || !$revokeRes) {
                        @pft_log('annual/task',
                            json_encode(['任务状态更新失败', 'updateTask' => $updateTask, 'revokeRes' => $revokeRes]));
                    }
                    break;
                //导入
                case 'import':
                    //年卡导入操作
                    $sid           = $this->args['saveData']['sid'];
                    $pid           = $this->args['saveData']['pid'];
                    $realExcelData = $this->args['saveData']['realExcelData'];
                    $taskId        = $this->args['saveData']['taskId'];
                    $taskType      = $this->args['saveData']['taskType'];

                    if (!$realExcelData || !is_array($realExcelData) || !$sid || !$taskId) {
                        @pft_log('annual/task', json_encode(['数据缺失', $this->args]));

                        return false;
                    }

                    //判断类型是否是导入
                    if ($taskType != 1) {
                        @pft_log('annual/task', json_encode(['数据缺失', $this->args]));

                        return false;
                    }

                    //生成虚拟卡号
                    $virtualNoRes = $annualCardBis->createVirtualNo(count($realExcelData));
                    if ($virtualNoRes['code'] != 200 || empty($virtualNoRes['data']['success'])) {
                        @pft_log('annual/task', json_encode(['虚拟卡号生成失败']));

                        return false;
                    }

                    $virtualNoArr = $virtualNoRes['data']['success'];

                    //查询产品信息
                    $javaApi = new \Business\CommodityCenter\Ticket();
                    $ticket  = $javaApi->queryTicketInfoByProductIds([$pid]);
                    if (!$ticket) {
                        @pft_log('annual/task', json_encode(['年卡套餐信息获取失败']));

                        return false;
                    }

                    $ticketMap = [];
                    $extInfo   = [];
                    foreach ($ticket as $item) {
                        $ticketMap[$item['ticket']['pid']] = [
                            'tid' => $item['ticket']['id'],
                            'lid' => $item['land']['id'],
                            'pid' => $item['product']['id'],
                            'sid' => $item['ticket']['apply_did'],
                        ];
                        $extInfo[$item['ticket']['pid']] = [
                            'available_time_period' => $item['ext']['available_time_period'] ?? '',
                            'annual_valid_type'     => $item['ext']['annual_valid_type'] ?? 0,
                            'annual_valid_day'      => $item['ext']['annual_valid_day'] ?? 30,
                            'annual_valid_start'    => $item['ext']['annual_valid_start'] ?? '',
                            'annual_valid_end'      => $item['ext']['annual_valid_end'] ?? '',
                            'family_card_num'       => $item['land_f']['family_card_num'] ?? 1,
                            'valid_period_days'     => $item['ticket']['delaydays'] ?? 0,
                        ];
                    }

                    $packageBiz = new \Business\AnnualCard\PackageManage();

                    //先创建用户 然后创建年卡
                    $detailArr = [];
                    foreach ($realExcelData as $key => $value) {
                        $cardNo            = $value[0];
                        $physicsNo         = $value[1];
                        $dname             = $value[2];
                        $mobile            = $value[3];
                        $voucherType       = $value[4];
                        $idCardNo          = trim($value[5]);
                        $beginTime         = $value[6];
                        $endTime           = $value[7];
                        $affiliatesPerson1 = $value[8] ?? "";
                        $voucherType1      = $value[9] ?? 1;
                        $idCard1           = $value[10] ?? "";
                        $affiliatesPerson2 = $value[11] ?? "";
                        $voucherType2      = $value[12] ?? 1;
                        $idCard2           = $value[13] ?? "";
                        $affiliatesPerson3 = $value[14] ?? "";
                        $voucherType3      = $value[15] ?? 1;
                        $idCard3           = $value[16] ?? "";
                        $affiliatesPerson4 = $value[17] ?? "";
                        $voucherType4      = $value[18] ?? 1;
                        $idCard4           = $value[19] ?? "";
                        $affiliatesPerson  = [];
                        $idCardData        = [];
                        if (!empty($affiliatesPerson1)) {
                            $affiliatesPerson[] = [
                                'name'         => $affiliatesPerson1,
                                'voucher_type' => $voucherType1,
                                'id_card'      => $idCard1,
                            ];
                            if (!empty($idCard1)) {
                                $idCardData[] = [
                                    'documentType' => $voucherType1,
                                    'idNumber'     => $idCard1,
                                ];
                            }
                        }
                        if (!empty($affiliatesPerson2)) {
                            $affiliatesPerson[] = [
                                'name'         => $affiliatesPerson2,
                                'voucher_type' => $voucherType2,
                                'id_card'      => $idCard2,
                            ];
                            if (!empty($idCard2)) {
                                $idCardData[] = [
                                    'documentType' => $voucherType2,
                                    'idNumber'     => $idCard2,
                                ];
                            }
                        }
                        if (!empty($affiliatesPerson3)) {
                            $affiliatesPerson[] = [
                                'name'        => $affiliatesPerson3,
                                'voucher_type' => $voucherType3,
                                'id_card'     => $idCard3,
                            ];
                            if (!empty($idCard3)) {
                                $idCardData[] = [
                                    'documentType' => $voucherType3,
                                    'idNumber'     => $idCard3,
                                ];
                            }
                        }
                        if (!empty($affiliatesPerson4)) {
                            $affiliatesPerson[] = [
                                'name'         => $affiliatesPerson4,
                                'voucher_type' => $voucherType4,
                                'id_card'      => $idCard4,
                            ];
                            if (!empty($idCard4)) {
                                $idCardData[] = [
                                    'documentType' => $voucherType4,
                                    'idNumber'     => $idCard4,
                                ];
                            }
                        }
                        $affiliatesPerson = $annualCardBis->makeAffiliatesPersonArr($affiliatesPerson);
                        $affiliatesPerson = $affiliatesPerson['data'] ?? [];
                        $detailArr[$key] = [
                            'virtual_no'   => '',
                            'dname'        => $dname,
                            'id_card_no'   => $idCardNo,
                            'mobile'       => $mobile,
                            'card_no'      => $cardNo,
                            'physics_no'   => $physicsNo,
                            'avalid_begin' => strtotime(date('Y-m-d 00:00:00', strtotime($beginTime))),
                            'avalid_end'   => strtotime(date('Y-m-d 23:59:59', strtotime($endTime))),
                            'd_status'     => 2,
                            'remark'       => '',
                            'ext'          => json_encode(['voucher_type' => $voucherType]),
                        ];
                        $virtualNo                     = array_shift($virtualNoArr);
                        $detailArr[$key]['virtual_no'] = $virtualNo;
                        $idCardArr    = empty($affiliatesPerson) ? [] : array_column($affiliatesPerson, 'id_card');
                        $existsIdCard = [];
                        foreach ($idCardArr as $item) {
                            if (in_array($item, $existsIdCard)) {
                                $detailArr[$key]['remark'] = "证件号码{$item}重复";
                                continue 2;
                            }
                            $existsIdCard[] = $item;
                        }
                        if (!empty($affiliatesPerson) && $extInfo[$pid]['family_card_num'] < count($affiliatesPerson) + 1) {
                            $detailArr[$key]['remark'] = "年卡未配置多人年卡或多人年卡不支持填写". count($affiliatesPerson). "张附属卡信息";
                            continue;
                        }
                        //物理卡号和实体卡号必须同时存在
                        if (($cardNo && empty($physicsNo)) || ($physicsNo && empty($cardNo))) {
                            $detailArr[$key]['remark'] = '缺少必要参数';
                            continue;
                        }

                        //有效期格式不正确直接失败
                        if (strlen($beginTime) != 8 || strlen($endTime) != 8) {
                            $detailArr[$key]['remark'] = '有效期格式不正确';
                            continue;
                        }
                        if ($idCardNo) {
                            $idCardData[] = [
                                'documentType' => $voucherType ?? 1,
                                'idNumber'     => $idCardNo,
                            ];
                        }
                        //有证件号的时候校验下证件类型
                        if ($idCardData) {
                            $orderApi         = new Order();
                            $certificateCheck = $orderApi->certificateCheck($idCardData);
                            if ($certificateCheck['code'] != 200) {
                                pft_log('annual/task', '导入数据' . json_encode($idCardData) . "校验结果" . json_encode($certificateCheck));
                                $detailArr[$key]['remark'] = $certificateCheck['msg'];
                                continue;
                            }
                        }

                        if (!$dname || !$mobile) {
                            $detailArr[$key]['remark'] = '缺少必要参数';

                            continue;
                        }

                        //判断年卡有效期格式
                        if (!strtotime($beginTime) || !strtotime($endTime)) {
                            $detailArr[$key]['remark'] = '年卡有效期错误';

                            continue;
                        }

                        //年卡有效期时间判断
                        if (strtotime($endTime) < strtotime($beginTime)) {
                            $detailArr[$key]['remark'] = '有效期结束时间不能早于开始时间';

                            continue;
                        }
                        //物理卡号和实体卡号同时存在 需要校验一次物理卡号是否存在  当前供应商下的实体卡号是否存在
                        if ($cardNo && $physicsNo) {
                            //检测pft_card 中是否存在
                            $cardInfoArr = $cardMode->getCardInfoByPhysicsNo($physicsNo, 'id, type, status');
                            if ($cardInfoArr) {
                                $detailArr[$key]['remark'] = '物理卡号已存在';

                                continue;
                            }
                            //检测当前供应商下实体卡号是否存在
                            $cardInfoArr = $annualCardModel->getAnnualCardBycardNoAndsid($cardNo, $sid);
                            if ($cardInfoArr) {
                                $detailArr[$key]['remark'] = "当前供应商已存在该实体卡号:{$cardNo}";

                                continue;
                            }

                            //创建卡类基础信息
                            $createRes = $cardMode->createBaseCard($physicsNo, $type = 3, time());
                            if ($createRes === false) {
                                $detailArr[$key]['remark'] = '物理卡添加失败';

                                continue;
                            }

                            $physicsNo = dechex($physicsNo);
                            $cardNo    = $value[0];
                        }

                        //用户创建（存在新建用户 否则返回对应用户id）
                        $paramsObj = (Object)[
                            'ordertel'  => $mobile,
                            'ordername' => $dname,
                            'id_card'   => $idCardNo,
                        ];

                        $userObj         = new \stdClass();
                        $userObj->name   = $paramsObj->ordername;
                        $userObj->mobile = $paramsObj->ordertel;
                        $userObj->idCard = $paramsObj->id_card;

                        $bindMemRes = $annualCardBis->_getBindMemberId($userObj);
                        if ($bindMemRes['code'] != 200) {
                            pft_log('annual/task', json_encode([
                                'ac'        => 'import',
                                'res'       => $bindMemRes,
                                'paramsObj' => $paramsObj,
                            ], JSON_UNESCAPED_UNICODE));

                            $detailArr[$key]['remark'] = $bindMemRes['msg'];

                            continue;
                        }


                        //激活时间计算
                        $activeTime        = time();

                        $startTime = strtotime(date('Y-m-d 00:00:00', strtotime($beginTime)));
                        $endTime   = strtotime(date('Y-m-d 23:59:59', strtotime($endTime)));
                        //年卡信息补充
                        $extInfo[$pid]['voucher_type'] = $voucherType;
                        $tmpAnnualCardInfo = [
                            'memberid'     => $bindMemRes['data']['member_id'],
                            'sid'          => $sid,
                            'pid'          => $pid,
                            'virtual_no'   => $virtualNo,
                            'physics_no'   => $physicsNo,
                            'card_no'      => $cardNo,
                            'status'       => AnnualCardConst::STATUS_ACTIVE,
                            'create_time'  => time(),
                            'sale_time'    => $activeTime,
                            'active_time'  => $activeTime,
                            'update_time'  => $activeTime,
                            'avalid_begin' => $startTime,
                            'avalid_end'   => $endTime,
                            'dname'        => $dname,
                            'id_card_no'   => $idCardNo ?? '',
                            'mobile'       => $mobile,
                            'annual_status'=> AnnualCardConst::ANNUAL_STATUS_ACTIVE,
                            'ext_info'     => json_encode($extInfo[$pid]),
                        ];

                        $cardId = $annualCardModel->createAnnualCardSingle($tmpAnnualCardInfo);

                        if ($cardId === false) {
                            pft_log('annual/task', json_encode([
                                'ac'   => 'import',
                                'res'  => '年卡创建失败',
                                'data' => $tmpAnnualCardInfo,
                            ], JSON_UNESCAPED_UNICODE));

                            $detailArr[$key]['remark'] = '年卡创建失败';

                            continue;
                        }

                        //年卡套餐后续处理
                        $handlerRes = $packageBiz->packageAfterHandlerForOrder($cardId, $extInfo[$pid]['annual_valid_type'], true, $ticketMap[$pid]['lid'], $pid,
                            $sid, $ticketMap[$pid]['tid'], $virtualNo, '', 1, $sid, $startTime, $endTime, '年卡导入', $extInfo[$pid], 2, $affiliatesPerson);
                        if ($handlerRes['code'] != 200) {
                            pft_log('annual/task', json_encode([
                                'ac'   => 'import',
                                'res'  => $handlerRes['msg'],
                                'params' => [$cardId, $extInfo[$pid]['annual_valid_type'], true, $ticketMap[$pid]['lid'], $pid,
                                    $sid, $ticketMap[$pid]['tid'], $virtualNo, '', 1, $sid, $startTime, $endTime, '年卡导入', $extInfo[$pid]],
                            ], JSON_UNESCAPED_UNICODE));

                            $detailArr[$key]['remark'] = '年卡套餐写入异常';

                            continue;
                        }

                        $detailArr[$key]['d_status'] = 1;
                    }
                    if ($detailArr) {
                        $res = $annualCardTask->bindOptTaskDetail($detailArr, $sid, $taskId, $taskType);
                        if ($res === false) {
                            $logData = [
                                'ac'        => 'import',
                                'res'       => '任务写入失败',
                                'sid'       => $sid,
                                'detailArr' => $detailArr,
                                'taskId'    => $taskId,
                                'taskType'  => $taskType,
                            ];
                            pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));
                        }
                    } else {
                        $logData = [
                            'ac'            => 'import',
                            'res'           => '数据为空',
                            'sid'           => $sid,
                            'realExcelData' => $realExcelData,
                            'taskId'        => $taskId,
                        ];
                        pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));
                    }

                    $time       = time();
                    $updateTask = $annualCardTask->updateTask($taskId, 1, $time);
                    if (!$updateTask) {
                        $logData = [
                            'ac'         => 'import',
                            'res'        => '任务状态更新失败',
                            'updateTask' => $updateTask,
                            'taskId'     => $taskId,
                        ];
                        pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));
                    }

                    break;
                //延期
                case 'postpone':
                    $this->handlePostpone();
                    break;
                case 'sms':
                    $sid           = $this->args['saveData']['sid'];
                    $opid          = $this->args['saveData']['opid'];
                    $realExcelData = $this->args['saveData']['realExcelData'];
                    $taskId        = $this->args['saveData']['taskId'];
                    $taskType      = $this->args['saveData']['taskType'];

                    if (!$realExcelData || !is_array($realExcelData) || !$sid || !$taskId) {
                        @pft_log($this->logPath, json_encode(['数据缺失', $this->args]));

                        return false;
                    }

                    //判断操作类型是否是人脸短信
                    if ($taskType != 3) {
                        @pft_log($this->logPath, json_encode(['数据缺失', $this->args]));

                        return false;
                    }

                    $detailArr = [];
                    $batchId = time();
                    $annualModel = new \Model\Product\AnnualCard();
                    $cardManage = new CardManage();
                    foreach ($realExcelData as $key => $value) {
                        //$annualStatus    = AnnualCardConst::getAnnualFinalStatus($value['annual_status'], $value['avalid_end']);
                        $detailArr[$key] = [
                            'virtual_no'   => $value['virtual_no'] ?? '',
                            'physics_no'   => $value['physics_no'] ?? '',
                            'card_no'      => $value['card_no'],
                            'dname'        => $value['dname'],
                            'id_card_no'   => $value['id_card_no'],
                            'mobile'       => $value['mobile'],
                            'avalid_begin' => $value['avalid_begin'],
                            'avalid_end'   => $value['avalid_end'],
                            'd_status'     => 2,
                            'remark'       => '',
                            'ext'          => json_encode(['annual_status' => $value['status']]),
                        ];

                        if (!in_array((int)$value['status'], [AnnualCardConst::STATUS_ACTIVE])) {
                            pft_log('annual/task', json_encode([
                                'ac'    => 'disabled',
                                'res'   => '该年卡状态不支持发送短信',
                                'value' => $value,
                            ], JSON_UNESCAPED_UNICODE));

                            $detailArr[$key]['remark'] = '该年卡状态不支持发送短信';

                            continue;
                        }

                        if (!Helpers::isMobile($value['mobile'])) {
                            pft_log('annual/task', json_encode([
                                'ac'    => 'sms',
                                'res'   => '手机号不能为空',
                                'value' => $value,
                            ], JSON_UNESCAPED_UNICODE));

                            $detailArr[$key]['remark'] = '手机号不能为空';

                            continue;
                        }

                        $token  = $this->hashId($value['mobile'], 'encode');
                        $url    = "https://wx.12301.cc/c/annual_face.html?token={$token}&sid={$value['sid']}&cardno={$value['virtual_no']}";

                        if ($value['sid'] == 2010497) {
                            $template   = "池州市民旅游卡全面升级，线上信息录入，线下刷脸入园，速度采集人脸（{$url}），更多详情关注“乐GO池州”公众号或拨打电话0566-2319868。";
                        } else {
                            //$template   = "年卡功能全面升级，线上信息录入线下刷脸入园，速度采集人脸体验吧，采集地址:{$url}";
                            $card = $annualModel->getAnnualCard($value['virtual_no'], 'virtual_no');
                            $ticketInfo = $cardManage->getTicketAndPrivileges($card['sid'], $card['pid'], $card['id'], false, false);
                            $template   = "您持有的{$ticketInfo['tInfo'][$card['pid']]['lname']}{$ticketInfo['tInfo'][$card['pid']]['tname']}{$card['virtual_no']}上传人像照片可刷脸快速入园，采集地址：{$url}";
                        }
                        $messageServiceApi = Container::pull(MessageService::class);
                        [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, PFTSMSInterface::ISDIY, $value['mobile'],
                            [$template], $value['sid'], '', '人脸采集', '票付通', '', 1, true);
                        if ($approval) {
                            if (!EnvUtil::isRelease()) {
                                pft_log('message_service', json_encode(['人脸采集', __METHOD__, [$value['mobile'], [$template], $value['sid']], $res], JSON_UNESCAPED_UNICODE));
                            }
                        } else {
                            /** @depreciate 放量结束后删除 */
                            $smsLib = SmsFactory::getFactory($value['mobile']);
                            $res = $smsLib->customMsg($value['sid'], '票付通', $template, $value['mobile'], '', '', true, '人脸采集');
                            if (!EnvUtil::isRelease()) {
                                pft_log('message_service', json_encode(['人脸采集.old', __METHOD__, [$value['mobile'], [$template], $value['sid']], $res], JSON_UNESCAPED_UNICODE));
                            }
                        }
                        //写入年卡管理日志
                        $ret = $cardManageService->addCardManageRecordWithCardInfo(AnnualCardConst::ANNUAL_OPT_BATCH_FACE_SMS, [
                            'opt_sid' => $sid,
                            'opt_member' => $opid,
                            'batch_id' => $batchId,
                        ], $value);
                        if ($ret['code'] != 200) {
                            pft_log($this->failPath, json_encode(['人脸短信操作记录异步添加失败', $ret['msg']]));
                        }
                        if ($res['code'] != 200) {
                            pft_log($this->logPath, json_encode([
                                'ac'    => 'sms',
                                'res'   => '发送通知失败',
                                'value' => $value,
                            ], JSON_UNESCAPED_UNICODE));

                            $detailArr[$key]['remark'] = '发送通知失败';

                            continue;
                        }

                        $detailArr[$key]['d_status'] = 1;
                    }
                    if ($detailArr) {
                        $res = $annualCardTask->bindOptTaskDetail($detailArr, $sid, $taskId, $taskType);
                        if ($res === false) {
                            $logData = [
                                'ac'        => 'sms',
                                'res'       => '任务写入失败',
                                'sid'       => $sid,
                                'detailArr' => $detailArr,
                                'taskId'    => $taskId,
                                'taskType'  => $taskType,
                            ];
                            pft_log($this->logPath, json_encode($logData, JSON_UNESCAPED_UNICODE));
                        }
                    } else {
                        $logData = [
                            'ac'            => 'sms',
                            'res'           => '数据为空',
                            'sid'           => $sid,
                            'realExcelData' => $realExcelData,
                            'taskId'        => $taskId,
                        ];
                        pft_log($this->logPath, json_encode($logData, JSON_UNESCAPED_UNICODE));
                    }

                    $time       = time();
                    $updateTask = $annualCardTask->updateTask($taskId, 1, $time);
                    if (!$updateTask) {
                        $logData = [
                            'ac'         => 'sms',
                            'res'        => '任务状态更新失败',
                            'updateTask' => $updateTask,
                            'taskId'     => $taskId,
                        ];
                        pft_log($this->logPath, json_encode($logData, JSON_UNESCAPED_UNICODE));
                    }
                    break;
                //禁用
                case 'disabled':
                    $sid           = $this->args['saveData']['sid'];
                    $opid           = $this->args['saveData']['opid'];
                    $realExcelData = $this->args['saveData']['realExcelData'];
                    $taskId        = $this->args['saveData']['taskId'];
                    $taskType      = $this->args['saveData']['taskType'];

                    if (!$realExcelData || !is_array($realExcelData) || !$sid || !$taskId) {
                        pft_log('annual/task', json_encode(['数据缺失', $this->args]));

                        return false;
                    }

                    //判断操作类型是否是禁用
                    if ($taskType != 4) {
                        pft_log('annual/task', json_encode(['数据缺失', $this->args]));

                        return false;
                    }

                    $detailArr = [];
                    $batchId = time();
                    $scenicRpc = new \Business\JsonRpcApi\ScenicLocalService\EscapeTicket();
                    //尝试删除人脸
                    foreach ($realExcelData as $key => $value) {
                        $detailArr[$key] = [
                            'virtual_no'   => $value['virtual_no'] ?? '',
                            'physics_no'   => $value['physics_no'] ?? '',
                            'card_no'      => $value['card_no'],
                            'dname'        => $value['dname'],
                            'id_card_no'   => $value['id_card_no'],
                            'mobile'       => $value['mobile'],
                            'avalid_begin' => $value['avalid_begin'],
                            'avalid_end'   => $value['avalid_end'],
                            'd_status'     => 2,
                            'remark'       => '',
                            'ext'          => json_encode(['annual_status' => $value['status']]),
                        ];

                        if (!in_array((int)$value['status'], [AnnualCardConst::STATUS_ACTIVE])) {
                            pft_log('annual/task', json_encode([
                                'ac'    => 'disabled',
                                'res'   => '该年卡状态不支持禁用',
                                'value' => $value,
                            ], JSON_UNESCAPED_UNICODE));

                            $detailArr[$key]['remark'] = '该年卡状态不支持禁用';

                            continue;
                        }
                        //拿到年卡叠加之前的状态
                        $annualStatus = $value['annual_status'] | AnnualCardConst::STATUS_BAN;

                        //恢复原始状态时  需要判断状态是否包含已激活 已使用和已挂失  如有包含原状态为1（已激活）  否则为0（未激活）
                        $updataData = [
                            'status'        => AnnualCardConst::STATUS_BAN,
                            'annual_status' => $annualStatus,
                        ];
                        $updataRes = $annualCardModel->updateAnnualById($value['id'], $updataData);
                        //尝试同步删除海康人脸
                        $scenicRpc->syncHikFace(0, 'del', $value['virtual_no']);
                        $annual = $annualCardModel->getAnnualCard($value['virtual_no'], 'virtual_no');
                        $callIsAliFacePlatform = AnnualAliFaceBiz::getInstance()->callIsAliFacePlatform($sid, $annual['pid']);
                        if ($callIsAliFacePlatform) {
                            $ticketModel = new \Model\Product\Ticket();
                            $ticketInfo = $ticketModel->getTicketInfoByPid($annual['pid']);
                            $ordernum = $annualCardModel->getOrdernumByVirtualNo($annual['virtual_no']);
                            //获取人脸id
                            $annualAliFaceIdMapModel = new AliFaceMapModel();
                            $faceIdList = $annualAliFaceIdMapModel->getValidFaceIdListByVirtualNo($value['virtual_no']);
                            if (!empty($faceIdList)) {
                                foreach ($faceIdList as $faceId) {
                                    AnnualAliFaceBiz::getInstance()->callDeleteFaceInfo([
                                        'virtual_no' => $annual['virtual_no'],
                                        'apply_did' => (int)$sid,
                                        'faceid' => $faceId,
                                        'order_id' => $ordernum,
                                        'lid' => (int)$ticketInfo['lid'],
                                        'id_card_no' => $value['id_card_no'],
                                    ]);
                                }
                            }
                        }
                        //写入年卡管理日志
                        $ret = $cardManageService->addCardManageRecordWithCardInfo(AnnualCardConst::ANNUAL_OPT_BATCH_BAN, [
                            'opt_sid' => $sid,
                            'opt_member' => $opid,
                            'batch_id' => $batchId,
                            //如果操作失败状态保持原来的不变
                            'fail_success' => $updataRes
                        ], $value);
                        if ($ret['code'] != 200) {
                            pft_log($this->failPath, json_encode(['批量禁用操作记录异步添加失败', $ret['msg']]));
                        }
                        if (!$updataRes) {
                            $logData = [
                                'ac'         => 'postpone',
                                'res'        => '年卡状态更新失败',
                                'data'       => $value,
                                'updataRes'  => $updataRes,
                                'checkResId' => $value['id'],
                                'updataData' => $updataData,
                            ];
                            pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));

                            $detailArr[$key]['remark'] = '年卡状态更新失败';

                            continue;
                        }
                        //操作成功之后 年卡状态改成最新的
                        $detailArr[$key]['ext']      = json_encode(['annual_status' => AnnualCardConst::ANNUAL_STATUS_BAN]);
                        $detailArr[$key]['d_status'] = 1;
                    }
                    if ($detailArr) {
                        $res = $annualCardTask->bindOptTaskDetail($detailArr, $sid, $taskId, $taskType);
                        if ($res === false) {
                            $logData = [
                                'ac'        => 'disabled',
                                'res'       => '任务写入失败',
                                'sid'       => $sid,
                                'detailArr' => $detailArr,
                                'taskId'    => $taskId,
                                'taskType'  => $taskType,
                            ];
                            pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));
                        }
                    } else {
                        $logData = [
                            'ac'            => 'disabled',
                            'res'           => '数据为空',
                            'sid'           => $sid,
                            'realExcelData' => $realExcelData,
                            'taskId'        => $taskId,
                        ];
                        pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));
                    }

                    $time       = time();
                    $updateTask = $annualCardTask->updateTask($taskId, 1, $time);
                    if (!$updateTask) {
                        $logData = [
                            'ac'         => 'disabled',
                            'res'        => '任务状态更新失败',
                            'updateTask' => $updateTask,
                            'taskId'     => $taskId,
                        ];
                        pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));
                    }
                    break;
                //恢复
                case 'recover':
                    $sid           = $this->args['saveData']['sid'];
                    $opid           = $this->args['saveData']['opid'];
                    $realExcelData = $this->args['saveData']['realExcelData'];
                    $taskId        = $this->args['saveData']['taskId'];
                    $taskType      = $this->args['saveData']['taskType'];

                    if (!$realExcelData || !is_array($realExcelData) || !$sid || !$taskId) {
                        pft_log('annual/task', json_encode(['数据缺失', $this->args]));
                        return false;
                    }

                    //判断操作类型是否是恢复
                    if ($taskType != 5) {
                        pft_log('annual/task', json_encode(['数据缺失', $this->args]));
                        return false;
                    }

                    $detailArr = [];
                    $batchId = time();
                    //尝试注册人脸
                    foreach ($realExcelData as $key => $value) {
                        //$finalStatus  = AnnualCardConst::getAnnualFinalStatus($value['annual_status'], $value['avalid_end']);
                        $detailArr[$key] = [
                            'virtual_no'   => $value['virtual_no'] ?? '',
                            'physics_no'   => $value['physics_no'] ?? '',
                            'card_no'      => $value['card_no'],
                            'dname'        => $value['dname'],
                            'id_card_no'   => $value['id_card_no'],
                            'mobile'       => $value['mobile'],
                            'avalid_begin' => $value['avalid_begin'],
                            'avalid_end'   => $value['avalid_end'],
                            'd_status'     => 2,
                            'remark'       => '',
                            'ext'          => json_encode(['annual_status' => $value['status']]),
                        ];

                        if (!in_array((int)$value['status'], [AnnualCardConst::STATUS_BAN])) {
                            pft_log('annual/task', json_encode([
                                'ac'    => 'recover',
                                'res'   => '该年卡状态不支持恢复',
                                'value' => $value,
                            ], JSON_UNESCAPED_UNICODE));

                            $detailArr[$key]['remark'] = '该年卡状态不支持恢复';

                            continue;
                        }


                        //拿到年卡叠加之前的状态
                        $annualStatus = $value['annual_status'] &~ AnnualCardConst::STATUS_BAN;

                        //恢复原始状态时  需要判断状态是否包含已激活 已使用和已挂失  如有包含原状态为1（已激活）  否则为0（未激活）
                        $updataData = [
                            'status'        => (($value['annual_status'] & (AnnualCardConst::ANNUAL_STATUS_ACTIVE | AnnualCardConst::ANNUAL_STATUS_LOSS | AnnualCardConst::ANNUAL_STATUS_USED)) > 0) ? 1 : 0,
                            'annual_status' => $annualStatus,
                        ];

                        $updataRes = $annualCardModel->updateAnnualById($value['id'], $updataData);
                        $annual = $annualCardModel->getAnnualCard($value['virtual_no'], 'virtual_no');
                        $callIsAliFacePlatform = AnnualAliFaceBiz::getInstance()->callIsAliFacePlatform($sid, $annual['pid']);
                        if ($callIsAliFacePlatform) {
                            $ticketModel = new \Model\Product\Ticket();
                            $ticketInfo = $ticketModel->getTicketInfoByPid($annual['pid']);
                            $ordernum = $annualCardModel->getOrdernumByVirtualNo($annual['virtual_no']);
                            AnnualAliFaceBiz::getInstance()->callRegisterFace([
                                'virtual_no' => $value['virtual_no'],
                                'apply_did' => (int)$sid,
                                'name' => $annual['dname'],
                                'id_card' => $annual['id_card_no'],
                                'lid' => (int)$ticketInfo['lid'],
                                'product_type' => 'I',
                                'order_id' => $ordernum,
                                'begin_time' => (int)date('Ymd', $annual['avalid_begin']),
                                'end_time' => (int)date('Ymd', $annual['avalid_end']),
                            ]);
                        }
                        //写入年卡管理日志
                        $ret = $cardManageService->addCardManageRecordWithCardInfo(AnnualCardConst::ANNUAL_OPT_BATCH_RECOVER, [
                            'opt_sid' => $sid,
                            'opt_member' => $opid,
                            'batch_id' => $batchId,
                            //如果操作失败状态保持原来的不变
                            'fail_success' => $updataRes
                        ], $value);
                        if ($ret['code'] != 200) {
                            pft_log($this->failPath, json_encode(['批量恢复操作记录异步添加失败', $ret['msg']]));
                        }
                        if (!$updataRes) {
                            $logData = [
                                'ac'         => 'postpone',
                                'res'        => '年卡状态更新失败',
                                'data'       => $value,
                                'updataRes'  => $updataRes,
                                'checkResId' => $value['id'],
                                'updataData' => $updataData,
                            ];
                            pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));

                            $detailArr[$key]['remark'] = '年卡状态更新失败';

                            continue;
                        }

                        //操作成功之后 年卡状态改成最新的
                        $detailArr[$key]['ext']      = json_encode(['annual_status' => $updataData['status']]);
                        $detailArr[$key]['d_status'] = 1;
                    }
                    if ($detailArr) {
                        $res = $annualCardTask->bindOptTaskDetail($detailArr, $sid, $taskId, $taskType);
                        if ($res === false) {
                            $logData = [
                                'ac'        => 'recover',
                                'res'       => '任务写入失败',
                                'sid'       => $sid,
                                'detailArr' => $detailArr,
                                'taskId'    => $taskId,
                                'taskType'  => $taskType,
                            ];
                            pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));
                        }
                    } else {
                        $logData = [
                            'ac'            => 'recover',
                            'res'           => '数据为空',
                            'sid'           => $sid,
                            'realExcelData' => $realExcelData,
                            'taskId'        => $taskId,
                        ];
                        pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));
                    }

                    $time       = time();
                    $updateTask = $annualCardTask->updateTask($taskId, 1, $time);
                    if (!$updateTask) {
                        $logData = [
                            'ac'         => 'recover',
                            'res'        => '任务状态更新失败',
                            'updateTask' => $updateTask,
                            'taskId'     => $taskId,
                        ];
                        pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));
                    }
                    break;
                //添加特权
                case 'add_privilege':
                    $this->_handleAddPrivilege();
                    break;
                //删除特权
                case 'delete_privilege':
                    $this->_handleDelPrivilege();
                    break;
                //导入礼品卡
                case 'import_gift_card':
                    $this->importGiftCard();
                    break;
                case 'physical_order':
                    $mid          = $this->args['saveData']['mid'];
                    $param        = $this->args['saveData']['param'];
                    $orderParams  = $this->args['saveData']['order_params'];
                    $taskId       = $this->args['saveData']['taskId'];

                    if (empty($orderParams) || !is_array($orderParams) || !$mid || !$param) {
                        @pft_log('annual/task', json_encode(['数据缺失', $this->args]));

                        return false;
                    }
                    $ordertel       = $param['ordertel'] ?? '';
                    $ordername      = $param['ordername'] ?? '';
                    $idcard         = $param['id_card'] ?? '';
                    $voucherType    = $param['voucher_type'] ?? 1;
                    $checkInventory = $param['check_inventory'] ?? 0;
                    $fail           = [];
                    foreach ($orderParams as $value) {
                        $handleData  = [
                            'aid'          => $param['aid'],
                            'order_type'   => $value['order_type'],
                            'pid'          => $param['pid'],
                            'paymode'      => $param['paymode'],
                            'mid'          => $mid,
                            'memo'         => $param['memo'],
                            'ordertel'     => $ordertel,
                            'ordername'    => $ordername,
                            'ordermode'    => $param['ordermode'],
                            'id_card'      => $idcard,
                            'avatar'       => [],
                            'virtual_no'   => $value['virtual_no'],
                            'inactive'     => 1,  // 0需要激活, 1不需要激活操作
                            'rand_math'    => mt_rand(100, 999) . $value['virtual_no'], // 添加一个随机数，避免批量年卡下单枷锁
                            'card_type'    => 'physics',
                            'voucher_type' => $voucherType, //年卡证件类型
                            'physics_no'   => $value['physics_no'],
                            'card_no'      => $value['card_no'],
                        ];
                        $result     = $annualCardBis->orderForCard($mid, (object)$handleData);
                        $ext        = ['voucher_type' => $voucherType];

                        if ($result['code'] == 200) {
                            $ordernum   = $result['data']['ordernum'];
                            $totalMoney = $result['data']['totalMoney'];

                            $taskRes = $annualCardTask->updateTaskDetail($taskId, $value['virtual_no'], $param['aid'], '', $ordernum,
                                $totalMoney, 1, $ext);
                            if (!$taskRes) {
                                @pft_log('annual/task',
                                    json_encode([
                                        '详情状态更新失败',
                                        $taskId,
                                        $value['virtual_no'],
                                        $param['aid'],
                                        $ordernum,
                                        $totalMoney,
                                        $result,
                                    ]));
                            }
                        } else {
                            $fail[] = $value['physics_no'];
                            $taskRes = $annualCardTask->updateTaskDetail($taskId, $value['virtual_no'], $param['aid'],
                                $result['msg'], $ext);
                            if (!$taskRes) {
                                @pft_log('annual/task',
                                    json_encode(['详情状态更新失败', $taskId, $value['virtual_no'], $param['aid'], $result]));
                            }

                        }
                    }
                    //下单的时候不扣库存，把失败的卡号删了
                    if (!empty($fail) && $checkInventory == 0) {
                        $where = [
                            'physics_no' => ['in', $fail],
                        ];
                        $deleteRes = $annualCardModel->deleteAnnualCard($where);
                        if ($deleteRes === false) {
                            @pft_log('annual/task', "sql". $annualCardModel->getLastSql() . "数据" . json_encode(['删除失败', $where]));
                        }
                    }
                    $virtualNoArr = array_column($orderParams, 'virtual_no');
                    $time         = time();
                    $updateTask   = $annualCardTask->updateTask($taskId, 1, $time);
                    $revokeRes    = $annualCardModel->lockOrUnLockAnnualCard($virtualNoArr, 0);
                    if (!$updateTask || !$revokeRes) {
                        @pft_log('annual/task',
                            json_encode(['任务状态更新失败', 'updateTask' => $updateTask, 'revokeRes' => $revokeRes]));
                    }
                    break;
                //物理卡批量发卡
                case 'bulk_associate_physics_card':
                    PhysicsOperateService::getInstance()->bulkAssociatePhysicsCard($this->args['saveData']);
                    break;
                default:
                    return false;
            }
        }

        return true;
    }

    private function hashId($token, $action)
    {
        $hashLib = new \Library\Hashids\Hashids('cab');
        if ($action == 'decode') {
            $res = $hashLib->decode($token);
            if ($res) {
                $res = array_shift($res);
            }
        } elseif ($action == 'encode') {
            $res = $hashLib->encode($token);
        }
        return $res;
    }

    /**
     * 批量延期处理
     * <AUTHOR>
     * @date   2022/10/20
     *
     * @return bool
     */
    private function handlePostpone()
    {
        //年卡延期操作
        $sid           = $this->args['saveData']['sid'];
        $realExcelData = $this->args['saveData']['realExcelData'];
        $taskId        = $this->args['saveData']['taskId'];
        $taskType      = $this->args['saveData']['taskType'];
        $operatorId    = $this->args['saveData']['opid'];

        //必传参数验证 是否需要优化到返回结果上
        if (!$realExcelData || !is_array($realExcelData) || !$sid || !$taskId) {
            pft_log('annual/task', json_encode(['数据缺失', $this->args]));
            return false;
        }

        //判断操作类型是否是延期
        if ($taskType != 2) {
            pft_log('annual/task', json_encode(['数据缺失', $this->args]));
            return false;
        }
        //$annualCardTask  = new \Model\Product\AnnualCardTask();
        $annualCardModel = new \Model\Product\AnnualCard();

        //延期方式 1调整至指定日期 2顺延X天
        $delayType = $realExcelData[0]['type'];
        //延期的日期
        $delayData = $realExcelData[0]['delay_data'];
        $map       = ['sid' => $sid];
        $detailArr = [];
        $recordArr = [];

        //当前时间
        $currentTime = time();
        //人脸续期
        foreach ($realExcelData as $key => $value) {
            $cardId          = $value['id'] ?? 0;
            $cardSid         = $value['sid'] ?? 0;
            $status          = $value['status'] ?? -1;
            $extInfo         = $value['ext_info'] ?? '';
            $extInfo         = json_decode($extInfo, true);
            $detailArr[$key] = [
                'track_id'     => $taskId,
                'sid'          => $sid,
                'virtual_no'   => $value['virtual_no'] ?? '',
                'physics_no'   => $value['physics_no'] ?? '',
                'card_no'      => $value['card_no'] ?? '',
                'dname'        => $value['dname'] ?? '',
                'id_card_no'   => $value['id_card_no'] ?? '',
                'mobile'       => $value['mobile'] ?? '',
                'avalid_begin' => 0,
                'avalid_end'   => 0,
                'd_status'     => 2,
                'remark'       => '',
                'ext'          => "[]",
            ];

            //上传模板未找到对应虚拟卡号时，延期失败
            if (isset($value['virtual_no']) && (!$cardId || !$cardSid)) {
                $detailArr[$key]['remark'] = '未找到该虚拟卡号';
                continue;
            }

            $checkRes = $annualCardModel->checkCard('virtual_no', $value['virtual_no'], $map, 'id,pid,status,id_card_no,avalid_begin,avalid_end');
            if (!$checkRes) {
                pft_log('annual/task', json_encode([
                    'ac'    => 'postpone',
                    'res'   => '虚拟卡号不存在',
                    'sid'   => $sid,
                    'value' => $value,
                ], JSON_UNESCAPED_UNICODE));
                $detailArr[$key]['remark'] = '虚拟卡号不存在';
                continue;
            }
            if ($checkRes['status'] == AnnualCardConst::STATUS_REVOKE) {
                $detailArr[$key]['remark'] = '该年卡已被撤销';
                continue;
            }

            $annualPackageBiz = new PackageBiz();
            $packageRes       = $annualPackageBiz->getPeriodPackageByCardAndSid($cardId, $cardSid);
            if ($packageRes['code'] != 200 || empty($packageRes['data'])) {
                $detailArr[$key]['remark'] = '该年卡没有对应套餐';
                continue;
            }
            $packageInfo = $packageRes['data'][0] ?? [];
            //时段套餐id
            $packageId   = $packageInfo['id'];
            //时段套餐时间
            $startTime = $packageInfo['start_time'];
            $endTime   = $packageInfo['end_time'];

            $annualStatus = $value['status'];

            //保存下原始的有效期
            $detailArr[$key]['ext']      = json_encode(['avalid_begin'=> $startTime, 'avalid_end'=> $endTime, 'annual_status' => $annualStatus]);

            //未激活且为激活后x天有效的年卡延期失败
            $validType = $extInfo['annual_valid_type'] ?? 0;
            if ($status == AnnualCardConst::STATUS_NOT_ACTIVE && $validType == AnnualCardConst::ANNUAL_VALID_TYPE_DAY) {
                $detailArr[$key]['remark'] = '该年卡状态不支持延期';
                continue;
            }

            //过滤下 非未激活 已激活的状态
            if (!in_array($status, [AnnualCardConst::STATUS_NOT_ACTIVE, AnnualCardConst::STATUS_ACTIVE])) {
                $detailArr[$key]['remark'] = '该年卡状态不支持延期';
                continue;
            }

            //选择为指定日期延期时，延期日期小于最后时段套餐开始时间的年卡延期失败
            if ($delayType == 1 &&  strtotime($delayData . ' 23:59:59') < $startTime) {
                $detailArr[$key]['remark'] = '延期日期小于最后套餐的开始时间，延期失败';
                continue;
            }

            $avalidEnd = $delayType == 1 ? strtotime($delayData . ' 23:59:59') : $endTime + (86400 * intval($delayData));

            $updataData = [
                'avalid_end' => $avalidEnd,
            ];

            if ($avalidEnd < $currentTime) {
                $detailArr[$key]['remark'] = '年卡有效期不能小于当前时间';
                continue;
            }

            $detailArr[$key]['avalid_begin'] = $startTime;
            $detailArr[$key]['avalid_end']   = $avalidEnd;

            $updataRes = $annualCardModel->updateAnnualById($checkRes['id'], $updataData);
            if (!$updataRes) {
                $logData = [
                    'ac'         => 'postpone',
                    'res'        => '年卡有效期更新失败',
                    'data'       => $value,
                    'updataRes'  => $updataRes,
                    'checkResId' => $checkRes['id'],
                    'updataData' => $updataData,
                ];
                pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));

                $detailArr[$key]['remark'] = '年卡有效期更新失败';
                continue;
            }

            $state = 0;
            //未生效
            $currentTime < $startTime && $currentTime < $avalidEnd && $state = 1;
            //已生效
            $currentTime >= $startTime && $currentTime <= $avalidEnd && $state = 2;
            //已过期
            $currentTime > $startTime && $currentTime > $avalidEnd && $state = 3;

            //更新时段套餐状态及有效期
            $updateRes = $annualPackageBiz->updatePeriodPackageInfoById($packageId, $startTime, $avalidEnd, $state);
            if ($updateRes['code'] != 200) {
                $detailArr[$key]['remark'] = '时段套餐记录更新失败';
                continue;
            }

            //写入年卡记录表
            $recordArr[] = [
                'card_id'     => $cardId,
                'virtual_no'  => $value['virtual_no'] ?? '',
                'sid'         => $cardSid,
                'ordernum'    => '',
                'type'        => 4,
                'state'       => $state,
                'operator_id' => $operatorId,
                'start_time'  => $startTime,
                'end_time'    => $avalidEnd,
                'ext_info'    => [],
            ];
            //阿里云人脸库更新过期时间
            $callIsAliFacePlatform = AnnualAliFaceBiz::getInstance()->callIsAliFacePlatform($sid, $checkRes['pid']);
            if ($callIsAliFacePlatform) {
                //获取人脸id
                $annualAliFaceIdMapModel = new AliFaceMapModel();
                $faceIdList = $annualAliFaceIdMapModel->getValidFaceIdListByVirtualNo($value['virtual_no']);
                if (!empty($faceIdList)) {
                    foreach ($faceIdList as $faceId) {
                        AnnualAliFaceBiz::getInstance()->callRenewalFaceExpiredTime([
                            'faceid' => $faceId ?: '',
                            'order_id' => '',
                            'end_time' => (int)date('Ymd', $avalidEnd),
                            'product_type' => 'I'
                        ]);
                    }
                }
            }
            //更新状态已成功
            $detailArr[$key]['d_status'] = 1;
        }

        //写入年卡记录表
        if ($recordArr) {
            $insertRes = (new PackageBiz())->batchCreatePackageRecord($recordArr);
            //返回有错误数据就记录日志
            if ($insertRes['code'] != 200 || !empty($insertRes['data']['error'])) {
                $logData = [
                    'ac'        => 'postpone',
                    'res'       => '写入年卡记录失败',
                    'sid'       => $sid,
                    'recordArr' => $recordArr,
                    'taskId'    => $taskId,
                    'taskType'  => $taskType,
                    'response'  => $insertRes,
                ];
                pft_log('annual/task', json_encode($logData, JSON_UNESCAPED_UNICODE));
            }
        }
        //任务后置操作
        $this->_taskPostOperation($detailArr, $sid, $taskId, $taskType, $realExcelData, $this->postponeLogPath, 'postpone');
        return true;
    }

    /**
     * 年卡批量增加特权
     * <AUTHOR>
     * @Date 2023/6/27 21:20
     * @return bool
     */
    private function _handleAddPrivilege()
    {
        $sid           = $this->args['saveData']['sid'];
        $tid           = $this->args['saveData']['tid'];
        $pid           = $this->args['saveData']['pid'];
        $lid           = $this->args['saveData']['lid'];
        //虚拟卡号数组
        $realExcelData = $this->args['saveData']['realExcelData'];
        $taskId        = $this->args['saveData']['taskId'];
        $taskType      = $this->args['saveData']['taskType'];
        //$operatorId    = $this->args['saveData']['opid'];
        //必传参数验证 是否需要优化到返回结果上
        if (!$realExcelData || !is_array($realExcelData) || !$sid || !$taskId) {
            @pft_log($this->addPrivilegeLogPath, json_encode(['数据缺失', $this->args]));
            return false;
        }
        //判断操作类型是否是增加特权
        if ($taskType != 6) {
            @pft_log($this->addPrivilegeLogPath, json_encode(['操作类型需要是新增特权', $this->args]));
            return false;
        }
        $annualCardModel = new \Model\Product\AnnualCard();
        //特权数组
        $groupPriv = $realExcelData[0]['group_priv'];
        if (!is_array($groupPriv)) {
            @pft_log($this->addPrivilegeLogPath, "请选择要添加的特权数据" . var_export($groupPriv, true));
            return false;
        }
        $res = $this->_generateAddGroupPrivilegeData($groupPriv, $sid, $tid);
        $privilegeData = $res['data'];
        //pft_log('annual/task/debug', __METHOD__.json_encode($privilegeData, JSON_UNESCAPED_UNICODE));
        $map       = ['sid' => $sid];
        $detailArr = [];
        $packageBiz = new \Business\AnnualCard\PackageManage();
        foreach ($realExcelData as $key => $value) {
            $cardId          = $value['id'] ?? 0;
            $cardSid         = $value['sid'] ?? 0;
            $detailArr[$key] = [
                'track_id'     => $taskId,
                'sid'          => $sid,
                'virtual_no'   => $value['virtual_no'] ?? '',
                'physics_no'   => $value['physics_no'] ?? '',
                'card_no'      => $value['card_no'] ?? '',
                'dname'        => $value['dname'] ?? '',
                'id_card_no'   => $value['id_card_no'] ?? '',
                'mobile'       => $value['mobile'] ?? '',
                'avalid_begin' => 0,
                'avalid_end'   => 0,
                'd_status'     => 2,
                'remark'       => '',
                'ext'          => "[]",
            ];
            if (!empty($res['msg'])) {
                $detailArr[$key]['remark'] = $res['msg'];
                break;
            }
            //上传模板未找到对应虚拟卡号时，延期失败
            if (isset($value['virtual_no']) && (!$cardId || !$cardSid)) {
                $detailArr[$key]['remark'] = '未找到该虚拟卡号';
                continue;
            }
            $checkRes = $annualCardModel->checkCard('virtual_no', $value['virtual_no'], $map, 'id');
            if (!$checkRes) {
                pft_log($this->addPrivilegeLogPath, json_encode([
                    'ac'    => __METHOD__,
                    'res'   => '虚拟卡号不存在',
                    'sid'   => $sid,
                    'value' => $value,
                ], JSON_UNESCAPED_UNICODE));
                $detailArr[$key]['remark'] = '虚拟卡号不存在';
                continue;
            }
            $groupNames = array_column($groupPriv, 'group_name');
            //保存下原始特权操作信息
            $detailArr[$key]['ext']      = json_encode(['group_names' => $groupNames, 'annual_status' => $value['status']]);
            //这里处理pft_annual_card_package_privilege
            //套餐状态应该继承要刷的套餐的状态
            //如果用户年卡有多个记录 比如同一个年卡有已生效快过期的 还有续费未生效的 则这两种情况都要刷特权，套餐和特权状态继承自两种年卡的状态
            $handlerRes = $packageBiz->packagePrivilegeAdd($privilegeData, $cardId, $lid, $pid, $cardSid, $tid, $value['virtual_no']);
            if ($handlerRes['code'] != 200) {
                $logData = [
                    'ac'         => __METHOD__,
                    'res'        => '年卡特权新增失败',
                    'data'       => $value,
                    'msg'  => $handlerRes['msg'],
                    'dat'  => $handlerRes['data'],
                    'checkResId' => $checkRes['id'],
                ];
                pft_log($this->addPrivilegeLogPath, json_encode($logData, JSON_UNESCAPED_UNICODE));
                $detailArr[$key]['remark'] = '年卡特权新增失败：' . $handlerRes['msg'];
                continue;
            }
            //更新状态已成功
            $detailArr[$key]['d_status'] = 1;
        }
        //任务后置操作
        $this->_taskPostOperation($detailArr, $sid, $taskId, $taskType, $realExcelData, $this->addPrivilegeLogPath, __METHOD__);
        return true;
    }

    /**
     * 批量任务进度后续处理
     * <AUTHOR>
     * @Date 2023/7/10 15:48
     * @param $detailArr
     * @param $sid
     * @param $taskId
     * @param $taskType
     * @param $realExcelData
     * @param $logPath
     * @param string $ac
     * @return void
     */
    private function _taskPostOperation($detailArr, $sid, $taskId, $taskType, $realExcelData, $logPath, string $ac = '', $remark = '')
    {
        $annualCardTask  = new \Model\Product\AnnualCardTask();
        if ($detailArr) {
            $res = $annualCardTask->bindOptTaskDetail($detailArr, $sid, $taskId, $taskType);
            if ($res === false) {
                $logData = [
                    'ac'        => $ac ?: __METHOD__,
                    'res'       => '任务写入失败',
                    'sid'       => $sid,
                    'sql'       => $annualCardTask->_sql(),
                    'dbError'   => $annualCardTask->getDbError(),
                    //'detailArr' => $detailArr,
                    'taskId'    => $taskId,
                    'taskType'  => $taskType,
                ];
                pft_log($logPath, json_encode($logData, JSON_UNESCAPED_UNICODE));
            }
        } else {
            $logData = [
                'ac'            => $ac ?: __METHOD__,
                'res'           => '数据为空',
                'sid'           => $sid,
                'realExcelData' => $realExcelData,
                'taskId'        => $taskId,
            ];
            pft_log($logPath, json_encode($logData, JSON_UNESCAPED_UNICODE));
        }
        $time       = time();
        $updateTask = $annualCardTask->updateTask($taskId, 1, $time, $remark);
        if (!$updateTask) {
            $logData = [
                'ac'         => $ac ?: __METHOD__,
                'res'        => '任务状态更新失败',
                'sql'       => $annualCardTask->_sql(),
                'dbError'   => $annualCardTask->getDbError(),
                'updateTask' => $updateTask,
                'taskId'     => $taskId,
            ];
            pft_log($logPath, json_encode($logData, JSON_UNESCAPED_UNICODE));
        }
    }
    private function onlyUpdateTask($taskId, $logPath, $remark)
    {
        $annualCardTask  = new \Model\Product\AnnualCardTask();
        $updateTask = $annualCardTask->updateTask($taskId, 1, time(), $remark);
        if (!$updateTask) {
            $logData = [
                'ac'         => __METHOD__,
                'res'        => '任务状态更新失败',
                'sql'       => $annualCardTask->_sql(),
                'updateTask' => $updateTask,
                'taskId'     => $taskId,
            ];
            pft_log($logPath, json_encode($logData, JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 年卡批量删除特权
     * 前提是票属性里面先不要删除年卡特权 等刷完数据再删除
     * <AUTHOR>
     * @Date 2023/7/10 13:03
     * @return bool
     */
    private function _handleDelPrivilege()
    {
        $sid           = $this->args['saveData']['sid'];
        $tid           = $this->args['saveData']['tid'];
        $pid           = $this->args['saveData']['pid'];
        $lid           = $this->args['saveData']['lid'];
        //虚拟卡号数组
        $realExcelData = $this->args['saveData']['realExcelData'];
        $taskId        = $this->args['saveData']['taskId'];
        $taskType      = $this->args['saveData']['taskType'];
        //$operatorId    = $this->args['saveData']['opid'];
        //必传参数验证 是否需要优化到返回结果上
        if (!$realExcelData || !is_array($realExcelData) || !$sid || !$taskId) {
            @pft_log($this->delPrivilegeLogPath, json_encode(['数据缺失', $this->args]));
            return false;
        }
        //判断操作类型是否是删除特权
        if ($taskType != 7) {
            @pft_log($this->delPrivilegeLogPath, json_encode(['操作类型需要是删除特权', $this->args]));
            return false;
        }
        $annualCardModel = new \Model\Product\AnnualCard();
        //特权数组
        $groupPriv = $realExcelData[0]['group_priv'];
        if (!is_array($groupPriv)) {
            pft_log($this->delPrivilegeLogPath, "请选择要删除的特权数据" . var_export($groupPriv, true));
            return false;
        }
        $res = $this->_generateDeleteGroupPrivilegeData($groupPriv, $sid, $tid);
        $privilegeData = $res['data'];
        //pft_log('annual/task/debug', __METHOD__.json_encode($privilegeData, JSON_UNESCAPED_UNICODE));
        $map       = ['sid' => $sid];
        $detailArr = [];
        $packageBiz = new \Business\AnnualCard\PackageManage();
        foreach ($realExcelData as $key => $value) {
            $cardId          = $value['id'] ?? 0;
            $cardSid         = $value['sid'] ?? 0;
            $detailArr[$key] = [
                'track_id'     => $taskId,
                'sid'          => $sid,
                'virtual_no'   => $value['virtual_no'] ?? '',
                'physics_no'   => $value['physics_no'] ?? '',
                'card_no'      => $value['card_no'] ?? '',
                'dname'        => $value['dname'] ?? '',
                'id_card_no'   => $value['id_card_no'] ?? '',
                'mobile'       => $value['mobile'] ?? '',
                'avalid_begin' => 0,
                'avalid_end'   => 0,
                'd_status'     => 2,
                'remark'       => '',
                'ext'          => "[]",
            ];
            //上传模板未找到对应虚拟卡号时，延期失败
            if (isset($value['virtual_no']) && (!$cardId || !$cardSid)) {
                $detailArr[$key]['remark'] = '未找到该虚拟卡号';
                continue;
            }
            $checkRes = $annualCardModel->checkCard('virtual_no', $value['virtual_no'], $map, 'id');
            if (!$checkRes) {
                @pft_log($this->delPrivilegeLogPath, json_encode([
                    'ac'    => __METHOD__,
                    'res'   => '虚拟卡号不存在',
                    'sid'   => $sid,
                    'value' => $value,
                ], JSON_UNESCAPED_UNICODE));
                $detailArr[$key]['remark'] = '虚拟卡号不存在：'. $value['virtual_no'];
                continue;
            }
            //保存下特权操作信息
            $groupNames = array_column($groupPriv, 'group_name');
            $detailArr[$key]['ext']      = json_encode(['group_names' => $groupNames, 'annual_status' => $value['status']]);
            //票属性删除特权需要处理pft_annual_card_privilege_relation
            //特权快照删除需要处理pft_annual_card_package_privilege
            $handlerRes = $packageBiz->packagePrivilegeDelete($privilegeData, $cardId, $lid, $pid, $cardSid, $tid, $value['virtual_no']);
            if ($handlerRes['code'] != 200) {
                $logData = [
                    'ac'         => __METHOD__,
                    'res'        => '年卡特权删除失败',
                    'data'       => $value,
                    'msg'  => $handlerRes['msg'],
                    'dat'  => $handlerRes['data'],
                    'checkResId' => $checkRes['id'],
                    'privilegeData' => json_encode($privilegeData),
                ];
                @pft_log($this->delPrivilegeLogPath, json_encode($logData, JSON_UNESCAPED_UNICODE));
                $detailArr[$key]['remark'] = '年卡特权删除失败：' . $handlerRes['msg'];
                continue;
            }
            //更新状态已成功
            $detailArr[$key]['d_status'] = 1;
        }
        //任务后置操作
        $this->_taskPostOperation($detailArr, $sid, $taskId, $taskType, $realExcelData, $this->delPrivilegeLogPath, __METHOD__);
        return true;
    }

    /**
     * 处理提交过来的新增特权数据
     * 新增的特权和票属性里的特权对比
     * 如果有就直接使用group_id 没有则新建特权group_id
     * 新建的同时体现在票属性上
     * <AUTHOR>
     * @Date 2023/7/7 17:06
     * @param array $groupPriv 特权数组 形如 [{
     * "superior_id":3385,
     * "use_limit":"-1",
     * "group_name":"ypc-tq001",
     * "sort":2,
     * "tid_arr":[1019394,1019526,110034171],
     * "tid_aid_map":{"1019394":3385,"1019526":3385,"110034171":3385}
     * },...]
     * @param $sid
     * @param $tid
     * @return array
     */
    private function _generateAddGroupPrivilegeData(array $groupPriv, $sid, $tid)
    {
        $msg = '';
        $annualPrivBiz = new \Business\AnnualCard\Privilege();
        $privilegeData = [];
        //1、如果这个年卡的票属性里边有这条特权，那么批量给年卡新增特权的时候直接获取特权组id（用户已经在票属性里边添加了这个特权）
        //2、如果这个年卡的票属性里边没有这条特权，那么批量给年卡新增特权的时候，同步给票属性添加这条特权
        foreach ($groupPriv as $item) {
            //判断下重复的tid
            $count    = count($item['tid_arr']);
            $tmpCount = count(array_unique($item['tid_arr']));
            if ($count != $tmpCount) {
                $msg = "'{$item['group_name']}' 特权分组添加失败:当前选项中存在重复的特权门票；";
                pft_log($this->addPrivilegeLogPath, $msg);
                continue;
            }
            //r.tid, r.aid, g.use_limit, g.group_name, r.group_id, r.parent_tid, r.supplier_aid,g.sort
            $result = $annualPrivBiz->addCardPrivilege($sid, $tid, $item['group_name'],
                $item['use_limit'], $item['tid_arr'], $item['superior_id'], $item['sort'], $item['tid_aid_map'] ?? [],
                $item['use_time_limit'] ?? '',2, true, $item['holiday_limit'] ?? '');
            if ($result['code'] != 200) {
                $msg = "'{$item['group_name']}' 特权分组添加失败:" . $result['msg'];
                pft_log($this->addPrivilegeLogPath, $msg);
                continue;
            }
            foreach ($item['tid_arr'] as $ticket_id) {
                $privilegeData[] = [
                    'tid'            => $ticket_id,
                    'aid'            => $sid,
                    'use_limit'      => $item['use_limit'],
                    'group_name'     => $item['group_name'],
                    'group_id'       => $result['data']['relation'][0]['group_id'] ?? 0,
                    'parent_tid'     => $tid,
                    'supplier_aid'   => isset($item['tid_aid_map'][$ticket_id]) && $count > 1 ? $item['tid_aid_map'][$ticket_id] : $sid,
                    'sort'           => $item['sort'],
                    'use_time_limit' => $item['use_time_limit'] ?? '',
                    'holiday_limit'  => $item['holiday_limit'] ?? '',
                    //标识是否是组合特权
                    'combine_flag'   => count($item['tid_arr']) > 1,
                ];
            }
        }
        return ['msg' => $msg, 'data' => $privilegeData];
    }
    private function _generateDeleteGroupPrivilegeData(array $groupPriv, $sid, $tid)
    {
        $msg = '';
        $privilegeData = [];
        foreach ($groupPriv as $item) {
            $privilegeData[] = [
                'tid' => $item['tid'],
                'aid' => $item['aid'],
                'group_id' => $item['group_id'],
                'parent_tid' => $item['parent_tid'],
            ];
        }
        return ['msg' => $msg, 'data' => $privilegeData];
    }
    private function importGiftCard()
    {
        $sid = $this->args['saveData']['sid'];
        $tid = $this->args['saveData']['tid'];
        $pid = $this->args['saveData']['pid'];
        //$lid = $this->args['saveData']['lid'];
        //虚拟卡号数组
        $realExcelData = $this->args['saveData']['realExcelData'];
        $taskId = $this->args['saveData']['taskId'];
        $taskType = $this->args['saveData']['taskType'];
        //$operatorId = $this->args['saveData']['opid'];
        $orderName = $this->args['saveData']['extraParams']['order_name'];
        $orderMobile = $this->args['saveData']['extraParams']['order_mobile'];
        //必传参数验证 是否需要优化到返回结果上
        if (!$realExcelData || !is_array($realExcelData) || !$sid || !$taskId || empty($orderMobile) || empty($orderName)) {
            pft_log($this->importGiftCard, json_encode(['Fatal Error数据缺失', $this->args]));
            $this->onlyUpdateTask($taskId, $this->importGiftCard, '数据缺失,请检查必传参数');
            return false;
        }
        //判断操作类型是否是增加特权
        if ($taskType != 8) {
            pft_log($this->importGiftCard, json_encode(['Fatal Error操作类型需要是导入礼品卡', $this->args]));
            $this->onlyUpdateTask($taskId, $this->importGiftCard, '操作类型需要是导入礼品卡');
            return false;
        }
        $javaApi = new \Business\CommodityCenter\Ticket();
        $tidAttr = $javaApi->queryTicketAttrsById($tid);
        $ticketAttr = [];
        if (!empty($tidAttr)) {
            foreach ($tidAttr as $item) {
                $ticketAttr[$item['ticket_id']][$item['key']] = $item['val'];
            }
        }
        //valid_period_days要单独获取
        $ticketInfo = $javaApi->queryTicketById($tid);
        $validPeriodDays = isset($ticketInfo['delaydays']) ? (int)$ticketInfo['delaydays'] : 0;
        $annualValidType = isset($ticketAttr[$tid]['annual_valid_type']) ? (int)$ticketAttr[$tid]['annual_valid_type'] : 0;
        $annualValidDay = isset($ticketAttr[$tid]['annual_valid_day']) ? (int)$ticketAttr[$tid]['annual_valid_day'] : '';
        $annualValidStart = isset($ticketAttr[$tid]['annual_valid_start']) ? (string)$ticketAttr[$tid]['annual_valid_start'] : '';
        $annualValidEnd = isset($ticketAttr[$tid]['annual_valid_end']) ? (string)$ticketAttr[$tid]['annual_valid_end'] : '';
        $annualDelayEnabledTime = isset($ticketAttr[$tid]['annual_delay_enabled_time']) ? (int)$ticketAttr[$tid]['annual_delay_enabled_time'] : 0;
        $annualTicketAttr = [
            'valid_period_days' => $validPeriodDays,
            'annual_valid_type' => $annualValidType,
            'annual_valid_day' => $annualValidDay,
            'annual_valid_start' => $annualValidStart,
            'annual_valid_end' => $annualValidEnd,
            'annual_delay_enabled_time' => $annualDelayEnabledTime,
        ];
        $jsonAttr = json_encode($annualTicketAttr, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        //判断导入的物理id和实体卡号是否重复
        //物理id是全局唯一 实体卡号是商户内唯一
        $map = [];
        $detailArr = [];
        $allCount = count($realExcelData);
        $errorCount = 0;
        foreach ($realExcelData as $item) {
            if (empty($item[1])) {
                $errorCount++;
                $detailArr[] = [
                    'track_id' => $taskId,
                    'sid' => $sid,
                    'virtual_no' => '',
                    'physics_no' => $item[0],
                    'card_no' => $item[1] ?? '',
                    'dname' => '',
                    'id_card_no' => '',
                    'mobile' => '',
                    'avalid_begin' => 0,
                    'avalid_end' => 0,
                    'd_status' => 2,//0处理中 1下单成功 2下单失败
                    'remark' => '实体卡卡号为空',
                    'ext' => $jsonAttr,
                ];
                continue;
            }
            $physicsHex = dechex(intval($item[0]));
            if ($physicsHex == '0') {
                $errorCount++;
                $detailArr[] = [
                    'track_id' => $taskId,
                    'sid' => $sid,
                    'virtual_no' => '',
                    'physics_no' => $item[0],
                    'card_no' => $item[1],
                    'dname' => '',
                    'id_card_no' => '',
                    'mobile' => '',
                    'avalid_begin' => 0,
                    'avalid_end' => 0,
                    'd_status' => 2,//0处理中 1下单成功 2下单失败
                    'remark' => '物理卡号不正确',
                    'ext' => $jsonAttr,
                ];
                continue;
            }
            $map[$item[0]] = $item[1];
        }
        $allPhysicsIds = array_keys($map);
        //检验物理卡号是否重复
        $chunks1 = array_chunk($allPhysicsIds, 50);
        $baseCardModel = $this->getBaseCardModel();
        foreach ($chunks1 as $chunk) {
            $batchCheckPhysics = $baseCardModel->batchGetInfoByPhysicsNo($chunk);
            if (!empty($batchCheckPhysics)) {
                $errorCount += count($batchCheckPhysics);
                foreach ($batchCheckPhysics as $item) {
                    $detailArr[] = [
                        'track_id' => $taskId,
                        'sid' => $sid,
                        'virtual_no' => '',
                        'physics_no' => $item['physics_no'],
                        'card_no' => $map[$item['physics_no']] ?? '',
                        'dname' => '',
                        'id_card_no' => '',
                        'mobile' => '',
                        'avalid_begin' => 0,
                        'avalid_end' => 0,
                        'd_status' => 2,//0处理中 1下单成功 2下单失败
                        'remark' => '物理卡号已经被占用',
                        'ext' => $jsonAttr,
                    ];
                    //删除被占用的物理卡号 剩下的都可以导入
                    unset($map[$item['physics_no']]);
                }
            }
        }
        //检验实体卡号是否重复
        $allCardNo = array_values($map);
        $chunks2 = array_chunk($allCardNo, 50);
        $annualCardModel = $this->getAnnualCardModel();
        $flip = array_flip($map);
        foreach ($chunks2 as $chunk) {
            $batchCheckCardNo = $annualCardModel->batchGetInfoByCardNo($sid, $chunk);
            if (!empty($batchCheckCardNo)) {
                $errorCount += count($batchCheckCardNo);
                foreach ($batchCheckCardNo as $item) {
                    $detailArr[] = [
                        'track_id' => $taskId,
                        'sid' => $sid,
                        'virtual_no' => '',
                        'physics_no' => $flip[$item['card_no']] ?? '',
                        'card_no' => $item['card_no'],
                        'dname' => '',
                        'id_card_no' => '',
                        'mobile' => '',
                        'avalid_begin' => 0,
                        'avalid_end' => 0,
                        'd_status' => 2,//0处理中 1下单成功 2下单失败
                        'remark' => '实体卡号已经被占用',
                        'ext' => $jsonAttr,
                    ];
                    unset($map[$flip[$item['card_no']]]);
                }
            }
        }
        unset($flip, $chunks1, $chunks2, $allCardNo, $allPhysicsIds);
        //创建虚拟卡号 每次生成$total 如果数据库中有重复的则进行去重后补充够3000个
        $annualCardBis = new \Business\Product\AnnualCard();
        //如果上面过滤完后没有数据 则直接更新
        if (empty($map) && !empty($detailArr)) {
            $remark = '成功' . ($allCount - $errorCount) . '单，失败' . $errorCount . '单';
            $this->_taskPostOperation($detailArr, $sid, $taskId, $taskType, $realExcelData, $this->importGiftCard, __METHOD__, $remark);
            return true;
        }
        $total = count($map);
        $virtualNoRes = $annualCardBis->createVirtualNo($total);
        if ($virtualNoRes['code'] != 200 || empty($virtualNoRes['data']['success'])) {
            pft_log($this->importGiftCard, '虚拟卡号生成失败total=' . $total.' msg=' . $virtualNoRes['msg']);
            $this->onlyUpdateTask($taskId, $this->importGiftCard, '虚拟卡号生成失败total=' . $total.' msg=' . $virtualNoRes['msg']);
            return false;
        }
        $virtualNoArr = $virtualNoRes['data']['success'];
        foreach ($map as $k => $v) {
            $time = time();
            $physics_no = dechex(intval($k));
            //==========================创建基础卡================================//
            $ret = $this->getBaseCardModel()->createBaseCard($k, 3, $time, $sid);
            if ($ret === false) {
                $detailArr[] = [
                    'track_id' => $taskId,
                    'sid' => $sid,
                    'virtual_no' => '',
                    'physics_no' => $k,
                    'card_no' => $v,
                    'dname' => '',
                    'id_card_no' => '',
                    'mobile' => '',
                    'avalid_begin' => 0,
                    'avalid_end' => 0,
                    'd_status' => 2,//0处理中 1下单成功 2下单失败
                    'remark' => '物理卡号已经被占用',
                    'ext' => $jsonAttr,
                ];
                pft_log($this->importGiftCard, json_encode([
                    'ac' => __METHOD__,
                    'res' => '年卡BaseCard创建失败 可能原因：物理ID唯一索引拦截,跳过',
                    'sid' => $sid,
                    'physics_no' => $k
                ], JSON_UNESCAPED_UNICODE));
                continue;
            }
            //如果生成的虚拟卡号分配完了 需要重新生成下
            if (empty($virtualNoArr)) {
                $virtualNoRes = $annualCardBis->createVirtualNo($total);
                if ($virtualNoRes['code'] != 200) {
                    pft_log($this->importGiftCard, '虚拟卡号生成失败total=' . $total.' msg=' . $virtualNoRes['msg']);
                    $this->onlyUpdateTask($taskId, $this->importGiftCard, '虚拟卡号生成失败total=' . $total.' msg=' . $virtualNoRes['msg']);
                    return false;
                }
                $virtualNoArr = $virtualNoRes['data']['success'];
            }
            $virtualNo = array_shift($virtualNoArr);
            $tmpAnnualCardInfo = [
                'sid' => $sid,
                'pid' => $pid,
                'memberid' => 0,
                'virtual_no' => $virtualNo,
                'physics_no' => $physics_no,
                'card_no' => $v,
                'sale_time' => 0,
                'active_time' => 0,
                'update_time' => 0,
                'avalid_begin' => 0,
                'avalid_end' => 0,
                'create_time' => time(),
                'status' => \Business\Product\AnnualCardConst::STATUS_IN_STOCK,
            ];
            //========================创建年卡和套餐特权数据========================//
            $cardId = $annualCardModel->createAnnualCardSingle($tmpAnnualCardInfo);
            if ($cardId === false) {
                $errorCount++;
                $detailArr[] = [
                    'track_id' => $taskId,
                    'sid' => $sid,
                    'virtual_no' => $virtualNo,
                    'physics_no' => $k,
                    'card_no' => $v,
                    'dname' => '',
                    'id_card_no' => '',
                    'mobile' => '',
                    'avalid_begin' => 0,
                    'avalid_end' => 0,
                    'd_status' => 2,//0处理中 1下单成功 2下单失败
                    'remark' => '年卡创建失败',
                    'ext' => $jsonAttr,
                ];
                pft_log($this->importGiftCard . '/for_annual', json_encode([
                    'ac' => __METHOD__,
                    'res' => '年卡创建失败',
                    'data' => $tmpAnnualCardInfo,
                ], JSON_UNESCAPED_UNICODE));
                continue;
            }
            //==========================创建年卡订单==============================//
            //如果只导入物理卡的库存则不用下单  导入库存的年卡都是status=3库存中 下单后会变为：不激活status=0待激活 激活status=1
            $handleData = [
                'aid' => $sid,//上级供应商id
                'order_type' => 1, //下单类型，0：虚拟卡购买，1物理卡购买，2年卡续费
                'pid' => $pid,//产品id
                'paymode' => 3,//自供自销
                'mid' => $sid,//下单人
                'memo' => '',
                'ordertel' => $orderMobile,//联系人手机
                'ordername' => $orderName,//联系人姓名
                'ordermode' => 0,//默认平台
                'id_card' => '', //身份证号码
                'avatar' => [],
                'virtual_no' => $virtualNo,
                'physics_no' => $physics_no,//物理卡号
                'card_no' => $v,//实体卡号
                'inactive' => 1,  // 年卡下单是否激活  0激活 1不激活
                'rand_math' => mt_rand(1000, 9999) . $virtualNo, // 添加一个随机数，避免批量年卡下单枷锁
                'card_type' => 'physics',
            ];
            //这里会处理后续套餐
            $result = $annualCardBis->orderForCard($sid, (object)$handleData);
            //pft_log($this->importGiftCard . '/result', json_encode($result));
            if ($result['code'] != 200) {
                $errorCount++;
                $detailArr[] = [
                    'track_id' => $taskId,
                    'sid' => $sid,
                    'virtual_no' => $virtualNo,
                    'physics_no' => $k,
                    'card_no' => $v,
                    'dname' => '',
                    'id_card_no' => '',
                    'mobile' => '',
                    'avalid_begin' => 0,
                    'avalid_end' => 0,
                    'd_status' => 2,//0处理中 1下单成功 2下单失败
                    'remark' => '年卡下单失败：' . $result['msg'],
                    'ext' => $jsonAttr,
                ];
                pft_log($this->importGiftCard . '/for_order', json_encode($handleData, JSON_UNESCAPED_UNICODE) . '--' . __LINE__);
            } else {
                $detailArr[] = [
                    'track_id' => $taskId,
                    'sid' => $sid,
                    'virtual_no' => $virtualNo,
                    'physics_no' => $k,
                    'card_no' => $v,
                    'dname' => '',
                    'id_card_no' => '',
                    'mobile' => '',
                    'avalid_begin' => 0,
                    'avalid_end' => 0,
                    'd_status' => 1,//0处理中 1下单成功 2下单失败
                    'remark' => '',
                    'ext' => $jsonAttr,
                ];
            }
        }
        $remark = '成功' . ($allCount - $errorCount) . '单，失败' . $errorCount . '单';
        $this->_taskPostOperation($detailArr, $sid, $taskId, $taskType, $realExcelData, $this->importGiftCard, __METHOD__, $remark);
        return true;
    }

    private function getBaseCardModel(): BaseCard
    {
        if (empty($this->baseCardModel)) {
            $this->baseCardModel = new BaseCard();
        }
        return $this->baseCardModel;
    }

    private function getAnnualCardModel(): AnnualCard
    {
        if (empty($this->annualCardModel)) {
            $this->annualCardModel = new AnnualCard();
        }
        return $this->annualCardModel;
    }
}
