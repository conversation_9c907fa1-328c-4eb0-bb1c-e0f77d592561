<?php

/**
 * 年卡异步下单
 *
 * @date 2018-12-10
 * <AUTHOR>
 */

use Library\ApplicationContext;
use Library\Constants\FastCheckConst;
use Library\Tools\Helpers;
use Library\Constants\DingTalkRobots;

class OrderAnnual_Job
{

    /**
     * 执行入口
     *
     * <AUTHOR>
     * @date   2018-06-12
     */
    public function perform()
    {
        pft_log('order/annual', json_encode($this->args));
        $consumeMap   = $this->args['consumeMap'];
        $cardMid      = $this->args['cardMid'];
        $sid          = $this->args['sid'];
        $terminal     = $this->args['terminal'];
        $oper         = $this->args['oper'];
        $cardOrderid  = $this->args['cardOrderid'];
        $optionParams = $this->args['optionParams'];
        $optionParams['terminal'] = $terminal;

        $annualBiz    = new \Business\Product\AnnualCard();
        $annualModel  = new \Model\Product\AnnualCard();

        $result       = $annualBiz->submitOrder($consumeMap, $cardMid, $sid, $oper, $optionParams);
        if($result[0] !=200){
            $data = [
                'create_time'   => time(),
                'consume_map'   => json_encode($consumeMap),
                'card_mid'      =>  $cardMid,
                'sid'           =>  $sid,
                'terminal'      =>  $terminal,
                'operId'        =>  $oper,
                'err_msg'       =>  $result[1]?:'',
                'number'        =>  1,
                'card_order_id' => $cardOrderid,
                'status'        =>  0,
                'options'       =>  json_encode($optionParams),
            ];
            $annualModel->addAsyncOrderErr($data);
            //下单失败后 发起钉钉告警
            $message = "年卡特权下单失败\n 供应商id： {$sid};\n pft_annual_card_order表id：{$cardOrderid};\n 错误信息：{$data['err_msg']};";
            pft_log('debug/leeRobot', json_encode([
                'action'        => 'retryOrder',
                'sid'           => $sid,
                'card_order_id' => $cardOrderid,
                'todo'          => "请到队列执行脚本  php /var/www/html/Service/Crontab/run.php cli_AsyncOrderAnnual reOrder",
                'msg'           => $data['err_msg'],
            ], JSON_UNESCAPED_UNICODE));
            //Helpers::sendDingTalkGroupRobotMessageRaw($message, DingTalkRobots::PAYMENT_EXCEPTION_ROBOT);

        }else{
            ApplicationContext::set("device_key", $optionParams['deviceNo'] ?? "");
            //成功 删掉之前的坑并验证
            $annualModel->updateAnnualOrderRecord($cardOrderid, $result[1]);
            $orderHandle = new \Model\Order\OrderHandler();
            $orderHandle->CheckOrderSimply($result[1], $oper, null, '年卡过闸', 5, $dtime = false, 0,
                1, false, '', true, false, FastCheckConst::CHECK_TYPE_DELAY_ASYNC);
            //记录年卡验证记录

        }


    }

}
